{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, EventEmitter, inject, Injector, input, Output, signal } from '@angular/core';\nimport { toObservable } from \"@angular/core/rxjs-interop\";\nimport { PackagesModel, PackageType } from \"src/app/core/models/package.model\";\nlet BuyPackageSuggestionBoxComponent = class BuyPackageSuggestionBoxComponent {\n  constructor() {\n    this.show = signal(true);\n    this.defaultPackages = PackagesModel.getDefaultPackages();\n    this.injector = inject(Injector);\n    this.preselectedPackage = input(this.defaultPackages[1]);\n    this.switchBackToPackageTypeSelected = new EventEmitter();\n    this.selectedPackageType = this.defaultPackages[0].type;\n    this.packageTypeEnum = PackageType;\n    this.filterOutPackageTypes = this.getFilterOutPackageTypes(this.preselectedPackage().type);\n  }\n  ngOnInit() {\n    this.observePreselectedPackageChanges();\n  }\n  selectPackageType(type) {\n    this.selectedPackageType = type;\n  }\n  getPackages(types) {\n    const defaultPackages = this.defaultPackages;\n    return defaultPackages.filter(p => this.filterOutPackageTypes.includes(p.type));\n  }\n  getSelectedPackage() {\n    return this.defaultPackages.find(pkg => pkg.type === this.selectedPackageType);\n  }\n  switchBackToPackageType() {\n    this.switchBackToPackageTypeSelected.emit(this.getSelectedPackage());\n  }\n  observePreselectedPackageChanges() {\n    toObservable(this.preselectedPackage, {\n      injector: this.injector\n    }).subscribe({\n      next: preselectedPackage => {\n        this.filterOutPackageTypes = this.getFilterOutPackageTypes(preselectedPackage.type);\n        this.selectedPackageType = this.getNextPackageType(preselectedPackage.type);\n      }\n    });\n  }\n  getFilterOutPackageTypes(type) {\n    switch (type) {\n      case this.packageTypeEnum.REGULAR:\n        return [this.packageTypeEnum.FLEX, this.packageTypeEnum.PREMIUM];\n      case this.packageTypeEnum.FLEX:\n        return [this.packageTypeEnum.PREMIUM];\n      case this.packageTypeEnum.PREMIUM:\n        return [];\n      default:\n        return [];\n    }\n  }\n  getNextPackageType(type) {\n    switch (type) {\n      case this.packageTypeEnum.REGULAR:\n        return this.packageTypeEnum.FLEX;\n      case this.packageTypeEnum.FLEX:\n        return this.packageTypeEnum.PREMIUM;\n      default:\n        return this.packageTypeEnum.REGULAR;\n    }\n  }\n};\n__decorate([Output()], BuyPackageSuggestionBoxComponent.prototype, \"switchBackToPackageTypeSelected\", void 0);\nBuyPackageSuggestionBoxComponent = __decorate([Component({\n  selector: 'app-buy-package-suggestion-box',\n  templateUrl: './buy-package-suggestion-box.component.html',\n  styleUrl: './buy-package-suggestion-box.component.css'\n})], BuyPackageSuggestionBoxComponent);\nexport { BuyPackageSuggestionBoxComponent };", "map": {"version": 3, "names": ["Component", "EventEmitter", "inject", "Injector", "input", "Output", "signal", "toObservable", "PackagesModel", "PackageType", "BuyPackageSuggestionBoxComponent", "constructor", "show", "defaultPackages", "getDefaultPackages", "injector", "preselectedPackage", "switchBackToPackageTypeSelected", "selectedPackageType", "type", "packageTypeEnum", "filterOutPackageTypes", "getFilterOutPackageTypes", "ngOnInit", "observePreselectedPackageChanges", "selectPackageType", "getPackages", "types", "filter", "p", "includes", "getSelectedPackage", "find", "pkg", "switchBackToPackageType", "emit", "subscribe", "next", "getNextPackageType", "REGULAR", "FLEX", "PREMIUM", "__decorate", "selector", "templateUrl", "styleUrl"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\buy-package\\buy-package-suggestion-box\\buy-package-suggestion-box.component.ts"], "sourcesContent": ["import { CommonModule } from \"@angular/common\";\r\nimport { ChangeDetectionStrategy, Component, EventEmitter, inject, Injector, input, Input, Output, signal } from '@angular/core';\r\nimport { toObservable } from \"@angular/core/rxjs-interop\";\r\nimport { PackagesModel, PackageType } from \"src/app/core/models/package.model\";\r\n\r\n@Component({\r\n    selector: 'app-buy-package-suggestion-box',\r\n    templateUrl: './buy-package-suggestion-box.component.html',\r\n    styleUrl: './buy-package-suggestion-box.component.css',\r\n})\r\nexport class BuyPackageSuggestionBoxComponent {\r\n\r\n    show = signal(true);\r\n    public defaultPackages = PackagesModel.getDefaultPackages();\r\n    private injector = inject(Injector);\r\n\r\n    preselectedPackage = input(this.defaultPackages[1]);\r\n    @Output() switchBackToPackageTypeSelected = new EventEmitter<any>();\r\n\r\n    selectedPackageType = this.defaultPackages[0].type;\r\n    packageTypeEnum = PackageType;\r\n\r\n    filterOutPackageTypes = this.getFilterOutPackageTypes(this.preselectedPackage().type);\r\n\r\n    ngOnInit() {\r\n        this.observePreselectedPackageChanges();\r\n    }\r\n\r\n    public selectPackageType(type: any) {\r\n        this.selectedPackageType = type;\r\n    }\r\n\r\n    getPackages(types: string[]) {\r\n        const defaultPackages = this.defaultPackages;\r\n        return defaultPackages.filter(p => this.filterOutPackageTypes.includes(p.type));\r\n    }\r\n\r\n    public getSelectedPackage() {\r\n        return this.defaultPackages.find(pkg => pkg.type === this.selectedPackageType);\r\n    }\r\n\r\n    switchBackToPackageType() {\r\n        this.switchBackToPackageTypeSelected.emit(this.getSelectedPackage());\r\n    }\r\n\r\n    private observePreselectedPackageChanges() {\r\n        toObservable(this.preselectedPackage, {\r\n            injector: this.injector\r\n        }).subscribe({\r\n            next: (preselectedPackage) => {\r\n                this.filterOutPackageTypes = this.getFilterOutPackageTypes(preselectedPackage.type);\r\n                this.selectedPackageType = this.getNextPackageType(preselectedPackage.type);\r\n            }\r\n        });\r\n    }\r\n\r\n    private getFilterOutPackageTypes(type: string): string[] {\r\n        switch (type) {\r\n            case this.packageTypeEnum.REGULAR:\r\n                return [this.packageTypeEnum.FLEX, this.packageTypeEnum.PREMIUM];\r\n            case this.packageTypeEnum.FLEX:\r\n                return [this.packageTypeEnum.PREMIUM];\r\n            case this.packageTypeEnum.PREMIUM:\r\n                return [];\r\n            default:\r\n                return [];\r\n        }\r\n    }\r\n\r\n    private getNextPackageType(type: string): PackageType {\r\n        switch (type) {\r\n            case this.packageTypeEnum.REGULAR:\r\n                return this.packageTypeEnum.FLEX;\r\n            case this.packageTypeEnum.FLEX:\r\n                return this.packageTypeEnum.PREMIUM;\r\n            default:\r\n                return this.packageTypeEnum.REGULAR;\r\n        }\r\n    }\r\n}\r\n\r\n"], "mappings": ";AACA,SAAkCA,SAAS,EAAEC,YAAY,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAASC,MAAM,EAAEC,MAAM,QAAQ,eAAe;AAChI,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,aAAa,EAAEC,WAAW,QAAQ,mCAAmC;AAOvE,IAAMC,gCAAgC,GAAtC,MAAMA,gCAAgC;EAAtCC,YAAA;IAEH,KAAAC,IAAI,GAAGN,MAAM,CAAC,IAAI,CAAC;IACZ,KAAAO,eAAe,GAAGL,aAAa,CAACM,kBAAkB,EAAE;IACnD,KAAAC,QAAQ,GAAGb,MAAM,CAACC,QAAQ,CAAC;IAEnC,KAAAa,kBAAkB,GAAGZ,KAAK,CAAC,IAAI,CAACS,eAAe,CAAC,CAAC,CAAC,CAAC;IACzC,KAAAI,+BAA+B,GAAG,IAAIhB,YAAY,EAAO;IAEnE,KAAAiB,mBAAmB,GAAG,IAAI,CAACL,eAAe,CAAC,CAAC,CAAC,CAACM,IAAI;IAClD,KAAAC,eAAe,GAAGX,WAAW;IAE7B,KAAAY,qBAAqB,GAAG,IAAI,CAACC,wBAAwB,CAAC,IAAI,CAACN,kBAAkB,EAAE,CAACG,IAAI,CAAC;EAyDzF;EAvDII,QAAQA,CAAA;IACJ,IAAI,CAACC,gCAAgC,EAAE;EAC3C;EAEOC,iBAAiBA,CAACN,IAAS;IAC9B,IAAI,CAACD,mBAAmB,GAAGC,IAAI;EACnC;EAEAO,WAAWA,CAACC,KAAe;IACvB,MAAMd,eAAe,GAAG,IAAI,CAACA,eAAe;IAC5C,OAAOA,eAAe,CAACe,MAAM,CAACC,CAAC,IAAI,IAAI,CAACR,qBAAqB,CAACS,QAAQ,CAACD,CAAC,CAACV,IAAI,CAAC,CAAC;EACnF;EAEOY,kBAAkBA,CAAA;IACrB,OAAO,IAAI,CAAClB,eAAe,CAACmB,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACd,IAAI,KAAK,IAAI,CAACD,mBAAmB,CAAC;EAClF;EAEAgB,uBAAuBA,CAAA;IACnB,IAAI,CAACjB,+BAA+B,CAACkB,IAAI,CAAC,IAAI,CAACJ,kBAAkB,EAAE,CAAC;EACxE;EAEQP,gCAAgCA,CAAA;IACpCjB,YAAY,CAAC,IAAI,CAACS,kBAAkB,EAAE;MAClCD,QAAQ,EAAE,IAAI,CAACA;KAClB,CAAC,CAACqB,SAAS,CAAC;MACTC,IAAI,EAAGrB,kBAAkB,IAAI;QACzB,IAAI,CAACK,qBAAqB,GAAG,IAAI,CAACC,wBAAwB,CAACN,kBAAkB,CAACG,IAAI,CAAC;QACnF,IAAI,CAACD,mBAAmB,GAAG,IAAI,CAACoB,kBAAkB,CAACtB,kBAAkB,CAACG,IAAI,CAAC;MAC/E;KACH,CAAC;EACN;EAEQG,wBAAwBA,CAACH,IAAY;IACzC,QAAQA,IAAI;MACR,KAAK,IAAI,CAACC,eAAe,CAACmB,OAAO;QAC7B,OAAO,CAAC,IAAI,CAACnB,eAAe,CAACoB,IAAI,EAAE,IAAI,CAACpB,eAAe,CAACqB,OAAO,CAAC;MACpE,KAAK,IAAI,CAACrB,eAAe,CAACoB,IAAI;QAC1B,OAAO,CAAC,IAAI,CAACpB,eAAe,CAACqB,OAAO,CAAC;MACzC,KAAK,IAAI,CAACrB,eAAe,CAACqB,OAAO;QAC7B,OAAO,EAAE;MACb;QACI,OAAO,EAAE;IACjB;EACJ;EAEQH,kBAAkBA,CAACnB,IAAY;IACnC,QAAQA,IAAI;MACR,KAAK,IAAI,CAACC,eAAe,CAACmB,OAAO;QAC7B,OAAO,IAAI,CAACnB,eAAe,CAACoB,IAAI;MACpC,KAAK,IAAI,CAACpB,eAAe,CAACoB,IAAI;QAC1B,OAAO,IAAI,CAACpB,eAAe,CAACqB,OAAO;MACvC;QACI,OAAO,IAAI,CAACrB,eAAe,CAACmB,OAAO;IAC3C;EACJ;CACH;AA9DaG,UAAA,EAATrC,MAAM,EAAE,C,wFAA2D;AAP3DK,gCAAgC,GAAAgC,UAAA,EAL5C1C,SAAS,CAAC;EACP2C,QAAQ,EAAE,gCAAgC;EAC1CC,WAAW,EAAE,6CAA6C;EAC1DC,QAAQ,EAAE;CACb,CAAC,C,EACWnC,gCAAgC,CAqE5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}