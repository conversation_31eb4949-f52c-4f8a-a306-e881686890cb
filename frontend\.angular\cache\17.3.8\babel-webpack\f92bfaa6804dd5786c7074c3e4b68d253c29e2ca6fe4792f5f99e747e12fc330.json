{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { UserProfileRoutingModule } from './user-profile-routing.module';\nimport { UserProfileComponent } from './user-profile.component';\nimport { ProfileMenuComponent } from './components/profile-menu/profile-menu.component';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { ButtonModule } from 'primeng/button';\nimport { CalendarModule } from 'primeng/calendar';\nimport { RippleModule } from 'primeng/ripple';\nimport { NotificationsProfileComponent } from './components/notifications-profile/notifications-profile.component';\nimport { NotificationComponent } from './components/notifications-profile/notification/notification.component';\nimport { PackageHistoryComponent } from './components/package-history/package-history.component';\nimport { RateExperienceComponent } from './components/rate-experience/rate-experience.component';\nimport { ClassroomModule } from '../classroom/classroom.module';\nimport { SettingsComponent } from './components/settings/settings.component';\nimport { InfoComponent } from './components/info/info.component';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport { EditBioDialogComponent } from './components/edit-bio-dialog/edit-bio-dialog.component';\nimport { NotificationAlertsSettingsComponent } from './components/notification-alerts-settings/notification-alerts-settings.component';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nlet UserProfileModule = class UserProfileModule {};\nUserProfileModule = __decorate([NgModule({\n  declarations: [UserProfileComponent, ProfileMenuComponent, NotificationsProfileComponent, NotificationComponent, PackageHistoryComponent, RateExperienceComponent, SettingsComponent, InfoComponent, EditBioDialogComponent, NotificationAlertsSettingsComponent],\n  imports: [CommonModule, FormsModule, ReactiveFormsModule, ClassroomModule, UserProfileRoutingModule, DropdownModule, SharedModule, ButtonModule, InputTextModule, CalendarModule, RippleModule, InputSwitchModule, ConfirmDialogModule]\n})], UserProfileModule);\nexport { UserProfileModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "FormsModule", "ReactiveFormsModule", "UserProfileRoutingModule", "UserProfileComponent", "ProfileMenuComponent", "DropdownModule", "InputTextModule", "ButtonModule", "CalendarModule", "RippleModule", "NotificationsProfileComponent", "NotificationComponent", "PackageHistoryComponent", "RateExperienceComponent", "ClassroomModule", "SettingsComponent", "InfoComponent", "SharedModule", "EditBioDialogComponent", "NotificationAlertsSettingsComponent", "InputSwitchModule", "ConfirmDialogModule", "UserProfileModule", "__decorate", "declarations", "imports"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\user-profile\\user-profile.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { UserProfileRoutingModule } from './user-profile-routing.module';\r\nimport { UserProfileComponent } from './user-profile.component';\r\nimport { ProfileMenuComponent } from './components/profile-menu/profile-menu.component';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport {CalendarModule} from 'primeng/calendar';\r\nimport {RippleModule} from 'primeng/ripple';\r\nimport { NotificationsProfileComponent } from './components/notifications-profile/notifications-profile.component';\r\nimport { NotificationComponent } from './components/notifications-profile/notification/notification.component';\r\nimport { PackageHistoryComponent } from './components/package-history/package-history.component';\r\nimport { RateExperienceComponent } from './components/rate-experience/rate-experience.component';\r\nimport { ClassroomModule } from '../classroom/classroom.module';\r\nimport { SettingsComponent } from './components/settings/settings.component';\r\nimport { InfoComponent } from './components/info/info.component';\r\nimport { SharedModule } from 'src/app/shared/shared.module';\r\nimport { EditBioDialogComponent } from './components/edit-bio-dialog/edit-bio-dialog.component';\r\nimport { NotificationAlertsSettingsComponent } from './components/notification-alerts-settings/notification-alerts-settings.component';\r\nimport { InputSwitchModule } from 'primeng/inputswitch';\r\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    UserProfileComponent,\r\n    ProfileMenuComponent,\r\n    NotificationsProfileComponent,\r\n    NotificationComponent,\r\n    PackageHistoryComponent,\r\n    RateExperienceComponent,\r\n    SettingsComponent,\r\n    InfoComponent,\r\n    EditBioDialogComponent,\r\n    NotificationAlertsSettingsComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    ClassroomModule,\r\n    UserProfileRoutingModule,\r\n    DropdownModule,\r\n    SharedModule,\r\n    ButtonModule,\r\n    InputTextModule,\r\n    CalendarModule,\r\n    RippleModule,\r\n    InputSwitchModule,\r\n    ConfirmDialogModule,\r\n  ]\r\n})\r\nexport class UserProfileModule { }\r\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,oBAAoB,QAAQ,kDAAkD;AACvF,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAAQC,cAAc,QAAO,kBAAkB;AAC/C,SAAQC,YAAY,QAAO,gBAAgB;AAC3C,SAASC,6BAA6B,QAAQ,oEAAoE;AAClH,SAASC,qBAAqB,QAAQ,wEAAwE;AAC9G,SAASC,uBAAuB,QAAQ,wDAAwD;AAChG,SAASC,uBAAuB,QAAQ,wDAAwD;AAChG,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,iBAAiB,QAAQ,0CAA0C;AAC5E,SAASC,aAAa,QAAQ,kCAAkC;AAChE,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,sBAAsB,QAAQ,wDAAwD;AAC/F,SAASC,mCAAmC,QAAQ,kFAAkF;AACtI,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,mBAAmB,QAAQ,uBAAuB;AAgCpD,IAAMC,iBAAiB,GAAvB,MAAMA,iBAAiB,GAAI;AAArBA,iBAAiB,GAAAC,UAAA,EA7B7BzB,QAAQ,CAAC;EACR0B,YAAY,EAAE,CACZrB,oBAAoB,EACpBC,oBAAoB,EACpBM,6BAA6B,EAC7BC,qBAAqB,EACrBC,uBAAuB,EACvBC,uBAAuB,EACvBE,iBAAiB,EACjBC,aAAa,EACbE,sBAAsB,EACtBC,mCAAmC,CACpC;EACDM,OAAO,EAAE,CACP1B,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBa,eAAe,EACfZ,wBAAwB,EACxBG,cAAc,EACdY,YAAY,EACZV,YAAY,EACZD,eAAe,EACfE,cAAc,EACdC,YAAY,EACZW,iBAAiB,EACjBC,mBAAmB;CAEtB,CAAC,C,EACWC,iBAAiB,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}