{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/button\";\nimport * as i4 from \"primeng/inputswitch\";\nimport * as i5 from \"primeng/radiobutton\";\nimport * as i6 from \"primeng/calendar\";\nfunction TeacherDaysOffPanelComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"label\", 17);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-inputSwitch\", 18);\n    i0.ɵɵlistener(\"onChange\", function TeacherDaysOffPanelComponent_div_2_Template_p_inputSwitch_onChange_3_listener() {\n      const reason_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(reason_r2.key));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const reason_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reason_r2.name);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formControlName\", reason_r2.key);\n  }\n}\nfunction TeacherDaysOffPanelComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtext(1, \"From date must be before to date\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction TeacherDaysOffPanelComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵelement(1, \"p-radioButton\", 20);\n    i0.ɵɵelementStart(2, \"label\", 21);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputId\", category_r4.key)(\"value\", category_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"for\", category_r4.key);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(category_r4.name);\n  }\n}\nexport let TeacherDaysOffPanelComponent = /*#__PURE__*/(() => {\n  class TeacherDaysOffPanelComponent {\n    constructor(fb) {\n      this.fb = fb;\n      this.form = {};\n      this.fromDate = new Date();\n      this.toDate = new Date();\n      this.dateError = false;\n      this.holidays = true;\n      this.sickness = false;\n      this.other = false;\n      this.cancelLessons = false;\n      this.extendPackages = false;\n      this.categories = [{\n        key: 'cat2',\n        name: 'Cancel arranged lessons'\n      }, {\n        key: 'cat3',\n        name: 'Extend student packages'\n      }];\n      this.reasons = [{\n        key: 'holidays',\n        name: 'Holidays'\n      }, {\n        key: 'sickness',\n        name: 'Sickness'\n      }, {\n        key: 'other',\n        name: 'Other'\n      }];\n      /**\n       * Validates a date range in a FormGroup.\n       * @param {FormGroup} control - The FormGroup to validate.\n       * @returns {object | null} - Returns an object with the `dateRange` property set to true if fromDate is greater than toDate, otherwise returns null.\n       */\n      this.dateRangeValidator = control => {\n        const fromDate = control.get('fromDate').value;\n        const toDate = control.get('toDate').value;\n        if (fromDate && toDate && fromDate > toDate) {\n          return {\n            dateRange: true\n          };\n        }\n        return null;\n      };\n    }\n    ngOnInit() {\n      const formControls = {};\n      for (const reason of this.reasons) {\n        const formControls = {}; // example object\n        formControls[reason.key] = [false];\n      }\n      this.form = this.fb.group({\n        ...formControls,\n        category: [null, Validators.required],\n        holidays: [false],\n        sickness: [false],\n        other: [false],\n        reasonText: ['', Validators.required],\n        period: [null, Validators.required],\n        fromDate: [null, Validators.required],\n        toDate: [null, Validators.required]\n      }, {\n        validators: this.dateRangeValidator\n      });\n    }\n    get selectedCategory() {\n      return this.form.get('category').value;\n    }\n    set selectedCategory(category) {\n      this.form.get('category').setValue(category);\n    }\n    /**\n     * Toggles all form controls except for one specified by the `reason` parameter.\n     * @param {string} reason - The name of the form control to exclude from toggling.\n     * @returns {void}\n     */\n    toggleSwitch(reason) {\n      const formControls = this.form.controls;\n      for (const key in formControls) {\n        if (key !== reason && formControls.hasOwnProperty(key)) {\n          formControls[key].setValue(false);\n        }\n      }\n    }\n    /**\n     * Validates the fromDate and toDate properties of the class instance.\n     * @returns {void}\n     */\n    validateDates() {\n      const fromDate = this.form.get('fromDate').value;\n      const toDate = this.form.get('toDate').value;\n      if (fromDate && toDate && fromDate > toDate) {\n        this.dateError = true;\n      } else {\n        this.dateError = false;\n      }\n    }\n    static #_ = this.ɵfac = function TeacherDaysOffPanelComponent_Factory(t) {\n      return new (t || TeacherDaysOffPanelComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TeacherDaysOffPanelComponent,\n      selectors: [[\"app-teacher-days-off-panel\"]],\n      decls: 23,\n      vars: 4,\n      consts: [[1, \"flex\", \"flex-column\", \"gap-3\", 3, \"formGroup\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"justify-content-center\"], [\"class\", \"field-checkbox my-0\", 4, \"ngFor\", \"ngForOf\"], [1, \"my-1\", \"align-items-center\"], [\"src\", \"assets/icons/calendar-sm.svg\", \"width\", \"14\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"flex-column\"], [1, \"flex\", \"justify-content-center\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-1\"], [1, \"font-xs\"], [\"inputStyleClass\", \"input-blue gradient h-1rem\", \"formControlName\", \"fromDate\", 3, \"onSelect\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-1\", \"ml-3\"], [\"inputStyleClass\", \"input-blue gradient h-1rem\", \"formControlName\", \"toDate\", 3, \"onSelect\"], [\"class\", \"error-message font-2xs\", 4, \"ngIf\"], [1, \"flex\", \"align-items-start\", \"flex-column\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\"], [\"type\", \"submit\", \"icon\", \"pi pi-calendar\", \"iconPos\", \"left\", \"pButton\", \"\", \"type\", \"button\", \"label\", \"Schedule\", 1, \"mt-2\", \"p-button-xs\"], [1, \"field-checkbox\", \"my-0\"], [1, \"lesson-filter-label\", \"font-xs\"], [1, \"flex\", \"small-input-switch\", \"p-mr-2\", 3, \"onChange\", \"formControlName\"], [1, \"error-message\", \"font-2xs\"], [\"name\", \"category\", \"formControlName\", \"category\", 3, \"inputId\", \"value\"], [1, \"font-xs\", 3, \"for\"]],\n      template: function TeacherDaysOffPanelComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"form\", 0)(1, \"div\", 1);\n          i0.ɵɵtemplate(2, TeacherDaysOffPanelComponent_div_2_Template, 4, 2, \"div\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"h6\", 3);\n          i0.ɵɵelement(4, \"img\", 4);\n          i0.ɵɵtext(5, \" Date Range\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"div\", 6)(8, \"div\", 7)(9, \"span\", 8);\n          i0.ɵɵtext(10, \"From\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p-calendar\", 9);\n          i0.ɵɵlistener(\"onSelect\", function TeacherDaysOffPanelComponent_Template_p_calendar_onSelect_11_listener() {\n            return ctx.validateDates();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 10)(13, \"span\", 8);\n          i0.ɵɵtext(14, \"To\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"p-calendar\", 11);\n          i0.ɵɵlistener(\"onSelect\", function TeacherDaysOffPanelComponent_Template_p_calendar_onSelect_15_listener() {\n            return ctx.validateDates();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(16, \"div\", 6);\n          i0.ɵɵtemplate(17, TeacherDaysOffPanelComponent_div_17_Template, 2, 0, \"div\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 5)(19, \"div\", 13);\n          i0.ɵɵtemplate(20, TeacherDaysOffPanelComponent_div_20_Template, 4, 4, \"div\", 2);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(21, \"div\", 14);\n          i0.ɵɵelement(22, \"button\", 15);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"formGroup\", ctx.form);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.reasons);\n          i0.ɵɵadvance(15);\n          i0.ɵɵproperty(\"ngIf\", ctx.dateError);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n        }\n      },\n      dependencies: [i1.ɵNgNoValidate, i1.NgControlStatus, i1.NgControlStatusGroup, i2.NgForOf, i2.NgIf, i3.ButtonDirective, i4.InputSwitch, i5.RadioButton, i1.FormGroupDirective, i1.FormControlName, i6.Calendar],\n      styles: [\"[_nghost-%COMP%]     .small-input-switch .p-inputswitch{height:1rem;width:2.4rem}[_nghost-%COMP%]     .small-input-switch .p-inputswitch .p-inputswitch-slider:before{width:.8rem;height:.8rem;margin-top:-.45rem}\"]\n    });\n  }\n  return TeacherDaysOffPanelComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}