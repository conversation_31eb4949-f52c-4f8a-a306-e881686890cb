{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class DisableOnClickDirective {\n  constructor(el, renderer) {\n    this.el = el;\n    this.renderer = renderer;\n    this.disableFlag = false; // Flag to indicate whether the element is disabled\n  }\n  onClick(event) {\n    event.preventDefault();\n    if (!this.disableFlag) {\n      this.disableElement();\n      if (this.disableTime) {\n        setTimeout(() => {\n          this.enableElement();\n        }, this.disableTime);\n      }\n    }\n  }\n  disableElement() {\n    this.disableFlag = true;\n    this.renderer.setProperty(this.el.nativeElement, 'disabled', true);\n    this.renderer.addClass(this.el.nativeElement, 'disabled');\n    this.renderer.addClass(this.el.nativeElement, 'disabled-div');\n  }\n  enableElement() {\n    this.disableFlag = false;\n    this.renderer.setProperty(this.el.nativeElement, 'disabled', false);\n    this.renderer.removeClass(this.el.nativeElement, 'disabled');\n    this.renderer.removeClass(this.el.nativeElement, 'disabled-div');\n  }\n  static #_ = this.ɵfac = function DisableOnClickDirective_Factory(t) {\n    return new (t || DisableOnClickDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n  };\n  static #_2 = this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n    type: DisableOnClickDirective,\n    selectors: [[\"\", \"disableOnClick\", \"\"]],\n    hostBindings: function DisableOnClickDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function DisableOnClickDirective_click_HostBindingHandler($event) {\n          return ctx.onClick($event);\n        });\n      }\n    },\n    inputs: {\n      disableTime: \"disableTime\",\n      disableFlag: \"disableFlag\"\n    }\n  });\n}", "map": {"version": 3, "names": ["DisableOnClickDirective", "constructor", "el", "renderer", "disableFlag", "onClick", "event", "preventDefault", "disableElement", "disableTime", "setTimeout", "enableElement", "setProperty", "nativeElement", "addClass", "removeClass", "_", "i0", "ɵɵdirectiveInject", "ElementRef", "Renderer2", "_2", "selectors", "hostBindings", "DisableOnClickDirective_HostBindings", "rf", "ctx", "ɵɵlistener", "DisableOnClickDirective_click_HostBindingHandler", "$event"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\directives\\button-disable.directive.ts"], "sourcesContent": ["import { Directive, HostListener, Input, Renderer2, ElementRef } from '@angular/core';\r\n\r\n@Directive({\r\n  selector: '[disableOnClick]'\r\n})\r\nexport class DisableOnClickDirective {\r\n  @Input() disableTime!: number; // Disable time in milliseconds\r\n  @Input() disableFlag = false; // Flag to indicate whether the element is disabled\r\n\r\n  constructor(private el: ElementRef, private renderer: Renderer2) {}\r\n\r\n  @HostListener('click', ['$event']) onClick(event: Event) {\r\n    event.preventDefault();\r\n\r\n    if (!this.disableFlag) {\r\n      this.disableElement();\r\n      if (this.disableTime) {\r\n        setTimeout(() => {\r\n          this.enableElement();\r\n        }, this.disableTime);\r\n      }\r\n    }\r\n  }\r\n\r\n  private disableElement() {\r\n    this.disableFlag = true;\r\n    this.renderer.setProperty(this.el.nativeElement, 'disabled', true);\r\n    this.renderer.addClass(this.el.nativeElement, 'disabled');\r\n    this.renderer.addClass(this.el.nativeElement, 'disabled-div');\r\n  }\r\n\r\n  private enableElement() {\r\n    this.disableFlag = false;\r\n    this.renderer.setProperty(this.el.nativeElement, 'disabled', false);\r\n    this.renderer.removeClass(this.el.nativeElement, 'disabled');\r\n    this.renderer.removeClass(this.el.nativeElement, 'disabled-div');\r\n  }\r\n}\r\n"], "mappings": ";AAKA,OAAM,MAAOA,uBAAuB;EAIlCC,YAAoBC,EAAc,EAAUC,QAAmB;IAA3C,KAAAD,EAAE,GAAFA,EAAE;IAAsB,KAAAC,QAAQ,GAARA,QAAQ;IAF3C,KAAAC,WAAW,GAAG,KAAK,CAAC,CAAC;EAEoC;EAE/BC,OAAOA,CAACC,KAAY;IACrDA,KAAK,CAACC,cAAc,EAAE;IAEtB,IAAI,CAAC,IAAI,CAACH,WAAW,EAAE;MACrB,IAAI,CAACI,cAAc,EAAE;MACrB,IAAI,IAAI,CAACC,WAAW,EAAE;QACpBC,UAAU,CAAC,MAAK;UACd,IAAI,CAACC,aAAa,EAAE;QACtB,CAAC,EAAE,IAAI,CAACF,WAAW,CAAC;MACtB;IACF;EACF;EAEQD,cAAcA,CAAA;IACpB,IAAI,CAACJ,WAAW,GAAG,IAAI;IACvB,IAAI,CAACD,QAAQ,CAACS,WAAW,CAAC,IAAI,CAACV,EAAE,CAACW,aAAa,EAAE,UAAU,EAAE,IAAI,CAAC;IAClE,IAAI,CAACV,QAAQ,CAACW,QAAQ,CAAC,IAAI,CAACZ,EAAE,CAACW,aAAa,EAAE,UAAU,CAAC;IACzD,IAAI,CAACV,QAAQ,CAACW,QAAQ,CAAC,IAAI,CAACZ,EAAE,CAACW,aAAa,EAAE,cAAc,CAAC;EAC/D;EAEQF,aAAaA,CAAA;IACnB,IAAI,CAACP,WAAW,GAAG,KAAK;IACxB,IAAI,CAACD,QAAQ,CAACS,WAAW,CAAC,IAAI,CAACV,EAAE,CAACW,aAAa,EAAE,UAAU,EAAE,KAAK,CAAC;IACnE,IAAI,CAACV,QAAQ,CAACY,WAAW,CAAC,IAAI,CAACb,EAAE,CAACW,aAAa,EAAE,UAAU,CAAC;IAC5D,IAAI,CAACV,QAAQ,CAACY,WAAW,CAAC,IAAI,CAACb,EAAE,CAACW,aAAa,EAAE,cAAc,CAAC;EAClE;EAAC,QAAAG,CAAA,G;qBA/BUhB,uBAAuB,EAAAiB,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,UAAA,GAAAF,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAG,SAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAvBrB,uBAAuB;IAAAsB,SAAA;IAAAC,YAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAvBR,EAAA,CAAAU,UAAA,mBAAAC,iDAAAC,MAAA;UAAA,OAAAH,GAAA,CAAArB,OAAA,CAAAwB,MAAA,CAAe;QAAA,EAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}