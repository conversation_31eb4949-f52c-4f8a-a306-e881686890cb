{"ast": null, "code": "import { throwError } from \"rxjs\";\nimport { catchError } from \"rxjs/operators\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/toast.service\";\nexport let ErrorInterceptor = /*#__PURE__*/(() => {\n  class ErrorInterceptor {\n    constructor(toastService) {\n      this.toastService = toastService;\n    }\n    intercept(req, next) {\n      if (req.headers.has('X-Bypass-Error-Interceptor')) {\n        return next.handle(req);\n      }\n      return next.handle(req).pipe(catchError(error => {\n        console.log(error);\n        let errorMessage = \"An unknown error occured\";\n        if (error.error && error.error.error && error.error.error.message) {\n          errorMessage = error.error.error.message;\n        }\n        if (error.error && error.error.message) {\n          errorMessage = error.error.message;\n        }\n        this.toastService.setShowToastmessage({\n          severity: 'error',\n          summary: '',\n          detail: errorMessage\n        });\n        return throwError(error);\n      }));\n    }\n    static #_ = this.ɵfac = function ErrorInterceptor_Factory(t) {\n      return new (t || ErrorInterceptor)(i0.ɵɵinject(i1.ToastService));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ErrorInterceptor,\n      factory: ErrorInterceptor.ɵfac\n    });\n  }\n  return ErrorInterceptor;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}