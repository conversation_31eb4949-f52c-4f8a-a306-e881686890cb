{"ast": null, "code": "import { switchMap, take, tap } from 'rxjs/operators';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nimport * as i2 from \"src/app/core/services/layout.service\";\nimport * as i3 from \"src/app/core/services/classroom.service\";\nimport * as i4 from \"src/app/core/services/user.service\";\nimport * as i5 from \"src/app/core/services/auth.service\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"../../header/header.component\";\nimport * as i9 from \"../../menu/menu.component\";\nimport * as i10 from \"../../mobile-menu/mobile-menu.component\";\nconst _c0 = [\"scale\"];\nconst _c1 = [\"menuOuterDiv\"];\nfunction SideMenuContainerLayoutComponent_app_header_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-header\");\n  }\n}\nfunction SideMenuContainerLayoutComponent_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nexport class SideMenuContainerLayoutComponent {\n  constructor(generalService, layoutService, classroomService, userService, authService, router) {\n    this.generalService = generalService;\n    this.layoutService = layoutService;\n    this.classroomService = classroomService;\n    this.userService = userService;\n    this.authService = authService;\n    this.router = router;\n    this.isBelow768 = false;\n    this.subs = new SubSink();\n    this.menuOuterDiv = {};\n    this.menuItems = [];\n    this.loggedInUser = {};\n  }\n  ngOnInit() {\n    this.loggedInUser = this.authService.getLoggedInUser();\n    this.menuItems = this.layoutService.getMenuItems();\n    this.subs.add(this.userService.getUserById(this.authService.getLoggedInUser().aspUserId).subscribe(res => {\n      console.log(res);\n      // this.loggenInUser = res;\n      this.userService.updateAvatarUrl(res.photo);\n    }));\n    this.subs.add(this.generalService.deviceKind.subscribe(res => {\n      // Check if the height of the source div element is greater than 0\n      this.isBelow768 = res.is768;\n      // TODO check if rescaling is needed...\n      // if (res.w992up && !res.w1366up) {\n      //   this.generalService.scaleElementDown(this.ratingDetails, \"left top\", 0.8);\n      // } else {\n      //   this.generalService.scaleElementReset(this.ratingDetails);\n      // }\n    }));\n    // if (this.authService.isStudent) {\n    //   this.handleStudentClassroomDataAndMenu();\n    // }\n  }\n  ngAfterViewChecked() {\n    if (this.menuOuterDiv.nativeElement.offsetHeight > 0) {\n      this.layoutService.setSideMenuHeight(this.menuOuterDiv.nativeElement.offsetHeight);\n    }\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n    this.menuItems = [];\n  }\n  menuUrlChanged(event) {\n    this.router.navigateByUrl(event);\n  }\n  handleStudentClassroomDataAndMenu() {\n    this.subs.add(this.classroomService.getLMSUserClassrooms(this.loggedInUser.id).pipe(switchMap(response => {\n      return this.userService.getUserHasPayment().pipe(tap(res => {\n        response = {\n          response,\n          res\n        };\n        const isStudentActive = this.classroomService.isStudentActive(response.response, response.res);\n        this.layoutService.updateMenuItemDisabledStatus(\"Classrooms\", response.length === 0);\n        if (isStudentActive) {\n          this.layoutService.updateAllMenuItemsDisabledStatus(false);\n        } else {\n          this.layoutService.updateMenuItemClassroomToTrialsForStudent(res);\n        }\n        console.log(response);\n      }));\n    }), take(1)).subscribe());\n  }\n  static #_ = this.ɵfac = function SideMenuContainerLayoutComponent_Factory(t) {\n    return new (t || SideMenuContainerLayoutComponent)(i0.ɵɵdirectiveInject(i1.GeneralService), i0.ɵɵdirectiveInject(i2.LayoutService), i0.ɵɵdirectiveInject(i3.ClassroomService), i0.ɵɵdirectiveInject(i4.UserService), i0.ɵɵdirectiveInject(i5.AuthService), i0.ɵɵdirectiveInject(i6.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SideMenuContainerLayoutComponent,\n    selectors: [[\"app-side-menu-container-layout\"]],\n    viewQuery: function SideMenuContainerLayoutComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n        i0.ɵɵviewQuery(_c1, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ratingDetails = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.menuOuterDiv = _t.first);\n      }\n    },\n    decls: 14,\n    vars: 3,\n    consts: [[\"scale\", \"\"], [\"menuOuterDiv\", \"\"], [4, \"ngIf\"], [1, \"scale\", \"sm:pb-4\"], [\"id\", \"mainContainerSideMenu\", 1, \"container\", \"p-0\", \"sm:pt-5\"], [\"id\", \"mainContainerSideMenuBelow\", 1, \"padding-top-section\", \"mt-0\", \"md:mt-2\"], [1, \"sm:block\", \"md:flex\", \"grid-nogutter\"], [1, \"col-fixed\", \"si\"], [1, \"sticky-menu-wrapper\"], [1, \"menu-column\", \"full-width\", 3, \"menuItems\"], [\"id\", \"mainContentColumn\", 1, \"col\", \"md:pl-2\", \"content-column\"]],\n    template: function SideMenuContainerLayoutComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, SideMenuContainerLayoutComponent_app_header_0_Template, 1, 0, \"app-header\", 2);\n        i0.ɵɵelement(1, \"app-mobile-menu\");\n        i0.ɵɵelementStart(2, \"div\", 3, 0)(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6);\n        i0.ɵɵtemplate(7, SideMenuContainerLayoutComponent_ng_container_7_Template, 1, 0, \"ng-container\", 2);\n        i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8, 1);\n        i0.ɵɵelement(11, \"app-menu\", 9);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(12, \"div\", 10);\n        i0.ɵɵelement(13, \"router-outlet\");\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", !ctx.isBelow768);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngIf\", ctx.isBelow768);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"menuItems\", ctx.menuItems);\n      }\n    },\n    dependencies: [i7.NgIf, i6.RouterOutlet, i8.HeaderComponent, i9.MenuComponent, i10.MobileMenuComponent],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\nbody[_ngcontent-%COMP%] {\\n  --ratio: 360 / 640;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100vw;\\n  height: calc(100vw * var(--ratio));\\n}\\n\\n.max-width[_ngcontent-%COMP%] {\\n  --container-width: 30em;\\n  height: calc(100vw * var(--ratio));\\n  max-height: calc(var(--container-width) * var(--ratio));\\n  width: 100%;\\n  max-width: var(--container-width);\\n}\\n\\n.padding-top-section[_ngcontent-%COMP%] {\\n  padding-left: 0;\\n  padding-right: 0;\\n  padding-top: 3rem;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .padding-top-section[_ngcontent-%COMP%] {\\n    padding-left: 1rem;\\n    padding-right: 1rem;\\n  }\\n}\\n@media screen and (max-width: 768px) {\\n  .padding-top-section[_ngcontent-%COMP%] {\\n    padding-top: 4.4rem;\\n  }\\n}\\n@media screen and (min-height: 400px) and (max-width: 950px) {\\n  .padding-top-section[_ngcontent-%COMP%] {\\n    padding-top: 4rem;\\n  }\\n}\\n\\n.menu-column[_ngcontent-%COMP%] {\\n  width: 260px;\\n}\\n\\n.content-column[_ngcontent-%COMP%] {\\n  overflow-x: clip;\\n  overflow-y: hidden;\\n}\\n\\n.si[_ngcontent-%COMP%] {\\n  width: 100%;\\n  \\n\\n  box-sizing: border-box;\\n}\\n@media only screen and (min-width: 768px) {\\n  .si[_ngcontent-%COMP%] {\\n    width: clamp(3.75rem, 16.08vw + -3.08rem, 6.88rem);\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .si[_ngcontent-%COMP%] {\\n    width: clamp(9.38rem, 3.49vw + 7.21rem, 13.75rem);\\n  }\\n}\\n\\n.sticky-menu-wrapper[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 56px;\\n}\\n@media screen and (min-width: 1400px) {\\n  .sticky-menu-wrapper[_ngcontent-%COMP%] {\\n    top: 56px;\\n  }\\n}\\n@media screen and (min-height: 400px) and (max-width: 950px) {\\n  .sticky-menu-wrapper[_ngcontent-%COMP%] {\\n    top: 70px;\\n  }\\n}\\n\\n@media only screen and (min-width: 768px) and (max-width: 769px) {\\n  .si[_ngcontent-%COMP%] {\\n    display: none !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["switchMap", "take", "tap", "SubSink", "i0", "ɵɵelement", "ɵɵelementContainer", "SideMenuContainerLayoutComponent", "constructor", "generalService", "layoutService", "classroomService", "userService", "authService", "router", "isBelow768", "subs", "menuOuterDiv", "menuItems", "loggedInUser", "ngOnInit", "getLoggedInUser", "getMenuItems", "add", "getUserById", "aspUserId", "subscribe", "res", "console", "log", "updateAvatarUrl", "photo", "deviceKind", "is768", "ngAfterViewChecked", "nativeElement", "offsetHeight", "setSideMenuHeight", "ngOnDestroy", "unsubscribe", "menuUrlChanged", "event", "navigateByUrl", "handleStudentClassroomDataAndMenu", "getLMSUserClassrooms", "id", "pipe", "response", "getUserHasPayment", "isStudentActive", "updateMenuItemDisabledStatus", "length", "updateAllMenuItemsDisabledStatus", "updateMenuItemClassroomToTrialsForStudent", "_", "ɵɵdirectiveInject", "i1", "GeneralService", "i2", "LayoutService", "i3", "ClassroomService", "i4", "UserService", "i5", "AuthService", "i6", "Router", "_2", "selectors", "viewQuery", "SideMenuContainerLayoutComponent_Query", "rf", "ctx", "ɵɵtemplate", "SideMenuContainerLayoutComponent_app_header_0_Template", "ɵɵelementStart", "SideMenuContainerLayoutComponent_ng_container_7_Template", "ɵɵelementEnd", "ɵɵproperty", "ɵɵadvance"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\layout\\side-menu-container-layout\\side-menu-container-layout.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\layout\\side-menu-container-layout\\side-menu-container-layout.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { switchMap, filter, take, catchError, tap } from 'rxjs/operators';\r\nimport { LeftMenuItem } from 'src/app/core/models/general.model';\r\nimport { User } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { LayoutService } from 'src/app/core/services/layout.service';\r\nimport { UserService } from 'src/app/core/services/user.service';\r\nimport { SubSink } from 'subsink';\r\n\r\n\r\n\r\n@Component({\r\n  selector: 'app-side-menu-container-layout',\r\n  templateUrl: './side-menu-container-layout.component.html',\r\n  styleUrls: ['./side-menu-container-layout.component.scss'],\r\n})\r\nexport class SideMenuContainerLayoutComponent implements OnInit {\r\n\r\n  isBelow768 = false;\r\n  private subs = new SubSink();\r\n  @ViewChild('scale',{static:true}) public ratingDetails: any;\r\n  @ViewChild('menuOuterDiv', {static: true}) menuOuterDiv: ElementRef = {} as ElementRef;\r\n  menuItems: LeftMenuItem[] = [];\r\n  public loggedInUser: User = {} as User;\r\n\r\n  constructor(\r\n    private generalService: GeneralService,\r\n    private layoutService: LayoutService,\r\n    private classroomService: ClassroomService,\r\n    private userService: UserService,\r\n    private authService: AuthService,\r\n    private router: Router,\r\n    ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.loggedInUser = this.authService.getLoggedInUser();\r\n    this.menuItems = this.layoutService.getMenuItems();\r\n\r\n    this.subs.add(this.userService.getUserById(this.authService.getLoggedInUser().aspUserId!).subscribe((res: User) => {\r\n      console.log(res);\r\n      // this.loggenInUser = res;\r\n      this.userService.updateAvatarUrl(res.photo!);\r\n    }));\r\n\r\n    this.subs.add(this.generalService.deviceKind.subscribe(res => {\r\n\r\n      // Check if the height of the source div element is greater than 0\r\n      \r\n      this.isBelow768 = res.is768;\r\n      // TODO check if rescaling is needed...\r\n      // if (res.w992up && !res.w1366up) {\r\n\r\n      //   this.generalService.scaleElementDown(this.ratingDetails, \"left top\", 0.8);\r\n      // } else {\r\n      //   this.generalService.scaleElementReset(this.ratingDetails);\r\n      // }\r\n    }));\r\n\r\n    // if (this.authService.isStudent) {\r\n    //   this.handleStudentClassroomDataAndMenu();\r\n    // }\r\n\r\n  }\r\n\r\n  ngAfterViewChecked() {\r\n    if (this.menuOuterDiv.nativeElement.offsetHeight > 0) {\r\n        this.layoutService.setSideMenuHeight(this.menuOuterDiv.nativeElement.offsetHeight);\r\n    }\r\n\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n    this.menuItems = [];\r\n  }\r\n\r\n  menuUrlChanged(event: any) {\r\n    this.router.navigateByUrl(event);\r\n  }\r\n\r\n  private handleStudentClassroomDataAndMenu() {\r\n    this.subs.add(\r\n      this.classroomService.getLMSUserClassrooms(this.loggedInUser.id).pipe(\r\n        switchMap((response) => {\r\n          return this.userService.getUserHasPayment().pipe(\r\n            tap((res: any) => {\r\n              response = { response, res } as any;\r\n              const isStudentActive = this.classroomService.isStudentActive(response.response, response.res);\r\n              this.layoutService.updateMenuItemDisabledStatus(\"Classrooms\", response.length === 0);\r\n              if (isStudentActive) {\r\n                this.layoutService.updateAllMenuItemsDisabledStatus(false);\r\n              } else {\r\n                this.layoutService.updateMenuItemClassroomToTrialsForStudent(res);\r\n              }\r\n              console.log(response);\r\n            })\r\n          );\r\n        }),\r\n        take(1)\r\n      ).subscribe()\r\n    );\r\n  }\r\n\r\n}\r\n", "<app-header *ngIf=\"!isBelow768\"></app-header>\r\n<app-mobile-menu></app-mobile-menu>\r\n<div #scale class=\"scale sm:pb-4\">\r\n    <div class=\"container p-0 sm:pt-5\" id=\"mainContainerSideMenu\">\r\n        <div class=\"padding-top-section mt-0 md:mt-2\"  id=\"mainContainerSideMenuBelow\">\r\n            <div class=\"sm:block md:flex grid-nogutter \">\r\n                <ng-container *ngIf=\"isBelow768\">\r\n                    <!-- <app-mobile-left-menu [menuItems]=\"menuItems\"\r\n                    [hideRightSideButton]=\"true\"\r\n                        (onMenuUrlChange)=\"menuUrlChanged($event)\"></app-mobile-left-menu> -->\r\n                </ng-container>\r\n                <div class=\"col-fixed si\">\r\n                    <div class=\"sticky-menu-wrapper\" #menuOuterDiv>\r\n                    <app-menu [menuItems]=\"menuItems\" class=\"menu-column full-width\"></app-menu>\r\n                    </div>\r\n                </div>\r\n                <div class=\"col md:pl-2 content-column\" id=\"mainContentColumn\">\r\n                    <router-outlet></router-outlet>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": "AAEA,SAASA,SAAS,EAAUC,IAAI,EAAcC,GAAG,QAAQ,gBAAgB;AAQzE,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;;;;;ICVjCC,EAAA,CAAAC,SAAA,iBAA6C;;;;;IAM7BD,EAAA,CAAAE,kBAAA,GAIe;;;ADS/B,OAAM,MAAOC,gCAAgC;EAS3CC,YACUC,cAA8B,EAC9BC,aAA4B,EAC5BC,gBAAkC,EAClCC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc;IALd,KAAAL,cAAc,GAAdA,cAAc;IACd,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAbhB,KAAAC,UAAU,GAAG,KAAK;IACV,KAAAC,IAAI,GAAG,IAAIb,OAAO,EAAE;IAEe,KAAAc,YAAY,GAAe,EAAgB;IACtF,KAAAC,SAAS,GAAmB,EAAE;IACvB,KAAAC,YAAY,GAAS,EAAU;EAShC;EAENC,QAAQA,CAAA;IACN,IAAI,CAACD,YAAY,GAAG,IAAI,CAACN,WAAW,CAACQ,eAAe,EAAE;IACtD,IAAI,CAACH,SAAS,GAAG,IAAI,CAACR,aAAa,CAACY,YAAY,EAAE;IAElD,IAAI,CAACN,IAAI,CAACO,GAAG,CAAC,IAAI,CAACX,WAAW,CAACY,WAAW,CAAC,IAAI,CAACX,WAAW,CAACQ,eAAe,EAAE,CAACI,SAAU,CAAC,CAACC,SAAS,CAAEC,GAAS,IAAI;MAChHC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;MAChB;MACA,IAAI,CAACf,WAAW,CAACkB,eAAe,CAACH,GAAG,CAACI,KAAM,CAAC;IAC9C,CAAC,CAAC,CAAC;IAEH,IAAI,CAACf,IAAI,CAACO,GAAG,CAAC,IAAI,CAACd,cAAc,CAACuB,UAAU,CAACN,SAAS,CAACC,GAAG,IAAG;MAE3D;MAEA,IAAI,CAACZ,UAAU,GAAGY,GAAG,CAACM,KAAK;MAC3B;MACA;MAEA;MACA;MACA;MACA;IACF,CAAC,CAAC,CAAC;IAEH;IACA;IACA;EAEF;EAEAC,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAACjB,YAAY,CAACkB,aAAa,CAACC,YAAY,GAAG,CAAC,EAAE;MAClD,IAAI,CAAC1B,aAAa,CAAC2B,iBAAiB,CAAC,IAAI,CAACpB,YAAY,CAACkB,aAAa,CAACC,YAAY,CAAC;IACtF;EAEF;EAEAE,WAAWA,CAAA;IACT,IAAI,CAACtB,IAAI,CAACuB,WAAW,EAAE;IACvB,IAAI,CAACrB,SAAS,GAAG,EAAE;EACrB;EAEAsB,cAAcA,CAACC,KAAU;IACvB,IAAI,CAAC3B,MAAM,CAAC4B,aAAa,CAACD,KAAK,CAAC;EAClC;EAEQE,iCAAiCA,CAAA;IACvC,IAAI,CAAC3B,IAAI,CAACO,GAAG,CACX,IAAI,CAACZ,gBAAgB,CAACiC,oBAAoB,CAAC,IAAI,CAACzB,YAAY,CAAC0B,EAAE,CAAC,CAACC,IAAI,CACnE9C,SAAS,CAAE+C,QAAQ,IAAI;MACrB,OAAO,IAAI,CAACnC,WAAW,CAACoC,iBAAiB,EAAE,CAACF,IAAI,CAC9C5C,GAAG,CAAEyB,GAAQ,IAAI;QACfoB,QAAQ,GAAG;UAAEA,QAAQ;UAAEpB;QAAG,CAAS;QACnC,MAAMsB,eAAe,GAAG,IAAI,CAACtC,gBAAgB,CAACsC,eAAe,CAACF,QAAQ,CAACA,QAAQ,EAAEA,QAAQ,CAACpB,GAAG,CAAC;QAC9F,IAAI,CAACjB,aAAa,CAACwC,4BAA4B,CAAC,YAAY,EAAEH,QAAQ,CAACI,MAAM,KAAK,CAAC,CAAC;QACpF,IAAIF,eAAe,EAAE;UACnB,IAAI,CAACvC,aAAa,CAAC0C,gCAAgC,CAAC,KAAK,CAAC;QAC5D,CAAC,MAAM;UACL,IAAI,CAAC1C,aAAa,CAAC2C,yCAAyC,CAAC1B,GAAG,CAAC;QACnE;QACAC,OAAO,CAACC,GAAG,CAACkB,QAAQ,CAAC;MACvB,CAAC,CAAC,CACH;IACH,CAAC,CAAC,EACF9C,IAAI,CAAC,CAAC,CAAC,CACR,CAACyB,SAAS,EAAE,CACd;EACH;EAAC,QAAA4B,CAAA,G;qBArFU/C,gCAAgC,EAAAH,EAAA,CAAAmD,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAArD,EAAA,CAAAmD,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAvD,EAAA,CAAAmD,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAzD,EAAA,CAAAmD,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA3D,EAAA,CAAAmD,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAA7D,EAAA,CAAAmD,iBAAA,CAAAW,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAhC7D,gCAAgC;IAAA8D,SAAA;IAAAC,SAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;QCnB7CpE,EAAA,CAAAsE,UAAA,IAAAC,sDAAA,wBAAgC;QAChCvE,EAAA,CAAAC,SAAA,sBAAmC;QAIvBD,EAHZ,CAAAwE,cAAA,gBAAkC,aACgC,aACqB,aAC9B;QACzCxE,EAAA,CAAAsE,UAAA,IAAAG,wDAAA,0BAAiC;QAM7BzE,EADJ,CAAAwE,cAAA,aAA0B,gBACyB;QAC/CxE,EAAA,CAAAC,SAAA,mBAA4E;QAEhFD,EADI,CAAA0E,YAAA,EAAM,EACJ;QACN1E,EAAA,CAAAwE,cAAA,eAA+D;QAC3DxE,EAAA,CAAAC,SAAA,qBAA+B;QAKnDD,EAJgB,CAAA0E,YAAA,EAAM,EACJ,EACJ,EACJ,EACJ;;;QAtBO1E,EAAA,CAAA2E,UAAA,UAAAN,GAAA,CAAA1D,UAAA,CAAiB;QAMCX,EAAA,CAAA4E,SAAA,GAAgB;QAAhB5E,EAAA,CAAA2E,UAAA,SAAAN,GAAA,CAAA1D,UAAA,CAAgB;QAOjBX,EAAA,CAAA4E,SAAA,GAAuB;QAAvB5E,EAAA,CAAA2E,UAAA,cAAAN,GAAA,CAAAvD,SAAA,CAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}