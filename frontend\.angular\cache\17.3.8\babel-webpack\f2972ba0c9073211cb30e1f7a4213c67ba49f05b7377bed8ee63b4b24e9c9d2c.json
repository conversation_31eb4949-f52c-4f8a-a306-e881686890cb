{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ChatRoutingModule } from './chat-routing.module';\nimport * as i0 from \"@angular/core\";\nexport class ChatModule {\n  static #_ = this.ɵfac = function ChatModule_Factory(t) {\n    return new (t || ChatModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ChatModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, ChatRoutingModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ChatModule, {\n    imports: [CommonModule, ChatRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ChatRoutingModule", "ChatModule", "_", "_2", "_3", "imports"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\chat\\chat.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { ChatRoutingModule } from './chat-routing.module';\r\n\r\n\r\n@NgModule({\r\n  declarations: [],\r\n  imports: [\r\n    CommonModule,\r\n    ChatRoutingModule\r\n  ]\r\n})\r\nexport class ChatModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,iBAAiB,QAAQ,uBAAuB;;AAUzD,OAAM,MAAOC,UAAU;EAAA,QAAAC,CAAA,G;qBAAVD,UAAU;EAAA;EAAA,QAAAE,EAAA,G;UAAVF;EAAU;EAAA,QAAAG,EAAA,G;cAJnBL,YAAY,EACZC,iBAAiB;EAAA;;;2EAGRC,UAAU;IAAAI,OAAA,GAJnBN,YAAY,EACZC,iBAAiB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}