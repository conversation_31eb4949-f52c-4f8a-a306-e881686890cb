{"ast": null, "code": "import { MODE, URLS } from './constants';\nimport { getVersionedUrl, loadModule } from './utils';\nimport { RevolutPaymentsVersionLoader } from './versionLoader';\nvar loadedUpsellInstance = null;\nexport function RevolutUpsellLoader(token, mode, locale) {\n  if (mode === void 0) {\n    mode = RevolutUpsellLoader.mode;\n  }\n  if (loadedUpsellInstance) {\n    var instance = loadedUpsellInstance({\n      publicToken: token,\n      locale: locale\n    });\n    return Promise.resolve(instance);\n  }\n  return RevolutPaymentsVersionLoader(mode).then(function (version) {\n    return loadRevolutUpsell(version, token, mode, locale);\n  });\n}\nfunction loadRevolutUpsell(version, token, mode, locale) {\n  return loadModule({\n    src: getVersionedUrl(URLS[mode].upsell, version),\n    id: 'revolut-upsell',\n    name: 'RevolutUpsell'\n  }).then(function (scriptElement) {\n    if (typeof RevolutUpsell === 'function') {\n      loadedUpsellInstance = RevolutUpsell;\n      delete window.RevolutUpsell;\n      return loadedUpsellInstance({\n        publicToken: token,\n        locale: locale\n      });\n    } else {\n      document.head.removeChild(scriptElement);\n      throw new Error(\"'RevolutUpsell' failed to load: RevolutUpsell is not a function\");\n    }\n  });\n}\nRevolutUpsellLoader.mode = MODE.PRODUCTION;", "map": {"version": 3, "names": ["MODE", "URLS", "getVersionedUrl", "loadModule", "RevolutPaymentsVersionLoader", "loadedUpsellInstance", "RevolutUpsellLoader", "token", "mode", "locale", "instance", "publicToken", "Promise", "resolve", "then", "version", "loadRevolutUpsell", "src", "upsell", "id", "name", "scriptElement", "RevolutUpsell", "window", "document", "head", "<PERSON><PERSON><PERSON><PERSON>", "Error", "PRODUCTION"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@revolut/checkout/esm/upsellLoader.js"], "sourcesContent": ["import { MODE, URLS } from './constants';\nimport { getVersionedUrl, loadModule } from './utils';\nimport { RevolutPaymentsVersionLoader } from './versionLoader';\nvar loadedUpsellInstance = null;\nexport function RevolutUpsellLoader(token, mode, locale) {\n    if (mode === void 0) { mode = RevolutUpsellLoader.mode; }\n    if (loadedUpsellInstance) {\n        var instance = loadedUpsellInstance({ publicToken: token, locale: locale });\n        return Promise.resolve(instance);\n    }\n    return RevolutPaymentsVersionLoader(mode).then(function (version) {\n        return loadRevolutUpsell(version, token, mode, locale);\n    });\n}\nfunction loadRevolutUpsell(version, token, mode, locale) {\n    return loadModule({\n        src: getVersionedUrl(URLS[mode].upsell, version),\n        id: 'revolut-upsell',\n        name: 'RevolutUpsell',\n    }).then(function (scriptElement) {\n        if (typeof RevolutUpsell === 'function') {\n            loadedUpsellInstance = RevolutUpsell;\n            delete window.RevolutUpsell;\n            return loadedUpsellInstance({ publicToken: token, locale: locale });\n        }\n        else {\n            document.head.removeChild(scriptElement);\n            throw new Error(\"'RevolutUpsell' failed to load: RevolutUpsell is not a function\");\n        }\n    });\n}\nRevolutUpsellLoader.mode = MODE.PRODUCTION;\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,IAAI,QAAQ,aAAa;AACxC,SAASC,eAAe,EAAEC,UAAU,QAAQ,SAAS;AACrD,SAASC,4BAA4B,QAAQ,iBAAiB;AAC9D,IAAIC,oBAAoB,GAAG,IAAI;AAC/B,OAAO,SAASC,mBAAmBA,CAACC,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAE;EACrD,IAAID,IAAI,KAAK,KAAK,CAAC,EAAE;IAAEA,IAAI,GAAGF,mBAAmB,CAACE,IAAI;EAAE;EACxD,IAAIH,oBAAoB,EAAE;IACtB,IAAIK,QAAQ,GAAGL,oBAAoB,CAAC;MAAEM,WAAW,EAAEJ,KAAK;MAAEE,MAAM,EAAEA;IAAO,CAAC,CAAC;IAC3E,OAAOG,OAAO,CAACC,OAAO,CAACH,QAAQ,CAAC;EACpC;EACA,OAAON,4BAA4B,CAACI,IAAI,CAAC,CAACM,IAAI,CAAC,UAAUC,OAAO,EAAE;IAC9D,OAAOC,iBAAiB,CAACD,OAAO,EAAER,KAAK,EAAEC,IAAI,EAAEC,MAAM,CAAC;EAC1D,CAAC,CAAC;AACN;AACA,SAASO,iBAAiBA,CAACD,OAAO,EAAER,KAAK,EAAEC,IAAI,EAAEC,MAAM,EAAE;EACrD,OAAON,UAAU,CAAC;IACdc,GAAG,EAAEf,eAAe,CAACD,IAAI,CAACO,IAAI,CAAC,CAACU,MAAM,EAAEH,OAAO,CAAC;IAChDI,EAAE,EAAE,gBAAgB;IACpBC,IAAI,EAAE;EACV,CAAC,CAAC,CAACN,IAAI,CAAC,UAAUO,aAAa,EAAE;IAC7B,IAAI,OAAOC,aAAa,KAAK,UAAU,EAAE;MACrCjB,oBAAoB,GAAGiB,aAAa;MACpC,OAAOC,MAAM,CAACD,aAAa;MAC3B,OAAOjB,oBAAoB,CAAC;QAAEM,WAAW,EAAEJ,KAAK;QAAEE,MAAM,EAAEA;MAAO,CAAC,CAAC;IACvE,CAAC,MACI;MACDe,QAAQ,CAACC,IAAI,CAACC,WAAW,CAACL,aAAa,CAAC;MACxC,MAAM,IAAIM,KAAK,CAAC,iEAAiE,CAAC;IACtF;EACJ,CAAC,CAAC;AACN;AACArB,mBAAmB,CAACE,IAAI,GAAGR,IAAI,CAAC4B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}