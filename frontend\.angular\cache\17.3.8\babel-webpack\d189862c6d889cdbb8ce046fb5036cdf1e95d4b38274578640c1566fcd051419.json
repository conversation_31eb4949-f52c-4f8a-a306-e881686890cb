{"ast": null, "code": "import { take } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/notification.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"./notification/notification.component\";\nfunction NotificationsProfileComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵelement(1, \"app-notification\", 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notification_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"background\", ctx_r1.getNotificationBd(notification_r1.isRead));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"notification\", notification_r1);\n  }\n}\nexport class NotificationsProfileComponent {\n  constructor(notificationService) {\n    this.notificationService = notificationService;\n    this.showDescription = false;\n    this.notifications = [];\n  }\n  ngOnInit() {\n    this.notificationService.get().pipe(take(1)).subscribe(res => {\n      this.notifications = res;\n    });\n  }\n  getNotificationBd(read) {\n    if (read) {\n      return \"white\";\n    } else {\n      return \"#efefef\";\n    }\n  }\n  static #_ = this.ɵfac = function NotificationsProfileComponent_Factory(t) {\n    return new (t || NotificationsProfileComponent)(i0.ɵɵdirectiveInject(i1.NotificationService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NotificationsProfileComponent,\n    selectors: [[\"app-notifications-profile\"]],\n    decls: 1,\n    vars: 1,\n    consts: [[\"class\", \"notification\", 3, \"background\", 4, \"ngFor\", \"ngForOf\"], [1, \"notification\"], [3, \"notification\"]],\n    template: function NotificationsProfileComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NotificationsProfileComponent_div_0_Template, 2, 3, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngForOf\", ctx.notifications);\n      }\n    },\n    dependencies: [i2.NgForOf, i3.NotificationComponent],\n    styles: [\".notification[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  cursor: pointer;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy91c2VyLXByb2ZpbGUvY29tcG9uZW50cy9ub3RpZmljYXRpb25zLXByb2ZpbGUvbm90aWZpY2F0aW9ucy1wcm9maWxlLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksYUFBQTtFQUNBLGVBQUE7QUFDSiIsInNvdXJjZXNDb250ZW50IjpbIi5ub3RpZmljYXRpb24ge1xyXG4gICAgcGFkZGluZzogMTBweDtcclxuICAgIGN1cnNvcjogcG9pbnRlcjtcclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["take", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵstyleProp", "ctx_r1", "getNotificationBd", "notification_r1", "isRead", "ɵɵadvance", "ɵɵproperty", "NotificationsProfileComponent", "constructor", "notificationService", "showDescription", "notifications", "ngOnInit", "get", "pipe", "subscribe", "res", "read", "_", "ɵɵdirectiveInject", "i1", "NotificationService", "_2", "selectors", "decls", "vars", "consts", "template", "NotificationsProfileComponent_Template", "rf", "ctx", "ɵɵtemplate", "NotificationsProfileComponent_div_0_Template"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\user-profile\\components\\notifications-profile\\notifications-profile.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\user-profile\\components\\notifications-profile\\notifications-profile.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { take } from 'rxjs/operators';\r\nimport { NotificationService } from 'src/app/core/services/notification.service';\r\n\r\n@Component({\r\n  selector: 'app-notifications-profile',\r\n  templateUrl: './notifications-profile.component.html',\r\n  styleUrls: ['./notifications-profile.component.scss']\r\n})\r\nexport class NotificationsProfileComponent implements OnInit {\r\n\r\n  showDescription: boolean = false;\r\n  notifications: any[] = []\r\n  constructor(\r\n    private notificationService: NotificationService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.notificationService.get().pipe(take(1)).subscribe((res: any) => {\r\n      this.notifications = res;\r\n    })\r\n  }\r\n\r\n\r\n  getNotificationBd(read: boolean) {\r\n    if (read) {\r\n      return \"white\"\r\n    } else {\r\n      return \"#efefef\"\r\n    }\r\n  }\r\n}\r\n", "<div *ngFor=\"let notification of notifications\" class=\"notification\"\r\n    [style.background]=\"getNotificationBd(notification.isRead)\">\r\n    <app-notification [notification]=\"notification\"></app-notification>\r\n</div>"], "mappings": "AACA,SAASA,IAAI,QAAQ,gBAAgB;;;;;;;ICDrCC,EAAA,CAAAC,cAAA,aACgE;IAC5DD,EAAA,CAAAE,SAAA,0BAAmE;IACvEF,EAAA,CAAAG,YAAA,EAAM;;;;;IAFFH,EAAA,CAAAI,WAAA,eAAAC,MAAA,CAAAC,iBAAA,CAAAC,eAAA,CAAAC,MAAA,EAA2D;IACzCR,EAAA,CAAAS,SAAA,EAA6B;IAA7BT,EAAA,CAAAU,UAAA,iBAAAH,eAAA,CAA6B;;;ADOnD,OAAM,MAAOI,6BAA6B;EAIxCC,YACUC,mBAAwC;IAAxC,KAAAA,mBAAmB,GAAnBA,mBAAmB;IAH7B,KAAAC,eAAe,GAAY,KAAK;IAChC,KAAAC,aAAa,GAAU,EAAE;EAGrB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACH,mBAAmB,CAACI,GAAG,EAAE,CAACC,IAAI,CAACnB,IAAI,CAAC,CAAC,CAAC,CAAC,CAACoB,SAAS,CAAEC,GAAQ,IAAI;MAClE,IAAI,CAACL,aAAa,GAAGK,GAAG;IAC1B,CAAC,CAAC;EACJ;EAGAd,iBAAiBA,CAACe,IAAa;IAC7B,IAAIA,IAAI,EAAE;MACR,OAAO,OAAO;IAChB,CAAC,MAAM;MACL,OAAO,SAAS;IAClB;EACF;EAAC,QAAAC,CAAA,G;qBArBUX,6BAA6B,EAAAX,EAAA,CAAAuB,iBAAA,CAAAC,EAAA,CAAAC,mBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA7Bf,6BAA6B;IAAAgB,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCT1CjC,EAAA,CAAAmC,UAAA,IAAAC,4CAAA,iBACgE;;;QADlCpC,EAAA,CAAAU,UAAA,YAAAwB,GAAA,CAAAnB,aAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}