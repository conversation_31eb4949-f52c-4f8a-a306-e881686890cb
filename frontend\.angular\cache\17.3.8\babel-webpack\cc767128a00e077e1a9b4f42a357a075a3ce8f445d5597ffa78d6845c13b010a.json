{"ast": null, "code": "import { ConfirmationService } from 'primeng/api';\nimport { startWith, switchMap, take } from 'rxjs/operators';\nimport { UserRole } from 'src/app/core/models/user.model';\nimport { SubSink } from 'subsink';\nimport { forkJoin, of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/library.service\";\nimport * as i2 from \"src/app/core/services/classroom.service\";\nimport * as i3 from \"src/app/core/services/general.service\";\nimport * as i4 from \"src/app/core/services/auth.service\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"src/app/core/services/toast.service\";\nimport * as i7 from \"src/app/core/services/layout.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"../../../shared/block-viewer/block-viewer.component\";\nimport * as i10 from \"../../../shared/loader/loader.component\";\nimport * as i11 from \"./components/library-left-sidebar/library-left-sidebar.component\";\nimport * as i12 from \"./components/single-library/single-library.component\";\nconst _c0 = [\"addLibrary\"];\nconst _c1 = [\"leftSide\"];\nconst _c2 = [\"mainWrapper\"];\nconst _c3 = (a0, a1) => ({\n  \"expanded\": a0,\n  \"w-full\": a1\n});\nconst _c4 = a0 => ({\n  \"collapsed\": a0\n});\nconst _c5 = () => [\"View\", \"Copy URL\", \"Download\"];\nconst _c6 = () => [\"View\", \"Copy URL\", \"Move\", \"Unshare\", \"Download\"];\nconst _c7 = () => [\"View\", \"Copy URL\", \"Download\", \"Edit\", \"Move\", \"Share\", \"Delete\"];\nconst _c8 = () => [\"View\", \"Copy URL\", \"Download\", \"Edit\", \"Move\", \"Delete\"];\nfunction LibraryComponent_div_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-library-left-sidebar\", 7);\n    i0.ɵɵlistener(\"myFilesSelected\", function LibraryComponent_div_2_ng_container_2_Template_app_library_left_sidebar_myFilesSelected_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.getLibFolders());\n    })(\"classroomSelected\", function LibraryComponent_div_2_ng_container_2_Template_app_library_left_sidebar_classroomSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onClassRoomSelected($event));\n    })(\"collapsed\", function LibraryComponent_div_2_ng_container_2_Template_app_library_left_sidebar_collapsed_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.oncollapseNotesLeftSideChanged($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const teacherClassrooms_r3 = ctx.ngIf;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"leftSideHeight\", ctx_r1.leftSideHeight)(\"classrooms\", teacherClassrooms_r3)(\"autoSelectFirstClassroom\", ctx_r1.authService.isStudent);\n  }\n}\nfunction LibraryComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6, 0);\n    i0.ɵɵtemplate(2, LibraryComponent_div_2_ng_container_2_Template, 2, 3, \"ng-container\", 5);\n    i0.ɵɵpipe(3, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c4, ctx_r1.isLeftsideCollapsed));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(3, 2, ctx_r1.teacherClassrooms$));\n  }\n}\nfunction LibraryComponent_ng_container_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12);\n    i0.ɵɵelement(2, \"app-loader\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scale\", 1.6);\n  }\n}\nfunction LibraryComponent_ng_container_4_ng_container_2_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 29);\n    i0.ɵɵtext(2, \"Files shared by me \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction LibraryComponent_ng_container_4_ng_container_2_app_single_library_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-single-library\", 30);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"leftSideHeight\", ctx_r1.leftSideHeight / 1.2)(\"allLibrariesWithFiles\", ctx_r1.libsSharedBy)(\"classroomStudents\", ctx_r1.classroomStudents)(\"classroomIdToAddLib\", ctx_r1.classroomId)(\"withClassroom\", true)(\"canShare\", false)(\"canUpload\", true)(\"availableActions\", i0.ɵɵpureFunction0(10, _c6))(\"isInTab\", true)(\"fileListHeight\", ctx_r1.leftSideHeight - 255);\n  }\n}\nfunction LibraryComponent_ng_container_4_ng_container_2_ng_container_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 29);\n    i0.ɵɵtext(2, \"Files shared by the students \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction LibraryComponent_ng_container_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 14)(2, \"div\", 15)(3, \"div\", 16);\n    i0.ɵɵelement(4, \"input\", 17);\n    i0.ɵɵelementStart(5, \"label\", 18);\n    i0.ɵɵelement(6, \"img\", 19);\n    i0.ɵɵelementStart(7, \"div\", 20)(8, \"span\", 21);\n    i0.ɵɵtext(9, \"Teacher Files\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, LibraryComponent_ng_container_4_ng_container_2_ng_container_10_Template, 3, 0, \"ng-container\", 5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 22);\n    i0.ɵɵtemplate(12, LibraryComponent_ng_container_4_ng_container_2_app_single_library_12_Template, 1, 11, \"app-single-library\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 16);\n    i0.ɵɵelement(14, \"input\", 24);\n    i0.ɵɵelementStart(15, \"label\", 25);\n    i0.ɵɵelement(16, \"img\", 26);\n    i0.ɵɵelementStart(17, \"div\", 20)(18, \"span\", 21);\n    i0.ɵɵtext(19, \"Student Files\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, LibraryComponent_ng_container_4_ng_container_2_ng_container_20_Template, 3, 0, \"ng-container\", 5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 27);\n    i0.ɵɵelement(22, \"app-single-library\", 28);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLarge);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLarge);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"leftSideHeight\", ctx_r1.leftSideHeight / 1.2)(\"allLibrariesWithFiles\", ctx_r1.libsSharedWith)(\"classroomStudents\", ctx_r1.classroomStudents)(\"withClassroom\", false)(\"availableActions\", i0.ɵɵpureFunction0(12, _c5))(\"canUpload\", false)(\"canShare\", false)(\"isInTab\", true)(\"fileListHeight\", ctx_r1.leftSideHeight - 255);\n  }\n}\nfunction LibraryComponent_ng_container_4_app_single_library_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-single-library\", 31);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"allLibrariesWithFiles\", ctx_r1.myLibs)(\"availableActions\", i0.ɵɵpureFunction0(5, _c7))(\"extraGradientClass\", \"blue\")(\"leftSideHeight\", !ctx_r1.isMyFiles ? ctx_r1.leftSideHeight / 1.17 : ctx_r1.leftSideHeight / 1.2)(\"fileListHeight\", !ctx_r1.isMyFiles ? ctx_r1.leftSideHeight - 170 : ctx_r1.leftSideHeight - 255);\n  }\n}\nfunction LibraryComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, LibraryComponent_ng_container_4_div_1_Template, 3, 1, \"div\", 8)(2, LibraryComponent_ng_container_4_ng_container_2_Template, 23, 13, \"ng-container\", 5);\n    i0.ɵɵelementStart(3, \"div\", 9);\n    i0.ɵɵtemplate(4, LibraryComponent_ng_container_4_app_single_library_4_Template, 1, 6, \"app-single-library\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isMyFiles && !ctx_r1.isLoading);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading);\n  }\n}\nfunction LibraryComponent_ng_container_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12);\n    i0.ɵɵelement(2, \"app-loader\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scale\", 1.6);\n  }\n}\nfunction LibraryComponent_ng_container_5_ng_container_2_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 38);\n    i0.ɵɵtext(2, \"Files shared by the teacher \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction LibraryComponent_ng_container_5_ng_container_2_app_single_library_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-single-library\", 39);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"allLibrariesWithFiles\", ctx_r1.libsSharedWith)(\"classroomStudents\", ctx_r1.classroomStudents)(\"classroomIdToAddLib\", ctx_r1.classroomId)(\"withClassroom\", false)(\"canShare\", false)(\"canUpload\", false)(\"hasLimitedOptions\", true)(\"availableActions\", i0.ɵɵpureFunction0(11, _c5))(\"isInTab\", true)(\"leftSideHeight\", ctx_r1.leftSideHeight / 1.15)(\"fileListHeight\", ctx_r1.authService.isStudent ? ctx_r1.leftSideHeight - 255 : ctx_r1.leftSideHeight - 160);\n  }\n}\nfunction LibraryComponent_ng_container_5_ng_container_2_ng_container_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 38);\n    i0.ɵɵtext(2, \"Files shared by me \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction LibraryComponent_ng_container_5_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 32)(2, \"div\", 33)(3, \"div\", 16)(4, \"input\", 34);\n    i0.ɵɵlistener(\"change\", function LibraryComponent_ng_container_5_ng_container_2_Template_input_change_4_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTabChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"label\", 18);\n    i0.ɵɵelement(6, \"img\", 19);\n    i0.ɵɵelementStart(7, \"div\", 20)(8, \"span\", 21);\n    i0.ɵɵtext(9, \"Teacher Files\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, LibraryComponent_ng_container_5_ng_container_2_ng_container_10_Template, 3, 0, \"ng-container\", 5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 22);\n    i0.ɵɵtemplate(12, LibraryComponent_ng_container_5_ng_container_2_app_single_library_12_Template, 1, 12, \"app-single-library\", 35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 16)(14, \"input\", 36);\n    i0.ɵɵlistener(\"change\", function LibraryComponent_ng_container_5_ng_container_2_Template_input_change_14_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTabChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"label\", 25);\n    i0.ɵɵelement(16, \"img\", 26);\n    i0.ɵɵelementStart(17, \"div\", 20)(18, \"span\", 21);\n    i0.ɵɵtext(19, \"Student Files\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, LibraryComponent_ng_container_5_ng_container_2_ng_container_20_Template, 3, 0, \"ng-container\", 5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 27);\n    i0.ɵɵelement(22, \"app-single-library\", 37);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLarge);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isLoading);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLarge);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"allLibrariesWithFiles\", ctx_r1.libsSharedBy)(\"classroomIdToAddLib\", ctx_r1.classroomId)(\"classroomStudents\", ctx_r1.classroomStudents)(\"availableActions\", i0.ɵɵpureFunction0(11, _c8))(\"withClassroom\", true)(\"isInTab\", true)(\"fileListHeight\", ctx_r1.authService.isStudent ? ctx_r1.leftSideHeight - 255 : ctx_r1.leftSideHeight - 160)(\"leftSideHeight\", ctx_r1.leftSideHeight / 1.15);\n  }\n}\nfunction LibraryComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, LibraryComponent_ng_container_5_div_1_Template, 3, 1, \"div\", 8)(2, LibraryComponent_ng_container_5_ng_container_2_Template, 23, 12, \"ng-container\", 5);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isMyFiles && !ctx_r1.isLoading);\n  }\n}\nexport class LibraryComponent {\n  constructor(libraryService, classroomService, generalService, authService, confirmationService, toastService, layoutService, cdr) {\n    this.libraryService = libraryService;\n    this.classroomService = classroomService;\n    this.generalService = generalService;\n    this.authService = authService;\n    this.confirmationService = confirmationService;\n    this.toastService = toastService;\n    this.layoutService = layoutService;\n    this.cdr = cdr;\n    this.leftSide = {};\n    this.mainWrapper = {};\n    this.showHeader = true;\n    this.isMyFiles = false;\n    this.showShare = false;\n    this.StudentIdsToSend = [];\n    this.folderPath = [this.libraryService.rootFolder];\n    this.isDisabled = false;\n    this.isDisabledWholeClass = false;\n    this.isMoveRadioCheked = false;\n    this.moveFileId = 0;\n    this.moveFolderId = 0;\n    this.UserRoles = UserRole;\n    this.role = \"\";\n    this.loggedInUser = {};\n    this.inHomework = false;\n    this.task = {};\n    this.classroomIdToAddLib = 0;\n    this.selectedClassroom = {};\n    this.subs = new SubSink();\n    this.showFilters = false;\n    this.showGroupActions = false;\n    this.showAddLibrary = false;\n    this.foldersWithFiles = [];\n    this.allLibrariesWithFiles = [];\n    this.title = \"\";\n    this.currentPathLibraries = [];\n    this.innerFolders = [];\n    this.libraryToAdd = {};\n    this.folderName = \"\";\n    this.showInput = false;\n    this.user = {};\n    this.folders = [];\n    this.inClassroom = true;\n    this.showingMine = true;\n    this.files = [];\n    this.libsSharedWith = [];\n    this.libsSharedBy = [];\n    this.deleteFolderConfirmData = {\n      showDialog: false,\n      message: 'Delete folder?'\n    };\n    this.deleteFileConfirmData = {\n      showDialog: false,\n      message: 'Delete File?'\n    };\n    this.isLoading = true;\n    this.isLarge = false;\n    this.myLibs = [];\n    this.leftSideHeight = 0;\n    this.tab1checked = true;\n    this.tab2checked = false;\n  }\n  ngOnInit() {\n    this.subs.add(this.generalService.deviceKind.pipe().subscribe(res => {\n      if (res && res.w768up) {\n        this.isLarge = true;\n      }\n    }));\n    // this.generalService.slideNativeElements(true, this.addLibrary.nativeElement);\n    this.loggedInUser = this.authService.getLoggedInUser();\n    this.role = this.authService.getUserRole();\n    this.subs.add(this.libraryService.updateFolderListener.subscribe(res => {\n      if (res.folder.folderId > 0) {\n        for (let lib of this.allLibrariesWithFiles) {\n          let found = false;\n          for (let file of lib.libraryFiles) {\n            if (file.fileId == res.libraryFile.fileId) {\n              found = true;\n            }\n          }\n          if (found) {\n            lib.folderId = res.folder.folderId;\n            lib.name = res.folder.name;\n            lib.parent = res.folder.parent;\n          }\n        }\n      }\n    }));\n    this.initUpdateListener();\n    this.initClassroomUpdateListener();\n    this.loadLibFolders();\n    this.getTeacherClassRooms();\n    // IRAKLIS SOURCE\n    this.libraryToAdd = this.libraryService.initializeEmptyLibraryObject();\n    this.user = this.authService.getLoggedInUser();\n    this.role = this.user.role;\n  }\n  ngAfterViewInit() {\n    this.subs.sink = this.generalService.deviceKind.pipe(take(2), switchMap(res => {\n      console.log(res);\n      if (res.is576 || res.is992) {\n        return of(0);\n      } else {\n        return this.layoutService.sideMenuHeight;\n      }\n    }), startWith(0)).subscribe(height => {\n      const heightOffset = this.authService.isStudent ? 120 : 115;\n      if (height !== 0) {\n        this.leftSideHeight = height - heightOffset;\n        if (this.mainWrapper) {\n          this.mainWrapper.nativeElement.style.height = height + 80 + 'px';\n        }\n        if (this.leftSide) {\n          this.leftSide.nativeElement.style.maxHeight = height - 100 + 'px';\n        }\n        this.cdr.detectChanges();\n      } else {\n        this.leftSideHeight = 585;\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  ngOnChanges() {\n    if (this.hasSelectedClassroom) {\n      this.onClassRoomSelected(this.selectedClassroom);\n    }\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n    this.libraryService.setCurrentSelectedClassroomId(0);\n    this.libraryService.setCurrentSelectedClassroom({});\n    this.libraryService.setMyLibrariesListener(true);\n  }\n  get hasSelectedClassroom() {\n    return this.selectedClassroom && Object.keys(this.selectedClassroom).length > 0;\n  }\n  initUpdateListener() {\n    this.subs.add(this.libraryService.updateListener.subscribe(res => {\n      if (res) {\n        this.isLoading = false;\n        this.loadLibFolders();\n      }\n    }));\n  }\n  initClassroomUpdateListener() {\n    this.subs.add(this.libraryService.currentSelectedClassroomId.subscribe(res => {\n      if (res != 0) {\n        console.log(res);\n        this.loadLibraryData(res);\n      }\n    }));\n  }\n  loadLibFolders() {\n    this.subs.add(this.libraryService.getUserCreatedLibFolders().subscribe(res => {\n      this.isLoading = false;\n      this.myLibs = res;\n      this.libraryService.setLibraryUpdatedListener(true);\n    }));\n  }\n  loadLibraryData(classroomId, showLoading = false) {\n    if (showLoading) {\n      this.isLoading = true;\n    }\n    this.inClassroom = classroomId !== 0;\n    this.libsSharedWith = [];\n    this.libsSharedBy = [];\n    const requests = [this.libraryService.getShareWithMeLibFolders(classroomId), this.libraryService.getClassroomFiles(classroomId), this.libraryService.getSharedByMeLibFolders(classroomId)];\n    this.subs.add(forkJoin(requests).subscribe(([sharedWith, classroomFiles, sharedBy]) => {\n      this.isLoading = false;\n      if (this.authService.isTeacher) {\n        this.libsSharedWith = sharedWith;\n        this.libsSharedBy = sharedBy;\n      } else {\n        this.libsSharedWith = sharedWith;\n        this.libsSharedBy = classroomFiles;\n      }\n    }));\n  }\n  onShowAddLibrary() {\n    this.showAddLibrary = !this.showAddLibrary;\n    this.libraryToAdd.folder = this.folderPath[this.folderPath.length - 1];\n    this.generalService.slideNativeElements(this.showAddLibrary, this.addLibrary.nativeElement);\n  }\n  getTeacherClassRooms() {\n    this.teacherClassrooms$ = this.classroomService.getLMSUserClassrooms(this.loggedInUser.id);\n    // this.subs.add(this.classroomService.getLMSUserClassrooms(this.loggedInUser.id).subscribe((response) => {\n    //   this.teacherClassrooms = response;\n    // }));\n    // this.subs.add(this.classroomService.getLMSUserClassrooms(this.loggedInUser.id).subscribe((response) => {\n    //   this.teacherClassrooms = response;\n    // }));\n  }\n  // createLibFolder(){\n  //   this.libraryService.createFolder().subscribe( (response) => {\n  //   });\n  // }\n  deleteLibFolder(folderId) {\n    this.subs.add(this.libraryService.deleteFolder(folderId).subscribe(response => {\n      this.toastService.setShowToastmessage({\n        severity: 'success',\n        summary: '',\n        detail: 'Folder Deleted successfully.'\n      });\n    }));\n  }\n  onClassRoomSelected(classroom) {\n    if (!classroom) {\n      return;\n    }\n    const classroomId = parseInt(classroom.id);\n    this.isLoading = true;\n    this.isMyFiles = true;\n    this.classroomId = classroomId;\n    this.classroomStudents = classroom.classroomStudents;\n    this.libraryService.setCurrentSelectedClassroomId(parseInt(classroom.id));\n    this.libraryService.setCurrentSelectedClassroom(classroom);\n    this.libraryService.setMyLibrariesListener(false);\n    this.loadLibraryData(classroomId);\n  }\n  getFileName(path) {\n    if (path) {\n      let n = path.lastIndexOf(\"/\");\n      if (n === -1) n = path.lastIndexOf(\"\\\\\");\n      let result = path.substring(n + 1);\n      return result;\n    }\n    return path;\n  }\n  // IRAKLIS SOURCE\n  getLibFolders() {\n    this.isMyFiles = false;\n    this.loadLibFolders();\n    // this.removeClass()\n    this.libraryService.setCurrentSelectedClassroomId(0);\n    this.libraryService.setCurrentSelectedClassroom({});\n    this.libraryService.setMyLibrariesListener(true);\n    // this.libraryService.getUserCreatedLibFolders().subscribe( (response) => {\n    //   this.allLibrariesWithFiles = response;\n    //   this.classroomFiles = this.allLibrariesWithFiles;\n    //   this.getFoldersWithFiles();\n    // });\n  }\n  oncollapseNotesLeftSideChanged(event) {\n    console.log(event);\n    this.isLeftsideCollapsed = event;\n  }\n  setGroupActions() {\n    this.showGroupActions = !this.showGroupActions;\n    setTimeout(() => {\n      this.libraryService.setGroupActions(this.showGroupActions);\n    }, 300);\n  }\n  onTabChange(event) {\n    const target = event.target;\n    if (target.id === 'tab-1') {\n      // this.libraryService.setshowUploadAction(false);\n      // this.libraryService.setshowGroupAction(true);\n      this.tab1checked = true;\n      this.tab2checked = false;\n      console.log('Tab 1 is checked');\n    } else if (target.id === 'tab-2') {\n      // this.libraryService.setshowUploadAction(true);\n      // this.libraryService.setshowGroupAction(true);\n      console.log('Tab 2 is checked');\n      this.tab1checked = false;\n      this.tab2checked = true;\n    }\n  }\n  static #_ = this.ɵfac = function LibraryComponent_Factory(t) {\n    return new (t || LibraryComponent)(i0.ɵɵdirectiveInject(i1.LibraryService), i0.ɵɵdirectiveInject(i2.ClassroomService), i0.ɵɵdirectiveInject(i3.GeneralService), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i5.ConfirmationService), i0.ɵɵdirectiveInject(i6.ToastService), i0.ɵɵdirectiveInject(i7.LayoutService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LibraryComponent,\n    selectors: [[\"app-library\"]],\n    viewQuery: function LibraryComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n        i0.ɵɵviewQuery(_c1, 7);\n        i0.ɵɵviewQuery(_c2, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.addLibrary = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.leftSide = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.mainWrapper = _t.first);\n      }\n    },\n    inputs: {\n      showHeader: \"showHeader\",\n      inHomework: \"inHomework\",\n      task: \"task\",\n      classroomIdToAddLib: \"classroomIdToAddLib\",\n      selectedClassroom: \"selectedClassroom\",\n      allLibrariesWithFiles: \"allLibrariesWithFiles\",\n      title: \"title\"\n    },\n    features: [i0.ɵɵProvidersFeature([ConfirmationService]), i0.ɵɵNgOnChangesFeature],\n    decls: 6,\n    vars: 11,\n    consts: [[\"leftSide\", \"\"], [\"header\", \"Library\", \"headerBackgroundImage\", \"/assets/images/library/library-header-bg.png\", \"blockClass\", \"border-radius-bottom-10 mb-6 pb-2\", \"containerClass\", \"bg-white px-2 sm:px-3 py-2\", 3, \"headerClass\", \"headerTextClass\"], [1, \"notes\", \"grid\", \"flex-column\", \"md:flex-row\"], [\"id\", \"notes-left-side\", \"class\", \"notes-left-side lg:mt-2\", 3, \"ngClass\", 4, \"ngIf\"], [\"id\", \"notes-right-side\", 1, \"notes-right-side\", \"mt-2\", \"pl-1\", 3, \"ngClass\"], [4, \"ngIf\"], [\"id\", \"notes-left-side\", 1, \"notes-left-side\", \"lg:mt-2\", 3, \"ngClass\"], [3, \"myFilesSelected\", \"classroomSelected\", \"collapsed\", \"leftSideHeight\", \"classrooms\", \"autoSelectFirstClassroom\"], [\"class\", \"relative h-20rem\", 4, \"ngIf\"], [1, \"lib-content\", \"pb-6\"], [3, \"allLibrariesWithFiles\", \"availableActions\", \"extraGradientClass\", \"leftSideHeight\", \"fileListHeight\", 4, \"ngIf\"], [1, \"relative\", \"h-20rem\"], [1, \"abs-centered\"], [3, \"scale\"], [1, \"mb-2\"], [1, \"tabs\", \"lg:mt-7\", \"mb-5\"], [1, \"tab\"], [\"type\", \"radio\", \"name\", \"css-tabs\", \"id\", \"tab-1\", \"checked\", \"\", 1, \"tab-switch\"], [\"for\", \"tab-1\", 1, \"tab-label\", \"flex\", \"align-items-center\", \"sm:gap-2\", \"line-height-1\"], [\"src\", \"/assets/icons/file_manager.png\", 1, \"w-3rem\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [1, \"font-sm\", \"lg:w-4\"], [\"id\", \"sharedWithAllFiles\", 1, \"tab-content\", \"border-round-xl\"], [\"libraryTitle\", \"Classroom Files\", 3, \"leftSideHeight\", \"allLibrariesWithFiles\", \"classroomStudents\", \"classroomIdToAddLib\", \"withClassroom\", \"canShare\", \"canUpload\", \"availableActions\", \"isInTab\", \"fileListHeight\", 4, \"ngIf\"], [\"type\", \"radio\", \"name\", \"css-tabs\", \"id\", \"tab-2\", 1, \"tab-switch\"], [\"for\", \"tab-2\", 1, \"tab-label\", \"flex\", \"align-items-center\", \"sm:gap-2\", \"line-height-1\"], [\"src\", \"/assets/icons/3d_graduation_cap_7.png\", 1, \"w-3rem\"], [1, \"tab-content\"], [\"libraryTitle\", \"Classroom Files\", 3, \"leftSideHeight\", \"allLibrariesWithFiles\", \"classroomStudents\", \"withClassroom\", \"availableActions\", \"canUpload\", \"canShare\", \"isInTab\", \"fileListHeight\"], [1, \"font-xs\"], [\"libraryTitle\", \"Classroom Files\", 3, \"leftSideHeight\", \"allLibrariesWithFiles\", \"classroomStudents\", \"classroomIdToAddLib\", \"withClassroom\", \"canShare\", \"canUpload\", \"availableActions\", \"isInTab\", \"fileListHeight\"], [3, \"allLibrariesWithFiles\", \"availableActions\", \"extraGradientClass\", \"leftSideHeight\", \"fileListHeight\"], [1, \"lib-content\"], [1, \"tabs\", \"mb-5\"], [\"type\", \"radio\", \"name\", \"css-tabs\", \"id\", \"tab-1\", \"checked\", \"\", 1, \"tab-switch\", 3, \"change\"], [\"libraryTitle\", \"Classroom Files\", 3, \"allLibrariesWithFiles\", \"classroomStudents\", \"classroomIdToAddLib\", \"withClassroom\", \"canShare\", \"canUpload\", \"hasLimitedOptions\", \"availableActions\", \"isInTab\", \"leftSideHeight\", \"fileListHeight\", 4, \"ngIf\"], [\"type\", \"radio\", \"name\", \"css-tabs\", \"id\", \"tab-2\", 1, \"tab-switch\", 3, \"change\"], [3, \"allLibrariesWithFiles\", \"classroomIdToAddLib\", \"classroomStudents\", \"availableActions\", \"withClassroom\", \"isInTab\", \"fileListHeight\", \"leftSideHeight\"], [1, \"font-sm\"], [\"libraryTitle\", \"Classroom Files\", 3, \"allLibrariesWithFiles\", \"classroomStudents\", \"classroomIdToAddLib\", \"withClassroom\", \"canShare\", \"canUpload\", \"hasLimitedOptions\", \"availableActions\", \"isInTab\", \"leftSideHeight\", \"fileListHeight\"]],\n    template: function LibraryComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"app-block-viewer\", 1)(1, \"div\", 2);\n        i0.ɵɵtemplate(2, LibraryComponent_div_2_Template, 4, 6, \"div\", 3);\n        i0.ɵɵelementStart(3, \"div\", 4);\n        i0.ɵɵtemplate(4, LibraryComponent_ng_container_4_Template, 5, 3, \"ng-container\", 5)(5, LibraryComponent_ng_container_5_Template, 3, 2, \"ng-container\", 5);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"headerClass\", \"justify-content-center my-2\")(\"headerTextClass\", \"font-xl font-semibold justify-content-center\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.hasSelectedClassroom);\n        i0.ɵɵadvance();\n        i0.ɵɵstyleProp(\"height\", \"100%\");\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(8, _c3, ctx.isLeftsideCollapsed, ctx.hasSelectedClassroom));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.authService.isTeacher);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.authService.isStudent);\n      }\n    },\n    dependencies: [i8.NgClass, i8.NgIf, i9.BlockViewerComponent, i10.LoaderComponent, i11.LibraryLeftSidebarComponent, i12.SingleLibraryComponent, i8.AsyncPipe],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.notes[_ngcontent-%COMP%] {\\n  background-color: white;\\n  padding: 10px 0;\\n  border-radius: 28px;\\n  font-size: 15px;\\n  position: relative;\\n}\\n.notes[_ngcontent-%COMP%]   .notes-right-side[_ngcontent-%COMP%] {\\n  width: 100%;\\n  box-sizing: border-box;\\n  border-radius: 28px;\\n  transition: width 0.3s ease-in-out; \\n\\n  height: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .notes[_ngcontent-%COMP%]   .notes-right-side[_ngcontent-%COMP%] {\\n    width: 75%;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .notes[_ngcontent-%COMP%]   .notes-right-side.expanded[_ngcontent-%COMP%] {\\n    width: 97%;\\n  }\\n}\\n.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 4px 10px;\\n  border-radius: 12px;\\n  transition: width 0.3s ease-in-out; \\n\\n  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);\\n  box-sizing: border-box;\\n  align-items: center;\\n  height: 100%;\\n  top: 86px;\\n  background-color: white;\\n}\\n@media only screen and (min-width: 992px) {\\n  .notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%] {\\n    width: 25%;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .notes[_ngcontent-%COMP%]   .notes-left-side.collapsed[_ngcontent-%COMP%] {\\n    width: 3%;\\n  }\\n}\\n.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]   .link-main-color[_ngcontent-%COMP%] {\\n  width: 150px;\\n  text-align: center;\\n  border-radius: 50px;\\n}\\n.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]   .classroom-title[_ngcontent-%COMP%] {\\n  padding: 10px;\\n}\\n.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]   .classroom-title.one-line[_ngcontent-%COMP%] {\\n  white-space: nowrap; \\n\\n  overflow: hidden; \\n\\n  text-overflow: ellipsis; \\n\\n}\\n.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]   .seperator[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 1px;\\n  background-color: #d9ddf0;\\n  margin: 3px 0;\\n}\\n\\n.tabs[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 35rem;\\n}\\n@media only screen and (min-width: 768px) {\\n  .tabs[_ngcontent-%COMP%] {\\n    height: 31rem;\\n  }\\n}\\n@media only screen and (min-width: 768px) and (max-height: 768px) {\\n  .tabs[_ngcontent-%COMP%] {\\n    height: 28rem;\\n  }\\n}\\n@media only screen and (max-width: 768px) {\\n  .tabs[_ngcontent-%COMP%] {\\n    height: 40rem;\\n  }\\n}\\n\\n.tabs[_ngcontent-%COMP%]::before, .tabs[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  display: table;\\n}\\n\\n.tabs[_ngcontent-%COMP%]::after {\\n  clear: both;\\n}\\n\\n.tab[_ngcontent-%COMP%] {\\n  float: left;\\n  width: 48%;\\n  margin-right: 1%;\\n  margin-left: 1%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .tab[_ngcontent-%COMP%] {\\n    margin-top: -2px;\\n  }\\n}\\n.tab-switch[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.tab-label[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: block;\\n  line-height: 2.75em;\\n  height: 3.7em;\\n  padding: 0 1.318em;\\n  cursor: pointer;\\n  top: 0;\\n  width: 100%;\\n  transition: all 0.25s;\\n  border: 1px solid #96a3e8;\\n  border-radius: 31.5px;\\n  justify-content: center;\\n}\\n@media only screen and (min-width: 768px) {\\n  .tab-label[_ngcontent-%COMP%] {\\n    height: 4em;\\n    justify-content: start;\\n  }\\n}\\n\\n.tab-label[_ngcontent-%COMP%]:hover {\\n  top: -0.25rem;\\n  transition: top 0.25s;\\n}\\n\\n.tab-content[_ngcontent-%COMP%] {\\n  height: 100%;\\n  position: absolute;\\n  z-index: 1;\\n  top: 4.5em;\\n  left: 0;\\n  color: #2c3e50;\\n  opacity: 0;\\n}\\n@media only screen and (max-width: 768px) {\\n  .tab-content[_ngcontent-%COMP%] {\\n    top: 4.5em;\\n    height: max-content;\\n  }\\n}\\n\\n.tab-switch[_ngcontent-%COMP%]:checked    + .tab-label[_ngcontent-%COMP%] {\\n  background: #fff;\\n  color: #fff;\\n  border-bottom: 0;\\n  border-right: 0.125rem solid #fff;\\n  border: none;\\n  transition: all 0.35s;\\n  z-index: 1;\\n  opacity: 1;\\n  background-image: linear-gradient(180deg, hsl(249, 92%, 74%) 0%, hsl(248, 90%, 73%) 11%, hsl(247, 88%, 72%) 22%, hsl(246, 86%, 71%) 33%, hsl(245, 84%, 70%) 44%, hsl(244, 82%, 68%) 56%, hsl(242, 80%, 67%) 67%, hsl(241, 79%, 66%) 78%, hsl(240, 77%, 65%) 89%, hsl(238, 76%, 63%) 100%);\\n  border-radius: 31.5px;\\n  width: 100%;\\n}\\n\\n.tab-switch[_ngcontent-%COMP%]:checked    + label[_ngcontent-%COMP%]    + .tab-content[_ngcontent-%COMP%] {\\n  z-index: 2;\\n  opacity: 1;\\n  transition: all 0.35s;\\n  width: 100%;\\n}\\n\\n.responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.modal[_ngcontent-%COMP%] {\\n  min-width: 50vw;\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown:not(.p-disabled):hover {\\n  border: 2px solid rgb(228, 185, 84);\\n}\\n\\n  .no-label > .p-button-label {\\n  display: none;\\n}\\n\\ninput[type=file][_ngcontent-%COMP%] {\\n  visibility: hidden;\\n  width: 10px;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%] {\\n  position: relative;\\n  padding: 10px;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%]::-webkit-calendar-picker-indicator {\\n  color: transparent;\\n  background: none;\\n  z-index: 1;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%]:before {\\n  color: transparent;\\n  background: none;\\n  display: block;\\n  font-family: \\\"FontAwesome\\\";\\n  content: \\\"\\\\f073\\\";\\n  \\n\\n  width: 20px;\\n  height: 25px;\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  color: #999;\\n}\\n\\n.image-preview[_ngcontent-%COMP%] {\\n  height: 10rem;\\n  margin: 1rem 0;\\n  border-radius: 50%;\\n  border: 3px solid var(--main-color);\\n}\\n\\n.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 100%;\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-header {\\n  padding: clamp(0.38rem, 0.47vw + 0.28rem, 0.75rem);\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-items .p-dropdown-item {\\n  margin-bottom: clamp(0.38rem, 0.47vw + 0.28rem, 0.75rem);\\n}\\n\\n.country-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: clamp(0.75rem, 0.31vw + 0.69rem, 1rem);\\n  font-size: clamp(0.88rem, 0.16vw + 0.84rem, 1rem);\\n}\\n.country-item[_ngcontent-%COMP%]   .country-name[_ngcontent-%COMP%] {\\n  white-space: pre-line; \\n\\n}\\n.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  \\n\\n\\n\\n\\n\\n  \\n\\n  display: inline-block;\\n  vertical-align: middle;\\n}\\n.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  clip-path: circle();\\n}\\n.country-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 32px;\\n  margin-right: 0.5rem;\\n}\\n.country-item[_ngcontent-%COMP%]   img.smaller[_ngcontent-%COMP%] {\\n  width: 16px;\\n}\\n\\n.info-element[_ngcontent-%COMP%] {\\n  padding: 10px 0;\\n}\\n\\n.responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.note[_ngcontent-%COMP%] {\\n  border-radius: 40px;\\n  border: 1px solid #eaf0f5;\\n  margin-top: 2px;\\n  position: relative;\\n}\\n.note[_ngcontent-%COMP%]::before {\\n  pointer-events: none;\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  border-radius: 30px;\\n  padding: 2px;\\n}\\n.note.isFavNote[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(90deg, hsl(221, 90%, 59%) 0%, hsl(241, 76%, 67%) 29%, hsl(261, 66%, 64%) 53%, hsl(279, 56%, 60%) 68%, hsl(296, 48%, 56%) 77%, hsl(311, 53%, 56%) 84%, hsl(321, 62%, 57%) 89%, hsl(330, 67%, 59%) 93%, hsl(337, 71%, 60%) 97%, hsl(344, 72%, 61%) 100%);\\n}\\n.note.isFavNote[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.note.isFavNote[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .note.isFavNote[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%] {\\n  border: 1px solid white;\\n}\\n.note.isFavNote[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .note.isFavNote[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  color: var(--main-color);\\n}\\n.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .section-arrow[_ngcontent-%COMP%] {\\n  width: 40px;\\n}\\n.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  width: 100%;\\n}\\n@media screen and (max-width: 768px) {\\n  .note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    margin-left: 20px;\\n  }\\n}\\n.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%] {\\n  margin-left: 20px;\\n}\\n@media screen and (max-width: 768px) {\\n  .note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%] {\\n    margin-left: 0px;\\n  }\\n}\\n.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%]   .note-dates[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-top: 10px;\\n}\\n@media screen and (max-width: 768px) {\\n  .note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%]   .note-dates[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n\\n.note-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n@media only screen and (max-width: 768px) {\\n  .note-info[_ngcontent-%COMP%] {\\n    margin-top: 0px;\\n  }\\n}\\n.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n@media only screen and (max-width: 768px) {\\n  .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%] {\\n    justify-content: end;\\n  }\\n}\\n.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-right: 10px;\\n  margin-left: auto;\\n}\\n@media only screen and (max-width: 768px) {\\n  .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n  }\\n}\\n.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-icon[_ngcontent-%COMP%], .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-text[_ngcontent-%COMP%] {\\n  margin-left: 4px;\\n  cursor: pointer;\\n}\\n@media screen and (max-width: 768px) {\\n  .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-icon[_ngcontent-%COMP%], .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-text[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n  }\\n}\\n.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  display: flex;\\n  justify-content: center;\\n}\\n.note-info[_ngcontent-%COMP%]   .note-info-bottom[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: end;\\n  text-align: right;\\n  margin-right: 10px;\\n  font-size: 0.725rem;\\n  color: #2d3b8e;\\n}\\n\\n.note-content[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  transition: height 0.3s ease-out;\\n}\\n.rte[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  border-radius: 20px;\\n  z-index: 0 !important;\\n}\\n\\n.note-text-content[_ngcontent-%COMP%] {\\n  padding: 20px 30px;\\n}\\n\\n.update-button[_ngcontent-%COMP%] {\\n  padding: 5px 30px;\\n  background-color: var(--light-purple);\\n  text-align: center;\\n  border-radius: 10px;\\n  color: white;\\n  margin-top: 15px;\\n  cursor: pointer;\\n}\\n\\n.update-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--main-color);\\n}\\n\\n.section-arrow[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  transition-duration: 0.2s;\\n  transition-property: transform;\\n  cursor: pointer;\\n}\\n\\n.note-burger[_ngcontent-%COMP%], .note-burger-group[_ngcontent-%COMP%] {\\n  position: relative;\\n  border-radius: 50%;\\n  min-width: 36px;\\n  min-height: 36px;\\n  border: 2px solid #6563ec;\\n  display: flex;\\n  align-items: center;\\n  box-sizing: border-box;\\n  justify-content: center;\\n  cursor: pointer;\\n}\\n.note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%] {\\n  width: 4px;\\n  height: 4px;\\n  border-radius: 50%;\\n  background-color: #6563ec;\\n  margin: 2px;\\n}\\n.note-burger[_ngcontent-%COMP%]   .inner-circle-group[_ngcontent-%COMP%], .note-burger-group[_ngcontent-%COMP%]   .inner-circle-group[_ngcontent-%COMP%] {\\n  width: 34px;\\n  height: 34px;\\n  border-radius: 50%;\\n  background-color: transparent;\\n}\\n\\n.note-menu[_ngcontent-%COMP%] {\\n  width: 300px;\\n  position: absolute;\\n  right: 30px;\\n  top: 69px;\\n  background-color: white;\\n  border-radius: 12px;\\n  border: 1px solid var(--main-color);\\n  box-sizing: border-box;\\n  transition-duration: 0.2s;\\n  transition-property: transform;\\n  background-color: white;\\n  z-index: 1;\\n  display: none;\\n  flex-direction: column;\\n  padding: 10px;\\n}\\n@media screen and (max-width: 1124px) {\\n  .note-menu[_ngcontent-%COMP%] {\\n    right: 30px;\\n  }\\n}\\n.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%] {\\n  margin-top: 5px;\\n  display: flex;\\n  align-items: center;\\n}\\n.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-text[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n  cursor: pointer;\\n}\\n.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  display: flex;\\n  justify-content: center;\\n}\\n.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n\\n[_nghost-%COMP%]     .e-toolbar-items {\\n  border-radius: 20px !important;\\n}\\n[_nghost-%COMP%]     .e-rte-toolbar {\\n  border-radius: 20px 20px 0px 0px !important;\\n}\\n[_nghost-%COMP%]     .e-rte-content {\\n  border-radius: 0 0 20px 20px !important;\\n  border-top: 0px !important;\\n}\\n\\n.plain[_ngcontent-%COMP%] {\\n  border-radius: 0px;\\n  margin: -13px 2px 0 2px;\\n  border-top: 0;\\n  border-bottom-right-radius: 12px;\\n  border-bottom-left-radius: 12px;\\n  position: relative;\\n}\\n\\n.filter-white[_ngcontent-%COMP%] {\\n  filter: invert(100%) sepia(0%) saturate(1%) hue-rotate(328deg) brightness(200%) contrast(101%);\\n}\\n\\n.note-info-width[_ngcontent-%COMP%] {\\n  min-width: 10rem;\\n}\\n\\n.note-title-text[_ngcontent-%COMP%] {\\n  font-weight: normal;\\n  font-size: 1rem;\\n  letter-spacing: 0.01em;\\n  text-align: left;\\n  max-width: 17rem;\\n}\\n@media only screen and (max-width: 768px) {\\n  .note-title-text[_ngcontent-%COMP%] {\\n    max-width: 9rem;\\n    font-size: 0.825rem;\\n  }\\n}\\n\\n.accordion[_ngcontent-%COMP%] {\\n  border-radius: 4px;\\n  margin-top: 10px;\\n}\\n\\n.header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 8px;\\n  cursor: pointer;\\n  border-radius: 31px;\\n  box-shadow: 3px 3px 6px 2px rgba(0, 0, 0, 0.16);\\n  z-index: 4;\\n  position: relative;\\n  border: 1px solid transparent;\\n}\\n.header.isPinned[_ngcontent-%COMP%] {\\n  color: white;\\n  background-image: linear-gradient(90deg, hsl(232, 49%, 54%) 0%, hsl(232, 49%, 54%) 13%, hsl(232, 49%, 54%) 25%, hsl(232, 49%, 54%) 37%, hsl(232, 49%, 54%) 50%, hsl(232, 49%, 54%) 63%, hsl(232, 49%, 54%) 75%, hsl(232, 49%, 54%) 87%, hsl(232, 49%, 54%) 100%);\\n}\\n.header.isPinned[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.isPinned[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%] {\\n  border: 2px solid white;\\n}\\n.header.isPinned[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.isPinned[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n.header.isPinned[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%] {\\n  background: transparent;\\n  border: 2px solid white;\\n}\\n.header.isPinnedAndFavourite[_ngcontent-%COMP%] {\\n  color: white;\\n  background-image: linear-gradient(90deg, hsl(231, 46%, 48%) 0%, hsl(231, 46%, 51%) 11%, hsl(232, 49%, 54%) 22%, hsl(232, 53%, 57%) 33%, hsl(232, 57%, 60%) 44%, hsl(233, 62%, 63%) 56%, hsl(233, 67%, 65%) 67%, hsl(233, 74%, 68%) 78%, hsl(233, 82%, 71%) 89%, hsl(233, 92%, 74%) 100%);\\n}\\n.header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%] {\\n  border: 2px solid white;\\n}\\n.header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n.header.isPinnedAndFavourite[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%] {\\n  background: transparent;\\n  border: 2px solid white;\\n}\\n.header.opened[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%] {\\n  border: 1px solid transparent;\\n  color: white;\\n  background-color: #7f8dfa;\\n}\\n.header.opened.activated[_ngcontent-%COMP%], .header.opened2.activated[_ngcontent-%COMP%] {\\n  background-color: #7f8dfa;\\n}\\n.header.opened[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.opened[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%] {\\n  border: 2px solid white;\\n}\\n.header.opened[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.opened[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n.header.opened[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%] {\\n  border: 1px solid white;\\n  background: transparent;\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.content[_ngcontent-%COMP%] {\\n  padding: 10px;\\n}\\n\\n.down-arrow[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease-in-out;\\n}\\n\\n.down-arrow.pi-chevron-up[_ngcontent-%COMP%] {\\n  transform: rotate(360deg);\\n}\\n\\n.circle[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(180deg, hsl(249, 92%, 74%) 0%, hsl(248, 90%, 73%) 11%, hsl(247, 88%, 72%) 22%, hsl(246, 86%, 71%) 33%, hsl(245, 84%, 70%) 44%, hsl(244, 82%, 68%) 56%, hsl(242, 80%, 67%) 67%, hsl(241, 79%, 66%) 78%, hsl(240, 77%, 65%) 89%, hsl(238, 76%, 63%) 100%);\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.circle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 20px;\\n}\\n\\n.rotate[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n  transition: transform 0.3s ease-in-out;\\n}\\n\\n.accordion-content[_ngcontent-%COMP%] {\\n  max-height: 0;\\n  overflow: hidden;\\n}\\n\\n.open[_ngcontent-%COMP%] {\\n  background-color: white;\\n  margin-top: -20px;\\n  border-bottom-left-radius: 30px;\\n  border-bottom-right-radius: 30px;\\n  height: 370px;\\n  max-height: 370px;\\n  padding: 20px 3px 3px 3px;\\n}\\n@media only screen and (max-width: 768px) {\\n  .open[_ngcontent-%COMP%] {\\n    height: 410px;\\n    max-height: 410px;\\n  }\\n}\\n\\n.btn-action[_ngcontent-%COMP%] {\\n  width: 200px;\\n}\\n@media only screen and (max-width: 768px) {\\n  .btn-action[_ngcontent-%COMP%] {\\n    width: auto;\\n  }\\n}\\n\\n[_nghost-%COMP%]     .p-editor-container .p-editor-toolbar.ql-snow {\\n  border: none;\\n  padding: 0 !important;\\n}\\n[_nghost-%COMP%]     .p-editor-container .p-editor-content .ql-editor {\\n  background: transparent;\\n  padding: 4px;\\n}\\n[_nghost-%COMP%]     .p-editor-container .p-editor-content .ql-editor p {\\n  font-size: 1rem;\\n}\\n[_nghost-%COMP%]     .p-editor-container .p-editor-content .ql-editor[contenteditable=true] {\\n  border: 1px solid var(--my-gray);\\n  border-radius: 10px;\\n}\\n[_nghost-%COMP%]     .circle.ql-snow {\\n  border: none;\\n}\\n[_nghost-%COMP%]     .p-editor-container .p-editor-content.ql-snow {\\n  border: none;\\n}\\n[_nghost-%COMP%]     .ql-snow .ql-tooltip {\\n  transform: translate(80%, -50%);\\n}\\n@media only screen and (max-width: 768px) {\\n  [_nghost-%COMP%]     .ql-snow .ql-tooltip {\\n    transform: translate(45%, -50%);\\n  }\\n}\\n\\n  .p-menu .p-menuitem-link {\\n  padding: 0.5rem 1rem !important;\\n}\\n\\n#add-note[_ngcontent-%COMP%] {\\n  background-color: var(--gray);\\n  padding: 30px;\\n  border-radius: 12px;\\n  margin-top: 30px;\\n}\\n\\n.add-note-btn-icon[_ngcontent-%COMP%] {\\n  width: 25px;\\n  height: 25px;\\n}\\n\\n.notes-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.notes-header[_ngcontent-%COMP%]   .notes-header-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  color: var(--main-color);\\n  margin-left: 10px;\\n  font-weight: bold;\\n}\\n.notes-header[_ngcontent-%COMP%]   .notes-header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.notes-header[_ngcontent-%COMP%]   .notes-header-actions[_ngcontent-%COMP%]   div[_ngcontent-%COMP%] {\\n  margin-right: 0.7em;\\n}\\n.notes-header[_ngcontent-%COMP%]   .notes-header-actions[_ngcontent-%COMP%]   .light-purple-circle-button[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n}\\n\\n.note-menu-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-evenly;\\n  border-radius: 40px;\\n}\\n.note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  position: relative;\\n}\\n.note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-text[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n.note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  display: flex;\\n  justify-content: center;\\n}\\n\\n.rte[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  border-radius: 20px;\\n  z-index: 0 !important;\\n}\\n\\n[_nghost-%COMP%]     .e-toolbar-items {\\n  border-radius: 20px !important;\\n}\\n[_nghost-%COMP%]     .e-rte-toolbar {\\n  border-radius: 20px 20px 0px 0px !important;\\n}\\n[_nghost-%COMP%]     .e-rte-content {\\n  border-radius: 0 0 20px 20px !important;\\n  border-top: 0px !important;\\n}\\n[_nghost-%COMP%]     .e-date-icon {\\n  position: absolute !important;\\n  left: 0 !important;\\n}\\n[_nghost-%COMP%]     .e-input {\\n  margin-left: 30px !important;\\n}\\n\\n.notes-popup[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 40px;\\n  width: 200px;\\n  border-radius: 12px;\\n  background-color: white;\\n  color: var(--main-color);\\n  right: 0;\\n  box-shadow: 0px -4px 4px rgba(0, 0, 0, 0.25);\\n  max-height: 400px;\\n  z-index: 10000;\\n}\\n\\n.notes-filters-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: bold;\\n}\\n\\n.notes-filters-filters[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n}\\n.notes-filters-filters[_ngcontent-%COMP%]   .filter-row[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n  display: flex;\\n  align-items: center;\\n}\\n.notes-filters-filters[_ngcontent-%COMP%]   .filter-row[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%] {\\n  margin-left: 15px;\\n}\\n.notes-filters-filters[_ngcontent-%COMP%]   .filter-col[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n}\\n\\n.send-classroom-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: bold;\\n}\\n\\n.send-classroom-user[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  margin-left: 10px;\\n  display: flex;\\n}\\n\\n.send-button[_ngcontent-%COMP%] {\\n  background-color: var(--main-color);\\n  width: 100%;\\n  position: sticky;\\n  bottom: 0;\\n  left: 0;\\n  padding: 8px;\\n  box-sizing: border-box;\\n  color: white;\\n  text-align: center;\\n  border-radius: 12px;\\n  cursor: pointer;\\n}\\n\\n.notes-height[_ngcontent-%COMP%] {\\n  height: calc(100% - 50px);\\n}\\n\\n.btns[_ngcontent-%COMP%]   div[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n[_nghost-%COMP%]     .ngx-pagination {\\n  padding: 0 !important;\\n}\\n\\n.filter-blue[_ngcontent-%COMP%] {\\n  opacity: 0.7;\\n}\\n\\n.add[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n}\\n.add.lg[_ngcontent-%COMP%] {\\n  width: 2.1rem;\\n  height: 2.1rem;\\n}\\n.add.empty[_ngcontent-%COMP%] {\\n  width: 7.1rem;\\n  height: auto;\\n}\\n\\n  .p-tabview .p-tabview-panels {\\n  background-color: transparent;\\n  padding: 0;\\n  justify-content: center;\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n  .p-tabview .p-tabview-nav {\\n  background: transparent;\\n  border: none;\\n}\\n\\n  .p-tabview .p-tabview-nav li .p-tabview-nav-link,   .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {\\n  background: transparent;\\n  border: none;\\n}\\n\\n  .p-tabview .p-tabview-nav .p-tabview-ink-bar {\\n  background-color: #8a7af7;\\n  bottom: 5px;\\n}\\n\\n.responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_collapse {\\n  from {\\n    width: auto;\\n  }\\n  to {\\n    width: 0;\\n  }\\n}\\n.notes[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n  padding: 0;\\n  justify-content: space-evenly;\\n}\\n.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%] {\\n  padding: 4px 10px;\\n  border-radius: 12px;\\n  top: 86px;\\n  transition: width 0.3s ease-in-out; \\n\\n}\\n@media only screen and (min-width: 992px) {\\n  .notes[_ngcontent-%COMP%]   .notes-left-side.collapsed[_ngcontent-%COMP%] {\\n    width: 3%;\\n  }\\n}\\n.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: var(--primary-color);\\n}\\n.notes[_ngcontent-%COMP%]   .notes-right-side[_ngcontent-%COMP%] {\\n  border-radius: 28px;\\n  transition: width 0.3s ease-in-out; \\n\\n  height: 100%;\\n}\\n.notes[_ngcontent-%COMP%]   .notes-right-side.expanded[_ngcontent-%COMP%] {\\n  width: 97%;\\n}\\n.notes[_ngcontent-%COMP%]   .add[_ngcontent-%COMP%] {\\n  width: 25px;\\n  height: 25px;\\n}\\n.notes[_ngcontent-%COMP%]   .folder-nav-buttons[_ngcontent-%COMP%] {\\n  border-right: 3px solid #a7a7a7;\\n  margin-right: 10px;\\n  display: flex;\\n  justify-content: center;\\n  align-items: self-end;\\n  gap: 5px;\\n}\\n.notes[_ngcontent-%COMP%]   .folder-nav-buttons[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 11px;\\n}\\n.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  margin: 8px 8px;\\n}\\n.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .bg-grad-blue[_ngcontent-%COMP%] {\\n  background: linear-gradient(#657aef 0%, #1a266c 100%);\\n}\\n.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .bg-grad-purple[_ngcontent-%COMP%] {\\n  background: linear-gradient(#7f8dfa 0%, #4f29a2 100%);\\n  border: 1px solid #7d89f6;\\n}\\n.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%] {\\n  border-radius: 40px;\\n  display: flex;\\n  background-color: #fff;\\n  align-items: center;\\n  margin-bottom: 10px;\\n}\\n.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   svg.library-file-icon[_ngcontent-%COMP%] {\\n  padding: 8px;\\n  margin-top: 5px;\\n}\\n.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .library-file-details[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n}\\n.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .library-file-details[_ngcontent-%COMP%]   .library-file-meta[_ngcontent-%COMP%] {\\n  margin-top: 5px;\\n  display: flex;\\n}\\n.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .library-file-details[_ngcontent-%COMP%]   .library-file-meta[_ngcontent-%COMP%]   span.file-level[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background: #444070;\\n  padding: 6px;\\n  border-radius: 50px;\\n}\\n.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .library-file-details[_ngcontent-%COMP%]   .library-file-meta[_ngcontent-%COMP%]   span.file-category[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #A44FD0;\\n  padding: 6px;\\n  border-radius: 50px;\\n  margin-left: 6px;\\n}\\n.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%] {\\n  width: 180px;\\n  position: absolute;\\n  left: 92.5%;\\n  top: 69px;\\n  background-color: white;\\n  border-radius: 12px;\\n  border: 1px solid var(--main-color);\\n  box-sizing: border-box;\\n  transition-duration: 0.2s;\\n  transition-property: transform;\\n  background-color: white;\\n  z-index: 1;\\n  display: none;\\n  flex-direction: column;\\n  padding: 10px;\\n}\\n@media screen and (max-width: 1124px) {\\n  .notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%] {\\n    right: 30px;\\n  }\\n}\\n.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]   .lib-file-menu-col[_ngcontent-%COMP%] {\\n  margin-top: 5px;\\n  display: flex;\\n  align-items: center;\\n}\\n.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]   .lib-file-menu-col[_ngcontent-%COMP%]   .lib-file-menu-text[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n  cursor: pointer;\\n}\\n.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]   .lib-file-menu-col[_ngcontent-%COMP%]   .lib-file-menu-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  display: flex;\\n  justify-content: center;\\n}\\n.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n\\n.libs[_ngcontent-%COMP%] {\\n  background: white;\\n  padding: 30px;\\n  margin-bottom: 15px;\\n  border-radius: 28px;\\n}\\n\\n[_nghost-%COMP%]     .tawk-card-primary {\\n  background-color: var(--main-color) !important;\\n}\\n\\n.inner-folders[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n}\\n.inner-folders[_ngcontent-%COMP%]   .inner-folder[_ngcontent-%COMP%] {\\n  margin: 15px;\\n  margin-top: 29px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: var(--main-color);\\n  font-weight: bold;\\n}\\n.inner-folders[_ngcontent-%COMP%]   .inner-folder[_ngcontent-%COMP%]::selection {\\n  background-color: white;\\n}\\n.inner-folders[_ngcontent-%COMP%]   .new-folder[_ngcontent-%COMP%] {\\n  color: var(--main-color);\\n  background-color: white;\\n}\\n.inner-folders[_ngcontent-%COMP%]   .new-folder[_ngcontent-%COMP%]::selection {\\n  color: var(--main-color);\\n  background-color: white;\\n}\\n\\n.folder-path[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin: 15px 0;\\n  font-size: 18px;\\n  align-items: center;\\n}\\n\\n.close-img[_ngcontent-%COMP%] {\\n  width: 20px;\\n  position: absolute;\\n  padding: 5px;\\n  border-radius: 50%;\\n  border: 1px solid black;\\n  left: 90px;\\n  z-index: 10;\\n  background: white;\\n  top: 11px;\\n}\\n\\n.my-folder[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  position: relative;\\n  z-index: 2;\\n}\\n.my-folder[_ngcontent-%COMP%]::before {\\n  position: absolute;\\n  width: 100%;\\n  height: 100%;\\n  content: \\\"\\\";\\n  background: linear-gradient(#002ccf 0%, rgba(201, 210, 255, 0) 100%);\\n  opacity: 0.65;\\n  left: 0;\\n  border-top-left-radius: 10px;\\n  border-top-right-radius: 10px;\\n  z-index: -1;\\n  left: 50%;\\n  transform: translate(-50%, 0%);\\n  top: 0px;\\n}\\n\\n.folder-name-tag[_ngcontent-%COMP%] {\\n  width: auto;\\n  height: 18.68px;\\n  border-radius: 9.34px;\\n  background: #1a266c;\\n  color: #fff;\\n}\\n\\n.wrapper[_ngcontent-%COMP%] {\\n  max-width: 50rem;\\n  width: 100%;\\n  margin: 0 auto;\\n}\\n\\n.tabs[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 35rem;\\n}\\n@media only screen and (min-width: 768px) {\\n  .tabs[_ngcontent-%COMP%] {\\n    height: 31rem;\\n  }\\n}\\n@media only screen and (min-width: 768px) and (max-height: 768px) {\\n  .tabs[_ngcontent-%COMP%] {\\n    height: 28rem;\\n  }\\n}\\n@media only screen and (max-width: 768px) {\\n  .tabs[_ngcontent-%COMP%] {\\n    height: 40rem;\\n  }\\n}\\n\\n.tabs[_ngcontent-%COMP%]::before, .tabs[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  display: table;\\n}\\n\\n.tabs[_ngcontent-%COMP%]::after {\\n  clear: both;\\n}\\n\\n.tab[_ngcontent-%COMP%] {\\n  width: 48%;\\n  margin-right: 1%;\\n  margin-left: 1%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .tab[_ngcontent-%COMP%] {\\n    margin-top: -2px;\\n  }\\n}\\n.tab-switch[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.tab-label[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: block;\\n  line-height: 2.75em;\\n  height: 3.7em;\\n  padding: 0 1.318em;\\n  cursor: pointer;\\n  top: 0;\\n  width: 100%;\\n  transition: all 0.25s;\\n  border: 1px solid #96a3e8;\\n  border-radius: 31.5px;\\n  justify-content: center;\\n}\\n@media only screen and (min-width: 768px) {\\n  .tab-label[_ngcontent-%COMP%] {\\n    height: 4em;\\n    justify-content: start;\\n  }\\n}\\n\\n.tab-label[_ngcontent-%COMP%]:hover {\\n  top: -0.25rem;\\n  transition: top 0.25s;\\n}\\n\\n.tab-content[_ngcontent-%COMP%] {\\n  height: 100%;\\n  position: absolute;\\n  z-index: 1;\\n  top: 4.5em;\\n  left: 0;\\n  color: #2c3e50;\\n  opacity: 0;\\n}\\n@media only screen and (max-width: 768px) {\\n  .tab-content[_ngcontent-%COMP%] {\\n    top: 4.5em;\\n    height: max-content;\\n  }\\n}\\n\\n.tab-switch[_ngcontent-%COMP%]:checked    + .tab-label[_ngcontent-%COMP%] {\\n  background: #fff;\\n  color: #fff;\\n  border-bottom: 0;\\n  border-right: 0.125rem solid #fff;\\n  transition: all 0.35s;\\n  z-index: 1;\\n  opacity: 1;\\n  background-image: linear-gradient(180deg, hsl(249, 92%, 74%) 0%, hsl(248, 90%, 73%) 11%, hsl(247, 88%, 72%) 22%, hsl(246, 86%, 71%) 33%, hsl(245, 84%, 70%) 44%, hsl(244, 82%, 68%) 56%, hsl(242, 80%, 67%) 67%, hsl(241, 79%, 66%) 78%, hsl(240, 77%, 65%) 89%, hsl(238, 76%, 63%) 100%);\\n  border-radius: 31.5px;\\n  width: 100%;\\n}\\n\\n.tab-switch[_ngcontent-%COMP%]:checked    + label[_ngcontent-%COMP%]    + .tab-content[_ngcontent-%COMP%] {\\n  z-index: 2;\\n  opacity: 1;\\n  transition: all 0.35s;\\n  width: 100%;\\n}\\n\\n.horizontal-scroll-menu[_ngcontent-%COMP%] {\\n  overflow: auto;\\n  white-space: nowrap;\\n}\\n\\n.folder-options-icon[_ngcontent-%COMP%] {\\n  right: -15px;\\n  transform: rotate(90deg);\\n}\\n\\n.lib-content[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n@media only screen and (min-width: 576px) {\\n  .lib-content[_ngcontent-%COMP%] {\\n    top: 60px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["ConfirmationService", "startWith", "switchMap", "take", "UserRole", "SubSink", "fork<PERSON><PERSON>n", "of", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "LibraryComponent_div_2_ng_container_2_Template_app_library_left_sidebar_myFilesSelected_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "getLibFolders", "LibraryComponent_div_2_ng_container_2_Template_app_library_left_sidebar_classroomSelected_1_listener", "$event", "onClassRoomSelected", "LibraryComponent_div_2_ng_container_2_Template_app_library_left_sidebar_collapsed_1_listener", "oncollapseNotesLeftSideChanged", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "leftSideHeight", "teacherClassrooms_r3", "authService", "isStudent", "ɵɵtemplate", "LibraryComponent_div_2_ng_container_2_Template", "ɵɵpureFunction1", "_c4", "isLeftsideCollapsed", "ɵɵpipeBind1", "teacherClassrooms$", "ɵɵelement", "ɵɵtext", "libsSharedBy", "classroomStudents", "classroomId", "ɵɵpureFunction0", "_c6", "LibraryComponent_ng_container_4_ng_container_2_ng_container_10_Template", "LibraryComponent_ng_container_4_ng_container_2_app_single_library_12_Template", "LibraryComponent_ng_container_4_ng_container_2_ng_container_20_Template", "is<PERSON>arge", "isLoading", "libsSharedWith", "_c5", "myLibs", "_c7", "isMyFiles", "LibraryComponent_ng_container_4_div_1_Template", "LibraryComponent_ng_container_4_ng_container_2_Template", "LibraryComponent_ng_container_4_app_single_library_4_Template", "LibraryComponent_ng_container_5_ng_container_2_Template_input_change_4_listener", "_r4", "onTabChange", "LibraryComponent_ng_container_5_ng_container_2_ng_container_10_Template", "LibraryComponent_ng_container_5_ng_container_2_app_single_library_12_Template", "LibraryComponent_ng_container_5_ng_container_2_Template_input_change_14_listener", "LibraryComponent_ng_container_5_ng_container_2_ng_container_20_Template", "_c8", "LibraryComponent_ng_container_5_div_1_Template", "LibraryComponent_ng_container_5_ng_container_2_Template", "LibraryComponent", "constructor", "libraryService", "classroomService", "generalService", "confirmationService", "toastService", "layoutService", "cdr", "leftSide", "mainWrapper", "showHeader", "showShare", "StudentIdsToSend", "folderPath", "rootFolder", "isDisabled", "isDisabledWholeClass", "isMoveRadioCheked", "moveFileId", "moveFolderId", "UserRoles", "role", "loggedInUser", "inHomework", "task", "classroomIdToAddLib", "selectedClassroom", "subs", "showFilters", "showGroupActions", "showAddLibrary", "foldersWithFiles", "allLibrariesWithFiles", "title", "currentPathLibraries", "innerFolders", "libraryToAdd", "folderName", "showInput", "user", "folders", "inClassroom", "showingMine", "files", "deleteFolderConfirmData", "showDialog", "message", "deleteFileConfirmData", "tab1checked", "tab2checked", "ngOnInit", "add", "deviceKind", "pipe", "subscribe", "res", "w768up", "getLoggedInUser", "getUserRole", "updateFolderListener", "folder", "folderId", "lib", "found", "file", "libraryFiles", "fileId", "libraryFile", "name", "parent", "initUpdateListener", "initClassroomUpdateListener", "loadLibFolders", "getTeacherClassRooms", "initializeEmptyLibraryObject", "ngAfterViewInit", "sink", "console", "log", "is576", "is992", "sideMenuHeight", "height", "heightOffset", "nativeElement", "style", "maxHeight", "detectChanges", "ngOnChanges", "hasSelectedClassroom", "ngOnDestroy", "unsubscribe", "setCurrentSelectedClassroomId", "setCurrentSelectedClassroom", "setMyLibrariesListener", "Object", "keys", "length", "updateListener", "currentSelectedClassroomId", "loadLibraryData", "getUserCreatedLibFolders", "setLibraryUpdatedListener", "showLoading", "requests", "getShareWithMeLibFolders", "getClassroomFiles", "getSharedByMeLibFolders", "sharedWith", "classroomFiles", "sharedBy", "<PERSON><PERSON><PERSON>er", "onShowAddLibrary", "slideNativeElements", "addLibrary", "getLMSUserClassrooms", "id", "deleteLibFolder", "deleteFolder", "response", "setShowToastmessage", "severity", "summary", "detail", "classroom", "parseInt", "getFileName", "path", "n", "lastIndexOf", "result", "substring", "event", "setGroupActions", "setTimeout", "target", "_", "ɵɵdirectiveInject", "i1", "LibraryService", "i2", "ClassroomService", "i3", "GeneralService", "i4", "AuthService", "i5", "i6", "ToastService", "i7", "LayoutService", "ChangeDetectorRef", "_2", "selectors", "viewQuery", "LibraryComponent_Query", "rf", "ctx", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "LibraryComponent_Template", "LibraryComponent_div_2_Template", "LibraryComponent_ng_container_4_Template", "LibraryComponent_ng_container_5_Template", "ɵɵstyleProp", "ɵɵpureFunction2", "_c3"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\library\\library\\library.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\library\\library\\library.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, ElementRef, Input, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';\r\nimport { ConfirmationService, MenuItem } from 'primeng/api';\r\nimport { filter, startWith, switchMap, take, tap } from 'rxjs/operators';\r\nimport { Classroom, ClassroomType } from 'src/app/core/models/classroom.model';\r\nimport { HomeworkTask } from 'src/app/core/models/homework.model';\r\nimport { Folder, FoldersWithFiles, Library, LibraryFile, UploadFilesToFolderRequest } from 'src/app/core/models/library.model';\r\nimport { User, UserRole } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { LibraryService } from 'src/app/core/services/library.service';\r\nimport { environment } from 'src/environments/environment';\r\nimport { SubSink } from 'subsink';\r\nimport { MessageService } from 'primeng/api';\r\nimport { Observable, forkJoin, of } from 'rxjs';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { LayoutService } from 'src/app/core/services/layout.service';\r\n\r\n@Component({\r\n  selector: 'app-library',\r\n  templateUrl: './library.component.html',\r\n  styleUrls: ['./library.component.scss'],\r\n  providers: [ConfirmationService],\r\n})\r\nexport class LibraryComponent implements OnInit, OnDestroy {\r\n\r\n  @ViewChild('addLibrary', { static: true }) public addLibrary: any;\r\n  @ViewChild('leftSide', { static: true }) public leftSide = {} as ElementRef;\r\n  @ViewChild('mainWrapper', { static: true }) public mainWrapper = {} as ElementRef;\r\n\r\n  @Input() showHeader = true;\r\n  teacherClassrooms: any;\r\n  sharedByMeLibFolders: any;\r\n  sharedWithMeLibFolders: any;\r\n  classroomFiles: any;\r\n  classroomStudents: any;\r\n  isMyFiles = false;\r\n  showShare: boolean = false;\r\n  StudentIdsToSend: string[] = [];\r\n  folderPath: Folder[] = [this.libraryService.rootFolder];\r\n  fileURL: string | undefined;\r\n  classroomId: number | undefined;\r\n  isDisabled = false;\r\n  isDisabledWholeClass = false;\r\n  isMoveRadioCheked = false;\r\n  moveFileId = 0;\r\n  moveFolderId = 0;\r\n  items!: MenuItem[];\r\n  UserRoles = UserRole;\r\n  public role: string = \"\";\r\n  public loggedInUser: User = {} as User;\r\n  @Input() inHomework: boolean = false;\r\n  @Input() task: HomeworkTask = {} as HomeworkTask;\r\n  @Input() classroomIdToAddLib: number = 0;\r\n  @Input() selectedClassroom = {} as Classroom;\r\n\r\n  private subs = new SubSink()\r\n  term: any;\r\n  showFilters: boolean = false;\r\n  showGroupActions: boolean = false;\r\n  showAddLibrary: boolean = false;\r\n  foldersWithFiles: FoldersWithFiles[] = [];\r\n  @Input() allLibrariesWithFiles: FoldersWithFiles[] = [];\r\n  @Input() title: string = \"\";\r\n  currentPathLibraries: Library[] = [];\r\n  innerFolders: Folder[] = [];\r\n  libraryToAdd: Library = {} as Library;\r\n  folderName: string = \"\";\r\n  showInput: boolean = false;\r\n  user: User = {} as User;\r\n  folders: Folder[] = []\r\n  inClassroom: boolean = true;\r\n  showingMine: boolean = true;\r\n  files: LibraryFile[] = [];\r\n  libsSharedWith: FoldersWithFiles[] = [];\r\n  libsSharedBy: FoldersWithFiles[] = [];\r\n  deleteFolderConfirmData = {\r\n    showDialog: false,\r\n    message: 'Delete folder?'\r\n  }\r\n  deleteFileConfirmData = {\r\n    showDialog: false,\r\n    message: 'Delete File?'\r\n  }\r\n  isLoading = true;\r\n  isLarge = false;\r\n  isLeftsideCollapsed: any;\r\n  myLibs: FoldersWithFiles[] = [];\r\n  leftSideHeight = 0;\r\n  tab1checked = true;\r\n  tab2checked = false;\r\n  teacherClassrooms$!: Observable<Classroom[]>;\r\n\r\n  constructor(\r\n    private libraryService: LibraryService,\r\n    private classroomService: ClassroomService,\r\n    private generalService: GeneralService,\r\n    public authService: AuthService,\r\n    private confirmationService: ConfirmationService,\r\n    private toastService: ToastService,\r\n    private layoutService: LayoutService,\r\n    private cdr: ChangeDetectorRef,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n\r\n    this.subs.add(this.generalService.deviceKind.pipe().subscribe(res => {\r\n      if (res && res.w768up) {\r\n        this.isLarge = true;\r\n      }\r\n    }));\r\n\r\n    // this.generalService.slideNativeElements(true, this.addLibrary.nativeElement);\r\n    this.loggedInUser = this.authService.getLoggedInUser();\r\n    this.role = this.authService.getUserRole();\r\n\r\n    this.subs.add(this.libraryService.updateFolderListener.subscribe(res => {\r\n      if (res.folder.folderId! > 0) {\r\n        for (let lib of this.allLibrariesWithFiles) {\r\n          let found: boolean = false;\r\n          for (let file of lib.libraryFiles) {\r\n            if (file.fileId == res.libraryFile.fileId) {\r\n              found = true;\r\n            }\r\n          }\r\n          if (found) {\r\n            lib.folderId = res.folder.folderId!\r\n            lib.name = res.folder.name\r\n            lib.parent = res.folder.parent\r\n          }\r\n        }\r\n      }\r\n    }));\r\n\r\n    this.initUpdateListener();\r\n    this.initClassroomUpdateListener();\r\n    this.loadLibFolders();\r\n    this.getTeacherClassRooms();\r\n\r\n    // IRAKLIS SOURCE\r\n    this.libraryToAdd = this.libraryService.initializeEmptyLibraryObject()\r\n    this.user = this.authService.getLoggedInUser();\r\n    this.role = this.user.role!\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    this.subs.sink = this.generalService.deviceKind.pipe(\r\n      take(2),\r\n      switchMap(res => {\r\n        console.log(res);\r\n        if (res.is576 || res.is992) {\r\n          return of(0);\r\n        } else {\r\n          return this.layoutService.sideMenuHeight;\r\n        }\r\n      }),\r\n      startWith(0),\r\n    ).subscribe(height => {\r\n      const heightOffset = this.authService.isStudent ? 120 : 115;\r\n      if (height !== 0) {\r\n        this.leftSideHeight = height - heightOffset;\r\n        if (this.mainWrapper) {\r\n          this.mainWrapper.nativeElement.style.height = (height + 80 + 'px');\r\n        }\r\n        if (this.leftSide) {\r\n          this.leftSide.nativeElement.style.maxHeight = (height - 100 + 'px');\r\n        }\r\n        this.cdr.detectChanges();\r\n      } else {\r\n        this.leftSideHeight = 585;\r\n        this.cdr.detectChanges();\r\n      }\r\n    });\r\n  }\r\n\r\n  ngOnChanges() {\r\n    if (this.hasSelectedClassroom) {\r\n      this.onClassRoomSelected(this.selectedClassroom);\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subs.unsubscribe();\r\n    this.libraryService.setCurrentSelectedClassroomId(0)\r\n    this.libraryService.setCurrentSelectedClassroom({} as Classroom);\r\n    this.libraryService.setMyLibrariesListener(true)\r\n  }\r\n\r\n  get hasSelectedClassroom() {\r\n    return (this.selectedClassroom && Object.keys(this.selectedClassroom).length > 0);\r\n  }\r\n\r\n  initUpdateListener() {\r\n    this.subs.add(this.libraryService.updateListener.subscribe(res => {\r\n      if (res) {\r\n        this.isLoading = false;\r\n        this.loadLibFolders();\r\n      }\r\n    }));\r\n  }\r\n\r\n  initClassroomUpdateListener() {\r\n    this.subs.add(this.libraryService.currentSelectedClassroomId.subscribe(res => {\r\n      if (res != 0) {\r\n        console.log(res);\r\n        this.loadLibraryData(res);\r\n      }\r\n    }));\r\n  }\r\n\r\n  loadLibFolders() {\r\n    this.subs.add(this.libraryService.getUserCreatedLibFolders().subscribe(res => {\r\n      this.isLoading = false;\r\n      this.myLibs = res;\r\n      this.libraryService.setLibraryUpdatedListener(true);\r\n    }));\r\n  }\r\n\r\n  loadLibraryData(classroomId: number, showLoading = false) {\r\n    if (showLoading) {\r\n      this.isLoading = true;\r\n    }\r\n    this.inClassroom = classroomId !== 0;\r\n    this.libsSharedWith = [];\r\n    this.libsSharedBy = [];\r\n    const requests = [\r\n      this.libraryService.getShareWithMeLibFolders(classroomId),\r\n      this.libraryService.getClassroomFiles(classroomId),\r\n      this.libraryService.getSharedByMeLibFolders(classroomId)\r\n    ];\r\n    this.subs.add(forkJoin(requests).subscribe(([sharedWith, classroomFiles, sharedBy]) => {\r\n      this.isLoading = false;\r\n      if (this.authService.isTeacher) {\r\n        this.libsSharedWith = sharedWith;\r\n        this.libsSharedBy = sharedBy;\r\n      } else {\r\n        this.libsSharedWith = sharedWith;\r\n        this.libsSharedBy = classroomFiles;\r\n      }\r\n    }));\r\n  }\r\n\r\n  onShowAddLibrary() {\r\n    this.showAddLibrary = !this.showAddLibrary\r\n    this.libraryToAdd.folder = this.folderPath[this.folderPath.length - 1];\r\n    this.generalService.slideNativeElements(this.showAddLibrary, this.addLibrary.nativeElement);\r\n  }\r\n\r\n\r\n  getTeacherClassRooms() {\r\n    this.teacherClassrooms$ = this.classroomService.getLMSUserClassrooms(this.loggedInUser.id);\r\n    // this.subs.add(this.classroomService.getLMSUserClassrooms(this.loggedInUser.id).subscribe((response) => {\r\n    //   this.teacherClassrooms = response;\r\n    // }));\r\n    // this.subs.add(this.classroomService.getLMSUserClassrooms(this.loggedInUser.id).subscribe((response) => {\r\n    //   this.teacherClassrooms = response;\r\n    // }));\r\n  }\r\n\r\n\r\n  // createLibFolder(){\r\n  //   this.libraryService.createFolder().subscribe( (response) => {\r\n  //   });\r\n  // }\r\n\r\n  deleteLibFolder(folderId: number) {\r\n    this.subs.add(this.libraryService.deleteFolder(folderId).subscribe((response) => {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'success',\r\n        summary: '',\r\n        detail: 'Folder Deleted successfully.'\r\n      });\r\n    }));\r\n  }\r\n\r\n  onClassRoomSelected(classroom: Classroom) {\r\n    if (!classroom) {\r\n      return;\r\n    }\r\n    const classroomId = parseInt(classroom.id);\r\n    this.isLoading = true;\r\n    this.isMyFiles = true;\r\n    this.classroomId = classroomId;\r\n    this.classroomStudents = classroom.classroomStudents;\r\n\r\n    this.libraryService.setCurrentSelectedClassroomId(parseInt(classroom.id));\r\n    this.libraryService.setCurrentSelectedClassroom(classroom);\r\n    this.libraryService.setMyLibrariesListener(false)\r\n    this.loadLibraryData(classroomId);\r\n  }\r\n\r\n  getFileName(path: string) {\r\n    if (path) {\r\n      let n = path.lastIndexOf(\"/\");\r\n      if (n === -1)\r\n        n = path.lastIndexOf(\"\\\\\");\r\n      let result = path.substring(n + 1);\r\n      return result;\r\n    }\r\n    return path\r\n  }\r\n\r\n\r\n  // IRAKLIS SOURCE\r\n\r\n\r\n  getLibFolders() {\r\n    this.isMyFiles = false;\r\n    this.loadLibFolders();\r\n    // this.removeClass()\r\n    this.libraryService.setCurrentSelectedClassroomId(0)\r\n    this.libraryService.setCurrentSelectedClassroom({} as Classroom);\r\n    this.libraryService.setMyLibrariesListener(true)\r\n    // this.libraryService.getUserCreatedLibFolders().subscribe( (response) => {\r\n    //   this.allLibrariesWithFiles = response;\r\n    //   this.classroomFiles = this.allLibrariesWithFiles;\r\n    //   this.getFoldersWithFiles();\r\n    // });\r\n  }\r\n\r\n  oncollapseNotesLeftSideChanged(event: any) {\r\n    console.log(event);\r\n    this.isLeftsideCollapsed = event;\r\n  }\r\n\r\n  setGroupActions() {\r\n    this.showGroupActions = !this.showGroupActions;\r\n    setTimeout(() => {\r\n      this.libraryService.setGroupActions(this.showGroupActions);\r\n    }, 300);\r\n  }\r\n\r\n  onTabChange(event: Event) {\r\n    const target = event.target as HTMLInputElement;\r\n    if (target.id === 'tab-1') {\r\n      // this.libraryService.setshowUploadAction(false);\r\n      // this.libraryService.setshowGroupAction(true);\r\n      \r\n      this.tab1checked = true;\r\n      this.tab2checked = false;\r\n      console.log('Tab 1 is checked');\r\n    } else if (target.id === 'tab-2') {\r\n      // this.libraryService.setshowUploadAction(true);\r\n      // this.libraryService.setshowGroupAction(true);\r\n      console.log('Tab 2 is checked');\r\n      this.tab1checked = false;\r\n      this.tab2checked = true;\r\n    }\r\n  }\r\n\r\n}\r\n", "\r\n<app-block-viewer header=\"Library\" headerBackgroundImage=\"/assets/images/library/library-header-bg.png\"\r\n    blockClass=\"border-radius-bottom-10 mb-6 pb-2\" containerClass=\"bg-white px-2 sm:px-3 py-2\"\r\n    [headerClass]=\"'justify-content-center my-2'\" [headerTextClass]=\"'font-xl font-semibold justify-content-center'\">\r\n\r\n    <div class=\"notes grid flex-column md:flex-row\" >\r\n        <div #leftSide id=\"notes-left-side\" *ngIf=\"!hasSelectedClassroom\" class=\"notes-left-side lg:mt-2\" [ngClass]=\"{'collapsed': isLeftsideCollapsed}\">\r\n            <ng-container *ngIf=\"(teacherClassrooms$ | async) as teacherClassrooms\">\r\n                <app-library-left-sidebar [leftSideHeight]=\"leftSideHeight\" [classrooms]=\"teacherClassrooms\" [autoSelectFirstClassroom]=\"authService.isStudent\" \r\n                  (myFilesSelected)=\"getLibFolders()\"\r\n                  (classroomSelected)=\"onClassRoomSelected($event)\" \r\n                  (collapsed)=\"oncollapseNotesLeftSideChanged($event)\">\r\n                </app-library-left-sidebar>\r\n              </ng-container>\r\n        </div>\r\n        <div id=\"notes-right-side\" [style.height]=\"'100%'\" class=\"notes-right-side mt-2 pl-1\" [ngClass]=\"{'expanded': isLeftsideCollapsed, 'w-full': hasSelectedClassroom}\">\r\n\r\n            <ng-container *ngIf=\"authService.isTeacher\">\r\n                \r\n                <div class=\"relative h-20rem\" *ngIf=\"isLoading\">\r\n                    <div class=\"abs-centered\">\r\n                        <app-loader [scale]=\"1.6\"></app-loader>\r\n                    </div>\r\n                </div>\r\n                <ng-container *ngIf=\"isMyFiles && !isLoading\">\r\n                    <!-- <div class=\"notes-header mt-4 mb-2\">\r\n                        <div class=\"notes-header-title\">\r\n                            Classroom Files\r\n                        </div>\r\n                    </div> -->\r\n                    <div class=\"mb-2\">\r\n                        <div class=\"tabs lg:mt-7 mb-5\">\r\n                            <div class=\"tab\">\r\n                                <input type=\"radio\" name=\"css-tabs\" id=\"tab-1\" checked class=\"tab-switch\">\r\n                                <label for=\"tab-1\"\r\n                                    class=\"tab-label flex align-items-center sm:gap-2 line-height-1\">\r\n                                    <img src=\"/assets/icons/file_manager.png\" class=\"w-3rem\">\r\n                                    <div class=\"flex align-items-center gap-2\"><span class=\"font-sm lg:w-4\">Teacher Files</span> \r\n                                        <ng-container *ngIf=\"isLarge\">\r\n                                            <span class=\"font-xs\">Files shared \r\n                                            by me </span>\r\n                                        </ng-container>\r\n                                    </div> \r\n                                        \r\n                                    \r\n                                </label>\r\n                                <div class=\"tab-content border-round-xl\" id=\"sharedWithAllFiles\">\r\n                                    <app-single-library *ngIf=\"!isLoading\" [leftSideHeight]=\"leftSideHeight / 1.20\" [allLibrariesWithFiles]=\"libsSharedBy\"\r\n                                        [classroomStudents]=\"classroomStudents\" [classroomIdToAddLib]=\"classroomId\"\r\n                                        [withClassroom]=\"true\" [canShare]=\"false\" \r\n                                        [canUpload]=\"true\"\r\n                                        [availableActions]=\"[ 'View', 'Copy URL', 'Move', 'Unshare', 'Download']\"\r\n                                        [isInTab]=\"true\"\r\n                                        [fileListHeight]=\"leftSideHeight - 255\"\r\n                                        libraryTitle=\"Classroom Files\">\r\n                                    </app-single-library>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"tab\">\r\n                                <input type=\"radio\" name=\"css-tabs\" id=\"tab-2\" class=\"tab-switch\">\r\n                                <label for=\"tab-2\"\r\n                                    class=\"tab-label flex align-items-center sm:gap-2 line-height-1\">\r\n                                    <img src=\"/assets/icons/3d_graduation_cap_7.png\" class=\"w-3rem\">\r\n                                    <div class=\"flex align-items-center gap-2\"><span class=\"font-sm lg:w-4\">Student Files</span> \r\n                                        <ng-container *ngIf=\"isLarge\">\r\n                                            <span class=\"font-xs\">Files shared \r\n                                            by the students </span>\r\n                                        </ng-container>\r\n                                    </div> \r\n                                </label>\r\n                                <div class=\"tab-content\">\r\n                                    <app-single-library [leftSideHeight]=\"leftSideHeight / 1.20\" [allLibrariesWithFiles]=\"libsSharedWith\"\r\n                                        [classroomStudents]=\"classroomStudents\" [withClassroom]=\"false\"\r\n                                        [availableActions]=\"['View', 'Copy URL', 'Download']\"\r\n                                        [canUpload]=\"false\" [canShare]=\"false\"\r\n                                        [isInTab]=\"true\"\r\n                                        [fileListHeight]=\"leftSideHeight - 255\"\r\n                                        libraryTitle=\"Classroom Files\"></app-single-library>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </ng-container>\r\n                <div class=\"lib-content pb-6\">\r\n                <app-single-library *ngIf=\"!isLoading\" [allLibrariesWithFiles]=\"myLibs\" \r\n                [availableActions]=\"['View', 'Copy URL', 'Download', 'Edit', 'Move', 'Share', 'Delete']\"\r\n                [extraGradientClass]=\"'blue'\"\r\n                [leftSideHeight]=\"!isMyFiles ? leftSideHeight / 1.17 : leftSideHeight / 1.20\"\r\n                [fileListHeight]=\"!isMyFiles ? (leftSideHeight - 170) : (leftSideHeight - 255)\"\r\n                ></app-single-library>\r\n                </div>\r\n            </ng-container>\r\n\r\n\r\n            <ng-container *ngIf=\"authService.isStudent\">\r\n                <div class=\"relative h-20rem\" *ngIf=\"isLoading\">\r\n                    <div class=\"abs-centered\">\r\n                        <app-loader [scale]=\"1.6\"></app-loader>\r\n                    </div>\r\n                </div>\r\n                <ng-container *ngIf=\"isMyFiles && !isLoading\">\r\n                    <!-- <div class=\"notes-header mt-4 mb-2\">\r\n                        <div class=\"notes-header-title font-2xl opaque-box-rtl col-4\">\r\n                            Classroom Files\r\n                        </div>\r\n                        <app-single-library-button-actions \r\n                        [canShare]=\"canShare\"\r\n                        [canUpload]=\"canUpload\"\r\n                        ></app-single-library-button-actions>\r\n                    </div> -->\r\n                    <div class=\"lib-content\">\r\n                        <div class=\"tabs mb-5\">\r\n                            <div class=\"tab\">\r\n                                <input type=\"radio\" name=\"css-tabs\" id=\"tab-1\" (change)=\"onTabChange($event)\" checked class=\"tab-switch\">\r\n                                <label for=\"tab-1\"\r\n                                    class=\"tab-label flex align-items-center sm:gap-2 line-height-1\">\r\n                                    <img src=\"/assets/icons/file_manager.png\" class=\"w-3rem\"> \r\n                                    <div class=\"flex align-items-center gap-2\"><span class=\"font-sm lg:w-4\">Teacher Files</span> \r\n                                        <ng-container *ngIf=\"isLarge\">\r\n                                            <span class=\"font-sm\">Files shared \r\n                                                by the teacher </span>\r\n                                        </ng-container>\r\n                                    </div> \r\n                                </label>\r\n                                <div class=\"tab-content border-round-xl\" id=\"sharedWithAllFiles\">\r\n\r\n                                    <app-single-library *ngIf=\"!isLoading\" [allLibrariesWithFiles]=\"libsSharedWith\"\r\n                                        [classroomStudents]=\"classroomStudents\" [classroomIdToAddLib]=\"classroomId\"\r\n                                        [withClassroom]=\"false\" [canShare]=\"false\"\r\n                                        [canUpload]=\"false\" [hasLimitedOptions]=\"true\"\r\n                                        [availableActions]=\"['View', 'Copy URL', 'Download']\"\r\n                                        [isInTab]=\"true\"\r\n                                        [leftSideHeight]=\"leftSideHeight / 1.15\"\r\n                                        [fileListHeight]=\"authService.isStudent ? leftSideHeight - 255 : leftSideHeight - 160\"\r\n                                        libraryTitle=\"Classroom Files\"></app-single-library>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"tab\">\r\n                                <input type=\"radio\" name=\"css-tabs\" (change)=\"onTabChange($event)\" id=\"tab-2\" class=\"tab-switch\">\r\n                                <label for=\"tab-2\"\r\n                                    class=\"tab-label flex align-items-center sm:gap-2 line-height-1\">\r\n                                    <img src=\"/assets/icons/3d_graduation_cap_7.png\" class=\"w-3rem\">\r\n                                    <div class=\"flex align-items-center gap-2\"><span class=\"font-sm lg:w-4\">Student Files</span> \r\n                                        <ng-container *ngIf=\"isLarge\">\r\n                                            <span class=\"font-sm\">Files shared \r\n                                            by me </span>\r\n                                        </ng-container>\r\n                                    </div> \r\n                                </label>\r\n                                <div class=\"tab-content\">\r\n                                    <app-single-library [allLibrariesWithFiles]=\"libsSharedBy\"\r\n                                        [classroomIdToAddLib]=\"classroomId\" [classroomStudents]=\"classroomStudents\"\r\n                                        [availableActions]=\"['View', 'Copy URL', 'Download', 'Edit', 'Move', 'Delete']\"\r\n                                        [withClassroom]=\"true\"\r\n                                        [isInTab]=\"true\"\r\n                                        [fileListHeight]=\"authService.isStudent ? leftSideHeight - 255 : leftSideHeight - 160\"\r\n                                        [leftSideHeight]=\"leftSideHeight / 1.15\"></app-single-library>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </ng-container>\r\n            </ng-container>\r\n\r\n        </div>\r\n    </div>\r\n</app-block-viewer>"], "mappings": "AACA,SAASA,mBAAmB,QAAkB,aAAa;AAC3D,SAAiBC,SAAS,EAAEC,SAAS,EAAEC,IAAI,QAAa,gBAAgB;AAIxE,SAAeC,QAAQ,QAAQ,gCAAgC;AAM/D,SAASC,OAAO,QAAQ,SAAS;AAEjC,SAAqBC,QAAQ,EAAEC,EAAE,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICPnCC,EAAA,CAAAC,uBAAA,GAAwE;IACpED,EAAA,CAAAE,cAAA,kCAGuD;IAArDF,EAFA,CAAAG,UAAA,6BAAAC,mGAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAmBF,MAAA,CAAAG,aAAA,EAAe;IAAA,EAAC,+BAAAC,qGAAAC,MAAA;MAAAZ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CACdF,MAAA,CAAAM,mBAAA,CAAAD,MAAA,CAA2B;IAAA,EAAC,uBAAAE,6FAAAF,MAAA;MAAAZ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CACpCF,MAAA,CAAAQ,8BAAA,CAAAH,MAAA,CAAsC;IAAA,EAAC;IACtDZ,EAAA,CAAAgB,YAAA,EAA2B;;;;;;IAJDhB,EAAA,CAAAiB,SAAA,EAAiC;IAAkCjB,EAAnE,CAAAkB,UAAA,mBAAAX,MAAA,CAAAY,cAAA,CAAiC,eAAAC,oBAAA,CAAiC,6BAAAb,MAAA,CAAAc,WAAA,CAAAC,SAAA,CAAmD;;;;;IAFvJtB,EAAA,CAAAE,cAAA,gBAAiJ;IAC7IF,EAAA,CAAAuB,UAAA,IAAAC,8CAAA,0BAAwE;;IAO5ExB,EAAA,CAAAgB,YAAA,EAAM;;;;IAR4FhB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAyB,eAAA,IAAAC,GAAA,EAAAnB,MAAA,CAAAoB,mBAAA,EAA8C;IAC7H3B,EAAA,CAAAiB,SAAA,GAAmC;IAAnCjB,EAAA,CAAAkB,UAAA,SAAAlB,EAAA,CAAA4B,WAAA,OAAArB,MAAA,CAAAsB,kBAAA,EAAmC;;;;;IAa1C7B,EADJ,CAAAE,cAAA,cAAgD,cAClB;IACtBF,EAAA,CAAA8B,SAAA,qBAAuC;IAE/C9B,EADI,CAAAgB,YAAA,EAAM,EACJ;;;IAFchB,EAAA,CAAAiB,SAAA,GAAa;IAAbjB,EAAA,CAAAkB,UAAA,cAAa;;;;;IAiBTlB,EAAA,CAAAC,uBAAA,GAA8B;IAC1BD,EAAA,CAAAE,cAAA,eAAsB;IAAAF,EAAA,CAAA+B,MAAA,0BAChB;IAAA/B,EAAA,CAAAgB,YAAA,EAAO;;;;;;IAOrBhB,EAAA,CAAA8B,SAAA,6BAQqB;;;;IAFjB9B,EANmC,CAAAkB,UAAA,mBAAAX,MAAA,CAAAY,cAAA,OAAwC,0BAAAZ,MAAA,CAAAyB,YAAA,CAAuC,sBAAAzB,MAAA,CAAA0B,iBAAA,CAC3E,wBAAA1B,MAAA,CAAA2B,WAAA,CAAoC,uBACrD,mBAAmB,mBACvB,qBAAAlC,EAAA,CAAAmC,eAAA,KAAAC,GAAA,EACuD,iBACzD,mBAAA7B,MAAA,CAAAY,cAAA,OACuB;;;;;IAWvCnB,EAAA,CAAAC,uBAAA,GAA8B;IAC1BD,EAAA,CAAAE,cAAA,eAAsB;IAAAF,EAAA,CAAA+B,MAAA,oCACN;IAAA/B,EAAA,CAAAgB,YAAA,EAAO;;;;;;IA1CnDhB,EAAA,CAAAC,uBAAA,GAA8C;IAQlCD,EAFR,CAAAE,cAAA,cAAkB,cACiB,cACV;IACbF,EAAA,CAAA8B,SAAA,gBAA0E;IAC1E9B,EAAA,CAAAE,cAAA,gBACqE;IACjEF,EAAA,CAAA8B,SAAA,cAAyD;IACd9B,EAA3C,CAAAE,cAAA,cAA2C,eAA6B;IAAAF,EAAA,CAAA+B,MAAA,oBAAa;IAAA/B,EAAA,CAAAgB,YAAA,EAAO;IACxFhB,EAAA,CAAAuB,UAAA,KAAAc,uEAAA,0BAA8B;IAOtCrC,EAHI,CAAAgB,YAAA,EAAM,EAGF;IACRhB,EAAA,CAAAE,cAAA,eAAiE;IAC7DF,EAAA,CAAAuB,UAAA,KAAAe,6EAAA,kCAOmC;IAG3CtC,EADI,CAAAgB,YAAA,EAAM,EACJ;IACNhB,EAAA,CAAAE,cAAA,eAAiB;IACbF,EAAA,CAAA8B,SAAA,iBAAkE;IAClE9B,EAAA,CAAAE,cAAA,iBACqE;IACjEF,EAAA,CAAA8B,SAAA,eAAgE;IACrB9B,EAA3C,CAAAE,cAAA,eAA2C,gBAA6B;IAAAF,EAAA,CAAA+B,MAAA,qBAAa;IAAA/B,EAAA,CAAAgB,YAAA,EAAO;IACxFhB,EAAA,CAAAuB,UAAA,KAAAgB,uEAAA,0BAA8B;IAKtCvC,EADI,CAAAgB,YAAA,EAAM,EACF;IACRhB,EAAA,CAAAE,cAAA,eAAyB;IACrBF,EAAA,CAAA8B,SAAA,8BAMwD;IAIxE9B,EAHY,CAAAgB,YAAA,EAAM,EACJ,EACJ,EACJ;;;;;IA3C6BhB,EAAA,CAAAiB,SAAA,IAAa;IAAbjB,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAiC,OAAA,CAAa;IASXxC,EAAA,CAAAiB,SAAA,GAAgB;IAAhBjB,EAAA,CAAAkB,UAAA,UAAAX,MAAA,CAAAkC,SAAA,CAAgB;IAiBlBzC,EAAA,CAAAiB,SAAA,GAAa;IAAbjB,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAiC,OAAA,CAAa;IAOZxC,EAAA,CAAAiB,SAAA,GAAwC;IAKxDjB,EALgB,CAAAkB,UAAA,mBAAAX,MAAA,CAAAY,cAAA,OAAwC,0BAAAZ,MAAA,CAAAmC,cAAA,CAAyC,sBAAAnC,MAAA,CAAA0B,iBAAA,CAC1D,wBAAwB,qBAAAjC,EAAA,CAAAmC,eAAA,KAAAQ,GAAA,EACV,oBAClC,mBAAmB,iBACtB,mBAAApC,MAAA,CAAAY,cAAA,OACuB;;;;;IAQ/DnB,EAAA,CAAA8B,SAAA,6BAKsB;;;;IADtB9B,EAJuC,CAAAkB,UAAA,0BAAAX,MAAA,CAAAqC,MAAA,CAAgC,qBAAA5C,EAAA,CAAAmC,eAAA,IAAAU,GAAA,EACiB,8BAC3D,oBAAAtC,MAAA,CAAAuC,SAAA,GAAAvC,MAAA,CAAAY,cAAA,UAAAZ,MAAA,CAAAY,cAAA,OACgD,oBAAAZ,MAAA,CAAAuC,SAAA,GAAAvC,MAAA,CAAAY,cAAA,SAAAZ,MAAA,CAAAY,cAAA,OACE;;;;;IAvEnFnB,EAAA,CAAAC,uBAAA,GAA4C;IAOxCD,EALA,CAAAuB,UAAA,IAAAwB,8CAAA,iBAAgD,IAAAC,uDAAA,4BAKF;IA2D9ChD,EAAA,CAAAE,cAAA,aAA8B;IAC9BF,EAAA,CAAAuB,UAAA,IAAA0B,6DAAA,iCAKC;IACDjD,EAAA,CAAAgB,YAAA,EAAM;;;;;IAvEyBhB,EAAA,CAAAiB,SAAA,EAAe;IAAfjB,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAkC,SAAA,CAAe;IAK/BzC,EAAA,CAAAiB,SAAA,EAA6B;IAA7BjB,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAuC,SAAA,KAAAvC,MAAA,CAAAkC,SAAA,CAA6B;IA4DvBzC,EAAA,CAAAiB,SAAA,GAAgB;IAAhBjB,EAAA,CAAAkB,UAAA,UAAAX,MAAA,CAAAkC,SAAA,CAAgB;;;;;IAYjCzC,EADJ,CAAAE,cAAA,cAAgD,cAClB;IACtBF,EAAA,CAAA8B,SAAA,qBAAuC;IAE/C9B,EADI,CAAAgB,YAAA,EAAM,EACJ;;;IAFchB,EAAA,CAAAiB,SAAA,GAAa;IAAbjB,EAAA,CAAAkB,UAAA,cAAa;;;;;IAqBTlB,EAAA,CAAAC,uBAAA,GAA8B;IAC1BD,EAAA,CAAAE,cAAA,eAAsB;IAAAF,EAAA,CAAA+B,MAAA,mCACH;IAAA/B,EAAA,CAAAgB,YAAA,EAAO;;;;;;IAMlChB,EAAA,CAAA8B,SAAA,6BAQwD;;;;IADpD9B,EAPmC,CAAAkB,UAAA,0BAAAX,MAAA,CAAAmC,cAAA,CAAwC,sBAAAnC,MAAA,CAAA0B,iBAAA,CACpC,wBAAA1B,MAAA,CAAA2B,WAAA,CAAoC,wBACpD,mBAAmB,oBACvB,2BAA2B,qBAAAlC,EAAA,CAAAmC,eAAA,KAAAQ,GAAA,EACO,iBACrC,mBAAApC,MAAA,CAAAY,cAAA,QACwB,mBAAAZ,MAAA,CAAAc,WAAA,CAAAC,SAAA,GAAAf,MAAA,CAAAY,cAAA,SAAAZ,MAAA,CAAAY,cAAA,OAC8C;;;;;IAUtFnB,EAAA,CAAAC,uBAAA,GAA8B;IAC1BD,EAAA,CAAAE,cAAA,eAAsB;IAAAF,EAAA,CAAA+B,MAAA,0BAChB;IAAA/B,EAAA,CAAAgB,YAAA,EAAO;;;;;;;IA7CzChB,EAAA,CAAAC,uBAAA,GAA8C;IAa9BD,EAHZ,CAAAE,cAAA,cAAyB,cACE,cACF,gBAC4F;IAA1DF,EAAA,CAAAG,UAAA,oBAAA+C,gFAAAtC,MAAA;MAAAZ,EAAA,CAAAK,aAAA,CAAA8C,GAAA;MAAA,MAAA5C,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAUF,MAAA,CAAA6C,WAAA,CAAAxC,MAAA,CAAmB;IAAA,EAAC;IAA7EZ,EAAA,CAAAgB,YAAA,EAAyG;IACzGhB,EAAA,CAAAE,cAAA,gBACqE;IACjEF,EAAA,CAAA8B,SAAA,cAAyD;IACd9B,EAA3C,CAAAE,cAAA,cAA2C,eAA6B;IAAAF,EAAA,CAAA+B,MAAA,oBAAa;IAAA/B,EAAA,CAAAgB,YAAA,EAAO;IACxFhB,EAAA,CAAAuB,UAAA,KAAA8B,uEAAA,0BAA8B;IAKtCrD,EADI,CAAAgB,YAAA,EAAM,EACF;IACRhB,EAAA,CAAAE,cAAA,eAAiE;IAE7DF,EAAA,CAAAuB,UAAA,KAAA+B,6EAAA,kCAQmC;IAE3CtD,EADI,CAAAgB,YAAA,EAAM,EACJ;IAEFhB,EADJ,CAAAE,cAAA,eAAiB,iBACoF;IAA7DF,EAAA,CAAAG,UAAA,oBAAAoD,iFAAA3C,MAAA;MAAAZ,EAAA,CAAAK,aAAA,CAAA8C,GAAA;MAAA,MAAA5C,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAAUF,MAAA,CAAA6C,WAAA,CAAAxC,MAAA,CAAmB;IAAA,EAAC;IAAlEZ,EAAA,CAAAgB,YAAA,EAAiG;IACjGhB,EAAA,CAAAE,cAAA,iBACqE;IACjEF,EAAA,CAAA8B,SAAA,eAAgE;IACrB9B,EAA3C,CAAAE,cAAA,eAA2C,gBAA6B;IAAAF,EAAA,CAAA+B,MAAA,qBAAa;IAAA/B,EAAA,CAAAgB,YAAA,EAAO;IACxFhB,EAAA,CAAAuB,UAAA,KAAAiC,uEAAA,0BAA8B;IAKtCxD,EADI,CAAAgB,YAAA,EAAM,EACF;IACRhB,EAAA,CAAAE,cAAA,eAAyB;IACrBF,EAAA,CAAA8B,SAAA,8BAMkE;IAIlF9B,EAHY,CAAAgB,YAAA,EAAM,EACJ,EACJ,EACJ;;;;;IA1C6BhB,EAAA,CAAAiB,SAAA,IAAa;IAAbjB,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAiC,OAAA,CAAa;IAQXxC,EAAA,CAAAiB,SAAA,GAAgB;IAAhBjB,EAAA,CAAAkB,UAAA,UAAAX,MAAA,CAAAkC,SAAA,CAAgB;IAiBlBzC,EAAA,CAAAiB,SAAA,GAAa;IAAbjB,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAiC,OAAA,CAAa;IAOZxC,EAAA,CAAAiB,SAAA,GAAsC;IAMtDjB,EANgB,CAAAkB,UAAA,0BAAAX,MAAA,CAAAyB,YAAA,CAAsC,wBAAAzB,MAAA,CAAA2B,WAAA,CACnB,sBAAA3B,MAAA,CAAA0B,iBAAA,CAAwC,qBAAAjC,EAAA,CAAAmC,eAAA,KAAAsB,GAAA,EACI,uBACzD,iBACN,mBAAAlD,MAAA,CAAAc,WAAA,CAAAC,SAAA,GAAAf,MAAA,CAAAY,cAAA,SAAAZ,MAAA,CAAAY,cAAA,OACsE,mBAAAZ,MAAA,CAAAY,cAAA,QAC9C;;;;;IA9DpEnB,EAAA,CAAAC,uBAAA,GAA4C;IAMxCD,EALA,CAAAuB,UAAA,IAAAmC,8CAAA,iBAAgD,IAAAC,uDAAA,4BAKF;;;;;IALf3D,EAAA,CAAAiB,SAAA,EAAe;IAAfjB,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAkC,SAAA,CAAe;IAK/BzC,EAAA,CAAAiB,SAAA,EAA6B;IAA7BjB,EAAA,CAAAkB,UAAA,SAAAX,MAAA,CAAAuC,SAAA,KAAAvC,MAAA,CAAAkC,SAAA,CAA6B;;;AD5E5D,OAAM,MAAOmB,gBAAgB;EAqE3BC,YACUC,cAA8B,EAC9BC,gBAAkC,EAClCC,cAA8B,EAC/B3C,WAAwB,EACvB4C,mBAAwC,EACxCC,YAA0B,EAC1BC,aAA4B,EAC5BC,GAAsB;IAPtB,KAAAN,cAAc,GAAdA,cAAc;IACd,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACf,KAAA3C,WAAW,GAAXA,WAAW;IACV,KAAA4C,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,GAAG,GAAHA,GAAG;IA1EmC,KAAAC,QAAQ,GAAG,EAAgB;IACxB,KAAAC,WAAW,GAAG,EAAgB;IAExE,KAAAC,UAAU,GAAG,IAAI;IAM1B,KAAAzB,SAAS,GAAG,KAAK;IACjB,KAAA0B,SAAS,GAAY,KAAK;IAC1B,KAAAC,gBAAgB,GAAa,EAAE;IAC/B,KAAAC,UAAU,GAAa,CAAC,IAAI,CAACZ,cAAc,CAACa,UAAU,CAAC;IAGvD,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,oBAAoB,GAAG,KAAK;IAC5B,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAEhB,KAAAC,SAAS,GAAGrF,QAAQ;IACb,KAAAsF,IAAI,GAAW,EAAE;IACjB,KAAAC,YAAY,GAAS,EAAU;IAC7B,KAAAC,UAAU,GAAY,KAAK;IAC3B,KAAAC,IAAI,GAAiB,EAAkB;IACvC,KAAAC,mBAAmB,GAAW,CAAC;IAC/B,KAAAC,iBAAiB,GAAG,EAAe;IAEpC,KAAAC,IAAI,GAAG,IAAI3F,OAAO,EAAE;IAE5B,KAAA4F,WAAW,GAAY,KAAK;IAC5B,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,gBAAgB,GAAuB,EAAE;IAChC,KAAAC,qBAAqB,GAAuB,EAAE;IAC9C,KAAAC,KAAK,GAAW,EAAE;IAC3B,KAAAC,oBAAoB,GAAc,EAAE;IACpC,KAAAC,YAAY,GAAa,EAAE;IAC3B,KAAAC,YAAY,GAAY,EAAa;IACrC,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,IAAI,GAAS,EAAU;IACvB,KAAAC,OAAO,GAAa,EAAE;IACtB,KAAAC,WAAW,GAAY,IAAI;IAC3B,KAAAC,WAAW,GAAY,IAAI;IAC3B,KAAAC,KAAK,GAAkB,EAAE;IACzB,KAAA9D,cAAc,GAAuB,EAAE;IACvC,KAAAV,YAAY,GAAuB,EAAE;IACrC,KAAAyE,uBAAuB,GAAG;MACxBC,UAAU,EAAE,KAAK;MACjBC,OAAO,EAAE;KACV;IACD,KAAAC,qBAAqB,GAAG;MACtBF,UAAU,EAAE,KAAK;MACjBC,OAAO,EAAE;KACV;IACD,KAAAlE,SAAS,GAAG,IAAI;IAChB,KAAAD,OAAO,GAAG,KAAK;IAEf,KAAAI,MAAM,GAAuB,EAAE;IAC/B,KAAAzB,cAAc,GAAG,CAAC;IAClB,KAAA0F,WAAW,GAAG,IAAI;IAClB,KAAAC,WAAW,GAAG,KAAK;EAYf;EAEJC,QAAQA,CAAA;IAEN,IAAI,CAACvB,IAAI,CAACwB,GAAG,CAAC,IAAI,CAAChD,cAAc,CAACiD,UAAU,CAACC,IAAI,EAAE,CAACC,SAAS,CAACC,GAAG,IAAG;MAClE,IAAIA,GAAG,IAAIA,GAAG,CAACC,MAAM,EAAE;QACrB,IAAI,CAAC7E,OAAO,GAAG,IAAI;MACrB;IACF,CAAC,CAAC,CAAC;IAEH;IACA,IAAI,CAAC2C,YAAY,GAAG,IAAI,CAAC9D,WAAW,CAACiG,eAAe,EAAE;IACtD,IAAI,CAACpC,IAAI,GAAG,IAAI,CAAC7D,WAAW,CAACkG,WAAW,EAAE;IAE1C,IAAI,CAAC/B,IAAI,CAACwB,GAAG,CAAC,IAAI,CAAClD,cAAc,CAAC0D,oBAAoB,CAACL,SAAS,CAACC,GAAG,IAAG;MACrE,IAAIA,GAAG,CAACK,MAAM,CAACC,QAAS,GAAG,CAAC,EAAE;QAC5B,KAAK,IAAIC,GAAG,IAAI,IAAI,CAAC9B,qBAAqB,EAAE;UAC1C,IAAI+B,KAAK,GAAY,KAAK;UAC1B,KAAK,IAAIC,IAAI,IAAIF,GAAG,CAACG,YAAY,EAAE;YACjC,IAAID,IAAI,CAACE,MAAM,IAAIX,GAAG,CAACY,WAAW,CAACD,MAAM,EAAE;cACzCH,KAAK,GAAG,IAAI;YACd;UACF;UACA,IAAIA,KAAK,EAAE;YACTD,GAAG,CAACD,QAAQ,GAAGN,GAAG,CAACK,MAAM,CAACC,QAAS;YACnCC,GAAG,CAACM,IAAI,GAAGb,GAAG,CAACK,MAAM,CAACQ,IAAI;YAC1BN,GAAG,CAACO,MAAM,GAAGd,GAAG,CAACK,MAAM,CAACS,MAAM;UAChC;QACF;MACF;IACF,CAAC,CAAC,CAAC;IAEH,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,2BAA2B,EAAE;IAClC,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,oBAAoB,EAAE;IAE3B;IACA,IAAI,CAACrC,YAAY,GAAG,IAAI,CAACnC,cAAc,CAACyE,4BAA4B,EAAE;IACtE,IAAI,CAACnC,IAAI,GAAG,IAAI,CAAC/E,WAAW,CAACiG,eAAe,EAAE;IAC9C,IAAI,CAACpC,IAAI,GAAG,IAAI,CAACkB,IAAI,CAAClB,IAAK;EAC7B;EAEAsD,eAAeA,CAAA;IACb,IAAI,CAAChD,IAAI,CAACiD,IAAI,GAAG,IAAI,CAACzE,cAAc,CAACiD,UAAU,CAACC,IAAI,CAClDvH,IAAI,CAAC,CAAC,CAAC,EACPD,SAAS,CAAC0H,GAAG,IAAG;MACdsB,OAAO,CAACC,GAAG,CAACvB,GAAG,CAAC;MAChB,IAAIA,GAAG,CAACwB,KAAK,IAAIxB,GAAG,CAACyB,KAAK,EAAE;QAC1B,OAAO9I,EAAE,CAAC,CAAC,CAAC;MACd,CAAC,MAAM;QACL,OAAO,IAAI,CAACoE,aAAa,CAAC2E,cAAc;MAC1C;IACF,CAAC,CAAC,EACFrJ,SAAS,CAAC,CAAC,CAAC,CACb,CAAC0H,SAAS,CAAC4B,MAAM,IAAG;MACnB,MAAMC,YAAY,GAAG,IAAI,CAAC3H,WAAW,CAACC,SAAS,GAAG,GAAG,GAAG,GAAG;MAC3D,IAAIyH,MAAM,KAAK,CAAC,EAAE;QAChB,IAAI,CAAC5H,cAAc,GAAG4H,MAAM,GAAGC,YAAY;QAC3C,IAAI,IAAI,CAAC1E,WAAW,EAAE;UACpB,IAAI,CAACA,WAAW,CAAC2E,aAAa,CAACC,KAAK,CAACH,MAAM,GAAIA,MAAM,GAAG,EAAE,GAAG,IAAK;QACpE;QACA,IAAI,IAAI,CAAC1E,QAAQ,EAAE;UACjB,IAAI,CAACA,QAAQ,CAAC4E,aAAa,CAACC,KAAK,CAACC,SAAS,GAAIJ,MAAM,GAAG,GAAG,GAAG,IAAK;QACrE;QACA,IAAI,CAAC3E,GAAG,CAACgF,aAAa,EAAE;MAC1B,CAAC,MAAM;QACL,IAAI,CAACjI,cAAc,GAAG,GAAG;QACzB,IAAI,CAACiD,GAAG,CAACgF,aAAa,EAAE;MAC1B;IACF,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACC,oBAAoB,EAAE;MAC7B,IAAI,CAACzI,mBAAmB,CAAC,IAAI,CAAC0E,iBAAiB,CAAC;IAClD;EACF;EAEAgE,WAAWA,CAAA;IACT,IAAI,CAAC/D,IAAI,CAACgE,WAAW,EAAE;IACvB,IAAI,CAAC1F,cAAc,CAAC2F,6BAA6B,CAAC,CAAC,CAAC;IACpD,IAAI,CAAC3F,cAAc,CAAC4F,2BAA2B,CAAC,EAAe,CAAC;IAChE,IAAI,CAAC5F,cAAc,CAAC6F,sBAAsB,CAAC,IAAI,CAAC;EAClD;EAEA,IAAIL,oBAAoBA,CAAA;IACtB,OAAQ,IAAI,CAAC/D,iBAAiB,IAAIqE,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtE,iBAAiB,CAAC,CAACuE,MAAM,GAAG,CAAC;EAClF;EAEA3B,kBAAkBA,CAAA;IAChB,IAAI,CAAC3C,IAAI,CAACwB,GAAG,CAAC,IAAI,CAAClD,cAAc,CAACiG,cAAc,CAAC5C,SAAS,CAACC,GAAG,IAAG;MAC/D,IAAIA,GAAG,EAAE;QACP,IAAI,CAAC3E,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC4F,cAAc,EAAE;MACvB;IACF,CAAC,CAAC,CAAC;EACL;EAEAD,2BAA2BA,CAAA;IACzB,IAAI,CAAC5C,IAAI,CAACwB,GAAG,CAAC,IAAI,CAAClD,cAAc,CAACkG,0BAA0B,CAAC7C,SAAS,CAACC,GAAG,IAAG;MAC3E,IAAIA,GAAG,IAAI,CAAC,EAAE;QACZsB,OAAO,CAACC,GAAG,CAACvB,GAAG,CAAC;QAChB,IAAI,CAAC6C,eAAe,CAAC7C,GAAG,CAAC;MAC3B;IACF,CAAC,CAAC,CAAC;EACL;EAEAiB,cAAcA,CAAA;IACZ,IAAI,CAAC7C,IAAI,CAACwB,GAAG,CAAC,IAAI,CAAClD,cAAc,CAACoG,wBAAwB,EAAE,CAAC/C,SAAS,CAACC,GAAG,IAAG;MAC3E,IAAI,CAAC3E,SAAS,GAAG,KAAK;MACtB,IAAI,CAACG,MAAM,GAAGwE,GAAG;MACjB,IAAI,CAACtD,cAAc,CAACqG,yBAAyB,CAAC,IAAI,CAAC;IACrD,CAAC,CAAC,CAAC;EACL;EAEAF,eAAeA,CAAC/H,WAAmB,EAAEkI,WAAW,GAAG,KAAK;IACtD,IAAIA,WAAW,EAAE;MACf,IAAI,CAAC3H,SAAS,GAAG,IAAI;IACvB;IACA,IAAI,CAAC6D,WAAW,GAAGpE,WAAW,KAAK,CAAC;IACpC,IAAI,CAACQ,cAAc,GAAG,EAAE;IACxB,IAAI,CAACV,YAAY,GAAG,EAAE;IACtB,MAAMqI,QAAQ,GAAG,CACf,IAAI,CAACvG,cAAc,CAACwG,wBAAwB,CAACpI,WAAW,CAAC,EACzD,IAAI,CAAC4B,cAAc,CAACyG,iBAAiB,CAACrI,WAAW,CAAC,EAClD,IAAI,CAAC4B,cAAc,CAAC0G,uBAAuB,CAACtI,WAAW,CAAC,CACzD;IACD,IAAI,CAACsD,IAAI,CAACwB,GAAG,CAAClH,QAAQ,CAACuK,QAAQ,CAAC,CAAClD,SAAS,CAAC,CAAC,CAACsD,UAAU,EAAEC,cAAc,EAAEC,QAAQ,CAAC,KAAI;MACpF,IAAI,CAAClI,SAAS,GAAG,KAAK;MACtB,IAAI,IAAI,CAACpB,WAAW,CAACuJ,SAAS,EAAE;QAC9B,IAAI,CAAClI,cAAc,GAAG+H,UAAU;QAChC,IAAI,CAACzI,YAAY,GAAG2I,QAAQ;MAC9B,CAAC,MAAM;QACL,IAAI,CAACjI,cAAc,GAAG+H,UAAU;QAChC,IAAI,CAACzI,YAAY,GAAG0I,cAAc;MACpC;IACF,CAAC,CAAC,CAAC;EACL;EAEAG,gBAAgBA,CAAA;IACd,IAAI,CAAClF,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAC1C,IAAI,CAACM,YAAY,CAACwB,MAAM,GAAG,IAAI,CAAC/C,UAAU,CAAC,IAAI,CAACA,UAAU,CAACoF,MAAM,GAAG,CAAC,CAAC;IACtE,IAAI,CAAC9F,cAAc,CAAC8G,mBAAmB,CAAC,IAAI,CAACnF,cAAc,EAAE,IAAI,CAACoF,UAAU,CAAC9B,aAAa,CAAC;EAC7F;EAGAX,oBAAoBA,CAAA;IAClB,IAAI,CAACzG,kBAAkB,GAAG,IAAI,CAACkC,gBAAgB,CAACiH,oBAAoB,CAAC,IAAI,CAAC7F,YAAY,CAAC8F,EAAE,CAAC;IAC1F;IACA;IACA;IACA;IACA;IACA;EACF;EAGA;EACA;EACA;EACA;EAEAC,eAAeA,CAACxD,QAAgB;IAC9B,IAAI,CAAClC,IAAI,CAACwB,GAAG,CAAC,IAAI,CAAClD,cAAc,CAACqH,YAAY,CAACzD,QAAQ,CAAC,CAACP,SAAS,CAAEiE,QAAQ,IAAI;MAC9E,IAAI,CAAClH,YAAY,CAACmH,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;IACJ,CAAC,CAAC,CAAC;EACL;EAEA3K,mBAAmBA,CAAC4K,SAAoB;IACtC,IAAI,CAACA,SAAS,EAAE;MACd;IACF;IACA,MAAMvJ,WAAW,GAAGwJ,QAAQ,CAACD,SAAS,CAACR,EAAE,CAAC;IAC1C,IAAI,CAACxI,SAAS,GAAG,IAAI;IACrB,IAAI,CAACK,SAAS,GAAG,IAAI;IACrB,IAAI,CAACZ,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACD,iBAAiB,GAAGwJ,SAAS,CAACxJ,iBAAiB;IAEpD,IAAI,CAAC6B,cAAc,CAAC2F,6BAA6B,CAACiC,QAAQ,CAACD,SAAS,CAACR,EAAE,CAAC,CAAC;IACzE,IAAI,CAACnH,cAAc,CAAC4F,2BAA2B,CAAC+B,SAAS,CAAC;IAC1D,IAAI,CAAC3H,cAAc,CAAC6F,sBAAsB,CAAC,KAAK,CAAC;IACjD,IAAI,CAACM,eAAe,CAAC/H,WAAW,CAAC;EACnC;EAEAyJ,WAAWA,CAACC,IAAY;IACtB,IAAIA,IAAI,EAAE;MACR,IAAIC,CAAC,GAAGD,IAAI,CAACE,WAAW,CAAC,GAAG,CAAC;MAC7B,IAAID,CAAC,KAAK,CAAC,CAAC,EACVA,CAAC,GAAGD,IAAI,CAACE,WAAW,CAAC,IAAI,CAAC;MAC5B,IAAIC,MAAM,GAAGH,IAAI,CAACI,SAAS,CAACH,CAAC,GAAG,CAAC,CAAC;MAClC,OAAOE,MAAM;IACf;IACA,OAAOH,IAAI;EACb;EAGA;EAGAlL,aAAaA,CAAA;IACX,IAAI,CAACoC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACuF,cAAc,EAAE;IACrB;IACA,IAAI,CAACvE,cAAc,CAAC2F,6BAA6B,CAAC,CAAC,CAAC;IACpD,IAAI,CAAC3F,cAAc,CAAC4F,2BAA2B,CAAC,EAAe,CAAC;IAChE,IAAI,CAAC5F,cAAc,CAAC6F,sBAAsB,CAAC,IAAI,CAAC;IAChD;IACA;IACA;IACA;IACA;EACF;EAEA5I,8BAA8BA,CAACkL,KAAU;IACvCvD,OAAO,CAACC,GAAG,CAACsD,KAAK,CAAC;IAClB,IAAI,CAACtK,mBAAmB,GAAGsK,KAAK;EAClC;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACxG,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;IAC9CyG,UAAU,CAAC,MAAK;MACd,IAAI,CAACrI,cAAc,CAACoI,eAAe,CAAC,IAAI,CAACxG,gBAAgB,CAAC;IAC5D,CAAC,EAAE,GAAG,CAAC;EACT;EAEAtC,WAAWA,CAAC6I,KAAY;IACtB,MAAMG,MAAM,GAAGH,KAAK,CAACG,MAA0B;IAC/C,IAAIA,MAAM,CAACnB,EAAE,KAAK,OAAO,EAAE;MACzB;MACA;MAEA,IAAI,CAACpE,WAAW,GAAG,IAAI;MACvB,IAAI,CAACC,WAAW,GAAG,KAAK;MACxB4B,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IACjC,CAAC,MAAM,IAAIyD,MAAM,CAACnB,EAAE,KAAK,OAAO,EAAE;MAChC;MACA;MACAvC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;MAC/B,IAAI,CAAC9B,WAAW,GAAG,KAAK;MACxB,IAAI,CAACC,WAAW,GAAG,IAAI;IACzB;EACF;EAAC,QAAAuF,CAAA,G;qBApUUzI,gBAAgB,EAAA5D,EAAA,CAAAsM,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAxM,EAAA,CAAAsM,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAA1M,EAAA,CAAAsM,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA5M,EAAA,CAAAsM,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA9M,EAAA,CAAAsM,iBAAA,CAAAS,EAAA,CAAAvN,mBAAA,GAAAQ,EAAA,CAAAsM,iBAAA,CAAAU,EAAA,CAAAC,YAAA,GAAAjN,EAAA,CAAAsM,iBAAA,CAAAY,EAAA,CAAAC,aAAA,GAAAnN,EAAA,CAAAsM,iBAAA,CAAAtM,EAAA,CAAAoN,iBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAhBzJ,gBAAgB;IAAA0J,SAAA;IAAAC,SAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;qCAFhB,CAACjO,mBAAmB,CAAC,GAAAQ,EAAA,CAAA2N,oBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0BAAAP,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCjB9BzN,EAJJ,CAAAE,cAAA,0BAEqH,aAEhE;QAC7CF,EAAA,CAAAuB,UAAA,IAAA0M,+BAAA,iBAAiJ;QASjJjO,EAAA,CAAAE,cAAA,aAAoK;QA+EhKF,EA7EA,CAAAuB,UAAA,IAAA2M,wCAAA,0BAA4C,IAAAC,wCAAA,0BA6EA;QAwExDnO,EAFQ,CAAAgB,YAAA,EAAM,EACJ,EACS;;;QAnK+BhB,EAA9C,CAAAkB,UAAA,8CAA6C,mEAAmE;QAGvElB,EAAA,CAAAiB,SAAA,GAA2B;QAA3BjB,EAAA,CAAAkB,UAAA,UAAAwM,GAAA,CAAApE,oBAAA,CAA2B;QASrCtJ,EAAA,CAAAiB,SAAA,EAAuB;QAAvBjB,EAAA,CAAAoO,WAAA,kBAAuB;QAAoCpO,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAqO,eAAA,IAAAC,GAAA,EAAAZ,GAAA,CAAA/L,mBAAA,EAAA+L,GAAA,CAAApE,oBAAA,EAA6E;QAEhJtJ,EAAA,CAAAiB,SAAA,EAA2B;QAA3BjB,EAAA,CAAAkB,UAAA,SAAAwM,GAAA,CAAArM,WAAA,CAAAuJ,SAAA,CAA2B;QA6E3B5K,EAAA,CAAAiB,SAAA,EAA2B;QAA3BjB,EAAA,CAAAkB,UAAA,SAAAwM,GAAA,CAAArM,WAAA,CAAAC,SAAA,CAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}