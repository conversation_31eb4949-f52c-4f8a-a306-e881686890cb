{"ast": null, "code": "export var Statistic;\n(function (Statistic) {\n  Statistic[\"DIVISION\"] = \"Division\";\n  Statistic[\"OVERVIEW\"] = \"Overview\";\n  Statistic[\"PROGRESS\"] = \"Progress\";\n  Statistic[\"HOMEWORK\"] = \"Homework\";\n})(Statistic || (Statistic = {}));\nexport const dummyReportRequests = [{\n  classroomId: 123,\n  title: \"Report 1 #KI16\",\n  level: \"A1\",\n  studentIds: [\"1\", \"2\", \"3\"],\n  period: {\n    from: \"2022-01-01\",\n    to: \"2022-01-31\"\n  },\n  note: \"This is a sample report\",\n  dateCreated: \"2023-02-01\"\n}, {\n  classroomId: 456,\n  title: \"Report 2 #KI15\",\n  level: \"A2\",\n  studentIds: [\"4\", \"5\", \"6\"],\n  period: {\n    from: \"2022-02-01\",\n    to: \"2022-02-28\"\n  },\n  note: \"This is another sample report\",\n  dateCreated: \"2022-03-01\"\n}];", "map": {"version": 3, "names": ["Statistic", "dummyReportRequests", "classroomId", "title", "level", "studentIds", "period", "from", "to", "note", "dateCreated"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\models\\report.model.ts"], "sourcesContent": ["import { Homework } from \"./homework.model\";\r\nimport { LessonBreakdown } from \"./rating.model\";\r\nimport { User } from \"./user.model\";\r\n\r\nexport interface Report {\r\n    id: string,\r\n    title: string,\r\n    student: User[],\r\n    period: ReportPeriod,\r\n    lessonRatings: LessonBreakdown[],\r\n    correspondingHomeworks: Homework[],\r\n    note: string,\r\n    dateCreated: Date\r\n}\r\n\r\nexport interface ReportPeriod {\r\n    from: Date,\r\n    to: Date\r\n}\r\n\r\nexport enum Statistic {\r\n    DIVISION = 'Division',\r\n    OVERVIEW = 'Overview',\r\n    PROGRESS = 'Progress',\r\n    HOMEWORK = 'Homework'\r\n}\r\n\r\nexport interface getReportRequest {\r\n    classId: number,\r\n    studentIds: string[],\r\n    start: string,\r\n    end: string\r\n}\r\n\r\nexport interface CreateReportRequest {\r\n    classroomId: number,\r\n    title: string,\r\n    level: string,\r\n    studentIds: string[],\r\n    period: {\r\n        from: string,\r\n        to: string\r\n    },\r\n    note: string,\r\n    dateCreated: string\r\n}\r\n\r\nexport const dummyReportRequests: any[] = [\r\n    {\r\n      classroomId: 123,\r\n      title: \"Report 1 #KI16\",\r\n      level: \"A1\",\r\n      studentIds: [\"1\", \"2\", \"3\"],\r\n      period: {\r\n        from: \"2022-01-01\",\r\n        to: \"2022-01-31\"\r\n      },\r\n      note: \"This is a sample report\",\r\n      dateCreated: \"2023-02-01\"\r\n    },\r\n    {\r\n      classroomId: 456,\r\n      title: \"Report 2 #KI15\",\r\n      level: \"A2\",\r\n      studentIds: [\"4\", \"5\", \"6\"],\r\n      period: {\r\n        from: \"2022-02-01\",\r\n        to: \"2022-02-28\"\r\n      },\r\n      note: \"This is another sample report\",\r\n      dateCreated: \"2022-03-01\"\r\n    }\r\n  ];"], "mappings": "AAoBA,WAAYA,SAKX;AALD,WAAYA,SAAS;EACjBA,SAAA,yBAAqB;EACrBA,SAAA,yBAAqB;EACrBA,SAAA,yBAAqB;EACrBA,SAAA,yBAAqB;AACzB,CAAC,EALWA,SAAS,KAATA,SAAS;AA2BrB,OAAO,MAAMC,mBAAmB,GAAU,CACtC;EACEC,WAAW,EAAE,GAAG;EAChBC,KAAK,EAAE,gBAAgB;EACvBC,KAAK,EAAE,IAAI;EACXC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3BC,MAAM,EAAE;IACNC,IAAI,EAAE,YAAY;IAClBC,EAAE,EAAE;GACL;EACDC,IAAI,EAAE,yBAAyB;EAC/BC,WAAW,EAAE;CACd,EACD;EACER,WAAW,EAAE,GAAG;EAChBC,KAAK,EAAE,gBAAgB;EACvBC,KAAK,EAAE,IAAI;EACXC,UAAU,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC3BC,MAAM,EAAE;IACNC,IAAI,EAAE,YAAY;IAClBC,EAAE,EAAE;GACL;EACDC,IAAI,EAAE,+BAA+B;EACrCC,WAAW,EAAE;CACd,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}