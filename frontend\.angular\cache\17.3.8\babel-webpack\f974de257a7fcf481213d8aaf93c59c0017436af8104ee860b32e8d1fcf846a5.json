{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport { ClassroomModule } from '../classroom/classroom.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { ScrollPanelModule } from 'primeng/scrollpanel';\nimport { CalendarModule } from 'primeng/calendar';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { OverlayPanelModule } from 'primeng/overlaypanel';\nimport { RouterModule } from '@angular/router';\nimport { DashboardRoutingModule } from './dashboard-routing.module';\nimport { DeferModule } from 'primeng/defer';\nimport { CarouselModule } from 'primeng/carousel';\nimport * as i0 from \"@angular/core\";\nexport let DashboardModule = /*#__PURE__*/(() => {\n  class DashboardModule {\n    static #_ = this.ɵfac = function DashboardModule_Factory(t) {\n      return new (t || DashboardModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: DashboardModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, RouterModule, DashboardRoutingModule, ClassroomModule, SharedModule, ReactiveFormsModule, MatProgressSpinnerModule, ScrollPanelModule, CalendarModule, FormsModule, InputTextModule, DropdownModule, OverlayPanelModule, DeferModule, CarouselModule]\n    });\n  }\n  return DashboardModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}