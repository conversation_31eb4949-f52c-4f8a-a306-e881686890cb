{"ast": null, "code": "import { animation, style, animate, trigger, transition, useAnimation } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, booleanAttribute, numberAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChild, ViewChild, ContentChildren, NgModule } from '@angular/core';\nimport * as i1 from 'primeng/api';\nimport { ConfirmEventType, TranslationKeys, Footer, PrimeTemplate, SharedModule } from 'primeng/api';\nimport * as i3 from 'primeng/button';\nimport { ButtonModule } from 'primeng/button';\nimport { DomHandler } from 'primeng/dom';\nimport { CheckIcon } from 'primeng/icons/check';\nimport { TimesIcon } from 'primeng/icons/times';\nimport * as i4 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { UniqueComponentId, ZIndexUtils } from 'primeng/utils';\nconst _c0 = [\"content\"];\nconst _c1 = [[[\"p-footer\"]]];\nconst _c2 = [\"p-footer\"];\nconst _c3 = a0 => ({\n  \"p-dialog p-confirm-dialog p-component\": true,\n  \"p-dialog-rtl\": a0\n});\nconst _c4 = (a0, a1) => ({\n  transform: a0,\n  transition: a1\n});\nconst _c5 = a0 => ({\n  value: \"visible\",\n  params: a0\n});\nconst _c6 = a0 => ({\n  $implicit: a0\n});\nconst _c7 = () => ({\n  \"p-dialog-header-icon p-dialog-header-close p-link\": true\n});\nfunction ConfirmDialog_div_0_div_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ConfirmDialog_div_0_div_1_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.headlessTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c6, ctx_r1.confirmation));\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_div_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtemplate(1, ConfirmDialog_div_0_div_1_ng_template_2_div_0_ng_container_1_Template, 1, 0, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.headerTemplate);\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_div_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"id\", ctx_r1.ariaLabelledBy);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.option(\"header\"));\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_div_1_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function ConfirmDialog_div_0_div_1_ng_template_2_div_1_button_3_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.close($event));\n    })(\"keydown.enter\", function ConfirmDialog_div_0_div_1_ng_template_2_div_1_button_3_Template_button_keydown_enter_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.close($event));\n    });\n    i0.ɵɵelement(1, \"TimesIcon\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction0(2, _c7));\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.closeAriaLabel);\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtemplate(1, ConfirmDialog_div_0_div_1_ng_template_2_div_1_span_1_Template, 2, 2, \"span\", 16);\n    i0.ɵɵelementStart(2, \"div\", 17);\n    i0.ɵɵtemplate(3, ConfirmDialog_div_0_div_1_ng_template_2_div_1_button_3_Template, 2, 3, \"button\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.option(\"header\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.closable);\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_i_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassMap(ctx_r1.option(\"icon\"));\n    i0.ɵɵproperty(\"ngClass\", \"p-confirm-dialog-icon\");\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_ng_container_5_1_ng_template_0_Template(rf, ctx) {}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_ng_container_5_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ConfirmDialog_div_0_div_1_ng_template_2_ng_container_5_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ConfirmDialog_div_0_div_1_ng_template_2_ng_container_5_1_Template, 1, 0, null, 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.iconTemplate);\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 21);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.option(\"message\"), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_ng_container_7_1_ng_template_0_Template(rf, ctx) {}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_ng_container_7_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ConfirmDialog_div_0_div_1_ng_template_2_ng_container_7_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ConfirmDialog_div_0_div_1_ng_template_2_ng_container_7_1_Template, 1, 0, null, 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.messageTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c6, ctx_r1.confirmation));\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_div_8_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵprojection(1);\n    i0.ɵɵtemplate(2, ConfirmDialog_div_0_div_1_ng_template_2_div_8_ng_container_2_Template, 1, 0, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.footerTemplate);\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_1_ng_container_1_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\");\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(7);\n    i0.ɵɵclassMap(ctx_r1.option(\"rejectIcon\"));\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_1_ng_container_1_TimesIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"TimesIcon\", 28);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-button-icon-left\");\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_1_ng_container_1_i_1_Template, 1, 2, \"i\", 26)(2, ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_1_ng_container_1_TimesIcon_2_Template, 1, 1, \"TimesIcon\", 27);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.option(\"rejectIcon\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.option(\"rejectIcon\"));\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_1_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_1_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_1_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 29);\n    i0.ɵɵtemplate(1, ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_1_span_2_1_Template, 1, 0, null, 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.rejectIconTemplate);\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_1_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.reject());\n    });\n    i0.ɵɵtemplate(1, ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_1_ng_container_1_Template, 3, 2, \"ng-container\", 11)(2, ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_1_span_2_Template, 2, 1, \"span\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵclassMap(ctx_r1.option(\"rejectButtonStyleClass\"));\n    i0.ɵɵproperty(\"label\", ctx_r1.rejectButtonLabel)(\"ngClass\", \"p-confirm-dialog-reject\");\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.rejectAriaLabel);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.rejectIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.rejectIconTemplate);\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_2_ng_container_1_i_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\");\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(7);\n    i0.ɵɵclassMap(ctx_r1.option(\"acceptIcon\"));\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_2_ng_container_1_CheckIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"CheckIcon\", 28);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-button-icon-left\");\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_2_ng_container_1_i_1_Template, 1, 2, \"i\", 26)(2, ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_2_ng_container_1_CheckIcon_2_Template, 1, 1, \"CheckIcon\", 27);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.option(\"acceptIcon\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.option(\"acceptIcon\"));\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_2_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_2_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_2_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 29);\n    i0.ɵɵtemplate(1, ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_2_span_2_1_Template, 1, 0, null, 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.acceptIconTemplate);\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_2_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.accept());\n    });\n    i0.ɵɵtemplate(1, ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_2_ng_container_1_Template, 3, 2, \"ng-container\", 11)(2, ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_2_span_2_Template, 2, 1, \"span\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵclassMap(ctx_r1.option(\"acceptButtonStyleClass\"));\n    i0.ɵɵproperty(\"label\", ctx_r1.acceptButtonLabel)(\"ngClass\", \"p-confirm-dialog-accept\");\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.acceptAriaLabel);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.acceptIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.acceptIconTemplate);\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_1_Template, 3, 7, \"button\", 23)(2, ConfirmDialog_div_0_div_1_ng_template_2_div_9_button_2_Template, 3, 7, \"button\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.option(\"rejectVisible\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.option(\"acceptVisible\"));\n  }\n}\nfunction ConfirmDialog_div_0_div_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ConfirmDialog_div_0_div_1_ng_template_2_div_0_Template, 2, 1, \"div\", 8)(1, ConfirmDialog_div_0_div_1_ng_template_2_div_1_Template, 4, 2, \"div\", 8);\n    i0.ɵɵelementStart(2, \"div\", 9, 1);\n    i0.ɵɵtemplate(4, ConfirmDialog_div_0_div_1_ng_template_2_i_4_Template, 1, 3, \"i\", 10)(5, ConfirmDialog_div_0_div_1_ng_template_2_ng_container_5_Template, 2, 1, \"ng-container\", 11)(6, ConfirmDialog_div_0_div_1_ng_template_2_span_6_Template, 1, 1, \"span\", 12)(7, ConfirmDialog_div_0_div_1_ng_template_2_ng_container_7_Template, 2, 4, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, ConfirmDialog_div_0_div_1_ng_template_2_div_8_Template, 3, 1, \"div\", 13)(9, ConfirmDialog_div_0_div_1_ng_template_2_div_9_Template, 3, 2, \"div\", 13);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.headerTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.headerTemplate);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.iconTemplate && ctx_r1.option(\"icon\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.iconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.messageTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.messageTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.footer || ctx_r1.footerTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.footer && !ctx_r1.footerTemplate);\n  }\n}\nfunction ConfirmDialog_div_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵlistener(\"@animation.start\", function ConfirmDialog_div_0_div_1_Template_div_animation_animation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAnimationStart($event));\n    })(\"@animation.done\", function ConfirmDialog_div_0_div_1_Template_div_animation_animation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAnimationEnd($event));\n    });\n    i0.ɵɵtemplate(1, ConfirmDialog_div_0_div_1_ng_container_1_Template, 2, 4, \"ng-container\", 6)(2, ConfirmDialog_div_0_div_1_ng_template_2_Template, 10, 8, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notHeadless_r6 = i0.ɵɵreference(3);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r1.styleClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c3, ctx_r1.rtl))(\"ngStyle\", ctx_r1.style)(\"@animation\", i0.ɵɵpureFunction1(14, _c5, i0.ɵɵpureFunction2(11, _c4, ctx_r1.transformOptions, ctx_r1.transitionOptions)));\n    i0.ɵɵattribute(\"aria-labelledby\", ctx_r1.ariaLabelledBy)(\"aria-modal\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.headlessTemplate)(\"ngIfElse\", notHeadless_r6);\n  }\n}\nfunction ConfirmDialog_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵtemplate(1, ConfirmDialog_div_0_div_1_Template, 4, 16, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.maskStyleClass);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getMaskClass());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.visible);\n  }\n}\nconst showAnimation = animation([style({\n  transform: '{{transform}}',\n  opacity: 0\n}), animate('{{transition}}', style({\n  transform: 'none',\n  opacity: 1\n}))]);\nconst hideAnimation = animation([animate('{{transition}}', style({\n  transform: '{{transform}}',\n  opacity: 0\n}))]);\n/**\n * ConfirmDialog uses a Dialog UI that is integrated with the Confirmation API.\n * @group Components\n */\nlet ConfirmDialog = /*#__PURE__*/(() => {\n  class ConfirmDialog {\n    el;\n    renderer;\n    confirmationService;\n    zone;\n    cd;\n    config;\n    document;\n    /**\n     * Title text of the dialog.\n     * @group Props\n     */\n    header;\n    /**\n     * Icon to display next to message.\n     * @group Props\n     */\n    icon;\n    /**\n     * Message of the confirmation.\n     * @group Props\n     */\n    message;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    get style() {\n      return this._style;\n    }\n    set style(value) {\n      this._style = value;\n      this.cd.markForCheck();\n    }\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Specify the CSS class(es) for styling the mask element\n     * @group Props\n     */\n    maskStyleClass;\n    /**\n     * Icon of the accept button.\n     * @group Props\n     */\n    acceptIcon;\n    /**\n     * Label of the accept button.\n     * @group Props\n     */\n    acceptLabel;\n    /**\n     * Defines a string that labels the close button for accessibility.\n     * @group Props\n     */\n    closeAriaLabel;\n    /**\n     * Defines a string that labels the accept button for accessibility.\n     * @group Props\n     */\n    acceptAriaLabel;\n    /**\n     * Visibility of the accept button.\n     * @group Props\n     */\n    acceptVisible = true;\n    /**\n     * Icon of the reject button.\n     * @group Props\n     */\n    rejectIcon;\n    /**\n     * Label of the reject button.\n     * @group Props\n     */\n    rejectLabel;\n    /**\n     * Defines a string that labels the reject button for accessibility.\n     * @group Props\n     */\n    rejectAriaLabel;\n    /**\n     * Visibility of the reject button.\n     * @group Props\n     */\n    rejectVisible = true;\n    /**\n     * Style class of the accept button.\n     * @group Props\n     */\n    acceptButtonStyleClass;\n    /**\n     * Style class of the reject button.\n     * @group Props\n     */\n    rejectButtonStyleClass;\n    /**\n     * Specifies if pressing escape key should hide the dialog.\n     * @group Props\n     */\n    closeOnEscape = true;\n    /**\n     * Specifies if clicking the modal background should hide the dialog.\n     * @group Props\n     */\n    dismissableMask;\n    /**\n     * Determines whether scrolling behavior should be blocked within the component.\n     * @group Props\n     */\n    blockScroll = true;\n    /**\n     * When enabled dialog is displayed in RTL direction.\n     * @group Props\n     */\n    rtl = false;\n    /**\n     * Adds a close icon to the header to hide the dialog.\n     * @group Props\n     */\n    closable = true;\n    /**\n     *  Target element to attach the dialog, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having #mydiv as variable name).\n     * @group Props\n     */\n    appendTo;\n    /**\n     * Optional key to match the key of confirm object, necessary to use when component tree has multiple confirm dialogs.\n     * @group Props\n     */\n    key;\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    autoZIndex = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    baseZIndex = 0;\n    /**\n     * Transition options of the animation.\n     * @group Props\n     */\n    transitionOptions = '150ms cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * When enabled, can only focus on elements inside the confirm dialog.\n     * @group Props\n     */\n    focusTrap = true;\n    /**\n     * Element to receive the focus when the dialog gets visible.\n     * @group Props\n     */\n    defaultFocus = 'accept';\n    /**\n     * Object literal to define widths per screen size.\n     * @group Props\n     */\n    breakpoints;\n    /**\n     * Current visible state as a boolean.\n     * @group Props\n     */\n    get visible() {\n      return this._visible;\n    }\n    set visible(value) {\n      this._visible = value;\n      if (this._visible && !this.maskVisible) {\n        this.maskVisible = true;\n      }\n      this.cd.markForCheck();\n    }\n    /**\n     *  Allows getting the position of the component.\n     * @group Props\n     */\n    get position() {\n      return this._position;\n    }\n    set position(value) {\n      this._position = value;\n      switch (value) {\n        case 'top-left':\n        case 'bottom-left':\n        case 'left':\n          this.transformOptions = 'translate3d(-100%, 0px, 0px)';\n          break;\n        case 'top-right':\n        case 'bottom-right':\n        case 'right':\n          this.transformOptions = 'translate3d(100%, 0px, 0px)';\n          break;\n        case 'bottom':\n          this.transformOptions = 'translate3d(0px, 100%, 0px)';\n          break;\n        case 'top':\n          this.transformOptions = 'translate3d(0px, -100%, 0px)';\n          break;\n        default:\n          this.transformOptions = 'scale(0.7)';\n          break;\n      }\n    }\n    /**\n     * Callback to invoke when dialog is hidden.\n     * @param {ConfirmEventType} enum - Custom confirm event.\n     * @group Emits\n     */\n    onHide = new EventEmitter();\n    footer;\n    contentViewChild;\n    templates;\n    ngAfterContentInit() {\n      this.templates?.forEach(item => {\n        switch (item.getType()) {\n          case 'header':\n            this.headerTemplate = item.template;\n            break;\n          case 'footer':\n            this.footerTemplate = item.template;\n            break;\n          case 'message':\n            this.messageTemplate = item.template;\n            break;\n          case 'icon':\n            this.iconTemplate = item.template;\n            break;\n          case 'rejecticon':\n            this.rejectIconTemplate = item.template;\n            break;\n          case 'accepticon':\n            this.acceptIconTemplate = item.template;\n            break;\n          case 'headless':\n            this.headlessTemplate = item.template;\n            break;\n        }\n      });\n    }\n    headerTemplate;\n    footerTemplate;\n    rejectIconTemplate;\n    acceptIconTemplate;\n    messageTemplate;\n    iconTemplate;\n    headlessTemplate;\n    confirmation;\n    _visible;\n    _style;\n    maskVisible;\n    documentEscapeListener;\n    container;\n    wrapper;\n    contentContainer;\n    subscription;\n    maskClickListener;\n    preWidth;\n    _position = 'center';\n    transformOptions = 'scale(0.7)';\n    styleElement;\n    id = UniqueComponentId();\n    ariaLabelledBy = this.getAriaLabelledBy();\n    confirmationOptions;\n    translationSubscription;\n    constructor(el, renderer, confirmationService, zone, cd, config, document) {\n      this.el = el;\n      this.renderer = renderer;\n      this.confirmationService = confirmationService;\n      this.zone = zone;\n      this.cd = cd;\n      this.config = config;\n      this.document = document;\n      this.subscription = this.confirmationService.requireConfirmation$.subscribe(confirmation => {\n        if (!confirmation) {\n          this.hide();\n          return;\n        }\n        if (confirmation.key === this.key) {\n          this.confirmation = confirmation;\n          this.confirmationOptions = {\n            message: this.confirmation.message || this.message,\n            icon: this.confirmation.icon || this.icon,\n            header: this.confirmation.header || this.header,\n            rejectVisible: this.confirmation.rejectVisible == null ? this.rejectVisible : this.confirmation.rejectVisible,\n            acceptVisible: this.confirmation.acceptVisible == null ? this.acceptVisible : this.confirmation.acceptVisible,\n            acceptLabel: this.confirmation.acceptLabel || this.acceptLabel,\n            rejectLabel: this.confirmation.rejectLabel || this.rejectLabel,\n            acceptIcon: this.confirmation.acceptIcon || this.acceptIcon,\n            rejectIcon: this.confirmation.rejectIcon || this.rejectIcon,\n            acceptButtonStyleClass: this.confirmation.acceptButtonStyleClass || this.acceptButtonStyleClass,\n            rejectButtonStyleClass: this.confirmation.rejectButtonStyleClass || this.rejectButtonStyleClass,\n            defaultFocus: this.confirmation.defaultFocus || this.defaultFocus,\n            blockScroll: this.confirmation.blockScroll === false || this.confirmation.blockScroll === true ? this.confirmation.blockScroll : this.blockScroll,\n            closeOnEscape: this.confirmation.closeOnEscape === false || this.confirmation.closeOnEscape === true ? this.confirmation.closeOnEscape : this.closeOnEscape,\n            dismissableMask: this.confirmation.dismissableMask === false || this.confirmation.dismissableMask === true ? this.confirmation.dismissableMask : this.dismissableMask\n          };\n          if (this.confirmation.accept) {\n            this.confirmation.acceptEvent = new EventEmitter();\n            this.confirmation.acceptEvent.subscribe(this.confirmation.accept);\n          }\n          if (this.confirmation.reject) {\n            this.confirmation.rejectEvent = new EventEmitter();\n            this.confirmation.rejectEvent.subscribe(this.confirmation.reject);\n          }\n          this.visible = true;\n        }\n      });\n    }\n    ngOnInit() {\n      if (this.breakpoints) {\n        this.createStyle();\n      }\n      this.translationSubscription = this.config.translationObserver.subscribe(() => {\n        if (this.visible) {\n          this.cd.markForCheck();\n        }\n      });\n    }\n    getAriaLabelledBy() {\n      return this.header !== null ? UniqueComponentId() + '_header' : null;\n    }\n    option(name) {\n      const source = this.confirmationOptions || this;\n      if (source.hasOwnProperty(name)) {\n        return source[name];\n      }\n      return undefined;\n    }\n    onAnimationStart(event) {\n      switch (event.toState) {\n        case 'visible':\n          this.container = event.element;\n          this.wrapper = this.container?.parentElement;\n          this.contentContainer = DomHandler.findSingle(this.container, '.p-dialog-content');\n          this.container?.setAttribute(this.id, '');\n          this.appendContainer();\n          this.moveOnTop();\n          this.bindGlobalListeners();\n          this.enableModality();\n          const element = this.getElementToFocus();\n          if (element) {\n            element.focus();\n          }\n          break;\n      }\n    }\n    onAnimationEnd(event) {\n      switch (event.toState) {\n        case 'void':\n          this.onOverlayHide();\n          break;\n      }\n    }\n    getElementToFocus() {\n      switch (this.option('defaultFocus')) {\n        case 'accept':\n          return DomHandler.findSingle(this.container, '.p-confirm-dialog-accept');\n        case 'reject':\n          return DomHandler.findSingle(this.container, '.p-confirm-dialog-reject');\n        case 'close':\n          return DomHandler.findSingle(this.container, '.p-dialog-header-close');\n        case 'none':\n          return null;\n        //backward compatibility\n        default:\n          return DomHandler.findSingle(this.container, '.p-confirm-dialog-accept');\n      }\n    }\n    appendContainer() {\n      if (this.appendTo) {\n        if (this.appendTo === 'body') this.document.body.appendChild(this.wrapper);else DomHandler.appendChild(this.wrapper, this.appendTo);\n      }\n    }\n    restoreAppend() {\n      if (this.wrapper && this.appendTo) {\n        this.el.nativeElement.appendChild(this.wrapper);\n      }\n    }\n    enableModality() {\n      if (this.option('blockScroll')) {\n        DomHandler.addClass(this.document.body, 'p-overflow-hidden');\n      }\n      if (this.option('dismissableMask')) {\n        this.maskClickListener = this.renderer.listen(this.wrapper, 'mousedown', event => {\n          if (this.wrapper && this.wrapper.isSameNode(event.target)) {\n            this.close(event);\n          }\n        });\n      }\n    }\n    disableModality() {\n      this.maskVisible = false;\n      if (this.option('blockScroll')) {\n        DomHandler.removeClass(this.document.body, 'p-overflow-hidden');\n      }\n      if (this.dismissableMask) {\n        this.unbindMaskClickListener();\n      }\n      if (this.container && !this.cd['destroyed']) {\n        this.cd.detectChanges();\n      }\n    }\n    createStyle() {\n      if (!this.styleElement) {\n        this.styleElement = this.document.createElement('style');\n        this.styleElement.type = 'text/css';\n        DomHandler.setAttribute(this.styleElement, 'nonce', this.config?.csp()?.nonce);\n        this.document.head.appendChild(this.styleElement);\n        let innerHTML = '';\n        for (let breakpoint in this.breakpoints) {\n          innerHTML += `\n                    @media screen and (max-width: ${breakpoint}) {\n                        .p-dialog[${this.id}] {\n                            width: ${this.breakpoints[breakpoint]} !important;\n                        }\n                    }\n                `;\n        }\n        this.styleElement.innerHTML = innerHTML;\n      }\n    }\n    close(event) {\n      if (this.confirmation?.rejectEvent) {\n        this.confirmation.rejectEvent.emit(ConfirmEventType.CANCEL);\n      }\n      this.hide(ConfirmEventType.CANCEL);\n      event.preventDefault();\n    }\n    hide(type) {\n      this.onHide.emit(type);\n      this.visible = false;\n      this.confirmation = null;\n      this.confirmationOptions = null;\n    }\n    moveOnTop() {\n      if (this.autoZIndex) {\n        ZIndexUtils.set('modal', this.container, this.baseZIndex + this.config.zIndex.modal);\n        this.wrapper.style.zIndex = String(parseInt(this.container.style.zIndex, 10) - 1);\n      }\n    }\n    getMaskClass() {\n      let maskClass = {\n        'p-dialog-mask p-component-overlay': true,\n        'p-dialog-mask-scrollblocker': this.blockScroll\n      };\n      maskClass[this.getPositionClass().toString()] = true;\n      return maskClass;\n    }\n    getPositionClass() {\n      const positions = ['left', 'right', 'top', 'top-left', 'top-right', 'bottom', 'bottom-left', 'bottom-right'];\n      const pos = positions.find(item => item === this.position);\n      return pos ? `p-dialog-${pos}` : '';\n    }\n    bindGlobalListeners() {\n      if (this.option('closeOnEscape') && this.closable || this.focusTrap && !this.documentEscapeListener) {\n        const documentTarget = this.el ? this.el.nativeElement.ownerDocument : 'document';\n        this.documentEscapeListener = this.renderer.listen(documentTarget, 'keydown', event => {\n          if (event.which == 27 && this.option('closeOnEscape') && this.closable) {\n            if (parseInt(this.container.style.zIndex) === ZIndexUtils.get(this.container) && this.visible) {\n              this.close(event);\n            }\n          }\n          if (event.which === 9 && this.focusTrap) {\n            event.preventDefault();\n            let focusableElements = DomHandler.getFocusableElements(this.container);\n            if (focusableElements && focusableElements.length > 0) {\n              if (!focusableElements[0].ownerDocument.activeElement) {\n                focusableElements[0].focus();\n              } else {\n                let focusedIndex = focusableElements.indexOf(focusableElements[0].ownerDocument.activeElement);\n                if (event.shiftKey) {\n                  if (focusedIndex == -1 || focusedIndex === 0) focusableElements[focusableElements.length - 1].focus();else focusableElements[focusedIndex - 1].focus();\n                } else {\n                  if (focusedIndex == -1 || focusedIndex === focusableElements.length - 1) focusableElements[0].focus();else focusableElements[focusedIndex + 1].focus();\n                }\n              }\n            }\n          }\n        });\n      }\n    }\n    unbindGlobalListeners() {\n      if (this.documentEscapeListener) {\n        this.documentEscapeListener();\n        this.documentEscapeListener = null;\n      }\n    }\n    unbindMaskClickListener() {\n      if (this.maskClickListener) {\n        this.maskClickListener();\n        this.maskClickListener = null;\n      }\n    }\n    onOverlayHide() {\n      if (this.container && this.autoZIndex) {\n        ZIndexUtils.clear(this.container);\n      }\n      this.disableModality();\n      this.unbindGlobalListeners();\n      this.container = null;\n    }\n    destroyStyle() {\n      if (this.styleElement) {\n        this.document.head.removeChild(this.styleElement);\n        this.styleElement = null;\n      }\n    }\n    ngOnDestroy() {\n      this.restoreAppend();\n      this.onOverlayHide();\n      this.subscription.unsubscribe();\n      if (this.translationSubscription) {\n        this.translationSubscription.unsubscribe();\n      }\n      this.destroyStyle();\n    }\n    accept() {\n      if (this.confirmation && this.confirmation.acceptEvent) {\n        this.confirmation.acceptEvent.emit();\n      }\n      this.hide(ConfirmEventType.ACCEPT);\n    }\n    reject() {\n      if (this.confirmation && this.confirmation.rejectEvent) {\n        this.confirmation.rejectEvent.emit(ConfirmEventType.REJECT);\n      }\n      this.hide(ConfirmEventType.REJECT);\n    }\n    get acceptButtonLabel() {\n      return this.option('acceptLabel') || this.config.getTranslation(TranslationKeys.ACCEPT);\n    }\n    get rejectButtonLabel() {\n      return this.option('rejectLabel') || this.config.getTranslation(TranslationKeys.REJECT);\n    }\n    static ɵfac = function ConfirmDialog_Factory(t) {\n      return new (t || ConfirmDialog)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i1.ConfirmationService), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(DOCUMENT));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ConfirmDialog,\n      selectors: [[\"p-confirmDialog\"]],\n      contentQueries: function ConfirmDialog_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, Footer, 5);\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.footer = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function ConfirmDialog_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentViewChild = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        header: \"header\",\n        icon: \"icon\",\n        message: \"message\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        maskStyleClass: \"maskStyleClass\",\n        acceptIcon: \"acceptIcon\",\n        acceptLabel: \"acceptLabel\",\n        closeAriaLabel: \"closeAriaLabel\",\n        acceptAriaLabel: \"acceptAriaLabel\",\n        acceptVisible: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"acceptVisible\", \"acceptVisible\", booleanAttribute],\n        rejectIcon: \"rejectIcon\",\n        rejectLabel: \"rejectLabel\",\n        rejectAriaLabel: \"rejectAriaLabel\",\n        rejectVisible: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"rejectVisible\", \"rejectVisible\", booleanAttribute],\n        acceptButtonStyleClass: \"acceptButtonStyleClass\",\n        rejectButtonStyleClass: \"rejectButtonStyleClass\",\n        closeOnEscape: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"closeOnEscape\", \"closeOnEscape\", booleanAttribute],\n        dismissableMask: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"dismissableMask\", \"dismissableMask\", booleanAttribute],\n        blockScroll: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"blockScroll\", \"blockScroll\", booleanAttribute],\n        rtl: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"rtl\", \"rtl\", booleanAttribute],\n        closable: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"closable\", \"closable\", booleanAttribute],\n        appendTo: \"appendTo\",\n        key: \"key\",\n        autoZIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autoZIndex\", \"autoZIndex\", booleanAttribute],\n        baseZIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"baseZIndex\", \"baseZIndex\", numberAttribute],\n        transitionOptions: \"transitionOptions\",\n        focusTrap: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"focusTrap\", \"focusTrap\", booleanAttribute],\n        defaultFocus: \"defaultFocus\",\n        breakpoints: \"breakpoints\",\n        visible: \"visible\",\n        position: \"position\"\n      },\n      outputs: {\n        onHide: \"onHide\"\n      },\n      features: [i0.ɵɵInputTransformsFeature],\n      ngContentSelectors: _c2,\n      decls: 1,\n      vars: 1,\n      consts: [[\"notHeadless\", \"\"], [\"content\", \"\"], [3, \"class\", \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\"], [\"role\", \"alertdialog\", 3, \"ngClass\", \"ngStyle\", \"class\", 4, \"ngIf\"], [\"role\", \"alertdialog\", 3, \"ngClass\", \"ngStyle\"], [4, \"ngIf\", \"ngIfElse\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"p-dialog-header\", 4, \"ngIf\"], [1, \"p-dialog-content\"], [3, \"ngClass\", \"class\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"p-confirm-dialog-message\", 3, \"innerHTML\", 4, \"ngIf\"], [\"class\", \"p-dialog-footer\", 4, \"ngIf\"], [1, \"p-dialog-header\"], [4, \"ngTemplateOutlet\"], [\"class\", \"p-dialog-title\", 3, \"id\", 4, \"ngIf\"], [1, \"p-dialog-header-icons\"], [\"type\", \"button\", \"role\", \"button\", 3, \"ngClass\", \"click\", \"keydown.enter\", 4, \"ngIf\"], [1, \"p-dialog-title\", 3, \"id\"], [\"type\", \"button\", \"role\", \"button\", 3, \"click\", \"keydown.enter\", \"ngClass\"], [1, \"p-confirm-dialog-message\", 3, \"innerHTML\"], [1, \"p-dialog-footer\"], [\"type\", \"button\", \"pRipple\", \"\", \"pButton\", \"\", 3, \"label\", \"ngClass\", \"class\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"pRipple\", \"\", \"pButton\", \"\", 3, \"click\", \"label\", \"ngClass\"], [\"class\", \"p-button-icon-left\", 4, \"ngIf\"], [3, \"class\", 4, \"ngIf\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"], [1, \"p-button-icon-left\"]],\n      template: function ConfirmDialog_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c1);\n          i0.ɵɵtemplate(0, ConfirmDialog_div_0_Template, 2, 4, \"div\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.maskVisible);\n        }\n      },\n      dependencies: () => [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.ButtonDirective, i4.Ripple, TimesIcon, CheckIcon],\n      styles: [\"@layer primeng{.p-dialog-mask{position:fixed;top:0;left:0;width:100%;height:100%;display:flex;justify-content:center;align-items:center;pointer-events:none}.p-dialog-mask.p-component-overlay{pointer-events:auto}.p-dialog{display:flex;flex-direction:column;pointer-events:auto;max-height:90%;transform:scale(1);position:relative}.p-dialog-content{overflow-y:auto;flex-grow:1}.p-dialog-header{display:flex;align-items:center;justify-content:space-between;flex-shrink:0}.p-dialog-draggable .p-dialog-header{cursor:move}.p-dialog-footer{flex-shrink:0}.p-dialog .p-dialog-header-icons{display:flex;align-items:center}.p-dialog .p-dialog-header-icon{display:flex;align-items:center;justify-content:center;overflow:hidden;position:relative}.p-fluid .p-dialog-footer .p-button{width:auto}.p-dialog-top .p-dialog,.p-dialog-bottom .p-dialog,.p-dialog-left .p-dialog,.p-dialog-right .p-dialog,.p-dialog-top-left .p-dialog,.p-dialog-top-right .p-dialog,.p-dialog-bottom-left .p-dialog,.p-dialog-bottom-right .p-dialog{margin:.75rem;transform:translateZ(0)}.p-dialog-maximized{-webkit-transition:none;transition:none;transform:none;width:100vw!important;height:100vh!important;top:0!important;left:0!important;max-height:100%;height:100%}.p-dialog-maximized .p-dialog-content{flex-grow:1}.p-dialog-left{justify-content:flex-start}.p-dialog-right{justify-content:flex-end}.p-dialog-top{align-items:flex-start}.p-dialog-top-left{justify-content:flex-start;align-items:flex-start}.p-dialog-top-right{justify-content:flex-end;align-items:flex-start}.p-dialog-bottom{align-items:flex-end}.p-dialog-bottom-left{justify-content:flex-start;align-items:flex-end}.p-dialog-bottom-right{justify-content:flex-end;align-items:flex-end}.p-dialog .p-resizable-handle{position:absolute;font-size:.1px;display:block;cursor:se-resize;width:12px;height:12px;right:1px;bottom:1px}.p-confirm-dialog .p-dialog-content{display:flex;align-items:center}}\\n\"],\n      encapsulation: 2,\n      data: {\n        animation: [trigger('animation', [transition('void => visible', [useAnimation(showAnimation)]), transition('visible => void', [useAnimation(hideAnimation)])])]\n      },\n      changeDetection: 0\n    });\n  }\n  return ConfirmDialog;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ConfirmDialogModule = /*#__PURE__*/(() => {\n  class ConfirmDialogModule {\n    static ɵfac = function ConfirmDialogModule_Factory(t) {\n      return new (t || ConfirmDialogModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ConfirmDialogModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, ButtonModule, RippleModule, TimesIcon, CheckIcon, ButtonModule, SharedModule]\n    });\n  }\n  return ConfirmDialogModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ConfirmDialog, ConfirmDialogModule };\n//# sourceMappingURL=primeng-confirmdialog.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}