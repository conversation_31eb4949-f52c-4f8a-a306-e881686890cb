{"ast": null, "code": "import { take } from 'rxjs/operators';\nimport { UserRole } from 'src/app/core/models/user.model';\nimport { SubSink } from 'subsink';\nimport { DialogService } from 'primeng/dynamicdialog';\nimport { LibraryFileActionsDialogComponent } from '../library-file-actions-dialog/library-file-actions-dialog.component';\nimport { FilterPipe } from 'src/app/core/pipes/filter.pipe';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/library.service\";\nimport * as i2 from \"src/app/core/services/classroom.service\";\nimport * as i3 from \"src/app/core/services/general.service\";\nimport * as i4 from \"src/app/core/services/auth.service\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"src/app/core/services/toast.service\";\nimport * as i7 from \"src/app/core/services/layout.service\";\nimport * as i8 from \"primeng/dynamicdialog\";\nimport * as i9 from \"src/app/core/services/user.service\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"../../../../../shared/classrooms-check-list/classrooms-check-list.component\";\nimport * as i12 from \"../../../../../shared/prime/confirm-dialog/confirm-dialog.component\";\nimport * as i13 from \"../../../../../shared/block-viewer/block-viewer.component\";\nimport * as i14 from \"primeng/tabview\";\nimport * as i15 from \"@angular/forms\";\nimport * as i16 from \"../library-file-row-item/library-file-row-item.component\";\nimport * as i17 from \"../single-library-folder-item/single-library-folder-item.component\";\nimport * as i18 from \"src/app/core/pipes/filter.pipe\";\nconst _c0 = [\"addLibrary\"];\nconst _c1 = [\"leftSide\"];\nconst _c2 = [\"mainWrapper\"];\nconst _c3 = a0 => ({\n  \"no-visibility\": a0\n});\nconst _c4 = a0 => ({\n  classroom: a0\n});\nconst _c5 = a0 => ({\n  classroom: a0,\n  uniqueFiles: true\n});\nconst _c6 = (a0, a1) => ({\n  classroom: a0,\n  student: a1\n});\nconst _c7 = a0 => ({\n  \"more\": a0\n});\nconst _c8 = a0 => ({\n  \"purple-folder-bg\": a0\n});\nconst _c9 = a0 => ({\n  \"filter-blue\": a0\n});\nconst _c10 = a0 => ({\n  term: a0\n});\nfunction SingleLibraryComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-confirm-dialog\", 8);\n    i0.ɵɵlistener(\"rejectSelected\", function SingleLibraryComponent_ng_container_0_Template_app_confirm_dialog_rejectSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onRejectFolderDelete($event));\n    })(\"confirmSelected\", function SingleLibraryComponent_ng_container_0_Template_app_confirm_dialog_confirmSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onAcceptFolderDelete($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"confirmMessage\", ctx_r2.deleteFolderConfirmData.message);\n  }\n}\nfunction SingleLibraryComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-confirm-dialog\", 9);\n    i0.ɵɵlistener(\"rejectSelected\", function SingleLibraryComponent_ng_container_1_Template_app_confirm_dialog_rejectSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onRejectFileDelete($event));\n    })(\"confirmSelected\", function SingleLibraryComponent_ng_container_1_Template_app_confirm_dialog_confirmSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onAcceptFileDelete($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"confirmMessage\", ctx_r2.deleteFileConfirmData.message);\n  }\n}\nfunction SingleLibraryComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-confirm-dialog\", 9);\n    i0.ɵɵlistener(\"rejectSelected\", function SingleLibraryComponent_ng_container_2_Template_app_confirm_dialog_rejectSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onRejectFileDelete($event));\n    })(\"confirmSelected\", function SingleLibraryComponent_ng_container_2_Template_app_confirm_dialog_confirmSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onAcceptFileDeleteMultiple($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"confirmMessage\", ctx_r2.deleteMultipleFilesConfirmData.message);\n  }\n}\nfunction SingleLibraryComponent_ng_container_9_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction SingleLibraryComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, SingleLibraryComponent_ng_container_9_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    const classInfoGeneralTemplate_r6 = i0.ɵɵreference(12);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", classInfoGeneralTemplate_r6)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, ctx_r2.classroom));\n  }\n}\nfunction SingleLibraryComponent_ng_container_10_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction SingleLibraryComponent_ng_container_10_ng_container_5_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction SingleLibraryComponent_ng_container_10_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p-tabPanel\", 15);\n    i0.ɵɵtemplate(2, SingleLibraryComponent_ng_container_10_ng_container_5_ng_container_2_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const selectedStudent_r8 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    const classInfoGeneralTemplate_r6 = i0.ɵɵreference(12);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"header\", selectedStudent_r8.firstName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", classInfoGeneralTemplate_r6)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(3, _c6, ctx_r2.classroom, selectedStudent_r8));\n  }\n}\nfunction SingleLibraryComponent_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p-tabView\", 11);\n    i0.ɵɵlistener(\"onChange\", function SingleLibraryComponent_ng_container_10_Template_p_tabView_onChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onTabChangeFilterFilesPerStudent($event));\n    });\n    i0.ɵɵelementStart(2, \"p-tabPanel\", 12)(3, \"div\", 13);\n    i0.ɵɵtemplate(4, SingleLibraryComponent_ng_container_10_ng_container_4_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, SingleLibraryComponent_ng_container_10_ng_container_5_Template, 3, 6, \"ng-container\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    const classInfoGeneralTemplate_r6 = i0.ɵɵreference(12);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"scrollable\", ctx_r2.classroomStudents.length > 4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", classInfoGeneralTemplate_r6)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c5, ctx_r2.classroom));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.classroomStudents);\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_img_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 43);\n    i0.ɵɵlistener(\"click\", function SingleLibraryComponent_ng_template_11_img_7_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.setGroupActions());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c9, ctx_r2.showGroupActions));\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_img_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 44);\n    i0.ɵɵlistener(\"click\", function SingleLibraryComponent_ng_template_11_img_8_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onShowUploadDialog());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_div_10_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"app-classrooms-check-list\", 56);\n    i0.ɵɵlistener(\"close\", function SingleLibraryComponent_ng_template_11_div_10_div_16_Template_app_classrooms_check_list_close_1_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.showShare = !ctx_r2.showShare);\n    })(\"checkedList\", function SingleLibraryComponent_ng_template_11_div_10_div_16_Template_app_classrooms_check_list_checkedList_1_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onCheckedList($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 57);\n    i0.ɵɵlistener(\"click\", function SingleLibraryComponent_ng_template_11_div_10_div_16_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.shareLibraryFiles());\n    });\n    i0.ɵɵtext(3, \" Share \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46)(2, \"div\", 47);\n    i0.ɵɵelement(3, \"img\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 49);\n    i0.ɵɵlistener(\"click\", function SingleLibraryComponent_ng_template_11_div_10_Template_div_click_4_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.openShareDialog());\n    });\n    i0.ɵɵtext(5, \" Share \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 50);\n    i0.ɵɵlistener(\"click\", function SingleLibraryComponent_ng_template_11_div_10_Template_div_click_6_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.moveMultipleFiles());\n    });\n    i0.ɵɵelementStart(7, \"div\", 47);\n    i0.ɵɵelement(8, \"img\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 52);\n    i0.ɵɵtext(10, \" Move \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 50);\n    i0.ɵɵlistener(\"click\", function SingleLibraryComponent_ng_template_11_div_10_Template_div_click_11_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.deleteMultipleFiles());\n    });\n    i0.ɵɵelementStart(12, \"div\", 47);\n    i0.ɵɵelement(13, \"img\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 52);\n    i0.ɵɵtext(15, \" Delete \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, SingleLibraryComponent_ng_template_11_div_10_div_16_Template, 4, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showShare);\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"app-single-library-folder-item\", 59);\n    i0.ɵɵlistener(\"deleteFolder\", function SingleLibraryComponent_ng_template_11_div_18_Template_app_single_library_folder_item_deleteFolder_1_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.deleteFolderSelected($event));\n    })(\"openFolder\", function SingleLibraryComponent_ng_template_11_div_18_Template_app_single_library_folder_item_openFolder_1_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.openFolderSelected($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const innerFolder_r15 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"folder\", innerFolder_r15)(\"hasLimitedOptions\", ctx_r2.hasLimitedOptions);\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_img_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 60);\n    i0.ɵɵlistener(\"click\", function SingleLibraryComponent_ng_template_11_img_19_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.createFolder());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_img_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 61);\n    i0.ɵɵlistener(\"click\", function SingleLibraryComponent_ng_template_11_img_24_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.openFolder(ctx_r2.folderPath[ctx_r2.folderPath.length - 2]));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵlistener(\"click\", function SingleLibraryComponent_ng_template_11_div_30_Template_div_click_0_listener() {\n      const folder_r19 = i0.ɵɵrestoreView(_r18).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.openFolder(folder_r19));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const folder_r19 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u00A0\", folder_r19.name, \"\\u00A0/ \");\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_ng_container_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 63)(2, \"i\", 64);\n    i0.ɵɵlistener(\"click\", function SingleLibraryComponent_ng_template_11_ng_container_32_Template_i_click_2_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.createFolder());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"input\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SingleLibraryComponent_ng_template_11_ng_container_32_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.folderName, $event) || (ctx_r2.folderName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function SingleLibraryComponent_ng_template_11_ng_container_32_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onCreateFolder());\n    });\n    i0.ɵɵelementStart(5, \"span\", 67);\n    i0.ɵɵtext(6, \"Create\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.folderName);\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_ng_container_33_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" No files found. \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_ng_container_33_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" No files found. \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_ng_container_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 68);\n    i0.ɵɵelement(2, \"img\", 69);\n    i0.ɵɵelementStart(3, \"p\", 70);\n    i0.ɵɵtemplate(4, SingleLibraryComponent_ng_template_11_ng_container_33_ng_container_4_Template, 2, 0, \"ng-container\", 3)(5, SingleLibraryComponent_ng_template_11_ng_container_33_ng_container_5_Template, 2, 0, \"ng-container\", 3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.authService.isStudent);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.authService.isTeacher);\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_ng_container_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 68)(2, \"img\", 71);\n    i0.ɵɵlistener(\"click\", function SingleLibraryComponent_ng_template_11_ng_container_34_Template_img_click_2_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onShowUploadDialog());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 70);\n    i0.ɵɵtext(4, \" Upload your first file! \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_div_35_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-library-file-row-item\", 74);\n    i0.ɵɵlistener(\"fileDeleted\", function SingleLibraryComponent_ng_template_11_div_35_ng_container_2_Template_app_library_file_row_item_fileDeleted_1_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onFileDeleted($event));\n    })(\"libraryChecked\", function SingleLibraryComponent_ng_template_11_div_35_ng_container_2_Template_app_library_file_row_item_libraryChecked_1_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onLibraryChecked($event));\n    })(\"fileUnshared\", function SingleLibraryComponent_ng_template_11_div_35_ng_container_2_Template_app_library_file_row_item_fileUnshared_1_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onFileUnshared($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const libFiles_r23 = ctx.$implicit;\n    const classroom_r24 = i0.ɵɵnextContext(2).classroom;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"showGroupActions\", ctx_r2.showGroupActions)(\"showSharedWithUsers\", ctx_r2.withClassroom)(\"classroom\", classroom_r24)(\"classroomFile\", libFiles_r23)(\"hasLimitedOptions\", ctx_r2.hasLimitedOptions)(\"isSmallScreen\", ctx_r2.isSmallScreen)(\"isInTab\", ctx_r2.isInTab)(\"folders\", ctx_r2.folders)(\"availableActions\", ctx_r2.availableActions)(\"classroomStudents\", ctx_r2.classroomStudents);\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"ul\", 73);\n    i0.ɵɵtemplate(2, SingleLibraryComponent_ng_template_11_div_35_ng_container_2_Template, 2, 10, \"ng-container\", 14);\n    i0.ɵɵpipe(3, \"filter\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const uniqueFiles_r25 = i0.ɵɵnextContext().uniqueFiles;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"height\", ctx_r2.fileListHeight + \"px\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind2(3, 3, ctx_r2.getFilteredFiles(uniqueFiles_r25), i0.ɵɵpureFunction1(6, _c10, ctx_r2.term)));\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 17)(4, \"span\", 18)(5, \"input\", 19);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SingleLibraryComponent_ng_template_11_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.term, $event) || (ctx_r2.term = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"i\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, SingleLibraryComponent_ng_template_11_img_7_Template, 1, 3, \"img\", 21)(8, SingleLibraryComponent_ng_template_11_img_8_Template, 1, 0, \"img\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 23);\n    i0.ɵɵtemplate(10, SingleLibraryComponent_ng_template_11_div_10_Template, 17, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 25)(12, \"div\", 26)(13, \"div\", 27);\n    i0.ɵɵelement(14, \"img\", 28);\n    i0.ɵɵelementStart(15, \"h4\", 29);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 30);\n    i0.ɵɵtemplate(18, SingleLibraryComponent_ng_template_11_div_18_Template, 2, 2, \"div\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, SingleLibraryComponent_ng_template_11_img_19_Template, 1, 0, \"img\", 32);\n    i0.ɵɵelement(20, \"div\", 33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 34)(22, \"div\", 35)(23, \"div\", 36);\n    i0.ɵɵtemplate(24, SingleLibraryComponent_ng_template_11_img_24_Template, 1, 0, \"img\", 37);\n    i0.ɵɵtext(25, \" \\u00A0 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"img\", 38);\n    i0.ɵɵtext(27, \" \\u00A0 \");\n    i0.ɵɵelement(28, \"img\", 39);\n    i0.ɵɵtext(29, \" \\u00A0 \");\n    i0.ɵɵtemplate(30, SingleLibraryComponent_ng_template_11_div_30_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 41);\n    i0.ɵɵtemplate(32, SingleLibraryComponent_ng_template_11_ng_container_32_Template, 7, 1, \"ng-container\", 3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(33, SingleLibraryComponent_ng_template_11_ng_container_33_Template, 6, 2, \"ng-container\", 3)(34, SingleLibraryComponent_ng_template_11_ng_container_34_Template, 5, 0, \"ng-container\", 3)(35, SingleLibraryComponent_ng_template_11_div_35_Template, 4, 8, \"div\", 42);\n  }\n  if (rf & 2) {\n    const uniqueFiles_r25 = ctx.uniqueFiles;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c7, ctx_r2.isInTab));\n    i0.ɵɵadvance();\n    i0.ɵɵclassMapInterpolate1(\"notes-header-title hidden md:block font-2xl opaque-box-rtl \", ctx_r2.extraGradientClass, \" sm:col-4\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.libraryTitle, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.term);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.canShare);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.canUpload);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showGroupActions);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(21, _c8, ctx_r2.isInTab));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.libraryToAdd.folder.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.innerFolders);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.canUpload && ctx_r2.canCreateNewFolder);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.folderPath.length > 1);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.folderPath);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showInput);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.files.length === 0 && !ctx_r2.canUpload);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.files.length === 0 && ctx_r2.canUpload);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getFilteredFiles(uniqueFiles_r25).length > 0);\n  }\n}\nfunction SingleLibraryComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" hi\\n\");\n  }\n}\nexport let SingleLibraryComponent = /*#__PURE__*/(() => {\n  class SingleLibraryComponent {\n    constructor(libraryService, classroomService, generalService, authService, confirmationService, toastService, layoutService, dialogService, userService) {\n      this.libraryService = libraryService;\n      this.classroomService = classroomService;\n      this.generalService = generalService;\n      this.authService = authService;\n      this.confirmationService = confirmationService;\n      this.toastService = toastService;\n      this.layoutService = layoutService;\n      this.dialogService = dialogService;\n      this.userService = userService;\n      this.showHeader = true;\n      this.isMyFiles = false;\n      this.showShare = false;\n      this.StudentIdsToSend = [];\n      this.folderPath = [this.libraryService.rootFolder];\n      this.isDisabled = false;\n      this.isDisabledWholeClass = false;\n      this.isMoveRadioCheked = false;\n      this.moveFileId = 0;\n      this.moveFolderId = 0;\n      this.UserRoles = UserRole;\n      this.role = \"\";\n      this.loggedInUser = {};\n      this.inHomework = false;\n      this.task = {};\n      this.classroomIdToAddLib = 0;\n      this.withClassroom = false;\n      this.canUpload = true;\n      this.canShare = true;\n      this.hasLimitedOptions = false;\n      this.availableActions = [];\n      this.libraryTitle = 'My Files';\n      this.isInTab = false;\n      this.extraGradientClass = 'purple';\n      this.fileListHeight = 0;\n      this.canCreateNewFolder = true;\n      this.subs = new SubSink();\n      this.showFilters = false;\n      this.showGroupActions = false;\n      this.showAddLibrary = false;\n      this.foldersWithFiles = [];\n      this.allLibrariesWithFiles = [];\n      this.title = \"\";\n      this.currentPathLibraries = [];\n      this.innerFolders = [];\n      this.libraryToAdd = {};\n      this.folderName = \"\";\n      this.showInput = false;\n      this.user = {};\n      this.folders = [];\n      this.inClassroom = true;\n      this.showingMine = true;\n      this.files = [];\n      this.filteredFiles = [];\n      this.libsSharedWith = [];\n      this.libsSharedBy = [];\n      this.studentsToShareWith = [];\n      this.deleteFolderConfirmData = {\n        showDialog: false,\n        message: 'Delete folder?'\n      };\n      this.deleteFileConfirmData = {\n        showDialog: false,\n        message: 'Delete File?'\n      };\n      this.deleteMultipleFilesConfirmData = {\n        showDialog: false,\n        message: 'Delete selected files?'\n      };\n      this.checkedLibraryFiles = [];\n      this.isSmallScreen = false;\n      this.currentUploadFolder = {};\n      this.classroomStudentsIds = [];\n    }\n    ngOnInit() {\n      // if (this.libraryService.isPreviewingFromChat()) {\n      //   this.libraryService.setGroupActions(true);\n      // }\n      // set unique student ids...\n      if (this.classroomStudents) {\n        this.classroomStudents = Array.from(new Set(this.classroomStudents.map(student => student.aspUserId))).map(aspUserId => this.classroomStudents.find(student => student.aspUserId === aspUserId));\n        this.classroomStudentsIds = this.classroomStudents.map(student => student.aspUserId);\n      }\n      if (this.classroomStudents && this.classroomStudents.length === 1) {\n        this.setStudentsToShareWith(this.classroomStudents[0].aspUserId);\n      }\n      this.oncollapseNotesLeftSideChanged(true);\n      console.log(this.classroomIdToAddLib);\n      // this.generalService.slideNativeElements(true, this.addLibrary.nativeElement);\n      this.loggedInUser = this.authService.getLoggedInUser();\n      this.role = this.authService.getUserRole();\n      this.items = [{\n        label: 'Add New',\n        icon: 'pi pi-fw pi-plus',\n        command: event => {}\n      }, {\n        label: 'Remove',\n        icon: 'pi pi-fw pi-minus'\n      }];\n      this.subs.sink = this.generalService.deviceKind.subscribe(deviceKind => {\n        this.isSmallScreen = !deviceKind.w768up;\n      });\n      this.subs.sink = this.libraryService.showGroupActions.subscribe(res => {\n        this.showGroupActions = res;\n      });\n      this.subs.sink = this.layoutService.sideMenuHeight.subscribe(res => {\n        // this.leftSide.nativeElement.style.maxHeight = (res - 100 + 'px');\n        // this.mainWrapper.nativeElement.style.height = (res -60 + 'px');\n        // this.classroomWrapper.nativeElement.style.height = (res - extraHeight.wrapper + 'px');\n        // this.scrollHeight = (res - extraHeight.scrollWrapper + 'px');/assets/icons/library/arrow-left.svg\n      });\n      this.subs.sink = this.libraryService.updateFolderListener.subscribe(res => {\n        if (res.folder.folderId > 0) {\n          for (let lib of this.allLibrariesWithFiles) {\n            let found = false;\n            for (let file of lib.libraryFiles) {\n              if (file.fileId == res.libraryFile.fileId) {\n                found = true;\n              }\n            }\n            if (found) {\n              lib.folderId = res.folder.folderId;\n              lib.name = res.folder.name;\n              lib.parent = res.folder.parent;\n            }\n          }\n          this.openFolder(this.libraryToAdd.folder);\n        }\n      });\n      this.initOpenFolderListener();\n      this.getClassRooms();\n      // IRAKLIS SOURCE\n      this.subs.sink = this.libraryService.updateListener.subscribe(res => {\n        if (res) {\n          this.getFoldersWithFiles();\n        }\n      });\n      this.subs.sink = this.libraryService.deleteListener.subscribe(res => {\n        if (res > 0) {\n          for (let lib of this.allLibrariesWithFiles) {\n            let found = false;\n            for (let file of lib.libraryFiles) {\n              if (file.fileId == res) {\n                found = true;\n              }\n            }\n            if (found) {\n              lib.libraryFiles = lib.libraryFiles.filter(el => el.fileId != res);\n            }\n          }\n          this.getFoldersWithFiles();\n        }\n      });\n      this.libraryToAdd = this.libraryService.initializeEmptyLibraryObject();\n      this.user = this.authService.getLoggedInUser();\n      this.role = this.user.role;\n    }\n    ngOnChanges() {\n      this.initFiles();\n      this.getFoldersWithFiles();\n    }\n    ngAfterViewInit() {}\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n      this.libraryService.setGroupActions(false);\n    }\n    openShareDialog() {\n      if (this.checkedLibraryFiles.length === 0) {\n        this.toastService.setShowToastmessage({\n          severity: 'warn',\n          summary: '',\n          detail: 'Please choose at least one file!'\n        });\n        return;\n      }\n      this.libraryService.openDialogWithComponent(this.dialogService, LibraryFileActionsDialogComponent, 400, {\n        action: 'shareWithMultiple',\n        dialogsubTitle: 'Share multiple with...',\n        dialogTitle: 'Share file with...'\n      }, 'allFiles', result => {\n        if (result && result.action === 'shareWithMultiple') {\n          this.shareLibraryFiles(result.map.map);\n        }\n      });\n    }\n    shareMultipleLibraryFiles() {}\n    onLibraryChecked(event) {\n      console.log(event);\n      if (event.checked) {\n        this.checkedLibraryFiles.push(event.file);\n      } else {\n        this.checkedLibraryFiles = this.checkedLibraryFiles.filter(el => el.path != event.file.path);\n      }\n      this.libraryService.checkedLibraryFiles.set(this.checkedLibraryFiles);\n      console.log(this.checkedLibraryFiles);\n    }\n    shareLibraryFiles(map) {\n      map.forEach((users, classroomId) => {\n        let ids = [];\n        let fileIds = [];\n        for (let user of users) {\n          ids.push(user.aspUserId);\n        }\n        for (let lib of this.checkedLibraryFiles) {\n          fileIds.push(lib.fileId);\n        }\n        let send = {\n          fileIds: fileIds,\n          classroomId: classroomId,\n          studentIds: ids\n        };\n        this.sendShareToApi(send);\n      });\n    }\n    sendShareToApi(sendParams) {\n      this.libraryService.shareFileWithClassUsers(sendParams).pipe(take(1)).subscribe(res => {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Library has been sent.'\n        });\n      });\n    }\n    onShowAddLibrary() {\n      this.showAddLibrary = !this.showAddLibrary;\n      this.libraryToAdd.folder = this.folderPath[this.folderPath.length - 1];\n      // this.generalService.slideNativeElements(this.showAddLibrary, this.addLibrary.nativeElement);\n    }\n    deleteFolderSelected(folder) {\n      this.deleteFolder(folder.folderId);\n    }\n    openFolderSelected(folder) {\n      this.openFolder(folder);\n    }\n    getClassRooms() {\n      this.subs.add(this.classroomService.getLMSUserClassrooms(this.loggedInUser.id).subscribe(response => {\n        this.teacherClassrooms = response;\n      }));\n    }\n    // createLibFolder(){\n    //   this.libraryService.createFolder().subscribe( (response) => {\n    //   });\n    // }\n    deleteLibFolder(folderId) {\n      this.libraryService.deleteFolder(folderId).subscribe(response => {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Folder Deleted successfully.'\n        });\n      });\n    }\n    getFileName(path) {\n      if (path) {\n        let n = path.lastIndexOf(\"/\");\n        if (n === -1) n = path.lastIndexOf(\"\\\\\");\n        let result = path.substring(n + 1);\n        return result;\n      }\n      return path;\n    }\n    showSharePopup(fileId) {\n      document.getElementById('lib-file-move-menu-' + fileId).style.display = 'none';\n      document.getElementById('lib-file-share-menu-' + fileId).style.display = 'block';\n      document.getElementById('lib-file-share-menu-' + fileId).style.top = '25%';\n    }\n    // shareLibraryFiles(fileId: number, classroomID: number) {\n    //   let fileIds = []\n    //   fileIds.push(fileId)\n    //   let body: any = {\n    //     fileIds: fileIds,\n    //     classroomId: this.classroomId,\n    //     studentIds: this.StudentIdsToSend\n    //   }\n    //   if (this.StudentIdsToSend.length === 0){\n    //   }\n    //   else{\n    //     this.libraryService.shareFileWithClassUsers(body).pipe(take(1)).subscribe(res => {\n    //     })\n    //   }\n    //   this.ngOnInit();\n    // }\n    onFolderSelect(fileId, folderId) {\n      this.isMoveRadioCheked = true;\n      this.moveFileId = fileId;\n      this.moveFolderId = folderId;\n    }\n    moveFile() {\n      this.moveFileAPI(this.moveFileId, this.moveFolderId);\n      // this.libraryService.moveFileToFolder(this.moveFileId, this.moveFolderId).subscribe( (response) => {\n      //   this.toastService.setShowToastmessage({\n      //     severity: 'success',\n      //     summary: '',\n      //     detail: 'File moved successfully.'\n      //   });\n      // });\n      this.ngOnInit();\n    }\n    // IRAKLIS SOURCE\n    initFiles() {\n      this.filteredFiles = [];\n      let i = 0;\n      for (let folderWithFiles of this.foldersWithFiles) {\n        for (let libraryFile of folderWithFiles.libraryFiles) {\n          libraryFile.dummyId = i;\n          i++;\n          this.filteredFiles.push(libraryFile);\n        }\n      }\n      this.files = this.filteredFiles;\n    }\n    getFoldersWithFiles() {\n      this.initializeFilesWithFolders(this.allLibrariesWithFiles);\n    }\n    initializeFilesWithFolders(res) {\n      this.allLibrariesWithFiles = res;\n      this.folders = [];\n      for (const lib of this.allLibrariesWithFiles) {\n        const folder = {\n          name: lib.name,\n          parent: lib.parent,\n          folderId: lib.folderId\n        };\n        if (!this.folders.some(el => el.name === folder.name && el.parent === folder.parent)) {\n          this.folders.push(folder);\n        }\n      }\n      this.foldersWithFiles = this.libraryService.getLibrariesOnRootFolders(this.allLibrariesWithFiles);\n      this.initFiles();\n      this.getInnerFolders('home');\n    }\n    getInnerFolders(parent) {\n      this.innerFolders = [];\n      let copyFolders = this.allLibrariesWithFiles.filter(el => el.parent == parent);\n      for (let innerFolder of copyFolders) {\n        if (!this.innerFolders.some(el => el.parent == innerFolder.parent && el.name == innerFolder.name)) {\n          this.innerFolders.push(innerFolder);\n        }\n      }\n    }\n    openFolder(folder) {\n      if (!folder) {\n        this.getInnerFolders('home');\n        return;\n      }\n      this.libraryToAdd.folder = folder;\n      console.log(this.libraryToAdd.folder);\n      this.foldersWithFiles = this.libraryService.getLibrariesOfFolder(folder, this.allLibrariesWithFiles);\n      this.initFiles();\n      if (folder.name == 'home') {\n        this.getInnerFolders('home');\n      } else {\n        this.getInnerFolders(folder.name);\n      }\n      this.folderPath = this.libraryService.getFolderPathOfFolder(folder, [], this.folders);\n      this.folderPath.unshift(this.libraryService.rootFolder);\n      this.libraryToAdd.folder = this.folderPath[this.folderPath.length - 1];\n    }\n    deleteFolder(id) {\n      const folderToDelete = this.innerFolders.filter(el => el.folderId === id);\n      this.libraryService.setFolderToDelete(folderToDelete[0]);\n      this.deleteFolderConfirmData.showDialog = true;\n      this.deleteFolderConfirmData.message = 'Delete the folder \"' + folderToDelete[0].name + '\"?';\n      // this.libraryService.deleteFolder(id).pipe(take(1)).subscribe(res => {\n      //   this.innerFolders = this.innerFolders.filter(el => el.folderId != id)\n      //   console.log(this.innerFolders)\n      // })\n    }\n    onRejectFolderDelete(event) {\n      this.libraryService.setFolderToDelete({});\n      this.deleteFolderConfirmData.showDialog = false;\n    }\n    onAcceptFolderDelete(event) {\n      this.libraryService.deleteFolder(this.libraryService.getFolderToDelete().folderId).pipe(take(1)).subscribe(res => {\n        this.innerFolders = this.innerFolders.filter(el => el.folderId != this.libraryService.getFolderToDelete().folderId);\n        console.log(this.innerFolders);\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Folder has been deleted.'\n        });\n        this.removeFolderByFolderId(this.libraryService.getFolderToDelete());\n        this.libraryService.setFolderToDelete({});\n        this.libraryService.setUpdateListener(true);\n      });\n      this.deleteFolderConfirmData.showDialog = false;\n    }\n    onTagChoosen(event) {\n      let isLevel = event.isLevel;\n      if (event.checked) {\n        if (event.applyToAll) {\n          for (let file of this.libraryToAdd.libraryFiles) {\n            let arrLibrary = isLevel ? file.levels : file.categories;\n            arrLibrary.push(event.tag);\n          }\n        } else {\n          let file = this.libraryToAdd.libraryFiles[event.file.index];\n          let arrLibrary = isLevel ? file.levels : file.categories;\n          arrLibrary.push(event.tag);\n        }\n      } else {\n        if (event.applyToAll) {\n          for (let file of this.libraryToAdd.libraryFiles) {\n            let arrLibrary = isLevel ? file.levels : file.categories;\n            let index2 = arrLibrary.findIndex(el => el == event.tag);\n            arrLibrary.splice(index2, 1);\n          }\n        } else {\n          let file = this.libraryToAdd.libraryFiles[event.file.index];\n          let arrLibrary = isLevel ? file.levels : file.categories;\n          let index2 = arrLibrary.findIndex(el => el == event.tag);\n          arrLibrary.splice(index2, 1);\n        }\n      }\n    }\n    onCreateFolder() {\n      this.libraryService.getUserCreatedLibFolders().pipe(take(1)).subscribe(res => {\n        let found = false;\n        for (let r of res) {\n          if (r.parent == this.folderPath[this.folderPath.length - 1].name && r.name == this.folderName) {\n            found = true;\n            break;\n          }\n        }\n        if (!found) {\n          this.showInput = false;\n          let newFolder = {\n            parent: this.folderPath[this.folderPath.length - 1].name,\n            name: this.folderName\n          };\n          this.libraryService.createFolder(newFolder).pipe(take(1)).subscribe(res => {\n            this.innerFolders.push(res);\n            this.allLibrariesWithFiles.push({\n              parent: this.folderPath[this.folderPath.length - 1].name,\n              name: this.folderName,\n              libraryFiles: [],\n              createdBy: this.user,\n              folderId: 10000\n            });\n            this.toastService.setShowToastmessage({\n              severity: 'success',\n              summary: '',\n              detail: 'Folder created.'\n            });\n            this.folderName = '';\n            this.libraryService.setUpdateListener(true);\n          });\n        } else {\n          this.toastService.setShowToastmessage({\n            severity: 'warn',\n            summary: '',\n            detail: 'Folder already exists.'\n          });\n        }\n      });\n    }\n    createFolder() {\n      this.showInput = !this.showInput;\n    }\n    onShowUploadDialog() {\n      const targetElementName = this.libraryService.isPreviewingFromChat() ? 'notifications-overview' : 'mainContentColumn';\n      const myElement = document.getElementById(targetElementName);\n      // this.libraryToAdd = this.libraryService.initializeEmptyLibraryObject();\n      // this.libraryToAdd.libraryFiles = [];\n      this.userService.setNewFileUpload({});\n      console.log(myElement.getBoundingClientRect().width);\n      const dialogWidth = myElement.getBoundingClientRect().width - 10;\n      this.generalService.openDialogWithComponent(this.dialogService, LibraryFileActionsDialogComponent, dialogWidth.toFixed(), {\n        action: 'upload',\n        dialogsubTitle: '',\n        dialogTitle: 'Upload to library...',\n        folder: this.libraryToAdd.folder,\n        libraryToAdd: this.libraryToAdd\n      }, targetElementName, result => {\n        if (result && result.action === 'upload') {\n          console.log(result);\n          this.libraryToAdd.libraryFiles = result.libraryFiles;\n          this.onSubmit();\n          // this.moveFile(result.map.map);\n        }\n      }, myElement.getBoundingClientRect().height - 90 + 'px');\n    }\n    onSubmit() {\n      if (this.libraryToAdd.libraryFiles.length > 0) {\n        this.libraryToAdd.classroom = this.classroomIdToAddLib;\n        let toSend = {\n          folderId: this.libraryToAdd.folder.folderId,\n          classroomId: this.classroomIdToAddLib,\n          name: this.libraryToAdd.folder.name,\n          parent: this.libraryToAdd.folder.parent,\n          libFiles: this.libraryToAdd.libraryFiles,\n          StudentIds: this.studentsToShareWith\n        };\n        // return;\n        if (this.classroomIdToAddLib > 0) {\n          this.libraryService.uploadClassroomFilesToFolder(toSend).pipe(take(1)).subscribe(res => {\n            this.toastService.setShowToastmessage({\n              severity: 'success',\n              summary: '',\n              detail: 'Your files have been uploaded to Classroom library.'\n            });\n            if (this.authService.isStudent) {\n              console.log(res);\n              res.fileIds.forEach((fileId, index) => {\n                this.shareWithTeacher(fileId);\n              });\n            }\n            this.userService.setUploadFiles(true);\n            this.mapLibraryToAddFiles(res);\n            this.e();\n          });\n        } else {\n          this.libraryService.uploadFilesToFolder(toSend).pipe(take(1)).subscribe(res => {\n            this.toastService.setShowToastmessage({\n              severity: 'success',\n              summary: '',\n              detail: 'Your files have been uploaded to library folder: ' + toSend.name\n            });\n            this.currentUploadFolder = this.innerFolders.filter(el => el.folderId === toSend.folderId)[0];\n            this.userService.setUploadFiles(true);\n            this.mapLibraryToAddFiles(res);\n            this.e();\n            console.log(this.currentUploadFolder);\n            // this.libraryService.setOpenFolderListener(currentFolder[0]);\n            // this.libraryService.setUpdateFolderListener(toSend.libFiles[0], currentFolder[0])\n          });\n        }\n      } else {\n        this.toastService.setShowToastmessage({\n          severity: 'warn',\n          summary: '',\n          detail: 'Please add files'\n        });\n      }\n    }\n    shareWithTeacher(fileId) {\n      let send = {\n        fileIds: [fileId],\n        classroomId: this.libraryService.getCurrentSelectedClassroom().id,\n        studentIds: [this.libraryService.getCurrentSelectedClassroom().teacher?.aspUserId]\n      };\n      this.sendShareToApi(send);\n    }\n    e() {\n      this.filteredFiles.concat(this.libraryToAdd.libraryFiles);\n      this.foldersWithFiles.push({\n        folderId: this.libraryToAdd.folder.folderId,\n        name: this.libraryToAdd.folder.name,\n        parent: this.libraryToAdd.folder.parent,\n        libraryFiles: this.libraryToAdd.libraryFiles,\n        createdBy: this.user\n      });\n      // this.getFoldersWithFiles();\n      // this.showAddLibrary = !this.showAddLibrary\n      // this.generalService.slideNativeElements(this.showAddLibrary, this.addLibrary.nativeElement);\n      console.log(this.filteredFiles);\n      this.allLibrariesWithFiles.push({\n        folderId: this.libraryToAdd.folder.folderId,\n        name: this.libraryToAdd.folder.name,\n        parent: this.libraryToAdd.folder.parent,\n        libraryFiles: this.libraryToAdd.libraryFiles,\n        createdBy: this.user\n      });\n      // this.files.push(this.files[this.files.length - 1]);\n      this.initFiles();\n      this.libraryToAdd = this.libraryService.initializeEmptyLibraryObject();\n      // this.libraryToAdd.libraryFiles = [];\n      // this.libraryService.setUpdateListener(true)\n      this.userService.setNewFileUpload({});\n    }\n    // addes also fileId to a new uploaded file in the library\n    mapLibraryToAddFiles(res) {\n      this.libraryToAdd.libraryFiles = res.libFiles.map((file, index) => ({\n        ...file,\n        fileId: res.fileIds[index]\n      }));\n    }\n    uploadFinished(event) {\n      console.log(event);\n      this.libraryToAdd.libraryFiles?.push({\n        path: event.filePath.dbPath,\n        levels: [],\n        categories: [],\n        name: event.filePath.dbPath,\n        fileId: event.filePath.dbPath\n      });\n    }\n    /**\n    This method is triggered when a file is deleted from the classroom files.\n    It filters out the deleted file from the libraryFiles array of each folder in the allLibrariesWithFiles array.\n    @param {any} event - The event object containing the ID of the deleted file.\n    @returns {void}\n    */\n    onFileDeleted(event) {\n      this.deleteFileConfirmData.showDialog = true;\n      this.deleteFileConfirmData.message = 'Delete file \"' + event.name + '\"?';\n      // this.allLibrariesWithFiles = this.allLibrariesWithFiles..filter(file => file.fileId !== libFile.fileId);\n      this.libraryService.setLibraryFile(event);\n    }\n    deleteFile(fileId) {\n      this.subs.add(this.libraryService.delete(fileId).subscribe(res => {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Files has been deleted successfully.'\n        });\n      }));\n    }\n    moveMultipleFiles() {\n      if (this.checkedLibraryFiles.length === 0) {\n        this.toastService.setShowToastmessage({\n          severity: 'warn',\n          summary: '',\n          detail: 'Please choose at least one file!'\n        });\n        return;\n      }\n      this.libraryService.openDialogWithComponent(this.dialogService, LibraryFileActionsDialogComponent, 400, {\n        action: 'moveMultiple',\n        dialogsubTitle: 'Move Files to folder...',\n        dialogTitle: 'Choose folder...',\n        folders: this.folders\n      }, null, result => {\n        if (result && result.action === 'moveMultiple') {\n          console.log(result);\n          console.log(this.checkedLibraryFiles);\n          this.checkedLibraryFiles.forEach(libFile => {\n            this.moveFileAPI(libFile.fileId, result.folder.folderId);\n            this.libraryService.setUpdateListener(true);\n          });\n          // this.moveFile(result.map.map);\n        }\n      });\n    }\n    deleteMultipleFiles() {\n      if (this.checkedLibraryFiles.length === 0) {\n        this.toastService.setShowToastmessage({\n          severity: 'warn',\n          summary: '',\n          detail: 'Please choose at least one file!'\n        });\n        return;\n      }\n      this.deleteMultipleFilesConfirmData.showDialog = true;\n      this.deleteMultipleFilesConfirmData.message = 'Delete the selected files ?';\n    }\n    onAcceptFileDeleteMultiple(event) {\n      const libFiles = this.checkedLibraryFiles;\n      libFiles.forEach(libFile => {\n        this.filterDelteFromLibraryFiles(libFile.fileId);\n        this.deleteFileAPI(libFile);\n      });\n      this.deleteFileConfirmData.showDialog = false;\n      this.deleteMultipleFilesConfirmData.showDialog = false;\n      this.checkedLibraryFiles = [];\n    }\n    filterDelteFromLibraryFiles(...fileIds) {\n      if (fileIds.length === 1) {\n        this.filteredFiles = this.filteredFiles.filter(el => el.fileId !== fileIds[0]);\n      } else {\n        this.filteredFiles = this.filteredFiles.filter(el => !fileIds.includes(el.fileId));\n      }\n    }\n    onRejectFileDelete(event) {\n      this.deleteFileConfirmData.showDialog = false;\n      this.deleteMultipleFilesConfirmData.showDialog = false;\n      this.libraryService.setLibraryFile({});\n    }\n    onAcceptFileDelete(event) {\n      const libFile = this.libraryService.getLibraryFile();\n      this.filterDelteFromLibraryFiles(libFile.fileId);\n      this.deleteFileAPI(libFile);\n    }\n    deleteFileAPI(libFile) {\n      this.subs.add(this.libraryService.delete(libFile.fileId).subscribe(res => {\n        if (res) {\n          this.deleteFileConfirmData.showDialog = false;\n          this.toastService.setShowToastmessage({\n            severity: 'success',\n            summary: '',\n            detail: 'Files has been deleted successfully..'\n          });\n          this.removeLibraryFileByFileId(libFile);\n        }\n      }));\n    }\n    removeLibraryFileByFileId(libFile) {\n      this.allLibrariesWithFiles = this.allLibrariesWithFiles.map(file => {\n        const filteredLibraryFiles = file.libraryFiles.filter(libraryFile => libraryFile.fileId !== libFile.fileId);\n        return {\n          ...file,\n          libraryFiles: filteredLibraryFiles\n        };\n      });\n    }\n    removeFolderByFolderId(folder) {\n      this.allLibrariesWithFiles = this.allLibrariesWithFiles.filter(file => {\n        return file.folderId !== folder.folderId;\n      });\n    }\n    moveFileAPI(fileId, moveFolderId) {\n      this.subs.add(this.libraryService.moveFileToFolder(fileId, moveFolderId).subscribe(response => {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'File moved successfully.'\n        });\n        this.checkedLibraryFiles = [];\n        this.showGroupActions = false;\n        // this.openFolder(this.libraryToAdd.folder);\n        this.libraryService.setUpdateListener(true);\n      }));\n    }\n    oncollapseNotesLeftSideChanged(event) {\n      this.isLeftsideCollapsed = event;\n    }\n    setGroupActions() {\n      this.showGroupActions = !this.showGroupActions;\n      // this.libraryService.setGroupActions(this.showGroupActions);\n      setTimeout(() => {\n        // this.libraryService.setGroupActions(this.showGroupActions);\n      }, 300);\n    }\n    onTabChangeFilterFilesPerStudent(event) {\n      if (event.index > 0) {\n        this.studentsToShareWith = [];\n        const currentStudent = this.classroomStudents[event.index - 1];\n        this.setStudentsToShareWith(this.classroomStudents[event.index - 1].aspUserId);\n        this.filteredFiles = this.files.filter(folder => folder.sharedWith.id === currentStudent.aspUserId);\n      } else {\n        this.filteredFiles = this.files;\n        this.setStudentsToShareWith();\n      }\n    }\n    setStudentsToShareWith(studentId) {\n      this.studentsToShareWith = studentId ? [studentId] : this.classroomStudents.map(user => user.aspUserId);\n    }\n    getFilteredFiles(unique = false) {\n      if (!unique) {\n        return this.filteredFiles;\n      } else {\n        const uniqueFilteredFiles = this.filteredFiles = this.filteredFiles.filter((file, index, self) => index === self.findIndex(f => f.fileId === file.fileId));\n        return uniqueFilteredFiles;\n      }\n    }\n    onFileUnshared(event) {\n      console.log(event);\n      this.libraryService.setUpdateListener(true);\n    }\n    initOpenFolderListener() {\n      this.subs.sink = this.libraryService.libraryUpdatedListener.subscribe(res => {\n        if (res && this.currentUploadFolder) {\n          if (!this.generalService.isNullishObject(this.currentUploadFolder)) {\n            this.openFolder(this.currentUploadFolder);\n            console.log(this.libraryToAdd.folder);\n          }\n        }\n      });\n    }\n    static #_ = this.ɵfac = function SingleLibraryComponent_Factory(t) {\n      return new (t || SingleLibraryComponent)(i0.ɵɵdirectiveInject(i1.LibraryService), i0.ɵɵdirectiveInject(i2.ClassroomService), i0.ɵɵdirectiveInject(i3.GeneralService), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i5.ConfirmationService), i0.ɵɵdirectiveInject(i6.ToastService), i0.ɵɵdirectiveInject(i7.LayoutService), i0.ɵɵdirectiveInject(i8.DialogService), i0.ɵɵdirectiveInject(i9.UserService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SingleLibraryComponent,\n      selectors: [[\"app-single-library\"]],\n      viewQuery: function SingleLibraryComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n          i0.ɵɵviewQuery(_c1, 7);\n          i0.ɵɵviewQuery(_c2, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.addLibrary = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.leftSide = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.mainWrapper = _t.first);\n        }\n      },\n      inputs: {\n        showHeader: \"showHeader\",\n        inHomework: \"inHomework\",\n        task: \"task\",\n        classroomIdToAddLib: \"classroomIdToAddLib\",\n        classroomStudents: \"classroomStudents\",\n        withClassroom: \"withClassroom\",\n        canUpload: \"canUpload\",\n        canShare: \"canShare\",\n        hasLimitedOptions: \"hasLimitedOptions\",\n        availableActions: \"availableActions\",\n        leftSideHeight: \"leftSideHeight\",\n        libraryTitle: \"libraryTitle\",\n        isInTab: \"isInTab\",\n        extraGradientClass: \"extraGradientClass\",\n        fileListHeight: \"fileListHeight\",\n        canCreateNewFolder: \"canCreateNewFolder\",\n        allLibrariesWithFiles: \"allLibrariesWithFiles\",\n        title: \"title\"\n      },\n      features: [i0.ɵɵProvidersFeature([DialogService, FilterPipe]), i0.ɵɵNgOnChangesFeature],\n      decls: 15,\n      vars: 15,\n      consts: [[\"mainWrapper\", \"\"], [\"classInfoGeneralTemplate\", \"\"], [\"classInfoStudentTemplate\", \"\"], [4, \"ngIf\"], [1, \"notes\"], [1, \"modal\", \"p-0\", \"max-w-12rem\", 3, \"ngClass\"], [\"headerBackgroundImage\", \"/assets/images/dashboard/calendar/calendar-dialog-lesson-info-arranged.png\", \"blockClass\", \"border-radius-bottom-10\", \"headerBlockClass\", \"py-1 border-round-lg bg-cover sticky top-0 z-5 \", \"containerClass\", \"block-gradient-reverse px-3 py-2 relative\", 3, \"closeDialogEvent\", \"header\", \"headerClass\", \"headerTextClass\", \"showCloseDialogIcon\"], [1, \"block-gradient\", \"p-3\"], [\"dialogKey\", \"key1\", \"headerClass\", \"text-center flex align-items-center flex-column bg-white\", \"acceptBtnLabel\", \"Yes\", \"rejectBtnLabel\", \"No\", 3, \"rejectSelected\", \"confirmSelected\", \"rejectBtnIcon\", \"confirmMessage\"], [\"dialogKey\", \"key2\", \"headerClass\", \"text-center flex align-items-center flex-column bg-white\", \"acceptBtnLabel\", \"Yes\", \"rejectBtnLabel\", \"No\", 3, \"rejectSelected\", \"confirmSelected\", \"rejectBtnIcon\", \"confirmMessage\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"w-full\", 3, \"onChange\", \"scrollable\"], [\"header\", \"All\"], [\"id\", \"classroom-info\", 1, \"\"], [4, \"ngFor\", \"ngForOf\"], [3, \"header\"], [1, \"notes-header\", \"mb-2\", \"gap-3\", \"lg:flex\", 3, \"ngClass\"], [1, \"flex\", \"align-items-center\", \"justify-content-end\", \"notes-header-actions\", \"gap-2\", 2, \"margin-right\", \"5px\"], [1, \"p-input-icon-right\", \"w-full\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Search\", 1, \"h-2rem\", \"w-full\", \"input-blue\", \"gradient-blue\", \"rounded\", 3, \"ngModelChange\", \"ngModel\"], [1, \"pi\", \"pi-search\", \"text-primary\"], [\"title\", \"Group Actions\", \"src\", \"/assets/icons/library/group-actions.svg\", \"class\", \"hvr-grow pointer\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"title\", \"Upload File\", \"src\", \"/assets/icons/library/add-lib.svg\", \"class\", \"add hvr-grow\", 3, \"click\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\"], [\"class\", \"note-menu-row block-gradient border-round-xl font-sm my-2 p-1 px-3 gap-2\", 4, \"ngIf\"], [1, \"my-folders\"], [1, \"flex\", \"flex-row\", \"relative\", \"z-1\"], [1, \"flex\", \"flex-column\", \"p-4\", \"my-folder\", \"align-items-center\", 3, \"ngClass\"], [\"src\", \"/assets/icons/my-folder.svg\", \"width\", \"40\"], [1, \"text-primary\", \"m-0\", \"font-base\", \"text-center\", \"font-semibold\", \"pt-1\", \"capitalize\"], [1, \"flex\", \"flex-row\", \"gap-4\", \"ml-4\", \"align-items-center\", \"horizontal-scroll-menu\", \"px-3\"], [\"class\", \"flex flex-column relative\", 4, \"ngFor\", \"ngForOf\"], [\"title\", \"Create new subfolder\", \"class\", \"ml-auto px-3 pointer hvr-grow\", \"src\", \"/assets/icons/folder-add.svg\", \"width\", \"60\", 3, \"click\", 4, \"ngIf\"], [1, \"inner-folders\"], [1, \"folder-path\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"folder-nav-buttons\"], [\"src\", \"/assets/icons/arr-left.svg\", \"height\", \"18\", \"class\", \"pointer\", 3, \"click\", 4, \"ngIf\"], [\"src\", \"/assets/icons/folder-start.png\"], [\"src\", \"/assets/icons/arr-right.svg\"], [\"class\", \"link-main-color font-sm capitalize\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"ml-auto\"], [\"class\", \"allFiles\", \"id\", \"allFiles\", 4, \"ngIf\"], [\"title\", \"Group Actions\", \"src\", \"/assets/icons/library/group-actions.svg\", 1, \"hvr-grow\", \"pointer\", 3, \"click\", \"ngClass\"], [\"title\", \"Upload File\", \"src\", \"/assets/icons/library/add-lib.svg\", 1, \"add\", \"hvr-grow\", 3, \"click\"], [1, \"note-menu-row\", \"block-gradient\", \"border-round-xl\", \"font-sm\", \"my-2\", \"p-1\", \"px-3\", \"gap-2\"], [1, \"note-menu-col\"], [1, \"note-menu-icon\"], [\"src\", \"/assets/icons/library/share-sm-icon.svg\", \"height\", \"14\"], [1, \"note-menu-text\", \"link-main-color\", 3, \"click\"], [1, \"note-menu-col\", 3, \"click\"], [\"src\", \"/assets/icons/library/move.svg\", \"height\", \"14\"], [1, \"note-menu-text\", \"link-main-color\"], [\"src\", \"/assets/icons/library/delete.svg\", \"height\", \"14\"], [\"class\", \"send\", 4, \"ngIf\"], [1, \"send\"], [3, \"close\", \"checkedList\"], [1, \"main-color-button\", \"share-btn\", 3, \"click\"], [1, \"flex\", \"flex-column\", \"relative\"], [3, \"deleteFolder\", \"openFolder\", \"folder\", \"hasLimitedOptions\"], [\"title\", \"Create new subfolder\", \"src\", \"/assets/icons/folder-add.svg\", \"width\", \"60\", 1, \"ml-auto\", \"px-3\", \"pointer\", \"hvr-grow\", 3, \"click\"], [\"src\", \"/assets/icons/arr-left.svg\", \"height\", \"18\", 1, \"pointer\", 3, \"click\"], [1, \"link-main-color\", \"font-sm\", \"capitalize\", 3, \"click\"], [1, \"flex\", \"w-auto\", \"align-items-center\"], [1, \"pi\", \"pi-times-circle\", \"mr-2\", \"pointer\", \"icon-times-blue\", 3, \"click\"], [\"type\", \"text\", \"pinputtext\", \"\", \"placeholder\", \"Name your folder...\", 1, \"h-2rem\", \"input-blue\", \"gradient-blue\", \"rounded\", \"btn-right\", \"mr-2\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"button\", \"pbutton\", \"\", \"pripple\", \"\", \"label\", \"Search\", 1, \"p-element\", \"p-button\", \"p-button-xs\", \"p-component\", \"btn-gradient-blue\", 2, \"height\", \"32px\", 3, \"click\"], [1, \"p-button-label\"], [1, \"flex\", \"flex-column\", \"gap-1\", \"align-items-center\", \"justify-content-center\"], [\"src\", \"/assets/icons/library/file_manager_emp.png\", \"alt\", \"file manager empty icon\", 1, \"add\", \"empty\", \"hvr-grow\"], [1, \"font-base\", \"max-w-14rem\", \"text-center\"], [\"src\", \"/assets/icons/library/add-lib.svg\", 1, \"add\", \"lg\", \"hvr-grow\", 3, \"click\"], [\"id\", \"allFiles\", 1, \"allFiles\"], [1, \"library-file-list\"], [\"appendDialogActionsTo\", \"allFiles\", 3, \"fileDeleted\", \"libraryChecked\", \"fileUnshared\", \"showGroupActions\", \"showSharedWithUsers\", \"classroom\", \"classroomFile\", \"hasLimitedOptions\", \"isSmallScreen\", \"isInTab\", \"folders\", \"availableActions\", \"classroomStudents\"]],\n      template: function SingleLibraryComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵtemplate(0, SingleLibraryComponent_ng_container_0_Template, 2, 1, \"ng-container\", 3)(1, SingleLibraryComponent_ng_container_1_Template, 2, 1, \"ng-container\", 3)(2, SingleLibraryComponent_ng_container_2_Template, 2, 1, \"ng-container\", 3);\n          i0.ɵɵelementStart(3, \"div\", 4, 0)(5, \"div\", 5)(6, \"app-block-viewer\", 6);\n          i0.ɵɵlistener(\"closeDialogEvent\", function SingleLibraryComponent_Template_app_block_viewer_closeDialogEvent_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onShowAddLibrary());\n          });\n          i0.ɵɵelement(7, \"div\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\");\n          i0.ɵɵtemplate(9, SingleLibraryComponent_ng_container_9_Template, 2, 4, \"ng-container\", 3)(10, SingleLibraryComponent_ng_container_10_Template, 6, 6, \"ng-container\", 3)(11, SingleLibraryComponent_ng_template_11_Template, 36, 23, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(13, SingleLibraryComponent_ng_template_13_Template, 1, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.deleteFolderConfirmData.showDialog);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.deleteFileConfirmData.showDialog);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.deleteMultipleFilesConfirmData.showDialog);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(13, _c3, !ctx.showAddLibrary));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"header\", \"Upload files in folder \" + ctx.libraryToAdd.folder.name)(\"headerClass\", \"justify-content-center my-0\")(\"headerTextClass\", \"font-base font-semibold justify-content-center capitalize\")(\"showCloseDialogIcon\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassMapInterpolate1(\"block-gradient-reverse \", ctx.extraGradientClass, \" py-2 md:py-0 border-round-xl px-1\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.withClassroom || ctx.authService.isStudent || ctx.classroomStudents.length === 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.classroomStudents && ctx.withClassroom && ctx.authService.isTeacher && ctx.classroomStudents.length > 1);\n        }\n      },\n      dependencies: [i10.NgClass, i10.NgForOf, i10.NgIf, i10.NgTemplateOutlet, i11.ClassroomsCheckListComponent, i12.ConfirmDialogComponent, i13.BlockViewerComponent, i14.TabView, i14.TabPanel, i15.DefaultValueAccessor, i15.NgControlStatus, i15.NgModel, i16.LibraryFileRowItemComponent, i17.SingleLibraryFolderItemComponent, i18.FilterPipe],\n      styles: [\".notes[_ngcontent-%COMP%]{background-color:#fff;padding:10px 0;border-radius:28px;font-size:15px;position:relative}.notes[_ngcontent-%COMP%]   .notes-right-side[_ngcontent-%COMP%]{width:100%;box-sizing:border-box;border-radius:28px;transition:width .3s ease-in-out;height:100%}@media only screen and (min-width: 992px){.notes[_ngcontent-%COMP%]   .notes-right-side[_ngcontent-%COMP%]{width:75%}}@media only screen and (min-width: 992px){.notes[_ngcontent-%COMP%]   .notes-right-side.expanded[_ngcontent-%COMP%]{width:97%}}.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]{width:100%;padding:4px 10px;border-radius:12px;transition:width .3s ease-in-out;box-shadow:0 3px 6px #00000029;box-sizing:border-box;align-items:center;height:100%;top:86px;background-color:#fff}@media only screen and (min-width: 992px){.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]{width:25%}}@media only screen and (min-width: 992px){.notes[_ngcontent-%COMP%]   .notes-left-side.collapsed[_ngcontent-%COMP%]{width:3%}}.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]   .link-main-color[_ngcontent-%COMP%]{width:150px;text-align:center;border-radius:50px}.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]   .classroom-title[_ngcontent-%COMP%]{padding:10px}.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]   .classroom-title.one-line[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]   .seperator[_ngcontent-%COMP%]{width:100%;height:1px;background-color:#d9ddf0;margin:3px 0}.tabs[_ngcontent-%COMP%]{position:relative;height:35rem}@media only screen and (min-width: 768px){.tabs[_ngcontent-%COMP%]{height:31rem}}@media only screen and (min-width: 768px) and (max-height: 768px){.tabs[_ngcontent-%COMP%]{height:28rem}}@media only screen and (max-width: 768px){.tabs[_ngcontent-%COMP%]{height:40rem}}.tab[_ngcontent-%COMP%]{float:left;width:48%;margin-right:1%;margin-left:1%}@media only screen and (min-width: 768px){.tab[_ngcontent-%COMP%]{margin-top:-2px}}.tab-label[_ngcontent-%COMP%]{position:relative;display:block;line-height:2.75em;height:3.7em;padding:0 1.318em;cursor:pointer;top:0;width:100%;transition:all .25s;border:1px solid #96a3e8;border-radius:31.5px;justify-content:center}@media only screen and (min-width: 768px){.tab-label[_ngcontent-%COMP%]{height:4em;justify-content:start}}.tab-content[_ngcontent-%COMP%]{height:100%;position:absolute;z-index:1;top:4.5em;left:0;color:#2c3e50;opacity:0}@media only screen and (max-width: 768px){.tab-content[_ngcontent-%COMP%]{top:4.5em;height:max-content}}.tab-switch[_ngcontent-%COMP%]:checked + .tab-label[_ngcontent-%COMP%]{background:#fff;color:#fff;border-bottom:0;border-right:.125rem solid #fff;border:none;transition:all .35s;z-index:1;opacity:1;background-image:linear-gradient(180deg,#9280fa,#8d7cf8,#8779f6,#8275f5,#7d72f3 44%,#736af0 56%,#6c68ee,#6664ed,#6161ea,#595ee8);border-radius:31.5px;width:100%}[_nghost-%COMP%]     .p-dropdown:not(.p-disabled):hover{border:2px solid rgb(228,185,84)}  .no-label>.p-button-label{display:none}input[type=file][_ngcontent-%COMP%]{visibility:hidden;width:10px}input[type=date][_ngcontent-%COMP%]{position:relative;padding:10px}input[type=date][_ngcontent-%COMP%]::-webkit-calendar-picker-indicator{color:transparent;background:none;z-index:1}input[type=date][_ngcontent-%COMP%]:before{color:transparent;background:none;display:block;font-family:FontAwesome;content:\\\"\\\\f073\\\";width:20px;height:25px;position:absolute;top:12px;right:12px;color:#999}.image-preview[_ngcontent-%COMP%]{height:10rem;margin:1rem 0;border-radius:50%;border:3px solid var(--main-color)}.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{height:100%}[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-header{padding:clamp(.38rem,.47vw + .28rem,.75rem)}[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-items .p-dropdown-item{margin-bottom:clamp(.38rem,.47vw + .28rem,.75rem)}.country-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:clamp(.75rem,.31vw + .69rem,1rem);font-size:clamp(.88rem,.16vw + .84rem,1rem)}.country-item[_ngcontent-%COMP%]   .country-name[_ngcontent-%COMP%]{white-space:pre-line}.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;display:inline-block;vertical-align:middle}.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;clip-path:circle()}.country-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:32px;margin-right:.5rem}.country-item[_ngcontent-%COMP%]   img.smaller[_ngcontent-%COMP%]{width:16px}.info-element[_ngcontent-%COMP%]{padding:10px 0}.note[_ngcontent-%COMP%]{border-radius:40px;border:1px solid #eaf0f5;margin-top:2px;position:relative}.note[_ngcontent-%COMP%]:before{pointer-events:none;content:\\\"\\\";position:absolute;inset:0;border-radius:30px;padding:2px}.note.isFavNote[_ngcontent-%COMP%]{background-image:linear-gradient(90deg,#3874f5,#6d6beb 29%,#9167e0 53%,#aa60d2 68%,#bd59c5 77%,#ca53b4 84%,#d54da6 89%,#dc5096,#e15188 97%,#e3547a)}.note.isFavNote[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]{color:#fff}.note.isFavNote[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .note.isFavNote[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]{border:1px solid white}.note.isFavNote[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .note.isFavNote[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%]{background-color:#fff}.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]{cursor:pointer;display:flex;align-items:center;color:var(--main-color)}.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .section-arrow[_ngcontent-%COMP%]{width:40px}.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;width:100%}@media screen and (max-width: 768px){.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;margin-left:20px}}.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%]{margin-left:20px}@media screen and (max-width: 768px){.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%]{margin-left:0}}.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%]   .note-dates[_ngcontent-%COMP%]{display:flex;margin-top:10px}@media screen and (max-width: 768px){.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%]   .note-dates[_ngcontent-%COMP%]{flex-direction:column}}.note-info[_ngcontent-%COMP%]{display:flex;flex-direction:column}@media only screen and (max-width: 768px){.note-info[_ngcontent-%COMP%]{margin-top:0}}.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]{display:flex}@media only screen and (max-width: 768px){.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]{justify-content:end}}.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]{display:flex;align-items:center;margin-right:10px;margin-left:auto}@media only screen and (max-width: 768px){.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]{margin-left:0}}.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-icon[_ngcontent-%COMP%], .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-text[_ngcontent-%COMP%]{margin-left:4px;cursor:pointer}@media screen and (max-width: 768px){.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-icon[_ngcontent-%COMP%], .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-text[_ngcontent-%COMP%]{margin-left:0}}.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-icon[_ngcontent-%COMP%]{width:20px;display:flex;justify-content:center}.note-info[_ngcontent-%COMP%]   .note-info-bottom[_ngcontent-%COMP%]{display:flex;justify-content:end;text-align:right;margin-right:10px;font-size:.725rem;color:#2d3b8e}.note-content[_ngcontent-%COMP%]{overflow:hidden;transition:height .3s ease-out}.note-text-content[_ngcontent-%COMP%]{padding:20px 30px}.update-button[_ngcontent-%COMP%]{padding:5px 30px;background-color:var(--light-purple);text-align:center;border-radius:10px;color:#fff;margin-top:15px;cursor:pointer}.update-button[_ngcontent-%COMP%]:hover{background-color:var(--main-color)}.section-arrow[_ngcontent-%COMP%]{overflow:hidden;transition-duration:.2s;transition-property:transform;cursor:pointer}.note-burger[_ngcontent-%COMP%], .note-burger-group[_ngcontent-%COMP%]{position:relative;border-radius:50%;min-width:36px;min-height:36px;border:2px solid #6563ec;display:flex;align-items:center;box-sizing:border-box;justify-content:center;cursor:pointer}.note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%]{width:4px;height:4px;border-radius:50%;background-color:#6563ec;margin:2px}.note-burger[_ngcontent-%COMP%]   .inner-circle-group[_ngcontent-%COMP%], .note-burger-group[_ngcontent-%COMP%]   .inner-circle-group[_ngcontent-%COMP%]{width:34px;height:34px;border-radius:50%;background-color:transparent}.note-menu[_ngcontent-%COMP%]{width:300px;position:absolute;right:30px;top:69px;border-radius:12px;border:1px solid var(--main-color);box-sizing:border-box;transition-duration:.2s;transition-property:transform;background-color:#fff;z-index:1;display:none;flex-direction:column;padding:10px}@media screen and (max-width: 1124px){.note-menu[_ngcontent-%COMP%]{right:30px}}.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]{display:flex;flex-direction:column}.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]{margin-top:5px;display:flex;align-items:center}.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-text[_ngcontent-%COMP%]{margin-left:10px;cursor:pointer}.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-icon[_ngcontent-%COMP%]{width:20px;display:flex;justify-content:center}.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]:first-child{margin-top:0}.plain[_ngcontent-%COMP%]{margin:-13px 2px 0;border-top:0;border-radius:0 0 12px 12px;position:relative}.filter-white[_ngcontent-%COMP%]{filter:invert(100%) sepia(0%) saturate(1%) hue-rotate(328deg) brightness(200%) contrast(101%)}.note-info-width[_ngcontent-%COMP%]{min-width:10rem}.note-title-text[_ngcontent-%COMP%]{font-weight:400;font-size:1rem;letter-spacing:.01em;text-align:left;max-width:17rem}@media only screen and (max-width: 768px){.note-title-text[_ngcontent-%COMP%]{max-width:9rem;font-size:.825rem}}.accordion[_ngcontent-%COMP%]{border-radius:4px;margin-top:10px}.header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:8px;cursor:pointer;border-radius:31px;box-shadow:3px 3px 6px 2px #00000029;z-index:4;position:relative;border:1px solid transparent}.header.isPinned[_ngcontent-%COMP%]{color:#fff;background-image:linear-gradient(90deg,#5060c3 0% 13%,#5060c3 25% 37%,#5060c3 50% 63%,#5060c3 75% 87%,#5060c3)}.header.isPinned[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.isPinned[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]{border:2px solid white}.header.isPinned[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.isPinned[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%]{background-color:#fff}.header.isPinned[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{background:transparent;border:2px solid white}.header.isPinnedAndFavourite[_ngcontent-%COMP%]{color:#fff;background-image:linear-gradient(90deg,#4253b3,#495abc,#5060c3,#5767cb,#5f6ed3 44%,#6674db 56%,#6a78e2,#717fea,#7887f2,#808efa)}.header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]{border:2px solid white}.header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%]{background-color:#fff}.header.isPinnedAndFavourite[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{background:transparent;border:2px solid white}.header.opened[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]{border:1px solid transparent;color:#fff;background-color:#7f8dfa}.header.opened.activated[_ngcontent-%COMP%], .header.opened2.activated[_ngcontent-%COMP%]{background-color:#7f8dfa}.header.opened[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.opened[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]{border:2px solid white}.header.opened[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.opened[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%]{background-color:#fff}.header.opened[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{border:1px solid white;background:transparent;width:36px;height:36px;border-radius:50%;display:flex;justify-content:center;align-items:center}.content[_ngcontent-%COMP%]{padding:10px}.down-arrow[_ngcontent-%COMP%]{transition:transform .3s ease-in-out}.down-arrow.pi-chevron-up[_ngcontent-%COMP%]{transform:rotate(360deg)}.circle[_ngcontent-%COMP%]{background-image:linear-gradient(180deg,#9280fa,#8d7cf8,#8779f6,#8275f5,#7d72f3 44%,#736af0 56%,#6c68ee,#6664ed,#6161ea,#595ee8);width:36px;height:36px;border-radius:50%;display:flex;justify-content:center;align-items:center}.circle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#fff;font-size:20px}.rotate[_ngcontent-%COMP%]{transform:rotate(180deg);transition:transform .3s ease-in-out}.accordion-content[_ngcontent-%COMP%]{max-height:0;overflow:hidden}.open[_ngcontent-%COMP%]{background-color:#fff;margin-top:-20px;border-bottom-left-radius:30px;border-bottom-right-radius:30px;height:370px;max-height:370px;padding:20px 3px 3px}@media only screen and (max-width: 768px){.open[_ngcontent-%COMP%]{height:410px;max-height:410px}}.btn-action[_ngcontent-%COMP%]{width:200px}@media only screen and (max-width: 768px){.btn-action[_ngcontent-%COMP%]{width:auto}}[_nghost-%COMP%]     .p-editor-container .p-editor-toolbar.ql-snow{border:none;padding:0!important}[_nghost-%COMP%]     .p-editor-container .p-editor-content .ql-editor{background:transparent;padding:4px}[_nghost-%COMP%]     .p-editor-container .p-editor-content .ql-editor p{font-size:1rem}[_nghost-%COMP%]     .p-editor-container .p-editor-content .ql-editor[contenteditable=true]{border:1px solid var(--my-gray);border-radius:10px}[_nghost-%COMP%]     .circle.ql-snow{border:none}[_nghost-%COMP%]     .p-editor-container .p-editor-content.ql-snow{border:none}[_nghost-%COMP%]     .ql-snow .ql-tooltip{transform:translate(80%,-50%)}@media only screen and (max-width: 768px){[_nghost-%COMP%]     .ql-snow .ql-tooltip{transform:translate(45%,-50%)}}  .p-menu .p-menuitem-link{padding:.5rem 1rem!important}#add-note[_ngcontent-%COMP%]{background-color:var(--gray);padding:30px;border-radius:12px;margin-top:30px}.add-note-btn-icon[_ngcontent-%COMP%]{width:25px;height:25px}.notes-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.notes-header[_ngcontent-%COMP%]   .notes-header-title[_ngcontent-%COMP%]{font-size:18px;color:var(--main-color);font-weight:700}.notes-header[_ngcontent-%COMP%]   .notes-header-actions[_ngcontent-%COMP%]{display:flex;align-items:center}.notes-header[_ngcontent-%COMP%]   .notes-header-actions[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{margin-right:.7em}.notes-header[_ngcontent-%COMP%]   .notes-header-actions[_ngcontent-%COMP%]   .light-purple-circle-button[_ngcontent-%COMP%]{width:20px;height:20px}.note-menu-row[_ngcontent-%COMP%]{display:flex;justify-content:space-evenly;border-radius:40px}.note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]{display:flex;align-items:center;position:relative}.note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-text[_ngcontent-%COMP%]{cursor:pointer}.note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-icon[_ngcontent-%COMP%]{width:20px;display:flex;justify-content:center}.rte[_ngcontent-%COMP%]{margin-top:20px;border-radius:20px;z-index:0!important}[_nghost-%COMP%]     .e-toolbar-items{border-radius:20px!important}[_nghost-%COMP%]     .e-rte-toolbar{border-radius:20px 20px 0 0!important}[_nghost-%COMP%]     .e-rte-content{border-radius:0 0 20px 20px!important;border-top:0px!important}[_nghost-%COMP%]     .e-date-icon{position:absolute!important;left:0!important}[_nghost-%COMP%]     .e-input{margin-left:30px!important}.notes-popup[_ngcontent-%COMP%]{position:absolute;top:40px;width:200px;border-radius:12px;background-color:#fff;color:var(--main-color);right:0;box-shadow:0 -4px 4px #00000040;max-height:400px;z-index:10000}.notes-filters-title[_ngcontent-%COMP%]{font-size:18px;font-weight:700}.notes-filters-filters[_ngcontent-%COMP%]{font-size:15px}.notes-filters-filters[_ngcontent-%COMP%]   .filter-row[_ngcontent-%COMP%]{margin-top:15px;display:flex;align-items:center}.notes-filters-filters[_ngcontent-%COMP%]   .filter-row[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]{margin-left:15px}.notes-filters-filters[_ngcontent-%COMP%]   .filter-col[_ngcontent-%COMP%]{margin-top:15px}.send-classroom-title[_ngcontent-%COMP%]{font-size:18px;font-weight:700}.send-classroom-user[_ngcontent-%COMP%]{font-size:15px;margin-left:10px;display:flex}.send-button[_ngcontent-%COMP%]{background-color:var(--main-color);width:100%;position:sticky;bottom:0;left:0;padding:8px;box-sizing:border-box;color:#fff;text-align:center;border-radius:12px;cursor:pointer}.notes-height[_ngcontent-%COMP%]{height:calc(100% - 50px)}.btns[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{width:100%}[_nghost-%COMP%]     .ngx-pagination{padding:0!important}.add[_ngcontent-%COMP%]{width:32px;height:32px}.add.lg[_ngcontent-%COMP%]{width:2.1rem;height:2.1rem}.add.empty[_ngcontent-%COMP%]{width:7.1rem;height:auto}  .p-tabview .p-tabview-panels{background-color:transparent;padding:0;justify-content:center;height:100%;display:flex;flex-direction:column}.responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}.modal[_ngcontent-%COMP%]{min-width:50vw}@media only screen and (max-width: 768px){.modal[_ngcontent-%COMP%]{min-width:80vw}}@keyframes _ngcontent-%COMP%_collapse{0%{width:auto}to{width:0}}.notes[_ngcontent-%COMP%]{background-color:transparent;padding:0;justify-content:space-evenly}.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]{padding:10px;border-radius:12px;top:86px;transition:width .3s ease-in-out}.notes[_ngcontent-%COMP%]   .notes-left-side.collapsed[_ngcontent-%COMP%]{width:3%}.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#00000050}.notes[_ngcontent-%COMP%]   .notes-right-side[_ngcontent-%COMP%]{border-radius:28px;transition:width .3s ease-in-out}.notes[_ngcontent-%COMP%]   .notes-right-side.expanded[_ngcontent-%COMP%]{width:97%}.notes[_ngcontent-%COMP%]   .add[_ngcontent-%COMP%]{width:32px;height:32px}.notes[_ngcontent-%COMP%]   .add.lg[_ngcontent-%COMP%]{width:2.1rem;height:2.1rem}.notes[_ngcontent-%COMP%]   .add.empty[_ngcontent-%COMP%]{width:7.1rem;height:auto}.notes[_ngcontent-%COMP%]   .folder-nav-buttons[_ngcontent-%COMP%]{border-right:1.5px solid #002CCF;margin-right:10px;display:flex;justify-content:center;align-items:self-end;gap:5px}.notes[_ngcontent-%COMP%]   .folder-nav-buttons[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:11px}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]{list-style:none;padding:0;margin:8px;overflow:auto}@media only screen and (max-width: 768px){.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]{margin:0;width:100%}}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .bg-grad-blue[_ngcontent-%COMP%]{background:linear-gradient(#657aef,#1a266c)}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]{border-radius:40px;display:flex;background-color:#fff;align-items:center;margin-bottom:10px}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   svg.library-file-icon[_ngcontent-%COMP%]{padding:8px;margin-top:5px}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .library-file-details[_ngcontent-%COMP%]{width:100%;display:flex}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .library-file-details[_ngcontent-%COMP%]   .library-file-meta[_ngcontent-%COMP%]{margin-top:5px;display:flex}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .library-file-details[_ngcontent-%COMP%]   .library-file-meta[_ngcontent-%COMP%]   span.file-level[_ngcontent-%COMP%]{color:#fff;background:#444070;padding:6px;border-radius:50px}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .library-file-details[_ngcontent-%COMP%]   .library-file-meta[_ngcontent-%COMP%]   span.file-category[_ngcontent-%COMP%]{color:#fff;background-color:#a44fd0;padding:6px;border-radius:50px;margin-left:6px}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]{width:180px;position:absolute;left:92.5%;top:69px;border-radius:12px;border:1px solid var(--main-color);box-sizing:border-box;transition-duration:.2s;transition-property:transform;background-color:#fff;z-index:1;display:none;flex-direction:column;padding:10px}@media screen and (max-width: 1124px){.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]{right:30px}}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]{display:flex;flex-direction:column}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]   .lib-file-menu-col[_ngcontent-%COMP%]{margin-top:5px;display:flex;align-items:center}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]   .lib-file-menu-col[_ngcontent-%COMP%]   .lib-file-menu-text[_ngcontent-%COMP%]{margin-left:10px;cursor:pointer}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]   .lib-file-menu-col[_ngcontent-%COMP%]   .lib-file-menu-icon[_ngcontent-%COMP%]{width:20px;display:flex;justify-content:center}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]:first-child{margin-top:0}.libs[_ngcontent-%COMP%]{background:#fff;padding:30px;margin-bottom:15px;border-radius:28px}[_nghost-%COMP%]     .tawk-card-primary{background-color:var(--main-color)!important}.inner-folders[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap}.inner-folders[_ngcontent-%COMP%]   .inner-folder[_ngcontent-%COMP%]{margin:29px 15px 15px;display:flex;align-items:center;justify-content:center;color:var(--main-color);font-weight:700}.inner-folders[_ngcontent-%COMP%]   .inner-folder[_ngcontent-%COMP%]::selection{background-color:#fff}.inner-folders[_ngcontent-%COMP%]   .new-folder[_ngcontent-%COMP%]{color:var(--main-color);background-color:#fff}.inner-folders[_ngcontent-%COMP%]   .new-folder[_ngcontent-%COMP%]::selection{color:var(--main-color);background-color:#fff}.folder-path[_ngcontent-%COMP%]{display:flex;margin:15px 0;font-size:18px;align-items:center}.close-img[_ngcontent-%COMP%]{width:20px;position:absolute;padding:5px;border-radius:50%;border:1px solid black;left:90px;z-index:10;background:#fff;top:11px}.my-folder[_ngcontent-%COMP%]{border-radius:8px;position:relative;z-index:2}.my-folder[_ngcontent-%COMP%]:before{position:absolute;width:100%;height:100%;content:\\\"\\\";background:linear-gradient(#002ccf,#c9d2ff00);opacity:.65;border-top-left-radius:10px;border-top-right-radius:10px;z-index:-1;left:50%;transform:translate(-50%);top:0}.my-folder.purple-folder-bg[_ngcontent-%COMP%]:before{background:linear-gradient(#6262eb,#c9d2ff00)}.wrapper[_ngcontent-%COMP%]{max-width:50rem;width:100%;margin:0 auto}.tabs[_ngcontent-%COMP%]{position:relative;margin:3rem 0;height:18.75rem}.tabs[_ngcontent-%COMP%]:before, .tabs[_ngcontent-%COMP%]:after{content:\\\"\\\";display:table}.tabs[_ngcontent-%COMP%]:after{clear:both}.tab[_ngcontent-%COMP%]{float:left;width:50%}.tab-switch[_ngcontent-%COMP%]{display:none}.tab-label[_ngcontent-%COMP%]{position:relative;display:block;line-height:2.75em;height:4em;padding:0 1.618em;cursor:pointer;top:0;opacity:.6;width:76%;transition:all .25s}.tab-label[_ngcontent-%COMP%]:hover{top:-.25rem;transition:top .25s}.tab-content[_ngcontent-%COMP%]{height:100%;position:absolute;z-index:1;top:4em;left:0;color:#2c3e50;opacity:0;overflow:auto}.tab-switch[_ngcontent-%COMP%]:checked + .tab-label[_ngcontent-%COMP%]{background:#fff;color:#2c3e50;border-bottom:0;border-right:.125rem solid #fff;transition:all .35s;z-index:1;opacity:1;background-image:linear-gradient(180deg,#6580e2,#758ce6,#8296e8,#8e9feb,#9faeef 44%,#acb8f1 56%,#b8c3f4,#c5cef6,#d2d9f9,#dfe4fb);border-top-left-radius:.5rem;border-top-right-radius:.5rem;width:100%}.tab-switch[_ngcontent-%COMP%]:checked + label[_ngcontent-%COMP%] + .tab-content[_ngcontent-%COMP%]{z-index:2;opacity:1;transition:all .35s;width:100%}.horizontal-scroll-menu[_ngcontent-%COMP%]{overflow:auto;white-space:nowrap}.folder-options-icon[_ngcontent-%COMP%]{right:-15px;transform:rotate(90deg)}  .p-tabview .p-tabview-panels{background-color:transparent;padding:0}  .p-tabview .p-tabview-nav{background:transparent;border:none}  .p-tabview .p-tabview-nav li .p-tabview-nav-link,   .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link{background:transparent;border:none}  .p-tabview .p-tabview-nav .p-tabview-ink-bar{background-color:#8a7af7;bottom:5px}  .p-tabview .p-tabview-title{color:#4354b4}.filter-blue[_ngcontent-%COMP%]{opacity:.7}.abs-header-top[_ngcontent-%COMP%]{top:-68px;width:100%;z-index:4}.abs-header-top.more[_ngcontent-%COMP%]{top:-130px}@media only screen and (max-width: 768px){.abs-header-top.more[_ngcontent-%COMP%]{top:-218px;flex-direction:column}}.icon-times-blue[_ngcontent-%COMP%]{color:#6a79e2}*[_ngcontent-%COMP%]::-webkit-scrollbar{width:10px}@media only screen and (max-width: 768px){.allFiles[_ngcontent-%COMP%]{max-height:23rem;overflow-y:auto}}\"]\n    });\n  }\n  return SingleLibraryComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}