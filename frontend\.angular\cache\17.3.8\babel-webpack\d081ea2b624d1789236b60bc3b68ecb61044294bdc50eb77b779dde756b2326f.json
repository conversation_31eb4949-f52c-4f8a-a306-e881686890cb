{"ast": null, "code": "import { ClassroomType, Level } from 'src/app/core/models/classroom.model';\nimport { CircleType, GoalReason } from 'src/app/core/models/goal.model';\nimport { Category } from 'src/app/core/models/homework.model';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/toast.service\";\nimport * as i2 from \"src/app/core/services/general.service\";\nimport * as i3 from \"src/app/core/services/classroom.service\";\nimport * as i4 from \"src/app/core/services/goal.service\";\nimport * as i5 from \"src/app/core/services/auth.service\";\nconst _c0 = [\"createGoal\"];\nfunction CreateGoalComponent_app_circle_line_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-circle-line\", 12);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"circles\", ctx_r0.stepCircleLines);\n  }\n}\nfunction CreateGoalComponent_span_5_div_1_div_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const goal_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(goal_r3.language);\n  }\n}\nfunction CreateGoalComponent_span_5_div_1_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1, \" Save current edit? \");\n    i0.ɵɵelementStart(2, \"div\", 21);\n    i0.ɵɵlistener(\"click\", function CreateGoalComponent_span_5_div_1_div_1_div_3_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r0.answerConfirm(false));\n    });\n    i0.ɵɵtext(3, \" no \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 22);\n    i0.ɵɵlistener(\"click\", function CreateGoalComponent_span_5_div_1_div_1_div_3_Template_div_click_4_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r0.answerConfirm(true));\n    });\n    i0.ɵɵtext(5, \" yes \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CreateGoalComponent_span_5_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"p-dropdown\", 15);\n    i0.ɵɵlistener(\"onChange\", function CreateGoalComponent_span_5_div_1_div_1_Template_p_dropdown_onChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.changeIndex($event));\n    });\n    i0.ɵɵtemplate(2, CreateGoalComponent_span_5_div_1_div_1_ng_template_2_Template, 3, 1, \"ng-template\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CreateGoalComponent_span_5_div_1_div_1_div_3_Template, 6, 0, \"div\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r0.goals);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showConfirm);\n  }\n}\nfunction CreateGoalComponent_span_5_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, CreateGoalComponent_span_5_div_1_div_1_Template, 4, 2, \"div\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.goals.length > 0);\n  }\n}\nfunction CreateGoalComponent_span_5_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Choose classroom you want to create a goal for \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateGoalComponent_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtemplate(1, CreateGoalComponent_span_5_div_1_Template, 2, 1, \"div\", 5)(2, CreateGoalComponent_span_5_div_2_Template, 2, 0, \"div\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.goals.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.goals.length == 0);\n  }\n}\nfunction CreateGoalComponent_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Choose level desired to reach \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateGoalComponent_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" When would you like to achieve your goal? \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateGoalComponent_span_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" How many lesson hours would you like to take per week? \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateGoalComponent_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Why do you want to study? \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateGoalComponent_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Which areas would you like to improve? \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateGoalComponent_div_11_div_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const classroom_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(classroom_r6.language);\n  }\n}\nfunction CreateGoalComponent_div_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"p-dropdown\", 24);\n    i0.ɵɵlistener(\"onChange\", function CreateGoalComponent_div_11_div_1_Template_p_dropdown_onChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.chooseClassroom($event));\n    });\n    i0.ɵɵtemplate(2, CreateGoalComponent_div_11_div_1_ng_template_2_Template, 3, 1, \"ng-template\", 16);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r0.userClassrooms);\n  }\n}\nfunction CreateGoalComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtemplate(1, CreateGoalComponent_div_11_div_1_Template, 3, 1, \"div\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.goals.length == 0);\n  }\n}\nfunction CreateGoalComponent_app_circle_line_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-circle-line\", 25);\n    i0.ɵɵlistener(\"circleChoice\", function CreateGoalComponent_app_circle_line_12_Template_app_circle_line_circleChoice_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.desiredLevelChoice($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"allowHoverMain\", true)(\"circles\", ctx_r0.currentLevelCircleLines);\n  }\n}\nfunction CreateGoalComponent_div_13_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const month_r9 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(month_r9.month);\n  }\n}\nfunction CreateGoalComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"p-dropdown\", 26);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CreateGoalComponent_div_13_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.monthSelected, $event) || (ctx_r0.monthSelected = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function CreateGoalComponent_div_13_Template_p_dropdown_onChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.chooseMonth($event));\n    });\n    i0.ɵɵtemplate(2, CreateGoalComponent_div_13_ng_template_2_Template, 3, 1, \"ng-template\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-dropdown\", 27);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CreateGoalComponent_div_13_Template_p_dropdown_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.yearChosen, $event) || (ctx_r0.yearChosen = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function CreateGoalComponent_div_13_Template_p_dropdown_onChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.chooseYear($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r0.months);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.monthSelected);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"options\", ctx_r0.years);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.yearChosen);\n  }\n}\nfunction CreateGoalComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"p-dropdown\", 28);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CreateGoalComponent_div_14_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r0.hoursWeeklyChosen, $event) || (ctx_r0.hoursWeeklyChosen = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function CreateGoalComponent_div_14_Template_p_dropdown_onChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.chooseHours($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r0.hours);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r0.hoursWeeklyChosen);\n  }\n}\nfunction CreateGoalComponent_div_15_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"ejs-checkbox\", 31);\n    i0.ɵɵlistener(\"change\", function CreateGoalComponent_div_15_div_1_Template_ejs_checkbox_change_1_listener() {\n      const reason_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.chooseReason(reason_r12));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const reason_r12 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r0.getIsReasonChecked(reason_r12));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", reason_r12, \" \");\n  }\n}\nfunction CreateGoalComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtemplate(1, CreateGoalComponent_div_15_div_1_Template, 3, 2, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.reasons);\n  }\n}\nfunction CreateGoalComponent_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"ejs-checkbox\", 31);\n    i0.ɵɵlistener(\"change\", function CreateGoalComponent_div_16_div_1_Template_ejs_checkbox_change_1_listener() {\n      const area_r14 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.chooseArea(area_r14));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const area_r14 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r0.getIsAreaChecked(area_r14));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", area_r14, \" \");\n  }\n}\nfunction CreateGoalComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtemplate(1, CreateGoalComponent_div_16_div_1_Template, 3, 2, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.areas);\n  }\n}\nfunction CreateGoalComponent_div_17_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵlistener(\"click\", function CreateGoalComponent_div_17_div_3_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.changeStep(true));\n    });\n    i0.ɵɵtext(1, \" NEXT \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateGoalComponent_div_17_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵlistener(\"click\", function CreateGoalComponent_div_17_div_4_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.submit());\n    });\n    i0.ɵɵtext(1, \" SUBMIT \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreateGoalComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 34);\n    i0.ɵɵlistener(\"click\", function CreateGoalComponent_div_17_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.changeStep(false));\n    });\n    i0.ɵɵtext(2, \" BACK \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CreateGoalComponent_div_17_div_3_Template, 2, 0, \"div\", 35)(4, CreateGoalComponent_div_17_div_4_Template, 2, 0, \"div\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.currentStep > 0 ? \"link-main-color\" : \"inactive\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.currentStep < 5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.currentStep == 5);\n  }\n}\nfunction CreateGoalComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵelement(1, \"img\", 40);\n    i0.ɵɵelementStart(2, \"div\", 41);\n    i0.ɵɵtext(3, \" All set! Start Learning! \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class CreateGoalComponent {\n  constructor(toastService, generalService, classroomService, goalService, authService) {\n    this.toastService = toastService;\n    this.generalService = generalService;\n    this.classroomService = classroomService;\n    this.goalService = goalService;\n    this.authService = authService;\n    this.goals = [];\n    this.editIndex = 0;\n    this.showConfirm = false;\n    this.subs = new SubSink();\n    this.stepCircleLines = [];\n    this.userClassrooms = [];\n    this.currentLevelCircleLines = [];\n    this.currentStep = 0;\n    this.levels = [];\n    this.months = [{\n      number: \"0\",\n      month: \"January\"\n    }, {\n      number: \"1\",\n      month: \"February\"\n    }, {\n      number: \"2\",\n      month: \"March\"\n    }, {\n      number: \"3\",\n      month: \"April\"\n    }, {\n      number: \"4\",\n      month: \"May\"\n    }, {\n      number: \"5\",\n      month: \"June\"\n    }, {\n      number: \"6\",\n      month: \"July\"\n    }, {\n      number: \"7\",\n      month: \"August\"\n    }, {\n      number: \"8\",\n      month: \"September\"\n    }, {\n      number: \"9\",\n      month: \"October\"\n    }, {\n      number: \"10\",\n      month: \"November\"\n    }, {\n      number: \"11\",\n      month: \"December\"\n    }];\n    this.years = [\"2022\", \"2023\", \"2024\"];\n    this.hours = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];\n    this.reasons = [GoalReason.BOOST, GoalReason.CERTIFICATE, GoalReason.COMMUNICATE, GoalReason.CULTURE, GoalReason.EDUCATION, GoalReason.HOBBY, GoalReason.MOVING, GoalReason.TRAVELING];\n    this.areas = [Category.GRAMMAR, Category.LISTENING, Category.READING, Category.REVISION, Category.SPEAKING, Category.TEST, Category.VOCABULARY, Category.WRITING];\n    this.currentLevelChosen = {};\n    this.desiredLevelChosen = {};\n    this.monthChosen = \"01\";\n    this.monthSelected = {};\n    this.yearChosen = \"\";\n    this.dateChosen = new Date();\n    this.hoursWeeklyChosen = 0;\n    this.reasonsChosen = [];\n    this.areasChosen = [];\n    this.languageChosen = \"\";\n    this.classroomChoosen = {};\n    this.choosenGoalId = \"\";\n    this.indexToEdit = 0;\n  }\n  ngOnInit() {}\n  ngAfterViewInit() {\n    if (this.goals.length > 0) {\n      this.initiateGoalToEdit(0);\n    }\n    this.subs.sink = this.getUserClassrooms().subscribe(res => {\n      this.userClassrooms = res;\n    });\n    let extraTexts = [\"Classroom\", \"Desired Level\", \"When\", \"Lesson Hours\", \"Why\", \"Improvement\"];\n    let extraTextsml = [\"-30px\", \"-38px\", \"-9px\", \"-37px\", \"-1px\", \"-43px\"];\n    this.levels = [Level.A1, Level.A2, Level.B1, Level.B2, Level.C1, Level.C2];\n    for (let i = 0; i < 6; i++) {\n      let background = i == 0 ? '#a4a2e6' : 'white';\n      let color = i == 0 ? 'white' : '#a4a2e6';\n      let circleLine = {\n        text: (i + 1).toString(),\n        extraText: extraTexts[i],\n        extraTextML: extraTextsml[i],\n        lineStyle: '1px dashed lightgray',\n        background: background,\n        color: color,\n        width: this.createGoal.nativeElement.clientWidth / 5 - 21 + 'px',\n        type: CircleType.GENERAL\n      };\n      this.stepCircleLines.push(circleLine);\n      circleLine = this.levelsEmpty(i);\n      if (this.goals.length == 0) this.currentLevelCircleLines.push(circleLine);\n    }\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  initiateGoalToEdit(editIndex) {\n    this.choosenGoalId = this.goals[editIndex].id;\n    this.currentLevelChosen = this.goals[editIndex].currentLevel;\n    this.desiredLevelChosen = this.goals[editIndex].desiredLevel;\n    this.dateChosen = this.goals[editIndex].desiredAchieveDate;\n    this.hoursWeeklyChosen = this.goals[editIndex].hoursWeekly;\n    this.reasonsChosen = this.goals[editIndex].reasons;\n    this.areasChosen = this.goals[editIndex].areas;\n    this.languageChosen = this.goals[editIndex].language;\n    this.monthSelected = this.months.filter(el => el.number == this.goals[editIndex]?.desiredAchieveDate.getMonth().toString())[0];\n    this.yearChosen = this.goals[editIndex]?.desiredAchieveDate.getFullYear().toString();\n    let currentLevel = this.goals[editIndex].currentLevel;\n    let desiredLevel = this.goals[editIndex].desiredLevel;\n    let levels = [Level.A1, Level.A2, Level.B1, Level.B2, Level.C1, Level.C2];\n    let currentLevelIndex = levels.findIndex(el => el === currentLevel);\n    let desiredLevelIndex = levels.findIndex(el => el === desiredLevel);\n    let newCircleLines = [];\n    this.currentLevelCircleLines = [];\n    for (let i = 0; i <= currentLevelIndex; i++) {\n      let lineStyle = i == currentLevelIndex ? '1px dashed lightgray' : '1px solid lightgray';\n      let circleLine = {\n        text: levels[i],\n        lineStyle,\n        background: \"#a4a2e6\",\n        color: \"white\",\n        type: CircleType.GENERAL,\n        width: this.createGoal.nativeElement.clientWidth / 5 - 21 + 'px'\n      };\n      newCircleLines.push(circleLine);\n      this.currentLevelCircleLines.push(circleLine);\n    }\n    for (let i = currentLevelIndex + 1; i <= desiredLevelIndex; i++) {\n      let circleLine = {\n        text: levels[i],\n        lineStyle: '1px dashed lightgray',\n        background: \"#2d2a4b\",\n        color: \"white\",\n        type: CircleType.GENERAL,\n        width: this.createGoal.nativeElement.clientWidth / 5 - 21 + 'px'\n      };\n      newCircleLines.push(circleLine);\n      this.currentLevelCircleLines.push(circleLine);\n    }\n    for (let i = desiredLevelIndex + 1; i < 6; i++) {\n      let circleLine = {\n        text: levels[i],\n        lineStyle: '1px dashed lightgray',\n        background: \"white\",\n        color: \"#a4a2e6\",\n        type: CircleType.GENERAL,\n        width: this.createGoal.nativeElement.clientWidth / 5 - 21 + 'px'\n      };\n      newCircleLines.push(circleLine);\n      this.currentLevelCircleLines.push(circleLine);\n    }\n  }\n  getIsReasonChecked(reason) {\n    return this.reasonsChosen.some(el => el == reason);\n  }\n  getIsAreaChecked(area) {\n    return this.areasChosen.some(el => el == area);\n  }\n  getUserClassrooms() {\n    return this.classroomService.getUserClassrooms(ClassroomType.PAID);\n  }\n  levelsEmpty(i) {\n    return {\n      text: this.levels[i],\n      lineStyle: '1px dashed lightgray',\n      background: \"white\",\n      color: '#a4a2e6',\n      width: this.createGoal.nativeElement.clientWidth / 5 - 21 + 'px',\n      type: CircleType.CURRENT_LEVEL\n    };\n  }\n  changeStep(next) {\n    if (this.currentStep == 0 && this.currentLevelChosen.length == undefined) {\n      this.toastService.setShowToastmessage({\n        severity: 'info',\n        summary: '',\n        detail: 'Please choose a language to set a goal'\n      });\n      return;\n    }\n    if (this.currentStep == 1 && this.desiredLevelChosen.length == undefined) {\n      this.toastService.setShowToastmessage({\n        severity: 'info',\n        summary: '',\n        detail: 'Please choose a level to continue'\n      });\n      return;\n    }\n    if (next) {\n      if (this.currentStep < 5) {\n        this.stepCircleLines[this.currentStep].lineStyle = '1px solid lightgray';\n        this.currentStep++;\n        this.stepCircleLines[this.currentStep].background = '#a4a2e6';\n        this.stepCircleLines[this.currentStep].color = 'white';\n      }\n    } else {\n      if (this.currentStep > 0) {\n        this.stepCircleLines[this.currentStep].background = 'white';\n        this.stepCircleLines[this.currentStep].color = '#a4a2e6';\n        this.currentStep--;\n        this.stepCircleLines[this.currentStep].lineStyle = '1px dashed lightgray';\n      }\n    }\n  }\n  chooseClassroom(event) {\n    this.currentLevelChosen = event.value.activeLevel;\n    this.languageChosen = event.value.language;\n    this.classroomChoosen = event.value;\n    this.currentLevelChoice();\n    // TODO GETCLASSROOMTEACHER\n  }\n  currentLevelChoice() {\n    let currentLevelIndex = this.levels.findIndex(el => el == this.currentLevelChosen);\n    for (let i = 0; i < 6; i++) {\n      this.currentLevelCircleLines[i] = this.levelsEmpty(i);\n    }\n    for (let i = 0; i <= currentLevelIndex; i++) {\n      let line = i < currentLevelIndex ? '1px solid lightgray' : '1px dashed lightgray';\n      this.currentLevelCircleLines[i].background = '#a4a2e6';\n      this.currentLevelCircleLines[i].color = 'white';\n      this.currentLevelCircleLines[i].lineStyle = line;\n    }\n  }\n  desiredLevelChoice(event) {\n    if (event.circle.text <= this.currentLevelChosen) {\n      this.toastService.setShowToastmessage({\n        severity: 'info',\n        summary: '',\n        detail: 'Please choose level greater than the current one'\n      });\n    } else {\n      this.desiredLevelChosen = event.circle.text;\n      let desiredLevelIndex = this.levels.findIndex(el => el === event.circle.text);\n      let currentLevelIndex = this.levels.findIndex(el => el === this.currentLevelChosen);\n      for (let i = currentLevelIndex + 1; i < 6; i++) {\n        this.currentLevelCircleLines[i] = this.levelsEmpty(i);\n      }\n      for (let i = currentLevelIndex + 1; i <= desiredLevelIndex; i++) {\n        let line = i < desiredLevelIndex ? '1px solid lightgray' : '1px dashed lightgray';\n        this.currentLevelCircleLines[i].background = '#2d2a4b';\n        this.currentLevelCircleLines[i].color = 'white';\n        this.currentLevelCircleLines[i].lineStyle = line;\n      }\n    }\n  }\n  chooseMonth(event) {\n    this.monthChosen = (parseInt(event.value.number) + 1).toString();\n  }\n  chooseYear(event) {\n    this.yearChosen = event.value;\n  }\n  chooseHours(event) {\n    this.hoursWeeklyChosen = event.value;\n  }\n  chooseReason(reason) {\n    if (this.reasonsChosen.some(el => el == reason)) {\n      this.reasonsChosen = this.reasonsChosen.filter(el => el !== reason);\n    } else {\n      this.reasonsChosen.push(reason);\n    }\n  }\n  chooseArea(area) {\n    if (this.areasChosen.some(el => el == area)) {\n      this.areasChosen = this.areasChosen.filter(el => el !== area);\n    } else {\n      this.areasChosen.push(area);\n    }\n  }\n  changeIndex(event) {\n    this.indexToEdit = this.goals.findIndex(el => el.language == event.value.language);\n    if (this.currentStep == 0) {\n      this.editIndex = this.indexToEdit;\n      this.initiateGoalToEdit(this.indexToEdit);\n    } else {\n      this.showConfirm = true;\n      this.createGoal.nativeElement.style.opacity = '0.5';\n    }\n  }\n  answerConfirm(confirm) {\n    this.createGoal.nativeElement.style.opacity = '1';\n    this.showConfirm = false;\n    if (confirm) {\n      this.submit(true);\n    } else {\n      this.toastService.setShowToastmessage({\n        severity: 'info',\n        summary: '',\n        detail: 'Your edit has not been saved'\n      });\n    }\n    this.currentStep = 0;\n    this.editIndex = this.indexToEdit;\n    this.initiateGoalToEdit(this.indexToEdit);\n  }\n  submit(update) {\n    this.currentStep = 6;\n    console.log(this.monthChosen + \"/01/\" + this.yearChosen);\n    this.dateChosen = new Date(this.monthChosen + \"/01/\" + this.yearChosen);\n    console.log(this.dateChosen);\n    let goal = {\n      startingLevel: this.currentLevelChosen,\n      currentLevel: this.currentLevelChosen,\n      desiredLevel: this.desiredLevelChosen,\n      desiredAchieveDate: this.dateChosen,\n      // totalWeeks: this.generalService.getDatesDiff(new Date(), this.dateChosen).weeks,\n      hoursWeekly: this.hoursWeeklyChosen,\n      // hoursCompleted: [],\n      reasons: this.reasonsChosen,\n      areas: this.areasChosen,\n      language: this.languageChosen,\n      teacher: this.classroomChoosen.teacher,\n      // classroom: this.classroomChoosen,\n      studentId: this.authService.getUserId()\n    };\n    console.log(goal);\n    if (this.goals.length > 0) {\n      this.goalService.updateGoal(goal, this.choosenGoalId).subscribe(res => {\n        console.log(res);\n        this.goalService.setUpdateListener(true);\n      });\n      this.toastService.setShowToastmessage({\n        severity: 'info',\n        summary: '',\n        detail: 'Your goal has been successfully updated.'\n      });\n    } else {\n      //create\n      this.goalService.createGoal(goal).subscribe(res => {\n        console.log(res);\n        this.goalService.setUpdateListener(true);\n      });\n      this.toastService.setShowToastmessage({\n        severity: 'info',\n        summary: '',\n        detail: 'Your goal has been successfully created. Good luck achieving it!'\n      });\n    }\n  }\n  static #_ = this.ɵfac = function CreateGoalComponent_Factory(t) {\n    return new (t || CreateGoalComponent)(i0.ɵɵdirectiveInject(i1.ToastService), i0.ɵɵdirectiveInject(i2.GeneralService), i0.ɵɵdirectiveInject(i3.ClassroomService), i0.ɵɵdirectiveInject(i4.GoalService), i0.ɵɵdirectiveInject(i5.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: CreateGoalComponent,\n    selectors: [[\"app-create-goal\"]],\n    viewQuery: function CreateGoalComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.createGoal = _t.first);\n      }\n    },\n    inputs: {\n      goals: \"goals\"\n    },\n    decls: 19,\n    vars: 15,\n    consts: [[\"createGoal\", \"\"], [\"id\", \"create-goal-2\", 1, \"create-goal\"], [3, \"circles\", 4, \"ngIf\"], [1, \"choose-level\"], [1, \"choose-level-title\"], [4, \"ngIf\"], [\"class\", \"desired-date\", 4, \"ngIf\"], [3, \"allowHoverMain\", \"circles\", \"circleChoice\", 4, \"ngIf\"], [\"class\", \"choose-reason\", 4, \"ngIf\"], [\"class\", \"choose-area\", 4, \"ngIf\"], [\"class\", \"btns\", 4, \"ngIf\"], [\"class\", \"end\", 4, \"ngIf\"], [3, \"circles\"], [\"style\", \"display:flex; justify-content: center;\", 4, \"ngIf\"], [2, \"display\", \"flex\", \"justify-content\", \"center\"], [\"placeholder\", \"Choose Goal\", \"optionLabel\", \"language\", 3, \"onChange\", \"options\"], [\"pTemplate\", \"item\"], [\"style\", \"display:flex; align-items: center; margin-left:15px\", 4, \"ngIf\"], [1, \"country-item\"], [1, \"country-name\"], [2, \"display\", \"flex\", \"align-items\", \"center\", \"margin-left\", \"15px\"], [1, \"light-purple-button\", 2, \"margin-left\", \"15px\", 3, \"click\"], [1, \"main-color-button\", 2, \"margin-left\", \"15px\", 3, \"click\"], [1, \"desired-date\"], [\"placeholder\", \"Choose Language\", \"optionLabel\", \"language\", 3, \"onChange\", \"options\"], [3, \"circleChoice\", \"allowHoverMain\", \"circles\"], [\"placeholder\", \"Choose Month\", \"optionLabel\", \"month\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\"], [\"placeholder\", \"Choose Year\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\"], [\"placeholder\", \"Choose Hours\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\"], [1, \"choose-reason\"], [4, \"ngFor\", \"ngForOf\"], [3, \"change\", \"checked\"], [1, \"choose-area\"], [1, \"btns\"], [3, \"click\", \"ngClass\"], [\"class\", \"link-main-color\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"light-purple-button\", 3, \"click\", 4, \"ngIf\"], [1, \"link-main-color\", 3, \"click\"], [1, \"light-purple-button\", 3, \"click\"], [1, \"end\"], [\"src\", \"/assets/icons/big-check.svg\"], [1, \"end-msg\"]],\n    template: function CreateGoalComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 1, 0);\n        i0.ɵɵtemplate(2, CreateGoalComponent_app_circle_line_2_Template, 1, 1, \"app-circle-line\", 2);\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4);\n        i0.ɵɵtemplate(5, CreateGoalComponent_span_5_Template, 3, 2, \"span\", 5)(6, CreateGoalComponent_span_6_Template, 2, 0, \"span\", 5)(7, CreateGoalComponent_span_7_Template, 2, 0, \"span\", 5)(8, CreateGoalComponent_span_8_Template, 2, 0, \"span\", 5)(9, CreateGoalComponent_span_9_Template, 2, 0, \"span\", 5)(10, CreateGoalComponent_span_10_Template, 2, 0, \"span\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(11, CreateGoalComponent_div_11_Template, 2, 1, \"div\", 6)(12, CreateGoalComponent_app_circle_line_12_Template, 1, 2, \"app-circle-line\", 7)(13, CreateGoalComponent_div_13_Template, 4, 4, \"div\", 6)(14, CreateGoalComponent_div_14_Template, 2, 2, \"div\", 6)(15, CreateGoalComponent_div_15_Template, 2, 1, \"div\", 8)(16, CreateGoalComponent_div_16_Template, 2, 1, \"div\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(17, CreateGoalComponent_div_17_Template, 5, 3, \"div\", 10)(18, CreateGoalComponent_div_18_Template, 4, 0, \"div\", 11);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep < 6);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep == 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep == 1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep == 2);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep == 3);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep == 4);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep == 5);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep == 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep == 1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep == 2);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep == 3);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep == 4);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep == 5);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep < 6);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.currentStep == 6);\n      }\n    },\n    styles: [\".create-goal[_ngcontent-%COMP%] {\\n  padding: 30px;\\n  min-height: 500px;\\n  box-sizing: border-box;\\n}\\n\\n.inactive[_ngcontent-%COMP%] {\\n  color: lightgray;\\n  font-weight: bold;\\n}\\n\\n.link-main-color[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n}\\n\\n.btns[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 90%;\\n  bottom: 0;\\n}\\n\\n.choose-level[_ngcontent-%COMP%] {\\n  margin-top: 80px;\\n}\\n.choose-level[_ngcontent-%COMP%]   .choose-level-title[_ngcontent-%COMP%] {\\n  width: 100%;\\n  text-align: center;\\n  font-weight: bold;\\n  padding: 30px 0;\\n}\\n\\n.desired-date[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n}\\n\\n.choose-reason[_ngcontent-%COMP%] {\\n  width: 70%;\\n  margin-left: 20%;\\n}\\n\\n.choose-area[_ngcontent-%COMP%] {\\n  width: 20%;\\n  margin-left: 40%;\\n}\\n\\n.end[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n.end[_ngcontent-%COMP%]   .end-msg[_ngcontent-%COMP%] {\\n  margin-top: 30px;\\n  font-weight: bold;\\n}\\n\\n.o-05[_ngcontent-%COMP%] {\\n  opacity: 0.5;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2dvYWwvY3JlYXRlLWdvYWwvY3JlYXRlLWdvYWwuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDSSxhQUFBO0VBQ0EsaUJBQUE7RUFDQSxzQkFBQTtBQUNKOztBQUVBO0VBQ0ksZ0JBQUE7RUFDQSxpQkFBQTtBQUNKOztBQUVBO0VBQ0ksaUJBQUE7QUFDSjs7QUFFQTtFQUNJLGtCQUFBO0VBQ0EsVUFBQTtFQUNBLFNBQUE7QUFDSjs7QUFFQTtFQUNJLGdCQUFBO0FBQ0o7QUFBSTtFQUNJLFdBQUE7RUFDQSxrQkFBQTtFQUNBLGlCQUFBO0VBQ0EsZUFBQTtBQUVSOztBQUVBO0VBQ0ksYUFBQTtFQUNBLHVCQUFBO0FBQ0o7O0FBRUE7RUFDSSxVQUFBO0VBQ0EsZ0JBQUE7QUFDSjs7QUFFQTtFQUNJLFVBQUE7RUFDQSxnQkFBQTtBQUNKOztBQUVBO0VBQ0ksYUFBQTtFQUNBLHVCQUFBO0VBQ0Esc0JBQUE7RUFDQSxtQkFBQTtBQUNKO0FBQUk7RUFDSSxnQkFBQTtFQUNBLGlCQUFBO0FBRVI7O0FBRUE7RUFDSSxZQUFBO0FBQ0oiLCJzb3VyY2VzQ29udGVudCI6WyIuY3JlYXRlLWdvYWwge1xyXG4gICAgcGFkZGluZzogMzBweDtcclxuICAgIG1pbi1oZWlnaHQ6IDUwMHB4O1xyXG4gICAgYm94LXNpemluZzogYm9yZGVyLWJveDtcclxufVxyXG5cclxuLmluYWN0aXZlIHtcclxuICAgIGNvbG9yOiBsaWdodGdyYXk7XHJcbiAgICBmb250LXdlaWdodDogYm9sZDtcclxufVxyXG5cclxuLmxpbmstbWFpbi1jb2xvciB7XHJcbiAgICBmb250LXdlaWdodDogYm9sZDtcclxufVxyXG5cclxuLmJ0bnMge1xyXG4gICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgd2lkdGg6IDkwJTtcclxuICAgIGJvdHRvbTogMDtcclxufVxyXG5cclxuLmNob29zZS1sZXZlbCB7XHJcbiAgICBtYXJnaW4tdG9wOiA4MHB4O1xyXG4gICAgLmNob29zZS1sZXZlbC10aXRsZSB7XHJcbiAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkO1xyXG4gICAgICAgIHBhZGRpbmc6IDMwcHggMDtcclxuICAgIH1cclxufVxyXG5cclxuLmRlc2lyZWQtZGF0ZSB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbn1cclxuXHJcbi5jaG9vc2UtcmVhc29uIHtcclxuICAgIHdpZHRoOiA3MCU7XHJcbiAgICBtYXJnaW4tbGVmdDogMjAlO1xyXG59XHJcblxyXG4uY2hvb3NlLWFyZWEge1xyXG4gICAgd2lkdGg6IDIwJTtcclxuICAgIG1hcmdpbi1sZWZ0OiA0MCU7XHJcbn1cclxuXHJcbi5lbmQge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xyXG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAuZW5kLW1zZyB7XHJcbiAgICAgICAgbWFyZ2luLXRvcDogMzBweDtcclxuICAgICAgICBmb250LXdlaWdodDogYm9sZDtcclxuICAgIH1cclxufVxyXG5cclxuLm8tMDV7XHJcbiAgICBvcGFjaXR5OiAwLjU7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["ClassroomType", "Level", "CircleType", "GoalReason", "Category", "SubSink", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r0", "stepCircleLines", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "goal_r3", "language", "ɵɵlistener", "CreateGoalComponent_span_5_div_1_div_1_div_3_Template_div_click_2_listener", "ɵɵrestoreView", "_r4", "ɵɵnextContext", "ɵɵresetView", "answerConfirm", "CreateGoalComponent_span_5_div_1_div_1_div_3_Template_div_click_4_listener", "CreateGoalComponent_span_5_div_1_div_1_Template_p_dropdown_onChange_1_listener", "$event", "_r2", "changeIndex", "ɵɵtemplate", "CreateGoalComponent_span_5_div_1_div_1_ng_template_2_Template", "CreateGoalComponent_span_5_div_1_div_1_div_3_Template", "goals", "showConfirm", "CreateGoalComponent_span_5_div_1_div_1_Template", "length", "CreateGoalComponent_span_5_div_1_Template", "CreateGoalComponent_span_5_div_2_Template", "classroom_r6", "CreateGoalComponent_div_11_div_1_Template_p_dropdown_onChange_1_listener", "_r5", "chooseClassroom", "CreateGoalComponent_div_11_div_1_ng_template_2_Template", "userClassrooms", "CreateGoalComponent_div_11_div_1_Template", "CreateGoalComponent_app_circle_line_12_Template_app_circle_line_circleChoice_0_listener", "_r7", "desiredLevelChoice", "currentLevelCircleLines", "month_r9", "month", "ɵɵtwoWayListener", "CreateGoalComponent_div_13_Template_p_dropdown_ngModelChange_1_listener", "_r8", "ɵɵtwoWayBindingSet", "monthSelected", "CreateGoalComponent_div_13_Template_p_dropdown_onChange_1_listener", "choose<PERSON>ont<PERSON>", "CreateGoalComponent_div_13_ng_template_2_Template", "CreateGoalComponent_div_13_Template_p_dropdown_ngModelChange_3_listener", "yearChosen", "CreateGoalComponent_div_13_Template_p_dropdown_onChange_3_listener", "chooseYear", "months", "ɵɵtwoWayProperty", "years", "CreateGoalComponent_div_14_Template_p_dropdown_ngModelChange_1_listener", "_r10", "hoursWeeklyChosen", "CreateGoalComponent_div_14_Template_p_dropdown_onChange_1_listener", "chooseHours", "hours", "CreateGoalComponent_div_15_div_1_Template_ejs_checkbox_change_1_listener", "reason_r12", "_r11", "$implicit", "chooseReason", "getIsReasonChecked", "ɵɵtextInterpolate1", "CreateGoalComponent_div_15_div_1_Template", "reasons", "CreateGoalComponent_div_16_div_1_Template_ejs_checkbox_change_1_listener", "area_r14", "_r13", "chooseArea", "getIsAreaChecked", "CreateGoalComponent_div_16_div_1_Template", "areas", "CreateGoalComponent_div_17_div_3_Template_div_click_0_listener", "_r16", "changeStep", "CreateGoalComponent_div_17_div_4_Template_div_click_0_listener", "_r17", "submit", "CreateGoalComponent_div_17_Template_div_click_1_listener", "_r15", "CreateGoalComponent_div_17_div_3_Template", "CreateGoalComponent_div_17_div_4_Template", "currentStep", "CreateGoalComponent", "constructor", "toastService", "generalService", "classroomService", "goalService", "authService", "editIndex", "subs", "levels", "number", "BOOST", "CERTIFICATE", "COMMUNICATE", "CULTURE", "EDUCATION", "HOBBY", "MOVING", "TRAVELING", "GRAMMAR", "LISTENING", "READING", "REVISION", "SPEAKING", "TEST", "VOCABULARY", "WRITING", "currentLevelChosen", "desiredLevelChosen", "month<PERSON><PERSON><PERSON>", "date<PERSON><PERSON><PERSON>", "Date", "reasonsChosen", "areasChosen", "languageChosen", "classroomChoosen", "choosenGoalId", "indexToEdit", "ngOnInit", "ngAfterViewInit", "initiateGoalToEdit", "sink", "getUserClassrooms", "subscribe", "res", "extraTexts", "extraTextsml", "A1", "A2", "B1", "B2", "C1", "C2", "i", "background", "color", "circleLine", "text", "toString", "extraText", "extraTextML", "lineStyle", "width", "createGoal", "nativeElement", "clientWidth", "type", "GENERAL", "push", "levelsEmpty", "ngOnDestroy", "unsubscribe", "id", "currentLevel", "desiredLevel", "desiredAchieveDate", "hoursWeekly", "filter", "el", "getMonth", "getFullYear", "currentLevelIndex", "findIndex", "desiredLevelIndex", "newCircleLines", "reason", "some", "area", "PAID", "CURRENT_LEVEL", "next", "undefined", "setShowToastmessage", "severity", "summary", "detail", "event", "value", "activeLevel", "currentLevelChoice", "line", "circle", "parseInt", "style", "opacity", "confirm", "update", "console", "log", "goal", "startingLevel", "teacher", "studentId", "getUserId", "updateGoal", "setUpdateListener", "_", "ɵɵdirectiveInject", "i1", "ToastService", "i2", "GeneralService", "i3", "ClassroomService", "i4", "GoalService", "i5", "AuthService", "_2", "selectors", "viewQuery", "CreateGoalComponent_Query", "rf", "ctx", "CreateGoalComponent_app_circle_line_2_Template", "CreateGoalComponent_span_5_Template", "CreateGoalComponent_span_6_Template", "CreateGoalComponent_span_7_Template", "CreateGoalComponent_span_8_Template", "CreateGoalComponent_span_9_Template", "CreateGoalComponent_span_10_Template", "CreateGoalComponent_div_11_Template", "CreateGoalComponent_app_circle_line_12_Template", "CreateGoalComponent_div_13_Template", "CreateGoalComponent_div_14_Template", "CreateGoalComponent_div_15_Template", "CreateGoalComponent_div_16_Template", "CreateGoalComponent_div_17_Template", "CreateGoalComponent_div_18_Template"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\goal\\create-goal\\create-goal.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\goal\\create-goal\\create-goal.component.html"], "sourcesContent": ["import { Component, Input, OnInit, ViewChild } from '@angular/core';\r\nimport { Classroom, ClassroomType, Level } from 'src/app/core/models/classroom.model';\r\nimport { CircleLine, CircleType, GoalReason, StudentGoal } from 'src/app/core/models/goal.model';\r\nimport { Category } from 'src/app/core/models/homework.model';\r\nimport { User } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { GoalService } from 'src/app/core/services/goal.service';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { SubSink } from 'subsink';\r\nexport interface Month {\r\n  number: string,\r\n  month: string\r\n}\r\n@Component({\r\n  selector: 'app-create-goal',\r\n  templateUrl: './create-goal.component.html',\r\n  styleUrls: ['./create-goal.component.scss']\r\n})\r\nexport class CreateGoalComponent implements OnInit {\r\n  @Input() goals: StudentGoal[] = []\r\n  @ViewChild('createGoal') public createGoal: any;\r\n  editIndex: number = 0;\r\n  showConfirm: boolean = false;\r\n  private subs = new SubSink();\r\n  public stepCircleLines: CircleLine[] = [];\r\n  public userClassrooms: Classroom[] = [];\r\n  public currentLevelCircleLines: CircleLine[] = [];\r\n  public currentStep: number = 0;\r\n  public levels: Level[] = [];\r\n  public months: Month[] = [{ number: \"0\", month: \"January\" }, { number: \"1\", month: \"February\" }, { number: \"2\", month: \"March\" }, { number: \"3\", month: \"April\" }, { number: \"4\", month: \"May\" }, { number: \"5\", month: \"June\" }, { number: \"6\", month: \"July\" }, { number: \"7\", month: \"August\" }, { number: \"8\", month: \"September\" }, { number: \"9\", month: \"October\" }, { number: \"10\", month: \"November\" }, { number: \"11\", month: \"December\" }];\r\n  public years = [\"2022\", \"2023\", \"2024\"];\r\n  public hours = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];\r\n  public reasons = [GoalReason.BOOST, GoalReason.CERTIFICATE, GoalReason.COMMUNICATE, GoalReason.CULTURE, GoalReason.EDUCATION, GoalReason.HOBBY, GoalReason.MOVING, GoalReason.TRAVELING];\r\n  public areas = [Category.GRAMMAR, Category.LISTENING, Category.READING, Category.REVISION, Category.SPEAKING, Category.TEST, Category.VOCABULARY, Category.WRITING]\r\n  private currentLevelChosen: Level = {} as Level\r\n  private desiredLevelChosen: Level = {} as Level\r\n  private monthChosen: string = \"01\";\r\n  monthSelected: Month = {} as Month\r\n  private yearChosen: string = \"\";\r\n  private dateChosen: Date = new Date();\r\n  private hoursWeeklyChosen: number = 0;\r\n  private reasonsChosen: GoalReason[] = [];\r\n  private areasChosen: Category[] = [];\r\n  private languageChosen: string = \"\";\r\n  private classroomChoosen: Classroom = {} as Classroom;\r\n  private choosenGoalId: string  = \"\";\r\n  constructor(\r\n    private toastService: ToastService,\r\n    private generalService: GeneralService,\r\n    private classroomService: ClassroomService,\r\n    private goalService: GoalService,\r\n    private authService: AuthService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n\r\n\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    if (this.goals.length > 0) {\r\n      this.initiateGoalToEdit(0);\r\n    }\r\n    this.subs.sink = this.getUserClassrooms().subscribe(res => {\r\n      this.userClassrooms = res;\r\n    })\r\n    let extraTexts = [\"Classroom\", \"Desired Level\", \"When\", \"Lesson Hours\", \"Why\", \"Improvement\"]\r\n    let extraTextsml = [\"-30px\", \"-38px\", \"-9px\", \"-37px\", \"-1px\", \"-43px\"]\r\n    this.levels = [Level.A1, Level.A2, Level.B1, Level.B2, Level.C1, Level.C2];\r\n    for (let i = 0; i < 6; i++) {\r\n      let background = i == 0 ? '#a4a2e6' : 'white';\r\n      let color = i == 0 ? 'white' : '#a4a2e6';\r\n\r\n      let circleLine: CircleLine = {\r\n        text: (i + 1).toString(),\r\n        extraText: extraTexts[i],\r\n        extraTextML: extraTextsml[i],\r\n        lineStyle: '1px dashed lightgray',\r\n        background: background,\r\n        color: color,\r\n        width: this.createGoal.nativeElement!.clientWidth / (5) - 21 + 'px',\r\n        type: CircleType.GENERAL\r\n      }\r\n      this.stepCircleLines.push(circleLine)\r\n      circleLine = this.levelsEmpty(i);\r\n      if (this.goals.length == 0)\r\n        this.currentLevelCircleLines.push(circleLine)\r\n    }\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  initiateGoalToEdit(editIndex: number) {\r\n    this.choosenGoalId = this.goals[editIndex].id!\r\n    this.currentLevelChosen = this.goals[editIndex].currentLevel;\r\n    this.desiredLevelChosen = this.goals[editIndex].desiredLevel\r\n    this.dateChosen = this.goals[editIndex].desiredAchieveDate;\r\n    this.hoursWeeklyChosen = this.goals[editIndex].hoursWeekly\r\n    this.reasonsChosen = this.goals[editIndex].reasons\r\n    this.areasChosen = this.goals[editIndex].areas;\r\n    this.languageChosen = this.goals[editIndex].language\r\n    this.monthSelected = this.months.filter(el => el.number == this.goals[editIndex]?.desiredAchieveDate.getMonth().toString())[0]\r\n    this.yearChosen = this.goals[editIndex]?.desiredAchieveDate.getFullYear().toString()\r\n    let currentLevel = this.goals[editIndex].currentLevel;\r\n    let desiredLevel = this.goals[editIndex].desiredLevel;\r\n    let levels: Level[] = [Level.A1, Level.A2, Level.B1, Level.B2, Level.C1, Level.C2];\r\n    let currentLevelIndex = levels.findIndex(el => el === currentLevel);\r\n    let desiredLevelIndex = levels.findIndex(el => el === desiredLevel);\r\n    let newCircleLines: CircleLine[] = [];\r\n    this.currentLevelCircleLines = []\r\n    for (let i = 0; i <= currentLevelIndex; i++) {\r\n      let lineStyle = i == currentLevelIndex ? '1px dashed lightgray' : '1px solid lightgray'\r\n      let circleLine: CircleLine = {\r\n        text: levels[i],\r\n        lineStyle,\r\n        background: \"#a4a2e6\",\r\n        color: \"white\",\r\n        type: CircleType.GENERAL,\r\n        width: this.createGoal.nativeElement!.clientWidth / (5) - 21 + 'px',\r\n\r\n      }\r\n      newCircleLines.push(circleLine)\r\n      this.currentLevelCircleLines.push(circleLine)\r\n    }\r\n    for (let i = currentLevelIndex + 1; i <= desiredLevelIndex; i++) {\r\n      let circleLine: CircleLine = {\r\n        text: levels[i],\r\n        lineStyle: '1px dashed lightgray',\r\n        background: \"#2d2a4b\",\r\n        color: \"white\",\r\n        type: CircleType.GENERAL,\r\n        width: this.createGoal.nativeElement!.clientWidth / (5) - 21 + 'px',\r\n      }\r\n      newCircleLines.push(circleLine)\r\n      this.currentLevelCircleLines.push(circleLine)\r\n    }\r\n    for (let i = desiredLevelIndex + 1; i < 6; i++) {\r\n      let circleLine: CircleLine = {\r\n        text: levels[i],\r\n        lineStyle: '1px dashed lightgray',\r\n        background: \"white\",\r\n        color: \"#a4a2e6\",\r\n        type: CircleType.GENERAL,\r\n        width: this.createGoal.nativeElement!.clientWidth / (5) - 21 + 'px',\r\n      }\r\n      newCircleLines.push(circleLine)\r\n      this.currentLevelCircleLines.push(circleLine)\r\n    }\r\n  }\r\n\r\n  getIsReasonChecked(reason: GoalReason) {\r\n    return this.reasonsChosen.some(el => el == reason)\r\n  }\r\n\r\n  getIsAreaChecked(area: Category) {\r\n    return this.areasChosen.some(el => el == area)\r\n  }\r\n\r\n  getUserClassrooms() {\r\n    return this.classroomService.getUserClassrooms(ClassroomType.PAID)\r\n  }\r\n\r\n  levelsEmpty(i: number) {\r\n    return {\r\n      text: this.levels[i],\r\n      lineStyle: '1px dashed lightgray',\r\n      background: \"white\",\r\n      color: '#a4a2e6',\r\n      width: this.createGoal.nativeElement!.clientWidth / (5) - 21 + 'px',\r\n      type: CircleType.CURRENT_LEVEL\r\n    }\r\n  }\r\n\r\n  changeStep(next: boolean) {\r\n    if (this.currentStep == 0 && this.currentLevelChosen.length == undefined) {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'info',\r\n        summary: '',\r\n        detail: 'Please choose a language to set a goal'\r\n      });\r\n      return;\r\n    }\r\n    if (this.currentStep == 1 && this.desiredLevelChosen.length == undefined) {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'info',\r\n        summary: '',\r\n        detail: 'Please choose a level to continue'\r\n      });\r\n      return;\r\n    }\r\n    if (next) {\r\n      if (this.currentStep < 5) {\r\n        this.stepCircleLines[this.currentStep].lineStyle = '1px solid lightgray';\r\n        this.currentStep++\r\n        this.stepCircleLines[this.currentStep].background = '#a4a2e6';\r\n        this.stepCircleLines[this.currentStep].color = 'white';\r\n      }\r\n    } else {\r\n      if (this.currentStep > 0) {\r\n        this.stepCircleLines[this.currentStep].background = 'white';\r\n        this.stepCircleLines[this.currentStep].color = '#a4a2e6';\r\n        this.currentStep--;\r\n        this.stepCircleLines[this.currentStep].lineStyle = '1px dashed lightgray';\r\n      }\r\n    }\r\n  }\r\n\r\n  chooseClassroom(event: { value: Classroom }) {\r\n    this.currentLevelChosen = event.value.activeLevel\r\n    this.languageChosen = event.value.language!\r\n    this.classroomChoosen = event.value\r\n    this.currentLevelChoice()\r\n    // TODO GETCLASSROOMTEACHER\r\n  }\r\n\r\n  currentLevelChoice() {\r\n    let currentLevelIndex = this.levels.findIndex(el => el == this.currentLevelChosen);\r\n    for (let i = 0; i < 6; i++) {\r\n      this.currentLevelCircleLines[i] = this.levelsEmpty(i);\r\n    }\r\n    for (let i = 0; i <= currentLevelIndex; i++) {\r\n      let line = i < currentLevelIndex ? '1px solid lightgray' : '1px dashed lightgray'\r\n      this.currentLevelCircleLines[i].background = '#a4a2e6'\r\n      this.currentLevelCircleLines[i].color = 'white'\r\n      this.currentLevelCircleLines[i].lineStyle = line\r\n    }\r\n  }\r\n\r\n  desiredLevelChoice(event: { circle: CircleLine }) {\r\n    if (event.circle.text <= this.currentLevelChosen) {\r\n      \r\n      this.toastService.setShowToastmessage({\r\n        severity: 'info',\r\n        summary: '',\r\n        detail: 'Please choose level greater than the current one'\r\n      });\r\n    } else {\r\n      this.desiredLevelChosen = event.circle.text as Level;\r\n      let desiredLevelIndex = this.levels.findIndex(el => el === event.circle.text);\r\n      let currentLevelIndex = this.levels.findIndex(el => el === this.currentLevelChosen);\r\n      for (let i = currentLevelIndex + 1; i < 6; i++) {\r\n        this.currentLevelCircleLines[i] = this.levelsEmpty(i);\r\n      }\r\n      for (let i = currentLevelIndex + 1; i <= desiredLevelIndex; i++) {\r\n        let line = i < desiredLevelIndex ? '1px solid lightgray' : '1px dashed lightgray'\r\n        this.currentLevelCircleLines[i].background = '#2d2a4b'\r\n        this.currentLevelCircleLines[i].color = 'white'\r\n        this.currentLevelCircleLines[i].lineStyle = line\r\n      }\r\n    }\r\n  }\r\n\r\n  chooseMonth(event: any) {\r\n    this.monthChosen = (parseInt(event.value.number) + 1).toString()\r\n  }\r\n\r\n  chooseYear(event: any) {\r\n    this.yearChosen = event.value\r\n  }\r\n\r\n  chooseHours(event: any) {\r\n    this.hoursWeeklyChosen = event.value;\r\n  }\r\n\r\n  chooseReason(reason: GoalReason) {\r\n    if (this.reasonsChosen.some(el => el == reason)) {\r\n      this.reasonsChosen = this.reasonsChosen.filter(el => el !== reason)\r\n    } else {\r\n      this.reasonsChosen.push(reason)\r\n    }\r\n  }\r\n\r\n  chooseArea(area: Category) {\r\n    if (this.areasChosen.some(el => el == area)) {\r\n      this.areasChosen = this.areasChosen.filter(el => el !== area)\r\n    } else {\r\n      this.areasChosen.push(area)\r\n    }\r\n  }\r\n  indexToEdit: number = 0;\r\n  changeIndex(event: any) {\r\n    this.indexToEdit = this.goals.findIndex(((el: StudentGoal) => el.language == event.value.language));\r\n    if (this.currentStep == 0) {\r\n      this.editIndex = this.indexToEdit;\r\n      this.initiateGoalToEdit(this.indexToEdit);\r\n    } else {\r\n      this.showConfirm = true;\r\n      this.createGoal.nativeElement!.style.opacity = '0.5';\r\n    }\r\n  }\r\n\r\n  answerConfirm(confirm: boolean) {\r\n    this.createGoal.nativeElement!.style.opacity = '1';\r\n    this.showConfirm = false;\r\n    if (confirm) {\r\n      this.submit(true)\r\n    } else {\r\n      \r\n      this.toastService.setShowToastmessage({\r\n        severity: 'info',\r\n        summary: '',\r\n        detail: 'Your edit has not been saved'\r\n      });\r\n    }\r\n    this.currentStep = 0;\r\n    this.editIndex = this.indexToEdit;\r\n    this.initiateGoalToEdit(this.indexToEdit);\r\n  }\r\n\r\n  submit(update?: boolean) {\r\n    this.currentStep = 6;\r\n    console.log(this.monthChosen + \"/01/\" + this.yearChosen)\r\n    this.dateChosen = new Date(this.monthChosen + \"/01/\" + this.yearChosen);\r\n    console.log(this.dateChosen)\r\n\r\n    let goal: StudentGoal = {\r\n      startingLevel: this.currentLevelChosen,\r\n      currentLevel: this.currentLevelChosen,\r\n      desiredLevel: this.desiredLevelChosen,\r\n      desiredAchieveDate: this.dateChosen,\r\n      // totalWeeks: this.generalService.getDatesDiff(new Date(), this.dateChosen).weeks,\r\n      hoursWeekly: this.hoursWeeklyChosen,\r\n      // hoursCompleted: [],\r\n      reasons: this.reasonsChosen,\r\n      areas: this.areasChosen,\r\n      language: this.languageChosen,\r\n      teacher: this.classroomChoosen.teacher!,\r\n      // classroom: this.classroomChoosen,\r\n      studentId: this.authService.getUserId()!\r\n    }\r\n    console.log(goal)\r\n    if (this.goals.length > 0) {\r\n      this.goalService.updateGoal(goal, this.choosenGoalId).subscribe(res => {\r\n        console.log(res)\r\n        this.goalService.setUpdateListener(true)\r\n      })\r\n      \r\n      this.toastService.setShowToastmessage({\r\n        severity: 'info',\r\n        summary: '',\r\n        detail: 'Your goal has been successfully updated.'\r\n      });\r\n    } else {\r\n      //create\r\n      this.goalService.createGoal(goal).subscribe(res => {\r\n        console.log(res)\r\n        this.goalService.setUpdateListener(true)\r\n      })\r\n      \r\n      this.toastService.setShowToastmessage({\r\n        severity: 'info',\r\n        summary: '',\r\n        detail: 'Your goal has been successfully created. Good luck achieving it!'\r\n      });\r\n    }\r\n  }\r\n\r\n}\r\n", "<div #createGoal id=\"create-goal-2\" class=\"create-goal\">\r\n    <app-circle-line *ngIf=\"currentStep<6\" [circles]=\"stepCircleLines\"></app-circle-line>\r\n\r\n    <div class=\"choose-level\">\r\n        <div class=\"choose-level-title\">\r\n            <span *ngIf=\"currentStep==0\">\r\n                <div *ngIf=\"goals.length > 0\">\r\n                    <!-- Click next to edit goals to choose goal to edit -->\r\n                    <div *ngIf=\"goals.length > 0\" style=\"display:flex; justify-content: center;\">\r\n                        <p-dropdown placeholder=\"Choose Goal\" [options]=\"goals\" (onChange)=\"changeIndex($event)\" optionLabel=\"language\">\r\n                            <ng-template let-goal pTemplate=\"item\">\r\n                                <div class=\"country-item\">\r\n                                    <div class=\"country-name\">{{goal.language}}</div>\r\n                                </div>\r\n                            </ng-template>\r\n                        </p-dropdown>\r\n                        <div *ngIf=\"showConfirm\" style=\"display:flex; align-items: center; margin-left:15px\">\r\n                            Save current edit?\r\n                            <div class=\"light-purple-button\" style=\"margin-left:15px\" (click)=\"answerConfirm(false)\">\r\n                                no\r\n                            </div>\r\n                            <div class=\"main-color-button\" style=\"margin-left:15px\" (click)=\"answerConfirm(true)\">\r\n                                yes\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div *ngIf=\"goals.length == 0\">\r\n                    Choose classroom you want to create a goal for\r\n                </div>\r\n            </span>\r\n            <span *ngIf=\"currentStep==1\">\r\n                Choose level desired to reach\r\n            </span>\r\n            <span *ngIf=\"currentStep==2\">\r\n                When would you like to achieve your goal?\r\n            </span>\r\n            <span *ngIf=\"currentStep==3\">\r\n                How many lesson hours would you like to take per week?\r\n            </span>\r\n            <span *ngIf=\"currentStep==4\">\r\n                Why do you want to study?\r\n            </span>\r\n            <span *ngIf=\"currentStep==5\">\r\n                Which areas would you like to improve?\r\n            </span>\r\n        </div>\r\n        <div *ngIf=\"currentStep==0\" class=\"desired-date\">\r\n            <div *ngIf=\"goals.length == 0\">\r\n                <p-dropdown placeholder=\"Choose Language\" [options]=\"userClassrooms\"\r\n                    (onChange)=\"chooseClassroom($event)\" optionLabel=\"language\">\r\n                    <ng-template let-classroom pTemplate=\"item\">\r\n                        <div class=\"country-item\">\r\n                            <div class=\"country-name\">{{classroom.language}}</div>\r\n                        </div>\r\n                    </ng-template>\r\n                </p-dropdown>\r\n            </div>\r\n        </div>\r\n\r\n        <app-circle-line *ngIf=\"currentStep==1\" (circleChoice)=\"desiredLevelChoice($event)\" [allowHoverMain]=\"true\"\r\n            [circles]=\"currentLevelCircleLines\"></app-circle-line>\r\n\r\n        <div *ngIf=\"currentStep==2\" class=\"desired-date\">\r\n            <p-dropdown placeholder=\"Choose Month\" [options]=\"months\" [(ngModel)]=\"monthSelected\"\r\n                (onChange)=\"chooseMonth($event)\" optionLabel=\"month\">\r\n                <ng-template let-month pTemplate=\"item\">\r\n                    <div class=\"country-item\">\r\n                        <div class=\"country-name\">{{month.month}}</div>\r\n                    </div>\r\n                </ng-template>\r\n            </p-dropdown>\r\n            <p-dropdown placeholder=\"Choose Year\" [options]=\"years\" [(ngModel)]=\"yearChosen\"\r\n                (onChange)=\"chooseYear($event)\">\r\n            </p-dropdown>\r\n        </div>\r\n\r\n        <div *ngIf=\"currentStep==3\" class=\"desired-date\">\r\n            <p-dropdown placeholder=\"Choose Hours\" [options]=\"hours\" [(ngModel)]=\"hoursWeeklyChosen\"\r\n                (onChange)=\"chooseHours($event)\">\r\n            </p-dropdown>\r\n        </div>\r\n\r\n        <div *ngIf=\"currentStep==4\" class=\"choose-reason\">\r\n            <div *ngFor=\"let reason of reasons\">\r\n                <ejs-checkbox (change)=\"chooseReason(reason)\" [checked]=\"getIsReasonChecked(reason)\"></ejs-checkbox>\r\n                {{reason}}\r\n            </div>\r\n        </div>\r\n\r\n        <div *ngIf=\"currentStep==5\" class=\"choose-area\">\r\n            <!-- <div class=\"choose-area\"> -->\r\n            <div *ngFor=\"let area of areas\">\r\n                <ejs-checkbox (change)=\"chooseArea(area)\" [checked]=\"getIsAreaChecked(area)\"></ejs-checkbox> {{area}}\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"btns\" *ngIf=\"currentStep < 6\">\r\n        <div [ngClass]=\"currentStep>0? 'link-main-color': 'inactive'\" (click)=\"changeStep(false)\">\r\n            BACK\r\n        </div>\r\n        <div *ngIf=\"currentStep<5\" class='link-main-color' (click)=\"changeStep(true)\">\r\n            NEXT\r\n        </div>\r\n        <div *ngIf=\"currentStep==5\" class='light-purple-button' (click)=\"submit()\">\r\n            SUBMIT\r\n        </div>\r\n    </div>\r\n\r\n    <div *ngIf=\"currentStep==6\" class=\"end\">\r\n        <img src=\"/assets/icons/big-check.svg\">\r\n        <div class=\"end-msg\">\r\n            All set! Start Learning!\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": "AACA,SAAoBA,aAAa,EAAEC,KAAK,QAAQ,qCAAqC;AACrF,SAAqBC,UAAU,EAAEC,UAAU,QAAqB,gCAAgC;AAChG,SAASC,QAAQ,QAAQ,oCAAoC;AAO7D,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;ICT7BC,EAAA,CAAAC,SAAA,0BAAqF;;;;IAA9CD,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAC,eAAA,CAA2B;;;;;IAWlCJ,EADJ,CAAAK,cAAA,cAA0B,cACI;IAAAL,EAAA,CAAAM,MAAA,GAAiB;IAC/CN,EAD+C,CAAAO,YAAA,EAAM,EAC/C;;;;IADwBP,EAAA,CAAAQ,SAAA,GAAiB;IAAjBR,EAAA,CAAAS,iBAAA,CAAAC,OAAA,CAAAC,QAAA,CAAiB;;;;;;IAIvDX,EAAA,CAAAK,cAAA,cAAqF;IACjFL,EAAA,CAAAM,MAAA,2BACA;IAAAN,EAAA,CAAAK,cAAA,cAAyF;IAA/BL,EAAA,CAAAY,UAAA,mBAAAC,2EAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAZ,MAAA,GAAAH,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASd,MAAA,CAAAe,aAAA,CAAc,KAAK,CAAC;IAAA,EAAC;IACpFlB,EAAA,CAAAM,MAAA,WACJ;IAAAN,EAAA,CAAAO,YAAA,EAAM;IACNP,EAAA,CAAAK,cAAA,cAAsF;IAA9BL,EAAA,CAAAY,UAAA,mBAAAO,2EAAA;MAAAnB,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAZ,MAAA,GAAAH,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASd,MAAA,CAAAe,aAAA,CAAc,IAAI,CAAC;IAAA,EAAC;IACjFlB,EAAA,CAAAM,MAAA,YACJ;IACJN,EADI,CAAAO,YAAA,EAAM,EACJ;;;;;;IAfNP,EADJ,CAAAK,cAAA,cAA6E,qBACuC;IAAxDL,EAAA,CAAAY,UAAA,sBAAAQ,+EAAAC,MAAA;MAAArB,EAAA,CAAAc,aAAA,CAAAQ,GAAA;MAAA,MAAAnB,MAAA,GAAAH,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAYd,MAAA,CAAAoB,WAAA,CAAAF,MAAA,CAAmB;IAAA,EAAC;IACpFrB,EAAA,CAAAwB,UAAA,IAAAC,6DAAA,0BAAuC;IAK3CzB,EAAA,CAAAO,YAAA,EAAa;IACbP,EAAA,CAAAwB,UAAA,IAAAE,qDAAA,kBAAqF;IASzF1B,EAAA,CAAAO,YAAA,EAAM;;;;IAhBoCP,EAAA,CAAAQ,SAAA,EAAiB;IAAjBR,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAwB,KAAA,CAAiB;IAOjD3B,EAAA,CAAAQ,SAAA,GAAiB;IAAjBR,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAyB,WAAA,CAAiB;;;;;IAV/B5B,EAAA,CAAAK,cAAA,UAA8B;IAE1BL,EAAA,CAAAwB,UAAA,IAAAK,+CAAA,kBAA6E;IAkBjF7B,EAAA,CAAAO,YAAA,EAAM;;;;IAlBIP,EAAA,CAAAQ,SAAA,EAAsB;IAAtBR,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAwB,KAAA,CAAAG,MAAA,KAAsB;;;;;IAmBhC9B,EAAA,CAAAK,cAAA,UAA+B;IAC3BL,EAAA,CAAAM,MAAA,uDACJ;IAAAN,EAAA,CAAAO,YAAA,EAAM;;;;;IAxBVP,EAAA,CAAAK,cAAA,WAA6B;IAsBzBL,EArBA,CAAAwB,UAAA,IAAAO,yCAAA,iBAA8B,IAAAC,yCAAA,iBAqBC;IAGnChC,EAAA,CAAAO,YAAA,EAAO;;;;IAxBGP,EAAA,CAAAQ,SAAA,EAAsB;IAAtBR,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAwB,KAAA,CAAAG,MAAA,KAAsB;IAqBtB9B,EAAA,CAAAQ,SAAA,EAAuB;IAAvBR,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAwB,KAAA,CAAAG,MAAA,MAAuB;;;;;IAIjC9B,EAAA,CAAAK,cAAA,WAA6B;IACzBL,EAAA,CAAAM,MAAA,sCACJ;IAAAN,EAAA,CAAAO,YAAA,EAAO;;;;;IACPP,EAAA,CAAAK,cAAA,WAA6B;IACzBL,EAAA,CAAAM,MAAA,kDACJ;IAAAN,EAAA,CAAAO,YAAA,EAAO;;;;;IACPP,EAAA,CAAAK,cAAA,WAA6B;IACzBL,EAAA,CAAAM,MAAA,+DACJ;IAAAN,EAAA,CAAAO,YAAA,EAAO;;;;;IACPP,EAAA,CAAAK,cAAA,WAA6B;IACzBL,EAAA,CAAAM,MAAA,kCACJ;IAAAN,EAAA,CAAAO,YAAA,EAAO;;;;;IACPP,EAAA,CAAAK,cAAA,WAA6B;IACzBL,EAAA,CAAAM,MAAA,+CACJ;IAAAN,EAAA,CAAAO,YAAA,EAAO;;;;;IAQSP,EADJ,CAAAK,cAAA,cAA0B,cACI;IAAAL,EAAA,CAAAM,MAAA,GAAsB;IACpDN,EADoD,CAAAO,YAAA,EAAM,EACpD;;;;IADwBP,EAAA,CAAAQ,SAAA,GAAsB;IAAtBR,EAAA,CAAAS,iBAAA,CAAAwB,YAAA,CAAAtB,QAAA,CAAsB;;;;;;IAJ5DX,EADJ,CAAAK,cAAA,UAA+B,qBAEqC;IAA5DL,EAAA,CAAAY,UAAA,sBAAAsB,yEAAAb,MAAA;MAAArB,EAAA,CAAAc,aAAA,CAAAqB,GAAA;MAAA,MAAAhC,MAAA,GAAAH,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAYd,MAAA,CAAAiC,eAAA,CAAAf,MAAA,CAAuB;IAAA,EAAC;IACpCrB,EAAA,CAAAwB,UAAA,IAAAa,uDAAA,0BAA4C;IAMpDrC,EADI,CAAAO,YAAA,EAAa,EACX;;;;IARwCP,EAAA,CAAAQ,SAAA,EAA0B;IAA1BR,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAmC,cAAA,CAA0B;;;;;IAF5EtC,EAAA,CAAAK,cAAA,cAAiD;IAC7CL,EAAA,CAAAwB,UAAA,IAAAe,yCAAA,iBAA+B;IAUnCvC,EAAA,CAAAO,YAAA,EAAM;;;;IAVIP,EAAA,CAAAQ,SAAA,EAAuB;IAAvBR,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAwB,KAAA,CAAAG,MAAA,MAAuB;;;;;;IAYjC9B,EAAA,CAAAK,cAAA,0BACwC;IADAL,EAAA,CAAAY,UAAA,0BAAA4B,wFAAAnB,MAAA;MAAArB,EAAA,CAAAc,aAAA,CAAA2B,GAAA;MAAA,MAAAtC,MAAA,GAAAH,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAgBd,MAAA,CAAAuC,kBAAA,CAAArB,MAAA,CAA0B;IAAA,EAAC;IAC3CrB,EAAA,CAAAO,YAAA,EAAkB;;;;IAAtDP,EADgF,CAAAE,UAAA,wBAAuB,YAAAC,MAAA,CAAAwC,uBAAA,CACpE;;;;;IAOvB3C,EADJ,CAAAK,cAAA,cAA0B,cACI;IAAAL,EAAA,CAAAM,MAAA,GAAe;IAC7CN,EAD6C,CAAAO,YAAA,EAAM,EAC7C;;;;IADwBP,EAAA,CAAAQ,SAAA,GAAe;IAAfR,EAAA,CAAAS,iBAAA,CAAAmC,QAAA,CAAAC,KAAA,CAAe;;;;;;IAJrD7C,EADJ,CAAAK,cAAA,cAAiD,qBAEY;IADCL,EAAA,CAAA8C,gBAAA,2BAAAC,wEAAA1B,MAAA;MAAArB,EAAA,CAAAc,aAAA,CAAAkC,GAAA;MAAA,MAAA7C,MAAA,GAAAH,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiD,kBAAA,CAAA9C,MAAA,CAAA+C,aAAA,EAAA7B,MAAA,MAAAlB,MAAA,CAAA+C,aAAA,GAAA7B,MAAA;MAAA,OAAArB,EAAA,CAAAiB,WAAA,CAAAI,MAAA;IAAA,EAA2B;IACjFrB,EAAA,CAAAY,UAAA,sBAAAuC,mEAAA9B,MAAA;MAAArB,EAAA,CAAAc,aAAA,CAAAkC,GAAA;MAAA,MAAA7C,MAAA,GAAAH,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAYd,MAAA,CAAAiD,WAAA,CAAA/B,MAAA,CAAmB;IAAA,EAAC;IAChCrB,EAAA,CAAAwB,UAAA,IAAA6B,iDAAA,0BAAwC;IAK5CrD,EAAA,CAAAO,YAAA,EAAa;IACbP,EAAA,CAAAK,cAAA,qBACoC;IADoBL,EAAA,CAAA8C,gBAAA,2BAAAQ,wEAAAjC,MAAA;MAAArB,EAAA,CAAAc,aAAA,CAAAkC,GAAA;MAAA,MAAA7C,MAAA,GAAAH,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiD,kBAAA,CAAA9C,MAAA,CAAAoD,UAAA,EAAAlC,MAAA,MAAAlB,MAAA,CAAAoD,UAAA,GAAAlC,MAAA;MAAA,OAAArB,EAAA,CAAAiB,WAAA,CAAAI,MAAA;IAAA,EAAwB;IAC5ErB,EAAA,CAAAY,UAAA,sBAAA4C,mEAAAnC,MAAA;MAAArB,EAAA,CAAAc,aAAA,CAAAkC,GAAA;MAAA,MAAA7C,MAAA,GAAAH,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAYd,MAAA,CAAAsD,UAAA,CAAApC,MAAA,CAAkB;IAAA,EAAC;IAEvCrB,EADI,CAAAO,YAAA,EAAa,EACX;;;;IAXqCP,EAAA,CAAAQ,SAAA,EAAkB;IAAlBR,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAuD,MAAA,CAAkB;IAAC1D,EAAA,CAAA2D,gBAAA,YAAAxD,MAAA,CAAA+C,aAAA,CAA2B;IAQ/ClD,EAAA,CAAAQ,SAAA,GAAiB;IAAjBR,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAyD,KAAA,CAAiB;IAAC5D,EAAA,CAAA2D,gBAAA,YAAAxD,MAAA,CAAAoD,UAAA,CAAwB;;;;;;IAMhFvD,EADJ,CAAAK,cAAA,cAAiD,qBAER;IADoBL,EAAA,CAAA8C,gBAAA,2BAAAe,wEAAAxC,MAAA;MAAArB,EAAA,CAAAc,aAAA,CAAAgD,IAAA;MAAA,MAAA3D,MAAA,GAAAH,EAAA,CAAAgB,aAAA;MAAAhB,EAAA,CAAAiD,kBAAA,CAAA9C,MAAA,CAAA4D,iBAAA,EAAA1C,MAAA,MAAAlB,MAAA,CAAA4D,iBAAA,GAAA1C,MAAA;MAAA,OAAArB,EAAA,CAAAiB,WAAA,CAAAI,MAAA;IAAA,EAA+B;IACpFrB,EAAA,CAAAY,UAAA,sBAAAoD,mEAAA3C,MAAA;MAAArB,EAAA,CAAAc,aAAA,CAAAgD,IAAA;MAAA,MAAA3D,MAAA,GAAAH,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAYd,MAAA,CAAA8D,WAAA,CAAA5C,MAAA,CAAmB;IAAA,EAAC;IAExCrB,EADI,CAAAO,YAAA,EAAa,EACX;;;;IAHqCP,EAAA,CAAAQ,SAAA,EAAiB;IAAjBR,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA+D,KAAA,CAAiB;IAAClE,EAAA,CAAA2D,gBAAA,YAAAxD,MAAA,CAAA4D,iBAAA,CAA+B;;;;;;IAOpF/D,EADJ,CAAAK,cAAA,UAAoC,uBACqD;IAAvEL,EAAA,CAAAY,UAAA,oBAAAuD,yEAAA;MAAA,MAAAC,UAAA,GAAApE,EAAA,CAAAc,aAAA,CAAAuD,IAAA,EAAAC,SAAA;MAAA,MAAAnE,MAAA,GAAAH,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAUd,MAAA,CAAAoE,YAAA,CAAAH,UAAA,CAAoB;IAAA,EAAC;IAAwCpE,EAAA,CAAAO,YAAA,EAAe;IACpGP,EAAA,CAAAM,MAAA,GACJ;IAAAN,EAAA,CAAAO,YAAA,EAAM;;;;;IAF4CP,EAAA,CAAAQ,SAAA,EAAsC;IAAtCR,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAqE,kBAAA,CAAAJ,UAAA,EAAsC;IACpFpE,EAAA,CAAAQ,SAAA,EACJ;IADIR,EAAA,CAAAyE,kBAAA,MAAAL,UAAA,MACJ;;;;;IAJJpE,EAAA,CAAAK,cAAA,cAAkD;IAC9CL,EAAA,CAAAwB,UAAA,IAAAkD,yCAAA,kBAAoC;IAIxC1E,EAAA,CAAAO,YAAA,EAAM;;;;IAJsBP,EAAA,CAAAQ,SAAA,EAAU;IAAVR,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAwE,OAAA,CAAU;;;;;;IAS9B3E,EADJ,CAAAK,cAAA,UAAgC,uBACiD;IAA/DL,EAAA,CAAAY,UAAA,oBAAAgE,yEAAA;MAAA,MAAAC,QAAA,GAAA7E,EAAA,CAAAc,aAAA,CAAAgE,IAAA,EAAAR,SAAA;MAAA,MAAAnE,MAAA,GAAAH,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAUd,MAAA,CAAA4E,UAAA,CAAAF,QAAA,CAAgB;IAAA,EAAC;IAAoC7E,EAAA,CAAAO,YAAA,EAAe;IAACP,EAAA,CAAAM,MAAA,GACjG;IAAAN,EAAA,CAAAO,YAAA,EAAM;;;;;IADwCP,EAAA,CAAAQ,SAAA,EAAkC;IAAlCR,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA6E,gBAAA,CAAAH,QAAA,EAAkC;IAAiB7E,EAAA,CAAAQ,SAAA,EACjG;IADiGR,EAAA,CAAAyE,kBAAA,MAAAI,QAAA,MACjG;;;;;IAJJ7E,EAAA,CAAAK,cAAA,cAAgD;IAE5CL,EAAA,CAAAwB,UAAA,IAAAyD,yCAAA,kBAAgC;IAGpCjF,EAAA,CAAAO,YAAA,EAAM;;;;IAHoBP,EAAA,CAAAQ,SAAA,EAAQ;IAARR,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA+E,KAAA,CAAQ;;;;;;IAUlClF,EAAA,CAAAK,cAAA,cAA8E;IAA3BL,EAAA,CAAAY,UAAA,mBAAAuE,+DAAA;MAAAnF,EAAA,CAAAc,aAAA,CAAAsE,IAAA;MAAA,MAAAjF,MAAA,GAAAH,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASd,MAAA,CAAAkF,UAAA,CAAW,IAAI,CAAC;IAAA,EAAC;IACzErF,EAAA,CAAAM,MAAA,aACJ;IAAAN,EAAA,CAAAO,YAAA,EAAM;;;;;;IACNP,EAAA,CAAAK,cAAA,cAA2E;IAAnBL,EAAA,CAAAY,UAAA,mBAAA0E,+DAAA;MAAAtF,EAAA,CAAAc,aAAA,CAAAyE,IAAA;MAAA,MAAApF,MAAA,GAAAH,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASd,MAAA,CAAAqF,MAAA,EAAQ;IAAA,EAAC;IACtExF,EAAA,CAAAM,MAAA,eACJ;IAAAN,EAAA,CAAAO,YAAA,EAAM;;;;;;IARNP,EADJ,CAAAK,cAAA,cAA0C,cACoD;IAA5BL,EAAA,CAAAY,UAAA,mBAAA6E,yDAAA;MAAAzF,EAAA,CAAAc,aAAA,CAAA4E,IAAA;MAAA,MAAAvF,MAAA,GAAAH,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASd,MAAA,CAAAkF,UAAA,CAAW,KAAK,CAAC;IAAA,EAAC;IACrFrF,EAAA,CAAAM,MAAA,aACJ;IAAAN,EAAA,CAAAO,YAAA,EAAM;IAINP,EAHA,CAAAwB,UAAA,IAAAmE,yCAAA,kBAA8E,IAAAC,yCAAA,kBAGH;IAG/E5F,EAAA,CAAAO,YAAA,EAAM;;;;IATGP,EAAA,CAAAQ,SAAA,EAAwD;IAAxDR,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA0F,WAAA,sCAAwD;IAGvD7F,EAAA,CAAAQ,SAAA,GAAmB;IAAnBR,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAA0F,WAAA,KAAmB;IAGnB7F,EAAA,CAAAQ,SAAA,EAAoB;IAApBR,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAA0F,WAAA,MAAoB;;;;;IAK9B7F,EAAA,CAAAK,cAAA,cAAwC;IACpCL,EAAA,CAAAC,SAAA,cAAuC;IACvCD,EAAA,CAAAK,cAAA,cAAqB;IACjBL,EAAA,CAAAM,MAAA,iCACJ;IACJN,EADI,CAAAO,YAAA,EAAM,EACJ;;;AD/FV,OAAM,MAAOuF,mBAAmB;EA4B9BC,YACUC,YAA0B,EAC1BC,cAA8B,EAC9BC,gBAAkC,EAClCC,WAAwB,EACxBC,WAAwB;IAJxB,KAAAJ,YAAY,GAAZA,YAAY;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IAhCZ,KAAAzE,KAAK,GAAkB,EAAE;IAElC,KAAA0E,SAAS,GAAW,CAAC;IACrB,KAAAzE,WAAW,GAAY,KAAK;IACpB,KAAA0E,IAAI,GAAG,IAAIvG,OAAO,EAAE;IACrB,KAAAK,eAAe,GAAiB,EAAE;IAClC,KAAAkC,cAAc,GAAgB,EAAE;IAChC,KAAAK,uBAAuB,GAAiB,EAAE;IAC1C,KAAAkD,WAAW,GAAW,CAAC;IACvB,KAAAU,MAAM,GAAY,EAAE;IACpB,KAAA7C,MAAM,GAAY,CAAC;MAAE8C,MAAM,EAAE,GAAG;MAAE3D,KAAK,EAAE;IAAS,CAAE,EAAE;MAAE2D,MAAM,EAAE,GAAG;MAAE3D,KAAK,EAAE;IAAU,CAAE,EAAE;MAAE2D,MAAM,EAAE,GAAG;MAAE3D,KAAK,EAAE;IAAO,CAAE,EAAE;MAAE2D,MAAM,EAAE,GAAG;MAAE3D,KAAK,EAAE;IAAO,CAAE,EAAE;MAAE2D,MAAM,EAAE,GAAG;MAAE3D,KAAK,EAAE;IAAK,CAAE,EAAE;MAAE2D,MAAM,EAAE,GAAG;MAAE3D,KAAK,EAAE;IAAM,CAAE,EAAE;MAAE2D,MAAM,EAAE,GAAG;MAAE3D,KAAK,EAAE;IAAM,CAAE,EAAE;MAAE2D,MAAM,EAAE,GAAG;MAAE3D,KAAK,EAAE;IAAQ,CAAE,EAAE;MAAE2D,MAAM,EAAE,GAAG;MAAE3D,KAAK,EAAE;IAAW,CAAE,EAAE;MAAE2D,MAAM,EAAE,GAAG;MAAE3D,KAAK,EAAE;IAAS,CAAE,EAAE;MAAE2D,MAAM,EAAE,IAAI;MAAE3D,KAAK,EAAE;IAAU,CAAE,EAAE;MAAE2D,MAAM,EAAE,IAAI;MAAE3D,KAAK,EAAE;IAAU,CAAE,CAAC;IAC9a,KAAAe,KAAK,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;IAChC,KAAAM,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;IACvC,KAAAS,OAAO,GAAG,CAAC9E,UAAU,CAAC4G,KAAK,EAAE5G,UAAU,CAAC6G,WAAW,EAAE7G,UAAU,CAAC8G,WAAW,EAAE9G,UAAU,CAAC+G,OAAO,EAAE/G,UAAU,CAACgH,SAAS,EAAEhH,UAAU,CAACiH,KAAK,EAAEjH,UAAU,CAACkH,MAAM,EAAElH,UAAU,CAACmH,SAAS,CAAC;IACjL,KAAA9B,KAAK,GAAG,CAACpF,QAAQ,CAACmH,OAAO,EAAEnH,QAAQ,CAACoH,SAAS,EAAEpH,QAAQ,CAACqH,OAAO,EAAErH,QAAQ,CAACsH,QAAQ,EAAEtH,QAAQ,CAACuH,QAAQ,EAAEvH,QAAQ,CAACwH,IAAI,EAAExH,QAAQ,CAACyH,UAAU,EAAEzH,QAAQ,CAAC0H,OAAO,CAAC;IAC3J,KAAAC,kBAAkB,GAAU,EAAW;IACvC,KAAAC,kBAAkB,GAAU,EAAW;IACvC,KAAAC,WAAW,GAAW,IAAI;IAClC,KAAAzE,aAAa,GAAU,EAAW;IAC1B,KAAAK,UAAU,GAAW,EAAE;IACvB,KAAAqE,UAAU,GAAS,IAAIC,IAAI,EAAE;IAC7B,KAAA9D,iBAAiB,GAAW,CAAC;IAC7B,KAAA+D,aAAa,GAAiB,EAAE;IAChC,KAAAC,WAAW,GAAe,EAAE;IAC5B,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,gBAAgB,GAAc,EAAe;IAC7C,KAAAC,aAAa,GAAY,EAAE;IA4OnC,KAAAC,WAAW,GAAW,CAAC;EArOnB;EAEJC,QAAQA,CAAA,GAGR;EAEAC,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC1G,KAAK,CAACG,MAAM,GAAG,CAAC,EAAE;MACzB,IAAI,CAACwG,kBAAkB,CAAC,CAAC,CAAC;IAC5B;IACA,IAAI,CAAChC,IAAI,CAACiC,IAAI,GAAG,IAAI,CAACC,iBAAiB,EAAE,CAACC,SAAS,CAACC,GAAG,IAAG;MACxD,IAAI,CAACpG,cAAc,GAAGoG,GAAG;IAC3B,CAAC,CAAC;IACF,IAAIC,UAAU,GAAG,CAAC,WAAW,EAAE,eAAe,EAAE,MAAM,EAAE,cAAc,EAAE,KAAK,EAAE,aAAa,CAAC;IAC7F,IAAIC,YAAY,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;IACvE,IAAI,CAACrC,MAAM,GAAG,CAAC5G,KAAK,CAACkJ,EAAE,EAAElJ,KAAK,CAACmJ,EAAE,EAAEnJ,KAAK,CAACoJ,EAAE,EAAEpJ,KAAK,CAACqJ,EAAE,EAAErJ,KAAK,CAACsJ,EAAE,EAAEtJ,KAAK,CAACuJ,EAAE,CAAC;IAC1E,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B,IAAIC,UAAU,GAAGD,CAAC,IAAI,CAAC,GAAG,SAAS,GAAG,OAAO;MAC7C,IAAIE,KAAK,GAAGF,CAAC,IAAI,CAAC,GAAG,OAAO,GAAG,SAAS;MAExC,IAAIG,UAAU,GAAe;QAC3BC,IAAI,EAAE,CAACJ,CAAC,GAAG,CAAC,EAAEK,QAAQ,EAAE;QACxBC,SAAS,EAAEd,UAAU,CAACQ,CAAC,CAAC;QACxBO,WAAW,EAAEd,YAAY,CAACO,CAAC,CAAC;QAC5BQ,SAAS,EAAE,sBAAsB;QACjCP,UAAU,EAAEA,UAAU;QACtBC,KAAK,EAAEA,KAAK;QACZO,KAAK,EAAE,IAAI,CAACC,UAAU,CAACC,aAAc,CAACC,WAAW,GAAI,CAAE,GAAG,EAAE,GAAG,IAAI;QACnEC,IAAI,EAAEpK,UAAU,CAACqK;OAClB;MACD,IAAI,CAAC7J,eAAe,CAAC8J,IAAI,CAACZ,UAAU,CAAC;MACrCA,UAAU,GAAG,IAAI,CAACa,WAAW,CAAChB,CAAC,CAAC;MAChC,IAAI,IAAI,CAACxH,KAAK,CAACG,MAAM,IAAI,CAAC,EACxB,IAAI,CAACa,uBAAuB,CAACuH,IAAI,CAACZ,UAAU,CAAC;IACjD;EACF;EAEAc,WAAWA,CAAA;IACT,IAAI,CAAC9D,IAAI,CAAC+D,WAAW,EAAE;EACzB;EAEA/B,kBAAkBA,CAACjC,SAAiB;IAClC,IAAI,CAAC6B,aAAa,GAAG,IAAI,CAACvG,KAAK,CAAC0E,SAAS,CAAC,CAACiE,EAAG;IAC9C,IAAI,CAAC7C,kBAAkB,GAAG,IAAI,CAAC9F,KAAK,CAAC0E,SAAS,CAAC,CAACkE,YAAY;IAC5D,IAAI,CAAC7C,kBAAkB,GAAG,IAAI,CAAC/F,KAAK,CAAC0E,SAAS,CAAC,CAACmE,YAAY;IAC5D,IAAI,CAAC5C,UAAU,GAAG,IAAI,CAACjG,KAAK,CAAC0E,SAAS,CAAC,CAACoE,kBAAkB;IAC1D,IAAI,CAAC1G,iBAAiB,GAAG,IAAI,CAACpC,KAAK,CAAC0E,SAAS,CAAC,CAACqE,WAAW;IAC1D,IAAI,CAAC5C,aAAa,GAAG,IAAI,CAACnG,KAAK,CAAC0E,SAAS,CAAC,CAAC1B,OAAO;IAClD,IAAI,CAACoD,WAAW,GAAG,IAAI,CAACpG,KAAK,CAAC0E,SAAS,CAAC,CAACnB,KAAK;IAC9C,IAAI,CAAC8C,cAAc,GAAG,IAAI,CAACrG,KAAK,CAAC0E,SAAS,CAAC,CAAC1F,QAAQ;IACpD,IAAI,CAACuC,aAAa,GAAG,IAAI,CAACQ,MAAM,CAACiH,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACpE,MAAM,IAAI,IAAI,CAAC7E,KAAK,CAAC0E,SAAS,CAAC,EAAEoE,kBAAkB,CAACI,QAAQ,EAAE,CAACrB,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC;IAC9H,IAAI,CAACjG,UAAU,GAAG,IAAI,CAAC5B,KAAK,CAAC0E,SAAS,CAAC,EAAEoE,kBAAkB,CAACK,WAAW,EAAE,CAACtB,QAAQ,EAAE;IACpF,IAAIe,YAAY,GAAG,IAAI,CAAC5I,KAAK,CAAC0E,SAAS,CAAC,CAACkE,YAAY;IACrD,IAAIC,YAAY,GAAG,IAAI,CAAC7I,KAAK,CAAC0E,SAAS,CAAC,CAACmE,YAAY;IACrD,IAAIjE,MAAM,GAAY,CAAC5G,KAAK,CAACkJ,EAAE,EAAElJ,KAAK,CAACmJ,EAAE,EAAEnJ,KAAK,CAACoJ,EAAE,EAAEpJ,KAAK,CAACqJ,EAAE,EAAErJ,KAAK,CAACsJ,EAAE,EAAEtJ,KAAK,CAACuJ,EAAE,CAAC;IAClF,IAAI6B,iBAAiB,GAAGxE,MAAM,CAACyE,SAAS,CAACJ,EAAE,IAAIA,EAAE,KAAKL,YAAY,CAAC;IACnE,IAAIU,iBAAiB,GAAG1E,MAAM,CAACyE,SAAS,CAACJ,EAAE,IAAIA,EAAE,KAAKJ,YAAY,CAAC;IACnE,IAAIU,cAAc,GAAiB,EAAE;IACrC,IAAI,CAACvI,uBAAuB,GAAG,EAAE;IACjC,KAAK,IAAIwG,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI4B,iBAAiB,EAAE5B,CAAC,EAAE,EAAE;MAC3C,IAAIQ,SAAS,GAAGR,CAAC,IAAI4B,iBAAiB,GAAG,sBAAsB,GAAG,qBAAqB;MACvF,IAAIzB,UAAU,GAAe;QAC3BC,IAAI,EAAEhD,MAAM,CAAC4C,CAAC,CAAC;QACfQ,SAAS;QACTP,UAAU,EAAE,SAAS;QACrBC,KAAK,EAAE,OAAO;QACdW,IAAI,EAAEpK,UAAU,CAACqK,OAAO;QACxBL,KAAK,EAAE,IAAI,CAACC,UAAU,CAACC,aAAc,CAACC,WAAW,GAAI,CAAE,GAAG,EAAE,GAAG;OAEhE;MACDmB,cAAc,CAAChB,IAAI,CAACZ,UAAU,CAAC;MAC/B,IAAI,CAAC3G,uBAAuB,CAACuH,IAAI,CAACZ,UAAU,CAAC;IAC/C;IACA,KAAK,IAAIH,CAAC,GAAG4B,iBAAiB,GAAG,CAAC,EAAE5B,CAAC,IAAI8B,iBAAiB,EAAE9B,CAAC,EAAE,EAAE;MAC/D,IAAIG,UAAU,GAAe;QAC3BC,IAAI,EAAEhD,MAAM,CAAC4C,CAAC,CAAC;QACfQ,SAAS,EAAE,sBAAsB;QACjCP,UAAU,EAAE,SAAS;QACrBC,KAAK,EAAE,OAAO;QACdW,IAAI,EAAEpK,UAAU,CAACqK,OAAO;QACxBL,KAAK,EAAE,IAAI,CAACC,UAAU,CAACC,aAAc,CAACC,WAAW,GAAI,CAAE,GAAG,EAAE,GAAG;OAChE;MACDmB,cAAc,CAAChB,IAAI,CAACZ,UAAU,CAAC;MAC/B,IAAI,CAAC3G,uBAAuB,CAACuH,IAAI,CAACZ,UAAU,CAAC;IAC/C;IACA,KAAK,IAAIH,CAAC,GAAG8B,iBAAiB,GAAG,CAAC,EAAE9B,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC9C,IAAIG,UAAU,GAAe;QAC3BC,IAAI,EAAEhD,MAAM,CAAC4C,CAAC,CAAC;QACfQ,SAAS,EAAE,sBAAsB;QACjCP,UAAU,EAAE,OAAO;QACnBC,KAAK,EAAE,SAAS;QAChBW,IAAI,EAAEpK,UAAU,CAACqK,OAAO;QACxBL,KAAK,EAAE,IAAI,CAACC,UAAU,CAACC,aAAc,CAACC,WAAW,GAAI,CAAE,GAAG,EAAE,GAAG;OAChE;MACDmB,cAAc,CAAChB,IAAI,CAACZ,UAAU,CAAC;MAC/B,IAAI,CAAC3G,uBAAuB,CAACuH,IAAI,CAACZ,UAAU,CAAC;IAC/C;EACF;EAEA9E,kBAAkBA,CAAC2G,MAAkB;IACnC,OAAO,IAAI,CAACrD,aAAa,CAACsD,IAAI,CAACR,EAAE,IAAIA,EAAE,IAAIO,MAAM,CAAC;EACpD;EAEAnG,gBAAgBA,CAACqG,IAAc;IAC7B,OAAO,IAAI,CAACtD,WAAW,CAACqD,IAAI,CAACR,EAAE,IAAIA,EAAE,IAAIS,IAAI,CAAC;EAChD;EAEA7C,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACtC,gBAAgB,CAACsC,iBAAiB,CAAC9I,aAAa,CAAC4L,IAAI,CAAC;EACpE;EAEAnB,WAAWA,CAAChB,CAAS;IACnB,OAAO;MACLI,IAAI,EAAE,IAAI,CAAChD,MAAM,CAAC4C,CAAC,CAAC;MACpBQ,SAAS,EAAE,sBAAsB;MACjCP,UAAU,EAAE,OAAO;MACnBC,KAAK,EAAE,SAAS;MAChBO,KAAK,EAAE,IAAI,CAACC,UAAU,CAACC,aAAc,CAACC,WAAW,GAAI,CAAE,GAAG,EAAE,GAAG,IAAI;MACnEC,IAAI,EAAEpK,UAAU,CAAC2L;KAClB;EACH;EAEAlG,UAAUA,CAACmG,IAAa;IACtB,IAAI,IAAI,CAAC3F,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC4B,kBAAkB,CAAC3F,MAAM,IAAI2J,SAAS,EAAE;MACxE,IAAI,CAACzF,YAAY,CAAC0F,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;MACF;IACF;IACA,IAAI,IAAI,CAAChG,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC6B,kBAAkB,CAAC5F,MAAM,IAAI2J,SAAS,EAAE;MACxE,IAAI,CAACzF,YAAY,CAAC0F,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;MACF;IACF;IACA,IAAIL,IAAI,EAAE;MACR,IAAI,IAAI,CAAC3F,WAAW,GAAG,CAAC,EAAE;QACxB,IAAI,CAACzF,eAAe,CAAC,IAAI,CAACyF,WAAW,CAAC,CAAC8D,SAAS,GAAG,qBAAqB;QACxE,IAAI,CAAC9D,WAAW,EAAE;QAClB,IAAI,CAACzF,eAAe,CAAC,IAAI,CAACyF,WAAW,CAAC,CAACuD,UAAU,GAAG,SAAS;QAC7D,IAAI,CAAChJ,eAAe,CAAC,IAAI,CAACyF,WAAW,CAAC,CAACwD,KAAK,GAAG,OAAO;MACxD;IACF,CAAC,MAAM;MACL,IAAI,IAAI,CAACxD,WAAW,GAAG,CAAC,EAAE;QACxB,IAAI,CAACzF,eAAe,CAAC,IAAI,CAACyF,WAAW,CAAC,CAACuD,UAAU,GAAG,OAAO;QAC3D,IAAI,CAAChJ,eAAe,CAAC,IAAI,CAACyF,WAAW,CAAC,CAACwD,KAAK,GAAG,SAAS;QACxD,IAAI,CAACxD,WAAW,EAAE;QAClB,IAAI,CAACzF,eAAe,CAAC,IAAI,CAACyF,WAAW,CAAC,CAAC8D,SAAS,GAAG,sBAAsB;MAC3E;IACF;EACF;EAEAvH,eAAeA,CAAC0J,KAA2B;IACzC,IAAI,CAACrE,kBAAkB,GAAGqE,KAAK,CAACC,KAAK,CAACC,WAAW;IACjD,IAAI,CAAChE,cAAc,GAAG8D,KAAK,CAACC,KAAK,CAACpL,QAAS;IAC3C,IAAI,CAACsH,gBAAgB,GAAG6D,KAAK,CAACC,KAAK;IACnC,IAAI,CAACE,kBAAkB,EAAE;IACzB;EACF;EAEAA,kBAAkBA,CAAA;IAChB,IAAIlB,iBAAiB,GAAG,IAAI,CAACxE,MAAM,CAACyE,SAAS,CAACJ,EAAE,IAAIA,EAAE,IAAI,IAAI,CAACnD,kBAAkB,CAAC;IAClF,KAAK,IAAI0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B,IAAI,CAACxG,uBAAuB,CAACwG,CAAC,CAAC,GAAG,IAAI,CAACgB,WAAW,CAAChB,CAAC,CAAC;IACvD;IACA,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI4B,iBAAiB,EAAE5B,CAAC,EAAE,EAAE;MAC3C,IAAI+C,IAAI,GAAG/C,CAAC,GAAG4B,iBAAiB,GAAG,qBAAqB,GAAG,sBAAsB;MACjF,IAAI,CAACpI,uBAAuB,CAACwG,CAAC,CAAC,CAACC,UAAU,GAAG,SAAS;MACtD,IAAI,CAACzG,uBAAuB,CAACwG,CAAC,CAAC,CAACE,KAAK,GAAG,OAAO;MAC/C,IAAI,CAAC1G,uBAAuB,CAACwG,CAAC,CAAC,CAACQ,SAAS,GAAGuC,IAAI;IAClD;EACF;EAEAxJ,kBAAkBA,CAACoJ,KAA6B;IAC9C,IAAIA,KAAK,CAACK,MAAM,CAAC5C,IAAI,IAAI,IAAI,CAAC9B,kBAAkB,EAAE;MAEhD,IAAI,CAACzB,YAAY,CAAC0F,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACnE,kBAAkB,GAAGoE,KAAK,CAACK,MAAM,CAAC5C,IAAa;MACpD,IAAI0B,iBAAiB,GAAG,IAAI,CAAC1E,MAAM,CAACyE,SAAS,CAACJ,EAAE,IAAIA,EAAE,KAAKkB,KAAK,CAACK,MAAM,CAAC5C,IAAI,CAAC;MAC7E,IAAIwB,iBAAiB,GAAG,IAAI,CAACxE,MAAM,CAACyE,SAAS,CAACJ,EAAE,IAAIA,EAAE,KAAK,IAAI,CAACnD,kBAAkB,CAAC;MACnF,KAAK,IAAI0B,CAAC,GAAG4B,iBAAiB,GAAG,CAAC,EAAE5B,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC9C,IAAI,CAACxG,uBAAuB,CAACwG,CAAC,CAAC,GAAG,IAAI,CAACgB,WAAW,CAAChB,CAAC,CAAC;MACvD;MACA,KAAK,IAAIA,CAAC,GAAG4B,iBAAiB,GAAG,CAAC,EAAE5B,CAAC,IAAI8B,iBAAiB,EAAE9B,CAAC,EAAE,EAAE;QAC/D,IAAI+C,IAAI,GAAG/C,CAAC,GAAG8B,iBAAiB,GAAG,qBAAqB,GAAG,sBAAsB;QACjF,IAAI,CAACtI,uBAAuB,CAACwG,CAAC,CAAC,CAACC,UAAU,GAAG,SAAS;QACtD,IAAI,CAACzG,uBAAuB,CAACwG,CAAC,CAAC,CAACE,KAAK,GAAG,OAAO;QAC/C,IAAI,CAAC1G,uBAAuB,CAACwG,CAAC,CAAC,CAACQ,SAAS,GAAGuC,IAAI;MAClD;IACF;EACF;EAEA9I,WAAWA,CAAC0I,KAAU;IACpB,IAAI,CAACnE,WAAW,GAAG,CAACyE,QAAQ,CAACN,KAAK,CAACC,KAAK,CAACvF,MAAM,CAAC,GAAG,CAAC,EAAEgD,QAAQ,EAAE;EAClE;EAEA/F,UAAUA,CAACqI,KAAU;IACnB,IAAI,CAACvI,UAAU,GAAGuI,KAAK,CAACC,KAAK;EAC/B;EAEA9H,WAAWA,CAAC6H,KAAU;IACpB,IAAI,CAAC/H,iBAAiB,GAAG+H,KAAK,CAACC,KAAK;EACtC;EAEAxH,YAAYA,CAAC4G,MAAkB;IAC7B,IAAI,IAAI,CAACrD,aAAa,CAACsD,IAAI,CAACR,EAAE,IAAIA,EAAE,IAAIO,MAAM,CAAC,EAAE;MAC/C,IAAI,CAACrD,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC6C,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKO,MAAM,CAAC;IACrE,CAAC,MAAM;MACL,IAAI,CAACrD,aAAa,CAACoC,IAAI,CAACiB,MAAM,CAAC;IACjC;EACF;EAEApG,UAAUA,CAACsG,IAAc;IACvB,IAAI,IAAI,CAACtD,WAAW,CAACqD,IAAI,CAACR,EAAE,IAAIA,EAAE,IAAIS,IAAI,CAAC,EAAE;MAC3C,IAAI,CAACtD,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC4C,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKS,IAAI,CAAC;IAC/D,CAAC,MAAM;MACL,IAAI,CAACtD,WAAW,CAACmC,IAAI,CAACmB,IAAI,CAAC;IAC7B;EACF;EAEA9J,WAAWA,CAACuK,KAAU;IACpB,IAAI,CAAC3D,WAAW,GAAG,IAAI,CAACxG,KAAK,CAACqJ,SAAS,CAAGJ,EAAe,IAAKA,EAAE,CAACjK,QAAQ,IAAImL,KAAK,CAACC,KAAK,CAACpL,QAAS,CAAC;IACnG,IAAI,IAAI,CAACkF,WAAW,IAAI,CAAC,EAAE;MACzB,IAAI,CAACQ,SAAS,GAAG,IAAI,CAAC8B,WAAW;MACjC,IAAI,CAACG,kBAAkB,CAAC,IAAI,CAACH,WAAW,CAAC;IAC3C,CAAC,MAAM;MACL,IAAI,CAACvG,WAAW,GAAG,IAAI;MACvB,IAAI,CAACiI,UAAU,CAACC,aAAc,CAACuC,KAAK,CAACC,OAAO,GAAG,KAAK;IACtD;EACF;EAEApL,aAAaA,CAACqL,OAAgB;IAC5B,IAAI,CAAC1C,UAAU,CAACC,aAAc,CAACuC,KAAK,CAACC,OAAO,GAAG,GAAG;IAClD,IAAI,CAAC1K,WAAW,GAAG,KAAK;IACxB,IAAI2K,OAAO,EAAE;MACX,IAAI,CAAC/G,MAAM,CAAC,IAAI,CAAC;IACnB,CAAC,MAAM;MAEL,IAAI,CAACQ,YAAY,CAAC0F,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;IACJ;IACA,IAAI,CAAChG,WAAW,GAAG,CAAC;IACpB,IAAI,CAACQ,SAAS,GAAG,IAAI,CAAC8B,WAAW;IACjC,IAAI,CAACG,kBAAkB,CAAC,IAAI,CAACH,WAAW,CAAC;EAC3C;EAEA3C,MAAMA,CAACgH,MAAgB;IACrB,IAAI,CAAC3G,WAAW,GAAG,CAAC;IACpB4G,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC/E,WAAW,GAAG,MAAM,GAAG,IAAI,CAACpE,UAAU,CAAC;IACxD,IAAI,CAACqE,UAAU,GAAG,IAAIC,IAAI,CAAC,IAAI,CAACF,WAAW,GAAG,MAAM,GAAG,IAAI,CAACpE,UAAU,CAAC;IACvEkJ,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC9E,UAAU,CAAC;IAE5B,IAAI+E,IAAI,GAAgB;MACtBC,aAAa,EAAE,IAAI,CAACnF,kBAAkB;MACtC8C,YAAY,EAAE,IAAI,CAAC9C,kBAAkB;MACrC+C,YAAY,EAAE,IAAI,CAAC9C,kBAAkB;MACrC+C,kBAAkB,EAAE,IAAI,CAAC7C,UAAU;MACnC;MACA8C,WAAW,EAAE,IAAI,CAAC3G,iBAAiB;MACnC;MACAY,OAAO,EAAE,IAAI,CAACmD,aAAa;MAC3B5C,KAAK,EAAE,IAAI,CAAC6C,WAAW;MACvBpH,QAAQ,EAAE,IAAI,CAACqH,cAAc;MAC7B6E,OAAO,EAAE,IAAI,CAAC5E,gBAAgB,CAAC4E,OAAQ;MACvC;MACAC,SAAS,EAAE,IAAI,CAAC1G,WAAW,CAAC2G,SAAS;KACtC;IACDN,OAAO,CAACC,GAAG,CAACC,IAAI,CAAC;IACjB,IAAI,IAAI,CAAChL,KAAK,CAACG,MAAM,GAAG,CAAC,EAAE;MACzB,IAAI,CAACqE,WAAW,CAAC6G,UAAU,CAACL,IAAI,EAAE,IAAI,CAACzE,aAAa,CAAC,CAACO,SAAS,CAACC,GAAG,IAAG;QACpE+D,OAAO,CAACC,GAAG,CAAChE,GAAG,CAAC;QAChB,IAAI,CAACvC,WAAW,CAAC8G,iBAAiB,CAAC,IAAI,CAAC;MAC1C,CAAC,CAAC;MAEF,IAAI,CAACjH,YAAY,CAAC0F,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI,CAAC1F,WAAW,CAAC0D,UAAU,CAAC8C,IAAI,CAAC,CAAClE,SAAS,CAACC,GAAG,IAAG;QAChD+D,OAAO,CAACC,GAAG,CAAChE,GAAG,CAAC;QAChB,IAAI,CAACvC,WAAW,CAAC8G,iBAAiB,CAAC,IAAI,CAAC;MAC1C,CAAC,CAAC;MAEF,IAAI,CAACjH,YAAY,CAAC0F,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;IACJ;EACF;EAAC,QAAAqB,CAAA,G;qBAnVUpH,mBAAmB,EAAA9F,EAAA,CAAAmN,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAArN,EAAA,CAAAmN,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAvN,EAAA,CAAAmN,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAzN,EAAA,CAAAmN,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA3N,EAAA,CAAAmN,iBAAA,CAAAS,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAnBhI,mBAAmB;IAAAiI,SAAA;IAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;QCpBhClO,EAAA,CAAAK,cAAA,gBAAwD;QACpDL,EAAA,CAAAwB,UAAA,IAAA4M,8CAAA,6BAAmE;QAG/DpO,EADJ,CAAAK,cAAA,aAA0B,aACU;QAuC5BL,EAtCA,CAAAwB,UAAA,IAAA6M,mCAAA,kBAA6B,IAAAC,mCAAA,kBA0BA,IAAAC,mCAAA,kBAGA,IAAAC,mCAAA,kBAGA,IAAAC,mCAAA,kBAGA,KAAAC,oCAAA,kBAGA;QAGjC1O,EAAA,CAAAO,YAAA,EAAM;QA4CNP,EA3CA,CAAAwB,UAAA,KAAAmN,mCAAA,iBAAiD,KAAAC,+CAAA,6BAcT,KAAAC,mCAAA,iBAES,KAAAC,mCAAA,iBAcA,KAAAC,mCAAA,iBAMC,KAAAC,mCAAA,iBAOF;QAMpDhP,EAAA,CAAAO,YAAA,EAAM;QAcNP,EAZA,CAAAwB,UAAA,KAAAyN,mCAAA,kBAA0C,KAAAC,mCAAA,kBAYF;QAM5ClP,EAAA,CAAAO,YAAA,EAAM;;;QAnHgBP,EAAA,CAAAQ,SAAA,GAAmB;QAAnBR,EAAA,CAAAE,UAAA,SAAAiO,GAAA,CAAAtI,WAAA,KAAmB;QAItB7F,EAAA,CAAAQ,SAAA,GAAoB;QAApBR,EAAA,CAAAE,UAAA,SAAAiO,GAAA,CAAAtI,WAAA,MAAoB;QA0BpB7F,EAAA,CAAAQ,SAAA,EAAoB;QAApBR,EAAA,CAAAE,UAAA,SAAAiO,GAAA,CAAAtI,WAAA,MAAoB;QAGpB7F,EAAA,CAAAQ,SAAA,EAAoB;QAApBR,EAAA,CAAAE,UAAA,SAAAiO,GAAA,CAAAtI,WAAA,MAAoB;QAGpB7F,EAAA,CAAAQ,SAAA,EAAoB;QAApBR,EAAA,CAAAE,UAAA,SAAAiO,GAAA,CAAAtI,WAAA,MAAoB;QAGpB7F,EAAA,CAAAQ,SAAA,EAAoB;QAApBR,EAAA,CAAAE,UAAA,SAAAiO,GAAA,CAAAtI,WAAA,MAAoB;QAGpB7F,EAAA,CAAAQ,SAAA,EAAoB;QAApBR,EAAA,CAAAE,UAAA,SAAAiO,GAAA,CAAAtI,WAAA,MAAoB;QAIzB7F,EAAA,CAAAQ,SAAA,EAAoB;QAApBR,EAAA,CAAAE,UAAA,SAAAiO,GAAA,CAAAtI,WAAA,MAAoB;QAaR7F,EAAA,CAAAQ,SAAA,EAAoB;QAApBR,EAAA,CAAAE,UAAA,SAAAiO,GAAA,CAAAtI,WAAA,MAAoB;QAGhC7F,EAAA,CAAAQ,SAAA,EAAoB;QAApBR,EAAA,CAAAE,UAAA,SAAAiO,GAAA,CAAAtI,WAAA,MAAoB;QAcpB7F,EAAA,CAAAQ,SAAA,EAAoB;QAApBR,EAAA,CAAAE,UAAA,SAAAiO,GAAA,CAAAtI,WAAA,MAAoB;QAMpB7F,EAAA,CAAAQ,SAAA,EAAoB;QAApBR,EAAA,CAAAE,UAAA,SAAAiO,GAAA,CAAAtI,WAAA,MAAoB;QAOpB7F,EAAA,CAAAQ,SAAA,EAAoB;QAApBR,EAAA,CAAAE,UAAA,SAAAiO,GAAA,CAAAtI,WAAA,MAAoB;QAQX7F,EAAA,CAAAQ,SAAA,EAAqB;QAArBR,EAAA,CAAAE,UAAA,SAAAiO,GAAA,CAAAtI,WAAA,KAAqB;QAYlC7F,EAAA,CAAAQ,SAAA,EAAoB;QAApBR,EAAA,CAAAE,UAAA,SAAAiO,GAAA,CAAAtI,WAAA,MAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}