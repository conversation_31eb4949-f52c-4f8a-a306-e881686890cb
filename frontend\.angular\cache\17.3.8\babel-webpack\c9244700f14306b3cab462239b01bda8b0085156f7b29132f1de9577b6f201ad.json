{"ast": null, "code": "import { LessonStatus } from '../models/lesson.model';\nimport * as moment from 'moment';\nimport { BehaviorSubject } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./general.service\";\nimport * as i3 from \"./auth.service\";\nimport * as i4 from \"./lesson.service\";\nimport * as i5 from \"./classroom.service\";\nconst BACKEND_URL = environment.apiUrl;\nconst BACKEND_LMS_URL = environment.apiUrl + \"/LMS/\";\nconst dayOfWeekMap = {\n  'mon': 1,\n  'tue': 2,\n  'wed': 3,\n  'thu': 4,\n  'fri': 5,\n  'sat': 6,\n  'sun': 0\n};\nexport class CalendarService {\n  constructor(http, generalService, authService, lessonService, classroomService) {\n    this.http = http;\n    this.generalService = generalService;\n    this.authService = authService;\n    this.lessonService = lessonService;\n    this.classroomService = classroomService;\n    this.chooseClassroomListener$ = new BehaviorSubject({});\n    this.chooseClassroomListener = this.chooseClassroomListener$.asObservable();\n    this.selectedDateListener$ = new BehaviorSubject(new Date());\n    this.selectedDateListener = this.selectedDateListener$.asObservable();\n    this.durationListener$ = new BehaviorSubject(0);\n    this.durationListener = this.durationListener$.asObservable();\n    this.scheduleListener$ = new BehaviorSubject({\n      listen: false,\n      isUpdating: false\n    });\n    this.scheduleListener = this.scheduleListener$.asObservable();\n    this.updateListener$ = new BehaviorSubject(false);\n    this.updateListener = this.updateListener$.asObservable();\n    this.toggleOverlayListener$ = new BehaviorSubject(false);\n    this.toggleOverlayListener = this.toggleOverlayListener$.asObservable();\n    this.teacherChangeListener$ = new BehaviorSubject(false);\n    this.teacherChangeListener = this.teacherChangeListener$.asObservable();\n    this.calendarAgendaMonthListener$ = new BehaviorSubject(new Date());\n    this.calendarAgendaMonthListener = this.calendarAgendaMonthListener$.asObservable();\n    this.calendarAgendaHeightListener$ = new BehaviorSubject(0);\n    this.calendarAgendaHeightListener = this.calendarAgendaHeightListener$.asObservable();\n    this.scrollTo = 0;\n    this.eventSettings = {};\n    this.classoomEventSettings = {};\n    this.calendarLessons = [];\n  }\n  setChooseClassroom(classroom) {\n    this.chooseClassroomListener$.next(classroom);\n  }\n  setSelectedDateListener(date) {\n    this.selectedDateListener$.next(date);\n  }\n  setDurationListener(duration) {\n    this.durationListener$.next(duration);\n  }\n  setScheduleListener(listen, isUpdating) {\n    this.scheduleListener$.next({\n      listen: listen,\n      isUpdating: isUpdating\n    });\n  }\n  setUpdateListener(val) {\n    this.updateListener$.next(val);\n  }\n  setToggleOverlayListener(val) {\n    this.toggleOverlayListener$.next(val);\n  }\n  setTeacherChangeListener(val) {\n    this.teacherChangeListener$.next(val);\n  }\n  setCalendarAgendaMonthListener(val) {\n    this.calendarAgendaMonthListener$.next(val);\n  }\n  setCalendarAgendaHeightListener(val) {\n    this.calendarAgendaHeightListener$.next(val);\n  }\n  convertLessonsToCallendarObjects(lessons, language) {\n    let eventSettings = [];\n    let currentEventSettings;\n    for (let lesson of lessons) {\n      currentEventSettings = this.createLessonEvent(lesson, lesson.title, language);\n      eventSettings.push(currentEventSettings);\n    }\n    return eventSettings;\n    // return { dataSource: extend([], eventSettings, undefined, true) as Record<string, any>[] };\n  }\n  createLessonEvent(lesson, title, language) {\n    // let recRule = lesson.isRecccuring ? 'FREQ=WEEKLY;INTERVAL=1;COUNT=10' : ''\n    return {\n      Id: lesson.id,\n      Subject: title,\n      Language: language.substring(0, 2).toUpperCase(),\n      StartTime: lesson.startingDate,\n      EndTime: moment(lesson.startingDate).add(lesson.duration * 60, 'm').toDate(),\n      CategoryColor: \"white\",\n      Duration: lesson.duration,\n      Status: lesson.status,\n      RecurrenceRule: \"\",\n      Type: \"appointment\",\n      Lesson: lesson,\n      Padding: lesson.duration * 2 * 36 - 35 + 12 + \"px 10px\"\n    };\n  }\n  convertAvailabilityToCalendarObjects(times, type) {\n    let currentWeek = this.getWeek();\n    let mon = new Date(currentWeek[0]);\n    let tue = new Date(currentWeek[1]);\n    let wed = new Date(currentWeek[2]);\n    let thu = new Date(currentWeek[3]);\n    let fri = new Date(currentWeek[4]);\n    let sat = new Date(currentWeek[5]);\n    let sun = new Date(currentWeek[6]);\n    ;\n    let eventSettings = [];\n    this.createAvailabilityEventsByDay(mon, 'mon', times, type, eventSettings);\n    this.createAvailabilityEventsByDay(tue, 'tue', times, type, eventSettings);\n    this.createAvailabilityEventsByDay(wed, 'wed', times, type, eventSettings);\n    this.createAvailabilityEventsByDay(thu, 'thu', times, type, eventSettings);\n    this.createAvailabilityEventsByDay(fri, 'fri', times, type, eventSettings);\n    this.createAvailabilityEventsByDay(sat, 'sat', times, type, eventSettings);\n    this.createAvailabilityEventsByDay(sun, 'sun', times, type, eventSettings);\n    return eventSettings;\n  }\n  createAvailabilityEventsByDay(day, dayString, times, type, eventSettings) {\n    let currentEventSettings;\n    let id = 0;\n    let hourFrom = 0;\n    let minutesFrom = 0;\n    let hourTo = 0;\n    let minutesTo = 0;\n    let splitTimeFrom = [];\n    let splitTimeTo = [];\n    let from = 0;\n    let to = 0;\n    let nextFrom = 0;\n    let nextTo = 0;\n    let array = times[dayString];\n    let escapeFrom = false;\n    if (array == null) {\n      array = [];\n    }\n    for (let i = 0; i < array.length; i++) {\n      splitTimeFrom = array[i].from.split(':');\n      splitTimeTo = array[i].to.split(':');\n      hourFrom = parseInt(splitTimeFrom[0]);\n      minutesFrom = parseInt(splitTimeFrom[1]);\n      hourTo = parseInt(splitTimeTo[0]);\n      minutesTo = parseInt(splitTimeTo[1]);\n      if (!escapeFrom) {\n        from = day.setHours(hourFrom, minutesFrom);\n      }\n      escapeFrom = false;\n      to = day.setHours(hourTo, minutesTo);\n      if (i + 1 < array.length) {\n        splitTimeFrom = array[i + 1].from.split(':');\n        splitTimeTo = array[i + 1].to.split(':');\n        hourFrom = parseInt(splitTimeFrom[0]);\n        minutesFrom = parseInt(splitTimeFrom[1]);\n        hourTo = parseInt(splitTimeTo[0]);\n        minutesTo = parseInt(splitTimeTo[1]);\n        nextFrom = day.setHours(hourFrom, minutesFrom);\n        nextTo = day.setHours(hourTo, minutesTo);\n        if (to == nextFrom) {\n          escapeFrom = true;\n        }\n      }\n      if (!escapeFrom) {\n        currentEventSettings = this.crateAvailabilityEvent(from, to, id, type);\n        eventSettings.push(currentEventSettings);\n      }\n    }\n  }\n  crateAvailabilityEvent(from, to, id, type) {\n    return {\n      Id: id,\n      StartTime: moment(from).add(0, 'm').toDate(),\n      EndTime: moment(to).add(0, 'm').toDate(),\n      Subject: \"\",\n      CategoryColor: \"\",\n      Duration: this.generalService.getDatesDiff(moment(from).add(0, 'm').toDate(), moment(to).add(0, 'm').toDate()).minutes / 60,\n      //helps to set the height. View getAvailabilityEventHeight in scheduler.ts\n      Status: LessonStatus.ARRANGED,\n      RecurrenceRule: 'FREQ=WEEKLY;INTERVAL=1;COUNT=10',\n      Type: type\n    };\n  }\n  getEventGradientColor(status) {\n    if (status === LessonStatus.ARRANGED) {\n      return 'linear-gradient( #c056f5 0%,  #5d5085 100%)';\n    } else if (status === LessonStatus.COMPLETED) {\n      return 'linear-gradient( #3b9bed 0%,  #5d5085 100%)';\n    } else {\n      return 'linear-gradient( #F27769 0%,  #5d5085 100%)';\n    }\n  }\n  getWeek() {\n    let curr = new Date();\n    let week = [];\n    for (let i = 1; i <= 7; i++) {\n      let first = curr.getUTCDate() - curr.getUTCDay() + i;\n      let day = new Date(curr.setUTCDate(first)).toISOString().slice(0, 10);\n      week.push(day);\n    }\n    return week;\n  }\n  /**\n   * The function checks if a given time slot is available in a list of user availability hours.\n   * @param {any} data - The `data` parameter is of type `any`, which means it can be any data type. It\n   * is used to pass in the data for which availability needs to be checked.\n   * @param {UserAvailabilityHours[]} dayAvailability - An array of objects representing the availability\n   * hours for a user. Each object has two properties: \"from\" and \"to\", which represent the start and end\n   * times of the availability period.\n   * @returns an object with two properties: \"add\" and \"block\".\n   */\n  checkIfAvailabilityExists(data, dayAvailability) {\n    if (dayAvailability == null) {\n      return {\n        add: true,\n        block: false\n      };\n    }\n    let aTo = \"\";\n    let aFrom = \"\";\n    aFrom = data.StartTime.getHours().toString() + \":\" + data.StartTime.getMinutes().toString();\n    let splitTimeFrom = aFrom.split(':');\n    if (splitTimeFrom[1].toString().length === 1) {\n      aFrom = aFrom + \"0\";\n    }\n    if (splitTimeFrom[0].length === 1) {\n      aFrom = \"0\" + aFrom;\n    }\n    // let end = moment(data.EndTime).subtract(15, 'm').toDate(); // dont know why but it works\n    aTo = data.EndTime.getHours().toString() + \":\" + data.EndTime.getMinutes().toString();\n    let splitTimeTo = aTo.split(':');\n    if (splitTimeTo[1].toString().length === 1) {\n      aTo = aTo + \"0\";\n    }\n    if (splitTimeTo[0].length === 1) {\n      aTo = \"0\" + aTo;\n    }\n    if (dayAvailability.length === 0) {\n      return {\n        add: true,\n        block: false\n      };\n    }\n    for (let available of dayAvailability) {\n      let splitAvailabilityFrom = available.from.split(\":\");\n      if (splitAvailabilityFrom[0].length === 1) {\n        splitAvailabilityFrom[0] = \"0\" + splitAvailabilityFrom[0];\n      }\n      let from = splitAvailabilityFrom[0] + \":\" + splitAvailabilityFrom[1];\n      let splitAvailabilityTo = available.to.split(\":\");\n      if (splitAvailabilityTo[0].length === 1) {\n        splitAvailabilityTo[0] = \"0\" + splitAvailabilityTo[0];\n      }\n      let to = splitAvailabilityTo[0] + \":\" + splitAvailabilityTo[1];\n      if (from <= aFrom && to >= aTo) {\n        return {\n          add: false,\n          block: true\n        };\n      }\n    }\n    for (let available of dayAvailability) {\n      if (available.from < aFrom && available.to > aFrom || aTo > available.from && aTo < available.to) {\n        return {\n          add: false,\n          block: false\n        };\n      }\n    }\n    for (let available of dayAvailability) {\n      if (available.from > aFrom || available.to < aTo) {\n        return {\n          add: true,\n          block: false\n        };\n      }\n    }\n    return {\n      add: false,\n      block: false\n    };\n  }\n  addDaysOff(dayOff) {\n    return this.http.post(BACKEND_URL + \"/Teacher/AddUserDayOff\", dayOff);\n  }\n  cancelDaysOff(dayOffId) {\n    return this.http.post(BACKEND_URL + \"/Teacher/DeleteUserDayOff?id=\" + dayOffId, {});\n  }\n  getPrettyLessonDate(date) {\n    return date.getHours() + \":\" + date.getMinutes() + \" on \" + this.generalService.getDayFromNumber(date.getDay()) + \" \" + this.generalService.getMonthFromNumber(date.getMonth()) + \" \" + date.getDate();\n  }\n  createSpecificTeacherAvailability(availability) {\n    return this.http.post(BACKEND_LMS_URL + \"CreateSpecificTeacherAvailability\", availability);\n  }\n  blockSpecificTeacherAvailability(availability) {\n    return this.http.post(BACKEND_LMS_URL + \"BlockSpecificTeacherAvailability\", availability);\n  }\n  retrieveCalendarAvailability(availability, headers) {\n    return this.http.post(BACKEND_LMS_URL + \"RetrieveCalendarAvailability\", availability, {\n      headers\n    });\n  }\n  getBusinessHours(calendarAvailability) {\n    const businessHours = [];\n    for (const availability of calendarAvailability) {\n      const dateParts = availability.date.split('/');\n      const date = new Date(`${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`);\n      for (const interval of availability.availability) {\n        const startTimeParts = interval.from.split(':');\n        const endTimeParts = interval.to.split(':');\n        const startHour = parseInt(startTimeParts[0]);\n        const startMinute = parseInt(startTimeParts[1]);\n        const endHour = parseInt(endTimeParts[0]);\n        const endMinute = parseInt(endTimeParts[1]);\n        // Check if the end time is greater than the start time,\n        // indicating that it goes until the next day\n        if (endHour < startHour || endHour === startHour && endMinute < startMinute) {\n          // Calculate the duration until midnight\n          const midnightDuration = (24 - startHour) * 60 - startMinute;\n          console.log(midnightDuration);\n          // Add the availability for the current day until midnight\n          businessHours.push({\n            daysOfWeek: [date.getDay()],\n            startTime: interval.from,\n            endTime: '24:00'\n          });\n          // Increment the date to the next day\n          date.setDate(date.getDate() + 1);\n          // Calculate the duration from midnight until the end time\n          const nextDayDuration = endHour * 60 + endMinute;\n          // Add the availability for the next day from midnight\n          businessHours.push({\n            daysOfWeek: [date.getDay()],\n            startTime: '00:00',\n            endTime: interval.to\n          });\n        } else {\n          // Add the availability for the current day\n          businessHours.push({\n            daysOfWeek: [date.getDay()],\n            startTime: interval.from,\n            endTime: interval.to\n          });\n        }\n      }\n    }\n    console.log(businessHours);\n    return businessHours;\n  }\n  createDayOffEvents(daysOff) {\n    return daysOff.map(dayOff => {\n      const from = new Date(dayOff.period.from);\n      const to = new Date(dayOff.period.to);\n      return {\n        title: dayOff.reason,\n        start: from,\n        display: 'background',\n        extendedProps: {\n          type: 'dayOff',\n          rendering: 'background'\n        },\n        selectable: false,\n        editable: false,\n        allDay: true\n      };\n    });\n  }\n  static #_ = this.ɵfac = function CalendarService_Factory(t) {\n    return new (t || CalendarService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.GeneralService), i0.ɵɵinject(i3.AuthService), i0.ɵɵinject(i4.LessonService), i0.ɵɵinject(i5.ClassroomService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: CalendarService,\n    factory: CalendarService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["LessonStatus", "moment", "BehaviorSubject", "environment", "BACKEND_URL", "apiUrl", "BACKEND_LMS_URL", "dayOfWeekMap", "CalendarService", "constructor", "http", "generalService", "authService", "lessonService", "classroomService", "chooseClassroomListener$", "chooseClassroomListener", "asObservable", "selectedDateListener$", "Date", "selectedDateListener", "durationListener$", "durationListener", "scheduleListener$", "listen", "isUpdating", "scheduleListener", "updateListener$", "updateListener", "toggleOverlayListener$", "toggleOverlayListener", "teacherChangeListener$", "teacher<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "calendarAgendaMonthListener$", "calendarAgendaMonthListener", "calendarAgendaHeightListener$", "calendarAgendaHeightListener", "scrollTo", "eventSettings", "classoomEventSettings", "calendarLessons", "setChooseClassroom", "classroom", "next", "setSelectedDateListener", "date", "setDurationListener", "duration", "setScheduleListener", "setUpdateListener", "val", "setToggleOverlayListener", "setTeacherChangeListener", "setCalendarAgendaMonthListener", "setCalendarAgendaHeightListener", "convertLessonsToCallendarObjects", "lessons", "language", "currentEventSettings", "lesson", "createLessonEvent", "title", "push", "Id", "id", "Subject", "Language", "substring", "toUpperCase", "StartTime", "startingDate", "EndTime", "add", "toDate", "CategoryColor", "Duration", "Status", "status", "RecurrenceRule", "Type", "Lesson", "Padding", "convertAvailabilityToCalendarObjects", "times", "type", "currentWeek", "getWeek", "mon", "tue", "wed", "thu", "fri", "sat", "sun", "createAvailabilityEventsByDay", "day", "dayString", "hourFrom", "minutesFrom", "hourTo", "minutesTo", "splitTimeFrom", "splitTimeTo", "from", "to", "nextFrom", "nextTo", "array", "escapeFrom", "i", "length", "split", "parseInt", "setHours", "crateAvailabilityEvent", "getDatesDiff", "minutes", "ARRANGED", "getEventGradientColor", "COMPLETED", "curr", "week", "first", "getUTCDate", "getUTCDay", "setUTCDate", "toISOString", "slice", "checkIfAvailabilityExists", "data", "dayAvailability", "block", "aTo", "aFrom", "getHours", "toString", "getMinutes", "available", "splitAvailabilityFrom", "splitAvailabilityTo", "addDaysOff", "dayOff", "post", "cancelDaysOff", "dayOffId", "getPrettyLessonDate", "getDayFromNumber", "getDay", "getMonthFromNumber", "getMonth", "getDate", "createSpecificTeacherAvailability", "availability", "blockSpecificTeacherAvailability", "retrieveCalendarAvailability", "headers", "getBusinessHours", "calendarAvailability", "businessHours", "dateParts", "interval", "startTimeParts", "endTimeParts", "startHour", "startMinute", "endHour", "endMinute", "midnightDuration", "console", "log", "daysOfWeek", "startTime", "endTime", "setDate", "nextDayDuration", "createDayOffEvents", "daysOff", "map", "period", "reason", "start", "display", "extendedProps", "rendering", "selectable", "editable", "allDay", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "GeneralService", "i3", "AuthService", "i4", "LessonService", "i5", "ClassroomService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\services\\calendar.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Lesson, LessonStatus } from '../models/lesson.model';\r\nimport * as moment from 'moment';\r\nimport { UserAvailability, UserAvailabilityHours, UserAvailabilityOff } from '../models/user.model';\r\nimport { MyCalendarEvent, RetrieveCalendarAvailabilityRequestData } from '../models/calendar.model';\r\nimport { GeneralService } from './general.service';\r\nimport { BehaviorSubject, forkJoin, Observable, of } from 'rxjs';\r\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { environment } from 'src/environments/environment';\r\nimport { AuthService } from './auth.service';\r\nimport { Classroom } from '../models/classroom.model';\r\nimport { LessonService } from './lesson.service';\r\nimport { ClassroomService } from './classroom.service';\r\nconst BACKEND_URL = environment.apiUrl;\r\nconst BACKEND_LMS_URL = environment.apiUrl + \"/LMS/\";\r\n\r\nconst dayOfWeekMap: { [key: string]: number } = {\r\n  'mon': 1,\r\n  'tue': 2,\r\n  'wed': 3,\r\n  'thu': 4,\r\n  'fri': 5,\r\n  'sat': 6,\r\n  'sun': 0\r\n};\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class CalendarService {\r\n  private chooseClassroomListener$: BehaviorSubject<Classroom> = new BehaviorSubject<Classroom>({} as Classroom);\r\n  public readonly chooseClassroomListener: Observable<Classroom> = this.chooseClassroomListener$.asObservable();\r\n\r\n  private selectedDateListener$: BehaviorSubject<Date> = new BehaviorSubject<Date>(new Date());\r\n  public readonly selectedDateListener: Observable<Date> = this.selectedDateListener$.asObservable();\r\n\r\n  private durationListener$: BehaviorSubject<number> = new BehaviorSubject<number>(0);\r\n  public readonly durationListener: Observable<number> = this.durationListener$.asObservable();\r\n\r\n  private scheduleListener$: BehaviorSubject<{ listen: boolean, isUpdating: boolean }> = new BehaviorSubject<{ listen: boolean, isUpdating: boolean }>({ listen: false, isUpdating: false });\r\n  public readonly scheduleListener: Observable<{ listen: boolean, isUpdating: boolean }> = this.scheduleListener$.asObservable();\r\n\r\n  private updateListener$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);\r\n  public readonly updateListener: Observable<boolean> = this.updateListener$.asObservable();\r\n  private toggleOverlayListener$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);\r\n  public readonly toggleOverlayListener: Observable<boolean> = this.toggleOverlayListener$.asObservable();\r\n\r\n  private teacherChangeListener$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);\r\n  public readonly teacherChangeListener: Observable<boolean> = this.teacherChangeListener$.asObservable();\r\n\r\n  private calendarAgendaMonthListener$: BehaviorSubject<Date> = new BehaviorSubject<Date>(new Date());\r\n  public readonly calendarAgendaMonthListener: Observable<Date> = this.calendarAgendaMonthListener$.asObservable();\r\n\r\n  private calendarAgendaHeightListener$: BehaviorSubject<number> = new BehaviorSubject<number>(0);\r\n  public readonly calendarAgendaHeightListener: Observable<number> = this.calendarAgendaHeightListener$.asObservable();\r\n\r\n  public scrollTo: number = 0;\r\n  public eventSettings: { dataSource: Record<string, any>[] } = {} as { dataSource: Record<string, any>[] }\r\n  public classoomEventSettings: { dataSource: Record<string, any>[] } = {} as { dataSource: Record<string, any>[] }\r\n  private calendarLessons: MyCalendarEvent[] = [];\r\n  constructor(\r\n    private http: HttpClient,\r\n    private generalService: GeneralService,\r\n    private authService: AuthService,\r\n    private lessonService: LessonService,\r\n    private classroomService: ClassroomService\r\n  ) { }\r\n\r\n  public setChooseClassroom(classroom: Classroom) {\r\n    this.chooseClassroomListener$.next(classroom);\r\n  }\r\n\r\n  public setSelectedDateListener(date: Date) {\r\n    this.selectedDateListener$.next(date);\r\n  }\r\n\r\n  public setDurationListener(duration: number) {\r\n    this.durationListener$.next(duration);\r\n  }\r\n\r\n  public setScheduleListener(listen: boolean, isUpdating: boolean,) {\r\n    this.scheduleListener$.next({ listen: listen, isUpdating: isUpdating });\r\n  }\r\n\r\n  public setUpdateListener(val: boolean) {\r\n    this.updateListener$.next(val);\r\n  }\r\n\r\n  public setToggleOverlayListener(val: boolean) {\r\n    this.toggleOverlayListener$.next(val);\r\n  }\r\n\r\n  public setTeacherChangeListener(val: boolean) {\r\n    this.teacherChangeListener$.next(val);\r\n  }\r\n\r\n  public setCalendarAgendaMonthListener(val: Date) {\r\n    this.calendarAgendaMonthListener$.next(val);\r\n  }\r\n\r\n  public setCalendarAgendaHeightListener(val: number) {\r\n    this.calendarAgendaHeightListener$.next(val);\r\n  }\r\n\r\n  public convertLessonsToCallendarObjects(lessons: Lesson[], language: string): MyCalendarEvent[] {\r\n    let eventSettings: MyCalendarEvent[] = [];\r\n    let currentEventSettings: MyCalendarEvent;\r\n    for (let lesson of lessons) {\r\n      currentEventSettings = this.createLessonEvent(lesson, lesson.title!, language);\r\n      eventSettings.push(currentEventSettings);\r\n    }\r\n    return eventSettings;\r\n    // return { dataSource: extend([], eventSettings, undefined, true) as Record<string, any>[] };\r\n  }\r\n\r\n  createLessonEvent(lesson: Lesson, title: string, language: string) {\r\n    // let recRule = lesson.isRecccuring ? 'FREQ=WEEKLY;INTERVAL=1;COUNT=10' : ''\r\n    return {\r\n      Id: lesson.id,\r\n      Subject: title,\r\n      Language: language.substring(0, 2).toUpperCase(),\r\n      StartTime: lesson.startingDate,\r\n      EndTime: moment(lesson.startingDate).add(lesson.duration * 60, 'm').toDate(),\r\n      CategoryColor: \"white\",\r\n      Duration: lesson.duration,\r\n      Status: lesson.status,\r\n      RecurrenceRule: \"\",\r\n      Type: \"appointment\",\r\n      Lesson: lesson,\r\n      Padding: ((lesson.duration * 2) * 36 - 35 + 12) + \"px 10px\"\r\n    }\r\n  }\r\n\r\n  public convertAvailabilityToCalendarObjects(times: UserAvailability, type: string): MyCalendarEvent[] {\r\n    let currentWeek = this.getWeek();\r\n    let mon = new Date(currentWeek[0]);\r\n    let tue = new Date(currentWeek[1]);\r\n    let wed = new Date(currentWeek[2]);\r\n    let thu = new Date(currentWeek[3]);\r\n    let fri = new Date(currentWeek[4]);\r\n    let sat = new Date(currentWeek[5]);\r\n    let sun = new Date(currentWeek[6]);;\r\n    let eventSettings: MyCalendarEvent[] = [];\r\n    this.createAvailabilityEventsByDay(mon, 'mon', times, type, eventSettings)\r\n    this.createAvailabilityEventsByDay(tue, 'tue', times, type, eventSettings)\r\n    this.createAvailabilityEventsByDay(wed, 'wed', times, type, eventSettings)\r\n    this.createAvailabilityEventsByDay(thu, 'thu', times, type, eventSettings)\r\n    this.createAvailabilityEventsByDay(fri, 'fri', times, type, eventSettings)\r\n    this.createAvailabilityEventsByDay(sat, 'sat', times, type, eventSettings)\r\n    this.createAvailabilityEventsByDay(sun, 'sun', times, type, eventSettings)\r\n    return eventSettings;\r\n  }\r\n\r\n  createAvailabilityEventsByDay(day: Date, dayString: 'mon' | 'tue' | 'wed' | 'thu' | 'fri' | 'sat' | 'sun', times: UserAvailability, type: string, eventSettings: MyCalendarEvent[]) {\r\n    let currentEventSettings: MyCalendarEvent;\r\n    let id: number = 0;\r\n    let hourFrom = 0;\r\n    let minutesFrom = 0;\r\n    let hourTo = 0;\r\n    let minutesTo = 0;\r\n    let splitTimeFrom = [];\r\n    let splitTimeTo = [];\r\n    let from: number = 0;\r\n    let to: number = 0;\r\n    let nextFrom: number = 0;\r\n    let nextTo: number = 0;\r\n    let array: UserAvailabilityHours[] = times[dayString];\r\n    let escapeFrom: boolean = false;\r\n    if (array == null) {\r\n      array = []\r\n    }\r\n    for (let i = 0; i < array.length; i++) {\r\n      splitTimeFrom = array[i].from.split(':');\r\n      splitTimeTo = array[i].to.split(':');\r\n      hourFrom = parseInt(splitTimeFrom[0]);\r\n      minutesFrom = parseInt(splitTimeFrom[1]);\r\n      hourTo = parseInt(splitTimeTo[0]);\r\n      minutesTo = parseInt(splitTimeTo[1]);\r\n      if (!escapeFrom) {\r\n        from = day.setHours(hourFrom, minutesFrom);\r\n      }\r\n      escapeFrom = false;\r\n      to = day.setHours(hourTo, minutesTo);\r\n      if (i + 1 < array.length) {\r\n        splitTimeFrom = array[i + 1].from.split(':');\r\n        splitTimeTo = array[i + 1].to.split(':');\r\n        hourFrom = parseInt(splitTimeFrom[0]);\r\n        minutesFrom = parseInt(splitTimeFrom[1]);\r\n        hourTo = parseInt(splitTimeTo[0]);\r\n        minutesTo = parseInt(splitTimeTo[1]);\r\n        nextFrom = day.setHours(hourFrom, minutesFrom);\r\n        nextTo = day.setHours(hourTo, minutesTo);\r\n\r\n        if (to == nextFrom) {\r\n          escapeFrom = true;\r\n        }\r\n      }\r\n      if (!escapeFrom) {\r\n        currentEventSettings = this.crateAvailabilityEvent(from, to, id, type);\r\n        eventSettings.push(currentEventSettings);\r\n      }\r\n    }\r\n  }\r\n\r\n  crateAvailabilityEvent(from: number, to: number, id: number, type: string) {\r\n    return {\r\n      Id: id,\r\n      StartTime: moment(from).add(0, 'm').toDate(),\r\n      EndTime: moment(to).add(0, 'm').toDate(),\r\n      Subject: \"\",\r\n      CategoryColor: \"\",\r\n      Duration: this.generalService.getDatesDiff(moment(from).add(0, 'm').toDate(), moment(to).add(0, 'm').toDate()).minutes / 60,//helps to set the height. View getAvailabilityEventHeight in scheduler.ts\r\n      Status: LessonStatus.ARRANGED,\r\n      RecurrenceRule: 'FREQ=WEEKLY;INTERVAL=1;COUNT=10',\r\n      Type: type\r\n    }\r\n  }\r\n\r\n  getEventGradientColor(status: LessonStatus): string {\r\n    if (status === LessonStatus.ARRANGED) {\r\n      return 'linear-gradient( #c056f5 0%,  #5d5085 100%)'\r\n    } else if (status === LessonStatus.COMPLETED) {\r\n      return 'linear-gradient( #3b9bed 0%,  #5d5085 100%)'\r\n    } else {\r\n      return 'linear-gradient( #F27769 0%,  #5d5085 100%)'\r\n    }\r\n  }\r\n\r\n  getWeek() {\r\n    let curr = new Date();\r\n    let week = []\r\n\r\n    for (let i = 1; i <= 7; i++) {\r\n      let first = curr.getUTCDate() - curr.getUTCDay() + i\r\n      let day = new Date(curr.setUTCDate(first)).toISOString().slice(0, 10)\r\n      week.push(day)\r\n    }\r\n    return week\r\n  }\r\n\r\n/**\r\n * The function checks if a given time slot is available in a list of user availability hours.\r\n * @param {any} data - The `data` parameter is of type `any`, which means it can be any data type. It\r\n * is used to pass in the data for which availability needs to be checked.\r\n * @param {UserAvailabilityHours[]} dayAvailability - An array of objects representing the availability\r\n * hours for a user. Each object has two properties: \"from\" and \"to\", which represent the start and end\r\n * times of the availability period.\r\n * @returns an object with two properties: \"add\" and \"block\".\r\n */\r\n  checkIfAvailabilityExists(data: any, dayAvailability: UserAvailabilityHours[]) {\r\n    if (dayAvailability == null) {\r\n      return {\r\n        add: true,\r\n        block: false\r\n      }\r\n    }\r\n    let aTo: string = \"\";\r\n    let aFrom: string = \"\";\r\n    aFrom = data.StartTime.getHours().toString() + \":\" + data.StartTime.getMinutes().toString();\r\n    let splitTimeFrom = aFrom.split(':');\r\n    if (splitTimeFrom[1].toString().length === 1) {\r\n      aFrom = aFrom + \"0\";\r\n    }\r\n    if (splitTimeFrom[0].length === 1) {\r\n      aFrom = \"0\" + aFrom\r\n    }\r\n    // let end = moment(data.EndTime).subtract(15, 'm').toDate(); // dont know why but it works\r\n    aTo = data.EndTime.getHours().toString() + \":\" + data.EndTime.getMinutes().toString();\r\n    let splitTimeTo = aTo.split(':');\r\n    if (splitTimeTo[1].toString().length === 1) {\r\n      aTo = aTo + \"0\";\r\n    }\r\n    if (splitTimeTo[0].length === 1) {\r\n      aTo = \"0\" + aTo\r\n    }\r\n\r\n    if (dayAvailability.length === 0) {\r\n      return {\r\n        add: true,\r\n        block: false\r\n      }\r\n    }\r\n\r\n    for (let available of dayAvailability) {\r\n      let splitAvailabilityFrom = available.from.split(\":\");\r\n      if (splitAvailabilityFrom[0].length === 1) {\r\n        splitAvailabilityFrom[0] = \"0\" + splitAvailabilityFrom[0]\r\n      }\r\n      let from = splitAvailabilityFrom[0] + \":\" + splitAvailabilityFrom[1];\r\n\r\n      let splitAvailabilityTo = available.to.split(\":\");\r\n      if (splitAvailabilityTo[0].length === 1) {\r\n        splitAvailabilityTo[0] = \"0\" + splitAvailabilityTo[0]\r\n      }\r\n      let to = splitAvailabilityTo[0] + \":\" + splitAvailabilityTo[1];\r\n\r\n      if (from <= aFrom && to >= aTo) {\r\n        return {\r\n          add: false,\r\n          block: true\r\n        }\r\n      }\r\n    }\r\n\r\n    for (let available of dayAvailability) {\r\n      if ((available.from < aFrom && available.to > aFrom) || (aTo > available.from && aTo < available.to)) {\r\n        return {\r\n          add: false,\r\n          block: false\r\n        }\r\n      }\r\n    }\r\n\r\n    for (let available of dayAvailability) {\r\n      if (available.from > aFrom || available.to < aTo) {\r\n        return {\r\n          add: true,\r\n          block: false\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      add: false,\r\n      block: false\r\n    }\r\n  }\r\n\r\n  addDaysOff(dayOff: UserAvailabilityOff) {\r\n    return this.http.post<UserAvailabilityOff>(BACKEND_URL + \"/Teacher/AddUserDayOff\", dayOff)\r\n  }\r\n\r\n  cancelDaysOff(dayOffId: number) {\r\n    return this.http.post(BACKEND_URL + \"/Teacher/DeleteUserDayOff?id=\" + dayOffId, {})\r\n  }\r\n\r\n  getPrettyLessonDate(date: Date) {\r\n    return date.getHours() + \":\" + date.getMinutes() + \" on \" + this.generalService.getDayFromNumber(date.getDay()) + \" \" + this.generalService.getMonthFromNumber(date.getMonth()) + \" \" + date.getDate()\r\n  }\r\n\r\n  createSpecificTeacherAvailability(availability: {}) {\r\n    return this.http.post(BACKEND_LMS_URL + \"CreateSpecificTeacherAvailability\", availability)\r\n  }\r\n\r\n  blockSpecificTeacherAvailability(availability: {}) {\r\n    return this.http.post(BACKEND_LMS_URL + \"BlockSpecificTeacherAvailability\", availability)\r\n  }\r\n\r\n  retrieveCalendarAvailability(availability: RetrieveCalendarAvailabilityRequestData, headers?: HttpHeaders): Observable<RetrieveCalendarAvailabilityRequestData> {\r\n    return (this.http.post(BACKEND_LMS_URL + \"RetrieveCalendarAvailability\", availability, { headers }) as Observable<RetrieveCalendarAvailabilityRequestData>)\r\n  }\r\n\r\n  getBusinessHours(calendarAvailability: any) {\r\n    const businessHours = [];\r\n  \r\n    for (const availability of calendarAvailability) {\r\n      const dateParts = availability.date.split('/');\r\n      const date = new Date(`${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`);\r\n  \r\n      for (const interval of availability.availability) {\r\n        const startTimeParts = interval.from.split(':');\r\n        const endTimeParts = interval.to.split(':');\r\n        const startHour = parseInt(startTimeParts[0]);\r\n        const startMinute = parseInt(startTimeParts[1]);\r\n        const endHour = parseInt(endTimeParts[0]);\r\n        const endMinute = parseInt(endTimeParts[1]);\r\n  \r\n        // Check if the end time is greater than the start time,\r\n        // indicating that it goes until the next day\r\n        if (endHour < startHour || (endHour === startHour && endMinute < startMinute)) {\r\n          // Calculate the duration until midnight\r\n          const midnightDuration = (24 - startHour) * 60 - startMinute;\r\n  \r\n          console.log(midnightDuration);\r\n          // Add the availability for the current day until midnight\r\n          businessHours.push({\r\n            daysOfWeek: [date.getDay()],\r\n            startTime: interval.from,\r\n            endTime: '24:00'\r\n          });\r\n  \r\n          // Increment the date to the next day\r\n          date.setDate(date.getDate() + 1);\r\n  \r\n          // Calculate the duration from midnight until the end time\r\n          const nextDayDuration = endHour * 60 + endMinute;\r\n  \r\n          // Add the availability for the next day from midnight\r\n          businessHours.push({\r\n            daysOfWeek: [date.getDay()],\r\n            startTime: '00:00',\r\n            endTime: interval.to\r\n          });\r\n        } else {\r\n          // Add the availability for the current day\r\n          businessHours.push({\r\n            daysOfWeek: [date.getDay()],\r\n            startTime: interval.from,\r\n            endTime: interval.to\r\n          });\r\n        }\r\n      }\r\n    }\r\n  \r\n    console.log(businessHours);\r\n    return businessHours;\r\n  }\r\n\r\n  createDayOffEvents(daysOff: any[]): any[] {\r\n    return daysOff.map((dayOff: any) => {\r\n      const from = new Date(dayOff.period.from);\r\n      const to = new Date(dayOff.period.to);\r\n      return {\r\n        title: dayOff.reason,\r\n        start: from,\r\n        display: 'background',\r\n        extendedProps: {\r\n          type: 'dayOff',\r\n          rendering: 'background'\r\n        },\r\n        selectable: false,\r\n        editable: false,\r\n        allDay: true\r\n      };\r\n    });\r\n  }\r\n\r\n}\r\n"], "mappings": "AACA,SAAiBA,YAAY,QAAQ,wBAAwB;AAC7D,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAIhC,SAASC,eAAe,QAAkC,MAAM;AAEhE,SAASC,WAAW,QAAQ,8BAA8B;;;;;;;AAK1D,MAAMC,WAAW,GAAGD,WAAW,CAACE,MAAM;AACtC,MAAMC,eAAe,GAAGH,WAAW,CAACE,MAAM,GAAG,OAAO;AAEpD,MAAME,YAAY,GAA8B;EAC9C,KAAK,EAAE,CAAC;EACR,KAAK,EAAE,CAAC;EACR,KAAK,EAAE,CAAC;EACR,KAAK,EAAE,CAAC;EACR,KAAK,EAAE,CAAC;EACR,KAAK,EAAE,CAAC;EACR,KAAK,EAAE;CACR;AAID,OAAM,MAAOC,eAAe;EA+B1BC,YACUC,IAAgB,EAChBC,cAA8B,EAC9BC,WAAwB,EACxBC,aAA4B,EAC5BC,gBAAkC;IAJlC,KAAAJ,IAAI,GAAJA,IAAI;IACJ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAnClB,KAAAC,wBAAwB,GAA+B,IAAIb,eAAe,CAAY,EAAe,CAAC;IAC9F,KAAAc,uBAAuB,GAA0B,IAAI,CAACD,wBAAwB,CAACE,YAAY,EAAE;IAErG,KAAAC,qBAAqB,GAA0B,IAAIhB,eAAe,CAAO,IAAIiB,IAAI,EAAE,CAAC;IAC5E,KAAAC,oBAAoB,GAAqB,IAAI,CAACF,qBAAqB,CAACD,YAAY,EAAE;IAE1F,KAAAI,iBAAiB,GAA4B,IAAInB,eAAe,CAAS,CAAC,CAAC;IACnE,KAAAoB,gBAAgB,GAAuB,IAAI,CAACD,iBAAiB,CAACJ,YAAY,EAAE;IAEpF,KAAAM,iBAAiB,GAA8D,IAAIrB,eAAe,CAA2C;MAAEsB,MAAM,EAAE,KAAK;MAAEC,UAAU,EAAE;IAAK,CAAE,CAAC;IAC1K,KAAAC,gBAAgB,GAAyD,IAAI,CAACH,iBAAiB,CAACN,YAAY,EAAE;IAEtH,KAAAU,eAAe,GAA6B,IAAIzB,eAAe,CAAU,KAAK,CAAC;IACvE,KAAA0B,cAAc,GAAwB,IAAI,CAACD,eAAe,CAACV,YAAY,EAAE;IACjF,KAAAY,sBAAsB,GAA6B,IAAI3B,eAAe,CAAU,KAAK,CAAC;IAC9E,KAAA4B,qBAAqB,GAAwB,IAAI,CAACD,sBAAsB,CAACZ,YAAY,EAAE;IAE/F,KAAAc,sBAAsB,GAA6B,IAAI7B,eAAe,CAAU,KAAK,CAAC;IAC9E,KAAA8B,qBAAqB,GAAwB,IAAI,CAACD,sBAAsB,CAACd,YAAY,EAAE;IAE/F,KAAAgB,4BAA4B,GAA0B,IAAI/B,eAAe,CAAO,IAAIiB,IAAI,EAAE,CAAC;IACnF,KAAAe,2BAA2B,GAAqB,IAAI,CAACD,4BAA4B,CAAChB,YAAY,EAAE;IAExG,KAAAkB,6BAA6B,GAA4B,IAAIjC,eAAe,CAAS,CAAC,CAAC;IAC/E,KAAAkC,4BAA4B,GAAuB,IAAI,CAACD,6BAA6B,CAAClB,YAAY,EAAE;IAE7G,KAAAoB,QAAQ,GAAW,CAAC;IACpB,KAAAC,aAAa,GAA0C,EAA2C;IAClG,KAAAC,qBAAqB,GAA0C,EAA2C;IACzG,KAAAC,eAAe,GAAsB,EAAE;EAO3C;EAEGC,kBAAkBA,CAACC,SAAoB;IAC5C,IAAI,CAAC3B,wBAAwB,CAAC4B,IAAI,CAACD,SAAS,CAAC;EAC/C;EAEOE,uBAAuBA,CAACC,IAAU;IACvC,IAAI,CAAC3B,qBAAqB,CAACyB,IAAI,CAACE,IAAI,CAAC;EACvC;EAEOC,mBAAmBA,CAACC,QAAgB;IACzC,IAAI,CAAC1B,iBAAiB,CAACsB,IAAI,CAACI,QAAQ,CAAC;EACvC;EAEOC,mBAAmBA,CAACxB,MAAe,EAAEC,UAAmB;IAC7D,IAAI,CAACF,iBAAiB,CAACoB,IAAI,CAAC;MAAEnB,MAAM,EAAEA,MAAM;MAAEC,UAAU,EAAEA;IAAU,CAAE,CAAC;EACzE;EAEOwB,iBAAiBA,CAACC,GAAY;IACnC,IAAI,CAACvB,eAAe,CAACgB,IAAI,CAACO,GAAG,CAAC;EAChC;EAEOC,wBAAwBA,CAACD,GAAY;IAC1C,IAAI,CAACrB,sBAAsB,CAACc,IAAI,CAACO,GAAG,CAAC;EACvC;EAEOE,wBAAwBA,CAACF,GAAY;IAC1C,IAAI,CAACnB,sBAAsB,CAACY,IAAI,CAACO,GAAG,CAAC;EACvC;EAEOG,8BAA8BA,CAACH,GAAS;IAC7C,IAAI,CAACjB,4BAA4B,CAACU,IAAI,CAACO,GAAG,CAAC;EAC7C;EAEOI,+BAA+BA,CAACJ,GAAW;IAChD,IAAI,CAACf,6BAA6B,CAACQ,IAAI,CAACO,GAAG,CAAC;EAC9C;EAEOK,gCAAgCA,CAACC,OAAiB,EAAEC,QAAgB;IACzE,IAAInB,aAAa,GAAsB,EAAE;IACzC,IAAIoB,oBAAqC;IACzC,KAAK,IAAIC,MAAM,IAAIH,OAAO,EAAE;MAC1BE,oBAAoB,GAAG,IAAI,CAACE,iBAAiB,CAACD,MAAM,EAAEA,MAAM,CAACE,KAAM,EAAEJ,QAAQ,CAAC;MAC9EnB,aAAa,CAACwB,IAAI,CAACJ,oBAAoB,CAAC;IAC1C;IACA,OAAOpB,aAAa;IACpB;EACF;EAEAsB,iBAAiBA,CAACD,MAAc,EAAEE,KAAa,EAAEJ,QAAgB;IAC/D;IACA,OAAO;MACLM,EAAE,EAAEJ,MAAM,CAACK,EAAE;MACbC,OAAO,EAAEJ,KAAK;MACdK,QAAQ,EAAET,QAAQ,CAACU,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,EAAE;MAChDC,SAAS,EAAEV,MAAM,CAACW,YAAY;MAC9BC,OAAO,EAAEtE,MAAM,CAAC0D,MAAM,CAACW,YAAY,CAAC,CAACE,GAAG,CAACb,MAAM,CAACZ,QAAQ,GAAG,EAAE,EAAE,GAAG,CAAC,CAAC0B,MAAM,EAAE;MAC5EC,aAAa,EAAE,OAAO;MACtBC,QAAQ,EAAEhB,MAAM,CAACZ,QAAQ;MACzB6B,MAAM,EAAEjB,MAAM,CAACkB,MAAM;MACrBC,cAAc,EAAE,EAAE;MAClBC,IAAI,EAAE,aAAa;MACnBC,MAAM,EAAErB,MAAM;MACdsB,OAAO,EAAItB,MAAM,CAACZ,QAAQ,GAAG,CAAC,GAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAI;KACnD;EACH;EAEOmC,oCAAoCA,CAACC,KAAuB,EAAEC,IAAY;IAC/E,IAAIC,WAAW,GAAG,IAAI,CAACC,OAAO,EAAE;IAChC,IAAIC,GAAG,GAAG,IAAIpE,IAAI,CAACkE,WAAW,CAAC,CAAC,CAAC,CAAC;IAClC,IAAIG,GAAG,GAAG,IAAIrE,IAAI,CAACkE,WAAW,CAAC,CAAC,CAAC,CAAC;IAClC,IAAII,GAAG,GAAG,IAAItE,IAAI,CAACkE,WAAW,CAAC,CAAC,CAAC,CAAC;IAClC,IAAIK,GAAG,GAAG,IAAIvE,IAAI,CAACkE,WAAW,CAAC,CAAC,CAAC,CAAC;IAClC,IAAIM,GAAG,GAAG,IAAIxE,IAAI,CAACkE,WAAW,CAAC,CAAC,CAAC,CAAC;IAClC,IAAIO,GAAG,GAAG,IAAIzE,IAAI,CAACkE,WAAW,CAAC,CAAC,CAAC,CAAC;IAClC,IAAIQ,GAAG,GAAG,IAAI1E,IAAI,CAACkE,WAAW,CAAC,CAAC,CAAC,CAAC;IAAC;IACnC,IAAI/C,aAAa,GAAsB,EAAE;IACzC,IAAI,CAACwD,6BAA6B,CAACP,GAAG,EAAE,KAAK,EAAEJ,KAAK,EAAEC,IAAI,EAAE9C,aAAa,CAAC;IAC1E,IAAI,CAACwD,6BAA6B,CAACN,GAAG,EAAE,KAAK,EAAEL,KAAK,EAAEC,IAAI,EAAE9C,aAAa,CAAC;IAC1E,IAAI,CAACwD,6BAA6B,CAACL,GAAG,EAAE,KAAK,EAAEN,KAAK,EAAEC,IAAI,EAAE9C,aAAa,CAAC;IAC1E,IAAI,CAACwD,6BAA6B,CAACJ,GAAG,EAAE,KAAK,EAAEP,KAAK,EAAEC,IAAI,EAAE9C,aAAa,CAAC;IAC1E,IAAI,CAACwD,6BAA6B,CAACH,GAAG,EAAE,KAAK,EAAER,KAAK,EAAEC,IAAI,EAAE9C,aAAa,CAAC;IAC1E,IAAI,CAACwD,6BAA6B,CAACF,GAAG,EAAE,KAAK,EAAET,KAAK,EAAEC,IAAI,EAAE9C,aAAa,CAAC;IAC1E,IAAI,CAACwD,6BAA6B,CAACD,GAAG,EAAE,KAAK,EAAEV,KAAK,EAAEC,IAAI,EAAE9C,aAAa,CAAC;IAC1E,OAAOA,aAAa;EACtB;EAEAwD,6BAA6BA,CAACC,GAAS,EAAEC,SAAgE,EAAEb,KAAuB,EAAEC,IAAY,EAAE9C,aAAgC;IAChL,IAAIoB,oBAAqC;IACzC,IAAIM,EAAE,GAAW,CAAC;IAClB,IAAIiC,QAAQ,GAAG,CAAC;IAChB,IAAIC,WAAW,GAAG,CAAC;IACnB,IAAIC,MAAM,GAAG,CAAC;IACd,IAAIC,SAAS,GAAG,CAAC;IACjB,IAAIC,aAAa,GAAG,EAAE;IACtB,IAAIC,WAAW,GAAG,EAAE;IACpB,IAAIC,IAAI,GAAW,CAAC;IACpB,IAAIC,EAAE,GAAW,CAAC;IAClB,IAAIC,QAAQ,GAAW,CAAC;IACxB,IAAIC,MAAM,GAAW,CAAC;IACtB,IAAIC,KAAK,GAA4BxB,KAAK,CAACa,SAAS,CAAC;IACrD,IAAIY,UAAU,GAAY,KAAK;IAC/B,IAAID,KAAK,IAAI,IAAI,EAAE;MACjBA,KAAK,GAAG,EAAE;IACZ;IACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACrCR,aAAa,GAAGM,KAAK,CAACE,CAAC,CAAC,CAACN,IAAI,CAACQ,KAAK,CAAC,GAAG,CAAC;MACxCT,WAAW,GAAGK,KAAK,CAACE,CAAC,CAAC,CAACL,EAAE,CAACO,KAAK,CAAC,GAAG,CAAC;MACpCd,QAAQ,GAAGe,QAAQ,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC;MACrCH,WAAW,GAAGc,QAAQ,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC;MACxCF,MAAM,GAAGa,QAAQ,CAACV,WAAW,CAAC,CAAC,CAAC,CAAC;MACjCF,SAAS,GAAGY,QAAQ,CAACV,WAAW,CAAC,CAAC,CAAC,CAAC;MACpC,IAAI,CAACM,UAAU,EAAE;QACfL,IAAI,GAAGR,GAAG,CAACkB,QAAQ,CAAChB,QAAQ,EAAEC,WAAW,CAAC;MAC5C;MACAU,UAAU,GAAG,KAAK;MAClBJ,EAAE,GAAGT,GAAG,CAACkB,QAAQ,CAACd,MAAM,EAAEC,SAAS,CAAC;MACpC,IAAIS,CAAC,GAAG,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAE;QACxBT,aAAa,GAAGM,KAAK,CAACE,CAAC,GAAG,CAAC,CAAC,CAACN,IAAI,CAACQ,KAAK,CAAC,GAAG,CAAC;QAC5CT,WAAW,GAAGK,KAAK,CAACE,CAAC,GAAG,CAAC,CAAC,CAACL,EAAE,CAACO,KAAK,CAAC,GAAG,CAAC;QACxCd,QAAQ,GAAGe,QAAQ,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC;QACrCH,WAAW,GAAGc,QAAQ,CAACX,aAAa,CAAC,CAAC,CAAC,CAAC;QACxCF,MAAM,GAAGa,QAAQ,CAACV,WAAW,CAAC,CAAC,CAAC,CAAC;QACjCF,SAAS,GAAGY,QAAQ,CAACV,WAAW,CAAC,CAAC,CAAC,CAAC;QACpCG,QAAQ,GAAGV,GAAG,CAACkB,QAAQ,CAAChB,QAAQ,EAAEC,WAAW,CAAC;QAC9CQ,MAAM,GAAGX,GAAG,CAACkB,QAAQ,CAACd,MAAM,EAAEC,SAAS,CAAC;QAExC,IAAII,EAAE,IAAIC,QAAQ,EAAE;UAClBG,UAAU,GAAG,IAAI;QACnB;MACF;MACA,IAAI,CAACA,UAAU,EAAE;QACflD,oBAAoB,GAAG,IAAI,CAACwD,sBAAsB,CAACX,IAAI,EAAEC,EAAE,EAAExC,EAAE,EAAEoB,IAAI,CAAC;QACtE9C,aAAa,CAACwB,IAAI,CAACJ,oBAAoB,CAAC;MAC1C;IACF;EACF;EAEAwD,sBAAsBA,CAACX,IAAY,EAAEC,EAAU,EAAExC,EAAU,EAAEoB,IAAY;IACvE,OAAO;MACLrB,EAAE,EAAEC,EAAE;MACNK,SAAS,EAAEpE,MAAM,CAACsG,IAAI,CAAC,CAAC/B,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAACC,MAAM,EAAE;MAC5CF,OAAO,EAAEtE,MAAM,CAACuG,EAAE,CAAC,CAAChC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAACC,MAAM,EAAE;MACxCR,OAAO,EAAE,EAAE;MACXS,aAAa,EAAE,EAAE;MACjBC,QAAQ,EAAE,IAAI,CAAChE,cAAc,CAACwG,YAAY,CAAClH,MAAM,CAACsG,IAAI,CAAC,CAAC/B,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAACC,MAAM,EAAE,EAAExE,MAAM,CAACuG,EAAE,CAAC,CAAChC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAACC,MAAM,EAAE,CAAC,CAAC2C,OAAO,GAAG,EAAE;MAAC;MAC5HxC,MAAM,EAAE5E,YAAY,CAACqH,QAAQ;MAC7BvC,cAAc,EAAE,iCAAiC;MACjDC,IAAI,EAAEK;KACP;EACH;EAEAkC,qBAAqBA,CAACzC,MAAoB;IACxC,IAAIA,MAAM,KAAK7E,YAAY,CAACqH,QAAQ,EAAE;MACpC,OAAO,6CAA6C;IACtD,CAAC,MAAM,IAAIxC,MAAM,KAAK7E,YAAY,CAACuH,SAAS,EAAE;MAC5C,OAAO,6CAA6C;IACtD,CAAC,MAAM;MACL,OAAO,6CAA6C;IACtD;EACF;EAEAjC,OAAOA,CAAA;IACL,IAAIkC,IAAI,GAAG,IAAIrG,IAAI,EAAE;IACrB,IAAIsG,IAAI,GAAG,EAAE;IAEb,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3B,IAAIa,KAAK,GAAGF,IAAI,CAACG,UAAU,EAAE,GAAGH,IAAI,CAACI,SAAS,EAAE,GAAGf,CAAC;MACpD,IAAId,GAAG,GAAG,IAAI5E,IAAI,CAACqG,IAAI,CAACK,UAAU,CAACH,KAAK,CAAC,CAAC,CAACI,WAAW,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;MACrEN,IAAI,CAAC3D,IAAI,CAACiC,GAAG,CAAC;IAChB;IACA,OAAO0B,IAAI;EACb;EAEF;;;;;;;;;EASEO,yBAAyBA,CAACC,IAAS,EAAEC,eAAwC;IAC3E,IAAIA,eAAe,IAAI,IAAI,EAAE;MAC3B,OAAO;QACL1D,GAAG,EAAE,IAAI;QACT2D,KAAK,EAAE;OACR;IACH;IACA,IAAIC,GAAG,GAAW,EAAE;IACpB,IAAIC,KAAK,GAAW,EAAE;IACtBA,KAAK,GAAGJ,IAAI,CAAC5D,SAAS,CAACiE,QAAQ,EAAE,CAACC,QAAQ,EAAE,GAAG,GAAG,GAAGN,IAAI,CAAC5D,SAAS,CAACmE,UAAU,EAAE,CAACD,QAAQ,EAAE;IAC3F,IAAIlC,aAAa,GAAGgC,KAAK,CAACtB,KAAK,CAAC,GAAG,CAAC;IACpC,IAAIV,aAAa,CAAC,CAAC,CAAC,CAACkC,QAAQ,EAAE,CAACzB,MAAM,KAAK,CAAC,EAAE;MAC5CuB,KAAK,GAAGA,KAAK,GAAG,GAAG;IACrB;IACA,IAAIhC,aAAa,CAAC,CAAC,CAAC,CAACS,MAAM,KAAK,CAAC,EAAE;MACjCuB,KAAK,GAAG,GAAG,GAAGA,KAAK;IACrB;IACA;IACAD,GAAG,GAAGH,IAAI,CAAC1D,OAAO,CAAC+D,QAAQ,EAAE,CAACC,QAAQ,EAAE,GAAG,GAAG,GAAGN,IAAI,CAAC1D,OAAO,CAACiE,UAAU,EAAE,CAACD,QAAQ,EAAE;IACrF,IAAIjC,WAAW,GAAG8B,GAAG,CAACrB,KAAK,CAAC,GAAG,CAAC;IAChC,IAAIT,WAAW,CAAC,CAAC,CAAC,CAACiC,QAAQ,EAAE,CAACzB,MAAM,KAAK,CAAC,EAAE;MAC1CsB,GAAG,GAAGA,GAAG,GAAG,GAAG;IACjB;IACA,IAAI9B,WAAW,CAAC,CAAC,CAAC,CAACQ,MAAM,KAAK,CAAC,EAAE;MAC/BsB,GAAG,GAAG,GAAG,GAAGA,GAAG;IACjB;IAEA,IAAIF,eAAe,CAACpB,MAAM,KAAK,CAAC,EAAE;MAChC,OAAO;QACLtC,GAAG,EAAE,IAAI;QACT2D,KAAK,EAAE;OACR;IACH;IAEA,KAAK,IAAIM,SAAS,IAAIP,eAAe,EAAE;MACrC,IAAIQ,qBAAqB,GAAGD,SAAS,CAAClC,IAAI,CAACQ,KAAK,CAAC,GAAG,CAAC;MACrD,IAAI2B,qBAAqB,CAAC,CAAC,CAAC,CAAC5B,MAAM,KAAK,CAAC,EAAE;QACzC4B,qBAAqB,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,qBAAqB,CAAC,CAAC,CAAC;MAC3D;MACA,IAAInC,IAAI,GAAGmC,qBAAqB,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,qBAAqB,CAAC,CAAC,CAAC;MAEpE,IAAIC,mBAAmB,GAAGF,SAAS,CAACjC,EAAE,CAACO,KAAK,CAAC,GAAG,CAAC;MACjD,IAAI4B,mBAAmB,CAAC,CAAC,CAAC,CAAC7B,MAAM,KAAK,CAAC,EAAE;QACvC6B,mBAAmB,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,mBAAmB,CAAC,CAAC,CAAC;MACvD;MACA,IAAInC,EAAE,GAAGmC,mBAAmB,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,mBAAmB,CAAC,CAAC,CAAC;MAE9D,IAAIpC,IAAI,IAAI8B,KAAK,IAAI7B,EAAE,IAAI4B,GAAG,EAAE;QAC9B,OAAO;UACL5D,GAAG,EAAE,KAAK;UACV2D,KAAK,EAAE;SACR;MACH;IACF;IAEA,KAAK,IAAIM,SAAS,IAAIP,eAAe,EAAE;MACrC,IAAKO,SAAS,CAAClC,IAAI,GAAG8B,KAAK,IAAII,SAAS,CAACjC,EAAE,GAAG6B,KAAK,IAAMD,GAAG,GAAGK,SAAS,CAAClC,IAAI,IAAI6B,GAAG,GAAGK,SAAS,CAACjC,EAAG,EAAE;QACpG,OAAO;UACLhC,GAAG,EAAE,KAAK;UACV2D,KAAK,EAAE;SACR;MACH;IACF;IAEA,KAAK,IAAIM,SAAS,IAAIP,eAAe,EAAE;MACrC,IAAIO,SAAS,CAAClC,IAAI,GAAG8B,KAAK,IAAII,SAAS,CAACjC,EAAE,GAAG4B,GAAG,EAAE;QAChD,OAAO;UACL5D,GAAG,EAAE,IAAI;UACT2D,KAAK,EAAE;SACR;MACH;IACF;IAEA,OAAO;MACL3D,GAAG,EAAE,KAAK;MACV2D,KAAK,EAAE;KACR;EACH;EAEAS,UAAUA,CAACC,MAA2B;IACpC,OAAO,IAAI,CAACnI,IAAI,CAACoI,IAAI,CAAsB1I,WAAW,GAAG,wBAAwB,EAAEyI,MAAM,CAAC;EAC5F;EAEAE,aAAaA,CAACC,QAAgB;IAC5B,OAAO,IAAI,CAACtI,IAAI,CAACoI,IAAI,CAAC1I,WAAW,GAAG,+BAA+B,GAAG4I,QAAQ,EAAE,EAAE,CAAC;EACrF;EAEAC,mBAAmBA,CAACpG,IAAU;IAC5B,OAAOA,IAAI,CAACyF,QAAQ,EAAE,GAAG,GAAG,GAAGzF,IAAI,CAAC2F,UAAU,EAAE,GAAG,MAAM,GAAG,IAAI,CAAC7H,cAAc,CAACuI,gBAAgB,CAACrG,IAAI,CAACsG,MAAM,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,CAACxI,cAAc,CAACyI,kBAAkB,CAACvG,IAAI,CAACwG,QAAQ,EAAE,CAAC,GAAG,GAAG,GAAGxG,IAAI,CAACyG,OAAO,EAAE;EACxM;EAEAC,iCAAiCA,CAACC,YAAgB;IAChD,OAAO,IAAI,CAAC9I,IAAI,CAACoI,IAAI,CAACxI,eAAe,GAAG,mCAAmC,EAAEkJ,YAAY,CAAC;EAC5F;EAEAC,gCAAgCA,CAACD,YAAgB;IAC/C,OAAO,IAAI,CAAC9I,IAAI,CAACoI,IAAI,CAACxI,eAAe,GAAG,kCAAkC,EAAEkJ,YAAY,CAAC;EAC3F;EAEAE,4BAA4BA,CAACF,YAAqD,EAAEG,OAAqB;IACvG,OAAQ,IAAI,CAACjJ,IAAI,CAACoI,IAAI,CAACxI,eAAe,GAAG,8BAA8B,EAAEkJ,YAAY,EAAE;MAAEG;IAAO,CAAE,CAAyD;EAC7J;EAEAC,gBAAgBA,CAACC,oBAAyB;IACxC,MAAMC,aAAa,GAAG,EAAE;IAExB,KAAK,MAAMN,YAAY,IAAIK,oBAAoB,EAAE;MAC/C,MAAME,SAAS,GAAGP,YAAY,CAAC3G,IAAI,CAACkE,KAAK,CAAC,GAAG,CAAC;MAC9C,MAAMlE,IAAI,GAAG,IAAI1B,IAAI,CAAC,GAAG4I,SAAS,CAAC,CAAC,CAAC,IAAIA,SAAS,CAAC,CAAC,CAAC,IAAIA,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;MAExE,KAAK,MAAMC,QAAQ,IAAIR,YAAY,CAACA,YAAY,EAAE;QAChD,MAAMS,cAAc,GAAGD,QAAQ,CAACzD,IAAI,CAACQ,KAAK,CAAC,GAAG,CAAC;QAC/C,MAAMmD,YAAY,GAAGF,QAAQ,CAACxD,EAAE,CAACO,KAAK,CAAC,GAAG,CAAC;QAC3C,MAAMoD,SAAS,GAAGnD,QAAQ,CAACiD,cAAc,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAMG,WAAW,GAAGpD,QAAQ,CAACiD,cAAc,CAAC,CAAC,CAAC,CAAC;QAC/C,MAAMI,OAAO,GAAGrD,QAAQ,CAACkD,YAAY,CAAC,CAAC,CAAC,CAAC;QACzC,MAAMI,SAAS,GAAGtD,QAAQ,CAACkD,YAAY,CAAC,CAAC,CAAC,CAAC;QAE3C;QACA;QACA,IAAIG,OAAO,GAAGF,SAAS,IAAKE,OAAO,KAAKF,SAAS,IAAIG,SAAS,GAAGF,WAAY,EAAE;UAC7E;UACA,MAAMG,gBAAgB,GAAG,CAAC,EAAE,GAAGJ,SAAS,IAAI,EAAE,GAAGC,WAAW;UAE5DI,OAAO,CAACC,GAAG,CAACF,gBAAgB,CAAC;UAC7B;UACAT,aAAa,CAAChG,IAAI,CAAC;YACjB4G,UAAU,EAAE,CAAC7H,IAAI,CAACsG,MAAM,EAAE,CAAC;YAC3BwB,SAAS,EAAEX,QAAQ,CAACzD,IAAI;YACxBqE,OAAO,EAAE;WACV,CAAC;UAEF;UACA/H,IAAI,CAACgI,OAAO,CAAChI,IAAI,CAACyG,OAAO,EAAE,GAAG,CAAC,CAAC;UAEhC;UACA,MAAMwB,eAAe,GAAGT,OAAO,GAAG,EAAE,GAAGC,SAAS;UAEhD;UACAR,aAAa,CAAChG,IAAI,CAAC;YACjB4G,UAAU,EAAE,CAAC7H,IAAI,CAACsG,MAAM,EAAE,CAAC;YAC3BwB,SAAS,EAAE,OAAO;YAClBC,OAAO,EAAEZ,QAAQ,CAACxD;WACnB,CAAC;QACJ,CAAC,MAAM;UACL;UACAsD,aAAa,CAAChG,IAAI,CAAC;YACjB4G,UAAU,EAAE,CAAC7H,IAAI,CAACsG,MAAM,EAAE,CAAC;YAC3BwB,SAAS,EAAEX,QAAQ,CAACzD,IAAI;YACxBqE,OAAO,EAAEZ,QAAQ,CAACxD;WACnB,CAAC;QACJ;MACF;IACF;IAEAgE,OAAO,CAACC,GAAG,CAACX,aAAa,CAAC;IAC1B,OAAOA,aAAa;EACtB;EAEAiB,kBAAkBA,CAACC,OAAc;IAC/B,OAAOA,OAAO,CAACC,GAAG,CAAEpC,MAAW,IAAI;MACjC,MAAMtC,IAAI,GAAG,IAAIpF,IAAI,CAAC0H,MAAM,CAACqC,MAAM,CAAC3E,IAAI,CAAC;MACzC,MAAMC,EAAE,GAAG,IAAIrF,IAAI,CAAC0H,MAAM,CAACqC,MAAM,CAAC1E,EAAE,CAAC;MACrC,OAAO;QACL3C,KAAK,EAAEgF,MAAM,CAACsC,MAAM;QACpBC,KAAK,EAAE7E,IAAI;QACX8E,OAAO,EAAE,YAAY;QACrBC,aAAa,EAAE;UACblG,IAAI,EAAE,QAAQ;UACdmG,SAAS,EAAE;SACZ;QACDC,UAAU,EAAE,KAAK;QACjBC,QAAQ,EAAE,KAAK;QACfC,MAAM,EAAE;OACT;IACH,CAAC,CAAC;EACJ;EAAC,QAAAC,CAAA,G;qBA5YUnL,eAAe,EAAAoL,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAO,EAAA,CAAAC,aAAA,GAAAT,EAAA,CAAAC,QAAA,CAAAS,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAfhM,eAAe;IAAAiM,OAAA,EAAfjM,eAAe,CAAAkM,IAAA;IAAAC,UAAA,EAFd;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}