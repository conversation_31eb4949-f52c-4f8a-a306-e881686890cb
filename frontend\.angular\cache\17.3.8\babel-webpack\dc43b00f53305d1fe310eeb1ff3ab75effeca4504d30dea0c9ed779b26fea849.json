{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate, __param } from \"tslib\";\nimport { CommonModule, DOCUMENT } from '@angular/common';\nimport { Component, Inject, Input } from '@angular/core';\nimport { FormsModule, ReactiveFormsModule, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { switchMap, take } from 'rxjs/operators';\nimport { PackageType, PackagesModel } from 'src/app/core/models/package.model';\nimport { slideInOut2, slideInOut } from 'src/app/helpers/my-animations';\nimport { SubSink } from 'subsink';\nimport RevolutCheckout from \"@revolut/checkout\";\nimport { of } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport { BuyPackageSuggestionBoxComponent } from './buy-package-suggestion-box/buy-package-suggestion-box.component';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { PrimeInputDropdownComponent } from 'src/app/shared/prime/input/prime-input-dropdown/prime-input-dropdown.component';\nconst isProductionMode = environment.production;\nlet BuyPackageComponent = class BuyPackageComponent {\n  constructor(authService, activatedRoute, location, classroomService, generalService, packageService, toastService, confirmDialogService, sanitizer, router, renderer, document) {\n    this.authService = authService;\n    this.activatedRoute = activatedRoute;\n    this.location = location;\n    this.classroomService = classroomService;\n    this.generalService = generalService;\n    this.packageService = packageService;\n    this.toastService = toastService;\n    this.confirmDialogService = confirmDialogService;\n    this.sanitizer = sanitizer;\n    this.router = router;\n    this.renderer = renderer;\n    this.document = document;\n    this.inTrial = false;\n    this.hideSection = false;\n    this.user = {};\n    this.subs = new SubSink();\n    this.guid = \"guid-guid\";\n    this.packageToBuyOrderDeails = {};\n    this.phoneCodes = this.generalService.getPhoneCodes();\n    this.currentStep = 1;\n    this.userClassrooms = [];\n    this.classroomId = \"\";\n    this.isNew = false;\n    this.numberOfStudents = [\"1\", \"2\", \"3\"];\n    this.payload = {\n      pk: 'your-public-key',\n      amount: 1000,\n      locale: 'en',\n      txnType: 'tds'\n    };\n    this.selectedClassroom = {};\n    this.selectedPackageToBuy = {};\n    this.selectedLevel = \"A1\";\n    this.selectedDefaultLevel = \"A1\";\n    this.selectedNumberOfStudents = \"1\";\n    this.selectedLanguage = {};\n    this.selectedHoursIndex = 2;\n    this.selectedBillingType = \"receipt\";\n    this.selectedPhoneCode = {};\n    this.selectedResidenceCountry = {};\n    this.buyerUserDetails = {};\n    this.mltLanguages = this.generalService.mltLanguages;\n    this.mltLevels = this.generalService.mltLevels;\n    this.countries = this.generalService.countries;\n    this.mltPricesHourly = [];\n    this.selectedHours = {};\n    this.mltPackages = this.generalService.mltPackages;\n    this.defaultPackages = PackagesModel.getDefaultPackages();\n    this.newPackages = [];\n    this.reqBody = {};\n    this.form = new UntypedFormGroup({});\n    this.classroomFromQueryParams = {};\n    this.showSplitPayment = false;\n    this.splitPaymentPackages = [];\n    this.leadDynoAffiliateId = localStorage.getItem('leadDynoAffiliateId');\n    this.leadDynoPrivateKey = environment.leadDynoPrivateKey;\n    this.leadDynoScriptLoaded = false;\n    this.expiresIn = 1;\n    this.isReceipt = true;\n    this.isInvoice = false;\n    this.isSubmitted = false;\n    this.agreed = false;\n    this.termsError = false;\n  }\n  ngOnInit() {\n    if (!this.inTrial) {\n      const classroomString = this.activatedRoute.snapshot.queryParams['classroom'];\n      if (classroomString) {\n        this.classroomFromQueryParams = JSON.parse(classroomString);\n      }\n    }\n    this.responsiveOptions = [{\n      breakpoint: '1199px',\n      numVisible: 1,\n      numScroll: 1\n    }, {\n      breakpoint: '991px',\n      numVisible: 2,\n      numScroll: 1\n    }, {\n      breakpoint: '767px',\n      numVisible: 1,\n      numScroll: 1\n    }];\n    this.user = this.authService.getLoggedInUser();\n    this.initializeData();\n    this.loadToltScript('https://cdn.tolt.io/tolt.js', '344bed5a-704b-49eb-9ec5-f0215c879772');\n    // TODO: Add LeadDyno script\n    this.loadScript('https://static.leaddyno.com/js').then(() => {\n      // LeadDyno script loaded successfully\n      // Now, you can add your events\n      console.log('LeadDyno script loaded, executing LeadDyno dependent code...');\n      this.leadDynoScriptLoaded = true;\n      this.addLeadDynoEvents();\n      if (this.leadDynoScriptLoaded) {\n        this.packageService.leadDynoGetAffiliateEvent({\n          key: this.leadDynoPrivateKey\n        }).subscribe({\n          next: res => {\n            console.log(res);\n          },\n          error: err => {\n            console.log(err);\n          }\n        });\n        // this.packageService.addPurchaseEvent(purchaseData).subscribe((res: any) => {\n        //   console.log(res);\n        // });\n      }\n    }).catch(error => console.log('Error loading LeadDyno script:', error));\n  }\n  addLeadDynoEvents() {\n    // Assuming LeadDyno is available globally\n    // Add your LeadDyno events here\n    if (typeof LeadDyno !== 'undefined') {\n      LeadDyno.key = \"6f488593e746d71bf3743119f96e36b92acfd049\";\n      LeadDyno.recordVisit();\n      LeadDyno.autoWatch();\n    } else {\n      console.error('LeadDyno is not available');\n    }\n  }\n  loadScript(url) {\n    return new Promise((resolve, reject) => {\n      const script = this.renderer.createElement('script');\n      script.src = url;\n      script.onload = () => {\n        resolve();\n      };\n      script.onerror = error => {\n        reject(error);\n      };\n      this.renderer.appendChild(document.body, script);\n    });\n  }\n  loadJsScript(renderer, src) {\n    const script = renderer.createElement('script');\n    script.type = 'text/javascript';\n    script.src = src;\n    renderer.appendChild(this.document.body, script);\n    return script;\n  }\n  removeLeadDynoScript() {\n    const scriptElement = document.querySelector('script[src=\"https://static.leaddyno.com/js\"]');\n    if (scriptElement) {\n      scriptElement.remove();\n      console.log('LeadDyno script removed');\n    }\n  }\n  loadToltScript(url, dataTolt) {\n    return new Promise((resolve, reject) => {\n      const script = this.renderer.createElement('script');\n      script.src = url;\n      script.setAttribute('id', 'tolt-player');\n      script.setAttribute('data-tolt', dataTolt);\n      script.onload = () => {\n        resolve();\n      };\n      script.onerror = error => {\n        reject(error);\n      };\n      this.renderer.appendChild(document.head, script);\n    });\n  }\n  loadLeadDynoStatic() {\n    console.log('preparing to load LeadDyno...Static');\n    let node = document.createElement('script');\n    node.setAttribute(\"id\", \"leaddyno-static\");\n    node.src = \"https://static.leaddyno.com/js\";\n    node.type = 'text/javascript';\n    node.async = true;\n    node.charset = 'utf-8';\n    document.getElementsByTagName('head')[0].appendChild(node);\n  }\n  ngAfterViewInit() {\n    this.rightSide = document.getElementById('package-overview');\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n    this.removeLeadDynoScript();\n    const scriptElement = document.getElementById('tolt-player');\n    if (scriptElement) {\n      scriptElement.remove();\n    }\n  }\n  get hasSplitPayment() {\n    return this.showSplitPayment;\n  }\n  get errorControl() {\n    return this.form.controls;\n  }\n  trialSection() {\n    this.hideSection = !this.hideSection;\n    setTimeout(() => {\n      [].forEach.call(document.getElementsByClassName('btn'), el => {\n        el.classList.add('hvr-glow');\n      });\n    }, 1000);\n  }\n  initializeForm() {\n    this.selectedResidenceCountry = this.countries.find(obj => obj.name === this.user.residence);\n    this.selectedPhoneCode = this.phoneCodes.filter(el => el.code === this.generalService.extractCountryPrefix(this.user.phone))[0] || this.generalService.getDefaultPhoneCode();\n    console.log(this.selectedResidenceCountry);\n    this.form = new UntypedFormGroup({\n      fname: new UntypedFormControl(this.user.firstName, {\n        validators: [Validators.required]\n      }),\n      lname: new UntypedFormControl(this.user.lastName, {\n        validators: [Validators.required]\n      }),\n      company: new UntypedFormControl(\"\", {}),\n      profession: new UntypedFormControl(\"\", {}),\n      country: new UntypedFormControl(this.selectedResidenceCountry, {\n        validators: [Validators.required]\n      }),\n      street: new UntypedFormControl(\"\", {\n        validators: [Validators.required]\n      }),\n      number: new UntypedFormControl(\"\", {\n        validators: [Validators.required]\n      }),\n      city: new UntypedFormControl(\"\", {\n        validators: [Validators.required]\n      }),\n      postcode: new UntypedFormControl(\"\", {\n        validators: [Validators.required]\n      }),\n      tax: new UntypedFormControl(\"\", {}),\n      email: new UntypedFormControl(this.user.email, {\n        validators: [Validators.required]\n      }),\n      phone: new UntypedFormControl(this.generalService.extractPhoneNumber(this.user.phone), {\n        validators: [Validators.required]\n      }),\n      tin: new UntypedFormControl(\"\", {})\n    });\n  }\n  updateFormValidationRules() {\n    if (this.isInvoice) {\n      this.form.controls['tin'].setValidators([Validators.required]);\n    } else {\n      this.form.controls['tin'].setValidators([]);\n    }\n    this.form.controls['tin'].updateValueAndValidity();\n  }\n  styleAll() {\n    setTimeout(() => {\n      if (!this.isNew) {\n        this.styleClassrooms();\n      } else {\n        this.selectedBtnStyle(`package-new`);\n      }\n      this.styleLanguages();\n      this.styleHours();\n      this.styleNumberOfStudents();\n    }, 1000);\n  }\n  deStyleAll() {\n    this.styleClassrooms();\n    this.styleLanguages();\n    this.styleHours();\n    this.styleNumberOfStudents();\n  }\n  styleClassrooms() {\n    this.selectedBtnStyle(`package-btn-${this.selectedClassroom.id}`, `package-btn-teacher-${this.selectedClassroom?.id}`);\n  }\n  styleLanguages() {\n    this.selectedBtnStyle(`language-btn-${this.selectedLanguage.code}`);\n  }\n  styleNumberOfStudents() {\n    if (this.selectedNumberOfStudents > \"3\") {\n      this.selectedNumberOfStudents = \"3+\";\n    }\n    this.selectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`);\n  }\n  styleHours() {\n    this.selectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-hours-${this.mltPricesHourly[this.selectedHoursIndex].price}`);\n    this.selectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-per-${this.mltPricesHourly[this.selectedHoursIndex].price}`);\n  }\n  selectedBtnStyle(id, extraId) {\n    let color = this.inTrial ? '#4895ef' : this.getGradientColor(this.selectedPackageToBuy.type);\n    let btn = document.getElementById(id);\n    if (btn && this.selectedPackageToBuy) {\n      btn.style.backgroundImage = color;\n    }\n    if (btn && !id.includes('package-new')) {\n      btn.style.color = \"white\";\n      btn.style.opacity = \"1\";\n    }\n    if (extraId) {\n      let extraElement = document.getElementById(extraId);\n      if (extraElement) {\n        extraElement.style.color = \"white\";\n      }\n    }\n  }\n  deSelectedBtnStyle(id, extraId) {\n    let color = this.inTrial ? '#4895ef' : this.getCasualColor();\n    let btn = document.getElementById(id);\n    if (btn) {\n      if (!this.isNew && !id.includes('package-btn') && !id.includes('number-btn') && !id.includes('hour-btn') && !id.includes('package-new') && !id.includes('level')) {\n        btn.style.color = \"#CCD6FF\";\n      } else {\n        btn.style.color = color;\n      }\n      if (!id.includes('package-new')) {\n        btn.style.backgroundColor = \"white\";\n        btn.style.background = \"white\";\n      }\n    }\n    if (extraId) {\n      let extraElement = document.getElementById(extraId);\n      if (extraElement) {\n        extraElement.style.color = \"#2d2a4b\";\n      }\n    }\n  }\n  getBtnStyle(notToBeDisabled) {\n    let color = this.inTrial ? '#4895ef' : this.getCasualColor();\n    if (this.isNew || notToBeDisabled) {\n      return {\n        'border': `1px solid ${color}`,\n        'color': `${color}`\n      };\n    }\n    return {\n      'border': `1px solid ${color}`,\n      'color': `${color}`,\n      'opacity': '0.5',\n      'cursor': 'initial'\n    };\n  }\n  disableLanguage(id) {\n    let btn = document.getElementById(id);\n    btn.style.pointerEvents = 'none';\n    btn.style.opacity = '0.5';\n  }\n  enableLanguage(id) {\n    let btn = document.getElementById(id);\n    btn.style.pointerEvents = 'initial';\n    btn.style.opacity = '1';\n  }\n  getLevelIsGreaterThanSelected(level) {\n    if (this.selectedClassroom.activeLevel == \"TBD\") {\n      return true;\n    }\n    return level >= this.selectedClassroom.activeLevel;\n  }\n  compareLevels(level1, level2) {\n    const levelsOrder = ['TBD', 'A1', 'A2', 'B1', 'B2', 'C1', 'C2', 'BS', 'NT21', 'NT22'];\n    return levelsOrder.indexOf(level1) - levelsOrder.indexOf(level2);\n  }\n  getGradientColor(type) {\n    if (type == PackageType.REGULAR) {\n      return \"linear-gradient(to top, #122171, #353792, #544eb3, #7366d6, #927ffa)\";\n    }\n    if (type == PackageType.FLEX) {\n      return \"linear-gradient(to bottom, #7588f4, #5f6ed1, #4955af, #323d8e, #1b276e)\";\n    }\n    if (type == PackageType.PREMIUM) {\n      return \"linear-gradient(to top, #122171, #004b99, #0074bb, #009ed7, #4dc8f0)\";\n    }\n    return \"\";\n  }\n  getCasualColor() {\n    return \"#657AEF\";\n  }\n  selectClassroom(classroom) {\n    this.isNew = true;\n    [].forEach.call(document.getElementsByClassName('btn'), el => {\n      if (!el.id.includes('package-btn') && !el.id.includes('hour-btn') && !el.id.includes('package-new')) {\n        el.classList.remove('hvr-glow');\n      }\n      setTimeout(() => {\n        // el.classList.add('disabled')\n      }, 300);\n    });\n    this.deSelectedBtnStyle(`package-new`);\n    this.deSelectedBtnStyle(`package-btn-${this.selectedClassroom.id}`, `package-btn-teacher-${this.selectedClassroom?.id}`);\n    // this.deSelectedBtnStyle(`level-btn-${this.selectedLevel}`)\n    this.deSelectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`);\n    this.deSelectedBtnStyle(`language-btn-${this.selectedLanguage.code}`);\n    for (let language of this.mltLanguages) {\n      if (this.selectedClassroom.language == language.name) {\n        this.deSelectedBtnStyle(`language-btn-${language.code}`);\n      }\n    }\n    this.selectedClassroom = classroom;\n    // /Updates the selected classroom's student list by removing any duplicate students.\n    this.selectedClassroom.classroomStudents = this.classroomService.getuniqueClassroomStudents(this.selectedClassroom.classroomStudents);\n    for (let language of this.mltLanguages) {\n      if (this.selectedClassroom.language !== language.name) {\n        this.disableLanguage(`language-btn-${language.code}`);\n      }\n    }\n    this.selectedLevel = this.selectedClassroom.activeLevel;\n    if (this.selectedClassroom.activeLevel == \"TBD\") {\n      this.selectedLevel = \"A1\";\n    }\n    this.selectedDefaultLevel = this.selectedClassroom.activeLevel;\n    this.selectedNumberOfStudents = this.selectedClassroom.classroomStudents.length.toString();\n    this.selectedLanguage = this.mltLanguages.filter(el => el.name.toLowerCase() == this.selectedClassroom.language.toLowerCase())[0];\n    this.reqBody.language = this.selectedLanguage.name;\n    this.reqBody.level = this.selectedLevel;\n    this.toggleSplitPaymentDisplay();\n    console.log(this.showSplitPayment);\n    this.getNewPackagePrices();\n    this.getNewSplitPackagePrices();\n    this.styleAll();\n    this.isNew = false;\n  }\n  selectLanguage(language) {\n    if (this.isNew) {\n      this.deSelectedBtnStyle(`language-btn-${this.selectedLanguage.code}`);\n      this.selectedLanguage = language;\n      this.reqBody.language = this.selectedLanguage.name;\n      this.getNewPackagePrices();\n      for (let language of this.mltLanguages) {\n        this.deSelectedBtnStyle(`language-btn-${language.code}`);\n      }\n      this.styleLanguages();\n    }\n  }\n  selectLevel(level) {\n    if (this.isNew || this.getLevelIsGreaterThanSelected(level)) {\n      this.selectedLevel = level;\n      this.reqBody.level = this.selectedLevel;\n      this.getNewPackagePrices();\n      this.getNewSplitPackagePrices();\n    }\n  }\n  selectNumber(number) {\n    if (this.isNew) {\n      this.deSelectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`);\n      this.selectedNumberOfStudents = number;\n      this.selectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`);\n      this.reqBody.numberOfStudents = this.selectedNumberOfStudents;\n      this.getNewPackagePrices();\n      this.getNewSplitPackagePrices();\n    }\n  }\n  selectHours(hour, i) {\n    this.deSelectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-hours-${this.mltPricesHourly[this.selectedHoursIndex].price}`);\n    this.deSelectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-per-${this.mltPricesHourly[this.selectedHoursIndex].price}`);\n    this.selectedHoursIndex = i;\n    this.selectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-hours-${this.mltPricesHourly[this.selectedHoursIndex].price}`);\n    this.selectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-per-${this.mltPricesHourly[this.selectedHoursIndex].price}`);\n    this.selectedPrice = this.mltPricesHourly[this.selectedHoursIndex].price;\n    this.expiresIn = this.mltPricesHourly[this.selectedHoursIndex].expirationMonths;\n    this.selectedHours = this.mltPricesHourly[this.selectedHoursIndex];\n  }\n  selectPackage(packageType) {\n    this.selectedPackageToBuy = packageType;\n    this.rightSide.style.background = this.getGradientColor(this.selectedPackageToBuy.type);\n    this.styleAll();\n  }\n  buyNew() {\n    [].forEach.call(document.getElementsByClassName('btn'), el => {\n      el.classList.add('hvr-glow');\n      el.classList.remove('disabled');\n    });\n    for (let language of this.mltLanguages) {\n      if (this.selectedClassroom.language !== language.name) {\n        this.enableLanguage(`language-btn-${language.code}`);\n      }\n    }\n    this.deSelectedBtnStyle(`package-btn-${this.selectedClassroom.id}`, `package-btn-teacher-${this.selectedClassroom?.id}`);\n    this.selectedBtnStyle(`package-new`);\n    this.isNew = true;\n    this.selectedLanguage = this.mltLanguages[0];\n    this.selectLanguage(this.selectedLanguage);\n    this.deSelectedBtnStyle(`level-btn-${this.selectedLevel}`);\n    this.selectedLevel = \"A1\";\n    this.selectedDefaultLevel = \"A1\";\n    this.selectLevel(this.selectedLevel);\n    this.deSelectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`);\n    this.selectedNumberOfStudents = \"1\";\n    this.styleAll();\n    this.getNewPackagePrices();\n    // this.getNewSplitPackagePrices();\n    this.showSplitPayment = false;\n  }\n  chooseReceipt() {\n    this.isReceipt = true;\n    this.isInvoice = false;\n    this.updateFormValidationRules();\n  }\n  chooseInvoice() {\n    this.isReceipt = false;\n    this.isInvoice = true;\n    this.updateFormValidationRules();\n  }\n  next() {\n    window.scrollTo(0, 0);\n    this.currentStep++;\n    if (this.currentStep == 2) {\n      return;\n    }\n    if (this.currentStep == 3) {\n      this.isSubmitted = true;\n      if (this.form.valid) {\n        let formValue = this.form.value;\n        this.buyerUserDetails.fname = formValue.fname;\n        this.buyerUserDetails.lname = formValue.lname;\n        this.buyerUserDetails.email = formValue.email;\n        this.buyerUserDetails.city = formValue.city;\n        this.buyerUserDetails.company = formValue.company;\n        this.buyerUserDetails.streetName = formValue.street;\n        this.buyerUserDetails.streetNumber = formValue.number;\n        this.buyerUserDetails.tax = formValue.tax;\n        this.buyerUserDetails.profession = formValue.profession;\n        this.buyerUserDetails.postcode = formValue.postcode;\n        this.buyerUserDetails.tin = formValue.tin;\n        this.buyerUserDetails.phone = formValue.phone;\n        this.buyerUserDetails.country = formValue.country.name;\n        return;\n      } else {\n        this.toastService.setShowToastmessage({\n          severity: 'info',\n          summary: '',\n          detail: 'Please fill all the required fields with *'\n        });\n        this.currentStep = 2;\n        return;\n      }\n    }\n    if (this.currentStep == 4) {\n      this.payload.amount = (this.selectedPrice + this.selectedPackageToBuy.costPlus) * 100;\n      return;\n    }\n    if (this.currentStep < 5) {\n      this.currentStep++;\n    }\n    // let classroom = this.isNew ? null : this.selectedClassroom;\n    // this.packageToBuyOrderDeails = {\n    //   isNew: this.isNew,\n    //   selectedClassroom: classroom,\n    //   selectedLevel: this.selectedLevel,\n    //   selectedLanguage: this.selectedLanguage,\n    //   selectedNumberOfStudents: this.selectedNumberOfStudents,\n    //   // selectedPackageToBuy: this.selectedPackageToBuy,\n    //   buyerUserDetails: this.buyerUserDetails\n    // }\n  }\n  previous() {\n    window.scrollTo(0, 0);\n    this.styleAll();\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    } else {\n      this.toastService.setShowToastmessage({\n        severity: 'info',\n        summary: '',\n        detail: 'There is no previous step'\n      });\n    }\n  }\n  goBack() {\n    this.location.back();\n  }\n  toggleSection(sectionElement, sectionArrowImgSrc) {\n    this.generalService.toggleSection(sectionElement, sectionArrowImgSrc, true);\n  }\n  agree() {\n    this.agreed = !this.agreed;\n  }\n  purchase() {\n    if (this.agreed) {\n      let classroom = this.isNew ? 0 : this.selectedClassroom.id;\n      let req = {\n        token: \"\",\n        language: this.selectedLanguage.name,\n        level: this.selectedLevel,\n        studentsNumber: this.selectedNumberOfStudents,\n        hours: this.selectedHours.hours.toString(),\n        packageType: this.selectedPackageToBuy.type,\n        merchant_order_ext_ref: \"\",\n        classroomId: classroom,\n        details: {\n          billingType: this.selectedBillingType,\n          firstname: this.buyerUserDetails.fname,\n          lastname: this.buyerUserDetails.lname,\n          email: this.buyerUserDetails.email,\n          phone: this.selectedPhoneCode.code + ' ' + this.buyerUserDetails.phone,\n          address: this.buyerUserDetails.streetName + \" \" + this.buyerUserDetails.streetNumber,\n          city: this.buyerUserDetails.city,\n          country: this.buyerUserDetails.country,\n          postcode: this.buyerUserDetails.postcode,\n          company: this.buyerUserDetails.company,\n          profession: this.buyerUserDetails.profession,\n          taxOffice: this.buyerUserDetails.tax,\n          vat: this.buyerUserDetails.tin\n        }\n      };\n      this.initializePaymentForm(req);\n    } else {\n      this.termsError = true;\n      this.toastService.setShowToastmessage({\n        severity: 'info',\n        summary: '',\n        detail: 'Please agree to the terms and conditions to make the order'\n      });\n    }\n    //   // this.currentStep = 4;\n    //   this.packageService.requestbuyPackage(req).subscribe((res: any) => {\n    //     this.everypayLink = this.transform(res.result);\n    //     this.generalService.slideNativeElements(true, this.everypay.nativeElement)\n    //   })\n    // } else {\n    //   this.termsError = true;\n    // }\n  }\n  sendRequestTrial() {\n    if (this.selectedNumberOfStudents == \"3+\") {\n      this.selectedNumberOfStudents = \"4\";\n    }\n    let req = {\n      trialLanguage: this.selectedLanguage.name,\n      trialLevel: this.selectedLevel,\n      trialStudentNumber: this.selectedNumberOfStudents\n    };\n    this.packageService.requestTrial(req).pipe(take(1)).subscribe(res => {\n      this.toastService.setShowToastmessage({\n        severity: 'success',\n        summary: '',\n        detail: 'Your trial has been requested.'\n      });\n    });\n  }\n  closePayment() {\n    this.router.navigate(['dashboard']);\n  }\n  initializeData() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const res = yield _this.classroomService.getLMSUserClassrooms(_this.user.id).pipe(take(1)).toPromise(); // Convert to Promise and await its resolution\n        // Proceed after receiving the response\n        _this.userClassrooms = res.filter(item => item && item.language && item.language.trim() !== '');\n        _this.userClassrooms = _this.filterOutTrialClassroomsWithSameTeacher(_this.userClassrooms);\n        // Create an object to track classrooms by teacher ID\n        const classroomsByTeacher = {};\n        // Update this.userClassrooms and classroomFromQueryParams\n        _this.userClassrooms = _this.userClassrooms.filter(response => {\n          const teacherId = response.teacher?.id ?? null;\n          if (teacherId !== null) {\n            // Check if the response is of type \"Paid\" or if there is no existing response for this teacher ID\n            if (response.type === \"Paid\" || !(teacherId in classroomsByTeacher)) {\n              classroomsByTeacher[teacherId] = response; // Store the response\n            }\n            if (_this.classroomFromQueryParams && _this.classroomFromQueryParams.type === \"Trial\" && _this.classroomFromQueryParams.teacher && _this.classroomFromQueryParams.teacher.id === teacherId) {\n              // Replace classroomFromQueryParams with the paid response\n              _this.classroomFromQueryParams = response;\n            }\n            return response; // else Include all classrooms\n          }\n          return true; // Include classrooms with no teacher ID\n        });\n        if (_this.userClassrooms.length == 0) {\n          _this.isNew = true;\n          _this.selectedLanguage = _this.mltLanguages[0];\n          _this.selectedLevel = \"A1\";\n          _this.selectedNumberOfStudents = \"1\";\n          _this.selectedDefaultLevel = \"A1\";\n        } else {\n          if (_this.classroomFromQueryParams.id) {\n            _this.selectedClassroom = _this.classroomFromQueryParams;\n          } else {\n            _this.selectedClassroom = _this.userClassrooms[0];\n          }\n          for (let language of _this.mltLanguages) {\n            if (_this.selectedClassroom.language !== language.name) {\n              _this.disableLanguage(`language-btn-${language.code}`);\n            }\n          }\n          _this.selectedClassroom.classroomStudents = _this.classroomService.getuniqueClassroomStudents(_this.selectedClassroom.classroomStudents);\n          _this.selectedNumberOfStudents = _this.selectedClassroom.classroomStudents.length.toString();\n          _this.selectedLevel = _this.selectedClassroom.activeLevel;\n          if (_this.selectedLevel == \"TBD\") {\n            _this.selectedLevel = \"A1\";\n          }\n          _this.selectedDefaultLevel = _this.selectedClassroom.activeLevel;\n          _this.selectedLanguage = _this.mltLanguages.filter(el => el.name == _this.selectedClassroom.language)[0];\n          _this.toggleSplitPaymentDisplay();\n        }\n        _this.initializeForm();\n        _this.updateFormValidationRules();\n        if (_this.selectedNumberOfStudents <= \"3\") {\n          _this.reqBody = {\n            \"language\": _this.selectedLanguage.name,\n            \"level\": _this.selectedLevel,\n            \"numberOfStudents\": _this.selectedNumberOfStudents\n          };\n        } else {\n          _this.reqBody = {\n            \"language\": _this.selectedLanguage.name,\n            \"level\": _this.selectedLevel,\n            \"numberOfStudents\": \"2\"\n          };\n        }\n        _this.selectedPackageToBuy = _this.defaultPackages[1];\n        _this.mltPricesHourly = _this.defaultPackages[0].expiresIn;\n        _this.getNewPackagePrices();\n        _this.getNewSplitPackagePrices();\n        _this.selectedHours = _this.mltPricesHourly[2];\n        _this.expiresIn = _this.mltPricesHourly[2].expirationMonths;\n        _this.styleAll();\n      } catch (error) {\n        console.error('An error occurred:', error);\n      }\n    })();\n  }\n  updatePrices() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      return new Promise((resolve, reject) => {\n        try {\n          _this2.mltPricesHourly.forEach((item, index) => {\n            item.price = _this2.newPackages[index].price;\n            item.perHour = _this2.newPackages[index].perHour;\n          });\n          resolve();\n        } catch (error) {\n          reject(error);\n        }\n      });\n    })();\n  }\n  getNewPackagePrices() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const res = yield _this3.packageService.getUpdatedPackagesToBuy(_this3.reqBody).pipe(take(1)).toPromise();\n        _this3.newPackages = res;\n        yield _this3.updatePrices();\n        _this3.selectedPrice = _this3.selectedHours.price;\n      } catch (error) {\n        console.log('An error occurred:', error);\n      }\n    })();\n  }\n  getNewSplitPackagePrices() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const res = yield _this4.packageService.getSplitPackagesToBuy(_this4.reqBody).pipe(take(1)).toPromise();\n        _this4.splitPaymentPackages = res;\n      } catch (error) {\n        console.log('An error occurred:', error);\n      }\n    })();\n  }\n  onPhoneCodeChange(event) {\n    setTimeout(() => {\n      this.selectedPhoneCode = event.value;\n    }, 100);\n  }\n  initializePaymentForm(data) {\n    console.log(data);\n    this.subs.add((this.hasSplitPayment ? this.packageService.createSplitRevolutOrder(data) : this.packageService.createRevolutOrder(data)).pipe(switchMap(order => {\n      console.log('order', order);\n      var that = this;\n      RevolutCheckout(order.public_id, isProductionMode ? 'prod' : 'sandbox').then(function (instance) {\n        instance.payWithPopup({\n          onSuccess() {\n            data.token = order.public_id;\n            data.merchant_order_ext_ref = order.merchant_order_ext_ref;\n            // let event = new CustomEvent('dosomething', {detail: 'hello'});\n            that.completeOrder(data, order);\n          },\n          onError(message) {\n            console.log(message);\n          }\n        });\n      });\n      return of(data);\n    }), take(1)).subscribe(res => {}));\n  }\n  completeOrder(paymentResponse, order) {\n    this.subs.add((this.hasSplitPayment ? this.packageService.completeSplitOrder(paymentResponse) : this.packageService.completeOrder(paymentResponse)).subscribe(res => {\n      this.currentStep = 4;\n      if (this.leadDynoScriptLoaded) {\n        const purchaseData = {\n          key: this.leadDynoPrivateKey,\n          // Replace with your Leaddyno API key\n          email: order.email,\n          purchase_amount: order.order_amount.value / 100,\n          // Convert to the correct currency\n          purchase_code: order.merchant_order_ext_ref,\n          affiliate: localStorage.getItem('leadDynoAffiliateId') || '',\n          line_items: [{\n            sku: order.id,\n            quantity: 1,\n            description: paymentResponse.language + ' - ' + paymentResponse.level + ' level - ' + paymentResponse.hours + ' hours',\n            amount: order.order_amount.value / 100\n          }]\n        };\n        // affiliate exists\n        if (this.leadDynoAffiliateId) {\n          this.packageService.getLeadDynoAffiliateByAffiliateCode(this.leadDynoAffiliateId, this.leadDynoPrivateKey).subscribe({\n            next: res => {\n              console.log(res);\n              const randId = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n              const purchaseData = {\n                key: this.leadDynoPrivateKey,\n                email: this.user.email,\n                purchase_amount: order.order_amount.value / 100,\n                // Convert to the correct currency\n                purchase_code: order.merchant_order_ext_ref,\n                code: res.affiliate_code || this.leadDynoAffiliateId,\n                affiliate: res.email,\n                line_items: [{\n                  sku: order.merchant_order_ext_ref,\n                  quantity: 1,\n                  description: order.merchant_order_ext_ref,\n                  amount: order.order_amount.value / 100\n                }]\n              };\n              this.packageService.addPurchaseEvent(purchaseData).subscribe(res => {\n                console.log(res);\n              });\n            },\n            error: err => {\n              console.log(err);\n            }\n          });\n        } else {\n          // without affiliate\n          this.packageService.addPurchaseEvent(purchaseData).subscribe(res => {\n            console.log(res);\n          });\n        }\n      }\n    }));\n  }\n  getTotalSplitPaymentAmount() {\n    if (this.splitPaymentPackages.length === 0) {\n      return 0;\n    }\n    const selectedPackage = this.splitPaymentPackages[this.selectedHoursIndex];\n    const additionalCost = this.selectedPackageToBuy.costPlus * 0.5;\n    return (selectedPackage?.companyFee || 0) + additionalCost;\n  }\n  getRemainingAmount() {\n    const totalPrice = this.selectedPrice + this.selectedPackageToBuy.costPlus;\n    return totalPrice - this.getTotalSplitPaymentAmount();\n  }\n  onSplitPaymentPayNowTooltipSelected() {\n    this.confirmDialogService.setProperties({\n      confirmMessage: `The booking fee is an upfront payment, ensuring the immediate availability of your preferred teacher.`,\n      acceptBtnLabel: 'OK',\n      action: 'cancel-lesson',\n      confirmIcon: '',\n      acceptBtnImage: '',\n      acceptBtnIcon: 'pi pi-check',\n      showHeader: true,\n      dialogType: 'custom',\n      showConfirmIcon: false,\n      headerClass: 'my-custom-header-class',\n      headerText: 'Booking Fee'\n    });\n    this.confirmDialogService.show();\n  }\n  onSplitPaymentPayLaterTooltipSelected() {\n    this.confirmDialogService.setProperties({\n      confirmMessage: `\n      Once your booking fee payment is completed, a customer service representative will contact you shortly to provide further assistance. \n      They will guide you on how to complete the payment process.`,\n      acceptBtnLabel: 'OK',\n      action: 'cancel-lesson',\n      confirmIcon: '',\n      acceptBtnImage: '',\n      acceptBtnIcon: 'pi pi-check',\n      showHeader: true,\n      dialogType: 'custom',\n      showConfirmIcon: false,\n      headerClass: 'my-custom-header-class',\n      headerText: 'What is a Remaining Balance?'\n    });\n    this.confirmDialogService.show();\n  }\n  toggleSplitPaymentDisplay() {\n    this.showSplitPayment = this.selectedClassroom.teacher.accSlitPayments;\n    console.log(this.selectedPackageToBuy);\n  }\n  /**\n  Filters out trial classrooms with the same teacher.\n  @param {Classroom[]} userClassrooms - An array of classrooms for the user.\n  @returns {Classroom[]} - An array of classrooms with the same teacher as the user.\n  */\n  filterOutTrialClassroomsWithSameTeacher(userClassrooms) {\n    return userClassrooms.filter(classroom => {\n      // Check if classroom is a trial\n      if (classroom.type === 'Trial') {\n        // Find classrooms with the same teacher asp user id\n        const sameTeacherClassrooms = userClassrooms.filter(c => c.teacher.aspUserId === classroom.teacher.aspUserId);\n        // Filter out the trial classroom if there are other classrooms with the same teacher asp user id\n        if (sameTeacherClassrooms.length > 1) {\n          return false; // Exclude the trial classroom\n        }\n      }\n      return true; // Include other classrooms\n    });\n  }\n};\n__decorate([Input()], BuyPackageComponent.prototype, \"inTrial\", void 0);\n__decorate([Input()], BuyPackageComponent.prototype, \"hideSection\", void 0);\nBuyPackageComponent = __decorate([Component({\n  selector: 'app-buy-package',\n  templateUrl: './buy-package.component.html',\n  styleUrls: ['./buy-package.component.scss'],\n  animations: [slideInOut2, slideInOut],\n  standalone: true,\n  imports: [CommonModule, FormsModule, ReactiveFormsModule, CheckboxModule, PrimeInputDropdownComponent, BuyPackageSuggestionBoxComponent]\n}), __param(11, Inject(DOCUMENT))], BuyPackageComponent);\nexport { BuyPackageComponent };", "map": {"version": 3, "names": ["CommonModule", "DOCUMENT", "Component", "Inject", "Input", "FormsModule", "ReactiveFormsModule", "UntypedFormControl", "UntypedFormGroup", "Validators", "switchMap", "take", "PackageType", "PackagesModel", "slideInOut2", "slideInOut", "SubSink", "RevolutCheckout", "of", "environment", "BuyPackageSuggestionBoxComponent", "CheckboxModule", "PrimeInputDropdownComponent", "isProductionMode", "production", "BuyPackageComponent", "constructor", "authService", "activatedRoute", "location", "classroomService", "generalService", "packageService", "toastService", "confirmDialogService", "sanitizer", "router", "renderer", "document", "inTrial", "hideSection", "user", "subs", "guid", "packageToBuyOrderDeails", "phoneCodes", "getPhoneCodes", "currentStep", "userClassrooms", "classroomId", "isNew", "numberOfStudents", "payload", "pk", "amount", "locale", "txnType", "selectedClassroom", "selectedP<PERSON>ageToBuy", "selectedLevel", "selectedDefaultLevel", "selectedNumberOfStudents", "selectedLanguage", "selectedHoursIndex", "selectedBillingType", "selectedPhoneCode", "selectedResidenceCountry", "buyerUserDetails", "mltLanguages", "mltLevels", "countries", "mltPricesHourly", "selectedHours", "mltPackages", "defaultPackages", "getDefaultPackages", "newPackages", "reqBody", "form", "classroomFromQueryParams", "showSplitPayment", "splitPaymentPackages", "leadDynoAffiliateId", "localStorage", "getItem", "leadDynoPrivateKey", "leadDynoScriptLoaded", "expiresIn", "isReceipt", "isInvoice", "isSubmitted", "agreed", "termsError", "ngOnInit", "classroomString", "snapshot", "queryParams", "JSON", "parse", "responsiveOptions", "breakpoint", "numVisible", "numScroll", "getLoggedInUser", "initializeData", "loadToltScript", "loadScript", "then", "console", "log", "addLeadDynoEvents", "leadDynoGetAffiliateEvent", "key", "subscribe", "next", "res", "error", "err", "catch", "LeadDyno", "recordVisit", "autoWatch", "url", "Promise", "resolve", "reject", "script", "createElement", "src", "onload", "onerror", "append<PERSON><PERSON><PERSON>", "body", "loadJsScript", "type", "removeLeadDynoScript", "scriptElement", "querySelector", "remove", "dataTolt", "setAttribute", "head", "loadLeadDynoStatic", "node", "async", "charset", "getElementsByTagName", "ngAfterViewInit", "rightSide", "getElementById", "ngOnDestroy", "unsubscribe", "hasSplitPayment", "errorControl", "controls", "trialSection", "setTimeout", "for<PERSON>ach", "call", "getElementsByClassName", "el", "classList", "add", "initializeForm", "find", "obj", "name", "residence", "filter", "code", "extractCountryPrefix", "phone", "getDefaultPhoneCode", "fname", "firstName", "validators", "required", "lname", "lastName", "company", "profession", "country", "street", "number", "city", "postcode", "tax", "email", "extractPhoneNumber", "tin", "updateFormValidationRules", "setValidators", "updateValueAndValidity", "styleAll", "styleClassrooms", "selectedBtnStyle", "styleLanguages", "styleHours", "styleNumberOfStudents", "deStyleAll", "id", "price", "extraId", "color", "getGradientColor", "btn", "style", "backgroundImage", "includes", "opacity", "extraElement", "deSelectedBtnStyle", "getCasualColor", "backgroundColor", "background", "getBtnStyle", "notToBeDisabled", "disableLanguage", "pointerEvents", "enableLanguage", "getLevelIsGreaterThanSelected", "level", "activeLevel", "compareLevels", "level1", "level2", "levelsOrder", "indexOf", "REGULAR", "FLEX", "PREMIUM", "selectClassroom", "classroom", "language", "classroomStudents", "getuniqueClassroomStudents", "length", "toString", "toLowerCase", "toggleSplitPaymentDisplay", "getNewPackagePrices", "getNewSplitPackagePrices", "selectLanguage", "selectLevel", "selectNumber", "selectHours", "hour", "i", "selectedPrice", "expirationMonths", "selectPackage", "packageType", "buyNew", "chooseReceipt", "chooseInvoice", "window", "scrollTo", "valid", "formValue", "value", "streetName", "streetNumber", "setShowToastmessage", "severity", "summary", "detail", "costPlus", "previous", "goBack", "back", "toggleSection", "sectionElement", "sectionArrowImgSrc", "agree", "purchase", "req", "token", "studentsNumber", "hours", "merchant_order_ext_ref", "details", "billingType", "firstname", "lastname", "address", "taxOffice", "vat", "initializePaymentForm", "sendRequestTrial", "trialLanguage", "trialLevel", "trialStudentNumber", "requestTrial", "pipe", "closePayment", "navigate", "_this", "_asyncToGenerator", "getLMSUserClassrooms", "to<PERSON>romise", "item", "trim", "filterOutTrialClassroomsWithSameTeacher", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "response", "teacherId", "teacher", "updatePrices", "_this2", "index", "perHour", "_this3", "getUpdatedPackagesToBuy", "_this4", "getSplitPackagesToBuy", "onPhoneCodeChange", "event", "data", "createSplitRevolutOrder", "createRevolutOrder", "order", "that", "public_id", "instance", "payWithPopup", "onSuccess", "completeOrder", "onError", "message", "paymentResponse", "completeSplitOrder", "purchaseData", "purchase_amount", "order_amount", "purchase_code", "affiliate", "line_items", "sku", "quantity", "description", "getLeadDynoAffiliateByAffiliateCode", "randId", "Math", "random", "substring", "affiliate_code", "addPurchaseEvent", "getTotalSplitPaymentAmount", "selected<PERSON><PERSON><PERSON>", "additionalCost", "companyFee", "getRemainingAmount", "totalPrice", "onSplitPaymentPayNowTooltipSelected", "setProperties", "confirmMessage", "acceptBtnLabel", "action", "confirmIcon", "acceptBtnImage", "acceptBtnIcon", "showHeader", "dialogType", "showConfirmIcon", "headerClass", "headerText", "show", "onSplitPaymentPayLaterTooltipSelected", "accSlitPayments", "sameTeacherClassrooms", "c", "aspUserId", "__decorate", "selector", "templateUrl", "styleUrls", "animations", "standalone", "imports", "__param"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\buy-package\\buy-package.component.ts"], "sourcesContent": ["import { CommonModule, DOCUMENT, Location } from '@angular/common';\r\nimport { Component, HostListener, Inject, Input, OnInit, Renderer2, ViewChild } from '@angular/core';\r\nimport { FormsModule, ReactiveFormsModule, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\r\nimport { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { switchMap, take } from 'rxjs/operators';\r\nimport { Classroom } from 'src/app/core/models/classroom.model';\r\nimport { Country, Language, LeadDynoAffiliate } from 'src/app/core/models/general.model';\r\nimport { BuyerUserDetails, PackagePriceHourly, PackageToBuy, PackageToBuyNew, PackageToBuyOrderDeails, PackageType, PackagesModel, SplitPackagesToBuyResponse, mltPackages } from 'src/app/core/models/package.model';\r\nimport { User } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { PackageService } from 'src/app/core/services/package.service';\r\nimport { slideInOut2, slideInOut } from 'src/app/helpers/my-animations';\r\nimport { SubSink } from 'subsink';\r\nimport RevolutCheckout from \"@revolut/checkout\"\r\nimport { MODE } from '@revolut/checkout/types/constants';\r\nimport { of } from 'rxjs';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { ConfirmDialogService } from 'src/app/core/services/confirm-dialog.service';\r\nimport { environment } from 'src/environments/environment';\r\nimport { HttpHeaders } from '@angular/common/http';\r\nimport { BuyPackageSuggestionBoxComponent } from './buy-package-suggestion-box/buy-package-suggestion-box.component';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { PrimeInputDropdownComponent } from 'src/app/shared/prime/input/prime-input-dropdown/prime-input-dropdown.component';\r\ninterface PaidResponses {\r\n  [key: string]: any;\r\n}\r\nconst isProductionMode = environment.production;\r\ndeclare var LeadDyno: any;\r\n\r\n@Component({\r\n  selector: 'app-buy-package',\r\n  templateUrl: './buy-package.component.html',\r\n  styleUrls: ['./buy-package.component.scss'],\r\n  animations: [slideInOut2, slideInOut],\r\n  standalone: true,\r\n  imports: [ \r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    CheckboxModule,\r\n    PrimeInputDropdownComponent,\r\n    BuyPackageSuggestionBoxComponent],\r\n})\r\nexport class BuyPackageComponent implements OnInit {\r\n  @Input() inTrial: boolean = false;\r\n  @Input() hideSection: boolean = false;\r\n  user = {} as User;\r\n  private subs = new SubSink();\r\n  guid: string = \"guid-guid\"\r\n  public packageToBuyOrderDeails: PackageToBuyOrderDeails = {} as PackageToBuyOrderDeails\r\n  public phoneCodes: any[] = this.generalService.getPhoneCodes();\r\n  public currentStep: number = 1;\r\n\r\n  public userClassrooms: Classroom[] = []\r\n  public classroomId: string = \"\"\r\n\r\n  public isNew: boolean = false;\r\n\r\n  public numberOfStudents = [\"1\", \"2\", \"3\"]\r\n\r\n  payload = {\r\n    pk: 'your-public-key',\r\n    amount: 1000,\r\n    locale: 'en',\r\n    txnType: 'tds',\r\n  };\r\n\r\n  public selectedClassroom: Classroom = {} as Classroom;\r\n  public selectedPackageToBuy: mltPackages = {} as mltPackages;\r\n  public selectedPrice: any;\r\n  public selectedLevel: string = \"A1\";\r\n  public selectedDefaultLevel: string = \"A1\";\r\n  public selectedNumberOfStudents: string = \"1\"\r\n  public selectedLanguage: Language = {} as Language;\r\n  public selectedHoursIndex: number = 2;\r\n  public selectedBillingType: string = \"receipt\";\r\n  public selectedPhoneCode: any | undefined = {} as any | undefined;\r\n  public selectedResidenceCountry: Country | undefined = {} as Country | undefined;\r\n  public buyerUserDetails: BuyerUserDetails = {} as BuyerUserDetails\r\n  public mltLanguages = this.generalService.mltLanguages\r\n  public mltLevels = this.generalService.mltLevels\r\n  public countries = this.generalService.countries\r\n  public mltPricesHourly: PackagePriceHourly[] = []\r\n  public selectedHours: PackagePriceHourly = {} as PackagePriceHourly;\r\n  public mltPackages = this.generalService.mltPackages\r\n  public defaultPackages = PackagesModel.getDefaultPackages();\r\n  public newPackages: PackageToBuyNew[] = [];\r\n  public reqBody: any = {}\r\n  public form: UntypedFormGroup = new UntypedFormGroup({})\r\n  public classroomFromQueryParams: Classroom = {} as Classroom;\r\n  public showSplitPayment = false;\r\n  public splitPaymentPackages: SplitPackagesToBuyResponse[] = [];\r\n  public leadDynoAffiliateId = localStorage.getItem('leadDynoAffiliateId');\r\n  rightSide: any;\r\n  e?: SafeResourceUrl\r\n  responsiveOptions!: any[];\r\n  leadDynoPrivateKey = environment.leadDynoPrivateKey;\r\n  leadDynoScriptLoaded = false;\r\n  constructor(\r\n    private authService: AuthService,\r\n    private activatedRoute: ActivatedRoute,\r\n    private location: Location,\r\n    private classroomService: ClassroomService,\r\n    private generalService: GeneralService,\r\n    private packageService: PackageService,\r\n    private toastService: ToastService,\r\n    public confirmDialogService: ConfirmDialogService,\r\n    private sanitizer: DomSanitizer,\r\n    private router: Router,\r\n    private renderer: Renderer2,\r\n    @Inject(DOCUMENT) private document: Document,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    if (!this.inTrial) {\r\n      const classroomString = this.activatedRoute.snapshot.queryParams['classroom'];\r\n      if (classroomString) {\r\n        this.classroomFromQueryParams = JSON.parse(classroomString);\r\n      }\r\n    }\r\n\r\n    this.responsiveOptions = [\r\n      {\r\n        breakpoint: '1199px',\r\n        numVisible: 1,\r\n        numScroll: 1\r\n      },\r\n      {\r\n        breakpoint: '991px',\r\n        numVisible: 2,\r\n        numScroll: 1\r\n      },\r\n      {\r\n        breakpoint: '767px',\r\n        numVisible: 1,\r\n        numScroll: 1\r\n      }\r\n    ];\r\n\r\n    this.user = this.authService.getLoggedInUser();\r\n    this.initializeData();\r\n    this.loadToltScript('https://cdn.tolt.io/tolt.js', '344bed5a-704b-49eb-9ec5-f0215c879772');\r\n\r\n    // TODO: Add LeadDyno script\r\n    this.loadScript('https://static.leaddyno.com/js').then(() => {\r\n      // LeadDyno script loaded successfully\r\n      // Now, you can add your events\r\n      console.log('LeadDyno script loaded, executing LeadDyno dependent code...');\r\n\r\n      this.leadDynoScriptLoaded = true;\r\n      this.addLeadDynoEvents();\r\n\r\n\r\n      if (this.leadDynoScriptLoaded) {\r\n\r\n\r\n        this.packageService.leadDynoGetAffiliateEvent({\r\n          key: this.leadDynoPrivateKey,\r\n        }).subscribe({\r\n          next: (res: any) => {\r\n            console.log(res);\r\n          },\r\n          error: (err: any) => {\r\n            console.log(err);\r\n          }\r\n        });\r\n\r\n\r\n        // this.packageService.addPurchaseEvent(purchaseData).subscribe((res: any) => {\r\n        //   console.log(res);\r\n        // });\r\n      }\r\n\r\n    }).catch(error => console.log('Error loading LeadDyno script:', error));\r\n\r\n  }\r\n\r\n  addLeadDynoEvents(): void {\r\n    // Assuming LeadDyno is available globally\r\n    // Add your LeadDyno events here\r\n    if (typeof LeadDyno !== 'undefined') {\r\n      LeadDyno.key = \"6f488593e746d71bf3743119f96e36b92acfd049\";\r\n      LeadDyno.recordVisit();\r\n      LeadDyno.autoWatch();\r\n    } else {\r\n      console.error('LeadDyno is not available');\r\n    }\r\n\r\n  }\r\n\r\n  loadScript(url: string): Promise<void> {\r\n    return new Promise<void>((resolve, reject) => {\r\n      const script = this.renderer.createElement('script');\r\n      script.src = url;\r\n      script.onload = () => {\r\n        resolve();\r\n      };\r\n      script.onerror = (error: any) => {\r\n        reject(error);\r\n      };\r\n      this.renderer.appendChild(document.body, script);\r\n    });\r\n  }\r\n\r\n  loadJsScript(renderer: Renderer2, src: string): HTMLScriptElement {\r\n    const script = renderer.createElement('script');\r\n    script.type = 'text/javascript';\r\n    script.src = src;\r\n    renderer.appendChild(this.document.body, script);\r\n    return script;\r\n  }\r\n\r\n  removeLeadDynoScript(): void {\r\n    const scriptElement = document.querySelector('script[src=\"https://static.leaddyno.com/js\"]');\r\n    if (scriptElement) {\r\n      scriptElement.remove();\r\n      console.log('LeadDyno script removed');\r\n    }\r\n  }\r\n\r\n  loadToltScript(url: string, dataTolt: string): Promise<void> {\r\n    return new Promise<void>((resolve, reject) => {\r\n      const script = this.renderer.createElement('script');\r\n      script.src = url;\r\n      script.setAttribute('id', 'tolt-player');\r\n      script.setAttribute('data-tolt', dataTolt);\r\n      script.onload = () => {\r\n        resolve();\r\n      };\r\n      script.onerror = (error: any) => {\r\n        reject(error);\r\n      };\r\n      this.renderer.appendChild(document.head, script);\r\n    });\r\n  }\r\n\r\n  public loadLeadDynoStatic() {\r\n    console.log('preparing to load LeadDyno...Static');\r\n    let node = document.createElement('script');\r\n    node.setAttribute(\"id\", \"leaddyno-static\");\r\n    node.src = \"https://static.leaddyno.com/js\";\r\n    node.type = 'text/javascript';\r\n    node.async = true;\r\n    node.charset = 'utf-8';\r\n    document.getElementsByTagName('head')[0].appendChild(node);\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    this.rightSide = document.getElementById('package-overview');\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n    this.removeLeadDynoScript();\r\n    const scriptElement = document.getElementById('tolt-player');\r\n    if (scriptElement) {\r\n      scriptElement.remove();\r\n    }\r\n  }\r\n\r\n  get hasSplitPayment(): boolean {\r\n    return this.showSplitPayment;\r\n  }\r\n\r\n  get errorControl() {\r\n    return this.form.controls\r\n  }\r\n\r\n  trialSection() {\r\n    this.hideSection = !this.hideSection;\r\n    setTimeout(() => {\r\n      [].forEach.call(document.getElementsByClassName('btn'), (el: any) => {\r\n        el.classList.add('hvr-glow')\r\n      });\r\n    }, 1000);\r\n  }\r\n\r\n  initializeForm() {\r\n    this.selectedResidenceCountry = this.countries.find(obj => obj.name === this.user.residence);\r\n    this.selectedPhoneCode = this.phoneCodes.filter(el => el.code === this.generalService.extractCountryPrefix(this.user.phone))[0] || this.generalService.getDefaultPhoneCode();\r\n    console.log(this.selectedResidenceCountry);\r\n    this.form = new UntypedFormGroup({\r\n      fname: new UntypedFormControl(this.user.firstName, {\r\n        validators: [Validators.required]\r\n      }),\r\n      lname: new UntypedFormControl(this.user.lastName, {\r\n        validators: [Validators.required]\r\n      }),\r\n      company: new UntypedFormControl(\"\", {}),\r\n      profession: new UntypedFormControl(\"\", {}),\r\n      country: new UntypedFormControl(this.selectedResidenceCountry, {\r\n        validators: [Validators.required]\r\n      }),\r\n      street: new UntypedFormControl(\"\", {\r\n        validators: [Validators.required]\r\n      }),\r\n      number: new UntypedFormControl(\"\", {\r\n        validators: [Validators.required]\r\n      }),\r\n      city: new UntypedFormControl(\"\", {\r\n        validators: [Validators.required]\r\n      }),\r\n      postcode: new UntypedFormControl(\"\", {\r\n        validators: [Validators.required]\r\n      }),\r\n      tax: new UntypedFormControl(\"\", {\r\n      }),\r\n      email: new UntypedFormControl(this.user.email, {\r\n        validators: [Validators.required]\r\n      }),\r\n      phone: new UntypedFormControl(this.generalService.extractPhoneNumber(this.user.phone), {\r\n        validators: [Validators.required]\r\n      }),\r\n      tin: new UntypedFormControl(\"\", {\r\n      }),\r\n    })\r\n\r\n  }\r\n\r\n  updateFormValidationRules() {\r\n    if (this.isInvoice) {\r\n      this.form.controls['tin'].setValidators([Validators.required]);\r\n    } else {\r\n      this.form.controls['tin'].setValidators([]);\r\n    }\r\n\r\n    this.form.controls['tin'].updateValueAndValidity();\r\n  }\r\n\r\n  styleAll() {\r\n    setTimeout(() => {\r\n      if (!this.isNew) {\r\n        this.styleClassrooms();\r\n      } else {\r\n        this.selectedBtnStyle(`package-new`)\r\n      }\r\n      this.styleLanguages()\r\n      this.styleHours();\r\n      this.styleNumberOfStudents();\r\n    }, 1000);\r\n  }\r\n\r\n  deStyleAll() {\r\n    this.styleClassrooms();\r\n    this.styleLanguages()\r\n    this.styleHours();\r\n    this.styleNumberOfStudents();\r\n  }\r\n\r\n  styleClassrooms() {\r\n    this.selectedBtnStyle(`package-btn-${this.selectedClassroom.id}`, `package-btn-teacher-${this.selectedClassroom?.id}`)\r\n  }\r\n\r\n  styleLanguages() {\r\n    this.selectedBtnStyle(`language-btn-${this.selectedLanguage.code}`)\r\n  }\r\n\r\n  styleNumberOfStudents() {\r\n    if (this.selectedNumberOfStudents > \"3\") {\r\n      this.selectedNumberOfStudents = \"3+\"\r\n    }\r\n    this.selectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`)\r\n  }\r\n\r\n  styleHours() {\r\n    this.selectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-hours-${this.mltPricesHourly[this.selectedHoursIndex].price}`)\r\n    this.selectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-per-${this.mltPricesHourly[this.selectedHoursIndex].price}`)\r\n  }\r\n\r\n  selectedBtnStyle(id: string, extraId?: string) {\r\n    let color = this.inTrial ? '#4895ef' : this.getGradientColor(this.selectedPackageToBuy.type)\r\n    let btn = document.getElementById(id)\r\n\r\n    if (btn && this.selectedPackageToBuy) {\r\n      btn!.style.backgroundImage = color;\r\n    }\r\n    if (btn && !id.includes('package-new')) {\r\n      btn!.style.color = \"white\";\r\n      btn!.style.opacity = \"1\";\r\n    }\r\n\r\n    if (extraId) {\r\n      let extraElement = document.getElementById(extraId);\r\n      if (extraElement) {\r\n        extraElement.style.color = \"white\";\r\n      }\r\n    }\r\n  }\r\n\r\n  deSelectedBtnStyle(id: string, extraId?: string) {\r\n    let color = this.inTrial ? '#4895ef' : this.getCasualColor();\r\n    let btn = document.getElementById(id);\r\n\r\n    if (btn) {\r\n      if (!this.isNew && !id.includes('package-btn') && !id.includes('number-btn') && !id.includes('hour-btn') && !id.includes('package-new') && !id.includes('level')) {\r\n        btn!.style.color = \"#CCD6FF\";\r\n      } else {\r\n        btn!.style.color = color;\r\n      }\r\n      if (!id.includes('package-new')) {\r\n        btn!.style.backgroundColor = \"white\";\r\n        btn!.style.background = \"white\";\r\n      }\r\n    }\r\n\r\n    if (extraId) {\r\n      let extraElement = document.getElementById(extraId);\r\n      if (extraElement) {\r\n        extraElement.style.color = \"#2d2a4b\";\r\n      }\r\n    }\r\n\r\n  }\r\n\r\n  getBtnStyle(notToBeDisabled: boolean) {\r\n    let color = this.inTrial ? '#4895ef' : this.getCasualColor()\r\n    if (this.isNew || notToBeDisabled) {\r\n      return {\r\n        'border': `1px solid ${color}`,\r\n        'color': `${color}`,\r\n      }\r\n    }\r\n    return {\r\n      'border': `1px solid ${color}`,\r\n      'color': `${color}`,\r\n      'opacity': '0.5',\r\n      'cursor': 'initial'\r\n    }\r\n  }\r\n\r\n  disableLanguage(id: string) {\r\n    let btn = document.getElementById(id);\r\n    btn!.style.pointerEvents = 'none';\r\n    btn!.style.opacity = '0.5';\r\n  }\r\n\r\n  enableLanguage(id: string) {\r\n    let btn = document.getElementById(id);\r\n    btn!.style.pointerEvents = 'initial';\r\n    btn!.style.opacity = '1';\r\n  }\r\n\r\n  getLevelIsGreaterThanSelected(level: string) {\r\n    if (this.selectedClassroom.activeLevel == \"TBD\") {\r\n      return true;\r\n    }\r\n    return level >= this.selectedClassroom.activeLevel\r\n  }\r\n\r\n  compareLevels(level1: string, level2: string): number {\r\n    const levelsOrder = ['TBD', 'A1', 'A2', 'B1', 'B2', 'C1', 'C2', 'BS', 'NT21', 'NT22'];\r\n    return levelsOrder.indexOf(level1) - levelsOrder.indexOf(level2);\r\n  }\r\n\r\n  getGradientColor(type: string) {\r\n    if (type == PackageType.REGULAR) {\r\n      return \"linear-gradient(to top, #122171, #353792, #544eb3, #7366d6, #927ffa)\"\r\n    }\r\n    if (type == PackageType.FLEX) {\r\n      return \"linear-gradient(to bottom, #7588f4, #5f6ed1, #4955af, #323d8e, #1b276e)\"\r\n    }\r\n    if (type == PackageType.PREMIUM) {\r\n      return \"linear-gradient(to top, #122171, #004b99, #0074bb, #009ed7, #4dc8f0)\"\r\n    }\r\n    return \"\"\r\n  }\r\n\r\n  getCasualColor() {\r\n    return \"#657AEF\";\r\n  }\r\n\r\n  selectClassroom(classroom: Classroom) {\r\n    this.isNew = true;\r\n    [].forEach.call(document.getElementsByClassName('btn'), (el: any) => {\r\n      if (!el.id.includes('package-btn') && !el.id.includes('hour-btn') && !el.id.includes('package-new')) {\r\n        el.classList.remove('hvr-glow');\r\n      }\r\n      setTimeout(() => {\r\n        // el.classList.add('disabled')\r\n      }, 300);\r\n    });\r\n    this.deSelectedBtnStyle(`package-new`)\r\n    this.deSelectedBtnStyle(`package-btn-${this.selectedClassroom.id}`, `package-btn-teacher-${this.selectedClassroom?.id}`)\r\n    // this.deSelectedBtnStyle(`level-btn-${this.selectedLevel}`)\r\n    this.deSelectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`)\r\n    this.deSelectedBtnStyle(`language-btn-${this.selectedLanguage.code}`)\r\n\r\n    for (let language of this.mltLanguages) {\r\n      if (this.selectedClassroom.language == language.name) {\r\n        this.deSelectedBtnStyle(`language-btn-${language.code}`)\r\n      }\r\n    }\r\n\r\n    this.selectedClassroom = classroom;\r\n    // /Updates the selected classroom's student list by removing any duplicate students.\r\n    this.selectedClassroom.classroomStudents = this.classroomService.getuniqueClassroomStudents(this.selectedClassroom.classroomStudents);\r\n    for (let language of this.mltLanguages) {\r\n      if (this.selectedClassroom.language !== language.name) {\r\n        this.disableLanguage(`language-btn-${language.code}`);\r\n      }\r\n    }\r\n    this.selectedLevel = this.selectedClassroom.activeLevel\r\n    if (this.selectedClassroom.activeLevel == \"TBD\") {\r\n      this.selectedLevel = \"A1\"\r\n    }\r\n    this.selectedDefaultLevel = this.selectedClassroom.activeLevel;\r\n    this.selectedNumberOfStudents = this.selectedClassroom.classroomStudents.length.toString();\r\n    this.selectedLanguage = this.mltLanguages.filter(el => el.name.toLowerCase() == this.selectedClassroom.language!.toLowerCase())[0]\r\n    this.reqBody.language = this.selectedLanguage.name;\r\n    this.reqBody.level = this.selectedLevel;\r\n    this.toggleSplitPaymentDisplay();\r\n    console.log(this.showSplitPayment);\r\n    this.getNewPackagePrices();\r\n    this.getNewSplitPackagePrices();\r\n    this.styleAll();\r\n    this.isNew = false;\r\n  }\r\n\r\n  selectLanguage(language: Language) {\r\n    if (this.isNew) {\r\n      this.deSelectedBtnStyle(`language-btn-${this.selectedLanguage.code}`)\r\n      this.selectedLanguage = language\r\n      this.reqBody.language = this.selectedLanguage.name;\r\n      this.getNewPackagePrices();\r\n      for (let language of this.mltLanguages) {\r\n        this.deSelectedBtnStyle(`language-btn-${language.code}`);\r\n      }\r\n      this.styleLanguages();\r\n    }\r\n  }\r\n\r\n  selectLevel(level: string) {\r\n    if (this.isNew || this.getLevelIsGreaterThanSelected(level)) {\r\n      this.selectedLevel = level\r\n      this.reqBody.level = this.selectedLevel;\r\n      this.getNewPackagePrices();\r\n      this.getNewSplitPackagePrices();\r\n    }\r\n  }\r\n\r\n  selectNumber(number: string) {\r\n    if (this.isNew) {\r\n      this.deSelectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`)\r\n      this.selectedNumberOfStudents = number\r\n      this.selectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`)\r\n      this.reqBody.numberOfStudents = this.selectedNumberOfStudents;\r\n      this.getNewPackagePrices();\r\n      this.getNewSplitPackagePrices();\r\n    }\r\n  }\r\n\r\n  expiresIn: number = 1;\r\n\r\n  selectHours(hour: any, i: number) {\r\n    this.deSelectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-hours-${this.mltPricesHourly[this.selectedHoursIndex].price}`)\r\n    this.deSelectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-per-${this.mltPricesHourly[this.selectedHoursIndex].price}`)\r\n    this.selectedHoursIndex = i;\r\n    this.selectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-hours-${this.mltPricesHourly[this.selectedHoursIndex].price}`)\r\n    this.selectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-per-${this.mltPricesHourly[this.selectedHoursIndex].price}`)\r\n    this.selectedPrice = this.mltPricesHourly[this.selectedHoursIndex].price;\r\n    this.expiresIn = this.mltPricesHourly[this.selectedHoursIndex].expirationMonths;\r\n    this.selectedHours = this.mltPricesHourly[this.selectedHoursIndex];\r\n  }\r\n\r\n  selectPackage(packageType: any) {\r\n    this.selectedPackageToBuy = packageType\r\n    this.rightSide.style.background = this.getGradientColor(this.selectedPackageToBuy.type);\r\n    this.styleAll();\r\n  }\r\n\r\n  buyNew() {\r\n    [].forEach.call(document.getElementsByClassName('btn'), (el: any) => {\r\n      el.classList.add('hvr-glow')\r\n      el.classList.remove('disabled')\r\n    });\r\n    for (let language of this.mltLanguages) {\r\n      if (this.selectedClassroom.language !== language.name) {\r\n        this.enableLanguage(`language-btn-${language.code}`);\r\n      }\r\n    }\r\n    this.deSelectedBtnStyle(`package-btn-${this.selectedClassroom.id}`, `package-btn-teacher-${this.selectedClassroom?.id}`)\r\n    this.selectedBtnStyle(`package-new`)\r\n    this.isNew = true;\r\n    this.selectedLanguage = this.mltLanguages[0];\r\n    this.selectLanguage(this.selectedLanguage);\r\n    this.deSelectedBtnStyle(`level-btn-${this.selectedLevel}`);\r\n    this.selectedLevel = \"A1\";\r\n    this.selectedDefaultLevel = \"A1\";\r\n    this.selectLevel(this.selectedLevel);\r\n    this.deSelectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`);\r\n    this.selectedNumberOfStudents = \"1\";\r\n    this.styleAll();\r\n    this.getNewPackagePrices();\r\n    // this.getNewSplitPackagePrices();\r\n    this.showSplitPayment = false;\r\n  }\r\n\r\n  isReceipt: boolean = true;\r\n  isInvoice: boolean = false;\r\n  isSubmitted: boolean = false;\r\n\r\n  chooseReceipt() {\r\n    this.isReceipt = true;\r\n    this.isInvoice = false;\r\n    this.updateFormValidationRules();\r\n  }\r\n\r\n  chooseInvoice() {\r\n    this.isReceipt = false;\r\n    this.isInvoice = true;\r\n    this.updateFormValidationRules();\r\n  }\r\n\r\n  next() {\r\n    window.scrollTo(0, 0)\r\n    this.currentStep++;\r\n    if (this.currentStep == 2) {\r\n      return;\r\n    }\r\n    if (this.currentStep == 3) {\r\n      this.isSubmitted = true;\r\n\r\n      if (this.form.valid) {\r\n        let formValue = this.form.value\r\n        this.buyerUserDetails.fname = formValue.fname\r\n        this.buyerUserDetails.lname = formValue.lname\r\n        this.buyerUserDetails.email = formValue.email\r\n        this.buyerUserDetails.city = formValue.city\r\n        this.buyerUserDetails.company = formValue.company\r\n        this.buyerUserDetails.streetName = formValue.street\r\n        this.buyerUserDetails.streetNumber = formValue.number\r\n        this.buyerUserDetails.tax = formValue.tax\r\n        this.buyerUserDetails.profession = formValue.profession\r\n        this.buyerUserDetails.postcode = formValue.postcode\r\n        this.buyerUserDetails.tin = formValue.tin\r\n        this.buyerUserDetails.phone = formValue.phone\r\n        this.buyerUserDetails.country = formValue.country.name\r\n        return;\r\n      } else {\r\n\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'info',\r\n          summary: '',\r\n          detail: 'Please fill all the required fields with *'\r\n        });\r\n        this.currentStep = 2;\r\n        return;\r\n      }\r\n    }\r\n    if (this.currentStep == 4) {\r\n      this.payload.amount = (this.selectedPrice + this.selectedPackageToBuy.costPlus) * 100;\r\n      return;\r\n    }\r\n    if (this.currentStep < 5) {\r\n      this.currentStep++;\r\n    }\r\n    // let classroom = this.isNew ? null : this.selectedClassroom;\r\n    // this.packageToBuyOrderDeails = {\r\n    //   isNew: this.isNew,\r\n    //   selectedClassroom: classroom,\r\n    //   selectedLevel: this.selectedLevel,\r\n    //   selectedLanguage: this.selectedLanguage,\r\n    //   selectedNumberOfStudents: this.selectedNumberOfStudents,\r\n    //   // selectedPackageToBuy: this.selectedPackageToBuy,\r\n    //   buyerUserDetails: this.buyerUserDetails\r\n    // }\r\n  }\r\n\r\n  previous() {\r\n    window.scrollTo(0, 0)\r\n    this.styleAll()\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    } else {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'info',\r\n        summary: '',\r\n        detail: 'There is no previous step'\r\n      });\r\n    }\r\n  }\r\n\r\n  goBack() {\r\n    this.location.back();\r\n  }\r\n\r\n  toggleSection(sectionElement: any, sectionArrowImgSrc: any) {\r\n    this.generalService.toggleSection(sectionElement, sectionArrowImgSrc, true)\r\n  }\r\n  agreed: boolean = false;\r\n  agree() {\r\n    this.agreed = !this.agreed;\r\n  }\r\n\r\n  termsError: boolean = false;\r\n  purchase() {\r\n\r\n    if (this.agreed) {\r\n      let classroom = this.isNew ? 0 : this.selectedClassroom.id;\r\n      let req = {\r\n        token: \"\",\r\n        language: this.selectedLanguage.name,\r\n        level: this.selectedLevel,\r\n        studentsNumber: this.selectedNumberOfStudents,\r\n        hours: this.selectedHours.hours.toString(),\r\n        packageType: this.selectedPackageToBuy.type,\r\n        merchant_order_ext_ref: \"\",\r\n        classroomId: classroom,\r\n        details: {\r\n          billingType: this.selectedBillingType,\r\n          firstname: this.buyerUserDetails.fname,\r\n          lastname: this.buyerUserDetails.lname,\r\n          email: this.buyerUserDetails.email,\r\n          phone: this.selectedPhoneCode.code + ' ' + this.buyerUserDetails.phone,\r\n          address: this.buyerUserDetails.streetName + \" \" + this.buyerUserDetails.streetNumber,\r\n          city: this.buyerUserDetails.city,\r\n          country: this.buyerUserDetails.country,\r\n          postcode: this.buyerUserDetails.postcode,\r\n          company: this.buyerUserDetails.company,\r\n          profession: this.buyerUserDetails.profession,\r\n          taxOffice: this.buyerUserDetails.tax,\r\n          vat: this.buyerUserDetails.tin,\r\n        }\r\n      }\r\n      this.initializePaymentForm(req);\r\n    }\r\n    else {\r\n      this.termsError = true;\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'info',\r\n        summary: '',\r\n        detail: 'Please agree to the terms and conditions to make the order'\r\n      });\r\n    }\r\n    //   // this.currentStep = 4;\r\n\r\n    //   this.packageService.requestbuyPackage(req).subscribe((res: any) => {\r\n    //     this.everypayLink = this.transform(res.result);\r\n    //     this.generalService.slideNativeElements(true, this.everypay.nativeElement)\r\n    //   })\r\n    // } else {\r\n    //   this.termsError = true;\r\n\r\n    // }\r\n  }\r\n\r\n  sendRequestTrial() {\r\n    if (this.selectedNumberOfStudents == \"3+\") {\r\n      this.selectedNumberOfStudents = \"4\"\r\n    }\r\n    let req = {\r\n      trialLanguage: this.selectedLanguage.name,\r\n      trialLevel: this.selectedLevel,\r\n      trialStudentNumber: this.selectedNumberOfStudents\r\n    }\r\n    this.packageService.requestTrial(req).pipe(take(1)).subscribe(res => {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'success',\r\n        summary: '',\r\n        detail: 'Your trial has been requested.'\r\n      });\r\n    })\r\n  }\r\n\r\n  closePayment() {\r\n    this.router.navigate(['dashboard'])\r\n  }\r\n\r\n  async initializeData() {\r\n    try {\r\n      const res = await this.classroomService.getLMSUserClassrooms(this.user.id)\r\n        .pipe(take(1))\r\n        .toPromise(); // Convert to Promise and await its resolution\r\n\r\n      // Proceed after receiving the response\r\n      this.userClassrooms = res.filter((item: Classroom) => item && item.language && item.language.trim() !== '');\r\n      this.userClassrooms = this.filterOutTrialClassroomsWithSameTeacher(this.userClassrooms);\r\n      // Create an object to track classrooms by teacher ID\r\n      const classroomsByTeacher: Record<string, Classroom> = {};\r\n\r\n      // Update this.userClassrooms and classroomFromQueryParams\r\n      this.userClassrooms = this.userClassrooms.filter((response) => {\r\n        const teacherId = response.teacher?.id ?? null;\r\n\r\n        if (teacherId !== null) {\r\n          // Check if the response is of type \"Paid\" or if there is no existing response for this teacher ID\r\n          if (response.type === \"Paid\" || !(teacherId in classroomsByTeacher)) {\r\n            classroomsByTeacher[teacherId] = response; // Store the response\r\n          }\r\n\r\n          if (\r\n            this.classroomFromQueryParams &&\r\n            this.classroomFromQueryParams.type === \"Trial\" &&\r\n            this.classroomFromQueryParams.teacher &&\r\n            this.classroomFromQueryParams.teacher.id === teacherId\r\n          ) {\r\n            // Replace classroomFromQueryParams with the paid response\r\n            this.classroomFromQueryParams = response;\r\n          }\r\n\r\n          return response; // else Include all classrooms\r\n        }\r\n\r\n        return true; // Include classrooms with no teacher ID\r\n      });\r\n\r\n\r\n      if (this.userClassrooms.length == 0) {\r\n        this.isNew = true;\r\n        this.selectedLanguage = this.mltLanguages[0];\r\n        this.selectedLevel = \"A1\";\r\n        this.selectedNumberOfStudents = \"1\";\r\n        this.selectedDefaultLevel = \"A1\";\r\n      }\r\n      else {\r\n        if (this.classroomFromQueryParams.id) {\r\n          this.selectedClassroom = this.classroomFromQueryParams;\r\n        } else {\r\n          this.selectedClassroom = this.userClassrooms[0];\r\n        }\r\n        for (let language of this.mltLanguages) {\r\n          if (this.selectedClassroom.language !== language.name) {\r\n            this.disableLanguage(`language-btn-${language.code}`);\r\n          }\r\n        }\r\n        this.selectedClassroom.classroomStudents = this.classroomService.getuniqueClassroomStudents(this.selectedClassroom.classroomStudents);\r\n        this.selectedNumberOfStudents = this.selectedClassroom.classroomStudents.length.toString();\r\n        this.selectedLevel = this.selectedClassroom.activeLevel;\r\n        if (this.selectedLevel == \"TBD\") {\r\n          this.selectedLevel = \"A1\";\r\n        }\r\n        this.selectedDefaultLevel = this.selectedClassroom.activeLevel;\r\n        this.selectedLanguage = this.mltLanguages.filter(el => el.name == this.selectedClassroom.language!)[0];\r\n        this.toggleSplitPaymentDisplay();\r\n      }\r\n      this.initializeForm();\r\n      this.updateFormValidationRules();\r\n      if (this.selectedNumberOfStudents <= \"3\") {\r\n        this.reqBody = {\r\n          \"language\": this.selectedLanguage.name,\r\n          \"level\": this.selectedLevel,\r\n          \"numberOfStudents\": this.selectedNumberOfStudents\r\n        }\r\n      }\r\n      else {\r\n        this.reqBody = {\r\n          \"language\": this.selectedLanguage.name,\r\n          \"level\": this.selectedLevel,\r\n          \"numberOfStudents\": \"2\"\r\n        }\r\n      }\r\n      this.selectedPackageToBuy = this.defaultPackages[1];\r\n      this.mltPricesHourly = this.defaultPackages[0].expiresIn;\r\n      this.getNewPackagePrices();\r\n      this.getNewSplitPackagePrices();\r\n      this.selectedHours = this.mltPricesHourly[2];\r\n      this.expiresIn = this.mltPricesHourly[2].expirationMonths;\r\n      this.styleAll();\r\n    } catch (error) {\r\n      console.error('An error occurred:', error);\r\n    }\r\n  }\r\n\r\n  async updatePrices(): Promise<void> {\r\n    return new Promise<void>((resolve, reject) => {\r\n      try {\r\n        this.mltPricesHourly.forEach((item, index) => {\r\n          item.price = this.newPackages[index].price;\r\n          item.perHour = this.newPackages[index].perHour;\r\n        });\r\n        resolve();\r\n      } catch (error) {\r\n        reject(error);\r\n      }\r\n    });\r\n  }\r\n\r\n  async getNewPackagePrices(): Promise<void> {\r\n    try {\r\n      const res = await this.packageService.getUpdatedPackagesToBuy(this.reqBody).pipe(take(1)).toPromise();\r\n      this.newPackages = res as any[];\r\n      await this.updatePrices();\r\n      this.selectedPrice = this.selectedHours.price;\r\n    } catch (error) {\r\n      console.log('An error occurred:', error);\r\n    }\r\n  }\r\n\r\n  async getNewSplitPackagePrices(): Promise<void> {\r\n    try {\r\n      const res = await this.packageService.getSplitPackagesToBuy(this.reqBody).pipe(take(1)).toPromise();\r\n      this.splitPaymentPackages = res as any[];\r\n    } catch (error) {\r\n      console.log('An error occurred:', error);\r\n    }\r\n  }\r\n\r\n  onPhoneCodeChange(event: any) {\r\n    setTimeout(() => {\r\n      this.selectedPhoneCode = event.value;\r\n    }, 100);\r\n  }\r\n\r\n  initializePaymentForm(data: any) {\r\n    console.log(data);\r\n    this.subs.add((this.hasSplitPayment ? this.packageService.createSplitRevolutOrder(data) : this.packageService.createRevolutOrder(data)).pipe(\r\n      switchMap((order: any) => {\r\n        console.log('order', order);\r\n        var that = this;\r\n        RevolutCheckout(order.public_id, isProductionMode ? 'prod' : 'sandbox').then(function (instance: any) {\r\n          instance.payWithPopup({\r\n            onSuccess() {\r\n              data.token = order.public_id;\r\n              data.merchant_order_ext_ref = order.merchant_order_ext_ref;\r\n              // let event = new CustomEvent('dosomething', {detail: 'hello'});\r\n              that.completeOrder(data, order);\r\n            },\r\n            onError(message: any) {\r\n              console.log(message);\r\n            }\r\n          });\r\n        });\r\n        return of(data);\r\n      }),\r\n      take(1)\r\n    ).subscribe((res: any) => { }))\r\n  }\r\n\r\n  completeOrder(paymentResponse: any, order: any) {\r\n    this.subs.add((this.hasSplitPayment ? this.packageService.completeSplitOrder(paymentResponse) : this.packageService.completeOrder(paymentResponse)).subscribe((res: any) => {\r\n\r\n      this.currentStep = 4;\r\n      if (this.leadDynoScriptLoaded) {\r\n        const purchaseData = {\r\n          key: this.leadDynoPrivateKey, // Replace with your Leaddyno API key\r\n          email: order.email,\r\n          purchase_amount: order.order_amount.value / 100, // Convert to the correct currency\r\n          purchase_code: order.merchant_order_ext_ref,\r\n          affiliate: localStorage.getItem('leadDynoAffiliateId') || '',\r\n          line_items: [\r\n            {\r\n              sku: order.id,\r\n              quantity: 1,\r\n              description: paymentResponse.language + ' - ' + paymentResponse.level + ' level - ' + paymentResponse.hours + ' hours',\r\n              amount: order.order_amount.value / 100\r\n            },\r\n          ],\r\n        };\r\n\r\n        // affiliate exists\r\n        if (this.leadDynoAffiliateId) {\r\n          this.packageService.getLeadDynoAffiliateByAffiliateCode(this.leadDynoAffiliateId,\r\n            this.leadDynoPrivateKey).subscribe({\r\n              next: (res: LeadDynoAffiliate) => {\r\n                console.log(res);\r\n                const randId = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\r\n                const purchaseData =\r\n                {\r\n                  key: this.leadDynoPrivateKey,\r\n                  email: this.user.email,\r\n                  purchase_amount: order.order_amount.value / 100, // Convert to the correct currency\r\n                  purchase_code: order.merchant_order_ext_ref,\r\n                  code: res.affiliate_code || this.leadDynoAffiliateId,\r\n                  affiliate: res.email,\r\n                  line_items: [\r\n                    {\r\n                      sku: order.merchant_order_ext_ref,\r\n                      quantity: 1,\r\n                      description: order.merchant_order_ext_ref,\r\n                      amount: order.order_amount.value / 100,\r\n                    },\r\n                  ],\r\n\r\n                };\r\n                this.packageService.addPurchaseEvent(purchaseData).subscribe((res: any) => {\r\n                  console.log(res);\r\n                });\r\n              },\r\n              error: (err: any) => {\r\n                console.log(err);\r\n              }\r\n            });\r\n        } else {\r\n          // without affiliate\r\n          this.packageService.addPurchaseEvent(purchaseData).subscribe((res: any) => {\r\n            console.log(res);\r\n          });\r\n        }\r\n      }\r\n\r\n    }))\r\n  }\r\n\r\n  getTotalSplitPaymentAmount(): number {\r\n    if (this.splitPaymentPackages.length === 0) {\r\n      return 0;\r\n    }\r\n    const selectedPackage = this.splitPaymentPackages[this.selectedHoursIndex];\r\n    const additionalCost = this.selectedPackageToBuy.costPlus * 0.5;\r\n\r\n    return (selectedPackage?.companyFee || 0) + additionalCost;\r\n  }\r\n\r\n\r\n  getRemainingAmount(): number {\r\n    const totalPrice = this.selectedPrice + this.selectedPackageToBuy.costPlus;\r\n    return totalPrice - this.getTotalSplitPaymentAmount();\r\n  }\r\n\r\n  onSplitPaymentPayNowTooltipSelected() {\r\n    this.confirmDialogService.setProperties({\r\n      confirmMessage: `The booking fee is an upfront payment, ensuring the immediate availability of your preferred teacher.`,\r\n      acceptBtnLabel: 'OK',\r\n      action: 'cancel-lesson',\r\n      confirmIcon: '',\r\n      acceptBtnImage: '',\r\n      acceptBtnIcon: 'pi pi-check',\r\n      showHeader: true,\r\n      dialogType: 'custom',\r\n      showConfirmIcon: false,\r\n      headerClass: 'my-custom-header-class',\r\n      headerText: 'Booking Fee'\r\n    });\r\n    this.confirmDialogService.show();\r\n  }\r\n\r\n  onSplitPaymentPayLaterTooltipSelected() {\r\n    this.confirmDialogService.setProperties({\r\n      confirmMessage: `\r\n      Once your booking fee payment is completed, a customer service representative will contact you shortly to provide further assistance. \r\n      They will guide you on how to complete the payment process.`,\r\n      acceptBtnLabel: 'OK',\r\n      action: 'cancel-lesson',\r\n      confirmIcon: '',\r\n      acceptBtnImage: '',\r\n      acceptBtnIcon: 'pi pi-check',\r\n      showHeader: true,\r\n      dialogType: 'custom',\r\n      showConfirmIcon: false,\r\n      headerClass: 'my-custom-header-class',\r\n      headerText: 'What is a Remaining Balance?'\r\n    });\r\n    this.confirmDialogService.show();\r\n  }\r\n\r\n  private toggleSplitPaymentDisplay() {\r\n    this.showSplitPayment = this.selectedClassroom!.teacher!.accSlitPayments!;\r\n    console.log(this.selectedPackageToBuy);\r\n  }\r\n\r\n  /**\r\n  Filters out trial classrooms with the same teacher.\r\n  @param {Classroom[]} userClassrooms - An array of classrooms for the user.\r\n  @returns {Classroom[]} - An array of classrooms with the same teacher as the user.\r\n  */\r\n  private filterOutTrialClassroomsWithSameTeacher(userClassrooms: Classroom[]): Classroom[] {\r\n    return userClassrooms.filter((classroom: Classroom) => {\r\n      // Check if classroom is a trial\r\n      if (classroom.type === 'Trial') {\r\n        // Find classrooms with the same teacher asp user id\r\n        const sameTeacherClassrooms = userClassrooms.filter((c) => c.teacher!.aspUserId === classroom.teacher!.aspUserId);\r\n\r\n        // Filter out the trial classroom if there are other classrooms with the same teacher asp user id\r\n        if (sameTeacherClassrooms.length > 1) {\r\n          return false; // Exclude the trial classroom\r\n        }\r\n      }\r\n\r\n      return true; // Include other classrooms\r\n    });\r\n  }\r\n}\r\n"], "mappings": ";;AAAA,SAASA,YAAY,EAAEC,QAAQ,QAAkB,iBAAiB;AAClE,SAASC,SAAS,EAAgBC,MAAM,EAAEC,KAAK,QAAsC,eAAe;AACpG,SAASC,WAAW,EAAEC,mBAAmB,EAAEC,kBAAkB,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,gBAAgB;AAGnH,SAASC,SAAS,EAAEC,IAAI,QAAQ,gBAAgB;AAGhD,SAAuGC,WAAW,EAAEC,aAAa,QAAiD,mCAAmC;AAMrN,SAASC,WAAW,EAAEC,UAAU,QAAQ,+BAA+B;AACvE,SAASC,OAAO,QAAQ,SAAS;AACjC,OAAOC,eAAe,MAAM,mBAAmB;AAE/C,SAASC,EAAE,QAAQ,MAAM;AAGzB,SAASC,WAAW,QAAQ,8BAA8B;AAE1D,SAASC,gCAAgC,QAAQ,mEAAmE;AACpH,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,2BAA2B,QAAQ,gFAAgF;AAI5H,MAAMC,gBAAgB,GAAGJ,WAAW,CAACK,UAAU;AAiBxC,IAAMC,mBAAmB,GAAzB,MAAMA,mBAAmB;EAuD9BC,YACUC,WAAwB,EACxBC,cAA8B,EAC9BC,QAAkB,EAClBC,gBAAkC,EAClCC,cAA8B,EAC9BC,cAA8B,EAC9BC,YAA0B,EAC3BC,oBAA0C,EACzCC,SAAuB,EACvBC,MAAc,EACdC,QAAmB,EACTC,QAA0B;IAXpC,KAAAX,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACb,KAAAC,oBAAoB,GAApBA,oBAAoB;IACnB,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IACU,KAAAC,QAAQ,GAARA,QAAQ;IAlE3B,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,WAAW,GAAY,KAAK;IACrC,KAAAC,IAAI,GAAG,EAAU;IACT,KAAAC,IAAI,GAAG,IAAI1B,OAAO,EAAE;IAC5B,KAAA2B,IAAI,GAAW,WAAW;IACnB,KAAAC,uBAAuB,GAA4B,EAA6B;IAChF,KAAAC,UAAU,GAAU,IAAI,CAACd,cAAc,CAACe,aAAa,EAAE;IACvD,KAAAC,WAAW,GAAW,CAAC;IAEvB,KAAAC,cAAc,GAAgB,EAAE;IAChC,KAAAC,WAAW,GAAW,EAAE;IAExB,KAAAC,KAAK,GAAY,KAAK;IAEtB,KAAAC,gBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IAEzC,KAAAC,OAAO,GAAG;MACRC,EAAE,EAAE,iBAAiB;MACrBC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE;KACV;IAEM,KAAAC,iBAAiB,GAAc,EAAe;IAC9C,KAAAC,oBAAoB,GAAgB,EAAiB;IAErD,KAAAC,aAAa,GAAW,IAAI;IAC5B,KAAAC,oBAAoB,GAAW,IAAI;IACnC,KAAAC,wBAAwB,GAAW,GAAG;IACtC,KAAAC,gBAAgB,GAAa,EAAc;IAC3C,KAAAC,kBAAkB,GAAW,CAAC;IAC9B,KAAAC,mBAAmB,GAAW,SAAS;IACvC,KAAAC,iBAAiB,GAAoB,EAAqB;IAC1D,KAAAC,wBAAwB,GAAwB,EAAyB;IACzE,KAAAC,gBAAgB,GAAqB,EAAsB;IAC3D,KAAAC,YAAY,GAAG,IAAI,CAACrC,cAAc,CAACqC,YAAY;IAC/C,KAAAC,SAAS,GAAG,IAAI,CAACtC,cAAc,CAACsC,SAAS;IACzC,KAAAC,SAAS,GAAG,IAAI,CAACvC,cAAc,CAACuC,SAAS;IACzC,KAAAC,eAAe,GAAyB,EAAE;IAC1C,KAAAC,aAAa,GAAuB,EAAwB;IAC5D,KAAAC,WAAW,GAAG,IAAI,CAAC1C,cAAc,CAAC0C,WAAW;IAC7C,KAAAC,eAAe,GAAG7D,aAAa,CAAC8D,kBAAkB,EAAE;IACpD,KAAAC,WAAW,GAAsB,EAAE;IACnC,KAAAC,OAAO,GAAQ,EAAE;IACjB,KAAAC,IAAI,GAAqB,IAAItE,gBAAgB,CAAC,EAAE,CAAC;IACjD,KAAAuE,wBAAwB,GAAc,EAAe;IACrD,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,oBAAoB,GAAiC,EAAE;IACvD,KAAAC,mBAAmB,GAAGC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC;IAIxE,KAAAC,kBAAkB,GAAGlE,WAAW,CAACkE,kBAAkB;IACnD,KAAAC,oBAAoB,GAAG,KAAK;IAsc5B,KAAAC,SAAS,GAAW,CAAC;IA8CrB,KAAAC,SAAS,GAAY,IAAI;IACzB,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,WAAW,GAAY,KAAK;IA0F5B,KAAAC,MAAM,GAAY,KAAK;IAKvB,KAAAC,UAAU,GAAY,KAAK;EAvkBvB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACtD,OAAO,EAAE;MACjB,MAAMuD,eAAe,GAAG,IAAI,CAAClE,cAAc,CAACmE,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC;MAC7E,IAAIF,eAAe,EAAE;QACnB,IAAI,CAACf,wBAAwB,GAAGkB,IAAI,CAACC,KAAK,CAACJ,eAAe,CAAC;MAC7D;IACF;IAEA,IAAI,CAACK,iBAAiB,GAAG,CACvB;MACEC,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAE;KACZ,EACD;MACEF,UAAU,EAAE,OAAO;MACnBC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAE;KACZ,EACD;MACEF,UAAU,EAAE,OAAO;MACnBC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAE;KACZ,CACF;IAED,IAAI,CAAC7D,IAAI,GAAG,IAAI,CAACd,WAAW,CAAC4E,eAAe,EAAE;IAC9C,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAACC,cAAc,CAAC,6BAA6B,EAAE,sCAAsC,CAAC;IAE1F;IACA,IAAI,CAACC,UAAU,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAAC,MAAK;MAC1D;MACA;MACAC,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;MAE3E,IAAI,CAACvB,oBAAoB,GAAG,IAAI;MAChC,IAAI,CAACwB,iBAAiB,EAAE;MAGxB,IAAI,IAAI,CAACxB,oBAAoB,EAAE;QAG7B,IAAI,CAACtD,cAAc,CAAC+E,yBAAyB,CAAC;UAC5CC,GAAG,EAAE,IAAI,CAAC3B;SACX,CAAC,CAAC4B,SAAS,CAAC;UACXC,IAAI,EAAGC,GAAQ,IAAI;YACjBP,OAAO,CAACC,GAAG,CAACM,GAAG,CAAC;UAClB,CAAC;UACDC,KAAK,EAAGC,GAAQ,IAAI;YAClBT,OAAO,CAACC,GAAG,CAACQ,GAAG,CAAC;UAClB;SACD,CAAC;QAGF;QACA;QACA;MACF;IAEF,CAAC,CAAC,CAACC,KAAK,CAACF,KAAK,IAAIR,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEO,KAAK,CAAC,CAAC;EAEzE;EAEAN,iBAAiBA,CAAA;IACf;IACA;IACA,IAAI,OAAOS,QAAQ,KAAK,WAAW,EAAE;MACnCA,QAAQ,CAACP,GAAG,GAAG,0CAA0C;MACzDO,QAAQ,CAACC,WAAW,EAAE;MACtBD,QAAQ,CAACE,SAAS,EAAE;IACtB,CAAC,MAAM;MACLb,OAAO,CAACQ,KAAK,CAAC,2BAA2B,CAAC;IAC5C;EAEF;EAEAV,UAAUA,CAACgB,GAAW;IACpB,OAAO,IAAIC,OAAO,CAAO,CAACC,OAAO,EAAEC,MAAM,KAAI;MAC3C,MAAMC,MAAM,GAAG,IAAI,CAACzF,QAAQ,CAAC0F,aAAa,CAAC,QAAQ,CAAC;MACpDD,MAAM,CAACE,GAAG,GAAGN,GAAG;MAChBI,MAAM,CAACG,MAAM,GAAG,MAAK;QACnBL,OAAO,EAAE;MACX,CAAC;MACDE,MAAM,CAACI,OAAO,GAAId,KAAU,IAAI;QAC9BS,MAAM,CAACT,KAAK,CAAC;MACf,CAAC;MACD,IAAI,CAAC/E,QAAQ,CAAC8F,WAAW,CAAC7F,QAAQ,CAAC8F,IAAI,EAAEN,MAAM,CAAC;IAClD,CAAC,CAAC;EACJ;EAEAO,YAAYA,CAAChG,QAAmB,EAAE2F,GAAW;IAC3C,MAAMF,MAAM,GAAGzF,QAAQ,CAAC0F,aAAa,CAAC,QAAQ,CAAC;IAC/CD,MAAM,CAACQ,IAAI,GAAG,iBAAiB;IAC/BR,MAAM,CAACE,GAAG,GAAGA,GAAG;IAChB3F,QAAQ,CAAC8F,WAAW,CAAC,IAAI,CAAC7F,QAAQ,CAAC8F,IAAI,EAAEN,MAAM,CAAC;IAChD,OAAOA,MAAM;EACf;EAEAS,oBAAoBA,CAAA;IAClB,MAAMC,aAAa,GAAGlG,QAAQ,CAACmG,aAAa,CAAC,8CAA8C,CAAC;IAC5F,IAAID,aAAa,EAAE;MACjBA,aAAa,CAACE,MAAM,EAAE;MACtB9B,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACxC;EACF;EAEAJ,cAAcA,CAACiB,GAAW,EAAEiB,QAAgB;IAC1C,OAAO,IAAIhB,OAAO,CAAO,CAACC,OAAO,EAAEC,MAAM,KAAI;MAC3C,MAAMC,MAAM,GAAG,IAAI,CAACzF,QAAQ,CAAC0F,aAAa,CAAC,QAAQ,CAAC;MACpDD,MAAM,CAACE,GAAG,GAAGN,GAAG;MAChBI,MAAM,CAACc,YAAY,CAAC,IAAI,EAAE,aAAa,CAAC;MACxCd,MAAM,CAACc,YAAY,CAAC,WAAW,EAAED,QAAQ,CAAC;MAC1Cb,MAAM,CAACG,MAAM,GAAG,MAAK;QACnBL,OAAO,EAAE;MACX,CAAC;MACDE,MAAM,CAACI,OAAO,GAAId,KAAU,IAAI;QAC9BS,MAAM,CAACT,KAAK,CAAC;MACf,CAAC;MACD,IAAI,CAAC/E,QAAQ,CAAC8F,WAAW,CAAC7F,QAAQ,CAACuG,IAAI,EAAEf,MAAM,CAAC;IAClD,CAAC,CAAC;EACJ;EAEOgB,kBAAkBA,CAAA;IACvBlC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClD,IAAIkC,IAAI,GAAGzG,QAAQ,CAACyF,aAAa,CAAC,QAAQ,CAAC;IAC3CgB,IAAI,CAACH,YAAY,CAAC,IAAI,EAAE,iBAAiB,CAAC;IAC1CG,IAAI,CAACf,GAAG,GAAG,gCAAgC;IAC3Ce,IAAI,CAACT,IAAI,GAAG,iBAAiB;IAC7BS,IAAI,CAACC,KAAK,GAAG,IAAI;IACjBD,IAAI,CAACE,OAAO,GAAG,OAAO;IACtB3G,QAAQ,CAAC4G,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAACf,WAAW,CAACY,IAAI,CAAC;EAC5D;EAEAI,eAAeA,CAAA;IACb,IAAI,CAACC,SAAS,GAAG9G,QAAQ,CAAC+G,cAAc,CAAC,kBAAkB,CAAC;EAC9D;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC5G,IAAI,CAAC6G,WAAW,EAAE;IACvB,IAAI,CAAChB,oBAAoB,EAAE;IAC3B,MAAMC,aAAa,GAAGlG,QAAQ,CAAC+G,cAAc,CAAC,aAAa,CAAC;IAC5D,IAAIb,aAAa,EAAE;MACjBA,aAAa,CAACE,MAAM,EAAE;IACxB;EACF;EAEA,IAAIc,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACxE,gBAAgB;EAC9B;EAEA,IAAIyE,YAAYA,CAAA;IACd,OAAO,IAAI,CAAC3E,IAAI,CAAC4E,QAAQ;EAC3B;EAEAC,YAAYA,CAAA;IACV,IAAI,CAACnH,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IACpCoH,UAAU,CAAC,MAAK;MACd,EAAE,CAACC,OAAO,CAACC,IAAI,CAACxH,QAAQ,CAACyH,sBAAsB,CAAC,KAAK,CAAC,EAAGC,EAAO,IAAI;QAClEA,EAAE,CAACC,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;EACV;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAACjG,wBAAwB,GAAG,IAAI,CAACI,SAAS,CAAC8F,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,KAAK,IAAI,CAAC7H,IAAI,CAAC8H,SAAS,CAAC;IAC5F,IAAI,CAACtG,iBAAiB,GAAG,IAAI,CAACpB,UAAU,CAAC2H,MAAM,CAACR,EAAE,IAAIA,EAAE,CAACS,IAAI,KAAK,IAAI,CAAC1I,cAAc,CAAC2I,oBAAoB,CAAC,IAAI,CAACjI,IAAI,CAACkI,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC5I,cAAc,CAAC6I,mBAAmB,EAAE;IAC5KhE,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC3C,wBAAwB,CAAC;IAC1C,IAAI,CAACY,IAAI,GAAG,IAAItE,gBAAgB,CAAC;MAC/BqK,KAAK,EAAE,IAAItK,kBAAkB,CAAC,IAAI,CAACkC,IAAI,CAACqI,SAAS,EAAE;QACjDC,UAAU,EAAE,CAACtK,UAAU,CAACuK,QAAQ;OACjC,CAAC;MACFC,KAAK,EAAE,IAAI1K,kBAAkB,CAAC,IAAI,CAACkC,IAAI,CAACyI,QAAQ,EAAE;QAChDH,UAAU,EAAE,CAACtK,UAAU,CAACuK,QAAQ;OACjC,CAAC;MACFG,OAAO,EAAE,IAAI5K,kBAAkB,CAAC,EAAE,EAAE,EAAE,CAAC;MACvC6K,UAAU,EAAE,IAAI7K,kBAAkB,CAAC,EAAE,EAAE,EAAE,CAAC;MAC1C8K,OAAO,EAAE,IAAI9K,kBAAkB,CAAC,IAAI,CAAC2D,wBAAwB,EAAE;QAC7D6G,UAAU,EAAE,CAACtK,UAAU,CAACuK,QAAQ;OACjC,CAAC;MACFM,MAAM,EAAE,IAAI/K,kBAAkB,CAAC,EAAE,EAAE;QACjCwK,UAAU,EAAE,CAACtK,UAAU,CAACuK,QAAQ;OACjC,CAAC;MACFO,MAAM,EAAE,IAAIhL,kBAAkB,CAAC,EAAE,EAAE;QACjCwK,UAAU,EAAE,CAACtK,UAAU,CAACuK,QAAQ;OACjC,CAAC;MACFQ,IAAI,EAAE,IAAIjL,kBAAkB,CAAC,EAAE,EAAE;QAC/BwK,UAAU,EAAE,CAACtK,UAAU,CAACuK,QAAQ;OACjC,CAAC;MACFS,QAAQ,EAAE,IAAIlL,kBAAkB,CAAC,EAAE,EAAE;QACnCwK,UAAU,EAAE,CAACtK,UAAU,CAACuK,QAAQ;OACjC,CAAC;MACFU,GAAG,EAAE,IAAInL,kBAAkB,CAAC,EAAE,EAAE,EAC/B,CAAC;MACFoL,KAAK,EAAE,IAAIpL,kBAAkB,CAAC,IAAI,CAACkC,IAAI,CAACkJ,KAAK,EAAE;QAC7CZ,UAAU,EAAE,CAACtK,UAAU,CAACuK,QAAQ;OACjC,CAAC;MACFL,KAAK,EAAE,IAAIpK,kBAAkB,CAAC,IAAI,CAACwB,cAAc,CAAC6J,kBAAkB,CAAC,IAAI,CAACnJ,IAAI,CAACkI,KAAK,CAAC,EAAE;QACrFI,UAAU,EAAE,CAACtK,UAAU,CAACuK,QAAQ;OACjC,CAAC;MACFa,GAAG,EAAE,IAAItL,kBAAkB,CAAC,EAAE,EAAE,EAC/B;KACF,CAAC;EAEJ;EAEAuL,yBAAyBA,CAAA;IACvB,IAAI,IAAI,CAACrG,SAAS,EAAE;MAClB,IAAI,CAACX,IAAI,CAAC4E,QAAQ,CAAC,KAAK,CAAC,CAACqC,aAAa,CAAC,CAACtL,UAAU,CAACuK,QAAQ,CAAC,CAAC;IAChE,CAAC,MAAM;MACL,IAAI,CAAClG,IAAI,CAAC4E,QAAQ,CAAC,KAAK,CAAC,CAACqC,aAAa,CAAC,EAAE,CAAC;IAC7C;IAEA,IAAI,CAACjH,IAAI,CAAC4E,QAAQ,CAAC,KAAK,CAAC,CAACsC,sBAAsB,EAAE;EACpD;EAEAC,QAAQA,CAAA;IACNrC,UAAU,CAAC,MAAK;MACd,IAAI,CAAC,IAAI,CAAC1G,KAAK,EAAE;QACf,IAAI,CAACgJ,eAAe,EAAE;MACxB,CAAC,MAAM;QACL,IAAI,CAACC,gBAAgB,CAAC,aAAa,CAAC;MACtC;MACA,IAAI,CAACC,cAAc,EAAE;MACrB,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAACC,qBAAqB,EAAE;IAC9B,CAAC,EAAE,IAAI,CAAC;EACV;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACL,eAAe,EAAE;IACtB,IAAI,CAACE,cAAc,EAAE;IACrB,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAACC,qBAAqB,EAAE;EAC9B;EAEAJ,eAAeA,CAAA;IACb,IAAI,CAACC,gBAAgB,CAAC,eAAe,IAAI,CAAC1I,iBAAiB,CAAC+I,EAAE,EAAE,EAAE,uBAAuB,IAAI,CAAC/I,iBAAiB,EAAE+I,EAAE,EAAE,CAAC;EACxH;EAEAJ,cAAcA,CAAA;IACZ,IAAI,CAACD,gBAAgB,CAAC,gBAAgB,IAAI,CAACrI,gBAAgB,CAAC2G,IAAI,EAAE,CAAC;EACrE;EAEA6B,qBAAqBA,CAAA;IACnB,IAAI,IAAI,CAACzI,wBAAwB,GAAG,GAAG,EAAE;MACvC,IAAI,CAACA,wBAAwB,GAAG,IAAI;IACtC;IACA,IAAI,CAACsI,gBAAgB,CAAC,cAAc,IAAI,CAACtI,wBAAwB,EAAE,CAAC;EACtE;EAEAwI,UAAUA,CAAA;IACR,IAAI,CAACF,gBAAgB,CAAC,YAAY,IAAI,CAAC5H,eAAe,CAAC,IAAI,CAACR,kBAAkB,CAAC,CAAC0I,KAAK,EAAE,EAAE,kBAAkB,IAAI,CAAClI,eAAe,CAAC,IAAI,CAACR,kBAAkB,CAAC,CAAC0I,KAAK,EAAE,CAAC;IACjK,IAAI,CAACN,gBAAgB,CAAC,YAAY,IAAI,CAAC5H,eAAe,CAAC,IAAI,CAACR,kBAAkB,CAAC,CAAC0I,KAAK,EAAE,EAAE,gBAAgB,IAAI,CAAClI,eAAe,CAAC,IAAI,CAACR,kBAAkB,CAAC,CAAC0I,KAAK,EAAE,CAAC;EACjK;EAEAN,gBAAgBA,CAACK,EAAU,EAAEE,OAAgB;IAC3C,IAAIC,KAAK,GAAG,IAAI,CAACpK,OAAO,GAAG,SAAS,GAAG,IAAI,CAACqK,gBAAgB,CAAC,IAAI,CAAClJ,oBAAoB,CAAC4E,IAAI,CAAC;IAC5F,IAAIuE,GAAG,GAAGvK,QAAQ,CAAC+G,cAAc,CAACmD,EAAE,CAAC;IAErC,IAAIK,GAAG,IAAI,IAAI,CAACnJ,oBAAoB,EAAE;MACpCmJ,GAAI,CAACC,KAAK,CAACC,eAAe,GAAGJ,KAAK;IACpC;IACA,IAAIE,GAAG,IAAI,CAACL,EAAE,CAACQ,QAAQ,CAAC,aAAa,CAAC,EAAE;MACtCH,GAAI,CAACC,KAAK,CAACH,KAAK,GAAG,OAAO;MAC1BE,GAAI,CAACC,KAAK,CAACG,OAAO,GAAG,GAAG;IAC1B;IAEA,IAAIP,OAAO,EAAE;MACX,IAAIQ,YAAY,GAAG5K,QAAQ,CAAC+G,cAAc,CAACqD,OAAO,CAAC;MACnD,IAAIQ,YAAY,EAAE;QAChBA,YAAY,CAACJ,KAAK,CAACH,KAAK,GAAG,OAAO;MACpC;IACF;EACF;EAEAQ,kBAAkBA,CAACX,EAAU,EAAEE,OAAgB;IAC7C,IAAIC,KAAK,GAAG,IAAI,CAACpK,OAAO,GAAG,SAAS,GAAG,IAAI,CAAC6K,cAAc,EAAE;IAC5D,IAAIP,GAAG,GAAGvK,QAAQ,CAAC+G,cAAc,CAACmD,EAAE,CAAC;IAErC,IAAIK,GAAG,EAAE;MACP,IAAI,CAAC,IAAI,CAAC3J,KAAK,IAAI,CAACsJ,EAAE,CAACQ,QAAQ,CAAC,aAAa,CAAC,IAAI,CAACR,EAAE,CAACQ,QAAQ,CAAC,YAAY,CAAC,IAAI,CAACR,EAAE,CAACQ,QAAQ,CAAC,UAAU,CAAC,IAAI,CAACR,EAAE,CAACQ,QAAQ,CAAC,aAAa,CAAC,IAAI,CAACR,EAAE,CAACQ,QAAQ,CAAC,OAAO,CAAC,EAAE;QAChKH,GAAI,CAACC,KAAK,CAACH,KAAK,GAAG,SAAS;MAC9B,CAAC,MAAM;QACLE,GAAI,CAACC,KAAK,CAACH,KAAK,GAAGA,KAAK;MAC1B;MACA,IAAI,CAACH,EAAE,CAACQ,QAAQ,CAAC,aAAa,CAAC,EAAE;QAC/BH,GAAI,CAACC,KAAK,CAACO,eAAe,GAAG,OAAO;QACpCR,GAAI,CAACC,KAAK,CAACQ,UAAU,GAAG,OAAO;MACjC;IACF;IAEA,IAAIZ,OAAO,EAAE;MACX,IAAIQ,YAAY,GAAG5K,QAAQ,CAAC+G,cAAc,CAACqD,OAAO,CAAC;MACnD,IAAIQ,YAAY,EAAE;QAChBA,YAAY,CAACJ,KAAK,CAACH,KAAK,GAAG,SAAS;MACtC;IACF;EAEF;EAEAY,WAAWA,CAACC,eAAwB;IAClC,IAAIb,KAAK,GAAG,IAAI,CAACpK,OAAO,GAAG,SAAS,GAAG,IAAI,CAAC6K,cAAc,EAAE;IAC5D,IAAI,IAAI,CAAClK,KAAK,IAAIsK,eAAe,EAAE;MACjC,OAAO;QACL,QAAQ,EAAE,aAAab,KAAK,EAAE;QAC9B,OAAO,EAAE,GAAGA,KAAK;OAClB;IACH;IACA,OAAO;MACL,QAAQ,EAAE,aAAaA,KAAK,EAAE;MAC9B,OAAO,EAAE,GAAGA,KAAK,EAAE;MACnB,SAAS,EAAE,KAAK;MAChB,QAAQ,EAAE;KACX;EACH;EAEAc,eAAeA,CAACjB,EAAU;IACxB,IAAIK,GAAG,GAAGvK,QAAQ,CAAC+G,cAAc,CAACmD,EAAE,CAAC;IACrCK,GAAI,CAACC,KAAK,CAACY,aAAa,GAAG,MAAM;IACjCb,GAAI,CAACC,KAAK,CAACG,OAAO,GAAG,KAAK;EAC5B;EAEAU,cAAcA,CAACnB,EAAU;IACvB,IAAIK,GAAG,GAAGvK,QAAQ,CAAC+G,cAAc,CAACmD,EAAE,CAAC;IACrCK,GAAI,CAACC,KAAK,CAACY,aAAa,GAAG,SAAS;IACpCb,GAAI,CAACC,KAAK,CAACG,OAAO,GAAG,GAAG;EAC1B;EAEAW,6BAA6BA,CAACC,KAAa;IACzC,IAAI,IAAI,CAACpK,iBAAiB,CAACqK,WAAW,IAAI,KAAK,EAAE;MAC/C,OAAO,IAAI;IACb;IACA,OAAOD,KAAK,IAAI,IAAI,CAACpK,iBAAiB,CAACqK,WAAW;EACpD;EAEAC,aAAaA,CAACC,MAAc,EAAEC,MAAc;IAC1C,MAAMC,WAAW,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC;IACrF,OAAOA,WAAW,CAACC,OAAO,CAACH,MAAM,CAAC,GAAGE,WAAW,CAACC,OAAO,CAACF,MAAM,CAAC;EAClE;EAEArB,gBAAgBA,CAACtE,IAAY;IAC3B,IAAIA,IAAI,IAAI1H,WAAW,CAACwN,OAAO,EAAE;MAC/B,OAAO,sEAAsE;IAC/E;IACA,IAAI9F,IAAI,IAAI1H,WAAW,CAACyN,IAAI,EAAE;MAC5B,OAAO,yEAAyE;IAClF;IACA,IAAI/F,IAAI,IAAI1H,WAAW,CAAC0N,OAAO,EAAE;MAC/B,OAAO,sEAAsE;IAC/E;IACA,OAAO,EAAE;EACX;EAEAlB,cAAcA,CAAA;IACZ,OAAO,SAAS;EAClB;EAEAmB,eAAeA,CAACC,SAAoB;IAClC,IAAI,CAACtL,KAAK,GAAG,IAAI;IACjB,EAAE,CAAC2G,OAAO,CAACC,IAAI,CAACxH,QAAQ,CAACyH,sBAAsB,CAAC,KAAK,CAAC,EAAGC,EAAO,IAAI;MAClE,IAAI,CAACA,EAAE,CAACwC,EAAE,CAACQ,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAChD,EAAE,CAACwC,EAAE,CAACQ,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAChD,EAAE,CAACwC,EAAE,CAACQ,QAAQ,CAAC,aAAa,CAAC,EAAE;QACnGhD,EAAE,CAACC,SAAS,CAACvB,MAAM,CAAC,UAAU,CAAC;MACjC;MACAkB,UAAU,CAAC,MAAK;QACd;MAAA,CACD,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;IACF,IAAI,CAACuD,kBAAkB,CAAC,aAAa,CAAC;IACtC,IAAI,CAACA,kBAAkB,CAAC,eAAe,IAAI,CAAC1J,iBAAiB,CAAC+I,EAAE,EAAE,EAAE,uBAAuB,IAAI,CAAC/I,iBAAiB,EAAE+I,EAAE,EAAE,CAAC;IACxH;IACA,IAAI,CAACW,kBAAkB,CAAC,cAAc,IAAI,CAACtJ,wBAAwB,EAAE,CAAC;IACtE,IAAI,CAACsJ,kBAAkB,CAAC,gBAAgB,IAAI,CAACrJ,gBAAgB,CAAC2G,IAAI,EAAE,CAAC;IAErE,KAAK,IAAIgE,QAAQ,IAAI,IAAI,CAACrK,YAAY,EAAE;MACtC,IAAI,IAAI,CAACX,iBAAiB,CAACgL,QAAQ,IAAIA,QAAQ,CAACnE,IAAI,EAAE;QACpD,IAAI,CAAC6C,kBAAkB,CAAC,gBAAgBsB,QAAQ,CAAChE,IAAI,EAAE,CAAC;MAC1D;IACF;IAEA,IAAI,CAAChH,iBAAiB,GAAG+K,SAAS;IAClC;IACA,IAAI,CAAC/K,iBAAiB,CAACiL,iBAAiB,GAAG,IAAI,CAAC5M,gBAAgB,CAAC6M,0BAA0B,CAAC,IAAI,CAAClL,iBAAiB,CAACiL,iBAAiB,CAAC;IACrI,KAAK,IAAID,QAAQ,IAAI,IAAI,CAACrK,YAAY,EAAE;MACtC,IAAI,IAAI,CAACX,iBAAiB,CAACgL,QAAQ,KAAKA,QAAQ,CAACnE,IAAI,EAAE;QACrD,IAAI,CAACmD,eAAe,CAAC,gBAAgBgB,QAAQ,CAAChE,IAAI,EAAE,CAAC;MACvD;IACF;IACA,IAAI,CAAC9G,aAAa,GAAG,IAAI,CAACF,iBAAiB,CAACqK,WAAW;IACvD,IAAI,IAAI,CAACrK,iBAAiB,CAACqK,WAAW,IAAI,KAAK,EAAE;MAC/C,IAAI,CAACnK,aAAa,GAAG,IAAI;IAC3B;IACA,IAAI,CAACC,oBAAoB,GAAG,IAAI,CAACH,iBAAiB,CAACqK,WAAW;IAC9D,IAAI,CAACjK,wBAAwB,GAAG,IAAI,CAACJ,iBAAiB,CAACiL,iBAAiB,CAACE,MAAM,CAACC,QAAQ,EAAE;IAC1F,IAAI,CAAC/K,gBAAgB,GAAG,IAAI,CAACM,YAAY,CAACoG,MAAM,CAACR,EAAE,IAAIA,EAAE,CAACM,IAAI,CAACwE,WAAW,EAAE,IAAI,IAAI,CAACrL,iBAAiB,CAACgL,QAAS,CAACK,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC;IAClI,IAAI,CAACjK,OAAO,CAAC4J,QAAQ,GAAG,IAAI,CAAC3K,gBAAgB,CAACwG,IAAI;IAClD,IAAI,CAACzF,OAAO,CAACgJ,KAAK,GAAG,IAAI,CAAClK,aAAa;IACvC,IAAI,CAACoL,yBAAyB,EAAE;IAChCnI,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC7B,gBAAgB,CAAC;IAClC,IAAI,CAACgK,mBAAmB,EAAE;IAC1B,IAAI,CAACC,wBAAwB,EAAE;IAC/B,IAAI,CAAChD,QAAQ,EAAE;IACf,IAAI,CAAC/I,KAAK,GAAG,KAAK;EACpB;EAEAgM,cAAcA,CAACT,QAAkB;IAC/B,IAAI,IAAI,CAACvL,KAAK,EAAE;MACd,IAAI,CAACiK,kBAAkB,CAAC,gBAAgB,IAAI,CAACrJ,gBAAgB,CAAC2G,IAAI,EAAE,CAAC;MACrE,IAAI,CAAC3G,gBAAgB,GAAG2K,QAAQ;MAChC,IAAI,CAAC5J,OAAO,CAAC4J,QAAQ,GAAG,IAAI,CAAC3K,gBAAgB,CAACwG,IAAI;MAClD,IAAI,CAAC0E,mBAAmB,EAAE;MAC1B,KAAK,IAAIP,QAAQ,IAAI,IAAI,CAACrK,YAAY,EAAE;QACtC,IAAI,CAAC+I,kBAAkB,CAAC,gBAAgBsB,QAAQ,CAAChE,IAAI,EAAE,CAAC;MAC1D;MACA,IAAI,CAAC2B,cAAc,EAAE;IACvB;EACF;EAEA+C,WAAWA,CAACtB,KAAa;IACvB,IAAI,IAAI,CAAC3K,KAAK,IAAI,IAAI,CAAC0K,6BAA6B,CAACC,KAAK,CAAC,EAAE;MAC3D,IAAI,CAAClK,aAAa,GAAGkK,KAAK;MAC1B,IAAI,CAAChJ,OAAO,CAACgJ,KAAK,GAAG,IAAI,CAAClK,aAAa;MACvC,IAAI,CAACqL,mBAAmB,EAAE;MAC1B,IAAI,CAACC,wBAAwB,EAAE;IACjC;EACF;EAEAG,YAAYA,CAAC7D,MAAc;IACzB,IAAI,IAAI,CAACrI,KAAK,EAAE;MACd,IAAI,CAACiK,kBAAkB,CAAC,cAAc,IAAI,CAACtJ,wBAAwB,EAAE,CAAC;MACtE,IAAI,CAACA,wBAAwB,GAAG0H,MAAM;MACtC,IAAI,CAACY,gBAAgB,CAAC,cAAc,IAAI,CAACtI,wBAAwB,EAAE,CAAC;MACpE,IAAI,CAACgB,OAAO,CAAC1B,gBAAgB,GAAG,IAAI,CAACU,wBAAwB;MAC7D,IAAI,CAACmL,mBAAmB,EAAE;MAC1B,IAAI,CAACC,wBAAwB,EAAE;IACjC;EACF;EAIAI,WAAWA,CAACC,IAAS,EAAEC,CAAS;IAC9B,IAAI,CAACpC,kBAAkB,CAAC,YAAY,IAAI,CAAC5I,eAAe,CAAC,IAAI,CAACR,kBAAkB,CAAC,CAAC0I,KAAK,EAAE,EAAE,kBAAkB,IAAI,CAAClI,eAAe,CAAC,IAAI,CAACR,kBAAkB,CAAC,CAAC0I,KAAK,EAAE,CAAC;IACnK,IAAI,CAACU,kBAAkB,CAAC,YAAY,IAAI,CAAC5I,eAAe,CAAC,IAAI,CAACR,kBAAkB,CAAC,CAAC0I,KAAK,EAAE,EAAE,gBAAgB,IAAI,CAAClI,eAAe,CAAC,IAAI,CAACR,kBAAkB,CAAC,CAAC0I,KAAK,EAAE,CAAC;IACjK,IAAI,CAAC1I,kBAAkB,GAAGwL,CAAC;IAC3B,IAAI,CAACpD,gBAAgB,CAAC,YAAY,IAAI,CAAC5H,eAAe,CAAC,IAAI,CAACR,kBAAkB,CAAC,CAAC0I,KAAK,EAAE,EAAE,kBAAkB,IAAI,CAAClI,eAAe,CAAC,IAAI,CAACR,kBAAkB,CAAC,CAAC0I,KAAK,EAAE,CAAC;IACjK,IAAI,CAACN,gBAAgB,CAAC,YAAY,IAAI,CAAC5H,eAAe,CAAC,IAAI,CAACR,kBAAkB,CAAC,CAAC0I,KAAK,EAAE,EAAE,gBAAgB,IAAI,CAAClI,eAAe,CAAC,IAAI,CAACR,kBAAkB,CAAC,CAAC0I,KAAK,EAAE,CAAC;IAC/J,IAAI,CAAC+C,aAAa,GAAG,IAAI,CAACjL,eAAe,CAAC,IAAI,CAACR,kBAAkB,CAAC,CAAC0I,KAAK;IACxE,IAAI,CAAClH,SAAS,GAAG,IAAI,CAAChB,eAAe,CAAC,IAAI,CAACR,kBAAkB,CAAC,CAAC0L,gBAAgB;IAC/E,IAAI,CAACjL,aAAa,GAAG,IAAI,CAACD,eAAe,CAAC,IAAI,CAACR,kBAAkB,CAAC;EACpE;EAEA2L,aAAaA,CAACC,WAAgB;IAC5B,IAAI,CAACjM,oBAAoB,GAAGiM,WAAW;IACvC,IAAI,CAACvG,SAAS,CAAC0D,KAAK,CAACQ,UAAU,GAAG,IAAI,CAACV,gBAAgB,CAAC,IAAI,CAAClJ,oBAAoB,CAAC4E,IAAI,CAAC;IACvF,IAAI,CAAC2D,QAAQ,EAAE;EACjB;EAEA2D,MAAMA,CAAA;IACJ,EAAE,CAAC/F,OAAO,CAACC,IAAI,CAACxH,QAAQ,CAACyH,sBAAsB,CAAC,KAAK,CAAC,EAAGC,EAAO,IAAI;MAClEA,EAAE,CAACC,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;MAC5BF,EAAE,CAACC,SAAS,CAACvB,MAAM,CAAC,UAAU,CAAC;IACjC,CAAC,CAAC;IACF,KAAK,IAAI+F,QAAQ,IAAI,IAAI,CAACrK,YAAY,EAAE;MACtC,IAAI,IAAI,CAACX,iBAAiB,CAACgL,QAAQ,KAAKA,QAAQ,CAACnE,IAAI,EAAE;QACrD,IAAI,CAACqD,cAAc,CAAC,gBAAgBc,QAAQ,CAAChE,IAAI,EAAE,CAAC;MACtD;IACF;IACA,IAAI,CAAC0C,kBAAkB,CAAC,eAAe,IAAI,CAAC1J,iBAAiB,CAAC+I,EAAE,EAAE,EAAE,uBAAuB,IAAI,CAAC/I,iBAAiB,EAAE+I,EAAE,EAAE,CAAC;IACxH,IAAI,CAACL,gBAAgB,CAAC,aAAa,CAAC;IACpC,IAAI,CAACjJ,KAAK,GAAG,IAAI;IACjB,IAAI,CAACY,gBAAgB,GAAG,IAAI,CAACM,YAAY,CAAC,CAAC,CAAC;IAC5C,IAAI,CAAC8K,cAAc,CAAC,IAAI,CAACpL,gBAAgB,CAAC;IAC1C,IAAI,CAACqJ,kBAAkB,CAAC,aAAa,IAAI,CAACxJ,aAAa,EAAE,CAAC;IAC1D,IAAI,CAACA,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACuL,WAAW,CAAC,IAAI,CAACxL,aAAa,CAAC;IACpC,IAAI,CAACwJ,kBAAkB,CAAC,cAAc,IAAI,CAACtJ,wBAAwB,EAAE,CAAC;IACtE,IAAI,CAACA,wBAAwB,GAAG,GAAG;IACnC,IAAI,CAACoI,QAAQ,EAAE;IACf,IAAI,CAAC+C,mBAAmB,EAAE;IAC1B;IACA,IAAI,CAAChK,gBAAgB,GAAG,KAAK;EAC/B;EAMA6K,aAAaA,CAAA;IACX,IAAI,CAACrK,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACqG,yBAAyB,EAAE;EAClC;EAEAgE,aAAaA,CAAA;IACX,IAAI,CAACtK,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACqG,yBAAyB,EAAE;EAClC;EAEA5E,IAAIA,CAAA;IACF6I,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACrB,IAAI,CAACjN,WAAW,EAAE;IAClB,IAAI,IAAI,CAACA,WAAW,IAAI,CAAC,EAAE;MACzB;IACF;IACA,IAAI,IAAI,CAACA,WAAW,IAAI,CAAC,EAAE;MACzB,IAAI,CAAC2C,WAAW,GAAG,IAAI;MAEvB,IAAI,IAAI,CAACZ,IAAI,CAACmL,KAAK,EAAE;QACnB,IAAIC,SAAS,GAAG,IAAI,CAACpL,IAAI,CAACqL,KAAK;QAC/B,IAAI,CAAChM,gBAAgB,CAAC0G,KAAK,GAAGqF,SAAS,CAACrF,KAAK;QAC7C,IAAI,CAAC1G,gBAAgB,CAAC8G,KAAK,GAAGiF,SAAS,CAACjF,KAAK;QAC7C,IAAI,CAAC9G,gBAAgB,CAACwH,KAAK,GAAGuE,SAAS,CAACvE,KAAK;QAC7C,IAAI,CAACxH,gBAAgB,CAACqH,IAAI,GAAG0E,SAAS,CAAC1E,IAAI;QAC3C,IAAI,CAACrH,gBAAgB,CAACgH,OAAO,GAAG+E,SAAS,CAAC/E,OAAO;QACjD,IAAI,CAAChH,gBAAgB,CAACiM,UAAU,GAAGF,SAAS,CAAC5E,MAAM;QACnD,IAAI,CAACnH,gBAAgB,CAACkM,YAAY,GAAGH,SAAS,CAAC3E,MAAM;QACrD,IAAI,CAACpH,gBAAgB,CAACuH,GAAG,GAAGwE,SAAS,CAACxE,GAAG;QACzC,IAAI,CAACvH,gBAAgB,CAACiH,UAAU,GAAG8E,SAAS,CAAC9E,UAAU;QACvD,IAAI,CAACjH,gBAAgB,CAACsH,QAAQ,GAAGyE,SAAS,CAACzE,QAAQ;QACnD,IAAI,CAACtH,gBAAgB,CAAC0H,GAAG,GAAGqE,SAAS,CAACrE,GAAG;QACzC,IAAI,CAAC1H,gBAAgB,CAACwG,KAAK,GAAGuF,SAAS,CAACvF,KAAK;QAC7C,IAAI,CAACxG,gBAAgB,CAACkH,OAAO,GAAG6E,SAAS,CAAC7E,OAAO,CAACf,IAAI;QACtD;MACF,CAAC,MAAM;QAEL,IAAI,CAACrI,YAAY,CAACqO,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAAC1N,WAAW,GAAG,CAAC;QACpB;MACF;IACF;IACA,IAAI,IAAI,CAACA,WAAW,IAAI,CAAC,EAAE;MACzB,IAAI,CAACK,OAAO,CAACE,MAAM,GAAG,CAAC,IAAI,CAACkM,aAAa,GAAG,IAAI,CAAC9L,oBAAoB,CAACgN,QAAQ,IAAI,GAAG;MACrF;IACF;IACA,IAAI,IAAI,CAAC3N,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EAEA4N,QAAQA,CAAA;IACNZ,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACrB,IAAI,CAAC/D,QAAQ,EAAE;IACf,IAAI,IAAI,CAAClJ,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;IACpB,CAAC,MAAM;MACL,IAAI,CAACd,YAAY,CAACqO,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;IACJ;EACF;EAEAG,MAAMA,CAAA;IACJ,IAAI,CAAC/O,QAAQ,CAACgP,IAAI,EAAE;EACtB;EAEAC,aAAaA,CAACC,cAAmB,EAAEC,kBAAuB;IACxD,IAAI,CAACjP,cAAc,CAAC+O,aAAa,CAACC,cAAc,EAAEC,kBAAkB,EAAE,IAAI,CAAC;EAC7E;EAEAC,KAAKA,CAAA;IACH,IAAI,CAACtL,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;EAC5B;EAGAuL,QAAQA,CAAA;IAEN,IAAI,IAAI,CAACvL,MAAM,EAAE;MACf,IAAI6I,SAAS,GAAG,IAAI,CAACtL,KAAK,GAAG,CAAC,GAAG,IAAI,CAACO,iBAAiB,CAAC+I,EAAE;MAC1D,IAAI2E,GAAG,GAAG;QACRC,KAAK,EAAE,EAAE;QACT3C,QAAQ,EAAE,IAAI,CAAC3K,gBAAgB,CAACwG,IAAI;QACpCuD,KAAK,EAAE,IAAI,CAAClK,aAAa;QACzB0N,cAAc,EAAE,IAAI,CAACxN,wBAAwB;QAC7CyN,KAAK,EAAE,IAAI,CAAC9M,aAAa,CAAC8M,KAAK,CAACzC,QAAQ,EAAE;QAC1Cc,WAAW,EAAE,IAAI,CAACjM,oBAAoB,CAAC4E,IAAI;QAC3CiJ,sBAAsB,EAAE,EAAE;QAC1BtO,WAAW,EAAEuL,SAAS;QACtBgD,OAAO,EAAE;UACPC,WAAW,EAAE,IAAI,CAACzN,mBAAmB;UACrC0N,SAAS,EAAE,IAAI,CAACvN,gBAAgB,CAAC0G,KAAK;UACtC8G,QAAQ,EAAE,IAAI,CAACxN,gBAAgB,CAAC8G,KAAK;UACrCU,KAAK,EAAE,IAAI,CAACxH,gBAAgB,CAACwH,KAAK;UAClChB,KAAK,EAAE,IAAI,CAAC1G,iBAAiB,CAACwG,IAAI,GAAG,GAAG,GAAG,IAAI,CAACtG,gBAAgB,CAACwG,KAAK;UACtEiH,OAAO,EAAE,IAAI,CAACzN,gBAAgB,CAACiM,UAAU,GAAG,GAAG,GAAG,IAAI,CAACjM,gBAAgB,CAACkM,YAAY;UACpF7E,IAAI,EAAE,IAAI,CAACrH,gBAAgB,CAACqH,IAAI;UAChCH,OAAO,EAAE,IAAI,CAAClH,gBAAgB,CAACkH,OAAO;UACtCI,QAAQ,EAAE,IAAI,CAACtH,gBAAgB,CAACsH,QAAQ;UACxCN,OAAO,EAAE,IAAI,CAAChH,gBAAgB,CAACgH,OAAO;UACtCC,UAAU,EAAE,IAAI,CAACjH,gBAAgB,CAACiH,UAAU;UAC5CyG,SAAS,EAAE,IAAI,CAAC1N,gBAAgB,CAACuH,GAAG;UACpCoG,GAAG,EAAE,IAAI,CAAC3N,gBAAgB,CAAC0H;;OAE9B;MACD,IAAI,CAACkG,qBAAqB,CAACZ,GAAG,CAAC;IACjC,CAAC,MACI;MACH,IAAI,CAACvL,UAAU,GAAG,IAAI;MACtB,IAAI,CAAC3D,YAAY,CAACqO,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;IACJ;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IAEA;EACF;EAEAuB,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACnO,wBAAwB,IAAI,IAAI,EAAE;MACzC,IAAI,CAACA,wBAAwB,GAAG,GAAG;IACrC;IACA,IAAIsN,GAAG,GAAG;MACRc,aAAa,EAAE,IAAI,CAACnO,gBAAgB,CAACwG,IAAI;MACzC4H,UAAU,EAAE,IAAI,CAACvO,aAAa;MAC9BwO,kBAAkB,EAAE,IAAI,CAACtO;KAC1B;IACD,IAAI,CAAC7B,cAAc,CAACoQ,YAAY,CAACjB,GAAG,CAAC,CAACkB,IAAI,CAAC1R,IAAI,CAAC,CAAC,CAAC,CAAC,CAACsG,SAAS,CAACE,GAAG,IAAG;MAClE,IAAI,CAAClF,YAAY,CAACqO,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA6B,YAAYA,CAAA;IACV,IAAI,CAAClQ,MAAM,CAACmQ,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACrC;EAEM/L,cAAcA,CAAA;IAAA,IAAAgM,KAAA;IAAA,OAAAC,iBAAA;MAClB,IAAI;QACF,MAAMtL,GAAG,SAASqL,KAAI,CAAC1Q,gBAAgB,CAAC4Q,oBAAoB,CAACF,KAAI,CAAC/P,IAAI,CAAC+J,EAAE,CAAC,CACvE6F,IAAI,CAAC1R,IAAI,CAAC,CAAC,CAAC,CAAC,CACbgS,SAAS,EAAE,CAAC,CAAC;QAEhB;QACAH,KAAI,CAACxP,cAAc,GAAGmE,GAAG,CAACqD,MAAM,CAAEoI,IAAe,IAAKA,IAAI,IAAIA,IAAI,CAACnE,QAAQ,IAAImE,IAAI,CAACnE,QAAQ,CAACoE,IAAI,EAAE,KAAK,EAAE,CAAC;QAC3GL,KAAI,CAACxP,cAAc,GAAGwP,KAAI,CAACM,uCAAuC,CAACN,KAAI,CAACxP,cAAc,CAAC;QACvF;QACA,MAAM+P,mBAAmB,GAA8B,EAAE;QAEzD;QACAP,KAAI,CAACxP,cAAc,GAAGwP,KAAI,CAACxP,cAAc,CAACwH,MAAM,CAAEwI,QAAQ,IAAI;UAC5D,MAAMC,SAAS,GAAGD,QAAQ,CAACE,OAAO,EAAE1G,EAAE,IAAI,IAAI;UAE9C,IAAIyG,SAAS,KAAK,IAAI,EAAE;YACtB;YACA,IAAID,QAAQ,CAAC1K,IAAI,KAAK,MAAM,IAAI,EAAE2K,SAAS,IAAIF,mBAAmB,CAAC,EAAE;cACnEA,mBAAmB,CAACE,SAAS,CAAC,GAAGD,QAAQ,CAAC,CAAC;YAC7C;YAEA,IACER,KAAI,CAACzN,wBAAwB,IAC7ByN,KAAI,CAACzN,wBAAwB,CAACuD,IAAI,KAAK,OAAO,IAC9CkK,KAAI,CAACzN,wBAAwB,CAACmO,OAAO,IACrCV,KAAI,CAACzN,wBAAwB,CAACmO,OAAO,CAAC1G,EAAE,KAAKyG,SAAS,EACtD;cACA;cACAT,KAAI,CAACzN,wBAAwB,GAAGiO,QAAQ;YAC1C;YAEA,OAAOA,QAAQ,CAAC,CAAC;UACnB;UAEA,OAAO,IAAI,CAAC,CAAC;QACf,CAAC,CAAC;QAGF,IAAIR,KAAI,CAACxP,cAAc,CAAC4L,MAAM,IAAI,CAAC,EAAE;UACnC4D,KAAI,CAACtP,KAAK,GAAG,IAAI;UACjBsP,KAAI,CAAC1O,gBAAgB,GAAG0O,KAAI,CAACpO,YAAY,CAAC,CAAC,CAAC;UAC5CoO,KAAI,CAAC7O,aAAa,GAAG,IAAI;UACzB6O,KAAI,CAAC3O,wBAAwB,GAAG,GAAG;UACnC2O,KAAI,CAAC5O,oBAAoB,GAAG,IAAI;QAClC,CAAC,MACI;UACH,IAAI4O,KAAI,CAACzN,wBAAwB,CAACyH,EAAE,EAAE;YACpCgG,KAAI,CAAC/O,iBAAiB,GAAG+O,KAAI,CAACzN,wBAAwB;UACxD,CAAC,MAAM;YACLyN,KAAI,CAAC/O,iBAAiB,GAAG+O,KAAI,CAACxP,cAAc,CAAC,CAAC,CAAC;UACjD;UACA,KAAK,IAAIyL,QAAQ,IAAI+D,KAAI,CAACpO,YAAY,EAAE;YACtC,IAAIoO,KAAI,CAAC/O,iBAAiB,CAACgL,QAAQ,KAAKA,QAAQ,CAACnE,IAAI,EAAE;cACrDkI,KAAI,CAAC/E,eAAe,CAAC,gBAAgBgB,QAAQ,CAAChE,IAAI,EAAE,CAAC;YACvD;UACF;UACA+H,KAAI,CAAC/O,iBAAiB,CAACiL,iBAAiB,GAAG8D,KAAI,CAAC1Q,gBAAgB,CAAC6M,0BAA0B,CAAC6D,KAAI,CAAC/O,iBAAiB,CAACiL,iBAAiB,CAAC;UACrI8D,KAAI,CAAC3O,wBAAwB,GAAG2O,KAAI,CAAC/O,iBAAiB,CAACiL,iBAAiB,CAACE,MAAM,CAACC,QAAQ,EAAE;UAC1F2D,KAAI,CAAC7O,aAAa,GAAG6O,KAAI,CAAC/O,iBAAiB,CAACqK,WAAW;UACvD,IAAI0E,KAAI,CAAC7O,aAAa,IAAI,KAAK,EAAE;YAC/B6O,KAAI,CAAC7O,aAAa,GAAG,IAAI;UAC3B;UACA6O,KAAI,CAAC5O,oBAAoB,GAAG4O,KAAI,CAAC/O,iBAAiB,CAACqK,WAAW;UAC9D0E,KAAI,CAAC1O,gBAAgB,GAAG0O,KAAI,CAACpO,YAAY,CAACoG,MAAM,CAACR,EAAE,IAAIA,EAAE,CAACM,IAAI,IAAIkI,KAAI,CAAC/O,iBAAiB,CAACgL,QAAS,CAAC,CAAC,CAAC,CAAC;UACtG+D,KAAI,CAACzD,yBAAyB,EAAE;QAClC;QACAyD,KAAI,CAACrI,cAAc,EAAE;QACrBqI,KAAI,CAAC1G,yBAAyB,EAAE;QAChC,IAAI0G,KAAI,CAAC3O,wBAAwB,IAAI,GAAG,EAAE;UACxC2O,KAAI,CAAC3N,OAAO,GAAG;YACb,UAAU,EAAE2N,KAAI,CAAC1O,gBAAgB,CAACwG,IAAI;YACtC,OAAO,EAAEkI,KAAI,CAAC7O,aAAa;YAC3B,kBAAkB,EAAE6O,KAAI,CAAC3O;WAC1B;QACH,CAAC,MACI;UACH2O,KAAI,CAAC3N,OAAO,GAAG;YACb,UAAU,EAAE2N,KAAI,CAAC1O,gBAAgB,CAACwG,IAAI;YACtC,OAAO,EAAEkI,KAAI,CAAC7O,aAAa;YAC3B,kBAAkB,EAAE;WACrB;QACH;QACA6O,KAAI,CAAC9O,oBAAoB,GAAG8O,KAAI,CAAC9N,eAAe,CAAC,CAAC,CAAC;QACnD8N,KAAI,CAACjO,eAAe,GAAGiO,KAAI,CAAC9N,eAAe,CAAC,CAAC,CAAC,CAACa,SAAS;QACxDiN,KAAI,CAACxD,mBAAmB,EAAE;QAC1BwD,KAAI,CAACvD,wBAAwB,EAAE;QAC/BuD,KAAI,CAAChO,aAAa,GAAGgO,KAAI,CAACjO,eAAe,CAAC,CAAC,CAAC;QAC5CiO,KAAI,CAACjN,SAAS,GAAGiN,KAAI,CAACjO,eAAe,CAAC,CAAC,CAAC,CAACkL,gBAAgB;QACzD+C,KAAI,CAACvG,QAAQ,EAAE;MACjB,CAAC,CAAC,OAAO7E,KAAK,EAAE;QACdR,OAAO,CAACQ,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC5C;IAAC;EACH;EAEM+L,YAAYA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAX,iBAAA;MAChB,OAAO,IAAI9K,OAAO,CAAO,CAACC,OAAO,EAAEC,MAAM,KAAI;QAC3C,IAAI;UACFuL,MAAI,CAAC7O,eAAe,CAACsF,OAAO,CAAC,CAAC+I,IAAI,EAAES,KAAK,KAAI;YAC3CT,IAAI,CAACnG,KAAK,GAAG2G,MAAI,CAACxO,WAAW,CAACyO,KAAK,CAAC,CAAC5G,KAAK;YAC1CmG,IAAI,CAACU,OAAO,GAAGF,MAAI,CAACxO,WAAW,CAACyO,KAAK,CAAC,CAACC,OAAO;UAChD,CAAC,CAAC;UACF1L,OAAO,EAAE;QACX,CAAC,CAAC,OAAOR,KAAK,EAAE;UACdS,MAAM,CAACT,KAAK,CAAC;QACf;MACF,CAAC,CAAC;IAAC;EACL;EAEM4H,mBAAmBA,CAAA;IAAA,IAAAuE,MAAA;IAAA,OAAAd,iBAAA;MACvB,IAAI;QACF,MAAMtL,GAAG,SAASoM,MAAI,CAACvR,cAAc,CAACwR,uBAAuB,CAACD,MAAI,CAAC1O,OAAO,CAAC,CAACwN,IAAI,CAAC1R,IAAI,CAAC,CAAC,CAAC,CAAC,CAACgS,SAAS,EAAE;QACrGY,MAAI,CAAC3O,WAAW,GAAGuC,GAAY;QAC/B,MAAMoM,MAAI,CAACJ,YAAY,EAAE;QACzBI,MAAI,CAAC/D,aAAa,GAAG+D,MAAI,CAAC/O,aAAa,CAACiI,KAAK;MAC/C,CAAC,CAAC,OAAOrF,KAAK,EAAE;QACdR,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,KAAK,CAAC;MAC1C;IAAC;EACH;EAEM6H,wBAAwBA,CAAA;IAAA,IAAAwE,MAAA;IAAA,OAAAhB,iBAAA;MAC5B,IAAI;QACF,MAAMtL,GAAG,SAASsM,MAAI,CAACzR,cAAc,CAAC0R,qBAAqB,CAACD,MAAI,CAAC5O,OAAO,CAAC,CAACwN,IAAI,CAAC1R,IAAI,CAAC,CAAC,CAAC,CAAC,CAACgS,SAAS,EAAE;QACnGc,MAAI,CAACxO,oBAAoB,GAAGkC,GAAY;MAC1C,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdR,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,KAAK,CAAC;MAC1C;IAAC;EACH;EAEAuM,iBAAiBA,CAACC,KAAU;IAC1BhK,UAAU,CAAC,MAAK;MACd,IAAI,CAAC3F,iBAAiB,GAAG2P,KAAK,CAACzD,KAAK;IACtC,CAAC,EAAE,GAAG,CAAC;EACT;EAEA4B,qBAAqBA,CAAC8B,IAAS;IAC7BjN,OAAO,CAACC,GAAG,CAACgN,IAAI,CAAC;IACjB,IAAI,CAACnR,IAAI,CAACwH,GAAG,CAAC,CAAC,IAAI,CAACV,eAAe,GAAG,IAAI,CAACxH,cAAc,CAAC8R,uBAAuB,CAACD,IAAI,CAAC,GAAG,IAAI,CAAC7R,cAAc,CAAC+R,kBAAkB,CAACF,IAAI,CAAC,EAAExB,IAAI,CAC1I3R,SAAS,CAAEsT,KAAU,IAAI;MACvBpN,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEmN,KAAK,CAAC;MAC3B,IAAIC,IAAI,GAAG,IAAI;MACfhT,eAAe,CAAC+S,KAAK,CAACE,SAAS,EAAE3S,gBAAgB,GAAG,MAAM,GAAG,SAAS,CAAC,CAACoF,IAAI,CAAC,UAAUwN,QAAa;QAClGA,QAAQ,CAACC,YAAY,CAAC;UACpBC,SAASA,CAAA;YACPR,IAAI,CAACzC,KAAK,GAAG4C,KAAK,CAACE,SAAS;YAC5BL,IAAI,CAACtC,sBAAsB,GAAGyC,KAAK,CAACzC,sBAAsB;YAC1D;YACA0C,IAAI,CAACK,aAAa,CAACT,IAAI,EAAEG,KAAK,CAAC;UACjC,CAAC;UACDO,OAAOA,CAACC,OAAY;YAClB5N,OAAO,CAACC,GAAG,CAAC2N,OAAO,CAAC;UACtB;SACD,CAAC;MACJ,CAAC,CAAC;MACF,OAAOtT,EAAE,CAAC2S,IAAI,CAAC;IACjB,CAAC,CAAC,EACFlT,IAAI,CAAC,CAAC,CAAC,CACR,CAACsG,SAAS,CAAEE,GAAQ,IAAI,CAAG,CAAC,CAAC,CAAC;EACjC;EAEAmN,aAAaA,CAACG,eAAoB,EAAET,KAAU;IAC5C,IAAI,CAACtR,IAAI,CAACwH,GAAG,CAAC,CAAC,IAAI,CAACV,eAAe,GAAG,IAAI,CAACxH,cAAc,CAAC0S,kBAAkB,CAACD,eAAe,CAAC,GAAG,IAAI,CAACzS,cAAc,CAACsS,aAAa,CAACG,eAAe,CAAC,EAAExN,SAAS,CAAEE,GAAQ,IAAI;MAEzK,IAAI,CAACpE,WAAW,GAAG,CAAC;MACpB,IAAI,IAAI,CAACuC,oBAAoB,EAAE;QAC7B,MAAMqP,YAAY,GAAG;UACnB3N,GAAG,EAAE,IAAI,CAAC3B,kBAAkB;UAAE;UAC9BsG,KAAK,EAAEqI,KAAK,CAACrI,KAAK;UAClBiJ,eAAe,EAAEZ,KAAK,CAACa,YAAY,CAAC1E,KAAK,GAAG,GAAG;UAAE;UACjD2E,aAAa,EAAEd,KAAK,CAACzC,sBAAsB;UAC3CwD,SAAS,EAAE5P,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,IAAI,EAAE;UAC5D4P,UAAU,EAAE,CACV;YACEC,GAAG,EAAEjB,KAAK,CAACxH,EAAE;YACb0I,QAAQ,EAAE,CAAC;YACXC,WAAW,EAAEV,eAAe,CAAChG,QAAQ,GAAG,KAAK,GAAGgG,eAAe,CAAC5G,KAAK,GAAG,WAAW,GAAG4G,eAAe,CAACnD,KAAK,GAAG,QAAQ;YACtHhO,MAAM,EAAE0Q,KAAK,CAACa,YAAY,CAAC1E,KAAK,GAAG;WACpC;SAEJ;QAED;QACA,IAAI,IAAI,CAACjL,mBAAmB,EAAE;UAC5B,IAAI,CAAClD,cAAc,CAACoT,mCAAmC,CAAC,IAAI,CAAClQ,mBAAmB,EAC9E,IAAI,CAACG,kBAAkB,CAAC,CAAC4B,SAAS,CAAC;YACjCC,IAAI,EAAGC,GAAsB,IAAI;cAC/BP,OAAO,CAACC,GAAG,CAACM,GAAG,CAAC;cAChB,MAAMkO,MAAM,GAAGC,IAAI,CAACC,MAAM,EAAE,CAAC1G,QAAQ,CAAC,EAAE,CAAC,CAAC2G,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGF,IAAI,CAACC,MAAM,EAAE,CAAC1G,QAAQ,CAAC,EAAE,CAAC,CAAC2G,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;cACxG,MAAMb,YAAY,GAClB;gBACE3N,GAAG,EAAE,IAAI,CAAC3B,kBAAkB;gBAC5BsG,KAAK,EAAE,IAAI,CAAClJ,IAAI,CAACkJ,KAAK;gBACtBiJ,eAAe,EAAEZ,KAAK,CAACa,YAAY,CAAC1E,KAAK,GAAG,GAAG;gBAAE;gBACjD2E,aAAa,EAAEd,KAAK,CAACzC,sBAAsB;gBAC3C9G,IAAI,EAAEtD,GAAG,CAACsO,cAAc,IAAI,IAAI,CAACvQ,mBAAmB;gBACpD6P,SAAS,EAAE5N,GAAG,CAACwE,KAAK;gBACpBqJ,UAAU,EAAE,CACV;kBACEC,GAAG,EAAEjB,KAAK,CAACzC,sBAAsB;kBACjC2D,QAAQ,EAAE,CAAC;kBACXC,WAAW,EAAEnB,KAAK,CAACzC,sBAAsB;kBACzCjO,MAAM,EAAE0Q,KAAK,CAACa,YAAY,CAAC1E,KAAK,GAAG;iBACpC;eAGJ;cACD,IAAI,CAACnO,cAAc,CAAC0T,gBAAgB,CAACf,YAAY,CAAC,CAAC1N,SAAS,CAAEE,GAAQ,IAAI;gBACxEP,OAAO,CAACC,GAAG,CAACM,GAAG,CAAC;cAClB,CAAC,CAAC;YACJ,CAAC;YACDC,KAAK,EAAGC,GAAQ,IAAI;cAClBT,OAAO,CAACC,GAAG,CAACQ,GAAG,CAAC;YAClB;WACD,CAAC;QACN,CAAC,MAAM;UACL;UACA,IAAI,CAACrF,cAAc,CAAC0T,gBAAgB,CAACf,YAAY,CAAC,CAAC1N,SAAS,CAAEE,GAAQ,IAAI;YACxEP,OAAO,CAACC,GAAG,CAACM,GAAG,CAAC;UAClB,CAAC,CAAC;QACJ;MACF;IAEF,CAAC,CAAC,CAAC;EACL;EAEAwO,0BAA0BA,CAAA;IACxB,IAAI,IAAI,CAAC1Q,oBAAoB,CAAC2J,MAAM,KAAK,CAAC,EAAE;MAC1C,OAAO,CAAC;IACV;IACA,MAAMgH,eAAe,GAAG,IAAI,CAAC3Q,oBAAoB,CAAC,IAAI,CAAClB,kBAAkB,CAAC;IAC1E,MAAM8R,cAAc,GAAG,IAAI,CAACnS,oBAAoB,CAACgN,QAAQ,GAAG,GAAG;IAE/D,OAAO,CAACkF,eAAe,EAAEE,UAAU,IAAI,CAAC,IAAID,cAAc;EAC5D;EAGAE,kBAAkBA,CAAA;IAChB,MAAMC,UAAU,GAAG,IAAI,CAACxG,aAAa,GAAG,IAAI,CAAC9L,oBAAoB,CAACgN,QAAQ;IAC1E,OAAOsF,UAAU,GAAG,IAAI,CAACL,0BAA0B,EAAE;EACvD;EAEAM,mCAAmCA,CAAA;IACjC,IAAI,CAAC/T,oBAAoB,CAACgU,aAAa,CAAC;MACtCC,cAAc,EAAE,uGAAuG;MACvHC,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE,eAAe;MACvBC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,aAAa,EAAE,aAAa;MAC5BC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,QAAQ;MACpBC,eAAe,EAAE,KAAK;MACtBC,WAAW,EAAE,wBAAwB;MACrCC,UAAU,EAAE;KACb,CAAC;IACF,IAAI,CAAC3U,oBAAoB,CAAC4U,IAAI,EAAE;EAClC;EAEAC,qCAAqCA,CAAA;IACnC,IAAI,CAAC7U,oBAAoB,CAACgU,aAAa,CAAC;MACtCC,cAAc,EAAE;;kEAE4C;MAC5DC,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE,eAAe;MACvBC,WAAW,EAAE,EAAE;MACfC,cAAc,EAAE,EAAE;MAClBC,aAAa,EAAE,aAAa;MAC5BC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,QAAQ;MACpBC,eAAe,EAAE,KAAK;MACtBC,WAAW,EAAE,wBAAwB;MACrCC,UAAU,EAAE;KACb,CAAC;IACF,IAAI,CAAC3U,oBAAoB,CAAC4U,IAAI,EAAE;EAClC;EAEQ/H,yBAAyBA,CAAA;IAC/B,IAAI,CAAC/J,gBAAgB,GAAG,IAAI,CAACvB,iBAAkB,CAACyP,OAAQ,CAAC8D,eAAgB;IACzEpQ,OAAO,CAACC,GAAG,CAAC,IAAI,CAACnD,oBAAoB,CAAC;EACxC;EAEA;;;;;EAKQoP,uCAAuCA,CAAC9P,cAA2B;IACzE,OAAOA,cAAc,CAACwH,MAAM,CAAEgE,SAAoB,IAAI;MACpD;MACA,IAAIA,SAAS,CAAClG,IAAI,KAAK,OAAO,EAAE;QAC9B;QACA,MAAM2O,qBAAqB,GAAGjU,cAAc,CAACwH,MAAM,CAAE0M,CAAC,IAAKA,CAAC,CAAChE,OAAQ,CAACiE,SAAS,KAAK3I,SAAS,CAAC0E,OAAQ,CAACiE,SAAS,CAAC;QAEjH;QACA,IAAIF,qBAAqB,CAACrI,MAAM,GAAG,CAAC,EAAE;UACpC,OAAO,KAAK,CAAC,CAAC;QAChB;MACF;MAEA,OAAO,IAAI,CAAC,CAAC;IACf,CAAC,CAAC;EACJ;CACD;AAngCUwI,UAAA,EAARhX,KAAK,EAAE,C,mDAA0B;AACzBgX,UAAA,EAARhX,KAAK,EAAE,C,uDAA8B;AAF3BqB,mBAAmB,GAAA2V,UAAA,EAd/BlX,SAAS,CAAC;EACTmX,QAAQ,EAAE,iBAAiB;EAC3BC,WAAW,EAAE,8BAA8B;EAC3CC,SAAS,EAAE,CAAC,8BAA8B,CAAC;EAC3CC,UAAU,EAAE,CAAC1W,WAAW,EAAEC,UAAU,CAAC;EACrC0W,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP1X,YAAY,EACZK,WAAW,EACXC,mBAAmB,EACnBe,cAAc,EACdC,2BAA2B,EAC3BF,gCAAgC;CACnC,CAAC,EAoEGuW,OAAA,KAAAxX,MAAM,CAACF,QAAQ,CAAC,E,EAnERwB,mBAAmB,CAogC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}