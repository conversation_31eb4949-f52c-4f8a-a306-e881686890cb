{"ast": null, "code": "import { take } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/notification.service\";\nimport * as i2 from \"@angular/common\";\nfunction NotificationComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.notification.description, \"\\n\");\n  }\n}\nexport class NotificationComponent {\n  constructor(notificationService) {\n    this.notificationService = notificationService;\n    this.notification = {};\n    this.showDescription = false;\n  }\n  ngOnInit() {\n    console.log(this.notification);\n  }\n  getNotificationColor(read) {\n    if (!read) {\n      return 'white';\n    } else {\n      return '#2d2a4b';\n    }\n  }\n  toggleNotification() {\n    this.showDescription = !this.showDescription;\n    this.notificationService.updateRead(this.notification.id).pipe(take(1)).subscribe(res => {\n      console.log(res);\n      this.notification.isRead = true;\n    });\n  }\n  static #_ = this.ɵfac = function NotificationComponent_Factory(t) {\n    return new (t || NotificationComponent)(i0.ɵɵdirectiveInject(i1.NotificationService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NotificationComponent,\n    selectors: [[\"app-notification\"]],\n    inputs: {\n      notification: \"notification\"\n    },\n    decls: 5,\n    vars: 4,\n    consts: [[1, \"inner\", 3, \"click\"], [1, \"lesson-circle\"], [2, \"width\", \"calc(100% - 30px)\"], [\"class\", \"description\", 4, \"ngIf\"], [1, \"description\"]],\n    template: function NotificationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵlistener(\"click\", function NotificationComponent_Template_div_click_0_listener() {\n          return ctx.toggleNotification();\n        });\n        i0.ɵɵelement(1, \"div\", 1);\n        i0.ɵɵelementStart(2, \"div\", 2);\n        i0.ɵɵtext(3);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(4, NotificationComponent_div_4_Template, 2, 1, \"div\", 3);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵstyleProp(\"background\", ctx.getNotificationColor(ctx.notification.isRead));\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\" \", ctx.notification.title, \" \");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showDescription);\n      }\n    },\n    dependencies: [i2.NgIf],\n    styles: [\".inner[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  flex-wrap: wrap;\\n}\\n.inner[_ngcontent-%COMP%]   .lesson-circle[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n  width: 10px;\\n  height: 10px;\\n  border-radius: 50%;\\n  border: 1px solid;\\n}\\n\\n.description[_ngcontent-%COMP%] {\\n  padding: 5px 25px;\\n  font-size: 15px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy91c2VyLXByb2ZpbGUvY29tcG9uZW50cy9ub3RpZmljYXRpb25zLXByb2ZpbGUvbm90aWZpY2F0aW9uL25vdGlmaWNhdGlvbi5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGFBQUE7RUFDQSxtQkFBQTtFQUNBLGVBQUE7QUFDSjtBQUFJO0VBQ0ksa0JBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsaUJBQUE7QUFFUjs7QUFDQTtFQUNJLGlCQUFBO0VBQ0EsZUFBQTtBQUVKIiwic291cmNlc0NvbnRlbnQiOlsiLmlubmVyIHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgZmxleC13cmFwOiB3cmFwO1xyXG4gICAgLmxlc3Nvbi1jaXJjbGUge1xyXG4gICAgICAgIG1hcmdpbi1yaWdodDogMTBweDtcclxuICAgICAgICB3aWR0aDogMTBweDtcclxuICAgICAgICBoZWlnaHQ6IDEwcHg7XHJcbiAgICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xyXG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkO1xyXG4gICAgfVxyXG59XHJcbi5kZXNjcmlwdGlvbiB7XHJcbiAgICBwYWRkaW5nOiA1cHggMjVweDtcclxuICAgIGZvbnQtc2l6ZTogMTVweDtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "names": ["take", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "notification", "description", "NotificationComponent", "constructor", "notificationService", "showDescription", "ngOnInit", "console", "log", "getNotificationColor", "read", "toggleNotification", "updateRead", "id", "pipe", "subscribe", "res", "isRead", "_", "ɵɵdirectiveInject", "i1", "NotificationService", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "NotificationComponent_Template", "rf", "ctx", "ɵɵlistener", "NotificationComponent_Template_div_click_0_listener", "ɵɵelement", "ɵɵtemplate", "NotificationComponent_div_4_Template", "ɵɵstyleProp", "title", "ɵɵproperty"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\user-profile\\components\\notifications-profile\\notification\\notification.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\user-profile\\components\\notifications-profile\\notification\\notification.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\r\nimport { take } from 'rxjs/operators';\r\nimport { Notification } from 'src/app/core/models/notification.model';\r\nimport { NotificationService } from 'src/app/core/services/notification.service';\r\n\r\n@Component({\r\n  selector: 'app-notification',\r\n  templateUrl: './notification.component.html',\r\n  styleUrls: ['./notification.component.scss']\r\n})\r\nexport class NotificationComponent implements OnInit {\r\n  @Input() notification: Notification = { } as Notification\r\n  showDescription: boolean = false;\r\n\r\n  constructor(\r\n    private notificationService: NotificationService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    console.log(this.notification)\r\n  }\r\n\r\n  getNotificationColor(read: boolean) {\r\n    if (!read) {\r\n      return 'white'\r\n    } else {\r\n      return '#2d2a4b'\r\n    }\r\n  }\r\n\r\n\r\n  toggleNotification() {\r\n    this.showDescription = !this.showDescription;\r\n    this.notificationService.updateRead(this.notification.id).pipe(take(1)).subscribe((res: any) => {\r\n      console.log(res);\r\n      this.notification.isRead = true;\r\n    })\r\n  }\r\n}\r\n", "<div class=\"inner\" (click)=\"toggleNotification()\">\r\n    <div class=\"lesson-circle\" [style.background]=\"getNotificationColor(notification.isRead)\"></div>\r\n    <div style=\"width: calc(100% - 30px);\">\r\n        {{notification.title}}\r\n    </div>\r\n</div>\r\n<div class=\"description\" *ngIf=\"showDescription\">\r\n    {{notification.description}}\r\n</div>"], "mappings": "AACA,SAASA,IAAI,QAAQ,gBAAgB;;;;;;ICKrCC,EAAA,CAAAC,cAAA,aAAiD;IAC7CD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,YAAA,CAAAC,WAAA,OACJ;;;ADEA,OAAM,MAAOC,qBAAqB;EAIhCC,YACUC,mBAAwC;IAAxC,KAAAA,mBAAmB,GAAnBA,mBAAmB;IAJpB,KAAAJ,YAAY,GAAiB,EAAmB;IACzD,KAAAK,eAAe,GAAY,KAAK;EAI5B;EAEJC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACR,YAAY,CAAC;EAChC;EAEAS,oBAAoBA,CAACC,IAAa;IAChC,IAAI,CAACA,IAAI,EAAE;MACT,OAAO,OAAO;IAChB,CAAC,MAAM;MACL,OAAO,SAAS;IAClB;EACF;EAGAC,kBAAkBA,CAAA;IAChB,IAAI,CAACN,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;IAC5C,IAAI,CAACD,mBAAmB,CAACQ,UAAU,CAAC,IAAI,CAACZ,YAAY,CAACa,EAAE,CAAC,CAACC,IAAI,CAACtB,IAAI,CAAC,CAAC,CAAC,CAAC,CAACuB,SAAS,CAAEC,GAAQ,IAAI;MAC7FT,OAAO,CAACC,GAAG,CAACQ,GAAG,CAAC;MAChB,IAAI,CAAChB,YAAY,CAACiB,MAAM,GAAG,IAAI;IACjC,CAAC,CAAC;EACJ;EAAC,QAAAC,CAAA,G;qBA3BUhB,qBAAqB,EAAAT,EAAA,CAAA0B,iBAAA,CAAAC,EAAA,CAAAC,mBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAArBpB,qBAAqB;IAAAqB,SAAA;IAAAC,MAAA;MAAAxB,YAAA;IAAA;IAAAyB,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCVlCrC,EAAA,CAAAC,cAAA,aAAkD;QAA/BD,EAAA,CAAAuC,UAAA,mBAAAC,oDAAA;UAAA,OAASF,GAAA,CAAApB,kBAAA,EAAoB;QAAA,EAAC;QAC7ClB,EAAA,CAAAyC,SAAA,aAAgG;QAChGzC,EAAA,CAAAC,cAAA,aAAuC;QACnCD,EAAA,CAAAE,MAAA,GACJ;QACJF,EADI,CAAAG,YAAA,EAAM,EACJ;QACNH,EAAA,CAAA0C,UAAA,IAAAC,oCAAA,iBAAiD;;;QALlB3C,EAAA,CAAAI,SAAA,EAA8D;QAA9DJ,EAAA,CAAA4C,WAAA,eAAAN,GAAA,CAAAtB,oBAAA,CAAAsB,GAAA,CAAA/B,YAAA,CAAAiB,MAAA,EAA8D;QAErFxB,EAAA,CAAAI,SAAA,GACJ;QADIJ,EAAA,CAAAK,kBAAA,MAAAiC,GAAA,CAAA/B,YAAA,CAAAsC,KAAA,MACJ;QAEsB7C,EAAA,CAAAI,SAAA,EAAqB;QAArBJ,EAAA,CAAA8C,UAAA,SAAAR,GAAA,CAAA1B,eAAA,CAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}