{"ast": null, "code": "export var StudentTrialStatus = /*#__PURE__*/function (StudentTrialStatus) {\n  StudentTrialStatus[\"NEW\"] = \"new\";\n  StudentTrialStatus[\"FORWARDED\"] = \"forwarded\";\n  StudentTrialStatus[\"ASSIGNED\"] = \"assigned\";\n  StudentTrialStatus[\"LOST\"] = \"lost\";\n  StudentTrialStatus[\"GALAXY\"] = \"galaxy\";\n  StudentTrialStatus[\"IGNORED\"] = \"ignored\";\n  StudentTrialStatus[\"HAS_PAYMENT\"] = \"hasPayment\";\n  StudentTrialStatus[\"NONE\"] = \"none\";\n  return StudentTrialStatus;\n}(StudentTrialStatus || {});", "map": {"version": 3, "names": ["StudentTrialStatus"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\models\\student.model.ts"], "sourcesContent": ["export interface RequestTrialRequest {\r\n  firstName: string;\r\n  lastName: string;\r\n  contactNumber: string;\r\n  email: string;\r\n  nativeLanguage: string;\r\n  wantToLearnLanguage: string;\r\n  country: string;\r\n  timeZone: string;\r\n  numberOfStudents: number;\r\n  findUs: string;\r\n  availability: string;\r\n  message: string;\r\n}\r\n\r\nexport enum StudentTrialStatus {\r\n  NEW = \"new\",\r\n  FORWARDED = \"forwarded\",\r\n  ASSIGNED = \"assigned\",\r\n  LOST = \"lost\",\r\n  GALAXY = \"galaxy\",\r\n  IGNORED = \"ignored\",\r\n  HAS_PAYMENT = \"hasPayment\",\r\n  NONE = \"none\"\r\n}\r\n\r\nexport interface ContactUsRequest {\r\n    userData: {\r\n      firstName: string,\r\n      lastName: string,\r\n      email: string,\r\n      phone: string\r\n    },\r\n    course: string,\r\n    studentType: string,\r\n    message: string\r\n}\r\n"], "mappings": "AAeA,WAAYA,kBASX,gBATD,UAAYA,kBAAkB;EAC5BA,kBAAA,eAAW;EACXA,kBAAA,2BAAuB;EACvBA,kBAAA,yBAAqB;EACrBA,kBAAA,iBAAa;EACbA,kBAAA,qBAAiB;EACjBA,kBAAA,uBAAmB;EACnBA,kBAAA,8BAA0B;EAC1BA,kBAAA,iBAAa;EAAA,OARHA,kBAAkB;AAS9B,CAAC,CATWA,kBAAkB,OAS7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}