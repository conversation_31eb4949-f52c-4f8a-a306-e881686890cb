{"ast": null, "code": "import { switchMap, take } from 'rxjs/operators';\nimport { UserRole } from 'src/app/core/models/user.model';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/dynamicdialog\";\nimport * as i2 from \"src/app/core/services/auth.service\";\nimport * as i3 from \"src/app/core/services/user.service\";\nimport * as i4 from \"src/app/core/services/general.service\";\nimport * as i5 from \"src/app/core/services/library.service\";\nimport * as i6 from \"src/app/core/services/toast.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"../../../../../shared/classrooms-check-list/classrooms-check-list.component\";\nimport * as i9 from \"../../../../../shared/upload-files/upload-files.component\";\nimport * as i10 from \"../../../../../shared/block-viewer/block-viewer.component\";\nimport * as i11 from \"primeng/button\";\nimport * as i12 from \"../../../../../shared/upload-files/choose-tags/choose-tags.component\";\nimport * as i13 from \"@angular/forms\";\nconst _c0 = a0 => ({\n  \"blue-teal\": a0\n});\nfunction LibraryFileActionsDialogComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4)(4, \"input\", 5);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LibraryFileActionsDialogComponent_ng_container_1_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.dialogData.libraryFile.name, $event) || (ctx_r1.dialogData.libraryFile.name = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 6)(6, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function LibraryFileActionsDialogComponent_ng_container_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDialogClose({\n        action: \"rename\",\n        libraryFile: ctx_r1.dialogData.libraryFile\n      }));\n    });\n    i0.ɵɵelement(7, \"span\", 8);\n    i0.ɵɵtext(8, \" Submit \");\n    i0.ɵɵelement(9, \"img\", 9);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.dialogData.libraryFile.name);\n  }\n}\nfunction LibraryFileActionsDialogComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h4\", 10);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"app-classrooms-check-list\", 11);\n    i0.ɵɵlistener(\"checkedList\", function LibraryFileActionsDialogComponent_ng_container_2_Template_app_classrooms_check_list_checkedList_3_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCheckedList($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 12)(5, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function LibraryFileActionsDialogComponent_ng_container_2_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSendSharedWith());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.dialogData.dialogsubTitle);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"preselectedClassroom\", ctx_r1.dialogData.preselectedClassroom)(\"showBlur\", true);\n  }\n}\nfunction LibraryFileActionsDialogComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h4\", 10);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"app-classrooms-check-list\", 14);\n    i0.ɵɵlistener(\"checkedList\", function LibraryFileActionsDialogComponent_ng_container_3_Template_app_classrooms_check_list_checkedList_3_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCheckedList($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 12)(5, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function LibraryFileActionsDialogComponent_ng_container_3_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSendSharedWith());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.dialogData.dialogsubTitle);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"showBlur\", true);\n  }\n}\nfunction LibraryFileActionsDialogComponent_ng_container_4_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 22);\n    i0.ɵɵlistener(\"click\", function LibraryFileActionsDialogComponent_ng_container_4_ng_container_7_Template_li_click_1_listener() {\n      const innerFolder_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.moveSelected(innerFolder_r7));\n    });\n    i0.ɵɵelementStart(2, \"div\", 23);\n    i0.ɵɵelement(3, \"i\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 25)(5, \"span\", 26);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(7, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const innerFolder_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵstyleProp(\"margin-left\", innerFolder_r7.marginLeft, \"px\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", innerFolder_r7.name, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c0, ctx_r1.selectedFolder === innerFolder_r7));\n  }\n}\nfunction LibraryFileActionsDialogComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h4\", 10);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 15)(4, \"input\", 16);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LibraryFileActionsDialogComponent_ng_container_4_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.term, $event) || (ctx_r1.term = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"i\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"ul\", 18);\n    i0.ɵɵtemplate(7, LibraryFileActionsDialogComponent_ng_container_4_ng_container_7_Template, 8, 6, \"ng-container\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 20)(9, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function LibraryFileActionsDialogComponent_ng_container_4_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onMoveSubmitted());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.dialogData.dialogsubTitle);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.term);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.dialogData.folders);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.selectedFolder);\n  }\n}\nfunction LibraryFileActionsDialogComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h4\", 10);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"app-upload-files\", 28);\n    i0.ɵɵlistener(\"onSubmit\", function LibraryFileActionsDialogComponent_ng_container_5_Template_app_upload_files_onSubmit_3_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onUploadedFilesSubmitted());\n    })(\"onUploadFinished\", function LibraryFileActionsDialogComponent_ng_container_5_Template_app_upload_files_onUploadFinished_3_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.uploadFinished($event));\n    })(\"tagChoosen\", function LibraryFileActionsDialogComponent_ng_container_5_Template_app_upload_files_tagChoosen_3_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTagChoosen($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.dialogData.dialogsubTitle);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"fileDropImage\", \"/assets/icons/library/upload-file-lib.svg\")(\"fileDropImageWidth\", 10)(\"isProfile\", false)(\"files\", ctx_r1.files)(\"teacherFilesLength\", ctx_r1.dialogData.libraryToAdd.libraryFiles.length)(\"dndUI\", true)(\"showSubmitButton\", true)(\"checkboxUI\", true);\n  }\n}\nfunction LibraryFileActionsDialogComponent_ng_container_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"img\", 35);\n    i0.ɵɵelementStart(2, \"label\", 36)(3, \"span\", 37);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"i\", 38);\n    i0.ɵɵlistener(\"click\", function LibraryFileActionsDialogComponent_ng_container_6_div_2_Template_i_click_5_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.showEditRename = !ctx_r1.showEditRename);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.getFileName(ctx_r1.modifiedFile.name));\n  }\n}\nfunction LibraryFileActionsDialogComponent_ng_container_6_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 39)(2, \"div\", 40)(3, \"div\", 4)(4, \"div\", 41);\n    i0.ɵɵelement(5, \"img\", 35);\n    i0.ɵɵelementStart(6, \"label\", 42)(7, \"input\", 43);\n    i0.ɵɵlistener(\"ngModelChange\", function LibraryFileActionsDialogComponent_ng_container_6_ng_container_3_Template_input_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.newFileName = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"i\", 38);\n    i0.ɵɵlistener(\"click\", function LibraryFileActionsDialogComponent_ng_container_6_ng_container_3_Template_i_click_8_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.showEditRename = !ctx_r1.showEditRename);\n    });\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.getFileName(ctx_r1.modifiedFile.name));\n  }\n}\nfunction LibraryFileActionsDialogComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 29);\n    i0.ɵɵtemplate(2, LibraryFileActionsDialogComponent_ng_container_6_div_2_Template, 6, 1, \"div\", 30)(3, LibraryFileActionsDialogComponent_ng_container_6_ng_container_3_Template, 9, 1, \"ng-container\", 1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 29)(5, \"app-choose-tags\", 31);\n    i0.ɵɵlistener(\"chooseTag\", function LibraryFileActionsDialogComponent_ng_container_6_Template_app_choose_tags_chooseTag_5_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onChooseTag($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 32)(7, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function LibraryFileActionsDialogComponent_ng_container_6_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTagsSubmitted());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.showEditRename);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showEditRename);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"applyToAll\", false)(\"libraryFile\", ctx_r1.dialogData.libraryFile);\n  }\n}\nexport let LibraryFileActionsDialogComponent = /*#__PURE__*/(() => {\n  class LibraryFileActionsDialogComponent {\n    constructor(ref, authService, userService, generalService, libraryService, toastService, config) {\n      this.ref = ref;\n      this.authService = authService;\n      this.userService = userService;\n      this.generalService = generalService;\n      this.libraryService = libraryService;\n      this.toastService = toastService;\n      this.config = config;\n      this.subs = new SubSink();\n      this.role = UserRole.NONE;\n      this.UserRoles = UserRole;\n      this.dialogData = {};\n      this.libraryToAdd = {};\n      this.files = [];\n      this.modifiedFile = {};\n      this.showEditRename = false;\n      this.newFileName = '';\n    }\n    ngOnInit() {\n      this.role = this.authService.getLoggedInUser().role;\n      this.dialogData = this.config.data.dialogData;\n      console.log(this.dialogData);\n      this.userService.setUploadFiles(true);\n      this.files = [];\n      if (this.dialogData.libraryFile) {\n        this.newFileName = this.dialogData.libraryFile.name;\n        this.modifiedFile = this.dialogData.libraryFile;\n      }\n      // this.sortFolders(this.dialogData.folders);\n      // console.log((this.sortFolders(this.dialogData.folders)))\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n      this.ref.destroy();\n    }\n    onDialogClose(data) {\n      console.log(data);\n      this.ref.close(data);\n      this.ref.destroy();\n    }\n    onCheckedList(event) {\n      console.log(event);\n      this.sharedWithMap = event;\n    }\n    onSendSharedWith() {\n      this.onDialogClose({\n        action: this.dialogData.action,\n        map: this.sharedWithMap\n      });\n    }\n    moveSelected(folder) {\n      this.selectedFolder = folder;\n      console.log(folder);\n    }\n    /**\n     * Executes when the move is submitted.\n     *\n     * @param {type} paramName - description of parameter\n     * @return {type} description of return value\n     */\n    onMoveSubmitted() {\n      console.log(this.selectedFolder);\n      this.onDialogClose({\n        action: this.dialogData.action,\n        libraryFile: this.dialogData.libraryFile,\n        folder: this.selectedFolder\n      });\n    }\n    /**\n     * Retrieves the parent folders from the dialogData object.\n     *\n     * @return {Array} An array containing objects representing the parent folders,\n     * their child folders, and the library files associated with each parent folder.\n     */\n    getParentFolders() {\n      const parentFolders = this.dialogData.folders.filter(f => !f.parent);\n      return parentFolders.map(parent => {\n        const childFolders = this.dialogData.folders.filter(f => f.parent === parent.name);\n        const libraryFiles = parent.libraryFiles;\n        return {\n          parent,\n          childFolders,\n          libraryFiles\n        };\n      }).concat(this.dialogData.folders.filter(f => f.parent));\n    }\n    /**\n     * Sorts an array of folders by separating them into parent folders and child folders,\n     * sorting the parent folders by name, and adding the child folders to their respective parent folders.\n     *\n     * @param {any[]} folders - The array of folders to be sorted.\n     * @return {any[]} - The sorted array of folders.\n     */\n    sortFolders(folders) {\n      const sortedFolders = [];\n      const parentFolders = [];\n      const childFolders = [];\n      // Separate parent folders and child folders\n      folders.forEach(folder => {\n        if (folder.parent === '') {\n          parentFolders.push(folder);\n        } else {\n          childFolders.push(folder);\n        }\n      });\n      // Sort parent folders by name\n      parentFolders.sort((a, b) => a.name.localeCompare(b.name));\n      // Add parent folders to sorted array\n      parentFolders.forEach(folder => {\n        sortedFolders.push(folder);\n        const children = childFolders.filter(child => child.parent === folder.name);\n        const sortedChildren = this.sortFolders(children);\n        sortedChildren.forEach(child => {\n          sortedFolders.push({\n            ...child,\n            marginLeft: 10\n          });\n        });\n      });\n      // Add child folders to sorted array\n      childFolders.forEach(folder => {\n        if (!sortedFolders.some(f => f.folderId === folder.folderId)) {\n          sortedFolders.push({\n            ...folder,\n            marginLeft: 10\n          });\n        }\n      });\n      return sortedFolders;\n    }\n    /**\n     * Uploads the finished event.\n     *\n     * @param {any} event - The event object.\n     * @return {void} This function does not return anything.\n     */\n    uploadFinished(event) {\n      this.files.push({\n        path: event.filePath.dbPath,\n        levels: [],\n        categories: [],\n        name: event.filePath.dbPath,\n        fileId: event.filePath.dbPath\n      });\n      console.log(this.files);\n    }\n    /**\n     * Handles the event when a tag is chosen.\n     *\n     * @param {any} event - The event object containing information about the tag selection.\n     */\n    onTagChoosen(event) {\n      let isLevel = event.isLevel;\n      if (event.checked) {\n        if (event.applyToAll) {\n          for (let file of this.files) {\n            let arrLibrary = isLevel ? file.levels : file.categories;\n            arrLibrary.push(event.tag);\n          }\n        } else {\n          let file = this.files[event.file.index];\n          let arrLibrary = isLevel ? file.levels : file.categories;\n          arrLibrary.push(event.tag);\n        }\n      } else {\n        if (event.applyToAll) {\n          for (let file of this.files) {\n            let arrLibrary = isLevel ? file.levels : file.categories;\n            let index2 = arrLibrary.findIndex(el => el == event.tag);\n            arrLibrary.splice(index2, 1);\n          }\n        } else {\n          let file = this.files[event.file.index];\n          let arrLibrary = isLevel ? file.levels : file.categories;\n          let index2 = arrLibrary.findIndex(el => el == event.tag);\n          arrLibrary.splice(index2, 1);\n        }\n      }\n    }\n    onUploadedFilesSubmitted() {\n      const params = {\n        action: this.dialogData.action,\n        libraryFiles: this.files,\n        folder: this.dialogData.folder\n      };\n      console.log(params);\n      this.onDialogClose(params);\n    }\n    /**\n     * Retrieves the file name from a given file path.\n     *\n     * @param {string} filePath - The file path.\n     * @return {string | undefined} The file name without extension if it exists, otherwise the entire file name.\n     */\n    getFileName(filePath) {\n      const fileNameWithPath = filePath.split('\\\\').pop();\n      const fileNameParts = fileNameWithPath.split('.');\n      if (fileNameParts.length > 1) {\n        const fileNameWithoutExtension = fileNameParts.slice(0, -1).join('.');\n        return fileNameWithoutExtension;\n      }\n      return fileNameWithPath;\n    }\n    /**\n     * A function that creates a deep copy of an object.\n     *\n     * @param {any} obj - The object to be deep copied.\n     * @return {any} The deep copy of the object.\n     */\n    deepCopy(obj) {\n      if (typeof obj !== 'object' || obj === null) {\n        return obj;\n      }\n      let copy;\n      if (obj instanceof Array) {\n        copy = [];\n        for (let i = 0; i < obj.length; i++) {\n          copy[i] = this.deepCopy(obj[i]);\n        }\n      } else {\n        copy = {};\n        for (const key in obj) {\n          if (obj.hasOwnProperty(key)) {\n            copy[key] = this.deepCopy(obj[key]);\n          }\n        }\n      }\n      return copy;\n    }\n    onChooseTag(checkbox) {\n      const libraryFile = this.deepCopy(this.modifiedFile);\n      if (checkbox.checked) {\n        if (checkbox.isCategory) {\n          // Update categories in libraryFile\n          libraryFile.categories.push(checkbox.tag);\n        }\n        if (checkbox.isLevel) {\n          // Update levels in libraryFile\n          libraryFile.levels.push(checkbox.tag);\n        }\n      } else {\n        if (checkbox.isCategory) {\n          // Remove category from libraryFile\n          libraryFile.categories = libraryFile.categories.filter(category => category !== checkbox.tag);\n        }\n        if (checkbox.isLevel) {\n          // Remove level from libraryFile\n          libraryFile.levels = libraryFile.levels.filter(level => level !== checkbox.tag);\n        }\n      }\n      this.modifiedFile = libraryFile;\n    }\n    onTagsSubmitted() {\n      const libraryFile = this.deepCopy(this.modifiedFile);\n      const newLevels = {\n        fileId: libraryFile.fileId,\n        levels: this.modifiedFile.levels\n      };\n      const newCategories = {\n        fileId: libraryFile.fileId,\n        categories: this.modifiedFile.categories\n      };\n      this.rename(this.modifiedFile.fileId, this.newFileName);\n      this.subs.add(this.libraryService.editFileLevels(newLevels).pipe(switchMap(res => {\n        console.log(res);\n        return this.libraryService.editFileCategories(newCategories);\n      })).subscribe(res => {\n        console.log(res);\n        const params = {\n          action: this.dialogData.action,\n          result: this.modifiedFile\n        };\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Library file saved successfully.'\n        });\n        console.log(this.modifiedFile);\n        this.onDialogClose(params);\n      }));\n    }\n    rename(fileId, newName) {\n      this.subs.add(this.libraryService.updateLibraryFileName(fileId, newName).pipe(take(1)).subscribe(res => {\n        this.modifiedFile.name = this.newFileName;\n      }));\n    }\n    static #_ = this.ɵfac = function LibraryFileActionsDialogComponent_Factory(t) {\n      return new (t || LibraryFileActionsDialogComponent)(i0.ɵɵdirectiveInject(i1.DynamicDialogRef), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i4.GeneralService), i0.ɵɵdirectiveInject(i5.LibraryService), i0.ɵɵdirectiveInject(i6.ToastService), i0.ɵɵdirectiveInject(i1.DynamicDialogConfig));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LibraryFileActionsDialogComponent,\n      selectors: [[\"app-library-file-actions-dialog\"]],\n      decls: 7,\n      vars: 10,\n      consts: [[\"headerBackgroundImage\", \"/assets/images/dashboard/calendar/calendar-dialog-lesson-info-arranged.png\", \"blockClass\", \"border-radius-bottom-10\", \"headerBlockClass\", \"py-1 border-round-lg bg-cover sticky top-0 z-5 \", \"containerClass\", \"block-gradient-reverse px-3 py-2 relative\", 3, \"closeDialogEvent\", \"header\", \"headerClass\", \"headerTextClass\", \"showCloseDialogIcon\"], [4, \"ngIf\"], [1, \"library-info\", \"grid\"], [1, \"w-full\", \"flex\", \"align-items-center\", \"justify-content-center\", \"py-2\"], [1, \"library-file-details\", \"py-1\", \"font-sm\", \"flex\", \"flex-column\"], [\"id\", \"fileName\", 1, \"input-element\", 3, \"ngModelChange\", \"ngModel\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"text-center\", \"mt-2\"], [\"pRipple\", \"\", \"pbutton\", \"\", \"label\", \"\", \"icon\", \"pi pi-discord\", 1, \"element\", \"ripple\", \"p-button-raised\", \"p-button-rounded\", \"white-space-nowrap\", \"p-button\", \"btn-gradient-blue\", \"p-component\", \"w-10rem\", \"font-lg\", \"flex\", \"justify-content-center\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"p-button-icon\", \"p-button-icon-right\"], [\"src\", \"/assets/icons/forward-arrow.svg\", \"width\", \"18\", 1, \"ml-2\"], [1, \"font-base\"], [3, \"checkedList\", \"preselectedClassroom\", \"showBlur\"], [1, \"py-2\", \"sticky\", \"bottom-0\", \"flex\", \"justify-content-center\", \"align-items-center\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"Ok\", \"icon\", \"pi pi-check\", \"iconPos\", \"right\", 1, \"p-button-sm\", \"p-button-raised\", \"p-button-rounded\", \"blue-teal\", \"mt-3\", 3, \"click\"], [3, \"checkedList\", \"showBlur\"], [1, \"p-input-icon-right\", \"w-full\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Search\", 1, \"h-2rem\", \"w-full\", \"input-blue\", \"gradient-blue\", \"rounded\", 3, \"ngModelChange\", \"ngModel\"], [1, \"pi\", \"pi-search\", \"text-primary\"], [1, \"p-0\", \"mx-0\", \"mt-2\", \"mb-1\", \"list-none\", \"overflow-y-scroll\", \"h-11rem\"], [4, \"ngFor\", \"ngForOf\"], [1, \"bg-white\", \"py-1\", \"sticky\", \"bottom-0\", \"flex\", \"justify-content-center\", \"align-items-center\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"Ok\", \"icon\", \"pi pi-check\", \"iconPos\", \"right\", 1, \"p-button-sm\", \"p-button-raised\", \"p-button-rounded\", \"blue-teal\", \"mt-3\", 3, \"click\", \"disabled\"], [1, \"flex\", \"align-items-center\", \"py-1\", \"pr-1\", \"border-bottom-1\", \"surface-border\", \"folder-hover\", \"pointer\", 3, \"click\"], [1, \"w-2rem\", \"h-1rem\", \"flex\", \"align-items-center\", \"justify-content-center\", \"mr-3\", \"flex-shrink-0\"], [1, \"pi\", \"pi\", \"pi-folder\", \"text-blue-500\"], [1, \"text-900\", \"line-height-3\"], [1, \"text-700\"], [1, \"circle\", \"ml-auto\", \"border-circle\", \"w-1rem\", \"h-1rem\", \"cursor-pointer\", 3, \"ngClass\"], [\"title\", \"Library\", \"postUrl\", \"/Upload/UploadLibraryFile\", 1, \"p-20\", 3, \"onSubmit\", \"onUploadFinished\", \"tagChoosen\", \"fileDropImage\", \"fileDropImageWidth\", \"isProfile\", \"files\", \"teacherFilesLength\", \"dndUI\", \"showSubmitButton\", \"checkboxUI\"], [1, \"bordered-tags\"], [\"class\", \"library-edit-title justify-content-center my-2\", 4, \"ngIf\"], [3, \"chooseTag\", \"applyToAll\", \"libraryFile\"], [1, \"py-1\", \"sticky\", \"bottom-0\", \"flex\", \"justify-content-center\", \"align-items-center\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"Save\", \"icon\", \"pi pi-check\", \"iconPos\", \"right\", 1, \"p-button-sm\", \"p-button-raised\", \"p-button-rounded\", \"blue-teal\", \"mt-3\", 3, \"click\"], [1, \"library-edit-title\", \"justify-content-center\", \"my-2\"], [\"src\", \"/assets/icons/library/surface1.svg\"], [1, \"font-sm\", \"text-800\", \"in-title\", \"p-2\", \"flex\", \"gap-3\"], [1, \"file-path-text\", \"w-15rem\"], [1, \"pi\", \"pi-pencil\", \"pointer\", 3, \"click\"], [1, \"library-info\"], [1, \"w-full\", \"flex\", \"align-items-center\", \"justify-content-center\", \"py-1\"], [1, \"library-edit-title\", \"justify-content-center\", \"align-items-center\"], [1, \"font-sm\", \"text-800\", \"in-title\", \"flex\", \"gap-3\", \"px-2\", \"align-items-center\"], [\"id\", \"fileName\", 1, \"input-element\", \"new-filename-input\", \"file-path-text\", \"w-15rem\", 3, \"ngModelChange\", \"ngModel\"]],\n      template: function LibraryFileActionsDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"app-block-viewer\", 0);\n          i0.ɵɵlistener(\"closeDialogEvent\", function LibraryFileActionsDialogComponent_Template_app_block_viewer_closeDialogEvent_0_listener() {\n            return ctx.onDialogClose();\n          });\n          i0.ɵɵtemplate(1, LibraryFileActionsDialogComponent_ng_container_1_Template, 10, 1, \"ng-container\", 1)(2, LibraryFileActionsDialogComponent_ng_container_2_Template, 6, 3, \"ng-container\", 1)(3, LibraryFileActionsDialogComponent_ng_container_3_Template, 6, 2, \"ng-container\", 1)(4, LibraryFileActionsDialogComponent_ng_container_4_Template, 10, 4, \"ng-container\", 1)(5, LibraryFileActionsDialogComponent_ng_container_5_Template, 4, 9, \"ng-container\", 1)(6, LibraryFileActionsDialogComponent_ng_container_6_Template, 8, 4, \"ng-container\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"header\", ctx.dialogData.dialogTitle ? ctx.dialogData.dialogTitle : ctx.dialogData.action)(\"headerClass\", \"justify-content-center my-0\")(\"headerTextClass\", \"font-base font-semibold justify-content-center capitalize\")(\"showCloseDialogIcon\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dialogData.action === \"rename\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dialogData.action === \"shareWith\" || ctx.dialogData.action === \"unShareWith\" || ctx.dialogData.action === \"send\" || ctx.dialogData.action === \"send-and-share\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dialogData.action === \"shareWithMultiple\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dialogData.action === \"move\" || ctx.dialogData.action === \"moveMultiple\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dialogData.action === \"upload\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dialogData.action === \"editFileLevelsCategories\");\n        }\n      },\n      dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i8.ClassroomsCheckListComponent, i9.UploadFilesComponent, i10.BlockViewerComponent, i11.ButtonDirective, i12.ChooseTagsComponent, i13.DefaultValueAccessor, i13.NgControlStatus, i13.NgModel],\n      styles: [\".folder-hover[_ngcontent-%COMP%]:hover{background:#a7c1f9;background:linear-gradient(90deg,#a7c1f9,#e4ecfd)}.circle[_ngcontent-%COMP%]{transition:background-color .2s ease-in-out}.circle.blue-teal[_ngcontent-%COMP%]{background-color:#5bb7d0}.circle[_ngcontent-%COMP%]:hover{background-color:#e2e8f0}.bordered-tags[_ngcontent-%COMP%]{border-radius:16px;border:1px solid #2E3D90;margin-top:15px;width:100%}.library-edit-title[_ngcontent-%COMP%]{display:flex;align-items:center;font-size:18px}.library-edit-title[_ngcontent-%COMP%]   .in-title[_ngcontent-%COMP%]{border-radius:20px;background:#fff}.library-edit-title[_ngcontent-%COMP%] > img[_ngcontent-%COMP%]{width:30px;margin-right:15px}.new-filename-input[_ngcontent-%COMP%]{border-radius:16px;height:36px;border:1px solid transparent;font-size:.85rem;color:var(--surface-800);padding-inline:0}.new-filename-input[_ngcontent-%COMP%]:focus{outline-color:transparent}\"]\n    });\n  }\n  return LibraryFileActionsDialogComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}