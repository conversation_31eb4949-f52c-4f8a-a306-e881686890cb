{"ast": null, "code": "import { CommonModule, DOCUMENT } from '@angular/common';\nimport { signal } from '@angular/core';\nimport { FormsModule, ReactiveFormsModule, UntypedFormControl, Validators } from '@angular/forms';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { RecaptchaModule } from 'ng-recaptcha';\nimport { ButtonModule } from 'primeng/button';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { forkJoin, of } from 'rxjs';\nimport { switchMap, take } from 'rxjs/operators';\nimport { UserRole, UserStatus } from 'src/app/core/models/user.model';\nimport { slideInOut, slideUpDownAnimation } from 'src/app/helpers/my-animations';\nimport { LoaderComponent } from 'src/app/shared/loader/loader.component';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/toast.service\";\nimport * as i2 from \"src/app/core/services/general.service\";\nimport * as i3 from \"src/app/core/services/chat.service\";\nimport * as i4 from \"src/app/core/services/auth.service\";\nimport * as i5 from \"src/app/core/services/user.service\";\nimport * as i6 from \"src/app/core/services/teacher-application.service\";\nimport * as i7 from \"src/app/core/services/student-trial.service\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"primeng/button\";\nimport * as i11 from \"primeng/checkbox\";\nimport * as i12 from \"@angular/forms\";\nimport * as i13 from \"ng-recaptcha\";\nconst _c0 = [\"lottieSignupEl\"];\nconst LoginComponent_Defer_2_DepsFn = () => [i9.NgClass, i9.NgIf, i10.ButtonDirective, i11.Checkbox, i12.ɵNgNoValidate, i12.DefaultValueAccessor, i12.NgControlStatus, i12.NgControlStatusGroup, i12.RequiredValidator, i12.PatternValidator, i12.EmailValidator, i12.NgModel, i12.NgForm, LoaderComponent, i13.RecaptchaComponent];\nconst _c1 = a0 => ({\n  \"right-panel-active\": a0\n});\nconst _c2 = (a0, a1, a2) => ({\n  \"teacher-bg\": a0,\n  \"student-bg\": a1,\n  \"disabled-link\": a2\n});\nfunction LoginComponent_Defer_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 35)(2, \"div\", 36);\n    i0.ɵɵelement(3, \"app-loader\", 37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"scale\", 1.6);\n  }\n}\nfunction LoginComponent_Defer_0_ng_container_12_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtext(1, \"Email is not valid.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_Defer_0_ng_container_12_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 62);\n    i0.ɵɵtext(2, \"Great job! Your password meets the requirements.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LoginComponent_Defer_0_ng_container_12_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 62);\n    i0.ɵɵtext(2, \"Password must contain at least one capital letter and one number\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LoginComponent_Defer_0_ng_container_12_ng_template_27_Template(rf, ctx) {}\nfunction LoginComponent_Defer_0_ng_container_12_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtext(1, \"Passwords do not match.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_Defer_0_ng_container_12_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64);\n    i0.ɵɵelement(2, \"button\", 65);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r2.registerButtonLabel)(\"icon\", ctx_r2.isRegisterLoading ? \"pi pi-spin pi-spinner\" : \"pi pi-arrow-right\")(\"ngClass\", i0.ɵɵpureFunction3(3, _c2, ctx_r2.registerAsTeacher, !ctx_r2.registerAsTeacher, ctx_r2.isRegisterLoading));\n  }\n}\nfunction LoginComponent_Defer_0_ng_container_12_ng_container_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction LoginComponent_Defer_0_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 38);\n    i0.ɵɵtext(2, \" Sign Up \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 39)(4, \"div\", 40)(5, \"div\", 41)(6, \"p-checkbox\", 42);\n    i0.ɵɵlistener(\"onChange\", function LoginComponent_Defer_0_ng_container_12_Template_p_checkbox_onChange_6_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onChange());\n    });\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Defer_0_ng_container_12_Template_p_checkbox_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedRole, $event) || (ctx_r2.selectedRole = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 40)(8, \"div\", 43)(9, \"p-checkbox\", 44);\n    i0.ɵɵlistener(\"onChange\", function LoginComponent_Defer_0_ng_container_12_Template_p_checkbox_onChange_9_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onChange());\n    });\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Defer_0_ng_container_12_Template_p_checkbox_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedRole, $event) || (ctx_r2.selectedRole = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(10, \"form\", 45, 1);\n    i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Defer_0_ng_container_12_Template_form_ngSubmit_10_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const signupForm_r4 = i0.ɵɵreference(11);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onSignup(signupForm_r4));\n    });\n    i0.ɵɵelementStart(12, \"div\", 46)(13, \"div\", 47);\n    i0.ɵɵtext(14, \"Email * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 48, 2);\n    i0.ɵɵtemplate(17, LoginComponent_Defer_0_ng_container_12_div_17_Template, 2, 0, \"div\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 46)(19, \"div\", 47);\n    i0.ɵɵtext(20, \"Password *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 50)(22, \"i\", 51);\n    i0.ɵɵlistener(\"click\", function LoginComponent_Defer_0_ng_container_12_Template_i_click_22_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.togglemyPasswordFieldType(\"password\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"input\", 52, 3);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Defer_0_ng_container_12_Template_input_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.password, $event) || (ctx_r2.password = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(25, LoginComponent_Defer_0_ng_container_12_div_25_Template, 3, 0, \"div\", 10)(26, LoginComponent_Defer_0_ng_container_12_div_26_Template, 3, 0, \"div\", 53)(27, LoginComponent_Defer_0_ng_container_12_ng_template_27_Template, 0, 0, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 46)(30, \"div\", 47);\n    i0.ɵɵtext(31, \"Confirm Password *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\", 50)(33, \"i\", 51);\n    i0.ɵɵlistener(\"click\", function LoginComponent_Defer_0_ng_container_12_Template_i_click_33_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.togglemyPasswordFieldType(\"confirm\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(34, \"input\", 54, 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(36, LoginComponent_Defer_0_ng_container_12_div_36_Template, 2, 0, \"div\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 55)(38, \"p-checkbox\", 56);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Defer_0_ng_container_12_Template_p_checkbox_ngModelChange_38_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.termsAgreed, $event) || (ctx_r2.termsAgreed = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"a\", 57);\n    i0.ɵɵtext(40, \"terms and conditions*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerStart(41);\n    i0.ɵɵelementStart(42, \"div\", 58)(43, \"re-captcha\", 59);\n    i0.ɵɵlistener(\"resolved\", function LoginComponent_Defer_0_ng_container_12_Template_re_captcha_resolved_43_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.captchaResolvedEvent($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(44, LoginComponent_Defer_0_ng_container_12_div_44_Template, 3, 7, \"div\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(45, LoginComponent_Defer_0_ng_container_12_ng_container_45_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const passwordInput_r5 = i0.ɵɵreference(24);\n    const templateName_r6 = i0.ɵɵreference(28);\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedRole);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedRole);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.emailError);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getKeyValueFieldType(\"password\") ? \"pi-eye\" : \"pi-eye-slash\");\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.password);\n    i0.ɵɵproperty(\"type\", ctx_r2.getKeyValueFieldType(\"password\") ? \"text\" : \"password\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", passwordInput_r5 && passwordInput_r5.valid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", passwordInput_r5.invalid)(\"ngIfElse\", templateName_r6);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getKeyValueFieldType(\"confirm\") ? \"pi-eye\" : \"pi-eye-slash\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"type\", ctx_r2.getKeyValueFieldType(\"confirm\") ? \"text\" : \"password\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.confirmError);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.termsAgreed);\n    i0.ɵɵproperty(\"value\", ctx_r2.termsAgreed);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedRole.length !== 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.registerAsTeacher && ctx_r2.selectedRole.length !== 0);\n  }\n}\nfunction LoginComponent_Defer_0_ng_container_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 35)(2, \"div\", 36);\n    i0.ɵɵelement(3, \"app-loader\", 37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"scale\", 1.6);\n  }\n}\nfunction LoginComponent_Defer_0_div_19_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtext(1, \"Email is not valid\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_Defer_0_div_19_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵtext(1, \" Don't have an account? \");\n    i0.ɵɵelementStart(2, \"div\", 77);\n    i0.ɵɵlistener(\"click\", function LoginComponent_Defer_0_div_19_div_20_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.switchView(true, true));\n    });\n    i0.ɵɵtext(3, \" Sign Up\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LoginComponent_Defer_0_div_19_form_25_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtext(1, \"Email is not valid\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_Defer_0_div_19_form_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 67, 6);\n    i0.ɵɵlistener(\"submit\", function LoginComponent_Defer_0_div_19_form_25_Template_form_submit_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const forgotForm_r11 = i0.ɵɵreference(1);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onForgot(forgotForm_r11));\n    });\n    i0.ɵɵelementStart(2, \"div\", 46)(3, \"div\", 47);\n    i0.ɵɵtext(4, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 62);\n    i0.ɵɵtext(6, \"Please enter your email address to receive the password reset instructions. If you don\\u2019t see the email in your inbox, please check your spam or junk folder.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"input\", 68, 7);\n    i0.ɵɵtemplate(9, LoginComponent_Defer_0_div_19_form_25_div_9_Template, 2, 0, \"div\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 78);\n    i0.ɵɵelement(11, \"button\", 79);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.emailError);\n  }\n}\nfunction LoginComponent_Defer_0_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"div\", 39);\n    i0.ɵɵelement(2, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"form\", 67, 5);\n    i0.ɵɵlistener(\"submit\", function LoginComponent_Defer_0_div_19_Template_form_submit_3_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const loginForm_r8 = i0.ɵɵreference(4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onLogin(loginForm_r8));\n    });\n    i0.ɵɵelementStart(5, \"div\", 46)(6, \"div\", 47);\n    i0.ɵɵtext(7, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"input\", 68, 2);\n    i0.ɵɵtemplate(10, LoginComponent_Defer_0_div_19_div_10_Template, 2, 0, \"div\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 46)(12, \"div\", 47);\n    i0.ɵɵtext(13, \"Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 50)(15, \"i\", 51);\n    i0.ɵɵlistener(\"click\", function LoginComponent_Defer_0_div_19_Template_i_click_15_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.togglemyPasswordFieldType(\"email\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"input\", 69, 3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 70);\n    i0.ɵɵelement(19, \"button\", 71);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(20, LoginComponent_Defer_0_div_19_div_20_Template, 4, 0, \"div\", 72);\n    i0.ɵɵelementStart(21, \"div\", 73);\n    i0.ɵɵlistener(\"click\", function LoginComponent_Defer_0_div_19_Template_div_click_21_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.showForgotPassword = !ctx_r2.showForgotPassword);\n    });\n    i0.ɵɵtext(22, \" Forgot password? \");\n    i0.ɵɵelementStart(23, \"div\", 74);\n    i0.ɵɵtext(24, \" Get it here\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(25, LoginComponent_Defer_0_div_19_form_25_Template, 12, 1, \"form\", 75);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.emailError);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getKeyValueFieldType(\"email\") ? \"pi-eye\" : \"pi-eye-slash\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"type\", ctx_r2.getKeyValueFieldType(\"email\") ? \"text\" : \"password\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.is1024);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showForgotPassword);\n  }\n}\nfunction LoginComponent_Defer_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9);\n    i0.ɵɵtemplate(2, LoginComponent_Defer_0_ng_container_2_Template, 4, 1, \"ng-container\", 10);\n    i0.ɵɵelementStart(3, \"div\", 11)(4, \"div\", 12)(5, \"div\", 13)(6, \"div\", 14);\n    i0.ɵɵelement(7, \"div\", null, 0);\n    i0.ɵɵelementStart(9, \"div\", 15)(10, \"span\", 16);\n    i0.ɵɵtext(11, \"You have successfully registered an account!\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵtemplate(12, LoginComponent_Defer_0_ng_container_12_Template, 46, 16, \"ng-container\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 17)(14, \"div\", 18)(15, \"div\", 19)(16, \"span\", 20);\n    i0.ɵɵtext(17, \" Sign In \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(18, LoginComponent_Defer_0_ng_container_18_Template, 4, 1, \"ng-container\", 10)(19, LoginComponent_Defer_0_div_19_Template, 26, 5, \"div\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 22)(21, \"div\", 23)(22, \"div\", 24)(23, \"div\", 25)(24, \"a\", 26);\n    i0.ɵɵelement(25, \"img\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function LoginComponent_Defer_0_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.handleSwitchViewClick());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\")(28, \"h1\", 29);\n    i0.ɵɵtext(29, \"Welcome!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"h2\", 30)(31, \"a\", 31);\n    i0.ɵɵlistener(\"click\", function LoginComponent_Defer_0_Template_a_click_31_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.handleSwitchViewClick());\n    });\n    i0.ɵɵtext(32, \"Sign Up if you don't have an account\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(33, \"div\", 32)(34, \"div\", 25)(35, \"a\", 26);\n    i0.ɵɵelement(36, \"img\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 33)(38, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function LoginComponent_Defer_0_Template_button_click_38_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.handleSwitchViewClick());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\")(40, \"a\", 34);\n    i0.ɵɵlistener(\"click\", function LoginComponent_Defer_0_Template_a_click_40_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.handleSwitchViewClick());\n    });\n    i0.ɵɵtext(41, \"Sign-In if you are already a member\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(42, \"div\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c1, ctx_r2.loginIsOpen));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"hidden\", ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"hidden\", !ctx_r2.showSuccessRegistrationMsg);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.showSuccessRegistrationMsg);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoading);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"label\", \"Sign Up\");\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"label\", \"Sign In\");\n  }\n}\nfunction LoginComponent_DeferPlaceholder_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36);\n    i0.ɵɵelement(2, \"app-loader\", 37);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scale\", 1.6);\n  }\n}\nexport class LoginComponent {\n  constructor(toastService, generalService, chatService, authService, userService, teacherApplicationService, studentTrialService, router, zone, document, renderer, location, activatedRoute) {\n    this.toastService = toastService;\n    this.generalService = generalService;\n    this.chatService = chatService;\n    this.authService = authService;\n    this.userService = userService;\n    this.teacherApplicationService = teacherApplicationService;\n    this.studentTrialService = studentTrialService;\n    this.router = router;\n    this.zone = zone;\n    this.document = document;\n    this.renderer = renderer;\n    this.location = location;\n    this.activatedRoute = activatedRoute;\n    this.subs = new SubSink();\n    this.isLoading = false;\n    this.isRegisterLoading = false;\n    this.termsAgreed = false;\n    this.loginIsOpen = true;\n    this.signUpIsOpen = false;\n    this.registerAsTeacher = false;\n    this.is768 = false;\n    this.is1024 = false;\n    this.is1366 = false;\n    this.is1024up = false;\n    this.isBig = false;\n    this.emailError = false;\n    this.confirmError = false;\n    this.showSuccessRegistrationMsg = false;\n    this.showForgotPassword = false;\n    this.selectedRole = [];\n    this.registerButtonLabel = 'Register as Student';\n    this.ismyTextFieldType = {\n      email: false,\n      password: false,\n      confirm: false\n    };\n    this.passwordCheck = '';\n    this.passwordFormControl = new UntypedFormControl('', [Validators.required, Validators.pattern('^(?=.*[A-Z])(?=.*[0-9]).*$')]);\n    this.isDisabled = true;\n    this.hasAnimated = true;\n    this.captchaResolved = signal(false);\n    this.lottieSignupEl = {};\n    this.mwindow = this.document.defaultView;\n    console.log(activatedRoute.firstChild);\n    // try to switchView on register/login route detection\n  }\n  ngOnInit() {\n    // get return url from route parameters or default to ‘/’\n    this.returnUrl = this.activatedRoute.snapshot.queryParams['returnUrl'] || undefined;\n    if (this.router.url.includes('login')) {\n      this.loginIsOpen = true;\n    }\n    if (this.router.url.includes('register')) {\n      this.loginIsOpen = false;\n    }\n    this.subs.add(this.authService.getAuthStatusListener().subscribe(authStatus => {\n      if (authStatus) {\n        // this.isLoading = false;\n      }\n    }));\n    this.subs.add(this.generalService.deviceKind.pipe(take(1)).subscribe(res => {\n      if (res) {\n        this.is1024 = res.is1024;\n        this.is1366 = res.is1366;\n        this.is768 = res.is768;\n        this.isBig = res.isBig;\n        this.is1024up = res.w1024up;\n        if (this.is1024up) {\n          // this.renderer.addClass(this.document.getElementsByClassName('app')[0], 'h-full');\n        }\n        console.log(res);\n      }\n    }));\n    this.registrationSuccessMessageListener();\n    // this.renderer.addClass(this.document.body, 'gradient-moving-body');\n  }\n  executeRecaptchaV3() {}\n  loadScript(url) {\n    console.log('preparing to load...');\n    let node = document.createElement('script');\n    node.setAttribute(\"id\", \"lottie-player\");\n    node.src = url;\n    node.type = 'text/javascript';\n    node.async = true;\n    node.charset = 'utf-8';\n    document.getElementsByTagName('head')[0].appendChild(node);\n  }\n  ngAfterViewInit() {}\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n    // this.renderer.removeClass(this.document.body, 'height-screen');\n    // this.renderer.removeClass(this.document.getElementsByClassName('app')[0], 'h-full');\n  }\n  registrationSuccessMessageListener() {\n    this.subs.add(this.authService.showRegistrationSuccessfullMessage.subscribe(res => {\n      console.log(res);\n      this.showSuccessRegistrationMsg = res;\n      if (res) {\n        this.addLottiePlayerForSignupCompleted(this.lottieSignupEl);\n      }\n    }));\n  }\n  checkBoxChange(args) {\n    this.registerAsTeacher = args.checked;\n  }\n  switchView(animate = false, changeHasAnimated = false) {\n    this.showSuccessRegistrationMsg = false;\n    if (changeHasAnimated) {\n      this.hasAnimated = changeHasAnimated;\n    }\n    if (this.isBig || this.is1366 || animate) {\n      if (this.loginIsOpen) {\n        if (this.is1024up) {\n          const elem1 = document.getElementById('left-side');\n          const elem2 = document.getElementById('right-side');\n          // const rect = elem2!.getBoundingClientRect();\n          // const parent = elem2!.parentElement;\n          // const leftOffset = elem2!.offsetLeft;\n          // const topOffset = elem1!.offsetTop;\n          // const leftWidth = elem2!.offsetWidth;\n          // const newLeft2 = rect.left - parent!.offsetLeft;\n          // const newLeft = newLeft2 + leftWidth;\n          // elem1!.style.left = newLeft - 2 + 'px';\n          document.getElementById('left-side').style.left = '470px';\n          document.getElementById('left-side').style.borderRadius = '0px 28px 28px 0';\n          document.getElementById('right-side').style.borderRadius = '28px 0px 0 28px';\n          document.getElementById('right-side').style.right = '270px';\n        }\n        const url = this.router.createUrlTree(['/auth/register'], {\n          relativeTo: this.activatedRoute\n        }).toString();\n        this.location.go(url);\n      } else {\n        if (this.is1024up) {\n          document.getElementById('left-side').style.left = '0';\n          document.getElementById('left-side').style.borderRadius = '28px 0px 0 28px';\n          document.getElementById('right-side').style.borderRadius = '0px 28px 28px 0';\n          document.getElementById('right-side').style.right = '0';\n        }\n        const url = this.router.createUrlTree(['/auth/login'], {\n          relativeTo: this.activatedRoute\n        }).toString();\n        this.location.go(url);\n      }\n    }\n    this.loginIsOpen = !this.loginIsOpen;\n    this.signUpIsOpen = !this.signUpIsOpen;\n  }\n  onChange() {\n    const selectedRole = this.selectedRole[this.selectedRole.length - 1];\n    this.selectedRole.length = 0;\n    this.selectedRole.push(selectedRole);\n    if (!selectedRole) {\n      this.selectedRole.length = 0;\n    }\n    this.detectRole();\n    this.isDisabled = false;\n  }\n  detectRole() {\n    if (this.selectedRole[0] === 'teacher') {\n      this.registerAsTeacher = true;\n      this.registerButtonLabel = 'Register as Teacher';\n    } else {\n      this.registerAsTeacher = false;\n      this.registerButtonLabel = 'Register as Student';\n    }\n  }\n  removeSpaces(control) {\n    if (control && control.value && !control.value.replace(/\\s/g, '').length) {\n      control.setValue('');\n    }\n    return null;\n  }\n  setValidatorsForTrimming(form, controlName) {\n    const control = form.controls[controlName];\n    control.setValue(control.value.trim());\n    control.setValidators([Validators.required, this.removeSpaces]);\n    control.updateValueAndValidity();\n  }\n  onLogin(form) {\n    this.setValidatorsForTrimming(form, 'email');\n    this.setValidatorsForTrimming(form, 'password');\n    this.emailError = this.validEmail(form);\n    const loginForm = form.value;\n    console.log(form.value);\n    if (form.invalid) {\n      this.toastService.setShowToastmessage({\n        severity: 'warn',\n        summary: '',\n        detail: 'Please enter email and password to continue.'\n      });\n      return;\n    }\n    this.isLoading = true;\n    let expiresInDuration = 10800;\n    let token = null;\n    let tokenInfo = null;\n    const result1$ = this.subs.add(this.authService.login(form.value.email.trim(), form.value.password.trim()).pipe(switchMap(response => {\n      token = response.token;\n      tokenInfo = this.authService.getDecodedAccessToken(token); // decode token\n      this.authService.setToken(token);\n      if (token) {\n        expiresInDuration = 10800;\n        // const expiresInDuration = tokenInfo.exp;\n        this.authService.setAuthTimer(expiresInDuration);\n        this.userId = response.id;\n        this.user = response;\n        if (tokenInfo.role === UserRole.TEACHER) {\n          return this.requestDataFromMultipleSources(response);\n          // return this.userService.getUserAvailability(response.id);\n        } else {\n          this.authService.authStatusListener.next(true);\n          const now = new Date();\n          const expirationDate = new Date(now.getTime() + expiresInDuration * 1000);\n          this.authService.saveAuthData(token, expirationDate, this.userId, this.user, tokenInfo.role);\n          // this.router.navigate(['/dashboard']);\n          return this.requestStudentTrialData();\n        }\n      } else {\n        return of(null);\n      }\n    })).subscribe(res => {\n      console.log(loginForm);\n      let u = {};\n      // TODO: check if below is needed for Firebase integration\n      // this.existsFirebaseUserEmail(loginForm);\n      if (tokenInfo.role === UserRole.TEACHER) {\n        let u = {\n          ...this.user,\n          availability: res[0],\n          off: res[1]\n        };\n      } else {\n        console.log(res);\n        // for student\n        this.studentTrialService.setTrialRequested(res[0]);\n        // this.user = this.user;\n      }\n      // this.user.role = 'trial'\n      this.authService.authStatusListener.next(true);\n      const now = new Date();\n      const expirationDate = new Date(now.getTime() + expiresInDuration * 1000);\n      this.authService.setIsAuthenticated(true);\n      this.authService.saveAuthData(token, expirationDate, this.userId, this.user, tokenInfo.role);\n      this.isLoading = false;\n      // checking teacher application status steps if all are false (means the user hasnt started filling forms yet)\n      if (tokenInfo.role.toLowerCase() === UserRole.TEACHER.toLowerCase()) {\n        // teacher is active\n        if (this.user?.status.toLowerCase() === UserStatus.ACTIVE.toLowerCase()) {\n          if (this.returnUrl) {\n            this.router.navigate([this.returnUrl]);\n          } else {\n            this.router.navigateByUrl('/dashboard');\n          }\n          return false;\n        }\n        const valuesArray = Object.values(res[2]);\n        const allFalse = valuesArray.every(value => value === false);\n        console.log(allFalse);\n        if (allFalse) {\n          this.router.navigateByUrl('/teacher/info');\n          return false;\n        } else {\n          this.router.navigateByUrl(this.teacherApplicationService.getTeacherStatusNavigationUrl(res[1]));\n          return false;\n        }\n      }\n      // student\n      if (this.returnUrl) {\n        this.router.navigate([this.returnUrl]);\n      } else {\n        this.router.navigateByUrl('/dashboard');\n      }\n      return false;\n    }, error => {\n      this.isLoading = false;\n    }));\n  }\n  onForgot(form) {\n    this.emailError = this.validEmail(form);\n    if (form.invalid) {\n      this.toastService.setShowToastmessage({\n        severity: 'warn',\n        summary: '',\n        detail: 'Please enter email to continue.'\n      });\n      return;\n    }\n    this.toastService.setShowToastmessage({\n      severity: 'success',\n      summary: '',\n      detail: 'Please check your email to reset your password.'\n    });\n    this.authService.forgot(form.value.email);\n    form.reset();\n  }\n  onSignup(form) {\n    let endPointType = this.registerAsTeacher ? 'RegisterTeacher' : 'RegisterStudent';\n    this.emailError = this.validEmail(form);\n    this.confirmError = this.confirmPassword(form.value.password, form.value.confirm);\n    let invalidInputs = '';\n    for (const controlName in form.controls) {\n      if (form.controls.hasOwnProperty(controlName)) {\n        const control = form.controls[controlName];\n        if (control.invalid) {\n          invalidInputs += `${controlName}, `;\n        }\n      }\n    }\n    let detailMessage = 'Please check all required fields to continue. \\n\\n';\n    if (invalidInputs !== '') {\n      invalidInputs = invalidInputs.slice(0, -2); // Remove the trailing comma and space\n      if (invalidInputs !== '') {\n        detailMessage += ` Invalid input for the following fields: ${invalidInputs}`;\n      }\n    }\n    if (!this.termsAgreed) {\n      detailMessage += ` Please agree to our terms & conditions.`;\n    }\n    if (!this.captchaResolved()) {\n      detailMessage += ` Please resolve the captcha to continue.`;\n    }\n    if (form.invalid || !this.termsAgreed || this.confirmError || !this.captchaResolved()) {\n      this.toastService.setShowToastmessage({\n        severity: 'warn',\n        summary: '',\n        detail: detailMessage\n      });\n      return;\n    }\n    this.isRegisterLoading = true;\n    this.authService.createUser(form.value.email, form.value.password, endPointType).subscribe(res => {\n      this.authService.setShowRegistrationSuccessfullMessage(true);\n      this.isRegisterLoading = false;\n    }, error => {\n      this.isRegisterLoading = false;\n      this.authService.authStatusListener.next(false);\n      form.reset();\n    });\n  }\n  onTeacherButtonClicked() {\n    this.registerAsTeacher = true;\n  }\n  onStudentButtonClicked() {\n    this.registerAsTeacher = false;\n  }\n  validEmail(form) {\n    if (form.value.email) {\n      let validEmail = form.value.email.trim().toLowerCase().match(/^\\S+@\\S+\\.\\S+$/);\n      if (validEmail === null && form.touched) {\n        return true;\n      }\n    }\n    return false;\n  }\n  confirmPassword(password, confirm) {\n    return password !== confirm;\n  }\n  //TODO GOOGLE LOGIN\n  // public externalLogin = () => {\n  //   this.authService.signInWithGoogle()\n  //   .then(res => {\n  //     const user: SocialUser = { ...res };\n  //     console.log(user);\n  //   }, error => console.log(error))\n  // }\n  togglemyPasswordFieldType(key) {\n    this.ismyTextFieldType[key] = !this.ismyTextFieldType[key];\n  }\n  getKeyValueFieldType(key) {\n    return this.ismyTextFieldType[key];\n  }\n  requestDataFromMultipleSources(response) {\n    let response1 = this.userService.getUserAvailability(response.id);\n    let response2 = this.authService.getTeacherStatus();\n    let response3 = this.teacherApplicationService.getTeacherApplicationStepsStatus();\n    // Observable.forkJoin (RxJS 5) changes to just forkJoin() in RxJS 6\n    return forkJoin([response1, response2, response3]);\n  }\n  requestStudentTrialData() {\n    let response2 = this.studentTrialService.getStudentHasTrialRequest();\n    // Observable.forkJoin (RxJS 5) changes to just forkJoin() in RxJS 6\n    return forkJoin([response2]);\n  }\n  /**\n   * Loads LottiePlayer library from the CDN and shows success registration message.\n   */\n  loadLottiePlayer() {\n    const _this = this;\n    const successMessageDelay = 10; // in milliseconds\n    const lottiePlayerCDN = '/assets/js/lottie-player.js';\n    setTimeout(() => {\n      var id = document.getElementById('lottie-player');\n      if (!id) {\n        _this.loadScript(lottiePlayerCDN);\n      }\n      _this.showSuccessRegistrationMsg = true;\n    }, successMessageDelay);\n  }\n  handleSwitchViewClick() {\n    const container = document.getElementById('container');\n    container.classList.toggle(\"right-panel-active\");\n    if (!container.classList.contains(\"right-panel-active\")) {\n      const url = this.router.createUrlTree(['/auth/register'], {\n        relativeTo: this.activatedRoute\n      }).toString();\n      this.location.go(url);\n    } else {\n      const url = this.router.createUrlTree(['/auth/login'], {\n        relativeTo: this.activatedRoute\n      }).toString();\n      this.location.go(url);\n    }\n  }\n  captchaResolvedEvent(captchaResponse) {\n    console.log(`Resolved captcha with response: ${captchaResponse}`);\n    this.captchaResolved.set(true);\n  }\n  addLottiePlayerForSignupCompleted(el) {\n    const lottiePlayer = `<lottie-player src=\"https://assets4.lottiefiles.com/packages/lf20_xwmj0hsk.json\"  \n    background=\"transparent\"  speed=\"1\"  style=\"width: 280px; height: 280px; margin:0 auto;\"  autoplay></lottie-player>`;\n    el.nativeElement.innerHTML = lottiePlayer;\n  }\n  static #_ = this.ɵfac = function LoginComponent_Factory(t) {\n    return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.ToastService), i0.ɵɵdirectiveInject(i2.GeneralService), i0.ɵɵdirectiveInject(i3.ChatService), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i5.UserService), i0.ɵɵdirectiveInject(i6.TeacherApplicationService), i0.ɵɵdirectiveInject(i7.StudentTrialService), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i9.Location), i0.ɵɵdirectiveInject(i8.ActivatedRoute));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LoginComponent,\n    selectors: [[\"app-login\"]],\n    viewQuery: function LoginComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.lottieSignupEl = _t.first);\n      }\n    },\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 4,\n    vars: 0,\n    consts: [[\"lottieSignupEl\", \"\"], [\"signupForm\", \"ngForm\"], [\"emailInput\", \"ngModel\"], [\"passwordInput\", \"ngModel\"], [\"templateName\", \"\"], [\"loginForm\", \"ngForm\"], [\"forgotForm\", \"ngForm\"], [\"emailInputForgot\", \"ngModel\"], [\"id\", \"container\", 1, \"login-container\", \"w-100\", 3, \"ngClass\"], [1, \"form-container\", \"sign-up-container\"], [4, \"ngIf\"], [1, \"auth-form\", \"mx-3\", \"sm:mx-5\", \"md:mx-6\", \"lg:mx-7\", 3, \"hidden\"], [3, \"hidden\"], [1, \"surface-section\", \"px-4\", \"py-1\", \"md:px-2\", \"lg:px-2\", \"lg:mt-8\", \"text-center\"], [1, \"text-700\", \"mb-6\"], [1, \"mb-4\", \"font-bold\", \"text-2xl\"], [1, \"text-primary\", \"line-height-2\"], [1, \"form-container\", \"sign-in-container\"], [1, \"relative\", \"h-full\"], [1, \"title\", \"my-3\", \"md:my-4\", \"pb-3\", \"mx-3\", \"sm:mx-5\", \"md:mx-6\", \"lg:mx-7\", \"align-items-start\"], [1, \"text-center\", \"sm:text-left\"], [\"class\", \"auth-form  flex flex-column mx-3 sm:mx-5 md:mx-6 lg:mx-7\", 4, \"ngIf\"], [1, \"overlay-container\"], [1, \"overlay\"], [1, \"overlay-panel\", \"overlay-left\", \"left-side-content\", \"justify-content-between\", \"gap-2\", \"py-5\"], [1, \"logo\", \"my-3\", \"md:my-0\"], [\"href\", \"https://mylingotrip.com\", \"target\", \"_blank\"], [\"src\", \"/assets/icons/logo/mlt_logo-new.svg\", \"width\", \"100\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-outlined\", \"auth-btn\", 3, \"click\", \"label\"], [1, \"title\", \"text-center\", \"line-height-1\"], [1, \"m-0\"], [1, \"fl-typo\", \"s12-16\", \"cursor-pointer\", \"hover:underline\", \"mt-2\", \"block\", 3, \"click\"], [1, \"overlay-panel\", \"overlay-right\", \"left-side-content\", \"justify-content-between\", \"gap-2\", \"py-5\"], [1, \"flex\", \"gap-3\", \"flex-column\"], [1, \"cursor-pointer\", \"hover:underline\", 3, \"click\"], [1, \"h-17rem\", \"md:h-auto\"], [1, \"abs-centered\"], [3, \"scale\"], [1, \"title\", \"my-3\", \"sm:my-2\", \"md:my-4\", \"text-center\", \"sm:text-left\"], [1, \"grid\"], [1, \"col-6\"], [1, \"checkbox-bg\", \"student-bg\"], [\"name\", \"group1\", \"value\", \"student\", \"label\", \"Register as Student\", \"inputId\", \"sf\", 1, \"teacher-checkbox\", 3, \"onChange\", \"ngModelChange\", \"ngModel\"], [1, \"checkbox-bg\", \"teacher-bg\"], [\"styleClass\", \"teacher-checkbox-inner\", \"name\", \"group1\", \"value\", \"teacher\", \"label\", \"Register as Teacher\", \"inputId\", \"ny\", 1, \"teacher-checkbox\", 3, \"onChange\", \"ngModelChange\", \"ngModel\"], [\"autocomplete\", \"off\", 3, \"ngSubmit\"], [1, \"input-field\"], [1, \"input-element-title\"], [\"name\", \"email\", \"ngModel\", \"\", \"type\", \"email\", \"placeholder\", \"Email\", \"required\", \"\", \"email\", \"\", \"autocomplete\", \"off\", 1, \"input-element\"], [\"class\", \"input-error\", 4, \"ngIf\"], [1, \"p-input-icon-right\", 2, \"width\", \"100%\"], [1, \"pi\", 2, \"margin-top\", \"-2px\", 3, \"click\", \"ngClass\"], [\"name\", \"password\", \"placeholder\", \"Password\", \"required\", \"\", \"autocomplete\", \"off\", \"pattern\", \"^(?=.*[A-Z])(?=.*[0-9]).*$\", 1, \"input-element\", 3, \"ngModelChange\", \"ngModel\", \"type\"], [4, \"ngIf\", \"ngIfElse\"], [\"name\", \"confirm\", \"ngModel\", \"\", \"type\", \"password\", \"placeholder\", \"Confirm Password\", \"required\", \"\", \"autocomplete\", \"off\", \"pattern\", \"^(?=.*[A-Z])(?=.*[0-9]).*$\", 1, \"input-element\", 3, \"type\"], [1, \"review-section\", \"flex\", \"align-items-center\", \"gap-1\", 2, \"font-size\", \"15px\", \"margin\", \"20px 0\", \"font-weight\", \"bold\"], [\"id\", \"termsCheckbox\", \"label\", \"I have read and agree to the website \", \"name\", \"groupname\", \"binary\", \"true\", 1, \"terms-checkbox\", 3, \"ngModelChange\", \"ngModel\", \"value\"], [\"href\", \"https://mylingotrip.com/terms-conditions/\", \"target\", \"_blank\", 2, \"color\", \"black\", \"font-size\", \"16px\"], [1, \"grid\", \"align-items-center\", \"justify-content-center\", \"w-full\", \"mx-auto\", \"mt-3\"], [\"siteKey\", \"6LdFlA4pAAAAAJzrvvdou58XS_zQQ94z0lwxMv8i\", 3, \"resolved\"], [\"class\", \"grid justify-content-center mt-3\", 4, \"ngIf\"], [1, \"input-error\"], [2, \"font-size\", \"12px\", \"margin-top\", \"10px\"], [1, \"grid\", \"justify-content-center\", \"mt-3\"], [1, \"sm:col-7\"], [\"pButton\", \"\", \"type\", \"submit\", \"iconPos\", \"right\", 1, \"auth-btn-right\", \"teacher-button\", \"disabled-link\", 3, \"label\", \"icon\", \"ngClass\"], [1, \"auth-form\", \"flex\", \"flex-column\", \"mx-3\", \"sm:mx-5\", \"md:mx-6\", \"lg:mx-7\"], [3, \"submit\"], [\"name\", \"email\", \"ngModel\", \"\", \"type\", \"email\", \"placeholder\", \"Email\", \"required\", \"\", \"email\", \"\", \"type\", \"text\", 1, \"input-element\"], [\"name\", \"password\", \"ngModel\", \"\", \"placeholder\", \"Password\", \"required\", \"\", 1, \"input-element\", 3, \"type\"], [1, \"input-field\", \"pt-2\"], [\"pButton\", \"\", \"type\", \"submit\", \"styleClass\", \"bg-primary\", \"label\", \"Sign In\", 1, \"sign-in-btn\", \"hvr-glow\"], [\"class\", \"alternative\", 4, \"ngIf\"], [1, \"alternative\", 3, \"click\"], [1, \"alternative-btn\"], [3, \"submit\", 4, \"ngIf\"], [1, \"alternative\"], [1, \"alternative-btn\", 3, \"click\"], [1, \"flex\", \"mt-3\"], [\"pButton\", \"\", \"type\", \"submit\", \"styleClass\", \"bg-primary\", \"label\", \"Submit\", 1, \"sign-in-btn\", \"hvr-glow\"]],\n    template: function LoginComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, LoginComponent_Defer_0_Template, 43, 11)(1, LoginComponent_DeferPlaceholder_1_Template, 3, 1);\n        i0.ɵɵdefer(2, 0, LoginComponent_Defer_2_DepsFn, null, 1);\n        i0.ɵɵdeferOnViewport(0, -1);\n      }\n    },\n    dependencies: [CommonModule, MatProgressSpinnerModule, ButtonModule, CheckboxModule, FormsModule, ReactiveFormsModule, LoaderComponent, RecaptchaModule],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n@media screen and (max-width: 1024px) {\\n    .app-content {\\n    margin-top: 0 !important;\\n  }\\n}\\n  .app-content.auth-route {\\n  margin-top: 0 !important;\\n  margin-bottom: 0 !important;\\n}\\n\\n.title[_ngcontent-%COMP%] {\\n  font-size: clamp(1.5rem, 0.47vw + 1.41rem, 1.88rem) !important;\\n  text-align: center;\\n}\\n@media only screen and (min-width: 768px) {\\n  .title[_ngcontent-%COMP%] {\\n    text-align: left;\\n  }\\n}\\n\\n.auth[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 10px;\\n  overflow: hidden;\\n  width: 100%;\\n}\\n.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%] {\\n  width: 80%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100px;\\n}\\n@media screen and (max-width: 1366px) {\\n  .auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n@media screen and (max-width: 1024px) {\\n  .auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%] {\\n    position: relative;\\n    flex-direction: column;\\n  }\\n}\\n@media screen and (max-width: 768px) {\\n  .auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%] {\\n    flex-direction: column-reverse;\\n  }\\n  .auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 80px;\\n  }\\n}\\n.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .left-side[_ngcontent-%COMP%] {\\n  height: 500px;\\n  width: 42%;\\n  border-top-left-radius: 28px;\\n  border-bottom-left-radius: 28px;\\n  background-image: linear-gradient(179deg, #9baaff -6%, #152caf 106%);\\n  display: flex;\\n  align-items: center;\\n  flex-direction: column;\\n  color: var(--white);\\n  position: relative;\\n  left: 0;\\n}\\n.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .left-side.login-is-open[_ngcontent-%COMP%] {\\n  background: linear-gradient(#927ffa 0%, #5a5fe9 100%);\\n}\\n.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .left-side.has-animated[_ngcontent-%COMP%] {\\n  transition: all 0.3s linear;\\n}\\n@media screen and (max-width: 1024px) {\\n  .auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .left-side[_ngcontent-%COMP%] {\\n    flex-direction: column-reverse;\\n    align-items: center;\\n    justify-content: flex-start;\\n    height: -moz-fit-content;\\n    height: fit-content;\\n    position: relative;\\n    border-radius: 0;\\n    bottom: 0;\\n    width: 100%;\\n  }\\n}\\n.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .left-side[_ngcontent-%COMP%]   .left-side-content[_ngcontent-%COMP%] {\\n  height: 100%;\\n  text-align: center;\\n  flex-direction: column;\\n  display: flex;\\n  justify-content: space-between;\\n  padding-top: 32px;\\n  padding-bottom: 40px;\\n  align-items: center;\\n  font-size: 15px;\\n  font-weight: normal;\\n  font-stretch: normal;\\n  font-style: normal;\\n  line-height: normal;\\n  letter-spacing: normal;\\n  text-align: center;\\n}\\n.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .left-side[_ngcontent-%COMP%]   .left-side-content[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%] {\\n  height: 500px;\\n  width: 60%;\\n  border-top-right-radius: 28px;\\n  border-bottom-right-radius: 28px;\\n  color: var(--main-color);\\n  background-color: white;\\n  position: relative;\\n}\\n.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .right-side.has-animated[_ngcontent-%COMP%] {\\n  transition: all 0.3s linear;\\n}\\n@media screen and (max-width: 768px) {\\n  .auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%] {\\n    width: 90%;\\n    height: auto;\\n  }\\n}\\n@media screen and (min-width: 1024px) {\\n  .auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%] {\\n    right: 0;\\n  }\\n}\\n.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%]   .right-side-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n}\\n@media screen and (max-width: 768px) {\\n  .auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%]   .right-side-content[_ngcontent-%COMP%] {\\n    margin-top: 30px;\\n    width: 90%;\\n  }\\n}\\n.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%]   .right-side-content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  font-size: 30px;\\n}\\n\\n.input-field[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 30px;\\n}\\n\\n.input-field[_ngcontent-%COMP%] {\\n  margin-top: 0.75rem;\\n  font-size: 1.125rem;\\n  box-sizing: border-box;\\n}\\n.input-field[_ngcontent-%COMP%]   .input-element-title[_ngcontent-%COMP%] {\\n  color: var(--main-color);\\n  font-size: clamp(0.88rem, 0.16vw + 0.84rem, 1rem);\\n  font-weight: bold;\\n  font-stretch: normal;\\n  font-style: normal;\\n  line-height: normal;\\n  letter-spacing: normal;\\n  text-align: left;\\n}\\n.input-field[_ngcontent-%COMP%]   .input-element[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n  color: var(--main-color);\\n  width: 100%;\\n  box-sizing: border-box;\\n  border: 1px solid var(--my-gray-2);\\n  border-radius: 8px;\\n  padding: 11px;\\n  font-size: 18px;\\n}\\n\\n.checkbox-bg[_ngcontent-%COMP%] {\\n  background-size: cover;\\n  background-repeat: no-repeat;\\n  background-position: center;\\n  height: 44px;\\n  align-items: center;\\n  display: flex;\\n  justify-content: center;\\n  border-radius: 8px;\\n}\\n\\n.teacher-bg[_ngcontent-%COMP%] {\\n  background: url(/assets/images/auth-btn-reg-teacher.png) no-repeat center center;\\n  background-size: cover;\\n}\\n\\n.student-bg[_ngcontent-%COMP%] {\\n  background: url(/assets/images/auth-btn-reg-student.png) no-repeat center center;\\n  background-size: cover;\\n}\\n\\n[_nghost-%COMP%]  .terms-checkbox .p-checkbox {\\n  width: 18px;\\n  height: 18px;\\n}\\n[_nghost-%COMP%]  .terms-checkbox .p-checkbox-label {\\n  font-size: 16px;\\n  cursor: pointer;\\n}\\n@media screen and (max-width: 768px) {\\n  [_nghost-%COMP%]  .terms-checkbox .p-checkbox-label {\\n    font-size: 14px;\\n  }\\n}\\n[_nghost-%COMP%]  .terms-checkbox .p-checkbox .p-checkbox-box.p-highlight .p-checkbox-icon.pi-check:before {\\n  top: 1px;\\n  left: -5px;\\n}\\n[_nghost-%COMP%]  .terms-checkbox .p-checkbox .p-checkbox-box {\\n  border-radius: 50px;\\n}\\n[_nghost-%COMP%]  .terms-checkbox .p-checkbox .p-checkbox-box.p-highlight {\\n  border-radius: 50px;\\n}\\n[_nghost-%COMP%]  .terms-checkbox .p-checkbox .p-checkbox-box .p-checkbox-icon {\\n  transform: scale(1.3);\\n}\\n[_nghost-%COMP%]  .terms-checkbox .p-component .p-checkbox-box {\\n  width: 18px !important;\\n  height: 18px !important;\\n}\\n[_nghost-%COMP%]  .teacher-checkbox {\\n  height: 32px;\\n  color: #ffffff;\\n}\\n@media screen and (max-width: 768px) {\\n  [_nghost-%COMP%]  .teacher-checkbox {\\n    padding-left: 6px;\\n  }\\n}\\n[_nghost-%COMP%]  .teacher-checkbox .p-checkbox {\\n  width: 28px;\\n  height: 28px;\\n}\\n[_nghost-%COMP%]  .teacher-checkbox .p-checkbox-label {\\n  font-size: 16px;\\n  cursor: pointer;\\n}\\n@media screen and (max-width: 768px) {\\n  [_nghost-%COMP%]  .teacher-checkbox .p-checkbox-label {\\n    font-size: 14px;\\n  }\\n}\\n[_nghost-%COMP%]  .teacher-checkbox .p-checkbox .p-checkbox-box.p-highlight .p-checkbox-icon.pi-check:before {\\n  top: 0px;\\n  left: -3px;\\n}\\n[_nghost-%COMP%]  .teacher-checkbox .p-checkbox .p-checkbox-box {\\n  background: transparent;\\n  border: 1px solid #ffffff;\\n  border-radius: 50px;\\n}\\n[_nghost-%COMP%]  .teacher-checkbox .p-checkbox .p-checkbox-box.p-highlight {\\n  background: transparent;\\n  border-color: #ffffff;\\n  border-radius: 50px;\\n}\\n[_nghost-%COMP%]  .teacher-checkbox .p-checkbox .p-checkbox-box .p-checkbox-icon {\\n  transform: scale(2.5);\\n}\\n[_nghost-%COMP%]  .teacher-checkbox .p-component .p-checkbox-box {\\n  width: 25px !important;\\n  height: 25px !important;\\n}\\n.auth-btn[_ngcontent-%COMP%] {\\n  border: 1px solid white;\\n  border-radius: 10px;\\n  padding: 10px;\\n  font-size: 20px;\\n  width: 250px;\\n  margin: 0 auto;\\n  cursor: pointer;\\n  transition: all 0.2s ease-in;\\n  box-sizing: border-box;\\n  white-space: normal;\\n  height: 50px;\\n  color: #ffffff;\\n}\\n.auth-btn[_ngcontent-%COMP%]:hover {\\n  background: white !important;\\n  color: #746ef1 !important;\\n}\\n\\n.sign-in-btn.p-button[_ngcontent-%COMP%] {\\n  padding-right: 60px;\\n  padding-left: 60px;\\n  border-radius: 8px;\\n  background: #2E3D90;\\n}\\n\\n.p-button.teacher-bg[_ngcontent-%COMP%]:enabled:hover {\\n  background: url(/assets/images/auth-btn-reg-teacher.png) !important;\\n}\\n\\n.p-button.student-bg[_ngcontent-%COMP%]:enabled:hover {\\n  background: url(/assets/images/auth-btn-reg-student.png) no-repeat center center;\\n  background-size: cover;\\n}\\n\\n.auth-btn-right[_ngcontent-%COMP%] {\\n  width: 100%;\\n  color: white;\\n  border-radius: 8px;\\n  font-size: clamp(1rem, 0.16vw + 0.97rem, 1.13rem);\\n}\\n.auth-btn-right[_ngcontent-%COMP%]   .teacher-bg[_ngcontent-%COMP%] {\\n  background: url(/assets/images/auth-btn-reg-teacher.png) !important;\\n}\\n\\n.auth-btn[_ngcontent-%COMP%]:hover {\\n  background: white;\\n  color: var(--main-color);\\n}\\n\\n.alternative[_ngcontent-%COMP%] {\\n  font-size: clamp(0.88rem, 0.16vw + 0.84rem, 1rem);\\n  margin-top: 15px;\\n  display: flex;\\n}\\n\\n.alternative-btn[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  cursor: pointer;\\n  white-space: break-spaces;\\n}\\n\\n.alternative-btn[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.spinner[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  -ms-transform: translate(-50%, -50%); \\n\\n  -webkit-transform: translate(-50%, -50%); \\n\\n}\\n\\n.register-success[_ngcontent-%COMP%] {\\n  color: var(--light-purple);\\n  font-weight: bold;\\n  display: flex;\\n  align-items: center;\\n  font-size: 16px;\\n  padding: 15px 0px;\\n}\\n.register-success[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 30px;\\n  margin-right: 15px;\\n}\\n\\n.form-container[_ngcontent-%COMP%] {\\n  background-color: #FFFFFF;\\n  top: 0;\\n  padding-bottom: 1rem;\\n}\\n@media only screen and (min-width: 768px) {\\n  .form-container[_ngcontent-%COMP%] {\\n    height: 100%;\\n    transition: all 0.6s ease-in-out;\\n    display: flex;\\n    align-items: center;\\n    justify-content: start;\\n    flex-direction: column;\\n    position: absolute;\\n    padding-bottom: 0;\\n  }\\n}\\n\\n.sign-up-container[_ngcontent-%COMP%] {\\n  left: 0;\\n  width: 100%;\\n  z-index: 2;\\n  display: block;\\n}\\n@media only screen and (min-width: 768px) {\\n  .sign-up-container[_ngcontent-%COMP%] {\\n    display: block;\\n    width: 60%;\\n  }\\n}\\n\\n.login-container.right-panel-active[_ngcontent-%COMP%]   .sign-up-container[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n@media only screen and (min-width: 768px) {\\n  .login-container.right-panel-active[_ngcontent-%COMP%]   .sign-up-container[_ngcontent-%COMP%] {\\n    display: block;\\n    transform: translateX(100%);\\n  }\\n}\\n\\n.sign-in-container[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  z-index: 1;\\n  display: none;\\n}\\n@media only screen and (min-width: 768px) {\\n  .sign-in-container[_ngcontent-%COMP%] {\\n    display: block;\\n    left: -20%;\\n    width: 60%;\\n  }\\n}\\n\\n.login-container.right-panel-active[_ngcontent-%COMP%]   .sign-in-container[_ngcontent-%COMP%] {\\n  opacity: 1;\\n  z-index: 5;\\n  display: block;\\n}\\n@media only screen and (min-width: 768px) {\\n  .login-container.right-panel-active[_ngcontent-%COMP%]   .sign-in-container[_ngcontent-%COMP%] {\\n    transform: translateX(100%);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_show {\\n  0%, 49.99% {\\n    opacity: 0;\\n    z-index: 1;\\n  }\\n  50%, 100% {\\n    opacity: 1;\\n    z-index: 5;\\n  }\\n}\\n.overlay-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  top: 0;\\n  \\n\\n  width: 100%;\\n  height: 300px;\\n  overflow: hidden;\\n  transition: transform 0.6s ease-in-out;\\n  z-index: 100;\\n  z-index: 100;\\n}\\n@media only screen and (min-width: 768px) {\\n  .overlay-container[_ngcontent-%COMP%] {\\n    position: absolute;\\n    top: 0;\\n    left: 60%;\\n    width: 50%;\\n    height: 100%;\\n    overflow: hidden;\\n    transition: transform 0.6s ease-in-out;\\n  }\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .login-container.right-panel-active[_ngcontent-%COMP%]   .overlay-container[_ngcontent-%COMP%] {\\n    transform: translateX(-150%);\\n    left: 60%;\\n    width: 40%;\\n  }\\n}\\n\\n.login-container.right-panel-active[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(180deg, hsl(231, 84%, 75%) 0%, hsl(231, 78%, 72%) 10%, hsl(232, 72%, 69%) 21%, hsl(232, 68%, 66%) 32%, hsl(232, 64%, 62%) 43%, hsl(233, 61%, 59%) 54%, hsl(233, 58%, 56%) 65%, hsl(233, 56%, 53%) 77%, hsl(232, 56%, 49%) 88%, hsl(231, 63%, 45%) 100%);\\n}\\n\\n.overlay[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(180deg, hsl(246, 88%, 72%) 0%, hsl(245, 86%, 71%) 10%, hsl(245, 84%, 70%) 21%, hsl(244, 83%, 69%) 32%, hsl(243, 82%, 68%) 43%, hsl(242, 80%, 67%) 54%, hsl(241, 79%, 67%) 65%, hsl(240, 78%, 66%) 77%, hsl(239, 77%, 65%) 88%, hsl(238, 76%, 63%) 100%);\\n  background-repeat: no-repeat;\\n  background-size: cover;\\n  background-position: 0 0;\\n  color: #FFFFFF;\\n  position: relative;\\n  left: -100%;\\n  height: 100%;\\n  width: 200%;\\n  transform: translateX(0);\\n  transition: transform 0.6s ease-in-out;\\n}\\n\\n.login-container.right-panel-active[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%] {\\n  transform: translateX(50%);\\n}\\n\\n.overlay-panel[_ngcontent-%COMP%] {\\n  box-sizing: border-box;\\n  position: absolute;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-direction: column;\\n  text-align: center;\\n  top: 0;\\n  height: 100%;\\n  width: 50%;\\n  transform: translateX(0);\\n  transition: transform 0.6s ease-in-out;\\n}\\n@media only screen and (min-width: 768px) {\\n  .overlay-panel[_ngcontent-%COMP%] {\\n    width: 40%;\\n  }\\n}\\n\\n.overlay-left[_ngcontent-%COMP%] {\\n  display: none;\\n  transform: translateX(-20%);\\n}\\n@media only screen and (min-width: 768px) {\\n  .overlay-left[_ngcontent-%COMP%] {\\n    display: flex;\\n    transform: unset;\\n    width: 60%;\\n  }\\n}\\n\\n.login-container.right-panel-active[_ngcontent-%COMP%]   .overlay-left[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: block;\\n  display: flex;\\n  transform: unset;\\n  width: 50%;\\n}\\n.overlay-right[_ngcontent-%COMP%] {\\n  left: 50%;\\n  right: 0;\\n  transform: translateX(0%);\\n}\\n@media only screen and (min-width: 768px) {\\n  .overlay-right[_ngcontent-%COMP%] {\\n    left: initial;\\n    right: 0;\\n    transform: translateX(-25%);\\n  }\\n}\\n\\n.login-container.right-panel-active[_ngcontent-%COMP%]   .overlay-right[_ngcontent-%COMP%] {\\n  transform: translateX(20%);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n    data: {\n      animation: [slideUpDownAnimation, slideInOut]\n    }\n  });\n}", "map": {"version": 3, "names": ["CommonModule", "DOCUMENT", "signal", "FormsModule", "ReactiveFormsModule", "UntypedFormControl", "Validators", "MatProgressSpinnerModule", "RecaptchaModule", "ButtonModule", "CheckboxModule", "fork<PERSON><PERSON>n", "of", "switchMap", "take", "UserRole", "UserStatus", "slideInOut", "slideUpDownAnimation", "LoaderComponent", "SubSink", "i13", "RecaptchaComponent", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵtext", "ctx_r2", "registerButtonLabel", "isRegisterLoading", "ɵɵpureFunction3", "_c2", "register<PERSON><PERSON><PERSON>er", "ɵɵelementContainer", "ɵɵlistener", "LoginComponent_Defer_0_ng_container_12_Template_p_checkbox_onChange_6_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "onChange", "ɵɵtwoWayListener", "LoginComponent_Defer_0_ng_container_12_Template_p_checkbox_ngModelChange_6_listener", "$event", "ɵɵtwoWayBindingSet", "selectedR<PERSON>", "LoginComponent_Defer_0_ng_container_12_Template_p_checkbox_onChange_9_listener", "LoginComponent_Defer_0_ng_container_12_Template_p_checkbox_ngModelChange_9_listener", "LoginComponent_Defer_0_ng_container_12_Template_form_ngSubmit_10_listener", "signupForm_r4", "ɵɵreference", "onSignup", "ɵɵtemplate", "LoginComponent_Defer_0_ng_container_12_div_17_Template", "LoginComponent_Defer_0_ng_container_12_Template_i_click_22_listener", "togglemyPasswordFieldType", "LoginComponent_Defer_0_ng_container_12_Template_input_ngModelChange_23_listener", "password", "LoginComponent_Defer_0_ng_container_12_div_25_Template", "LoginComponent_Defer_0_ng_container_12_div_26_Template", "LoginComponent_Defer_0_ng_container_12_ng_template_27_Template", "ɵɵtemplateRefExtractor", "LoginComponent_Defer_0_ng_container_12_Template_i_click_33_listener", "LoginComponent_Defer_0_ng_container_12_div_36_Template", "LoginComponent_Defer_0_ng_container_12_Template_p_checkbox_ngModelChange_38_listener", "termsAgreed", "LoginComponent_Defer_0_ng_container_12_Template_re_captcha_resolved_43_listener", "captchaResolvedEvent", "LoginComponent_Defer_0_ng_container_12_div_44_Template", "LoginComponent_Defer_0_ng_container_12_ng_container_45_Template", "ɵɵtwoWayProperty", "emailError", "getKeyValueFieldType", "passwordInput_r5", "valid", "invalid", "templateName_r6", "confirmError", "length", "LoginComponent_Defer_0_div_19_div_20_Template_div_click_2_listener", "_r9", "switchView", "LoginComponent_Defer_0_div_19_form_25_Template_form_submit_0_listener", "_r10", "forgotForm_r11", "onForgot", "LoginComponent_Defer_0_div_19_form_25_div_9_Template", "LoginComponent_Defer_0_div_19_Template_form_submit_3_listener", "_r7", "loginForm_r8", "onLogin", "LoginComponent_Defer_0_div_19_div_10_Template", "LoginComponent_Defer_0_div_19_Template_i_click_15_listener", "LoginComponent_Defer_0_div_19_div_20_Template", "LoginComponent_Defer_0_div_19_Template_div_click_21_listener", "showForgotPassword", "LoginComponent_Defer_0_div_19_form_25_Template", "is1024", "LoginComponent_Defer_0_ng_container_2_Template", "LoginComponent_Defer_0_ng_container_12_Template", "LoginComponent_Defer_0_ng_container_18_Template", "LoginComponent_Defer_0_div_19_Template", "LoginComponent_Defer_0_Template_button_click_26_listener", "_r1", "handleSwitchViewClick", "LoginComponent_Defer_0_Template_a_click_31_listener", "LoginComponent_Defer_0_Template_button_click_38_listener", "LoginComponent_Defer_0_Template_a_click_40_listener", "ɵɵpureFunction1", "_c1", "loginIsOpen", "isLoading", "showSuccessRegistrationMsg", "LoginComponent", "constructor", "toastService", "generalService", "chatService", "authService", "userService", "teacherApplicationService", "studentTrialService", "router", "zone", "document", "renderer", "location", "activatedRoute", "subs", "signUpIsOpen", "is768", "is1366", "is1024up", "isBig", "ismyTextFieldType", "email", "confirm", "passwordCheck", "passwordFormControl", "required", "pattern", "isDisabled", "hasAnimated", "captchaResolved", "lottieSignupEl", "mwindow", "defaultView", "console", "log", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "returnUrl", "snapshot", "queryParams", "undefined", "url", "includes", "add", "getAuthStatusListener", "subscribe", "authStatus", "deviceKind", "pipe", "res", "w1024up", "registrationSuccessMessageListener", "executeRecaptchaV3", "loadScript", "node", "createElement", "setAttribute", "src", "type", "async", "charset", "getElementsByTagName", "append<PERSON><PERSON><PERSON>", "ngAfterViewInit", "ngOnDestroy", "unsubscribe", "showRegistrationSuccessfullMessage", "addLottiePlayerForSignupCompleted", "checkBoxChange", "args", "checked", "animate", "changeHasAnimated", "elem1", "getElementById", "elem2", "style", "left", "borderRadius", "right", "createUrlTree", "relativeTo", "toString", "go", "push", "detectRole", "removeSpaces", "control", "value", "replace", "setValue", "setValidatorsForTrimming", "form", "controlName", "controls", "trim", "setValidators", "updateValueAndValidity", "validEmail", "loginForm", "setShowToastmessage", "severity", "summary", "detail", "expiresInDuration", "token", "tokenInfo", "result1$", "login", "response", "getDecodedAccessToken", "setToken", "setAuthTimer", "userId", "id", "user", "role", "TEACHER", "requestDataFromMultipleSources", "authStatusListener", "next", "now", "Date", "expirationDate", "getTime", "saveAuthData", "requestStudentTrialData", "u", "availability", "off", "setTrialRequested", "setIsAuthenticated", "toLowerCase", "status", "ACTIVE", "navigate", "navigateByUrl", "valuesArray", "Object", "values", "allFalse", "every", "getTeacherStatusNavigationUrl", "error", "forgot", "reset", "endPointType", "confirmPassword", "invalidInputs", "hasOwnProperty", "detailMessage", "slice", "createUser", "setShowRegistrationSuccessfullMessage", "onTeacherButtonClicked", "onStudentButtonClicked", "match", "touched", "key", "response1", "getUserAvailability", "response2", "getTeacherStatus", "response3", "getTeacherApplicationStepsStatus", "getStudentHasTrialRequest", "loadLottiePlayer", "_this", "successMessageDelay", "lottiePlayerCDN", "setTimeout", "container", "classList", "toggle", "contains", "captchaResponse", "set", "el", "lottiePlayer", "nativeElement", "innerHTML", "_", "ɵɵdirectiveInject", "i1", "ToastService", "i2", "GeneralService", "i3", "ChatService", "i4", "AuthService", "i5", "UserService", "i6", "TeacherApplicationService", "i7", "StudentTrialService", "i8", "Router", "NgZone", "Renderer2", "i9", "Location", "ActivatedRoute", "_2", "selectors", "viewQuery", "LoginComponent_Query", "rf", "ctx", "LoginComponent_Defer_0_Template", "LoginComponent_DeferPlaceholder_1_Template", "ɵɵdefer", "LoginComponent_Defer_2_DepsFn", "ɵɵdeferOnViewport", "styles", "data", "animation"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\auth\\login\\login.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\auth\\login\\login.component.html"], "sourcesContent": ["import { CommonModule, DOCUMENT, Location } from '@angular/common';\r\nimport { Component, ElementRef, Inject, NgZone, OnDestroy, OnInit, Renderer2, signal, ViewChild } from '@angular/core';\r\nimport { AbstractControl, FormsModule, NgForm, ReactiveFormsModule, UntypedFormControl, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { RecaptchaModule, ReCaptchaV3Service } from 'ng-recaptcha';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { forkJoin, Observable, of } from 'rxjs';\r\nimport { switchMap, take } from 'rxjs/operators';\r\nimport { User, UserRole, UserStatus } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { ChatService } from 'src/app/core/services/chat.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { StudentTrialService } from 'src/app/core/services/student-trial.service';\r\nimport { TeacherApplicationService } from 'src/app/core/services/teacher-application.service';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { UserService } from 'src/app/core/services/user.service';\r\nimport { slideInOut, slideUpDownAnimation } from 'src/app/helpers/my-animations';\r\nimport { LoaderComponent } from 'src/app/shared/loader/loader.component';\r\nimport { SubSink } from 'subsink';\r\n\r\ntype TextFieldType = \"email\" | \"password\" | \"confirm\";\r\n@Component({\r\n  imports: [\r\n    CommonModule,\r\n    MatProgressSpinnerModule,\r\n    ButtonModule,\r\n    CheckboxModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    LoaderComponent,\r\n    RecaptchaModule,\r\n  ],\r\n  selector: 'app-login',\r\n  templateUrl: './login.component.html',\r\n  styleUrls: ['./login.component.scss'],\r\n  animations: [slideUpDownAnimation, slideInOut],\r\n  standalone: true,\r\n})\r\nexport class LoginComponent implements OnInit, OnDestroy {\r\n  private subs = new SubSink();\r\n  isLoading = false;\r\n  isRegisterLoading = false;\r\n  termsAgreed = false;\r\n  imagePreview?: string;\r\n  user?: User;\r\n  userId?: string | null;\r\n  loginIsOpen: boolean = true;\r\n  signUpIsOpen: boolean = false;\r\n  registerAsTeacher: boolean = false;\r\n  is768: boolean = false;\r\n  is1024: boolean = false;\r\n  is1366: boolean = false;\r\n  is1024up: boolean = false;\r\n  isBig: boolean = false;\r\n  emailError: boolean = false;\r\n  confirmError: boolean = false;\r\n  showSuccessRegistrationMsg: boolean = false;\r\n  showForgotPassword: boolean = false;\r\n  selectedRole: string[] = [];\r\n  registerButtonLabel = 'Register as Student';\r\n  returnUrl!: string;\r\n  ismyTextFieldType: Record<TextFieldType, boolean> = {\r\n    email: false,\r\n    password: false,\r\n    confirm: false,\r\n  };\r\n  passwordCheck: string = '';\r\n  passwordFormControl = new UntypedFormControl('', [\r\n    Validators.required,\r\n    Validators.pattern('^(?=.*[A-Z])(?=.*[0-9]).*$')\r\n  ]);\r\n  isDisabled = true;\r\n  hasAnimated = true;\r\n  private mwindow?: Window;\r\n  captchaResolved = signal(false);\r\n  @ViewChild('lottieSignupEl', { static: false }) lottieSignupEl: ElementRef = {} as ElementRef;\r\n  constructor(\r\n    private toastService: ToastService,\r\n    private generalService: GeneralService,\r\n    private chatService: ChatService,\r\n    public authService: AuthService,\r\n    public userService: UserService,\r\n    public teacherApplicationService: TeacherApplicationService,\r\n    public studentTrialService: StudentTrialService,\r\n    private router: Router,\r\n    private zone: NgZone,\r\n    @Inject(DOCUMENT) private document: Document,\r\n    private renderer: Renderer2,\r\n    private location: Location,\r\n    private activatedRoute: ActivatedRoute,\r\n  ) {\r\n    this.mwindow = <any>this.document.defaultView;\r\n    console.log(activatedRoute.firstChild)\r\n    // try to switchView on register/login route detection\r\n\r\n  }\r\n\r\n  ngOnInit() {\r\n\r\n    // get return url from route parameters or default to ‘/’\r\n    this.returnUrl = this.activatedRoute.snapshot.queryParams['returnUrl'] || undefined;\r\n    if (this.router.url.includes('login')) {\r\n      this.loginIsOpen = true;\r\n    }\r\n    if (this.router.url.includes('register')) {\r\n      this.loginIsOpen = false;\r\n    }\r\n\r\n    this.subs.add(this.authService.getAuthStatusListener().subscribe(authStatus => {\r\n      if (authStatus) {\r\n        // this.isLoading = false;\r\n      }\r\n    }));\r\n    this.subs.add(this.generalService.deviceKind.pipe(take(1)).subscribe(res => {\r\n      if (res) {\r\n        this.is1024 = res.is1024;\r\n        this.is1366 = res.is1366;\r\n        this.is768 = res.is768;\r\n        this.isBig = res.isBig;\r\n        this.is1024up = res.w1024up!;\r\n        if (this.is1024up) {\r\n\r\n          // this.renderer.addClass(this.document.getElementsByClassName('app')[0], 'h-full');\r\n        }\r\n        console.log(res);\r\n      }\r\n    }));\r\n    this.registrationSuccessMessageListener();\r\n    // this.renderer.addClass(this.document.body, 'gradient-moving-body');\r\n  }\r\n\r\n  public executeRecaptchaV3() {\r\n\r\n  }\r\n\r\n  public loadScript(url: string) {\r\n    console.log('preparing to load...')\r\n    let node = document.createElement('script');\r\n    node.setAttribute(\"id\", \"lottie-player\");\r\n    node.src = url;\r\n    node.type = 'text/javascript';\r\n    node.async = true;\r\n    node.charset = 'utf-8';\r\n    document.getElementsByTagName('head')[0].appendChild(node);\r\n  }\r\n  ngAfterViewInit() {\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n    // this.renderer.removeClass(this.document.body, 'height-screen');\r\n    // this.renderer.removeClass(this.document.getElementsByClassName('app')[0], 'h-full');\r\n  }\r\n\r\n  registrationSuccessMessageListener() {\r\n    this.subs.add(this.authService.showRegistrationSuccessfullMessage.subscribe(res => {\r\n      console.log(res);\r\n      this.showSuccessRegistrationMsg = res;\r\n      if (res) {\r\n        this.addLottiePlayerForSignupCompleted(this.lottieSignupEl);\r\n      }\r\n    }));\r\n  }\r\n\r\n  checkBoxChange(args: any) {\r\n    this.registerAsTeacher = args.checked\r\n  }\r\n\r\n  switchView(animate = false, changeHasAnimated = false) {\r\n    this.showSuccessRegistrationMsg = false;\r\n\r\n    if (changeHasAnimated) {\r\n      this.hasAnimated = changeHasAnimated;\r\n    }\r\n    if (this.isBig || this.is1366 || animate) {\r\n      if (this.loginIsOpen) {\r\n        if (this.is1024up) {\r\n\r\n          const elem1 = document.getElementById('left-side');\r\n          const elem2 = document.getElementById('right-side');\r\n\r\n          // const rect = elem2!.getBoundingClientRect();\r\n          // const parent = elem2!.parentElement;\r\n          // const leftOffset = elem2!.offsetLeft;\r\n          // const topOffset = elem1!.offsetTop;\r\n          // const leftWidth = elem2!.offsetWidth;\r\n          // const newLeft2 = rect.left - parent!.offsetLeft;\r\n\r\n          // const newLeft = newLeft2 + leftWidth;\r\n\r\n          // elem1!.style.left = newLeft - 2 + 'px';\r\n\r\n          document.getElementById('left-side')!.style.left = '470px';\r\n          document.getElementById('left-side')!.style.borderRadius = '0px 28px 28px 0';\r\n          document.getElementById('right-side')!.style.borderRadius = '28px 0px 0 28px';\r\n          document.getElementById('right-side')!.style.right = '270px';\r\n        }\r\n        const url = this.router.createUrlTree(['/auth/register'], { relativeTo: this.activatedRoute }).toString();\r\n        this.location.go(url);\r\n      } else {\r\n        if (this.is1024up) {\r\n          document.getElementById('left-side')!.style.left = '0';\r\n          document.getElementById('left-side')!.style.borderRadius = '28px 0px 0 28px';\r\n          document.getElementById('right-side')!.style.borderRadius = '0px 28px 28px 0';\r\n          document.getElementById('right-side')!.style.right = '0';\r\n        }\r\n        const url = this.router.createUrlTree(['/auth/login'], { relativeTo: this.activatedRoute }).toString();\r\n        this.location.go(url);\r\n      }\r\n    }\r\n    this.loginIsOpen = !this.loginIsOpen;\r\n    this.signUpIsOpen = !this.signUpIsOpen;\r\n  }\r\n\r\n  onChange() {\r\n    const selectedRole = this.selectedRole[this.selectedRole.length - 1];\r\n\r\n    this.selectedRole.length = 0;\r\n    this.selectedRole.push(selectedRole);\r\n    if (!selectedRole) {\r\n      this.selectedRole.length = 0;\r\n    }\r\n    this.detectRole();\r\n    this.isDisabled = false;\r\n  }\r\n\r\n  detectRole() {\r\n    if (this.selectedRole[0] === 'teacher') {\r\n      this.registerAsTeacher = true;\r\n      this.registerButtonLabel = 'Register as Teacher';\r\n    } else {\r\n      this.registerAsTeacher = false;\r\n      this.registerButtonLabel = 'Register as Student';\r\n    }\r\n  }\r\n\r\n  removeSpaces(control: AbstractControl) {\r\n    if (control && control.value && !control.value.replace(/\\s/g, '').length) {\r\n      control.setValue('');\r\n    }\r\n    return null;\r\n  }\r\n\r\n  setValidatorsForTrimming(form: NgForm, controlName: string) {\r\n    const control = form.controls[controlName];\r\n    control.setValue(control.value.trim());\r\n    control.setValidators([Validators.required, this.removeSpaces]);\r\n    control.updateValueAndValidity();\r\n  }\r\n\r\n  onLogin(form: NgForm) {\r\n    this.setValidatorsForTrimming(form, 'email');\r\n    this.setValidatorsForTrimming(form, 'password');\r\n    this.emailError = this.validEmail(form);\r\n    const loginForm = form.value;\r\n    console.log(form.value);\r\n    if (form.invalid) {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'warn',\r\n        summary: '',\r\n        detail: 'Please enter email and password to continue.'\r\n      });\r\n      return;\r\n    }\r\n\r\n    this.isLoading = true;\r\n    let expiresInDuration: number = 10800;\r\n    let token: string | null = null;\r\n    let tokenInfo: any = null;\r\n    const result1$ = this.subs.add(this.authService.login(form.value.email.trim(), form.value.password.trim())\r\n      .pipe(\r\n        switchMap((response: any) => {\r\n          token = response.token;\r\n          tokenInfo = this.authService.getDecodedAccessToken(token!); // decode token\r\n          this.authService.setToken(token!);\r\n          if (token) {\r\n            expiresInDuration = 10800;\r\n            // const expiresInDuration = tokenInfo.exp;\r\n            this.authService.setAuthTimer(expiresInDuration);\r\n            this.userId = response.id;\r\n            this.user = response;\r\n            if (tokenInfo.role === UserRole.TEACHER) {\r\n\r\n              return this.requestDataFromMultipleSources(response);\r\n              // return this.userService.getUserAvailability(response.id);\r\n            } else {\r\n              this.authService.authStatusListener.next(true);\r\n              const now = new Date();\r\n              const expirationDate = new Date(now.getTime() + expiresInDuration * 1000);\r\n              this.authService.saveAuthData(token!, expirationDate, this.userId!, this.user!, tokenInfo.role);\r\n              // this.router.navigate(['/dashboard']);\r\n              return (this.requestStudentTrialData());\r\n            }\r\n          } else {\r\n            return of(null);\r\n          }\r\n        }),\r\n      ).subscribe((res: any) => {\r\n        console.log(loginForm);\r\n        let u: User = {} as User;\r\n        // TODO: check if below is needed for Firebase integration\r\n        // this.existsFirebaseUserEmail(loginForm);\r\n        if (tokenInfo.role === UserRole.TEACHER) {\r\n          let u: User = { ...this.user, availability: res[0], off: res[1] } as User;\r\n        } else {\r\n\r\n          console.log(res);\r\n          // for student\r\n          this.studentTrialService.setTrialRequested(res[0]);\r\n          // this.user = this.user;\r\n        }\r\n        // this.user.role = 'trial'\r\n        this.authService.authStatusListener.next(true);\r\n        const now = new Date();\r\n        const expirationDate = new Date(now.getTime() + expiresInDuration * 1000);\r\n        this.authService.setIsAuthenticated(true);\r\n        this.authService.saveAuthData(token!, expirationDate, this.userId!, this.user!, tokenInfo.role);\r\n        this.isLoading = false;\r\n        // checking teacher application status steps if all are false (means the user hasnt started filling forms yet)\r\n\r\n        if (tokenInfo.role.toLowerCase() === UserRole.TEACHER.toLowerCase()) {\r\n          // teacher is active\r\n          if (this.user?.status.toLowerCase() === UserStatus.ACTIVE.toLowerCase()) {\r\n            if (this.returnUrl) {\r\n              this.router.navigate([this.returnUrl]);\r\n            } else {\r\n              this.router.navigateByUrl('/dashboard');\r\n            }\r\n            return false;\r\n          }\r\n          const valuesArray = Object.values(res[2]);\r\n          const allFalse = valuesArray.every(value => value === false);\r\n          console.log(allFalse);\r\n          if (allFalse) {\r\n            this.router.navigateByUrl('/teacher/info');\r\n            return false;\r\n          } else {\r\n            this.router.navigateByUrl(this.teacherApplicationService.getTeacherStatusNavigationUrl(res[1]));\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // student\r\n        if (this.returnUrl) {\r\n          this.router.navigate([this.returnUrl]);\r\n        } else {\r\n          this.router.navigateByUrl('/dashboard');\r\n        }\r\n        return false;\r\n      }, error => {\r\n        this.isLoading = false;\r\n      }));\r\n\r\n\r\n  }\r\n\r\n  onForgot(form: NgForm) {\r\n    this.emailError = this.validEmail(form);\r\n    if (form.invalid) {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'warn',\r\n        summary: '',\r\n        detail: 'Please enter email to continue.'\r\n      });\r\n      return;\r\n    }\r\n\r\n    this.toastService.setShowToastmessage({\r\n      severity: 'success',\r\n      summary: '',\r\n      detail: 'Please check your email to reset your password.'\r\n    });\r\n    this.authService.forgot(form.value.email);\r\n    form.reset();\r\n  }\r\n\r\n  onSignup(form: NgForm) {\r\n    let endPointType = this.registerAsTeacher ? 'RegisterTeacher' : 'RegisterStudent';\r\n    this.emailError = this.validEmail(form);\r\n    this.confirmError = this.confirmPassword(form.value.password, form.value.confirm);\r\n\r\n    let invalidInputs = '';\r\n    for (const controlName in form.controls) {\r\n      if (form.controls.hasOwnProperty(controlName)) {\r\n        const control = form.controls[controlName];\r\n        if (control.invalid) {\r\n          invalidInputs += `${controlName}, `;\r\n        }\r\n      }\r\n    }\r\n\r\n    let detailMessage = 'Please check all required fields to continue. \\n\\n';\r\n    if (invalidInputs !== '') {\r\n      invalidInputs = invalidInputs.slice(0, -2); // Remove the trailing comma and space\r\n\r\n      if (invalidInputs !== '') {\r\n        detailMessage += ` Invalid input for the following fields: ${invalidInputs}`;\r\n      }\r\n    }\r\n    if (!this.termsAgreed) {\r\n      detailMessage += ` Please agree to our terms & conditions.`;\r\n    }\r\n    if (!this.captchaResolved()) {\r\n      detailMessage += ` Please resolve the captcha to continue.`;\r\n    }\r\n\r\n    if (form.invalid || !this.termsAgreed || this.confirmError || !this.captchaResolved()) {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'warn',\r\n        summary: '',\r\n        detail: detailMessage\r\n      });\r\n      return;\r\n    }\r\n    this.isRegisterLoading = true;\r\n    this.authService.createUser(form.value.email, form.value.password, endPointType)\r\n      .subscribe((res) => {\r\n        this.authService.setShowRegistrationSuccessfullMessage(true);\r\n        this.isRegisterLoading = false;\r\n      }, error => {\r\n        this.isRegisterLoading = false;\r\n        this.authService.authStatusListener.next(false);\r\n        form.reset();\r\n      });\r\n  }\r\n\r\n  public onTeacherButtonClicked(): void {\r\n    this.registerAsTeacher = true;\r\n  }\r\n\r\n  public onStudentButtonClicked(): void {\r\n    this.registerAsTeacher = false;\r\n  }\r\n\r\n  validEmail(form: NgForm) {\r\n    if (form.value.email) {\r\n      let validEmail = form.value.email.trim()\r\n        .toLowerCase()\r\n        .match(\r\n          /^\\S+@\\S+\\.\\S+$/\r\n        );\r\n      if (validEmail === null && form.touched) {\r\n        return true\r\n      }\r\n    }\r\n    return false;\r\n  };\r\n\r\n  confirmPassword(password: string, confirm: string) {\r\n    return password !== confirm\r\n  }\r\n\r\n  //TODO GOOGLE LOGIN\r\n  // public externalLogin = () => {\r\n  //   this.authService.signInWithGoogle()\r\n  //   .then(res => {\r\n  //     const user: SocialUser = { ...res };\r\n  //     console.log(user);\r\n  //   }, error => console.log(error))\r\n  // }\r\n\r\n  togglemyPasswordFieldType(key: TextFieldType): void {\r\n    this.ismyTextFieldType[key] = !this.ismyTextFieldType[key];\r\n  }\r\n\r\n  getKeyValueFieldType(key: TextFieldType): boolean {\r\n    return this.ismyTextFieldType[key];\r\n  }\r\n\r\n  private requestDataFromMultipleSources(response: any): Observable<any[]> {\r\n    let response1 = this.userService.getUserAvailability(response.id);\r\n    let response2 = this.authService.getTeacherStatus();\r\n    let response3 = this.teacherApplicationService.getTeacherApplicationStepsStatus();\r\n    // Observable.forkJoin (RxJS 5) changes to just forkJoin() in RxJS 6\r\n    return forkJoin([response1, response2, response3]);\r\n  }\r\n\r\n\r\n  private requestStudentTrialData(): Observable<any[]> {\r\n    let response2 = this.studentTrialService.getStudentHasTrialRequest();\r\n    // Observable.forkJoin (RxJS 5) changes to just forkJoin() in RxJS 6\r\n    return forkJoin([response2]);\r\n  }\r\n\r\n  /**\r\n   * Loads LottiePlayer library from the CDN and shows success registration message.\r\n   */\r\n  loadLottiePlayer() {\r\n    const _this = this;\r\n    const successMessageDelay = 10; // in milliseconds\r\n    const lottiePlayerCDN = '/assets/js/lottie-player.js';\r\n\r\n    setTimeout(() => {\r\n      var id = document.getElementById('lottie-player');\r\n      if (!id) {\r\n        _this.loadScript(lottiePlayerCDN);\r\n      }\r\n      _this.showSuccessRegistrationMsg = true;\r\n    }, successMessageDelay);\r\n  }\r\n\r\n  handleSwitchViewClick() {\r\n    const container = document.getElementById('container');\r\n    container!.classList.toggle(\"right-panel-active\");\r\n\r\n    if (!container!.classList.contains(\"right-panel-active\")) {\r\n\r\n      const url = this.router.createUrlTree(['/auth/register'], { relativeTo: this.activatedRoute }).toString();\r\n      this.location.go(url);\r\n    } else {\r\n\r\n      const url = this.router.createUrlTree(['/auth/login'], { relativeTo: this.activatedRoute }).toString();\r\n      this.location.go(url);\r\n    }\r\n  }\r\n\r\n  captchaResolvedEvent(captchaResponse: string) {\r\n    console.log(`Resolved captcha with response: ${captchaResponse}`);\r\n    this.captchaResolved.set(true);\r\n  }\r\n\r\n  private addLottiePlayerForSignupCompleted(el: ElementRef): void {\r\n    const lottiePlayer = `<lottie-player src=\"https://assets4.lottiefiles.com/packages/lf20_xwmj0hsk.json\"  \r\n    background=\"transparent\"  speed=\"1\"  style=\"width: 280px; height: 280px; margin:0 auto;\"  autoplay></lottie-player>`;\r\n    el.nativeElement.innerHTML = lottiePlayer;\r\n  }\r\n\r\n}\r\n", "<!-- \r\n  //TODO GOOGLE LOGIN\r\n  \r\n  <div class=\"card\">\r\n  <div class=\"card-body\">\r\n      <div>\r\n          <h2>External Providers</h2>\r\n          <div style=\"padding: 20px;\">\r\n              <button class=\"btn btn-info\" type=\"button\" (click)=\"externalLogin()\">Google</button>\r\n          </div>\r\n      </div>\r\n  </div>\r\n</div> -->\r\n\r\n\r\n@defer(on viewport) {\r\n\r\n<div class=\"login-container w-100\" id=\"container\" [ngClass]=\"{'right-panel-active': loginIsOpen}\">\r\n  <div class=\"form-container sign-up-container\">\r\n\r\n    <ng-container *ngIf=\"isLoading\">\r\n\r\n\r\n      <div class=\"h-17rem md:h-auto\">\r\n        <div class=\"abs-centered\">\r\n          <app-loader [scale]=\"1.6\"></app-loader>\r\n        </div>\r\n      </div>\r\n\r\n    </ng-container>\r\n    <div [hidden]=\"isLoading\" class=\"auth-form  mx-3 sm:mx-5 md:mx-6 lg:mx-7\">\r\n\r\n      <div [hidden]=\"!showSuccessRegistrationMsg\">\r\n        <div class=\"surface-section px-4 py-1 md:px-2 lg:px-2 lg:mt-8 text-center\">\r\n          <div class=\"text-700 mb-6\">\r\n\r\n            <div #lottieSignupEl></div>\r\n\r\n            <div class=\"mb-4 font-bold text-2xl\"><span class=\"text-primary line-height-2\">You have successfully\r\n                registered\r\n                an account!</span></div>\r\n            <!-- <p class=\"text-primary text-sm\">\r\n              Please check your email and confirm your address by\r\n              clicking the verification link we sent you.\r\n              If you can’t find the email, please check your spam folder.\r\n            </p> -->\r\n\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <ng-container *ngIf=\"!showSuccessRegistrationMsg;\">\r\n        <div class=\"title my-3 sm:my-2 md:my-4 text-center sm:text-left\">\r\n          Sign Up\r\n        </div>\r\n\r\n        <div class=\"grid\">\r\n          <div class=\"col-6\">\r\n            <div class=\"checkbox-bg student-bg\">\r\n              <p-checkbox class=\"teacher-checkbox\" (onChange)=\"onChange()\" name=\"group1\" value=\"student\"\r\n                label=\"Register as Student\" [(ngModel)]=\"selectedRole\" inputId=\"sf\"></p-checkbox>\r\n            </div>\r\n          </div>\r\n          <div class=\"col-6\">\r\n            <div class=\"checkbox-bg teacher-bg\">\r\n              <p-checkbox class=\"teacher-checkbox\" styleClass=\"teacher-checkbox-inner\" (onChange)=\"onChange()\"\r\n                name=\"group1\" value=\"teacher\" label=\"Register as Teacher\" [(ngModel)]=\"selectedRole\"\r\n                inputId=\"ny\"></p-checkbox>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n\r\n        <form (ngSubmit)=\"onSignup(signupForm)\" #signupForm=\"ngForm\" autocomplete=\"off\">\r\n          <div class=\"input-field\">\r\n            <div class=\"input-element-title\">Email * </div>\r\n            <input name=\"email\" ngModel type=\"email\" placeholder=\"Email\" #emailInput=\"ngModel\" required email\r\n              class=\"input-element\" autocomplete=\"off\" />\r\n            <div *ngIf=\"emailError\" class=\"input-error\">Email is not valid.</div>\r\n          </div>\r\n          <div class=\"input-field\">\r\n            <div class=\"input-element-title\">Password *</div>\r\n            <span class=\"p-input-icon-right\" style=\"width: 100%;\">\r\n              <i class=\"pi\" (click)=\"togglemyPasswordFieldType('password')\"\r\n                [ngClass]=\"getKeyValueFieldType('password')? 'pi-eye': 'pi-eye-slash'\" style=\"margin-top:-2px;\"></i>\r\n              <input name=\"password\" [(ngModel)]=\"password\"\r\n                [type]=\"getKeyValueFieldType('password') ? 'text' : 'password'\" placeholder=\"Password\" required\r\n                class=\"input-element\" #passwordInput=\"ngModel\" autocomplete=\"off\"\r\n                pattern=\"^(?=.*[A-Z])(?=.*[0-9]).*$\" />\r\n\r\n\r\n            </span>\r\n            <div *ngIf=\"passwordInput && passwordInput.valid\">\r\n              <div style=\"font-size:12px; margin-top:10px;\">Great job! Your password meets the requirements.</div>\r\n            </div>\r\n            <div *ngIf=\"passwordInput.invalid; else templateName\">\r\n              <div style=\"font-size:12px; margin-top:10px;\">Password must contain at least one capital letter and one\r\n                number</div>\r\n\r\n            </div>\r\n\r\n            <ng-template #templateName>\r\n\r\n              <!-- <div style=\"font-size:12px; margin-top:10px;\">Password must contain at least one capital letter and one number</div> -->\r\n\r\n            </ng-template>\r\n          </div>\r\n          <div class=\"input-field\">\r\n            <div class=\"input-element-title\">Confirm Password *</div>\r\n            <span class=\"p-input-icon-right\" style=\"width: 100%;\">\r\n              <i class=\"pi\" (click)=\"togglemyPasswordFieldType('confirm')\"\r\n                [ngClass]=\"getKeyValueFieldType('confirm')? 'pi-eye': 'pi-eye-slash'\" style=\"margin-top:-2px;\"></i>\r\n              <input name=\"confirm\" [type]=\"getKeyValueFieldType('confirm') ? 'text' : 'password'\" ngModel\r\n                type=\"password\" placeholder=\"Confirm Password\" #passwordInput=\"ngModel\" required class=\"input-element\"\r\n                autocomplete=\"off\" pattern=\"^(?=.*[A-Z])(?=.*[0-9]).*$\" />\r\n            </span>\r\n            <div *ngIf=\"confirmError\" class=\"input-error\">Passwords do not match.</div>\r\n          </div>\r\n          <div class=\"review-section flex align-items-center gap-1\"\r\n            style=\"font-size:15px; margin: 20px 0; font-weight: bold;\">\r\n            <!--  [(ngModel)]=\"selectedValues\" (onChange)=\"agree()\" -->\r\n            <p-checkbox id=\"termsCheckbox\" label=\"I have read and agree to the website \" class=\"terms-checkbox\"\r\n              name=\"groupname\" [(ngModel)]=\"termsAgreed\" binary=\"true\" [value]=\"termsAgreed\"></p-checkbox><a\r\n              href=\"https://mylingotrip.com/terms-conditions/\" target=\"_blank\"\r\n              style=\"color: black;font-size: 16px;\">terms and conditions*</a>\r\n          </div>\r\n          <ng-container>\r\n            <div class=\"grid align-items-center justify-content-center w-full mx-auto mt-3\">\r\n              <re-captcha (resolved)=\"captchaResolvedEvent($event)\"\r\n                siteKey=\"6LdFlA4pAAAAAJzrvvdou58XS_zQQ94z0lwxMv8i\"></re-captcha>\r\n            </div>\r\n          </ng-container>\r\n          <div class=\"grid justify-content-center mt-3\" *ngIf=\"this.selectedRole.length !== 0\">\r\n            <div class=\"sm:col-7\">\r\n              <button pButton type=\"submit\" [label]=\"registerButtonLabel\"\r\n                [icon]=\"isRegisterLoading ? 'pi pi-spin pi-spinner' : 'pi pi-arrow-right'\" iconPos=\"right\"\r\n                class=\"auth-btn-right teacher-button disabled-link\"\r\n                [ngClass]=\"{'teacher-bg' : registerAsTeacher, 'student-bg' : !registerAsTeacher, 'disabled-link': isRegisterLoading }\"></button>\r\n            </div>\r\n          </div>\r\n        </form>\r\n        <ng-container *ngIf=\"!registerAsTeacher && this.selectedRole.length !== 0\">\r\n          <!-- <div class=\"flex flex-column sm:flex-row gap-3 align-items-center sm:justify-content-center md:justify-content-between  mt-2\">\r\n        <button pButton pRipple type=\"button\" class=\"p-button p-button-md p-button-raised p-button-outlined btn-social sm:w-full\">\r\n          <div class=\"flex align-items-center pr-2\">\r\n            <img class=\"contact-icon-img\" src=\"/assets/icons/facebook_icon.svg\" width=\"32\" class=\"pr-2\" />\r\n            <span class=\"text-800 py-1\">Sign up with facebook</span>\r\n          </div>\r\n        </button>\r\n        <button pButton pRipple type=\"button\" class=\"p-button p-button-md p-button-raised pr-3 p-button-outlined btn-social sm:w-full\">\r\n          <div class=\"flex align-items-center pr-2\">\r\n            <img class=\"contact-icon-img\" src=\"/assets/icons/google_g_Logo.svg\" width=\"32\" class=\"pr-2\" />\r\n            <span class=\"text-800 py-1\">Sign up with Google</span>\r\n          </div>\r\n        </button>\r\n      </div> -->\r\n        </ng-container>\r\n\r\n      </ng-container>\r\n      <!-- <div *ngIf=\"is1024\" class=\"alternative\">\r\n        Already have an account? <div class=\"alternative-btn\" (click)=\"switchView()\"> Sign In</div>\r\n      </div> -->\r\n    </div>\r\n  </div>\r\n  <div class=\"form-container sign-in-container \">\r\n    <div class=\"relative h-full\">\r\n      <div class=\"title my-3 md:my-4 pb-3 mx-3 sm:mx-5 md:mx-6 lg:mx-7 align-items-start\">\r\n\r\n        <span class=\" text-center sm:text-left\">\r\n          Sign In\r\n        </span>\r\n\r\n      </div>\r\n\r\n      <ng-container *ngIf=\"isLoading\">\r\n\r\n\r\n        <div class=\"h-17rem md:h-auto\">\r\n          <div class=\"abs-centered\">\r\n            <app-loader [scale]=\"1.6\"></app-loader>\r\n          </div>\r\n        </div>\r\n\r\n      </ng-container>\r\n      <div *ngIf=\"!isLoading\" class=\"auth-form  flex flex-column mx-3 sm:mx-5 md:mx-6 lg:mx-7\">\r\n\r\n\r\n        <div class=\"grid\">\r\n          <div class=\"col-6\">\r\n          </div>\r\n        </div>\r\n\r\n        <!-- <div class=\"flex flex-column sm:flex-row gap-3 sm:gap-2 align-items-center justify-content-between\">\r\n        <button pButton pRipple type=\"button\" class=\"p-button p-button-md p-button-raised p-button-outlined btn-social sm:w-full\">\r\n          <div class=\"flex align-items-center lg:pr-3\">\r\n            <img class=\"contact-icon-img\" src=\"/assets/icons/facebook_icon.svg\" width=\"32\" class=\"pr-2\" />\r\n            <span class=\"text-800 py-1\">Login with facebook</span>\r\n          </div>\r\n        </button>\r\n        <button pButton pRipple type=\"button\" class=\"p-button p-button-md p-button-raised pr-3 p-button-outlined btn-social sm:w-full\">\r\n          <div class=\"flex align-items-center lg:pr-3\">\r\n            <img class=\"contact-icon-img\" src=\"/assets/icons/google_g_Logo.svg\" width=\"32\" class=\"pr-2\" />\r\n            <span class=\"text-800 py-1\">Login with Google</span>\r\n          </div>\r\n        </button>\r\n      </div> -->\r\n        <form (submit)=\"onLogin(loginForm)\" #loginForm=\"ngForm\">\r\n          <div class=\"input-field\">\r\n            <div class=\"input-element-title\">Email</div>\r\n            <input name=\"email\" ngModel type=\"email\" placeholder=\"Email\" #emailInput=\"ngModel\" required email\r\n              class=\"input-element\" type=\"text\">\r\n            <div *ngIf=\"emailError\" class=\"input-error\">Email is not valid</div>\r\n          </div>\r\n          <div class=\"input-field\">\r\n            <div class=\"input-element-title\">Password</div>\r\n\r\n            <span class=\"p-input-icon-right\" style=\"width: 100%;\">\r\n              <i class=\"pi\" (click)=\"togglemyPasswordFieldType('email')\"\r\n                [ngClass]=\"getKeyValueFieldType('email')? 'pi-eye': 'pi-eye-slash'\" style=\"margin-top:-2px;\"></i>\r\n              <input name=\"password\" ngModel [type]=\"getKeyValueFieldType('email') ? 'text' : 'password'\"\r\n                placeholder=\"Password\" #passwordInput=\"ngModel\" required class=\"input-element\" />\r\n            </span>\r\n          </div>\r\n          <div class=\"input-field pt-2\">\r\n            <button pButton type=\"submit\" styleClass=\"bg-primary\" label=\"Sign In\" class=\"mt-3\"\r\n              class=\"sign-in-btn hvr-glow\"></button>\r\n          </div>\r\n        </form>\r\n        <div *ngIf=\"is1024\" class=\"alternative\">\r\n          Don't have an account? <div class=\"alternative-btn\" (click)=\"switchView(true, true)\"> Sign Up</div>\r\n        </div>\r\n        <div class=\"alternative\" (click)=\"showForgotPassword = !showForgotPassword\">\r\n          Forgot password? <div class=\"alternative-btn\"> Get it here</div>\r\n        </div>\r\n        <form *ngIf=\"showForgotPassword\" (submit)=\"onForgot(forgotForm)\" #forgotForm=\"ngForm\">\r\n          <div class=\"input-field\">\r\n            <div class=\"input-element-title\">Email</div>\r\n            <div style=\"font-size:12px; margin-top:10px;\">Please enter your email address to receive the password reset\r\n              instructions. If you don’t see the email in your inbox, please check your spam or junk folder.</div>\r\n            <input name=\"email\" ngModel type=\"email\" placeholder=\"Email\" #emailInputForgot=\"ngModel\" required email\r\n              class=\"input-element\" type=\"text\">\r\n            <div *ngIf=\"emailError\" class=\"input-error\">Email is not valid</div>\r\n          </div>\r\n\r\n          <div class=\"flex mt-3\">\r\n            <button pButton type=\"submit\" styleClass=\"bg-primary\" label=\"Submit\" class=\"sign-in-btn hvr-glow\"></button>\r\n          </div>\r\n        </form>\r\n        <!-- <div class=\"alternative\">\r\n        Change password? <div class=\"alternative-btn\"> Change it here</div>\r\n      </div> -->\r\n      </div>\r\n\r\n    </div>\r\n  </div>\r\n  <div class=\"overlay-container\">\r\n    <div class=\"overlay\">\r\n      <div class=\"overlay-panel overlay-left left-side-content justify-content-between gap-2 py-5\">\r\n        <div class=\"logo my-3 md:my-0\">\r\n          <a href=\"https://mylingotrip.com\" target=\"_blank\">\r\n          <img src=\"/assets/icons/logo/mlt_logo-new.svg\" width=\"100\">\r\n          </a>\r\n        </div>\r\n\r\n        <button pButton type=\"button\" (click)=\"handleSwitchViewClick()\" [label]=\"'Sign Up'\"\r\n          class=\"p-button-outlined auth-btn\"></button>\r\n\r\n        <div>\r\n          <h1 class=\"title text-center line-height-1\">Welcome!</h1>\r\n          <h2 class=\"m-0\"><a class=\"fl-typo s12-16 cursor-pointer hover:underline mt-2 block\"\r\n              (click)=\"handleSwitchViewClick()\">Sign Up if you don't\r\n              have an account</a>\r\n          </h2>\r\n        </div>\r\n      </div>\r\n      <div class=\"overlay-panel overlay-right left-side-content justify-content-between gap-2 py-5\">\r\n        <div class=\"logo my-3 md:my-0\">\r\n          \r\n          <a href=\"https://mylingotrip.com\" target=\"_blank\">\r\n          <img src=\"/assets/icons/logo/mlt_logo-new.svg\" width=\"100\">\r\n          </a>\r\n        </div>\r\n\r\n        <div class=\"flex gap-3 flex-column\">\r\n\r\n          <button pButton type=\"button\" (click)=\"handleSwitchViewClick()\" [label]=\"'Sign In'\"\r\n            class=\"p-button-outlined auth-btn\"></button>\r\n\r\n          <div>\r\n            <a class=\"cursor-pointer hover:underline\" (click)=\"handleSwitchViewClick()\">Sign-In if you are already a\r\n              member</a>\r\n          </div>\r\n        </div>\r\n\r\n        <div>\r\n\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n}@placeholder {\r\n<div class=\"h-17rem md:h-auto\">\r\n  <div class=\"abs-centered\">\r\n    <app-loader [scale]=\"1.6\"></app-loader>\r\n  </div>\r\n</div>\r\n}"], "mappings": "AAAA,SAASA,YAAY,EAAEC,QAAQ,QAAkB,iBAAiB;AAClE,SAA8EC,MAAM,QAAmB,eAAe;AACtH,SAA0BC,WAAW,EAAUC,mBAAmB,EAAEC,kBAAkB,EAAiCC,UAAU,QAAQ,gBAAgB;AACzJ,SAASC,wBAAwB,QAAQ,oCAAoC;AAE7E,SAASC,eAAe,QAA4B,cAAc;AAClE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,QAAQ,EAAcC,EAAE,QAAQ,MAAM;AAC/C,SAASC,SAAS,EAAEC,IAAI,QAAQ,gBAAgB;AAChD,SAAeC,QAAQ,EAAEC,UAAU,QAAQ,gCAAgC;AAQ3E,SAASC,UAAU,EAAEC,oBAAoB,QAAQ,+BAA+B;AAChF,SAASC,eAAe,QAAQ,wCAAwC;AACxE,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;;;;;2RAW7BD,eAAe,EAAAE,GAAA,CAAAC,kBAAA;;;;;;;;;;;ICXfC,EAAA,CAAAC,uBAAA,GAAgC;IAI5BD,EADF,CAAAE,cAAA,cAA+B,cACH;IACxBF,EAAA,CAAAG,SAAA,qBAAuC;IAE3CH,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAFUJ,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAM,UAAA,cAAa;;;;;IAqDvBN,EAAA,CAAAE,cAAA,cAA4C;IAAAF,EAAA,CAAAO,MAAA,0BAAmB;IAAAP,EAAA,CAAAI,YAAA,EAAM;;;;;IAenEJ,EADF,CAAAE,cAAA,UAAkD,cACF;IAAAF,EAAA,CAAAO,MAAA,uDAAgD;IAChGP,EADgG,CAAAI,YAAA,EAAM,EAChG;;;;;IAEJJ,EADF,CAAAE,cAAA,UAAsD,cACN;IAAAF,EAAA,CAAAO,MAAA,uEACtC;IAEVP,EAFU,CAAAI,YAAA,EAAM,EAEV;;;;;;IAiBNJ,EAAA,CAAAE,cAAA,cAA8C;IAAAF,EAAA,CAAAO,MAAA,8BAAuB;IAAAP,EAAA,CAAAI,YAAA,EAAM;;;;;IAiB3EJ,EADF,CAAAE,cAAA,cAAqF,cAC7D;IACpBF,EAAA,CAAAG,SAAA,iBAGkI;IAEtIH,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAL4BJ,EAAA,CAAAK,SAAA,GAA6B;IAGzDL,EAH4B,CAAAM,UAAA,UAAAE,MAAA,CAAAC,mBAAA,CAA6B,SAAAD,MAAA,CAAAE,iBAAA,iDACiB,YAAAV,EAAA,CAAAW,eAAA,IAAAC,GAAA,EAAAJ,MAAA,CAAAK,iBAAA,GAAAL,MAAA,CAAAK,iBAAA,EAAAL,MAAA,CAAAE,iBAAA,EAE4C;;;;;IAI9HV,EAAA,CAAAc,kBAAA,GAee;;;;;;IAzGjBd,EAAA,CAAAC,uBAAA,GAAmD;IACjDD,EAAA,CAAAE,cAAA,cAAiE;IAC/DF,EAAA,CAAAO,MAAA,gBACF;IAAAP,EAAA,CAAAI,YAAA,EAAM;IAKAJ,EAHN,CAAAE,cAAA,cAAkB,cACG,cACmB,qBAEoC;IADjCF,EAAA,CAAAe,UAAA,sBAAAC,+EAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAYZ,MAAA,CAAAa,QAAA,EAAU;IAAA,EAAC;IAC9BrB,EAAA,CAAAsB,gBAAA,2BAAAC,oFAAAC,MAAA;MAAAxB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAAnB,EAAA,CAAAyB,kBAAA,CAAAjB,MAAA,CAAAkB,YAAA,EAAAF,MAAA,MAAAhB,MAAA,CAAAkB,YAAA,GAAAF,MAAA;MAAA,OAAAxB,EAAA,CAAAoB,WAAA,CAAAI,MAAA;IAAA,EAA0B;IAE5DxB,EAF0E,CAAAI,YAAA,EAAa,EAC/E,EACF;IAGFJ,EAFJ,CAAAE,cAAA,cAAmB,cACmB,qBAGnB;IAF0DF,EAAA,CAAAe,UAAA,sBAAAY,+EAAA;MAAA3B,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAYZ,MAAA,CAAAa,QAAA,EAAU;IAAA,EAAC;IACpCrB,EAAA,CAAAsB,gBAAA,2BAAAM,oFAAAJ,MAAA;MAAAxB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAAnB,EAAA,CAAAyB,kBAAA,CAAAjB,MAAA,CAAAkB,YAAA,EAAAF,MAAA,MAAAhB,MAAA,CAAAkB,YAAA,GAAAF,MAAA;MAAA,OAAAxB,EAAA,CAAAoB,WAAA,CAAAI,MAAA;IAAA,EAA0B;IAI5FxB,EAHqB,CAAAI,YAAA,EAAa,EACxB,EACF,EACF;IAGNJ,EAAA,CAAAE,cAAA,mBAAgF;IAA1EF,EAAA,CAAAe,UAAA,sBAAAc,0EAAA;MAAA7B,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAY,aAAA,GAAA9B,EAAA,CAAA+B,WAAA;MAAA,MAAAvB,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAYZ,MAAA,CAAAwB,QAAA,CAAAF,aAAA,CAAoB;IAAA,EAAC;IAEnC9B,EADF,CAAAE,cAAA,eAAyB,eACU;IAAAF,EAAA,CAAAO,MAAA,gBAAQ;IAAAP,EAAA,CAAAI,YAAA,EAAM;IAC/CJ,EAAA,CAAAG,SAAA,oBAC6C;IAC7CH,EAAA,CAAAiC,UAAA,KAAAC,sDAAA,kBAA4C;IAC9ClC,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAE,cAAA,eAAyB,eACU;IAAAF,EAAA,CAAAO,MAAA,kBAAU;IAAAP,EAAA,CAAAI,YAAA,EAAM;IAE/CJ,EADF,CAAAE,cAAA,gBAAsD,aAE8C;IADpFF,EAAA,CAAAe,UAAA,mBAAAoB,oEAAA;MAAAnC,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASZ,MAAA,CAAA4B,yBAAA,CAA0B,UAAU,CAAC;IAAA,EAAC;IACqCpC,EAAA,CAAAI,YAAA,EAAI;IACtGJ,EAAA,CAAAE,cAAA,oBAGyC;IAHlBF,EAAA,CAAAsB,gBAAA,2BAAAe,gFAAAb,MAAA;MAAAxB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAAnB,EAAA,CAAAyB,kBAAA,CAAAjB,MAAA,CAAA8B,QAAA,EAAAd,MAAA,MAAAhB,MAAA,CAAA8B,QAAA,GAAAd,MAAA;MAAA,OAAAxB,EAAA,CAAAoB,WAAA,CAAAI,MAAA;IAAA,EAAsB;IAM/CxB,EANE,CAAAI,YAAA,EAGyC,EAGpC;IAUPJ,EATA,CAAAiC,UAAA,KAAAM,sDAAA,kBAAkD,KAAAC,sDAAA,kBAGI,KAAAC,8DAAA,gCAAAzC,EAAA,CAAA0C,sBAAA,CAM3B;IAK7B1C,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAE,cAAA,eAAyB,eACU;IAAAF,EAAA,CAAAO,MAAA,0BAAkB;IAAAP,EAAA,CAAAI,YAAA,EAAM;IAEvDJ,EADF,CAAAE,cAAA,gBAAsD,aAE6C;IADnFF,EAAA,CAAAe,UAAA,mBAAA4B,oEAAA;MAAA3C,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASZ,MAAA,CAAA4B,yBAAA,CAA0B,SAAS,CAAC;IAAA,EAAC;IACqCpC,EAAA,CAAAI,YAAA,EAAI;IACrGJ,EAAA,CAAAG,SAAA,oBAE4D;IAC9DH,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAiC,UAAA,KAAAW,sDAAA,kBAA8C;IAChD5C,EAAA,CAAAI,YAAA,EAAM;IAIJJ,EAHF,CAAAE,cAAA,eAC6D,sBAGsB;IAA9DF,EAAA,CAAAsB,gBAAA,2BAAAuB,qFAAArB,MAAA;MAAAxB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAAnB,EAAA,CAAAyB,kBAAA,CAAAjB,MAAA,CAAAsC,WAAA,EAAAtB,MAAA,MAAAhB,MAAA,CAAAsC,WAAA,GAAAtB,MAAA;MAAA,OAAAxB,EAAA,CAAAoB,WAAA,CAAAI,MAAA;IAAA,EAAyB;IAAqCxB,EAAA,CAAAI,YAAA,EAAa;IAAAJ,EAAA,CAAAE,cAAA,aAEtD;IAAAF,EAAA,CAAAO,MAAA,6BAAqB;IAC/DP,EAD+D,CAAAI,YAAA,EAAI,EAC7D;IACNJ,EAAA,CAAAC,uBAAA,IAAc;IAEVD,EADF,CAAAE,cAAA,eAAgF,sBAEzB;IADzCF,EAAA,CAAAe,UAAA,sBAAAgC,gFAAAvB,MAAA;MAAAxB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAYZ,MAAA,CAAAwC,oBAAA,CAAAxB,MAAA,CAA4B;IAAA,EAAC;IAEvDxB,EADuD,CAAAI,YAAA,EAAa,EAC9D;;IAERJ,EAAA,CAAAiC,UAAA,KAAAgB,sDAAA,kBAAqF;IAQvFjD,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAiC,UAAA,KAAAiB,+DAAA,2BAA2E;;;;;;;IAjFvClD,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAmD,gBAAA,YAAA3C,MAAA,CAAAkB,YAAA,CAA0B;IAMI1B,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAmD,gBAAA,YAAA3C,MAAA,CAAAkB,YAAA,CAA0B;IAYlF1B,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAA4C,UAAA,CAAgB;IAMlBpD,EAAA,CAAAK,SAAA,GAAsE;IAAtEL,EAAA,CAAAM,UAAA,YAAAE,MAAA,CAAA6C,oBAAA,yCAAsE;IACjDrD,EAAA,CAAAK,SAAA,EAAsB;IAAtBL,EAAA,CAAAmD,gBAAA,YAAA3C,MAAA,CAAA8B,QAAA,CAAsB;IAC3CtC,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAA6C,oBAAA,mCAA+D;IAM7DrD,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAM,UAAA,SAAAgD,gBAAA,IAAAA,gBAAA,CAAAC,KAAA,CAA0C;IAG1CvD,EAAA,CAAAK,SAAA,EAA6B;IAAAL,EAA7B,CAAAM,UAAA,SAAAgD,gBAAA,CAAAE,OAAA,CAA6B,aAAAC,eAAA,CAAiB;IAgBhDzD,EAAA,CAAAK,SAAA,GAAqE;IAArEL,EAAA,CAAAM,UAAA,YAAAE,MAAA,CAAA6C,oBAAA,wCAAqE;IACjDrD,EAAA,CAAAK,SAAA,EAA8D;IAA9DL,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAA6C,oBAAA,kCAA8D;IAIhFrD,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAAkD,YAAA,CAAkB;IAML1D,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAmD,gBAAA,YAAA3C,MAAA,CAAAsC,WAAA,CAAyB;IAAe9C,EAAA,CAAAM,UAAA,UAAAE,MAAA,CAAAsC,WAAA,CAAqB;IAUnC9C,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAAkB,YAAA,CAAAiC,MAAA,OAAoC;IAStE3D,EAAA,CAAAK,SAAA,EAA0D;IAA1DL,EAAA,CAAAM,UAAA,UAAAE,MAAA,CAAAK,iBAAA,IAAAL,MAAA,CAAAkB,YAAA,CAAAiC,MAAA,OAA0D;;;;;IAiC3E3D,EAAA,CAAAC,uBAAA,GAAgC;IAI5BD,EADF,CAAAE,cAAA,cAA+B,cACH;IACxBF,EAAA,CAAAG,SAAA,qBAAuC;IAE3CH,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAFUJ,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAM,UAAA,cAAa;;;;;IAgCzBN,EAAA,CAAAE,cAAA,cAA4C;IAAAF,EAAA,CAAAO,MAAA,yBAAkB;IAAAP,EAAA,CAAAI,YAAA,EAAM;;;;;;IAiBxEJ,EAAA,CAAAE,cAAA,cAAwC;IACtCF,EAAA,CAAAO,MAAA,+BAAuB;IAAAP,EAAA,CAAAE,cAAA,cAA8D;IAAjCF,EAAA,CAAAe,UAAA,mBAAA6C,mEAAA;MAAA5D,EAAA,CAAAiB,aAAA,CAAA4C,GAAA;MAAA,MAAArD,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASZ,MAAA,CAAAsD,UAAA,CAAW,IAAI,EAAE,IAAI,CAAC;IAAA,EAAC;IAAE9D,EAAA,CAAAO,MAAA,eAAO;IAC/FP,EAD+F,CAAAI,YAAA,EAAM,EAC/F;;;;;IAWFJ,EAAA,CAAAE,cAAA,cAA4C;IAAAF,EAAA,CAAAO,MAAA,yBAAkB;IAAAP,EAAA,CAAAI,YAAA,EAAM;;;;;;IAPxEJ,EAAA,CAAAE,cAAA,kBAAsF;IAArDF,EAAA,CAAAe,UAAA,oBAAAgD,sEAAA;MAAA/D,EAAA,CAAAiB,aAAA,CAAA+C,IAAA;MAAA,MAAAC,cAAA,GAAAjE,EAAA,CAAA+B,WAAA;MAAA,MAAAvB,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAUZ,MAAA,CAAA0D,QAAA,CAAAD,cAAA,CAAoB;IAAA,EAAC;IAE5DjE,EADF,CAAAE,cAAA,cAAyB,cACU;IAAAF,EAAA,CAAAO,MAAA,YAAK;IAAAP,EAAA,CAAAI,YAAA,EAAM;IAC5CJ,EAAA,CAAAE,cAAA,cAA8C;IAAAF,EAAA,CAAAO,MAAA,wKACkD;IAAAP,EAAA,CAAAI,YAAA,EAAM;IACtGJ,EAAA,CAAAG,SAAA,mBACoC;IACpCH,EAAA,CAAAiC,UAAA,IAAAkC,oDAAA,kBAA4C;IAC9CnE,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAE,cAAA,eAAuB;IACrBF,EAAA,CAAAG,SAAA,kBAA2G;IAE/GH,EADE,CAAAI,YAAA,EAAM,EACD;;;;IANGJ,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAA4C,UAAA,CAAgB;;;;;;IAtD1BpD,EAHF,CAAAE,cAAA,cAAyF,cAGrE;IAChBF,EAAA,CAAAG,SAAA,cACM;IACRH,EAAA,CAAAI,YAAA,EAAM;IAgBNJ,EAAA,CAAAE,cAAA,kBAAwD;IAAlDF,EAAA,CAAAe,UAAA,oBAAAqD,8DAAA;MAAApE,EAAA,CAAAiB,aAAA,CAAAoD,GAAA;MAAA,MAAAC,YAAA,GAAAtE,EAAA,CAAA+B,WAAA;MAAA,MAAAvB,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAUZ,MAAA,CAAA+D,OAAA,CAAAD,YAAA,CAAkB;IAAA,EAAC;IAE/BtE,EADF,CAAAE,cAAA,cAAyB,cACU;IAAAF,EAAA,CAAAO,MAAA,YAAK;IAAAP,EAAA,CAAAI,YAAA,EAAM;IAC5CJ,EAAA,CAAAG,SAAA,mBACoC;IACpCH,EAAA,CAAAiC,UAAA,KAAAuC,6CAAA,kBAA4C;IAC9CxE,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAE,cAAA,eAAyB,eACU;IAAAF,EAAA,CAAAO,MAAA,gBAAQ;IAAAP,EAAA,CAAAI,YAAA,EAAM;IAG7CJ,EADF,CAAAE,cAAA,gBAAsD,aAE2C;IADjFF,EAAA,CAAAe,UAAA,mBAAA0D,2DAAA;MAAAzE,EAAA,CAAAiB,aAAA,CAAAoD,GAAA;MAAA,MAAA7D,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASZ,MAAA,CAAA4B,yBAAA,CAA0B,OAAO,CAAC;IAAA,EAAC;IACqCpC,EAAA,CAAAI,YAAA,EAAI;IACnGJ,EAAA,CAAAG,SAAA,oBACmF;IAEvFH,EADE,CAAAI,YAAA,EAAO,EACH;IACNJ,EAAA,CAAAE,cAAA,eAA8B;IAC5BF,EAAA,CAAAG,SAAA,kBACwC;IAE5CH,EADE,CAAAI,YAAA,EAAM,EACD;IACPJ,EAAA,CAAAiC,UAAA,KAAAyC,6CAAA,kBAAwC;IAGxC1E,EAAA,CAAAE,cAAA,eAA4E;IAAnDF,EAAA,CAAAe,UAAA,mBAAA4D,6DAAA;MAAA3E,EAAA,CAAAiB,aAAA,CAAAoD,GAAA;MAAA,MAAA7D,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAAZ,MAAA,CAAAoE,kBAAA,IAAApE,MAAA,CAAAoE,kBAAA;IAAA,EAAkD;IACzE5E,EAAA,CAAAO,MAAA,0BAAiB;IAAAP,EAAA,CAAAE,cAAA,eAA6B;IAACF,EAAA,CAAAO,MAAA,oBAAW;IAC5DP,EAD4D,CAAAI,YAAA,EAAM,EAC5D;IACNJ,EAAA,CAAAiC,UAAA,KAAA4C,8CAAA,oBAAsF;IAiBxF7E,EAAA,CAAAI,YAAA,EAAM;;;;IAxCMJ,EAAA,CAAAK,SAAA,IAAgB;IAAhBL,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAA4C,UAAA,CAAgB;IAOlBpD,EAAA,CAAAK,SAAA,GAAmE;IAAnEL,EAAA,CAAAM,UAAA,YAAAE,MAAA,CAAA6C,oBAAA,sCAAmE;IACtCrD,EAAA,CAAAK,SAAA,EAA4D;IAA5DL,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAA6C,oBAAA,gCAA4D;IAS3FrD,EAAA,CAAAK,SAAA,GAAY;IAAZL,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAAsE,MAAA,CAAY;IAMX9E,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAAoE,kBAAA,CAAwB;;;;;;IAxNrC5E,EADF,CAAAE,cAAA,aAAkG,aAClD;IAE5CF,EAAA,CAAAiC,UAAA,IAAA8C,8CAAA,2BAAgC;IAc1B/E,EAJN,CAAAE,cAAA,cAA0E,cAE5B,cACiC,cAC9C;IAEzBF,EAAA,CAAAG,SAAA,mBAA2B;IAEUH,EAArC,CAAAE,cAAA,cAAqC,gBAAyC;IAAAF,EAAA,CAAAO,MAAA,oDAE/D;IASrBP,EATqB,CAAAI,YAAA,EAAO,EAAM,EAOxB,EACF,EACF;IAENJ,EAAA,CAAAiC,UAAA,KAAA+C,+CAAA,6BAAmD;IAgHvDhF,EADE,CAAAI,YAAA,EAAM,EACF;IAKAJ,EAJN,CAAAE,cAAA,eAA+C,eAChB,eACyD,gBAE1C;IACtCF,EAAA,CAAAO,MAAA,iBACF;IAEFP,EAFE,CAAAI,YAAA,EAAO,EAEH;IAYNJ,EAVA,CAAAiC,UAAA,KAAAgD,+CAAA,2BAAgC,KAAAC,sCAAA,mBAUyD;IAsE7FlF,EADE,CAAAI,YAAA,EAAM,EACF;IAKEJ,EAJR,CAAAE,cAAA,eAA+B,eACR,eAC0E,eAC5D,aACqB;IAClDF,EAAA,CAAAG,SAAA,eAA2D;IAE7DH,EADE,CAAAI,YAAA,EAAI,EACA;IAENJ,EAAA,CAAAE,cAAA,kBACqC;IADPF,EAAA,CAAAe,UAAA,mBAAAoE,yDAAA;MAAAnF,EAAA,CAAAiB,aAAA,CAAAmE,GAAA;MAAA,MAAA5E,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASZ,MAAA,CAAA6E,qBAAA,EAAuB;IAAA,EAAC;IAC1BrF,EAAA,CAAAI,YAAA,EAAS;IAG5CJ,EADF,CAAAE,cAAA,WAAK,cACyC;IAAAF,EAAA,CAAAO,MAAA,gBAAQ;IAAAP,EAAA,CAAAI,YAAA,EAAK;IACzCJ,EAAhB,CAAAE,cAAA,cAAgB,aACsB;IAAlCF,EAAA,CAAAe,UAAA,mBAAAuE,oDAAA;MAAAtF,EAAA,CAAAiB,aAAA,CAAAmE,GAAA;MAAA,MAAA5E,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASZ,MAAA,CAAA6E,qBAAA,EAAuB;IAAA,EAAC;IAACrF,EAAA,CAAAO,MAAA,4CACnB;IAGvBP,EAHuB,CAAAI,YAAA,EAAI,EAClB,EACD,EACF;IAIFJ,EAHJ,CAAAE,cAAA,eAA8F,eAC7D,aAEqB;IAClDF,EAAA,CAAAG,SAAA,eAA2D;IAE7DH,EADE,CAAAI,YAAA,EAAI,EACA;IAIJJ,EAFF,CAAAE,cAAA,eAAoC,kBAGG;IADPF,EAAA,CAAAe,UAAA,mBAAAwE,yDAAA;MAAAvF,EAAA,CAAAiB,aAAA,CAAAmE,GAAA;MAAA,MAAA5E,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASZ,MAAA,CAAA6E,qBAAA,EAAuB;IAAA,EAAC;IAC1BrF,EAAA,CAAAI,YAAA,EAAS;IAG5CJ,EADF,CAAAE,cAAA,WAAK,aACyE;IAAlCF,EAAA,CAAAe,UAAA,mBAAAyE,oDAAA;MAAAxF,EAAA,CAAAiB,aAAA,CAAAmE,GAAA;MAAA,MAAA5E,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASZ,MAAA,CAAA6E,qBAAA,EAAuB;IAAA,EAAC;IAACrF,EAAA,CAAAO,MAAA,2CACpE;IAEZP,EAFY,CAAAI,YAAA,EAAI,EACR,EACF;IAENJ,EAAA,CAAAG,SAAA,WAEM;IAIdH,EAHM,CAAAI,YAAA,EAAM,EACF,EACF,EACF;;;;IA3R4CJ,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAyF,eAAA,IAAAC,GAAA,EAAAlF,MAAA,CAAAmF,WAAA,EAA+C;IAG9E3F,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAAoF,SAAA,CAAe;IAUzB5F,EAAA,CAAAK,SAAA,EAAoB;IAApBL,EAAA,CAAAM,UAAA,WAAAE,MAAA,CAAAoF,SAAA,CAAoB;IAElB5F,EAAA,CAAAK,SAAA,EAAsC;IAAtCL,EAAA,CAAAM,UAAA,YAAAE,MAAA,CAAAqF,0BAAA,CAAsC;IAmB5B7F,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,UAAA,UAAAE,MAAA,CAAAqF,0BAAA,CAAkC;IA2HlC7F,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAAoF,SAAA,CAAe;IAUxB5F,EAAA,CAAAK,SAAA,EAAgB;IAAhBL,EAAA,CAAAM,UAAA,UAAAE,MAAA,CAAAoF,SAAA,CAAgB;IAgF4C5F,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,UAAA,oBAAmB;IAqBjBN,EAAA,CAAAK,SAAA,IAAmB;IAAnBL,EAAA,CAAAM,UAAA,oBAAmB;;;;;IAmB3FN,EADF,CAAAE,cAAA,cAA+B,cACH;IACxBF,EAAA,CAAAG,SAAA,qBAAuC;IAE3CH,EADE,CAAAI,YAAA,EAAM,EACF;;;IAFUJ,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAM,UAAA,cAAa;;;ADzQ7B,OAAM,MAAOwF,cAAc;EAsCzBC,YACUC,YAA0B,EAC1BC,cAA8B,EAC9BC,WAAwB,EACzBC,WAAwB,EACxBC,WAAwB,EACxBC,yBAAoD,EACpDC,mBAAwC,EACvCC,MAAc,EACdC,IAAY,EACMC,QAAkB,EACpCC,QAAmB,EACnBC,QAAkB,EAClBC,cAA8B;IAZ9B,KAAAZ,YAAY,GAAZA,YAAY;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAClB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACc,KAAAC,QAAQ,GAARA,QAAQ;IAC1B,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,cAAc,GAAdA,cAAc;IAlDhB,KAAAC,IAAI,GAAG,IAAIhH,OAAO,EAAE;IAC5B,KAAA+F,SAAS,GAAG,KAAK;IACjB,KAAAlF,iBAAiB,GAAG,KAAK;IACzB,KAAAoC,WAAW,GAAG,KAAK;IAInB,KAAA6C,WAAW,GAAY,IAAI;IAC3B,KAAAmB,YAAY,GAAY,KAAK;IAC7B,KAAAjG,iBAAiB,GAAY,KAAK;IAClC,KAAAkG,KAAK,GAAY,KAAK;IACtB,KAAAjC,MAAM,GAAY,KAAK;IACvB,KAAAkC,MAAM,GAAY,KAAK;IACvB,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,KAAK,GAAY,KAAK;IACtB,KAAA9D,UAAU,GAAY,KAAK;IAC3B,KAAAM,YAAY,GAAY,KAAK;IAC7B,KAAAmC,0BAA0B,GAAY,KAAK;IAC3C,KAAAjB,kBAAkB,GAAY,KAAK;IACnC,KAAAlD,YAAY,GAAa,EAAE;IAC3B,KAAAjB,mBAAmB,GAAG,qBAAqB;IAE3C,KAAA0G,iBAAiB,GAAmC;MAClDC,KAAK,EAAE,KAAK;MACZ9E,QAAQ,EAAE,KAAK;MACf+E,OAAO,EAAE;KACV;IACD,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,mBAAmB,GAAG,IAAIzI,kBAAkB,CAAC,EAAE,EAAE,CAC/CC,UAAU,CAACyI,QAAQ,EACnBzI,UAAU,CAAC0I,OAAO,CAAC,4BAA4B,CAAC,CACjD,CAAC;IACF,KAAAC,UAAU,GAAG,IAAI;IACjB,KAAAC,WAAW,GAAG,IAAI;IAElB,KAAAC,eAAe,GAAGjJ,MAAM,CAAC,KAAK,CAAC;IACiB,KAAAkJ,cAAc,GAAe,EAAgB;IAgB3F,IAAI,CAACC,OAAO,GAAQ,IAAI,CAACrB,QAAQ,CAACsB,WAAW;IAC7CC,OAAO,CAACC,GAAG,CAACrB,cAAc,CAACsB,UAAU,CAAC;IACtC;EAEF;EAEAC,QAAQA,CAAA;IAEN;IACA,IAAI,CAACC,SAAS,GAAG,IAAI,CAACxB,cAAc,CAACyB,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAIC,SAAS;IACnF,IAAI,IAAI,CAAChC,MAAM,CAACiC,GAAG,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MACrC,IAAI,CAAC9C,WAAW,GAAG,IAAI;IACzB;IACA,IAAI,IAAI,CAACY,MAAM,CAACiC,GAAG,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;MACxC,IAAI,CAAC9C,WAAW,GAAG,KAAK;IAC1B;IAEA,IAAI,CAACkB,IAAI,CAAC6B,GAAG,CAAC,IAAI,CAACvC,WAAW,CAACwC,qBAAqB,EAAE,CAACC,SAAS,CAACC,UAAU,IAAG;MAC5E,IAAIA,UAAU,EAAE;QACd;MAAA;IAEJ,CAAC,CAAC,CAAC;IACH,IAAI,CAAChC,IAAI,CAAC6B,GAAG,CAAC,IAAI,CAACzC,cAAc,CAAC6C,UAAU,CAACC,IAAI,CAACxJ,IAAI,CAAC,CAAC,CAAC,CAAC,CAACqJ,SAAS,CAACI,GAAG,IAAG;MACzE,IAAIA,GAAG,EAAE;QACP,IAAI,CAAClE,MAAM,GAAGkE,GAAG,CAAClE,MAAM;QACxB,IAAI,CAACkC,MAAM,GAAGgC,GAAG,CAAChC,MAAM;QACxB,IAAI,CAACD,KAAK,GAAGiC,GAAG,CAACjC,KAAK;QACtB,IAAI,CAACG,KAAK,GAAG8B,GAAG,CAAC9B,KAAK;QACtB,IAAI,CAACD,QAAQ,GAAG+B,GAAG,CAACC,OAAQ;QAC5B,IAAI,IAAI,CAAChC,QAAQ,EAAE;UAEjB;QAAA;QAEFe,OAAO,CAACC,GAAG,CAACe,GAAG,CAAC;MAClB;IACF,CAAC,CAAC,CAAC;IACH,IAAI,CAACE,kCAAkC,EAAE;IACzC;EACF;EAEOC,kBAAkBA,CAAA,GAEzB;EAEOC,UAAUA,CAACZ,GAAW;IAC3BR,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACnC,IAAIoB,IAAI,GAAG5C,QAAQ,CAAC6C,aAAa,CAAC,QAAQ,CAAC;IAC3CD,IAAI,CAACE,YAAY,CAAC,IAAI,EAAE,eAAe,CAAC;IACxCF,IAAI,CAACG,GAAG,GAAGhB,GAAG;IACda,IAAI,CAACI,IAAI,GAAG,iBAAiB;IAC7BJ,IAAI,CAACK,KAAK,GAAG,IAAI;IACjBL,IAAI,CAACM,OAAO,GAAG,OAAO;IACtBlD,QAAQ,CAACmD,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAACC,WAAW,CAACR,IAAI,CAAC;EAC5D;EACAS,eAAeA,CAAA,GACf;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAClD,IAAI,CAACmD,WAAW,EAAE;IACvB;IACA;EACF;EAEAd,kCAAkCA,CAAA;IAChC,IAAI,CAACrC,IAAI,CAAC6B,GAAG,CAAC,IAAI,CAACvC,WAAW,CAAC8D,kCAAkC,CAACrB,SAAS,CAACI,GAAG,IAAG;MAChFhB,OAAO,CAACC,GAAG,CAACe,GAAG,CAAC;MAChB,IAAI,CAACnD,0BAA0B,GAAGmD,GAAG;MACrC,IAAIA,GAAG,EAAE;QACP,IAAI,CAACkB,iCAAiC,CAAC,IAAI,CAACrC,cAAc,CAAC;MAC7D;IACF,CAAC,CAAC,CAAC;EACL;EAEAsC,cAAcA,CAACC,IAAS;IACtB,IAAI,CAACvJ,iBAAiB,GAAGuJ,IAAI,CAACC,OAAO;EACvC;EAEAvG,UAAUA,CAACwG,OAAO,GAAG,KAAK,EAAEC,iBAAiB,GAAG,KAAK;IACnD,IAAI,CAAC1E,0BAA0B,GAAG,KAAK;IAEvC,IAAI0E,iBAAiB,EAAE;MACrB,IAAI,CAAC5C,WAAW,GAAG4C,iBAAiB;IACtC;IACA,IAAI,IAAI,CAACrD,KAAK,IAAI,IAAI,CAACF,MAAM,IAAIsD,OAAO,EAAE;MACxC,IAAI,IAAI,CAAC3E,WAAW,EAAE;QACpB,IAAI,IAAI,CAACsB,QAAQ,EAAE;UAEjB,MAAMuD,KAAK,GAAG/D,QAAQ,CAACgE,cAAc,CAAC,WAAW,CAAC;UAClD,MAAMC,KAAK,GAAGjE,QAAQ,CAACgE,cAAc,CAAC,YAAY,CAAC;UAEnD;UACA;UACA;UACA;UACA;UACA;UAEA;UAEA;UAEAhE,QAAQ,CAACgE,cAAc,CAAC,WAAW,CAAE,CAACE,KAAK,CAACC,IAAI,GAAG,OAAO;UAC1DnE,QAAQ,CAACgE,cAAc,CAAC,WAAW,CAAE,CAACE,KAAK,CAACE,YAAY,GAAG,iBAAiB;UAC5EpE,QAAQ,CAACgE,cAAc,CAAC,YAAY,CAAE,CAACE,KAAK,CAACE,YAAY,GAAG,iBAAiB;UAC7EpE,QAAQ,CAACgE,cAAc,CAAC,YAAY,CAAE,CAACE,KAAK,CAACG,KAAK,GAAG,OAAO;QAC9D;QACA,MAAMtC,GAAG,GAAG,IAAI,CAACjC,MAAM,CAACwE,aAAa,CAAC,CAAC,gBAAgB,CAAC,EAAE;UAAEC,UAAU,EAAE,IAAI,CAACpE;QAAc,CAAE,CAAC,CAACqE,QAAQ,EAAE;QACzG,IAAI,CAACtE,QAAQ,CAACuE,EAAE,CAAC1C,GAAG,CAAC;MACvB,CAAC,MAAM;QACL,IAAI,IAAI,CAACvB,QAAQ,EAAE;UACjBR,QAAQ,CAACgE,cAAc,CAAC,WAAW,CAAE,CAACE,KAAK,CAACC,IAAI,GAAG,GAAG;UACtDnE,QAAQ,CAACgE,cAAc,CAAC,WAAW,CAAE,CAACE,KAAK,CAACE,YAAY,GAAG,iBAAiB;UAC5EpE,QAAQ,CAACgE,cAAc,CAAC,YAAY,CAAE,CAACE,KAAK,CAACE,YAAY,GAAG,iBAAiB;UAC7EpE,QAAQ,CAACgE,cAAc,CAAC,YAAY,CAAE,CAACE,KAAK,CAACG,KAAK,GAAG,GAAG;QAC1D;QACA,MAAMtC,GAAG,GAAG,IAAI,CAACjC,MAAM,CAACwE,aAAa,CAAC,CAAC,aAAa,CAAC,EAAE;UAAEC,UAAU,EAAE,IAAI,CAACpE;QAAc,CAAE,CAAC,CAACqE,QAAQ,EAAE;QACtG,IAAI,CAACtE,QAAQ,CAACuE,EAAE,CAAC1C,GAAG,CAAC;MACvB;IACF;IACA,IAAI,CAAC7C,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IACpC,IAAI,CAACmB,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEAzF,QAAQA,CAAA;IACN,MAAMK,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC,IAAI,CAACA,YAAY,CAACiC,MAAM,GAAG,CAAC,CAAC;IAEpE,IAAI,CAACjC,YAAY,CAACiC,MAAM,GAAG,CAAC;IAC5B,IAAI,CAACjC,YAAY,CAACyJ,IAAI,CAACzJ,YAAY,CAAC;IACpC,IAAI,CAACA,YAAY,EAAE;MACjB,IAAI,CAACA,YAAY,CAACiC,MAAM,GAAG,CAAC;IAC9B;IACA,IAAI,CAACyH,UAAU,EAAE;IACjB,IAAI,CAAC1D,UAAU,GAAG,KAAK;EACzB;EAEA0D,UAAUA,CAAA;IACR,IAAI,IAAI,CAAC1J,YAAY,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;MACtC,IAAI,CAACb,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACJ,mBAAmB,GAAG,qBAAqB;IAClD,CAAC,MAAM;MACL,IAAI,CAACI,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAACJ,mBAAmB,GAAG,qBAAqB;IAClD;EACF;EAEA4K,YAAYA,CAACC,OAAwB;IACnC,IAAIA,OAAO,IAAIA,OAAO,CAACC,KAAK,IAAI,CAACD,OAAO,CAACC,KAAK,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC7H,MAAM,EAAE;MACxE2H,OAAO,CAACG,QAAQ,CAAC,EAAE,CAAC;IACtB;IACA,OAAO,IAAI;EACb;EAEAC,wBAAwBA,CAACC,IAAY,EAAEC,WAAmB;IACxD,MAAMN,OAAO,GAAGK,IAAI,CAACE,QAAQ,CAACD,WAAW,CAAC;IAC1CN,OAAO,CAACG,QAAQ,CAACH,OAAO,CAACC,KAAK,CAACO,IAAI,EAAE,CAAC;IACtCR,OAAO,CAACS,aAAa,CAAC,CAAChN,UAAU,CAACyI,QAAQ,EAAE,IAAI,CAAC6D,YAAY,CAAC,CAAC;IAC/DC,OAAO,CAACU,sBAAsB,EAAE;EAClC;EAEAzH,OAAOA,CAACoH,IAAY;IAClB,IAAI,CAACD,wBAAwB,CAACC,IAAI,EAAE,OAAO,CAAC;IAC5C,IAAI,CAACD,wBAAwB,CAACC,IAAI,EAAE,UAAU,CAAC;IAC/C,IAAI,CAACvI,UAAU,GAAG,IAAI,CAAC6I,UAAU,CAACN,IAAI,CAAC;IACvC,MAAMO,SAAS,GAAGP,IAAI,CAACJ,KAAK;IAC5BvD,OAAO,CAACC,GAAG,CAAC0D,IAAI,CAACJ,KAAK,CAAC;IACvB,IAAII,IAAI,CAACnI,OAAO,EAAE;MAChB,IAAI,CAACwC,YAAY,CAACmG,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;MACF;IACF;IAEA,IAAI,CAAC1G,SAAS,GAAG,IAAI;IACrB,IAAI2G,iBAAiB,GAAW,KAAK;IACrC,IAAIC,KAAK,GAAkB,IAAI;IAC/B,IAAIC,SAAS,GAAQ,IAAI;IACzB,MAAMC,QAAQ,GAAG,IAAI,CAAC7F,IAAI,CAAC6B,GAAG,CAAC,IAAI,CAACvC,WAAW,CAACwG,KAAK,CAAChB,IAAI,CAACJ,KAAK,CAACnE,KAAK,CAAC0E,IAAI,EAAE,EAAEH,IAAI,CAACJ,KAAK,CAACjJ,QAAQ,CAACwJ,IAAI,EAAE,CAAC,CACvG/C,IAAI,CACHzJ,SAAS,CAAEsN,QAAa,IAAI;MAC1BJ,KAAK,GAAGI,QAAQ,CAACJ,KAAK;MACtBC,SAAS,GAAG,IAAI,CAACtG,WAAW,CAAC0G,qBAAqB,CAACL,KAAM,CAAC,CAAC,CAAC;MAC5D,IAAI,CAACrG,WAAW,CAAC2G,QAAQ,CAACN,KAAM,CAAC;MACjC,IAAIA,KAAK,EAAE;QACTD,iBAAiB,GAAG,KAAK;QACzB;QACA,IAAI,CAACpG,WAAW,CAAC4G,YAAY,CAACR,iBAAiB,CAAC;QAChD,IAAI,CAACS,MAAM,GAAGJ,QAAQ,CAACK,EAAE;QACzB,IAAI,CAACC,IAAI,GAAGN,QAAQ;QACpB,IAAIH,SAAS,CAACU,IAAI,KAAK3N,QAAQ,CAAC4N,OAAO,EAAE;UAEvC,OAAO,IAAI,CAACC,8BAA8B,CAACT,QAAQ,CAAC;UACpD;QACF,CAAC,MAAM;UACL,IAAI,CAACzG,WAAW,CAACmH,kBAAkB,CAACC,IAAI,CAAC,IAAI,CAAC;UAC9C,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;UACtB,MAAMC,cAAc,GAAG,IAAID,IAAI,CAACD,GAAG,CAACG,OAAO,EAAE,GAAGpB,iBAAiB,GAAG,IAAI,CAAC;UACzE,IAAI,CAACpG,WAAW,CAACyH,YAAY,CAACpB,KAAM,EAAEkB,cAAc,EAAE,IAAI,CAACV,MAAO,EAAE,IAAI,CAACE,IAAK,EAAET,SAAS,CAACU,IAAI,CAAC;UAC/F;UACA,OAAQ,IAAI,CAACU,uBAAuB,EAAE;QACxC;MACF,CAAC,MAAM;QACL,OAAOxO,EAAE,CAAC,IAAI,CAAC;MACjB;IACF,CAAC,CAAC,CACH,CAACuJ,SAAS,CAAEI,GAAQ,IAAI;MACvBhB,OAAO,CAACC,GAAG,CAACiE,SAAS,CAAC;MACtB,IAAI4B,CAAC,GAAS,EAAU;MACxB;MACA;MACA,IAAIrB,SAAS,CAACU,IAAI,KAAK3N,QAAQ,CAAC4N,OAAO,EAAE;QACvC,IAAIU,CAAC,GAAS;UAAE,GAAG,IAAI,CAACZ,IAAI;UAAEa,YAAY,EAAE/E,GAAG,CAAC,CAAC,CAAC;UAAEgF,GAAG,EAAEhF,GAAG,CAAC,CAAC;QAAC,CAAU;MAC3E,CAAC,MAAM;QAELhB,OAAO,CAACC,GAAG,CAACe,GAAG,CAAC;QAChB;QACA,IAAI,CAAC1C,mBAAmB,CAAC2H,iBAAiB,CAACjF,GAAG,CAAC,CAAC,CAAC,CAAC;QAClD;MACF;MACA;MACA,IAAI,CAAC7C,WAAW,CAACmH,kBAAkB,CAACC,IAAI,CAAC,IAAI,CAAC;MAC9C,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;MACtB,MAAMC,cAAc,GAAG,IAAID,IAAI,CAACD,GAAG,CAACG,OAAO,EAAE,GAAGpB,iBAAiB,GAAG,IAAI,CAAC;MACzE,IAAI,CAACpG,WAAW,CAAC+H,kBAAkB,CAAC,IAAI,CAAC;MACzC,IAAI,CAAC/H,WAAW,CAACyH,YAAY,CAACpB,KAAM,EAAEkB,cAAc,EAAE,IAAI,CAACV,MAAO,EAAE,IAAI,CAACE,IAAK,EAAET,SAAS,CAACU,IAAI,CAAC;MAC/F,IAAI,CAACvH,SAAS,GAAG,KAAK;MACtB;MAEA,IAAI6G,SAAS,CAACU,IAAI,CAACgB,WAAW,EAAE,KAAK3O,QAAQ,CAAC4N,OAAO,CAACe,WAAW,EAAE,EAAE;QACnE;QACA,IAAI,IAAI,CAACjB,IAAI,EAAEkB,MAAM,CAACD,WAAW,EAAE,KAAK1O,UAAU,CAAC4O,MAAM,CAACF,WAAW,EAAE,EAAE;UACvE,IAAI,IAAI,CAAC/F,SAAS,EAAE;YAClB,IAAI,CAAC7B,MAAM,CAAC+H,QAAQ,CAAC,CAAC,IAAI,CAAClG,SAAS,CAAC,CAAC;UACxC,CAAC,MAAM;YACL,IAAI,CAAC7B,MAAM,CAACgI,aAAa,CAAC,YAAY,CAAC;UACzC;UACA,OAAO,KAAK;QACd;QACA,MAAMC,WAAW,GAAGC,MAAM,CAACC,MAAM,CAAC1F,GAAG,CAAC,CAAC,CAAC,CAAC;QACzC,MAAM2F,QAAQ,GAAGH,WAAW,CAACI,KAAK,CAACrD,KAAK,IAAIA,KAAK,KAAK,KAAK,CAAC;QAC5DvD,OAAO,CAACC,GAAG,CAAC0G,QAAQ,CAAC;QACrB,IAAIA,QAAQ,EAAE;UACZ,IAAI,CAACpI,MAAM,CAACgI,aAAa,CAAC,eAAe,CAAC;UAC1C,OAAO,KAAK;QACd,CAAC,MAAM;UACL,IAAI,CAAChI,MAAM,CAACgI,aAAa,CAAC,IAAI,CAAClI,yBAAyB,CAACwI,6BAA6B,CAAC7F,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/F,OAAO,KAAK;QACd;MACF;MAEA;MACA,IAAI,IAAI,CAACZ,SAAS,EAAE;QAClB,IAAI,CAAC7B,MAAM,CAAC+H,QAAQ,CAAC,CAAC,IAAI,CAAClG,SAAS,CAAC,CAAC;MACxC,CAAC,MAAM;QACL,IAAI,CAAC7B,MAAM,CAACgI,aAAa,CAAC,YAAY,CAAC;MACzC;MACA,OAAO,KAAK;IACd,CAAC,EAAEO,KAAK,IAAG;MACT,IAAI,CAAClJ,SAAS,GAAG,KAAK;IACxB,CAAC,CAAC,CAAC;EAGP;EAEA1B,QAAQA,CAACyH,IAAY;IACnB,IAAI,CAACvI,UAAU,GAAG,IAAI,CAAC6I,UAAU,CAACN,IAAI,CAAC;IACvC,IAAIA,IAAI,CAACnI,OAAO,EAAE;MAChB,IAAI,CAACwC,YAAY,CAACmG,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;MACF;IACF;IAEA,IAAI,CAACtG,YAAY,CAACmG,mBAAmB,CAAC;MACpCC,QAAQ,EAAE,SAAS;MACnBC,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE;KACT,CAAC;IACF,IAAI,CAACnG,WAAW,CAAC4I,MAAM,CAACpD,IAAI,CAACJ,KAAK,CAACnE,KAAK,CAAC;IACzCuE,IAAI,CAACqD,KAAK,EAAE;EACd;EAEAhN,QAAQA,CAAC2J,IAAY;IACnB,IAAIsD,YAAY,GAAG,IAAI,CAACpO,iBAAiB,GAAG,iBAAiB,GAAG,iBAAiB;IACjF,IAAI,CAACuC,UAAU,GAAG,IAAI,CAAC6I,UAAU,CAACN,IAAI,CAAC;IACvC,IAAI,CAACjI,YAAY,GAAG,IAAI,CAACwL,eAAe,CAACvD,IAAI,CAACJ,KAAK,CAACjJ,QAAQ,EAAEqJ,IAAI,CAACJ,KAAK,CAAClE,OAAO,CAAC;IAEjF,IAAI8H,aAAa,GAAG,EAAE;IACtB,KAAK,MAAMvD,WAAW,IAAID,IAAI,CAACE,QAAQ,EAAE;MACvC,IAAIF,IAAI,CAACE,QAAQ,CAACuD,cAAc,CAACxD,WAAW,CAAC,EAAE;QAC7C,MAAMN,OAAO,GAAGK,IAAI,CAACE,QAAQ,CAACD,WAAW,CAAC;QAC1C,IAAIN,OAAO,CAAC9H,OAAO,EAAE;UACnB2L,aAAa,IAAI,GAAGvD,WAAW,IAAI;QACrC;MACF;IACF;IAEA,IAAIyD,aAAa,GAAG,oDAAoD;IACxE,IAAIF,aAAa,KAAK,EAAE,EAAE;MACxBA,aAAa,GAAGA,aAAa,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAE5C,IAAIH,aAAa,KAAK,EAAE,EAAE;QACxBE,aAAa,IAAI,4CAA4CF,aAAa,EAAE;MAC9E;IACF;IACA,IAAI,CAAC,IAAI,CAACrM,WAAW,EAAE;MACrBuM,aAAa,IAAI,0CAA0C;IAC7D;IACA,IAAI,CAAC,IAAI,CAACzH,eAAe,EAAE,EAAE;MAC3ByH,aAAa,IAAI,0CAA0C;IAC7D;IAEA,IAAI1D,IAAI,CAACnI,OAAO,IAAI,CAAC,IAAI,CAACV,WAAW,IAAI,IAAI,CAACY,YAAY,IAAI,CAAC,IAAI,CAACkE,eAAe,EAAE,EAAE;MACrF,IAAI,CAAC5B,YAAY,CAACmG,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE+C;OACT,CAAC;MACF;IACF;IACA,IAAI,CAAC3O,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACyF,WAAW,CAACoJ,UAAU,CAAC5D,IAAI,CAACJ,KAAK,CAACnE,KAAK,EAAEuE,IAAI,CAACJ,KAAK,CAACjJ,QAAQ,EAAE2M,YAAY,CAAC,CAC7ErG,SAAS,CAAEI,GAAG,IAAI;MACjB,IAAI,CAAC7C,WAAW,CAACqJ,qCAAqC,CAAC,IAAI,CAAC;MAC5D,IAAI,CAAC9O,iBAAiB,GAAG,KAAK;IAChC,CAAC,EAAEoO,KAAK,IAAG;MACT,IAAI,CAACpO,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAACyF,WAAW,CAACmH,kBAAkB,CAACC,IAAI,CAAC,KAAK,CAAC;MAC/C5B,IAAI,CAACqD,KAAK,EAAE;IACd,CAAC,CAAC;EACN;EAEOS,sBAAsBA,CAAA;IAC3B,IAAI,CAAC5O,iBAAiB,GAAG,IAAI;EAC/B;EAEO6O,sBAAsBA,CAAA;IAC3B,IAAI,CAAC7O,iBAAiB,GAAG,KAAK;EAChC;EAEAoL,UAAUA,CAACN,IAAY;IACrB,IAAIA,IAAI,CAACJ,KAAK,CAACnE,KAAK,EAAE;MACpB,IAAI6E,UAAU,GAAGN,IAAI,CAACJ,KAAK,CAACnE,KAAK,CAAC0E,IAAI,EAAE,CACrCqC,WAAW,EAAE,CACbwB,KAAK,CACJ,gBAAgB,CACjB;MACH,IAAI1D,UAAU,KAAK,IAAI,IAAIN,IAAI,CAACiE,OAAO,EAAE;QACvC,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd;EAEAV,eAAeA,CAAC5M,QAAgB,EAAE+E,OAAe;IAC/C,OAAO/E,QAAQ,KAAK+E,OAAO;EAC7B;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEAjF,yBAAyBA,CAACyN,GAAkB;IAC1C,IAAI,CAAC1I,iBAAiB,CAAC0I,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC1I,iBAAiB,CAAC0I,GAAG,CAAC;EAC5D;EAEAxM,oBAAoBA,CAACwM,GAAkB;IACrC,OAAO,IAAI,CAAC1I,iBAAiB,CAAC0I,GAAG,CAAC;EACpC;EAEQxC,8BAA8BA,CAACT,QAAa;IAClD,IAAIkD,SAAS,GAAG,IAAI,CAAC1J,WAAW,CAAC2J,mBAAmB,CAACnD,QAAQ,CAACK,EAAE,CAAC;IACjE,IAAI+C,SAAS,GAAG,IAAI,CAAC7J,WAAW,CAAC8J,gBAAgB,EAAE;IACnD,IAAIC,SAAS,GAAG,IAAI,CAAC7J,yBAAyB,CAAC8J,gCAAgC,EAAE;IACjF;IACA,OAAO/Q,QAAQ,CAAC,CAAC0Q,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC,CAAC;EACpD;EAGQrC,uBAAuBA,CAAA;IAC7B,IAAImC,SAAS,GAAG,IAAI,CAAC1J,mBAAmB,CAAC8J,yBAAyB,EAAE;IACpE;IACA,OAAOhR,QAAQ,CAAC,CAAC4Q,SAAS,CAAC,CAAC;EAC9B;EAEA;;;EAGAK,gBAAgBA,CAAA;IACd,MAAMC,KAAK,GAAG,IAAI;IAClB,MAAMC,mBAAmB,GAAG,EAAE,CAAC,CAAC;IAChC,MAAMC,eAAe,GAAG,6BAA6B;IAErDC,UAAU,CAAC,MAAK;MACd,IAAIxD,EAAE,GAAGxG,QAAQ,CAACgE,cAAc,CAAC,eAAe,CAAC;MACjD,IAAI,CAACwC,EAAE,EAAE;QACPqD,KAAK,CAAClH,UAAU,CAACoH,eAAe,CAAC;MACnC;MACAF,KAAK,CAACzK,0BAA0B,GAAG,IAAI;IACzC,CAAC,EAAE0K,mBAAmB,CAAC;EACzB;EAEAlL,qBAAqBA,CAAA;IACnB,MAAMqL,SAAS,GAAGjK,QAAQ,CAACgE,cAAc,CAAC,WAAW,CAAC;IACtDiG,SAAU,CAACC,SAAS,CAACC,MAAM,CAAC,oBAAoB,CAAC;IAEjD,IAAI,CAACF,SAAU,CAACC,SAAS,CAACE,QAAQ,CAAC,oBAAoB,CAAC,EAAE;MAExD,MAAMrI,GAAG,GAAG,IAAI,CAACjC,MAAM,CAACwE,aAAa,CAAC,CAAC,gBAAgB,CAAC,EAAE;QAAEC,UAAU,EAAE,IAAI,CAACpE;MAAc,CAAE,CAAC,CAACqE,QAAQ,EAAE;MACzG,IAAI,CAACtE,QAAQ,CAACuE,EAAE,CAAC1C,GAAG,CAAC;IACvB,CAAC,MAAM;MAEL,MAAMA,GAAG,GAAG,IAAI,CAACjC,MAAM,CAACwE,aAAa,CAAC,CAAC,aAAa,CAAC,EAAE;QAAEC,UAAU,EAAE,IAAI,CAACpE;MAAc,CAAE,CAAC,CAACqE,QAAQ,EAAE;MACtG,IAAI,CAACtE,QAAQ,CAACuE,EAAE,CAAC1C,GAAG,CAAC;IACvB;EACF;EAEAxF,oBAAoBA,CAAC8N,eAAuB;IAC1C9I,OAAO,CAACC,GAAG,CAAC,mCAAmC6I,eAAe,EAAE,CAAC;IACjE,IAAI,CAAClJ,eAAe,CAACmJ,GAAG,CAAC,IAAI,CAAC;EAChC;EAEQ7G,iCAAiCA,CAAC8G,EAAc;IACtD,MAAMC,YAAY,GAAG;wHAC+F;IACpHD,EAAE,CAACE,aAAa,CAACC,SAAS,GAAGF,YAAY;EAC3C;EAAC,QAAAG,CAAA,G;qBAveUtL,cAAc,EAAA9F,EAAA,CAAAqR,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAvR,EAAA,CAAAqR,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAzR,EAAA,CAAAqR,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA3R,EAAA,CAAAqR,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA7R,EAAA,CAAAqR,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAA/R,EAAA,CAAAqR,iBAAA,CAAAW,EAAA,CAAAC,yBAAA,GAAAjS,EAAA,CAAAqR,iBAAA,CAAAa,EAAA,CAAAC,mBAAA,GAAAnS,EAAA,CAAAqR,iBAAA,CAAAe,EAAA,CAAAC,MAAA,GAAArS,EAAA,CAAAqR,iBAAA,CAAArR,EAAA,CAAAsS,MAAA,GAAAtS,EAAA,CAAAqR,iBAAA,CAgDf3S,QAAQ,GAAAsB,EAAA,CAAAqR,iBAAA,CAAArR,EAAA,CAAAuS,SAAA,GAAAvS,EAAA,CAAAqR,iBAAA,CAAAmB,EAAA,CAAAC,QAAA,GAAAzS,EAAA,CAAAqR,iBAAA,CAAAe,EAAA,CAAAM,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAhDP7M,cAAc;IAAA8M,SAAA;IAAAC,SAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;QCsQ1B/S,EA/RD,CAAAiC,UAAA,IAAAgR,+BAAA,SAqSC,IAAAC,0CAAA;QArSDlT,EAAA,CAAAmT,OAAA,OAAAC,6BAAA,UAqSC;QArSMpT,EAAA,CAAAqT,iBAAA,OAAW;;;mBDUd5U,YAAY,EACZO,wBAAwB,EACxBE,YAAY,EACZC,cAAc,EACdP,WAAW,EACXC,mBAAmB,EACnBe,eAAe,EACfX,eAAe;IAAAqU,MAAA;IAAAC,IAAA;MAAAC,SAAA,EAKL,CAAC7T,oBAAoB,EAAED,UAAU;IAAC;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}