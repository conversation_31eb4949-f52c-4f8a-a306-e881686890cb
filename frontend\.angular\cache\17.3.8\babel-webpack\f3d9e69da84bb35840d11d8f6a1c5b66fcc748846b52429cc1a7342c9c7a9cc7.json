{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { LessonStatus } from 'src/app/core/models/lesson.model';\nimport { dummylessonFullRatings } from 'src/app/core/models/rating.model';\nimport { of } from 'rxjs';\nimport { switchMap } from 'rxjs/operators';\nimport jsPDF from 'jspdf';\nimport html2canvas from 'html2canvas';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/toast.service\";\nimport * as i2 from \"src/app/core/services/auth.service\";\nimport * as i3 from \"src/app/core/services/rating-and-report.service\";\nimport * as i4 from \"src/app/core/services/lesson.service\";\nimport * as i5 from \"src/app/core/services/calendar.service\";\nimport * as i6 from \"src/app/core/services/classroom.service\";\nimport * as i7 from \"src/app/core/services/general.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"primeng/button\";\nimport * as i11 from \"primeng/ripple\";\nimport * as i12 from \"primeng/checkbox\";\nimport * as i13 from \"primeng/radiobutton\";\nimport * as i14 from \"./lesson-rating-range-slider/lesson-rating-range-slider.component\";\nimport * as i15 from \"../../multi-handle-slider/multi-handle-slider.component\";\nconst _c0 = [\"chartLessonBreakdown\"];\nconst _c1 = [\"slider\"];\nconst _c2 = a0 => ({\n  \"pointer-events-none opacity-70\": a0\n});\nconst _c3 = a0 => ({\n  \"pointer-events-none\": a0\n});\nconst _c4 = a0 => ({\n  \"hidden\": a0\n});\nconst _c5 = a0 => ({\n  \"pointer-events-none  opacity-70\": a0\n});\nfunction LessonRatingComponent_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"p-checkbox\", 19);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LessonRatingComponent_div_22_Template_p_checkbox_ngModelChange_1_listener($event) {\n      const i_r2 = i0.ɵɵrestoreView(_r1).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.ratings[i_r2].checked, $event) || (ctx_r2.ratings[i_r2].checked = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function LessonRatingComponent_div_22_Template_p_checkbox_onChange_1_listener($event) {\n      const rating_r4 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCheckboxChange($event, rating_r4));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 20);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const rating_r4 = ctx.$implicit;\n    const i_r2 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"binary\", true)(\"value\", ctx_r2.ratings[i_r2].checked);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.ratings[i_r2].checked);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.mode === \"view\" ? true : false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(rating_r4.category);\n  }\n}\nfunction LessonRatingComponent_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 21);\n  }\n}\nfunction LessonRatingComponent_div_27_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 35);\n  }\n}\nfunction LessonRatingComponent_div_27_div_22_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵelementContainerStart(1);\n    i0.ɵɵelementStart(2, \"div\", 40);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 41)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"app-lesson-rating-range-slider\", 42);\n    i0.ɵɵlistener(\"categoryRatingChanged\", function LessonRatingComponent_div_27_div_22_div_1_Template_app_lesson_rating_range_slider_categoryRatingChanged_7_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const i_r8 = i0.ɵɵnextContext(2).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCategoryRatingChanged($event, ctx_r2.arrayValue[i_r8], i_r8));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 43)(9, \"div\", 44);\n    i0.ɵɵtext(10, \"1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 45);\n    i0.ɵɵtext(12, \"5\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 46);\n    i0.ɵɵtext(14, \"10\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const variant_r9 = ctx.$implicit;\n    const value_r6 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", variant_r9.rating.category, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c5, ctx_r2.mode === \"view\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(variant_r9.category);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"rating\", value_r6.lessonUserRating[variant_r9.rating.category.toLowerCase()])(\"category\", variant_r9.rating.category.toLowerCase());\n  }\n}\nfunction LessonRatingComponent_div_27_div_22_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtext(1, \" Please check the checkboxes in the CLASSROOM rating section so you can rate your student. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LessonRatingComponent_div_27_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵtemplate(1, LessonRatingComponent_div_27_div_22_div_1_Template, 15, 7, \"div\", 37)(2, LessonRatingComponent_div_27_div_22_div_2_Template, 2, 0, \"div\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.variants);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.variants.length === 0 && ctx_r2.mode !== \"view\");\n  }\n}\nfunction LessonRatingComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23)(2, \"div\", 24);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"slice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 8)(6, \"div\", 25)(7, \"div\", 26);\n    i0.ɵɵtext(8, \" Absent \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 27)(10, \"div\", 28)(11, \"div\", 29)(12, \"p-radioButton\", 30);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LessonRatingComponent_div_27_Template_p_radioButton_ngModelChange_12_listener($event) {\n      const value_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      i0.ɵɵtwoWayBindingSet(value_r6.lessonUserRating.absent, $event) || (value_r6.lessonUserRating.absent = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"label\", 31);\n    i0.ɵɵtext(14, \"Yes\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 29)(16, \"p-radioButton\", 30);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LessonRatingComponent_div_27_Template_p_radioButton_ngModelChange_16_listener($event) {\n      const value_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      i0.ɵɵtwoWayBindingSet(value_r6.lessonUserRating.absent, $event) || (value_r6.lessonUserRating.absent = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"label\", 31);\n    i0.ɵɵtext(18, \"No\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(19, \"div\", 32);\n    i0.ɵɵtext(20, \" Lesson Rating \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, LessonRatingComponent_div_27_div_21_Template, 1, 0, \"div\", 33)(22, LessonRatingComponent_div_27_div_22_Template, 3, 2, \"div\", 34);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const value_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", value_r6.user.firstName, \" \", i0.ɵɵpipeBind3(4, 12, value_r6.user.lastName, 0, 1), \". \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"hidden\", ctx_r2.arrayValue.length === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(16, _c3, ctx_r2.mode === \"view\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(18, _c2, ctx_r2.mode === \"view\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", true);\n    i0.ɵɵtwoWayProperty(\"ngModel\", value_r6.lessonUserRating.absent);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"value\", false);\n    i0.ɵɵtwoWayProperty(\"ngModel\", value_r6.lessonUserRating.absent);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(20, _c4, ctx_r2.mode === \"view\" && value_r6.lessonUserRating.absent));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.mode == \"view\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !value_r6.lessonUserRating.absent);\n  }\n}\nfunction LessonRatingComponent_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵlistener(\"click\", function LessonRatingComponent_div_28_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.saveRating());\n    });\n    i0.ɵɵelement(1, \"button\", 49);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LessonRatingComponent_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵlistener(\"click\", function LessonRatingComponent_div_29_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const allRating_r12 = i0.ɵɵreference(2);\n      return i0.ɵɵresetView(ctx_r2.download(allRating_r12));\n    });\n    i0.ɵɵelementStart(1, \"div\", 50);\n    i0.ɵɵtext(2, \"Download\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let LessonRatingComponent = /*#__PURE__*/(() => {\n  class LessonRatingComponent {\n    tooltipChangeHandler(args) {\n      // Weekdays Array\n      let daysArr = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10'];\n      // Customizing each ticks text into weeksdays\n      args.text = daysArr[parseFloat(args.value)];\n    }\n    renderingTicksHandler(args) {\n      // Customizing tooltip to display the Day (in numeric) of the week\n      args.text = 'Day ' + (Number(args.value) + 1).toString();\n    }\n    constructor(toast, authService, ratingAndReportService, lessonService, calendarService, classroomService, generalService, elementRef, renderer) {\n      this.toast = toast;\n      this.authService = authService;\n      this.ratingAndReportService = ratingAndReportService;\n      this.lessonService = lessonService;\n      this.calendarService = calendarService;\n      this.classroomService = classroomService;\n      this.generalService = generalService;\n      this.elementRef = elementRef;\n      this.renderer = renderer;\n      this.subs = new SubSink();\n      this.lesson = {};\n      this.classroom = {};\n      this.mode = 'create';\n      this.lessonRatings = {};\n      this.lessonUserRatings = [];\n      this.users = [];\n      this.ratingSubmitted = new EventEmitter();\n      this.ratings = [{\n        id: 'r1',\n        color: '#362FBB',\n        category: 'Vocabulary',\n        checked: false\n      }, {\n        id: 'r2',\n        color: '#758BFA',\n        category: 'Grammar',\n        checked: false\n      }, {\n        id: 'r3',\n        color: '#00B2AE',\n        category: 'Writing',\n        checked: false\n      }, {\n        id: 'r4',\n        color: '#9DC6B6',\n        category: 'Reading',\n        checked: false\n      }, {\n        id: 'r5',\n        color: '#0076CD',\n        category: 'Listening',\n        checked: false\n      }, {\n        id: 'r6',\n        color: '#00639A',\n        category: 'Speaking',\n        checked: false\n      }, {\n        id: 'r7',\n        color: '#D094EA',\n        category: 'Revision',\n        checked: false\n      }, {\n        id: 'r8',\n        color: '#A44FD0',\n        category: 'Test',\n        checked: false\n      }];\n      this.loggedInUser = {};\n      this.classRatings = dummylessonFullRatings;\n      this.handles = [25, 50, 75]; // initial handle positions\n      this.min = 0;\n      this.max = 100;\n      this.step = 1;\n      this.count = 0;\n      this.variants = [];\n      this.colors = ['transparent', 'transparent', 'transparent', 'transparent', 'transparent', 'transparent', 'transparent', 'transparent'];\n      this.variantsHTML = [];\n      this.chooseAbsentValue = \"no\";\n      this.tooltipData = {\n        placement: 'Before',\n        isVisible: true\n      };\n      this.ticksData = {\n        placement: 'After',\n        largeStep: 1\n      };\n      this.absentValue = [];\n      this.arrayValue = [];\n      this.sliderRatings = [];\n      this.user = {};\n      this.labels = [];\n      this.data = [];\n    }\n    ngOnInit() {\n      if (this.authService.isStudent) {\n        this.mode = 'view';\n      }\n      this.loggedInUser = this.authService.getLoggedInUser();\n      // this.lessonUserRatings = dummylessonFullRatings[0].userRatings;\n      console.log(this.lesson);\n      console.log(this.lessonUserRatings);\n    }\n    ngAfterContentInit() {\n      console.log(this.users);\n      if (this.mode != 'view' && this.mode != 'edit') {\n        for (let user of this.users) {\n          this.arrayValue.push({\n            user: user,\n            userId: user.id,\n            options: [\"yes\", \"no\"],\n            absent: false,\n            lessonUserRating: {\n              absent: false,\n              vocabulary: 0,\n              listening: 0,\n              grammar: 0,\n              speaking: 0,\n              writing: 0,\n              revision: 0,\n              reading: 0,\n              test: 0,\n              studentId: user.aspUserId,\n              teacherId: this.loggedInUser.aspUserId,\n              lessonId: this.lesson.id\n            }\n          });\n          if (this.lessonUserRatings.length === 0) {\n            const defaultLessonUserRating = {\n              id: user.id,\n              absent: false,\n              vocabulary: 0,\n              listening: 0,\n              grammar: 0,\n              speaking: 0,\n              writing: 0,\n              revision: 0,\n              reading: 0,\n              test: 0\n            };\n            this.lessonUserRatings.push(defaultLessonUserRating);\n          }\n        }\n      }\n      console.log(this.lessonUserRatings);\n      if (this.mode != 'view') {\n        console.log(this.lesson);\n        for (let user of this.users) {}\n      } else {\n        console.log(this.lessonUserRatings);\n        // if (this.lessonUserRatings) {\n        //   for (let lessonUserRating of this.lessonUserRatings) {\n        //     this.arrayValue.push({ user: this.users[0], userId: 'test', \n        //     options: [\"yes\", \"no\"], absent: lessonUserRating.absent })\n        //   }\n        // }\n      }\n      if (this.mode === 'view' || this.mode === 'edit') {\n        this.subs.sink = this.ratingAndReportService.getLessonBreakDown(this.lesson.id).subscribe(res => {\n          console.log(res[0]);\n          // this.lessonRatings.id = res[0].id;\n          this.lessonRatings = res[0];\n          this.prepareRatings();\n          this.updateVariants();\n        });\n        for (let user of this.users) {\n          this.subs.sink = this.ratingAndReportService.getUserLessonRatings(user.aspUserId, this.lesson.id).subscribe(res => {\n            console.log(res);\n            // if (res.length === 0) {\n            //   return;\n            // }\n            if (res.length === 0) {\n              return;\n            }\n            res.forEach((key, index) => {\n              this.arrayValue.push({\n                user: user,\n                userId: user.id,\n                options: [\"yes\", \"no\"],\n                absent: false,\n                lessonUserRating: {\n                  absent: res[index].absent,\n                  id: res[index].id,\n                  vocabulary: res[index].vocabulary,\n                  listening: res[index].listening,\n                  grammar: res[index].grammar,\n                  speaking: res[index].speaking,\n                  writing: res[index].writing,\n                  revision: res[index].revision,\n                  reading: res[index].reading,\n                  test: res[index].test,\n                  studentId: user.aspUserId,\n                  teacherId: this.classroom.teacher?.aspUserId,\n                  lessonId: this.lesson.id\n                }\n              });\n              // this.variants.push({ rating: this.ratings[index], value: parseFloat((100 / 5).toFixed(1)) });\n              // this.showSliderValue(this.slider.nativeElement.parentElement.querySelector('.rs-bullet'), this.slider.nativeElement);\n            });\n            // this.variants.push({ rating: this.ratings[index], value: parseFloat((100 / 5).toFixed(1)) });\n            // this.updateVariants();\n            // const defaultLessonUserRating: LessonUserRating = {\n            //   id: this.lesson.id.toString(),\n            //   absent: false,\n            //   vocabulary: res[0].vocabulary,\n            //   listening: res[0].listening,\n            //   grammar: res[0].grammar,\n            //   speaking: res[0].speaking,\n            //   writing: res[0].speaking,\n            //   revision: res[0].revision,\n            //   reading: res[0].reading,\n            //   test: res[0].test,\n            //   lessonId: this.lesson.id\n            // };\n            // this.lessonUserRatings.push(defaultLessonUserRating);\n          }, error => {\n            console.error(error);\n          });\n        }\n      }\n    }\n    ngOnChanges() {\n      // const sliderHandles = this.elementRef.nativeElement.querySelectorAll('.rate-slider .p-slider-handle');\n      // sliderHandles.forEach((sliderHandle: HTMLElement) => {\n      //   console.log(sliderHandle);\n      //   const valueNow = sliderHandle.getAttribute('aria-valuenow');\n      //   const valueSpan = this.renderer.createElement('span');\n      //   const text = this.renderer.createText(valueNow!);\n      //   this.renderer.appendChild(valueSpan, text);\n      //   this.renderer.insertBefore(sliderHandle.parentNode, valueSpan, sliderHandle);\n      // });\n    }\n    prepareRatings() {\n      Object.keys(this.lessonRatings).forEach(key => {\n        const ratingFound = this.ratings.find(rating => rating.category.toLowerCase() === key);\n        if (ratingFound) {\n          const ratingVal = this.lessonRatings[key];\n          if (ratingVal > -1) {\n            this.onCheckboxChange({\n              checked: true\n            }, ratingFound);\n          }\n        }\n      });\n    }\n    showSliderValue(bullet, slider) {\n      bullet.innerHTML = slider.value;\n      console.log(slider.value);\n      var sliderWidth = slider.offsetWidth;\n      var sliderMax = slider.max;\n      var sliderValue = parseInt(slider.value);\n      var bulletPosition = sliderValue / sliderMax * sliderWidth;\n      const badOffset = sliderValue === 0 ? 2 : sliderValue;\n      bullet.style.left = sliderValue === 0 ? 2 + \"px\" : bulletPosition - badOffset * 1.3 + \"px\";\n      // bullet.style.top = \"10px\";\n      console.log(bullet.style);\n    }\n    onCategoryRatingChanged($event, arraval, index) {\n      console.log($event, index);\n      this.arrayValue[index].lessonUserRating[$event.category] = $event.value;\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n    }\n    onCheckboxChange(event, rating) {\n      console.log(this.lessonRatings);\n      const index = this.ratings.findIndex(r => r.category === rating.category);\n      this.ratings[index].checked = true;\n      if (event.checked) {\n        for (let lessonUserRating of this.lessonUserRatings) {\n          Object.keys(lessonUserRating).forEach(key => {\n            if (rating.category.toLowerCase() == key) {\n              lessonUserRating[key] = 5;\n            }\n          });\n        }\n        let cheat = 0.2; //sometimes sums to 100.2% and breaks\n        for (let i = 0; i < this.count; i++) {\n          if (i !== 4 && i !== 5) {\n            cheat = 0;\n          } else {\n            cheat = 0.2;\n          }\n          this.variants[i].value = parseFloat(((this.variants[i].value * this.count - cheat) / (this.count + 1)).toFixed(1));\n        }\n        this.count++;\n        this.variants.push({\n          rating: rating,\n          value: parseFloat((100 / this.count).toFixed(1))\n        });\n      } else {\n        for (let lessonUserRating of this.lessonUserRatings) {\n          Object.keys(lessonUserRating).forEach(key => {\n            if (rating.category == key) {\n              lessonUserRating[key] = 0;\n            }\n          });\n        }\n        this.count--;\n        let valueToAdd = this.variants.find(el => {\n          return el.rating.id === rating.id;\n        })?.value * (1 / this.count);\n        this.variants = this.variants.filter(el => {\n          return el.rating.id !== rating.id;\n        });\n        for (let i = 0; i < this.count; i++) {\n          this.variants[i].value = parseFloat((this.variants[i].value + valueToAdd - 0.2).toFixed(1));\n        }\n      }\n      setTimeout(() => {\n        this.styleVariants();\n      }, 10);\n      // Check if the arrayValue array has an object with a lessonUserRating property that matches the rating object\n      const arrayValueIndex = this.arrayValue.findIndex(obj => obj.lessonUserRating && obj.lessonUserRating.id === rating.id);\n      if (arrayValueIndex !== -1) {\n        this.arrayValue[arrayValueIndex].lessonUserRating.checked = event.checked;\n      }\n      // Hide user's variant ratings when \"absent\" checkbox is checked\n      // if (event.checked) {\n      //   this.variants.forEach((variant: any) => {\n      //     const userVariantRatingIndex = this.lessonUserRatings.findIndex((userRating: any) => userRating.user?.id === this.loggedInUser.id && userRating.lessonUserRating.id === variant.rating.id);\n      //     if (userVariantRatingIndex!== -1) {\n      //       this.lessonUserRatings.splice(userVariantRatingIndex, 1);\n      //     }\n      //   });\n      // }\n    }\n    resetVariants() {\n      this.count = 0;\n      this.variants = [];\n      this.ratings = this.ratings.map(item => ({\n        ...item,\n        checked: false\n      }));\n      this.arrayValue.forEach((key, index) => {\n        // this.variants.push({ rating: this.ratings[index], value: parseFloat((100 / 5).toFixed(1)) });\n        Object.keys(this.arrayValue[index].lessonUserRating).forEach(key => {\n          console.log(this.arrayValue[index].lessonUserRating[key]);\n          if (this.arrayValue[index].lessonUserRating[key] !== 0) {\n            const ratingFound = this.ratings.find(rating => rating.category.toLowerCase() === key);\n            // this.arrayValue[index].lessonUserRating[key] = 0;\n            if (ratingFound) {\n              console.log(ratingFound);\n              // this.variants.push({ rating: ratingFound, value: parseFloat((100 / 3).toFixed(1)) });\n              // ratingFound.checked = true;\n              // this.onCheckboxChange({ checked: false }, ratingFound);\n            }\n          }\n        });\n      });\n    }\n    updateVariants() {\n      if (this.mode === 'edit' || this.mode === 'view') {\n        const updatedVariants = this.variants.map(variant => {\n          const {\n            rating\n          } = variant;\n          const {\n            category\n          } = rating;\n          let value = this.lessonRatings[category];\n          let ratingBreakdown = value;\n          for (const [key, value] of Object.entries(this.lessonRatings)) {\n            if (category.toLowerCase() == key) {\n              ratingBreakdown = this.lessonRatings[key];\n            }\n          }\n          value = ratingBreakdown !== -1 ? ratingBreakdown : 0;\n          return {\n            ...variant,\n            value\n          };\n        });\n        this.variants = updatedVariants;\n        setTimeout(() => {\n          this.styleVariants();\n        }, 50);\n      }\n    }\n    getRadioChecked(value, key) {\n      if (value && key == 'yes') {\n        return true;\n      }\n      if (!value && key == 'no') {\n        return true;\n      }\n      return null;\n    }\n    getRatingValue(key, user) {\n      let val = 5;\n      if (this.lessonUserRatings.length > 0) {\n        let userRating = dummylessonFullRatings[0].userRatings[0];\n        console.log(userRating);\n        if (key == \"listening\") {\n          val = userRating.listening;\n        }\n        if (key == \"grammar\") {\n          val = userRating.grammar;\n        }\n        if (key == \"speaking\") {\n          val = userRating.speaking;\n        }\n        if (key == \"writing\") {\n          val = userRating.writing;\n        }\n        if (key == \"revision\") {\n          val = userRating.revision;\n        }\n        if (key == \"reading\") {\n          val = userRating.reading;\n        }\n        if (key == \"test\") {\n          val = userRating.test;\n        }\n      }\n      return val;\n    }\n    styleVariants() {\n      let i = 0;\n      let width = \"\";\n      let left = \"\";\n      this.variantsHTML = [];\n      [].forEach.call(document.getElementsByClassName('slider-handle'), el => {\n        el.style.setProperty(\"background\", \"white\", \"important\");\n      });\n      [].forEach.call(document.getElementsByClassName('slider'), el => {\n        el.style.setProperty(\"color\", \"white\", \"important\");\n        // el.style.setProperty(\"text-align\", \"center\", \"important\");\n        el.style.setProperty(\"top\", \"-5px\", \"important\");\n        this.variantsHTML.push(el);\n      });\n      console.log(this.variantsHTML.length);\n      this.textInterval = setInterval(() => {\n        for (let i = 0; i < this.variantsHTML.length; i++) {\n          if (!this.variants[i]) {\n            continue;\n          }\n          if (i > 0) {\n            width = \"calc(100% - 40px)\";\n            left = \"50px\";\n          } else {\n            width = \"100%\";\n            left = \"0px\";\n          }\n          this.variantsHTML[i].innerHTML = '<div style=\"margin-top:-30px; text-align:center; width:' + width + '; margin-left:' + left + '\"><div class=\"font-xs\" style=\"color: #2d2a4b; font-weight:bold;\">' + this.variants[i].rating.category + '<br></div><div style=\"width:100%;\"><div style=\"margin-top:9px;\">' + this.variants[i].value.toFixed(0) + '%</div></div></div>';\n          // this.variantsHTML[i].innerHTML = '<div style=margin-top:-30px><div style=\"color: #2d2a4b; font-weight:bold; margin-left:' + space + '\">' + this.variants[i].rating.rating + '<br></div><div style=\"width:100%;\"><div style=\"margin-top:9px; margin-left:' + space + '\">' + this.variants[i].value + '%</div></div></div>';\n        }\n      }, 100);\n    }\n    chooseAbsent(event, index) {\n      this.lessonUserRatings.filter(el => {\n        if (el.user?.id === event.value[1].id) {\n          el.absent = event.value[0] === 'yes' ? true : false;\n        }\n      });\n    }\n    changeSlider(event, rating, user, variant) {\n      let category = variant.rating.category.toLowerCase();\n      let lessonUserRating = this.lessonUserRatings.filter(el => el.user?.id === user.id)[0];\n      // this.changeRating(category, lessonUserRating, event.value)\n    }\n    changeRating(category, lessonUserRating, value) {\n      if (category == \"vocabulary\") {\n        lessonUserRating.vocabulary = value;\n      }\n      if (category == \"listening\") {\n        lessonUserRating.listening = value;\n      }\n      if (category == \"grammar\") {\n        lessonUserRating.grammar = value;\n      }\n      if (category == \"speaking\") {\n        lessonUserRating.speaking = value;\n      }\n      if (category == \"writing\") {\n        lessonUserRating.writing = value;\n      }\n      if (category == \"revision\") {\n        lessonUserRating.revision = value;\n      }\n      if (category == \"reading\") {\n        lessonUserRating.reading = value;\n      }\n      if (category == \"test\") {\n        lessonUserRating.test = value;\n      }\n    }\n    saveRating() {\n      this.lessonRatings.vocabulary = this.getCategoryValue(\"vocabulary\");\n      this.lessonRatings.listening = this.getCategoryValue(\"listening\");\n      this.lessonRatings.grammar = this.getCategoryValue(\"grammar\");\n      this.lessonRatings.speaking = this.getCategoryValue(\"speaking\");\n      this.lessonRatings.writing = this.getCategoryValue(\"writing\");\n      this.lessonRatings.revision = this.getCategoryValue(\"revision\");\n      this.lessonRatings.reading = this.getCategoryValue(\"reading\");\n      this.lessonRatings.test = this.getCategoryValue(\"test\");\n      this.lessonRatings.lessonId = this.lesson.id;\n      console.log(this.lessonUserRatings);\n      console.log(this.lessonRatings);\n      const lessonUserRatings = this.arrayValue.map(obj => obj.lessonUserRating);\n      if (this.mode === 'create') {\n        this.ratingAndReportService.createEndOfLessonRatings(lessonUserRatings, this.lessonRatings).pipe(switchMap(res => {\n          let lesson = {\n            id: this.lesson.id,\n            classroomId: this.lesson.classroomId,\n            status: LessonStatus.COMPLETED,\n            startingDate: this.lesson.startingDate.toString(),\n            duration: this.lesson.duration,\n            isRecccuring: this.lesson.isRecccuring,\n            answered: \"accepted\"\n          };\n          return this.lessonService.update(lesson);\n        }), switchMap(res => {\n          return of(null); //this.classroomService.changeClassroomStatus(this.lesson.classroomId, Status.COMPLETED)\n        })).subscribe(res => {\n          this.ratingSubmitted.emit({\n            rated: true\n          });\n          this.toast.setShowToastmessage({\n            severity: 'success',\n            summary: '',\n            detail: 'Lesson has been rated and stated as Completed. Students will get notifications.'\n          });\n          console.log(res);\n          this.lessonService.setUpdateListener(true);\n          this.calendarService.setUpdateListener(true);\n          this.classroomService.setSelectedClassroomPackagesUpdate(true);\n        });\n      } else if (this.mode === 'edit') {\n        // this.lessonRatings.id = this.lesson.id.toString();\n        this.ratingAndReportService.updateEndOfLessonRatings(lessonUserRatings, this.lessonRatings).pipe(switchMap(res => {\n          let lesson = {\n            id: this.lesson.id,\n            classroomId: this.lesson.classroomId,\n            status: LessonStatus.COMPLETED,\n            startingDate: this.lesson.startingDate.toString(),\n            duration: this.lesson.duration,\n            isRecccuring: this.lesson.isRecccuring,\n            answered: \"accepted\"\n          };\n          return this.lessonService.update(lesson);\n        })).subscribe(res => {\n          this.ratingSubmitted.emit({\n            rated: true\n          });\n          this.toast.setShowToastmessage({\n            severity: 'success',\n            summary: '',\n            detail: 'Lesson has been rated and stated as Completed. Students will get notifications.'\n          });\n          this.lessonService.setUpdateListener(true);\n          this.calendarService.setUpdateListener(true);\n        });\n      }\n    }\n    getCategoryValue(category) {\n      let value = this.variants.filter(el => el.rating.category.toLowerCase() === category);\n      return value.length > 0 ? value[0].value : -1;\n    }\n    getLessonBreakdownOfCategory(category) {\n      let value = this.lessonUserRatings.filter(el => el[category] === category);\n      return value.length > 0 ? value[0].value : -1;\n    }\n    getVariantIsChecked(rating) {\n      return this.variants.some(el => rating.category.toLowerCase() == el.rating.category.toLowerCase());\n    }\n    getGradient(chartArea, index, gradients) {\n      let width, height;\n      const chartWidth = chartArea.right - chartArea.left;\n      const chartHeight = chartArea.bottom - chartArea.top;\n      if (gradients[index] === null || width !== chartWidth || height !== chartHeight) {\n        width = chartWidth;\n        height = chartHeight;\n      }\n      return gradients[index];\n    }\n    getProgress(rating) {\n      return (rating * 10 / 100 * 100).toString();\n    }\n    getProgressPercent(rating, copyProgress) {\n      let value = 100 - rating * 10 / 100 * 100 - 5;\n      if (value < 0) {\n        copyProgress.remove();\n      }\n      return value.toFixed(0) + \"%\";\n    }\n    download(el) {\n      this.toast.setShowToastmessage({\n        severity: 'success',\n        summary: '',\n        detail: 'Rating will be downloaded shortly. Please wait.'\n      });\n      setTimeout(() => {\n        html2canvas(el).then(canvas => {\n          var imgData = canvas.toDataURL('image/png');\n          var imgWidth = 200;\n          var pageHeight = 295;\n          var imgHeight = canvas.height * imgWidth / canvas.width;\n          var heightLeft = imgHeight;\n          var doc = new jsPDF('p', 'mm', 'a4');\n          var position = 10; // give some top padding to first page\n          doc.addImage(imgData, 'PNG', 5, position, imgWidth, imgHeight);\n          heightLeft -= pageHeight;\n          while (heightLeft >= 0) {\n            position += heightLeft - imgHeight; // top padding for other pages\n            doc.addPage();\n            doc.addImage(imgData, 'PNG', 5, position, imgWidth, imgHeight);\n            heightLeft -= pageHeight;\n          }\n          doc.save(\"Lesson Rating for: \" + this.loggedInUser.firstName + \" \" + this.loggedInUser.lastName);\n        });\n      }, 500);\n    }\n    static #_ = this.ɵfac = function LessonRatingComponent_Factory(t) {\n      return new (t || LessonRatingComponent)(i0.ɵɵdirectiveInject(i1.ToastService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.RatingAndReportService), i0.ɵɵdirectiveInject(i4.LessonService), i0.ɵɵdirectiveInject(i5.CalendarService), i0.ɵɵdirectiveInject(i6.ClassroomService), i0.ɵɵdirectiveInject(i7.GeneralService), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LessonRatingComponent,\n      selectors: [[\"app-lesson-rating\"]],\n      viewQuery: function LessonRatingComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.chartLessonBreakdown = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.slider = _t.first);\n        }\n      },\n      inputs: {\n        lesson: \"lesson\",\n        classroom: \"classroom\",\n        mode: \"mode\",\n        lessonRatings: \"lessonRatings\",\n        lessonUserRatings: \"lessonUserRatings\",\n        users: \"users\"\n      },\n      outputs: {\n        ratingSubmitted: \"ratingSubmitted\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 30,\n      vars: 16,\n      consts: [[\"allRating\", \"\"], [1, \"lesson-rating\", \"p-2\"], [1, \"lesson-rating-header\", \"text-primary\", \"block-gradient\", \"font-sm\"], [1, \"lesson-rating-content\", \"block-gradient\", \"border-round-xl\", \"p-2\"], [1, \"lesoon-rating-title\", \"font-base\"], [1, \"rating\", \"mt-1\"], [1, \"rating-content\"], [1, \"left-side\", \"font-sm\", \"hidden\", \"sm:block\"], [1, \"right-side\"], [1, \"right-side-title\", \"font-base\"], [1, \"ratings\", \"flex-column\", \"sm:flex-row\"], [\"class\", \"checkbox col-3 flex gap-1\", 4, \"ngFor\", \"ngForOf\"], [1, \"classroom-rating-bar\", 3, \"ngClass\"], [3, \"variants\", \"prop\", \"colorList\"], [\"class\", \"classroom-rating-bar\", 4, \"ngIf\"], [1, \"style-three\"], [\"class\", \"rating\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"submit-button\", 3, \"click\", 4, \"ngIf\"], [1, \"checkbox\", \"col-3\", \"flex\", \"gap-1\"], [\"name\", \"groupname\", 3, \"ngModelChange\", \"onChange\", \"binary\", \"value\", \"ngModel\", \"disabled\"], [1, \"font-sm\"], [1, \"classroom-rating-bar\"], [1, \"rating\"], [1, \"rating-content\", \"mt-2\"], [1, \"left-side\", \"font-sm\"], [1, \"font-sm\", 3, \"hidden\"], [1, \"right-side-title\", 3, \"ngClass\"], [1, \"block-gradient\", \"inline-flex\", \"px-3\", \"py-1\", \"border-round-xl\", 3, \"ngClass\"], [1, \"flex\", \"gap-2\", \"font-xs\"], [1, \"p-field-radiobutton\", \"flex\", \"gap-2\"], [3, \"ngModelChange\", \"value\", \"ngModel\"], [\"for\", \"city3\"], [1, \"right-side-title\", \"m-t-30\", \"font-sm\", 3, \"ngClass\"], [\"class\", \"flex flex-column\", 4, \"ngIf\"], [\"class\", \"student-ratings\", 4, \"ngIf\"], [1, \"flex\", \"flex-column\"], [1, \"student-ratings\"], [\"class\", \"student-rating\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"guides font-xs\", 4, \"ngIf\"], [1, \"student-rating\"], [1, \"student-rating-title\", \"font-xs\"], [1, \"w-100\", 2, \"position\", \"relative\", 3, \"ngClass\"], [3, \"categoryRatingChanged\", \"rating\", \"category\"], [1, \"slider-values\"], [1, \"slider-value\", \"text-left\", \"font-2xs\", \"ml-1\"], [1, \"slider-value\", \"font-2xs\"], [1, \"slider-value\", \"text-right\", \"font-2xs\"], [1, \"guides\", \"font-xs\"], [1, \"submit-button\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"Submit\", \"icon\", \"pi pi-check-circle\", \"iconPos\", \"right\", 1, \"p-button-raised\", \"p-button-rounded\"], [1, \"light-purple-button\"]],\n      template: function LessonRatingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", null, 0)(3, \"div\", 2)(4, \"div\");\n          i0.ɵɵtext(5);\n          i0.ɵɵpipe(6, \"date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\");\n          i0.ɵɵtext(8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\");\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 3)(12, \"div\", 4);\n          i0.ɵɵtext(13, \" Ratings \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 5)(15, \"div\", 6)(16, \"div\", 7);\n          i0.ɵɵtext(17, \" CLASSROOM \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 8)(19, \"h5\", 9);\n          i0.ɵɵtext(20, \" Lesson was divided in... \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 10);\n          i0.ɵɵtemplate(22, LessonRatingComponent_div_22_Template, 4, 5, \"div\", 11);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"div\", 12);\n          i0.ɵɵelement(24, \"app-multi-handle-slider\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(25, LessonRatingComponent_div_25_Template, 1, 0, \"div\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(26, \"hr\", 15);\n          i0.ɵɵtemplate(27, LessonRatingComponent_div_27_Template, 23, 22, \"div\", 16)(28, LessonRatingComponent_div_28_Template, 2, 0, \"div\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(29, LessonRatingComponent_div_29_Template, 3, 0, \"div\", 17);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\" Lesson Date - \", i0.ɵɵpipeBind2(6, 11, ctx.lesson.startingDate, \"MMM d, y\"), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate1(\" Lesson Started at: \", ctx.generalService.getLessonTime(ctx.lesson.startingDate), \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" Lesson Duration: \", ctx.generalService.convertHoursToMinutesWithSuffix(ctx.lesson.duration, true), \" \");\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngForOf\", ctx.ratings);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(14, _c2, ctx.mode === \"view\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"variants\", ctx.variants)(\"prop\", \"value\")(\"colorList\", ctx.colors);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.mode != \"view\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.arrayValue);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.mode != \"view\");\n        }\n      },\n      dependencies: [i8.NgClass, i8.NgForOf, i8.NgIf, i9.NgControlStatus, i9.NgModel, i10.ButtonDirective, i11.Ripple, i12.Checkbox, i13.RadioButton, i14.LessonRatingRangeSliderComponent, i15.MultiHandleSliderComponent, i8.SlicePipe, i8.DatePipe],\n      styles: [\".lesson-rating[_ngcontent-%COMP%]{position:relative}.lesson-rating-header[_ngcontent-%COMP%]{width:100%;padding:10px;display:flex;justify-content:space-between;border-radius:10px}.lesson-rating-content[_ngcontent-%COMP%]   .lesoon-rating-title[_ngcontent-%COMP%]{font-weight:700}.lesson-rating-content[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .rating-content[_ngcontent-%COMP%]{display:flex}.lesson-rating-content[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .rating-content[_ngcontent-%COMP%]   .left-side[_ngcontent-%COMP%]{width:20%;font-weight:700;max-width:140px}.lesson-rating-content[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .rating-content[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%]{width:80%}.lesson-rating-content[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .rating-content[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%]   .ratings[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap}.lesson-rating-content[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .rating-content[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%]   .absent[_ngcontent-%COMP%]{border-radius:8px;background-color:#d9ddf0;padding:10px;display:flex;justify-content:space-between;width:200px;margin-top:10px}.lesson-rating-content[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .rating-content[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%]   .student-ratings[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap}.lesson-rating-content[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .rating-content[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%]   .student-ratings[_ngcontent-%COMP%]   .guides[_ngcontent-%COMP%]{padding:10px 10px 10px 0;font-size:15px}.lesson-rating-content[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .rating-content[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%]   .student-ratings[_ngcontent-%COMP%]   .student-rating[_ngcontent-%COMP%]{width:50%;display:flex;align-items:flex-start;padding:15px 30px 15px 0;box-sizing:border-box}.lesson-rating-content[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .rating-content[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%]   .student-ratings[_ngcontent-%COMP%]   .student-rating[_ngcontent-%COMP%]   .student-rating-title[_ngcontent-%COMP%]{width:40%}.lesson-rating-content[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .rating-content[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%]   .student-ratings[_ngcontent-%COMP%]   .student-rating[_ngcontent-%COMP%]   .student-rating-slider[_ngcontent-%COMP%]{width:60%}.lesson-rating-content[_ngcontent-%COMP%]   .rating[_ngcontent-%COMP%]   .classroom-rating-bar[_ngcontent-%COMP%]{width:100%;margin-top:30px}.style-three[_ngcontent-%COMP%]{border:0;border-bottom:3px dashed #ccc;margin:20px 0}.student-rating-title[_ngcontent-%COMP%]{width:40%}.student-rating-slider[_ngcontent-%COMP%]{width:60%}[_nghost-%COMP%]     .p-slider .p-slider-range{background:transparent}[_nghost-%COMP%]     .p-slider-horizontal{margin-top:6px;height:10px;background-image:linear-gradient(90deg,#b2c9fa,#b7ccfa,#bcd0fb,#c0d3fb,#c5d6fc 44%,#cadafc 56%,#cfddfc,#d4e1fc,#d8e4fd,#dde7fd)}  .slider-control{margin:0!important;padding:10px 0!important;background-image:linear-gradient(90deg,#93a2fb,#8695f3,#7988ec,#717ee5,#6472dd 44%,#5864d5 56%,#4c59cd,#3c49c3,#2e3ebd,#1c33b5);border-radius:32px!important;border:3px solid var(--primary-color)!important;width:auto!important;display:block!important}  .slider-handle{width:36px!important;height:36px!important;top:-21px!important;background-color:#867fc7!important;background:#867fc7!important;border:5px solid var(--primary-color)!important}.submit-button[_ngcontent-%COMP%]{display:flex;justify-content:center;padding:20px;position:sticky;bottom:0;z-index:10}[_nghost-%COMP%]     .progress-inner{background:transparent!important;text-align:end!important}[_nghost-%COMP%]     .progress-outer{background:linear-gradient(-90deg,#4895f0,#c758fd)!important;border:3px solid #e2e2e6!important;height:5px}[_nghost-%COMP%]     .p-checkbox .p-checkbox-box.p-highlight, [_nghost-%COMP%]     .p-checkbox .p-checkbox-box{border-radius:50px}.progress-outer[_ngcontent-%COMP%]{background:#000}.copy-progress[_ngcontent-%COMP%]{margin:10px 2%;padding:5px;background:#e2e2e6;color:#e2e2e6;border-radius:20px;text-align:center;position:absolute;right:-11px;top:0;height:5px;font-size:8px}.copy-progress[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;top:0;left:-12px;width:17px;height:17px;transition:all 1s;background-image:url(/assets/icons/for-progress.svg);background-size:100%;background-repeat:no-repeat}.points[_ngcontent-%COMP%]{display:flex;justify-content:space-between;width:100%;margin:5px}.slider-container[_ngcontent-%COMP%]{display:flex;flex-direction:row;justify-content:space-between}input[type=range][_ngcontent-%COMP%]{-webkit-appearance:none;width:100%;height:10px;background-image:linear-gradient(90deg,#b2c9fa,#b7ccfa,#bcd0fb,#c0d3fb,#c5d6fc 44%,#cadafc 56%,#cfddfc,#d4e1fc,#d8e4fd,#dde7fd);border-radius:5px;outline:none}input[type=range][_ngcontent-%COMP%]::-webkit-slider-thumb{appearance:none;width:20px;height:20px;background-color:#007bff;border-radius:50%;cursor:pointer}input[type=range][_ngcontent-%COMP%]::-moz-range-thumb{width:20px;height:20px;background-color:#007bff;border-radius:50%;cursor:pointer}.slider-values[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-top:2px}.slider-value[_ngcontent-%COMP%]{text-align:center;width:33%}.slider-value-text[_ngcontent-%COMP%]{position:absolute;top:-20px;left:50%;transform:translate(-50%);background-color:#fff;border:1px solid #ccc;padding:4px 8px;border-radius:4px}[_nghost-%COMP%]     .p-radiobutton{width:16px;height:16px}[_nghost-%COMP%]     .p-radiobutton-box{width:16px;height:16px}[_nghost-%COMP%]     .p-checkbox-box{width:14px;height:14px}input[type=range][_ngcontent-%COMP%]::-webkit-slider-thumb{appearance:none;height:15px;width:15px;background:#3f51b5;border-radius:50%}input[type=range][_ngcontent-%COMP%]::-moz-range-thumb{height:15px;width:15px;background:#3f51b5;border-radius:50%}\"]\n    });\n  }\n  return LessonRatingComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}