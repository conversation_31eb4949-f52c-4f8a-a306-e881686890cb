{"ast": null, "code": "import { concat } from '../observable/concat';\nimport { popScheduler } from '../util/args';\nimport { operate } from '../util/lift';\nexport function startWith(...values) {\n  const scheduler = popScheduler(values);\n  return operate((source, subscriber) => {\n    (scheduler ? concat(values, source, scheduler) : concat(values, source)).subscribe(subscriber);\n  });\n}", "map": {"version": 3, "names": ["concat", "popScheduler", "operate", "startWith", "values", "scheduler", "source", "subscriber", "subscribe"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/rxjs/dist/esm/internal/operators/startWith.js"], "sourcesContent": ["import { concat } from '../observable/concat';\nimport { popScheduler } from '../util/args';\nimport { operate } from '../util/lift';\nexport function startWith(...values) {\n    const scheduler = popScheduler(values);\n    return operate((source, subscriber) => {\n        (scheduler ? concat(values, source, scheduler) : concat(values, source)).subscribe(subscriber);\n    });\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,sBAAsB;AAC7C,SAASC,YAAY,QAAQ,cAAc;AAC3C,SAASC,OAAO,QAAQ,cAAc;AACtC,OAAO,SAASC,SAASA,CAAC,GAAGC,MAAM,EAAE;EACjC,MAAMC,SAAS,GAAGJ,YAAY,CAACG,MAAM,CAAC;EACtC,OAAOF,OAAO,CAAC,CAACI,MAAM,EAAEC,UAAU,KAAK;IACnC,CAACF,SAAS,GAAGL,MAAM,CAACI,MAAM,EAAEE,MAAM,EAAED,SAAS,CAAC,GAAGL,MAAM,CAACI,MAAM,EAAEE,MAAM,CAAC,EAAEE,SAAS,CAACD,UAAU,CAAC;EAClG,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}