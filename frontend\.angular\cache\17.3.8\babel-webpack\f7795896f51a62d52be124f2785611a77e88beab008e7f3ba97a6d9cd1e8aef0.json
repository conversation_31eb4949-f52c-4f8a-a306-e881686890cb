{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Slovak [sk]\n//! author : <PERSON> : https://github.com/k2s\n//! based on work of petrbela : https://github.com/petrbela\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var months = 'január_február_marec_apríl_máj_jún_júl_august_september_október_november_december'.split('_'),\n    monthsShort = 'jan_feb_mar_apr_máj_jún_júl_aug_sep_okt_nov_dec'.split('_');\n  function plural(n) {\n    return n > 1 && n < 5;\n  }\n  function translate(number, withoutSuffix, key, isFuture) {\n    var result = number + ' ';\n    switch (key) {\n      case 's':\n        // a few seconds / in a few seconds / a few seconds ago\n        return withoutSuffix || isFuture ? 'pár sekúnd' : 'pár sekundami';\n      case 'ss':\n        // 9 seconds / in 9 seconds / 9 seconds ago\n        if (withoutSuffix || isFuture) {\n          return result + (plural(number) ? 'sekundy' : 'sekúnd');\n        } else {\n          return result + 'sekundami';\n        }\n      case 'm':\n        // a minute / in a minute / a minute ago\n        return withoutSuffix ? 'minúta' : isFuture ? 'minútu' : 'minútou';\n      case 'mm':\n        // 9 minutes / in 9 minutes / 9 minutes ago\n        if (withoutSuffix || isFuture) {\n          return result + (plural(number) ? 'minúty' : 'minút');\n        } else {\n          return result + 'minútami';\n        }\n      case 'h':\n        // an hour / in an hour / an hour ago\n        return withoutSuffix ? 'hodina' : isFuture ? 'hodinu' : 'hodinou';\n      case 'hh':\n        // 9 hours / in 9 hours / 9 hours ago\n        if (withoutSuffix || isFuture) {\n          return result + (plural(number) ? 'hodiny' : 'hodín');\n        } else {\n          return result + 'hodinami';\n        }\n      case 'd':\n        // a day / in a day / a day ago\n        return withoutSuffix || isFuture ? 'deň' : 'dňom';\n      case 'dd':\n        // 9 days / in 9 days / 9 days ago\n        if (withoutSuffix || isFuture) {\n          return result + (plural(number) ? 'dni' : 'dní');\n        } else {\n          return result + 'dňami';\n        }\n      case 'M':\n        // a month / in a month / a month ago\n        return withoutSuffix || isFuture ? 'mesiac' : 'mesiacom';\n      case 'MM':\n        // 9 months / in 9 months / 9 months ago\n        if (withoutSuffix || isFuture) {\n          return result + (plural(number) ? 'mesiace' : 'mesiacov');\n        } else {\n          return result + 'mesiacmi';\n        }\n      case 'y':\n        // a year / in a year / a year ago\n        return withoutSuffix || isFuture ? 'rok' : 'rokom';\n      case 'yy':\n        // 9 years / in 9 years / 9 years ago\n        if (withoutSuffix || isFuture) {\n          return result + (plural(number) ? 'roky' : 'rokov');\n        } else {\n          return result + 'rokmi';\n        }\n    }\n  }\n  var sk = moment.defineLocale('sk', {\n    months: months,\n    monthsShort: monthsShort,\n    weekdays: 'nedeľa_pondelok_utorok_streda_štvrtok_piatok_sobota'.split('_'),\n    weekdaysShort: 'ne_po_ut_st_št_pi_so'.split('_'),\n    weekdaysMin: 'ne_po_ut_st_št_pi_so'.split('_'),\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D. MMMM YYYY',\n      LLL: 'D. MMMM YYYY H:mm',\n      LLLL: 'dddd D. MMMM YYYY H:mm'\n    },\n    calendar: {\n      sameDay: '[dnes o] LT',\n      nextDay: '[zajtra o] LT',\n      nextWeek: function () {\n        switch (this.day()) {\n          case 0:\n            return '[v nedeľu o] LT';\n          case 1:\n          case 2:\n            return '[v] dddd [o] LT';\n          case 3:\n            return '[v stredu o] LT';\n          case 4:\n            return '[vo štvrtok o] LT';\n          case 5:\n            return '[v piatok o] LT';\n          case 6:\n            return '[v sobotu o] LT';\n        }\n      },\n      lastDay: '[včera o] LT',\n      lastWeek: function () {\n        switch (this.day()) {\n          case 0:\n            return '[minulú nedeľu o] LT';\n          case 1:\n          case 2:\n            return '[minulý] dddd [o] LT';\n          case 3:\n            return '[minulú stredu o] LT';\n          case 4:\n          case 5:\n            return '[minulý] dddd [o] LT';\n          case 6:\n            return '[minulú sobotu o] LT';\n        }\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'za %s',\n      past: 'pred %s',\n      s: translate,\n      ss: translate,\n      m: translate,\n      mm: translate,\n      h: translate,\n      hh: translate,\n      d: translate,\n      dd: translate,\n      M: translate,\n      MM: translate,\n      y: translate,\n      yy: translate\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return sk;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "months", "split", "monthsShort", "plural", "n", "translate", "number", "withoutSuffix", "key", "isFuture", "result", "sk", "defineLocale", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "calendar", "sameDay", "nextDay", "nextWeek", "day", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/moment/locale/sk.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Slovak [sk]\n//! author : <PERSON> : https://github.com/k2s\n//! based on work of petrbela : https://github.com/petrbela\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var months =\n            'január_február_marec_apríl_máj_jún_júl_august_september_október_november_december'.split(\n                '_'\n            ),\n        monthsShort = 'jan_feb_mar_apr_máj_jún_júl_aug_sep_okt_nov_dec'.split('_');\n    function plural(n) {\n        return n > 1 && n < 5;\n    }\n    function translate(number, withoutSuffix, key, isFuture) {\n        var result = number + ' ';\n        switch (key) {\n            case 's': // a few seconds / in a few seconds / a few seconds ago\n                return withoutSuffix || isFuture ? 'pár sekúnd' : 'pár sekundami';\n            case 'ss': // 9 seconds / in 9 seconds / 9 seconds ago\n                if (withoutSuffix || isFuture) {\n                    return result + (plural(number) ? 'sekundy' : 'sekúnd');\n                } else {\n                    return result + 'sekundami';\n                }\n            case 'm': // a minute / in a minute / a minute ago\n                return withoutSuffix ? 'minúta' : isFuture ? 'minútu' : 'minútou';\n            case 'mm': // 9 minutes / in 9 minutes / 9 minutes ago\n                if (withoutSuffix || isFuture) {\n                    return result + (plural(number) ? 'minúty' : 'minút');\n                } else {\n                    return result + 'minútami';\n                }\n            case 'h': // an hour / in an hour / an hour ago\n                return withoutSuffix ? 'hodina' : isFuture ? 'hodinu' : 'hodinou';\n            case 'hh': // 9 hours / in 9 hours / 9 hours ago\n                if (withoutSuffix || isFuture) {\n                    return result + (plural(number) ? 'hodiny' : 'hodín');\n                } else {\n                    return result + 'hodinami';\n                }\n            case 'd': // a day / in a day / a day ago\n                return withoutSuffix || isFuture ? 'deň' : 'dňom';\n            case 'dd': // 9 days / in 9 days / 9 days ago\n                if (withoutSuffix || isFuture) {\n                    return result + (plural(number) ? 'dni' : 'dní');\n                } else {\n                    return result + 'dňami';\n                }\n            case 'M': // a month / in a month / a month ago\n                return withoutSuffix || isFuture ? 'mesiac' : 'mesiacom';\n            case 'MM': // 9 months / in 9 months / 9 months ago\n                if (withoutSuffix || isFuture) {\n                    return result + (plural(number) ? 'mesiace' : 'mesiacov');\n                } else {\n                    return result + 'mesiacmi';\n                }\n            case 'y': // a year / in a year / a year ago\n                return withoutSuffix || isFuture ? 'rok' : 'rokom';\n            case 'yy': // 9 years / in 9 years / 9 years ago\n                if (withoutSuffix || isFuture) {\n                    return result + (plural(number) ? 'roky' : 'rokov');\n                } else {\n                    return result + 'rokmi';\n                }\n        }\n    }\n\n    var sk = moment.defineLocale('sk', {\n        months: months,\n        monthsShort: monthsShort,\n        weekdays: 'nedeľa_pondelok_utorok_streda_štvrtok_piatok_sobota'.split('_'),\n        weekdaysShort: 'ne_po_ut_st_št_pi_so'.split('_'),\n        weekdaysMin: 'ne_po_ut_st_št_pi_so'.split('_'),\n        longDateFormat: {\n            LT: 'H:mm',\n            LTS: 'H:mm:ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D. MMMM YYYY',\n            LLL: 'D. MMMM YYYY H:mm',\n            LLLL: 'dddd D. MMMM YYYY H:mm',\n        },\n        calendar: {\n            sameDay: '[dnes o] LT',\n            nextDay: '[zajtra o] LT',\n            nextWeek: function () {\n                switch (this.day()) {\n                    case 0:\n                        return '[v nedeľu o] LT';\n                    case 1:\n                    case 2:\n                        return '[v] dddd [o] LT';\n                    case 3:\n                        return '[v stredu o] LT';\n                    case 4:\n                        return '[vo štvrtok o] LT';\n                    case 5:\n                        return '[v piatok o] LT';\n                    case 6:\n                        return '[v sobotu o] LT';\n                }\n            },\n            lastDay: '[včera o] LT',\n            lastWeek: function () {\n                switch (this.day()) {\n                    case 0:\n                        return '[minulú nedeľu o] LT';\n                    case 1:\n                    case 2:\n                        return '[minulý] dddd [o] LT';\n                    case 3:\n                        return '[minulú stredu o] LT';\n                    case 4:\n                    case 5:\n                        return '[minulý] dddd [o] LT';\n                    case 6:\n                        return '[minulú sobotu o] LT';\n                }\n            },\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'za %s',\n            past: 'pred %s',\n            s: translate,\n            ss: translate,\n            m: translate,\n            mm: translate,\n            h: translate,\n            hh: translate,\n            d: translate,\n            dd: translate,\n            M: translate,\n            MM: translate,\n            y: translate,\n            yy: translate,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    return sk;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,MAAM,GACF,mFAAmF,CAACC,KAAK,CACrF,GACJ,CAAC;IACLC,WAAW,GAAG,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;EAC9E,SAASE,MAAMA,CAACC,CAAC,EAAE;IACf,OAAOA,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,CAAC;EACzB;EACA,SAASC,SAASA,CAACC,MAAM,EAAEC,aAAa,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IACrD,IAAIC,MAAM,GAAGJ,MAAM,GAAG,GAAG;IACzB,QAAQE,GAAG;MACP,KAAK,GAAG;QAAE;QACN,OAAOD,aAAa,IAAIE,QAAQ,GAAG,YAAY,GAAG,eAAe;MACrE,KAAK,IAAI;QAAE;QACP,IAAIF,aAAa,IAAIE,QAAQ,EAAE;UAC3B,OAAOC,MAAM,IAAIP,MAAM,CAACG,MAAM,CAAC,GAAG,SAAS,GAAG,QAAQ,CAAC;QAC3D,CAAC,MAAM;UACH,OAAOI,MAAM,GAAG,WAAW;QAC/B;MACJ,KAAK,GAAG;QAAE;QACN,OAAOH,aAAa,GAAG,QAAQ,GAAGE,QAAQ,GAAG,QAAQ,GAAG,SAAS;MACrE,KAAK,IAAI;QAAE;QACP,IAAIF,aAAa,IAAIE,QAAQ,EAAE;UAC3B,OAAOC,MAAM,IAAIP,MAAM,CAACG,MAAM,CAAC,GAAG,QAAQ,GAAG,OAAO,CAAC;QACzD,CAAC,MAAM;UACH,OAAOI,MAAM,GAAG,UAAU;QAC9B;MACJ,KAAK,GAAG;QAAE;QACN,OAAOH,aAAa,GAAG,QAAQ,GAAGE,QAAQ,GAAG,QAAQ,GAAG,SAAS;MACrE,KAAK,IAAI;QAAE;QACP,IAAIF,aAAa,IAAIE,QAAQ,EAAE;UAC3B,OAAOC,MAAM,IAAIP,MAAM,CAACG,MAAM,CAAC,GAAG,QAAQ,GAAG,OAAO,CAAC;QACzD,CAAC,MAAM;UACH,OAAOI,MAAM,GAAG,UAAU;QAC9B;MACJ,KAAK,GAAG;QAAE;QACN,OAAOH,aAAa,IAAIE,QAAQ,GAAG,KAAK,GAAG,MAAM;MACrD,KAAK,IAAI;QAAE;QACP,IAAIF,aAAa,IAAIE,QAAQ,EAAE;UAC3B,OAAOC,MAAM,IAAIP,MAAM,CAACG,MAAM,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC;QACpD,CAAC,MAAM;UACH,OAAOI,MAAM,GAAG,OAAO;QAC3B;MACJ,KAAK,GAAG;QAAE;QACN,OAAOH,aAAa,IAAIE,QAAQ,GAAG,QAAQ,GAAG,UAAU;MAC5D,KAAK,IAAI;QAAE;QACP,IAAIF,aAAa,IAAIE,QAAQ,EAAE;UAC3B,OAAOC,MAAM,IAAIP,MAAM,CAACG,MAAM,CAAC,GAAG,SAAS,GAAG,UAAU,CAAC;QAC7D,CAAC,MAAM;UACH,OAAOI,MAAM,GAAG,UAAU;QAC9B;MACJ,KAAK,GAAG;QAAE;QACN,OAAOH,aAAa,IAAIE,QAAQ,GAAG,KAAK,GAAG,OAAO;MACtD,KAAK,IAAI;QAAE;QACP,IAAIF,aAAa,IAAIE,QAAQ,EAAE;UAC3B,OAAOC,MAAM,IAAIP,MAAM,CAACG,MAAM,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC;QACvD,CAAC,MAAM;UACH,OAAOI,MAAM,GAAG,OAAO;QAC3B;IACR;EACJ;EAEA,IAAIC,EAAE,GAAGZ,MAAM,CAACa,YAAY,CAAC,IAAI,EAAE;IAC/BZ,MAAM,EAAEA,MAAM;IACdE,WAAW,EAAEA,WAAW;IACxBW,QAAQ,EAAE,qDAAqD,CAACZ,KAAK,CAAC,GAAG,CAAC;IAC1Ea,aAAa,EAAE,sBAAsB,CAACb,KAAK,CAAC,GAAG,CAAC;IAChDc,WAAW,EAAE,sBAAsB,CAACd,KAAK,CAAC,GAAG,CAAC;IAC9Ce,cAAc,EAAE;MACZC,EAAE,EAAE,MAAM;MACVC,GAAG,EAAE,SAAS;MACdC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,cAAc;MAClBC,GAAG,EAAE,mBAAmB;MACxBC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,aAAa;MACtBC,OAAO,EAAE,eAAe;MACxBC,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,QAAQ,IAAI,CAACC,GAAG,CAAC,CAAC;UACd,KAAK,CAAC;YACF,OAAO,iBAAiB;UAC5B,KAAK,CAAC;UACN,KAAK,CAAC;YACF,OAAO,iBAAiB;UAC5B,KAAK,CAAC;YACF,OAAO,iBAAiB;UAC5B,KAAK,CAAC;YACF,OAAO,mBAAmB;UAC9B,KAAK,CAAC;YACF,OAAO,iBAAiB;UAC5B,KAAK,CAAC;YACF,OAAO,iBAAiB;QAChC;MACJ,CAAC;MACDC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,SAAAA,CAAA,EAAY;QAClB,QAAQ,IAAI,CAACF,GAAG,CAAC,CAAC;UACd,KAAK,CAAC;YACF,OAAO,sBAAsB;UACjC,KAAK,CAAC;UACN,KAAK,CAAC;YACF,OAAO,sBAAsB;UACjC,KAAK,CAAC;YACF,OAAO,sBAAsB;UACjC,KAAK,CAAC;UACN,KAAK,CAAC;YACF,OAAO,sBAAsB;UACjC,KAAK,CAAC;YACF,OAAO,sBAAsB;QACrC;MACJ,CAAC;MACDG,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,OAAO;MACfC,IAAI,EAAE,SAAS;MACfC,CAAC,EAAE7B,SAAS;MACZ8B,EAAE,EAAE9B,SAAS;MACb+B,CAAC,EAAE/B,SAAS;MACZgC,EAAE,EAAEhC,SAAS;MACbiC,CAAC,EAAEjC,SAAS;MACZkC,EAAE,EAAElC,SAAS;MACbmC,CAAC,EAAEnC,SAAS;MACZoC,EAAE,EAAEpC,SAAS;MACbqC,CAAC,EAAErC,SAAS;MACZsC,EAAE,EAAEtC,SAAS;MACbuC,CAAC,EAAEvC,SAAS;MACZwC,EAAE,EAAExC;IACR,CAAC;IACDyC,sBAAsB,EAAE,WAAW;IACnCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,OAAOvC,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}