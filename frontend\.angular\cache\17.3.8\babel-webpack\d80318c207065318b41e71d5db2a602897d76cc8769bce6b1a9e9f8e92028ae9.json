{"ast": null, "code": "import { MODE, URLS } from './constants';\nimport { RevolutPaymentsLoader } from './paymentsLoader';\nimport { getVersionedUrl, loadModule } from './utils';\nimport { RevolutUpsellLoader } from './upsellLoader';\nimport { RevolutPaymentsVersionLoader } from './versionLoader';\nvar loaded = null;\n/**\n * Load [`RevolutCheckout.js`](https://developer.revolut.com/docs/revolut-checkout-js/#revolutcheckout)\n * and create [`Instance`](https://developer.revolut.com/docs/revolut-checkout-js/#instance) for the order `token`.\n *\n * @param token `public_id`  from [create payment order](https://developer.revolut.com/api-reference/merchant/#operation/createOrder) API request\n * @param mode [API](https://developer.revolut.com/docs/revolut-checkout-js/#revolutcheckout-parameters) environment, defaults to `'prod'`\n *\n * @see [`RevolutCheckout.js` reference](https://developer.revolut.com/docs/revolut-checkout-js)\n *\n * @example\n * ```js\n * RevolutCheckout('TOKEN_XXX', 'prod').then(function(instance) {\n *   // Work with instance\n * });\n * ```\n */\nexport function RevolutCheckoutLoader(token, mode) {\n  if (mode === void 0) {\n    mode = RevolutCheckoutLoader.mode;\n  }\n  if (loaded) {\n    return Promise.resolve(loaded(token));\n  }\n  return RevolutPaymentsVersionLoader(mode).then(function (version) {\n    return loadRevolutCheckout(version, token, mode);\n  });\n}\nfunction loadRevolutCheckout(version, token, mode) {\n  return loadModule({\n    src: getVersionedUrl(URLS[mode].embed, version),\n    id: 'revolut-checkout',\n    name: 'RevolutCheckout'\n  }).then(function (scriptElement) {\n    if (typeof RevolutCheckout === 'function') {\n      loaded = RevolutCheckout;\n      delete window.RevolutCheckout;\n      return loaded(token);\n    } else {\n      document.head.removeChild(scriptElement);\n      throw new Error(\"'RevolutCheckout' failed to load: RevolutCheckout is not a function\");\n    }\n  });\n}\nRevolutCheckoutLoader.mode = MODE.PRODUCTION;\nRevolutCheckoutLoader.payments = function (_a) {\n  var locale = _a.locale,\n    publicToken = _a.publicToken,\n    _b = _a.mode,\n    mode = _b === void 0 ? RevolutCheckoutLoader.mode : _b;\n  return RevolutPaymentsLoader(publicToken, mode, locale);\n};\nRevolutCheckoutLoader.upsell = function (_a) {\n  var locale = _a.locale,\n    publicToken = _a.publicToken,\n    _b = _a.mode,\n    mode = _b === void 0 ? RevolutCheckoutLoader.mode : _b;\n  return RevolutUpsellLoader(publicToken, mode, locale);\n};", "map": {"version": 3, "names": ["MODE", "URLS", "RevolutPaymentsLoader", "getVersionedUrl", "loadModule", "RevolutUpsellLoader", "RevolutPaymentsVersionLoader", "loaded", "RevolutCheckoutLoader", "token", "mode", "Promise", "resolve", "then", "version", "loadRevolutCheckout", "src", "embed", "id", "name", "scriptElement", "RevolutCheckout", "window", "document", "head", "<PERSON><PERSON><PERSON><PERSON>", "Error", "PRODUCTION", "payments", "_a", "locale", "publicToken", "_b", "upsell"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@revolut/checkout/esm/loader.js"], "sourcesContent": ["import { MODE, URLS } from './constants';\nimport { RevolutPaymentsLoader } from './paymentsLoader';\nimport { getVersionedUrl, loadModule } from './utils';\nimport { RevolutUpsellLoader } from './upsellLoader';\nimport { RevolutPaymentsVersionLoader } from './versionLoader';\nvar loaded = null;\n/**\n * Load [`RevolutCheckout.js`](https://developer.revolut.com/docs/revolut-checkout-js/#revolutcheckout)\n * and create [`Instance`](https://developer.revolut.com/docs/revolut-checkout-js/#instance) for the order `token`.\n *\n * @param token `public_id`  from [create payment order](https://developer.revolut.com/api-reference/merchant/#operation/createOrder) API request\n * @param mode [API](https://developer.revolut.com/docs/revolut-checkout-js/#revolutcheckout-parameters) environment, defaults to `'prod'`\n *\n * @see [`RevolutCheckout.js` reference](https://developer.revolut.com/docs/revolut-checkout-js)\n *\n * @example\n * ```js\n * RevolutCheckout('TOKEN_XXX', 'prod').then(function(instance) {\n *   // Work with instance\n * });\n * ```\n */\nexport function RevolutCheckoutLoader(token, mode) {\n    if (mode === void 0) { mode = RevolutCheckoutLoader.mode; }\n    if (loaded) {\n        return Promise.resolve(loaded(token));\n    }\n    return RevolutPaymentsVersionLoader(mode).then(function (version) {\n        return loadRevolutCheckout(version, token, mode);\n    });\n}\nfunction loadRevolutCheckout(version, token, mode) {\n    return loadModule({\n        src: getVersionedUrl(URLS[mode].embed, version),\n        id: 'revolut-checkout',\n        name: 'RevolutCheckout',\n    }).then(function (scriptElement) {\n        if (typeof RevolutCheckout === 'function') {\n            loaded = RevolutCheckout;\n            delete window.RevolutCheckout;\n            return loaded(token);\n        }\n        else {\n            document.head.removeChild(scriptElement);\n            throw new Error(\"'RevolutCheckout' failed to load: RevolutCheckout is not a function\");\n        }\n    });\n}\nRevolutCheckoutLoader.mode = MODE.PRODUCTION;\nRevolutCheckoutLoader.payments = function (_a) {\n    var locale = _a.locale, publicToken = _a.publicToken, _b = _a.mode, mode = _b === void 0 ? RevolutCheckoutLoader.mode : _b;\n    return RevolutPaymentsLoader(publicToken, mode, locale);\n};\nRevolutCheckoutLoader.upsell = function (_a) {\n    var locale = _a.locale, publicToken = _a.publicToken, _b = _a.mode, mode = _b === void 0 ? RevolutCheckoutLoader.mode : _b;\n    return RevolutUpsellLoader(publicToken, mode, locale);\n};\n"], "mappings": "AAAA,SAASA,IAAI,EAAEC,IAAI,QAAQ,aAAa;AACxC,SAASC,qBAAqB,QAAQ,kBAAkB;AACxD,SAASC,eAAe,EAAEC,UAAU,QAAQ,SAAS;AACrD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,4BAA4B,QAAQ,iBAAiB;AAC9D,IAAIC,MAAM,GAAG,IAAI;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAC/C,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IAAEA,IAAI,GAAGF,qBAAqB,CAACE,IAAI;EAAE;EAC1D,IAAIH,MAAM,EAAE;IACR,OAAOI,OAAO,CAACC,OAAO,CAACL,MAAM,CAACE,KAAK,CAAC,CAAC;EACzC;EACA,OAAOH,4BAA4B,CAACI,IAAI,CAAC,CAACG,IAAI,CAAC,UAAUC,OAAO,EAAE;IAC9D,OAAOC,mBAAmB,CAACD,OAAO,EAAEL,KAAK,EAAEC,IAAI,CAAC;EACpD,CAAC,CAAC;AACN;AACA,SAASK,mBAAmBA,CAACD,OAAO,EAAEL,KAAK,EAAEC,IAAI,EAAE;EAC/C,OAAON,UAAU,CAAC;IACdY,GAAG,EAAEb,eAAe,CAACF,IAAI,CAACS,IAAI,CAAC,CAACO,KAAK,EAAEH,OAAO,CAAC;IAC/CI,EAAE,EAAE,kBAAkB;IACtBC,IAAI,EAAE;EACV,CAAC,CAAC,CAACN,IAAI,CAAC,UAAUO,aAAa,EAAE;IAC7B,IAAI,OAAOC,eAAe,KAAK,UAAU,EAAE;MACvCd,MAAM,GAAGc,eAAe;MACxB,OAAOC,MAAM,CAACD,eAAe;MAC7B,OAAOd,MAAM,CAACE,KAAK,CAAC;IACxB,CAAC,MACI;MACDc,QAAQ,CAACC,IAAI,CAACC,WAAW,CAACL,aAAa,CAAC;MACxC,MAAM,IAAIM,KAAK,CAAC,qEAAqE,CAAC;IAC1F;EACJ,CAAC,CAAC;AACN;AACAlB,qBAAqB,CAACE,IAAI,GAAGV,IAAI,CAAC2B,UAAU;AAC5CnB,qBAAqB,CAACoB,QAAQ,GAAG,UAAUC,EAAE,EAAE;EAC3C,IAAIC,MAAM,GAAGD,EAAE,CAACC,MAAM;IAAEC,WAAW,GAAGF,EAAE,CAACE,WAAW;IAAEC,EAAE,GAAGH,EAAE,CAACnB,IAAI;IAAEA,IAAI,GAAGsB,EAAE,KAAK,KAAK,CAAC,GAAGxB,qBAAqB,CAACE,IAAI,GAAGsB,EAAE;EAC1H,OAAO9B,qBAAqB,CAAC6B,WAAW,EAAErB,IAAI,EAAEoB,MAAM,CAAC;AAC3D,CAAC;AACDtB,qBAAqB,CAACyB,MAAM,GAAG,UAAUJ,EAAE,EAAE;EACzC,IAAIC,MAAM,GAAGD,EAAE,CAACC,MAAM;IAAEC,WAAW,GAAGF,EAAE,CAACE,WAAW;IAAEC,EAAE,GAAGH,EAAE,CAACnB,IAAI;IAAEA,IAAI,GAAGsB,EAAE,KAAK,KAAK,CAAC,GAAGxB,qBAAqB,CAACE,IAAI,GAAGsB,EAAE;EAC1H,OAAO3B,mBAAmB,CAAC0B,WAAW,EAAErB,IAAI,EAAEoB,MAAM,CAAC;AACzD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}