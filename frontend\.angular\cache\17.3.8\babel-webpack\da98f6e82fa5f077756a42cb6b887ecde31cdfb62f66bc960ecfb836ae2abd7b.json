{"ast": null, "code": "import { ɵAPP_CHECK_PROVIDER_NAME, ɵgetAllInstancesOf, ɵgetDefaultInstanceOf, ɵAppCheckInstances, VERSION, ɵAngularFireSchedulers, ɵzoneWrap } from '@angular/fire';\nexport { ɵAppCheckInstances as AppCheckInstances } from '@angular/fire';\nimport { from, timer } from 'rxjs';\nimport { concatMap, distinct } from 'rxjs/operators';\nimport { isPlatformServer } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, isDevMode, Optional, PLATFORM_ID, NgModule, NgZone, Injector, makeEnvironmentProviders } from '@angular/core';\nimport { FirebaseApp, FirebaseApps } from '@angular/fire/app';\nimport { registerVersion } from 'firebase/app';\nimport { getLimitedUseToken as getLimitedUseToken$1, getToken as getToken$1, initializeAppCheck as initializeAppCheck$1, onTokenChanged as onTokenChanged$1, setTokenAutoRefreshEnabled as setTokenAutoRefreshEnabled$1 } from 'firebase/app-check';\nexport * from 'firebase/app-check';\nclass AppCheck {\n  constructor(appCheck) {\n    return appCheck;\n  }\n}\nconst appCheckInstance$ = timer(0, 300).pipe(concatMap(() => from(ɵgetAllInstancesOf(ɵAPP_CHECK_PROVIDER_NAME))), distinct());\nconst PROVIDED_APP_CHECK_INSTANCES = new InjectionToken('angularfire2.app-check-instances');\nfunction defaultAppCheckInstanceFactory(provided, defaultApp) {\n  const defaultAppCheck = ɵgetDefaultInstanceOf(ɵAPP_CHECK_PROVIDER_NAME, provided, defaultApp);\n  return defaultAppCheck && new AppCheck(defaultAppCheck);\n}\nconst LOCALHOSTS = ['localhost', '0.0.0.0', '127.0.0.1'];\nconst isLocalhost = typeof window !== 'undefined' && LOCALHOSTS.includes(window.location.hostname);\nfunction appCheckInstanceFactory(fn) {\n  return (zone, injector, platformId) => {\n    // Node should use admin token provider, browser devmode and localhost should use debug token\n    if (!isPlatformServer(platformId) && (isDevMode() || isLocalhost)) {\n      globalThis.FIREBASE_APPCHECK_DEBUG_TOKEN ??= true;\n    }\n    const appCheck = zone.runOutsideAngular(() => fn(injector));\n    return new AppCheck(appCheck);\n  };\n}\nconst APP_CHECK_INSTANCES_PROVIDER = {\n  provide: ɵAppCheckInstances,\n  deps: [[new Optional(), PROVIDED_APP_CHECK_INSTANCES]]\n};\nconst DEFAULT_APP_CHECK_INSTANCE_PROVIDER = {\n  provide: AppCheck,\n  useFactory: defaultAppCheckInstanceFactory,\n  deps: [[new Optional(), PROVIDED_APP_CHECK_INSTANCES], FirebaseApp, PLATFORM_ID]\n};\nclass AppCheckModule {\n  constructor() {\n    registerVersion('angularfire', VERSION.full, 'app-check');\n  }\n  static ɵfac = function AppCheckModule_Factory(t) {\n    return new (t || AppCheckModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: AppCheckModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [DEFAULT_APP_CHECK_INSTANCE_PROVIDER, APP_CHECK_INSTANCES_PROVIDER]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AppCheckModule, [{\n    type: NgModule,\n    args: [{\n      providers: [DEFAULT_APP_CHECK_INSTANCE_PROVIDER, APP_CHECK_INSTANCES_PROVIDER]\n    }]\n  }], () => [], null);\n})();\nfunction provideAppCheck(fn, ...deps) {\n  registerVersion('angularfire', VERSION.full, 'app-check');\n  return makeEnvironmentProviders([DEFAULT_APP_CHECK_INSTANCE_PROVIDER, APP_CHECK_INSTANCES_PROVIDER, {\n    provide: PROVIDED_APP_CHECK_INSTANCES,\n    useFactory: appCheckInstanceFactory(fn),\n    multi: true,\n    deps: [NgZone, Injector, PLATFORM_ID, ɵAngularFireSchedulers, FirebaseApps, ...deps]\n  }]);\n}\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\nconst getLimitedUseToken = ɵzoneWrap(getLimitedUseToken$1, true);\nconst getToken = ɵzoneWrap(getToken$1, true);\nconst initializeAppCheck = ɵzoneWrap(initializeAppCheck$1, true);\nconst onTokenChanged = ɵzoneWrap(onTokenChanged$1, true);\nconst setTokenAutoRefreshEnabled = ɵzoneWrap(setTokenAutoRefreshEnabled$1, true);\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AppCheck, AppCheckModule, appCheckInstance$, getLimitedUseToken, getToken, initializeAppCheck, onTokenChanged, provideAppCheck, setTokenAutoRefreshEnabled };", "map": {"version": 3, "names": ["ɵAPP_CHECK_PROVIDER_NAME", "ɵgetAllInstancesOf", "ɵgetDefaultInstanceOf", "ɵAppCheckInstances", "VERSION", "ɵAngularFireSchedulers", "ɵzoneWrap", "AppCheckInstances", "from", "timer", "concatMap", "distinct", "isPlatformServer", "i0", "InjectionToken", "isDevMode", "Optional", "PLATFORM_ID", "NgModule", "NgZone", "Injector", "makeEnvironmentProviders", "FirebaseApp", "FirebaseApps", "registerVersion", "getLimitedUseToken", "getLimitedUseToken$1", "getToken", "getToken$1", "initializeAppCheck", "initializeAppCheck$1", "onTokenChanged", "onTokenChanged$1", "setTokenAutoRefreshEnabled", "setTokenAutoRefreshEnabled$1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "appCheck", "appCheckInstance$", "pipe", "PROVIDED_APP_CHECK_INSTANCES", "defaultAppCheckInstanceFactory", "provided", "defaultApp", "defaultAppCheck", "LOCALHOSTS", "isLocalhost", "window", "includes", "location", "hostname", "appCheckInstanceFactory", "fn", "zone", "injector", "platformId", "globalThis", "FIREBASE_APPCHECK_DEBUG_TOKEN", "runOutsideAngular", "APP_CHECK_INSTANCES_PROVIDER", "provide", "deps", "DEFAULT_APP_CHECK_INSTANCE_PROVIDER", "useFactory", "AppCheckModule", "full", "ɵfac", "AppCheckModule_Factory", "t", "ɵmod", "ɵɵdefineNgModule", "type", "ɵinj", "ɵɵdefineInjector", "providers", "ngDevMode", "ɵsetClassMetadata", "args", "provideAppCheck", "multi"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@angular/fire/fesm2022/angular-fire-app-check.mjs"], "sourcesContent": ["import { ɵAPP_CHECK_PROVIDER_NAME, ɵgetAllInstancesOf, ɵgetDefaultInstanceOf, ɵAppCheckInstances, VERSION, ɵAngularFireSchedulers, ɵzoneWrap } from '@angular/fire';\nexport { ɵAppCheckInstances as AppCheckInstances } from '@angular/fire';\nimport { from, timer } from 'rxjs';\nimport { concatMap, distinct } from 'rxjs/operators';\nimport { isPlatformServer } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, isDevMode, Optional, PLATFORM_ID, NgModule, NgZone, Injector, makeEnvironmentProviders } from '@angular/core';\nimport { FirebaseApp, FirebaseApps } from '@angular/fire/app';\nimport { registerVersion } from 'firebase/app';\nimport { getLimitedUseToken as getLimitedUseToken$1, getToken as getToken$1, initializeAppCheck as initializeAppCheck$1, onTokenChanged as onTokenChanged$1, setTokenAutoRefreshEnabled as setTokenAutoRefreshEnabled$1 } from 'firebase/app-check';\nexport * from 'firebase/app-check';\n\nclass AppCheck {\n    constructor(appCheck) {\n        return appCheck;\n    }\n}\nconst appCheckInstance$ = timer(0, 300).pipe(concatMap(() => from(ɵgetAllInstancesOf(ɵAPP_CHECK_PROVIDER_NAME))), distinct());\n\nconst PROVIDED_APP_CHECK_INSTANCES = new InjectionToken('angularfire2.app-check-instances');\nfunction defaultAppCheckInstanceFactory(provided, defaultApp) {\n    const defaultAppCheck = ɵgetDefaultInstanceOf(ɵAPP_CHECK_PROVIDER_NAME, provided, defaultApp);\n    return defaultAppCheck && new AppCheck(defaultAppCheck);\n}\nconst LOCALHOSTS = ['localhost', '0.0.0.0', '127.0.0.1'];\nconst isLocalhost = typeof window !== 'undefined' && LOCALHOSTS.includes(window.location.hostname);\nfunction appCheckInstanceFactory(fn) {\n    return (zone, injector, platformId) => {\n        // Node should use admin token provider, browser devmode and localhost should use debug token\n        if (!isPlatformServer(platformId) && (isDevMode() || isLocalhost)) {\n            globalThis.FIREBASE_APPCHECK_DEBUG_TOKEN ??= true;\n        }\n        const appCheck = zone.runOutsideAngular(() => fn(injector));\n        return new AppCheck(appCheck);\n    };\n}\nconst APP_CHECK_INSTANCES_PROVIDER = {\n    provide: ɵAppCheckInstances,\n    deps: [\n        [new Optional(), PROVIDED_APP_CHECK_INSTANCES],\n    ]\n};\nconst DEFAULT_APP_CHECK_INSTANCE_PROVIDER = {\n    provide: AppCheck,\n    useFactory: defaultAppCheckInstanceFactory,\n    deps: [\n        [new Optional(), PROVIDED_APP_CHECK_INSTANCES],\n        FirebaseApp,\n        PLATFORM_ID,\n    ]\n};\nclass AppCheckModule {\n    constructor() {\n        registerVersion('angularfire', VERSION.full, 'app-check');\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.6\", ngImport: i0, type: AppCheckModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.6\", ngImport: i0, type: AppCheckModule });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.6\", ngImport: i0, type: AppCheckModule, providers: [\n            DEFAULT_APP_CHECK_INSTANCE_PROVIDER,\n            APP_CHECK_INSTANCES_PROVIDER,\n        ] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.6\", ngImport: i0, type: AppCheckModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        DEFAULT_APP_CHECK_INSTANCE_PROVIDER,\n                        APP_CHECK_INSTANCES_PROVIDER,\n                    ]\n                }]\n        }], ctorParameters: () => [] });\nfunction provideAppCheck(fn, ...deps) {\n    registerVersion('angularfire', VERSION.full, 'app-check');\n    return makeEnvironmentProviders([\n        DEFAULT_APP_CHECK_INSTANCE_PROVIDER,\n        APP_CHECK_INSTANCES_PROVIDER,\n        {\n            provide: PROVIDED_APP_CHECK_INSTANCES,\n            useFactory: appCheckInstanceFactory(fn),\n            multi: true,\n            deps: [\n                NgZone,\n                Injector,\n                PLATFORM_ID,\n                ɵAngularFireSchedulers,\n                FirebaseApps,\n                ...deps,\n            ]\n        }\n    ]);\n}\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\nconst getLimitedUseToken = ɵzoneWrap(getLimitedUseToken$1, true);\nconst getToken = ɵzoneWrap(getToken$1, true);\nconst initializeAppCheck = ɵzoneWrap(initializeAppCheck$1, true);\nconst onTokenChanged = ɵzoneWrap(onTokenChanged$1, true);\nconst setTokenAutoRefreshEnabled = ɵzoneWrap(setTokenAutoRefreshEnabled$1, true);\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AppCheck, AppCheckModule, appCheckInstance$, getLimitedUseToken, getToken, initializeAppCheck, onTokenChanged, provideAppCheck, setTokenAutoRefreshEnabled };\n"], "mappings": "AAAA,SAASA,wBAAwB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,kBAAkB,EAAEC,OAAO,EAAEC,sBAAsB,EAAEC,SAAS,QAAQ,eAAe;AACnK,SAASH,kBAAkB,IAAII,iBAAiB,QAAQ,eAAe;AACvE,SAASC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AAClC,SAASC,SAAS,EAAEC,QAAQ,QAAQ,gBAAgB;AACpD,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,wBAAwB,QAAQ,eAAe;AACtI,SAASC,WAAW,EAAEC,YAAY,QAAQ,mBAAmB;AAC7D,SAASC,eAAe,QAAQ,cAAc;AAC9C,SAASC,kBAAkB,IAAIC,oBAAoB,EAAEC,QAAQ,IAAIC,UAAU,EAAEC,kBAAkB,IAAIC,oBAAoB,EAAEC,cAAc,IAAIC,gBAAgB,EAAEC,0BAA0B,IAAIC,4BAA4B,QAAQ,oBAAoB;AACnP,cAAc,oBAAoB;AAElC,MAAMC,QAAQ,CAAC;EACXC,WAAWA,CAACC,QAAQ,EAAE;IAClB,OAAOA,QAAQ;EACnB;AACJ;AACA,MAAMC,iBAAiB,GAAG7B,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC8B,IAAI,CAAC7B,SAAS,CAAC,MAAMF,IAAI,CAACP,kBAAkB,CAACD,wBAAwB,CAAC,CAAC,CAAC,EAAEW,QAAQ,CAAC,CAAC,CAAC;AAE7H,MAAM6B,4BAA4B,GAAG,IAAI1B,cAAc,CAAC,kCAAkC,CAAC;AAC3F,SAAS2B,8BAA8BA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAC1D,MAAMC,eAAe,GAAG1C,qBAAqB,CAACF,wBAAwB,EAAE0C,QAAQ,EAAEC,UAAU,CAAC;EAC7F,OAAOC,eAAe,IAAI,IAAIT,QAAQ,CAACS,eAAe,CAAC;AAC3D;AACA,MAAMC,UAAU,GAAG,CAAC,WAAW,EAAE,SAAS,EAAE,WAAW,CAAC;AACxD,MAAMC,WAAW,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIF,UAAU,CAACG,QAAQ,CAACD,MAAM,CAACE,QAAQ,CAACC,QAAQ,CAAC;AAClG,SAASC,uBAAuBA,CAACC,EAAE,EAAE;EACjC,OAAO,CAACC,IAAI,EAAEC,QAAQ,EAAEC,UAAU,KAAK;IACnC;IACA,IAAI,CAAC3C,gBAAgB,CAAC2C,UAAU,CAAC,KAAKxC,SAAS,CAAC,CAAC,IAAI+B,WAAW,CAAC,EAAE;MAC/DU,UAAU,CAACC,6BAA6B,KAAK,IAAI;IACrD;IACA,MAAMpB,QAAQ,GAAGgB,IAAI,CAACK,iBAAiB,CAAC,MAAMN,EAAE,CAACE,QAAQ,CAAC,CAAC;IAC3D,OAAO,IAAInB,QAAQ,CAACE,QAAQ,CAAC;EACjC,CAAC;AACL;AACA,MAAMsB,4BAA4B,GAAG;EACjCC,OAAO,EAAEzD,kBAAkB;EAC3B0D,IAAI,EAAE,CACF,CAAC,IAAI7C,QAAQ,CAAC,CAAC,EAAEwB,4BAA4B,CAAC;AAEtD,CAAC;AACD,MAAMsB,mCAAmC,GAAG;EACxCF,OAAO,EAAEzB,QAAQ;EACjB4B,UAAU,EAAEtB,8BAA8B;EAC1CoB,IAAI,EAAE,CACF,CAAC,IAAI7C,QAAQ,CAAC,CAAC,EAAEwB,4BAA4B,CAAC,EAC9ClB,WAAW,EACXL,WAAW;AAEnB,CAAC;AACD,MAAM+C,cAAc,CAAC;EACjB5B,WAAWA,CAAA,EAAG;IACVZ,eAAe,CAAC,aAAa,EAAEpB,OAAO,CAAC6D,IAAI,EAAE,WAAW,CAAC;EAC7D;EACA,OAAOC,IAAI,YAAAC,uBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFJ,cAAc;EAAA;EACjH,OAAOK,IAAI,kBAD8ExD,EAAE,CAAAyD,gBAAA;IAAAC,IAAA,EACSP;EAAc;EAClH,OAAOQ,IAAI,kBAF8E3D,EAAE,CAAA4D,gBAAA;IAAAC,SAAA,EAEoC,CACvHZ,mCAAmC,EACnCH,4BAA4B;EAC/B;AACT;AACA;EAAA,QAAAgB,SAAA,oBAAAA,SAAA,KAP6F9D,EAAE,CAAA+D,iBAAA,CAOJZ,cAAc,EAAc,CAAC;IAC5GO,IAAI,EAAErD,QAAQ;IACd2D,IAAI,EAAE,CAAC;MACCH,SAAS,EAAE,CACPZ,mCAAmC,EACnCH,4BAA4B;IAEpC,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;AACpC,SAASmB,eAAeA,CAAC1B,EAAE,EAAE,GAAGS,IAAI,EAAE;EAClCrC,eAAe,CAAC,aAAa,EAAEpB,OAAO,CAAC6D,IAAI,EAAE,WAAW,CAAC;EACzD,OAAO5C,wBAAwB,CAAC,CAC5ByC,mCAAmC,EACnCH,4BAA4B,EAC5B;IACIC,OAAO,EAAEpB,4BAA4B;IACrCuB,UAAU,EAAEZ,uBAAuB,CAACC,EAAE,CAAC;IACvC2B,KAAK,EAAE,IAAI;IACXlB,IAAI,EAAE,CACF1C,MAAM,EACNC,QAAQ,EACRH,WAAW,EACXZ,sBAAsB,EACtBkB,YAAY,EACZ,GAAGsC,IAAI;EAEf,CAAC,CACJ,CAAC;AACN;;AAEA;AACA,MAAMpC,kBAAkB,GAAGnB,SAAS,CAACoB,oBAAoB,EAAE,IAAI,CAAC;AAChE,MAAMC,QAAQ,GAAGrB,SAAS,CAACsB,UAAU,EAAE,IAAI,CAAC;AAC5C,MAAMC,kBAAkB,GAAGvB,SAAS,CAACwB,oBAAoB,EAAE,IAAI,CAAC;AAChE,MAAMC,cAAc,GAAGzB,SAAS,CAAC0B,gBAAgB,EAAE,IAAI,CAAC;AACxD,MAAMC,0BAA0B,GAAG3B,SAAS,CAAC4B,4BAA4B,EAAE,IAAI,CAAC;;AAEhF;AACA;AACA;;AAEA,SAASC,QAAQ,EAAE6B,cAAc,EAAE1B,iBAAiB,EAAEb,kBAAkB,EAAEE,QAAQ,EAAEE,kBAAkB,EAAEE,cAAc,EAAE+C,eAAe,EAAE7C,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}