{"ast": null, "code": "import { inject, signal } from '@angular/core';\nimport { SubSink } from 'subsink';\nimport { forkJoin } from 'rxjs';\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\nimport { PackageService } from 'src/app/core/services/package.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/certificate.service\";\nimport * as i2 from \"src/app/core/services/auth.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/tabview\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"primeng/carousel\";\nimport * as i8 from \"primeng/dialog\";\nimport * as i9 from \"../../shared/share-button/share-button.component\";\nconst _c0 = () => ({\n  width: \"50vw\"\n});\nconst _c1 = () => ({\n  \"960px\": \"75vw\"\n});\nfunction CertificateComponent_p_carousel_7_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26);\n    i0.ɵɵelement(2, \"img\", 27)(3, \"img\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 29);\n    i0.ɵɵtext(5);\n    i0.ɵɵelement(6, \"br\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"titlecase\");\n    i0.ɵɵelement(9, \"br\");\n    i0.ɵɵelementStart(10, \"div\", 30)(11, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function CertificateComponent_p_carousel_7_ng_template_1_Template_button_click_11_listener() {\n      const certificate_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.viewPdf(certificate_r3.level, certificate_r3.language, certificate_r3.type));\n    });\n    i0.ɵɵelement(12, \"img\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"app-share-button\", 33);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const certificate_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", certificate_r3.type == \"completion\" ? \"/assets/images/dashboard/completion.png\" : \"/assets/images/dashboard/attendance.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", certificate_r3.language, \" \", certificate_r3.level, \" Certificate \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" of \", i0.ɵɵpipeBind1(8, 6, certificate_r3.type), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"topic\", \"MyLingoTrip Certificate\")(\"componentId\", \"Certificate\" + certificate_r3.id);\n  }\n}\nfunction CertificateComponent_p_carousel_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-carousel\", 23);\n    i0.ɵɵtemplate(1, CertificateComponent_p_carousel_7_ng_template_1_Template, 14, 8, \"ng-template\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r3.allCertificates().slice(-3))(\"numVisible\", 1)(\"numScroll\", 1)(\"circular\", false);\n  }\n}\nfunction CertificateComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"img\", 35);\n    i0.ɵɵelementStart(2, \"div\", 36);\n    i0.ɵɵtext(3, \"You haven\\u2019t received\");\n    i0.ɵɵelement(4, \"br\");\n    i0.ɵɵtext(5, \" any certifications yet\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CertificateComponent_ng_container_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 37)(2, \"div\", 14);\n    i0.ɵɵlistener(\"click\", function CertificateComponent_ng_container_18_Template_div_click_2_listener() {\n      const language_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.selectLanguage(language_r6));\n    });\n    i0.ɵɵelement(3, \"img\", 38);\n    i0.ɵɵelementStart(4, \"span\", 39);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const language_r6 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"src\", \"/assets/icons/languages/\", language_r6, \".svg\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(language_r6);\n  }\n}\nfunction CertificateComponent_p_table_22_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 43)(1, \"th\");\n    i0.ɵɵtext(2, \"Level\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Hours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"Language\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CertificateComponent_p_table_22_ng_template_2_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"img\", 51);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction CertificateComponent_p_table_22_ng_template_2_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 32);\n  }\n}\nfunction CertificateComponent_p_table_22_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 44)(1, \"td\", 45)(2, \"div\");\n    i0.ɵɵelement(3, \"img\", 46);\n    i0.ɵɵelementStart(4, \"span\", 47);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"td\", 48);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 48);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 48);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 48)(14, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function CertificateComponent_p_table_22_ng_template_2_Template_button_click_14_listener() {\n      const certificate_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.viewPdf(certificate_r8.level, certificate_r8.language, certificate_r8.type, ctx_r3.i, certificate_r8.hours));\n    });\n    i0.ɵɵtemplate(15, CertificateComponent_p_table_22_ng_template_2_ng_container_15_Template, 2, 0, \"ng-container\", 49)(16, CertificateComponent_p_table_22_ng_template_2_ng_template_16_Template, 1, 0, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"app-share-button\", 50);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const certificate_r8 = ctx.$implicit;\n    const downloadButton_r9 = i0.ɵɵreference(17);\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(certificate_r8.level);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", certificate_r8.hours, \" hours\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(certificate_r8.language);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(12, 9, certificate_r8.type));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isDownloadLoading[ctx_r3.i])(\"ngIfElse\", downloadButton_r9);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"topic\", \"MyLingoTrip Certificate\")(\"link\", ctx_r3.shareUrl + certificate_r8.urlParam)(\"componentId\", \"certificate\" + certificate_r8.id);\n  }\n}\nfunction CertificateComponent_p_table_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-table\", 40);\n    i0.ɵɵtemplate(1, CertificateComponent_p_table_22_ng_template_1_Template, 11, 0, \"ng-template\", 41)(2, CertificateComponent_p_table_22_ng_template_2_Template, 19, 11, \"ng-template\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r3.filteredCertificates());\n  }\n}\nfunction CertificateComponent_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"img\", 52);\n    i0.ɵɵelementStart(2, \"div\", 53);\n    i0.ɵɵtext(3, \"No Certifications to show\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CertificateComponent_p_table_26_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 43)(1, \"th\");\n    i0.ɵɵtext(2, \"Level\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Hours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"Language\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CertificateComponent_p_table_26_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 44)(1, \"td\", 45)(2, \"div\");\n    i0.ɵɵelement(3, \"img\", 46);\n    i0.ɵɵelementStart(4, \"span\", 47);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"td\", 48);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 48);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 48);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 48)(13, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function CertificateComponent_p_table_26_ng_template_2_Template_button_click_13_listener() {\n      const certificate_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.view(certificate_r11.level, certificate_r11.language));\n    });\n    i0.ɵɵelement(14, \"img\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const certificate_r11 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(certificate_r11.level);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", certificate_r11.hours, \" hours\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(certificate_r11.language);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(certificate_r11.type);\n  }\n}\nfunction CertificateComponent_p_table_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-table\", 40);\n    i0.ɵɵtemplate(1, CertificateComponent_p_table_26_ng_template_1_Template, 11, 0, \"ng-template\", 41)(2, CertificateComponent_p_table_26_ng_template_2_Template, 15, 4, \"ng-template\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r3.filteredCertificates());\n  }\n}\nfunction CertificateComponent_ng_template_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"img\", 52);\n    i0.ɵɵelementStart(2, \"div\", 53);\n    i0.ɵɵtext(3, \"No Certifications to show\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let CertificateComponent = /*#__PURE__*/(() => {\n  class CertificateComponent {\n    constructor(certificaterService, authService, datePipe) {\n      this.certificaterService = certificaterService;\n      this.authService = authService;\n      this.datePipe = datePipe;\n      this.classroomService = inject(ClassroomService);\n      this.packageService = inject(PackageService);\n      this.userCertificates = [];\n      this.certificates = [];\n      this.completionCertificates = [];\n      this.attendanceCertificates = [];\n      this.languages = [];\n      this.shareButtons = [];\n      this.visible = false;\n      this.base64data = \"\";\n      this.pdfSrc = \"\";\n      this.shareUrl = \"https://www.mylingotrip.com/certificates?id=\";\n      this.isDownloadLoading = [];\n      this.user = {};\n      this.allCertificates = signal([]);\n      this.filteredCertificates = signal([]);\n      this.packages = [];\n      this.classrooms = [];\n      this.subs = new SubSink();\n    }\n    ngOnInit() {\n      this.responsiveOptions = [{\n        breakpoint: '1220px',\n        numVisible: 4,\n        numScroll: 2\n      }, {\n        breakpoint: '1100px',\n        numVisible: 1,\n        numScroll: 1\n      }];\n      this.getCertifications();\n      this.user = this.authService.getLoggedInUser();\n      this.findClassrooms();\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n    }\n    findClassrooms() {\n      forkJoin([this.packageService.getAllUserPackages(this.authService.getUserId()), this.classroomService.getLMSUserClassrooms(this.user.id)]).subscribe({\n        next: ([packagesResponse, classroomsResponse]) => {\n          this.packages = packagesResponse;\n          this.classrooms = classroomsResponse;\n          if (classroomsResponse) {\n            const certificates = this.generateCertificatesForPackages();\n            this.allCertificates.set(certificates);\n            this.filteredCertificates.set(certificates);\n          }\n        },\n        error: err => console.error('Error fetching data:', err)\n      });\n    }\n    generateCertificatesForPackages() {\n      const certificates = [];\n      // 1. Collect ALL completed packages across ALL classrooms\n      const allCompletedPackages = this.packages.filter(pkg => pkg.state.toLowerCase() === 'completed');\n      // 2. Group ALL completed packages by language and level\n      const groupedCompletedPackages = this.groupPackagesByLanguageAndLevel(allCompletedPackages);\n      console.log(allCompletedPackages);\n      // 3. Generate Completion Certificates\n      for (const key in groupedCompletedPackages) {\n        if (groupedCompletedPackages.hasOwnProperty(key)) {\n          const packagesInGroup = groupedCompletedPackages[key];\n          const [language, level] = key.split('-');\n          const totalHoursInGroup = this.calculateTotalHours(packagesInGroup);\n          const certificate = {\n            id: `completion-${language}-${level}`,\n            level: level,\n            language: language,\n            hours: totalHoursInGroup.toString(),\n            type: 'completion'\n          };\n          certificates.push(certificate);\n          this.completionCertificates.push(certificate);\n        }\n      }\n      return certificates;\n    }\n    groupPackagesByLanguageAndLevel(packages) {\n      const groupedPackages = {};\n      packages.forEach(pkg => {\n        // Normalize language and level\n        const language = (pkg.packLanguage || 'Unknown Language').trim().toUpperCase();\n        const level = (pkg.currentPackageLevel || 'Unknown Level').trim().toUpperCase();\n        const key = `${language}-${level}`; // Create unique key for grouping\n        if (!groupedPackages[key]) {\n          groupedPackages[key] = [];\n        }\n        groupedPackages[key].push(pkg);\n      });\n      return groupedPackages;\n    }\n    // Create the grouping key\n    getLanguageFromClassroom(classroomId) {\n      const classroom = this.classrooms.find(c => +c.id === +classroomId);\n      return classroom ? classroom.language : \"\";\n    }\n    getLevelFromClassroom(classroomId) {\n      const classroom = this.classrooms.find(c => +c.id === +classroomId);\n      return classroom ? classroom.activeLevel : \"\";\n    }\n    calculateOngoingHoursSpent(packages) {\n      let totalSpent = 0;\n      packages.forEach(pkg => {\n        if (pkg.state.toLowerCase() === 'ongoing') {\n          totalSpent += pkg.totalHours - pkg.hoursLeft; // Calculate hours spent\n        }\n      });\n      return totalSpent;\n    }\n    calculateAttendanceHours(classroom) {\n      // Calculate the total duration of requested and arranged lessons\n      const totalDuration = classroom.lessons.reduce((duration, lesson) => {\n        if (lesson.status.toLowerCase() === 'arranged' || lesson.status.toLowerCase() === 'requested') {\n          return duration + lesson.duration;\n        }\n        return duration;\n      }, 0);\n      // Get the accumulated hours left in the classroom packages\n      const accumulatedHoursLeft = this.getAccumulatedHoursLeft(classroom.packages);\n      // Get the total hours from completed packages\n      const totalCompletedHours = this.calculateTotalHours(classroom.packages.filter(pkg => pkg.state.toLowerCase() === 'completed'));\n      // Calculate the total attendance hours\n      const totalAttendanceHours = accumulatedHoursLeft + totalDuration + totalCompletedHours;\n      return totalAttendanceHours;\n    }\n    // Helper method to get accumulated hours left from packages\n    getAccumulatedHoursLeft(packages) {\n      return packages.reduce((total, pkg) => {\n        if (pkg.state.toLowerCase() === 'ongoing') {\n          return total + pkg.hoursLeft; // Add hours left from ongoing packages\n        }\n        return total;\n      }, 0);\n    }\n    calculateTotalHours(packages) {\n      let totalHours = 0;\n      packages.forEach(pkg => {\n        if (pkg.state === 'Completed') {\n          totalHours += pkg.totalHours; // Add completed package hours\n        } else if (pkg.state === 'Ongoing') {\n          totalHours += pkg.hoursLeft; // Add ongoing package hours\n        }\n      });\n      return totalHours;\n    }\n    viewPdf(level, language, type, index, hours) {\n      this.isDownloadLoading[index] = true;\n      let hoursInput = hours?.toString(); // Example input as a string\n      let hoursF = parseFloat(hoursInput); // Parse the string to a number\n      let formattedHours = hoursF.toFixed(0); // Format to 2 decimal places\n      this.subs.sink = this.certificaterService.generateCertificationPdf(level, language, type, formattedHours).subscribe(pdfResponse => {\n        this.isDownloadLoading[index] = false;\n        const byteCharacters = atob(pdfResponse);\n        const byteNumbers = new Array(byteCharacters.length);\n        for (let i = 0; i < byteCharacters.length; i++) {\n          byteNumbers[i] = byteCharacters.charCodeAt(i);\n        }\n        const byteArray = new Uint8Array(byteNumbers);\n        const blob = new Blob([byteArray], {\n          type: 'application/pdf'\n        });\n        var url = window.URL.createObjectURL(blob);\n        let anchor = document.createElement(\"a\");\n        anchor.href = url;\n        anchor.target = \"_blank\";\n        anchor.click();\n      }, error => {\n        console.error('Error generating PDF:', error);\n      });\n    }\n    ngAfterViewInit() {\n      this.setupShareButton();\n    }\n    setupShareButton() {\n      this.shareButtons = Array.from(document.querySelectorAll(\"button.shareButton\"));\n    }\n    toggleShareButtons() {\n      for (let i = 0; i < this.shareButtons.length; i++) {\n        this.shareButtons[i].classList.toggle(\"open\");\n      }\n    }\n    showDialog() {\n      this.visible = true;\n    }\n    selectLanguage(lang) {\n      this.certificates = this.userCertificates;\n      this.attendanceCertificates = this.certificates.filter(certificate => certificate.type === \"attendance\");\n      this.filteredCertificates.set(this.allCertificates().filter(certificate => certificate.type === \"completion\"));\n      if (lang !== \"all\") {\n        this.filteredCertificates.set(this.allCertificates().filter(certificate => certificate.language.toLowerCase() === lang.toLowerCase() && certificate.type === \"completion\"));\n      }\n    }\n    getCertifications() {\n      this.subs.sink = this.certificaterService.getListOfCertifications().subscribe(response => {\n        this.userCertificates = response;\n        this.userCertificates.forEach(certificate => {\n          certificate.urlParam = btoa(`type=${certificate.type}&name=${this.user.firstName}_${this.user.lastName}&language=${certificate.language}&level=${certificate.level}&hours=${certificate.hours}&date=${this.convertDate(certificate.datetime)}`);\n        });\n        this.languages = this.userCertificates.map(certificate => certificate.language) // Extract the language property from each certificate\n        .filter((language, index, array) => array.indexOf(language) === index); // Filter out duplicate languages\n        this.certificates = this.userCertificates;\n        // this.completionCertificates = this.userCertificates.filter((certificate) => certificate.type === \"completion\");\n        this.attendanceCertificates = this.userCertificates.filter(certificate => certificate.type === \"attendance\");\n      });\n    }\n    b64toBlob(b64Data, contentType) {\n      contentType = contentType || '';\n      let sliceSize = 512;\n      var byteCharacters = atob(b64Data);\n      var byteArrays = [];\n      for (var offset = 0; offset < byteCharacters.length; offset += sliceSize) {\n        var slice = byteCharacters.slice(offset, offset + sliceSize);\n        var byteNumbers = new Array(slice.length);\n        for (var i = 0; i < slice.length; i++) {\n          byteNumbers[i] = slice.charCodeAt(i);\n        }\n        var byteArray = new Uint8Array(byteNumbers);\n        byteArrays.push(byteArray);\n      }\n      var blob = new Blob(byteArrays, {\n        type: contentType\n      });\n      return blob;\n    }\n    getImage() {\n      var blob = this.b64toBlob(this.base64data, \"application/pdf\");\n      let a = document.createElement(\"a\");\n      document.body.appendChild(a);\n      var url = window.URL.createObjectURL(blob);\n      a.href = url;\n      a.download = String(\"MyLingoTrip_Certificate.pdf\");\n      a.click();\n      window.URL.revokeObjectURL(url);\n      a.remove();\n    }\n    convertDate(dateString) {\n      const date = new Date(dateString);\n      let formattedDate = this.datePipe.transform(date, 'dd MMMM yyyy');\n      return formattedDate;\n    }\n    static #_ = this.ɵfac = function CertificateComponent_Factory(t) {\n      return new (t || CertificateComponent)(i0.ɵɵdirectiveInject(i1.CertificateService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.DatePipe));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CertificateComponent,\n      selectors: [[\"app-certificate\"]],\n      decls: 32,\n      vars: 15,\n      consts: [[\"noLatest\", \"\"], [\"noCertificates\", \"\"], [\"noCompletionCertificates\", \"\"], [\"downloadButton\", \"\"], [1, \"certificates\"], [1, \"text-0\", \"font-2xl\", \"font-bold\"], [1, \"block\", \"my-7\"], [1, \"card\", \"block-gradient\", \"block\", \"mt-5\"], [1, \"text-primary\", \"font-lg\", \"font-semibold\", \"pl-4\"], [3, \"value\", \"numVisible\", \"numScroll\", \"circular\", 4, \"ngIf\", \"ngIfElse\"], [1, \"flex\"], [1, \"col-fixed\"], [1, \"flex\", \"flex-wrap\", \"gap-2\", \"align-items-center\"], [\"pbutton\", \"\", \"type\", \"button\", 1, \"p-element\", \"p-button-outlined\", \"w-7rem\", \"p-button-rounded\", \"mb-2\", \"button-outlined\", \"p-button-sm\", \"p-button\", \"p-component\", \"py-1\", \"pl-2\"], [1, \"flex\", \"align-items-center\", \"gap-1\", 3, \"click\"], [\"height\", \"18\", \"src\", \"/assets/icons/flag.svg\", 1, \"ng-star-inserted\"], [1, \"font-sm\", \"mx-4\"], [4, \"ngFor\", \"ngForOf\"], [1, \"card\", \"block-gradient\", \"relative\"], [\"header\", \"All\"], [3, \"value\", 4, \"ngIf\", \"ngIfElse\"], [\"header\", \"of Completion\"], [\"header\", \"Header\", 3, \"visibleChange\", \"visible\", \"breakpoints\", \"draggable\", \"resizable\"], [3, \"value\", \"numVisible\", \"numScroll\", \"circular\"], [\"pTemplate\", \"item\"], [1, \"m-2\", \"px-3\", \"flex\", \"flex-column\", \"sm:flex-row\", \"align-items-center\", \"justify-content-center\"], [1, \"relative\", \"w-full\", \"sm:w-6\", \"sm:mb-3\"], [1, \"w-full\", 3, \"src\"], [\"src\", \"/assets/images/dashboard/new.svg\", \"alt\", \"\", 1, \"new-tag\", \"hidden\", \"sm:block\"], [1, \"text-primary\", \"font-bold\", \"text-sm\", \"sm:text-xl\"], [1, \"hidden\", \"mt-4\", \"pill-bg\", \"pt-1\"], [1, \"mr-2\", \"shareButton\", 3, \"click\"], [\"src\", \"/assets/icons/view-file.svg\"], [3, \"topic\", \"componentId\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"py-5\"], [\"src\", \"/assets/icons/certificate-gradient.svg\", 1, \"w-3rem\", \"sm:w-5rem\"], [1, \"text-primary\", \"font-bold\", \"text-base\", \"sm:text-xl\", \"ml-3\"], [\"pbutton\", \"\", \"type\", \"button\", 1, \"p-element\", \"p-button-outlined\", \"w-7rem\", \"p-button-rounded\", \"mb-2\", \"button-outlined\", \"p-button-sm\", \"p-button\", \"p-component\", \"py-1\", \"pl-1\"], [\"height\", \"20\", 1, \"ng-star-inserted\", 3, \"src\"], [1, \"font-sm\", \"mx-3\"], [3, \"value\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [1, \"font-sm\", \"text-blue\"], [1, \"font-sm\"], [1, \"flex\", \"py-2\"], [\"src\", \"/assets/icons/certificate-gradient.svg\", 1, \"icon-cer\"], [2, \"vertical-align\", \"super\"], [1, \"py-2\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"hidden\", 3, \"topic\", \"link\", \"componentId\"], [\"src\", \"/assets/images/load.gif\", \"width\", \"30\"], [\"src\", \"/assets/icons/certificate-gradient.svg\", 1, \"\"], [1, \"text-primary\", \"font-bold\", \"font-base\", \"ml-3\"]],\n      template: function CertificateComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5);\n          i0.ɵɵtext(2, \"Certificates\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(3, \"div\", 6);\n          i0.ɵɵelementStart(4, \"div\", 7)(5, \"div\", 8);\n          i0.ɵɵtext(6, \"Latest\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, CertificateComponent_p_carousel_7_Template, 2, 4, \"p-carousel\", 9)(8, CertificateComponent_ng_template_8_Template, 6, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 10);\n          i0.ɵɵelement(11, \"div\", 11);\n          i0.ɵɵelementStart(12, \"div\", 12)(13, \"button\", 13)(14, \"div\", 14);\n          i0.ɵɵlistener(\"click\", function CertificateComponent_Template_div_click_14_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.selectLanguage(\"all\"));\n          });\n          i0.ɵɵelement(15, \"img\", 15);\n          i0.ɵɵelementStart(16, \"span\", 16);\n          i0.ɵɵtext(17, \"All\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(18, CertificateComponent_ng_container_18_Template, 6, 3, \"ng-container\", 17);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"div\", 18)(20, \"p-tabView\")(21, \"p-tabPanel\", 19);\n          i0.ɵɵtemplate(22, CertificateComponent_p_table_22_Template, 3, 1, \"p-table\", 20)(23, CertificateComponent_ng_template_23_Template, 4, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"p-tabPanel\", 21);\n          i0.ɵɵtemplate(26, CertificateComponent_p_table_26_Template, 3, 1, \"p-table\", 20)(27, CertificateComponent_ng_template_27_Template, 4, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(29, \"p-dialog\", 22);\n          i0.ɵɵtwoWayListener(\"visibleChange\", function CertificateComponent_Template_p_dialog_visibleChange_29_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.visible, $event) || (ctx.visible = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵelementStart(30, \"p\");\n          i0.ɵɵtext(31, \" Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. \");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          const noLatest_r12 = i0.ɵɵreference(9);\n          const noCertificates_r13 = i0.ɵɵreference(24);\n          const noCompletionCertificates_r14 = i0.ɵɵreference(28);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.allCertificates() && ctx.allCertificates().length > 0)(\"ngIfElse\", noLatest_r12);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.languages);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.filteredCertificates().length > 0)(\"ngIfElse\", noCertificates_r13);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.filteredCertificates().length > 0)(\"ngIfElse\", noCompletionCertificates_r14);\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(13, _c0));\n          i0.ɵɵtwoWayProperty(\"visible\", ctx.visible);\n          i0.ɵɵproperty(\"breakpoints\", i0.ɵɵpureFunction0(14, _c1))(\"draggable\", false)(\"resizable\", false);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.TabView, i4.TabPanel, i5.PrimeTemplate, i6.Table, i7.Carousel, i8.Dialog, i9.ShareButtonComponent, i3.TitleCasePipe],\n      styles: [\".certificates[_ngcontent-%COMP%]{background-color:#fff;background-image:url(certificate-bg.3a3623921ea7d85e.svg);background-repeat:no-repeat;border-radius:12px;padding:40px}.certificates[_ngcontent-%COMP%]   .certificate[_ngcontent-%COMP%]{font-size:24px;padding:20px 0;margin-top:40px;font-size:18px}.certificates[_ngcontent-%COMP%]   .certificate[_ngcontent-%COMP%]   .language[_ngcontent-%COMP%]{font-size:22px}.certificates[_ngcontent-%COMP%]   .certificate[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{width:100%;padding:30px 0;font-weight:700}.certificates[_ngcontent-%COMP%]   .certificate[_ngcontent-%COMP%]   .levels[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;justify-content:space-between}.certificates[_ngcontent-%COMP%]   .certificate[_ngcontent-%COMP%]   .levels[_ngcontent-%COMP%]   .level[_ngcontent-%COMP%]{box-sizing:border-box;width:47%;display:flex;justify-content:space-evenly;padding-bottom:20px}.certificates[_ngcontent-%COMP%]   .certificate[_ngcontent-%COMP%]:first-child{margin-top:0}.style-three[_ngcontent-%COMP%]{border:0;border-bottom:3px dashed #ccc;margin:20px 0}.icon-cer[_ngcontent-%COMP%]{width:1.3rem;margin-right:.5rem}.text-blue[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{color:#3345a7}[_nghost-%COMP%]     .btn-modify{padding:.25rem 1rem;border-radius:5px;background:linear-gradient(#8497ff,#122171);color:#fff}[_nghost-%COMP%]     .card{border:0}[_nghost-%COMP%]     .p-tabview .p-tabview-nav{background:transparent}[_nghost-%COMP%]     .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link{background:transparent;border-color:#0000001f;color:#3f51b5}[_nghost-%COMP%]     .p-tabview-panels{background:transparent}[_nghost-%COMP%]     .p-datatable .p-datatable-tbody>tr{background-color:transparent}[_nghost-%COMP%]     .p-datatable .p-datatable-tbody>tr>td{line-height:26px}[_nghost-%COMP%]     .p-datatable .p-datatable-thead>tr>th{background-color:transparent}[_nghost-%COMP%]     .p-tabview .p-tabview-nav li .p-tabview-nav-link{background:transparent}[_nghost-%COMP%]     .p-tabview .p-tabview-nav li .p-tabview-nav-link{color:#3f51b5}[_nghost-%COMP%]     .p-tabview .p-tabview-nav{border:0}[_nghost-%COMP%]     .p-tabview .p-tabview-nav .p-tabview-ink-bar{background:transparent}[_nghost-%COMP%]     .p-tabview .p-tabview-nav li .p-tabview-nav-link{border:0}[_nghost-%COMP%]     .p-carousel-indicators{display:none!important}[_nghost-%COMP%]     .pi-chevron-right:before, [_nghost-%COMP%]     .pi-chevron-left:before{color:#84abff!important}[_nghost-%COMP%]     .p-carousel .p-carousel-content .p-carousel-prev, [_nghost-%COMP%]     .p-carousel .p-carousel-content .p-carousel-next{margin-top:.2rem}[_nghost-%COMP%]     .p-highlight{font-family:Proxima Nova Bold!important}[_nghost-%COMP%]     ul.p-tabview-nav li:nth-of-type(2) .p-tabview-nav-link{border-right:1px solid #3F51B5;border-left:1px solid #3F51B5}[_nghost-%COMP%]     .p-tabview .p-tabview-nav li .p-tabview-nav-link{padding:.5rem 1.5rem}[_nghost-%COMP%]     .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link{border-color:#3f51b5}.content[_ngcontent-%COMP%]{display:contents}.challenge[_ngcontent-%COMP%]{width:541px;height:22px;position:absolute;transform:rotate(-90deg);top:569.5px;left:755px;transform-origin:0 0}.challenge[_ngcontent-%COMP%]   .number[_ngcontent-%COMP%]{float:right}.challenge[_ngcontent-%COMP%]   .daily[_ngcontent-%COMP%]{float:left}.content[_ngcontent-%COMP%]   .shareButton.main[_ngcontent-%COMP%]   .share[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   .shareButton.main[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   .shareButton.main[_ngcontent-%COMP%]   .check[_ngcontent-%COMP%]{position:absolute;top:0;left:0;transition:all .15s}.content[_ngcontent-%COMP%]   .shareButton.main[_ngcontent-%COMP%]   .share[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   .shareButton.main.open[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   .shareButton.main.sent[_ngcontent-%COMP%]   .check[_ngcontent-%COMP%]{transform:rotate(0) scale(1);opacity:1}.content[_ngcontent-%COMP%]   .shareButton.main[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   .shareButton.main.open[_ngcontent-%COMP%]   .share[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   .shareButton.main[_ngcontent-%COMP%]   .check[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   .shareButton.main.sent[_ngcontent-%COMP%]   .share[_ngcontent-%COMP%]{opacity:0;transform:rotate(90deg) scale(0)}.shareButton[_ngcontent-%COMP%], .shareButton.open[_ngcontent-%COMP%]{border:none;border-radius:50%;background:transparent;overflow:hidden;outline:none;width:24px;height:24px;box-sizing:content-box;transition:all .2s;position:relative;opacity:1;transform:scale(1);box-shadow:0 0 #0000;cursor:pointer}.shareButton[_ngcontent-%COMP%]:hover, .shareButton.open[_ngcontent-%COMP%]:hover{transform:scale(1.1) translateY(-3px)}.shareButton[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%], .shareButton.open[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{display:block;fill:#3f51b5;width:24px;height:24px;opacity:1;transition:all .15s;transform:scale(1)}.fb[_ngcontent-%COMP%], .shareButton.open.ig[_ngcontent-%COMP%]{transition-delay:.1s}.tw[_ngcontent-%COMP%], .shareButton.open.tw[_ngcontent-%COMP%]{transition-delay:50ms}.ig[_ngcontent-%COMP%], .shareButton.open.fb[_ngcontent-%COMP%]{transition-delay:0ms}.fb[_ngcontent-%COMP%], .tw[_ngcontent-%COMP%], .ig[_ngcontent-%COMP%]{width:0;height:0;overflow:hidden;padding:0;margin:0;opacity:0;transform:scale(0)}.fb[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%], .tw[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%], .ig[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{width:0;height:0;opacity:0;transform:scale(0)}.shareButton.main.open[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{opacity:0;transform:rotate(90deg) scale(0)}.btn-modify[_ngcontent-%COMP%]{position:absolute;top:0;right:1rem}.text-primary[_ngcontent-%COMP%]{color:#3f51b5}.pill-bg[_ngcontent-%COMP%]{border-radius:20px;background:#f4f7ff;width:-moz-fit-content;width:fit-content}.new-tag[_ngcontent-%COMP%]{position:absolute;right:1.5rem;top:1rem}\"]\n    });\n  }\n  return CertificateComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}