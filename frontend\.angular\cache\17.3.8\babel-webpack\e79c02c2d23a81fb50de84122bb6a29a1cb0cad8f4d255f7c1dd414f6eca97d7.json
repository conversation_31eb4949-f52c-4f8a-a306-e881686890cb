{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let PackageContentInfoComponent = /*#__PURE__*/(() => {\n  class PackageContentInfoComponent {\n    constructor() {\n      this.type = '';\n      this.duration = '-';\n      this.from = '-';\n      this.to = '-';\n      this.date = '-';\n    }\n    ngOnInit() {}\n    static #_ = this.ɵfac = function PackageContentInfoComponent_Factory(t) {\n      return new (t || PackageContentInfoComponent)();\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PackageContentInfoComponent,\n      selectors: [[\"app-package-content-info\"]],\n      inputs: {\n        type: \"type\",\n        duration: \"duration\",\n        from: \"from\",\n        to: \"to\",\n        date: \"date\"\n      },\n      decls: 11,\n      vars: 5,\n      consts: [[1, \"package-content-info\", \"text-primary\"], [1, \"col-20-per\", \"col-25-per-left\", \"font-bold\", \"text-primary\", \"font-xs\"], [1, \"col-20-per\", \"text-primary\", \"font-xs\"], [1, \"col-20-per\", \"col-25-per-right\", \"text-primary\", \"font-xs\"]],\n      template: function PackageContentInfoComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 2);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 2);\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 2);\n          i0.ɵɵtext(8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 3);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.type, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.duration, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.from, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.to, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.date, \" \");\n        }\n      },\n      styles: [\".package-content-info[_ngcontent-%COMP%]{width:100%;display:flex;justify-content:space-evenly;border-bottom:.5px solid var(--gray-border-2);padding:10px 0}.package-content-info[_ngcontent-%COMP%]:last-child{border-bottom:0px solid var(--gray-border-2)}\"]\n    });\n  }\n  return PackageContentInfoComponent;\n})();", "map": {"version": 3, "names": ["PackageContentInfoComponent", "constructor", "type", "duration", "from", "to", "date", "ngOnInit", "_", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "PackageContentInfoComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\package-content-info\\package-content-info.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\package-content-info\\package-content-info.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-package-content-info',\r\n  templateUrl: './package-content-info.component.html',\r\n  styleUrls: ['./package-content-info.component.scss']\r\n})\r\nexport class PackageContentInfoComponent implements OnInit {\r\n  @Input() type: string = '';\r\n  @Input() duration: string = '-';\r\n  @Input() from: string = '-';\r\n  @Input() to: string = '-';\r\n  @Input() date: string = '-';\r\n  constructor() { }\r\n\r\n  ngOnInit(): void {\r\n  }\r\n\r\n}\r\n", "<div  class=\"package-content-info text-primary\">\r\n    <div class=\"col-20-per col-25-per-left font-bold text-primary font-xs\">\r\n        {{ type }}\r\n    </div>\r\n    <div class=\"col-20-per text-primary font-xs\">\r\n        {{ duration }}\r\n    </div>\r\n    <div class=\"col-20-per text-primary font-xs\">\r\n        {{ from }}\r\n    </div>\r\n    <div class=\"col-20-per text-primary font-xs\">\r\n        {{ to }}\r\n    </div>\r\n    <div class=\"col-20-per col-25-per-right text-primary font-xs\">\r\n        {{ date }}\r\n    </div>\r\n</div>"], "mappings": ";AAOA,WAAaA,2BAA2B;EAAlC,MAAOA,2BAA2B;IAMtCC,YAAA;MALS,KAAAC,IAAI,GAAW,EAAE;MACjB,KAAAC,QAAQ,GAAW,GAAG;MACtB,KAAAC,IAAI,GAAW,GAAG;MAClB,KAAAC,EAAE,GAAW,GAAG;MAChB,KAAAC,IAAI,GAAW,GAAG;IACX;IAEhBC,QAAQA,CAAA,GACR;IAAC,QAAAC,CAAA,G;uBATUR,2BAA2B;IAAA;IAAA,QAAAS,EAAA,G;YAA3BT,2BAA2B;MAAAU,SAAA;MAAAC,MAAA;QAAAT,IAAA;QAAAC,QAAA;QAAAC,IAAA;QAAAC,EAAA;QAAAC,IAAA;MAAA;MAAAM,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCNpCE,EADJ,CAAAC,cAAA,aAAgD,aAC2B;UACnED,EAAA,CAAAE,MAAA,GACJ;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,aAA6C;UACzCD,EAAA,CAAAE,MAAA,GACJ;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,aAA6C;UACzCD,EAAA,CAAAE,MAAA,GACJ;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,aAA6C;UACzCD,EAAA,CAAAE,MAAA,GACJ;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,aAA8D;UAC1DD,EAAA,CAAAE,MAAA,IACJ;UACJF,EADI,CAAAG,YAAA,EAAM,EACJ;;;UAdEH,EAAA,CAAAI,SAAA,GACJ;UADIJ,EAAA,CAAAK,kBAAA,MAAAN,GAAA,CAAAhB,IAAA,MACJ;UAEIiB,EAAA,CAAAI,SAAA,GACJ;UADIJ,EAAA,CAAAK,kBAAA,MAAAN,GAAA,CAAAf,QAAA,MACJ;UAEIgB,EAAA,CAAAI,SAAA,GACJ;UADIJ,EAAA,CAAAK,kBAAA,MAAAN,GAAA,CAAAd,IAAA,MACJ;UAEIe,EAAA,CAAAI,SAAA,GACJ;UADIJ,EAAA,CAAAK,kBAAA,MAAAN,GAAA,CAAAb,EAAA,MACJ;UAEIc,EAAA,CAAAI,SAAA,GACJ;UADIJ,EAAA,CAAAK,kBAAA,MAAAN,GAAA,CAAAZ,IAAA,MACJ;;;;;;SDRSN,2BAA2B;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}