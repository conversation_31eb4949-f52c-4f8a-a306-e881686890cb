{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst _c0 = [\"notFoundEl\"];\nexport let NotFoundComponent = /*#__PURE__*/(() => {\n  class NotFoundComponent {\n    constructor() {\n      this.notFoundEl = {};\n    }\n    ngOnInit() {}\n    ngAfterViewInit() {\n      this.addLottiePlayerGraphic(this.notFoundEl);\n    }\n    addLottiePlayerGraphic(el) {\n      const lottiePlayer = `<div  class=\"animation-wrapper\"><lottie-player src=\"https://assets3.lottiefiles.com/packages/lf20_aaesnvcw.json\" background=\"transparent\" \n    speed=\"1\" style=\"width: 100%; max-height: 100%;\" autoplay loop></lottie-player></div>`;\n      el.nativeElement.innerHTML = lottiePlayer;\n    }\n    static #_ = this.ɵfac = function NotFoundComponent_Factory(t) {\n      return new (t || NotFoundComponent)();\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NotFoundComponent,\n      selectors: [[\"app-not-found\"]],\n      viewQuery: function NotFoundComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.notFoundEl = _t.first);\n        }\n      },\n      decls: 9,\n      vars: 1,\n      consts: [[\"notFoundEl\", \"\"], [1, \"w-full\", \"h-full\", \"block-gradient\"], [1, \"abs-centered\", \"justify-content-center\", \"align-items-center\", \"flex\", \"flex-column\"], [1, \"not-found-wrapper\", \"margin-auto\", \"text-center\"], [\"pripple\", \"\", \"type\", \"button\", 1, \"p-ripple\", \"p-element\", \"p-button\", \"p-component\", 3, \"routerLink\"], [1, \"p-button-label\", \"ng-star-inserted\"], [1, \"pi\", \"pi-home\"]],\n      template: function NotFoundComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3);\n          i0.ɵɵelement(3, \"div\", null, 0);\n          i0.ɵɵelementStart(5, \"button\", 4)(6, \"span\", 5);\n          i0.ɵɵelement(7, \"i\", 6);\n          i0.ɵɵtext(8, \" Go back to home\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"routerLink\", \"/dashboard/\");\n        }\n      },\n      dependencies: [i1.RouterLink],\n      styles: [\".lottie-wrapper[_ngcontent-%COMP%]{display:flex;align-content:center;justify-content:center;height:200px}.not-found-wrapper[_ngcontent-%COMP%]{max-width:600px}\"]\n    });\n  }\n  return NotFoundComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}