{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { GuidesRoutingModule } from './guides-routing.module';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nexport let GuidesModule = /*#__PURE__*/(() => {\n  class GuidesModule {\n    static #_ = this.ɵfac = function GuidesModule_Factory(t) {\n      return new (t || GuidesModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: GuidesModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, GuidesRoutingModule, SharedModule]\n    });\n  }\n  return GuidesModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}