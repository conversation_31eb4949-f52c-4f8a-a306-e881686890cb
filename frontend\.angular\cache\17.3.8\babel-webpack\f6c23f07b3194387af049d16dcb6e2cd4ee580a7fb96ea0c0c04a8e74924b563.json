{"ast": null, "code": "import { DialogService } from 'primeng/dynamicdialog';\nimport { LessonStatus } from 'src/app/core/models/lesson.model';\nimport { LessonInfoCalendarDialogComponent } from 'src/app/modules/calendar/calendar-dialogs/lesson-info-calendar-dialog/lesson-info-calendar-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/library.service\";\nimport * as i2 from \"primeng/dynamicdialog\";\nimport * as i3 from \"src/app/core/services/general.service\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = a0 => ({\n  \"stop-color\": a0\n});\nfunction MiniLessonInfoCardComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 2);\n    i0.ɵɵlistener(\"click\", function MiniLessonInfoCardComponent_ng_container_0_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.requestlessonCardClicked());\n    });\n    i0.ɵɵelementStart(2, \"div\", 3)(3, \"span\", 4);\n    i0.ɵɵelement(4, \"i\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 6);\n    i0.ɵɵtext(6, \"Request Lesson\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction MiniLessonInfoCardComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7);\n    i0.ɵɵlistener(\"click\", function MiniLessonInfoCardComponent_ng_template_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.lessonCardClicked());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 8)(2, \"defs\")(3, \"linearGradient\", 9);\n    i0.ɵɵelement(4, \"stop\", 10)(5, \"stop\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"g\", 12);\n    i0.ɵɵelement(7, \"path\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(8, \"span\", 14)(9, \"div\", 15);\n    i0.ɵɵelement(10, \"img\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 17);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 18)(14, \"div\", 19)(15, \"div\", 20)(16, \"div\", 21);\n    i0.ɵɵelement(17, \"i\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 23);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 24)(22, \"div\", 21);\n    i0.ɵɵelement(23, \"i\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 23);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 24)(27, \"div\", 21);\n    i0.ɵɵelement(28, \"i\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 23);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵattribute(\"id\", \"linear-gradient\" + ctx_r1.convertStatusStringToSlug(ctx_r1.lesson.status));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(21, _c0, ctx_r1.getStopColorByStatus(ctx_r1.lesson.status)[0]));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(23, _c0, ctx_r1.getStopColorByStatus(ctx_r1.lesson.status)[1]));\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"id\", \"Group_4200\", ctx_r1.lesson.id, \"\");\n    i0.ɵɵpropertyInterpolate1(\"name\", \"Group 4200\", ctx_r1.lesson.id, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"id\", \"Path_4654\", ctx_r1.lesson.id, \"\");\n    i0.ɵɵpropertyInterpolate1(\"name\", \"Path 4654\", ctx_r1.lesson.id, \"\");\n    i0.ɵɵattribute(\"fill\", \"url(#linear-gradient\" + ctx_r1.convertStatusStringToSlug(ctx_r1.lesson.status) + \")\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"src\", \"/assets/icons/lessons/\", ctx_r1.lessonStatusIcon, \".svg\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.lesson.status, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 18, ctx_r1.lesson.startingDate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.generalService.getLessonTime(ctx_r1.lesson.startingDate));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.generalService.convertHoursToMinutesWithSuffix(ctx_r1.lesson.duration));\n  }\n}\nexport class MiniLessonInfoCardComponent {\n  constructor(libraryService, dialogService, generalService, router) {\n    this.libraryService = libraryService;\n    this.dialogService = dialogService;\n    this.generalService = generalService;\n    this.router = router;\n    this.cardType = 'lesson';\n    this.statusColor = 'trial-completed';\n    this.lesson = {};\n    this.classroom = {};\n    this.lessonStatus = LessonStatus;\n  }\n  ngOnInit() {}\n  get lessonStatusColor() {\n    let status = this.lesson.status;\n    switch (status.toLowerCase()) {\n      case this.lessonStatus.COMPLETED.toLowerCase():\n        return 'completed';\n        break;\n      case this.lessonStatus.ARRANGED.toLowerCase():\n        return 'arranged';\n        break;\n      case this.lessonStatus.NO_SHOW.toLowerCase():\n        return 'no-show';\n        break;\n      case this.lessonStatus.CANCELED.toLowerCase():\n        return 'canceled';\n        break;\n      default:\n        // Do nothing if the status string is not recognized\n        break;\n    }\n    return 'arranged';\n  }\n  get lessonStatusIcon() {\n    let status = this.lesson.status;\n    switch (status.toLowerCase()) {\n      case this.lessonStatus.COMPLETED.toLowerCase():\n      case this.lessonStatus.COMPLETED_TRIAL.toLowerCase():\n        return 'completed';\n      case this.lessonStatus.ARRANGED.toLowerCase():\n      case this.lessonStatus.ARRANGED_TRIAL.toLowerCase():\n        return 'arranged';\n      case this.lessonStatus.NO_SHOW.toLowerCase():\n        return 'no-show';\n      case this.lessonStatus.CANCELED.toLowerCase():\n      case this.lessonStatus.CANCELED_TRIAL.toLowerCase():\n        return 'canceled';\n      default:\n        // Do nothing if the status string is not recognized\n        break;\n    }\n    return 'arranged';\n  }\n  getStopColorByStatus(status) {\n    if (status.toLowerCase().includes('trial')) {\n      // trial arranged\n      if (status.toLowerCase().includes(this.lessonStatus.ARRANGED.toLowerCase())) {\n        return ['#7F74F4', '#7F74F4'];\n      }\n    }\n    if (status.toLowerCase().includes(this.lessonStatus.ARRANGED.toLowerCase())) {\n      return ['#6F6DDE', '#583CB3'];\n    } else if (status.toLowerCase().includes('show')) {\n      return ['#B4CAFB', '#457CF5'];\n    } else if (status.toLowerCase().includes(this.lessonStatus.ARRANGED_TRIAL.toLowerCase())) {\n      return ['#6F6DDE', '#583CB3'];\n    } else if (status.toLowerCase().includes(this.lessonStatus.CANCELED_TRIAL.toLowerCase())) {\n      return ['#ED6C74', '#EF7B6E'];\n    } else if (status.toLowerCase().includes(this.lessonStatus.CANCELED.toLowerCase())) {\n      return ['#E3557C', '#E47272'];\n    } else if (status.toLowerCase().includes(this.lessonStatus.COMPLETED_TRIAL.toLowerCase())) {\n      return ['#58B5DD', '#468DDD'];\n    } else if (status.toLowerCase().includes(this.lessonStatus.COMPLETED.toLowerCase())) {\n      return ['#529BDD', '#315DAF'];\n    } else if (status.toLowerCase().includes(this.lessonStatus.REQUESTED.toLowerCase())) {\n      return ['#9C50CA', '#6C5194'];\n    }\n    return ['#000', '#AAA'];\n  }\n  lessonCardClicked() {\n    const dData = {\n      type: 'lesson',\n      lesson: this.lesson,\n      classroom: this.classroom,\n      dialogService: this.dialogService\n    };\n    this.libraryService.openDialogWithComponent(this.dialogService, LessonInfoCalendarDialogComponent, 290, dData, null, result => {\n      console.log(result);\n      // if (result && result.action === 'shareWithMultiple') {\n      //   this.shareLibraryFiles(result.map.map);\n      // }\n    });\n  }\n  requestlessonCardClicked() {\n    // this.router.navigateByUrl('/dashboard/calendar/booking-system');\n    const navigationExtras = {\n      queryParams: {\n        'classroom': JSON.stringify(this.classroom)\n      }\n    };\n    this.router.navigate(['/dashboard/calendar/booking-system'], navigationExtras);\n    // return this.generalService.navigateToBookingSystem();\n  }\n  convertStatusStringToSlug(word) {\n    return word.toLowerCase().replace(\" \", \"-\");\n  }\n  static #_ = this.ɵfac = function MiniLessonInfoCardComponent_Factory(t) {\n    return new (t || MiniLessonInfoCardComponent)(i0.ɵɵdirectiveInject(i1.LibraryService), i0.ɵɵdirectiveInject(i2.DialogService), i0.ɵɵdirectiveInject(i3.GeneralService), i0.ɵɵdirectiveInject(i4.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MiniLessonInfoCardComponent,\n    selectors: [[\"app-mini-lesson-info-card\"]],\n    inputs: {\n      cardType: \"cardType\",\n      statusColor: \"statusColor\",\n      lesson: \"lesson\",\n      classroom: \"classroom\"\n    },\n    features: [i0.ɵɵProvidersFeature([DialogService])],\n    decls: 3,\n    vars: 2,\n    consts: [[\"scheduleCard\", \"\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"card-height\", \"shadow-2\", \"request-card-bg\", \"pointer\", \"hover:text-gray-900\", 3, \"click\"], [1, \"p-2\", \"text-center\", \"font-sm\", \"mx-5\"], [1, \"inline-flex\", \"justify-content-center\", \"align-items-center\", \"border-circle\", \"mb-1\", \"p-1\"], [1, \"pi\", \"pi-plus-circle\", \"text-4xl\", \"text-white\"], [1, \"text-md\", \"font-xs\", \"text-white\", \"line-height-1\"], [1, \"relative\", \"overflow-hidden\", \"card-height\", \"surface-card\", \"schedule-card\", \"shadow-2\", \"pointer\", 3, \"click\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", 0, \"xmlns\", \"xlink\", \"http://www.w3.org/1999/xlink\", \"width\", \"315.754\", \"height\", \"126.01\", \"viewBox\", \"0 0 315.754 126.01\", 1, \"grad-group-svg\"], [\"x1\", \"0.5\", \"x2\", \"0.529\", \"y2\", \"0.953\", \"gradientUnits\", \"objectBoundingBox\"], [\"offset\", \"0\", 3, \"ngStyle\"], [\"offset\", \"1\", 3, \"ngStyle\"], [\"transform\", \"translate(-900.623 -490.391)\", 3, \"id\", \"name\"], [\"d\", \"M6338,16122.322s9.9,23.213,116.033-11.912,191.156-9.033,191.156-9.033l-.3-24.115s-.441-5.428-3.148-8.766c-1.331-1.646-5.028-18.027-7.678-17.566-7.635,1.32-292.554,15.408-292.554,15.408s-3.583,3.865-3.513,13.514S6338,16122.322,6338,16122.322Z\", \"transform\", \"translate(-2836.078 -16354.396) rotate(9)\", 3, \"id\", \"name\"], [1, \"flex\", \"align-items-center\", \"text-white\", \"my-2\", \"text-center\", \"lg:text-center\", \"ml-1\", \"capitalize\", \"icon-text\", \"z-3\", 2, \"font-size\", \"0.69rem!important\"], [1, \"icon-badge\", \"inverted\", \"z-6\"], [3, \"src\"], [1, \"max-w-3rem\", \"text-left\", \"line-height-1\"], [1, \"grid\", \"grid-nogutter\", \"bottom-icons\", \"w-100\", \"align-items-center\", \"justify-content-center\", \"h-5rem\"], [1, \"flex\", \"flex-column\", \"fl-typo\", \"s10-14\"], [1, \"flex\", \"text-primary\", \"align-items-center\", \"justify-content-center\", \"gap-1\", \"font-xs\"], [1, \"col-icon\"], [1, \"pi\", \"pi-calendar\", \"font-xs\"], [1, \"col-text\"], [1, \"flex\", \"text-primary\", \"gap-1\", \"font-xs\"], [1, \"pi\", \"pi-clock\", \"font-xs\"], [1, \"pi\", \"pi-hourglass\", \"font-xs\"]],\n    template: function MiniLessonInfoCardComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, MiniLessonInfoCardComponent_ng_container_0_Template, 7, 0, \"ng-container\", 1)(1, MiniLessonInfoCardComponent_ng_template_1_Template, 31, 25, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        const scheduleCard_r4 = i0.ɵɵreference(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.cardType === \"request\")(\"ngIfElse\", scheduleCard_r4);\n      }\n    },\n    dependencies: [i5.NgIf, i5.NgStyle, i5.DatePipe],\n    styles: [\"@charset \\\"UTF-8\\\";\\n.responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.icon-badge[_ngcontent-%COMP%] {\\n  width: 0;\\n  height: 0;\\n  position: relative;\\n  top: 0;\\n  left: 0;\\n  \\n\\n  \\n\\n  \\n\\n  width: 22px;\\n  \\n\\n  \\n\\n  \\n\\n  width: 28px;\\n  border-radius: 0 0 50px 0;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n@media (min-width: 320px) and (max-width: 991px) {\\n  .icon-badge[_ngcontent-%COMP%] {\\n    \\n\\n    width: 27px;\\n    \\n\\n    width: calc(22px + (32 - 22) * (100vw - 320px) / (992 - 320));\\n  }\\n}\\n@media (min-width: 992px) {\\n  .icon-badge[_ngcontent-%COMP%] {\\n    width: 32px;\\n  }\\n}\\n@media (min-width: 992px) and (max-width: 1599px) {\\n  .icon-badge[_ngcontent-%COMP%] {\\n    \\n\\n    width: 35px;\\n    \\n\\n    width: calc(28px + (42 - 28) * (100vw - 992px) / (1600 - 992));\\n  }\\n}\\n@media (min-width: 1600px) {\\n  .icon-badge[_ngcontent-%COMP%] {\\n    width: 42px;\\n  }\\n}\\n.icon-badge[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: clamp(0.88rem, 0.31vw + 0.81rem, 1.13rem);\\n  height: clamp(0.88rem, 0.31vw + 0.81rem, 1.13rem);\\n}\\n\\n.request-card-bg[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(0deg, hsl(230, 51%, 57%) 0%, hsl(230, 53%, 60%) 24%, hsl(230, 55%, 62%) 46%, hsl(230, 58%, 65%) 61%, hsl(230, 61%, 68%) 72%, hsl(230, 65%, 71%) 80%, hsl(230, 69%, 74%) 86%, hsl(230, 75%, 77%) 91%, hsl(230, 82%, 79%) 96%, hsl(230, 91%, 82%) 100%);\\n}\\n\\n.surface-card[_ngcontent-%COMP%], .request-card-bg[_ngcontent-%COMP%] {\\n  aspect-ratio: 1/1;\\n  border-radius: 9px;\\n  height: 120px;\\n  width: 120px;\\n}\\n@media only screen and (max-width: 768px) {\\n  .surface-card[_ngcontent-%COMP%], .request-card-bg[_ngcontent-%COMP%] {\\n    margin: 0 auto;\\n  }\\n}\\n\\n.request-card-bg[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.no-show[_ngcontent-%COMP%] {\\n  background: linear-gradient(#002ccf 0%, #dbe2ff 100%);\\n}\\n\\n.grad-group-svg[_ngcontent-%COMP%] {\\n  position: absolute;\\n  z-index: 1;\\n  top: -27px;\\n  height: 101px;\\n  left: -49px;\\n}\\n\\n.icon-text[_ngcontent-%COMP%] {\\n  position: absolute;\\n  height: 32px;\\n  top: -5px;\\n}\\n@media only screen and (max-width: 768px) {\\n  .icon-text[_ngcontent-%COMP%] {\\n    left: 15px;\\n  }\\n}\\n\\n.bottom-icons[_ngcontent-%COMP%] {\\n  margin-top: 2.3rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["DialogService", "LessonStatus", "LessonInfoCalendarDialogComponent", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "MiniLessonInfoCardComponent_ng_container_0_Template_div_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "requestlessonCardClicked", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "MiniLessonInfoCardComponent_ng_template_1_Template_div_click_0_listener", "_r3", "lessonCardClicked", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "getStopColorByStatus", "lesson", "status", "ɵɵpropertyInterpolate1", "id", "lessonStatusIcon", "ɵɵsanitizeUrl", "ɵɵtextInterpolate1", "ɵɵtextInterpolate", "ɵɵpipeBind2", "startingDate", "generalService", "getLessonTime", "convertHoursToMinutesWithSuffix", "duration", "MiniLessonInfoCardComponent", "constructor", "libraryService", "dialogService", "router", "cardType", "statusColor", "classroom", "lessonStatus", "ngOnInit", "lessonStatusColor", "toLowerCase", "COMPLETED", "ARRANGED", "NO_SHOW", "CANCELED", "COMPLETED_TRIAL", "ARRANGED_TRIAL", "CANCELED_TRIAL", "includes", "REQUESTED", "dData", "type", "openDialogWithComponent", "result", "console", "log", "navigationExtras", "queryParams", "JSON", "stringify", "navigate", "convertStatusStringToSlug", "word", "replace", "_", "ɵɵdirectiveInject", "i1", "LibraryService", "i2", "i3", "GeneralService", "i4", "Router", "_2", "selectors", "inputs", "features", "ɵɵProvidersFeature", "decls", "vars", "consts", "template", "MiniLessonInfoCardComponent_Template", "rf", "ctx", "ɵɵtemplate", "MiniLessonInfoCardComponent_ng_container_0_Template", "MiniLessonInfoCardComponent_ng_template_1_Template", "ɵɵtemplateRefExtractor", "scheduleCard_r4"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\lesson\\mini-lesson-info-card\\mini-lesson-info-card.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\lesson\\mini-lesson-info-card\\mini-lesson-info-card.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';\r\nimport { NavigationExtras, Router } from '@angular/router';\r\nimport { DialogService } from 'primeng/dynamicdialog';\r\nimport { Classroom } from 'src/app/core/models/classroom.model';\r\nimport { Lesson, LessonStatus } from 'src/app/core/models/lesson.model';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { LibraryService } from 'src/app/core/services/library.service';\r\nimport { LessonInfoCalendarDialogComponent } from 'src/app/modules/calendar/calendar-dialogs/lesson-info-calendar-dialog/lesson-info-calendar-dialog.component';\r\ntype CardType = 'lesson' | 'request';\r\n\r\n@Component({\r\n  selector: 'app-mini-lesson-info-card',\r\n  templateUrl: './mini-lesson-info-card.component.html',\r\n  styleUrls: ['./mini-lesson-info-card.component.scss'],\r\n  providers: [DialogService]\r\n})\r\n\r\nexport class MiniLessonInfoCardComponent implements OnInit {\r\n\r\n  @Input() cardType: CardType = 'lesson';\r\n  @Input() statusColor: string = 'trial-completed';\r\n  @Input() lesson: Lesson = {} as Lesson;\r\n  @Input() classroom: Classroom = {} as Classroom;\r\n  lessonStatus = LessonStatus;\r\n  constructor(\r\n    private libraryService: LibraryService,\r\n    private dialogService: DialogService,\r\n    public generalService: GeneralService,\r\n    private router: Router,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n  }\r\n\r\n  get lessonStatusColor() {\r\n    let status = this.lesson.status as LessonStatus;\r\n    switch (status.toLowerCase()) {\r\n      case this.lessonStatus.COMPLETED.toLowerCase():\r\n        return 'completed';\r\n        break;\r\n      case this.lessonStatus.ARRANGED.toLowerCase():\r\n        return 'arranged';\r\n        break;\r\n      case this.lessonStatus.NO_SHOW.toLowerCase():\r\n        return 'no-show';\r\n        break;\r\n      case this.lessonStatus.CANCELED.toLowerCase():\r\n        return 'canceled';\r\n        break;\r\n      default:\r\n        // Do nothing if the status string is not recognized\r\n        break;\r\n    }\r\n    return 'arranged';\r\n  }\r\n\r\n  get lessonStatusIcon() {\r\n    let status = this.lesson.status as LessonStatus;\r\n    switch (status.toLowerCase()) {\r\n      case this.lessonStatus.COMPLETED.toLowerCase():\r\n      case this.lessonStatus.COMPLETED_TRIAL.toLowerCase():\r\n        return 'completed';\r\n      case this.lessonStatus.ARRANGED.toLowerCase():\r\n      case this.lessonStatus.ARRANGED_TRIAL.toLowerCase():\r\n        return 'arranged';\r\n      case this.lessonStatus.NO_SHOW.toLowerCase():\r\n        return 'no-show';\r\n      case this.lessonStatus.CANCELED.toLowerCase():\r\n      case this.lessonStatus.CANCELED_TRIAL.toLowerCase():\r\n        return 'canceled';\r\n      default:\r\n        // Do nothing if the status string is not recognized\r\n        break;\r\n    }\r\n    return 'arranged';\r\n  }\r\n\r\n  getStopColorByStatus(status: string): string[] {\r\n\r\n    if (status.toLowerCase().includes('trial')) {\r\n      // trial arranged\r\n      if (status.toLowerCase().includes(this.lessonStatus.ARRANGED.toLowerCase())) {\r\n        return ['#7F74F4', '#7F74F4'];\r\n      }\r\n    }\r\n\r\n\r\n    if (status.toLowerCase().includes(this.lessonStatus.ARRANGED.toLowerCase())) {\r\n      return ['#6F6DDE', '#583CB3'];\r\n    } else if (status.toLowerCase().includes('show')) {\r\n      return ['#B4CAFB', '#457CF5'];\r\n    } else if (status.toLowerCase().includes(this.lessonStatus.ARRANGED_TRIAL.toLowerCase())) {\r\n      return ['#6F6DDE', '#583CB3'];\r\n    } else if (status.toLowerCase().includes(this.lessonStatus.CANCELED_TRIAL.toLowerCase())) {\r\n      return ['#ED6C74', '#EF7B6E'];\r\n    } else if (status.toLowerCase().includes(this.lessonStatus.CANCELED.toLowerCase())) {\r\n      return ['#E3557C', '#E47272'];\r\n    } else if (status.toLowerCase().includes(this.lessonStatus.COMPLETED_TRIAL.toLowerCase())) {\r\n      return ['#58B5DD', '#468DDD'];\r\n    } else if (status.toLowerCase().includes(this.lessonStatus.COMPLETED.toLowerCase())) {\r\n      return ['#529BDD', '#315DAF'];\r\n    } else if (status.toLowerCase().includes(this.lessonStatus.REQUESTED.toLowerCase())) {\r\n      return ['#9C50CA', '#6C5194'];\r\n    }\r\n    return ['#000', '#AAA'];\r\n\r\n  }\r\n\r\n  lessonCardClicked() {\r\n    const dData = {\r\n      type: 'lesson',\r\n      lesson: this.lesson,\r\n      classroom: this.classroom,\r\n      dialogService: this.dialogService\r\n    }\r\n    this.libraryService.openDialogWithComponent(this.dialogService, LessonInfoCalendarDialogComponent, 290,\r\n      dData, null, (result: any) => {\r\n        console.log(result);\r\n        // if (result && result.action === 'shareWithMultiple') {\r\n        //   this.shareLibraryFiles(result.map.map);\r\n        // }\r\n      });\r\n  }\r\n\r\n  requestlessonCardClicked() {\r\n    // this.router.navigateByUrl('/dashboard/calendar/booking-system');\r\n    const navigationExtras: NavigationExtras = {\r\n      queryParams: { 'classroom': JSON.stringify(this.classroom) }\r\n    };\r\n    this.router.navigate(['/dashboard/calendar/booking-system'], navigationExtras);\r\n    // return this.generalService.navigateToBookingSystem();\r\n  }\r\n\r\n  convertStatusStringToSlug(word: string) {\r\n    return word.toLowerCase().replace(\" \", \"-\");\r\n  }\r\n\r\n}\r\n", "\r\n\r\n<ng-container *ngIf=\"cardType === 'request'; else scheduleCard\">\r\n    <div class=\"card-height shadow-2 request-card-bg pointer hover:text-gray-900\" (click)=\"requestlessonCardClicked()\">\r\n      <div class=\"p-2 text-center font-sm mx-5\">\r\n        <span class=\"inline-flex justify-content-center align-items-center border-circle mb-1 p-1\">\r\n          <i class=\"pi pi-plus-circle text-4xl text-white\"></i>\r\n        </span>\r\n        <div class=\"text-md font-xs text-white line-height-1\">Request Lesson</div>\r\n      </div>\r\n    </div>\r\n  </ng-container>\r\n\r\n  <ng-template #scheduleCard>\r\n    <div class=\"relative overflow-hidden card-height surface-card schedule-card shadow-2 pointer\" (click)=\"lessonCardClicked()\">\r\n\r\n\r\n      <svg xmlns=\"http://www.w3.org/2000/svg\"  class=\"grad-group-svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" \r\n      width=\"315.754\" height=\"126.01\" viewBox=\"0 0 315.754 126.01\">\r\n        <defs>\r\n          <!--  [attr.stop-color]=\"getStopColorByStatus(lesson.status)[0]\" \r\n          [attr.class]=\"'stop-1-'+lesson.status.toLowerCase()\"\r\n          -->\r\n          <linearGradient [attr.id]=\"'linear-gradient'+convertStatusStringToSlug(lesson.status)\" x1=\"0.5\" x2=\"0.529\" y2=\"0.953\" gradientUnits=\"objectBoundingBox\">\r\n            <stop offset=\"0\" [ngStyle]=\"{'stop-color': getStopColorByStatus(lesson.status)[0]}\" />\r\n            <stop offset=\"1\"  [ngStyle]=\"{'stop-color': getStopColorByStatus(lesson.status)[1]}\" />\r\n          </linearGradient>\r\n        </defs>\r\n        <g id=\"Group_4200{{lesson.id}}\" data-name=\"Group 4200{{lesson.id}}\" transform=\"translate(-900.623 -490.391)\">\r\n          <path id=\"Path_4654{{lesson.id}}\" data-name=\"Path 4654{{lesson.id}}\" d=\"M6338,16122.322s9.9,23.213,116.033-11.912,191.156-9.033,191.156-9.033l-.3-24.115s-.441-5.428-3.148-8.766c-1.331-1.646-5.028-18.027-7.678-17.566-7.635,1.32-292.554,15.408-292.554,15.408s-3.583,3.865-3.513,13.514S6338,16122.322,6338,16122.322Z\" transform=\"translate(-2836.078 -16354.396) rotate(9)\" \r\n          [attr.fill]=\"'url(#linear-gradient'+convertStatusStringToSlug(lesson.status)+')'\"/>\r\n        </g>\r\n      </svg>\r\n\r\n      \r\n      <!--  [ngClass]=\"lessonStatusColor + '-gradient-lesson-bg'\" -->\r\n    \r\n        <span class=\"flex align-items-center text-white my-2 text-center lg:text-center ml-1 capitalize icon-text z-3\" style=\"font-size: 0.69rem!important;\">\r\n          <div class=\"icon-badge inverted z-6\">\r\n            <img src=\"/assets/icons/lessons/{{lessonStatusIcon}}.svg\" />\r\n          </div> <span class=\"max-w-3rem text-left line-height-1\" > {{lesson.status}} </span>\r\n        </span>\r\n    \r\n    \r\n        <div class=\"grid grid-nogutter bottom-icons w-100 align-items-center justify-content-center h-5rem\">\r\n          <div class=\"flex flex-column fl-typo s10-14\">\r\n    \r\n            <div class=\"flex text-primary align-items-center justify-content-center gap-1 font-xs\">\r\n              <div class=\"col-icon\">\r\n                <i class=\"pi  pi-calendar font-xs\"></i>\r\n              </div>\r\n              <div class=\"col-text\">{{lesson.startingDate | date: 'dd/MM/yyyy'}}</div>\r\n            </div>\r\n    \r\n            <div class=\"flex text-primary gap-1 font-xs\">\r\n              <div class=\"col-icon\">\r\n                <i class=\"pi  pi-clock font-xs\"></i>\r\n              </div>\r\n              <div class=\"col-text\">{{generalService.getLessonTime(lesson.startingDate)}}</div>\r\n            </div>\r\n    \r\n            <div class=\"flex text-primary gap-1 font-xs\">\r\n              <div class=\"col-icon\">\r\n                <i class=\"pi  pi-hourglass font-xs\"></i>\r\n              </div>\r\n              <div class=\"col-text\">{{generalService.convertHoursToMinutesWithSuffix(lesson.duration)}}</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n  </ng-template>\r\n\r\n"], "mappings": "AAEA,SAASA,aAAa,QAAQ,uBAAuB;AAErD,SAAiBC,YAAY,QAAQ,kCAAkC;AAGvE,SAASC,iCAAiC,QAAQ,6GAA6G;;;;;;;;;;;;;ICL/JC,EAAA,CAAAC,uBAAA,GAAgE;IAC5DD,EAAA,CAAAE,cAAA,aAAmH;IAArCF,EAAA,CAAAG,UAAA,mBAAAC,yEAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAG,wBAAA,EAA0B;IAAA,EAAC;IAE9GV,EADF,CAAAE,cAAA,aAA0C,cACmD;IACzFF,EAAA,CAAAW,SAAA,WAAqD;IACvDX,EAAA,CAAAY,YAAA,EAAO;IACPZ,EAAA,CAAAE,cAAA,aAAsD;IAAAF,EAAA,CAAAa,MAAA,qBAAc;IAExEb,EAFwE,CAAAY,YAAA,EAAM,EACtE,EACF;;;;;;;IAINZ,EAAA,CAAAE,cAAA,aAA4H;IAA9BF,EAAA,CAAAG,UAAA,mBAAAW,wEAAA;MAAAd,EAAA,CAAAK,aAAA,CAAAU,GAAA;MAAA,MAAAR,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAS,iBAAA,EAAmB;IAAA,EAAC;;IASrHhB,EANJ,CAAAE,cAAA,aAC6D,WACrD,wBAIoJ;IAEtJF,EADA,CAAAW,SAAA,eAAsF,eACC;IAE3FX,EADE,CAAAY,YAAA,EAAiB,EACZ;IACPZ,EAAA,CAAAE,cAAA,YAA6G;IAC3GF,EAAA,CAAAW,SAAA,eACmF;IAEvFX,EADE,CAAAY,YAAA,EAAI,EACA;;IAMFZ,EADF,CAAAE,cAAA,eAAqJ,cAC9G;IACnCF,EAAA,CAAAW,SAAA,eAA4D;IAC9DX,EAAA,CAAAY,YAAA,EAAM;IAACZ,EAAA,CAAAE,cAAA,gBAAkD;IAACF,EAAA,CAAAa,MAAA,IAAkB;IAC9Eb,EAD8E,CAAAY,YAAA,EAAO,EAC9E;IAODZ,EAJN,CAAAE,cAAA,eAAoG,eACrD,eAE4C,eAC/D;IACpBF,EAAA,CAAAW,SAAA,aAAuC;IACzCX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAE,cAAA,eAAsB;IAAAF,EAAA,CAAAa,MAAA,IAA4C;;IACpEb,EADoE,CAAAY,YAAA,EAAM,EACpE;IAGJZ,EADF,CAAAE,cAAA,eAA6C,eACrB;IACpBF,EAAA,CAAAW,SAAA,aAAoC;IACtCX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAE,cAAA,eAAsB;IAAAF,EAAA,CAAAa,MAAA,IAAqD;IAC7Eb,EAD6E,CAAAY,YAAA,EAAM,EAC7E;IAGJZ,EADF,CAAAE,cAAA,eAA6C,eACrB;IACpBF,EAAA,CAAAW,SAAA,aAAwC;IAC1CX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAE,cAAA,eAAsB;IAAAF,EAAA,CAAAa,MAAA,IAAmE;IAIjGb,EAJiG,CAAAY,YAAA,EAAM,EAC3F,EACF,EACF,EACF;;;;IA9CcZ,EAAA,CAAAiB,SAAA,GAAsE;;IACnEjB,EAAA,CAAAiB,SAAA,EAAkE;IAAlEjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAmB,eAAA,KAAAC,GAAA,EAAAb,MAAA,CAAAc,oBAAA,CAAAd,MAAA,CAAAe,MAAA,CAAAC,MAAA,MAAkE;IACjEvB,EAAA,CAAAiB,SAAA,EAAkE;IAAlEjB,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAmB,eAAA,KAAAC,GAAA,EAAAb,MAAA,CAAAc,oBAAA,CAAAd,MAAA,CAAAe,MAAA,CAAAC,MAAA,MAAkE;IAGrFvB,EAAA,CAAAiB,SAAA,EAA4B;IAA5BjB,EAAA,CAAAwB,sBAAA,qBAAAjB,MAAA,CAAAe,MAAA,CAAAG,EAAA,KAA4B;IAACzB,EAAA,CAAAwB,sBAAA,uBAAAjB,MAAA,CAAAe,MAAA,CAAAG,EAAA,KAAmC;IAC3DzB,EAAA,CAAAiB,SAAA,EAA2B;IAA3BjB,EAAA,CAAAwB,sBAAA,oBAAAjB,MAAA,CAAAe,MAAA,CAAAG,EAAA,KAA2B;IAACzB,EAAA,CAAAwB,sBAAA,sBAAAjB,MAAA,CAAAe,MAAA,CAAAG,EAAA,KAAkC;;IAU7DzB,EAAA,CAAAiB,SAAA,GAAoD;IAApDjB,EAAA,CAAAwB,sBAAA,kCAAAjB,MAAA,CAAAmB,gBAAA,UAAA1B,EAAA,CAAA2B,aAAA,CAAoD;IACD3B,EAAA,CAAAiB,SAAA,GAAkB;IAAlBjB,EAAA,CAAA4B,kBAAA,MAAArB,MAAA,CAAAe,MAAA,CAAAC,MAAA,MAAkB;IAWlDvB,EAAA,CAAAiB,SAAA,GAA4C;IAA5CjB,EAAA,CAAA6B,iBAAA,CAAA7B,EAAA,CAAA8B,WAAA,SAAAvB,MAAA,CAAAe,MAAA,CAAAS,YAAA,gBAA4C;IAO5C/B,EAAA,CAAAiB,SAAA,GAAqD;IAArDjB,EAAA,CAAA6B,iBAAA,CAAAtB,MAAA,CAAAyB,cAAA,CAAAC,aAAA,CAAA1B,MAAA,CAAAe,MAAA,CAAAS,YAAA,EAAqD;IAOrD/B,EAAA,CAAAiB,SAAA,GAAmE;IAAnEjB,EAAA,CAAA6B,iBAAA,CAAAtB,MAAA,CAAAyB,cAAA,CAAAE,+BAAA,CAAA3B,MAAA,CAAAe,MAAA,CAAAa,QAAA,EAAmE;;;ADhDvG,OAAM,MAAOC,2BAA2B;EAOtCC,YACUC,cAA8B,EAC9BC,aAA4B,EAC7BP,cAA8B,EAC7BQ,MAAc;IAHd,KAAAF,cAAc,GAAdA,cAAc;IACd,KAAAC,aAAa,GAAbA,aAAa;IACd,KAAAP,cAAc,GAAdA,cAAc;IACb,KAAAQ,MAAM,GAANA,MAAM;IATP,KAAAC,QAAQ,GAAa,QAAQ;IAC7B,KAAAC,WAAW,GAAW,iBAAiB;IACvC,KAAApB,MAAM,GAAW,EAAY;IAC7B,KAAAqB,SAAS,GAAc,EAAe;IAC/C,KAAAC,YAAY,GAAG9C,YAAY;EAMvB;EAEJ+C,QAAQA,CAAA,GACR;EAEA,IAAIC,iBAAiBA,CAAA;IACnB,IAAIvB,MAAM,GAAG,IAAI,CAACD,MAAM,CAACC,MAAsB;IAC/C,QAAQA,MAAM,CAACwB,WAAW,EAAE;MAC1B,KAAK,IAAI,CAACH,YAAY,CAACI,SAAS,CAACD,WAAW,EAAE;QAC5C,OAAO,WAAW;QAClB;MACF,KAAK,IAAI,CAACH,YAAY,CAACK,QAAQ,CAACF,WAAW,EAAE;QAC3C,OAAO,UAAU;QACjB;MACF,KAAK,IAAI,CAACH,YAAY,CAACM,OAAO,CAACH,WAAW,EAAE;QAC1C,OAAO,SAAS;QAChB;MACF,KAAK,IAAI,CAACH,YAAY,CAACO,QAAQ,CAACJ,WAAW,EAAE;QAC3C,OAAO,UAAU;QACjB;MACF;QACE;QACA;IACJ;IACA,OAAO,UAAU;EACnB;EAEA,IAAIrB,gBAAgBA,CAAA;IAClB,IAAIH,MAAM,GAAG,IAAI,CAACD,MAAM,CAACC,MAAsB;IAC/C,QAAQA,MAAM,CAACwB,WAAW,EAAE;MAC1B,KAAK,IAAI,CAACH,YAAY,CAACI,SAAS,CAACD,WAAW,EAAE;MAC9C,KAAK,IAAI,CAACH,YAAY,CAACQ,eAAe,CAACL,WAAW,EAAE;QAClD,OAAO,WAAW;MACpB,KAAK,IAAI,CAACH,YAAY,CAACK,QAAQ,CAACF,WAAW,EAAE;MAC7C,KAAK,IAAI,CAACH,YAAY,CAACS,cAAc,CAACN,WAAW,EAAE;QACjD,OAAO,UAAU;MACnB,KAAK,IAAI,CAACH,YAAY,CAACM,OAAO,CAACH,WAAW,EAAE;QAC1C,OAAO,SAAS;MAClB,KAAK,IAAI,CAACH,YAAY,CAACO,QAAQ,CAACJ,WAAW,EAAE;MAC7C,KAAK,IAAI,CAACH,YAAY,CAACU,cAAc,CAACP,WAAW,EAAE;QACjD,OAAO,UAAU;MACnB;QACE;QACA;IACJ;IACA,OAAO,UAAU;EACnB;EAEA1B,oBAAoBA,CAACE,MAAc;IAEjC,IAAIA,MAAM,CAACwB,WAAW,EAAE,CAACQ,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC1C;MACA,IAAIhC,MAAM,CAACwB,WAAW,EAAE,CAACQ,QAAQ,CAAC,IAAI,CAACX,YAAY,CAACK,QAAQ,CAACF,WAAW,EAAE,CAAC,EAAE;QAC3E,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC;MAC/B;IACF;IAGA,IAAIxB,MAAM,CAACwB,WAAW,EAAE,CAACQ,QAAQ,CAAC,IAAI,CAACX,YAAY,CAACK,QAAQ,CAACF,WAAW,EAAE,CAAC,EAAE;MAC3E,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC;IAC/B,CAAC,MAAM,IAAIxB,MAAM,CAACwB,WAAW,EAAE,CAACQ,QAAQ,CAAC,MAAM,CAAC,EAAE;MAChD,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC;IAC/B,CAAC,MAAM,IAAIhC,MAAM,CAACwB,WAAW,EAAE,CAACQ,QAAQ,CAAC,IAAI,CAACX,YAAY,CAACS,cAAc,CAACN,WAAW,EAAE,CAAC,EAAE;MACxF,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC;IAC/B,CAAC,MAAM,IAAIxB,MAAM,CAACwB,WAAW,EAAE,CAACQ,QAAQ,CAAC,IAAI,CAACX,YAAY,CAACU,cAAc,CAACP,WAAW,EAAE,CAAC,EAAE;MACxF,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC;IAC/B,CAAC,MAAM,IAAIxB,MAAM,CAACwB,WAAW,EAAE,CAACQ,QAAQ,CAAC,IAAI,CAACX,YAAY,CAACO,QAAQ,CAACJ,WAAW,EAAE,CAAC,EAAE;MAClF,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC;IAC/B,CAAC,MAAM,IAAIxB,MAAM,CAACwB,WAAW,EAAE,CAACQ,QAAQ,CAAC,IAAI,CAACX,YAAY,CAACQ,eAAe,CAACL,WAAW,EAAE,CAAC,EAAE;MACzF,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC;IAC/B,CAAC,MAAM,IAAIxB,MAAM,CAACwB,WAAW,EAAE,CAACQ,QAAQ,CAAC,IAAI,CAACX,YAAY,CAACI,SAAS,CAACD,WAAW,EAAE,CAAC,EAAE;MACnF,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC;IAC/B,CAAC,MAAM,IAAIxB,MAAM,CAACwB,WAAW,EAAE,CAACQ,QAAQ,CAAC,IAAI,CAACX,YAAY,CAACY,SAAS,CAACT,WAAW,EAAE,CAAC,EAAE;MACnF,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC;IAC/B;IACA,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC;EAEzB;EAEA/B,iBAAiBA,CAAA;IACf,MAAMyC,KAAK,GAAG;MACZC,IAAI,EAAE,QAAQ;MACdpC,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBqB,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBJ,aAAa,EAAE,IAAI,CAACA;KACrB;IACD,IAAI,CAACD,cAAc,CAACqB,uBAAuB,CAAC,IAAI,CAACpB,aAAa,EAAExC,iCAAiC,EAAE,GAAG,EACpG0D,KAAK,EAAE,IAAI,EAAGG,MAAW,IAAI;MAC3BC,OAAO,CAACC,GAAG,CAACF,MAAM,CAAC;MACnB;MACA;MACA;IACF,CAAC,CAAC;EACN;EAEAlD,wBAAwBA,CAAA;IACtB;IACA,MAAMqD,gBAAgB,GAAqB;MACzCC,WAAW,EAAE;QAAE,WAAW,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACvB,SAAS;MAAC;KAC3D;IACD,IAAI,CAACH,MAAM,CAAC2B,QAAQ,CAAC,CAAC,oCAAoC,CAAC,EAAEJ,gBAAgB,CAAC;IAC9E;EACF;EAEAK,yBAAyBA,CAACC,IAAY;IACpC,OAAOA,IAAI,CAACtB,WAAW,EAAE,CAACuB,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAC7C;EAAC,QAAAC,CAAA,G;qBAtHUnC,2BAA2B,EAAApC,EAAA,CAAAwE,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA1E,EAAA,CAAAwE,iBAAA,CAAAG,EAAA,CAAA9E,aAAA,GAAAG,EAAA,CAAAwE,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAA7E,EAAA,CAAAwE,iBAAA,CAAAM,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA3B5C,2BAA2B;IAAA6C,SAAA;IAAAC,MAAA;MAAAzC,QAAA;MAAAC,WAAA;MAAApB,MAAA;MAAAqB,SAAA;IAAA;IAAAwC,QAAA,GAAAnF,EAAA,CAAAoF,kBAAA,CAH3B,CAACvF,aAAa,CAAC;IAAAwF,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCD1B1F,EAXF,CAAA4F,UAAA,IAAAC,mDAAA,0BAAgE,IAAAC,kDAAA,kCAAA9F,EAAA,CAAA+F,sBAAA,CAWnC;;;;QAXgB/F,EAA9B,CAAAkB,UAAA,SAAAyE,GAAA,CAAAlD,QAAA,eAA8B,aAAAuD,eAAA,CAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}