{"ast": null, "code": "import { CommonModule, DOCUMENT } from \"@angular/common\";\nimport { ChangeDetectorRef, inject } from '@angular/core';\nimport { FormsModule } from \"@angular/forms\";\nimport { DomSanitizer } from \"@angular/platform-browser\";\nimport { Router } from \"@angular/router\";\nimport { DropdownModule } from \"primeng/dropdown\";\nimport { EMPTY, forkJoin, of } from \"rxjs\";\nimport { catchError, startWith, switchMap, take, tap } from \"rxjs/operators\";\nimport { AuthService } from \"src/app/core/services/auth.service\";\nimport { GeneralService } from \"src/app/core/services/general.service\";\nimport { LingoletteService } from \"src/app/core/services/lingolette.service\";\nimport { PackageService } from \"src/app/core/services/package.service\";\nimport { TeacherApplicationService } from \"src/app/core/services/teacher-application.service\";\nimport { UserService } from \"src/app/core/services/user.service\";\nimport { LoaderComponent } from \"src/app/shared/loader/loader.component\";\nimport { SubSink } from \"subsink\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/layout.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"primeng/dropdown\";\nconst _c0 = [\"lingoletteWrapper\"];\nconst _c1 = [\"lottieEl\"];\nconst _c2 = a0 => ({\n  \"surface-section px-4 py-8 md:px-6 lg:px-4 block-gradient-reverse border-round-3xl\\nflex align-items-center justify-content-center\": a0\n});\nfunction LingoletteRoomComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6);\n    i0.ɵɵelement(2, \"app-loader\", 7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scale\", 1.7)(\"size\", 200);\n  }\n}\nfunction LingoletteRoomComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 8)(2, \"div\", 9);\n    i0.ɵɵelement(3, \"div\", 10, 1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h5\", 11);\n    i0.ɵɵtext(6, \" Please purchase a Flexible or Premium Package to use our AI-Chat.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function LingoletteRoomComponent_ng_container_3_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onGoToBuyPackage());\n    });\n    i0.ɵɵelement(8, \"span\", 13);\n    i0.ɵɵelementStart(9, \"span\", 14);\n    i0.ɵɵtext(10, \"Buy a Package\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"i\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction LingoletteRoomComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 8)(2, \"p\");\n    i0.ɵɵtext(3, \" There was an error adding your Lingolette account.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function LingoletteRoomComponent_ng_container_4_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClickRemoveLingoletteUser());\n    });\n    i0.ɵɵelement(5, \"span\", 13);\n    i0.ɵɵelementStart(6, \"span\", 14);\n    i0.ɵɵtext(7, \"Reset your Lingolette account\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction LingoletteRoomComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 16)(2, \"div\", 17)(3, \"div\", 18);\n    i0.ɵɵelement(4, \"div\", 10, 1);\n    i0.ɵɵelementStart(6, \"div\", 19);\n    i0.ɵɵtext(7, \"Start Learning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"p\", 20);\n    i0.ɵɵelementStart(9, \"div\", 8)(10, \"div\", 21)(11, \"div\", 22)(12, \"label\", 23);\n    i0.ɵɵtext(13, \"Choose a language to learn\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p-dropdown\", 24);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LingoletteRoomComponent_ng_container_5_Template_p_dropdown_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedLanguage, $event) || (ctx_r1.selectedLanguage = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function LingoletteRoomComponent_ng_container_5_Template_p_dropdown_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTeacherChange($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 21)(16, \"div\", 22)(17, \"label\", 23);\n    i0.ɵɵtext(18, \"Choose a language level\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"p-dropdown\", 24);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LingoletteRoomComponent_ng_container_5_Template_p_dropdown_ngModelChange_19_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedLevel, $event) || (ctx_r1.selectedLevel = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function LingoletteRoomComponent_ng_container_5_Template_p_dropdown_ngModelChange_19_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTeacherChange($event));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(20, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function LingoletteRoomComponent_ng_container_5_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.createLingoletteUser());\n    });\n    i0.ɵɵelement(21, \"span\", 13);\n    i0.ɵɵelementStart(22, \"span\", 14);\n    i0.ɵɵtext(23, \"Continue to Chat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(24, \"i\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(25, \"div\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"options\", ctx_r1.languageOptions);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedLanguage);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"options\", ctx_r1.languageLevels);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedLevel);\n  }\n}\nfunction LingoletteRoomComponent_ng_container_6_iframe_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"iframe\", 26);\n    i0.ɵɵlistener(\"load\", function LingoletteRoomComponent_ng_container_6_iframe_1_Template_iframe_load_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.handleIframeLoaded());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.url, i0.ɵɵsanitizeResourceUrl);\n  }\n}\nfunction LingoletteRoomComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, LingoletteRoomComponent_ng_container_6_iframe_1_Template, 1, 1, \"iframe\", 25);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showIframe);\n  }\n}\nexport let LingoletteRoomComponent = /*#__PURE__*/(() => {\n  class LingoletteRoomComponent {\n    constructor(layoutService, renderer, document) {\n      this.layoutService = layoutService;\n      this.renderer = renderer;\n      this.document = document;\n      this.subs = new SubSink();\n      this.lingoletteService = inject(LingoletteService);\n      this.authService = inject(AuthService);\n      this.packageService = inject(PackageService);\n      this.generalService = inject(GeneralService);\n      this.userService = inject(UserService);\n      this.sanitizer = inject(DomSanitizer);\n      this.router = inject(Router);\n      this.teacherApplicationService = inject(TeacherApplicationService);\n      this.loading = true;\n      this.showIframe = false;\n      this.userAddError = false;\n      this.user = {};\n      this.token = null;\n      this.cdr = inject(ChangeDetectorRef);\n      this.galaxyWrapper = {};\n      this.lottieEl = {};\n      this.languageCodeMap = {\n        \"English\": \"en\",\n        \"French\": \"fr\",\n        \"German\": \"de\",\n        \"Italian\": \"it\",\n        \"Spanish\": \"es\",\n        \"Swedish\": \"sv\",\n        \"Dutch\": \"nl\",\n        \"Russian\": \"ru\",\n        \"Japanese\": \"ja\",\n        \"Korean\": \"ko\",\n        \"Chinese\": \"zh\"\n      };\n      this.languageLevels = [{\n        label: 'I don\\'t know',\n        value: 0\n      }, {\n        label: 'Absolute Beginner',\n        value: 7\n      }, {\n        label: 'A1',\n        value: 1\n      }, {\n        label: 'A2',\n        value: 2\n      }, {\n        label: 'B1',\n        value: 3\n      }, {\n        label: 'B2',\n        value: 4\n      }, {\n        label: 'C1',\n        value: 5\n      }, {\n        label: 'C2',\n        value: 6\n      }];\n      this.step = 0;\n      this.languageOptions = [];\n      this.selectedLanguage = {\n        label: 'English',\n        value: 'en'\n      };\n      this.selectedLevel = this.languageLevels[1];\n      this.url = {};\n      this.isUserPremiumOrFlex = false;\n    }\n    ngOnInit() {\n      this.languageOptions = Object.keys(this.languageCodeMap).map(key => ({\n        label: key,\n        value: this.languageCodeMap[key]\n      }));\n      this.selectedLanguage = this.languageOptions[0];\n      this.user = this.authService.getLoggedInUser();\n      this.getLingoLanguages();\n      this.setIframeHeight();\n      forkJoin([this.packageService.getAllUserPackages(this.authService.getUserId()).pipe(tap(res => {\n        // handle packages\n        this.handlePackages(res);\n      })), this.lingoletteService.getLingoletteUserList().pipe(tap(res => {\n        // handle lingolette user list\n        console.log(res);\n      }), catchError(err => {\n        console.error(err);\n        return EMPTY;\n      }))]).subscribe();\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n    }\n    handlePackages(packages) {\n      const matchingPackage = packages.find(pkg => pkg.packLanguage && this.languageOptions.some(opt => opt.label === pkg.packLanguage));\n      const matchingLevel = packages.find(pkg => pkg.packageLevel && this.languageLevels.some(opt => opt.label === pkg.packageLevel));\n      console.log(matchingPackage);\n      if (matchingLevel) {\n        this.selectedLevel = {\n          label: matchingLevel.packageLevel,\n          value: this.languageLevels.find(opt => opt.label === matchingLevel.packageLevel)?.value\n        };\n      }\n      if (matchingPackage) {\n        this.selectedLanguage = {\n          label: matchingPackage.packLanguage,\n          value: this.languageCodeMap[matchingPackage.packLanguage]\n        };\n      }\n      this.handleLingoletteInit();\n      console.log(this.packageService.userIsOnFlexOrPremiumPackage(packages));\n      this.isUserPremiumOrFlex = this.packageService.userIsOnFlexOrPremiumPackage(packages);\n    }\n    handleLingoletteInit() {\n      setTimeout(() => {\n        this.addLottiePlayer(this.lottieEl);\n      }, 600);\n      // if (this.step !== 1) {\n      //     return;\n      // }\n      // this.createLingoletteUser();\n      this.subs.add(this.userService.getUserById(this.authService.getUserId()).pipe(take(1), tap(res => {\n        console.log(res);\n        if (res.lingoletteId !== \"\" && res.lingoletteId !== null) {\n          this.getLingoletteToken(res.lingoletteId);\n        } else {\n          this.loading = false;\n          this.step = 1;\n        }\n      })).subscribe());\n    }\n    getLingoletteToken(lingoletteId) {\n      this.subs.add(this.lingoletteService.createLingoletteUserSession(lingoletteId).pipe(take(1), tap(res => {\n        console.log(res);\n        this.token = res.data.token;\n        setTimeout(() => {\n          this.step = 2;\n          this.showIframe = true;\n          const url = `https://lingolette.com/talk/?token=${this.token}`;\n          this.url = this.sanitizer.bypassSecurityTrustResourceUrl(url);\n        }, 500);\n        this.loading = false;\n      }), catchError(err => {\n        console.log(err);\n        this.handleError(err);\n        return EMPTY;\n      })).subscribe());\n    }\n    createLingoletteUser() {\n      const userData = {\n        \"name\": this.generalService.getPersonFullName(this.user),\n        \"targetLng\": this.selectedLanguage.value,\n        \"nativeLng\": \"en\",\n        \"languageLevel\": this.selectedLevel.value\n      };\n      this.subs.add(this.lingoletteService.createLingoletteUser(userData).subscribe({\n        next: res => {\n          console.log(res);\n          this.getLingoletteToken(res.data.id);\n        },\n        error: err => {\n          console.log(err);\n          this.handleError(err);\n        }\n      }));\n    }\n    onClickRemoveLingoletteUser() {\n      this.userService.getUserById(this.authService.getUserId()).subscribe(res => {\n        console.log(res);\n        if (res.lingoletteId !== \"\" && res.lingoletteId !== null) {\n          this.removeLingoletteUser(res.lingoletteId);\n        } else {\n          this.step = 1;\n        }\n      });\n    }\n    removeLingoletteUser(userId) {\n      this.subs.add(this.lingoletteService.removeLingoletteUser(userId).pipe(tap(res => {\n        console.log(res);\n        this.step = 1;\n      }), catchError(err => {\n        console.log(err);\n        this.handleError(err);\n        return EMPTY;\n      })).subscribe());\n    }\n    setIframeHeight() {\n      this.subs.sink = this.generalService.deviceKind.pipe(take(2), switchMap(res => {\n        console.log(res);\n        if (res.is576 || res.is992) {\n          return of(0);\n        } else {\n          return this.layoutService.sideMenuHeight;\n        }\n      }), startWith(0)).subscribe(height => {\n        console.log(window.innerHeight);\n        if (height !== 0) {\n          this.galaxyWrapper.nativeElement.style.height = height + \"px\";\n          this.cdr.detectChanges();\n        } else {\n          this.galaxyWrapper.nativeElement.style.height = window.innerHeight - 70 + \"px\";\n          this.cdr.detectChanges();\n        }\n      });\n    }\n    handleIframeLoaded() {\n      console.log('Iframe loaded');\n      // Perform any actions you want to take once the iframe is loaded\n      // For example, you can hide the loading spinner or show a success message\n    }\n    getLingoLanguages() {\n      this.subs.sink = this.teacherApplicationService.getLingoTeachingLanguages().pipe(switchMap(res => {\n        this.mltLanguages = res;\n        return of(null);\n      })).subscribe(res => {\n        if (!res) {\n          return;\n        }\n      }, error => {\n        this.handleError(error);\n      });\n    }\n    onTeacherChange(event) {\n      console.log(event);\n    }\n    onGoToBuyPackage() {\n      this.router.navigate(['/dashboard/buy-package/']);\n    }\n    addLottiePlayer(el) {\n      console.log(el);\n      if (!el) {\n        return;\n      }\n      const lottiePlayer = `<lottie-player src=\"/assets/icons/lottie/ai-chat-2.json\"  \n    background=\"transparent\"  speed=\"1\"  style=\"width: 120px; height: 120px; margin:-8px auto; transform: scale(1.2);\" autoplay loop></lottie-player>`;\n      el.nativeElement.innerHTML = lottiePlayer;\n    }\n    handleError(error) {\n      console.error(error);\n      this.loading = false;\n      this.userService.getUserById(this.authService.getUserId()).subscribe(res => {\n        console.log(res);\n        if (res.lingoletteId !== \"\" && res.lingoletteId !== null) {\n          this.removeLingoletteUser(res.lingoletteId);\n        } else {\n          this.step = 1;\n        }\n      });\n    }\n    static #_ = this.ɵfac = function LingoletteRoomComponent_Factory(t) {\n      return new (t || LingoletteRoomComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(DOCUMENT));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LingoletteRoomComponent,\n      selectors: [[\"app-lingolette-room\"]],\n      viewQuery: function LingoletteRoomComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.galaxyWrapper = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.lottieEl = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 7,\n      vars: 8,\n      consts: [[\"lingoletteWrapper\", \"\"], [\"lottieEl\", \"\"], [\"class\", \"h-17rem md:h-auto w-full flex align-items-center justify-content-center\", 4, \"ngIf\"], [3, \"ngClass\"], [4, \"ngIf\"], [1, \"h-17rem\", \"md:h-auto\", \"w-full\", \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"\"], [3, \"scale\", \"size\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"h-full\", \"flex-column\", \"text-center\"], [1, \"text-center\", \"w-full\"], [2, \"mix-blend-mode\", \"luminosity\"], [1, \"m-0\", \"text-lg\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Enroll Now\", 1, \"p-element\", \"p-ripple\", \"font-bold\", \"mt-2\", \"px-3\", \"py-2\", \"p-button-outlined\", \"p-button-rounded\", \"white-space-nowrap\", \"p-button\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"p-button-icon\", \"p-button-icon-right\"], [1, \"p-button-label\", \"mr-3\"], [1, \"pi\", \"pi-arrow-right\"], [1, \"w-full\"], [1, \"w-full\", \"flex\", \"m-auto\", \"flex-column\", \"lg:flex-row\", \"justify-content-center\", \"align-items-center\", \"gap-7\"], [1, \"text-center\", \"lg:text-center\"], [1, \"mt-0\", \"mb-3\", \"font-bold\", \"text-2xl\", \"text-primary\", \"text-center\"], [1, \"text-700\", \"text-md\", \"mt-0\", \"mb-2\"], [1, \"p-fluid\", \"w-full\"], [1, \"field\", \"mb-0\"], [\"htmlfor\", \"name1\"], [\"optionLabel\", \"label\", \"placeholder\", \"Select language \", \"appendTo\", \"body\", \"styleClass\", \"w-full dropdown-blue rounded\", 1, \"col-12\", \"dropdown-blue\", 3, \"ngModelChange\", \"options\", \"ngModel\"], [\"frameborder\", \"0\", \"width\", \"100%\", \"height\", \"100%\", \"allow\", \"camera;microphone\", \"class\", \"border-round-xl\", 3, \"src\", \"load\", 4, \"ngIf\"], [\"frameborder\", \"0\", \"width\", \"100%\", \"height\", \"100%\", \"allow\", \"camera;microphone\", 1, \"border-round-xl\", 3, \"load\", \"src\"]],\n      template: function LingoletteRoomComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, LingoletteRoomComponent_div_0_Template, 3, 2, \"div\", 2);\n          i0.ɵɵelementStart(1, \"div\", 3, 0);\n          i0.ɵɵtemplate(3, LingoletteRoomComponent_ng_container_3_Template, 12, 0, \"ng-container\", 4)(4, LingoletteRoomComponent_ng_container_4_Template, 9, 0, \"ng-container\", 4)(5, LingoletteRoomComponent_ng_container_5_Template, 26, 4, \"ng-container\", 4)(6, LingoletteRoomComponent_ng_container_6_Template, 2, 1, \"ng-container\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c2, ctx.step === 1));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isUserPremiumOrFlex && !ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.userAddError && ctx.step === 0 && ctx.isUserPremiumOrFlex);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.step === 1 && ctx.isUserPremiumOrFlex);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.step === 2 && ctx.isUserPremiumOrFlex);\n        }\n      },\n      dependencies: [CommonModule, i2.NgClass, i2.NgIf, FormsModule, i3.NgControlStatus, i3.NgModel, LoaderComponent, DropdownModule, i4.Dropdown],\n      styles: [\"[_nghost-%COMP%]{display:block}\\n\\n/*# sourceMappingURL=lingolette-room.component.css.map*/\"]\n    });\n  }\n  return LingoletteRoomComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "DOCUMENT", "ChangeDetectorRef", "inject", "FormsModule", "Dom<PERSON><PERSON><PERSON>zer", "Router", "DropdownModule", "EMPTY", "fork<PERSON><PERSON>n", "of", "catchError", "startWith", "switchMap", "take", "tap", "AuthService", "GeneralService", "LingoletteService", "PackageService", "TeacherApplicationService", "UserService", "LoaderComponent", "SubSink", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵlistener", "LingoletteRoomComponent_ng_container_3_Template_button_click_7_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onGoToBuyPackage", "LingoletteRoomComponent_ng_container_4_Template_button_click_4_listener", "_r3", "onClickRemoveLingoletteUser", "ɵɵtwoWayListener", "LingoletteRoomComponent_ng_container_5_Template_p_dropdown_ngModelChange_14_listener", "$event", "_r4", "ɵɵtwoWayBindingSet", "selectedLanguage", "onTeacher<PERSON><PERSON>e", "LingoletteRoomComponent_ng_container_5_Template_p_dropdown_ngModelChange_19_listener", "selectedLevel", "LingoletteRoomComponent_ng_container_5_Template_button_click_20_listener", "createLingoletteUser", "languageOptions", "ɵɵtwoWayProperty", "languageLevels", "LingoletteRoomComponent_ng_container_6_iframe_1_Template_iframe_load_0_listener", "_r5", "handleIframeLoaded", "url", "ɵɵsanitizeResourceUrl", "ɵɵtemplate", "LingoletteRoomComponent_ng_container_6_iframe_1_Template", "showIframe", "LingoletteRoomComponent", "constructor", "layoutService", "renderer", "document", "subs", "lingoletteService", "authService", "packageService", "generalService", "userService", "sanitizer", "router", "teacherApplicationService", "loading", "userAddError", "user", "token", "cdr", "galaxyWrapper", "<PERSON><PERSON><PERSON><PERSON>", "languageCodeMap", "label", "value", "step", "isUserPremiumOrFlex", "ngOnInit", "Object", "keys", "map", "key", "getLoggedInUser", "getLingoLanguages", "setIframeHeight", "getAllUserPackages", "getUserId", "pipe", "res", "handlePackages", "getLingoletteUserList", "console", "log", "err", "error", "subscribe", "ngOnDestroy", "unsubscribe", "packages", "matchingPackage", "find", "pkg", "packLanguage", "some", "opt", "matchingLevel", "packageLevel", "handleLingoletteInit", "userIsOnFlexOrPremiumPackage", "setTimeout", "addLottiePlayer", "add", "getUserById", "lingoletteId", "getLingoletteToken", "createLingoletteUserSession", "data", "bypassSecurityTrustResourceUrl", "handleError", "userData", "getPersonFullName", "next", "id", "removeLingoletteUser", "userId", "sink", "deviceKind", "is576", "is992", "sideMenuHeight", "height", "window", "innerHeight", "nativeElement", "style", "detectChanges", "getLingoTeachingLanguages", "mltLanguages", "event", "navigate", "el", "lottiePlayer", "innerHTML", "_", "ɵɵdirectiveInject", "i1", "LayoutService", "Renderer2", "_2", "selectors", "viewQuery", "LingoletteRoomComponent_Query", "rf", "ctx", "LingoletteRoomComponent_div_0_Template", "LingoletteRoomComponent_ng_container_3_Template", "LingoletteRoomComponent_ng_container_4_Template", "LingoletteRoomComponent_ng_container_5_Template", "LingoletteRoomComponent_ng_container_6_Template", "ɵɵpureFunction1", "_c2", "i2", "Ng<PERSON><PERSON>", "NgIf", "i3", "NgControlStatus", "NgModel", "i4", "Dropdown", "styles"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\dashboard\\lingolette-room\\lingolette-room.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\dashboard\\lingolette-room\\lingolette-room.component.html"], "sourcesContent": ["import { CommonModule, DOCUMENT } from \"@angular/common\";\r\nimport { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, Inject, Renderer2, ViewChild, inject } from '@angular/core';\r\nimport { FormsModule } from \"@angular/forms\";\r\nimport { <PERSON><PERSON>anitizer, SafeResourceUrl } from \"@angular/platform-browser\";\r\nimport { Router } from \"@angular/router\";\r\nimport { SelectItem } from \"primeng/api\";\r\nimport { DropdownModule } from \"primeng/dropdown\";\r\nimport { EMPTY, forkJoin, of } from \"rxjs\";\r\nimport { catchError, finalize, startWith, switchMap, take, tap } from \"rxjs/operators\";\r\nimport { DeviceKind } from \"src/app/core/models/general.model\";\r\nimport { Package, PackageState, PackageType } from \"src/app/core/models/package.model\";\r\nimport { User } from \"src/app/core/models/user.model\";\r\nimport { AuthService } from \"src/app/core/services/auth.service\";\r\nimport { GeneralService } from \"src/app/core/services/general.service\";\r\nimport { LayoutService } from \"src/app/core/services/layout.service\";\r\nimport { LingoletteService } from \"src/app/core/services/lingolette.service\";\r\nimport { PackageService } from \"src/app/core/services/package.service\";\r\nimport { TeacherApplicationService } from \"src/app/core/services/teacher-application.service\";\r\nimport { UserService } from \"src/app/core/services/user.service\";\r\nimport { LoaderComponent } from \"src/app/shared/loader/loader.component\";\r\nimport { SubSink } from \"subsink\";\r\n\r\n@Component({\r\n    selector: 'app-lingolette-room',\r\n    standalone: true,\r\n    imports: [\r\n        CommonModule,\r\n        FormsModule,\r\n        LoaderComponent,\r\n        DropdownModule,\r\n    ],\r\n    templateUrl: './lingolette-room.component.html',\r\n    styleUrls: ['./lingolette-room.component.css'],\r\n})\r\nexport class LingoletteRoomComponent {\r\n    subs = new SubSink();\r\n    lingoletteService = inject(LingoletteService);\r\n    authService = inject(AuthService);\r\n    packageService = inject(PackageService);\r\n    generalService = inject(GeneralService);\r\n    userService = inject(UserService);\r\n    sanitizer = inject(DomSanitizer);\r\n    router = inject(Router);\r\n    teacherApplicationService = inject(TeacherApplicationService);\r\n    loading = true;\r\n    showIframe = false;\r\n    userAddError = false;\r\n    user = {} as User;\r\n    token = null;\r\n    cdr = inject(ChangeDetectorRef);\r\n    @ViewChild(\"lingoletteWrapper\", { static: true }) galaxyWrapper: ElementRef =\r\n        {} as ElementRef;\r\n\r\n    @ViewChild('lottieEl') lottieEl: ElementRef = {} as ElementRef;\r\n    mltLanguages: any;\r\n    languageCodeMap: { [key: string]: string } = {\r\n        \"English\": \"en\",\r\n        \"French\": \"fr\",\r\n        \"German\": \"de\",\r\n        \"Italian\": \"it\",\r\n        \"Spanish\": \"es\",\r\n        \"Swedish\": \"sv\",\r\n        \"Dutch\": \"nl\",\r\n        \"Russian\": \"ru\",\r\n        \"Japanese\": \"ja\",\r\n        \"Korean\": \"ko\",\r\n        \"Chinese\": \"zh\"\r\n    };\r\n\r\n    languageLevels: { label: any; value: number; }[] = [\r\n        { label: 'I don\\'t know', value: 0 },\r\n        { label: 'Absolute Beginner', value: 7 },\r\n        { label: 'A1', value: 1 },\r\n        { label: 'A2', value: 2 },\r\n        { label: 'B1', value: 3 },\r\n        { label: 'B2', value: 4 },\r\n        { label: 'C1', value: 5 },\r\n        { label: 'C2', value: 6 }\r\n    ];\r\n\r\n    step = 0;\r\n    languageOptions: { label: any; value: string; }[] = [];\r\n    selectedLanguage: { label: any; value: string; } = { label: 'English', value: 'en' };\r\n    selectedLevel: { label: any; value: number; } = this.languageLevels[1];\r\n    url: SafeResourceUrl = {} as SafeResourceUrl;\r\n    isUserPremiumOrFlex = false;\r\n\r\n    constructor(private layoutService: LayoutService,\r\n        private renderer: Renderer2,\r\n        @Inject(DOCUMENT) private document: Document,\r\n    ) { }\r\n    ngOnInit() {\r\n\r\n        this.languageOptions = Object.keys(this.languageCodeMap).map(key => ({\r\n            label: key,\r\n            value: this.languageCodeMap[key]\r\n        }));\r\n        this.selectedLanguage = this.languageOptions[0];\r\n        this.user = this.authService.getLoggedInUser();\r\n        this.getLingoLanguages();\r\n        this.setIframeHeight();\r\n        forkJoin([\r\n            this.packageService.getAllUserPackages(this.authService.getUserId()!).pipe(\r\n                tap(res => {\r\n                    // handle packages\r\n                    this.handlePackages(res);\r\n                })\r\n            ),\r\n            this.lingoletteService.getLingoletteUserList().pipe(\r\n                tap(res => {\r\n                    // handle lingolette user list\r\n                    console.log(res);\r\n                }),\r\n                catchError(err => {\r\n                    console.error(err);\r\n                    return EMPTY;\r\n                })\r\n            )\r\n        ]).subscribe();\r\n    }\r\n\r\n    ngOnDestroy() {\r\n        this.subs.unsubscribe();\r\n    }\r\n\r\n    handlePackages(packages: any[]): void {\r\n        const matchingPackage = packages.find(pkg => pkg.packLanguage && this.languageOptions.some(opt => opt.label === pkg.packLanguage));\r\n        const matchingLevel = packages.find(pkg => pkg.packageLevel && this.languageLevels.some(opt => opt.label === pkg.packageLevel));\r\n\r\n        console.log(matchingPackage)\r\n        if (matchingLevel) {\r\n            this.selectedLevel = {\r\n                label: matchingLevel.packageLevel,\r\n                value: this.languageLevels.find(opt => opt.label === matchingLevel.packageLevel)?.value!\r\n            };\r\n        }\r\n\r\n        if (matchingPackage) {\r\n            this.selectedLanguage = {\r\n                label: matchingPackage.packLanguage,\r\n                value: this.languageCodeMap[matchingPackage.packLanguage]\r\n            };\r\n        }\r\n\r\n        this.handleLingoletteInit();\r\n        console.log(this.packageService.userIsOnFlexOrPremiumPackage(packages));\r\n        this.isUserPremiumOrFlex = this.packageService.userIsOnFlexOrPremiumPackage(packages);\r\n    }\r\n\r\n    handleLingoletteInit() {\r\n        setTimeout(() => {\r\n            this.addLottiePlayer(this.lottieEl);\r\n        }, 600);\r\n\r\n        // if (this.step !== 1) {\r\n        //     return;\r\n        // }\r\n\r\n        // this.createLingoletteUser();\r\n        this.subs.add(\r\n            this.userService.getUserById(this.authService.getUserId()!).pipe(\r\n                take(1),\r\n                tap((res: User) => {\r\n                    console.log(res);\r\n                    if (res.lingoletteId !== \"\" && res.lingoletteId !== null) {\r\n                        this.getLingoletteToken(res.lingoletteId);\r\n                    } else {\r\n                        this.loading = false;\r\n                        this.step = 1;\r\n                    }\r\n                })\r\n            ).subscribe()\r\n        );\r\n    }\r\n\r\n    getLingoletteToken(lingoletteId: string) {\r\n        this.subs.add(\r\n            this.lingoletteService.createLingoletteUserSession(lingoletteId).pipe(\r\n                take(1),\r\n                tap((res: any) => {\r\n                    console.log(res);\r\n                    this.token = res.data.token;\r\n                    setTimeout(() => {\r\n                        this.step = 2;\r\n                        this.showIframe = true;\r\n                        const url = `https://lingolette.com/talk/?token=${this.token}`;\r\n                        this.url = this.sanitizer.bypassSecurityTrustResourceUrl(url);\r\n                    }, 500);\r\n                    this.loading = false;\r\n                }),\r\n                catchError((err) => {\r\n                    console.log(err);\r\n                    this.handleError(err);\r\n                    return EMPTY;\r\n                })\r\n            ).subscribe()\r\n        );\r\n    }\r\n\r\n    createLingoletteUser() {\r\n        const userData = {\r\n            \"name\": this.generalService.getPersonFullName(this.user),\r\n            \"targetLng\": this.selectedLanguage.value,\r\n            \"nativeLng\": \"en\",\r\n            \"languageLevel\": this.selectedLevel.value,\r\n        };\r\n\r\n        this.subs.add(\r\n            this.lingoletteService.createLingoletteUser(userData).subscribe({\r\n                next: (res: any) => {\r\n                    console.log(res);\r\n                    this.getLingoletteToken(res.data.id);\r\n                },\r\n                error: (err) => {\r\n                    console.log(err);\r\n                    this.handleError(err);\r\n                }\r\n            })\r\n        );\r\n    }\r\n\r\n    onClickRemoveLingoletteUser() {\r\n        this.userService.getUserById(this.authService.getUserId()!).subscribe((res: User) => {\r\n            console.log(res);\r\n            if (res.lingoletteId !== \"\" && res.lingoletteId !== null) {\r\n                this.removeLingoletteUser(res.lingoletteId)\r\n            } else {\r\n                this.step = 1;\r\n            }\r\n        });\r\n    }\r\n\r\n    removeLingoletteUser(userId: string) {\r\n        this.subs.add(\r\n            this.lingoletteService.removeLingoletteUser(userId).pipe(\r\n                tap((res: any) => {\r\n                    console.log(res);\r\n                    this.step = 1;\r\n                }),\r\n                catchError((err) => {\r\n                    console.log(err);\r\n                    this.handleError(err);\r\n                    return EMPTY;\r\n                })\r\n            ).subscribe()\r\n        );\r\n    }\r\n\r\n    setIframeHeight() {\r\n\r\n\r\n        this.subs.sink = this.generalService.deviceKind.pipe(\r\n            take(2),\r\n            switchMap((res: DeviceKind) => {\r\n                console.log(res);\r\n                if (res.is576 || res.is992) {\r\n                    return of(0);\r\n                } else {\r\n                    return this.layoutService.sideMenuHeight;\r\n                }\r\n            }),\r\n            startWith(0),\r\n        ).subscribe((height: number) => {\r\n            console.log(window.innerHeight);\r\n            if (height !== 0) {\r\n\r\n                this.galaxyWrapper.nativeElement.style.height = height + \"px\";\r\n                this.cdr.detectChanges();\r\n            } else {\r\n                this.galaxyWrapper.nativeElement.style.height = window.innerHeight - 70 + \"px\";\r\n                this.cdr.detectChanges();\r\n            }\r\n        });\r\n      \r\n    }\r\n\r\n    handleIframeLoaded() {\r\n        console.log('Iframe loaded');\r\n        // Perform any actions you want to take once the iframe is loaded\r\n        // For example, you can hide the loading spinner or show a success message\r\n    }\r\n\r\n    getLingoLanguages() {\r\n        this.subs.sink = this.teacherApplicationService.getLingoTeachingLanguages().pipe(\r\n            switchMap(res => {\r\n                this.mltLanguages = res;\r\n                return of(null);\r\n            })\r\n        ).subscribe((res) => {\r\n            if (!res) {\r\n                return;\r\n            }\r\n        },\r\n            error => {\r\n                this.handleError(error);\r\n            });\r\n    }\r\n\r\n    onTeacherChange(event: any) {\r\n        console.log(event);\r\n    }\r\n\r\n    onGoToBuyPackage() {\r\n        this.router.navigate(['/dashboard/buy-package/'])\r\n    }\r\n\r\n    private addLottiePlayer(el: ElementRef): void {\r\n        console.log(el);\r\n        if (!el) {\r\n            return;\r\n        }\r\n        const lottiePlayer = `<lottie-player src=\"/assets/icons/lottie/ai-chat-2.json\"  \r\n    background=\"transparent\"  speed=\"1\"  style=\"width: 120px; height: 120px; margin:-8px auto; transform: scale(1.2);\" autoplay loop></lottie-player>`;\r\n        el.nativeElement.innerHTML = lottiePlayer;\r\n    }\r\n\r\n    private handleError(error: any): void {\r\n        console.error(error);\r\n        this.loading = false;\r\n        this.userService.getUserById(this.authService.getUserId()!).subscribe((res: User) => {\r\n            console.log(res);\r\n            if (res.lingoletteId !== \"\" && res.lingoletteId !== null) {\r\n                this.removeLingoletteUser(res.lingoletteId)\r\n            } else {\r\n                this.step = 1;\r\n            }\r\n        });\r\n    }\r\n\r\n}\r\n", "<div class=\"h-17rem md:h-auto w-full flex align-items-center justify-content-center\" *ngIf=\"loading\">\r\n    <div class=\"\">\r\n        <app-loader [scale]=\"1.7\" [size]=\"200\"></app-loader>\r\n    </div>\r\n</div>\r\n\r\n\r\n<div #lingoletteWrapper [ngClass]=\"{'surface-section px-4 py-8 md:px-6 lg:px-4 block-gradient-reverse border-round-3xl\r\nflex align-items-center justify-content-center' : step === 1}\">\r\n\r\n    <ng-container *ngIf=\"!isUserPremiumOrFlex && !loading\">\r\n        <div class=\"flex align-items-center justify-content-center h-full flex-column text-center\">\r\n\r\n            <div class=\"text-center w-full\">\r\n                <div #lottieEl style=\"    mix-blend-mode: luminosity;\"></div>\r\n            </div>\r\n            <h5 class=\"m-0 text-lg\"> Please purchase a Flexible or Premium Package to use our AI-Chat.</h5>\r\n            <button pButton pRipple label=\"Enroll Now\" (click)=\"onGoToBuyPackage()\"\r\n                class=\"p-element p-ripple font-bold mt-2 px-3 py-2 p-button-outlined p-button-rounded white-space-nowrap p-button\">\r\n                <span class=\"p-button-icon p-button-icon-right\" aria-hidden=\"true\"></span>\r\n                <span class=\"p-button-label mr-3 \">Buy a Package</span>\r\n                <i class=\"pi pi-arrow-right\"></i>\r\n            </button>\r\n        </div>\r\n    </ng-container>\r\n\r\n    <ng-container *ngIf=\"userAddError && step === 0 && isUserPremiumOrFlex\">\r\n        <div class=\"flex align-items-center justify-content-center h-full flex-column text-center\">\r\n            <p> There was an error adding your Lingolette account.</p>\r\n            <button pButton pRipple label=\"Enroll Now\" (click)=\"onClickRemoveLingoletteUser()\"\r\n                class=\"p-element p-ripple font-bold mt-2 px-3 py-2 p-button-outlined p-button-rounded white-space-nowrap p-button\">\r\n                <span class=\"p-button-icon p-button-icon-right\" aria-hidden=\"true\"></span>\r\n                <span class=\"p-button-label mr-3 \">Reset your Lingolette account</span>\r\n                <i class=\"pi pi-arrow-right\"></i>\r\n            </button>\r\n        </div>\r\n    </ng-container>\r\n\r\n    <ng-container *ngIf=\"step === 1 && isUserPremiumOrFlex\">\r\n\r\n        <div class=\"w-full\">\r\n            <div class=\"w-full flex m-auto flex-column lg:flex-row justify-content-center align-items-center gap-7\">\r\n                <div class=\"text-center lg:text-center\">\r\n\r\n                    <div #lottieEl style=\"    mix-blend-mode: luminosity;\"></div>\r\n                    <div class=\"mt-0 mb-3 font-bold text-2xl text-primary text-center\">Start Learning</div>\r\n                    <p class=\"text-700 text-md mt-0 mb-2\"></p>\r\n                    <div class=\"flex align-items-center justify-content-center h-full flex-column text-center\">\r\n\r\n\r\n                        <div class=\"p-fluid w-full\">\r\n                            <div class=\"field mb-0\"><label htmlfor=\"name1\">Choose a language to learn</label>\r\n\r\n                                <p-dropdown [options]=\"languageOptions\" optionLabel=\"label\"\r\n                                    [(ngModel)]=\"selectedLanguage\" placeholder=\"Select language \" appendTo=\"body\"\r\n                                    (ngModelChange)=\"onTeacherChange($event)\" class=\"col-12 dropdown-blue\"\r\n                                    styleClass=\"w-full dropdown-blue rounded\"></p-dropdown>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"p-fluid w-full\">\r\n                            <div class=\"field mb-0\"><label htmlfor=\"name1\">Choose a language level</label>\r\n\r\n                                <p-dropdown [options]=\"languageLevels\" optionLabel=\"label\" [(ngModel)]=\"selectedLevel\"\r\n                                    placeholder=\"Select language \" appendTo=\"body\"\r\n                                    (ngModelChange)=\"onTeacherChange($event)\" class=\"col-12 dropdown-blue\"\r\n                                    styleClass=\"w-full dropdown-blue rounded\"></p-dropdown>\r\n                            </div>\r\n                        </div>\r\n\r\n                    </div>\r\n\r\n                    <button pButton pRipple label=\"Enroll Now\" (click)=\"createLingoletteUser()\"\r\n                        class=\"p-element p-ripple font-bold mt-2 px-3 py-2 p-button-outlined p-button-rounded white-space-nowrap p-button\">\r\n                        <span class=\"p-button-icon p-button-icon-right\" aria-hidden=\"true\"></span>\r\n                        <span class=\"p-button-label mr-3 \">Continue to Chat</span>\r\n                        <i class=\"pi pi-arrow-right\"></i>\r\n                    </button>\r\n                </div>\r\n                <div></div>\r\n            </div>\r\n        </div>\r\n\r\n\r\n    </ng-container>\r\n\r\n    <ng-container *ngIf=\"step === 2 && isUserPremiumOrFlex\">\r\n        <iframe *ngIf=\"showIframe\" [src]=\"url\" frameborder=\"0\" width=\"100%\" height=\"100%\" allow=\"camera;microphone\"\r\n            class=\"border-round-xl\" (load)=\"handleIframeLoaded()\"></iframe>\r\n    </ng-container>\r\n</div>"], "mappings": "AAAA,SAASA,YAAY,EAAEC,QAAQ,QAAQ,iBAAiB;AACxD,SAAkCC,iBAAiB,EAAuDC,MAAM,QAAQ,eAAe;AACvI,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAyB,2BAA2B;AACzE,SAASC,MAAM,QAAQ,iBAAiB;AAExC,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,KAAK,EAAEC,QAAQ,EAAEC,EAAE,QAAQ,MAAM;AAC1C,SAASC,UAAU,EAAYC,SAAS,EAAEC,SAAS,EAAEC,IAAI,EAAEC,GAAG,QAAQ,gBAAgB;AAItF,SAASC,WAAW,QAAQ,oCAAoC;AAChE,SAASC,cAAc,QAAQ,uCAAuC;AAEtE,SAASC,iBAAiB,QAAQ,0CAA0C;AAC5E,SAASC,cAAc,QAAQ,uCAAuC;AACtE,SAASC,yBAAyB,QAAQ,mDAAmD;AAC7F,SAASC,WAAW,QAAQ,oCAAoC;AAChE,SAASC,eAAe,QAAQ,wCAAwC;AACxE,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;;ICnB7BC,EADJ,CAAAC,cAAA,aAAqG,aACnF;IACVD,EAAA,CAAAE,SAAA,oBAAoD;IAE5DF,EADI,CAAAG,YAAA,EAAM,EACJ;;;IAFcH,EAAA,CAAAI,SAAA,GAAa;IAACJ,EAAd,CAAAK,UAAA,cAAa,aAAa;;;;;;IAQ1CL,EAAA,CAAAM,uBAAA,GAAuD;IAG/CN,EAFJ,CAAAC,cAAA,aAA2F,aAEvD;IAC5BD,EAAA,CAAAE,SAAA,iBAA6D;IACjEF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAAwB;IAACD,EAAA,CAAAO,MAAA,yEAAiE;IAAAP,EAAA,CAAAG,YAAA,EAAK;IAC/FH,EAAA,CAAAC,cAAA,iBACuH;IAD5ED,EAAA,CAAAQ,UAAA,mBAAAC,wEAAA;MAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASF,MAAA,CAAAG,gBAAA,EAAkB;IAAA,EAAC;IAEnEf,EAAA,CAAAE,SAAA,eAA0E;IAC1EF,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAO,MAAA,qBAAa;IAAAP,EAAA,CAAAG,YAAA,EAAO;IACvDH,EAAA,CAAAE,SAAA,aAAiC;IAEzCF,EADI,CAAAG,YAAA,EAAS,EACP;;;;;;;IAGVH,EAAA,CAAAM,uBAAA,GAAwE;IAEhEN,EADJ,CAAAC,cAAA,aAA2F,QACpF;IAACD,EAAA,CAAAO,MAAA,0DAAkD;IAAAP,EAAA,CAAAG,YAAA,EAAI;IAC1DH,EAAA,CAAAC,cAAA,iBACuH;IAD5ED,EAAA,CAAAQ,UAAA,mBAAAQ,wEAAA;MAAAhB,EAAA,CAAAU,aAAA,CAAAO,GAAA;MAAA,MAAAL,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASF,MAAA,CAAAM,2BAAA,EAA6B;IAAA,EAAC;IAE9ElB,EAAA,CAAAE,SAAA,eAA0E;IAC1EF,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAO,MAAA,oCAA6B;IAAAP,EAAA,CAAAG,YAAA,EAAO;IACvEH,EAAA,CAAAE,SAAA,YAAiC;IAEzCF,EADI,CAAAG,YAAA,EAAS,EACP;;;;;;;IAGVH,EAAA,CAAAM,uBAAA,GAAwD;IAI5CN,EAFR,CAAAC,cAAA,cAAoB,cACwF,cAC5D;IAEpCD,EAAA,CAAAE,SAAA,iBAA6D;IAC7DF,EAAA,CAAAC,cAAA,cAAmE;IAAAD,EAAA,CAAAO,MAAA,qBAAc;IAAAP,EAAA,CAAAG,YAAA,EAAM;IACvFH,EAAA,CAAAE,SAAA,YAA0C;IAKVF,EAJhC,CAAAC,cAAA,aAA2F,eAG3D,eACA,iBAAuB;IAAAD,EAAA,CAAAO,MAAA,kCAA0B;IAAAP,EAAA,CAAAG,YAAA,EAAQ;IAE7EH,EAAA,CAAAC,cAAA,sBAG8C;IAF1CD,EAAA,CAAAmB,gBAAA,2BAAAC,qFAAAC,MAAA;MAAArB,EAAA,CAAAU,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAAb,EAAA,CAAAuB,kBAAA,CAAAX,MAAA,CAAAY,gBAAA,EAAAH,MAAA,MAAAT,MAAA,CAAAY,gBAAA,GAAAH,MAAA;MAAA,OAAArB,EAAA,CAAAc,WAAA,CAAAO,MAAA;IAAA,EAA8B;IAC9BrB,EAAA,CAAAQ,UAAA,2BAAAY,qFAAAC,MAAA;MAAArB,EAAA,CAAAU,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAiBF,MAAA,CAAAa,eAAA,CAAAJ,MAAA,CAAuB;IAAA,EAAC;IAGrDrB,EAFsD,CAAAG,YAAA,EAAa,EACzD,EACJ;IAGsBH,EAD5B,CAAAC,cAAA,eAA4B,eACA,iBAAuB;IAAAD,EAAA,CAAAO,MAAA,+BAAuB;IAAAP,EAAA,CAAAG,YAAA,EAAQ;IAE1EH,EAAA,CAAAC,cAAA,sBAG8C;IAHaD,EAAA,CAAAmB,gBAAA,2BAAAO,qFAAAL,MAAA;MAAArB,EAAA,CAAAU,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAAb,EAAA,CAAAuB,kBAAA,CAAAX,MAAA,CAAAe,aAAA,EAAAN,MAAA,MAAAT,MAAA,CAAAe,aAAA,GAAAN,MAAA;MAAA,OAAArB,EAAA,CAAAc,WAAA,CAAAO,MAAA;IAAA,EAA2B;IAElFrB,EAAA,CAAAQ,UAAA,2BAAAkB,qFAAAL,MAAA;MAAArB,EAAA,CAAAU,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAiBF,MAAA,CAAAa,eAAA,CAAAJ,MAAA,CAAuB;IAAA,EAAC;IAKzDrB,EAJ0D,CAAAG,YAAA,EAAa,EACzD,EACJ,EAEJ;IAENH,EAAA,CAAAC,cAAA,kBACuH;IAD5ED,EAAA,CAAAQ,UAAA,mBAAAoB,yEAAA;MAAA5B,EAAA,CAAAU,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASF,MAAA,CAAAiB,oBAAA,EAAsB;IAAA,EAAC;IAEvE7B,EAAA,CAAAE,SAAA,gBAA0E;IAC1EF,EAAA,CAAAC,cAAA,gBAAmC;IAAAD,EAAA,CAAAO,MAAA,wBAAgB;IAAAP,EAAA,CAAAG,YAAA,EAAO;IAC1DH,EAAA,CAAAE,SAAA,aAAiC;IAEzCF,EADI,CAAAG,YAAA,EAAS,EACP;IACNH,EAAA,CAAAE,SAAA,WAAW;IAEnBF,EADI,CAAAG,YAAA,EAAM,EACJ;;;;;IA5B8BH,EAAA,CAAAI,SAAA,IAA2B;IAA3BJ,EAAA,CAAAK,UAAA,YAAAO,MAAA,CAAAkB,eAAA,CAA2B;IACnC9B,EAAA,CAAA+B,gBAAA,YAAAnB,MAAA,CAAAY,gBAAA,CAA8B;IAStBxB,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,UAAA,YAAAO,MAAA,CAAAoB,cAAA,CAA0B;IAAqBhC,EAAA,CAAA+B,gBAAA,YAAAnB,MAAA,CAAAe,aAAA,CAA2B;;;;;;IAwB9G3B,EAAA,CAAAC,cAAA,iBAC0D;IAA9BD,EAAA,CAAAQ,UAAA,kBAAAyB,gFAAA;MAAAjC,EAAA,CAAAU,aAAA,CAAAwB,GAAA;MAAA,MAAAtB,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAQF,MAAA,CAAAuB,kBAAA,EAAoB;IAAA,EAAC;IAACnC,EAAA,CAAAG,YAAA,EAAS;;;;IADxCH,EAAA,CAAAK,UAAA,QAAAO,MAAA,CAAAwB,GAAA,EAAApC,EAAA,CAAAqC,qBAAA,CAAW;;;;;IAD1CrC,EAAA,CAAAM,uBAAA,GAAwD;IACpDN,EAAA,CAAAsC,UAAA,IAAAC,wDAAA,qBAC0D;;;;;IADjDvC,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAK,UAAA,SAAAO,MAAA,CAAA4B,UAAA,CAAgB;;;ADrDjC,WAAaC,uBAAuB;EAA9B,MAAOA,uBAAuB;IAqDhCC,YAAoBC,aAA4B,EACpCC,QAAmB,EACDC,QAAkB;MAF5B,KAAAF,aAAa,GAAbA,aAAa;MACrB,KAAAC,QAAQ,GAARA,QAAQ;MACU,KAAAC,QAAQ,GAARA,QAAQ;MAtDtC,KAAAC,IAAI,GAAG,IAAI/C,OAAO,EAAE;MACpB,KAAAgD,iBAAiB,GAAGpE,MAAM,CAACe,iBAAiB,CAAC;MAC7C,KAAAsD,WAAW,GAAGrE,MAAM,CAACa,WAAW,CAAC;MACjC,KAAAyD,cAAc,GAAGtE,MAAM,CAACgB,cAAc,CAAC;MACvC,KAAAuD,cAAc,GAAGvE,MAAM,CAACc,cAAc,CAAC;MACvC,KAAA0D,WAAW,GAAGxE,MAAM,CAACkB,WAAW,CAAC;MACjC,KAAAuD,SAAS,GAAGzE,MAAM,CAACE,YAAY,CAAC;MAChC,KAAAwE,MAAM,GAAG1E,MAAM,CAACG,MAAM,CAAC;MACvB,KAAAwE,yBAAyB,GAAG3E,MAAM,CAACiB,yBAAyB,CAAC;MAC7D,KAAA2D,OAAO,GAAG,IAAI;MACd,KAAAf,UAAU,GAAG,KAAK;MAClB,KAAAgB,YAAY,GAAG,KAAK;MACpB,KAAAC,IAAI,GAAG,EAAU;MACjB,KAAAC,KAAK,GAAG,IAAI;MACZ,KAAAC,GAAG,GAAGhF,MAAM,CAACD,iBAAiB,CAAC;MACmB,KAAAkF,aAAa,GAC3D,EAAgB;MAEG,KAAAC,QAAQ,GAAe,EAAgB;MAE9D,KAAAC,eAAe,GAA8B;QACzC,SAAS,EAAE,IAAI;QACf,QAAQ,EAAE,IAAI;QACd,QAAQ,EAAE,IAAI;QACd,SAAS,EAAE,IAAI;QACf,SAAS,EAAE,IAAI;QACf,SAAS,EAAE,IAAI;QACf,OAAO,EAAE,IAAI;QACb,SAAS,EAAE,IAAI;QACf,UAAU,EAAE,IAAI;QAChB,QAAQ,EAAE,IAAI;QACd,SAAS,EAAE;OACd;MAED,KAAA9B,cAAc,GAAqC,CAC/C;QAAE+B,KAAK,EAAE,eAAe;QAAEC,KAAK,EAAE;MAAC,CAAE,EACpC;QAAED,KAAK,EAAE,mBAAmB;QAAEC,KAAK,EAAE;MAAC,CAAE,EACxC;QAAED,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAC,CAAE,EACzB;QAAED,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAC,CAAE,EACzB;QAAED,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAC,CAAE,EACzB;QAAED,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAC,CAAE,EACzB;QAAED,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAC,CAAE,EACzB;QAAED,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE;MAAC,CAAE,CAC5B;MAED,KAAAC,IAAI,GAAG,CAAC;MACR,KAAAnC,eAAe,GAAqC,EAAE;MACtD,KAAAN,gBAAgB,GAAmC;QAAEuC,KAAK,EAAE,SAAS;QAAEC,KAAK,EAAE;MAAI,CAAE;MACpF,KAAArC,aAAa,GAAmC,IAAI,CAACK,cAAc,CAAC,CAAC,CAAC;MACtE,KAAAI,GAAG,GAAoB,EAAqB;MAC5C,KAAA8B,mBAAmB,GAAG,KAAK;IAKvB;IACJC,QAAQA,CAAA;MAEJ,IAAI,CAACrC,eAAe,GAAGsC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACP,eAAe,CAAC,CAACQ,GAAG,CAACC,GAAG,KAAK;QACjER,KAAK,EAAEQ,GAAG;QACVP,KAAK,EAAE,IAAI,CAACF,eAAe,CAACS,GAAG;OAClC,CAAC,CAAC;MACH,IAAI,CAAC/C,gBAAgB,GAAG,IAAI,CAACM,eAAe,CAAC,CAAC,CAAC;MAC/C,IAAI,CAAC2B,IAAI,GAAG,IAAI,CAACT,WAAW,CAACwB,eAAe,EAAE;MAC9C,IAAI,CAACC,iBAAiB,EAAE;MACxB,IAAI,CAACC,eAAe,EAAE;MACtBzF,QAAQ,CAAC,CACL,IAAI,CAACgE,cAAc,CAAC0B,kBAAkB,CAAC,IAAI,CAAC3B,WAAW,CAAC4B,SAAS,EAAG,CAAC,CAACC,IAAI,CACtEtF,GAAG,CAACuF,GAAG,IAAG;QACN;QACA,IAAI,CAACC,cAAc,CAACD,GAAG,CAAC;MAC5B,CAAC,CAAC,CACL,EACD,IAAI,CAAC/B,iBAAiB,CAACiC,qBAAqB,EAAE,CAACH,IAAI,CAC/CtF,GAAG,CAACuF,GAAG,IAAG;QACN;QACAG,OAAO,CAACC,GAAG,CAACJ,GAAG,CAAC;MACpB,CAAC,CAAC,EACF3F,UAAU,CAACgG,GAAG,IAAG;QACbF,OAAO,CAACG,KAAK,CAACD,GAAG,CAAC;QAClB,OAAOnG,KAAK;MAChB,CAAC,CAAC,CACL,CACJ,CAAC,CAACqG,SAAS,EAAE;IAClB;IAEAC,WAAWA,CAAA;MACP,IAAI,CAACxC,IAAI,CAACyC,WAAW,EAAE;IAC3B;IAEAR,cAAcA,CAACS,QAAe;MAC1B,MAAMC,eAAe,GAAGD,QAAQ,CAACE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,YAAY,IAAI,IAAI,CAAC9D,eAAe,CAAC+D,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC/B,KAAK,KAAK4B,GAAG,CAACC,YAAY,CAAC,CAAC;MAClI,MAAMG,aAAa,GAAGP,QAAQ,CAACE,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACK,YAAY,IAAI,IAAI,CAAChE,cAAc,CAAC6D,IAAI,CAACC,GAAG,IAAIA,GAAG,CAAC/B,KAAK,KAAK4B,GAAG,CAACK,YAAY,CAAC,CAAC;MAE/Hf,OAAO,CAACC,GAAG,CAACO,eAAe,CAAC;MAC5B,IAAIM,aAAa,EAAE;QACf,IAAI,CAACpE,aAAa,GAAG;UACjBoC,KAAK,EAAEgC,aAAa,CAACC,YAAY;UACjChC,KAAK,EAAE,IAAI,CAAChC,cAAc,CAAC0D,IAAI,CAACI,GAAG,IAAIA,GAAG,CAAC/B,KAAK,KAAKgC,aAAa,CAACC,YAAY,CAAC,EAAEhC;SACrF;MACL;MAEA,IAAIyB,eAAe,EAAE;QACjB,IAAI,CAACjE,gBAAgB,GAAG;UACpBuC,KAAK,EAAE0B,eAAe,CAACG,YAAY;UACnC5B,KAAK,EAAE,IAAI,CAACF,eAAe,CAAC2B,eAAe,CAACG,YAAY;SAC3D;MACL;MAEA,IAAI,CAACK,oBAAoB,EAAE;MAC3BhB,OAAO,CAACC,GAAG,CAAC,IAAI,CAACjC,cAAc,CAACiD,4BAA4B,CAACV,QAAQ,CAAC,CAAC;MACvE,IAAI,CAACtB,mBAAmB,GAAG,IAAI,CAACjB,cAAc,CAACiD,4BAA4B,CAACV,QAAQ,CAAC;IACzF;IAEAS,oBAAoBA,CAAA;MAChBE,UAAU,CAAC,MAAK;QACZ,IAAI,CAACC,eAAe,CAAC,IAAI,CAACvC,QAAQ,CAAC;MACvC,CAAC,EAAE,GAAG,CAAC;MAEP;MACA;MACA;MAEA;MACA,IAAI,CAACf,IAAI,CAACuD,GAAG,CACT,IAAI,CAAClD,WAAW,CAACmD,WAAW,CAAC,IAAI,CAACtD,WAAW,CAAC4B,SAAS,EAAG,CAAC,CAACC,IAAI,CAC5DvF,IAAI,CAAC,CAAC,CAAC,EACPC,GAAG,CAAEuF,GAAS,IAAI;QACdG,OAAO,CAACC,GAAG,CAACJ,GAAG,CAAC;QAChB,IAAIA,GAAG,CAACyB,YAAY,KAAK,EAAE,IAAIzB,GAAG,CAACyB,YAAY,KAAK,IAAI,EAAE;UACtD,IAAI,CAACC,kBAAkB,CAAC1B,GAAG,CAACyB,YAAY,CAAC;QAC7C,CAAC,MAAM;UACH,IAAI,CAAChD,OAAO,GAAG,KAAK;UACpB,IAAI,CAACU,IAAI,GAAG,CAAC;QACjB;MACJ,CAAC,CAAC,CACL,CAACoB,SAAS,EAAE,CAChB;IACL;IAEAmB,kBAAkBA,CAACD,YAAoB;MACnC,IAAI,CAACzD,IAAI,CAACuD,GAAG,CACT,IAAI,CAACtD,iBAAiB,CAAC0D,2BAA2B,CAACF,YAAY,CAAC,CAAC1B,IAAI,CACjEvF,IAAI,CAAC,CAAC,CAAC,EACPC,GAAG,CAAEuF,GAAQ,IAAI;QACbG,OAAO,CAACC,GAAG,CAACJ,GAAG,CAAC;QAChB,IAAI,CAACpB,KAAK,GAAGoB,GAAG,CAAC4B,IAAI,CAAChD,KAAK;QAC3ByC,UAAU,CAAC,MAAK;UACZ,IAAI,CAAClC,IAAI,GAAG,CAAC;UACb,IAAI,CAACzB,UAAU,GAAG,IAAI;UACtB,MAAMJ,GAAG,GAAG,sCAAsC,IAAI,CAACsB,KAAK,EAAE;UAC9D,IAAI,CAACtB,GAAG,GAAG,IAAI,CAACgB,SAAS,CAACuD,8BAA8B,CAACvE,GAAG,CAAC;QACjE,CAAC,EAAE,GAAG,CAAC;QACP,IAAI,CAACmB,OAAO,GAAG,KAAK;MACxB,CAAC,CAAC,EACFpE,UAAU,CAAEgG,GAAG,IAAI;QACfF,OAAO,CAACC,GAAG,CAACC,GAAG,CAAC;QAChB,IAAI,CAACyB,WAAW,CAACzB,GAAG,CAAC;QACrB,OAAOnG,KAAK;MAChB,CAAC,CAAC,CACL,CAACqG,SAAS,EAAE,CAChB;IACL;IAEAxD,oBAAoBA,CAAA;MAChB,MAAMgF,QAAQ,GAAG;QACb,MAAM,EAAE,IAAI,CAAC3D,cAAc,CAAC4D,iBAAiB,CAAC,IAAI,CAACrD,IAAI,CAAC;QACxD,WAAW,EAAE,IAAI,CAACjC,gBAAgB,CAACwC,KAAK;QACxC,WAAW,EAAE,IAAI;QACjB,eAAe,EAAE,IAAI,CAACrC,aAAa,CAACqC;OACvC;MAED,IAAI,CAAClB,IAAI,CAACuD,GAAG,CACT,IAAI,CAACtD,iBAAiB,CAAClB,oBAAoB,CAACgF,QAAQ,CAAC,CAACxB,SAAS,CAAC;QAC5D0B,IAAI,EAAGjC,GAAQ,IAAI;UACfG,OAAO,CAACC,GAAG,CAACJ,GAAG,CAAC;UAChB,IAAI,CAAC0B,kBAAkB,CAAC1B,GAAG,CAAC4B,IAAI,CAACM,EAAE,CAAC;QACxC,CAAC;QACD5B,KAAK,EAAGD,GAAG,IAAI;UACXF,OAAO,CAACC,GAAG,CAACC,GAAG,CAAC;UAChB,IAAI,CAACyB,WAAW,CAACzB,GAAG,CAAC;QACzB;OACH,CAAC,CACL;IACL;IAEAjE,2BAA2BA,CAAA;MACvB,IAAI,CAACiC,WAAW,CAACmD,WAAW,CAAC,IAAI,CAACtD,WAAW,CAAC4B,SAAS,EAAG,CAAC,CAACS,SAAS,CAAEP,GAAS,IAAI;QAChFG,OAAO,CAACC,GAAG,CAACJ,GAAG,CAAC;QAChB,IAAIA,GAAG,CAACyB,YAAY,KAAK,EAAE,IAAIzB,GAAG,CAACyB,YAAY,KAAK,IAAI,EAAE;UACtD,IAAI,CAACU,oBAAoB,CAACnC,GAAG,CAACyB,YAAY,CAAC;QAC/C,CAAC,MAAM;UACH,IAAI,CAACtC,IAAI,GAAG,CAAC;QACjB;MACJ,CAAC,CAAC;IACN;IAEAgD,oBAAoBA,CAACC,MAAc;MAC/B,IAAI,CAACpE,IAAI,CAACuD,GAAG,CACT,IAAI,CAACtD,iBAAiB,CAACkE,oBAAoB,CAACC,MAAM,CAAC,CAACrC,IAAI,CACpDtF,GAAG,CAAEuF,GAAQ,IAAI;QACbG,OAAO,CAACC,GAAG,CAACJ,GAAG,CAAC;QAChB,IAAI,CAACb,IAAI,GAAG,CAAC;MACjB,CAAC,CAAC,EACF9E,UAAU,CAAEgG,GAAG,IAAI;QACfF,OAAO,CAACC,GAAG,CAACC,GAAG,CAAC;QAChB,IAAI,CAACyB,WAAW,CAACzB,GAAG,CAAC;QACrB,OAAOnG,KAAK;MAChB,CAAC,CAAC,CACL,CAACqG,SAAS,EAAE,CAChB;IACL;IAEAX,eAAeA,CAAA;MAGX,IAAI,CAAC5B,IAAI,CAACqE,IAAI,GAAG,IAAI,CAACjE,cAAc,CAACkE,UAAU,CAACvC,IAAI,CAChDvF,IAAI,CAAC,CAAC,CAAC,EACPD,SAAS,CAAEyF,GAAe,IAAI;QAC1BG,OAAO,CAACC,GAAG,CAACJ,GAAG,CAAC;QAChB,IAAIA,GAAG,CAACuC,KAAK,IAAIvC,GAAG,CAACwC,KAAK,EAAE;UACxB,OAAOpI,EAAE,CAAC,CAAC,CAAC;QAChB,CAAC,MAAM;UACH,OAAO,IAAI,CAACyD,aAAa,CAAC4E,cAAc;QAC5C;MACJ,CAAC,CAAC,EACFnI,SAAS,CAAC,CAAC,CAAC,CACf,CAACiG,SAAS,CAAEmC,MAAc,IAAI;QAC3BvC,OAAO,CAACC,GAAG,CAACuC,MAAM,CAACC,WAAW,CAAC;QAC/B,IAAIF,MAAM,KAAK,CAAC,EAAE;UAEd,IAAI,CAAC5D,aAAa,CAAC+D,aAAa,CAACC,KAAK,CAACJ,MAAM,GAAGA,MAAM,GAAG,IAAI;UAC7D,IAAI,CAAC7D,GAAG,CAACkE,aAAa,EAAE;QAC5B,CAAC,MAAM;UACH,IAAI,CAACjE,aAAa,CAAC+D,aAAa,CAACC,KAAK,CAACJ,MAAM,GAAGC,MAAM,CAACC,WAAW,GAAG,EAAE,GAAG,IAAI;UAC9E,IAAI,CAAC/D,GAAG,CAACkE,aAAa,EAAE;QAC5B;MACJ,CAAC,CAAC;IAEN;IAEA1F,kBAAkBA,CAAA;MACd8C,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;MAC5B;MACA;IACJ;IAEAT,iBAAiBA,CAAA;MACb,IAAI,CAAC3B,IAAI,CAACqE,IAAI,GAAG,IAAI,CAAC7D,yBAAyB,CAACwE,yBAAyB,EAAE,CAACjD,IAAI,CAC5ExF,SAAS,CAACyF,GAAG,IAAG;QACZ,IAAI,CAACiD,YAAY,GAAGjD,GAAG;QACvB,OAAO5F,EAAE,CAAC,IAAI,CAAC;MACnB,CAAC,CAAC,CACL,CAACmG,SAAS,CAAEP,GAAG,IAAI;QAChB,IAAI,CAACA,GAAG,EAAE;UACN;QACJ;MACJ,CAAC,EACGM,KAAK,IAAG;QACJ,IAAI,CAACwB,WAAW,CAACxB,KAAK,CAAC;MAC3B,CAAC,CAAC;IACV;IAEA3D,eAAeA,CAACuG,KAAU;MACtB/C,OAAO,CAACC,GAAG,CAAC8C,KAAK,CAAC;IACtB;IAEAjH,gBAAgBA,CAAA;MACZ,IAAI,CAACsC,MAAM,CAAC4E,QAAQ,CAAC,CAAC,yBAAyB,CAAC,CAAC;IACrD;IAEQ7B,eAAeA,CAAC8B,EAAc;MAClCjD,OAAO,CAACC,GAAG,CAACgD,EAAE,CAAC;MACf,IAAI,CAACA,EAAE,EAAE;QACL;MACJ;MACA,MAAMC,YAAY,GAAG;sJACyH;MAC9ID,EAAE,CAACP,aAAa,CAACS,SAAS,GAAGD,YAAY;IAC7C;IAEQvB,WAAWA,CAACxB,KAAU;MAC1BH,OAAO,CAACG,KAAK,CAACA,KAAK,CAAC;MACpB,IAAI,CAAC7B,OAAO,GAAG,KAAK;MACpB,IAAI,CAACJ,WAAW,CAACmD,WAAW,CAAC,IAAI,CAACtD,WAAW,CAAC4B,SAAS,EAAG,CAAC,CAACS,SAAS,CAAEP,GAAS,IAAI;QAChFG,OAAO,CAACC,GAAG,CAACJ,GAAG,CAAC;QAChB,IAAIA,GAAG,CAACyB,YAAY,KAAK,EAAE,IAAIzB,GAAG,CAACyB,YAAY,KAAK,IAAI,EAAE;UACtD,IAAI,CAACU,oBAAoB,CAACnC,GAAG,CAACyB,YAAY,CAAC;QAC/C,CAAC,MAAM;UACH,IAAI,CAACtC,IAAI,GAAG,CAAC;QACjB;MACJ,CAAC,CAAC;IACN;IAAC,QAAAoE,CAAA,G;uBArSQ5F,uBAAuB,EAAAzC,EAAA,CAAAsI,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAxI,EAAA,CAAAsI,iBAAA,CAAAtI,EAAA,CAAAyI,SAAA,GAAAzI,EAAA,CAAAsI,iBAAA,CAuDpB7J,QAAQ;IAAA;IAAA,QAAAiK,EAAA,G;YAvDXjG,uBAAuB;MAAAkG,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;UClCpC9I,EAAA,CAAAsC,UAAA,IAAA0G,sCAAA,iBAAqG;UAOrGhJ,EAAA,CAAAC,cAAA,gBAC+D;UA8E3DD,EA5EA,CAAAsC,UAAA,IAAA2G,+CAAA,2BAAuD,IAAAC,+CAAA,0BAgBiB,IAAAC,+CAAA,2BAYhB,IAAAC,+CAAA,0BAgDA;UAI5DpJ,EAAA,CAAAG,YAAA,EAAM;;;UA1FgFH,EAAA,CAAAK,UAAA,SAAA0I,GAAA,CAAAxF,OAAA,CAAa;UAO3EvD,EAAA,CAAAI,SAAA,EACsC;UADtCJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAqJ,eAAA,IAAAC,GAAA,EAAAP,GAAA,CAAA9E,IAAA,QACsC;UAE3CjE,EAAA,CAAAI,SAAA,GAAsC;UAAtCJ,EAAA,CAAAK,UAAA,UAAA0I,GAAA,CAAA7E,mBAAA,KAAA6E,GAAA,CAAAxF,OAAA,CAAsC;UAgBtCvD,EAAA,CAAAI,SAAA,EAAuD;UAAvDJ,EAAA,CAAAK,UAAA,SAAA0I,GAAA,CAAAvF,YAAA,IAAAuF,GAAA,CAAA9E,IAAA,UAAA8E,GAAA,CAAA7E,mBAAA,CAAuD;UAYvDlE,EAAA,CAAAI,SAAA,EAAuC;UAAvCJ,EAAA,CAAAK,UAAA,SAAA0I,GAAA,CAAA9E,IAAA,UAAA8E,GAAA,CAAA7E,mBAAA,CAAuC;UAgDvClE,EAAA,CAAAI,SAAA,EAAuC;UAAvCJ,EAAA,CAAAK,UAAA,SAAA0I,GAAA,CAAA9E,IAAA,UAAA8E,GAAA,CAAA7E,mBAAA,CAAuC;;;qBD5DlD1F,YAAY,EAAA+K,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ7K,WAAW,EAAA8K,EAAA,CAAAC,eAAA,EAAAD,EAAA,CAAAE,OAAA,EACX9J,eAAe,EACff,cAAc,EAAA8K,EAAA,CAAAC,QAAA;MAAAC,MAAA;IAAA;;SAKTtH,uBAAuB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}