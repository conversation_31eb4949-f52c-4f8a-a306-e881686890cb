{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { take } from 'rxjs/operators';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/classroom.service\";\nimport * as i2 from \"src/app/core/services/homework.service\";\nimport * as i3 from \"src/app/core/services/toast.service\";\nimport * as i4 from \"src/app/core/services/general.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"../../../shared/classrooms-check-list/classrooms-check-list.component\";\nimport * as i8 from \"@angular/material/tooltip\";\nimport * as i9 from \"./homework-task/homework-task.component\";\nconst _c0 = [\"datepicker\"];\nfunction HomeworkFormComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21, 0)(2, \"app-classrooms-check-list\", 22);\n    i0.ɵɵlistener(\"close\", function HomeworkFormComponent_div_19_Template_app_classrooms_check_list_close_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleAssignTo());\n    })(\"checkedList\", function HomeworkFormComponent_div_19_Template_app_classrooms_check_list_checkedList_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCheckedList($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disapear\", false)(\"showBlur\", ctx_r1.edit)(\"classroomUsers\", ctx_r1.classroomUsers)(\"test\", ctx_r1.map)(\"usersAssignedTo\", ctx_r1.usersAssignedTo)(\"already\", ctx_r1.already)(\"classroomsAssignedTo\", ctx_r1.classroomsAssignedTo);\n  }\n}\nfunction HomeworkFormComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1, \"Title is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeworkFormComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1, \" Description is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeworkFormComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵtext(1, \" If you picked students, you need to assign tasks to them. Please press the + button next to the student and/or classroom to assign a task. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeworkFormComponent_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Assign homework to students so you can add tasks to them \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeworkFormComponent_div_43_div_1_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-homework-task\", 32);\n    i0.ɵɵlistener(\"onOpenAddFiles\", function HomeworkFormComponent_div_43_div_1_div_9_div_1_Template_app_homework_task_onOpenAddFiles_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onOpenFiles());\n    })(\"onCloseAddFiles\", function HomeworkFormComponent_div_43_div_1_div_9_div_1_Template_app_homework_task_onCloseAddFiles_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onCloseFiles());\n    })(\"deleteTask\", function HomeworkFormComponent_div_43_div_1_div_9_div_1_Template_app_homework_task_deleteTask_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onDeleteTask($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const task_r6 = ctx.$implicit;\n    const recipient_r4 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"showSubmitButton\", true)(\"assignToClassroom\", recipient_r4.value.classroom)(\"edit\", ctx_r1.edit)(\"task\", task_r6)(\"homework\", ctx_r1.homework);\n  }\n}\nfunction HomeworkFormComponent_div_43_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, HomeworkFormComponent_div_43_div_1_div_9_div_1_Template, 2, 5, \"div\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const classroomTaks_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", classroomTaks_r7);\n  }\n}\nfunction HomeworkFormComponent_div_43_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"div\")(3, \"strong\");\n    i0.ɵɵtext(4, \"Classroom:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 28);\n    i0.ɵɵtext(7, \" Add to classroom \");\n    i0.ɵɵelementStart(8, \"img\", 29);\n    i0.ɵɵlistener(\"click\", function HomeworkFormComponent_div_43_div_1_Template_img_click_8_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const recipient_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addTaskToClassroom(recipient_r4.value.classroom));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(9, HomeworkFormComponent_div_43_div_1_div_9_Template, 2, 1, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const recipient_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getClassroomTitle(recipient_r4.value.classroom), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentClassroomTasks(recipient_r4.value.classroom));\n  }\n}\nfunction HomeworkFormComponent_div_43_div_2_div_9_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-homework-task\", 34);\n    i0.ɵɵlistener(\"onOpenAddFiles\", function HomeworkFormComponent_div_43_div_2_div_9_div_1_Template_app_homework_task_onOpenAddFiles_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onOpenFiles());\n    })(\"onCloseAddFiles\", function HomeworkFormComponent_div_43_div_2_div_9_div_1_Template_app_homework_task_onCloseAddFiles_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onCloseFiles());\n    })(\"deleteTask\", function HomeworkFormComponent_div_43_div_2_div_9_div_1_Template_app_homework_task_deleteTask_1_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onDeleteTask($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const task_r11 = ctx.$implicit;\n    const user_r9 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"showSubmitButton\", true)(\"edit\", ctx_r1.edit)(\"homework\", ctx_r1.homework)(\"task\", task_r11)(\"assignToUser\", user_r9);\n  }\n}\nfunction HomeworkFormComponent_div_43_div_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵtemplate(1, HomeworkFormComponent_div_43_div_2_div_9_div_1_Template, 2, 5, \"div\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const userTaks_r12 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", userTaks_r12);\n  }\n}\nfunction HomeworkFormComponent_div_43_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 33)(2, \"div\")(3, \"strong\");\n    i0.ɵɵtext(4, \"Student:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 28);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementStart(8, \"img\", 29);\n    i0.ɵɵlistener(\"click\", function HomeworkFormComponent_div_43_div_2_Template_img_click_8_listener() {\n      const user_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const recipient_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.addTaskToUser(user_r9, recipient_r4.value.classroom.id));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(9, HomeworkFormComponent_div_43_div_2_div_9_Template, 2, 1, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r9 = ctx.$implicit;\n    const recipient_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\" \", user_r9.firstName, \" \", user_r9.lastName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\" Add to \", user_r9.firstName, \" \", user_r9.lastName, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.getCurrentUserTasks(user_r9, recipient_r4.value.classroom.id));\n  }\n}\nfunction HomeworkFormComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, HomeworkFormComponent_div_43_div_1_Template, 10, 2, \"div\", 24)(2, HomeworkFormComponent_div_43_div_2_Template, 10, 5, \"div\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const recipient_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", recipient_r4.value.users.length == recipient_r4.value.classroom.users.length);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", recipient_r4.value.users);\n  }\n}\nfunction HomeworkFormComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36);\n    i0.ɵɵlistener(\"click\", function HomeworkFormComponent_div_52_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.saveHomework(false, false));\n    });\n    i0.ɵɵtext(2, \" Assign Homework \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 37);\n    i0.ɵɵlistener(\"click\", function HomeworkFormComponent_div_52_Template_div_click_3_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.saveHomework(true, false));\n    });\n    i0.ɵɵtext(4, \" Archive Homework \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matTooltip\", \"Assign homework to the users you picked\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"matTooltip\", \"Archive homework if you want to save it for later\");\n  }\n}\nfunction HomeworkFormComponent_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 38);\n    i0.ɵɵlistener(\"click\", function HomeworkFormComponent_div_53_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.saveHomework(false, true));\n    });\n    i0.ɵɵtext(2, \" Assign to Students \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 39);\n    i0.ɵɵlistener(\"click\", function HomeworkFormComponent_div_53_Template_div_click_3_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.saveHomework(true, false));\n    });\n    i0.ɵɵtext(4, \" Save Changes \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class HomeworkFormComponent {\n  constructor(classroomService, homeworkService, toastService, generalService, renderer) {\n    this.classroomService = classroomService;\n    this.homeworkService = homeworkService;\n    this.toastService = toastService;\n    this.generalService = generalService;\n    this.renderer = renderer;\n    this.subs = new SubSink();\n    this.closeHomeworkForm = new EventEmitter();\n    this.homework = {};\n    this.edit = false;\n    this.createForm = {};\n    this.usersAssignedTo = [];\n    this.already = [];\n    this.classroomsAssignedTo = [];\n    this.now = new Date();\n    this.showUsersList = false;\n    this.usersOfNotWholeClassroomChecked = [];\n    this.tasks = [];\n    this.classroomTasks = [];\n    this.isSubmitted = false;\n    this.tasksError = false;\n    this.classroomUsers = new Map();\n    this.classroomUsersForEdit = new Map();\n    this.map = new Map();\n  }\n  ngOnInit() {\n    this.createForm = new UntypedFormGroup({\n      title: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      }),\n      description: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      }),\n      deadLine: new UntypedFormControl(null, {})\n    });\n    if (this.edit) {\n      let deadline = this.homework.deadLine instanceof String ? new Date(this.homework.deadLine).toISOString().substring(0, 10) : null;\n      this.createForm.controls['title'].setValue(this.homework.title);\n      this.createForm.controls['description'].setValue(this.homework.description);\n      this.createForm.controls['deadLine'].setValue(deadline);\n      this.tasks = this.homework.homeWorkTasks;\n      this.usersAssignedTo = this.homework.assignedTo;\n      Object.assign(this.already, this.usersAssignedTo);\n      let dummyHas = [];\n      for (let task of this.homework.homeWorkTasks) {\n        // TODO BACK WHEN GEORGE ACTIVATES\n        if (task.assignedToClassroom == undefined) {\n          for (let classroom of this.classroomService.sessionUserClassrooms) {\n            for (let user of classroom.users) {\n              if (user.id == task.assignedTo?.id) {\n                task.assignedToClassroom = classroom;\n                if (dummyHas.find(el => el == classroom.id)) {\n                  let users = this.classroomUsersForEdit.get(JSON.stringify(classroom));\n                  if (!users.some(el => el.id == task.assignedTo.id)) users.push(task.assignedTo);\n                  this.classroomUsersForEdit.set(JSON.stringify(classroom), users);\n                } else {\n                  dummyHas.push(classroom.id);\n                  this.classroomUsersForEdit.set(JSON.stringify(classroom), [task.assignedTo]);\n                }\n              }\n            }\n          }\n        }\n        if (task.assignedToClassroom) {\n          // task.assignedToClassroom!.users = []\n          if (dummyHas.find(el => el == task.assignedToClassroom.id)) {\n            let users = this.classroomUsersForEdit.get(JSON.stringify(task.assignedToClassroom));\n            if (!users.some(el => el.id == task.assignedTo.id)) users.push(task.assignedTo);\n            this.classroomUsersForEdit.set(JSON.stringify(task.assignedToClassroom), users);\n          } else {\n            dummyHas.push(task.assignedToClassroom.id);\n            this.classroomUsersForEdit.set(JSON.stringify(task.assignedToClassroom), [task.assignedTo]);\n          }\n          // task.assignedToClassroom!.users = this.classroomUsersForEdit.get(JSON.stringify(task.assignedToClassroom!))!\n        }\n      }\n      for (let [classroom, users] of this.classroomUsersForEdit) {\n        this.classroomUsers.set(JSON.parse(classroom), users);\n      }\n    } else {\n      this.homework = this.homeworkService.initiateHomeworkObject();\n    }\n  }\n  ngOnDestroy() {\n    this.homeworkService.setShowFormListener(false, {});\n    this.subs.unsubscribe();\n  }\n  saveHomework(archive, sendFromArchive) {\n    if (this.usersAssignedTo.length > 0 && !archive || archive) {\n      this.isSubmitted = true;\n      this.homeworkService.setSubmitListener(true);\n      let go = true;\n      if (!archive) {\n        this.homeworkService.isTaskFormValid.subscribe(res => {\n          if (!res && this.classroomUsers.size > 0) {\n            this.tasksError = true;\n            this.toastService.setShowToastmessage({\n              severity: 'info',\n              summary: '',\n              detail: 'Please fullfil all the required fields'\n            });\n            go = false;\n          }\n        });\n      }\n      if (go) {\n        if (this.createForm.invalid) {\n          this.toastService.setShowToastmessage({\n            severity: 'info',\n            summary: '',\n            detail: 'Please fullfil all the required fields'\n          });\n          return;\n        }\n        this.homework.homeWorkTasks = this.tasks;\n        for (let task of this.classroomTasks) {\n          for (let user of task.assignedToClassroom?.users) {\n            let newTask = {};\n            Object.assign(newTask, task);\n            newTask.assignedTo = user;\n            this.homework.homeWorkTasks.push(newTask);\n          }\n        }\n        this.homework.deadLine = this.createForm.value.deadLine;\n        this.homework.assignedTo = this.usersAssignedTo;\n        this.homework.title = this.createForm.value.title;\n        this.homework.description = this.createForm.value.description;\n        this.homework.pinned = false;\n        this.homework.submitted = null;\n        this.homework.status = \"Pending\";\n        this.homework.state = \"Assigned\";\n        if (archive) {\n          this.homework.state = \"Archived\";\n        }\n        if (this.homework.id && !sendFromArchive) {\n          let deadline = this.homework.deadLine instanceof Date ? this.generalService.toIsoString(this.homework.deadLine) : 'No Deadline';\n          let updateHomeworkReq = {\n            id: parseInt(this.homework.id),\n            title: this.homework.title,\n            description: this.homework.description,\n            deadLine: deadline,\n            status: this.homework.status,\n            state: this.homework.state,\n            levels: this.homework.levels,\n            categories: this.homework.categories\n          };\n          for (let task of this.homework.homeWorkTasks) {\n            let libraryFileIds = [];\n            for (let library of task.libraries) {\n              libraryFileIds.push(library.fileId);\n            }\n            let classroomId = task.assignedToClassroom ? task.assignedToClassroom.id : task.assignedToUserInClassroom;\n            let homeworkTaskToSend = {\n              title: task.title,\n              finished: false,\n              assignedTo: task.assignedTo.id,\n              studentFiles: [],\n              teacherFiles: task.teacherFiles,\n              link: task.link,\n              rate: task.rate,\n              proportion: task.proportion,\n              dismissed: false,\n              libraryFileIds: libraryFileIds,\n              classroomId: parseInt(classroomId)\n            };\n            if (task.id) {\n              homeworkTaskToSend.id = task.id;\n              this.homeworkService.updateTaskDetails(homeworkTaskToSend, this.homework.id).pipe(take(1)).subscribe(res => {});\n            } else {\n              this.homeworkService.addTaskToHomework(homeworkTaskToSend, this.homework.id).pipe(take(1)).subscribe(res => {});\n            }\n          }\n          this.homeworkService.updateHomework(updateHomeworkReq).pipe(take(1)).subscribe(res => {\n            this.toastService.setShowToastmessage({\n              severity: 'info',\n              summary: '',\n              detail: 'Homework has been updated'\n            });\n            this.onCloseHomeworkForm();\n          });\n          // put\n        } else {\n          // post\n          if (this.homework.deadLine.length > 0) {\n            this.homework.deadLine;\n          }\n          this.subs.sink = this.homeworkService.createHomework(this.homework).subscribe(res => {\n            this.toastService.setShowToastmessage({\n              severity: 'info',\n              summary: '',\n              detail: 'Homework creation was successfull'\n            });\n            this.onCloseHomeworkForm();\n            this.homeworkService.setUpdateListener(true);\n          });\n        }\n        // this.homeworkService.setLibrariesToAddToHomeworkTask(new Map())\n      }\n    } else {\n      this.toastService.setShowToastmessage({\n        severity: 'info',\n        summary: '',\n        detail: 'Please pick students to assign homework'\n      });\n    }\n  }\n  onCloseHomeworkForm() {\n    this.homeworkService.setShowFormListener(false, {});\n    window.scrollTo(0, 0);\n    this.closeHomeworkForm.emit({});\n  }\n  onCheckedList(event) {\n    this.usersAssignedTo = event.usersChecked;\n    this.already = this.usersAssignedTo;\n    this.classroomsAssignedTo = event.classroomsChecked;\n    this.classroomUsers = event.classroomUsers;\n    this.map = event.map;\n  }\n  checkIfUserExistsToAssignedClassroom(user) {\n    let exists = false;\n    this.classroomUsers.forEach((users, classroom) => {});\n    for (let [classroom, users] of this.classroomUsers) {\n      exists = classroom.users.some(el => {\n        return el.id == user.id;\n      });\n      if (exists) return true;\n    }\n    return false;\n  }\n  toggleAssignTo() {\n    this.showUsersList = !this.showUsersList;\n  }\n  getClassroomTitle(rec) {\n    // console.log(rec)\n    // let users = this.edit? rec.value : rec.key\n    // if(this.edit){\n    //   return this.classroomService.getUsersTitle(users)\n    // }\n    // return this.classroomService.getClassroomTitle(users);\n    return this.classroomService.getClassroomTitle(rec);\n  }\n  addTaskToClassroom(classroom) {\n    let task = this.homeworkService.initiateHomeworkTaskObject();\n    task.assignedToClassroom = classroom;\n    this.classroomTasks.unshift(task);\n  }\n  addTaskToUser(user, classroomId) {\n    let task = this.homeworkService.initiateHomeworkTaskObject();\n    task.assignedTo = user;\n    task.assignedToUserInClassroom = classroomId;\n    this.tasks.unshift(task);\n  }\n  getCurrentUserTasks(user, classroomId) {\n    let userTasks = [];\n    for (let task of this.tasks) {\n      if (!this.edit && task.assignedTo?.id === user.id && task.assignedToUserInClassroom == classroomId && task.assignedToClassroom === undefined || this.edit && task.assignedTo?.id === user.id) {\n        userTasks.push(task);\n      }\n    }\n    return userTasks;\n  }\n  getCurrentClassroomTasks(classroom) {\n    let classroomTasks = [];\n    for (let task of this.classroomTasks) {\n      if (task.assignedToClassroom?.id === classroom.id) {\n        classroomTasks.push(task);\n      }\n    }\n    return classroomTasks;\n  }\n  onDeleteTask(event) {\n    if (event.task.assignedToClassroom == undefined) {\n      let taskIndex = this.tasks.findIndex(el => el.assignedTo == event.task.assignedTo && el.assignedToUserInClassroom == event.task.assignedToUserInClassroom);\n      if (this.tasks[taskIndex].id) {\n        this.homeworkService.deleteTaskFromHomework(this.tasks[taskIndex].id).subscribe(res => {});\n      }\n      this.tasks.splice(taskIndex, 1);\n    } else {\n      let taskIndex = this.classroomTasks.findIndex(el => el.assignedToClassroom?.id == event.task.assignedToClassroom.id);\n      this.classroomTasks.splice(taskIndex, 1);\n    }\n  }\n  onOpenFiles(event) {\n    // this.generalService.slideInElement('blur_bg2')\n    // document.getElementById('blur_bg2')!.style.height = this.el.scrollHeight + 600 + \"px\"\n  }\n  onCloseFiles(event) {\n    // this.generalService.slideOutElement('blur_bg2')\n  }\n  onClose(event) {\n    // event.preventDefault!()\n  }\n  onFocus(args) {\n    this.datepickerObj.show();\n  }\n  get errorControl() {\n    return this.createForm.controls;\n  }\n  static #_ = this.ɵfac = function HomeworkFormComponent_Factory(t) {\n    return new (t || HomeworkFormComponent)(i0.ɵɵdirectiveInject(i1.ClassroomService), i0.ɵɵdirectiveInject(i2.HomeworkService), i0.ɵɵdirectiveInject(i3.ToastService), i0.ɵɵdirectiveInject(i4.GeneralService), i0.ɵɵdirectiveInject(i0.Renderer2));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: HomeworkFormComponent,\n    selectors: [[\"app-homework-form\"]],\n    viewQuery: function HomeworkFormComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.datepickerObj = _t.first);\n      }\n    },\n    inputs: {\n      homework: \"homework\",\n      edit: \"edit\",\n      el: \"el\"\n    },\n    outputs: {\n      closeHomeworkForm: \"closeHomeworkForm\"\n    },\n    decls: 54,\n    vars: 15,\n    consts: [[\"usersList\", \"\"], [1, \"homework-form\"], [1, \"back\", 3, \"click\"], [\"src\", \"/assets/icons/back-main-color.svg\", 2, \"width\", \"15px\", \"cursor\", \"pointer\"], [1, \"link-main-color\"], [1, \"create\", 3, \"formGroup\"], [1, \"field\"], [1, \"title\"], [1, \"assign\", 3, \"click\"], [\"src\", \"/assets/icons/down-arrow.svg\"], [\"class\", \"users-list\", 4, \"ngIf\"], [1, \"input-fields\"], [1, \"input-field-100\"], [\"formControlName\", \"title\", \"type\", \"text\", \"placeholder\", \"Homework title\", 1, \"input-element\"], [\"class\", \"input-error\", 4, \"ngIf\"], [\"placeholder\", \"Description\", \"formControlName\", \"description\", \"rows\", \"3\", 1, \"input-element\"], [4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"input-field-100\", 2, \"width\", \"200px\", \"flex-basis\", \"200px\"], [\"id\", \"birth\", \"formControlName\", \"deadLine\", \"type\", \"date\", 1, \"input-element\"], [\"class\", \"field save-btn btns\", 4, \"ngIf\"], [1, \"users-list\"], [3, \"close\", \"checkedList\", \"disapear\", \"showBlur\", \"classroomUsers\", \"test\", \"usersAssignedTo\", \"already\", \"classroomsAssignedTo\"], [1, \"input-error\"], [\"class\", \"field\", \"style\", \"padding:10px\", 4, \"ngIf\"], [\"class\", \"field\", \"style\", \"padding:10px\", 4, \"ngFor\", \"ngForOf\"], [1, \"field\", 2, \"padding\", \"10px\"], [1, \"title\", \"m-0\", \"class-seperator\"], [1, \"task-btn\"], [\"src\", \"/assets/icons/add.png\", 1, \"add\", \"m-l-10\", \"hvr-grow\", 3, \"click\"], [\"class\", \"p-10\", 4, \"ngIf\"], [1, \"p-10\"], [3, \"onOpenAddFiles\", \"onCloseAddFiles\", \"deleteTask\", \"showSubmitButton\", \"assignToClassroom\", \"edit\", \"task\", \"homework\"], [1, \"title\", \"m-0\", \"p-10\"], [3, \"onOpenAddFiles\", \"onCloseAddFiles\", \"deleteTask\", \"showSubmitButton\", \"edit\", \"homework\", \"task\", \"assignToUser\"], [1, \"field\", \"save-btn\", \"btns\"], [1, \"main-color-button\", 2, \"width\", \"200px\", \"margin\", \"auto\", 3, \"click\", \"matTooltip\"], [1, \"light-purple-button\", 2, \"width\", \"200px\", \"margin\", \"auto\", 3, \"click\", \"matTooltip\"], [1, \"main-color-button\", 2, \"width\", \"200px\", \"margin\", \"auto\", 3, \"click\"], [1, \"light-purple-button\", 2, \"width\", \"200px\", \"margin\", \"auto\", 3, \"click\"]],\n    template: function HomeworkFormComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n        i0.ɵɵlistener(\"click\", function HomeworkFormComponent_Template_div_click_1_listener() {\n          return ctx.onCloseHomeworkForm();\n        });\n        i0.ɵɵelement(2, \"img\", 3);\n        i0.ɵɵelementStart(3, \"div\", 4);\n        i0.ɵɵtext(4, \" Back to homeworks list \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"strong\");\n        i0.ɵɵtext(9);\n        i0.ɵɵpipe(10, \"date\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(11, \"div\", 6)(12, \"div\", 7)(13, \"strong\");\n        i0.ɵɵtext(14, \" Assign to students \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(15, \"div\", 8);\n        i0.ɵɵlistener(\"click\", function HomeworkFormComponent_Template_div_click_15_listener() {\n          return ctx.toggleAssignTo();\n        });\n        i0.ɵɵelementStart(16, \"div\", 4);\n        i0.ɵɵtext(17, \" Pick Students \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(18, \"img\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(19, HomeworkFormComponent_div_19_Template, 3, 7, \"div\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"div\", 6)(21, \"div\", 7)(22, \"strong\");\n        i0.ɵɵtext(23, \" * Title \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(24, \"div\", 11)(25, \"div\", 12);\n        i0.ɵɵelement(26, \"input\", 13);\n        i0.ɵɵtemplate(27, HomeworkFormComponent_div_27_Template, 2, 0, \"div\", 14);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(28, \"div\", 6)(29, \"div\", 7)(30, \"strong\");\n        i0.ɵɵtext(31, \" * Description \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(32, \"div\", 11)(33, \"div\", 12);\n        i0.ɵɵelement(34, \"textarea\", 15);\n        i0.ɵɵtemplate(35, HomeworkFormComponent_div_35_Template, 2, 0, \"div\", 14);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(36, \"div\", 6)(37, \"div\", 7)(38, \"strong\");\n        i0.ɵɵtext(39, \" Tasks \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(40, HomeworkFormComponent_div_40_Template, 2, 0, \"div\", 14);\n        i0.ɵɵelementStart(41, \"div\");\n        i0.ɵɵtemplate(42, HomeworkFormComponent_div_42_Template, 2, 0, \"div\", 16)(43, HomeworkFormComponent_div_43_Template, 3, 2, \"div\", 17);\n        i0.ɵɵpipe(44, \"keyvalue\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(45, \"div\", 6)(46, \"div\", 7)(47, \"strong\");\n        i0.ɵɵtext(48, \" Deadline \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(49, \"div\", 11)(50, \"div\", 18);\n        i0.ɵɵelement(51, \"input\", 19);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(52, HomeworkFormComponent_div_52_Template, 5, 2, \"div\", 20)(53, HomeworkFormComponent_div_53_Template, 5, 0, \"div\", 20);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"formGroup\", ctx.createForm);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate1(\" Created at: \", i0.ɵɵpipeBind2(10, 10, ctx.now, \"dd/MM/yyyy\"), \" \");\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"ngIf\", ctx.showUsersList);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngIf\", (ctx.isSubmitted || ctx.errorControl.title.touched) && (ctx.errorControl.title.errors == null ? null : ctx.errorControl.title.errors.required));\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngIf\", (ctx.isSubmitted || ctx.errorControl.description.touched) && (ctx.errorControl.description.errors == null ? null : ctx.errorControl.description.errors.required));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.tasksError);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.classroomUsers.size == 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind1(44, 13, ctx.classroomUsers));\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngIf\", !ctx.edit);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.edit);\n      }\n    },\n    dependencies: [i5.NgForOf, i5.NgIf, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgControlStatusGroup, i6.FormGroupDirective, i6.FormControlName, i7.ClassroomsCheckListComponent, i8.MatTooltip, i9.HomeworkTaskComponent, i5.DatePipe, i5.KeyValuePipe],\n    styles: [\".homework-form[_ngcontent-%COMP%] {\\n  z-index: 1;\\n  border-radius: 10px;\\n  background-color: var(--white);\\n  width: var(--page-width);\\n  box-sizing: border-box;\\n  font-size: 15px;\\n}\\n.homework-form[_ngcontent-%COMP%]   .create[_ngcontent-%COMP%] {\\n  padding: 30px;\\n}\\n\\n.back[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  position: sticky;\\n  top: 72px;\\n  padding: 30px;\\n  background: white;\\n  border-radius: 10px 10px 0 0;\\n  border-bottom: 1px solid lightgray;\\n}\\n.back[_ngcontent-%COMP%]    > .link-main-color[_ngcontent-%COMP%] {\\n  margin-left: 15px;\\n}\\n\\n.homeo[_ngcontent-%COMP%]   .save-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.field[_ngcontent-%COMP%]    > .title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  margin-top: 25px;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.users-list[_ngcontent-%COMP%] {\\n  width: 310px;\\n  max-height: 400px;\\n  overflow-y: auto;\\n  border-radius: 12px;\\n  margin-top: 15px;\\n  border: 1px solid var(--main-color);\\n  position: absolute;\\n  background: white;\\n}\\n\\n.input-field-100[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n  padding-top: 0;\\n}\\n\\n.assign[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 180px;\\n  justify-content: space-between;\\n  cursor: pointer;\\n  margin-top: 15px;\\n}\\n\\n.task-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-weight: normal;\\n}\\n\\n  .e-input-group:not(.e-float-icon-left),   .e-input-group.e-control-wrapper:not(.e-float-icon-left) {\\n  border: 0px !important;\\n}\\n\\n.save-btn[_ngcontent-%COMP%] {\\n  position: sticky;\\n  bottom: 0;\\n  padding: 20px 0;\\n  background: white;\\n  border-top: 1px solid lightgray;\\n}\\n\\n.class-seperator[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  background: var(--main-color);\\n  color: white;\\n  border-radius: 12px;\\n}\\n\\n.p-10[_ngcontent-%COMP%] {\\n  padding: 10px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "UntypedFormControl", "UntypedFormGroup", "Validators", "take", "SubSink", "i0", "ɵɵelementStart", "ɵɵlistener", "HomeworkFormComponent_div_19_Template_app_classrooms_check_list_close_2_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "toggleAssign<PERSON>o", "HomeworkFormComponent_div_19_Template_app_classrooms_check_list_checkedList_2_listener", "$event", "onCheckedList", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "edit", "classroomUsers", "map", "usersAssignedTo", "already", "classroomsAssignedTo", "ɵɵtext", "HomeworkFormComponent_div_43_div_1_div_9_div_1_Template_app_homework_task_onOpenAddFiles_1_listener", "_r5", "onOpenFiles", "HomeworkFormComponent_div_43_div_1_div_9_div_1_Template_app_homework_task_onCloseAddFiles_1_listener", "onCloseFiles", "HomeworkFormComponent_div_43_div_1_div_9_div_1_Template_app_homework_task_deleteTask_1_listener", "onDeleteTask", "recipient_r4", "value", "classroom", "task_r6", "homework", "ɵɵtemplate", "HomeworkFormComponent_div_43_div_1_div_9_div_1_Template", "classroomTaks_r7", "HomeworkFormComponent_div_43_div_1_Template_img_click_8_listener", "_r3", "$implicit", "addTaskToClassroom", "HomeworkFormComponent_div_43_div_1_div_9_Template", "ɵɵtextInterpolate1", "getClassroomTitle", "getCurrentClassroomTasks", "HomeworkFormComponent_div_43_div_2_div_9_div_1_Template_app_homework_task_onOpenAddFiles_1_listener", "_r10", "HomeworkFormComponent_div_43_div_2_div_9_div_1_Template_app_homework_task_onCloseAddFiles_1_listener", "HomeworkFormComponent_div_43_div_2_div_9_div_1_Template_app_homework_task_deleteTask_1_listener", "task_r11", "user_r9", "HomeworkFormComponent_div_43_div_2_div_9_div_1_Template", "userTaks_r12", "HomeworkFormComponent_div_43_div_2_Template_img_click_8_listener", "_r8", "addTaskToUser", "id", "HomeworkFormComponent_div_43_div_2_div_9_Template", "ɵɵtextInterpolate2", "firstName", "lastName", "getCurrentUserTasks", "HomeworkFormComponent_div_43_div_1_Template", "HomeworkFormComponent_div_43_div_2_Template", "users", "length", "HomeworkFormComponent_div_52_Template_div_click_1_listener", "_r13", "saveHomework", "HomeworkFormComponent_div_52_Template_div_click_3_listener", "HomeworkFormComponent_div_53_Template_div_click_1_listener", "_r14", "HomeworkFormComponent_div_53_Template_div_click_3_listener", "HomeworkFormComponent", "constructor", "classroomService", "homeworkService", "toastService", "generalService", "renderer", "subs", "closeHomeworkForm", "createForm", "now", "Date", "showUsersList", "usersOfNotWholeClassroomChecked", "tasks", "classroomTasks", "isSubmitted", "tasksError", "Map", "classroomUsersForEdit", "ngOnInit", "title", "validators", "required", "description", "deadLine", "deadline", "String", "toISOString", "substring", "controls", "setValue", "homeWorkTasks", "assignedTo", "Object", "assign", "dummy<PERSON>as", "task", "assignedToClassroom", "undefined", "sessionUserClassrooms", "user", "find", "el", "get", "JSON", "stringify", "some", "push", "set", "parse", "initiateHomeworkObject", "ngOnDestroy", "setShowFormListener", "unsubscribe", "archive", "sendFromArchive", "setSubmitListener", "go", "isTaskFormValid", "subscribe", "res", "size", "setShowToastmessage", "severity", "summary", "detail", "invalid", "newTask", "pinned", "submitted", "status", "state", "toIsoString", "updateHomeworkReq", "parseInt", "levels", "categories", "libraryFileIds", "library", "libraries", "fileId", "classroomId", "assignedToUserInClassroom", "homeworkTaskToSend", "finished", "studentFiles", "teacherFiles", "link", "rate", "proportion", "dismissed", "updateTaskDetails", "pipe", "addTaskToHomework", "updateHomework", "onCloseHomeworkForm", "sink", "createHomework", "setUpdateListener", "window", "scrollTo", "emit", "event", "usersChecked", "classroomsChecked", "checkIfUserExistsToAssignedClassroom", "exists", "for<PERSON>ach", "rec", "initiateHomeworkTaskObject", "unshift", "userTasks", "taskIndex", "findIndex", "deleteTaskFromHomework", "splice", "onClose", "onFocus", "args", "datepickerObj", "show", "errorControl", "_", "ɵɵdirectiveInject", "i1", "ClassroomService", "i2", "HomeworkService", "i3", "ToastService", "i4", "GeneralService", "Renderer2", "_2", "selectors", "viewQuery", "HomeworkFormComponent_Query", "rf", "ctx", "HomeworkFormComponent_Template_div_click_1_listener", "ɵɵelement", "HomeworkFormComponent_Template_div_click_15_listener", "HomeworkFormComponent_div_19_Template", "HomeworkFormComponent_div_27_Template", "HomeworkFormComponent_div_35_Template", "HomeworkFormComponent_div_40_Template", "HomeworkFormComponent_div_42_Template", "HomeworkFormComponent_div_43_Template", "HomeworkFormComponent_div_52_Template", "HomeworkFormComponent_div_53_Template", "ɵɵpipeBind2", "touched", "errors", "ɵɵpipeBind1"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\homework\\homework-form\\homework-form.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\homework\\homework-form\\homework-form.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output, Renderer2, ViewChild } from '@angular/core';\r\nimport { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\r\n\r\nimport { take } from 'rxjs/operators';\r\nimport { Classroom } from 'src/app/core/models/classroom.model';\r\nimport { Homework, HomeworkTask, HomeworkTaskToSend, UpdateHomeworkReq } from 'src/app/core/models/homework.model';\r\nimport { Library } from 'src/app/core/models/library.model';\r\nimport { User } from 'src/app/core/models/user.model';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { HomeworkService } from 'src/app/core/services/homework.service';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { SubSink } from 'subsink';\r\n\r\n@Component({\r\n  selector: 'app-homework-form',\r\n  templateUrl: './homework-form.component.html',\r\n  styleUrls: ['./homework-form.component.scss']\r\n})\r\nexport class HomeworkFormComponent implements OnInit {\r\n  private subs = new SubSink();\r\n  @Output() closeHomeworkForm = new EventEmitter<{}>();\r\n  @Input() homework: Homework = {} as Homework;\r\n  @Input() edit: boolean = false;\r\n  @Input() el: any;\r\n  public createForm: UntypedFormGroup = {} as UntypedFormGroup;\r\n  public usersAssignedTo: User[] = [];\r\n  public already: User[] = [];\r\n  public classroomsAssignedTo: Classroom[] = [];\r\n  public now = new Date();\r\n  public showUsersList: boolean = false;\r\n  public usersOfNotWholeClassroomChecked: User[] = [];\r\n  public tasks: HomeworkTask[] = [];\r\n  public classroomTasks: HomeworkTask[] = [];\r\n  public isSubmitted: boolean = false;\r\n  public tasksError: boolean = false;\r\n  classroomUsers: Map<Classroom, User[]> = new Map()\r\n  classroomUsersForEdit: Map<string, User[]> = new Map()\r\n  map: Map<string, User[]> = new Map()\r\n\r\n  constructor(\r\n    private classroomService: ClassroomService,\r\n    private homeworkService: HomeworkService,\r\n    private toastService: ToastService,\r\n    private generalService: GeneralService,\r\n    private renderer: Renderer2\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.createForm = new UntypedFormGroup({\r\n      title: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      description: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      deadLine: new UntypedFormControl(null, {\r\n      })\r\n    })\r\n    if (this.edit) {\r\n      let deadline = this.homework.deadLine instanceof String ? new Date(this.homework.deadLine).toISOString().substring(0, 10) : null\r\n\r\n      this.createForm.controls['title'].setValue(this.homework.title);\r\n      this.createForm.controls['description'].setValue(this.homework.description);\r\n      this.createForm.controls['deadLine'].setValue(deadline);\r\n      this.tasks = this.homework.homeWorkTasks\r\n      this.usersAssignedTo = this.homework.assignedTo\r\n      Object.assign(this.already, this.usersAssignedTo)\r\n\r\n      let dummyHas: string[] = [];\r\n      for (let task of this.homework.homeWorkTasks) { // TODO BACK WHEN GEORGE ACTIVATES\r\n        if(task.assignedToClassroom == undefined){\r\n          for(let classroom of this.classroomService.sessionUserClassrooms){\r\n            for(let user of classroom.users){\r\n              if(user.id==task.assignedTo?.id){\r\n                task.assignedToClassroom = classroom\r\n                if (dummyHas.find(el => el == classroom.id)) {\r\n                  let users = this.classroomUsersForEdit.get(JSON.stringify(classroom))!;\r\n                  if (!users.some(el => el.id == task.assignedTo!.id))\r\n                    users.push(task.assignedTo!)\r\n                  this.classroomUsersForEdit.set(JSON.stringify(classroom), users)\r\n                } else {\r\n                  dummyHas.push(classroom.id)\r\n                  this.classroomUsersForEdit.set(JSON.stringify(classroom), [task.assignedTo!])\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (task.assignedToClassroom) {\r\n          // task.assignedToClassroom!.users = []\r\n          if (dummyHas.find(el => el == task.assignedToClassroom!.id!)) {\r\n            let users = this.classroomUsersForEdit.get(JSON.stringify(task.assignedToClassroom!))!;\r\n            if (!users.some(el => el.id == task.assignedTo!.id))\r\n              users.push(task.assignedTo!)\r\n            this.classroomUsersForEdit.set(JSON.stringify(task.assignedToClassroom!), users)\r\n          } else {\r\n            dummyHas.push(task.assignedToClassroom!.id!)\r\n            this.classroomUsersForEdit.set(JSON.stringify(task.assignedToClassroom!), [task.assignedTo!])\r\n          }\r\n          // task.assignedToClassroom!.users = this.classroomUsersForEdit.get(JSON.stringify(task.assignedToClassroom!))!\r\n        }\r\n      }\r\n      for (let [classroom, users] of this.classroomUsersForEdit) {\r\n        this.classroomUsers.set(JSON.parse(classroom), users)\r\n      }\r\n    } else {\r\n      this.homework = this.homeworkService.initiateHomeworkObject()\r\n    }\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.homeworkService.setShowFormListener(false, {} as Homework)\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n\r\n  saveHomework(archive: boolean, sendFromArchive: boolean) {\r\n    if((this.usersAssignedTo.length > 0 && !archive) || archive){\r\n      this.isSubmitted = true;\r\n      this.homeworkService.setSubmitListener(true)\r\n      let go = true;\r\n      if (!archive) {\r\n        this.homeworkService.isTaskFormValid.subscribe(res => {\r\n          if (!res && this.classroomUsers.size > 0) {\r\n            this.tasksError = true;\r\n            \r\n            this.toastService.setShowToastmessage({\r\n              severity: 'info',\r\n              summary: '',\r\n              detail: 'Please fullfil all the required fields'\r\n            });\r\n            go = false\r\n          }\r\n        })\r\n      }\r\n      if (go) {\r\n        if (this.createForm.invalid) {\r\n          \r\n          this.toastService.setShowToastmessage({\r\n            severity: 'info',\r\n            summary: '',\r\n            detail: 'Please fullfil all the required fields'\r\n          });\r\n          return\r\n        }\r\n        this.homework.homeWorkTasks = this.tasks\r\n        for (let task of this.classroomTasks) {\r\n          for (let user of task.assignedToClassroom?.users!) {\r\n            let newTask = {} as HomeworkTask\r\n            Object.assign(newTask, task);\r\n            newTask.assignedTo = user\r\n            this.homework.homeWorkTasks.push(newTask)\r\n          }\r\n        }\r\n        this.homework.deadLine = this.createForm.value.deadLine;\r\n        this.homework.assignedTo = this.usersAssignedTo\r\n        this.homework.title = this.createForm.value.title\r\n        this.homework.description = this.createForm.value.description\r\n        this.homework.pinned = false\r\n        this.homework.submitted = null\r\n        this.homework.status = \"Pending\"\r\n        this.homework.state = \"Assigned\"\r\n        if (archive) {\r\n          this.homework.state = \"Archived\"\r\n        }\r\n        if (this.homework.id && !sendFromArchive) {\r\n          let deadline = this.homework.deadLine instanceof Date ? this.generalService.toIsoString(this.homework.deadLine!) : 'No Deadline'\r\n  \r\n          let updateHomeworkReq: UpdateHomeworkReq = {\r\n            id: parseInt(this.homework.id),\r\n            title: this.homework.title,\r\n            description: this.homework.description,\r\n            deadLine: deadline,\r\n            status: this.homework.status,\r\n            state: this.homework.state,\r\n            levels: this.homework.levels,\r\n            categories: this.homework.categories\r\n          }\r\n          for (let task of this.homework.homeWorkTasks) {\r\n            let libraryFileIds: number[] = []\r\n            for (let library of task.libraries!) {\r\n              libraryFileIds.push(library.fileId!)\r\n            }\r\n            let classroomId = task.assignedToClassroom ? task.assignedToClassroom.id : task.assignedToUserInClassroom\r\n            let homeworkTaskToSend: HomeworkTaskToSend = {\r\n              title: task.title,\r\n              finished: false,\r\n              assignedTo: task.assignedTo!.id,\r\n              studentFiles: [],\r\n              teacherFiles: task.teacherFiles,\r\n              link: task.link,\r\n              rate: task.rate,\r\n              proportion: task.proportion,\r\n              dismissed: false,\r\n              libraryFileIds: libraryFileIds,\r\n              classroomId: parseInt(classroomId!)\r\n            }\r\n            if (task.id) {\r\n              homeworkTaskToSend.id! = task.id;\r\n              this.homeworkService.updateTaskDetails(homeworkTaskToSend, this.homework.id).pipe(take(1)).subscribe(res => {\r\n              })\r\n            } else {\r\n              this.homeworkService.addTaskToHomework(homeworkTaskToSend, this.homework.id).pipe(take(1)).subscribe(res => {\r\n              })\r\n            }\r\n          }\r\n          this.homeworkService.updateHomework(updateHomeworkReq).pipe(take(1)).subscribe(res => {\r\n            \r\n            this.toastService.setShowToastmessage({\r\n              severity: 'info',\r\n              summary: '',\r\n              detail: 'Homework has been updated'\r\n            });\r\n            this.onCloseHomeworkForm()\r\n          })\r\n          // put\r\n        } else {\r\n          // post\r\n          if((this.homework.deadLine as string).length > 0){\r\n            this.homework.deadLine\r\n          }\r\n          this.subs.sink = this.homeworkService.createHomework(this.homework).subscribe(res => {\r\n               \r\n            this.toastService.setShowToastmessage({\r\n              severity: 'info',\r\n              summary: '',\r\n              detail: 'Homework creation was successfull'\r\n            });\r\n            this.onCloseHomeworkForm()\r\n            this.homeworkService.setUpdateListener(true);\r\n          })\r\n        }\r\n        // this.homeworkService.setLibrariesToAddToHomeworkTask(new Map())\r\n      }\r\n    } else {\r\n      \r\n      this.toastService.setShowToastmessage({\r\n        severity: 'info',\r\n        summary: '',\r\n        detail: 'Please pick students to assign homework'\r\n      });\r\n    }\r\n\r\n  }\r\n\r\n  onCloseHomeworkForm() {\r\n    this.homeworkService.setShowFormListener(false, {} as Homework)\r\n    window.scrollTo(0, 0)\r\n    this.closeHomeworkForm.emit({});\r\n  }\r\n\r\n  onCheckedList(event: any) {\r\n    this.usersAssignedTo = event.usersChecked;\r\n    this.already = this.usersAssignedTo\r\n    this.classroomsAssignedTo = event.classroomsChecked;\r\n    this.classroomUsers = event.classroomUsers\r\n    this.map = event.map;\r\n  }\r\n\r\n  checkIfUserExistsToAssignedClassroom(user: User) {\r\n    let exists = false\r\n    this.classroomUsers.forEach((users: User[], classroom: Classroom) => {\r\n    });\r\n    for (let [classroom, users] of this.classroomUsers) {\r\n      exists = classroom.users.some((el: User) => {\r\n        return el.id == user.id\r\n      })\r\n      if (exists)\r\n        return true;\r\n    }\r\n    return false;\r\n  }\r\n\r\n  toggleAssignTo() {\r\n    this.showUsersList = !this.showUsersList;\r\n  }\r\n\r\n  getClassroomTitle(rec: any) {\r\n    // console.log(rec)\r\n    // let users = this.edit? rec.value : rec.key\r\n    // if(this.edit){\r\n    //   return this.classroomService.getUsersTitle(users)\r\n    // }\r\n    // return this.classroomService.getClassroomTitle(users);\r\n    return this.classroomService.getClassroomTitle(rec);\r\n  }\r\n\r\n  addTaskToClassroom(classroom: Classroom) {\r\n    let task: HomeworkTask = this.homeworkService.initiateHomeworkTaskObject();\r\n    task.assignedToClassroom = classroom;\r\n    this.classroomTasks.unshift(task);\r\n  }\r\n\r\n  addTaskToUser(user: User, classroomId: string) {\r\n    let task: HomeworkTask = this.homeworkService.initiateHomeworkTaskObject();\r\n    task.assignedTo = user;\r\n    task.assignedToUserInClassroom = classroomId;\r\n    this.tasks.unshift(task);\r\n  }\r\n\r\n  getCurrentUserTasks(user: User, classroomId: string) {\r\n    let userTasks: HomeworkTask[] = [];\r\n    for (let task of this.tasks) {\r\n      if ((!this.edit && task.assignedTo?.id === user.id && task.assignedToUserInClassroom == classroomId && task.assignedToClassroom === undefined) || (this.edit && task.assignedTo?.id === user.id)) {\r\n        userTasks.push(task);\r\n      }\r\n    }\r\n    return userTasks;\r\n  }\r\n\r\n  getCurrentClassroomTasks(classroom: Classroom) {\r\n    let classroomTasks: HomeworkTask[] = [];\r\n    for (let task of this.classroomTasks) {\r\n      if (task.assignedToClassroom?.id === classroom.id) {\r\n        classroomTasks.push(task);\r\n      }\r\n    }\r\n    return classroomTasks;\r\n  }\r\n\r\n  onDeleteTask(event: any) {\r\n\r\n    if(event.task.assignedToClassroom == undefined){\r\n      let taskIndex = this.tasks.findIndex(((el: HomeworkTask) => el.assignedTo == event.task.assignedTo && el.assignedToUserInClassroom == event.task.assignedToUserInClassroom));\r\n      if (this.tasks[taskIndex].id) {\r\n        this.homeworkService.deleteTaskFromHomework(this.tasks[taskIndex].id!).subscribe(res => {\r\n        })\r\n      }\r\n      this.tasks.splice(taskIndex, 1);\r\n    } else {\r\n      let taskIndex = this.classroomTasks.findIndex(((el: HomeworkTask) => el.assignedToClassroom?.id == event.task.assignedToClassroom.id));\r\n      this.classroomTasks.splice(taskIndex, 1);\r\n\r\n    }\r\n  }\r\n\r\n  onOpenFiles(event: any) {\r\n    // this.generalService.slideInElement('blur_bg2')\r\n    // document.getElementById('blur_bg2')!.style.height = this.el.scrollHeight + 600 + \"px\"\r\n  }\r\n\r\n  onCloseFiles(event: any) {\r\n    // this.generalService.slideOutElement('blur_bg2')\r\n  }\r\n\r\n  onClose(event: any) {\r\n    // event.preventDefault!()\r\n  }\r\n\r\n  @ViewChild('datepicker')\r\n  public datepickerObj: any;\r\n  onFocus(args: any): void {\r\n    this.datepickerObj.show()\r\n  }\r\n\r\n  get errorControl() {\r\n    return this.createForm.controls\r\n  }\r\n\r\n}\r\n", "<div class=homework-form>\r\n    <div class=\"back\" (click)=\"onCloseHomeworkForm()\">\r\n        <img src=\"/assets/icons/back-main-color.svg\" style=\"width:15px; cursor: pointer\">\r\n        <div class=\"link-main-color\">\r\n            Back to homeworks list\r\n        </div>\r\n    </div>\r\n    <div [formGroup]=\"createForm\" class=\"create\">\r\n        <div class=\"field\">\r\n            <div class=\"title\">\r\n                <strong>\r\n                    Created at: {{now | date: 'dd/MM/yyyy'}}\r\n                </strong>\r\n            </div>\r\n        </div>\r\n        <div class=\"field\">\r\n            <div class=\"title\">\r\n                <strong>\r\n                    Assign to students\r\n                </strong>\r\n            </div>\r\n            <div class=\"assign\" (click)=\"toggleAssignTo()\">\r\n                <div class=\"link-main-color\">\r\n                    Pick Students\r\n                </div>\r\n                <img src=\"/assets/icons/down-arrow.svg\">\r\n            </div>\r\n            <div class=\"users-list\" #usersList *ngIf=\"showUsersList\">\r\n                <app-classrooms-check-list [disapear]=\"false\" [showBlur]=\"edit\" [classroomUsers]=\"classroomUsers\"\r\n                    [test]=\"map\" [usersAssignedTo]=\"usersAssignedTo\" [already]=\"already\"\r\n                    [classroomsAssignedTo]=\"classroomsAssignedTo\" (close)=\"toggleAssignTo()\"\r\n                    (checkedList)=\"onCheckedList($event)\"></app-classrooms-check-list>\r\n            </div>\r\n        </div>\r\n        <div class=\"field\">\r\n            <div class=\"title\">\r\n                <strong>\r\n                    * Title\r\n                </strong>\r\n            </div>\r\n            <div class=\"input-fields\">\r\n                <div class=\"input-field-100\">\r\n                    <input class=\"input-element\" formControlName=\"title\" type=\"text\" placeholder=\"Homework title\">\r\n                    <div *ngIf=\"(isSubmitted || errorControl.title.touched) && errorControl.title.errors?.required\"\r\n                        class=\"input-error\">Title is\r\n                        required</div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"field\">\r\n            <div class=\"title\">\r\n                <strong>\r\n                    * Description\r\n                </strong>\r\n            </div>\r\n            <div class=\"input-fields\">\r\n                <div class=\"input-field-100\">\r\n                    <textarea placeholder=\"Description\" class=\"input-element\" formControlName=\"description\"\r\n                        rows=\"3\"></textarea>\r\n                    <div *ngIf=\"(isSubmitted || errorControl.description.touched) && errorControl.description.errors?.required\"\r\n                        class=\"input-error\">\r\n                        Description\r\n                        is required</div>\r\n\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"field\">\r\n            <div class=\"title\">\r\n                <strong>\r\n                    Tasks\r\n                </strong>\r\n            </div>\r\n            <div *ngIf=\"tasksError\" class=\"input-error\">\r\n                If you picked students, you need to assign tasks to them. Please press the + button next to the student\r\n                and/or classroom to assign a task.\r\n            </div>\r\n            <div>\r\n                <div *ngIf=\"classroomUsers.size == 0\">\r\n                    Assign homework to students so you can add tasks to them\r\n                </div>\r\n                <div *ngFor=\"let recipient of classroomUsers | keyvalue\">\r\n                    <div *ngIf=\"recipient.value.users.length == recipient.value.classroom.users.length\" class=\"field\" style=\"padding:10px\">\r\n                        <div class=\"title m-0 class-seperator\">\r\n                            <div>\r\n                                <strong>Classroom:</strong> {{getClassroomTitle(recipient.value.classroom)}}\r\n                            </div>\r\n                            <div class=\"task-btn\">\r\n                                Add to classroom\r\n                                <img (click)=\"addTaskToClassroom(recipient.value.classroom)\" src=\"/assets/icons/add.png\"\r\n                                    class=\"add m-l-10 hvr-grow\">\r\n                            </div>\r\n                        </div>\r\n                        <div *ngIf=\"getCurrentClassroomTasks(recipient.value.classroom); let classroomTaks\" class=\"p-10\">\r\n                            <div *ngFor=\"let task of classroomTaks\">\r\n                                <app-homework-task [showSubmitButton]=\"true\" [assignToClassroom]=\"recipient.value.classroom\"\r\n                                    (onOpenAddFiles)=\"onOpenFiles()\" (onCloseAddFiles)=\"onCloseFiles()\" [edit]=\"edit\"\r\n                                    [task]=\"task\" [homework]=\"homework\" (deleteTask)=\"onDeleteTask($event)\">\r\n                                </app-homework-task>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div *ngFor=\"let user of recipient.value.users\" class=\"field\" style=\"padding:10px\">\r\n                        <div class=\"title m-0 p-10\">\r\n                            <div>\r\n                                <strong>Student:</strong> {{user.firstName}} {{user.lastName}}\r\n                            </div>\r\n                            <div class=\"task-btn\">\r\n                                Add to {{user.firstName}} {{user.lastName}}\r\n                                <img (click)=\"addTaskToUser(user, recipient.value.classroom.id)\" src=\"/assets/icons/add.png\" class=\"add m-l-10 hvr-grow\">\r\n                            </div>\r\n                        </div>\r\n                        <div *ngIf=\"getCurrentUserTasks(user, recipient.value.classroom.id); let userTaks\" class=\"p-10\">\r\n                            <div *ngFor=\"let task of userTaks\">\r\n                                <app-homework-task [showSubmitButton]=\"true\" (onOpenAddFiles)=\"onOpenFiles()\"\r\n                                    (onCloseAddFiles)=\"onCloseFiles()\" [edit]=\"edit\" [homework]=\"homework\"\r\n                                    (deleteTask)=\"onDeleteTask($event)\" [task]=\"task\" [assignToUser]=\"user\">\r\n                                </app-homework-task>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n            </div>\r\n        </div>\r\n        <div class=\"field\">\r\n            <div class=\"title\">\r\n                <strong>\r\n                    Deadline\r\n                </strong>\r\n            </div>\r\n            <div class=\"input-fields\">\r\n                <div class=\"input-field-100\" style=\"width:200px; flex-basis: 200px\">\r\n                    <input id=\"birth\" class=\"input-element\" formControlName=\"deadLine\" type=\"date\">\r\n\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div *ngIf=\"!edit\" class=\"field save-btn btns\">\r\n            <div [matTooltip]=\"'Assign homework to the users you picked'\" class=\"main-color-button\"\r\n                (click)=\"saveHomework(false, false)\" style=\"width:200px; margin: auto;\">\r\n                Assign Homework\r\n            </div>\r\n            <div [matTooltip]=\"'Archive homework if you want to save it for later'\" class=\"light-purple-button\"\r\n                (click)=\"saveHomework(true, false)\" style=\"width:200px; margin: auto;\">\r\n                Archive Homework\r\n            </div>\r\n        </div>\r\n        <div *ngIf=\"edit\" class=\"field save-btn btns\">\r\n            <div class=\"main-color-button\" (click)=\"saveHomework(false, true)\" style=\"width:200px; margin: auto;\">\r\n                Assign to Students\r\n            </div>\r\n            <div class=\"light-purple-button\" (click)=\"saveHomework(true, false)\" style=\"width:200px; margin: auto;\">\r\n                Save Changes\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": "AAAA,SAAoBA,YAAY,QAAqD,eAAe;AACpG,SAASC,kBAAkB,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,gBAAgB;AAEjF,SAASC,IAAI,QAAQ,gBAAgB;AASrC,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;;;;ICgBjBC,EADJ,CAAAC,cAAA,iBAAyD,oCAIX;IAAtCD,EAD8C,CAAAE,UAAA,mBAAAC,iFAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,cAAA,EAAgB;IAAA,EAAC,yBAAAC,uFAAAC,MAAA;MAAAX,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CACzDF,MAAA,CAAAM,aAAA,CAAAD,MAAA,CAAqB;IAAA,EAAC;IAC7CX,EAD8C,CAAAa,YAAA,EAA4B,EACpE;;;;IAJyBb,EAAA,CAAAc,SAAA,GAAkB;IAEzCd,EAFuB,CAAAe,UAAA,mBAAkB,aAAAT,MAAA,CAAAU,IAAA,CAAkB,mBAAAV,MAAA,CAAAW,cAAA,CAAkC,SAAAX,MAAA,CAAAY,GAAA,CACjF,oBAAAZ,MAAA,CAAAa,eAAA,CAAoC,YAAAb,MAAA,CAAAc,OAAA,CAAoB,yBAAAd,MAAA,CAAAe,oBAAA,CACvB;;;;;IAa7CrB,EAAA,CAAAC,cAAA,cACwB;IAAAD,EAAA,CAAAsB,MAAA,wBACZ;IAAAtB,EAAA,CAAAa,YAAA,EAAM;;;;;IAclBb,EAAA,CAAAC,cAAA,cACwB;IACpBD,EAAA,CAAAsB,MAAA,+BACW;IAAAtB,EAAA,CAAAa,YAAA,EAAM;;;;;IAW7Bb,EAAA,CAAAC,cAAA,cAA4C;IACxCD,EAAA,CAAAsB,MAAA,mJAEJ;IAAAtB,EAAA,CAAAa,YAAA,EAAM;;;;;IAEFb,EAAA,CAAAC,cAAA,UAAsC;IAClCD,EAAA,CAAAsB,MAAA,iEACJ;IAAAtB,EAAA,CAAAa,YAAA,EAAM;;;;;;IAeUb,EADJ,CAAAC,cAAA,UAAwC,4BAGwC;IAApCD,EADpC,CAAAE,UAAA,4BAAAqB,oGAAA;MAAAvB,EAAA,CAAAI,aAAA,CAAAoB,GAAA;MAAA,MAAAlB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAkBF,MAAA,CAAAmB,WAAA,EAAa;IAAA,EAAC,6BAAAC,qGAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAAoB,GAAA;MAAA,MAAAlB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAoBF,MAAA,CAAAqB,YAAA,EAAc;IAAA,EAAC,wBAAAC,gGAAAjB,MAAA;MAAAX,EAAA,CAAAI,aAAA,CAAAoB,GAAA;MAAA,MAAAlB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CACjBF,MAAA,CAAAuB,YAAA,CAAAlB,MAAA,CAAoB;IAAA,EAAC;IAE/EX,EADI,CAAAa,YAAA,EAAoB,EAClB;;;;;;IAJiBb,EAAA,CAAAc,SAAA,EAAyB;IAE1Bd,EAFC,CAAAe,UAAA,0BAAyB,sBAAAe,YAAA,CAAAC,KAAA,CAAAC,SAAA,CAAgD,SAAA1B,MAAA,CAAAU,IAAA,CACP,SAAAiB,OAAA,CACpE,aAAA3B,MAAA,CAAA4B,QAAA,CAAsB;;;;;IAJ/ClC,EAAA,CAAAC,cAAA,cAAiG;IAC7FD,EAAA,CAAAmC,UAAA,IAAAC,uDAAA,kBAAwC;IAM5CpC,EAAA,CAAAa,YAAA,EAAM;;;;IANoBb,EAAA,CAAAc,SAAA,EAAgB;IAAhBd,EAAA,CAAAe,UAAA,YAAAsB,gBAAA,CAAgB;;;;;;IATlCrC,EAHZ,CAAAC,cAAA,cAAuH,cAC5E,UAC9B,aACO;IAAAD,EAAA,CAAAsB,MAAA,iBAAU;IAAAtB,EAAA,CAAAa,YAAA,EAAS;IAACb,EAAA,CAAAsB,MAAA,GAChC;IAAAtB,EAAA,CAAAa,YAAA,EAAM;IACNb,EAAA,CAAAC,cAAA,cAAsB;IAClBD,EAAA,CAAAsB,MAAA,yBACA;IAAAtB,EAAA,CAAAC,cAAA,cACgC;IAD3BD,EAAA,CAAAE,UAAA,mBAAAoC,iEAAA;MAAAtC,EAAA,CAAAI,aAAA,CAAAmC,GAAA;MAAA,MAAAT,YAAA,GAAA9B,EAAA,CAAAO,aAAA,GAAAiC,SAAA;MAAA,MAAAlC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAmC,kBAAA,CAAAX,YAAA,CAAAC,KAAA,CAAAC,SAAA,CAA6C;IAAA,EAAC;IAGpEhC,EAHQ,CAAAa,YAAA,EACgC,EAC9B,EACJ;IACNb,EAAA,CAAAmC,UAAA,IAAAO,iDAAA,kBAAiG;IAQrG1C,EAAA,CAAAa,YAAA,EAAM;;;;;IAhBkCb,EAAA,CAAAc,SAAA,GAChC;IADgCd,EAAA,CAAA2C,kBAAA,MAAArC,MAAA,CAAAsC,iBAAA,CAAAd,YAAA,CAAAC,KAAA,CAAAC,SAAA,OAChC;IAOEhC,EAAA,CAAAc,SAAA,GAA2D;IAA3Dd,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAuC,wBAAA,CAAAf,YAAA,CAAAC,KAAA,CAAAC,SAAA,EAA2D;;;;;;IAqBzDhC,EADJ,CAAAC,cAAA,UAAmC,4BAG6C;IAAxED,EAFyC,CAAAE,UAAA,4BAAA4C,oGAAA;MAAA9C,EAAA,CAAAI,aAAA,CAAA2C,IAAA;MAAA,MAAAzC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAkBF,MAAA,CAAAmB,WAAA,EAAa;IAAA,EAAC,6BAAAuB,qGAAA;MAAAhD,EAAA,CAAAI,aAAA,CAAA2C,IAAA;MAAA,MAAAzC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CACtDF,MAAA,CAAAqB,YAAA,EAAc;IAAA,EAAC,wBAAAsB,gGAAAtC,MAAA;MAAAX,EAAA,CAAAI,aAAA,CAAA2C,IAAA;MAAA,MAAAzC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CACpBF,MAAA,CAAAuB,YAAA,CAAAlB,MAAA,CAAoB;IAAA,EAAC;IAE3CX,EADI,CAAAa,YAAA,EAAoB,EAClB;;;;;;IAJiBb,EAAA,CAAAc,SAAA,EAAyB;IAEUd,EAFnC,CAAAe,UAAA,0BAAyB,SAAAT,MAAA,CAAAU,IAAA,CACQ,aAAAV,MAAA,CAAA4B,QAAA,CAAsB,SAAAgB,QAAA,CACrB,iBAAAC,OAAA,CAAsB;;;;;IAJnFnD,EAAA,CAAAC,cAAA,cAAgG;IAC5FD,EAAA,CAAAmC,UAAA,IAAAiB,uDAAA,kBAAmC;IAMvCpD,EAAA,CAAAa,YAAA,EAAM;;;;IANoBb,EAAA,CAAAc,SAAA,EAAW;IAAXd,EAAA,CAAAe,UAAA,YAAAsC,YAAA,CAAW;;;;;;IAR7BrD,EAHZ,CAAAC,cAAA,cAAmF,cACnD,UACnB,aACO;IAAAD,EAAA,CAAAsB,MAAA,eAAQ;IAAAtB,EAAA,CAAAa,YAAA,EAAS;IAACb,EAAA,CAAAsB,MAAA,GAC9B;IAAAtB,EAAA,CAAAa,YAAA,EAAM;IACNb,EAAA,CAAAC,cAAA,cAAsB;IAClBD,EAAA,CAAAsB,MAAA,GACA;IAAAtB,EAAA,CAAAC,cAAA,cAAyH;IAApHD,EAAA,CAAAE,UAAA,mBAAAoD,iEAAA;MAAA,MAAAH,OAAA,GAAAnD,EAAA,CAAAI,aAAA,CAAAmD,GAAA,EAAAf,SAAA;MAAA,MAAAV,YAAA,GAAA9B,EAAA,CAAAO,aAAA,GAAAiC,SAAA;MAAA,MAAAlC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAkD,aAAA,CAAAL,OAAA,EAAArB,YAAA,CAAAC,KAAA,CAAAC,SAAA,CAAAyB,EAAA,CAAiD;IAAA,EAAC;IAExEzD,EAFQ,CAAAa,YAAA,EAAyH,EACvH,EACJ;IACNb,EAAA,CAAAmC,UAAA,IAAAuB,iDAAA,kBAAgG;IAQpG1D,EAAA,CAAAa,YAAA,EAAM;;;;;;IAfgCb,EAAA,CAAAc,SAAA,GAC9B;IAD8Bd,EAAA,CAAA2D,kBAAA,MAAAR,OAAA,CAAAS,SAAA,OAAAT,OAAA,CAAAU,QAAA,MAC9B;IAEI7D,EAAA,CAAAc,SAAA,GACA;IADAd,EAAA,CAAA2D,kBAAA,aAAAR,OAAA,CAAAS,SAAA,OAAAT,OAAA,CAAAU,QAAA,MACA;IAGF7D,EAAA,CAAAc,SAAA,GAA+D;IAA/Dd,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAwD,mBAAA,CAAAX,OAAA,EAAArB,YAAA,CAAAC,KAAA,CAAAC,SAAA,CAAAyB,EAAA,EAA+D;;;;;IA/B7EzD,EAAA,CAAAC,cAAA,UAAyD;IAqBrDD,EApBA,CAAAmC,UAAA,IAAA4B,2CAAA,mBAAuH,IAAAC,2CAAA,mBAoBpC;IAmBvFhE,EAAA,CAAAa,YAAA,EAAM;;;;IAvCIb,EAAA,CAAAc,SAAA,EAA4E;IAA5Ed,EAAA,CAAAe,UAAA,SAAAe,YAAA,CAAAC,KAAA,CAAAkC,KAAA,CAAAC,MAAA,IAAApC,YAAA,CAAAC,KAAA,CAAAC,SAAA,CAAAiC,KAAA,CAAAC,MAAA,CAA4E;IAoB5DlE,EAAA,CAAAc,SAAA,EAAwB;IAAxBd,EAAA,CAAAe,UAAA,YAAAe,YAAA,CAAAC,KAAA,CAAAkC,KAAA,CAAwB;;;;;;IAqCtDjE,EADJ,CAAAC,cAAA,cAA+C,cAEiC;IAAxED,EAAA,CAAAE,UAAA,mBAAAiE,2DAAA;MAAAnE,EAAA,CAAAI,aAAA,CAAAgE,IAAA;MAAA,MAAA9D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+D,YAAA,CAAa,KAAK,EAAE,KAAK,CAAC;IAAA,EAAC;IACpCrE,EAAA,CAAAsB,MAAA,wBACJ;IAAAtB,EAAA,CAAAa,YAAA,EAAM;IACNb,EAAA,CAAAC,cAAA,cAC2E;IAAvED,EAAA,CAAAE,UAAA,mBAAAoE,2DAAA;MAAAtE,EAAA,CAAAI,aAAA,CAAAgE,IAAA;MAAA,MAAA9D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+D,YAAA,CAAa,IAAI,EAAE,KAAK,CAAC;IAAA,EAAC;IACnCrE,EAAA,CAAAsB,MAAA,yBACJ;IACJtB,EADI,CAAAa,YAAA,EAAM,EACJ;;;IARGb,EAAA,CAAAc,SAAA,EAAwD;IAAxDd,EAAA,CAAAe,UAAA,yDAAwD;IAIxDf,EAAA,CAAAc,SAAA,GAAkE;IAAlEd,EAAA,CAAAe,UAAA,mEAAkE;;;;;;IAMvEf,EADJ,CAAAC,cAAA,cAA8C,cAC4D;IAAvED,EAAA,CAAAE,UAAA,mBAAAqE,2DAAA;MAAAvE,EAAA,CAAAI,aAAA,CAAAoE,IAAA;MAAA,MAAAlE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+D,YAAA,CAAa,KAAK,EAAE,IAAI,CAAC;IAAA,EAAC;IAC9DrE,EAAA,CAAAsB,MAAA,2BACJ;IAAAtB,EAAA,CAAAa,YAAA,EAAM;IACNb,EAAA,CAAAC,cAAA,cAAwG;IAAvED,EAAA,CAAAE,UAAA,mBAAAuE,2DAAA;MAAAzE,EAAA,CAAAI,aAAA,CAAAoE,IAAA;MAAA,MAAAlE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA+D,YAAA,CAAa,IAAI,EAAE,KAAK,CAAC;IAAA,EAAC;IAChErE,EAAA,CAAAsB,MAAA,qBACJ;IACJtB,EADI,CAAAa,YAAA,EAAM,EACJ;;;ADxId,OAAM,MAAO6D,qBAAqB;EAqBhCC,YACUC,gBAAkC,EAClCC,eAAgC,EAChCC,YAA0B,EAC1BC,cAA8B,EAC9BC,QAAmB;IAJnB,KAAAJ,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,QAAQ,GAARA,QAAQ;IAzBV,KAAAC,IAAI,GAAG,IAAIlF,OAAO,EAAE;IAClB,KAAAmF,iBAAiB,GAAG,IAAIxF,YAAY,EAAM;IAC3C,KAAAwC,QAAQ,GAAa,EAAc;IACnC,KAAAlB,IAAI,GAAY,KAAK;IAEvB,KAAAmE,UAAU,GAAqB,EAAsB;IACrD,KAAAhE,eAAe,GAAW,EAAE;IAC5B,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAAC,oBAAoB,GAAgB,EAAE;IACtC,KAAA+D,GAAG,GAAG,IAAIC,IAAI,EAAE;IAChB,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAC,+BAA+B,GAAW,EAAE;IAC5C,KAAAC,KAAK,GAAmB,EAAE;IAC1B,KAAAC,cAAc,GAAmB,EAAE;IACnC,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAAC,UAAU,GAAY,KAAK;IAClC,KAAA1E,cAAc,GAA2B,IAAI2E,GAAG,EAAE;IAClD,KAAAC,qBAAqB,GAAwB,IAAID,GAAG,EAAE;IACtD,KAAA1E,GAAG,GAAwB,IAAI0E,GAAG,EAAE;EAQhC;EAEJE,QAAQA,CAAA;IACN,IAAI,CAACX,UAAU,GAAG,IAAIvF,gBAAgB,CAAC;MACrCmG,KAAK,EAAE,IAAIpG,kBAAkB,CAAC,IAAI,EAAE;QAClCqG,UAAU,EAAE,CAACnG,UAAU,CAACoG,QAAQ;OACjC,CAAC;MACFC,WAAW,EAAE,IAAIvG,kBAAkB,CAAC,IAAI,EAAE;QACxCqG,UAAU,EAAE,CAACnG,UAAU,CAACoG,QAAQ;OACjC,CAAC;MACFE,QAAQ,EAAE,IAAIxG,kBAAkB,CAAC,IAAI,EAAE,EACtC;KACF,CAAC;IACF,IAAI,IAAI,CAACqB,IAAI,EAAE;MACb,IAAIoF,QAAQ,GAAG,IAAI,CAAClE,QAAQ,CAACiE,QAAQ,YAAYE,MAAM,GAAG,IAAIhB,IAAI,CAAC,IAAI,CAACnD,QAAQ,CAACiE,QAAQ,CAAC,CAACG,WAAW,EAAE,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI;MAEhI,IAAI,CAACpB,UAAU,CAACqB,QAAQ,CAAC,OAAO,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACvE,QAAQ,CAAC6D,KAAK,CAAC;MAC/D,IAAI,CAACZ,UAAU,CAACqB,QAAQ,CAAC,aAAa,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACvE,QAAQ,CAACgE,WAAW,CAAC;MAC3E,IAAI,CAACf,UAAU,CAACqB,QAAQ,CAAC,UAAU,CAAC,CAACC,QAAQ,CAACL,QAAQ,CAAC;MACvD,IAAI,CAACZ,KAAK,GAAG,IAAI,CAACtD,QAAQ,CAACwE,aAAa;MACxC,IAAI,CAACvF,eAAe,GAAG,IAAI,CAACe,QAAQ,CAACyE,UAAU;MAC/CC,MAAM,CAACC,MAAM,CAAC,IAAI,CAACzF,OAAO,EAAE,IAAI,CAACD,eAAe,CAAC;MAEjD,IAAI2F,QAAQ,GAAa,EAAE;MAC3B,KAAK,IAAIC,IAAI,IAAI,IAAI,CAAC7E,QAAQ,CAACwE,aAAa,EAAE;QAAE;QAC9C,IAAGK,IAAI,CAACC,mBAAmB,IAAIC,SAAS,EAAC;UACvC,KAAI,IAAIjF,SAAS,IAAI,IAAI,CAAC4C,gBAAgB,CAACsC,qBAAqB,EAAC;YAC/D,KAAI,IAAIC,IAAI,IAAInF,SAAS,CAACiC,KAAK,EAAC;cAC9B,IAAGkD,IAAI,CAAC1D,EAAE,IAAEsD,IAAI,CAACJ,UAAU,EAAElD,EAAE,EAAC;gBAC9BsD,IAAI,CAACC,mBAAmB,GAAGhF,SAAS;gBACpC,IAAI8E,QAAQ,CAACM,IAAI,CAACC,EAAE,IAAIA,EAAE,IAAIrF,SAAS,CAACyB,EAAE,CAAC,EAAE;kBAC3C,IAAIQ,KAAK,GAAG,IAAI,CAAC4B,qBAAqB,CAACyB,GAAG,CAACC,IAAI,CAACC,SAAS,CAACxF,SAAS,CAAC,CAAE;kBACtE,IAAI,CAACiC,KAAK,CAACwD,IAAI,CAACJ,EAAE,IAAIA,EAAE,CAAC5D,EAAE,IAAIsD,IAAI,CAACJ,UAAW,CAAClD,EAAE,CAAC,EACjDQ,KAAK,CAACyD,IAAI,CAACX,IAAI,CAACJ,UAAW,CAAC;kBAC9B,IAAI,CAACd,qBAAqB,CAAC8B,GAAG,CAACJ,IAAI,CAACC,SAAS,CAACxF,SAAS,CAAC,EAAEiC,KAAK,CAAC;gBAClE,CAAC,MAAM;kBACL6C,QAAQ,CAACY,IAAI,CAAC1F,SAAS,CAACyB,EAAE,CAAC;kBAC3B,IAAI,CAACoC,qBAAqB,CAAC8B,GAAG,CAACJ,IAAI,CAACC,SAAS,CAACxF,SAAS,CAAC,EAAE,CAAC+E,IAAI,CAACJ,UAAW,CAAC,CAAC;gBAC/E;cACF;YACF;UACF;QACF;QACA,IAAII,IAAI,CAACC,mBAAmB,EAAE;UAC5B;UACA,IAAIF,QAAQ,CAACM,IAAI,CAACC,EAAE,IAAIA,EAAE,IAAIN,IAAI,CAACC,mBAAoB,CAACvD,EAAG,CAAC,EAAE;YAC5D,IAAIQ,KAAK,GAAG,IAAI,CAAC4B,qBAAqB,CAACyB,GAAG,CAACC,IAAI,CAACC,SAAS,CAACT,IAAI,CAACC,mBAAoB,CAAC,CAAE;YACtF,IAAI,CAAC/C,KAAK,CAACwD,IAAI,CAACJ,EAAE,IAAIA,EAAE,CAAC5D,EAAE,IAAIsD,IAAI,CAACJ,UAAW,CAAClD,EAAE,CAAC,EACjDQ,KAAK,CAACyD,IAAI,CAACX,IAAI,CAACJ,UAAW,CAAC;YAC9B,IAAI,CAACd,qBAAqB,CAAC8B,GAAG,CAACJ,IAAI,CAACC,SAAS,CAACT,IAAI,CAACC,mBAAoB,CAAC,EAAE/C,KAAK,CAAC;UAClF,CAAC,MAAM;YACL6C,QAAQ,CAACY,IAAI,CAACX,IAAI,CAACC,mBAAoB,CAACvD,EAAG,CAAC;YAC5C,IAAI,CAACoC,qBAAqB,CAAC8B,GAAG,CAACJ,IAAI,CAACC,SAAS,CAACT,IAAI,CAACC,mBAAoB,CAAC,EAAE,CAACD,IAAI,CAACJ,UAAW,CAAC,CAAC;UAC/F;UACA;QACF;MACF;MACA,KAAK,IAAI,CAAC3E,SAAS,EAAEiC,KAAK,CAAC,IAAI,IAAI,CAAC4B,qBAAqB,EAAE;QACzD,IAAI,CAAC5E,cAAc,CAAC0G,GAAG,CAACJ,IAAI,CAACK,KAAK,CAAC5F,SAAS,CAAC,EAAEiC,KAAK,CAAC;MACvD;IACF,CAAC,MAAM;MACL,IAAI,CAAC/B,QAAQ,GAAG,IAAI,CAAC2C,eAAe,CAACgD,sBAAsB,EAAE;IAC/D;EACF;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACjD,eAAe,CAACkD,mBAAmB,CAAC,KAAK,EAAE,EAAc,CAAC;IAC/D,IAAI,CAAC9C,IAAI,CAAC+C,WAAW,EAAE;EACzB;EAGA3D,YAAYA,CAAC4D,OAAgB,EAAEC,eAAwB;IACrD,IAAI,IAAI,CAAC/G,eAAe,CAAC+C,MAAM,GAAG,CAAC,IAAI,CAAC+D,OAAO,IAAKA,OAAO,EAAC;MAC1D,IAAI,CAACvC,WAAW,GAAG,IAAI;MACvB,IAAI,CAACb,eAAe,CAACsD,iBAAiB,CAAC,IAAI,CAAC;MAC5C,IAAIC,EAAE,GAAG,IAAI;MACb,IAAI,CAACH,OAAO,EAAE;QACZ,IAAI,CAACpD,eAAe,CAACwD,eAAe,CAACC,SAAS,CAACC,GAAG,IAAG;UACnD,IAAI,CAACA,GAAG,IAAI,IAAI,CAACtH,cAAc,CAACuH,IAAI,GAAG,CAAC,EAAE;YACxC,IAAI,CAAC7C,UAAU,GAAG,IAAI;YAEtB,IAAI,CAACb,YAAY,CAAC2D,mBAAmB,CAAC;cACpCC,QAAQ,EAAE,MAAM;cAChBC,OAAO,EAAE,EAAE;cACXC,MAAM,EAAE;aACT,CAAC;YACFR,EAAE,GAAG,KAAK;UACZ;QACF,CAAC,CAAC;MACJ;MACA,IAAIA,EAAE,EAAE;QACN,IAAI,IAAI,CAACjD,UAAU,CAAC0D,OAAO,EAAE;UAE3B,IAAI,CAAC/D,YAAY,CAAC2D,mBAAmB,CAAC;YACpCC,QAAQ,EAAE,MAAM;YAChBC,OAAO,EAAE,EAAE;YACXC,MAAM,EAAE;WACT,CAAC;UACF;QACF;QACA,IAAI,CAAC1G,QAAQ,CAACwE,aAAa,GAAG,IAAI,CAAClB,KAAK;QACxC,KAAK,IAAIuB,IAAI,IAAI,IAAI,CAACtB,cAAc,EAAE;UACpC,KAAK,IAAI0B,IAAI,IAAIJ,IAAI,CAACC,mBAAmB,EAAE/C,KAAM,EAAE;YACjD,IAAI6E,OAAO,GAAG,EAAkB;YAChClC,MAAM,CAACC,MAAM,CAACiC,OAAO,EAAE/B,IAAI,CAAC;YAC5B+B,OAAO,CAACnC,UAAU,GAAGQ,IAAI;YACzB,IAAI,CAACjF,QAAQ,CAACwE,aAAa,CAACgB,IAAI,CAACoB,OAAO,CAAC;UAC3C;QACF;QACA,IAAI,CAAC5G,QAAQ,CAACiE,QAAQ,GAAG,IAAI,CAAChB,UAAU,CAACpD,KAAK,CAACoE,QAAQ;QACvD,IAAI,CAACjE,QAAQ,CAACyE,UAAU,GAAG,IAAI,CAACxF,eAAe;QAC/C,IAAI,CAACe,QAAQ,CAAC6D,KAAK,GAAG,IAAI,CAACZ,UAAU,CAACpD,KAAK,CAACgE,KAAK;QACjD,IAAI,CAAC7D,QAAQ,CAACgE,WAAW,GAAG,IAAI,CAACf,UAAU,CAACpD,KAAK,CAACmE,WAAW;QAC7D,IAAI,CAAChE,QAAQ,CAAC6G,MAAM,GAAG,KAAK;QAC5B,IAAI,CAAC7G,QAAQ,CAAC8G,SAAS,GAAG,IAAI;QAC9B,IAAI,CAAC9G,QAAQ,CAAC+G,MAAM,GAAG,SAAS;QAChC,IAAI,CAAC/G,QAAQ,CAACgH,KAAK,GAAG,UAAU;QAChC,IAAIjB,OAAO,EAAE;UACX,IAAI,CAAC/F,QAAQ,CAACgH,KAAK,GAAG,UAAU;QAClC;QACA,IAAI,IAAI,CAAChH,QAAQ,CAACuB,EAAE,IAAI,CAACyE,eAAe,EAAE;UACxC,IAAI9B,QAAQ,GAAG,IAAI,CAAClE,QAAQ,CAACiE,QAAQ,YAAYd,IAAI,GAAG,IAAI,CAACN,cAAc,CAACoE,WAAW,CAAC,IAAI,CAACjH,QAAQ,CAACiE,QAAS,CAAC,GAAG,aAAa;UAEhI,IAAIiD,iBAAiB,GAAsB;YACzC3F,EAAE,EAAE4F,QAAQ,CAAC,IAAI,CAACnH,QAAQ,CAACuB,EAAE,CAAC;YAC9BsC,KAAK,EAAE,IAAI,CAAC7D,QAAQ,CAAC6D,KAAK;YAC1BG,WAAW,EAAE,IAAI,CAAChE,QAAQ,CAACgE,WAAW;YACtCC,QAAQ,EAAEC,QAAQ;YAClB6C,MAAM,EAAE,IAAI,CAAC/G,QAAQ,CAAC+G,MAAM;YAC5BC,KAAK,EAAE,IAAI,CAAChH,QAAQ,CAACgH,KAAK;YAC1BI,MAAM,EAAE,IAAI,CAACpH,QAAQ,CAACoH,MAAM;YAC5BC,UAAU,EAAE,IAAI,CAACrH,QAAQ,CAACqH;WAC3B;UACD,KAAK,IAAIxC,IAAI,IAAI,IAAI,CAAC7E,QAAQ,CAACwE,aAAa,EAAE;YAC5C,IAAI8C,cAAc,GAAa,EAAE;YACjC,KAAK,IAAIC,OAAO,IAAI1C,IAAI,CAAC2C,SAAU,EAAE;cACnCF,cAAc,CAAC9B,IAAI,CAAC+B,OAAO,CAACE,MAAO,CAAC;YACtC;YACA,IAAIC,WAAW,GAAG7C,IAAI,CAACC,mBAAmB,GAAGD,IAAI,CAACC,mBAAmB,CAACvD,EAAE,GAAGsD,IAAI,CAAC8C,yBAAyB;YACzG,IAAIC,kBAAkB,GAAuB;cAC3C/D,KAAK,EAAEgB,IAAI,CAAChB,KAAK;cACjBgE,QAAQ,EAAE,KAAK;cACfpD,UAAU,EAAEI,IAAI,CAACJ,UAAW,CAAClD,EAAE;cAC/BuG,YAAY,EAAE,EAAE;cAChBC,YAAY,EAAElD,IAAI,CAACkD,YAAY;cAC/BC,IAAI,EAAEnD,IAAI,CAACmD,IAAI;cACfC,IAAI,EAAEpD,IAAI,CAACoD,IAAI;cACfC,UAAU,EAAErD,IAAI,CAACqD,UAAU;cAC3BC,SAAS,EAAE,KAAK;cAChBb,cAAc,EAAEA,cAAc;cAC9BI,WAAW,EAAEP,QAAQ,CAACO,WAAY;aACnC;YACD,IAAI7C,IAAI,CAACtD,EAAE,EAAE;cACXqG,kBAAkB,CAACrG,EAAG,GAAGsD,IAAI,CAACtD,EAAE;cAChC,IAAI,CAACoB,eAAe,CAACyF,iBAAiB,CAACR,kBAAkB,EAAE,IAAI,CAAC5H,QAAQ,CAACuB,EAAE,CAAC,CAAC8G,IAAI,CAACzK,IAAI,CAAC,CAAC,CAAC,CAAC,CAACwI,SAAS,CAACC,GAAG,IAAG,CAC3G,CAAC,CAAC;YACJ,CAAC,MAAM;cACL,IAAI,CAAC1D,eAAe,CAAC2F,iBAAiB,CAACV,kBAAkB,EAAE,IAAI,CAAC5H,QAAQ,CAACuB,EAAE,CAAC,CAAC8G,IAAI,CAACzK,IAAI,CAAC,CAAC,CAAC,CAAC,CAACwI,SAAS,CAACC,GAAG,IAAG,CAC3G,CAAC,CAAC;YACJ;UACF;UACA,IAAI,CAAC1D,eAAe,CAAC4F,cAAc,CAACrB,iBAAiB,CAAC,CAACmB,IAAI,CAACzK,IAAI,CAAC,CAAC,CAAC,CAAC,CAACwI,SAAS,CAACC,GAAG,IAAG;YAEnF,IAAI,CAACzD,YAAY,CAAC2D,mBAAmB,CAAC;cACpCC,QAAQ,EAAE,MAAM;cAChBC,OAAO,EAAE,EAAE;cACXC,MAAM,EAAE;aACT,CAAC;YACF,IAAI,CAAC8B,mBAAmB,EAAE;UAC5B,CAAC,CAAC;UACF;QACF,CAAC,MAAM;UACL;UACA,IAAI,IAAI,CAACxI,QAAQ,CAACiE,QAAmB,CAACjC,MAAM,GAAG,CAAC,EAAC;YAC/C,IAAI,CAAChC,QAAQ,CAACiE,QAAQ;UACxB;UACA,IAAI,CAAClB,IAAI,CAAC0F,IAAI,GAAG,IAAI,CAAC9F,eAAe,CAAC+F,cAAc,CAAC,IAAI,CAAC1I,QAAQ,CAAC,CAACoG,SAAS,CAACC,GAAG,IAAG;YAElF,IAAI,CAACzD,YAAY,CAAC2D,mBAAmB,CAAC;cACpCC,QAAQ,EAAE,MAAM;cAChBC,OAAO,EAAE,EAAE;cACXC,MAAM,EAAE;aACT,CAAC;YACF,IAAI,CAAC8B,mBAAmB,EAAE;YAC1B,IAAI,CAAC7F,eAAe,CAACgG,iBAAiB,CAAC,IAAI,CAAC;UAC9C,CAAC,CAAC;QACJ;QACA;MACF;IACF,CAAC,MAAM;MAEL,IAAI,CAAC/F,YAAY,CAAC2D,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;IACJ;EAEF;EAEA8B,mBAAmBA,CAAA;IACjB,IAAI,CAAC7F,eAAe,CAACkD,mBAAmB,CAAC,KAAK,EAAE,EAAc,CAAC;IAC/D+C,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IACrB,IAAI,CAAC7F,iBAAiB,CAAC8F,IAAI,CAAC,EAAE,CAAC;EACjC;EAEApK,aAAaA,CAACqK,KAAU;IACtB,IAAI,CAAC9J,eAAe,GAAG8J,KAAK,CAACC,YAAY;IACzC,IAAI,CAAC9J,OAAO,GAAG,IAAI,CAACD,eAAe;IACnC,IAAI,CAACE,oBAAoB,GAAG4J,KAAK,CAACE,iBAAiB;IACnD,IAAI,CAAClK,cAAc,GAAGgK,KAAK,CAAChK,cAAc;IAC1C,IAAI,CAACC,GAAG,GAAG+J,KAAK,CAAC/J,GAAG;EACtB;EAEAkK,oCAAoCA,CAACjE,IAAU;IAC7C,IAAIkE,MAAM,GAAG,KAAK;IAClB,IAAI,CAACpK,cAAc,CAACqK,OAAO,CAAC,CAACrH,KAAa,EAAEjC,SAAoB,KAAI,CACpE,CAAC,CAAC;IACF,KAAK,IAAI,CAACA,SAAS,EAAEiC,KAAK,CAAC,IAAI,IAAI,CAAChD,cAAc,EAAE;MAClDoK,MAAM,GAAGrJ,SAAS,CAACiC,KAAK,CAACwD,IAAI,CAAEJ,EAAQ,IAAI;QACzC,OAAOA,EAAE,CAAC5D,EAAE,IAAI0D,IAAI,CAAC1D,EAAE;MACzB,CAAC,CAAC;MACF,IAAI4H,MAAM,EACR,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EACd;EAEA5K,cAAcA,CAAA;IACZ,IAAI,CAAC6E,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;EAC1C;EAEA1C,iBAAiBA,CAAC2I,GAAQ;IACxB;IACA;IACA;IACA;IACA;IACA;IACA,OAAO,IAAI,CAAC3G,gBAAgB,CAAChC,iBAAiB,CAAC2I,GAAG,CAAC;EACrD;EAEA9I,kBAAkBA,CAACT,SAAoB;IACrC,IAAI+E,IAAI,GAAiB,IAAI,CAAClC,eAAe,CAAC2G,0BAA0B,EAAE;IAC1EzE,IAAI,CAACC,mBAAmB,GAAGhF,SAAS;IACpC,IAAI,CAACyD,cAAc,CAACgG,OAAO,CAAC1E,IAAI,CAAC;EACnC;EAEAvD,aAAaA,CAAC2D,IAAU,EAAEyC,WAAmB;IAC3C,IAAI7C,IAAI,GAAiB,IAAI,CAAClC,eAAe,CAAC2G,0BAA0B,EAAE;IAC1EzE,IAAI,CAACJ,UAAU,GAAGQ,IAAI;IACtBJ,IAAI,CAAC8C,yBAAyB,GAAGD,WAAW;IAC5C,IAAI,CAACpE,KAAK,CAACiG,OAAO,CAAC1E,IAAI,CAAC;EAC1B;EAEAjD,mBAAmBA,CAACqD,IAAU,EAAEyC,WAAmB;IACjD,IAAI8B,SAAS,GAAmB,EAAE;IAClC,KAAK,IAAI3E,IAAI,IAAI,IAAI,CAACvB,KAAK,EAAE;MAC3B,IAAK,CAAC,IAAI,CAACxE,IAAI,IAAI+F,IAAI,CAACJ,UAAU,EAAElD,EAAE,KAAK0D,IAAI,CAAC1D,EAAE,IAAIsD,IAAI,CAAC8C,yBAAyB,IAAID,WAAW,IAAI7C,IAAI,CAACC,mBAAmB,KAAKC,SAAS,IAAM,IAAI,CAACjG,IAAI,IAAI+F,IAAI,CAACJ,UAAU,EAAElD,EAAE,KAAK0D,IAAI,CAAC1D,EAAG,EAAE;QAChMiI,SAAS,CAAChE,IAAI,CAACX,IAAI,CAAC;MACtB;IACF;IACA,OAAO2E,SAAS;EAClB;EAEA7I,wBAAwBA,CAACb,SAAoB;IAC3C,IAAIyD,cAAc,GAAmB,EAAE;IACvC,KAAK,IAAIsB,IAAI,IAAI,IAAI,CAACtB,cAAc,EAAE;MACpC,IAAIsB,IAAI,CAACC,mBAAmB,EAAEvD,EAAE,KAAKzB,SAAS,CAACyB,EAAE,EAAE;QACjDgC,cAAc,CAACiC,IAAI,CAACX,IAAI,CAAC;MAC3B;IACF;IACA,OAAOtB,cAAc;EACvB;EAEA5D,YAAYA,CAACoJ,KAAU;IAErB,IAAGA,KAAK,CAAClE,IAAI,CAACC,mBAAmB,IAAIC,SAAS,EAAC;MAC7C,IAAI0E,SAAS,GAAG,IAAI,CAACnG,KAAK,CAACoG,SAAS,CAAGvE,EAAgB,IAAKA,EAAE,CAACV,UAAU,IAAIsE,KAAK,CAAClE,IAAI,CAACJ,UAAU,IAAIU,EAAE,CAACwC,yBAAyB,IAAIoB,KAAK,CAAClE,IAAI,CAAC8C,yBAA0B,CAAC;MAC5K,IAAI,IAAI,CAACrE,KAAK,CAACmG,SAAS,CAAC,CAAClI,EAAE,EAAE;QAC5B,IAAI,CAACoB,eAAe,CAACgH,sBAAsB,CAAC,IAAI,CAACrG,KAAK,CAACmG,SAAS,CAAC,CAAClI,EAAG,CAAC,CAAC6E,SAAS,CAACC,GAAG,IAAG,CACvF,CAAC,CAAC;MACJ;MACA,IAAI,CAAC/C,KAAK,CAACsG,MAAM,CAACH,SAAS,EAAE,CAAC,CAAC;IACjC,CAAC,MAAM;MACL,IAAIA,SAAS,GAAG,IAAI,CAAClG,cAAc,CAACmG,SAAS,CAAGvE,EAAgB,IAAKA,EAAE,CAACL,mBAAmB,EAAEvD,EAAE,IAAIwH,KAAK,CAAClE,IAAI,CAACC,mBAAmB,CAACvD,EAAG,CAAC;MACtI,IAAI,CAACgC,cAAc,CAACqG,MAAM,CAACH,SAAS,EAAE,CAAC,CAAC;IAE1C;EACF;EAEAlK,WAAWA,CAACwJ,KAAU;IACpB;IACA;EAAA;EAGFtJ,YAAYA,CAACsJ,KAAU;IACrB;EAAA;EAGFc,OAAOA,CAACd,KAAU;IAChB;EAAA;EAKFe,OAAOA,CAACC,IAAS;IACf,IAAI,CAACC,aAAa,CAACC,IAAI,EAAE;EAC3B;EAEA,IAAIC,YAAYA,CAAA;IACd,OAAO,IAAI,CAACjH,UAAU,CAACqB,QAAQ;EACjC;EAAC,QAAA6F,CAAA,G;qBAnVU3H,qBAAqB,EAAA1E,EAAA,CAAAsM,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAxM,EAAA,CAAAsM,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA1M,EAAA,CAAAsM,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAA5M,EAAA,CAAAsM,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA9M,EAAA,CAAAsM,iBAAA,CAAAtM,EAAA,CAAA+M,SAAA;EAAA;EAAA,QAAAC,EAAA,G;UAArBtI,qBAAqB;IAAAuI,SAAA;IAAAC,SAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;QClB9BpN,EADJ,CAAAC,cAAA,aAAyB,aAC6B;QAAhCD,EAAA,CAAAE,UAAA,mBAAAoN,oDAAA;UAAA,OAASD,GAAA,CAAA3C,mBAAA,EAAqB;QAAA,EAAC;QAC7C1K,EAAA,CAAAuN,SAAA,aAAiF;QACjFvN,EAAA,CAAAC,cAAA,aAA6B;QACzBD,EAAA,CAAAsB,MAAA,+BACJ;QACJtB,EADI,CAAAa,YAAA,EAAM,EACJ;QAIMb,EAHZ,CAAAC,cAAA,aAA6C,aACtB,aACI,aACP;QACJD,EAAA,CAAAsB,MAAA,GACJ;;QAERtB,EAFQ,CAAAa,YAAA,EAAS,EACP,EACJ;QAGEb,EAFR,CAAAC,cAAA,cAAmB,cACI,cACP;QACJD,EAAA,CAAAsB,MAAA,4BACJ;QACJtB,EADI,CAAAa,YAAA,EAAS,EACP;QACNb,EAAA,CAAAC,cAAA,cAA+C;QAA3BD,EAAA,CAAAE,UAAA,mBAAAsN,qDAAA;UAAA,OAASH,GAAA,CAAA5M,cAAA,EAAgB;QAAA,EAAC;QAC1CT,EAAA,CAAAC,cAAA,cAA6B;QACzBD,EAAA,CAAAsB,MAAA,uBACJ;QAAAtB,EAAA,CAAAa,YAAA,EAAM;QACNb,EAAA,CAAAuN,SAAA,cAAwC;QAC5CvN,EAAA,CAAAa,YAAA,EAAM;QACNb,EAAA,CAAAmC,UAAA,KAAAsL,qCAAA,kBAAyD;QAM7DzN,EAAA,CAAAa,YAAA,EAAM;QAGEb,EAFR,CAAAC,cAAA,cAAmB,cACI,cACP;QACJD,EAAA,CAAAsB,MAAA,iBACJ;QACJtB,EADI,CAAAa,YAAA,EAAS,EACP;QAEFb,EADJ,CAAAC,cAAA,eAA0B,eACO;QACzBD,EAAA,CAAAuN,SAAA,iBAA8F;QAC9FvN,EAAA,CAAAmC,UAAA,KAAAuL,qCAAA,kBACwB;QAIpC1N,EAFQ,CAAAa,YAAA,EAAM,EACJ,EACJ;QAGEb,EAFR,CAAAC,cAAA,cAAmB,cACI,cACP;QACJD,EAAA,CAAAsB,MAAA,uBACJ;QACJtB,EADI,CAAAa,YAAA,EAAS,EACP;QAEFb,EADJ,CAAAC,cAAA,eAA0B,eACO;QACzBD,EAAA,CAAAuN,SAAA,oBACwB;QACxBvN,EAAA,CAAAmC,UAAA,KAAAwL,qCAAA,kBACwB;QAMpC3N,EAFQ,CAAAa,YAAA,EAAM,EACJ,EACJ;QAGEb,EAFR,CAAAC,cAAA,cAAmB,cACI,cACP;QACJD,EAAA,CAAAsB,MAAA,eACJ;QACJtB,EADI,CAAAa,YAAA,EAAS,EACP;QACNb,EAAA,CAAAmC,UAAA,KAAAyL,qCAAA,kBAA4C;QAI5C5N,EAAA,CAAAC,cAAA,WAAK;QAIDD,EAHA,CAAAmC,UAAA,KAAA0L,qCAAA,kBAAsC,KAAAC,qCAAA,kBAGmB;;QA2CjE9N,EADI,CAAAa,YAAA,EAAM,EACJ;QAGEb,EAFR,CAAAC,cAAA,cAAmB,cACI,cACP;QACJD,EAAA,CAAAsB,MAAA,kBACJ;QACJtB,EADI,CAAAa,YAAA,EAAS,EACP;QAEFb,EADJ,CAAAC,cAAA,eAA0B,eAC8C;QAChED,EAAA,CAAAuN,SAAA,iBAA+E;QAI3FvN,EAFQ,CAAAa,YAAA,EAAM,EACJ,EACJ;QAWNb,EAVA,CAAAmC,UAAA,KAAA4L,qCAAA,kBAA+C,KAAAC,qCAAA,kBAUD;QAStDhO,EADI,CAAAa,YAAA,EAAM,EACJ;;;QAtJGb,EAAA,CAAAc,SAAA,GAAwB;QAAxBd,EAAA,CAAAe,UAAA,cAAAsM,GAAA,CAAAlI,UAAA,CAAwB;QAIbnF,EAAA,CAAAc,SAAA,GACJ;QADId,EAAA,CAAA2C,kBAAA,kBAAA3C,EAAA,CAAAiO,WAAA,SAAAZ,GAAA,CAAAjI,GAAA,qBACJ;QAegCpF,EAAA,CAAAc,SAAA,IAAmB;QAAnBd,EAAA,CAAAe,UAAA,SAAAsM,GAAA,CAAA/H,aAAA,CAAmB;QAgBzCtF,EAAA,CAAAc,SAAA,GAAwF;QAAxFd,EAAA,CAAAe,UAAA,UAAAsM,GAAA,CAAA3H,WAAA,IAAA2H,GAAA,CAAAjB,YAAA,CAAArG,KAAA,CAAAmI,OAAA,MAAAb,GAAA,CAAAjB,YAAA,CAAArG,KAAA,CAAAoI,MAAA,kBAAAd,GAAA,CAAAjB,YAAA,CAAArG,KAAA,CAAAoI,MAAA,CAAAlI,QAAA,EAAwF;QAgBxFjG,EAAA,CAAAc,SAAA,GAAoG;QAApGd,EAAA,CAAAe,UAAA,UAAAsM,GAAA,CAAA3H,WAAA,IAAA2H,GAAA,CAAAjB,YAAA,CAAAlG,WAAA,CAAAgI,OAAA,MAAAb,GAAA,CAAAjB,YAAA,CAAAlG,WAAA,CAAAiI,MAAA,kBAAAd,GAAA,CAAAjB,YAAA,CAAAlG,WAAA,CAAAiI,MAAA,CAAAlI,QAAA,EAAoG;QAc5GjG,EAAA,CAAAc,SAAA,GAAgB;QAAhBd,EAAA,CAAAe,UAAA,SAAAsM,GAAA,CAAA1H,UAAA,CAAgB;QAKZ3F,EAAA,CAAAc,SAAA,GAA8B;QAA9Bd,EAAA,CAAAe,UAAA,SAAAsM,GAAA,CAAApM,cAAA,CAAAuH,IAAA,MAA8B;QAGTxI,EAAA,CAAAc,SAAA,EAA4B;QAA5Bd,EAAA,CAAAe,UAAA,YAAAf,EAAA,CAAAoO,WAAA,SAAAf,GAAA,CAAApM,cAAA,EAA4B;QAyDzDjB,EAAA,CAAAc,SAAA,GAAW;QAAXd,EAAA,CAAAe,UAAA,UAAAsM,GAAA,CAAArM,IAAA,CAAW;QAUXhB,EAAA,CAAAc,SAAA,EAAU;QAAVd,EAAA,CAAAe,UAAA,SAAAsM,GAAA,CAAArM,IAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}