{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FilterPipe } from 'src/app/core/pipes/filter.pipe';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/classroom.service\";\nimport * as i2 from \"src/app/core/services/auth.service\";\nimport * as i3 from \"src/app/core/services/general.service\";\nimport * as i4 from \"src/app/core/services/layout.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/api\";\nimport * as i9 from \"src/app/core/pipes/filter.pipe\";\nconst _c0 = [\"myFilesDiv\"];\nconst _c1 = [\"op\"];\nconst _c2 = [\"scrollPanel\"];\nconst _c3 = a0 => ({\n  \"collapsed\": a0\n});\nconst _c4 = a0 => ({\n  \"rotate-180 cola\": a0\n});\nconst _c5 = a0 => ({\n  \"link-main-color-active\": a0\n});\nconst _c6 = a0 => ({\n  \"filter-white\": a0\n});\nconst _c7 = a0 => ({\n  \"no-visibility\": a0\n});\nconst _c8 = a0 => ({\n  term: a0\n});\nfunction LibraryLeftSidebarComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"img\", 13);\n    i0.ɵɵlistener(\"click\", function LibraryLeftSidebarComponent_div_1_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.collapseNotesLeftSide());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c4, ctx_r1.isCollapsed));\n  }\n}\nfunction LibraryLeftSidebarComponent_ng_template_4_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵlistener(\"click\", function LibraryLeftSidebarComponent_ng_template_4_div_0_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onMyFilesSelected());\n    });\n    i0.ɵɵelementStart(1, \"span\", 17);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c5, ctx_r1.selectedClassroom === null || !ctx_r1.hasSelectedClassroom));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.firstListItemTitle);\n  }\n}\nfunction LibraryLeftSidebarComponent_ng_template_4_div_1_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 25);\n  }\n}\nfunction LibraryLeftSidebarComponent_ng_template_4_div_1_img_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 26);\n  }\n  if (rf & 2) {\n    const classroom_r5 = i0.ɵɵnextContext().$implicit;\n    const classTitleElem_r6 = i0.ɵɵreference(2);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.classroomService.getClassroomTypeSmallIconPath(classroom_r5), i0.ɵɵsanitizeUrl)(\"ngClass\", i0.ɵɵpureFunction1(2, _c6, classTitleElem_r6.classList.contains(\"link-main-color-active\")));\n  }\n}\nfunction LibraryLeftSidebarComponent_ng_template_4_div_1_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const classroom_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.classroomService.getFormattedStudentNames(classroom_r5.classroomStudents));\n  }\n}\nfunction LibraryLeftSidebarComponent_ng_template_4_div_1_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const classroom_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", classroom_r5.language, \" - \", classroom_r5.teacher.firstName, \"\");\n  }\n}\nfunction LibraryLeftSidebarComponent_ng_template_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19, 2);\n    i0.ɵɵlistener(\"click\", function LibraryLeftSidebarComponent_ng_template_4_div_1_Template_div_click_1_listener() {\n      const classroom_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.classRoomSelected(classroom_r5));\n    });\n    i0.ɵɵelementStart(3, \"span\", 20);\n    i0.ɵɵtemplate(4, LibraryLeftSidebarComponent_ng_template_4_div_1_img_4_Template, 1, 0, \"img\", 21)(5, LibraryLeftSidebarComponent_ng_template_4_div_1_img_5_Template, 1, 4, \"img\", 22)(6, LibraryLeftSidebarComponent_ng_template_4_div_1_span_6_Template, 2, 1, \"span\", 23)(7, LibraryLeftSidebarComponent_ng_template_4_div_1_span_7_Template, 2, 2, \"span\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(8, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const classroom_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"id\", classroom_r5.id);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c5, ctx_r1.selectedClassroom.id === classroom_r5.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.classroomService.isClassroomOngoing(classroom_r5));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isTeacher);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isTeacher);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isStudent);\n  }\n}\nfunction LibraryLeftSidebarComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, LibraryLeftSidebarComponent_ng_template_4_div_0_Template, 3, 4, \"div\", 14)(1, LibraryLeftSidebarComponent_ng_template_4_div_1_Template, 9, 8, \"div\", 15);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isTeacher);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.filteredClassRooms);\n  }\n}\nfunction LibraryLeftSidebarComponent_p_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 27);\n    i0.ɵɵlistener(\"click\", function LibraryLeftSidebarComponent_p_button_5_Template_p_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      i0.ɵɵnextContext();\n      const op_r8 = i0.ɵɵreference(3);\n      return i0.ɵɵresetView(op_r8.toggle($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"label\", ctx_r1.hasSelectedClassroom ? ctx_r1.classroomService.getFormattedStudentNames(ctx_r1.selectedClassroom.classroomStudents) : \"Select Classroom\");\n  }\n}\nfunction LibraryLeftSidebarComponent_ng_template_8_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 30)(1, \"span\", 31);\n    i0.ɵɵelement(2, \"img\", 32);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 33);\n    i0.ɵɵelement(5, \"img\", 34);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const classroom_r9 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", classroom_r9.language, \" - \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", classroom_r9.totalHours, \" hours available \");\n  }\n}\nfunction LibraryLeftSidebarComponent_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 28);\n    i0.ɵɵtemplate(1, LibraryLeftSidebarComponent_ng_template_8_li_1_Template, 7, 2, \"li\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.filteredClassRooms);\n  }\n}\nfunction LibraryLeftSidebarComponent_div_9_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16, 3);\n    i0.ɵɵlistener(\"click\", function LibraryLeftSidebarComponent_div_9_div_5_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onMyFilesSelected());\n    });\n    i0.ɵɵelementStart(2, \"span\", 17);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c5, ctx_r1.selectedClassroom === null || !ctx_r1.hasSelectedClassroom));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.firstListItemTitle);\n  }\n}\nfunction LibraryLeftSidebarComponent_div_9_ng_container_7_div_1_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 25);\n  }\n}\nfunction LibraryLeftSidebarComponent_div_9_ng_container_7_div_1_img_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 26);\n  }\n  if (rf & 2) {\n    const classroom_r13 = i0.ɵɵnextContext().$implicit;\n    const classTitleElem_r14 = i0.ɵɵreference(2);\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.classroomService.getClassroomTypeSmallIconPath(classroom_r13), i0.ɵɵsanitizeUrl)(\"ngClass\", i0.ɵɵpureFunction1(2, _c6, classTitleElem_r14.classList.contains(\"link-main-color-active\")));\n  }\n}\nfunction LibraryLeftSidebarComponent_div_9_ng_container_7_div_1_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const classroom_r13 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.classroomService.getFormattedStudentNames(classroom_r13.classroomStudents));\n  }\n}\nfunction LibraryLeftSidebarComponent_div_9_ng_container_7_div_1_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const classroom_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", classroom_r13.language, \" - \", classroom_r13.teacher.firstName, \"\");\n  }\n}\nfunction LibraryLeftSidebarComponent_div_9_ng_container_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 45, 2);\n    i0.ɵɵlistener(\"click\", function LibraryLeftSidebarComponent_div_9_ng_container_7_div_1_Template_div_click_1_listener() {\n      const classroom_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.classRoomSelected(classroom_r13));\n    });\n    i0.ɵɵelementStart(3, \"span\", 20);\n    i0.ɵɵtemplate(4, LibraryLeftSidebarComponent_div_9_ng_container_7_div_1_img_4_Template, 1, 0, \"img\", 21)(5, LibraryLeftSidebarComponent_div_9_ng_container_7_div_1_img_5_Template, 1, 4, \"img\", 22)(6, LibraryLeftSidebarComponent_div_9_ng_container_7_div_1_span_6_Template, 2, 1, \"span\", 23)(7, LibraryLeftSidebarComponent_div_9_ng_container_7_div_1_span_7_Template, 2, 2, \"span\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(8, \"div\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const classroom_r13 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"id\", classroom_r13.id);\n    i0.ɵɵproperty(\"pTooltip\", classroom_r13.type == \"Trial\" ? \"Trial Classroom\" : \"\")(\"ngClass\", i0.ɵɵpureFunction1(7, _c5, ctx_r1.selectedClassroom.id === classroom_r13.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.classroomService.isClassroomOngoing(classroom_r13));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isTeacher);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isTeacher);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isStudent);\n  }\n}\nfunction LibraryLeftSidebarComponent_div_9_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, LibraryLeftSidebarComponent_div_9_ng_container_7_div_1_Template, 9, 9, \"div\", 15);\n    i0.ɵɵpipe(2, \"filter\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind2(2, 1, ctx_r1.filteredClassRooms, i0.ɵɵpureFunction1(4, _c8, ctx_r1.term)));\n  }\n}\nfunction LibraryLeftSidebarComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"span\", 36)(2, \"input\", 37);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LibraryLeftSidebarComponent_div_9_Template_input_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.term, $event) || (ctx_r1.term = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"i\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 39);\n    i0.ɵɵtemplate(5, LibraryLeftSidebarComponent_div_9_div_5_Template, 4, 4, \"div\", 14);\n    i0.ɵɵelement(6, \"div\", 40);\n    i0.ɵɵtemplate(7, LibraryLeftSidebarComponent_div_9_ng_container_7_Template, 3, 6, \"ng-container\", 23);\n    i0.ɵɵelementStart(8, \"div\", 41)(9, \"div\", 42)(10, \"label\", 43);\n    i0.ɵɵtext(11, \"Show Active\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p-inputSwitch\", 44);\n    i0.ɵɵlistener(\"onChange\", function LibraryLeftSidebarComponent_div_9_Template_p_inputSwitch_onChange_12_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleSwitch($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"label\", 43);\n    i0.ɵɵtext(14, \"Show All\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c7, ctx_r1.isCollapsed));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.term);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"height\", ctx_r1.leftSideHeight + \"px\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isTeacher);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.filteredClassRooms && ctx_r1.selectedClassroom);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.showActiveClassrooms);\n  }\n}\nexport class LibraryLeftSidebarComponent {\n  constructor(classroomService, authService, generalService, layoutService) {\n    this.classroomService = classroomService;\n    this.authService = authService;\n    this.generalService = generalService;\n    this.layoutService = layoutService;\n    this.subs = new SubSink();\n    this.isCollapsed = false;\n    this.showActiveClassrooms = false;\n    this.autoSelectFirstClassroom = false;\n    this.firstListItemTitle = 'My Library';\n    this.showMyNotes = false;\n    this.myFilesSelected = new EventEmitter();\n    this.classroomSelected = new EventEmitter();\n    this.collapsed = new EventEmitter();\n    this.myFilesDiv = {};\n    this.dayOverlayPanel = {};\n    this.scrollPanel = {};\n    this.filteredClassRooms = [];\n    this.selectedClassroom = {};\n    this.isLarge = false;\n  }\n  ngOnInit() {\n    this.subs.add(this.layoutService.sideMenuHeight.pipe().subscribe(res => {}));\n    this.subs.add(this.generalService.deviceKind.pipe().subscribe(res => {\n      if (res) {\n        this.isLarge = res.w992up;\n        if (res.w768up) {\n          // this.renderer.addClass(this.document.getElementsByClassName('app')[0], 'h-full');\n        } else {}\n      }\n    }));\n  }\n  ngOnChanges() {\n    // Set the filteredClassRooms array to the classrooms array\n    this.filteredClassRooms = this.classrooms;\n    // Check if the user is a student\n    if (this.authService.isStudent) {\n      // Set showActiveClassrooms to true\n      this.showActiveClassrooms = true;\n      // Call toggleSwitch method with checked: true\n      this.toggleSwitch({\n        checked: true\n      });\n    }\n    // Check if filteredClassRooms and user is a teacher\n    if (this.filteredClassRooms && this.authService.isTeacher) {\n      // Sort the classrooms array by first name and assign it to filteredClassRooms\n      this.filteredClassRooms = this.classroomService.sortClassroomsByFirstName(this.classrooms);\n      // Call toggleSwitch method with checked: false\n      this.toggleSwitch({\n        checked: false\n      });\n    }\n    // Prepare the collapsed state for the student\n    this.prepareCollapsedForStudent();\n    // Check if autoSelectFirstClassroom is true\n    if (this.autoSelectFirstClassroom) {\n      // Check if filteredClassRooms is not null or empty\n      if (this.filteredClassRooms && !this.generalService.isNullishObject(this.filteredClassRooms)) {\n        // Log the first element of filteredClassRooms to the console\n        console.log(this.filteredClassRooms[0]);\n        // Call classRoomSelected method with the first element of filteredClassRooms\n        this.classRoomSelected(this.filteredClassRooms[0]);\n      }\n    } else {\n      // Check if the user is a teacher\n      if (this.authService.isTeacher) {\n        // Call addMyFilesClass method\n        this.addMyFilesClass();\n      }\n    }\n    // Check if scrollPanel exists\n    if (this.scrollPanel) {\n      // Call refresh method on scrollPanel\n      this.scrollPanel.refresh();\n    }\n  }\n  ngAfterViewInit() {\n    this.prepareCollapsedForStudent();\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  get hasSelectedClassroom() {\n    return Object.keys(this.selectedClassroom).length > 0;\n  }\n  onMyFilesSelected() {\n    this.addMyFilesClass();\n    this.selectedClassroom = {};\n    this.myFilesSelected.emit(true);\n    if (!this.generalService.isNullishObject(this.dayOverlayPanel)) {\n      this.dayOverlayPanel.hide();\n    }\n  }\n  classRoomSelected(classroom) {\n    if (this.authService.isTeacher) {\n      this.removeMyFilesClass();\n    }\n    this.selectedClassroom = classroom;\n    this.classroomSelected.emit(classroom);\n    if (!this.generalService.isNullishObject(this.dayOverlayPanel)) {\n      this.dayOverlayPanel.hide();\n    }\n  }\n  removeClass() {\n    [].forEach.call(document.getElementsByClassName('classroom-title'), el => {\n      el.classList.remove('link-main-color-active');\n    });\n  }\n  addMyFilesClass() {\n    if (this.myFilesDiv && this.myFilesDiv.nativeElement) {\n      this.myFilesDiv.nativeElement.classList.add('link-main-color-active');\n    }\n    // document.getElementById('my-files')!.classList.add('link-main-color-active');\n  }\n  removeMyFilesClass() {\n    const myFilesElement = document.getElementById('my-files');\n    if (myFilesElement && myFilesElement.classList.contains('link-main-color-active')) {\n      myFilesElement.classList.remove('link-main-color-active');\n    }\n  }\n  collapseNotesLeftSide() {\n    this.isCollapsed = !this.isCollapsed;\n    this.collapsed.emit(this.isCollapsed);\n  }\n  toggleSwitch(event) {\n    console.log(event);\n    if (!event.checked) {\n      this.filteredClassRooms = this.classrooms.filter(el => el.status.toLowerCase() === this.classroomService.ClassroomStatuses.ON_GOING.toLowerCase());\n    } else {\n      this.filteredClassRooms = this.classrooms;\n    }\n  }\n  prepareCollapsedForStudent() {\n    if (this.authService.isStudent && this.filteredClassRooms && this.filteredClassRooms.length === 1) {\n      // this.isCollapsed = false;\n      this.collapseNotesLeftSide();\n    }\n  }\n  toggleDropdown(event) {\n    event.stopPropagation(); // prevent the click event from bubbling up to the parent element\n    const dropdown = document.querySelector('.dropdown-content');\n    dropdown.classList.toggle('show');\n  }\n  static #_ = this.ɵfac = function LibraryLeftSidebarComponent_Factory(t) {\n    return new (t || LibraryLeftSidebarComponent)(i0.ɵɵdirectiveInject(i1.ClassroomService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.GeneralService), i0.ɵɵdirectiveInject(i4.LayoutService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LibraryLeftSidebarComponent,\n    selectors: [[\"app-library-left-sidebar\"]],\n    viewQuery: function LibraryLeftSidebarComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(_c2, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.myFilesDiv = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dayOverlayPanel = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollPanel = _t.first);\n      }\n    },\n    inputs: {\n      libFolders: \"libFolders\",\n      classrooms: \"classrooms\",\n      autoSelectFirstClassroom: \"autoSelectFirstClassroom\",\n      leftSideHeight: \"leftSideHeight\",\n      firstListItemTitle: \"firstListItemTitle\",\n      showMyNotes: \"showMyNotes\"\n    },\n    outputs: {\n      myFilesSelected: \"myFilesSelected\",\n      classroomSelected: \"classroomSelected\",\n      collapsed: \"collapsed\"\n    },\n    features: [i0.ɵɵProvidersFeature([FilterPipe]), i0.ɵɵNgOnChangesFeature],\n    decls: 10,\n    vars: 7,\n    consts: [[\"op\", \"\"], [\"classroomOverlayPanel\", \"\"], [\"classTitleElem\", \"\"], [\"myFilesDiv\", \"\"], [\"id\", \"notes-left-side\", 1, \"notes-left-side\", \"relative\", \"h-full\", 3, \"ngClass\"], [\"class\", \"icon-container pointer hvr-grow\", 4, \"ngIf\"], [\"styleClass\", \"w-full\"], [\"pTemplate\", \"content\"], [\"class\", \"w-full\", \"styleClass\", \"p-button-outlined p-button-rounded p-button-sm w-full\", 3, \"label\", \"click\", 4, \"ngIf\"], [3, \"styleClass\"], [\"pTemplate\", \"content\", 1, \"p-0\"], [\"class\", \"h-full\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"icon-container\", \"pointer\", \"hvr-grow\"], [\"src\", \"/assets/icons/library/arrow-left.svg\", 2, \"width\", \"1.45rem\", \"height\", \"1.45rem\", 3, \"click\", \"ngClass\"], [\"id\", \"my-files\", \"class\", \"classroom-title link-main-color mx-2\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"class\", \"mx-2\", 4, \"ngFor\", \"ngForOf\"], [\"id\", \"my-files\", 1, \"classroom-title\", \"link-main-color\", \"mx-2\", 3, \"click\", \"ngClass\"], [1, \"font-semibold\"], [1, \"mx-2\"], [1, \"classroom-title\", \"one-line\", \"link-main-color\", \"text-left\", 3, \"click\", \"ngClass\", \"id\"], [1, \"hovered-item\"], [\"title\", \"active\", \"src\", \"/assets/icons/library/active-icon.svg\", \"width\", \"12\", \"class\", \"mr-1\", 4, \"ngIf\"], [\"height\", \"12\", \"class\", \"mr-1\", 3, \"src\", \"ngClass\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"seperator\"], [\"title\", \"active\", \"src\", \"/assets/icons/library/active-icon.svg\", \"width\", \"12\", 1, \"mr-1\"], [\"height\", \"12\", 1, \"mr-1\", 3, \"src\", \"ngClass\"], [\"styleClass\", \"p-button-outlined p-button-rounded p-button-sm w-full\", 1, \"w-full\", 3, \"click\", \"label\"], [1, \"p-0\"], [\"class\", \"flex align-items-center py-0 border-bottom-1 surface-border mx-2 line-height-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"align-items-center\", \"py-0\", \"border-bottom-1\", \"surface-border\", \"mx-2\", \"line-height-3\"], [1, \"col-6\", \"p-0\", \"no-gutter\", \"flex\", \"align-items-center\", \"gap-1\", \"text-700\", \"font-xs\"], [\"src\", \"/assets/images/dashboard/student.svg\", \"height\", \"8\"], [1, \"col-6\", \"p-0\", \"gap-1\", \"no-gutter\", \"flex\", \"align-items-center\", \"justify-content-start\", \"font-xs\"], [\"src\", \"/assets/images/dashboard/calendar/check-circled.svg\", \"height\", \"8\"], [1, \"h-full\", 3, \"ngClass\"], [1, \"p-input-icon-right\", \"w-full\", \"mb-1\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Search\", 1, \"h-2rem\", \"w-full\", \"input-blue\", \"gradient-blue\", \"rounded\", 3, \"ngModelChange\", \"ngModel\"], [1, \"pi\", \"pi-search\", \"text-primary\"], [1, \"overflow-y-auto\", \"custom-scrollbar\", \"lg:pt-3\", \"lg:pb-2\"], [1, \"seperator\", \"max-w-8rem\", 2, \"border-bottom\", \"1px solid #d9ddf0\", \"width\", \"85%\"], [1, \"text-center\", \"mt-4\"], [1, \"field-checkbox\", \"my-0\", \"py-1\", \"font-2xs\", \"bottom-toggles\", \"sm:absolute\", \"bottom-0\"], [1, \"lesson-filter-label\", \"font-xs\", \"mr-2\"], [1, \"flex\", \"small-input-switch\", \"p-mr-2\", 3, \"onChange\", \"ngModel\"], [1, \"classroom-title\", \"one-line\", \"link-main-color\", \"text-left\", 3, \"click\", \"pTooltip\", \"ngClass\", \"id\"], [1, \"seperator\", \"max-w-8rem\"]],\n    template: function LibraryLeftSidebarComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 4);\n        i0.ɵɵtemplate(1, LibraryLeftSidebarComponent_div_1_Template, 2, 3, \"div\", 5);\n        i0.ɵɵelementStart(2, \"p-overlayPanel\", 6, 0);\n        i0.ɵɵtemplate(4, LibraryLeftSidebarComponent_ng_template_4_Template, 2, 2, \"ng-template\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(5, LibraryLeftSidebarComponent_p_button_5_Template, 1, 1, \"p-button\", 8);\n        i0.ɵɵelementStart(6, \"p-overlayPanel\", 9, 1);\n        i0.ɵɵtemplate(8, LibraryLeftSidebarComponent_ng_template_8_Template, 2, 1, \"ng-template\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(9, LibraryLeftSidebarComponent_div_9_Template, 15, 9, \"div\", 11);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c3, ctx.isCollapsed));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isLarge);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLarge);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"styleClass\", \"overlay-panel-width\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.isLarge);\n      }\n    },\n    dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i6.DefaultValueAccessor, i6.NgControlStatus, i6.NgModel, i7.Button, i8.PrimeTemplate, i9.FilterPipe],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.link-main-color[_ngcontent-%COMP%] {\\n  text-align: center;\\n  border-radius: 50px;\\n}\\n\\n.classroom-title[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  border-radius: 50px;\\n}\\n.classroom-title.one-line[_ngcontent-%COMP%] {\\n  white-space: nowrap; \\n\\n  overflow: hidden; \\n\\n  text-overflow: ellipsis; \\n\\n}\\n\\n.seperator[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 0.1px;\\n  background-color: #D9DDF0;\\n  margin: 3px auto;\\n}\\n\\n.icon-container[_ngcontent-%COMP%] {\\n  height: 100%;\\n  transform: translateY(50%);\\n  position: absolute;\\n  right: -8px;\\n}\\n\\n.icon-container[_ngcontent-%COMP%]    > img[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 16px;\\n  margin-left: auto;\\n  \\n\\n}\\n.icon-container[_ngcontent-%COMP%]    > img.cola[_ngcontent-%COMP%] {\\n  right: 0;\\n}\\n\\n[_nghost-%COMP%]     .small-input-switch .p-inputswitch {\\n  height: 1rem;\\n  width: 1.8rem;\\n}\\n[_nghost-%COMP%]     .small-input-switch .p-inputswitch.p-inputswitch-checked .p-inputswitch-slider::before {\\n  transform: translateX(1rem);\\n  background-image: linear-gradient(90deg, hsl(229, 95%, 71%) 0%, hsl(230, 90%, 68%) 11%, hsl(230, 86%, 66%) 22%, hsl(230, 83%, 63%) 33%, hsl(231, 79%, 60%) 44%, hsl(231, 77%, 58%) 56%, hsl(231, 74%, 55%) 67%, hsl(230, 72%, 52%) 78%, hsl(230, 75%, 48%) 89%, hsl(227, 90%, 43%) 100%);\\n}\\n[_nghost-%COMP%]     .small-input-switch .p-inputswitch .p-inputswitch-slider {\\n  background: rgba(0, 0, 0, 0.1);\\n}\\n[_nghost-%COMP%]     .small-input-switch .p-inputswitch .p-inputswitch-slider::before {\\n  width: 0.8rem;\\n  height: 0.8rem;\\n  margin-top: -0.4rem;\\n}\\n\\n.dropdown[_ngcontent-%COMP%] {\\n  border: 1px solid red;\\n}\\n\\n.dropdown-content[_ngcontent-%COMP%] {\\n  display: none;\\n  position: absolute;\\n  z-index: 1;\\n}\\n\\n.dropdown-content.show[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n\\n.selected-item[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  padding: 10px;\\n  margin-bottom: 10px;\\n}\\n\\n.custom-scrollbar[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n\\n.custom-scrollbar[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #d2d2ff;\\n  border-radius: 100px;\\n}\\n\\n.custom-scrollbar[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: linear-gradient(#4354b4 0%, #c9d2ff 100%);\\n  opacity: 0.12;\\n  border-radius: 100px;\\n  border: 1px none #363636;\\n}\\n\\n.bottom-toggles[_ngcontent-%COMP%] {\\n  background-color: #ffffff;\\n  width: 100%;\\n}\\n\\n.notes-left-side.collapsed[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "FilterPipe", "SubSink", "i0", "ɵɵelementStart", "ɵɵlistener", "LibraryLeftSidebarComponent_div_1_Template_img_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "collapseNotesLeftSide", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction1", "_c4", "isCollapsed", "LibraryLeftSidebarComponent_ng_template_4_div_0_Template_div_click_0_listener", "_r3", "onMyFilesSelected", "ɵɵtext", "_c5", "selectedClassroom", "hasSelectedClassroom", "ɵɵtextInterpolate", "firstListItemTitle", "ɵɵelement", "classroomService", "getClassroomTypeSmallIconPath", "classroom_r5", "ɵɵsanitizeUrl", "_c6", "classTitleElem_r6", "classList", "contains", "getFormattedStudentNames", "classroomStudents", "ɵɵtextInterpolate2", "language", "teacher", "firstName", "LibraryLeftSidebarComponent_ng_template_4_div_1_Template_div_click_1_listener", "_r4", "$implicit", "classRoomSelected", "ɵɵtemplate", "LibraryLeftSidebarComponent_ng_template_4_div_1_img_4_Template", "LibraryLeftSidebarComponent_ng_template_4_div_1_img_5_Template", "LibraryLeftSidebarComponent_ng_template_4_div_1_span_6_Template", "LibraryLeftSidebarComponent_ng_template_4_div_1_span_7_Template", "ɵɵpropertyInterpolate", "id", "isClassroomOngoing", "authService", "<PERSON><PERSON><PERSON>er", "isStudent", "LibraryLeftSidebarComponent_ng_template_4_div_0_Template", "LibraryLeftSidebarComponent_ng_template_4_div_1_Template", "filteredClassRooms", "LibraryLeftSidebarComponent_p_button_5_Template_p_button_click_0_listener", "$event", "_r7", "op_r8", "ɵɵreference", "toggle", "ɵɵtextInterpolate1", "classroom_r9", "totalHours", "LibraryLeftSidebarComponent_ng_template_8_li_1_Template", "LibraryLeftSidebarComponent_div_9_div_5_Template_div_click_0_listener", "_r11", "classroom_r13", "classTitleElem_r14", "LibraryLeftSidebarComponent_div_9_ng_container_7_div_1_Template_div_click_1_listener", "_r12", "LibraryLeftSidebarComponent_div_9_ng_container_7_div_1_img_4_Template", "LibraryLeftSidebarComponent_div_9_ng_container_7_div_1_img_5_Template", "LibraryLeftSidebarComponent_div_9_ng_container_7_div_1_span_6_Template", "LibraryLeftSidebarComponent_div_9_ng_container_7_div_1_span_7_Template", "type", "ɵɵelementContainerStart", "LibraryLeftSidebarComponent_div_9_ng_container_7_div_1_Template", "ɵɵpipeBind2", "_c8", "term", "ɵɵtwoWayListener", "LibraryLeftSidebarComponent_div_9_Template_input_ngModelChange_2_listener", "_r10", "ɵɵtwoWayBindingSet", "LibraryLeftSidebarComponent_div_9_div_5_Template", "LibraryLeftSidebarComponent_div_9_ng_container_7_Template", "LibraryLeftSidebarComponent_div_9_Template_p_inputSwitch_onChange_12_listener", "toggleSwitch", "_c7", "ɵɵtwoWayProperty", "ɵɵstyleProp", "leftSideHeight", "showActiveClassrooms", "LibraryLeftSidebarComponent", "constructor", "generalService", "layoutService", "subs", "autoSelectFirstClassroom", "showMyNotes", "myFilesSelected", "classroomSelected", "collapsed", "myFilesDiv", "dayOverlayPanel", "scrollPanel", "is<PERSON>arge", "ngOnInit", "add", "sideMenuHeight", "pipe", "subscribe", "res", "deviceKind", "w992up", "w768up", "ngOnChanges", "classrooms", "checked", "sortClassroomsByFirstName", "prepareCollapsedForStudent", "isNullishObject", "console", "log", "addMyFilesClass", "refresh", "ngAfterViewInit", "ngOnDestroy", "unsubscribe", "Object", "keys", "length", "emit", "hide", "classroom", "removeMyFilesClass", "removeClass", "for<PERSON>ach", "call", "document", "getElementsByClassName", "el", "remove", "nativeElement", "myFilesElement", "getElementById", "event", "filter", "status", "toLowerCase", "ClassroomStatuses", "ON_GOING", "toggleDropdown", "stopPropagation", "dropdown", "querySelector", "_", "ɵɵdirectiveInject", "i1", "ClassroomService", "i2", "AuthService", "i3", "GeneralService", "i4", "LayoutService", "_2", "selectors", "viewQuery", "LibraryLeftSidebarComponent_Query", "rf", "ctx", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "LibraryLeftSidebarComponent_Template", "LibraryLeftSidebarComponent_div_1_Template", "LibraryLeftSidebarComponent_ng_template_4_Template", "LibraryLeftSidebarComponent_p_button_5_Template", "LibraryLeftSidebarComponent_ng_template_8_Template", "LibraryLeftSidebarComponent_div_9_Template", "_c3"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\library\\library\\components\\library-left-sidebar\\library-left-sidebar.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\library\\library\\components\\library-left-sidebar\\library-left-sidebar.component.html"], "sourcesContent": ["import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';\r\nimport { OverlayPanel } from 'primeng/overlaypanel';\r\nimport { ScrollPanel } from 'primeng/scrollpanel';\r\nimport { Classroom } from 'src/app/core/models/classroom.model';\r\nimport { FilterPipe } from 'src/app/core/pipes/filter.pipe';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { LayoutService } from 'src/app/core/services/layout.service';\r\nimport { SubSink } from 'subsink';\r\n\r\n@Component({\r\n  selector: 'app-library-left-sidebar',\r\n  templateUrl: './library-left-sidebar.component.html',\r\n  styleUrls: ['./library-left-sidebar.component.scss'],\r\n  providers: [FilterPipe]\r\n})\r\nexport class LibraryLeftSidebarComponent implements OnInit {\r\n  private subs = new SubSink();\r\n  isCollapsed = false;\r\n  showActiveClassrooms = false;\r\n  term: any;\r\n  @Input() libFolders: any;\r\n  @Input() classrooms: any;\r\n  @Input() autoSelectFirstClassroom = false;\r\n  @Input() leftSideHeight!: string;\r\n  @Input() firstListItemTitle = 'My Library';\r\n  @Input() showMyNotes = false;\r\n  @Output() myFilesSelected = new EventEmitter();\r\n  @Output() classroomSelected = new EventEmitter();\r\n  @Output() collapsed = new EventEmitter();\r\n  @ViewChild('myFilesDiv', { static: false }) public myFilesDiv = {} as ElementRef;\r\n  @ViewChild('op', { static: false }) dayOverlayPanel: OverlayPanel = {} as OverlayPanel;\r\n  @ViewChild('scrollPanel', { static: true }) scrollPanel: ScrollPanel = {} as ScrollPanel;\r\n  filteredClassRooms: Classroom[] = [];\r\n  selectedClassroom = {} as Classroom;\r\n  isLarge = false;\r\n  constructor(\r\n    public classroomService: ClassroomService,\r\n    public authService: AuthService,\r\n    private generalService: GeneralService,\r\n    private layoutService: LayoutService,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.subs.add(this.layoutService.sideMenuHeight.pipe().subscribe(res => {\r\n    }));\r\n    this.subs.add(this.generalService.deviceKind.pipe().subscribe(res => {\r\n      if (res) {\r\n        this.isLarge = res.w992up as boolean;\r\n        if (res.w768up) {\r\n          // this.renderer.addClass(this.document.getElementsByClassName('app')[0], 'h-full');\r\n        } else {\r\n        }\r\n      }\r\n    }));\r\n  }\r\n\r\n  ngOnChanges(): void {\r\n    // Set the filteredClassRooms array to the classrooms array\r\n    this.filteredClassRooms = this.classrooms;\r\n  \r\n    // Check if the user is a student\r\n    if (this.authService.isStudent) {\r\n      // Set showActiveClassrooms to true\r\n      this.showActiveClassrooms = true;\r\n      // Call toggleSwitch method with checked: true\r\n      this.toggleSwitch({ checked: true });\r\n    }\r\n  \r\n    // Check if filteredClassRooms and user is a teacher\r\n    if (this.filteredClassRooms && this.authService.isTeacher) {\r\n      // Sort the classrooms array by first name and assign it to filteredClassRooms\r\n      this.filteredClassRooms = this.classroomService.sortClassroomsByFirstName(this.classrooms);\r\n      // Call toggleSwitch method with checked: false\r\n      this.toggleSwitch({ checked: false });\r\n    }\r\n  \r\n    // Prepare the collapsed state for the student\r\n    this.prepareCollapsedForStudent();\r\n  \r\n    // Check if autoSelectFirstClassroom is true\r\n    if (this.autoSelectFirstClassroom) {\r\n      // Check if filteredClassRooms is not null or empty\r\n      if (this.filteredClassRooms && !this.generalService.isNullishObject(this.filteredClassRooms)) {\r\n        // Log the first element of filteredClassRooms to the console\r\n        console.log(this.filteredClassRooms[0]);\r\n        // Call classRoomSelected method with the first element of filteredClassRooms\r\n        this.classRoomSelected(this.filteredClassRooms[0]);\r\n      }\r\n    } else {\r\n      // Check if the user is a teacher\r\n      if (this.authService.isTeacher) {\r\n        // Call addMyFilesClass method\r\n        this.addMyFilesClass();\r\n      }\r\n    }\r\n  \r\n    // Check if scrollPanel exists\r\n    if (this.scrollPanel) {\r\n      // Call refresh method on scrollPanel\r\n      this.scrollPanel.refresh();\r\n    }\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    this.prepareCollapsedForStudent();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  get hasSelectedClassroom() {\r\n    return Object.keys(this.selectedClassroom).length > 0;\r\n  }\r\n\r\n  onMyFilesSelected() {\r\n    this.addMyFilesClass();\r\n    this.selectedClassroom = {} as Classroom;\r\n    this.myFilesSelected.emit(true);\r\n    if (!this.generalService.isNullishObject(this.dayOverlayPanel)) {\r\n      this.dayOverlayPanel.hide();\r\n    }\r\n  }\r\n\r\n  classRoomSelected(classroom: Classroom) {\r\n    if (this.authService.isTeacher) {\r\n      this.removeMyFilesClass();\r\n    }\r\n    this.selectedClassroom = classroom;\r\n    this.classroomSelected.emit(classroom);\r\n    if (!this.generalService.isNullishObject(this.dayOverlayPanel)) {\r\n      this.dayOverlayPanel.hide();\r\n    }\r\n  }\r\n\r\n  removeClass() {\r\n    [].forEach.call(document.getElementsByClassName('classroom-title'), (el: any) => {\r\n      el.classList.remove('link-main-color-active');\r\n    })\r\n  }\r\n\r\n  addMyFilesClass() {\r\n    if (this.myFilesDiv && this.myFilesDiv.nativeElement) {\r\n      this.myFilesDiv.nativeElement!.classList.add('link-main-color-active')\r\n    }\r\n    // document.getElementById('my-files')!.classList.add('link-main-color-active');\r\n  }\r\n\r\n  removeMyFilesClass() {\r\n    const myFilesElement = document.getElementById('my-files');\r\n    if (myFilesElement && myFilesElement.classList.contains('link-main-color-active')) {\r\n      myFilesElement.classList.remove('link-main-color-active');\r\n    }\r\n  }\r\n\r\n  collapseNotesLeftSide() {\r\n    this.isCollapsed = !this.isCollapsed;\r\n    this.collapsed.emit(this.isCollapsed);\r\n  }\r\n\r\n  toggleSwitch(event: { checked: boolean }) {\r\n    console.log(event);\r\n    if (!event.checked) {\r\n      this.filteredClassRooms = this.classrooms.filter((el: Classroom) => el.status.toLowerCase() === this.classroomService.ClassroomStatuses.ON_GOING.toLowerCase());\r\n    } else {\r\n      this.filteredClassRooms = this.classrooms;\r\n    }\r\n  }\r\n\r\n  prepareCollapsedForStudent() {\r\n    if (this.authService.isStudent && this.filteredClassRooms && this.filteredClassRooms.length === 1) {\r\n      // this.isCollapsed = false;\r\n      this.collapseNotesLeftSide();\r\n    }\r\n  }\r\n\r\n  toggleDropdown(event: Event) {\r\n    event.stopPropagation(); // prevent the click event from bubbling up to the parent element\r\n    const dropdown = document.querySelector('.dropdown-content');\r\n    dropdown!.classList.toggle('show');\r\n  }\r\n\r\n}\r\n", "<!--  [style.position]=\"'sticky'\" -->\r\n<div id=\"notes-left-side\" class=\"notes-left-side relative h-full\" [ngClass]=\"{'collapsed': isCollapsed}\">\r\n    <div *ngIf=\"isLarge\" class=\"icon-container pointer hvr-grow\">\r\n        <img src=\"/assets/icons/library/arrow-left.svg\" [ngClass]=\"{'rotate-180 cola': isCollapsed}\"\r\n            style=\"width:1.45rem; height:1.45rem\" (click)=\"collapseNotesLeftSide()\">\r\n    </div>\r\n\r\n    <p-overlayPanel #op styleClass=\"w-full\">\r\n        <ng-template pTemplate=\"content\">\r\n            <div *ngIf=\"authService.isTeacher\" id=\"my-files\" (click)=\"onMyFilesSelected()\"\r\n             class=\"classroom-title link-main-color mx-2\"\r\n            [ngClass]=\"{'link-main-color-active': (selectedClassroom) === null || !hasSelectedClassroom}\">\r\n            <span class=\"font-semibold\">{{firstListItemTitle}}</span>\r\n        </div>\r\n            <div *ngFor=\"let classroom of filteredClassRooms\" class=\"mx-2\">\r\n                <div (click)=\"classRoomSelected(classroom)\" #classTitleElem\r\n                    class=\"classroom-title one-line link-main-color text-left\" \r\n                    [ngClass]=\"{'link-main-color-active': selectedClassroom.id === classroom.id}\" id={{classroom.id}}>\r\n    \r\n                    <span class=\"hovered-item\"><img *ngIf=\"classroomService.isClassroomOngoing(classroom)\" title=\"active\"\r\n                            src=\"/assets/icons/library/active-icon.svg\" width=\"12\" class=\"mr-1\">\r\n                        <img *ngIf=\"authService.isTeacher\" [src]=\"classroomService.getClassroomTypeSmallIconPath(classroom)\"\r\n                            [ngClass]=\"{'filter-white': classTitleElem.classList.contains('link-main-color-active') }\"\r\n                            height=\"12\" class=\"mr-1\" />\r\n                        <span\r\n                            *ngIf=\"authService.isTeacher\">{{classroomService.getFormattedStudentNames(classroom.classroomStudents)}}</span>\r\n                        <span *ngIf=\"authService.isStudent\">{{classroom.language}} - {{classroom.teacher.firstName}}</span>\r\n                    </span>\r\n                </div>\r\n                <div class=\"seperator\"></div>\r\n            </div>\r\n        </ng-template>\r\n    </p-overlayPanel>\r\n    <p-button *ngIf=\"!isLarge\" class=\"w-full\" styleClass=\"p-button-outlined p-button-rounded p-button-sm w-full\" (click)=\"op.toggle($event)\" \r\n    [label]=\"hasSelectedClassroom ? classroomService.getFormattedStudentNames(this.selectedClassroom.classroomStudents) : 'Select Classroom'\">\r\n    </p-button>\r\n    <p-overlayPanel #classroomOverlayPanel [styleClass]=\"'overlay-panel-width'\">\r\n        <ng-template class=\"p-0\" pTemplate=\"content\">\r\n            <ul class=\"p-0\">\r\n                    <li *ngFor=\"let classroom of filteredClassRooms\"\r\n                        class=\"flex align-items-center py-0 border-bottom-1 surface-border mx-2 line-height-3\">\r\n                        <span class=\"col-6 p-0 no-gutter flex align-items-center gap-1 text-700  font-xs\">\r\n                            <img src=\"/assets/images/dashboard/student.svg\" height=\"8\" /> {{classroom.language}} -\r\n                        </span>\r\n                        <span class=\"col-6 p-0 gap-1 no-gutter flex align-items-center justify-content-start font-xs\">\r\n                            <img src=\"/assets/images/dashboard/calendar/check-circled.svg\" height=\"8\" />\r\n                            {{classroom.totalHours}} hours available </span>\r\n                    </li>\r\n            </ul>\r\n        </ng-template>\r\n    </p-overlayPanel>\r\n\r\n    <div  *ngIf=\"isLarge\" class=\"h-full\" [ngClass]=\"{'no-visibility': isCollapsed}\">\r\n\r\n        <span class=\"p-input-icon-right w-full mb-1\">\r\n            <input type=\"text\" [(ngModel)]=\"term\" pInputText placeholder=\"Search\"\r\n                class=\"h-2rem w-full input-blue gradient-blue rounded\" />\r\n            <i class=\"pi pi-search text-primary\"></i>\r\n        </span>\r\n\r\n        <div class=\"overflow-y-auto custom-scrollbar lg:pt-3 lg:pb-2\" [style.height]=\"leftSideHeight + 'px'\">\r\n        <div *ngIf=\"authService.isTeacher\" id=\"my-files\" (click)=\"onMyFilesSelected()\"\r\n            #myFilesDiv class=\"classroom-title link-main-color mx-2\"\r\n            [ngClass]=\"{'link-main-color-active': (selectedClassroom) === null || !hasSelectedClassroom}\">\r\n            <span class=\"font-semibold\">{{firstListItemTitle}}</span>\r\n        </div>\r\n        <div class=\"seperator  max-w-8rem\" style=\"border-bottom: 1px solid #d9ddf0; width: 85%;\"></div>\r\n\r\n        <ng-container *ngIf=\"filteredClassRooms && selectedClassroom\">\r\n        <div *ngFor=\"let classroom of filteredClassRooms | filter: { term: term }\" class=\"mx-2\">\r\n            <div (click)=\"classRoomSelected(classroom)\" #classTitleElem [pTooltip]=\"classroom.type == 'Trial' ? 'Trial Classroom' : ''\"\r\n                class=\"classroom-title one-line link-main-color text-left\" \r\n                [ngClass]=\"{'link-main-color-active': selectedClassroom.id === classroom.id}\" id={{classroom.id}}>\r\n\r\n                <span class=\"hovered-item\"><img *ngIf=\"classroomService.isClassroomOngoing(classroom)\" title=\"active\"\r\n                        src=\"/assets/icons/library/active-icon.svg\" width=\"12\" class=\"mr-1\">\r\n                    <img *ngIf=\"authService.isTeacher\" [src]=\"classroomService.getClassroomTypeSmallIconPath(classroom)\"\r\n                        [ngClass]=\"{'filter-white': classTitleElem.classList.contains('link-main-color-active') }\"\r\n                        height=\"12\" class=\"mr-1\" />\r\n                    <span\r\n                        *ngIf=\"authService.isTeacher\">{{classroomService.getFormattedStudentNames(classroom.classroomStudents)}}</span>\r\n                    <span *ngIf=\"authService.isStudent\">{{classroom.language}} - {{classroom.teacher.firstName}}</span>\r\n                </span>\r\n            </div>\r\n            <div class=\"seperator max-w-8rem\"></div>\r\n        </div>\r\n        </ng-container>\r\n\r\n        <div class=\"text-center mt-4\">\r\n            <div class=\"field-checkbox my-0 py-1 font-2xs bottom-toggles sm:absolute bottom-0\">\r\n                <label class=\"lesson-filter-label font-xs mr-2\">Show Active</label>\r\n                <p-inputSwitch class=\"flex small-input-switch p-mr-2\" [ngModel]=\"showActiveClassrooms\"\r\n                    (onChange)=\"toggleSwitch($event)\"></p-inputSwitch>\r\n                <label class=\"lesson-filter-label font-xs mr-2\">Show All</label>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    </div>\r\n</div>"], "mappings": "AAAA,SAAgCA,YAAY,QAA0C,eAAe;AAIrG,SAASC,UAAU,QAAQ,gCAAgC;AAK3D,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICNzBC,EADJ,CAAAC,cAAA,cAA6D,cAEmB;IAAlCD,EAAA,CAAAE,UAAA,mBAAAC,gEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,qBAAA,EAAuB;IAAA,EAAC;IAC/ET,EAFI,CAAAU,YAAA,EAC4E,EAC1E;;;;IAF8CV,EAAA,CAAAW,SAAA,EAA4C;IAA5CX,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAAa,eAAA,IAAAC,GAAA,EAAAR,MAAA,CAAAS,WAAA,EAA4C;;;;;;IAMxFf,EAAA,CAAAC,cAAA,cAE8F;IAF7CD,EAAA,CAAAE,UAAA,mBAAAc,8EAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAY,iBAAA,EAAmB;IAAA,EAAC;IAG9ElB,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAmB,MAAA,GAAsB;IACtDnB,EADsD,CAAAU,YAAA,EAAO,EACvD;;;;IAFFV,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAAa,eAAA,IAAAO,GAAA,EAAAd,MAAA,CAAAe,iBAAA,cAAAf,MAAA,CAAAgB,oBAAA,EAA6F;IACjEtB,EAAA,CAAAW,SAAA,GAAsB;IAAtBX,EAAA,CAAAuB,iBAAA,CAAAjB,MAAA,CAAAkB,kBAAA,CAAsB;;;;;IAOfxB,EAAA,CAAAyB,SAAA,cACiD;;;;;IACxEzB,EAAA,CAAAyB,SAAA,cAE+B;;;;;;IAD3BzB,EAD+B,CAAAY,UAAA,QAAAN,MAAA,CAAAoB,gBAAA,CAAAC,6BAAA,CAAAC,YAAA,GAAA5B,EAAA,CAAA6B,aAAA,CAAiE,YAAA7B,EAAA,CAAAa,eAAA,IAAAiB,GAAA,EAAAC,iBAAA,CAAAC,SAAA,CAAAC,QAAA,4BACN;;;;;IAE9FjC,EAAA,CAAAC,cAAA,WACkC;IAAAD,EAAA,CAAAmB,MAAA,GAA0E;IAAAnB,EAAA,CAAAU,YAAA,EAAO;;;;;IAAjFV,EAAA,CAAAW,SAAA,EAA0E;IAA1EX,EAAA,CAAAuB,iBAAA,CAAAjB,MAAA,CAAAoB,gBAAA,CAAAQ,wBAAA,CAAAN,YAAA,CAAAO,iBAAA,EAA0E;;;;;IAC5GnC,EAAA,CAAAC,cAAA,WAAoC;IAAAD,EAAA,CAAAmB,MAAA,GAAwD;IAAAnB,EAAA,CAAAU,YAAA,EAAO;;;;IAA/DV,EAAA,CAAAW,SAAA,EAAwD;IAAxDX,EAAA,CAAAoC,kBAAA,KAAAR,YAAA,CAAAS,QAAA,SAAAT,YAAA,CAAAU,OAAA,CAAAC,SAAA,KAAwD;;;;;;IAXpGvC,EADJ,CAAAC,cAAA,cAA+D,iBAG2C;IAFjGD,EAAA,CAAAE,UAAA,mBAAAsC,8EAAA;MAAA,MAAAZ,YAAA,GAAA5B,EAAA,CAAAI,aAAA,CAAAqC,GAAA,EAAAC,SAAA;MAAA,MAAApC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqC,iBAAA,CAAAf,YAAA,CAA4B;IAAA,EAAC;IAIvC5B,EAAA,CAAAC,cAAA,eAA2B;IAOvBD,EAPuB,CAAA4C,UAAA,IAAAC,8DAAA,kBACiD,IAAAC,8DAAA,kBAGzC,IAAAC,+DAAA,mBAEG,IAAAC,+DAAA,mBACE;IAE5ChD,EADI,CAAAU,YAAA,EAAO,EACL;IACNV,EAAA,CAAAyB,SAAA,cAA6B;IACjCzB,EAAA,CAAAU,YAAA,EAAM;;;;;IAbgFV,EAAA,CAAAW,SAAA,EAAmB;IAAnBX,EAAA,CAAAiD,qBAAA,OAAArB,YAAA,CAAAsB,EAAA,CAAmB;IAAjGlD,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAAa,eAAA,IAAAO,GAAA,EAAAd,MAAA,CAAAe,iBAAA,CAAA6B,EAAA,KAAAtB,YAAA,CAAAsB,EAAA,EAA6E;IAE5ClD,EAAA,CAAAW,SAAA,GAAoD;IAApDX,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAoB,gBAAA,CAAAyB,kBAAA,CAAAvB,YAAA,EAAoD;IAE3E5B,EAAA,CAAAW,SAAA,EAA2B;IAA3BX,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA8C,WAAA,CAAAC,SAAA,CAA2B;IAI5BrD,EAAA,CAAAW,SAAA,EAA2B;IAA3BX,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA8C,WAAA,CAAAC,SAAA,CAA2B;IACzBrD,EAAA,CAAAW,SAAA,EAA2B;IAA3BX,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA8C,WAAA,CAAAE,SAAA,CAA2B;;;;;IAZ9CtD,EALA,CAAA4C,UAAA,IAAAW,wDAAA,kBAE8F,IAAAC,wDAAA,kBAG/B;;;;IALzDxD,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA8C,WAAA,CAAAC,SAAA,CAA2B;IAKNrD,EAAA,CAAAW,SAAA,EAAqB;IAArBX,EAAA,CAAAY,UAAA,YAAAN,MAAA,CAAAmD,kBAAA,CAAqB;;;;;;IAmBxDzD,EAAA,CAAAC,cAAA,mBAC0I;IAD7BD,EAAA,CAAAE,UAAA,mBAAAwD,0EAAAC,MAAA;MAAA3D,EAAA,CAAAI,aAAA,CAAAwD,GAAA;MAAA5D,EAAA,CAAAO,aAAA;MAAA,MAAAsD,KAAA,GAAA7D,EAAA,CAAA8D,WAAA;MAAA,OAAA9D,EAAA,CAAAQ,WAAA,CAASqD,KAAA,CAAAE,MAAA,CAAAJ,MAAA,CAAiB;IAAA,EAAC;IAExI3D,EAAA,CAAAU,YAAA,EAAW;;;;IADXV,EAAA,CAAAY,UAAA,UAAAN,MAAA,CAAAgB,oBAAA,GAAAhB,MAAA,CAAAoB,gBAAA,CAAAQ,wBAAA,CAAA5B,MAAA,CAAAe,iBAAA,CAAAc,iBAAA,uBAAyI;;;;;IAOrHnC,EAFJ,CAAAC,cAAA,aAC2F,eACL;IAC9ED,EAAA,CAAAyB,SAAA,cAA6D;IAACzB,EAAA,CAAAmB,MAAA,GAClE;IAAAnB,EAAA,CAAAU,YAAA,EAAO;IACPV,EAAA,CAAAC,cAAA,eAA8F;IAC1FD,EAAA,CAAAyB,SAAA,cAA4E;IAC5EzB,EAAA,CAAAmB,MAAA,GAAyC;IACjDnB,EADiD,CAAAU,YAAA,EAAO,EACnD;;;;IALiEV,EAAA,CAAAW,SAAA,GAClE;IADkEX,EAAA,CAAAgE,kBAAA,MAAAC,YAAA,CAAA5B,QAAA,QAClE;IAGIrC,EAAA,CAAAW,SAAA,GAAyC;IAAzCX,EAAA,CAAAgE,kBAAA,MAAAC,YAAA,CAAAC,UAAA,sBAAyC;;;;;IARzDlE,EAAA,CAAAC,cAAA,aAAgB;IACRD,EAAA,CAAA4C,UAAA,IAAAuB,uDAAA,iBAC2F;IAQnGnE,EAAA,CAAAU,YAAA,EAAK;;;;IAT6BV,EAAA,CAAAW,SAAA,EAAqB;IAArBX,EAAA,CAAAY,UAAA,YAAAN,MAAA,CAAAmD,kBAAA,CAAqB;;;;;;IAsB3DzD,EAAA,CAAAC,cAAA,iBAEkG;IAFjDD,EAAA,CAAAE,UAAA,mBAAAkE,sEAAA;MAAApE,EAAA,CAAAI,aAAA,CAAAiE,IAAA;MAAA,MAAA/D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAY,iBAAA,EAAmB;IAAA,EAAC;IAG1ElB,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAmB,MAAA,GAAsB;IACtDnB,EADsD,CAAAU,YAAA,EAAO,EACvD;;;;IAFFV,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAAa,eAAA,IAAAO,GAAA,EAAAd,MAAA,CAAAe,iBAAA,cAAAf,MAAA,CAAAgB,oBAAA,EAA6F;IACjEtB,EAAA,CAAAW,SAAA,GAAsB;IAAtBX,EAAA,CAAAuB,iBAAA,CAAAjB,MAAA,CAAAkB,kBAAA,CAAsB;;;;;IAUnBxB,EAAA,CAAAyB,SAAA,cACiD;;;;;IACxEzB,EAAA,CAAAyB,SAAA,cAE+B;;;;;;IAD3BzB,EAD+B,CAAAY,UAAA,QAAAN,MAAA,CAAAoB,gBAAA,CAAAC,6BAAA,CAAA2C,aAAA,GAAAtE,EAAA,CAAA6B,aAAA,CAAiE,YAAA7B,EAAA,CAAAa,eAAA,IAAAiB,GAAA,EAAAyC,kBAAA,CAAAvC,SAAA,CAAAC,QAAA,4BACN;;;;;IAE9FjC,EAAA,CAAAC,cAAA,WACkC;IAAAD,EAAA,CAAAmB,MAAA,GAA0E;IAAAnB,EAAA,CAAAU,YAAA,EAAO;;;;;IAAjFV,EAAA,CAAAW,SAAA,EAA0E;IAA1EX,EAAA,CAAAuB,iBAAA,CAAAjB,MAAA,CAAAoB,gBAAA,CAAAQ,wBAAA,CAAAoC,aAAA,CAAAnC,iBAAA,EAA0E;;;;;IAC5GnC,EAAA,CAAAC,cAAA,WAAoC;IAAAD,EAAA,CAAAmB,MAAA,GAAwD;IAAAnB,EAAA,CAAAU,YAAA,EAAO;;;;IAA/DV,EAAA,CAAAW,SAAA,EAAwD;IAAxDX,EAAA,CAAAoC,kBAAA,KAAAkC,aAAA,CAAAjC,QAAA,SAAAiC,aAAA,CAAAhC,OAAA,CAAAC,SAAA,KAAwD;;;;;;IAXpGvC,EADJ,CAAAC,cAAA,cAAwF,iBAGkB;IAFjGD,EAAA,CAAAE,UAAA,mBAAAsE,qFAAA;MAAA,MAAAF,aAAA,GAAAtE,EAAA,CAAAI,aAAA,CAAAqE,IAAA,EAAA/B,SAAA;MAAA,MAAApC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqC,iBAAA,CAAA2B,aAAA,CAA4B;IAAA,EAAC;IAIvCtE,EAAA,CAAAC,cAAA,eAA2B;IAOvBD,EAPuB,CAAA4C,UAAA,IAAA8B,qEAAA,kBACiD,IAAAC,qEAAA,kBAGzC,IAAAC,sEAAA,mBAEG,IAAAC,sEAAA,mBACE;IAE5C7E,EADI,CAAAU,YAAA,EAAO,EACL;IACNV,EAAA,CAAAyB,SAAA,cAAwC;IAC5CzB,EAAA,CAAAU,YAAA,EAAM;;;;;IAbgFV,EAAA,CAAAW,SAAA,EAAmB;IAAnBX,EAAA,CAAAiD,qBAAA,OAAAqB,aAAA,CAAApB,EAAA,CAAmB;IAAjGlD,EAFwD,CAAAY,UAAA,aAAA0D,aAAA,CAAAQ,IAAA,qCAA+D,YAAA9E,EAAA,CAAAa,eAAA,IAAAO,GAAA,EAAAd,MAAA,CAAAe,iBAAA,CAAA6B,EAAA,KAAAoB,aAAA,CAAApB,EAAA,EAE1C;IAE5ClD,EAAA,CAAAW,SAAA,GAAoD;IAApDX,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAoB,gBAAA,CAAAyB,kBAAA,CAAAmB,aAAA,EAAoD;IAE3EtE,EAAA,CAAAW,SAAA,EAA2B;IAA3BX,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA8C,WAAA,CAAAC,SAAA,CAA2B;IAI5BrD,EAAA,CAAAW,SAAA,EAA2B;IAA3BX,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA8C,WAAA,CAAAC,SAAA,CAA2B;IACzBrD,EAAA,CAAAW,SAAA,EAA2B;IAA3BX,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA8C,WAAA,CAAAE,SAAA,CAA2B;;;;;IAb9CtD,EAAA,CAAA+E,uBAAA,GAA8D;IAC9D/E,EAAA,CAAA4C,UAAA,IAAAoC,+DAAA,kBAAwF;;;;;;IAA7DhF,EAAA,CAAAW,SAAA,EAA8C;IAA9CX,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAAiF,WAAA,OAAA3E,MAAA,CAAAmD,kBAAA,EAAAzD,EAAA,CAAAa,eAAA,IAAAqE,GAAA,EAAA5E,MAAA,CAAA6E,IAAA,GAA8C;;;;;;IAdrEnF,EAHR,CAAAC,cAAA,cAAgF,eAE/B,gBAEoB;IAD1CD,EAAA,CAAAoF,gBAAA,2BAAAC,0EAAA1B,MAAA;MAAA3D,EAAA,CAAAI,aAAA,CAAAkF,IAAA;MAAA,MAAAhF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAAP,EAAA,CAAAuF,kBAAA,CAAAjF,MAAA,CAAA6E,IAAA,EAAAxB,MAAA,MAAArD,MAAA,CAAA6E,IAAA,GAAAxB,MAAA;MAAA,OAAA3D,EAAA,CAAAQ,WAAA,CAAAmD,MAAA;IAAA,EAAkB;IAArC3D,EAAA,CAAAU,YAAA,EAC6D;IAC7DV,EAAA,CAAAyB,SAAA,YAAyC;IAC7CzB,EAAA,CAAAU,YAAA,EAAO;IAEPV,EAAA,CAAAC,cAAA,cAAqG;IACrGD,EAAA,CAAA4C,UAAA,IAAA4C,gDAAA,kBAEkG;IAGlGxF,EAAA,CAAAyB,SAAA,cAA+F;IAE/FzB,EAAA,CAAA4C,UAAA,IAAA6C,yDAAA,2BAA8D;IAsBtDzF,EAFR,CAAAC,cAAA,cAA8B,cACyD,iBAC/B;IAAAD,EAAA,CAAAmB,MAAA,mBAAW;IAAAnB,EAAA,CAAAU,YAAA,EAAQ;IACnEV,EAAA,CAAAC,cAAA,yBACsC;IAAlCD,EAAA,CAAAE,UAAA,sBAAAwF,8EAAA/B,MAAA;MAAA3D,EAAA,CAAAI,aAAA,CAAAkF,IAAA;MAAA,MAAAhF,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAYF,MAAA,CAAAqF,YAAA,CAAAhC,MAAA,CAAoB;IAAA,EAAC;IAAC3D,EAAA,CAAAU,YAAA,EAAgB;IACtDV,EAAA,CAAAC,cAAA,iBAAgD;IAAAD,EAAA,CAAAmB,MAAA,gBAAQ;IAIpEnB,EAJoE,CAAAU,YAAA,EAAQ,EAC9D,EACJ,EACJ,EACA;;;;IA7C+BV,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAAa,eAAA,IAAA+E,GAAA,EAAAtF,MAAA,CAAAS,WAAA,EAA0C;IAGpDf,EAAA,CAAAW,SAAA,GAAkB;IAAlBX,EAAA,CAAA6F,gBAAA,YAAAvF,MAAA,CAAA6E,IAAA,CAAkB;IAKqBnF,EAAA,CAAAW,SAAA,GAAsC;IAAtCX,EAAA,CAAA8F,WAAA,WAAAxF,MAAA,CAAAyF,cAAA,QAAsC;IAC9F/F,EAAA,CAAAW,SAAA,EAA2B;IAA3BX,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAA8C,WAAA,CAAAC,SAAA,CAA2B;IAOlBrD,EAAA,CAAAW,SAAA,GAA6C;IAA7CX,EAAA,CAAAY,UAAA,SAAAN,MAAA,CAAAmD,kBAAA,IAAAnD,MAAA,CAAAe,iBAAA,CAA6C;IAuBErB,EAAA,CAAAW,SAAA,GAAgC;IAAhCX,EAAA,CAAAY,UAAA,YAAAN,MAAA,CAAA0F,oBAAA,CAAgC;;;AD1EtG,OAAM,MAAOC,2BAA2B;EAoBtCC,YACSxE,gBAAkC,EAClC0B,WAAwB,EACvB+C,cAA8B,EAC9BC,aAA4B;IAH7B,KAAA1E,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAA0B,WAAW,GAAXA,WAAW;IACV,KAAA+C,cAAc,GAAdA,cAAc;IACd,KAAAC,aAAa,GAAbA,aAAa;IAvBf,KAAAC,IAAI,GAAG,IAAItG,OAAO,EAAE;IAC5B,KAAAgB,WAAW,GAAG,KAAK;IACnB,KAAAiF,oBAAoB,GAAG,KAAK;IAInB,KAAAM,wBAAwB,GAAG,KAAK;IAEhC,KAAA9E,kBAAkB,GAAG,YAAY;IACjC,KAAA+E,WAAW,GAAG,KAAK;IAClB,KAAAC,eAAe,GAAG,IAAI3G,YAAY,EAAE;IACpC,KAAA4G,iBAAiB,GAAG,IAAI5G,YAAY,EAAE;IACtC,KAAA6G,SAAS,GAAG,IAAI7G,YAAY,EAAE;IACW,KAAA8G,UAAU,GAAG,EAAgB;IAC5C,KAAAC,eAAe,GAAiB,EAAkB;IAC1C,KAAAC,WAAW,GAAgB,EAAiB;IACxF,KAAApD,kBAAkB,GAAgB,EAAE;IACpC,KAAApC,iBAAiB,GAAG,EAAe;IACnC,KAAAyF,OAAO,GAAG,KAAK;EAMX;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACV,IAAI,CAACW,GAAG,CAAC,IAAI,CAACZ,aAAa,CAACa,cAAc,CAACC,IAAI,EAAE,CAACC,SAAS,CAACC,GAAG,IAAG,CACvE,CAAC,CAAC,CAAC;IACH,IAAI,CAACf,IAAI,CAACW,GAAG,CAAC,IAAI,CAACb,cAAc,CAACkB,UAAU,CAACH,IAAI,EAAE,CAACC,SAAS,CAACC,GAAG,IAAG;MAClE,IAAIA,GAAG,EAAE;QACP,IAAI,CAACN,OAAO,GAAGM,GAAG,CAACE,MAAiB;QACpC,IAAIF,GAAG,CAACG,MAAM,EAAE;UACd;QAAA,CACD,MAAM,CACP;MACF;IACF,CAAC,CAAC,CAAC;EACL;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,CAAC/D,kBAAkB,GAAG,IAAI,CAACgE,UAAU;IAEzC;IACA,IAAI,IAAI,CAACrE,WAAW,CAACE,SAAS,EAAE;MAC9B;MACA,IAAI,CAAC0C,oBAAoB,GAAG,IAAI;MAChC;MACA,IAAI,CAACL,YAAY,CAAC;QAAE+B,OAAO,EAAE;MAAI,CAAE,CAAC;IACtC;IAEA;IACA,IAAI,IAAI,CAACjE,kBAAkB,IAAI,IAAI,CAACL,WAAW,CAACC,SAAS,EAAE;MACzD;MACA,IAAI,CAACI,kBAAkB,GAAG,IAAI,CAAC/B,gBAAgB,CAACiG,yBAAyB,CAAC,IAAI,CAACF,UAAU,CAAC;MAC1F;MACA,IAAI,CAAC9B,YAAY,CAAC;QAAE+B,OAAO,EAAE;MAAK,CAAE,CAAC;IACvC;IAEA;IACA,IAAI,CAACE,0BAA0B,EAAE;IAEjC;IACA,IAAI,IAAI,CAACtB,wBAAwB,EAAE;MACjC;MACA,IAAI,IAAI,CAAC7C,kBAAkB,IAAI,CAAC,IAAI,CAAC0C,cAAc,CAAC0B,eAAe,CAAC,IAAI,CAACpE,kBAAkB,CAAC,EAAE;QAC5F;QACAqE,OAAO,CAACC,GAAG,CAAC,IAAI,CAACtE,kBAAkB,CAAC,CAAC,CAAC,CAAC;QACvC;QACA,IAAI,CAACd,iBAAiB,CAAC,IAAI,CAACc,kBAAkB,CAAC,CAAC,CAAC,CAAC;MACpD;IACF,CAAC,MAAM;MACL;MACA,IAAI,IAAI,CAACL,WAAW,CAACC,SAAS,EAAE;QAC9B;QACA,IAAI,CAAC2E,eAAe,EAAE;MACxB;IACF;IAEA;IACA,IAAI,IAAI,CAACnB,WAAW,EAAE;MACpB;MACA,IAAI,CAACA,WAAW,CAACoB,OAAO,EAAE;IAC5B;EACF;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACN,0BAA0B,EAAE;EACnC;EAEAO,WAAWA,CAAA;IACT,IAAI,CAAC9B,IAAI,CAAC+B,WAAW,EAAE;EACzB;EAEA,IAAI9G,oBAAoBA,CAAA;IACtB,OAAO+G,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjH,iBAAiB,CAAC,CAACkH,MAAM,GAAG,CAAC;EACvD;EAEArH,iBAAiBA,CAAA;IACf,IAAI,CAAC8G,eAAe,EAAE;IACtB,IAAI,CAAC3G,iBAAiB,GAAG,EAAe;IACxC,IAAI,CAACmF,eAAe,CAACgC,IAAI,CAAC,IAAI,CAAC;IAC/B,IAAI,CAAC,IAAI,CAACrC,cAAc,CAAC0B,eAAe,CAAC,IAAI,CAACjB,eAAe,CAAC,EAAE;MAC9D,IAAI,CAACA,eAAe,CAAC6B,IAAI,EAAE;IAC7B;EACF;EAEA9F,iBAAiBA,CAAC+F,SAAoB;IACpC,IAAI,IAAI,CAACtF,WAAW,CAACC,SAAS,EAAE;MAC9B,IAAI,CAACsF,kBAAkB,EAAE;IAC3B;IACA,IAAI,CAACtH,iBAAiB,GAAGqH,SAAS;IAClC,IAAI,CAACjC,iBAAiB,CAAC+B,IAAI,CAACE,SAAS,CAAC;IACtC,IAAI,CAAC,IAAI,CAACvC,cAAc,CAAC0B,eAAe,CAAC,IAAI,CAACjB,eAAe,CAAC,EAAE;MAC9D,IAAI,CAACA,eAAe,CAAC6B,IAAI,EAAE;IAC7B;EACF;EAEAG,WAAWA,CAAA;IACT,EAAE,CAACC,OAAO,CAACC,IAAI,CAACC,QAAQ,CAACC,sBAAsB,CAAC,iBAAiB,CAAC,EAAGC,EAAO,IAAI;MAC9EA,EAAE,CAACjH,SAAS,CAACkH,MAAM,CAAC,wBAAwB,CAAC;IAC/C,CAAC,CAAC;EACJ;EAEAlB,eAAeA,CAAA;IACb,IAAI,IAAI,CAACrB,UAAU,IAAI,IAAI,CAACA,UAAU,CAACwC,aAAa,EAAE;MACpD,IAAI,CAACxC,UAAU,CAACwC,aAAc,CAACnH,SAAS,CAACgF,GAAG,CAAC,wBAAwB,CAAC;IACxE;IACA;EACF;EAEA2B,kBAAkBA,CAAA;IAChB,MAAMS,cAAc,GAAGL,QAAQ,CAACM,cAAc,CAAC,UAAU,CAAC;IAC1D,IAAID,cAAc,IAAIA,cAAc,CAACpH,SAAS,CAACC,QAAQ,CAAC,wBAAwB,CAAC,EAAE;MACjFmH,cAAc,CAACpH,SAAS,CAACkH,MAAM,CAAC,wBAAwB,CAAC;IAC3D;EACF;EAEAzI,qBAAqBA,CAAA;IACnB,IAAI,CAACM,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IACpC,IAAI,CAAC2F,SAAS,CAAC8B,IAAI,CAAC,IAAI,CAACzH,WAAW,CAAC;EACvC;EAEA4E,YAAYA,CAAC2D,KAA2B;IACtCxB,OAAO,CAACC,GAAG,CAACuB,KAAK,CAAC;IAClB,IAAI,CAACA,KAAK,CAAC5B,OAAO,EAAE;MAClB,IAAI,CAACjE,kBAAkB,GAAG,IAAI,CAACgE,UAAU,CAAC8B,MAAM,CAAEN,EAAa,IAAKA,EAAE,CAACO,MAAM,CAACC,WAAW,EAAE,KAAK,IAAI,CAAC/H,gBAAgB,CAACgI,iBAAiB,CAACC,QAAQ,CAACF,WAAW,EAAE,CAAC;IACjK,CAAC,MAAM;MACL,IAAI,CAAChG,kBAAkB,GAAG,IAAI,CAACgE,UAAU;IAC3C;EACF;EAEAG,0BAA0BA,CAAA;IACxB,IAAI,IAAI,CAACxE,WAAW,CAACE,SAAS,IAAI,IAAI,CAACG,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAAC8E,MAAM,KAAK,CAAC,EAAE;MACjG;MACA,IAAI,CAAC9H,qBAAqB,EAAE;IAC9B;EACF;EAEAmJ,cAAcA,CAACN,KAAY;IACzBA,KAAK,CAACO,eAAe,EAAE,CAAC,CAAC;IACzB,MAAMC,QAAQ,GAAGf,QAAQ,CAACgB,aAAa,CAAC,mBAAmB,CAAC;IAC5DD,QAAS,CAAC9H,SAAS,CAAC+B,MAAM,CAAC,MAAM,CAAC;EACpC;EAAC,QAAAiG,CAAA,G;qBArKU/D,2BAA2B,EAAAjG,EAAA,CAAAiK,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAnK,EAAA,CAAAiK,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAArK,EAAA,CAAAiK,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAvK,EAAA,CAAAiK,iBAAA,CAAAO,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA3BzE,2BAA2B;IAAA0E,SAAA;IAAAC,SAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;qCAF3B,CAAChL,UAAU,CAAC,GAAAE,EAAA,CAAAgL,oBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,qCAAAP,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCdzB9K,EAAA,CAAAC,cAAA,aAAyG;QACrGD,EAAA,CAAA4C,UAAA,IAAA0I,0CAAA,iBAA6D;QAK7DtL,EAAA,CAAAC,cAAA,2BAAwC;QACpCD,EAAA,CAAA4C,UAAA,IAAA2I,kDAAA,yBAAiC;QAwBrCvL,EAAA,CAAAU,YAAA,EAAiB;QACjBV,EAAA,CAAA4C,UAAA,IAAA4I,+CAAA,sBAC0I;QAE1IxL,EAAA,CAAAC,cAAA,2BAA4E;QACxED,EAAA,CAAA4C,UAAA,IAAA6I,kDAAA,0BAA6C;QAajDzL,EAAA,CAAAU,YAAA,EAAiB;QAEjBV,EAAA,CAAA4C,UAAA,IAAA8I,0CAAA,mBAAgF;QA8CpF1L,EAAA,CAAAU,YAAA,EAAM;;;QAjG4DV,EAAA,CAAAY,UAAA,YAAAZ,EAAA,CAAAa,eAAA,IAAA8K,GAAA,EAAAZ,GAAA,CAAAhK,WAAA,EAAsC;QAC9Ff,EAAA,CAAAW,SAAA,EAAa;QAAbX,EAAA,CAAAY,UAAA,SAAAmK,GAAA,CAAAjE,OAAA,CAAa;QA+BR9G,EAAA,CAAAW,SAAA,GAAc;QAAdX,EAAA,CAAAY,UAAA,UAAAmK,GAAA,CAAAjE,OAAA,CAAc;QAGc9G,EAAA,CAAAW,SAAA,EAAoC;QAApCX,EAAA,CAAAY,UAAA,qCAAoC;QAgBpEZ,EAAA,CAAAW,SAAA,GAAa;QAAbX,EAAA,CAAAY,UAAA,SAAAmK,GAAA,CAAAjE,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}