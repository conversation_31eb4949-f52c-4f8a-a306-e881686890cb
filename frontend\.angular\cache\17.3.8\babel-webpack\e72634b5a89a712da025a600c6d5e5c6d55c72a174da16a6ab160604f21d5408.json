{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let WindowService = /*#__PURE__*/(() => {\n  class WindowService {\n    constructor() {}\n    get windowRef() {\n      return window;\n    }\n    static #_ = this.ɵfac = function WindowService_Factory(t) {\n      return new (t || WindowService)();\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: WindowService,\n      factory: WindowService.ɵfac\n    });\n  }\n  return WindowService;\n})();", "map": {"version": 3, "names": ["WindowService", "constructor", "windowRef", "window", "_", "_2", "factory", "ɵfac"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\services\\window.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\n\r\n@Injectable()\r\nexport class WindowService {\r\n\r\n    constructor() {\r\n    }\r\n\r\n    get windowRef() {\r\n        return window;\r\n    }\r\n\r\n}"], "mappings": ";AAGA,WAAaA,aAAa;EAApB,MAAOA,aAAa;IAEtBC,YAAA,GACA;IAEA,IAAIC,SAASA,CAAA;MACT,OAAOC,MAAM;IACjB;IAAC,QAAAC,CAAA,G;uBAPQJ,aAAa;IAAA;IAAA,QAAAK,EAAA,G;aAAbL,aAAa;MAAAM,OAAA,EAAbN,aAAa,CAAAO;IAAA;;SAAbP,aAAa;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}