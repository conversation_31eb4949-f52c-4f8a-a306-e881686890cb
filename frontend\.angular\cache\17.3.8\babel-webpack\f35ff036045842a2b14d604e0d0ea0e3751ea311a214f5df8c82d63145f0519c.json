{"ast": null, "code": "var isFunction = function (fn) {\n  return typeof fn === 'function';\n};\n/**\n * Subscription sink that holds Observable subscriptions\n * until you call unsubscribe on it in ngOnDestroy.\n */\nvar SubSink = /** @class */function () {\n  /**\n   * Subscription sink that holds Observable subscriptions\n   * until you call unsubscribe on it in ngOnDestroy.\n   *\n   * @example\n   * In Angular:\n   * ```\n   *   private subs = new SubSink();\n   *   ...\n   *   this.subs.sink = observable$.subscribe(...)\n   *   this.subs.add(observable$.subscribe(...));\n   *   ...\n   *   ngOnDestroy() {\n   *     this.subs.unsubscribe();\n   *   }\n   * ```\n   */\n  function SubSink() {\n    this._subs = [];\n  }\n  /**\n   * Add subscriptions to the tracked subscriptions\n   * @example\n   *  this.subs.add(observable$.subscribe(...));\n   */\n  SubSink.prototype.add = function () {\n    var subscriptions = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      subscriptions[_i] = arguments[_i];\n    }\n    this._subs = this._subs.concat(subscriptions);\n  };\n  Object.defineProperty(SubSink.prototype, \"sink\", {\n    /**\n     * Assign subscription to this sink to add it to the tracked subscriptions\n     * @example\n     *  this.subs.sink = observable$.subscribe(...);\n     */\n    set: function (subscription) {\n      this._subs.push(subscription);\n    },\n    enumerable: true,\n    configurable: true\n  });\n  /**\n   * Unsubscribe to all subscriptions in ngOnDestroy()\n   * @example\n   *   ngOnDestroy() {\n   *     this.subs.unsubscribe();\n   *   }\n   */\n  SubSink.prototype.unsubscribe = function () {\n    this._subs.forEach(function (sub) {\n      return sub && isFunction(sub.unsubscribe) && sub.unsubscribe();\n    });\n    this._subs = [];\n  };\n  return SubSink;\n}();\nexport { SubSink };\n//# sourceMappingURL=subsink.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}