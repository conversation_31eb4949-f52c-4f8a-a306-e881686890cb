{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class BuyPackageSuggestionBoxComponent {\n  static #_ = this.ɵfac = function BuyPackageSuggestionBoxComponent_Factory(t) {\n    return new (t || BuyPackageSuggestionBoxComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: BuyPackageSuggestionBoxComponent,\n    selectors: [[\"app-buy-package-suggestion-box\"]],\n    decls: 0,\n    vars: 0,\n    template: function BuyPackageSuggestionBoxComponent_Template(rf, ctx) {},\n    styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9jbGFzc3Jvb20vcGFja2FnZXMvYnV5LXBhY2thZ2UvYnV5LXBhY2thZ2Utc3VnZ2VzdGlvbi1ib3gvYnV5LXBhY2thZ2Utc3VnZ2VzdGlvbi1ib3guY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGNBQWM7QUFDaEIiLCJzb3VyY2VzQ29udGVudCI6WyI6aG9zdCB7XG4gIGRpc3BsYXk6IGJsb2NrO1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"],\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["BuyPackageSuggestionBoxComponent", "_", "_2", "selectors", "decls", "vars", "template", "BuyPackageSuggestionBoxComponent_Template", "rf", "ctx", "styles", "changeDetection"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\buy-package\\buy-package-suggestion-box\\buy-package-suggestion-box.component.ts"], "sourcesContent": ["import { CommonModule } from \"@angular/common\";\nimport { ChangeDetectionStrategy, Component } from '@angular/core';\n\n@Component({\n    selector: 'app-buy-package-suggestion-box',\n    templateUrl: './buy-package-suggestion-box.component.html',\n    styleUrl: './buy-package-suggestion-box.component.css',\n    changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class BuyPackageSuggestionBoxComponent { }\n"], "mappings": ";AASA,OAAM,MAAOA,gCAAgC;EAAA,QAAAC,CAAA,G;qBAAhCD,gCAAgC;EAAA;EAAA,QAAAE,EAAA,G;UAAhCF,gCAAgC;IAAAG,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,0CAAAC,EAAA,EAAAC,GAAA;IAAAC,MAAA;IAAAC,eAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}