{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport * as i0 from \"@angular/core\";\nexport let HomeworkModule = /*#__PURE__*/(() => {\n  class HomeworkModule {\n    static #_ = this.ɵfac = function HomeworkModule_Factory(t) {\n      return new (t || HomeworkModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: HomeworkModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, DropdownModule, SharedModule, MatTooltipModule]\n    });\n  }\n  return HomeworkModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}