{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Talossan [tzl]\n//! author : <PERSON> : https://github.com/robin0van0der0v\n//! author : <PERSON><PERSON><PERSON>\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n\n  // After the year there should be a slash and the amount of years since December 26, 1979 in Roman numerals.\n  // This is currently too difficult (maybe even impossible) to add.\n  var tzl = moment.defineLocale('tzl', {\n    months: 'Januar_Fevraglh_Març_Avrïu_Mai_Gün_<PERSON>_Guscht_Setemvar_Listopäts_Noemvar_Zecemvar'.split('_'),\n    monthsShort: 'Jan_Fev_Mar_Avr_<PERSON>_<PERSON>ü<PERSON>_Jul_Gus_Set_Lis_Noe_Zec'.split('_'),\n    weekdays: 'Súladi_Lúneçi_Maitzi_Márcuri_Xhúadi_Viénerçi_Sáturi'.split('_'),\n    weekdaysShort: 'Súl_Lún_Mai_Már_Xhú_Vié_Sát'.split('_'),\n    weekdaysMin: 'Sú_Lú_Ma_Má_Xh_Vi_Sá'.split('_'),\n    longDateFormat: {\n      LT: 'HH.mm',\n      LTS: 'HH.mm.ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D. MMMM [dallas] YYYY',\n      LLL: 'D. MMMM [dallas] YYYY HH.mm',\n      LLLL: 'dddd, [li] D. MMMM [dallas] YYYY HH.mm'\n    },\n    meridiemParse: /d\\'o|d\\'a/i,\n    isPM: function (input) {\n      return \"d'o\" === input.toLowerCase();\n    },\n    meridiem: function (hours, minutes, isLower) {\n      if (hours > 11) {\n        return isLower ? \"d'o\" : \"D'O\";\n      } else {\n        return isLower ? \"d'a\" : \"D'A\";\n      }\n    },\n    calendar: {\n      sameDay: '[oxhi à] LT',\n      nextDay: '[demà à] LT',\n      nextWeek: 'dddd [à] LT',\n      lastDay: '[ieiri à] LT',\n      lastWeek: '[sür el] dddd [lasteu à] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'osprei %s',\n      past: 'ja%s',\n      s: processRelativeTime,\n      ss: processRelativeTime,\n      m: processRelativeTime,\n      mm: processRelativeTime,\n      h: processRelativeTime,\n      hh: processRelativeTime,\n      d: processRelativeTime,\n      dd: processRelativeTime,\n      M: processRelativeTime,\n      MM: processRelativeTime,\n      y: processRelativeTime,\n      yy: processRelativeTime\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  function processRelativeTime(number, withoutSuffix, key, isFuture) {\n    var format = {\n      s: ['viensas secunds', \"'iensas secunds\"],\n      ss: [number + ' secunds', '' + number + ' secunds'],\n      m: [\"'n míut\", \"'iens míut\"],\n      mm: [number + ' míuts', '' + number + ' míuts'],\n      h: [\"'n þora\", \"'iensa þora\"],\n      hh: [number + ' þoras', '' + number + ' þoras'],\n      d: [\"'n ziua\", \"'iensa ziua\"],\n      dd: [number + ' ziuas', '' + number + ' ziuas'],\n      M: [\"'n mes\", \"'iens mes\"],\n      MM: [number + ' mesen', '' + number + ' mesen'],\n      y: [\"'n ar\", \"'iens ar\"],\n      yy: [number + ' ars', '' + number + ' ars']\n    };\n    return isFuture ? format[key][0] : withoutSuffix ? format[key][0] : format[key][1];\n  }\n  return tzl;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "tzl", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "meridiemParse", "isPM", "input", "toLowerCase", "meridiem", "hours", "minutes", "isLower", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "processRelativeTime", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "week", "dow", "doy", "number", "withoutSuffix", "key", "isFuture", "format"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/moment/locale/tzl.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Talossan [tzl]\n//! author : <PERSON> : https://github.com/robin0van0der0v\n//! author : <PERSON><PERSON><PERSON>\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    // After the year there should be a slash and the amount of years since December 26, 1979 in Roman numerals.\n    // This is currently too difficult (maybe even impossible) to add.\n    var tzl = moment.defineLocale('tzl', {\n        months: 'Januar_Fevraglh_Març_Avrïu_Mai_Gün_<PERSON>_<PERSON>_Setemvar_Listopäts_Noemvar_Zecemvar'.split(\n            '_'\n        ),\n        monthsShort: 'Jan_Fev_Mar_Avr_<PERSON>_<PERSON>ü<PERSON>_Jul_<PERSON>_Set_Lis_Noe_Zec'.split('_'),\n        weekdays: 'Súladi_Lúneçi_Maitzi_Márcuri_Xhúadi_Viénerçi_Sáturi'.split('_'),\n        weekdaysShort: 'Súl_Lún_Mai_Már_Xhú_Vié_Sát'.split('_'),\n        weekdaysMin: 'Sú_Lú_Ma_Má_Xh_Vi_Sá'.split('_'),\n        longDateFormat: {\n            LT: 'HH.mm',\n            LTS: 'HH.mm.ss',\n            L: 'DD.MM.YYYY',\n            LL: 'D. MMMM [dallas] YYYY',\n            LLL: 'D. MMMM [dallas] YYYY HH.mm',\n            LLLL: 'dddd, [li] D. MMMM [dallas] YYYY HH.mm',\n        },\n        meridiemParse: /d\\'o|d\\'a/i,\n        isPM: function (input) {\n            return \"d'o\" === input.toLowerCase();\n        },\n        meridiem: function (hours, minutes, isLower) {\n            if (hours > 11) {\n                return isLower ? \"d'o\" : \"D'O\";\n            } else {\n                return isLower ? \"d'a\" : \"D'A\";\n            }\n        },\n        calendar: {\n            sameDay: '[oxhi à] LT',\n            nextDay: '[demà à] LT',\n            nextWeek: 'dddd [à] LT',\n            lastDay: '[ieiri à] LT',\n            lastWeek: '[sür el] dddd [lasteu à] LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: 'osprei %s',\n            past: 'ja%s',\n            s: processRelativeTime,\n            ss: processRelativeTime,\n            m: processRelativeTime,\n            mm: processRelativeTime,\n            h: processRelativeTime,\n            hh: processRelativeTime,\n            d: processRelativeTime,\n            dd: processRelativeTime,\n            M: processRelativeTime,\n            MM: processRelativeTime,\n            y: processRelativeTime,\n            yy: processRelativeTime,\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n        ordinal: '%d.',\n        week: {\n            dow: 1, // Monday is the first day of the week.\n            doy: 4, // The week that contains Jan 4th is the first week of the year.\n        },\n    });\n\n    function processRelativeTime(number, withoutSuffix, key, isFuture) {\n        var format = {\n            s: ['viensas secunds', \"'iensas secunds\"],\n            ss: [number + ' secunds', '' + number + ' secunds'],\n            m: [\"'n míut\", \"'iens míut\"],\n            mm: [number + ' míuts', '' + number + ' míuts'],\n            h: [\"'n þora\", \"'iensa þora\"],\n            hh: [number + ' þoras', '' + number + ' þoras'],\n            d: [\"'n ziua\", \"'iensa ziua\"],\n            dd: [number + ' ziuas', '' + number + ' ziuas'],\n            M: [\"'n mes\", \"'iens mes\"],\n            MM: [number + ' mesen', '' + number + ' mesen'],\n            y: [\"'n ar\", \"'iens ar\"],\n            yy: [number + ' ars', '' + number + ' ars'],\n        };\n        return isFuture\n            ? format[key][0]\n            : withoutSuffix\n              ? format[key][0]\n              : format[key][1];\n    }\n\n    return tzl;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;;EAEA;EACA;EACA,IAAIC,GAAG,GAAGD,MAAM,CAACE,YAAY,CAAC,KAAK,EAAE;IACjCC,MAAM,EAAE,qFAAqF,CAACC,KAAK,CAC/F,GACJ,CAAC;IACDC,WAAW,EAAE,iDAAiD,CAACD,KAAK,CAAC,GAAG,CAAC;IACzEE,QAAQ,EAAE,qDAAqD,CAACF,KAAK,CAAC,GAAG,CAAC;IAC1EG,aAAa,EAAE,6BAA6B,CAACH,KAAK,CAAC,GAAG,CAAC;IACvDI,WAAW,EAAE,sBAAsB,CAACJ,KAAK,CAAC,GAAG,CAAC;IAC9CK,cAAc,EAAE;MACZC,EAAE,EAAE,OAAO;MACXC,GAAG,EAAE,UAAU;MACfC,CAAC,EAAE,YAAY;MACfC,EAAE,EAAE,uBAAuB;MAC3BC,GAAG,EAAE,6BAA6B;MAClCC,IAAI,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,YAAY;IAC3BC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAO,KAAK,KAAKA,KAAK,CAACC,WAAW,CAAC,CAAC;IACxC,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,KAAK,EAAEC,OAAO,EAAEC,OAAO,EAAE;MACzC,IAAIF,KAAK,GAAG,EAAE,EAAE;QACZ,OAAOE,OAAO,GAAG,KAAK,GAAG,KAAK;MAClC,CAAC,MAAM;QACH,OAAOA,OAAO,GAAG,KAAK,GAAG,KAAK;MAClC;IACJ,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,aAAa;MACtBC,OAAO,EAAE,aAAa;MACtBC,QAAQ,EAAE,aAAa;MACvBC,OAAO,EAAE,cAAc;MACvBC,QAAQ,EAAE,6BAA6B;MACvCC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,WAAW;MACnBC,IAAI,EAAE,MAAM;MACZC,CAAC,EAAEC,mBAAmB;MACtBC,EAAE,EAAED,mBAAmB;MACvBE,CAAC,EAAEF,mBAAmB;MACtBG,EAAE,EAAEH,mBAAmB;MACvBI,CAAC,EAAEJ,mBAAmB;MACtBK,EAAE,EAAEL,mBAAmB;MACvBM,CAAC,EAAEN,mBAAmB;MACtBO,EAAE,EAAEP,mBAAmB;MACvBQ,CAAC,EAAER,mBAAmB;MACtBS,EAAE,EAAET,mBAAmB;MACvBU,CAAC,EAAEV,mBAAmB;MACtBW,EAAE,EAAEX;IACR,CAAC;IACDY,sBAAsB,EAAE,WAAW;IACnCC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE;MACFC,GAAG,EAAE,CAAC;MAAE;MACRC,GAAG,EAAE,CAAC,CAAE;IACZ;EACJ,CAAC,CAAC;EAEF,SAAShB,mBAAmBA,CAACiB,MAAM,EAAEC,aAAa,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IAC/D,IAAIC,MAAM,GAAG;MACTtB,CAAC,EAAE,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;MACzCE,EAAE,EAAE,CAACgB,MAAM,GAAG,UAAU,EAAE,EAAE,GAAGA,MAAM,GAAG,UAAU,CAAC;MACnDf,CAAC,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC;MAC5BC,EAAE,EAAE,CAACc,MAAM,GAAG,QAAQ,EAAE,EAAE,GAAGA,MAAM,GAAG,QAAQ,CAAC;MAC/Cb,CAAC,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;MAC7BC,EAAE,EAAE,CAACY,MAAM,GAAG,QAAQ,EAAE,EAAE,GAAGA,MAAM,GAAG,QAAQ,CAAC;MAC/CX,CAAC,EAAE,CAAC,SAAS,EAAE,aAAa,CAAC;MAC7BC,EAAE,EAAE,CAACU,MAAM,GAAG,QAAQ,EAAE,EAAE,GAAGA,MAAM,GAAG,QAAQ,CAAC;MAC/CT,CAAC,EAAE,CAAC,QAAQ,EAAE,WAAW,CAAC;MAC1BC,EAAE,EAAE,CAACQ,MAAM,GAAG,QAAQ,EAAE,EAAE,GAAGA,MAAM,GAAG,QAAQ,CAAC;MAC/CP,CAAC,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC;MACxBC,EAAE,EAAE,CAACM,MAAM,GAAG,MAAM,EAAE,EAAE,GAAGA,MAAM,GAAG,MAAM;IAC9C,CAAC;IACD,OAAOG,QAAQ,GACTC,MAAM,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC,GACdD,aAAa,GACXG,MAAM,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC,GACdE,MAAM,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC;EAC1B;EAEA,OAAOrD,GAAG;AAEd,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}