{"ast": null, "code": "import { DOCUMENT } from '@angular/common';\nimport { EventEmitter, inject } from '@angular/core';\nimport { ChatService } from 'src/app/core/services/chat.service';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/core/services/general.service\";\nimport * as i3 from \"src/app/core/services/auth.service\";\nimport * as i4 from \"src/app/core/services/layout.service\";\nimport * as i5 from \"src/app/core/services/notification.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"primeng/badge\";\nconst _c0 = () => [\"/notifications\"];\nconst _c1 = () => [\"/chat\"];\nconst _c2 = a0 => ({\n  \"disabled-link\": a0\n});\nfunction MobileMenuComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"div\", 34);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction MobileMenuComponent_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 8)(1, \"a\", 9)(2, \"div\", 10);\n    i0.ɵɵelement(3, \"i\", 35);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(2, _c1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 0);\n  }\n}\nfunction MobileMenuComponent_ng_container_21_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 37);\n    i0.ɵɵlistener(\"click\", function MobileMenuComponent_ng_container_21_li_1_Template_li_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const item_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.navigateTo(item_r3.url));\n    });\n    i0.ɵɵelementStart(1, \"div\", 38)(2, \"span\", 39);\n    i0.ɵɵelement(3, \"lottie-player\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c2, item_r3.disabled))(\"tabindex\", item_r3.disabled ? -1 : null);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"width\", \"50\", \"px\")(\"height\", \"50\", \"px\")(\"transform\", \"scale(\" + item_r3.scale + \")\");\n    i0.ɵɵproperty(\"src\", item_r3.lottieIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r3.title, \" \");\n  }\n}\nfunction MobileMenuComponent_ng_container_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MobileMenuComponent_ng_container_21_li_1_Template, 5, 12, \"li\", 36);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.role.includes(ctx_r3.role));\n  }\n}\nfunction MobileMenuComponent_ng_container_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 41);\n    i0.ɵɵlistener(\"click\", function MobileMenuComponent_ng_container_22_Template_li_click_1_listener() {\n      const item_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(item_r6.url ? ctx_r3.navigateTo(item_r6.url) : item_r6.method());\n    });\n    i0.ɵɵelementStart(2, \"div\", 38)(3, \"span\", 39);\n    i0.ɵɵelement(4, \"lottie-player\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵstyleProp(\"width\", \"50\", \"px\")(\"height\", \"50\", \"px\")(\"transform\", \"scale(\" + item_r6.scale + \")\");\n    i0.ɵɵproperty(\"src\", item_r6.lottieIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r6.title, \" \");\n  }\n}\nexport class MobileMenuComponent {\n  constructor(router, generalService, authService, changeDetector, layoutService, notificationService, renderer, document) {\n    this.router = router;\n    this.generalService = generalService;\n    this.authService = authService;\n    this.changeDetector = changeDetector;\n    this.layoutService = layoutService;\n    this.notificationService = notificationService;\n    this.renderer = renderer;\n    this.document = document;\n    this.init = false;\n    this.opened = new EventEmitter();\n    this.chatService = inject(ChatService);\n    this.role = {};\n    this.active = false;\n    this.subs = new SubSink();\n    this.mobileMenuTitle = '';\n    this.burgerClasses = {\n      burger: '',\n      nava: '',\n      overlay: '',\n      body: '',\n      menu: 'hidden'\n    };\n    this.extraMobileMenuItems = [];\n    this.unreadNotifications = [];\n    this.socialLinks = {\n      linkedin: 'https://gr.linkedin.com/company/mylingotrip',\n      twitter: 'https://twitter.com/MyLingoTrip',\n      facebook: 'https://www.facebook.com/mylingotrip',\n      youtube: 'https://www.youtube.com/c/MyLingoTrip',\n      instagram: 'https://www.instagram.com/mylingotrip/'\n    };\n  }\n  ngOnInit() {\n    // this.burgerClick();\n    this.role = this.authService.getUserRole();\n    this.menuItems = this.layoutService.getMenuItems();\n    this.extraMobileMenuItems = this.layoutService.getExtraMobileMenuItems();\n    this.subs.add(this.notificationService.get().subscribe(res => {\n      // Filter notifications to get only the unread ones\n      this.unreadNotifications = res.filter(notification => !notification.isRead);\n    }));\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  ngAfterContentChecked() {\n    this.subs.sink = this.generalService.mobileMenuTitle.subscribe(res => {\n      this.mobileMenuTitle = res;\n      this.changeDetector.detectChanges();\n    });\n  }\n  onBurgerClicked() {\n    this.active = !this.active;\n    this.opened.emit();\n  }\n  toggleMobileMenuItems() {\n    var mobileMenuItems = document.getElementById('mobile-menu-items');\n    if (mobileMenuItems.clientHeight) {\n      mobileMenuItems.style.height = \"0\";\n    } else {\n      mobileMenuItems.style.height = window.innerHeight + \"px\";\n    }\n  }\n  logout() {\n    this.authService.logout();\n  }\n  navigateTo(route) {\n    this.onBurgerClicked();\n    this.burgerClick();\n    this.router.navigateByUrl(route, {\n      replaceUrl: true\n    });\n    // this.router.navigate([route]);\n  }\n  burgerClick() {\n    this.burgerClasses.burger = this.burgerClasses.burger === 'clicked' ? '' : 'clicked';\n    this.burgerClasses.overlay = this.burgerClasses.overlay === 'show' ? '' : 'show';\n    this.burgerClasses.nava = this.burgerClasses.nava === 'show' ? '' : 'show';\n    this.burgerClasses.menu = this.burgerClasses.menu === 'hidden' ? '' : 'hidden';\n    this.burgerClasses.body = 'overflow';\n    if (this.burgerClasses.burger === 'clicked') {\n      this.renderer.addClass(this.document.body, 'overflow-hidden');\n    } else {\n      this.renderer.removeClass(this.document.body, 'overflow-hidden');\n    }\n  }\n  static #_ = this.ɵfac = function MobileMenuComponent_Factory(t) {\n    return new (t || MobileMenuComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.GeneralService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i4.LayoutService), i0.ɵɵdirectiveInject(i5.NotificationService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(DOCUMENT));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MobileMenuComponent,\n    selectors: [[\"app-mobile-menu\"]],\n    inputs: {\n      init: \"init\",\n      menuItems: \"menuItems\"\n    },\n    outputs: {\n      opened: \"opened\"\n    },\n    decls: 48,\n    vars: 16,\n    consts: [[\"myDiv\", \"\"], [1, \"mobile-menu\", \"px-2\"], [1, \"ml-2\"], [\"href\", \"https://www.mylingotrip.com\", \"target\", \"_blank\"], [\"src\", \"/assets/icons/logo/mlt-logo-white.svg\", \"alt\", \"mlt-logo-white\", 1, \"logo-img\"], [4, \"ngIf\"], [1, \"flex\", \"ml-auto\"], [1, \"list-none\", \"select-none\", \"flex\", \"flex-row\", \"md:border-top-none\"], [\"id\", \"chats-dropdown\", 1, \"relative\", \"lg:pl-3\", \"lg:ml-2\", 2, \"z-index\", \"55\"], [\"title\", \"Chat\", 1, \"flex\", \"align-items-center\", \"text-indigo-100\", \"note-badge\", \"hover:text-indigo-50\", \"hover:bg-indigo-700\", \"font-medium\", \"border-round\", \"cursor-pointer\", 3, \"routerLink\"], [1, \"notaa\", \"flex\", \"align-items-center\", \"justify-content-center\", \"mr-3\", 2, \"z-index\", \"1\", \"pointer-events\", \"none\"], [\"pBadge\", \"\", 1, \"pi\", \"pi-bell\", \"pb-0\", \"text-2xl\", \"note-badge\", 3, \"value\"], [1, \"nava\", 3, \"ngClass\"], [1, \"wpo-site-footer\", 3, \"ngClass\"], [1, \"wpo-upper-footer\"], [1, \"container\"], [1, \"grid\", \"align-items-start\", \"justify-content-center\"], [1, \"col-12\", \"sm:col-12\", \"lg:col-2\", \"md:col-4\"], [1, \"widget\", \"link-widget\", \"flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\"], [1, \"pl-0\"], [4, \"ngFor\", \"ngForOf\"], [1, \"wpo-lower-footer\"], [1, \"row\"], [1, \"col-12\", \"text-center\"], [1, \"about-widget\", \"text-center\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"gap-1\", \"p-0\"], [\"target\", \"_blank\", 3, \"href\"], [\"src\", \"/assets/icons/navbar/facebook.svg\", \"alt\", \"facebook Image\"], [\"src\", \"/assets/icons/navbar/instagram.svg\", \"alt\", \"instagram Image\"], [\"src\", \"/assets/icons/navbar/linkedin.svg\", \"alt\", \"linkedin Image\"], [\"src\", \"/assets/icons/navbar/twitter.svg\", \"alt\", \"twitter Image\"], [\"src\", \"/assets/icons/navbar/youtube.svg\", \"alt\", \"youtube Image\"], [1, \"overlay\", 3, \"ngClass\"], [1, \"burger\", 3, \"click\", \"ngClass\"], [1, \"mobile-title-header\"], [\"pBadge\", \"\", 1, \"pi\", \"pi-comments\", \"pb-0\", \"text-2xl\", \"note-badge\", 3, \"value\"], [\"class\", \"hover:bg-indigo-600 pointer flex align-items-center font-3xl gap-2\", 3, \"ngClass\", \"tabindex\", \"click\", 4, \"ngIf\"], [1, \"hover:bg-indigo-600\", \"pointer\", \"flex\", \"align-items-center\", \"font-3xl\", \"gap-2\", 3, \"click\", \"ngClass\", \"tabindex\"], [1, \"icon-container\"], [1, \"lottie-wrapper\"], [\"background\", \"transparent\", \"speed\", \"1\", \"loop\", \"\", 3, \"src\"], [1, \"hover:bg-indigo-600\", \"pointer\", \"flex\", \"align-items-center\", \"font-3xl\", \"gap-2\", 3, \"click\"]],\n    template: function MobileMenuComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"a\", 3);\n        i0.ɵɵelement(3, \"img\", 4);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(4, MobileMenuComponent_ng_container_4_Template, 2, 0, \"ng-container\", 5);\n        i0.ɵɵelementStart(5, \"div\", 6)(6, \"ul\", 7, 0);\n        i0.ɵɵtemplate(8, MobileMenuComponent_Conditional_8_Template, 4, 3, \"li\", 8);\n        i0.ɵɵelementStart(9, \"li\", 8)(10, \"a\", 9)(11, \"div\", 10);\n        i0.ɵɵelement(12, \"i\", 11);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(13, \"div\", 12)(14, \"div\", 13)(15, \"div\", 14)(16, \"div\", 15)(17, \"div\", 16)(18, \"div\", 17)(19, \"div\", 18)(20, \"ul\", 19);\n        i0.ɵɵtemplate(21, MobileMenuComponent_ng_container_21_Template, 2, 1, \"ng-container\", 20)(22, MobileMenuComponent_ng_container_22_Template, 6, 8, \"ng-container\", 20);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(23, \"div\", 21)(24, \"div\", 15)(25, \"div\", 22)(26, \"div\", 23)(27, \"p\", 24);\n        i0.ɵɵtext(28, \"Let\\u2019s get social!\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(29, \"ul\", 25)(30, \"li\")(31, \"a\", 26);\n        i0.ɵɵelement(32, \"img\", 27);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(33, \"li\")(34, \"a\", 26);\n        i0.ɵɵelement(35, \"img\", 28);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(36, \"li\")(37, \"a\", 26);\n        i0.ɵɵelement(38, \"img\", 29);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(39, \"li\")(40, \"a\", 26);\n        i0.ɵɵelement(41, \"img\", 30);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(42, \"li\")(43, \"a\", 26);\n        i0.ɵɵelement(44, \"img\", 31);\n        i0.ɵɵelementEnd()()()()()()()()()();\n        i0.ɵɵelement(45, \"div\", 32);\n        i0.ɵɵelementStart(46, \"div\", 33);\n        i0.ɵɵlistener(\"click\", function MobileMenuComponent_Template_div_click_46_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.burgerClick());\n        });\n        i0.ɵɵelement(47, \"span\");\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.mobileMenuTitle !== \"\");\n        i0.ɵɵadvance(4);\n        i0.ɵɵconditional(8, ctx.chatService.isChatEnabled() ? 8 : -1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(15, _c0));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"value\", ctx.unreadNotifications.length > 0 ? ctx.unreadNotifications.length : 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngClass\", ctx.burgerClasses.nava);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngClass\", ctx.burgerClasses.menu);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngForOf\", ctx.menuItems);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngForOf\", ctx.extraMobileMenuItems);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"href\", ctx.socialLinks.facebook, i0.ɵɵsanitizeUrl);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"href\", ctx.socialLinks.instagram, i0.ɵɵsanitizeUrl);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"href\", ctx.socialLinks.linkedin, i0.ɵɵsanitizeUrl);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"href\", ctx.socialLinks.twitter, i0.ɵɵsanitizeUrl);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"href\", ctx.socialLinks.youtube, i0.ɵɵsanitizeUrl);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngClass\", ctx.burgerClasses.overlay);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngClass\", ctx.burgerClasses.burger);\n      }\n    },\n    dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i1.RouterLink, i7.BadgeDirective],\n    styles: [\".burger-menu[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  cursor: pointer;\\n}\\n.burger-menu[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.burger-menu[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]   .burger-inner[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 22px;\\n  left: 0px;\\n  top: 11px;\\n  border: 1px solid #ffffff;\\n  transition: border-color 0.2s ease-in 0.2s;\\n}\\n.burger-menu[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]   .burger-inner[_ngcontent-%COMP%]::before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  top: -6px;\\n  left: -1px;\\n  width: 22px;\\n  border: 1px solid #ffffff;\\n  transition: top 0.2s ease-in 0.3s, transform ease-in 0.2s;\\n}\\n.burger-menu[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]   .burger-inner[_ngcontent-%COMP%]::after {\\n  position: absolute;\\n  content: \\\"\\\";\\n  top: 4px;\\n  left: -1px;\\n  width: 22px;\\n  border: 1px solid #ffffff;\\n  transition: top 0.2s ease-in 0.3s, transform ease-in 0.2s;\\n}\\n.burger-menu.active[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]   .burger-inner[_ngcontent-%COMP%] {\\n  border-color: transparent;\\n  transition: border-color ease-in 0.1s;\\n}\\n.burger-menu.active[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]   .burger-inner[_ngcontent-%COMP%]::before {\\n  top: -1px;\\n  transform: rotate(-45deg);\\n  transition: top ease-out 0.2s, transform 0.2s ease-out 0.3s;\\n}\\n.burger-menu.active[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]   .burger-inner[_ngcontent-%COMP%]::after {\\n  top: -1px;\\n  transform: rotate(45deg);\\n  transition: top ease-out 0.2s, transform 0.2s ease-out 0.3s;\\n}\\n\\n.lottie-wrapper[_ngcontent-%COMP%] {\\n  mix-blend-mode: luminosity;\\n}\\n\\n.icon-container[_ngcontent-%COMP%] {\\n  border-radius: 1.25rem;\\n  mix-blend-mode: luminosity;\\n}\\n\\n.mobile-menu[_ngcontent-%COMP%] {\\n  width: 100%;\\n  position: fixed;\\n  background: rgb(101, 122, 239);\\n  background-image: linear-gradient(90deg, hsl(231, 56%, 31%) 0%, hsl(231, 54%, 33%) 26%, hsl(232, 52%, 36%) 48%, hsl(232, 50%, 38%) 65%, hsl(232, 49%, 41%) 76%, hsl(232, 47%, 44%) 84%, hsl(231, 46%, 46%) 90%, hsl(231, 45%, 49%) 94%, hsl(231, 47%, 52%) 97%, hsl(231, 52%, 54%) 100%);\\n  display: none;\\n  z-index: 100;\\n  color: var(--white);\\n  height: 64px;\\n  top: 0;\\n  \\n\\n\\n  \\n\\n\\n  \\n\\n\\n  \\n\\n\\n  \\n\\n\\n  \\n\\n\\n  \\n\\n\\n}\\n@media screen and (max-width: 768px) {\\n  .mobile-menu[_ngcontent-%COMP%] {\\n    display: flex;\\n    align-items: center;\\n    justify-content: space-between;\\n  }\\n}\\n.mobile-menu[_ngcontent-%COMP%]   .burger[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  position: relative;\\n  border-radius: 4px;\\n  z-index: 10;\\n  border-radius: 100px;\\n}\\n.mobile-menu[_ngcontent-%COMP%]   .burger[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin-top: 9px;\\n  margin-bottom: 9px;\\n  -webkit-user-select: none;\\n  user-select: none;\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  margin-left: -15px;\\n  margin-top: -1.5px;\\n}\\n.mobile-menu[_ngcontent-%COMP%]   .burger[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .mobile-menu[_ngcontent-%COMP%]   .burger[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::before, .mobile-menu[_ngcontent-%COMP%]   .burger[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::after {\\n  display: block;\\n  width: 30px;\\n  height: 3px;\\n  background-color: white;\\n  outline: 1px solid transparent;\\n  transition-property: background-color, transform;\\n  transition-duration: 0.3s;\\n}\\n.mobile-menu[_ngcontent-%COMP%]   .burger[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::before, .mobile-menu[_ngcontent-%COMP%]   .burger[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::after {\\n  position: absolute;\\n  content: \\\"\\\";\\n}\\n.mobile-menu[_ngcontent-%COMP%]   .burger[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::before {\\n  top: -9px;\\n}\\n.mobile-menu[_ngcontent-%COMP%]   .burger[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::after {\\n  top: 9px;\\n}\\n.mobile-menu[_ngcontent-%COMP%]   .burger.clicked[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  background-color: transparent;\\n}\\n.mobile-menu[_ngcontent-%COMP%]   .burger.clicked[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::before {\\n  transform: translateY(9px) rotate(45deg);\\n}\\n.mobile-menu[_ngcontent-%COMP%]   .burger.clicked[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]::after {\\n  transform: translateY(-9px) rotate(-45deg);\\n}\\n.mobile-menu[_ngcontent-%COMP%]   .burger.clicked[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:before, .mobile-menu[_ngcontent-%COMP%]   .burger.clicked[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:after {\\n  background-color: white;\\n}\\n.mobile-menu[_ngcontent-%COMP%]   .burger[_ngcontent-%COMP%]:hover {\\n  cursor: pointer;\\n}\\n.mobile-menu[_ngcontent-%COMP%]   .nava[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(140deg, hsl(231, 53%, 43%) 0%, hsl(231, 49%, 46%) 35%, hsl(231, 46%, 50%) 57%, hsl(232, 49%, 53%) 70%, hsl(232, 53%, 56%) 78%, hsl(231, 58%, 60%) 85%, hsl(231, 63%, 63%) 89%, hsl(231, 70%, 67%) 93%, hsl(231, 79%, 70%) 97%, hsl(231, 90%, 73%) 100%);\\n  position: fixed;\\n  z-index: 9;\\n  top: 0;\\n  right: 0;\\n  height: 100%;\\n  max-width: 100%;\\n  width: 100%;\\n  padding: 45px 0;\\n  overflow-y: auto;\\n  visibility: hidden;\\n  transform: translateX(100%);\\n}\\n.mobile-menu[_ngcontent-%COMP%]   .nava.show[_ngcontent-%COMP%] {\\n  visibility: visible;\\n  transform: translateX(0px);\\n}\\n@media (min-width: 667px) {\\n  .mobile-menu[_ngcontent-%COMP%]   .nava[_ngcontent-%COMP%] {\\n    padding: 120px 40px 70px 40px;\\n  }\\n}\\n.mobile-menu[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  z-index: 1;\\n  width: 100%;\\n  height: 100%;\\n  transition: all 0.3s ease-in-out;\\n  background-color: #8497ff;\\n  opacity: 0;\\n  visibility: hidden;\\n}\\n.mobile-menu[_ngcontent-%COMP%]   .overlay.show[_ngcontent-%COMP%] {\\n  opacity: 0.8;\\n  visibility: visible;\\n}\\n\\n.logo-img[_ngcontent-%COMP%] {\\n  width: 50px;\\n}\\n\\n.icon[_ngcontent-%COMP%] {\\n  display: block;\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n  border: 2px solid #ffffff;\\n  border-radius: 50px;\\n}\\n\\n.mobile-tab[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  text-align: center;\\n}\\n\\n#mobile-menu-items[_ngcontent-%COMP%] {\\n  transition: height 0.5s;\\n  height: 0;\\n  overflow: hidden;\\n  width: calc(100% - 100px);\\n}\\n\\n.measuringWrapper[_ngcontent-%COMP%] {\\n  margin-top: 80px;\\n}\\n\\n.mobile-title-header[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n}\\n\\n\\n\\n\\n\\n.wpo-site-footer[_ngcontent-%COMP%] {\\n  position: relative;\\n  font-size: 15px;\\n  overflow: hidden;\\n  width: 100%;\\n}\\n\\n.wpo-site-footer[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  list-style: none;\\n}\\n\\n.wpo-site-footer[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #fff;\\n}\\n\\n.wpo-site-footer[_ngcontent-%COMP%]   li[_ngcontent-%COMP%], .wpo-site-footer[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #fff;\\n  letter-spacing: 0.01em;\\n  text-align: left;\\n  color: #fff;\\n  text-decoration: none;\\n  line-height: 2;\\n  mix-blend-mode: luminosity;\\n}\\n\\n.wpo-site-footer[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n\\n.wpo-site-footer[_ngcontent-%COMP%]   .wpo-upper-footer[_ngcontent-%COMP%] {\\n  padding: 10px 0;\\n}\\n\\n@media (max-width: 991px) {\\n  .wpo-site-footer[_ngcontent-%COMP%]   .wpo-upper-footer[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%] {\\n    min-height: 235px;\\n    margin-bottom: 70px;\\n  }\\n}\\n@media (max-width: 767px) {\\n  .wpo-site-footer[_ngcontent-%COMP%]   .wpo-upper-footer[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%] {\\n    min-height: auto;\\n    margin-bottom: 60px;\\n  }\\n}\\n.wpo-site-footer[_ngcontent-%COMP%]   .widget-title[_ngcontent-%COMP%] {\\n  \\n\\n  align-self: center;\\n}\\n\\n@media (max-width: 767px) {\\n  .wpo-site-footer[_ngcontent-%COMP%]   .widget-title[_ngcontent-%COMP%] {\\n    \\n\\n  }\\n}\\n.wpo-site-footer[_ngcontent-%COMP%]   .widget-title[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  color: #fff;\\n  margin: 0;\\n  position: relative;\\n  font-weight: normal;\\n  font-size: 28px;\\n  line-height: 24px;\\n  text-align: left;\\n  color: #fff;\\n  opacity: 0.89;\\n}\\n\\n@media (max-width: 991px) {\\n  .wpo-site-footer[_ngcontent-%COMP%]   .widget-title[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n    font-size: 1.25rem;\\n  }\\n}\\n.wpo-site-footer[_ngcontent-%COMP%]   .about-widget[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%] {\\n  max-width: 180px;\\n}\\n\\n.wpo-site-footer[_ngcontent-%COMP%]   .about-widget[_ngcontent-%COMP%] {\\n  font-size: 38px;\\n  letter-spacing: 0.05em;\\n  line-height: 70px;\\n  text-align: left;\\n  color: #fff;\\n  opacity: 0.89;\\n}\\n\\n.circle-singleline[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border-radius: 50%;\\n  font-size: 1rem;\\n  line-height: 24px;\\n  text-align: center;\\n  color: #cbd3ff;\\n  opacity: 0.89;\\n  border: 1px solid;\\n  left: -11px;\\n  position: relative;\\n  display: inline-block;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["DOCUMENT", "EventEmitter", "inject", "ChatService", "SubSink", "i0", "ɵɵelementContainerStart", "ɵɵelement", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c1", "ɵɵlistener", "MobileMenuComponent_ng_container_21_li_1_Template_li_click_0_listener", "ɵɵrestoreView", "_r2", "item_r3", "ɵɵnextContext", "$implicit", "ctx_r3", "ɵɵresetView", "navigateTo", "url", "ɵɵtext", "ɵɵpureFunction1", "_c2", "disabled", "ɵɵstyleProp", "scale", "lottieIcon", "ɵɵtextInterpolate1", "title", "ɵɵtemplate", "MobileMenuComponent_ng_container_21_li_1_Template", "role", "includes", "MobileMenuComponent_ng_container_22_Template_li_click_1_listener", "item_r6", "_r5", "method", "MobileMenuComponent", "constructor", "router", "generalService", "authService", "changeDetector", "layoutService", "notificationService", "renderer", "document", "init", "opened", "chatService", "active", "subs", "mobileMenuTitle", "burgerClasses", "burger", "nava", "overlay", "body", "menu", "extraMobileMenuItems", "unreadNotifications", "socialLinks", "linkedin", "twitter", "facebook", "youtube", "instagram", "ngOnInit", "getUserRole", "menuItems", "getMenuItems", "getExtraMobileMenuItems", "add", "get", "subscribe", "res", "filter", "notification", "isRead", "ngOnDestroy", "unsubscribe", "ngAfterContentChecked", "sink", "detectChanges", "onBurgerClicked", "emit", "toggleMobileMenuItems", "mobileMenuItems", "getElementById", "clientHeight", "style", "height", "window", "innerHeight", "logout", "route", "burgerClick", "navigateByUrl", "replaceUrl", "addClass", "removeClass", "_", "ɵɵdirectiveInject", "i1", "Router", "i2", "GeneralService", "i3", "AuthService", "ChangeDetectorRef", "i4", "LayoutService", "i5", "NotificationService", "Renderer2", "_2", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "MobileMenuComponent_Template", "rf", "ctx", "MobileMenuComponent_ng_container_4_Template", "MobileMenuComponent_Conditional_8_Template", "MobileMenuComponent_ng_container_21_Template", "MobileMenuComponent_ng_container_22_Template", "MobileMenuComponent_Template_div_click_46_listener", "_r1", "ɵɵconditional", "isChatEnabled", "_c0", "length", "ɵɵsanitizeUrl"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\mobile-menu\\mobile-menu.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\mobile-menu\\mobile-menu.component.html"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\r\nimport { AfterContentChecked, ChangeDetectorRef, Component, EventEmitter, inject, Inject, Input, OnInit, Output, Renderer2 } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { LeftMenuItem } from 'src/app/core/models/general.model';\r\nimport { Notification } from 'src/app/core/models/notification.model';\r\nimport { UserRole } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { ChatService } from 'src/app/core/services/chat.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { LayoutService } from 'src/app/core/services/layout.service';\r\nimport { NotificationService } from 'src/app/core/services/notification.service';\r\nimport { SubSink } from 'subsink';\r\n\r\n@Component({\r\n  selector: 'app-mobile-menu',\r\n  templateUrl: './mobile-menu.component.html',\r\n  styleUrls: ['./mobile-menu.component.scss']\r\n})\r\nexport class MobileMenuComponent implements OnInit, AfterContentChecked {\r\n  @Input() init: boolean = false;\r\n  @Input() menuItems!: any;\r\n  @Output() opened = new EventEmitter<any>();\r\n  chatService = inject(ChatService);\r\n  public role: UserRole = {} as UserRole;\r\n  active = false;\r\n  subs = new SubSink();\r\n  mobileMenuTitle: string = '';\r\n  burgerClasses = { burger: '', nava: '', overlay: '', body: '', menu: 'hidden' };\r\n  extraMobileMenuItems = [] as LeftMenuItem[];\r\n  unreadNotifications: Notification[] = [];\r\n  socialLinks =\r\n    {\r\n      linkedin: 'https://gr.linkedin.com/company/mylingotrip',\r\n      twitter: 'https://twitter.com/MyLingoTrip',\r\n      facebook: 'https://www.facebook.com/mylingotrip',\r\n      youtube: 'https://www.youtube.com/c/MyLingoTrip',\r\n      instagram: 'https://www.instagram.com/mylingotrip/',\r\n    };\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private generalService: GeneralService,\r\n    private authService: AuthService,\r\n    private changeDetector: ChangeDetectorRef,\r\n    private layoutService: LayoutService,\r\n    private notificationService: NotificationService,\r\n    private renderer: Renderer2,\r\n    @Inject(DOCUMENT) private document: Document,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    // this.burgerClick();\r\n    this.role = this.authService.getUserRole();\r\n    this.menuItems = this.layoutService.getMenuItems();\r\n    this.extraMobileMenuItems = this.layoutService.getExtraMobileMenuItems();\r\n\r\n    this.subs.add(this.notificationService.get().subscribe((res: any) => {\r\n      // Filter notifications to get only the unread ones\r\n      this.unreadNotifications = res.filter(\r\n        (notification: Notification) => !notification.isRead\r\n      );\r\n    }));\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  ngAfterContentChecked(): void {\r\n    this.subs.sink = this.generalService.mobileMenuTitle.subscribe((res: string) => {\r\n      this.mobileMenuTitle = res;\r\n      this.changeDetector.detectChanges();\r\n    })\r\n  }\r\n  onBurgerClicked() {\r\n    this.active = !this.active;\r\n    this.opened.emit();\r\n  }\r\n  toggleMobileMenuItems() {\r\n    var mobileMenuItems = document.getElementById('mobile-menu-items');\r\n    if (mobileMenuItems!.clientHeight) {\r\n      mobileMenuItems!.style.height = \"0\";\r\n    } else {\r\n      mobileMenuItems!.style.height = window!.innerHeight + \"px\";\r\n    }\r\n  }\r\n\r\n  logout() {\r\n    this.authService.logout();\r\n  }\r\n\r\n  navigateTo(route: string) {\r\n    this.onBurgerClicked();\r\n    this.burgerClick();\r\n    this.router.navigateByUrl(route, { replaceUrl: true });\r\n    // this.router.navigate([route]);\r\n  }\r\n\r\n  burgerClick() {\r\n    this.burgerClasses.burger = this.burgerClasses.burger === 'clicked' ? '' : 'clicked';\r\n    this.burgerClasses.overlay = this.burgerClasses.overlay === 'show' ? '' : 'show';\r\n    this.burgerClasses.nava = this.burgerClasses.nava === 'show' ? '' : 'show';\r\n    this.burgerClasses.menu = this.burgerClasses.menu === 'hidden' ? '' : 'hidden';\r\n    this.burgerClasses.body = 'overflow';\r\n\r\n    if (this.burgerClasses.burger === 'clicked') {\r\n\r\n      this.renderer.addClass(this.document.body, 'overflow-hidden');\r\n    } else {\r\n\r\n      this.renderer.removeClass(this.document.body, 'overflow-hidden');\r\n    }\r\n  }\r\n}\r\n", "<div class=\"mobile-menu px-2\">\r\n  <div class=\" ml-2\">\r\n    <a href=\"https://www.mylingotrip.com\" target=\"_blank\"> <img src=\"/assets/icons/logo/mlt-logo-white.svg\"\r\n        alt=\"mlt-logo-white\" class=\"logo-img\">\r\n    </a>\r\n  </div>\r\n  <ng-container *ngIf=\"mobileMenuTitle !== ''\">\r\n    <div class=\"mobile-title-header\">\r\n      <!-- Application Progress -->\r\n      <!-- {{mobileMenuTitle}} -->\r\n    </div>\r\n  </ng-container>\r\n\r\n  <div class=\"flex ml-auto\">\r\n    <ul #myDiv class=\"list-none select-none flex flex-row \r\n    md:border-top-none\">\r\n      <!-- TODO: enable for chat -->\r\n      @if (chatService.isChatEnabled()) {\r\n      <li class=\"relative  lg:pl-3 lg:ml-2\" id=\"chats-dropdown\" style=\"z-index: 55\">\r\n        <a title=\"Chat\" [routerLink]=\"['/chat']\" class=\" flex align-items-center text-indigo-100 note-badge \r\nhover:text-indigo-50 hover:bg-indigo-700 font-medium border-round cursor-pointer\">\r\n\r\n          <div class=\"notaa flex align-items-center justify-content-center mr-3\"\r\n            style=\"z-index: 1; pointer-events: none;\">\r\n            <i class=\"pi pi-comments pb-0 text-2xl note-badge \" pBadge [value]=\"0\"></i>\r\n          </div>\r\n        </a>\r\n      </li>\r\n      }\r\n      <li class=\"relative  lg:pl-3 lg:ml-2\" id=\"chats-dropdown\" style=\"z-index: 55\">\r\n        <a title=\"Chat\" [routerLink]=\"['/notifications']\" class=\" flex align-items-center text-indigo-100 note-badge \r\nhover:text-indigo-50 hover:bg-indigo-700 font-medium border-round cursor-pointer\">\r\n\r\n          <div class=\"notaa flex align-items-center justify-content-center mr-3\"\r\n            style=\"z-index: 1; pointer-events: none;\">\r\n            <i class=\"pi pi-bell pb-0 text-2xl note-badge \" pBadge\r\n              [value]=\"this.unreadNotifications.length > 0 ? this.unreadNotifications.length : 0\"></i>\r\n          </div>\r\n        </a>\r\n      </li>\r\n\r\n    </ul>\r\n\r\n    <div class=\"nava\" [ngClass]=\"burgerClasses.nava\">\r\n      <div class=\"wpo-site-footer\" [ngClass]=\"burgerClasses.menu\">\r\n        <div class=\"wpo-upper-footer\">\r\n          <div class=\"container\">\r\n            <div class=\"grid align-items-start justify-content-center\">\r\n\r\n              <!-- <div href=\"javascript:void(0);\" class=\"icon\" (click)=\"toggleMobileMenuItems()\">\r\n                        <i class=\"fa fa-bars\" style=\"font-size:30px; padding: 14px; color:var(--main-color)\"></i>\r\n                    </div> -->\r\n              <!--                     \r\n                    <div class=\"col-12 sm:col-12 lg:col-2 md:col-4\">\r\n                      <div class=\"widget link-widget flex flex-column align-items-center justify-content-center\">\r\n                          <div class=\"widget-title\">\r\n                              <h3 class=\" d-flex align-items-sm-center mb-2 mb-md-4\"><span class=\"circle-singleline\">1</span>Home</h3>\r\n                          </div>\r\n                          <ul>\r\n                            <li><a pButton class=\"btn p-button-outlined p-button-raised p-button-rounded rounded-pill text-decoration-none text-white w-100 text-center mb-2\" href=\"\">\r\n                                LingoGalaxy</a></li>\r\n                            <li><a class=\"btn p-button-outlined p-button-raised p-button-rounded  rounded-pill text-decoration-none text-white w-100 text-center mb-2\" href=\"\">\r\n                                MyLingoBusiness</a></li>\r\n                          </ul>\r\n                      </div>\r\n                  </div> -->\r\n\r\n\r\n              <div class=\"col-12 sm:col-12 lg:col-2 md:col-4\">\r\n                <div class=\"widget link-widget flex flex-column align-items-center justify-content-center\">\r\n                  <!-- <div class=\"widget-title\">\r\n                            <h3 class=\" d-flex align-items-sm-center mb-2 mb-md-4\"><span class=\"circle-singleline\">1</span>Home</h3>\r\n                        </div> -->\r\n                  <ul class=\"pl-0\">\r\n                    <ng-container *ngFor=\"let item of menuItems; let i = index;\">\r\n                      <li *ngIf=\"item.role.includes(role)\"\r\n                        class=\"hover:bg-indigo-600 pointer flex align-items-center font-3xl gap-2\"\r\n                        [ngClass]=\"{'disabled-link': item.disabled}\" (click)=\"navigateTo(item.url)\"\r\n                        [tabindex]=\"item.disabled ? -1 : null\">\r\n                        <div class=\"icon-container\">\r\n                          <span class=\"lottie-wrapper\">\r\n                            <lottie-player [src]=\"item.lottieIcon\" background=\"transparent\" speed=\"1\"\r\n                              [style.width.px]=\"'50'\" [style.height.px]=\"'50'\"\r\n                              [style.transform]=\"'scale(' + item.scale + ')'\" loop></lottie-player>\r\n                          </span>\r\n                          <!-- <img [src]=\"item.icon\" class=\"icon\"> -->\r\n                        </div>\r\n\r\n                        {{item.title}}\r\n                      </li>\r\n                    </ng-container>\r\n                    <ng-container *ngFor=\"let item of extraMobileMenuItems; let i = index;\">\r\n                      <li class=\"hover:bg-indigo-600 pointer flex align-items-center font-3xl gap-2\"\r\n                        (click)=\"item.url ? navigateTo(item.url): item.method()\">\r\n\r\n                        <div class=\"icon-container\">\r\n                          <span class=\"lottie-wrapper\">\r\n                            <lottie-player [src]=\"item.lottieIcon\" background=\"transparent\" speed=\"1\"\r\n                              [style.width.px]=\"'50'\" [style.height.px]=\"'50'\"\r\n                              [style.transform]=\"'scale(' + item.scale + ')'\" loop></lottie-player>\r\n                          </span>\r\n                          <!-- <img [src]=\"item.icon\" class=\"icon\"> -->\r\n                        </div>\r\n                        {{item.title}}\r\n                      </li>\r\n                    </ng-container>\r\n                  </ul>\r\n                </div>\r\n              </div>\r\n\r\n            </div>\r\n          </div> <!-- end container -->\r\n        </div>\r\n        <div class=\"wpo-lower-footer\">\r\n          <div class=\"container\">\r\n            <div class=\"row\">\r\n              <div class=\"col-12 text-center\">\r\n                <p class=\"about-widget text-center\">Let’s get social!</p>\r\n\r\n                <ul class=\"flex justify-content-center align-items-center gap-1 p-0\">\r\n                  <li>\r\n                    <a [href]=\"this.socialLinks.facebook\" target=\"_blank\">\r\n                      <img src=\"/assets/icons/navbar/facebook.svg\" alt=\"facebook Image\">\r\n                    </a>\r\n                  </li>\r\n                  <li>\r\n                    <a [href]=\"this.socialLinks.instagram\" target=\"_blank\">\r\n                      <img src=\"/assets/icons/navbar/instagram.svg\" alt=\"instagram Image\">\r\n                    </a>\r\n                  </li>\r\n                  <li>\r\n                    <a [href]=\"this.socialLinks.linkedin\" target=\"_blank\">\r\n                      <img src=\"/assets/icons/navbar/linkedin.svg\" alt=\"linkedin Image\">\r\n                    </a>\r\n                  </li>\r\n                  <li>\r\n                    <a [href]=\"this.socialLinks.twitter\" target=\"_blank\">\r\n                      <img src=\"/assets/icons/navbar/twitter.svg\" alt=\"twitter Image\">\r\n                    </a>\r\n                  </li>\r\n                  <li>\r\n                    <a [href]=\"this.socialLinks.youtube\" target=\"_blank\">\r\n                      <img src=\"/assets/icons/navbar/youtube.svg\" alt=\"youtube Image\">\r\n                    </a>\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"overlay\" [ngClass]=\"burgerClasses.overlay\"></div>\r\n\r\n\r\n  <div class=\"burger\" [ngClass]=\"burgerClasses.burger\" (click)=\"burgerClick()\">\r\n    <span></span>\r\n  </div>\r\n</div>"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,SAA4DC,YAAY,EAAEC,MAAM,QAAkD,eAAe;AAMjJ,SAASC,WAAW,QAAQ,oCAAoC;AAIhE,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;;;;;ICL/BC,EAAA,CAAAC,uBAAA,GAA6C;IAC3CD,EAAA,CAAAE,SAAA,cAGM;;;;;;IAYAF,EAJJ,CAAAG,cAAA,YAA8E,WAEF,cAG5B;IAC1CH,EAAA,CAAAE,SAAA,YAA2E;IAGjFF,EAFI,CAAAI,YAAA,EAAM,EACJ,EACD;;;IARaJ,EAAA,CAAAK,SAAA,EAAwB;IAAxBL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAAO,eAAA,IAAAC,GAAA,EAAwB;IAKuBR,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAAM,UAAA,YAAW;;;;;;IAmD5DN,EAAA,CAAAG,cAAA,aAGyC;IADMH,EAAA,CAAAS,UAAA,mBAAAC,sEAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAC,OAAA,GAAAb,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASD,MAAA,CAAAE,UAAA,CAAAL,OAAA,CAAAM,GAAA,CAAoB;IAAA,EAAC;IAGzEnB,EADF,CAAAG,cAAA,cAA4B,eACG;IAC3BH,EAAA,CAAAE,SAAA,wBAEuE;IAG3EF,EAFE,CAAAI,YAAA,EAAO,EAEH;IAENJ,EAAA,CAAAoB,MAAA,GACF;IAAApB,EAAA,CAAAI,YAAA,EAAK;;;;IAXHJ,EADA,CAAAM,UAAA,YAAAN,EAAA,CAAAqB,eAAA,KAAAC,GAAA,EAAAT,OAAA,CAAAU,QAAA,EAA4C,aAAAV,OAAA,CAAAU,QAAA,aACN;IAIhCvB,EAAA,CAAAK,SAAA,GAAuB;IACvBL,EADA,CAAAwB,WAAA,qBAAuB,sBAAyB,yBAAAX,OAAA,CAAAY,KAAA,OACD;IAFlCzB,EAAA,CAAAM,UAAA,QAAAO,OAAA,CAAAa,UAAA,CAAuB;IAO1C1B,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAA2B,kBAAA,MAAAd,OAAA,CAAAe,KAAA,MACF;;;;;IAfF5B,EAAA,CAAAC,uBAAA,GAA6D;IAC3DD,EAAA,CAAA6B,UAAA,IAAAC,iDAAA,kBAGyC;;;;;;IAHpC9B,EAAA,CAAAK,SAAA,EAA8B;IAA9BL,EAAA,CAAAM,UAAA,SAAAO,OAAA,CAAAkB,IAAA,CAAAC,QAAA,CAAAhB,MAAA,CAAAe,IAAA,EAA8B;;;;;;IAgBrC/B,EAAA,CAAAC,uBAAA,GAAwE;IACtED,EAAA,CAAAG,cAAA,aAC2D;IAAzDH,EAAA,CAAAS,UAAA,mBAAAwB,iEAAA;MAAA,MAAAC,OAAA,GAAAlC,EAAA,CAAAW,aAAA,CAAAwB,GAAA,EAAApB,SAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAAiB,OAAA,CAAAf,GAAA,GAAoBH,MAAA,CAAAE,UAAA,CAAAgB,OAAA,CAAAf,GAAA,CAAoB,GAAEe,OAAA,CAAAE,MAAA,EAAa;IAAA,EAAC;IAGtDpC,EADF,CAAAG,cAAA,cAA4B,eACG;IAC3BH,EAAA,CAAAE,SAAA,wBAEuE;IAG3EF,EAFE,CAAAI,YAAA,EAAO,EAEH;IACNJ,EAAA,CAAAoB,MAAA,GACF;IAAApB,EAAA,CAAAI,YAAA,EAAK;;;;;IANGJ,EAAA,CAAAK,SAAA,GAAuB;IACvBL,EADA,CAAAwB,WAAA,qBAAuB,sBAAyB,yBAAAU,OAAA,CAAAT,KAAA,OACD;IAFlCzB,EAAA,CAAAM,UAAA,QAAA4B,OAAA,CAAAR,UAAA,CAAuB;IAM1C1B,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAA2B,kBAAA,MAAAO,OAAA,CAAAN,KAAA,MACF;;;ADtFtB,OAAM,MAAOS,mBAAmB;EAqB9BC,YACUC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB,EACxBC,cAAiC,EACjCC,aAA4B,EAC5BC,mBAAwC,EACxCC,QAAmB,EACDC,QAAkB;IAPpC,KAAAP,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,QAAQ,GAARA,QAAQ;IACU,KAAAC,QAAQ,GAARA,QAAQ;IA5B3B,KAAAC,IAAI,GAAY,KAAK;IAEpB,KAAAC,MAAM,GAAG,IAAIpD,YAAY,EAAO;IAC1C,KAAAqD,WAAW,GAAGpD,MAAM,CAACC,WAAW,CAAC;IAC1B,KAAAiC,IAAI,GAAa,EAAc;IACtC,KAAAmB,MAAM,GAAG,KAAK;IACd,KAAAC,IAAI,GAAG,IAAIpD,OAAO,EAAE;IACpB,KAAAqD,eAAe,GAAW,EAAE;IAC5B,KAAAC,aAAa,GAAG;MAAEC,MAAM,EAAE,EAAE;MAAEC,IAAI,EAAE,EAAE;MAAEC,OAAO,EAAE,EAAE;MAAEC,IAAI,EAAE,EAAE;MAAEC,IAAI,EAAE;IAAQ,CAAE;IAC/E,KAAAC,oBAAoB,GAAG,EAAoB;IAC3C,KAAAC,mBAAmB,GAAmB,EAAE;IACxC,KAAAC,WAAW,GACT;MACEC,QAAQ,EAAE,6CAA6C;MACvDC,OAAO,EAAE,iCAAiC;MAC1CC,QAAQ,EAAE,sCAAsC;MAChDC,OAAO,EAAE,uCAAuC;MAChDC,SAAS,EAAE;KACZ;EAWC;EAEJC,QAAQA,CAAA;IACN;IACA,IAAI,CAACpC,IAAI,GAAG,IAAI,CAACU,WAAW,CAAC2B,WAAW,EAAE;IAC1C,IAAI,CAACC,SAAS,GAAG,IAAI,CAAC1B,aAAa,CAAC2B,YAAY,EAAE;IAClD,IAAI,CAACX,oBAAoB,GAAG,IAAI,CAAChB,aAAa,CAAC4B,uBAAuB,EAAE;IAExE,IAAI,CAACpB,IAAI,CAACqB,GAAG,CAAC,IAAI,CAAC5B,mBAAmB,CAAC6B,GAAG,EAAE,CAACC,SAAS,CAAEC,GAAQ,IAAI;MAClE;MACA,IAAI,CAACf,mBAAmB,GAAGe,GAAG,CAACC,MAAM,CAClCC,YAA0B,IAAK,CAACA,YAAY,CAACC,MAAM,CACrD;IACH,CAAC,CAAC,CAAC;EACL;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC5B,IAAI,CAAC6B,WAAW,EAAE;EACzB;EAEAC,qBAAqBA,CAAA;IACnB,IAAI,CAAC9B,IAAI,CAAC+B,IAAI,GAAG,IAAI,CAAC1C,cAAc,CAACY,eAAe,CAACsB,SAAS,CAAEC,GAAW,IAAI;MAC7E,IAAI,CAACvB,eAAe,GAAGuB,GAAG;MAC1B,IAAI,CAACjC,cAAc,CAACyC,aAAa,EAAE;IACrC,CAAC,CAAC;EACJ;EACAC,eAAeA,CAAA;IACb,IAAI,CAAClC,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;IAC1B,IAAI,CAACF,MAAM,CAACqC,IAAI,EAAE;EACpB;EACAC,qBAAqBA,CAAA;IACnB,IAAIC,eAAe,GAAGzC,QAAQ,CAAC0C,cAAc,CAAC,mBAAmB,CAAC;IAClE,IAAID,eAAgB,CAACE,YAAY,EAAE;MACjCF,eAAgB,CAACG,KAAK,CAACC,MAAM,GAAG,GAAG;IACrC,CAAC,MAAM;MACLJ,eAAgB,CAACG,KAAK,CAACC,MAAM,GAAGC,MAAO,CAACC,WAAW,GAAG,IAAI;IAC5D;EACF;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAACrD,WAAW,CAACqD,MAAM,EAAE;EAC3B;EAEA5E,UAAUA,CAAC6E,KAAa;IACtB,IAAI,CAACX,eAAe,EAAE;IACtB,IAAI,CAACY,WAAW,EAAE;IAClB,IAAI,CAACzD,MAAM,CAAC0D,aAAa,CAACF,KAAK,EAAE;MAAEG,UAAU,EAAE;IAAI,CAAE,CAAC;IACtD;EACF;EAEAF,WAAWA,CAAA;IACT,IAAI,CAAC3C,aAAa,CAACC,MAAM,GAAG,IAAI,CAACD,aAAa,CAACC,MAAM,KAAK,SAAS,GAAG,EAAE,GAAG,SAAS;IACpF,IAAI,CAACD,aAAa,CAACG,OAAO,GAAG,IAAI,CAACH,aAAa,CAACG,OAAO,KAAK,MAAM,GAAG,EAAE,GAAG,MAAM;IAChF,IAAI,CAACH,aAAa,CAACE,IAAI,GAAG,IAAI,CAACF,aAAa,CAACE,IAAI,KAAK,MAAM,GAAG,EAAE,GAAG,MAAM;IAC1E,IAAI,CAACF,aAAa,CAACK,IAAI,GAAG,IAAI,CAACL,aAAa,CAACK,IAAI,KAAK,QAAQ,GAAG,EAAE,GAAG,QAAQ;IAC9E,IAAI,CAACL,aAAa,CAACI,IAAI,GAAG,UAAU;IAEpC,IAAI,IAAI,CAACJ,aAAa,CAACC,MAAM,KAAK,SAAS,EAAE;MAE3C,IAAI,CAACT,QAAQ,CAACsD,QAAQ,CAAC,IAAI,CAACrD,QAAQ,CAACW,IAAI,EAAE,iBAAiB,CAAC;IAC/D,CAAC,MAAM;MAEL,IAAI,CAACZ,QAAQ,CAACuD,WAAW,CAAC,IAAI,CAACtD,QAAQ,CAACW,IAAI,EAAE,iBAAiB,CAAC;IAClE;EACF;EAAC,QAAA4C,CAAA,G;qBA9FUhE,mBAAmB,EAAArC,EAAA,CAAAsG,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAxG,EAAA,CAAAsG,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA1G,EAAA,CAAAsG,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA5G,EAAA,CAAAsG,iBAAA,CAAAtG,EAAA,CAAA6G,iBAAA,GAAA7G,EAAA,CAAAsG,iBAAA,CAAAQ,EAAA,CAAAC,aAAA,GAAA/G,EAAA,CAAAsG,iBAAA,CAAAU,EAAA,CAAAC,mBAAA,GAAAjH,EAAA,CAAAsG,iBAAA,CAAAtG,EAAA,CAAAkH,SAAA,GAAAlH,EAAA,CAAAsG,iBAAA,CA6BpB3G,QAAQ;EAAA;EAAA,QAAAwH,EAAA,G;UA7BP9E,mBAAmB;IAAA+E,SAAA;IAAAC,MAAA;MAAAtE,IAAA;MAAAsB,SAAA;IAAA;IAAAiD,OAAA;MAAAtE,MAAA;IAAA;IAAAuE,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QChB5B5H,EAFJ,CAAAG,cAAA,aAA8B,aACT,WACqC;QAACH,EAAA,CAAAE,SAAA,aACb;QAE5CF,EADE,CAAAI,YAAA,EAAI,EACA;QACNJ,EAAA,CAAA6B,UAAA,IAAAiG,2CAAA,0BAA6C;QAQ3C9H,EADF,CAAAG,cAAA,aAA0B,eAEJ;QAElBH,EAAA,CAAA6B,UAAA,IAAAkG,0CAAA,gBAAmC;QAgB/B/H,EAJJ,CAAAG,cAAA,YAA8E,YAEF,eAG5B;QAC1CH,EAAA,CAAAE,SAAA,aAC0F;QAKlGF,EAJM,CAAAI,YAAA,EAAM,EACJ,EACD,EAEF;QAgCSJ,EA9Bd,CAAAG,cAAA,eAAiD,eACa,eAC5B,eACL,eACsC,eAqBT,eAC6C,cAIxE;QAkBfH,EAjBA,CAAA6B,UAAA,KAAAmG,4CAAA,2BAA6D,KAAAC,4CAAA,2BAiBW;QAqBpFjI,EANU,CAAAI,YAAA,EAAK,EACD,EACF,EAEF,EACF,EACF;QAKEJ,EAJR,CAAAG,cAAA,eAA8B,eACL,eACJ,eACiB,aACM;QAAAH,EAAA,CAAAoB,MAAA,8BAAiB;QAAApB,EAAA,CAAAI,YAAA,EAAI;QAIrDJ,EAFJ,CAAAG,cAAA,cAAqE,UAC/D,aACoD;QACpDH,EAAA,CAAAE,SAAA,eAAkE;QAEtEF,EADE,CAAAI,YAAA,EAAI,EACD;QAEHJ,EADF,CAAAG,cAAA,UAAI,aACqD;QACrDH,EAAA,CAAAE,SAAA,eAAoE;QAExEF,EADE,CAAAI,YAAA,EAAI,EACD;QAEHJ,EADF,CAAAG,cAAA,UAAI,aACoD;QACpDH,EAAA,CAAAE,SAAA,eAAkE;QAEtEF,EADE,CAAAI,YAAA,EAAI,EACD;QAEHJ,EADF,CAAAG,cAAA,UAAI,aACmD;QACnDH,EAAA,CAAAE,SAAA,eAAgE;QAEpEF,EADE,CAAAI,YAAA,EAAI,EACD;QAEHJ,EADF,CAAAG,cAAA,UAAI,aACmD;QACnDH,EAAA,CAAAE,SAAA,eAAgE;QAUpFF,EATkB,CAAAI,YAAA,EAAI,EACD,EACF,EACD,EACF,EACF,EACF,EACF,EACF,EACF;QAENJ,EAAA,CAAAE,SAAA,eAA6D;QAG7DF,EAAA,CAAAG,cAAA,eAA6E;QAAxBH,EAAA,CAAAS,UAAA,mBAAAyH,mDAAA;UAAAlI,EAAA,CAAAW,aAAA,CAAAwH,GAAA;UAAA,OAAAnI,EAAA,CAAAiB,WAAA,CAAS4G,GAAA,CAAA7B,WAAA,EAAa;QAAA,EAAC;QAC1EhG,EAAA,CAAAE,SAAA,YAAa;QAEjBF,EADE,CAAAI,YAAA,EAAM,EACF;;;QA1JWJ,EAAA,CAAAK,SAAA,GAA4B;QAA5BL,EAAA,CAAAM,UAAA,SAAAuH,GAAA,CAAAzE,eAAA,QAA4B;QAWvCpD,EAAA,CAAAK,SAAA,GAWC;QAXDL,EAAA,CAAAoI,aAAA,IAAAP,GAAA,CAAA5E,WAAA,CAAAoF,aAAA,YAWC;QAEiBrI,EAAA,CAAAK,SAAA,GAAiC;QAAjCL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAAO,eAAA,KAAA+H,GAAA,EAAiC;QAM3CtI,EAAA,CAAAK,SAAA,GAAmF;QAAnFL,EAAA,CAAAM,UAAA,UAAAuH,GAAA,CAAAjE,mBAAA,CAAA2E,MAAA,OAAAV,GAAA,CAAAjE,mBAAA,CAAA2E,MAAA,KAAmF;QAO3EvI,EAAA,CAAAK,SAAA,EAA8B;QAA9BL,EAAA,CAAAM,UAAA,YAAAuH,GAAA,CAAAxE,aAAA,CAAAE,IAAA,CAA8B;QACjBvD,EAAA,CAAAK,SAAA,EAA8B;QAA9BL,EAAA,CAAAM,UAAA,YAAAuH,GAAA,CAAAxE,aAAA,CAAAK,IAAA,CAA8B;QA8Bd1D,EAAA,CAAAK,SAAA,GAAc;QAAdL,EAAA,CAAAM,UAAA,YAAAuH,GAAA,CAAAxD,SAAA,CAAc;QAiBdrE,EAAA,CAAAK,SAAA,EAAyB;QAAzBL,EAAA,CAAAM,UAAA,YAAAuH,GAAA,CAAAlE,oBAAA,CAAyB;QA8BrD3D,EAAA,CAAAK,SAAA,GAAkC;QAAlCL,EAAA,CAAAM,UAAA,SAAAuH,GAAA,CAAAhE,WAAA,CAAAG,QAAA,EAAAhE,EAAA,CAAAwI,aAAA,CAAkC;QAKlCxI,EAAA,CAAAK,SAAA,GAAmC;QAAnCL,EAAA,CAAAM,UAAA,SAAAuH,GAAA,CAAAhE,WAAA,CAAAK,SAAA,EAAAlE,EAAA,CAAAwI,aAAA,CAAmC;QAKnCxI,EAAA,CAAAK,SAAA,GAAkC;QAAlCL,EAAA,CAAAM,UAAA,SAAAuH,GAAA,CAAAhE,WAAA,CAAAC,QAAA,EAAA9D,EAAA,CAAAwI,aAAA,CAAkC;QAKlCxI,EAAA,CAAAK,SAAA,GAAiC;QAAjCL,EAAA,CAAAM,UAAA,SAAAuH,GAAA,CAAAhE,WAAA,CAAAE,OAAA,EAAA/D,EAAA,CAAAwI,aAAA,CAAiC;QAKjCxI,EAAA,CAAAK,SAAA,GAAiC;QAAjCL,EAAA,CAAAM,UAAA,SAAAuH,GAAA,CAAAhE,WAAA,CAAAI,OAAA,EAAAjE,EAAA,CAAAwI,aAAA,CAAiC;QAajCxI,EAAA,CAAAK,SAAA,GAAiC;QAAjCL,EAAA,CAAAM,UAAA,YAAAuH,GAAA,CAAAxE,aAAA,CAAAG,OAAA,CAAiC;QAGlCxD,EAAA,CAAAK,SAAA,EAAgC;QAAhCL,EAAA,CAAAM,UAAA,YAAAuH,GAAA,CAAAxE,aAAA,CAAAC,MAAA,CAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}