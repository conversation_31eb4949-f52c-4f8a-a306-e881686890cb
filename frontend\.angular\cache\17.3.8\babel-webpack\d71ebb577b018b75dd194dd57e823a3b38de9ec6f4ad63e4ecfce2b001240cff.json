{"ast": null, "code": "// This file can be replaced during build by using the `fileReplacements` array.\n// `ng build` replaces `environment.ts` with `environment.prod.ts`.\n// The list of file replacements can be found in `angular.json`.\nexport const environment = {\n  production: false,\n  apiUrl: \"https://mlt.api.kiddobrains.com\",\n  leadDynoPrivateKey: '165d71696287208d956a45cffb6d8112b66f0e81',\n  isChatEnabled: true,\n  firebase: {\n    apiKey: \"AIzaSyAS3J9uI0k2gtFBSI1daBcG2xbWJrBcCik\",\n    authDomain: \"mylingotrip-d2338.firebaseapp.com\",\n    databaseURL: \"https://mylingotrip-d2338-default-rtdb.europe-west1.firebasedatabase.app\",\n    projectId: \"mylingotrip-d2338\",\n    storageBucket: \"mylingotrip-d2338.firebasestorage.app\",\n    messagingSenderId: \"618062748166\",\n    appId: \"1:618062748166:web:83fa6d423e3d6913db0187\",\n    measurementId: \"G-QF0X9YTHTL\"\n  }\n};\n/*\n * For easier debugging in development mode, you can import the following file\n * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.\n *\n * This import should be commented out in production mode because it will have a negative impact\n * on performance if an error is thrown.\n */\n// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.", "map": {"version": 3, "names": ["environment", "production", "apiUrl", "leadDynoPrivateKey", "isChatEnabled", "firebase", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "databaseURL", "projectId", "storageBucket", "messagingSenderId", "appId", "measurementId"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\environments\\environment.ts"], "sourcesContent": ["// This file can be replaced during build by using the `fileReplacements` array.\r\n// `ng build` replaces `environment.ts` with `environment.prod.ts`.\r\n// The list of file replacements can be found in `angular.json`.\r\n\r\nexport const environment = {\r\n  production: false,\r\n  apiUrl: \"https://mlt.api.kiddobrains.com\",\r\n  leadDynoPrivateKey: '165d71696287208d956a45cffb6d8112b66f0e81',\r\n  isChatEnabled: true,\r\n\r\n  firebase: {\r\n    apiKey: \"AIzaSyAS3J9uI0k2gtFBSI1daBcG2xbWJrBcCik\",\r\n    authDomain: \"mylingotrip-d2338.firebaseapp.com\",\r\n    databaseURL: \"https://mylingotrip-d2338-default-rtdb.europe-west1.firebasedatabase.app\",\r\n    projectId: \"mylingotrip-d2338\",\r\n    storageBucket: \"mylingotrip-d2338.firebasestorage.app\",\r\n    messagingSenderId: \"618062748166\",\r\n    appId: \"1:618062748166:web:83fa6d423e3d6913db0187\",\r\n    measurementId: \"G-QF0X9YTHTL\"\r\n  }\r\n};\r\n\r\n/*\r\n * For easier debugging in development mode, you can import the following file\r\n * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.\r\n *\r\n * This import should be commented out in production mode because it will have a negative impact\r\n * on performance if an error is thrown.\r\n */\r\n// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,iCAAiC;EACzCC,kBAAkB,EAAE,0CAA0C;EAC9DC,aAAa,EAAE,IAAI;EAEnBC,QAAQ,EAAE;IACRC,MAAM,EAAE,yCAAyC;IACjDC,UAAU,EAAE,mCAAmC;IAC/CC,WAAW,EAAE,0EAA0E;IACvFC,SAAS,EAAE,mBAAmB;IAC9BC,aAAa,EAAE,uCAAuC;IACtDC,iBAAiB,EAAE,cAAc;IACjCC,KAAK,EAAE,2CAA2C;IAClDC,aAAa,EAAE;;CAElB;AAED;;;;;;;AAOA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}