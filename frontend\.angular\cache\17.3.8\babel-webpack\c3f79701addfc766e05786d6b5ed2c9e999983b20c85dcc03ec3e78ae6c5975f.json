{"ast": null, "code": "import { of } from 'rxjs';\nimport { map, startWith, switchMap, take } from 'rxjs/operators';\nimport { ClassroomType } from 'src/app/core/models/classroom.model';\nimport { SubSink } from 'subsink';\nimport * as moment from 'moment';\nimport { LessonStatus } from 'src/app/core/models/lesson.model';\nimport { UserRole } from 'src/app/core/models/user.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/classroom.service\";\nimport * as i2 from \"src/app/core/services/general.service\";\nimport * as i3 from \"src/app/core/services/auth.service\";\nimport * as i4 from \"src/app/core/services/user.service\";\nimport * as i5 from \"src/app/core/services/calendar.service\";\nimport * as i6 from \"src/app/core/services/layout.service\";\nimport * as i7 from \"src/app/core/services/lesson.service\";\nimport * as i8 from \"src/app/core/services/toast.service\";\nimport * as i9 from \"@angular/router\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"@angular/forms\";\nimport * as i12 from \"../../../shared/block-viewer/block-viewer.component\";\nimport * as i13 from \"../../../shared/prime/prime/calendar-agenda/calendar-agenda.component\";\nimport * as i14 from \"primeng/button\";\nimport * as i15 from \"primeng/api\";\nimport * as i16 from \"primeng/scrollpanel\";\nimport * as i17 from \"primeng/dropdown\";\nconst _c0 = [\"bookingWrapper\"];\nconst _c1 = [\"scrollPanel\"];\nconst _c2 = a0 => ({\n  \"height\": a0\n});\nconst _c3 = () => ({\n  width: \"100%\"\n});\nconst _c4 = (a0, a1) => ({\n  \"availability\": a0,\n  \"no-availability\": a1\n});\nconst _c5 = a0 => ({\n  \"pointer-events-none\": a0\n});\nfunction BookingSystemComponent_ng_container_15_ng_template_2_div_0_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 47);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"src\", ctx_r2.classroomService.getClassroomTypeSmallIconPath(ctx_r2.studentSelectedClassroom), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction BookingSystemComponent_ng_container_15_ng_template_2_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, BookingSystemComponent_ng_container_15_ng_template_2_div_0_img_1_Template, 1, 1, \"img\", 45);\n    i0.ɵɵelement(2, \"div\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.authService.isTeacher);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.classroomService.getStudentNamesWithHTML(ctx_r2.studentSelectedClassroom), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction BookingSystemComponent_ng_container_15_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, BookingSystemComponent_ng_container_15_ng_template_2_div_0_Template, 3, 2, \"div\", 43);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.studentSelectedClassroom);\n  }\n}\nfunction BookingSystemComponent_ng_container_15_ng_template_3_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 47);\n  }\n  if (rf & 2) {\n    const classroom_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.classroomService.getClassroomTypeSmallIconPath(classroom_r4), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction BookingSystemComponent_ng_container_15_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, BookingSystemComponent_ng_container_15_ng_template_3_img_1_Template, 1, 1, \"img\", 45);\n    i0.ɵɵelement(2, \"div\", 46)(3, \"br\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 48);\n    i0.ɵɵelement(5, \"img\", 49);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const classroom_r4 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.authService.isTeacher);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.classroomService.getStudentNamesWithHTML(classroom_r4, true), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.generalService.convertHoursToMinutesWithSuffix(ctx_r2.classroomService.getAccumulatedHoursLeft(classroom_r4.packages), true), \" available\");\n  }\n}\nfunction BookingSystemComponent_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p-dropdown\", 40);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BookingSystemComponent_ng_container_15_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.studentSelectedClassroom, $event) || (ctx_r2.studentSelectedClassroom = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function BookingSystemComponent_ng_container_15_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClassroomChange($event));\n    });\n    i0.ɵɵtemplate(2, BookingSystemComponent_ng_container_15_ng_template_2_Template, 1, 1, \"ng-template\", 41)(3, BookingSystemComponent_ng_container_15_ng_template_3_Template, 7, 3, \"ng-template\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r2.availableClassrooms);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.studentSelectedClassroom);\n  }\n}\nfunction BookingSystemComponent_ng_container_16_ng_template_2_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 51);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.selectedTeachersLabel.language, \" - \", ctx_r2.selectedTeachersLabel.teacherName, \"\");\n  }\n}\nfunction BookingSystemComponent_ng_container_16_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, BookingSystemComponent_ng_container_16_ng_template_2_div_0_Template, 3, 2, \"div\", 43);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedTeachersLabel);\n  }\n}\nfunction BookingSystemComponent_ng_container_16_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵelement(1, \"img\", 53);\n    i0.ɵɵelementStart(2, \"div\", 51);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"br\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 48);\n    i0.ɵɵelement(6, \"img\", 49);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const teacher_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"src\", \"/assets/images/dashboard/calendar/\", teacher_r6.icon, \".svg\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", teacher_r6.language, \" - \", teacher_r6.teacherName, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.generalService.convertHoursToMinutesWithSuffix(teacher_r6.hoursLeft, true), \" available\");\n  }\n}\nfunction BookingSystemComponent_ng_container_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p-dropdown\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BookingSystemComponent_ng_container_16_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedTeachersLabel, $event) || (ctx_r2.selectedTeachersLabel = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function BookingSystemComponent_ng_container_16_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onTeacherChange($event));\n    });\n    i0.ɵɵtemplate(2, BookingSystemComponent_ng_container_16_ng_template_2_Template, 1, 1, \"ng-template\", 41)(3, BookingSystemComponent_ng_container_16_ng_template_3_Template, 8, 5, \"ng-template\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r2.teachersLabels);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedTeachersLabel);\n  }\n}\nfunction BookingSystemComponent_div_39_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, ctx_r2.selectedDay, \"EEEE, dd/MM\"));\n  }\n}\nfunction BookingSystemComponent_div_39_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.selectedTimeSlot);\n  }\n}\nfunction BookingSystemComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 54)(2, \"div\", 55)(3, \"div\", 56);\n    i0.ɵɵtext(4, \"You have chosen a lesson for\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 57)(6, \"div\", 58)(7, \"div\", 59);\n    i0.ɵɵelement(8, \"i\", 60);\n    i0.ɵɵtemplate(9, BookingSystemComponent_div_39_span_9_Template, 3, 4, \"span\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\");\n    i0.ɵɵelement(11, \"i\", 61);\n    i0.ɵɵtemplate(12, BookingSystemComponent_div_39_span_12_Template, 2, 1, \"span\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\");\n    i0.ɵɵelement(14, \"i\", 62);\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(17, \"div\", 63)(18, \"div\", 64)(19, \"div\", 65);\n    i0.ɵɵelement(20, \"img\", 66);\n    i0.ɵɵtext(21, \"Add to Google Calendar \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 67);\n    i0.ɵɵelement(23, \"img\", 66);\n    i0.ɵɵtext(24, \"Add to Outlook Calendar \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 68)(26, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function BookingSystemComponent_div_39_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onLessonRequested());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedDay);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedTimeSlot);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedDuration.viewValue);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"hidden\", true);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"loading\", ctx_r2.buttonLoading)(\"label\", ctx_r2.isRescheduling ? \"RESCHEDULE\" : \"REQUEST\");\n  }\n}\nfunction BookingSystemComponent_li_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind2(2, 2, day_r8, \"EEE\"), \" \", day_r8.getDate(), \"\");\n  }\n}\nfunction BookingSystemComponent_ng_container_57_a_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 75);\n    i0.ɵɵlistener(\"click\", function BookingSystemComponent_ng_container_57_a_9_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.generalService.navigateToBuyPackage());\n    });\n    i0.ɵɵtext(1, \" Purchase Package \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingSystemComponent_ng_container_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 70)(2, \"div\", 71);\n    i0.ɵɵelement(3, \"img\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 73);\n    i0.ɵɵtext(5, \" You have \");\n    i0.ɵɵelementStart(6, \"b\");\n    i0.ɵɵtext(7, \"0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" hours available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, BookingSystemComponent_ng_container_57_a_9_Template, 2, 0, \"a\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.authService.isStudent);\n  }\n}\nfunction BookingSystemComponent_li_59_ul_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 76)(2, \"div\", 77)(3, \"label\", 78);\n    i0.ɵɵlistener(\"click\", function BookingSystemComponent_li_59_ul_1_ng_container_1_Template_label_click_3_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const hour_r11 = i0.ɵɵnextContext().$implicit;\n      const day_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onTimeSlotSelected(day_r12, hour_r11));\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 79);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const hour_r11 = i0.ɵɵnextContext().$implicit;\n    const day_r12 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(14, _c4, ctx_r2.isTimeSlotAvailableOnDay(day_r12, hour_r11), !ctx_r2.isTimeSlotAvailableOnDay(day_r12, hour_r11) && ctx_r2.authService.isTeacher));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c5, !ctx_r2.isTimeSlotAvailableOnDay(day_r12, hour_r11)));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"checked\", ctx_r2.isSelectedDayAndTime(day_r12, hour_r11) && ctx_r2.radioChecked);\n    i0.ɵɵpropertyInterpolate2(\"for\", \"radio\", day_r12, \"\", hour_r11, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(hour_r11);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate2(\"id\", \"radio\", day_r12, \"\", hour_r11, \"\");\n    i0.ɵɵpropertyInterpolate(\"value\", hour_r11);\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.isTimeSlotAvailableOnDay(day_r12, hour_r11))(\"checked\", ctx_r2.isSelectedDayAndTime(day_r12, hour_r11) && ctx_r2.radioChecked ? true : null);\n  }\n}\nfunction BookingSystemComponent_li_59_ul_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\");\n    i0.ɵɵtemplate(1, BookingSystemComponent_li_59_ul_1_ng_container_1_Template, 6, 19, \"ng-container\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const hour_r11 = ctx.$implicit;\n    const day_r12 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isTimeSlotAvailableOnDay(day_r12, hour_r11) || ctx_r2.authService.isTeacher);\n  }\n}\nfunction BookingSystemComponent_li_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtemplate(1, BookingSystemComponent_li_59_ul_1_Template, 2, 1, \"ul\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.availableHours);\n  }\n}\nexport let BookingSystemComponent = /*#__PURE__*/(() => {\n  class BookingSystemComponent {\n    constructor(classroomService, generalService, authService, userService, calendarService, layoutService, lessonService, toastService, route, router, cdRef, location) {\n      this.classroomService = classroomService;\n      this.generalService = generalService;\n      this.authService = authService;\n      this.userService = userService;\n      this.calendarService = calendarService;\n      this.layoutService = layoutService;\n      this.lessonService = lessonService;\n      this.toastService = toastService;\n      this.route = route;\n      this.router = router;\n      this.cdRef = cdRef;\n      this.location = location;\n      this.bookingWrapper = {};\n      this.scrollPanel = {};\n      this.studentSelectedClassroom = {};\n      this.startDate = new Date();\n      this.weekDays = [];\n      this.hours = [];\n      this.schedule = {};\n      this.availableHours = [];\n      this.subs = new SubSink();\n      this.classrooms = [];\n      this.classroomsWithRecurringLessons = [];\n      this.hasClassrooms = true;\n      this.availableTeachers = [];\n      this.lessonEvents = [];\n      this.selectedTeacher = null;\n      this.filteredLessonEvents = [];\n      this.user = {};\n      this.teachersLabels = [];\n      this.selectedTeachersLabel = null;\n      this.selectedHour = null;\n      this.duration = [];\n      this.availableDuration = [];\n      this.selectedDuration = null;\n      this.cdr = {};\n      this.containerHeight = '';\n      this.availableClassrooms = [];\n      this.UserRoles = UserRole;\n      this.radioChecked = false;\n      this.calendarAgendaUserId = '';\n      this.extraData = {};\n      this.extraClassroomData = {};\n      this.isRescheduling = false;\n      this.isPending = false;\n      this.mobileView = false;\n      this.daysRange = 7;\n      this.height = 0;\n      this.noClassroomsFound = false;\n      this.buttonLoading = false;\n      this.startDate = new Date();\n    }\n    ngOnInit() {\n      const queryParams = this.route.snapshot.queryParams;\n      const lessonString = queryParams['lesson'];\n      const classroomString = queryParams['classroom'];\n      const hasRescheduleString = queryParams['reschedule'];\n      const answeredString = queryParams['answered'];\n      this.mobileView = this.isMobileScreenSize();\n      if (hasRescheduleString) {\n        this.isRescheduling = true;\n      }\n      if (answeredString) {\n        this.isPending = true;\n      }\n      if (lessonString) {\n        const lesson = JSON.parse(lessonString);\n        console.log(lesson);\n        this.extraData = {\n          \"lessonId\": lesson.id,\n          \"lessonStatus\": lesson.status,\n          \"classroomId\": lesson.classroomId,\n          \"status\": lesson.status,\n          \"startingDate\": lesson.startingDate,\n          \"duration\": lesson.duration,\n          \"isRecccuring\": lesson.isRecccuring\n        };\n      }\n      if (classroomString) {\n        const classroom = JSON.parse(classroomString);\n        console.log(classroom);\n        this.extraClassroomData = {\n          \"classroomId\": classroom.id,\n          \"status\": classroom.status,\n          \"classroomType\": classroom.type\n        };\n      }\n      this.duration = [{\n        value: 15,\n        viewValue: '15m'\n      }, {\n        value: 30,\n        viewValue: '30m'\n      }, {\n        value: 45,\n        viewValue: '45m'\n      }, {\n        value: 60,\n        viewValue: '1h'\n      }, {\n        value: 75,\n        viewValue: '1h 15m'\n      }, {\n        value: 90,\n        viewValue: '1h 30m'\n      }, {\n        value: 105,\n        viewValue: '1h 45m'\n      }, {\n        value: 120,\n        viewValue: '2h'\n      }, {\n        value: 135,\n        viewValue: '2h 15m'\n      }, {\n        value: 150,\n        viewValue: '2h 30m'\n      }, {\n        value: 165,\n        viewValue: '2h 45m'\n      }, {\n        value: 180,\n        viewValue: '3h'\n      }];\n      this.availableDuration = [...this.duration];\n      this.selectedDuration = this.duration[3];\n      this.calculateWeekDays(new Date());\n      this.calculateHours();\n      this.initSchedule();\n      this.user = this.authService.getLoggedInUser();\n      this.prepareInitialData();\n    }\n    isMobileScreenSize() {\n      const screenWidth = window.innerWidth;\n      return screenWidth < 768;\n    }\n    isTabletScreenSize() {\n      const screenWidth = window.innerWidth;\n      return screenWidth < 1024; // Change the width here to the desired tablet width\n    }\n    ngOnChanges() {\n      this.mobileView = this.isMobileScreenSize();\n      console.log(this.mobileView);\n    }\n    ngAfterViewInit() {\n      this.subs.sink = this.generalService.deviceKind.pipe(take(2), switchMap(res => {\n        console.log(res);\n        if (res.is576 || res.is992) {\n          return of(0);\n        } else {\n          return this.layoutService.sideMenuHeight;\n        }\n      }), startWith(0)).subscribe(height => {\n        if (height !== 0) {\n          this.setDaysRange(7);\n          this.height = height;\n          this.bookingWrapper.nativeElement.style.height = height + 30 + 'px';\n        } else {\n          this.setDaysRange(3);\n          this.bookingWrapper.nativeElement.style.height = '100%';\n        }\n        this.cdRef.detectChanges(); // Manually trigger change detection\n      });\n      // this.subs.sink = this.layoutService.sideMenuHeight.subscribe((res) => {\n      //   this.bookingWrapper.nativeElement.style.height = (res + 'px');\n      // });\n      this.scrollToMiddle();\n    }\n    ngAfterContentInit() {}\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n    }\n    setDaysRange(days) {\n      this.daysRange = days;\n    }\n    getDaysRange() {\n      return this.daysRange;\n    }\n    scrollToMiddle() {\n      setTimeout(() => {\n        const scrollContent = this.scrollPanel.containerViewChild.nativeElement.getElementsByClassName('p-scrollpanel-content')[0];\n        const middleScrollPosition = scrollContent.scrollHeight / 3;\n        scrollContent.scrollTop = middleScrollPosition;\n      }, 1000);\n    }\n    onDurationSelected(event) {\n      this.selectedTimeSlot = '';\n      this.radioChecked = false;\n      console.log(this.selectedDay);\n      // Add your own logic here to handle the selected duration\n    }\n    onTimeSlotSelected(day, hour) {\n      this.selectedDay = moment(day, \"ddd MMM DD YYYY\").toDate();\n      this.selectedTimeSlot = hour;\n      this.displayAvailableTimeSlots();\n      const selectedDateTime = moment(day, \"ddd MMM DD YYYY HH:mm:ss [GMT]ZZ\");\n      const formattedDateTime = selectedDateTime.format(\"ddd MMM DD YYYY \" + hour);\n      // TODO : implement uncheck all radios functionality\n      // if (this.radioChecked && this.timeSlotRadio.checked) {\n      //   if (this.timeSlotRadio) {\n      //     this.timeSlotRadio.checked = false;\n      //   }\n      //   this.resetSelectedTimeSlot();\n      //   return;\n      // }\n      if (this.user.role === UserRole.TEACHER) {\n        this.radioChecked = true;\n      } else {\n        if (!this.isDateTimeInThePast(new Date(formattedDateTime))) {\n          this.radioChecked = true;\n        } else {\n          this.toastService.setShowToastmessage({\n            severity: 'info',\n            summary: '',\n            detail: 'Date is in the past. Please choose another date.'\n          });\n          this.radioChecked = false;\n        }\n      }\n    }\n    resetSelectedTimeSlot() {\n      this.selectedDay = new Date();\n      this.selectedTimeSlot = '';\n      this.displayAvailableTimeSlots();\n      this.radioChecked = false;\n    }\n    isSelectedDayAndTime(day, hour) {\n      const d = new Date(day);\n      if (this.hasExtraData()) {\n        return d.getDate() + '' + d.getMonth() === this.selectedDay.getDate() + '' + this.selectedDay.getMonth() && hour === this.selectedTimeSlot;\n      } else {\n        return day === this.selectedDay && hour === this.selectedTimeSlot;\n      }\n    }\n    /**\n     * The function `getTeacherAvailability` retrieves the calendar availability of a teacher based on\n     * their ASP user ID.\n     * @param {any} teacher - The `teacher` parameter is an object that represents a teacher. It contains\n     * information about the teacher, such as their ID (`aspUserId`).\n     * @returns an Observable of type RetrieveCalendarAvailabilityRequestData or null.\n     */\n    getTeacherAvailability(teacher) {\n      if (!teacher || this.generalService.isNullishObject(teacher)) {\n        return of(null);\n      }\n      const data = {\n        ...this.getDatesRange(),\n        userId: teacher.aspUserId\n      };\n      return this.getAvailabilityFromRange(data);\n    }\n    getAvailabilityFromRange(requestData) {\n      console.log(requestData);\n      return this.calendarService.retrieveCalendarAvailability(requestData).pipe(map(res => {\n        // const daysOff = res.daysOff;\n        // // Set business hours and select constraint\n        this.businessHours = res.calendarAvailability;\n        this.availableHours = this.displayAvailableTimeSlots();\n        // // Create events for each day off\n        // this.dayOffEvents = this.calendarService.createDayOffEvents(daysOff);\n        return null;\n      }));\n    }\n    filterLessonsByTeacherId(lessons, teacherId) {\n      return lessons.filter(lesson => lesson.teacher.id === teacherId);\n    }\n    onTeacherChange(event) {\n      // Reset selected time slot and radio checked status\n      this.selectedTimeSlot = '';\n      this.radioChecked = false;\n      // Find the selected teacher based on the teacherId\n      this.selectedTeacher = this.availableTeachers.find(item => item.id === event.teacherId);\n      // Find the classroom based on the selected teacher's classroom label\n      const classroomId = this.availableClassrooms.find(item => item.id === this.selectedTeachersLabel.classroom);\n      console.log(classroomId);\n      // Handle the available duration for the classroom type\n      this.handleClassroomTypeAvailableDuration(classroomId);\n      // Get the teacher's availability and update the filtered lesson events\n      this.subs.add(this.getTeacherAvailability(this.selectedTeacher).subscribe(res => {\n        this.filteredLessonEvents = this.lessonEvents;\n        console.log('filteredLessonEvents', this.filteredLessonEvents);\n      }));\n      // Set the calendar agenda user ID to the selected teacher's ASP user ID\n      this.calendarAgendaUserId = this.selectedTeacher.aspUserId;\n    }\n    /**\n     * Handles the change event when a classroom is selected.\n     * It sets the selected classroom and updates the calendar agenda user ID if a teacher is associated with the classroom.\n     * It also handles the available duration based on the classroom type.\n     *\n     * @param event - The selected classroom object.\n     */\n    onClassroomChange(event) {\n      console.log(event);\n      this.studentSelectedClassroom = event;\n      console.log(event);\n      if (this.studentSelectedClassroom.teacher) {\n        this.calendarAgendaUserId = this.studentSelectedClassroom.teacher?.aspUserId;\n      }\n      this.handleClassroomTypeAvailableDuration(event);\n    }\n    getTeacherLabel(teacher) {\n      return ` ${teacher.lastName} ${teacher.firstName}`;\n    }\n    /**\n     * Updates the selected classroom when a user chooses a classroom from the UI.\n     * @param event - The change event triggered by selecting a classroom.\n     */\n    /**\n     * Calculates the dates of the current week, starting from the given start date.\n     * @param startDate - The start date for the current week.\n     */\n    calculateWeekDays(startDate) {\n      const today = moment(startDate).toDate();\n      const firstDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());\n      const firstDayCopy = new Date(firstDay);\n      this.weekDays = Array.from({\n        length: this.daysRange\n      }, (_, i) => {\n        const d = new Date(firstDayCopy);\n        d.setDate(d.getDate() + i);\n        return d;\n      });\n    }\n    /**\n     * Returns the date range for the current week.\n     * @returns {Object} An object with \"from\" and \"to\" properties representing the start and end dates of the week.\n     */\n    getDatesRange() {\n      // Convert date strings to Date objects\n      const dates = this.weekDays.map(dateString => new Date(dateString));\n      // Get the range of dates from and to\n      const fromDate = dates[0];\n      const toDate = dates[dates.length - 1];\n      // console.log(fromDate);\n      // Log the range of dates\n      return {\n        fromDate: moment(fromDate, 'ddd MMM D YYYY HH:mm:ss [GMT]ZZ').format('YYYY-MM-DD') + 'T09:29:27.434Z',\n        toDate: moment(toDate, 'ddd MMM D YYYY HH:mm:ss [GMT]ZZ').format('YYYY-MM-DD') + 'T09:29:27.434Z'\n      };\n    }\n    findMonthFromDateRange() {\n      const data = this.getDatesRange();\n      const fromDate = moment(data.fromDate);\n      const toDate = moment(data.toDate);\n      const fromMonth = fromDate.format('MMMM');\n      const toMonth = toDate.format('MMMM');\n      const fromYear = fromDate.format('YYYY');\n      const toYear = toDate.format('YYYY');\n      if (fromMonth === toMonth) {\n        return `${fromMonth} ${fromYear}`;\n      } else {\n        return `${fromMonth} - ${toMonth} ${toYear}`;\n      }\n    }\n    displayAvailableTimeSlots() {\n      const weekDayAbbreviations = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];\n      const availableHours = [];\n      const startHour = 0;\n      const endHour = 23;\n      const minuteIncrements = ['00', '15', '30', '45'];\n      for (let hour = startHour; hour <= endHour; hour++) {\n        const hourString = hour.toString().padStart(2, '0');\n        for (const minute of minuteIncrements) {\n          const timeString = `${hourString}:${minute}`;\n          let isAvailable = true;\n          // Check availability for each day\n          // for (const day of this.weekDays) {\n          //   const dayAbbr = weekDayAbbreviations[day.getDay()];\n          //   if (this.isTimeSlotAvailableOnDay(day.toDateString(), timeString)) {\n          //     isAvailable = true;\n          //   }\n          // }\n          if (isAvailable) {\n            availableHours.push(timeString);\n          }\n        }\n      }\n      return availableHours;\n    }\n    /**\n     * Calculates the available hours for scheduling.\n     */\n    calculateHours() {\n      const startHour = 0;\n      const endHour = 23;\n      const quarterHours = ['00', '15', '30', '45'];\n      this.hours = [];\n      for (let hour = startHour; hour <= endHour; hour++) {\n        const hourString = hour.toString().padStart(2, '0');\n        for (const minute of quarterHours) {\n          const timeString = `${hourString}:${minute}`;\n          this.hours.push(timeString);\n        }\n      }\n      for (let hour = 0; hour < startHour; hour++) {\n        const hourString = hour.toString().padStart(2, '0');\n        for (const minute of quarterHours) {\n          const timeString = `${hourString}:${minute}`;\n          this.hours.push(timeString);\n        }\n      }\n    }\n    convertHoursToMinutes(hours) {\n      return hours * 60;\n    }\n    removeValuesGreaterThanHoursLeft(hours) {\n      const hoursLeft = hours; // Hours left in Classroom for the selected teacher\n      const minutesLeft = this.convertHoursToMinutes(hoursLeft);\n      const maxDuration = this.convertHoursToMinutes(3); // Maximum duration of 3 hours\n      console.log(hoursLeft);\n      this.availableDuration = this.duration.filter(item => item.value <= minutesLeft && item.value <= maxDuration);\n    }\n    /**\n     * The function initializes a schedule by setting time slots from the minimum hour and minute to the\n     * end hour and minute in 15-minute intervals.\n     */\n    initSchedule() {\n      const startMinute = 0;\n      const endMinute = 45;\n      let minHour = 23;\n      let minMinute = 59;\n      for (const day in this.schedule) {\n        const dayObj = this.schedule[day];\n        for (const hour in dayObj) {\n          if (dayObj[hour]) {\n            const [hourStr, minuteStr] = hour.split(':');\n            const hourNum = parseInt(hourStr);\n            const minuteNum = parseInt(minuteStr);\n            if (hourNum < minHour || hourNum === minHour && minuteNum < minMinute) {\n              minHour = hourNum;\n              minMinute = minuteNum;\n            }\n          }\n        }\n      }\n      const startHour = minHour;\n      const endHour = 23;\n      for (let i = startHour; i <= endHour; i++) {\n        const hour = i.toString().padStart(2, '0');\n        for (let j = 0; j < 4; j++) {\n          let minute = j * 15 + startMinute;\n          if (minute >= 60) {\n            minute = 45;\n          }\n          if (i === endHour && minute > endMinute || i === startHour && minute < minMinute) {\n            continue;\n          }\n          const timeString = `${hour}:${minute.toString().padStart(2, '0')}`;\n          for (const day in this.schedule) {\n            this.schedule[day][timeString] = true;\n          }\n        }\n      }\n    }\n    /**\n     * Toggles the availability of a time slot on a specific day.\n     * @param day - The date object representing the day to toggle.\n     * @param hour - The hour string representing the time slot to toggle.\n     */\n    toggleSchedule(day, hour) {\n      const dateString = day.toDateString();\n      const isAvailable = !this.schedule[dateString]?.[hour];\n      this.schedule[dateString] = this.schedule[dateString] || {};\n      this.schedule[dateString][hour] = isAvailable;\n    }\n    /**\n     * Moves the calendar view back one week and recalculates the dates of the new week.\n     */\n    goToPrevWeek() {\n      this.startDate.setDate(this.startDate.getDate() - this.daysRange);\n      this.startDate = new Date(this.startDate);\n      console.log('prev', this.startDate);\n      this.calculateWeekDays(this.startDate);\n      this.subs.sink = this.getTeacherAvailability(this.selectedTeacher).subscribe(res => {\n        console.log(res);\n      });\n    }\n    /**\n     * Moves the calendar view forward one week and recalculates the dates of the new week.\n     */\n    goToNextWeek() {\n      this.startDate.setDate(this.startDate.getDate() + this.daysRange);\n      this.startDate = new Date(this.startDate);\n      this.calculateWeekDays(this.startDate);\n      this.subs.sink = this.getTeacherAvailability(this.selectedTeacher).subscribe(res => {});\n    }\n    /**\n     * Gets the availability status of a specific time slot on a specific day.\n     * @param dayString - The string representation of the date to check.\n     * @param hour - The hour string representing the time slot to check.\n     * @returns A boolean indicating whether the time slot is available or not.\n     */\n    isTimeSlotAvailableOnDay(dayString, hour) {\n      const day = moment(dayString, \"ddd MMM DD YYYY HH:mm:ss ZZ\");\n      const dayFormatted = day.format(\"DD/MM/YYYY\");\n      const dayAbbr = day.format(\"ddd\").toLowerCase();\n      if (this.businessHours) {\n        const dayObj = this.businessHours.find(a => a.date === dayFormatted);\n        // This code checks if the role of the user is equal to the 'TEACHER' role.\n        if (this.user.role === UserRole.TEACHER) {\n          return true;\n        }\n        if (dayObj && dayObj.availability.length > 0) {\n          const availableHours = dayObj.availability;\n          let hourIndex = this.hours.indexOf(hour);\n          const durationInMinutes = this.selectedDuration.value;\n          for (let i = 0; i < availableHours.length; i++) {\n            const range = availableHours[i];\n            let start = Math.max(this.hours.indexOf(range.from), hourIndex);\n            const end = range.to === \"00:00\" ? this.hours.length - 1 : this.hours.indexOf(range.to);\n            if (start >= 0 && end >= 0 && start <= hourIndex && hourIndex <= end) {\n              const availableDuration = (end - start + (range.to === \"00:00\" ? 2 : 1)) * 14;\n              if (availableDuration >= durationInMinutes) {\n                return true;\n              }\n            }\n          }\n        }\n      }\n      return false;\n    }\n    /**\n    * Creates a new lesson based on user input and sends it to backend.\n    *\n    * @returns void\n    */\n    onLessonRequested() {\n      // Check if a timeslot is selected\n      if (this.selectedTimeSlot === '') {\n        this.toastService.setShowToastmessage({\n          severity: 'info',\n          summary: '',\n          detail: 'Please select a timeslot'\n        });\n        return;\n      }\n      // Get the output date\n      const outputDate = this.getOutputDate();\n      // Get the current time\n      const currentTime = moment().startOf('hour');\n      // Get the UTC date\n      const utcDate = moment(this.selectedDay);\n      // Format the date\n      const formattedDate = utcDate.format('ddd MMM DD YYYY HH:mm:ss ZZ');\n      if (!this.isTimeSlotAvailableOnDay(formattedDate, this.selectedTimeSlot)) {\n        this.toastService.setShowToastmessage({\n          severity: 'info',\n          summary: '',\n          detail: 'Please select a correct timeslot'\n        });\n        return;\n      }\n      // Prevent button spam\n      if (this.buttonLoading) {\n        return;\n      }\n      // Set the button loading state to true\n      this.buttonLoading = true;\n      // Set the default lesson status\n      let status = LessonStatus.REQUESTED;\n      // Check if the user is a teacher and has extra classroom data\n      if (this.user.role === UserRole.TEACHER) {\n        if (this.hasExtraClassroomData()) {\n          if (this.extraClassroomData.status.toLowerCase().includes(ClassroomType.TRIAL.toLowerCase())) {\n            status = LessonStatus.ARRANGED_TRIAL;\n          } else {\n            status = LessonStatus.ARRANGED;\n          }\n        } else {\n          const matchingClassroom = this.availableClassrooms.find(classroom => {\n            return classroom.id === this.studentSelectedClassroom?.id;\n          });\n          if (matchingClassroom.type.toLowerCase().includes(ClassroomType.TRIAL.toLowerCase())) {\n            status = LessonStatus.ARRANGED_TRIAL;\n          } else {\n            status = LessonStatus.ARRANGED;\n          }\n        }\n      }\n      let classroomId;\n      // Check if the user is a student\n      if (this.user.role === UserRole.STUDENT) {\n        const matchingClassroom = this.availableClassrooms.find(classroom => {\n          return classroom.teacher.id === this.selectedTeachersLabel.teacherId && classroom.language === this.selectedTeachersLabel.language;\n        });\n        [classroomId] = matchingClassroom ? [matchingClassroom.id] : [null];\n      } else {\n        [classroomId] = [this.studentSelectedClassroom?.id];\n      }\n      // Create the lesson object\n      const lesson = {\n        classroomId,\n        status,\n        startingDate: this.generalService.formatSelectedDateTimeForLesson(this.selectedDay, this.selectedTimeSlot),\n        duration: this.generalService.convertMinutesToHours(this.selectedDuration.value),\n        isRecccuring: false\n      };\n      // Check if it is a rescheduling\n      if (this.isRescheduling) {\n        let lessonUpdate = {\n          id: this.extraData.lessonId,\n          classroomId,\n          status: this.isPending ? LessonStatus.ARRANGED : this.extraData.status,\n          startingDate: this.generalService.formatSelectedDateTimeForLesson(this.selectedDay, this.selectedTimeSlot),\n          duration: this.generalService.convertMinutesToHours(this.selectedDuration.value),\n          isRecccuring: false,\n          answered: this.isPending ? \"requested\" : \"accepted\"\n        };\n        console.log(lessonUpdate);\n        this.subs.sink = this.lessonService.update(lessonUpdate).subscribe(res => {\n          console.log(res);\n          this.toastService.setShowToastmessage({\n            severity: 'success',\n            summary: '',\n            detail: 'Lesson Rescheduled for ' + moment.parseZone(this.generalService.formatSelectedDateTimeForLesson(this.selectedDay, this.selectedTimeSlot)).format('DD-MM-YYYY HH:mm')\n          });\n          this.location.back();\n          // this.router.navigate(['/dashboard/calendar']);\n        }, error => {\n          this.buttonLoading = false;\n        });\n      } else {\n        const extraTimeString = moment.parseZone(this.generalService.formatSelectedDateTimeForLesson(this.selectedDay, this.selectedTimeSlot)).format('DD-MM-YYYY HH:mm');\n        this.subs.sink = this.lessonService.create(lesson).subscribe(res => {\n          console.log(res);\n          this.toastService.setShowToastmessage({\n            severity: 'success',\n            summary: '',\n            detail: status === LessonStatus.ARRANGED ? 'Lesson Created for ' + extraTimeString : 'Lesson Requested for ' + extraTimeString\n          });\n          this.location.back();\n        }, error => {\n          this.buttonLoading = false;\n        });\n      }\n    }\n    prepareInitialData() {\n      this.subs.sink = this.classroomService.getLMSUserClassrooms(this.user.id).pipe(switchMap(classrooms => {\n        // sort classrooms first\n        classrooms = this.classroomService.sortClassroomsByFirstName(classrooms);\n        return this.addTeacherLabelToArrays(classrooms);\n        //TODO: remove when database is updated - START\n        // const modifiedArray = Object.values(this.teachersLabels.reduce((acc, { teacherId, hoursLeft, ...rest }) => {\n        //   if (acc[teacherId]) {\n        //     acc[teacherId].hoursLeft += hoursLeft;\n        //   } else {\n        //     acc[teacherId] = { teacherId, hoursLeft, ...rest };\n        //   }\n        //   return acc;\n        // }, {}));\n        // this.teachersLabels = modifiedArray;\n        //TODO: remove when database is updated - END\n        // const uniqueTeachers = [...new Set(this.availableTeachers.map(t => t.id))].map(id => this.availableTeachers.find(t => t.id === id));\n        // this.availableTeachers = uniqueTeachers;\n        if (this.user.role === UserRole.TEACHER) {\n          this.studentSelectedClassroom = this.availableClassrooms[0];\n          this.calendarAgendaUserId = this.studentSelectedClassroom.teacher?.aspUserId;\n        } else {\n          this.selectedTeacher = this.availableTeachers[0];\n          this.selectedTeachersLabel = this.teachersLabels[0];\n          const classroomId = this.availableClassrooms.find(item => item.id === this.selectedTeachersLabel.classroom);\n          this.removeValuesGreaterThanHoursLeft(classroomId.hoursLeft);\n          this.calendarAgendaUserId = this.selectedTeacher.aspUserId;\n        }\n        return this.getTeacherAvailability(this.availableTeachers[0]);\n      })).subscribe(res => {\n        console.log(res);\n        // this.filteredLessonEvents = this.lessonEvents;\n      });\n    }\n    /**\n     * Formats a date object to a string in the format 'dddd DD/MM'.\n     * @param {Date} date - The date object to format.\n     * @returns {string} The formatted date string.\n     */\n    formatDateWithDay(date) {\n      const dateString = date.toDateString();\n      const momentDate = moment(dateString);\n      return momentDate.format('dddd DD/MM');\n    }\n    onAgendaDayClicked(event) {\n      console.log(event);\n      this.calculateWeekDays(event);\n      this.subs.add(this.getTeacherAvailability(this.selectedTeacher).subscribe(res => {\n        this.filteredLessonEvents = this.lessonEvents;\n        console.log('filteredLessonEvents', this.filteredLessonEvents);\n      }));\n    }\n    /**\n     * The function adds teacher labels to arrays of classrooms and performs various initialization tasks.\n     * @param {Classroom[]} classrooms - An array of Classroom objects.\n     * @returns the result of the `getTeacherAvailability` function with the first element of the\n     * `availableTeachers` array as the argument.\n     */\n    addTeacherLabelToArrays(classrooms) {\n      if (this.hasExtraData()) {\n        const classroom = this.getClassroomById(classrooms);\n        if (!classroom) {\n          console.log(`No teacher label found for classroom`);\n          return undefined;\n        }\n        this.addTeacherLabel(classroom);\n        this.addAvailableClassroom(classroom);\n        console.log(classroom);\n        this.addAvailableTeacher(classroom.teacher);\n      } else {\n        const events = this.getAvailableClassroomEvents(classrooms);\n        console.log(`No teacher label found for classroom`, events);\n        this.lessonEvents.push(...events);\n      }\n      this.setInitialValuesForUserRole();\n      this.initializeStartDateAndTime();\n      this.initializeWeekDays();\n      if (this.authService.isTeacher) {\n        if (!this.studentSelectedClassroom) {\n          this.noClassroomsFound = true;\n          return of(null);\n        }\n      }\n      this.initializeCalendarAgendaUserId();\n      return this.getTeacherAvailability(this.availableTeachers[0]);\n    }\n    /**\n     * The function `getClassroomById` takes an array of `Classroom` objects and returns the `Classroom`\n     * object with a matching `id` property, or `undefined` if no match is found.\n     * @param {Classroom[]} classrooms - An array of Classroom objects.\n     * @returns a Classroom object or undefined.\n     */\n    getClassroomById(classrooms) {\n      const classroomId = this.extraData.classroomId;\n      return classrooms.find(classroom => classroom.id === classroomId);\n    }\n    /**\n     * The addTeacherLabel function adds a teacher label to the teachersLabels array for a given classroom.\n     * @param {Classroom} classroom - The classroom parameter is an object that represents a classroom.\n     */\n    addTeacherLabel(classroom) {\n      const icon = this.getTeacherLabelIcon(classroom);\n      const teachersLabel = this.createTeachersLabel(classroom, icon);\n      this.teachersLabels.push(teachersLabel);\n    }\n    /**\n     * The function adds a classroom to the list of available classrooms.\n     * @param {Classroom} classroom - The parameter \"classroom\" is of type \"Classroom\".\n     */\n    addAvailableClassroom(classroom) {\n      this.availableClassrooms.push(classroom);\n    }\n    /**\n     * The function adds a teacher to the list of available teachers.\n     * @param {User} teacher - The \"teacher\" parameter is of type \"User\".\n     */\n    addAvailableTeacher(teacher) {\n      this.availableTeachers.push(teacher);\n    }\n    /**\n     * The function sets initial values for user role based on certain conditions and handles classroom\n     * selection.\n     */\n    setInitialValuesForUserRole() {\n      if (this.isRescheduling) {\n        this.selectedDuration = this.duration.find(duration => duration.value === this.extraData.duration * 60);\n      } else {\n        this.selectedDuration = this.duration[3];\n      }\n      if (this.hasExtraClassroomData()) {\n        this.studentSelectedClassroom = this.availableClassrooms.find(item => item.id === this.extraClassroomData.classroomId);\n        if (this.studentSelectedClassroom) {\n          this.removeValuesGreaterThanHoursLeft(this.classroomService.getAccumulatedHoursLeft(this.studentSelectedClassroom.packages));\n        }\n        if (!this.studentSelectedClassroom) {\n          this.toastService.setShowToastmessage({\n            severity: 'info',\n            summary: '',\n            detail: 'Not enough hours for previously selected classroom. Please select another.'\n          });\n          this.handleSelectedClassroomForUserRole();\n        } else {\n          this.selectedTeacher = this.studentSelectedClassroom.teacher;\n          this.selectedTeachersLabel = this.teachersLabels.find(item => item.classroom === this.studentSelectedClassroom.id);\n        }\n      } else {\n        if (this.user.role === UserRole.TEACHER) {\n          this.studentSelectedClassroom = this.availableClassrooms[0];\n          this.selectedTeacher = this.user;\n        } else {\n          this.handleSelectedClassroomForUserRole();\n        }\n      }\n    }\n    /**\n     * The function handles the selection of a classroom based on the user's role and performs additional\n     * operations based on the selected classroom.\n     */\n    handleSelectedClassroomForUserRole() {\n      if (this.user.role === UserRole.TEACHER) {\n        this.studentSelectedClassroom = this.availableClassrooms[0];\n        this.selectedTeacher = this.user;\n      } else {\n        console.log(this.availableTeachers);\n        this.selectedTeacher = this.availableTeachers[0];\n        this.selectedTeachersLabel = this.teachersLabels[0];\n        const classroom = this.availableClassrooms.find(item => item.id === this.selectedTeachersLabel.classroom);\n        if (classroom) {\n          if (!this.isRescheduling) {\n            console.log(classroom);\n            this.handleClassroomTypeAvailableDuration(classroom);\n            this.availableDuration = this.duration.filter(duration => {\n              return this.generalService.convertMinutesToHours(duration.value) <= this.classroomService.getAccumulatedHoursLeft(classroom.packages);\n            });\n            // this.removeValuesGreaterThanHoursLeft(classroom.packages[0].hoursLeft);\n          }\n        }\n      }\n    }\n    /**\n     * The function `handleClassroomTypeAvailableDuration` sets the available duration options for a\n     * classroom based on its type and removes any options that exceed the remaining hours.\n     * @param {Classroom} classroom - The \"classroom\" parameter is an object of type Classroom.\n     */\n    handleClassroomTypeAvailableDuration(classroom) {\n      if (this.classroomService.isTrialClassroom(classroom)) {\n        const trialDuration = this.duration[3];\n        this.availableDuration = [trialDuration];\n        this.selectedDuration = trialDuration;\n      } else {\n        this.availableDuration = [...this.duration];\n        this.selectedDuration = this.duration[3];\n        const accumulatedHoursLeft = this.classroomService.getAccumulatedHoursLeft(classroom.packages);\n        this.removeValuesGreaterThanHoursLeft(accumulatedHoursLeft);\n      }\n    }\n    onCurrentMonthChanged(monthNumber) {\n      const outputDate = this.getOutputDate();\n      console.log(outputDate);\n      // this.selectedDay = undefined;\n      // const currentTime = moment().startOf('hour');\n      // const time = currentTime.format('HH:00');\n      // const utcDate = moment(outputDate);\n      // // Fri Jul 14 2023 00:00:00 GMT+0300 (Eastern European Summer Time)\n      // const formattedDate = utcDate.format('ddd MMM DD YYYY HH:mm:ss [GMT]ZZ (z)');\n      // if (this.isTimeSlotAvailableOnDay(formattedDate, this.selectedTimeSlot)) {\n      //   this.onTimeSlotSelected(outputDate, time);\n      // } else {\n      //   this.selectedTimeSlot = '';\n      //   this.radioChecked = false;\n      // }\n    }\n    initializeStartDateAndTime() {\n      const outputDate = this.getOutputDate();\n      this.startDate = new Date(outputDate);\n      const inputDate = this.extraData.startingDate;\n      if (!this.isRescheduling) {\n        // (TODO) init an hour to help the ui \n        // const currentTime = moment().add(2, 'hour').startOf('hour');\n        // const time = currentTime.format('HH:00');\n        // this.onTimeSlotSelected(outputDate, time);\n      } else {\n        const selectedTime = moment(inputDate).format('HH:mm');\n        this.onTimeSlotSelected(outputDate, selectedTime);\n      }\n    }\n    /**\n     * The function initializes the week days by calculating them based on the output date.\n     */\n    initializeWeekDays() {\n      const outputDate = this.getOutputDate();\n      this.calculateWeekDays(outputDate);\n    }\n    /**\n     * The function takes in an array of classrooms and returns an array of lesson events for the available\n     * classrooms.\n     * @param {Classroom[]} classrooms - An array of Classroom objects.\n     * @returns an array of Lesson objects.\n     */\n    getAvailableClassroomEvents(classrooms) {\n      return this.classroomService.filterAvailableClassrooms(classrooms).flatMap(classroom => {\n        const teachersLabel = this.createTeachersLabelForEvent(classroom);\n        this.teachersLabels.push(teachersLabel);\n        this.availableClassrooms.push(classroom);\n        this.availableTeachers.push(classroom.teacher);\n        return classroom.lessons.map(lesson => this.createLessonEvent(lesson, classroom));\n      });\n    }\n    /**\n     * The function creates a label for teachers based on the given classroom information.\n     * @param {Classroom} classroom - The \"classroom\" parameter is an object of type \"Classroom\".\n     * @returns an object with the following properties:\n     */\n    createTeachersLabelForEvent(classroom) {\n      let icon = 'student';\n      if (classroom.type && classroom.type.includes('Trial')) {\n        icon = 'blue-plane';\n      }\n      if (classroom.type && classroom.type.includes('Gift')) {\n        icon = 'gift-box';\n      }\n      return {\n        language: classroom.language,\n        teacherName: classroom.teacher && classroom.teacher.firstName,\n        icon,\n        hoursLeft: classroom.packages && classroom.packages.length > 0 ? this.classroomService.getAccumulatedHoursLeft(classroom.packages) : classroom.hoursLeft,\n        teacherId: classroom.teacher && classroom.teacher.id,\n        classroom: classroom.id,\n        classroomType: classroom.type\n      };\n    }\n    /**\n     * The function creates a lesson event object with specific properties based on the given lesson and\n     * classroom data.\n     * @param {any} lesson - The lesson parameter is an object that represents a lesson. It contains\n     * properties such as startingDate (the date and time the lesson starts), duration (the duration of the\n     * lesson in hours), and id (a unique identifier for the lesson).\n     * @param {Classroom} classroom - The `classroom` parameter is an object that represents a classroom.\n     * It likely contains information such as the classroom name, teacher, students, and other relevant\n     * details.\n     * @returns an object with the following properties:\n     * - id: the id of the lesson\n     * - title: the short full name of the teacher\n     * - start: the starting date and time of the lesson (with the time set to 12:00:00)\n     * - end: the ending date and time of the lesson (calculated based on the starting date and the\n     * duration of the lesson\n     */\n    createLessonEvent(lesson, classroom) {\n      const start = new Date(lesson.startingDate);\n      const end = new Date(start.getTime() + lesson.duration * 60 * 60 * 1000);\n      const {\n        teacher\n      } = classroom;\n      const title = this.generalService.getShortFullName(teacher);\n      return {\n        id: lesson.id,\n        title,\n        start: lesson.startingDate.slice(0, 11) + '12:00:00',\n        end,\n        allDay: false,\n        backgroundColor: '#378006',\n        borderColor: '#378006',\n        textColor: '#ffffff',\n        extendedProps: {\n          type: 'lesson',\n          lesson,\n          classRoom: classroom\n        },\n        editable: false\n      };\n    }\n    /**\n     * The function initializes the calendar agenda user ID based on the user's role and selected classroom\n     * or teacher.\n     */\n    initializeCalendarAgendaUserId() {\n      if (this.authService.isTeacher) {\n        console.log(this.studentSelectedClassroom);\n        if (!this.studentSelectedClassroom) {\n          this.showToastMessage('info', '', 'No available classrooms for booking');\n        }\n        this.calendarAgendaUserId = this.getTeacherAspUserId();\n      } else if (this.authService.isStudent && this.selectedTeacher) {\n        this.calendarAgendaUserId = this.selectedTeacher.aspUserId;\n      } else {\n        this.showToastMessage('info', '', 'No available classrooms for booking or enrolled classrooms have no available hours.');\n        this.goBack();\n      }\n    }\n    showToastMessage(severity, summary, detail) {\n      this.toastService.setShowToastmessage({\n        severity: severity,\n        summary: summary,\n        detail: detail\n      });\n    }\n    getTeacherAspUserId() {\n      return this.studentSelectedClassroom.teacher?.aspUserId;\n    }\n    goBack() {\n      this.location.back();\n    }\n    getTeacherLabelIcon(teacherLabel) {\n      let icon = 'student';\n      if (teacherLabel.status.includes('Trial')) {\n        icon = 'blue-plane';\n      } else if (teacherLabel.status.includes('Gift')) {\n        icon = 'gift-box';\n      }\n      return icon;\n    }\n    createTeachersLabel(teacherLabel, icon) {\n      const {\n        language,\n        teacher,\n        id,\n        hoursLeft\n      } = teacherLabel;\n      console.log(teacherLabel);\n      return {\n        language,\n        teacherName: teacher.firstName,\n        icon,\n        hoursLeft,\n        teacherId: teacher.id,\n        classroom: id\n      };\n    }\n    getOutputDate() {\n      const outputFormat = \"ddd MMM DD YYYY HH:mm:ss [GMT]ZZ (z)\";\n      const inputDate = this.extraData.startingDate;\n      const outputDateStr = moment.utc(inputDate).startOf('day').local().format(outputFormat);\n      const outputDate = new Date(Date.parse(outputDateStr));\n      outputDate.setHours(0, 0, 0, 0);\n      return outputDate;\n    }\n    hasExtraData() {\n      return typeof this.extraData !== 'undefined' && Object.keys(this.extraData).length > 0;\n    }\n    hasExtraClassroomData() {\n      return !!this.extraClassroomData && Object.keys(this.extraClassroomData).length > 0;\n    }\n    /**\n     * Check if the selected date and time is in the past.\n     * @param {Date} selectedDateTime - The selected date and time.\n     * @returns {boolean} True if the selected date and time is in the past, false otherwise.\n     */\n    isDateTimeInThePast(selectedDateTime) {\n      const currentDateTime = moment();\n      const selectedDateTimeMoment = moment(selectedDateTime);\n      return selectedDateTimeMoment.isBefore(currentDateTime);\n    }\n    static #_ = this.ɵfac = function BookingSystemComponent_Factory(t) {\n      return new (t || BookingSystemComponent)(i0.ɵɵdirectiveInject(i1.ClassroomService), i0.ɵɵdirectiveInject(i2.GeneralService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.UserService), i0.ɵɵdirectiveInject(i5.CalendarService), i0.ɵɵdirectiveInject(i6.LayoutService), i0.ɵɵdirectiveInject(i7.LessonService), i0.ɵɵdirectiveInject(i8.ToastService), i0.ɵɵdirectiveInject(i9.ActivatedRoute), i0.ɵɵdirectiveInject(i9.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i10.Location));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BookingSystemComponent,\n      selectors: [[\"app-booking-system\"]],\n      viewQuery: function BookingSystemComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n          i0.ɵɵviewQuery(_c1, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.bookingWrapper = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollPanel = _t.first);\n        }\n      },\n      hostBindings: function BookingSystemComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function BookingSystemComponent_resize_HostBindingHandler() {\n            return ctx.isTabletScreenSize();\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      inputs: {\n        studentSelectedClassroom: \"studentSelectedClassroom\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 60,\n      vars: 23,\n      consts: [[\"bookingWrapper\", \"\"], [\"scrollPanel\", \"\"], [\"header\", \"Booking System\", \"headerBackgroundImage\", \"/assets/images/dashboard/calendar-banner.svg\", \"blockClass\", \"border-radius-bottom-10\", \"containerClass\", \"bg-white p-2 border-round-lg\", 2, \"height\", \"inherit\", 3, \"headerClass\", \"backButtonLabel\", \"headerTextClass\"], [1, \"surface-section\", \"lg:px-1\", \"lg:h-full\"], [1, \"grid\", \"h-full\", \"flex-column\", \"lg:flex-row\", \"relative\", \"overflow-y-scroll\"], [1, \"calendar-nav\", \"hidden\", \"lg:block\"], [\"type\", \"button\", 1, \"prev\", 3, \"click\"], [\"src\", \"/assets/icons/arrow-right-calendar-grad.svg\"], [\"type\", \"button\", 1, \"next\", 3, \"click\"], [1, \"col\", \"lg:col-fixed\", \"lg:pr-0\", \"lg:m-3\", \"lg:m-0\", \"left-col\"], [1, \"flex\", \"align-items-center\", \"justify-content-start\", \"flex-wrap\", \"gap-3\", \"mb-3\"], [\"for\", \"Teacher\", 1, \"col-fix\", \"flex\", \"gap-1\", \"font-sm\", 2, \"width\", \"70px\"], [\"src\", \"/assets/icons/student.svg\"], [4, \"ngIf\"], [\"for\", \"Duration\", 1, \"col-fix\", \"flex\", \"gap-1\", \"font-sm\", 2, \"width\", \"70px\"], [\"src\", \"/assets/icons/hourglass.svg\"], [\"optionLabel\", \"viewValue\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\"], [1, \"flex\", \"flex-column\", \"justify-content-between\", 3, \"ngStyle\"], [1, \"mt-5\"], [\"title\", \"\", 3, \"dayClicked\", \"currentMonthChanged\", \"currentSelectedDay\", \"reverseGradient\", \"showAvailability\", \"userId\"], [1, \"grid\", \"font-2xs\", \"justify-content-between\"], [1, \"col-fixed\", \"col-circle\", \"pr-0\", \"pb-0\", \"gap-1\"], [1, \"high-bg\", \"circle\"], [1, \"low-bg\", \"circle\"], [1, \"col-fixed\", \"col-circle\", \"pb-0\", \"gap-1\"], [1, \"no-bg\", \"circle\"], [1, \"col\", \"h-full\", \"p-2\", \"md:pr-4\", \"right-col\"], [1, \"calendar-nav\", \"relative\", \"block\", \"lg:hidden\"], [\"type\", \"button\", 1, \"prev\", \"z-5\", 3, \"click\"], [\"type\", \"button\", 1, \"next\", \"z-5\", 3, \"click\"], [1, \"appointment-popup\", \"h-full\", \"pl-1\"], [\"id\", \"calendar\", 1, \"appointment-calendar\", \"h-full\"], [1, \"calendar-wrapper\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"text-primary\", \"mb-1\"], [1, \"calendar-week\", \"relative\", \"flex\", \"justify-content-center\"], [1, \"p-0\", \"mx-2\"], [4, \"ngFor\", \"ngForOf\"], [1, \"calendar-hours\"], [\"styleClass\", \"custombar1 h-30rem lg:h-full\"], [1, \"calendar-inside\", \"pb-2\"], [\"styleClass\", \"max-w-10rem\", \"panelStyleClass\", \"bordered-panel\", \"placeholder\", \"Select Classroom\", 3, \"ngModelChange\", \"options\", \"ngModel\"], [\"pTemplate\", \"selectedItem\"], [\"pTemplate\", \"item\"], [\"class\", \"flex align-items-center gap-2\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"height\", \"12\", 3, \"src\", 4, \"ngIf\"], [1, \"font-xs\", 3, \"innerHTML\"], [\"height\", \"12\", 3, \"src\"], [1, \"flex\", \"gap-1\", \"font-xs\"], [\"src\", \"/assets/images/dashboard/calendar/check-circled.svg\", 2, \"width\", \"12px\"], [\"optionLabel\", \"teacherName\", \"styleClass\", \"max-w-10rem\", \"placeholder\", \"Select Teacher\", 3, \"ngModelChange\", \"options\", \"ngModel\"], [1, \"capitalize\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"font-xs\"], [2, \"width\", \"18px\", 3, \"src\"], [1, \"border-round-xl\", \"block-gradient-reverse\", \"mt-3\", \"mb-0\"], [1, \"card-header\", \"text-center\"], [1, \"card-title\", \"font-xs\", \"py-2\"], [1, \"card-body\", \"px-2\"], [1, \"flex\", \"justify-content-center\", \"font-sm\", \"gap-1\", \"text-primary\"], [1, \"\"], [1, \"pi\", \"pi-calendar\", \"font-sm\", \"mr-1\"], [1, \"pi\", \"pi-clock\", \"font-sm\", \"mr-1\"], [1, \"pi\", \"pi-hourglass\", \"font-sm\", \"mr-1\"], [3, \"hidden\"], [1, \"grid\", \"justify-content-evenly\", \"font-2xs\", \"mt-2\"], [1, \"col-fixed\", \"flex\", \"align-items-center\"], [\"src\", \"/assets/icons/add-to-calendar.svg\", \"width\", \"15\", 1, \"mr-1\"], [1, \"col-fixed\", \"align-items-center\", \"pr-0\"], [1, \"flex\", \"justify-content-center\", \"mt-2\"], [\"styleClass\", \" px-2 \", \"icon\", \"pi pi-calendar\", \"iconPos\", \"left\", \"pButton\", \"\", \"type\", \"button\", 1, \"font-base\", \"mt-2\", \"border-round-lg\", \"request\", 3, \"click\", \"loading\", \"label\"], [1, \"flex\", \"w-full\", \"flex-column\", \"align-items-center\", \"justify-content-center\"], [1, \"text-center\", \"w-full\", \"pt-5\"], [\"src\", \"/assets/icons/fast-time-clock-ui-icon.png\", 1, \"max-w-14rem\"], [1, \"text-primary\", \"font-lg\"], [\"type\", \"button\", \"class\", \"font-lg gray-border-button border-round-3xl gap-2 px-3 py-2\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"font-lg\", \"gray-border-button\", \"border-round-3xl\", \"gap-2\", \"px-3\", \"py-2\", 3, \"click\"], [1, \"relative\", \"font-sm\", 3, \"ngClass\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", 3, \"ngClass\"], [1, \"hour-label\", 3, \"click\", \"for\"], [\"type\", \"radio\", \"name\", \"timeslot\", 3, \"disabled\", \"id\", \"value\", \"checked\"]],\n      template: function BookingSystemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", null, 0)(2, \"app-block-viewer\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function BookingSystemComponent_Template_button_click_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.goToPrevWeek());\n          });\n          i0.ɵɵelement(7, \"img\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function BookingSystemComponent_Template_button_click_8_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.goToNextWeek());\n          });\n          i0.ɵɵelement(9, \"img\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 9)(11, \"div\", 10)(12, \"label\", 11);\n          i0.ɵɵelement(13, \"img\", 12);\n          i0.ɵɵtext(14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(15, BookingSystemComponent_ng_container_15_Template, 4, 2, \"ng-container\", 13)(16, BookingSystemComponent_ng_container_16_Template, 4, 2, \"ng-container\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 10)(18, \"label\", 14);\n          i0.ɵɵelement(19, \"img\", 15);\n          i0.ɵɵtext(20, \" Duration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"p-dropdown\", 16);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function BookingSystemComponent_Template_p_dropdown_ngModelChange_21_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedDuration, $event) || (ctx.selectedDuration = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"onChange\", function BookingSystemComponent_Template_p_dropdown_onChange_21_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onDurationSelected($event));\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 17)(23, \"div\")(24, \"div\", 18)(25, \"app-calendar-agenda\", 19);\n          i0.ɵɵlistener(\"dayClicked\", function BookingSystemComponent_Template_app_calendar_agenda_dayClicked_25_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onAgendaDayClicked($event));\n          })(\"currentMonthChanged\", function BookingSystemComponent_Template_app_calendar_agenda_currentMonthChanged_25_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCurrentMonthChanged($event));\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 20)(27, \"div\", 21);\n          i0.ɵɵelement(28, \"div\", 22);\n          i0.ɵɵelementStart(29, \"div\");\n          i0.ɵɵtext(30, \"High Availability\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 21);\n          i0.ɵɵelement(32, \"div\", 23);\n          i0.ɵɵelementStart(33, \"div\");\n          i0.ɵɵtext(34, \"Low Availability\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"div\", 24);\n          i0.ɵɵelement(36, \"div\", 25);\n          i0.ɵɵelementStart(37, \"div\");\n          i0.ɵɵtext(38, \"No Availability\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(39, BookingSystemComponent_div_39_Template, 27, 6, \"div\", 13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"div\", 26)(41, \"div\", 27)(42, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function BookingSystemComponent_Template_button_click_42_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.goToPrevWeek());\n          });\n          i0.ɵɵelement(43, \"img\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"button\", 29);\n          i0.ɵɵlistener(\"click\", function BookingSystemComponent_Template_button_click_44_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.goToNextWeek());\n          });\n          i0.ɵɵelement(45, \"img\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"div\", 30)(47, \"div\", 31)(48, \"div\", 32)(49, \"div\", 33);\n          i0.ɵɵtext(50);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"div\", 34)(52, \"ul\", 35);\n          i0.ɵɵtemplate(53, BookingSystemComponent_li_53_Template, 3, 5, \"li\", 36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(54, \"div\", 37)(55, \"p-scrollPanel\", 38, 1);\n          i0.ɵɵtemplate(57, BookingSystemComponent_ng_container_57_Template, 10, 1, \"ng-container\", 13);\n          i0.ɵɵelementStart(58, \"ul\", 39);\n          i0.ɵɵtemplate(59, BookingSystemComponent_li_59_Template, 2, 1, \"li\", 36);\n          i0.ɵɵelementEnd()()()()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"headerClass\", \"justify-content-center my-2\")(\"backButtonLabel\", \"Back\")(\"headerTextClass\", \"font-xl font-semibold justify-content-center\");\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate1(\" \", ctx.user.role == ctx.UserRoles.TEACHER ? \"Classroom\" : \"Teacher\", \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.user.role == ctx.UserRoles.TEACHER);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.user.role == ctx.UserRoles.STUDENT);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"options\", ctx.availableDuration);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedDuration);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(20, _c2, !ctx.isTabletScreenSize() ? ctx.height - 150 + \"px\" : \"auto\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"currentSelectedDay\", ctx.selectedDay)(\"reverseGradient\", true)(\"showAvailability\", true)(\"userId\", ctx.calendarAgendaUserId);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngIf\", ctx.radioChecked);\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate1(\" \", ctx.findMonthFromDateRange(), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.weekDays);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(22, _c3));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.noClassroomsFound);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.weekDays);\n        }\n      },\n      dependencies: [i11.NgControlStatus, i11.NgModel, i10.NgClass, i10.NgForOf, i10.NgIf, i10.NgStyle, i12.BlockViewerComponent, i13.CalendarAgendaComponent, i14.ButtonDirective, i15.PrimeTemplate, i16.ScrollPanel, i17.Dropdown, i10.DatePipe],\n      styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}.hour-label[_ngcontent-%COMP%]{font-size:clamp(.75rem,.12vw + .67rem,.88rem)}.hour-label.checked[_ngcontent-%COMP%]{color:#2fb9d3;font-weight:bolder}[_nghost-%COMP%]     .block-section{height:inherit}[_nghost-%COMP%]     .block-content{height:calc(100% - 52px)}[_nghost-%COMP%]     .block-content .bg-white{height:100%;padding-bottom:0!important}[_nghost-%COMP%]     .p-scrollpanel-content{padding:0}[_nghost-%COMP%]     .request.p-button{background:linear-gradient(#657aef,#1a266c);width:auto;justify-content:center}[_nghost-%COMP%]     .request.p-button .p-button-label{flex:unset}[_nghost-%COMP%]     p-dropdown .p-dropdown{width:100%;background-image:linear-gradient(to bottom,#fff,#b4b4fa33);border:none}[_nghost-%COMP%]     p-dropdown .p-dropdown .p-inputtext{padding:.3rem 1rem;font-size:.85rem;font-family:Proxima Nova Regular}[_nghost-%COMP%]     p-dropdown .p-dropdown-panel{box-shadow:none}[_nghost-%COMP%]     p-dropdown .p-dropdown-panel .p-dropdown-items{background-image:linear-gradient(to bottom,#b4b4fa33,#8492f733,#3873f433);color:#2e3d90}[_nghost-%COMP%]     p-dropdown .p-dropdown:not(.p-disabled).p-focus{border:0;box-shadow:none}[_nghost-%COMP%]     .reversed-gradient .cal-header{width:100%}[_nghost-%COMP%]     .table-container{overflow-x:hidden!important}[_nghost-%COMP%]     .card{border:0}[_nghost-%COMP%]     .card .p-datepicker{padding-top:0;padding-bottom:0}[_nghost-%COMP%]     .card .p-datepicker table th{padding-top:0;padding-bottom:0;border:0}[_nghost-%COMP%]     .card .p-datepicker table td{height:40px}[_nghost-%COMP%]     .card .p-datepicker table thead th{color:#cacbd9}.left-col[_ngcontent-%COMP%]{width:40%}.right-col[_ngcontent-%COMP%]{width:60%}.no-availability[_ngcontent-%COMP%]{color:#cacbd9}.availability[_ngcontent-%COMP%]{color:#2e3d90}.block-header[_ngcontent-%COMP%]{position:relative;z-index:2;min-height:60px;box-shadow:0 3px 8px #b3b3b3f5;border-radius:16px}.block-header[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;background-image:url(/assets/images/lessons/calendar-availability-gradient-bg.png);background-size:inherit;background-repeat:no-repeat;background-position:center;width:100%;height:100%;left:0;top:0;z-index:1;border-radius:16px}.appointment-popup[_ngcontent-%COMP%]{position:relative;width:100%;z-index:1;overflow:hidden}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]{width:100%;display:flex;flex-direction:column}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .current-week[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:.5rem 0;border-bottom:1px solid #f3f3f3;align-items:center}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .current-week[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]{font-size:1rem;color:#2d3152}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .current-week[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:.5rem;opacity:.5}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:inherit}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-week[_ngcontent-%COMP%]{border-bottom:0!important}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-week[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{padding:0 3px 0 0;list-style:none;display:flex;width:100%;margin:0;background-image:linear-gradient(359deg,rgba(0,44,207,.1882352941),transparent);border-radius:14px}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-week[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{width:100%;padding:.5rem 0;text-align:center;color:#575757}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]{padding:0 0 0 1rem;overflow:auto;height:100%}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{list-style:none;padding:0;margin:0}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%]{display:flex}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%] > li[_ngcontent-%COMP%]{width:100%;text-align:center}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%] > li[_ngcontent-%COMP%] > ul[_ngcontent-%COMP%] > li[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:1rem;padding-top:6px;padding-bottom:6px}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%] > li[_ngcontent-%COMP%] > ul[_ngcontent-%COMP%] > li.no-availability[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{cursor:initial}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%] > li[_ngcontent-%COMP%] > ul[_ngcontent-%COMP%] > li.availability[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]:hover{color:#2fb9d3;cursor:pointer}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%] > li[_ngcontent-%COMP%] > ul[_ngcontent-%COMP%] > li[_ngcontent-%COMP%]   input[type=radio i][_ngcontent-%COMP%]{appearance:none;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;-o-appearance:none;width:20px;height:20px;margin-right:5px}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%] > li[_ngcontent-%COMP%] > ul[_ngcontent-%COMP%] > li[_ngcontent-%COMP%]   input[type=radio i][_ngcontent-%COMP%]:before{background-color:#fff;content:\\\"\\\";display:inline-block;width:16px;height:16px;border-radius:50%;transition:background-color .2s ease;cursor:pointer}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%] > li[_ngcontent-%COMP%] > ul[_ngcontent-%COMP%] > li[_ngcontent-%COMP%]   input[type=radio i][_ngcontent-%COMP%]:checked:before{background-color:#2fb9d3;border:1px solid #2FB9D3;position:absolute}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%] > li[_ngcontent-%COMP%] > ul[_ngcontent-%COMP%] > li[_ngcontent-%COMP%]   input[type=radio i][_ngcontent-%COMP%]:checked:after{content:\\\"\\\";display:block;position:relative;width:6px;height:10px;border-bottom:2px solid #fff;border-right:2px solid #fff;transform:rotate(45deg);top:2px;left:4px}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-buttons[_ngcontent-%COMP%]{display:flex;border-top:1px solid #f3f3f3;margin:0 -2rem}@media screen and (max-width: 768px){.appointment-popup[_ngcontent-%COMP%]{flex-direction:column;width:100%;max-width:100%;top:0;border-radius:0;overflow:auto;height:calc(100% - 6.4rem);min-height:auto;max-height:100%}.appointment-popup[_ngcontent-%COMP%]   .calendar-nav[_ngcontent-%COMP%]{flex-wrap:wrap}.appointment-popup[_ngcontent-%COMP%]   .calendar-nav[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:before{padding:0 1rem;font-size:1.2rem}.appointment-popup[_ngcontent-%COMP%]   .calendar-nav[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]{font-size:1.5rem}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]{padding:0}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]{padding:0;margin:0}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%] > ul[_ngcontent-%COMP%] > li[_ngcontent-%COMP%] > ul[_ngcontent-%COMP%] > li[_ngcontent-%COMP%]{padding:1rem 0 .75rem;font-size:1.2rem}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-buttons[_ngcontent-%COMP%]{margin:0}}.round[_ngcontent-%COMP%]{position:relative}.round[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{background-color:#fff;border:1px solid #ccc;border-radius:50%;cursor:pointer;height:14px;left:0;position:absolute;top:3px;width:14px}.round[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]:after{border:2px solid #fff;border-top:none;border-right:none;content:\\\"\\\";height:6px;left:1px;opacity:0;position:absolute;top:2px;transform:rotate(-45deg);width:10px}.round[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]{visibility:hidden}.round[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked + label[_ngcontent-%COMP%]{background-color:#2fb9d3;border-color:#2fb9d3}.round[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked + label[_ngcontent-%COMP%]:after{opacity:1}.high-bg[_ngcontent-%COMP%]{background-image:linear-gradient(to bottom,#64cfdd,#48c4e0,#2cb8e2,#18abe4,#219de3,#1591e2,#1e85e0,#3177dc,#2a69da,#2b5ad6,#334ad0,#3f37c9)}.low-bg[_ngcontent-%COMP%]{background-image:linear-gradient(to bottom,#9baaff,#7c8aec,#5e6ad9,#3f4bc4,#152caf)}.no-bg[_ngcontent-%COMP%]{background:#dfe8fc}.circle[_ngcontent-%COMP%]{height:.75rem;width:.75rem;border-radius:50%}.col-circle[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-size:.55rem!important}[_ngcontent-%COMP%]::-webkit-scrollbar{width:10px!important;height:10px!important;display:none}[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:transparent!important}[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:transparent!important}.calendar-nav[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{border:none;background:transparent;padding:0;font-size:0;cursor:pointer;transform:translate(-50%,1%)}.calendar-nav[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:before{font-size:2rem;padding:0 1.5rem;display:block}.calendar-nav[_ngcontent-%COMP%]   button.prev[_ngcontent-%COMP%]{transform:rotate(-180deg);position:absolute;top:2rem;left:calc(1% - 5px)}@media only screen and (min-width: 992px){.calendar-nav[_ngcontent-%COMP%]   button.prev[_ngcontent-%COMP%]{top:2.5rem;left:calc(36% - 8px)}}.calendar-nav[_ngcontent-%COMP%]   button.next[_ngcontent-%COMP%]{position:absolute;top:2rem;right:calc(1% - 15px)}@media only screen and (min-width: 992px){.calendar-nav[_ngcontent-%COMP%]   button.next[_ngcontent-%COMP%]{top:2.5rem;right:-8px}}.left-col[_ngcontent-%COMP%]{width:100%}@media only screen and (min-width: 992px){.left-col[_ngcontent-%COMP%]{width:32%}}.right-col[_ngcontent-%COMP%]{width:100%}@media only screen and (min-width: 992px){.right-col[_ngcontent-%COMP%]{width:63%}}[_nghost-%COMP%]     .bordered-panel .p-dropdown-items-wrapper{border-radius:8px}\"]\n    });\n  }\n  return BookingSystemComponent;\n})();", "map": {"version": 3, "names": ["of", "map", "startWith", "switchMap", "take", "ClassroomType", "SubSink", "moment", "LessonStatus", "UserRole", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r2", "classroomService", "getClassroomTypeSmallIconPath", "studentSelectedClassroom", "ɵɵsanitizeUrl", "ɵɵelementStart", "ɵɵtemplate", "BookingSystemComponent_ng_container_15_ng_template_2_div_0_img_1_Template", "ɵɵelementEnd", "ɵɵadvance", "authService", "<PERSON><PERSON><PERSON>er", "getStudentNamesWithHTML", "ɵɵsanitizeHtml", "BookingSystemComponent_ng_container_15_ng_template_2_div_0_Template", "classroom_r4", "BookingSystemComponent_ng_container_15_ng_template_3_img_1_Template", "ɵɵtext", "ɵɵtextInterpolate1", "generalService", "convertHoursToMinutesWithSuffix", "getAccumulatedHoursLeft", "packages", "ɵɵelementContainerStart", "ɵɵtwoWayListener", "BookingSystemComponent_ng_container_15_Template_p_dropdown_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "ɵɵresetView", "ɵɵlistener", "onClassroomChange", "BookingSystemComponent_ng_container_15_ng_template_2_Template", "BookingSystemComponent_ng_container_15_ng_template_3_Template", "availableClassrooms", "ɵɵtwoWayProperty", "ɵɵtextInterpolate2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "language", "<PERSON><PERSON><PERSON>", "BookingSystemComponent_ng_container_16_ng_template_2_div_0_Template", "ɵɵpropertyInterpolate1", "teacher_r6", "icon", "hoursLeft", "BookingSystemComponent_ng_container_16_Template_p_dropdown_ngModelChange_1_listener", "_r5", "onTeacher<PERSON><PERSON>e", "BookingSystemComponent_ng_container_16_ng_template_2_Template", "BookingSystemComponent_ng_container_16_ng_template_3_Template", "teachers<PERSON><PERSON><PERSON>", "ɵɵtextInterpolate", "ɵɵpipeBind2", "selected<PERSON>ay", "selectedTimeSlot", "BookingSystemComponent_div_39_span_9_Template", "BookingSystemComponent_div_39_span_12_Template", "BookingSystemComponent_div_39_Template_button_click_26_listener", "_r7", "onLessonRequested", "selectedDuration", "viewValue", "buttonLoading", "isRescheduling", "day_r8", "getDate", "BookingSystemComponent_ng_container_57_a_9_Template_a_click_0_listener", "_r9", "navigateToBuyPackage", "BookingSystemComponent_ng_container_57_a_9_Template", "isStudent", "BookingSystemComponent_li_59_ul_1_ng_container_1_Template_label_click_3_listener", "_r10", "hour_r11", "$implicit", "day_r12", "onTimeSlotSelected", "ɵɵpureFunction2", "_c4", "isTimeSlotAvailableOnDay", "ɵɵpureFunction1", "_c5", "ɵɵclassProp", "isSelectedDayAndTime", "radioChecked", "ɵɵpropertyInterpolate2", "ɵɵpropertyInterpolate", "BookingSystemComponent_li_59_ul_1_ng_container_1_Template", "BookingSystemComponent_li_59_ul_1_Template", "availableHours", "BookingSystemComponent", "constructor", "userService", "calendarService", "layoutService", "lessonService", "toastService", "route", "router", "cdRef", "location", "bookingWrapper", "scrollPanel", "startDate", "Date", "weekDays", "hours", "schedule", "subs", "classrooms", "classroomsWithRecurringLessons", "hasClassrooms", "availableTeachers", "lessonEvents", "<PERSON><PERSON><PERSON><PERSON>", "filteredLessonEvents", "user", "selected<PERSON>our", "duration", "availableDuration", "cdr", "containerHeight", "UserRoles", "calendarAgendaUserId", "extraData", "extraClassroomData", "isPending", "mobileView", "<PERSON><PERSON><PERSON><PERSON>", "height", "noClassroomsFound", "ngOnInit", "queryParams", "snapshot", "lessonString", "classroomString", "hasRescheduleString", "answeredString", "isMobileScreenSize", "lesson", "JSON", "parse", "console", "log", "id", "status", "classroomId", "startingDate", "isRecccuring", "classroom", "type", "value", "calculateWeekDays", "calculateHours", "initSchedule", "getLoggedInUser", "prepareInitialData", "screenWidth", "window", "innerWidth", "isTabletScreenSize", "ngOnChanges", "ngAfterViewInit", "sink", "deviceKind", "pipe", "res", "is576", "is992", "sideMenuHeight", "subscribe", "setDaysRange", "nativeElement", "style", "detectChanges", "scrollToMiddle", "ngAfterContentInit", "ngOnDestroy", "unsubscribe", "days", "getDaysRange", "setTimeout", "scrollContent", "containerViewChild", "getElementsByClassName", "middleScrollPosition", "scrollHeight", "scrollTop", "onDurationSelected", "event", "day", "hour", "toDate", "displayAvailableTimeSlots", "selectedDateTime", "formattedDateTime", "format", "role", "TEACHER", "isDateTimeInThePast", "setShowToastmessage", "severity", "summary", "detail", "resetSelectedTimeSlot", "d", "hasExtraData", "getMonth", "getTeacherAvailability", "teacher", "isNullishObject", "data", "getDatesRange", "userId", "aspUserId", "getAvailabilityFromRange", "requestData", "retrieveCalendarAvailability", "businessHours", "calendarAvailability", "filterLessonsByTeacherId", "lessons", "teacherId", "filter", "find", "item", "handleClassroomTypeAvailableDuration", "add", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lastName", "firstName", "today", "firstDay", "getFullYear", "firstDayCopy", "Array", "from", "length", "_", "i", "setDate", "dates", "dateString", "fromDate", "findMonthFromDateRange", "fromMonth", "toMonth", "fromYear", "toYear", "weekDayAbbreviations", "startHour", "endHour", "minuteIncrements", "hourString", "toString", "padStart", "minute", "timeString", "isAvailable", "push", "quarterHours", "convertHoursToMinutes", "removeValuesGreaterThanHoursLeft", "minutesLeft", "maxDuration", "startMinute", "endMinute", "minHour", "minMinute", "<PERSON><PERSON><PERSON><PERSON>", "hourStr", "minuteStr", "split", "hourNum", "parseInt", "minute<PERSON>um", "j", "toggleSchedule", "toDateString", "goToPrevWeek", "goToNextWeek", "dayString", "dayFormatted", "dayAbbr", "toLowerCase", "a", "date", "availability", "hourIndex", "indexOf", "durationInMinutes", "range", "start", "Math", "max", "end", "to", "outputDate", "getOutputDate", "currentTime", "startOf", "utcDate", "formattedDate", "REQUESTED", "hasExtraClassroomData", "includes", "TRIAL", "ARRANGED_TRIAL", "ARRANGED", "matchingClassroom", "STUDENT", "formatSelectedDateTimeForLesson", "convertMinutesToHours", "lessonUpdate", "lessonId", "answered", "update", "parseZone", "back", "error", "extraTimeString", "create", "getLMSUserClassrooms", "sortClassroomsByFirstName", "addTeacherLabelToArrays", "formatDateWithDay", "momentDate", "onAgendaDayClicked", "getClassroomById", "undefined", "add<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addAvailableClassroom", "addAvailableTeacher", "events", "getAvailableClassroomEvents", "setInitialValuesForUserRole", "initializeStartDateAndTime", "initializeWeekDays", "initializeCalendarAgendaUserId", "getTeacherLabelIcon", "teachers<PERSON>abel", "createTeachersLabel", "handleSelectedClassroomForUserRole", "isTrialClassroom", "trialDuration", "accumulatedHoursLeft", "onCurrentMonthChanged", "monthNumber", "inputDate", "selectedTime", "filterAvailableClassrooms", "flatMap", "createTeachersLabelForEvent", "createLessonEvent", "classroomType", "getTime", "title", "getShortFullName", "slice", "allDay", "backgroundColor", "borderColor", "textColor", "extendedProps", "classRoom", "editable", "showToastMessage", "getTeacherAspUserId", "goBack", "<PERSON><PERSON><PERSON><PERSON>", "outputFormat", "outputDateStr", "utc", "local", "setHours", "Object", "keys", "currentDateTime", "selectedDateTimeMoment", "isBefore", "ɵɵdirectiveInject", "i1", "ClassroomService", "i2", "GeneralService", "i3", "AuthService", "i4", "UserService", "i5", "CalendarService", "i6", "LayoutService", "i7", "LessonService", "i8", "ToastService", "i9", "ActivatedRoute", "Router", "ChangeDetectorRef", "i10", "Location", "_2", "selectors", "viewQuery", "BookingSystemComponent_Query", "rf", "ctx", "BookingSystemComponent_resize_HostBindingHandler", "ɵɵresolveWindow", "BookingSystemComponent_Template_button_click_6_listener", "_r1", "BookingSystemComponent_Template_button_click_8_listener", "BookingSystemComponent_ng_container_15_Template", "BookingSystemComponent_ng_container_16_Template", "BookingSystemComponent_Template_p_dropdown_ngModelChange_21_listener", "BookingSystemComponent_Template_p_dropdown_onChange_21_listener", "BookingSystemComponent_Template_app_calendar_agenda_dayClicked_25_listener", "BookingSystemComponent_Template_app_calendar_agenda_currentMonthChanged_25_listener", "BookingSystemComponent_div_39_Template", "BookingSystemComponent_Template_button_click_42_listener", "BookingSystemComponent_Template_button_click_44_listener", "BookingSystemComponent_li_53_Template", "BookingSystemComponent_ng_container_57_Template", "BookingSystemComponent_li_59_Template", "_c2", "ɵɵstyleMap", "ɵɵpureFunction0", "_c3"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\calendar\\booking-system\\booking-system.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\calendar\\booking-system\\booking-system.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, HostListener, Input, OnInit, ViewChild } from '@angular/core';\r\nimport { Location } from '@angular/common';\r\nimport { Observable, of } from 'rxjs';\r\nimport { map, startWith, switchMap, take } from 'rxjs/operators';\r\nimport { Classroom, ClassroomType } from 'src/app/core/models/classroom.model';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { SubSink } from 'subsink';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { UserService } from 'src/app/core/services/user.service';\r\nimport { CalendarService } from 'src/app/core/services/calendar.service';\r\nimport { LayoutService } from 'src/app/core/services/layout.service';\r\nimport * as moment from 'moment';\r\nimport { Lesson, LessonPostRequest, LessonStatus, LessonUpdateRequest } from 'src/app/core/models/lesson.model';\r\nimport { LessonService } from 'src/app/core/services/lesson.service';\r\nimport { User, UserRole } from 'src/app/core/models/user.model';\r\nimport { ScrollPanel } from 'primeng/scrollpanel';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { RetrieveCalendarAvailabilityRequestData } from 'src/app/core/models/calendar.model';\r\n\r\ninterface HourlySchedule {\r\n  [hour: string]: boolean;\r\n}\r\n\r\ninterface DailySchedule {\r\n  [day: string]: HourlySchedule;\r\n}\r\n\r\ninterface AvailabilitySchedule {\r\n  [key: string]: any;\r\n  mon: HourRange[];\r\n  tue: HourRange[];\r\n  wed: HourRange[];\r\n  thu: HourRange[];\r\n  fri: HourRange[];\r\n  sat: HourRange[];\r\n  sun: HourRange[];\r\n  mondayActive: boolean;\r\n  tuesdayActive: boolean;\r\n  wednesdayActive: boolean;\r\n  thursdayActive: boolean;\r\n  fridayActive: boolean;\r\n  saturdayActive: boolean;\r\n  sundayActive: boolean;\r\n}\r\n\r\ninterface HourRange {\r\n  from: string;\r\n  to: string;\r\n}\r\n\r\n\r\n@Component({\r\n  selector: 'app-booking-system',\r\n  templateUrl: './booking-system.component.html',\r\n  styleUrls: ['./booking-system.component.scss'],\r\n\r\n})\r\nexport class BookingSystemComponent implements OnInit {\r\n  @ViewChild('bookingWrapper', { static: true }) bookingWrapper: ElementRef = {} as ElementRef;\r\n  @ViewChild('scrollPanel', { static: true }) scrollPanel: ScrollPanel = {} as ScrollPanel;\r\n  @Input() studentSelectedClassroom: Classroom = {} as Classroom;\r\n  startDate: Date = new Date();\r\n  weekDays: Date[] = [];\r\n  hours: string[] = [];\r\n  schedule: { [day: string]: { [hour: string]: boolean } } = {};\r\n  availableHours: string[] = [];\r\n  private subs = new SubSink();\r\n  public classrooms: Classroom[] = [];\r\n  public classroomsWithRecurringLessons: Classroom[] = [];\r\n  public hasClassrooms = true;\r\n  availableTeachers: any[] = [];\r\n  lessonEvents: any[] = [];\r\n  selectedTeacher: any = null;\r\n  filteredLessonEvents: any[] = [];\r\n  businessHours: any;\r\n  dayOffEvents: any;\r\n  user: User = {} as User;\r\n  teachersLabels: any[] = [];\r\n  selectedTeachersLabel: any = null;\r\n  selectedHour: any = null;\r\n  duration: any[] = [];\r\n  availableDuration: any[] = [];\r\n  selectedDuration: any = null;\r\n  cdr: ChangeDetectorRef = {} as ChangeDetectorRef;\r\n  containerHeight = '';\r\n  selectedTimeSlot!: string;\r\n  selectedDay!: Date | undefined;\r\n  availableClassrooms: any[] = [];\r\n  UserRoles = UserRole;\r\n  radioChecked = false;\r\n  calendarAgendaUserId = '';\r\n  extraData: any = {};\r\n  extraClassroomData: any = {};\r\n  isRescheduling = false;\r\n  isPending = false;\r\n  mobileView = false;\r\n  daysRange = 7;\r\n  height = 0;\r\n  noClassroomsFound = false;\r\n  buttonLoading = false;\r\n  constructor(\r\n    public classroomService: ClassroomService,\r\n    public generalService: GeneralService,\r\n    private authService: AuthService,\r\n    private userService: UserService,\r\n    private calendarService: CalendarService,\r\n    private layoutService: LayoutService,\r\n    private lessonService: LessonService,\r\n    public toastService: ToastService,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private cdRef: ChangeDetectorRef,\r\n    private location: Location,\r\n  ) {\r\n    this.startDate = new Date();\r\n  }\r\n  ngOnInit() {\r\n    const queryParams = this.route.snapshot.queryParams;\r\n    const lessonString = queryParams['lesson'];\r\n    const classroomString = queryParams['classroom'];\r\n    const hasRescheduleString = queryParams['reschedule'];\r\n    const answeredString = queryParams['answered'];\r\n    this.mobileView = this.isMobileScreenSize();\r\n\r\n    if (hasRescheduleString) {\r\n      this.isRescheduling = true;\r\n    }\r\n    if (answeredString) {\r\n      this.isPending = true;\r\n    }\r\n    if (lessonString) {\r\n      const lesson = JSON.parse(lessonString);\r\n      console.log(lesson);\r\n      this.extraData = {\r\n        \"lessonId\": lesson.id,\r\n        \"lessonStatus\": lesson.status,\r\n        \"classroomId\": lesson.classroomId,\r\n        \"status\": lesson.status,\r\n        \"startingDate\": lesson.startingDate,\r\n        \"duration\": lesson.duration,\r\n        \"isRecccuring\": lesson.isRecccuring,\r\n      }\r\n    }\r\n  \r\n    if (classroomString) {\r\n      const classroom = JSON.parse(classroomString);\r\n      console.log(classroom);\r\n      this.extraClassroomData = {\r\n        \"classroomId\": classroom.id,\r\n        \"status\": classroom.status,\r\n        \"classroomType\": classroom.type,\r\n      }\r\n    }\r\n\r\n    this.duration = [\r\n      { value: 15, viewValue: '15m'},\r\n      { value: 30, viewValue: '30m' },\r\n      { value: 45, viewValue: '45m' },\r\n      { value: 60, viewValue: '1h' },\r\n      { value: 75, viewValue: '1h 15m' },\r\n      { value: 90, viewValue: '1h 30m' },\r\n      { value: 105, viewValue: '1h 45m' },\r\n      { value: 120, viewValue: '2h' },\r\n      { value: 135, viewValue: '2h 15m' },\r\n      { value: 150, viewValue: '2h 30m' },\r\n      { value: 165, viewValue: '2h 45m' },\r\n      { value: 180, viewValue: '3h' }\r\n    ]\r\n    this.availableDuration = [...this.duration];\r\n    this.selectedDuration = this.duration[3];\r\n    this.calculateWeekDays(new Date());\r\n    this.calculateHours();\r\n    this.initSchedule();\r\n    \r\n    this.user = this.authService.getLoggedInUser();\r\n\r\n    this.prepareInitialData();\r\n  }\r\n  @HostListener('window:resize')\r\n  isMobileScreenSize() {\r\n    const screenWidth = window.innerWidth;\r\n    return screenWidth < 768;\r\n  }\r\n\r\n  @HostListener('window:resize')\r\n  isTabletScreenSize() {\r\n    const screenWidth = window.innerWidth;\r\n    return screenWidth < 1024; // Change the width here to the desired tablet width\r\n  }\r\n\r\n  ngOnChanges() {\r\n    \r\n    this.mobileView = this.isMobileScreenSize();\r\n    console.log(this.mobileView);\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n        \r\n    this.subs.sink = this.generalService.deviceKind.pipe(\r\n      take(2),\r\n      switchMap(res => {\r\n        console.log(res);\r\n        if (res.is576 || res.is992) {\r\n          return of(0);\r\n        } else {\r\n          return this.layoutService.sideMenuHeight;\r\n        }\r\n      }),\r\n      startWith(0),\r\n    ).subscribe(height => {\r\n      if (height !== 0) {\r\n        this.setDaysRange(7);\r\n      this.height = height;\r\n        this.bookingWrapper.nativeElement.style.height = (height + 30 + 'px');\r\n      } else {\r\n        this.setDaysRange(3);\r\n        this.bookingWrapper.nativeElement.style.height = ('100%');\r\n      }\r\n      this.cdRef.detectChanges(); // Manually trigger change detection\r\n    });\r\n    // this.subs.sink = this.layoutService.sideMenuHeight.subscribe((res) => {\r\n    //   this.bookingWrapper.nativeElement.style.height = (res + 'px');\r\n    // });\r\n    this.scrollToMiddle();\r\n  }\r\n\r\n  ngAfterContentInit() {\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  setDaysRange(days: number) {\r\n    this.daysRange = days;\r\n  }\r\n\r\n  getDaysRange() {\r\n    return this.daysRange;\r\n  }\r\n\r\n  scrollToMiddle() {\r\n    setTimeout(() => {\r\n      const scrollContent = this.scrollPanel.containerViewChild!.nativeElement.getElementsByClassName('p-scrollpanel-content')[0];\r\n      const middleScrollPosition = scrollContent.scrollHeight / 3;\r\n      scrollContent.scrollTop = middleScrollPosition;\r\n    }, 1000);\r\n  }\r\n\r\n  onDurationSelected(event: any) {\r\n    this.selectedTimeSlot = '';\r\n    this.radioChecked = false;\r\n    console.log(this.selectedDay);\r\n    // Add your own logic here to handle the selected duration\r\n  }\r\n\r\n  onTimeSlotSelected(day: Date, hour: string) {\r\n\r\n    this.selectedDay = moment(day, \"ddd MMM DD YYYY\").toDate();\r\n    this.selectedTimeSlot = hour;\r\n    this.displayAvailableTimeSlots();\r\n    const selectedDateTime = moment(day, \"ddd MMM DD YYYY HH:mm:ss [GMT]ZZ\");\r\n    const formattedDateTime = selectedDateTime.format(\"ddd MMM DD YYYY \" + hour);\r\n\r\n    // TODO : implement uncheck all radios functionality\r\n    // if (this.radioChecked && this.timeSlotRadio.checked) {\r\n    //   if (this.timeSlotRadio) {\r\n    //     this.timeSlotRadio.checked = false;\r\n    //   }\r\n    //   this.resetSelectedTimeSlot();\r\n    //   return;\r\n    // }\r\n\r\n    if (this.user.role === UserRole.TEACHER) {\r\n      this.radioChecked = true;\r\n    } else {\r\n      if (!this.isDateTimeInThePast(new Date(formattedDateTime))) {\r\n        this.radioChecked = true;\r\n      } else {\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'info',\r\n          summary: '',\r\n          detail: 'Date is in the past. Please choose another date.'\r\n        });\r\n        this.radioChecked = false;\r\n      }\r\n    }\r\n  }\r\n\r\n  resetSelectedTimeSlot() {\r\n    this.selectedDay = new Date();\r\n    this.selectedTimeSlot = '';\r\n    this.displayAvailableTimeSlots();\r\n    this.radioChecked = false;\r\n  }\r\n\r\n  isSelectedDayAndTime(day: Date, hour: string): boolean {\r\n    const d = new Date(day);\r\n    if (this.hasExtraData()) {\r\n      return (d.getDate() + '' + d.getMonth()) === (this.selectedDay!.getDate() + '' + this.selectedDay!.getMonth()) && hour === this.selectedTimeSlot;\r\n    } else {\r\n      return day === this.selectedDay && hour === this.selectedTimeSlot;\r\n    }\r\n  }\r\n\r\n/**\r\n * The function `getTeacherAvailability` retrieves the calendar availability of a teacher based on\r\n * their ASP user ID.\r\n * @param {any} teacher - The `teacher` parameter is an object that represents a teacher. It contains\r\n * information about the teacher, such as their ID (`aspUserId`).\r\n * @returns an Observable of type RetrieveCalendarAvailabilityRequestData or null.\r\n */\r\n  private getTeacherAvailability(teacher: any): Observable<RetrieveCalendarAvailabilityRequestData | null> {\r\n    if (!teacher || this.generalService.isNullishObject(teacher)) {\r\n      return of(null);\r\n    }\r\n    const data = {\r\n      ...this.getDatesRange(),\r\n      userId: teacher.aspUserId\r\n    }\r\n    return this.getAvailabilityFromRange(data);\r\n\r\n  }\r\n\r\n  private getAvailabilityFromRange(requestData: RetrieveCalendarAvailabilityRequestData) {\r\n    console.log(requestData);\r\n    return this.calendarService.retrieveCalendarAvailability(requestData).pipe(\r\n      map((res: any) => {\r\n        // const daysOff = res.daysOff;\r\n\r\n        // // Set business hours and select constraint\r\n        this.businessHours = (res.calendarAvailability);\r\n\r\n        this.availableHours = this.displayAvailableTimeSlots();\r\n        \r\n        // // Create events for each day off\r\n        // this.dayOffEvents = this.calendarService.createDayOffEvents(daysOff);\r\n\r\n        return null;\r\n      })\r\n    );\r\n  }\r\n\r\n  filterLessonsByTeacherId(lessons: Lesson[], teacherId: number): Lesson[] {\r\n    return lessons.filter((lesson: any) => lesson.teacher.id === teacherId);\r\n  }\r\n\r\n  onTeacherChange(event: { teacherId: number }) {\r\n// Reset selected time slot and radio checked status\r\n    this.selectedTimeSlot = '';\r\n    this.radioChecked = false;\r\n\r\n  // Find the selected teacher based on the teacherId\r\n    this.selectedTeacher = this.availableTeachers.find((item) => item.id === event.teacherId);\r\n\r\n  // Find the classroom based on the selected teacher's classroom label\r\n    const classroomId = this.availableClassrooms.find((item) => item.id === this.selectedTeachersLabel.classroom);\r\n    console.log(classroomId);\r\n    \r\n// Handle the available duration for the classroom type\r\n    this.handleClassroomTypeAvailableDuration(classroomId);\r\n    \r\n  // Get the teacher's availability and update the filtered lesson events\r\n    this.subs.add(this.getTeacherAvailability(this.selectedTeacher).subscribe((res) => {\r\n      this.filteredLessonEvents = this.lessonEvents;\r\n      console.log('filteredLessonEvents', this.filteredLessonEvents);\r\n    }));\r\n\r\n  // Set the calendar agenda user ID to the selected teacher's ASP user ID\r\n    this.calendarAgendaUserId = this.selectedTeacher.aspUserId;\r\n  }\r\n\r\n  /**\r\n   * Handles the change event when a classroom is selected.\r\n   * It sets the selected classroom and updates the calendar agenda user ID if a teacher is associated with the classroom.\r\n   * It also handles the available duration based on the classroom type.\r\n   * \r\n   * @param event - The selected classroom object.\r\n   */\r\n  onClassroomChange(event: Classroom) {\r\n    console.log(event);\r\n    this.studentSelectedClassroom = event;\r\n    console.log(event);\r\n    if (this.studentSelectedClassroom.teacher) {\r\n      this.calendarAgendaUserId = this.studentSelectedClassroom.teacher?.aspUserId!;\r\n    }\r\n    this.handleClassroomTypeAvailableDuration(event);\r\n  }\r\n\r\n  getTeacherLabel(teacher: User): string {\r\n    return ` ${teacher.lastName} ${teacher.firstName}`;\r\n  }\r\n  /**\r\n   * Updates the selected classroom when a user chooses a classroom from the UI.\r\n   * @param event - The change event triggered by selecting a classroom.\r\n   */\r\n\r\n  /**\r\n   * Calculates the dates of the current week, starting from the given start date.\r\n   * @param startDate - The start date for the current week.\r\n   */\r\n  calculateWeekDays(startDate: Date) {\r\n    const today = moment(startDate).toDate();\r\n    const firstDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());\r\n    const firstDayCopy = new Date(firstDay);\r\n    this.weekDays = Array.from({ length: this.daysRange }, (_, i) => {\r\n      const d = new Date(firstDayCopy);\r\n      d.setDate(d.getDate() + i);\r\n      return d;\r\n    });\r\n\r\n  }\r\n\r\n  /**\r\n   * Returns the date range for the current week.\r\n   * @returns {Object} An object with \"from\" and \"to\" properties representing the start and end dates of the week.\r\n   */\r\n  getDatesRange() {\r\n    // Convert date strings to Date objects\r\n    const dates = this.weekDays.map(dateString => new Date(dateString));\r\n\r\n    // Get the range of dates from and to\r\n    const fromDate = dates[0];\r\n    const toDate = dates[dates.length - 1];\r\n\r\n    // console.log(fromDate);\r\n    // Log the range of dates\r\n    return {\r\n      fromDate: moment((fromDate), 'ddd MMM D YYYY HH:mm:ss [GMT]ZZ').format('YYYY-MM-DD') + 'T09:29:27.434Z',\r\n      toDate: moment((toDate), 'ddd MMM D YYYY HH:mm:ss [GMT]ZZ').format('YYYY-MM-DD') + 'T09:29:27.434Z'\r\n    }\r\n    \r\n  }\r\n\r\n  findMonthFromDateRange() {\r\n    const data = this.getDatesRange();\r\n  \r\n    const fromDate = moment(data.fromDate);\r\n    const toDate = moment(data.toDate);\r\n  \r\n    const fromMonth = fromDate.format('MMMM');\r\n    const toMonth = toDate.format('MMMM');\r\n    const fromYear = fromDate.format('YYYY');\r\n    const toYear = toDate.format('YYYY');\r\n  \r\n    if (fromMonth === toMonth) {\r\n      return `${fromMonth} ${fromYear}`;\r\n    } else {\r\n      return `${fromMonth} - ${toMonth} ${toYear}`;\r\n    }\r\n  }\r\n\r\n  displayAvailableTimeSlots() {\r\n    const weekDayAbbreviations = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];\r\n    const availableHours: string[] = [];\r\n    const startHour = 0;\r\n    const endHour = 23;\r\n    const minuteIncrements = ['00', '15', '30', '45'];\r\n\r\n    for (let hour = startHour; hour <= endHour; hour++) {\r\n      const hourString = hour.toString().padStart(2, '0');\r\n      for (const minute of minuteIncrements) {\r\n        const timeString = `${hourString}:${minute}`;\r\n        let isAvailable = true;\r\n\r\n        // Check availability for each day\r\n        // for (const day of this.weekDays) {\r\n        //   const dayAbbr = weekDayAbbreviations[day.getDay()];\r\n        //   if (this.isTimeSlotAvailableOnDay(day.toDateString(), timeString)) {\r\n        //     isAvailable = true;\r\n        //   }\r\n        // }\r\n\r\n        if (isAvailable) {\r\n          availableHours.push(timeString);\r\n        }\r\n      }\r\n    }\r\n\r\n    return availableHours;\r\n  }\r\n\r\n/**\r\n * Calculates the available hours for scheduling.\r\n */\r\ncalculateHours() {\r\n  const startHour = 0;\r\n  const endHour = 23;\r\n  const quarterHours = ['00', '15', '30', '45'];\r\n  this.hours = [];\r\n\r\n  for (let hour = startHour; hour <= endHour; hour++) {\r\n    const hourString = hour.toString().padStart(2, '0');\r\n\r\n    for (const minute of quarterHours) {\r\n      const timeString = `${hourString}:${minute}`;\r\n      this.hours.push(timeString);\r\n    }\r\n  }\r\n\r\n  for (let hour = 0; hour < startHour; hour++) {\r\n    const hourString = hour.toString().padStart(2, '0');\r\n\r\n    for (const minute of quarterHours) {\r\n      const timeString = `${hourString}:${minute}`;\r\n      this.hours.push(timeString);\r\n    }\r\n  }\r\n}\r\n\r\nconvertHoursToMinutes(hours: number): number {\r\n  return hours * 60;\r\n}\r\nremoveValuesGreaterThanHoursLeft(hours: number) {\r\n  const hoursLeft = hours; // Hours left in Classroom for the selected teacher\r\n  const minutesLeft = this.convertHoursToMinutes(hoursLeft); \r\n  const maxDuration = this.convertHoursToMinutes(3); // Maximum duration of 3 hours\r\n\r\n  console.log(hoursLeft);\r\n  this.availableDuration = this.duration.filter((item) => item.value <= minutesLeft && item.value <= maxDuration);\r\n\r\n}\r\n/**\r\n * The function initializes a schedule by setting time slots from the minimum hour and minute to the\r\n * end hour and minute in 15-minute intervals.\r\n */\r\ninitSchedule() \r\n{\r\n  const startMinute = 0;\r\n  const endMinute = 45;\r\n  let minHour = 23;\r\n  let minMinute = 59;\r\n  for (const day in this.schedule) {\r\n    const dayObj = this.schedule[day];\r\n    for (const hour in dayObj) {\r\n      if (dayObj[hour]) {\r\n        const [hourStr, minuteStr] = hour.split(':');\r\n        const hourNum = parseInt(hourStr);\r\n        const minuteNum = parseInt(minuteStr);\r\n        if (hourNum < minHour || (hourNum === minHour && minuteNum < minMinute)) {\r\n          minHour = hourNum;\r\n          minMinute = minuteNum;\r\n        }\r\n      }\r\n    }\r\n  }\r\n  const startHour = minHour;\r\n  const endHour = 23;\r\n  for (let i = startHour; i <= endHour; i++) {\r\n    const hour = i.toString().padStart(2, '0');\r\n    for (let j = 0; j < 4; j++) {\r\n      let minute = j * 15 + startMinute;\r\n      if (minute >= 60) {\r\n        minute = 45;\r\n      }\r\n      if ((i === endHour && minute > endMinute) || (i === startHour && minute < minMinute)) {\r\n        continue;\r\n      }\r\n      const timeString = `${hour}:${minute.toString().padStart(2, '0')}`;\r\n      for (const day in this.schedule) {\r\n        this.schedule[day][timeString] = true;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n  /**\r\n   * Toggles the availability of a time slot on a specific day.\r\n   * @param day - The date object representing the day to toggle.\r\n   * @param hour - The hour string representing the time slot to toggle.\r\n   */\r\n  toggleSchedule(day: Date, hour: string) {\r\n    const dateString = day.toDateString();\r\n    const isAvailable = !this.schedule[dateString]?.[hour];\r\n    this.schedule[dateString] = this.schedule[dateString] || {};\r\n    this.schedule[dateString][hour] = isAvailable;\r\n  }\r\n\r\n  /**\r\n   * Moves the calendar view back one week and recalculates the dates of the new week.\r\n   */\r\n  goToPrevWeek() {\r\n    this.startDate.setDate(this.startDate.getDate() - this.daysRange);\r\n    this.startDate = new Date(this.startDate);\r\n    console.log('prev', this.startDate);\r\n    this.calculateWeekDays(this.startDate);\r\n    this.subs.sink = this.getTeacherAvailability(this.selectedTeacher).subscribe((res)=> {\r\n\r\n      console.log(res);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Moves the calendar view forward one week and recalculates the dates of the new week.\r\n   */\r\n  goToNextWeek() {\r\n    this.startDate.setDate(this.startDate.getDate() + this.daysRange);\r\n    this.startDate = new Date(this.startDate);\r\n    this.calculateWeekDays(this.startDate);\r\n    this.subs.sink = this.getTeacherAvailability(this.selectedTeacher).subscribe((res)=> {\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Gets the availability status of a specific time slot on a specific day.\r\n   * @param dayString - The string representation of the date to check.\r\n   * @param hour - The hour string representing the time slot to check.\r\n   * @returns A boolean indicating whether the time slot is available or not.\r\n   */\r\n  isTimeSlotAvailableOnDay(dayString: string, hour: string): boolean {\r\n    const day = moment(dayString, \"ddd MMM DD YYYY HH:mm:ss ZZ\");\r\n    const dayFormatted = day.format(\"DD/MM/YYYY\");\r\n    const dayAbbr = day.format(\"ddd\").toLowerCase();\r\n  \r\n    if (this.businessHours) {\r\n\r\n\r\n    const dayObj = this.businessHours.find((a: any) => a.date === dayFormatted);\r\n\r\n    // This code checks if the role of the user is equal to the 'TEACHER' role.\r\n    if (this.user.role === UserRole.TEACHER) {\r\n      return true;\r\n    }\r\n    if (dayObj && dayObj.availability.length > 0) {\r\n      const availableHours: Record<string, any> = dayObj.availability;\r\n      let hourIndex = this.hours.indexOf(hour);\r\n      const durationInMinutes = this.selectedDuration.value;\r\n      for (let i = 0; i < availableHours.length; i++) {\r\n        const range = availableHours[i];\r\n        let start = Math.max(this.hours.indexOf(range.from), hourIndex);\r\n        const end = range.to === \"00:00\" ? this.hours.length - 1 : this.hours.indexOf(range.to);\r\n  \r\n        if (start >= 0 && end >= 0 && start <= hourIndex && hourIndex <= end) {\r\n          const availableDuration = (end - start + (range.to === \"00:00\" ? 2 : 1)) * 14;\r\n          if (availableDuration >= durationInMinutes) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n    return false;\r\n  }\r\n\r\n /**\r\n * Creates a new lesson based on user input and sends it to backend.\r\n *\r\n * @returns void\r\n */\r\n  onLessonRequested(): void {\r\n// Check if a timeslot is selected\r\n    if (this.selectedTimeSlot === '') {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'info',\r\n        summary: '',\r\n        detail: 'Please select a timeslot'\r\n      });\r\n      return;\r\n    }\r\n\r\n  // Get the output date\r\n    const outputDate = this.getOutputDate();\r\n\r\n  // Get the current time\r\n    const currentTime = moment().startOf('hour');\r\n\r\n// Get the UTC date\r\n    const utcDate = moment(this.selectedDay);\r\n    \r\n  // Format the date\r\n    const formattedDate = utcDate.format('ddd MMM DD YYYY HH:mm:ss ZZ');\r\n\r\n    if (!this.isTimeSlotAvailableOnDay(formattedDate, this.selectedTimeSlot)) {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'info',\r\n        summary: '',\r\n        detail: 'Please select a correct timeslot'\r\n      });\r\n      return;\r\n        }\r\n    \r\n  // Prevent button spam\r\n    if (this.buttonLoading) {\r\n      return;\r\n    }\r\n\r\n  // Set the button loading state to true\r\n    this.buttonLoading = true;\r\n\r\n  // Set the default lesson status\r\n    let status = LessonStatus.REQUESTED;\r\n\r\n// Check if the user is a teacher and has extra classroom data\r\n    if (this.user.role === UserRole.TEACHER) {\r\n      if (this.hasExtraClassroomData()) {\r\n        if (this.extraClassroomData.status.toLowerCase().includes(ClassroomType.TRIAL.toLowerCase())) {\r\n          status = LessonStatus.ARRANGED_TRIAL;\r\n        } else {\r\n          status = LessonStatus.ARRANGED;\r\n        }\r\n      } else {\r\n        const matchingClassroom = this.availableClassrooms.find((classroom: any) => {\r\n          return classroom.id === this.studentSelectedClassroom?.id;\r\n        });\r\n        if (matchingClassroom.type.toLowerCase().includes(ClassroomType.TRIAL.toLowerCase())) {\r\n          status = LessonStatus.ARRANGED_TRIAL;\r\n        } else {\r\n          status = LessonStatus.ARRANGED;\r\n        }\r\n      }\r\n    }\r\n\r\n    let classroomId;\r\n\r\n  // Check if the user is a student\r\n    if (this.user.role === UserRole.STUDENT) {\r\n      const matchingClassroom = this.availableClassrooms.find((classroom: Classroom) => {\r\n        return classroom.teacher!.id === this.selectedTeachersLabel.teacherId && classroom.language === this.selectedTeachersLabel.language;\r\n      });\r\n      [classroomId] = matchingClassroom ? [matchingClassroom.id] : [null];\r\n    } else {\r\n      [classroomId] = [this.studentSelectedClassroom?.id];\r\n    }\r\n\r\n  // Create the lesson object\r\n    const lesson: LessonPostRequest = {\r\n      classroomId,\r\n      status,\r\n      startingDate: this.generalService.formatSelectedDateTimeForLesson(this.selectedDay!, this.selectedTimeSlot),\r\n      duration: this.generalService.convertMinutesToHours(this.selectedDuration.value),\r\n      isRecccuring: false,\r\n    };\r\n\r\n  // Check if it is a rescheduling\r\n    if (this.isRescheduling) {\r\n      let lessonUpdate: LessonUpdateRequest = {\r\n        id: this.extraData.lessonId,\r\n        classroomId,\r\n        status: this.isPending ? LessonStatus.ARRANGED : this.extraData.status,\r\n        startingDate: this.generalService.formatSelectedDateTimeForLesson(this.selectedDay!, this.selectedTimeSlot),\r\n        duration: this.generalService.convertMinutesToHours(this.selectedDuration.value),\r\n        isRecccuring: false,\r\n        answered: this.isPending ? \"requested\" : \"accepted\",\r\n      };\r\n      console.log(lessonUpdate);\r\n    this.subs.sink = this.lessonService.update(lessonUpdate).subscribe(res => {\r\n      console.log(res);\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'success',\r\n        summary: '',\r\n        detail: 'Lesson Rescheduled for ' + moment.parseZone(this.generalService.formatSelectedDateTimeForLesson(this.selectedDay!, this.selectedTimeSlot)).format('DD-MM-YYYY HH:mm')\r\n      });\r\n      \r\n      this.location.back();\r\n      // this.router.navigate(['/dashboard/calendar']);\r\n    }, error => {\r\n      this.buttonLoading = false;\r\n    });\r\n    } else {\r\n      const extraTimeString = moment.parseZone(this.generalService.formatSelectedDateTimeForLesson(this.selectedDay!, this.selectedTimeSlot)).format('DD-MM-YYYY HH:mm');\r\n      this.subs.sink = this.lessonService.create(lesson).subscribe(res => {\r\n        console.log(res);\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'success',\r\n          summary: '',\r\n          detail: status === LessonStatus.ARRANGED ? 'Lesson Created for ' + extraTimeString : 'Lesson Requested for ' + extraTimeString\r\n        });\r\n        this.location.back();\r\n      }, error => {\r\n        this.buttonLoading = false;\r\n      });\r\n    }\r\n  }\r\n\r\n  private prepareInitialData() {\r\n    this.subs.sink = this.classroomService.getLMSUserClassrooms(this.user.id).pipe(\r\n      switchMap((classrooms: any) => {\r\n\r\n        // sort classrooms first\r\n        classrooms = this.classroomService.sortClassroomsByFirstName(classrooms);\r\n        return this.addTeacherLabelToArrays(classrooms);\r\n\r\n\r\n      //TODO: remove when database is updated - START\r\n      // const modifiedArray = Object.values(this.teachersLabels.reduce((acc, { teacherId, hoursLeft, ...rest }) => {\r\n      //   if (acc[teacherId]) {\r\n      //     acc[teacherId].hoursLeft += hoursLeft;\r\n      //   } else {\r\n      //     acc[teacherId] = { teacherId, hoursLeft, ...rest };\r\n      //   }\r\n      //   return acc;\r\n      // }, {}));\r\n      // this.teachersLabels = modifiedArray;\r\n      //TODO: remove when database is updated - END\r\n\r\n      // const uniqueTeachers = [...new Set(this.availableTeachers.map(t => t.id))].map(id => this.availableTeachers.find(t => t.id === id));\r\n      // this.availableTeachers = uniqueTeachers;\r\n      if (this.user.role === UserRole.TEACHER) {\r\n        this.studentSelectedClassroom = this.availableClassrooms[0];\r\n        this.calendarAgendaUserId = this.studentSelectedClassroom.teacher?.aspUserId!;\r\n      }\r\n      else {\r\n        this.selectedTeacher = this.availableTeachers[0];\r\n        this.selectedTeachersLabel = this.teachersLabels[0];\r\n        const classroomId = this.availableClassrooms .find((item) => item.id === this.selectedTeachersLabel.classroom);\r\n        this.removeValuesGreaterThanHoursLeft(classroomId.hoursLeft);\r\n        this.calendarAgendaUserId = this.selectedTeacher.aspUserId;\r\n      }\r\n      return this.getTeacherAvailability(this.availableTeachers[0]);\r\n      })\r\n    ).subscribe((res: any) => {\r\n      console.log(res);\r\n      // this.filteredLessonEvents = this.lessonEvents;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Formats a date object to a string in the format 'dddd DD/MM'.\r\n   * @param {Date} date - The date object to format.\r\n   * @returns {string} The formatted date string.\r\n   */\r\n  formatDateWithDay(date: Date): string {\r\n    const dateString = date.toDateString();\r\n    const momentDate = moment(dateString);\r\n    return momentDate.format('dddd DD/MM');\r\n  }\r\n\r\n  onAgendaDayClicked(event: any) {\r\n    console.log(event);\r\n    this.calculateWeekDays(event);\r\n    this.subs.add(this.getTeacherAvailability(this.selectedTeacher).subscribe((res) => {\r\n      this.filteredLessonEvents = this.lessonEvents;\r\n      console.log('filteredLessonEvents', this.filteredLessonEvents);\r\n    }));\r\n  }\r\n\r\n/**\r\n * The function adds teacher labels to arrays of classrooms and performs various initialization tasks.\r\n * @param {Classroom[]} classrooms - An array of Classroom objects.\r\n * @returns the result of the `getTeacherAvailability` function with the first element of the\r\n * `availableTeachers` array as the argument.\r\n */\r\n  private addTeacherLabelToArrays(classrooms: Classroom[]): any | undefined {\r\n    if (this.hasExtraData()) {\r\n      const classroom = this.getClassroomById(classrooms);\r\n      if (!classroom) {\r\n        console.log(`No teacher label found for classroom`);\r\n        return undefined;\r\n      }\r\n  \r\n      this.addTeacherLabel(classroom);\r\n      this.addAvailableClassroom(classroom);\r\n      console.log(classroom);\r\n      this.addAvailableTeacher(classroom.teacher!);\r\n  \r\n    } else {\r\n      const events = this.getAvailableClassroomEvents(classrooms);\r\n      console.log(`No teacher label found for classroom`, events);\r\n      this.lessonEvents.push(...events);\r\n    }\r\n    this.setInitialValuesForUserRole();\r\n    this.initializeStartDateAndTime();\r\n    this.initializeWeekDays();\r\n  \r\n    \r\n    if (this.authService.isTeacher) {\r\n      if (!this.studentSelectedClassroom) {\r\n        this.noClassroomsFound = true;\r\n        return of(null);\r\n      }\r\n    }\r\n    this.initializeCalendarAgendaUserId();\r\n  \r\n    return this.getTeacherAvailability(this.availableTeachers[0]);\r\n  }\r\n  \r\n/**\r\n * The function `getClassroomById` takes an array of `Classroom` objects and returns the `Classroom`\r\n * object with a matching `id` property, or `undefined` if no match is found.\r\n * @param {Classroom[]} classrooms - An array of Classroom objects.\r\n * @returns a Classroom object or undefined.\r\n */\r\n  private getClassroomById(classrooms: Classroom[]): Classroom | undefined {\r\n    const classroomId = this.extraData.classroomId;\r\n    return classrooms.find((classroom: Classroom) => classroom.id === classroomId);\r\n  }\r\n  \r\n/**\r\n * The addTeacherLabel function adds a teacher label to the teachersLabels array for a given classroom.\r\n * @param {Classroom} classroom - The classroom parameter is an object that represents a classroom.\r\n */\r\n  private addTeacherLabel(classroom: Classroom): void {\r\n    const icon = this.getTeacherLabelIcon(classroom);\r\n    const teachersLabel = this.createTeachersLabel(classroom, icon);\r\n    this.teachersLabels.push(teachersLabel);\r\n  }\r\n  \r\n/**\r\n * The function adds a classroom to the list of available classrooms.\r\n * @param {Classroom} classroom - The parameter \"classroom\" is of type \"Classroom\".\r\n */\r\n  private addAvailableClassroom(classroom: Classroom): void {\r\n    this.availableClassrooms.push(classroom);\r\n  }\r\n  \r\n/**\r\n * The function adds a teacher to the list of available teachers.\r\n * @param {User} teacher - The \"teacher\" parameter is of type \"User\".\r\n */\r\n  private addAvailableTeacher(teacher: User): void {\r\n    this.availableTeachers.push(teacher);\r\n  }\r\n  \r\n/**\r\n * The function sets initial values for user role based on certain conditions and handles classroom\r\n * selection.\r\n */\r\n  private setInitialValuesForUserRole(): void {\r\n    if (this.isRescheduling) {\r\n      this.selectedDuration = this.duration.find(duration => duration.value === this.extraData.duration * 60);\r\n    } else {\r\n      this.selectedDuration = this.duration[3];\r\n    }\r\n    if (this.hasExtraClassroomData()) {\r\n      this.studentSelectedClassroom = this.availableClassrooms.find((item) => item.id === this.extraClassroomData.classroomId);\r\n\r\n\r\n      if (this.studentSelectedClassroom) {\r\n        this.removeValuesGreaterThanHoursLeft(this.classroomService.getAccumulatedHoursLeft(this.studentSelectedClassroom.packages!));\r\n      }\r\n      if (!this.studentSelectedClassroom) {\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'info',\r\n          summary: '',\r\n          detail: 'Not enough hours for previously selected classroom. Please select another.'\r\n        });\r\n        \r\n        this.handleSelectedClassroomForUserRole();\r\n      } else {\r\n        this.selectedTeacher = this.studentSelectedClassroom.teacher;\r\n        this.selectedTeachersLabel = this.teachersLabels.find((item) => item.classroom === this.studentSelectedClassroom.id);\r\n      }\r\n    } else {\r\n      if (this.user.role === UserRole.TEACHER) {\r\n        this.studentSelectedClassroom = this.availableClassrooms[0];\r\n\r\n        this.selectedTeacher = this.user;\r\n      } else {\r\n        this.handleSelectedClassroomForUserRole();\r\n      }\r\n    }\r\n  }\r\n\r\n/**\r\n * The function handles the selection of a classroom based on the user's role and performs additional\r\n * operations based on the selected classroom.\r\n */\r\n  handleSelectedClassroomForUserRole() {\r\n    if (this.user.role === UserRole.TEACHER) {\r\n      this.studentSelectedClassroom = this.availableClassrooms[0];\r\n      this.selectedTeacher = this.user;\r\n    } else {\r\n      console.log(this.availableTeachers);\r\n      this.selectedTeacher = this.availableTeachers[0];\r\n      this.selectedTeachersLabel = this.teachersLabels[0];\r\n      const classroom = this.availableClassrooms.find((item) => item.id === this.selectedTeachersLabel.classroom);\r\n\r\n      if (classroom) {\r\n        if(!this.isRescheduling) {\r\n          console.log(classroom);\r\n          this.handleClassroomTypeAvailableDuration(classroom);\r\n          this.availableDuration = this.duration.filter((duration) => {\r\n            return this.generalService.convertMinutesToHours(duration.value) <= this.classroomService.getAccumulatedHoursLeft(classroom.packages!);\r\n          });\r\n          // this.removeValuesGreaterThanHoursLeft(classroom.packages[0].hoursLeft);\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n/**\r\n * The function `handleClassroomTypeAvailableDuration` sets the available duration options for a\r\n * classroom based on its type and removes any options that exceed the remaining hours.\r\n * @param {Classroom} classroom - The \"classroom\" parameter is an object of type Classroom.\r\n */\r\n  handleClassroomTypeAvailableDuration(classroom: Classroom) {\r\n    if (this.classroomService.isTrialClassroom(classroom)) {\r\n      const trialDuration = this.duration[3];\r\n      this.availableDuration = [trialDuration];\r\n      this.selectedDuration = trialDuration;\r\n    } else {\r\n      this.availableDuration = [...this.duration];\r\n      this.selectedDuration = this.duration[3];\r\n      const accumulatedHoursLeft = this.classroomService.getAccumulatedHoursLeft(classroom.packages!);\r\n      this.removeValuesGreaterThanHoursLeft(accumulatedHoursLeft);\r\n    }\r\n  }\r\n\r\n  onCurrentMonthChanged(monthNumber: any) {\r\n    const outputDate = this.getOutputDate();\r\n    console.log(outputDate);\r\n    // this.selectedDay = undefined;\r\n    // const currentTime = moment().startOf('hour');\r\n    // const time = currentTime.format('HH:00');\r\n\r\n    // const utcDate = moment(outputDate);\r\n    // // Fri Jul 14 2023 00:00:00 GMT+0300 (Eastern European Summer Time)\r\n    // const formattedDate = utcDate.format('ddd MMM DD YYYY HH:mm:ss [GMT]ZZ (z)');\r\n\r\n    // if (this.isTimeSlotAvailableOnDay(formattedDate, this.selectedTimeSlot)) {\r\n    //   this.onTimeSlotSelected(outputDate, time);\r\n    // } else {\r\n    //   this.selectedTimeSlot = '';\r\n    //   this.radioChecked = false;\r\n    // }\r\n  }\r\n  \r\n  private initializeStartDateAndTime(): void {\r\n    const outputDate = this.getOutputDate();\r\n    this.startDate = new Date(outputDate);\r\n    const inputDate = this.extraData.startingDate;\r\n\r\n    if (!this.isRescheduling) {\r\n      // (TODO) init an hour to help the ui \r\n      // const currentTime = moment().add(2, 'hour').startOf('hour');\r\n      // const time = currentTime.format('HH:00');\r\n      // this.onTimeSlotSelected(outputDate, time);\r\n    } else {\r\n      const selectedTime = moment(inputDate).format('HH:mm');\r\n      this.onTimeSlotSelected(outputDate, selectedTime);\r\n    }\r\n\r\n  }\r\n  \r\n/**\r\n * The function initializes the week days by calculating them based on the output date.\r\n */\r\n  private initializeWeekDays(): void {\r\n    const outputDate = this.getOutputDate();\r\n    this.calculateWeekDays(outputDate);\r\n  }\r\n  \r\n/**\r\n * The function takes in an array of classrooms and returns an array of lesson events for the available\r\n * classrooms.\r\n * @param {Classroom[]} classrooms - An array of Classroom objects.\r\n * @returns an array of Lesson objects.\r\n */\r\n  private getAvailableClassroomEvents(classrooms: Classroom[]): Lesson[] {\r\n    return this.classroomService.filterAvailableClassrooms(classrooms)\r\n      .flatMap((classroom: any) => {\r\n        const teachersLabel = this.createTeachersLabelForEvent(classroom);\r\n        this.teachersLabels.push(teachersLabel);\r\n        this.availableClassrooms.push(classroom);\r\n        this.availableTeachers.push(classroom.teacher);\r\n        return classroom.lessons.map((lesson: Lesson) => this.createLessonEvent(lesson, classroom));\r\n      });\r\n  }\r\n  \r\n  /**\r\n   * The function creates a label for teachers based on the given classroom information.\r\n   * @param {Classroom} classroom - The \"classroom\" parameter is an object of type \"Classroom\".\r\n   * @returns an object with the following properties:\r\n   */\r\n  private createTeachersLabelForEvent(classroom: Classroom): any {\r\n    let icon = 'student';\r\n    if (classroom.type && classroom.type.includes('Trial')) {\r\n      icon = 'blue-plane';\r\n    }\r\n    if (classroom.type && classroom.type.includes('Gift')) {\r\n      icon = 'gift-box';\r\n    }\r\n    return {\r\n      language: classroom.language,\r\n      teacherName: classroom.teacher && classroom.teacher.firstName,\r\n      icon,\r\n      hoursLeft: classroom.packages && classroom.packages.length > 0\r\n        ? this.classroomService.getAccumulatedHoursLeft(classroom.packages)\r\n        : classroom.hoursLeft,\r\n      teacherId: classroom.teacher && classroom.teacher.id,\r\n      classroom: classroom.id,\r\n      classroomType: classroom.type,\r\n    };\r\n  }\r\n  \r\n/**\r\n * The function creates a lesson event object with specific properties based on the given lesson and\r\n * classroom data.\r\n * @param {any} lesson - The lesson parameter is an object that represents a lesson. It contains\r\n * properties such as startingDate (the date and time the lesson starts), duration (the duration of the\r\n * lesson in hours), and id (a unique identifier for the lesson).\r\n * @param {Classroom} classroom - The `classroom` parameter is an object that represents a classroom.\r\n * It likely contains information such as the classroom name, teacher, students, and other relevant\r\n * details.\r\n * @returns an object with the following properties:\r\n * - id: the id of the lesson\r\n * - title: the short full name of the teacher\r\n * - start: the starting date and time of the lesson (with the time set to 12:00:00)\r\n * - end: the ending date and time of the lesson (calculated based on the starting date and the\r\n * duration of the lesson\r\n */\r\n  private createLessonEvent(lesson: any, classroom: Classroom): any {\r\n    const start = new Date(lesson.startingDate);\r\n    const end = new Date(start.getTime() + lesson.duration * 60 * 60 * 1000);\r\n    const { teacher } = classroom;\r\n    const title = this.generalService.getShortFullName(teacher);\r\n    return {\r\n      id: lesson.id,\r\n      title,\r\n      start: lesson.startingDate.slice(0, 11) + '12:00:00',\r\n      end,\r\n      allDay: false,\r\n      backgroundColor: '#378006',\r\n      borderColor: '#378006',\r\n      textColor: '#ffffff',\r\n      extendedProps: {\r\n        type: 'lesson',\r\n        lesson,\r\n        classRoom: classroom,\r\n      },\r\n      editable: false,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * The function initializes the calendar agenda user ID based on the user's role and selected classroom\r\n   * or teacher.\r\n   */\r\n  private initializeCalendarAgendaUserId(): void {\r\n    if (this.authService.isTeacher) {\r\n      console.log(this.studentSelectedClassroom);\r\n      if (!this.studentSelectedClassroom) {\r\n        this.showToastMessage('info', '', 'No available classrooms for booking');\r\n      }\r\n      this.calendarAgendaUserId = this.getTeacherAspUserId();\r\n    } else if (this.authService.isStudent && this.selectedTeacher) {\r\n      this.calendarAgendaUserId = this.selectedTeacher.aspUserId;\r\n    } else {\r\n      this.showToastMessage('info', '', 'No available classrooms for booking or enrolled classrooms have no available hours.');\r\n      this.goBack();\r\n    }\r\n  }\r\n\r\n  private showToastMessage(severity: string, summary: string, detail: string): void {\r\n    this.toastService.setShowToastmessage({\r\n      severity: severity,\r\n      summary: summary,\r\n      detail: detail\r\n    });\r\n  }\r\n\r\n  private getTeacherAspUserId(): string {\r\n    return this.studentSelectedClassroom.teacher?.aspUserId!;\r\n  }\r\n\r\n  private goBack(): void {\r\n    this.location.back();\r\n  }\r\n  \r\n  private getTeacherLabelIcon(teacherLabel: Classroom): string {\r\n    let icon = 'student';\r\n  \r\n    if (teacherLabel.status.includes('Trial')) {\r\n      icon = 'blue-plane';\r\n    } else if (teacherLabel.status.includes('Gift')) {\r\n      icon = 'gift-box';\r\n    }\r\n\r\n    return icon;\r\n  }\r\n  \r\n  private createTeachersLabel(teacherLabel: Classroom, icon: string): { language: string | undefined, teacherName: string, icon: string, hoursLeft: number, teacherId: string, classroom: string } {\r\n    const { language, teacher, id, hoursLeft } = teacherLabel;\r\n    console.log(teacherLabel);\r\n    return { \r\n      language, \r\n      teacherName: teacher!.firstName, \r\n      icon, \r\n      hoursLeft, \r\n      teacherId: teacher!.id, \r\n      classroom: id \r\n    };\r\n  }\r\n  \r\n  private getOutputDate(): Date {\r\n    const outputFormat = \"ddd MMM DD YYYY HH:mm:ss [GMT]ZZ (z)\";\r\n    const inputDate = this.extraData.startingDate;\r\n    const outputDateStr = moment.utc(inputDate).startOf('day').local().format(outputFormat);\r\n    \r\n    const outputDate = new Date(Date.parse(outputDateStr));\r\n    outputDate.setHours(0, 0, 0, 0);\r\n  \r\n    return outputDate;\r\n  }\r\n\r\n  private hasExtraData() {\r\n    return typeof this.extraData !== 'undefined' && Object.keys(this.extraData).length > 0;\r\n  }\r\n\r\n  private hasExtraClassroomData(): boolean {\r\n    return !!this.extraClassroomData && Object.keys(this.extraClassroomData).length > 0;\r\n  }\r\n\r\n  /**\r\n   * Check if the selected date and time is in the past.\r\n   * @param {Date} selectedDateTime - The selected date and time.\r\n   * @returns {boolean} True if the selected date and time is in the past, false otherwise.\r\n   */\r\n  private isDateTimeInThePast(selectedDateTime: Date) {\r\n    const currentDateTime = moment();\r\n    const selectedDateTimeMoment = moment(selectedDateTime);\r\n  \r\n    return selectedDateTimeMoment.isBefore(currentDateTime);\r\n  }\r\n\r\n}\r\n\r\n", "<div #bookingWrapper>\r\n<app-block-viewer header=\"Booking System\"\r\nheaderBackgroundImage=\"/assets/images/dashboard/calendar-banner.svg\" blockClass=\"border-radius-bottom-10\"\r\ncontainerClass=\"bg-white p-2 border-round-lg\" [headerClass]=\"'justify-content-center my-2'\"\r\n[backButtonLabel]=\"'Back'\"\r\n[headerTextClass]=\"'font-xl font-semibold justify-content-center'\" style=\"height: inherit;\">\r\n    <div class=\"surface-section lg:px-1 lg:h-full\">\r\n        <div class=\"grid h-full flex-column lg:flex-row relative overflow-y-scroll\">\r\n            <div class=\"calendar-nav hidden lg:block\">\r\n                <button type=\"button\" class=\"prev\" (click)=\"goToPrevWeek()\">\r\n                    <img src=\"/assets/icons/arrow-right-calendar-grad.svg\" />\r\n                </button>\r\n                <button type=\"button\" class=\"next\" (click)=\"goToNextWeek()\">\r\n                    <img src=\"/assets/icons/arrow-right-calendar-grad.svg\" />\r\n                </button>\r\n            </div>\r\n            <div class=\"col lg:col-fixed lg:pr-0 lg:m-3 lg:m-0 left-col\">\r\n                <div class=\"flex align-items-center justify-content-start flex-wrap gap-3 mb-3\">\r\n                    <label for=\"Teacher\" class=\"col-fix flex gap-1 font-sm\" style=\"width: 70px;\"><img\r\n                            src=\"/assets/icons/student.svg\" />\r\n                            {{user.role == UserRoles.TEACHER ? 'Classroom':'Teacher'}} </label>\r\n\r\n                <ng-container *ngIf=\"user.role == UserRoles.TEACHER\">\r\n                <p-dropdown [options]=\"availableClassrooms\" [(ngModel)]=\"studentSelectedClassroom\" \r\n                styleClass=\"max-w-10rem\" panelStyleClass=\"bordered-panel\"\r\n                     placeholder=\"Select Classroom\" (ngModelChange)=\"onClassroomChange($event)\">\r\n                    <ng-template pTemplate=\"selectedItem\">\r\n                        <div class=\"flex align-items-center gap-2\" *ngIf=\"studentSelectedClassroom\">\r\n                            <img *ngIf=\"authService.isTeacher\" [src]=\"classroomService.getClassroomTypeSmallIconPath(studentSelectedClassroom)\"\r\n                            height=\"12\" />\r\n                            <div class=\"font-xs\" [innerHTML]=\"classroomService.getStudentNamesWithHTML(studentSelectedClassroom)\"></div>\r\n                        </div>\r\n                    </ng-template>\r\n                    <ng-template let-classroom pTemplate=\"item\">\r\n                        <div class=\"flex align-items-center gap-2\">\r\n                            <img *ngIf=\"authService.isTeacher\" [src]=\"classroomService.getClassroomTypeSmallIconPath(classroom)\"\r\n                            height=\"12\" />\r\n                            <div class=\"font-xs\" [innerHTML]=\"classroomService.getStudentNamesWithHTML(classroom, true)\"></div>\r\n                            <br>\r\n                        </div>\r\n                        <div class=\"flex gap-1 font-xs\">\r\n                             <img src=\"/assets/images/dashboard/calendar/check-circled.svg\" style=\"width: 12px\"/>\r\n                             {{ generalService.convertHoursToMinutesWithSuffix(classroomService.getAccumulatedHoursLeft(classroom.packages!), true) }} available</div>\r\n                    </ng-template>\r\n                </p-dropdown>\r\n                </ng-container>\r\n\r\n                        <ng-container *ngIf=\"user.role == UserRoles.STUDENT\">\r\n                            <p-dropdown [options]=\"teachersLabels\" [(ngModel)]=\"selectedTeachersLabel\" optionLabel=\"teacherName\"\r\n                            styleClass=\"max-w-10rem\" placeholder=\"Select Teacher\" \r\n                            (ngModelChange)=\"onTeacherChange($event)\">\r\n                                <ng-template pTemplate=\"selectedItem\">\r\n                                    <div class=\"flex align-items-center gap-2\" *ngIf=\"selectedTeachersLabel\">\r\n                                        <div class=\"capitalize\">{{ selectedTeachersLabel.language }} - {{ selectedTeachersLabel.teacherName }}</div>\r\n                                    </div>\r\n                                </ng-template>\r\n                                <ng-template let-teacher pTemplate=\"item\">\r\n                                    <div class=\"flex align-items-center gap-2 font-xs\">\r\n                                        <img src=\"/assets/images/dashboard/calendar/{{ teacher.icon }}.svg\" style=\"width: 18px\"/>\r\n                                        <div class=\"capitalize\">{{ teacher.language }} - {{ teacher.teacherName }}</div>\r\n                                        <br>\r\n                                    </div>\r\n                                      \r\n                                    <div class=\"flex gap-1 font-xs\">\r\n                                        <img src=\"/assets/images/dashboard/calendar/check-circled.svg\" style=\"width: 12px\"/>\r\n                                        {{ generalService.convertHoursToMinutesWithSuffix(teacher.hoursLeft, true) }} available</div>\r\n                                </ng-template>\r\n                            </p-dropdown>\r\n                        </ng-container>\r\n                            \r\n                </div>\r\n                <div class=\"flex align-items-center justify-content-start flex-wrap gap-3 mb-3\">\r\n                    <label for=\"Duration\" class=\"col-fix flex gap-1 font-sm\" style=\"width: 70px;\">\r\n                        <img src=\"/assets/icons/hourglass.svg\" /> Duration</label>\r\n                        <p-dropdown [options]=\"availableDuration\" \r\n                        [(ngModel)]=\"selectedDuration\" \r\n                        (onChange)=\"onDurationSelected($event)\"\r\n                        optionLabel=\"viewValue\"\r\n                        ></p-dropdown>\r\n                    <!-- <p-dropdown class=\"dropdown\" [options]=\"classrooms\" (onChange)=\"onChooseClassroomByStudent($event)\"\r\n                        styleClass=\"dropdown-blue rounded\" optionLabel=\"language\">\r\n                        <ng-template let-classroom pTemplate=\"item\">\r\n                            <div class=\"country-item\">\r\n                                <div class=\"country-name\">{{classroom.language}}</div>\r\n                            </div>\r\n                        </ng-template>\r\n                    </p-dropdown> -->\r\n                </div>\r\n\r\n                <div class=\"flex flex-column justify-content-between\"\r\n                    [ngStyle]=\"{'height': !isTabletScreenSize() ? height -150 + 'px' : 'auto'}\">\r\n                    <div>\r\n                    <div class=\"mt-5\">\r\n                        <app-calendar-agenda (dayClicked)=\"onAgendaDayClicked($event)\" (currentMonthChanged)=\"onCurrentMonthChanged($event)\" [currentSelectedDay]=\"selectedDay\" title=\"\"\r\n                            [reverseGradient]=\"true\" [showAvailability]=\"true\" [userId]=\"calendarAgendaUserId\"></app-calendar-agenda>\r\n                    </div>\r\n                \r\n                    <div class=\"grid font-2xs justify-content-between\">\r\n                        <div class=\"col-fixed col-circle pr-0 pb-0 gap-1\">\r\n                            <div class=\"high-bg circle\"></div>\r\n                            <div>High Availability</div>\r\n                        </div>\r\n                        <div class=\"col-fixed col-circle pr-0 pb-0 gap-1\">\r\n                            <div class=\"low-bg circle\"></div>\r\n                            <div>Low Availability</div>\r\n                        </div>\r\n                        <div class=\"col-fixed col-circle pb-0 gap-1\">\r\n                            <div class=\"no-bg circle\"></div>\r\n                            <div>No Availability</div>\r\n                        </div>\r\n                    </div>\r\n                    </div>\r\n                    <div *ngIf=\"radioChecked\">\r\n                        <div class=\"border-round-xl block-gradient-reverse mt-3 mb-0\">\r\n                            <div class=\"card-header text-center\">\r\n                                <div class=\"card-title font-xs py-2\">You have chosen a lesson for</div>\r\n                            </div>\r\n                            <div class=\"card-body px-2\">\r\n                                <div class=\"flex justify-content-center font-sm gap-1 text-primary\">\r\n                                    <div class=\"\">\r\n                                        <i class=\"pi pi-calendar font-sm mr-1\"></i>\r\n                                        <span *ngIf=\"selectedDay\">{{selectedDay | date:'EEEE, dd/MM'}}</span>\r\n                                    </div>\r\n                                    <div>\r\n                                        <i class=\"pi pi-clock font-sm mr-1\"></i>\r\n                                        <span *ngIf=\"selectedTimeSlot\">{{selectedTimeSlot}}</span>\r\n                                    </div>\r\n                                    <div>\r\n                                        <i class=\"pi pi-hourglass font-sm mr-1\"></i>\r\n                                        <span>{{selectedDuration.viewValue}}</span>\r\n                                    </div>\r\n                                </div>\r\n                                <!-- <div class=\"flex w-100\">\r\n                                            <button class=\"extend-backage w-100 border-noround\" pButton type=\"button\" label=\"EXTEND Package\"></button>\r\n                                            </div> -->\r\n                            </div>\r\n                        </div>\r\n                        <div [hidden]=\"true\">\r\n                            <div class=\"grid justify-content-evenly font-2xs  mt-2\">\r\n                                <div class=\"col-fixed flex align-items-center\">\r\n                                    <img src=\"/assets/icons/add-to-calendar.svg\" width=\"15\" class=\"mr-1\" />Add to Google Calendar\r\n                                </div>\r\n                                <div class=\"col-fixed align-items-center pr-0\">\r\n                                    <img src=\"/assets/icons/add-to-calendar.svg\" width=\"15\" class=\"mr-1\" />Add to Outlook Calendar\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"flex justify-content-center mt-2\">\r\n                            <button (click)=\"onLessonRequested()\" styleClass=\" px-2 \" icon=\"pi pi-calendar\" iconPos=\"left\"\r\n                                class=\"font-base mt-2 border-round-lg request\" pButton type=\"button\"\r\n                                [loading]=\"buttonLoading\"\r\n                                [label]=\"isRescheduling ? 'RESCHEDULE' : 'REQUEST'\"></button>\r\n                        </div>\r\n                    </div>\r\n                \r\n                </div>\r\n            </div>\r\n            <div class=\"col h-full p-2 md:pr-4 right-col\">\r\n                \r\n            <div class=\"calendar-nav relative block lg:hidden\">\r\n                <button type=\"button\" class=\"prev z-5\" (click)=\"goToPrevWeek()\">\r\n                    <img src=\"/assets/icons/arrow-right-calendar-grad.svg\" />\r\n                </button>\r\n                <button type=\"button\" class=\"next z-5\" (click)=\"goToNextWeek()\">\r\n                    <img src=\"/assets/icons/arrow-right-calendar-grad.svg\" />\r\n                </button>\r\n            </div>\r\n                <div class=\"appointment-popup h-full pl-1\">\r\n                    <div id=\"calendar\" class=\"appointment-calendar h-full\">\r\n                        <!-- <div class=\"current-week\">\r\n                            <span><i class=\"fa fa-calendar\"></i> Week of {{startDate | date: 'mediumDate'}} </span>\r\n                            <div class=\"calendar-nav\">\r\n                            </div>\r\n                        </div> -->\r\n                        <div class=\"calendar-wrapper\">\r\n                            <div class=\"flex align-items-center justify-content-center text-primary mb-1\">\r\n                                {{findMonthFromDateRange()}}\r\n                            </div> \r\n                            <div class=\"calendar-week relative flex justify-content-center\">\r\n\r\n                                <ul class=\"p-0 mx-2\">\r\n                                    <li *ngFor=\"let day of weekDays\">{{day | date: 'EEE'}} {{day.getDate()}}</li>\r\n                                </ul>\r\n                            </div>\r\n\r\n                            \r\n\r\n                                <div class=\"calendar-hours\">\r\n                                    <p-scrollPanel #scrollPanel [style]=\"{width: '100%'}\" styleClass=\"custombar1 h-30rem lg:h-full\">\r\n                                        \r\n                        <ng-container *ngIf=\"noClassroomsFound\">\r\n                            <div class=\"flex w-full flex-column align-items-center justify-content-center\">\r\n\r\n                                <div class=\"text-center w-full pt-5\">\r\n                                <img src=\"/assets/icons/fast-time-clock-ui-icon.png\" class=\"max-w-14rem\" />\r\n                                </div>\r\n\r\n                                <p class=\"text-primary font-lg\"> You have <b>0</b> hours available</p>\r\n                                <a *ngIf=\"authService.isStudent\" type=\"button\" (click)=\"generalService.navigateToBuyPackage()\" class=\"font-lg gray-border-button border-round-3xl gap-2 px-3 py-2\">\r\n                                    Purchase Package\r\n                                </a>\r\n                            </div>\r\n                        </ng-container>\r\n                                        <ul class=\"calendar-inside pb-2\">\r\n                                            <li *ngFor=\"let day of weekDays\">\r\n                                                <ul *ngFor=\"let hour of availableHours\">\r\n                                                    <ng-container *ngIf=\"isTimeSlotAvailableOnDay(day, hour) || authService.isTeacher\">\r\n                                                    <li [ngClass]=\"{\r\n                                                        'availability': isTimeSlotAvailableOnDay(day, hour),\r\n                                                        'no-availability': !isTimeSlotAvailableOnDay(day, hour) && authService.isTeacher,\r\n                                                        }\"\r\n                                                        class=\"relative font-sm\">\r\n                                                        <div class=\"flex justify-content-center align-items-center\"\r\n                                                        [ngClass]=\"{'pointer-events-none': !isTimeSlotAvailableOnDay(day, hour)}\">\r\n                                                            <label [class.checked]=\"isSelectedDayAndTime(day, hour) && radioChecked\" class=\"hour-label\" for=\"radio{{day}}{{hour}}\" (click)=\"onTimeSlotSelected(day, hour)\">{{hour}}</label>\r\n                                                            <input [disabled]=\"!isTimeSlotAvailableOnDay(day, hour)\" type=\"radio\" id=\"radio{{day}}{{hour}}\"\r\n                                                                name=\"timeslot\" value=\"{{hour}}\" [checked]=\"isSelectedDayAndTime(day, hour) && radioChecked ? true : null\"\r\n                                                                 />\r\n                                                        </div>\r\n                                                    </li>\r\n                                                    </ng-container>\r\n                                                </ul>\r\n                                            </li>\r\n                                        </ul>\r\n                                    </p-scrollPanel>\r\n                                </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</app-block-viewer>\r\n</div>"], "mappings": "AAEA,SAAqBA,EAAE,QAAQ,MAAM;AACrC,SAASC,GAAG,EAAEC,SAAS,EAAEC,SAAS,EAAEC,IAAI,QAAQ,gBAAgB;AAChE,SAAoBC,aAAa,QAAQ,qCAAqC;AAG9E,SAASC,OAAO,QAAQ,SAAS;AAKjC,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAAoCC,YAAY,QAA6B,kCAAkC;AAE/G,SAAeC,QAAQ,QAAQ,gCAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICanCC,EAAA,CAAAC,SAAA,cACc;;;;IADqBD,EAAA,CAAAE,UAAA,QAAAC,MAAA,CAAAC,gBAAA,CAAAC,6BAAA,CAAAF,MAAA,CAAAG,wBAAA,GAAAN,EAAA,CAAAO,aAAA,CAAgF;;;;;IADvHP,EAAA,CAAAQ,cAAA,cAA4E;IACxER,EAAA,CAAAS,UAAA,IAAAC,yEAAA,kBACc;IACdV,EAAA,CAAAC,SAAA,cAA4G;IAChHD,EAAA,CAAAW,YAAA,EAAM;;;;IAHIX,EAAA,CAAAY,SAAA,EAA2B;IAA3BZ,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAU,WAAA,CAAAC,SAAA,CAA2B;IAEZd,EAAA,CAAAY,SAAA,EAAgF;IAAhFZ,EAAA,CAAAE,UAAA,cAAAC,MAAA,CAAAC,gBAAA,CAAAW,uBAAA,CAAAZ,MAAA,CAAAG,wBAAA,GAAAN,EAAA,CAAAgB,cAAA,CAAgF;;;;;IAHzGhB,EAAA,CAAAS,UAAA,IAAAQ,mEAAA,kBAA4E;;;;IAAhCjB,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAG,wBAAA,CAA8B;;;;;IAQtEN,EAAA,CAAAC,SAAA,cACc;;;;;IADqBD,EAAA,CAAAE,UAAA,QAAAC,MAAA,CAAAC,gBAAA,CAAAC,6BAAA,CAAAa,YAAA,GAAAlB,EAAA,CAAAO,aAAA,CAAiE;;;;;IADxGP,EAAA,CAAAQ,cAAA,cAA2C;IACvCR,EAAA,CAAAS,UAAA,IAAAU,mEAAA,kBACc;IAEdnB,EADA,CAAAC,SAAA,cAAmG,SAC/F;IACRD,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAQ,cAAA,cAAgC;IAC3BR,EAAA,CAAAC,SAAA,cAAoF;IACpFD,EAAA,CAAAoB,MAAA,GAAmI;IAAApB,EAAA,CAAAW,YAAA,EAAM;;;;;IAPpIX,EAAA,CAAAY,SAAA,EAA2B;IAA3BZ,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAU,WAAA,CAAAC,SAAA,CAA2B;IAEZd,EAAA,CAAAY,SAAA,EAAuE;IAAvEZ,EAAA,CAAAE,UAAA,cAAAC,MAAA,CAAAC,gBAAA,CAAAW,uBAAA,CAAAG,YAAA,SAAAlB,EAAA,CAAAgB,cAAA,CAAuE;IAK3FhB,EAAA,CAAAY,SAAA,GAAmI;IAAnIZ,EAAA,CAAAqB,kBAAA,MAAAlB,MAAA,CAAAmB,cAAA,CAAAC,+BAAA,CAAApB,MAAA,CAAAC,gBAAA,CAAAoB,uBAAA,CAAAN,YAAA,CAAAO,QAAA,uBAAmI;;;;;;IApBhJzB,EAAA,CAAA0B,uBAAA,GAAqD;IACrD1B,EAAA,CAAAQ,cAAA,qBAEgF;IAFpCR,EAAA,CAAA2B,gBAAA,2BAAAC,oFAAAC,MAAA;MAAA7B,EAAA,CAAA8B,aAAA,CAAAC,GAAA;MAAA,MAAA5B,MAAA,GAAAH,EAAA,CAAAgC,aAAA;MAAAhC,EAAA,CAAAiC,kBAAA,CAAA9B,MAAA,CAAAG,wBAAA,EAAAuB,MAAA,MAAA1B,MAAA,CAAAG,wBAAA,GAAAuB,MAAA;MAAA,OAAA7B,EAAA,CAAAkC,WAAA,CAAAL,MAAA;IAAA,EAAsC;IAE9C7B,EAAA,CAAAmC,UAAA,2BAAAP,oFAAAC,MAAA;MAAA7B,EAAA,CAAA8B,aAAA,CAAAC,GAAA;MAAA,MAAA5B,MAAA,GAAAH,EAAA,CAAAgC,aAAA;MAAA,OAAAhC,EAAA,CAAAkC,WAAA,CAAiB/B,MAAA,CAAAiC,iBAAA,CAAAP,MAAA,CAAyB;IAAA,EAAC;IAQ3E7B,EAPA,CAAAS,UAAA,IAAA4B,6DAAA,0BAAsC,IAAAC,6DAAA,0BAOM;IAWhDtC,EAAA,CAAAW,YAAA,EAAa;;;;;IArBDX,EAAA,CAAAY,SAAA,EAA+B;IAA/BZ,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAoC,mBAAA,CAA+B;IAACvC,EAAA,CAAAwC,gBAAA,YAAArC,MAAA,CAAAG,wBAAA,CAAsC;;;;;IA8B1DN,EADJ,CAAAQ,cAAA,cAAyE,cAC7C;IAAAR,EAAA,CAAAoB,MAAA,GAA8E;IAC1GpB,EAD0G,CAAAW,YAAA,EAAM,EAC1G;;;;IADsBX,EAAA,CAAAY,SAAA,GAA8E;IAA9EZ,EAAA,CAAAyC,kBAAA,KAAAtC,MAAA,CAAAuC,qBAAA,CAAAC,QAAA,SAAAxC,MAAA,CAAAuC,qBAAA,CAAAE,WAAA,KAA8E;;;;;IAD1G5C,EAAA,CAAAS,UAAA,IAAAoC,mEAAA,kBAAyE;;;;IAA7B7C,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAuC,qBAAA,CAA2B;;;;;IAKvE1C,EAAA,CAAAQ,cAAA,cAAmD;IAC/CR,EAAA,CAAAC,SAAA,cAAyF;IACzFD,EAAA,CAAAQ,cAAA,cAAwB;IAAAR,EAAA,CAAAoB,MAAA,GAAkD;IAAApB,EAAA,CAAAW,YAAA,EAAM;IAChFX,EAAA,CAAAC,SAAA,SAAI;IACRD,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAQ,cAAA,cAAgC;IAC5BR,EAAA,CAAAC,SAAA,cAAoF;IACpFD,EAAA,CAAAoB,MAAA,GAAuF;IAAApB,EAAA,CAAAW,YAAA,EAAM;;;;;IAPxFX,EAAA,CAAAY,SAAA,EAA8D;IAA9DZ,EAAA,CAAA8C,sBAAA,8CAAAC,UAAA,CAAAC,IAAA,UAAAhD,EAAA,CAAAO,aAAA,CAA8D;IAC3CP,EAAA,CAAAY,SAAA,GAAkD;IAAlDZ,EAAA,CAAAyC,kBAAA,KAAAM,UAAA,CAAAJ,QAAA,SAAAI,UAAA,CAAAH,WAAA,KAAkD;IAM1E5C,EAAA,CAAAY,SAAA,GAAuF;IAAvFZ,EAAA,CAAAqB,kBAAA,MAAAlB,MAAA,CAAAmB,cAAA,CAAAC,+BAAA,CAAAwB,UAAA,CAAAE,SAAA,sBAAuF;;;;;;IAlBvGjD,EAAA,CAAA0B,uBAAA,GAAqD;IACjD1B,EAAA,CAAAQ,cAAA,qBAE0C;IAFHR,EAAA,CAAA2B,gBAAA,2BAAAuB,oFAAArB,MAAA;MAAA7B,EAAA,CAAA8B,aAAA,CAAAqB,GAAA;MAAA,MAAAhD,MAAA,GAAAH,EAAA,CAAAgC,aAAA;MAAAhC,EAAA,CAAAiC,kBAAA,CAAA9B,MAAA,CAAAuC,qBAAA,EAAAb,MAAA,MAAA1B,MAAA,CAAAuC,qBAAA,GAAAb,MAAA;MAAA,OAAA7B,EAAA,CAAAkC,WAAA,CAAAL,MAAA;IAAA,EAAmC;IAE1E7B,EAAA,CAAAmC,UAAA,2BAAAe,oFAAArB,MAAA;MAAA7B,EAAA,CAAA8B,aAAA,CAAAqB,GAAA;MAAA,MAAAhD,MAAA,GAAAH,EAAA,CAAAgC,aAAA;MAAA,OAAAhC,EAAA,CAAAkC,WAAA,CAAiB/B,MAAA,CAAAiD,eAAA,CAAAvB,MAAA,CAAuB;IAAA,EAAC;IAMrC7B,EALA,CAAAS,UAAA,IAAA4C,6DAAA,0BAAsC,IAAAC,6DAAA,0BAKI;IAW9CtD,EAAA,CAAAW,YAAA,EAAa;;;;;IAnBDX,EAAA,CAAAY,SAAA,EAA0B;IAA1BZ,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAAoD,cAAA,CAA0B;IAACvD,EAAA,CAAAwC,gBAAA,YAAArC,MAAA,CAAAuC,qBAAA,CAAmC;;;;;IAyE9D1C,EAAA,CAAAQ,cAAA,WAA0B;IAAAR,EAAA,CAAAoB,MAAA,GAAoC;;IAAApB,EAAA,CAAAW,YAAA,EAAO;;;;IAA3CX,EAAA,CAAAY,SAAA,EAAoC;IAApCZ,EAAA,CAAAwD,iBAAA,CAAAxD,EAAA,CAAAyD,WAAA,OAAAtD,MAAA,CAAAuD,WAAA,iBAAoC;;;;;IAI9D1D,EAAA,CAAAQ,cAAA,WAA+B;IAAAR,EAAA,CAAAoB,MAAA,GAAoB;IAAApB,EAAA,CAAAW,YAAA,EAAO;;;;IAA3BX,EAAA,CAAAY,SAAA,EAAoB;IAApBZ,EAAA,CAAAwD,iBAAA,CAAArD,MAAA,CAAAwD,gBAAA,CAAoB;;;;;;IAV3D3D,EAHZ,CAAAQ,cAAA,UAA0B,cACwC,cACrB,cACI;IAAAR,EAAA,CAAAoB,MAAA,mCAA4B;IACrEpB,EADqE,CAAAW,YAAA,EAAM,EACrE;IAGEX,EAFR,CAAAQ,cAAA,cAA4B,cAC4C,cAClD;IACVR,EAAA,CAAAC,SAAA,YAA2C;IAC3CD,EAAA,CAAAS,UAAA,IAAAmD,6CAAA,mBAA0B;IAC9B5D,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAQ,cAAA,WAAK;IACDR,EAAA,CAAAC,SAAA,aAAwC;IACxCD,EAAA,CAAAS,UAAA,KAAAoD,8CAAA,mBAA+B;IACnC7D,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAQ,cAAA,WAAK;IACDR,EAAA,CAAAC,SAAA,aAA4C;IAC5CD,EAAA,CAAAQ,cAAA,YAAM;IAAAR,EAAA,CAAAoB,MAAA,IAA8B;IAOpDpB,EAPoD,CAAAW,YAAA,EAAO,EACzC,EACJ,EAIJ,EACJ;IAGEX,EAFR,CAAAQ,cAAA,eAAqB,eACuC,eACL;IAC3CR,EAAA,CAAAC,SAAA,eAAuE;IAAAD,EAAA,CAAAoB,MAAA,+BAC3E;IAAApB,EAAA,CAAAW,YAAA,EAAM;IACNX,EAAA,CAAAQ,cAAA,eAA+C;IAC3CR,EAAA,CAAAC,SAAA,eAAuE;IAAAD,EAAA,CAAAoB,MAAA,gCAC3E;IAERpB,EAFQ,CAAAW,YAAA,EAAM,EACJ,EACJ;IAEFX,EADJ,CAAAQ,cAAA,eAA8C,kBAIc;IAHhDR,EAAA,CAAAmC,UAAA,mBAAA2B,gEAAA;MAAA9D,EAAA,CAAA8B,aAAA,CAAAiC,GAAA;MAAA,MAAA5D,MAAA,GAAAH,EAAA,CAAAgC,aAAA;MAAA,OAAAhC,EAAA,CAAAkC,WAAA,CAAS/B,MAAA,CAAA6D,iBAAA,EAAmB;IAAA,EAAC;IAK7ChE,EAFgE,CAAAW,YAAA,EAAS,EAC/D,EACJ;;;;IAhCqBX,EAAA,CAAAY,SAAA,GAAiB;IAAjBZ,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAuD,WAAA,CAAiB;IAIjB1D,EAAA,CAAAY,SAAA,GAAsB;IAAtBZ,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAwD,gBAAA,CAAsB;IAIvB3D,EAAA,CAAAY,SAAA,GAA8B;IAA9BZ,EAAA,CAAAwD,iBAAA,CAAArD,MAAA,CAAA8D,gBAAA,CAAAC,SAAA,CAA8B;IAQ/ClE,EAAA,CAAAY,SAAA,EAAe;IAAfZ,EAAA,CAAAE,UAAA,gBAAe;IAaZF,EAAA,CAAAY,SAAA,GAAyB;IACzBZ,EADA,CAAAE,UAAA,YAAAC,MAAA,CAAAgE,aAAA,CAAyB,UAAAhE,MAAA,CAAAiE,cAAA,4BAC0B;;;;;IA8B/CpE,EAAA,CAAAQ,cAAA,SAAiC;IAAAR,EAAA,CAAAoB,MAAA,GAAuC;;IAAApB,EAAA,CAAAW,YAAA,EAAK;;;;IAA5CX,EAAA,CAAAY,SAAA,EAAuC;IAAvCZ,EAAA,CAAAyC,kBAAA,KAAAzC,EAAA,CAAAyD,WAAA,OAAAY,MAAA,eAAAA,MAAA,CAAAC,OAAA,OAAuC;;;;;;IAiB5EtE,EAAA,CAAAQ,cAAA,YAAmK;IAApHR,EAAA,CAAAmC,UAAA,mBAAAoC,uEAAA;MAAAvE,EAAA,CAAA8B,aAAA,CAAA0C,GAAA;MAAA,MAAArE,MAAA,GAAAH,EAAA,CAAAgC,aAAA;MAAA,OAAAhC,EAAA,CAAAkC,WAAA,CAAS/B,MAAA,CAAAmB,cAAA,CAAAmD,oBAAA,EAAqC;IAAA,EAAC;IAC1FzE,EAAA,CAAAoB,MAAA,yBACJ;IAAApB,EAAA,CAAAW,YAAA,EAAI;;;;;IAVZX,EAAA,CAAA0B,uBAAA,GAAwC;IAGhC1B,EAFJ,CAAAQ,cAAA,cAA+E,cAEtC;IACrCR,EAAA,CAAAC,SAAA,cAA2E;IAC3ED,EAAA,CAAAW,YAAA,EAAM;IAENX,EAAA,CAAAQ,cAAA,YAAgC;IAACR,EAAA,CAAAoB,MAAA,iBAAS;IAAApB,EAAA,CAAAQ,cAAA,QAAG;IAAAR,EAAA,CAAAoB,MAAA,QAAC;IAAApB,EAAA,CAAAW,YAAA,EAAI;IAACX,EAAA,CAAAoB,MAAA,uBAAe;IAAApB,EAAA,CAAAW,YAAA,EAAI;IACtEX,EAAA,CAAAS,UAAA,IAAAiE,mDAAA,gBAAmK;IAGvK1E,EAAA,CAAAW,YAAA,EAAM;;;;;IAHEX,EAAA,CAAAY,SAAA,GAA2B;IAA3BZ,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAU,WAAA,CAAA8D,SAAA,CAA2B;;;;;;IAQX3E,EAAA,CAAA0B,uBAAA,GAAmF;IAQ3E1B,EAPR,CAAAQ,cAAA,aAI6B,cAEiD,gBACyF;IAAxCR,EAAA,CAAAmC,UAAA,mBAAAyC,iFAAA;MAAA5E,EAAA,CAAA8B,aAAA,CAAA+C,IAAA;MAAA,MAAAC,QAAA,GAAA9E,EAAA,CAAAgC,aAAA,GAAA+C,SAAA;MAAA,MAAAC,OAAA,GAAAhF,EAAA,CAAAgC,aAAA,GAAA+C,SAAA;MAAA,MAAA5E,MAAA,GAAAH,EAAA,CAAAgC,aAAA;MAAA,OAAAhC,EAAA,CAAAkC,WAAA,CAAS/B,MAAA,CAAA8E,kBAAA,CAAAD,OAAA,EAAAF,QAAA,CAA6B;IAAA,EAAC;IAAC9E,EAAA,CAAAoB,MAAA,GAAQ;IAAApB,EAAA,CAAAW,YAAA,EAAQ;IAC/KX,EAAA,CAAAC,SAAA,gBAEO;IAEfD,EADI,CAAAW,YAAA,EAAM,EACL;;;;;;;IAZDX,EAAA,CAAAY,SAAA,EAGE;IAHFZ,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAkF,eAAA,KAAAC,GAAA,EAAAhF,MAAA,CAAAiF,wBAAA,CAAAJ,OAAA,EAAAF,QAAA,IAAA3E,MAAA,CAAAiF,wBAAA,CAAAJ,OAAA,EAAAF,QAAA,KAAA3E,MAAA,CAAAU,WAAA,CAAAC,SAAA,EAGE;IAGFd,EAAA,CAAAY,SAAA,EAAyE;IAAzEZ,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAqF,eAAA,KAAAC,GAAA,GAAAnF,MAAA,CAAAiF,wBAAA,CAAAJ,OAAA,EAAAF,QAAA,GAAyE;IAC9D9E,EAAA,CAAAY,SAAA,EAAiE;IAAjEZ,EAAA,CAAAuF,WAAA,YAAApF,MAAA,CAAAqF,oBAAA,CAAAR,OAAA,EAAAF,QAAA,KAAA3E,MAAA,CAAAsF,YAAA,CAAiE;IAAoBzF,EAAA,CAAA0F,sBAAA,iBAAAV,OAAA,MAAAF,QAAA,KAA0B;IAAyC9E,EAAA,CAAAY,SAAA,EAAQ;IAARZ,EAAA,CAAAwD,iBAAA,CAAAsB,QAAA,CAAQ;IACjG9E,EAAA,CAAAY,SAAA,EAAyB;IAAzBZ,EAAA,CAAA0F,sBAAA,gBAAAV,OAAA,MAAAF,QAAA,KAAyB;IAC3E9E,EAAA,CAAA2F,qBAAA,UAAAb,QAAA,CAAgB;IAAC9E,EAD9B,CAAAE,UAAA,cAAAC,MAAA,CAAAiF,wBAAA,CAAAJ,OAAA,EAAAF,QAAA,EAAiD,YAAA3E,MAAA,CAAAqF,oBAAA,CAAAR,OAAA,EAAAF,QAAA,KAAA3E,MAAA,CAAAsF,YAAA,eACsD;;;;;IAX1HzF,EAAA,CAAAQ,cAAA,SAAwC;IACpCR,EAAA,CAAAS,UAAA,IAAAmF,yDAAA,4BAAmF;IAevF5F,EAAA,CAAAW,YAAA,EAAK;;;;;;IAfcX,EAAA,CAAAY,SAAA,EAAkE;IAAlEZ,EAAA,CAAAE,UAAA,SAAAC,MAAA,CAAAiF,wBAAA,CAAAJ,OAAA,EAAAF,QAAA,KAAA3E,MAAA,CAAAU,WAAA,CAAAC,SAAA,CAAkE;;;;;IAFzFd,EAAA,CAAAQ,cAAA,SAAiC;IAC7BR,EAAA,CAAAS,UAAA,IAAAoF,0CAAA,iBAAwC;IAiB5C7F,EAAA,CAAAW,YAAA,EAAK;;;;IAjBoBX,EAAA,CAAAY,SAAA,EAAiB;IAAjBZ,EAAA,CAAAE,UAAA,YAAAC,MAAA,CAAA2F,cAAA,CAAiB;;;ADlJtF,WAAaC,sBAAsB;EAA7B,MAAOA,sBAAsB;IA2CjCC,YACS5F,gBAAkC,EAClCkB,cAA8B,EAC7BT,WAAwB,EACxBoF,WAAwB,EACxBC,eAAgC,EAChCC,aAA4B,EAC5BC,aAA4B,EAC7BC,YAA0B,EACzBC,KAAqB,EACrBC,MAAc,EACdC,KAAwB,EACxBC,QAAkB;MAXnB,KAAArG,gBAAgB,GAAhBA,gBAAgB;MAChB,KAAAkB,cAAc,GAAdA,cAAc;MACb,KAAAT,WAAW,GAAXA,WAAW;MACX,KAAAoF,WAAW,GAAXA,WAAW;MACX,KAAAC,eAAe,GAAfA,eAAe;MACf,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,aAAa,GAAbA,aAAa;MACd,KAAAC,YAAY,GAAZA,YAAY;MACX,KAAAC,KAAK,GAALA,KAAK;MACL,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,KAAK,GAALA,KAAK;MACL,KAAAC,QAAQ,GAARA,QAAQ;MAtD6B,KAAAC,cAAc,GAAe,EAAgB;MAChD,KAAAC,WAAW,GAAgB,EAAiB;MAC/E,KAAArG,wBAAwB,GAAc,EAAe;MAC9D,KAAAsG,SAAS,GAAS,IAAIC,IAAI,EAAE;MAC5B,KAAAC,QAAQ,GAAW,EAAE;MACrB,KAAAC,KAAK,GAAa,EAAE;MACpB,KAAAC,QAAQ,GAAmD,EAAE;MAC7D,KAAAlB,cAAc,GAAa,EAAE;MACrB,KAAAmB,IAAI,GAAG,IAAIrH,OAAO,EAAE;MACrB,KAAAsH,UAAU,GAAgB,EAAE;MAC5B,KAAAC,8BAA8B,GAAgB,EAAE;MAChD,KAAAC,aAAa,GAAG,IAAI;MAC3B,KAAAC,iBAAiB,GAAU,EAAE;MAC7B,KAAAC,YAAY,GAAU,EAAE;MACxB,KAAAC,eAAe,GAAQ,IAAI;MAC3B,KAAAC,oBAAoB,GAAU,EAAE;MAGhC,KAAAC,IAAI,GAAS,EAAU;MACvB,KAAAlE,cAAc,GAAU,EAAE;MAC1B,KAAAb,qBAAqB,GAAQ,IAAI;MACjC,KAAAgF,YAAY,GAAQ,IAAI;MACxB,KAAAC,QAAQ,GAAU,EAAE;MACpB,KAAAC,iBAAiB,GAAU,EAAE;MAC7B,KAAA3D,gBAAgB,GAAQ,IAAI;MAC5B,KAAA4D,GAAG,GAAsB,EAAuB;MAChD,KAAAC,eAAe,GAAG,EAAE;MAGpB,KAAAvF,mBAAmB,GAAU,EAAE;MAC/B,KAAAwF,SAAS,GAAGhI,QAAQ;MACpB,KAAA0F,YAAY,GAAG,KAAK;MACpB,KAAAuC,oBAAoB,GAAG,EAAE;MACzB,KAAAC,SAAS,GAAQ,EAAE;MACnB,KAAAC,kBAAkB,GAAQ,EAAE;MAC5B,KAAA9D,cAAc,GAAG,KAAK;MACtB,KAAA+D,SAAS,GAAG,KAAK;MACjB,KAAAC,UAAU,GAAG,KAAK;MAClB,KAAAC,SAAS,GAAG,CAAC;MACb,KAAAC,MAAM,GAAG,CAAC;MACV,KAAAC,iBAAiB,GAAG,KAAK;MACzB,KAAApE,aAAa,GAAG,KAAK;MAenB,IAAI,CAACyC,SAAS,GAAG,IAAIC,IAAI,EAAE;IAC7B;IACA2B,QAAQA,CAAA;MACN,MAAMC,WAAW,GAAG,IAAI,CAACnC,KAAK,CAACoC,QAAQ,CAACD,WAAW;MACnD,MAAME,YAAY,GAAGF,WAAW,CAAC,QAAQ,CAAC;MAC1C,MAAMG,eAAe,GAAGH,WAAW,CAAC,WAAW,CAAC;MAChD,MAAMI,mBAAmB,GAAGJ,WAAW,CAAC,YAAY,CAAC;MACrD,MAAMK,cAAc,GAAGL,WAAW,CAAC,UAAU,CAAC;MAC9C,IAAI,CAACL,UAAU,GAAG,IAAI,CAACW,kBAAkB,EAAE;MAE3C,IAAIF,mBAAmB,EAAE;QACvB,IAAI,CAACzE,cAAc,GAAG,IAAI;MAC5B;MACA,IAAI0E,cAAc,EAAE;QAClB,IAAI,CAACX,SAAS,GAAG,IAAI;MACvB;MACA,IAAIQ,YAAY,EAAE;QAChB,MAAMK,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACP,YAAY,CAAC;QACvCQ,OAAO,CAACC,GAAG,CAACJ,MAAM,CAAC;QACnB,IAAI,CAACf,SAAS,GAAG;UACf,UAAU,EAAEe,MAAM,CAACK,EAAE;UACrB,cAAc,EAAEL,MAAM,CAACM,MAAM;UAC7B,aAAa,EAAEN,MAAM,CAACO,WAAW;UACjC,QAAQ,EAAEP,MAAM,CAACM,MAAM;UACvB,cAAc,EAAEN,MAAM,CAACQ,YAAY;UACnC,UAAU,EAAER,MAAM,CAACrB,QAAQ;UAC3B,cAAc,EAAEqB,MAAM,CAACS;SACxB;MACH;MAEA,IAAIb,eAAe,EAAE;QACnB,MAAMc,SAAS,GAAGT,IAAI,CAACC,KAAK,CAACN,eAAe,CAAC;QAC7CO,OAAO,CAACC,GAAG,CAACM,SAAS,CAAC;QACtB,IAAI,CAACxB,kBAAkB,GAAG;UACxB,aAAa,EAAEwB,SAAS,CAACL,EAAE;UAC3B,QAAQ,EAAEK,SAAS,CAACJ,MAAM;UAC1B,eAAe,EAAEI,SAAS,CAACC;SAC5B;MACH;MAEA,IAAI,CAAChC,QAAQ,GAAG,CACd;QAAEiC,KAAK,EAAE,EAAE;QAAE1F,SAAS,EAAE;MAAK,CAAC,EAC9B;QAAE0F,KAAK,EAAE,EAAE;QAAE1F,SAAS,EAAE;MAAK,CAAE,EAC/B;QAAE0F,KAAK,EAAE,EAAE;QAAE1F,SAAS,EAAE;MAAK,CAAE,EAC/B;QAAE0F,KAAK,EAAE,EAAE;QAAE1F,SAAS,EAAE;MAAI,CAAE,EAC9B;QAAE0F,KAAK,EAAE,EAAE;QAAE1F,SAAS,EAAE;MAAQ,CAAE,EAClC;QAAE0F,KAAK,EAAE,EAAE;QAAE1F,SAAS,EAAE;MAAQ,CAAE,EAClC;QAAE0F,KAAK,EAAE,GAAG;QAAE1F,SAAS,EAAE;MAAQ,CAAE,EACnC;QAAE0F,KAAK,EAAE,GAAG;QAAE1F,SAAS,EAAE;MAAI,CAAE,EAC/B;QAAE0F,KAAK,EAAE,GAAG;QAAE1F,SAAS,EAAE;MAAQ,CAAE,EACnC;QAAE0F,KAAK,EAAE,GAAG;QAAE1F,SAAS,EAAE;MAAQ,CAAE,EACnC;QAAE0F,KAAK,EAAE,GAAG;QAAE1F,SAAS,EAAE;MAAQ,CAAE,EACnC;QAAE0F,KAAK,EAAE,GAAG;QAAE1F,SAAS,EAAE;MAAI,CAAE,CAChC;MACD,IAAI,CAAC0D,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC;MAC3C,IAAI,CAAC1D,gBAAgB,GAAG,IAAI,CAAC0D,QAAQ,CAAC,CAAC,CAAC;MACxC,IAAI,CAACkC,iBAAiB,CAAC,IAAIhD,IAAI,EAAE,CAAC;MAClC,IAAI,CAACiD,cAAc,EAAE;MACrB,IAAI,CAACC,YAAY,EAAE;MAEnB,IAAI,CAACtC,IAAI,GAAG,IAAI,CAAC5G,WAAW,CAACmJ,eAAe,EAAE;MAE9C,IAAI,CAACC,kBAAkB,EAAE;IAC3B;IAEAlB,kBAAkBA,CAAA;MAChB,MAAMmB,WAAW,GAAGC,MAAM,CAACC,UAAU;MACrC,OAAOF,WAAW,GAAG,GAAG;IAC1B;IAGAG,kBAAkBA,CAAA;MAChB,MAAMH,WAAW,GAAGC,MAAM,CAACC,UAAU;MACrC,OAAOF,WAAW,GAAG,IAAI,CAAC,CAAC;IAC7B;IAEAI,WAAWA,CAAA;MAET,IAAI,CAAClC,UAAU,GAAG,IAAI,CAACW,kBAAkB,EAAE;MAC3CI,OAAO,CAACC,GAAG,CAAC,IAAI,CAAChB,UAAU,CAAC;IAC9B;IAEAmC,eAAeA,CAAA;MAEb,IAAI,CAACtD,IAAI,CAACuD,IAAI,GAAG,IAAI,CAAClJ,cAAc,CAACmJ,UAAU,CAACC,IAAI,CAClDhL,IAAI,CAAC,CAAC,CAAC,EACPD,SAAS,CAACkL,GAAG,IAAG;QACdxB,OAAO,CAACC,GAAG,CAACuB,GAAG,CAAC;QAChB,IAAIA,GAAG,CAACC,KAAK,IAAID,GAAG,CAACE,KAAK,EAAE;UAC1B,OAAOvL,EAAE,CAAC,CAAC,CAAC;QACd,CAAC,MAAM;UACL,OAAO,IAAI,CAAC6G,aAAa,CAAC2E,cAAc;QAC1C;MACF,CAAC,CAAC,EACFtL,SAAS,CAAC,CAAC,CAAC,CACb,CAACuL,SAAS,CAACzC,MAAM,IAAG;QACnB,IAAIA,MAAM,KAAK,CAAC,EAAE;UAChB,IAAI,CAAC0C,YAAY,CAAC,CAAC,CAAC;UACtB,IAAI,CAAC1C,MAAM,GAAGA,MAAM;UAClB,IAAI,CAAC5B,cAAc,CAACuE,aAAa,CAACC,KAAK,CAAC5C,MAAM,GAAIA,MAAM,GAAG,EAAE,GAAG,IAAK;QACvE,CAAC,MAAM;UACL,IAAI,CAAC0C,YAAY,CAAC,CAAC,CAAC;UACpB,IAAI,CAACtE,cAAc,CAACuE,aAAa,CAACC,KAAK,CAAC5C,MAAM,GAAI,MAAO;QAC3D;QACA,IAAI,CAAC9B,KAAK,CAAC2E,aAAa,EAAE,CAAC,CAAC;MAC9B,CAAC,CAAC;MACF;MACA;MACA;MACA,IAAI,CAACC,cAAc,EAAE;IACvB;IAEAC,kBAAkBA,CAAA,GAClB;IAEAC,WAAWA,CAAA;MACT,IAAI,CAACrE,IAAI,CAACsE,WAAW,EAAE;IACzB;IAEAP,YAAYA,CAACQ,IAAY;MACvB,IAAI,CAACnD,SAAS,GAAGmD,IAAI;IACvB;IAEAC,YAAYA,CAAA;MACV,OAAO,IAAI,CAACpD,SAAS;IACvB;IAEA+C,cAAcA,CAAA;MACZM,UAAU,CAAC,MAAK;QACd,MAAMC,aAAa,GAAG,IAAI,CAAChF,WAAW,CAACiF,kBAAmB,CAACX,aAAa,CAACY,sBAAsB,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;QAC3H,MAAMC,oBAAoB,GAAGH,aAAa,CAACI,YAAY,GAAG,CAAC;QAC3DJ,aAAa,CAACK,SAAS,GAAGF,oBAAoB;MAChD,CAAC,EAAE,IAAI,CAAC;IACV;IAEAG,kBAAkBA,CAACC,KAAU;MAC3B,IAAI,CAACvI,gBAAgB,GAAG,EAAE;MAC1B,IAAI,CAAC8B,YAAY,GAAG,KAAK;MACzB0D,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC1F,WAAW,CAAC;MAC7B;IACF;IAEAuB,kBAAkBA,CAACkH,GAAS,EAAEC,IAAY;MAExC,IAAI,CAAC1I,WAAW,GAAG7D,MAAM,CAACsM,GAAG,EAAE,iBAAiB,CAAC,CAACE,MAAM,EAAE;MAC1D,IAAI,CAAC1I,gBAAgB,GAAGyI,IAAI;MAC5B,IAAI,CAACE,yBAAyB,EAAE;MAChC,MAAMC,gBAAgB,GAAG1M,MAAM,CAACsM,GAAG,EAAE,kCAAkC,CAAC;MACxE,MAAMK,iBAAiB,GAAGD,gBAAgB,CAACE,MAAM,CAAC,kBAAkB,GAAGL,IAAI,CAAC;MAE5E;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA,IAAI,IAAI,CAAC3E,IAAI,CAACiF,IAAI,KAAK3M,QAAQ,CAAC4M,OAAO,EAAE;QACvC,IAAI,CAAClH,YAAY,GAAG,IAAI;MAC1B,CAAC,MAAM;QACL,IAAI,CAAC,IAAI,CAACmH,mBAAmB,CAAC,IAAI/F,IAAI,CAAC2F,iBAAiB,CAAC,CAAC,EAAE;UAC1D,IAAI,CAAC/G,YAAY,GAAG,IAAI;QAC1B,CAAC,MAAM;UACL,IAAI,CAACY,YAAY,CAACwG,mBAAmB,CAAC;YACpCC,QAAQ,EAAE,MAAM;YAChBC,OAAO,EAAE,EAAE;YACXC,MAAM,EAAE;WACT,CAAC;UACF,IAAI,CAACvH,YAAY,GAAG,KAAK;QAC3B;MACF;IACF;IAEAwH,qBAAqBA,CAAA;MACnB,IAAI,CAACvJ,WAAW,GAAG,IAAImD,IAAI,EAAE;MAC7B,IAAI,CAAClD,gBAAgB,GAAG,EAAE;MAC1B,IAAI,CAAC2I,yBAAyB,EAAE;MAChC,IAAI,CAAC7G,YAAY,GAAG,KAAK;IAC3B;IAEAD,oBAAoBA,CAAC2G,GAAS,EAAEC,IAAY;MAC1C,MAAMc,CAAC,GAAG,IAAIrG,IAAI,CAACsF,GAAG,CAAC;MACvB,IAAI,IAAI,CAACgB,YAAY,EAAE,EAAE;QACvB,OAAQD,CAAC,CAAC5I,OAAO,EAAE,GAAG,EAAE,GAAG4I,CAAC,CAACE,QAAQ,EAAE,KAAO,IAAI,CAAC1J,WAAY,CAACY,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,CAACZ,WAAY,CAAC0J,QAAQ,EAAG,IAAIhB,IAAI,KAAK,IAAI,CAACzI,gBAAgB;MAClJ,CAAC,MAAM;QACL,OAAOwI,GAAG,KAAK,IAAI,CAACzI,WAAW,IAAI0I,IAAI,KAAK,IAAI,CAACzI,gBAAgB;MACnE;IACF;IAEF;;;;;;;IAOU0J,sBAAsBA,CAACC,OAAY;MACzC,IAAI,CAACA,OAAO,IAAI,IAAI,CAAChM,cAAc,CAACiM,eAAe,CAACD,OAAO,CAAC,EAAE;QAC5D,OAAOhO,EAAE,CAAC,IAAI,CAAC;MACjB;MACA,MAAMkO,IAAI,GAAG;QACX,GAAG,IAAI,CAACC,aAAa,EAAE;QACvBC,MAAM,EAAEJ,OAAO,CAACK;OACjB;MACD,OAAO,IAAI,CAACC,wBAAwB,CAACJ,IAAI,CAAC;IAE5C;IAEQI,wBAAwBA,CAACC,WAAoD;MACnF1E,OAAO,CAACC,GAAG,CAACyE,WAAW,CAAC;MACxB,OAAO,IAAI,CAAC3H,eAAe,CAAC4H,4BAA4B,CAACD,WAAW,CAAC,CAACnD,IAAI,CACxEnL,GAAG,CAAEoL,GAAQ,IAAI;QACf;QAEA;QACA,IAAI,CAACoD,aAAa,GAAIpD,GAAG,CAACqD,oBAAqB;QAE/C,IAAI,CAAClI,cAAc,GAAG,IAAI,CAACwG,yBAAyB,EAAE;QAEtD;QACA;QAEA,OAAO,IAAI;MACb,CAAC,CAAC,CACH;IACH;IAEA2B,wBAAwBA,CAACC,OAAiB,EAAEC,SAAiB;MAC3D,OAAOD,OAAO,CAACE,MAAM,CAAEpF,MAAW,IAAKA,MAAM,CAACsE,OAAO,CAACjE,EAAE,KAAK8E,SAAS,CAAC;IACzE;IAEA/K,eAAeA,CAAC8I,KAA4B;MAC9C;MACI,IAAI,CAACvI,gBAAgB,GAAG,EAAE;MAC1B,IAAI,CAAC8B,YAAY,GAAG,KAAK;MAE3B;MACE,IAAI,CAAC8B,eAAe,GAAG,IAAI,CAACF,iBAAiB,CAACgH,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACjF,EAAE,KAAK6C,KAAK,CAACiC,SAAS,CAAC;MAE3F;MACE,MAAM5E,WAAW,GAAG,IAAI,CAAChH,mBAAmB,CAAC8L,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACjF,EAAE,KAAK,IAAI,CAAC3G,qBAAqB,CAACgH,SAAS,CAAC;MAC7GP,OAAO,CAACC,GAAG,CAACG,WAAW,CAAC;MAE5B;MACI,IAAI,CAACgF,oCAAoC,CAAChF,WAAW,CAAC;MAExD;MACE,IAAI,CAACtC,IAAI,CAACuH,GAAG,CAAC,IAAI,CAACnB,sBAAsB,CAAC,IAAI,CAAC9F,eAAe,CAAC,CAACwD,SAAS,CAAEJ,GAAG,IAAI;QAChF,IAAI,CAACnD,oBAAoB,GAAG,IAAI,CAACF,YAAY;QAC7C6B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC5B,oBAAoB,CAAC;MAChE,CAAC,CAAC,CAAC;MAEL;MACE,IAAI,CAACQ,oBAAoB,GAAG,IAAI,CAACT,eAAe,CAACoG,SAAS;IAC5D;IAEA;;;;;;;IAOAvL,iBAAiBA,CAAC8J,KAAgB;MAChC/C,OAAO,CAACC,GAAG,CAAC8C,KAAK,CAAC;MAClB,IAAI,CAAC5L,wBAAwB,GAAG4L,KAAK;MACrC/C,OAAO,CAACC,GAAG,CAAC8C,KAAK,CAAC;MAClB,IAAI,IAAI,CAAC5L,wBAAwB,CAACgN,OAAO,EAAE;QACzC,IAAI,CAACtF,oBAAoB,GAAG,IAAI,CAAC1H,wBAAwB,CAACgN,OAAO,EAAEK,SAAU;MAC/E;MACA,IAAI,CAACY,oCAAoC,CAACrC,KAAK,CAAC;IAClD;IAEAuC,eAAeA,CAACnB,OAAa;MAC3B,OAAO,IAAIA,OAAO,CAACoB,QAAQ,IAAIpB,OAAO,CAACqB,SAAS,EAAE;IACpD;IACA;;;;IAKA;;;;IAIA9E,iBAAiBA,CAACjD,SAAe;MAC/B,MAAMgI,KAAK,GAAG/O,MAAM,CAAC+G,SAAS,CAAC,CAACyF,MAAM,EAAE;MACxC,MAAMwC,QAAQ,GAAG,IAAIhI,IAAI,CAAC+H,KAAK,CAACE,WAAW,EAAE,EAAEF,KAAK,CAACxB,QAAQ,EAAE,EAAEwB,KAAK,CAACtK,OAAO,EAAE,CAAC;MACjF,MAAMyK,YAAY,GAAG,IAAIlI,IAAI,CAACgI,QAAQ,CAAC;MACvC,IAAI,CAAC/H,QAAQ,GAAGkI,KAAK,CAACC,IAAI,CAAC;QAAEC,MAAM,EAAE,IAAI,CAAC7G;MAAS,CAAE,EAAE,CAAC8G,CAAC,EAAEC,CAAC,KAAI;QAC9D,MAAMlC,CAAC,GAAG,IAAIrG,IAAI,CAACkI,YAAY,CAAC;QAChC7B,CAAC,CAACmC,OAAO,CAACnC,CAAC,CAAC5I,OAAO,EAAE,GAAG8K,CAAC,CAAC;QAC1B,OAAOlC,CAAC;MACV,CAAC,CAAC;IAEJ;IAEA;;;;IAIAO,aAAaA,CAAA;MACX;MACA,MAAM6B,KAAK,GAAG,IAAI,CAACxI,QAAQ,CAACvH,GAAG,CAACgQ,UAAU,IAAI,IAAI1I,IAAI,CAAC0I,UAAU,CAAC,CAAC;MAEnE;MACA,MAAMC,QAAQ,GAAGF,KAAK,CAAC,CAAC,CAAC;MACzB,MAAMjD,MAAM,GAAGiD,KAAK,CAACA,KAAK,CAACJ,MAAM,GAAG,CAAC,CAAC;MAEtC;MACA;MACA,OAAO;QACLM,QAAQ,EAAE3P,MAAM,CAAE2P,QAAQ,EAAG,iCAAiC,CAAC,CAAC/C,MAAM,CAAC,YAAY,CAAC,GAAG,gBAAgB;QACvGJ,MAAM,EAAExM,MAAM,CAAEwM,MAAM,EAAG,iCAAiC,CAAC,CAACI,MAAM,CAAC,YAAY,CAAC,GAAG;OACpF;IAEH;IAEAgD,sBAAsBA,CAAA;MACpB,MAAMjC,IAAI,GAAG,IAAI,CAACC,aAAa,EAAE;MAEjC,MAAM+B,QAAQ,GAAG3P,MAAM,CAAC2N,IAAI,CAACgC,QAAQ,CAAC;MACtC,MAAMnD,MAAM,GAAGxM,MAAM,CAAC2N,IAAI,CAACnB,MAAM,CAAC;MAElC,MAAMqD,SAAS,GAAGF,QAAQ,CAAC/C,MAAM,CAAC,MAAM,CAAC;MACzC,MAAMkD,OAAO,GAAGtD,MAAM,CAACI,MAAM,CAAC,MAAM,CAAC;MACrC,MAAMmD,QAAQ,GAAGJ,QAAQ,CAAC/C,MAAM,CAAC,MAAM,CAAC;MACxC,MAAMoD,MAAM,GAAGxD,MAAM,CAACI,MAAM,CAAC,MAAM,CAAC;MAEpC,IAAIiD,SAAS,KAAKC,OAAO,EAAE;QACzB,OAAO,GAAGD,SAAS,IAAIE,QAAQ,EAAE;MACnC,CAAC,MAAM;QACL,OAAO,GAAGF,SAAS,MAAMC,OAAO,IAAIE,MAAM,EAAE;MAC9C;IACF;IAEAvD,yBAAyBA,CAAA;MACvB,MAAMwD,oBAAoB,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;MAC9E,MAAMhK,cAAc,GAAa,EAAE;MACnC,MAAMiK,SAAS,GAAG,CAAC;MACnB,MAAMC,OAAO,GAAG,EAAE;MAClB,MAAMC,gBAAgB,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAEjD,KAAK,IAAI7D,IAAI,GAAG2D,SAAS,EAAE3D,IAAI,IAAI4D,OAAO,EAAE5D,IAAI,EAAE,EAAE;QAClD,MAAM8D,UAAU,GAAG9D,IAAI,CAAC+D,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QACnD,KAAK,MAAMC,MAAM,IAAIJ,gBAAgB,EAAE;UACrC,MAAMK,UAAU,GAAG,GAAGJ,UAAU,IAAIG,MAAM,EAAE;UAC5C,IAAIE,WAAW,GAAG,IAAI;UAEtB;UACA;UACA;UACA;UACA;UACA;UACA;UAEA,IAAIA,WAAW,EAAE;YACfzK,cAAc,CAAC0K,IAAI,CAACF,UAAU,CAAC;UACjC;QACF;MACF;MAEA,OAAOxK,cAAc;IACvB;IAEF;;;IAGAgE,cAAcA,CAAA;MACZ,MAAMiG,SAAS,GAAG,CAAC;MACnB,MAAMC,OAAO,GAAG,EAAE;MAClB,MAAMS,YAAY,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MAC7C,IAAI,CAAC1J,KAAK,GAAG,EAAE;MAEf,KAAK,IAAIqF,IAAI,GAAG2D,SAAS,EAAE3D,IAAI,IAAI4D,OAAO,EAAE5D,IAAI,EAAE,EAAE;QAClD,MAAM8D,UAAU,GAAG9D,IAAI,CAAC+D,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QAEnD,KAAK,MAAMC,MAAM,IAAII,YAAY,EAAE;UACjC,MAAMH,UAAU,GAAG,GAAGJ,UAAU,IAAIG,MAAM,EAAE;UAC5C,IAAI,CAACtJ,KAAK,CAACyJ,IAAI,CAACF,UAAU,CAAC;QAC7B;MACF;MAEA,KAAK,IAAIlE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAG2D,SAAS,EAAE3D,IAAI,EAAE,EAAE;QAC3C,MAAM8D,UAAU,GAAG9D,IAAI,CAAC+D,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QAEnD,KAAK,MAAMC,MAAM,IAAII,YAAY,EAAE;UACjC,MAAMH,UAAU,GAAG,GAAGJ,UAAU,IAAIG,MAAM,EAAE;UAC5C,IAAI,CAACtJ,KAAK,CAACyJ,IAAI,CAACF,UAAU,CAAC;QAC7B;MACF;IACF;IAEAI,qBAAqBA,CAAC3J,KAAa;MACjC,OAAOA,KAAK,GAAG,EAAE;IACnB;IACA4J,gCAAgCA,CAAC5J,KAAa;MAC5C,MAAM9D,SAAS,GAAG8D,KAAK,CAAC,CAAC;MACzB,MAAM6J,WAAW,GAAG,IAAI,CAACF,qBAAqB,CAACzN,SAAS,CAAC;MACzD,MAAM4N,WAAW,GAAG,IAAI,CAACH,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC;MAEnDvH,OAAO,CAACC,GAAG,CAACnG,SAAS,CAAC;MACtB,IAAI,CAAC2E,iBAAiB,GAAG,IAAI,CAACD,QAAQ,CAACyG,MAAM,CAAEE,IAAI,IAAKA,IAAI,CAAC1E,KAAK,IAAIgH,WAAW,IAAItC,IAAI,CAAC1E,KAAK,IAAIiH,WAAW,CAAC;IAEjH;IACA;;;;IAIA9G,YAAYA,CAAA;MAEV,MAAM+G,WAAW,GAAG,CAAC;MACrB,MAAMC,SAAS,GAAG,EAAE;MACpB,IAAIC,OAAO,GAAG,EAAE;MAChB,IAAIC,SAAS,GAAG,EAAE;MAClB,KAAK,MAAM9E,GAAG,IAAI,IAAI,CAACnF,QAAQ,EAAE;QAC/B,MAAMkK,MAAM,GAAG,IAAI,CAAClK,QAAQ,CAACmF,GAAG,CAAC;QACjC,KAAK,MAAMC,IAAI,IAAI8E,MAAM,EAAE;UACzB,IAAIA,MAAM,CAAC9E,IAAI,CAAC,EAAE;YAChB,MAAM,CAAC+E,OAAO,EAAEC,SAAS,CAAC,GAAGhF,IAAI,CAACiF,KAAK,CAAC,GAAG,CAAC;YAC5C,MAAMC,OAAO,GAAGC,QAAQ,CAACJ,OAAO,CAAC;YACjC,MAAMK,SAAS,GAAGD,QAAQ,CAACH,SAAS,CAAC;YACrC,IAAIE,OAAO,GAAGN,OAAO,IAAKM,OAAO,KAAKN,OAAO,IAAIQ,SAAS,GAAGP,SAAU,EAAE;cACvED,OAAO,GAAGM,OAAO;cACjBL,SAAS,GAAGO,SAAS;YACvB;UACF;QACF;MACF;MACA,MAAMzB,SAAS,GAAGiB,OAAO;MACzB,MAAMhB,OAAO,GAAG,EAAE;MAClB,KAAK,IAAIZ,CAAC,GAAGW,SAAS,EAAEX,CAAC,IAAIY,OAAO,EAAEZ,CAAC,EAAE,EAAE;QACzC,MAAMhD,IAAI,GAAGgD,CAAC,CAACe,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;QAC1C,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC1B,IAAIpB,MAAM,GAAGoB,CAAC,GAAG,EAAE,GAAGX,WAAW;UACjC,IAAIT,MAAM,IAAI,EAAE,EAAE;YAChBA,MAAM,GAAG,EAAE;UACb;UACA,IAAKjB,CAAC,KAAKY,OAAO,IAAIK,MAAM,GAAGU,SAAS,IAAM3B,CAAC,KAAKW,SAAS,IAAIM,MAAM,GAAGY,SAAU,EAAE;YACpF;UACF;UACA,MAAMX,UAAU,GAAG,GAAGlE,IAAI,IAAIiE,MAAM,CAACF,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;UAClE,KAAK,MAAMjE,GAAG,IAAI,IAAI,CAACnF,QAAQ,EAAE;YAC/B,IAAI,CAACA,QAAQ,CAACmF,GAAG,CAAC,CAACmE,UAAU,CAAC,GAAG,IAAI;UACvC;QACF;MACF;IACF;IAEE;;;;;IAKAoB,cAAcA,CAACvF,GAAS,EAAEC,IAAY;MACpC,MAAMmD,UAAU,GAAGpD,GAAG,CAACwF,YAAY,EAAE;MACrC,MAAMpB,WAAW,GAAG,CAAC,IAAI,CAACvJ,QAAQ,CAACuI,UAAU,CAAC,GAAGnD,IAAI,CAAC;MACtD,IAAI,CAACpF,QAAQ,CAACuI,UAAU,CAAC,GAAG,IAAI,CAACvI,QAAQ,CAACuI,UAAU,CAAC,IAAI,EAAE;MAC3D,IAAI,CAACvI,QAAQ,CAACuI,UAAU,CAAC,CAACnD,IAAI,CAAC,GAAGmE,WAAW;IAC/C;IAEA;;;IAGAqB,YAAYA,CAAA;MACV,IAAI,CAAChL,SAAS,CAACyI,OAAO,CAAC,IAAI,CAACzI,SAAS,CAACtC,OAAO,EAAE,GAAG,IAAI,CAAC+D,SAAS,CAAC;MACjE,IAAI,CAACzB,SAAS,GAAG,IAAIC,IAAI,CAAC,IAAI,CAACD,SAAS,CAAC;MACzCuC,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE,IAAI,CAACxC,SAAS,CAAC;MACnC,IAAI,CAACiD,iBAAiB,CAAC,IAAI,CAACjD,SAAS,CAAC;MACtC,IAAI,CAACK,IAAI,CAACuD,IAAI,GAAG,IAAI,CAAC6C,sBAAsB,CAAC,IAAI,CAAC9F,eAAe,CAAC,CAACwD,SAAS,CAAEJ,GAAG,IAAG;QAElFxB,OAAO,CAACC,GAAG,CAACuB,GAAG,CAAC;MAClB,CAAC,CAAC;IACJ;IAEA;;;IAGAkH,YAAYA,CAAA;MACV,IAAI,CAACjL,SAAS,CAACyI,OAAO,CAAC,IAAI,CAACzI,SAAS,CAACtC,OAAO,EAAE,GAAG,IAAI,CAAC+D,SAAS,CAAC;MACjE,IAAI,CAACzB,SAAS,GAAG,IAAIC,IAAI,CAAC,IAAI,CAACD,SAAS,CAAC;MACzC,IAAI,CAACiD,iBAAiB,CAAC,IAAI,CAACjD,SAAS,CAAC;MACtC,IAAI,CAACK,IAAI,CAACuD,IAAI,GAAG,IAAI,CAAC6C,sBAAsB,CAAC,IAAI,CAAC9F,eAAe,CAAC,CAACwD,SAAS,CAAEJ,GAAG,IAAG,CACpF,CAAC,CAAC;IACJ;IAEA;;;;;;IAMAvF,wBAAwBA,CAAC0M,SAAiB,EAAE1F,IAAY;MACtD,MAAMD,GAAG,GAAGtM,MAAM,CAACiS,SAAS,EAAE,6BAA6B,CAAC;MAC5D,MAAMC,YAAY,GAAG5F,GAAG,CAACM,MAAM,CAAC,YAAY,CAAC;MAC7C,MAAMuF,OAAO,GAAG7F,GAAG,CAACM,MAAM,CAAC,KAAK,CAAC,CAACwF,WAAW,EAAE;MAE/C,IAAI,IAAI,CAAClE,aAAa,EAAE;QAGxB,MAAMmD,MAAM,GAAG,IAAI,CAACnD,aAAa,CAACM,IAAI,CAAE6D,CAAM,IAAKA,CAAC,CAACC,IAAI,KAAKJ,YAAY,CAAC;QAE3E;QACA,IAAI,IAAI,CAACtK,IAAI,CAACiF,IAAI,KAAK3M,QAAQ,CAAC4M,OAAO,EAAE;UACvC,OAAO,IAAI;QACb;QACA,IAAIuE,MAAM,IAAIA,MAAM,CAACkB,YAAY,CAAClD,MAAM,GAAG,CAAC,EAAE;UAC5C,MAAMpJ,cAAc,GAAwBoL,MAAM,CAACkB,YAAY;UAC/D,IAAIC,SAAS,GAAG,IAAI,CAACtL,KAAK,CAACuL,OAAO,CAAClG,IAAI,CAAC;UACxC,MAAMmG,iBAAiB,GAAG,IAAI,CAACtO,gBAAgB,CAAC2F,KAAK;UACrD,KAAK,IAAIwF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtJ,cAAc,CAACoJ,MAAM,EAAEE,CAAC,EAAE,EAAE;YAC9C,MAAMoD,KAAK,GAAG1M,cAAc,CAACsJ,CAAC,CAAC;YAC/B,IAAIqD,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC5L,KAAK,CAACuL,OAAO,CAACE,KAAK,CAACvD,IAAI,CAAC,EAAEoD,SAAS,CAAC;YAC/D,MAAMO,GAAG,GAAGJ,KAAK,CAACK,EAAE,KAAK,OAAO,GAAG,IAAI,CAAC9L,KAAK,CAACmI,MAAM,GAAG,CAAC,GAAG,IAAI,CAACnI,KAAK,CAACuL,OAAO,CAACE,KAAK,CAACK,EAAE,CAAC;YAEvF,IAAIJ,KAAK,IAAI,CAAC,IAAIG,GAAG,IAAI,CAAC,IAAIH,KAAK,IAAIJ,SAAS,IAAIA,SAAS,IAAIO,GAAG,EAAE;cACpE,MAAMhL,iBAAiB,GAAG,CAACgL,GAAG,GAAGH,KAAK,IAAID,KAAK,CAACK,EAAE,KAAK,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE;cAC7E,IAAIjL,iBAAiB,IAAI2K,iBAAiB,EAAE;gBAC1C,OAAO,IAAI;cACb;YACF;UACF;QACF;MACF;MACE,OAAO,KAAK;IACd;IAED;;;;;IAKCvO,iBAAiBA,CAAA;MACnB;MACI,IAAI,IAAI,CAACL,gBAAgB,KAAK,EAAE,EAAE;QAChC,IAAI,CAAC0C,YAAY,CAACwG,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACF;MACF;MAEF;MACE,MAAM8F,UAAU,GAAG,IAAI,CAACC,aAAa,EAAE;MAEzC;MACE,MAAMC,WAAW,GAAGnT,MAAM,EAAE,CAACoT,OAAO,CAAC,MAAM,CAAC;MAEhD;MACI,MAAMC,OAAO,GAAGrT,MAAM,CAAC,IAAI,CAAC6D,WAAW,CAAC;MAE1C;MACE,MAAMyP,aAAa,GAAGD,OAAO,CAACzG,MAAM,CAAC,6BAA6B,CAAC;MAEnE,IAAI,CAAC,IAAI,CAACrH,wBAAwB,CAAC+N,aAAa,EAAE,IAAI,CAACxP,gBAAgB,CAAC,EAAE;QACxE,IAAI,CAAC0C,YAAY,CAACwG,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACF;MACE;MAEN;MACE,IAAI,IAAI,CAAC7I,aAAa,EAAE;QACtB;MACF;MAEF;MACE,IAAI,CAACA,aAAa,GAAG,IAAI;MAE3B;MACE,IAAImF,MAAM,GAAGxJ,YAAY,CAACsT,SAAS;MAEvC;MACI,IAAI,IAAI,CAAC3L,IAAI,CAACiF,IAAI,KAAK3M,QAAQ,CAAC4M,OAAO,EAAE;QACvC,IAAI,IAAI,CAAC0G,qBAAqB,EAAE,EAAE;UAChC,IAAI,IAAI,CAACnL,kBAAkB,CAACoB,MAAM,CAAC2I,WAAW,EAAE,CAACqB,QAAQ,CAAC3T,aAAa,CAAC4T,KAAK,CAACtB,WAAW,EAAE,CAAC,EAAE;YAC5F3I,MAAM,GAAGxJ,YAAY,CAAC0T,cAAc;UACtC,CAAC,MAAM;YACLlK,MAAM,GAAGxJ,YAAY,CAAC2T,QAAQ;UAChC;QACF,CAAC,MAAM;UACL,MAAMC,iBAAiB,GAAG,IAAI,CAACnR,mBAAmB,CAAC8L,IAAI,CAAE3E,SAAc,IAAI;YACzE,OAAOA,SAAS,CAACL,EAAE,KAAK,IAAI,CAAC/I,wBAAwB,EAAE+I,EAAE;UAC3D,CAAC,CAAC;UACF,IAAIqK,iBAAiB,CAAC/J,IAAI,CAACsI,WAAW,EAAE,CAACqB,QAAQ,CAAC3T,aAAa,CAAC4T,KAAK,CAACtB,WAAW,EAAE,CAAC,EAAE;YACpF3I,MAAM,GAAGxJ,YAAY,CAAC0T,cAAc;UACtC,CAAC,MAAM;YACLlK,MAAM,GAAGxJ,YAAY,CAAC2T,QAAQ;UAChC;QACF;MACF;MAEA,IAAIlK,WAAW;MAEjB;MACE,IAAI,IAAI,CAAC9B,IAAI,CAACiF,IAAI,KAAK3M,QAAQ,CAAC4T,OAAO,EAAE;QACvC,MAAMD,iBAAiB,GAAG,IAAI,CAACnR,mBAAmB,CAAC8L,IAAI,CAAE3E,SAAoB,IAAI;UAC/E,OAAOA,SAAS,CAAC4D,OAAQ,CAACjE,EAAE,KAAK,IAAI,CAAC3G,qBAAqB,CAACyL,SAAS,IAAIzE,SAAS,CAAC/G,QAAQ,KAAK,IAAI,CAACD,qBAAqB,CAACC,QAAQ;QACrI,CAAC,CAAC;QACF,CAAC4G,WAAW,CAAC,GAAGmK,iBAAiB,GAAG,CAACA,iBAAiB,CAACrK,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC;MACrE,CAAC,MAAM;QACL,CAACE,WAAW,CAAC,GAAG,CAAC,IAAI,CAACjJ,wBAAwB,EAAE+I,EAAE,CAAC;MACrD;MAEF;MACE,MAAML,MAAM,GAAsB;QAChCO,WAAW;QACXD,MAAM;QACNE,YAAY,EAAE,IAAI,CAAClI,cAAc,CAACsS,+BAA+B,CAAC,IAAI,CAAClQ,WAAY,EAAE,IAAI,CAACC,gBAAgB,CAAC;QAC3GgE,QAAQ,EAAE,IAAI,CAACrG,cAAc,CAACuS,qBAAqB,CAAC,IAAI,CAAC5P,gBAAgB,CAAC2F,KAAK,CAAC;QAChFH,YAAY,EAAE;OACf;MAEH;MACE,IAAI,IAAI,CAACrF,cAAc,EAAE;QACvB,IAAI0P,YAAY,GAAwB;UACtCzK,EAAE,EAAE,IAAI,CAACpB,SAAS,CAAC8L,QAAQ;UAC3BxK,WAAW;UACXD,MAAM,EAAE,IAAI,CAACnB,SAAS,GAAGrI,YAAY,CAAC2T,QAAQ,GAAG,IAAI,CAACxL,SAAS,CAACqB,MAAM;UACtEE,YAAY,EAAE,IAAI,CAAClI,cAAc,CAACsS,+BAA+B,CAAC,IAAI,CAAClQ,WAAY,EAAE,IAAI,CAACC,gBAAgB,CAAC;UAC3GgE,QAAQ,EAAE,IAAI,CAACrG,cAAc,CAACuS,qBAAqB,CAAC,IAAI,CAAC5P,gBAAgB,CAAC2F,KAAK,CAAC;UAChFH,YAAY,EAAE,KAAK;UACnBuK,QAAQ,EAAE,IAAI,CAAC7L,SAAS,GAAG,WAAW,GAAG;SAC1C;QACDgB,OAAO,CAACC,GAAG,CAAC0K,YAAY,CAAC;QAC3B,IAAI,CAAC7M,IAAI,CAACuD,IAAI,GAAG,IAAI,CAACpE,aAAa,CAAC6N,MAAM,CAACH,YAAY,CAAC,CAAC/I,SAAS,CAACJ,GAAG,IAAG;UACvExB,OAAO,CAACC,GAAG,CAACuB,GAAG,CAAC;UAChB,IAAI,CAACtE,YAAY,CAACwG,mBAAmB,CAAC;YACpCC,QAAQ,EAAE,SAAS;YACnBC,OAAO,EAAE,EAAE;YACXC,MAAM,EAAE,yBAAyB,GAAGnN,MAAM,CAACqU,SAAS,CAAC,IAAI,CAAC5S,cAAc,CAACsS,+BAA+B,CAAC,IAAI,CAAClQ,WAAY,EAAE,IAAI,CAACC,gBAAgB,CAAC,CAAC,CAAC8I,MAAM,CAAC,kBAAkB;WAC9K,CAAC;UAEF,IAAI,CAAChG,QAAQ,CAAC0N,IAAI,EAAE;UACpB;QACF,CAAC,EAAEC,KAAK,IAAG;UACT,IAAI,CAACjQ,aAAa,GAAG,KAAK;QAC5B,CAAC,CAAC;MACF,CAAC,MAAM;QACL,MAAMkQ,eAAe,GAAGxU,MAAM,CAACqU,SAAS,CAAC,IAAI,CAAC5S,cAAc,CAACsS,+BAA+B,CAAC,IAAI,CAAClQ,WAAY,EAAE,IAAI,CAACC,gBAAgB,CAAC,CAAC,CAAC8I,MAAM,CAAC,kBAAkB,CAAC;QAClK,IAAI,CAACxF,IAAI,CAACuD,IAAI,GAAG,IAAI,CAACpE,aAAa,CAACkO,MAAM,CAACtL,MAAM,CAAC,CAAC+B,SAAS,CAACJ,GAAG,IAAG;UACjExB,OAAO,CAACC,GAAG,CAACuB,GAAG,CAAC;UAChB,IAAI,CAACtE,YAAY,CAACwG,mBAAmB,CAAC;YACpCC,QAAQ,EAAE,SAAS;YACnBC,OAAO,EAAE,EAAE;YACXC,MAAM,EAAE1D,MAAM,KAAKxJ,YAAY,CAAC2T,QAAQ,GAAG,qBAAqB,GAAGY,eAAe,GAAG,uBAAuB,GAAGA;WAChH,CAAC;UACF,IAAI,CAAC5N,QAAQ,CAAC0N,IAAI,EAAE;QACtB,CAAC,EAAEC,KAAK,IAAG;UACT,IAAI,CAACjQ,aAAa,GAAG,KAAK;QAC5B,CAAC,CAAC;MACJ;IACF;IAEQ8F,kBAAkBA,CAAA;MACxB,IAAI,CAAChD,IAAI,CAACuD,IAAI,GAAG,IAAI,CAACpK,gBAAgB,CAACmU,oBAAoB,CAAC,IAAI,CAAC9M,IAAI,CAAC4B,EAAE,CAAC,CAACqB,IAAI,CAC5EjL,SAAS,CAAEyH,UAAe,IAAI;QAE5B;QACAA,UAAU,GAAG,IAAI,CAAC9G,gBAAgB,CAACoU,yBAAyB,CAACtN,UAAU,CAAC;QACxE,OAAO,IAAI,CAACuN,uBAAuB,CAACvN,UAAU,CAAC;QAGjD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA;QACA;QACA,IAAI,IAAI,CAACO,IAAI,CAACiF,IAAI,KAAK3M,QAAQ,CAAC4M,OAAO,EAAE;UACvC,IAAI,CAACrM,wBAAwB,GAAG,IAAI,CAACiC,mBAAmB,CAAC,CAAC,CAAC;UAC3D,IAAI,CAACyF,oBAAoB,GAAG,IAAI,CAAC1H,wBAAwB,CAACgN,OAAO,EAAEK,SAAU;QAC/E,CAAC,MACI;UACH,IAAI,CAACpG,eAAe,GAAG,IAAI,CAACF,iBAAiB,CAAC,CAAC,CAAC;UAChD,IAAI,CAAC3E,qBAAqB,GAAG,IAAI,CAACa,cAAc,CAAC,CAAC,CAAC;UACnD,MAAMgG,WAAW,GAAG,IAAI,CAAChH,mBAAmB,CAAE8L,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACjF,EAAE,KAAK,IAAI,CAAC3G,qBAAqB,CAACgH,SAAS,CAAC;UAC9G,IAAI,CAACiH,gCAAgC,CAACpH,WAAW,CAACtG,SAAS,CAAC;UAC5D,IAAI,CAAC+E,oBAAoB,GAAG,IAAI,CAACT,eAAe,CAACoG,SAAS;QAC5D;QACA,OAAO,IAAI,CAACN,sBAAsB,CAAC,IAAI,CAAChG,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAC7D,CAAC,CAAC,CACH,CAAC0D,SAAS,CAAEJ,GAAQ,IAAI;QACvBxB,OAAO,CAACC,GAAG,CAACuB,GAAG,CAAC;QAChB;MACF,CAAC,CAAC;IACJ;IAEA;;;;;IAKA+J,iBAAiBA,CAACvC,IAAU;MAC1B,MAAM5C,UAAU,GAAG4C,IAAI,CAACR,YAAY,EAAE;MACtC,MAAMgD,UAAU,GAAG9U,MAAM,CAAC0P,UAAU,CAAC;MACrC,OAAOoF,UAAU,CAAClI,MAAM,CAAC,YAAY,CAAC;IACxC;IAEAmI,kBAAkBA,CAAC1I,KAAU;MAC3B/C,OAAO,CAACC,GAAG,CAAC8C,KAAK,CAAC;MAClB,IAAI,CAACrC,iBAAiB,CAACqC,KAAK,CAAC;MAC7B,IAAI,CAACjF,IAAI,CAACuH,GAAG,CAAC,IAAI,CAACnB,sBAAsB,CAAC,IAAI,CAAC9F,eAAe,CAAC,CAACwD,SAAS,CAAEJ,GAAG,IAAI;QAChF,IAAI,CAACnD,oBAAoB,GAAG,IAAI,CAACF,YAAY;QAC7C6B,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAC5B,oBAAoB,CAAC;MAChE,CAAC,CAAC,CAAC;IACL;IAEF;;;;;;IAMUiN,uBAAuBA,CAACvN,UAAuB;MACrD,IAAI,IAAI,CAACiG,YAAY,EAAE,EAAE;QACvB,MAAMzD,SAAS,GAAG,IAAI,CAACmL,gBAAgB,CAAC3N,UAAU,CAAC;QACnD,IAAI,CAACwC,SAAS,EAAE;UACdP,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;UACnD,OAAO0L,SAAS;QAClB;QAEA,IAAI,CAACC,eAAe,CAACrL,SAAS,CAAC;QAC/B,IAAI,CAACsL,qBAAqB,CAACtL,SAAS,CAAC;QACrCP,OAAO,CAACC,GAAG,CAACM,SAAS,CAAC;QACtB,IAAI,CAACuL,mBAAmB,CAACvL,SAAS,CAAC4D,OAAQ,CAAC;MAE9C,CAAC,MAAM;QACL,MAAM4H,MAAM,GAAG,IAAI,CAACC,2BAA2B,CAACjO,UAAU,CAAC;QAC3DiC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE8L,MAAM,CAAC;QAC3D,IAAI,CAAC5N,YAAY,CAACkJ,IAAI,CAAC,GAAG0E,MAAM,CAAC;MACnC;MACA,IAAI,CAACE,2BAA2B,EAAE;MAClC,IAAI,CAACC,0BAA0B,EAAE;MACjC,IAAI,CAACC,kBAAkB,EAAE;MAGzB,IAAI,IAAI,CAACzU,WAAW,CAACC,SAAS,EAAE;QAC9B,IAAI,CAAC,IAAI,CAACR,wBAAwB,EAAE;UAClC,IAAI,CAACiI,iBAAiB,GAAG,IAAI;UAC7B,OAAOjJ,EAAE,CAAC,IAAI,CAAC;QACjB;MACF;MACA,IAAI,CAACiW,8BAA8B,EAAE;MAErC,OAAO,IAAI,CAAClI,sBAAsB,CAAC,IAAI,CAAChG,iBAAiB,CAAC,CAAC,CAAC,CAAC;IAC/D;IAEF;;;;;;IAMUwN,gBAAgBA,CAAC3N,UAAuB;MAC9C,MAAMqC,WAAW,GAAG,IAAI,CAACtB,SAAS,CAACsB,WAAW;MAC9C,OAAOrC,UAAU,CAACmH,IAAI,CAAE3E,SAAoB,IAAKA,SAAS,CAACL,EAAE,KAAKE,WAAW,CAAC;IAChF;IAEF;;;;IAIUwL,eAAeA,CAACrL,SAAoB;MAC1C,MAAM1G,IAAI,GAAG,IAAI,CAACwS,mBAAmB,CAAC9L,SAAS,CAAC;MAChD,MAAM+L,aAAa,GAAG,IAAI,CAACC,mBAAmB,CAAChM,SAAS,EAAE1G,IAAI,CAAC;MAC/D,IAAI,CAACO,cAAc,CAACiN,IAAI,CAACiF,aAAa,CAAC;IACzC;IAEF;;;;IAIUT,qBAAqBA,CAACtL,SAAoB;MAChD,IAAI,CAACnH,mBAAmB,CAACiO,IAAI,CAAC9G,SAAS,CAAC;IAC1C;IAEF;;;;IAIUuL,mBAAmBA,CAAC3H,OAAa;MACvC,IAAI,CAACjG,iBAAiB,CAACmJ,IAAI,CAAClD,OAAO,CAAC;IACtC;IAEF;;;;IAIU8H,2BAA2BA,CAAA;MACjC,IAAI,IAAI,CAAChR,cAAc,EAAE;QACvB,IAAI,CAACH,gBAAgB,GAAG,IAAI,CAAC0D,QAAQ,CAAC0G,IAAI,CAAC1G,QAAQ,IAAIA,QAAQ,CAACiC,KAAK,KAAK,IAAI,CAAC3B,SAAS,CAACN,QAAQ,GAAG,EAAE,CAAC;MACzG,CAAC,MAAM;QACL,IAAI,CAAC1D,gBAAgB,GAAG,IAAI,CAAC0D,QAAQ,CAAC,CAAC,CAAC;MAC1C;MACA,IAAI,IAAI,CAAC0L,qBAAqB,EAAE,EAAE;QAChC,IAAI,CAAC/S,wBAAwB,GAAG,IAAI,CAACiC,mBAAmB,CAAC8L,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACjF,EAAE,KAAK,IAAI,CAACnB,kBAAkB,CAACqB,WAAW,CAAC;QAGxH,IAAI,IAAI,CAACjJ,wBAAwB,EAAE;UACjC,IAAI,CAACqQ,gCAAgC,CAAC,IAAI,CAACvQ,gBAAgB,CAACoB,uBAAuB,CAAC,IAAI,CAAClB,wBAAwB,CAACmB,QAAS,CAAC,CAAC;QAC/H;QACA,IAAI,CAAC,IAAI,CAACnB,wBAAwB,EAAE;UAClC,IAAI,CAAC+F,YAAY,CAACwG,mBAAmB,CAAC;YACpCC,QAAQ,EAAE,MAAM;YAChBC,OAAO,EAAE,EAAE;YACXC,MAAM,EAAE;WACT,CAAC;UAEF,IAAI,CAAC2I,kCAAkC,EAAE;QAC3C,CAAC,MAAM;UACL,IAAI,CAACpO,eAAe,GAAG,IAAI,CAACjH,wBAAwB,CAACgN,OAAO;UAC5D,IAAI,CAAC5K,qBAAqB,GAAG,IAAI,CAACa,cAAc,CAAC8K,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAAC5E,SAAS,KAAK,IAAI,CAACpJ,wBAAwB,CAAC+I,EAAE,CAAC;QACtH;MACF,CAAC,MAAM;QACL,IAAI,IAAI,CAAC5B,IAAI,CAACiF,IAAI,KAAK3M,QAAQ,CAAC4M,OAAO,EAAE;UACvC,IAAI,CAACrM,wBAAwB,GAAG,IAAI,CAACiC,mBAAmB,CAAC,CAAC,CAAC;UAE3D,IAAI,CAACgF,eAAe,GAAG,IAAI,CAACE,IAAI;QAClC,CAAC,MAAM;UACL,IAAI,CAACkO,kCAAkC,EAAE;QAC3C;MACF;IACF;IAEF;;;;IAIEA,kCAAkCA,CAAA;MAChC,IAAI,IAAI,CAAClO,IAAI,CAACiF,IAAI,KAAK3M,QAAQ,CAAC4M,OAAO,EAAE;QACvC,IAAI,CAACrM,wBAAwB,GAAG,IAAI,CAACiC,mBAAmB,CAAC,CAAC,CAAC;QAC3D,IAAI,CAACgF,eAAe,GAAG,IAAI,CAACE,IAAI;MAClC,CAAC,MAAM;QACL0B,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC/B,iBAAiB,CAAC;QACnC,IAAI,CAACE,eAAe,GAAG,IAAI,CAACF,iBAAiB,CAAC,CAAC,CAAC;QAChD,IAAI,CAAC3E,qBAAqB,GAAG,IAAI,CAACa,cAAc,CAAC,CAAC,CAAC;QACnD,MAAMmG,SAAS,GAAG,IAAI,CAACnH,mBAAmB,CAAC8L,IAAI,CAAEC,IAAI,IAAKA,IAAI,CAACjF,EAAE,KAAK,IAAI,CAAC3G,qBAAqB,CAACgH,SAAS,CAAC;QAE3G,IAAIA,SAAS,EAAE;UACb,IAAG,CAAC,IAAI,CAACtF,cAAc,EAAE;YACvB+E,OAAO,CAACC,GAAG,CAACM,SAAS,CAAC;YACtB,IAAI,CAAC6E,oCAAoC,CAAC7E,SAAS,CAAC;YACpD,IAAI,CAAC9B,iBAAiB,GAAG,IAAI,CAACD,QAAQ,CAACyG,MAAM,CAAEzG,QAAQ,IAAI;cACzD,OAAO,IAAI,CAACrG,cAAc,CAACuS,qBAAqB,CAAClM,QAAQ,CAACiC,KAAK,CAAC,IAAI,IAAI,CAACxJ,gBAAgB,CAACoB,uBAAuB,CAACkI,SAAS,CAACjI,QAAS,CAAC;YACxI,CAAC,CAAC;YACF;UACF;QACF;MACF;IACF;IAEF;;;;;IAKE8M,oCAAoCA,CAAC7E,SAAoB;MACvD,IAAI,IAAI,CAACtJ,gBAAgB,CAACwV,gBAAgB,CAAClM,SAAS,CAAC,EAAE;QACrD,MAAMmM,aAAa,GAAG,IAAI,CAAClO,QAAQ,CAAC,CAAC,CAAC;QACtC,IAAI,CAACC,iBAAiB,GAAG,CAACiO,aAAa,CAAC;QACxC,IAAI,CAAC5R,gBAAgB,GAAG4R,aAAa;MACvC,CAAC,MAAM;QACL,IAAI,CAACjO,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACD,QAAQ,CAAC;QAC3C,IAAI,CAAC1D,gBAAgB,GAAG,IAAI,CAAC0D,QAAQ,CAAC,CAAC,CAAC;QACxC,MAAMmO,oBAAoB,GAAG,IAAI,CAAC1V,gBAAgB,CAACoB,uBAAuB,CAACkI,SAAS,CAACjI,QAAS,CAAC;QAC/F,IAAI,CAACkP,gCAAgC,CAACmF,oBAAoB,CAAC;MAC7D;IACF;IAEAC,qBAAqBA,CAACC,WAAgB;MACpC,MAAMlD,UAAU,GAAG,IAAI,CAACC,aAAa,EAAE;MACvC5J,OAAO,CAACC,GAAG,CAAC0J,UAAU,CAAC;MACvB;MACA;MACA;MAEA;MACA;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;IACF;IAEQuC,0BAA0BA,CAAA;MAChC,MAAMvC,UAAU,GAAG,IAAI,CAACC,aAAa,EAAE;MACvC,IAAI,CAACnM,SAAS,GAAG,IAAIC,IAAI,CAACiM,UAAU,CAAC;MACrC,MAAMmD,SAAS,GAAG,IAAI,CAAChO,SAAS,CAACuB,YAAY;MAE7C,IAAI,CAAC,IAAI,CAACpF,cAAc,EAAE;QACxB;QACA;QACA;QACA;MAAA,CACD,MAAM;QACL,MAAM8R,YAAY,GAAGrW,MAAM,CAACoW,SAAS,CAAC,CAACxJ,MAAM,CAAC,OAAO,CAAC;QACtD,IAAI,CAACxH,kBAAkB,CAAC6N,UAAU,EAAEoD,YAAY,CAAC;MACnD;IAEF;IAEF;;;IAGUZ,kBAAkBA,CAAA;MACxB,MAAMxC,UAAU,GAAG,IAAI,CAACC,aAAa,EAAE;MACvC,IAAI,CAAClJ,iBAAiB,CAACiJ,UAAU,CAAC;IACpC;IAEF;;;;;;IAMUqC,2BAA2BA,CAACjO,UAAuB;MACzD,OAAO,IAAI,CAAC9G,gBAAgB,CAAC+V,yBAAyB,CAACjP,UAAU,CAAC,CAC/DkP,OAAO,CAAE1M,SAAc,IAAI;QAC1B,MAAM+L,aAAa,GAAG,IAAI,CAACY,2BAA2B,CAAC3M,SAAS,CAAC;QACjE,IAAI,CAACnG,cAAc,CAACiN,IAAI,CAACiF,aAAa,CAAC;QACvC,IAAI,CAAClT,mBAAmB,CAACiO,IAAI,CAAC9G,SAAS,CAAC;QACxC,IAAI,CAACrC,iBAAiB,CAACmJ,IAAI,CAAC9G,SAAS,CAAC4D,OAAO,CAAC;QAC9C,OAAO5D,SAAS,CAACwE,OAAO,CAAC3O,GAAG,CAAEyJ,MAAc,IAAK,IAAI,CAACsN,iBAAiB,CAACtN,MAAM,EAAEU,SAAS,CAAC,CAAC;MAC7F,CAAC,CAAC;IACN;IAEA;;;;;IAKQ2M,2BAA2BA,CAAC3M,SAAoB;MACtD,IAAI1G,IAAI,GAAG,SAAS;MACpB,IAAI0G,SAAS,CAACC,IAAI,IAAID,SAAS,CAACC,IAAI,CAAC2J,QAAQ,CAAC,OAAO,CAAC,EAAE;QACtDtQ,IAAI,GAAG,YAAY;MACrB;MACA,IAAI0G,SAAS,CAACC,IAAI,IAAID,SAAS,CAACC,IAAI,CAAC2J,QAAQ,CAAC,MAAM,CAAC,EAAE;QACrDtQ,IAAI,GAAG,UAAU;MACnB;MACA,OAAO;QACLL,QAAQ,EAAE+G,SAAS,CAAC/G,QAAQ;QAC5BC,WAAW,EAAE8G,SAAS,CAAC4D,OAAO,IAAI5D,SAAS,CAAC4D,OAAO,CAACqB,SAAS;QAC7D3L,IAAI;QACJC,SAAS,EAAEyG,SAAS,CAACjI,QAAQ,IAAIiI,SAAS,CAACjI,QAAQ,CAACyN,MAAM,GAAG,CAAC,GAC1D,IAAI,CAAC9O,gBAAgB,CAACoB,uBAAuB,CAACkI,SAAS,CAACjI,QAAQ,CAAC,GACjEiI,SAAS,CAACzG,SAAS;QACvBkL,SAAS,EAAEzE,SAAS,CAAC4D,OAAO,IAAI5D,SAAS,CAAC4D,OAAO,CAACjE,EAAE;QACpDK,SAAS,EAAEA,SAAS,CAACL,EAAE;QACvBkN,aAAa,EAAE7M,SAAS,CAACC;OAC1B;IACH;IAEF;;;;;;;;;;;;;;;;IAgBU2M,iBAAiBA,CAACtN,MAAW,EAAEU,SAAoB;MACzD,MAAM+I,KAAK,GAAG,IAAI5L,IAAI,CAACmC,MAAM,CAACQ,YAAY,CAAC;MAC3C,MAAMoJ,GAAG,GAAG,IAAI/L,IAAI,CAAC4L,KAAK,CAAC+D,OAAO,EAAE,GAAGxN,MAAM,CAACrB,QAAQ,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MACxE,MAAM;QAAE2F;MAAO,CAAE,GAAG5D,SAAS;MAC7B,MAAM+M,KAAK,GAAG,IAAI,CAACnV,cAAc,CAACoV,gBAAgB,CAACpJ,OAAO,CAAC;MAC3D,OAAO;QACLjE,EAAE,EAAEL,MAAM,CAACK,EAAE;QACboN,KAAK;QACLhE,KAAK,EAAEzJ,MAAM,CAACQ,YAAY,CAACmN,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,UAAU;QACpD/D,GAAG;QACHgE,MAAM,EAAE,KAAK;QACbC,eAAe,EAAE,SAAS;QAC1BC,WAAW,EAAE,SAAS;QACtBC,SAAS,EAAE,SAAS;QACpBC,aAAa,EAAE;UACbrN,IAAI,EAAE,QAAQ;UACdX,MAAM;UACNiO,SAAS,EAAEvN;SACZ;QACDwN,QAAQ,EAAE;OACX;IACH;IAEA;;;;IAIQ3B,8BAA8BA,CAAA;MACpC,IAAI,IAAI,CAAC1U,WAAW,CAACC,SAAS,EAAE;QAC9BqI,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC9I,wBAAwB,CAAC;QAC1C,IAAI,CAAC,IAAI,CAACA,wBAAwB,EAAE;UAClC,IAAI,CAAC6W,gBAAgB,CAAC,MAAM,EAAE,EAAE,EAAE,qCAAqC,CAAC;QAC1E;QACA,IAAI,CAACnP,oBAAoB,GAAG,IAAI,CAACoP,mBAAmB,EAAE;MACxD,CAAC,MAAM,IAAI,IAAI,CAACvW,WAAW,CAAC8D,SAAS,IAAI,IAAI,CAAC4C,eAAe,EAAE;QAC7D,IAAI,CAACS,oBAAoB,GAAG,IAAI,CAACT,eAAe,CAACoG,SAAS;MAC5D,CAAC,MAAM;QACL,IAAI,CAACwJ,gBAAgB,CAAC,MAAM,EAAE,EAAE,EAAE,qFAAqF,CAAC;QACxH,IAAI,CAACE,MAAM,EAAE;MACf;IACF;IAEQF,gBAAgBA,CAACrK,QAAgB,EAAEC,OAAe,EAAEC,MAAc;MACxE,IAAI,CAAC3G,YAAY,CAACwG,mBAAmB,CAAC;QACpCC,QAAQ,EAAEA,QAAQ;QAClBC,OAAO,EAAEA,OAAO;QAChBC,MAAM,EAAEA;OACT,CAAC;IACJ;IAEQoK,mBAAmBA,CAAA;MACzB,OAAO,IAAI,CAAC9W,wBAAwB,CAACgN,OAAO,EAAEK,SAAU;IAC1D;IAEQ0J,MAAMA,CAAA;MACZ,IAAI,CAAC5Q,QAAQ,CAAC0N,IAAI,EAAE;IACtB;IAEQqB,mBAAmBA,CAAC8B,YAAuB;MACjD,IAAItU,IAAI,GAAG,SAAS;MAEpB,IAAIsU,YAAY,CAAChO,MAAM,CAACgK,QAAQ,CAAC,OAAO,CAAC,EAAE;QACzCtQ,IAAI,GAAG,YAAY;MACrB,CAAC,MAAM,IAAIsU,YAAY,CAAChO,MAAM,CAACgK,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC/CtQ,IAAI,GAAG,UAAU;MACnB;MAEA,OAAOA,IAAI;IACb;IAEQ0S,mBAAmBA,CAAC4B,YAAuB,EAAEtU,IAAY;MAC/D,MAAM;QAAEL,QAAQ;QAAE2K,OAAO;QAAEjE,EAAE;QAAEpG;MAAS,CAAE,GAAGqU,YAAY;MACzDnO,OAAO,CAACC,GAAG,CAACkO,YAAY,CAAC;MACzB,OAAO;QACL3U,QAAQ;QACRC,WAAW,EAAE0K,OAAQ,CAACqB,SAAS;QAC/B3L,IAAI;QACJC,SAAS;QACTkL,SAAS,EAAEb,OAAQ,CAACjE,EAAE;QACtBK,SAAS,EAAEL;OACZ;IACH;IAEQ0J,aAAaA,CAAA;MACnB,MAAMwE,YAAY,GAAG,sCAAsC;MAC3D,MAAMtB,SAAS,GAAG,IAAI,CAAChO,SAAS,CAACuB,YAAY;MAC7C,MAAMgO,aAAa,GAAG3X,MAAM,CAAC4X,GAAG,CAACxB,SAAS,CAAC,CAAChD,OAAO,CAAC,KAAK,CAAC,CAACyE,KAAK,EAAE,CAACjL,MAAM,CAAC8K,YAAY,CAAC;MAEvF,MAAMzE,UAAU,GAAG,IAAIjM,IAAI,CAACA,IAAI,CAACqC,KAAK,CAACsO,aAAa,CAAC,CAAC;MACtD1E,UAAU,CAAC6E,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAE/B,OAAO7E,UAAU;IACnB;IAEQ3F,YAAYA,CAAA;MAClB,OAAO,OAAO,IAAI,CAAClF,SAAS,KAAK,WAAW,IAAI2P,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC5P,SAAS,CAAC,CAACiH,MAAM,GAAG,CAAC;IACxF;IAEQmE,qBAAqBA,CAAA;MAC3B,OAAO,CAAC,CAAC,IAAI,CAACnL,kBAAkB,IAAI0P,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC3P,kBAAkB,CAAC,CAACgH,MAAM,GAAG,CAAC;IACrF;IAEA;;;;;IAKQtC,mBAAmBA,CAACL,gBAAsB;MAChD,MAAMuL,eAAe,GAAGjY,MAAM,EAAE;MAChC,MAAMkY,sBAAsB,GAAGlY,MAAM,CAAC0M,gBAAgB,CAAC;MAEvD,OAAOwL,sBAAsB,CAACC,QAAQ,CAACF,eAAe,CAAC;IACzD;IAAC,QAAA3I,CAAA,G;uBApoCUpJ,sBAAsB,EAAA/F,EAAA,CAAAiY,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAnY,EAAA,CAAAiY,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAArY,EAAA,CAAAiY,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAvY,EAAA,CAAAiY,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAAzY,EAAA,CAAAiY,iBAAA,CAAAS,EAAA,CAAAC,eAAA,GAAA3Y,EAAA,CAAAiY,iBAAA,CAAAW,EAAA,CAAAC,aAAA,GAAA7Y,EAAA,CAAAiY,iBAAA,CAAAa,EAAA,CAAAC,aAAA,GAAA/Y,EAAA,CAAAiY,iBAAA,CAAAe,EAAA,CAAAC,YAAA,GAAAjZ,EAAA,CAAAiY,iBAAA,CAAAiB,EAAA,CAAAC,cAAA,GAAAnZ,EAAA,CAAAiY,iBAAA,CAAAiB,EAAA,CAAAE,MAAA,GAAApZ,EAAA,CAAAiY,iBAAA,CAAAjY,EAAA,CAAAqZ,iBAAA,GAAArZ,EAAA,CAAAiY,iBAAA,CAAAqB,GAAA,CAAAC,QAAA;IAAA;IAAA,QAAAC,EAAA,G;YAAtBzT,sBAAsB;MAAA0T,SAAA;MAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;UAAtB5Z,EAAA,CAAAmC,UAAA,oBAAA2X,iDAAA;YAAA,OAAAD,GAAA,CAAAxP,kBAAA,EAAoB;UAAA,UAAArK,EAAA,CAAA+Z,eAAA,CAAE;;;;;;;;;;;;;UClDnB/Z,EAThB,CAAAQ,cAAA,mBAAqB,0BAKuE,aACzC,aACiC,aAC9B,gBACsB;UAAzBR,EAAA,CAAAmC,UAAA,mBAAA6X,wDAAA;YAAAha,EAAA,CAAA8B,aAAA,CAAAmY,GAAA;YAAA,OAAAja,EAAA,CAAAkC,WAAA,CAAS2X,GAAA,CAAAjI,YAAA,EAAc;UAAA,EAAC;UACvD5R,EAAA,CAAAC,SAAA,aAAyD;UAC7DD,EAAA,CAAAW,YAAA,EAAS;UACTX,EAAA,CAAAQ,cAAA,gBAA4D;UAAzBR,EAAA,CAAAmC,UAAA,mBAAA+X,wDAAA;YAAAla,EAAA,CAAA8B,aAAA,CAAAmY,GAAA;YAAA,OAAAja,EAAA,CAAAkC,WAAA,CAAS2X,GAAA,CAAAhI,YAAA,EAAc;UAAA,EAAC;UACvD7R,EAAA,CAAAC,SAAA,aAAyD;UAEjED,EADI,CAAAW,YAAA,EAAS,EACP;UAGEX,EAFR,CAAAQ,cAAA,cAA6D,eACuB,iBACC;UAAAR,EAAA,CAAAC,SAAA,eACnC;UAClCD,EAAA,CAAAoB,MAAA,IAA2D;UAAApB,EAAA,CAAAW,YAAA,EAAQ;UA2BvEX,EAzBR,CAAAS,UAAA,KAAA0Z,+CAAA,2BAAqD,KAAAC,+CAAA,2BAyBQ;UAuB7Dpa,EAAA,CAAAW,YAAA,EAAM;UAEFX,EADJ,CAAAQ,cAAA,eAAgF,iBACE;UAC1ER,EAAA,CAAAC,SAAA,eAAyC;UAACD,EAAA,CAAAoB,MAAA,iBAAQ;UAAApB,EAAA,CAAAW,YAAA,EAAQ;UAC1DX,EAAA,CAAAQ,cAAA,sBAIC;UAHDR,EAAA,CAAA2B,gBAAA,2BAAA0Y,qEAAAxY,MAAA;YAAA7B,EAAA,CAAA8B,aAAA,CAAAmY,GAAA;YAAAja,EAAA,CAAAiC,kBAAA,CAAA4X,GAAA,CAAA5V,gBAAA,EAAApC,MAAA,MAAAgY,GAAA,CAAA5V,gBAAA,GAAApC,MAAA;YAAA,OAAA7B,EAAA,CAAAkC,WAAA,CAAAL,MAAA;UAAA,EAA8B;UAC9B7B,EAAA,CAAAmC,UAAA,sBAAAmY,gEAAAzY,MAAA;YAAA7B,EAAA,CAAA8B,aAAA,CAAAmY,GAAA;YAAA,OAAAja,EAAA,CAAAkC,WAAA,CAAY2X,GAAA,CAAA5N,kBAAA,CAAApK,MAAA,CAA0B;UAAA,EAAC;UAW/C7B,EATS,CAAAW,YAAA,EAAa,EAShB;UAMEX,EAJR,CAAAQ,cAAA,eACgF,WACvE,eACa,+BAEyE;UADxBR,EAA1C,CAAAmC,UAAA,wBAAAoY,2EAAA1Y,MAAA;YAAA7B,EAAA,CAAA8B,aAAA,CAAAmY,GAAA;YAAA,OAAAja,EAAA,CAAAkC,WAAA,CAAc2X,GAAA,CAAAjF,kBAAA,CAAA/S,MAAA,CAA0B;UAAA,EAAC,iCAAA2Y,oFAAA3Y,MAAA;YAAA7B,EAAA,CAAA8B,aAAA,CAAAmY,GAAA;YAAA,OAAAja,EAAA,CAAAkC,WAAA,CAAwB2X,GAAA,CAAA9D,qBAAA,CAAAlU,MAAA,CAA6B;UAAA,EAAC;UAExH7B,EAD2F,CAAAW,YAAA,EAAsB,EAC3G;UAGFX,EADJ,CAAAQ,cAAA,eAAmD,eACG;UAC9CR,EAAA,CAAAC,SAAA,eAAkC;UAClCD,EAAA,CAAAQ,cAAA,WAAK;UAAAR,EAAA,CAAAoB,MAAA,yBAAiB;UAC1BpB,EAD0B,CAAAW,YAAA,EAAM,EAC1B;UACNX,EAAA,CAAAQ,cAAA,eAAkD;UAC9CR,EAAA,CAAAC,SAAA,eAAiC;UACjCD,EAAA,CAAAQ,cAAA,WAAK;UAAAR,EAAA,CAAAoB,MAAA,wBAAgB;UACzBpB,EADyB,CAAAW,YAAA,EAAM,EACzB;UACNX,EAAA,CAAAQ,cAAA,eAA6C;UACzCR,EAAA,CAAAC,SAAA,eAAgC;UAChCD,EAAA,CAAAQ,cAAA,WAAK;UAAAR,EAAA,CAAAoB,MAAA,uBAAe;UAG5BpB,EAH4B,CAAAW,YAAA,EAAM,EACxB,EACJ,EACA;UACNX,EAAA,CAAAS,UAAA,KAAAga,sCAAA,mBAA0B;UA4ClCza,EADI,CAAAW,YAAA,EAAM,EACJ;UAIFX,EAHJ,CAAAQ,cAAA,eAA8C,eAEK,kBACiB;UAAzBR,EAAA,CAAAmC,UAAA,mBAAAuY,yDAAA;YAAA1a,EAAA,CAAA8B,aAAA,CAAAmY,GAAA;YAAA,OAAAja,EAAA,CAAAkC,WAAA,CAAS2X,GAAA,CAAAjI,YAAA,EAAc;UAAA,EAAC;UAC3D5R,EAAA,CAAAC,SAAA,cAAyD;UAC7DD,EAAA,CAAAW,YAAA,EAAS;UACTX,EAAA,CAAAQ,cAAA,kBAAgE;UAAzBR,EAAA,CAAAmC,UAAA,mBAAAwY,yDAAA;YAAA3a,EAAA,CAAA8B,aAAA,CAAAmY,GAAA;YAAA,OAAAja,EAAA,CAAAkC,WAAA,CAAS2X,GAAA,CAAAhI,YAAA,EAAc;UAAA,EAAC;UAC3D7R,EAAA,CAAAC,SAAA,cAAyD;UAEjED,EADI,CAAAW,YAAA,EAAS,EACP;UASUX,EARZ,CAAAQ,cAAA,eAA2C,eACgB,eAMrB,eACoD;UAC1ER,EAAA,CAAAoB,MAAA,IACJ;UAAApB,EAAA,CAAAW,YAAA,EAAM;UAGFX,EAFJ,CAAAQ,cAAA,eAAgE,cAEvC;UACjBR,EAAA,CAAAS,UAAA,KAAAma,qCAAA,iBAAiC;UAEzC5a,EADI,CAAAW,YAAA,EAAK,EACH;UAKEX,EADJ,CAAAQ,cAAA,eAA4B,4BACwE;UAE5GR,EAAA,CAAAS,UAAA,KAAAoa,+CAAA,4BAAwC;UAaxB7a,EAAA,CAAAQ,cAAA,cAAiC;UAC7BR,EAAA,CAAAS,UAAA,KAAAqa,qCAAA,iBAAiC;UA6B7E9a,EAVwC,CAAAW,YAAA,EAAK,EACO,EACd,EACR,EACJ,EACJ,EACJ,EACJ,EACJ,EACS,EACb;;;UAtOwCX,EAAA,CAAAY,SAAA,GAA6C;UAE3FZ,EAF8C,CAAAE,UAAA,8CAA6C,2BACjE,mEACwC;UAetCF,EAAA,CAAAY,SAAA,IAA2D;UAA3DZ,EAAA,CAAAqB,kBAAA,MAAAwY,GAAA,CAAApS,IAAA,CAAAiF,IAAA,IAAAmN,GAAA,CAAA9R,SAAA,CAAA4E,OAAA,gCAA2D;UAExD3M,EAAA,CAAAY,SAAA,EAAoC;UAApCZ,EAAA,CAAAE,UAAA,SAAA2Z,GAAA,CAAApS,IAAA,CAAAiF,IAAA,IAAAmN,GAAA,CAAA9R,SAAA,CAAA4E,OAAA,CAAoC;UAyB5B3M,EAAA,CAAAY,SAAA,EAAoC;UAApCZ,EAAA,CAAAE,UAAA,SAAA2Z,GAAA,CAAApS,IAAA,CAAAiF,IAAA,IAAAmN,GAAA,CAAA9R,SAAA,CAAA4L,OAAA,CAAoC;UA2BvC3T,EAAA,CAAAY,SAAA,GAA6B;UAA7BZ,EAAA,CAAAE,UAAA,YAAA2Z,GAAA,CAAAjS,iBAAA,CAA6B;UACzC5H,EAAA,CAAAwC,gBAAA,YAAAqX,GAAA,CAAA5V,gBAAA,CAA8B;UAelCjE,EAAA,CAAAY,SAAA,EAA2E;UAA3EZ,EAAA,CAAAE,UAAA,YAAAF,EAAA,CAAAqF,eAAA,KAAA0V,GAAA,GAAAlB,GAAA,CAAAxP,kBAAA,KAAAwP,GAAA,CAAAvR,MAAA,wBAA2E;UAG8CtI,EAAA,CAAAY,SAAA,GAAkC;UAChGZ,EAD8D,CAAAE,UAAA,uBAAA2Z,GAAA,CAAAnW,WAAA,CAAkC,yBAC3H,0BAA0B,WAAAmW,GAAA,CAAA7R,oBAAA,CAAgC;UAkBpFhI,EAAA,CAAAY,SAAA,IAAkB;UAAlBZ,EAAA,CAAAE,UAAA,SAAA2Z,GAAA,CAAApU,YAAA,CAAkB;UAgEZzF,EAAA,CAAAY,SAAA,IACJ;UADIZ,EAAA,CAAAqB,kBAAA,MAAAwY,GAAA,CAAApK,sBAAA,QACJ;UAI4BzP,EAAA,CAAAY,SAAA,GAAW;UAAXZ,EAAA,CAAAE,UAAA,YAAA2Z,GAAA,CAAA/S,QAAA,CAAW;UAOH9G,EAAA,CAAAY,SAAA,GAAyB;UAAzBZ,EAAA,CAAAgb,UAAA,CAAAhb,EAAA,CAAAib,eAAA,KAAAC,GAAA,EAAyB;UAElDlb,EAAA,CAAAY,SAAA,GAAuB;UAAvBZ,EAAA,CAAAE,UAAA,SAAA2Z,GAAA,CAAAtR,iBAAA,CAAuB;UAcEvI,EAAA,CAAAY,SAAA,GAAW;UAAXZ,EAAA,CAAAE,UAAA,YAAA2Z,GAAA,CAAA/S,QAAA,CAAW;;;;;;;SDjJ9Df,sBAAsB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}