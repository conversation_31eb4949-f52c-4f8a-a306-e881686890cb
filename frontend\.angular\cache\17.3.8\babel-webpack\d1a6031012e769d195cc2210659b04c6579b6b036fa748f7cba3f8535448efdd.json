{"ast": null, "code": "import { EventEmitter, inject, Injector, input, signal } from '@angular/core';\nimport { toObservable } from \"@angular/core/rxjs-interop\";\nimport { PackagesModel, PackageType } from \"src/app/core/models/package.model\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"primeng/radiobutton\";\nconst _c0 = (a0, a1) => ({\n  \"surface-border\": a0,\n  \"border-primary\": a1\n});\nfunction BuyPackageSuggestionBoxComponent_div_0_ng_container_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 35);\n    i0.ɵɵlistener(\"click\", function BuyPackageSuggestionBoxComponent_div_0_ng_container_20_Template_div_click_1_listener() {\n      const pkg_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.selectPackageType(pkg_r4.type));\n    });\n    i0.ɵɵelementStart(2, \"p-radioButton\", 36);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuyPackageSuggestionBoxComponent_div_0_ng_container_20_Template_p_radioButton_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedPackageType, $event) || (ctx_r1.selectedPackageType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 37)(4, \"div\", 38);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const pkg_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c0, ctx_r1.selectedPackageType !== pkg_r4.type, ctx_r1.selectedPackage === pkg_r4.type));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", pkg_r4.type);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedPackageType);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(pkg_r4.type);\n  }\n}\nfunction BuyPackageSuggestionBoxComponent_div_0_ng_container_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" Switch to \", (tmp_3_0 = ctx_r1.getSelectedPackage()) == null ? null : tmp_3_0.type, \" Package \");\n  }\n}\nfunction BuyPackageSuggestionBoxComponent_div_0_ng_template_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Go Premium for the Ultimate Learning Experience \");\n  }\n}\nfunction BuyPackageSuggestionBoxComponent_div_0_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Expires in +\", (tmp_3_0 = ctx_r1.getSelectedPackage()) == null ? null : tmp_3_0.expiresPlus, \"\");\n  }\n}\nfunction BuyPackageSuggestionBoxComponent_div_0_span_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Exclusive Access to our \");\n    i0.ɵɵelementStart(2, \"span\", 12);\n    i0.ɵɵtext(3, \"AI Chat\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuyPackageSuggestionBoxComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"div\", 3)(3, \"div\", 4)(4, \"div\", 5)(5, \"div\", 6)(6, \"span\", 7);\n    i0.ɵɵelement(7, \"img\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 9)(9, \"span\", 10);\n    i0.ɵɵtext(10, \"Consider upgrading?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 11);\n    i0.ɵɵtext(12, \"Unlock Exclusive Access to our \");\n    i0.ɵɵelementStart(13, \"span\", 12);\n    i0.ɵɵtext(14, \"AI Chat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \", your personal learning assistant! \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function BuyPackageSuggestionBoxComponent_div_0_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.show.set(false));\n    });\n    i0.ɵɵelement(17, \"span\", 14)(18, \"span\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 16);\n    i0.ɵɵtemplate(20, BuyPackageSuggestionBoxComponent_div_0_ng_container_20_Template, 6, 7, \"ng-container\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 18)(22, \"div\", 19)(23, \"div\", 20);\n    i0.ɵɵtemplate(24, BuyPackageSuggestionBoxComponent_div_0_ng_container_24_Template, 2, 1, \"ng-container\", 21)(25, BuyPackageSuggestionBoxComponent_div_0_ng_template_25_Template, 1, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 22)(28, \"span\", 23);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"div\", 24)(31, \"div\", 25)(32, \"ul\", 26)(33, \"li\", 27);\n    i0.ɵɵelement(34, \"img\", 28);\n    i0.ɵɵtemplate(35, BuyPackageSuggestionBoxComponent_div_0_span_35_Template, 2, 1, \"span\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"li\", 27);\n    i0.ɵɵelement(37, \"img\", 28);\n    i0.ɵɵelementStart(38, \"span\");\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"li\", 27);\n    i0.ɵɵelement(41, \"img\", 28);\n    i0.ɵɵtemplate(42, BuyPackageSuggestionBoxComponent_div_0_span_42_Template, 4, 0, \"span\", 29);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(43, \"div\", 30)(44, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function BuyPackageSuggestionBoxComponent_div_0_Template_button_click_44_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.switchBackToPackageType());\n    });\n    i0.ɵɵelement(45, \"span\", 32);\n    i0.ɵɵelementStart(46, \"span\", 33);\n    i0.ɵɵtext(47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(48, \"span\", 34);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    let tmp_8_0;\n    let tmp_9_0;\n    let tmp_10_0;\n    const notFlexFirstTitle_r5 = i0.ɵɵreference(26);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedPackageType, \" \");\n    i0.ɵɵadvance(19);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getPackages());\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r1.getSelectedPackage()) == null ? null : tmp_4_0.type) === ctx_r1.packageTypeEnum.FLEX)(\"ngIfElse\", notFlexFirstTitle_r5);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"+\", (tmp_6_0 = ctx_r1.getSelectedPackage()) == null ? null : tmp_6_0.costPlus, \"\\u20AC\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx_r1.getSelectedPackage()) == null ? null : tmp_7_0.type) != \"Regular\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_8_0 = ctx_r1.getSelectedPackage()) == null ? null : tmp_8_0.cancelation, \" hours cancellation policy \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_9_0 = ctx_r1.getSelectedPackage()) == null ? null : tmp_9_0.type) != \"Regular\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"Switch to \", (tmp_10_0 = ctx_r1.getSelectedPackage()) == null ? null : tmp_10_0.type, \"\");\n  }\n}\nexport class BuyPackageSuggestionBoxComponent {\n  constructor() {\n    this.show = signal(true);\n    this.defaultPackages = PackagesModel.getDefaultPackages();\n    this.injector = inject(Injector);\n    this.preselectedPackage = input(this.defaultPackages[1]);\n    this.switchBackToPackageTypeSelected = new EventEmitter();\n    this.selectedPackageType = this.defaultPackages[1].type;\n    this.packageTypeEnum = PackageType;\n    this.filterOutPackageTypes = [this.packageTypeEnum.REGULAR];\n  }\n  ngOnInit() {\n    this.listenChatListsLoaded();\n  }\n  listenChatListsLoaded() {\n    toObservable(this.preselectedPackage, {\n      injector: this.injector\n    }).subscribe({\n      next: preselectedPackage => {\n        console.log(preselectedPackage);\n        switch (preselectedPackage.type) {\n          case this.packageTypeEnum.REGULAR:\n            this.filterOutPackageTypes = [this.packageTypeEnum.REGULAR];\n            break;\n          case this.packageTypeEnum.FLEX:\n            this.filterOutPackageTypes = [this.packageTypeEnum.REGULAR, this.packageTypeEnum.FLEX];\n            break;\n          case this.packageTypeEnum.PREMIUM:\n            this.filterOutPackageTypes = [this.packageTypeEnum.REGULAR, this.packageTypeEnum.FLEX];\n            break;\n          default:\n        }\n        if (preselectedPackage && preselectedPackage.type !== this.packageTypeEnum.REGULAR) {\n          this.selectedPackageType = preselectedPackage.type;\n          setTimeout(() => {\n            this.switchBackToPackageTypeSelected.emit(preselectedPackage);\n          }, 10);\n        }\n      }\n    });\n  }\n  /**\n   * Updates the selected package type by setting `selectedPackageType` to the given type.\n   * @param type The package type to select.\n   */\n  selectPackageType(type) {\n    this.selectedPackageType = type;\n  }\n  getPackages(types) {\n    const defaultPackages = this.defaultPackages;\n    return defaultPackages.filter(p => !this.filterOutPackageTypes.includes(p.type));\n  }\n  getSelectedPackage() {\n    return this.defaultPackages.find(pkg => pkg.type === this.selectedPackageType);\n  }\n  switchBackToPackageType() {\n    this.switchBackToPackageTypeSelected.emit(this.getSelectedPackage());\n  }\n  static #_ = this.ɵfac = function BuyPackageSuggestionBoxComponent_Factory(t) {\n    return new (t || BuyPackageSuggestionBoxComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: BuyPackageSuggestionBoxComponent,\n    selectors: [[\"app-buy-package-suggestion-box\"]],\n    inputs: {\n      preselectedPackage: [i0.ɵɵInputFlags.SignalBased, \"preselectedPackage\"]\n    },\n    outputs: {\n      switchBackToPackageTypeSelected: \"switchBackToPackageTypeSelected\"\n    },\n    decls: 1,\n    vars: 1,\n    consts: [[\"notFlexFirstTitle\", \"\"], [\"class\", \"\", 4, \"ngIf\"], [1, \"\"], [1, \"shadow-2\", \"border-round\", \"mb-3\", \"h-full\", \"flex-column\", \"justify-content-between\", \"flex\", \"border-round-lg\", \"border-2\", \"bg-blue-50\", \"border-blue-500\"], [1, \"p-3\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"line-height-1\", \"mb-3\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"inline-flex\", \"border-circle\", \"align-self-start\", \"align-items-start\", \"justify-content-center\", \"bg-green-100\", \"mr-3\"], [\"src\", \"/assets/icons/switch-premium.png\", 1, \"w-2rem\"], [1, \"flex\", \"flex-column\", \"gap-2\"], [1, \"text-900\", \"font-medium\", \"text-2xl\"], [1, \"text-sm\"], [1, \"font-semibold\"], [\"pbutton\", \"\", \"pripple\", \"\", \"type\", \"button\", \"icon\", \"pi pi-times text-sm\", 1, \"p-element\", \"p-ripple\", \"align-self-start\", \"w-2rem\", \"h-2rem\", \"p-button-rounded\", \"p-button-secondary\", \"p-button-text\", \"text-600\", \"p-button\", \"p-component\", \"p-button-icon-only\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"p-button-icon\", \"pi\", \"pi-times\", \"text-sm\"], [1, \"p-ink\", 2, \"height\", \"32px\", \"width\", \"32px\", \"top\", \"-5px\", \"left\", \"9.8125px\"], [1, \"flex\", \"flex-column\", \"sm:flex-row\", \"align-items-center\", \"justify-content-between\", \"gap-2\"], [4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"flex-column\", \"justify-content-between\", \"md:flex-row\", \"mt-3\"], [1, \"flex-grow-1\", \"lg:w-min\"], [1, \"text-900\", \"font-bold\", \"text-xl\", \"mb-2\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"flex\", \"align-items-center\", \"w-auto\", \"lg:w-10rem\", \"justify-content-center\", \"lg:justify-content-end\", \"my-3\", \"lg:mt-0\"], [1, \"font-bold\", \"text-2xl\", \"price-blue\"], [1, \"flex\", \"flex-column\", \"align-items-center\", \"justify-space-between\", \"md:flex-row\"], [1, \"flex\", \"flex-column\", \"justify-space-between\", \"md:flex-row\"], [1, \"list-none\", \"p-0\", \"m-0\", \"flex-grow-1\", \"font-sm\", \"font-medium\"], [1, \"flex\", \"align-items-center\", \"my-1\"], [\"src\", \"/assets/images/dashboard/check.svg\", \"alt\", \"\", 1, \"w-1rem\", \"mr-2\"], [4, \"ngIf\"], [1, \"mb-1\", \"mt-2\", \"sm:mt-0\", \"sm:ml-auto\", \"text-right\", \"border-round-lg\", \"align-self-center\", \"sm:align-self-end\"], [\"pbutton\", \"\", \"pripple\", \"\", \"type\", \"button\", \"icon\", \"pi pi-arrow-right\", \"iconpos\", \"right\", \"label\", \"More\", 1, \"p-element\", \"p-ripple\", \"p-button-rounded\", \"blue-gradient-btn\", \"p-button\", \"p-component\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"p-button-icon\", \"p-button-icon-right\", \"pi\", \"pi-arrow-right\"], [1, \"p-button-label\"], [1, \"p-ink\"], [1, \"surface-card\", \"border-round-lg\", \"w-full\", \"mx-1\", \"border-2\", \"p-1\", \"line-height-1\", \"flex\", \"align-items-center\", \"cursor-pointer\", \"border-blue-500\", 3, \"click\", \"ngClass\"], [\"name\", \"packageType\", \"styleClass\", \"mr-3\", 3, \"ngModelChange\", \"value\", \"ngModel\"], [1, \"mr-4\", \"md:mr-8\", \"line-height-1\"], [1, \"font-medium\", \"text-base\"]],\n    template: function BuyPackageSuggestionBoxComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, BuyPackageSuggestionBoxComponent_div_0_Template, 49, 9, \"div\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.show());\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i2.NgControlStatus, i2.NgModel, i3.RadioButton],\n    styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n}\\n\\n.price-blue[_ngcontent-%COMP%] {\\n  color: #3345A7;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9jbGFzc3Jvb20vcGFja2FnZXMvYnV5LXBhY2thZ2UvYnV5LXBhY2thZ2Utc3VnZ2VzdGlvbi1ib3gvYnV5LXBhY2thZ2Utc3VnZ2VzdGlvbi1ib3guY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxjQUFjO0FBQ2hCIiwic291cmNlc0NvbnRlbnQiOlsiOmhvc3Qge1xyXG4gIGRpc3BsYXk6IGJsb2NrO1xyXG59XHJcblxyXG4ucHJpY2UtYmx1ZSB7XHJcbiAgY29sb3I6ICMzMzQ1QTc7XHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"],\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "inject", "Injector", "input", "signal", "toObservable", "PackagesModel", "PackageType", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "BuyPackageSuggestionBoxComponent_div_0_ng_container_20_Template_div_click_1_listener", "pkg_r4", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "selectPackageType", "type", "ɵɵtwoWayListener", "BuyPackageSuggestionBoxComponent_div_0_ng_container_20_Template_p_radioButton_ngModelChange_2_listener", "$event", "ɵɵtwoWayBindingSet", "selectedPackageType", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "selected<PERSON><PERSON><PERSON>", "ɵɵtwoWayProperty", "ɵɵtextInterpolate", "ɵɵtextInterpolate1", "tmp_3_0", "getSelectedPackage", "expiresPlus", "ɵɵelement", "BuyPackageSuggestionBoxComponent_div_0_Template_button_click_16_listener", "_r1", "show", "set", "ɵɵtemplate", "BuyPackageSuggestionBoxComponent_div_0_ng_container_20_Template", "BuyPackageSuggestionBoxComponent_div_0_ng_container_24_Template", "BuyPackageSuggestionBoxComponent_div_0_ng_template_25_Template", "ɵɵtemplateRefExtractor", "BuyPackageSuggestionBoxComponent_div_0_span_35_Template", "BuyPackageSuggestionBoxComponent_div_0_span_42_Template", "BuyPackageSuggestionBoxComponent_div_0_Template_button_click_44_listener", "switchBackToPackageType", "getPackages", "tmp_4_0", "packageTypeEnum", "FLEX", "notFlexFirstTitle_r5", "tmp_6_0", "costPlus", "tmp_7_0", "tmp_8_0", "cancelation", "tmp_9_0", "tmp_10_0", "BuyPackageSuggestionBoxComponent", "constructor", "defaultPackages", "getDefaultPackages", "injector", "preselectedPackage", "switchBackToPackageTypeSelected", "filterOutPackageTypes", "REGULAR", "ngOnInit", "listenChatListsLoaded", "subscribe", "next", "console", "log", "PREMIUM", "setTimeout", "emit", "types", "filter", "p", "includes", "find", "pkg", "_", "_2", "selectors", "inputs", "ɵɵInputFlags", "SignalBased", "outputs", "decls", "vars", "consts", "template", "BuyPackageSuggestionBoxComponent_Template", "rf", "ctx", "BuyPackageSuggestionBoxComponent_div_0_Template"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\buy-package\\buy-package-suggestion-box\\buy-package-suggestion-box.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\buy-package\\buy-package-suggestion-box\\buy-package-suggestion-box.component.html"], "sourcesContent": ["import { CommonModule } from \"@angular/common\";\r\nimport { ChangeDetectionStrategy, Component, EventEmitter, inject, Injector, input, Input, Output, signal } from '@angular/core';\r\nimport { toObservable } from \"@angular/core/rxjs-interop\";\r\nimport { PackagesModel, PackageType } from \"src/app/core/models/package.model\";\r\n\r\n@Component({\r\n    selector: 'app-buy-package-suggestion-box',\r\n    templateUrl: './buy-package-suggestion-box.component.html',\r\n    styleUrl: './buy-package-suggestion-box.component.css',\r\n    changeDetection: ChangeDetectionStrategy.OnPush,\r\n})\r\nexport class BuyPackageSuggestionBoxComponent {\r\n\r\n    show = signal(true);\r\n    public defaultPackages = PackagesModel.getDefaultPackages();\r\n    private injector = inject(Injector);\r\n\r\n    preselectedPackage = input(this.defaultPackages[1]);\r\n    @Output() switchBackToPackageTypeSelected = new EventEmitter<any>();\r\n\r\n    selectedPackageType = this.defaultPackages[1].type;\r\n    packageTypeEnum = PackageType;\r\n\r\n    filterOutPackageTypes = [this.packageTypeEnum.REGULAR];\r\n\r\n    ngOnInit() {\r\n        this.listenChatListsLoaded();\r\n    }\r\n\r\n\r\n    private listenChatListsLoaded() {\r\n        toObservable(this.preselectedPackage, {\r\n            injector: this.injector\r\n        }).subscribe({\r\n            next: (preselectedPackage) => {\r\n                console.log(preselectedPackage);\r\n\r\n                switch (preselectedPackage.type) {\r\n                    case this.packageTypeEnum.REGULAR:\r\n                        this.filterOutPackageTypes = [this.packageTypeEnum.REGULAR];\r\n                        break;\r\n                    case this.packageTypeEnum.FLEX:\r\n                        this.filterOutPackageTypes = [this.packageTypeEnum.REGULAR, this.packageTypeEnum.FLEX];\r\n                        break;\r\n                    case this.packageTypeEnum.PREMIUM:\r\n                        this.filterOutPackageTypes = [this.packageTypeEnum.REGULAR, this.packageTypeEnum.FLEX];\r\n                        break;\r\n                    default:\r\n                }\r\n\r\n\r\n                if (preselectedPackage && preselectedPackage.type !== this.packageTypeEnum.REGULAR) {\r\n                    this.selectedPackageType = preselectedPackage.type;\r\n                    setTimeout(() => {\r\n                        this.switchBackToPackageTypeSelected.emit(preselectedPackage);\r\n                    }, 10);\r\n\r\n                }\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Updates the selected package type by setting `selectedPackageType` to the given type.\r\n     * @param type The package type to select.\r\n     */\r\n\r\n    public selectPackageType(type: any) {\r\n        this.selectedPackageType = type;\r\n    }\r\n\r\n    getPackages(types: string[]) {\r\n        const defaultPackages = this.defaultPackages;\r\n        return defaultPackages.filter(p => !this.filterOutPackageTypes.includes(p.type));\r\n    }\r\n\r\n    public getSelectedPackage() {\r\n        return this.defaultPackages.find(pkg => pkg.type === this.selectedPackageType);\r\n    }\r\n\r\n    switchBackToPackageType() {\r\n        this.switchBackToPackageTypeSelected.emit(this.getSelectedPackage());\r\n    }\r\n}\r\n", "<div class=\"\" *ngIf=\"show()\">\r\n\r\n    {{selectedPackageType}}\r\n    <div class=\"shadow-2 border-round  mb-3 h-full flex-column justify-content-between flex border-round-lg border-2 \r\n    bg-blue-50 border-blue-500\">\r\n        <div class=\"p-3\">\r\n            <div class=\"flex align-items-center justify-content-between line-height-1 mb-3\">\r\n                <div class=\"flex align-items-center justify-content-center \">\r\n                    <span class=\"inline-flex border-circle align-self-start align-items-start justify-content-center bg-green-100 mr-3\">\r\n                        <img src=\"/assets/icons/switch-premium.png\" class=\"w-2rem\">\r\n                    </span>\r\n                    <div class=\"flex flex-column gap-2\">\r\n                        <span class=\"text-900 font-medium text-2xl\">Consider upgrading?</span>\r\n                        <span class=\"text-sm\">Unlock Exclusive Access to our <span\r\n                            class=\"font-semibold\">AI Chat</span>, your personal learning\r\n                            assistant! </span>\r\n                    </div>\r\n                </div>\r\n\r\n                <button pbutton=\"\" (click)=\"show.set(false)\" pripple=\"\" type=\"button\" icon=\"pi pi-times text-sm\"\r\n                    class=\"p-element p-ripple align-self-start w-2rem h-2rem p-button-rounded p-button-secondary p-button-text text-600 p-button p-component p-button-icon-only\"><span\r\n                        class=\"p-button-icon pi pi-times text-sm\" aria-hidden=\"true\"></span><span class=\"p-ink\"\r\n                        style=\"height: 32px; width: 32px; top: -5px; left: 9.8125px;\"></span></button>\r\n            </div>\r\n\r\n\r\n\r\n            <div class=\"flex flex-column sm:flex-row align-items-center justify-content-between gap-2\">\r\n                <ng-container *ngFor=\"let pkg of getPackages(); let i = index\">\r\n                    <div class=\"surface-card border-round-lg w-full mx-1 border-2 p-1 line-height-1 flex \r\n                    align-items-center cursor-pointer border-blue-500\"\r\n                        [ngClass]=\"{'surface-border': selectedPackageType !== pkg.type, 'border-primary': selectedPackage === pkg.type}\"\r\n                        (click)=\"selectPackageType(pkg.type)\">\r\n                        <p-radioButton name=\"packageType\" [value]=\"pkg.type\" [(ngModel)]=\"selectedPackageType\"\r\n                            styleClass=\"mr-3\"></p-radioButton>\r\n                        <div class=\"mr-4 md:mr-8 line-height-1\">\r\n                            <div class=\"font-medium text-base\">{{ pkg.type }}</div>\r\n                            <!-- <span class=\"text-sm\">Cost: {{ pkg.costPlus }} USD</span> -->\r\n                        </div>\r\n                        <!-- <div class=\"border-round border-1 p-1 ml-auto flex w-min\"\r\n                            [ngClass]=\"{'bg-green-400 text-green-900': selectedPackageType !== pkg.type, 'bg-primary-reverse': selectedPackageType === pkg.type}\">\r\n                            \r\n                            <span class=\" font-bold font-2xs py-1 px-2\" style=\"border-radius: 10px;\">Best Value</span>\r\n                        </div> -->\r\n                    </div>\r\n                </ng-container>\r\n            </div>\r\n\r\n            <div class=\"flex flex-column justify-content-between md:flex-row mt-3\">\r\n                <div class=\"flex-grow-1 lg:w-min\">\r\n                    <div class=\"text-900 font-bold text-xl mb-2\">\r\n\r\n                        <ng-container\r\n                            *ngIf=\"getSelectedPackage()?.type === packageTypeEnum.FLEX; else notFlexFirstTitle\">\r\n\r\n                            Switch to {{ getSelectedPackage()?.type }} Package\r\n\r\n                        </ng-container>\r\n\r\n                        <ng-template #notFlexFirstTitle>\r\n                            Go Premium for the Ultimate Learning Experience\r\n                        </ng-template>\r\n\r\n                    </div>\r\n                    <!-- <div class=\"text-500 text-xl\">Id diam vel quam elementum.</div> -->\r\n                </div>\r\n                <div\r\n                    class=\"flex align-items-center w-auto lg:w-10rem justify-content-center lg:justify-content-end my-3 lg:mt-0\">\r\n                    <span class=\"font-bold text-2xl price-blue\">+{{ getSelectedPackage()?.costPlus }}€</span>\r\n                    <!-- <span class=\"text-700 text-xl ml-2 line-height-3\">per month</span> -->\r\n                </div>\r\n            </div>\r\n\r\n            <!-- <div class=\"text-900 my-3 text-xl font-medium\">{{getSelectedPackage()?.costPlus}} €.</div>\r\n            <p class=\"mt-0 mb-3 text-700 line-height-3\">\r\n                Eget sit amet tellus cras adipiscing enim. At quis risus sed vulputate odio. Proin libero nunc consequat\r\n                interdum varius sit amet.\r\n            </p> -->\r\n\r\n            <!-- <div class=\"text-900 font-medium text-xl pb-1\">Features</div> -->\r\n\r\n\r\n            <div class=\"flex flex-column align-items-center justify-space-between md:flex-row\">\r\n\r\n                <div class=\"flex flex-column justify-space-between md:flex-row\">\r\n                    <ul class=\"list-none p-0 m-0 flex-grow-1 font-sm font-medium\">\r\n                        <li class=\"flex align-items-center my-1\"><img src=\"/assets/images/dashboard/check.svg\" alt=\"\"\r\n                                class=\"w-1rem mr-2\">\r\n                            <span *ngIf=\"getSelectedPackage()?.type!='Regular'\">Expires in\r\n                                +{{getSelectedPackage()?.expiresPlus}}</span>\r\n                        </li>\r\n                        <li class=\"flex align-items-center my-1\"><img src=\"/assets/images/dashboard/check.svg\" alt=\"\"\r\n                                class=\"w-1rem mr-2\">\r\n                            <span>\r\n                                {{getSelectedPackage()?.cancelation}} hours cancellation policy\r\n                            </span>\r\n                        </li>\r\n                        <li class=\"flex align-items-center my-1\"><img src=\"/assets/images/dashboard/check.svg\" alt=\"\"\r\n                                class=\"w-1rem mr-2\">\r\n                            <span *ngIf=\"getSelectedPackage()?.type!='Regular'\">Exclusive Access to our <span\r\n                                    class=\"font-semibold\">AI Chat</span></span>\r\n                        </li>\r\n                    </ul>\r\n                </div>\r\n\r\n                <div\r\n                    class=\"mb-1 mt-2 sm:mt-0 sm:ml-auto text-right border-round-lg align-self-center sm:align-self-end\">\r\n                    <button pbutton=\"\" pripple=\"\" (click)=\"switchBackToPackageType()\" type=\"button\"\r\n                        icon=\"pi pi-arrow-right\" iconpos=\"right\" label=\"More\"\r\n                        class=\"p-element p-ripple p-button-rounded blue-gradient-btn p-button p-component\">\r\n                        <span class=\"p-button-icon p-button-icon-right pi pi-arrow-right\" aria-hidden=\"true\"></span>\r\n                        <span class=\"p-button-label\">Switch to {{ getSelectedPackage()?.type }}</span>\r\n                        <span class=\"p-ink\"></span>\r\n                    </button>\r\n                </div>\r\n\r\n            </div>\r\n\r\n        </div>\r\n\r\n    </div>\r\n</div>"], "mappings": "AACA,SAA6CA,YAAY,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAiBC,MAAM,QAAQ,eAAe;AAChI,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,aAAa,EAAEC,WAAW,QAAQ,mCAAmC;;;;;;;;;;;;ICyB9DC,EAAA,CAAAC,uBAAA,GAA+D;IAC3DD,EAAA,CAAAE,cAAA,cAG0C;IAAtCF,EAAA,CAAAG,UAAA,mBAAAC,qFAAA;MAAA,MAAAC,MAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,iBAAA,CAAAP,MAAA,CAAAQ,IAAA,CAA2B;IAAA,EAAC;IACrCb,EAAA,CAAAE,cAAA,wBACsB;IAD+BF,EAAA,CAAAc,gBAAA,2BAAAC,uGAAAC,MAAA;MAAAhB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAiB,kBAAA,CAAAR,MAAA,CAAAS,mBAAA,EAAAF,MAAA,MAAAP,MAAA,CAAAS,mBAAA,GAAAF,MAAA;MAAA,OAAAhB,EAAA,CAAAW,WAAA,CAAAK,MAAA;IAAA,EAAiC;IAChEhB,EAAA,CAAAmB,YAAA,EAAgB;IAElCnB,EADJ,CAAAE,cAAA,cAAwC,cACD;IAAAF,EAAA,CAAAoB,MAAA,GAAc;IAQzDpB,EARyD,CAAAmB,YAAA,EAAM,EAErD,EAMJ;;;;;;IAbFnB,EAAA,CAAAqB,SAAA,EAAgH;IAAhHrB,EAAA,CAAAsB,UAAA,YAAAtB,EAAA,CAAAuB,eAAA,IAAAC,GAAA,EAAAf,MAAA,CAAAS,mBAAA,KAAAb,MAAA,CAAAQ,IAAA,EAAAJ,MAAA,CAAAgB,eAAA,KAAApB,MAAA,CAAAQ,IAAA,EAAgH;IAE9Eb,EAAA,CAAAqB,SAAA,EAAkB;IAAlBrB,EAAA,CAAAsB,UAAA,UAAAjB,MAAA,CAAAQ,IAAA,CAAkB;IAACb,EAAA,CAAA0B,gBAAA,YAAAjB,MAAA,CAAAS,mBAAA,CAAiC;IAG/ClB,EAAA,CAAAqB,SAAA,GAAc;IAAdrB,EAAA,CAAA2B,iBAAA,CAAAtB,MAAA,CAAAQ,IAAA,CAAc;;;;;IAgBrDb,EAAA,CAAAC,uBAAA,GACwF;IAEpFD,EAAA,CAAAoB,MAAA,GAEJ;;;;;;IAFIpB,EAAA,CAAAqB,SAAA,EAEJ;IAFIrB,EAAA,CAAA4B,kBAAA,iBAAAC,OAAA,GAAApB,MAAA,CAAAqB,kBAAA,qBAAAD,OAAA,CAAAhB,IAAA,cAEJ;;;;;IAGIb,EAAA,CAAAoB,MAAA,wDACJ;;;;;IA2BIpB,EAAA,CAAAE,cAAA,WAAoD;IAAAF,EAAA,CAAAoB,MAAA,GACV;IAAApB,EAAA,CAAAmB,YAAA,EAAO;;;;;IADGnB,EAAA,CAAAqB,SAAA,EACV;IADUrB,EAAA,CAAA4B,kBAAA,kBAAAC,OAAA,GAAApB,MAAA,CAAAqB,kBAAA,qBAAAD,OAAA,CAAAE,WAAA,KACV;;;;;IAU1C/B,EAAA,CAAAE,cAAA,WAAoD;IAAAF,EAAA,CAAAoB,MAAA,+BAAwB;IAAApB,EAAA,CAAAE,cAAA,eAC9C;IAAAF,EAAA,CAAAoB,MAAA,cAAO;IAAOpB,EAAP,CAAAmB,YAAA,EAAO,EAAO;;;;;;IApG/EnB,EAAA,CAAAE,cAAA,aAA6B;IAEzBF,EAAA,CAAAoB,MAAA,GACA;IAKgBpB,EALhB,CAAAE,cAAA,aAC4B,aACP,aACmE,aACf,cAC2D;IAChHF,EAAA,CAAAgC,SAAA,aAA2D;IAC/DhC,EAAA,CAAAmB,YAAA,EAAO;IAEHnB,EADJ,CAAAE,cAAA,aAAoC,eACY;IAAAF,EAAA,CAAAoB,MAAA,2BAAmB;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IACtEnB,EAAA,CAAAE,cAAA,gBAAsB;IAAAF,EAAA,CAAAoB,MAAA,uCAA+B;IAAApB,EAAA,CAAAE,cAAA,gBAC3B;IAAAF,EAAA,CAAAoB,MAAA,eAAO;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAAAnB,EAAA,CAAAoB,MAAA,4CACzB;IAEvBpB,EAFuB,CAAAmB,YAAA,EAAO,EACpB,EACJ;IAENnB,EAAA,CAAAE,cAAA,kBACiK;IAD9IF,EAAA,CAAAG,UAAA,mBAAA8B,yEAAA;MAAAjC,EAAA,CAAAM,aAAA,CAAA4B,GAAA;MAAA,MAAAzB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA0B,IAAA,CAAAC,GAAA,CAAS,KAAK,CAAC;IAAA,EAAC;IAEgCpC,EADqF,CAAAgC,SAAA,gBACrF,gBACC;IACjFhC,EADiF,CAAAmB,YAAA,EAAS,EACpF;IAINnB,EAAA,CAAAE,cAAA,eAA2F;IACvFF,EAAA,CAAAqC,UAAA,KAAAC,+DAAA,2BAA+D;IAkBnEtC,EAAA,CAAAmB,YAAA,EAAM;IAIEnB,EAFR,CAAAE,cAAA,eAAuE,eACjC,eACe;IASzCF,EAPA,CAAAqC,UAAA,KAAAE,+DAAA,2BACwF,KAAAC,8DAAA,gCAAAxC,EAAA,CAAAyC,sBAAA,CAMxD;IAMxCzC,EAFI,CAAAmB,YAAA,EAAM,EAEJ;IAGFnB,EAFJ,CAAAE,cAAA,eACiH,gBACjE;IAAAF,EAAA,CAAAoB,MAAA,IAAsC;IAG1FpB,EAH0F,CAAAmB,YAAA,EAAO,EAEvF,EACJ;IAeMnB,EAJZ,CAAAE,cAAA,eAAmF,eAEf,cACE,cACjB;IAAAF,EAAA,CAAAgC,SAAA,eACb;IACxBhC,EAAA,CAAAqC,UAAA,KAAAK,uDAAA,mBAAoD;IAExD1C,EAAA,CAAAmB,YAAA,EAAK;IACLnB,EAAA,CAAAE,cAAA,cAAyC;IAAAF,EAAA,CAAAgC,SAAA,eACb;IACxBhC,EAAA,CAAAE,cAAA,YAAM;IACFF,EAAA,CAAAoB,MAAA,IACJ;IACJpB,EADI,CAAAmB,YAAA,EAAO,EACN;IACLnB,EAAA,CAAAE,cAAA,cAAyC;IAAAF,EAAA,CAAAgC,SAAA,eACb;IACxBhC,EAAA,CAAAqC,UAAA,KAAAM,uDAAA,mBAAoD;IAIhE3C,EAFQ,CAAAmB,YAAA,EAAK,EACJ,EACH;IAIFnB,EAFJ,CAAAE,cAAA,eACwG,kBAGb;IAFzDF,EAAA,CAAAG,UAAA,mBAAAyC,yEAAA;MAAA5C,EAAA,CAAAM,aAAA,CAAA4B,GAAA;MAAA,MAAAzB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAoC,uBAAA,EAAyB;IAAA,EAAC;IAG7D7C,EAAA,CAAAgC,SAAA,gBAA4F;IAC5FhC,EAAA,CAAAE,cAAA,gBAA6B;IAAAF,EAAA,CAAAoB,MAAA,IAA0C;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAC9EnB,EAAA,CAAAgC,SAAA,gBAA2B;IASnDhC,EARoB,CAAAmB,YAAA,EAAS,EACP,EAEJ,EAEJ,EAEJ,EACJ;;;;;;;;;;;IAvHFnB,EAAA,CAAAqB,SAAA,EACA;IADArB,EAAA,CAAA4B,kBAAA,MAAAnB,MAAA,CAAAS,mBAAA,MACA;IAyB0ClB,EAAA,CAAAqB,SAAA,IAAkB;IAAlBrB,EAAA,CAAAsB,UAAA,YAAAb,MAAA,CAAAqC,WAAA,GAAkB;IAyBnC9C,EAAA,CAAAqB,SAAA,GAA2D;IAAArB,EAA3D,CAAAsB,UAAA,WAAAyB,OAAA,GAAAtC,MAAA,CAAAqB,kBAAA,qBAAAiB,OAAA,CAAAlC,IAAA,MAAAJ,MAAA,CAAAuC,eAAA,CAAAC,IAAA,CAA2D,aAAAC,oBAAA,CAAsB;IAe9ClD,EAAA,CAAAqB,SAAA,GAAsC;IAAtCrB,EAAA,CAAA4B,kBAAA,OAAAuB,OAAA,GAAA1C,MAAA,CAAAqB,kBAAA,qBAAAqB,OAAA,CAAAC,QAAA,WAAsC;IAoBnEpD,EAAA,CAAAqB,SAAA,GAA2C;IAA3CrB,EAAA,CAAAsB,UAAA,WAAA+B,OAAA,GAAA5C,MAAA,CAAAqB,kBAAA,qBAAAuB,OAAA,CAAAxC,IAAA,eAA2C;IAM9Cb,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA4B,kBAAA,OAAA0B,OAAA,GAAA7C,MAAA,CAAAqB,kBAAA,qBAAAwB,OAAA,CAAAC,WAAA,gCACJ;IAIOvD,EAAA,CAAAqB,SAAA,GAA2C;IAA3CrB,EAAA,CAAAsB,UAAA,WAAAkC,OAAA,GAAA/C,MAAA,CAAAqB,kBAAA,qBAAA0B,OAAA,CAAA3C,IAAA,eAA2C;IAYzBb,EAAA,CAAAqB,SAAA,GAA0C;IAA1CrB,EAAA,CAAA4B,kBAAA,gBAAA6B,QAAA,GAAAhD,MAAA,CAAAqB,kBAAA,qBAAA2B,QAAA,CAAA5C,IAAA,KAA0C;;;ADpG/F,OAAM,MAAO6C,gCAAgC;EAN7CC,YAAA;IAQI,KAAAxB,IAAI,GAAGvC,MAAM,CAAC,IAAI,CAAC;IACZ,KAAAgE,eAAe,GAAG9D,aAAa,CAAC+D,kBAAkB,EAAE;IACnD,KAAAC,QAAQ,GAAGrE,MAAM,CAACC,QAAQ,CAAC;IAEnC,KAAAqE,kBAAkB,GAAGpE,KAAK,CAAC,IAAI,CAACiE,eAAe,CAAC,CAAC,CAAC,CAAC;IACzC,KAAAI,+BAA+B,GAAG,IAAIxE,YAAY,EAAO;IAEnE,KAAA0B,mBAAmB,GAAG,IAAI,CAAC0C,eAAe,CAAC,CAAC,CAAC,CAAC/C,IAAI;IAClD,KAAAmC,eAAe,GAAGjD,WAAW;IAE7B,KAAAkE,qBAAqB,GAAG,CAAC,IAAI,CAACjB,eAAe,CAACkB,OAAO,CAAC;;EAEtDC,QAAQA,CAAA;IACJ,IAAI,CAACC,qBAAqB,EAAE;EAChC;EAGQA,qBAAqBA,CAAA;IACzBvE,YAAY,CAAC,IAAI,CAACkE,kBAAkB,EAAE;MAClCD,QAAQ,EAAE,IAAI,CAACA;KAClB,CAAC,CAACO,SAAS,CAAC;MACTC,IAAI,EAAGP,kBAAkB,IAAI;QACzBQ,OAAO,CAACC,GAAG,CAACT,kBAAkB,CAAC;QAE/B,QAAQA,kBAAkB,CAAClD,IAAI;UAC3B,KAAK,IAAI,CAACmC,eAAe,CAACkB,OAAO;YAC7B,IAAI,CAACD,qBAAqB,GAAG,CAAC,IAAI,CAACjB,eAAe,CAACkB,OAAO,CAAC;YAC3D;UACJ,KAAK,IAAI,CAAClB,eAAe,CAACC,IAAI;YAC1B,IAAI,CAACgB,qBAAqB,GAAG,CAAC,IAAI,CAACjB,eAAe,CAACkB,OAAO,EAAE,IAAI,CAAClB,eAAe,CAACC,IAAI,CAAC;YACtF;UACJ,KAAK,IAAI,CAACD,eAAe,CAACyB,OAAO;YAC7B,IAAI,CAACR,qBAAqB,GAAG,CAAC,IAAI,CAACjB,eAAe,CAACkB,OAAO,EAAE,IAAI,CAAClB,eAAe,CAACC,IAAI,CAAC;YACtF;UACJ;QACJ;QAGA,IAAIc,kBAAkB,IAAIA,kBAAkB,CAAClD,IAAI,KAAK,IAAI,CAACmC,eAAe,CAACkB,OAAO,EAAE;UAChF,IAAI,CAAChD,mBAAmB,GAAG6C,kBAAkB,CAAClD,IAAI;UAClD6D,UAAU,CAAC,MAAK;YACZ,IAAI,CAACV,+BAA+B,CAACW,IAAI,CAACZ,kBAAkB,CAAC;UACjE,CAAC,EAAE,EAAE,CAAC;QAEV;MACJ;KACH,CAAC;EACN;EAEA;;;;EAKOnD,iBAAiBA,CAACC,IAAS;IAC9B,IAAI,CAACK,mBAAmB,GAAGL,IAAI;EACnC;EAEAiC,WAAWA,CAAC8B,KAAe;IACvB,MAAMhB,eAAe,GAAG,IAAI,CAACA,eAAe;IAC5C,OAAOA,eAAe,CAACiB,MAAM,CAACC,CAAC,IAAI,CAAC,IAAI,CAACb,qBAAqB,CAACc,QAAQ,CAACD,CAAC,CAACjE,IAAI,CAAC,CAAC;EACpF;EAEOiB,kBAAkBA,CAAA;IACrB,OAAO,IAAI,CAAC8B,eAAe,CAACoB,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACpE,IAAI,KAAK,IAAI,CAACK,mBAAmB,CAAC;EAClF;EAEA2B,uBAAuBA,CAAA;IACnB,IAAI,CAACmB,+BAA+B,CAACW,IAAI,CAAC,IAAI,CAAC7C,kBAAkB,EAAE,CAAC;EACxE;EAAC,QAAAoD,CAAA,G;qBAvEQxB,gCAAgC;EAAA;EAAA,QAAAyB,EAAA,G;UAAhCzB,gCAAgC;IAAA0B,SAAA;IAAAC,MAAA;MAAAtB,kBAAA,GAAA/D,EAAA,CAAAsF,YAAA,CAAAC,WAAA;IAAA;IAAAC,OAAA;MAAAxB,+BAAA;IAAA;IAAAyB,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0CAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCX7C9F,EAAA,CAAAqC,UAAA,IAAA2D,+CAAA,kBAA6B;;;QAAdhG,EAAA,CAAAsB,UAAA,SAAAyE,GAAA,CAAA5D,IAAA,GAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}