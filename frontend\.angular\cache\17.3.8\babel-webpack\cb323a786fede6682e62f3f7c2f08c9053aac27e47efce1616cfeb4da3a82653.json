{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { inject, signal } from '@angular/core';\nimport { serverTimestamp, Timestamp } from 'firebase/firestore';\nimport * as moment from 'moment';\nimport { Observable, combineLatest, firstValueFrom } from 'rxjs';\nimport { map, switchMap } from 'rxjs/operators';\nimport { UserService } from './user.service';\nimport { GeneralService } from './general.service';\nimport { environment } from 'src/environments/environment';\nimport { getAuth, signInAnonymously } from \"firebase/auth\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/fire/compat/firestore\";\nimport * as i2 from \"@angular/fire/compat/auth\";\nimport * as i3 from \"@angular/common/http\";\nconst BACKEND_URL = environment.apiUrl;\nconst BACKEND_LMS_URL = environment.apiUrl + \"/LMS/\";\nexport class ChatService {\n  constructor(db, afAuth, http) {\n    this.db = db;\n    this.afAuth = afAuth;\n    this.http = http;\n    this.userService = inject(UserService);\n    this.generalService = inject(GeneralService);\n    this.isChatEnabled = signal(environment.isChatEnabled);\n    this.showUploadFile = signal(false);\n    this.unreadCount = signal(0);\n    this.currentRoom = signal({});\n    this.showBubbleChat = signal(false);\n    this.dbPath = '/chats';\n    this.dbUnreadPath = '/chats';\n    this.usersRef = this.db.collection('users');\n    this.readCount = 0;\n    this.secondsToCheckOnlineStatus = 10;\n    this.chatsRef = db.collection(this.dbPath);\n    this.usersRef = db.collection('/users');\n  }\n  deleteChatFilesFromLibraryFolder(fileUrl) {\n    console.log(fileUrl);\n    return this.http.post(BACKEND_LMS_URL + \"DeleteChatFilesFromLibraryFolder?fileUrl=\" + fileUrl, {});\n  }\n  // Sign in anonymously\n  signInAnonymously() {\n    return new Observable(observer => {\n      this.afAuth.signInAnonymously().then(userCredential => {\n        console.log('Signed in anonymously:', userCredential.user);\n        observer.next(userCredential);\n        observer.complete();\n      }).catch(error => {\n        console.error('Error signing in anonymously:', error);\n        observer.error(error);\n      });\n    });\n  }\n  signInAnonym() {\n    const auth = getAuth();\n    signInAnonymously(auth).then(() => {\n      // Signed in..\n    }).catch(error => {\n      const errorCode = error.code;\n      const errorMessage = error.message;\n      // ...\n    });\n  }\n  getLastMessage(classroomId) {\n    const chatRef = this.chatsRef.doc(`${classroomId}/`);\n    return chatRef.snapshotChanges().pipe(map(action => {\n      const data = action.payload.data();\n      const id = action.payload.id;\n      return {\n        id,\n        ...data\n      };\n    }));\n  }\n  // Fetch all chats for a classroom\n  getClassroomChats(classroomId) {\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    return this.chatsRef.doc(classroomId).collection('chats').snapshotChanges().pipe(map(actions => actions.map(a => {\n      const data = a.payload.doc.data();\n      const id = a.payload.doc.id;\n      return {\n        id,\n        ...data\n      };\n    })));\n  }\n  // Fetch messages for a specific chat room\n  getChatMessages(chatId, limit, lastMessage) {\n    const messagesQuery = this.db.collection(`${this.dbPath}/${chatId}/messages`, ref => {\n      let query = ref.orderBy('timestamp', 'desc').limit(limit);\n      if (lastMessage) {\n        query = query.startAfter(lastMessage.timestamp);\n      }\n      return query;\n    });\n    return messagesQuery.snapshotChanges().pipe(map(actions => actions.map(a => {\n      const data = a.payload.doc.data();\n      const id = a.payload.doc.id;\n      return {\n        id,\n        ...data\n      };\n    })));\n  }\n  // Create a new chat room\n  createChatRoom(classroomId, participants) {\n    const chat = {\n      classroomId: classroomId,\n      createdAt: moment().format('YYYY-MM-DD HH:mm:ss'),\n      lastMessage: '',\n      lastMessageAt: serverTimestamp(),\n      lastMessageFrom: '',\n      lastMessageFromName: '',\n      lastMessageFromImage: '',\n      lastMessageIsFile: false,\n      lastMessageDeleted: false,\n      lastMessageId: '',\n      lastProcessedMessageId: ''\n    };\n    return this.chatsRef.add(chat);\n  }\n  // Add a new message to a chat room\n  addMessage(classroomId, message, user) {\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    const messageRef = chatRef.collection('messages').doc();\n    const messageId = messageRef.ref.id;\n    return firstValueFrom(chatRef.get()).then(snapshot => {\n      const updateData = {\n        createdAt: moment().format('YYYY-MM-DD HH:mm:ss'),\n        lastMessage: message.content,\n        lastMessageAt: serverTimestamp(),\n        classroomId: classroomId,\n        lastMessageFrom: message.senderId,\n        lastMessageFromName: this.generalService.getPersonFullName(user),\n        lastMessageFromImage: this.userService.getUserPhoto(user?.avatarUrl),\n        lastMessageIsFile: message.isFileFromLibrary || message.isUploadFile,\n        lastMessageDeleted: false,\n        lastMessageId: messageId,\n        // Accessing id through ref\n        lastProcessedMessageId: ''\n      };\n      if (!snapshot.exists) {\n        // Retrieve users in the chat, including teacher and students\n        const usersInChat = [this.currentRoom().teacher, ...this.currentRoom().classroomStudents];\n        const updatedUnreadCounts = {};\n        usersInChat.forEach(chatUser => {\n          if (chatUser.aspUserId !== message.senderId) {\n            updatedUnreadCounts[chatUser.aspUserId] = 1; // Initialize unread count to 1 for new chat\n          }\n        });\n        updateData.unreadCounts = updatedUnreadCounts;\n        return chatRef.set(updateData).then(() => {\n          return messageRef.set(message).then(() => {\n            return messageRef.update({\n              id: messageRef.ref.id\n            }).then(() => {\n              return this.updateChatLastMessage(chatRef, classroomId);\n            });\n          });\n        });\n      } else {\n        // Existing chat room, update last message and unread counts\n        return this.updateLastMessage(chatRef, message, user, false, messageId).then(() => {\n          return messageRef.set(message).then(() => {\n            return messageRef.update({\n              id: messageRef.ref.id\n            });\n          });\n        });\n      }\n    });\n  }\n  sendMessageAndMarkRead(classroomId, message, user) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const chatRef = _this.chatsRef.doc(`${classroomId}`);\n      const messageRef = chatRef.collection('messages').doc();\n      const unreadMessagesRef = _this.db.collection(`${_this.dbUnreadPath}/${classroomId}/messages`);\n      const messageId = messageRef.ref.id;\n      const batch = _this.db.firestore.batch();\n      // Prepare message data\n      const messageData = {\n        ...message,\n        id: messageId,\n        createdAt: moment().format('YYYY-MM-DD HH:mm:ss')\n      };\n      // Add new message to the batch\n      batch.set(messageRef.ref, messageData);\n      const chatSnapshot = yield firstValueFrom(chatRef.get());\n      const updateData = {\n        lastMessage: message.content,\n        lastMessageAt: serverTimestamp(),\n        lastMessageFrom: message.senderId,\n        lastMessageFromName: _this.generalService.getPersonFullName(user),\n        lastMessageFromImage: _this.userService.getUserPhoto(user?.avatarUrl),\n        lastMessageIsFile: message.isFileFromLibrary || message.isUploadFile,\n        lastMessageDeleted: false,\n        lastMessageId: messageId\n      };\n      if (!chatSnapshot.exists) {\n        // New chat room: initialize unread counts for all users except the sender\n        const usersInChat = [_this.currentRoom().teacher, ..._this.currentRoom().classroomStudents];\n        const unreadCounts = {};\n        usersInChat.forEach(chatUser => {\n          if (chatUser.aspUserId !== message.senderId) {\n            unreadCounts[chatUser.aspUserId] = 1;\n          }\n        });\n        updateData.unreadCounts = unreadCounts;\n        batch.set(chatRef.ref, updateData);\n      } else {\n        // Update last message and adjust unread counts for existing room\n        const existingData = chatSnapshot.data();\n        const updatedUnreadCounts = {\n          ...existingData?.unreadCounts\n        };\n        Object.keys(updatedUnreadCounts).forEach(userId => {\n          if (userId !== message.senderId) {\n            updatedUnreadCounts[userId] = (updatedUnreadCounts[userId] || 0) + 1;\n          }\n        });\n        updateData.unreadCounts = updatedUnreadCounts;\n        batch.update(chatRef.ref, updateData);\n      }\n      // Update read status for the current user in unread messages\n      const unreadMessages = yield firstValueFrom(unreadMessagesRef.get());\n      unreadMessages.forEach(doc => {\n        const docData = doc.data(); // Retrieve data from each document\n        const readStatus = docData.readStatus || {}; // Access or initialize readStatus\n        if (!readStatus[user.aspUserId]) {\n          readStatus[user.aspUserId] = true; // Mark as read for the current user\n          batch.update(doc.ref, {\n            readStatus\n          }); // Add update to batch\n        }\n      });\n      // Commit the entire batch\n      yield batch.commit();\n    })();\n  }\n  updateLastMessage(chatRef, updatedMessage, user, isLastMessageDeleted = false, messageId = '') {\n    const messageRef = chatRef.collection('messages').doc();\n    const updateData = {\n      lastMessage: updatedMessage.content,\n      lastMessageAt: serverTimestamp(),\n      lastMessageFrom: updatedMessage.senderId,\n      lastMessageFromImage: this.userService.getUserPhoto(user?.avatarUrl),\n      lastMessageFromName: this.generalService.getPersonFullName(user),\n      lastMessageIsFile: updatedMessage.isFileFromLibrary || updatedMessage.isUploadFile,\n      lastMessageDeleted: isLastMessageDeleted,\n      lastMessageId: messageId\n    };\n    return firstValueFrom(chatRef.get()).then(snapshot => {\n      console.log('current room', this.currentRoom());\n      const updatedUnreadCounts = {};\n      // Retrieve users in the chat, including teacher and students\n      const usersInChat = [this.currentRoom().teacher, ...this.currentRoom().classroomStudents];\n      usersInChat.forEach(chatUser => {\n        if (chatUser.aspUserId !== updatedMessage.senderId) {\n          updatedUnreadCounts[chatUser.aspUserId] = (snapshot.exists ? snapshot.data().unreadCounts?.[chatUser.aspUserId] || 0 : 0) + 1;\n        }\n      });\n      updateData.unreadCounts = updatedUnreadCounts;\n      return chatRef.update(updateData);\n    });\n  }\n  editMessage(classroomId, messageId, updatedMessage, user, updateLastMessage = false) {\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    const messageRef = chatRef.collection('messages').doc(`${messageId}`);\n    const messagesRef = chatRef.collection('messages', ref => ref.orderBy('timestamp', 'desc').limit(1));\n    const lastMessageDoc = messagesRef.ref.doc();\n    const lastMessageId = lastMessageDoc.id;\n    console.log(lastMessageId);\n    return messageRef.get().toPromise().then(snapshot => {\n      if (snapshot.exists) {\n        return messageRef.update({\n          ...updatedMessage\n        }).then(() => {\n          if (updateLastMessage) {\n            return this.updateLastMessage(chatRef, updatedMessage, user, false, updatedMessage.id);\n          }\n          return Promise.resolve();\n        });\n      } else {\n        throw new Error('Message does not exist');\n      }\n    });\n  }\n  getLastUnreadMessagesCountForUser(classroomId, userId) {\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    return chatRef.valueChanges().pipe(map(data => {\n      const unreadCounts = data?.unreadCounts;\n      return unreadCounts ? unreadCounts[userId] || 0 : 0;\n    }));\n  }\n  markLastMessagesAsRead(classroomId, userId) {\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    return this.markLastMessagesAsReadInLastMessage(chatRef, userId);\n  }\n  markLastMessagesAsReadInLastMessage(chatRunreadCountsRefef, userId) {\n    return chatRunreadCountsRefef.get().toPromise().then(snapshot => {\n      if (snapshot.exists) {\n        const data = snapshot.data();\n        const currentCount = data.unreadCounts?.[userId] || 0;\n        // Only update if the unread count for the user is greater than zero\n        if (currentCount > 0) {\n          const updatedUnreadCounts = {\n            ...data.unreadCounts,\n            [userId]: 0\n          };\n          return chatRunreadCountsRefef.update({\n            unreadCounts: updatedUnreadCounts\n          });\n        }\n      }\n      return Promise.resolve(); // If the document doesn't exist or no update is needed\n    });\n  }\n  // Delete a specific message from a chat room\n  deleteMessage(classroomId, message, user, updateLastMessage = false) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      console.log(classroomId);\n      console.log(message);\n      const chatRef = _this2.chatsRef.doc(`${classroomId}`);\n      const messageRef = chatRef.collection('messages').doc(message.id);\n      // Start a batch to combine operations\n      const batch = _this2.db.firestore.batch();\n      // Queue the deletion of the message\n      batch.delete(messageRef.ref);\n      if (updateLastMessage) {\n        const latestMessage = yield _this2.getLatestMessage(classroomId);\n        const updateData = {\n          ...message,\n          content: 'Message Deleted'\n        };\n        yield _this2.updateLastMessageInBatch(chatRef, updateData, user, true, latestMessage?.id, batch);\n      }\n      // Commit the batch\n      yield batch.commit();\n      // Update the chat's last message if not updating the last message\n      if (!updateLastMessage) {\n        yield _this2.updateChatLastMessage(chatRef, classroomId);\n      }\n    })();\n  }\n  updateLastMessageInBatch(chatRef, updatedMessage, user, isLastMessageDeleted, messageId, batch) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const updateData = {\n        lastMessage: updatedMessage.content,\n        lastMessageAt: serverTimestamp(),\n        lastMessageFrom: updatedMessage.senderId,\n        lastMessageFromImage: _this3.userService.getUserPhoto(user?.avatarUrl),\n        lastMessageFromName: _this3.generalService.getPersonFullName(user),\n        lastMessageIsFile: updatedMessage.isFileFromLibrary || updatedMessage.isUploadFile,\n        lastMessageDeleted: isLastMessageDeleted,\n        lastMessageId: messageId\n      };\n      // Get snapshot of the chat document\n      const snapshot = yield firstValueFrom(chatRef.get());\n      // Prepare updated unread counts\n      const updatedUnreadCounts = {};\n      const usersInChat = [_this3.currentRoom().teacher, ..._this3.currentRoom().classroomStudents];\n      const currentUnreadCounts = snapshot.exists ? snapshot.data()?.unreadCounts || {} : {};\n      usersInChat.forEach(chatUser => {\n        if (chatUser.aspUserId !== updatedMessage.senderId) {\n          updatedUnreadCounts[chatUser.aspUserId] = (currentUnreadCounts[chatUser.aspUserId] || 0) + 1; // Increment unread count\n        }\n      });\n      // Only update if there are changes to the unread counts\n      if (Object.keys(updatedUnreadCounts).length > 0) {\n        updateData.unreadCounts = updatedUnreadCounts;\n      }\n      // Perform a single update to the chat document in the batch\n      batch.update(chatRef.ref, updateData);\n    })();\n  }\n  updateChatLastMessage(chatRef, classroomId) {\n    return this.getLatestMessage(classroomId).then(snapshot => {\n      console.log(snapshot);\n      if (snapshot) {\n        const latestMessage = snapshot;\n        return chatRef.update({\n          lastMessage: latestMessage.content,\n          lastMessageAt: latestMessage.createdAt,\n          lastMessageFrom: latestMessage.senderId,\n          lastMessageFromName: latestMessage.senderName,\n          lastMessageFromImage: latestMessage.senderImage,\n          lastMessageIsFile: latestMessage.isFileFromLibrary || latestMessage.isUploadFile,\n          lastMessageDeleted: false,\n          lastMessageId: latestMessage.id\n        });\n      } else {\n        // Handle case where there are no messages left in the collection\n        return chatRef.update({\n          lastMessage: '',\n          lastMessageAt: null,\n          lastMessageFrom: '',\n          lastMessageFromName: '',\n          lastMessageFromImage: '',\n          lastMessageIsFile: false,\n          lastMessageDeleted: false,\n          lastMessageId: ''\n        });\n      }\n    });\n  }\n  getLatestMessage(classroomId) {\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    return firstValueFrom(chatRef.collection('messages', ref => ref.orderBy('timestamp', 'desc').limit(1)).get()).then(snapshot => {\n      if (!snapshot.empty) {\n        const latestMessage = snapshot.docs[0].data();\n        return latestMessage;\n      } else {\n        return null; // No messages found\n      }\n    });\n  }\n  // Update a chat room document\n  updateChatRoom(classroomId, data) {\n    return this.chatsRef.doc(classroomId).update(data);\n  }\n  // Delete a chat room document\n  deleteChatRoom(classroomId) {\n    return this.chatsRef.doc(classroomId).delete();\n  }\n  addUnreadMessage(classroomId, message) {\n    const messageRef = this.db.collection(`${this.dbUnreadPath}/${classroomId}/messages`).doc(message.id);\n    // Set the initial readStatus for the new message\n    const initialReadStatus = {\n      // Initialize the readStatus for all users, defaulting to false (unread)\n      [message.senderId]: false // Mark the sender as false (unread)\n      // You can add more user IDs here if needed, depending on your application logic\n    };\n    return messageRef.set({\n      readStatus: initialReadStatus,\n      // Add the readStatus field\n      timestamp: serverTimestamp() // Ensure there's a timestamp for ordering\n    });\n  }\n  markMessagesAsRead(classroomId, userId) {\n    return this.db.collection(`${this.dbUnreadPath}/${classroomId}/messages`).get().toPromise().then(querySnapshot => {\n      const batch = this.db.firestore.batch();\n      let hasUpdates = false; // Flag to track if there are any updates\n      querySnapshot.forEach(doc => {\n        const data = doc.data();\n        // Ensure readStatus is a valid map or initialize it\n        const readStatusUpdate = data.readStatus || {};\n        // Only update if the userId is not already marked as read\n        if (!readStatusUpdate[userId]) {\n          readStatusUpdate[userId] = true;\n          // Update the document with the new or modified readStatus map\n          batch.update(doc.ref, {\n            readStatus: readStatusUpdate\n          });\n          hasUpdates = true; // Set the flag to true if an update is made\n        }\n      });\n      // Only commit the batch if there are updates\n      if (hasUpdates) {\n        return batch.commit();\n      }\n      // If no updates were made, resolve immediately\n      return Promise.resolve();\n    });\n  }\n  // Mark messages as read\n  markAllMessagesAsRead(classroomId) {\n    return this.db.collection(`${this.dbUnreadPath}/${classroomId}/messages`).get().toPromise().then(querySnapshot => {\n      const batch = this.db.firestore.batch();\n      querySnapshot.forEach(doc => {\n        const messageData = doc.data();\n        const updatedReadStatus = {};\n        // Set readStatus for all users in the message's readStatus map to true\n        for (const userId in messageData.readStatus) {\n          if (messageData.readStatus.hasOwnProperty(userId)) {\n            const a = `readStatus.${userId}`;\n            updatedReadStatus[a] = true;\n          }\n        }\n        batch.update(doc.ref, updatedReadStatus);\n      });\n      return batch.commit();\n    });\n  }\n  // Get unread messages count for a user in a classroom\n  getUnreadMessagesCount(classroomId, userId) {\n    return this.db.collection(`${this.dbUnreadPath}/${classroomId}/messages`).snapshotChanges().pipe(map(actions => actions.filter(action => {\n      const data = action.payload.doc.data();\n      return !data.readStatus.hasOwnProperty(userId);\n    }).length));\n  }\n  // Get total unread messages count for a user across all classrooms\n  getUnreadMessagesCountForUser(userId) {\n    return this.chatsRef.snapshotChanges().pipe(switchMap(chatSnapshots => {\n      const chatObservables = chatSnapshots.map(snapshot => {\n        const chatId = snapshot.payload.doc.id;\n        return this.getUnreadMessagesCount(chatId, userId);\n      });\n      return combineLatest(chatObservables);\n    }), map(unreadCounts => unreadCounts.reduce((acc, count) => acc + count, 0)));\n  }\n  // Or, to get the user object directly\n  getFireBaseUser() {\n    return this.afAuth.authState;\n  }\n  setUserStatus(userId, status) {\n    const userRef = this.usersRef.doc(userId);\n    return userRef.set({\n      status,\n      lastOnlineTime: Timestamp.now()\n    }, {\n      merge: true\n    });\n  }\n  setUserLastOnlineTime(userId, time) {\n    const userRef = this.usersRef.doc(userId);\n    return userRef.set({\n      status: 'online',\n      lastOnlineTime: time\n    }, {\n      merge: true\n    });\n  }\n  getUserStatus(userId) {\n    return this.usersRef.doc(userId).valueChanges().pipe(map(user => user?.status || 'offline'));\n  }\n  getOnlineUsers() {\n    return this.db.collection('/users', ref => ref.where('status', '==', 'online')).snapshotChanges().pipe(map(actions => actions.map(a => {\n      const data = a.payload.doc.data();\n      const id = a.payload.doc.id;\n      return {\n        ...data,\n        id\n      }; // Corrected merge of id and data\n    })));\n  }\n  /**\n   * Retrieves all users in the Firestore 'users' collection, including their status compared to the last online time.\n   * The status is determined by comparing the current time with the user's last online time. If the user was online\n   * within the last `secondsToCheckOnlineStatus` seconds, the status is set to 'online', otherwise it is set to 'offline'.\n   * If the user does not have a last online time, the status is set to 'offline'.\n   * @returns An Observable of an array of ChatUserStatus objects, where each object contains the user's id, status, and last online time.\n   */\n  getOnlineUsersComparedToLastOnlineTime() {\n    return this.db.collection('/users').snapshotChanges().pipe(map(actions => {\n      const currentTime = Timestamp.now();\n      return actions.map(a => {\n        const data = a.payload.doc.data();\n        const id = a.payload.doc.id;\n        const lastOnlineTime = data.lastOnlineTime;\n        if (lastOnlineTime) {\n          if (!currentTime.seconds || !lastOnlineTime.seconds) {\n            return {\n              ...data,\n              id,\n              status: 'offline'\n            };\n          }\n          const timeDifference = currentTime?.seconds - lastOnlineTime?.seconds;\n          // Check if the user was online within the last 10 seconds\n          if (timeDifference <= this.secondsToCheckOnlineStatus) {\n            return {\n              ...data,\n              id,\n              status: 'online'\n            };\n          } else {\n            return {\n              ...data,\n              id,\n              status: 'offline'\n            };\n          }\n        }\n        return {\n          ...data,\n          id,\n          status: 'offline'\n        };\n      });\n    }));\n  }\n  static #_ = this.ɵfac = function ChatService_Factory(t) {\n    return new (t || ChatService)(i0.ɵɵinject(i1.AngularFirestore), i0.ɵɵinject(i2.AngularFireAuth), i0.ɵɵinject(i3.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ChatService,\n    factory: ChatService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["inject", "signal", "serverTimestamp", "Timestamp", "moment", "Observable", "combineLatest", "firstValueFrom", "map", "switchMap", "UserService", "GeneralService", "environment", "getAuth", "signInAnonymously", "BACKEND_URL", "apiUrl", "BACKEND_LMS_URL", "ChatService", "constructor", "db", "afAuth", "http", "userService", "generalService", "isChatEnabled", "showUploadFile", "unreadCount", "currentRoom", "showBubbleChat", "db<PERSON><PERSON>", "dbUnreadPath", "usersRef", "collection", "readCount", "secondsToCheckOnlineStatus", "chatsRef", "deleteChatFilesFromLibraryFolder", "fileUrl", "console", "log", "post", "observer", "then", "userCredential", "user", "next", "complete", "catch", "error", "signInAnonym", "auth", "errorCode", "code", "errorMessage", "message", "getLastMessage", "classroomId", "chatRef", "doc", "snapshotChanges", "pipe", "action", "data", "payload", "id", "getClassroomChats", "actions", "a", "getChatMessages", "chatId", "limit", "lastMessage", "messagesQuery", "ref", "query", "orderBy", "startAfter", "timestamp", "createChatRoom", "participants", "chat", "createdAt", "format", "lastMessageAt", "lastMessageFrom", "lastMessageFromName", "lastMessageFromImage", "lastMessageIsFile", "lastMessageDeleted", "lastMessageId", "lastProcessedMessageId", "add", "addMessage", "messageRef", "messageId", "get", "snapshot", "updateData", "content", "senderId", "getPersonFullName", "getUserPhoto", "avatarUrl", "isFileFromLibrary", "isUploadFile", "exists", "usersInChat", "teacher", "classroomStudents", "updatedUnreadCounts", "for<PERSON>ach", "chatUser", "aspUserId", "unreadCounts", "set", "update", "updateChatLastMessage", "updateLastMessage", "sendMessageAndMarkRead", "_this", "_asyncToGenerator", "unreadMessagesRef", "batch", "firestore", "messageData", "chatSnapshot", "existingData", "Object", "keys", "userId", "unreadMessages", "docData", "readStatus", "commit", "updatedMessage", "isLastMessageDeleted", "editMessage", "messagesRef", "lastMessageDoc", "to<PERSON>romise", "Promise", "resolve", "Error", "getLastUnreadMessagesCountForUser", "valueChanges", "markLastMessagesAsRead", "markLastMessagesAsReadInLastMessage", "chatRunreadCountsRefef", "currentCount", "deleteMessage", "_this2", "delete", "latestMessage", "getLatestMessage", "updateLastMessageInBatch", "_this3", "currentUnreadCounts", "length", "sender<PERSON>ame", "senderImage", "empty", "docs", "updateChatRoom", "deleteChatRoom", "addUnreadMessage", "initialReadStatus", "markMessagesAsRead", "querySnapshot", "hasUpdates", "readStatusUpdate", "markAllMessagesAsRead", "updatedReadStatus", "hasOwnProperty", "getUnreadMessagesCount", "filter", "getUnreadMessagesCountForUser", "chatSnapshots", "chatObservables", "reduce", "acc", "count", "getFireBaseUser", "authState", "setUserStatus", "status", "userRef", "lastOnlineTime", "now", "merge", "setUserLastOnlineTime", "time", "getUserStatus", "getOnlineUsers", "where", "getOnlineUsersComparedToLastOnlineTime", "currentTime", "seconds", "timeDifference", "_", "i0", "ɵɵinject", "i1", "AngularFirestore", "i2", "AngularFireAuth", "i3", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\services\\chat.service.ts"], "sourcesContent": ["import { Injectable, inject, signal } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { AngularFirestore, AngularFirestoreCollection, AngularFirestoreDocument } from '@angular/fire/compat/firestore';\r\nimport { FieldValue, serverTimestamp, Timestamp } from 'firebase/firestore';\r\nimport * as moment from 'moment';\r\nimport { Observable, combineLatest, defer, firstValueFrom, forkJoin, from } from 'rxjs';\r\nimport { map, switchMap } from 'rxjs/operators';\r\nimport { UserService } from './user.service';\r\nimport { GeneralService } from './general.service';\r\nimport { environment } from 'src/environments/environment';\r\nconst BACKEND_URL = environment.apiUrl;\r\nconst BACKEND_LMS_URL = environment.apiUrl + \"/LMS/\";\r\nimport { getAuth, signInAnonymously } from \"firebase/auth\";\r\nimport { AngularFireAuth } from '@angular/fire/compat/auth';\r\n\r\ninterface Chat {\r\n  createdAt: string;\r\n  lastMessage: string;\r\n  lastMessageAt: FieldValue;\r\n  lastMessageFrom: string;\r\n  lastMessageFromName: string;\r\n  lastMessageFromImage: string;\r\n  lastMessageIsFile: boolean;\r\n  lastMessageDeleted: boolean;\r\n  lastMessageId: string;\r\n  lastProcessedMessageId: string;\r\n  classroomId: string;\r\n  unreadCounts?: any;\r\n}\r\n\r\nexport interface ChatUserStatus {\r\n  id?: string;\r\n  status: 'online' | 'offline';\r\n  lastOnlineTime: FieldValue;\r\n}\r\n\r\n\r\nexport interface Message {\r\n  id?: string;\r\n  senderId: string;\r\n  content: string;\r\n  timestamp: FieldValue;\r\n  isFileFromLibrary: boolean;\r\n  isUploadFile: boolean;\r\n  readStatus: {}\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ChatService {\r\n  userService = inject(UserService);\r\n  generalService = inject(GeneralService);\r\n  isChatEnabled = signal(environment.isChatEnabled);\r\n  showUploadFile = signal(false);\r\n  unreadCount = signal(0);\r\n  currentRoom = signal({} as any);\r\n  showBubbleChat = signal(false);\r\n  private dbPath = '/chats';\r\n  private dbUnreadPath = '/chats';\r\n  chatsRef: AngularFirestoreCollection<Chat>;\r\n  private usersRef: AngularFirestoreCollection<ChatUserStatus> = this.db.collection('users');\r\n  readCount = 0;\r\n  readonly secondsToCheckOnlineStatus = 10;\r\n\r\n\r\n  constructor(private db: AngularFirestore, private afAuth: AngularFireAuth, private http: HttpClient) {\r\n    this.chatsRef = db.collection(this.dbPath);\r\n    this.usersRef = db.collection('/users');\r\n  }\r\n\r\n\r\n  deleteChatFilesFromLibraryFolder(fileUrl: any): Observable<any> {\r\n    console.log(fileUrl)\r\n    return this.http.post<any>(BACKEND_LMS_URL + \"DeleteChatFilesFromLibraryFolder?fileUrl=\" + fileUrl, {})\r\n  }\r\n\r\n  // Sign in anonymously\r\n  signInAnonymously(): Observable<any> {\r\n    return new Observable(observer => {\r\n      this.afAuth.signInAnonymously()\r\n        .then(userCredential => {\r\n          console.log('Signed in anonymously:', userCredential.user);\r\n          observer.next(userCredential);\r\n          observer.complete();\r\n        })\r\n        .catch(error => {\r\n          console.error('Error signing in anonymously:', error);\r\n          observer.error(error);\r\n        });\r\n    });\r\n  }\r\n\r\n  signInAnonym() {\r\n    const auth = getAuth();\r\n    signInAnonymously(auth)\r\n      .then(() => {\r\n        // Signed in..\r\n      })\r\n      .catch((error) => {\r\n        const errorCode = error.code;\r\n        const errorMessage = error.message;\r\n        // ...\r\n      });\r\n  }\r\n\r\n  getLastMessage(classroomId: string): Observable<Message> {\r\n    const chatRef = this.chatsRef.doc<Message>(`${classroomId}/`);\r\n\r\n    return chatRef.snapshotChanges().pipe(\r\n      map(action => {\r\n        const data = action.payload.data() as Message;\r\n        const id = action.payload.id;\r\n        return { id, ...data };\r\n      })\r\n    );\r\n  }\r\n\r\n  // Fetch all chats for a classroom\r\n  getClassroomChats(classroomId: string): Observable<Chat[]> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    return this.chatsRef.doc(classroomId).collection<Chat>('chats')\r\n      .snapshotChanges().pipe(\r\n        map(actions => actions.map(a => {\r\n          const data = a.payload.doc.data() as Chat;\r\n          const id = a.payload.doc.id;\r\n          return { id, ...data };\r\n        }))\r\n      );\r\n  }\r\n\r\n  // Fetch messages for a specific chat room\r\n  getChatMessages(chatId: string, limit: number, lastMessage?: Message): Observable<Message[]> {\r\n    const messagesQuery = this.db.collection<Message>(`${this.dbPath}/${chatId}/messages`, ref => {\r\n      let query = ref.orderBy('timestamp', 'desc').limit(limit);\r\n      if (lastMessage) {\r\n        query = query.startAfter(lastMessage.timestamp);\r\n      }\r\n      return query;\r\n    });\r\n\r\n    return messagesQuery.snapshotChanges().pipe(\r\n      map(actions => actions.map(a => {\r\n        const data = a.payload.doc.data() as Message;\r\n        const id = a.payload.doc.id;\r\n        return { id, ...data };\r\n      }))\r\n    );\r\n  }\r\n\r\n  // Create a new chat room\r\n  createChatRoom(classroomId: string, participants: string[]) {\r\n    const chat: Chat = {\r\n      classroomId: classroomId,\r\n      createdAt: moment().format('YYYY-MM-DD HH:mm:ss'),\r\n      lastMessage: '',\r\n      lastMessageAt: serverTimestamp(),\r\n      lastMessageFrom: '',\r\n      lastMessageFromName: '',\r\n      lastMessageFromImage: '',\r\n      lastMessageIsFile: false,\r\n      lastMessageDeleted: false,\r\n      lastMessageId: '',\r\n      lastProcessedMessageId: '',\r\n    };\r\n    return this.chatsRef.add(chat);\r\n  }\r\n\r\n  // Add a new message to a chat room\r\n  addMessage(classroomId: string, message: Message, user: any): Promise<void> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    const messageRef = chatRef.collection('messages').doc();\r\n\r\n    const messageId = (messageRef.ref.id);\r\n    return firstValueFrom(chatRef.get()).then(snapshot => {\r\n      const updateData: any = {\r\n        createdAt: moment().format('YYYY-MM-DD HH:mm:ss'),\r\n        lastMessage: message.content,\r\n        lastMessageAt: serverTimestamp(),\r\n        classroomId: classroomId,\r\n        lastMessageFrom: message.senderId,\r\n        lastMessageFromName: this.generalService.getPersonFullName(user),\r\n        lastMessageFromImage: this.userService.getUserPhoto(user?.avatarUrl),\r\n        lastMessageIsFile: message.isFileFromLibrary || message.isUploadFile,\r\n        lastMessageDeleted: false,\r\n        lastMessageId: messageId, // Accessing id through ref\r\n        lastProcessedMessageId: '',\r\n      };\r\n\r\n      if (!snapshot.exists) {\r\n        // Retrieve users in the chat, including teacher and students\r\n        const usersInChat = [this.currentRoom().teacher, ...this.currentRoom().classroomStudents];\r\n        const updatedUnreadCounts: any = {};\r\n        usersInChat.forEach((chatUser: any) => {\r\n          if (chatUser.aspUserId !== message.senderId) {\r\n            updatedUnreadCounts[chatUser.aspUserId] = 1; // Initialize unread count to 1 for new chat\r\n          }\r\n        });\r\n        updateData.unreadCounts = updatedUnreadCounts;\r\n\r\n        return chatRef.set(updateData).then(() => {\r\n          return messageRef.set(message).then(() => {\r\n            return messageRef.update({ id: messageRef.ref.id }).then(() => {\r\n              return this.updateChatLastMessage(chatRef, classroomId);\r\n            })\r\n          });\r\n        });\r\n      } else {\r\n        // Existing chat room, update last message and unread counts\r\n        return this.updateLastMessage(chatRef, message, user, false, messageId).then(() => {\r\n          return messageRef.set(message).then(() => {\r\n            return messageRef.update({ id: messageRef.ref.id });\r\n          })\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  async sendMessageAndMarkRead(\r\n    classroomId: string, \r\n    message: Message, \r\n    user: any\r\n  ): Promise<void> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    const messageRef = chatRef.collection('messages').doc();\r\n    const unreadMessagesRef = this.db.collection(`${this.dbUnreadPath}/${classroomId}/messages`);\r\n  \r\n    const messageId = messageRef.ref.id;\r\n    const batch = this.db.firestore.batch();\r\n  \r\n    // Prepare message data\r\n    const messageData = {\r\n      ...message,\r\n      id: messageId,\r\n      createdAt: moment().format('YYYY-MM-DD HH:mm:ss')\r\n    };\r\n  \r\n    // Add new message to the batch\r\n    batch.set(messageRef.ref, messageData);\r\n  \r\n    const chatSnapshot = await firstValueFrom(chatRef.get());\r\n    const updateData: any = {\r\n      lastMessage: message.content,\r\n      lastMessageAt: serverTimestamp(),\r\n      lastMessageFrom: message.senderId,\r\n      lastMessageFromName: this.generalService.getPersonFullName(user),\r\n      lastMessageFromImage: this.userService.getUserPhoto(user?.avatarUrl),\r\n      lastMessageIsFile: message.isFileFromLibrary || message.isUploadFile,\r\n      lastMessageDeleted: false,\r\n      lastMessageId: messageId,\r\n    };\r\n  \r\n    if (!chatSnapshot.exists) {\r\n      // New chat room: initialize unread counts for all users except the sender\r\n      const usersInChat = [this.currentRoom().teacher, ...this.currentRoom().classroomStudents];\r\n      const unreadCounts: { [key: string]: number }  = {};\r\n  \r\n      usersInChat.forEach(chatUser => {\r\n        if (chatUser.aspUserId !== message.senderId) {\r\n          unreadCounts[chatUser.aspUserId] = 1;\r\n        }\r\n      });\r\n  \r\n      updateData.unreadCounts = unreadCounts;\r\n      batch.set(chatRef.ref, updateData);\r\n    } else {\r\n      // Update last message and adjust unread counts for existing room\r\n      const existingData = chatSnapshot.data();\r\n      const updatedUnreadCounts = { ...existingData?.unreadCounts };\r\n  \r\n      Object.keys(updatedUnreadCounts).forEach(userId => {\r\n        if (userId !== message.senderId) {\r\n          updatedUnreadCounts[userId] = (updatedUnreadCounts[userId] || 0) + 1;\r\n        }\r\n      });\r\n  \r\n      updateData.unreadCounts = updatedUnreadCounts;\r\n      batch.update(chatRef.ref, updateData);\r\n    }\r\n  \r\n    // Update read status for the current user in unread messages\r\n    const unreadMessages = await firstValueFrom(unreadMessagesRef.get());\r\n    unreadMessages.forEach(doc => {  // Use .docs to access individual documents\r\n      const docData = doc.data() as any;   // Retrieve data from each document\r\n      const readStatus = docData.readStatus || {}; // Access or initialize readStatus\r\n    \r\n      if (!readStatus[user.aspUserId]) {\r\n        readStatus[user.aspUserId] = true; // Mark as read for the current user\r\n        batch.update(doc.ref, { readStatus }); // Add update to batch\r\n      }\r\n    });\r\n    \r\n    \r\n  \r\n    // Commit the entire batch\r\n    await batch.commit();\r\n  }\r\n  \r\n\r\n  private updateLastMessage(chatRef: AngularFirestoreDocument<any>,\r\n    updatedMessage: Message, user: any, isLastMessageDeleted = false, messageId = ''): Promise<void> {\r\n\r\n    const messageRef = chatRef.collection('messages').doc();\r\n    const updateData: any = {\r\n      lastMessage: updatedMessage.content,\r\n      lastMessageAt: serverTimestamp(),\r\n      lastMessageFrom: updatedMessage.senderId,\r\n      lastMessageFromImage: this.userService.getUserPhoto(user?.avatarUrl),\r\n      lastMessageFromName: this.generalService.getPersonFullName(user),\r\n      lastMessageIsFile: updatedMessage.isFileFromLibrary || updatedMessage.isUploadFile,\r\n      lastMessageDeleted: isLastMessageDeleted,\r\n      lastMessageId: messageId\r\n    };\r\n\r\n    return firstValueFrom(chatRef.get()).then(snapshot => {\r\n      console.log('current room', this.currentRoom());\r\n      const updatedUnreadCounts: any = {};\r\n      // Retrieve users in the chat, including teacher and students\r\n      const usersInChat = [this.currentRoom().teacher, ...this.currentRoom().classroomStudents];\r\n\r\n      usersInChat.forEach((chatUser: any) => {\r\n        if (chatUser.aspUserId !== updatedMessage.senderId) {\r\n          updatedUnreadCounts[chatUser.aspUserId] = (snapshot!.exists ? (snapshot!.data() as any).unreadCounts?.[chatUser.aspUserId] || 0 : 0) + 1;\r\n        }\r\n      });\r\n\r\n      updateData.unreadCounts = updatedUnreadCounts;\r\n      return chatRef.update(updateData);\r\n    });\r\n  }\r\n\r\n  editMessage(classroomId: string, messageId: string, updatedMessage: Message, user: any, updateLastMessage = false): Promise<void> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    const messageRef = chatRef.collection('messages').doc(`${messageId}`);\r\n    const messagesRef = chatRef.collection('messages', ref => ref.orderBy('timestamp', 'desc').limit(1));\r\n\r\n    const lastMessageDoc = messagesRef.ref.doc();\r\n    const lastMessageId = lastMessageDoc.id;\r\n    console.log(lastMessageId);\r\n    return messageRef.get().toPromise().then(snapshot => {\r\n      if (snapshot!.exists) {\r\n\r\n\r\n        return messageRef.update({\r\n          ...updatedMessage,\r\n        }).then(() => {\r\n          if (updateLastMessage) {\r\n            return this.updateLastMessage(chatRef, updatedMessage, user, false, updatedMessage.id!);\r\n          }\r\n          return Promise.resolve();\r\n        });\r\n      } else {\r\n        throw new Error('Message does not exist');\r\n      }\r\n    });\r\n  }\r\n\r\n  getLastUnreadMessagesCountForUser(classroomId: string, userId: string): Observable<number> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n\r\n    return chatRef.valueChanges().pipe(\r\n      map(data => {\r\n        const unreadCounts = (data as any)?.unreadCounts;\r\n        return unreadCounts ? unreadCounts[userId] || 0 : 0;\r\n      })\r\n    );\r\n  }\r\n\r\n  markLastMessagesAsRead(classroomId: string, userId: string): Promise<void> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    return this.markLastMessagesAsReadInLastMessage(chatRef, userId);\r\n  }\r\n\r\n  markLastMessagesAsReadInLastMessage(chatRunreadCountsRefef: AngularFirestoreDocument<any>, userId: string): Promise<void> {\r\n    return chatRunreadCountsRefef.get().toPromise().then(snapshot => {\r\n      if (snapshot!.exists) {\r\n        const data = snapshot!.data() as any;\r\n        const currentCount = data.unreadCounts?.[userId] || 0;\r\n\r\n        // Only update if the unread count for the user is greater than zero\r\n        if (currentCount > 0) {\r\n          const updatedUnreadCounts = { ...data.unreadCounts, [userId]: 0 };\r\n          return chatRunreadCountsRefef.update({ unreadCounts: updatedUnreadCounts });\r\n        }\r\n      }\r\n      return Promise.resolve(); // If the document doesn't exist or no update is needed\r\n    });\r\n  }\r\n\r\n  // Delete a specific message from a chat room\r\n  async deleteMessage(classroomId: string, message: any, user: any, updateLastMessage = false): Promise<void> {\r\n    console.log(classroomId);\r\n    console.log(message);\r\n\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    const messageRef = chatRef.collection('messages').doc(message.id);\r\n    \r\n    // Start a batch to combine operations\r\n    const batch = this.db.firestore.batch();\r\n\r\n    // Queue the deletion of the message\r\n    batch.delete(messageRef.ref);\r\n\r\n    if (updateLastMessage) {\r\n        const latestMessage = await this.getLatestMessage(classroomId);\r\n        const updateData = { \r\n            ...message, \r\n            content: 'Message Deleted' \r\n        };\r\n\r\n        await this.updateLastMessageInBatch(chatRef, updateData, user, true, latestMessage?.id, batch);\r\n    }\r\n\r\n    // Commit the batch\r\n    await batch.commit();\r\n\r\n    // Update the chat's last message if not updating the last message\r\n    if (!updateLastMessage) {\r\n        await this.updateChatLastMessage(chatRef, classroomId);\r\n    }\r\n}\r\n\r\nprivate async updateLastMessageInBatch(\r\n    chatRef: AngularFirestoreDocument<any>,\r\n    updatedMessage: any,\r\n    user: any,\r\n    isLastMessageDeleted: boolean,\r\n    messageId: string | undefined,\r\n    batch: any\r\n): Promise<void> {\r\n    const updateData: any = {\r\n        lastMessage: updatedMessage.content,\r\n        lastMessageAt: serverTimestamp(),\r\n        lastMessageFrom: updatedMessage.senderId,\r\n        lastMessageFromImage: this.userService.getUserPhoto(user?.avatarUrl),\r\n        lastMessageFromName: this.generalService.getPersonFullName(user),\r\n        lastMessageIsFile: updatedMessage.isFileFromLibrary || updatedMessage.isUploadFile,\r\n        lastMessageDeleted: isLastMessageDeleted,\r\n        lastMessageId: messageId\r\n    };\r\n\r\n    // Get snapshot of the chat document\r\n    const snapshot = await firstValueFrom(chatRef.get());\r\n\r\n    // Prepare updated unread counts\r\n    const updatedUnreadCounts: any = {};\r\n    const usersInChat = [this.currentRoom().teacher, ...this.currentRoom().classroomStudents];\r\n    const currentUnreadCounts = snapshot.exists ? snapshot.data()?.unreadCounts || {} : {};\r\n\r\n    usersInChat.forEach((chatUser: any) => {\r\n        if (chatUser.aspUserId !== updatedMessage.senderId) {\r\n            updatedUnreadCounts[chatUser.aspUserId] = (currentUnreadCounts[chatUser.aspUserId] || 0) + 1; // Increment unread count\r\n        }\r\n    });\r\n\r\n    // Only update if there are changes to the unread counts\r\n    if (Object.keys(updatedUnreadCounts).length > 0) {\r\n        updateData.unreadCounts = updatedUnreadCounts;\r\n    }\r\n\r\n    // Perform a single update to the chat document in the batch\r\n    batch.update(chatRef.ref, updateData);\r\n}\r\n\r\n  private updateChatLastMessage(chatRef: AngularFirestoreDocument<any>, classroomId: string): Promise<void> {\r\n    return this.getLatestMessage(classroomId).then((snapshot: any | null) => {\r\n\r\n      console.log(snapshot);\r\n      if (snapshot) {\r\n        const latestMessage = snapshot;\r\n        return chatRef.update({\r\n          lastMessage: latestMessage.content,\r\n          lastMessageAt: latestMessage.createdAt,\r\n          lastMessageFrom: latestMessage.senderId,\r\n          lastMessageFromName: latestMessage.senderName,\r\n          lastMessageFromImage: latestMessage.senderImage,\r\n          lastMessageIsFile: latestMessage.isFileFromLibrary || latestMessage.isUploadFile,\r\n          lastMessageDeleted: false,\r\n          lastMessageId: latestMessage.id,\r\n        });\r\n      } else {\r\n        // Handle case where there are no messages left in the collection\r\n        return chatRef.update({\r\n          lastMessage: '',\r\n          lastMessageAt: null,\r\n          lastMessageFrom: '',\r\n          lastMessageFromName: '',\r\n          lastMessageFromImage: '',\r\n          lastMessageIsFile: false,\r\n          lastMessageDeleted: false,\r\n          lastMessageId: '',\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  getLatestMessage(classroomId: string): Promise<Message | null> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    return firstValueFrom(chatRef.collection('messages', ref => ref.orderBy('timestamp', 'desc').limit(1)).get()).then(snapshot => {\r\n      if (!snapshot.empty) {\r\n        const latestMessage = snapshot.docs[0].data() as Message;\r\n        return latestMessage;\r\n      } else {\r\n        return null; // No messages found\r\n      }\r\n    });\r\n  }\r\n\r\n  // Update a chat room document\r\n  updateChatRoom(classroomId: string, data: Partial<Chat>): Promise<void> {\r\n    return this.chatsRef.doc(classroomId).update(data);\r\n  }\r\n\r\n  // Delete a chat room document\r\n  deleteChatRoom(classroomId: string): Promise<void> {\r\n    return this.chatsRef.doc(classroomId).delete();\r\n  }\r\n\r\n  addUnreadMessage(classroomId: string, message: Message): Promise<void> {\r\n    const messageRef = this.db.collection(`${this.dbUnreadPath}/${classroomId}/messages`).doc(message.id);\r\n\r\n    // Set the initial readStatus for the new message\r\n    const initialReadStatus = {\r\n      // Initialize the readStatus for all users, defaulting to false (unread)\r\n      [message.senderId]: false, // Mark the sender as false (unread)\r\n      // You can add more user IDs here if needed, depending on your application logic\r\n    };\r\n\r\n    return messageRef.set({\r\n      readStatus: initialReadStatus, // Add the readStatus field\r\n      timestamp: serverTimestamp(), // Ensure there's a timestamp for ordering\r\n    });\r\n  }\r\n\r\n  markMessagesAsRead(classroomId: string, userId: string): Promise<void> {\r\n    return this.db.collection(`${this.dbUnreadPath}/${classroomId}/messages`).get().toPromise().then(querySnapshot => {\r\n      const batch = this.db.firestore.batch();\r\n      let hasUpdates = false; // Flag to track if there are any updates\r\n\r\n      querySnapshot!.forEach(doc => {\r\n        const data = doc.data();\r\n\r\n        // Ensure readStatus is a valid map or initialize it\r\n        const readStatusUpdate = (data as any).readStatus || {};\r\n\r\n        // Only update if the userId is not already marked as read\r\n        if (!readStatusUpdate[userId]) {\r\n          readStatusUpdate[userId] = true;\r\n          // Update the document with the new or modified readStatus map\r\n          batch.update(doc.ref, { readStatus: readStatusUpdate });\r\n          hasUpdates = true; // Set the flag to true if an update is made\r\n        }\r\n      });\r\n\r\n      // Only commit the batch if there are updates\r\n      if (hasUpdates) {\r\n        return batch.commit();\r\n      }\r\n\r\n      // If no updates were made, resolve immediately\r\n      return Promise.resolve();\r\n    });\r\n  }\r\n\r\n  // Mark messages as read\r\n  markAllMessagesAsRead(classroomId: string): Promise<void> {\r\n    return this.db.collection(`${this.dbUnreadPath}/${classroomId}/messages`).get().toPromise().then(querySnapshot => {\r\n      const batch = this.db.firestore.batch();\r\n      querySnapshot!.forEach(doc => {\r\n        const messageData = doc.data() as Message;\r\n        const updatedReadStatus = {};\r\n\r\n        // Set readStatus for all users in the message's readStatus map to true\r\n        for (const userId in messageData.readStatus) {\r\n          if (messageData.readStatus.hasOwnProperty(userId)) {\r\n            const a = `readStatus.${userId}`;\r\n            (updatedReadStatus as any)[a] = true;\r\n          }\r\n        }\r\n\r\n        batch.update(doc.ref, updatedReadStatus);\r\n      });\r\n      return batch.commit();\r\n    });\r\n  }\r\n\r\n  // Get unread messages count for a user in a classroom\r\n  getUnreadMessagesCount(classroomId: string, userId: string): Observable<number> {\r\n    return this.db.collection<Message>(`${this.dbUnreadPath}/${classroomId}/messages`).snapshotChanges().pipe(\r\n      map(actions => actions.filter(action => {\r\n        const data = action.payload.doc.data() as Message;\r\n        return !data.readStatus.hasOwnProperty(userId);\r\n      }).length)\r\n    );\r\n  }\r\n\r\n\r\n  // Get total unread messages count for a user across all classrooms\r\n  getUnreadMessagesCountForUser(userId: string): Observable<number> {\r\n    return this.chatsRef.snapshotChanges().pipe(\r\n      switchMap(chatSnapshots => {\r\n        const chatObservables = chatSnapshots.map(snapshot => {\r\n          const chatId = snapshot.payload.doc.id;\r\n          return this.getUnreadMessagesCount(chatId, userId);\r\n        });\r\n        return combineLatest(chatObservables);\r\n      }),\r\n      map(unreadCounts => unreadCounts.reduce((acc, count) => acc + count, 0))\r\n    );\r\n  }\r\n\r\n  // Or, to get the user object directly\r\n  getFireBaseUser(): Observable<any> {\r\n    return this.afAuth.authState;\r\n  }\r\n\r\n  setUserStatus(userId: string, status: 'online' | 'offline'): Promise<void> {\r\n    const userRef = this.usersRef.doc(userId);\r\n    return userRef.set({ status, lastOnlineTime: Timestamp.now() }, { merge: true });\r\n  }\r\n\r\n  setUserLastOnlineTime(userId: string, time: FieldValue): Promise<void> {\r\n    const userRef = this.usersRef.doc(userId);\r\n    return userRef.set({ status: 'online', lastOnlineTime: time }, { merge: true });\r\n  }\r\n\r\n  getUserStatus(userId: string): Observable<'online' | 'offline'> {\r\n    return this.usersRef.doc<ChatUserStatus>(userId).valueChanges().pipe(\r\n      map(user => user?.status || 'offline')\r\n    );\r\n  }\r\n\r\n  getOnlineUsers(): Observable<ChatUserStatus[]> {\r\n    return this.db.collection<ChatUserStatus>('/users', ref => ref.where('status', '==', 'online'))\r\n      .snapshotChanges().pipe(\r\n        map(actions => actions.map(a => {\r\n          const data = a.payload.doc.data() as ChatUserStatus;\r\n          const id = a.payload.doc.id;\r\n          return { ...data, id }; // Corrected merge of id and data\r\n        }))\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Retrieves all users in the Firestore 'users' collection, including their status compared to the last online time.\r\n   * The status is determined by comparing the current time with the user's last online time. If the user was online\r\n   * within the last `secondsToCheckOnlineStatus` seconds, the status is set to 'online', otherwise it is set to 'offline'.\r\n   * If the user does not have a last online time, the status is set to 'offline'.\r\n   * @returns An Observable of an array of ChatUserStatus objects, where each object contains the user's id, status, and last online time.\r\n   */\r\n  getOnlineUsersComparedToLastOnlineTime(): Observable<ChatUserStatus[]> {\r\n    return this.db.collection<ChatUserStatus>('/users')\r\n      .snapshotChanges().pipe(\r\n        map(actions => {\r\n          const currentTime = Timestamp.now();\r\n          return actions.map(a => {\r\n            const data = a.payload.doc.data() as ChatUserStatus;\r\n            const id = a.payload.doc.id;\r\n            const lastOnlineTime = data.lastOnlineTime as Timestamp;\r\n            if (lastOnlineTime) {\r\n              if (!currentTime.seconds || !lastOnlineTime.seconds) {\r\n                return { ...data, id, status: 'offline' };\r\n              }\r\n              const timeDifference = currentTime?.seconds - lastOnlineTime?.seconds;\r\n\r\n              // Check if the user was online within the last 10 seconds\r\n              if (timeDifference <= this.secondsToCheckOnlineStatus) {\r\n                return { ...data, id, status: 'online' };\r\n              } else {\r\n                return { ...data, id, status: 'offline' };\r\n              }\r\n            }\r\n            return { ...data, id, status: 'offline' };\r\n\r\n          });\r\n        })\r\n      );\r\n  }\r\n\r\n}\r\n"], "mappings": ";AAAA,SAAqBA,MAAM,EAAEC,MAAM,QAAQ,eAAe;AAG1D,SAAqBC,eAAe,EAAEC,SAAS,QAAQ,oBAAoB;AAC3E,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,UAAU,EAAEC,aAAa,EAASC,cAAc,QAAwB,MAAM;AACvF,SAASC,GAAG,EAAEC,SAAS,QAAQ,gBAAgB;AAC/C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,WAAW,QAAQ,8BAA8B;AAG1D,SAASC,OAAO,EAAEC,iBAAiB,QAAQ,eAAe;;;;;AAF1D,MAAMC,WAAW,GAAGH,WAAW,CAACI,MAAM;AACtC,MAAMC,eAAe,GAAGL,WAAW,CAACI,MAAM,GAAG,OAAO;AAuCpD,OAAM,MAAOE,WAAW;EAgBtBC,YAAoBC,EAAoB,EAAUC,MAAuB,EAAUC,IAAgB;IAA/E,KAAAF,EAAE,GAAFA,EAAE;IAA4B,KAAAC,MAAM,GAANA,MAAM;IAA2B,KAAAC,IAAI,GAAJA,IAAI;IAfvF,KAAAC,WAAW,GAAGvB,MAAM,CAACU,WAAW,CAAC;IACjC,KAAAc,cAAc,GAAGxB,MAAM,CAACW,cAAc,CAAC;IACvC,KAAAc,aAAa,GAAGxB,MAAM,CAACW,WAAW,CAACa,aAAa,CAAC;IACjD,KAAAC,cAAc,GAAGzB,MAAM,CAAC,KAAK,CAAC;IAC9B,KAAA0B,WAAW,GAAG1B,MAAM,CAAC,CAAC,CAAC;IACvB,KAAA2B,WAAW,GAAG3B,MAAM,CAAC,EAAS,CAAC;IAC/B,KAAA4B,cAAc,GAAG5B,MAAM,CAAC,KAAK,CAAC;IACtB,KAAA6B,MAAM,GAAG,QAAQ;IACjB,KAAAC,YAAY,GAAG,QAAQ;IAEvB,KAAAC,QAAQ,GAA+C,IAAI,CAACZ,EAAE,CAACa,UAAU,CAAC,OAAO,CAAC;IAC1F,KAAAC,SAAS,GAAG,CAAC;IACJ,KAAAC,0BAA0B,GAAG,EAAE;IAItC,IAAI,CAACC,QAAQ,GAAGhB,EAAE,CAACa,UAAU,CAAC,IAAI,CAACH,MAAM,CAAC;IAC1C,IAAI,CAACE,QAAQ,GAAGZ,EAAE,CAACa,UAAU,CAAC,QAAQ,CAAC;EACzC;EAGAI,gCAAgCA,CAACC,OAAY;IAC3CC,OAAO,CAACC,GAAG,CAACF,OAAO,CAAC;IACpB,OAAO,IAAI,CAAChB,IAAI,CAACmB,IAAI,CAAMxB,eAAe,GAAG,2CAA2C,GAAGqB,OAAO,EAAE,EAAE,CAAC;EACzG;EAEA;EACAxB,iBAAiBA,CAAA;IACf,OAAO,IAAIT,UAAU,CAACqC,QAAQ,IAAG;MAC/B,IAAI,CAACrB,MAAM,CAACP,iBAAiB,EAAE,CAC5B6B,IAAI,CAACC,cAAc,IAAG;QACrBL,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEI,cAAc,CAACC,IAAI,CAAC;QAC1DH,QAAQ,CAACI,IAAI,CAACF,cAAc,CAAC;QAC7BF,QAAQ,CAACK,QAAQ,EAAE;MACrB,CAAC,CAAC,CACDC,KAAK,CAACC,KAAK,IAAG;QACbV,OAAO,CAACU,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrDP,QAAQ,CAACO,KAAK,CAACA,KAAK,CAAC;MACvB,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;EAEAC,YAAYA,CAAA;IACV,MAAMC,IAAI,GAAGtC,OAAO,EAAE;IACtBC,iBAAiB,CAACqC,IAAI,CAAC,CACpBR,IAAI,CAAC,MAAK;MACT;IAAA,CACD,CAAC,CACDK,KAAK,CAAEC,KAAK,IAAI;MACf,MAAMG,SAAS,GAAGH,KAAK,CAACI,IAAI;MAC5B,MAAMC,YAAY,GAAGL,KAAK,CAACM,OAAO;MAClC;IACF,CAAC,CAAC;EACN;EAEAC,cAAcA,CAACC,WAAmB;IAChC,MAAMC,OAAO,GAAG,IAAI,CAACtB,QAAQ,CAACuB,GAAG,CAAU,GAAGF,WAAW,GAAG,CAAC;IAE7D,OAAOC,OAAO,CAACE,eAAe,EAAE,CAACC,IAAI,CACnCrD,GAAG,CAACsD,MAAM,IAAG;MACX,MAAMC,IAAI,GAAGD,MAAM,CAACE,OAAO,CAACD,IAAI,EAAa;MAC7C,MAAME,EAAE,GAAGH,MAAM,CAACE,OAAO,CAACC,EAAE;MAC5B,OAAO;QAAEA,EAAE;QAAE,GAAGF;MAAI,CAAE;IACxB,CAAC,CAAC,CACH;EACH;EAEA;EACAG,iBAAiBA,CAACT,WAAmB;IACnC,MAAMC,OAAO,GAAG,IAAI,CAACtB,QAAQ,CAACuB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IACnD,OAAO,IAAI,CAACrB,QAAQ,CAACuB,GAAG,CAACF,WAAW,CAAC,CAACxB,UAAU,CAAO,OAAO,CAAC,CAC5D2B,eAAe,EAAE,CAACC,IAAI,CACrBrD,GAAG,CAAC2D,OAAO,IAAIA,OAAO,CAAC3D,GAAG,CAAC4D,CAAC,IAAG;MAC7B,MAAML,IAAI,GAAGK,CAAC,CAACJ,OAAO,CAACL,GAAG,CAACI,IAAI,EAAU;MACzC,MAAME,EAAE,GAAGG,CAAC,CAACJ,OAAO,CAACL,GAAG,CAACM,EAAE;MAC3B,OAAO;QAAEA,EAAE;QAAE,GAAGF;MAAI,CAAE;IACxB,CAAC,CAAC,CAAC,CACJ;EACL;EAEA;EACAM,eAAeA,CAACC,MAAc,EAAEC,KAAa,EAAEC,WAAqB;IAClE,MAAMC,aAAa,GAAG,IAAI,CAACrD,EAAE,CAACa,UAAU,CAAU,GAAG,IAAI,CAACH,MAAM,IAAIwC,MAAM,WAAW,EAAEI,GAAG,IAAG;MAC3F,IAAIC,KAAK,GAAGD,GAAG,CAACE,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAACL,KAAK,CAACA,KAAK,CAAC;MACzD,IAAIC,WAAW,EAAE;QACfG,KAAK,GAAGA,KAAK,CAACE,UAAU,CAACL,WAAW,CAACM,SAAS,CAAC;MACjD;MACA,OAAOH,KAAK;IACd,CAAC,CAAC;IAEF,OAAOF,aAAa,CAACb,eAAe,EAAE,CAACC,IAAI,CACzCrD,GAAG,CAAC2D,OAAO,IAAIA,OAAO,CAAC3D,GAAG,CAAC4D,CAAC,IAAG;MAC7B,MAAML,IAAI,GAAGK,CAAC,CAACJ,OAAO,CAACL,GAAG,CAACI,IAAI,EAAa;MAC5C,MAAME,EAAE,GAAGG,CAAC,CAACJ,OAAO,CAACL,GAAG,CAACM,EAAE;MAC3B,OAAO;QAAEA,EAAE;QAAE,GAAGF;MAAI,CAAE;IACxB,CAAC,CAAC,CAAC,CACJ;EACH;EAEA;EACAgB,cAAcA,CAACtB,WAAmB,EAAEuB,YAAsB;IACxD,MAAMC,IAAI,GAAS;MACjBxB,WAAW,EAAEA,WAAW;MACxByB,SAAS,EAAE9E,MAAM,EAAE,CAAC+E,MAAM,CAAC,qBAAqB,CAAC;MACjDX,WAAW,EAAE,EAAE;MACfY,aAAa,EAAElF,eAAe,EAAE;MAChCmF,eAAe,EAAE,EAAE;MACnBC,mBAAmB,EAAE,EAAE;MACvBC,oBAAoB,EAAE,EAAE;MACxBC,iBAAiB,EAAE,KAAK;MACxBC,kBAAkB,EAAE,KAAK;MACzBC,aAAa,EAAE,EAAE;MACjBC,sBAAsB,EAAE;KACzB;IACD,OAAO,IAAI,CAACvD,QAAQ,CAACwD,GAAG,CAACX,IAAI,CAAC;EAChC;EAEA;EACAY,UAAUA,CAACpC,WAAmB,EAAEF,OAAgB,EAAEV,IAAS;IACzD,MAAMa,OAAO,GAAG,IAAI,CAACtB,QAAQ,CAACuB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IACnD,MAAMqC,UAAU,GAAGpC,OAAO,CAACzB,UAAU,CAAC,UAAU,CAAC,CAAC0B,GAAG,EAAE;IAEvD,MAAMoC,SAAS,GAAID,UAAU,CAACpB,GAAG,CAACT,EAAG;IACrC,OAAO1D,cAAc,CAACmD,OAAO,CAACsC,GAAG,EAAE,CAAC,CAACrD,IAAI,CAACsD,QAAQ,IAAG;MACnD,MAAMC,UAAU,GAAQ;QACtBhB,SAAS,EAAE9E,MAAM,EAAE,CAAC+E,MAAM,CAAC,qBAAqB,CAAC;QACjDX,WAAW,EAAEjB,OAAO,CAAC4C,OAAO;QAC5Bf,aAAa,EAAElF,eAAe,EAAE;QAChCuD,WAAW,EAAEA,WAAW;QACxB4B,eAAe,EAAE9B,OAAO,CAAC6C,QAAQ;QACjCd,mBAAmB,EAAE,IAAI,CAAC9D,cAAc,CAAC6E,iBAAiB,CAACxD,IAAI,CAAC;QAChE0C,oBAAoB,EAAE,IAAI,CAAChE,WAAW,CAAC+E,YAAY,CAACzD,IAAI,EAAE0D,SAAS,CAAC;QACpEf,iBAAiB,EAAEjC,OAAO,CAACiD,iBAAiB,IAAIjD,OAAO,CAACkD,YAAY;QACpEhB,kBAAkB,EAAE,KAAK;QACzBC,aAAa,EAAEK,SAAS;QAAE;QAC1BJ,sBAAsB,EAAE;OACzB;MAED,IAAI,CAACM,QAAQ,CAACS,MAAM,EAAE;QACpB;QACA,MAAMC,WAAW,GAAG,CAAC,IAAI,CAAC/E,WAAW,EAAE,CAACgF,OAAO,EAAE,GAAG,IAAI,CAAChF,WAAW,EAAE,CAACiF,iBAAiB,CAAC;QACzF,MAAMC,mBAAmB,GAAQ,EAAE;QACnCH,WAAW,CAACI,OAAO,CAAEC,QAAa,IAAI;UACpC,IAAIA,QAAQ,CAACC,SAAS,KAAK1D,OAAO,CAAC6C,QAAQ,EAAE;YAC3CU,mBAAmB,CAACE,QAAQ,CAACC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;UAC/C;QACF,CAAC,CAAC;QACFf,UAAU,CAACgB,YAAY,GAAGJ,mBAAmB;QAE7C,OAAOpD,OAAO,CAACyD,GAAG,CAACjB,UAAU,CAAC,CAACvD,IAAI,CAAC,MAAK;UACvC,OAAOmD,UAAU,CAACqB,GAAG,CAAC5D,OAAO,CAAC,CAACZ,IAAI,CAAC,MAAK;YACvC,OAAOmD,UAAU,CAACsB,MAAM,CAAC;cAAEnD,EAAE,EAAE6B,UAAU,CAACpB,GAAG,CAACT;YAAE,CAAE,CAAC,CAACtB,IAAI,CAAC,MAAK;cAC5D,OAAO,IAAI,CAAC0E,qBAAqB,CAAC3D,OAAO,EAAED,WAAW,CAAC;YACzD,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,OAAO,IAAI,CAAC6D,iBAAiB,CAAC5D,OAAO,EAAEH,OAAO,EAAEV,IAAI,EAAE,KAAK,EAAEkD,SAAS,CAAC,CAACpD,IAAI,CAAC,MAAK;UAChF,OAAOmD,UAAU,CAACqB,GAAG,CAAC5D,OAAO,CAAC,CAACZ,IAAI,CAAC,MAAK;YACvC,OAAOmD,UAAU,CAACsB,MAAM,CAAC;cAAEnD,EAAE,EAAE6B,UAAU,CAACpB,GAAG,CAACT;YAAE,CAAE,CAAC;UACrD,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EAEMsD,sBAAsBA,CAC1B9D,WAAmB,EACnBF,OAAgB,EAChBV,IAAS;IAAA,IAAA2E,KAAA;IAAA,OAAAC,iBAAA;MAET,MAAM/D,OAAO,GAAG8D,KAAI,CAACpF,QAAQ,CAACuB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;MACnD,MAAMqC,UAAU,GAAGpC,OAAO,CAACzB,UAAU,CAAC,UAAU,CAAC,CAAC0B,GAAG,EAAE;MACvD,MAAM+D,iBAAiB,GAAGF,KAAI,CAACpG,EAAE,CAACa,UAAU,CAAC,GAAGuF,KAAI,CAACzF,YAAY,IAAI0B,WAAW,WAAW,CAAC;MAE5F,MAAMsC,SAAS,GAAGD,UAAU,CAACpB,GAAG,CAACT,EAAE;MACnC,MAAM0D,KAAK,GAAGH,KAAI,CAACpG,EAAE,CAACwG,SAAS,CAACD,KAAK,EAAE;MAEvC;MACA,MAAME,WAAW,GAAG;QAClB,GAAGtE,OAAO;QACVU,EAAE,EAAE8B,SAAS;QACbb,SAAS,EAAE9E,MAAM,EAAE,CAAC+E,MAAM,CAAC,qBAAqB;OACjD;MAED;MACAwC,KAAK,CAACR,GAAG,CAACrB,UAAU,CAACpB,GAAG,EAAEmD,WAAW,CAAC;MAEtC,MAAMC,YAAY,SAASvH,cAAc,CAACmD,OAAO,CAACsC,GAAG,EAAE,CAAC;MACxD,MAAME,UAAU,GAAQ;QACtB1B,WAAW,EAAEjB,OAAO,CAAC4C,OAAO;QAC5Bf,aAAa,EAAElF,eAAe,EAAE;QAChCmF,eAAe,EAAE9B,OAAO,CAAC6C,QAAQ;QACjCd,mBAAmB,EAAEkC,KAAI,CAAChG,cAAc,CAAC6E,iBAAiB,CAACxD,IAAI,CAAC;QAChE0C,oBAAoB,EAAEiC,KAAI,CAACjG,WAAW,CAAC+E,YAAY,CAACzD,IAAI,EAAE0D,SAAS,CAAC;QACpEf,iBAAiB,EAAEjC,OAAO,CAACiD,iBAAiB,IAAIjD,OAAO,CAACkD,YAAY;QACpEhB,kBAAkB,EAAE,KAAK;QACzBC,aAAa,EAAEK;OAChB;MAED,IAAI,CAAC+B,YAAY,CAACpB,MAAM,EAAE;QACxB;QACA,MAAMC,WAAW,GAAG,CAACa,KAAI,CAAC5F,WAAW,EAAE,CAACgF,OAAO,EAAE,GAAGY,KAAI,CAAC5F,WAAW,EAAE,CAACiF,iBAAiB,CAAC;QACzF,MAAMK,YAAY,GAA+B,EAAE;QAEnDP,WAAW,CAACI,OAAO,CAACC,QAAQ,IAAG;UAC7B,IAAIA,QAAQ,CAACC,SAAS,KAAK1D,OAAO,CAAC6C,QAAQ,EAAE;YAC3Cc,YAAY,CAACF,QAAQ,CAACC,SAAS,CAAC,GAAG,CAAC;UACtC;QACF,CAAC,CAAC;QAEFf,UAAU,CAACgB,YAAY,GAAGA,YAAY;QACtCS,KAAK,CAACR,GAAG,CAACzD,OAAO,CAACgB,GAAG,EAAEwB,UAAU,CAAC;MACpC,CAAC,MAAM;QACL;QACA,MAAM6B,YAAY,GAAGD,YAAY,CAAC/D,IAAI,EAAE;QACxC,MAAM+C,mBAAmB,GAAG;UAAE,GAAGiB,YAAY,EAAEb;QAAY,CAAE;QAE7Dc,MAAM,CAACC,IAAI,CAACnB,mBAAmB,CAAC,CAACC,OAAO,CAACmB,MAAM,IAAG;UAChD,IAAIA,MAAM,KAAK3E,OAAO,CAAC6C,QAAQ,EAAE;YAC/BU,mBAAmB,CAACoB,MAAM,CAAC,GAAG,CAACpB,mBAAmB,CAACoB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;UACtE;QACF,CAAC,CAAC;QAEFhC,UAAU,CAACgB,YAAY,GAAGJ,mBAAmB;QAC7Ca,KAAK,CAACP,MAAM,CAAC1D,OAAO,CAACgB,GAAG,EAAEwB,UAAU,CAAC;MACvC;MAEA;MACA,MAAMiC,cAAc,SAAS5H,cAAc,CAACmH,iBAAiB,CAAC1B,GAAG,EAAE,CAAC;MACpEmC,cAAc,CAACpB,OAAO,CAACpD,GAAG,IAAG;QAC3B,MAAMyE,OAAO,GAAGzE,GAAG,CAACI,IAAI,EAAS,CAAC,CAAG;QACrC,MAAMsE,UAAU,GAAGD,OAAO,CAACC,UAAU,IAAI,EAAE,CAAC,CAAC;QAE7C,IAAI,CAACA,UAAU,CAACxF,IAAI,CAACoE,SAAS,CAAC,EAAE;UAC/BoB,UAAU,CAACxF,IAAI,CAACoE,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC;UACnCU,KAAK,CAACP,MAAM,CAACzD,GAAG,CAACe,GAAG,EAAE;YAAE2D;UAAU,CAAE,CAAC,CAAC,CAAC;QACzC;MACF,CAAC,CAAC;MAIF;MACA,MAAMV,KAAK,CAACW,MAAM,EAAE;IAAC;EACvB;EAGQhB,iBAAiBA,CAAC5D,OAAsC,EAC9D6E,cAAuB,EAAE1F,IAAS,EAAE2F,oBAAoB,GAAG,KAAK,EAAEzC,SAAS,GAAG,EAAE;IAEhF,MAAMD,UAAU,GAAGpC,OAAO,CAACzB,UAAU,CAAC,UAAU,CAAC,CAAC0B,GAAG,EAAE;IACvD,MAAMuC,UAAU,GAAQ;MACtB1B,WAAW,EAAE+D,cAAc,CAACpC,OAAO;MACnCf,aAAa,EAAElF,eAAe,EAAE;MAChCmF,eAAe,EAAEkD,cAAc,CAACnC,QAAQ;MACxCb,oBAAoB,EAAE,IAAI,CAAChE,WAAW,CAAC+E,YAAY,CAACzD,IAAI,EAAE0D,SAAS,CAAC;MACpEjB,mBAAmB,EAAE,IAAI,CAAC9D,cAAc,CAAC6E,iBAAiB,CAACxD,IAAI,CAAC;MAChE2C,iBAAiB,EAAE+C,cAAc,CAAC/B,iBAAiB,IAAI+B,cAAc,CAAC9B,YAAY;MAClFhB,kBAAkB,EAAE+C,oBAAoB;MACxC9C,aAAa,EAAEK;KAChB;IAED,OAAOxF,cAAc,CAACmD,OAAO,CAACsC,GAAG,EAAE,CAAC,CAACrD,IAAI,CAACsD,QAAQ,IAAG;MACnD1D,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAACZ,WAAW,EAAE,CAAC;MAC/C,MAAMkF,mBAAmB,GAAQ,EAAE;MACnC;MACA,MAAMH,WAAW,GAAG,CAAC,IAAI,CAAC/E,WAAW,EAAE,CAACgF,OAAO,EAAE,GAAG,IAAI,CAAChF,WAAW,EAAE,CAACiF,iBAAiB,CAAC;MAEzFF,WAAW,CAACI,OAAO,CAAEC,QAAa,IAAI;QACpC,IAAIA,QAAQ,CAACC,SAAS,KAAKsB,cAAc,CAACnC,QAAQ,EAAE;UAClDU,mBAAmB,CAACE,QAAQ,CAACC,SAAS,CAAC,GAAG,CAAChB,QAAS,CAACS,MAAM,GAAIT,QAAS,CAAClC,IAAI,EAAU,CAACmD,YAAY,GAAGF,QAAQ,CAACC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;QAC1I;MACF,CAAC,CAAC;MAEFf,UAAU,CAACgB,YAAY,GAAGJ,mBAAmB;MAC7C,OAAOpD,OAAO,CAAC0D,MAAM,CAAClB,UAAU,CAAC;IACnC,CAAC,CAAC;EACJ;EAEAuC,WAAWA,CAAChF,WAAmB,EAAEsC,SAAiB,EAAEwC,cAAuB,EAAE1F,IAAS,EAAEyE,iBAAiB,GAAG,KAAK;IAC/G,MAAM5D,OAAO,GAAG,IAAI,CAACtB,QAAQ,CAACuB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IACnD,MAAMqC,UAAU,GAAGpC,OAAO,CAACzB,UAAU,CAAC,UAAU,CAAC,CAAC0B,GAAG,CAAC,GAAGoC,SAAS,EAAE,CAAC;IACrE,MAAM2C,WAAW,GAAGhF,OAAO,CAACzB,UAAU,CAAC,UAAU,EAAEyC,GAAG,IAAIA,GAAG,CAACE,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAACL,KAAK,CAAC,CAAC,CAAC,CAAC;IAEpG,MAAMoE,cAAc,GAAGD,WAAW,CAAChE,GAAG,CAACf,GAAG,EAAE;IAC5C,MAAM+B,aAAa,GAAGiD,cAAc,CAAC1E,EAAE;IACvC1B,OAAO,CAACC,GAAG,CAACkD,aAAa,CAAC;IAC1B,OAAOI,UAAU,CAACE,GAAG,EAAE,CAAC4C,SAAS,EAAE,CAACjG,IAAI,CAACsD,QAAQ,IAAG;MAClD,IAAIA,QAAS,CAACS,MAAM,EAAE;QAGpB,OAAOZ,UAAU,CAACsB,MAAM,CAAC;UACvB,GAAGmB;SACJ,CAAC,CAAC5F,IAAI,CAAC,MAAK;UACX,IAAI2E,iBAAiB,EAAE;YACrB,OAAO,IAAI,CAACA,iBAAiB,CAAC5D,OAAO,EAAE6E,cAAc,EAAE1F,IAAI,EAAE,KAAK,EAAE0F,cAAc,CAACtE,EAAG,CAAC;UACzF;UACA,OAAO4E,OAAO,CAACC,OAAO,EAAE;QAC1B,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;MAC3C;IACF,CAAC,CAAC;EACJ;EAEAC,iCAAiCA,CAACvF,WAAmB,EAAEyE,MAAc;IACnE,MAAMxE,OAAO,GAAG,IAAI,CAACtB,QAAQ,CAACuB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IAEnD,OAAOC,OAAO,CAACuF,YAAY,EAAE,CAACpF,IAAI,CAChCrD,GAAG,CAACuD,IAAI,IAAG;MACT,MAAMmD,YAAY,GAAInD,IAAY,EAAEmD,YAAY;MAChD,OAAOA,YAAY,GAAGA,YAAY,CAACgB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;IACrD,CAAC,CAAC,CACH;EACH;EAEAgB,sBAAsBA,CAACzF,WAAmB,EAAEyE,MAAc;IACxD,MAAMxE,OAAO,GAAG,IAAI,CAACtB,QAAQ,CAACuB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IACnD,OAAO,IAAI,CAAC0F,mCAAmC,CAACzF,OAAO,EAAEwE,MAAM,CAAC;EAClE;EAEAiB,mCAAmCA,CAACC,sBAAqD,EAAElB,MAAc;IACvG,OAAOkB,sBAAsB,CAACpD,GAAG,EAAE,CAAC4C,SAAS,EAAE,CAACjG,IAAI,CAACsD,QAAQ,IAAG;MAC9D,IAAIA,QAAS,CAACS,MAAM,EAAE;QACpB,MAAM3C,IAAI,GAAGkC,QAAS,CAAClC,IAAI,EAAS;QACpC,MAAMsF,YAAY,GAAGtF,IAAI,CAACmD,YAAY,GAAGgB,MAAM,CAAC,IAAI,CAAC;QAErD;QACA,IAAImB,YAAY,GAAG,CAAC,EAAE;UACpB,MAAMvC,mBAAmB,GAAG;YAAE,GAAG/C,IAAI,CAACmD,YAAY;YAAE,CAACgB,MAAM,GAAG;UAAC,CAAE;UACjE,OAAOkB,sBAAsB,CAAChC,MAAM,CAAC;YAAEF,YAAY,EAAEJ;UAAmB,CAAE,CAAC;QAC7E;MACF;MACA,OAAO+B,OAAO,CAACC,OAAO,EAAE,CAAC,CAAC;IAC5B,CAAC,CAAC;EACJ;EAEA;EACMQ,aAAaA,CAAC7F,WAAmB,EAAEF,OAAY,EAAEV,IAAS,EAAEyE,iBAAiB,GAAG,KAAK;IAAA,IAAAiC,MAAA;IAAA,OAAA9B,iBAAA;MACzFlF,OAAO,CAACC,GAAG,CAACiB,WAAW,CAAC;MACxBlB,OAAO,CAACC,GAAG,CAACe,OAAO,CAAC;MAEpB,MAAMG,OAAO,GAAG6F,MAAI,CAACnH,QAAQ,CAACuB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;MACnD,MAAMqC,UAAU,GAAGpC,OAAO,CAACzB,UAAU,CAAC,UAAU,CAAC,CAAC0B,GAAG,CAACJ,OAAO,CAACU,EAAE,CAAC;MAEjE;MACA,MAAM0D,KAAK,GAAG4B,MAAI,CAACnI,EAAE,CAACwG,SAAS,CAACD,KAAK,EAAE;MAEvC;MACAA,KAAK,CAAC6B,MAAM,CAAC1D,UAAU,CAACpB,GAAG,CAAC;MAE5B,IAAI4C,iBAAiB,EAAE;QACnB,MAAMmC,aAAa,SAASF,MAAI,CAACG,gBAAgB,CAACjG,WAAW,CAAC;QAC9D,MAAMyC,UAAU,GAAG;UACf,GAAG3C,OAAO;UACV4C,OAAO,EAAE;SACZ;QAED,MAAMoD,MAAI,CAACI,wBAAwB,CAACjG,OAAO,EAAEwC,UAAU,EAAErD,IAAI,EAAE,IAAI,EAAE4G,aAAa,EAAExF,EAAE,EAAE0D,KAAK,CAAC;MAClG;MAEA;MACA,MAAMA,KAAK,CAACW,MAAM,EAAE;MAEpB;MACA,IAAI,CAAChB,iBAAiB,EAAE;QACpB,MAAMiC,MAAI,CAAClC,qBAAqB,CAAC3D,OAAO,EAAED,WAAW,CAAC;MAC1D;IAAC;EACL;EAEckG,wBAAwBA,CAClCjG,OAAsC,EACtC6E,cAAmB,EACnB1F,IAAS,EACT2F,oBAA6B,EAC7BzC,SAA6B,EAC7B4B,KAAU;IAAA,IAAAiC,MAAA;IAAA,OAAAnC,iBAAA;MAEV,MAAMvB,UAAU,GAAQ;QACpB1B,WAAW,EAAE+D,cAAc,CAACpC,OAAO;QACnCf,aAAa,EAAElF,eAAe,EAAE;QAChCmF,eAAe,EAAEkD,cAAc,CAACnC,QAAQ;QACxCb,oBAAoB,EAAEqE,MAAI,CAACrI,WAAW,CAAC+E,YAAY,CAACzD,IAAI,EAAE0D,SAAS,CAAC;QACpEjB,mBAAmB,EAAEsE,MAAI,CAACpI,cAAc,CAAC6E,iBAAiB,CAACxD,IAAI,CAAC;QAChE2C,iBAAiB,EAAE+C,cAAc,CAAC/B,iBAAiB,IAAI+B,cAAc,CAAC9B,YAAY;QAClFhB,kBAAkB,EAAE+C,oBAAoB;QACxC9C,aAAa,EAAEK;OAClB;MAED;MACA,MAAME,QAAQ,SAAS1F,cAAc,CAACmD,OAAO,CAACsC,GAAG,EAAE,CAAC;MAEpD;MACA,MAAMc,mBAAmB,GAAQ,EAAE;MACnC,MAAMH,WAAW,GAAG,CAACiD,MAAI,CAAChI,WAAW,EAAE,CAACgF,OAAO,EAAE,GAAGgD,MAAI,CAAChI,WAAW,EAAE,CAACiF,iBAAiB,CAAC;MACzF,MAAMgD,mBAAmB,GAAG5D,QAAQ,CAACS,MAAM,GAAGT,QAAQ,CAAClC,IAAI,EAAE,EAAEmD,YAAY,IAAI,EAAE,GAAG,EAAE;MAEtFP,WAAW,CAACI,OAAO,CAAEC,QAAa,IAAI;QAClC,IAAIA,QAAQ,CAACC,SAAS,KAAKsB,cAAc,CAACnC,QAAQ,EAAE;UAChDU,mBAAmB,CAACE,QAAQ,CAACC,SAAS,CAAC,GAAG,CAAC4C,mBAAmB,CAAC7C,QAAQ,CAACC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAClG;MACJ,CAAC,CAAC;MAEF;MACA,IAAIe,MAAM,CAACC,IAAI,CAACnB,mBAAmB,CAAC,CAACgD,MAAM,GAAG,CAAC,EAAE;QAC7C5D,UAAU,CAACgB,YAAY,GAAGJ,mBAAmB;MACjD;MAEA;MACAa,KAAK,CAACP,MAAM,CAAC1D,OAAO,CAACgB,GAAG,EAAEwB,UAAU,CAAC;IAAC;EAC1C;EAEUmB,qBAAqBA,CAAC3D,OAAsC,EAAED,WAAmB;IACvF,OAAO,IAAI,CAACiG,gBAAgB,CAACjG,WAAW,CAAC,CAACd,IAAI,CAAEsD,QAAoB,IAAI;MAEtE1D,OAAO,CAACC,GAAG,CAACyD,QAAQ,CAAC;MACrB,IAAIA,QAAQ,EAAE;QACZ,MAAMwD,aAAa,GAAGxD,QAAQ;QAC9B,OAAOvC,OAAO,CAAC0D,MAAM,CAAC;UACpB5C,WAAW,EAAEiF,aAAa,CAACtD,OAAO;UAClCf,aAAa,EAAEqE,aAAa,CAACvE,SAAS;UACtCG,eAAe,EAAEoE,aAAa,CAACrD,QAAQ;UACvCd,mBAAmB,EAAEmE,aAAa,CAACM,UAAU;UAC7CxE,oBAAoB,EAAEkE,aAAa,CAACO,WAAW;UAC/CxE,iBAAiB,EAAEiE,aAAa,CAACjD,iBAAiB,IAAIiD,aAAa,CAAChD,YAAY;UAChFhB,kBAAkB,EAAE,KAAK;UACzBC,aAAa,EAAE+D,aAAa,CAACxF;SAC9B,CAAC;MACJ,CAAC,MAAM;QACL;QACA,OAAOP,OAAO,CAAC0D,MAAM,CAAC;UACpB5C,WAAW,EAAE,EAAE;UACfY,aAAa,EAAE,IAAI;UACnBC,eAAe,EAAE,EAAE;UACnBC,mBAAmB,EAAE,EAAE;UACvBC,oBAAoB,EAAE,EAAE;UACxBC,iBAAiB,EAAE,KAAK;UACxBC,kBAAkB,EAAE,KAAK;UACzBC,aAAa,EAAE;SAChB,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EAEAgE,gBAAgBA,CAACjG,WAAmB;IAClC,MAAMC,OAAO,GAAG,IAAI,CAACtB,QAAQ,CAACuB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IACnD,OAAOlD,cAAc,CAACmD,OAAO,CAACzB,UAAU,CAAC,UAAU,EAAEyC,GAAG,IAAIA,GAAG,CAACE,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAACL,KAAK,CAAC,CAAC,CAAC,CAAC,CAACyB,GAAG,EAAE,CAAC,CAACrD,IAAI,CAACsD,QAAQ,IAAG;MAC5H,IAAI,CAACA,QAAQ,CAACgE,KAAK,EAAE;QACnB,MAAMR,aAAa,GAAGxD,QAAQ,CAACiE,IAAI,CAAC,CAAC,CAAC,CAACnG,IAAI,EAAa;QACxD,OAAO0F,aAAa;MACtB,CAAC,MAAM;QACL,OAAO,IAAI,CAAC,CAAC;MACf;IACF,CAAC,CAAC;EACJ;EAEA;EACAU,cAAcA,CAAC1G,WAAmB,EAAEM,IAAmB;IACrD,OAAO,IAAI,CAAC3B,QAAQ,CAACuB,GAAG,CAACF,WAAW,CAAC,CAAC2D,MAAM,CAACrD,IAAI,CAAC;EACpD;EAEA;EACAqG,cAAcA,CAAC3G,WAAmB;IAChC,OAAO,IAAI,CAACrB,QAAQ,CAACuB,GAAG,CAACF,WAAW,CAAC,CAAC+F,MAAM,EAAE;EAChD;EAEAa,gBAAgBA,CAAC5G,WAAmB,EAAEF,OAAgB;IACpD,MAAMuC,UAAU,GAAG,IAAI,CAAC1E,EAAE,CAACa,UAAU,CAAC,GAAG,IAAI,CAACF,YAAY,IAAI0B,WAAW,WAAW,CAAC,CAACE,GAAG,CAACJ,OAAO,CAACU,EAAE,CAAC;IAErG;IACA,MAAMqG,iBAAiB,GAAG;MACxB;MACA,CAAC/G,OAAO,CAAC6C,QAAQ,GAAG,KAAK,CAAE;MAC3B;KACD;IAED,OAAON,UAAU,CAACqB,GAAG,CAAC;MACpBkB,UAAU,EAAEiC,iBAAiB;MAAE;MAC/BxF,SAAS,EAAE5E,eAAe,EAAE,CAAE;KAC/B,CAAC;EACJ;EAEAqK,kBAAkBA,CAAC9G,WAAmB,EAAEyE,MAAc;IACpD,OAAO,IAAI,CAAC9G,EAAE,CAACa,UAAU,CAAC,GAAG,IAAI,CAACF,YAAY,IAAI0B,WAAW,WAAW,CAAC,CAACuC,GAAG,EAAE,CAAC4C,SAAS,EAAE,CAACjG,IAAI,CAAC6H,aAAa,IAAG;MAC/G,MAAM7C,KAAK,GAAG,IAAI,CAACvG,EAAE,CAACwG,SAAS,CAACD,KAAK,EAAE;MACvC,IAAI8C,UAAU,GAAG,KAAK,CAAC,CAAC;MAExBD,aAAc,CAACzD,OAAO,CAACpD,GAAG,IAAG;QAC3B,MAAMI,IAAI,GAAGJ,GAAG,CAACI,IAAI,EAAE;QAEvB;QACA,MAAM2G,gBAAgB,GAAI3G,IAAY,CAACsE,UAAU,IAAI,EAAE;QAEvD;QACA,IAAI,CAACqC,gBAAgB,CAACxC,MAAM,CAAC,EAAE;UAC7BwC,gBAAgB,CAACxC,MAAM,CAAC,GAAG,IAAI;UAC/B;UACAP,KAAK,CAACP,MAAM,CAACzD,GAAG,CAACe,GAAG,EAAE;YAAE2D,UAAU,EAAEqC;UAAgB,CAAE,CAAC;UACvDD,UAAU,GAAG,IAAI,CAAC,CAAC;QACrB;MACF,CAAC,CAAC;MAEF;MACA,IAAIA,UAAU,EAAE;QACd,OAAO9C,KAAK,CAACW,MAAM,EAAE;MACvB;MAEA;MACA,OAAOO,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA;EACA6B,qBAAqBA,CAAClH,WAAmB;IACvC,OAAO,IAAI,CAACrC,EAAE,CAACa,UAAU,CAAC,GAAG,IAAI,CAACF,YAAY,IAAI0B,WAAW,WAAW,CAAC,CAACuC,GAAG,EAAE,CAAC4C,SAAS,EAAE,CAACjG,IAAI,CAAC6H,aAAa,IAAG;MAC/G,MAAM7C,KAAK,GAAG,IAAI,CAACvG,EAAE,CAACwG,SAAS,CAACD,KAAK,EAAE;MACvC6C,aAAc,CAACzD,OAAO,CAACpD,GAAG,IAAG;QAC3B,MAAMkE,WAAW,GAAGlE,GAAG,CAACI,IAAI,EAAa;QACzC,MAAM6G,iBAAiB,GAAG,EAAE;QAE5B;QACA,KAAK,MAAM1C,MAAM,IAAIL,WAAW,CAACQ,UAAU,EAAE;UAC3C,IAAIR,WAAW,CAACQ,UAAU,CAACwC,cAAc,CAAC3C,MAAM,CAAC,EAAE;YACjD,MAAM9D,CAAC,GAAG,cAAc8D,MAAM,EAAE;YAC/B0C,iBAAyB,CAACxG,CAAC,CAAC,GAAG,IAAI;UACtC;QACF;QAEAuD,KAAK,CAACP,MAAM,CAACzD,GAAG,CAACe,GAAG,EAAEkG,iBAAiB,CAAC;MAC1C,CAAC,CAAC;MACF,OAAOjD,KAAK,CAACW,MAAM,EAAE;IACvB,CAAC,CAAC;EACJ;EAEA;EACAwC,sBAAsBA,CAACrH,WAAmB,EAAEyE,MAAc;IACxD,OAAO,IAAI,CAAC9G,EAAE,CAACa,UAAU,CAAU,GAAG,IAAI,CAACF,YAAY,IAAI0B,WAAW,WAAW,CAAC,CAACG,eAAe,EAAE,CAACC,IAAI,CACvGrD,GAAG,CAAC2D,OAAO,IAAIA,OAAO,CAAC4G,MAAM,CAACjH,MAAM,IAAG;MACrC,MAAMC,IAAI,GAAGD,MAAM,CAACE,OAAO,CAACL,GAAG,CAACI,IAAI,EAAa;MACjD,OAAO,CAACA,IAAI,CAACsE,UAAU,CAACwC,cAAc,CAAC3C,MAAM,CAAC;IAChD,CAAC,CAAC,CAAC4B,MAAM,CAAC,CACX;EACH;EAGA;EACAkB,6BAA6BA,CAAC9C,MAAc;IAC1C,OAAO,IAAI,CAAC9F,QAAQ,CAACwB,eAAe,EAAE,CAACC,IAAI,CACzCpD,SAAS,CAACwK,aAAa,IAAG;MACxB,MAAMC,eAAe,GAAGD,aAAa,CAACzK,GAAG,CAACyF,QAAQ,IAAG;QACnD,MAAM3B,MAAM,GAAG2B,QAAQ,CAACjC,OAAO,CAACL,GAAG,CAACM,EAAE;QACtC,OAAO,IAAI,CAAC6G,sBAAsB,CAACxG,MAAM,EAAE4D,MAAM,CAAC;MACpD,CAAC,CAAC;MACF,OAAO5H,aAAa,CAAC4K,eAAe,CAAC;IACvC,CAAC,CAAC,EACF1K,GAAG,CAAC0G,YAAY,IAAIA,YAAY,CAACiE,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC,CAAC,CACzE;EACH;EAEA;EACAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACjK,MAAM,CAACkK,SAAS;EAC9B;EAEAC,aAAaA,CAACtD,MAAc,EAAEuD,MAA4B;IACxD,MAAMC,OAAO,GAAG,IAAI,CAAC1J,QAAQ,CAAC2B,GAAG,CAACuE,MAAM,CAAC;IACzC,OAAOwD,OAAO,CAACvE,GAAG,CAAC;MAAEsE,MAAM;MAAEE,cAAc,EAAExL,SAAS,CAACyL,GAAG;IAAE,CAAE,EAAE;MAAEC,KAAK,EAAE;IAAI,CAAE,CAAC;EAClF;EAEAC,qBAAqBA,CAAC5D,MAAc,EAAE6D,IAAgB;IACpD,MAAML,OAAO,GAAG,IAAI,CAAC1J,QAAQ,CAAC2B,GAAG,CAACuE,MAAM,CAAC;IACzC,OAAOwD,OAAO,CAACvE,GAAG,CAAC;MAAEsE,MAAM,EAAE,QAAQ;MAAEE,cAAc,EAAEI;IAAI,CAAE,EAAE;MAAEF,KAAK,EAAE;IAAI,CAAE,CAAC;EACjF;EAEAG,aAAaA,CAAC9D,MAAc;IAC1B,OAAO,IAAI,CAAClG,QAAQ,CAAC2B,GAAG,CAAiBuE,MAAM,CAAC,CAACe,YAAY,EAAE,CAACpF,IAAI,CAClErD,GAAG,CAACqC,IAAI,IAAIA,IAAI,EAAE4I,MAAM,IAAI,SAAS,CAAC,CACvC;EACH;EAEAQ,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAC7K,EAAE,CAACa,UAAU,CAAiB,QAAQ,EAAEyC,GAAG,IAAIA,GAAG,CAACwH,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAC5FtI,eAAe,EAAE,CAACC,IAAI,CACrBrD,GAAG,CAAC2D,OAAO,IAAIA,OAAO,CAAC3D,GAAG,CAAC4D,CAAC,IAAG;MAC7B,MAAML,IAAI,GAAGK,CAAC,CAACJ,OAAO,CAACL,GAAG,CAACI,IAAI,EAAoB;MACnD,MAAME,EAAE,GAAGG,CAAC,CAACJ,OAAO,CAACL,GAAG,CAACM,EAAE;MAC3B,OAAO;QAAE,GAAGF,IAAI;QAAEE;MAAE,CAAE,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CACJ;EACL;EAEA;;;;;;;EAOAkI,sCAAsCA,CAAA;IACpC,OAAO,IAAI,CAAC/K,EAAE,CAACa,UAAU,CAAiB,QAAQ,CAAC,CAChD2B,eAAe,EAAE,CAACC,IAAI,CACrBrD,GAAG,CAAC2D,OAAO,IAAG;MACZ,MAAMiI,WAAW,GAAGjM,SAAS,CAACyL,GAAG,EAAE;MACnC,OAAOzH,OAAO,CAAC3D,GAAG,CAAC4D,CAAC,IAAG;QACrB,MAAML,IAAI,GAAGK,CAAC,CAACJ,OAAO,CAACL,GAAG,CAACI,IAAI,EAAoB;QACnD,MAAME,EAAE,GAAGG,CAAC,CAACJ,OAAO,CAACL,GAAG,CAACM,EAAE;QAC3B,MAAM0H,cAAc,GAAG5H,IAAI,CAAC4H,cAA2B;QACvD,IAAIA,cAAc,EAAE;UAClB,IAAI,CAACS,WAAW,CAACC,OAAO,IAAI,CAACV,cAAc,CAACU,OAAO,EAAE;YACnD,OAAO;cAAE,GAAGtI,IAAI;cAAEE,EAAE;cAAEwH,MAAM,EAAE;YAAS,CAAE;UAC3C;UACA,MAAMa,cAAc,GAAGF,WAAW,EAAEC,OAAO,GAAGV,cAAc,EAAEU,OAAO;UAErE;UACA,IAAIC,cAAc,IAAI,IAAI,CAACnK,0BAA0B,EAAE;YACrD,OAAO;cAAE,GAAG4B,IAAI;cAAEE,EAAE;cAAEwH,MAAM,EAAE;YAAQ,CAAE;UAC1C,CAAC,MAAM;YACL,OAAO;cAAE,GAAG1H,IAAI;cAAEE,EAAE;cAAEwH,MAAM,EAAE;YAAS,CAAE;UAC3C;QACF;QACA,OAAO;UAAE,GAAG1H,IAAI;UAAEE,EAAE;UAAEwH,MAAM,EAAE;QAAS,CAAE;MAE3C,CAAC,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EAAC,QAAAc,CAAA,G;qBAnnBUrL,WAAW,EAAAsL,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAX9L,WAAW;IAAA+L,OAAA,EAAX/L,WAAW,CAAAgM,IAAA;IAAAC,UAAA,EAFV;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}