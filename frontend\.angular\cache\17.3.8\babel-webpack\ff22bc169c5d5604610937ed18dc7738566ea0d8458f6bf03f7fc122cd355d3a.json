{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { DOCUMENT } from '@angular/common';\nimport { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./auth.service\";\nexport let JitsiMeetService = /*#__PURE__*/(() => {\n  class JitsiMeetService {\n    constructor(route, document, authService) {\n      var _this = this;\n      this.route = route;\n      this.document = document;\n      this.authService = authService;\n      this.teacherJoined$ = new BehaviorSubject(false);\n      this.teacherJoined = this.teacherJoined$.asObservable();\n      this.roomClosed$ = new BehaviorSubject(false);\n      this.roomClosed = this.roomClosed$.asObservable();\n      this.videoConferenceLeft$ = new BehaviorSubject(false);\n      this.videoConferenceLeft = this.videoConferenceLeft$.asObservable();\n      this.namePrincipalRoom = '';\n      this.domain = 'meet.cerint.net';\n      // For Custom Controls\n      this.isAudioMuted = true;\n      this.isVideoMuted = true;\n      this.isHangupExecuted = false;\n      this.role = '';\n      this.log = event => {\n        console.log('LOGGGGGGGGGGGGGG', event);\n      };\n      this.handleClose = ev => {\n        console.log('handleClose', ev);\n        this.roomClosed$.next(true);\n        // this.api.executeCommand('getParticipantsInfo', 'The Password');\n      };\n      this.getParticipantsInfo = /*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* (event) {\n          console.log('getParticipantsInfo', event);\n        });\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }();\n      this.notificationTriggered = /*#__PURE__*/function () {\n        var _ref2 = _asyncToGenerator(function* (event) {\n          console.log('notificationTriggered', event);\n          if (event && event.title === 'dialog.thankYou') {\n            // this.videoConferenceLeft$.next(true);\n          }\n        });\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }();\n      this.endpointTextMessageReceived = /*#__PURE__*/function () {\n        var _ref3 = _asyncToGenerator(function* (event) {\n          if (event.data.eventData.text = 'mover a principal') {\n            _this.moveRoom('grupo 1', true);\n          }\n        });\n        return function (_x3) {\n          return _ref3.apply(this, arguments);\n        };\n      }();\n      this.passwordRequired = /*#__PURE__*/_asyncToGenerator(function* () {\n        console.log('passwordRequired'); // { id: \"2baa184e\" }\n        // this.api.executeCommand('password', 'The Password');\n      });\n      this.handleParticipantLeft = /*#__PURE__*/function () {\n        var _ref5 = _asyncToGenerator(function* (participant) {\n          const data = yield _this.getParticipants();\n          // this.isHangupExecuted = false;\n          console.log('handleParticipantLeft', participant); // { id: \"2baa184e\" }\n          console.log('user', _this.user); // { id: \"2baa184e\" }\n          if (participant.id !== _this.user.participantId) {\n            _this.isHangupExecuted = false;\n          }\n        });\n        return function (_x4) {\n          return _ref5.apply(this, arguments);\n        };\n      }();\n      this.participantRoleChanged = /*#__PURE__*/function () {\n        var _ref6 = _asyncToGenerator(function* (participant) {\n          console.log('participantRoleChanged', participant);\n          _this.role = participant.role;\n          if (participant.role === \"moderator\") {\n            console.log('participantRoleChanged:', participant.role);\n            // this.api.executeCommand('hangup');\n            // this.api.executeCommand('password', 'The Password');\n          }\n        });\n        return function (_x5) {\n          return _ref6.apply(this, arguments);\n        };\n      }();\n      this.subjectChange = /*#__PURE__*/function () {\n        var _ref7 = _asyncToGenerator(function* (participant) {\n          console.log('subjectChange', participant);\n        });\n        return function (_x6) {\n          return _ref7.apply(this, arguments);\n        };\n      }();\n      this.handleParticipantJoined = /*#__PURE__*/function () {\n        var _ref8 = _asyncToGenerator(function* (participant) {\n          console.log('OJOJOJOJ  handleParticipantJoined', participant);\n          console.log('current User', _this.user); // { id: \"2baa184e\", displayName: \"Shanu Verma\", formattedDisplayName: \"Shanu Verma\" }\n          //this.api.executeCommand('displayName', 'New Nickname');\n          const data = yield _this.getParticipants();\n          console.log(data);\n          if (!_this.user.id) {\n            _this.isHangupExecuted = true;\n            return;\n          }\n          if (participant.id === _this.user.id) {\n            _this.isHangupExecuted = true;\n            return;\n          }\n          console.log('isHangupExecuted', _this.isHangupExecuted);\n          console.log('role', _this.role);\n          console.log('isStudent', _this.authService.isStudent);\n          if (data.length > 1) {\n            if (_this.authService.isStudent && !_this.isHangupExecuted && _this.role !== 'none') {\n              _this.api.executeCommand('hangup');\n              console.log('HEREEEEEEEEEEEE');\n              _this.teacherJoined$.next(true);\n              _this.isHangupExecuted = true; // Set the flag to true\n            }\n            for (const participant of data) {\n              if (participant.participantId === _this.user.id) {}\n            }\n          }\n        });\n        return function (_x7) {\n          return _ref8.apply(this, arguments);\n        };\n      }();\n      this.handleVideoConferenceJoined = /*#__PURE__*/function () {\n        var _ref9 = _asyncToGenerator(function* (participant) {\n          console.log('handleVideoConferenceJoined', participant);\n          // Once the conference is joined, make the participant a moderator\n          // this.api.executeCommand('setLocalParticipantRole', 'moderator');\n          _this.user = participant;\n          const data = yield _this.getParticipants();\n          const participants = JSON.stringify(data);\n          const parsedParticipants = JSON.parse(participants); // Convert back to object if needed\n          let count = 0;\n          for (const participant of parsedParticipants) {\n            // Do something with each participant\n            if (participant.participantId !== 'local') {\n              count++;\n            }\n          }\n          if (count === 1) {}\n          _this.teacherJoined$.next(false);\n          _this.roomClosed$.next(false);\n        });\n        return function (_x8) {\n          return _ref9.apply(this, arguments);\n        };\n      }();\n      this.handleVideoConferenceLeft = e => {\n        console.log('handleVideoConferenceLeft', e);\n        // this.route.navigate(['/thank-you']);\n        // this.videoConferenceLeft$.next(true);\n      };\n      this.handleMuteStatus = audio => {\n        console.log('handleMuteStatus', audio); // { muted: true }\n      };\n      this.handleVideoStatus = video => {\n        console.log('handleVideoStatus', video); // { muted: true }\n      };\n    }\n    ngOnDestroy() {\n      this.api.dispose();\n      this.teacherJoined$.next(false);\n      this.roomClosed$.next(false);\n    }\n    /**\n     * Append the JS tag to the Document Body.\n     * @param renderer The Angular Renderer\n     * @param src The path to the script\n     * @returns the script element\n     */\n    loadJsScript(renderer, src) {\n      const script = renderer.createElement('script');\n      script.type = 'text/javascript';\n      script.src = src;\n      renderer.appendChild(this.document.body, script);\n      return script;\n    }\n    moveRoom(nameRoom, isAdmin) {\n      const myNode = document.getElementById('jitsi-iframe');\n      if (myNode) {\n        myNode.innerHTML = '';\n      }\n      this.options = {\n        roomName: nameRoom,\n        width: '100%',\n        height: 500,\n        configOverwrite: {\n          prejoinPageEnabled: false,\n          disableInviteFunctions: true\n        },\n        interfaceConfigOverwrite: {\n          // customToolbarButtons: [\n          //     {\n          //         icon: ``,\n          //         id: 'custom-toolbar-button',\n          //         text: 'Custom Toolbar Button'\n          //     }\n          // ],\n          disableInviteFunctions: true,\n          MOBILE_APP_PROMO: false,\n          // don't works \n          //DEFAULT_LOCAL_DISPLAY_NAME: 'Eu', //ok\n          //DEFAULT_LOGO_URL: 'images/sample.png',   // dont work\n          SHOW_CHROME_EXTENSION_BANNER: false,\n          //ok\n          //TOOLBAR_ALWAYS_VISIBLE: true, // ok\n          //SETTINGS_SECTIONS: ['devices', 'language'], //ok,\n          TOOLBAR_BUTTONS: ['microphone', 'camera', 'closedcaptions', 'desktop', 'fullscreen', 'fodeviceselection', 'profile', '', 'chat', 'recording', 'livestreaming', 'etherpad', 'sharedvideo', 'settings', 'raisehand', 'videoquality', 'filmstrip', 'invite', 'feedback', 'stats', 'shortcuts', 'tileview', 'videobackgroundblur', 'download', 'help', 'mute-everyone', 'e2ee'],\n          // ok\n          filmStripOnly: true,\n          FILM_STRIP_MAX_HEIGHT: 80,\n          DEFAULT_BACKGROUND: \"transparent\",\n          //DISABLE_FOCUS_INDICATOR: true,\n          //DISABLE_DOMINANT_SPEAKER_INDICATOR: true,\n          //DISPLAY_WELCOME_PAGE_CONTENT: false,\n          //TILE_VIEW_MAX_COLUMNS: 1,\n          DISABLE_VIDEO_BACKGROUND: false,\n          VIDEO_QUALITY_LABEL_DISABLED: true,\n          CONNECTION_INDICATOR_DISABLED: true,\n          HIDE_KICK_BUTTON_FOR_GUESTS: true\n        },\n        parentNode: document.querySelector('#jitsi-iframe'),\n        userInfo: {\n          displayName: this.loggedUser.firstName,\n          email: this.loggedUser.email,\n          testVar: 'makaa',\n          role: 'moderator'\n        }\n        // TODO: Whiteboard feature\n        // whiteboard: {\n        //     enabled: true,\n        //     collabServerBaseUrl: 'https://meet.example.com'\n        // },\n      };\n      this.api = new JitsiMeetExternalAPI(this.domain, this.options);\n      this.api.addEventListeners({\n        readyToClose: this.handleClose,\n        participantLeft: this.handleParticipantLeft,\n        participantJoined: this.handleParticipantJoined,\n        videoConferenceJoined: this.handleVideoConferenceJoined,\n        videoConferenceLeft: this.handleVideoConferenceLeft,\n        audioMuteStatusChanged: this.handleMuteStatus,\n        videoMuteStatusChanged: this.handleVideoStatus,\n        participantRoleChanged: this.participantRoleChanged,\n        getParticipantsInfo: this.getParticipantsInfo,\n        endpointTextMessageReceived: this.endpointTextMessageReceived,\n        moderationStatusChanged: this.moderationStatusChanged,\n        notificationTriggered: this.notificationTriggered,\n        subjectChange: this.subjectChange,\n        log: this.log\n      });\n      this.api.executeCommand('setNoiseSuppressionEnabled', {\n        enabled: true\n      });\n      // this.api.executeCommand('toggleWhiteboard');\n      this.api.executeCommand('toggleLobby', false); // Ensure lobby mode is disabled\n      this.api.executeCommand('overwriteConfig', {\n        config: this.options\n      });\n    }\n    moderationStatusChanged() {\n      alert('moderationStatusChanged');\n    }\n    myUserId() {\n      alert(this.api.myUserId());\n      return this.api.myUserId();\n    }\n    changeRouterLink(value) {\n      this.namePrincipalRoom = value;\n      const myNode = document.getElementById('jitsi-iframe');\n      myNode.innerHTML = '';\n      this.options = {\n        roomName: this.namePrincipalRoom,\n        width: 900,\n        height: 500,\n        configOverwrite: {\n          prejoinPageEnabled: false,\n          openBridgeChannel: 'datachannel',\n          JITSI_WATERMARK_LINK: 'https://aaa.org',\n          DISABLE_DOMINANT_SPEAKER_INDICATOR: true,\n          DISPLAY_WELCOME_FOOTER: false,\n          DISPLAY_WELCOME_PAGE_ADDITIONAL_CARD: true\n        },\n        \"welcomepage\": {\n          \"appDescription\": \"description\",\n          \"headerTitle\": \"Test\",\n          \"headerSubtitle\": \"description\",\n          \"title\": \"Test\"\n        },\n        interfaceConfigOverwrite: {\n          JITSI_WATERMARK_LINK: 'https://meet.jit.si',\n          DISABLE_DOMINANT_SPEAKER_INDICATOR: true,\n          DISPLAY_WELCOME_FOOTER: false,\n          DISPLAY_WELCOME_PAGE_ADDITIONAL_CARD: true,\n          DISPLAY_WELCOME_PAGE_CONTENT: true,\n          DISPLAY_WELCOME_PAGE_TOOLBAR_ADDITIONAL_CONTENT: true,\n          disableGrantModerator: false\n          // overwrite interface properties\n        },\n        parentNode: document.querySelector('#jitsi-iframe'),\n        displayName: 'cool name',\n        role: 'moderator',\n        userInfo: {\n          displayName: 'cool name',\n          role: 'moderator'\n        }\n      };\n      this.api = new JitsiMeetExternalAPI(this.domain, this.options);\n    }\n    getParticipantsNumber() {\n      // Get participants info\n      const participants = this.api.getParticipantsInfo();\n      alert(participants.length);\n      // Return the number of participants\n      return participants.length;\n    }\n    getParticipants() {\n      return new Promise((resolve, reject) => {\n        setTimeout(() => {\n          resolve(this.api.getParticipantsInfo()); // get all participants\n        }, 100);\n      });\n    }\n    // custom events\n    executeCommand(command) {\n      this.api.executeCommand(command);\n      if (command == 'hangup') {\n        this.route.navigate(['/thank-you']);\n        return;\n      }\n      if (command == 'toggleAudio') {\n        this.isAudioMuted = !this.isAudioMuted;\n      }\n      if (command == 'toggleVideo') {\n        this.isVideoMuted = !this.isVideoMuted;\n      }\n    }\n    openMicrophoneSettings() {\n      // this.api.executeCommand('toggleAudio');\n      // Check if the JitsiMeetExternalAPI instance is available\n      if (this.api && this.api.executeCommand) {\n        // Execute the command to open microphone settings\n        // this.api.executeCommand('toggleAudio');\n      } else {\n        console.error('JitsiMeetExternalAPI instance not available or does not support executeCommand');\n      }\n    }\n    setLoggedUser(user) {\n      this.loggedUser = user;\n    }\n    setRoomName(name) {\n      this.namePrincipalRoom = name;\n    }\n    setRoomSubject(subject) {\n      // this.api.executeCommand('subject', 'MyLingoTrip Lesson');\n      // TODO: check if needed to add real subject\n      this.api.executeCommand('subject', subject);\n    }\n    setVideoConferenceLeft(val) {\n      this.videoConferenceLeft$.next(val);\n    }\n    setTeacherJoined(val) {\n      this.teacherJoined$.next(val);\n    }\n    static #_ = this.ɵfac = function JitsiMeetService_Factory(t) {\n      return new (t || JitsiMeetService)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i2.AuthService));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: JitsiMeetService,\n      factory: JitsiMeetService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return JitsiMeetService;\n})();", "map": {"version": 3, "names": ["DOCUMENT", "BehaviorSubject", "JitsiMeetService", "constructor", "route", "document", "authService", "_this", "teacherJoined$", "teacherJoined", "asObservable", "roomClosed$", "roomClosed", "videoConferenceLeft$", "videoConferenceLeft", "namePrincipalRoom", "domain", "isAudioMuted", "isVideoMuted", "isHangupExecuted", "role", "log", "event", "console", "handleClose", "ev", "next", "getParticipantsInfo", "_ref", "_asyncToGenerator", "_x", "apply", "arguments", "notificationTriggered", "_ref2", "title", "_x2", "endpointTextMessageReceived", "_ref3", "data", "eventData", "text", "moveRoom", "_x3", "passwordRequired", "handleParticipantLeft", "_ref5", "participant", "getParticipants", "user", "id", "participantId", "_x4", "participantRoleChanged", "_ref6", "_x5", "subjectChange", "_ref7", "_x6", "handleParticipantJoined", "_ref8", "isStudent", "length", "api", "executeCommand", "_x7", "handleVideoConferenceJoined", "_ref9", "participants", "JSON", "stringify", "parsedParticipants", "parse", "count", "_x8", "handleVideoConferenceLeft", "e", "handleMuteStatus", "audio", "handleVideoStatus", "video", "ngOnDestroy", "dispose", "loadJsScript", "renderer", "src", "script", "createElement", "type", "append<PERSON><PERSON><PERSON>", "body", "nameRoom", "isAdmin", "myNode", "getElementById", "innerHTML", "options", "roomName", "width", "height", "configOverwrite", "prejoinPageEnabled", "disableInviteFunctions", "interfaceConfigOverwrite", "MOBILE_APP_PROMO", "SHOW_CHROME_EXTENSION_BANNER", "TOOLBAR_BUTTONS", "filmStripOnly", "FILM_STRIP_MAX_HEIGHT", "DEFAULT_BACKGROUND", "DISABLE_VIDEO_BACKGROUND", "VIDEO_QUALITY_LABEL_DISABLED", "CONNECTION_INDICATOR_DISABLED", "HIDE_KICK_BUTTON_FOR_GUESTS", "parentNode", "querySelector", "userInfo", "displayName", "loggedUser", "firstName", "email", "testVar", "JitsiMeetExternalAPI", "addEventListeners", "readyToClose", "participantLeft", "participantJoined", "videoConferenceJoined", "audioMuteStatusChanged", "videoMuteStatusChanged", "moderationStatusChanged", "enabled", "config", "alert", "myUserId", "changeRouterLink", "value", "openBridgeChannel", "JITSI_WATERMARK_LINK", "DISABLE_DOMINANT_SPEAKER_INDICATOR", "DISPLAY_WELCOME_FOOTER", "DISPLAY_WELCOME_PAGE_ADDITIONAL_CARD", "DISPLAY_WELCOME_PAGE_CONTENT", "DISPLAY_WELCOME_PAGE_TOOLBAR_ADDITIONAL_CONTENT", "disableGrantModerator", "getParticipantsNumber", "Promise", "resolve", "reject", "setTimeout", "command", "navigate", "openMicrophoneSettings", "error", "setLogged<PERSON>ser", "setRoomName", "name", "setRoomSubject", "subject", "setVideoConferenceLeft", "val", "set<PERSON><PERSON><PERSON><PERSON>oined", "_", "i0", "ɵɵinject", "i1", "Router", "i2", "AuthService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\services\\jitsi-meet.service.ts"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\r\nimport { Inject, Injectable, OnInit, Renderer2 } from '@angular/core';\r\ndeclare var JitsiMeetExternalAPI: any;\r\nimport { Router } from '@angular/router'; // import router from angular router\r\nimport { User } from '../models/user.model';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { AuthService } from './auth.service';\r\n\r\n@Injectable({\r\n    providedIn: 'root',\r\n})\r\nexport class JitsiMeetService {\r\n\r\n    private teacherJoined$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);\r\n    public teacherJoined: Observable<boolean> = this.teacherJoined$.asObservable();\r\n\r\n    private roomClosed$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);\r\n    public roomClosed: Observable<boolean> = this.roomClosed$.asObservable();\r\n\r\n    private videoConferenceLeft$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);\r\n    public videoConferenceLeft: Observable<boolean> = this.videoConferenceLeft$.asObservable();\r\n    api: any;\r\n    user: any;\r\n    namePrincipalRoom = '';\r\n    options: any;\r\n    domain: string = 'meet.cerint.net';\r\n\r\n    // For Custom Controls\r\n    isAudioMuted = true;\r\n    isVideoMuted = true;\r\n    isHangupExecuted = false\r\n    loggedUser!: User;\r\n    role = '';\r\n    constructor(private route: Router,\r\n        @Inject(DOCUMENT) private document: Document,\r\n\r\n        private authService: AuthService,) {\r\n    }\r\n\r\n\r\n    ngOnDestroy(): void {\r\n        this.api.dispose();\r\n        this.teacherJoined$.next(false);\r\n        this.roomClosed$.next(false);\r\n    }\r\n\r\n\r\n    /**\r\n     * Append the JS tag to the Document Body.\r\n     * @param renderer The Angular Renderer\r\n     * @param src The path to the script\r\n     * @returns the script element\r\n     */\r\n    public loadJsScript(renderer: Renderer2, src: string): HTMLScriptElement {\r\n        const script = renderer.createElement('script');\r\n        script.type = 'text/javascript';\r\n        script.src = src;\r\n        renderer.appendChild(this.document.body, script);\r\n        return script;\r\n    }\r\n\r\n    moveRoom(nameRoom: String, isAdmin: Boolean): void {\r\n        const myNode = document.getElementById('jitsi-iframe');\r\n        if (myNode) {\r\n            myNode!.innerHTML = '';\r\n        }\r\n\r\n        this.options = {\r\n            roomName: nameRoom,\r\n            width: '100%',\r\n            height: 500,\r\n            configOverwrite: {\r\n                prejoinPageEnabled: false,\r\n                disableInviteFunctions: true,\r\n            },\r\n            interfaceConfigOverwrite: {\r\n                // customToolbarButtons: [\r\n                //     {\r\n                //         icon: ``,\r\n                //         id: 'custom-toolbar-button',\r\n                //         text: 'Custom Toolbar Button'\r\n                //     }\r\n                // ],\r\n                disableInviteFunctions: true,\r\n                MOBILE_APP_PROMO: false, // don't works \r\n                //DEFAULT_LOCAL_DISPLAY_NAME: 'Eu', //ok\r\n                //DEFAULT_LOGO_URL: 'images/sample.png',   // dont work\r\n                SHOW_CHROME_EXTENSION_BANNER: false, //ok\r\n                //TOOLBAR_ALWAYS_VISIBLE: true, // ok\r\n                //SETTINGS_SECTIONS: ['devices', 'language'], //ok,\r\n                TOOLBAR_BUTTONS: [\r\n                    'microphone', 'camera', 'closedcaptions', 'desktop', 'fullscreen',\r\n                    'fodeviceselection', 'profile', '', 'chat', 'recording',\r\n                    'livestreaming', 'etherpad', 'sharedvideo', 'settings', 'raisehand',\r\n                    'videoquality', 'filmstrip', 'invite', 'feedback', 'stats', 'shortcuts',\r\n                    'tileview', 'videobackgroundblur', 'download', 'help', 'mute-everyone',\r\n                    'e2ee'\r\n                ], // ok\r\n                filmStripOnly: true,\r\n                FILM_STRIP_MAX_HEIGHT: 80,\r\n                DEFAULT_BACKGROUND: \"transparent\",\r\n                //DISABLE_FOCUS_INDICATOR: true,\r\n                //DISABLE_DOMINANT_SPEAKER_INDICATOR: true,\r\n                //DISPLAY_WELCOME_PAGE_CONTENT: false,\r\n                //TILE_VIEW_MAX_COLUMNS: 1,\r\n                DISABLE_VIDEO_BACKGROUND: false,\r\n                VIDEO_QUALITY_LABEL_DISABLED: true,\r\n                CONNECTION_INDICATOR_DISABLED: true,\r\n                HIDE_KICK_BUTTON_FOR_GUESTS: true,\r\n            },\r\n            parentNode: document.querySelector('#jitsi-iframe'),\r\n            userInfo: {\r\n                displayName: this.loggedUser.firstName,\r\n                email: this.loggedUser.email,\r\n                testVar: 'makaa',\r\n                role: 'moderator',\r\n            },\r\n            // TODO: Whiteboard feature\r\n            // whiteboard: {\r\n            //     enabled: true,\r\n            //     collabServerBaseUrl: 'https://meet.example.com'\r\n            // },\r\n        };\r\n\r\n        this.api = new JitsiMeetExternalAPI(this.domain, this.options);\r\n        this.api.addEventListeners({\r\n            readyToClose: this.handleClose,\r\n            participantLeft: this.handleParticipantLeft,\r\n            participantJoined: this.handleParticipantJoined,\r\n            videoConferenceJoined: this.handleVideoConferenceJoined,\r\n            videoConferenceLeft: this.handleVideoConferenceLeft,\r\n            audioMuteStatusChanged: this.handleMuteStatus,\r\n            videoMuteStatusChanged: this.handleVideoStatus,\r\n            participantRoleChanged: this.participantRoleChanged,\r\n            getParticipantsInfo: this.getParticipantsInfo,\r\n            endpointTextMessageReceived: this.endpointTextMessageReceived,\r\n            moderationStatusChanged: this.moderationStatusChanged,\r\n            notificationTriggered: this.notificationTriggered,\r\n            subjectChange: this.subjectChange,\r\n            log: this.log,\r\n        });\r\n        this.api.executeCommand('setNoiseSuppressionEnabled', {\r\n            enabled: true\r\n        });\r\n\r\n        // this.api.executeCommand('toggleWhiteboard');\r\n\r\n\r\n        this.api.executeCommand('toggleLobby', false); // Ensure lobby mode is disabled\r\n\r\n        this.api.executeCommand('overwriteConfig',\r\n            { config: this.options }\r\n        );\r\n    }\r\n\r\n    moderationStatusChanged() {\r\n        alert('moderationStatusChanged');\r\n    }\r\n\r\n    myUserId(): string {\r\n        alert(this.api.myUserId());\r\n        return this.api.myUserId();\r\n    }\r\n\r\n    changeRouterLink(value: any) {\r\n        this.namePrincipalRoom = value;\r\n\r\n        const myNode = document.getElementById('jitsi-iframe');\r\n        myNode!.innerHTML = '';\r\n\r\n        this.options = {\r\n            roomName: this.namePrincipalRoom,\r\n            width: 900,\r\n            height: 500,\r\n            configOverwrite: {\r\n                prejoinPageEnabled: false,\r\n                openBridgeChannel: 'datachannel',\r\n                JITSI_WATERMARK_LINK: 'https://aaa.org',\r\n                DISABLE_DOMINANT_SPEAKER_INDICATOR: true,\r\n                DISPLAY_WELCOME_FOOTER: false,\r\n                DISPLAY_WELCOME_PAGE_ADDITIONAL_CARD: true,\r\n            },\r\n            \"welcomepage\": {\r\n                \"appDescription\": \"description\",\r\n                \"headerTitle\": \"Test\",\r\n                \"headerSubtitle\": \"description\",\r\n                \"title\": \"Test\"\r\n            },\r\n            interfaceConfigOverwrite: {\r\n                JITSI_WATERMARK_LINK: 'https://meet.jit.si',\r\n                DISABLE_DOMINANT_SPEAKER_INDICATOR: true,\r\n                DISPLAY_WELCOME_FOOTER: false,\r\n                DISPLAY_WELCOME_PAGE_ADDITIONAL_CARD: true,\r\n                DISPLAY_WELCOME_PAGE_CONTENT: true,\r\n                DISPLAY_WELCOME_PAGE_TOOLBAR_ADDITIONAL_CONTENT: true,\r\n                disableGrantModerator: false,\r\n                // overwrite interface properties\r\n            },\r\n            parentNode: document.querySelector('#jitsi-iframe'),\r\n            displayName: 'cool name',\r\n            role: 'moderator',\r\n            userInfo: {\r\n                displayName: 'cool name',\r\n                role: 'moderator',\r\n            },\r\n        };\r\n\r\n        this.api = new JitsiMeetExternalAPI(this.domain, this.options);\r\n    }\r\n\r\n    log = (event: any) => {\r\n        console.log('LOGGGGGGGGGGGGGG', event);\r\n    };\r\n\r\n    handleClose = (ev: any) => {\r\n        console.log('handleClose', ev);\r\n        this.roomClosed$.next(true);\r\n        // this.api.executeCommand('getParticipantsInfo', 'The Password');\r\n    };\r\n\r\n    getParticipantsInfo = async (event: any) => {\r\n        console.log('getParticipantsInfo', event);\r\n    };\r\n\r\n    notificationTriggered = async (event: any) => {\r\n        console.log('notificationTriggered', event);\r\n        if (event && event.title === 'dialog.thankYou') {\r\n            // this.videoConferenceLeft$.next(true);\r\n        }\r\n    };\r\n\r\n\r\n    endpointTextMessageReceived = async (event: any) => {\r\n        if ((event.data.eventData.text = 'mover a principal')) {\r\n            this.moveRoom('grupo 1', true);\r\n        }\r\n    };\r\n\r\n    passwordRequired = async () => {\r\n        console.log('passwordRequired'); // { id: \"2baa184e\" }\r\n        // this.api.executeCommand('password', 'The Password');\r\n    };\r\n\r\n    handleParticipantLeft = async (participant: any) => {\r\n        const data = await this.getParticipants();\r\n        // this.isHangupExecuted = false;\r\n        console.log('handleParticipantLeft', participant); // { id: \"2baa184e\" }\r\n        console.log('user', this.user); // { id: \"2baa184e\" }\r\n        if (participant.id !== this.user.participantId) {\r\n            this.isHangupExecuted = false;\r\n        }\r\n    };\r\n\r\n    participantRoleChanged = async (participant: any) => {\r\n        console.log('participantRoleChanged', participant);\r\n        this.role = participant.role;\r\n        if (participant.role === \"moderator\") {\r\n            console.log('participantRoleChanged:', participant.role);\r\n            // this.api.executeCommand('hangup');\r\n            // this.api.executeCommand('password', 'The Password');\r\n        }\r\n    };\r\n\r\n    subjectChange = async (participant: any) => {\r\n        console.log('subjectChange', participant);\r\n    };\r\n\r\n    handleParticipantJoined = async (participant: any) => {\r\n        console.log('OJOJOJOJ  handleParticipantJoined', participant);\r\n        console.log('current User', this.user); // { id: \"2baa184e\", displayName: \"Shanu Verma\", formattedDisplayName: \"Shanu Verma\" }\r\n        //this.api.executeCommand('displayName', 'New Nickname');\r\n        const data = await this.getParticipants() as any[];\r\n        console.log(data);\r\n\r\n        if (!this.user.id) {\r\n            this.isHangupExecuted = true;\r\n            return;\r\n        }\r\n\r\n        if (participant.id === this.user.id) {\r\n            this.isHangupExecuted = true;\r\n            return;\r\n        }\r\n\r\n        console.log('isHangupExecuted', this.isHangupExecuted);\r\n        console.log('role', this.role);\r\n        console.log('isStudent', this.authService.isStudent);\r\n        if (data.length > 1) {\r\n            if (this.authService.isStudent && !this.isHangupExecuted && this.role !== 'none') {\r\n                this.api.executeCommand('hangup');\r\n                console.log('HEREEEEEEEEEEEE')\r\n                this.teacherJoined$.next(true);\r\n                this.isHangupExecuted = true; // Set the flag to true\r\n\r\n            }\r\n            for (const participant of data) {\r\n                if (participant.participantId === this.user.id) {\r\n                }\r\n            }\r\n        }\r\n\r\n    };\r\n\r\n    getParticipantsNumber() {\r\n        // Get participants info\r\n        const participants = this.api.getParticipantsInfo();\r\n\r\n        alert(participants.length);\r\n        // Return the number of participants\r\n        return participants.length;\r\n    }\r\n\r\n\r\n    handleVideoConferenceJoined = async (participant: any) => {\r\n        console.log('handleVideoConferenceJoined', participant);\r\n        // Once the conference is joined, make the participant a moderator\r\n        // this.api.executeCommand('setLocalParticipantRole', 'moderator');\r\n\r\n        this.user = participant;\r\n\r\n        const data = await this.getParticipants();\r\n        const participants = JSON.stringify(data);\r\n        const parsedParticipants = JSON.parse(participants); // Convert back to object if needed\r\n\r\n        let count = 0;\r\n        for (const participant of parsedParticipants) {\r\n            // Do something with each participant\r\n            if (participant.participantId !== 'local') {\r\n                count++;\r\n            }\r\n        }\r\n        if (count === 1) {\r\n\r\n        }\r\n        this.teacherJoined$.next(false);\r\n        this.roomClosed$.next(false);\r\n    };\r\n\r\n    handleVideoConferenceLeft = (e: any) => {\r\n        console.log('handleVideoConferenceLeft', e);\r\n        // this.route.navigate(['/thank-you']);\r\n        // this.videoConferenceLeft$.next(true);\r\n    };\r\n\r\n    handleMuteStatus = (audio: any) => {\r\n        console.log('handleMuteStatus', audio); // { muted: true }\r\n    };\r\n\r\n    handleVideoStatus = (video: any) => {\r\n        console.log('handleVideoStatus', video); // { muted: true }\r\n    };\r\n\r\n    getParticipants() {\r\n        return new Promise((resolve, reject) => {\r\n            setTimeout(() => {\r\n\r\n                resolve(this.api.getParticipantsInfo()); // get all participants\r\n            }, 100);\r\n        });\r\n    }\r\n\r\n    // custom events\r\n    executeCommand(command: string) {\r\n        this.api.executeCommand(command);\r\n        if (command == 'hangup') {\r\n            this.route.navigate(['/thank-you']);\r\n            return;\r\n        }\r\n\r\n        if (command == 'toggleAudio') {\r\n            this.isAudioMuted = !this.isAudioMuted;\r\n        }\r\n\r\n        if (command == 'toggleVideo') {\r\n            this.isVideoMuted = !this.isVideoMuted;\r\n        }\r\n    }\r\n\r\n    openMicrophoneSettings() {\r\n        // this.api.executeCommand('toggleAudio');\r\n        // Check if the JitsiMeetExternalAPI instance is available\r\n        if (this.api && this.api.executeCommand) {\r\n            // Execute the command to open microphone settings\r\n            // this.api.executeCommand('toggleAudio');\r\n        } else {\r\n            console.error('JitsiMeetExternalAPI instance not available or does not support executeCommand');\r\n        }\r\n    }\r\n\r\n    setLoggedUser(user: User) {\r\n        this.loggedUser = user;\r\n    }\r\n\r\n    setRoomName(name: string) {\r\n        this.namePrincipalRoom = name;\r\n    }\r\n\r\n    setRoomSubject(subject: string) {\r\n        // this.api.executeCommand('subject', 'MyLingoTrip Lesson');\r\n        // TODO: check if needed to add real subject\r\n        this.api.executeCommand('subject', subject);\r\n    }\r\n\r\n    setVideoConferenceLeft(val: boolean) {\r\n        this.videoConferenceLeft$.next(val);\r\n    }\r\n\r\n    setTeacherJoined(val: boolean) {\r\n        this.teacherJoined$.next(val);\r\n    }\r\n}\r\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAK1C,SAASC,eAAe,QAAoB,MAAM;;;;AAMlD,WAAaC,gBAAgB;EAAvB,MAAOA,gBAAgB;IAsBzBC,YAAoBC,KAAa,EACHC,QAAkB,EAEpCC,WAAwB;MAAA,IAAAC,KAAA;MAHhB,KAAAH,KAAK,GAALA,KAAK;MACK,KAAAC,QAAQ,GAARA,QAAQ;MAE1B,KAAAC,WAAW,GAAXA,WAAW;MAvBf,KAAAE,cAAc,GAA6B,IAAIP,eAAe,CAAU,KAAK,CAAC;MAC/E,KAAAQ,aAAa,GAAwB,IAAI,CAACD,cAAc,CAACE,YAAY,EAAE;MAEtE,KAAAC,WAAW,GAA6B,IAAIV,eAAe,CAAU,KAAK,CAAC;MAC5E,KAAAW,UAAU,GAAwB,IAAI,CAACD,WAAW,CAACD,YAAY,EAAE;MAEhE,KAAAG,oBAAoB,GAA6B,IAAIZ,eAAe,CAAU,KAAK,CAAC;MACrF,KAAAa,mBAAmB,GAAwB,IAAI,CAACD,oBAAoB,CAACH,YAAY,EAAE;MAG1F,KAAAK,iBAAiB,GAAG,EAAE;MAEtB,KAAAC,MAAM,GAAW,iBAAiB;MAElC;MACA,KAAAC,YAAY,GAAG,IAAI;MACnB,KAAAC,YAAY,GAAG,IAAI;MACnB,KAAAC,gBAAgB,GAAG,KAAK;MAExB,KAAAC,IAAI,GAAG,EAAE;MAkLT,KAAAC,GAAG,GAAIC,KAAU,IAAI;QACjBC,OAAO,CAACF,GAAG,CAAC,kBAAkB,EAAEC,KAAK,CAAC;MAC1C,CAAC;MAED,KAAAE,WAAW,GAAIC,EAAO,IAAI;QACtBF,OAAO,CAACF,GAAG,CAAC,aAAa,EAAEI,EAAE,CAAC;QAC9B,IAAI,CAACd,WAAW,CAACe,IAAI,CAAC,IAAI,CAAC;QAC3B;MACJ,CAAC;MAED,KAAAC,mBAAmB;QAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOP,KAAU,EAAI;UACvCC,OAAO,CAACF,GAAG,CAAC,qBAAqB,EAAEC,KAAK,CAAC;QAC7C,CAAC;QAAA,iBAAAQ,EAAA;UAAA,OAAAF,IAAA,CAAAG,KAAA,OAAAC,SAAA;QAAA;MAAA;MAED,KAAAC,qBAAqB;QAAA,IAAAC,KAAA,GAAAL,iBAAA,CAAG,WAAOP,KAAU,EAAI;UACzCC,OAAO,CAACF,GAAG,CAAC,uBAAuB,EAAEC,KAAK,CAAC;UAC3C,IAAIA,KAAK,IAAIA,KAAK,CAACa,KAAK,KAAK,iBAAiB,EAAE;YAC5C;UAAA;QAER,CAAC;QAAA,iBAAAC,GAAA;UAAA,OAAAF,KAAA,CAAAH,KAAA,OAAAC,SAAA;QAAA;MAAA;MAGD,KAAAK,2BAA2B;QAAA,IAAAC,KAAA,GAAAT,iBAAA,CAAG,WAAOP,KAAU,EAAI;UAC/C,IAAKA,KAAK,CAACiB,IAAI,CAACC,SAAS,CAACC,IAAI,GAAG,mBAAmB,EAAG;YACnDlC,KAAI,CAACmC,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC;UAClC;QACJ,CAAC;QAAA,iBAAAC,GAAA;UAAA,OAAAL,KAAA,CAAAP,KAAA,OAAAC,SAAA;QAAA;MAAA;MAED,KAAAY,gBAAgB,gBAAAf,iBAAA,CAAG,aAAW;QAC1BN,OAAO,CAACF,GAAG,CAAC,kBAAkB,CAAC,CAAC,CAAC;QACjC;MACJ,CAAC;MAED,KAAAwB,qBAAqB;QAAA,IAAAC,KAAA,GAAAjB,iBAAA,CAAG,WAAOkB,WAAgB,EAAI;UAC/C,MAAMR,IAAI,SAAShC,KAAI,CAACyC,eAAe,EAAE;UACzC;UACAzB,OAAO,CAACF,GAAG,CAAC,uBAAuB,EAAE0B,WAAW,CAAC,CAAC,CAAC;UACnDxB,OAAO,CAACF,GAAG,CAAC,MAAM,EAAEd,KAAI,CAAC0C,IAAI,CAAC,CAAC,CAAC;UAChC,IAAIF,WAAW,CAACG,EAAE,KAAK3C,KAAI,CAAC0C,IAAI,CAACE,aAAa,EAAE;YAC5C5C,KAAI,CAACY,gBAAgB,GAAG,KAAK;UACjC;QACJ,CAAC;QAAA,iBAAAiC,GAAA;UAAA,OAAAN,KAAA,CAAAf,KAAA,OAAAC,SAAA;QAAA;MAAA;MAED,KAAAqB,sBAAsB;QAAA,IAAAC,KAAA,GAAAzB,iBAAA,CAAG,WAAOkB,WAAgB,EAAI;UAChDxB,OAAO,CAACF,GAAG,CAAC,wBAAwB,EAAE0B,WAAW,CAAC;UAClDxC,KAAI,CAACa,IAAI,GAAG2B,WAAW,CAAC3B,IAAI;UAC5B,IAAI2B,WAAW,CAAC3B,IAAI,KAAK,WAAW,EAAE;YAClCG,OAAO,CAACF,GAAG,CAAC,yBAAyB,EAAE0B,WAAW,CAAC3B,IAAI,CAAC;YACxD;YACA;UACJ;QACJ,CAAC;QAAA,iBAAAmC,GAAA;UAAA,OAAAD,KAAA,CAAAvB,KAAA,OAAAC,SAAA;QAAA;MAAA;MAED,KAAAwB,aAAa;QAAA,IAAAC,KAAA,GAAA5B,iBAAA,CAAG,WAAOkB,WAAgB,EAAI;UACvCxB,OAAO,CAACF,GAAG,CAAC,eAAe,EAAE0B,WAAW,CAAC;QAC7C,CAAC;QAAA,iBAAAW,GAAA;UAAA,OAAAD,KAAA,CAAA1B,KAAA,OAAAC,SAAA;QAAA;MAAA;MAED,KAAA2B,uBAAuB;QAAA,IAAAC,KAAA,GAAA/B,iBAAA,CAAG,WAAOkB,WAAgB,EAAI;UACjDxB,OAAO,CAACF,GAAG,CAAC,mCAAmC,EAAE0B,WAAW,CAAC;UAC7DxB,OAAO,CAACF,GAAG,CAAC,cAAc,EAAEd,KAAI,CAAC0C,IAAI,CAAC,CAAC,CAAC;UACxC;UACA,MAAMV,IAAI,SAAShC,KAAI,CAACyC,eAAe,EAAW;UAClDzB,OAAO,CAACF,GAAG,CAACkB,IAAI,CAAC;UAEjB,IAAI,CAAChC,KAAI,CAAC0C,IAAI,CAACC,EAAE,EAAE;YACf3C,KAAI,CAACY,gBAAgB,GAAG,IAAI;YAC5B;UACJ;UAEA,IAAI4B,WAAW,CAACG,EAAE,KAAK3C,KAAI,CAAC0C,IAAI,CAACC,EAAE,EAAE;YACjC3C,KAAI,CAACY,gBAAgB,GAAG,IAAI;YAC5B;UACJ;UAEAI,OAAO,CAACF,GAAG,CAAC,kBAAkB,EAAEd,KAAI,CAACY,gBAAgB,CAAC;UACtDI,OAAO,CAACF,GAAG,CAAC,MAAM,EAAEd,KAAI,CAACa,IAAI,CAAC;UAC9BG,OAAO,CAACF,GAAG,CAAC,WAAW,EAAEd,KAAI,CAACD,WAAW,CAACuD,SAAS,CAAC;UACpD,IAAItB,IAAI,CAACuB,MAAM,GAAG,CAAC,EAAE;YACjB,IAAIvD,KAAI,CAACD,WAAW,CAACuD,SAAS,IAAI,CAACtD,KAAI,CAACY,gBAAgB,IAAIZ,KAAI,CAACa,IAAI,KAAK,MAAM,EAAE;cAC9Eb,KAAI,CAACwD,GAAG,CAACC,cAAc,CAAC,QAAQ,CAAC;cACjCzC,OAAO,CAACF,GAAG,CAAC,iBAAiB,CAAC;cAC9Bd,KAAI,CAACC,cAAc,CAACkB,IAAI,CAAC,IAAI,CAAC;cAC9BnB,KAAI,CAACY,gBAAgB,GAAG,IAAI,CAAC,CAAC;YAElC;YACA,KAAK,MAAM4B,WAAW,IAAIR,IAAI,EAAE;cAC5B,IAAIQ,WAAW,CAACI,aAAa,KAAK5C,KAAI,CAAC0C,IAAI,CAACC,EAAE,EAAE,CAChD;YACJ;UACJ;QAEJ,CAAC;QAAA,iBAAAe,GAAA;UAAA,OAAAL,KAAA,CAAA7B,KAAA,OAAAC,SAAA;QAAA;MAAA;MAYD,KAAAkC,2BAA2B;QAAA,IAAAC,KAAA,GAAAtC,iBAAA,CAAG,WAAOkB,WAAgB,EAAI;UACrDxB,OAAO,CAACF,GAAG,CAAC,6BAA6B,EAAE0B,WAAW,CAAC;UACvD;UACA;UAEAxC,KAAI,CAAC0C,IAAI,GAAGF,WAAW;UAEvB,MAAMR,IAAI,SAAShC,KAAI,CAACyC,eAAe,EAAE;UACzC,MAAMoB,YAAY,GAAGC,IAAI,CAACC,SAAS,CAAC/B,IAAI,CAAC;UACzC,MAAMgC,kBAAkB,GAAGF,IAAI,CAACG,KAAK,CAACJ,YAAY,CAAC,CAAC,CAAC;UAErD,IAAIK,KAAK,GAAG,CAAC;UACb,KAAK,MAAM1B,WAAW,IAAIwB,kBAAkB,EAAE;YAC1C;YACA,IAAIxB,WAAW,CAACI,aAAa,KAAK,OAAO,EAAE;cACvCsB,KAAK,EAAE;YACX;UACJ;UACA,IAAIA,KAAK,KAAK,CAAC,EAAE,CAEjB;UACAlE,KAAI,CAACC,cAAc,CAACkB,IAAI,CAAC,KAAK,CAAC;UAC/BnB,KAAI,CAACI,WAAW,CAACe,IAAI,CAAC,KAAK,CAAC;QAChC,CAAC;QAAA,iBAAAgD,GAAA;UAAA,OAAAP,KAAA,CAAApC,KAAA,OAAAC,SAAA;QAAA;MAAA;MAED,KAAA2C,yBAAyB,GAAIC,CAAM,IAAI;QACnCrD,OAAO,CAACF,GAAG,CAAC,2BAA2B,EAAEuD,CAAC,CAAC;QAC3C;QACA;MACJ,CAAC;MAED,KAAAC,gBAAgB,GAAIC,KAAU,IAAI;QAC9BvD,OAAO,CAACF,GAAG,CAAC,kBAAkB,EAAEyD,KAAK,CAAC,CAAC,CAAC;MAC5C,CAAC;MAED,KAAAC,iBAAiB,GAAIC,KAAU,IAAI;QAC/BzD,OAAO,CAACF,GAAG,CAAC,mBAAmB,EAAE2D,KAAK,CAAC,CAAC,CAAC;MAC7C,CAAC;IAzTD;IAGAC,WAAWA,CAAA;MACP,IAAI,CAAClB,GAAG,CAACmB,OAAO,EAAE;MAClB,IAAI,CAAC1E,cAAc,CAACkB,IAAI,CAAC,KAAK,CAAC;MAC/B,IAAI,CAACf,WAAW,CAACe,IAAI,CAAC,KAAK,CAAC;IAChC;IAGA;;;;;;IAMOyD,YAAYA,CAACC,QAAmB,EAAEC,GAAW;MAChD,MAAMC,MAAM,GAAGF,QAAQ,CAACG,aAAa,CAAC,QAAQ,CAAC;MAC/CD,MAAM,CAACE,IAAI,GAAG,iBAAiB;MAC/BF,MAAM,CAACD,GAAG,GAAGA,GAAG;MAChBD,QAAQ,CAACK,WAAW,CAAC,IAAI,CAACpF,QAAQ,CAACqF,IAAI,EAAEJ,MAAM,CAAC;MAChD,OAAOA,MAAM;IACjB;IAEA5C,QAAQA,CAACiD,QAAgB,EAAEC,OAAgB;MACvC,MAAMC,MAAM,GAAGxF,QAAQ,CAACyF,cAAc,CAAC,cAAc,CAAC;MACtD,IAAID,MAAM,EAAE;QACRA,MAAO,CAACE,SAAS,GAAG,EAAE;MAC1B;MAEA,IAAI,CAACC,OAAO,GAAG;QACXC,QAAQ,EAAEN,QAAQ;QAClBO,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,GAAG;QACXC,eAAe,EAAE;UACbC,kBAAkB,EAAE,KAAK;UACzBC,sBAAsB,EAAE;SAC3B;QACDC,wBAAwB,EAAE;UACtB;UACA;UACA;UACA;UACA;UACA;UACA;UACAD,sBAAsB,EAAE,IAAI;UAC5BE,gBAAgB,EAAE,KAAK;UAAE;UACzB;UACA;UACAC,4BAA4B,EAAE,KAAK;UAAE;UACrC;UACA;UACAC,eAAe,EAAE,CACb,YAAY,EAAE,QAAQ,EAAE,gBAAgB,EAAE,SAAS,EAAE,YAAY,EACjE,mBAAmB,EAAE,SAAS,EAAE,EAAE,EAAE,MAAM,EAAE,WAAW,EACvD,eAAe,EAAE,UAAU,EAAE,aAAa,EAAE,UAAU,EAAE,WAAW,EACnE,cAAc,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,EACvE,UAAU,EAAE,qBAAqB,EAAE,UAAU,EAAE,MAAM,EAAE,eAAe,EACtE,MAAM,CACT;UAAE;UACHC,aAAa,EAAE,IAAI;UACnBC,qBAAqB,EAAE,EAAE;UACzBC,kBAAkB,EAAE,aAAa;UACjC;UACA;UACA;UACA;UACAC,wBAAwB,EAAE,KAAK;UAC/BC,4BAA4B,EAAE,IAAI;UAClCC,6BAA6B,EAAE,IAAI;UACnCC,2BAA2B,EAAE;SAChC;QACDC,UAAU,EAAE7G,QAAQ,CAAC8G,aAAa,CAAC,eAAe,CAAC;QACnDC,QAAQ,EAAE;UACNC,WAAW,EAAE,IAAI,CAACC,UAAU,CAACC,SAAS;UACtCC,KAAK,EAAE,IAAI,CAACF,UAAU,CAACE,KAAK;UAC5BC,OAAO,EAAE,OAAO;UAChBrG,IAAI,EAAE;;QAEV;QACA;QACA;QACA;QACA;OACH;MAED,IAAI,CAAC2C,GAAG,GAAG,IAAI2D,oBAAoB,CAAC,IAAI,CAAC1G,MAAM,EAAE,IAAI,CAACgF,OAAO,CAAC;MAC9D,IAAI,CAACjC,GAAG,CAAC4D,iBAAiB,CAAC;QACvBC,YAAY,EAAE,IAAI,CAACpG,WAAW;QAC9BqG,eAAe,EAAE,IAAI,CAAChF,qBAAqB;QAC3CiF,iBAAiB,EAAE,IAAI,CAACnE,uBAAuB;QAC/CoE,qBAAqB,EAAE,IAAI,CAAC7D,2BAA2B;QACvDpD,mBAAmB,EAAE,IAAI,CAAC6D,yBAAyB;QACnDqD,sBAAsB,EAAE,IAAI,CAACnD,gBAAgB;QAC7CoD,sBAAsB,EAAE,IAAI,CAAClD,iBAAiB;QAC9C1B,sBAAsB,EAAE,IAAI,CAACA,sBAAsB;QACnD1B,mBAAmB,EAAE,IAAI,CAACA,mBAAmB;QAC7CU,2BAA2B,EAAE,IAAI,CAACA,2BAA2B;QAC7D6F,uBAAuB,EAAE,IAAI,CAACA,uBAAuB;QACrDjG,qBAAqB,EAAE,IAAI,CAACA,qBAAqB;QACjDuB,aAAa,EAAE,IAAI,CAACA,aAAa;QACjCnC,GAAG,EAAE,IAAI,CAACA;OACb,CAAC;MACF,IAAI,CAAC0C,GAAG,CAACC,cAAc,CAAC,4BAA4B,EAAE;QAClDmE,OAAO,EAAE;OACZ,CAAC;MAEF;MAGA,IAAI,CAACpE,GAAG,CAACC,cAAc,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC;MAE/C,IAAI,CAACD,GAAG,CAACC,cAAc,CAAC,iBAAiB,EACrC;QAAEoE,MAAM,EAAE,IAAI,CAACpC;MAAO,CAAE,CAC3B;IACL;IAEAkC,uBAAuBA,CAAA;MACnBG,KAAK,CAAC,yBAAyB,CAAC;IACpC;IAEAC,QAAQA,CAAA;MACJD,KAAK,CAAC,IAAI,CAACtE,GAAG,CAACuE,QAAQ,EAAE,CAAC;MAC1B,OAAO,IAAI,CAACvE,GAAG,CAACuE,QAAQ,EAAE;IAC9B;IAEAC,gBAAgBA,CAACC,KAAU;MACvB,IAAI,CAACzH,iBAAiB,GAAGyH,KAAK;MAE9B,MAAM3C,MAAM,GAAGxF,QAAQ,CAACyF,cAAc,CAAC,cAAc,CAAC;MACtDD,MAAO,CAACE,SAAS,GAAG,EAAE;MAEtB,IAAI,CAACC,OAAO,GAAG;QACXC,QAAQ,EAAE,IAAI,CAAClF,iBAAiB;QAChCmF,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,GAAG;QACXC,eAAe,EAAE;UACbC,kBAAkB,EAAE,KAAK;UACzBoC,iBAAiB,EAAE,aAAa;UAChCC,oBAAoB,EAAE,iBAAiB;UACvCC,kCAAkC,EAAE,IAAI;UACxCC,sBAAsB,EAAE,KAAK;UAC7BC,oCAAoC,EAAE;SACzC;QACD,aAAa,EAAE;UACX,gBAAgB,EAAE,aAAa;UAC/B,aAAa,EAAE,MAAM;UACrB,gBAAgB,EAAE,aAAa;UAC/B,OAAO,EAAE;SACZ;QACDtC,wBAAwB,EAAE;UACtBmC,oBAAoB,EAAE,qBAAqB;UAC3CC,kCAAkC,EAAE,IAAI;UACxCC,sBAAsB,EAAE,KAAK;UAC7BC,oCAAoC,EAAE,IAAI;UAC1CC,4BAA4B,EAAE,IAAI;UAClCC,+CAA+C,EAAE,IAAI;UACrDC,qBAAqB,EAAE;UACvB;SACH;QACD9B,UAAU,EAAE7G,QAAQ,CAAC8G,aAAa,CAAC,eAAe,CAAC;QACnDE,WAAW,EAAE,WAAW;QACxBjG,IAAI,EAAE,WAAW;QACjBgG,QAAQ,EAAE;UACNC,WAAW,EAAE,WAAW;UACxBjG,IAAI,EAAE;;OAEb;MAED,IAAI,CAAC2C,GAAG,GAAG,IAAI2D,oBAAoB,CAAC,IAAI,CAAC1G,MAAM,EAAE,IAAI,CAACgF,OAAO,CAAC;IAClE;IA+FAiD,qBAAqBA,CAAA;MACjB;MACA,MAAM7E,YAAY,GAAG,IAAI,CAACL,GAAG,CAACpC,mBAAmB,EAAE;MAEnD0G,KAAK,CAACjE,YAAY,CAACN,MAAM,CAAC;MAC1B;MACA,OAAOM,YAAY,CAACN,MAAM;IAC9B;IA0CAd,eAAeA,CAAA;MACX,OAAO,IAAIkG,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACnCC,UAAU,CAAC,MAAK;UAEZF,OAAO,CAAC,IAAI,CAACpF,GAAG,CAACpC,mBAAmB,EAAE,CAAC,CAAC,CAAC;QAC7C,CAAC,EAAE,GAAG,CAAC;MACX,CAAC,CAAC;IACN;IAEA;IACAqC,cAAcA,CAACsF,OAAe;MAC1B,IAAI,CAACvF,GAAG,CAACC,cAAc,CAACsF,OAAO,CAAC;MAChC,IAAIA,OAAO,IAAI,QAAQ,EAAE;QACrB,IAAI,CAAClJ,KAAK,CAACmJ,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;QACnC;MACJ;MAEA,IAAID,OAAO,IAAI,aAAa,EAAE;QAC1B,IAAI,CAACrI,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;MAC1C;MAEA,IAAIqI,OAAO,IAAI,aAAa,EAAE;QAC1B,IAAI,CAACpI,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;MAC1C;IACJ;IAEAsI,sBAAsBA,CAAA;MAClB;MACA;MACA,IAAI,IAAI,CAACzF,GAAG,IAAI,IAAI,CAACA,GAAG,CAACC,cAAc,EAAE;QACrC;QACA;MAAA,CACH,MAAM;QACHzC,OAAO,CAACkI,KAAK,CAAC,gFAAgF,CAAC;MACnG;IACJ;IAEAC,aAAaA,CAACzG,IAAU;MACpB,IAAI,CAACqE,UAAU,GAAGrE,IAAI;IAC1B;IAEA0G,WAAWA,CAACC,IAAY;MACpB,IAAI,CAAC7I,iBAAiB,GAAG6I,IAAI;IACjC;IAEAC,cAAcA,CAACC,OAAe;MAC1B;MACA;MACA,IAAI,CAAC/F,GAAG,CAACC,cAAc,CAAC,SAAS,EAAE8F,OAAO,CAAC;IAC/C;IAEAC,sBAAsBA,CAACC,GAAY;MAC/B,IAAI,CAACnJ,oBAAoB,CAACa,IAAI,CAACsI,GAAG,CAAC;IACvC;IAEAC,gBAAgBA,CAACD,GAAY;MACzB,IAAI,CAACxJ,cAAc,CAACkB,IAAI,CAACsI,GAAG,CAAC;IACjC;IAAC,QAAAE,CAAA,G;uBA9YQhK,gBAAgB,EAAAiK,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAuBbpK,QAAQ,GAAAmK,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;IAAA,QAAAC,EAAA,G;aAvBXvK,gBAAgB;MAAAwK,OAAA,EAAhBxK,gBAAgB,CAAAyK,IAAA;MAAAC,UAAA,EAFb;IAAM;;SAET1K,gBAAgB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}