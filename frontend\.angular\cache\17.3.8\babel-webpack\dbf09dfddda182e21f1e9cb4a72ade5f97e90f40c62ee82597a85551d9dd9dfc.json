{"ast": null, "code": "/**\n * @dynamic is for runtime initializing DomHandler.browser\n *\n * If delete below comment, we can see this error message:\n *  Metadata collected contains an error that will be reported at runtime:\n *  Only initialized variables and constants can be referenced\n *  because the value of this variable is needed by the template compiler.\n */\n// @dynamic\nlet DomHandler = /*#__PURE__*/(() => {\n  class DomHandler {\n    static zindex = 1000;\n    static calculatedScrollbarWidth = null;\n    static calculatedScrollbarHeight = null;\n    static browser;\n    static addClass(element, className) {\n      if (element && className) {\n        if (element.classList) element.classList.add(className);else element.className += ' ' + className;\n      }\n    }\n    static addMultipleClasses(element, className) {\n      if (element && className) {\n        if (element.classList) {\n          let styles = className.trim().split(' ');\n          for (let i = 0; i < styles.length; i++) {\n            element.classList.add(styles[i]);\n          }\n        } else {\n          let styles = className.split(' ');\n          for (let i = 0; i < styles.length; i++) {\n            element.className += ' ' + styles[i];\n          }\n        }\n      }\n    }\n    static removeClass(element, className) {\n      if (element && className) {\n        if (element.classList) element.classList.remove(className);else element.className = element.className.replace(new RegExp('(^|\\\\b)' + className.split(' ').join('|') + '(\\\\b|$)', 'gi'), ' ');\n      }\n    }\n    static removeMultipleClasses(element, classNames) {\n      if (element && classNames) {\n        [classNames].flat().filter(Boolean).forEach(cNames => cNames.split(' ').forEach(className => this.removeClass(element, className)));\n      }\n    }\n    static hasClass(element, className) {\n      if (element && className) {\n        if (element.classList) return element.classList.contains(className);else return new RegExp('(^| )' + className + '( |$)', 'gi').test(element.className);\n      }\n      return false;\n    }\n    static siblings(element) {\n      return Array.prototype.filter.call(element.parentNode.children, function (child) {\n        return child !== element;\n      });\n    }\n    static find(element, selector) {\n      return Array.from(element.querySelectorAll(selector));\n    }\n    static findSingle(element, selector) {\n      return this.isElement(element) ? element.querySelector(selector) : null;\n    }\n    static index(element) {\n      let children = element.parentNode.childNodes;\n      let num = 0;\n      for (var i = 0; i < children.length; i++) {\n        if (children[i] == element) return num;\n        if (children[i].nodeType == 1) num++;\n      }\n      return -1;\n    }\n    static indexWithinGroup(element, attributeName) {\n      let children = element.parentNode ? element.parentNode.childNodes : [];\n      let num = 0;\n      for (var i = 0; i < children.length; i++) {\n        if (children[i] == element) return num;\n        if (children[i].attributes && children[i].attributes[attributeName] && children[i].nodeType == 1) num++;\n      }\n      return -1;\n    }\n    static appendOverlay(overlay, target, appendTo = 'self') {\n      if (appendTo !== 'self' && overlay && target) {\n        this.appendChild(overlay, target);\n      }\n    }\n    static alignOverlay(overlay, target, appendTo = 'self', calculateMinWidth = true) {\n      if (overlay && target) {\n        if (calculateMinWidth) {\n          overlay.style.minWidth = `${DomHandler.getOuterWidth(target)}px`;\n        }\n        if (appendTo === 'self') {\n          this.relativePosition(overlay, target);\n        } else {\n          this.absolutePosition(overlay, target);\n        }\n      }\n    }\n    static relativePosition(element, target, gutter = true) {\n      const getClosestRelativeElement = el => {\n        if (!el) return;\n        return getComputedStyle(el).getPropertyValue('position') === 'relative' ? el : getClosestRelativeElement(el.parentElement);\n      };\n      const elementDimensions = element.offsetParent ? {\n        width: element.offsetWidth,\n        height: element.offsetHeight\n      } : this.getHiddenElementDimensions(element);\n      const targetHeight = target.offsetHeight ?? target.getBoundingClientRect().height;\n      const targetOffset = target.getBoundingClientRect();\n      const windowScrollTop = this.getWindowScrollTop();\n      const windowScrollLeft = this.getWindowScrollLeft();\n      const viewport = this.getViewport();\n      const relativeElement = getClosestRelativeElement(element);\n      const relativeElementOffset = relativeElement?.getBoundingClientRect() || {\n        top: -1 * windowScrollTop,\n        left: -1 * windowScrollLeft\n      };\n      let top, left;\n      if (targetOffset.top + targetHeight + elementDimensions.height > viewport.height) {\n        top = targetOffset.top - relativeElementOffset.top - elementDimensions.height;\n        element.style.transformOrigin = 'bottom';\n        if (targetOffset.top + top < 0) {\n          top = -1 * targetOffset.top;\n        }\n      } else {\n        top = targetHeight + targetOffset.top - relativeElementOffset.top;\n        element.style.transformOrigin = 'top';\n      }\n      const horizontalOverflow = targetOffset.left + elementDimensions.width - viewport.width;\n      const targetLeftOffsetInSpaceOfRelativeElement = targetOffset.left - relativeElementOffset.left;\n      if (elementDimensions.width > viewport.width) {\n        // element wider then viewport and cannot fit on screen (align at left side of viewport)\n        left = (targetOffset.left - relativeElementOffset.left) * -1;\n      } else if (horizontalOverflow > 0) {\n        // element wider then viewport but can be fit on screen (align at right side of viewport)\n        left = targetLeftOffsetInSpaceOfRelativeElement - horizontalOverflow;\n      } else {\n        // element fits on screen (align with target)\n        left = targetOffset.left - relativeElementOffset.left;\n      }\n      element.style.top = top + 'px';\n      element.style.left = left + 'px';\n      gutter && (element.style.marginTop = origin === 'bottom' ? 'calc(var(--p-anchor-gutter) * -1)' : 'calc(var(--p-anchor-gutter))');\n    }\n    static absolutePosition(element, target, gutter = true) {\n      const elementDimensions = element.offsetParent ? {\n        width: element.offsetWidth,\n        height: element.offsetHeight\n      } : this.getHiddenElementDimensions(element);\n      const elementOuterHeight = elementDimensions.height;\n      const elementOuterWidth = elementDimensions.width;\n      const targetOuterHeight = target.offsetHeight ?? target.getBoundingClientRect().height;\n      const targetOuterWidth = target.offsetWidth ?? target.getBoundingClientRect().width;\n      const targetOffset = target.getBoundingClientRect();\n      const windowScrollTop = this.getWindowScrollTop();\n      const windowScrollLeft = this.getWindowScrollLeft();\n      const viewport = this.getViewport();\n      let top, left;\n      if (targetOffset.top + targetOuterHeight + elementOuterHeight > viewport.height) {\n        top = targetOffset.top + windowScrollTop - elementOuterHeight;\n        element.style.transformOrigin = 'bottom';\n        if (top < 0) {\n          top = windowScrollTop;\n        }\n      } else {\n        top = targetOuterHeight + targetOffset.top + windowScrollTop;\n        element.style.transformOrigin = 'top';\n      }\n      if (targetOffset.left + elementOuterWidth > viewport.width) left = Math.max(0, targetOffset.left + windowScrollLeft + targetOuterWidth - elementOuterWidth);else left = targetOffset.left + windowScrollLeft;\n      element.style.top = top + 'px';\n      element.style.left = left + 'px';\n      gutter && (element.style.marginTop = origin === 'bottom' ? 'calc(var(--p-anchor-gutter) * -1)' : 'calc(var(--p-anchor-gutter))');\n    }\n    static getParents(element, parents = []) {\n      return element['parentNode'] === null ? parents : this.getParents(element.parentNode, parents.concat([element.parentNode]));\n    }\n    static getScrollableParents(element) {\n      let scrollableParents = [];\n      if (element) {\n        let parents = this.getParents(element);\n        const overflowRegex = /(auto|scroll)/;\n        const overflowCheck = node => {\n          let styleDeclaration = window['getComputedStyle'](node, null);\n          return overflowRegex.test(styleDeclaration.getPropertyValue('overflow')) || overflowRegex.test(styleDeclaration.getPropertyValue('overflowX')) || overflowRegex.test(styleDeclaration.getPropertyValue('overflowY'));\n        };\n        for (let parent of parents) {\n          let scrollSelectors = parent.nodeType === 1 && parent.dataset['scrollselectors'];\n          if (scrollSelectors) {\n            let selectors = scrollSelectors.split(',');\n            for (let selector of selectors) {\n              let el = this.findSingle(parent, selector);\n              if (el && overflowCheck(el)) {\n                scrollableParents.push(el);\n              }\n            }\n          }\n          if (parent.nodeType !== 9 && overflowCheck(parent)) {\n            scrollableParents.push(parent);\n          }\n        }\n      }\n      return scrollableParents;\n    }\n    static getHiddenElementOuterHeight(element) {\n      element.style.visibility = 'hidden';\n      element.style.display = 'block';\n      let elementHeight = element.offsetHeight;\n      element.style.display = 'none';\n      element.style.visibility = 'visible';\n      return elementHeight;\n    }\n    static getHiddenElementOuterWidth(element) {\n      element.style.visibility = 'hidden';\n      element.style.display = 'block';\n      let elementWidth = element.offsetWidth;\n      element.style.display = 'none';\n      element.style.visibility = 'visible';\n      return elementWidth;\n    }\n    static getHiddenElementDimensions(element) {\n      let dimensions = {};\n      element.style.visibility = 'hidden';\n      element.style.display = 'block';\n      dimensions.width = element.offsetWidth;\n      dimensions.height = element.offsetHeight;\n      element.style.display = 'none';\n      element.style.visibility = 'visible';\n      return dimensions;\n    }\n    static scrollInView(container, item) {\n      let borderTopValue = getComputedStyle(container).getPropertyValue('borderTopWidth');\n      let borderTop = borderTopValue ? parseFloat(borderTopValue) : 0;\n      let paddingTopValue = getComputedStyle(container).getPropertyValue('paddingTop');\n      let paddingTop = paddingTopValue ? parseFloat(paddingTopValue) : 0;\n      let containerRect = container.getBoundingClientRect();\n      let itemRect = item.getBoundingClientRect();\n      let offset = itemRect.top + document.body.scrollTop - (containerRect.top + document.body.scrollTop) - borderTop - paddingTop;\n      let scroll = container.scrollTop;\n      let elementHeight = container.clientHeight;\n      let itemHeight = this.getOuterHeight(item);\n      if (offset < 0) {\n        container.scrollTop = scroll + offset;\n      } else if (offset + itemHeight > elementHeight) {\n        container.scrollTop = scroll + offset - elementHeight + itemHeight;\n      }\n    }\n    static fadeIn(element, duration) {\n      element.style.opacity = 0;\n      let last = +new Date();\n      let opacity = 0;\n      let tick = function () {\n        opacity = +element.style.opacity.replace(',', '.') + (new Date().getTime() - last) / duration;\n        element.style.opacity = opacity;\n        last = +new Date();\n        if (+opacity < 1) {\n          window.requestAnimationFrame && requestAnimationFrame(tick) || setTimeout(tick, 16);\n        }\n      };\n      tick();\n    }\n    static fadeOut(element, ms) {\n      var opacity = 1,\n        interval = 50,\n        duration = ms,\n        gap = interval / duration;\n      let fading = setInterval(() => {\n        opacity = opacity - gap;\n        if (opacity <= 0) {\n          opacity = 0;\n          clearInterval(fading);\n        }\n        element.style.opacity = opacity;\n      }, interval);\n    }\n    static getWindowScrollTop() {\n      let doc = document.documentElement;\n      return (window.pageYOffset || doc.scrollTop) - (doc.clientTop || 0);\n    }\n    static getWindowScrollLeft() {\n      let doc = document.documentElement;\n      return (window.pageXOffset || doc.scrollLeft) - (doc.clientLeft || 0);\n    }\n    static matches(element, selector) {\n      var p = Element.prototype;\n      var f = p['matches'] || p.webkitMatchesSelector || p['mozMatchesSelector'] || p['msMatchesSelector'] || function (s) {\n        return [].indexOf.call(document.querySelectorAll(s), this) !== -1;\n      };\n      return f.call(element, selector);\n    }\n    static getOuterWidth(el, margin) {\n      let width = el.offsetWidth;\n      if (margin) {\n        let style = getComputedStyle(el);\n        width += parseFloat(style.marginLeft) + parseFloat(style.marginRight);\n      }\n      return width;\n    }\n    static getHorizontalPadding(el) {\n      let style = getComputedStyle(el);\n      return parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n    }\n    static getHorizontalMargin(el) {\n      let style = getComputedStyle(el);\n      return parseFloat(style.marginLeft) + parseFloat(style.marginRight);\n    }\n    static innerWidth(el) {\n      let width = el.offsetWidth;\n      let style = getComputedStyle(el);\n      width += parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n      return width;\n    }\n    static width(el) {\n      let width = el.offsetWidth;\n      let style = getComputedStyle(el);\n      width -= parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n      return width;\n    }\n    static getInnerHeight(el) {\n      let height = el.offsetHeight;\n      let style = getComputedStyle(el);\n      height += parseFloat(style.paddingTop) + parseFloat(style.paddingBottom);\n      return height;\n    }\n    static getOuterHeight(el, margin) {\n      let height = el.offsetHeight;\n      if (margin) {\n        let style = getComputedStyle(el);\n        height += parseFloat(style.marginTop) + parseFloat(style.marginBottom);\n      }\n      return height;\n    }\n    static getHeight(el) {\n      let height = el.offsetHeight;\n      let style = getComputedStyle(el);\n      height -= parseFloat(style.paddingTop) + parseFloat(style.paddingBottom) + parseFloat(style.borderTopWidth) + parseFloat(style.borderBottomWidth);\n      return height;\n    }\n    static getWidth(el) {\n      let width = el.offsetWidth;\n      let style = getComputedStyle(el);\n      width -= parseFloat(style.paddingLeft) + parseFloat(style.paddingRight) + parseFloat(style.borderLeftWidth) + parseFloat(style.borderRightWidth);\n      return width;\n    }\n    static getViewport() {\n      let win = window,\n        d = document,\n        e = d.documentElement,\n        g = d.getElementsByTagName('body')[0],\n        w = win.innerWidth || e.clientWidth || g.clientWidth,\n        h = win.innerHeight || e.clientHeight || g.clientHeight;\n      return {\n        width: w,\n        height: h\n      };\n    }\n    static getOffset(el) {\n      var rect = el.getBoundingClientRect();\n      return {\n        top: rect.top + (window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0),\n        left: rect.left + (window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0)\n      };\n    }\n    static replaceElementWith(element, replacementElement) {\n      let parentNode = element.parentNode;\n      if (!parentNode) throw `Can't replace element`;\n      return parentNode.replaceChild(replacementElement, element);\n    }\n    static getUserAgent() {\n      if (navigator && this.isClient()) {\n        return navigator.userAgent;\n      }\n    }\n    static isIE() {\n      var ua = window.navigator.userAgent;\n      var msie = ua.indexOf('MSIE ');\n      if (msie > 0) {\n        // IE 10 or older => return version number\n        return true;\n      }\n      var trident = ua.indexOf('Trident/');\n      if (trident > 0) {\n        // IE 11 => return version number\n        var rv = ua.indexOf('rv:');\n        return true;\n      }\n      var edge = ua.indexOf('Edge/');\n      if (edge > 0) {\n        // Edge (IE 12+) => return version number\n        return true;\n      }\n      // other browser\n      return false;\n    }\n    static isIOS() {\n      return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window['MSStream'];\n    }\n    static isAndroid() {\n      return /(android)/i.test(navigator.userAgent);\n    }\n    static isTouchDevice() {\n      return 'ontouchstart' in window || navigator.maxTouchPoints > 0;\n    }\n    static appendChild(element, target) {\n      if (this.isElement(target)) target.appendChild(element);else if (target && target.el && target.el.nativeElement) target.el.nativeElement.appendChild(element);else throw 'Cannot append ' + target + ' to ' + element;\n    }\n    static removeChild(element, target) {\n      if (this.isElement(target)) target.removeChild(element);else if (target.el && target.el.nativeElement) target.el.nativeElement.removeChild(element);else throw 'Cannot remove ' + element + ' from ' + target;\n    }\n    static removeElement(element) {\n      if (!('remove' in Element.prototype)) element.parentNode.removeChild(element);else element.remove();\n    }\n    static isElement(obj) {\n      return typeof HTMLElement === 'object' ? obj instanceof HTMLElement : obj && typeof obj === 'object' && obj !== null && obj.nodeType === 1 && typeof obj.nodeName === 'string';\n    }\n    static calculateScrollbarWidth(el) {\n      if (el) {\n        let style = getComputedStyle(el);\n        return el.offsetWidth - el.clientWidth - parseFloat(style.borderLeftWidth) - parseFloat(style.borderRightWidth);\n      } else {\n        if (this.calculatedScrollbarWidth !== null) return this.calculatedScrollbarWidth;\n        let scrollDiv = document.createElement('div');\n        scrollDiv.className = 'p-scrollbar-measure';\n        document.body.appendChild(scrollDiv);\n        let scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n        document.body.removeChild(scrollDiv);\n        this.calculatedScrollbarWidth = scrollbarWidth;\n        return scrollbarWidth;\n      }\n    }\n    static calculateScrollbarHeight() {\n      if (this.calculatedScrollbarHeight !== null) return this.calculatedScrollbarHeight;\n      let scrollDiv = document.createElement('div');\n      scrollDiv.className = 'p-scrollbar-measure';\n      document.body.appendChild(scrollDiv);\n      let scrollbarHeight = scrollDiv.offsetHeight - scrollDiv.clientHeight;\n      document.body.removeChild(scrollDiv);\n      this.calculatedScrollbarWidth = scrollbarHeight;\n      return scrollbarHeight;\n    }\n    static invokeElementMethod(element, methodName, args) {\n      element[methodName].apply(element, args);\n    }\n    static clearSelection() {\n      if (window.getSelection) {\n        if (window.getSelection().empty) {\n          window.getSelection().empty();\n        } else if (window.getSelection().removeAllRanges && window.getSelection().rangeCount > 0 && window.getSelection().getRangeAt(0).getClientRects().length > 0) {\n          window.getSelection().removeAllRanges();\n        }\n      } else if (document['selection'] && document['selection'].empty) {\n        try {\n          document['selection'].empty();\n        } catch (error) {\n          //ignore IE bug\n        }\n      }\n    }\n    static getBrowser() {\n      if (!this.browser) {\n        let matched = this.resolveUserAgent();\n        this.browser = {};\n        if (matched.browser) {\n          this.browser[matched.browser] = true;\n          this.browser['version'] = matched.version;\n        }\n        if (this.browser['chrome']) {\n          this.browser['webkit'] = true;\n        } else if (this.browser['webkit']) {\n          this.browser['safari'] = true;\n        }\n      }\n      return this.browser;\n    }\n    static resolveUserAgent() {\n      let ua = navigator.userAgent.toLowerCase();\n      let match = /(chrome)[ \\/]([\\w.]+)/.exec(ua) || /(webkit)[ \\/]([\\w.]+)/.exec(ua) || /(opera)(?:.*version|)[ \\/]([\\w.]+)/.exec(ua) || /(msie) ([\\w.]+)/.exec(ua) || ua.indexOf('compatible') < 0 && /(mozilla)(?:.*? rv:([\\w.]+)|)/.exec(ua) || [];\n      return {\n        browser: match[1] || '',\n        version: match[2] || '0'\n      };\n    }\n    static isInteger(value) {\n      if (Number.isInteger) {\n        return Number.isInteger(value);\n      } else {\n        return typeof value === 'number' && isFinite(value) && Math.floor(value) === value;\n      }\n    }\n    static isHidden(element) {\n      return !element || element.offsetParent === null;\n    }\n    static isVisible(element) {\n      return element && element.offsetParent != null;\n    }\n    static isExist(element) {\n      return element !== null && typeof element !== 'undefined' && element.nodeName && element.parentNode;\n    }\n    static focus(element, options) {\n      element && document.activeElement !== element && element.focus(options);\n    }\n    static getFocusableSelectorString(selector = '') {\n      return `button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        [href][clientHeight][clientWidth]:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        input:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        select:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        textarea:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        [tabIndex]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        [contenteditable]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        .p-inputtext:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        .p-button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector}`;\n    }\n    static getFocusableElements(element, selector = '') {\n      let focusableElements = this.find(element, this.getFocusableSelectorString(selector));\n      let visibleFocusableElements = [];\n      for (let focusableElement of focusableElements) {\n        const computedStyle = getComputedStyle(focusableElement);\n        if (this.isVisible(focusableElement) && computedStyle.display != 'none' && computedStyle.visibility != 'hidden') visibleFocusableElements.push(focusableElement);\n      }\n      return visibleFocusableElements;\n    }\n    static getFocusableElement(element, selector = '') {\n      let focusableElement = this.findSingle(element, this.getFocusableSelectorString(selector));\n      if (focusableElement) {\n        const computedStyle = getComputedStyle(focusableElement);\n        if (this.isVisible(focusableElement) && computedStyle.display != 'none' && computedStyle.visibility != 'hidden') return focusableElement;\n      }\n      return null;\n    }\n    static getFirstFocusableElement(element, selector = '') {\n      const focusableElements = this.getFocusableElements(element, selector);\n      return focusableElements.length > 0 ? focusableElements[0] : null;\n    }\n    static getLastFocusableElement(element, selector) {\n      const focusableElements = this.getFocusableElements(element, selector);\n      return focusableElements.length > 0 ? focusableElements[focusableElements.length - 1] : null;\n    }\n    static getNextFocusableElement(element, reverse = false) {\n      const focusableElements = DomHandler.getFocusableElements(element);\n      let index = 0;\n      if (focusableElements && focusableElements.length > 0) {\n        const focusedIndex = focusableElements.indexOf(focusableElements[0].ownerDocument.activeElement);\n        if (reverse) {\n          if (focusedIndex == -1 || focusedIndex === 0) {\n            index = focusableElements.length - 1;\n          } else {\n            index = focusedIndex - 1;\n          }\n        } else if (focusedIndex != -1 && focusedIndex !== focusableElements.length - 1) {\n          index = focusedIndex + 1;\n        }\n      }\n      return focusableElements[index];\n    }\n    static generateZIndex() {\n      this.zindex = this.zindex || 999;\n      return ++this.zindex;\n    }\n    static getSelection() {\n      if (window.getSelection) return window.getSelection().toString();else if (document.getSelection) return document.getSelection().toString();else if (document['selection']) return document['selection'].createRange().text;\n      return null;\n    }\n    static getTargetElement(target, el) {\n      if (!target) return null;\n      switch (target) {\n        case 'document':\n          return document;\n        case 'window':\n          return window;\n        case '@next':\n          return el?.nextElementSibling;\n        case '@prev':\n          return el?.previousElementSibling;\n        case '@parent':\n          return el?.parentElement;\n        case '@grandparent':\n          return el?.parentElement.parentElement;\n        default:\n          const type = typeof target;\n          if (type === 'string') {\n            return document.querySelector(target);\n          } else if (type === 'object' && target.hasOwnProperty('nativeElement')) {\n            return this.isExist(target.nativeElement) ? target.nativeElement : undefined;\n          }\n          const isFunction = obj => !!(obj && obj.constructor && obj.call && obj.apply);\n          const element = isFunction(target) ? target() : target;\n          return element && element.nodeType === 9 || this.isExist(element) ? element : null;\n      }\n    }\n    static isClient() {\n      return !!(typeof window !== 'undefined' && window.document && window.document.createElement);\n    }\n    static getAttribute(element, name) {\n      if (element) {\n        const value = element.getAttribute(name);\n        if (!isNaN(value)) {\n          return +value;\n        }\n        if (value === 'true' || value === 'false') {\n          return value === 'true';\n        }\n        return value;\n      }\n      return undefined;\n    }\n    static calculateBodyScrollbarWidth() {\n      return window.innerWidth - document.documentElement.offsetWidth;\n    }\n    static blockBodyScroll(className = 'p-overflow-hidden') {\n      document.body.style.setProperty('--scrollbar-width', this.calculateBodyScrollbarWidth() + 'px');\n      this.addClass(document.body, className);\n    }\n    static unblockBodyScroll(className = 'p-overflow-hidden') {\n      document.body.style.removeProperty('--scrollbar-width');\n      this.removeClass(document.body, className);\n    }\n    static createElement(type, attributes = {}, ...children) {\n      if (type) {\n        const element = document.createElement(type);\n        this.setAttributes(element, attributes);\n        element.append(...children);\n        return element;\n      }\n      return undefined;\n    }\n    static setAttribute(element, attribute = '', value) {\n      if (this.isElement(element) && value !== null && value !== undefined) {\n        element.setAttribute(attribute, value);\n      }\n    }\n    static setAttributes(element, attributes = {}) {\n      if (this.isElement(element)) {\n        const computedStyles = (rule, value) => {\n          const styles = element?.$attrs?.[rule] ? [element?.$attrs?.[rule]] : [];\n          return [value].flat().reduce((cv, v) => {\n            if (v !== null && v !== undefined) {\n              const type = typeof v;\n              if (type === 'string' || type === 'number') {\n                cv.push(v);\n              } else if (type === 'object') {\n                const _cv = Array.isArray(v) ? computedStyles(rule, v) : Object.entries(v).map(([_k, _v]) => rule === 'style' && (!!_v || _v === 0) ? `${_k.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase()}:${_v}` : !!_v ? _k : undefined);\n                cv = _cv.length ? cv.concat(_cv.filter(c => !!c)) : cv;\n              }\n            }\n            return cv;\n          }, styles);\n        };\n        Object.entries(attributes).forEach(([key, value]) => {\n          if (value !== undefined && value !== null) {\n            const matchedEvent = key.match(/^on(.+)/);\n            if (matchedEvent) {\n              element.addEventListener(matchedEvent[1].toLowerCase(), value);\n            } else if (key === 'pBind') {\n              this.setAttributes(element, value);\n            } else {\n              value = key === 'class' ? [...new Set(computedStyles('class', value))].join(' ').trim() : key === 'style' ? computedStyles('style', value).join(';').trim() : value;\n              (element.$attrs = element.$attrs || {}) && (element.$attrs[key] = value);\n              element.setAttribute(key, value);\n            }\n          }\n        });\n      }\n    }\n    static isFocusableElement(element, selector = '') {\n      return this.isElement(element) ? element.matches(`button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                [href][clientHeight][clientWidth]:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                input:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                select:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                textarea:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                [tabIndex]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                [contenteditable]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector}`) : false;\n    }\n  }\n  return DomHandler;\n})();\nclass ConnectedOverlayScrollHandler {\n  element;\n  listener;\n  scrollableParents;\n  constructor(element, listener = () => {}) {\n    this.element = element;\n    this.listener = listener;\n  }\n  bindScrollListener() {\n    this.scrollableParents = DomHandler.getScrollableParents(this.element);\n    for (let i = 0; i < this.scrollableParents.length; i++) {\n      this.scrollableParents[i].addEventListener('scroll', this.listener);\n    }\n  }\n  unbindScrollListener() {\n    if (this.scrollableParents) {\n      for (let i = 0; i < this.scrollableParents.length; i++) {\n        this.scrollableParents[i].removeEventListener('scroll', this.listener);\n      }\n    }\n  }\n  destroy() {\n    this.unbindScrollListener();\n    this.element = null;\n    this.listener = null;\n    this.scrollableParents = null;\n  }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ConnectedOverlayScrollHandler, DomHandler };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "zindex", "calculatedScrollbarWidth", "calculatedScrollbarHeight", "browser", "addClass", "element", "className", "classList", "add", "addMultipleClasses", "styles", "trim", "split", "i", "length", "removeClass", "remove", "replace", "RegExp", "join", "removeMultipleClasses", "classNames", "flat", "filter", "Boolean", "for<PERSON>ach", "cNames", "hasClass", "contains", "test", "siblings", "Array", "prototype", "call", "parentNode", "children", "child", "find", "selector", "from", "querySelectorAll", "findSingle", "isElement", "querySelector", "index", "childNodes", "num", "nodeType", "indexWithinGroup", "attributeName", "attributes", "appendOverlay", "overlay", "target", "appendTo", "append<PERSON><PERSON><PERSON>", "alignOverlay", "calculateMinWidth", "style", "min<PERSON><PERSON><PERSON>", "getOuterWidth", "relativePosition", "absolutePosition", "gutter", "getClosestRelativeElement", "el", "getComputedStyle", "getPropertyValue", "parentElement", "elementDimensions", "offsetParent", "width", "offsetWidth", "height", "offsetHeight", "getHiddenElementDimensions", "targetHeight", "getBoundingClientRect", "targetOffset", "windowScrollTop", "getWindowScrollTop", "windowScrollLeft", "getWindowScrollLeft", "viewport", "getViewport", "relativeElement", "relativeElementOffset", "top", "left", "transform<PERSON><PERSON>in", "horizontalOverflow", "targetLeftOffsetInSpaceOfRelativeElement", "marginTop", "origin", "elementOuterHeight", "elementOuterWidth", "targetOuterHeight", "targetOuterWidth", "Math", "max", "getParents", "parents", "concat", "getScrollableParents", "scrollableParents", "overflowRegex", "overflowCheck", "node", "styleDeclaration", "window", "parent", "scrollSelectors", "dataset", "selectors", "push", "getHiddenElementOuterHeight", "visibility", "display", "elementHeight", "getHiddenElementOuterWidth", "elementWidth", "dimensions", "scrollInView", "container", "item", "borderTopValue", "borderTop", "parseFloat", "paddingTopValue", "paddingTop", "containerRect", "itemRect", "offset", "document", "body", "scrollTop", "scroll", "clientHeight", "itemHeight", "getOuterHeight", "fadeIn", "duration", "opacity", "last", "Date", "tick", "getTime", "requestAnimationFrame", "setTimeout", "fadeOut", "ms", "interval", "gap", "fading", "setInterval", "clearInterval", "doc", "documentElement", "pageYOffset", "clientTop", "pageXOffset", "scrollLeft", "clientLeft", "matches", "p", "Element", "f", "webkitMatchesSelector", "s", "indexOf", "margin", "marginLeft", "marginRight", "getHorizontalPadding", "paddingLeft", "paddingRight", "getHorizontalMargin", "innerWidth", "getInnerHeight", "paddingBottom", "marginBottom", "getHeight", "borderTopWidth", "borderBottomWidth", "getWidth", "borderLeftWidth", "borderRightWidth", "win", "d", "e", "g", "getElementsByTagName", "w", "clientWidth", "h", "innerHeight", "getOffset", "rect", "replaceElementWith", "replacementElement", "<PERSON><PERSON><PERSON><PERSON>", "getUserAgent", "navigator", "isClient", "userAgent", "isIE", "ua", "msie", "trident", "rv", "edge", "isIOS", "isAndroid", "isTouchDevice", "maxTouchPoints", "nativeElement", "<PERSON><PERSON><PERSON><PERSON>", "removeElement", "obj", "HTMLElement", "nodeName", "calculateScrollbarWidth", "scrollDiv", "createElement", "scrollbarWidth", "calculateScrollbarHeight", "scrollbarHeight", "invokeElementMethod", "methodName", "args", "apply", "clearSelection", "getSelection", "empty", "removeAllRanges", "rangeCount", "getRangeAt", "getClientRects", "error", "<PERSON><PERSON><PERSON><PERSON>", "matched", "resolveUserAgent", "version", "toLowerCase", "match", "exec", "isInteger", "value", "Number", "isFinite", "floor", "isHidden", "isVisible", "isExist", "focus", "options", "activeElement", "getFocusableSelectorString", "getFocusableElements", "focusableElements", "visibleFocusableElements", "focusableElement", "computedStyle", "getFocusableElement", "getFirstFocusableElement", "getLastFocusableElement", "getNextFocusableElement", "reverse", "focusedIndex", "ownerDocument", "generateZIndex", "toString", "createRange", "text", "getTargetElement", "nextElement<PERSON><PERSON>ling", "previousElementSibling", "type", "hasOwnProperty", "undefined", "isFunction", "constructor", "getAttribute", "name", "isNaN", "calculateBodyScrollbarWidth", "blockBodyScroll", "setProperty", "unblockBodyScroll", "removeProperty", "setAttributes", "append", "setAttribute", "attribute", "computedStyles", "rule", "$attrs", "reduce", "cv", "v", "_cv", "isArray", "Object", "entries", "map", "_k", "_v", "c", "key", "matchedEvent", "addEventListener", "Set", "isFocusableElement", "ConnectedOverlayScrollHandler", "listener", "bindScrollListener", "unbindScrollListener", "removeEventListener", "destroy"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/primeng/fesm2022/primeng-dom.mjs"], "sourcesContent": ["/**\n * @dynamic is for runtime initializing DomHandler.browser\n *\n * If delete below comment, we can see this error message:\n *  Metadata collected contains an error that will be reported at runtime:\n *  Only initialized variables and constants can be referenced\n *  because the value of this variable is needed by the template compiler.\n */\n// @dynamic\nclass DomHandler {\n    static zindex = 1000;\n    static calculatedScrollbarWidth = null;\n    static calculatedScrollbarHeight = null;\n    static browser;\n    static addClass(element, className) {\n        if (element && className) {\n            if (element.classList)\n                element.classList.add(className);\n            else\n                element.className += ' ' + className;\n        }\n    }\n    static addMultipleClasses(element, className) {\n        if (element && className) {\n            if (element.classList) {\n                let styles = className.trim().split(' ');\n                for (let i = 0; i < styles.length; i++) {\n                    element.classList.add(styles[i]);\n                }\n            }\n            else {\n                let styles = className.split(' ');\n                for (let i = 0; i < styles.length; i++) {\n                    element.className += ' ' + styles[i];\n                }\n            }\n        }\n    }\n    static removeClass(element, className) {\n        if (element && className) {\n            if (element.classList)\n                element.classList.remove(className);\n            else\n                element.className = element.className.replace(new RegExp('(^|\\\\b)' + className.split(' ').join('|') + '(\\\\b|$)', 'gi'), ' ');\n        }\n    }\n    static removeMultipleClasses(element, classNames) {\n        if (element && classNames) {\n            [classNames]\n                .flat()\n                .filter(Boolean)\n                .forEach((cNames) => cNames.split(' ').forEach((className) => this.removeClass(element, className)));\n        }\n    }\n    static hasClass(element, className) {\n        if (element && className) {\n            if (element.classList)\n                return element.classList.contains(className);\n            else\n                return new RegExp('(^| )' + className + '( |$)', 'gi').test(element.className);\n        }\n        return false;\n    }\n    static siblings(element) {\n        return Array.prototype.filter.call(element.parentNode.children, function (child) {\n            return child !== element;\n        });\n    }\n    static find(element, selector) {\n        return Array.from(element.querySelectorAll(selector));\n    }\n    static findSingle(element, selector) {\n        return this.isElement(element) ? element.querySelector(selector) : null;\n    }\n    static index(element) {\n        let children = element.parentNode.childNodes;\n        let num = 0;\n        for (var i = 0; i < children.length; i++) {\n            if (children[i] == element)\n                return num;\n            if (children[i].nodeType == 1)\n                num++;\n        }\n        return -1;\n    }\n    static indexWithinGroup(element, attributeName) {\n        let children = element.parentNode ? element.parentNode.childNodes : [];\n        let num = 0;\n        for (var i = 0; i < children.length; i++) {\n            if (children[i] == element)\n                return num;\n            if (children[i].attributes && children[i].attributes[attributeName] && children[i].nodeType == 1)\n                num++;\n        }\n        return -1;\n    }\n    static appendOverlay(overlay, target, appendTo = 'self') {\n        if (appendTo !== 'self' && overlay && target) {\n            this.appendChild(overlay, target);\n        }\n    }\n    static alignOverlay(overlay, target, appendTo = 'self', calculateMinWidth = true) {\n        if (overlay && target) {\n            if (calculateMinWidth) {\n                overlay.style.minWidth = `${DomHandler.getOuterWidth(target)}px`;\n            }\n            if (appendTo === 'self') {\n                this.relativePosition(overlay, target);\n            }\n            else {\n                this.absolutePosition(overlay, target);\n            }\n        }\n    }\n    static relativePosition(element, target, gutter = true) {\n        const getClosestRelativeElement = (el) => {\n            if (!el)\n                return;\n            return getComputedStyle(el).getPropertyValue('position') === 'relative' ? el : getClosestRelativeElement(el.parentElement);\n        };\n        const elementDimensions = element.offsetParent ? { width: element.offsetWidth, height: element.offsetHeight } : this.getHiddenElementDimensions(element);\n        const targetHeight = target.offsetHeight ?? target.getBoundingClientRect().height;\n        const targetOffset = target.getBoundingClientRect();\n        const windowScrollTop = this.getWindowScrollTop();\n        const windowScrollLeft = this.getWindowScrollLeft();\n        const viewport = this.getViewport();\n        const relativeElement = getClosestRelativeElement(element);\n        const relativeElementOffset = relativeElement?.getBoundingClientRect() || { top: -1 * windowScrollTop, left: -1 * windowScrollLeft };\n        let top, left;\n        if (targetOffset.top + targetHeight + elementDimensions.height > viewport.height) {\n            top = targetOffset.top - relativeElementOffset.top - elementDimensions.height;\n            element.style.transformOrigin = 'bottom';\n            if (targetOffset.top + top < 0) {\n                top = -1 * targetOffset.top;\n            }\n        }\n        else {\n            top = targetHeight + targetOffset.top - relativeElementOffset.top;\n            element.style.transformOrigin = 'top';\n        }\n        const horizontalOverflow = targetOffset.left + elementDimensions.width - viewport.width;\n        const targetLeftOffsetInSpaceOfRelativeElement = targetOffset.left - relativeElementOffset.left;\n        if (elementDimensions.width > viewport.width) {\n            // element wider then viewport and cannot fit on screen (align at left side of viewport)\n            left = (targetOffset.left - relativeElementOffset.left) * -1;\n        }\n        else if (horizontalOverflow > 0) {\n            // element wider then viewport but can be fit on screen (align at right side of viewport)\n            left = targetLeftOffsetInSpaceOfRelativeElement - horizontalOverflow;\n        }\n        else {\n            // element fits on screen (align with target)\n            left = targetOffset.left - relativeElementOffset.left;\n        }\n        element.style.top = top + 'px';\n        element.style.left = left + 'px';\n        gutter && (element.style.marginTop = origin === 'bottom' ? 'calc(var(--p-anchor-gutter) * -1)' : 'calc(var(--p-anchor-gutter))');\n    }\n    static absolutePosition(element, target, gutter = true) {\n        const elementDimensions = element.offsetParent ? { width: element.offsetWidth, height: element.offsetHeight } : this.getHiddenElementDimensions(element);\n        const elementOuterHeight = elementDimensions.height;\n        const elementOuterWidth = elementDimensions.width;\n        const targetOuterHeight = target.offsetHeight ?? target.getBoundingClientRect().height;\n        const targetOuterWidth = target.offsetWidth ?? target.getBoundingClientRect().width;\n        const targetOffset = target.getBoundingClientRect();\n        const windowScrollTop = this.getWindowScrollTop();\n        const windowScrollLeft = this.getWindowScrollLeft();\n        const viewport = this.getViewport();\n        let top, left;\n        if (targetOffset.top + targetOuterHeight + elementOuterHeight > viewport.height) {\n            top = targetOffset.top + windowScrollTop - elementOuterHeight;\n            element.style.transformOrigin = 'bottom';\n            if (top < 0) {\n                top = windowScrollTop;\n            }\n        }\n        else {\n            top = targetOuterHeight + targetOffset.top + windowScrollTop;\n            element.style.transformOrigin = 'top';\n        }\n        if (targetOffset.left + elementOuterWidth > viewport.width)\n            left = Math.max(0, targetOffset.left + windowScrollLeft + targetOuterWidth - elementOuterWidth);\n        else\n            left = targetOffset.left + windowScrollLeft;\n        element.style.top = top + 'px';\n        element.style.left = left + 'px';\n        gutter && (element.style.marginTop = origin === 'bottom' ? 'calc(var(--p-anchor-gutter) * -1)' : 'calc(var(--p-anchor-gutter))');\n    }\n    static getParents(element, parents = []) {\n        return element['parentNode'] === null ? parents : this.getParents(element.parentNode, parents.concat([element.parentNode]));\n    }\n    static getScrollableParents(element) {\n        let scrollableParents = [];\n        if (element) {\n            let parents = this.getParents(element);\n            const overflowRegex = /(auto|scroll)/;\n            const overflowCheck = (node) => {\n                let styleDeclaration = window['getComputedStyle'](node, null);\n                return overflowRegex.test(styleDeclaration.getPropertyValue('overflow')) || overflowRegex.test(styleDeclaration.getPropertyValue('overflowX')) || overflowRegex.test(styleDeclaration.getPropertyValue('overflowY'));\n            };\n            for (let parent of parents) {\n                let scrollSelectors = parent.nodeType === 1 && parent.dataset['scrollselectors'];\n                if (scrollSelectors) {\n                    let selectors = scrollSelectors.split(',');\n                    for (let selector of selectors) {\n                        let el = this.findSingle(parent, selector);\n                        if (el && overflowCheck(el)) {\n                            scrollableParents.push(el);\n                        }\n                    }\n                }\n                if (parent.nodeType !== 9 && overflowCheck(parent)) {\n                    scrollableParents.push(parent);\n                }\n            }\n        }\n        return scrollableParents;\n    }\n    static getHiddenElementOuterHeight(element) {\n        element.style.visibility = 'hidden';\n        element.style.display = 'block';\n        let elementHeight = element.offsetHeight;\n        element.style.display = 'none';\n        element.style.visibility = 'visible';\n        return elementHeight;\n    }\n    static getHiddenElementOuterWidth(element) {\n        element.style.visibility = 'hidden';\n        element.style.display = 'block';\n        let elementWidth = element.offsetWidth;\n        element.style.display = 'none';\n        element.style.visibility = 'visible';\n        return elementWidth;\n    }\n    static getHiddenElementDimensions(element) {\n        let dimensions = {};\n        element.style.visibility = 'hidden';\n        element.style.display = 'block';\n        dimensions.width = element.offsetWidth;\n        dimensions.height = element.offsetHeight;\n        element.style.display = 'none';\n        element.style.visibility = 'visible';\n        return dimensions;\n    }\n    static scrollInView(container, item) {\n        let borderTopValue = getComputedStyle(container).getPropertyValue('borderTopWidth');\n        let borderTop = borderTopValue ? parseFloat(borderTopValue) : 0;\n        let paddingTopValue = getComputedStyle(container).getPropertyValue('paddingTop');\n        let paddingTop = paddingTopValue ? parseFloat(paddingTopValue) : 0;\n        let containerRect = container.getBoundingClientRect();\n        let itemRect = item.getBoundingClientRect();\n        let offset = itemRect.top + document.body.scrollTop - (containerRect.top + document.body.scrollTop) - borderTop - paddingTop;\n        let scroll = container.scrollTop;\n        let elementHeight = container.clientHeight;\n        let itemHeight = this.getOuterHeight(item);\n        if (offset < 0) {\n            container.scrollTop = scroll + offset;\n        }\n        else if (offset + itemHeight > elementHeight) {\n            container.scrollTop = scroll + offset - elementHeight + itemHeight;\n        }\n    }\n    static fadeIn(element, duration) {\n        element.style.opacity = 0;\n        let last = +new Date();\n        let opacity = 0;\n        let tick = function () {\n            opacity = +element.style.opacity.replace(',', '.') + (new Date().getTime() - last) / duration;\n            element.style.opacity = opacity;\n            last = +new Date();\n            if (+opacity < 1) {\n                (window.requestAnimationFrame && requestAnimationFrame(tick)) || setTimeout(tick, 16);\n            }\n        };\n        tick();\n    }\n    static fadeOut(element, ms) {\n        var opacity = 1, interval = 50, duration = ms, gap = interval / duration;\n        let fading = setInterval(() => {\n            opacity = opacity - gap;\n            if (opacity <= 0) {\n                opacity = 0;\n                clearInterval(fading);\n            }\n            element.style.opacity = opacity;\n        }, interval);\n    }\n    static getWindowScrollTop() {\n        let doc = document.documentElement;\n        return (window.pageYOffset || doc.scrollTop) - (doc.clientTop || 0);\n    }\n    static getWindowScrollLeft() {\n        let doc = document.documentElement;\n        return (window.pageXOffset || doc.scrollLeft) - (doc.clientLeft || 0);\n    }\n    static matches(element, selector) {\n        var p = Element.prototype;\n        var f = p['matches'] ||\n            p.webkitMatchesSelector ||\n            p['mozMatchesSelector'] ||\n            p['msMatchesSelector'] ||\n            function (s) {\n                return [].indexOf.call(document.querySelectorAll(s), this) !== -1;\n            };\n        return f.call(element, selector);\n    }\n    static getOuterWidth(el, margin) {\n        let width = el.offsetWidth;\n        if (margin) {\n            let style = getComputedStyle(el);\n            width += parseFloat(style.marginLeft) + parseFloat(style.marginRight);\n        }\n        return width;\n    }\n    static getHorizontalPadding(el) {\n        let style = getComputedStyle(el);\n        return parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n    }\n    static getHorizontalMargin(el) {\n        let style = getComputedStyle(el);\n        return parseFloat(style.marginLeft) + parseFloat(style.marginRight);\n    }\n    static innerWidth(el) {\n        let width = el.offsetWidth;\n        let style = getComputedStyle(el);\n        width += parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n        return width;\n    }\n    static width(el) {\n        let width = el.offsetWidth;\n        let style = getComputedStyle(el);\n        width -= parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n        return width;\n    }\n    static getInnerHeight(el) {\n        let height = el.offsetHeight;\n        let style = getComputedStyle(el);\n        height += parseFloat(style.paddingTop) + parseFloat(style.paddingBottom);\n        return height;\n    }\n    static getOuterHeight(el, margin) {\n        let height = el.offsetHeight;\n        if (margin) {\n            let style = getComputedStyle(el);\n            height += parseFloat(style.marginTop) + parseFloat(style.marginBottom);\n        }\n        return height;\n    }\n    static getHeight(el) {\n        let height = el.offsetHeight;\n        let style = getComputedStyle(el);\n        height -= parseFloat(style.paddingTop) + parseFloat(style.paddingBottom) + parseFloat(style.borderTopWidth) + parseFloat(style.borderBottomWidth);\n        return height;\n    }\n    static getWidth(el) {\n        let width = el.offsetWidth;\n        let style = getComputedStyle(el);\n        width -= parseFloat(style.paddingLeft) + parseFloat(style.paddingRight) + parseFloat(style.borderLeftWidth) + parseFloat(style.borderRightWidth);\n        return width;\n    }\n    static getViewport() {\n        let win = window, d = document, e = d.documentElement, g = d.getElementsByTagName('body')[0], w = win.innerWidth || e.clientWidth || g.clientWidth, h = win.innerHeight || e.clientHeight || g.clientHeight;\n        return { width: w, height: h };\n    }\n    static getOffset(el) {\n        var rect = el.getBoundingClientRect();\n        return {\n            top: rect.top + (window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0),\n            left: rect.left + (window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0)\n        };\n    }\n    static replaceElementWith(element, replacementElement) {\n        let parentNode = element.parentNode;\n        if (!parentNode)\n            throw `Can't replace element`;\n        return parentNode.replaceChild(replacementElement, element);\n    }\n    static getUserAgent() {\n        if (navigator && this.isClient()) {\n            return navigator.userAgent;\n        }\n    }\n    static isIE() {\n        var ua = window.navigator.userAgent;\n        var msie = ua.indexOf('MSIE ');\n        if (msie > 0) {\n            // IE 10 or older => return version number\n            return true;\n        }\n        var trident = ua.indexOf('Trident/');\n        if (trident > 0) {\n            // IE 11 => return version number\n            var rv = ua.indexOf('rv:');\n            return true;\n        }\n        var edge = ua.indexOf('Edge/');\n        if (edge > 0) {\n            // Edge (IE 12+) => return version number\n            return true;\n        }\n        // other browser\n        return false;\n    }\n    static isIOS() {\n        return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window['MSStream'];\n    }\n    static isAndroid() {\n        return /(android)/i.test(navigator.userAgent);\n    }\n    static isTouchDevice() {\n        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;\n    }\n    static appendChild(element, target) {\n        if (this.isElement(target))\n            target.appendChild(element);\n        else if (target && target.el && target.el.nativeElement)\n            target.el.nativeElement.appendChild(element);\n        else\n            throw 'Cannot append ' + target + ' to ' + element;\n    }\n    static removeChild(element, target) {\n        if (this.isElement(target))\n            target.removeChild(element);\n        else if (target.el && target.el.nativeElement)\n            target.el.nativeElement.removeChild(element);\n        else\n            throw 'Cannot remove ' + element + ' from ' + target;\n    }\n    static removeElement(element) {\n        if (!('remove' in Element.prototype))\n            element.parentNode.removeChild(element);\n        else\n            element.remove();\n    }\n    static isElement(obj) {\n        return typeof HTMLElement === 'object' ? obj instanceof HTMLElement : obj && typeof obj === 'object' && obj !== null && obj.nodeType === 1 && typeof obj.nodeName === 'string';\n    }\n    static calculateScrollbarWidth(el) {\n        if (el) {\n            let style = getComputedStyle(el);\n            return el.offsetWidth - el.clientWidth - parseFloat(style.borderLeftWidth) - parseFloat(style.borderRightWidth);\n        }\n        else {\n            if (this.calculatedScrollbarWidth !== null)\n                return this.calculatedScrollbarWidth;\n            let scrollDiv = document.createElement('div');\n            scrollDiv.className = 'p-scrollbar-measure';\n            document.body.appendChild(scrollDiv);\n            let scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n            document.body.removeChild(scrollDiv);\n            this.calculatedScrollbarWidth = scrollbarWidth;\n            return scrollbarWidth;\n        }\n    }\n    static calculateScrollbarHeight() {\n        if (this.calculatedScrollbarHeight !== null)\n            return this.calculatedScrollbarHeight;\n        let scrollDiv = document.createElement('div');\n        scrollDiv.className = 'p-scrollbar-measure';\n        document.body.appendChild(scrollDiv);\n        let scrollbarHeight = scrollDiv.offsetHeight - scrollDiv.clientHeight;\n        document.body.removeChild(scrollDiv);\n        this.calculatedScrollbarWidth = scrollbarHeight;\n        return scrollbarHeight;\n    }\n    static invokeElementMethod(element, methodName, args) {\n        element[methodName].apply(element, args);\n    }\n    static clearSelection() {\n        if (window.getSelection) {\n            if (window.getSelection().empty) {\n                window.getSelection().empty();\n            }\n            else if (window.getSelection().removeAllRanges && window.getSelection().rangeCount > 0 && window.getSelection().getRangeAt(0).getClientRects().length > 0) {\n                window.getSelection().removeAllRanges();\n            }\n        }\n        else if (document['selection'] && document['selection'].empty) {\n            try {\n                document['selection'].empty();\n            }\n            catch (error) {\n                //ignore IE bug\n            }\n        }\n    }\n    static getBrowser() {\n        if (!this.browser) {\n            let matched = this.resolveUserAgent();\n            this.browser = {};\n            if (matched.browser) {\n                this.browser[matched.browser] = true;\n                this.browser['version'] = matched.version;\n            }\n            if (this.browser['chrome']) {\n                this.browser['webkit'] = true;\n            }\n            else if (this.browser['webkit']) {\n                this.browser['safari'] = true;\n            }\n        }\n        return this.browser;\n    }\n    static resolveUserAgent() {\n        let ua = navigator.userAgent.toLowerCase();\n        let match = /(chrome)[ \\/]([\\w.]+)/.exec(ua) || /(webkit)[ \\/]([\\w.]+)/.exec(ua) || /(opera)(?:.*version|)[ \\/]([\\w.]+)/.exec(ua) || /(msie) ([\\w.]+)/.exec(ua) || (ua.indexOf('compatible') < 0 && /(mozilla)(?:.*? rv:([\\w.]+)|)/.exec(ua)) || [];\n        return {\n            browser: match[1] || '',\n            version: match[2] || '0'\n        };\n    }\n    static isInteger(value) {\n        if (Number.isInteger) {\n            return Number.isInteger(value);\n        }\n        else {\n            return typeof value === 'number' && isFinite(value) && Math.floor(value) === value;\n        }\n    }\n    static isHidden(element) {\n        return !element || element.offsetParent === null;\n    }\n    static isVisible(element) {\n        return element && element.offsetParent != null;\n    }\n    static isExist(element) {\n        return element !== null && typeof element !== 'undefined' && element.nodeName && element.parentNode;\n    }\n    static focus(element, options) {\n        element && document.activeElement !== element && element.focus(options);\n    }\n    static getFocusableSelectorString(selector = '') {\n        return `button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        [href][clientHeight][clientWidth]:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        input:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        select:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        textarea:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        [tabIndex]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        [contenteditable]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        .p-inputtext:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        .p-button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector}`;\n    }\n    static getFocusableElements(element, selector = '') {\n        let focusableElements = this.find(element, this.getFocusableSelectorString(selector));\n        let visibleFocusableElements = [];\n        for (let focusableElement of focusableElements) {\n            const computedStyle = getComputedStyle(focusableElement);\n            if (this.isVisible(focusableElement) && computedStyle.display != 'none' && computedStyle.visibility != 'hidden')\n                visibleFocusableElements.push(focusableElement);\n        }\n        return visibleFocusableElements;\n    }\n    static getFocusableElement(element, selector = '') {\n        let focusableElement = this.findSingle(element, this.getFocusableSelectorString(selector));\n        if (focusableElement) {\n            const computedStyle = getComputedStyle(focusableElement);\n            if (this.isVisible(focusableElement) && computedStyle.display != 'none' && computedStyle.visibility != 'hidden')\n                return focusableElement;\n        }\n        return null;\n    }\n    static getFirstFocusableElement(element, selector = '') {\n        const focusableElements = this.getFocusableElements(element, selector);\n        return focusableElements.length > 0 ? focusableElements[0] : null;\n    }\n    static getLastFocusableElement(element, selector) {\n        const focusableElements = this.getFocusableElements(element, selector);\n        return focusableElements.length > 0 ? focusableElements[focusableElements.length - 1] : null;\n    }\n    static getNextFocusableElement(element, reverse = false) {\n        const focusableElements = DomHandler.getFocusableElements(element);\n        let index = 0;\n        if (focusableElements && focusableElements.length > 0) {\n            const focusedIndex = focusableElements.indexOf(focusableElements[0].ownerDocument.activeElement);\n            if (reverse) {\n                if (focusedIndex == -1 || focusedIndex === 0) {\n                    index = focusableElements.length - 1;\n                }\n                else {\n                    index = focusedIndex - 1;\n                }\n            }\n            else if (focusedIndex != -1 && focusedIndex !== focusableElements.length - 1) {\n                index = focusedIndex + 1;\n            }\n        }\n        return focusableElements[index];\n    }\n    static generateZIndex() {\n        this.zindex = this.zindex || 999;\n        return ++this.zindex;\n    }\n    static getSelection() {\n        if (window.getSelection)\n            return window.getSelection().toString();\n        else if (document.getSelection)\n            return document.getSelection().toString();\n        else if (document['selection'])\n            return document['selection'].createRange().text;\n        return null;\n    }\n    static getTargetElement(target, el) {\n        if (!target)\n            return null;\n        switch (target) {\n            case 'document':\n                return document;\n            case 'window':\n                return window;\n            case '@next':\n                return el?.nextElementSibling;\n            case '@prev':\n                return el?.previousElementSibling;\n            case '@parent':\n                return el?.parentElement;\n            case '@grandparent':\n                return el?.parentElement.parentElement;\n            default:\n                const type = typeof target;\n                if (type === 'string') {\n                    return document.querySelector(target);\n                }\n                else if (type === 'object' && target.hasOwnProperty('nativeElement')) {\n                    return this.isExist(target.nativeElement) ? target.nativeElement : undefined;\n                }\n                const isFunction = (obj) => !!(obj && obj.constructor && obj.call && obj.apply);\n                const element = isFunction(target) ? target() : target;\n                return (element && element.nodeType === 9) || this.isExist(element) ? element : null;\n        }\n    }\n    static isClient() {\n        return !!(typeof window !== 'undefined' && window.document && window.document.createElement);\n    }\n    static getAttribute(element, name) {\n        if (element) {\n            const value = element.getAttribute(name);\n            if (!isNaN(value)) {\n                return +value;\n            }\n            if (value === 'true' || value === 'false') {\n                return value === 'true';\n            }\n            return value;\n        }\n        return undefined;\n    }\n    static calculateBodyScrollbarWidth() {\n        return window.innerWidth - document.documentElement.offsetWidth;\n    }\n    static blockBodyScroll(className = 'p-overflow-hidden') {\n        document.body.style.setProperty('--scrollbar-width', this.calculateBodyScrollbarWidth() + 'px');\n        this.addClass(document.body, className);\n    }\n    static unblockBodyScroll(className = 'p-overflow-hidden') {\n        document.body.style.removeProperty('--scrollbar-width');\n        this.removeClass(document.body, className);\n    }\n    static createElement(type, attributes = {}, ...children) {\n        if (type) {\n            const element = document.createElement(type);\n            this.setAttributes(element, attributes);\n            element.append(...children);\n            return element;\n        }\n        return undefined;\n    }\n    static setAttribute(element, attribute = '', value) {\n        if (this.isElement(element) && value !== null && value !== undefined) {\n            element.setAttribute(attribute, value);\n        }\n    }\n    static setAttributes(element, attributes = {}) {\n        if (this.isElement(element)) {\n            const computedStyles = (rule, value) => {\n                const styles = element?.$attrs?.[rule] ? [element?.$attrs?.[rule]] : [];\n                return [value].flat().reduce((cv, v) => {\n                    if (v !== null && v !== undefined) {\n                        const type = typeof v;\n                        if (type === 'string' || type === 'number') {\n                            cv.push(v);\n                        }\n                        else if (type === 'object') {\n                            const _cv = Array.isArray(v)\n                                ? computedStyles(rule, v)\n                                : Object.entries(v).map(([_k, _v]) => (rule === 'style' && (!!_v || _v === 0) ? `${_k.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase()}:${_v}` : !!_v ? _k : undefined));\n                            cv = _cv.length ? cv.concat(_cv.filter((c) => !!c)) : cv;\n                        }\n                    }\n                    return cv;\n                }, styles);\n            };\n            Object.entries(attributes).forEach(([key, value]) => {\n                if (value !== undefined && value !== null) {\n                    const matchedEvent = key.match(/^on(.+)/);\n                    if (matchedEvent) {\n                        element.addEventListener(matchedEvent[1].toLowerCase(), value);\n                    }\n                    else if (key === 'pBind') {\n                        this.setAttributes(element, value);\n                    }\n                    else {\n                        value = key === 'class' ? [...new Set(computedStyles('class', value))].join(' ').trim() : key === 'style' ? computedStyles('style', value).join(';').trim() : value;\n                        (element.$attrs = element.$attrs || {}) && (element.$attrs[key] = value);\n                        element.setAttribute(key, value);\n                    }\n                }\n            });\n        }\n    }\n    static isFocusableElement(element, selector = '') {\n        return this.isElement(element)\n            ? element.matches(`button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                [href][clientHeight][clientWidth]:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                input:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                select:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                textarea:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                [tabIndex]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                [contenteditable]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector}`)\n            : false;\n    }\n}\n\nclass ConnectedOverlayScrollHandler {\n    element;\n    listener;\n    scrollableParents;\n    constructor(element, listener = () => { }) {\n        this.element = element;\n        this.listener = listener;\n    }\n    bindScrollListener() {\n        this.scrollableParents = DomHandler.getScrollableParents(this.element);\n        for (let i = 0; i < this.scrollableParents.length; i++) {\n            this.scrollableParents[i].addEventListener('scroll', this.listener);\n        }\n    }\n    unbindScrollListener() {\n        if (this.scrollableParents) {\n            for (let i = 0; i < this.scrollableParents.length; i++) {\n                this.scrollableParents[i].removeEventListener('scroll', this.listener);\n            }\n        }\n    }\n    destroy() {\n        this.unbindScrollListener();\n        this.element = null;\n        this.listener = null;\n        this.scrollableParents = null;\n    }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ConnectedOverlayScrollHandler, DomHandler };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA,IACMA,UAAU;EAAhB,MAAMA,UAAU,CAAC;IACb,OAAOC,MAAM,GAAG,IAAI;IACpB,OAAOC,wBAAwB,GAAG,IAAI;IACtC,OAAOC,yBAAyB,GAAG,IAAI;IACvC,OAAOC,OAAO;IACd,OAAOC,QAAQA,CAACC,OAAO,EAAEC,SAAS,EAAE;MAChC,IAAID,OAAO,IAAIC,SAAS,EAAE;QACtB,IAAID,OAAO,CAACE,SAAS,EACjBF,OAAO,CAACE,SAAS,CAACC,GAAG,CAACF,SAAS,CAAC,CAAC,KAEjCD,OAAO,CAACC,SAAS,IAAI,GAAG,GAAGA,SAAS;MAC5C;IACJ;IACA,OAAOG,kBAAkBA,CAACJ,OAAO,EAAEC,SAAS,EAAE;MAC1C,IAAID,OAAO,IAAIC,SAAS,EAAE;QACtB,IAAID,OAAO,CAACE,SAAS,EAAE;UACnB,IAAIG,MAAM,GAAGJ,SAAS,CAACK,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;UACxC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;YACpCR,OAAO,CAACE,SAAS,CAACC,GAAG,CAACE,MAAM,CAACG,CAAC,CAAC,CAAC;UACpC;QACJ,CAAC,MACI;UACD,IAAIH,MAAM,GAAGJ,SAAS,CAACM,KAAK,CAAC,GAAG,CAAC;UACjC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;YACpCR,OAAO,CAACC,SAAS,IAAI,GAAG,GAAGI,MAAM,CAACG,CAAC,CAAC;UACxC;QACJ;MACJ;IACJ;IACA,OAAOE,WAAWA,CAACV,OAAO,EAAEC,SAAS,EAAE;MACnC,IAAID,OAAO,IAAIC,SAAS,EAAE;QACtB,IAAID,OAAO,CAACE,SAAS,EACjBF,OAAO,CAACE,SAAS,CAACS,MAAM,CAACV,SAAS,CAAC,CAAC,KAEpCD,OAAO,CAACC,SAAS,GAAGD,OAAO,CAACC,SAAS,CAACW,OAAO,CAAC,IAAIC,MAAM,CAAC,SAAS,GAAGZ,SAAS,CAACM,KAAK,CAAC,GAAG,CAAC,CAACO,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,EAAE,IAAI,CAAC,EAAE,GAAG,CAAC;MACpI;IACJ;IACA,OAAOC,qBAAqBA,CAACf,OAAO,EAAEgB,UAAU,EAAE;MAC9C,IAAIhB,OAAO,IAAIgB,UAAU,EAAE;QACvB,CAACA,UAAU,CAAC,CACPC,IAAI,CAAC,CAAC,CACNC,MAAM,CAACC,OAAO,CAAC,CACfC,OAAO,CAAEC,MAAM,IAAKA,MAAM,CAACd,KAAK,CAAC,GAAG,CAAC,CAACa,OAAO,CAAEnB,SAAS,IAAK,IAAI,CAACS,WAAW,CAACV,OAAO,EAAEC,SAAS,CAAC,CAAC,CAAC;MAC5G;IACJ;IACA,OAAOqB,QAAQA,CAACtB,OAAO,EAAEC,SAAS,EAAE;MAChC,IAAID,OAAO,IAAIC,SAAS,EAAE;QACtB,IAAID,OAAO,CAACE,SAAS,EACjB,OAAOF,OAAO,CAACE,SAAS,CAACqB,QAAQ,CAACtB,SAAS,CAAC,CAAC,KAE7C,OAAO,IAAIY,MAAM,CAAC,OAAO,GAAGZ,SAAS,GAAG,OAAO,EAAE,IAAI,CAAC,CAACuB,IAAI,CAACxB,OAAO,CAACC,SAAS,CAAC;MACtF;MACA,OAAO,KAAK;IAChB;IACA,OAAOwB,QAAQA,CAACzB,OAAO,EAAE;MACrB,OAAO0B,KAAK,CAACC,SAAS,CAACT,MAAM,CAACU,IAAI,CAAC5B,OAAO,CAAC6B,UAAU,CAACC,QAAQ,EAAE,UAAUC,KAAK,EAAE;QAC7E,OAAOA,KAAK,KAAK/B,OAAO;MAC5B,CAAC,CAAC;IACN;IACA,OAAOgC,IAAIA,CAAChC,OAAO,EAAEiC,QAAQ,EAAE;MAC3B,OAAOP,KAAK,CAACQ,IAAI,CAAClC,OAAO,CAACmC,gBAAgB,CAACF,QAAQ,CAAC,CAAC;IACzD;IACA,OAAOG,UAAUA,CAACpC,OAAO,EAAEiC,QAAQ,EAAE;MACjC,OAAO,IAAI,CAACI,SAAS,CAACrC,OAAO,CAAC,GAAGA,OAAO,CAACsC,aAAa,CAACL,QAAQ,CAAC,GAAG,IAAI;IAC3E;IACA,OAAOM,KAAKA,CAACvC,OAAO,EAAE;MAClB,IAAI8B,QAAQ,GAAG9B,OAAO,CAAC6B,UAAU,CAACW,UAAU;MAC5C,IAAIC,GAAG,GAAG,CAAC;MACX,KAAK,IAAIjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,QAAQ,CAACrB,MAAM,EAAED,CAAC,EAAE,EAAE;QACtC,IAAIsB,QAAQ,CAACtB,CAAC,CAAC,IAAIR,OAAO,EACtB,OAAOyC,GAAG;QACd,IAAIX,QAAQ,CAACtB,CAAC,CAAC,CAACkC,QAAQ,IAAI,CAAC,EACzBD,GAAG,EAAE;MACb;MACA,OAAO,CAAC,CAAC;IACb;IACA,OAAOE,gBAAgBA,CAAC3C,OAAO,EAAE4C,aAAa,EAAE;MAC5C,IAAId,QAAQ,GAAG9B,OAAO,CAAC6B,UAAU,GAAG7B,OAAO,CAAC6B,UAAU,CAACW,UAAU,GAAG,EAAE;MACtE,IAAIC,GAAG,GAAG,CAAC;MACX,KAAK,IAAIjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,QAAQ,CAACrB,MAAM,EAAED,CAAC,EAAE,EAAE;QACtC,IAAIsB,QAAQ,CAACtB,CAAC,CAAC,IAAIR,OAAO,EACtB,OAAOyC,GAAG;QACd,IAAIX,QAAQ,CAACtB,CAAC,CAAC,CAACqC,UAAU,IAAIf,QAAQ,CAACtB,CAAC,CAAC,CAACqC,UAAU,CAACD,aAAa,CAAC,IAAId,QAAQ,CAACtB,CAAC,CAAC,CAACkC,QAAQ,IAAI,CAAC,EAC5FD,GAAG,EAAE;MACb;MACA,OAAO,CAAC,CAAC;IACb;IACA,OAAOK,aAAaA,CAACC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,GAAG,MAAM,EAAE;MACrD,IAAIA,QAAQ,KAAK,MAAM,IAAIF,OAAO,IAAIC,MAAM,EAAE;QAC1C,IAAI,CAACE,WAAW,CAACH,OAAO,EAAEC,MAAM,CAAC;MACrC;IACJ;IACA,OAAOG,YAAYA,CAACJ,OAAO,EAAEC,MAAM,EAAEC,QAAQ,GAAG,MAAM,EAAEG,iBAAiB,GAAG,IAAI,EAAE;MAC9E,IAAIL,OAAO,IAAIC,MAAM,EAAE;QACnB,IAAII,iBAAiB,EAAE;UACnBL,OAAO,CAACM,KAAK,CAACC,QAAQ,GAAI,GAAE5D,UAAU,CAAC6D,aAAa,CAACP,MAAM,CAAE,IAAG;QACpE;QACA,IAAIC,QAAQ,KAAK,MAAM,EAAE;UACrB,IAAI,CAACO,gBAAgB,CAACT,OAAO,EAAEC,MAAM,CAAC;QAC1C,CAAC,MACI;UACD,IAAI,CAACS,gBAAgB,CAACV,OAAO,EAAEC,MAAM,CAAC;QAC1C;MACJ;IACJ;IACA,OAAOQ,gBAAgBA,CAACxD,OAAO,EAAEgD,MAAM,EAAEU,MAAM,GAAG,IAAI,EAAE;MACpD,MAAMC,yBAAyB,GAAIC,EAAE,IAAK;QACtC,IAAI,CAACA,EAAE,EACH;QACJ,OAAOC,gBAAgB,CAACD,EAAE,CAAC,CAACE,gBAAgB,CAAC,UAAU,CAAC,KAAK,UAAU,GAAGF,EAAE,GAAGD,yBAAyB,CAACC,EAAE,CAACG,aAAa,CAAC;MAC9H,CAAC;MACD,MAAMC,iBAAiB,GAAGhE,OAAO,CAACiE,YAAY,GAAG;QAAEC,KAAK,EAAElE,OAAO,CAACmE,WAAW;QAAEC,MAAM,EAAEpE,OAAO,CAACqE;MAAa,CAAC,GAAG,IAAI,CAACC,0BAA0B,CAACtE,OAAO,CAAC;MACxJ,MAAMuE,YAAY,GAAGvB,MAAM,CAACqB,YAAY,IAAIrB,MAAM,CAACwB,qBAAqB,CAAC,CAAC,CAACJ,MAAM;MACjF,MAAMK,YAAY,GAAGzB,MAAM,CAACwB,qBAAqB,CAAC,CAAC;MACnD,MAAME,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACjD,MAAMC,gBAAgB,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC;MACnD,MAAMC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;MACnC,MAAMC,eAAe,GAAGrB,yBAAyB,CAAC3D,OAAO,CAAC;MAC1D,MAAMiF,qBAAqB,GAAGD,eAAe,EAAER,qBAAqB,CAAC,CAAC,IAAI;QAAEU,GAAG,EAAE,CAAC,CAAC,GAAGR,eAAe;QAAES,IAAI,EAAE,CAAC,CAAC,GAAGP;MAAiB,CAAC;MACpI,IAAIM,GAAG,EAAEC,IAAI;MACb,IAAIV,YAAY,CAACS,GAAG,GAAGX,YAAY,GAAGP,iBAAiB,CAACI,MAAM,GAAGU,QAAQ,CAACV,MAAM,EAAE;QAC9Ec,GAAG,GAAGT,YAAY,CAACS,GAAG,GAAGD,qBAAqB,CAACC,GAAG,GAAGlB,iBAAiB,CAACI,MAAM;QAC7EpE,OAAO,CAACqD,KAAK,CAAC+B,eAAe,GAAG,QAAQ;QACxC,IAAIX,YAAY,CAACS,GAAG,GAAGA,GAAG,GAAG,CAAC,EAAE;UAC5BA,GAAG,GAAG,CAAC,CAAC,GAAGT,YAAY,CAACS,GAAG;QAC/B;MACJ,CAAC,MACI;QACDA,GAAG,GAAGX,YAAY,GAAGE,YAAY,CAACS,GAAG,GAAGD,qBAAqB,CAACC,GAAG;QACjElF,OAAO,CAACqD,KAAK,CAAC+B,eAAe,GAAG,KAAK;MACzC;MACA,MAAMC,kBAAkB,GAAGZ,YAAY,CAACU,IAAI,GAAGnB,iBAAiB,CAACE,KAAK,GAAGY,QAAQ,CAACZ,KAAK;MACvF,MAAMoB,wCAAwC,GAAGb,YAAY,CAACU,IAAI,GAAGF,qBAAqB,CAACE,IAAI;MAC/F,IAAInB,iBAAiB,CAACE,KAAK,GAAGY,QAAQ,CAACZ,KAAK,EAAE;QAC1C;QACAiB,IAAI,GAAG,CAACV,YAAY,CAACU,IAAI,GAAGF,qBAAqB,CAACE,IAAI,IAAI,CAAC,CAAC;MAChE,CAAC,MACI,IAAIE,kBAAkB,GAAG,CAAC,EAAE;QAC7B;QACAF,IAAI,GAAGG,wCAAwC,GAAGD,kBAAkB;MACxE,CAAC,MACI;QACD;QACAF,IAAI,GAAGV,YAAY,CAACU,IAAI,GAAGF,qBAAqB,CAACE,IAAI;MACzD;MACAnF,OAAO,CAACqD,KAAK,CAAC6B,GAAG,GAAGA,GAAG,GAAG,IAAI;MAC9BlF,OAAO,CAACqD,KAAK,CAAC8B,IAAI,GAAGA,IAAI,GAAG,IAAI;MAChCzB,MAAM,KAAK1D,OAAO,CAACqD,KAAK,CAACkC,SAAS,GAAGC,MAAM,KAAK,QAAQ,GAAG,mCAAmC,GAAG,8BAA8B,CAAC;IACpI;IACA,OAAO/B,gBAAgBA,CAACzD,OAAO,EAAEgD,MAAM,EAAEU,MAAM,GAAG,IAAI,EAAE;MACpD,MAAMM,iBAAiB,GAAGhE,OAAO,CAACiE,YAAY,GAAG;QAAEC,KAAK,EAAElE,OAAO,CAACmE,WAAW;QAAEC,MAAM,EAAEpE,OAAO,CAACqE;MAAa,CAAC,GAAG,IAAI,CAACC,0BAA0B,CAACtE,OAAO,CAAC;MACxJ,MAAMyF,kBAAkB,GAAGzB,iBAAiB,CAACI,MAAM;MACnD,MAAMsB,iBAAiB,GAAG1B,iBAAiB,CAACE,KAAK;MACjD,MAAMyB,iBAAiB,GAAG3C,MAAM,CAACqB,YAAY,IAAIrB,MAAM,CAACwB,qBAAqB,CAAC,CAAC,CAACJ,MAAM;MACtF,MAAMwB,gBAAgB,GAAG5C,MAAM,CAACmB,WAAW,IAAInB,MAAM,CAACwB,qBAAqB,CAAC,CAAC,CAACN,KAAK;MACnF,MAAMO,YAAY,GAAGzB,MAAM,CAACwB,qBAAqB,CAAC,CAAC;MACnD,MAAME,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACjD,MAAMC,gBAAgB,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC;MACnD,MAAMC,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAC,CAAC;MACnC,IAAIG,GAAG,EAAEC,IAAI;MACb,IAAIV,YAAY,CAACS,GAAG,GAAGS,iBAAiB,GAAGF,kBAAkB,GAAGX,QAAQ,CAACV,MAAM,EAAE;QAC7Ec,GAAG,GAAGT,YAAY,CAACS,GAAG,GAAGR,eAAe,GAAGe,kBAAkB;QAC7DzF,OAAO,CAACqD,KAAK,CAAC+B,eAAe,GAAG,QAAQ;QACxC,IAAIF,GAAG,GAAG,CAAC,EAAE;UACTA,GAAG,GAAGR,eAAe;QACzB;MACJ,CAAC,MACI;QACDQ,GAAG,GAAGS,iBAAiB,GAAGlB,YAAY,CAACS,GAAG,GAAGR,eAAe;QAC5D1E,OAAO,CAACqD,KAAK,CAAC+B,eAAe,GAAG,KAAK;MACzC;MACA,IAAIX,YAAY,CAACU,IAAI,GAAGO,iBAAiB,GAAGZ,QAAQ,CAACZ,KAAK,EACtDiB,IAAI,GAAGU,IAAI,CAACC,GAAG,CAAC,CAAC,EAAErB,YAAY,CAACU,IAAI,GAAGP,gBAAgB,GAAGgB,gBAAgB,GAAGF,iBAAiB,CAAC,CAAC,KAEhGP,IAAI,GAAGV,YAAY,CAACU,IAAI,GAAGP,gBAAgB;MAC/C5E,OAAO,CAACqD,KAAK,CAAC6B,GAAG,GAAGA,GAAG,GAAG,IAAI;MAC9BlF,OAAO,CAACqD,KAAK,CAAC8B,IAAI,GAAGA,IAAI,GAAG,IAAI;MAChCzB,MAAM,KAAK1D,OAAO,CAACqD,KAAK,CAACkC,SAAS,GAAGC,MAAM,KAAK,QAAQ,GAAG,mCAAmC,GAAG,8BAA8B,CAAC;IACpI;IACA,OAAOO,UAAUA,CAAC/F,OAAO,EAAEgG,OAAO,GAAG,EAAE,EAAE;MACrC,OAAOhG,OAAO,CAAC,YAAY,CAAC,KAAK,IAAI,GAAGgG,OAAO,GAAG,IAAI,CAACD,UAAU,CAAC/F,OAAO,CAAC6B,UAAU,EAAEmE,OAAO,CAACC,MAAM,CAAC,CAACjG,OAAO,CAAC6B,UAAU,CAAC,CAAC,CAAC;IAC/H;IACA,OAAOqE,oBAAoBA,CAAClG,OAAO,EAAE;MACjC,IAAImG,iBAAiB,GAAG,EAAE;MAC1B,IAAInG,OAAO,EAAE;QACT,IAAIgG,OAAO,GAAG,IAAI,CAACD,UAAU,CAAC/F,OAAO,CAAC;QACtC,MAAMoG,aAAa,GAAG,eAAe;QACrC,MAAMC,aAAa,GAAIC,IAAI,IAAK;UAC5B,IAAIC,gBAAgB,GAAGC,MAAM,CAAC,kBAAkB,CAAC,CAACF,IAAI,EAAE,IAAI,CAAC;UAC7D,OAAOF,aAAa,CAAC5E,IAAI,CAAC+E,gBAAgB,CAACzC,gBAAgB,CAAC,UAAU,CAAC,CAAC,IAAIsC,aAAa,CAAC5E,IAAI,CAAC+E,gBAAgB,CAACzC,gBAAgB,CAAC,WAAW,CAAC,CAAC,IAAIsC,aAAa,CAAC5E,IAAI,CAAC+E,gBAAgB,CAACzC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QACxN,CAAC;QACD,KAAK,IAAI2C,MAAM,IAAIT,OAAO,EAAE;UACxB,IAAIU,eAAe,GAAGD,MAAM,CAAC/D,QAAQ,KAAK,CAAC,IAAI+D,MAAM,CAACE,OAAO,CAAC,iBAAiB,CAAC;UAChF,IAAID,eAAe,EAAE;YACjB,IAAIE,SAAS,GAAGF,eAAe,CAACnG,KAAK,CAAC,GAAG,CAAC;YAC1C,KAAK,IAAI0B,QAAQ,IAAI2E,SAAS,EAAE;cAC5B,IAAIhD,EAAE,GAAG,IAAI,CAACxB,UAAU,CAACqE,MAAM,EAAExE,QAAQ,CAAC;cAC1C,IAAI2B,EAAE,IAAIyC,aAAa,CAACzC,EAAE,CAAC,EAAE;gBACzBuC,iBAAiB,CAACU,IAAI,CAACjD,EAAE,CAAC;cAC9B;YACJ;UACJ;UACA,IAAI6C,MAAM,CAAC/D,QAAQ,KAAK,CAAC,IAAI2D,aAAa,CAACI,MAAM,CAAC,EAAE;YAChDN,iBAAiB,CAACU,IAAI,CAACJ,MAAM,CAAC;UAClC;QACJ;MACJ;MACA,OAAON,iBAAiB;IAC5B;IACA,OAAOW,2BAA2BA,CAAC9G,OAAO,EAAE;MACxCA,OAAO,CAACqD,KAAK,CAAC0D,UAAU,GAAG,QAAQ;MACnC/G,OAAO,CAACqD,KAAK,CAAC2D,OAAO,GAAG,OAAO;MAC/B,IAAIC,aAAa,GAAGjH,OAAO,CAACqE,YAAY;MACxCrE,OAAO,CAACqD,KAAK,CAAC2D,OAAO,GAAG,MAAM;MAC9BhH,OAAO,CAACqD,KAAK,CAAC0D,UAAU,GAAG,SAAS;MACpC,OAAOE,aAAa;IACxB;IACA,OAAOC,0BAA0BA,CAAClH,OAAO,EAAE;MACvCA,OAAO,CAACqD,KAAK,CAAC0D,UAAU,GAAG,QAAQ;MACnC/G,OAAO,CAACqD,KAAK,CAAC2D,OAAO,GAAG,OAAO;MAC/B,IAAIG,YAAY,GAAGnH,OAAO,CAACmE,WAAW;MACtCnE,OAAO,CAACqD,KAAK,CAAC2D,OAAO,GAAG,MAAM;MAC9BhH,OAAO,CAACqD,KAAK,CAAC0D,UAAU,GAAG,SAAS;MACpC,OAAOI,YAAY;IACvB;IACA,OAAO7C,0BAA0BA,CAACtE,OAAO,EAAE;MACvC,IAAIoH,UAAU,GAAG,CAAC,CAAC;MACnBpH,OAAO,CAACqD,KAAK,CAAC0D,UAAU,GAAG,QAAQ;MACnC/G,OAAO,CAACqD,KAAK,CAAC2D,OAAO,GAAG,OAAO;MAC/BI,UAAU,CAAClD,KAAK,GAAGlE,OAAO,CAACmE,WAAW;MACtCiD,UAAU,CAAChD,MAAM,GAAGpE,OAAO,CAACqE,YAAY;MACxCrE,OAAO,CAACqD,KAAK,CAAC2D,OAAO,GAAG,MAAM;MAC9BhH,OAAO,CAACqD,KAAK,CAAC0D,UAAU,GAAG,SAAS;MACpC,OAAOK,UAAU;IACrB;IACA,OAAOC,YAAYA,CAACC,SAAS,EAAEC,IAAI,EAAE;MACjC,IAAIC,cAAc,GAAG3D,gBAAgB,CAACyD,SAAS,CAAC,CAACxD,gBAAgB,CAAC,gBAAgB,CAAC;MACnF,IAAI2D,SAAS,GAAGD,cAAc,GAAGE,UAAU,CAACF,cAAc,CAAC,GAAG,CAAC;MAC/D,IAAIG,eAAe,GAAG9D,gBAAgB,CAACyD,SAAS,CAAC,CAACxD,gBAAgB,CAAC,YAAY,CAAC;MAChF,IAAI8D,UAAU,GAAGD,eAAe,GAAGD,UAAU,CAACC,eAAe,CAAC,GAAG,CAAC;MAClE,IAAIE,aAAa,GAAGP,SAAS,CAAC9C,qBAAqB,CAAC,CAAC;MACrD,IAAIsD,QAAQ,GAAGP,IAAI,CAAC/C,qBAAqB,CAAC,CAAC;MAC3C,IAAIuD,MAAM,GAAGD,QAAQ,CAAC5C,GAAG,GAAG8C,QAAQ,CAACC,IAAI,CAACC,SAAS,IAAIL,aAAa,CAAC3C,GAAG,GAAG8C,QAAQ,CAACC,IAAI,CAACC,SAAS,CAAC,GAAGT,SAAS,GAAGG,UAAU;MAC5H,IAAIO,MAAM,GAAGb,SAAS,CAACY,SAAS;MAChC,IAAIjB,aAAa,GAAGK,SAAS,CAACc,YAAY;MAC1C,IAAIC,UAAU,GAAG,IAAI,CAACC,cAAc,CAACf,IAAI,CAAC;MAC1C,IAAIQ,MAAM,GAAG,CAAC,EAAE;QACZT,SAAS,CAACY,SAAS,GAAGC,MAAM,GAAGJ,MAAM;MACzC,CAAC,MACI,IAAIA,MAAM,GAAGM,UAAU,GAAGpB,aAAa,EAAE;QAC1CK,SAAS,CAACY,SAAS,GAAGC,MAAM,GAAGJ,MAAM,GAAGd,aAAa,GAAGoB,UAAU;MACtE;IACJ;IACA,OAAOE,MAAMA,CAACvI,OAAO,EAAEwI,QAAQ,EAAE;MAC7BxI,OAAO,CAACqD,KAAK,CAACoF,OAAO,GAAG,CAAC;MACzB,IAAIC,IAAI,GAAG,CAAC,IAAIC,IAAI,CAAC,CAAC;MACtB,IAAIF,OAAO,GAAG,CAAC;MACf,IAAIG,IAAI,GAAG,SAAAA,CAAA,EAAY;QACnBH,OAAO,GAAG,CAACzI,OAAO,CAACqD,KAAK,CAACoF,OAAO,CAAC7H,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,IAAI+H,IAAI,CAAC,CAAC,CAACE,OAAO,CAAC,CAAC,GAAGH,IAAI,IAAIF,QAAQ;QAC7FxI,OAAO,CAACqD,KAAK,CAACoF,OAAO,GAAGA,OAAO;QAC/BC,IAAI,GAAG,CAAC,IAAIC,IAAI,CAAC,CAAC;QAClB,IAAI,CAACF,OAAO,GAAG,CAAC,EAAE;UACbjC,MAAM,CAACsC,qBAAqB,IAAIA,qBAAqB,CAACF,IAAI,CAAC,IAAKG,UAAU,CAACH,IAAI,EAAE,EAAE,CAAC;QACzF;MACJ,CAAC;MACDA,IAAI,CAAC,CAAC;IACV;IACA,OAAOI,OAAOA,CAAChJ,OAAO,EAAEiJ,EAAE,EAAE;MACxB,IAAIR,OAAO,GAAG,CAAC;QAAES,QAAQ,GAAG,EAAE;QAAEV,QAAQ,GAAGS,EAAE;QAAEE,GAAG,GAAGD,QAAQ,GAAGV,QAAQ;MACxE,IAAIY,MAAM,GAAGC,WAAW,CAAC,MAAM;QAC3BZ,OAAO,GAAGA,OAAO,GAAGU,GAAG;QACvB,IAAIV,OAAO,IAAI,CAAC,EAAE;UACdA,OAAO,GAAG,CAAC;UACXa,aAAa,CAACF,MAAM,CAAC;QACzB;QACApJ,OAAO,CAACqD,KAAK,CAACoF,OAAO,GAAGA,OAAO;MACnC,CAAC,EAAES,QAAQ,CAAC;IAChB;IACA,OAAOvE,kBAAkBA,CAAA,EAAG;MACxB,IAAI4E,GAAG,GAAGvB,QAAQ,CAACwB,eAAe;MAClC,OAAO,CAAChD,MAAM,CAACiD,WAAW,IAAIF,GAAG,CAACrB,SAAS,KAAKqB,GAAG,CAACG,SAAS,IAAI,CAAC,CAAC;IACvE;IACA,OAAO7E,mBAAmBA,CAAA,EAAG;MACzB,IAAI0E,GAAG,GAAGvB,QAAQ,CAACwB,eAAe;MAClC,OAAO,CAAChD,MAAM,CAACmD,WAAW,IAAIJ,GAAG,CAACK,UAAU,KAAKL,GAAG,CAACM,UAAU,IAAI,CAAC,CAAC;IACzE;IACA,OAAOC,OAAOA,CAAC9J,OAAO,EAAEiC,QAAQ,EAAE;MAC9B,IAAI8H,CAAC,GAAGC,OAAO,CAACrI,SAAS;MACzB,IAAIsI,CAAC,GAAGF,CAAC,CAAC,SAAS,CAAC,IAChBA,CAAC,CAACG,qBAAqB,IACvBH,CAAC,CAAC,oBAAoB,CAAC,IACvBA,CAAC,CAAC,mBAAmB,CAAC,IACtB,UAAUI,CAAC,EAAE;QACT,OAAO,EAAE,CAACC,OAAO,CAACxI,IAAI,CAACoG,QAAQ,CAAC7F,gBAAgB,CAACgI,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;MACrE,CAAC;MACL,OAAOF,CAAC,CAACrI,IAAI,CAAC5B,OAAO,EAAEiC,QAAQ,CAAC;IACpC;IACA,OAAOsB,aAAaA,CAACK,EAAE,EAAEyG,MAAM,EAAE;MAC7B,IAAInG,KAAK,GAAGN,EAAE,CAACO,WAAW;MAC1B,IAAIkG,MAAM,EAAE;QACR,IAAIhH,KAAK,GAAGQ,gBAAgB,CAACD,EAAE,CAAC;QAChCM,KAAK,IAAIwD,UAAU,CAACrE,KAAK,CAACiH,UAAU,CAAC,GAAG5C,UAAU,CAACrE,KAAK,CAACkH,WAAW,CAAC;MACzE;MACA,OAAOrG,KAAK;IAChB;IACA,OAAOsG,oBAAoBA,CAAC5G,EAAE,EAAE;MAC5B,IAAIP,KAAK,GAAGQ,gBAAgB,CAACD,EAAE,CAAC;MAChC,OAAO8D,UAAU,CAACrE,KAAK,CAACoH,WAAW,CAAC,GAAG/C,UAAU,CAACrE,KAAK,CAACqH,YAAY,CAAC;IACzE;IACA,OAAOC,mBAAmBA,CAAC/G,EAAE,EAAE;MAC3B,IAAIP,KAAK,GAAGQ,gBAAgB,CAACD,EAAE,CAAC;MAChC,OAAO8D,UAAU,CAACrE,KAAK,CAACiH,UAAU,CAAC,GAAG5C,UAAU,CAACrE,KAAK,CAACkH,WAAW,CAAC;IACvE;IACA,OAAOK,UAAUA,CAAChH,EAAE,EAAE;MAClB,IAAIM,KAAK,GAAGN,EAAE,CAACO,WAAW;MAC1B,IAAId,KAAK,GAAGQ,gBAAgB,CAACD,EAAE,CAAC;MAChCM,KAAK,IAAIwD,UAAU,CAACrE,KAAK,CAACoH,WAAW,CAAC,GAAG/C,UAAU,CAACrE,KAAK,CAACqH,YAAY,CAAC;MACvE,OAAOxG,KAAK;IAChB;IACA,OAAOA,KAAKA,CAACN,EAAE,EAAE;MACb,IAAIM,KAAK,GAAGN,EAAE,CAACO,WAAW;MAC1B,IAAId,KAAK,GAAGQ,gBAAgB,CAACD,EAAE,CAAC;MAChCM,KAAK,IAAIwD,UAAU,CAACrE,KAAK,CAACoH,WAAW,CAAC,GAAG/C,UAAU,CAACrE,KAAK,CAACqH,YAAY,CAAC;MACvE,OAAOxG,KAAK;IAChB;IACA,OAAO2G,cAAcA,CAACjH,EAAE,EAAE;MACtB,IAAIQ,MAAM,GAAGR,EAAE,CAACS,YAAY;MAC5B,IAAIhB,KAAK,GAAGQ,gBAAgB,CAACD,EAAE,CAAC;MAChCQ,MAAM,IAAIsD,UAAU,CAACrE,KAAK,CAACuE,UAAU,CAAC,GAAGF,UAAU,CAACrE,KAAK,CAACyH,aAAa,CAAC;MACxE,OAAO1G,MAAM;IACjB;IACA,OAAOkE,cAAcA,CAAC1E,EAAE,EAAEyG,MAAM,EAAE;MAC9B,IAAIjG,MAAM,GAAGR,EAAE,CAACS,YAAY;MAC5B,IAAIgG,MAAM,EAAE;QACR,IAAIhH,KAAK,GAAGQ,gBAAgB,CAACD,EAAE,CAAC;QAChCQ,MAAM,IAAIsD,UAAU,CAACrE,KAAK,CAACkC,SAAS,CAAC,GAAGmC,UAAU,CAACrE,KAAK,CAAC0H,YAAY,CAAC;MAC1E;MACA,OAAO3G,MAAM;IACjB;IACA,OAAO4G,SAASA,CAACpH,EAAE,EAAE;MACjB,IAAIQ,MAAM,GAAGR,EAAE,CAACS,YAAY;MAC5B,IAAIhB,KAAK,GAAGQ,gBAAgB,CAACD,EAAE,CAAC;MAChCQ,MAAM,IAAIsD,UAAU,CAACrE,KAAK,CAACuE,UAAU,CAAC,GAAGF,UAAU,CAACrE,KAAK,CAACyH,aAAa,CAAC,GAAGpD,UAAU,CAACrE,KAAK,CAAC4H,cAAc,CAAC,GAAGvD,UAAU,CAACrE,KAAK,CAAC6H,iBAAiB,CAAC;MACjJ,OAAO9G,MAAM;IACjB;IACA,OAAO+G,QAAQA,CAACvH,EAAE,EAAE;MAChB,IAAIM,KAAK,GAAGN,EAAE,CAACO,WAAW;MAC1B,IAAId,KAAK,GAAGQ,gBAAgB,CAACD,EAAE,CAAC;MAChCM,KAAK,IAAIwD,UAAU,CAACrE,KAAK,CAACoH,WAAW,CAAC,GAAG/C,UAAU,CAACrE,KAAK,CAACqH,YAAY,CAAC,GAAGhD,UAAU,CAACrE,KAAK,CAAC+H,eAAe,CAAC,GAAG1D,UAAU,CAACrE,KAAK,CAACgI,gBAAgB,CAAC;MAChJ,OAAOnH,KAAK;IAChB;IACA,OAAOa,WAAWA,CAAA,EAAG;MACjB,IAAIuG,GAAG,GAAG9E,MAAM;QAAE+E,CAAC,GAAGvD,QAAQ;QAAEwD,CAAC,GAAGD,CAAC,CAAC/B,eAAe;QAAEiC,CAAC,GAAGF,CAAC,CAACG,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QAAEC,CAAC,GAAGL,GAAG,CAACV,UAAU,IAAIY,CAAC,CAACI,WAAW,IAAIH,CAAC,CAACG,WAAW;QAAEC,CAAC,GAAGP,GAAG,CAACQ,WAAW,IAAIN,CAAC,CAACpD,YAAY,IAAIqD,CAAC,CAACrD,YAAY;MAC3M,OAAO;QAAElE,KAAK,EAAEyH,CAAC;QAAEvH,MAAM,EAAEyH;MAAE,CAAC;IAClC;IACA,OAAOE,SAASA,CAACnI,EAAE,EAAE;MACjB,IAAIoI,IAAI,GAAGpI,EAAE,CAACY,qBAAqB,CAAC,CAAC;MACrC,OAAO;QACHU,GAAG,EAAE8G,IAAI,CAAC9G,GAAG,IAAIsB,MAAM,CAACiD,WAAW,IAAIzB,QAAQ,CAACwB,eAAe,CAACtB,SAAS,IAAIF,QAAQ,CAACC,IAAI,CAACC,SAAS,IAAI,CAAC,CAAC;QAC1G/C,IAAI,EAAE6G,IAAI,CAAC7G,IAAI,IAAIqB,MAAM,CAACmD,WAAW,IAAI3B,QAAQ,CAACwB,eAAe,CAACI,UAAU,IAAI5B,QAAQ,CAACC,IAAI,CAAC2B,UAAU,IAAI,CAAC;MACjH,CAAC;IACL;IACA,OAAOqC,kBAAkBA,CAACjM,OAAO,EAAEkM,kBAAkB,EAAE;MACnD,IAAIrK,UAAU,GAAG7B,OAAO,CAAC6B,UAAU;MACnC,IAAI,CAACA,UAAU,EACX,MAAO,uBAAsB;MACjC,OAAOA,UAAU,CAACsK,YAAY,CAACD,kBAAkB,EAAElM,OAAO,CAAC;IAC/D;IACA,OAAOoM,YAAYA,CAAA,EAAG;MAClB,IAAIC,SAAS,IAAI,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE;QAC9B,OAAOD,SAAS,CAACE,SAAS;MAC9B;IACJ;IACA,OAAOC,IAAIA,CAAA,EAAG;MACV,IAAIC,EAAE,GAAGjG,MAAM,CAAC6F,SAAS,CAACE,SAAS;MACnC,IAAIG,IAAI,GAAGD,EAAE,CAACrC,OAAO,CAAC,OAAO,CAAC;MAC9B,IAAIsC,IAAI,GAAG,CAAC,EAAE;QACV;QACA,OAAO,IAAI;MACf;MACA,IAAIC,OAAO,GAAGF,EAAE,CAACrC,OAAO,CAAC,UAAU,CAAC;MACpC,IAAIuC,OAAO,GAAG,CAAC,EAAE;QACb;QACA,IAAIC,EAAE,GAAGH,EAAE,CAACrC,OAAO,CAAC,KAAK,CAAC;QAC1B,OAAO,IAAI;MACf;MACA,IAAIyC,IAAI,GAAGJ,EAAE,CAACrC,OAAO,CAAC,OAAO,CAAC;MAC9B,IAAIyC,IAAI,GAAG,CAAC,EAAE;QACV;QACA,OAAO,IAAI;MACf;MACA;MACA,OAAO,KAAK;IAChB;IACA,OAAOC,KAAKA,CAAA,EAAG;MACX,OAAO,kBAAkB,CAACtL,IAAI,CAAC6K,SAAS,CAACE,SAAS,CAAC,IAAI,CAAC/F,MAAM,CAAC,UAAU,CAAC;IAC9E;IACA,OAAOuG,SAASA,CAAA,EAAG;MACf,OAAO,YAAY,CAACvL,IAAI,CAAC6K,SAAS,CAACE,SAAS,CAAC;IACjD;IACA,OAAOS,aAAaA,CAAA,EAAG;MACnB,OAAO,cAAc,IAAIxG,MAAM,IAAI6F,SAAS,CAACY,cAAc,GAAG,CAAC;IACnE;IACA,OAAO/J,WAAWA,CAAClD,OAAO,EAAEgD,MAAM,EAAE;MAChC,IAAI,IAAI,CAACX,SAAS,CAACW,MAAM,CAAC,EACtBA,MAAM,CAACE,WAAW,CAAClD,OAAO,CAAC,CAAC,KAC3B,IAAIgD,MAAM,IAAIA,MAAM,CAACY,EAAE,IAAIZ,MAAM,CAACY,EAAE,CAACsJ,aAAa,EACnDlK,MAAM,CAACY,EAAE,CAACsJ,aAAa,CAAChK,WAAW,CAAClD,OAAO,CAAC,CAAC,KAE7C,MAAM,gBAAgB,GAAGgD,MAAM,GAAG,MAAM,GAAGhD,OAAO;IAC1D;IACA,OAAOmN,WAAWA,CAACnN,OAAO,EAAEgD,MAAM,EAAE;MAChC,IAAI,IAAI,CAACX,SAAS,CAACW,MAAM,CAAC,EACtBA,MAAM,CAACmK,WAAW,CAACnN,OAAO,CAAC,CAAC,KAC3B,IAAIgD,MAAM,CAACY,EAAE,IAAIZ,MAAM,CAACY,EAAE,CAACsJ,aAAa,EACzClK,MAAM,CAACY,EAAE,CAACsJ,aAAa,CAACC,WAAW,CAACnN,OAAO,CAAC,CAAC,KAE7C,MAAM,gBAAgB,GAAGA,OAAO,GAAG,QAAQ,GAAGgD,MAAM;IAC5D;IACA,OAAOoK,aAAaA,CAACpN,OAAO,EAAE;MAC1B,IAAI,EAAE,QAAQ,IAAIgK,OAAO,CAACrI,SAAS,CAAC,EAChC3B,OAAO,CAAC6B,UAAU,CAACsL,WAAW,CAACnN,OAAO,CAAC,CAAC,KAExCA,OAAO,CAACW,MAAM,CAAC,CAAC;IACxB;IACA,OAAO0B,SAASA,CAACgL,GAAG,EAAE;MAClB,OAAO,OAAOC,WAAW,KAAK,QAAQ,GAAGD,GAAG,YAAYC,WAAW,GAAGD,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,IAAIA,GAAG,CAAC3K,QAAQ,KAAK,CAAC,IAAI,OAAO2K,GAAG,CAACE,QAAQ,KAAK,QAAQ;IAClL;IACA,OAAOC,uBAAuBA,CAAC5J,EAAE,EAAE;MAC/B,IAAIA,EAAE,EAAE;QACJ,IAAIP,KAAK,GAAGQ,gBAAgB,CAACD,EAAE,CAAC;QAChC,OAAOA,EAAE,CAACO,WAAW,GAAGP,EAAE,CAACgI,WAAW,GAAGlE,UAAU,CAACrE,KAAK,CAAC+H,eAAe,CAAC,GAAG1D,UAAU,CAACrE,KAAK,CAACgI,gBAAgB,CAAC;MACnH,CAAC,MACI;QACD,IAAI,IAAI,CAACzL,wBAAwB,KAAK,IAAI,EACtC,OAAO,IAAI,CAACA,wBAAwB;QACxC,IAAI6N,SAAS,GAAGzF,QAAQ,CAAC0F,aAAa,CAAC,KAAK,CAAC;QAC7CD,SAAS,CAACxN,SAAS,GAAG,qBAAqB;QAC3C+H,QAAQ,CAACC,IAAI,CAAC/E,WAAW,CAACuK,SAAS,CAAC;QACpC,IAAIE,cAAc,GAAGF,SAAS,CAACtJ,WAAW,GAAGsJ,SAAS,CAAC7B,WAAW;QAClE5D,QAAQ,CAACC,IAAI,CAACkF,WAAW,CAACM,SAAS,CAAC;QACpC,IAAI,CAAC7N,wBAAwB,GAAG+N,cAAc;QAC9C,OAAOA,cAAc;MACzB;IACJ;IACA,OAAOC,wBAAwBA,CAAA,EAAG;MAC9B,IAAI,IAAI,CAAC/N,yBAAyB,KAAK,IAAI,EACvC,OAAO,IAAI,CAACA,yBAAyB;MACzC,IAAI4N,SAAS,GAAGzF,QAAQ,CAAC0F,aAAa,CAAC,KAAK,CAAC;MAC7CD,SAAS,CAACxN,SAAS,GAAG,qBAAqB;MAC3C+H,QAAQ,CAACC,IAAI,CAAC/E,WAAW,CAACuK,SAAS,CAAC;MACpC,IAAII,eAAe,GAAGJ,SAAS,CAACpJ,YAAY,GAAGoJ,SAAS,CAACrF,YAAY;MACrEJ,QAAQ,CAACC,IAAI,CAACkF,WAAW,CAACM,SAAS,CAAC;MACpC,IAAI,CAAC7N,wBAAwB,GAAGiO,eAAe;MAC/C,OAAOA,eAAe;IAC1B;IACA,OAAOC,mBAAmBA,CAAC9N,OAAO,EAAE+N,UAAU,EAAEC,IAAI,EAAE;MAClDhO,OAAO,CAAC+N,UAAU,CAAC,CAACE,KAAK,CAACjO,OAAO,EAAEgO,IAAI,CAAC;IAC5C;IACA,OAAOE,cAAcA,CAAA,EAAG;MACpB,IAAI1H,MAAM,CAAC2H,YAAY,EAAE;QACrB,IAAI3H,MAAM,CAAC2H,YAAY,CAAC,CAAC,CAACC,KAAK,EAAE;UAC7B5H,MAAM,CAAC2H,YAAY,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;QACjC,CAAC,MACI,IAAI5H,MAAM,CAAC2H,YAAY,CAAC,CAAC,CAACE,eAAe,IAAI7H,MAAM,CAAC2H,YAAY,CAAC,CAAC,CAACG,UAAU,GAAG,CAAC,IAAI9H,MAAM,CAAC2H,YAAY,CAAC,CAAC,CAACI,UAAU,CAAC,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC,CAAC/N,MAAM,GAAG,CAAC,EAAE;UACvJ+F,MAAM,CAAC2H,YAAY,CAAC,CAAC,CAACE,eAAe,CAAC,CAAC;QAC3C;MACJ,CAAC,MACI,IAAIrG,QAAQ,CAAC,WAAW,CAAC,IAAIA,QAAQ,CAAC,WAAW,CAAC,CAACoG,KAAK,EAAE;QAC3D,IAAI;UACApG,QAAQ,CAAC,WAAW,CAAC,CAACoG,KAAK,CAAC,CAAC;QACjC,CAAC,CACD,OAAOK,KAAK,EAAE;UACV;QAAA;MAER;IACJ;IACA,OAAOC,UAAUA,CAAA,EAAG;MAChB,IAAI,CAAC,IAAI,CAAC5O,OAAO,EAAE;QACf,IAAI6O,OAAO,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;QACrC,IAAI,CAAC9O,OAAO,GAAG,CAAC,CAAC;QACjB,IAAI6O,OAAO,CAAC7O,OAAO,EAAE;UACjB,IAAI,CAACA,OAAO,CAAC6O,OAAO,CAAC7O,OAAO,CAAC,GAAG,IAAI;UACpC,IAAI,CAACA,OAAO,CAAC,SAAS,CAAC,GAAG6O,OAAO,CAACE,OAAO;QAC7C;QACA,IAAI,IAAI,CAAC/O,OAAO,CAAC,QAAQ,CAAC,EAAE;UACxB,IAAI,CAACA,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI;QACjC,CAAC,MACI,IAAI,IAAI,CAACA,OAAO,CAAC,QAAQ,CAAC,EAAE;UAC7B,IAAI,CAACA,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI;QACjC;MACJ;MACA,OAAO,IAAI,CAACA,OAAO;IACvB;IACA,OAAO8O,gBAAgBA,CAAA,EAAG;MACtB,IAAInC,EAAE,GAAGJ,SAAS,CAACE,SAAS,CAACuC,WAAW,CAAC,CAAC;MAC1C,IAAIC,KAAK,GAAG,uBAAuB,CAACC,IAAI,CAACvC,EAAE,CAAC,IAAI,uBAAuB,CAACuC,IAAI,CAACvC,EAAE,CAAC,IAAI,oCAAoC,CAACuC,IAAI,CAACvC,EAAE,CAAC,IAAI,iBAAiB,CAACuC,IAAI,CAACvC,EAAE,CAAC,IAAKA,EAAE,CAACrC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,+BAA+B,CAAC4E,IAAI,CAACvC,EAAE,CAAE,IAAI,EAAE;MACnP,OAAO;QACH3M,OAAO,EAAEiP,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;QACvBF,OAAO,EAAEE,KAAK,CAAC,CAAC,CAAC,IAAI;MACzB,CAAC;IACL;IACA,OAAOE,SAASA,CAACC,KAAK,EAAE;MACpB,IAAIC,MAAM,CAACF,SAAS,EAAE;QAClB,OAAOE,MAAM,CAACF,SAAS,CAACC,KAAK,CAAC;MAClC,CAAC,MACI;QACD,OAAO,OAAOA,KAAK,KAAK,QAAQ,IAAIE,QAAQ,CAACF,KAAK,CAAC,IAAIrJ,IAAI,CAACwJ,KAAK,CAACH,KAAK,CAAC,KAAKA,KAAK;MACtF;IACJ;IACA,OAAOI,QAAQA,CAACtP,OAAO,EAAE;MACrB,OAAO,CAACA,OAAO,IAAIA,OAAO,CAACiE,YAAY,KAAK,IAAI;IACpD;IACA,OAAOsL,SAASA,CAACvP,OAAO,EAAE;MACtB,OAAOA,OAAO,IAAIA,OAAO,CAACiE,YAAY,IAAI,IAAI;IAClD;IACA,OAAOuL,OAAOA,CAACxP,OAAO,EAAE;MACpB,OAAOA,OAAO,KAAK,IAAI,IAAI,OAAOA,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACuN,QAAQ,IAAIvN,OAAO,CAAC6B,UAAU;IACvG;IACA,OAAO4N,KAAKA,CAACzP,OAAO,EAAE0P,OAAO,EAAE;MAC3B1P,OAAO,IAAIgI,QAAQ,CAAC2H,aAAa,KAAK3P,OAAO,IAAIA,OAAO,CAACyP,KAAK,CAACC,OAAO,CAAC;IAC3E;IACA,OAAOE,0BAA0BA,CAAC3N,QAAQ,GAAG,EAAE,EAAE;MAC7C,OAAQ,2FAA0FA,QAAS;AACnH,6HAA6HA,QAAS;AACtI,iGAAiGA,QAAS;AAC1G,kGAAkGA,QAAS;AAC3G,oGAAoGA,QAAS;AAC7G,sGAAsGA,QAAS;AAC/G,6GAA6GA,QAAS;AACtH,wGAAwGA,QAAS;AACjH,qGAAqGA,QAAS,EAAC;IAC3G;IACA,OAAO4N,oBAAoBA,CAAC7P,OAAO,EAAEiC,QAAQ,GAAG,EAAE,EAAE;MAChD,IAAI6N,iBAAiB,GAAG,IAAI,CAAC9N,IAAI,CAAChC,OAAO,EAAE,IAAI,CAAC4P,0BAA0B,CAAC3N,QAAQ,CAAC,CAAC;MACrF,IAAI8N,wBAAwB,GAAG,EAAE;MACjC,KAAK,IAAIC,gBAAgB,IAAIF,iBAAiB,EAAE;QAC5C,MAAMG,aAAa,GAAGpM,gBAAgB,CAACmM,gBAAgB,CAAC;QACxD,IAAI,IAAI,CAACT,SAAS,CAACS,gBAAgB,CAAC,IAAIC,aAAa,CAACjJ,OAAO,IAAI,MAAM,IAAIiJ,aAAa,CAAClJ,UAAU,IAAI,QAAQ,EAC3GgJ,wBAAwB,CAAClJ,IAAI,CAACmJ,gBAAgB,CAAC;MACvD;MACA,OAAOD,wBAAwB;IACnC;IACA,OAAOG,mBAAmBA,CAAClQ,OAAO,EAAEiC,QAAQ,GAAG,EAAE,EAAE;MAC/C,IAAI+N,gBAAgB,GAAG,IAAI,CAAC5N,UAAU,CAACpC,OAAO,EAAE,IAAI,CAAC4P,0BAA0B,CAAC3N,QAAQ,CAAC,CAAC;MAC1F,IAAI+N,gBAAgB,EAAE;QAClB,MAAMC,aAAa,GAAGpM,gBAAgB,CAACmM,gBAAgB,CAAC;QACxD,IAAI,IAAI,CAACT,SAAS,CAACS,gBAAgB,CAAC,IAAIC,aAAa,CAACjJ,OAAO,IAAI,MAAM,IAAIiJ,aAAa,CAAClJ,UAAU,IAAI,QAAQ,EAC3G,OAAOiJ,gBAAgB;MAC/B;MACA,OAAO,IAAI;IACf;IACA,OAAOG,wBAAwBA,CAACnQ,OAAO,EAAEiC,QAAQ,GAAG,EAAE,EAAE;MACpD,MAAM6N,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAAC7P,OAAO,EAAEiC,QAAQ,CAAC;MACtE,OAAO6N,iBAAiB,CAACrP,MAAM,GAAG,CAAC,GAAGqP,iBAAiB,CAAC,CAAC,CAAC,GAAG,IAAI;IACrE;IACA,OAAOM,uBAAuBA,CAACpQ,OAAO,EAAEiC,QAAQ,EAAE;MAC9C,MAAM6N,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAAC7P,OAAO,EAAEiC,QAAQ,CAAC;MACtE,OAAO6N,iBAAiB,CAACrP,MAAM,GAAG,CAAC,GAAGqP,iBAAiB,CAACA,iBAAiB,CAACrP,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;IAChG;IACA,OAAO4P,uBAAuBA,CAACrQ,OAAO,EAAEsQ,OAAO,GAAG,KAAK,EAAE;MACrD,MAAMR,iBAAiB,GAAGpQ,UAAU,CAACmQ,oBAAoB,CAAC7P,OAAO,CAAC;MAClE,IAAIuC,KAAK,GAAG,CAAC;MACb,IAAIuN,iBAAiB,IAAIA,iBAAiB,CAACrP,MAAM,GAAG,CAAC,EAAE;QACnD,MAAM8P,YAAY,GAAGT,iBAAiB,CAAC1F,OAAO,CAAC0F,iBAAiB,CAAC,CAAC,CAAC,CAACU,aAAa,CAACb,aAAa,CAAC;QAChG,IAAIW,OAAO,EAAE;UACT,IAAIC,YAAY,IAAI,CAAC,CAAC,IAAIA,YAAY,KAAK,CAAC,EAAE;YAC1ChO,KAAK,GAAGuN,iBAAiB,CAACrP,MAAM,GAAG,CAAC;UACxC,CAAC,MACI;YACD8B,KAAK,GAAGgO,YAAY,GAAG,CAAC;UAC5B;QACJ,CAAC,MACI,IAAIA,YAAY,IAAI,CAAC,CAAC,IAAIA,YAAY,KAAKT,iBAAiB,CAACrP,MAAM,GAAG,CAAC,EAAE;UAC1E8B,KAAK,GAAGgO,YAAY,GAAG,CAAC;QAC5B;MACJ;MACA,OAAOT,iBAAiB,CAACvN,KAAK,CAAC;IACnC;IACA,OAAOkO,cAAcA,CAAA,EAAG;MACpB,IAAI,CAAC9Q,MAAM,GAAG,IAAI,CAACA,MAAM,IAAI,GAAG;MAChC,OAAO,EAAE,IAAI,CAACA,MAAM;IACxB;IACA,OAAOwO,YAAYA,CAAA,EAAG;MAClB,IAAI3H,MAAM,CAAC2H,YAAY,EACnB,OAAO3H,MAAM,CAAC2H,YAAY,CAAC,CAAC,CAACuC,QAAQ,CAAC,CAAC,CAAC,KACvC,IAAI1I,QAAQ,CAACmG,YAAY,EAC1B,OAAOnG,QAAQ,CAACmG,YAAY,CAAC,CAAC,CAACuC,QAAQ,CAAC,CAAC,CAAC,KACzC,IAAI1I,QAAQ,CAAC,WAAW,CAAC,EAC1B,OAAOA,QAAQ,CAAC,WAAW,CAAC,CAAC2I,WAAW,CAAC,CAAC,CAACC,IAAI;MACnD,OAAO,IAAI;IACf;IACA,OAAOC,gBAAgBA,CAAC7N,MAAM,EAAEY,EAAE,EAAE;MAChC,IAAI,CAACZ,MAAM,EACP,OAAO,IAAI;MACf,QAAQA,MAAM;QACV,KAAK,UAAU;UACX,OAAOgF,QAAQ;QACnB,KAAK,QAAQ;UACT,OAAOxB,MAAM;QACjB,KAAK,OAAO;UACR,OAAO5C,EAAE,EAAEkN,kBAAkB;QACjC,KAAK,OAAO;UACR,OAAOlN,EAAE,EAAEmN,sBAAsB;QACrC,KAAK,SAAS;UACV,OAAOnN,EAAE,EAAEG,aAAa;QAC5B,KAAK,cAAc;UACf,OAAOH,EAAE,EAAEG,aAAa,CAACA,aAAa;QAC1C;UACI,MAAMiN,IAAI,GAAG,OAAOhO,MAAM;UAC1B,IAAIgO,IAAI,KAAK,QAAQ,EAAE;YACnB,OAAOhJ,QAAQ,CAAC1F,aAAa,CAACU,MAAM,CAAC;UACzC,CAAC,MACI,IAAIgO,IAAI,KAAK,QAAQ,IAAIhO,MAAM,CAACiO,cAAc,CAAC,eAAe,CAAC,EAAE;YAClE,OAAO,IAAI,CAACzB,OAAO,CAACxM,MAAM,CAACkK,aAAa,CAAC,GAAGlK,MAAM,CAACkK,aAAa,GAAGgE,SAAS;UAChF;UACA,MAAMC,UAAU,GAAI9D,GAAG,IAAK,CAAC,EAAEA,GAAG,IAAIA,GAAG,CAAC+D,WAAW,IAAI/D,GAAG,CAACzL,IAAI,IAAIyL,GAAG,CAACY,KAAK,CAAC;UAC/E,MAAMjO,OAAO,GAAGmR,UAAU,CAACnO,MAAM,CAAC,GAAGA,MAAM,CAAC,CAAC,GAAGA,MAAM;UACtD,OAAQhD,OAAO,IAAIA,OAAO,CAAC0C,QAAQ,KAAK,CAAC,IAAK,IAAI,CAAC8M,OAAO,CAACxP,OAAO,CAAC,GAAGA,OAAO,GAAG,IAAI;MAC5F;IACJ;IACA,OAAOsM,QAAQA,CAAA,EAAG;MACd,OAAO,CAAC,EAAE,OAAO9F,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACwB,QAAQ,IAAIxB,MAAM,CAACwB,QAAQ,CAAC0F,aAAa,CAAC;IAChG;IACA,OAAO2D,YAAYA,CAACrR,OAAO,EAAEsR,IAAI,EAAE;MAC/B,IAAItR,OAAO,EAAE;QACT,MAAMkP,KAAK,GAAGlP,OAAO,CAACqR,YAAY,CAACC,IAAI,CAAC;QACxC,IAAI,CAACC,KAAK,CAACrC,KAAK,CAAC,EAAE;UACf,OAAO,CAACA,KAAK;QACjB;QACA,IAAIA,KAAK,KAAK,MAAM,IAAIA,KAAK,KAAK,OAAO,EAAE;UACvC,OAAOA,KAAK,KAAK,MAAM;QAC3B;QACA,OAAOA,KAAK;MAChB;MACA,OAAOgC,SAAS;IACpB;IACA,OAAOM,2BAA2BA,CAAA,EAAG;MACjC,OAAOhL,MAAM,CAACoE,UAAU,GAAG5C,QAAQ,CAACwB,eAAe,CAACrF,WAAW;IACnE;IACA,OAAOsN,eAAeA,CAACxR,SAAS,GAAG,mBAAmB,EAAE;MACpD+H,QAAQ,CAACC,IAAI,CAAC5E,KAAK,CAACqO,WAAW,CAAC,mBAAmB,EAAE,IAAI,CAACF,2BAA2B,CAAC,CAAC,GAAG,IAAI,CAAC;MAC/F,IAAI,CAACzR,QAAQ,CAACiI,QAAQ,CAACC,IAAI,EAAEhI,SAAS,CAAC;IAC3C;IACA,OAAO0R,iBAAiBA,CAAC1R,SAAS,GAAG,mBAAmB,EAAE;MACtD+H,QAAQ,CAACC,IAAI,CAAC5E,KAAK,CAACuO,cAAc,CAAC,mBAAmB,CAAC;MACvD,IAAI,CAAClR,WAAW,CAACsH,QAAQ,CAACC,IAAI,EAAEhI,SAAS,CAAC;IAC9C;IACA,OAAOyN,aAAaA,CAACsD,IAAI,EAAEnO,UAAU,GAAG,CAAC,CAAC,EAAE,GAAGf,QAAQ,EAAE;MACrD,IAAIkP,IAAI,EAAE;QACN,MAAMhR,OAAO,GAAGgI,QAAQ,CAAC0F,aAAa,CAACsD,IAAI,CAAC;QAC5C,IAAI,CAACa,aAAa,CAAC7R,OAAO,EAAE6C,UAAU,CAAC;QACvC7C,OAAO,CAAC8R,MAAM,CAAC,GAAGhQ,QAAQ,CAAC;QAC3B,OAAO9B,OAAO;MAClB;MACA,OAAOkR,SAAS;IACpB;IACA,OAAOa,YAAYA,CAAC/R,OAAO,EAAEgS,SAAS,GAAG,EAAE,EAAE9C,KAAK,EAAE;MAChD,IAAI,IAAI,CAAC7M,SAAS,CAACrC,OAAO,CAAC,IAAIkP,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKgC,SAAS,EAAE;QAClElR,OAAO,CAAC+R,YAAY,CAACC,SAAS,EAAE9C,KAAK,CAAC;MAC1C;IACJ;IACA,OAAO2C,aAAaA,CAAC7R,OAAO,EAAE6C,UAAU,GAAG,CAAC,CAAC,EAAE;MAC3C,IAAI,IAAI,CAACR,SAAS,CAACrC,OAAO,CAAC,EAAE;QACzB,MAAMiS,cAAc,GAAGA,CAACC,IAAI,EAAEhD,KAAK,KAAK;UACpC,MAAM7O,MAAM,GAAGL,OAAO,EAAEmS,MAAM,GAAGD,IAAI,CAAC,GAAG,CAAClS,OAAO,EAAEmS,MAAM,GAAGD,IAAI,CAAC,CAAC,GAAG,EAAE;UACvE,OAAO,CAAChD,KAAK,CAAC,CAACjO,IAAI,CAAC,CAAC,CAACmR,MAAM,CAAC,CAACC,EAAE,EAAEC,CAAC,KAAK;YACpC,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAKpB,SAAS,EAAE;cAC/B,MAAMF,IAAI,GAAG,OAAOsB,CAAC;cACrB,IAAItB,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,QAAQ,EAAE;gBACxCqB,EAAE,CAACxL,IAAI,CAACyL,CAAC,CAAC;cACd,CAAC,MACI,IAAItB,IAAI,KAAK,QAAQ,EAAE;gBACxB,MAAMuB,GAAG,GAAG7Q,KAAK,CAAC8Q,OAAO,CAACF,CAAC,CAAC,GACtBL,cAAc,CAACC,IAAI,EAAEI,CAAC,CAAC,GACvBG,MAAM,CAACC,OAAO,CAACJ,CAAC,CAAC,CAACK,GAAG,CAAC,CAAC,CAACC,EAAE,EAAEC,EAAE,CAAC,KAAMX,IAAI,KAAK,OAAO,KAAK,CAAC,CAACW,EAAE,IAAIA,EAAE,KAAK,CAAC,CAAC,GAAI,GAAED,EAAE,CAAChS,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAACkO,WAAW,CAAC,CAAE,IAAG+D,EAAG,EAAC,GAAG,CAAC,CAACA,EAAE,GAAGD,EAAE,GAAG1B,SAAU,CAAC;gBAC7KmB,EAAE,GAAGE,GAAG,CAAC9R,MAAM,GAAG4R,EAAE,CAACpM,MAAM,CAACsM,GAAG,CAACrR,MAAM,CAAE4R,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC,CAAC,GAAGT,EAAE;cAC5D;YACJ;YACA,OAAOA,EAAE;UACb,CAAC,EAAEhS,MAAM,CAAC;QACd,CAAC;QACDoS,MAAM,CAACC,OAAO,CAAC7P,UAAU,CAAC,CAACzB,OAAO,CAAC,CAAC,CAAC2R,GAAG,EAAE7D,KAAK,CAAC,KAAK;UACjD,IAAIA,KAAK,KAAKgC,SAAS,IAAIhC,KAAK,KAAK,IAAI,EAAE;YACvC,MAAM8D,YAAY,GAAGD,GAAG,CAAChE,KAAK,CAAC,SAAS,CAAC;YACzC,IAAIiE,YAAY,EAAE;cACdhT,OAAO,CAACiT,gBAAgB,CAACD,YAAY,CAAC,CAAC,CAAC,CAAClE,WAAW,CAAC,CAAC,EAAEI,KAAK,CAAC;YAClE,CAAC,MACI,IAAI6D,GAAG,KAAK,OAAO,EAAE;cACtB,IAAI,CAAClB,aAAa,CAAC7R,OAAO,EAAEkP,KAAK,CAAC;YACtC,CAAC,MACI;cACDA,KAAK,GAAG6D,GAAG,KAAK,OAAO,GAAG,CAAC,GAAG,IAAIG,GAAG,CAACjB,cAAc,CAAC,OAAO,EAAE/C,KAAK,CAAC,CAAC,CAAC,CAACpO,IAAI,CAAC,GAAG,CAAC,CAACR,IAAI,CAAC,CAAC,GAAGyS,GAAG,KAAK,OAAO,GAAGd,cAAc,CAAC,OAAO,EAAE/C,KAAK,CAAC,CAACpO,IAAI,CAAC,GAAG,CAAC,CAACR,IAAI,CAAC,CAAC,GAAG4O,KAAK;cACnK,CAAClP,OAAO,CAACmS,MAAM,GAAGnS,OAAO,CAACmS,MAAM,IAAI,CAAC,CAAC,MAAMnS,OAAO,CAACmS,MAAM,CAACY,GAAG,CAAC,GAAG7D,KAAK,CAAC;cACxElP,OAAO,CAAC+R,YAAY,CAACgB,GAAG,EAAE7D,KAAK,CAAC;YACpC;UACJ;QACJ,CAAC,CAAC;MACN;IACJ;IACA,OAAOiE,kBAAkBA,CAACnT,OAAO,EAAEiC,QAAQ,GAAG,EAAE,EAAE;MAC9C,OAAO,IAAI,CAACI,SAAS,CAACrC,OAAO,CAAC,GACxBA,OAAO,CAAC8J,OAAO,CAAE,2FAA0F7H,QAAS;AAClI,qIAAqIA,QAAS;AAC9I,yGAAyGA,QAAS;AAClH,0GAA0GA,QAAS;AACnH,4GAA4GA,QAAS;AACrH,8GAA8GA,QAAS;AACvH,qHAAqHA,QAAS,EAAC,CAAC,GAClH,KAAK;IACf;EACJ;EAAC,OAvsBKvC,UAAU;AAAA;AAysBhB,MAAM0T,6BAA6B,CAAC;EAChCpT,OAAO;EACPqT,QAAQ;EACRlN,iBAAiB;EACjBiL,WAAWA,CAACpR,OAAO,EAAEqT,QAAQ,GAAGA,CAAA,KAAM,CAAE,CAAC,EAAE;IACvC,IAAI,CAACrT,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACqT,QAAQ,GAAGA,QAAQ;EAC5B;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACnN,iBAAiB,GAAGzG,UAAU,CAACwG,oBAAoB,CAAC,IAAI,CAAClG,OAAO,CAAC;IACtE,KAAK,IAAIQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC2F,iBAAiB,CAAC1F,MAAM,EAAED,CAAC,EAAE,EAAE;MACpD,IAAI,CAAC2F,iBAAiB,CAAC3F,CAAC,CAAC,CAACyS,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACI,QAAQ,CAAC;IACvE;EACJ;EACAE,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACpN,iBAAiB,EAAE;MACxB,KAAK,IAAI3F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC2F,iBAAiB,CAAC1F,MAAM,EAAED,CAAC,EAAE,EAAE;QACpD,IAAI,CAAC2F,iBAAiB,CAAC3F,CAAC,CAAC,CAACgT,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACH,QAAQ,CAAC;MAC1E;IACJ;EACJ;EACAI,OAAOA,CAAA,EAAG;IACN,IAAI,CAACF,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACvT,OAAO,GAAG,IAAI;IACnB,IAAI,CAACqT,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAClN,iBAAiB,GAAG,IAAI;EACjC;AACJ;;AAEA;AACA;AACA;;AAEA,SAASiN,6BAA6B,EAAE1T,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}