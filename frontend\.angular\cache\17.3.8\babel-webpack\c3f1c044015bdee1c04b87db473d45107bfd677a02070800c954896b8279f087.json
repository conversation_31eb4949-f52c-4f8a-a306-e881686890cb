{"ast": null, "code": "// import { ChartOptions, Chart } from 'chart.js';\nimport \"../../../../assets/ts/chartjs-rounded-corners\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/auth.service\";\nimport * as i2 from \"src/app/core/services/goal.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/dropdown\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"../../circle-line/circle-line.component\";\nconst _c0 = [\"viewGoals\"];\nconst _c1 = () => [];\nfunction ViewGoalsComponent_div_0_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const goal_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(goal_r3.language);\n  }\n}\nfunction ViewGoalsComponent_div_0_div_16_app_circle_line_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-circle-line\", 31);\n  }\n  if (rf & 2) {\n    const w_r4 = i0.ɵɵnextContext().index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"circles\", ctx_r1.weeklyHoursCircle[ctx_r1.indexToShow][w_r4]);\n  }\n}\nfunction ViewGoalsComponent_div_0_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, ViewGoalsComponent_div_0_div_16_app_circle_line_1_Template, 1, 1, \"app-circle-line\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const w_r4 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showWeek[ctx_r1.indexToShow][w_r4]);\n  }\n}\nfunction ViewGoalsComponent_div_0_div_85_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const reason_r5 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", reason_r5, \" \");\n  }\n}\nfunction ViewGoalsComponent_div_0_div_91_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const area_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", area_r6, \" \");\n  }\n}\nfunction ViewGoalsComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2, 0)(2, \"div\", 3)(3, \"strong\");\n    i0.ɵɵtext(4, \" Choose Goal \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 4)(6, \"p-dropdown\", 5);\n    i0.ɵɵlistener(\"onChange\", function ViewGoalsComponent_div_0_Template_p_dropdown_onChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.changeIndex($event));\n    });\n    i0.ɵɵtemplate(7, ViewGoalsComponent_div_0_ng_template_7_Template, 3, 1, \"ng-template\", 6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8);\n    i0.ɵɵtext(10, \" Weekly Goals \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 9)(12, \"img\", 10);\n    i0.ɵɵlistener(\"click\", function ViewGoalsComponent_div_0_Template_img_click_12_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showWeekLeft(ctx_r1.indexToShow));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 11);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"img\", 12);\n    i0.ɵɵlistener(\"click\", function ViewGoalsComponent_div_0_Template_img_click_15_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showWeekRight(ctx_r1.indexToShow));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, ViewGoalsComponent_div_0_div_16_Template, 2, 1, \"div\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 7)(18, \"div\", 8);\n    i0.ɵɵtext(19, \" Desired Level \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(20, \"app-circle-line\", 14);\n    i0.ɵɵelementStart(21, \"div\", 15)(22, \"div\", 16);\n    i0.ɵɵtext(23, \" Goal Insights \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(24, \"canvas\", 17);\n    i0.ɵɵelementStart(25, \"div\", 18)(26, \"div\", 19);\n    i0.ɵɵelement(27, \"div\", 20);\n    i0.ɵɵelementStart(28, \"div\", 21);\n    i0.ɵɵtext(29, \"OUTSTANDING!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 19);\n    i0.ɵɵelement(31, \"div\", 22);\n    i0.ɵɵelementStart(32, \"div\", 21);\n    i0.ɵɵtext(33, \"Goal Met!\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 19);\n    i0.ɵɵelement(35, \"div\", 23);\n    i0.ɵɵelementStart(36, \"div\", 21);\n    i0.ɵɵtext(37, \"Almost There\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"div\", 19);\n    i0.ɵɵelement(39, \"div\", 24);\n    i0.ɵɵelementStart(40, \"div\", 21);\n    i0.ɵɵtext(41, \"Didn't Start\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelement(42, \"hr\");\n    i0.ɵɵelementStart(43, \"div\", 25)(44, \"div\", 26)(45, \"strong\");\n    i0.ɵɵtext(46, \"Language: \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 26);\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(49, \"div\", 25)(50, \"div\", 26)(51, \"strong\");\n    i0.ɵɵtext(52, \"Teacher: \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"div\", 26);\n    i0.ɵɵtext(54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 27)(56, \"div\", 26)(57, \"strong\");\n    i0.ɵɵtext(58);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(59, \"div\", 26);\n    i0.ɵɵtext(60);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(61, \"div\", 27)(62, \"div\", 26)(63, \"strong\");\n    i0.ɵɵtext(64);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(65, \"div\", 26);\n    i0.ɵɵtext(66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(67, \"div\", 27)(68, \"div\", 26)(69, \"strong\");\n    i0.ɵɵtext(70);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(71, \"div\", 26);\n    i0.ɵɵtext(72);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(73, \"div\", 27)(74, \"div\", 26)(75, \"strong\");\n    i0.ɵɵtext(76, \"When\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(77, \"div\", 26);\n    i0.ɵɵtext(78);\n    i0.ɵɵpipe(79, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(80, \"div\", 27)(81, \"div\", 26)(82, \"strong\");\n    i0.ɵɵtext(83, \"Why\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(84, \"div\", 26);\n    i0.ɵɵtemplate(85, ViewGoalsComponent_div_0_div_85_Template, 2, 1, \"div\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(86, \"div\", 27)(87, \"div\", 26)(88, \"strong\");\n    i0.ɵɵtext(89, \"What\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(90, \"div\", 26);\n    i0.ɵɵtemplate(91, ViewGoalsComponent_div_0_div_91_Template, 2, 1, \"div\", 13);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"options\", ctx_r1.studentGoals);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate2(\" Week \", ctx_r1.currentWeek[ctx_r1.indexToShow] + 1, \"/\", ctx_r1.goalToShow.totalWeeks, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(27, _c1).constructor(ctx_r1.goalToShow.totalWeeks));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"circles\", ctx_r1.circleLines.get(ctx_r1.goalToShow.id));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"datasets\", ctx_r1.barChartData[ctx_r1.indexToShow])(\"labels\", ctx_r1.barChartLabels[ctx_r1.indexToShow])(\"options\", ctx_r1.barChartOptions)(\"plugins\", ctx_r1.barChartPlugins)(\"legend\", ctx_r1.barChartLegend)(\"chartType\", ctx_r1.barChartType)(\"colors\", ctx_r1.colors);\n    i0.ɵɵadvance(24);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.goalToShow.language, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r1.goalToShow.teacher.firstName, \" \", ctx_r1.goalToShow.teacher.lastName, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Starting \", ctx_r1.goalToShow.language, \" Level\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.goalToShow.startingLevel, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Current \", ctx_r1.goalToShow.language, \" Level\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.goalToShow.currentLevel, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.goalToShow.language, \" Level Goal\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.goalToShow.desiredLevel, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(79, 24, ctx_r1.goalToShow.desiredAchieveDate, \"MMMM, y\"), \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.goalToShow.reasons);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.goalToShow.areas);\n  }\n}\nexport class ViewGoalsComponent {\n  constructor(authService, goalService) {\n    this.authService = authService;\n    this.goalService = goalService;\n    this.student = this.authService.getLoggedInUser();\n    this.weeklyHoursCircle = [];\n    this.showWeek = [];\n    this.studentGoals = [];\n    this.currentWeek = [];\n    this.circleLines = new Map();\n    // Note: we MUST define colors for custom types\n    // see https://github.com/valor-software/ng2-charts/issues/876\n    this.colors = [];\n    this.goalToShow = {};\n    this.indexToShow = 0;\n  }\n  ngOnInit() {\n    // this.goalService.updateListener.subscribe(()=>{\n    //   this.goalService.getGoals().pipe(take(1)).subscribe(res => {\n    //     console.log(res)\n    //     this.studentGoals = res;\n    //     if(res.length>0){\n    //       this.circleLines = this.goalService.getGoalLevelsInCircle(this.studentGoals, 'goals');\n    //       this.goalToShow = this.studentGoals[0];\n    //       for (let i = 0; i < this.studentGoals.length; i++) {\n    //         this.barChartData[i] = []\n    //         this.barChartData[i].push({\n    //           data: this.studentGoals[i].hoursCompleted,\n    //           barThickness: 30,\n    //           barPercentage: 1,\n    //           borderColor: (context) => {\n    //             const index = context.dataIndex!;\n    //             return this.getBarBorderColor(this.studentGoals[i], index) + \" !important\"\n    //           },\n    //           backgroundColor: (context) => {\n    //             const index = context.dataIndex!;\n    //             return this.getBarColor(this.studentGoals[i], index)\n    //           },\n    //           borderWidth: 2,\n    //         })\n    //         this.barChartLabels[i] = [];\n    //         this.colors[i] = [];\n    //         for (let j = 0; j < this.studentGoals[i].hoursCompleted.length; j++) {\n    //           this.barChartLabels[i].push(\"Week \" + (j + 1))\n    //           this.colors[i].push({\n    //             backgroundColor: \"hack\",\n    //             borderColor: \"hack\",\n    //             hoverBackgroundColor: \"hack\",\n    //             hoverBorderColor: \"hack\"\n    //           })\n    //         }\n    //       }\n    //       this.circleLines = this.goalService.getGoalLevelsInCircle(this.studentGoals, 'view-goals');\n    //       let g = 0;\n    //       for (let goal of this.studentGoals) {\n    //         setTimeout(() => {\n    //           // this.createStats(goal)\n    //         }, 1000);\n    //         this.weeklyHoursCircle[g] = []\n    //         this.showWeek[g] = []\n    //         for (let w = 0; w < goal.totalWeeks; w++) {\n    //           this.weeklyHoursCircle[g][w] = []\n    //           if (!goal.hoursCompleted[w]) {\n    //             for (let i = 0; i < goal.hoursWeekly; i++) {\n    //               this.showWeek[g][i] = false;\n    //               let circleLine: CircleLine = {\n    //                 text: (i + 1).toString() + \"h\",\n    //                 lineStyle: '1px dashed lightgray',\n    //                 background: 'white',\n    //                 color: '#a4a2e6',\n    //                 width: document.getElementById('view-goals')!.clientWidth / (goal.hoursWeekly - 1) - 42 + 'px',\n    //                 type: CircleType.GENERAL\n    //               }\n    //               this.weeklyHoursCircle[g][w].push(circleLine)\n    //             }\n    //           } else {\n    //             let iMax = goal.hoursCompleted[w] > goal.hoursWeekly ? goal.hoursWeekly: goal.hoursCompleted[w]\n    //             for (let i = 0; i < iMax; i++) {\n    //               this.showWeek[g][i] = false;\n    //               let line = i < iMax - 1 ? '1px solid lightgray' : '1px dashed lightgray'\n    //               let circleLine: CircleLine = {\n    //                 text: (i + 1).toString() + \"h\",\n    //                 lineStyle: line,\n    //                 background: '#a4a2e6',\n    //                 color: 'white',\n    //                 width: document.getElementById('view-goals')!.clientWidth / (goal.hoursWeekly - 1) - 42 + 'px',\n    //                 type: CircleType.GENERAL\n    //               }\n    //               this.weeklyHoursCircle[g][w].push(circleLine)\n    //             }\n    //             for (let i = iMax; i < goal.hoursWeekly; i++) {\n    //               let circleLine: CircleLine = {\n    //                 text: (i + 1).toString() + \"h\",\n    //                 lineStyle: '1px dashed lightgray',\n    //                 background: 'white',\n    //                 color: '#a4a2e6',\n    //                 width: document.getElementById('view-goals')!.clientWidth / (goal.hoursWeekly - 1) - 42 + 'px',\n    //                 type: CircleType.GENERAL\n    //               }\n    //               this.weeklyHoursCircle[g][w].push(circleLine)\n    //               this.showWeek[g][i] = false;\n    //             }\n    //           }\n    //         }\n    //         this.showWeek[g][goal.hoursCompleted.length - 1] = true\n    //         this.currentWeek[g] = goal.hoursCompleted.length - 1\n    //         g++;\n    //       }\n    //     }\n    //   })\n    // })\n  }\n  ngAfterContentInit() {}\n  changeIndex(event) {\n    // this.indexToShow = this.studentGoals.findIndex(((el: StudentGoal) => el.language == event.value.language));\n    // this.goalToShow = this.studentGoals[this.indexToShow];\n  }\n  getBarColor(goal, hoursCompletedIndex) {\n    // if (goal.hoursCompleted[hoursCompletedIndex] / goal.hoursWeekly <= 0.1) {\n    //   return \"#D7DAEC\"\n    // }\n    // if (goal.hoursCompleted[hoursCompletedIndex] / goal.hoursWeekly <= 0.9) {\n    //   return \"#A4A2E6\"\n    // }\n    // if (goal.hoursCompleted[hoursCompletedIndex] / goal.hoursWeekly <= 1) {\n    //   return \"#7209B7\"\n    // }\n    return \"#F27769\";\n  }\n  getBarBorderColor(goal, hoursCompletedIndex) {\n    // if (goal.hoursCompleted[hoursCompletedIndex] / goal.hoursWeekly <= 0.25) {\n    //   return \"#c5cbf1\"\n    // }\n    // if (goal.hoursCompleted[hoursCompletedIndex] / goal.hoursWeekly <= 0.5) {\n    //   return \"#8b87f0\"\n    // }\n    // if (goal.hoursCompleted[hoursCompletedIndex] / goal.hoursWeekly <= 0.75) {\n    //   return \"#6301a5\"\n    // }\n    return \"#e66456\";\n  }\n  createStats(goal) {\n    // return new Promise((resolve, reject) => {\n    //   const doughnut: Chart = new Chart(\"stats-\" + goal.id!, {\n    //     type: \"bar\",\n    //     data: {\n    //       labels: labels,\n    //       datasets: [{\n    //         borderColor: (context) => {\n    //           const index = context.dataIndex!;\n    //           return this.getBarBorderColor(goal, index)\n    //         },\n    //         backgroundColor: (context) => {\n    //           const index = context.dataIndex!;\n    //           return this.getBarColor(goal, index)\n    //         },\n    //         borderWidth: 2,\n    //         data: goal.hoursCompleted,\n    //       }]\n    //     },\n    //     options: {\n    //       // cornerRadius: 20,\n    //       scales: {\n    //         yAxes: [{\n    //           display: true,\n    //           stacked: true,\n    //           ticks: {\n    //             min: 0, // minimum value\n    //             max: 10 // maximum value\n    //           }\n    //         }]\n    //       },\n    //       responsive: true,\n    //       legend: {\n    //         display: false,\n    //         position: \"right\",\n    //         labels: {\n    //           usePointStyle: true,\n    //           boxWidth: 8,\n    //           fontSize: 15,\n    //           fontColor: \"black\",\n    //           // padding: 9\n    //         },\n    //       }\n    //     },\n    //   });\n    //   if (doughnut) {\n    //     resolve(doughnut);\n    //   } else {\n    //     reject('No doughnut found for: ' + goal.id);\n    //   }\n    // });\n  }\n  showWeekLeft(i) {\n    this.showWeek[i][this.currentWeek[i]] = false;\n    this.showWeek[i][this.currentWeek[i] - 1] = true;\n    this.currentWeek[i]--;\n  }\n  showWeekRight(i) {\n    this.showWeek[i][this.currentWeek[i]] = false;\n    this.showWeek[i][this.currentWeek[i] + 1] = true;\n    this.currentWeek[i]++;\n  }\n  static #_ = this.ɵfac = function ViewGoalsComponent_Factory(t) {\n    return new (t || ViewGoalsComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.GoalService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ViewGoalsComponent,\n    selectors: [[\"app-view-goals\"]],\n    viewQuery: function ViewGoalsComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.viewGoals = _t.first);\n      }\n    },\n    inputs: {\n      student: \"student\"\n    },\n    decls: 1,\n    vars: 1,\n    consts: [[\"viewGoals\", \"\"], [\"class\", \"goals\", \"id\", \"view-goals\", 4, \"ngIf\"], [\"id\", \"view-goals\", 1, \"goals\"], [2, \"display\", \"flex\", \"align-items\", \"center\"], [2, \"margin-left\", \"15px\"], [\"placeholder\", \"Choose Goal\", \"optionLabel\", \"language\", 3, \"onChange\", \"options\"], [\"pTemplate\", \"item\"], [1, \"weekly-goal\"], [2, \"padding\", \"20px 0\"], [1, \"weeks-counter\"], [\"src\", \"/assets/icons/left-week.svg\", 3, \"click\"], [2, \"margin\", \"0 15px\"], [\"src\", \"/assets/icons/right-week.svg\", 3, \"click\"], [4, \"ngFor\", \"ngForOf\"], [2, \"width\", \"100%\", 3, \"circles\"], [1, \"goal-chart\"], [1, \"goal-chart-title\"], [\"baseChart\", \"\", 3, \"datasets\", \"labels\", \"options\", \"plugins\", \"legend\", \"chartType\", \"colors\"], [1, \"indicators\"], [1, \"indicator\"], [1, \"circle\", 2, \"background-color\", \"#F27769\"], [1, \"text\"], [1, \"circle\", 2, \"background-color\", \"#7209B7\"], [1, \"circle\", 2, \"background-color\", \"#A4A2E6\"], [1, \"circle\", 2, \"background-color\", \"#D7DAEC\"], [1, \"section\", 2, \"margin-bottom\", \"15px\"], [1, \"section-content\"], [1, \"section\"], [1, \"country-item\"], [1, \"country-name\"], [3, \"circles\", 4, \"ngIf\"], [3, \"circles\"]],\n    template: function ViewGoalsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, ViewGoalsComponent_div_0_Template, 92, 28, \"div\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.studentGoals.length > 0);\n      }\n    },\n    dependencies: [i3.NgForOf, i3.NgIf, i4.Dropdown, i5.PrimeTemplate, i6.CircleLineComponent, i3.DatePipe],\n    styles: [\".goals[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n}\\n.goals[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  width: 100%;\\n  flex-wrap: wrap;\\n  padding: 10px 0;\\n}\\n.goals[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .section-content[_ngcontent-%COMP%] {\\n  width: 50%;\\n}\\n.goals[_ngcontent-%COMP%]   .goal[_ngcontent-%COMP%] {\\n  margin: 10px 0;\\n  display: flex;\\n  justify-content: space-evenly;\\n  width: 100%;\\n  flex-wrap: wrap;\\n}\\n.goals[_ngcontent-%COMP%]   .goal[_ngcontent-%COMP%]:first-child {\\n  margin: 0;\\n}\\n\\n.weekly-goal[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  position: relative;\\n  width: 100%;\\n}\\n.weekly-goal[_ngcontent-%COMP%]   .weeks-counter[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  position: absolute;\\n  width: 100%;\\n  justify-content: center;\\n  text-align: center;\\n  top: 20px;\\n}\\n.weekly-goal[_ngcontent-%COMP%]   .weeks-counter[_ngcontent-%COMP%]    > img[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n\\n.goal-chart[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-top: 20px;\\n}\\n.goal-chart[_ngcontent-%COMP%]   .goal-chart-title[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  padding: 10px 0;\\n}\\n\\n.details-title[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  padding: 10px 0;\\n}\\n\\n.indicators[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-evenly;\\n  align-items: center;\\n  padding: 25px 0;\\n  font-size: 15px;\\n}\\n@media screen and (max-width: 768px) {\\n  .indicators[_ngcontent-%COMP%] {\\n    flex-wrap: wrap;\\n  }\\n}\\n.indicators[_ngcontent-%COMP%]   .indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-right: 15px;\\n}\\n@media screen and (max-width: 768px) {\\n  .indicators[_ngcontent-%COMP%]   .indicator[_ngcontent-%COMP%] {\\n    width: 33%;\\n  }\\n}\\n.indicators[_ngcontent-%COMP%]   .indicator[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%] {\\n  width: 15px;\\n  height: 15px;\\n  border-radius: 50%;\\n}\\n.indicators[_ngcontent-%COMP%]   .indicator[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%] {\\n  margin-left: 15px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "goal_r3", "language", "ɵɵelement", "ɵɵproperty", "ctx_r1", "weeklyHoursCircle", "indexToShow", "w_r4", "ɵɵtemplate", "ViewGoalsComponent_div_0_div_16_app_circle_line_1_Template", "showWeek", "ɵɵtextInterpolate1", "reason_r5", "area_r6", "ɵɵlistener", "ViewGoalsComponent_div_0_Template_p_dropdown_onChange_6_listener", "$event", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "changeIndex", "ViewGoalsComponent_div_0_ng_template_7_Template", "ViewGoalsComponent_div_0_Template_img_click_12_listener", "showWeekLeft", "ViewGoalsComponent_div_0_Template_img_click_15_listener", "showWeekRight", "ViewGoalsComponent_div_0_div_16_Template", "ViewGoalsComponent_div_0_div_85_Template", "ViewGoalsComponent_div_0_div_91_Template", "studentGoals", "ɵɵtextInterpolate2", "currentWeek", "goalToShow", "totalWeeks", "ɵɵpureFunction0", "_c1", "constructor", "circleLines", "get", "id", "barChartData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "barChartOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "barChartLegend", "barChartType", "colors", "teacher", "firstName", "lastName", "startingLevel", "currentLevel", "desiredLevel", "ɵɵpipeBind2", "desiredAchieveDate", "reasons", "areas", "ViewGoalsComponent", "authService", "goalService", "student", "getLoggedInUser", "Map", "ngOnInit", "ngAfterContentInit", "event", "getBarColor", "goal", "hoursCompletedIndex", "getBarBorderColor", "createStats", "i", "_", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "GoalService", "_2", "selectors", "viewQuery", "ViewGoalsComponent_Query", "rf", "ctx", "ViewGoalsComponent_div_0_Template", "length"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\goal\\view-goals\\view-goals.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\goal\\view-goals\\view-goals.component.html"], "sourcesContent": ["import { Component, Input, OnInit, ViewChild } from '@angular/core';\r\nimport { take } from 'rxjs/operators';\r\nimport { Level } from 'src/app/core/models/classroom.model';\r\nimport { CircleLine, CircleType, StudentGoal } from 'src/app/core/models/goal.model';\r\nimport { User } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { GoalService } from 'src/app/core/services/goal.service';\r\n// import { ChartOptions, Chart } from 'chart.js';\r\nimport \"../../../../assets/ts/chartjs-rounded-corners\";\r\n\r\n@Component({\r\n  selector: 'app-view-goals',\r\n  templateUrl: './view-goals.component.html',\r\n  styleUrls: ['./view-goals.component.scss']\r\n})\r\nexport class ViewGoalsComponent implements OnInit {\r\n  @ViewChild('viewGoals') public viewGoals: any;\r\n  @Input() student: User = this.authService.getLoggedInUser();\r\n  public weeklyHoursCircle: CircleLine[][][] = []\r\n  public showWeek: boolean[][] = []\r\n  public studentGoals: StudentGoal[] = []\r\n  public currentWeek: number[] = [];\r\n  public circleLines: Map<string, CircleLine[]> = new Map();\r\n\r\n  // Note: we MUST define colors for custom types\r\n  // see https://github.com/valor-software/ng2-charts/issues/876\r\n  public colors: any[][] = [];\r\n  goalToShow: StudentGoal = {} as StudentGoal;\r\n\r\n\r\n  constructor(\r\n    private authService: AuthService,\r\n    private goalService: GoalService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    // this.goalService.updateListener.subscribe(()=>{\r\n    //   this.goalService.getGoals().pipe(take(1)).subscribe(res => {\r\n    //     console.log(res)\r\n    //     this.studentGoals = res;\r\n    //     if(res.length>0){\r\n    //       this.circleLines = this.goalService.getGoalLevelsInCircle(this.studentGoals, 'goals');\r\n    //       this.goalToShow = this.studentGoals[0];\r\n    //       for (let i = 0; i < this.studentGoals.length; i++) {\r\n    //         this.barChartData[i] = []\r\n    //         this.barChartData[i].push({\r\n    //           data: this.studentGoals[i].hoursCompleted,\r\n    //           barThickness: 30,\r\n    //           barPercentage: 1,\r\n    //           borderColor: (context) => {\r\n    //             const index = context.dataIndex!;\r\n    //             return this.getBarBorderColor(this.studentGoals[i], index) + \" !important\"\r\n    //           },\r\n    //           backgroundColor: (context) => {\r\n    //             const index = context.dataIndex!;\r\n    //             return this.getBarColor(this.studentGoals[i], index)\r\n    //           },\r\n    //           borderWidth: 2,\r\n    //         })\r\n    //         this.barChartLabels[i] = [];\r\n    //         this.colors[i] = [];\r\n    //         for (let j = 0; j < this.studentGoals[i].hoursCompleted.length; j++) {\r\n    //           this.barChartLabels[i].push(\"Week \" + (j + 1))\r\n    //           this.colors[i].push({\r\n    //             backgroundColor: \"hack\",\r\n    //             borderColor: \"hack\",\r\n    //             hoverBackgroundColor: \"hack\",\r\n    //             hoverBorderColor: \"hack\"\r\n    //           })\r\n    //         }\r\n    //       }\r\n    //       this.circleLines = this.goalService.getGoalLevelsInCircle(this.studentGoals, 'view-goals');\r\n    //       let g = 0;\r\n    //       for (let goal of this.studentGoals) {\r\n    //         setTimeout(() => {\r\n      \r\n    //           // this.createStats(goal)\r\n    //         }, 1000);\r\n    //         this.weeklyHoursCircle[g] = []\r\n    //         this.showWeek[g] = []\r\n    //         for (let w = 0; w < goal.totalWeeks; w++) {\r\n    //           this.weeklyHoursCircle[g][w] = []\r\n    //           if (!goal.hoursCompleted[w]) {\r\n    //             for (let i = 0; i < goal.hoursWeekly; i++) {\r\n    //               this.showWeek[g][i] = false;\r\n    //               let circleLine: CircleLine = {\r\n    //                 text: (i + 1).toString() + \"h\",\r\n    //                 lineStyle: '1px dashed lightgray',\r\n    //                 background: 'white',\r\n    //                 color: '#a4a2e6',\r\n    //                 width: document.getElementById('view-goals')!.clientWidth / (goal.hoursWeekly - 1) - 42 + 'px',\r\n    //                 type: CircleType.GENERAL\r\n    //               }\r\n    //               this.weeklyHoursCircle[g][w].push(circleLine)\r\n    //             }\r\n    //           } else {\r\n    //             let iMax = goal.hoursCompleted[w] > goal.hoursWeekly ? goal.hoursWeekly: goal.hoursCompleted[w]\r\n    //             for (let i = 0; i < iMax; i++) {\r\n    //               this.showWeek[g][i] = false;\r\n    //               let line = i < iMax - 1 ? '1px solid lightgray' : '1px dashed lightgray'\r\n      \r\n    //               let circleLine: CircleLine = {\r\n    //                 text: (i + 1).toString() + \"h\",\r\n    //                 lineStyle: line,\r\n    //                 background: '#a4a2e6',\r\n    //                 color: 'white',\r\n    //                 width: document.getElementById('view-goals')!.clientWidth / (goal.hoursWeekly - 1) - 42 + 'px',\r\n    //                 type: CircleType.GENERAL\r\n    //               }\r\n    //               this.weeklyHoursCircle[g][w].push(circleLine)\r\n    //             }\r\n    //             for (let i = iMax; i < goal.hoursWeekly; i++) {\r\n    //               let circleLine: CircleLine = {\r\n    //                 text: (i + 1).toString() + \"h\",\r\n    //                 lineStyle: '1px dashed lightgray',\r\n    //                 background: 'white',\r\n    //                 color: '#a4a2e6',\r\n    //                 width: document.getElementById('view-goals')!.clientWidth / (goal.hoursWeekly - 1) - 42 + 'px',\r\n    //                 type: CircleType.GENERAL\r\n    //               }\r\n    //               this.weeklyHoursCircle[g][w].push(circleLine)\r\n    //               this.showWeek[g][i] = false;\r\n    //             }\r\n    //           }\r\n    //         }\r\n    //         this.showWeek[g][goal.hoursCompleted.length - 1] = true\r\n    //         this.currentWeek[g] = goal.hoursCompleted.length - 1\r\n    //         g++;\r\n    //       }\r\n    //     }\r\n    //   })\r\n    // })\r\n  }\r\n\r\n  ngAfterContentInit() {\r\n\r\n\r\n  }\r\n  indexToShow: number = 0;\r\n  changeIndex(event: any) {\r\n    // this.indexToShow = this.studentGoals.findIndex(((el: StudentGoal) => el.language == event.value.language));\r\n    // this.goalToShow = this.studentGoals[this.indexToShow];\r\n  }\r\n\r\n  getBarColor(goal: StudentGoal, hoursCompletedIndex: number) {\r\n    // if (goal.hoursCompleted[hoursCompletedIndex] / goal.hoursWeekly <= 0.1) {\r\n    //   return \"#D7DAEC\"\r\n    // }\r\n    // if (goal.hoursCompleted[hoursCompletedIndex] / goal.hoursWeekly <= 0.9) {\r\n    //   return \"#A4A2E6\"\r\n    // }\r\n    // if (goal.hoursCompleted[hoursCompletedIndex] / goal.hoursWeekly <= 1) {\r\n    //   return \"#7209B7\"\r\n    // }\r\n    return \"#F27769\"\r\n  }\r\n\r\n  getBarBorderColor(goal: StudentGoal, hoursCompletedIndex: number) {\r\n    // if (goal.hoursCompleted[hoursCompletedIndex] / goal.hoursWeekly <= 0.25) {\r\n    //   return \"#c5cbf1\"\r\n    // }\r\n    // if (goal.hoursCompleted[hoursCompletedIndex] / goal.hoursWeekly <= 0.5) {\r\n    //   return \"#8b87f0\"\r\n    // }\r\n    // if (goal.hoursCompleted[hoursCompletedIndex] / goal.hoursWeekly <= 0.75) {\r\n    //   return \"#6301a5\"\r\n    // }\r\n    return \"#e66456\"\r\n  }\r\n\r\n  createStats(goal: StudentGoal) {\r\n\r\n\r\n    // return new Promise((resolve, reject) => {\r\n    //   const doughnut: Chart = new Chart(\"stats-\" + goal.id!, {\r\n    //     type: \"bar\",\r\n    //     data: {\r\n    //       labels: labels,\r\n    //       datasets: [{\r\n    //         borderColor: (context) => {\r\n    //           const index = context.dataIndex!;\r\n    //           return this.getBarBorderColor(goal, index)\r\n    //         },\r\n    //         backgroundColor: (context) => {\r\n    //           const index = context.dataIndex!;\r\n    //           return this.getBarColor(goal, index)\r\n    //         },\r\n    //         borderWidth: 2,\r\n    //         data: goal.hoursCompleted,\r\n    //       }]\r\n    //     },\r\n    //     options: {\r\n    //       // cornerRadius: 20,\r\n    //       scales: {\r\n    //         yAxes: [{\r\n    //           display: true,\r\n    //           stacked: true,\r\n    //           ticks: {\r\n    //             min: 0, // minimum value\r\n    //             max: 10 // maximum value\r\n    //           }\r\n    //         }]\r\n    //       },\r\n    //       responsive: true,\r\n    //       legend: {\r\n    //         display: false,\r\n    //         position: \"right\",\r\n    //         labels: {\r\n    //           usePointStyle: true,\r\n    //           boxWidth: 8,\r\n    //           fontSize: 15,\r\n    //           fontColor: \"black\",\r\n    //           // padding: 9\r\n    //         },\r\n    //       }\r\n    //     },\r\n    //   });\r\n    //   if (doughnut) {\r\n    //     resolve(doughnut);\r\n    //   } else {\r\n    //     reject('No doughnut found for: ' + goal.id);\r\n    //   }\r\n    // });\r\n  }\r\n\r\n  showWeekLeft(i: number) {\r\n    this.showWeek[i][this.currentWeek[i]] = false\r\n    this.showWeek[i][this.currentWeek[i] - 1] = true\r\n    this.currentWeek[i]--\r\n  }\r\n\r\n  showWeekRight(i: number) {\r\n    this.showWeek[i][this.currentWeek[i]] = false\r\n    this.showWeek[i][this.currentWeek[i] + 1] = true\r\n    this.currentWeek[i]++\r\n  }\r\n\r\n}\r\n", "<div *ngIf=\"studentGoals.length > 0\" class=\"goals\" id=\"view-goals\" #viewGoals>\r\n    <div style=\"display:flex; align-items: center\">\r\n        <strong>\r\n            Choose Goal\r\n        </strong>\r\n        <div style=\"margin-left:15px\">\r\n            <p-dropdown placeholder=\"Choose Goal\" [options]=\"studentGoals\" (onChange)=\"changeIndex($event)\"\r\n                optionLabel=\"language\">\r\n                <ng-template let-goal pTemplate=\"item\">\r\n                    <div class=\"country-item\">\r\n                        <div class=\"country-name\">{{goal.language}}</div>\r\n                    </div>\r\n                </ng-template>\r\n            </p-dropdown>\r\n        </div>\r\n    </div>\r\n    <!-- <div *ngFor=\"let goal of studentGoals; let indexToShow =index\" class=\"goal\"> -->\r\n    <div class=\"weekly-goal\">\r\n        <div style=\"padding:20px 0;\">\r\n            Weekly Goals\r\n        </div>\r\n        <div class=\"weeks-counter\">\r\n            <img src=\"/assets/icons/left-week.svg\" (click)=\"showWeekLeft(indexToShow)\">\r\n            <div style=\"margin: 0 15px;\">\r\n                Week {{this.currentWeek[indexToShow]+1}}/{{goalToShow.totalWeeks}}\r\n            </div>\r\n            <img src=\"/assets/icons/right-week.svg\" (click)=\"showWeekRight(indexToShow)\">\r\n        </div>\r\n\r\n        <div *ngFor=\"let item of [].constructor(goalToShow.totalWeeks); let w = index\">\r\n            <app-circle-line *ngIf=\"showWeek[indexToShow][w]\" [circles]=\"weeklyHoursCircle[indexToShow][w]\">\r\n            </app-circle-line>\r\n        </div>\r\n\r\n    </div>\r\n    <div class=\"weekly-goal\">\r\n        <div style=\"padding:20px 0;\">\r\n            Desired Level\r\n        </div>\r\n    </div>\r\n    <app-circle-line style=\"width:100%\" [circles]=\"circleLines.get(goalToShow.id!)\"></app-circle-line>\r\n    <div class=\"goal-chart\">\r\n        <div class=\"goal-chart-title\">\r\n            Goal Insights\r\n        </div>\r\n\r\n\r\n\r\n        <canvas baseChart [datasets]=\"barChartData[indexToShow]\" [labels]=\"barChartLabels[indexToShow]\"\r\n            [options]=\"barChartOptions\" [plugins]=\"barChartPlugins\" [legend]=\"barChartLegend\" [chartType]=\"barChartType\"\r\n            [colors]=\"colors\">\r\n        </canvas>\r\n\r\n\r\n        <div class=\"indicators\">\r\n            <div class=\"indicator\">\r\n                <div class=\"circle\" style=\"background-color: #F27769;\"></div>\r\n                <div class=\"text\">OUTSTANDING!</div>\r\n            </div>\r\n            <div class=\"indicator\">\r\n                <div class=\"circle\" style=\"background-color: #7209B7;\"></div>\r\n                <div class=\"text\">Goal Met!</div>\r\n            </div>\r\n            <div class=\"indicator\">\r\n                <div class=\"circle\" style=\"background-color: #A4A2E6;\"></div>\r\n                <div class=\"text\">Almost There</div>\r\n            </div>\r\n            <div class=\"indicator\">\r\n                <div class=\"circle\" style=\"background-color: #D7DAEC;\"></div>\r\n                <div class=\"text\">Didn't Start</div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    <hr>\r\n    <div class=\"section\" style=\"margin-bottom:15px;\">\r\n        <div class=\"section-content\">\r\n            <strong>Language: </strong>\r\n        </div>\r\n        <div class=\"section-content\">\r\n            {{goalToShow.language}}\r\n        </div>\r\n    </div>\r\n    <div class=\"section\" style=\"margin-bottom:15px;\">\r\n        <div class=\"section-content\">\r\n            <strong>Teacher: </strong>\r\n        </div>\r\n        <div class=\"section-content\">\r\n            {{goalToShow.teacher.firstName}} {{goalToShow.teacher.lastName}}\r\n        </div>\r\n    </div>\r\n    <div class=\"section\">\r\n        <div class=\"section-content\">\r\n            <strong>Starting {{goalToShow.language}} Level</strong>\r\n        </div>\r\n        <div class=\"section-content\">\r\n            {{goalToShow.startingLevel}}\r\n        </div>\r\n    </div>\r\n    <div class=\"section\">\r\n        <div class=\"section-content\">\r\n            <strong>Current {{goalToShow.language}} Level</strong>\r\n        </div>\r\n        <div class=\"section-content\">\r\n            {{goalToShow.currentLevel}}\r\n        </div>\r\n    </div>\r\n    <div class=\"section\">\r\n        <div class=\"section-content\">\r\n            <strong>{{goalToShow.language}} Level Goal</strong>\r\n        </div>\r\n        <div class=\"section-content\">\r\n            {{goalToShow.desiredLevel}}\r\n        </div>\r\n    </div>\r\n    <div class=\"section\">\r\n        <div class=\"section-content\">\r\n            <strong>When</strong>\r\n        </div>\r\n        <div class=\"section-content\">\r\n            {{goalToShow.desiredAchieveDate | date: 'MMMM, y'}}\r\n        </div>\r\n    </div>\r\n    <div class=\"section\">\r\n        <div class=\"section-content\">\r\n            <strong>Why</strong>\r\n        </div>\r\n        <div class=\"section-content\">\r\n            <div *ngFor=\"let reason of goalToShow.reasons\">\r\n                {{reason}}\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <div class=\"section\">\r\n        <div class=\"section-content\">\r\n            <strong>What</strong>\r\n        </div>\r\n        <div class=\"section-content\">\r\n            <div *ngFor=\"let area of goalToShow.areas\">\r\n                {{area}}\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <!-- </div> -->\r\n</div>"], "mappings": "AAOA;AACA,OAAO,+CAA+C;;;;;;;;;;;;ICE9BA,EADJ,CAAAC,cAAA,cAA0B,cACI;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAC/CF,EAD+C,CAAAG,YAAA,EAAM,EAC/C;;;;IADwBH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAAC,QAAA,CAAiB;;;;;IAoBvDP,EAAA,CAAAQ,SAAA,0BACkB;;;;;IADgCR,EAAA,CAAAS,UAAA,YAAAC,MAAA,CAAAC,iBAAA,CAAAD,MAAA,CAAAE,WAAA,EAAAC,IAAA,EAA6C;;;;;IADnGb,EAAA,CAAAC,cAAA,UAA+E;IAC3ED,EAAA,CAAAc,UAAA,IAAAC,0DAAA,8BAAgG;IAEpGf,EAAA,CAAAG,YAAA,EAAM;;;;;IAFgBH,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAS,UAAA,SAAAC,MAAA,CAAAM,QAAA,CAAAN,MAAA,CAAAE,WAAA,EAAAC,IAAA,EAA8B;;;;;IAkGhDb,EAAA,CAAAC,cAAA,UAA+C;IAC3CD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAiB,kBAAA,MAAAC,SAAA,MACJ;;;;;IAQAlB,EAAA,CAAAC,cAAA,UAA2C;IACvCD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAiB,kBAAA,MAAAE,OAAA,MACJ;;;;;;IA1IJnB,EAFR,CAAAC,cAAA,gBAA8E,aAC3B,aACnC;IACJD,EAAA,CAAAE,MAAA,oBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAELH,EADJ,CAAAC,cAAA,aAA8B,oBAEC;IADoCD,EAAA,CAAAoB,UAAA,sBAAAC,iEAAAC,MAAA;MAAAtB,EAAA,CAAAuB,aAAA,CAAAC,GAAA;MAAA,MAAAd,MAAA,GAAAV,EAAA,CAAAyB,aAAA;MAAA,OAAAzB,EAAA,CAAA0B,WAAA,CAAYhB,MAAA,CAAAiB,WAAA,CAAAL,MAAA,CAAmB;IAAA,EAAC;IAE3FtB,EAAA,CAAAc,UAAA,IAAAc,+CAAA,yBAAuC;IAOnD5B,EAFQ,CAAAG,YAAA,EAAa,EACX,EACJ;IAGFH,EADJ,CAAAC,cAAA,aAAyB,aACQ;IACzBD,EAAA,CAAAE,MAAA,sBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADJ,CAAAC,cAAA,cAA2B,eACoD;IAApCD,EAAA,CAAAoB,UAAA,mBAAAS,wDAAA;MAAA7B,EAAA,CAAAuB,aAAA,CAAAC,GAAA;MAAA,MAAAd,MAAA,GAAAV,EAAA,CAAAyB,aAAA;MAAA,OAAAzB,EAAA,CAAA0B,WAAA,CAAShB,MAAA,CAAAoB,YAAA,CAAApB,MAAA,CAAAE,WAAA,CAAyB;IAAA,EAAC;IAA1EZ,EAAA,CAAAG,YAAA,EAA2E;IAC3EH,EAAA,CAAAC,cAAA,eAA6B;IACzBD,EAAA,CAAAE,MAAA,IACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA6E;IAArCD,EAAA,CAAAoB,UAAA,mBAAAW,wDAAA;MAAA/B,EAAA,CAAAuB,aAAA,CAAAC,GAAA;MAAA,MAAAd,MAAA,GAAAV,EAAA,CAAAyB,aAAA;MAAA,OAAAzB,EAAA,CAAA0B,WAAA,CAAShB,MAAA,CAAAsB,aAAA,CAAAtB,MAAA,CAAAE,WAAA,CAA0B;IAAA,EAAC;IAChFZ,EADI,CAAAG,YAAA,EAA6E,EAC3E;IAENH,EAAA,CAAAc,UAAA,KAAAmB,wCAAA,kBAA+E;IAKnFjC,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADJ,CAAAC,cAAA,cAAyB,cACQ;IACzBD,EAAA,CAAAE,MAAA,uBACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IACNH,EAAA,CAAAQ,SAAA,2BAAkG;IAE9FR,EADJ,CAAAC,cAAA,eAAwB,eACU;IAC1BD,EAAA,CAAAE,MAAA,uBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAINH,EAAA,CAAAQ,SAAA,kBAGS;IAILR,EADJ,CAAAC,cAAA,eAAwB,eACG;IACnBD,EAAA,CAAAQ,SAAA,eAA6D;IAC7DR,EAAA,CAAAC,cAAA,eAAkB;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAClCF,EADkC,CAAAG,YAAA,EAAM,EAClC;IACNH,EAAA,CAAAC,cAAA,eAAuB;IACnBD,EAAA,CAAAQ,SAAA,eAA6D;IAC7DR,EAAA,CAAAC,cAAA,eAAkB;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAC/BF,EAD+B,CAAAG,YAAA,EAAM,EAC/B;IACNH,EAAA,CAAAC,cAAA,eAAuB;IACnBD,EAAA,CAAAQ,SAAA,eAA6D;IAC7DR,EAAA,CAAAC,cAAA,eAAkB;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAClCF,EADkC,CAAAG,YAAA,EAAM,EAClC;IACNH,EAAA,CAAAC,cAAA,eAAuB;IACnBD,EAAA,CAAAQ,SAAA,eAA6D;IAC7DR,EAAA,CAAAC,cAAA,eAAkB;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAG1CF,EAH0C,CAAAG,YAAA,EAAM,EAClC,EACJ,EACJ;IAENH,EAAA,CAAAQ,SAAA,UAAI;IAGIR,EAFR,CAAAC,cAAA,eAAiD,eAChB,cACjB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IACtBF,EADsB,CAAAG,YAAA,EAAS,EACzB;IACNH,EAAA,CAAAC,cAAA,eAA6B;IACzBD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IAGEH,EAFR,CAAAC,cAAA,eAAiD,eAChB,cACjB;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IACrBF,EADqB,CAAAG,YAAA,EAAS,EACxB;IACNH,EAAA,CAAAC,cAAA,eAA6B;IACzBD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IAGEH,EAFR,CAAAC,cAAA,eAAqB,eACY,cACjB;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IAClDF,EADkD,CAAAG,YAAA,EAAS,EACrD;IACNH,EAAA,CAAAC,cAAA,eAA6B;IACzBD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IAGEH,EAFR,CAAAC,cAAA,eAAqB,eACY,cACjB;IAAAD,EAAA,CAAAE,MAAA,IAAqC;IACjDF,EADiD,CAAAG,YAAA,EAAS,EACpD;IACNH,EAAA,CAAAC,cAAA,eAA6B;IACzBD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IAGEH,EAFR,CAAAC,cAAA,eAAqB,eACY,cACjB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAC9CF,EAD8C,CAAAG,YAAA,EAAS,EACjD;IACNH,EAAA,CAAAC,cAAA,eAA6B;IACzBD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IAGEH,EAFR,CAAAC,cAAA,eAAqB,eACY,cACjB;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAChBF,EADgB,CAAAG,YAAA,EAAS,EACnB;IACNH,EAAA,CAAAC,cAAA,eAA6B;IACzBD,EAAA,CAAAE,MAAA,IACJ;;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IAGEH,EAFR,CAAAC,cAAA,eAAqB,eACY,cACjB;IAAAD,EAAA,CAAAE,MAAA,WAAG;IACfF,EADe,CAAAG,YAAA,EAAS,EAClB;IACNH,EAAA,CAAAC,cAAA,eAA6B;IACzBD,EAAA,CAAAc,UAAA,KAAAoB,wCAAA,kBAA+C;IAIvDlC,EADI,CAAAG,YAAA,EAAM,EACJ;IAGEH,EAFR,CAAAC,cAAA,eAAqB,eACY,cACjB;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAChBF,EADgB,CAAAG,YAAA,EAAS,EACnB;IACNH,EAAA,CAAAC,cAAA,eAA6B;IACzBD,EAAA,CAAAc,UAAA,KAAAqB,wCAAA,kBAA2C;IAMvDnC,EAHQ,CAAAG,YAAA,EAAM,EACJ,EAEJ;;;;IA1I4CH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAS,UAAA,YAAAC,MAAA,CAAA0B,YAAA,CAAwB;IAkB1DpC,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAqC,kBAAA,WAAA3B,MAAA,CAAA4B,WAAA,CAAA5B,MAAA,CAAAE,WAAA,YAAAF,MAAA,CAAA6B,UAAA,CAAAC,UAAA,MACJ;IAIkBxC,EAAA,CAAAI,SAAA,GAA0C;IAA1CJ,EAAA,CAAAS,UAAA,YAAAT,EAAA,CAAAyC,eAAA,KAAAC,GAAA,EAAAC,WAAA,CAAAjC,MAAA,CAAA6B,UAAA,CAAAC,UAAA,EAA0C;IAWhCxC,EAAA,CAAAI,SAAA,GAA2C;IAA3CJ,EAAA,CAAAS,UAAA,YAAAC,MAAA,CAAAkC,WAAA,CAAAC,GAAA,CAAAnC,MAAA,CAAA6B,UAAA,CAAAO,EAAA,EAA2C;IAQzD9C,EAAA,CAAAI,SAAA,GAAsC;IAEpDJ,EAFc,CAAAS,UAAA,aAAAC,MAAA,CAAAqC,YAAA,CAAArC,MAAA,CAAAE,WAAA,EAAsC,WAAAF,MAAA,CAAAsC,cAAA,CAAAtC,MAAA,CAAAE,WAAA,EAAuC,YAAAF,MAAA,CAAAuC,eAAA,CAChE,YAAAvC,MAAA,CAAAwC,eAAA,CAA4B,WAAAxC,MAAA,CAAAyC,cAAA,CAA0B,cAAAzC,MAAA,CAAA0C,YAAA,CAA2B,WAAA1C,MAAA,CAAA2C,MAAA,CAC3F;IA8BjBrD,EAAA,CAAAI,SAAA,IACJ;IADIJ,EAAA,CAAAiB,kBAAA,MAAAP,MAAA,CAAA6B,UAAA,CAAAhC,QAAA,MACJ;IAOIP,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAqC,kBAAA,MAAA3B,MAAA,CAAA6B,UAAA,CAAAe,OAAA,CAAAC,SAAA,OAAA7C,MAAA,CAAA6B,UAAA,CAAAe,OAAA,CAAAE,QAAA,MACJ;IAIYxD,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAiB,kBAAA,cAAAP,MAAA,CAAA6B,UAAA,CAAAhC,QAAA,WAAsC;IAG9CP,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAiB,kBAAA,MAAAP,MAAA,CAAA6B,UAAA,CAAAkB,aAAA,MACJ;IAIYzD,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAiB,kBAAA,aAAAP,MAAA,CAAA6B,UAAA,CAAAhC,QAAA,WAAqC;IAG7CP,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAiB,kBAAA,MAAAP,MAAA,CAAA6B,UAAA,CAAAmB,YAAA,MACJ;IAIY1D,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAiB,kBAAA,KAAAP,MAAA,CAAA6B,UAAA,CAAAhC,QAAA,gBAAkC;IAG1CP,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAiB,kBAAA,MAAAP,MAAA,CAAA6B,UAAA,CAAAoB,YAAA,MACJ;IAOI3D,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAiB,kBAAA,MAAAjB,EAAA,CAAA4D,WAAA,SAAAlD,MAAA,CAAA6B,UAAA,CAAAsB,kBAAA,kBACJ;IAO4B7D,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAS,UAAA,YAAAC,MAAA,CAAA6B,UAAA,CAAAuB,OAAA,CAAqB;IAUvB9D,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAS,UAAA,YAAAC,MAAA,CAAA6B,UAAA,CAAAwB,KAAA,CAAmB;;;AD3HrD,OAAM,MAAOC,kBAAkB;EAe7BrB,YACUsB,WAAwB,EACxBC,WAAwB;IADxB,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IAfZ,KAAAC,OAAO,GAAS,IAAI,CAACF,WAAW,CAACG,eAAe,EAAE;IACpD,KAAAzD,iBAAiB,GAAqB,EAAE;IACxC,KAAAK,QAAQ,GAAgB,EAAE;IAC1B,KAAAoB,YAAY,GAAkB,EAAE;IAChC,KAAAE,WAAW,GAAa,EAAE;IAC1B,KAAAM,WAAW,GAA8B,IAAIyB,GAAG,EAAE;IAEzD;IACA;IACO,KAAAhB,MAAM,GAAY,EAAE;IAC3B,KAAAd,UAAU,GAAgB,EAAiB;IA+G3C,KAAA3B,WAAW,GAAW,CAAC;EAzGnB;EAEJ0D,QAAQA,CAAA;IACN;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;EAGFC,kBAAkBA,CAAA,GAGlB;EAEA5C,WAAWA,CAAC6C,KAAU;IACpB;IACA;EAAA;EAGFC,WAAWA,CAACC,IAAiB,EAAEC,mBAA2B;IACxD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,OAAO,SAAS;EAClB;EAEAC,iBAAiBA,CAACF,IAAiB,EAAEC,mBAA2B;IAC9D;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,OAAO,SAAS;EAClB;EAEAE,WAAWA,CAACH,IAAiB;IAG3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;EAGF5C,YAAYA,CAACgD,CAAS;IACpB,IAAI,CAAC9D,QAAQ,CAAC8D,CAAC,CAAC,CAAC,IAAI,CAACxC,WAAW,CAACwC,CAAC,CAAC,CAAC,GAAG,KAAK;IAC7C,IAAI,CAAC9D,QAAQ,CAAC8D,CAAC,CAAC,CAAC,IAAI,CAACxC,WAAW,CAACwC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;IAChD,IAAI,CAACxC,WAAW,CAACwC,CAAC,CAAC,EAAE;EACvB;EAEA9C,aAAaA,CAAC8C,CAAS;IACrB,IAAI,CAAC9D,QAAQ,CAAC8D,CAAC,CAAC,CAAC,IAAI,CAACxC,WAAW,CAACwC,CAAC,CAAC,CAAC,GAAG,KAAK;IAC7C,IAAI,CAAC9D,QAAQ,CAAC8D,CAAC,CAAC,CAAC,IAAI,CAACxC,WAAW,CAACwC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;IAChD,IAAI,CAACxC,WAAW,CAACwC,CAAC,CAAC,EAAE;EACvB;EAAC,QAAAC,CAAA,G;qBA5NUf,kBAAkB,EAAAhE,EAAA,CAAAgF,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlF,EAAA,CAAAgF,iBAAA,CAAAG,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlBrB,kBAAkB;IAAAsB,SAAA;IAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;QCf/BzF,EAAA,CAAAc,UAAA,IAAA6E,iCAAA,mBAA8E;;;QAAxE3F,EAAA,CAAAS,UAAA,SAAAiF,GAAA,CAAAtD,YAAA,CAAAwD,MAAA,KAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}