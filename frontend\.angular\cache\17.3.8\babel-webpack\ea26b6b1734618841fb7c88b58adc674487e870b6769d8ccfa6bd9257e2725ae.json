{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport { ClassroomModule } from '../classroom/classroom.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { ScrollPanelModule } from 'primeng/scrollpanel';\nimport { CalendarModule } from 'primeng/calendar';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { OverlayPanelModule } from 'primeng/overlaypanel';\nimport { RouterModule } from '@angular/router';\nimport { DashboardRoutingModule } from './dashboard-routing.module';\nimport { DeferModule } from 'primeng/defer';\nimport { CarouselModule } from 'primeng/carousel';\nimport * as i0 from \"@angular/core\";\nexport let DashboardModule = /*#__PURE__*/(() => {\n  class DashboardModule {\n    static #_ = this.ɵfac = function DashboardModule_Factory(t) {\n      return new (t || DashboardModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: DashboardModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, RouterModule, DashboardRoutingModule, ClassroomModule, SharedModule, ReactiveFormsModule, MatProgressSpinnerModule, ScrollPanelModule, CalendarModule, FormsModule, InputTextModule, DropdownModule, OverlayPanelModule, DeferModule, CarouselModule]\n    });\n  }\n  return DashboardModule;\n})();", "map": {"version": 3, "names": ["CommonModule", "SharedModule", "ClassroomModule", "FormsModule", "ReactiveFormsModule", "MatProgressSpinnerModule", "ScrollPanelModule", "CalendarModule", "InputTextModule", "DropdownModule", "OverlayPanelModule", "RouterModule", "DashboardRoutingModule", "DeferModule", "CarouselModule", "DashboardModule", "_", "_2", "_3"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\dashboard\\dashboard.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { DashboardComponent } from './dashboard.component';\r\nimport { SharedModule } from 'src/app/shared/shared.module';\r\nimport { ClassroomModule } from '../classroom/classroom.module';\r\nimport { SafePipe } from 'src/app/core/pipes/save.pipe';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { IntroGradientComponent } from './intro-gradient/intro-gradient.component';\r\nimport { DashboardNotificationsComponent } from './dashboard-notifications/dashboard-notifications.component';\r\nimport { ScrollPanelModule } from 'primeng/scrollpanel';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { DashboardNextStepsComponent } from './dashboard-next-steps/dashboard-next-steps.component';\r\nimport { DashboardBenefitsComponent } from './dashboard-benefits/dashboard-benefits.component';\r\nimport { DashboardRequestTrialFormComponent } from './dashboard-request-trial-form/dashboard-request-trial-form.component';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { OverlayPanelModule } from 'primeng/overlaypanel';\r\nimport { RouterModule } from '@angular/router';\r\nimport { DashboardRoutingModule } from './dashboard-routing.module';\r\nimport { DeferModule } from 'primeng/defer';\r\nimport { CalendarPreviewWeekComponent } from './calendar-preview-week/calendar-preview-week.component';\r\nimport { RedirectToGalaxyComponent } from './redirect-to-galaxy/redirect-to-galaxy.component';\r\nimport { DashboardContactUsFormComponent } from './dashboard-contact-us-form/dashboard-contact-us-form.component';\r\nimport { CarouselModule } from 'primeng/carousel';\r\nimport { DashboardTeacherClassroomsComponent } from './dashboard-teacher-classrooms/dashboard-teacher-classrooms.component';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    DashboardComponent,\r\n    SafePipe,\r\n    IntroGradientComponent,\r\n    DashboardNotificationsComponent,\r\n    DashboardNextStepsComponent,\r\n    DashboardBenefitsComponent,\r\n    DashboardRequestTrialFormComponent,\r\n    CalendarPreviewWeekComponent,\r\n    RedirectToGalaxyComponent,\r\n    DashboardContactUsFormComponent,\r\n    DashboardTeacherClassroomsComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule,\r\n    DashboardRoutingModule,\r\n    ClassroomModule,\r\n    SharedModule,\r\n    ReactiveFormsModule,\r\n    MatProgressSpinnerModule,\r\n    ScrollPanelModule,\r\n    CalendarModule,\r\n    FormsModule,\r\n    InputTextModule,\r\n    DropdownModule,\r\n    OverlayPanelModule,\r\n    DeferModule,\r\n    CarouselModule,\r\n  ]\r\n})\r\nexport class DashboardModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,eAAe,QAAQ,+BAA+B;AAE/D,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,wBAAwB,QAAQ,oCAAoC;AAG7E,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,cAAc,QAAQ,kBAAkB;AAIjD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,WAAW,QAAQ,eAAe;AAI3C,SAASC,cAAc,QAAQ,kBAAkB;;AAoCjD,WAAaC,eAAe;EAAtB,MAAOA,eAAe;IAAA,QAAAC,CAAA,G;uBAAfD,eAAe;IAAA;IAAA,QAAAE,EAAA,G;YAAfF;IAAe;IAAA,QAAAG,EAAA,G;gBAjBxBlB,YAAY,EACZW,YAAY,EACZC,sBAAsB,EACtBV,eAAe,EACfD,YAAY,EACZG,mBAAmB,EACnBC,wBAAwB,EACxBC,iBAAiB,EACjBC,cAAc,EACdJ,WAAW,EACXK,eAAe,EACfC,cAAc,EACdC,kBAAkB,EAClBG,WAAW,EACXC,cAAc;IAAA;;SAGLC,eAAe;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}