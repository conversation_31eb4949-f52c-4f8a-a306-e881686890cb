@import "../../../../../assets/styles/my-radio.scss";
@import "mixins";

.buy-package {
    width: 100%;
    font-size: 17px;
    background: rgb(255, 255, 255);
    border-radius: 14px;
}

.section {
    display: flex;
    flex-direction: column;
    background-color: var(--white);
    border-radius: 14px;
    // box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);
    margin-top: 0;
    margin-bottom: 30px;

    @include breakpoint(tablet) {
        flex-direction: row;
    }

    .left {
        width: 100% !important;

        @include breakpoint(tablet) {
            width: 70% !important;
        }

        padding: 20px;

        .title {
            width: 100%;
            text-align: center;
            font-family: "Proxima Nova Bold";
        }
    }

    .right {
        width: 100%;

        @include breakpoint(tablet) {
            width: 30%;
        }

        margin-top: -10px;
        padding: 20px;
        border-radius: 0 0 14px 0;
        color: white;
        transition: all 0.2s ease-in;
        position: sticky;
        background-image: linear-gradient(to bottom, rgb(117, 136, 244), rgb(95, 110, 209), rgb(73, 85, 175), rgb(50, 61, 142), rgb(27, 39, 110));

        .seperator {
            border-top: 1px solid white;
            width: 100%;
            margin: 15px 0;
        }

        .title {
            font-size: 18px;
            font-family: "Proxima Nova Bold";
        }

        .small-title {
            font-size: 15px;
            font-family: "Proxima Nova Bold";
        }

        .row {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            font-size: 15px;
            text-align: start;

            .row-right {
                font-family: "Proxima Nova Bold";
            }
        }

        .row-first {
            margin-top: 20px;
        }
    }
}

.package-type {
    border-radius: 5px;
    margin-top: 20px;
    padding: 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    border-color: transparent !important;

    .package-type-section {
        flex-direction: column;
        align-items: center;
        display: flex;
        background-color: rgba(204, 214, 255, 0.54);
        width: 100%;
        color: #3345A7;
        border-radius: 10px 10px 5px 5px;

        .package-info {
            color: #000;
            font-size: 13px;
        }

        .my-radio {
            border: 2px solid #2f338d;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            background-color: white;
            margin-left: 10px;
            background-clip: content-box !important;
            cursor: pointer;
            top: -3px;
            left: -15px;
            position: relative;
        }

        .type-name {
            color: white;
            padding: 5px 15px;
            font-size: 0.95rem;
            text-align: center;
            font-family: "Proxima Nova Bold";
            border-radius: 10px;
            width: 100%;
        }
    }
}

.top-btns {
    display: flex;
    width: 100%;
    justify-content: center;
    margin: 20px 0;
    flex-wrap: wrap;
    box-sizing: border-box;

    .btn {
        transition: all 0.2s ease-in;
        padding: 2px;
        border-radius: 12px;
        margin: 10px;
        text-align: center;
        cursor: pointer;

        .btn-language {
            font-family: 'Proxima Nova Bold';
        }

        .btn-teacher-name {
            font-size: 12px;
            color: rgb(101, 122, 239);
            transition: all 0.2s ease-in;
        }
    }

    .btn-classroom {
        // min-width: 7.5rem;
        height: 3rem;
        color: #3345a7;

        &.new {
            background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0.33, #002ccf), color-stop(0.67, #a796ff));

            span {
                background-color: var(--white);
                margin: 10px;
                width: 100%;
                min-height: 100%;
                border-radius: inherit;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }

        @media screen and (min-width: 992px) {
            &.lg\:col-3 {
                @include breakpoint(tablet) {
                    width: 20% !important;
                }
            }
        }
    }

    .btn-hours {
        padding: 10px;
        height: auto;

        width: 33%;

        @include breakpoint(tablet) {
            width: 18%;
        }

        transition: all 0.2s ease-in;

        .price {
            margin: 5px 0;
        }

        .hours-info {
            transition: all 0.2s ease-in;
            color: #000;
        }
    }

    .btn-new {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-family: "Proxima Nova Bold";
    }

    .btn-level {
        border-radius: 50%;
        width: 40px;
        height: 40px;
    }
}

.top-btns-hours {
    flex-wrap: wrap;

    @include breakpoint(tablet) {
        flex-wrap: nowrap;
    }
}

.buy-package-header {
    background-image: url('/assets/images/payment.png');
    align-items: center;
    width: 100%;
    border: 0 !important;
    padding: 10px;
    box-sizing: border-box;
    position: sticky;
    padding: 0.5rem 1rem;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    border-radius: 12px;
    min-height: 37px;
    background-size: inherit;
    color: var(--white);
    font-family: "Proxima Nova Regular", sans-serif;
    letter-spacing: 0.05rem;
    z-index: 2;

    .back-button {
        display: flex;
        align-items: center;
        cursor: pointer;
    }

    .buy-package-title {
        text-align: center;
    }

    .steps {
        display: flex;

        .package-step {
            color: var(--white);
            text-align: center;
            transition: all 0.3s linear;
            width: 33px;
            height: 33px;
            line-height: 33px;
            border-radius: 50%;
            margin: 0 10px;
        }
    }
}

:host ::ng-deep {
    .e-label {
        font-size: 15px !important;
    }

    .p-dropdown {
        width: 100%;
        padding: 4.5px;
        border-radius: 10px;
        margin-top: 10px;
    }

    .dropdown-blue {
        width: 75px !important;

        .with-icon {
            padding-left: 0 !important;
        }
    }

    .fwidth {
        .dropdown-blue {
            width: 100% !important;
        }
    }
}

.country-item {
    display: flex;
    align-items: center;

    .country-name {
        margin-left: 10px;
    }

    img {
        width: 40px;
    }
}

.disabled {
    background: white;
    color: lightgray;
    border: 1px solid lightgray;
}

::ng-deep .mat-tooltip {
    /* your own custom styles here */
    /* e.g. */
    color: yellow;
    font-size: 15px;
    background-color: #707070;
}

.review {
                 
    @include breakpoint(mobile) {
        padding: 1rem;
    }
    padding: 30px;
    box-sizing: border-box;

    .review-section {
        margin-top: 30px;

        .review-section-title {
            color: var(--main-color);
            font-size: 20px;
            font-family: "Proxima Nova Bold";
        }

        .review-section-content {
            color: var(--main-color);
            font-size: 15px;
            display: flex;
            flex-wrap: wrap;

            .col-25,
            .col-50 {
                display: flex;
                flex-direction: column;

                .info {
                    color: #3345A7;
                    font-family: "Proxima Nova Bold";
                }

                .availability {
                    display: flex;

                    .col-50 {
                        width: 50%;
                    }
                }
            }

            .col-25 {
                width: 25%;
                flex-basis: 25%;
                
    @include breakpoint(mobile) {
        width: 100%;
        flex-basis: 100%;
    }
            }

            .col-50 {
                width: 50%;
                flex-basis: 50%;
                @include breakpoint(mobile) {
                    width: 100%;
                    flex-basis: 100%;
                }
            }

            .col-100 {
                width: 100%;
                flex-basis: 100%;
            }
        }
    }

    .review-section:first-child {
        margin-top: 0;
    }
}

.btns {
    position: sticky;
    top: 0px;
    background-color: white;
    border-bottom: 1px solid lightgray;
}

.section-step-3 {
    border-radius: 28px;
    background: linear-gradient(#fff 0%, rgba(56, 115, 244, 0.2) 100%);
    border-radius: 28px 28px 8px 8px;
}

.section-content {
    overflow: hidden;
    transition: height 0.3s ease-out;
}

.fixed-height {
    height: 200px !important;
    overflow-y: scroll !important;
}

.section-step-3-title {
    width: 100%;
    border: 1px solid #2E3D90;
    font-size: 15px;
    text-align: center;
    padding: 0.3rem 0;
    box-sizing: border-box;
    border-radius: 28px;
    position: relative;
    color: #2E3D90;

    .toggle-section {
        .section-arrow {
            background-image: linear-gradient(to top, rgb(117, 136, 244), rgb(95, 110, 209), rgb(73, 85, 175), rgb(50, 61, 142), rgb(27, 39, 110));
            transform: rotate(180deg);
            position: absolute;
            top: 0;
            right: 0;
            overflow: hidden;
            transition-duration: 0.2s;
            transition-property: transform;
            cursor: pointer;
            border-radius: 50%;
            width: 1.8rem;
        }
    }
}

.end {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    height: 470px;

    .end-msg {
        margin-top: 30px;
        font-family: "Proxima Nova Bold";
    }
}

.white-button {
    width: 200px;
    margin-top: 15px;
}

button.p-element {
    color: var(--white);
}

.cursor-none {
    cursor: default !important;
}

.p-custom {
    padding: 0.3rem !important;
}

.hvr-glow:hover,
.hvr-glow:focus,
.hvr-glow:active {
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.46);
}

.level {
    position: relative;
    color: #fff !important;
    font-weight: normal !important;

    div {
        font-family: 'Proxima Nova Regular';
        z-index: 1;
    }

    &:after {
        content: ' ';
        display: block;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-image: url(/assets/images/dashboard/goals/level-bubble.png) !important;
        background-repeat: no-repeat;
        background-position: 50% 0;
        background-size: 40px;
        z-index: 0;
        opacity: 0.8;
    }
}

.level-disabled {
    border: 1px solid #CCD6FF;
    background-image: none;

    &:after {
        opacity: 0.2;
    }
}

.level-default {
    border: 1px solid rgb(101, 122, 239);
    color: rgb(101, 122, 239);
    background-image: none;
}

.level-selected {
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.46);
    margin: 4px;
    background-color: transparent;
    color: #fff;
    border: none;
    overflow: hidden;
    float: left;
    border-radius: 50%;

    &:after {
        opacity: 1;
    }
}

.custom-border {
    border-radius: 0 0 10px 10px !important;
}

.selected::before {
    content: url(/assets/icons/check-blue.svg);
    position: absolute;
    top: 0;
    left: 0;
    left: 50%;
    transform: translate(-50%, -50%);
    top: 55%;
    z-index: 2;
}

.input-fields .input-field {
    margin-top: 0 !important;
}

.payment-step {
    display: flex;
    justify-content: center;
}

.prev-button-outlined,
.next-button-outlined {
    border-radius: 50px;
    background-color: #7082E6 !important;

    &:hover {
        background-color: #7082E6 !important;
        box-shadow: none !important;
        color: #fff !important;
    }

    &.inactive {
        opacity: 0.3;
        cursor: not-allowed;

        &:hover {
            color: #fff;
            border-color: #fff;
        }
    }
}

.prev-button-outlined {
    // opacity: 0.6;
}

.split-pay-button-outlined {
    border-radius: 50px;
    box-shadow: none;
    background-color: #2FB9D3 !important;

    &:hover {
        background-color: #7082E6 !important;
        box-shadow: none !important;
        color: #fff !important;
    }

    &.inactive {
        opacity: 0.3;
        cursor: not-allowed;

        &:hover {
            color: #fff;
            border-color: #fff;
        }
    }
}

.buy-button {
    box-shadow: none !important;
    background: linear-gradient(#9baaff 0%, #152caf 100%);
    border-radius: 50px;

    &:hover {
        background: linear-gradient(#152caf 0%, #9baaff 100%) !important;
        box-shadow: none !important;
        color: #fff !important;
    }
}

.input-element {
    font-size: 0.85rem !important;
    font-family: inherit;
    outline: none !important;
}

.split-text {
    color: #CCD6FF;
}

:host::ng-deep .terms-checkbox {

    .p-checkbox {
        width: 18px;
        height: 18px;
    }

    .p-checkbox-label {
        font-size: 16px;
        cursor: pointer;

        @media screen and (max-width: 768px) {
            font-size: 14px;
        }
    }

    .p-checkbox .p-checkbox-box.p-highlight .p-checkbox-icon.pi-check:before {
        top: 1px;
        left: -5px;
    }

    .p-checkbox .p-checkbox-box {
        border-radius: 50px;
    }

    .p-checkbox .p-checkbox-box.p-highlight {
        border-radius: 50px;
    }

    .p-checkbox .p-checkbox-box .p-checkbox-icon {
        transform: scale(1.3);
    }

    .p-component {

        .p-checkbox-box {
            width: 18px !important;
            height: 18px !important;
        }
    }

    .teacher-checkbox-inner {}

    .p-checkbox-label {}
}

.bottom-buttons {
    @include breakpoint(mobile) {
    z-index: 55555;
     bottom: 0;
     left: 0;
     width: 100%;
     position: fixed;
     padding: 1rem;
     background-color: #2e3b8f;
    }
}

// Revolut Pay Button Styles
#revolut-pay-button {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    // Hover effect for the container
    &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border-color: #6c757d;
    }

    // Ensure the button container has proper spacing
    &:empty {
        display: none;
    }

    // Style for when the button is loading/initializing
    &.loading {
        opacity: 0.7;
        pointer-events: none;

        &::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
    }

    // Style the actual Revolut button inside
    button,
    [role="button"] {
        width: 100% !important;
        max-width: none !important;
        border-radius: 8px !important;
        font-weight: 600 !important;
        font-size: 16px !important;
        padding: 12px 24px !important;
        transition: all 0.2s ease !important;
        position: relative !important;

        &:hover {
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
        }

        &:active {
            transform: translateY(0) !important;
        }
    }

    // Optional: Custom text overlay (uncomment to use)
    /*
    &.custom-text {
        button, [role="button"] {
            &::after {
                content: 'Pay Now - Quick & Secure';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                background: inherit;
                color: inherit;
                font-weight: 600;
                border-radius: inherit;
                z-index: 1;
            }

            // Hide original text
            * {
                opacity: 0;
            }
        }
    }
    */
}

// Animation for loading spinner
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

// Responsive adjustments for Revolut Pay button
@include breakpoint(mobile) {
    #revolut-pay-button {
        padding: 0.25rem;
        border-radius: 8px;

        button,
        [role="button"] {
            font-size: 14px !important;
            padding: 10px 16px !important;
        }
    }
}

// Payment Instructions Styles
.payment-instructions {
    .p-message {
        border-radius: 8px;
        border: 1px solid #b3d4fc;
        background: #f0f8ff;

        .p-message-wrapper {
            padding: 1rem;
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
        }

        .p-message-icon {
            color: #2196f3;
            font-size: 1.1rem;
            margin-top: 0.1rem;
        }

        .p-message-summary {
            font-weight: 600;
            color: #1976d2;
            margin-bottom: 0.25rem;
            display: block;
        }

        .p-message-detail {
            color: #424242;
            font-size: 0.9rem;
            line-height: 1.4;
        }
    }
}

// Payment Method Section Styles
.revolut-pay-section {
    .payment-method-label {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .payment-badge {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
        }
    }
}

// Alternative payment section
.alternative-payment-section {
    position: relative;

    &::before {
        content: 'OR';
        position: absolute;
        top: -10px;
        left: 50%;
        transform: translateX(-50%);
        background: white;
        padding: 0 1rem;
        color: #6c757d;
        font-size: 0.8rem;
        font-weight: 600;
    }

    border-top: 1px solid #dee2e6;
    padding-top: 1.5rem;
    margin-top: 1.5rem;
}

// Disabled button styles
button:disabled {
    opacity: 0.6;
    cursor: not-allowed;

    &:hover {
        opacity: 0.6;
    }
}

// Responsive adjustments for payment labels
@include breakpoint(mobile) {
    .revolut-pay-section {
        .payment-method-label {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;

            .payment-badge {
                align-self: flex-end;
            }
        }
    }
}