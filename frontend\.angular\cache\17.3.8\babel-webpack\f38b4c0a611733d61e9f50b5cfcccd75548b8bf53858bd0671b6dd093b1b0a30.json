{"ast": null, "code": "import { getApps as getApps$1, getApp as getApp$1, registerVersion as registerVersion$1, deleteApp as deleteApp$1, initializeApp as initializeApp$1, initializeServerApp as initializeServerApp$1, onLog as onLog$1, setLogLevel as setLogLevel$1 } from 'firebase/app';\nexport * from 'firebase/app';\nimport { from, timer } from 'rxjs';\nimport { concatMap, distinct } from 'rxjs/operators';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Optional, PLATFORM_ID, VERSION as VERSION$1, NgModule, Inject, NgZone, Injector, makeEnvironmentProviders } from '@angular/core';\nimport { VERSION, ɵAngularFireSchedulers, ɵzoneWrap } from '@angular/fire';\nclass FirebaseApp {\n  constructor(app) {\n    return app;\n  }\n}\nclass FirebaseApps {\n  constructor() {\n    return getApps$1();\n  }\n}\nconst firebaseApp$ = /*#__PURE__*/ /*#__PURE__*/timer(0, 300).pipe( /*#__PURE__*/concatMap(() => from(getApps$1())), /*#__PURE__*/distinct());\nfunction defaultFirebaseAppFactory(provided) {\n  // Use the provided app, if there is only one, otherwise fetch the default app\n  if (provided && provided.length === 1) {\n    return provided[0];\n  }\n  return new FirebaseApp(getApp$1());\n}\n// With FIREBASE_APPS I wanted to capture the default app instance, if it is initialized by\n// the reserved URL; ɵPROVIDED_FIREBASE_APPS is not for public consumption and serves to ensure that all\n// provideFirebaseApp(...) calls are satisfied before FirebaseApp$ or FirebaseApp is resolved\nconst PROVIDED_FIREBASE_APPS = /*#__PURE__*/new InjectionToken('angularfire2._apps');\n// Injecting FirebaseApp will now only inject the default Firebase App\n// this allows allows beginners to import /__/firebase/init.js to auto initialize Firebase App\n// from the reserved URL.\nconst DEFAULT_FIREBASE_APP_PROVIDER = {\n  provide: FirebaseApp,\n  useFactory: defaultFirebaseAppFactory,\n  deps: [[/*#__PURE__*/new Optional(), PROVIDED_FIREBASE_APPS]]\n};\nconst FIREBASE_APPS_PROVIDER = {\n  provide: FirebaseApps,\n  deps: [[/*#__PURE__*/new Optional(), PROVIDED_FIREBASE_APPS]]\n};\nfunction firebaseAppFactory(fn) {\n  return (zone, injector) => {\n    const platformId = injector.get(PLATFORM_ID);\n    registerVersion$1('angularfire', VERSION.full, 'core');\n    registerVersion$1('angularfire', VERSION.full, 'app');\n    // eslint-disable-next-line @typescript-eslint/no-base-to-string\n    registerVersion$1('angular', VERSION$1.full, platformId.toString());\n    const app = zone.runOutsideAngular(() => fn(injector));\n    return new FirebaseApp(app);\n  };\n}\nlet FirebaseAppModule = /*#__PURE__*/(() => {\n  class FirebaseAppModule {\n    // eslint-disable-next-line @typescript-eslint/ban-types\n    constructor(platformId) {\n      registerVersion$1('angularfire', VERSION.full, 'core');\n      registerVersion$1('angularfire', VERSION.full, 'app');\n      // eslint-disable-next-line @typescript-eslint/no-base-to-string\n      registerVersion$1('angular', VERSION$1.full, platformId.toString());\n    }\n    static ɵfac = function FirebaseAppModule_Factory(t) {\n      return new (t || FirebaseAppModule)(i0.ɵɵinject(PLATFORM_ID));\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: FirebaseAppModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [DEFAULT_FIREBASE_APP_PROVIDER, FIREBASE_APPS_PROVIDER]\n    });\n  }\n  return FirebaseAppModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n// Calling initializeApp({ ... }, 'name') multiple times will add more FirebaseApps into the FIREBASE_APPS\n// injection scope. This allows developers to more easily work with multiple Firebase Applications. Downside\n// is that DI for app name and options doesn't really make sense anymore.\nfunction provideFirebaseApp(fn, ...deps) {\n  return makeEnvironmentProviders([DEFAULT_FIREBASE_APP_PROVIDER, FIREBASE_APPS_PROVIDER, {\n    provide: PROVIDED_FIREBASE_APPS,\n    useFactory: firebaseAppFactory(fn),\n    multi: true,\n    deps: [NgZone, Injector, ɵAngularFireSchedulers, ...deps]\n  }]);\n}\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\nconst deleteApp = /*#__PURE__*/ɵzoneWrap(deleteApp$1, true);\nconst getApp = /*#__PURE__*/ɵzoneWrap(getApp$1, true);\nconst getApps = /*#__PURE__*/ɵzoneWrap(getApps$1, true);\nconst initializeApp = /*#__PURE__*/ɵzoneWrap(initializeApp$1, true);\nconst initializeServerApp = /*#__PURE__*/ɵzoneWrap(initializeServerApp$1, true);\nconst onLog = /*#__PURE__*/ɵzoneWrap(onLog$1, true);\nconst registerVersion = /*#__PURE__*/ɵzoneWrap(registerVersion$1, true);\nconst setLogLevel = /*#__PURE__*/ɵzoneWrap(setLogLevel$1, true);\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FirebaseApp, FirebaseAppModule, FirebaseApps, deleteApp, firebaseApp$, getApp, getApps, initializeApp, initializeServerApp, onLog, provideFirebaseApp, registerVersion, setLogLevel };", "map": {"version": 3, "names": ["getApps", "getApps$1", "getApp", "getApp$1", "registerVersion", "registerVersion$1", "deleteApp", "deleteApp$1", "initializeApp", "initializeApp$1", "initializeServerApp", "initializeServerApp$1", "onLog", "onLog$1", "setLogLevel", "setLogLevel$1", "from", "timer", "concatMap", "distinct", "i0", "InjectionToken", "Optional", "PLATFORM_ID", "VERSION", "VERSION$1", "NgModule", "Inject", "NgZone", "Injector", "makeEnvironmentProviders", "ɵAngularFireSchedulers", "ɵzoneWrap", "FirebaseApp", "constructor", "app", "FirebaseApps", "firebaseApp$", "pipe", "defaultFirebaseAppFactory", "provided", "length", "PROVIDED_FIREBASE_APPS", "DEFAULT_FIREBASE_APP_PROVIDER", "provide", "useFactory", "deps", "FIREBASE_APPS_PROVIDER", "firebaseAppFactory", "fn", "zone", "injector", "platformId", "get", "full", "toString", "runOutsideAngular", "FirebaseAppModule", "ɵfac", "FirebaseAppModule_Factory", "t", "ɵɵinject", "ɵmod", "ɵɵdefineNgModule", "type", "ɵinj", "ɵɵdefineInjector", "providers", "ngDevMode", "provideFirebaseApp", "multi"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@angular/fire/fesm2022/angular-fire-app.mjs"], "sourcesContent": ["import { getApps as getApps$1, getApp as getApp$1, registerVersion as registerVersion$1, deleteApp as deleteApp$1, initializeApp as initializeApp$1, initializeServerApp as initializeServerApp$1, onLog as onLog$1, setLogLevel as setLogLevel$1 } from 'firebase/app';\nexport * from 'firebase/app';\nimport { from, timer } from 'rxjs';\nimport { concatMap, distinct } from 'rxjs/operators';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Optional, PLATFORM_ID, VERSION as VERSION$1, NgModule, Inject, NgZone, Injector, makeEnvironmentProviders } from '@angular/core';\nimport { VERSION, ɵAngularFireSchedulers, ɵzoneWrap } from '@angular/fire';\n\nclass FirebaseApp {\n    constructor(app) {\n        return app;\n    }\n}\nclass FirebaseApps {\n    constructor() {\n        return getApps$1();\n    }\n}\nconst firebaseApp$ = timer(0, 300).pipe(concatMap(() => from(getApps$1())), distinct());\n\nfunction defaultFirebaseAppFactory(provided) {\n    // Use the provided app, if there is only one, otherwise fetch the default app\n    if (provided && provided.length === 1) {\n        return provided[0];\n    }\n    return new FirebaseApp(getApp$1());\n}\n// With FIREBASE_APPS I wanted to capture the default app instance, if it is initialized by\n// the reserved URL; ɵPROVIDED_FIREBASE_APPS is not for public consumption and serves to ensure that all\n// provideFirebaseApp(...) calls are satisfied before FirebaseApp$ or FirebaseApp is resolved\nconst PROVIDED_FIREBASE_APPS = new InjectionToken('angularfire2._apps');\n// Injecting FirebaseApp will now only inject the default Firebase App\n// this allows allows beginners to import /__/firebase/init.js to auto initialize Firebase App\n// from the reserved URL.\nconst DEFAULT_FIREBASE_APP_PROVIDER = {\n    provide: FirebaseApp,\n    useFactory: defaultFirebaseAppFactory,\n    deps: [\n        [new Optional(), PROVIDED_FIREBASE_APPS],\n    ],\n};\nconst FIREBASE_APPS_PROVIDER = {\n    provide: FirebaseApps,\n    deps: [\n        [new Optional(), PROVIDED_FIREBASE_APPS],\n    ],\n};\nfunction firebaseAppFactory(fn) {\n    return (zone, injector) => {\n        const platformId = injector.get(PLATFORM_ID);\n        registerVersion$1('angularfire', VERSION.full, 'core');\n        registerVersion$1('angularfire', VERSION.full, 'app');\n        // eslint-disable-next-line @typescript-eslint/no-base-to-string\n        registerVersion$1('angular', VERSION$1.full, platformId.toString());\n        const app = zone.runOutsideAngular(() => fn(injector));\n        return new FirebaseApp(app);\n    };\n}\nclass FirebaseAppModule {\n    // eslint-disable-next-line @typescript-eslint/ban-types\n    constructor(platformId) {\n        registerVersion$1('angularfire', VERSION.full, 'core');\n        registerVersion$1('angularfire', VERSION.full, 'app');\n        // eslint-disable-next-line @typescript-eslint/no-base-to-string\n        registerVersion$1('angular', VERSION$1.full, platformId.toString());\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.6\", ngImport: i0, type: FirebaseAppModule, deps: [{ token: PLATFORM_ID }], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.6\", ngImport: i0, type: FirebaseAppModule });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.6\", ngImport: i0, type: FirebaseAppModule, providers: [\n            DEFAULT_FIREBASE_APP_PROVIDER,\n            FIREBASE_APPS_PROVIDER,\n        ] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.6\", ngImport: i0, type: FirebaseAppModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        DEFAULT_FIREBASE_APP_PROVIDER,\n                        FIREBASE_APPS_PROVIDER,\n                    ]\n                }]\n        }], ctorParameters: () => [{ type: Object, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }] });\n// Calling initializeApp({ ... }, 'name') multiple times will add more FirebaseApps into the FIREBASE_APPS\n// injection scope. This allows developers to more easily work with multiple Firebase Applications. Downside\n// is that DI for app name and options doesn't really make sense anymore.\nfunction provideFirebaseApp(fn, ...deps) {\n    return makeEnvironmentProviders([\n        DEFAULT_FIREBASE_APP_PROVIDER,\n        FIREBASE_APPS_PROVIDER,\n        {\n            provide: PROVIDED_FIREBASE_APPS,\n            useFactory: firebaseAppFactory(fn),\n            multi: true,\n            deps: [\n                NgZone,\n                Injector,\n                ɵAngularFireSchedulers,\n                ...deps,\n            ],\n        }\n    ]);\n}\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\nconst deleteApp = ɵzoneWrap(deleteApp$1, true);\nconst getApp = ɵzoneWrap(getApp$1, true);\nconst getApps = ɵzoneWrap(getApps$1, true);\nconst initializeApp = ɵzoneWrap(initializeApp$1, true);\nconst initializeServerApp = ɵzoneWrap(initializeServerApp$1, true);\nconst onLog = ɵzoneWrap(onLog$1, true);\nconst registerVersion = ɵzoneWrap(registerVersion$1, true);\nconst setLogLevel = ɵzoneWrap(setLogLevel$1, true);\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { FirebaseApp, FirebaseAppModule, FirebaseApps, deleteApp, firebaseApp$, getApp, getApps, initializeApp, initializeServerApp, onLog, provideFirebaseApp, registerVersion, setLogLevel };\n"], "mappings": "AAAA,SAASA,OAAO,IAAIC,SAAS,EAAEC,MAAM,IAAIC,QAAQ,EAAEC,eAAe,IAAIC,iBAAiB,EAAEC,SAAS,IAAIC,WAAW,EAAEC,aAAa,IAAIC,eAAe,EAAEC,mBAAmB,IAAIC,qBAAqB,EAAEC,KAAK,IAAIC,OAAO,EAAEC,WAAW,IAAIC,aAAa,QAAQ,cAAc;AACvQ,cAAc,cAAc;AAC5B,SAASC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AAClC,SAASC,SAAS,EAAEC,QAAQ,QAAQ,gBAAgB;AACpD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,OAAO,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,wBAAwB,QAAQ,eAAe;AACzJ,SAASN,OAAO,EAAEO,sBAAsB,EAAEC,SAAS,QAAQ,eAAe;AAE1E,MAAMC,WAAW,CAAC;EACdC,WAAWA,CAACC,GAAG,EAAE;IACb,OAAOA,GAAG;EACd;AACJ;AACA,MAAMC,YAAY,CAAC;EACfF,WAAWA,CAAA,EAAG;IACV,OAAOjC,SAAS,CAAC,CAAC;EACtB;AACJ;AACA,MAAMoC,YAAY,gBAAG,cAAApB,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAACqB,IAAI,eAACpB,SAAS,CAAC,MAAMF,IAAI,CAACf,SAAS,CAAC,CAAC,CAAC,CAAC,eAAEkB,QAAQ,CAAC,CAAC,CAAC;AAEvF,SAASoB,yBAAyBA,CAACC,QAAQ,EAAE;EACzC;EACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;IACnC,OAAOD,QAAQ,CAAC,CAAC,CAAC;EACtB;EACA,OAAO,IAAIP,WAAW,CAAC9B,QAAQ,CAAC,CAAC,CAAC;AACtC;AACA;AACA;AACA;AACA,MAAMuC,sBAAsB,gBAAG,IAAIrB,cAAc,CAAC,oBAAoB,CAAC;AACvE;AACA;AACA;AACA,MAAMsB,6BAA6B,GAAG;EAClCC,OAAO,EAAEX,WAAW;EACpBY,UAAU,EAAEN,yBAAyB;EACrCO,IAAI,EAAE,CACF,cAAC,IAAIxB,QAAQ,CAAC,CAAC,EAAEoB,sBAAsB,CAAC;AAEhD,CAAC;AACD,MAAMK,sBAAsB,GAAG;EAC3BH,OAAO,EAAER,YAAY;EACrBU,IAAI,EAAE,CACF,cAAC,IAAIxB,QAAQ,CAAC,CAAC,EAAEoB,sBAAsB,CAAC;AAEhD,CAAC;AACD,SAASM,kBAAkBA,CAACC,EAAE,EAAE;EAC5B,OAAO,CAACC,IAAI,EAAEC,QAAQ,KAAK;IACvB,MAAMC,UAAU,GAAGD,QAAQ,CAACE,GAAG,CAAC9B,WAAW,CAAC;IAC5ClB,iBAAiB,CAAC,aAAa,EAAEmB,OAAO,CAAC8B,IAAI,EAAE,MAAM,CAAC;IACtDjD,iBAAiB,CAAC,aAAa,EAAEmB,OAAO,CAAC8B,IAAI,EAAE,KAAK,CAAC;IACrD;IACAjD,iBAAiB,CAAC,SAAS,EAAEoB,SAAS,CAAC6B,IAAI,EAAEF,UAAU,CAACG,QAAQ,CAAC,CAAC,CAAC;IACnE,MAAMpB,GAAG,GAAGe,IAAI,CAACM,iBAAiB,CAAC,MAAMP,EAAE,CAACE,QAAQ,CAAC,CAAC;IACtD,OAAO,IAAIlB,WAAW,CAACE,GAAG,CAAC;EAC/B,CAAC;AACL;AAAC,IACKsB,iBAAiB;EAAvB,MAAMA,iBAAiB,CAAC;IACpB;IACAvB,WAAWA,CAACkB,UAAU,EAAE;MACpB/C,iBAAiB,CAAC,aAAa,EAAEmB,OAAO,CAAC8B,IAAI,EAAE,MAAM,CAAC;MACtDjD,iBAAiB,CAAC,aAAa,EAAEmB,OAAO,CAAC8B,IAAI,EAAE,KAAK,CAAC;MACrD;MACAjD,iBAAiB,CAAC,SAAS,EAAEoB,SAAS,CAAC6B,IAAI,EAAEF,UAAU,CAACG,QAAQ,CAAC,CAAC,CAAC;IACvE;IACA,OAAOG,IAAI,YAAAC,0BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFH,iBAAiB,EAA3BrC,EAAE,CAAAyC,QAAA,CAA2CtC,WAAW;IAAA;IACjJ,OAAOuC,IAAI,kBAD8E1C,EAAE,CAAA2C,gBAAA;MAAAC,IAAA,EACSP;IAAiB;IACrH,OAAOQ,IAAI,kBAF8E7C,EAAE,CAAA8C,gBAAA;MAAAC,SAAA,EAEuC,CAC1HxB,6BAA6B,EAC7BI,sBAAsB;IACzB;EACT;EAAC,OAdKU,iBAAiB;AAAA;AAevB;EAAA,QAAAW,SAAA,oBAAAA,SAAA;AAAA;AAYA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACpB,EAAE,EAAE,GAAGH,IAAI,EAAE;EACrC,OAAOhB,wBAAwB,CAAC,CAC5Ba,6BAA6B,EAC7BI,sBAAsB,EACtB;IACIH,OAAO,EAAEF,sBAAsB;IAC/BG,UAAU,EAAEG,kBAAkB,CAACC,EAAE,CAAC;IAClCqB,KAAK,EAAE,IAAI;IACXxB,IAAI,EAAE,CACFlB,MAAM,EACNC,QAAQ,EACRE,sBAAsB,EACtB,GAAGe,IAAI;EAEf,CAAC,CACJ,CAAC;AACN;;AAEA;AACA,MAAMxC,SAAS,gBAAG0B,SAAS,CAACzB,WAAW,EAAE,IAAI,CAAC;AAC9C,MAAML,MAAM,gBAAG8B,SAAS,CAAC7B,QAAQ,EAAE,IAAI,CAAC;AACxC,MAAMH,OAAO,gBAAGgC,SAAS,CAAC/B,SAAS,EAAE,IAAI,CAAC;AAC1C,MAAMO,aAAa,gBAAGwB,SAAS,CAACvB,eAAe,EAAE,IAAI,CAAC;AACtD,MAAMC,mBAAmB,gBAAGsB,SAAS,CAACrB,qBAAqB,EAAE,IAAI,CAAC;AAClE,MAAMC,KAAK,gBAAGoB,SAAS,CAACnB,OAAO,EAAE,IAAI,CAAC;AACtC,MAAMT,eAAe,gBAAG4B,SAAS,CAAC3B,iBAAiB,EAAE,IAAI,CAAC;AAC1D,MAAMS,WAAW,gBAAGkB,SAAS,CAACjB,aAAa,EAAE,IAAI,CAAC;;AAElD;AACA;AACA;;AAEA,SAASkB,WAAW,EAAEwB,iBAAiB,EAAErB,YAAY,EAAE9B,SAAS,EAAE+B,YAAY,EAAEnC,MAAM,EAAEF,OAAO,EAAEQ,aAAa,EAAEE,mBAAmB,EAAEE,KAAK,EAAEyD,kBAAkB,EAAEjE,eAAe,EAAEU,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}