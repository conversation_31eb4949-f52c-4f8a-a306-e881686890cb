{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nconst _c0 = (a0, a1, a2) => ({\n  \"slim px-2 py-1\": a0,\n  \"px-2 py-2\": a1,\n  \"is-read\": a2\n});\nconst _c1 = a0 => ({\n  \"is-read\": a0\n});\nfunction NotificationRowItemComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 1)(2, \"div\", 2)(3, \"img\", 3);\n    i0.ɵɵlistener(\"error\", function NotificationRowItemComponent_ng_container_0_Template_img_error_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onNotificationIconError($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 4)(7, \"i\", 5);\n    i0.ɵɵlistener(\"click\", function NotificationRowItemComponent_ng_container_0_Template_i_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRemoveNotification(ctx_r1.notification.id));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(6, _c0, ctx_r1.slimStyle, !ctx_r1.slimStyle, !ctx_r1.notification.isRead));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.getNotifIcon(ctx_r1.notification.type), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r1.textSizeClass);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.notification.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.slimStyle ? \"hidden\" : \"flex\");\n  }\n}\nfunction NotificationRowItemComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 6)(2, \"div\", 7)(3, \"img\", 8);\n    i0.ɵɵlistener(\"error\", function NotificationRowItemComponent_ng_container_1_Template_img_error_3_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onNotificationIconError($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 9);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 10)(10, \"i\", 11);\n    i0.ɵɵlistener(\"click\", function NotificationRowItemComponent_ng_container_1_Template_i_click_10_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onRemoveNotification(ctx_r1.notification.id));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c1, ctx_r1.notification.isRead));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.getNotifIcon(ctx_r1.notification.type), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.notification.description, \".\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(8, 4, ctx_r1.notification.createdAt, \"dd/MM/yyyy h:mm a\"), \"\");\n  }\n}\nexport class NotificationRowItemComponent {\n  constructor() {\n    this.notification = {};\n    this.slimStyle = false;\n    this.textSizeClass = 'font-sm';\n    this.removeNotification = new EventEmitter();\n  }\n  ngOnInit() {}\n  getNotifIcon(type) {\n    return '/assets/icons/notifications/' + type + '.svg';\n  }\n  onNotificationIconError(event) {\n    const imgElement = event.target;\n    imgElement.src = '/assets/icons/notifications/notif-check.svg';\n  }\n  onRemoveNotification(notificationId) {\n    this.removeNotification.emit(notificationId);\n  }\n  static #_ = this.ɵfac = function NotificationRowItemComponent_Factory(t) {\n    return new (t || NotificationRowItemComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NotificationRowItemComponent,\n    selectors: [[\"app-notification-row-item\"]],\n    inputs: {\n      notification: \"notification\",\n      slimStyle: \"slimStyle\",\n      textSizeClass: \"textSizeClass\"\n    },\n    outputs: {\n      removeNotification: \"removeNotification\"\n    },\n    decls: 2,\n    vars: 2,\n    consts: [[4, \"ngIf\"], [1, \"notification-item-wrapper\", \"flex\", \"align-items-center\", \"flex-wrap\", \"mb-1\", \"text-md\", \"text-primary\", 3, \"ngClass\"], [1, \"w-full\", \"lg:w-11\", \"text-sm\", \"font-medium\", \"main-text\", \"flex\", \"align-items-center\", \"gap-3\"], [1, \"pl-2\", 2, \"max-height\", \"20px\", 3, \"error\", \"src\"], [1, \"w-full\", \"lg:w-1\", \"justify-content-end\", \"text-primary\", 3, \"ngClass\"], [1, \"pi\", \"pi-trash\", \"font-bold\", 3, \"click\"], [1, \"notification-item-wrapper\", \"flex\", \"align-items-center\", \"justify-content-between\", \"py-1\", \"px-3\", \"border-top-1\", \"surface-border\", \"flex-wrap\", \"mb-2\", \"font-sm\", 3, \"ngClass\"], [1, \"w-full\", \"lg:w-7\", \"main-text\", \"flex\", \"align-items-center\", \"gap-2\"], [1, \"w-1rem\", 3, \"error\", \"src\"], [1, \"w-full\", \"lg:w-3\", \"lg:flex-order-0\", \"flex-order-1\", \"date\", \"justify-content-center\", \"font-xs\"], [1, \"w-full\", \"lg:w-1\", \"flex\", \"justify-content-end\", \"text-primary\"], [\"title\", \"Remove Notification\", 1, \"pi\", \"pi-trash\", \"pointer\", 3, \"click\"]],\n    template: function NotificationRowItemComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NotificationRowItemComponent_ng_container_0_Template, 8, 10, \"ng-container\", 0)(1, NotificationRowItemComponent_ng_container_1_Template, 11, 9, \"ng-container\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.slimStyle);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.slimStyle);\n      }\n    },\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"],\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "NotificationRowItemComponent_ng_container_0_Template_img_error_3_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "onNotificationIconError", "ɵɵelementEnd", "ɵɵtext", "NotificationRowItemComponent_ng_container_0_Template_i_click_7_listener", "onRemoveNotification", "notification", "id", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction3", "_c0", "slimStyle", "isRead", "getNotifIcon", "type", "ɵɵsanitizeUrl", "ɵɵclassMap", "textSizeClass", "ɵɵtextInterpolate", "description", "NotificationRowItemComponent_ng_container_1_Template_img_error_3_listener", "_r3", "NotificationRowItemComponent_ng_container_1_Template_i_click_10_listener", "ɵɵpureFunction1", "_c1", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "createdAt", "NotificationRowItemComponent", "constructor", "removeNotification", "ngOnInit", "event", "imgElement", "target", "src", "notificationId", "emit", "_", "_2", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "NotificationRowItemComponent_Template", "rf", "ctx", "ɵɵtemplate", "NotificationRowItemComponent_ng_container_0_Template", "NotificationRowItemComponent_ng_container_1_Template"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\notification\\notification-row-item\\notification-row-item.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\notification\\notification-row-item\\notification-row-item.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\r\nimport { Notification } from 'src/app/core/models/notification.model';\r\n\r\n@Component({\r\n  selector: 'app-notification-row-item',\r\n  templateUrl: './notification-row-item.component.html',\r\n  styleUrls: ['./notification-row-item.component.scss'],\r\n  changeDetection: ChangeDetectionStrategy.OnPush\r\n})\r\nexport class NotificationRowItemComponent implements OnInit {\r\n  @Input() notification = {} as Notification;\r\n  @Input() slimStyle = false;\r\n  @Input() textSizeClass = 'font-sm';\r\n  @Output() removeNotification = new EventEmitter<number>();\r\n  constructor() { }\r\n\r\n  ngOnInit(): void {\r\n  }\r\n\r\n  getNotifIcon(type: string) {\r\n    return '/assets/icons/notifications/' + type + '.svg';\r\n  }\r\n\r\n  onNotificationIconError(event: Event) {\r\n    const imgElement = event.target as HTMLImageElement;\r\n    imgElement.src = '/assets/icons/notifications/notif-check.svg';\r\n  }\r\n\r\n  onRemoveNotification(notificationId: number) {\r\n    this.removeNotification.emit(notificationId);\r\n  }\r\n}\r\n", "<ng-container *ngIf=\"slimStyle\">\r\n    <li class=\"notification-item-wrapper flex align-items-center  \r\n                flex-wrap mb-1 text-md text-primary\"\r\n        [ngClass]=\"{'slim px-2 py-1': slimStyle, 'px-2 py-2': !slimStyle, 'is-read': !notification.isRead}\">\r\n        <div class=\" w-full lg:w-11 text-sm font-medium main-text flex align-items-center gap-3\">\r\n            <img [src]=\"getNotifIcon(notification.type)\" class=\"pl-2\" (error)=\"onNotificationIconError($event)\"\r\n                style=\"max-height: 20px;\" />\r\n            <span [class]=\"textSizeClass\">{{notification.description}}</span>\r\n        </div>\r\n        <!-- <div class=\"w-full lg:w-2 lg:flex-order-0 flex-order-1 date\">{{notif.createdAt |\r\n                    date:'dd/MM/yyyy h:mm a'}}</div> -->\r\n        <div class=\"w-full lg:w-1  justify-content-end text-primary\" [ngClass]=\"slimStyle ? 'hidden' : 'flex'\">\r\n            <i class=\"pi pi-trash font-bold\" (click)=\"onRemoveNotification(notification.id!)\"></i>\r\n        </div>\r\n    </li>\r\n</ng-container>\r\n\r\n<ng-container *ngIf=\"!slimStyle\">\r\n    <li class=\"notification-item-wrapper flex align-items-center justify-content-between py-1 px-3 border-top-1 surface-border flex-wrap mb-2 font-sm\"\r\n        [ngClass]=\"{'is-read': notification.isRead}\">\r\n        <div class=\" w-full lg:w-7 main-text flex align-items-center gap-2\">\r\n            <img [src]=\"getNotifIcon(notification.type)\" (error)=\"onNotificationIconError($event)\" class=\"w-1rem\" />\r\n            <span>{{notification.description}}.</span>\r\n        </div>\r\n        <div class=\"w-full lg:w-3 lg:flex-order-0 flex-order-1 date justify-content-center font-xs\">\r\n            {{notification.createdAt |\r\n            date:'dd/MM/yyyy h:mm a'}}</div>\r\n        <div class=\"w-full lg:w-1 flex justify-content-end text-primary\">\r\n            <i class=\"pi pi-trash pointer\" title=\"Remove Notification\"\r\n                (click)=\"onRemoveNotification(notification.id)\"></i>\r\n        </div>\r\n    </li>\r\n</ng-container>"], "mappings": "AAAA,SAA6CA,YAAY,QAA+B,eAAe;;;;;;;;;;;;;ICAvGC,EAAA,CAAAC,uBAAA,GAAgC;IAKpBD,EAJR,CAAAE,cAAA,YAEwG,aACX,aAErD;IAD0BF,EAAA,CAAAG,UAAA,mBAAAC,0EAAAC,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,uBAAA,CAAAN,MAAA,CAA+B;IAAA,EAAC;IAAnGL,EAAA,CAAAY,YAAA,EACgC;IAChCZ,EAAA,CAAAE,cAAA,WAA8B;IAAAF,EAAA,CAAAa,MAAA,GAA4B;IAC9Db,EAD8D,CAAAY,YAAA,EAAO,EAC/D;IAIFZ,EADJ,CAAAE,cAAA,aAAuG,WACjB;IAAjDF,EAAA,CAAAG,UAAA,mBAAAW,wEAAA;MAAAd,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAO,oBAAA,CAAAP,MAAA,CAAAQ,YAAA,CAAAC,EAAA,CAAsC;IAAA,EAAC;IAEzFjB,EAF0F,CAAAY,YAAA,EAAI,EACpF,EACL;;;;;IAXDZ,EAAA,CAAAkB,SAAA,EAAmG;IAAnGlB,EAAA,CAAAmB,UAAA,YAAAnB,EAAA,CAAAoB,eAAA,IAAAC,GAAA,EAAAb,MAAA,CAAAc,SAAA,GAAAd,MAAA,CAAAc,SAAA,GAAAd,MAAA,CAAAQ,YAAA,CAAAO,MAAA,EAAmG;IAE1FvB,EAAA,CAAAkB,SAAA,GAAuC;IAAvClB,EAAA,CAAAmB,UAAA,QAAAX,MAAA,CAAAgB,YAAA,CAAAhB,MAAA,CAAAQ,YAAA,CAAAS,IAAA,GAAAzB,EAAA,CAAA0B,aAAA,CAAuC;IAEtC1B,EAAA,CAAAkB,SAAA,EAAuB;IAAvBlB,EAAA,CAAA2B,UAAA,CAAAnB,MAAA,CAAAoB,aAAA,CAAuB;IAAC5B,EAAA,CAAAkB,SAAA,EAA4B;IAA5BlB,EAAA,CAAA6B,iBAAA,CAAArB,MAAA,CAAAQ,YAAA,CAAAc,WAAA,CAA4B;IAID9B,EAAA,CAAAkB,SAAA,EAAyC;IAAzClB,EAAA,CAAAmB,UAAA,YAAAX,MAAA,CAAAc,SAAA,qBAAyC;;;;;;IAM9GtB,EAAA,CAAAC,uBAAA,GAAiC;IAIrBD,EAHR,CAAAE,cAAA,YACiD,aACuB,aACwC;IAA3DF,EAAA,CAAAG,UAAA,mBAAA4B,0EAAA1B,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAA0B,GAAA;MAAA,MAAAxB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,uBAAA,CAAAN,MAAA,CAA+B;IAAA,EAAC;IAAtFL,EAAA,CAAAY,YAAA,EAAwG;IACxGZ,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAa,MAAA,GAA6B;IACvCb,EADuC,CAAAY,YAAA,EAAO,EACxC;IACNZ,EAAA,CAAAE,cAAA,aAA4F;IACxFF,EAAA,CAAAa,MAAA,GAC0B;;IAAAb,EAAA,CAAAY,YAAA,EAAM;IAEhCZ,EADJ,CAAAE,cAAA,cAAiE,aAET;IAAhDF,EAAA,CAAAG,UAAA,mBAAA8B,yEAAA;MAAAjC,EAAA,CAAAM,aAAA,CAAA0B,GAAA;MAAA,MAAAxB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAO,oBAAA,CAAAP,MAAA,CAAAQ,YAAA,CAAAC,EAAA,CAAqC;IAAA,EAAC;IAE3DjB,EAF4D,CAAAY,YAAA,EAAI,EACtD,EACL;;;;;IAZDZ,EAAA,CAAAkB,SAAA,EAA4C;IAA5ClB,EAAA,CAAAmB,UAAA,YAAAnB,EAAA,CAAAkC,eAAA,IAAAC,GAAA,EAAA3B,MAAA,CAAAQ,YAAA,CAAAO,MAAA,EAA4C;IAEnCvB,EAAA,CAAAkB,SAAA,GAAuC;IAAvClB,EAAA,CAAAmB,UAAA,QAAAX,MAAA,CAAAgB,YAAA,CAAAhB,MAAA,CAAAQ,YAAA,CAAAS,IAAA,GAAAzB,EAAA,CAAA0B,aAAA,CAAuC;IACtC1B,EAAA,CAAAkB,SAAA,GAA6B;IAA7BlB,EAAA,CAAAoC,kBAAA,KAAA5B,MAAA,CAAAQ,YAAA,CAAAc,WAAA,MAA6B;IAGnC9B,EAAA,CAAAkB,SAAA,GAC0B;IAD1BlB,EAAA,CAAAoC,kBAAA,MAAApC,EAAA,CAAAqC,WAAA,OAAA7B,MAAA,CAAAQ,YAAA,CAAAsB,SAAA,2BAC0B;;;ADjBtC,OAAM,MAAOC,4BAA4B;EAKvCC,YAAA;IAJS,KAAAxB,YAAY,GAAG,EAAkB;IACjC,KAAAM,SAAS,GAAG,KAAK;IACjB,KAAAM,aAAa,GAAG,SAAS;IACxB,KAAAa,kBAAkB,GAAG,IAAI1C,YAAY,EAAU;EACzC;EAEhB2C,QAAQA,CAAA,GACR;EAEAlB,YAAYA,CAACC,IAAY;IACvB,OAAO,8BAA8B,GAAGA,IAAI,GAAG,MAAM;EACvD;EAEAd,uBAAuBA,CAACgC,KAAY;IAClC,MAAMC,UAAU,GAAGD,KAAK,CAACE,MAA0B;IACnDD,UAAU,CAACE,GAAG,GAAG,6CAA6C;EAChE;EAEA/B,oBAAoBA,CAACgC,cAAsB;IACzC,IAAI,CAACN,kBAAkB,CAACO,IAAI,CAACD,cAAc,CAAC;EAC9C;EAAC,QAAAE,CAAA,G;qBArBUV,4BAA4B;EAAA;EAAA,QAAAW,EAAA,G;UAA5BX,4BAA4B;IAAAY,SAAA;IAAAC,MAAA;MAAApC,YAAA;MAAAM,SAAA;MAAAM,aAAA;IAAA;IAAAyB,OAAA;MAAAZ,kBAAA;IAAA;IAAAa,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCQzC3D,EAjBA,CAAA6D,UAAA,IAAAC,oDAAA,2BAAgC,IAAAC,oDAAA,2BAiBC;;;QAjBlB/D,EAAA,CAAAmB,UAAA,SAAAyC,GAAA,CAAAtC,SAAA,CAAe;QAiBftB,EAAA,CAAAkB,SAAA,EAAgB;QAAhBlB,EAAA,CAAAmB,UAAA,UAAAyC,GAAA,CAAAtC,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}