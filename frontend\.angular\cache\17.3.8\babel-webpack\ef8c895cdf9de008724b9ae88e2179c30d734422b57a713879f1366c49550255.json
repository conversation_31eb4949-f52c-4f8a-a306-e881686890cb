{"ast": null, "code": "import { EventEmitter, inject } from '@angular/core';\nimport { HttpEventType, HttpHeaders } from '@angular/common/http';\nimport { environment } from 'src/environments/environment';\nimport { SubSink } from 'subsink';\nimport { DomSanitizer } from '@angular/platform-browser';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"src/app/core/services/user.service\";\nimport * as i3 from \"src/app/core/services/library.service\";\nimport * as i4 from \"src/app/core/services/toast.service\";\nimport * as i5 from \"src/app/core/services/auth.service\";\nimport * as i6 from \"src/app/core/services/general.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"ngx-image-cropper\";\nimport * as i9 from \"primeng/button\";\nimport * as i10 from \"primeng/ripple\";\nimport * as i11 from \"../../core/directives/dnd.directive\";\nimport * as i12 from \"./choose-tags/choose-tags.component\";\nfunction UploadFilesComponent_div_0_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"img\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r1.fileDropImageWidth ? ctx_r1.fileDropImageWidth : 100, \"%\");\n    i0.ɵɵproperty(\"src\", ctx_r1.fileDropImage, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction UploadFilesComponent_div_0_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 13);\n  }\n}\nfunction UploadFilesComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 7);\n    i0.ɵɵlistener(\"fileDropped\", function UploadFilesComponent_div_0_Template_div_fileDropped_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFileDropped($event));\n    });\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 8);\n    i0.ɵɵlistener(\"change\", function UploadFilesComponent_div_0_Template_input_change_4_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.fileBrowseHandler($event.target.files));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, UploadFilesComponent_div_0_ng_container_5_Template, 2, 3, \"ng-container\", 9)(6, UploadFilesComponent_div_0_ng_template_6_Template, 1, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(8, \"div\", 10);\n    i0.ɵɵtext(9, \" Drag and drop files here or \");\n    i0.ɵɵelementStart(10, \"label\", 11);\n    i0.ɵɵtext(11, \"Browse\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const defaultImage_r3 = i0.ɵɵreference(7);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.title);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"id\", ctx_r1.title);\n    i0.ɵɵproperty(\"multiple\", ctx_r1.multiple);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.fileDropImage)(\"ngIfElse\", defaultImage_r3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵpropertyInterpolate(\"for\", ctx_r1.title);\n  }\n}\nfunction UploadFilesComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\")(2, \"input\", 8);\n    i0.ɵɵlistener(\"change\", function UploadFilesComponent_div_1_Template_input_change_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.fileBrowseHandler($event.target.files));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 14);\n    i0.ɵɵtext(4, \"Browse\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"id\", ctx_r1.title);\n    i0.ɵɵproperty(\"multiple\", ctx_r1.multiple);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"for\", ctx_r1.title);\n  }\n}\nfunction UploadFilesComponent_div_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"img\", 22);\n    i0.ɵɵtext(2, \"No file chosen\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UploadFilesComponent_div_2_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 23);\n    i0.ɵɵelement(2, \"div\")(3, \"img\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 19);\n    i0.ɵɵlistener(\"change\", function UploadFilesComponent_div_2_ng_container_10_Template_input_change_4_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.fileChangeEvent($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"image-cropper\", 25);\n    i0.ɵɵlistener(\"imageCropped\", function UploadFilesComponent_div_2_ng_container_10_Template_image_cropper_imageCropped_5_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.imageCropped($event));\n    })(\"imageLoaded\", function UploadFilesComponent_div_2_ng_container_10_Template_image_cropper_imageLoaded_5_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.imageLoaded($event));\n    })(\"cropperReady\", function UploadFilesComponent_div_2_ng_container_10_Template_image_cropper_cropperReady_5_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.cropperReady());\n    })(\"loadImageFailed\", function UploadFilesComponent_div_2_ng_container_10_Template_image_cropper_loadImageFailed_5_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.loadImageFailed());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.croppedImage, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"imageChangedEvent\", ctx_r1.imageChangedEvent)(\"maintainAspectRatio\", true)(\"aspectRatio\", 4 / 3)(\"aspectRatio\", 1 / 1)(\"resizeToWidth\", 256)(\"cropperMinWidth\", 128);\n  }\n}\nfunction UploadFilesComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 15)(2, \"div\", 16)(3, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function UploadFilesComponent_div_2_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const filePicker_r6 = i0.ɵɵreference(8);\n      return i0.ɵɵresetView(filePicker_r6.click());\n    });\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Choose file\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"img\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"input\", 19, 1);\n    i0.ɵɵlistener(\"change\", function UploadFilesComponent_div_2_Template_input_change_7_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.fileBrowseHandlerProfile($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, UploadFilesComponent_div_2_div_9_Template, 3, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, UploadFilesComponent_div_2_ng_container_10_Template, 6, 7, \"ng-container\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.croppedImage === \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showCropper);\n  }\n}\nfunction UploadFilesComponent_div_3_div_1_div_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"img\", 37);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r1.fileAfterImageWidth ? ctx_r1.fileAfterImageWidth : null, \"%\");\n    i0.ɵɵproperty(\"src\", ctx_r1.fileAfterImage, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction UploadFilesComponent_div_3_div_1_div_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵelement(1, \"img\", 39);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UploadFilesComponent_div_3_div_1_div_1_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r9 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.formatBytes(file_r9 == null ? null : file_r9.file.size), \" \");\n  }\n}\nfunction UploadFilesComponent_div_3_div_1_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵelement(1, \"div\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r9 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", file_r9.progress + \"%\");\n  }\n}\nfunction UploadFilesComponent_div_3_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtemplate(1, UploadFilesComponent_div_3_div_1_div_1_ng_container_1_Template, 2, 3, \"ng-container\", 9)(2, UploadFilesComponent_div_3_div_1_div_1_ng_template_2_Template, 2, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(4, \"div\", 30)(5, \"p\", 31)(6, \"a\", 32);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, UploadFilesComponent_div_3_div_1_div_1_p_8_Template, 2, 1, \"p\", 33)(9, UploadFilesComponent_div_3_div_1_div_1_div_9_Template, 2, 2, \"div\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 35);\n    i0.ɵɵlistener(\"click\", function UploadFilesComponent_div_3_div_1_div_1_Template_div_click_10_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const i_r10 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.deleteFile(i_r10));\n    });\n    i0.ɵɵelement(11, \"i\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const defaultAfterImage_r11 = i0.ɵɵreference(3);\n    const file_r9 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.fileAfterImage)(\"ngIfElse\", defaultAfterImage_r11);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"href\", ctx_r1.generalService.getDomainFileNamePath(file_r9 == null ? null : file_r9.file.name), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.generalService.getFileNameAndExtension(file_r9 == null ? null : file_r9.file.name), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", file_r9.file.size);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", file_r9.progress);\n  }\n}\nfunction UploadFilesComponent_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, UploadFilesComponent_div_3_div_1_div_1_Template, 12, 6, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", file_r9.file.name);\n  }\n}\nfunction UploadFilesComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtemplate(1, UploadFilesComponent_div_3_div_1_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.files);\n  }\n}\nfunction UploadFilesComponent_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"div\", 45)(3, \"div\", 46)(4, \"strong\");\n    i0.ɵɵtext(5, \"Tags\\u00A0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" apply to all \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"app-choose-tags\", 47);\n    i0.ɵɵlistener(\"chooseTag\", function UploadFilesComponent_div_4_div_1_Template_app_choose_tags_chooseTag_7_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onChooseTag($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" You have selected \", ctx_r1.files.length, \" files \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"applyToAll\", true);\n  }\n}\nfunction UploadFilesComponent_div_4_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49);\n    i0.ɵɵelement(2, \"img\", 39);\n    i0.ɵɵelementStart(3, \"label\", 50);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(5, \"hr\");\n    i0.ɵɵelementStart(6, \"app-choose-tags\", 51);\n    i0.ɵɵlistener(\"chooseTag\", function UploadFilesComponent_div_4_div_2_Template_app_choose_tags_chooseTag_6_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onChooseTag($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r14 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", file_r14.file.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"applyToAll\", false)(\"file\", file_r14);\n  }\n}\nfunction UploadFilesComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtemplate(1, UploadFilesComponent_div_4_div_1_Template, 8, 2, \"div\", 3)(2, UploadFilesComponent_div_4_div_2_Template, 7, 3, \"div\", 44);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.files.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.files);\n  }\n}\nfunction UploadFilesComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function UploadFilesComponent_div_5_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.submit());\n    });\n    i0.ɵɵelementStart(2, \"div\", 54)(3, \"span\", 55);\n    i0.ɵɵtext(4, \"Upload\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"img\", 56);\n    i0.ɵɵelementEnd()()();\n  }\n}\nconst MAX_FILE_SIZE_BYTES = 25 * 1024 * 1024; // 25 MB in bytes\nexport class UploadFilesComponent {\n  constructor(http, userService, libraryService, toast, authService, generalService) {\n    this.http = http;\n    this.userService = userService;\n    this.libraryService = libraryService;\n    this.toast = toast;\n    this.authService = authService;\n    this.generalService = generalService;\n    this.isProfile = false;\n    this.isTeacherApplication = false;\n    this.isSick = false;\n    this.files = [];\n    this.teacherFilesLength = 0;\n    this.postUrl = \"\";\n    this.title = \"\";\n    this.buttonUI = false;\n    this.dndUI = false;\n    this.checkboxUI = false;\n    this.multiple = true;\n    this.showSubmitButton = false;\n    this.inHomeworkTask = false;\n    this.homeworkTaskStudentId = \"\";\n    this.clearFiles = false;\n    this.onUploadFinished = new EventEmitter();\n    this.onDeleteFileClicked = new EventEmitter();\n    this.tagChoosen = new EventEmitter();\n    this.onSubmit = new EventEmitter();\n    this.subs = new SubSink();\n    this.progress = [];\n    this.message = \"\";\n    this.showCropper = false;\n    this.croppedImage = \"\";\n    this.profileFile = {};\n    this.profileFileName = \"\";\n    this.uploadedFile = \"\";\n    this.sanitizer = inject(DomSanitizer);\n    this.uploadFile = files => {\n      console.log(files);\n      if (this.isProfile && !files.name) {\n        this.onUploadFinished.emit({\n          filePath: \"\",\n          levels: [],\n          categories: []\n        });\n        return;\n      }\n      if (!this.isProfile && files.length === 0) {\n        return;\n      }\n      if (!this.isProfile && !this.isTeacherApplication) {\n        for (const file of files) {\n          let myFile = {\n            file: file,\n            progress: 0,\n            index: this.teacherFilesLength\n          };\n          this.teacherFilesLength++;\n          // this.files.push(myFile);\n          this.userService.setNewFileUpload(myFile);\n        }\n      }\n      if (!this.isProfile && this.isTeacherApplication) {\n        for (const file of files) {\n          let myFile = {\n            file: file,\n            progress: 0,\n            index: this.teacherFilesLength\n          };\n          // this.teacherFilesLength++;\n          // this.files.push(myFile);\n          // this.userService.setNewFileUpload(myFile);\n          this.upload(myFile);\n        }\n      } else {\n        let myFile = {\n          file: files,\n          progress: 0,\n          index: this.teacherFilesLength\n        };\n        this.upload(myFile);\n      }\n    };\n  }\n  ngOnInit() {\n    // this.uploadFile([{lastModified: 1680183216555,\n    //   lastModifiedDate\n    //   : \n    //   'Thu Mar 30 2023 16:33:36 GMT+0300 (Eastern European Summer Time)',\n    //   name  :  \"calendar (-41.png\",\n    //   size: 323,\n    //   type : \"image/png\",\n    //   webkitRelativePath: \"\"}])\n    // this is happening because of cropped image. we cannot imidiatly upload it until the user presses save at profile info component\n    this.subs.sink = this.userService.updateProfile.subscribe(res => {\n      if (res) {\n        console.log(this.profileFile);\n        this.uploadFile(this.profileFile);\n      }\n    });\n    this.subs.sink = this.userService.uploadFiles.subscribe(res => {\n      if (res) {\n        this.files = [];\n      }\n    });\n    this.subs.sink = this.userService.newFileUpload.subscribe(res => {\n      if (res && !this.generalService.isObjectEmpty(res)) {\n        console.log(res);\n        this.files.push(res);\n        this.upload(res);\n      }\n    });\n  }\n  ngOnDestroy() {\n    //when we land to this cocmponent the value must be false so it wont get called in ngOnInit\n    this.userService.setUpdateProfile(false);\n    this.subs.unsubscribe();\n  }\n  /**\n   * on file drop handler\n   */\n  onFileDropped($event) {\n    this.uploadFile($event);\n  }\n  /**\n   * Delete file from files list\n   * @param index (File index)\n   */\n  deleteFile(index) {\n    this.onDeleteFileClicked.emit({\n      file: this.files[index]\n    });\n    this.files.splice(index, 1);\n  }\n  /**\n   * handle file from browsing\n   */\n  fileBrowseHandlerProfile(event) {\n    this.profileFileName = event.target.files[0].name;\n    this.showCropper = true;\n    this.imageChangedEvent = event;\n    // document.getElementById('cropped-img')!.style.borderColor = \"var(--main-color)\";\n  }\n  /**\n   * handle file from browsing\n   */\n  fileBrowseHandler(event) {\n    console.log(event);\n    this.uploadFile(event);\n  }\n  upload(myFile) {\n    if (myFile.file.size && myFile.file.size > MAX_FILE_SIZE_BYTES) {\n      this.toast.setShowToastmessage({\n        severity: 'error',\n        summary: '',\n        detail: 'The file size must be less than 25 MB.'\n      });\n      return;\n    }\n    let headers;\n    const formData = new FormData();\n    formData.append('file', myFile.file, this.replaceSpecialCharactersForFilename(myFile.file.name));\n    headers = new HttpHeaders({\n      'documentType': myFile.file.name.split('.').pop(),\n      \"Authorization\": \"Bearer \" + this.authService.getToken()\n    });\n    console.log(headers);\n    if (this.assignToClassroom) {\n      for (let user of this.assignToClassroom.users) {\n        let endpointExtras = this.inHomeworkTask ? \"?studentId=\" + user.id : \"\";\n        this.uploadRequest(headers, endpointExtras, formData, myFile);\n      }\n    } else if (this.assignToUser) {\n      let endpointExtras = \"?studentId=\" + this.assignToUser.id;\n      console.log(endpointExtras);\n      this.uploadRequest(headers, endpointExtras, formData, myFile);\n    } else {\n      let endpointExtras = this.inHomeworkTask ? \"?studentId=\" + this.homeworkTaskStudentId : \"\";\n      this.uploadRequest(headers, endpointExtras, formData, myFile);\n    }\n  }\n  uploadRequest(headers, endpointExtras, formData, myFile) {\n    this.subs.add(this.http.post(environment.apiUrl + this.postUrl + endpointExtras, formData, {\n      headers: headers,\n      reportProgress: true,\n      observe: 'events'\n    }).subscribe(event => {\n      console.log(event);\n      if (event.type === HttpEventType.UploadProgress) {\n        myFile.progress = Math.round(100 * event.loaded / event.total);\n      } else if (event.type === HttpEventType.Response) {\n        this.toast.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Uploaded successfully!'\n        });\n        this.uploadedFile = event.body;\n        if (this.isProfile) {\n          console.log('GEGEG');\n          this.showCropper = false;\n        }\n        this.onUploadFinished.emit({\n          filePath: event.body,\n          task: this.task\n        });\n      } else {}\n    }, err => {\n      console.log(err);\n    }));\n  }\n  formatBytes(bytes, decimals) {\n    if (bytes === 0) {\n      return '0 Bytes';\n    }\n    const k = 1024;\n    const dm = decimals <= 0 ? 0 : decimals || 2;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\n  }\n  onChooseTag(event) {\n    this.tagChoosen.emit(event);\n  }\n  imageCropped(event) {\n    console.log(event);\n    this.croppedImage = this.sanitizer.bypassSecurityTrustUrl(event.objectUrl || event.base64 || '');\n    const file = new File([event.blob], this.profileFileName, {\n      type: event.blob.type\n    });\n    this.profileFile = new File([file], this.profileFileName);\n    const reader = new FileReader();\n    reader.onload = () => {\n      this.croppedImage = reader.result;\n    };\n    reader.readAsDataURL(file);\n  }\n  imageLoaded(event) {\n    console.log(event);\n    // show cropper\n  }\n  cropperReady(event) {\n    // cropper ready\n  }\n  loadImageFailed() {\n    // show message\n  }\n  submit() {\n    this.onSubmit.emit();\n  }\n  /**\n   * Replaces special characters in a given filename with a specified replacement character.\n   * @param {string} fileName - The original filename.\n   * @returns {string} - The modified filename with special characters replaced.\n   */\n  replaceSpecialCharactersForFilename(fileName) {\n    const specialCharacters = /[^\\w\\s.-]/g;\n    const replacementCharacter = \"_\";\n    return fileName.replace(specialCharacters, replacementCharacter);\n  }\n  static #_ = this.ɵfac = function UploadFilesComponent_Factory(t) {\n    return new (t || UploadFilesComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.LibraryService), i0.ɵɵdirectiveInject(i4.ToastService), i0.ɵɵdirectiveInject(i5.AuthService), i0.ɵɵdirectiveInject(i6.GeneralService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: UploadFilesComponent,\n    selectors: [[\"app-upload-files\"]],\n    inputs: {\n      isProfile: \"isProfile\",\n      isTeacherApplication: \"isTeacherApplication\",\n      isSick: \"isSick\",\n      files: \"files\",\n      teacherFilesLength: \"teacherFilesLength\",\n      postUrl: \"postUrl\",\n      title: \"title\",\n      buttonUI: \"buttonUI\",\n      dndUI: \"dndUI\",\n      checkboxUI: \"checkboxUI\",\n      multiple: \"multiple\",\n      showSubmitButton: \"showSubmitButton\",\n      inHomeworkTask: \"inHomeworkTask\",\n      homeworkTaskStudentId: \"homeworkTaskStudentId\",\n      assignToClassroom: \"assignToClassroom\",\n      assignToUser: \"assignToUser\",\n      task: \"task\",\n      filePath: \"filePath\",\n      fileDropImage: \"fileDropImage\",\n      fileDropImageWidth: \"fileDropImageWidth\",\n      fileAfterImage: \"fileAfterImage\",\n      fileAfterImageWidth: \"fileAfterImageWidth\",\n      clearFiles: \"clearFiles\"\n    },\n    outputs: {\n      onUploadFinished: \"onUploadFinished\",\n      onDeleteFileClicked: \"onDeleteFileClicked\",\n      tagChoosen: \"tagChoosen\",\n      onSubmit: \"onSubmit\"\n    },\n    decls: 6,\n    vars: 6,\n    consts: [[\"defaultImage\", \"\"], [\"filePicker\", \"\"], [\"defaultAfterImage\", \"\"], [4, \"ngIf\"], [\"class\", \"files-list lg:mt-2\", 4, \"ngIf\"], [\"class\", \"library-uploads\", 4, \"ngIf\"], [\"class\", \"flex justify-content-center align-items-center mt-2\", 4, \"ngIf\"], [\"appDnd\", \"\", 1, \"files-container\", 3, \"fileDropped\"], [\"type\", \"file\", 3, \"change\", \"id\", \"multiple\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"drop-text-subtitle\"], [3, \"for\"], [3, \"src\"], [\"src\", \"/assets/icons/drop.svg\", 2, \"width\", \"60%\"], [1, \"light-purple-button\", 2, \"color\", \"white !important\", 3, \"for\"], [1, \"flex\", \"align-items-center\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-outlined\", \"p-button-rounded\", \"gradient-input-like-btn\", \"no-label\", \"text-center\", \"flex\", \"align-items-center\", \"justify-content-center\", \"px-6\", 3, \"click\"], [\"src\", \"/assets/icons/profile-upload-icon.svg\", \"height\", \"20\", 1, \"pl-3\"], [\"type\", \"file\", 3, \"change\"], [\"class\", \"flex align-items-center z-1\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"z-1\"], [\"src\", \"/assets/icons/warning-sign.svg\", \"height\", \"24\", 1, \"pr-3\"], [1, \"flex\", \"flex-row\", \"align-items-center\"], [\"id\", \"cropped-img\", 1, \"cropped-img\", 2, \"border-color\", \"var(--primary-color)\", 3, \"src\"], [\"format\", \"png\", 3, \"imageCropped\", \"imageLoaded\", \"cropperReady\", \"loadImageFailed\", \"imageChangedEvent\", \"maintainAspectRatio\", \"aspectRatio\", \"resizeToWidth\", \"cropperMinWidth\"], [1, \"files-list\", \"lg:mt-2\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"single-file section-bg-gradient flex\", 4, \"ngIf\"], [1, \"single-file\", \"section-bg-gradient\", \"flex\"], [1, \"info\"], [1, \"name\"], [\"target\", \"_blank\", 1, \"text-primary\", 3, \"href\"], [\"class\", \"size\", 4, \"ngIf\"], [\"class\", \"progress-cont\", 4, \"ngIf\"], [1, \"delete\", 3, \"click\"], [1, \"pi\", \"pi-times-circle\", \"text-xl\", \"text-primary\"], [1, \"mr-2\", 3, \"src\"], [1, \"file-icon\", 2, \"width\", \"50px\"], [\"src\", \"/assets/icons/library/surface1.svg\"], [1, \"size\"], [1, \"progress-cont\"], [1, \"progress\"], [1, \"library-uploads\"], [\"class\", \"library-upload p-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"library-upload\"], [1, \"font-sm\", \"p-3\"], [3, \"chooseTag\", \"applyToAll\"], [1, \"library-upload\", \"p-3\"], [1, \"library-upload-title\"], [1, \"font-sm\", \"text-800\", \"in-title\", \"p-2\"], [3, \"chooseTag\", \"applyToAll\", \"file\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"mt-2\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", 1, \"p-button\", \"p-button-sm\", \"p-button-rounded\", \"link-main-color-active\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"px-3\"], [1, \"text-white\", \"py-1\"], [\"src\", \"/assets/icons/library/upload-file-lib.svg\", \"width\", \"26\", 1, \"pl-2\"]],\n    template: function UploadFilesComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, UploadFilesComponent_div_0_Template, 12, 6, \"div\", 3)(1, UploadFilesComponent_div_1_Template, 5, 3, \"div\", 3)(2, UploadFilesComponent_div_2_Template, 11, 2, \"div\", 3)(3, UploadFilesComponent_div_3_Template, 2, 1, \"div\", 4)(4, UploadFilesComponent_div_4_Template, 3, 2, \"div\", 5)(5, UploadFilesComponent_div_5_Template, 6, 0, \"div\", 6);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.dndUI);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isSick);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isProfile);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.dndUI);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.checkboxUI);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showSubmitButton);\n      }\n    },\n    dependencies: [i7.NgForOf, i7.NgIf, i8.ImageCropperComponent, i9.ButtonDirective, i10.Ripple, i11.DndDirective, i12.ChooseTagsComponent],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.modal[_ngcontent-%COMP%] {\\n  min-width: 50vw;\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown:not(.p-disabled):hover {\\n  border: 2px solid rgb(228, 185, 84);\\n}\\n\\n  .no-label > .p-button-label {\\n  display: none;\\n}\\n\\ninput[type=file][_ngcontent-%COMP%] {\\n  visibility: hidden;\\n  width: 10px;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%] {\\n  position: relative;\\n  padding: 10px;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%]::-webkit-calendar-picker-indicator {\\n  color: transparent;\\n  background: none;\\n  z-index: 1;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%]:before {\\n  color: transparent;\\n  background: none;\\n  display: block;\\n  font-family: \\\"FontAwesome\\\";\\n  content: \\\"\\\\f073\\\";\\n  \\n\\n  width: 20px;\\n  height: 25px;\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  color: #999;\\n}\\n\\n.image-preview[_ngcontent-%COMP%] {\\n  height: 10rem;\\n  margin: 1rem 0;\\n  border-radius: 50%;\\n  border: 3px solid var(--main-color);\\n}\\n\\n.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 100%;\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-header {\\n  padding: clamp(0.38rem, 0.47vw + 0.28rem, 0.75rem);\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-items .p-dropdown-item {\\n  margin-bottom: clamp(0.38rem, 0.47vw + 0.28rem, 0.75rem);\\n}\\n\\n.country-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: clamp(0.75rem, 0.31vw + 0.69rem, 1rem);\\n  font-size: clamp(0.88rem, 0.16vw + 0.84rem, 1rem);\\n}\\n.country-item[_ngcontent-%COMP%]   .country-name[_ngcontent-%COMP%] {\\n  white-space: pre-line; \\n\\n}\\n.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  \\n\\n\\n\\n\\n\\n  \\n\\n  display: inline-block;\\n  vertical-align: middle;\\n}\\n.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  clip-path: circle();\\n}\\n.country-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 32px;\\n  margin-right: 0.5rem;\\n}\\n.country-item[_ngcontent-%COMP%]   img.smaller[_ngcontent-%COMP%] {\\n  width: 16px;\\n}\\n\\n.info-element[_ngcontent-%COMP%] {\\n  padding: 10px 0;\\n}\\n\\n.responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.files-container[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n  border-radius: 12px;\\n  text-align: center;\\n  position: relative;\\n  background: rgba(242, 246, 255, 0.68);\\n  background-image: repeating-linear-gradient(to right, rgba(52, 131, 226, 0.61) 0%, rgba(52, 131, 226, 0.61) 50%, transparent 50%, transparent 100%), repeating-linear-gradient(to right, rgba(52, 131, 226, 0.61) 0%, rgba(52, 131, 226, 0.61) 50%, transparent 50%, transparent 100%), repeating-linear-gradient(to bottom, rgba(52, 131, 226, 0.61) 0%, rgba(52, 131, 226, 0.61) 50%, transparent 50%, transparent 100%), repeating-linear-gradient(to bottom, rgba(52, 131, 226, 0.61) 0%, rgba(52, 131, 226, 0.61) 50%, transparent 50%, transparent 100%);\\n  background-position: left top, left bottom, left top, right top;\\n  background-repeat: repeat-x, repeat-x, repeat-y, repeat-y;\\n  background-size: 14px 2px, 14px 2px, 2px 12px, 2px 14px;\\n}\\n.files-container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  position: absolute;\\n  z-index: 2;\\n  width: 100%;\\n  height: 100%;\\n  top: 0;\\n  left: 0;\\n}\\n.files-container[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 600;\\n  color: #38424c;\\n}\\n.files-container[_ngcontent-%COMP%]   .drop-text-subtitle[_ngcontent-%COMP%] {\\n  margin-top: 12px;\\n  font-size: 16px;\\n  font-weight: 500;\\n  font-stretch: normal;\\n  font-style: normal;\\n  line-height: normal;\\n  letter-spacing: 1.5px;\\n  text-align: left;\\n  color: #000;\\n  text-align: center;\\n}\\n\\nlabel[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n  font-weight: bold;\\n  cursor: pointer;\\n}\\n\\n.fileover[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_shake 1s;\\n  animation-iteration-count: infinite;\\n}\\n\\n.single-file[_ngcontent-%COMP%] {\\n  display: flex;\\n  padding: 0.5rem;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1rem;\\n  flex-grow: 1;\\n  word-break: break-all;\\n}\\n.single-file[_ngcontent-%COMP%]   .delete[_ngcontent-%COMP%] {\\n  display: flex;\\n  cursor: pointer;\\n  align-self: flex-start;\\n}\\n.single-file[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  color: #353f4a;\\n  margin: 0;\\n}\\n.single-file[_ngcontent-%COMP%]   .size[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 500;\\n  color: #a4a4a4;\\n  margin: 0;\\n  margin-bottom: 0.25rem;\\n}\\n.single-file[_ngcontent-%COMP%]   .info[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.progress-cont[_ngcontent-%COMP%] {\\n  height: 7px;\\n  width: 100%;\\n  border-radius: 4px;\\n  background-color: #d0d0d0;\\n  position: relative;\\n}\\n.progress-cont[_ngcontent-%COMP%]   .progress[_ngcontent-%COMP%] {\\n  width: 0;\\n  height: 100%;\\n  position: absolute;\\n  z-index: 1;\\n  top: 0;\\n  left: 0;\\n  border-radius: 4px;\\n  background-color: #4c97cb;\\n  transition: 0.5s all;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_shake {\\n  0% {\\n    transform: translate(1px, 1px) rotate(0deg);\\n  }\\n  10% {\\n    transform: translate(-1px, -2px) rotate(-1deg);\\n  }\\n  20% {\\n    transform: translate(-3px, 0px) rotate(1deg);\\n  }\\n  30% {\\n    transform: translate(3px, 2px) rotate(0deg);\\n  }\\n  40% {\\n    transform: translate(1px, -1px) rotate(1deg);\\n  }\\n  50% {\\n    transform: translate(-1px, 2px) rotate(-1deg);\\n  }\\n  60% {\\n    transform: translate(-3px, 1px) rotate(0deg);\\n  }\\n  70% {\\n    transform: translate(3px, 1px) rotate(-1deg);\\n  }\\n  80% {\\n    transform: translate(-1px, -1px) rotate(1deg);\\n  }\\n  90% {\\n    transform: translate(1px, 2px) rotate(0deg);\\n  }\\n  100% {\\n    transform: translate(1px, -2px) rotate(-1deg);\\n  }\\n}\\n.upload[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  color: var(--light-purple);\\n  line-height: 36px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-top: 15px;\\n}\\n\\n.item-img[_ngcontent-%COMP%] {\\n  margin-left: 15px;\\n}\\n\\n.spinner[_ngcontent-%COMP%] {\\n  position: absolute;\\n  margin-left: 10px;\\n}\\n\\n[_nghost-%COMP%]     .mat-progress-spinner circle, [_nghost-%COMP%]     .mat-spinner circle {\\n  stroke: var(--light-purple);\\n}\\n\\n.cropped-img[_ngcontent-%COMP%] {\\n  border: 3px solid white;\\n  width: 200px;\\n  border-radius: 50%;\\n}\\n\\n@media screen and (max-width: 1366px) {\\n  .cropped-img[_ngcontent-%COMP%] {\\n    border: 3px solid white;\\n    width: 200px;\\n    border-radius: 50%;\\n    position: absolute;\\n    right: 0;\\n  }\\n  .test[_ngcontent-%COMP%] {\\n    width: 50% !important;\\n  }\\n}\\n@media screen and (max-width: 768px) {\\n  .cropped-img[_ngcontent-%COMP%] {\\n    width: 100px;\\n  }\\n}\\n.white-button[_ngcontent-%COMP%] {\\n  border: 1px solid var(--my-gray);\\n  background-image: linear-gradient(#ffffff, #e3ebfd);\\n  border-radius: 50px;\\n  text-align: center;\\n  padding: 10px;\\n  width: 220px;\\n}\\n\\n.gradient-input-like-btn[_ngcontent-%COMP%] {\\n  border: none;\\n  background-image: linear-gradient(#ffffff, #e3ebfd);\\n  box-shadow: none;\\n}\\n\\n.library-uploads[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  flex-direction: column;\\n}\\n.library-uploads[_ngcontent-%COMP%]   .library-upload[_ngcontent-%COMP%] {\\n  border-radius: 32px;\\n  border: 1px solid #2E3D90;\\n  margin-top: 15px;\\n  width: 100%;\\n}\\n.library-uploads[_ngcontent-%COMP%]   .library-upload[_ngcontent-%COMP%]   .library-upload-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 18px;\\n}\\n.library-uploads[_ngcontent-%COMP%]   .library-upload[_ngcontent-%COMP%]   .library-upload-title[_ngcontent-%COMP%]   .in-title[_ngcontent-%COMP%] {\\n  border-radius: 20px;\\n  background: #fff;\\n}\\n.library-uploads[_ngcontent-%COMP%]   .library-upload[_ngcontent-%COMP%]   .library-upload-title[_ngcontent-%COMP%]    > img[_ngcontent-%COMP%] {\\n  width: 30px;\\n  margin-right: 15px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "inject", "HttpEventType", "HttpHeaders", "environment", "SubSink", "Dom<PERSON><PERSON><PERSON>zer", "i0", "ɵɵelementContainerStart", "ɵɵelement", "ɵɵadvance", "ɵɵstyleProp", "ctx_r1", "fileDropImageWidth", "ɵɵproperty", "fileDropImage", "ɵɵsanitizeUrl", "ɵɵelementStart", "ɵɵlistener", "UploadFilesComponent_div_0_Template_div_fileDropped_1_listener", "$event", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "onFileDropped", "ɵɵtext", "ɵɵelementEnd", "UploadFilesComponent_div_0_Template_input_change_4_listener", "fileBrowseHandler", "target", "files", "ɵɵtemplate", "UploadFilesComponent_div_0_ng_container_5_Template", "UploadFilesComponent_div_0_ng_template_6_Template", "ɵɵtemplateRefExtractor", "ɵɵtextInterpolate", "title", "ɵɵpropertyInterpolate", "multiple", "defaultImage_r3", "UploadFilesComponent_div_1_Template_input_change_2_listener", "_r4", "UploadFilesComponent_div_2_ng_container_10_Template_input_change_4_listener", "_r7", "fileChangeEvent", "UploadFilesComponent_div_2_ng_container_10_Template_image_cropper_imageCropped_5_listener", "imageCropped", "UploadFilesComponent_div_2_ng_container_10_Template_image_cropper_imageLoaded_5_listener", "imageLoaded", "UploadFilesComponent_div_2_ng_container_10_Template_image_cropper_cropperReady_5_listener", "cropperReady", "UploadFilesComponent_div_2_ng_container_10_Template_image_cropper_loadImageFailed_5_listener", "loadImageFailed", "croppedImage", "imageChangedEvent", "UploadFilesComponent_div_2_Template_button_click_3_listener", "_r5", "filePicker_r6", "ɵɵreference", "click", "UploadFilesComponent_div_2_Template_input_change_7_listener", "fileBrowseHandlerProfile", "UploadFilesComponent_div_2_div_9_Template", "UploadFilesComponent_div_2_ng_container_10_Template", "showCropper", "fileAfterImageWidth", "fileAfterImage", "ɵɵtextInterpolate1", "formatBytes", "file_r9", "file", "size", "progress", "UploadFilesComponent_div_3_div_1_div_1_ng_container_1_Template", "UploadFilesComponent_div_3_div_1_div_1_ng_template_2_Template", "UploadFilesComponent_div_3_div_1_div_1_p_8_Template", "UploadFilesComponent_div_3_div_1_div_1_div_9_Template", "UploadFilesComponent_div_3_div_1_div_1_Template_div_click_10_listener", "_r8", "i_r10", "index", "deleteFile", "defaultAfterImage_r11", "generalService", "getDomainFileNamePath", "name", "getFileNameAndExtension", "UploadFilesComponent_div_3_div_1_div_1_Template", "UploadFilesComponent_div_3_div_1_Template", "UploadFilesComponent_div_4_div_1_Template_app_choose_tags_chooseTag_7_listener", "_r12", "onChooseTag", "length", "UploadFilesComponent_div_4_div_2_Template_app_choose_tags_chooseTag_6_listener", "_r13", "file_r14", "UploadFilesComponent_div_4_div_1_Template", "UploadFilesComponent_div_4_div_2_Template", "UploadFilesComponent_div_5_Template_button_click_1_listener", "_r15", "submit", "MAX_FILE_SIZE_BYTES", "UploadFilesComponent", "constructor", "http", "userService", "libraryService", "toast", "authService", "isProfile", "isTeacherApplication", "isSick", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "postUrl", "buttonUI", "dndUI", "checkboxUI", "showSubmitButton", "inHomeworkTask", "homeworkTaskStudentId", "clearFiles", "onUploadFinished", "onDeleteFileClicked", "tagChoosen", "onSubmit", "subs", "message", "profileFile", "profileFileName", "uploadedFile", "sanitizer", "uploadFile", "console", "log", "emit", "filePath", "levels", "categories", "myFile", "setNewFileUpload", "upload", "ngOnInit", "sink", "updateProfile", "subscribe", "res", "uploadFiles", "newFileUpload", "isObjectEmpty", "push", "ngOnDestroy", "setUpdateProfile", "unsubscribe", "splice", "event", "setShowToastmessage", "severity", "summary", "detail", "headers", "formData", "FormData", "append", "replaceSpecialCharactersForFilename", "split", "pop", "getToken", "assignToClassroom", "user", "users", "endpointExtras", "id", "uploadRequest", "assignToUser", "add", "post", "apiUrl", "reportProgress", "observe", "type", "UploadProgress", "Math", "round", "loaded", "total", "Response", "body", "task", "err", "bytes", "decimals", "k", "dm", "sizes", "i", "floor", "parseFloat", "pow", "toFixed", "bypassSecurityTrustUrl", "objectUrl", "base64", "File", "blob", "reader", "FileReader", "onload", "result", "readAsDataURL", "fileName", "specialCharacters", "replacementCharacter", "replace", "_", "ɵɵdirectiveInject", "i1", "HttpClient", "i2", "UserService", "i3", "LibraryService", "i4", "ToastService", "i5", "AuthService", "i6", "GeneralService", "_2", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "UploadFilesComponent_Template", "rf", "ctx", "UploadFilesComponent_div_0_Template", "UploadFilesComponent_div_1_Template", "UploadFilesComponent_div_2_Template", "UploadFilesComponent_div_3_Template", "UploadFilesComponent_div_4_Template", "UploadFilesComponent_div_5_Template"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\upload-files\\upload-files.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\upload-files\\upload-files.component.html"], "sourcesContent": ["import { Component, OnInit, Output, EventEmitter, Input, inject } from '@angular/core';\r\nimport { HttpEventType, HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { environment } from 'src/environments/environment';\r\nimport { base64ToFile, ImageCroppedEvent, ImageCropperComponent } from 'ngx-image-cropper';\r\nimport { UserService } from 'src/app/core/services/user.service';\r\nimport { SubSink } from 'subsink';\r\nimport { LibraryService } from 'src/app/core/services/library.service';\r\nimport { FocusTrap } from '@angular/cdk/a11y';\r\nimport { Homework, HomeworkTask } from 'src/app/core/models/homework.model';\r\nimport { Classroom } from 'src/app/core/models/classroom.model';\r\nimport { User } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { DomSanitizer } from '@angular/platform-browser';\r\n\r\nconst MAX_FILE_SIZE_BYTES = 25 * 1024 * 1024; // 25 MB in bytes\r\n\r\n\r\nexport interface MyFile {\r\n  file: File,\r\n  progress: number,\r\n  index: number\r\n}\r\n@Component({\r\n  selector: 'app-upload-files',\r\n  templateUrl: './upload-files.component.html',\r\n  styleUrls: ['./upload-files.component.scss']\r\n})\r\nexport class UploadFilesComponent implements OnInit {\r\n  @Input() isProfile?: boolean = false;\r\n  @Input() isTeacherApplication?: boolean = false;\r\n  @Input() isSick?: boolean = false;\r\n  @Input() files: any[] = [];\r\n  @Input() teacherFilesLength: number = 0;\r\n  @Input() postUrl: string = \"\";\r\n  @Input() title: string = \"\";\r\n  @Input() buttonUI: boolean = false;\r\n  @Input() dndUI: boolean = false;\r\n  @Input() checkboxUI: boolean = false;\r\n  @Input() multiple: boolean = true;\r\n  @Input() showSubmitButton: boolean = false;\r\n  @Input() inHomeworkTask: boolean = false;\r\n  @Input() homeworkTaskStudentId: string = \"\";\r\n  @Input() assignToClassroom?: Classroom;\r\n  @Input() assignToUser?: User;\r\n  @Input() task?: HomeworkTask;\r\n  @Input() filePath?: string;\r\n  @Input() fileDropImage?: string;\r\n  @Input() fileDropImageWidth?: number;\r\n  @Input() fileAfterImage?: string;\r\n  @Input() fileAfterImageWidth?: string;\r\n  @Input() clearFiles: boolean = false;\r\n  @Output() onUploadFinished = new EventEmitter();\r\n  @Output() onDeleteFileClicked = new EventEmitter();\r\n  @Output() tagChoosen = new EventEmitter();\r\n  @Output() onSubmit = new EventEmitter();\r\n  private subs = new SubSink();\r\n  public progress: number[] = [];\r\n  public message: string = \"\";\r\n  public showCropper = false;\r\n  imageChangedEvent: any;\r\n  croppedImage: any = \"\";\r\n  profileFile: File = {} as File;\r\n  profileFileName: string = \"\";\r\n  uploadedFile: any = \"\";\r\n  sanitizer = inject(DomSanitizer);\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n    private userService: UserService,\r\n    private libraryService: LibraryService,\r\n    private toast: ToastService,\r\n    private authService: AuthService,\r\n    public generalService: GeneralService,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    // this.uploadFile([{lastModified: 1680183216555,\r\n    //   lastModifiedDate\r\n    //   : \r\n    //   'Thu Mar 30 2023 16:33:36 GMT+0300 (Eastern European Summer Time)',\r\n    //   name  :  \"calendar (-41.png\",\r\n    //   size: 323,\r\n    //   type : \"image/png\",\r\n    //   webkitRelativePath: \"\"}])\r\n    // this is happening because of cropped image. we cannot imidiatly upload it until the user presses save at profile info component\r\n    this.subs.sink = this.userService.updateProfile.subscribe(res => {\r\n      if (res) {\r\n        console.log(this.profileFile)\r\n        this.uploadFile(this.profileFile);\r\n      }\r\n    })\r\n    this.subs.sink = this.userService.uploadFiles.subscribe(res => {\r\n      if (res) {\r\n        this.files = [];\r\n      }\r\n    })\r\n    this.subs.sink = this.userService.newFileUpload.subscribe(res => {\r\n      if (res && !this.generalService.isObjectEmpty(res)) {\r\n        console.log(res);\r\n        this.files.push(res);\r\n        this.upload(res as MyFile);\r\n      }\r\n    })\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    //when we land to this cocmponent the value must be false so it wont get called in ngOnInit\r\n    this.userService.setUpdateProfile(false);\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  /**\r\n   * on file drop handler\r\n   */\r\n  onFileDropped($event: any) {\r\n    this.uploadFile($event);\r\n  }\r\n\r\n  /**\r\n   * Delete file from files list\r\n   * @param index (File index)\r\n   */\r\n  deleteFile(index: number) {\r\n    this.onDeleteFileClicked.emit({ file: this.files[index] });\r\n    this.files.splice(index, 1);\r\n  }\r\n\r\n  /**\r\n   * handle file from browsing\r\n   */\r\n  fileBrowseHandlerProfile(event: any) {\r\n    this.profileFileName = event.target.files[0].name;\r\n    this.showCropper = true;\r\n    this.imageChangedEvent = event;\r\n    // document.getElementById('cropped-img')!.style.borderColor = \"var(--main-color)\";\r\n  }\r\n\r\n  /**\r\n   * handle file from browsing\r\n   */\r\n  fileBrowseHandler(event: any) {\r\n    console.log(event);\r\n    this.uploadFile(event);\r\n  }\r\n\r\n  public uploadFile = (files: any) => {\r\n    console.log(files);\r\n\r\n\r\n    if (this.isProfile && !files.name) {\r\n      this.onUploadFinished.emit({ filePath: \"\", levels: [], categories: [] });\r\n      return;\r\n    }\r\n    if (!this.isProfile && files.length === 0) {\r\n      return;\r\n    }\r\n    if (!this.isProfile && !this.isTeacherApplication) {\r\n      for (const file of files) {\r\n        let myFile: MyFile = {\r\n          file: file,\r\n          progress: 0,\r\n          index: this.teacherFilesLength\r\n        }\r\n        this.teacherFilesLength++;\r\n        // this.files.push(myFile);\r\n        this.userService.setNewFileUpload(myFile);\r\n      }\r\n    }\r\n    if (!this.isProfile && this.isTeacherApplication) {\r\n      for (const file of files) {\r\n        let myFile: MyFile = {\r\n          file: file,\r\n          progress: 0,\r\n          index: this.teacherFilesLength\r\n        }\r\n        // this.teacherFilesLength++;\r\n        // this.files.push(myFile);\r\n        // this.userService.setNewFileUpload(myFile);\r\n        this.upload(myFile)\r\n      }\r\n    } else {\r\n      let myFile: MyFile = {\r\n        file: files,\r\n        progress: 0,\r\n        index: this.teacherFilesLength\r\n      }\r\n      this.upload(myFile)\r\n    }\r\n  }\r\n\r\n  upload(myFile: MyFile) {\r\n\r\n    if (myFile.file.size && myFile.file.size > MAX_FILE_SIZE_BYTES) {\r\n      this.toast.setShowToastmessage({\r\n        severity: 'error',\r\n        summary: '',\r\n        detail: 'The file size must be less than 25 MB.',\r\n      });\r\n      return;\r\n    }\r\n\r\n    let headers: any;\r\n    const formData = new FormData();\r\n    formData.append('file', myFile.file, this.replaceSpecialCharactersForFilename(myFile.file.name));\r\n    headers = new HttpHeaders({\r\n      'documentType': myFile.file.name.split('.').pop()!,\r\n      \"Authorization\": \"Bearer \" + this.authService.getToken(),\r\n    });\r\n\r\n\r\n    console.log(headers);\r\n    if (this.assignToClassroom) {\r\n      for (let user of this.assignToClassroom.users) {\r\n        let endpointExtras = this.inHomeworkTask ? \"?studentId=\" + user.id : \"\";\r\n        this.uploadRequest(headers, endpointExtras, formData, myFile)\r\n      }\r\n    } else if (this.assignToUser) {\r\n      let endpointExtras = \"?studentId=\" + this.assignToUser.id!;\r\n      console.log(endpointExtras)\r\n      this.uploadRequest(headers, endpointExtras, formData, myFile)\r\n    } else {\r\n      let endpointExtras = this.inHomeworkTask ? \"?studentId=\" + this.homeworkTaskStudentId : \"\";\r\n      this.uploadRequest(headers, endpointExtras, formData, myFile)\r\n    }\r\n\r\n  }\r\n\r\n  uploadRequest(headers: HttpHeaders, endpointExtras: string, formData: FormData, myFile: MyFile) {\r\n    this.subs.add(this.http.post(environment.apiUrl + this.postUrl + endpointExtras, formData, {\r\n      headers: headers,\r\n      reportProgress: true,\r\n      observe: 'events'\r\n    })\r\n      .subscribe(event => {\r\n        console.log(event)\r\n        if (event.type === HttpEventType.UploadProgress) {\r\n          myFile.progress = Math.round(100 * event.loaded / event.total!);\r\n        }\r\n        else if (event.type === HttpEventType.Response) {\r\n\r\n          this.toast.setShowToastmessage({\r\n            severity: 'success',\r\n            summary: '',\r\n            detail: 'Uploaded successfully!'\r\n          });\r\n          this.uploadedFile = event.body;\r\n          if (this.isProfile) {\r\n            console.log('GEGEG');\r\n            this.showCropper = false;\r\n          }\r\n          this.onUploadFinished.emit({ filePath: event.body, task: this.task });\r\n        } else {\r\n        }\r\n      }, err => {\r\n        console.log(err)\r\n      }));\r\n  }\r\n\r\n  formatBytes(bytes: any, decimals: any) {\r\n    if (bytes === 0) {\r\n      return '0 Bytes';\r\n    }\r\n    const k = 1024;\r\n    const dm = decimals <= 0 ? 0 : decimals || 2;\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\r\n  }\r\n\r\n  onChooseTag(event: any) {\r\n    this.tagChoosen.emit(event);\r\n  }\r\n\r\n  imageCropped(event: ImageCroppedEvent) {\r\n    console.log(event);\r\n    this.croppedImage = this.sanitizer.bypassSecurityTrustUrl(event.objectUrl || event.base64 || '');\r\n    const file = new File([event.blob!], this.profileFileName, { type: event.blob!.type });\r\n    this.profileFile = new File([file], this.profileFileName);\r\n    const reader = new FileReader();\r\n    reader.onload = () => {//async code, will take a while\r\n      this.croppedImage = reader.result as string;\r\n    };\r\n    reader.readAsDataURL(file);\r\n  }\r\n\r\n  imageLoaded(event: any) {\r\n    console.log(event);\r\n    // show cropper\r\n  }\r\n\r\n  cropperReady(event: any) {\r\n    // cropper ready\r\n  }\r\n\r\n  loadImageFailed() {\r\n    // show message\r\n  }\r\n\r\n  submit() {\r\n    this.onSubmit.emit();\r\n  }\r\n\r\n  /**\r\n   * Replaces special characters in a given filename with a specified replacement character.\r\n   * @param {string} fileName - The original filename.\r\n   * @returns {string} - The modified filename with special characters replaced.\r\n   */\r\n  private replaceSpecialCharactersForFilename(fileName: string): string {\r\n    const specialCharacters = /[^\\w\\s.-]/g;\r\n    const replacementCharacter = \"_\";\r\n\r\n    return fileName.replace(specialCharacters, replacementCharacter);\r\n  }\r\n\r\n}", "<div *ngIf=\"dndUI\">\r\n    <div class=\"files-container\" appDnd (fileDropped)=\"onFileDropped($event)\">\r\n        <div>{{title}}</div>\r\n        <input type=\"file\" id=\"{{title}}\" (change)=\"fileBrowseHandler($event.target.files)\" [multiple]=\"multiple\" />\r\n        <ng-container *ngIf=\"fileDropImage else defaultImage\" >\r\n            <img [src]=\"fileDropImage\" [style.width.%]=\"fileDropImageWidth ? fileDropImageWidth : 100\">\r\n        </ng-container>\r\n        <ng-template #defaultImage>\r\n            <img src=\"/assets/icons/drop.svg\" style=\"width:60%;\">\r\n        </ng-template>\r\n        <div class=\"drop-text-subtitle\">\r\n            Drag and drop files here or\r\n            <label for=\"{{title}}\">Browse</label>\r\n        </div>\r\n    </div>\r\n</div>\r\n\r\n<div *ngIf=\"isSick\">\r\n    <div>\r\n        <input type=\"file\" id=\"{{title}}\" (change)=\"file<PERSON>rowseHandler($event.target.files)\" [multiple]=\"multiple\" />\r\n        <label for=\"{{title}}\" class=\"light-purple-button\" style=\"color:white !important;\">Browse</label>\r\n    </div>\r\n</div>\r\n\r\n<div *ngIf=\"isProfile\">\r\n    <div class=\"flex align-items-center\">\r\n\r\n    <div class=\"flex align-items-center justify-content-center\">\r\n          <button pButton type=\"button\" \r\n          (click)=\"filePicker.click()\"\r\n          class=\"p-button-outlined p-button-rounded gradient-input-like-btn no-label text-center flex align-items-center justify-content-center px-6\">\r\n            <span>Choose file</span> <img src=\"/assets/icons/profile-upload-icon.svg\" class=\"pl-3\" height=\"20\" />\r\n        </button></div>\r\n    <input type=\"file\" #filePicker (change)=\"fileBrowseHandlerProfile($event)\" />\r\n    <div *ngIf=\"croppedImage === ''\" class=\"flex align-items-center z-1\"><img src=\"/assets/icons/warning-sign.svg\" class=\"pr-3\" height=\"24\" />No file chosen</div>\r\n    </div>\r\n    <ng-container *ngIf=\"showCropper\">\r\n    <div class=\"flex flex-row align-items-center\">\r\n        <div>\r\n    <!-- <image-cropper [hidden]=\"croppedImage === ''\" [imageChangedEvent]=\"imageChangedEvent\" [maintainAspectRatio]=\"true\"\r\n        [containWithinAspectRatio]=\"false\" [aspectRatio]=\"1 / 1\" [resizeToWidth]=\"256\" [cropperMinWidth]=\"128\"\r\n        [onlyScaleDown]=\"true\" [roundCropper]=\"false\" [alignImage]=\"'left'\" format=\"png\"\r\n        (imageCropped)=\"imageCropped($event)\" (imageLoaded)=\"imageLoaded()\" (cropperReady)=\"cropperReady($event)\"\r\n        (loadImageFailed)=\"loadImageFailed()\" class=\"test\">\r\n    </image-cropper> -->\r\n</div>\r\n    <img id=\"cropped-img\" class=\"cropped-img\" [src]=\"croppedImage\" style=\"border-color: var(--primary-color)\" />\r\n</div>\r\n\r\n<input type=\"file\" (change)=\"fileChangeEvent($event)\" />\r\n\r\n<image-cropper\r\n    [imageChangedEvent]=\"imageChangedEvent\"\r\n    [maintainAspectRatio]=\"true\"\r\n    [aspectRatio]=\"4 / 3\"\r\n    [aspectRatio]=\"1 / 1\" [resizeToWidth]=\"256\" [cropperMinWidth]=\"128\"\r\n    format=\"png\"\r\n    (imageCropped)=\"imageCropped($event)\"\r\n    (imageLoaded)=\"imageLoaded($event)\"\r\n    (cropperReady)=\"cropperReady()\"\r\n    (loadImageFailed)=\"loadImageFailed()\"\r\n></image-cropper>\r\n\r\n</ng-container>\r\n</div>\r\n\r\n<!-- <div *ngIf=\"buttonUI\">\r\n    <input type=\"file\" id=\"a{{title}}\" (change)=\"fileBrowseHandler($event.target.files)\" [multiple]=\"multiple\" />\r\n    <label *ngIf=\"uploadedFile===''\" for=\"a{{title}}\">\r\n        Upload File\r\n    </label>\r\n    <div *ngIf=\"uploadedFile!==''\">\r\n        {{uploadedFile}}\r\n    </div>\r\n</div> -->\r\n\r\n<div *ngIf=\"dndUI\" class=\"files-list lg:mt-2\">\r\n    <div *ngFor=\"let file of files; let i = index\">\r\n        <div *ngIf=\"file.file.name\" class=\"single-file section-bg-gradient flex\">\r\n            <ng-container *ngIf=\"fileAfterImage else defaultAfterImage\" >\r\n            <img class=\"mr-2\" [style.width.%]=\"fileAfterImageWidth ? fileAfterImageWidth : null\" [src]=\"fileAfterImage\" />\r\n            </ng-container>\r\n            \r\n        <ng-template #defaultAfterImage>\r\n            <div class=\"file-icon\" style=\"width: 50px\">\r\n                <img src=\"/assets/icons/library/surface1.svg\">\r\n            </div>\r\n        </ng-template>\r\n            <!--  -->\r\n            <div class=\"info\">\r\n                <p class=\"name\">\r\n                    <a class=\"text-primary\" [href]=\"this.generalService.getDomainFileNamePath(file?.file.name)\" target=\"_blank\">\r\n                \r\n                        {{ this.generalService.getFileNameAndExtension(file?.file.name) }}\r\n                      </a>\r\n                </p>\r\n                <p class=\"size\" *ngIf=\"file.file.size\" >\r\n                    {{ formatBytes(file?.file.size) }}\r\n                </p>\r\n                <div class=\"progress-cont\" *ngIf=\"file.progress\">\r\n                    <div class=\"progress\" [style.width]=\"file.progress + '%'\">\r\n                    </div>\r\n                </div>\r\n\r\n            </div>\r\n\r\n            <div class=\"delete\" (click)=\"deleteFile(i)\">\r\n                <i class=\"pi pi-times-circle text-xl text-primary\"></i>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n\r\n<div *ngIf=\"checkboxUI\" class=\"library-uploads\">\r\n    <div *ngIf=\"files.length > 1\">\r\n        You have selected {{files.length}} files\r\n        <div class=\"library-upload\">\r\n            <div class=\"font-sm p-3\">\r\n                <strong>Tags&nbsp;</strong> apply to all\r\n            </div>\r\n            <!-- <hr> -->\r\n            <app-choose-tags [applyToAll]=\"true\" (chooseTag)=\"onChooseTag($event)\"></app-choose-tags>\r\n        </div>\r\n    </div>\r\n    <div *ngFor=\"let file of files\" class=\"library-upload p-3\">\r\n        <div class=\"library-upload-title\">\r\n            <img src=\"/assets/icons/library/surface1.svg\">\r\n            <label class=\"font-sm text-800 in-title p-2\">\r\n                {{file.file.name}}\r\n            </label>\r\n        </div>\r\n        <hr>\r\n        <app-choose-tags [applyToAll]=\"false\" [file]=\"file\" (chooseTag)=\"onChooseTag($event)\"></app-choose-tags>\r\n    </div>\r\n</div>\r\n\r\n<div *ngIf=\"showSubmitButton\" class=\"flex justify-content-center align-items-center mt-2\">\r\n    <button pButton pRipple (click)=\"submit()\" type=\"button\"\r\n        class=\"p-button p-button-sm p-button-rounded link-main-color-active\">\r\n        <div class=\"flex align-items-center px-3\">\r\n            <span class=\"text-white py-1\">Upload</span>\r\n            <img class=\"contact-icon-img\" src=\"/assets/icons/library/upload-file-lib.svg\" width=\"26\" class=\"pl-2\" />\r\n        </div>\r\n    </button>\r\n</div>"], "mappings": "AAAA,SAAoCA,YAAY,EAASC,MAAM,QAAQ,eAAe;AACtF,SAASC,aAAa,EAAcC,WAAW,QAAQ,sBAAsB;AAC7E,SAASC,WAAW,QAAQ,8BAA8B;AAG1D,SAASC,OAAO,QAAQ,SAAS;AASjC,SAASC,YAAY,QAAQ,2BAA2B;;;;;;;;;;;;;;;;ICVhDC,EAAA,CAAAC,uBAAA,GAAuD;IACnDD,EAAA,CAAAE,SAAA,cAA2F;;;;;IAAhEF,EAAA,CAAAG,SAAA,EAA+D;IAA/DH,EAAA,CAAAI,WAAA,UAAAC,MAAA,CAAAC,kBAAA,GAAAD,MAAA,CAAAC,kBAAA,YAA+D;IAArFN,EAAA,CAAAO,UAAA,QAAAF,MAAA,CAAAG,aAAA,EAAAR,EAAA,CAAAS,aAAA,CAAqB;;;;;IAG1BT,EAAA,CAAAE,SAAA,cAAqD;;;;;;IAP7DF,EADJ,CAAAU,cAAA,UAAmB,aAC2D;IAAtCV,EAAA,CAAAW,UAAA,yBAAAC,+DAAAC,MAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAL,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAeZ,MAAA,CAAAa,aAAA,CAAAL,MAAA,CAAqB;IAAA,EAAC;IACrEb,EAAA,CAAAU,cAAA,UAAK;IAAAV,EAAA,CAAAmB,MAAA,GAAS;IAAAnB,EAAA,CAAAoB,YAAA,EAAM;IACpBpB,EAAA,CAAAU,cAAA,eAA4G;IAA1EV,EAAA,CAAAW,UAAA,oBAAAU,4DAAAR,MAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAL,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAUZ,MAAA,CAAAiB,iBAAA,CAAAT,MAAA,CAAAU,MAAA,CAAAC,KAAA,CAAsC;IAAA,EAAC;IAAnFxB,EAAA,CAAAoB,YAAA,EAA4G;IAI5GpB,EAHA,CAAAyB,UAAA,IAAAC,kDAAA,0BAAuD,IAAAC,iDAAA,gCAAA3B,EAAA,CAAA4B,sBAAA,CAG5B;IAG3B5B,EAAA,CAAAU,cAAA,cAAgC;IAC5BV,EAAA,CAAAmB,MAAA,oCACA;IAAAnB,EAAA,CAAAU,cAAA,iBAAuB;IAAAV,EAAA,CAAAmB,MAAA,cAAM;IAGzCnB,EAHyC,CAAAoB,YAAA,EAAQ,EACnC,EACJ,EACJ;;;;;IAbOpB,EAAA,CAAAG,SAAA,GAAS;IAATH,EAAA,CAAA6B,iBAAA,CAAAxB,MAAA,CAAAyB,KAAA,CAAS;IACK9B,EAAA,CAAAG,SAAA,EAAc;IAAdH,EAAA,CAAA+B,qBAAA,OAAA1B,MAAA,CAAAyB,KAAA,CAAc;IAAmD9B,EAAA,CAAAO,UAAA,aAAAF,MAAA,CAAA2B,QAAA,CAAqB;IAC1FhC,EAAA,CAAAG,SAAA,EAAoB;IAAAH,EAApB,CAAAO,UAAA,SAAAF,MAAA,CAAAG,aAAA,CAAoB,aAAAyB,eAAA,CAAiB;IAQzCjC,EAAA,CAAAG,SAAA,GAAe;IAAfH,EAAA,CAAA+B,qBAAA,QAAA1B,MAAA,CAAAyB,KAAA,CAAe;;;;;;IAO1B9B,EAFR,CAAAU,cAAA,UAAoB,UACX,eAC2G;IAA1EV,EAAA,CAAAW,UAAA,oBAAAuB,4DAAArB,MAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAqB,GAAA;MAAA,MAAA9B,MAAA,GAAAL,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAUZ,MAAA,CAAAiB,iBAAA,CAAAT,MAAA,CAAAU,MAAA,CAAAC,KAAA,CAAsC;IAAA,EAAC;IAAnFxB,EAAA,CAAAoB,YAAA,EAA4G;IAC5GpB,EAAA,CAAAU,cAAA,gBAAmF;IAAAV,EAAA,CAAAmB,MAAA,aAAM;IAEjGnB,EAFiG,CAAAoB,YAAA,EAAQ,EAC/F,EACJ;;;;IAHqBpB,EAAA,CAAAG,SAAA,GAAc;IAAdH,EAAA,CAAA+B,qBAAA,OAAA1B,MAAA,CAAAyB,KAAA,CAAc;IAAmD9B,EAAA,CAAAO,UAAA,aAAAF,MAAA,CAAA2B,QAAA,CAAqB;IAClGhC,EAAA,CAAAG,SAAA,EAAe;IAAfH,EAAA,CAAA+B,qBAAA,QAAA1B,MAAA,CAAAyB,KAAA,CAAe;;;;;IAc1B9B,EAAA,CAAAU,cAAA,cAAqE;IAAAV,EAAA,CAAAE,SAAA,cAAqE;IAAAF,EAAA,CAAAmB,MAAA,qBAAc;IAAAnB,EAAA,CAAAoB,YAAA,EAAM;;;;;;IAE9JpB,EAAA,CAAAC,uBAAA,GAAkC;IAClCD,EAAA,CAAAU,cAAA,cAA8C;IAS9CV,EARI,CAAAE,SAAA,UAOF,cAC0G;IAChHF,EAAA,CAAAoB,YAAA,EAAM;IAENpB,EAAA,CAAAU,cAAA,gBAAwD;IAArCV,EAAA,CAAAW,UAAA,oBAAAyB,4EAAAvB,MAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAuB,GAAA;MAAA,MAAAhC,MAAA,GAAAL,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAUZ,MAAA,CAAAiC,eAAA,CAAAzB,MAAA,CAAuB;IAAA,EAAC;IAArDb,EAAA,CAAAoB,YAAA,EAAwD;IAExDpB,EAAA,CAAAU,cAAA,wBAUC;IADGV,EAHA,CAAAW,UAAA,0BAAA4B,0FAAA1B,MAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAuB,GAAA;MAAA,MAAAhC,MAAA,GAAAL,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAgBZ,MAAA,CAAAmC,YAAA,CAAA3B,MAAA,CAAoB;IAAA,EAAC,yBAAA4B,yFAAA5B,MAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAuB,GAAA;MAAA,MAAAhC,MAAA,GAAAL,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CACtBZ,MAAA,CAAAqC,WAAA,CAAA7B,MAAA,CAAmB;IAAA,EAAC,0BAAA8B,0FAAA;MAAA3C,EAAA,CAAAc,aAAA,CAAAuB,GAAA;MAAA,MAAAhC,MAAA,GAAAL,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CACnBZ,MAAA,CAAAuC,YAAA,EAAc;IAAA,EAAC,6BAAAC,6FAAA;MAAA7C,EAAA,CAAAc,aAAA,CAAAuB,GAAA;MAAA,MAAAhC,MAAA,GAAAL,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CACZZ,MAAA,CAAAyC,eAAA,EAAiB;IAAA,EAAC;IACxC9C,EAAA,CAAAoB,YAAA,EAAgB;;;;;IAf6BpB,EAAA,CAAAG,SAAA,GAAoB;IAApBH,EAAA,CAAAO,UAAA,QAAAF,MAAA,CAAA0C,YAAA,EAAA/C,EAAA,CAAAS,aAAA,CAAoB;IAM9DT,EAAA,CAAAG,SAAA,GAAuC;IAGKH,EAH5C,CAAAO,UAAA,sBAAAF,MAAA,CAAA2C,iBAAA,CAAuC,6BACX,sBACP,sBACA,sBAAsB,wBAAwB;;;;;;IA3B7DhD,EAJV,CAAAU,cAAA,UAAuB,cACkB,cAEuB,iBAGsF;IAD5IV,EAAA,CAAAW,UAAA,mBAAAsC,4DAAA;MAAAjD,EAAA,CAAAc,aAAA,CAAAoC,GAAA;MAAA,MAAAC,aAAA,GAAAnD,EAAA,CAAAoD,WAAA;MAAA,OAAApD,EAAA,CAAAiB,WAAA,CAASkC,aAAA,CAAAE,KAAA,EAAkB;IAAA,EAAC;IAE1BrD,EAAA,CAAAU,cAAA,WAAM;IAAAV,EAAA,CAAAmB,MAAA,kBAAW;IAAAnB,EAAA,CAAAoB,YAAA,EAAO;IAACpB,EAAA,CAAAE,SAAA,cAA4E;IAChGF,EAAT,CAAAoB,YAAA,EAAS,EAAM;IACnBpB,EAAA,CAAAU,cAAA,mBAA6E;IAA9CV,EAAA,CAAAW,UAAA,oBAAA2C,4DAAAzC,MAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAoC,GAAA;MAAA,MAAA7C,MAAA,GAAAL,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAUZ,MAAA,CAAAkD,wBAAA,CAAA1C,MAAA,CAAgC;IAAA,EAAC;IAA1Eb,EAAA,CAAAoB,YAAA,EAA6E;IAC7EpB,EAAA,CAAAyB,UAAA,IAAA+B,yCAAA,kBAAqE;IACrExD,EAAA,CAAAoB,YAAA,EAAM;IACNpB,EAAA,CAAAyB,UAAA,KAAAgC,mDAAA,0BAAkC;IA4BtCzD,EAAA,CAAAoB,YAAA,EAAM;;;;IA9BIpB,EAAA,CAAAG,SAAA,GAAyB;IAAzBH,EAAA,CAAAO,UAAA,SAAAF,MAAA,CAAA0C,YAAA,QAAyB;IAEhB/C,EAAA,CAAAG,SAAA,EAAiB;IAAjBH,EAAA,CAAAO,UAAA,SAAAF,MAAA,CAAAqD,WAAA,CAAiB;;;;;IA2CxB1D,EAAA,CAAAC,uBAAA,GAA6D;IAC7DD,EAAA,CAAAE,SAAA,cAA8G;;;;;IAA5FF,EAAA,CAAAG,SAAA,EAAkE;IAAlEH,EAAA,CAAAI,WAAA,UAAAC,MAAA,CAAAsD,mBAAA,GAAAtD,MAAA,CAAAsD,mBAAA,aAAkE;IAAC3D,EAAA,CAAAO,UAAA,QAAAF,MAAA,CAAAuD,cAAA,EAAA5D,EAAA,CAAAS,aAAA,CAAsB;;;;;IAI3GT,EAAA,CAAAU,cAAA,cAA2C;IACvCV,EAAA,CAAAE,SAAA,cAA8C;IAClDF,EAAA,CAAAoB,YAAA,EAAM;;;;;IAUFpB,EAAA,CAAAU,cAAA,YAAwC;IACpCV,EAAA,CAAAmB,MAAA,GACJ;IAAAnB,EAAA,CAAAoB,YAAA,EAAI;;;;;IADApB,EAAA,CAAAG,SAAA,EACJ;IADIH,EAAA,CAAA6D,kBAAA,MAAAxD,MAAA,CAAAyD,WAAA,CAAAC,OAAA,kBAAAA,OAAA,CAAAC,IAAA,CAAAC,IAAA,OACJ;;;;;IACAjE,EAAA,CAAAU,cAAA,cAAiD;IAC7CV,EAAA,CAAAE,SAAA,cACM;IACVF,EAAA,CAAAoB,YAAA,EAAM;;;;IAFoBpB,EAAA,CAAAG,SAAA,EAAmC;IAAnCH,EAAA,CAAAI,WAAA,UAAA2D,OAAA,CAAAG,QAAA,OAAmC;;;;;;IAtBrElE,EAAA,CAAAU,cAAA,cAAyE;IAKzEV,EAJI,CAAAyB,UAAA,IAAA0C,8DAAA,0BAA6D,IAAAC,6DAAA,gCAAApE,EAAA,CAAA4B,sBAAA,CAIjC;IAQpB5B,EAFR,CAAAU,cAAA,cAAkB,YACE,YACgG;IAExGV,EAAA,CAAAmB,MAAA,GACF;IACNnB,EADM,CAAAoB,YAAA,EAAI,EACN;IAIJpB,EAHA,CAAAyB,UAAA,IAAA4C,mDAAA,gBAAwC,IAAAC,qDAAA,kBAGS;IAKrDtE,EAAA,CAAAoB,YAAA,EAAM;IAENpB,EAAA,CAAAU,cAAA,eAA4C;IAAxBV,EAAA,CAAAW,UAAA,mBAAA4D,sEAAA;MAAAvE,EAAA,CAAAc,aAAA,CAAA0D,GAAA;MAAA,MAAAC,KAAA,GAAAzE,EAAA,CAAAgB,aAAA,GAAA0D,KAAA;MAAA,MAAArE,MAAA,GAAAL,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASZ,MAAA,CAAAsE,UAAA,CAAAF,KAAA,CAAa;IAAA,EAAC;IACvCzE,EAAA,CAAAE,SAAA,aAAuD;IAE/DF,EADI,CAAAoB,YAAA,EAAM,EACJ;;;;;;IA9BapB,EAAA,CAAAG,SAAA,EAAqB;IAAAH,EAArB,CAAAO,UAAA,SAAAF,MAAA,CAAAuD,cAAA,CAAqB,aAAAgB,qBAAA,CAAsB;IAY1B5E,EAAA,CAAAG,SAAA,GAAmE;IAAnEH,EAAA,CAAAO,UAAA,SAAAF,MAAA,CAAAwE,cAAA,CAAAC,qBAAA,CAAAf,OAAA,kBAAAA,OAAA,CAAAC,IAAA,CAAAe,IAAA,GAAA/E,EAAA,CAAAS,aAAA,CAAmE;IAEvFT,EAAA,CAAAG,SAAA,EACF;IADEH,EAAA,CAAA6D,kBAAA,MAAAxD,MAAA,CAAAwE,cAAA,CAAAG,uBAAA,CAAAjB,OAAA,kBAAAA,OAAA,CAAAC,IAAA,CAAAe,IAAA,OACF;IAEW/E,EAAA,CAAAG,SAAA,EAAoB;IAApBH,EAAA,CAAAO,UAAA,SAAAwD,OAAA,CAAAC,IAAA,CAAAC,IAAA,CAAoB;IAGTjE,EAAA,CAAAG,SAAA,EAAmB;IAAnBH,EAAA,CAAAO,UAAA,SAAAwD,OAAA,CAAAG,QAAA,CAAmB;;;;;IAtB3DlE,EAAA,CAAAU,cAAA,UAA+C;IAC3CV,EAAA,CAAAyB,UAAA,IAAAwD,+CAAA,mBAAyE;IAgC7EjF,EAAA,CAAAoB,YAAA,EAAM;;;;IAhCIpB,EAAA,CAAAG,SAAA,EAAoB;IAApBH,EAAA,CAAAO,UAAA,SAAAwD,OAAA,CAAAC,IAAA,CAAAe,IAAA,CAAoB;;;;;IAFlC/E,EAAA,CAAAU,cAAA,cAA8C;IAC1CV,EAAA,CAAAyB,UAAA,IAAAyD,yCAAA,kBAA+C;IAkCnDlF,EAAA,CAAAoB,YAAA,EAAM;;;;IAlCoBpB,EAAA,CAAAG,SAAA,EAAU;IAAVH,EAAA,CAAAO,UAAA,YAAAF,MAAA,CAAAmB,KAAA,CAAU;;;;;;IAqChCxB,EAAA,CAAAU,cAAA,UAA8B;IAC1BV,EAAA,CAAAmB,MAAA,GACA;IAEQnB,EAFR,CAAAU,cAAA,cAA4B,cACC,aACb;IAAAV,EAAA,CAAAmB,MAAA,iBAAU;IAAAnB,EAAA,CAAAoB,YAAA,EAAS;IAACpB,EAAA,CAAAmB,MAAA,qBAChC;IAAAnB,EAAA,CAAAoB,YAAA,EAAM;IAENpB,EAAA,CAAAU,cAAA,0BAAuE;IAAlCV,EAAA,CAAAW,UAAA,uBAAAwE,+EAAAtE,MAAA;MAAAb,EAAA,CAAAc,aAAA,CAAAsE,IAAA;MAAA,MAAA/E,MAAA,GAAAL,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAaZ,MAAA,CAAAgF,WAAA,CAAAxE,MAAA,CAAmB;IAAA,EAAC;IAE9Eb,EAF+E,CAAAoB,YAAA,EAAkB,EACvF,EACJ;;;;IARFpB,EAAA,CAAAG,SAAA,EACA;IADAH,EAAA,CAAA6D,kBAAA,wBAAAxD,MAAA,CAAAmB,KAAA,CAAA8D,MAAA,YACA;IAKqBtF,EAAA,CAAAG,SAAA,GAAmB;IAAnBH,EAAA,CAAAO,UAAA,oBAAmB;;;;;;IAIxCP,EADJ,CAAAU,cAAA,cAA2D,cACrB;IAC9BV,EAAA,CAAAE,SAAA,cAA8C;IAC9CF,EAAA,CAAAU,cAAA,gBAA6C;IACzCV,EAAA,CAAAmB,MAAA,GACJ;IACJnB,EADI,CAAAoB,YAAA,EAAQ,EACN;IACNpB,EAAA,CAAAE,SAAA,SAAI;IACJF,EAAA,CAAAU,cAAA,0BAAsF;IAAlCV,EAAA,CAAAW,UAAA,uBAAA4E,+EAAA1E,MAAA;MAAAb,EAAA,CAAAc,aAAA,CAAA0E,IAAA;MAAA,MAAAnF,MAAA,GAAAL,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAAaZ,MAAA,CAAAgF,WAAA,CAAAxE,MAAA,CAAmB;IAAA,EAAC;IACzFb,EAD0F,CAAAoB,YAAA,EAAkB,EACtG;;;;IALMpB,EAAA,CAAAG,SAAA,GACJ;IADIH,EAAA,CAAA6D,kBAAA,MAAA4B,QAAA,CAAAzB,IAAA,CAAAe,IAAA,MACJ;IAGa/E,EAAA,CAAAG,SAAA,GAAoB;IAACH,EAArB,CAAAO,UAAA,qBAAoB,SAAAkF,QAAA,CAAc;;;;;IAnB3DzF,EAAA,CAAAU,cAAA,cAAgD;IAW5CV,EAVA,CAAAyB,UAAA,IAAAiE,yCAAA,iBAA8B,IAAAC,yCAAA,kBAU6B;IAU/D3F,EAAA,CAAAoB,YAAA,EAAM;;;;IApBIpB,EAAA,CAAAG,SAAA,EAAsB;IAAtBH,EAAA,CAAAO,UAAA,SAAAF,MAAA,CAAAmB,KAAA,CAAA8D,MAAA,KAAsB;IAUNtF,EAAA,CAAAG,SAAA,EAAQ;IAARH,EAAA,CAAAO,UAAA,YAAAF,MAAA,CAAAmB,KAAA,CAAQ;;;;;;IAa9BxB,EADJ,CAAAU,cAAA,cAA0F,iBAEb;IADjDV,EAAA,CAAAW,UAAA,mBAAAiF,4DAAA;MAAA5F,EAAA,CAAAc,aAAA,CAAA+E,IAAA;MAAA,MAAAxF,MAAA,GAAAL,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASZ,MAAA,CAAAyF,MAAA,EAAQ;IAAA,EAAC;IAGlC9F,EADJ,CAAAU,cAAA,cAA0C,eACR;IAAAV,EAAA,CAAAmB,MAAA,aAAM;IAAAnB,EAAA,CAAAoB,YAAA,EAAO;IAC3CpB,EAAA,CAAAE,SAAA,cAAwG;IAGpHF,EAFQ,CAAAoB,YAAA,EAAM,EACD,EACP;;;ADhIN,MAAM2E,mBAAmB,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;AAa9C,OAAM,MAAOC,oBAAoB;EAuC/BC,YACUC,IAAgB,EAChBC,WAAwB,EACxBC,cAA8B,EAC9BC,KAAmB,EACnBC,WAAwB,EACzBzB,cAA8B;IAL7B,KAAAqB,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,WAAW,GAAXA,WAAW;IACZ,KAAAzB,cAAc,GAAdA,cAAc;IA5Cd,KAAA0B,SAAS,GAAa,KAAK;IAC3B,KAAAC,oBAAoB,GAAa,KAAK;IACtC,KAAAC,MAAM,GAAa,KAAK;IACxB,KAAAjF,KAAK,GAAU,EAAE;IACjB,KAAAkF,kBAAkB,GAAW,CAAC;IAC9B,KAAAC,OAAO,GAAW,EAAE;IACpB,KAAA7E,KAAK,GAAW,EAAE;IAClB,KAAA8E,QAAQ,GAAY,KAAK;IACzB,KAAAC,KAAK,GAAY,KAAK;IACtB,KAAAC,UAAU,GAAY,KAAK;IAC3B,KAAA9E,QAAQ,GAAY,IAAI;IACxB,KAAA+E,gBAAgB,GAAY,KAAK;IACjC,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,qBAAqB,GAAW,EAAE;IASlC,KAAAC,UAAU,GAAY,KAAK;IAC1B,KAAAC,gBAAgB,GAAG,IAAI1H,YAAY,EAAE;IACrC,KAAA2H,mBAAmB,GAAG,IAAI3H,YAAY,EAAE;IACxC,KAAA4H,UAAU,GAAG,IAAI5H,YAAY,EAAE;IAC/B,KAAA6H,QAAQ,GAAG,IAAI7H,YAAY,EAAE;IAC/B,KAAA8H,IAAI,GAAG,IAAIzH,OAAO,EAAE;IACrB,KAAAoE,QAAQ,GAAa,EAAE;IACvB,KAAAsD,OAAO,GAAW,EAAE;IACpB,KAAA9D,WAAW,GAAG,KAAK;IAE1B,KAAAX,YAAY,GAAQ,EAAE;IACtB,KAAA0E,WAAW,GAAS,EAAU;IAC9B,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAAC,YAAY,GAAQ,EAAE;IACtB,KAAAC,SAAS,GAAGlI,MAAM,CAACK,YAAY,CAAC;IAiFzB,KAAA8H,UAAU,GAAIrG,KAAU,IAAI;MACjCsG,OAAO,CAACC,GAAG,CAACvG,KAAK,CAAC;MAGlB,IAAI,IAAI,CAAC+E,SAAS,IAAI,CAAC/E,KAAK,CAACuD,IAAI,EAAE;QACjC,IAAI,CAACoC,gBAAgB,CAACa,IAAI,CAAC;UAAEC,QAAQ,EAAE,EAAE;UAAEC,MAAM,EAAE,EAAE;UAAEC,UAAU,EAAE;QAAE,CAAE,CAAC;QACxE;MACF;MACA,IAAI,CAAC,IAAI,CAAC5B,SAAS,IAAI/E,KAAK,CAAC8D,MAAM,KAAK,CAAC,EAAE;QACzC;MACF;MACA,IAAI,CAAC,IAAI,CAACiB,SAAS,IAAI,CAAC,IAAI,CAACC,oBAAoB,EAAE;QACjD,KAAK,MAAMxC,IAAI,IAAIxC,KAAK,EAAE;UACxB,IAAI4G,MAAM,GAAW;YACnBpE,IAAI,EAAEA,IAAI;YACVE,QAAQ,EAAE,CAAC;YACXQ,KAAK,EAAE,IAAI,CAACgC;WACb;UACD,IAAI,CAACA,kBAAkB,EAAE;UACzB;UACA,IAAI,CAACP,WAAW,CAACkC,gBAAgB,CAACD,MAAM,CAAC;QAC3C;MACF;MACA,IAAI,CAAC,IAAI,CAAC7B,SAAS,IAAI,IAAI,CAACC,oBAAoB,EAAE;QAChD,KAAK,MAAMxC,IAAI,IAAIxC,KAAK,EAAE;UACxB,IAAI4G,MAAM,GAAW;YACnBpE,IAAI,EAAEA,IAAI;YACVE,QAAQ,EAAE,CAAC;YACXQ,KAAK,EAAE,IAAI,CAACgC;WACb;UACD;UACA;UACA;UACA,IAAI,CAAC4B,MAAM,CAACF,MAAM,CAAC;QACrB;MACF,CAAC,MAAM;QACL,IAAIA,MAAM,GAAW;UACnBpE,IAAI,EAAExC,KAAK;UACX0C,QAAQ,EAAE,CAAC;UACXQ,KAAK,EAAE,IAAI,CAACgC;SACb;QACD,IAAI,CAAC4B,MAAM,CAACF,MAAM,CAAC;MACrB;IACF,CAAC;EAnHG;EAEJG,QAAQA,CAAA;IACN;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAChB,IAAI,CAACiB,IAAI,GAAG,IAAI,CAACrC,WAAW,CAACsC,aAAa,CAACC,SAAS,CAACC,GAAG,IAAG;MAC9D,IAAIA,GAAG,EAAE;QACPb,OAAO,CAACC,GAAG,CAAC,IAAI,CAACN,WAAW,CAAC;QAC7B,IAAI,CAACI,UAAU,CAAC,IAAI,CAACJ,WAAW,CAAC;MACnC;IACF,CAAC,CAAC;IACF,IAAI,CAACF,IAAI,CAACiB,IAAI,GAAG,IAAI,CAACrC,WAAW,CAACyC,WAAW,CAACF,SAAS,CAACC,GAAG,IAAG;MAC5D,IAAIA,GAAG,EAAE;QACP,IAAI,CAACnH,KAAK,GAAG,EAAE;MACjB;IACF,CAAC,CAAC;IACF,IAAI,CAAC+F,IAAI,CAACiB,IAAI,GAAG,IAAI,CAACrC,WAAW,CAAC0C,aAAa,CAACH,SAAS,CAACC,GAAG,IAAG;MAC9D,IAAIA,GAAG,IAAI,CAAC,IAAI,CAAC9D,cAAc,CAACiE,aAAa,CAACH,GAAG,CAAC,EAAE;QAClDb,OAAO,CAACC,GAAG,CAACY,GAAG,CAAC;QAChB,IAAI,CAACnH,KAAK,CAACuH,IAAI,CAACJ,GAAG,CAAC;QACpB,IAAI,CAACL,MAAM,CAACK,GAAa,CAAC;MAC5B;IACF,CAAC,CAAC;EACJ;EAEAK,WAAWA,CAAA;IACT;IACA,IAAI,CAAC7C,WAAW,CAAC8C,gBAAgB,CAAC,KAAK,CAAC;IACxC,IAAI,CAAC1B,IAAI,CAAC2B,WAAW,EAAE;EACzB;EAEA;;;EAGAhI,aAAaA,CAACL,MAAW;IACvB,IAAI,CAACgH,UAAU,CAAChH,MAAM,CAAC;EACzB;EAEA;;;;EAIA8D,UAAUA,CAACD,KAAa;IACtB,IAAI,CAAC0C,mBAAmB,CAACY,IAAI,CAAC;MAAEhE,IAAI,EAAE,IAAI,CAACxC,KAAK,CAACkD,KAAK;IAAC,CAAE,CAAC;IAC1D,IAAI,CAAClD,KAAK,CAAC2H,MAAM,CAACzE,KAAK,EAAE,CAAC,CAAC;EAC7B;EAEA;;;EAGAnB,wBAAwBA,CAAC6F,KAAU;IACjC,IAAI,CAAC1B,eAAe,GAAG0B,KAAK,CAAC7H,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAACuD,IAAI;IACjD,IAAI,CAACrB,WAAW,GAAG,IAAI;IACvB,IAAI,CAACV,iBAAiB,GAAGoG,KAAK;IAC9B;EACF;EAEA;;;EAGA9H,iBAAiBA,CAAC8H,KAAU;IAC1BtB,OAAO,CAACC,GAAG,CAACqB,KAAK,CAAC;IAClB,IAAI,CAACvB,UAAU,CAACuB,KAAK,CAAC;EACxB;EA+CAd,MAAMA,CAACF,MAAc;IAEnB,IAAIA,MAAM,CAACpE,IAAI,CAACC,IAAI,IAAImE,MAAM,CAACpE,IAAI,CAACC,IAAI,GAAG8B,mBAAmB,EAAE;MAC9D,IAAI,CAACM,KAAK,CAACgD,mBAAmB,CAAC;QAC7BC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;MACF;IACF;IAEA,IAAIC,OAAY;IAChB,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,EAAE;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAExB,MAAM,CAACpE,IAAI,EAAE,IAAI,CAAC6F,mCAAmC,CAACzB,MAAM,CAACpE,IAAI,CAACe,IAAI,CAAC,CAAC;IAChG0E,OAAO,GAAG,IAAI7J,WAAW,CAAC;MACxB,cAAc,EAAEwI,MAAM,CAACpE,IAAI,CAACe,IAAI,CAAC+E,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAG;MAClD,eAAe,EAAE,SAAS,GAAG,IAAI,CAACzD,WAAW,CAAC0D,QAAQ;KACvD,CAAC;IAGFlC,OAAO,CAACC,GAAG,CAAC0B,OAAO,CAAC;IACpB,IAAI,IAAI,CAACQ,iBAAiB,EAAE;MAC1B,KAAK,IAAIC,IAAI,IAAI,IAAI,CAACD,iBAAiB,CAACE,KAAK,EAAE;QAC7C,IAAIC,cAAc,GAAG,IAAI,CAACpD,cAAc,GAAG,aAAa,GAAGkD,IAAI,CAACG,EAAE,GAAG,EAAE;QACvE,IAAI,CAACC,aAAa,CAACb,OAAO,EAAEW,cAAc,EAAEV,QAAQ,EAAEtB,MAAM,CAAC;MAC/D;IACF,CAAC,MAAM,IAAI,IAAI,CAACmC,YAAY,EAAE;MAC5B,IAAIH,cAAc,GAAG,aAAa,GAAG,IAAI,CAACG,YAAY,CAACF,EAAG;MAC1DvC,OAAO,CAACC,GAAG,CAACqC,cAAc,CAAC;MAC3B,IAAI,CAACE,aAAa,CAACb,OAAO,EAAEW,cAAc,EAAEV,QAAQ,EAAEtB,MAAM,CAAC;IAC/D,CAAC,MAAM;MACL,IAAIgC,cAAc,GAAG,IAAI,CAACpD,cAAc,GAAG,aAAa,GAAG,IAAI,CAACC,qBAAqB,GAAG,EAAE;MAC1F,IAAI,CAACqD,aAAa,CAACb,OAAO,EAAEW,cAAc,EAAEV,QAAQ,EAAEtB,MAAM,CAAC;IAC/D;EAEF;EAEAkC,aAAaA,CAACb,OAAoB,EAAEW,cAAsB,EAAEV,QAAkB,EAAEtB,MAAc;IAC5F,IAAI,CAACb,IAAI,CAACiD,GAAG,CAAC,IAAI,CAACtE,IAAI,CAACuE,IAAI,CAAC5K,WAAW,CAAC6K,MAAM,GAAG,IAAI,CAAC/D,OAAO,GAAGyD,cAAc,EAAEV,QAAQ,EAAE;MACzFD,OAAO,EAAEA,OAAO;MAChBkB,cAAc,EAAE,IAAI;MACpBC,OAAO,EAAE;KACV,CAAC,CACClC,SAAS,CAACU,KAAK,IAAG;MACjBtB,OAAO,CAACC,GAAG,CAACqB,KAAK,CAAC;MAClB,IAAIA,KAAK,CAACyB,IAAI,KAAKlL,aAAa,CAACmL,cAAc,EAAE;QAC/C1C,MAAM,CAAClE,QAAQ,GAAG6G,IAAI,CAACC,KAAK,CAAC,GAAG,GAAG5B,KAAK,CAAC6B,MAAM,GAAG7B,KAAK,CAAC8B,KAAM,CAAC;MACjE,CAAC,MACI,IAAI9B,KAAK,CAACyB,IAAI,KAAKlL,aAAa,CAACwL,QAAQ,EAAE;QAE9C,IAAI,CAAC9E,KAAK,CAACgD,mBAAmB,CAAC;UAC7BC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAAC7B,YAAY,GAAGyB,KAAK,CAACgC,IAAI;QAC9B,IAAI,IAAI,CAAC7E,SAAS,EAAE;UAClBuB,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC;UACpB,IAAI,CAACrE,WAAW,GAAG,KAAK;QAC1B;QACA,IAAI,CAACyD,gBAAgB,CAACa,IAAI,CAAC;UAAEC,QAAQ,EAAEmB,KAAK,CAACgC,IAAI;UAAEC,IAAI,EAAE,IAAI,CAACA;QAAI,CAAE,CAAC;MACvE,CAAC,MAAM,CACP;IACF,CAAC,EAAEC,GAAG,IAAG;MACPxD,OAAO,CAACC,GAAG,CAACuD,GAAG,CAAC;IAClB,CAAC,CAAC,CAAC;EACP;EAEAxH,WAAWA,CAACyH,KAAU,EAAEC,QAAa;IACnC,IAAID,KAAK,KAAK,CAAC,EAAE;MACf,OAAO,SAAS;IAClB;IACA,MAAME,CAAC,GAAG,IAAI;IACd,MAAMC,EAAE,GAAGF,QAAQ,IAAI,CAAC,GAAG,CAAC,GAAGA,QAAQ,IAAI,CAAC;IAC5C,MAAMG,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACvE,MAAMC,CAAC,GAAGb,IAAI,CAACc,KAAK,CAACd,IAAI,CAAChD,GAAG,CAACwD,KAAK,CAAC,GAAGR,IAAI,CAAChD,GAAG,CAAC0D,CAAC,CAAC,CAAC;IACnD,OAAOK,UAAU,CAAC,CAACP,KAAK,GAAGR,IAAI,CAACgB,GAAG,CAACN,CAAC,EAAEG,CAAC,CAAC,EAAEI,OAAO,CAACN,EAAE,CAAC,CAAC,GAAG,GAAG,GAAGC,KAAK,CAACC,CAAC,CAAC;EAC1E;EAEAvG,WAAWA,CAAC+D,KAAU;IACpB,IAAI,CAAC/B,UAAU,CAACW,IAAI,CAACoB,KAAK,CAAC;EAC7B;EAEA5G,YAAYA,CAAC4G,KAAwB;IACnCtB,OAAO,CAACC,GAAG,CAACqB,KAAK,CAAC;IAClB,IAAI,CAACrG,YAAY,GAAG,IAAI,CAAC6E,SAAS,CAACqE,sBAAsB,CAAC7C,KAAK,CAAC8C,SAAS,IAAI9C,KAAK,CAAC+C,MAAM,IAAI,EAAE,CAAC;IAChG,MAAMnI,IAAI,GAAG,IAAIoI,IAAI,CAAC,CAAChD,KAAK,CAACiD,IAAK,CAAC,EAAE,IAAI,CAAC3E,eAAe,EAAE;MAAEmD,IAAI,EAAEzB,KAAK,CAACiD,IAAK,CAACxB;IAAI,CAAE,CAAC;IACtF,IAAI,CAACpD,WAAW,GAAG,IAAI2E,IAAI,CAAC,CAACpI,IAAI,CAAC,EAAE,IAAI,CAAC0D,eAAe,CAAC;IACzD,MAAM4E,MAAM,GAAG,IAAIC,UAAU,EAAE;IAC/BD,MAAM,CAACE,MAAM,GAAG,MAAK;MACnB,IAAI,CAACzJ,YAAY,GAAGuJ,MAAM,CAACG,MAAgB;IAC7C,CAAC;IACDH,MAAM,CAACI,aAAa,CAAC1I,IAAI,CAAC;EAC5B;EAEAtB,WAAWA,CAAC0G,KAAU;IACpBtB,OAAO,CAACC,GAAG,CAACqB,KAAK,CAAC;IAClB;EACF;EAEAxG,YAAYA,CAACwG,KAAU;IACrB;EAAA;EAGFtG,eAAeA,CAAA;IACb;EAAA;EAGFgD,MAAMA,CAAA;IACJ,IAAI,CAACwB,QAAQ,CAACU,IAAI,EAAE;EACtB;EAEA;;;;;EAKQ6B,mCAAmCA,CAAC8C,QAAgB;IAC1D,MAAMC,iBAAiB,GAAG,YAAY;IACtC,MAAMC,oBAAoB,GAAG,GAAG;IAEhC,OAAOF,QAAQ,CAACG,OAAO,CAACF,iBAAiB,EAAEC,oBAAoB,CAAC;EAClE;EAAC,QAAAE,CAAA,G;qBA7RU/G,oBAAoB,EAAAhG,EAAA,CAAAgN,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAlN,EAAA,CAAAgN,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAApN,EAAA,CAAAgN,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAtN,EAAA,CAAAgN,iBAAA,CAAAO,EAAA,CAAAC,YAAA,GAAAxN,EAAA,CAAAgN,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAA1N,EAAA,CAAAgN,iBAAA,CAAAW,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAApB7H,oBAAoB;IAAA8H,SAAA;IAAAC,MAAA;MAAAxH,SAAA;MAAAC,oBAAA;MAAAC,MAAA;MAAAjF,KAAA;MAAAkF,kBAAA;MAAAC,OAAA;MAAA7E,KAAA;MAAA8E,QAAA;MAAAC,KAAA;MAAAC,UAAA;MAAA9E,QAAA;MAAA+E,gBAAA;MAAAC,cAAA;MAAAC,qBAAA;MAAAgD,iBAAA;MAAAM,YAAA;MAAAc,IAAA;MAAApD,QAAA;MAAAzH,aAAA;MAAAF,kBAAA;MAAAsD,cAAA;MAAAD,mBAAA;MAAAuD,UAAA;IAAA;IAAA8G,OAAA;MAAA7G,gBAAA;MAAAC,mBAAA;MAAAC,UAAA;MAAAC,QAAA;IAAA;IAAA2G,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC2GjCtO,EAxIA,CAAAyB,UAAA,IAAA+M,mCAAA,kBAAmB,IAAAC,mCAAA,iBAiBC,IAAAC,mCAAA,kBAOG,IAAAC,mCAAA,iBAoDuB,IAAAC,mCAAA,iBAqCE,IAAAC,mCAAA,iBAuB0C;;;QAxIpF7O,EAAA,CAAAO,UAAA,SAAAgO,GAAA,CAAA1H,KAAA,CAAW;QAiBX7G,EAAA,CAAAG,SAAA,EAAY;QAAZH,EAAA,CAAAO,UAAA,SAAAgO,GAAA,CAAA9H,MAAA,CAAY;QAOZzG,EAAA,CAAAG,SAAA,EAAe;QAAfH,EAAA,CAAAO,UAAA,SAAAgO,GAAA,CAAAhI,SAAA,CAAe;QAoDfvG,EAAA,CAAAG,SAAA,EAAW;QAAXH,EAAA,CAAAO,UAAA,SAAAgO,GAAA,CAAA1H,KAAA,CAAW;QAqCX7G,EAAA,CAAAG,SAAA,EAAgB;QAAhBH,EAAA,CAAAO,UAAA,SAAAgO,GAAA,CAAAzH,UAAA,CAAgB;QAuBhB9G,EAAA,CAAAG,SAAA,EAAsB;QAAtBH,EAAA,CAAAO,UAAA,SAAAgO,GAAA,CAAAxH,gBAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}