{"ast": null, "code": "import { UntypedFormArray, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { Observable } from 'rxjs';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/core/services/teacher-application.service\";\nimport * as i4 from \"src/app/core/services/general.service\";\nimport * as i5 from \"src/app/core/services/toast.service\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"primeng/dropdown\";\nimport * as i9 from \"primeng/checkbox\";\nimport * as i10 from \"primeng/ripple\";\nfunction ExperienceComponent_form_1_div_3_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtext(1, \" Additional Languages you speak & levels * \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExperienceComponent_form_1_div_3_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵlistener(\"click\", function ExperienceComponent_form_1_div_3_div_6_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.addLanguageSpeak());\n    });\n    i0.ɵɵtext(1, \" + \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExperienceComponent_form_1_div_3_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32);\n    i0.ɵɵlistener(\"click\", function ExperienceComponent_form_1_div_3_div_7_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const i_r5 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.removeLanguageSpeak(i_r5));\n    });\n    i0.ɵɵtext(2, \" \\u2013 \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ExperienceComponent_form_1_div_3_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 34);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const language_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(language_r6.name);\n  }\n}\nfunction ExperienceComponent_form_1_div_3_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 34);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const level_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(level_r7.levelName);\n  }\n}\nfunction ExperienceComponent_form_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵelementContainerStart(1, 21);\n    i0.ɵɵelement(2, \"div\", 16);\n    i0.ɵɵelementStart(3, \"div\", 14)(4, \"div\", 22);\n    i0.ɵɵtemplate(5, ExperienceComponent_form_1_div_3_div_5_Template, 2, 0, \"div\", 23)(6, ExperienceComponent_form_1_div_3_div_6_Template, 2, 0, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, ExperienceComponent_form_1_div_3_div_7_Template, 3, 0, \"div\", 25);\n    i0.ɵɵelementStart(8, \"div\", 26)(9, \"p-dropdown\", 27);\n    i0.ɵɵlistener(\"onChange\", function ExperienceComponent_form_1_div_3_Template_p_dropdown_onChange_9_listener($event) {\n      const i_r5 = i0.ɵɵrestoreView(_r1).index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.handleSpeakLanguageChange($event, i_r5));\n    });\n    i0.ɵɵtemplate(10, ExperienceComponent_form_1_div_3_ng_template_10_Template, 3, 1, \"ng-template\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 26)(12, \"p-dropdown\", 29);\n    i0.ɵɵtemplate(13, ExperienceComponent_form_1_div_3_ng_template_13_Template, 3, 1, \"ng-template\", 28);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r5 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroupName\", i_r5);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", i_r5 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r5 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r5 !== 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"options\", ctx_r2.languages);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"options\", ctx_r2.filteredLanguageSpeakLevels[i_r5])(\"disabled\", ctx_r2.disableSpeakLevelsDropdown[i_r5])(\"placeholder\", ctx_r2.disableSpeakLevelsDropdown[i_r5] ? \"Select a Language before Level\" : \"Select Level\");\n  }\n}\nfunction ExperienceComponent_form_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \" * Language & Level fields are required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExperienceComponent_form_1_div_6_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtext(1, \" Languages you teach & levels * \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExperienceComponent_form_1_div_6_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵlistener(\"click\", function ExperienceComponent_form_1_div_6_div_5_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.addLanguageTeach());\n    });\n    i0.ɵɵtext(1, \" + \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExperienceComponent_form_1_div_6_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32);\n    i0.ɵɵlistener(\"click\", function ExperienceComponent_form_1_div_6_div_6_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const i_r11 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.removeLanguageTeach(i_r11));\n    });\n    i0.ɵɵtext(2, \" \\u2013 \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ExperienceComponent_form_1_div_6_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 34);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const language_r12 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(language_r12.name);\n  }\n}\nfunction ExperienceComponent_form_1_div_6_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 34);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const level_r13 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(level_r13.levelName);\n  }\n}\nfunction ExperienceComponent_form_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelementContainerStart(1, 21);\n    i0.ɵɵelementStart(2, \"div\", 37)(3, \"div\", 22);\n    i0.ɵɵtemplate(4, ExperienceComponent_form_1_div_6_div_4_Template, 2, 0, \"div\", 23)(5, ExperienceComponent_form_1_div_6_div_5_Template, 2, 0, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ExperienceComponent_form_1_div_6_div_6_Template, 3, 0, \"div\", 25);\n    i0.ɵɵelementStart(7, \"div\", 26)(8, \"p-dropdown\", 27);\n    i0.ɵɵlistener(\"onChange\", function ExperienceComponent_form_1_div_6_Template_p_dropdown_onChange_8_listener($event) {\n      const i_r11 = i0.ɵɵrestoreView(_r8).index;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.handleTeachLanguageChange($event, i_r11));\n    });\n    i0.ɵɵtemplate(9, ExperienceComponent_form_1_div_6_ng_template_9_Template, 3, 1, \"ng-template\", 28);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 26)(11, \"p-dropdown\", 29);\n    i0.ɵɵtemplate(12, ExperienceComponent_form_1_div_6_ng_template_12_Template, 3, 1, \"ng-template\", 28);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"div\", 38)(14, \"div\", 39);\n    i0.ɵɵelement(15, \"p-checkbox\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r11 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroupName\", i_r11);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", i_r11 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r11 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r11 !== 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"options\", ctx_r2.languages);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"options\", ctx_r2.teachLevelOptions)(\"disabled\", ctx_r2.disableTeachLevelsDropdown[i_r11])(\"placeholder\", ctx_r2.disableTeachLevelsDropdown[i_r11] ? \"Select a Language before Level\" : \"Select Level\");\n  }\n}\nfunction ExperienceComponent_form_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \" * Language & Level fields are required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExperienceComponent_form_1_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \" * Teaching Methods are required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExperienceComponent_form_1_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \" * Years of experience field is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExperienceComponent_form_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"form\", 7)(1, \"div\", 8);\n    i0.ɵɵelementContainerStart(2, 9);\n    i0.ɵɵtemplate(3, ExperienceComponent_form_1_div_3_Template, 14, 8, \"div\", 10);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(4, ExperienceComponent_form_1_div_4_Template, 2, 0, \"div\", 11);\n    i0.ɵɵelementContainerStart(5, 12);\n    i0.ɵɵtemplate(6, ExperienceComponent_form_1_div_6_Template, 16, 8, \"div\", 13);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(7, ExperienceComponent_form_1_div_7_Template, 2, 0, \"div\", 11);\n    i0.ɵɵelementStart(8, \"div\", 14)(9, \"div\", 15)(10, \"div\", 16);\n    i0.ɵɵtext(11, \" Describe your teaching methods * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"textarea\", 17);\n    i0.ɵɵtemplate(13, ExperienceComponent_form_1_div_13_Template, 2, 0, \"div\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 18)(15, \"div\", 16);\n    i0.ɵɵtext(16, \" Years of experience as teacher * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(17, \"input\", 19);\n    i0.ɵɵtemplate(18, ExperienceComponent_form_1_div_18_Template, 2, 0, \"div\", 11);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.form);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.languagesSpeakForm.controls);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.ifFieldValid(\"languagesSpeakForm\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.languagesTeachForm.controls);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.ifFieldValid(\"languagesTeachForm\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.ifFieldValid(\"methods\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.ifFieldValid(\"years\"));\n  }\n}\nexport class ExperienceComponent {\n  constructor(fb, router, teacherService, generalService, toast, confirmationService) {\n    this.fb = fb;\n    this.router = router;\n    this.teacherService = teacherService;\n    this.generalService = generalService;\n    this.toast = toast;\n    this.confirmationService = confirmationService;\n    this.subs = new SubSink();\n    this.form = new UntypedFormGroup({});\n    this.teacherLanguagesSpeak = [];\n    this.teacherLanguagesTeach = [];\n    this.languageSpeakError = false;\n    this.languageTeachError = false;\n    this.isLoading = true;\n    this.tryToSave = false;\n    this.languages = this.generalService.languages;\n    this.availableLanguageLevels = this.generalService.getLanguageLevels();\n    this.filteredLanguageSpeakLevels = [];\n    this.filteredLanguageTeachLevels = [];\n    this.disableSpeakLevelsDropdown = [true];\n    this.disableTeachLevelsDropdown = [true];\n    this.formChanged = false;\n    this.teachLevelOptions = [{\n      levelName: \"I teach only beginner-level students (A1 - B1)\",\n      \"showOnlyOnCode\": [],\n      \"visible\": true\n    }, {\n      levelName: \"I teach only advanced-level students (B2 - C2)\",\n      \"showOnlyOnCode\": [],\n      \"visible\": true\n    }, {\n      levelName: \"I teach all levels (A1 - C2)\",\n      \"showOnlyOnCode\": [],\n      \"visible\": true\n    }];\n  }\n  ngOnInit() {\n    this.teacherService.setCurrentStepIndex(2);\n    this.subs.add(this.teacherService.getTeacherApplicationStep3().subscribe(res => {\n      this.isLoading = false;\n      this.createForm();\n      this.updateStepFormValues(res);\n      this.initFormChangedListener();\n    }));\n    this.subs.add(this.teacherService.submitOnMenuClickListener.subscribe(res => {\n      if (res) {\n        this.onSubmit();\n      }\n    }));\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n    this.teacherService.setSubmitOnMenuClickListener(false);\n  }\n  get languagesSpeakForm() {\n    return this.form.get('languagesSpeakForm');\n  }\n  get languagesTeachForm() {\n    return this.form.get('languagesTeachForm');\n  }\n  /**\n   * Initializes the form for the application\n   *\n   */\n  createForm() {\n    this.form = new UntypedFormGroup({\n      methods: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      }),\n      years: new UntypedFormControl(1, {\n        validators: [Validators.required]\n      }),\n      languagesSpeakForm: new UntypedFormArray([]),\n      languagesTeachForm: new UntypedFormArray([])\n    });\n  }\n  /**\n  * Updates the form values for the step 3 of the teacher application process\n  *\n  * @param {TeacherApplicationStep3} object - Object containing the updated values for the form\n  *\n  */\n  updateStepFormValues(object) {\n    this.form.get('methods')?.patchValue(object.teachingMethods);\n    this.form.get('years')?.patchValue(object.yearsExperience);\n    if (this.generalService.isNullishObject(object.languageSpeak)) {\n      this.addLanguageSpeak();\n    } else {\n      this.filteredLanguageSpeakLevels = [];\n      object.languageSpeak.forEach((element, index) => {\n        const elementFound = this.availableLanguageLevels.filter(i => {\n          return i.levelName === element.level.levelName;\n        });\n        element.level = elementFound[0];\n        this.disableSpeakLevelsDropdown[index] = false;\n        this.setfilteredLanguageSpeakLevels(index, element.language.code);\n        this.languagesSpeakForm.push(this.fb.group(element));\n      });\n    }\n    if (this.generalService.isNullishObject(object.languageTeach)) {\n      this.addLanguageTeach();\n    } else {\n      // this.filteredLanguageTeachLevels = this.teachLevelOptions;\n      object.languageTeach.forEach((element, index) => {\n        const elementFound = this.teachLevelOptions.filter(i => {\n          return i.levelName === element.level.levelName;\n        });\n        element.level = elementFound[0];\n        // element.level = {\n        //   levelName: element.levelName,\n        // }\n        console.log(element);\n        this.disableTeachLevelsDropdown[index] = false;\n        this.setfilteredLanguageTeachLevels(index);\n        this.languagesTeachForm.push(this.fb.group(element));\n      });\n    }\n  }\n  /**\n   * Sets the filtered language speak levels based on the language code\n   *\n   * @param {number} index - Index of the language in the array of languages\n   * @param {string} [languageCode] - Code of the language to filter the levels for\n   *\n   */\n  setfilteredLanguageSpeakLevels(index, languageCode) {\n    this.filteredLanguageSpeakLevels[index] = this.availableLanguageLevels.filter(i => {\n      return i.showOnlyOnCode.length === 0 || i.showOnlyOnCode.includes(languageCode);\n    });\n    console.log(index, this.filteredLanguageSpeakLevels[index]);\n  }\n  /**\n   * Sets the filtered language teach levels\n   *\n   * @param {number} index - Index of the language in the array of languages\n   *\n   */\n  setfilteredLanguageTeachLevels(index, languageCode) {\n    this.filteredLanguageTeachLevels[index] = this.teachLevelOptions.filter(i => {\n      return i;\n    });\n    console.log(index, this.filteredLanguageTeachLevels[index]);\n    // this.filteredLanguageTeachLevels[index] = this.availableLanguageLevels.\n    //   filter(i => {\n    //     return (i.visible);\n    //   });\n  }\n  /**\n   * Adds a new form group for spoken languages\n   *\n   * @throws {Error} If FormGroup or FormControl classes are not defined\n   */\n  addLanguageSpeak() {\n    if (!UntypedFormGroup || !UntypedFormControl) {\n      throw new Error('FormGroup or FormControl classes are not defined');\n    }\n    const group = new UntypedFormGroup({\n      language: new UntypedFormControl('', Validators.required),\n      level: new UntypedFormControl('', Validators.required),\n      hasBusinessLevel: new UntypedFormControl(false)\n    });\n    this.languagesSpeakForm.push(group);\n    this.disableSpeakLevelsDropdown[this.languagesSpeakForm.length - 1] = true;\n  }\n  /**\n   * Removes a form group for spoken languages\n   *\n   * @param {number} index - Index of the form group in the form array\n   */\n  removeLanguageSpeak(index) {\n    this.languagesSpeakForm.removeAt(index);\n  }\n  /**\n   * Adds a new form group for taught languages\n   *\n   * @throws {Error} If FormGroup or FormControl classes are not defined\n   */\n  addLanguageTeach() {\n    if (!UntypedFormGroup || !UntypedFormControl) {\n      throw new Error('FormGroup or FormControl classes are not defined');\n    }\n    const group = new UntypedFormGroup({\n      language: new UntypedFormControl('', Validators.required),\n      level: new UntypedFormControl('', Validators.required),\n      hasBusinessLevel: new UntypedFormControl(false)\n    });\n    this.languagesTeachForm.push(group);\n    this.disableTeachLevelsDropdown[this.languagesTeachForm.length - 1] = true;\n  }\n  /**\n   * Removes a form group for taught languages\n   *\n   * @param {number} index - Index of the form group in the form array\n   */\n  removeLanguageTeach(index) {\n    this.languagesTeachForm.removeAt(index);\n  }\n  onSubmit() {\n    this.tryToSave = true;\n    this.languageSpeakError = this.form.controls.languagesSpeakForm.status === 'INVALID' ? true : false;\n    this.languageTeachError = this.form.controls.languagesTeachForm.status === 'INVALID' ? true : false;\n    if (!this.form.valid) {\n      return;\n    }\n    const experienceValuesToSubmit = {\n      languageSpeak: this.form.value.languagesSpeakForm,\n      languageTeach: this.form.value.languagesTeachForm,\n      yearsExperience: this.form.value.years,\n      teachingMethods: this.form.value.methods\n    };\n    this.subs.add(this.teacherService.updateAPITeacherApplicationStep3(experienceValuesToSubmit).subscribe(res => {\n      console.log(res);\n      if (res) {\n        this.toast.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Your Experience info were updated.'\n        });\n        this.router.navigateByUrl('/teacher/resume', {\n          replaceUrl: true\n        });\n      }\n    }));\n  }\n  ifFieldValid(field) {\n    this.teacherService.setStepValid(2, this.form, 'teacher-experience-route');\n    return this.form.get(field)?.invalid && this.form.get(field)?.touched || this.tryToSave && this.form.get(field)?.invalid;\n  }\n  goBack() {\n    this.router.navigateByUrl('/teacher/education', {\n      replaceUrl: true\n    });\n  }\n  /**\n  Handles the change in language selection for speaking.\n  @param {Object} event - The event object containing the selected language code.\n  @param {number} languageIndex - The index of the selected language in the list.\n  @returns {void}\n  */\n  handleSpeakLanguageChange(event, languageIndex) {\n    this.disableSpeakLevelsDropdown[languageIndex] = false;\n    this.filteredLanguageSpeakLevels[languageIndex] = this.availableLanguageLevels.filter(i => {\n      return i.showOnlyOnCode.length === 0 || i.showOnlyOnCode.includes(event.value.code);\n    });\n  }\n  /**\n  Handles the change in language selection for teaching.\n  @param {Object} event - The event object containing the selected language code.\n  @param {number} languageIndex - The index of the selected language in the list.\n  @returns {void}\n  */\n  handleTeachLanguageChange(event, languageIndex) {\n    this.disableTeachLevelsDropdown[languageIndex] = false;\n    // this.filteredLanguageTeachLevels[languageIndex] = this.availableLanguageLevels.\n    //   filter(i => {\n    //     return (i.showOnlyOnCode.length === 0) || (i.showOnlyOnCode.includes(event.value.code));\n    //   });\n  }\n  initFormChangedListener() {\n    this.subs.add(this.form.valueChanges.subscribe(val => {\n      if (this.form.dirty) {\n        console.log(val);\n        this.formChanged = true;\n      }\n    }));\n  }\n  canDeactivate() {\n    if (this.formChanged && !this.tryToSave) {\n      return new Observable(observer => {\n        this.confirmationService.confirm({\n          header: '',\n          key: 'stepLeaveConfirmation',\n          message: 'Are you sure you want to leave this page? Your changes will be lost.',\n          accept: () => {\n            observer.next(true);\n            observer.complete();\n          },\n          reject: () => {\n            observer.next(false);\n            observer.complete();\n          }\n        });\n      });\n    } else {\n      return true;\n    }\n  }\n  static #_ = this.ɵfac = function ExperienceComponent_Factory(t) {\n    return new (t || ExperienceComponent)(i0.ɵɵdirectiveInject(i1.UntypedFormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.TeacherApplicationService), i0.ɵɵdirectiveInject(i4.GeneralService), i0.ɵɵdirectiveInject(i5.ToastService), i0.ɵɵdirectiveInject(i6.ConfirmationService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ExperienceComponent,\n    selectors: [[\"app-experience\"]],\n    decls: 9,\n    vars: 1,\n    consts: [[1, \"profile-info\", \"md:mt-3\"], [3, \"formGroup\", 4, \"ngIf\"], [1, \"btns\", \"md:ml-5\", \"mt-4\"], [\"pRipple\", \"\", 1, \"rounded-blue-button\", \"transparent\", 3, \"click\"], [\"src\", \"/assets/icons/arrow-left-blue.svg\"], [\"pRipple\", \"\", 1, \"rounded-blue-button\", 3, \"click\"], [\"src\", \"/assets/icons/arrow-right.svg\"], [3, \"formGroup\"], [1, \"profile-info-section\"], [\"formArrayName\", \"languagesSpeakForm\"], [\"style\", \"position: relative;\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"input-error mt-2 font-base\", 4, \"ngIf\"], [\"formArrayName\", \"languagesTeachForm\"], [\"class\", \"m-t-15\", \"style\", \"position: relative;\", 4, \"ngFor\", \"ngForOf\"], [1, \"input-fields\"], [1, \"input-field\", \"w-100\"], [1, \"input-element-title\"], [\"formControlName\", \"methods\", \"type\", \"text\", \"rows\", \"5\", 1, \"input-element\"], [1, \"input-field\"], [\"formControlName\", \"years\", \"type\", \"number\", \"min\", \"1\", 1, \"input-element\"], [2, \"position\", \"relative\"], [3, \"formGroupName\"], [1, \"input-field\", \"w-100\", \"p-b-0\", \"display-flex\", \"justify-space-between\", \"align-center\"], [\"class\", \"input-element-title\", 4, \"ngIf\"], [\"class\", \"cssCircle plusSign hvr-grow\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"input-field w-100 p-b-0 display-flex justify-end align-center\", 4, \"ngIf\"], [1, \"input-field\", \"col--1of2\"], [\"autocomplete\", \"off\", \"optionLabel\", \"name\", \"filterBy\", \"name\", \"filter\", \"true\", \"placeholder\", \"Select Language\", \"formControlName\", \"language\", \"styleClass\", \"dropdown-blue m-t-0\", 3, \"onChange\", \"options\"], [\"pTemplate\", \"item\"], [\"autocomplete\", \"off\", \"optionLabel\", \"levelName\", \"filterBy\", \"levelName\", \"formControlName\", \"level\", \"styleClass\", \"dropdown-blue m-t-0\", 3, \"options\", \"disabled\", \"placeholder\"], [1, \"cssCircle\", \"plusSign\", \"hvr-grow\", 3, \"click\"], [1, \"input-field\", \"w-100\", \"p-b-0\", \"display-flex\", \"justify-end\", \"align-center\"], [1, \"cssCircle\", \"minusSign\", \"hvr-grow\", 3, \"click\"], [1, \"country-item\"], [1, \"country-name\"], [1, \"input-error\", \"mt-2\", \"font-base\"], [1, \"m-t-15\", 2, \"position\", \"relative\"], [1, \"input-fields\", \"m-t-15\"], [1, \"input-fields\", \"text-right\", \"justify-end\"], [1, \"input-field\", \"text-left\"], [\"formControlName\", \"hasBusinessLevel\", \"styleClass\", \"primary-blue outlined\", \"binary\", \"true\", \"label\", \"I can teach Business Level\"]],\n    template: function ExperienceComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, ExperienceComponent_form_1_Template, 19, 7, \"form\", 1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(2, \"div\", 2)(3, \"button\", 3);\n        i0.ɵɵlistener(\"click\", function ExperienceComponent_Template_button_click_3_listener() {\n          return ctx.goBack();\n        });\n        i0.ɵɵelement(4, \"img\", 4);\n        i0.ɵɵtext(5, \" Back \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"button\", 5);\n        i0.ɵɵlistener(\"click\", function ExperienceComponent_Template_button_click_6_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵtext(7, \" Next \");\n        i0.ɵɵelement(8, \"img\", 6);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n      }\n    },\n    dependencies: [i7.NgForOf, i7.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NumberValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.MinValidator, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i1.FormArrayName, i8.Dropdown, i6.PrimeTemplate, i9.Checkbox, i10.Ripple],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.modal[_ngcontent-%COMP%] {\\n  min-width: 50vw;\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown:not(.p-disabled):hover {\\n  border: 2px solid rgb(228, 185, 84);\\n}\\n\\n  .no-label > .p-button-label {\\n  display: none;\\n}\\n\\ninput[type=file][_ngcontent-%COMP%] {\\n  visibility: hidden;\\n  width: 10px;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%] {\\n  position: relative;\\n  padding: 10px;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%]::-webkit-calendar-picker-indicator {\\n  color: transparent;\\n  background: none;\\n  z-index: 1;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%]:before {\\n  color: transparent;\\n  background: none;\\n  display: block;\\n  font-family: \\\"FontAwesome\\\";\\n  content: \\\"\\\\f073\\\";\\n  \\n\\n  width: 20px;\\n  height: 25px;\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  color: #999;\\n}\\n\\n.image-preview[_ngcontent-%COMP%] {\\n  height: 10rem;\\n  margin: 1rem 0;\\n  border-radius: 50%;\\n  border: 3px solid var(--main-color);\\n}\\n\\n.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 100%;\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-header {\\n  padding: clamp(0.38rem, 0.47vw + 0.28rem, 0.75rem);\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-items .p-dropdown-item {\\n  margin-bottom: clamp(0.38rem, 0.47vw + 0.28rem, 0.75rem);\\n}\\n\\n.country-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: clamp(0.75rem, 0.31vw + 0.69rem, 1rem);\\n  font-size: clamp(0.88rem, 0.16vw + 0.84rem, 1rem);\\n}\\n.country-item[_ngcontent-%COMP%]   .country-name[_ngcontent-%COMP%] {\\n  white-space: pre-line; \\n\\n}\\n.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  \\n\\n\\n\\n\\n\\n  \\n\\n  display: inline-block;\\n  vertical-align: middle;\\n}\\n.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  clip-path: circle();\\n}\\n.country-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 32px;\\n  margin-right: 0.5rem;\\n}\\n.country-item[_ngcontent-%COMP%]   img.smaller[_ngcontent-%COMP%] {\\n  width: 16px;\\n}\\n\\n.info-element[_ngcontent-%COMP%] {\\n  padding: 10px 0;\\n}\\n\\n.btns[_ngcontent-%COMP%] {\\n  bottom: 20px;\\n  right: 20px;\\n}\\n.btns[_ngcontent-%COMP%]   div[_ngcontent-%COMP%] {\\n  margin: 15px;\\n}\\n\\n.white-button[_ngcontent-%COMP%] {\\n  padding: 10px;\\n}\\n\\n.add-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 0px;\\n  top: 26px;\\n  width: 20px;\\n}\\n\\n.e-custom.e-checkbox-wrapper[_ngcontent-%COMP%]   .e-frame[_ngcontent-%COMP%] {\\n  height: 24px;\\n  line-height: 19px;\\n  width: 24px;\\n  border-radius: 50px;\\n}\\n.e-custom.e-checkbox-wrapper[_ngcontent-%COMP%]   .e-check[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  background-color: #2fb9d3;\\n  outline: 2px solid #2fb9d3;\\n  outline-offset: 3px;\\n}\\n.e-custom.e-checkbox-wrapper[_ngcontent-%COMP%]   .e-label[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n}\\n\\n.form-array-title[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  color: var(--main-color);\\n  font-size: 20px;\\n  margin-top: 30px;\\n  width: 100%;\\n  border-bottom: 1px solid var(--main-color);\\n  padding-bottom: 5px;\\n}\\n\\n.w-49[_ngcontent-%COMP%] {\\n  width: 48% !important;\\n  flex-basis: 48% !important;\\n}\\n\\n.trash[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 5px;\\n  bottom: 10px;\\n  cursor: pointer;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["UntypedFormArray", "UntypedFormControl", "UntypedFormGroup", "Validators", "Observable", "SubSink", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ExperienceComponent_form_1_div_3_div_6_Template_div_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "addLanguageSpeak", "ExperienceComponent_form_1_div_3_div_7_Template_div_click_1_listener", "_r4", "i_r5", "index", "removeLanguageSpeak", "ɵɵadvance", "ɵɵtextInterpolate", "language_r6", "name", "level_r7", "levelName", "ɵɵelementContainerStart", "ɵɵelement", "ɵɵtemplate", "ExperienceComponent_form_1_div_3_div_5_Template", "ExperienceComponent_form_1_div_3_div_6_Template", "ExperienceComponent_form_1_div_3_div_7_Template", "ExperienceComponent_form_1_div_3_Template_p_dropdown_onChange_9_listener", "$event", "_r1", "handleSpeakLanguageChange", "ExperienceComponent_form_1_div_3_ng_template_10_Template", "ExperienceComponent_form_1_div_3_ng_template_13_Template", "ɵɵproperty", "languages", "filteredLanguageSpeakLevels", "disableSpeakLevelsDropdown", "ExperienceComponent_form_1_div_6_div_5_Template_div_click_0_listener", "_r9", "addLanguageTeach", "ExperienceComponent_form_1_div_6_div_6_Template_div_click_1_listener", "_r10", "i_r11", "removeLanguageTeach", "language_r12", "level_r13", "ExperienceComponent_form_1_div_6_div_4_Template", "ExperienceComponent_form_1_div_6_div_5_Template", "ExperienceComponent_form_1_div_6_div_6_Template", "ExperienceComponent_form_1_div_6_Template_p_dropdown_onChange_8_listener", "_r8", "handleTeachLanguageChange", "ExperienceComponent_form_1_div_6_ng_template_9_Template", "ExperienceComponent_form_1_div_6_ng_template_12_Template", "teachLevelOptions", "disableTeachLevelsDropdown", "ExperienceComponent_form_1_div_3_Template", "ExperienceComponent_form_1_div_4_Template", "ExperienceComponent_form_1_div_6_Template", "ExperienceComponent_form_1_div_7_Template", "ExperienceComponent_form_1_div_13_Template", "ExperienceComponent_form_1_div_18_Template", "form", "languagesSpeakForm", "controls", "ifFieldValid", "languagesTeachForm", "ExperienceComponent", "constructor", "fb", "router", "teacherService", "generalService", "toast", "confirmationService", "subs", "teacherLanguagesSpeak", "teacherLanguagesTeach", "languageSpeakError", "languageTeachError", "isLoading", "tryToSave", "availableLanguageLevels", "getLanguageLevels", "filteredLanguageTeachLevels", "formChanged", "ngOnInit", "setCurrentStepIndex", "add", "getTeacherApplicationStep3", "subscribe", "res", "createForm", "updateStepFormValues", "initFormChangedListener", "submitOnMenuClickListener", "onSubmit", "ngOnDestroy", "unsubscribe", "setSubmitOnMenuClickListener", "get", "methods", "validators", "required", "years", "object", "patchValue", "teachingMethods", "yearsExperience", "isNullishObject", "languageSpeak", "for<PERSON>ach", "element", "elementFound", "filter", "i", "level", "setfilteredLanguageSpeakLevels", "language", "code", "push", "group", "languageTeach", "console", "log", "setfilteredLanguageTeachLevels", "languageCode", "showOnlyOnCode", "length", "includes", "Error", "hasBusinessLevel", "removeAt", "status", "valid", "experienceValuesToSubmit", "value", "updateAPITeacherApplicationStep3", "setShowToastmessage", "severity", "summary", "detail", "navigateByUrl", "replaceUrl", "field", "setStepValid", "invalid", "touched", "goBack", "event", "languageIndex", "valueChanges", "val", "dirty", "canDeactivate", "observer", "confirm", "header", "key", "message", "accept", "next", "complete", "reject", "_", "ɵɵdirectiveInject", "i1", "UntypedFormBuilder", "i2", "Router", "i3", "TeacherApplicationService", "i4", "GeneralService", "i5", "ToastService", "i6", "ConfirmationService", "_2", "selectors", "decls", "vars", "consts", "template", "ExperienceComponent_Template", "rf", "ctx", "ExperienceComponent_form_1_Template", "ExperienceComponent_Template_button_click_3_listener", "ExperienceComponent_Template_button_click_6_listener"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\teacher-application\\experience\\experience.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\teacher-application\\experience\\experience.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { UntypedForm<PERSON>rray, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { ConfirmationService } from 'primeng/api';\r\nimport { Observable } from 'rxjs';\r\nimport { Language } from 'src/app/core/models/general.model';\r\nimport { LanguageLevel, Teacher, TeacherApplicationStep3 } from 'src/app/core/models/teacher.model';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { TeacherApplicationService } from 'src/app/core/services/teacher-application.service';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { SubSink } from 'subsink';\r\n\r\n@Component({\r\n  selector: 'app-experience',\r\n  templateUrl: './experience.component.html',\r\n  styleUrls: ['./experience.component.scss']\r\n})\r\nexport class ExperienceComponent  implements OnInit {\r\n  private subs = new SubSink();\r\n  public form: UntypedFormGroup = new UntypedFormGroup({});\r\n  public teacherLanguagesSpeak: LanguageLevel[] = [];\r\n  public teacherLanguagesTeach: LanguageLevel[] = [];\r\n  public languageSpeakError: boolean = false;\r\n  public languageTeachError: boolean = false;\r\n  public teacher?: Teacher;\r\n  public isLoading: boolean = true;\r\n  public tryToSave: boolean = false;\r\n  public languages: Language[] = this.generalService.languages;\r\n  public availableLanguageLevels: any[] = this.generalService.getLanguageLevels();\r\n  public filteredLanguageSpeakLevels: any[] = [];\r\n  public filteredLanguageTeachLevels: any[] = [];\r\n  disableSpeakLevelsDropdown: boolean[] = [true];\r\n  disableTeachLevelsDropdown: boolean[] = [true];\r\n  formChanged = false;\r\n\r\n  teachLevelOptions = [\r\n    {\r\n      levelName: \"I teach only beginner-level students (A1 - B1)\",\r\n      \"showOnlyOnCode\": [],\r\n      \"visible\": true\r\n    },\r\n    {\r\n      levelName: \"I teach only advanced-level students (B2 - C2)\",\r\n      \"showOnlyOnCode\": [],\r\n      \"visible\": true\r\n    },\r\n    {\r\n      levelName: \"I teach all levels (A1 - C2)\",\r\n      \"showOnlyOnCode\": [],\r\n      \"visible\": true\r\n    },\r\n  ];\r\n\r\n  constructor(\r\n    private fb: UntypedFormBuilder,\r\n    private router: Router,\r\n    private teacherService: TeacherApplicationService,\r\n    private generalService: GeneralService,\r\n    private toast: ToastService,\r\n    private confirmationService: ConfirmationService,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.teacherService.setCurrentStepIndex(2);\r\n\r\n    this.subs.add(this.teacherService.getTeacherApplicationStep3().subscribe(res => {\r\n      this.isLoading = false;\r\n      this.createForm();\r\n      this.updateStepFormValues(res);\r\n      this.initFormChangedListener();\r\n    }));\r\n\r\n    this.subs.add(this.teacherService.submitOnMenuClickListener.subscribe(res => {\r\n      if (res) {\r\n        this.onSubmit();\r\n      }\r\n    }));\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n    this.teacherService.setSubmitOnMenuClickListener(false);\r\n  }\r\n\r\n  get languagesSpeakForm(): UntypedFormArray {\r\n    return this.form.get('languagesSpeakForm') as UntypedFormArray;\r\n  }\r\n\r\n  get languagesTeachForm(): UntypedFormArray {\r\n    return this.form.get('languagesTeachForm') as UntypedFormArray;\r\n  }\r\n\r\n  /**\r\n   * Initializes the form for the application\r\n   *\r\n   */\r\n  createForm() {\r\n    this.form = new UntypedFormGroup({\r\n      methods: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      years: new UntypedFormControl(1, {\r\n        validators: [Validators.required]\r\n      }),\r\n      languagesSpeakForm: new UntypedFormArray([]),\r\n      languagesTeachForm: new UntypedFormArray([])\r\n    });\r\n  }\r\n\r\n  /**\r\n * Updates the form values for the step 3 of the teacher application process\r\n *\r\n * @param {TeacherApplicationStep3} object - Object containing the updated values for the form\r\n *\r\n */\r\n  updateStepFormValues(object: TeacherApplicationStep3) {\r\n\r\n    this.form.get('methods')?.patchValue(object.teachingMethods);\r\n    this.form.get('years')?.patchValue(object.yearsExperience);\r\n    if (this.generalService.isNullishObject(object.languageSpeak)) {\r\n      this.addLanguageSpeak()\r\n    } else {\r\n      this.filteredLanguageSpeakLevels = [];\r\n\r\n      object.languageSpeak.forEach((element, index) => {\r\n        const elementFound = this.availableLanguageLevels.filter(i => {\r\n          return ((i.levelName === element.level.levelName));\r\n        });\r\n        element.level = elementFound[0];\r\n        this.disableSpeakLevelsDropdown[index] = false;\r\n        this.setfilteredLanguageSpeakLevels(index, element.language.code);\r\n        this.languagesSpeakForm.push(this.fb.group(element))\r\n      });\r\n    }\r\n    if (this.generalService.isNullishObject(object.languageTeach)) {\r\n      this.addLanguageTeach();\r\n    } else {\r\n\r\n      // this.filteredLanguageTeachLevels = this.teachLevelOptions;\r\n      object.languageTeach.forEach((element, index) => {\r\n\r\n        const elementFound = this.teachLevelOptions.filter(i => {\r\n          return ((i.levelName === element.level.levelName));\r\n        });\r\n        element.level = elementFound[0];\r\n\r\n        // element.level = {\r\n        //   levelName: element.levelName,\r\n        // }\r\n\r\n        console.log(element);\r\n        this.disableTeachLevelsDropdown[index] = false;\r\n        this.setfilteredLanguageTeachLevels(index);\r\n        this.languagesTeachForm.push(this.fb.group(element))\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Sets the filtered language speak levels based on the language code\r\n   *\r\n   * @param {number} index - Index of the language in the array of languages\r\n   * @param {string} [languageCode] - Code of the language to filter the levels for\r\n   *\r\n   */\r\n  setfilteredLanguageSpeakLevels(index: number, languageCode?: string) {\r\n    this.filteredLanguageSpeakLevels[index] = this.availableLanguageLevels.\r\n      filter(i => {\r\n        return ((i.showOnlyOnCode.length === 0) || (i.showOnlyOnCode.includes(languageCode)));\r\n      });\r\n      console.log(index, this.filteredLanguageSpeakLevels[index]);\r\n  }\r\n\r\n  /**\r\n   * Sets the filtered language teach levels\r\n   *\r\n   * @param {number} index - Index of the language in the array of languages\r\n   *\r\n   */\r\n  setfilteredLanguageTeachLevels(index: number, languageCode?: string) {\r\n    \r\n    this.filteredLanguageTeachLevels[index] = this.teachLevelOptions.\r\n      filter(i => {\r\n        return (i);\r\n      });\r\n      console.log(index, this.filteredLanguageTeachLevels[index]);\r\n    // this.filteredLanguageTeachLevels[index] = this.availableLanguageLevels.\r\n    //   filter(i => {\r\n    //     return (i.visible);\r\n    //   });\r\n  }\r\n\r\n  /**\r\n   * Adds a new form group for spoken languages\r\n   *\r\n   * @throws {Error} If FormGroup or FormControl classes are not defined\r\n   */\r\n  addLanguageSpeak() {\r\n    if (!UntypedFormGroup || !UntypedFormControl) {\r\n      throw new Error('FormGroup or FormControl classes are not defined');\r\n    }\r\n    const group = new UntypedFormGroup({\r\n      language: new UntypedFormControl('', Validators.required),\r\n      level: new UntypedFormControl('', Validators.required),\r\n      hasBusinessLevel: new UntypedFormControl(false)\r\n    });\r\n    this.languagesSpeakForm.push(group);\r\n    this.disableSpeakLevelsDropdown[this.languagesSpeakForm.length - 1] = true;\r\n  }\r\n\r\n  /**\r\n   * Removes a form group for spoken languages\r\n   *\r\n   * @param {number} index - Index of the form group in the form array\r\n   */\r\n  removeLanguageSpeak(index: number) {\r\n    this.languagesSpeakForm.removeAt(index);\r\n  }\r\n\r\n  /**\r\n   * Adds a new form group for taught languages\r\n   *\r\n   * @throws {Error} If FormGroup or FormControl classes are not defined\r\n   */\r\n  addLanguageTeach() {\r\n    if (!UntypedFormGroup || !UntypedFormControl) {\r\n      throw new Error('FormGroup or FormControl classes are not defined');\r\n    }\r\n    const group = new UntypedFormGroup({\r\n      language: new UntypedFormControl('', Validators.required),\r\n      level: new UntypedFormControl('', Validators.required),\r\n      hasBusinessLevel: new UntypedFormControl(false)\r\n    });\r\n    this.languagesTeachForm.push(group);\r\n    this.disableTeachLevelsDropdown[this.languagesTeachForm.length - 1] = true;\r\n  }\r\n\r\n  /**\r\n   * Removes a form group for taught languages\r\n   *\r\n   * @param {number} index - Index of the form group in the form array\r\n   */\r\n  removeLanguageTeach(index: number) {\r\n    this.languagesTeachForm.removeAt(index);\r\n  }\r\n\r\n  onSubmit() {\r\n    this.tryToSave = true;\r\n    this.languageSpeakError = this.form.controls.languagesSpeakForm.status === 'INVALID' ? true : false;\r\n    this.languageTeachError = this.form.controls.languagesTeachForm.status === 'INVALID' ? true : false;\r\n\r\n    if (!this.form.valid) {\r\n      return;\r\n    }\r\n\r\n    const experienceValuesToSubmit = {\r\n      languageSpeak: this.form.value.languagesSpeakForm,\r\n      languageTeach: this.form.value.languagesTeachForm,\r\n      yearsExperience: this.form.value.years,\r\n      teachingMethods: this.form.value.methods,\r\n    };\r\n\r\n    this.subs.add(this.teacherService.updateAPITeacherApplicationStep3(experienceValuesToSubmit).subscribe(res => {\r\n      console.log(res);\r\n      if (res) {\r\n        this.toast.setShowToastmessage({\r\n          severity: 'success',\r\n          summary: '',\r\n          detail: 'Your Experience info were updated.'\r\n        });\r\n        this.router.navigateByUrl('/teacher/resume', { replaceUrl: true });\r\n      }\r\n    }));\r\n  }\r\n\r\n  ifFieldValid(field: string) {\r\n    this.teacherService.setStepValid(2, this.form, 'teacher-experience-route');\r\n    return ((this.form.get(field)?.invalid && this.form.get(field)?.touched) || (this.tryToSave && this.form.get(field)?.invalid))\r\n  }\r\n\r\n  goBack() {\r\n    this.router.navigateByUrl('/teacher/education', { replaceUrl: true });\r\n  }\r\n\r\n  /**\r\n  Handles the change in language selection for speaking.\r\n  @param {Object} event - The event object containing the selected language code.\r\n  @param {number} languageIndex - The index of the selected language in the list.\r\n  @returns {void}\r\n  */\r\n  handleSpeakLanguageChange(event: { value: { code: string } }, languageIndex: number) {\r\n    this.disableSpeakLevelsDropdown[languageIndex] = false;\r\n    this.filteredLanguageSpeakLevels[languageIndex] = this.availableLanguageLevels.\r\n      filter(i => {\r\n        return (i.showOnlyOnCode.length === 0) || (i.showOnlyOnCode.includes(event.value.code));\r\n      });\r\n  }\r\n\r\n  /**\r\n  Handles the change in language selection for teaching.\r\n  @param {Object} event - The event object containing the selected language code.\r\n  @param {number} languageIndex - The index of the selected language in the list.\r\n  @returns {void}\r\n  */\r\n  handleTeachLanguageChange(event: { value: { code: string } }, languageIndex: number) {\r\n    this.disableTeachLevelsDropdown[languageIndex] = false;\r\n    // this.filteredLanguageTeachLevels[languageIndex] = this.availableLanguageLevels.\r\n    //   filter(i => {\r\n    //     return (i.showOnlyOnCode.length === 0) || (i.showOnlyOnCode.includes(event.value.code));\r\n    //   });\r\n  }\r\n\r\n  initFormChangedListener() {\r\n    this.subs.add(this.form.valueChanges.subscribe(val => {\r\n      if(this.form.dirty) {\r\n        console.log(val);\r\n        this.formChanged = true;\r\n      }\r\n    }));\r\n  }\r\n\r\n  canDeactivate(): Observable<boolean> | boolean {\r\n    if (this.formChanged && !this.tryToSave) {\r\n      return new Observable((observer: any) => {\r\n        this.confirmationService.confirm({\r\n          header: '',\r\n          key: 'stepLeaveConfirmation',\r\n          message: 'Are you sure you want to leave this page? Your changes will be lost.',\r\n          accept: () => {\r\n            observer.next(true);\r\n            observer.complete();\r\n          },\r\n          reject: () => {\r\n            observer.next(false);\r\n            observer.complete();\r\n          }\r\n        });\r\n      });\r\n    } else {\r\n      return (true);\r\n    }\r\n  }\r\n}\r\n", "<div class=\"profile-info md:mt-3\">\r\n    <form [formGroup]=\"form\" *ngIf=\"!isLoading\">\r\n      <div class=\"profile-info-section\">\r\n        <ng-container formArrayName=\"languagesSpeakForm\">\r\n          <div\r\n            *ngFor=\"let _ of languagesSpeakForm.controls; index as i\"\r\n            style=\"position: relative;\"\r\n          >\r\n            <!-- <img *ngIf=\"i===0\" (click)=\"addLanguageSpeak()\" src=\"/assets/icons/add.png\" class=\"add hvr-grow add-btn\"> -->\r\n  \r\n            <ng-container [formGroupName]=\"i\">\r\n              <div class=\"input-element-title\"></div>\r\n              <div class=\"input-fields\">\r\n                <div\r\n                  class=\"input-field w-100 p-b-0 display-flex justify-space-between align-center\"\r\n                >\r\n                  <div class=\"input-element-title\" *ngIf=\"i===0\">\r\n                    Additional Languages you speak & levels *\r\n                  </div>\r\n                  <div\r\n                    *ngIf=\"i===0\"\r\n                    class=\"cssCircle plusSign hvr-grow\"\r\n                    (click)=\"addLanguageSpeak()\">\r\n                    &#43;\r\n                  </div>\r\n                </div>\r\n  \r\n                <div\r\n                  *ngIf=\"i!==0\"\r\n                  class=\"input-field w-100 p-b-0 display-flex justify-end align-center\">\r\n                  <div\r\n                    class=\"cssCircle minusSign hvr-grow\"\r\n                    (click)=\"removeLanguageSpeak(i)\">\r\n                    &#8211;\r\n                  </div>\r\n                </div>\r\n                <div class=\"input-field col--1of2\">\r\n                  <p-dropdown\r\n                    autocomplete=\"off\"\r\n                    [options]=\"languages\"\r\n                    optionLabel=\"name\"\r\n                    filterBy=\"name\"\r\n                    (onChange)=\"handleSpeakLanguageChange($event, i)\"\r\n                    filter=\"true\"\r\n                    placeholder=\"Select Language\"\r\n                    formControlName=\"language\"\r\n                    styleClass=\"dropdown-blue m-t-0\">\r\n                    <ng-template let-language pTemplate=\"item\">\r\n                      <div class=\"country-item\">\r\n                        <div class=\"country-name\">{{language.name}}</div>\r\n                      </div>\r\n                    </ng-template>\r\n                  </p-dropdown>\r\n                </div>\r\n                <div class=\"input-field col--1of2\">\r\n                  <!-- {{filteredLanguageSpeakLevels[i] | json}} -->\r\n                  <p-dropdown\r\n                    autocomplete=\"off\"\r\n                    [options]=\"filteredLanguageSpeakLevels[i]\"\r\n                    [disabled]=\"disableSpeakLevelsDropdown[i]\"\r\n                    optionLabel=\"levelName\"\r\n                    filterBy=\"levelName\"\r\n                    [placeholder]=\"disableSpeakLevelsDropdown[i] ? 'Select a Language before Level' : 'Select Level'\"\r\n                    formControlName=\"level\"\r\n                    styleClass=\"dropdown-blue m-t-0\">\r\n                    <ng-template let-level pTemplate=\"item\">\r\n                      <div class=\"country-item\">\r\n                        <div class=\"country-name\">{{level.levelName}}</div>\r\n                      </div>\r\n                    </ng-template>\r\n                  </p-dropdown>\r\n                  <!-- <div *ngIf=\"i!==0\" (click)=\"removeLanguageSpeak(i)\" class=\"trash\"><img src=\"/assets/icons/trash.png\" style=\"width:30px\"></div> -->\r\n                </div>\r\n              </div>\r\n              <!-- <div class=\"input-fields text-right justify-end\">\r\n                <div class=\"input-field text-left\">\r\n                  <div class=\"\">\r\n                    <ejs-checkbox\r\n                      cssClass=\"e-custom\"\r\n                      formControlName=\"hasBusinessLevel\"\r\n                      label=\"I can teach Business Level\"\r\n                    ></ejs-checkbox>\r\n                  </div>\r\n                </div>\r\n              </div> -->\r\n            </ng-container>\r\n  \r\n            <!-- <div *ngIf=\"languageSpeakError\" class=\"input-error mt-2 font-base\">* Language & Level fields are required</div> -->\r\n          </div>\r\n        </ng-container>\r\n        <div *ngIf=\"ifFieldValid('languagesSpeakForm')\" class=\"input-error mt-2 font-base\">\r\n          * Language & Level fields are required\r\n        </div>\r\n  \r\n        <!-- <div class=\"form-array-title\">Languages you Teach</div> -->\r\n        <ng-container formArrayName=\"languagesTeachForm\">\r\n          <div\r\n            class=\"m-t-15\"\r\n            *ngFor=\"let _ of languagesTeachForm.controls; index as i\"\r\n            style=\"position: relative;\">\r\n            <!-- <img *ngIf=\"i===0\" (click)=\"addLanguageTeach()\" src=\"/assets/icons/add.png\" class=\"add hvr-grow add-btn\"> -->\r\n  \r\n            <ng-container [formGroupName]=\"i\">\r\n              <div class=\"input-fields m-t-15\">\r\n                <div\r\n                  class=\"input-field w-100 p-b-0 display-flex justify-space-between align-center\">\r\n                  <div class=\"input-element-title\" *ngIf=\"i===0\">\r\n                    Languages you teach & levels *\r\n                  </div>\r\n                  <div\r\n                    *ngIf=\"i===0\"\r\n                    class=\"cssCircle plusSign hvr-grow\"\r\n                    (click)=\"addLanguageTeach()\">\r\n                    &#43;\r\n                  </div>\r\n                </div>\r\n  \r\n                <div\r\n                  *ngIf=\"i!==0\"\r\n                  class=\"input-field w-100 p-b-0 display-flex justify-end align-center\">\r\n                  <div\r\n                    class=\"cssCircle minusSign hvr-grow\"\r\n                    (click)=\"removeLanguageTeach(i)\">\r\n                    &#8211;\r\n                  </div>\r\n                </div>\r\n                <div class=\"input-field col--1of2\">\r\n                  <!-- <div class=\"input-element-title\">* Language</div> -->\r\n  \r\n                  <p-dropdown\r\n                    autocomplete=\"off\"\r\n                    [options]=\"languages\"\r\n                    optionLabel=\"name\"\r\n                    filterBy=\"name\"\r\n                    filter=\"true\"\r\n                    (onChange)=\"handleTeachLanguageChange($event, i)\"\r\n                    placeholder=\"Select Language\"\r\n                    formControlName=\"language\"\r\n                    styleClass=\"dropdown-blue m-t-0\">\r\n                    <ng-template let-language pTemplate=\"item\">\r\n                      <div class=\"country-item\">\r\n                        <div class=\"country-name\">{{language.name}}</div>\r\n                      </div>\r\n                    </ng-template>\r\n                  </p-dropdown>\r\n                </div>\r\n                <div class=\"input-field col--1of2\">\r\n                  <!-- <div class=\"input-element-title\">* Level</div> -->\r\n                  \r\n                  <p-dropdown\r\n                    autocomplete=\"off\"\r\n                    [options]=\"teachLevelOptions\"\r\n                    [disabled]=\"disableTeachLevelsDropdown[i]\"\r\n                    optionLabel=\"levelName\"\r\n                    filterBy=\"levelName\"\r\n                    [placeholder]=\"disableTeachLevelsDropdown[i] ? 'Select a Language before Level' : 'Select Level'\"\r\n                    formControlName=\"level\"\r\n                    styleClass=\"dropdown-blue m-t-0\">\r\n                    <ng-template let-level pTemplate=\"item\">\r\n                      <div class=\"country-item\">\r\n                        <div class=\"country-name\">{{level.levelName}}</div>\r\n                      </div>\r\n                    </ng-template>\r\n                  </p-dropdown>\r\n                </div>\r\n                <!-- <div *ngIf=\"i!==0\" (click)=\"removeLanguageTeach(i)\" class=\"trash\"><img src=\"/assets/icons/trash.png\" style=\"width:30px\"></div> -->\r\n              </div>\r\n              <div class=\"input-fields text-right justify-end\">\r\n                <div class=\"input-field text-left\">\r\n                  <p-checkbox \r\n                  formControlName=\"hasBusinessLevel\" \r\n                  styleClass=\"primary-blue outlined\" \r\n                  binary=\"true\" \r\n                  label=\"I can teach Business Level\"></p-checkbox>\r\n                </div>\r\n              </div>\r\n            </ng-container>\r\n\r\n            <!-- <div *ngIf=\"languageTeachError\" class=\"input-error mt-2 font-base\">* Language & Level fields are required</div> -->\r\n          </div>\r\n        </ng-container>\r\n        <div *ngIf=\"ifFieldValid('languagesTeachForm')\" class=\"input-error mt-2 font-base\">\r\n          * Language & Level fields are required\r\n        </div>\r\n  \r\n        <div class=\"input-fields\">\r\n          <div class=\"input-field w-100\">\r\n            <div class=\"input-element-title\">\r\n               Describe your teaching methods *\r\n            </div>\r\n            <textarea\r\n              class=\"input-element\"\r\n              formControlName=\"methods\"\r\n              type=\"text\"\r\n              rows=\"5\"\r\n            ></textarea>\r\n            <div *ngIf=\"ifFieldValid('methods')\" class=\"input-error mt-2 font-base\">\r\n              * Teaching Methods are required\r\n            </div>\r\n          </div>\r\n          <div class=\"input-field\">\r\n            <div class=\"input-element-title\">\r\n               Years of experience as teacher *\r\n            </div>\r\n            <input\r\n              class=\"input-element\"\r\n              formControlName=\"years\"\r\n              type=\"number\"\r\n              min=\"1\"/>\r\n            <div *ngIf=\"ifFieldValid('years')\" class=\"input-error mt-2 font-base\">\r\n              * Years of experience field is required\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </form>\r\n  </div>\r\n  \r\n  <div class=\"btns md:ml-5 mt-4\">\r\n    <button pRipple class=\"rounded-blue-button transparent\" (click)=\"goBack()\">\r\n      <img src=\"/assets/icons/arrow-left-blue.svg\" /> Back\r\n    </button>\r\n    <button pRipple (click)=\"onSubmit()\" class=\"rounded-blue-button\">\r\n      Next <img src=\"/assets/icons/arrow-right.svg\" />\r\n    </button>\r\n    <!-- <div (click)=\"onSubmit()\" class=\"light-purple-button\">Next</div> -->\r\n  </div>\r\n  "], "mappings": "AACA,SAASA,gBAAgB,EAAsBC,kBAAkB,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,gBAAgB;AAGvH,SAASC,UAAU,QAAQ,MAAM;AAMjC,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;;;ICMfC,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAE,MAAA,kDACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IACNH,EAAA,CAAAC,cAAA,cAG+B;IAA7BD,EAAA,CAAAI,UAAA,mBAAAC,qEAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,gBAAA,EAAkB;IAAA,EAAC;IAC5BX,EAAA,CAAAE,MAAA,UACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAMNH,EAHF,CAAAC,cAAA,cAEwE,cAGnC;IAAjCD,EAAA,CAAAI,UAAA,mBAAAQ,qEAAA;MAAAZ,EAAA,CAAAM,aAAA,CAAAO,GAAA;MAAA,MAAAC,IAAA,GAAAd,EAAA,CAAAS,aAAA,GAAAM,KAAA;MAAA,MAAAP,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAQ,mBAAA,CAAAF,IAAA,CAAsB;IAAA,EAAC;IAChCd,EAAA,CAAAE,MAAA,eACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAcEH,EADF,CAAAC,cAAA,cAA0B,cACE;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAC7CF,EAD6C,CAAAG,YAAA,EAAM,EAC7C;;;;IADsBH,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAAkB,iBAAA,CAAAC,WAAA,CAAAC,IAAA,CAAiB;;;;;IAkB3CpB,EADF,CAAAC,cAAA,cAA0B,cACE;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAC/CF,EAD+C,CAAAG,YAAA,EAAM,EAC/C;;;;IADsBH,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAkB,iBAAA,CAAAG,QAAA,CAAAC,SAAA,CAAmB;;;;;;IA/D3DtB,EAAA,CAAAC,cAAA,cAGC;IAGCD,EAAA,CAAAuB,uBAAA,OAAkC;IAChCvB,EAAA,CAAAwB,SAAA,cAAuC;IAErCxB,EADF,CAAAC,cAAA,cAA0B,cAGvB;IAICD,EAHA,CAAAyB,UAAA,IAAAC,+CAAA,kBAA+C,IAAAC,+CAAA,kBAMhB;IAGjC3B,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAyB,UAAA,IAAAG,+CAAA,kBAEwE;IAQtE5B,EADF,CAAAC,cAAA,cAAmC,qBAUE;IAJjCD,EAAA,CAAAI,UAAA,sBAAAyB,yEAAAC,MAAA;MAAA,MAAAhB,IAAA,GAAAd,EAAA,CAAAM,aAAA,CAAAyB,GAAA,EAAAhB,KAAA;MAAA,MAAAP,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAYF,MAAA,CAAAwB,yBAAA,CAAAF,MAAA,EAAAhB,IAAA,CAAoC;IAAA,EAAC;IAKjDd,EAAA,CAAAyB,UAAA,KAAAQ,wDAAA,0BAA2C;IAM/CjC,EADE,CAAAG,YAAA,EAAa,EACT;IAGJH,EAFF,CAAAC,cAAA,eAAmC,sBAUE;IACjCD,EAAA,CAAAyB,UAAA,KAAAS,wDAAA,0BAAwC;IAQ9ClC,EAHI,CAAAG,YAAA,EAAa,EAET,EACF;;IAeVH,EAAA,CAAAG,YAAA,EAAM;;;;;IA9EUH,EAAA,CAAAiB,SAAA,EAAmB;IAAnBjB,EAAA,CAAAmC,UAAA,kBAAArB,IAAA,CAAmB;IAMOd,EAAA,CAAAiB,SAAA,GAAW;IAAXjB,EAAA,CAAAmC,UAAA,SAAArB,IAAA,OAAW;IAI1Cd,EAAA,CAAAiB,SAAA,EAAW;IAAXjB,EAAA,CAAAmC,UAAA,SAAArB,IAAA,OAAW;IAQbd,EAAA,CAAAiB,SAAA,EAAW;IAAXjB,EAAA,CAAAmC,UAAA,SAAArB,IAAA,OAAW;IAWVd,EAAA,CAAAiB,SAAA,GAAqB;IAArBjB,EAAA,CAAAmC,UAAA,YAAA3B,MAAA,CAAA4B,SAAA,CAAqB;IAmBrBpC,EAAA,CAAAiB,SAAA,GAA0C;IAI1CjB,EAJA,CAAAmC,UAAA,YAAA3B,MAAA,CAAA6B,2BAAA,CAAAvB,IAAA,EAA0C,aAAAN,MAAA,CAAA8B,0BAAA,CAAAxB,IAAA,EACA,gBAAAN,MAAA,CAAA8B,0BAAA,CAAAxB,IAAA,sDAGuD;;;;;IA4B7Gd,EAAA,CAAAC,cAAA,cAAmF;IACjFD,EAAA,CAAAE,MAAA,+CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAcIH,EAAA,CAAAC,cAAA,cAA+C;IAC7CD,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IACNH,EAAA,CAAAC,cAAA,cAG+B;IAA7BD,EAAA,CAAAI,UAAA,mBAAAmC,qEAAA;MAAAvC,EAAA,CAAAM,aAAA,CAAAkC,GAAA;MAAA,MAAAhC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAiC,gBAAA,EAAkB;IAAA,EAAC;IAC5BzC,EAAA,CAAAE,MAAA,UACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAMNH,EAHF,CAAAC,cAAA,cAEwE,cAGnC;IAAjCD,EAAA,CAAAI,UAAA,mBAAAsC,qEAAA;MAAA1C,EAAA,CAAAM,aAAA,CAAAqC,IAAA;MAAA,MAAAC,KAAA,GAAA5C,EAAA,CAAAS,aAAA,GAAAM,KAAA;MAAA,MAAAP,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAqC,mBAAA,CAAAD,KAAA,CAAsB;IAAA,EAAC;IAChC5C,EAAA,CAAAE,MAAA,eACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAgBEH,EADF,CAAAC,cAAA,cAA0B,cACE;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAC7CF,EAD6C,CAAAG,YAAA,EAAM,EAC7C;;;;IADsBH,EAAA,CAAAiB,SAAA,GAAiB;IAAjBjB,EAAA,CAAAkB,iBAAA,CAAA4B,YAAA,CAAA1B,IAAA,CAAiB;;;;;IAmB3CpB,EADF,CAAAC,cAAA,cAA0B,cACE;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAC/CF,EAD+C,CAAAG,YAAA,EAAM,EAC/C;;;;IADsBH,EAAA,CAAAiB,SAAA,GAAmB;IAAnBjB,EAAA,CAAAkB,iBAAA,CAAA6B,SAAA,CAAAzB,SAAA,CAAmB;;;;;;IAhE3DtB,EAAA,CAAAC,cAAA,cAG8B;IAG5BD,EAAA,CAAAuB,uBAAA,OAAkC;IAE9BvB,EADF,CAAAC,cAAA,cAAiC,cAEmD;IAIhFD,EAHA,CAAAyB,UAAA,IAAAuB,+CAAA,kBAA+C,IAAAC,+CAAA,kBAMhB;IAGjCjD,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAyB,UAAA,IAAAyB,+CAAA,kBAEwE;IAUtElD,EAHF,CAAAC,cAAA,cAAmC,qBAYE;IAHjCD,EAAA,CAAAI,UAAA,sBAAA+C,yEAAArB,MAAA;MAAA,MAAAc,KAAA,GAAA5C,EAAA,CAAAM,aAAA,CAAA8C,GAAA,EAAArC,KAAA;MAAA,MAAAP,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAYF,MAAA,CAAA6C,yBAAA,CAAAvB,MAAA,EAAAc,KAAA,CAAoC;IAAA,EAAC;IAIjD5C,EAAA,CAAAyB,UAAA,IAAA6B,uDAAA,0BAA2C;IAM/CtD,EADE,CAAAG,YAAA,EAAa,EACT;IAIJH,EAHF,CAAAC,cAAA,eAAmC,sBAWE;IACjCD,EAAA,CAAAyB,UAAA,KAAA8B,wDAAA,0BAAwC;IAQ9CvD,EAHI,CAAAG,YAAA,EAAa,EACT,EAEF;IAEJH,EADF,CAAAC,cAAA,eAAiD,eACZ;IACjCD,EAAA,CAAAwB,SAAA,sBAIgD;IAEpDxB,EADE,CAAAG,YAAA,EAAM,EACF;;IAIVH,EAAA,CAAAG,YAAA,EAAM;;;;;IA7EUH,EAAA,CAAAiB,SAAA,EAAmB;IAAnBjB,EAAA,CAAAmC,UAAA,kBAAAS,KAAA,CAAmB;IAIO5C,EAAA,CAAAiB,SAAA,GAAW;IAAXjB,EAAA,CAAAmC,UAAA,SAAAS,KAAA,OAAW;IAI1C5C,EAAA,CAAAiB,SAAA,EAAW;IAAXjB,EAAA,CAAAmC,UAAA,SAAAS,KAAA,OAAW;IAQb5C,EAAA,CAAAiB,SAAA,EAAW;IAAXjB,EAAA,CAAAmC,UAAA,SAAAS,KAAA,OAAW;IAaV5C,EAAA,CAAAiB,SAAA,GAAqB;IAArBjB,EAAA,CAAAmC,UAAA,YAAA3B,MAAA,CAAA4B,SAAA,CAAqB;IAoBrBpC,EAAA,CAAAiB,SAAA,GAA6B;IAI7BjB,EAJA,CAAAmC,UAAA,YAAA3B,MAAA,CAAAgD,iBAAA,CAA6B,aAAAhD,MAAA,CAAAiD,0BAAA,CAAAb,KAAA,EACa,gBAAApC,MAAA,CAAAiD,0BAAA,CAAAb,KAAA,sDAGuD;;;;;IA0B7G5C,EAAA,CAAAC,cAAA,cAAmF;IACjFD,EAAA,CAAAE,MAAA,+CACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAaFH,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAE,MAAA,wCACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAWNH,EAAA,CAAAC,cAAA,cAAsE;IACpED,EAAA,CAAAE,MAAA,gDACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAjNZH,EADF,CAAAC,cAAA,cAA4C,aACR;IAChCD,EAAA,CAAAuB,uBAAA,MAAiD;IAC/CvB,EAAA,CAAAyB,UAAA,IAAAiC,yCAAA,mBAGC;;IAmFH1D,EAAA,CAAAyB,UAAA,IAAAkC,yCAAA,kBAAmF;IAKnF3D,EAAA,CAAAuB,uBAAA,OAAiD;IAC/CvB,EAAA,CAAAyB,UAAA,IAAAmC,yCAAA,mBAG8B;;IAkFhC5D,EAAA,CAAAyB,UAAA,IAAAoC,yCAAA,kBAAmF;IAM/E7D,EAFJ,CAAAC,cAAA,cAA0B,cACO,eACI;IAC9BD,EAAA,CAAAE,MAAA,0CACH;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAwB,SAAA,oBAKY;IACZxB,EAAA,CAAAyB,UAAA,KAAAqC,0CAAA,kBAAwE;IAG1E9D,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAyB,eACU;IAC9BD,EAAA,CAAAE,MAAA,0CACH;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAwB,SAAA,iBAIW;IACXxB,EAAA,CAAAyB,UAAA,KAAAsC,0CAAA,kBAAsE;IAM9E/D,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACD;;;;IAtNDH,EAAA,CAAAmC,UAAA,cAAA3B,MAAA,CAAAwD,IAAA,CAAkB;IAIFhE,EAAA,CAAAiB,SAAA,GAAgC;IAAhCjB,EAAA,CAAAmC,UAAA,YAAA3B,MAAA,CAAAyD,kBAAA,CAAAC,QAAA,CAAgC;IAqF5ClE,EAAA,CAAAiB,SAAA,EAAwC;IAAxCjB,EAAA,CAAAmC,UAAA,SAAA3B,MAAA,CAAA2D,YAAA,uBAAwC;IAQ5BnE,EAAA,CAAAiB,SAAA,GAAgC;IAAhCjB,EAAA,CAAAmC,UAAA,YAAA3B,MAAA,CAAA4D,kBAAA,CAAAF,QAAA,CAAgC;IAmF5ClE,EAAA,CAAAiB,SAAA,EAAwC;IAAxCjB,EAAA,CAAAmC,UAAA,SAAA3B,MAAA,CAAA2D,YAAA,uBAAwC;IAepCnE,EAAA,CAAAiB,SAAA,GAA6B;IAA7BjB,EAAA,CAAAmC,UAAA,SAAA3B,MAAA,CAAA2D,YAAA,YAA6B;IAa7BnE,EAAA,CAAAiB,SAAA,GAA2B;IAA3BjB,EAAA,CAAAmC,UAAA,SAAA3B,MAAA,CAAA2D,YAAA,UAA2B;;;ADhM7C,OAAM,MAAOE,mBAAmB;EAoC9BC,YACUC,EAAsB,EACtBC,MAAc,EACdC,cAAyC,EACzCC,cAA8B,EAC9BC,KAAmB,EACnBC,mBAAwC;IALxC,KAAAL,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAzCrB,KAAAC,IAAI,GAAG,IAAI9E,OAAO,EAAE;IACrB,KAAAiE,IAAI,GAAqB,IAAIpE,gBAAgB,CAAC,EAAE,CAAC;IACjD,KAAAkF,qBAAqB,GAAoB,EAAE;IAC3C,KAAAC,qBAAqB,GAAoB,EAAE;IAC3C,KAAAC,kBAAkB,GAAY,KAAK;IACnC,KAAAC,kBAAkB,GAAY,KAAK;IAEnC,KAAAC,SAAS,GAAY,IAAI;IACzB,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAA/C,SAAS,GAAe,IAAI,CAACsC,cAAc,CAACtC,SAAS;IACrD,KAAAgD,uBAAuB,GAAU,IAAI,CAACV,cAAc,CAACW,iBAAiB,EAAE;IACxE,KAAAhD,2BAA2B,GAAU,EAAE;IACvC,KAAAiD,2BAA2B,GAAU,EAAE;IAC9C,KAAAhD,0BAA0B,GAAc,CAAC,IAAI,CAAC;IAC9C,KAAAmB,0BAA0B,GAAc,CAAC,IAAI,CAAC;IAC9C,KAAA8B,WAAW,GAAG,KAAK;IAEnB,KAAA/B,iBAAiB,GAAG,CAClB;MACElC,SAAS,EAAE,gDAAgD;MAC3D,gBAAgB,EAAE,EAAE;MACpB,SAAS,EAAE;KACZ,EACD;MACEA,SAAS,EAAE,gDAAgD;MAC3D,gBAAgB,EAAE,EAAE;MACpB,SAAS,EAAE;KACZ,EACD;MACEA,SAAS,EAAE,8BAA8B;MACzC,gBAAgB,EAAE,EAAE;MACpB,SAAS,EAAE;KACZ,CACF;EASG;EAEJkE,QAAQA,CAAA;IACN,IAAI,CAACf,cAAc,CAACgB,mBAAmB,CAAC,CAAC,CAAC;IAE1C,IAAI,CAACZ,IAAI,CAACa,GAAG,CAAC,IAAI,CAACjB,cAAc,CAACkB,0BAA0B,EAAE,CAACC,SAAS,CAACC,GAAG,IAAG;MAC7E,IAAI,CAACX,SAAS,GAAG,KAAK;MACtB,IAAI,CAACY,UAAU,EAAE;MACjB,IAAI,CAACC,oBAAoB,CAACF,GAAG,CAAC;MAC9B,IAAI,CAACG,uBAAuB,EAAE;IAChC,CAAC,CAAC,CAAC;IAEH,IAAI,CAACnB,IAAI,CAACa,GAAG,CAAC,IAAI,CAACjB,cAAc,CAACwB,yBAAyB,CAACL,SAAS,CAACC,GAAG,IAAG;MAC1E,IAAIA,GAAG,EAAE;QACP,IAAI,CAACK,QAAQ,EAAE;MACjB;IACF,CAAC,CAAC,CAAC;EACL;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACtB,IAAI,CAACuB,WAAW,EAAE;IACvB,IAAI,CAAC3B,cAAc,CAAC4B,4BAA4B,CAAC,KAAK,CAAC;EACzD;EAEA,IAAIpC,kBAAkBA,CAAA;IACpB,OAAO,IAAI,CAACD,IAAI,CAACsC,GAAG,CAAC,oBAAoB,CAAqB;EAChE;EAEA,IAAIlC,kBAAkBA,CAAA;IACpB,OAAO,IAAI,CAACJ,IAAI,CAACsC,GAAG,CAAC,oBAAoB,CAAqB;EAChE;EAEA;;;;EAIAR,UAAUA,CAAA;IACR,IAAI,CAAC9B,IAAI,GAAG,IAAIpE,gBAAgB,CAAC;MAC/B2G,OAAO,EAAE,IAAI5G,kBAAkB,CAAC,IAAI,EAAE;QACpC6G,UAAU,EAAE,CAAC3G,UAAU,CAAC4G,QAAQ;OACjC,CAAC;MACFC,KAAK,EAAE,IAAI/G,kBAAkB,CAAC,CAAC,EAAE;QAC/B6G,UAAU,EAAE,CAAC3G,UAAU,CAAC4G,QAAQ;OACjC,CAAC;MACFxC,kBAAkB,EAAE,IAAIvE,gBAAgB,CAAC,EAAE,CAAC;MAC5C0E,kBAAkB,EAAE,IAAI1E,gBAAgB,CAAC,EAAE;KAC5C,CAAC;EACJ;EAEA;;;;;;EAMAqG,oBAAoBA,CAACY,MAA+B;IAElD,IAAI,CAAC3C,IAAI,CAACsC,GAAG,CAAC,SAAS,CAAC,EAAEM,UAAU,CAACD,MAAM,CAACE,eAAe,CAAC;IAC5D,IAAI,CAAC7C,IAAI,CAACsC,GAAG,CAAC,OAAO,CAAC,EAAEM,UAAU,CAACD,MAAM,CAACG,eAAe,CAAC;IAC1D,IAAI,IAAI,CAACpC,cAAc,CAACqC,eAAe,CAACJ,MAAM,CAACK,aAAa,CAAC,EAAE;MAC7D,IAAI,CAACrG,gBAAgB,EAAE;IACzB,CAAC,MAAM;MACL,IAAI,CAAC0B,2BAA2B,GAAG,EAAE;MAErCsE,MAAM,CAACK,aAAa,CAACC,OAAO,CAAC,CAACC,OAAO,EAAEnG,KAAK,KAAI;QAC9C,MAAMoG,YAAY,GAAG,IAAI,CAAC/B,uBAAuB,CAACgC,MAAM,CAACC,CAAC,IAAG;UAC3D,OAASA,CAAC,CAAC/F,SAAS,KAAK4F,OAAO,CAACI,KAAK,CAAChG,SAAS;QAClD,CAAC,CAAC;QACF4F,OAAO,CAACI,KAAK,GAAGH,YAAY,CAAC,CAAC,CAAC;QAC/B,IAAI,CAAC7E,0BAA0B,CAACvB,KAAK,CAAC,GAAG,KAAK;QAC9C,IAAI,CAACwG,8BAA8B,CAACxG,KAAK,EAAEmG,OAAO,CAACM,QAAQ,CAACC,IAAI,CAAC;QACjE,IAAI,CAACxD,kBAAkB,CAACyD,IAAI,CAAC,IAAI,CAACnD,EAAE,CAACoD,KAAK,CAACT,OAAO,CAAC,CAAC;MACtD,CAAC,CAAC;IACJ;IACA,IAAI,IAAI,CAACxC,cAAc,CAACqC,eAAe,CAACJ,MAAM,CAACiB,aAAa,CAAC,EAAE;MAC7D,IAAI,CAACnF,gBAAgB,EAAE;IACzB,CAAC,MAAM;MAEL;MACAkE,MAAM,CAACiB,aAAa,CAACX,OAAO,CAAC,CAACC,OAAO,EAAEnG,KAAK,KAAI;QAE9C,MAAMoG,YAAY,GAAG,IAAI,CAAC3D,iBAAiB,CAAC4D,MAAM,CAACC,CAAC,IAAG;UACrD,OAASA,CAAC,CAAC/F,SAAS,KAAK4F,OAAO,CAACI,KAAK,CAAChG,SAAS;QAClD,CAAC,CAAC;QACF4F,OAAO,CAACI,KAAK,GAAGH,YAAY,CAAC,CAAC,CAAC;QAE/B;QACA;QACA;QAEAU,OAAO,CAACC,GAAG,CAACZ,OAAO,CAAC;QACpB,IAAI,CAACzD,0BAA0B,CAAC1C,KAAK,CAAC,GAAG,KAAK;QAC9C,IAAI,CAACgH,8BAA8B,CAAChH,KAAK,CAAC;QAC1C,IAAI,CAACqD,kBAAkB,CAACsD,IAAI,CAAC,IAAI,CAACnD,EAAE,CAACoD,KAAK,CAACT,OAAO,CAAC,CAAC;MACtD,CAAC,CAAC;IACJ;EACF;EAEA;;;;;;;EAOAK,8BAA8BA,CAACxG,KAAa,EAAEiH,YAAqB;IACjE,IAAI,CAAC3F,2BAA2B,CAACtB,KAAK,CAAC,GAAG,IAAI,CAACqE,uBAAuB,CACpEgC,MAAM,CAACC,CAAC,IAAG;MACT,OAASA,CAAC,CAACY,cAAc,CAACC,MAAM,KAAK,CAAC,IAAMb,CAAC,CAACY,cAAc,CAACE,QAAQ,CAACH,YAAY,CAAE;IACtF,CAAC,CAAC;IACFH,OAAO,CAACC,GAAG,CAAC/G,KAAK,EAAE,IAAI,CAACsB,2BAA2B,CAACtB,KAAK,CAAC,CAAC;EAC/D;EAEA;;;;;;EAMAgH,8BAA8BA,CAAChH,KAAa,EAAEiH,YAAqB;IAEjE,IAAI,CAAC1C,2BAA2B,CAACvE,KAAK,CAAC,GAAG,IAAI,CAACyC,iBAAiB,CAC9D4D,MAAM,CAACC,CAAC,IAAG;MACT,OAAQA,CAAC;IACX,CAAC,CAAC;IACFQ,OAAO,CAACC,GAAG,CAAC/G,KAAK,EAAE,IAAI,CAACuE,2BAA2B,CAACvE,KAAK,CAAC,CAAC;IAC7D;IACA;IACA;IACA;EACF;EAEA;;;;;EAKAJ,gBAAgBA,CAAA;IACd,IAAI,CAACf,gBAAgB,IAAI,CAACD,kBAAkB,EAAE;MAC5C,MAAM,IAAIyI,KAAK,CAAC,kDAAkD,CAAC;IACrE;IACA,MAAMT,KAAK,GAAG,IAAI/H,gBAAgB,CAAC;MACjC4H,QAAQ,EAAE,IAAI7H,kBAAkB,CAAC,EAAE,EAAEE,UAAU,CAAC4G,QAAQ,CAAC;MACzDa,KAAK,EAAE,IAAI3H,kBAAkB,CAAC,EAAE,EAAEE,UAAU,CAAC4G,QAAQ,CAAC;MACtD4B,gBAAgB,EAAE,IAAI1I,kBAAkB,CAAC,KAAK;KAC/C,CAAC;IACF,IAAI,CAACsE,kBAAkB,CAACyD,IAAI,CAACC,KAAK,CAAC;IACnC,IAAI,CAACrF,0BAA0B,CAAC,IAAI,CAAC2B,kBAAkB,CAACiE,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;EAC5E;EAEA;;;;;EAKAlH,mBAAmBA,CAACD,KAAa;IAC/B,IAAI,CAACkD,kBAAkB,CAACqE,QAAQ,CAACvH,KAAK,CAAC;EACzC;EAEA;;;;;EAKA0B,gBAAgBA,CAAA;IACd,IAAI,CAAC7C,gBAAgB,IAAI,CAACD,kBAAkB,EAAE;MAC5C,MAAM,IAAIyI,KAAK,CAAC,kDAAkD,CAAC;IACrE;IACA,MAAMT,KAAK,GAAG,IAAI/H,gBAAgB,CAAC;MACjC4H,QAAQ,EAAE,IAAI7H,kBAAkB,CAAC,EAAE,EAAEE,UAAU,CAAC4G,QAAQ,CAAC;MACzDa,KAAK,EAAE,IAAI3H,kBAAkB,CAAC,EAAE,EAAEE,UAAU,CAAC4G,QAAQ,CAAC;MACtD4B,gBAAgB,EAAE,IAAI1I,kBAAkB,CAAC,KAAK;KAC/C,CAAC;IACF,IAAI,CAACyE,kBAAkB,CAACsD,IAAI,CAACC,KAAK,CAAC;IACnC,IAAI,CAAClE,0BAA0B,CAAC,IAAI,CAACW,kBAAkB,CAAC8D,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;EAC5E;EAEA;;;;;EAKArF,mBAAmBA,CAAC9B,KAAa;IAC/B,IAAI,CAACqD,kBAAkB,CAACkE,QAAQ,CAACvH,KAAK,CAAC;EACzC;EAEAmF,QAAQA,CAAA;IACN,IAAI,CAACf,SAAS,GAAG,IAAI;IACrB,IAAI,CAACH,kBAAkB,GAAG,IAAI,CAAChB,IAAI,CAACE,QAAQ,CAACD,kBAAkB,CAACsE,MAAM,KAAK,SAAS,GAAG,IAAI,GAAG,KAAK;IACnG,IAAI,CAACtD,kBAAkB,GAAG,IAAI,CAACjB,IAAI,CAACE,QAAQ,CAACE,kBAAkB,CAACmE,MAAM,KAAK,SAAS,GAAG,IAAI,GAAG,KAAK;IAEnG,IAAI,CAAC,IAAI,CAACvE,IAAI,CAACwE,KAAK,EAAE;MACpB;IACF;IAEA,MAAMC,wBAAwB,GAAG;MAC/BzB,aAAa,EAAE,IAAI,CAAChD,IAAI,CAAC0E,KAAK,CAACzE,kBAAkB;MACjD2D,aAAa,EAAE,IAAI,CAAC5D,IAAI,CAAC0E,KAAK,CAACtE,kBAAkB;MACjD0C,eAAe,EAAE,IAAI,CAAC9C,IAAI,CAAC0E,KAAK,CAAChC,KAAK;MACtCG,eAAe,EAAE,IAAI,CAAC7C,IAAI,CAAC0E,KAAK,CAACnC;KAClC;IAED,IAAI,CAAC1B,IAAI,CAACa,GAAG,CAAC,IAAI,CAACjB,cAAc,CAACkE,gCAAgC,CAACF,wBAAwB,CAAC,CAAC7C,SAAS,CAACC,GAAG,IAAG;MAC3GgC,OAAO,CAACC,GAAG,CAACjC,GAAG,CAAC;MAChB,IAAIA,GAAG,EAAE;QACP,IAAI,CAAClB,KAAK,CAACiE,mBAAmB,CAAC;UAC7BC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAACvE,MAAM,CAACwE,aAAa,CAAC,iBAAiB,EAAE;UAAEC,UAAU,EAAE;QAAI,CAAE,CAAC;MACpE;IACF,CAAC,CAAC,CAAC;EACL;EAEA9E,YAAYA,CAAC+E,KAAa;IACxB,IAAI,CAACzE,cAAc,CAAC0E,YAAY,CAAC,CAAC,EAAE,IAAI,CAACnF,IAAI,EAAE,0BAA0B,CAAC;IAC1E,OAAS,IAAI,CAACA,IAAI,CAACsC,GAAG,CAAC4C,KAAK,CAAC,EAAEE,OAAO,IAAI,IAAI,CAACpF,IAAI,CAACsC,GAAG,CAAC4C,KAAK,CAAC,EAAEG,OAAO,IAAM,IAAI,CAAClE,SAAS,IAAI,IAAI,CAACnB,IAAI,CAACsC,GAAG,CAAC4C,KAAK,CAAC,EAAEE,OAAQ;EAC/H;EAEAE,MAAMA,CAAA;IACJ,IAAI,CAAC9E,MAAM,CAACwE,aAAa,CAAC,oBAAoB,EAAE;MAAEC,UAAU,EAAE;IAAI,CAAE,CAAC;EACvE;EAEA;;;;;;EAMAjH,yBAAyBA,CAACuH,KAAkC,EAAEC,aAAqB;IACjF,IAAI,CAAClH,0BAA0B,CAACkH,aAAa,CAAC,GAAG,KAAK;IACtD,IAAI,CAACnH,2BAA2B,CAACmH,aAAa,CAAC,GAAG,IAAI,CAACpE,uBAAuB,CAC5EgC,MAAM,CAACC,CAAC,IAAG;MACT,OAAQA,CAAC,CAACY,cAAc,CAACC,MAAM,KAAK,CAAC,IAAMb,CAAC,CAACY,cAAc,CAACE,QAAQ,CAACoB,KAAK,CAACb,KAAK,CAACjB,IAAI,CAAE;IACzF,CAAC,CAAC;EACN;EAEA;;;;;;EAMApE,yBAAyBA,CAACkG,KAAkC,EAAEC,aAAqB;IACjF,IAAI,CAAC/F,0BAA0B,CAAC+F,aAAa,CAAC,GAAG,KAAK;IACtD;IACA;IACA;IACA;EACF;EAEAxD,uBAAuBA,CAAA;IACrB,IAAI,CAACnB,IAAI,CAACa,GAAG,CAAC,IAAI,CAAC1B,IAAI,CAACyF,YAAY,CAAC7D,SAAS,CAAC8D,GAAG,IAAG;MACnD,IAAG,IAAI,CAAC1F,IAAI,CAAC2F,KAAK,EAAE;QAClB9B,OAAO,CAACC,GAAG,CAAC4B,GAAG,CAAC;QAChB,IAAI,CAACnE,WAAW,GAAG,IAAI;MACzB;IACF,CAAC,CAAC,CAAC;EACL;EAEAqE,aAAaA,CAAA;IACX,IAAI,IAAI,CAACrE,WAAW,IAAI,CAAC,IAAI,CAACJ,SAAS,EAAE;MACvC,OAAO,IAAIrF,UAAU,CAAE+J,QAAa,IAAI;QACtC,IAAI,CAACjF,mBAAmB,CAACkF,OAAO,CAAC;UAC/BC,MAAM,EAAE,EAAE;UACVC,GAAG,EAAE,uBAAuB;UAC5BC,OAAO,EAAE,sEAAsE;UAC/EC,MAAM,EAAEA,CAAA,KAAK;YACXL,QAAQ,CAACM,IAAI,CAAC,IAAI,CAAC;YACnBN,QAAQ,CAACO,QAAQ,EAAE;UACrB,CAAC;UACDC,MAAM,EAAEA,CAAA,KAAK;YACXR,QAAQ,CAACM,IAAI,CAAC,KAAK,CAAC;YACpBN,QAAQ,CAACO,QAAQ,EAAE;UACrB;SACD,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAQ,IAAI;IACd;EACF;EAAC,QAAAE,CAAA,G;qBApUUjG,mBAAmB,EAAArE,EAAA,CAAAuK,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAAzK,EAAA,CAAAuK,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA3K,EAAA,CAAAuK,iBAAA,CAAAK,EAAA,CAAAC,yBAAA,GAAA7K,EAAA,CAAAuK,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA/K,EAAA,CAAAuK,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAAjL,EAAA,CAAAuK,iBAAA,CAAAW,EAAA,CAAAC,mBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAnB/G,mBAAmB;IAAAgH,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCjBhC3L,EAAA,CAAAC,cAAA,aAAkC;QAC9BD,EAAA,CAAAyB,UAAA,IAAAoK,mCAAA,mBAA4C;QAuN9C7L,EAAA,CAAAG,YAAA,EAAM;QAGJH,EADF,CAAAC,cAAA,aAA+B,gBAC8C;QAAnBD,EAAA,CAAAI,UAAA,mBAAA0L,qDAAA;UAAA,OAASF,GAAA,CAAAtC,MAAA,EAAQ;QAAA,EAAC;QACxEtJ,EAAA,CAAAwB,SAAA,aAA+C;QAACxB,EAAA,CAAAE,MAAA,aAClD;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,gBAAiE;QAAjDD,EAAA,CAAAI,UAAA,mBAAA2L,qDAAA;UAAA,OAASH,GAAA,CAAA1F,QAAA,EAAU;QAAA,EAAC;QAClClG,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAwB,SAAA,aAA2C;QAGpDxB,EAFE,CAAAG,YAAA,EAAS,EAEL;;;QAjOsBH,EAAA,CAAAiB,SAAA,EAAgB;QAAhBjB,EAAA,CAAAmC,UAAA,UAAAyJ,GAAA,CAAA1G,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}