{"ast": null, "code": "import { switchMap, take } from 'rxjs/operators';\nimport { UserRole } from 'src/app/core/models/user.model';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/dynamicdialog\";\nimport * as i2 from \"src/app/core/services/auth.service\";\nimport * as i3 from \"src/app/core/services/user.service\";\nimport * as i4 from \"src/app/core/services/general.service\";\nimport * as i5 from \"src/app/core/services/library.service\";\nimport * as i6 from \"src/app/core/services/toast.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"primeng/button\";\nconst _c0 = a0 => ({\n  \"blue-teal\": a0\n});\nfunction LibraryFileActionsDialogComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4)(4, \"input\", 5);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LibraryFileActionsDialogComponent_ng_container_1_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.dialogData.libraryFile.name, $event) || (ctx_r1.dialogData.libraryFile.name = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 6)(6, \"button\", 7);\n    i0.ɵɵlistener(\"click\", function LibraryFileActionsDialogComponent_ng_container_1_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDialogClose({\n        action: \"rename\",\n        libraryFile: ctx_r1.dialogData.libraryFile\n      }));\n    });\n    i0.ɵɵelement(7, \"span\", 8);\n    i0.ɵɵtext(8, \" Submit \");\n    i0.ɵɵelement(9, \"img\", 9);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.dialogData.libraryFile.name);\n  }\n}\nfunction LibraryFileActionsDialogComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h4\", 10);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"app-classrooms-check-list\", 11);\n    i0.ɵɵlistener(\"checkedList\", function LibraryFileActionsDialogComponent_ng_container_2_Template_app_classrooms_check_list_checkedList_3_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCheckedList($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 12)(5, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function LibraryFileActionsDialogComponent_ng_container_2_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSendSharedWith());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.dialogData.dialogsubTitle);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"preselectedClassroom\", ctx_r1.dialogData.preselectedClassroom)(\"showBlur\", true);\n  }\n}\nfunction LibraryFileActionsDialogComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h4\", 10);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"app-classrooms-check-list\", 14);\n    i0.ɵɵlistener(\"checkedList\", function LibraryFileActionsDialogComponent_ng_container_3_Template_app_classrooms_check_list_checkedList_3_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCheckedList($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 12)(5, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function LibraryFileActionsDialogComponent_ng_container_3_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSendSharedWith());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.dialogData.dialogsubTitle);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"showBlur\", true);\n  }\n}\nfunction LibraryFileActionsDialogComponent_ng_container_4_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 22);\n    i0.ɵɵlistener(\"click\", function LibraryFileActionsDialogComponent_ng_container_4_ng_container_7_Template_li_click_1_listener() {\n      const innerFolder_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.moveSelected(innerFolder_r7));\n    });\n    i0.ɵɵelementStart(2, \"div\", 23);\n    i0.ɵɵelement(3, \"i\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 25)(5, \"span\", 26);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(7, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const innerFolder_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵstyleProp(\"margin-left\", innerFolder_r7.marginLeft, \"px\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", innerFolder_r7.name, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c0, ctx_r1.selectedFolder === innerFolder_r7));\n  }\n}\nfunction LibraryFileActionsDialogComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h4\", 10);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 15)(4, \"input\", 16);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LibraryFileActionsDialogComponent_ng_container_4_Template_input_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.term, $event) || (ctx_r1.term = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"i\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"ul\", 18);\n    i0.ɵɵtemplate(7, LibraryFileActionsDialogComponent_ng_container_4_ng_container_7_Template, 8, 6, \"ng-container\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 20)(9, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function LibraryFileActionsDialogComponent_ng_container_4_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onMoveSubmitted());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.dialogData.dialogsubTitle);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.term);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.dialogData.folders);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.selectedFolder);\n  }\n}\nfunction LibraryFileActionsDialogComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h4\", 10);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"app-upload-files\", 28);\n    i0.ɵɵlistener(\"onSubmit\", function LibraryFileActionsDialogComponent_ng_container_5_Template_app_upload_files_onSubmit_3_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onUploadedFilesSubmitted());\n    })(\"onUploadFinished\", function LibraryFileActionsDialogComponent_ng_container_5_Template_app_upload_files_onUploadFinished_3_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.uploadFinished($event));\n    })(\"tagChoosen\", function LibraryFileActionsDialogComponent_ng_container_5_Template_app_upload_files_tagChoosen_3_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTagChoosen($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.dialogData.dialogsubTitle);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"fileDropImage\", \"/assets/icons/library/upload-file-lib.svg\")(\"fileDropImageWidth\", 10)(\"isProfile\", false)(\"files\", ctx_r1.files)(\"teacherFilesLength\", ctx_r1.dialogData.libraryToAdd.libraryFiles.length)(\"dndUI\", true)(\"showSubmitButton\", true)(\"checkboxUI\", true);\n  }\n}\nfunction LibraryFileActionsDialogComponent_ng_container_6_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵelement(1, \"img\", 35);\n    i0.ɵɵelementStart(2, \"label\", 36)(3, \"span\", 37);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"i\", 38);\n    i0.ɵɵlistener(\"click\", function LibraryFileActionsDialogComponent_ng_container_6_div_2_Template_i_click_5_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.showEditRename = !ctx_r1.showEditRename);\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.getFileName(ctx_r1.modifiedFile.name));\n  }\n}\nfunction LibraryFileActionsDialogComponent_ng_container_6_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 39)(2, \"div\", 40)(3, \"div\", 4)(4, \"div\", 41);\n    i0.ɵɵelement(5, \"img\", 35);\n    i0.ɵɵelementStart(6, \"label\", 42)(7, \"input\", 43);\n    i0.ɵɵlistener(\"ngModelChange\", function LibraryFileActionsDialogComponent_ng_container_6_ng_container_3_Template_input_ngModelChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.newFileName = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"i\", 38);\n    i0.ɵɵlistener(\"click\", function LibraryFileActionsDialogComponent_ng_container_6_ng_container_3_Template_i_click_8_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.showEditRename = !ctx_r1.showEditRename);\n    });\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.getFileName(ctx_r1.modifiedFile.name));\n  }\n}\nfunction LibraryFileActionsDialogComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 29);\n    i0.ɵɵtemplate(2, LibraryFileActionsDialogComponent_ng_container_6_div_2_Template, 6, 1, \"div\", 30)(3, LibraryFileActionsDialogComponent_ng_container_6_ng_container_3_Template, 9, 1, \"ng-container\", 1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 29)(5, \"app-choose-tags\", 31);\n    i0.ɵɵlistener(\"chooseTag\", function LibraryFileActionsDialogComponent_ng_container_6_Template_app_choose_tags_chooseTag_5_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onChooseTag($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 32)(7, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function LibraryFileActionsDialogComponent_ng_container_6_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTagsSubmitted());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.showEditRename);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showEditRename);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"applyToAll\", false)(\"libraryFile\", ctx_r1.dialogData.libraryFile);\n  }\n}\nexport class LibraryFileActionsDialogComponent {\n  constructor(ref, authService, userService, generalService, libraryService, toastService, config) {\n    this.ref = ref;\n    this.authService = authService;\n    this.userService = userService;\n    this.generalService = generalService;\n    this.libraryService = libraryService;\n    this.toastService = toastService;\n    this.config = config;\n    this.subs = new SubSink();\n    this.role = UserRole.NONE;\n    this.UserRoles = UserRole;\n    this.dialogData = {};\n    this.libraryToAdd = {};\n    this.files = [];\n    this.modifiedFile = {};\n    this.showEditRename = false;\n    this.newFileName = '';\n  }\n  ngOnInit() {\n    this.role = this.authService.getLoggedInUser().role;\n    this.dialogData = this.config.data.dialogData;\n    console.log(this.dialogData);\n    this.userService.setUploadFiles(true);\n    this.files = [];\n    if (this.dialogData.libraryFile) {\n      this.newFileName = this.dialogData.libraryFile.name;\n      this.modifiedFile = this.dialogData.libraryFile;\n    }\n    // this.sortFolders(this.dialogData.folders);\n    // console.log((this.sortFolders(this.dialogData.folders)))\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n    this.ref.destroy();\n  }\n  onDialogClose(data) {\n    console.log(data);\n    this.ref.close(data);\n    this.ref.destroy();\n  }\n  onCheckedList(event) {\n    console.log(event);\n    this.sharedWithMap = event;\n  }\n  onSendSharedWith() {\n    this.onDialogClose({\n      action: this.dialogData.action,\n      map: this.sharedWithMap\n    });\n  }\n  moveSelected(folder) {\n    this.selectedFolder = folder;\n    console.log(folder);\n  }\n  /**\n   * Executes when the move is submitted.\n   *\n   * @param {type} paramName - description of parameter\n   * @return {type} description of return value\n   */\n  onMoveSubmitted() {\n    console.log(this.selectedFolder);\n    this.onDialogClose({\n      action: this.dialogData.action,\n      libraryFile: this.dialogData.libraryFile,\n      folder: this.selectedFolder\n    });\n  }\n  /**\n   * Retrieves the parent folders from the dialogData object.\n   *\n   * @return {Array} An array containing objects representing the parent folders,\n   * their child folders, and the library files associated with each parent folder.\n   */\n  getParentFolders() {\n    const parentFolders = this.dialogData.folders.filter(f => !f.parent);\n    return parentFolders.map(parent => {\n      const childFolders = this.dialogData.folders.filter(f => f.parent === parent.name);\n      const libraryFiles = parent.libraryFiles;\n      return {\n        parent,\n        childFolders,\n        libraryFiles\n      };\n    }).concat(this.dialogData.folders.filter(f => f.parent));\n  }\n  /**\n   * Sorts an array of folders by separating them into parent folders and child folders,\n   * sorting the parent folders by name, and adding the child folders to their respective parent folders.\n   *\n   * @param {any[]} folders - The array of folders to be sorted.\n   * @return {any[]} - The sorted array of folders.\n   */\n  sortFolders(folders) {\n    const sortedFolders = [];\n    const parentFolders = [];\n    const childFolders = [];\n    // Separate parent folders and child folders\n    folders.forEach(folder => {\n      if (folder.parent === '') {\n        parentFolders.push(folder);\n      } else {\n        childFolders.push(folder);\n      }\n    });\n    // Sort parent folders by name\n    parentFolders.sort((a, b) => a.name.localeCompare(b.name));\n    // Add parent folders to sorted array\n    parentFolders.forEach(folder => {\n      sortedFolders.push(folder);\n      const children = childFolders.filter(child => child.parent === folder.name);\n      const sortedChildren = this.sortFolders(children);\n      sortedChildren.forEach(child => {\n        sortedFolders.push({\n          ...child,\n          marginLeft: 10\n        });\n      });\n    });\n    // Add child folders to sorted array\n    childFolders.forEach(folder => {\n      if (!sortedFolders.some(f => f.folderId === folder.folderId)) {\n        sortedFolders.push({\n          ...folder,\n          marginLeft: 10\n        });\n      }\n    });\n    return sortedFolders;\n  }\n  /**\n   * Uploads the finished event.\n   *\n   * @param {any} event - The event object.\n   * @return {void} This function does not return anything.\n   */\n  uploadFinished(event) {\n    this.files.push({\n      path: event.filePath.dbPath,\n      levels: [],\n      categories: [],\n      name: event.filePath.dbPath,\n      fileId: event.filePath.dbPath\n    });\n    console.log(this.files);\n  }\n  /**\n   * Handles the event when a tag is chosen.\n   *\n   * @param {any} event - The event object containing information about the tag selection.\n   */\n  onTagChoosen(event) {\n    let isLevel = event.isLevel;\n    if (event.checked) {\n      if (event.applyToAll) {\n        for (let file of this.files) {\n          let arrLibrary = isLevel ? file.levels : file.categories;\n          arrLibrary.push(event.tag);\n        }\n      } else {\n        let file = this.files[event.file.index];\n        let arrLibrary = isLevel ? file.levels : file.categories;\n        arrLibrary.push(event.tag);\n      }\n    } else {\n      if (event.applyToAll) {\n        for (let file of this.files) {\n          let arrLibrary = isLevel ? file.levels : file.categories;\n          let index2 = arrLibrary.findIndex(el => el == event.tag);\n          arrLibrary.splice(index2, 1);\n        }\n      } else {\n        let file = this.files[event.file.index];\n        let arrLibrary = isLevel ? file.levels : file.categories;\n        let index2 = arrLibrary.findIndex(el => el == event.tag);\n        arrLibrary.splice(index2, 1);\n      }\n    }\n  }\n  onUploadedFilesSubmitted() {\n    const params = {\n      action: this.dialogData.action,\n      libraryFiles: this.files,\n      folder: this.dialogData.folder\n    };\n    console.log(params);\n    this.onDialogClose(params);\n  }\n  /**\n   * Retrieves the file name from a given file path.\n   *\n   * @param {string} filePath - The file path.\n   * @return {string | undefined} The file name without extension if it exists, otherwise the entire file name.\n   */\n  getFileName(filePath) {\n    const fileNameWithPath = filePath.split('\\\\').pop();\n    const fileNameParts = fileNameWithPath.split('.');\n    if (fileNameParts.length > 1) {\n      const fileNameWithoutExtension = fileNameParts.slice(0, -1).join('.');\n      return fileNameWithoutExtension;\n    }\n    return fileNameWithPath;\n  }\n  /**\n   * A function that creates a deep copy of an object.\n   *\n   * @param {any} obj - The object to be deep copied.\n   * @return {any} The deep copy of the object.\n   */\n  deepCopy(obj) {\n    if (typeof obj !== 'object' || obj === null) {\n      return obj;\n    }\n    let copy;\n    if (obj instanceof Array) {\n      copy = [];\n      for (let i = 0; i < obj.length; i++) {\n        copy[i] = this.deepCopy(obj[i]);\n      }\n    } else {\n      copy = {};\n      for (const key in obj) {\n        if (obj.hasOwnProperty(key)) {\n          copy[key] = this.deepCopy(obj[key]);\n        }\n      }\n    }\n    return copy;\n  }\n  onChooseTag(checkbox) {\n    const libraryFile = this.deepCopy(this.modifiedFile);\n    if (checkbox.checked) {\n      if (checkbox.isCategory) {\n        // Update categories in libraryFile\n        libraryFile.categories.push(checkbox.tag);\n      }\n      if (checkbox.isLevel) {\n        // Update levels in libraryFile\n        libraryFile.levels.push(checkbox.tag);\n      }\n    } else {\n      if (checkbox.isCategory) {\n        // Remove category from libraryFile\n        libraryFile.categories = libraryFile.categories.filter(category => category !== checkbox.tag);\n      }\n      if (checkbox.isLevel) {\n        // Remove level from libraryFile\n        libraryFile.levels = libraryFile.levels.filter(level => level !== checkbox.tag);\n      }\n    }\n    this.modifiedFile = libraryFile;\n  }\n  onTagsSubmitted() {\n    const libraryFile = this.deepCopy(this.modifiedFile);\n    const newLevels = {\n      fileId: libraryFile.fileId,\n      levels: this.modifiedFile.levels\n    };\n    const newCategories = {\n      fileId: libraryFile.fileId,\n      categories: this.modifiedFile.categories\n    };\n    this.rename(this.modifiedFile.fileId, this.newFileName);\n    this.subs.add(this.libraryService.editFileLevels(newLevels).pipe(switchMap(res => {\n      console.log(res);\n      return this.libraryService.editFileCategories(newCategories);\n    })).subscribe(res => {\n      console.log(res);\n      const params = {\n        action: this.dialogData.action,\n        result: this.modifiedFile\n      };\n      this.toastService.setShowToastmessage({\n        severity: 'success',\n        summary: '',\n        detail: 'Library file saved successfully.'\n      });\n      console.log(this.modifiedFile);\n      this.onDialogClose(params);\n    }));\n  }\n  rename(fileId, newName) {\n    this.subs.add(this.libraryService.updateLibraryFileName(fileId, newName).pipe(take(1)).subscribe(res => {\n      this.modifiedFile.name = this.newFileName;\n    }));\n  }\n  static #_ = this.ɵfac = function LibraryFileActionsDialogComponent_Factory(t) {\n    return new (t || LibraryFileActionsDialogComponent)(i0.ɵɵdirectiveInject(i1.DynamicDialogRef), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i4.GeneralService), i0.ɵɵdirectiveInject(i5.LibraryService), i0.ɵɵdirectiveInject(i6.ToastService), i0.ɵɵdirectiveInject(i1.DynamicDialogConfig));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LibraryFileActionsDialogComponent,\n    selectors: [[\"app-library-file-actions-dialog\"]],\n    decls: 7,\n    vars: 10,\n    consts: [[\"headerBackgroundImage\", \"/assets/images/dashboard/calendar/calendar-dialog-lesson-info-arranged.png\", \"blockClass\", \"border-radius-bottom-10\", \"headerBlockClass\", \"py-1 border-round-lg bg-cover sticky top-0 z-5 \", \"containerClass\", \"block-gradient-reverse px-3 py-2 relative\", 3, \"closeDialogEvent\", \"header\", \"headerClass\", \"headerTextClass\", \"showCloseDialogIcon\"], [4, \"ngIf\"], [1, \"library-info\", \"grid\"], [1, \"w-full\", \"flex\", \"align-items-center\", \"justify-content-center\", \"py-2\"], [1, \"library-file-details\", \"py-1\", \"font-sm\", \"flex\", \"flex-column\"], [\"id\", \"fileName\", 1, \"input-element\", 3, \"ngModelChange\", \"ngModel\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"text-center\", \"mt-2\"], [\"pRipple\", \"\", \"pbutton\", \"\", \"label\", \"\", \"icon\", \"pi pi-discord\", 1, \"element\", \"ripple\", \"p-button-raised\", \"p-button-rounded\", \"white-space-nowrap\", \"p-button\", \"btn-gradient-blue\", \"p-component\", \"w-10rem\", \"font-lg\", \"flex\", \"justify-content-center\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"p-button-icon\", \"p-button-icon-right\"], [\"src\", \"/assets/icons/forward-arrow.svg\", \"width\", \"18\", 1, \"ml-2\"], [1, \"font-base\"], [3, \"checkedList\", \"preselectedClassroom\", \"showBlur\"], [1, \"py-2\", \"sticky\", \"bottom-0\", \"flex\", \"justify-content-center\", \"align-items-center\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"Ok\", \"icon\", \"pi pi-check\", \"iconPos\", \"right\", 1, \"p-button-sm\", \"p-button-raised\", \"p-button-rounded\", \"blue-teal\", \"mt-3\", 3, \"click\"], [3, \"checkedList\", \"showBlur\"], [1, \"p-input-icon-right\", \"w-full\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Search\", 1, \"h-2rem\", \"w-full\", \"input-blue\", \"gradient-blue\", \"rounded\", 3, \"ngModelChange\", \"ngModel\"], [1, \"pi\", \"pi-search\", \"text-primary\"], [1, \"p-0\", \"mx-0\", \"mt-2\", \"mb-1\", \"list-none\", \"overflow-y-scroll\", \"h-11rem\"], [4, \"ngFor\", \"ngForOf\"], [1, \"bg-white\", \"py-1\", \"sticky\", \"bottom-0\", \"flex\", \"justify-content-center\", \"align-items-center\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"Ok\", \"icon\", \"pi pi-check\", \"iconPos\", \"right\", 1, \"p-button-sm\", \"p-button-raised\", \"p-button-rounded\", \"blue-teal\", \"mt-3\", 3, \"click\", \"disabled\"], [1, \"flex\", \"align-items-center\", \"py-1\", \"pr-1\", \"border-bottom-1\", \"surface-border\", \"folder-hover\", \"pointer\", 3, \"click\"], [1, \"w-2rem\", \"h-1rem\", \"flex\", \"align-items-center\", \"justify-content-center\", \"mr-3\", \"flex-shrink-0\"], [1, \"pi\", \"pi\", \"pi-folder\", \"text-blue-500\"], [1, \"text-900\", \"line-height-3\"], [1, \"text-700\"], [1, \"circle\", \"ml-auto\", \"border-circle\", \"w-1rem\", \"h-1rem\", \"cursor-pointer\", 3, \"ngClass\"], [\"title\", \"Library\", \"postUrl\", \"/Upload/UploadLibraryFile\", 1, \"p-20\", 3, \"onSubmit\", \"onUploadFinished\", \"tagChoosen\", \"fileDropImage\", \"fileDropImageWidth\", \"isProfile\", \"files\", \"teacherFilesLength\", \"dndUI\", \"showSubmitButton\", \"checkboxUI\"], [1, \"bordered-tags\"], [\"class\", \"library-edit-title justify-content-center my-2\", 4, \"ngIf\"], [3, \"chooseTag\", \"applyToAll\", \"libraryFile\"], [1, \"py-1\", \"sticky\", \"bottom-0\", \"flex\", \"justify-content-center\", \"align-items-center\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"Save\", \"icon\", \"pi pi-check\", \"iconPos\", \"right\", 1, \"p-button-sm\", \"p-button-raised\", \"p-button-rounded\", \"blue-teal\", \"mt-3\", 3, \"click\"], [1, \"library-edit-title\", \"justify-content-center\", \"my-2\"], [\"src\", \"/assets/icons/library/surface1.svg\"], [1, \"font-sm\", \"text-800\", \"in-title\", \"p-2\", \"flex\", \"gap-3\"], [1, \"file-path-text\", \"w-15rem\"], [1, \"pi\", \"pi-pencil\", \"pointer\", 3, \"click\"], [1, \"library-info\"], [1, \"w-full\", \"flex\", \"align-items-center\", \"justify-content-center\", \"py-1\"], [1, \"library-edit-title\", \"justify-content-center\", \"align-items-center\"], [1, \"font-sm\", \"text-800\", \"in-title\", \"flex\", \"gap-3\", \"px-2\", \"align-items-center\"], [\"id\", \"fileName\", 1, \"input-element\", \"new-filename-input\", \"file-path-text\", \"w-15rem\", 3, \"ngModelChange\", \"ngModel\"]],\n    template: function LibraryFileActionsDialogComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"app-block-viewer\", 0);\n        i0.ɵɵlistener(\"closeDialogEvent\", function LibraryFileActionsDialogComponent_Template_app_block_viewer_closeDialogEvent_0_listener() {\n          return ctx.onDialogClose();\n        });\n        i0.ɵɵtemplate(1, LibraryFileActionsDialogComponent_ng_container_1_Template, 10, 1, \"ng-container\", 1)(2, LibraryFileActionsDialogComponent_ng_container_2_Template, 6, 3, \"ng-container\", 1)(3, LibraryFileActionsDialogComponent_ng_container_3_Template, 6, 2, \"ng-container\", 1)(4, LibraryFileActionsDialogComponent_ng_container_4_Template, 10, 4, \"ng-container\", 1)(5, LibraryFileActionsDialogComponent_ng_container_5_Template, 4, 9, \"ng-container\", 1)(6, LibraryFileActionsDialogComponent_ng_container_6_Template, 8, 4, \"ng-container\", 1);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"header\", ctx.dialogData.dialogTitle ? ctx.dialogData.dialogTitle : ctx.dialogData.action)(\"headerClass\", \"justify-content-center my-0\")(\"headerTextClass\", \"font-base font-semibold justify-content-center capitalize\")(\"showCloseDialogIcon\", true);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.dialogData.action === \"rename\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.dialogData.action === \"shareWith\" || ctx.dialogData.action === \"unShareWith\" || ctx.dialogData.action === \"send\" || ctx.dialogData.action === \"send-and-share\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.dialogData.action === \"shareWithMultiple\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.dialogData.action === \"move\" || ctx.dialogData.action === \"moveMultiple\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.dialogData.action === \"upload\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.dialogData.action === \"editFileLevelsCategories\");\n      }\n    },\n    dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgModel, i9.ButtonDirective],\n    styles: [\".folder-hover[_ngcontent-%COMP%]:hover {\\n  background: rgb(167, 193, 249);\\n  background: linear-gradient(90deg, rgb(167, 193, 249) 0%, rgb(228, 236, 253) 100%);\\n}\\n\\n.circle[_ngcontent-%COMP%] {\\n  transition: background-color 0.2s ease-in-out;\\n}\\n.circle.blue-teal[_ngcontent-%COMP%] {\\n  background-color: #5BB7D0;\\n}\\n\\n.circle[_ngcontent-%COMP%]:hover {\\n  background-color: #e2e8f0;\\n}\\n\\n.bordered-tags[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  border: 1px solid #2E3D90;\\n  margin-top: 15px;\\n  width: 100%;\\n}\\n\\n.library-edit-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 18px;\\n}\\n.library-edit-title[_ngcontent-%COMP%]   .in-title[_ngcontent-%COMP%] {\\n  border-radius: 20px;\\n  background: #fff;\\n}\\n.library-edit-title[_ngcontent-%COMP%]    > img[_ngcontent-%COMP%] {\\n  width: 30px;\\n  margin-right: 15px;\\n}\\n\\n.new-filename-input[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  height: 36px;\\n  border: 1px solid transparent;\\n  font-size: 0.85rem;\\n  color: var(--surface-800);\\n  padding-inline: 0;\\n}\\n.new-filename-input[_ngcontent-%COMP%]:focus {\\n  outline-color: transparent;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["switchMap", "take", "UserRole", "SubSink", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtwoWayListener", "LibraryFileActionsDialogComponent_ng_container_1_Template_input_ngModelChange_4_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "dialogData", "libraryFile", "name", "ɵɵresetView", "ɵɵelementEnd", "ɵɵlistener", "LibraryFileActionsDialogComponent_ng_container_1_Template_button_click_6_listener", "onDialogClose", "action", "ɵɵelement", "ɵɵtext", "ɵɵadvance", "ɵɵtwoWayProperty", "LibraryFileActionsDialogComponent_ng_container_2_Template_app_classrooms_check_list_checkedList_3_listener", "_r3", "onCheckedList", "LibraryFileActionsDialogComponent_ng_container_2_Template_button_click_5_listener", "onSendSharedWith", "ɵɵtextInterpolate", "dialogsubTitle", "ɵɵproperty", "preselectedClassroom", "LibraryFileActionsDialogComponent_ng_container_3_Template_app_classrooms_check_list_checkedList_3_listener", "_r4", "LibraryFileActionsDialogComponent_ng_container_3_Template_button_click_5_listener", "LibraryFileActionsDialogComponent_ng_container_4_ng_container_7_Template_li_click_1_listener", "innerFolder_r7", "_r6", "$implicit", "moveSelected", "ɵɵstyleProp", "marginLeft", "ɵɵtextInterpolate1", "ɵɵpureFunction1", "_c0", "selectedFolder", "LibraryFileActionsDialogComponent_ng_container_4_Template_input_ngModelChange_4_listener", "_r5", "term", "ɵɵtemplate", "LibraryFileActionsDialogComponent_ng_container_4_ng_container_7_Template", "LibraryFileActionsDialogComponent_ng_container_4_Template_button_click_9_listener", "onMoveSubmitted", "folders", "LibraryFileActionsDialogComponent_ng_container_5_Template_app_upload_files_onSubmit_3_listener", "_r8", "onUploadedFilesSubmitted", "LibraryFileActionsDialogComponent_ng_container_5_Template_app_upload_files_onUploadFinished_3_listener", "uploadFinished", "LibraryFileActionsDialogComponent_ng_container_5_Template_app_upload_files_tagChoosen_3_listener", "onTagChoosen", "files", "libraryToAdd", "libraryFiles", "length", "LibraryFileActionsDialogComponent_ng_container_6_div_2_Template_i_click_5_listener", "_r10", "showEditRename", "getFileName", "modifiedFile", "LibraryFileActionsDialogComponent_ng_container_6_ng_container_3_Template_input_ngModelChange_7_listener", "_r11", "newFileName", "LibraryFileActionsDialogComponent_ng_container_6_ng_container_3_Template_i_click_8_listener", "LibraryFileActionsDialogComponent_ng_container_6_div_2_Template", "LibraryFileActionsDialogComponent_ng_container_6_ng_container_3_Template", "LibraryFileActionsDialogComponent_ng_container_6_Template_app_choose_tags_chooseTag_5_listener", "_r9", "onChooseTag", "LibraryFileActionsDialogComponent_ng_container_6_Template_button_click_7_listener", "onTagsSubmitted", "LibraryFileActionsDialogComponent", "constructor", "ref", "authService", "userService", "generalService", "libraryService", "toastService", "config", "subs", "role", "NONE", "UserRoles", "ngOnInit", "getLoggedInUser", "data", "console", "log", "setUploadFiles", "ngOnDestroy", "unsubscribe", "destroy", "close", "event", "sharedWithMap", "map", "folder", "getParentFolders", "parentFolders", "filter", "f", "parent", "childFolders", "concat", "sortFolders", "sortedFolders", "for<PERSON>ach", "push", "sort", "a", "b", "localeCompare", "children", "child", "sorted<PERSON><PERSON><PERSON><PERSON>", "some", "folderId", "path", "filePath", "db<PERSON><PERSON>", "levels", "categories", "fileId", "isLevel", "checked", "applyToAll", "file", "arrLibrary", "tag", "index", "index2", "findIndex", "el", "splice", "params", "fileNameWithPath", "split", "pop", "fileNameParts", "fileNameWithoutExtension", "slice", "join", "deepCopy", "obj", "copy", "Array", "i", "key", "hasOwnProperty", "checkbox", "isCategory", "category", "level", "newLevels", "newCategories", "rename", "add", "editFileLevels", "pipe", "res", "editFileCategories", "subscribe", "result", "setShowToastmessage", "severity", "summary", "detail", "newName", "updateLibraryFileName", "_", "ɵɵdirectiveInject", "i1", "DynamicDialogRef", "i2", "AuthService", "i3", "UserService", "i4", "GeneralService", "i5", "LibraryService", "i6", "ToastService", "DynamicDialogConfig", "_2", "selectors", "decls", "vars", "consts", "template", "LibraryFileActionsDialogComponent_Template", "rf", "ctx", "LibraryFileActionsDialogComponent_Template_app_block_viewer_closeDialogEvent_0_listener", "LibraryFileActionsDialogComponent_ng_container_1_Template", "LibraryFileActionsDialogComponent_ng_container_2_Template", "LibraryFileActionsDialogComponent_ng_container_3_Template", "LibraryFileActionsDialogComponent_ng_container_4_Template", "LibraryFileActionsDialogComponent_ng_container_5_Template", "LibraryFileActionsDialogComponent_ng_container_6_Template", "dialogTitle"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\library\\library\\components\\library-file-actions-dialog\\library-file-actions-dialog.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\library\\library\\components\\library-file-actions-dialog\\library-file-actions-dialog.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';\r\nimport { switchMap, take } from 'rxjs/operators';\r\nimport { Level } from 'src/app/core/models/classroom.model';\r\nimport { Category } from 'src/app/core/models/homework.model';\r\nimport { Folder, Library, LibraryFile } from 'src/app/core/models/library.model';\r\nimport { UserRole } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { LibraryService } from 'src/app/core/services/library.service';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { UserService } from 'src/app/core/services/user.service';\r\nimport { SubSink } from 'subsink';\r\n\r\n@Component({\r\n  selector: 'app-library-file-actions-dialog',\r\n  templateUrl: './library-file-actions-dialog.component.html',\r\n  styleUrls: ['./library-file-actions-dialog.component.scss']\r\n})\r\nexport class LibraryFileActionsDialogComponent implements OnInit {\r\n\r\n  private subs = new SubSink();\r\n  role: UserRole = UserRole.NONE;\r\n  UserRoles = UserRole;\r\n  dialogData: any = {};\r\n  sharedWithMap: any;\r\n  selectedFolder!: Folder;\r\n  term: any;\r\n  libraryToAdd: Library = {} as Library;\r\n  files = [] as LibraryFile[];\r\n  modifiedFile = {} as LibraryFile;\r\n  showEditRename = false;\r\n  newFileName = '';\r\n  constructor(\r\n    private ref: DynamicDialogRef,\r\n    private authService: AuthService,\r\n    private userService: UserService,\r\n    private generalService: GeneralService,\r\n    private libraryService: LibraryService,\r\n    private toastService: ToastService,\r\n    private config: DynamicDialogConfig,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.role = this.authService.getLoggedInUser().role;\r\n    this.dialogData = this.config.data.dialogData;\r\n    console.log(this.dialogData);\r\n    this.userService.setUploadFiles(true);\r\n    this.files = [];\r\n    if (this.dialogData.libraryFile) {\r\n      this.newFileName = this.dialogData.libraryFile.name;\r\n      this.modifiedFile = this.dialogData.libraryFile;\r\n    }\r\n    // this.sortFolders(this.dialogData.folders);\r\n    // console.log((this.sortFolders(this.dialogData.folders)))\r\n\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n    this.ref.destroy();\r\n  }\r\n\r\n  onDialogClose(data?: any) {\r\n    console.log(data);\r\n    this.ref.close(data);\r\n    this.ref.destroy();\r\n  }\r\n\r\n  onCheckedList(event: any) {\r\n    console.log(event);\r\n    this.sharedWithMap = event;\r\n  }\r\n\r\n  onSendSharedWith() {\r\n    this.onDialogClose({\r\n      action: this.dialogData.action,\r\n      map: this.sharedWithMap\r\n    })\r\n  }\r\n\r\n  moveSelected(folder: Folder) {\r\n    this.selectedFolder = folder;\r\n    console.log(folder);\r\n  }\r\n\r\n  /**\r\n   * Executes when the move is submitted.\r\n   *\r\n   * @param {type} paramName - description of parameter\r\n   * @return {type} description of return value\r\n   */\r\n  onMoveSubmitted() {\r\n    console.log(this.selectedFolder);\r\n    this.onDialogClose({\r\n      action: this.dialogData.action,\r\n      libraryFile: this.dialogData.libraryFile,\r\n      folder: this.selectedFolder\r\n    })\r\n  }\r\n\r\n  /**\r\n   * Retrieves the parent folders from the dialogData object.\r\n   *\r\n   * @return {Array} An array containing objects representing the parent folders,\r\n   * their child folders, and the library files associated with each parent folder.\r\n   */\r\n  getParentFolders() {\r\n    const parentFolders = this.dialogData.folders.filter((f: any) => !f.parent);\r\n    return parentFolders.map((parent: any) => {\r\n      const childFolders = this.dialogData.folders.filter((f: any) => f.parent === parent.name);\r\n      const libraryFiles = parent.libraryFiles;\r\n      return { parent, childFolders, libraryFiles };\r\n    }).concat(this.dialogData.folders.filter((f: any) => f.parent));\r\n  }\r\n\r\n  /**\r\n   * Sorts an array of folders by separating them into parent folders and child folders,\r\n   * sorting the parent folders by name, and adding the child folders to their respective parent folders.\r\n   *\r\n   * @param {any[]} folders - The array of folders to be sorted.\r\n   * @return {any[]} - The sorted array of folders.\r\n   */\r\n  sortFolders(folders: any[]): any[] {\r\n    const sortedFolders: any[] = [];\r\n    const parentFolders: any[] = [];\r\n    const childFolders: any[] = [];\r\n  \r\n    // Separate parent folders and child folders\r\n    folders.forEach(folder => {\r\n      if (folder.parent === '') {\r\n        parentFolders.push(folder);\r\n      } else {\r\n        childFolders.push(folder);\r\n      }\r\n    });\r\n  \r\n    // Sort parent folders by name\r\n    parentFolders.sort((a, b) => a.name.localeCompare(b.name));\r\n  \r\n    // Add parent folders to sorted array\r\n    parentFolders.forEach(folder => {\r\n      sortedFolders.push(folder);\r\n      const children = childFolders.filter(child => child.parent === folder.name);\r\n      const sortedChildren = this.sortFolders(children);\r\n      sortedChildren.forEach(child => {\r\n        sortedFolders.push({...child, marginLeft: 10 });\r\n      });\r\n    });\r\n  \r\n    // Add child folders to sorted array\r\n    childFolders.forEach(folder => {\r\n      if (!sortedFolders.some(f => f.folderId === folder.folderId)) {\r\n        sortedFolders.push({...folder, marginLeft: 10 });\r\n      }\r\n    });\r\n  \r\n    return sortedFolders;\r\n  }\r\n\r\n  /**\r\n   * Uploads the finished event.\r\n   *\r\n   * @param {any} event - The event object.\r\n   * @return {void} This function does not return anything.\r\n   */\r\n  uploadFinished(event: any) {\r\n    this.files.push({ \r\n      path: event.filePath.dbPath, \r\n      levels: [], \r\n      categories: [], \r\n      name: event.filePath.dbPath, \r\n      fileId: event.filePath.dbPath });\r\n      console.log(this.files);\r\n  }\r\n\r\n  /**\r\n   * Handles the event when a tag is chosen.\r\n   *\r\n   * @param {any} event - The event object containing information about the tag selection.\r\n   */\r\n  onTagChoosen(event: any) {\r\n    let isLevel = event.isLevel;\r\n    if (event.checked) {\r\n      if (event.applyToAll) {\r\n        for (let file of this.files) {\r\n          let arrLibrary: string[] = isLevel ? file.levels : file.categories;\r\n          arrLibrary.push(event.tag)\r\n        }\r\n      } else {\r\n        let file = this.files[event.file.index]\r\n        let arrLibrary: string[] = isLevel ? file.levels : file.categories;\r\n        arrLibrary.push(event.tag)\r\n      }\r\n    } else {\r\n      if (event.applyToAll) {\r\n        for (let file of this.files) {\r\n          let arrLibrary: string[] = isLevel ? file.levels : file.categories;\r\n          let index2 = arrLibrary.findIndex(((el: string) => el == event.tag));\r\n          arrLibrary.splice(index2, 1);\r\n        }\r\n      } else {\r\n        let file = this.files[event.file.index]\r\n        let arrLibrary: string[] = isLevel ? file.levels : file.categories;\r\n        let index2 = arrLibrary.findIndex(((el: string) => el == event.tag));\r\n        arrLibrary.splice(index2, 1);\r\n      }\r\n    }\r\n  }\r\n\r\n  onUploadedFilesSubmitted() {\r\n    const params = {\r\n      action: this.dialogData.action,\r\n      libraryFiles: this.files,\r\n      folder: this.dialogData.folder\r\n    };\r\n    console.log(params);\r\n    this.onDialogClose(params)\r\n  }\r\n\r\n  /**\r\n   * Retrieves the file name from a given file path.\r\n   *\r\n   * @param {string} filePath - The file path.\r\n   * @return {string | undefined} The file name without extension if it exists, otherwise the entire file name.\r\n   */\r\n  getFileName(filePath: string): string | undefined {\r\n    const fileNameWithPath = filePath.split('\\\\').pop();\r\n    const fileNameParts = fileNameWithPath!.split('.');\r\n    \r\n    if (fileNameParts.length > 1) {\r\n      const fileNameWithoutExtension = fileNameParts.slice(0, -1).join('.');\r\n      return fileNameWithoutExtension;\r\n    }\r\n    \r\n    return fileNameWithPath;\r\n  }\r\n\r\n  /**\r\n   * A function that creates a deep copy of an object.\r\n   *\r\n   * @param {any} obj - The object to be deep copied.\r\n   * @return {any} The deep copy of the object.\r\n   */\r\n  deepCopy(obj: any): any {\r\n    if (typeof obj !== 'object' || obj === null) {\r\n      return obj;\r\n    }\r\n  \r\n    let copy: any;\r\n  \r\n    if (obj instanceof Array) {\r\n      copy = [];\r\n      for (let i = 0; i < obj.length; i++) {\r\n        copy[i] = this.deepCopy(obj[i]);\r\n      }\r\n    } else {\r\n      copy = {};\r\n      for (const key in obj) {\r\n        if (obj.hasOwnProperty(key)) {\r\n          copy[key] = this.deepCopy(obj[key]);\r\n        }\r\n      }\r\n    }\r\n  \r\n    return copy;\r\n  }\r\n\r\n  onChooseTag(checkbox: any) {\r\n    const libraryFile = this.deepCopy(this.modifiedFile);\r\n\r\n    if (checkbox.checked) {\r\n      if (checkbox.isCategory) {\r\n        // Update categories in libraryFile\r\n        libraryFile.categories.push(checkbox.tag);\r\n      }\r\n      if (checkbox.isLevel) {\r\n        // Update levels in libraryFile\r\n        libraryFile.levels.push(checkbox.tag);\r\n      }\r\n    } else {\r\n      if (checkbox.isCategory) {\r\n        // Remove category from libraryFile\r\n        libraryFile.categories = libraryFile.categories.filter((category: Category) => category !== checkbox.tag);\r\n      }\r\n      if (checkbox.isLevel) {\r\n        // Remove level from libraryFile\r\n        libraryFile.levels = libraryFile.levels.filter((level: Level) => level !== checkbox.tag);\r\n      }\r\n    }\r\n\r\n    this.modifiedFile = libraryFile;\r\n  }\r\n\r\n  onTagsSubmitted() {\r\n    const libraryFile = this.deepCopy(this.modifiedFile);\r\n    const newLevels = {\r\n      fileId: libraryFile.fileId,\r\n      levels: this.modifiedFile.levels\r\n    }\r\n    const newCategories = {\r\n      fileId: libraryFile.fileId,\r\n      categories: this.modifiedFile.categories\r\n    }\r\n    this.rename(this.modifiedFile.fileId!, this.newFileName);\r\n    this.subs.add(this.libraryService.editFileLevels(newLevels).pipe(\r\n      switchMap((res) => {\r\n        console.log(res);\r\n        return this.libraryService.editFileCategories(newCategories);\r\n      })\r\n    ).subscribe((res) => {\r\n      console.log(res);\r\n      const params = {\r\n        action: this.dialogData.action,\r\n        result: this.modifiedFile,\r\n      };\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'success',\r\n        summary: '',\r\n        detail: 'Library file saved successfully.'\r\n      });\r\n      console.log(this.modifiedFile);\r\n      this.onDialogClose(params);\r\n    }));\r\n  }\r\n\r\n  rename(fileId: number, newName: string) {\r\n    this.subs.add(this.libraryService.updateLibraryFileName(fileId, newName).pipe(take(1)).subscribe(res => {\r\n      this.modifiedFile.name = this.newFileName;\r\n    }));\r\n  }\r\n}\r\n", "<app-block-viewer [header]=\"this.dialogData.dialogTitle ? this.dialogData.dialogTitle : this.dialogData.action\"\r\n    headerBackgroundImage=\"/assets/images/dashboard/calendar/calendar-dialog-lesson-info-arranged.png\"\r\n    blockClass=\"border-radius-bottom-10\" headerBlockClass=\"py-1 border-round-lg bg-cover sticky top-0 z-5 \"\r\n    containerClass=\"block-gradient-reverse px-3 py-2 relative\" [headerClass]=\"'justify-content-center my-0'\"\r\n    [headerTextClass]=\"'font-base font-semibold justify-content-center capitalize'\" [showCloseDialogIcon]=\"true\"\r\n    (closeDialogEvent)=\"onDialogClose()\">\r\n\r\n\r\n    <ng-container *ngIf=\"this.dialogData.action === 'rename'\">\r\n        <div class=\"library-info grid\">\r\n            <div class=\"w-full flex align-items-center justify-content-center py-2\">\r\n                <div class=\"library-file-details py-1 font-sm flex flex-column\">\r\n                    <input id=\"fileName\" class=\"input-element\" [(ngModel)]=\"this.dialogData.libraryFile.name\">\r\n                    <div class=\"flex align-items-center justify-content-center text-center mt-2\">\r\n                        <button (click)=\"onDialogClose({action: 'rename', libraryFile: this.dialogData.libraryFile})\"\r\n                            pRipple pbutton label=\"\" icon=\"pi pi-discord\" class=\"element ripple p-button-raised p-button-rounded white-space-nowrap p-button btn-gradient-blue p-component w-10rem \r\n                        font-lg flex justify-content-center\">\r\n                            <span class=\"p-button-icon p-button-icon-right\" aria-hidden=\"true\"></span>\r\n                            Submit\r\n                            <img src=\"/assets/icons/forward-arrow.svg\" width=\"18\" class=\"ml-2\" />\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n\r\n            </div>\r\n        </div>\r\n    </ng-container>\r\n\r\n    <ng-container *ngIf=\"this.dialogData.action === 'shareWith' || this.dialogData.action === 'unShareWith' || this.dialogData.action === 'send' || this.dialogData.action === 'send-and-share'\">\r\n        <h4 class=\"font-base\">{{this.dialogData.dialogsubTitle}}</h4>\r\n        <app-classrooms-check-list [preselectedClassroom]=\"this.dialogData.preselectedClassroom\" [showBlur]=\"true\" (checkedList)=\"onCheckedList($event)\">\r\n        </app-classrooms-check-list>\r\n        <div class=\"py-2 sticky bottom-0 flex  justify-content-center align-items-center\">\r\n            <button pButton pRipple type=\"button\" label=\"Ok\" (click)=\"onSendSharedWith()\" icon=\"pi pi-check\"\r\n                iconPos=\"right\" class=\"p-button-sm p-button-raised p-button-rounded blue-teal mt-3\"></button>\r\n        </div>\r\n    </ng-container>\r\n\r\n    <ng-container *ngIf=\"this.dialogData.action === 'shareWithMultiple'\">\r\n        <h4 class=\"font-base\">{{this.dialogData.dialogsubTitle}}</h4>\r\n        <app-classrooms-check-list [showBlur]=\"true\" (checkedList)=\"onCheckedList($event)\">\r\n        </app-classrooms-check-list>\r\n        <div class=\"py-2 sticky bottom-0 flex  justify-content-center align-items-center\">\r\n            <button pButton pRipple type=\"button\" label=\"Ok\" (click)=\"onSendSharedWith()\" icon=\"pi pi-check\"\r\n                iconPos=\"right\" class=\"p-button-sm p-button-raised p-button-rounded blue-teal mt-3\"></button>\r\n        </div>\r\n    </ng-container>\r\n\r\n    <ng-container *ngIf=\"this.dialogData.action === 'move' || this.dialogData.action === 'moveMultiple'\">\r\n        <h4 class=\"font-base\">{{this.dialogData.dialogsubTitle}}</h4>\r\n        <span class=\"p-input-icon-right w-full\">\r\n            <input type=\"text\" [(ngModel)]=\"term\" pInputText placeholder=\"Search\" class=\"h-2rem w-full input-blue gradient-blue rounded\" />\r\n            <i class=\"pi pi-search text-primary\"></i>\r\n        </span>\r\n\r\n        <ul class=\"p-0 mx-0 mt-2 mb-1 list-none overflow-y-scroll h-11rem\">\r\n            <!--  | filter: term -->\r\n            <ng-container *ngFor=\"let innerFolder of (this.dialogData.folders)\">\r\n            <li class=\"flex align-items-center py-1 pr-1 border-bottom-1 surface-border folder-hover pointer\"\r\n                 (click)=\"moveSelected(innerFolder)\">\r\n                <div class=\"w-2rem h-1rem flex align-items-center justify-content-center mr-3 flex-shrink-0\">\r\n                    <i class=\"pi pi pi-folder text-blue-500\"></i>\r\n                </div><span class=\"text-900 line-height-3\">\r\n                    <span class=\"text-700\" [style.marginLeft.px]=\"innerFolder.marginLeft\"> {{innerFolder.name}}</span></span>\r\n                <div class=\"circle ml-auto border-circle w-1rem h-1rem cursor-pointer\"\r\n                    [ngClass]=\"{'blue-teal': selectedFolder === innerFolder}\">\r\n                </div>\r\n            </li>\r\n        </ng-container>\r\n        </ul>\r\n        <div class=\"bg-white py-1 sticky bottom-0 flex  justify-content-center align-items-center\">\r\n            <button pButton pRipple type=\"button\" [disabled]=\"!selectedFolder\" label=\"Ok\" (click)=\"onMoveSubmitted()\" icon=\"pi pi-check\"\r\n                iconPos=\"right\" class=\"p-button-sm p-button-raised p-button-rounded blue-teal mt-3\"></button>\r\n        </div>\r\n    </ng-container>\r\n\r\n    <ng-container *ngIf=\"this.dialogData.action === 'upload'\">\r\n        <h4 class=\"font-base\">{{this.dialogData.dialogsubTitle}}</h4>\r\n        <app-upload-files (onSubmit)=\"onUploadedFilesSubmitted()\" class=\"p-20\" title=\"Library\"\r\n        [fileDropImage]=\"'/assets/icons/library/upload-file-lib.svg'\" [fileDropImageWidth]=\"10\"\r\n        [isProfile]=\"false\"\r\n        [files]=\"files\"\r\n        [teacherFilesLength]=\"dialogData.libraryToAdd.libraryFiles.length\" [dndUI]=\"true\" [showSubmitButton]=\"true\"\r\n        [checkboxUI]=\"true\" postUrl=\"/Upload/UploadLibraryFile\" (onUploadFinished)=\"uploadFinished($event)\"\r\n        (tagChoosen)=\"onTagChoosen($event)\">\r\n        </app-upload-files>\r\n    </ng-container>\r\n    \r\n    <ng-container *ngIf=\"this.dialogData.action === 'editFileLevelsCategories'\">\r\n        <div class=\"bordered-tags\">\r\n            <div class=\"library-edit-title justify-content-center my-2\" *ngIf=\"!showEditRename\">\r\n                <img src=\"/assets/icons/library/surface1.svg\">\r\n                <label class=\"font-sm text-800 in-title p-2 flex gap-3\">\r\n                    <span class=\"file-path-text w-15rem\">{{getFileName(modifiedFile.name)}}</span>\r\n                    <i class=\"pi pi-pencil pointer\" (click)=\"showEditRename = !showEditRename\"></i>\r\n                </label>\r\n            </div>\r\n            <ng-container *ngIf=\"showEditRename\">\r\n                <div class=\"library-info\">\r\n                    <div class=\"w-full flex align-items-center justify-content-center py-1\">\r\n                        <div class=\"library-file-details py-1 font-sm flex flex-column\">\r\n                            \r\n                            \r\n            <div class=\"library-edit-title justify-content-center align-items-center\">\r\n                <img src=\"/assets/icons/library/surface1.svg\">\r\n                <label class=\"font-sm text-800 in-title flex gap-3 px-2 align-items-center\">\r\n                    <input id=\"fileName\" class=\"input-element new-filename-input file-path-text w-15rem\" [ngModel]=\"getFileName(modifiedFile.name)\"\r\n                        (ngModelChange)=\"newFileName = $event\">\r\n                    <i class=\"pi pi-pencil pointer\" (click)=\"showEditRename = !showEditRename\"></i>\r\n                </label>\r\n            </div>\r\n\r\n                \r\n                            <!-- <div class=\"flex align-items-center justify-content-center text-center mt-2\">\r\n                                <button\r\n                                    (click)=\"onDialogClose({action: 'rename', libraryFile: this.dialogData.libraryFile})\"\r\n                                    pRipple pbutton label=\"\" icon=\"pi pi-discord\" class=\"element ripple p-button-raised p-button-rounded white-space-nowrap p-button btn-gradient-blue p-component w-10rem \r\n                            font-lg flex justify-content-center\">\r\n                                    <span class=\"p-button-icon p-button-icon-right\" aria-hidden=\"true\"></span>\r\n                                    Submit\r\n                                    <img src=\"/assets/icons/forward-arrow.svg\" width=\"18\" class=\"ml-2\" />\r\n                                </button>\r\n                            </div> -->\r\n                        </div>\r\n    \r\n                    </div>\r\n                </div>\r\n            </ng-container>\r\n        </div>\r\n        <div class=\"bordered-tags\">\r\n            <app-choose-tags [applyToAll]=\"false\" [libraryFile]=\"this.dialogData.libraryFile\"\r\n                (chooseTag)=\"onChooseTag($event)\"></app-choose-tags>\r\n        </div>\r\n    \r\n        <div class=\"py-1 sticky bottom-0 flex  justify-content-center align-items-center\">\r\n            <button pButton pRipple type=\"button\" (click)=\"onTagsSubmitted()\" label=\"Save\" icon=\"pi pi-check\" iconPos=\"right\"\r\n                class=\"p-button-sm p-button-raised p-button-rounded blue-teal mt-3\"></button>\r\n        </div>\r\n    </ng-container>\r\n\r\n</app-block-viewer>"], "mappings": "AAEA,SAASA,SAAS,EAAEC,IAAI,QAAQ,gBAAgB;AAIhD,SAASC,QAAQ,QAAQ,gCAAgC;AAMzD,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;;;;;;ICJ7BC,EAAA,CAAAC,uBAAA,GAA0D;IAI1CD,EAHZ,CAAAE,cAAA,aAA+B,aAC6C,aACJ,eAC8B;IAA/CF,EAAA,CAAAG,gBAAA,2BAAAC,yFAAAC,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAT,EAAA,CAAAU,kBAAA,CAAAF,MAAA,CAAAG,UAAA,CAAAC,WAAA,CAAAC,IAAA,EAAAR,MAAA,MAAAG,MAAA,CAAAG,UAAA,CAAAC,WAAA,CAAAC,IAAA,GAAAR,MAAA;MAAA,OAAAL,EAAA,CAAAc,WAAA,CAAAT,MAAA;IAAA,EAA8C;IAAzFL,EAAA,CAAAe,YAAA,EAA0F;IAEtFf,EADJ,CAAAE,cAAA,aAA6E,gBAGpC;IAF7BF,EAAA,CAAAgB,UAAA,mBAAAC,kFAAA;MAAAjB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAc,WAAA,CAASN,MAAA,CAAAU,aAAA,CAAc;QAAAC,MAAA,EAAS,QAAQ;QAAAP,WAAA,EAAAJ,MAAA,CAAAG,UAAA,CAAAC;MAAA,CAA2C,CAAC;IAAA,EAAC;IAGzFZ,EAAA,CAAAoB,SAAA,cAA0E;IAC1EpB,EAAA,CAAAqB,MAAA,eACA;IAAArB,EAAA,CAAAoB,SAAA,aAAqE;IAMzFpB,EALgB,CAAAe,YAAA,EAAS,EACP,EACJ,EAEJ,EACJ;;;;;IAbiDf,EAAA,CAAAsB,SAAA,GAA8C;IAA9CtB,EAAA,CAAAuB,gBAAA,YAAAf,MAAA,CAAAG,UAAA,CAAAC,WAAA,CAAAC,IAAA,CAA8C;;;;;;IAgBzGb,EAAA,CAAAC,uBAAA,GAA6L;IACzLD,EAAA,CAAAE,cAAA,aAAsB;IAAAF,EAAA,CAAAqB,MAAA,GAAkC;IAAArB,EAAA,CAAAe,YAAA,EAAK;IAC7Df,EAAA,CAAAE,cAAA,oCAAiJ;IAAtCF,EAAA,CAAAgB,UAAA,yBAAAQ,2GAAAnB,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAmB,GAAA;MAAA,MAAAjB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAc,WAAA,CAAeN,MAAA,CAAAkB,aAAA,CAAArB,MAAA,CAAqB;IAAA,EAAC;IAChJL,EAAA,CAAAe,YAAA,EAA4B;IAExBf,EADJ,CAAAE,cAAA,cAAkF,iBAEU;IADvCF,EAAA,CAAAgB,UAAA,mBAAAW,kFAAA;MAAA3B,EAAA,CAAAM,aAAA,CAAAmB,GAAA;MAAA,MAAAjB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAc,WAAA,CAASN,MAAA,CAAAoB,gBAAA,EAAkB;IAAA,EAAC;IAEjF5B,EAD4F,CAAAe,YAAA,EAAS,EAC/F;;;;;IANgBf,EAAA,CAAAsB,SAAA,GAAkC;IAAlCtB,EAAA,CAAA6B,iBAAA,CAAArB,MAAA,CAAAG,UAAA,CAAAmB,cAAA,CAAkC;IAC7B9B,EAAA,CAAAsB,SAAA,EAA6D;IAACtB,EAA9D,CAAA+B,UAAA,yBAAAvB,MAAA,CAAAG,UAAA,CAAAqB,oBAAA,CAA6D,kBAAkB;;;;;;IAQ9GhC,EAAA,CAAAC,uBAAA,GAAqE;IACjED,EAAA,CAAAE,cAAA,aAAsB;IAAAF,EAAA,CAAAqB,MAAA,GAAkC;IAAArB,EAAA,CAAAe,YAAA,EAAK;IAC7Df,EAAA,CAAAE,cAAA,oCAAmF;IAAtCF,EAAA,CAAAgB,UAAA,yBAAAiB,2GAAA5B,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAc,WAAA,CAAeN,MAAA,CAAAkB,aAAA,CAAArB,MAAA,CAAqB;IAAA,EAAC;IAClFL,EAAA,CAAAe,YAAA,EAA4B;IAExBf,EADJ,CAAAE,cAAA,cAAkF,iBAEU;IADvCF,EAAA,CAAAgB,UAAA,mBAAAmB,kFAAA;MAAAnC,EAAA,CAAAM,aAAA,CAAA4B,GAAA;MAAA,MAAA1B,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAc,WAAA,CAASN,MAAA,CAAAoB,gBAAA,EAAkB;IAAA,EAAC;IAEjF5B,EAD4F,CAAAe,YAAA,EAAS,EAC/F;;;;;IANgBf,EAAA,CAAAsB,SAAA,GAAkC;IAAlCtB,EAAA,CAAA6B,iBAAA,CAAArB,MAAA,CAAAG,UAAA,CAAAmB,cAAA,CAAkC;IAC7B9B,EAAA,CAAAsB,SAAA,EAAiB;IAAjBtB,EAAA,CAAA+B,UAAA,kBAAiB;;;;;;IAiBxC/B,EAAA,CAAAC,uBAAA,GAAoE;IACpED,EAAA,CAAAE,cAAA,aACyC;IAApCF,EAAA,CAAAgB,UAAA,mBAAAoB,6FAAA;MAAA,MAAAC,cAAA,GAAArC,EAAA,CAAAM,aAAA,CAAAgC,GAAA,EAAAC,SAAA;MAAA,MAAA/B,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAc,WAAA,CAASN,MAAA,CAAAgC,YAAA,CAAAH,cAAA,CAAyB;IAAA,EAAC;IACpCrC,EAAA,CAAAE,cAAA,cAA6F;IACzFF,EAAA,CAAAoB,SAAA,YAA6C;IACjDpB,EAAA,CAAAe,YAAA,EAAM;IACFf,EADE,CAAAE,cAAA,eAAqC,eAC+B;IAACF,EAAA,CAAAqB,MAAA,GAAoB;IAAOrB,EAAP,CAAAe,YAAA,EAAO,EAAO;IAC7Gf,EAAA,CAAAoB,SAAA,cAEM;IACVpB,EAAA,CAAAe,YAAA,EAAK;;;;;;IAJ0Bf,EAAA,CAAAsB,SAAA,GAA8C;IAA9CtB,EAAA,CAAAyC,WAAA,gBAAAJ,cAAA,CAAAK,UAAA,OAA8C;IAAE1C,EAAA,CAAAsB,SAAA,EAAoB;IAApBtB,EAAA,CAAA2C,kBAAA,MAAAN,cAAA,CAAAxB,IAAA,KAAoB;IAE3Fb,EAAA,CAAAsB,SAAA,EAAyD;IAAzDtB,EAAA,CAAA+B,UAAA,YAAA/B,EAAA,CAAA4C,eAAA,IAAAC,GAAA,EAAArC,MAAA,CAAAsC,cAAA,KAAAT,cAAA,EAAyD;;;;;;IAjBzErC,EAAA,CAAAC,uBAAA,GAAqG;IACjGD,EAAA,CAAAE,cAAA,aAAsB;IAAAF,EAAA,CAAAqB,MAAA,GAAkC;IAAArB,EAAA,CAAAe,YAAA,EAAK;IAEzDf,EADJ,CAAAE,cAAA,eAAwC,gBAC2F;IAA5GF,EAAA,CAAAG,gBAAA,2BAAA4C,yFAAA1C,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAA0C,GAAA;MAAA,MAAAxC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAT,EAAA,CAAAU,kBAAA,CAAAF,MAAA,CAAAyC,IAAA,EAAA5C,MAAA,MAAAG,MAAA,CAAAyC,IAAA,GAAA5C,MAAA;MAAA,OAAAL,EAAA,CAAAc,WAAA,CAAAT,MAAA;IAAA,EAAkB;IAArCL,EAAA,CAAAe,YAAA,EAA+H;IAC/Hf,EAAA,CAAAoB,SAAA,YAAyC;IAC7CpB,EAAA,CAAAe,YAAA,EAAO;IAEPf,EAAA,CAAAE,cAAA,aAAmE;IAE/DF,EAAA,CAAAkD,UAAA,IAAAC,wEAAA,2BAAoE;IAYxEnD,EAAA,CAAAe,YAAA,EAAK;IAEDf,EADJ,CAAAE,cAAA,cAA2F,iBAEC;IADVF,EAAA,CAAAgB,UAAA,mBAAAoC,kFAAA;MAAApD,EAAA,CAAAM,aAAA,CAAA0C,GAAA;MAAA,MAAAxC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAc,WAAA,CAASN,MAAA,CAAA6C,eAAA,EAAiB;IAAA,EAAC;IAE7GrD,EAD4F,CAAAe,YAAA,EAAS,EAC/F;;;;;IAxBgBf,EAAA,CAAAsB,SAAA,GAAkC;IAAlCtB,EAAA,CAAA6B,iBAAA,CAAArB,MAAA,CAAAG,UAAA,CAAAmB,cAAA,CAAkC;IAEjC9B,EAAA,CAAAsB,SAAA,GAAkB;IAAlBtB,EAAA,CAAAuB,gBAAA,YAAAf,MAAA,CAAAyC,IAAA,CAAkB;IAMCjD,EAAA,CAAAsB,SAAA,GAA4B;IAA5BtB,EAAA,CAAA+B,UAAA,YAAAvB,MAAA,CAAAG,UAAA,CAAA2C,OAAA,CAA4B;IAc5BtD,EAAA,CAAAsB,SAAA,GAA4B;IAA5BtB,EAAA,CAAA+B,UAAA,cAAAvB,MAAA,CAAAsC,cAAA,CAA4B;;;;;;IAK1E9C,EAAA,CAAAC,uBAAA,GAA0D;IACtDD,EAAA,CAAAE,cAAA,aAAsB;IAAAF,EAAA,CAAAqB,MAAA,GAAkC;IAAArB,EAAA,CAAAe,YAAA,EAAK;IAC7Df,EAAA,CAAAE,cAAA,2BAMoC;IAApCF,EANkB,CAAAgB,UAAA,sBAAAuC,+FAAA;MAAAvD,EAAA,CAAAM,aAAA,CAAAkD,GAAA;MAAA,MAAAhD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAc,WAAA,CAAYN,MAAA,CAAAiD,wBAAA,EAA0B;IAAA,EAAC,8BAAAC,uGAAArD,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAkD,GAAA;MAAA,MAAAhD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAc,WAAA,CAKmBN,MAAA,CAAAmD,cAAA,CAAAtD,MAAA,CAAsB;IAAA,EAAC,wBAAAuD,iGAAAvD,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAkD,GAAA;MAAA,MAAAhD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAc,WAAA,CACrFN,MAAA,CAAAqD,YAAA,CAAAxD,MAAA,CAAoB;IAAA,EAAC;IACnCL,EAAA,CAAAe,YAAA,EAAmB;;;;;IARGf,EAAA,CAAAsB,SAAA,GAAkC;IAAlCtB,EAAA,CAAA6B,iBAAA,CAAArB,MAAA,CAAAG,UAAA,CAAAmB,cAAA,CAAkC;IAExD9B,EAAA,CAAAsB,SAAA,EAA6D;IAI7DtB,EAJA,CAAA+B,UAAA,8DAA6D,0BAA0B,oBACpE,UAAAvB,MAAA,CAAAsD,KAAA,CACJ,uBAAAtD,MAAA,CAAAG,UAAA,CAAAoD,YAAA,CAAAC,YAAA,CAAAC,MAAA,CACmD,eAAe,0BAA0B,oBACxF;;;;;;IAOfjE,EAAA,CAAAE,cAAA,cAAoF;IAChFF,EAAA,CAAAoB,SAAA,cAA8C;IAE1CpB,EADJ,CAAAE,cAAA,gBAAwD,eACf;IAAAF,EAAA,CAAAqB,MAAA,GAAkC;IAAArB,EAAA,CAAAe,YAAA,EAAO;IAC9Ef,EAAA,CAAAE,cAAA,YAA2E;IAA3CF,EAAA,CAAAgB,UAAA,mBAAAkD,mFAAA;MAAAlE,EAAA,CAAAM,aAAA,CAAA6D,IAAA;MAAA,MAAA3D,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAc,WAAA,CAAAN,MAAA,CAAA4D,cAAA,IAAA5D,MAAA,CAAA4D,cAAA;IAAA,EAA0C;IAElFpE,EAFmF,CAAAe,YAAA,EAAI,EAC3E,EACN;;;;IAHuCf,EAAA,CAAAsB,SAAA,GAAkC;IAAlCtB,EAAA,CAAA6B,iBAAA,CAAArB,MAAA,CAAA6D,WAAA,CAAA7D,MAAA,CAAA8D,YAAA,CAAAzD,IAAA,EAAkC;;;;;;IAI/Eb,EAAA,CAAAC,uBAAA,GAAqC;IAMrCD,EALI,CAAAE,cAAA,cAA0B,cACkD,aACJ,cAGF;IACtEF,EAAA,CAAAoB,SAAA,cAA8C;IAE1CpB,EADJ,CAAAE,cAAA,gBAA4E,gBAE7B;IAAvCF,EAAA,CAAAgB,UAAA,2BAAAuD,wGAAAlE,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAc,WAAA,CAAAN,MAAA,CAAAiE,WAAA,GAAApE,MAAA;IAAA,EAAsC;IAD1CL,EAAA,CAAAe,YAAA,EAC2C;IAC3Cf,EAAA,CAAAE,cAAA,YAA2E;IAA3CF,EAAA,CAAAgB,UAAA,mBAAA0D,4FAAA;MAAA1E,EAAA,CAAAM,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAc,WAAA,CAAAN,MAAA,CAAA4D,cAAA,IAAA5D,MAAA,CAAA4D,cAAA;IAAA,EAA0C;IAkB9EpE,EAlB+E,CAAAe,YAAA,EAAI,EAC3E,EACN,EAaY,EAEJ,EACJ;;;;;IApBmFf,EAAA,CAAAsB,SAAA,GAA0C;IAA1CtB,EAAA,CAAA+B,UAAA,YAAAvB,MAAA,CAAA6D,WAAA,CAAA7D,MAAA,CAAA8D,YAAA,CAAAzD,IAAA,EAA0C;;;;;;IAlB/Ib,EAAA,CAAAC,uBAAA,GAA4E;IACxED,EAAA,CAAAE,cAAA,cAA2B;IAQvBF,EAPA,CAAAkD,UAAA,IAAAyB,+DAAA,kBAAoF,IAAAC,wEAAA,0BAO/C;IA+BzC5E,EAAA,CAAAe,YAAA,EAAM;IAEFf,EADJ,CAAAE,cAAA,cAA2B,0BAEe;IAAlCF,EAAA,CAAAgB,UAAA,uBAAA6D,+FAAAxE,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAwE,GAAA;MAAA,MAAAtE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAc,WAAA,CAAaN,MAAA,CAAAuE,WAAA,CAAA1E,MAAA,CAAmB;IAAA,EAAC;IACzCL,EAD0C,CAAAe,YAAA,EAAkB,EACtD;IAGFf,EADJ,CAAAE,cAAA,cAAkF,iBAEN;IADlCF,EAAA,CAAAgB,UAAA,mBAAAgE,kFAAA;MAAAhF,EAAA,CAAAM,aAAA,CAAAwE,GAAA;MAAA,MAAAtE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAc,WAAA,CAASN,MAAA,CAAAyE,eAAA,EAAiB;IAAA,EAAC;IAErEjF,EAD4E,CAAAe,YAAA,EAAS,EAC/E;;;;;IA/C2Df,EAAA,CAAAsB,SAAA,GAAqB;IAArBtB,EAAA,CAAA+B,UAAA,UAAAvB,MAAA,CAAA4D,cAAA,CAAqB;IAOnEpE,EAAA,CAAAsB,SAAA,EAAoB;IAApBtB,EAAA,CAAA+B,UAAA,SAAAvB,MAAA,CAAA4D,cAAA,CAAoB;IAiClBpE,EAAA,CAAAsB,SAAA,GAAoB;IAACtB,EAArB,CAAA+B,UAAA,qBAAoB,gBAAAvB,MAAA,CAAAG,UAAA,CAAAC,WAAA,CAA4C;;;AD/G7F,OAAM,MAAOsE,iCAAiC;EAc5CC,YACUC,GAAqB,EACrBC,WAAwB,EACxBC,WAAwB,EACxBC,cAA8B,EAC9BC,cAA8B,EAC9BC,YAA0B,EAC1BC,MAA2B;IAN3B,KAAAN,GAAG,GAAHA,GAAG;IACH,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,MAAM,GAANA,MAAM;IAnBR,KAAAC,IAAI,GAAG,IAAI5F,OAAO,EAAE;IAC5B,KAAA6F,IAAI,GAAa9F,QAAQ,CAAC+F,IAAI;IAC9B,KAAAC,SAAS,GAAGhG,QAAQ;IACpB,KAAAa,UAAU,GAAQ,EAAE;IAIpB,KAAAoD,YAAY,GAAY,EAAa;IACrC,KAAAD,KAAK,GAAG,EAAmB;IAC3B,KAAAQ,YAAY,GAAG,EAAiB;IAChC,KAAAF,cAAc,GAAG,KAAK;IACtB,KAAAK,WAAW,GAAG,EAAE;EASZ;EAEJsB,QAAQA,CAAA;IACN,IAAI,CAACH,IAAI,GAAG,IAAI,CAACP,WAAW,CAACW,eAAe,EAAE,CAACJ,IAAI;IACnD,IAAI,CAACjF,UAAU,GAAG,IAAI,CAAC+E,MAAM,CAACO,IAAI,CAACtF,UAAU;IAC7CuF,OAAO,CAACC,GAAG,CAAC,IAAI,CAACxF,UAAU,CAAC;IAC5B,IAAI,CAAC2E,WAAW,CAACc,cAAc,CAAC,IAAI,CAAC;IACrC,IAAI,CAACtC,KAAK,GAAG,EAAE;IACf,IAAI,IAAI,CAACnD,UAAU,CAACC,WAAW,EAAE;MAC/B,IAAI,CAAC6D,WAAW,GAAG,IAAI,CAAC9D,UAAU,CAACC,WAAW,CAACC,IAAI;MACnD,IAAI,CAACyD,YAAY,GAAG,IAAI,CAAC3D,UAAU,CAACC,WAAW;IACjD;IACA;IACA;EAEF;EAEAyF,WAAWA,CAAA;IACT,IAAI,CAACV,IAAI,CAACW,WAAW,EAAE;IACvB,IAAI,CAAClB,GAAG,CAACmB,OAAO,EAAE;EACpB;EAEArF,aAAaA,CAAC+E,IAAU;IACtBC,OAAO,CAACC,GAAG,CAACF,IAAI,CAAC;IACjB,IAAI,CAACb,GAAG,CAACoB,KAAK,CAACP,IAAI,CAAC;IACpB,IAAI,CAACb,GAAG,CAACmB,OAAO,EAAE;EACpB;EAEA7E,aAAaA,CAAC+E,KAAU;IACtBP,OAAO,CAACC,GAAG,CAACM,KAAK,CAAC;IAClB,IAAI,CAACC,aAAa,GAAGD,KAAK;EAC5B;EAEA7E,gBAAgBA,CAAA;IACd,IAAI,CAACV,aAAa,CAAC;MACjBC,MAAM,EAAE,IAAI,CAACR,UAAU,CAACQ,MAAM;MAC9BwF,GAAG,EAAE,IAAI,CAACD;KACX,CAAC;EACJ;EAEAlE,YAAYA,CAACoE,MAAc;IACzB,IAAI,CAAC9D,cAAc,GAAG8D,MAAM;IAC5BV,OAAO,CAACC,GAAG,CAACS,MAAM,CAAC;EACrB;EAEA;;;;;;EAMAvD,eAAeA,CAAA;IACb6C,OAAO,CAACC,GAAG,CAAC,IAAI,CAACrD,cAAc,CAAC;IAChC,IAAI,CAAC5B,aAAa,CAAC;MACjBC,MAAM,EAAE,IAAI,CAACR,UAAU,CAACQ,MAAM;MAC9BP,WAAW,EAAE,IAAI,CAACD,UAAU,CAACC,WAAW;MACxCgG,MAAM,EAAE,IAAI,CAAC9D;KACd,CAAC;EACJ;EAEA;;;;;;EAMA+D,gBAAgBA,CAAA;IACd,MAAMC,aAAa,GAAG,IAAI,CAACnG,UAAU,CAAC2C,OAAO,CAACyD,MAAM,CAAEC,CAAM,IAAK,CAACA,CAAC,CAACC,MAAM,CAAC;IAC3E,OAAOH,aAAa,CAACH,GAAG,CAAEM,MAAW,IAAI;MACvC,MAAMC,YAAY,GAAG,IAAI,CAACvG,UAAU,CAAC2C,OAAO,CAACyD,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAACC,MAAM,KAAKA,MAAM,CAACpG,IAAI,CAAC;MACzF,MAAMmD,YAAY,GAAGiD,MAAM,CAACjD,YAAY;MACxC,OAAO;QAAEiD,MAAM;QAAEC,YAAY;QAAElD;MAAY,CAAE;IAC/C,CAAC,CAAC,CAACmD,MAAM,CAAC,IAAI,CAACxG,UAAU,CAAC2C,OAAO,CAACyD,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAACC,MAAM,CAAC,CAAC;EACjE;EAEA;;;;;;;EAOAG,WAAWA,CAAC9D,OAAc;IACxB,MAAM+D,aAAa,GAAU,EAAE;IAC/B,MAAMP,aAAa,GAAU,EAAE;IAC/B,MAAMI,YAAY,GAAU,EAAE;IAE9B;IACA5D,OAAO,CAACgE,OAAO,CAACV,MAAM,IAAG;MACvB,IAAIA,MAAM,CAACK,MAAM,KAAK,EAAE,EAAE;QACxBH,aAAa,CAACS,IAAI,CAACX,MAAM,CAAC;MAC5B,CAAC,MAAM;QACLM,YAAY,CAACK,IAAI,CAACX,MAAM,CAAC;MAC3B;IACF,CAAC,CAAC;IAEF;IACAE,aAAa,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC5G,IAAI,CAAC8G,aAAa,CAACD,CAAC,CAAC7G,IAAI,CAAC,CAAC;IAE1D;IACAiG,aAAa,CAACQ,OAAO,CAACV,MAAM,IAAG;MAC7BS,aAAa,CAACE,IAAI,CAACX,MAAM,CAAC;MAC1B,MAAMgB,QAAQ,GAAGV,YAAY,CAACH,MAAM,CAACc,KAAK,IAAIA,KAAK,CAACZ,MAAM,KAAKL,MAAM,CAAC/F,IAAI,CAAC;MAC3E,MAAMiH,cAAc,GAAG,IAAI,CAACV,WAAW,CAACQ,QAAQ,CAAC;MACjDE,cAAc,CAACR,OAAO,CAACO,KAAK,IAAG;QAC7BR,aAAa,CAACE,IAAI,CAAC;UAAC,GAAGM,KAAK;UAAEnF,UAAU,EAAE;QAAE,CAAE,CAAC;MACjD,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACAwE,YAAY,CAACI,OAAO,CAACV,MAAM,IAAG;MAC5B,IAAI,CAACS,aAAa,CAACU,IAAI,CAACf,CAAC,IAAIA,CAAC,CAACgB,QAAQ,KAAKpB,MAAM,CAACoB,QAAQ,CAAC,EAAE;QAC5DX,aAAa,CAACE,IAAI,CAAC;UAAC,GAAGX,MAAM;UAAElE,UAAU,EAAE;QAAE,CAAE,CAAC;MAClD;IACF,CAAC,CAAC;IAEF,OAAO2E,aAAa;EACtB;EAEA;;;;;;EAMA1D,cAAcA,CAAC8C,KAAU;IACvB,IAAI,CAAC3C,KAAK,CAACyD,IAAI,CAAC;MACdU,IAAI,EAAExB,KAAK,CAACyB,QAAQ,CAACC,MAAM;MAC3BC,MAAM,EAAE,EAAE;MACVC,UAAU,EAAE,EAAE;MACdxH,IAAI,EAAE4F,KAAK,CAACyB,QAAQ,CAACC,MAAM;MAC3BG,MAAM,EAAE7B,KAAK,CAACyB,QAAQ,CAACC;KAAQ,CAAC;IAChCjC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACrC,KAAK,CAAC;EAC3B;EAEA;;;;;EAKAD,YAAYA,CAAC4C,KAAU;IACrB,IAAI8B,OAAO,GAAG9B,KAAK,CAAC8B,OAAO;IAC3B,IAAI9B,KAAK,CAAC+B,OAAO,EAAE;MACjB,IAAI/B,KAAK,CAACgC,UAAU,EAAE;QACpB,KAAK,IAAIC,IAAI,IAAI,IAAI,CAAC5E,KAAK,EAAE;UAC3B,IAAI6E,UAAU,GAAaJ,OAAO,GAAGG,IAAI,CAACN,MAAM,GAAGM,IAAI,CAACL,UAAU;UAClEM,UAAU,CAACpB,IAAI,CAACd,KAAK,CAACmC,GAAG,CAAC;QAC5B;MACF,CAAC,MAAM;QACL,IAAIF,IAAI,GAAG,IAAI,CAAC5E,KAAK,CAAC2C,KAAK,CAACiC,IAAI,CAACG,KAAK,CAAC;QACvC,IAAIF,UAAU,GAAaJ,OAAO,GAAGG,IAAI,CAACN,MAAM,GAAGM,IAAI,CAACL,UAAU;QAClEM,UAAU,CAACpB,IAAI,CAACd,KAAK,CAACmC,GAAG,CAAC;MAC5B;IACF,CAAC,MAAM;MACL,IAAInC,KAAK,CAACgC,UAAU,EAAE;QACpB,KAAK,IAAIC,IAAI,IAAI,IAAI,CAAC5E,KAAK,EAAE;UAC3B,IAAI6E,UAAU,GAAaJ,OAAO,GAAGG,IAAI,CAACN,MAAM,GAAGM,IAAI,CAACL,UAAU;UAClE,IAAIS,MAAM,GAAGH,UAAU,CAACI,SAAS,CAAGC,EAAU,IAAKA,EAAE,IAAIvC,KAAK,CAACmC,GAAI,CAAC;UACpED,UAAU,CAACM,MAAM,CAACH,MAAM,EAAE,CAAC,CAAC;QAC9B;MACF,CAAC,MAAM;QACL,IAAIJ,IAAI,GAAG,IAAI,CAAC5E,KAAK,CAAC2C,KAAK,CAACiC,IAAI,CAACG,KAAK,CAAC;QACvC,IAAIF,UAAU,GAAaJ,OAAO,GAAGG,IAAI,CAACN,MAAM,GAAGM,IAAI,CAACL,UAAU;QAClE,IAAIS,MAAM,GAAGH,UAAU,CAACI,SAAS,CAAGC,EAAU,IAAKA,EAAE,IAAIvC,KAAK,CAACmC,GAAI,CAAC;QACpED,UAAU,CAACM,MAAM,CAACH,MAAM,EAAE,CAAC,CAAC;MAC9B;IACF;EACF;EAEArF,wBAAwBA,CAAA;IACtB,MAAMyF,MAAM,GAAG;MACb/H,MAAM,EAAE,IAAI,CAACR,UAAU,CAACQ,MAAM;MAC9B6C,YAAY,EAAE,IAAI,CAACF,KAAK;MACxB8C,MAAM,EAAE,IAAI,CAACjG,UAAU,CAACiG;KACzB;IACDV,OAAO,CAACC,GAAG,CAAC+C,MAAM,CAAC;IACnB,IAAI,CAAChI,aAAa,CAACgI,MAAM,CAAC;EAC5B;EAEA;;;;;;EAMA7E,WAAWA,CAAC6D,QAAgB;IAC1B,MAAMiB,gBAAgB,GAAGjB,QAAQ,CAACkB,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,EAAE;IACnD,MAAMC,aAAa,GAAGH,gBAAiB,CAACC,KAAK,CAAC,GAAG,CAAC;IAElD,IAAIE,aAAa,CAACrF,MAAM,GAAG,CAAC,EAAE;MAC5B,MAAMsF,wBAAwB,GAAGD,aAAa,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;MACrE,OAAOF,wBAAwB;IACjC;IAEA,OAAOJ,gBAAgB;EACzB;EAEA;;;;;;EAMAO,QAAQA,CAACC,GAAQ;IACf,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE;MAC3C,OAAOA,GAAG;IACZ;IAEA,IAAIC,IAAS;IAEb,IAAID,GAAG,YAAYE,KAAK,EAAE;MACxBD,IAAI,GAAG,EAAE;MACT,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,GAAG,CAAC1F,MAAM,EAAE6F,CAAC,EAAE,EAAE;QACnCF,IAAI,CAACE,CAAC,CAAC,GAAG,IAAI,CAACJ,QAAQ,CAACC,GAAG,CAACG,CAAC,CAAC,CAAC;MACjC;IACF,CAAC,MAAM;MACLF,IAAI,GAAG,EAAE;MACT,KAAK,MAAMG,GAAG,IAAIJ,GAAG,EAAE;QACrB,IAAIA,GAAG,CAACK,cAAc,CAACD,GAAG,CAAC,EAAE;UAC3BH,IAAI,CAACG,GAAG,CAAC,GAAG,IAAI,CAACL,QAAQ,CAACC,GAAG,CAACI,GAAG,CAAC,CAAC;QACrC;MACF;IACF;IAEA,OAAOH,IAAI;EACb;EAEA7E,WAAWA,CAACkF,QAAa;IACvB,MAAMrJ,WAAW,GAAG,IAAI,CAAC8I,QAAQ,CAAC,IAAI,CAACpF,YAAY,CAAC;IAEpD,IAAI2F,QAAQ,CAACzB,OAAO,EAAE;MACpB,IAAIyB,QAAQ,CAACC,UAAU,EAAE;QACvB;QACAtJ,WAAW,CAACyH,UAAU,CAACd,IAAI,CAAC0C,QAAQ,CAACrB,GAAG,CAAC;MAC3C;MACA,IAAIqB,QAAQ,CAAC1B,OAAO,EAAE;QACpB;QACA3H,WAAW,CAACwH,MAAM,CAACb,IAAI,CAAC0C,QAAQ,CAACrB,GAAG,CAAC;MACvC;IACF,CAAC,MAAM;MACL,IAAIqB,QAAQ,CAACC,UAAU,EAAE;QACvB;QACAtJ,WAAW,CAACyH,UAAU,GAAGzH,WAAW,CAACyH,UAAU,CAACtB,MAAM,CAAEoD,QAAkB,IAAKA,QAAQ,KAAKF,QAAQ,CAACrB,GAAG,CAAC;MAC3G;MACA,IAAIqB,QAAQ,CAAC1B,OAAO,EAAE;QACpB;QACA3H,WAAW,CAACwH,MAAM,GAAGxH,WAAW,CAACwH,MAAM,CAACrB,MAAM,CAAEqD,KAAY,IAAKA,KAAK,KAAKH,QAAQ,CAACrB,GAAG,CAAC;MAC1F;IACF;IAEA,IAAI,CAACtE,YAAY,GAAG1D,WAAW;EACjC;EAEAqE,eAAeA,CAAA;IACb,MAAMrE,WAAW,GAAG,IAAI,CAAC8I,QAAQ,CAAC,IAAI,CAACpF,YAAY,CAAC;IACpD,MAAM+F,SAAS,GAAG;MAChB/B,MAAM,EAAE1H,WAAW,CAAC0H,MAAM;MAC1BF,MAAM,EAAE,IAAI,CAAC9D,YAAY,CAAC8D;KAC3B;IACD,MAAMkC,aAAa,GAAG;MACpBhC,MAAM,EAAE1H,WAAW,CAAC0H,MAAM;MAC1BD,UAAU,EAAE,IAAI,CAAC/D,YAAY,CAAC+D;KAC/B;IACD,IAAI,CAACkC,MAAM,CAAC,IAAI,CAACjG,YAAY,CAACgE,MAAO,EAAE,IAAI,CAAC7D,WAAW,CAAC;IACxD,IAAI,CAACkB,IAAI,CAAC6E,GAAG,CAAC,IAAI,CAAChF,cAAc,CAACiF,cAAc,CAACJ,SAAS,CAAC,CAACK,IAAI,CAC9D9K,SAAS,CAAE+K,GAAG,IAAI;MAChBzE,OAAO,CAACC,GAAG,CAACwE,GAAG,CAAC;MAChB,OAAO,IAAI,CAACnF,cAAc,CAACoF,kBAAkB,CAACN,aAAa,CAAC;IAC9D,CAAC,CAAC,CACH,CAACO,SAAS,CAAEF,GAAG,IAAI;MAClBzE,OAAO,CAACC,GAAG,CAACwE,GAAG,CAAC;MAChB,MAAMzB,MAAM,GAAG;QACb/H,MAAM,EAAE,IAAI,CAACR,UAAU,CAACQ,MAAM;QAC9B2J,MAAM,EAAE,IAAI,CAACxG;OACd;MACD,IAAI,CAACmB,YAAY,CAACsF,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;MACFhF,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC7B,YAAY,CAAC;MAC9B,IAAI,CAACpD,aAAa,CAACgI,MAAM,CAAC;IAC5B,CAAC,CAAC,CAAC;EACL;EAEAqB,MAAMA,CAACjC,MAAc,EAAE6C,OAAe;IACpC,IAAI,CAACxF,IAAI,CAAC6E,GAAG,CAAC,IAAI,CAAChF,cAAc,CAAC4F,qBAAqB,CAAC9C,MAAM,EAAE6C,OAAO,CAAC,CAACT,IAAI,CAAC7K,IAAI,CAAC,CAAC,CAAC,CAAC,CAACgL,SAAS,CAACF,GAAG,IAAG;MACrG,IAAI,CAACrG,YAAY,CAACzD,IAAI,GAAG,IAAI,CAAC4D,WAAW;IAC3C,CAAC,CAAC,CAAC;EACL;EAAC,QAAA4G,CAAA,G;qBAvTUnG,iCAAiC,EAAAlF,EAAA,CAAAsL,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAxL,EAAA,CAAAsL,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA1L,EAAA,CAAAsL,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA5L,EAAA,CAAAsL,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA9L,EAAA,CAAAsL,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAAhM,EAAA,CAAAsL,iBAAA,CAAAW,EAAA,CAAAC,YAAA,GAAAlM,EAAA,CAAAsL,iBAAA,CAAAC,EAAA,CAAAY,mBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAjClH,iCAAiC;IAAAmH,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,2CAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCnB9C3M,EAAA,CAAAE,cAAA,0BAKyC;QAArCF,EAAA,CAAAgB,UAAA,8BAAA6L,wFAAA;UAAA,OAAoBD,GAAA,CAAA1L,aAAA,EAAe;QAAA,EAAC;QAmFpClB,EAhFA,CAAAkD,UAAA,IAAA4J,yDAAA,2BAA0D,IAAAC,yDAAA,0BAoBmI,IAAAC,yDAAA,0BAUxH,IAAAC,yDAAA,2BAUgC,IAAAC,yDAAA,0BA4B3C,IAAAC,yDAAA,0BAYkB;QAoDhFnN,EAAA,CAAAe,YAAA,EAAmB;;;QAxIiEf,EAJlE,CAAA+B,UAAA,WAAA6K,GAAA,CAAAjM,UAAA,CAAAyM,WAAA,GAAAR,GAAA,CAAAjM,UAAA,CAAAyM,WAAA,GAAAR,GAAA,CAAAjM,UAAA,CAAAQ,MAAA,CAA6F,8CAGH,gFACzB,6BAA6B;QAI7FnB,EAAA,CAAAsB,SAAA,EAAyC;QAAzCtB,EAAA,CAAA+B,UAAA,SAAA6K,GAAA,CAAAjM,UAAA,CAAAQ,MAAA,cAAyC;QAoBzCnB,EAAA,CAAAsB,SAAA,EAA4K;QAA5KtB,EAAA,CAAA+B,UAAA,SAAA6K,GAAA,CAAAjM,UAAA,CAAAQ,MAAA,oBAAAyL,GAAA,CAAAjM,UAAA,CAAAQ,MAAA,sBAAAyL,GAAA,CAAAjM,UAAA,CAAAQ,MAAA,eAAAyL,GAAA,CAAAjM,UAAA,CAAAQ,MAAA,sBAA4K;QAU5KnB,EAAA,CAAAsB,SAAA,EAAoD;QAApDtB,EAAA,CAAA+B,UAAA,SAAA6K,GAAA,CAAAjM,UAAA,CAAAQ,MAAA,yBAAoD;QAUpDnB,EAAA,CAAAsB,SAAA,EAAoF;QAApFtB,EAAA,CAAA+B,UAAA,SAAA6K,GAAA,CAAAjM,UAAA,CAAAQ,MAAA,eAAAyL,GAAA,CAAAjM,UAAA,CAAAQ,MAAA,oBAAoF;QA4BpFnB,EAAA,CAAAsB,SAAA,EAAyC;QAAzCtB,EAAA,CAAA+B,UAAA,SAAA6K,GAAA,CAAAjM,UAAA,CAAAQ,MAAA,cAAyC;QAYzCnB,EAAA,CAAAsB,SAAA,EAA2D;QAA3DtB,EAAA,CAAA+B,UAAA,SAAA6K,GAAA,CAAAjM,UAAA,CAAAQ,MAAA,gCAA2D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}