{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, PLATFORM_ID, Injectable, Inject, Optional, EventEmitter, Component, Input, HostBinding, Output, NgModule, forwardRef, Directive, HostListener } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\nimport { of, BehaviorSubject, Subject } from 'rxjs';\nimport { filter } from 'rxjs/operators';\nimport { NG_VALUE_ACCESSOR, FormsModule } from '@angular/forms';\n\n/** @deprecated Use `LOADER_OPTIONS` instead. See `RecaptchaLoaderOptions.onBeforeLoad` */\nconst RECAPTCHA_LANGUAGE = new InjectionToken(\"recaptcha-language\");\n/** @deprecated Use `LOADER_OPTIONS` instead. See `RecaptchaLoaderOptions.onBeforeLoad` */\nconst RECAPTCHA_BASE_URL = new InjectionToken(\"recaptcha-base-url\");\n/** @deprecated Use `LOADER_OPTIONS` instead. See `RecaptchaLoaderOptions.onBeforeLoad` */\nconst RECAPTCHA_NONCE = new InjectionToken(\"recaptcha-nonce-tag\");\nconst RECAPTCHA_SETTINGS = new InjectionToken(\"recaptcha-settings\");\nconst RECAPTCHA_V3_SITE_KEY = new InjectionToken(\"recaptcha-v3-site-key\");\n/**\n * See the documentation for `RecaptchaLoaderOptions`.\n */\nconst RECAPTCHA_LOADER_OPTIONS = new InjectionToken(\"recaptcha-loader-options\");\nfunction loadScript(renderMode, onBeforeLoad, onLoaded, {\n  url,\n  lang,\n  nonce\n} = {}) {\n  window.ng2recaptchaloaded = () => {\n    onLoaded(grecaptcha);\n  };\n  const script = document.createElement(\"script\");\n  script.innerHTML = \"\";\n  const {\n    url: baseUrl,\n    nonce: onBeforeLoadNonce\n  } = onBeforeLoad(new URL(url || \"https://www.google.com/recaptcha/api.js\"));\n  baseUrl.searchParams.set(\"render\", renderMode === \"explicit\" ? renderMode : renderMode.key);\n  baseUrl.searchParams.set(\"onload\", \"ng2recaptchaloaded\");\n  baseUrl.searchParams.set(\"trustedtypes\", \"true\");\n  if (lang) {\n    baseUrl.searchParams.set(\"hl\", lang);\n  }\n  script.src = baseUrl.href;\n  const nonceValue = onBeforeLoadNonce || nonce;\n  if (nonceValue) {\n    script.setAttribute(\"nonce\", nonceValue);\n  }\n  script.async = true;\n  script.defer = true;\n  document.head.appendChild(script);\n}\nfunction newLoadScript({\n  v3SiteKey,\n  onBeforeLoad,\n  onLoaded\n}) {\n  const renderMode = v3SiteKey ? {\n    key: v3SiteKey\n  } : \"explicit\";\n  loader.loadScript(renderMode, onBeforeLoad, onLoaded);\n}\nconst loader = {\n  loadScript,\n  newLoadScript\n};\nfunction toNonNullObservable(subject) {\n  return subject.asObservable().pipe(filter(value => value !== null));\n}\nclass RecaptchaLoaderService {\n  /**\n   * @internal\n   * @nocollapse\n   */\n  static #_ = this.ready = null;\n  constructor(\n  // eslint-disable-next-line @typescript-eslint/ban-types\n  platformId,\n  // eslint-disable-next-line deprecation/deprecation\n  language,\n  // eslint-disable-next-line deprecation/deprecation\n  baseUrl,\n  // eslint-disable-next-line deprecation/deprecation\n  nonce, v3SiteKey, options) {\n    this.platformId = platformId;\n    this.language = language;\n    this.baseUrl = baseUrl;\n    this.nonce = nonce;\n    this.v3SiteKey = v3SiteKey;\n    this.options = options;\n    const subject = this.init();\n    this.ready = subject ? toNonNullObservable(subject) : of();\n  }\n  /** @internal */\n  init() {\n    if (RecaptchaLoaderService.ready) {\n      return RecaptchaLoaderService.ready;\n    }\n    if (!isPlatformBrowser(this.platformId)) {\n      return undefined;\n    }\n    const subject = new BehaviorSubject(null);\n    RecaptchaLoaderService.ready = subject;\n    loader.newLoadScript({\n      v3SiteKey: this.v3SiteKey,\n      onBeforeLoad: url => {\n        if (this.options?.onBeforeLoad) {\n          return this.options.onBeforeLoad(url);\n        }\n        const newUrl = new URL(this.baseUrl ?? url);\n        if (this.language) {\n          newUrl.searchParams.set(\"hl\", this.language);\n        }\n        return {\n          url: newUrl,\n          nonce: this.nonce\n        };\n      },\n      onLoaded: recaptcha => {\n        let value = recaptcha;\n        if (this.options?.onLoaded) {\n          value = this.options.onLoaded(recaptcha);\n        }\n        subject.next(value);\n      }\n    });\n    return subject;\n  }\n  static #_2 = this.ɵfac = function RecaptchaLoaderService_Factory(t) {\n    return new (t || RecaptchaLoaderService)(i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(RECAPTCHA_LANGUAGE, 8), i0.ɵɵinject(RECAPTCHA_BASE_URL, 8), i0.ɵɵinject(RECAPTCHA_NONCE, 8), i0.ɵɵinject(RECAPTCHA_V3_SITE_KEY, 8), i0.ɵɵinject(RECAPTCHA_LOADER_OPTIONS, 8));\n  };\n  static #_3 = this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: RecaptchaLoaderService,\n    factory: RecaptchaLoaderService.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RecaptchaLoaderService, [{\n    type: Injectable\n  }], () => [{\n    type: Object,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [RECAPTCHA_LANGUAGE]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [RECAPTCHA_BASE_URL]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [RECAPTCHA_NONCE]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [RECAPTCHA_V3_SITE_KEY]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [RECAPTCHA_LOADER_OPTIONS]\n    }]\n  }], null);\n})();\nlet nextId = 0;\nclass RecaptchaComponent {\n  constructor(elementRef, loader, zone, settings) {\n    this.elementRef = elementRef;\n    this.loader = loader;\n    this.zone = zone;\n    this.id = `ngrecaptcha-${nextId++}`;\n    this.errorMode = \"default\";\n    this.resolved = new EventEmitter();\n    /**\n     * @deprecated `(error) output will be removed in the next major version. Use (errored) instead\n     */\n    // eslint-disable-next-line @angular-eslint/no-output-native\n    this.error = new EventEmitter();\n    this.errored = new EventEmitter();\n    if (settings) {\n      this.siteKey = settings.siteKey;\n      this.theme = settings.theme;\n      this.type = settings.type;\n      this.size = settings.size;\n      this.badge = settings.badge;\n    }\n  }\n  ngAfterViewInit() {\n    this.subscription = this.loader.ready.subscribe(grecaptcha => {\n      if (grecaptcha != null && grecaptcha.render instanceof Function) {\n        this.grecaptcha = grecaptcha;\n        this.renderRecaptcha();\n      }\n    });\n  }\n  ngOnDestroy() {\n    // reset the captcha to ensure it does not leave anything behind\n    // after the component is no longer needed\n    this.grecaptchaReset();\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n  /**\n   * Executes the invisible recaptcha.\n   * Does nothing if component's size is not set to \"invisible\".\n   */\n  execute() {\n    if (this.size !== \"invisible\") {\n      return;\n    }\n    if (this.widget != null) {\n      void this.grecaptcha.execute(this.widget);\n    } else {\n      // delay execution of recaptcha until it actually renders\n      this.executeRequested = true;\n    }\n  }\n  reset() {\n    if (this.widget != null) {\n      if (this.grecaptcha.getResponse(this.widget)) {\n        // Only emit an event in case if something would actually change.\n        // That way we do not trigger \"touching\" of the control if someone does a \"reset\"\n        // on a non-resolved captcha.\n        this.resolved.emit(null);\n      }\n      this.grecaptchaReset();\n    }\n  }\n  /**\n   * ⚠️ Warning! Use this property at your own risk!\n   *\n   * While this member is `public`, it is not a part of the component's public API.\n   * The semantic versioning guarantees _will not be honored_! Thus, you might find that this property behavior changes in incompatible ways in minor or even patch releases.\n   * You are **strongly advised** against using this property.\n   * Instead, use more idiomatic ways to get reCAPTCHA value, such as `resolved` EventEmitter, or form-bound methods (ngModel, formControl, and the likes).å\n   */\n  get __unsafe_widgetValue() {\n    return this.widget != null ? this.grecaptcha.getResponse(this.widget) : null;\n  }\n  /** @internal */\n  expired() {\n    this.resolved.emit(null);\n  }\n  /** @internal */\n  onError(args) {\n    // eslint-disable-next-line deprecation/deprecation\n    this.error.emit(args);\n    this.errored.emit(args);\n  }\n  /** @internal */\n  captchaResponseCallback(response) {\n    this.resolved.emit(response);\n  }\n  /** @internal */\n  grecaptchaReset() {\n    if (this.widget != null) {\n      this.zone.runOutsideAngular(() => this.grecaptcha.reset(this.widget));\n    }\n  }\n  /** @internal */\n  renderRecaptcha() {\n    // This `any` can be removed after @types/grecaptcha get updated\n    const renderOptions = {\n      badge: this.badge,\n      callback: response => {\n        this.zone.run(() => this.captchaResponseCallback(response));\n      },\n      \"expired-callback\": () => {\n        this.zone.run(() => this.expired());\n      },\n      sitekey: this.siteKey,\n      size: this.size,\n      tabindex: this.tabIndex,\n      theme: this.theme,\n      type: this.type\n    };\n    if (this.errorMode === \"handled\") {\n      renderOptions[\"error-callback\"] = (...args) => {\n        this.zone.run(() => this.onError(args));\n      };\n    }\n    this.widget = this.grecaptcha.render(this.elementRef.nativeElement, renderOptions);\n    if (this.executeRequested === true) {\n      this.executeRequested = false;\n      this.execute();\n    }\n  }\n  static #_ = this.ɵfac = function RecaptchaComponent_Factory(t) {\n    return new (t || RecaptchaComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(RecaptchaLoaderService), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(RECAPTCHA_SETTINGS, 8));\n  };\n  static #_2 = this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: RecaptchaComponent,\n    selectors: [[\"re-captcha\"]],\n    hostVars: 1,\n    hostBindings: function RecaptchaComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"id\", ctx.id);\n      }\n    },\n    inputs: {\n      id: \"id\",\n      siteKey: \"siteKey\",\n      theme: \"theme\",\n      type: \"type\",\n      size: \"size\",\n      tabIndex: \"tabIndex\",\n      badge: \"badge\",\n      errorMode: \"errorMode\"\n    },\n    outputs: {\n      resolved: \"resolved\",\n      error: \"error\",\n      errored: \"errored\"\n    },\n    exportAs: [\"reCaptcha\"],\n    decls: 0,\n    vars: 0,\n    template: function RecaptchaComponent_Template(rf, ctx) {},\n    encapsulation: 2\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RecaptchaComponent, [{\n    type: Component,\n    args: [{\n      exportAs: \"reCaptcha\",\n      selector: \"re-captcha\",\n      template: ``\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: RecaptchaLoaderService\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [RECAPTCHA_SETTINGS]\n    }]\n  }], {\n    id: [{\n      type: Input\n    }, {\n      type: HostBinding,\n      args: [\"attr.id\"]\n    }],\n    siteKey: [{\n      type: Input\n    }],\n    theme: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    size: [{\n      type: Input\n    }],\n    tabIndex: [{\n      type: Input\n    }],\n    badge: [{\n      type: Input\n    }],\n    errorMode: [{\n      type: Input\n    }],\n    resolved: [{\n      type: Output\n    }],\n    error: [{\n      type: Output\n    }],\n    errored: [{\n      type: Output\n    }]\n  });\n})();\nclass RecaptchaCommonModule {\n  static #_ = this.ɵfac = function RecaptchaCommonModule_Factory(t) {\n    return new (t || RecaptchaCommonModule)();\n  };\n  static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: RecaptchaCommonModule\n  });\n  static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RecaptchaCommonModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [RecaptchaComponent],\n      exports: [RecaptchaComponent]\n    }]\n  }], null, null);\n})();\nclass RecaptchaModule {\n  static #_ = this.ɵfac = function RecaptchaModule_Factory(t) {\n    return new (t || RecaptchaModule)();\n  };\n  static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: RecaptchaModule\n  });\n  static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [RecaptchaLoaderService],\n    imports: [RecaptchaCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RecaptchaModule, [{\n    type: NgModule,\n    args: [{\n      exports: [RecaptchaComponent],\n      imports: [RecaptchaCommonModule],\n      providers: [RecaptchaLoaderService]\n    }]\n  }], null, null);\n})();\n\n/**\n * The main service for working with reCAPTCHA v3 APIs.\n *\n * Use the `execute` method for executing a single action, and\n * `onExecute` observable for listening to all actions at once.\n */\nclass ReCaptchaV3Service {\n  constructor(zone, recaptchaLoader, siteKey) {\n    this.recaptchaLoader = recaptchaLoader;\n    this.zone = zone;\n    this.siteKey = siteKey;\n    this.init();\n  }\n  get onExecute() {\n    if (!this.onExecuteSubject) {\n      this.onExecuteSubject = new Subject();\n      this.onExecuteObservable = this.onExecuteSubject.asObservable();\n    }\n    return this.onExecuteObservable;\n  }\n  get onExecuteError() {\n    if (!this.onExecuteErrorSubject) {\n      this.onExecuteErrorSubject = new Subject();\n      this.onExecuteErrorObservable = this.onExecuteErrorSubject.asObservable();\n    }\n    return this.onExecuteErrorObservable;\n  }\n  /**\n   * Executes the provided `action` with reCAPTCHA v3 API.\n   * Use the emitted token value for verification purposes on the backend.\n   *\n   * For more information about reCAPTCHA v3 actions and tokens refer to the official documentation at\n   * https://developers.google.com/recaptcha/docs/v3.\n   *\n   * @param {string} action the action to execute\n   * @returns {Observable<string>} an `Observable` that will emit the reCAPTCHA v3 string `token` value whenever ready.\n   * The returned `Observable` completes immediately after emitting a value.\n   */\n  execute(action) {\n    const subject = new Subject();\n    if (!this.grecaptcha) {\n      if (!this.actionBacklog) {\n        this.actionBacklog = [];\n      }\n      this.actionBacklog.push([action, subject]);\n    } else {\n      this.executeActionWithSubject(action, subject);\n    }\n    return subject.asObservable();\n  }\n  /** @internal */\n  executeActionWithSubject(action, subject) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    const onError = error => {\n      this.zone.run(() => {\n        subject.error(error);\n        if (this.onExecuteErrorSubject) {\n          // We don't know any better at this point, unfortunately, so have to resort to `any`\n          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n          this.onExecuteErrorSubject.next({\n            action,\n            error\n          });\n        }\n      });\n    };\n    this.zone.runOutsideAngular(() => {\n      try {\n        this.grecaptcha.execute(this.siteKey, {\n          action\n        }).then(token => {\n          this.zone.run(() => {\n            subject.next(token);\n            subject.complete();\n            if (this.onExecuteSubject) {\n              this.onExecuteSubject.next({\n                action,\n                token\n              });\n            }\n          });\n        }, onError);\n      } catch (e) {\n        onError(e);\n      }\n    });\n  }\n  /** @internal */\n  init() {\n    this.recaptchaLoader.ready.subscribe(value => {\n      this.grecaptcha = value;\n      if (this.actionBacklog && this.actionBacklog.length > 0) {\n        this.actionBacklog.forEach(([action, subject]) => this.executeActionWithSubject(action, subject));\n        this.actionBacklog = undefined;\n      }\n    });\n  }\n  static #_ = this.ɵfac = function ReCaptchaV3Service_Factory(t) {\n    return new (t || ReCaptchaV3Service)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(RecaptchaLoaderService), i0.ɵɵinject(RECAPTCHA_V3_SITE_KEY));\n  };\n  static #_2 = this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: ReCaptchaV3Service,\n    factory: ReCaptchaV3Service.ɵfac\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ReCaptchaV3Service, [{\n    type: Injectable\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: RecaptchaLoaderService\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [RECAPTCHA_V3_SITE_KEY]\n    }]\n  }], null);\n})();\nclass RecaptchaV3Module {\n  static #_ = this.ɵfac = function RecaptchaV3Module_Factory(t) {\n    return new (t || RecaptchaV3Module)();\n  };\n  static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: RecaptchaV3Module\n  });\n  static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [ReCaptchaV3Service, RecaptchaLoaderService]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RecaptchaV3Module, [{\n    type: NgModule,\n    args: [{\n      providers: [ReCaptchaV3Service, RecaptchaLoaderService]\n    }]\n  }], null, null);\n})();\nclass RecaptchaValueAccessorDirective {\n  constructor(host) {\n    this.host = host;\n    this.requiresControllerReset = false;\n  }\n  writeValue(value) {\n    if (!value) {\n      this.host.reset();\n    } else {\n      // In this case, it is most likely that a form controller has requested to write a specific value into the component.\n      // This isn't really a supported case - reCAPTCHA values are single-use, and, in a sense, readonly.\n      // What this means is that the form controller has recaptcha control state of X, while reCAPTCHA itself can't \"restore\"\n      // to that state. In order to make form controller aware of this discrepancy, and to fix the said misalignment,\n      // we'll be telling the controller to \"reset\" the value back to null.\n      if (this.host.__unsafe_widgetValue !== value && Boolean(this.host.__unsafe_widgetValue) === false) {\n        this.requiresControllerReset = true;\n      }\n    }\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n    if (this.requiresControllerReset) {\n      this.requiresControllerReset = false;\n      this.onChange(null);\n    }\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  onResolve($event) {\n    if (this.onChange) {\n      this.onChange($event);\n    }\n    if (this.onTouched) {\n      this.onTouched();\n    }\n  }\n  static #_ = this.ɵfac = function RecaptchaValueAccessorDirective_Factory(t) {\n    return new (t || RecaptchaValueAccessorDirective)(i0.ɵɵdirectiveInject(RecaptchaComponent));\n  };\n  static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: RecaptchaValueAccessorDirective,\n    selectors: [[\"re-captcha\", \"formControlName\", \"\"], [\"re-captcha\", \"formControl\", \"\"], [\"re-captcha\", \"ngModel\", \"\"]],\n    hostBindings: function RecaptchaValueAccessorDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"resolved\", function RecaptchaValueAccessorDirective_resolved_HostBindingHandler($event) {\n          return ctx.onResolve($event);\n        });\n      }\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      multi: true,\n      provide: NG_VALUE_ACCESSOR,\n      useExisting: forwardRef(() => RecaptchaValueAccessorDirective)\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RecaptchaValueAccessorDirective, [{\n    type: Directive,\n    args: [{\n      providers: [{\n        multi: true,\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => RecaptchaValueAccessorDirective)\n      }],\n      selector: \"re-captcha[formControlName],re-captcha[formControl],re-captcha[ngModel]\"\n    }]\n  }], () => [{\n    type: RecaptchaComponent\n  }], {\n    onResolve: [{\n      type: HostListener,\n      args: [\"resolved\", [\"$event\"]]\n    }]\n  });\n})();\nclass RecaptchaFormsModule {\n  static #_ = this.ɵfac = function RecaptchaFormsModule_Factory(t) {\n    return new (t || RecaptchaFormsModule)();\n  };\n  static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: RecaptchaFormsModule\n  });\n  static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [FormsModule, RecaptchaCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(RecaptchaFormsModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [RecaptchaValueAccessorDirective],\n      exports: [RecaptchaValueAccessorDirective],\n      imports: [FormsModule, RecaptchaCommonModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { RECAPTCHA_BASE_URL, RECAPTCHA_LANGUAGE, RECAPTCHA_LOADER_OPTIONS, RECAPTCHA_NONCE, RECAPTCHA_SETTINGS, RECAPTCHA_V3_SITE_KEY, ReCaptchaV3Service, RecaptchaComponent, RecaptchaFormsModule, RecaptchaLoaderService, RecaptchaModule, RecaptchaV3Module, RecaptchaValueAccessorDirective };", "map": {"version": 3, "names": ["i0", "InjectionToken", "PLATFORM_ID", "Injectable", "Inject", "Optional", "EventEmitter", "Component", "Input", "HostBinding", "Output", "NgModule", "forwardRef", "Directive", "HostListener", "isPlatformBrowser", "of", "BehaviorSubject", "Subject", "filter", "NG_VALUE_ACCESSOR", "FormsModule", "RECAPTCHA_LANGUAGE", "RECAPTCHA_BASE_URL", "RECAPTCHA_NONCE", "RECAPTCHA_SETTINGS", "RECAPTCHA_V3_SITE_KEY", "RECAPTCHA_LOADER_OPTIONS", "loadScript", "renderMode", "onBeforeLoad", "onLoaded", "url", "lang", "nonce", "window", "ng2recaptchaloaded", "gre<PERSON><PERSON>a", "script", "document", "createElement", "innerHTML", "baseUrl", "onBeforeLoadNonce", "URL", "searchParams", "set", "key", "src", "href", "nonceValue", "setAttribute", "async", "defer", "head", "append<PERSON><PERSON><PERSON>", "newLoadScript", "v3SiteKey", "loader", "toNonNullObservable", "subject", "asObservable", "pipe", "value", "RecaptchaLoaderService", "_", "ready", "constructor", "platformId", "language", "options", "init", "undefined", "newUrl", "recaptcha", "next", "_2", "ɵfac", "RecaptchaLoaderService_Factory", "t", "ɵɵinject", "_3", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "type", "Object", "decorators", "args", "nextId", "RecaptchaComponent", "elementRef", "zone", "settings", "id", "errorMode", "resolved", "error", "errored", "siteKey", "theme", "size", "badge", "ngAfterViewInit", "subscription", "subscribe", "render", "Function", "renderRecaptcha", "ngOnDestroy", "g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unsubscribe", "execute", "widget", "executeRequested", "reset", "getResponse", "emit", "__unsafe_widgetValue", "expired", "onError", "captchaResponseCallback", "response", "runOutsideAngular", "renderOptions", "callback", "run", "expired-callback", "sitekey", "tabindex", "tabIndex", "nativeElement", "RecaptchaComponent_Factory", "ɵɵdirectiveInject", "ElementRef", "NgZone", "ɵcmp", "ɵɵdefineComponent", "selectors", "hostVars", "hostBindings", "RecaptchaComponent_HostBindings", "rf", "ctx", "ɵɵattribute", "inputs", "outputs", "exportAs", "decls", "vars", "template", "RecaptchaComponent_Template", "encapsulation", "selector", "RecaptchaCommonModule", "RecaptchaCommonModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "declarations", "exports", "RecaptchaModule", "RecaptchaModule_Factory", "providers", "imports", "ReCaptchaV3Service", "recap<PERSON><PERSON><PERSON><PERSON><PERSON>", "onExecute", "onExecuteSubject", "onExecuteObservable", "onExecuteError", "onExecuteErrorSubject", "onExecuteErrorObservable", "action", "actionBacklog", "push", "executeActionWithSubject", "then", "complete", "e", "length", "for<PERSON>ach", "ReCaptchaV3Service_Factory", "RecaptchaV3Module", "RecaptchaV3Module_Factory", "RecaptchaValueAccessorDirective", "host", "requiresControllerReset", "writeValue", "Boolean", "registerOnChange", "fn", "onChange", "registerOnTouched", "onTouched", "onResolve", "$event", "RecaptchaValueAccessorDirective_Factory", "ɵdir", "ɵɵdefineDirective", "RecaptchaValueAccessorDirective_HostBindings", "ɵɵlistener", "RecaptchaValueAccessorDirective_resolved_HostBindingHandler", "features", "ɵɵProvidersFeature", "multi", "provide", "useExisting", "RecaptchaFormsModule", "RecaptchaFormsModule_Factory"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/ng-recaptcha/fesm2022/ng-recaptcha.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, PLATFORM_ID, Injectable, Inject, Optional, EventEmitter, Component, Input, HostBinding, Output, NgModule, forwardRef, Directive, HostListener } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\nimport { of, BehaviorSubject, Subject } from 'rxjs';\nimport { filter } from 'rxjs/operators';\nimport { NG_VALUE_ACCESSOR, FormsModule } from '@angular/forms';\n\n/** @deprecated Use `LOADER_OPTIONS` instead. See `RecaptchaLoaderOptions.onBeforeLoad` */\nconst RECAPTCHA_LANGUAGE = new InjectionToken(\"recaptcha-language\");\n/** @deprecated Use `LOADER_OPTIONS` instead. See `RecaptchaLoaderOptions.onBeforeLoad` */\nconst RECAPTCHA_BASE_URL = new InjectionToken(\"recaptcha-base-url\");\n/** @deprecated Use `LOADER_OPTIONS` instead. See `RecaptchaLoaderOptions.onBeforeLoad` */\nconst RECAPTCHA_NONCE = new InjectionToken(\"recaptcha-nonce-tag\");\nconst RECAPTCHA_SETTINGS = new InjectionToken(\"recaptcha-settings\");\nconst RECAPTCHA_V3_SITE_KEY = new InjectionToken(\"recaptcha-v3-site-key\");\n/**\n * See the documentation for `RecaptchaLoaderOptions`.\n */\nconst RECAPTCHA_LOADER_OPTIONS = new InjectionToken(\"recaptcha-loader-options\");\n\nfunction loadScript(renderMode, onBeforeLoad, onLoaded, { url, lang, nonce } = {}) {\n    window.ng2recaptchaloaded = () => {\n        onLoaded(grecaptcha);\n    };\n    const script = document.createElement(\"script\");\n    script.innerHTML = \"\";\n    const { url: baseUrl, nonce: onBeforeLoadNonce } = onBeforeLoad(new URL(url || \"https://www.google.com/recaptcha/api.js\"));\n    baseUrl.searchParams.set(\"render\", renderMode === \"explicit\" ? renderMode : renderMode.key);\n    baseUrl.searchParams.set(\"onload\", \"ng2recaptchaloaded\");\n    baseUrl.searchParams.set(\"trustedtypes\", \"true\");\n    if (lang) {\n        baseUrl.searchParams.set(\"hl\", lang);\n    }\n    script.src = baseUrl.href;\n    const nonceValue = onBeforeLoadNonce || nonce;\n    if (nonceValue) {\n        script.setAttribute(\"nonce\", nonceValue);\n    }\n    script.async = true;\n    script.defer = true;\n    document.head.appendChild(script);\n}\nfunction newLoadScript({ v3SiteKey, onBeforeLoad, onLoaded, }) {\n    const renderMode = v3SiteKey ? { key: v3SiteKey } : \"explicit\";\n    loader.loadScript(renderMode, onBeforeLoad, onLoaded);\n}\nconst loader = { loadScript, newLoadScript };\n\nfunction toNonNullObservable(subject) {\n    return subject.asObservable().pipe(filter((value) => value !== null));\n}\nclass RecaptchaLoaderService {\n    /**\n     * @internal\n     * @nocollapse\n     */\n    static { this.ready = null; }\n    constructor(\n    // eslint-disable-next-line @typescript-eslint/ban-types\n    platformId, \n    // eslint-disable-next-line deprecation/deprecation\n    language, \n    // eslint-disable-next-line deprecation/deprecation\n    baseUrl, \n    // eslint-disable-next-line deprecation/deprecation\n    nonce, v3SiteKey, options) {\n        this.platformId = platformId;\n        this.language = language;\n        this.baseUrl = baseUrl;\n        this.nonce = nonce;\n        this.v3SiteKey = v3SiteKey;\n        this.options = options;\n        const subject = this.init();\n        this.ready = subject ? toNonNullObservable(subject) : of();\n    }\n    /** @internal */\n    init() {\n        if (RecaptchaLoaderService.ready) {\n            return RecaptchaLoaderService.ready;\n        }\n        if (!isPlatformBrowser(this.platformId)) {\n            return undefined;\n        }\n        const subject = new BehaviorSubject(null);\n        RecaptchaLoaderService.ready = subject;\n        loader.newLoadScript({\n            v3SiteKey: this.v3SiteKey,\n            onBeforeLoad: (url) => {\n                if (this.options?.onBeforeLoad) {\n                    return this.options.onBeforeLoad(url);\n                }\n                const newUrl = new URL(this.baseUrl ?? url);\n                if (this.language) {\n                    newUrl.searchParams.set(\"hl\", this.language);\n                }\n                return {\n                    url: newUrl,\n                    nonce: this.nonce,\n                };\n            },\n            onLoaded: (recaptcha) => {\n                let value = recaptcha;\n                if (this.options?.onLoaded) {\n                    value = this.options.onLoaded(recaptcha);\n                }\n                subject.next(value);\n            },\n        });\n        return subject;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: RecaptchaLoaderService, deps: [{ token: PLATFORM_ID }, { token: RECAPTCHA_LANGUAGE, optional: true }, { token: RECAPTCHA_BASE_URL, optional: true }, { token: RECAPTCHA_NONCE, optional: true }, { token: RECAPTCHA_V3_SITE_KEY, optional: true }, { token: RECAPTCHA_LOADER_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: RecaptchaLoaderService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: RecaptchaLoaderService, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: Object, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [RECAPTCHA_LANGUAGE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [RECAPTCHA_BASE_URL]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [RECAPTCHA_NONCE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [RECAPTCHA_V3_SITE_KEY]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [RECAPTCHA_LOADER_OPTIONS]\n                }] }] });\n\nlet nextId = 0;\nclass RecaptchaComponent {\n    constructor(elementRef, loader, zone, settings) {\n        this.elementRef = elementRef;\n        this.loader = loader;\n        this.zone = zone;\n        this.id = `ngrecaptcha-${nextId++}`;\n        this.errorMode = \"default\";\n        this.resolved = new EventEmitter();\n        /**\n         * @deprecated `(error) output will be removed in the next major version. Use (errored) instead\n         */\n        // eslint-disable-next-line @angular-eslint/no-output-native\n        this.error = new EventEmitter();\n        this.errored = new EventEmitter();\n        if (settings) {\n            this.siteKey = settings.siteKey;\n            this.theme = settings.theme;\n            this.type = settings.type;\n            this.size = settings.size;\n            this.badge = settings.badge;\n        }\n    }\n    ngAfterViewInit() {\n        this.subscription = this.loader.ready.subscribe((grecaptcha) => {\n            if (grecaptcha != null && grecaptcha.render instanceof Function) {\n                this.grecaptcha = grecaptcha;\n                this.renderRecaptcha();\n            }\n        });\n    }\n    ngOnDestroy() {\n        // reset the captcha to ensure it does not leave anything behind\n        // after the component is no longer needed\n        this.grecaptchaReset();\n        if (this.subscription) {\n            this.subscription.unsubscribe();\n        }\n    }\n    /**\n     * Executes the invisible recaptcha.\n     * Does nothing if component's size is not set to \"invisible\".\n     */\n    execute() {\n        if (this.size !== \"invisible\") {\n            return;\n        }\n        if (this.widget != null) {\n            void this.grecaptcha.execute(this.widget);\n        }\n        else {\n            // delay execution of recaptcha until it actually renders\n            this.executeRequested = true;\n        }\n    }\n    reset() {\n        if (this.widget != null) {\n            if (this.grecaptcha.getResponse(this.widget)) {\n                // Only emit an event in case if something would actually change.\n                // That way we do not trigger \"touching\" of the control if someone does a \"reset\"\n                // on a non-resolved captcha.\n                this.resolved.emit(null);\n            }\n            this.grecaptchaReset();\n        }\n    }\n    /**\n     * ⚠️ Warning! Use this property at your own risk!\n     *\n     * While this member is `public`, it is not a part of the component's public API.\n     * The semantic versioning guarantees _will not be honored_! Thus, you might find that this property behavior changes in incompatible ways in minor or even patch releases.\n     * You are **strongly advised** against using this property.\n     * Instead, use more idiomatic ways to get reCAPTCHA value, such as `resolved` EventEmitter, or form-bound methods (ngModel, formControl, and the likes).å\n     */\n    get __unsafe_widgetValue() {\n        return this.widget != null ? this.grecaptcha.getResponse(this.widget) : null;\n    }\n    /** @internal */\n    expired() {\n        this.resolved.emit(null);\n    }\n    /** @internal */\n    onError(args) {\n        // eslint-disable-next-line deprecation/deprecation\n        this.error.emit(args);\n        this.errored.emit(args);\n    }\n    /** @internal */\n    captchaResponseCallback(response) {\n        this.resolved.emit(response);\n    }\n    /** @internal */\n    grecaptchaReset() {\n        if (this.widget != null) {\n            this.zone.runOutsideAngular(() => this.grecaptcha.reset(this.widget));\n        }\n    }\n    /** @internal */\n    renderRecaptcha() {\n        // This `any` can be removed after @types/grecaptcha get updated\n        const renderOptions = {\n            badge: this.badge,\n            callback: (response) => {\n                this.zone.run(() => this.captchaResponseCallback(response));\n            },\n            \"expired-callback\": () => {\n                this.zone.run(() => this.expired());\n            },\n            sitekey: this.siteKey,\n            size: this.size,\n            tabindex: this.tabIndex,\n            theme: this.theme,\n            type: this.type,\n        };\n        if (this.errorMode === \"handled\") {\n            renderOptions[\"error-callback\"] = (...args) => {\n                this.zone.run(() => this.onError(args));\n            };\n        }\n        this.widget = this.grecaptcha.render(this.elementRef.nativeElement, renderOptions);\n        if (this.executeRequested === true) {\n            this.executeRequested = false;\n            this.execute();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: RecaptchaComponent, deps: [{ token: i0.ElementRef }, { token: RecaptchaLoaderService }, { token: i0.NgZone }, { token: RECAPTCHA_SETTINGS, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.0.1\", type: RecaptchaComponent, selector: \"re-captcha\", inputs: { id: \"id\", siteKey: \"siteKey\", theme: \"theme\", type: \"type\", size: \"size\", tabIndex: \"tabIndex\", badge: \"badge\", errorMode: \"errorMode\" }, outputs: { resolved: \"resolved\", error: \"error\", errored: \"errored\" }, host: { properties: { \"attr.id\": \"this.id\" } }, exportAs: [\"reCaptcha\"], ngImport: i0, template: ``, isInline: true }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: RecaptchaComponent, decorators: [{\n            type: Component,\n            args: [{\n                    exportAs: \"reCaptcha\",\n                    selector: \"re-captcha\",\n                    template: ``,\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: RecaptchaLoaderService }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [RECAPTCHA_SETTINGS]\n                }] }], propDecorators: { id: [{\n                type: Input\n            }, {\n                type: HostBinding,\n                args: [\"attr.id\"]\n            }], siteKey: [{\n                type: Input\n            }], theme: [{\n                type: Input\n            }], type: [{\n                type: Input\n            }], size: [{\n                type: Input\n            }], tabIndex: [{\n                type: Input\n            }], badge: [{\n                type: Input\n            }], errorMode: [{\n                type: Input\n            }], resolved: [{\n                type: Output\n            }], error: [{\n                type: Output\n            }], errored: [{\n                type: Output\n            }] } });\n\nclass RecaptchaCommonModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: RecaptchaCommonModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.1\", ngImport: i0, type: RecaptchaCommonModule, declarations: [RecaptchaComponent], exports: [RecaptchaComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: RecaptchaCommonModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: RecaptchaCommonModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [RecaptchaComponent],\n                    exports: [RecaptchaComponent],\n                }]\n        }] });\n\nclass RecaptchaModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: RecaptchaModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.1\", ngImport: i0, type: RecaptchaModule, imports: [RecaptchaCommonModule], exports: [RecaptchaComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: RecaptchaModule, providers: [RecaptchaLoaderService], imports: [RecaptchaCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: RecaptchaModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [RecaptchaComponent],\n                    imports: [RecaptchaCommonModule],\n                    providers: [RecaptchaLoaderService],\n                }]\n        }] });\n\n/**\n * The main service for working with reCAPTCHA v3 APIs.\n *\n * Use the `execute` method for executing a single action, and\n * `onExecute` observable for listening to all actions at once.\n */\nclass ReCaptchaV3Service {\n    constructor(zone, recaptchaLoader, siteKey) {\n        this.recaptchaLoader = recaptchaLoader;\n        this.zone = zone;\n        this.siteKey = siteKey;\n        this.init();\n    }\n    get onExecute() {\n        if (!this.onExecuteSubject) {\n            this.onExecuteSubject = new Subject();\n            this.onExecuteObservable = this.onExecuteSubject.asObservable();\n        }\n        return this.onExecuteObservable;\n    }\n    get onExecuteError() {\n        if (!this.onExecuteErrorSubject) {\n            this.onExecuteErrorSubject = new Subject();\n            this.onExecuteErrorObservable = this.onExecuteErrorSubject.asObservable();\n        }\n        return this.onExecuteErrorObservable;\n    }\n    /**\n     * Executes the provided `action` with reCAPTCHA v3 API.\n     * Use the emitted token value for verification purposes on the backend.\n     *\n     * For more information about reCAPTCHA v3 actions and tokens refer to the official documentation at\n     * https://developers.google.com/recaptcha/docs/v3.\n     *\n     * @param {string} action the action to execute\n     * @returns {Observable<string>} an `Observable` that will emit the reCAPTCHA v3 string `token` value whenever ready.\n     * The returned `Observable` completes immediately after emitting a value.\n     */\n    execute(action) {\n        const subject = new Subject();\n        if (!this.grecaptcha) {\n            if (!this.actionBacklog) {\n                this.actionBacklog = [];\n            }\n            this.actionBacklog.push([action, subject]);\n        }\n        else {\n            this.executeActionWithSubject(action, subject);\n        }\n        return subject.asObservable();\n    }\n    /** @internal */\n    executeActionWithSubject(action, subject) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        const onError = (error) => {\n            this.zone.run(() => {\n                subject.error(error);\n                if (this.onExecuteErrorSubject) {\n                    // We don't know any better at this point, unfortunately, so have to resort to `any`\n                    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n                    this.onExecuteErrorSubject.next({ action, error });\n                }\n            });\n        };\n        this.zone.runOutsideAngular(() => {\n            try {\n                this.grecaptcha.execute(this.siteKey, { action }).then((token) => {\n                    this.zone.run(() => {\n                        subject.next(token);\n                        subject.complete();\n                        if (this.onExecuteSubject) {\n                            this.onExecuteSubject.next({ action, token });\n                        }\n                    });\n                }, onError);\n            }\n            catch (e) {\n                onError(e);\n            }\n        });\n    }\n    /** @internal */\n    init() {\n        this.recaptchaLoader.ready.subscribe((value) => {\n            this.grecaptcha = value;\n            if (this.actionBacklog && this.actionBacklog.length > 0) {\n                this.actionBacklog.forEach(([action, subject]) => this.executeActionWithSubject(action, subject));\n                this.actionBacklog = undefined;\n            }\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: ReCaptchaV3Service, deps: [{ token: i0.NgZone }, { token: RecaptchaLoaderService }, { token: RECAPTCHA_V3_SITE_KEY }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: ReCaptchaV3Service }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: ReCaptchaV3Service, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: i0.NgZone }, { type: RecaptchaLoaderService }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [RECAPTCHA_V3_SITE_KEY]\n                }] }] });\n\nclass RecaptchaV3Module {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: RecaptchaV3Module, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.1\", ngImport: i0, type: RecaptchaV3Module }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: RecaptchaV3Module, providers: [ReCaptchaV3Service, RecaptchaLoaderService] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: RecaptchaV3Module, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [ReCaptchaV3Service, RecaptchaLoaderService],\n                }]\n        }] });\n\nclass RecaptchaValueAccessorDirective {\n    constructor(host) {\n        this.host = host;\n        this.requiresControllerReset = false;\n    }\n    writeValue(value) {\n        if (!value) {\n            this.host.reset();\n        }\n        else {\n            // In this case, it is most likely that a form controller has requested to write a specific value into the component.\n            // This isn't really a supported case - reCAPTCHA values are single-use, and, in a sense, readonly.\n            // What this means is that the form controller has recaptcha control state of X, while reCAPTCHA itself can't \"restore\"\n            // to that state. In order to make form controller aware of this discrepancy, and to fix the said misalignment,\n            // we'll be telling the controller to \"reset\" the value back to null.\n            if (this.host.__unsafe_widgetValue !== value && Boolean(this.host.__unsafe_widgetValue) === false) {\n                this.requiresControllerReset = true;\n            }\n        }\n    }\n    registerOnChange(fn) {\n        this.onChange = fn;\n        if (this.requiresControllerReset) {\n            this.requiresControllerReset = false;\n            this.onChange(null);\n        }\n    }\n    registerOnTouched(fn) {\n        this.onTouched = fn;\n    }\n    onResolve($event) {\n        if (this.onChange) {\n            this.onChange($event);\n        }\n        if (this.onTouched) {\n            this.onTouched();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: RecaptchaValueAccessorDirective, deps: [{ token: RecaptchaComponent }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.0.1\", type: RecaptchaValueAccessorDirective, selector: \"re-captcha[formControlName],re-captcha[formControl],re-captcha[ngModel]\", host: { listeners: { \"resolved\": \"onResolve($event)\" } }, providers: [\n            {\n                multi: true,\n                provide: NG_VALUE_ACCESSOR,\n                useExisting: forwardRef(() => RecaptchaValueAccessorDirective),\n            },\n        ], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: RecaptchaValueAccessorDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    providers: [\n                        {\n                            multi: true,\n                            provide: NG_VALUE_ACCESSOR,\n                            useExisting: forwardRef(() => RecaptchaValueAccessorDirective),\n                        },\n                    ],\n                    selector: \"re-captcha[formControlName],re-captcha[formControl],re-captcha[ngModel]\",\n                }]\n        }], ctorParameters: () => [{ type: RecaptchaComponent }], propDecorators: { onResolve: [{\n                type: HostListener,\n                args: [\"resolved\", [\"$event\"]]\n            }] } });\n\nclass RecaptchaFormsModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: RecaptchaFormsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.0.1\", ngImport: i0, type: RecaptchaFormsModule, declarations: [RecaptchaValueAccessorDirective], imports: [FormsModule, RecaptchaCommonModule], exports: [RecaptchaValueAccessorDirective] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: RecaptchaFormsModule, imports: [FormsModule, RecaptchaCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.1\", ngImport: i0, type: RecaptchaFormsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    declarations: [RecaptchaValueAccessorDirective],\n                    exports: [RecaptchaValueAccessorDirective],\n                    imports: [FormsModule, RecaptchaCommonModule],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { RECAPTCHA_BASE_URL, RECAPTCHA_LANGUAGE, RECAPTCHA_LOADER_OPTIONS, RECAPTCHA_NONCE, RECAPTCHA_SETTINGS, RECAPTCHA_V3_SITE_KEY, ReCaptchaV3Service, RecaptchaComponent, RecaptchaFormsModule, RecaptchaLoaderService, RecaptchaModule, RecaptchaV3Module, RecaptchaValueAccessorDirective };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,WAAW,EAAEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,SAAS,EAAEC,KAAK,EAAEC,WAAW,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,YAAY,QAAQ,eAAe;AAC7L,SAASC,iBAAiB,QAAQ,iBAAiB;AACnD,SAASC,EAAE,EAAEC,eAAe,EAAEC,OAAO,QAAQ,MAAM;AACnD,SAASC,MAAM,QAAQ,gBAAgB;AACvC,SAASC,iBAAiB,EAAEC,WAAW,QAAQ,gBAAgB;;AAE/D;AACA,MAAMC,kBAAkB,GAAG,IAAIrB,cAAc,CAAC,oBAAoB,CAAC;AACnE;AACA,MAAMsB,kBAAkB,GAAG,IAAItB,cAAc,CAAC,oBAAoB,CAAC;AACnE;AACA,MAAMuB,eAAe,GAAG,IAAIvB,cAAc,CAAC,qBAAqB,CAAC;AACjE,MAAMwB,kBAAkB,GAAG,IAAIxB,cAAc,CAAC,oBAAoB,CAAC;AACnE,MAAMyB,qBAAqB,GAAG,IAAIzB,cAAc,CAAC,uBAAuB,CAAC;AACzE;AACA;AACA;AACA,MAAM0B,wBAAwB,GAAG,IAAI1B,cAAc,CAAC,0BAA0B,CAAC;AAE/E,SAAS2B,UAAUA,CAACC,UAAU,EAAEC,YAAY,EAAEC,QAAQ,EAAE;EAAEC,GAAG;EAAEC,IAAI;EAAEC;AAAM,CAAC,GAAG,CAAC,CAAC,EAAE;EAC/EC,MAAM,CAACC,kBAAkB,GAAG,MAAM;IAC9BL,QAAQ,CAACM,UAAU,CAAC;EACxB,CAAC;EACD,MAAMC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/CF,MAAM,CAACG,SAAS,GAAG,EAAE;EACrB,MAAM;IAAET,GAAG,EAAEU,OAAO;IAAER,KAAK,EAAES;EAAkB,CAAC,GAAGb,YAAY,CAAC,IAAIc,GAAG,CAACZ,GAAG,IAAI,yCAAyC,CAAC,CAAC;EAC1HU,OAAO,CAACG,YAAY,CAACC,GAAG,CAAC,QAAQ,EAAEjB,UAAU,KAAK,UAAU,GAAGA,UAAU,GAAGA,UAAU,CAACkB,GAAG,CAAC;EAC3FL,OAAO,CAACG,YAAY,CAACC,GAAG,CAAC,QAAQ,EAAE,oBAAoB,CAAC;EACxDJ,OAAO,CAACG,YAAY,CAACC,GAAG,CAAC,cAAc,EAAE,MAAM,CAAC;EAChD,IAAIb,IAAI,EAAE;IACNS,OAAO,CAACG,YAAY,CAACC,GAAG,CAAC,IAAI,EAAEb,IAAI,CAAC;EACxC;EACAK,MAAM,CAACU,GAAG,GAAGN,OAAO,CAACO,IAAI;EACzB,MAAMC,UAAU,GAAGP,iBAAiB,IAAIT,KAAK;EAC7C,IAAIgB,UAAU,EAAE;IACZZ,MAAM,CAACa,YAAY,CAAC,OAAO,EAAED,UAAU,CAAC;EAC5C;EACAZ,MAAM,CAACc,KAAK,GAAG,IAAI;EACnBd,MAAM,CAACe,KAAK,GAAG,IAAI;EACnBd,QAAQ,CAACe,IAAI,CAACC,WAAW,CAACjB,MAAM,CAAC;AACrC;AACA,SAASkB,aAAaA,CAAC;EAAEC,SAAS;EAAE3B,YAAY;EAAEC;AAAU,CAAC,EAAE;EAC3D,MAAMF,UAAU,GAAG4B,SAAS,GAAG;IAAEV,GAAG,EAAEU;EAAU,CAAC,GAAG,UAAU;EAC9DC,MAAM,CAAC9B,UAAU,CAACC,UAAU,EAAEC,YAAY,EAAEC,QAAQ,CAAC;AACzD;AACA,MAAM2B,MAAM,GAAG;EAAE9B,UAAU;EAAE4B;AAAc,CAAC;AAE5C,SAASG,mBAAmBA,CAACC,OAAO,EAAE;EAClC,OAAOA,OAAO,CAACC,YAAY,CAAC,CAAC,CAACC,IAAI,CAAC3C,MAAM,CAAE4C,KAAK,IAAKA,KAAK,KAAK,IAAI,CAAC,CAAC;AACzE;AACA,MAAMC,sBAAsB,CAAC;EACzB;AACJ;AACA;AACA;EAHI,QAAAC,CAAA,GAIS,IAAI,CAACC,KAAK,GAAG,IAAI;EAC1BC,WAAWA;EACX;EACAC,UAAU;EACV;EACAC,QAAQ;EACR;EACA3B,OAAO;EACP;EACAR,KAAK,EAAEuB,SAAS,EAAEa,OAAO,EAAE;IACvB,IAAI,CAACF,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC3B,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACR,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACuB,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACa,OAAO,GAAGA,OAAO;IACtB,MAAMV,OAAO,GAAG,IAAI,CAACW,IAAI,CAAC,CAAC;IAC3B,IAAI,CAACL,KAAK,GAAGN,OAAO,GAAGD,mBAAmB,CAACC,OAAO,CAAC,GAAG5C,EAAE,CAAC,CAAC;EAC9D;EACA;EACAuD,IAAIA,CAAA,EAAG;IACH,IAAIP,sBAAsB,CAACE,KAAK,EAAE;MAC9B,OAAOF,sBAAsB,CAACE,KAAK;IACvC;IACA,IAAI,CAACnD,iBAAiB,CAAC,IAAI,CAACqD,UAAU,CAAC,EAAE;MACrC,OAAOI,SAAS;IACpB;IACA,MAAMZ,OAAO,GAAG,IAAI3C,eAAe,CAAC,IAAI,CAAC;IACzC+C,sBAAsB,CAACE,KAAK,GAAGN,OAAO;IACtCF,MAAM,CAACF,aAAa,CAAC;MACjBC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzB3B,YAAY,EAAGE,GAAG,IAAK;QACnB,IAAI,IAAI,CAACsC,OAAO,EAAExC,YAAY,EAAE;UAC5B,OAAO,IAAI,CAACwC,OAAO,CAACxC,YAAY,CAACE,GAAG,CAAC;QACzC;QACA,MAAMyC,MAAM,GAAG,IAAI7B,GAAG,CAAC,IAAI,CAACF,OAAO,IAAIV,GAAG,CAAC;QAC3C,IAAI,IAAI,CAACqC,QAAQ,EAAE;UACfI,MAAM,CAAC5B,YAAY,CAACC,GAAG,CAAC,IAAI,EAAE,IAAI,CAACuB,QAAQ,CAAC;QAChD;QACA,OAAO;UACHrC,GAAG,EAAEyC,MAAM;UACXvC,KAAK,EAAE,IAAI,CAACA;QAChB,CAAC;MACL,CAAC;MACDH,QAAQ,EAAG2C,SAAS,IAAK;QACrB,IAAIX,KAAK,GAAGW,SAAS;QACrB,IAAI,IAAI,CAACJ,OAAO,EAAEvC,QAAQ,EAAE;UACxBgC,KAAK,GAAG,IAAI,CAACO,OAAO,CAACvC,QAAQ,CAAC2C,SAAS,CAAC;QAC5C;QACAd,OAAO,CAACe,IAAI,CAACZ,KAAK,CAAC;MACvB;IACJ,CAAC,CAAC;IACF,OAAOH,OAAO;EAClB;EAAC,QAAAgB,EAAA,GACQ,IAAI,CAACC,IAAI,YAAAC,+BAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFf,sBAAsB,EAAhChE,EAAE,CAAAgF,QAAA,CAAgD9E,WAAW,GAA7DF,EAAE,CAAAgF,QAAA,CAAwE1D,kBAAkB,MAA5FtB,EAAE,CAAAgF,QAAA,CAAuHzD,kBAAkB,MAA3IvB,EAAE,CAAAgF,QAAA,CAAsKxD,eAAe,MAAvLxB,EAAE,CAAAgF,QAAA,CAAkNtD,qBAAqB,MAAzO1B,EAAE,CAAAgF,QAAA,CAAoQrD,wBAAwB;EAAA,CAA6D;EAAA,QAAAsD,EAAA,GAClb,IAAI,CAACC,KAAK,kBAD6ElF,EAAE,CAAAmF,kBAAA;IAAAC,KAAA,EACYpB,sBAAsB;IAAAqB,OAAA,EAAtBrB,sBAAsB,CAAAa;EAAA,EAAG;AAC3I;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAHoGtF,EAAE,CAAAuF,iBAAA,CAGXvB,sBAAsB,EAAc,CAAC;IACpHwB,IAAI,EAAErF;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEqF,IAAI,EAAEC,MAAM;IAAEC,UAAU,EAAE,CAAC;MAC5CF,IAAI,EAAEpF,MAAM;MACZuF,IAAI,EAAE,CAACzF,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAEsF,IAAI,EAAEhB,SAAS;IAAEkB,UAAU,EAAE,CAAC;MAClCF,IAAI,EAAEnF;IACV,CAAC,EAAE;MACCmF,IAAI,EAAEpF,MAAM;MACZuF,IAAI,EAAE,CAACrE,kBAAkB;IAC7B,CAAC;EAAE,CAAC,EAAE;IAAEkE,IAAI,EAAEhB,SAAS;IAAEkB,UAAU,EAAE,CAAC;MAClCF,IAAI,EAAEnF;IACV,CAAC,EAAE;MACCmF,IAAI,EAAEpF,MAAM;MACZuF,IAAI,EAAE,CAACpE,kBAAkB;IAC7B,CAAC;EAAE,CAAC,EAAE;IAAEiE,IAAI,EAAEhB,SAAS;IAAEkB,UAAU,EAAE,CAAC;MAClCF,IAAI,EAAEnF;IACV,CAAC,EAAE;MACCmF,IAAI,EAAEpF,MAAM;MACZuF,IAAI,EAAE,CAACnE,eAAe;IAC1B,CAAC;EAAE,CAAC,EAAE;IAAEgE,IAAI,EAAEhB,SAAS;IAAEkB,UAAU,EAAE,CAAC;MAClCF,IAAI,EAAEnF;IACV,CAAC,EAAE;MACCmF,IAAI,EAAEpF,MAAM;MACZuF,IAAI,EAAE,CAACjE,qBAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAE8D,IAAI,EAAEhB,SAAS;IAAEkB,UAAU,EAAE,CAAC;MAClCF,IAAI,EAAEnF;IACV,CAAC,EAAE;MACCmF,IAAI,EAAEpF,MAAM;MACZuF,IAAI,EAAE,CAAChE,wBAAwB;IACnC,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,IAAIiE,MAAM,GAAG,CAAC;AACd,MAAMC,kBAAkB,CAAC;EACrB1B,WAAWA,CAAC2B,UAAU,EAAEpC,MAAM,EAAEqC,IAAI,EAAEC,QAAQ,EAAE;IAC5C,IAAI,CAACF,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACpC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACqC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACE,EAAE,GAAI,eAAcL,MAAM,EAAG,EAAC;IACnC,IAAI,CAACM,SAAS,GAAG,SAAS;IAC1B,IAAI,CAACC,QAAQ,GAAG,IAAI7F,YAAY,CAAC,CAAC;IAClC;AACR;AACA;IACQ;IACA,IAAI,CAAC8F,KAAK,GAAG,IAAI9F,YAAY,CAAC,CAAC;IAC/B,IAAI,CAAC+F,OAAO,GAAG,IAAI/F,YAAY,CAAC,CAAC;IACjC,IAAI0F,QAAQ,EAAE;MACV,IAAI,CAACM,OAAO,GAAGN,QAAQ,CAACM,OAAO;MAC/B,IAAI,CAACC,KAAK,GAAGP,QAAQ,CAACO,KAAK;MAC3B,IAAI,CAACf,IAAI,GAAGQ,QAAQ,CAACR,IAAI;MACzB,IAAI,CAACgB,IAAI,GAAGR,QAAQ,CAACQ,IAAI;MACzB,IAAI,CAACC,KAAK,GAAGT,QAAQ,CAACS,KAAK;IAC/B;EACJ;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACC,YAAY,GAAG,IAAI,CAACjD,MAAM,CAACQ,KAAK,CAAC0C,SAAS,CAAEvE,UAAU,IAAK;MAC5D,IAAIA,UAAU,IAAI,IAAI,IAAIA,UAAU,CAACwE,MAAM,YAAYC,QAAQ,EAAE;QAC7D,IAAI,CAACzE,UAAU,GAAGA,UAAU;QAC5B,IAAI,CAAC0E,eAAe,CAAC,CAAC;MAC1B;IACJ,CAAC,CAAC;EACN;EACAC,WAAWA,CAAA,EAAG;IACV;IACA;IACA,IAAI,CAACC,eAAe,CAAC,CAAC;IACtB,IAAI,IAAI,CAACN,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAACO,WAAW,CAAC,CAAC;IACnC;EACJ;EACA;AACJ;AACA;AACA;EACIC,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACX,IAAI,KAAK,WAAW,EAAE;MAC3B;IACJ;IACA,IAAI,IAAI,CAACY,MAAM,IAAI,IAAI,EAAE;MACrB,KAAK,IAAI,CAAC/E,UAAU,CAAC8E,OAAO,CAAC,IAAI,CAACC,MAAM,CAAC;IAC7C,CAAC,MACI;MACD;MACA,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAChC;EACJ;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACF,MAAM,IAAI,IAAI,EAAE;MACrB,IAAI,IAAI,CAAC/E,UAAU,CAACkF,WAAW,CAAC,IAAI,CAACH,MAAM,CAAC,EAAE;QAC1C;QACA;QACA;QACA,IAAI,CAACjB,QAAQ,CAACqB,IAAI,CAAC,IAAI,CAAC;MAC5B;MACA,IAAI,CAACP,eAAe,CAAC,CAAC;IAC1B;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIQ,oBAAoBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACL,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC/E,UAAU,CAACkF,WAAW,CAAC,IAAI,CAACH,MAAM,CAAC,GAAG,IAAI;EAChF;EACA;EACAM,OAAOA,CAAA,EAAG;IACN,IAAI,CAACvB,QAAQ,CAACqB,IAAI,CAAC,IAAI,CAAC;EAC5B;EACA;EACAG,OAAOA,CAAChC,IAAI,EAAE;IACV;IACA,IAAI,CAACS,KAAK,CAACoB,IAAI,CAAC7B,IAAI,CAAC;IACrB,IAAI,CAACU,OAAO,CAACmB,IAAI,CAAC7B,IAAI,CAAC;EAC3B;EACA;EACAiC,uBAAuBA,CAACC,QAAQ,EAAE;IAC9B,IAAI,CAAC1B,QAAQ,CAACqB,IAAI,CAACK,QAAQ,CAAC;EAChC;EACA;EACAZ,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACG,MAAM,IAAI,IAAI,EAAE;MACrB,IAAI,CAACrB,IAAI,CAAC+B,iBAAiB,CAAC,MAAM,IAAI,CAACzF,UAAU,CAACiF,KAAK,CAAC,IAAI,CAACF,MAAM,CAAC,CAAC;IACzE;EACJ;EACA;EACAL,eAAeA,CAAA,EAAG;IACd;IACA,MAAMgB,aAAa,GAAG;MAClBtB,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBuB,QAAQ,EAAGH,QAAQ,IAAK;QACpB,IAAI,CAAC9B,IAAI,CAACkC,GAAG,CAAC,MAAM,IAAI,CAACL,uBAAuB,CAACC,QAAQ,CAAC,CAAC;MAC/D,CAAC;MACD,kBAAkB,EAAEK,CAAA,KAAM;QACtB,IAAI,CAACnC,IAAI,CAACkC,GAAG,CAAC,MAAM,IAAI,CAACP,OAAO,CAAC,CAAC,CAAC;MACvC,CAAC;MACDS,OAAO,EAAE,IAAI,CAAC7B,OAAO;MACrBE,IAAI,EAAE,IAAI,CAACA,IAAI;MACf4B,QAAQ,EAAE,IAAI,CAACC,QAAQ;MACvB9B,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBf,IAAI,EAAE,IAAI,CAACA;IACf,CAAC;IACD,IAAI,IAAI,CAACU,SAAS,KAAK,SAAS,EAAE;MAC9B6B,aAAa,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAGpC,IAAI,KAAK;QAC3C,IAAI,CAACI,IAAI,CAACkC,GAAG,CAAC,MAAM,IAAI,CAACN,OAAO,CAAChC,IAAI,CAAC,CAAC;MAC3C,CAAC;IACL;IACA,IAAI,CAACyB,MAAM,GAAG,IAAI,CAAC/E,UAAU,CAACwE,MAAM,CAAC,IAAI,CAACf,UAAU,CAACwC,aAAa,EAAEP,aAAa,CAAC;IAClF,IAAI,IAAI,CAACV,gBAAgB,KAAK,IAAI,EAAE;MAChC,IAAI,CAACA,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACF,OAAO,CAAC,CAAC;IAClB;EACJ;EAAC,QAAAlD,CAAA,GACQ,IAAI,CAACY,IAAI,YAAA0D,2BAAAxD,CAAA;IAAA,YAAAA,CAAA,IAAwFc,kBAAkB,EAhK5B7F,EAAE,CAAAwI,iBAAA,CAgK4CxI,EAAE,CAACyI,UAAU,GAhK3DzI,EAAE,CAAAwI,iBAAA,CAgKsExE,sBAAsB,GAhK9FhE,EAAE,CAAAwI,iBAAA,CAgKyGxI,EAAE,CAAC0I,MAAM,GAhKpH1I,EAAE,CAAAwI,iBAAA,CAgK+H/G,kBAAkB;EAAA,CAA4D;EAAA,QAAAmD,EAAA,GACtS,IAAI,CAAC+D,IAAI,kBAjK8E3I,EAAE,CAAA4I,iBAAA;IAAApD,IAAA,EAiKJK,kBAAkB;IAAAgD,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAjKhBjJ,EAAE,CAAAmJ,WAAA,OAAAD,GAAA,CAAAjD,EAAA;MAAA;IAAA;IAAAmD,MAAA;MAAAnD,EAAA;MAAAK,OAAA;MAAAC,KAAA;MAAAf,IAAA;MAAAgB,IAAA;MAAA6B,QAAA;MAAA5B,KAAA;MAAAP,SAAA;IAAA;IAAAmD,OAAA;MAAAlD,QAAA;MAAAC,KAAA;MAAAC,OAAA;IAAA;IAAAiD,QAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,4BAAAT,EAAA,EAAAC,GAAA;IAAAS,aAAA;EAAA,EAiKyX;AAC/d;AACA;EAAA,QAAArE,SAAA,oBAAAA,SAAA,KAnKoGtF,EAAE,CAAAuF,iBAAA,CAmKXM,kBAAkB,EAAc,CAAC;IAChHL,IAAI,EAAEjF,SAAS;IACfoF,IAAI,EAAE,CAAC;MACC2D,QAAQ,EAAE,WAAW;MACrBM,QAAQ,EAAE,YAAY;MACtBH,QAAQ,EAAG;IACf,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEjE,IAAI,EAAExF,EAAE,CAACyI;EAAW,CAAC,EAAE;IAAEjD,IAAI,EAAExB;EAAuB,CAAC,EAAE;IAAEwB,IAAI,EAAExF,EAAE,CAAC0I;EAAO,CAAC,EAAE;IAAElD,IAAI,EAAEhB,SAAS;IAAEkB,UAAU,EAAE,CAAC;MAC/HF,IAAI,EAAEnF;IACV,CAAC,EAAE;MACCmF,IAAI,EAAEpF,MAAM;MACZuF,IAAI,EAAE,CAAClE,kBAAkB;IAC7B,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEwE,EAAE,EAAE,CAAC;MAC9BT,IAAI,EAAEhF;IACV,CAAC,EAAE;MACCgF,IAAI,EAAE/E,WAAW;MACjBkF,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAEW,OAAO,EAAE,CAAC;MACVd,IAAI,EAAEhF;IACV,CAAC,CAAC;IAAE+F,KAAK,EAAE,CAAC;MACRf,IAAI,EAAEhF;IACV,CAAC,CAAC;IAAEgF,IAAI,EAAE,CAAC;MACPA,IAAI,EAAEhF;IACV,CAAC,CAAC;IAAEgG,IAAI,EAAE,CAAC;MACPhB,IAAI,EAAEhF;IACV,CAAC,CAAC;IAAE6H,QAAQ,EAAE,CAAC;MACX7C,IAAI,EAAEhF;IACV,CAAC,CAAC;IAAEiG,KAAK,EAAE,CAAC;MACRjB,IAAI,EAAEhF;IACV,CAAC,CAAC;IAAE0F,SAAS,EAAE,CAAC;MACZV,IAAI,EAAEhF;IACV,CAAC,CAAC;IAAE2F,QAAQ,EAAE,CAAC;MACXX,IAAI,EAAE9E;IACV,CAAC,CAAC;IAAE0F,KAAK,EAAE,CAAC;MACRZ,IAAI,EAAE9E;IACV,CAAC,CAAC;IAAE2F,OAAO,EAAE,CAAC;MACVb,IAAI,EAAE9E;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMmJ,qBAAqB,CAAC;EAAA,QAAA5F,CAAA,GACf,IAAI,CAACY,IAAI,YAAAiF,8BAAA/E,CAAA;IAAA,YAAAA,CAAA,IAAwF8E,qBAAqB;EAAA,CAAkD;EAAA,QAAAjF,EAAA,GACxK,IAAI,CAACmF,IAAI,kBA5M8E/J,EAAE,CAAAgK,gBAAA;IAAAxE,IAAA,EA4MSqE;EAAqB,EAAsE;EAAA,QAAA5E,EAAA,GAC7L,IAAI,CAACgF,IAAI,kBA7M8EjK,EAAE,CAAAkK,gBAAA,IA6MiC;AACvI;AACA;EAAA,QAAA5E,SAAA,oBAAAA,SAAA,KA/MoGtF,EAAE,CAAAuF,iBAAA,CA+MXsE,qBAAqB,EAAc,CAAC;IACnHrE,IAAI,EAAE7E,QAAQ;IACdgF,IAAI,EAAE,CAAC;MACCwE,YAAY,EAAE,CAACtE,kBAAkB,CAAC;MAClCuE,OAAO,EAAE,CAACvE,kBAAkB;IAChC,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMwE,eAAe,CAAC;EAAA,QAAApG,CAAA,GACT,IAAI,CAACY,IAAI,YAAAyF,wBAAAvF,CAAA;IAAA,YAAAA,CAAA,IAAwFsF,eAAe;EAAA,CAAkD;EAAA,QAAAzF,EAAA,GAClK,IAAI,CAACmF,IAAI,kBAzN8E/J,EAAE,CAAAgK,gBAAA;IAAAxE,IAAA,EAyNS6E;EAAe,EAAoE;EAAA,QAAApF,EAAA,GACrL,IAAI,CAACgF,IAAI,kBA1N8EjK,EAAE,CAAAkK,gBAAA;IAAAK,SAAA,EA0NqC,CAACvG,sBAAsB,CAAC;IAAAwG,OAAA,GAAYX,qBAAqB;EAAA,EAAI;AACxM;AACA;EAAA,QAAAvE,SAAA,oBAAAA,SAAA,KA5NoGtF,EAAE,CAAAuF,iBAAA,CA4NX8E,eAAe,EAAc,CAAC;IAC7G7E,IAAI,EAAE7E,QAAQ;IACdgF,IAAI,EAAE,CAAC;MACCyE,OAAO,EAAE,CAACvE,kBAAkB,CAAC;MAC7B2E,OAAO,EAAE,CAACX,qBAAqB,CAAC;MAChCU,SAAS,EAAE,CAACvG,sBAAsB;IACtC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyG,kBAAkB,CAAC;EACrBtG,WAAWA,CAAC4B,IAAI,EAAE2E,eAAe,EAAEpE,OAAO,EAAE;IACxC,IAAI,CAACoE,eAAe,GAAGA,eAAe;IACtC,IAAI,CAAC3E,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACO,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC/B,IAAI,CAAC,CAAC;EACf;EACA,IAAIoG,SAASA,CAAA,EAAG;IACZ,IAAI,CAAC,IAAI,CAACC,gBAAgB,EAAE;MACxB,IAAI,CAACA,gBAAgB,GAAG,IAAI1J,OAAO,CAAC,CAAC;MACrC,IAAI,CAAC2J,mBAAmB,GAAG,IAAI,CAACD,gBAAgB,CAAC/G,YAAY,CAAC,CAAC;IACnE;IACA,OAAO,IAAI,CAACgH,mBAAmB;EACnC;EACA,IAAIC,cAAcA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACC,qBAAqB,EAAE;MAC7B,IAAI,CAACA,qBAAqB,GAAG,IAAI7J,OAAO,CAAC,CAAC;MAC1C,IAAI,CAAC8J,wBAAwB,GAAG,IAAI,CAACD,qBAAqB,CAAClH,YAAY,CAAC,CAAC;IAC7E;IACA,OAAO,IAAI,CAACmH,wBAAwB;EACxC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI7D,OAAOA,CAAC8D,MAAM,EAAE;IACZ,MAAMrH,OAAO,GAAG,IAAI1C,OAAO,CAAC,CAAC;IAC7B,IAAI,CAAC,IAAI,CAACmB,UAAU,EAAE;MAClB,IAAI,CAAC,IAAI,CAAC6I,aAAa,EAAE;QACrB,IAAI,CAACA,aAAa,GAAG,EAAE;MAC3B;MACA,IAAI,CAACA,aAAa,CAACC,IAAI,CAAC,CAACF,MAAM,EAAErH,OAAO,CAAC,CAAC;IAC9C,CAAC,MACI;MACD,IAAI,CAACwH,wBAAwB,CAACH,MAAM,EAAErH,OAAO,CAAC;IAClD;IACA,OAAOA,OAAO,CAACC,YAAY,CAAC,CAAC;EACjC;EACA;EACAuH,wBAAwBA,CAACH,MAAM,EAAErH,OAAO,EAAE;IACtC;IACA,MAAM+D,OAAO,GAAIvB,KAAK,IAAK;MACvB,IAAI,CAACL,IAAI,CAACkC,GAAG,CAAC,MAAM;QAChBrE,OAAO,CAACwC,KAAK,CAACA,KAAK,CAAC;QACpB,IAAI,IAAI,CAAC2E,qBAAqB,EAAE;UAC5B;UACA;UACA,IAAI,CAACA,qBAAqB,CAACpG,IAAI,CAAC;YAAEsG,MAAM;YAAE7E;UAAM,CAAC,CAAC;QACtD;MACJ,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACL,IAAI,CAAC+B,iBAAiB,CAAC,MAAM;MAC9B,IAAI;QACA,IAAI,CAACzF,UAAU,CAAC8E,OAAO,CAAC,IAAI,CAACb,OAAO,EAAE;UAAE2E;QAAO,CAAC,CAAC,CAACI,IAAI,CAAEjG,KAAK,IAAK;UAC9D,IAAI,CAACW,IAAI,CAACkC,GAAG,CAAC,MAAM;YAChBrE,OAAO,CAACe,IAAI,CAACS,KAAK,CAAC;YACnBxB,OAAO,CAAC0H,QAAQ,CAAC,CAAC;YAClB,IAAI,IAAI,CAACV,gBAAgB,EAAE;cACvB,IAAI,CAACA,gBAAgB,CAACjG,IAAI,CAAC;gBAAEsG,MAAM;gBAAE7F;cAAM,CAAC,CAAC;YACjD;UACJ,CAAC,CAAC;QACN,CAAC,EAAEuC,OAAO,CAAC;MACf,CAAC,CACD,OAAO4D,CAAC,EAAE;QACN5D,OAAO,CAAC4D,CAAC,CAAC;MACd;IACJ,CAAC,CAAC;EACN;EACA;EACAhH,IAAIA,CAAA,EAAG;IACH,IAAI,CAACmG,eAAe,CAACxG,KAAK,CAAC0C,SAAS,CAAE7C,KAAK,IAAK;MAC5C,IAAI,CAAC1B,UAAU,GAAG0B,KAAK;MACvB,IAAI,IAAI,CAACmH,aAAa,IAAI,IAAI,CAACA,aAAa,CAACM,MAAM,GAAG,CAAC,EAAE;QACrD,IAAI,CAACN,aAAa,CAACO,OAAO,CAAC,CAAC,CAACR,MAAM,EAAErH,OAAO,CAAC,KAAK,IAAI,CAACwH,wBAAwB,CAACH,MAAM,EAAErH,OAAO,CAAC,CAAC;QACjG,IAAI,CAACsH,aAAa,GAAG1G,SAAS;MAClC;IACJ,CAAC,CAAC;EACN;EAAC,QAAAP,CAAA,GACQ,IAAI,CAACY,IAAI,YAAA6G,2BAAA3G,CAAA;IAAA,YAAAA,CAAA,IAAwF0F,kBAAkB,EAhU5BzK,EAAE,CAAAgF,QAAA,CAgU4ChF,EAAE,CAAC0I,MAAM,GAhUvD1I,EAAE,CAAAgF,QAAA,CAgUkEhB,sBAAsB,GAhU1FhE,EAAE,CAAAgF,QAAA,CAgUqGtD,qBAAqB;EAAA,CAA6C;EAAA,QAAAkD,EAAA,GAChQ,IAAI,CAACM,KAAK,kBAjU6ElF,EAAE,CAAAmF,kBAAA;IAAAC,KAAA,EAiUYqF,kBAAkB;IAAApF,OAAA,EAAlBoF,kBAAkB,CAAA5F;EAAA,EAAG;AACvI;AACA;EAAA,QAAAS,SAAA,oBAAAA,SAAA,KAnUoGtF,EAAE,CAAAuF,iBAAA,CAmUXkF,kBAAkB,EAAc,CAAC;IAChHjF,IAAI,EAAErF;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEqF,IAAI,EAAExF,EAAE,CAAC0I;EAAO,CAAC,EAAE;IAAElD,IAAI,EAAExB;EAAuB,CAAC,EAAE;IAAEwB,IAAI,EAAEhB,SAAS;IAAEkB,UAAU,EAAE,CAAC;MACtGF,IAAI,EAAEpF,MAAM;MACZuF,IAAI,EAAE,CAACjE,qBAAqB;IAChC,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAMiK,iBAAiB,CAAC;EAAA,QAAA1H,CAAA,GACX,IAAI,CAACY,IAAI,YAAA+G,0BAAA7G,CAAA;IAAA,YAAAA,CAAA,IAAwF4G,iBAAiB;EAAA,CAAkD;EAAA,QAAA/G,EAAA,GACpK,IAAI,CAACmF,IAAI,kBA5U8E/J,EAAE,CAAAgK,gBAAA;IAAAxE,IAAA,EA4USmG;EAAiB,EAAG;EAAA,QAAA1G,EAAA,GACtH,IAAI,CAACgF,IAAI,kBA7U8EjK,EAAE,CAAAkK,gBAAA;IAAAK,SAAA,EA6UuC,CAACE,kBAAkB,EAAEzG,sBAAsB;EAAC,EAAG;AAC5L;AACA;EAAA,QAAAsB,SAAA,oBAAAA,SAAA,KA/UoGtF,EAAE,CAAAuF,iBAAA,CA+UXoG,iBAAiB,EAAc,CAAC;IAC/GnG,IAAI,EAAE7E,QAAQ;IACdgF,IAAI,EAAE,CAAC;MACC4E,SAAS,EAAE,CAACE,kBAAkB,EAAEzG,sBAAsB;IAC1D,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAM6H,+BAA+B,CAAC;EAClC1H,WAAWA,CAAC2H,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,uBAAuB,GAAG,KAAK;EACxC;EACAC,UAAUA,CAACjI,KAAK,EAAE;IACd,IAAI,CAACA,KAAK,EAAE;MACR,IAAI,CAAC+H,IAAI,CAACxE,KAAK,CAAC,CAAC;IACrB,CAAC,MACI;MACD;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACwE,IAAI,CAACrE,oBAAoB,KAAK1D,KAAK,IAAIkI,OAAO,CAAC,IAAI,CAACH,IAAI,CAACrE,oBAAoB,CAAC,KAAK,KAAK,EAAE;QAC/F,IAAI,CAACsE,uBAAuB,GAAG,IAAI;MACvC;IACJ;EACJ;EACAG,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACC,QAAQ,GAAGD,EAAE;IAClB,IAAI,IAAI,CAACJ,uBAAuB,EAAE;MAC9B,IAAI,CAACA,uBAAuB,GAAG,KAAK;MACpC,IAAI,CAACK,QAAQ,CAAC,IAAI,CAAC;IACvB;EACJ;EACAC,iBAAiBA,CAACF,EAAE,EAAE;IAClB,IAAI,CAACG,SAAS,GAAGH,EAAE;EACvB;EACAI,SAASA,CAACC,MAAM,EAAE;IACd,IAAI,IAAI,CAACJ,QAAQ,EAAE;MACf,IAAI,CAACA,QAAQ,CAACI,MAAM,CAAC;IACzB;IACA,IAAI,IAAI,CAACF,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAAC,CAAC;IACpB;EACJ;EAAC,QAAArI,CAAA,GACQ,IAAI,CAACY,IAAI,YAAA4H,wCAAA1H,CAAA;IAAA,YAAAA,CAAA,IAAwF8G,+BAA+B,EA5XzC7L,EAAE,CAAAwI,iBAAA,CA4XyD3C,kBAAkB;EAAA,CAA4C;EAAA,QAAAjB,EAAA,GAChN,IAAI,CAAC8H,IAAI,kBA7X8E1M,EAAE,CAAA2M,iBAAA;IAAAnH,IAAA,EA6XJqG,+BAA+B;IAAAhD,SAAA;IAAAE,YAAA,WAAA6D,6CAAA3D,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA7X7BjJ,EAAE,CAAA6M,UAAA,sBAAAC,4DAAAN,MAAA;UAAA,OA6XJtD,GAAA,CAAAqD,SAAA,CAAAC,MAAgB,CAAC;QAAA,CAAa,CAAC;MAAA;IAAA;IAAAO,QAAA,GA7X7B/M,EAAE,CAAAgN,kBAAA,CA6XuL,CACjR;MACIC,KAAK,EAAE,IAAI;MACXC,OAAO,EAAE9L,iBAAiB;MAC1B+L,WAAW,EAAEvM,UAAU,CAAC,MAAMiL,+BAA+B;IACjE,CAAC,CACJ;EAAA,EAAiB;AAC1B;AACA;EAAA,QAAAvG,SAAA,oBAAAA,SAAA,KArYoGtF,EAAE,CAAAuF,iBAAA,CAqYXsG,+BAA+B,EAAc,CAAC;IAC7HrG,IAAI,EAAE3E,SAAS;IACf8E,IAAI,EAAE,CAAC;MACC4E,SAAS,EAAE,CACP;QACI0C,KAAK,EAAE,IAAI;QACXC,OAAO,EAAE9L,iBAAiB;QAC1B+L,WAAW,EAAEvM,UAAU,CAAC,MAAMiL,+BAA+B;MACjE,CAAC,CACJ;MACDjC,QAAQ,EAAE;IACd,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEpE,IAAI,EAAEK;EAAmB,CAAC,CAAC,EAAkB;IAAE0G,SAAS,EAAE,CAAC;MAChF/G,IAAI,EAAE1E,YAAY;MAClB6E,IAAI,EAAE,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC;IACjC,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMyH,oBAAoB,CAAC;EAAA,QAAAnJ,CAAA,GACd,IAAI,CAACY,IAAI,YAAAwI,6BAAAtI,CAAA;IAAA,YAAAA,CAAA,IAAwFqI,oBAAoB;EAAA,CAAkD;EAAA,QAAAxI,EAAA,GACvK,IAAI,CAACmF,IAAI,kBAxZ8E/J,EAAE,CAAAgK,gBAAA;IAAAxE,IAAA,EAwZS4H;EAAoB,EAA+I;EAAA,QAAAnI,EAAA,GACrQ,IAAI,CAACgF,IAAI,kBAzZ8EjK,EAAE,CAAAkK,gBAAA;IAAAM,OAAA,GAyZyCnJ,WAAW,EAAEwI,qBAAqB;EAAA,EAAI;AACrL;AACA;EAAA,QAAAvE,SAAA,oBAAAA,SAAA,KA3ZoGtF,EAAE,CAAAuF,iBAAA,CA2ZX6H,oBAAoB,EAAc,CAAC;IAClH5H,IAAI,EAAE7E,QAAQ;IACdgF,IAAI,EAAE,CAAC;MACCwE,YAAY,EAAE,CAAC0B,+BAA+B,CAAC;MAC/CzB,OAAO,EAAE,CAACyB,+BAA+B,CAAC;MAC1CrB,OAAO,EAAE,CAACnJ,WAAW,EAAEwI,qBAAqB;IAChD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAStI,kBAAkB,EAAED,kBAAkB,EAAEK,wBAAwB,EAAEH,eAAe,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAE+I,kBAAkB,EAAE5E,kBAAkB,EAAEuH,oBAAoB,EAAEpJ,sBAAsB,EAAEqG,eAAe,EAAEsB,iBAAiB,EAAEE,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}