{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/layout.service\";\nimport * as i2 from \"src/app/core/services/general.service\";\nconst _c0 = [\"nextStepsWrapper\"];\nconst _c1 = [\"firstStep\"];\nconst _c2 = [\"secondStep\"];\nconst _c3 = [\"thirdStep\"];\nexport let DashboardNextStepsComponent = /*#__PURE__*/(() => {\n  class DashboardNextStepsComponent {\n    constructor(layoutService, generalService) {\n      this.layoutService = layoutService;\n      this.generalService = generalService;\n      this.nextStepsWrapper = {};\n      this.firstStep = {};\n      this.secondStep = {};\n      this.thirdStep = {};\n      this.activeStep = 1;\n      this.stepOneInfo = {\n        icon: '/assets/images/dashboard/icon-send-purple.png',\n        title: 'Request a Free Trial',\n        description: `Once you submit your Free Trial request, we will contact you for more information regarding your level, your availability, your goals, \n    and any specific requirements you may have. That way, we can match you with the most suitable of our teachers.`\n      };\n      this.stepThreeInfo = {\n        icon: '/assets/images/dashboard/comm-purple.png',\n        title: 'Choose a package and begin learning!',\n        description: `Once you have completed your Free Trial, you can proceed by choosing among our packages of 5, 10, 15, 20, and 50 hours. \n      After that, you are all set to begin learning your favorite language with MyLingoTrip.`\n      };\n    }\n    ngOnInit() {\n      // this.layoutService.sideMenuHeight.subscribe((res) => {\n      // this.nextStepsWrapper.nativeElement.style.height = ('700px');\n      // });\n    }\n    ngAfterViewInit() {\n      switch (this.activeStep) {\n        case 1:\n          this.addLottiePlayerToStep(this.firstStep);\n          break;\n        case 2:\n          this.addLottiePlayerToStep(this.secondStep);\n          break;\n        case 3:\n          this.addLottiePlayerToStep(this.thirdStep);\n          break;\n      }\n    }\n    addLottiePlayerToStep(step) {\n      const lottiePlayer = `<div  class=\"animation-wrapper\"><lottie-player src=\"https://assets1.lottiefiles.com/packages/lf20_80nu1g6c.json\" background=\"transparent\" \n    speed=\"1\" style=\"width: 42px; height: 42px;\" autoplay loop></lottie-player></div>`;\n      step.nativeElement.innerHTML = lottiePlayer;\n    }\n    static #_ = this.ɵfac = function DashboardNextStepsComponent_Factory(t) {\n      return new (t || DashboardNextStepsComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.GeneralService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardNextStepsComponent,\n      selectors: [[\"app-dashboard-next-steps\"]],\n      viewQuery: function DashboardNextStepsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n          i0.ɵɵviewQuery(_c1, 7);\n          i0.ɵɵviewQuery(_c2, 7);\n          i0.ɵɵviewQuery(_c3, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nextStepsWrapper = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.firstStep = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.secondStep = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.thirdStep = _t.first);\n        }\n      },\n      inputs: {\n        activeStep: \"activeStep\",\n        stepOneInfo: \"stepOneInfo\",\n        stepThreeInfo: \"stepThreeInfo\"\n      },\n      decls: 69,\n      vars: 6,\n      consts: [[\"nextStepsWrapper\", \"\"], [\"firstStep\", \"\"], [\"secondStep\", \"\"], [\"thirdStep\", \"\"], [1, \"next-steps\"], [1, \"next-steps-wrapper\", \"block\"], [1, \"enroll-step\", \"lg:absolute\"], [1, \"col-12\", \"md:col-5\", \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\", \"border-circle\"], [1, \"text-primary\", \"font-bold\", \"text-2xl\", \"m-0\"], [\"pRipple\", \"\", \"pbutton\", \"\", \"pripple\", \"\", \"label\", \"Enroll Now\", 1, \"p-element\", \"p-ripple\", \"font-bold\", \"px-3\", \"py-3\", \"p-button-outlined\", \"p-button-rounded\", \"border-white\", \"white-space-nowrap\", \"p-button\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"p-button-icon\", \"p-button-icon-right\"], [1, \"p-button-label\", \"mr-3\"], [1, \"pi\", \"pi-arrow-right\"], [1, \"step-wrapper\", \"flex-column\", \"lg:flex-row\"], [1, \"step1\", \"col-12\", \"lg:col-7\", \"text-center\", \"flex\", \"flex-column\", \"lg:flex-row\"], [1, \"relative\", \"max-w-11rem\", \"mx-auto\", \"align-items-center\", \"align-self-center\"], [1, \"animation-wrapper\"], [\"src\", \"/assets/images/dashboard/next-steps-1-bubble.png\", 1, \"align-self-center\", \"bubble-img\"], [1, \"icon-text-wrapper\", \"align-items-center\", \"justify-content-center\", \"flex-column\", \"lg:mr-6\"], [1, \"mb-flex\"], [1, \"mb-1\"], [1, \"step-3-img\", \"relative\", \"mb-1\", 3, \"src\"], [1, \"heading\", \"text-primary\", \"mb-1\", \"font-semibold\", \"font-xl\", \"line-height-1\"], [1, \"description\", \"text-700\", \"text-primary\", \"font-sm\"], [1, \"tail-wrapper-mobile\", \"mt-5\"], [\"src\", \"/assets/images/dashboard/step2.svg\", 1, \"w-100\", \"tail-img\"], [1, \"step2\", \"flex\", \"flex-colum\", \"step-wrapper\"], [1, \"col-12\", \"lg:col-4\", \"text-center\", \"text-wrapper\"], [1, \"step-2-img\", \"lg:relativelg:\", \"max-w-11rem\", \"lg:mx-auto\", \"lg:align-items-center\", \"lg:align-self-center\"], [\"src\", \"/assets/images/dashboard/next-steps-2-bubble.png\", 1, \"w-16rem\"], [\"src\", \"/assets/images/dashboard/next-steps-2-bubble2.png\", 1, \"img-mb\"], [1, \"mb-1\", \"block\", \"text-center\"], [\"src\", \"/assets/images/dashboard/icon-biz-purple.png\", 1, \"step-3-img\", \"relative\", \"mb-1\", \"step-icon\"], [1, \"heading\", \"text-primary\", \"mb-1\", \"line-height-1\", \"font-semibold\", \"font-xl\", \"xl:px-6\"], [1, \"description\", \"text-700\", \"text-primary\", \"font-sm\", \"xl:px-4\"], [1, \"tail-wrapper-mobile\", \"mt-8\"], [\"src\", \"/assets/images/dashboard/road3-1.svg\", 1, \"w-100\", \"tail-img\"], [1, \"tail-wrapper\"], [\"src\", \"/assets/images/dashboard/next-steps-tail-group.svg\", 1, \"w-100\", \"tail-img\"], [1, \"lg:absolute\", \"step-3\"], [1, \"relative\", \"flex\", \"flex-column\"], [1, \"col-12\", \"lg:col-5\", \"text-center\", \"flex\", \"flex-column\", \"align-items-center\"], [1, \"relative\"], [\"src\", \"/assets/images/dashboard/next-steps-3-bubble.png\", 1, \"step-3-img\", \"absolute\", \"w-13rem\"], [1, \"step-3-img\", \"relative\", \"mb-1\", \"step-icon\", 3, \"src\"], [1, \"heading\", \"text-primary\", \"mb-1\", \"font-semibold\", \"font-xl\", \"line-height-1\", \"lg:px-7\"], [1, \"description\", \"text-700\", \"text-primary\", \"font-sm\", \"lg:px-5\"], [\"src\", \"/assets/images/dashboard/road3-2.svg\", 1, \"w-100\", \"tail-img\", \"last-road\"], [1, \"flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\", \"lg:pl-8\", \"lg:ml-5\", \"lg:pb-2\", \"happy-step\"], [1, \"text-center\"], [1, \"text-primary\", \"lg:max-w-11rem\"], [1, \"heading\", \"font-semibold\", \"font-xl\", \"text-primary\", \"line-height-1\"], [\"src\", \"/assets/images/dashboard/infinite-steps.png\", 1, \"lg:w-4\", \"lg:mt-2\"]],\n      template: function DashboardNextStepsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5, 0)(3, \"div\", 6)(4, \"div\", 7)(5, \"div\", 8)(6, \"h3\", 9);\n          i0.ɵɵtext(7, \" Can't wait?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"button\", 10);\n          i0.ɵɵlistener(\"click\", function DashboardNextStepsComponent_Template_button_click_8_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.generalService.navigateToBuyPackage());\n          });\n          i0.ɵɵelement(9, \"span\", 11);\n          i0.ɵɵelementStart(10, \"span\", 12);\n          i0.ɵɵtext(11, \"Enroll now\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(12, \"i\", 13);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(13, \"div\", 14)(14, \"div\", 15)(15, \"div\", 16);\n          i0.ɵɵelement(16, \"div\", 17, 1)(18, \"img\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"div\", 19)(20, \"div\", 20)(21, \"span\", 21);\n          i0.ɵɵelement(22, \"img\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\", 23);\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"span\", 24);\n          i0.ɵɵtext(26);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(27, \"div\", 25);\n          i0.ɵɵelement(28, \"img\", 26);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"div\", 27)(30, \"div\", 28)(31, \"div\", 29);\n          i0.ɵɵelement(32, \"div\", 17, 2)(34, \"img\", 30)(35, \"img\", 31);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"div\", 20)(37, \"span\", 32);\n          i0.ɵɵelement(38, \"img\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"div\", 34);\n          i0.ɵɵtext(40, \" Get in touch with your assigned teacher \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"span\", 35);\n          i0.ɵɵtext(42, \" Your newly assigned teacher will then get in touch with you so the two of you can arrange the lesson based on your schedule. After assessing your personal requirements, your tutor will be able to establish a personalized course plan based on your own daily schedule. \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(43, \"div\", 36);\n          i0.ɵɵelement(44, \"img\", 37);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"div\", 38);\n          i0.ɵɵelement(46, \"img\", 39);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"div\", 40)(48, \"div\", 41)(49, \"div\", 42)(50, \"div\", 43);\n          i0.ɵɵelement(51, \"div\", 17, 3)(53, \"img\", 44);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"div\", 20)(55, \"span\", 21);\n          i0.ɵɵelement(56, \"img\", 45);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"div\", 46);\n          i0.ɵɵtext(58);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(59, \"span\", 47);\n          i0.ɵɵtext(60);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(61, \"div\", 36);\n          i0.ɵɵelement(62, \"img\", 48);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"div\", 49)(64, \"div\", 50)(65, \"div\", 51)(66, \"div\", 52);\n          i0.ɵɵtext(67, \"Happy Learning! \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(68, \"img\", 53);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(22);\n          i0.ɵɵproperty(\"src\", ctx.stepOneInfo.icon, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.stepOneInfo.title);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.stepOneInfo.description, \" \");\n          i0.ɵɵadvance(30);\n          i0.ɵɵproperty(\"src\", ctx.stepThreeInfo.icon, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.stepThreeInfo.title, \" \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.stepThreeInfo.description, \" \");\n        }\n      },\n      styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}.next-steps[_ngcontent-%COMP%]{display:block;position:relative}.next-steps-wrapper[_ngcontent-%COMP%]{background-image:url(/assets/images/dashboard/next-steps-bg-1.png);background-size:cover;border-radius:40px;position:relative;padding-top:0;height:100%;min-height:700px}.next-steps-wrapper[_ngcontent-%COMP%]   .tail-wrapper-mobile[_ngcontent-%COMP%]{display:none}.next-steps-wrapper[_ngcontent-%COMP%]   .last-road[_ngcontent-%COMP%]{position:absolute;bottom:0}@media only screen and (max-width: 768px){.next-steps-wrapper[_ngcontent-%COMP%]{height:unset!important;border-radius:10px;background-position:center center;padding-top:2rem}.next-steps-wrapper[_ngcontent-%COMP%]   .tail-wrapper[_ngcontent-%COMP%]{display:none}.next-steps-wrapper[_ngcontent-%COMP%]   .tail-wrapper-mobile[_ngcontent-%COMP%]{display:block;mix-blend-mode:multiply}}.next-steps-wrapper[_ngcontent-%COMP%]   .animation-wrapper[_ngcontent-%COMP%]{width:42px;height:42px;top:25%;transform:translateY(-50%);position:absolute;transform:scale(1.6);left:-12px}.next-steps-wrapper[_ngcontent-%COMP%]   .enroll-step[_ngcontent-%COMP%]{z-index:4;text-align:center;display:flex;align-items:center;justify-content:center}@media only screen and (min-width: 992px){.next-steps-wrapper[_ngcontent-%COMP%]   .enroll-step[_ngcontent-%COMP%]{top:0;right:10px}}@media only screen and (max-width: 768px){.next-steps-wrapper[_ngcontent-%COMP%]   .enroll-step[_ngcontent-%COMP%]{display:none}}.next-steps-wrapper[_ngcontent-%COMP%]   .enroll-circle[_ngcontent-%COMP%]{background:linear-gradient(#345dd8,#c1c6ff);padding:30px}.next-steps-wrapper[_ngcontent-%COMP%]   .enroll-circle[_ngcontent-%COMP%]   .p-button.p-button-outlined[_ngcontent-%COMP%]{color:#fff;border-color:#fff}.next-steps-wrapper[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]{position:relative;width:100%;height:auto}@media only screen and (min-width: 992px){.next-steps-wrapper[_ngcontent-%COMP%]   .step-wrapper[_ngcontent-%COMP%]{position:absolute;width:100%}}.next-steps-wrapper[_ngcontent-%COMP%]   .tail-wrapper[_ngcontent-%COMP%]{mix-blend-mode:multiply}@media only screen and (min-width: 992px){.next-steps-wrapper[_ngcontent-%COMP%]   .tail-wrapper[_ngcontent-%COMP%]{position:absolute;width:100%;transform:translateY(-50%);height:100%}}@media only screen and (min-width: 992px) and (min-height: 700px){.next-steps-wrapper[_ngcontent-%COMP%]   .tail-wrapper[_ngcontent-%COMP%]{top:76%}}.next-steps-wrapper[_ngcontent-%COMP%]   .tail-wrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:100%}@media only screen and (min-width: 992px){.next-steps-wrapper[_ngcontent-%COMP%]   .tail-wrapper[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{position:relative;top:31px}}.next-steps-wrapper[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{font-size:clamp(.88rem,.78vw + .72rem,1.5rem)}.next-steps-wrapper[_ngcontent-%COMP%]   .heading.bigger[_ngcontent-%COMP%]{font-size:clamp(.88rem,1.25vw + .63rem,1.88rem)}.next-steps-wrapper[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]{font-size:clamp(.63rem,.47vw + .53rem,1rem)}.next-steps-wrapper[_ngcontent-%COMP%]   .step1[_ngcontent-%COMP%]   .mb-flex[_ngcontent-%COMP%]{display:unset}@media only screen and (min-width: 992px){.next-steps-wrapper[_ngcontent-%COMP%]   .step1[_ngcontent-%COMP%]{left:40px;position:relative}}.next-steps-wrapper[_ngcontent-%COMP%]   .step1[_ngcontent-%COMP%]   .bubble-img[_ngcontent-%COMP%]{max-width:clamp(6.88rem,3.91vw + 6.09rem,10rem)}@media only screen and (max-width: 768px){.next-steps-wrapper[_ngcontent-%COMP%]   .step1[_ngcontent-%COMP%]   .bubble-img[_ngcontent-%COMP%]{max-width:10rem}}.next-steps-wrapper[_ngcontent-%COMP%]   .step1[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:2rem}@media only screen and (min-width: 992px){.next-steps-wrapper[_ngcontent-%COMP%]   .step1[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:4rem}}@media only screen and (max-width: 768px){.next-steps-wrapper[_ngcontent-%COMP%]   .step1[_ngcontent-%COMP%]{padding:0 1.5rem}.next-steps-wrapper[_ngcontent-%COMP%]   .step1[_ngcontent-%COMP%]   .mb-flex[_ngcontent-%COMP%]{display:flex;justify-content:center}.next-steps-wrapper[_ngcontent-%COMP%]   .step1[_ngcontent-%COMP%]   .mb-flex[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{width:70%}}.next-steps-wrapper[_ngcontent-%COMP%]   .step1[_ngcontent-%COMP%]   .icon-text-wrapper[_ngcontent-%COMP%]{max-width:100%}.next-steps-wrapper[_ngcontent-%COMP%]   .step2[_ngcontent-%COMP%]{top:0;right:10px}.next-steps-wrapper[_ngcontent-%COMP%]   .step2[_ngcontent-%COMP%]   .mb-flex[_ngcontent-%COMP%]{display:unset}@media only screen and (min-width: 992px){.next-steps-wrapper[_ngcontent-%COMP%]   .step2[_ngcontent-%COMP%]{margin-top:0;top:50%;transform:translateY(-50%);justify-content:flex-end}}@media screen and (min-height: 700px){.next-steps-wrapper[_ngcontent-%COMP%]   .step2[_ngcontent-%COMP%]{top:42%}}@media only screen and (max-width: 768px){.next-steps-wrapper[_ngcontent-%COMP%]   .step2[_ngcontent-%COMP%]{padding:0 1.5rem}.next-steps-wrapper[_ngcontent-%COMP%]   .step2[_ngcontent-%COMP%]   .mb-flex[_ngcontent-%COMP%]{display:flex;justify-content:center}.next-steps-wrapper[_ngcontent-%COMP%]   .step2[_ngcontent-%COMP%]   .mb-flex[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{width:70%}}.next-steps-wrapper[_ngcontent-%COMP%]   .step2[_ngcontent-%COMP%]   .text-wrapper[_ngcontent-%COMP%]{margin-top:10px;right:-3px;top:-35px;position:relative;z-index:10}.next-steps-wrapper[_ngcontent-%COMP%]   .step2[_ngcontent-%COMP%]   .step-2-img[_ngcontent-%COMP%]{max-width:clamp(6.88rem,7.81vw + 5.31rem,13.13rem);position:relative;display:block!important;display:unset}.next-steps-wrapper[_ngcontent-%COMP%]   .step2[_ngcontent-%COMP%]   .step-2-img[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:100%}@media only screen and (max-width: 768px){.next-steps-wrapper[_ngcontent-%COMP%]   .step2[_ngcontent-%COMP%]   .step-2-img[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:not(.img-mb){display:none}}@media only screen and (min-width: 768px){.next-steps-wrapper[_ngcontent-%COMP%]   .step2[_ngcontent-%COMP%]   .step-2-img[_ngcontent-%COMP%]{max-width:clamp(6.88rem,7.03vw + 5.47rem,12.5rem)}}@media only screen and (min-width: 1200px){.next-steps-wrapper[_ngcontent-%COMP%]   .step2[_ngcontent-%COMP%]   .step-2-img[_ngcontent-%COMP%]{top:36px;left:-115px}}@media only screen and (max-width: 768px){.next-steps-wrapper[_ngcontent-%COMP%]   .step2[_ngcontent-%COMP%]   .step-2-img[_ngcontent-%COMP%]   .animation-wrapper[_ngcontent-%COMP%]{top:-120px;left:65px}}.next-steps-wrapper[_ngcontent-%COMP%]   .step2[_ngcontent-%COMP%]   .step-2-img[_ngcontent-%COMP%]   .img-mb[_ngcontent-%COMP%]{display:none}@media only screen and (max-width: 768px){.next-steps-wrapper[_ngcontent-%COMP%]   .step2[_ngcontent-%COMP%]   .step-2-img[_ngcontent-%COMP%]   .img-mb[_ngcontent-%COMP%]{display:unset;position:absolute;top:-182px;left:37vw;max-width:13rem}}.next-steps-wrapper[_ngcontent-%COMP%]   .step-3[_ngcontent-%COMP%]{mix-blend-mode:multiply;bottom:0}.next-steps-wrapper[_ngcontent-%COMP%]   .step-3[_ngcontent-%COMP%]   .mb-flex[_ngcontent-%COMP%]{display:unset}.next-steps-wrapper[_ngcontent-%COMP%]   .step-3[_ngcontent-%COMP%]   .animation-wrapper[_ngcontent-%COMP%]{top:-100px;left:-125px}@media only screen and (min-width: 768px){.next-steps-wrapper[_ngcontent-%COMP%]   .step-3[_ngcontent-%COMP%]{bottom:30px}}@media only screen and (min-width: 992px){.next-steps-wrapper[_ngcontent-%COMP%]   .step-3[_ngcontent-%COMP%]{bottom:10px;left:45px;width:100%}}@media screen and (max-height: 700px){.next-steps-wrapper[_ngcontent-%COMP%]   .step-3[_ngcontent-%COMP%]{bottom:5%;left:25px}.next-steps-wrapper[_ngcontent-%COMP%]   .step-3[_ngcontent-%COMP%]   .animation-wrapper[_ngcontent-%COMP%]{top:45px;left:-170px}}@media only screen and (max-width: 992px) and (max-height: 992px){.next-steps-wrapper[_ngcontent-%COMP%]   .step-3[_ngcontent-%COMP%]{bottom:20px}}@media only screen and (max-width: 768px){.next-steps-wrapper[_ngcontent-%COMP%]   .step-3[_ngcontent-%COMP%]{padding:0 1.5rem;margin-top:-50px}.next-steps-wrapper[_ngcontent-%COMP%]   .step-3[_ngcontent-%COMP%]   .mb-flex[_ngcontent-%COMP%]{display:flex;justify-content:center}.next-steps-wrapper[_ngcontent-%COMP%]   .step-3[_ngcontent-%COMP%]   .mb-flex[_ngcontent-%COMP%]   .heading[_ngcontent-%COMP%]{width:70%}}.next-steps-wrapper[_ngcontent-%COMP%]   .step-3[_ngcontent-%COMP%]   .step-3-img[_ngcontent-%COMP%]{max-width:220px;margin-bottom:1rem;display:initial}.next-steps-wrapper[_ngcontent-%COMP%]   .step-3[_ngcontent-%COMP%]   .step-3-img[_ngcontent-%COMP%]:not(.step-icon){top:-7.6rem;left:-100px}@media screen and (max-height: 700px){.next-steps-wrapper[_ngcontent-%COMP%]   .step-3[_ngcontent-%COMP%]   .step-3-img[_ngcontent-%COMP%]:not(.step-icon){top:-5.5rem;left:-220px}}@media only screen and (max-width: 768px){.next-steps-wrapper[_ngcontent-%COMP%]   .step-3[_ngcontent-%COMP%]   .step-3-img[_ngcontent-%COMP%]:not(.step-icon){top:-165px;left:-2vw}}@media only screen and (min-width: 576px){.next-steps-wrapper[_ngcontent-%COMP%]   .step-3[_ngcontent-%COMP%]   .step-3-img[_ngcontent-%COMP%]{max-width:clamp(6.88rem,7.81vw + 5.31rem,13.13rem)}}@media only screen and (max-width: 768px){.next-steps-wrapper[_ngcontent-%COMP%]   .step-3[_ngcontent-%COMP%]   .step-3-img[_ngcontent-%COMP%]{display:flex;justify-content:center;margin:auto;margin-bottom:1rem}.next-steps-wrapper[_ngcontent-%COMP%]   .step-3[_ngcontent-%COMP%]   .step-3-img[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:9.5rem}}.next-steps-wrapper[_ngcontent-%COMP%]   .step-3[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]{max-width:3rem!important}@media only screen and (min-width: 992px){.next-steps-wrapper[_ngcontent-%COMP%]   .step-3[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]{max-width:4rem!important}}.next-steps-wrapper[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]{max-width:2.5rem!important}@media only screen and (min-width: 992px){.next-steps-wrapper[_ngcontent-%COMP%]   .step-icon[_ngcontent-%COMP%]{max-width:4rem!important}}.next-steps-wrapper[_ngcontent-%COMP%]   .happy-step[_ngcontent-%COMP%]{position:relative}@media only screen and (min-width: 992px){.next-steps-wrapper[_ngcontent-%COMP%]   .happy-step[_ngcontent-%COMP%]{bottom:0;right:133px;position:absolute}}@media screen and (min-height: 700px){.next-steps-wrapper[_ngcontent-%COMP%]   .happy-step[_ngcontent-%COMP%]{bottom:18px}.next-steps-wrapper[_ngcontent-%COMP%]   .happy-step[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{mix-blend-mode:darken;width:100%!important}}@media only screen and (max-width: 768px){.next-steps-wrapper[_ngcontent-%COMP%]   .happy-step[_ngcontent-%COMP%]{bottom:10px;left:70vw;width:2rem!important}}\"],\n      changeDetection: 0\n    });\n  }\n  return DashboardNextStepsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}