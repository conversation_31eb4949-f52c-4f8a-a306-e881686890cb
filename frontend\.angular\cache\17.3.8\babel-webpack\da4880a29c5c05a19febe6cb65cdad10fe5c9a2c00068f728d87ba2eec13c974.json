{"ast": null, "code": "import { ViewContainerRef } from '@angular/core';\nimport { of } from 'rxjs';\nimport { switchMap, take } from 'rxjs/operators';\nimport { Status } from 'src/app/core/models/classroom.model';\nimport { LessonStatus } from 'src/app/core/models/lesson.model';\nimport { UserRole } from 'src/app/core/models/user.model';\nimport { slideInOut } from 'src/app/helpers/my-animations';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nimport * as i2 from \"src/app/core/services/modal.service\";\nimport * as i3 from \"src/app/core/services/classroom.service\";\nimport * as i4 from \"src/app/core/services/auth.service\";\nimport * as i5 from \"src/app/core/services/layout.service\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"src/app/core/services/confirm-dialog.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"../../shared/loader/loader.component\";\nimport * as i10 from \"../../shared/classroom/classroom-card/classroom-card.component\";\nimport * as i11 from \"../../shared/classroom/teacher-classroom-mini-card/teacher-classroom-mini-card.component\";\nimport * as i12 from \"../../shared/classroom/classroom-level-filters/classroom-level-filters.component\";\nimport * as i13 from \"../../shared/classroom/classroom-status-filters/classroom-status-filters.component\";\nimport * as i14 from \"primeng/scrollpanel\";\nimport * as i15 from \"./stats/stats.component\";\nimport * as i16 from \"./classrooms/classrooms.component\";\nconst _c0 = [\"filtersModal\"];\nconst _c1 = [\"classroomWrapper\"];\nconst _c2 = [\"classroomScrollWrapper\"];\nconst ClassroomComponent_Defer_14_DepsFn = () => [i8.NgForOf, i8.NgIf, i9.LoaderComponent, i10.ClassroomCardComponent, i11.TeacherClassroomMiniCardComponent, i12.ClassroomLevelFiltersComponent, i13.ClassroomStatusFiltersComponent, i14.ScrollPanel, i15.StatsComponent, i16.ClassroomsComponent];\nconst _c3 = a0 => ({\n  \"trials-header\": a0\n});\nconst _c4 = a0 => ({\n  width: \"100%\",\n  height: a0\n});\nfunction ClassroomComponent_Defer_12_ng_container_0_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵlistener(\"click\", function ClassroomComponent_Defer_12_ng_container_0_div_1_div_5_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.viewSection(\"trials\"));\n    });\n    i0.ɵɵelement(1, \"img\", 26);\n    i0.ɵɵtext(2, \" Trials \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"classrooms-header-active\", ctx_r2.router.url.includes(\"trials\"));\n  }\n}\nfunction ClassroomComponent_Defer_12_ng_container_0_div_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵlistener(\"click\", function ClassroomComponent_Defer_12_ng_container_0_div_1_div_6_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.viewSection(\"stats\"));\n    });\n    i0.ɵɵelement(1, \"img\", 28);\n    i0.ɵɵtext(2, \" Stats \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClassroomComponent_Defer_12_ng_container_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20);\n    i0.ɵɵlistener(\"click\", function ClassroomComponent_Defer_12_ng_container_0_div_1_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.viewSection(\"classrooms\"));\n    });\n    i0.ɵɵelement(2, \"img\", 21);\n    i0.ɵɵtext(3, \" Classrooms \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"div\", 22);\n    i0.ɵɵtemplate(5, ClassroomComponent_Defer_12_ng_container_0_div_1_div_5_Template, 3, 2, \"div\", 23)(6, ClassroomComponent_Defer_12_ng_container_0_div_1_div_6_Template, 3, 0, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"classrooms-header-active\", !ctx_r2.router.url.includes(\"trials\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.role == \"Student\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.role == \"Teacher\");\n  }\n}\nfunction ClassroomComponent_Defer_12_ng_container_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 29);\n  }\n}\nfunction ClassroomComponent_Defer_12_ng_container_0_ng_container_8_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 33)(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"img\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"No \", ctx_r2.showTrials ? \"trials\" : \"classrooms\", \" for this section\");\n  }\n}\nfunction ClassroomComponent_Defer_12_ng_container_0_ng_container_8_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵelement(1, \"app-teacher-classroom-mini-card\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const class_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"classroom\", class_r6)(\"isTrial\", class_r6.type === \"Trial\");\n  }\n}\nfunction ClassroomComponent_Defer_12_ng_container_0_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p-scrollPanel\", 30);\n    i0.ɵɵtemplate(2, ClassroomComponent_Defer_12_ng_container_0_ng_container_8_ng_container_2_Template, 5, 1, \"ng-container\", 11);\n    i0.ɵɵelementStart(3, \"div\", 31);\n    i0.ɵɵtemplate(4, ClassroomComponent_Defer_12_ng_container_0_ng_container_8_div_4_Template, 2, 2, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction1(4, _c4, ctx_r2.scrollHeight));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.filteredClassrooms.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.filteredClassrooms);\n  }\n}\nfunction ClassroomComponent_Defer_12_ng_container_0_ng_container_9_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 33);\n    i0.ɵɵelement(2, \"img\", 34);\n    i0.ɵɵelementStart(3, \"strong\");\n    i0.ɵɵtext(4, \"No classrooms found.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ClassroomComponent_Defer_12_ng_container_0_ng_container_9_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-classroom-card\", 39);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const class_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"classRoom\", class_r7);\n  }\n}\nfunction ClassroomComponent_Defer_12_ng_container_0_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 37);\n    i0.ɵɵtemplate(2, ClassroomComponent_Defer_12_ng_container_0_ng_container_9_ng_container_2_Template, 5, 0, \"ng-container\", 11)(3, ClassroomComponent_Defer_12_ng_container_0_ng_container_9_ng_container_3_Template, 2, 1, \"ng-container\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.filteredClassrooms.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.filteredClassrooms);\n  }\n}\nfunction ClassroomComponent_Defer_12_ng_container_0_div_10_ng_container_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 33);\n    i0.ɵɵelement(2, \"img\", 34);\n    i0.ɵɵelementStart(3, \"strong\");\n    i0.ɵɵtext(4, \"No trials found.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ClassroomComponent_Defer_12_ng_container_0_div_10_ng_container_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-classroom-card\", 42);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const trialClassroom_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"classRoom\", trialClassroom_r8);\n  }\n}\nfunction ClassroomComponent_Defer_12_ng_container_0_div_10_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 41);\n    i0.ɵɵtemplate(2, ClassroomComponent_Defer_12_ng_container_0_div_10_ng_container_1_ng_container_2_Template, 5, 0, \"ng-container\", 11)(3, ClassroomComponent_Defer_12_ng_container_0_div_10_ng_container_1_ng_container_3_Template, 2, 1, \"ng-container\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.filteredClassrooms.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.filteredClassrooms);\n  }\n}\nfunction ClassroomComponent_Defer_12_ng_container_0_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵtemplate(1, ClassroomComponent_Defer_12_ng_container_0_div_10_ng_container_1_Template, 4, 2, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.role === ctx_r2.UserRoles.STUDENT);\n  }\n}\nfunction ClassroomComponent_Defer_12_ng_container_0_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-classrooms\", 43);\n    i0.ɵɵelementContainer(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"@slideInOut\", undefined)(\"filteredClassrooms\", ctx_r2.filteredClassrooms);\n  }\n}\nfunction ClassroomComponent_Defer_12_ng_container_0_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-stats\", 44);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"@slideInOut\", undefined)(\"classroomsType\", \"Paid\");\n  }\n}\nfunction ClassroomComponent_Defer_12_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ClassroomComponent_Defer_12_ng_container_0_div_1_Template, 7, 4, \"div\", 12)(2, ClassroomComponent_Defer_12_ng_container_0_div_2_Template, 1, 0, \"div\", 13);\n    i0.ɵɵelementStart(3, \"div\", 14)(4, \"div\", 15)(5, \"div\", 4)(6, \"app-classroom-level-filters\", 16);\n    i0.ɵɵlistener(\"levelSelected\", function ClassroomComponent_Defer_12_ng_container_0_Template_app_classroom_level_filters_levelSelected_6_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onLevelChanged($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"app-classroom-status-filters\", 17);\n    i0.ɵɵlistener(\"switchToggled\", function ClassroomComponent_Defer_12_ng_container_0_Template_app_classroom_status_filters_switchToggled_7_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(8, ClassroomComponent_Defer_12_ng_container_0_ng_container_8_Template, 5, 6, \"ng-container\", 11)(9, ClassroomComponent_Defer_12_ng_container_0_ng_container_9_Template, 4, 2, \"ng-container\", 11)(10, ClassroomComponent_Defer_12_ng_container_0_div_10_Template, 2, 1, \"div\", 18)(11, ClassroomComponent_Defer_12_ng_container_0_div_11_Template, 3, 2, \"div\", 11)(12, ClassroomComponent_Defer_12_ng_container_0_div_12_Template, 2, 2, \"div\", 11);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showSectionTabs);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isTablet && ctx_r2.showFilters && ctx_r2.role == \"Teacher\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"size\", \"xs\")(\"showFirstTitle\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"isTrial\", ctx_r2.showTrials)(\"switches\", ctx_r2.switches);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.role === ctx_r2.UserRoles.TEACHER);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.role === ctx_r2.UserRoles.STUDENT && !ctx_r2.showTrials);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showTrials);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showClassrooms);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showStats);\n  }\n}\nfunction ClassroomComponent_Defer_12_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 45);\n    i0.ɵɵelement(2, \"app-loader\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scale\", 1.6);\n  }\n}\nfunction ClassroomComponent_Defer_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ClassroomComponent_Defer_12_ng_container_0_Template, 13, 11, \"ng-container\", 11)(1, ClassroomComponent_Defer_12_ng_container_1_Template, 3, 1, \"ng-container\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n  }\n}\nfunction ClassroomComponent_DeferPlaceholder_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClassroomComponent_ng_container_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 47)(2, \"div\", 48)(3, \"div\", 4);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 49);\n    i0.ɵɵelement(6, \"img\", 50);\n    i0.ɵɵtext(7, \" Ongoing Classrooms: \");\n    i0.ɵɵelementStart(8, \"b\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 49);\n    i0.ɵɵelement(11, \"img\", 51);\n    i0.ɵɵtext(12, \" Completed Classrooms: \");\n    i0.ɵɵelementStart(13, \"b\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 49);\n    i0.ɵɵelement(16, \"img\", 52);\n    i0.ɵɵtext(17, \" Expired Classrooms: \");\n    i0.ɵɵelementStart(18, \"b\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Total Classrooms: \", ctx_r2.getClassroomCounts().total, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getClassroomCounts().ongoing);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getClassroomCounts().completed);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getClassroomCounts().expired);\n  }\n}\nfunction ClassroomComponent_ng_container_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 47)(2, \"div\", 48)(3, \"div\", 4);\n    i0.ɵɵtext(4, \"Total Trials: \");\n    i0.ɵɵelementStart(5, \"b\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 49);\n    i0.ɵɵelement(8, \"img\", 53);\n    i0.ɵɵtext(9, \" Active Trials: \");\n    i0.ɵɵelementStart(10, \"b\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 49);\n    i0.ɵɵelement(13, \"img\", 54);\n    i0.ɵɵtext(14, \" Completed Trials: \");\n    i0.ɵɵelementStart(15, \"b\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 49);\n    i0.ɵɵelement(18, \"img\", 55);\n    i0.ɵɵtext(19, \" Dismissed Trials: \");\n    i0.ɵɵelementStart(20, \"b\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.getClassroomTrialCounts().total);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getClassroomTrialCounts().ongoing);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getClassroomTrialCounts().completed);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getClassroomTrialCounts().dismissed);\n  }\n}\nexport class ClassroomComponent {\n  constructor(generalService, modalService, classroomService, authService, layoutService, route, router, cdr, ConfirmDialogService) {\n    this.generalService = generalService;\n    this.modalService = modalService;\n    this.classroomService = classroomService;\n    this.authService = authService;\n    this.layoutService = layoutService;\n    this.route = route;\n    this.router = router;\n    this.cdr = cdr;\n    this.ConfirmDialogService = ConfirmDialogService;\n    this.level = \"All\";\n    this.status = \"All\";\n    this.domStatusID = \"all-kinds\";\n    this.isTablet = false;\n    this.showFilters = true;\n    this.classroomWrapper = {};\n    this.classroomScrollWrapper = {};\n    this.scrollHeight = '400px';\n    this.filteredClassrooms = [];\n    this.allClassrooms = [];\n    this.subs = new SubSink();\n    this.isLoading = true;\n    this.switches = {\n      All: false,\n      Ongoing: true,\n      Completed: false,\n      Expired: false,\n      Freeze: false,\n      Active: false,\n      Dismissed: false\n    };\n    this.role = {};\n    this.trials = [];\n    this.showTrials = false;\n    this.showClassrooms = true;\n    this.showStats = false;\n    this.showSectionTabs = true;\n    this.UserRoles = UserRole;\n  }\n  ngOnInit() {\n    this.showTrials = this.route.snapshot.url.some(segment => segment.path === 'trials');\n    if (this.showTrials) {\n      this.switches.All = true;\n      this.switches.Ongoing = false;\n    }\n    this.subs.add(this.route.params.subscribe(params => {\n      console.log(params);\n      if (params && params['in_page']) {\n        this.showSectionTabs = false;\n      }\n      // const inPage = params['in_page'];\n      // Use the value of 'in_page' parameter as needed\n    }));\n    this.role = this.authService.getUserRole();\n    this.subs.sink = this.generalService.deviceKind.subscribe(res => {\n      this.isTablet = res.is1024;\n    });\n    this.levelElements = document.getElementsByClassName(\"level\");\n    this.statusElements = document.getElementsByClassName(\"status\");\n    this.getUserClassrooms();\n    const extraHeight = {\n      wrapper: this.authService.isTeacher ? 100 : 45,\n      scrollWrapper: this.authService.isTeacher ? 200 : 155\n    };\n    this.subs.sink = this.generalService.deviceKind.pipe(switchMap(res => {\n      this.isTablet = res.is1024;\n      console.log(res);\n      if (res.is768) {\n        return of(0);\n      } else {\n        return this.layoutService.sideMenuHeight;\n      }\n    })).subscribe(res => {\n      if (res === 0) {\n        return;\n      }\n      if (this.authService.isTeacher) {\n        this.classroomScrollWrapper.nativeElement.style.height = res - extraHeight.wrapper + 'px';\n        this.scrollHeight = res - extraHeight.scrollWrapper + 'px';\n      } else {\n        this.classroomScrollWrapper.nativeElement.style.minHeight = res - extraHeight.wrapper + 'px';\n      }\n      // this.scrollHeight = (res - extraHeight.scrollWrapper + 'px');\n      this.cdr.detectChanges();\n    });\n  }\n  ngAfterContentInit() {\n    this.subs.sink = this.classroomService.classroomsLevelFilter.subscribe(res => {\n      this.level = res.level;\n      this.filterResults();\n    });\n    this.subs.sink = this.classroomService.classroomsStatusFilter.subscribe(res => {\n      this.status = res.status;\n      this.domStatusID = res.domStatusID;\n      this.filterResults();\n    });\n  }\n  ngAfterViewInit() {}\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  getUserClassrooms() {\n    this.isLoading = true;\n    // TODO: new endpoint check\n    // this.classroomService.getUserClassrooms(ClassroomType.PAID).pipe(take(1)).subscribe((res: Classroom[]) => {\n    //   this.filteredClassrooms = res\n    //   this.allClassrooms = res\n    //   this.classroomService.sessionUserClassrooms = res;\n    //   this.isLoading = false;\n    // })\n    this.subs.sink = this.classroomService.getLMSUserClassrooms(this.authService.getUserId()).pipe(take(1)).subscribe(res => {\n      this.isLoading = false;\n      this.allClassrooms = res;\n      this.filteredClassrooms = res;\n      console.log(this.filteredClassrooms);\n      if (this.showTrials) {\n        this.filteredClassrooms = this.filteredClassrooms.filter(classroom => classroom.type === 'Trial');\n      } else {\n        this.filteredClassrooms = this.filteredClassrooms.filter(classroom => classroom.type !== 'Trial');\n        // this.filterLessons();\n        // this.toggleSwitch('Ongoing');\n      }\n      this.allClassrooms = this.classroomService.sortClassroomsByFirstName(this.filteredClassrooms);\n      this.classroomService.sessionUserClassrooms = res;\n      if (!this.showTrials) {\n        this.getOngoingClassrooms().length === 0 ? '' : this.toggleSwitch('Ongoing');\n      } else {\n        this.getOngoingClassrooms().length === 0 ? '' : this.toggleSwitch('Active');\n      }\n    });\n  }\n  viewSection(section) {\n    if (section === 'classrooms') {\n      this.router.navigateByUrl('dashboard/classrooms', {\n        replaceUrl: true\n      });\n    } else if (section === 'stats') {\n      this.router.navigateByUrl('dashboard/classrooms/stats', {\n        replaceUrl: true\n      });\n    } else if (section === 'trials') {\n      this.router.navigateByUrl('dashboard/classrooms/trials', {\n        replaceUrl: true\n      });\n    }\n  }\n  filterLevel(eventTarget) {\n    this.classroomService.changeClassroomsLevelFilter({\n      level: eventTarget.attributes.level.nodeValue,\n      domLevelID: eventTarget.attributes.id.nodeValue\n    });\n  }\n  filterStatus(eventTarget, id) {\n    this.classroomService.changeClassroomsStatusFilter({\n      status: eventTarget.attributes.status.nodeValue,\n      domStatusID: eventTarget.attributes.id.nodeValue\n    });\n  }\n  changeFilterDomElementClass(id, targetId, className) {\n    if (id === targetId) {\n      document.getElementById(id)?.classList.add(className);\n    } else {\n      document.getElementById(id)?.classList.remove(className);\n    }\n  }\n  createModal() {\n    this.subs.sink = this.modalService.openModal(this.entry, this.levelElements, this.statusElements).subscribe(filters => {\n      this.classroomService.changeClassroomsLevelFilter({\n        level: filters.level,\n        domLevelID: filters.domLevelID\n      });\n      this.classroomService.changeClassroomsStatusFilter({\n        status: filters.status,\n        domStatusID: filters.domStatusID\n      });\n    });\n  }\n  getClassMenuItems(classroomId, userRole) {\n    const menuItems = [{\n      label: 'Class',\n      icon: '/assets/icons/classroom/classroom-group-icon.svg',\n      url: `/dashboard/classrooms/lessons/${classroomId}/details`,\n      role: [UserRole.STUDENT, UserRole.TEACHER]\n    }, {\n      label: 'Info',\n      icon: 'profile-menu-settings.svg',\n      url: `/dashboard/classrooms/lessons/${classroomId}/info`,\n      role: [UserRole.STUDENT, UserRole.TEACHER]\n    }\n    // {\n    //   label: 'Progress',\n    //   icon: '/assets/icons/classroom/progress.svg',\n    //   url: `/dashboard/classrooms/lessons/${classroomId}/progress`,\n    //   role: [UserRole.STUDENT],\n    // },\n    ];\n    return menuItems.filter(item => item.role.includes(userRole));\n  }\n  onLevelChanged(event) {\n    console.log(event);\n    this.level = event;\n    this.filterLessons();\n  }\n  toggleSwitch(key) {\n    console.log(key);\n    this.resetSwitches();\n    switch (key) {\n      case 'All':\n      case 'Ongoing':\n      case 'Active':\n      case 'Completed':\n      case 'Expired':\n      case 'Dismissed':\n      case 'Freeze':\n        this.switches[key] = !this.switches[key];\n        break;\n    }\n    this.status = key;\n    this.filterLessons();\n  }\n  resetSwitches() {\n    for (const key in this.switches) {\n      this.switches[key] = false;\n    }\n  }\n  filterResults() {\n    if (this.level !== 'All') {\n      this.filteredClassrooms = this.allClassrooms.filter(el => {\n        return el.activeLevel == this.level;\n      });\n    }\n    if (this.level === 'All') {\n      this.filteredClassrooms = this.allClassrooms;\n    }\n  }\n  /**\n  * Filters the list of lessons based on the selected level and status filters.\n  * @function\n  * @returns {void}\n  */\n  filterLessons() {\n    let levelFilter;\n    if (this.level !== 'All') {\n      levelFilter = lesson => lesson.activeLevel == this.level;\n    }\n    this.filteredClassrooms = this.allClassrooms.filter(lesson => {\n      if (this.switches.Ongoing) {\n        return lesson.status.toLowerCase().includes(Status.ON_GOING.toLowerCase()) && (!levelFilter || levelFilter(lesson));\n      }\n      if (this.switches.Completed) {\n        return lesson.status.toLowerCase().includes(Status.COMPLETED.toLowerCase()) && (!levelFilter || levelFilter(lesson));\n      }\n      if (this.switches.Expired) {\n        return lesson.status.toLowerCase().includes(Status.EXPIRED.toLowerCase()) && (!levelFilter || levelFilter(lesson));\n      }\n      if (this.switches.Freeze) {\n        return lesson.status.toLowerCase().includes(Status.ON_HOLD.toLowerCase()) && (!levelFilter || levelFilter(lesson));\n      }\n      if (this.switches.Active) {\n        console.log(lesson.status);\n        console.log(Status.ON_GOING.toLowerCase());\n        return lesson.status.toLowerCase().includes(LessonStatus.ARRANGED.toLowerCase()) || lesson.status.toLowerCase().includes(Status.PENDING.toLowerCase()) || lesson.status.toLowerCase().includes(Status.ON_GOING.toLowerCase()) && (!levelFilter || levelFilter(lesson));\n        // return lesson.lessons!.filter((lesson) => lesson.status.toLowerCase().includes(LessonStatus.ARRANGED)) && (!levelFilter || levelFilter(lesson));\n      }\n      if (this.switches.Dismissed) {\n        return lesson.status.toLowerCase().includes(Status.DISMISSED.toLowerCase()) && (!levelFilter || levelFilter(lesson));\n      }\n      if (this.switches.All) {\n        return true && (!levelFilter || levelFilter(lesson));\n      }\n      return false;\n    });\n  }\n  getClassroomCounts() {\n    const completedClassrooms = this.allClassrooms.filter(classroom => classroom.status.toLowerCase().includes('completed'));\n    const ongoingClassrooms = this.allClassrooms.filter(classroom => classroom.status.toLowerCase().includes('ongoing'));\n    const expiredClassrooms = this.allClassrooms.filter(classroom => classroom.status.toLowerCase().includes('expired'));\n    const totalClassrooms = this.allClassrooms.length;\n    return {\n      completed: completedClassrooms.length,\n      ongoing: ongoingClassrooms.length,\n      expired: expiredClassrooms.length,\n      total: totalClassrooms\n    };\n  }\n  getClassroomTrialCounts() {\n    const completedClassrooms = this.allClassrooms.filter(classroom => classroom.status.toLowerCase().includes(Status.COMPLETED.toLowerCase()));\n    const ongoingClassrooms = this.allClassrooms.filter(classroom => classroom.status.toLowerCase().includes(Status.ON_GOING.toLowerCase()));\n    const dismissedClassrooms = this.allClassrooms.filter(classroom => classroom.status.toLowerCase().includes('dismissed'));\n    const totalClassrooms = this.allClassrooms.length;\n    return {\n      completed: completedClassrooms.length,\n      ongoing: ongoingClassrooms.length,\n      dismissed: dismissedClassrooms.length,\n      total: totalClassrooms\n    };\n  }\n  getOngoingClassrooms() {\n    return this.allClassrooms.filter(classroom => classroom.status.toLowerCase().includes(Status.ON_GOING.toLowerCase()));\n  }\n  static #_ = this.ɵfac = function ClassroomComponent_Factory(t) {\n    return new (t || ClassroomComponent)(i0.ɵɵdirectiveInject(i1.GeneralService), i0.ɵɵdirectiveInject(i2.ModalService), i0.ɵɵdirectiveInject(i3.ClassroomService), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i5.LayoutService), i0.ɵɵdirectiveInject(i6.ActivatedRoute), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i7.ConfirmDialogService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ClassroomComponent,\n    selectors: [[\"app-classroom\"]],\n    viewQuery: function ClassroomComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5, ViewContainerRef);\n        i0.ɵɵviewQuery(_c1, 7);\n        i0.ɵɵviewQuery(_c2, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.entry = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.classroomWrapper = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.classroomScrollWrapper = _t.first);\n      }\n    },\n    decls: 18,\n    vars: 6,\n    consts: [[\"classroomWrapper\", \"\"], [\"classroomScrollWrapper\", \"\"], [1, \"card\", \"mb-2\"], [1, \"border-round-xl\"], [1, \"\"], [1, \"border-round-xl\", \"overflow-hidden\"], [1, \"block-header\", \"gradient-header\", \"relative\", \"justify-content-center\", \"lg:flex\", 3, \"ngClass\"], [1, \"sm:absolute\", \"md:pl-2\", \"left-0\"], [1, \"block-title\"], [1, \"text-0\", \"font-lg\"], [1, \"p-2\"], [4, \"ngIf\"], [\"class\", \"classrooms-header\", 4, \"ngIf\"], [\"class\", \"classrooms-choose-level-status\", 4, \"ngIf\"], [1, \"my-3\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"align-items-center\", \"gap-2\", \"justify-content-between\"], [3, \"levelSelected\", \"size\", \"showFirstTitle\"], [3, \"switchToggled\", \"isTrial\", \"switches\"], [\"id\", \"classrooms-trials\", 4, \"ngIf\"], [1, \"classrooms-header\"], [\"id\", \"classrooms\", 1, \"title\", \"flex\", \"align-items-center\", \"gap-2\", \"text-primary\", \"flex-row\", 3, \"click\"], [\"src\", \"/assets/icons/classroom/classroom-group-icon.svg\", \"height\", \"16\"], [1, \"seperator\"], [\"id\", \"trials\", \"class\", \"title flex align-items-center gap-2 text-primary flex-row\", 3, \"classrooms-header-active\", \"click\", 4, \"ngIf\"], [\"id\", \"stats\", \"class\", \"title flex align-items-center gap-2 text-primary flex-row disabled-link\", 3, \"click\", 4, \"ngIf\"], [\"id\", \"trials\", 1, \"title\", \"flex\", \"align-items-center\", \"gap-2\", \"text-primary\", \"flex-row\", 3, \"click\"], [\"src\", \"/assets/icons/paper-plane-blue.svg\", \"height\", \"16\"], [\"id\", \"stats\", 1, \"title\", \"flex\", \"align-items-center\", \"gap-2\", \"text-primary\", \"flex-row\", \"disabled-link\", 3, \"click\"], [\"src\", \"/assets/icons/classroom/progress.svg\", \"height\", \"16\"], [1, \"classrooms-choose-level-status\"], [\"styleClass\", \"custombar1\"], [1, \"grid\", \"justify-content-center\", \"sm:justify-content-start\"], [\"class\", \"class-card-col-width col-fixed sm:col-offset-0 sm:col-6 md:col-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"h-full\", \"align-items-center\", \"justify-content-center\", \"flex-column\"], [\"src\", \"/assets/icons/empty-classroom.png\", \"height\", \"280\"], [1, \"class-card-col-width\", \"col-fixed\", \"sm:col-offset-0\", \"sm:col-6\", \"md:col-3\"], [3, \"classroom\", \"isTrial\"], [1, \"m-1\"], [4, \"ngFor\", \"ngForOf\"], [\"color\", \"expired\", 3, \"classRoom\"], [\"id\", \"classrooms-trials\"], [1, \"m-1\", \"h-full\"], [\"color\", \"expired\", 1, \"sm:pb-2\", \"block\", 3, \"classRoom\"], [3, \"filteredClassrooms\"], [3, \"classroomsType\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\"], [3, \"scale\"], [1, \"card\", \"border-round-xl\", \"p-1\", \"font-xs\"], [1, \"p-2\", \"flex\", \"flex-column\", \"sm:flex-row\", \"align-items-center\", \"justify-content-between\"], [1, \"flex\", \"align-items-center\", \"gap-1\"], [\"src\", \"/assets/icons/classroom/blue-dark.svg\", \"width\", \"14\"], [\"src\", \"/assets/icons/classroom/blue-completed.svg\", \"width\", \"14\"], [\"src\", \"/assets/icons/classroom/expired.svg\", \"width\", \"14\"], [\"src\", \"/assets/icons/lessons/paper-plane-purple.svg\", \"width\", \"14\"], [\"src\", \"/assets/icons/lessons/completed-blue.svg\", \"width\", \"14\"], [\"src\", \"/assets/icons/lessons/dismissed-purple.svg\", \"width\", \"14\"]],\n    template: function ClassroomComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 2, 0)(2, \"div\", 3)(3, \"div\", 4)(4, \"div\", 5)(5, \"div\", 6);\n        i0.ɵɵelement(6, \"div\", 7);\n        i0.ɵɵelementStart(7, \"span\", 8)(8, \"span\", 9);\n        i0.ɵɵtext(9);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(10, \"div\", 10, 1);\n        i0.ɵɵtemplate(12, ClassroomComponent_Defer_12_Template, 2, 2)(13, ClassroomComponent_DeferPlaceholder_13_Template, 2, 0);\n        i0.ɵɵdefer(14, 12, ClassroomComponent_Defer_14_DepsFn, null, 13);\n        i0.ɵɵdeferOnViewport(0, -1);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(16, ClassroomComponent_ng_container_16_Template, 20, 4, \"ng-container\", 11)(17, ClassroomComponent_ng_container_17_Template, 22, 4, \"ng-container\", 11);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c3, ctx.showTrials));\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(!ctx.showTrials ? \"Classrooms\" : \"Trials\");\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngIf\", !ctx.showTrials && ctx.role === \"Teacher\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showTrials && ctx.role === \"Teacher\");\n      }\n    },\n    dependencies: [i8.NgClass, i8.NgIf],\n    styles: [\".gradient-header[_ngcontent-%COMP%] {\\n  background-image: url(\\\"/assets/images/classroom-gradient-bg.png\\\");\\n  background-repeat: no-repeat;\\n  background-position: center center;\\n  min-height: 50px;\\n  border-radius: 8px;\\n  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);\\n}\\n.gradient-header.trials-header[_ngcontent-%COMP%] {\\n  background-image: url(\\\"/assets/images/dashboard/freetrialheader.png\\\");\\n  background-size: cover;\\n  background-repeat: no-repeat;\\n  background-position: center;\\n}\\n\\n[_nghost-%COMP%]     .small-input-switch .p-inputswitch {\\n  height: 1rem;\\n  width: 1.8rem;\\n}\\n[_nghost-%COMP%]     .small-input-switch .p-inputswitch.p-inputswitch-checked .p-inputswitch-slider::before {\\n  transform: translateX(1rem);\\n  background-image: linear-gradient(90deg, hsl(229, 95%, 71%) 0%, hsl(230, 90%, 68%) 11%, hsl(230, 86%, 66%) 22%, hsl(230, 83%, 63%) 33%, hsl(231, 79%, 60%) 44%, hsl(231, 77%, 58%) 56%, hsl(231, 74%, 55%) 67%, hsl(230, 72%, 52%) 78%, hsl(230, 75%, 48%) 89%, hsl(227, 90%, 43%) 100%);\\n}\\n[_nghost-%COMP%]     .small-input-switch .p-inputswitch .p-inputswitch-slider {\\n  background-image: linear-gradient(90deg, hsl(227, 51%, 93%) 0%, hsl(227, 55%, 92%) 11%, hsl(227, 58%, 91%) 22%, hsl(228, 61%, 90%) 33%, hsl(228, 62%, 88%) 44%, hsl(228, 64%, 87%) 56%, hsl(228, 65%, 86%) 67%, hsl(228, 66%, 85%) 78%, hsl(228, 67%, 84%) 89%, hsl(228, 67%, 83%) 100%);\\n}\\n[_nghost-%COMP%]     .small-input-switch .p-inputswitch .p-inputswitch-slider::before {\\n  width: 0.8rem;\\n  height: 0.8rem;\\n  margin-top: -0.4rem;\\n}\\n\\n.classrooms[_ngcontent-%COMP%] {\\n  z-index: 1;\\n  padding: 30px;\\n  border-radius: 10px;\\n  background-color: var(--white);\\n  width: var(--page-width);\\n  box-sizing: border-box;\\n  min-height: 700px;\\n}\\n\\n@media screen and (max-width: 768px) {\\n  .classrooms[_ngcontent-%COMP%] {\\n    padding: 10px;\\n    width: var(--page-width);\\n  }\\n}\\n.seperator[_ngcontent-%COMP%] {\\n  height: 30px;\\n  width: 1px;\\n  background-color: var(--light-purple);\\n  margin: 0 30px;\\n}\\n\\n.classrooms-header[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  width: auto;\\n}\\n.classrooms-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  color: var(--main-color);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  padding-bottom: 10px;\\n  cursor: pointer;\\n}\\n.classrooms-header[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]:hover {\\n  font-weight: bold;\\n}\\n\\n.classrooms-header-active[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  border-bottom: 1px solid var(--main-color);\\n}\\n\\n.classrooms-kinds[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-top: 50px;\\n  font-size: 15px;\\n  color: var(--main-color);\\n  cursor: pointer;\\n}\\n.classrooms-kinds[_ngcontent-%COMP%]   .level[_ngcontent-%COMP%], .classrooms-kinds[_ngcontent-%COMP%]   .status[_ngcontent-%COMP%] {\\n  border: 1px solid var(--main-color);\\n  text-align: center;\\n  margin-left: 20px;\\n  transition: all 0.3s linear;\\n}\\n.classrooms-kinds[_ngcontent-%COMP%]   .level[_ngcontent-%COMP%] {\\n  width: 33px;\\n  height: 33px;\\n  line-height: 33px;\\n  border-radius: 50%;\\n}\\n@media screen and (max-width: 1366px) {\\n  .classrooms-kinds[_ngcontent-%COMP%]   .level[_ngcontent-%COMP%] {\\n    width: 25px;\\n    height: 25px;\\n    line-height: 25px;\\n    font-size: 13px;\\n  }\\n}\\n.classrooms-kinds[_ngcontent-%COMP%]   .status[_ngcontent-%COMP%] {\\n  padding: 7px;\\n  border-radius: 30px;\\n  width: 59px;\\n  font-size: 12px;\\n}\\n@media screen and (max-width: 1366px) {\\n  .classrooms-kinds[_ngcontent-%COMP%]   .status[_ngcontent-%COMP%] {\\n    width: 45px;\\n  }\\n}\\n\\n.level[_ngcontent-%COMP%]:hover, .status[_ngcontent-%COMP%]:hover {\\n  transition: all 0.03s ease;\\n  background-color: var(--main-color);\\n  color: var(--white);\\n}\\n\\n.classrooms-kinds-active[_ngcontent-%COMP%], .classrooms-status-active[_ngcontent-%COMP%] {\\n  transition: all 0.3s linear;\\n  background-color: var(--main-color);\\n  color: var(--white);\\n}\\n\\n.filters[_ngcontent-%COMP%] {\\n  margin-top: 30px;\\n  flex-direction: column;\\n}\\n\\n.display-mobile-filters[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n\\n.display-non-mobile-filters[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n\\n@media screen and (max-width: 1024px) {\\n  .display-non-mobile-filters[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .display-mobile-filters[_ngcontent-%COMP%] {\\n    display: flex;\\n  }\\n}\\n@media screen and (max-width: 576px) {\\n  .class-card-col-width[_ngcontent-%COMP%] {\\n    width: 250px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n    data: {\n      animation: [slideInOut]\n    }\n  });\n}", "map": {"version": 3, "names": ["ViewContainerRef", "of", "switchMap", "take", "Status", "LessonStatus", "UserRole", "slideInOut", "SubSink", "i0", "ɵɵelementStart", "ɵɵlistener", "ClassroomComponent_Defer_12_ng_container_0_div_1_div_5_Template_div_click_0_listener", "ɵɵrestoreView", "_r4", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "viewSection", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassProp", "router", "url", "includes", "ClassroomComponent_Defer_12_ng_container_0_div_1_div_6_Template_div_click_0_listener", "_r5", "ClassroomComponent_Defer_12_ng_container_0_div_1_Template_div_click_1_listener", "_r2", "ɵɵtemplate", "ClassroomComponent_Defer_12_ng_container_0_div_1_div_5_Template", "ClassroomComponent_Defer_12_ng_container_0_div_1_div_6_Template", "ɵɵadvance", "ɵɵproperty", "role", "ɵɵelementContainerStart", "ɵɵtextInterpolate1", "showTrials", "class_r6", "type", "ClassroomComponent_Defer_12_ng_container_0_ng_container_8_ng_container_2_Template", "ClassroomComponent_Defer_12_ng_container_0_ng_container_8_div_4_Template", "ɵɵstyleMap", "ɵɵpureFunction1", "_c4", "scrollHeight", "filteredClassrooms", "length", "class_r7", "ClassroomComponent_Defer_12_ng_container_0_ng_container_9_ng_container_2_Template", "ClassroomComponent_Defer_12_ng_container_0_ng_container_9_ng_container_3_Template", "trialClassroom_r8", "ClassroomComponent_Defer_12_ng_container_0_div_10_ng_container_1_ng_container_2_Template", "ClassroomComponent_Defer_12_ng_container_0_div_10_ng_container_1_ng_container_3_Template", "ClassroomComponent_Defer_12_ng_container_0_div_10_ng_container_1_Template", "UserRoles", "STUDENT", "ɵɵelementContainer", "undefined", "ClassroomComponent_Defer_12_ng_container_0_div_1_Template", "ClassroomComponent_Defer_12_ng_container_0_div_2_Template", "ClassroomComponent_Defer_12_ng_container_0_Template_app_classroom_level_filters_levelSelected_6_listener", "$event", "_r1", "onLevelChanged", "ClassroomComponent_Defer_12_ng_container_0_Template_app_classroom_status_filters_switchToggled_7_listener", "toggleSwitch", "ClassroomComponent_Defer_12_ng_container_0_ng_container_8_Template", "ClassroomComponent_Defer_12_ng_container_0_ng_container_9_Template", "ClassroomComponent_Defer_12_ng_container_0_div_10_Template", "ClassroomComponent_Defer_12_ng_container_0_div_11_Template", "ClassroomComponent_Defer_12_ng_container_0_div_12_Template", "showSectionTabs", "isTablet", "showFilters", "switches", "TEACHER", "showClassrooms", "showStats", "ClassroomComponent_Defer_12_ng_container_0_Template", "ClassroomComponent_Defer_12_ng_container_1_Template", "isLoading", "getClassroomCounts", "total", "ɵɵtextInterpolate", "ongoing", "completed", "expired", "getClassroomTrialCounts", "dismissed", "ClassroomComponent", "constructor", "generalService", "modalService", "classroomService", "authService", "layoutService", "route", "cdr", "ConfirmDialogService", "level", "status", "domStatusID", "classroomWrapper", "classroomScrollWrapper", "allClassrooms", "subs", "All", "Ongoing", "Completed", "Expired", "Freeze", "Active", "Dismissed", "trials", "ngOnInit", "snapshot", "some", "segment", "path", "add", "params", "subscribe", "console", "log", "getUserRole", "sink", "deviceKind", "res", "is1024", "levelElements", "document", "getElementsByClassName", "statusElements", "getUserClassrooms", "extraHeight", "wrapper", "<PERSON><PERSON><PERSON>er", "scrollWrapper", "pipe", "is768", "sideMenuHeight", "nativeElement", "style", "height", "minHeight", "detectChanges", "ngAfterContentInit", "classroomsLevelFilter", "filterResults", "classroomsStatusFilter", "ngAfterViewInit", "ngOnDestroy", "unsubscribe", "getLMSUserClassrooms", "getUserId", "filter", "classroom", "sortClassroomsByFirstName", "sessionUserClassrooms", "getOngoingClassrooms", "section", "navigateByUrl", "replaceUrl", "filterLevel", "eventTarget", "changeClassroomsLevelFilter", "attributes", "nodeValue", "domLevelID", "id", "filterStatus", "changeClassroomsStatusFilter", "changeFilterDomElementClass", "targetId", "className", "getElementById", "classList", "remove", "createModal", "openModal", "entry", "filters", "getClassMenuItems", "classroomId", "userRole", "menuItems", "label", "icon", "item", "event", "filterLessons", "key", "resetSwitches", "el", "activeLevel", "levelFilter", "lesson", "toLowerCase", "ON_GOING", "COMPLETED", "EXPIRED", "ON_HOLD", "ARRANGED", "PENDING", "DISMISSED", "completedClassrooms", "ongoingClassrooms", "expiredClassrooms", "totalClassrooms", "dismissedClassrooms", "_", "ɵɵdirectiveInject", "i1", "GeneralService", "i2", "ModalService", "i3", "ClassroomService", "i4", "AuthService", "i5", "LayoutService", "i6", "ActivatedRoute", "Router", "ChangeDetectorRef", "i7", "_2", "selectors", "viewQuery", "ClassroomComponent_Query", "rf", "ctx", "ClassroomComponent_Defer_12_Template", "ClassroomComponent_DeferPlaceholder_13_Template", "ɵɵdefer", "ClassroomComponent_Defer_14_DepsFn", "ɵɵdeferOnViewport", "ClassroomComponent_ng_container_16_Template", "ClassroomComponent_ng_container_17_Template", "_c3"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\classroom.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\classroom.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, ElementRef, OnInit, ViewChild, ViewContainerRef } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { of } from 'rxjs';\r\nimport { switchMap, take } from 'rxjs/operators';\r\nimport { Classroom, ClassroomsLevelFilter, ClassroomsStatusFilter, ClassroomType, Status } from 'src/app/core/models/classroom.model';\r\nimport { LessonStatus } from 'src/app/core/models/lesson.model';\r\nimport { UserRole } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { ConfirmDialogService } from 'src/app/core/services/confirm-dialog.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { LayoutService } from 'src/app/core/services/layout.service';\r\nimport { ModalService } from 'src/app/core/services/modal.service';\r\nimport { slideInOut } from 'src/app/helpers/my-animations';\r\nimport { SubSink } from 'subsink';\r\n\r\ninterface Switches {\r\n  [key: string]: boolean;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-classroom',\r\n  templateUrl: './classroom.component.html',\r\n  styleUrls: ['./classroom.component.scss'],\r\n  animations: [slideInOut]\r\n})\r\nexport class ClassroomComponent implements OnInit {\r\n  @ViewChild('filtersModal', { read: ViewContainerRef })\r\n  entry!: ViewContainerRef;\r\n  public level: string = \"All\";\r\n  public status: string = \"All\";\r\n  public domStatusID: string = \"all-kinds\";\r\n  public isTablet: boolean = false;\r\n  levelElements?: HTMLCollectionOf<Element>;\r\n  statusElements?: HTMLCollectionOf<Element>;\r\n  showFilters: boolean = true;\r\n  @ViewChild('classroomWrapper', { static: true }) classroomWrapper: ElementRef = {} as ElementRef;\r\n  @ViewChild('classroomScrollWrapper', { static: true }) classroomScrollWrapper: ElementRef = {} as ElementRef;\r\n  scrollHeight = '400px';\r\n\r\n  public filteredClassrooms: Classroom[] = [];\r\n  public allClassrooms: Classroom[] = [];\r\n  private subs = new SubSink();\r\n  public isLoading: boolean = true;\r\n  switches: Switches = {\r\n    All: false,\r\n    Ongoing: true,\r\n    Completed: false,\r\n    Expired: false,\r\n    Freeze: false,\r\n    Active: false,\r\n    Dismissed: false,\r\n  };\r\n\r\n  public role: UserRole = {} as UserRole\r\n  public trials: Classroom[] = [];\r\n  showTrials: boolean = false;\r\n  showClassrooms: boolean = true;\r\n  showStats: boolean = false;\r\n  showSectionTabs = true;\r\n  UserRoles = UserRole;\r\n  constructor(\r\n    private generalService: GeneralService,\r\n    private modalService: ModalService,\r\n    private classroomService: ClassroomService,\r\n    private authService: AuthService,\r\n    private layoutService: LayoutService,\r\n    private route: ActivatedRoute,\r\n    public router: Router,\r\n    public cdr: ChangeDetectorRef,\r\n    public ConfirmDialogService: ConfirmDialogService,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.showTrials = this.route.snapshot.url.some(segment => segment.path === 'trials');\r\n\r\n    if(this.showTrials) {\r\n      this.switches.All = true;\r\n      this.switches.Ongoing = false;\r\n    }\r\n\r\n    this.subs.add(this.route.params.subscribe(params => {\r\n      console.log(params);\r\n      if (params && params['in_page']) {\r\n        this.showSectionTabs = false;\r\n      }\r\n      // const inPage = params['in_page'];\r\n      // Use the value of 'in_page' parameter as needed\r\n    }));\r\n\r\n    this.role = this.authService.getUserRole();\r\n    this.subs.sink = this.generalService.deviceKind.subscribe(res => {\r\n      this.isTablet = res.is1024;\r\n    })\r\n    this.levelElements = document.getElementsByClassName(\"level\");\r\n    this.statusElements = document.getElementsByClassName(\"status\");\r\n    this.getUserClassrooms();\r\n    const extraHeight = {\r\n      wrapper: this.authService.isTeacher ? 100 : 45,\r\n      scrollWrapper: this.authService.isTeacher ? 200 : 155\r\n    }\r\n\r\n    this.subs.sink = this.generalService.deviceKind.pipe(\r\n      switchMap(res => {\r\n        this.isTablet = res.is1024;\r\n        console.log(res);\r\n        if (res.is768) {\r\n          return of(0);\r\n        } else {\r\n          return this.layoutService.sideMenuHeight;\r\n        }\r\n      })\r\n    ).subscribe((res) => {\r\n      if (res === 0) {\r\n        return;\r\n      }\r\n      if (this.authService.isTeacher) {\r\n        this.classroomScrollWrapper.nativeElement.style.height = (res - extraHeight.wrapper + 'px');\r\n        this.scrollHeight = (res - extraHeight.scrollWrapper + 'px');\r\n      } else {\r\n        this.classroomScrollWrapper.nativeElement.style.minHeight = (res - extraHeight.wrapper + 'px');\r\n      }\r\n      // this.scrollHeight = (res - extraHeight.scrollWrapper + 'px');\r\n      this.cdr.detectChanges();\r\n    });\r\n  }\r\n\r\n  ngAfterContentInit() {\r\n    this.subs.sink = this.classroomService.classroomsLevelFilter.subscribe((res: ClassroomsLevelFilter) => {\r\n      this.level = res.level;\r\n      this.filterResults();\r\n    })\r\n    this.subs.sink = this.classroomService.classroomsStatusFilter.subscribe((res: ClassroomsStatusFilter) => {\r\n      this.status = res.status;\r\n      this.domStatusID = res.domStatusID;\r\n      this.filterResults();\r\n    })\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  getUserClassrooms() {\r\n    this.isLoading = true;\r\n\r\n    // TODO: new endpoint check\r\n    // this.classroomService.getUserClassrooms(ClassroomType.PAID).pipe(take(1)).subscribe((res: Classroom[]) => {\r\n    //   this.filteredClassrooms = res\r\n    //   this.allClassrooms = res\r\n    //   this.classroomService.sessionUserClassrooms = res;\r\n    //   this.isLoading = false;\r\n    // })\r\n    this.subs.sink = this.classroomService.getLMSUserClassrooms(this.authService.getUserId()!).pipe(take(1)).subscribe((res: Classroom[]) => {\r\n      this.isLoading = false;\r\n      this.allClassrooms = res;\r\n      this.filteredClassrooms = res;\r\n      console.log(this.filteredClassrooms);\r\n      if (this.showTrials) {\r\n        this.filteredClassrooms = this.filteredClassrooms.filter(classroom => classroom.type === 'Trial');\r\n      } else {\r\n        this.filteredClassrooms = this.filteredClassrooms.filter(classroom => classroom.type !== 'Trial');\r\n        // this.filterLessons();\r\n        // this.toggleSwitch('Ongoing');\r\n      }\r\n\r\n      this.allClassrooms = this.classroomService.sortClassroomsByFirstName(this.filteredClassrooms);\r\n      this.classroomService.sessionUserClassrooms = res;\r\n      if (!this.showTrials) {\r\n        this.getOngoingClassrooms().length === 0 ? '' : this.toggleSwitch('Ongoing');\r\n      } else {\r\n        this.getOngoingClassrooms().length === 0 ? '' : this.toggleSwitch('Active');\r\n      }\r\n    });\r\n  }\r\n\r\n  viewSection(section: string) {\r\n    if (section === 'classrooms') {\r\n      this.router.navigateByUrl('dashboard/classrooms', { replaceUrl: true });\r\n    } else if (section === 'stats') {\r\n      this.router.navigateByUrl('dashboard/classrooms/stats', { replaceUrl: true });\r\n    } else if (section === 'trials') {\r\n      this.router.navigateByUrl('dashboard/classrooms/trials', { replaceUrl: true });\r\n    }\r\n  }\r\n\r\n  filterLevel(eventTarget: any) {\r\n    this.classroomService.changeClassroomsLevelFilter({ level: eventTarget.attributes.level.nodeValue, domLevelID: eventTarget.attributes.id.nodeValue })\r\n  }\r\n\r\n  filterStatus(eventTarget: any, id?: string) {\r\n    this.classroomService.changeClassroomsStatusFilter({ status: eventTarget.attributes.status.nodeValue, domStatusID: eventTarget.attributes.id.nodeValue })\r\n  }\r\n\r\n  changeFilterDomElementClass(id: string, targetId: string, className: string) {\r\n    if (id === targetId) {\r\n      document.getElementById(id)?.classList.add(className);\r\n    } else {\r\n      document.getElementById(id)?.classList.remove(className);\r\n    }\r\n  }\r\n\r\n  createModal() {\r\n    this.subs.sink = this.modalService\r\n      .openModal(this.entry, this.levelElements!, this.statusElements!)\r\n      .subscribe((filters: any) => {\r\n        this.classroomService.changeClassroomsLevelFilter({ level: filters.level, domLevelID: filters.domLevelID });\r\n        this.classroomService.changeClassroomsStatusFilter({ status: filters.status, domStatusID: filters.domStatusID });\r\n      });\r\n  }\r\n\r\n  getClassMenuItems(classroomId: string, userRole: UserRole): any[] {\r\n    const menuItems = [\r\n      {\r\n        label: 'Class',\r\n        icon: '/assets/icons/classroom/classroom-group-icon.svg',\r\n        url: `/dashboard/classrooms/lessons/${classroomId}/details`,\r\n        role: [UserRole.STUDENT, UserRole.TEACHER],\r\n      },\r\n      {\r\n        label: 'Info',\r\n        icon: 'profile-menu-settings.svg',\r\n        url: `/dashboard/classrooms/lessons/${classroomId}/info`,\r\n        role: [UserRole.STUDENT, UserRole.TEACHER],\r\n      },\r\n      // {\r\n      //   label: 'Progress',\r\n      //   icon: '/assets/icons/classroom/progress.svg',\r\n      //   url: `/dashboard/classrooms/lessons/${classroomId}/progress`,\r\n      //   role: [UserRole.STUDENT],\r\n      // },\r\n    ];\r\n  \r\n    return menuItems.filter(item => item.role.includes(userRole));\r\n  }\r\n\r\n  onLevelChanged(event: any) {\r\n    console.log(event);\r\n    this.level = event;\r\n    this.filterLessons();\r\n  }\r\n\r\n  toggleSwitch(key: string) {\r\n    console.log(key);\r\n    this.resetSwitches();\r\n    switch (key) {\r\n      case 'All':\r\n      case 'Ongoing':\r\n      case 'Active':\r\n      case 'Completed':\r\n      case 'Expired':\r\n      case 'Dismissed':\r\n      case 'Freeze':\r\n        this.switches[key] = !this.switches[key];\r\n        break;\r\n    }\r\n    this.status = key;\r\n    this.filterLessons();\r\n  }\r\n\r\n  private resetSwitches() {\r\n    for (const key in this.switches) {\r\n      this.switches[key] = false;\r\n    }\r\n  }\r\n\r\n  filterResults() {\r\n    if (this.level !== 'All') {\r\n      this.filteredClassrooms = this.allClassrooms.filter((el: Classroom) => { return el.activeLevel == this.level});\r\n    }\r\n    if (this.level === 'All') {\r\n      this.filteredClassrooms = this.allClassrooms;\r\n    }\r\n  }\r\n\r\n /**\r\n * Filters the list of lessons based on the selected level and status filters.\r\n * @function\r\n * @returns {void}\r\n */\r\n  filterLessons() {\r\n    let levelFilter: any;\r\n    if (this.level !== 'All') {\r\n      levelFilter = (lesson: any) => lesson.activeLevel == this.level;\r\n    }\r\n    this.filteredClassrooms = this.allClassrooms.filter(lesson => {\r\n      if (this.switches.Ongoing) {\r\n        return lesson.status.toLowerCase().includes(Status.ON_GOING.toLowerCase()) && (!levelFilter || levelFilter(lesson));\r\n      }\r\n      if (this.switches.Completed) {\r\n        return lesson.status.toLowerCase().includes(Status.COMPLETED.toLowerCase()) && (!levelFilter || levelFilter(lesson));\r\n      }\r\n      if (this.switches.Expired) {\r\n        return lesson.status.toLowerCase().includes(Status.EXPIRED.toLowerCase()) && (!levelFilter || levelFilter(lesson));\r\n      }\r\n      if (this.switches.Freeze) {\r\n        return lesson.status.toLowerCase().includes(Status.ON_HOLD.toLowerCase()) && (!levelFilter || levelFilter(lesson))\r\n      }\r\n      if (this.switches.Active) {\r\n        console.log(lesson.status);\r\n        console.log((Status.ON_GOING.toLowerCase()));\r\n        return lesson.status.toLowerCase().includes(LessonStatus.ARRANGED.toLowerCase()) || lesson.status.toLowerCase().includes(Status.PENDING.toLowerCase()) || lesson.status.toLowerCase().includes(Status.ON_GOING.toLowerCase()) && (!levelFilter || levelFilter(lesson));\r\n        // return lesson.lessons!.filter((lesson) => lesson.status.toLowerCase().includes(LessonStatus.ARRANGED)) && (!levelFilter || levelFilter(lesson));\r\n      }\r\n      if (this.switches.Dismissed) {\r\n        return lesson.status.toLowerCase().includes(Status.DISMISSED.toLowerCase()) && (!levelFilter || levelFilter(lesson));\r\n      }\r\n      if (this.switches.All) {\r\n        return true && (!levelFilter || levelFilter(lesson));\r\n      }\r\n      return false;\r\n    });\r\n  }\r\n\r\n  getClassroomCounts(): { completed: number, ongoing: number, expired: number, total: number } {\r\n    const completedClassrooms = this.allClassrooms.filter(classroom => classroom.status.toLowerCase().includes('completed'));\r\n    const ongoingClassrooms = this.allClassrooms.filter(classroom => classroom.status.toLowerCase().includes('ongoing'));\r\n    const expiredClassrooms = this.allClassrooms.filter(classroom => classroom.status.toLowerCase().includes('expired'));\r\n    const totalClassrooms = this.allClassrooms.length;\r\n    return { completed: completedClassrooms.length, ongoing: ongoingClassrooms.length, expired: expiredClassrooms.length, total: totalClassrooms };\r\n  }\r\n\r\n  getClassroomTrialCounts(): { completed: number, ongoing: number, dismissed: number, total: number } {\r\n    const completedClassrooms = this.allClassrooms.filter(classroom => classroom.status.toLowerCase().includes(Status.COMPLETED.toLowerCase()));\r\n    const ongoingClassrooms = this.allClassrooms.filter(classroom => classroom.status.toLowerCase().includes(Status.ON_GOING.toLowerCase()));\r\n    const dismissedClassrooms = this.allClassrooms.filter(classroom => classroom.status.toLowerCase().includes('dismissed'));\r\n    const totalClassrooms = this.allClassrooms.length;\r\n    return { completed: completedClassrooms.length, ongoing: ongoingClassrooms.length, dismissed: dismissedClassrooms.length, total: totalClassrooms };\r\n  }\r\n\r\n  getOngoingClassrooms() {\r\n    return this.allClassrooms.filter(classroom => classroom.status.toLowerCase().includes(Status.ON_GOING.toLowerCase()));\r\n  }\r\n\r\n}\r\n", "<div class=\"card mb-2\" #classroomWrapper>\r\n    <div class=\"border-round-xl\">\r\n        <div class=\"\">\r\n            <div class=\"border-round-xl  overflow-hidden\">\r\n                <div class=\"block-header gradient-header  relative justify-content-center  lg:flex\"\r\n                    [ngClass]=\"{'trials-header': showTrials}\">\r\n                    <div class=\"sm:absolute md:pl-2 left-0\">\r\n                    </div>\r\n                    <span class=\"block-title\">\r\n                        <span class=\"text-0 font-lg\">{{!showTrials ? 'Classrooms' : 'Trials' }}</span>\r\n                    </span>\r\n\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <div class=\"p-2\" #classroomScrollWrapper>\r\n        @defer(on viewport) {\r\n        <ng-container *ngIf=\"!isLoading\">\r\n            <div class=\"classrooms-header\" *ngIf=\"showSectionTabs\">\r\n                <div id=\"classrooms\" class=\"title flex align-items-center gap-2 text-primary flex-row\"\r\n                    [class.classrooms-header-active]=\"!router.url.includes('trials')\"\r\n                    (click)=\"viewSection('classrooms')\">\r\n                    <img src=\"/assets/icons/classroom/classroom-group-icon.svg\" height=\"16\" /> Classrooms\r\n                </div>\r\n                <div class=\"seperator\"></div>\r\n                <div *ngIf=\"role=='Student'\" [class.classrooms-header-active]=\"router.url.includes('trials')\"\r\n                    id=\"trials\" class=\"title flex align-items-center gap-2 text-primary flex-row\"\r\n                    (click)=\"viewSection('trials')\">\r\n                    <img src=\"/assets/icons/paper-plane-blue.svg\" height=\"16\" /> Trials\r\n                </div>\r\n                <!-- TODO: enable -->\r\n                <div *ngIf=\"role=='Teacher'\" id=\"stats\"\r\n                    class=\"title flex align-items-center gap-2 text-primary flex-row disabled-link\"\r\n                    (click)=\"viewSection('stats')\">\r\n                    <img src=\"/assets/icons/classroom/progress.svg\" height=\"16\" /> Stats\r\n                </div>\r\n            </div>\r\n            <div *ngIf=\"!isTablet && showFilters && role=='Teacher'\" class=\"classrooms-choose-level-status\">\r\n\r\n\r\n            </div>\r\n            <!-- <div *ngIf=\"isTablet && showFilters && role=='Teacher'\" class=\"centered filters\">\r\n            <div class=\"white-button\" (click)=\"createModal()\">\r\n                Filters\r\n            </div>\r\n            <div style=\"margin-top:30px\">\r\n                <strong>Level:</strong> {{level}} | <strong>Status:</strong> {{status}}\r\n            </div>\r\n            <div id=\"filtersModal\" #filtersModal></div>\r\n        </div> -->\r\n\r\n            <div class=\"my-3\">\r\n                <div class=\"flex flex-column md:flex-row  align-items-center gap-2 justify-content-between\">\r\n                    <div class=\"\">\r\n                        <app-classroom-level-filters [size]=\"'xs'\" (levelSelected)=\"onLevelChanged($event)\"\r\n                            [showFirstTitle]=\"false\"></app-classroom-level-filters>\r\n                    </div>\r\n\r\n                    <app-classroom-status-filters [isTrial]=\"showTrials\" [switches]=\"switches\"\r\n                        (switchToggled)=\"toggleSwitch($event)\"></app-classroom-status-filters>\r\n\r\n                </div>\r\n            </div>\r\n\r\n            <ng-container *ngIf=\"role === UserRoles.TEACHER\">\r\n                <p-scrollPanel [style]=\"{width: '100%', height: scrollHeight}\" styleClass=\"custombar1\">\r\n                    <ng-container *ngIf=\"filteredClassrooms.length === 0\">\r\n                        <div class=\"flex h-full align-items-center justify-content-center flex-column\">\r\n                            <strong>No {{showTrials ? 'trials': 'classrooms'}} for this section</strong>\r\n                            <img src=\"/assets/icons/empty-classroom.png\" height=\"280\">\r\n                        </div>\r\n                    </ng-container>\r\n                    <div class=\"grid justify-content-center sm:justify-content-start\">\r\n                        <div class=\"class-card-col-width col-fixed sm:col-offset-0 sm:col-6 md:col-3\"\r\n                            *ngFor=\"let class of filteredClassrooms\">\r\n                            <app-teacher-classroom-mini-card [classroom]=\"class\"\r\n                                [isTrial]=\"class.type === 'Trial'\"></app-teacher-classroom-mini-card>\r\n                        </div>\r\n                    </div>\r\n                </p-scrollPanel>\r\n            </ng-container>\r\n\r\n            <ng-container *ngIf=\"role === UserRoles.STUDENT && !showTrials\">\r\n                <div class=\"m-1\">\r\n                    <ng-container *ngIf=\"filteredClassrooms.length === 0\">\r\n                        <div class=\"flex h-full align-items-center justify-content-center flex-column\">\r\n                            <img src=\"/assets/icons/empty-classroom.png\" height=\"280\">\r\n                            <strong>No classrooms found.</strong>\r\n                        </div>\r\n                    </ng-container>\r\n                    <ng-container *ngFor=\"let class of filteredClassrooms\">\r\n                        <app-classroom-card [classRoom]=\"class\" color=\"expired\"></app-classroom-card>\r\n                    </ng-container>\r\n                </div>\r\n            </ng-container>\r\n\r\n            <div id=\"classrooms-trials\" *ngIf=\"showTrials\">\r\n\r\n                <ng-container *ngIf=\"role === UserRoles.STUDENT\">\r\n                    <div class=\"m-1  h-full\">\r\n                        <ng-container *ngIf=\"filteredClassrooms.length === 0\">\r\n                            <div class=\"flex h-full align-items-center justify-content-center flex-column\">\r\n                                <img src=\"/assets/icons/empty-classroom.png\" height=\"280\">\r\n                                <strong>No trials found.</strong>\r\n                            </div>\r\n                        </ng-container>\r\n                        <ng-container *ngFor=\"let trialClassroom of filteredClassrooms\">\r\n                            <app-classroom-card class=\"sm:pb-2 block\" [classRoom]=\"trialClassroom\"\r\n                                color=\"expired\"></app-classroom-card>\r\n                        </ng-container>\r\n                    </div>\r\n                </ng-container>\r\n                <!-- <app-buy-package [inTrial]=\"true\" [hideSection]=\"true\"></app-buy-package> -->\r\n                <!-- <app-classrooms [inTrial]=\"true\" [@slideInOut] [filteredClassrooms]=\"trials\"></app-classrooms> -->\r\n            </div>\r\n            <div *ngIf=\"showClassrooms\">\r\n                <app-classrooms [@slideInOut] [filteredClassrooms]=\"filteredClassrooms\"></app-classrooms>\r\n                <ng-container></ng-container>\r\n            </div>\r\n\r\n            <div *ngIf=\"showStats\">\r\n                <app-stats [@slideInOut] [classroomsType]=\"'Paid'\"></app-stats>\r\n            </div>\r\n\r\n        </ng-container>\r\n\r\n        <ng-container *ngIf=\"isLoading\">\r\n            <div class=\"flex align-items-center justify-content-center\">\r\n                <app-loader [scale]=\"1.6\"></app-loader>\r\n            </div>\r\n        </ng-container>\r\n        } @placeholder {\r\n        <span>loading...</span>\r\n        }\r\n    </div>\r\n</div>\r\n\r\n<ng-container *ngIf=\"!showTrials && role==='Teacher'\">\r\n    <div class=\"card border-round-xl p-1 font-xs\">\r\n        <div class=\"p-2 flex flex-column sm:flex-row align-items-center justify-content-between\">\r\n            <div class=\"\">Total Classrooms: {{ getClassroomCounts().total }}</div>\r\n            <div class=\"flex align-items-center gap-1\"> <img src=\"/assets/icons/classroom/blue-dark.svg\" width=\"14\" />\r\n                Ongoing Classrooms: <b>{{ getClassroomCounts().ongoing }}</b></div>\r\n            <div class=\"flex align-items-center gap-1\"><img src=\"/assets/icons/classroom/blue-completed.svg\"\r\n                    width=\"14\" />\r\n                Completed Classrooms: <b>{{ getClassroomCounts().completed }}</b></div>\r\n            <div class=\"flex align-items-center gap-1\"><img src=\"/assets/icons/classroom/expired.svg\" width=\"14\" />\r\n                Expired Classrooms: <b>{{ getClassroomCounts().expired }}</b></div>\r\n        </div>\r\n    </div>\r\n</ng-container>\r\n\r\n<ng-container *ngIf=\"showTrials && role==='Teacher'\">\r\n    <div class=\"card border-round-xl p-1 font-xs\">\r\n        <div class=\"p-2 flex flex-column sm:flex-row align-items-center justify-content-between\">\r\n            <div class=\"\">Total Trials: <b>{{ getClassroomTrialCounts().total }}</b></div>\r\n            <div class=\"flex align-items-center gap-1\"> <img src=\"/assets/icons/lessons/paper-plane-purple.svg\"\r\n                    width=\"14\" />\r\n                Active Trials: <b>{{ getClassroomTrialCounts().ongoing }}</b></div>\r\n            <div class=\"flex align-items-center gap-1\"><img src=\"/assets/icons/lessons/completed-blue.svg\" width=\"14\" />\r\n                Completed Trials: <b>{{ getClassroomTrialCounts().completed }}</b></div>\r\n            <div class=\"flex align-items-center gap-1\"><img src=\"/assets/icons/lessons/dismissed-purple.svg\"\r\n                    width=\"14\" />\r\n                Dismissed Trials: <b>{{ getClassroomTrialCounts().dismissed }}</b></div>\r\n            <!-- <div class=\"flex align-items-center gap-1\"><img src=\"/assets/icons/lessons/successful.svg\" width=\"14\" />\r\n                Expired Trials: <b>{{ getClassroomTrialCounts().expired }}</b></div> -->\r\n        </div>\r\n    </div>\r\n</ng-container>"], "mappings": "AAAA,SAAsEA,gBAAgB,QAAQ,eAAe;AAE7G,SAASC,EAAE,QAAQ,MAAM;AACzB,SAASC,SAAS,EAAEC,IAAI,QAAQ,gBAAgB;AAChD,SAAkFC,MAAM,QAAQ,qCAAqC;AACrI,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,SAASC,QAAQ,QAAQ,gCAAgC;AAOzD,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICYjBC,EAAA,CAAAC,cAAA,cAEoC;IAAhCD,EAAA,CAAAE,UAAA,mBAAAC,qFAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,CAAY,QAAQ,CAAC;IAAA,EAAC;IAC/BT,EAAA,CAAAU,SAAA,cAA4D;IAACV,EAAA,CAAAW,MAAA,eACjE;IAAAX,EAAA,CAAAY,YAAA,EAAM;;;;IAJuBZ,EAAA,CAAAa,WAAA,6BAAAP,MAAA,CAAAQ,MAAA,CAAAC,GAAA,CAAAC,QAAA,WAAgE;;;;;;IAM7FhB,EAAA,CAAAC,cAAA,cAEmC;IAA/BD,EAAA,CAAAE,UAAA,mBAAAe,qFAAA;MAAAjB,EAAA,CAAAI,aAAA,CAAAc,GAAA;MAAA,MAAAZ,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,CAAY,OAAO,CAAC;IAAA,EAAC;IAC9BT,EAAA,CAAAU,SAAA,cAA8D;IAACV,EAAA,CAAAW,MAAA,cACnE;IAAAX,EAAA,CAAAY,YAAA,EAAM;;;;;;IAhBNZ,EADJ,CAAAC,cAAA,cAAuD,cAGX;IAApCD,EAAA,CAAAE,UAAA,mBAAAiB,+EAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,WAAA,CAAY,YAAY,CAAC;IAAA,EAAC;IACnCT,EAAA,CAAAU,SAAA,cAA0E;IAACV,EAAA,CAAAW,MAAA,mBAC/E;IAAAX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAU,SAAA,cAA6B;IAO7BV,EANA,CAAAqB,UAAA,IAAAC,+DAAA,kBAEoC,IAAAC,+DAAA,kBAMD;IAGvCvB,EAAA,CAAAY,YAAA,EAAM;;;;IAhBEZ,EAAA,CAAAwB,SAAA,EAAiE;IAAjExB,EAAA,CAAAa,WAAA,8BAAAP,MAAA,CAAAQ,MAAA,CAAAC,GAAA,CAAAC,QAAA,WAAiE;IAK/DhB,EAAA,CAAAwB,SAAA,GAAqB;IAArBxB,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAAoB,IAAA,cAAqB;IAMrB1B,EAAA,CAAAwB,SAAA,EAAqB;IAArBxB,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAAoB,IAAA,cAAqB;;;;;IAM/B1B,EAAA,CAAAU,SAAA,cAGM;;;;;IA0BEV,EAAA,CAAA2B,uBAAA,GAAsD;IAE9C3B,EADJ,CAAAC,cAAA,cAA+E,aACnE;IAAAD,EAAA,CAAAW,MAAA,GAA2D;IAAAX,EAAA,CAAAY,YAAA,EAAS;IAC5EZ,EAAA,CAAAU,SAAA,cAA0D;IAC9DV,EAAA,CAAAY,YAAA,EAAM;;;;;IAFMZ,EAAA,CAAAwB,SAAA,GAA2D;IAA3DxB,EAAA,CAAA4B,kBAAA,QAAAtB,MAAA,CAAAuB,UAAA,gDAA2D;;;;;IAKvE7B,EAAA,CAAAC,cAAA,cAC6C;IACzCD,EAAA,CAAAU,SAAA,0CACyE;IAC7EV,EAAA,CAAAY,YAAA,EAAM;;;;IAF+BZ,EAAA,CAAAwB,SAAA,EAAmB;IAChDxB,EAD6B,CAAAyB,UAAA,cAAAK,QAAA,CAAmB,YAAAA,QAAA,CAAAC,IAAA,aACd;;;;;IAZtD/B,EAAA,CAAA2B,uBAAA,GAAiD;IAC7C3B,EAAA,CAAAC,cAAA,wBAAuF;IACnFD,EAAA,CAAAqB,UAAA,IAAAW,iFAAA,2BAAsD;IAMtDhC,EAAA,CAAAC,cAAA,cAAkE;IAC9DD,EAAA,CAAAqB,UAAA,IAAAY,wEAAA,kBAC6C;IAKrDjC,EADI,CAAAY,YAAA,EAAM,EACM;;;;;IAdDZ,EAAA,CAAAwB,SAAA,EAA+C;IAA/CxB,EAAA,CAAAkC,UAAA,CAAAlC,EAAA,CAAAmC,eAAA,IAAAC,GAAA,EAAA9B,MAAA,CAAA+B,YAAA,EAA+C;IAC3CrC,EAAA,CAAAwB,SAAA,EAAqC;IAArCxB,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAAgC,kBAAA,CAAAC,MAAA,OAAqC;IAQ1BvC,EAAA,CAAAwB,SAAA,GAAqB;IAArBxB,EAAA,CAAAyB,UAAA,YAAAnB,MAAA,CAAAgC,kBAAA,CAAqB;;;;;IAU/CtC,EAAA,CAAA2B,uBAAA,GAAsD;IAClD3B,EAAA,CAAAC,cAAA,cAA+E;IAC3ED,EAAA,CAAAU,SAAA,cAA0D;IAC1DV,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAW,MAAA,2BAAoB;IAChCX,EADgC,CAAAY,YAAA,EAAS,EACnC;;;;;;IAEVZ,EAAA,CAAA2B,uBAAA,GAAuD;IACnD3B,EAAA,CAAAU,SAAA,6BAA6E;;;;;IAAzDV,EAAA,CAAAwB,SAAA,EAAmB;IAAnBxB,EAAA,CAAAyB,UAAA,cAAAe,QAAA,CAAmB;;;;;IATnDxC,EAAA,CAAA2B,uBAAA,GAAgE;IAC5D3B,EAAA,CAAAC,cAAA,cAAiB;IAObD,EANA,CAAAqB,UAAA,IAAAoB,iFAAA,2BAAsD,IAAAC,iFAAA,2BAMC;IAG3D1C,EAAA,CAAAY,YAAA,EAAM;;;;;IATaZ,EAAA,CAAAwB,SAAA,GAAqC;IAArCxB,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAAgC,kBAAA,CAAAC,MAAA,OAAqC;IAMpBvC,EAAA,CAAAwB,SAAA,EAAqB;IAArBxB,EAAA,CAAAyB,UAAA,YAAAnB,MAAA,CAAAgC,kBAAA,CAAqB;;;;;IAUjDtC,EAAA,CAAA2B,uBAAA,GAAsD;IAClD3B,EAAA,CAAAC,cAAA,cAA+E;IAC3ED,EAAA,CAAAU,SAAA,cAA0D;IAC1DV,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAW,MAAA,uBAAgB;IAC5BX,EAD4B,CAAAY,YAAA,EAAS,EAC/B;;;;;;IAEVZ,EAAA,CAAA2B,uBAAA,GAAgE;IAC5D3B,EAAA,CAAAU,SAAA,6BACyC;;;;;IADCV,EAAA,CAAAwB,SAAA,EAA4B;IAA5BxB,EAAA,CAAAyB,UAAA,cAAAkB,iBAAA,CAA4B;;;;;IATlF3C,EAAA,CAAA2B,uBAAA,GAAiD;IAC7C3B,EAAA,CAAAC,cAAA,cAAyB;IAOrBD,EANA,CAAAqB,UAAA,IAAAuB,wFAAA,2BAAsD,IAAAC,wFAAA,2BAMU;IAIpE7C,EAAA,CAAAY,YAAA,EAAM;;;;;IAVaZ,EAAA,CAAAwB,SAAA,GAAqC;IAArCxB,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAAgC,kBAAA,CAAAC,MAAA,OAAqC;IAMXvC,EAAA,CAAAwB,SAAA,EAAqB;IAArBxB,EAAA,CAAAyB,UAAA,YAAAnB,MAAA,CAAAgC,kBAAA,CAAqB;;;;;IAV1EtC,EAAA,CAAAC,cAAA,cAA+C;IAE3CD,EAAA,CAAAqB,UAAA,IAAAyB,yEAAA,2BAAiD;IAgBrD9C,EAAA,CAAAY,YAAA,EAAM;;;;IAhBaZ,EAAA,CAAAwB,SAAA,EAAgC;IAAhCxB,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAAoB,IAAA,KAAApB,MAAA,CAAAyC,SAAA,CAAAC,OAAA,CAAgC;;;;;IAiBnDhD,EAAA,CAAAC,cAAA,UAA4B;IACxBD,EAAA,CAAAU,SAAA,yBAAyF;IACzFV,EAAA,CAAAiD,kBAAA,GAA6B;IACjCjD,EAAA,CAAAY,YAAA,EAAM;;;;IAFcZ,EAAA,CAAAwB,SAAA,EAAa;IAACxB,EAAd,CAAAyB,UAAA,gBAAAyB,SAAA,CAAa,uBAAA5C,MAAA,CAAAgC,kBAAA,CAA0C;;;;;IAI3EtC,EAAA,CAAAC,cAAA,UAAuB;IACnBD,EAAA,CAAAU,SAAA,oBAA+D;IACnEV,EAAA,CAAAY,YAAA,EAAM;;;IADSZ,EAAA,CAAAwB,SAAA,EAAa;IAACxB,EAAd,CAAAyB,UAAA,gBAAAyB,SAAA,CAAa,0BAA0B;;;;;;IAxG1DlD,EAAA,CAAA2B,uBAAA,GAAiC;IAoB7B3B,EAnBA,CAAAqB,UAAA,IAAA8B,yDAAA,kBAAuD,IAAAC,yDAAA,kBAmByC;IAiBpFpD,EAHZ,CAAAC,cAAA,cAAkB,cAC8E,aAC1E,sCAEmB;IADcD,EAAA,CAAAE,UAAA,2BAAAmD,yGAAAC,MAAA;MAAAtD,EAAA,CAAAI,aAAA,CAAAmD,GAAA;MAAA,MAAAjD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAiBF,MAAA,CAAAkD,cAAA,CAAAF,MAAA,CAAsB;IAAA,EAAC;IAEvFtD,EADiC,CAAAY,YAAA,EAA8B,EACzD;IAENZ,EAAA,CAAAC,cAAA,uCAC2C;IAAvCD,EAAA,CAAAE,UAAA,2BAAAuD,0GAAAH,MAAA;MAAAtD,EAAA,CAAAI,aAAA,CAAAmD,GAAA;MAAA,MAAAjD,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAiBF,MAAA,CAAAoD,YAAA,CAAAJ,MAAA,CAAoB;IAAA,EAAC;IAGlDtD,EAHmD,CAAAY,YAAA,EAA+B,EAExE,EACJ;IA0DNZ,EAxDA,CAAAqB,UAAA,IAAAsC,kEAAA,2BAAiD,IAAAC,kEAAA,2BAkBe,KAAAC,0DAAA,kBAcjB,KAAAC,0DAAA,kBAmBnB,KAAAC,0DAAA,kBAKL;;;;;IAtGS/D,EAAA,CAAAwB,SAAA,EAAqB;IAArBxB,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAA0D,eAAA,CAAqB;IAmB/ChE,EAAA,CAAAwB,SAAA,EAAiD;IAAjDxB,EAAA,CAAAyB,UAAA,UAAAnB,MAAA,CAAA2D,QAAA,IAAA3D,MAAA,CAAA4D,WAAA,IAAA5D,MAAA,CAAAoB,IAAA,cAAiD;IAiBd1B,EAAA,CAAAwB,SAAA,GAAa;IACtCxB,EADyB,CAAAyB,UAAA,cAAa,yBACd;IAGFzB,EAAA,CAAAwB,SAAA,EAAsB;IAACxB,EAAvB,CAAAyB,UAAA,YAAAnB,MAAA,CAAAuB,UAAA,CAAsB,aAAAvB,MAAA,CAAA6D,QAAA,CAAsB;IAMnEnE,EAAA,CAAAwB,SAAA,EAAgC;IAAhCxB,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAAoB,IAAA,KAAApB,MAAA,CAAAyC,SAAA,CAAAqB,OAAA,CAAgC;IAkBhCpE,EAAA,CAAAwB,SAAA,EAA+C;IAA/CxB,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAAoB,IAAA,KAAApB,MAAA,CAAAyC,SAAA,CAAAC,OAAA,KAAA1C,MAAA,CAAAuB,UAAA,CAA+C;IAcjC7B,EAAA,CAAAwB,SAAA,EAAgB;IAAhBxB,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAAuB,UAAA,CAAgB;IAmBvC7B,EAAA,CAAAwB,SAAA,EAAoB;IAApBxB,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAA+D,cAAA,CAAoB;IAKpBrE,EAAA,CAAAwB,SAAA,EAAe;IAAfxB,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAAgE,SAAA,CAAe;;;;;IAMzBtE,EAAA,CAAA2B,uBAAA,GAAgC;IAC5B3B,EAAA,CAAAC,cAAA,cAA4D;IACxDD,EAAA,CAAAU,SAAA,qBAAuC;IAC3CV,EAAA,CAAAY,YAAA,EAAM;;;;IADUZ,EAAA,CAAAwB,SAAA,GAAa;IAAbxB,EAAA,CAAAyB,UAAA,cAAa;;;;;IAFjCzB,EA7GA,CAAAqB,UAAA,IAAAkD,mDAAA,6BAAiC,IAAAC,mDAAA,2BA6GD;;;;IA7GjBxE,EAAA,CAAAyB,UAAA,UAAAnB,MAAA,CAAAmE,SAAA,CAAgB;IA6GhBzE,EAAA,CAAAwB,SAAA,EAAe;IAAfxB,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAAmE,SAAA,CAAe;;;;;IAM9BzE,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAW,MAAA,iBAAU;IAAAX,EAAA,CAAAY,YAAA,EAAO;;;;;IAK/BZ,EAAA,CAAA2B,uBAAA,GAAsD;IAG1C3B,EAFR,CAAAC,cAAA,cAA8C,cAC+C,aACvE;IAAAD,EAAA,CAAAW,MAAA,GAAkD;IAAAX,EAAA,CAAAY,YAAA,EAAM;IACtEZ,EAAA,CAAAC,cAAA,cAA2C;IAACD,EAAA,CAAAU,SAAA,cAA8D;IACtGV,EAAA,CAAAW,MAAA,4BAAoB;IAAAX,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAW,MAAA,GAAkC;IAAIX,EAAJ,CAAAY,YAAA,EAAI,EAAM;IACvEZ,EAAA,CAAAC,cAAA,eAA2C;IAAAD,EAAA,CAAAU,SAAA,eACtB;IACjBV,EAAA,CAAAW,MAAA,+BAAsB;IAAAX,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAW,MAAA,IAAoC;IAAIX,EAAJ,CAAAY,YAAA,EAAI,EAAM;IAC3EZ,EAAA,CAAAC,cAAA,eAA2C;IAAAD,EAAA,CAAAU,SAAA,eAA4D;IACnGV,EAAA,CAAAW,MAAA,6BAAoB;IAAAX,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAW,MAAA,IAAkC;IAErEX,EAFqE,CAAAY,YAAA,EAAI,EAAM,EACrE,EACJ;;;;;IATgBZ,EAAA,CAAAwB,SAAA,GAAkD;IAAlDxB,EAAA,CAAA4B,kBAAA,uBAAAtB,MAAA,CAAAoE,kBAAA,GAAAC,KAAA,KAAkD;IAErC3E,EAAA,CAAAwB,SAAA,GAAkC;IAAlCxB,EAAA,CAAA4E,iBAAA,CAAAtE,MAAA,CAAAoE,kBAAA,GAAAG,OAAA,CAAkC;IAGhC7E,EAAA,CAAAwB,SAAA,GAAoC;IAApCxB,EAAA,CAAA4E,iBAAA,CAAAtE,MAAA,CAAAoE,kBAAA,GAAAI,SAAA,CAAoC;IAEtC9E,EAAA,CAAAwB,SAAA,GAAkC;IAAlCxB,EAAA,CAAA4E,iBAAA,CAAAtE,MAAA,CAAAoE,kBAAA,GAAAK,OAAA,CAAkC;;;;;IAKzE/E,EAAA,CAAA2B,uBAAA,GAAqD;IAGzC3B,EAFR,CAAAC,cAAA,cAA8C,cAC+C,aACvE;IAAAD,EAAA,CAAAW,MAAA,qBAAc;IAAAX,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAW,MAAA,GAAqC;IAAIX,EAAJ,CAAAY,YAAA,EAAI,EAAM;IAC9EZ,EAAA,CAAAC,cAAA,cAA2C;IAACD,EAAA,CAAAU,SAAA,cACvB;IACjBV,EAAA,CAAAW,MAAA,uBAAe;IAAAX,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAW,MAAA,IAAuC;IAAIX,EAAJ,CAAAY,YAAA,EAAI,EAAM;IACvEZ,EAAA,CAAAC,cAAA,eAA2C;IAAAD,EAAA,CAAAU,SAAA,eAAiE;IACxGV,EAAA,CAAAW,MAAA,2BAAkB;IAAAX,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAW,MAAA,IAAyC;IAAIX,EAAJ,CAAAY,YAAA,EAAI,EAAM;IAC5EZ,EAAA,CAAAC,cAAA,eAA2C;IAAAD,EAAA,CAAAU,SAAA,eACtB;IACjBV,EAAA,CAAAW,MAAA,2BAAkB;IAAAX,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAW,MAAA,IAAyC;IAI1EX,EAJ0E,CAAAY,YAAA,EAAI,EAAM,EAG1E,EACJ;;;;;IAZiCZ,EAAA,CAAAwB,SAAA,GAAqC;IAArCxB,EAAA,CAAA4E,iBAAA,CAAAtE,MAAA,CAAA0E,uBAAA,GAAAL,KAAA,CAAqC;IAG9C3E,EAAA,CAAAwB,SAAA,GAAuC;IAAvCxB,EAAA,CAAA4E,iBAAA,CAAAtE,MAAA,CAAA0E,uBAAA,GAAAH,OAAA,CAAuC;IAEpC7E,EAAA,CAAAwB,SAAA,GAAyC;IAAzCxB,EAAA,CAAA4E,iBAAA,CAAAtE,MAAA,CAAA0E,uBAAA,GAAAF,SAAA,CAAyC;IAGzC9E,EAAA,CAAAwB,SAAA,GAAyC;IAAzCxB,EAAA,CAAA4E,iBAAA,CAAAtE,MAAA,CAAA0E,uBAAA,GAAAC,SAAA,CAAyC;;;AD1I9E,OAAM,MAAOC,kBAAkB;EAmC7BC,YACUC,cAA8B,EAC9BC,YAA0B,EAC1BC,gBAAkC,EAClCC,WAAwB,EACxBC,aAA4B,EAC5BC,KAAqB,EACtB3E,MAAc,EACd4E,GAAsB,EACtBC,oBAA0C;IARzC,KAAAP,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,KAAK,GAALA,KAAK;IACN,KAAA3E,MAAM,GAANA,MAAM;IACN,KAAA4E,GAAG,GAAHA,GAAG;IACH,KAAAC,oBAAoB,GAApBA,oBAAoB;IAzCtB,KAAAC,KAAK,GAAW,KAAK;IACrB,KAAAC,MAAM,GAAW,KAAK;IACtB,KAAAC,WAAW,GAAW,WAAW;IACjC,KAAA7B,QAAQ,GAAY,KAAK;IAGhC,KAAAC,WAAW,GAAY,IAAI;IACsB,KAAA6B,gBAAgB,GAAe,EAAgB;IACzC,KAAAC,sBAAsB,GAAe,EAAgB;IAC5G,KAAA3D,YAAY,GAAG,OAAO;IAEf,KAAAC,kBAAkB,GAAgB,EAAE;IACpC,KAAA2D,aAAa,GAAgB,EAAE;IAC9B,KAAAC,IAAI,GAAG,IAAInG,OAAO,EAAE;IACrB,KAAA0E,SAAS,GAAY,IAAI;IAChC,KAAAN,QAAQ,GAAa;MACnBgC,GAAG,EAAE,KAAK;MACVC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,KAAK;MAChBC,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE,KAAK;MACbC,SAAS,EAAE;KACZ;IAEM,KAAA/E,IAAI,GAAa,EAAc;IAC/B,KAAAgF,MAAM,GAAgB,EAAE;IAC/B,KAAA7E,UAAU,GAAY,KAAK;IAC3B,KAAAwC,cAAc,GAAY,IAAI;IAC9B,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAN,eAAe,GAAG,IAAI;IACtB,KAAAjB,SAAS,GAAGlD,QAAQ;EAWhB;EAEJ8G,QAAQA,CAAA;IACN,IAAI,CAAC9E,UAAU,GAAG,IAAI,CAAC4D,KAAK,CAACmB,QAAQ,CAAC7F,GAAG,CAAC8F,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACC,IAAI,KAAK,QAAQ,CAAC;IAEpF,IAAG,IAAI,CAAClF,UAAU,EAAE;MAClB,IAAI,CAACsC,QAAQ,CAACgC,GAAG,GAAG,IAAI;MACxB,IAAI,CAAChC,QAAQ,CAACiC,OAAO,GAAG,KAAK;IAC/B;IAEA,IAAI,CAACF,IAAI,CAACc,GAAG,CAAC,IAAI,CAACvB,KAAK,CAACwB,MAAM,CAACC,SAAS,CAACD,MAAM,IAAG;MACjDE,OAAO,CAACC,GAAG,CAACH,MAAM,CAAC;MACnB,IAAIA,MAAM,IAAIA,MAAM,CAAC,SAAS,CAAC,EAAE;QAC/B,IAAI,CAACjD,eAAe,GAAG,KAAK;MAC9B;MACA;MACA;IACF,CAAC,CAAC,CAAC;IAEH,IAAI,CAACtC,IAAI,GAAG,IAAI,CAAC6D,WAAW,CAAC8B,WAAW,EAAE;IAC1C,IAAI,CAACnB,IAAI,CAACoB,IAAI,GAAG,IAAI,CAAClC,cAAc,CAACmC,UAAU,CAACL,SAAS,CAACM,GAAG,IAAG;MAC9D,IAAI,CAACvD,QAAQ,GAAGuD,GAAG,CAACC,MAAM;IAC5B,CAAC,CAAC;IACF,IAAI,CAACC,aAAa,GAAGC,QAAQ,CAACC,sBAAsB,CAAC,OAAO,CAAC;IAC7D,IAAI,CAACC,cAAc,GAAGF,QAAQ,CAACC,sBAAsB,CAAC,QAAQ,CAAC;IAC/D,IAAI,CAACE,iBAAiB,EAAE;IACxB,MAAMC,WAAW,GAAG;MAClBC,OAAO,EAAE,IAAI,CAACzC,WAAW,CAAC0C,SAAS,GAAG,GAAG,GAAG,EAAE;MAC9CC,aAAa,EAAE,IAAI,CAAC3C,WAAW,CAAC0C,SAAS,GAAG,GAAG,GAAG;KACnD;IAED,IAAI,CAAC/B,IAAI,CAACoB,IAAI,GAAG,IAAI,CAAClC,cAAc,CAACmC,UAAU,CAACY,IAAI,CAClD1I,SAAS,CAAC+H,GAAG,IAAG;MACd,IAAI,CAACvD,QAAQ,GAAGuD,GAAG,CAACC,MAAM;MAC1BN,OAAO,CAACC,GAAG,CAACI,GAAG,CAAC;MAChB,IAAIA,GAAG,CAACY,KAAK,EAAE;QACb,OAAO5I,EAAE,CAAC,CAAC,CAAC;MACd,CAAC,MAAM;QACL,OAAO,IAAI,CAACgG,aAAa,CAAC6C,cAAc;MAC1C;IACF,CAAC,CAAC,CACH,CAACnB,SAAS,CAAEM,GAAG,IAAI;MAClB,IAAIA,GAAG,KAAK,CAAC,EAAE;QACb;MACF;MACA,IAAI,IAAI,CAACjC,WAAW,CAAC0C,SAAS,EAAE;QAC9B,IAAI,CAACjC,sBAAsB,CAACsC,aAAa,CAACC,KAAK,CAACC,MAAM,GAAIhB,GAAG,GAAGO,WAAW,CAACC,OAAO,GAAG,IAAK;QAC3F,IAAI,CAAC3F,YAAY,GAAImF,GAAG,GAAGO,WAAW,CAACG,aAAa,GAAG,IAAK;MAC9D,CAAC,MAAM;QACL,IAAI,CAAClC,sBAAsB,CAACsC,aAAa,CAACC,KAAK,CAACE,SAAS,GAAIjB,GAAG,GAAGO,WAAW,CAACC,OAAO,GAAG,IAAK;MAChG;MACA;MACA,IAAI,CAACtC,GAAG,CAACgD,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAC,kBAAkBA,CAAA;IAChB,IAAI,CAACzC,IAAI,CAACoB,IAAI,GAAG,IAAI,CAAChC,gBAAgB,CAACsD,qBAAqB,CAAC1B,SAAS,CAAEM,GAA0B,IAAI;MACpG,IAAI,CAAC5B,KAAK,GAAG4B,GAAG,CAAC5B,KAAK;MACtB,IAAI,CAACiD,aAAa,EAAE;IACtB,CAAC,CAAC;IACF,IAAI,CAAC3C,IAAI,CAACoB,IAAI,GAAG,IAAI,CAAChC,gBAAgB,CAACwD,sBAAsB,CAAC5B,SAAS,CAAEM,GAA2B,IAAI;MACtG,IAAI,CAAC3B,MAAM,GAAG2B,GAAG,CAAC3B,MAAM;MACxB,IAAI,CAACC,WAAW,GAAG0B,GAAG,CAAC1B,WAAW;MAClC,IAAI,CAAC+C,aAAa,EAAE;IACtB,CAAC,CAAC;EACJ;EAEAE,eAAeA,CAAA,GACf;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC9C,IAAI,CAAC+C,WAAW,EAAE;EACzB;EAEAnB,iBAAiBA,CAAA;IACf,IAAI,CAACrD,SAAS,GAAG,IAAI;IAErB;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACyB,IAAI,CAACoB,IAAI,GAAG,IAAI,CAAChC,gBAAgB,CAAC4D,oBAAoB,CAAC,IAAI,CAAC3D,WAAW,CAAC4D,SAAS,EAAG,CAAC,CAAChB,IAAI,CAACzI,IAAI,CAAC,CAAC,CAAC,CAAC,CAACwH,SAAS,CAAEM,GAAgB,IAAI;MACtI,IAAI,CAAC/C,SAAS,GAAG,KAAK;MACtB,IAAI,CAACwB,aAAa,GAAGuB,GAAG;MACxB,IAAI,CAAClF,kBAAkB,GAAGkF,GAAG;MAC7BL,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC9E,kBAAkB,CAAC;MACpC,IAAI,IAAI,CAACT,UAAU,EAAE;QACnB,IAAI,CAACS,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAAC8G,MAAM,CAACC,SAAS,IAAIA,SAAS,CAACtH,IAAI,KAAK,OAAO,CAAC;MACnG,CAAC,MAAM;QACL,IAAI,CAACO,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAAC8G,MAAM,CAACC,SAAS,IAAIA,SAAS,CAACtH,IAAI,KAAK,OAAO,CAAC;QACjG;QACA;MACF;MAEA,IAAI,CAACkE,aAAa,GAAG,IAAI,CAACX,gBAAgB,CAACgE,yBAAyB,CAAC,IAAI,CAAChH,kBAAkB,CAAC;MAC7F,IAAI,CAACgD,gBAAgB,CAACiE,qBAAqB,GAAG/B,GAAG;MACjD,IAAI,CAAC,IAAI,CAAC3F,UAAU,EAAE;QACpB,IAAI,CAAC2H,oBAAoB,EAAE,CAACjH,MAAM,KAAK,CAAC,GAAG,EAAE,GAAG,IAAI,CAACmB,YAAY,CAAC,SAAS,CAAC;MAC9E,CAAC,MAAM;QACL,IAAI,CAAC8F,oBAAoB,EAAE,CAACjH,MAAM,KAAK,CAAC,GAAG,EAAE,GAAG,IAAI,CAACmB,YAAY,CAAC,QAAQ,CAAC;MAC7E;IACF,CAAC,CAAC;EACJ;EAEAjD,WAAWA,CAACgJ,OAAe;IACzB,IAAIA,OAAO,KAAK,YAAY,EAAE;MAC5B,IAAI,CAAC3I,MAAM,CAAC4I,aAAa,CAAC,sBAAsB,EAAE;QAAEC,UAAU,EAAE;MAAI,CAAE,CAAC;IACzE,CAAC,MAAM,IAAIF,OAAO,KAAK,OAAO,EAAE;MAC9B,IAAI,CAAC3I,MAAM,CAAC4I,aAAa,CAAC,4BAA4B,EAAE;QAAEC,UAAU,EAAE;MAAI,CAAE,CAAC;IAC/E,CAAC,MAAM,IAAIF,OAAO,KAAK,QAAQ,EAAE;MAC/B,IAAI,CAAC3I,MAAM,CAAC4I,aAAa,CAAC,6BAA6B,EAAE;QAAEC,UAAU,EAAE;MAAI,CAAE,CAAC;IAChF;EACF;EAEAC,WAAWA,CAACC,WAAgB;IAC1B,IAAI,CAACvE,gBAAgB,CAACwE,2BAA2B,CAAC;MAAElE,KAAK,EAAEiE,WAAW,CAACE,UAAU,CAACnE,KAAK,CAACoE,SAAS;MAAEC,UAAU,EAAEJ,WAAW,CAACE,UAAU,CAACG,EAAE,CAACF;IAAS,CAAE,CAAC;EACvJ;EAEAG,YAAYA,CAACN,WAAgB,EAAEK,EAAW;IACxC,IAAI,CAAC5E,gBAAgB,CAAC8E,4BAA4B,CAAC;MAAEvE,MAAM,EAAEgE,WAAW,CAACE,UAAU,CAAClE,MAAM,CAACmE,SAAS;MAAElE,WAAW,EAAE+D,WAAW,CAACE,UAAU,CAACG,EAAE,CAACF;IAAS,CAAE,CAAC;EAC3J;EAEAK,2BAA2BA,CAACH,EAAU,EAAEI,QAAgB,EAAEC,SAAiB;IACzE,IAAIL,EAAE,KAAKI,QAAQ,EAAE;MACnB3C,QAAQ,CAAC6C,cAAc,CAACN,EAAE,CAAC,EAAEO,SAAS,CAACzD,GAAG,CAACuD,SAAS,CAAC;IACvD,CAAC,MAAM;MACL5C,QAAQ,CAAC6C,cAAc,CAACN,EAAE,CAAC,EAAEO,SAAS,CAACC,MAAM,CAACH,SAAS,CAAC;IAC1D;EACF;EAEAI,WAAWA,CAAA;IACT,IAAI,CAACzE,IAAI,CAACoB,IAAI,GAAG,IAAI,CAACjC,YAAY,CAC/BuF,SAAS,CAAC,IAAI,CAACC,KAAK,EAAE,IAAI,CAACnD,aAAc,EAAE,IAAI,CAACG,cAAe,CAAC,CAChEX,SAAS,CAAE4D,OAAY,IAAI;MAC1B,IAAI,CAACxF,gBAAgB,CAACwE,2BAA2B,CAAC;QAAElE,KAAK,EAAEkF,OAAO,CAAClF,KAAK;QAAEqE,UAAU,EAAEa,OAAO,CAACb;MAAU,CAAE,CAAC;MAC3G,IAAI,CAAC3E,gBAAgB,CAAC8E,4BAA4B,CAAC;QAAEvE,MAAM,EAAEiF,OAAO,CAACjF,MAAM;QAAEC,WAAW,EAAEgF,OAAO,CAAChF;MAAW,CAAE,CAAC;IAClH,CAAC,CAAC;EACN;EAEAiF,iBAAiBA,CAACC,WAAmB,EAAEC,QAAkB;IACvD,MAAMC,SAAS,GAAG,CAChB;MACEC,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE,kDAAkD;MACxDrK,GAAG,EAAE,iCAAiCiK,WAAW,UAAU;MAC3DtJ,IAAI,EAAE,CAAC7B,QAAQ,CAACmD,OAAO,EAAEnD,QAAQ,CAACuE,OAAO;KAC1C,EACD;MACE+G,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE,2BAA2B;MACjCrK,GAAG,EAAE,iCAAiCiK,WAAW,OAAO;MACxDtJ,IAAI,EAAE,CAAC7B,QAAQ,CAACmD,OAAO,EAAEnD,QAAQ,CAACuE,OAAO;;IAE3C;IACA;IACA;IACA;IACA;IACA;IAAA,CACD;IAED,OAAO8G,SAAS,CAAC9B,MAAM,CAACiC,IAAI,IAAIA,IAAI,CAAC3J,IAAI,CAACV,QAAQ,CAACiK,QAAQ,CAAC,CAAC;EAC/D;EAEAzH,cAAcA,CAAC8H,KAAU;IACvBnE,OAAO,CAACC,GAAG,CAACkE,KAAK,CAAC;IAClB,IAAI,CAAC1F,KAAK,GAAG0F,KAAK;IAClB,IAAI,CAACC,aAAa,EAAE;EACtB;EAEA7H,YAAYA,CAAC8H,GAAW;IACtBrE,OAAO,CAACC,GAAG,CAACoE,GAAG,CAAC;IAChB,IAAI,CAACC,aAAa,EAAE;IACpB,QAAQD,GAAG;MACT,KAAK,KAAK;MACV,KAAK,SAAS;MACd,KAAK,QAAQ;MACb,KAAK,WAAW;MAChB,KAAK,SAAS;MACd,KAAK,WAAW;MAChB,KAAK,QAAQ;QACX,IAAI,CAACrH,QAAQ,CAACqH,GAAG,CAAC,GAAG,CAAC,IAAI,CAACrH,QAAQ,CAACqH,GAAG,CAAC;QACxC;IACJ;IACA,IAAI,CAAC3F,MAAM,GAAG2F,GAAG;IACjB,IAAI,CAACD,aAAa,EAAE;EACtB;EAEQE,aAAaA,CAAA;IACnB,KAAK,MAAMD,GAAG,IAAI,IAAI,CAACrH,QAAQ,EAAE;MAC/B,IAAI,CAACA,QAAQ,CAACqH,GAAG,CAAC,GAAG,KAAK;IAC5B;EACF;EAEA3C,aAAaA,CAAA;IACX,IAAI,IAAI,CAACjD,KAAK,KAAK,KAAK,EAAE;MACxB,IAAI,CAACtD,kBAAkB,GAAG,IAAI,CAAC2D,aAAa,CAACmD,MAAM,CAAEsC,EAAa,IAAI;QAAG,OAAOA,EAAE,CAACC,WAAW,IAAI,IAAI,CAAC/F,KAAK;MAAA,CAAC,CAAC;IAChH;IACA,IAAI,IAAI,CAACA,KAAK,KAAK,KAAK,EAAE;MACxB,IAAI,CAACtD,kBAAkB,GAAG,IAAI,CAAC2D,aAAa;IAC9C;EACF;EAED;;;;;EAKCsF,aAAaA,CAAA;IACX,IAAIK,WAAgB;IACpB,IAAI,IAAI,CAAChG,KAAK,KAAK,KAAK,EAAE;MACxBgG,WAAW,GAAIC,MAAW,IAAKA,MAAM,CAACF,WAAW,IAAI,IAAI,CAAC/F,KAAK;IACjE;IACA,IAAI,CAACtD,kBAAkB,GAAG,IAAI,CAAC2D,aAAa,CAACmD,MAAM,CAACyC,MAAM,IAAG;MAC3D,IAAI,IAAI,CAAC1H,QAAQ,CAACiC,OAAO,EAAE;QACzB,OAAOyF,MAAM,CAAChG,MAAM,CAACiG,WAAW,EAAE,CAAC9K,QAAQ,CAACrB,MAAM,CAACoM,QAAQ,CAACD,WAAW,EAAE,CAAC,KAAK,CAACF,WAAW,IAAIA,WAAW,CAACC,MAAM,CAAC,CAAC;MACrH;MACA,IAAI,IAAI,CAAC1H,QAAQ,CAACkC,SAAS,EAAE;QAC3B,OAAOwF,MAAM,CAAChG,MAAM,CAACiG,WAAW,EAAE,CAAC9K,QAAQ,CAACrB,MAAM,CAACqM,SAAS,CAACF,WAAW,EAAE,CAAC,KAAK,CAACF,WAAW,IAAIA,WAAW,CAACC,MAAM,CAAC,CAAC;MACtH;MACA,IAAI,IAAI,CAAC1H,QAAQ,CAACmC,OAAO,EAAE;QACzB,OAAOuF,MAAM,CAAChG,MAAM,CAACiG,WAAW,EAAE,CAAC9K,QAAQ,CAACrB,MAAM,CAACsM,OAAO,CAACH,WAAW,EAAE,CAAC,KAAK,CAACF,WAAW,IAAIA,WAAW,CAACC,MAAM,CAAC,CAAC;MACpH;MACA,IAAI,IAAI,CAAC1H,QAAQ,CAACoC,MAAM,EAAE;QACxB,OAAOsF,MAAM,CAAChG,MAAM,CAACiG,WAAW,EAAE,CAAC9K,QAAQ,CAACrB,MAAM,CAACuM,OAAO,CAACJ,WAAW,EAAE,CAAC,KAAK,CAACF,WAAW,IAAIA,WAAW,CAACC,MAAM,CAAC,CAAC;MACpH;MACA,IAAI,IAAI,CAAC1H,QAAQ,CAACqC,MAAM,EAAE;QACxBW,OAAO,CAACC,GAAG,CAACyE,MAAM,CAAChG,MAAM,CAAC;QAC1BsB,OAAO,CAACC,GAAG,CAAEzH,MAAM,CAACoM,QAAQ,CAACD,WAAW,EAAG,CAAC;QAC5C,OAAOD,MAAM,CAAChG,MAAM,CAACiG,WAAW,EAAE,CAAC9K,QAAQ,CAACpB,YAAY,CAACuM,QAAQ,CAACL,WAAW,EAAE,CAAC,IAAID,MAAM,CAAChG,MAAM,CAACiG,WAAW,EAAE,CAAC9K,QAAQ,CAACrB,MAAM,CAACyM,OAAO,CAACN,WAAW,EAAE,CAAC,IAAID,MAAM,CAAChG,MAAM,CAACiG,WAAW,EAAE,CAAC9K,QAAQ,CAACrB,MAAM,CAACoM,QAAQ,CAACD,WAAW,EAAE,CAAC,KAAK,CAACF,WAAW,IAAIA,WAAW,CAACC,MAAM,CAAC,CAAC;QACtQ;MACF;MACA,IAAI,IAAI,CAAC1H,QAAQ,CAACsC,SAAS,EAAE;QAC3B,OAAOoF,MAAM,CAAChG,MAAM,CAACiG,WAAW,EAAE,CAAC9K,QAAQ,CAACrB,MAAM,CAAC0M,SAAS,CAACP,WAAW,EAAE,CAAC,KAAK,CAACF,WAAW,IAAIA,WAAW,CAACC,MAAM,CAAC,CAAC;MACtH;MACA,IAAI,IAAI,CAAC1H,QAAQ,CAACgC,GAAG,EAAE;QACrB,OAAO,IAAI,KAAK,CAACyF,WAAW,IAAIA,WAAW,CAACC,MAAM,CAAC,CAAC;MACtD;MACA,OAAO,KAAK;IACd,CAAC,CAAC;EACJ;EAEAnH,kBAAkBA,CAAA;IAChB,MAAM4H,mBAAmB,GAAG,IAAI,CAACrG,aAAa,CAACmD,MAAM,CAACC,SAAS,IAAIA,SAAS,CAACxD,MAAM,CAACiG,WAAW,EAAE,CAAC9K,QAAQ,CAAC,WAAW,CAAC,CAAC;IACxH,MAAMuL,iBAAiB,GAAG,IAAI,CAACtG,aAAa,CAACmD,MAAM,CAACC,SAAS,IAAIA,SAAS,CAACxD,MAAM,CAACiG,WAAW,EAAE,CAAC9K,QAAQ,CAAC,SAAS,CAAC,CAAC;IACpH,MAAMwL,iBAAiB,GAAG,IAAI,CAACvG,aAAa,CAACmD,MAAM,CAACC,SAAS,IAAIA,SAAS,CAACxD,MAAM,CAACiG,WAAW,EAAE,CAAC9K,QAAQ,CAAC,SAAS,CAAC,CAAC;IACpH,MAAMyL,eAAe,GAAG,IAAI,CAACxG,aAAa,CAAC1D,MAAM;IACjD,OAAO;MAAEuC,SAAS,EAAEwH,mBAAmB,CAAC/J,MAAM;MAAEsC,OAAO,EAAE0H,iBAAiB,CAAChK,MAAM;MAAEwC,OAAO,EAAEyH,iBAAiB,CAACjK,MAAM;MAAEoC,KAAK,EAAE8H;IAAe,CAAE;EAChJ;EAEAzH,uBAAuBA,CAAA;IACrB,MAAMsH,mBAAmB,GAAG,IAAI,CAACrG,aAAa,CAACmD,MAAM,CAACC,SAAS,IAAIA,SAAS,CAACxD,MAAM,CAACiG,WAAW,EAAE,CAAC9K,QAAQ,CAACrB,MAAM,CAACqM,SAAS,CAACF,WAAW,EAAE,CAAC,CAAC;IAC3I,MAAMS,iBAAiB,GAAG,IAAI,CAACtG,aAAa,CAACmD,MAAM,CAACC,SAAS,IAAIA,SAAS,CAACxD,MAAM,CAACiG,WAAW,EAAE,CAAC9K,QAAQ,CAACrB,MAAM,CAACoM,QAAQ,CAACD,WAAW,EAAE,CAAC,CAAC;IACxI,MAAMY,mBAAmB,GAAG,IAAI,CAACzG,aAAa,CAACmD,MAAM,CAACC,SAAS,IAAIA,SAAS,CAACxD,MAAM,CAACiG,WAAW,EAAE,CAAC9K,QAAQ,CAAC,WAAW,CAAC,CAAC;IACxH,MAAMyL,eAAe,GAAG,IAAI,CAACxG,aAAa,CAAC1D,MAAM;IACjD,OAAO;MAAEuC,SAAS,EAAEwH,mBAAmB,CAAC/J,MAAM;MAAEsC,OAAO,EAAE0H,iBAAiB,CAAChK,MAAM;MAAE0C,SAAS,EAAEyH,mBAAmB,CAACnK,MAAM;MAAEoC,KAAK,EAAE8H;IAAe,CAAE;EACpJ;EAEAjD,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACvD,aAAa,CAACmD,MAAM,CAACC,SAAS,IAAIA,SAAS,CAACxD,MAAM,CAACiG,WAAW,EAAE,CAAC9K,QAAQ,CAACrB,MAAM,CAACoM,QAAQ,CAACD,WAAW,EAAE,CAAC,CAAC;EACvH;EAAC,QAAAa,CAAA,G;qBArTUzH,kBAAkB,EAAAlF,EAAA,CAAA4M,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA9M,EAAA,CAAA4M,iBAAA,CAAAG,EAAA,CAAAC,YAAA,GAAAhN,EAAA,CAAA4M,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAlN,EAAA,CAAA4M,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAApN,EAAA,CAAA4M,iBAAA,CAAAS,EAAA,CAAAC,aAAA,GAAAtN,EAAA,CAAA4M,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAAxN,EAAA,CAAA4M,iBAAA,CAAAW,EAAA,CAAAE,MAAA,GAAAzN,EAAA,CAAA4M,iBAAA,CAAA5M,EAAA,CAAA0N,iBAAA,GAAA1N,EAAA,CAAA4M,iBAAA,CAAAe,EAAA,CAAAhI,oBAAA;EAAA;EAAA,QAAAiI,EAAA,G;UAAlB1I,kBAAkB;IAAA2I,SAAA;IAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;+BACMzO,gBAAgB;;;;;;;;;;;;;;;;QCvBrCS,EAJhB,CAAAC,cAAA,gBAAyC,aACR,aACX,aACoC,aAEI;QAC1CD,EAAA,CAAAU,SAAA,aACM;QAEFV,EADJ,CAAAC,cAAA,cAA0B,cACO;QAAAD,EAAA,CAAAW,MAAA,GAA0C;QAM3FX,EAN2F,CAAAY,YAAA,EAAO,EAC3E,EAEL,EACJ,EACJ,EACJ;QACNZ,EAAA,CAAAC,cAAA,kBAAyC;QAoHnCD,EAnHF,CAAAqB,UAAA,KAAA6M,oCAAA,OAqHC,KAAAC,+CAAA;QArHDnO,EAAA,CAAAoO,OAAA,SAAAC,kCAAA,WAqHC;QArHMrO,EAAA,CAAAsO,iBAAA,OAAW;QAuH1BtO,EADI,CAAAY,YAAA,EAAM,EACJ;QAiBNZ,EAfA,CAAAqB,UAAA,KAAAkN,2CAAA,4BAAsD,KAAAC,2CAAA,4BAeD;;;QApJjCxO,EAAA,CAAAwB,SAAA,GAAyC;QAAzCxB,EAAA,CAAAyB,UAAA,YAAAzB,EAAA,CAAAmC,eAAA,IAAAsM,GAAA,EAAAR,GAAA,CAAApM,UAAA,EAAyC;QAIR7B,EAAA,CAAAwB,SAAA,GAA0C;QAA1CxB,EAAA,CAAA4E,iBAAA,EAAAqJ,GAAA,CAAApM,UAAA,2BAA0C;QAiIhF7B,EAAA,CAAAwB,SAAA,GAAqC;QAArCxB,EAAA,CAAAyB,UAAA,UAAAwM,GAAA,CAAApM,UAAA,IAAAoM,GAAA,CAAAvM,IAAA,eAAqC;QAerC1B,EAAA,CAAAwB,SAAA,EAAoC;QAApCxB,EAAA,CAAAyB,UAAA,SAAAwM,GAAA,CAAApM,UAAA,IAAAoM,GAAA,CAAAvM,IAAA,eAAoC;;;;;;iBDjIrC,CAAC5B,UAAU;IAAC;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}