{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/platform-browser\";\nexport class SafePipe {\n  constructor(sanitizer) {\n    this.sanitizer = sanitizer;\n  }\n  transform(url) {\n    return this.sanitizer.bypassSecurityTrustResourceUrl(url);\n  }\n  static #_ = this.ɵfac = function SafePipe_Factory(t) {\n    return new (t || SafePipe)(i0.ɵɵdirectiveInject(i1.<PERSON><PERSON>anitizer, 16));\n  };\n  static #_2 = this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n    name: \"safe\",\n    type: SafePipe,\n    pure: true\n  });\n}", "map": {"version": 3, "names": ["SafePipe", "constructor", "sanitizer", "transform", "url", "bypassSecurityTrustResourceUrl", "_", "i0", "ɵɵdirectiveInject", "i1", "Dom<PERSON><PERSON><PERSON>zer", "_2", "pure"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\pipes\\save.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\nimport { DomSanitizer} from '@angular/platform-browser';\r\n\r\n@Pipe({ name: 'safe' })\r\nexport class SafePipe implements PipeTransform {\r\n  constructor(private sanitizer: DomSanitizer) {}\r\n  transform(url: string) {\r\n    return this.sanitizer.bypassSecurityTrustResourceUrl(url);\r\n  }\r\n} "], "mappings": ";;AAIA,OAAM,MAAOA,QAAQ;EACnBC,YAAoBC,SAAuB;IAAvB,KAAAA,SAAS,GAATA,SAAS;EAAiB;EAC9CC,SAASA,CAACC,GAAW;IACnB,OAAO,IAAI,CAACF,SAAS,CAACG,8BAA8B,CAACD,GAAG,CAAC;EAC3D;EAAC,QAAAE,CAAA,G;qBAJUN,QAAQ,EAAAO,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA,G;;UAARX,QAAQ;IAAAY,IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}