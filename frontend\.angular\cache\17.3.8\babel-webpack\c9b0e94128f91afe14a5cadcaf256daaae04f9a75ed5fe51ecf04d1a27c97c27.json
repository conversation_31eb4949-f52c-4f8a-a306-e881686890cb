{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { NoteRoutingModule } from './note-routing.module';\nimport { LibraryModule } from '../library/library.module';\nimport { EditorModule } from 'primeng/editor';\nimport { ButtonModule } from 'primeng/button';\nimport * as i0 from \"@angular/core\";\nexport let NoteModule = /*#__PURE__*/(() => {\n  class NoteModule {\n    static #_ = this.ɵfac = function NoteModule_Factory(t) {\n      return new (t || NoteModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: NoteModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, NoteRoutingModule, SharedModule, FormsModule, ReactiveFormsModule, MatTooltipModule, MatProgressSpinnerModule, LibraryModule, EditorModule, ButtonModule, FormsModule]\n    });\n  }\n  return NoteModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}