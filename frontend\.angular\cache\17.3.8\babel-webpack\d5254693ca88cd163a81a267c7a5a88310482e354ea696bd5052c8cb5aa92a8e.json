{"ast": null, "code": "import { CommonModule, DOCUMENT } from \"@angular/common\";\nimport { inject } from '@angular/core';\nimport { RouterModule } from \"@angular/router\";\nimport { ButtonModule } from \"primeng/button\";\nimport { GeneralService } from \"src/app/core/services/general.service\";\nimport { LoaderComponent } from \"src/app/shared/loader/loader.component\";\nimport { SubSink } from \"subsink\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/jitsi-meet.service\";\nimport * as i2 from \"src/app/core/services/auth.service\";\nimport * as i3 from \"src/app/core/services/toast.service\";\nimport * as i4 from \"src/app/core/services/classroom.service\";\nimport * as i5 from \"@angular/router\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"primeng/button\";\nconst _c0 = [\"jitsiContainer\"];\nfunction MeetRoomComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11);\n    i0.ɵɵelement(1, \"app-loader\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"scale\", 1.7)(\"size\", 200);\n  }\n}\nfunction MeetRoomComponent_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 13)(2, \"div\", 14);\n    i0.ɵɵelement(3, \"img\", 15);\n    i0.ɵɵelementStart(4, \"h2\");\n    i0.ɵɵtext(5, \" Room Closed \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7, \"You can rejoin the room anytime by visiting again this page\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"button\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction MeetRoomComponent_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 17)(2, \"div\", 14)(3, \"h2\");\n    i0.ɵɵtext(4, \"Rejoining after teacher join...\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Please wait a moment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"app-loader\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"scale\", 1.7)(\"size\", 200);\n  }\n}\nfunction MeetRoomComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵelement(1, \"button\", 19);\n    i0.ɵɵelementEnd();\n  }\n}\nexport class MeetRoomComponent {\n  constructor(jitsiMeetService, authService, renderer, toastService, classroomService, route, router, document) {\n    this.jitsiMeetService = jitsiMeetService;\n    this.authService = authService;\n    this.renderer = renderer;\n    this.toastService = toastService;\n    this.classroomService = classroomService;\n    this.route = route;\n    this.router = router;\n    this.document = document;\n    this.subs = new SubSink();\n    this.roomName = '';\n    this.generalService = inject(GeneralService);\n    this.jitsiJSlibscript = 'https://meet.jit.si/external_api.js';\n    this.jitsiFrameLoading = true;\n  }\n  ngOnInit() {\n    this.user = this.authService.getLoggedInUser();\n    this.elem = document.documentElement;\n    this.route.queryParams.subscribe(params => {\n      console.log(params);\n      // const lessonId = params.id;\n      // const classroomId = params.classroomId;\n      this.loadJitsiFrame(params);\n      // Use the lesson parameter as needed\n    });\n  }\n  loadJitsiFrame(params) {\n    this.subs.add(this.classroomService.getClassroom(params.classroomId).pipe().subscribe(res => {\n      console.log(res);\n      this.classroom = res;\n      this.classroomLesson = res.lessons.find(el => el.id === +params.id);\n      console.log(this.classroomLesson);\n      if (this.classroomLesson === undefined || this.generalService.isObjectEmpty(this.classroomLesson)) {\n        return;\n      }\n      this.roomName = 'MyLingoTrip' + '-' + res.language + '-Lesson-' + this.classroomLesson.id;\n      console.log(this.classroomLesson);\n      const scriptElement = this.jitsiMeetService.loadJsScript(this.renderer, this.jitsiJSlibscript);\n      scriptElement.onload = () => {\n        setTimeout(() => {\n          this.jitsiMeetService.setRoomName(this.roomName);\n          this.jitsiMeetService.setLoggedUser(this.user);\n          this.jitsiMeetService.moveRoom(this.jitsiMeetService.namePrincipalRoom, true);\n          this.jitsiMeetService.setRoomSubject(res.language + ' Lesson');\n          setTimeout(() => {\n            this.jitsiFrameLoading = false;\n          }, 500);\n        }, 1050);\n      };\n      this.loadLeaveEvent();\n      this.loadCloseEvent();\n      this.loadConferenceLeftEvent();\n    }));\n  }\n  ngOnDestroy() {\n    // Unsubscribe from any subscriptions\n    // Clean up the service instance\n    this.jitsiMeetService.ngOnDestroy(); // Call a method to clean up the service instance\n    this.subs.unsubscribe();\n    this.jitsiMeetService.setVideoConferenceLeft(false);\n    this.jitsiMeetService.setTeacherJoined(false);\n  }\n  loadLeaveEvent() {\n    this.subs.add(this.jitsiMeetService.teacherJoined.pipe().subscribe(res => {\n      if (res) {\n        // this.toastService.setShowToastmessage({\n        //     severity: 'warn',\n        //     summary: '',\n        //     detail: 'Reloading after teacher join...'\n        // });\n        console.log(res);\n        setTimeout(() => {\n          this.jitsiMeetService.setLoggedUser(this.user);\n          this.jitsiMeetService.moveRoom(this.jitsiMeetService.namePrincipalRoom, true);\n        }, 2150);\n      }\n    }));\n  }\n  loadConferenceLeftEvent() {\n    this.subs.add(this.jitsiMeetService.videoConferenceLeft.pipe().subscribe(res => {\n      if (res) {\n        this.toastService.setShowToastmessage({\n          severity: 'info',\n          summary: '',\n          detail: 'You left the lesson or the lesson ended.'\n        });\n        this.router.navigate(['/dashboard']);\n      }\n    }));\n  }\n  loadCloseEvent() {}\n  toggleFullscreen() {\n    // this.jitsiMeetService.openMicrophoneSettings();\n    const jitsiIframe = this.jitsiContainer.nativeElement.firstChild;\n    console.log(jitsiIframe);\n    jitsiIframe.classList.toggle('fullscreen');\n    jitsiIframe.style.zIndex = '1';\n    if (jitsiIframe) {\n      if (!document.fullscreenElement) {\n        jitsiIframe.requestFullscreen().catch(err => {\n          console.error(`Error attempting to enable full-screen mode: ${err.message}`);\n        });\n      } else {\n        document.exitFullscreen();\n      }\n    }\n  }\n  exitFullscreen() {\n    document.exitFullscreen().then(() => {\n      //   this.isFullscreen = false;\n    }).catch(err => {\n      console.error(`Error attempting to exit full-screen mode: ${err.message}`);\n    });\n  }\n  static #_ = this.ɵfac = function MeetRoomComponent_Factory(t) {\n    return new (t || MeetRoomComponent)(i0.ɵɵdirectiveInject(i1.JitsiMeetService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i3.ToastService), i0.ɵɵdirectiveInject(i4.ClassroomService), i0.ɵɵdirectiveInject(i5.ActivatedRoute), i0.ɵɵdirectiveInject(i5.Router), i0.ɵɵdirectiveInject(DOCUMENT));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MeetRoomComponent,\n    selectors: [[\"app-meet-room\"]],\n    viewQuery: function MeetRoomComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.jitsiContainer = _t.first);\n      }\n    },\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 16,\n    vars: 10,\n    consts: [[\"jitsiContainer\", \"\"], [\"id\", \"profile\", 1, \"profile\", \"flex-column\"], [1, \"block-header\", \"meet-header\", \"gradient-header\", \"justify-content-center\", \"lg:flex\"], [1, \"block-title\"], [1, \"text-0\"], [\"class\", \"h-full w-full flex align-items-center justify-content-center\", 4, \"ngIf\"], [1, \"flex\", \"flex-column\", \"lg:flex-row\", \"relative\"], [4, \"ngIf\"], [1, \"w-full\", \"flex\"], [\"id\", \"jitsi-iframe\", 1, \"meet-wrap\", \"w-full\"], [\"class\", \"w-full flex align-items-center justify-content-center\", 4, \"ngIf\"], [1, \"h-full\", \"w-full\", \"flex\", \"align-items-center\", \"justify-content-center\"], [3, \"scale\", \"size\"], [1, \"absolute\", \"w-full\", \"load-overlay\", \"full-opacity\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"h-full\", \"flex-column\", \"text-center\"], [\"src\", \"/assets/images/dashboard/calendar/reschedule-calendar-icon.svg\", \"width\", \"48\"], [\"routerLink\", \"/dashboard\", \"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"Back to Dashboard\", \"icon\", \"pi pi-angle-left\", \"iconPos\", \"left\", \"styleClass\", \"\", 1, \"p-button-outlined\", \"p-button-sm\", \"p-button-rounded\"], [1, \"absolute\", \"w-full\", \"load-overlay\"], [1, \"w-full\", \"flex\", \"align-items-center\", \"justify-content-center\"], [\"routerLink\", \"/dashboard\", \"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"Back to Dashboard\", \"icon\", \"pi pi-angle-left\", \"iconPos\", \"left\", \"styleClass\", \"\", 1, \"p-button-outlined\", \"p-button-sm\", \"p-button-rounded\", \"mt-4\"]],\n    template: function MeetRoomComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"span\", 3)(3, \"span\", 4);\n        i0.ɵɵtext(4, \"MyLingoClassroom\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(5, MeetRoomComponent_div_5_Template, 2, 2, \"div\", 5);\n        i0.ɵɵelementStart(6, \"div\", 6);\n        i0.ɵɵtemplate(7, MeetRoomComponent_ng_container_7_Template, 9, 0, \"ng-container\", 7);\n        i0.ɵɵpipe(8, \"async\");\n        i0.ɵɵpipe(9, \"async\");\n        i0.ɵɵtemplate(10, MeetRoomComponent_ng_container_10_Template, 8, 2, \"ng-container\", 7);\n        i0.ɵɵpipe(11, \"async\");\n        i0.ɵɵelementStart(12, \"div\", 8);\n        i0.ɵɵelement(13, \"div\", 9, 0);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(15, MeetRoomComponent_div_15_Template, 2, 0, \"div\", 10);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.jitsiFrameLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(8, 4, ctx.jitsiMeetService.roomClosed) && !i0.ɵɵpipeBind1(9, 6, ctx.jitsiMeetService.teacherJoined));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(11, 8, ctx.jitsiMeetService.teacherJoined));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", !ctx.jitsiFrameLoading);\n      }\n    },\n    dependencies: [CommonModule, i6.NgIf, i6.AsyncPipe, RouterModule, i5.RouterLink, LoaderComponent, ButtonModule, i7.ButtonDirective],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.gradient-header[_ngcontent-%COMP%] {\\n  background-image: url(\\\"/assets/images/classroom-gradient-bg.png\\\");\\n  background-repeat: no-repeat;\\n  background-position: center center;\\n  min-height: 60px;\\n  border-radius: 8px;\\n  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);\\n}\\n\\n.load-overlay[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.89);\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  left: 0;\\n  right: 0;\\n  transition: 0.4s all ease-in-out;\\n}\\n.load-overlay.full-opacity[_ngcontent-%COMP%] {\\n  background: rgb(255, 255, 255);\\n}\\n\\n.meet-wrap[_ngcontent-%COMP%] {\\n  top: -10px;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.exit-fullscreen-button[_ngcontent-%COMP%] {\\n  display: none;\\n  \\n\\n  background-color: #f44336;\\n  \\n\\n  color: white;\\n  \\n\\n  border: none;\\n  \\n\\n  padding: 10px 20px;\\n  \\n\\n  font-size: 16px;\\n  \\n\\n  cursor: pointer;\\n  \\n\\n  border-radius: 4px;\\n  \\n\\n  position: fixed;\\n  top: 0px;\\n  left: 0px;\\n  bottom: 0px;\\n  right: 0px;\\n  width: 100%;\\n  height: 100%;\\n  border: none;\\n  margin: 0;\\n  padding: 0;\\n  overflow: hidden;\\n  z-index: 999999;\\n}\\n\\n.exit-fullscreen-button[_ngcontent-%COMP%]:hover {\\n  background-color: #d32f2f;\\n  \\n\\n}\\n\\n.exit-fullscreen-button.show[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  \\n\\n}\\n\\nbutton[_ngcontent-%COMP%] {\\n  border: none;\\n  padding: 8px 16px;\\n  background: linear-gradient(to top right, #4f5adb, #8e8bf3);\\n  color: white;\\n  cursor: pointer;\\n}\\n\\n#root[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 1000px;\\n  font-size: 24px;\\n  height: 400px;\\n  background-color: #e9c46a;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n#jitsiConferenceFrame0[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 600px;\\n  height: 200px;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  background-color: #e76f51;\\n}\\n\\n#jitsiConferenceFrame0[_ngcontent-%COMP%]:-webkit-full-screen {\\n  \\n\\n}\\n\\n#jitsiConferenceFrame0[_ngcontent-%COMP%]:fullscreen {\\n  \\n\\n}\\n\\n#jitsiConferenceFrame0[_ngcontent-%COMP%]::backdrop {\\n  background-color: transparent;\\n}\\n\\n.title[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 40px;\\n  font-family: roboto;\\n  text-transform: uppercase;\\n  color: #264653;\\n}\\n\\nbody[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  width: 100vw;\\n  background-color: #264653;\\n  margin: 0;\\n  box-sizing: border-box;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["CommonModule", "DOCUMENT", "inject", "RouterModule", "ButtonModule", "GeneralService", "LoaderComponent", "SubSink", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵelementContainerStart", "ɵɵtext", "MeetRoomComponent", "constructor", "jitsiMeetService", "authService", "renderer", "toastService", "classroomService", "route", "router", "document", "subs", "roomName", "generalService", "jitsiJSlibscript", "jitsi<PERSON>rameLoading", "ngOnInit", "user", "getLoggedInUser", "elem", "documentElement", "queryParams", "subscribe", "params", "console", "log", "loadJitsiFrame", "add", "getClassroom", "classroomId", "pipe", "res", "classroom", "<PERSON><PERSON><PERSON><PERSON>", "lessons", "find", "el", "id", "undefined", "isObjectEmpty", "language", "scriptElement", "loadJsScript", "onload", "setTimeout", "setRoomName", "setLogged<PERSON>ser", "moveRoom", "namePrincipalRoom", "setRoomSubject", "loadLeaveEvent", "loadCloseEvent", "loadConferenceLeftEvent", "ngOnDestroy", "unsubscribe", "setVideoConferenceLeft", "set<PERSON><PERSON><PERSON><PERSON>oined", "teacherJoined", "videoConferenceLeft", "setShowToastmessage", "severity", "summary", "detail", "navigate", "toggleFullscreen", "jitsiIframe", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nativeElement", "<PERSON><PERSON><PERSON><PERSON>", "classList", "toggle", "style", "zIndex", "fullscreenElement", "requestFullscreen", "catch", "err", "error", "message", "exitFullscreen", "then", "_", "ɵɵdirectiveInject", "i1", "JitsiMeetService", "i2", "AuthService", "Renderer2", "i3", "ToastService", "i4", "ClassroomService", "i5", "ActivatedRoute", "Router", "_2", "selectors", "viewQuery", "MeetRoomComponent_Query", "rf", "ctx", "ɵɵtemplate", "MeetRoomComponent_div_5_Template", "MeetRoomComponent_ng_container_7_Template", "MeetRoomComponent_ng_container_10_Template", "MeetRoomComponent_div_15_Template", "ɵɵpipeBind1", "roomClosed", "i6", "NgIf", "AsyncPipe", "RouterLink", "i7", "ButtonDirective", "styles"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\jitsi-meet\\meet-room\\meet-room.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\jitsi-meet\\meet-room\\meet-room.component.html"], "sourcesContent": ["import { CommonModule, DOCUMENT } from \"@angular/common\";\r\nimport { ChangeDetectionStrategy, Component, ElementRef, Inject, Renderer2, ViewChild, inject } from '@angular/core';\r\nimport { ActivatedRoute, Params, Router, RouterModule } from \"@angular/router\";\r\nimport { ButtonModule } from \"primeng/button\";\r\nimport { take } from \"rxjs/operators\";\r\nimport { Lesson } from \"src/app/core/models/lesson.model\";\r\nimport { AuthService } from \"src/app/core/services/auth.service\";\r\nimport { ClassroomService } from \"src/app/core/services/classroom.service\";\r\nimport { GeneralService } from \"src/app/core/services/general.service\";\r\nimport { JitsiMeetService } from \"src/app/core/services/jitsi-meet.service\";\r\nimport { ToastService } from \"src/app/core/services/toast.service\";\r\nimport { LoaderComponent } from \"src/app/shared/loader/loader.component\";\r\nimport { SubSink } from \"subsink\";\r\n\r\n@Component({\r\n    selector: 'app-meet-room',\r\n    standalone: true,\r\n    imports: [\r\n        CommonModule,\r\n        RouterModule,\r\n        LoaderComponent,\r\n        ButtonModule,\r\n    ],\r\n    templateUrl: './meet-room.component.html',\r\n    styleUrls: ['./meet-room.component.scss'],\r\n})\r\nexport class MeetRoomComponent {\r\n    @ViewChild('jitsiContainer') jitsiContainer: ElementRef | undefined;\r\n    user: any;\r\n    private subs = new SubSink();\r\n    classroomLesson: any;\r\n    classroom: any;\r\n    roomName = '';\r\n    elem: any;\r\n    generalService = inject(GeneralService);\r\n    jitsiJSlibscript = 'https://meet.jit.si/external_api.js';\r\n    jitsiFrameLoading = true;\r\n    constructor(\r\n        public jitsiMeetService: JitsiMeetService,\r\n        private authService: AuthService,\r\n        private renderer: Renderer2,\r\n        private toastService: ToastService,\r\n        private classroomService: ClassroomService,\r\n        private route: ActivatedRoute,\r\n        private router: Router,\r\n        @Inject(DOCUMENT) private document: any,\r\n    ) {\r\n    }\r\n\r\n    ngOnInit(): void {\r\n        this.user = this.authService.getLoggedInUser();\r\n        this.elem = document.documentElement;\r\n\r\n\r\n        this.route.queryParams.subscribe(params => {\r\n            console.log(params);\r\n            // const lessonId = params.id;\r\n            // const classroomId = params.classroomId;\r\n            this.loadJitsiFrame(params);\r\n            // Use the lesson parameter as needed\r\n        });\r\n\r\n\r\n    }\r\n\r\n    loadJitsiFrame(params: Params) {\r\n        this.subs.add(this.classroomService.getClassroom(params.classroomId).pipe().subscribe(res => {\r\n            console.log(res);\r\n            this.classroom = res;\r\n            this.classroomLesson = res.lessons!.find((el: Lesson) => el.id === +params.id);\r\n\r\n            console.log(this.classroomLesson);\r\n            if (this.classroomLesson === undefined || this.generalService.isObjectEmpty(this.classroomLesson)) {\r\n                return;\r\n            }\r\n            this.roomName = 'MyLingoTrip' + '-' + res.language + '-Lesson-' + this.classroomLesson.id;\r\n            console.log(this.classroomLesson);\r\n            const scriptElement = this.jitsiMeetService.loadJsScript(this.renderer,\r\n                this.jitsiJSlibscript);\r\n            scriptElement.onload = () => {\r\n                setTimeout(() => {// i dont know why..\r\n                    this.jitsiMeetService.setRoomName(this.roomName);\r\n                    this.jitsiMeetService.setLoggedUser(this.user);\r\n                    this.jitsiMeetService.moveRoom(this.jitsiMeetService.namePrincipalRoom, true);\r\n                    this.jitsiMeetService.setRoomSubject(res.language + ' Lesson');\r\n                    setTimeout(() => {// i dont know why..\r\n                    this.jitsiFrameLoading = false;\r\n                    }, 500);\r\n                }, 1050);\r\n            }\r\n\r\n            this.loadLeaveEvent();\r\n            this.loadCloseEvent();\r\n            this.loadConferenceLeftEvent();\r\n        }));\r\n    }\r\n\r\n    ngOnDestroy(): void {\r\n        // Unsubscribe from any subscriptions\r\n        // Clean up the service instance\r\n        this.jitsiMeetService.ngOnDestroy(); // Call a method to clean up the service instance\r\n        this.subs.unsubscribe();\r\n        this.jitsiMeetService.setVideoConferenceLeft(false);\r\n        this.jitsiMeetService.setTeacherJoined(false);\r\n    }\r\n\r\n    private loadLeaveEvent() {\r\n        this.subs.add(this.jitsiMeetService.teacherJoined.pipe().subscribe(res => {\r\n            if (res) {\r\n                // this.toastService.setShowToastmessage({\r\n                //     severity: 'warn',\r\n                //     summary: '',\r\n                //     detail: 'Reloading after teacher join...'\r\n                // });\r\n                console.log(res);\r\n                setTimeout(() => {// i dont know why..\r\n\r\n                    this.jitsiMeetService.setLoggedUser(this.user);\r\n                    this.jitsiMeetService.moveRoom(this.jitsiMeetService.namePrincipalRoom, true);\r\n                }, 2150);\r\n            }\r\n\r\n        }));\r\n    }\r\n\r\n    private loadConferenceLeftEvent() {\r\n        this.subs.add(this.jitsiMeetService.videoConferenceLeft.pipe().subscribe(res => {\r\n            if (res) {\r\n                this.toastService.setShowToastmessage({\r\n                    severity: 'info',\r\n                    summary: '',\r\n                    detail: 'You left the lesson or the lesson ended.'\r\n                });\r\n                this.router.navigate(['/dashboard']);\r\n            }\r\n        }));\r\n    }\r\n\r\n\r\n    private loadCloseEvent() {\r\n    }\r\n\r\n    toggleFullscreen() {\r\n        // this.jitsiMeetService.openMicrophoneSettings();\r\n        const jitsiIframe = this.jitsiContainer!.nativeElement.firstChild as HTMLIFrameElement;\r\n        console.log(jitsiIframe);\r\n        jitsiIframe.classList.toggle('fullscreen');\r\n\r\n        jitsiIframe.style.zIndex = '1';\r\n        if (jitsiIframe) {\r\n            if (!document.fullscreenElement) {\r\n                jitsiIframe.requestFullscreen().catch(err => {\r\n                    console.error(`Error attempting to enable full-screen mode: ${err.message}`);\r\n                });\r\n            } else {\r\n                document.exitFullscreen();\r\n            }\r\n        }\r\n    }\r\n\r\n    exitFullscreen() {\r\n        document.exitFullscreen().then(() => {\r\n            //   this.isFullscreen = false;\r\n        }).catch(err => {\r\n            console.error(`Error attempting to exit full-screen mode: ${err.message}`);\r\n        });\r\n    }\r\n\r\n}\r\n\r\n", "<div id=\"profile\" class=\"profile flex-column\">\r\n    <div class=\"block-header meet-header gradient-header justify-content-center lg:flex\">\r\n        <span class=\"block-title\">\r\n            <span class=\"text-0\">MyLingoClassroom</span>\r\n        </span>\r\n    </div>\r\n\r\n    <div class=\"h-full w-full flex align-items-center justify-content-center\" *ngIf=\"jitsiFrameLoading\">\r\n        <app-loader [scale]=\"1.7\" [size]=\"200\"></app-loader>\r\n    </div>\r\n\r\n    <div class=\"flex flex-column lg:flex-row relative\">\r\n\r\n        <ng-container\r\n            *ngIf=\"(this.jitsiMeetService.roomClosed | async) && !(this.jitsiMeetService.teacherJoined | async)\">\r\n            <div class=\"absolute w-full load-overlay full-opacity\">\r\n\r\n                <div class=\"flex align-items-center justify-content-center h-full flex-column text-center\">\r\n\r\n                    <img src=\"/assets/images/dashboard/calendar/reschedule-calendar-icon.svg\" width=\"48\" />\r\n                    <h2> Room Closed </h2>\r\n                    <p>You can rejoin the room anytime by visiting again this page</p>\r\n                    <button routerLink=\"/dashboard\" pButton pRipple type=\"button\" label=\"Back to Dashboard\"\r\n                        icon=\"pi pi-angle-left\" iconPos=\"left\" class=\"p-button-outlined p-button-sm p-button-rounded\"\r\n                        styleClass=\"\"></button>\r\n                </div>\r\n            </div>\r\n        </ng-container>\r\n        <!--  *ngIf=\"(this.jitsiMeetService.teacherJoined | async)\" -->\r\n        <ng-container *ngIf=\"(this.jitsiMeetService.teacherJoined | async)\">\r\n            <div class=\"absolute w-full load-overlay\">\r\n\r\n                <div class=\"flex align-items-center justify-content-center h-full flex-column text-center\">\r\n                    <h2>Rejoining after teacher join...</h2>\r\n                    <p>Please wait a moment</p>\r\n                    <app-loader [scale]=\"1.7\" [size]=\"200\"></app-loader>\r\n                </div>\r\n            </div>\r\n        </ng-container>\r\n        <!-- main iframe -->\r\n\r\n\r\n        <div class=\"w-full flex\">\r\n            <div #jitsiContainer id=\"jitsi-iframe\" class=\"meet-wrap w-full\"></div>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"w-full flex align-items-center justify-content-center\" *ngIf=\"!jitsiFrameLoading\">\r\n        <button routerLink=\"/dashboard\" pButton pRipple type=\"button\" label=\"Back to Dashboard\" icon=\"pi pi-angle-left\"\r\n            iconPos=\"left\" class=\"p-button-outlined p-button-sm p-button-rounded mt-4\" styleClass=\"\"></button>\r\n    </div>\r\n\r\n    <!-- TODO: fix fullscreen -->\r\n    <!-- <button pbutton=\"\" type=\"button\" (click)=\"toggleFullscreen()\"\r\n        class=\"lesson-btn reschedule p-button-outlined p-element p-button-rounded p-button-xs p-button p-component gray-border-button p-button-rounded m-auto\">\r\n        <div class=\"flex align-items-center gap-1 jus ju\">\r\n            <span class=\"font-xs text-primary flex gap-2 justify-content-center align-items-center\">\r\n                Toggle Fullscreen </span>\r\n        </div>\r\n    </button> -->\r\n</div>\r\n\r\n\r\n<!-- <button class=\"exit-fullscreen-button show\" (click)=\"exitFullscreen()\">Exit Fullscreen</button> -->"], "mappings": "AAAA,SAASA,YAAY,EAAEC,QAAQ,QAAQ,iBAAiB;AACxD,SAAuFC,MAAM,QAAQ,eAAe;AACpH,SAAyCC,YAAY,QAAQ,iBAAiB;AAC9E,SAASC,YAAY,QAAQ,gBAAgB;AAK7C,SAASC,cAAc,QAAQ,uCAAuC;AAGtE,SAASC,eAAe,QAAQ,wCAAwC;AACxE,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;ICL7BC,EAAA,CAAAC,cAAA,cAAoG;IAChGD,EAAA,CAAAE,SAAA,qBAAoD;IACxDF,EAAA,CAAAG,YAAA,EAAM;;;IADUH,EAAA,CAAAI,SAAA,EAAa;IAACJ,EAAd,CAAAK,UAAA,cAAa,aAAa;;;;;IAKtCL,EAAA,CAAAM,uBAAA,GACyG;IAGjGN,EAFJ,CAAAC,cAAA,cAAuD,cAEwC;IAEvFD,EAAA,CAAAE,SAAA,cAAuF;IACvFF,EAAA,CAAAC,cAAA,SAAI;IAACD,EAAA,CAAAO,MAAA,oBAAY;IAAAP,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAO,MAAA,kEAA2D;IAAAP,EAAA,CAAAG,YAAA,EAAI;IAClEH,EAAA,CAAAE,SAAA,iBAE2B;IAEnCF,EADI,CAAAG,YAAA,EAAM,EACJ;;;;;;IAGVH,EAAA,CAAAM,uBAAA,GAAoE;IAIxDN,EAHR,CAAAC,cAAA,cAA0C,cAEqD,SACnF;IAAAD,EAAA,CAAAO,MAAA,sCAA+B;IAAAP,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAO,MAAA,2BAAoB;IAAAP,EAAA,CAAAG,YAAA,EAAI;IAC3BH,EAAA,CAAAE,SAAA,qBAAoD;IAE5DF,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IAFcH,EAAA,CAAAI,SAAA,GAAa;IAACJ,EAAd,CAAAK,UAAA,cAAa,aAAa;;;;;IAYtDL,EAAA,CAAAC,cAAA,cAA8F;IAC1FD,EAAA,CAAAE,SAAA,iBACsG;IAC1GF,EAAA,CAAAG,YAAA,EAAM;;;ADxBV,OAAM,MAAOK,iBAAiB;EAW1BC,YACWC,gBAAkC,EACjCC,WAAwB,EACxBC,QAAmB,EACnBC,YAA0B,EAC1BC,gBAAkC,EAClCC,KAAqB,EACrBC,MAAc,EACIC,QAAa;IAPhC,KAAAP,gBAAgB,GAAhBA,gBAAgB;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACY,KAAAC,QAAQ,GAARA,QAAQ;IAhB9B,KAAAC,IAAI,GAAG,IAAInB,OAAO,EAAE;IAG5B,KAAAoB,QAAQ,GAAG,EAAE;IAEb,KAAAC,cAAc,GAAG1B,MAAM,CAACG,cAAc,CAAC;IACvC,KAAAwB,gBAAgB,GAAG,qCAAqC;IACxD,KAAAC,iBAAiB,GAAG,IAAI;EAWxB;EAEAC,QAAQA,CAAA;IACJ,IAAI,CAACC,IAAI,GAAG,IAAI,CAACb,WAAW,CAACc,eAAe,EAAE;IAC9C,IAAI,CAACC,IAAI,GAAGT,QAAQ,CAACU,eAAe;IAGpC,IAAI,CAACZ,KAAK,CAACa,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MACtCC,OAAO,CAACC,GAAG,CAACF,MAAM,CAAC;MACnB;MACA;MACA,IAAI,CAACG,cAAc,CAACH,MAAM,CAAC;MAC3B;IACJ,CAAC,CAAC;EAGN;EAEAG,cAAcA,CAACH,MAAc;IACzB,IAAI,CAACZ,IAAI,CAACgB,GAAG,CAAC,IAAI,CAACpB,gBAAgB,CAACqB,YAAY,CAACL,MAAM,CAACM,WAAW,CAAC,CAACC,IAAI,EAAE,CAACR,SAAS,CAACS,GAAG,IAAG;MACxFP,OAAO,CAACC,GAAG,CAACM,GAAG,CAAC;MAChB,IAAI,CAACC,SAAS,GAAGD,GAAG;MACpB,IAAI,CAACE,eAAe,GAAGF,GAAG,CAACG,OAAQ,CAACC,IAAI,CAAEC,EAAU,IAAKA,EAAE,CAACC,EAAE,KAAK,CAACd,MAAM,CAACc,EAAE,CAAC;MAE9Eb,OAAO,CAACC,GAAG,CAAC,IAAI,CAACQ,eAAe,CAAC;MACjC,IAAI,IAAI,CAACA,eAAe,KAAKK,SAAS,IAAI,IAAI,CAACzB,cAAc,CAAC0B,aAAa,CAAC,IAAI,CAACN,eAAe,CAAC,EAAE;QAC/F;MACJ;MACA,IAAI,CAACrB,QAAQ,GAAG,aAAa,GAAG,GAAG,GAAGmB,GAAG,CAACS,QAAQ,GAAG,UAAU,GAAG,IAAI,CAACP,eAAe,CAACI,EAAE;MACzFb,OAAO,CAACC,GAAG,CAAC,IAAI,CAACQ,eAAe,CAAC;MACjC,MAAMQ,aAAa,GAAG,IAAI,CAACtC,gBAAgB,CAACuC,YAAY,CAAC,IAAI,CAACrC,QAAQ,EAClE,IAAI,CAACS,gBAAgB,CAAC;MAC1B2B,aAAa,CAACE,MAAM,GAAG,MAAK;QACxBC,UAAU,CAAC,MAAK;UACZ,IAAI,CAACzC,gBAAgB,CAAC0C,WAAW,CAAC,IAAI,CAACjC,QAAQ,CAAC;UAChD,IAAI,CAACT,gBAAgB,CAAC2C,aAAa,CAAC,IAAI,CAAC7B,IAAI,CAAC;UAC9C,IAAI,CAACd,gBAAgB,CAAC4C,QAAQ,CAAC,IAAI,CAAC5C,gBAAgB,CAAC6C,iBAAiB,EAAE,IAAI,CAAC;UAC7E,IAAI,CAAC7C,gBAAgB,CAAC8C,cAAc,CAAClB,GAAG,CAACS,QAAQ,GAAG,SAAS,CAAC;UAC9DI,UAAU,CAAC,MAAK;YAChB,IAAI,CAAC7B,iBAAiB,GAAG,KAAK;UAC9B,CAAC,EAAE,GAAG,CAAC;QACX,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC;MAED,IAAI,CAACmC,cAAc,EAAE;MACrB,IAAI,CAACC,cAAc,EAAE;MACrB,IAAI,CAACC,uBAAuB,EAAE;IAClC,CAAC,CAAC,CAAC;EACP;EAEAC,WAAWA,CAAA;IACP;IACA;IACA,IAAI,CAAClD,gBAAgB,CAACkD,WAAW,EAAE,CAAC,CAAC;IACrC,IAAI,CAAC1C,IAAI,CAAC2C,WAAW,EAAE;IACvB,IAAI,CAACnD,gBAAgB,CAACoD,sBAAsB,CAAC,KAAK,CAAC;IACnD,IAAI,CAACpD,gBAAgB,CAACqD,gBAAgB,CAAC,KAAK,CAAC;EACjD;EAEQN,cAAcA,CAAA;IAClB,IAAI,CAACvC,IAAI,CAACgB,GAAG,CAAC,IAAI,CAACxB,gBAAgB,CAACsD,aAAa,CAAC3B,IAAI,EAAE,CAACR,SAAS,CAACS,GAAG,IAAG;MACrE,IAAIA,GAAG,EAAE;QACL;QACA;QACA;QACA;QACA;QACAP,OAAO,CAACC,GAAG,CAACM,GAAG,CAAC;QAChBa,UAAU,CAAC,MAAK;UAEZ,IAAI,CAACzC,gBAAgB,CAAC2C,aAAa,CAAC,IAAI,CAAC7B,IAAI,CAAC;UAC9C,IAAI,CAACd,gBAAgB,CAAC4C,QAAQ,CAAC,IAAI,CAAC5C,gBAAgB,CAAC6C,iBAAiB,EAAE,IAAI,CAAC;QACjF,CAAC,EAAE,IAAI,CAAC;MACZ;IAEJ,CAAC,CAAC,CAAC;EACP;EAEQI,uBAAuBA,CAAA;IAC3B,IAAI,CAACzC,IAAI,CAACgB,GAAG,CAAC,IAAI,CAACxB,gBAAgB,CAACuD,mBAAmB,CAAC5B,IAAI,EAAE,CAACR,SAAS,CAACS,GAAG,IAAG;MAC3E,IAAIA,GAAG,EAAE;QACL,IAAI,CAACzB,YAAY,CAACqD,mBAAmB,CAAC;UAClCC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACX,CAAC;QACF,IAAI,CAACrD,MAAM,CAACsD,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;MACxC;IACJ,CAAC,CAAC,CAAC;EACP;EAGQZ,cAAcA,CAAA,GACtB;EAEAa,gBAAgBA,CAAA;IACZ;IACA,MAAMC,WAAW,GAAG,IAAI,CAACC,cAAe,CAACC,aAAa,CAACC,UAA+B;IACtF5C,OAAO,CAACC,GAAG,CAACwC,WAAW,CAAC;IACxBA,WAAW,CAACI,SAAS,CAACC,MAAM,CAAC,YAAY,CAAC;IAE1CL,WAAW,CAACM,KAAK,CAACC,MAAM,GAAG,GAAG;IAC9B,IAAIP,WAAW,EAAE;MACb,IAAI,CAACvD,QAAQ,CAAC+D,iBAAiB,EAAE;QAC7BR,WAAW,CAACS,iBAAiB,EAAE,CAACC,KAAK,CAACC,GAAG,IAAG;UACxCpD,OAAO,CAACqD,KAAK,CAAC,gDAAgDD,GAAG,CAACE,OAAO,EAAE,CAAC;QAChF,CAAC,CAAC;MACN,CAAC,MAAM;QACHpE,QAAQ,CAACqE,cAAc,EAAE;MAC7B;IACJ;EACJ;EAEAA,cAAcA,CAAA;IACVrE,QAAQ,CAACqE,cAAc,EAAE,CAACC,IAAI,CAAC,MAAK;MAChC;IAAA,CACH,CAAC,CAACL,KAAK,CAACC,GAAG,IAAG;MACXpD,OAAO,CAACqD,KAAK,CAAC,8CAA8CD,GAAG,CAACE,OAAO,EAAE,CAAC;IAC9E,CAAC,CAAC;EACN;EAAC,QAAAG,CAAA,G;qBA5IQhF,iBAAiB,EAAAR,EAAA,CAAAyF,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAA3F,EAAA,CAAAyF,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA7F,EAAA,CAAAyF,iBAAA,CAAAzF,EAAA,CAAA8F,SAAA,GAAA9F,EAAA,CAAAyF,iBAAA,CAAAM,EAAA,CAAAC,YAAA,GAAAhG,EAAA,CAAAyF,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA,GAAAlG,EAAA,CAAAyF,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAApG,EAAA,CAAAyF,iBAAA,CAAAU,EAAA,CAAAE,MAAA,GAAArG,EAAA,CAAAyF,iBAAA,CAmBdhG,QAAQ;EAAA;EAAA,QAAA6G,EAAA,G;UAnBX9F,iBAAiB;IAAA+F,SAAA;IAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;QCvBlB1G,EAHZ,CAAAC,cAAA,aAA8C,aAC2C,cACvD,cACD;QAAAD,EAAA,CAAAO,MAAA,uBAAgB;QAE7CP,EAF6C,CAAAG,YAAA,EAAO,EACzC,EACL;QAENH,EAAA,CAAA4G,UAAA,IAAAC,gCAAA,iBAAoG;QAIpG7G,EAAA,CAAAC,cAAA,aAAmD;QAE/CD,EAAA,CAAA4G,UAAA,IAAAE,yCAAA,0BACyG;;;QAezG9G,EAAA,CAAA4G,UAAA,KAAAG,0CAAA,0BAAoE;;QAapE/G,EAAA,CAAAC,cAAA,cAAyB;QACrBD,EAAA,CAAAE,SAAA,iBAAsE;QAE9EF,EADI,CAAAG,YAAA,EAAM,EACJ;QAENH,EAAA,CAAA4G,UAAA,KAAAI,iCAAA,kBAA8F;QAalGhH,EAAA,CAAAG,YAAA,EAAM;;;QArDyEH,EAAA,CAAAI,SAAA,GAAuB;QAAvBJ,EAAA,CAAAK,UAAA,SAAAsG,GAAA,CAAArF,iBAAA,CAAuB;QAOzFtB,EAAA,CAAAI,SAAA,GAAkG;QAAlGJ,EAAA,CAAAK,UAAA,SAAAL,EAAA,CAAAiH,WAAA,OAAAN,GAAA,CAAAjG,gBAAA,CAAAwG,UAAA,MAAAlH,EAAA,CAAAiH,WAAA,OAAAN,GAAA,CAAAjG,gBAAA,CAAAsD,aAAA,EAAkG;QAexFhE,EAAA,CAAAI,SAAA,GAAmD;QAAnDJ,EAAA,CAAAK,UAAA,SAAAL,EAAA,CAAAiH,WAAA,QAAAN,GAAA,CAAAjG,gBAAA,CAAAsD,aAAA,EAAmD;QAkBFhE,EAAA,CAAAI,SAAA,GAAwB;QAAxBJ,EAAA,CAAAK,UAAA,UAAAsG,GAAA,CAAArF,iBAAA,CAAwB;;;mBD7BxF9B,YAAY,EAAA2H,EAAA,CAAAC,IAAA,EAAAD,EAAA,CAAAE,SAAA,EACZ1H,YAAY,EAAAwG,EAAA,CAAAmB,UAAA,EACZxH,eAAe,EACfF,YAAY,EAAA2H,EAAA,CAAAC,eAAA;IAAAC,MAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}