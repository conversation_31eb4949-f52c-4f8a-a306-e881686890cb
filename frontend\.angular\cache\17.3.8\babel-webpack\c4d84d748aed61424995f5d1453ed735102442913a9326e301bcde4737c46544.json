{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/homework.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"primeng/checkbox\";\nexport let ChooseTagsComponent = /*#__PURE__*/(() => {\n  class ChooseTagsComponent {\n    constructor(homeworkService) {\n      this.homeworkService = homeworkService;\n      this.applyToAll = false;\n      this.file = {};\n      this.libraryFile = {};\n      this.chooseTag = new EventEmitter();\n      this.tagsSubmitted = new EventEmitter();\n      this.tagsToAll = [];\n    }\n    ngOnInit() {\n      console.log(this.libraryFile);\n      this.homeworkService.tagToAll.subscribe(res => {\n        if (res.tag !== \"\") {\n          if (res.add) {\n            this.tagsToAll.push(res.tag);\n          } else {\n            let taskIndex = this.tagsToAll.findIndex(el => {\n              return el == res.tag;\n            });\n            this.tagsToAll.splice(taskIndex, 1);\n          }\n        }\n      });\n    }\n    ngOnDestroy() {\n      this.homeworkService.setTagToAll({\n        tag: \"\",\n        add: false\n      });\n    }\n    onChooseTag(event, tag, isLevel, isCategory) {\n      this.chooseTag.emit({\n        checked: event.checked,\n        tag: tag,\n        isLevel: isLevel,\n        isCategory: isCategory,\n        applyToAll: this.applyToAll,\n        file: this.file\n      });\n      if (this.applyToAll) {\n        this.homeworkService.setTagToAll({\n          tag: tag,\n          add: event.checked\n        });\n      }\n    }\n    getIsChecked(el) {\n      return this.tagsToAll.includes(el);\n    }\n    getIsCheckedCategory(el) {\n      return this.libraryFile && this.libraryFile.categories ? this.libraryFile.categories.includes(el) : this.tagsToAll.includes(el);\n    }\n    getIsCheckedLevel(el) {\n      return this.libraryFile && this.libraryFile.levels ? this.libraryFile.levels.includes(el) : this.tagsToAll.includes(el);\n    }\n    onTagsSubmitted() {\n      this.tagsSubmitted.emit();\n    }\n    static #_ = this.ɵfac = function ChooseTagsComponent_Factory(t) {\n      return new (t || ChooseTagsComponent)(i0.ɵɵdirectiveInject(i1.HomeworkService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChooseTagsComponent,\n      selectors: [[\"app-choose-tags\"]],\n      inputs: {\n        applyToAll: \"applyToAll\",\n        file: \"file\",\n        libraryFile: \"libraryFile\"\n      },\n      outputs: {\n        chooseTag: \"chooseTag\",\n        tagsSubmitted: \"tagsSubmitted\"\n      },\n      decls: 42,\n      vars: 28,\n      consts: [[1, \"library-upload-details\"], [1, \"library-upload-details-right\"], [1, \"library-upload-checkboxes\"], [1, \"flex\", \"w-full\"], [1, \"w-5rem\", \"text-right\", \"mr-2\"], [1, \"font-xs\", \"text-primary\", \"mr-2\"], [1, \"w-full\", \"flex\", \"flex-wrap\", \"gap-1\", \"align-items-center\", \"justify-content-start\"], [1, \"library-upload-checkbox\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"A1\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"A2\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"B1\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"B2\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"C1\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"C2\", 3, \"onChange\", \"ngModel\", \"binary\"], [1, \"font-xs\", \"text-primary\"], [1, \"w-full\", \"flex\", \"flex-wrap\", \"align-items-center\", \"justify-content-start\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"Reading\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"Writing\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"Listening\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"Grammar\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"Vocabulary\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"Test\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"Homework\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"Units\", 3, \"onChange\", \"ngModel\", \"binary\"]],\n      template: function ChooseTagsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵtext(6, \" Level: \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"p-checkbox\", 8);\n          i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_9_listener($event) {\n            return ctx.onChooseTag($event, \"A1\", true, false);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 7)(11, \"p-checkbox\", 9);\n          i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_11_listener($event) {\n            return ctx.onChooseTag($event, \"A2\", true, false);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 7)(13, \"p-checkbox\", 10);\n          i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_13_listener($event) {\n            return ctx.onChooseTag($event, \"B1\", true, false);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"div\", 7)(15, \"p-checkbox\", 11);\n          i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_15_listener($event) {\n            return ctx.onChooseTag($event, \"B2\", true, false);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 7)(17, \"p-checkbox\", 12);\n          i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_17_listener($event) {\n            return ctx.onChooseTag($event, \"C1\", true, false);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 7)(19, \"p-checkbox\", 13);\n          i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_19_listener($event) {\n            return ctx.onChooseTag($event, \"C2\", true, false);\n          });\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(20, \"div\", 2)(21, \"div\", 3)(22, \"div\", 4)(23, \"div\", 14);\n          i0.ɵɵtext(24, \" Categories: \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 15)(26, \"div\", 7)(27, \"p-checkbox\", 16);\n          i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_27_listener($event) {\n            return ctx.onChooseTag($event, \"Reading\", false, true);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 7)(29, \"p-checkbox\", 17);\n          i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_29_listener($event) {\n            return ctx.onChooseTag($event, \"Writing\", false, true);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 7)(31, \"p-checkbox\", 18);\n          i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_31_listener($event) {\n            return ctx.onChooseTag($event, \"Listening\", false, true);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 7)(33, \"p-checkbox\", 19);\n          i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_33_listener($event) {\n            return ctx.onChooseTag($event, \"Grammar\", false, true);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"div\", 7)(35, \"p-checkbox\", 20);\n          i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_35_listener($event) {\n            return ctx.onChooseTag($event, \"Vocabulary\", false, true);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 7)(37, \"p-checkbox\", 21);\n          i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_37_listener($event) {\n            return ctx.onChooseTag($event, \"Test\", false, true);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 7)(39, \"p-checkbox\", 22);\n          i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_39_listener($event) {\n            return ctx.onChooseTag($event, \"Homework\", false, true);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"div\", 7)(41, \"p-checkbox\", 23);\n          i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_41_listener($event) {\n            return ctx.onChooseTag($event, \"Units\", false, true);\n          });\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedLevel(\"A1\"))(\"binary\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedLevel(\"A2\"))(\"binary\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedLevel(\"B1\"))(\"binary\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedLevel(\"B2\"))(\"binary\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedLevel(\"C1\"))(\"binary\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedLevel(\"C2\"))(\"binary\", true);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedCategory(\"Reading\"))(\"binary\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedCategory(\"Writing\"))(\"binary\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedCategory(\"Listening\"))(\"binary\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedCategory(\"Grammar\"))(\"binary\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedCategory(\"Vocabulary\"))(\"binary\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedCategory(\"Test\"))(\"binary\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedCategory(\"Homework\"))(\"binary\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedCategory(\"Units\"))(\"binary\", true);\n        }\n      },\n      dependencies: [i2.NgControlStatus, i2.NgModel, i3.Checkbox],\n      styles: [\".library-upload-details[_ngcontent-%COMP%]{display:flex;flex-direction:column}.library-upload-details[_ngcontent-%COMP%]   .library-upload-details-left[_ngcontent-%COMP%] > div[_ngcontent-%COMP%], .library-upload-details[_ngcontent-%COMP%]   .library-upload-details-right[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{margin-top:15px}.library-upload-details[_ngcontent-%COMP%]   .library-upload-details-right[_ngcontent-%COMP%]   .library-upload-checkboxes[_ngcontent-%COMP%]{display:flex}.library-upload-details[_ngcontent-%COMP%]   .library-upload-details-right[_ngcontent-%COMP%]   .library-upload-checkboxes[_ngcontent-%COMP%]   .library-upload-checkbox[_ngcontent-%COMP%]{margin-right:20px;margin-bottom:10px;display:flex}.library-upload-details[_ngcontent-%COMP%]   .library-upload-details-right[_ngcontent-%COMP%]   .library-upload-checkboxes[_ngcontent-%COMP%]   .library-upload-checkbox[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{margin-left:10px}.e-checkbox-wrapper[_ngcontent-%COMP%]   .e-label[_ngcontent-%COMP%]{color:var(--primary-color)}.e-checkbox-wrapper.e-primary[_ngcontent-%COMP%]   .e-frame[_ngcontent-%COMP%]{border-radius:50px;border-color:#5bb7d0}.e-checkbox-wrapper.e-primary[_ngcontent-%COMP%]   .e-frame.e-check[_ngcontent-%COMP%]{background-color:#5bb7d0}[_nghost-%COMP%]     p-checkbox{width:5.4rem}\"]\n    });\n  }\n  return ChooseTagsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}