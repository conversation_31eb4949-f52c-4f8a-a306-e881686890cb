{"ast": null, "code": "export var Dimentions = /*#__PURE__*/function (Dimentions) {\n  Dimentions[Dimentions[\"w576\"] = 576] = \"w576\";\n  Dimentions[Dimentions[\"w768\"] = 768] = \"w768\";\n  Dimentions[Dimentions[\"w992\"] = 992] = \"w992\";\n  Dimentions[Dimentions[\"w1024\"] = 1024] = \"w1024\";\n  Dimentions[Dimentions[\"w1366\"] = 1366] = \"w1366\";\n  Dimentions[Dimentions[\"w1500\"] = 1500] = \"w1500\";\n  Dimentions[Dimentions[\"h850\"] = 850] = \"h850\";\n  return Dimentions;\n}(Dimentions || {});\nexport var AppLayoutType = /*#__PURE__*/function (AppLayoutType) {\n  AppLayoutType[\"Default\"] = \"default\";\n  AppLayoutType[\"Sidebar\"] = \"sidebar\";\n  AppLayoutType[\"Center\"] = \"center\";\n  AppLayoutType[\"Blank\"] = \"blank\";\n  AppLayoutType[\"Admin\"] = \"admin\";\n  return AppLayoutType;\n}(AppLayoutType || {});", "map": {"version": 3, "names": ["Dimentions", "AppLayoutType"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\models\\general.model.ts"], "sourcesContent": ["export interface LanguageToLearn {\r\n    title: string;\r\n    link: string;\r\n}\r\n\r\nexport interface Language {\r\n    code: string;\r\n    name: string;\r\n    nativeName?: string;\r\n}\r\n\r\nexport interface SocialMedia {\r\n    icon: string;\r\n    link: string;\r\n}\r\n\r\nexport interface Country {\r\n    name: string, code: string, emoji: string, unicode: string, image: string\r\n}\r\n\r\nexport interface Timezone {\r\n    value: string,\r\n    abbr: string,\r\n    offset: number,\r\n    isdst: boolean,\r\n    text: string,\r\n    utc: string[],\r\n    countries?: string[]\r\n}\r\n\r\nexport interface DeviceKind {\r\n    w576down: boolean,\r\n    is576: boolean,\r\n    is768: boolean,\r\n    is992: boolean,\r\n    is1024: boolean,\r\n    is1366: boolean,\r\n    isBig: boolean,\r\n    w576up?: boolean,\r\n    w768up?: boolean,\r\n    w992up?: boolean,\r\n    w1024up?: boolean,\r\n    w1366up?: boolean,\r\n    w1500up?: boolean,\r\n}\r\n\r\nexport enum Dimentions {\r\n    w576 = 576,\r\n    w768 = 768,\r\n    w992 = 992,\r\n    w1024 = 1024,\r\n    w1366 = 1366,\r\n    w1500 = 1500,\r\n    h850 = 850\r\n}\r\n\r\nexport interface ToastMessage {\r\n    severity: string,\r\n    summary: string,\r\n    detail: string,\r\n    key?: string,\r\n    life?: number,\r\n}\r\n\r\n\r\nexport enum AppLayoutType {\r\n    Default = 'default',\r\n    Sidebar = 'sidebar', // with sidebar\r\n    Center = 'center', // centered content, no frame\r\n    Blank = 'blank', // no frame\r\n    Admin = 'admin', // admin layout\r\n}\r\n\r\nexport interface LeftMenuItem {\r\n    title: string;\r\n    url: string | (string | { queryParams: { inPage: string; }; })[];\r\n    icon: string;\r\n    lottieIcon?: string;\r\n    disabled: boolean;\r\n    role: string[];\r\n    scale?: number;\r\n    disableOnStudentTrial?: boolean;\r\n    method?: () => void;\r\n  }\r\n\r\n  export interface LeadDynoAffiliate {\r\n    id: number;\r\n    email: string;\r\n    first_name: string;\r\n    last_name: string;\r\n    affiliate_code: string;\r\n    referring_affiliate_id?: any;\r\n    created_at: string;\r\n    updated_at: string;\r\n    status: string;\r\n    paypal_email: string;\r\n    unsubscribed: boolean;\r\n    archived: boolean;\r\n    pending_approval: boolean;\r\n    affiliate_url: string;\r\n    affiliate_dashboard_url: string;\r\n    compensation_tier_code: string;\r\n    custom_fields: {};\r\n    referring_affiliate?: any;\r\n    total_leads: number;\r\n    total_visitors: number;\r\n    total_purchases: number;\r\n  }"], "mappings": "AA8CA,WAAYA,UAQX,gBARD,UAAYA,UAAU;EAClBA,UAAA,CAAAA,UAAA,wBAAU;EACVA,UAAA,CAAAA,UAAA,wBAAU;EACVA,UAAA,CAAAA,UAAA,wBAAU;EACVA,UAAA,CAAAA,UAAA,2BAAY;EACZA,UAAA,CAAAA,UAAA,2BAAY;EACZA,UAAA,CAAAA,UAAA,2BAAY;EACZA,UAAA,CAAAA,UAAA,wBAAU;EAAA,OAPFA,UAAU;AAQtB,CAAC,CARWA,UAAU,OAQrB;AAWD,WAAYC,aAMX,gBAND,UAAYA,aAAa;EACrBA,aAAA,uBAAmB;EACnBA,aAAA,uBAAmB;EACnBA,aAAA,qBAAiB;EACjBA,aAAA,mBAAe;EACfA,aAAA,mBAAe;EAAA,OALPA,aAAa;AAMzB,CAAC,CANWA,aAAa,OAMxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}