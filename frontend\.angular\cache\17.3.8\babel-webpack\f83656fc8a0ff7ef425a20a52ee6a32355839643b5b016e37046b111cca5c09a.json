{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { HomeworkComponent } from './homework.component';\nimport { HomeworksComponent } from './homeworks/homeworks.component';\nimport { HomeworkDetailsComponent } from './homeworks/homework-details/homework-details.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { HomeworkFormComponent } from './homework-form/homework-form.component';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport { HomeworkTaskComponent } from './homework-form/homework-task/homework-task.component';\nimport { HomeworkRateComponent } from './homeworks/homework-details/homework-rate/homework-rate.component';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nlet HomeworkModule = class HomeworkModule {};\nHomeworkModule = __decorate([NgModule({\n  declarations: [HomeworkComponent, HomeworksComponent, HomeworkDetailsComponent, HomeworkFormComponent, HomeworkTaskComponent, HomeworkRateComponent],\n  imports: [CommonModule, FormsModule, ReactiveFormsModule, DropdownModule, SharedModule, MatTooltipModule],\n  exports: [HomeworkComponent]\n})], HomeworkModule);\nexport { HomeworkModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "HomeworkComponent", "HomeworksComponent", "HomeworkDetailsComponent", "FormsModule", "ReactiveFormsModule", "HomeworkFormComponent", "DropdownModule", "SharedModule", "HomeworkTaskComponent", "HomeworkRateComponent", "MatTooltipModule", "HomeworkModule", "__decorate", "declarations", "imports", "exports"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\homework\\homework.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { HomeworkComponent } from './homework.component';\r\nimport { HomeworksComponent } from './homeworks/homeworks.component';\r\nimport { HomeworkDetailsComponent } from './homeworks/homework-details/homework-details.component';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { HomeworkFormComponent } from './homework-form/homework-form.component';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { SharedModule } from 'src/app/shared/shared.module';\r\nimport { HomeworkTaskComponent } from './homework-form/homework-task/homework-task.component';\r\nimport { HomeworkRateComponent } from './homeworks/homework-details/homework-rate/homework-rate.component';\r\nimport { MatTooltipModule } from '@angular/material/tooltip';\r\n\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    HomeworkComponent,\r\n    HomeworksComponent,\r\n    HomeworkDetailsComponent,\r\n    HomeworkFormComponent,\r\n    HomeworkTaskComponent,\r\n    HomeworkRateComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    DropdownModule,\r\n    SharedModule,\r\n    MatTooltipModule\r\n  ],\r\n  exports: [\r\n    HomeworkComponent\r\n  ]\r\n})\r\nexport class HomeworkModule { }\r\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,wBAAwB,QAAQ,yDAAyD;AAClG,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,qBAAqB,QAAQ,yCAAyC;AAC/E,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,qBAAqB,QAAQ,uDAAuD;AAC7F,SAASC,qBAAqB,QAAQ,oEAAoE;AAC1G,SAASC,gBAAgB,QAAQ,2BAA2B;AAyBrD,IAAMC,cAAc,GAApB,MAAMA,cAAc,GAAI;AAAlBA,cAAc,GAAAC,UAAA,EArB1Bd,QAAQ,CAAC;EACRe,YAAY,EAAE,CACZb,iBAAiB,EACjBC,kBAAkB,EAClBC,wBAAwB,EACxBG,qBAAqB,EACrBG,qBAAqB,EACrBC,qBAAqB,CACtB;EACDK,OAAO,EAAE,CACPf,YAAY,EACZI,WAAW,EACXC,mBAAmB,EACnBE,cAAc,EACdC,YAAY,EACZG,gBAAgB,CACjB;EACDK,OAAO,EAAE,CACPf,iBAAiB;CAEpB,CAAC,C,EACWW,cAAc,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}