{"ast": null, "code": "import { RouterModule } from \"@angular/router\";\nimport { TopMenuContainerLayoutComponent } from \"src/app/shared/layout/top-menu-container-layout/top-menu-container-layout.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: TopMenuContainerLayoutComponent,\n  children: [{\n    path: \"\",\n    loadComponent: () => import(\"../jitsi-meet/meet-room/meet-room.component\").then(m => m.MeetRoomComponent),\n    data: {\n      animation: \"Basic\",\n      hideLeftMenu: true\n    }\n  }]\n}];\nexport let JitsiMeetRoutingModule = /*#__PURE__*/(() => {\n  class JitsiMeetRoutingModule {\n    static #_ = this.ɵfac = function JitsiMeetRoutingModule_Factory(t) {\n      return new (t || JitsiMeetRoutingModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: JitsiMeetRoutingModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return JitsiMeetRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}