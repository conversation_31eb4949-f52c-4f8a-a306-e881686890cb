{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { UntypedFormArray, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { UserAvailabilityType } from 'src/app/core/models/user.model';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"src/app/core/services/teacher-application.service\";\nimport * as i5 from \"src/app/core/services/user.service\";\nimport * as i6 from \"src/app/core/services/toast.service\";\nimport * as i7 from \"src/app/core/services/auth.service\";\nimport * as i8 from \"src/app/core/services/calendar.service\";\nimport * as i9 from \"src/app/core/services/general.service\";\nconst _c0 = (a0, a1) => ({\n  \"md:col-9 lg:col-9\": a0,\n  \"narrow\": a1\n});\nconst _c1 = (a0, a1) => ({\n  \"font-xs\": a0,\n  \"font-base\": a1\n});\nconst _c2 = a0 => ({\n  \"disabled-div\": a0\n});\nconst _c3 = a0 => ({\n  \"flex-nowrap gap-1\": a0\n});\nconst _c4 = a0 => ({\n  \"m-0\": a0\n});\nconst _c5 = a0 => ({\n  \"sm bottom-0\": a0\n});\nfunction AvailabilityComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41);\n    i0.ɵɵtext(2, \" Choose date & time available \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AvailabilityComponent_div_26_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 51);\n  }\n}\nfunction AvailabilityComponent_div_26_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_26_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.addMon());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_26_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_26_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const i_r2 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeMon(i_r2));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelementContainerStart(1, 43);\n    i0.ɵɵelementStart(2, \"div\", 44)(3, \"div\", 45);\n    i0.ɵɵtemplate(4, AvailabilityComponent_div_26_img_4_Template, 1, 0, \"img\", 46);\n    i0.ɵɵelementStart(5, \"p-calendar\", 47);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_26_Template_p_calendar_onSelect_5_listener($event) {\n      const i_r2 = i0.ɵɵrestoreView(_r1).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.mon, i_r2, \"from\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 45)(7, \"p-calendar\", 48);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_26_Template_p_calendar_onSelect_7_listener($event) {\n      const i_r2 = i0.ɵɵrestoreView(_r1).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.mon, i_r2, \"to\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, AvailabilityComponent_div_26_button_8_Template, 1, 3, \"button\", 49)(9, AvailabilityComponent_div_26_button_9_Template, 1, 3, \"button\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r2 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroupName\", i_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c3, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r2 > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r2 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r2 > 0);\n  }\n}\nfunction AvailabilityComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1, \"* Invalid Format \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AvailabilityComponent_div_39_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 51);\n  }\n}\nfunction AvailabilityComponent_div_39_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_39_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.addTue());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_39_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_39_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const i_r7 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeTue(i_r7));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelementContainerStart(1, 43);\n    i0.ɵɵelementStart(2, \"div\", 44)(3, \"div\", 45);\n    i0.ɵɵtemplate(4, AvailabilityComponent_div_39_img_4_Template, 1, 0, \"img\", 46);\n    i0.ɵɵelementStart(5, \"p-calendar\", 47);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_39_Template_p_calendar_onSelect_5_listener($event) {\n      const i_r7 = i0.ɵɵrestoreView(_r6).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.tue, i_r7, \"from\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 45)(7, \"p-calendar\", 48);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_39_Template_p_calendar_onSelect_7_listener($event) {\n      const i_r7 = i0.ɵɵrestoreView(_r6).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.tue, i_r7, \"to\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, AvailabilityComponent_div_39_button_8_Template, 1, 3, \"button\", 49)(9, AvailabilityComponent_div_39_button_9_Template, 1, 3, \"button\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r7 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroupName\", i_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c3, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r7 > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r7 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r7 > 0);\n  }\n}\nfunction AvailabilityComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1, \"* Invalid Format \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AvailabilityComponent_div_52_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 51);\n  }\n}\nfunction AvailabilityComponent_div_52_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_52_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.addWed());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_52_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_52_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const i_r11 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeWed(i_r11));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelementContainerStart(1, 43);\n    i0.ɵɵelementStart(2, \"div\", 44)(3, \"div\", 45);\n    i0.ɵɵtemplate(4, AvailabilityComponent_div_52_img_4_Template, 1, 0, \"img\", 46);\n    i0.ɵɵelementStart(5, \"p-calendar\", 47);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_52_Template_p_calendar_onSelect_5_listener($event) {\n      const i_r11 = i0.ɵɵrestoreView(_r10).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.wed, i_r11, \"from\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 45)(7, \"p-calendar\", 48);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_52_Template_p_calendar_onSelect_7_listener($event) {\n      const i_r11 = i0.ɵɵrestoreView(_r10).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.wed, i_r11, \"to\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, AvailabilityComponent_div_52_button_8_Template, 1, 3, \"button\", 49)(9, AvailabilityComponent_div_52_button_9_Template, 1, 3, \"button\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r11 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroupName\", i_r11);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c3, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r11 > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r11 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r11 > 0);\n  }\n}\nfunction AvailabilityComponent_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1, \"* Invalid Format \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AvailabilityComponent_div_65_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 51);\n  }\n}\nfunction AvailabilityComponent_div_65_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_65_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.addThu());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_65_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_65_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const i_r15 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeThu(i_r15));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelementContainerStart(1, 43);\n    i0.ɵɵelementStart(2, \"div\", 44)(3, \"div\", 45);\n    i0.ɵɵtemplate(4, AvailabilityComponent_div_65_img_4_Template, 1, 0, \"img\", 46);\n    i0.ɵɵelementStart(5, \"p-calendar\", 47);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_65_Template_p_calendar_onSelect_5_listener($event) {\n      const i_r15 = i0.ɵɵrestoreView(_r14).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.thu, i_r15, \"from\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 45)(7, \"p-calendar\", 48);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_65_Template_p_calendar_onSelect_7_listener($event) {\n      const i_r15 = i0.ɵɵrestoreView(_r14).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.thu, i_r15, \"to\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, AvailabilityComponent_div_65_button_8_Template, 1, 3, \"button\", 49)(9, AvailabilityComponent_div_65_button_9_Template, 1, 3, \"button\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r15 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroupName\", i_r15);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c3, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r15 > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r15 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r15 > 0);\n  }\n}\nfunction AvailabilityComponent_div_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1, \"* Invalid Format \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AvailabilityComponent_div_78_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 51);\n  }\n}\nfunction AvailabilityComponent_div_78_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_78_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.addFri());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_78_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_78_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const i_r19 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeFri(i_r19));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_78_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelementContainerStart(1, 43);\n    i0.ɵɵelementStart(2, \"div\", 44)(3, \"div\", 45);\n    i0.ɵɵtemplate(4, AvailabilityComponent_div_78_img_4_Template, 1, 0, \"img\", 46);\n    i0.ɵɵelementStart(5, \"p-calendar\", 47);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_78_Template_p_calendar_onSelect_5_listener($event) {\n      const i_r19 = i0.ɵɵrestoreView(_r18).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.fri, i_r19, \"from\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 45)(7, \"p-calendar\", 48);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_78_Template_p_calendar_onSelect_7_listener($event) {\n      const i_r19 = i0.ɵɵrestoreView(_r18).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.fri, i_r19, \"to\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, AvailabilityComponent_div_78_button_8_Template, 1, 3, \"button\", 49)(9, AvailabilityComponent_div_78_button_9_Template, 1, 3, \"button\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r19 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroupName\", i_r19);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c3, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r19 > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r19 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r19 > 0);\n  }\n}\nfunction AvailabilityComponent_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1, \"* Invalid Format \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AvailabilityComponent_div_91_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 51);\n  }\n}\nfunction AvailabilityComponent_div_91_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_91_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.addSat());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_91_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_91_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const i_r23 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeSat(i_r23));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_91_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelementContainerStart(1, 43);\n    i0.ɵɵelementStart(2, \"div\", 44)(3, \"div\", 45);\n    i0.ɵɵtemplate(4, AvailabilityComponent_div_91_img_4_Template, 1, 0, \"img\", 46);\n    i0.ɵɵelementStart(5, \"p-calendar\", 47);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_91_Template_p_calendar_onSelect_5_listener($event) {\n      const i_r23 = i0.ɵɵrestoreView(_r22).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.sat, i_r23, \"from\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 45)(7, \"p-calendar\", 48);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_91_Template_p_calendar_onSelect_7_listener($event) {\n      const i_r23 = i0.ɵɵrestoreView(_r22).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.sat, i_r23, \"to\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, AvailabilityComponent_div_91_button_8_Template, 1, 3, \"button\", 49)(9, AvailabilityComponent_div_91_button_9_Template, 1, 3, \"button\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r23 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroupName\", i_r23);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c3, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r23 > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r23 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r23 > 0);\n  }\n}\nfunction AvailabilityComponent_div_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1, \"* Invalid Format \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AvailabilityComponent_div_104_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 51);\n  }\n}\nfunction AvailabilityComponent_div_104_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_104_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.addSun());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_104_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_104_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const i_r27 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeSun(i_r27));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_104_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelementContainerStart(1, 43);\n    i0.ɵɵelementStart(2, \"div\", 44)(3, \"div\", 45);\n    i0.ɵɵtemplate(4, AvailabilityComponent_div_104_img_4_Template, 1, 0, \"img\", 46);\n    i0.ɵɵelementStart(5, \"p-calendar\", 47);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_104_Template_p_calendar_onSelect_5_listener($event) {\n      const i_r27 = i0.ɵɵrestoreView(_r26).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.sun, i_r27, \"from\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 45)(7, \"p-calendar\", 48);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_104_Template_p_calendar_onSelect_7_listener($event) {\n      const i_r27 = i0.ɵɵrestoreView(_r26).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.sun, i_r27, \"to\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, AvailabilityComponent_div_104_button_8_Template, 1, 3, \"button\", 49)(9, AvailabilityComponent_div_104_button_9_Template, 1, 3, \"button\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r27 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroupName\", i_r27);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c3, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r27 > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r27 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r27 > 0);\n  }\n}\nfunction AvailabilityComponent_div_105_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1, \"* Invalid Format \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AvailabilityComponent_div_106_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const time_r31 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(time_r31);\n  }\n}\nfunction AvailabilityComponent_div_106_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1, \"* Hours per week is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AvailabilityComponent_div_106_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 40)(2, \"div\", 41);\n    i0.ɵɵtext(3, \" Choose hours per week * \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 56)(5, \"div\", 57)(6, \"div\", 58)(7, \"p-dropdown\", 59);\n    i0.ɵɵlistener(\"onChange\", function AvailabilityComponent_div_106_Template_p_dropdown_onChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onHoursChange($event));\n    });\n    i0.ɵɵtemplate(8, AvailabilityComponent_div_106_ng_template_8_Template, 3, 1, \"ng-template\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, AvailabilityComponent_div_106_div_9_Template, 2, 0, \"div\", 21);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r2.hourOptions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.ifFieldValid(\"hours\"));\n  }\n}\nfunction AvailabilityComponent_ng_container_107_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 63)(2, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_ng_container_107_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AvailabilityComponent_ng_container_108_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Next \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AvailabilityComponent_ng_container_108_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Save \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AvailabilityComponent_ng_container_108_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 65)(2, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_ng_container_108_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.goBack());\n    });\n    i0.ɵɵelement(3, \"img\", 67);\n    i0.ɵɵtext(4, \" Back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_ng_container_108_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit());\n    });\n    i0.ɵɵtemplate(6, AvailabilityComponent_ng_container_108_span_6_Template, 2, 0, \"span\", 39)(7, AvailabilityComponent_ng_container_108_span_7_Template, 2, 0, \"span\", 39);\n    i0.ɵɵelement(8, \"img\", 69);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.user);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.user);\n  }\n}\nexport class AvailabilityComponent {\n  constructor(fb, router, location, teacherService, userService, toast, authService, calendarService, generalService) {\n    this.fb = fb;\n    this.router = router;\n    this.location = location;\n    this.teacherService = teacherService;\n    this.userService = userService;\n    this.toast = toast;\n    this.authService = authService;\n    this.calendarService = calendarService;\n    this.generalService = generalService;\n    this.user = {};\n    this.showHours = true;\n    this.isDialog = false;\n    this.subs = new SubSink();\n    this.form = new UntypedFormGroup({});\n    this.weekDays = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];\n    this.teacherAvailabilityMon = [];\n    this.teacherAvailabilityTue = [];\n    this.teacherAvailabilityWed = [];\n    this.teacherAvailabilityThu = [];\n    this.teacherAvailabilityFri = [];\n    this.teacherAvailabilitySat = [];\n    this.teacherAvailabilitySun = [];\n    this.hourOptions = ['5-10', '10-15', '15-20', '20-25', '25-30', '30-35', '35-40'];\n    this.selectedHours = \"5-10\";\n    this.tryToSave = false;\n    this.defaultDate = new Date(\"January 31 2024 00:00\");\n  }\n  ngOnInit() {\n    this.teacherService.setCurrentStepIndex(4);\n    this.subs.add(this.teacherService.updateAvailabilityListener.subscribe(res => {\n      this.teacherAvailabilityMon = [];\n      this.teacherAvailabilityTue = [];\n      this.teacherAvailabilityWed = [];\n      this.teacherAvailabilityThu = [];\n      this.teacherAvailabilityFri = [];\n      this.teacherAvailabilitySat = [];\n      this.teacherAvailabilitySun = [];\n      this.teacher = this.user ? this.authService.getLoggedInUser() : this.teacherService.dummyTeacher;\n      this.teacherAvailability = this.teacher?.availability;\n      this.form = new UntypedFormGroup({\n        mon: new UntypedFormArray([]),\n        tue: new UntypedFormArray([]),\n        wed: new UntypedFormArray([]),\n        thu: new UntypedFormArray([]),\n        fri: new UntypedFormArray([]),\n        sat: new UntypedFormArray([]),\n        sun: new UntypedFormArray([]),\n        mondayActive: new UntypedFormControl(false),\n        tuesdayActive: new UntypedFormControl(false),\n        wednesdayActive: new UntypedFormControl(false),\n        thursdayActive: new UntypedFormControl(false),\n        fridayActive: new UntypedFormControl(false),\n        saturdayActive: new UntypedFormControl(false),\n        sundayActive: new UntypedFormControl(false),\n        hours: new UntypedFormControl(this.teacher?.hours_per_week ? this.teacher?.hours_per_week : this.hourOptions[0], {\n          validators: [Validators.required]\n        })\n      });\n    }));\n    this.subs.add(this.teacherService.submitOnMenuClickListener.subscribe(res => {\n      if (res) {\n        this.onSubmit();\n      }\n    }));\n    if (this.isDialog) {\n      this.userService.getUserAvailability(this.user.aspUserId).subscribe(res => {\n        if (res) {\n          this.updateDaysFields(res);\n        }\n      });\n    } else {\n      this.subs.add(this.teacherService.getTeacherApplicationStep5().subscribe(res => {\n        if (res) {\n          this.updateDaysFields(res);\n        }\n      }));\n    }\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n    this.teacherService.setSubmitOnMenuClickListener(false);\n  }\n  /**\n   * Handle the change event of the time picker control\n   * @param {any} $event - The change event data\n   * @param {FormArray} formArray - The form array that contains the time picker control\n   * @param {any} i - The index of the control in the form array\n   * @param {string} property - The property name to be updated with the selected time\n   */\n  timePickChanged($event, formArray, i, property) {\n    const dd = new Date($event);\n    const timeConverted = {};\n    timeConverted[property] = dd.toLocaleString('en-US', {\n      hour12: false,\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n    const timeArray = timeConverted[property].split(\":\");\n    console.log(timeArray);\n    if (timeArray[0] <= '23') {\n      console.log(dd);\n      console.log(timeConverted);\n      let controls = this.form.controls;\n      formArray.at(i).patchValue(timeConverted);\n      return false;\n    } else {\n      formArray.at(i).patchValue({\n        from: '00:00',\n        to: '00:' + timeArray[1]\n      });\n      return false;\n    }\n  }\n  updateDaysFields(availability) {\n    this.form.get('mondayActive')?.patchValue(availability.mondayActive || !this.hasEmptyFromOrTo(availability.mon));\n    this.form.get('tuesdayActive')?.patchValue(availability.tuesdayActive || !this.hasEmptyFromOrTo(availability.tue));\n    this.form.get('wednesdayActive')?.patchValue(availability.wednesdayActive || !this.hasEmptyFromOrTo(availability.wed));\n    this.form.get('thursdayActive')?.patchValue(availability.thursdayActive || !this.hasEmptyFromOrTo(availability.thu));\n    this.form.get('fridayActive')?.patchValue(availability.fridayActive || !this.hasEmptyFromOrTo(availability.fri));\n    this.form.get('saturdayActive')?.patchValue(availability.saturdayActive || !this.hasEmptyFromOrTo(availability.sat));\n    this.form.get('sundayActive')?.patchValue(availability.sundayActive || !this.hasEmptyFromOrTo(availability.sun));\n    this.form.get('hours')?.patchValue(availability.hours);\n    const mondayActive = this.form.get('mondayActive')?.value;\n    const tuesdayActive = this.form.get('tuesdayActive')?.value;\n    const wednesdayActive = this.form.get('wednesdayActive')?.value;\n    const thursdayActive = this.form.get('thursdayActive')?.value;\n    const fridayActive = this.form.get('fridayActive')?.value;\n    const saturdayActive = this.form.get('saturdayActive')?.value;\n    const sundayActive = this.form.get('sundayActive')?.value;\n    if (availability?.mon && mondayActive) {\n      for (let m of availability.mon) {\n        this.teacherAvailabilityMon.push({\n          from: m.from,\n          to: m.to\n        });\n      }\n    }\n    if (availability?.tue && tuesdayActive) {\n      for (let m of availability.tue) {\n        this.teacherAvailabilityTue.push({\n          from: m.from,\n          to: m.to\n        });\n      }\n    }\n    if (availability?.wed && wednesdayActive) {\n      for (let m of availability.wed) {\n        this.teacherAvailabilityWed.push({\n          from: m.from,\n          to: m.to\n        });\n      }\n    }\n    if (availability?.thu && thursdayActive) {\n      for (let m of availability.thu) {\n        this.teacherAvailabilityThu.push({\n          from: m.from,\n          to: m.to\n        });\n      }\n    }\n    if (availability?.fri && fridayActive) {\n      for (let m of availability.fri) {\n        this.teacherAvailabilityFri.push({\n          from: m.from,\n          to: m.to\n        });\n      }\n    }\n    if (availability?.sat && saturdayActive) {\n      for (let m of availability.sat) {\n        this.teacherAvailabilitySat.push({\n          from: m.from,\n          to: m.to\n        });\n      }\n    }\n    if (availability?.sun && sundayActive) {\n      for (let m of availability.sun) {\n        this.teacherAvailabilitySun.push({\n          from: m.from,\n          to: m.to\n        });\n      }\n    }\n    this.teacherAvailabilityMon?.forEach(element => {\n      this.mon.push(this.fb.group(element));\n    });\n    if (this.teacherAvailabilityMon.length === 0) {\n      this.addMon();\n    }\n    this.teacherAvailabilityTue?.forEach(element => {\n      this.tue.push(this.fb.group(element));\n    });\n    if (this.teacherAvailabilityTue?.length === 0) {\n      this.addTue();\n    }\n    this.teacherAvailabilityWed?.forEach(element => {\n      this.wed.push(this.fb.group(element));\n    });\n    if (this.teacherAvailabilityWed?.length === 0) {\n      this.addWed();\n    }\n    this.teacherAvailabilityThu?.forEach(element => {\n      this.thu.push(this.fb.group(element));\n    });\n    if (this.teacherAvailabilityThu?.length === 0) {\n      this.addThu();\n    }\n    this.teacherAvailabilityFri?.forEach(element => {\n      this.fri.push(this.fb.group(element));\n    });\n    if (this.teacherAvailabilityFri?.length === 0) {\n      this.addFri();\n    }\n    this.teacherAvailabilitySat?.forEach(element => {\n      this.sat.push(this.fb.group(element));\n    });\n    if (this.teacherAvailabilitySat?.length === 0) {\n      this.addSat();\n    }\n    this.teacherAvailabilitySun?.forEach(element => {\n      this.sun.push(this.fb.group(element));\n    });\n    if (this.teacherAvailabilitySun.length === 0) {\n      this.addSun();\n    }\n  }\n  initCheckboxChangedListener(event, dayParam, dayActive) {\n    if (!event.checked) {\n      dayParam.controls?.forEach((element, index) => {\n        console.log(element);\n        const group = new UntypedFormGroup({\n          from: new UntypedFormControl(''),\n          to: new UntypedFormControl('')\n        });\n        this.form.setControl(dayActive, this.fb.array([group]));\n      });\n    }\n  }\n  removeDay(name, index) {\n    switch (name) {\n      case 'mon':\n        this.removeMon(index);\n        break;\n      case 'tue':\n        this.removeTue(index);\n        break;\n      case 'wed':\n        this.removeWed(index);\n        break;\n      case 'thu':\n        this.removeThu(index);\n        break;\n      case 'fri':\n        this.removeFri(index);\n        break;\n      case 'sat':\n        this.removeSat(index);\n        break;\n      case 'sun':\n        this.removeSun(index);\n        break;\n    }\n  }\n  // TODO: unused, see if can be used, else delete\n  processDay(day) {\n    day = day.toLowerCase();\n    const availability = [`teacherAvailability${day}`];\n    let list = [];\n    availability?.forEach(element => {\n      const group = new UntypedFormGroup({\n        from: new UntypedFormControl(''),\n        to: new UntypedFormControl('')\n      });\n      list.push(this.fb.group(group));\n    });\n  }\n  // TODO: unused, see if can be used, else delete\n  add(day) {\n    const dayString = day.toString();\n    const control = this.form.get(dayString.toLowerCase());\n    const group = new UntypedFormGroup({\n      from: new UntypedFormControl(''),\n      to: new UntypedFormControl('')\n    });\n    control.push(group);\n  }\n  addMon() {\n    const group = new UntypedFormGroup({\n      from: new UntypedFormControl(''),\n      to: new UntypedFormControl('')\n    });\n    this.mon.push(group);\n  }\n  removeMon(index) {\n    console.log('remove', index);\n    this.mon.removeAt(index);\n  }\n  addTue() {\n    const group = new UntypedFormGroup({\n      from: new UntypedFormControl(''),\n      to: new UntypedFormControl('')\n    });\n    this.tue.push(group);\n  }\n  removeTue(index) {\n    this.tue.removeAt(index);\n  }\n  addWed() {\n    const group = new UntypedFormGroup({\n      from: new UntypedFormControl(''),\n      to: new UntypedFormControl('')\n    });\n    this.wed.push(group);\n  }\n  removeWed(index) {\n    this.wed.removeAt(index);\n  }\n  addThu() {\n    const group = new UntypedFormGroup({\n      from: new UntypedFormControl(''),\n      to: new UntypedFormControl('')\n    });\n    this.thu.push(group);\n  }\n  removeThu(index) {\n    this.thu.removeAt(index);\n  }\n  addFri() {\n    const group = new UntypedFormGroup({\n      from: new UntypedFormControl(''),\n      to: new UntypedFormControl('')\n    });\n    this.fri.push(group);\n  }\n  removeFri(index) {\n    this.fri.removeAt(index);\n  }\n  addSat() {\n    const group = new UntypedFormGroup({\n      from: new UntypedFormControl(''),\n      to: new UntypedFormControl('')\n    });\n    this.sat.push(group);\n  }\n  removeSat(index) {\n    this.sat.removeAt(index);\n  }\n  addSun() {\n    const group = new UntypedFormGroup({\n      from: new UntypedFormControl(''),\n      to: new UntypedFormControl('')\n    });\n    this.sun.push(group);\n  }\n  removeSun(index) {\n    this.sun.removeAt(index);\n  }\n  get mon() {\n    return this.form.get('mon');\n  }\n  get tue() {\n    return this.form.get('tue');\n  }\n  get wed() {\n    return this.form.get('wed');\n  }\n  get thu() {\n    return this.form.get('thu');\n  }\n  get fri() {\n    return this.form.get('fri');\n  }\n  get sat() {\n    return this.form.get('sat');\n  }\n  get sun() {\n    return this.form.get('sun');\n  }\n  onSubmit() {\n    this.tryToSave = true;\n    // if (!this.form.valid) {\n    //   return;\n    // }\n    let availability = {\n      id: \"string\",\n      type: UserAvailabilityType.AVAILABLE,\n      mon: [],\n      tue: [],\n      wed: [],\n      thu: [],\n      fri: [],\n      sat: [],\n      sun: []\n    };\n    const days = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];\n    const activeDay = ['mondayActive', 'tuesdayActive', 'wednesdayActive', 'thursdayActive', 'fridayActive', 'saturdayActive', 'sundayActive'];\n    const controls = this.form.controls;\n    for (let day of days) {\n      const castedDay = day;\n      if (Array.isArray(availability[castedDay])) {\n        for (let availabilitySlot of controls[day].value) {\n          for (let activeDayFormValue of activeDay) {\n            if (this.form.get(activeDayFormValue).value) {\n              availability[castedDay].push({\n                from: availabilitySlot.from,\n                to: availabilitySlot.to\n              });\n            }\n          }\n        }\n      }\n    }\n    // console.log(this.form.value);\n    if (this.isDialog) {\n      this.subs.add(this.teacherService.updateTeacherAvailability(this.form.value).subscribe(res => {\n        if (res) {\n          this.toast.setShowToastmessage({\n            severity: 'success',\n            summary: '',\n            detail: 'Your Availability info were updated.'\n          });\n          this.calendarService.setToggleOverlayListener(true);\n          this.calendarService.setUpdateListener(true);\n        }\n      }));\n    } else {\n      this.subs.add(this.teacherService.updateAPITeacherApplicationStep5(this.form.value).subscribe(res => {\n        if (res) {\n          this.toast.setShowToastmessage({\n            severity: 'success',\n            summary: '',\n            detail: 'Your Availability info were updated.'\n          });\n          this.router.navigateByUrl('/teacher/review', {\n            replaceUrl: true\n          });\n        }\n        if (!res) {\n          this.toast.setShowToastmessage({\n            severity: 'warn',\n            summary: '',\n            detail: 'Previous steps must be completed first.'\n          });\n        }\n      }));\n      if (this.user) {\n        this.user.availability = availability;\n        this.user.hours_per_week = this.selectedHours;\n        console.log(this.user);\n        this.userService.updateUserListener();\n        this.subs.add(this.teacherService.updateAvailability(this.user.availability).subscribe(res => {\n          localStorage.setItem(\"user\", JSON.stringify(this.user));\n          this.teacherService.setUpdateAvailabilityForSchedulerClickListener(true);\n        }));\n        this.toast.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Availability has been updated.'\n        });\n      } else {\n        this.teacher.availability = availability;\n        this.teacher.hours_per_week = this.selectedHours;\n        // this.router.navigateByUrl('/teacher/(teacher:review)', { replaceUrl: true });\n      }\n    }\n  }\n  checkFormat(from, to) {\n    const isFromValid = from.length === 5 && from.indexOf(\":\") === 2;\n    const isToValid = to.length === 5 && to.indexOf(\":\") === 2;\n    return isFromValid && isToValid;\n  }\n  reload(url) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this.router.navigateByUrl('.', {\n        skipLocationChange: true\n      });\n      return _this.router.navigateByUrl(url);\n    })();\n  }\n  onHoursChange(event) {\n    setTimeout(() => {\n      this.selectedHours = event.value;\n    }, 100);\n  }\n  checkValidity(time) {\n    if (time.length === 0) return 1;\n    let split = time.split(':');\n    let hours = parseInt(split[0]);\n    let minutes = parseInt(split[1]);\n    return hours && minutes;\n  }\n  /**\n  * Check if the given field is valid or not.\n  * @param {string} field - The field to check its validity.\n  * @returns {boolean} - Returns `true` if the field is invalid, and `false` otherwise.\n  */\n  ifFieldValid(field) {\n    let control = this.form.get(field);\n    let isNotValid = false;\n    if (control.controls) {\n      for (let c of control.controls) {\n        let from = c.controls.from.value.toString();\n        let to = c.controls.to.value.toString();\n        isNotValid = isNotValid || this.tryToSave && control.touched && (isNaN(this.checkValidity(from)) || isNaN(this.checkValidity(to)));\n      }\n    } else {\n      isNotValid = this.form.get(field).invalid && (this.tryToSave || this.form.get(field).touched);\n    }\n    if (control.controls?.length === 0) {\n      isNotValid = false;\n    }\n    // if (this.router.url.includes('teacher')) {\n    //   this.teacherService.setStepValidWithBoolean(4, isNotValid, 'teacher-availability-route');\n    // }\n    return isNotValid;\n  }\n  goBack() {\n    this.router.navigateByUrl('/teacher/resume', {\n      replaceUrl: true\n    });\n  }\n  hasEmptyFromOrTo(data) {\n    if (this.generalService.isNullishObject(data)) {\n      return true;\n    }\n    if (data.length === 0) {\n      return true; // Empty array\n    }\n    for (const item of data) {\n      if (item.from.trim() === '' || item.to.trim() === '') {\n        return true; // Empty from or to\n      }\n    }\n    return false; // No empty from or to\n  }\n  static #_ = this.ɵfac = function AvailabilityComponent_Factory(t) {\n    return new (t || AvailabilityComponent)(i0.ɵɵdirectiveInject(i1.UntypedFormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.Location), i0.ɵɵdirectiveInject(i4.TeacherApplicationService), i0.ɵɵdirectiveInject(i5.UserService), i0.ɵɵdirectiveInject(i6.ToastService), i0.ɵɵdirectiveInject(i7.AuthService), i0.ɵɵdirectiveInject(i8.CalendarService), i0.ɵɵdirectiveInject(i9.GeneralService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AvailabilityComponent,\n    selectors: [[\"app-availability\"]],\n    inputs: {\n      user: \"user\",\n      showHours: \"showHours\",\n      isDialog: \"isDialog\"\n    },\n    decls: 109,\n    vars: 73,\n    consts: [[1, \"profile-info-section\"], [3, \"formGroup\"], [1, \"availability\", \"grid\", \"grid-nogutter\"], [\"class\", \"col-12 md:col-3 lg:col-3\", 4, \"ngIf\"], [1, \"col-12\", \"availability-wrapper\", 3, \"ngClass\"], [1, \"availability-row\", \"hidden\", \"md:flex\", 2, \"font-weight\", \"bold\", \"text-align\", \"left\"], [1, \"availability-column-1\", \"title-color\"], [1, \"availability-column-1\", \"title-color\", \"text-center\"], [1, \"availability-column-2\", 2, \"display\", \"flex\", \"flex-wrap\", \"wrap\"], [1, \"column-title\", \"title-color\", \"text-center\"], [1, \"availability-row\"], [1, \"availability-column-1\", \"day\"], [1, \"day-title\", 3, \"ngClass\"], [1, \"availability-column-1\", \"checkbox-wrap\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\"], [\"name\", \"mondayActive\", \"formControlName\", \"mondayActive\", \"styleClass\", \"primary-blue outlined\", \"binary\", \"true\", \"label\", \"\", 3, \"onChange\"], [1, \"availability-column-2\"], [1, \"profile-info\", 3, \"ngClass\"], [1, \"profile-info-section\", 2, \"padding-top\", \"0\"], [\"formArrayName\", \"mon\"], [\"style\", \"position: relative;\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"input-error\", 4, \"ngIf\"], [\"name\", \"tuesdayActive\", \"formControlName\", \"tuesdayActive\", \"styleClass\", \"primary-blue outlined\", \"binary\", \"true\", \"label\", \"\", 3, \"onChange\"], [1, \"availability-column-2\", 3, \"pTooltip\"], [1, \"profile-info\"], [1, \"profile-info-section\", 2, \"padding-top\", \"0\", 3, \"ngClass\"], [\"formArrayName\", \"tue\"], [\"name\", \"wednesdayActive\", \"formControlName\", \"wednesdayActive\", \"styleClass\", \"primary-blue outlined\", \"binary\", \"true\", \"label\", \"\", 3, \"onChange\"], [1, \"availability-column-2\", 3, \"ngClass\"], [\"formArrayName\", \"wed\"], [\"name\", \"thursdayActive\", \"formControlName\", \"thursdayActive\", \"styleClass\", \"primary-blue outlined\", \"binary\", \"true\", \"label\", \"\", 3, \"onChange\"], [\"formArrayName\", \"thu\"], [\"name\", \"fridayActive\", \"formControlName\", \"fridayActive\", \"styleClass\", \"primary-blue outlined\", \"binary\", \"true\", \"label\", \"\", 3, \"onChange\"], [\"formArrayName\", \"fri\"], [\"name\", \"saturdayActive\", \"formControlName\", \"saturdayActive\", \"styleClass\", \"primary-blue outlined\", \"binary\", \"true\", \"label\", \"\", 3, \"onChange\"], [\"formArrayName\", \"sat\"], [\"name\", \"sundayActive\", \"formControlName\", \"sundayActive\", \"styleClass\", \"primary-blue outlined\", \"binary\", \"true\", \"label\", \"\", 3, \"onChange\"], [\"formArrayName\", \"sun\"], [\"class\", \"availability grid\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"col-12\", \"md:col-3\", \"lg:col-3\"], [1, \"availability-title\"], [2, \"position\", \"relative\"], [3, \"formGroupName\"], [1, \"input-fields\", 3, \"ngClass\"], [1, \"input-field\", 3, \"ngClass\"], [\"class\", \"more-arrow\", \"src\", \"/assets/icons/availability-day-more-arrow.svg\", \"alt\", \"availability-day-more\", 4, \"ngIf\"], [\"readonlyInput\", \"true\", \"formControlName\", \"from\", \"placeholder\", \"e.g. 11:00\", 3, \"onSelect\", \"inputStyleClass\", \"stepMinute\", \"timeOnly\", \"defaultDate\"], [\"readonlyInput\", \"true\", \"formControlName\", \"to\", \"placeholder\", \"e.g. 14:00\", 3, \"onSelect\", \"inputStyleClass\", \"stepMinute\", \"timeOnly\", \"defaultDate\"], [\"pButton\", \"\", \"type\", \"button\", \"class\", \"p-button-raised p-button-rounded plus-btn-circle addDayIcon\", \"icon\", \"pi pi-plus\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"pButton\", \"\", \"type\", \"button\", \"class\", \"p-button-raised p-button-rounded minus-btn-circle addDayIcon\", \"icon\", \"pi pi-minus\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"src\", \"/assets/icons/availability-day-more-arrow.svg\", \"alt\", \"availability-day-more\", 1, \"more-arrow\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-plus\", 1, \"p-button-raised\", \"p-button-rounded\", \"plus-btn-circle\", \"addDayIcon\", 3, \"click\", \"ngClass\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-minus\", 1, \"p-button-raised\", \"p-button-rounded\", \"minus-btn-circle\", \"addDayIcon\", 3, \"click\", \"ngClass\"], [1, \"input-error\"], [1, \"availability\", \"grid\"], [1, \"col-12\", \"md:col-9\", \"lg:col-9\"], [1, \"input-fields\", \"my-dropdown\"], [1, \"input-field\"], [\"autocomplete\", \"off\", \"placeholder\", \"-\", \"formControlName\", \"hours\", \"styleClass\", \"dropdown-blue hours-dropdown m-t-0\", 3, \"onChange\", \"options\"], [\"pTemplate\", \"item\"], [1, \"country-item\"], [1, \"country-name\"], [1, \"flex\", \"justify-content-center\", \"my-3\"], [\"icon\", \"pi pi-calendar\", \"iconPos\", \"left\", \"pButton\", \"\", \"type\", \"button\", \"label\", \"Schedule\", 1, \"mt-2\", \"p-button-sm\", 3, \"click\"], [1, \"btns\", \"md:ml-5\", \"md:mt-4\"], [\"pRipple\", \"\", 1, \"rounded-blue-button\", \"transparent\", 3, \"click\"], [\"src\", \"/assets/icons/arrow-left-blue.svg\"], [\"pRipple\", \"\", 1, \"rounded-blue-button\", 3, \"click\"], [\"src\", \"/assets/icons/arrow-right.svg\"]],\n    template: function AvailabilityComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"form\", 1)(2, \"div\", 2);\n        i0.ɵɵtemplate(3, AvailabilityComponent_div_3_Template, 3, 0, \"div\", 3);\n        i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6);\n        i0.ɵɵtext(7, \" Day \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 7);\n        i0.ɵɵtext(9, \" Active \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9);\n        i0.ɵɵtext(12, \" Starts \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"div\", 9);\n        i0.ɵɵtext(14, \" Ends \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(15, \"div\", 10)(16, \"div\", 11)(17, \"div\", 12);\n        i0.ɵɵtext(18, \" Monday \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(19, \"div\", 13)(20, \"div\", 14)(21, \"p-checkbox\", 15);\n        i0.ɵɵlistener(\"onChange\", function AvailabilityComponent_Template_p_checkbox_onChange_21_listener($event) {\n          return ctx.initCheckboxChangedListener($event, ctx.mon, \"mon\");\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(22, \"div\", 16)(23, \"div\", 17)(24, \"div\", 18);\n        i0.ɵɵelementContainerStart(25, 19);\n        i0.ɵɵtemplate(26, AvailabilityComponent_div_26_Template, 10, 21, \"div\", 20)(27, AvailabilityComponent_div_27_Template, 2, 0, \"div\", 21);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(28, \"div\", 10)(29, \"div\", 11)(30, \"div\", 12);\n        i0.ɵɵtext(31, \" Tuesday \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(32, \"div\", 13)(33, \"div\", 14)(34, \"p-checkbox\", 22);\n        i0.ɵɵlistener(\"onChange\", function AvailabilityComponent_Template_p_checkbox_onChange_34_listener($event) {\n          return ctx.initCheckboxChangedListener($event, ctx.tue, \"tue\");\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(35, \"div\", 23)(36, \"div\", 24)(37, \"div\", 25);\n        i0.ɵɵelementContainerStart(38, 26);\n        i0.ɵɵtemplate(39, AvailabilityComponent_div_39_Template, 10, 21, \"div\", 20)(40, AvailabilityComponent_div_40_Template, 2, 0, \"div\", 21);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(41, \"div\", 10)(42, \"div\", 11)(43, \"div\", 12);\n        i0.ɵɵtext(44, \" Wednesday \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(45, \"div\", 13)(46, \"div\", 14)(47, \"p-checkbox\", 27);\n        i0.ɵɵlistener(\"onChange\", function AvailabilityComponent_Template_p_checkbox_onChange_47_listener($event) {\n          return ctx.initCheckboxChangedListener($event, ctx.wed, \"wed\");\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(48, \"div\", 28)(49, \"div\", 24)(50, \"div\", 18);\n        i0.ɵɵelementContainerStart(51, 29);\n        i0.ɵɵtemplate(52, AvailabilityComponent_div_52_Template, 10, 21, \"div\", 20)(53, AvailabilityComponent_div_53_Template, 2, 0, \"div\", 21);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(54, \"div\", 10)(55, \"div\", 11)(56, \"div\", 12);\n        i0.ɵɵtext(57, \" Thursday \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(58, \"div\", 13)(59, \"div\", 14)(60, \"p-checkbox\", 30);\n        i0.ɵɵlistener(\"onChange\", function AvailabilityComponent_Template_p_checkbox_onChange_60_listener($event) {\n          return ctx.initCheckboxChangedListener($event, ctx.thu, \"thu\");\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(61, \"div\", 28)(62, \"div\", 24)(63, \"div\", 18);\n        i0.ɵɵelementContainerStart(64, 31);\n        i0.ɵɵtemplate(65, AvailabilityComponent_div_65_Template, 10, 21, \"div\", 20)(66, AvailabilityComponent_div_66_Template, 2, 0, \"div\", 21);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(67, \"div\", 10)(68, \"div\", 11)(69, \"div\", 12);\n        i0.ɵɵtext(70, \" Friday \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(71, \"div\", 13)(72, \"div\", 14)(73, \"p-checkbox\", 32);\n        i0.ɵɵlistener(\"onChange\", function AvailabilityComponent_Template_p_checkbox_onChange_73_listener($event) {\n          return ctx.initCheckboxChangedListener($event, ctx.fri, \"fri\");\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(74, \"div\", 28)(75, \"div\", 24)(76, \"div\", 18);\n        i0.ɵɵelementContainerStart(77, 33);\n        i0.ɵɵtemplate(78, AvailabilityComponent_div_78_Template, 10, 21, \"div\", 20)(79, AvailabilityComponent_div_79_Template, 2, 0, \"div\", 21);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(80, \"div\", 10)(81, \"div\", 11)(82, \"div\", 12);\n        i0.ɵɵtext(83, \" Saturday \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(84, \"div\", 13)(85, \"div\", 14)(86, \"p-checkbox\", 34);\n        i0.ɵɵlistener(\"onChange\", function AvailabilityComponent_Template_p_checkbox_onChange_86_listener($event) {\n          return ctx.initCheckboxChangedListener($event, ctx.sat, \"sat\");\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(87, \"div\", 28)(88, \"div\", 24)(89, \"div\", 18);\n        i0.ɵɵelementContainerStart(90, 35);\n        i0.ɵɵtemplate(91, AvailabilityComponent_div_91_Template, 10, 21, \"div\", 20)(92, AvailabilityComponent_div_92_Template, 2, 0, \"div\", 21);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(93, \"div\", 10)(94, \"div\", 11)(95, \"div\", 12);\n        i0.ɵɵtext(96, \" Sunday \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(97, \"div\", 13)(98, \"div\", 14)(99, \"p-checkbox\", 36);\n        i0.ɵɵlistener(\"onChange\", function AvailabilityComponent_Template_p_checkbox_onChange_99_listener($event) {\n          return ctx.initCheckboxChangedListener($event, ctx.sun, \"sun\");\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(100, \"div\", 28)(101, \"div\", 24)(102, \"div\", 18);\n        i0.ɵɵelementContainerStart(103, 37);\n        i0.ɵɵtemplate(104, AvailabilityComponent_div_104_Template, 10, 21, \"div\", 20)(105, AvailabilityComponent_div_105_Template, 2, 0, \"div\", 21);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵtemplate(106, AvailabilityComponent_div_106_Template, 10, 2, \"div\", 38);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(107, AvailabilityComponent_ng_container_107_Template, 3, 0, \"ng-container\", 39)(108, AvailabilityComponent_ng_container_108_Template, 9, 2, \"ng-container\", 39);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        let tmp_4_0;\n        let tmp_8_0;\n        let tmp_9_0;\n        let tmp_13_0;\n        let tmp_17_0;\n        let tmp_21_0;\n        let tmp_25_0;\n        let tmp_29_0;\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"formGroup\", ctx.form);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isDialog);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(35, _c0, !ctx.isDialog, ctx.isDialog));\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(38, _c1, ctx.isDialog, !ctx.isDialog));\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(41, _c2, !((tmp_4_0 = ctx.form.get(\"mondayActive\")) == null ? null : tmp_4_0.value)));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", ctx.mon.controls);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.ifFieldValid(\"mon\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(43, _c1, ctx.isDialog, !ctx.isDialog));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"pTooltip\", !((tmp_8_0 = ctx.form.get(\"tuesdayActive\")) == null ? null : tmp_8_0.value) ? \"Tuesday must be active to edit time.\" : \"\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(46, _c2, !((tmp_9_0 = ctx.form.get(\"tuesdayActive\")) == null ? null : tmp_9_0.value)));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.tue.controls);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.ifFieldValid(\"tue\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(48, _c1, ctx.isDialog, !ctx.isDialog));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(51, _c2, !((tmp_13_0 = ctx.form.get(\"wednesdayActive\")) == null ? null : tmp_13_0.value)));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngForOf\", ctx.wed.controls);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.ifFieldValid(\"wed\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(53, _c1, ctx.isDialog, !ctx.isDialog));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(56, _c2, !((tmp_17_0 = ctx.form.get(\"thursdayActive\")) == null ? null : tmp_17_0.value)));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngForOf\", ctx.thu.controls);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.ifFieldValid(\"thu\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(58, _c1, ctx.isDialog, !ctx.isDialog));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(61, _c2, !((tmp_21_0 = ctx.form.get(\"fridayActive\")) == null ? null : tmp_21_0.value)));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngForOf\", ctx.fri.controls);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.ifFieldValid(\"fri\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(63, _c1, ctx.isDialog, !ctx.isDialog));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(66, _c2, !((tmp_25_0 = ctx.form.get(\"saturdayActive\")) == null ? null : tmp_25_0.value)));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngForOf\", ctx.sat.controls);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.ifFieldValid(\"sat\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(68, _c1, ctx.isDialog, !ctx.isDialog));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(71, _c2, !((tmp_29_0 = ctx.form.get(\"sundayActive\")) == null ? null : tmp_29_0.value)));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngForOf\", ctx.sun.controls);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.ifFieldValid(\"sun\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showHours);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isDialog);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isDialog);\n      }\n    },\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.modal[_ngcontent-%COMP%] {\\n  min-width: 50vw;\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown:not(.p-disabled):hover {\\n  border: 2px solid rgb(228, 185, 84);\\n}\\n\\n  .no-label > .p-button-label {\\n  display: none;\\n}\\n\\ninput[type=file][_ngcontent-%COMP%] {\\n  visibility: hidden;\\n  width: 10px;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%] {\\n  position: relative;\\n  padding: 10px;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%]::-webkit-calendar-picker-indicator {\\n  color: transparent;\\n  background: none;\\n  z-index: 1;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%]:before {\\n  color: transparent;\\n  background: none;\\n  display: block;\\n  font-family: \\\"FontAwesome\\\";\\n  content: \\\"\\\\f073\\\";\\n  \\n\\n  width: 20px;\\n  height: 25px;\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  color: #999;\\n}\\n\\n.image-preview[_ngcontent-%COMP%] {\\n  height: 10rem;\\n  margin: 1rem 0;\\n  border-radius: 50%;\\n  border: 3px solid var(--main-color);\\n}\\n\\n.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 100%;\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-header {\\n  padding: clamp(0.38rem, 0.47vw + 0.28rem, 0.75rem);\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-items .p-dropdown-item {\\n  margin-bottom: clamp(0.38rem, 0.47vw + 0.28rem, 0.75rem);\\n}\\n\\n.country-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: clamp(0.75rem, 0.31vw + 0.69rem, 1rem);\\n  font-size: clamp(0.88rem, 0.16vw + 0.84rem, 1rem);\\n}\\n.country-item[_ngcontent-%COMP%]   .country-name[_ngcontent-%COMP%] {\\n  white-space: pre-line; \\n\\n}\\n.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  \\n\\n\\n\\n\\n\\n  \\n\\n  display: inline-block;\\n  vertical-align: middle;\\n}\\n.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  clip-path: circle();\\n}\\n.country-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 32px;\\n  margin-right: 0.5rem;\\n}\\n.country-item[_ngcontent-%COMP%]   img.smaller[_ngcontent-%COMP%] {\\n  width: 16px;\\n}\\n\\n.info-element[_ngcontent-%COMP%] {\\n  padding: 10px 0;\\n}\\n\\n.btns[_ngcontent-%COMP%]   div[_ngcontent-%COMP%] {\\n  margin: 15px;\\n}\\n\\n.white-button[_ngcontent-%COMP%] {\\n  padding: 10px;\\n}\\n\\n[_nghost-%COMP%]     .p-element .input-element {\\n  height: 35px;\\n  margin-top: 0;\\n  margin-right: 10px;\\n}\\n[_nghost-%COMP%]     .p-element .input-element::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  left: 10px;\\n  top: 0;\\n  bottom: 0;\\n  width: 20px;\\n  background: url(\\\"/assets/icons/wall-clock.svg\\\") center/contain no-repeat;\\n}\\n\\n.availability[_ngcontent-%COMP%] {\\n  padding: 14px 0px 0 0px;\\n  box-sizing: border-box;\\n}\\n.availability[_ngcontent-%COMP%]   .availability-wrapper[_ngcontent-%COMP%] {\\n  padding: 14px;\\n  border-radius: 10px;\\n  box-shadow: 3px 3px 6px 0 rgba(0, 0, 0, 0.16);\\n  background-color: #fff;\\n}\\n.availability[_ngcontent-%COMP%]   .availability-wrapper.narrow[_ngcontent-%COMP%] {\\n  padding: 0;\\n  box-shadow: none;\\n}\\n.availability[_ngcontent-%COMP%]   .availability-wrapper.narrow[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n}\\n.availability[_ngcontent-%COMP%]   .availability-wrapper.narrow[_ngcontent-%COMP%]   .more-arrow[_ngcontent-%COMP%] {\\n  top: 8px;\\n}\\n.availability[_ngcontent-%COMP%]   .availability-title[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  font-weight: bold;\\n  color: var(--main-color);\\n  text-align: left;\\n}\\n.availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  width: 100%;\\n  margin-top: 10px;\\n  font-size: 15px;\\n  align-items: baseline;\\n}\\n@media screen and (max-width: 576px) {\\n  .availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n.availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]   .availability-column-1[_ngcontent-%COMP%] {\\n  width: 20%;\\n  padding: 5px;\\n  box-sizing: border-box;\\n}\\n@media screen and (max-width: 576px) {\\n  .availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]   .availability-column-1[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n.availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]   .day[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n@media screen and (max-width: 576px) {\\n  .availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]   .day[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n}\\n.availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]   .availability-column-2[_ngcontent-%COMP%] {\\n  width: 50%;\\n  padding: 5px;\\n  box-sizing: border-box;\\n}\\n@media only screen and (max-width: 576px) {\\n  .availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]   .availability-column-2[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n.availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]   .column-title[_ngcontent-%COMP%] {\\n  width: 50%;\\n  padding-right: 20px;\\n  box-sizing: border-box;\\n  font-stretch: normal;\\n  font-style: normal;\\n  line-height: normal;\\n  letter-spacing: normal;\\n  text-align: left;\\n}\\n.availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]   .title-color[_ngcontent-%COMP%] {\\n  color: #93949e;\\n}\\n.availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]   .more-arrow[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: -25px;\\n  top: 15px;\\n}\\n@media screen and (max-width: 576px) {\\n  .availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]   .more-arrow[_ngcontent-%COMP%] {\\n    left: -15px;\\n  }\\n}\\n@media screen and (max-width: 576px) {\\n  .availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]   .input-fields[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    display: flex;\\n  }\\n}\\n\\n.e-custom.e-checkbox-wrapper[_ngcontent-%COMP%]   .e-frame[_ngcontent-%COMP%] {\\n  \\n\\n  height: 16px;\\n  line-height: 8px;\\n  width: 16px;\\n  border-radius: 50px;\\n}\\n\\n.e-custom.e-checkbox-wrapper[_ngcontent-%COMP%]   .e-check[_ngcontent-%COMP%] {\\n  \\n\\n  font-size: 10px;\\n  background-color: #2fb9d3;\\n  outline: 2px solid #2fb9d3;\\n  outline-offset: 3px;\\n}\\n\\n.e-custom.e-checkbox-wrapper[_ngcontent-%COMP%]   .e-label[_ngcontent-%COMP%] {\\n  \\n\\n  font-size: 18px;\\n}\\n\\n.add-btn[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: -34px;\\n  top: 0px;\\n  width: 20px;\\n}\\n\\n.add-btn-2[_ngcontent-%COMP%] {\\n  width: 15px;\\n  margin-left: 15px;\\n  width: 15px;\\n}\\n\\n.light-purple-circle-button[_ngcontent-%COMP%] {\\n  width: 10px;\\n  height: 10px;\\n}\\n\\n.form-array-title[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  color: var(--main-color);\\n  font-size: 20px;\\n  margin-top: 30px;\\n  width: 100%;\\n  border-bottom: 1px solid var(--main-color);\\n  padding-bottom: 5px;\\n}\\n\\n.my-dropdown[_ngcontent-%COMP%] {\\n  box-sizing: border-box;\\n}\\n@media screen and (max-width: 1024px) {\\n  .my-dropdown[_ngcontent-%COMP%] {\\n    width: 90% !important;\\n  }\\n}\\n\\n.input-field[_ngcontent-%COMP%] {\\n  margin-top: 0 !important;\\n  font-size: 18px !important;\\n  padding: 0px !important;\\n  box-sizing: border-box !important;\\n  margin-bottom: 6px !important;\\n}\\n@media screen and (max-width: 1024px) {\\n  .input-field[_ngcontent-%COMP%] {\\n    width: 50% !important;\\n    flex-basis: 50% !important;\\n    padding: 0px 0px 0px 10px !important;\\n  }\\n}\\n\\n.input-element-title[_ngcontent-%COMP%] {\\n  text-align: center !important;\\n  font-weight: normal !important;\\n}\\n\\n.input-element[_ngcontent-%COMP%] {\\n  margin-top: 10px !important;\\n  padding: 5px !important;\\n  box-sizing: border-box;\\n  font-size: 15px !important;\\n}\\n\\n.profile-info-section[_ngcontent-%COMP%] {\\n  padding: 0 !important;\\n}\\n\\n.input-error[_ngcontent-%COMP%] {\\n  text-align: center !important;\\n}\\n\\n.trash[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: -34px;\\n  bottom: 0px;\\n  cursor: pointer;\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown.hours-dropdown {\\n  margin-top: 0;\\n}\\n\\n.addDayIcon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: -34px;\\n  bottom: 10px;\\n}\\n\\n.day-title[_ngcontent-%COMP%] {\\n  font-size: 19px;\\n  font-weight: normal;\\n  font-stretch: normal;\\n  font-style: normal;\\n  line-height: normal;\\n  letter-spacing: normal;\\n  text-align: left;\\n  color: #2d2a4b;\\n}\\n\\n@media screen and (max-width: 576px) {\\n  .addDayIcon[_ngcontent-%COMP%] {\\n    position: relative;\\n    right: auto;\\n    width: auto;\\n    bottom: 0;\\n    margin-top: 10px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["UntypedFormArray", "UntypedFormControl", "UntypedFormGroup", "Validators", "UserAvailabilityType", "SubSink", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "AvailabilityComponent_div_26_button_8_Template_button_click_0_listener", "ɵɵrestoreView", "_r4", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "addMon", "ɵɵproperty", "ɵɵpureFunction1", "_c5", "isDialog", "AvailabilityComponent_div_26_button_9_Template_button_click_0_listener", "_r5", "i_r2", "index", "removeMon", "ɵɵelementContainerStart", "ɵɵtemplate", "AvailabilityComponent_div_26_img_4_Template", "AvailabilityComponent_div_26_Template_p_calendar_onSelect_5_listener", "$event", "_r1", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "form", "controls", "mon", "AvailabilityComponent_div_26_Template_p_calendar_onSelect_7_listener", "AvailabilityComponent_div_26_button_8_Template", "AvailabilityComponent_div_26_button_9_Template", "ɵɵadvance", "_c3", "_c4", "defaultDate", "AvailabilityComponent_div_39_button_8_Template_button_click_0_listener", "_r8", "addTue", "AvailabilityComponent_div_39_button_9_Template_button_click_0_listener", "_r9", "i_r7", "removeTue", "AvailabilityComponent_div_39_img_4_Template", "AvailabilityComponent_div_39_Template_p_calendar_onSelect_5_listener", "_r6", "tue", "AvailabilityComponent_div_39_Template_p_calendar_onSelect_7_listener", "AvailabilityComponent_div_39_button_8_Template", "AvailabilityComponent_div_39_button_9_Template", "AvailabilityComponent_div_52_button_8_Template_button_click_0_listener", "_r12", "addWed", "AvailabilityComponent_div_52_button_9_Template_button_click_0_listener", "_r13", "i_r11", "removeWed", "AvailabilityComponent_div_52_img_4_Template", "AvailabilityComponent_div_52_Template_p_calendar_onSelect_5_listener", "_r10", "wed", "AvailabilityComponent_div_52_Template_p_calendar_onSelect_7_listener", "AvailabilityComponent_div_52_button_8_Template", "AvailabilityComponent_div_52_button_9_Template", "AvailabilityComponent_div_65_button_8_Template_button_click_0_listener", "_r16", "addThu", "AvailabilityComponent_div_65_button_9_Template_button_click_0_listener", "_r17", "i_r15", "removeThu", "AvailabilityComponent_div_65_img_4_Template", "AvailabilityComponent_div_65_Template_p_calendar_onSelect_5_listener", "_r14", "thu", "AvailabilityComponent_div_65_Template_p_calendar_onSelect_7_listener", "AvailabilityComponent_div_65_button_8_Template", "AvailabilityComponent_div_65_button_9_Template", "AvailabilityComponent_div_78_button_8_Template_button_click_0_listener", "_r20", "add<PERSON><PERSON>", "AvailabilityComponent_div_78_button_9_Template_button_click_0_listener", "_r21", "i_r19", "remove<PERSON><PERSON>", "AvailabilityComponent_div_78_img_4_Template", "AvailabilityComponent_div_78_Template_p_calendar_onSelect_5_listener", "_r18", "fri", "AvailabilityComponent_div_78_Template_p_calendar_onSelect_7_listener", "AvailabilityComponent_div_78_button_8_Template", "AvailabilityComponent_div_78_button_9_Template", "AvailabilityComponent_div_91_button_8_Template_button_click_0_listener", "_r24", "addSat", "AvailabilityComponent_div_91_button_9_Template_button_click_0_listener", "_r25", "i_r23", "removeSat", "AvailabilityComponent_div_91_img_4_Template", "AvailabilityComponent_div_91_Template_p_calendar_onSelect_5_listener", "_r22", "sat", "AvailabilityComponent_div_91_Template_p_calendar_onSelect_7_listener", "AvailabilityComponent_div_91_button_8_Template", "AvailabilityComponent_div_91_button_9_Template", "AvailabilityComponent_div_104_button_8_Template_button_click_0_listener", "_r28", "addSun", "AvailabilityComponent_div_104_button_9_Template_button_click_0_listener", "_r29", "i_r27", "removeSun", "AvailabilityComponent_div_104_img_4_Template", "AvailabilityComponent_div_104_Template_p_calendar_onSelect_5_listener", "_r26", "sun", "AvailabilityComponent_div_104_Template_p_calendar_onSelect_7_listener", "AvailabilityComponent_div_104_button_8_Template", "AvailabilityComponent_div_104_button_9_Template", "ɵɵtextInterpolate", "time_r31", "AvailabilityComponent_div_106_Template_p_dropdown_onChange_7_listener", "_r30", "onHoursChange", "AvailabilityComponent_div_106_ng_template_8_Template", "AvailabilityComponent_div_106_div_9_Template", "hourOptions", "ifFieldValid", "AvailabilityComponent_ng_container_107_Template_button_click_2_listener", "_r32", "onSubmit", "AvailabilityComponent_ng_container_108_Template_button_click_2_listener", "_r33", "goBack", "AvailabilityComponent_ng_container_108_Template_button_click_5_listener", "AvailabilityComponent_ng_container_108_span_6_Template", "AvailabilityComponent_ng_container_108_span_7_Template", "user", "AvailabilityComponent", "constructor", "fb", "router", "location", "teacherService", "userService", "toast", "authService", "calendarService", "generalService", "showHours", "subs", "weekDays", "teacherAvailabilityMon", "teacherAvailabilityTue", "teacherAvailabilityWed", "teacherAvailabilityThu", "teacherAvailabilityFri", "teacherAvailabilitySat", "teacherAvailabilitySun", "selectedHours", "tryToSave", "Date", "ngOnInit", "setCurrentStepIndex", "add", "updateAvailabilityListener", "subscribe", "res", "teacher", "getLoggedInUser", "dummy<PERSON><PERSON><PERSON>", "teacherAvailability", "availability", "mondayActive", "tuesdayActive", "wednesdayActive", "thursdayActive", "fridayActive", "saturdayActive", "sundayActive", "hours", "hours_per_week", "validators", "required", "submitOnMenuClickListener", "getUserAvailability", "aspUserId", "updateDaysFields", "getTeacherApplicationStep5", "ngOnDestroy", "unsubscribe", "setSubmitOnMenuClickListener", "formArray", "i", "property", "dd", "timeConverted", "toLocaleString", "hour12", "hour", "minute", "timeArray", "split", "console", "log", "at", "patchValue", "from", "to", "get", "hasEmptyFromOrTo", "value", "m", "push", "for<PERSON>ach", "element", "group", "length", "initCheckboxChangedListener", "event", "<PERSON><PERSON><PERSON><PERSON>", "dayActive", "checked", "setControl", "array", "removeDay", "name", "processDay", "day", "toLowerCase", "list", "dayString", "toString", "control", "removeAt", "id", "type", "AVAILABLE", "days", "activeDay", "castedDay", "Array", "isArray", "availabilitySlot", "activeDayFormValue", "updateTeacherAvailability", "setShowToastmessage", "severity", "summary", "detail", "setToggleOverlayListener", "setUpdateListener", "updateAPITeacherApplicationStep5", "navigateByUrl", "replaceUrl", "updateUserListener", "updateAvailability", "localStorage", "setItem", "JSON", "stringify", "setUpdateAvailabilityForSchedulerClickListener", "checkFormat", "isFromValid", "indexOf", "isToValid", "reload", "url", "_this", "_asyncToGenerator", "skipLocationChange", "setTimeout", "checkValidity", "time", "parseInt", "minutes", "field", "isNotValid", "c", "touched", "isNaN", "invalid", "data", "isNullishObject", "item", "trim", "_", "ɵɵdirectiveInject", "i1", "UntypedFormBuilder", "i2", "Router", "i3", "Location", "i4", "TeacherApplicationService", "i5", "UserService", "i6", "ToastService", "i7", "AuthService", "i8", "CalendarService", "i9", "GeneralService", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "AvailabilityComponent_Template", "rf", "ctx", "AvailabilityComponent_div_3_Template", "AvailabilityComponent_Template_p_checkbox_onChange_21_listener", "AvailabilityComponent_div_26_Template", "AvailabilityComponent_div_27_Template", "AvailabilityComponent_Template_p_checkbox_onChange_34_listener", "AvailabilityComponent_div_39_Template", "AvailabilityComponent_div_40_Template", "AvailabilityComponent_Template_p_checkbox_onChange_47_listener", "AvailabilityComponent_div_52_Template", "AvailabilityComponent_div_53_Template", "AvailabilityComponent_Template_p_checkbox_onChange_60_listener", "AvailabilityComponent_div_65_Template", "AvailabilityComponent_div_66_Template", "AvailabilityComponent_Template_p_checkbox_onChange_73_listener", "AvailabilityComponent_div_78_Template", "AvailabilityComponent_div_79_Template", "AvailabilityComponent_Template_p_checkbox_onChange_86_listener", "AvailabilityComponent_div_91_Template", "AvailabilityComponent_div_92_Template", "AvailabilityComponent_Template_p_checkbox_onChange_99_listener", "AvailabilityComponent_div_104_Template", "AvailabilityComponent_div_105_Template", "AvailabilityComponent_div_106_Template", "AvailabilityComponent_ng_container_107_Template", "AvailabilityComponent_ng_container_108_Template", "ɵɵpureFunction2", "_c0", "_c1", "_c2", "tmp_4_0", "tmp_8_0", "tmp_9_0", "tmp_13_0", "tmp_17_0", "tmp_21_0", "tmp_25_0", "tmp_29_0"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\availability\\availability.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\availability\\availability.component.html"], "sourcesContent": ["import { Location } from '@angular/common';\r\nimport { Component, Input, OnInit } from '@angular/core';\r\nimport { Form, UntypedFormArray, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { AvailabilityTime, Teacher } from 'src/app/core/models/teacher.model';\r\nimport { User, UserAvailability, UserAvailabilityHours, UserAvailabilityType } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { CalendarService } from 'src/app/core/services/calendar.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { TeacherApplicationService } from 'src/app/core/services/teacher-application.service';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { UserService } from 'src/app/core/services/user.service';\r\nimport { SubSink } from 'subsink';\r\n\r\n@Component({\r\n  selector: 'app-availability',\r\n  templateUrl: './availability.component.html',\r\n  styleUrls: ['./availability.component.scss']\r\n})\r\nexport class AvailabilityComponent implements OnInit {\r\n  @Input() user: User = {} as User;\r\n  @Input() showHours: boolean = true;\r\n  @Input() isDialog: boolean = false;\r\n  private subs = new SubSink();\r\n  public form: UntypedFormGroup = new UntypedFormGroup({});\r\n  public teacher?: Teacher | User;\r\n  weekDays = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];\r\n\r\n  public teacherAvailability?: UserAvailability;\r\n  public teacherAvailabilityMon: { from: string, to: string }[] = [];\r\n  public teacherAvailabilityTue: { from: string, to: string }[] = [];\r\n  public teacherAvailabilityWed: { from: string, to: string }[] = [];\r\n  public teacherAvailabilityThu: { from: string, to: string }[] = [];\r\n  public teacherAvailabilityFri: { from: string, to: string }[] = [];\r\n  public teacherAvailabilitySat: { from: string, to: string }[] = [];\r\n  public teacherAvailabilitySun: { from: string, to: string }[] = [];\r\n\r\n  public hourOptions: string[] = ['5-10', '10-15', '15-20', '20-25', '25-30', '30-35', '35-40']\r\n  public selectedHours: string = \"5-10\";\r\n\r\n  public tryToSave: boolean = false;\r\n  defaultDate: Date = new Date(\"January 31 2024 00:00\");\r\n  constructor(\r\n    private fb: UntypedFormBuilder,\r\n    private router: Router,\r\n    private location: Location,\r\n    private teacherService: TeacherApplicationService,\r\n    private userService: UserService,\r\n    private toast: ToastService,\r\n    private authService: AuthService,\r\n    private calendarService: CalendarService,\r\n    private generalService: GeneralService,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.teacherService.setCurrentStepIndex(4);\r\n    this.subs.add(this.teacherService.updateAvailabilityListener.subscribe(res => {\r\n      this.teacherAvailabilityMon = [];\r\n      this.teacherAvailabilityTue = [];\r\n      this.teacherAvailabilityWed = [];\r\n      this.teacherAvailabilityThu = [];\r\n      this.teacherAvailabilityFri = [];\r\n      this.teacherAvailabilitySat = [];\r\n      this.teacherAvailabilitySun = [];\r\n      this.teacher = this.user ? this.authService.getLoggedInUser()! : this.teacherService.dummyTeacher;\r\n      this.teacherAvailability = this.teacher?.availability;\r\n      this.form = new UntypedFormGroup({\r\n        mon: new UntypedFormArray([]),\r\n        tue: new UntypedFormArray([]),\r\n        wed: new UntypedFormArray([]),\r\n        thu: new UntypedFormArray([]),\r\n        fri: new UntypedFormArray([]),\r\n        sat: new UntypedFormArray([]),\r\n        sun: new UntypedFormArray([]),\r\n        mondayActive: new UntypedFormControl(false),\r\n        tuesdayActive: new UntypedFormControl(false),\r\n        wednesdayActive: new UntypedFormControl(false),\r\n        thursdayActive: new UntypedFormControl(false),\r\n        fridayActive: new UntypedFormControl(false),\r\n        saturdayActive: new UntypedFormControl(false),\r\n        sundayActive: new UntypedFormControl(false),\r\n        hours: new UntypedFormControl(this.teacher?.hours_per_week ? this.teacher?.hours_per_week : this.hourOptions[0], {\r\n          validators: [Validators.required]\r\n        }),\r\n      });\r\n\r\n    }));\r\n\r\n\r\n    this.subs.add(this.teacherService.submitOnMenuClickListener.subscribe(res => {\r\n      if (res) {\r\n        this.onSubmit();\r\n      }\r\n    }));\r\n\r\n    if (this.isDialog) {\r\n      this.userService.getUserAvailability(this.user.aspUserId!).subscribe((res: AvailabilityTime) => {\r\n        if (res) {\r\n          this.updateDaysFields(res);\r\n        }\r\n      });\r\n    } else {\r\n      this.subs.add(this.teacherService.getTeacherApplicationStep5().subscribe((res: AvailabilityTime) => {\r\n        if (res) {\r\n          this.updateDaysFields(res);\r\n        }\r\n      }));\r\n    }\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n    this.teacherService.setSubmitOnMenuClickListener(false)\r\n  }\r\n\r\n  /**\r\n   * Handle the change event of the time picker control\r\n   * @param {any} $event - The change event data\r\n   * @param {FormArray} formArray - The form array that contains the time picker control\r\n   * @param {any} i - The index of the control in the form array\r\n   * @param {string} property - The property name to be updated with the selected time\r\n   */\r\n  timePickChanged($event: any, formArray: UntypedFormArray, i: any, property: string) {\r\n    const dd = new Date($event);\r\n\r\n    const timeConverted: Record<string, string> = {} as Record<string, string>;\r\n    timeConverted[property] = dd.toLocaleString('en-US', {\r\n      hour12: false,\r\n      hour: '2-digit',\r\n      minute: '2-digit',\r\n    });\r\n    const timeArray = timeConverted[property].split(\":\");\r\n    console.log(timeArray);\r\n    if (timeArray[0] <= '23') {\r\n      console.log(dd)\r\n      console.log(timeConverted);\r\n      let controls = this.form.controls;\r\n      formArray.at(i).patchValue(\r\n        timeConverted\r\n      )\r\n      \r\n      return false;\r\n    } else {\r\n      formArray.at(i).patchValue(\r\n        {from: '00:00', to: '00:'+timeArray[1]}\r\n      )\r\n      return false;\r\n      \r\n    }\r\n  }\r\n\r\n  updateDaysFields(availability: any) {\r\n\r\n    this.form.get('mondayActive')?.patchValue(availability.mondayActive || !this.hasEmptyFromOrTo(availability!.mon));\r\n    this.form.get('tuesdayActive')?.patchValue(availability.tuesdayActive || !this.hasEmptyFromOrTo(availability!.tue));\r\n    this.form.get('wednesdayActive')?.patchValue(availability.wednesdayActive || !this.hasEmptyFromOrTo(availability!.wed));\r\n    this.form.get('thursdayActive')?.patchValue(availability.thursdayActive || !this.hasEmptyFromOrTo(availability!.thu));\r\n    this.form.get('fridayActive')?.patchValue(availability.fridayActive || !this.hasEmptyFromOrTo(availability!.fri));\r\n    this.form.get('saturdayActive')?.patchValue(availability.saturdayActive || !this.hasEmptyFromOrTo(availability!.sat));\r\n    this.form.get('sundayActive')?.patchValue(availability.sundayActive || !this.hasEmptyFromOrTo(availability!.sun));\r\n    this.form.get('hours')?.patchValue(availability.hours);\r\n\r\n    const mondayActive = this.form.get('mondayActive')?.value;\r\n    const tuesdayActive = this.form.get('tuesdayActive')?.value;\r\n    const wednesdayActive = this.form.get('wednesdayActive')?.value;\r\n    const thursdayActive = this.form.get('thursdayActive')?.value;\r\n    const fridayActive = this.form.get('fridayActive')?.value;\r\n    const saturdayActive = this.form.get('saturdayActive')?.value;\r\n    const sundayActive = this.form.get('sundayActive')?.value;\r\n\r\n\r\n    if (availability?.mon && mondayActive) {\r\n      for (let m of availability!.mon) {\r\n        this.teacherAvailabilityMon.push(\r\n          {\r\n            from: m.from,\r\n            to: m.to\r\n          }\r\n        )\r\n      }\r\n    }\r\n\r\n    if (availability?.tue && tuesdayActive) {\r\n      for (let m of availability!.tue) {\r\n        this.teacherAvailabilityTue.push(\r\n          {\r\n            from: m.from,\r\n            to: m.to\r\n          }\r\n        )\r\n      }\r\n    }\r\n\r\n    if (availability?.wed && wednesdayActive) {\r\n      for (let m of availability!.wed) {\r\n        this.teacherAvailabilityWed.push(\r\n          {\r\n            from: m.from,\r\n            to: m.to\r\n          }\r\n        )\r\n      }\r\n    }\r\n\r\n    if (availability?.thu && thursdayActive) {\r\n      for (let m of availability!.thu) {\r\n        this.teacherAvailabilityThu.push(\r\n          {\r\n            from: m.from,\r\n            to: m.to\r\n          }\r\n        )\r\n      }\r\n    }\r\n\r\n    if (availability?.fri && fridayActive) {\r\n      for (let m of availability!.fri) {\r\n        this.teacherAvailabilityFri.push(\r\n          {\r\n            from: m.from,\r\n            to: m.to\r\n          }\r\n        )\r\n      }\r\n    }\r\n\r\n    if (availability?.sat && saturdayActive) {\r\n      for (let m of availability!.sat) {\r\n        this.teacherAvailabilitySat.push(\r\n          {\r\n            from: m.from,\r\n            to: m.to\r\n          }\r\n        )\r\n      }\r\n    }\r\n\r\n    if (availability?.sun && sundayActive) {\r\n      for (let m of availability!.sun) {\r\n        this.teacherAvailabilitySun.push(\r\n          {\r\n            from: m.from,\r\n            to: m.to\r\n          }\r\n        )\r\n      }\r\n    }\r\n\r\n    this.teacherAvailabilityMon?.forEach(element => {\r\n      this.mon.push(this.fb.group(element))\r\n    });\r\n\r\n    if (this.teacherAvailabilityMon.length === 0) {\r\n      this.addMon()\r\n    }\r\n\r\n    this.teacherAvailabilityTue?.forEach(element => {\r\n      this.tue.push(this.fb.group(element))\r\n    });\r\n\r\n    if (this.teacherAvailabilityTue?.length === 0) {\r\n      this.addTue()\r\n    }\r\n\r\n    this.teacherAvailabilityWed?.forEach(element => {\r\n      this.wed.push(this.fb.group(element))\r\n    });\r\n\r\n    if (this.teacherAvailabilityWed?.length === 0) {\r\n      this.addWed()\r\n    }\r\n\r\n    this.teacherAvailabilityThu?.forEach(element => {\r\n      this.thu.push(this.fb.group(element))\r\n    });\r\n\r\n    if (this.teacherAvailabilityThu?.length === 0) {\r\n      this.addThu()\r\n    }\r\n\r\n    this.teacherAvailabilityFri?.forEach(element => {\r\n      this.fri.push(this.fb.group(element))\r\n    });\r\n\r\n    if (this.teacherAvailabilityFri?.length === 0) {\r\n      this.addFri()\r\n    }\r\n\r\n    this.teacherAvailabilitySat?.forEach(element => {\r\n      this.sat.push(this.fb.group(element))\r\n    });\r\n\r\n    if (this.teacherAvailabilitySat?.length === 0) {\r\n      this.addSat()\r\n    }\r\n\r\n    this.teacherAvailabilitySun?.forEach(element => {\r\n      this.sun.push(this.fb.group(element))\r\n    });\r\n\r\n    if (this.teacherAvailabilitySun.length === 0) {\r\n      this.addSun()\r\n    }\r\n  }\r\n\r\n  initCheckboxChangedListener(event: any, dayParam: any, dayActive: any) {\r\n    if (!event.checked) {\r\n      dayParam.controls?.forEach((element: any, index: number) => {\r\n        console.log(element);\r\n        const group = new UntypedFormGroup({\r\n          from: new UntypedFormControl(''),\r\n          to: new UntypedFormControl('')\r\n        });\r\n        this.form.setControl(dayActive, this.fb.array([group]));\r\n      });\r\n\r\n    }\r\n  }\r\n\r\n  removeDay(name: string, index: number) {\r\n    switch (name) {\r\n      case 'mon':\r\n        this.removeMon(index);\r\n        break;\r\n      case 'tue':\r\n        this.removeTue(index);\r\n        break;\r\n      case 'wed':\r\n        this.removeWed(index);\r\n        break;\r\n      case 'thu':\r\n        this.removeThu(index);\r\n        break;\r\n      case 'fri':\r\n        this.removeFri(index);\r\n        break;\r\n      case 'sat':\r\n        this.removeSat(index);\r\n        break;\r\n      case 'sun':\r\n        this.removeSun(index);\r\n        break;\r\n    }\r\n  }\r\n  // TODO: unused, see if can be used, else delete\r\n  processDay(day: string) {\r\n    day = day.toLowerCase();\r\n    const availability = [`teacherAvailability${day}`];\r\n    let list: UntypedFormGroup[] = [];\r\n    availability?.forEach(element => {\r\n      const group = new UntypedFormGroup({\r\n        from: new UntypedFormControl(''),\r\n        to: new UntypedFormControl('')\r\n      });\r\n      list.push(this.fb.group(group));\r\n    });\r\n  }\r\n\r\n  // TODO: unused, see if can be used, else delete\r\n  add(day: string) {\r\n    const dayString = day.toString();\r\n    const control = this.form.get(dayString.toLowerCase()) as UntypedFormArray;\r\n    const group = new UntypedFormGroup({\r\n      from: new UntypedFormControl(''),\r\n      to: new UntypedFormControl('')\r\n    });\r\n    (control).push(group);\r\n  }\r\n\r\n  addMon() {\r\n    const group = new UntypedFormGroup({\r\n      from: new UntypedFormControl(''),\r\n      to: new UntypedFormControl('')\r\n    });\r\n    this.mon.push(group);\r\n  }\r\n\r\n  removeMon(index: number) {\r\n    console.log('remove',index);\r\n    this.mon.removeAt(index);\r\n  }\r\n\r\n  addTue() {\r\n    const group = new UntypedFormGroup({\r\n      from: new UntypedFormControl(''),\r\n      to: new UntypedFormControl('')\r\n    });\r\n    this.tue.push(group);\r\n  }\r\n\r\n  removeTue(index: number) {\r\n    this.tue.removeAt(index);\r\n  }\r\n\r\n  addWed() {\r\n    const group = new UntypedFormGroup({\r\n      from: new UntypedFormControl(''),\r\n      to: new UntypedFormControl('')\r\n    });\r\n    this.wed.push(group);\r\n  }\r\n\r\n  removeWed(index: number) {\r\n    this.wed.removeAt(index);\r\n  }\r\n\r\n  addThu() {\r\n    const group = new UntypedFormGroup({\r\n      from: new UntypedFormControl(''),\r\n      to: new UntypedFormControl('')\r\n    });\r\n    this.thu.push(group);\r\n  }\r\n\r\n  removeThu(index: number) {\r\n    this.thu.removeAt(index);\r\n  }\r\n\r\n  addFri() {\r\n    const group = new UntypedFormGroup({\r\n      from: new UntypedFormControl(''),\r\n      to: new UntypedFormControl('')\r\n    });\r\n    this.fri.push(group);\r\n  }\r\n\r\n  removeFri(index: number) {\r\n    this.fri.removeAt(index);\r\n  }\r\n\r\n  addSat() {\r\n    const group = new UntypedFormGroup({\r\n      from: new UntypedFormControl(''),\r\n      to: new UntypedFormControl('')\r\n    });\r\n    this.sat.push(group);\r\n  }\r\n\r\n  removeSat(index: number) {\r\n    this.sat.removeAt(index);\r\n  }\r\n\r\n  addSun() {\r\n    const group = new UntypedFormGroup({\r\n      from: new UntypedFormControl(''),\r\n      to: new UntypedFormControl('')\r\n    });\r\n    this.sun.push(group);\r\n  }\r\n\r\n  removeSun(index: number) {\r\n    this.sun.removeAt(index);\r\n  }\r\n\r\n  get mon(): UntypedFormArray {\r\n    return this.form.get('mon') as UntypedFormArray;\r\n  }\r\n\r\n  get tue(): UntypedFormArray {\r\n    return this.form.get('tue') as UntypedFormArray;\r\n  }\r\n\r\n  get wed(): UntypedFormArray {\r\n    return this.form.get('wed') as UntypedFormArray;\r\n  }\r\n\r\n  get thu(): UntypedFormArray {\r\n    return this.form.get('thu') as UntypedFormArray;\r\n  }\r\n\r\n  get fri(): UntypedFormArray {\r\n    return this.form.get('fri') as UntypedFormArray;\r\n  }\r\n\r\n  get sat(): UntypedFormArray {\r\n    return this.form.get('sat') as UntypedFormArray;\r\n  }\r\n\r\n  get sun(): UntypedFormArray {\r\n    return this.form.get('sun') as UntypedFormArray;\r\n  }\r\n\r\n  onSubmit() {\r\n    this.tryToSave = true;\r\n\r\n    // if (!this.form.valid) {\r\n    //   return;\r\n    // }\r\n\r\n    let availability: UserAvailability = {\r\n      id: \"string\",\r\n      type: UserAvailabilityType.AVAILABLE,\r\n      mon: [],\r\n      tue: [],\r\n      wed: [],\r\n      thu: [],\r\n      fri: [],\r\n      sat: [],\r\n      sun: [],\r\n    };\r\n\r\n    const days = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];\r\n    const activeDay = ['mondayActive', 'tuesdayActive', 'wednesdayActive', 'thursdayActive', 'fridayActive', 'saturdayActive', 'sundayActive'];\r\n    const controls = this.form.controls;\r\n    for (let day of days) {\r\n      const castedDay = day as keyof UserAvailability;\r\n      if (Array.isArray(availability[castedDay])) {\r\n        for (let availabilitySlot of controls[day].value) {\r\n          for (let activeDayFormValue of activeDay) {\r\n          if (this.form.get(activeDayFormValue)!.value) {\r\n            (availability[castedDay] as UserAvailabilityHours[]).push({ from: availabilitySlot.from, to: availabilitySlot.to });\r\n          }\r\n        }\r\n        }\r\n      }\r\n    }\r\n\r\n    // console.log(this.form.value);\r\n\r\n    if (this.isDialog) {\r\n      this.subs.add(this.teacherService.updateTeacherAvailability(this.form.value).subscribe(res => {\r\n        if (res) {\r\n          this.toast.setShowToastmessage({\r\n            severity: 'success',\r\n            summary: '',\r\n            detail: 'Your Availability info were updated.'\r\n          });\r\n          this.calendarService.setToggleOverlayListener(true);\r\n          this.calendarService.setUpdateListener(true);\r\n        }\r\n      }));\r\n    } else {\r\n\r\n      this.subs.add(this.teacherService.updateAPITeacherApplicationStep5(this.form.value).subscribe(res => {\r\n        if (res) {\r\n          this.toast.setShowToastmessage({\r\n            severity: 'success',\r\n            summary: '',\r\n            detail: 'Your Availability info were updated.'\r\n          });\r\n          this.router.navigateByUrl('/teacher/review', { replaceUrl: true });\r\n        }\r\n        if (!res) {\r\n          this.toast.setShowToastmessage({\r\n            severity: 'warn',\r\n            summary: '',\r\n            detail: 'Previous steps must be completed first.'\r\n          });\r\n        }\r\n      }));\r\n      if (this.user) {\r\n        this.user!.availability = availability;\r\n        this.user!.hours_per_week = this.selectedHours\r\n        console.log(this.user)\r\n        this.userService.updateUserListener();\r\n        this.subs.add(this.teacherService.updateAvailability(this.user.availability).subscribe(res => {\r\n          localStorage.setItem(\"user\", JSON.stringify(this.user));\r\n          this.teacherService.setUpdateAvailabilityForSchedulerClickListener(true)\r\n        }));\r\n        this.toast.setShowToastmessage({\r\n          severity: 'success',\r\n          summary: '',\r\n          detail: 'Availability has been updated.'\r\n        });\r\n      }\r\n      else {\r\n        this.teacher!.availability = availability;\r\n        this.teacher!.hours_per_week = this.selectedHours\r\n        // this.router.navigateByUrl('/teacher/(teacher:review)', { replaceUrl: true });\r\n      }\r\n    }\r\n  }\r\n\r\n  checkFormat(from: string, to: string) {\r\n    const isFromValid = from.length === 5 && from.indexOf(\":\") === 2;\r\n    const isToValid = to.length === 5 && to.indexOf(\":\") === 2;\r\n\r\n    return isFromValid && isToValid;\r\n  }\r\n\r\n  async reload(url: string): Promise<boolean> {\r\n    await this.router.navigateByUrl('.', { skipLocationChange: true });\r\n    return this.router.navigateByUrl(url);\r\n  }\r\n\r\n  onHoursChange(event: any) {\r\n    setTimeout(() => {\r\n      this.selectedHours = event.value\r\n    }, 100);\r\n  }\r\n\r\n  checkValidity(time: string) {\r\n    if (time.length === 0) return 1\r\n    let split = time.split(':');\r\n    let hours = parseInt(split[0]);\r\n    let minutes = parseInt(split[1]);\r\n    return (hours && minutes);\r\n  }\r\n\r\n  /**\r\n * Check if the given field is valid or not.\r\n * @param {string} field - The field to check its validity.\r\n * @returns {boolean} - Returns `true` if the field is invalid, and `false` otherwise.\r\n */\r\n  ifFieldValid(field: string): boolean {\r\n    let control = this.form.get(field);\r\n    let isNotValid = false;\r\n\r\n    if ((control as UntypedFormArray).controls) {\r\n      for (let c of (control as UntypedFormArray).controls) {\r\n        let from = (c as UntypedFormGroup).controls.from.value.toString();\r\n        let to = (c as UntypedFormGroup).controls.to.value.toString();\r\n        isNotValid = isNotValid || (this.tryToSave && control!.touched && (isNaN(this.checkValidity(from)) || isNaN(this.checkValidity(to))));\r\n      }\r\n    } else {\r\n      isNotValid = this.form.get(field)!.invalid && (this.tryToSave || this.form.get(field)!.touched);\r\n    }\r\n\r\n    if ((control as UntypedFormArray).controls?.length === 0) {\r\n      isNotValid = false;\r\n    }\r\n\r\n    // if (this.router.url.includes('teacher')) {\r\n    //   this.teacherService.setStepValidWithBoolean(4, isNotValid, 'teacher-availability-route');\r\n    // }\r\n\r\n    return isNotValid;\r\n  }\r\n\r\n  goBack() {\r\n    this.router.navigateByUrl('/teacher/resume', { replaceUrl: true });\r\n  }\r\n\r\n  private hasEmptyFromOrTo(data: { from: string, to: string }[]): boolean {\r\n    if (this.generalService.isNullishObject(data)) {\r\n      return true;\r\n    }\r\n    if (data.length === 0) {\r\n      return true; // Empty array\r\n    }\r\n    for (const item of data) {\r\n      if (item.from.trim() === '' || item.to.trim() === '') {\r\n        return true; // Empty from or to\r\n      }\r\n    }\r\n    return false; // No empty from or to\r\n  }\r\n}\r\n", "<div class=\"profile-info-section\">\r\n<form [formGroup]=\"form\">\r\n    <div class=\"availability grid grid-nogutter\">\r\n        <div *ngIf=\"!isDialog\" class=\"col-12 md:col-3 lg:col-3\">\r\n        <div class=\"availability-title\">\r\n            Choose date & time available\r\n        </div>\r\n        </div>\r\n        \r\n        <div class=\"col-12 availability-wrapper\" [ngClass]=\"{'md:col-9 lg:col-9': !isDialog, 'narrow': isDialog}\">\r\n        <div class=\"availability-row  hidden md:flex\" style=\"font-weight: bold; text-align: left;\">\r\n            <div class=\"availability-column-1 title-color\">\r\n                Day\r\n            </div>\r\n            <div class=\"availability-column-1 title-color text-center\">\r\n                Active\r\n            </div>\r\n            \r\n            <div class=\"availability-column-2\" style=\"display: flex; flex-wrap: wrap;\">\r\n                <div class=\"column-title title-color text-center\">\r\n                    Starts\r\n                </div>\r\n                <div class=\"column-title title-color text-center\">\r\n                    Ends\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"availability-row\">\r\n            <div class=\"availability-column-1 day\">\r\n                <div class=\"day-title\" [ngClass]=\"{'font-xs': isDialog, 'font-base': !isDialog}\">\r\n                    Monday\r\n                </div>\r\n            </div>\r\n            <div class=\"availability-column-1 checkbox-wrap\">\r\n                <div class=\"flex align-items-center justify-content-center\">\r\n                    <p-checkbox name=\"mondayActive\" formControlName=\"mondayActive\" styleClass=\"primary-blue outlined\" binary=\"true\" label=\"\" (onChange)=\"initCheckboxChangedListener($event, mon, 'mon')\"></p-checkbox>\r\n                </div>\r\n            </div>\r\n            <div class=\"availability-column-2\">\r\n                <div class=\"profile-info\"\r\n                [ngClass]=\"{'disabled-div': !this.form.get('mondayActive')?.value}\">\r\n                    <div class=\"profile-info-section\" style=\"padding-top:0\">\r\n                        <ng-container formArrayName=\"mon\">\r\n                            <div *ngFor=\"let _ of mon.controls; index as i\" style=\"position: relative;\">\r\n                                <ng-container [formGroupName]=\"i\">\r\n                                    <div class=\"input-fields\" [ngClass]=\"{'flex-nowrap gap-1': isDialog}\">\r\n                                        <div class=\"input-field\" [ngClass]=\"{'m-0': isDialog}\">\r\n                                            \r\n                                            <img *ngIf=\"i > 0\" class=\"more-arrow\" src=\"/assets/icons/availability-day-more-arrow.svg\" \r\n                                            alt=\"availability-day-more\" />\r\n                                            <p-calendar\r\n                                            [inputStyleClass]=\"!isDialog ? 'input-element' : 'm-0 dialog-time-input'\"\r\n                                            [stepMinute]=\"15\" readonlyInput=\"true\" (onSelect)=\"timePickChanged($event, this.form.controls.mon, i, 'from')\" \r\n                                            [timeOnly]=\"true\" formControlName=\"from\"\r\n                                            [defaultDate]=\"defaultDate\"\r\n                                            placeholder=\"e.g. 11:00\"></p-calendar>\r\n                                        </div>\r\n                                        <div class=\"input-field\" [ngClass]=\"{'m-0': isDialog}\">\r\n                                            <p-calendar\r\n                                            [inputStyleClass]=\"!isDialog ? 'input-element' : 'm-0 dialog-time-input'\"\r\n                                            [stepMinute]=\"15\" readonlyInput=\"true\" (onSelect)=\"timePickChanged($event, this.form.controls.mon, i, 'to')\" \r\n                                            [timeOnly]=\"true\" formControlName=\"to\"\r\n                                            [defaultDate]=\"defaultDate\"\r\n                                            placeholder=\"e.g. 14:00\"></p-calendar>\r\n                                        </div>            \r\n                                        <button *ngIf=\"i === 0\" pButton type=\"button\" (click)=\"addMon()\"\r\n                                        [ngClass]=\"{'sm bottom-0': isDialog}\"\r\n                                            class=\"p-button-raised p-button-rounded plus-btn-circle addDayIcon\" icon=\"pi pi-plus\">\r\n                                        </button>\r\n                                        <button *ngIf=\"i > 0\" pButton type=\"button\" (click)=\"removeMon(i)\"\r\n                                            [ngClass]=\"{'sm bottom-0': isDialog}\"\r\n                                            class=\"p-button-raised p-button-rounded minus-btn-circle addDayIcon\" icon=\"pi pi-minus\"></button>\r\n                                    </div>\r\n                                </ng-container>\r\n                            </div>\r\n                            <div *ngIf=\"ifFieldValid('mon')\" class=\"input-error\">* Invalid Format\r\n                            </div>\r\n                        </ng-container>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"availability-row\">\r\n            <div class=\"availability-column-1 day\">\r\n                <div class=\"day-title\" [ngClass]=\"{'font-xs': isDialog, 'font-base': !isDialog}\">\r\n                    Tuesday\r\n                </div>\r\n            </div>\r\n            <div class=\"availability-column-1 checkbox-wrap\">\r\n                <div class=\"flex align-items-center justify-content-center\">\r\n                    <p-checkbox name=\"tuesdayActive\" formControlName=\"tuesdayActive\" styleClass=\"primary-blue outlined\" (onChange)=\"initCheckboxChangedListener($event, tue, 'tue')\" binary=\"true\" label=\"\"></p-checkbox>\r\n                </div>\r\n            </div>\r\n            <div class=\"availability-column-2\"\r\n            [pTooltip]=\"!this.form.get('tuesdayActive')?.value ? 'Tuesday must be active to edit time.' : ''\">\r\n                <div class=\"profile-info\">\r\n                    <div class=\"profile-info-section\" [ngClass]=\"{'disabled-div': !this.form.get('tuesdayActive')?.value}\" style=\"padding-top:0\">\r\n                        <ng-container formArrayName=\"tue\">\r\n                            <div *ngFor=\"let _ of tue.controls; index as i\" style=\"position: relative;\">\r\n                                <ng-container [formGroupName]=\"i\">\r\n                                    <div class=\"input-fields\" [ngClass]=\"{'flex-nowrap gap-1': isDialog}\">\r\n                                        <div class=\"input-field\" [ngClass]=\"{'m-0': isDialog}\">\r\n                                            \r\n                                            <img *ngIf=\"i > 0\" class=\"more-arrow\" src=\"/assets/icons/availability-day-more-arrow.svg\" \r\n                                            alt=\"availability-day-more\" />\r\n                                            <p-calendar\r\n                                            [inputStyleClass]=\"!isDialog ? 'input-element' : 'm-0 dialog-time-input'\"\r\n                                            [stepMinute]=\"15\" readonlyInput=\"true\" (onSelect)=\"timePickChanged($event, this.form.controls.tue, i, 'from')\" \r\n                                            [timeOnly]=\"true\" formControlName=\"from\"\r\n                                            [defaultDate]=\"defaultDate\"\r\n                                            placeholder=\"e.g. 11:00\"></p-calendar>\r\n                                        </div>\r\n                                        <div class=\"input-field\" [ngClass]=\"{'m-0': isDialog}\">\r\n                                            <p-calendar\r\n                                            [inputStyleClass]=\"!isDialog ? 'input-element' : 'm-0 dialog-time-input'\"\r\n                                            [stepMinute]=\"15\" readonlyInput=\"true\" (onSelect)=\"timePickChanged($event, this.form.controls.tue, i, 'to')\" \r\n                                            [timeOnly]=\"true\" formControlName=\"to\"\r\n                                            [defaultDate]=\"defaultDate\"\r\n                                            placeholder=\"e.g. 14:00\"></p-calendar>\r\n                                        </div>\r\n                                        <button *ngIf=\"i === 0\" pButton type=\"button\" (click)=\"addTue()\"\r\n                                        [ngClass]=\"{'sm bottom-0': isDialog}\"\r\n                                            class=\"p-button-raised p-button-rounded plus-btn-circle addDayIcon\" icon=\"pi pi-plus\">\r\n                                        </button>\r\n                                        <button *ngIf=\"i > 0\" pButton type=\"button\" (click)=\"removeTue(i)\"\r\n                                            [ngClass]=\"{'sm bottom-0': isDialog}\"\r\n                                            class=\"p-button-raised p-button-rounded minus-btn-circle addDayIcon\" icon=\"pi pi-minus\">\r\n                                        </button>\r\n                                    </div>\r\n                                </ng-container>\r\n                            </div>\r\n                            <div *ngIf=\"ifFieldValid('tue')\" class=\"input-error\">* Invalid Format\r\n                            </div>\r\n                        </ng-container>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"availability-row\">\r\n            <div class=\"availability-column-1 day\">\r\n                <div class=\"day-title\" [ngClass]=\"{'font-xs': isDialog, 'font-base': !isDialog}\">\r\n                    Wednesday\r\n                </div>\r\n            </div>\r\n            <div class=\"availability-column-1 checkbox-wrap\">\r\n                <div class=\"flex align-items-center justify-content-center\">\r\n                    <p-checkbox name=\"wednesdayActive\" formControlName=\"wednesdayActive\" styleClass=\"primary-blue outlined\" (onChange)=\"initCheckboxChangedListener($event, wed, 'wed')\" binary=\"true\" label=\"\"></p-checkbox>\r\n                </div>\r\n            </div>\r\n            <div class=\"availability-column-2\" [ngClass]=\"{'disabled-div': !this.form.get('wednesdayActive')?.value}\">\r\n                <div class=\"profile-info\">\r\n                    <div class=\"profile-info-section\" style=\"padding-top:0\">\r\n                        <ng-container formArrayName=\"wed\">\r\n                            <div *ngFor=\"let _ of wed.controls; index as i\" style=\"position: relative;\">\r\n                                <ng-container [formGroupName]=\"i\">\r\n                                    <div class=\"input-fields\" [ngClass]=\"{'flex-nowrap gap-1': isDialog}\">\r\n                                        <div class=\"input-field\" [ngClass]=\"{'m-0': isDialog}\">\r\n                                            <img *ngIf=\"i > 0\" class=\"more-arrow\" src=\"/assets/icons/availability-day-more-arrow.svg\" \r\n                                            alt=\"availability-day-more\" />\r\n                                            <p-calendar\r\n                                            [inputStyleClass]=\"!isDialog ? 'input-element' : 'm-0 dialog-time-input'\"\r\n                                            [stepMinute]=\"15\" readonlyInput=\"true\" (onSelect)=\"timePickChanged($event, this.form.controls.wed, i, 'from')\" \r\n                                            [timeOnly]=\"true\" formControlName=\"from\"\r\n                                            [defaultDate]=\"defaultDate\"\r\n                                            placeholder=\"e.g. 11:00\"></p-calendar>\r\n                                        </div>\r\n                                        <div class=\"input-field\" [ngClass]=\"{'m-0': isDialog}\">\r\n                                            <p-calendar\r\n                                            [inputStyleClass]=\"!isDialog ? 'input-element' : 'm-0 dialog-time-input'\"\r\n                                            [stepMinute]=\"15\" readonlyInput=\"true\" (onSelect)=\"timePickChanged($event, this.form.controls.wed, i, 'to')\" \r\n                                            [timeOnly]=\"true\" formControlName=\"to\"\r\n                                            [defaultDate]=\"defaultDate\"\r\n                                            placeholder=\"e.g. 14:00\"></p-calendar>\r\n                                        </div>\r\n                                        <button *ngIf=\"i === 0\" pButton type=\"button\" (click)=\"addWed()\"\r\n                                        [ngClass]=\"{'sm bottom-0': isDialog}\"\r\n                                            class=\"p-button-raised p-button-rounded plus-btn-circle addDayIcon\" icon=\"pi pi-plus\">\r\n                                        </button>\r\n                                        <button *ngIf=\"i > 0\" pButton type=\"button\" (click)=\"removeWed(i)\"\r\n                                            [ngClass]=\"{'sm bottom-0': isDialog}\"\r\n                                            class=\"p-button-raised p-button-rounded minus-btn-circle addDayIcon\" icon=\"pi pi-minus\">\r\n                                        </button>\r\n                                    </div>\r\n                                </ng-container>\r\n                            </div>\r\n                            <div *ngIf=\"ifFieldValid('wed')\" class=\"input-error\">* Invalid Format\r\n                            </div>\r\n                        </ng-container>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"availability-row\">\r\n            <div class=\"availability-column-1 day\">\r\n                <div class=\"day-title\" [ngClass]=\"{'font-xs': isDialog, 'font-base': !isDialog}\">\r\n                    Thursday\r\n                </div>\r\n            </div>\r\n            <div class=\"availability-column-1 checkbox-wrap\">\r\n                <div class=\"flex align-items-center justify-content-center\">\r\n                    <p-checkbox name=\"thursdayActive\" formControlName=\"thursdayActive\" styleClass=\"primary-blue outlined\" (onChange)=\"initCheckboxChangedListener($event, thu, 'thu')\" binary=\"true\" label=\"\"></p-checkbox>\r\n                </div>\r\n            </div>\r\n            <div class=\"availability-column-2\" [ngClass]=\"{'disabled-div': !this.form.get('thursdayActive')?.value}\">\r\n                <div class=\"profile-info\">\r\n                    <div class=\"profile-info-section\" style=\"padding-top:0\">\r\n                        <ng-container formArrayName=\"thu\">\r\n                            <div *ngFor=\"let _ of thu.controls; index as i\" style=\"position: relative;\">\r\n                                <ng-container [formGroupName]=\"i\">\r\n                                    <div class=\"input-fields\" [ngClass]=\"{'flex-nowrap gap-1': isDialog}\">\r\n                                        <div class=\"input-field\" [ngClass]=\"{'m-0': isDialog}\">\r\n                                            <img *ngIf=\"i > 0\" class=\"more-arrow\" src=\"/assets/icons/availability-day-more-arrow.svg\" \r\n                                            alt=\"availability-day-more\" />\r\n                                            \r\n                                            <p-calendar\r\n                                            [inputStyleClass]=\"!isDialog ? 'input-element' : 'm-0 dialog-time-input'\"\r\n                                            [stepMinute]=\"15\" readonlyInput=\"true\" (onSelect)=\"timePickChanged($event, this.form.controls.thu, i, 'from')\" \r\n                                            [timeOnly]=\"true\" formControlName=\"from\"\r\n                                            [defaultDate]=\"defaultDate\"\r\n                                            placeholder=\"e.g. 11:00\"></p-calendar>\r\n                                        </div>\r\n                                        <div class=\"input-field\" [ngClass]=\"{'m-0': isDialog}\">\r\n                                            <p-calendar\r\n                                            [inputStyleClass]=\"!isDialog ? 'input-element' : 'm-0 dialog-time-input'\"\r\n                                            [stepMinute]=\"15\" readonlyInput=\"true\" (onSelect)=\"timePickChanged($event, this.form.controls.thu, i, 'to')\" \r\n                                            [timeOnly]=\"true\" formControlName=\"to\"\r\n                                            [defaultDate]=\"defaultDate\"\r\n                                            placeholder=\"e.g. 14:00\"></p-calendar>\r\n                                        </div>\r\n                                        <button *ngIf=\"i === 0\" pButton type=\"button\" (click)=\"addThu()\"\r\n                                        [ngClass]=\"{'sm bottom-0': isDialog}\"\r\n                                            class=\"p-button-raised p-button-rounded plus-btn-circle addDayIcon\" icon=\"pi pi-plus\">\r\n                                        </button>\r\n                                        <button *ngIf=\"i > 0\" pButton type=\"button\" (click)=\"removeThu(i)\"\r\n                                            [ngClass]=\"{'sm bottom-0': isDialog}\"\r\n                                            class=\"p-button-raised p-button-rounded minus-btn-circle addDayIcon\" icon=\"pi pi-minus\">\r\n                                        </button>\r\n                                    </div>\r\n                                </ng-container>\r\n                            </div>\r\n                            <div *ngIf=\"ifFieldValid('thu')\" class=\"input-error\">* Invalid Format\r\n                            </div>\r\n                        </ng-container>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"availability-row\">\r\n            <div class=\"availability-column-1 day\">\r\n                <div class=\"day-title\" [ngClass]=\"{'font-xs': isDialog, 'font-base': !isDialog}\">\r\n                    Friday\r\n                </div>\r\n            </div>\r\n            <div class=\"availability-column-1 checkbox-wrap\">\r\n                <div class=\"flex align-items-center justify-content-center\">\r\n                    <p-checkbox name=\"fridayActive\" formControlName=\"fridayActive\" styleClass=\"primary-blue outlined\" (onChange)=\"initCheckboxChangedListener($event, fri, 'fri')\" binary=\"true\" label=\"\"></p-checkbox>\r\n                </div>\r\n            </div>\r\n            <div class=\"availability-column-2\" [ngClass]=\"{'disabled-div': !this.form.get('fridayActive')?.value}\">\r\n                <div class=\"profile-info\">\r\n                    <div class=\"profile-info-section\" style=\"padding-top:0\">\r\n                        <ng-container formArrayName=\"fri\">\r\n                            <div *ngFor=\"let _ of fri.controls; index as i\" style=\"position: relative;\">\r\n                                <ng-container [formGroupName]=\"i\">\r\n                                    <div class=\"input-fields\" [ngClass]=\"{'flex-nowrap gap-1': isDialog}\">\r\n                                        <div class=\"input-field\" [ngClass]=\"{'m-0': isDialog}\">\r\n                                            <img *ngIf=\"i > 0\" class=\"more-arrow\" src=\"/assets/icons/availability-day-more-arrow.svg\" \r\n                                            alt=\"availability-day-more\" />\r\n                                            \r\n                                            <p-calendar\r\n                                            [inputStyleClass]=\"!isDialog ? 'input-element' : 'm-0 dialog-time-input'\"\r\n                                            [stepMinute]=\"15\" readonlyInput=\"true\" (onSelect)=\"timePickChanged($event, this.form.controls.fri, i, 'from')\" \r\n                                            [timeOnly]=\"true\" formControlName=\"from\"\r\n                                            [defaultDate]=\"defaultDate\"\r\n                                            placeholder=\"e.g. 11:00\"></p-calendar>\r\n                                        </div>\r\n                                        <div class=\"input-field\" [ngClass]=\"{'m-0': isDialog}\">\r\n                                            <p-calendar\r\n                                            [inputStyleClass]=\"!isDialog ? 'input-element' : 'm-0 dialog-time-input'\"\r\n                                            [stepMinute]=\"15\" readonlyInput=\"true\" (onSelect)=\"timePickChanged($event, this.form.controls.fri, i, 'to')\" \r\n                                            [timeOnly]=\"true\" formControlName=\"to\"\r\n                                            [defaultDate]=\"defaultDate\"\r\n                                            placeholder=\"e.g. 14:00\"></p-calendar>\r\n                                        </div>\r\n                                        <button *ngIf=\"i === 0\" pButton type=\"button\" (click)=\"addFri()\"\r\n                                        [ngClass]=\"{'sm bottom-0': isDialog}\"\r\n                                            class=\"p-button-raised p-button-rounded plus-btn-circle addDayIcon\" icon=\"pi pi-plus\">\r\n                                        </button>\r\n                                        <button *ngIf=\"i > 0\" pButton type=\"button\" (click)=\"removeFri(i)\"\r\n                                            [ngClass]=\"{'sm bottom-0': isDialog}\"\r\n                                            class=\"p-button-raised p-button-rounded minus-btn-circle addDayIcon\" icon=\"pi pi-minus\">\r\n                                        </button>\r\n                                    </div>\r\n                                </ng-container>\r\n                            </div>\r\n                            <div *ngIf=\"ifFieldValid('fri')\" class=\"input-error\">* Invalid Format\r\n                            </div>\r\n                        </ng-container>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"availability-row\">\r\n            <div class=\"availability-column-1 day\">\r\n                <div class=\"day-title\" [ngClass]=\"{'font-xs': isDialog, 'font-base': !isDialog}\">\r\n                    Saturday\r\n                </div>\r\n            </div>\r\n            <div class=\"availability-column-1 checkbox-wrap\">\r\n                <div class=\"flex align-items-center justify-content-center\">\r\n                    <p-checkbox name=\"saturdayActive\" formControlName=\"saturdayActive\" styleClass=\"primary-blue outlined\" (onChange)=\"initCheckboxChangedListener($event, sat, 'sat')\" binary=\"true\" label=\"\"></p-checkbox>\r\n                </div>\r\n            </div>\r\n            <div class=\"availability-column-2\" [ngClass]=\"{'disabled-div': !this.form.get('saturdayActive')?.value}\">\r\n                <div class=\"profile-info\">\r\n                    <div class=\"profile-info-section\" style=\"padding-top:0\">\r\n                        <ng-container formArrayName=\"sat\">\r\n                            <div *ngFor=\"let _ of sat.controls; index as i\" style=\"position: relative;\">\r\n                                <ng-container [formGroupName]=\"i\">\r\n                                    <div class=\"input-fields\" [ngClass]=\"{'flex-nowrap gap-1': isDialog}\">\r\n                                        <div class=\"input-field\" [ngClass]=\"{'m-0': isDialog}\">\r\n                                            <img *ngIf=\"i > 0\" class=\"more-arrow\" src=\"/assets/icons/availability-day-more-arrow.svg\" \r\n                                            alt=\"availability-day-more\" />\r\n                                            <p-calendar\r\n                                            [inputStyleClass]=\"!isDialog ? 'input-element' : 'm-0 dialog-time-input'\"\r\n                                            [stepMinute]=\"15\" readonlyInput=\"true\" (onSelect)=\"timePickChanged($event, this.form.controls.sat, i, 'from')\" \r\n                                            [timeOnly]=\"true\" formControlName=\"from\"\r\n                                            [defaultDate]=\"defaultDate\"\r\n                                            placeholder=\"e.g. 11:00\"></p-calendar>\r\n                                        </div>\r\n                                        <div class=\"input-field\" [ngClass]=\"{'m-0': isDialog}\">\r\n                                            <p-calendar\r\n                                            [inputStyleClass]=\"!isDialog ? 'input-element' : 'm-0 dialog-time-input'\"\r\n                                            [stepMinute]=\"15\" readonlyInput=\"true\" (onSelect)=\"timePickChanged($event, this.form.controls.sat, i, 'to')\" \r\n                                            [timeOnly]=\"true\" formControlName=\"to\"\r\n                                            [defaultDate]=\"defaultDate\"\r\n                                            placeholder=\"e.g. 14:00\"></p-calendar>\r\n                                        </div>\r\n                                        <button *ngIf=\"i === 0\" pButton type=\"button\" (click)=\"addSat()\"\r\n                                        [ngClass]=\"{'sm bottom-0': isDialog}\"\r\n                                            class=\"p-button-raised p-button-rounded plus-btn-circle addDayIcon\" icon=\"pi pi-plus\">\r\n                                        </button>\r\n                                        <button *ngIf=\"i > 0\" pButton type=\"button\" (click)=\"removeSat(i)\"\r\n                                            [ngClass]=\"{'sm bottom-0': isDialog}\"\r\n                                            class=\"p-button-raised p-button-rounded minus-btn-circle addDayIcon\" icon=\"pi pi-minus\">\r\n                                        </button>\r\n                                    </div>\r\n                                </ng-container>\r\n                            </div>\r\n                            <div *ngIf=\"ifFieldValid('sat')\" class=\"input-error\">* Invalid Format\r\n                            </div>\r\n                        </ng-container>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"availability-row\">\r\n            <div class=\"availability-column-1 day\">\r\n                <div class=\"day-title\" [ngClass]=\"{'font-xs': isDialog, 'font-base': !isDialog}\">\r\n                    Sunday\r\n                </div>\r\n            </div>\r\n            <div class=\"availability-column-1 checkbox-wrap\">\r\n                <div class=\"flex align-items-center justify-content-center\">\r\n                    <p-checkbox name=\"sundayActive\" formControlName=\"sundayActive\" styleClass=\"primary-blue outlined\" (onChange)=\"initCheckboxChangedListener($event, sun, 'sun')\" binary=\"true\" label=\"\"></p-checkbox>\r\n                </div>\r\n            </div>\r\n            <div class=\"availability-column-2\" [ngClass]=\"{'disabled-div': !this.form.get('sundayActive')?.value}\">\r\n                <div class=\"profile-info\">\r\n                    <div class=\"profile-info-section\" style=\"padding-top:0\">\r\n                        <ng-container formArrayName=\"sun\">\r\n                            <div *ngFor=\"let _ of sun.controls; index as i\" style=\"position: relative;\">\r\n                                <ng-container [formGroupName]=\"i\">\r\n                                    <div class=\"input-fields\" [ngClass]=\"{'flex-nowrap gap-1': isDialog}\">\r\n                                        <div class=\"input-field\" [ngClass]=\"{'m-0': isDialog}\">\r\n                                            <img *ngIf=\"i > 0\" class=\"more-arrow\" src=\"/assets/icons/availability-day-more-arrow.svg\" \r\n                                            alt=\"availability-day-more\" />\r\n                                            <p-calendar\r\n                                            [inputStyleClass]=\"!isDialog ? 'input-element' : 'm-0 dialog-time-input'\"\r\n                                            [stepMinute]=\"15\" readonlyInput=\"true\" (onSelect)=\"timePickChanged($event, this.form.controls.sun, i, 'from')\" \r\n                                            [timeOnly]=\"true\" formControlName=\"from\"\r\n                                            [defaultDate]=\"defaultDate\"\r\n                                            placeholder=\"e.g. 11:00\"></p-calendar>\r\n                                        </div>\r\n                                        <div class=\"input-field\" [ngClass]=\"{'m-0': isDialog}\">\r\n                                            <p-calendar\r\n                                            [inputStyleClass]=\"!isDialog ? 'input-element' : 'm-0 dialog-time-input'\"\r\n                                            [stepMinute]=\"15\" readonlyInput=\"true\" (onSelect)=\"timePickChanged($event, this.form.controls.sun, i, 'to')\" \r\n                                            [timeOnly]=\"true\" formControlName=\"to\"\r\n                                            [defaultDate]=\"defaultDate\"\r\n                                            placeholder=\"e.g. 14:00\"></p-calendar>\r\n                                        </div>\r\n                                        <button *ngIf=\"i === 0\" pButton type=\"button\" (click)=\"addSun()\"\r\n                                        [ngClass]=\"{'sm bottom-0': isDialog}\"\r\n                                            class=\"p-button-raised p-button-rounded plus-btn-circle addDayIcon\" icon=\"pi pi-plus\">\r\n                                        </button>\r\n                                        <button *ngIf=\"i > 0\" pButton type=\"button\" (click)=\"removeSun(i)\"\r\n                                            [ngClass]=\"{'sm bottom-0': isDialog}\"\r\n                                            class=\"p-button-raised p-button-rounded minus-btn-circle addDayIcon\" icon=\"pi pi-minus\">\r\n                                        </button>\r\n                                    </div>\r\n                                </ng-container>\r\n                            </div>\r\n                            <div *ngIf=\"ifFieldValid('sun')\" class=\"input-error\">* Invalid Format\r\n                            </div>\r\n                        </ng-container>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"availability grid\" *ngIf=\"showHours\">\r\n    <div class=\"col-12 md:col-3 lg:col-3\">\r\n        <div class=\"availability-title\">\r\n            Choose hours per week *\r\n        </div>\r\n        </div>\r\n        <div class=\"col-12 md:col-9 lg:col-9\">\r\n    <div class=\"input-fields my-dropdown\">\r\n        <div class=\"input-field\">\r\n            <p-dropdown (onChange)=\"onHoursChange($event)\" autocomplete=\"off\" [options]=\"hourOptions\" placeholder=\"-\"\r\n                formControlName=\"hours\"\r\n                styleClass=\"dropdown-blue hours-dropdown m-t-0\">\r\n                <ng-template let-time pTemplate=\"item\">\r\n                    <div class=\"country-item\">\r\n                        <div class=\"country-name\">{{time}}</div>\r\n                    </div>\r\n                </ng-template>\r\n            </p-dropdown>\r\n            <div *ngIf=\"ifFieldValid('hours')\" class=\"input-error\">* Hours per week is required</div>\r\n        </div>\r\n    </div>\r\n</div>\r\n</div>\r\n</form>\r\n\r\n\r\n<ng-container *ngIf=\"isDialog\">\r\n    <div class=\"flex justify-content-center my-3\">\r\n        <button (click)=\"onSubmit()\" icon=\"pi pi-calendar\" iconPos=\"left\" class=\"mt-2 p-button-sm\" pButton type=\"button\"\r\n            label=\"Schedule\"></button>\r\n    </div>\r\n</ng-container>\r\n<ng-container *ngIf=\"!isDialog\">\r\n<div class=\"btns md:ml-5 md:mt-4\">\r\n    <button pRipple (click)=\"goBack()\" class=\"rounded-blue-button transparent\"> <img src=\"/assets/icons/arrow-left-blue.svg\" />\r\n        Back</button>\r\n    <button pRipple (click)=\"onSubmit()\" class=\"rounded-blue-button\">\r\n        <span *ngIf=\"!user\">\r\n            Next\r\n        </span>\r\n        <span *ngIf=\"user\">\r\n            Save\r\n        </span>\r\n        <img src=\"/assets/icons/arrow-right.svg\" /></button>\r\n</div>\r\n</ng-container>\r\n</div>"], "mappings": ";AAEA,SAAeA,gBAAgB,EAAsBC,kBAAkB,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,gBAAgB;AAG7H,SAAwDC,oBAAoB,QAAQ,gCAAgC;AAOpH,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICRzBC,EADA,CAAAC,cAAA,cAAwD,cACxB;IAC5BD,EAAA,CAAAE,MAAA,qCACJ;IACAF,EADA,CAAAG,YAAA,EAAM,EACA;;;;;IAyC8BH,EAAA,CAAAI,SAAA,cAC8B;;;;;;IAgBlCJ,EAAA,CAAAC,cAAA,iBAE0F;IAF5CD,EAAA,CAAAK,UAAA,mBAAAC,uEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,MAAA,EAAQ;IAAA,EAAC;IAGhEZ,EAAA,CAAAG,YAAA,EAAS;;;;IAFTH,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAN,MAAA,CAAAO,QAAA,EAAqC;;;;;;IAGrChB,EAAA,CAAAC,cAAA,iBAE4F;IAFhDD,EAAA,CAAAK,UAAA,mBAAAY,uEAAA;MAAAjB,EAAA,CAAAO,aAAA,CAAAW,GAAA;MAAA,MAAAC,IAAA,GAAAnB,EAAA,CAAAU,aAAA,GAAAU,KAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAY,SAAA,CAAAF,IAAA,CAAY;IAAA,EAAC;IAE0BnB,EAAA,CAAAG,YAAA,EAAS;;;;IADjGH,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAN,MAAA,CAAAO,QAAA,EAAqC;;;;;;IA3BrDhB,EAAA,CAAAC,cAAA,cAA4E;IACxED,EAAA,CAAAsB,uBAAA,OAAkC;IAE1BtB,EADJ,CAAAC,cAAA,cAAsE,cACX;IAEnDD,EAAA,CAAAuB,UAAA,IAAAC,2CAAA,kBAC8B;IAC9BxB,EAAA,CAAAC,cAAA,qBAKyB;IAHcD,EAAA,CAAAK,UAAA,sBAAAoB,qEAAAC,MAAA;MAAA,MAAAP,IAAA,GAAAnB,EAAA,CAAAO,aAAA,CAAAoB,GAAA,EAAAP,KAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAYF,MAAA,CAAAmB,eAAA,CAAAF,MAAA,EAAAjB,MAAA,CAAAoB,IAAA,CAAAC,QAAA,CAAAC,GAAA,EAAAZ,IAAA,EAAmD,MAAM,CAAC;IAAA,EAAC;IAIlHnB,EAD6B,CAAAG,YAAA,EAAa,EACpC;IAEFH,EADJ,CAAAC,cAAA,cAAuD,qBAM1B;IAHcD,EAAA,CAAAK,UAAA,sBAAA2B,qEAAAN,MAAA;MAAA,MAAAP,IAAA,GAAAnB,EAAA,CAAAO,aAAA,CAAAoB,GAAA,EAAAP,KAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAYF,MAAA,CAAAmB,eAAA,CAAAF,MAAA,EAAAjB,MAAA,CAAAoB,IAAA,CAAAC,QAAA,CAAAC,GAAA,EAAAZ,IAAA,EAAmD,IAAI,CAAC;IAAA,EAAC;IAIhHnB,EAD6B,CAAAG,YAAA,EAAa,EACpC;IAKNH,EAJA,CAAAuB,UAAA,IAAAU,8CAAA,qBAE0F,IAAAC,8CAAA,qBAIE;IAChGlC,EAAA,CAAAG,YAAA,EAAM;;IAEdH,EAAA,CAAAG,YAAA,EAAM;;;;;IA9BYH,EAAA,CAAAmC,SAAA,EAAmB;IAAnBnC,EAAA,CAAAa,UAAA,kBAAAM,IAAA,CAAmB;IACHnB,EAAA,CAAAmC,SAAA,EAA2C;IAA3CnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAAsB,GAAA,EAAA3B,MAAA,CAAAO,QAAA,EAA2C;IACxChB,EAAA,CAAAmC,SAAA,EAA6B;IAA7BnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAAuB,GAAA,EAAA5B,MAAA,CAAAO,QAAA,EAA6B;IAE5ChB,EAAA,CAAAmC,SAAA,EAAW;IAAXnC,EAAA,CAAAa,UAAA,SAAAM,IAAA,KAAW;IAGjBnB,EAAA,CAAAmC,SAAA,EAAyE;IAGzEnC,EAHA,CAAAa,UAAA,qBAAAJ,MAAA,CAAAO,QAAA,6CAAyE,kBACxD,kBACA,gBAAAP,MAAA,CAAA6B,WAAA,CACU;IAGNtC,EAAA,CAAAmC,SAAA,EAA6B;IAA7BnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAAuB,GAAA,EAAA5B,MAAA,CAAAO,QAAA,EAA6B;IAElDhB,EAAA,CAAAmC,SAAA,EAAyE;IAGzEnC,EAHA,CAAAa,UAAA,qBAAAJ,MAAA,CAAAO,QAAA,6CAAyE,kBACxD,kBACA,gBAAAP,MAAA,CAAA6B,WAAA,CACU;IAGtBtC,EAAA,CAAAmC,SAAA,EAAa;IAAbnC,EAAA,CAAAa,UAAA,SAAAM,IAAA,OAAa;IAIbnB,EAAA,CAAAmC,SAAA,EAAW;IAAXnC,EAAA,CAAAa,UAAA,SAAAM,IAAA,KAAW;;;;;IAMhCnB,EAAA,CAAAC,cAAA,cAAqD;IAAAD,EAAA,CAAAE,MAAA,wBACrD;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IA2BUH,EAAA,CAAAI,SAAA,cAC8B;;;;;;IAgBlCJ,EAAA,CAAAC,cAAA,iBAE0F;IAF5CD,EAAA,CAAAK,UAAA,mBAAAkC,uEAAA;MAAAvC,EAAA,CAAAO,aAAA,CAAAiC,GAAA;MAAA,MAAA/B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgC,MAAA,EAAQ;IAAA,EAAC;IAGhEzC,EAAA,CAAAG,YAAA,EAAS;;;;IAFTH,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAN,MAAA,CAAAO,QAAA,EAAqC;;;;;;IAGrChB,EAAA,CAAAC,cAAA,iBAE4F;IAFhDD,EAAA,CAAAK,UAAA,mBAAAqC,uEAAA;MAAA1C,EAAA,CAAAO,aAAA,CAAAoC,GAAA;MAAA,MAAAC,IAAA,GAAA5C,EAAA,CAAAU,aAAA,GAAAU,KAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAoC,SAAA,CAAAD,IAAA,CAAY;IAAA,EAAC;IAGlE5C,EAAA,CAAAG,YAAA,EAAS;;;;IAFLH,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAN,MAAA,CAAAO,QAAA,EAAqC;;;;;;IA3BrDhB,EAAA,CAAAC,cAAA,cAA4E;IACxED,EAAA,CAAAsB,uBAAA,OAAkC;IAE1BtB,EADJ,CAAAC,cAAA,cAAsE,cACX;IAEnDD,EAAA,CAAAuB,UAAA,IAAAuB,2CAAA,kBAC8B;IAC9B9C,EAAA,CAAAC,cAAA,qBAKyB;IAHcD,EAAA,CAAAK,UAAA,sBAAA0C,qEAAArB,MAAA;MAAA,MAAAkB,IAAA,GAAA5C,EAAA,CAAAO,aAAA,CAAAyC,GAAA,EAAA5B,KAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAYF,MAAA,CAAAmB,eAAA,CAAAF,MAAA,EAAAjB,MAAA,CAAAoB,IAAA,CAAAC,QAAA,CAAAmB,GAAA,EAAAL,IAAA,EAAmD,MAAM,CAAC;IAAA,EAAC;IAIlH5C,EAD6B,CAAAG,YAAA,EAAa,EACpC;IAEFH,EADJ,CAAAC,cAAA,cAAuD,qBAM1B;IAHcD,EAAA,CAAAK,UAAA,sBAAA6C,qEAAAxB,MAAA;MAAA,MAAAkB,IAAA,GAAA5C,EAAA,CAAAO,aAAA,CAAAyC,GAAA,EAAA5B,KAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAYF,MAAA,CAAAmB,eAAA,CAAAF,MAAA,EAAAjB,MAAA,CAAAoB,IAAA,CAAAC,QAAA,CAAAmB,GAAA,EAAAL,IAAA,EAAmD,IAAI,CAAC;IAAA,EAAC;IAIhH5C,EAD6B,CAAAG,YAAA,EAAa,EACpC;IAKNH,EAJA,CAAAuB,UAAA,IAAA4B,8CAAA,qBAE0F,IAAAC,8CAAA,qBAIE;IAEhGpD,EAAA,CAAAG,YAAA,EAAM;;IAEdH,EAAA,CAAAG,YAAA,EAAM;;;;;IA/BYH,EAAA,CAAAmC,SAAA,EAAmB;IAAnBnC,EAAA,CAAAa,UAAA,kBAAA+B,IAAA,CAAmB;IACH5C,EAAA,CAAAmC,SAAA,EAA2C;IAA3CnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAAsB,GAAA,EAAA3B,MAAA,CAAAO,QAAA,EAA2C;IACxChB,EAAA,CAAAmC,SAAA,EAA6B;IAA7BnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAAuB,GAAA,EAAA5B,MAAA,CAAAO,QAAA,EAA6B;IAE5ChB,EAAA,CAAAmC,SAAA,EAAW;IAAXnC,EAAA,CAAAa,UAAA,SAAA+B,IAAA,KAAW;IAGjB5C,EAAA,CAAAmC,SAAA,EAAyE;IAGzEnC,EAHA,CAAAa,UAAA,qBAAAJ,MAAA,CAAAO,QAAA,6CAAyE,kBACxD,kBACA,gBAAAP,MAAA,CAAA6B,WAAA,CACU;IAGNtC,EAAA,CAAAmC,SAAA,EAA6B;IAA7BnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAAuB,GAAA,EAAA5B,MAAA,CAAAO,QAAA,EAA6B;IAElDhB,EAAA,CAAAmC,SAAA,EAAyE;IAGzEnC,EAHA,CAAAa,UAAA,qBAAAJ,MAAA,CAAAO,QAAA,6CAAyE,kBACxD,kBACA,gBAAAP,MAAA,CAAA6B,WAAA,CACU;IAGtBtC,EAAA,CAAAmC,SAAA,EAAa;IAAbnC,EAAA,CAAAa,UAAA,SAAA+B,IAAA,OAAa;IAIb5C,EAAA,CAAAmC,SAAA,EAAW;IAAXnC,EAAA,CAAAa,UAAA,SAAA+B,IAAA,KAAW;;;;;IAOhC5C,EAAA,CAAAC,cAAA,cAAqD;IAAAD,EAAA,CAAAE,MAAA,wBACrD;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAyBUH,EAAA,CAAAI,SAAA,cAC8B;;;;;;IAgBlCJ,EAAA,CAAAC,cAAA,iBAE0F;IAF5CD,EAAA,CAAAK,UAAA,mBAAAgD,uEAAA;MAAArD,EAAA,CAAAO,aAAA,CAAA+C,IAAA;MAAA,MAAA7C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA8C,MAAA,EAAQ;IAAA,EAAC;IAGhEvD,EAAA,CAAAG,YAAA,EAAS;;;;IAFTH,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAN,MAAA,CAAAO,QAAA,EAAqC;;;;;;IAGrChB,EAAA,CAAAC,cAAA,iBAE4F;IAFhDD,EAAA,CAAAK,UAAA,mBAAAmD,uEAAA;MAAAxD,EAAA,CAAAO,aAAA,CAAAkD,IAAA;MAAA,MAAAC,KAAA,GAAA1D,EAAA,CAAAU,aAAA,GAAAU,KAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAkD,SAAA,CAAAD,KAAA,CAAY;IAAA,EAAC;IAGlE1D,EAAA,CAAAG,YAAA,EAAS;;;;IAFLH,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAN,MAAA,CAAAO,QAAA,EAAqC;;;;;;IA1BrDhB,EAAA,CAAAC,cAAA,cAA4E;IACxED,EAAA,CAAAsB,uBAAA,OAAkC;IAE1BtB,EADJ,CAAAC,cAAA,cAAsE,cACX;IACnDD,EAAA,CAAAuB,UAAA,IAAAqC,2CAAA,kBAC8B;IAC9B5D,EAAA,CAAAC,cAAA,qBAKyB;IAHcD,EAAA,CAAAK,UAAA,sBAAAwD,qEAAAnC,MAAA;MAAA,MAAAgC,KAAA,GAAA1D,EAAA,CAAAO,aAAA,CAAAuD,IAAA,EAAA1C,KAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAYF,MAAA,CAAAmB,eAAA,CAAAF,MAAA,EAAAjB,MAAA,CAAAoB,IAAA,CAAAC,QAAA,CAAAiC,GAAA,EAAAL,KAAA,EAAmD,MAAM,CAAC;IAAA,EAAC;IAIlH1D,EAD6B,CAAAG,YAAA,EAAa,EACpC;IAEFH,EADJ,CAAAC,cAAA,cAAuD,qBAM1B;IAHcD,EAAA,CAAAK,UAAA,sBAAA2D,qEAAAtC,MAAA;MAAA,MAAAgC,KAAA,GAAA1D,EAAA,CAAAO,aAAA,CAAAuD,IAAA,EAAA1C,KAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAYF,MAAA,CAAAmB,eAAA,CAAAF,MAAA,EAAAjB,MAAA,CAAAoB,IAAA,CAAAC,QAAA,CAAAiC,GAAA,EAAAL,KAAA,EAAmD,IAAI,CAAC;IAAA,EAAC;IAIhH1D,EAD6B,CAAAG,YAAA,EAAa,EACpC;IAKNH,EAJA,CAAAuB,UAAA,IAAA0C,8CAAA,qBAE0F,IAAAC,8CAAA,qBAIE;IAEhGlE,EAAA,CAAAG,YAAA,EAAM;;IAEdH,EAAA,CAAAG,YAAA,EAAM;;;;;IA9BYH,EAAA,CAAAmC,SAAA,EAAmB;IAAnBnC,EAAA,CAAAa,UAAA,kBAAA6C,KAAA,CAAmB;IACH1D,EAAA,CAAAmC,SAAA,EAA2C;IAA3CnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAAsB,GAAA,EAAA3B,MAAA,CAAAO,QAAA,EAA2C;IACxChB,EAAA,CAAAmC,SAAA,EAA6B;IAA7BnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAAuB,GAAA,EAAA5B,MAAA,CAAAO,QAAA,EAA6B;IAC5ChB,EAAA,CAAAmC,SAAA,EAAW;IAAXnC,EAAA,CAAAa,UAAA,SAAA6C,KAAA,KAAW;IAGjB1D,EAAA,CAAAmC,SAAA,EAAyE;IAGzEnC,EAHA,CAAAa,UAAA,qBAAAJ,MAAA,CAAAO,QAAA,6CAAyE,kBACxD,kBACA,gBAAAP,MAAA,CAAA6B,WAAA,CACU;IAGNtC,EAAA,CAAAmC,SAAA,EAA6B;IAA7BnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAAuB,GAAA,EAAA5B,MAAA,CAAAO,QAAA,EAA6B;IAElDhB,EAAA,CAAAmC,SAAA,EAAyE;IAGzEnC,EAHA,CAAAa,UAAA,qBAAAJ,MAAA,CAAAO,QAAA,6CAAyE,kBACxD,kBACA,gBAAAP,MAAA,CAAA6B,WAAA,CACU;IAGtBtC,EAAA,CAAAmC,SAAA,EAAa;IAAbnC,EAAA,CAAAa,UAAA,SAAA6C,KAAA,OAAa;IAIb1D,EAAA,CAAAmC,SAAA,EAAW;IAAXnC,EAAA,CAAAa,UAAA,SAAA6C,KAAA,KAAW;;;;;IAOhC1D,EAAA,CAAAC,cAAA,cAAqD;IAAAD,EAAA,CAAAE,MAAA,wBACrD;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAyBUH,EAAA,CAAAI,SAAA,cAC8B;;;;;;IAiBlCJ,EAAA,CAAAC,cAAA,iBAE0F;IAF5CD,EAAA,CAAAK,UAAA,mBAAA8D,uEAAA;MAAAnE,EAAA,CAAAO,aAAA,CAAA6D,IAAA;MAAA,MAAA3D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA4D,MAAA,EAAQ;IAAA,EAAC;IAGhErE,EAAA,CAAAG,YAAA,EAAS;;;;IAFTH,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAN,MAAA,CAAAO,QAAA,EAAqC;;;;;;IAGrChB,EAAA,CAAAC,cAAA,iBAE4F;IAFhDD,EAAA,CAAAK,UAAA,mBAAAiE,uEAAA;MAAAtE,EAAA,CAAAO,aAAA,CAAAgE,IAAA;MAAA,MAAAC,KAAA,GAAAxE,EAAA,CAAAU,aAAA,GAAAU,KAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgE,SAAA,CAAAD,KAAA,CAAY;IAAA,EAAC;IAGlExE,EAAA,CAAAG,YAAA,EAAS;;;;IAFLH,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAN,MAAA,CAAAO,QAAA,EAAqC;;;;;;IA3BrDhB,EAAA,CAAAC,cAAA,cAA4E;IACxED,EAAA,CAAAsB,uBAAA,OAAkC;IAE1BtB,EADJ,CAAAC,cAAA,cAAsE,cACX;IACnDD,EAAA,CAAAuB,UAAA,IAAAmD,2CAAA,kBAC8B;IAE9B1E,EAAA,CAAAC,cAAA,qBAKyB;IAHcD,EAAA,CAAAK,UAAA,sBAAAsE,qEAAAjD,MAAA;MAAA,MAAA8C,KAAA,GAAAxE,EAAA,CAAAO,aAAA,CAAAqE,IAAA,EAAAxD,KAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAYF,MAAA,CAAAmB,eAAA,CAAAF,MAAA,EAAAjB,MAAA,CAAAoB,IAAA,CAAAC,QAAA,CAAA+C,GAAA,EAAAL,KAAA,EAAmD,MAAM,CAAC;IAAA,EAAC;IAIlHxE,EAD6B,CAAAG,YAAA,EAAa,EACpC;IAEFH,EADJ,CAAAC,cAAA,cAAuD,qBAM1B;IAHcD,EAAA,CAAAK,UAAA,sBAAAyE,qEAAApD,MAAA;MAAA,MAAA8C,KAAA,GAAAxE,EAAA,CAAAO,aAAA,CAAAqE,IAAA,EAAAxD,KAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAYF,MAAA,CAAAmB,eAAA,CAAAF,MAAA,EAAAjB,MAAA,CAAAoB,IAAA,CAAAC,QAAA,CAAA+C,GAAA,EAAAL,KAAA,EAAmD,IAAI,CAAC;IAAA,EAAC;IAIhHxE,EAD6B,CAAAG,YAAA,EAAa,EACpC;IAKNH,EAJA,CAAAuB,UAAA,IAAAwD,8CAAA,qBAE0F,IAAAC,8CAAA,qBAIE;IAEhGhF,EAAA,CAAAG,YAAA,EAAM;;IAEdH,EAAA,CAAAG,YAAA,EAAM;;;;;IA/BYH,EAAA,CAAAmC,SAAA,EAAmB;IAAnBnC,EAAA,CAAAa,UAAA,kBAAA2D,KAAA,CAAmB;IACHxE,EAAA,CAAAmC,SAAA,EAA2C;IAA3CnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAAsB,GAAA,EAAA3B,MAAA,CAAAO,QAAA,EAA2C;IACxChB,EAAA,CAAAmC,SAAA,EAA6B;IAA7BnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAAuB,GAAA,EAAA5B,MAAA,CAAAO,QAAA,EAA6B;IAC5ChB,EAAA,CAAAmC,SAAA,EAAW;IAAXnC,EAAA,CAAAa,UAAA,SAAA2D,KAAA,KAAW;IAIjBxE,EAAA,CAAAmC,SAAA,EAAyE;IAGzEnC,EAHA,CAAAa,UAAA,qBAAAJ,MAAA,CAAAO,QAAA,6CAAyE,kBACxD,kBACA,gBAAAP,MAAA,CAAA6B,WAAA,CACU;IAGNtC,EAAA,CAAAmC,SAAA,EAA6B;IAA7BnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAAuB,GAAA,EAAA5B,MAAA,CAAAO,QAAA,EAA6B;IAElDhB,EAAA,CAAAmC,SAAA,EAAyE;IAGzEnC,EAHA,CAAAa,UAAA,qBAAAJ,MAAA,CAAAO,QAAA,6CAAyE,kBACxD,kBACA,gBAAAP,MAAA,CAAA6B,WAAA,CACU;IAGtBtC,EAAA,CAAAmC,SAAA,EAAa;IAAbnC,EAAA,CAAAa,UAAA,SAAA2D,KAAA,OAAa;IAIbxE,EAAA,CAAAmC,SAAA,EAAW;IAAXnC,EAAA,CAAAa,UAAA,SAAA2D,KAAA,KAAW;;;;;IAOhCxE,EAAA,CAAAC,cAAA,cAAqD;IAAAD,EAAA,CAAAE,MAAA,wBACrD;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAyBUH,EAAA,CAAAI,SAAA,cAC8B;;;;;;IAiBlCJ,EAAA,CAAAC,cAAA,iBAE0F;IAF5CD,EAAA,CAAAK,UAAA,mBAAA4E,uEAAA;MAAAjF,EAAA,CAAAO,aAAA,CAAA2E,IAAA;MAAA,MAAAzE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA0E,MAAA,EAAQ;IAAA,EAAC;IAGhEnF,EAAA,CAAAG,YAAA,EAAS;;;;IAFTH,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAN,MAAA,CAAAO,QAAA,EAAqC;;;;;;IAGrChB,EAAA,CAAAC,cAAA,iBAE4F;IAFhDD,EAAA,CAAAK,UAAA,mBAAA+E,uEAAA;MAAApF,EAAA,CAAAO,aAAA,CAAA8E,IAAA;MAAA,MAAAC,KAAA,GAAAtF,EAAA,CAAAU,aAAA,GAAAU,KAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA8E,SAAA,CAAAD,KAAA,CAAY;IAAA,EAAC;IAGlEtF,EAAA,CAAAG,YAAA,EAAS;;;;IAFLH,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAN,MAAA,CAAAO,QAAA,EAAqC;;;;;;IA3BrDhB,EAAA,CAAAC,cAAA,cAA4E;IACxED,EAAA,CAAAsB,uBAAA,OAAkC;IAE1BtB,EADJ,CAAAC,cAAA,cAAsE,cACX;IACnDD,EAAA,CAAAuB,UAAA,IAAAiE,2CAAA,kBAC8B;IAE9BxF,EAAA,CAAAC,cAAA,qBAKyB;IAHcD,EAAA,CAAAK,UAAA,sBAAAoF,qEAAA/D,MAAA;MAAA,MAAA4D,KAAA,GAAAtF,EAAA,CAAAO,aAAA,CAAAmF,IAAA,EAAAtE,KAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAYF,MAAA,CAAAmB,eAAA,CAAAF,MAAA,EAAAjB,MAAA,CAAAoB,IAAA,CAAAC,QAAA,CAAA6D,GAAA,EAAAL,KAAA,EAAmD,MAAM,CAAC;IAAA,EAAC;IAIlHtF,EAD6B,CAAAG,YAAA,EAAa,EACpC;IAEFH,EADJ,CAAAC,cAAA,cAAuD,qBAM1B;IAHcD,EAAA,CAAAK,UAAA,sBAAAuF,qEAAAlE,MAAA;MAAA,MAAA4D,KAAA,GAAAtF,EAAA,CAAAO,aAAA,CAAAmF,IAAA,EAAAtE,KAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAYF,MAAA,CAAAmB,eAAA,CAAAF,MAAA,EAAAjB,MAAA,CAAAoB,IAAA,CAAAC,QAAA,CAAA6D,GAAA,EAAAL,KAAA,EAAmD,IAAI,CAAC;IAAA,EAAC;IAIhHtF,EAD6B,CAAAG,YAAA,EAAa,EACpC;IAKNH,EAJA,CAAAuB,UAAA,IAAAsE,8CAAA,qBAE0F,IAAAC,8CAAA,qBAIE;IAEhG9F,EAAA,CAAAG,YAAA,EAAM;;IAEdH,EAAA,CAAAG,YAAA,EAAM;;;;;IA/BYH,EAAA,CAAAmC,SAAA,EAAmB;IAAnBnC,EAAA,CAAAa,UAAA,kBAAAyE,KAAA,CAAmB;IACHtF,EAAA,CAAAmC,SAAA,EAA2C;IAA3CnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAAsB,GAAA,EAAA3B,MAAA,CAAAO,QAAA,EAA2C;IACxChB,EAAA,CAAAmC,SAAA,EAA6B;IAA7BnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAAuB,GAAA,EAAA5B,MAAA,CAAAO,QAAA,EAA6B;IAC5ChB,EAAA,CAAAmC,SAAA,EAAW;IAAXnC,EAAA,CAAAa,UAAA,SAAAyE,KAAA,KAAW;IAIjBtF,EAAA,CAAAmC,SAAA,EAAyE;IAGzEnC,EAHA,CAAAa,UAAA,qBAAAJ,MAAA,CAAAO,QAAA,6CAAyE,kBACxD,kBACA,gBAAAP,MAAA,CAAA6B,WAAA,CACU;IAGNtC,EAAA,CAAAmC,SAAA,EAA6B;IAA7BnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAAuB,GAAA,EAAA5B,MAAA,CAAAO,QAAA,EAA6B;IAElDhB,EAAA,CAAAmC,SAAA,EAAyE;IAGzEnC,EAHA,CAAAa,UAAA,qBAAAJ,MAAA,CAAAO,QAAA,6CAAyE,kBACxD,kBACA,gBAAAP,MAAA,CAAA6B,WAAA,CACU;IAGtBtC,EAAA,CAAAmC,SAAA,EAAa;IAAbnC,EAAA,CAAAa,UAAA,SAAAyE,KAAA,OAAa;IAIbtF,EAAA,CAAAmC,SAAA,EAAW;IAAXnC,EAAA,CAAAa,UAAA,SAAAyE,KAAA,KAAW;;;;;IAOhCtF,EAAA,CAAAC,cAAA,cAAqD;IAAAD,EAAA,CAAAE,MAAA,wBACrD;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAyBUH,EAAA,CAAAI,SAAA,cAC8B;;;;;;IAgBlCJ,EAAA,CAAAC,cAAA,iBAE0F;IAF5CD,EAAA,CAAAK,UAAA,mBAAA0F,uEAAA;MAAA/F,EAAA,CAAAO,aAAA,CAAAyF,IAAA;MAAA,MAAAvF,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAwF,MAAA,EAAQ;IAAA,EAAC;IAGhEjG,EAAA,CAAAG,YAAA,EAAS;;;;IAFTH,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAN,MAAA,CAAAO,QAAA,EAAqC;;;;;;IAGrChB,EAAA,CAAAC,cAAA,iBAE4F;IAFhDD,EAAA,CAAAK,UAAA,mBAAA6F,uEAAA;MAAAlG,EAAA,CAAAO,aAAA,CAAA4F,IAAA;MAAA,MAAAC,KAAA,GAAApG,EAAA,CAAAU,aAAA,GAAAU,KAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA4F,SAAA,CAAAD,KAAA,CAAY;IAAA,EAAC;IAGlEpG,EAAA,CAAAG,YAAA,EAAS;;;;IAFLH,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAN,MAAA,CAAAO,QAAA,EAAqC;;;;;;IA1BrDhB,EAAA,CAAAC,cAAA,cAA4E;IACxED,EAAA,CAAAsB,uBAAA,OAAkC;IAE1BtB,EADJ,CAAAC,cAAA,cAAsE,cACX;IACnDD,EAAA,CAAAuB,UAAA,IAAA+E,2CAAA,kBAC8B;IAC9BtG,EAAA,CAAAC,cAAA,qBAKyB;IAHcD,EAAA,CAAAK,UAAA,sBAAAkG,qEAAA7E,MAAA;MAAA,MAAA0E,KAAA,GAAApG,EAAA,CAAAO,aAAA,CAAAiG,IAAA,EAAApF,KAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAYF,MAAA,CAAAmB,eAAA,CAAAF,MAAA,EAAAjB,MAAA,CAAAoB,IAAA,CAAAC,QAAA,CAAA2E,GAAA,EAAAL,KAAA,EAAmD,MAAM,CAAC;IAAA,EAAC;IAIlHpG,EAD6B,CAAAG,YAAA,EAAa,EACpC;IAEFH,EADJ,CAAAC,cAAA,cAAuD,qBAM1B;IAHcD,EAAA,CAAAK,UAAA,sBAAAqG,qEAAAhF,MAAA;MAAA,MAAA0E,KAAA,GAAApG,EAAA,CAAAO,aAAA,CAAAiG,IAAA,EAAApF,KAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAYF,MAAA,CAAAmB,eAAA,CAAAF,MAAA,EAAAjB,MAAA,CAAAoB,IAAA,CAAAC,QAAA,CAAA2E,GAAA,EAAAL,KAAA,EAAmD,IAAI,CAAC;IAAA,EAAC;IAIhHpG,EAD6B,CAAAG,YAAA,EAAa,EACpC;IAKNH,EAJA,CAAAuB,UAAA,IAAAoF,8CAAA,qBAE0F,IAAAC,8CAAA,qBAIE;IAEhG5G,EAAA,CAAAG,YAAA,EAAM;;IAEdH,EAAA,CAAAG,YAAA,EAAM;;;;;IA9BYH,EAAA,CAAAmC,SAAA,EAAmB;IAAnBnC,EAAA,CAAAa,UAAA,kBAAAuF,KAAA,CAAmB;IACHpG,EAAA,CAAAmC,SAAA,EAA2C;IAA3CnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAAsB,GAAA,EAAA3B,MAAA,CAAAO,QAAA,EAA2C;IACxChB,EAAA,CAAAmC,SAAA,EAA6B;IAA7BnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAAuB,GAAA,EAAA5B,MAAA,CAAAO,QAAA,EAA6B;IAC5ChB,EAAA,CAAAmC,SAAA,EAAW;IAAXnC,EAAA,CAAAa,UAAA,SAAAuF,KAAA,KAAW;IAGjBpG,EAAA,CAAAmC,SAAA,EAAyE;IAGzEnC,EAHA,CAAAa,UAAA,qBAAAJ,MAAA,CAAAO,QAAA,6CAAyE,kBACxD,kBACA,gBAAAP,MAAA,CAAA6B,WAAA,CACU;IAGNtC,EAAA,CAAAmC,SAAA,EAA6B;IAA7BnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAAuB,GAAA,EAAA5B,MAAA,CAAAO,QAAA,EAA6B;IAElDhB,EAAA,CAAAmC,SAAA,EAAyE;IAGzEnC,EAHA,CAAAa,UAAA,qBAAAJ,MAAA,CAAAO,QAAA,6CAAyE,kBACxD,kBACA,gBAAAP,MAAA,CAAA6B,WAAA,CACU;IAGtBtC,EAAA,CAAAmC,SAAA,EAAa;IAAbnC,EAAA,CAAAa,UAAA,SAAAuF,KAAA,OAAa;IAIbpG,EAAA,CAAAmC,SAAA,EAAW;IAAXnC,EAAA,CAAAa,UAAA,SAAAuF,KAAA,KAAW;;;;;IAOhCpG,EAAA,CAAAC,cAAA,cAAqD;IAAAD,EAAA,CAAAE,MAAA,wBACrD;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAyBUH,EAAA,CAAAI,SAAA,cAC8B;;;;;;IAgBlCJ,EAAA,CAAAC,cAAA,iBAE0F;IAF5CD,EAAA,CAAAK,UAAA,mBAAAwG,wEAAA;MAAA7G,EAAA,CAAAO,aAAA,CAAAuG,IAAA;MAAA,MAAArG,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAsG,MAAA,EAAQ;IAAA,EAAC;IAGhE/G,EAAA,CAAAG,YAAA,EAAS;;;;IAFTH,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAN,MAAA,CAAAO,QAAA,EAAqC;;;;;;IAGrChB,EAAA,CAAAC,cAAA,iBAE4F;IAFhDD,EAAA,CAAAK,UAAA,mBAAA2G,wEAAA;MAAAhH,EAAA,CAAAO,aAAA,CAAA0G,IAAA;MAAA,MAAAC,KAAA,GAAAlH,EAAA,CAAAU,aAAA,GAAAU,KAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA0G,SAAA,CAAAD,KAAA,CAAY;IAAA,EAAC;IAGlElH,EAAA,CAAAG,YAAA,EAAS;;;;IAFLH,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAN,MAAA,CAAAO,QAAA,EAAqC;;;;;;IA1BrDhB,EAAA,CAAAC,cAAA,cAA4E;IACxED,EAAA,CAAAsB,uBAAA,OAAkC;IAE1BtB,EADJ,CAAAC,cAAA,cAAsE,cACX;IACnDD,EAAA,CAAAuB,UAAA,IAAA6F,4CAAA,kBAC8B;IAC9BpH,EAAA,CAAAC,cAAA,qBAKyB;IAHcD,EAAA,CAAAK,UAAA,sBAAAgH,sEAAA3F,MAAA;MAAA,MAAAwF,KAAA,GAAAlH,EAAA,CAAAO,aAAA,CAAA+G,IAAA,EAAAlG,KAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAYF,MAAA,CAAAmB,eAAA,CAAAF,MAAA,EAAAjB,MAAA,CAAAoB,IAAA,CAAAC,QAAA,CAAAyF,GAAA,EAAAL,KAAA,EAAmD,MAAM,CAAC;IAAA,EAAC;IAIlHlH,EAD6B,CAAAG,YAAA,EAAa,EACpC;IAEFH,EADJ,CAAAC,cAAA,cAAuD,qBAM1B;IAHcD,EAAA,CAAAK,UAAA,sBAAAmH,sEAAA9F,MAAA;MAAA,MAAAwF,KAAA,GAAAlH,EAAA,CAAAO,aAAA,CAAA+G,IAAA,EAAAlG,KAAA;MAAA,MAAAX,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAYF,MAAA,CAAAmB,eAAA,CAAAF,MAAA,EAAAjB,MAAA,CAAAoB,IAAA,CAAAC,QAAA,CAAAyF,GAAA,EAAAL,KAAA,EAAmD,IAAI,CAAC;IAAA,EAAC;IAIhHlH,EAD6B,CAAAG,YAAA,EAAa,EACpC;IAKNH,EAJA,CAAAuB,UAAA,IAAAkG,+CAAA,qBAE0F,IAAAC,+CAAA,qBAIE;IAEhG1H,EAAA,CAAAG,YAAA,EAAM;;IAEdH,EAAA,CAAAG,YAAA,EAAM;;;;;IA9BYH,EAAA,CAAAmC,SAAA,EAAmB;IAAnBnC,EAAA,CAAAa,UAAA,kBAAAqG,KAAA,CAAmB;IACHlH,EAAA,CAAAmC,SAAA,EAA2C;IAA3CnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAAsB,GAAA,EAAA3B,MAAA,CAAAO,QAAA,EAA2C;IACxChB,EAAA,CAAAmC,SAAA,EAA6B;IAA7BnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAAuB,GAAA,EAAA5B,MAAA,CAAAO,QAAA,EAA6B;IAC5ChB,EAAA,CAAAmC,SAAA,EAAW;IAAXnC,EAAA,CAAAa,UAAA,SAAAqG,KAAA,KAAW;IAGjBlH,EAAA,CAAAmC,SAAA,EAAyE;IAGzEnC,EAHA,CAAAa,UAAA,qBAAAJ,MAAA,CAAAO,QAAA,6CAAyE,kBACxD,kBACA,gBAAAP,MAAA,CAAA6B,WAAA,CACU;IAGNtC,EAAA,CAAAmC,SAAA,EAA6B;IAA7BnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAAuB,GAAA,EAAA5B,MAAA,CAAAO,QAAA,EAA6B;IAElDhB,EAAA,CAAAmC,SAAA,EAAyE;IAGzEnC,EAHA,CAAAa,UAAA,qBAAAJ,MAAA,CAAAO,QAAA,6CAAyE,kBACxD,kBACA,gBAAAP,MAAA,CAAA6B,WAAA,CACU;IAGtBtC,EAAA,CAAAmC,SAAA,EAAa;IAAbnC,EAAA,CAAAa,UAAA,SAAAqG,KAAA,OAAa;IAIblH,EAAA,CAAAmC,SAAA,EAAW;IAAXnC,EAAA,CAAAa,UAAA,SAAAqG,KAAA,KAAW;;;;;IAOhClH,EAAA,CAAAC,cAAA,cAAqD;IAAAD,EAAA,CAAAE,MAAA,wBACrD;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAuBVH,EADJ,CAAAC,cAAA,cAA0B,cACI;IAAAD,EAAA,CAAAE,MAAA,GAAQ;IACtCF,EADsC,CAAAG,YAAA,EAAM,EACtC;;;;IADwBH,EAAA,CAAAmC,SAAA,GAAQ;IAARnC,EAAA,CAAA2H,iBAAA,CAAAC,QAAA,CAAQ;;;;;IAI9C5H,EAAA,CAAAC,cAAA,cAAuD;IAAAD,EAAA,CAAAE,MAAA,mCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAhB7FH,EAFJ,CAAAC,cAAA,cAAiD,cACX,cACF;IAC5BD,EAAA,CAAAE,MAAA,gCACJ;IACAF,EADA,CAAAG,YAAA,EAAM,EACA;IAIFH,EAHJ,CAAAC,cAAA,cAAsC,cACJ,cACT,qBAG+B;IAFxCD,EAAA,CAAAK,UAAA,sBAAAwH,sEAAAnG,MAAA;MAAA1B,EAAA,CAAAO,aAAA,CAAAuH,IAAA;MAAA,MAAArH,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAAYF,MAAA,CAAAsH,aAAA,CAAArG,MAAA,CAAqB;IAAA,EAAC;IAG1C1B,EAAA,CAAAuB,UAAA,IAAAyG,oDAAA,0BAAuC;IAK3ChI,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAuB,UAAA,IAAA0G,4CAAA,kBAAuD;IAInEjI,EAHQ,CAAAG,YAAA,EAAM,EACJ,EACJ,EACA;;;;IAbwEH,EAAA,CAAAmC,SAAA,GAAuB;IAAvBnC,EAAA,CAAAa,UAAA,YAAAJ,MAAA,CAAAyH,WAAA,CAAuB;IASnFlI,EAAA,CAAAmC,SAAA,GAA2B;IAA3BnC,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAA0H,YAAA,UAA2B;;;;;;IAQ7CnI,EAAA,CAAAsB,uBAAA,GAA+B;IAEvBtB,EADJ,CAAAC,cAAA,cAA8C,iBAErB;IADbD,EAAA,CAAAK,UAAA,mBAAA+H,wEAAA;MAAApI,EAAA,CAAAO,aAAA,CAAA8H,IAAA;MAAA,MAAA5H,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA6H,QAAA,EAAU;IAAA,EAAC;IAEhCtI,EADyB,CAAAG,YAAA,EAAS,EAC5B;;;;;;IAOFH,EAAA,CAAAC,cAAA,WAAoB;IAChBD,EAAA,CAAAE,MAAA,aACJ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,WAAmB;IACfD,EAAA,CAAAE,MAAA,aACJ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAVfH,EAAA,CAAAsB,uBAAA,GAAgC;IAE5BtB,EADJ,CAAAC,cAAA,cAAkC,iBAC6C;IAA3DD,EAAA,CAAAK,UAAA,mBAAAkI,wEAAA;MAAAvI,EAAA,CAAAO,aAAA,CAAAiI,IAAA;MAAA,MAAA/H,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAgI,MAAA,EAAQ;IAAA,EAAC;IAA0CzI,EAAA,CAAAI,SAAA,cAA+C;IACvHJ,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACjBH,EAAA,CAAAC,cAAA,iBAAiE;IAAjDD,EAAA,CAAAK,UAAA,mBAAAqI,wEAAA;MAAA1I,EAAA,CAAAO,aAAA,CAAAiI,IAAA;MAAA,MAAA/H,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA6H,QAAA,EAAU;IAAA,EAAC;IAIhCtI,EAHA,CAAAuB,UAAA,IAAAoH,sDAAA,mBAAoB,IAAAC,sDAAA,mBAGD;IAGnB5I,EAAA,CAAAI,SAAA,cAA2C;IACnDJ,EADmD,CAAAG,YAAA,EAAS,EACtD;;;;;IAPSH,EAAA,CAAAmC,SAAA,GAAW;IAAXnC,EAAA,CAAAa,UAAA,UAAAJ,MAAA,CAAAoI,IAAA,CAAW;IAGX7I,EAAA,CAAAmC,SAAA,EAAU;IAAVnC,EAAA,CAAAa,UAAA,SAAAJ,MAAA,CAAAoI,IAAA,CAAU;;;ADlbzB,OAAM,MAAOC,qBAAqB;EAuBhCC,YACUC,EAAsB,EACtBC,MAAc,EACdC,QAAkB,EAClBC,cAAyC,EACzCC,WAAwB,EACxBC,KAAmB,EACnBC,WAAwB,EACxBC,eAAgC,EAChCC,cAA8B;IAR9B,KAAAR,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IA/Bf,KAAAX,IAAI,GAAS,EAAU;IACvB,KAAAY,SAAS,GAAY,IAAI;IACzB,KAAAzI,QAAQ,GAAY,KAAK;IAC1B,KAAA0I,IAAI,GAAG,IAAI3J,OAAO,EAAE;IACrB,KAAA8B,IAAI,GAAqB,IAAIjC,gBAAgB,CAAC,EAAE,CAAC;IAExD,KAAA+J,QAAQ,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAGrD,KAAAC,sBAAsB,GAAmC,EAAE;IAC3D,KAAAC,sBAAsB,GAAmC,EAAE;IAC3D,KAAAC,sBAAsB,GAAmC,EAAE;IAC3D,KAAAC,sBAAsB,GAAmC,EAAE;IAC3D,KAAAC,sBAAsB,GAAmC,EAAE;IAC3D,KAAAC,sBAAsB,GAAmC,EAAE;IAC3D,KAAAC,sBAAsB,GAAmC,EAAE;IAE3D,KAAAhC,WAAW,GAAa,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;IACtF,KAAAiC,aAAa,GAAW,MAAM;IAE9B,KAAAC,SAAS,GAAY,KAAK;IACjC,KAAA9H,WAAW,GAAS,IAAI+H,IAAI,CAAC,uBAAuB,CAAC;EAWjD;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACnB,cAAc,CAACoB,mBAAmB,CAAC,CAAC,CAAC;IAC1C,IAAI,CAACb,IAAI,CAACc,GAAG,CAAC,IAAI,CAACrB,cAAc,CAACsB,0BAA0B,CAACC,SAAS,CAACC,GAAG,IAAG;MAC3E,IAAI,CAACf,sBAAsB,GAAG,EAAE;MAChC,IAAI,CAACC,sBAAsB,GAAG,EAAE;MAChC,IAAI,CAACC,sBAAsB,GAAG,EAAE;MAChC,IAAI,CAACC,sBAAsB,GAAG,EAAE;MAChC,IAAI,CAACC,sBAAsB,GAAG,EAAE;MAChC,IAAI,CAACC,sBAAsB,GAAG,EAAE;MAChC,IAAI,CAACC,sBAAsB,GAAG,EAAE;MAChC,IAAI,CAACU,OAAO,GAAG,IAAI,CAAC/B,IAAI,GAAG,IAAI,CAACS,WAAW,CAACuB,eAAe,EAAG,GAAG,IAAI,CAAC1B,cAAc,CAAC2B,YAAY;MACjG,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACH,OAAO,EAAEI,YAAY;MACrD,IAAI,CAACnJ,IAAI,GAAG,IAAIjC,gBAAgB,CAAC;QAC/BmC,GAAG,EAAE,IAAIrC,gBAAgB,CAAC,EAAE,CAAC;QAC7BuD,GAAG,EAAE,IAAIvD,gBAAgB,CAAC,EAAE,CAAC;QAC7BqE,GAAG,EAAE,IAAIrE,gBAAgB,CAAC,EAAE,CAAC;QAC7BmF,GAAG,EAAE,IAAInF,gBAAgB,CAAC,EAAE,CAAC;QAC7BiG,GAAG,EAAE,IAAIjG,gBAAgB,CAAC,EAAE,CAAC;QAC7B+G,GAAG,EAAE,IAAI/G,gBAAgB,CAAC,EAAE,CAAC;QAC7B6H,GAAG,EAAE,IAAI7H,gBAAgB,CAAC,EAAE,CAAC;QAC7BuL,YAAY,EAAE,IAAItL,kBAAkB,CAAC,KAAK,CAAC;QAC3CuL,aAAa,EAAE,IAAIvL,kBAAkB,CAAC,KAAK,CAAC;QAC5CwL,eAAe,EAAE,IAAIxL,kBAAkB,CAAC,KAAK,CAAC;QAC9CyL,cAAc,EAAE,IAAIzL,kBAAkB,CAAC,KAAK,CAAC;QAC7C0L,YAAY,EAAE,IAAI1L,kBAAkB,CAAC,KAAK,CAAC;QAC3C2L,cAAc,EAAE,IAAI3L,kBAAkB,CAAC,KAAK,CAAC;QAC7C4L,YAAY,EAAE,IAAI5L,kBAAkB,CAAC,KAAK,CAAC;QAC3C6L,KAAK,EAAE,IAAI7L,kBAAkB,CAAC,IAAI,CAACiL,OAAO,EAAEa,cAAc,GAAG,IAAI,CAACb,OAAO,EAAEa,cAAc,GAAG,IAAI,CAACvD,WAAW,CAAC,CAAC,CAAC,EAAE;UAC/GwD,UAAU,EAAE,CAAC7L,UAAU,CAAC8L,QAAQ;SACjC;OACF,CAAC;IAEJ,CAAC,CAAC,CAAC;IAGH,IAAI,CAACjC,IAAI,CAACc,GAAG,CAAC,IAAI,CAACrB,cAAc,CAACyC,yBAAyB,CAAClB,SAAS,CAACC,GAAG,IAAG;MAC1E,IAAIA,GAAG,EAAE;QACP,IAAI,CAACrC,QAAQ,EAAE;MACjB;IACF,CAAC,CAAC,CAAC;IAEH,IAAI,IAAI,CAACtH,QAAQ,EAAE;MACjB,IAAI,CAACoI,WAAW,CAACyC,mBAAmB,CAAC,IAAI,CAAChD,IAAI,CAACiD,SAAU,CAAC,CAACpB,SAAS,CAAEC,GAAqB,IAAI;QAC7F,IAAIA,GAAG,EAAE;UACP,IAAI,CAACoB,gBAAgB,CAACpB,GAAG,CAAC;QAC5B;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACjB,IAAI,CAACc,GAAG,CAAC,IAAI,CAACrB,cAAc,CAAC6C,0BAA0B,EAAE,CAACtB,SAAS,CAAEC,GAAqB,IAAI;QACjG,IAAIA,GAAG,EAAE;UACP,IAAI,CAACoB,gBAAgB,CAACpB,GAAG,CAAC;QAC5B;MACF,CAAC,CAAC,CAAC;IACL;EACF;EAEAsB,WAAWA,CAAA;IACT,IAAI,CAACvC,IAAI,CAACwC,WAAW,EAAE;IACvB,IAAI,CAAC/C,cAAc,CAACgD,4BAA4B,CAAC,KAAK,CAAC;EACzD;EAEA;;;;;;;EAOAvK,eAAeA,CAACF,MAAW,EAAE0K,SAA2B,EAAEC,CAAM,EAAEC,QAAgB;IAChF,MAAMC,EAAE,GAAG,IAAIlC,IAAI,CAAC3I,MAAM,CAAC;IAE3B,MAAM8K,aAAa,GAA2B,EAA4B;IAC1EA,aAAa,CAACF,QAAQ,CAAC,GAAGC,EAAE,CAACE,cAAc,CAAC,OAAO,EAAE;MACnDC,MAAM,EAAE,KAAK;MACbC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;IACF,MAAMC,SAAS,GAAGL,aAAa,CAACF,QAAQ,CAAC,CAACQ,KAAK,CAAC,GAAG,CAAC;IACpDC,OAAO,CAACC,GAAG,CAACH,SAAS,CAAC;IACtB,IAAIA,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;MACxBE,OAAO,CAACC,GAAG,CAACT,EAAE,CAAC;MACfQ,OAAO,CAACC,GAAG,CAACR,aAAa,CAAC;MAC1B,IAAI1K,QAAQ,GAAG,IAAI,CAACD,IAAI,CAACC,QAAQ;MACjCsK,SAAS,CAACa,EAAE,CAACZ,CAAC,CAAC,CAACa,UAAU,CACxBV,aAAa,CACd;MAED,OAAO,KAAK;IACd,CAAC,MAAM;MACLJ,SAAS,CAACa,EAAE,CAACZ,CAAC,CAAC,CAACa,UAAU,CACxB;QAACC,IAAI,EAAE,OAAO;QAAEC,EAAE,EAAE,KAAK,GAACP,SAAS,CAAC,CAAC;MAAC,CAAC,CACxC;MACD,OAAO,KAAK;IAEd;EACF;EAEAd,gBAAgBA,CAACf,YAAiB;IAEhC,IAAI,CAACnJ,IAAI,CAACwL,GAAG,CAAC,cAAc,CAAC,EAAEH,UAAU,CAAClC,YAAY,CAACC,YAAY,IAAI,CAAC,IAAI,CAACqC,gBAAgB,CAACtC,YAAa,CAACjJ,GAAG,CAAC,CAAC;IACjH,IAAI,CAACF,IAAI,CAACwL,GAAG,CAAC,eAAe,CAAC,EAAEH,UAAU,CAAClC,YAAY,CAACE,aAAa,IAAI,CAAC,IAAI,CAACoC,gBAAgB,CAACtC,YAAa,CAAC/H,GAAG,CAAC,CAAC;IACnH,IAAI,CAACpB,IAAI,CAACwL,GAAG,CAAC,iBAAiB,CAAC,EAAEH,UAAU,CAAClC,YAAY,CAACG,eAAe,IAAI,CAAC,IAAI,CAACmC,gBAAgB,CAACtC,YAAa,CAACjH,GAAG,CAAC,CAAC;IACvH,IAAI,CAAClC,IAAI,CAACwL,GAAG,CAAC,gBAAgB,CAAC,EAAEH,UAAU,CAAClC,YAAY,CAACI,cAAc,IAAI,CAAC,IAAI,CAACkC,gBAAgB,CAACtC,YAAa,CAACnG,GAAG,CAAC,CAAC;IACrH,IAAI,CAAChD,IAAI,CAACwL,GAAG,CAAC,cAAc,CAAC,EAAEH,UAAU,CAAClC,YAAY,CAACK,YAAY,IAAI,CAAC,IAAI,CAACiC,gBAAgB,CAACtC,YAAa,CAACrF,GAAG,CAAC,CAAC;IACjH,IAAI,CAAC9D,IAAI,CAACwL,GAAG,CAAC,gBAAgB,CAAC,EAAEH,UAAU,CAAClC,YAAY,CAACM,cAAc,IAAI,CAAC,IAAI,CAACgC,gBAAgB,CAACtC,YAAa,CAACvE,GAAG,CAAC,CAAC;IACrH,IAAI,CAAC5E,IAAI,CAACwL,GAAG,CAAC,cAAc,CAAC,EAAEH,UAAU,CAAClC,YAAY,CAACO,YAAY,IAAI,CAAC,IAAI,CAAC+B,gBAAgB,CAACtC,YAAa,CAACzD,GAAG,CAAC,CAAC;IACjH,IAAI,CAAC1F,IAAI,CAACwL,GAAG,CAAC,OAAO,CAAC,EAAEH,UAAU,CAAClC,YAAY,CAACQ,KAAK,CAAC;IAEtD,MAAMP,YAAY,GAAG,IAAI,CAACpJ,IAAI,CAACwL,GAAG,CAAC,cAAc,CAAC,EAAEE,KAAK;IACzD,MAAMrC,aAAa,GAAG,IAAI,CAACrJ,IAAI,CAACwL,GAAG,CAAC,eAAe,CAAC,EAAEE,KAAK;IAC3D,MAAMpC,eAAe,GAAG,IAAI,CAACtJ,IAAI,CAACwL,GAAG,CAAC,iBAAiB,CAAC,EAAEE,KAAK;IAC/D,MAAMnC,cAAc,GAAG,IAAI,CAACvJ,IAAI,CAACwL,GAAG,CAAC,gBAAgB,CAAC,EAAEE,KAAK;IAC7D,MAAMlC,YAAY,GAAG,IAAI,CAACxJ,IAAI,CAACwL,GAAG,CAAC,cAAc,CAAC,EAAEE,KAAK;IACzD,MAAMjC,cAAc,GAAG,IAAI,CAACzJ,IAAI,CAACwL,GAAG,CAAC,gBAAgB,CAAC,EAAEE,KAAK;IAC7D,MAAMhC,YAAY,GAAG,IAAI,CAAC1J,IAAI,CAACwL,GAAG,CAAC,cAAc,CAAC,EAAEE,KAAK;IAGzD,IAAIvC,YAAY,EAAEjJ,GAAG,IAAIkJ,YAAY,EAAE;MACrC,KAAK,IAAIuC,CAAC,IAAIxC,YAAa,CAACjJ,GAAG,EAAE;QAC/B,IAAI,CAAC6H,sBAAsB,CAAC6D,IAAI,CAC9B;UACEN,IAAI,EAAEK,CAAC,CAACL,IAAI;UACZC,EAAE,EAAEI,CAAC,CAACJ;SACP,CACF;MACH;IACF;IAEA,IAAIpC,YAAY,EAAE/H,GAAG,IAAIiI,aAAa,EAAE;MACtC,KAAK,IAAIsC,CAAC,IAAIxC,YAAa,CAAC/H,GAAG,EAAE;QAC/B,IAAI,CAAC4G,sBAAsB,CAAC4D,IAAI,CAC9B;UACEN,IAAI,EAAEK,CAAC,CAACL,IAAI;UACZC,EAAE,EAAEI,CAAC,CAACJ;SACP,CACF;MACH;IACF;IAEA,IAAIpC,YAAY,EAAEjH,GAAG,IAAIoH,eAAe,EAAE;MACxC,KAAK,IAAIqC,CAAC,IAAIxC,YAAa,CAACjH,GAAG,EAAE;QAC/B,IAAI,CAAC+F,sBAAsB,CAAC2D,IAAI,CAC9B;UACEN,IAAI,EAAEK,CAAC,CAACL,IAAI;UACZC,EAAE,EAAEI,CAAC,CAACJ;SACP,CACF;MACH;IACF;IAEA,IAAIpC,YAAY,EAAEnG,GAAG,IAAIuG,cAAc,EAAE;MACvC,KAAK,IAAIoC,CAAC,IAAIxC,YAAa,CAACnG,GAAG,EAAE;QAC/B,IAAI,CAACkF,sBAAsB,CAAC0D,IAAI,CAC9B;UACEN,IAAI,EAAEK,CAAC,CAACL,IAAI;UACZC,EAAE,EAAEI,CAAC,CAACJ;SACP,CACF;MACH;IACF;IAEA,IAAIpC,YAAY,EAAErF,GAAG,IAAI0F,YAAY,EAAE;MACrC,KAAK,IAAImC,CAAC,IAAIxC,YAAa,CAACrF,GAAG,EAAE;QAC/B,IAAI,CAACqE,sBAAsB,CAACyD,IAAI,CAC9B;UACEN,IAAI,EAAEK,CAAC,CAACL,IAAI;UACZC,EAAE,EAAEI,CAAC,CAACJ;SACP,CACF;MACH;IACF;IAEA,IAAIpC,YAAY,EAAEvE,GAAG,IAAI6E,cAAc,EAAE;MACvC,KAAK,IAAIkC,CAAC,IAAIxC,YAAa,CAACvE,GAAG,EAAE;QAC/B,IAAI,CAACwD,sBAAsB,CAACwD,IAAI,CAC9B;UACEN,IAAI,EAAEK,CAAC,CAACL,IAAI;UACZC,EAAE,EAAEI,CAAC,CAACJ;SACP,CACF;MACH;IACF;IAEA,IAAIpC,YAAY,EAAEzD,GAAG,IAAIgE,YAAY,EAAE;MACrC,KAAK,IAAIiC,CAAC,IAAIxC,YAAa,CAACzD,GAAG,EAAE;QAC/B,IAAI,CAAC2C,sBAAsB,CAACuD,IAAI,CAC9B;UACEN,IAAI,EAAEK,CAAC,CAACL,IAAI;UACZC,EAAE,EAAEI,CAAC,CAACJ;SACP,CACF;MACH;IACF;IAEA,IAAI,CAACxD,sBAAsB,EAAE8D,OAAO,CAACC,OAAO,IAAG;MAC7C,IAAI,CAAC5L,GAAG,CAAC0L,IAAI,CAAC,IAAI,CAACzE,EAAE,CAAC4E,KAAK,CAACD,OAAO,CAAC,CAAC;IACvC,CAAC,CAAC;IAEF,IAAI,IAAI,CAAC/D,sBAAsB,CAACiE,MAAM,KAAK,CAAC,EAAE;MAC5C,IAAI,CAACjN,MAAM,EAAE;IACf;IAEA,IAAI,CAACiJ,sBAAsB,EAAE6D,OAAO,CAACC,OAAO,IAAG;MAC7C,IAAI,CAAC1K,GAAG,CAACwK,IAAI,CAAC,IAAI,CAACzE,EAAE,CAAC4E,KAAK,CAACD,OAAO,CAAC,CAAC;IACvC,CAAC,CAAC;IAEF,IAAI,IAAI,CAAC9D,sBAAsB,EAAEgE,MAAM,KAAK,CAAC,EAAE;MAC7C,IAAI,CAACpL,MAAM,EAAE;IACf;IAEA,IAAI,CAACqH,sBAAsB,EAAE4D,OAAO,CAACC,OAAO,IAAG;MAC7C,IAAI,CAAC5J,GAAG,CAAC0J,IAAI,CAAC,IAAI,CAACzE,EAAE,CAAC4E,KAAK,CAACD,OAAO,CAAC,CAAC;IACvC,CAAC,CAAC;IAEF,IAAI,IAAI,CAAC7D,sBAAsB,EAAE+D,MAAM,KAAK,CAAC,EAAE;MAC7C,IAAI,CAACtK,MAAM,EAAE;IACf;IAEA,IAAI,CAACwG,sBAAsB,EAAE2D,OAAO,CAACC,OAAO,IAAG;MAC7C,IAAI,CAAC9I,GAAG,CAAC4I,IAAI,CAAC,IAAI,CAACzE,EAAE,CAAC4E,KAAK,CAACD,OAAO,CAAC,CAAC;IACvC,CAAC,CAAC;IAEF,IAAI,IAAI,CAAC5D,sBAAsB,EAAE8D,MAAM,KAAK,CAAC,EAAE;MAC7C,IAAI,CAACxJ,MAAM,EAAE;IACf;IAEA,IAAI,CAAC2F,sBAAsB,EAAE0D,OAAO,CAACC,OAAO,IAAG;MAC7C,IAAI,CAAChI,GAAG,CAAC8H,IAAI,CAAC,IAAI,CAACzE,EAAE,CAAC4E,KAAK,CAACD,OAAO,CAAC,CAAC;IACvC,CAAC,CAAC;IAEF,IAAI,IAAI,CAAC3D,sBAAsB,EAAE6D,MAAM,KAAK,CAAC,EAAE;MAC7C,IAAI,CAAC1I,MAAM,EAAE;IACf;IAEA,IAAI,CAAC8E,sBAAsB,EAAEyD,OAAO,CAACC,OAAO,IAAG;MAC7C,IAAI,CAAClH,GAAG,CAACgH,IAAI,CAAC,IAAI,CAACzE,EAAE,CAAC4E,KAAK,CAACD,OAAO,CAAC,CAAC;IACvC,CAAC,CAAC;IAEF,IAAI,IAAI,CAAC1D,sBAAsB,EAAE4D,MAAM,KAAK,CAAC,EAAE;MAC7C,IAAI,CAAC5H,MAAM,EAAE;IACf;IAEA,IAAI,CAACiE,sBAAsB,EAAEwD,OAAO,CAACC,OAAO,IAAG;MAC7C,IAAI,CAACpG,GAAG,CAACkG,IAAI,CAAC,IAAI,CAACzE,EAAE,CAAC4E,KAAK,CAACD,OAAO,CAAC,CAAC;IACvC,CAAC,CAAC;IAEF,IAAI,IAAI,CAACzD,sBAAsB,CAAC2D,MAAM,KAAK,CAAC,EAAE;MAC5C,IAAI,CAAC9G,MAAM,EAAE;IACf;EACF;EAEA+G,2BAA2BA,CAACC,KAAU,EAAEC,QAAa,EAAEC,SAAc;IACnE,IAAI,CAACF,KAAK,CAACG,OAAO,EAAE;MAClBF,QAAQ,CAAClM,QAAQ,EAAE4L,OAAO,CAAC,CAACC,OAAY,EAAEvM,KAAa,KAAI;QACzD2L,OAAO,CAACC,GAAG,CAACW,OAAO,CAAC;QACpB,MAAMC,KAAK,GAAG,IAAIhO,gBAAgB,CAAC;UACjCuN,IAAI,EAAE,IAAIxN,kBAAkB,CAAC,EAAE,CAAC;UAChCyN,EAAE,EAAE,IAAIzN,kBAAkB,CAAC,EAAE;SAC9B,CAAC;QACF,IAAI,CAACkC,IAAI,CAACsM,UAAU,CAACF,SAAS,EAAE,IAAI,CAACjF,EAAE,CAACoF,KAAK,CAAC,CAACR,KAAK,CAAC,CAAC,CAAC;MACzD,CAAC,CAAC;IAEJ;EACF;EAEAS,SAASA,CAACC,IAAY,EAAElN,KAAa;IACnC,QAAQkN,IAAI;MACV,KAAK,KAAK;QACR,IAAI,CAACjN,SAAS,CAACD,KAAK,CAAC;QACrB;MACF,KAAK,KAAK;QACR,IAAI,CAACyB,SAAS,CAACzB,KAAK,CAAC;QACrB;MACF,KAAK,KAAK;QACR,IAAI,CAACuC,SAAS,CAACvC,KAAK,CAAC;QACrB;MACF,KAAK,KAAK;QACR,IAAI,CAACqD,SAAS,CAACrD,KAAK,CAAC;QACrB;MACF,KAAK,KAAK;QACR,IAAI,CAACmE,SAAS,CAACnE,KAAK,CAAC;QACrB;MACF,KAAK,KAAK;QACR,IAAI,CAACiF,SAAS,CAACjF,KAAK,CAAC;QACrB;MACF,KAAK,KAAK;QACR,IAAI,CAAC+F,SAAS,CAAC/F,KAAK,CAAC;QACrB;IACJ;EACF;EACA;EACAmN,UAAUA,CAACC,GAAW;IACpBA,GAAG,GAAGA,GAAG,CAACC,WAAW,EAAE;IACvB,MAAMzD,YAAY,GAAG,CAAC,sBAAsBwD,GAAG,EAAE,CAAC;IAClD,IAAIE,IAAI,GAAuB,EAAE;IACjC1D,YAAY,EAAE0C,OAAO,CAACC,OAAO,IAAG;MAC9B,MAAMC,KAAK,GAAG,IAAIhO,gBAAgB,CAAC;QACjCuN,IAAI,EAAE,IAAIxN,kBAAkB,CAAC,EAAE,CAAC;QAChCyN,EAAE,EAAE,IAAIzN,kBAAkB,CAAC,EAAE;OAC9B,CAAC;MACF+O,IAAI,CAACjB,IAAI,CAAC,IAAI,CAACzE,EAAE,CAAC4E,KAAK,CAACA,KAAK,CAAC,CAAC;IACjC,CAAC,CAAC;EACJ;EAEA;EACApD,GAAGA,CAACgE,GAAW;IACb,MAAMG,SAAS,GAAGH,GAAG,CAACI,QAAQ,EAAE;IAChC,MAAMC,OAAO,GAAG,IAAI,CAAChN,IAAI,CAACwL,GAAG,CAACsB,SAAS,CAACF,WAAW,EAAE,CAAqB;IAC1E,MAAMb,KAAK,GAAG,IAAIhO,gBAAgB,CAAC;MACjCuN,IAAI,EAAE,IAAIxN,kBAAkB,CAAC,EAAE,CAAC;MAChCyN,EAAE,EAAE,IAAIzN,kBAAkB,CAAC,EAAE;KAC9B,CAAC;IACDkP,OAAO,CAAEpB,IAAI,CAACG,KAAK,CAAC;EACvB;EAEAhN,MAAMA,CAAA;IACJ,MAAMgN,KAAK,GAAG,IAAIhO,gBAAgB,CAAC;MACjCuN,IAAI,EAAE,IAAIxN,kBAAkB,CAAC,EAAE,CAAC;MAChCyN,EAAE,EAAE,IAAIzN,kBAAkB,CAAC,EAAE;KAC9B,CAAC;IACF,IAAI,CAACoC,GAAG,CAAC0L,IAAI,CAACG,KAAK,CAAC;EACtB;EAEAvM,SAASA,CAACD,KAAa;IACrB2L,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAC5L,KAAK,CAAC;IAC3B,IAAI,CAACW,GAAG,CAAC+M,QAAQ,CAAC1N,KAAK,CAAC;EAC1B;EAEAqB,MAAMA,CAAA;IACJ,MAAMmL,KAAK,GAAG,IAAIhO,gBAAgB,CAAC;MACjCuN,IAAI,EAAE,IAAIxN,kBAAkB,CAAC,EAAE,CAAC;MAChCyN,EAAE,EAAE,IAAIzN,kBAAkB,CAAC,EAAE;KAC9B,CAAC;IACF,IAAI,CAACsD,GAAG,CAACwK,IAAI,CAACG,KAAK,CAAC;EACtB;EAEA/K,SAASA,CAACzB,KAAa;IACrB,IAAI,CAAC6B,GAAG,CAAC6L,QAAQ,CAAC1N,KAAK,CAAC;EAC1B;EAEAmC,MAAMA,CAAA;IACJ,MAAMqK,KAAK,GAAG,IAAIhO,gBAAgB,CAAC;MACjCuN,IAAI,EAAE,IAAIxN,kBAAkB,CAAC,EAAE,CAAC;MAChCyN,EAAE,EAAE,IAAIzN,kBAAkB,CAAC,EAAE;KAC9B,CAAC;IACF,IAAI,CAACoE,GAAG,CAAC0J,IAAI,CAACG,KAAK,CAAC;EACtB;EAEAjK,SAASA,CAACvC,KAAa;IACrB,IAAI,CAAC2C,GAAG,CAAC+K,QAAQ,CAAC1N,KAAK,CAAC;EAC1B;EAEAiD,MAAMA,CAAA;IACJ,MAAMuJ,KAAK,GAAG,IAAIhO,gBAAgB,CAAC;MACjCuN,IAAI,EAAE,IAAIxN,kBAAkB,CAAC,EAAE,CAAC;MAChCyN,EAAE,EAAE,IAAIzN,kBAAkB,CAAC,EAAE;KAC9B,CAAC;IACF,IAAI,CAACkF,GAAG,CAAC4I,IAAI,CAACG,KAAK,CAAC;EACtB;EAEAnJ,SAASA,CAACrD,KAAa;IACrB,IAAI,CAACyD,GAAG,CAACiK,QAAQ,CAAC1N,KAAK,CAAC;EAC1B;EAEA+D,MAAMA,CAAA;IACJ,MAAMyI,KAAK,GAAG,IAAIhO,gBAAgB,CAAC;MACjCuN,IAAI,EAAE,IAAIxN,kBAAkB,CAAC,EAAE,CAAC;MAChCyN,EAAE,EAAE,IAAIzN,kBAAkB,CAAC,EAAE;KAC9B,CAAC;IACF,IAAI,CAACgG,GAAG,CAAC8H,IAAI,CAACG,KAAK,CAAC;EACtB;EAEArI,SAASA,CAACnE,KAAa;IACrB,IAAI,CAACuE,GAAG,CAACmJ,QAAQ,CAAC1N,KAAK,CAAC;EAC1B;EAEA6E,MAAMA,CAAA;IACJ,MAAM2H,KAAK,GAAG,IAAIhO,gBAAgB,CAAC;MACjCuN,IAAI,EAAE,IAAIxN,kBAAkB,CAAC,EAAE,CAAC;MAChCyN,EAAE,EAAE,IAAIzN,kBAAkB,CAAC,EAAE;KAC9B,CAAC;IACF,IAAI,CAAC8G,GAAG,CAACgH,IAAI,CAACG,KAAK,CAAC;EACtB;EAEAvH,SAASA,CAACjF,KAAa;IACrB,IAAI,CAACqF,GAAG,CAACqI,QAAQ,CAAC1N,KAAK,CAAC;EAC1B;EAEA2F,MAAMA,CAAA;IACJ,MAAM6G,KAAK,GAAG,IAAIhO,gBAAgB,CAAC;MACjCuN,IAAI,EAAE,IAAIxN,kBAAkB,CAAC,EAAE,CAAC;MAChCyN,EAAE,EAAE,IAAIzN,kBAAkB,CAAC,EAAE;KAC9B,CAAC;IACF,IAAI,CAAC4H,GAAG,CAACkG,IAAI,CAACG,KAAK,CAAC;EACtB;EAEAzG,SAASA,CAAC/F,KAAa;IACrB,IAAI,CAACmG,GAAG,CAACuH,QAAQ,CAAC1N,KAAK,CAAC;EAC1B;EAEA,IAAIW,GAAGA,CAAA;IACL,OAAO,IAAI,CAACF,IAAI,CAACwL,GAAG,CAAC,KAAK,CAAqB;EACjD;EAEA,IAAIpK,GAAGA,CAAA;IACL,OAAO,IAAI,CAACpB,IAAI,CAACwL,GAAG,CAAC,KAAK,CAAqB;EACjD;EAEA,IAAItJ,GAAGA,CAAA;IACL,OAAO,IAAI,CAAClC,IAAI,CAACwL,GAAG,CAAC,KAAK,CAAqB;EACjD;EAEA,IAAIxI,GAAGA,CAAA;IACL,OAAO,IAAI,CAAChD,IAAI,CAACwL,GAAG,CAAC,KAAK,CAAqB;EACjD;EAEA,IAAI1H,GAAGA,CAAA;IACL,OAAO,IAAI,CAAC9D,IAAI,CAACwL,GAAG,CAAC,KAAK,CAAqB;EACjD;EAEA,IAAI5G,GAAGA,CAAA;IACL,OAAO,IAAI,CAAC5E,IAAI,CAACwL,GAAG,CAAC,KAAK,CAAqB;EACjD;EAEA,IAAI9F,GAAGA,CAAA;IACL,OAAO,IAAI,CAAC1F,IAAI,CAACwL,GAAG,CAAC,KAAK,CAAqB;EACjD;EAEA/E,QAAQA,CAAA;IACN,IAAI,CAAC8B,SAAS,GAAG,IAAI;IAErB;IACA;IACA;IAEA,IAAIY,YAAY,GAAqB;MACnC+D,EAAE,EAAE,QAAQ;MACZC,IAAI,EAAElP,oBAAoB,CAACmP,SAAS;MACpClN,GAAG,EAAE,EAAE;MACPkB,GAAG,EAAE,EAAE;MACPc,GAAG,EAAE,EAAE;MACPc,GAAG,EAAE,EAAE;MACPc,GAAG,EAAE,EAAE;MACPc,GAAG,EAAE,EAAE;MACPc,GAAG,EAAE;KACN;IAED,MAAM2H,IAAI,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IAC9D,MAAMC,SAAS,GAAG,CAAC,cAAc,EAAE,eAAe,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,CAAC;IAC1I,MAAMrN,QAAQ,GAAG,IAAI,CAACD,IAAI,CAACC,QAAQ;IACnC,KAAK,IAAI0M,GAAG,IAAIU,IAAI,EAAE;MACpB,MAAME,SAAS,GAAGZ,GAA6B;MAC/C,IAAIa,KAAK,CAACC,OAAO,CAACtE,YAAY,CAACoE,SAAS,CAAC,CAAC,EAAE;QAC1C,KAAK,IAAIG,gBAAgB,IAAIzN,QAAQ,CAAC0M,GAAG,CAAC,CAACjB,KAAK,EAAE;UAChD,KAAK,IAAIiC,kBAAkB,IAAIL,SAAS,EAAE;YAC1C,IAAI,IAAI,CAACtN,IAAI,CAACwL,GAAG,CAACmC,kBAAkB,CAAE,CAACjC,KAAK,EAAE;cAC3CvC,YAAY,CAACoE,SAAS,CAA6B,CAAC3B,IAAI,CAAC;gBAAEN,IAAI,EAAEoC,gBAAgB,CAACpC,IAAI;gBAAEC,EAAE,EAAEmC,gBAAgB,CAACnC;cAAE,CAAE,CAAC;YACrH;UACF;QACA;MACF;IACF;IAEA;IAEA,IAAI,IAAI,CAACpM,QAAQ,EAAE;MACjB,IAAI,CAAC0I,IAAI,CAACc,GAAG,CAAC,IAAI,CAACrB,cAAc,CAACsG,yBAAyB,CAAC,IAAI,CAAC5N,IAAI,CAAC0L,KAAK,CAAC,CAAC7C,SAAS,CAACC,GAAG,IAAG;QAC3F,IAAIA,GAAG,EAAE;UACP,IAAI,CAACtB,KAAK,CAACqG,mBAAmB,CAAC;YAC7BC,QAAQ,EAAE,SAAS;YACnBC,OAAO,EAAE,EAAE;YACXC,MAAM,EAAE;WACT,CAAC;UACF,IAAI,CAACtG,eAAe,CAACuG,wBAAwB,CAAC,IAAI,CAAC;UACnD,IAAI,CAACvG,eAAe,CAACwG,iBAAiB,CAAC,IAAI,CAAC;QAC9C;MACF,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MAEL,IAAI,CAACrG,IAAI,CAACc,GAAG,CAAC,IAAI,CAACrB,cAAc,CAAC6G,gCAAgC,CAAC,IAAI,CAACnO,IAAI,CAAC0L,KAAK,CAAC,CAAC7C,SAAS,CAACC,GAAG,IAAG;QAClG,IAAIA,GAAG,EAAE;UACP,IAAI,CAACtB,KAAK,CAACqG,mBAAmB,CAAC;YAC7BC,QAAQ,EAAE,SAAS;YACnBC,OAAO,EAAE,EAAE;YACXC,MAAM,EAAE;WACT,CAAC;UACF,IAAI,CAAC5G,MAAM,CAACgH,aAAa,CAAC,iBAAiB,EAAE;YAAEC,UAAU,EAAE;UAAI,CAAE,CAAC;QACpE;QACA,IAAI,CAACvF,GAAG,EAAE;UACR,IAAI,CAACtB,KAAK,CAACqG,mBAAmB,CAAC;YAC7BC,QAAQ,EAAE,MAAM;YAChBC,OAAO,EAAE,EAAE;YACXC,MAAM,EAAE;WACT,CAAC;QACJ;MACF,CAAC,CAAC,CAAC;MACH,IAAI,IAAI,CAAChH,IAAI,EAAE;QACb,IAAI,CAACA,IAAK,CAACmC,YAAY,GAAGA,YAAY;QACtC,IAAI,CAACnC,IAAK,CAAC4C,cAAc,GAAG,IAAI,CAACtB,aAAa;QAC9C4C,OAAO,CAACC,GAAG,CAAC,IAAI,CAACnE,IAAI,CAAC;QACtB,IAAI,CAACO,WAAW,CAAC+G,kBAAkB,EAAE;QACrC,IAAI,CAACzG,IAAI,CAACc,GAAG,CAAC,IAAI,CAACrB,cAAc,CAACiH,kBAAkB,CAAC,IAAI,CAACvH,IAAI,CAACmC,YAAY,CAAC,CAACN,SAAS,CAACC,GAAG,IAAG;UAC3F0F,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC3H,IAAI,CAAC,CAAC;UACvD,IAAI,CAACM,cAAc,CAACsH,8CAA8C,CAAC,IAAI,CAAC;QAC1E,CAAC,CAAC,CAAC;QACH,IAAI,CAACpH,KAAK,CAACqG,mBAAmB,CAAC;UAC7BC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;MACJ,CAAC,MACI;QACH,IAAI,CAACjF,OAAQ,CAACI,YAAY,GAAGA,YAAY;QACzC,IAAI,CAACJ,OAAQ,CAACa,cAAc,GAAG,IAAI,CAACtB,aAAa;QACjD;MACF;IACF;EACF;EAEAuG,WAAWA,CAACvD,IAAY,EAAEC,EAAU;IAClC,MAAMuD,WAAW,GAAGxD,IAAI,CAACU,MAAM,KAAK,CAAC,IAAIV,IAAI,CAACyD,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC;IAChE,MAAMC,SAAS,GAAGzD,EAAE,CAACS,MAAM,KAAK,CAAC,IAAIT,EAAE,CAACwD,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC;IAE1D,OAAOD,WAAW,IAAIE,SAAS;EACjC;EAEMC,MAAMA,CAACC,GAAW;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtB,MAAMD,KAAI,CAAC/H,MAAM,CAACgH,aAAa,CAAC,GAAG,EAAE;QAAEiB,kBAAkB,EAAE;MAAI,CAAE,CAAC;MAClE,OAAOF,KAAI,CAAC/H,MAAM,CAACgH,aAAa,CAACc,GAAG,CAAC;IAAC;EACxC;EAEAhJ,aAAaA,CAACgG,KAAU;IACtBoD,UAAU,CAAC,MAAK;MACd,IAAI,CAAChH,aAAa,GAAG4D,KAAK,CAACR,KAAK;IAClC,CAAC,EAAE,GAAG,CAAC;EACT;EAEA6D,aAAaA,CAACC,IAAY;IACxB,IAAIA,IAAI,CAACxD,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAC/B,IAAIf,KAAK,GAAGuE,IAAI,CAACvE,KAAK,CAAC,GAAG,CAAC;IAC3B,IAAItB,KAAK,GAAG8F,QAAQ,CAACxE,KAAK,CAAC,CAAC,CAAC,CAAC;IAC9B,IAAIyE,OAAO,GAAGD,QAAQ,CAACxE,KAAK,CAAC,CAAC,CAAC,CAAC;IAChC,OAAQtB,KAAK,IAAI+F,OAAO;EAC1B;EAEA;;;;;EAKApJ,YAAYA,CAACqJ,KAAa;IACxB,IAAI3C,OAAO,GAAG,IAAI,CAAChN,IAAI,CAACwL,GAAG,CAACmE,KAAK,CAAC;IAClC,IAAIC,UAAU,GAAG,KAAK;IAEtB,IAAK5C,OAA4B,CAAC/M,QAAQ,EAAE;MAC1C,KAAK,IAAI4P,CAAC,IAAK7C,OAA4B,CAAC/M,QAAQ,EAAE;QACpD,IAAIqL,IAAI,GAAIuE,CAAsB,CAAC5P,QAAQ,CAACqL,IAAI,CAACI,KAAK,CAACqB,QAAQ,EAAE;QACjE,IAAIxB,EAAE,GAAIsE,CAAsB,CAAC5P,QAAQ,CAACsL,EAAE,CAACG,KAAK,CAACqB,QAAQ,EAAE;QAC7D6C,UAAU,GAAGA,UAAU,IAAK,IAAI,CAACrH,SAAS,IAAIyE,OAAQ,CAAC8C,OAAO,KAAKC,KAAK,CAAC,IAAI,CAACR,aAAa,CAACjE,IAAI,CAAC,CAAC,IAAIyE,KAAK,CAAC,IAAI,CAACR,aAAa,CAAChE,EAAE,CAAC,CAAC,CAAE;MACvI;IACF,CAAC,MAAM;MACLqE,UAAU,GAAG,IAAI,CAAC5P,IAAI,CAACwL,GAAG,CAACmE,KAAK,CAAE,CAACK,OAAO,KAAK,IAAI,CAACzH,SAAS,IAAI,IAAI,CAACvI,IAAI,CAACwL,GAAG,CAACmE,KAAK,CAAE,CAACG,OAAO,CAAC;IACjG;IAEA,IAAK9C,OAA4B,CAAC/M,QAAQ,EAAE+L,MAAM,KAAK,CAAC,EAAE;MACxD4D,UAAU,GAAG,KAAK;IACpB;IAEA;IACA;IACA;IAEA,OAAOA,UAAU;EACnB;EAEAhJ,MAAMA,CAAA;IACJ,IAAI,CAACQ,MAAM,CAACgH,aAAa,CAAC,iBAAiB,EAAE;MAAEC,UAAU,EAAE;IAAI,CAAE,CAAC;EACpE;EAEQ5C,gBAAgBA,CAACwE,IAAoC;IAC3D,IAAI,IAAI,CAACtI,cAAc,CAACuI,eAAe,CAACD,IAAI,CAAC,EAAE;MAC7C,OAAO,IAAI;IACb;IACA,IAAIA,IAAI,CAACjE,MAAM,KAAK,CAAC,EAAE;MACrB,OAAO,IAAI,CAAC,CAAC;IACf;IACA,KAAK,MAAMmE,IAAI,IAAIF,IAAI,EAAE;MACvB,IAAIE,IAAI,CAAC7E,IAAI,CAAC8E,IAAI,EAAE,KAAK,EAAE,IAAID,IAAI,CAAC5E,EAAE,CAAC6E,IAAI,EAAE,KAAK,EAAE,EAAE;QACpD,OAAO,IAAI,CAAC,CAAC;MACf;IACF;IACA,OAAO,KAAK,CAAC,CAAC;EAChB;EAAC,QAAAC,CAAA,G;qBAnnBUpJ,qBAAqB,EAAA9I,EAAA,CAAAmS,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAArS,EAAA,CAAAmS,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAvS,EAAA,CAAAmS,iBAAA,CAAAK,EAAA,CAAAC,QAAA,GAAAzS,EAAA,CAAAmS,iBAAA,CAAAO,EAAA,CAAAC,yBAAA,GAAA3S,EAAA,CAAAmS,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAA7S,EAAA,CAAAmS,iBAAA,CAAAW,EAAA,CAAAC,YAAA,GAAA/S,EAAA,CAAAmS,iBAAA,CAAAa,EAAA,CAAAC,WAAA,GAAAjT,EAAA,CAAAmS,iBAAA,CAAAe,EAAA,CAAAC,eAAA,GAAAnT,EAAA,CAAAmS,iBAAA,CAAAiB,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAArBxK,qBAAqB;IAAAyK,SAAA;IAAAC,MAAA;MAAA3K,IAAA;MAAAY,SAAA;MAAAzI,QAAA;IAAA;IAAAyS,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCjB9B9T,EAFJ,CAAAC,cAAA,aAAkC,cACT,aACwB;QACzCD,EAAA,CAAAuB,UAAA,IAAAyS,oCAAA,iBAAwD;QAQpDhU,EAFJ,CAAAC,cAAA,aAA0G,aACf,aACxC;QAC3CD,EAAA,CAAAE,MAAA,YACJ;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,aAA2D;QACvDD,EAAA,CAAAE,MAAA,eACJ;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAGFH,EADJ,CAAAC,cAAA,cAA2E,cACrB;QAC9CD,EAAA,CAAAE,MAAA,gBACJ;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,cAAkD;QAC9CD,EAAA,CAAAE,MAAA,cACJ;QAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;QAGEH,EAFR,CAAAC,cAAA,eAA8B,eACa,eAC8C;QAC7ED,EAAA,CAAAE,MAAA,gBACJ;QACJF,EADI,CAAAG,YAAA,EAAM,EACJ;QAGEH,EAFR,CAAAC,cAAA,eAAiD,eACe,sBAC8H;QAA7DD,EAAA,CAAAK,UAAA,sBAAA4T,+DAAAvS,MAAA;UAAA,OAAYqS,GAAA,CAAAjG,2BAAA,CAAApM,MAAA,EAAAqS,GAAA,CAAAhS,GAAA,EAAyC,KAAK,CAAC;QAAA,EAAC;QAE7L/B,EAF8L,CAAAG,YAAA,EAAa,EACjM,EACJ;QAIEH,EAHR,CAAAC,cAAA,eAAmC,eAEqC,eACR;QACpDD,EAAA,CAAAsB,uBAAA,QAAkC;QAiC9BtB,EAhCA,CAAAuB,UAAA,KAAA2S,qCAAA,oBAA4E,KAAAC,qCAAA,kBAgCvB;;QAMzEnU,EAHY,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ;QAGEH,EAFR,CAAAC,cAAA,eAA8B,eACa,eAC8C;QAC7ED,EAAA,CAAAE,MAAA,iBACJ;QACJF,EADI,CAAAG,YAAA,EAAM,EACJ;QAGEH,EAFR,CAAAC,cAAA,eAAiD,eACe,sBACgI;QAApFD,EAAA,CAAAK,UAAA,sBAAA+T,+DAAA1S,MAAA;UAAA,OAAYqS,GAAA,CAAAjG,2BAAA,CAAApM,MAAA,EAAAqS,GAAA,CAAA9Q,GAAA,EAAyC,KAAK,CAAC;QAAA,EAAC;QAExKjD,EAFgM,CAAAG,YAAA,EAAa,EACnM,EACJ;QAIEH,EAHR,CAAAC,cAAA,eACkG,eACpE,eACuG;QACzHD,EAAA,CAAAsB,uBAAA,QAAkC;QAkC9BtB,EAjCA,CAAAuB,UAAA,KAAA8S,qCAAA,oBAA4E,KAAAC,qCAAA,kBAiCvB;;QAMzEtU,EAHY,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ;QAGEH,EAFR,CAAAC,cAAA,eAA8B,eACa,eAC8C;QAC7ED,EAAA,CAAAE,MAAA,mBACJ;QACJF,EADI,CAAAG,YAAA,EAAM,EACJ;QAGEH,EAFR,CAAAC,cAAA,eAAiD,eACe,sBACoI;QAApFD,EAAA,CAAAK,UAAA,sBAAAkU,+DAAA7S,MAAA;UAAA,OAAYqS,GAAA,CAAAjG,2BAAA,CAAApM,MAAA,EAAAqS,GAAA,CAAAhQ,GAAA,EAAyC,KAAK,CAAC;QAAA,EAAC;QAE5K/D,EAFoM,CAAAG,YAAA,EAAa,EACvM,EACJ;QAGEH,EAFR,CAAAC,cAAA,eAA0G,eAC5E,eACkC;QACpDD,EAAA,CAAAsB,uBAAA,QAAkC;QAiC9BtB,EAhCA,CAAAuB,UAAA,KAAAiT,qCAAA,oBAA4E,KAAAC,qCAAA,kBAgCvB;;QAMzEzU,EAHY,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ;QAGEH,EAFR,CAAAC,cAAA,eAA8B,eACa,eAC8C;QAC7ED,EAAA,CAAAE,MAAA,kBACJ;QACJF,EADI,CAAAG,YAAA,EAAM,EACJ;QAGEH,EAFR,CAAAC,cAAA,eAAiD,eACe,sBACkI;QAApFD,EAAA,CAAAK,UAAA,sBAAAqU,+DAAAhT,MAAA;UAAA,OAAYqS,GAAA,CAAAjG,2BAAA,CAAApM,MAAA,EAAAqS,GAAA,CAAAlP,GAAA,EAAyC,KAAK,CAAC;QAAA,EAAC;QAE1K7E,EAFkM,CAAAG,YAAA,EAAa,EACrM,EACJ;QAGEH,EAFR,CAAAC,cAAA,eAAyG,eAC3E,eACkC;QACpDD,EAAA,CAAAsB,uBAAA,QAAkC;QAkC9BtB,EAjCA,CAAAuB,UAAA,KAAAoT,qCAAA,oBAA4E,KAAAC,qCAAA,kBAiCvB;;QAMzE5U,EAHY,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ;QAGEH,EAFR,CAAAC,cAAA,eAA8B,eACa,eAC8C;QAC7ED,EAAA,CAAAE,MAAA,gBACJ;QACJF,EADI,CAAAG,YAAA,EAAM,EACJ;QAGEH,EAFR,CAAAC,cAAA,eAAiD,eACe,sBAC8H;QAApFD,EAAA,CAAAK,UAAA,sBAAAwU,+DAAAnT,MAAA;UAAA,OAAYqS,GAAA,CAAAjG,2BAAA,CAAApM,MAAA,EAAAqS,GAAA,CAAApO,GAAA,EAAyC,KAAK,CAAC;QAAA,EAAC;QAEtK3F,EAF8L,CAAAG,YAAA,EAAa,EACjM,EACJ;QAGEH,EAFR,CAAAC,cAAA,eAAuG,eACzE,eACkC;QACpDD,EAAA,CAAAsB,uBAAA,QAAkC;QAkC9BtB,EAjCA,CAAAuB,UAAA,KAAAuT,qCAAA,oBAA4E,KAAAC,qCAAA,kBAiCvB;;QAMzE/U,EAHY,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ;QAGEH,EAFR,CAAAC,cAAA,eAA8B,eACa,eAC8C;QAC7ED,EAAA,CAAAE,MAAA,kBACJ;QACJF,EADI,CAAAG,YAAA,EAAM,EACJ;QAGEH,EAFR,CAAAC,cAAA,eAAiD,eACe,sBACkI;QAApFD,EAAA,CAAAK,UAAA,sBAAA2U,+DAAAtT,MAAA;UAAA,OAAYqS,GAAA,CAAAjG,2BAAA,CAAApM,MAAA,EAAAqS,GAAA,CAAAtN,GAAA,EAAyC,KAAK,CAAC;QAAA,EAAC;QAE1KzG,EAFkM,CAAAG,YAAA,EAAa,EACrM,EACJ;QAGEH,EAFR,CAAAC,cAAA,eAAyG,eAC3E,eACkC;QACpDD,EAAA,CAAAsB,uBAAA,QAAkC;QAiC9BtB,EAhCA,CAAAuB,UAAA,KAAA0T,qCAAA,oBAA4E,KAAAC,qCAAA,kBAgCvB;;QAMzElV,EAHY,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ;QAGEH,EAFR,CAAAC,cAAA,eAA8B,eACa,eAC8C;QAC7ED,EAAA,CAAAE,MAAA,gBACJ;QACJF,EADI,CAAAG,YAAA,EAAM,EACJ;QAGEH,EAFR,CAAAC,cAAA,eAAiD,eACe,sBAC8H;QAApFD,EAAA,CAAAK,UAAA,sBAAA8U,+DAAAzT,MAAA;UAAA,OAAYqS,GAAA,CAAAjG,2BAAA,CAAApM,MAAA,EAAAqS,GAAA,CAAAxM,GAAA,EAAyC,KAAK,CAAC;QAAA,EAAC;QAEtKvH,EAF8L,CAAAG,YAAA,EAAa,EACjM,EACJ;QAGEH,EAFR,CAAAC,cAAA,gBAAuG,gBACzE,gBACkC;QACpDD,EAAA,CAAAsB,uBAAA,SAAkC;QAiC9BtB,EAhCA,CAAAuB,UAAA,MAAA6T,sCAAA,oBAA4E,MAAAC,sCAAA,kBAgCvB;;QAQ7ErV,EALgB,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ,EACA,EACJ;QAENH,EAAA,CAAAuB,UAAA,MAAA+T,sCAAA,mBAAiD;QAuBrDtV,EAAA,CAAAG,YAAA,EAAO;QASPH,EANA,CAAAuB,UAAA,MAAAgU,+CAAA,2BAA+B,MAAAC,+CAAA,2BAMC;QAchCxV,EAAA,CAAAG,YAAA,EAAM;;;;;;;;;;;QA1cAH,EAAA,CAAAmC,SAAA,EAAkB;QAAlBnC,EAAA,CAAAa,UAAA,cAAAkT,GAAA,CAAAlS,IAAA,CAAkB;QAEV7B,EAAA,CAAAmC,SAAA,GAAe;QAAfnC,EAAA,CAAAa,UAAA,UAAAkT,GAAA,CAAA/S,QAAA,CAAe;QAMoBhB,EAAA,CAAAmC,SAAA,EAAgE;QAAhEnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAyV,eAAA,KAAAC,GAAA,GAAA3B,GAAA,CAAA/S,QAAA,EAAA+S,GAAA,CAAA/S,QAAA,EAAgE;QAoB1EhB,EAAA,CAAAmC,SAAA,IAAyD;QAAzDnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAyV,eAAA,KAAAE,GAAA,EAAA5B,GAAA,CAAA/S,QAAA,GAAA+S,GAAA,CAAA/S,QAAA,EAAyD;QAWhFhB,EAAA,CAAAmC,SAAA,GAAmE;QAAnEnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAA8U,GAAA,KAAAC,OAAA,GAAA9B,GAAA,CAAAlS,IAAA,CAAAwL,GAAA,mCAAAwI,OAAA,CAAAtI,KAAA,GAAmE;QAGpCvN,EAAA,CAAAmC,SAAA,GAAiB;QAAjBnC,EAAA,CAAAa,UAAA,YAAAkT,GAAA,CAAAhS,GAAA,CAAAD,QAAA,CAAiB;QAgC9B9B,EAAA,CAAAmC,SAAA,EAAyB;QAAzBnC,EAAA,CAAAa,UAAA,SAAAkT,GAAA,CAAA5L,YAAA,QAAyB;QASpBnI,EAAA,CAAAmC,SAAA,GAAyD;QAAzDnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAyV,eAAA,KAAAE,GAAA,EAAA5B,GAAA,CAAA/S,QAAA,GAAA+S,GAAA,CAAA/S,QAAA,EAAyD;QAUpFhB,EAAA,CAAAmC,SAAA,GAAiG;QAAjGnC,EAAA,CAAAa,UAAA,gBAAAiV,OAAA,GAAA/B,GAAA,CAAAlS,IAAA,CAAAwL,GAAA,oCAAAyI,OAAA,CAAAvI,KAAA,gDAAiG;QAEvDvN,EAAA,CAAAmC,SAAA,GAAoE;QAApEnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAA8U,GAAA,KAAAG,OAAA,GAAAhC,GAAA,CAAAlS,IAAA,CAAAwL,GAAA,oCAAA0I,OAAA,CAAAxI,KAAA,GAAoE;QAE3EvN,EAAA,CAAAmC,SAAA,GAAiB;QAAjBnC,EAAA,CAAAa,UAAA,YAAAkT,GAAA,CAAA9Q,GAAA,CAAAnB,QAAA,CAAiB;QAiC9B9B,EAAA,CAAAmC,SAAA,EAAyB;QAAzBnC,EAAA,CAAAa,UAAA,SAAAkT,GAAA,CAAA5L,YAAA,QAAyB;QASpBnI,EAAA,CAAAmC,SAAA,GAAyD;QAAzDnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAyV,eAAA,KAAAE,GAAA,EAAA5B,GAAA,CAAA/S,QAAA,GAAA+S,GAAA,CAAA/S,QAAA,EAAyD;QASjDhB,EAAA,CAAAmC,SAAA,GAAsE;QAAtEnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAA8U,GAAA,KAAAI,QAAA,GAAAjC,GAAA,CAAAlS,IAAA,CAAAwL,GAAA,sCAAA2I,QAAA,CAAAzI,KAAA,GAAsE;QAItEvN,EAAA,CAAAmC,SAAA,GAAiB;QAAjBnC,EAAA,CAAAa,UAAA,YAAAkT,GAAA,CAAAhQ,GAAA,CAAAjC,QAAA,CAAiB;QAgC9B9B,EAAA,CAAAmC,SAAA,EAAyB;QAAzBnC,EAAA,CAAAa,UAAA,SAAAkT,GAAA,CAAA5L,YAAA,QAAyB;QASpBnI,EAAA,CAAAmC,SAAA,GAAyD;QAAzDnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAyV,eAAA,KAAAE,GAAA,EAAA5B,GAAA,CAAA/S,QAAA,GAAA+S,GAAA,CAAA/S,QAAA,EAAyD;QASjDhB,EAAA,CAAAmC,SAAA,GAAqE;QAArEnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAA8U,GAAA,KAAAK,QAAA,GAAAlC,GAAA,CAAAlS,IAAA,CAAAwL,GAAA,qCAAA4I,QAAA,CAAA1I,KAAA,GAAqE;QAIrEvN,EAAA,CAAAmC,SAAA,GAAiB;QAAjBnC,EAAA,CAAAa,UAAA,YAAAkT,GAAA,CAAAlP,GAAA,CAAA/C,QAAA,CAAiB;QAiC9B9B,EAAA,CAAAmC,SAAA,EAAyB;QAAzBnC,EAAA,CAAAa,UAAA,SAAAkT,GAAA,CAAA5L,YAAA,QAAyB;QASpBnI,EAAA,CAAAmC,SAAA,GAAyD;QAAzDnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAyV,eAAA,KAAAE,GAAA,EAAA5B,GAAA,CAAA/S,QAAA,GAAA+S,GAAA,CAAA/S,QAAA,EAAyD;QASjDhB,EAAA,CAAAmC,SAAA,GAAmE;QAAnEnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAA8U,GAAA,KAAAM,QAAA,GAAAnC,GAAA,CAAAlS,IAAA,CAAAwL,GAAA,mCAAA6I,QAAA,CAAA3I,KAAA,GAAmE;QAInEvN,EAAA,CAAAmC,SAAA,GAAiB;QAAjBnC,EAAA,CAAAa,UAAA,YAAAkT,GAAA,CAAApO,GAAA,CAAA7D,QAAA,CAAiB;QAiC9B9B,EAAA,CAAAmC,SAAA,EAAyB;QAAzBnC,EAAA,CAAAa,UAAA,SAAAkT,GAAA,CAAA5L,YAAA,QAAyB;QASpBnI,EAAA,CAAAmC,SAAA,GAAyD;QAAzDnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAyV,eAAA,KAAAE,GAAA,EAAA5B,GAAA,CAAA/S,QAAA,GAAA+S,GAAA,CAAA/S,QAAA,EAAyD;QASjDhB,EAAA,CAAAmC,SAAA,GAAqE;QAArEnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAA8U,GAAA,KAAAO,QAAA,GAAApC,GAAA,CAAAlS,IAAA,CAAAwL,GAAA,qCAAA8I,QAAA,CAAA5I,KAAA,GAAqE;QAIrEvN,EAAA,CAAAmC,SAAA,GAAiB;QAAjBnC,EAAA,CAAAa,UAAA,YAAAkT,GAAA,CAAAtN,GAAA,CAAA3E,QAAA,CAAiB;QAgC9B9B,EAAA,CAAAmC,SAAA,EAAyB;QAAzBnC,EAAA,CAAAa,UAAA,SAAAkT,GAAA,CAAA5L,YAAA,QAAyB;QASpBnI,EAAA,CAAAmC,SAAA,GAAyD;QAAzDnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAyV,eAAA,KAAAE,GAAA,EAAA5B,GAAA,CAAA/S,QAAA,GAAA+S,GAAA,CAAA/S,QAAA,EAAyD;QASjDhB,EAAA,CAAAmC,SAAA,GAAmE;QAAnEnC,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,KAAA8U,GAAA,KAAAQ,QAAA,GAAArC,GAAA,CAAAlS,IAAA,CAAAwL,GAAA,mCAAA+I,QAAA,CAAA7I,KAAA,GAAmE;QAInEvN,EAAA,CAAAmC,SAAA,GAAiB;QAAjBnC,EAAA,CAAAa,UAAA,YAAAkT,GAAA,CAAAxM,GAAA,CAAAzF,QAAA,CAAiB;QAgC9B9B,EAAA,CAAAmC,SAAA,EAAyB;QAAzBnC,EAAA,CAAAa,UAAA,SAAAkT,GAAA,CAAA5L,YAAA,QAAyB;QAUvBnI,EAAA,CAAAmC,SAAA,EAAe;QAAfnC,EAAA,CAAAa,UAAA,SAAAkT,GAAA,CAAAtK,SAAA,CAAe;QA0BpCzJ,EAAA,CAAAmC,SAAA,EAAc;QAAdnC,EAAA,CAAAa,UAAA,SAAAkT,GAAA,CAAA/S,QAAA,CAAc;QAMdhB,EAAA,CAAAmC,SAAA,EAAe;QAAfnC,EAAA,CAAAa,UAAA,UAAAkT,GAAA,CAAA/S,QAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}