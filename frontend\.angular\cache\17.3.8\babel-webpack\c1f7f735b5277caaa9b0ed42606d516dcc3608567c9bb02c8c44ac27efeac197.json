{"ast": null, "code": "import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { SubSink } from 'subsink';\nimport { DialogService } from 'primeng/dynamicdialog';\nimport { EditBioDialogComponent } from '../edit-bio-dialog/edit-bio-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/toast.service\";\nimport * as i2 from \"src/app/core/services/general.service\";\nimport * as i3 from \"src/app/core/services/user.service\";\nimport * as i4 from \"src/app/core/services/auth.service\";\nimport * as i5 from \"primeng/dynamicdialog\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"../../../../shared/upload-files/upload-files.component\";\nimport * as i11 from \"../../../../shared/prime/input/prime-input-dropdown/prime-input-dropdown.component\";\nimport * as i12 from \"primeng/button\";\nimport * as i13 from \"../../../../core/directives/button-disable.directive\";\nimport * as i14 from \"primeng/inputtext\";\nimport * as i15 from \"primeng/calendar\";\nimport * as i16 from \"primeng/ripple\";\nconst _c0 = a0 => ({\n  \"ng-invalid ng-dirty\": a0\n});\nfunction InfoComponent_form_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵlistener(\"click\", function InfoComponent_form_2_div_9_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openBioDialog());\n    });\n    i0.ɵɵelementStart(1, \"span\", 43);\n    i0.ɵɵtext(2, \" Add Bio \");\n    i0.ɵɵelement(3, \"img\", 44);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction InfoComponent_form_2_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1, \"* First Name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InfoComponent_form_2_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1, \"* Last Name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InfoComponent_form_2_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1, \"* preferred Name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InfoComponent_form_2_div_30_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1, \"* Personal Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InfoComponent_form_2_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"label\", 24);\n    i0.ɵɵtext(2, \"Personal Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 47);\n    i0.ɵɵtemplate(4, InfoComponent_form_2_div_30_div_4_Template, 2, 0, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ctx_r1.form.controls.personalEmail.invalid && ctx_r1.form.controls.personalEmail.dirty));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"personalEmail\"));\n  }\n}\nfunction InfoComponent_form_2_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1, \"* Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InfoComponent_form_2_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"label\", 48);\n    i0.ɵɵtext(2, \"Country of Origin\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"app-prime-input-dropdown\", 28);\n    i0.ɵɵlistener(\"valueSelected\", function InfoComponent_form_2_div_41_Template_app_prime_input_dropdown_valueSelected_3_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onOriginChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"parentForm\", ctx_r1.form)(\"countries\", ctx_r1.countries)(\"inputName\", \"origin\")(\"withFlags\", true)(\"placeholder\", \"* Country of origin\")(\"selectedItemValue\", ctx_r1.selectedOriginCountry);\n  }\n}\nfunction InfoComponent_form_2_ng_template_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const timezone_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(timezone_r5.text);\n  }\n}\nfunction InfoComponent_form_2_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1, \"* Timezone is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InfoComponent_form_2_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1, \"* Skype is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InfoComponent_form_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 3)(1, \"div\", 4);\n    i0.ɵɵelement(2, \"div\", 5);\n    i0.ɵɵelementStart(3, \"h5\", 6);\n    i0.ɵɵtext(4, \"Avatar\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 7)(6, \"div\", 8)(7, \"div\", 9)(8, \"app-upload-files\", 10);\n    i0.ɵɵlistener(\"onUploadFinished\", function InfoComponent_form_2_Template_app_upload_files_onUploadFinished_8_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.uploadFinished($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, InfoComponent_form_2_div_9_Template, 4, 0, \"div\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"h5\", 6);\n    i0.ɵɵtext(11, \"Personal Info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 7)(13, \"div\", 12)(14, \"div\", 13)(15, \"label\", 14);\n    i0.ɵɵtext(16, \"* First Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(17, \"input\", 15);\n    i0.ɵɵtemplate(18, InfoComponent_form_2_div_18_Template, 2, 0, \"div\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 13)(20, \"label\", 17);\n    i0.ɵɵtext(21, \"* Last Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"input\", 18);\n    i0.ɵɵtemplate(23, InfoComponent_form_2_div_23_Template, 2, 0, \"div\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 13)(25, \"label\", 19);\n    i0.ɵɵtext(26, \"Preferred Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(27, \"input\", 20);\n    i0.ɵɵtemplate(28, InfoComponent_form_2_div_28_Template, 2, 0, \"div\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 12);\n    i0.ɵɵtemplate(30, InfoComponent_form_2_div_30_Template, 5, 4, \"div\", 21);\n    i0.ɵɵelementStart(31, \"div\", 13)(32, \"label\", 22);\n    i0.ɵɵtext(33, \"* Date of Birth\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"p-calendar\", 23);\n    i0.ɵɵlistener(\"onSelect\", function InfoComponent_form_2_Template_p_calendar_onSelect_34_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onBirthDateSelected($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 13)(36, \"label\", 24);\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(38, \"input\", 25);\n    i0.ɵɵtemplate(39, InfoComponent_form_2_div_39_Template, 2, 0, \"div\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 12);\n    i0.ɵɵtemplate(41, InfoComponent_form_2_div_41_Template, 4, 6, \"div\", 26);\n    i0.ɵɵelementStart(42, \"div\", 13)(43, \"label\", 27);\n    i0.ɵɵtext(44, \"* Country of Residence\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"app-prime-input-dropdown\", 28);\n    i0.ɵɵlistener(\"valueSelected\", function InfoComponent_form_2_Template_app_prime_input_dropdown_valueSelected_45_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onResidenceChange($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 29)(47, \"label\", 30);\n    i0.ɵɵtext(48, \"Phone number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 31)(50, \"div\", 32)(51, \"app-prime-input-dropdown\", 33);\n    i0.ɵɵlistener(\"valueSelected\", function InfoComponent_form_2_Template_app_prime_input_dropdown_valueSelected_51_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPhoneCodeChange($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(52, \"input\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(53, \"div\", 12)(54, \"div\", 35)(55, \"label\", 36);\n    i0.ɵɵtext(56, \"* Timezone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"p-dropdown\", 37);\n    i0.ɵɵlistener(\"onChange\", function InfoComponent_form_2_Template_p_dropdown_onChange_57_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTimeZoneChange($event));\n    });\n    i0.ɵɵtemplate(58, InfoComponent_form_2_ng_template_58_Template, 3, 1, \"ng-template\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(59, InfoComponent_form_2_div_59_Template, 2, 0, \"div\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"div\", 39)(61, \"label\", 30);\n    i0.ɵɵtext(62, \"Skype\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(63, \"input\", 40);\n    i0.ɵɵtemplate(64, InfoComponent_form_2_div_64_Template, 2, 0, \"div\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(65, \"div\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function InfoComponent_form_2_Template_button_click_66_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.form);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"isProfile\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isTeacher);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(37, _c0, ctx_r1.form.controls.fname.invalid && ctx_r1.form.controls.fname.dirty));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"fname\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(39, _c0, ctx_r1.form.controls.lname.invalid && ctx_r1.form.controls.lname.dirty));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"lname\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(41, _c0, ctx_r1.form.controls.preferredName.invalid && ctx_r1.form.controls.preferredName.dirty));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"preferredName\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.userRole === \"teacher\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"keepInvalid\", true)(\"firstDayOfWeek\", 1)(\"ngClass\", i0.ɵɵpureFunction1(43, _c0, ctx_r1.form.controls.birth.invalid && ctx_r1.form.controls.birth.dirty));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"* \", ctx_r1.userRole === \"teacher\" ? \"MLT\" : \"\", \" Email\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(45, _c0, ctx_r1.form.controls.email.invalid && ctx_r1.form.controls.email.dirty));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"email\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.userRole === \"student\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"parentForm\", ctx_r1.form)(\"countries\", ctx_r1.countries)(\"inputName\", \"residence\")(\"withFlags\", true)(\"placeholder\", \"* Country of Residence\")(\"selectedItemValue\", ctx_r1.selectedResidenceCountry);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"parentForm\", ctx_r1.form)(\"countries\", ctx_r1.phoneCodes)(\"filter\", true)(\"inputName\", \"phoneCode\")(\"placeholder\", \"\")(\"showTextAfterImage\", false)(\"withFlags\", true)(\"selectedItemValue\", ctx_r1.selectedPhoneCode);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"options\", ctx_r1.timezones)(\"filter\", !ctx_r1.isTablet)(\"showClear\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"timeZone\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"skype\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disableTime\", 1000);\n  }\n}\nexport class InfoComponent {\n  constructor(toast, generalService, userService, authService, dialogService, confirmationService) {\n    this.toast = toast;\n    this.generalService = generalService;\n    this.userService = userService;\n    this.authService = authService;\n    this.dialogService = dialogService;\n    this.confirmationService = confirmationService;\n    this.subs = new SubSink();\n    this.form = new UntypedFormGroup({});\n    this.isLoading = false;\n    this.countries = this.generalService.getCountries();\n    this.selectedOriginCountry = {};\n    this.selectedResidenceCountry = {};\n    this.selectedPhoneCode = {};\n    this.timezones = this.generalService.getTimezones();\n    this.phoneCodes = this.generalService.getPhoneCodes();\n    this.selectedTimezone = \"\";\n    this.imageChangedEvent = '';\n    this.croppedImage = '';\n    this.tryToSave = false;\n    this.isTablet = false;\n    this.loggedInUser = {};\n    this.edit = false;\n    this.formattedBirthDate = \"\";\n    this.userRole = 'teacher';\n    this.refs = [];\n    this.bioChanged = false;\n  }\n  ngOnInit() {\n    this.userRole = this.authService.getUserRole().toLowerCase();\n    this.edit = this.authService.isUserInfoFullfileld() ? false : true;\n    this.subs.add(this.userService.getUserById(this.authService.getLoggedInUser().aspUserId).subscribe(res => {\n      this.loggedInUser = res;\n      this.loadInitialProfileInfo();\n    }));\n    console.log(this.timezones.find(el => el.utc.includes(this.loggedInUser.timeZone)));\n    this.subs.add(this.generalService.deviceKind.subscribe(res => {\n      this.isTablet = res.is1024;\n    }));\n    console.log(this.loggedInUser);\n    // if (this.userRole === 'teacher') {\n    //   this.form.addControl('introduce', new UntypedFormControl(this.loggedInUser.introduce));\n    // }\n  }\n  loadInitialProfileInfo() {\n    this.selectedResidenceCountry = this.countries.find(obj => obj.name === this.loggedInUser.residence);\n    this.selectedOriginCountry = this.countries.find(obj => obj.name === this.loggedInUser.origin);\n    // TODO make dynamic when API get changes\n    this.selectedPhoneCode = this.phoneCodes.filter(el => el.code === this.generalService.extractCountryPrefix(this.loggedInUser.phone))[0] || this.generalService.getDefaultPhoneCode();\n    this.formattedBirthDate = this.generalService.convertToDDMMYYYY(this.loggedInUser.bdate);\n    this.form = new UntypedFormGroup({\n      // image: new UntypedFormControl(null, {\n      //   // validators: [Validators.required],\n      //   // asyncValidators: [mimeType]\n      // }),\n      fname: new UntypedFormControl(this.loggedInUser.firstName, {\n        validators: [Validators.required]\n      }),\n      lname: new UntypedFormControl(this.loggedInUser.lastName, {\n        validators: [Validators.required]\n      }),\n      preferredName: new UntypedFormControl(this.loggedInUser.firstName, {\n        validators: []\n      }),\n      birth: new UntypedFormControl(this.formattedBirthDate, {\n        validators: [Validators.required]\n      }),\n      phoneCode: new UntypedFormControl(this.selectedPhoneCode, {\n        validators: [Validators.required]\n      }),\n      phone: new UntypedFormControl(this.generalService.extractPhoneNumber(this.loggedInUser.phone), {\n        validators: []\n      }),\n      skype: new UntypedFormControl(this.loggedInUser.skype, {\n        validators: [Validators.required]\n      }),\n      personalEmail: new UntypedFormControl(this.loggedInUser.email, {\n        validators: []\n      }),\n      email: new UntypedFormControl(this.loggedInUser.email, {\n        validators: [Validators.required]\n      }),\n      residence: new UntypedFormControl(this.selectedResidenceCountry, {\n        validators: [Validators.required]\n      }),\n      timeZone: new UntypedFormControl(this.timezones.find(el => el.utc.includes(this.loggedInUser.timeZone)), {\n        validators: [Validators.required]\n      }),\n      introduce: new UntypedFormControl(this.loggedInUser.introduce, {\n        validators: []\n      })\n    });\n    this.subscribeToBioChanges();\n    if (this.userRole === 'student') {\n      this.form.addControl('origin', new UntypedFormControl(this.countries.filter(el => el.name === this.loggedInUser.origin)[0], Validators.required));\n      this.form.removeControl('personalEmail');\n    }\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  editMode() {\n    this.edit = true;\n  }\n  cancelEdit() {\n    this.edit = false;\n  }\n  onSubmit() {\n    this.tryToSave = true;\n    let invalidInputs = '';\n    for (const controlName in this.form.controls) {\n      if (this.form.controls.hasOwnProperty(controlName)) {\n        const control = this.form.controls[controlName];\n        if (control.invalid) {\n          invalidInputs += `${controlName}, `;\n        }\n      }\n    }\n    let detailMessage = 'Please enter all required fields to continue.';\n    if (invalidInputs !== '') {\n      invalidInputs = invalidInputs.slice(0, -2); // Remove the trailing comma and space\n      if (invalidInputs !== '') {\n        detailMessage += `Invalid input for the following fields: ${invalidInputs}`;\n      }\n    }\n    if (!this.form.valid) {\n      this.toast.setShowToastmessage({\n        severity: 'warn',\n        summary: '',\n        detail: detailMessage\n      });\n      return;\n    }\n    this.userService.setUpdateProfile(true);\n    // view uploadFinished function here\n  }\n  fileChangeEvent(event) {\n    this.imageChangedEvent = event;\n    document.getElementById('cropped-img').style.borderColor = \"var(--main-color)\";\n  }\n  onOriginChange(event) {\n    this.selectedOriginCountry = event.value;\n  }\n  onResidenceChange(event) {\n    console.log(event);\n    this.selectedResidenceCountry = event.value;\n    this.selectedPhoneCode = this.phoneCodes.filter(el => el.iso === this.selectedResidenceCountry.code)[0] || this.generalService.getDefaultPhoneCode();\n  }\n  onTimeZoneChange(event) {\n    this.selectedTimezone = event.value.text;\n  }\n  onPhoneCodeChange(event) {\n    this.selectedPhoneCode = event.value;\n  }\n  uploadFinished(event) {\n    if (event.filePath !== '') this.loggedInUser.photo = event.filePath.dbPath;\n    let value = this.form.value;\n    this.loggedInUser.personalEmail = value.personalEmail;\n    this.loggedInUser.firstName = value.fname;\n    this.loggedInUser.lastName = value.lname;\n    this.loggedInUser.bdate = this.generalService.convertDateStringToIsoFormatWithZeroTime(this.formattedBirthDate);\n    this.loggedInUser.dateOfBirth = this.generalService.convertDateStringToIsoFormatWithZeroTime(this.formattedBirthDate);\n    this.loggedInUser.phone = this.selectedPhoneCode.code + ' ' + value.phone;\n    this.loggedInUser.skype = value.skype;\n    this.loggedInUser.origin = value.origin?.name;\n    this.loggedInUser.timeZone = value.timeZone?.utc[0];\n    this.loggedInUser.residence = this.selectedResidenceCountry?.name;\n    this.loggedInUser.introduce = value.introduce;\n    this.updateProfileAndAPI();\n  }\n  updateProfileAndAPI() {\n    this.form.value.phoneCode = this.form.value.phoneCode.code;\n    console.log(this.form.value);\n    this.subs.add(this.userService.updateUserProfile(this.loggedInUser).subscribe(res => {\n      localStorage.setItem(\"user\", JSON.stringify(this.loggedInUser));\n      this.userService.updateAvatarUrl(this.loggedInUser.avatarUrl);\n      this.userService.updateUserListener();\n      this.toast.setShowToastmessage({\n        severity: 'success',\n        summary: '',\n        detail: 'Changes were saved successfully!'\n      });\n      this.form.markAsPristine();\n      // this.toastr.success(\"Changes were saved successfully!\")\n      // this.edit = false;\n    }));\n  }\n  ifFieldValid(field) {\n    return this.form.get(field)?.invalid && this.form.get(field)?.touched || this.tryToSave && this.form.get(field)?.invalid;\n  }\n  getUserPhoto() {\n    return this.userService.getUserPhoto(this.loggedInUser);\n  }\n  onBirthDateSelected(event) {\n    console.log(event);\n    let d = new Date(Date.parse(event));\n    this.formattedBirthDate = this.generalService.convertToDDMMYYYY(event);\n  }\n  toggleEdit() {\n    this.edit = !this.edit;\n  }\n  findCountryImage(phoneCode) {\n    return this.generalService.findCountryImage(phoneCode);\n  }\n  openBioDialog() {\n    const dialogRef = this.dialogService.open(EditBioDialogComponent, {\n      header: 'Choose a Product',\n      width: '26rem',\n      styleClass: 'border-round-xl',\n      showHeader: false,\n      dismissableMask: true,\n      modal: true,\n      contentStyle: {\n        \"max-height\": \"500px\",\n        \"overflow\": \"auto\",\n        \"border-radius\": \"12px\",\n        \"padding\": \"0px\"\n      },\n      baseZIndex: 10000,\n      maskStyleClass: 'transparent-mask',\n      data: {\n        dialogData: {\n          introduce: this.loggedInUser.introduce\n        }\n      }\n    });\n    this.refs.push(dialogRef);\n    dialogRef.onClose.subscribe(data => {\n      if (data) {\n        console.log(data);\n        this.form.get('introduce')?.patchValue(data.data);\n        this.form.get('introduce')?.updateValueAndValidity(data.data);\n        this.loggedInUser.introduce = data.data;\n      }\n    });\n  }\n  subscribeToBioChanges() {\n    this.subs.add(this.form.get('introduce')?.valueChanges.subscribe(value => {\n      if (value !== this.loggedInUser.introduce) {\n        this.bioChanged = true;\n      }\n    }));\n  }\n  /**\n   * Checks if there are any unsaved changes in the form or in the bio field.\n   *\n   * @returns A boolean value indicating whether there are unsaved changes in the form or in the bio field.\n   */\n  hasUnsavedChanges() {\n    return this.form.dirty || this.bioChanged;\n  }\n  canDeactivate() {\n    return this.userService.comfirmDiscardUnsavedChanges(this.hasUnsavedChanges.bind(this), this.confirmationService);\n  }\n  static #_ = this.ɵfac = function InfoComponent_Factory(t) {\n    return new (t || InfoComponent)(i0.ɵɵdirectiveInject(i1.ToastService), i0.ɵɵdirectiveInject(i2.GeneralService), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i5.DialogService), i0.ɵɵdirectiveInject(i6.ConfirmationService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: InfoComponent,\n    selectors: [[\"app-info\"]],\n    features: [i0.ɵɵProvidersFeature([DialogService])],\n    decls: 3,\n    vars: 1,\n    consts: [[1, \"btns\"], [1, \"profile-info\", \"md:mt-4\", \"md:mx-4\"], [3, \"formGroup\", 4, \"ngIf\"], [3, \"formGroup\"], [1, \"profile-info-section\", 2, \"padding-top\", \"0\"], [1, \"profile-info-section-title\"], [1, \"text-2xl\", \"text-primary\", \"font-semibold\"], [1, \"p-fluid\"], [1, \"p-formgrid\", \"grid\", \"justify-content-between\"], [1, \"profile-info-section-file\"], [\"postUrl\", \"/Upload/UploadAvatarAsync\", 3, \"onUploadFinished\", \"isProfile\"], [\"class\", \"light-purple-button border-round-3xl font-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"p-formgrid\", \"grid\"], [1, \"field\", \"col-12\", \"md:col\"], [\"htmlFor\", \"fname\", 1, \"font-medium\"], [\"pInputText\", \"\", \"formControlName\", \"fname\", \"type\", \"text\", 1, \"input-blue\", \"gradient\", \"rounded\", 3, \"ngClass\"], [\"class\", \"input-error\", 4, \"ngIf\"], [\"htmlFor\", \"lname\", 1, \"font-medium\"], [\"pInputText\", \"\", \"formControlName\", \"lname\", \"type\", \"text\", 1, \"input-blue\", \"gradient\", \"rounded\", 3, \"ngClass\"], [\"htmlFor\", \"email2\", 1, \"font-medium\"], [\"pInputText\", \"\", \"formControlName\", \"preferredName\", \"type\", \"text\", 1, \"input-blue\", \"gradient\", \"rounded\", 3, \"ngClass\"], [\"class\", \"field col-12 md:col disabled-div\", 4, \"ngIf\"], [\"htmlFor\", \"birth\", 1, \"font-medium\"], [\"dateFormat\", \"dd/mm/yy\", \"styleClass\", \"date-element\", \"inputStyleClass\", \"input-blue gradient rounded\", \"formControlName\", \"birth\", 3, \"onSelect\", \"keepInvalid\", \"firstDayOfWeek\", \"ngClass\"], [\"htmlFor\", \"email\", 1, \"font-medium\"], [\"pInputText\", \"\", \"formControlName\", \"email\", \"type\", \"email\", 1, \"input-blue\", \"gradient\", \"rounded\", 3, \"ngClass\"], [\"class\", \"field col-12 md:col\", 4, \"ngIf\"], [\"htmlFor\", \"residence\", 1, \"font-medium\"], [\"optionLabel\", \"name\", \"filterBy\", \"name\", \"flagFilter\", \"name\", \"templateValue\", \"name\", \"styleClass\", \"dropdown-blue rounded\", 3, \"valueSelected\", \"parentForm\", \"countries\", \"inputName\", \"withFlags\", \"placeholder\", \"selectedItemValue\"], [1, \"field\", \"col-12\", \"md:col\", \"flex\", \"flex-column\"], [\"htmlFor\", \"skype\", 1, \"font-medium\"], [1, \"inline-flex\"], [1, \"\"], [\"optionLabel\", \"code\", \"filterBy\", \"code\", \"flagFilter\", \"code\", \"templateValue\", \"code\", \"styleClass\", \"dropdown-blue phone-code-input rounded\", 3, \"valueSelected\", \"parentForm\", \"countries\", \"filter\", \"inputName\", \"placeholder\", \"showTextAfterImage\", \"withFlags\", \"selectedItemValue\"], [\"type\", \"text\", \"placeholder\", \"Phone Number\", \"pInputText\", \"\", \"formControlName\", \"phone\", 1, \"input-blue\", \"gradient\", \"rounded\", \"no-radius-left\"], [1, \"field\", \"col-12\", \"md:col-6\"], [\"htmlFor\", \"timeZone\", 1, \"font-medium\"], [\"autocomplete\", \"autocomplete_off_hack_xfr4!k\", \"optionLabel\", \"text\", \"filterBy\", \"text\", \"placeholder\", \"Select timezone\", \"formControlName\", \"timeZone\", \"styleClass\", \"dropdown-blue rounded\", 3, \"onChange\", \"options\", \"filter\", \"showClear\"], [\"pTemplate\", \"item\"], [1, \"field\", \"col\", \"md:col-6\"], [\"pInputText\", \"\", \"formControlName\", \"skype\", \"type\", \"email\", 1, \"input-blue\", \"gradient\", \"rounded\"], [\"disableOnClick\", \"\", \"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"CONFIRM\", \"icon\", \"pi pi-check-circle\", \"iconPos\", \"right\", 1, \"p-button-lg\", \"p-button-raised\", \"p-button-rounded\", \"blue-gradient-btn\", \"mt-3\", 3, \"click\", \"disableTime\"], [1, \"light-purple-button\", \"border-round-3xl\", \"font-sm\", 3, \"click\"], [1, \"text-primary\", \"px-3\", \"flex\", \"justify-content-center\", \"align-items-center\"], [\"src\", \"/assets/images/dashboard/calendar/pencil.svg\", \"height\", \"10\", 1, \"ml-1\", \"w-1rem\"], [1, \"input-error\"], [1, \"field\", \"col-12\", \"md:col\", \"disabled-div\"], [\"pInputText\", \"\", \"formControlName\", \"personalEmail\", \"type\", \"email\", 1, \"input-blue\", \"gradient\", \"rounded\", 3, \"ngClass\"], [\"htmlFor\", \"origin\", 1, \"font-medium\"], [1, \"country-item\"], [1, \"country-name\"]],\n    template: function InfoComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"div\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1);\n        i0.ɵɵtemplate(2, InfoComponent_form_2_Template, 67, 47, \"form\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.generalService.isNullishObject(ctx.loggedInUser));\n      }\n    },\n    dependencies: [i7.NgClass, i7.NgIf, i8.ɵNgNoValidate, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgControlStatusGroup, i8.FormGroupDirective, i8.FormControlName, i9.Dropdown, i6.PrimeTemplate, i10.UploadFilesComponent, i11.PrimeInputDropdownComponent, i12.ButtonDirective, i13.DisableOnClickDirective, i14.InputText, i15.Calendar, i16.Ripple],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.modal[_ngcontent-%COMP%] {\\n  min-width: 50vw;\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown:not(.p-disabled):hover {\\n  border: 2px solid rgb(228, 185, 84);\\n}\\n\\n  .no-label > .p-button-label {\\n  display: none;\\n}\\n\\ninput[type=file][_ngcontent-%COMP%] {\\n  visibility: hidden;\\n  width: 10px;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%] {\\n  position: relative;\\n  padding: 10px;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%]::-webkit-calendar-picker-indicator {\\n  color: transparent;\\n  background: none;\\n  z-index: 1;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%]:before {\\n  color: transparent;\\n  background: none;\\n  display: block;\\n  font-family: \\\"FontAwesome\\\";\\n  content: \\\"\\\\f073\\\";\\n  \\n\\n  width: 20px;\\n  height: 25px;\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  color: #999;\\n}\\n\\n.image-preview[_ngcontent-%COMP%] {\\n  height: 10rem;\\n  margin: 1rem 0;\\n  border-radius: 50%;\\n  border: 3px solid var(--main-color);\\n}\\n\\n.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 100%;\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-header {\\n  padding: clamp(0.38rem, 0.47vw + 0.28rem, 0.75rem);\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-items .p-dropdown-item {\\n  margin-bottom: clamp(0.38rem, 0.47vw + 0.28rem, 0.75rem);\\n}\\n\\n.country-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: clamp(0.75rem, 0.31vw + 0.69rem, 1rem);\\n  font-size: clamp(0.88rem, 0.16vw + 0.84rem, 1rem);\\n}\\n.country-item[_ngcontent-%COMP%]   .country-name[_ngcontent-%COMP%] {\\n  white-space: pre-line; \\n\\n}\\n.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  \\n\\n\\n\\n\\n\\n  \\n\\n  display: inline-block;\\n  vertical-align: middle;\\n}\\n.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  clip-path: circle();\\n}\\n.country-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 32px;\\n  margin-right: 0.5rem;\\n}\\n.country-item[_ngcontent-%COMP%]   img.smaller[_ngcontent-%COMP%] {\\n  width: 16px;\\n}\\n\\n.info-element[_ngcontent-%COMP%] {\\n  padding: 10px 0;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["UntypedFormControl", "UntypedFormGroup", "Validators", "SubSink", "DialogService", "EditBioDialogComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "InfoComponent_form_2_div_9_Template_div_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "openBioDialog", "ɵɵtext", "ɵɵelement", "ɵɵelementEnd", "ɵɵtemplate", "InfoComponent_form_2_div_30_div_4_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "form", "controls", "personalEmail", "invalid", "dirty", "ifFieldValid", "InfoComponent_form_2_div_41_Template_app_prime_input_dropdown_valueSelected_3_listener", "$event", "_r4", "onOriginChange", "countries", "selected<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵɵtextInterpolate", "timezone_r5", "text", "InfoComponent_form_2_Template_app_upload_files_onUploadFinished_8_listener", "_r1", "uploadFinished", "InfoComponent_form_2_div_9_Template", "InfoComponent_form_2_div_18_Template", "InfoComponent_form_2_div_23_Template", "InfoComponent_form_2_div_28_Template", "InfoComponent_form_2_div_30_Template", "InfoComponent_form_2_Template_p_calendar_onSelect_34_listener", "onBirthDateSelected", "InfoComponent_form_2_div_39_Template", "InfoComponent_form_2_div_41_Template", "InfoComponent_form_2_Template_app_prime_input_dropdown_valueSelected_45_listener", "onResidenceChange", "InfoComponent_form_2_Template_app_prime_input_dropdown_valueSelected_51_listener", "onPhoneCodeChange", "InfoComponent_form_2_Template_p_dropdown_onChange_57_listener", "onTimeZoneChange", "InfoComponent_form_2_ng_template_58_Template", "InfoComponent_form_2_div_59_Template", "InfoComponent_form_2_div_64_Template", "InfoComponent_form_2_Template_button_click_66_listener", "onSubmit", "authService", "<PERSON><PERSON><PERSON>er", "fname", "lname", "preferredName", "userRole", "birth", "ɵɵtextInterpolate1", "email", "selectedResidenceCountry", "phoneCodes", "selectedPhoneCode", "timezones", "isTablet", "InfoComponent", "constructor", "toast", "generalService", "userService", "dialogService", "confirmationService", "subs", "isLoading", "getCountries", "getTimezones", "getPhoneCodes", "selectedTimezone", "imageChangedEvent", "croppedImage", "tryToSave", "loggedInUser", "edit", "formattedBirthDate", "refs", "bioChanged", "ngOnInit", "getUserRole", "toLowerCase", "isUserInfoFullfileld", "add", "getUserById", "getLoggedInUser", "aspUserId", "subscribe", "res", "loadInitialProfileInfo", "console", "log", "find", "el", "utc", "includes", "timeZone", "deviceKind", "is1024", "obj", "name", "residence", "origin", "filter", "code", "extractCountryPrefix", "phone", "getDefaultPhoneCode", "convertToDDMMYYYY", "bdate", "firstName", "validators", "required", "lastName", "phoneCode", "extractPhoneNumber", "skype", "introduce", "subscribeToBioChanges", "addControl", "removeControl", "ngOnDestroy", "unsubscribe", "editMode", "cancelEdit", "invalidInputs", "controlName", "hasOwnProperty", "control", "detailMessage", "slice", "valid", "setShowToastmessage", "severity", "summary", "detail", "setUpdateProfile", "fileChangeEvent", "event", "document", "getElementById", "style", "borderColor", "value", "iso", "filePath", "photo", "db<PERSON><PERSON>", "convertDateStringToIsoFormatWithZeroTime", "dateOfBirth", "updateProfileAndAPI", "updateUserProfile", "localStorage", "setItem", "JSON", "stringify", "updateAvatarUrl", "avatarUrl", "updateUserListener", "mark<PERSON><PERSON>ristine", "field", "get", "touched", "getUserPhoto", "d", "Date", "parse", "toggleEdit", "findCountryImage", "dialogRef", "open", "header", "width", "styleClass", "showHeader", "dismissableMask", "modal", "contentStyle", "baseZIndex", "maskStyleClass", "data", "dialogData", "push", "onClose", "patchValue", "updateValueAndValidity", "valueChanges", "hasUnsavedChanges", "canDeactivate", "comfirmDiscardUnsavedChanges", "bind", "_", "ɵɵdirectiveInject", "i1", "ToastService", "i2", "GeneralService", "i3", "UserService", "i4", "AuthService", "i5", "i6", "ConfirmationService", "_2", "selectors", "features", "ɵɵProvidersFeature", "decls", "vars", "consts", "template", "InfoComponent_Template", "rf", "ctx", "InfoComponent_form_2_Template", "isNullishObject"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\user-profile\\components\\info\\info.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\user-profile\\components\\info\\info.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { Country, Timezone } from 'src/app/core/models/general.model';\r\nimport { SubSink } from 'subsink';\r\nimport { UserService } from 'src/app/core/services/user.service';\r\nimport { User } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { environment } from 'src/environments/environment';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';\r\nimport { EditBioDialogComponent } from '../edit-bio-dialog/edit-bio-dialog.component';\r\n\r\nimport { Observable } from 'rxjs';\r\nimport { ConfirmationService } from 'primeng/api';\r\n\r\n@Component({\r\n  selector: 'app-info',\r\n  templateUrl: './info.component.html',\r\n  styleUrls: ['./info.component.scss'],\r\n  providers: [DialogService],\r\n})\r\nexport class InfoComponent implements OnInit {\r\n  private subs = new SubSink();\r\n  public form: UntypedFormGroup = new UntypedFormGroup({});\r\n  public isLoading: boolean = false;\r\n  public countries: Country[] = this.generalService.getCountries();\r\n  public selectedOriginCountry: Country | undefined = {} as Country | undefined;\r\n  public selectedResidenceCountry: Country | undefined = {} as Country | undefined;\r\n  public selectedPhoneCode: any | undefined = {} as any | undefined;\r\n  public timezones: Timezone[] = this.generalService.getTimezones();\r\n  public phoneCodes: any[] = this.generalService.getPhoneCodes();\r\n  public selectedTimezone: string = \"\";\r\n  public imageChangedEvent: any = '';\r\n  public croppedImage: any = '';\r\n  public tryToSave: boolean = false;\r\n  public isTablet: boolean = false;\r\n  public loggedInUser: User = {} as User\r\n  public edit: boolean = false;\r\n  public formattedBirthDate: string = \"\";\r\n  public userRole = 'teacher';\r\n  refs: DynamicDialogRef[] = [];\r\n  bioChanged = false;\r\n\r\n  constructor(\r\n    private toast: ToastService,\r\n    public generalService: GeneralService,\r\n    private userService: UserService,\r\n    public authService: AuthService,\r\n    public dialogService: DialogService,\r\n    private confirmationService: ConfirmationService,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.userRole = this.authService.getUserRole().toLowerCase();\r\n\r\n    this.edit = this.authService.isUserInfoFullfileld() ? false: true\r\n    this.subs.add(this.userService.getUserById(this.authService.getLoggedInUser().aspUserId!).subscribe((res: User) => {\r\n      this.loggedInUser = res;\r\n      this.loadInitialProfileInfo();\r\n    }));\r\n    console.log(this.timezones.find(el => el.utc.includes(this.loggedInUser.timeZone!)));\r\n    this.subs.add(this.generalService.deviceKind.subscribe(res => {\r\n      this.isTablet = res.is1024;\r\n    }));\r\n\r\n    console.log(this.loggedInUser);\r\n\r\n    // if (this.userRole === 'teacher') {\r\n    //   this.form.addControl('introduce', new UntypedFormControl(this.loggedInUser.introduce));\r\n    // }\r\n  }\r\n\r\n  loadInitialProfileInfo() {\r\n    this.selectedResidenceCountry = this.countries.find(obj => obj.name === this.loggedInUser.residence);\r\n    this.selectedOriginCountry = this.countries.find(obj => obj.name === this.loggedInUser.origin);\r\n\r\n    // TODO make dynamic when API get changes\r\n    this.selectedPhoneCode = this.phoneCodes.filter(el => el.code === this.generalService.extractCountryPrefix(this.loggedInUser.phone))[0] || this.generalService.getDefaultPhoneCode();\r\n    this.formattedBirthDate = this.generalService.convertToDDMMYYYY(this.loggedInUser.bdate!);\r\n    this.form = new UntypedFormGroup({\r\n    // image: new UntypedFormControl(null, {\r\n    //   // validators: [Validators.required],\r\n    //   // asyncValidators: [mimeType]\r\n    // }),\r\n    fname: new UntypedFormControl(this.loggedInUser.firstName, {\r\n      validators: [Validators.required]\r\n    }),\r\n    lname: new UntypedFormControl(this.loggedInUser.lastName, {\r\n      validators: [Validators.required]\r\n    }),\r\n    preferredName: new UntypedFormControl(this.loggedInUser.firstName, {\r\n      validators: []\r\n    }),\r\n    birth: new UntypedFormControl(this.formattedBirthDate, {\r\n      validators: [Validators.required]\r\n    }),\r\n    phoneCode: new UntypedFormControl(this.selectedPhoneCode, {\r\n      validators: [Validators.required]\r\n    }),\r\n    phone: new UntypedFormControl(this.generalService.extractPhoneNumber(this.loggedInUser.phone), {\r\n      validators: []\r\n    }),\r\n    skype: new UntypedFormControl(this.loggedInUser.skype, {\r\n      validators: [Validators.required]\r\n    }),\r\n    personalEmail: new UntypedFormControl(this.loggedInUser.email, {\r\n      validators: []\r\n    }),\r\n    email: new UntypedFormControl(this.loggedInUser.email, {\r\n      validators: [Validators.required]\r\n    }),\r\n    residence: new UntypedFormControl(this.selectedResidenceCountry, {\r\n      validators: [Validators.required]\r\n    }),\r\n    timeZone: new UntypedFormControl(this.timezones.find(el => el.utc.includes(this.loggedInUser.timeZone!)), {\r\n      validators: [Validators.required]\r\n    }),\r\n    introduce: new UntypedFormControl(this.loggedInUser.introduce, {\r\n      validators: []\r\n    }),\r\n\r\n  });\r\n\r\n  this.subscribeToBioChanges();\r\n\r\n  if (this.userRole === 'student') {\r\n    this.form.addControl('origin', new UntypedFormControl(this.countries.filter(el => el.name === this.loggedInUser.origin)[0], Validators.required));\r\n    this.form.removeControl('personalEmail'); \r\n  }\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  editMode() {\r\n    this.edit = true;\r\n  }\r\n\r\n  cancelEdit() {\r\n    this.edit = false;\r\n  }\r\n\r\n  onSubmit() {\r\n      this.tryToSave = true;\r\n\r\n      let invalidInputs = '';\r\n      for (const controlName in this.form.controls) {\r\n        if (this.form.controls.hasOwnProperty(controlName)) {\r\n          const control = this.form.controls[controlName];\r\n          if (control.invalid) {\r\n            invalidInputs += `${controlName}, `;\r\n          }\r\n        }\r\n      }\r\n  \r\n      let detailMessage = 'Please enter all required fields to continue.';\r\n      if (invalidInputs !== '') {\r\n        invalidInputs = invalidInputs.slice(0, -2); // Remove the trailing comma and space\r\n  \r\n        if (invalidInputs !== '') {\r\n          detailMessage += `Invalid input for the following fields: ${invalidInputs}`;\r\n        }\r\n      }\r\n\r\n      if (!this.form.valid) {\r\n        this.toast.setShowToastmessage({\r\n          severity: 'warn',\r\n          summary: '',\r\n          detail: detailMessage\r\n        });\r\n        return;\r\n      }\r\n      this.userService.setUpdateProfile(true);\r\n    // view uploadFinished function here\r\n  }\r\n\r\n  fileChangeEvent(event: any): void {\r\n    this.imageChangedEvent = event;\r\n    document.getElementById('cropped-img')!.style.borderColor = \"var(--main-color)\";\r\n  }\r\n\r\n\r\n  onOriginChange(event: any) {\r\n    this.selectedOriginCountry = event.value;\r\n  }\r\n\r\n  onResidenceChange(event: any) {\r\n    console.log(event);\r\n    this.selectedResidenceCountry = event.value;\r\n    this.selectedPhoneCode = this.phoneCodes.filter(el => el.iso === this.selectedResidenceCountry!.code)[0] || this.generalService.getDefaultPhoneCode();\r\n  }\r\n\r\n  onTimeZoneChange(event: any) {\r\n    this.selectedTimezone = event.value.text\r\n  }\r\n\r\n  onPhoneCodeChange(event: any) {\r\n    this.selectedPhoneCode = event.value\r\n  }\r\n\r\n  uploadFinished(event: any) {\r\n    if (event.filePath !== '')\r\n      this.loggedInUser.photo = event.filePath.dbPath\r\n    let value = this.form.value;\r\n    this.loggedInUser.personalEmail = value.personalEmail\r\n    this.loggedInUser.firstName = value.fname\r\n    this.loggedInUser.lastName = value.lname\r\n    this.loggedInUser.bdate = this.generalService.convertDateStringToIsoFormatWithZeroTime(this.formattedBirthDate)\r\n    this.loggedInUser.dateOfBirth = this.generalService.convertDateStringToIsoFormatWithZeroTime(this.formattedBirthDate)\r\n    this.loggedInUser.phone =  this.selectedPhoneCode.code + ' ' + value.phone\r\n    this.loggedInUser.skype = value.skype\r\n    this.loggedInUser.origin = value.origin?.name\r\n    this.loggedInUser.timeZone = value.timeZone?.utc[0]\r\n    this.loggedInUser.residence = this.selectedResidenceCountry?.name\r\n    this.loggedInUser.introduce = value.introduce;\r\n    this.updateProfileAndAPI();\r\n  }\r\n\r\n  updateProfileAndAPI() {\r\n    this.form.value.phoneCode = this.form.value.phoneCode.code;\r\n    console.log(this.form.value);\r\n    this.subs.add(this.userService.updateUserProfile(this.loggedInUser)\r\n      .subscribe(res => {\r\n        localStorage.setItem(\"user\", JSON.stringify(this.loggedInUser));\r\n        this.userService.updateAvatarUrl(this.loggedInUser.avatarUrl!);\r\n        this.userService.updateUserListener();\r\n        this.toast.setShowToastmessage({\r\n          severity: 'success',\r\n          summary: '',\r\n          detail: 'Changes were saved successfully!'\r\n        });\r\n        this.form.markAsPristine();\r\n        // this.toastr.success(\"Changes were saved successfully!\")\r\n        // this.edit = false;\r\n      }));\r\n\r\n  }\r\n\r\n  ifFieldValid(field: string) {\r\n    return ((this.form.get(field)?.invalid && this.form.get(field)?.touched) || (this.tryToSave && this.form.get(field)?.invalid))\r\n  }\r\n\r\n  getUserPhoto() {\r\n    return this.userService.getUserPhoto(this.loggedInUser);\r\n  }\r\n\r\n  onBirthDateSelected(event: any) {\r\n    console.log(event);\r\n    let d = new Date(Date.parse(event));\r\n    this.formattedBirthDate = this.generalService.convertToDDMMYYYY(event);\r\n  }\r\n\r\n  toggleEdit() {\r\n    this.edit = !this.edit;\r\n  }\r\n\r\n  findCountryImage(phoneCode: string) {\r\n    return this.generalService.findCountryImage(phoneCode);\r\n  }\r\n\r\n  openBioDialog() {\r\n    const dialogRef = this.dialogService.open(EditBioDialogComponent, {\r\n      header: 'Choose a Product',\r\n      width: '26rem',\r\n      styleClass: 'border-round-xl',\r\n      showHeader: false,\r\n      dismissableMask: true,\r\n      modal: true,\r\n      contentStyle: {\"max-height\": \"500px\", \"overflow\": \"auto\", \"border-radius\": \"12px\", \"padding\": \"0px\"},\r\n      baseZIndex: 10000,\r\n      maskStyleClass: 'transparent-mask',\r\n      data: { dialogData: {introduce: this.loggedInUser.introduce} }\r\n    });\r\n    this.refs.push(dialogRef);\r\n    dialogRef.onClose.subscribe((data) => {\r\n      if (data) {\r\n        console.log(data);\r\n        this.form.get('introduce')?.patchValue(data.data);\r\n        this.form.get('introduce')?.updateValueAndValidity(data.data);\r\n        this.loggedInUser.introduce = data.data;\r\n      }\r\n    });\r\n  }\r\n\r\n  private subscribeToBioChanges(): void {\r\n    this.subs.add(this.form.get('introduce')?.valueChanges.subscribe((value) => {\r\n      if (value !== this.loggedInUser.introduce) {\r\n        this.bioChanged = true;\r\n      }\r\n    }));\r\n  }\r\n\r\n  /**\r\n   * Checks if there are any unsaved changes in the form or in the bio field.\r\n   * \r\n   * @returns A boolean value indicating whether there are unsaved changes in the form or in the bio field.\r\n   */\r\n  private hasUnsavedChanges(): boolean {\r\n    return this.form.dirty || this.bioChanged;\r\n  }\r\n\r\n  canDeactivate(): Observable<boolean> | boolean {\r\n    return this.userService.comfirmDiscardUnsavedChanges(this.hasUnsavedChanges.bind(this), this.confirmationService);\r\n  }\r\n\r\n}\r\n", "<div class=\"btns\">\r\n\r\n    <!-- <div *ngIf=\"edit\" (click)=\"cancelEdit()\" class=\"main-color-button\">\r\n        <span>\r\n            Cancel\r\n        </span>\r\n    </div> -->\r\n</div>\r\n<div class=\"profile-info md:mt-4 md:mx-4\">\r\n\r\n    <form [formGroup]=\"form\" *ngIf=\"!isLoading && !generalService.isNullishObject(loggedInUser)\">\r\n        <div class=\"profile-info-section\" style=\"padding-top:0\">\r\n            <div class=\"profile-info-section-title\">\r\n\r\n            </div>\r\n            <h5 class=\"text-2xl text-primary font-semibold\">Avatar</h5>\r\n            <div class=\"p-fluid\">\r\n                <div class=\"p-formgrid grid justify-content-between\">\r\n                    <div class=\"profile-info-section-file\">\r\n                        <app-upload-files postUrl=\"/Upload/UploadAvatarAsync\"\r\n                            (onUploadFinished)=\"uploadFinished($event)\" [isProfile]=\"true\">\r\n                        </app-upload-files>\r\n                    </div>\r\n\r\n\r\n                    <!-- <div *ngIf=\"!edit\" class=\"flex sm:flex-row align-items-center\">\r\n                        <img [src]=\"getUserPhoto()\" style=\"width:150px; border-radius:50%\">\r\n                        <button pButton type=\"button\" label=\"Edit\" icon=\"pi pi-check-circle\" iconPos=\"right\"\r\n                        [disabled]=\"form.invalid\"\r\n                            class=\"p-button-sm p-button-raised p-button-rounded gradient-input-like-bt\" \r\n                            (click)=\"toggleEdit()\"></button>\r\n                    </div> -->\r\n                \r\n                    <div *ngIf=\"authService.isTeacher\" class=\"light-purple-button border-round-3xl font-sm\" (click)=\"openBioDialog()\">\r\n                        <span class=\"text-primary px-3 flex justify-content-center align-items-center\">\r\n                           Add Bio <img class=\"ml-1 w-1rem\" src=\"/assets/images/dashboard/calendar/pencil.svg\" height=\"10\" />\r\n                        </span>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <h5 class=\"text-2xl text-primary font-semibold\">Personal Info</h5>\r\n            <div class=\"p-fluid\">\r\n                <div class=\"p-formgrid grid\">\r\n                    <div class=\"field col-12 md:col\">\r\n                        <label class=\"font-medium\" htmlFor=\"fname\">* First Name</label>\r\n                        <input class=\"input-blue gradient rounded\" pInputText formControlName=\"fname\" type=\"text\" \r\n                        [ngClass]=\"{'ng-invalid ng-dirty': form.controls.fname.invalid && form.controls.fname.dirty}\"\r\n                          />\r\n                        <div *ngIf=\"ifFieldValid('fname')\" class=\"input-error\">* First Name is required</div>\r\n                    </div>\r\n                    <div class=\"field col-12 md:col\">\r\n                        <label class=\"font-medium\" htmlFor=\"lname\">* Last Name</label>\r\n                        <input class=\"input-blue gradient rounded\" pInputText formControlName=\"lname\" type=\"text\"\r\n                        [ngClass]=\"{'ng-invalid ng-dirty': form.controls.lname.invalid && form.controls.lname.dirty}\">\r\n                        <div *ngIf=\"ifFieldValid('lname')\" class=\"input-error\">* Last Name is required</div>\r\n                    </div>\r\n                    <div class=\"field col-12 md:col\">\r\n                        <label class=\"font-medium\" htmlFor=\"email2\">Preferred Name</label>\r\n                        <input class=\"input-blue gradient rounded\" pInputText formControlName=\"preferredName\" type=\"text\"\r\n                        [ngClass]=\"{'ng-invalid ng-dirty': form.controls.preferredName.invalid && form.controls.preferredName.dirty}\">\r\n                        <div *ngIf=\"ifFieldValid('preferredName')\" class=\"input-error\">* preferred Name is required</div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"p-formgrid grid\">\r\n                    <div class=\"field col-12 md:col disabled-div\" *ngIf=\"userRole === 'teacher'\">\r\n                        <label class=\"font-medium\" htmlFor=\"email\">Personal Email</label>\r\n                        <input class=\"input-blue gradient rounded\" pInputText formControlName=\"personalEmail\" type=\"email\"\r\n                        [ngClass]=\"{'ng-invalid ng-dirty': form.controls.personalEmail.invalid && form.controls.personalEmail.dirty}\">\r\n                        <div *ngIf=\"ifFieldValid('personalEmail')\" class=\"input-error\">* Personal Email is required</div>\r\n                    </div>\r\n\r\n                    <div class=\"field col-12 md:col\">\r\n                        <label class=\"font-medium\" htmlFor=\"birth\">* Date of Birth</label>\r\n                        \r\n                    <p-calendar \r\n                    dateFormat=\"dd/mm/yy\"\r\n                    styleClass=\"date-element\" \r\n                    inputStyleClass=\"input-blue gradient rounded\"\r\n                    [keepInvalid]=\"true\"\r\n                    formControlName=\"birth\"\r\n                    [firstDayOfWeek]=\"1\"\r\n                    (onSelect)=\"onBirthDateSelected($event)\"\r\n                    [ngClass]=\"{'ng-invalid ng-dirty': form.controls.birth.invalid && form.controls.birth.dirty}\"\r\n                    ></p-calendar>\r\n                        <!-- <input id=\"birth\" class=\"input-blue rounded\" pInputText formControlName=\"birth\" type=\"date\"> -->\r\n                    </div>\r\n                \r\n                <div class=\"field col-12 md:col\">\r\n                    <label class=\"font-medium\" htmlFor=\"email\">* {{userRole === 'teacher' ? 'MLT' : ''}} Email</label>\r\n                    <input class=\"input-blue gradient rounded\" pInputText formControlName=\"email\" type=\"email\"\r\n                    [ngClass]=\"{'ng-invalid ng-dirty': form.controls.email.invalid && form.controls.email.dirty}\">\r\n                    <div *ngIf=\"ifFieldValid('email')\" class=\"input-error\">* Email is required</div>\r\n                </div>\r\n                </div>\r\n                <div class=\"p-formgrid grid\">\r\n                    <div class=\"field col-12 md:col\" *ngIf=\"userRole === 'student'\">\r\n                        <label class=\"font-medium\" htmlFor=\"origin\">Country of Origin</label>\r\n\r\n                        \r\n                        <app-prime-input-dropdown \r\n                        [parentForm]=\"form\"\r\n                        [countries]=\"countries\"\r\n                        optionLabel=\"name\"\r\n                        filterBy=\"name\"\r\n                        [inputName]=\"'origin'\"\r\n                        [withFlags]=\"true\"\r\n                        flagFilter=\"name\"\r\n                        [placeholder]=\"'* Country of origin'\"\r\n                        [selectedItemValue]=\"selectedOriginCountry\"\r\n                        templateValue=\"name\"\r\n                        styleClass=\"dropdown-blue rounded\"\r\n                        (valueSelected)=\"onOriginChange($event)\">\r\n                        </app-prime-input-dropdown>\r\n\r\n                    </div>\r\n                    <div class=\"field col-12 md:col\">\r\n                        <label class=\"font-medium\" htmlFor=\"residence\">* Country of Residence</label>\r\n\r\n                        \r\n                        <app-prime-input-dropdown \r\n                        [parentForm]=\"form\"\r\n                        [countries]=\"countries\"\r\n                        optionLabel=\"name\"\r\n                        filterBy=\"name\"\r\n                        [inputName]=\"'residence'\"\r\n                        [withFlags]=\"true\"\r\n                        flagFilter=\"name\"\r\n                        [placeholder]=\"'* Country of Residence'\"\r\n                        [selectedItemValue]=\"selectedResidenceCountry\"\r\n                        templateValue=\"name\"\r\n                        styleClass=\"dropdown-blue rounded\"\r\n                        (valueSelected)=\"onResidenceChange($event)\">\r\n                        </app-prime-input-dropdown>\r\n\r\n\r\n                    </div>\r\n                    \r\n                    <div class=\"field col-12 md:col flex flex-column\">\r\n                        <label class=\"font-medium\" htmlFor=\"skype\">Phone number</label>\r\n                        <div class=\" inline-flex\">\r\n                            <div class=\"\">\r\n                                \r\n                        <app-prime-input-dropdown \r\n                        optionLabel=\"code\"\r\n                        [parentForm]=\"form\"\r\n                        [countries]=\"phoneCodes\"\r\n                        filterBy=\"code\"\r\n                        [filter]=\"true\"\r\n                        [inputName]=\"'phoneCode'\"\r\n                        [placeholder]=\"''\"\r\n                        [showTextAfterImage]=\"false\"\r\n                        [withFlags]=\"true\"\r\n                        flagFilter=\"code\"\r\n                        [selectedItemValue]=\"selectedPhoneCode\"\r\n                        templateValue=\"code\"\r\n                        styleClass=\"dropdown-blue phone-code-input rounded\"\r\n                        (valueSelected)=\"onPhoneCodeChange($event)\">\r\n                        </app-prime-input-dropdown>\r\n                        </div>\r\n                        <input type=\"text\" class=\"input-blue gradient rounded no-radius-left\"\r\n                                    placeholder=\"Phone Number\"\r\n                                        pInputText formControlName=\"phone\">\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"p-formgrid grid\">\r\n                    <div class=\"field col-12 md:col-6\">\r\n                        <label class=\"font-medium\" htmlFor=\"timeZone\">* Timezone</label>\r\n                        <p-dropdown autocomplete=\"autocomplete_off_hack_xfr4!k\" [options]=\"timezones\"\r\n                            (onChange)=\"onTimeZoneChange($event)\" optionLabel=\"text\" [filter]=\"!isTablet\"\r\n                            filterBy=\"text\" [showClear]=\"true\" placeholder=\"Select timezone\" formControlName=\"timeZone\"\r\n                            styleClass=\"dropdown-blue rounded\">\r\n                            <ng-template let-timezone pTemplate=\"item\">\r\n                                <div class=\"country-item\">\r\n                                    <div class=\"country-name\">{{timezone.text}}</div>\r\n                                </div>\r\n                            </ng-template>\r\n                        </p-dropdown>\r\n                        <div *ngIf=\"ifFieldValid('timeZone')\" class=\"input-error\">* Timezone is required</div>\r\n                    </div>\r\n                    <div class=\"field col md:col-6\">\r\n                        <label class=\"font-medium\" htmlFor=\"skype\">Skype</label>\r\n                        <input class=\"input-blue gradient rounded\" pInputText formControlName=\"skype\" type=\"email\">\r\n                        <div *ngIf=\"ifFieldValid('skype')\" class=\"input-error\">* Skype is required</div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"p-formgrid grid\">\r\n                </div>\r\n            </div>\r\n            <button disableOnClick [disableTime]=\"1000\" pButton pRipple type=\"button\" label=\"CONFIRM\" icon=\"pi pi-check-circle\" iconPos=\"right\"\r\n                class=\"p-button-lg p-button-raised p-button-rounded blue-gradient-btn mt-3\" (click)=\"onSubmit()\"></button>\r\n\r\n        </div>\r\n    </form>\r\n</div>"], "mappings": "AACA,SAASA,kBAAkB,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,gBAAgB;AAGjF,SAASC,OAAO,QAAQ,SAAS;AAMjC,SAASC,aAAa,QAA0B,uBAAuB;AACvE,SAASC,sBAAsB,QAAQ,8CAA8C;;;;;;;;;;;;;;;;;;;;;;;;ICsBjEC,EAAA,CAAAC,cAAA,cAAkH;IAA1BD,EAAA,CAAAE,UAAA,mBAAAC,yDAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,aAAA,EAAe;IAAA,EAAC;IAC7GT,EAAA,CAAAC,cAAA,eAA+E;IAC5ED,EAAA,CAAAU,MAAA,gBAAQ;IAAAV,EAAA,CAAAW,SAAA,cAA0F;IAEzGX,EADI,CAAAY,YAAA,EAAO,EACL;;;;;IAWFZ,EAAA,CAAAC,cAAA,cAAuD;IAAAD,EAAA,CAAAU,MAAA,+BAAwB;IAAAV,EAAA,CAAAY,YAAA,EAAM;;;;;IAMrFZ,EAAA,CAAAC,cAAA,cAAuD;IAAAD,EAAA,CAAAU,MAAA,8BAAuB;IAAAV,EAAA,CAAAY,YAAA,EAAM;;;;;IAMpFZ,EAAA,CAAAC,cAAA,cAA+D;IAAAD,EAAA,CAAAU,MAAA,mCAA4B;IAAAV,EAAA,CAAAY,YAAA,EAAM;;;;;IAQjGZ,EAAA,CAAAC,cAAA,cAA+D;IAAAD,EAAA,CAAAU,MAAA,mCAA4B;IAAAV,EAAA,CAAAY,YAAA,EAAM;;;;;IAHjGZ,EADJ,CAAAC,cAAA,cAA6E,gBAC9B;IAAAD,EAAA,CAAAU,MAAA,qBAAc;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IACjEZ,EAAA,CAAAW,SAAA,gBAC8G;IAC9GX,EAAA,CAAAa,UAAA,IAAAC,0CAAA,kBAA+D;IACnEd,EAAA,CAAAY,YAAA,EAAM;;;;IAFFZ,EAAA,CAAAe,SAAA,GAA6G;IAA7Gf,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAiB,eAAA,IAAAC,GAAA,EAAAZ,MAAA,CAAAa,IAAA,CAAAC,QAAA,CAAAC,aAAA,CAAAC,OAAA,IAAAhB,MAAA,CAAAa,IAAA,CAAAC,QAAA,CAAAC,aAAA,CAAAE,KAAA,EAA6G;IACvGvB,EAAA,CAAAe,SAAA,EAAmC;IAAnCf,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAkB,YAAA,kBAAmC;;;;;IAuB7CxB,EAAA,CAAAC,cAAA,cAAuD;IAAAD,EAAA,CAAAU,MAAA,0BAAmB;IAAAV,EAAA,CAAAY,YAAA,EAAM;;;;;;IAK5EZ,EADJ,CAAAC,cAAA,cAAgE,gBAChB;IAAAD,EAAA,CAAAU,MAAA,wBAAiB;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IAGrEZ,EAAA,CAAAC,cAAA,mCAYyC;IAAzCD,EAAA,CAAAE,UAAA,2BAAAuB,uFAAAC,MAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,MAAArB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAiBF,MAAA,CAAAsB,cAAA,CAAAF,MAAA,CAAsB;IAAA,EAAC;IAG5C1B,EAFI,CAAAY,YAAA,EAA2B,EAEzB;;;;IAdFZ,EAAA,CAAAe,SAAA,GAAmB;IAQnBf,EARA,CAAAgB,UAAA,eAAAV,MAAA,CAAAa,IAAA,CAAmB,cAAAb,MAAA,CAAAuB,SAAA,CACI,uBAGD,mBACJ,sCAEmB,sBAAAvB,MAAA,CAAAwB,qBAAA,CACM;;;;;IAmE/B9B,EADJ,CAAAC,cAAA,cAA0B,cACI;IAAAD,EAAA,CAAAU,MAAA,GAAiB;IAC/CV,EAD+C,CAAAY,YAAA,EAAM,EAC/C;;;;IADwBZ,EAAA,CAAAe,SAAA,GAAiB;IAAjBf,EAAA,CAAA+B,iBAAA,CAAAC,WAAA,CAAAC,IAAA,CAAiB;;;;;IAIvDjC,EAAA,CAAAC,cAAA,cAA0D;IAAAD,EAAA,CAAAU,MAAA,6BAAsB;IAAAV,EAAA,CAAAY,YAAA,EAAM;;;;;IAKtFZ,EAAA,CAAAC,cAAA,cAAuD;IAAAD,EAAA,CAAAU,MAAA,0BAAmB;IAAAV,EAAA,CAAAY,YAAA,EAAM;;;;;;IA7KhGZ,EADJ,CAAAC,cAAA,cAA6F,aACjC;IACpDD,EAAA,CAAAW,SAAA,aAEM;IACNX,EAAA,CAAAC,cAAA,YAAgD;IAAAD,EAAA,CAAAU,MAAA,aAAM;IAAAV,EAAA,CAAAY,YAAA,EAAK;IAI/CZ,EAHZ,CAAAC,cAAA,aAAqB,aACoC,aACV,2BAEgC;IAA/DD,EAAA,CAAAE,UAAA,8BAAAgC,2EAAAR,MAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAA+B,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAoBF,MAAA,CAAA8B,cAAA,CAAAV,MAAA,CAAsB;IAAA,EAAC;IAEnD1B,EADI,CAAAY,YAAA,EAAmB,EACjB;IAWNZ,EAAA,CAAAa,UAAA,IAAAwB,mCAAA,kBAAkH;IAM1HrC,EADI,CAAAY,YAAA,EAAM,EACJ;IACNZ,EAAA,CAAAC,cAAA,aAAgD;IAAAD,EAAA,CAAAU,MAAA,qBAAa;IAAAV,EAAA,CAAAY,YAAA,EAAK;IAItDZ,EAHZ,CAAAC,cAAA,cAAqB,eACY,eACQ,iBACc;IAAAD,EAAA,CAAAU,MAAA,oBAAY;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IAC/DZ,EAAA,CAAAW,SAAA,iBAEI;IACJX,EAAA,CAAAa,UAAA,KAAAyB,oCAAA,kBAAuD;IAC3DtC,EAAA,CAAAY,YAAA,EAAM;IAEFZ,EADJ,CAAAC,cAAA,eAAiC,iBACc;IAAAD,EAAA,CAAAU,MAAA,mBAAW;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IAC9DZ,EAAA,CAAAW,SAAA,iBAC8F;IAC9FX,EAAA,CAAAa,UAAA,KAAA0B,oCAAA,kBAAuD;IAC3DvC,EAAA,CAAAY,YAAA,EAAM;IAEFZ,EADJ,CAAAC,cAAA,eAAiC,iBACe;IAAAD,EAAA,CAAAU,MAAA,sBAAc;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IAClEZ,EAAA,CAAAW,SAAA,iBAC8G;IAC9GX,EAAA,CAAAa,UAAA,KAAA2B,oCAAA,kBAA+D;IAEvExC,EADI,CAAAY,YAAA,EAAM,EACJ;IACNZ,EAAA,CAAAC,cAAA,eAA6B;IACzBD,EAAA,CAAAa,UAAA,KAAA4B,oCAAA,kBAA6E;IAQzEzC,EADJ,CAAAC,cAAA,eAAiC,iBACc;IAAAD,EAAA,CAAAU,MAAA,uBAAe;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IAEtEZ,EAAA,CAAAC,cAAA,sBASC;IAFDD,EAAA,CAAAE,UAAA,sBAAAwC,8DAAAhB,MAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAA+B,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAYF,MAAA,CAAAqC,mBAAA,CAAAjB,MAAA,CAA2B;IAAA,EAAC;IAIxC1B,EAFC,CAAAY,YAAA,EAAa,EAER;IAGNZ,EADJ,CAAAC,cAAA,eAAiC,iBACc;IAAAD,EAAA,CAAAU,MAAA,IAA+C;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IAClGZ,EAAA,CAAAW,SAAA,iBAC8F;IAC9FX,EAAA,CAAAa,UAAA,KAAA+B,oCAAA,kBAAuD;IAE3D5C,EADA,CAAAY,YAAA,EAAM,EACA;IACNZ,EAAA,CAAAC,cAAA,eAA6B;IACzBD,EAAA,CAAAa,UAAA,KAAAgC,oCAAA,kBAAgE;IAqB5D7C,EADJ,CAAAC,cAAA,eAAiC,iBACkB;IAAAD,EAAA,CAAAU,MAAA,8BAAsB;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IAG7EZ,EAAA,CAAAC,cAAA,oCAY4C;IAA5CD,EAAA,CAAAE,UAAA,2BAAA4C,iFAAApB,MAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAA+B,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAiBF,MAAA,CAAAyC,iBAAA,CAAArB,MAAA,CAAyB;IAAA,EAAC;IAI/C1B,EAHI,CAAAY,YAAA,EAA2B,EAGzB;IAGFZ,EADJ,CAAAC,cAAA,eAAkD,iBACH;IAAAD,EAAA,CAAAU,MAAA,oBAAY;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IAI/DZ,EAHA,CAAAC,cAAA,eAA0B,eACR,oCAgB0B;IAA5CD,EAAA,CAAAE,UAAA,2BAAA8C,iFAAAtB,MAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAA+B,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAiBF,MAAA,CAAA2C,iBAAA,CAAAvB,MAAA,CAAyB;IAAA,EAAC;IAE3C1B,EADA,CAAAY,YAAA,EAA2B,EACrB;IACNZ,EAAA,CAAAW,SAAA,iBAEmD;IAG3DX,EAFQ,CAAAY,YAAA,EAAM,EACJ,EACJ;IAIEZ,EAFR,CAAAC,cAAA,eAA6B,eACU,iBACe;IAAAD,EAAA,CAAAU,MAAA,kBAAU;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IAChEZ,EAAA,CAAAC,cAAA,sBAGuC;IAFnCD,EAAA,CAAAE,UAAA,sBAAAgD,8DAAAxB,MAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAA+B,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAYF,MAAA,CAAA6C,gBAAA,CAAAzB,MAAA,CAAwB;IAAA,EAAC;IAGrC1B,EAAA,CAAAa,UAAA,KAAAuC,4CAAA,0BAA2C;IAK/CpD,EAAA,CAAAY,YAAA,EAAa;IACbZ,EAAA,CAAAa,UAAA,KAAAwC,oCAAA,kBAA0D;IAC9DrD,EAAA,CAAAY,YAAA,EAAM;IAEFZ,EADJ,CAAAC,cAAA,eAAgC,iBACe;IAAAD,EAAA,CAAAU,MAAA,aAAK;IAAAV,EAAA,CAAAY,YAAA,EAAQ;IACxDZ,EAAA,CAAAW,SAAA,iBAA2F;IAC3FX,EAAA,CAAAa,UAAA,KAAAyC,oCAAA,kBAAuD;IAE/DtD,EADI,CAAAY,YAAA,EAAM,EACJ;IAENZ,EAAA,CAAAW,SAAA,eACM;IACVX,EAAA,CAAAY,YAAA,EAAM;IACNZ,EAAA,CAAAC,cAAA,kBACqG;IAArBD,EAAA,CAAAE,UAAA,mBAAAqD,uDAAA;MAAAvD,EAAA,CAAAI,aAAA,CAAA+B,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAkD,QAAA,EAAU;IAAA,EAAC;IAG5GxD,EAH6G,CAAAY,YAAA,EAAS,EAE5G,EACH;;;;IAzLDZ,EAAA,CAAAgB,UAAA,cAAAV,MAAA,CAAAa,IAAA,CAAkB;IAU4CnB,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAgB,UAAA,mBAAkB;IAahEhB,EAAA,CAAAe,SAAA,EAA2B;IAA3Bf,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAmD,WAAA,CAAAC,SAAA,CAA2B;IAa7B1D,EAAA,CAAAe,SAAA,GAA6F;IAA7Ff,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAiB,eAAA,KAAAC,GAAA,EAAAZ,MAAA,CAAAa,IAAA,CAAAC,QAAA,CAAAuC,KAAA,CAAArC,OAAA,IAAAhB,MAAA,CAAAa,IAAA,CAAAC,QAAA,CAAAuC,KAAA,CAAApC,KAAA,EAA6F;IAEvFvB,EAAA,CAAAe,SAAA,EAA2B;IAA3Bf,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAkB,YAAA,UAA2B;IAKjCxB,EAAA,CAAAe,SAAA,GAA6F;IAA7Ff,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAiB,eAAA,KAAAC,GAAA,EAAAZ,MAAA,CAAAa,IAAA,CAAAC,QAAA,CAAAwC,KAAA,CAAAtC,OAAA,IAAAhB,MAAA,CAAAa,IAAA,CAAAC,QAAA,CAAAwC,KAAA,CAAArC,KAAA,EAA6F;IACvFvB,EAAA,CAAAe,SAAA,EAA2B;IAA3Bf,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAkB,YAAA,UAA2B;IAKjCxB,EAAA,CAAAe,SAAA,GAA6G;IAA7Gf,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAiB,eAAA,KAAAC,GAAA,EAAAZ,MAAA,CAAAa,IAAA,CAAAC,QAAA,CAAAyC,aAAA,CAAAvC,OAAA,IAAAhB,MAAA,CAAAa,IAAA,CAAAC,QAAA,CAAAyC,aAAA,CAAAtC,KAAA,EAA6G;IACvGvB,EAAA,CAAAe,SAAA,EAAmC;IAAnCf,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAkB,YAAA,kBAAmC;IAIExB,EAAA,CAAAe,SAAA,GAA4B;IAA5Bf,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAwD,QAAA,eAA4B;IAc3E9D,EAAA,CAAAe,SAAA,GAAoB;IAIpBf,EAJA,CAAAgB,UAAA,qBAAoB,qBAEA,YAAAhB,EAAA,CAAAiB,eAAA,KAAAC,GAAA,EAAAZ,MAAA,CAAAa,IAAA,CAAAC,QAAA,CAAA2C,KAAA,CAAAzC,OAAA,IAAAhB,MAAA,CAAAa,IAAA,CAAAC,QAAA,CAAA2C,KAAA,CAAAxC,KAAA,EAEyE;IAMlDvB,EAAA,CAAAe,SAAA,GAA+C;IAA/Cf,EAAA,CAAAgE,kBAAA,OAAA1D,MAAA,CAAAwD,QAAA,sCAA+C;IAE1F9D,EAAA,CAAAe,SAAA,EAA6F;IAA7Ff,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAiB,eAAA,KAAAC,GAAA,EAAAZ,MAAA,CAAAa,IAAA,CAAAC,QAAA,CAAA6C,KAAA,CAAA3C,OAAA,IAAAhB,MAAA,CAAAa,IAAA,CAAAC,QAAA,CAAA6C,KAAA,CAAA1C,KAAA,EAA6F;IACvFvB,EAAA,CAAAe,SAAA,EAA2B;IAA3Bf,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAkB,YAAA,UAA2B;IAICxB,EAAA,CAAAe,SAAA,GAA4B;IAA5Bf,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAwD,QAAA,eAA4B;IAyB1D9D,EAAA,CAAAe,SAAA,GAAmB;IAQnBf,EARA,CAAAgB,UAAA,eAAAV,MAAA,CAAAa,IAAA,CAAmB,cAAAb,MAAA,CAAAuB,SAAA,CACI,0BAGE,mBACP,yCAEsB,sBAAAvB,MAAA,CAAA4D,wBAAA,CACM;IAgB9ClE,EAAA,CAAAe,SAAA,GAAmB;IASnBf,EATA,CAAAgB,UAAA,eAAAV,MAAA,CAAAa,IAAA,CAAmB,cAAAb,MAAA,CAAA6D,UAAA,CACK,gBAET,0BACU,mBACP,6BACU,mBACV,sBAAA7D,MAAA,CAAA8D,iBAAA,CAEqB;IAgBiBpE,EAAA,CAAAe,SAAA,GAAqB;IAEzDf,EAFoC,CAAAgB,UAAA,YAAAV,MAAA,CAAA+D,SAAA,CAAqB,YAAA/D,MAAA,CAAAgE,QAAA,CACI,mBAC3C;IAQhCtE,EAAA,CAAAe,SAAA,GAA8B;IAA9Bf,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAkB,YAAA,aAA8B;IAK9BxB,EAAA,CAAAe,SAAA,GAA2B;IAA3Bf,EAAA,CAAAgB,UAAA,SAAAV,MAAA,CAAAkB,YAAA,UAA2B;IAOtBxB,EAAA,CAAAe,SAAA,GAAoB;IAApBf,EAAA,CAAAgB,UAAA,qBAAoB;;;ADzKvD,OAAM,MAAOuD,aAAa;EAsBxBC,YACUC,KAAmB,EACpBC,cAA8B,EAC7BC,WAAwB,EACzBlB,WAAwB,EACxBmB,aAA4B,EAC3BC,mBAAwC;IALxC,KAAAJ,KAAK,GAALA,KAAK;IACN,KAAAC,cAAc,GAAdA,cAAc;IACb,KAAAC,WAAW,GAAXA,WAAW;IACZ,KAAAlB,WAAW,GAAXA,WAAW;IACX,KAAAmB,aAAa,GAAbA,aAAa;IACZ,KAAAC,mBAAmB,GAAnBA,mBAAmB;IA3BrB,KAAAC,IAAI,GAAG,IAAIjF,OAAO,EAAE;IACrB,KAAAsB,IAAI,GAAqB,IAAIxB,gBAAgB,CAAC,EAAE,CAAC;IACjD,KAAAoF,SAAS,GAAY,KAAK;IAC1B,KAAAlD,SAAS,GAAc,IAAI,CAAC6C,cAAc,CAACM,YAAY,EAAE;IACzD,KAAAlD,qBAAqB,GAAwB,EAAyB;IACtE,KAAAoC,wBAAwB,GAAwB,EAAyB;IACzE,KAAAE,iBAAiB,GAAoB,EAAqB;IAC1D,KAAAC,SAAS,GAAe,IAAI,CAACK,cAAc,CAACO,YAAY,EAAE;IAC1D,KAAAd,UAAU,GAAU,IAAI,CAACO,cAAc,CAACQ,aAAa,EAAE;IACvD,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAC,iBAAiB,GAAQ,EAAE;IAC3B,KAAAC,YAAY,GAAQ,EAAE;IACtB,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAhB,QAAQ,GAAY,KAAK;IACzB,KAAAiB,YAAY,GAAS,EAAU;IAC/B,KAAAC,IAAI,GAAY,KAAK;IACrB,KAAAC,kBAAkB,GAAW,EAAE;IAC/B,KAAA3B,QAAQ,GAAG,SAAS;IAC3B,KAAA4B,IAAI,GAAuB,EAAE;IAC7B,KAAAC,UAAU,GAAG,KAAK;EASd;EAEJC,QAAQA,CAAA;IACN,IAAI,CAAC9B,QAAQ,GAAG,IAAI,CAACL,WAAW,CAACoC,WAAW,EAAE,CAACC,WAAW,EAAE;IAE5D,IAAI,CAACN,IAAI,GAAG,IAAI,CAAC/B,WAAW,CAACsC,oBAAoB,EAAE,GAAG,KAAK,GAAE,IAAI;IACjE,IAAI,CAACjB,IAAI,CAACkB,GAAG,CAAC,IAAI,CAACrB,WAAW,CAACsB,WAAW,CAAC,IAAI,CAACxC,WAAW,CAACyC,eAAe,EAAE,CAACC,SAAU,CAAC,CAACC,SAAS,CAAEC,GAAS,IAAI;MAChH,IAAI,CAACd,YAAY,GAAGc,GAAG;MACvB,IAAI,CAACC,sBAAsB,EAAE;IAC/B,CAAC,CAAC,CAAC;IACHC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACnC,SAAS,CAACoC,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACC,GAAG,CAACC,QAAQ,CAAC,IAAI,CAACrB,YAAY,CAACsB,QAAS,CAAC,CAAC,CAAC;IACpF,IAAI,CAAC/B,IAAI,CAACkB,GAAG,CAAC,IAAI,CAACtB,cAAc,CAACoC,UAAU,CAACV,SAAS,CAACC,GAAG,IAAG;MAC3D,IAAI,CAAC/B,QAAQ,GAAG+B,GAAG,CAACU,MAAM;IAC5B,CAAC,CAAC,CAAC;IAEHR,OAAO,CAACC,GAAG,CAAC,IAAI,CAACjB,YAAY,CAAC;IAE9B;IACA;IACA;EACF;EAEAe,sBAAsBA,CAAA;IACpB,IAAI,CAACpC,wBAAwB,GAAG,IAAI,CAACrC,SAAS,CAAC4E,IAAI,CAACO,GAAG,IAAIA,GAAG,CAACC,IAAI,KAAK,IAAI,CAAC1B,YAAY,CAAC2B,SAAS,CAAC;IACpG,IAAI,CAACpF,qBAAqB,GAAG,IAAI,CAACD,SAAS,CAAC4E,IAAI,CAACO,GAAG,IAAIA,GAAG,CAACC,IAAI,KAAK,IAAI,CAAC1B,YAAY,CAAC4B,MAAM,CAAC;IAE9F;IACA,IAAI,CAAC/C,iBAAiB,GAAG,IAAI,CAACD,UAAU,CAACiD,MAAM,CAACV,EAAE,IAAIA,EAAE,CAACW,IAAI,KAAK,IAAI,CAAC3C,cAAc,CAAC4C,oBAAoB,CAAC,IAAI,CAAC/B,YAAY,CAACgC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC7C,cAAc,CAAC8C,mBAAmB,EAAE;IACpL,IAAI,CAAC/B,kBAAkB,GAAG,IAAI,CAACf,cAAc,CAAC+C,iBAAiB,CAAC,IAAI,CAAClC,YAAY,CAACmC,KAAM,CAAC;IACzF,IAAI,CAACvG,IAAI,GAAG,IAAIxB,gBAAgB,CAAC;MACjC;MACA;MACA;MACA;MACAgE,KAAK,EAAE,IAAIjE,kBAAkB,CAAC,IAAI,CAAC6F,YAAY,CAACoC,SAAS,EAAE;QACzDC,UAAU,EAAE,CAAChI,UAAU,CAACiI,QAAQ;OACjC,CAAC;MACFjE,KAAK,EAAE,IAAIlE,kBAAkB,CAAC,IAAI,CAAC6F,YAAY,CAACuC,QAAQ,EAAE;QACxDF,UAAU,EAAE,CAAChI,UAAU,CAACiI,QAAQ;OACjC,CAAC;MACFhE,aAAa,EAAE,IAAInE,kBAAkB,CAAC,IAAI,CAAC6F,YAAY,CAACoC,SAAS,EAAE;QACjEC,UAAU,EAAE;OACb,CAAC;MACF7D,KAAK,EAAE,IAAIrE,kBAAkB,CAAC,IAAI,CAAC+F,kBAAkB,EAAE;QACrDmC,UAAU,EAAE,CAAChI,UAAU,CAACiI,QAAQ;OACjC,CAAC;MACFE,SAAS,EAAE,IAAIrI,kBAAkB,CAAC,IAAI,CAAC0E,iBAAiB,EAAE;QACxDwD,UAAU,EAAE,CAAChI,UAAU,CAACiI,QAAQ;OACjC,CAAC;MACFN,KAAK,EAAE,IAAI7H,kBAAkB,CAAC,IAAI,CAACgF,cAAc,CAACsD,kBAAkB,CAAC,IAAI,CAACzC,YAAY,CAACgC,KAAK,CAAC,EAAE;QAC7FK,UAAU,EAAE;OACb,CAAC;MACFK,KAAK,EAAE,IAAIvI,kBAAkB,CAAC,IAAI,CAAC6F,YAAY,CAAC0C,KAAK,EAAE;QACrDL,UAAU,EAAE,CAAChI,UAAU,CAACiI,QAAQ;OACjC,CAAC;MACFxG,aAAa,EAAE,IAAI3B,kBAAkB,CAAC,IAAI,CAAC6F,YAAY,CAACtB,KAAK,EAAE;QAC7D2D,UAAU,EAAE;OACb,CAAC;MACF3D,KAAK,EAAE,IAAIvE,kBAAkB,CAAC,IAAI,CAAC6F,YAAY,CAACtB,KAAK,EAAE;QACrD2D,UAAU,EAAE,CAAChI,UAAU,CAACiI,QAAQ;OACjC,CAAC;MACFX,SAAS,EAAE,IAAIxH,kBAAkB,CAAC,IAAI,CAACwE,wBAAwB,EAAE;QAC/D0D,UAAU,EAAE,CAAChI,UAAU,CAACiI,QAAQ;OACjC,CAAC;MACFhB,QAAQ,EAAE,IAAInH,kBAAkB,CAAC,IAAI,CAAC2E,SAAS,CAACoC,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACC,GAAG,CAACC,QAAQ,CAAC,IAAI,CAACrB,YAAY,CAACsB,QAAS,CAAC,CAAC,EAAE;QACxGe,UAAU,EAAE,CAAChI,UAAU,CAACiI,QAAQ;OACjC,CAAC;MACFK,SAAS,EAAE,IAAIxI,kBAAkB,CAAC,IAAI,CAAC6F,YAAY,CAAC2C,SAAS,EAAE;QAC7DN,UAAU,EAAE;OACb;KAEF,CAAC;IAEF,IAAI,CAACO,qBAAqB,EAAE;IAE5B,IAAI,IAAI,CAACrE,QAAQ,KAAK,SAAS,EAAE;MAC/B,IAAI,CAAC3C,IAAI,CAACiH,UAAU,CAAC,QAAQ,EAAE,IAAI1I,kBAAkB,CAAC,IAAI,CAACmC,SAAS,CAACuF,MAAM,CAACV,EAAE,IAAIA,EAAE,CAACO,IAAI,KAAK,IAAI,CAAC1B,YAAY,CAAC4B,MAAM,CAAC,CAAC,CAAC,CAAC,EAAEvH,UAAU,CAACiI,QAAQ,CAAC,CAAC;MACjJ,IAAI,CAAC1G,IAAI,CAACkH,aAAa,CAAC,eAAe,CAAC;IAC1C;EACA;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACxD,IAAI,CAACyD,WAAW,EAAE;EACzB;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAChD,IAAI,GAAG,IAAI;EAClB;EAEAiD,UAAUA,CAAA;IACR,IAAI,CAACjD,IAAI,GAAG,KAAK;EACnB;EAEAhC,QAAQA,CAAA;IACJ,IAAI,CAAC8B,SAAS,GAAG,IAAI;IAErB,IAAIoD,aAAa,GAAG,EAAE;IACtB,KAAK,MAAMC,WAAW,IAAI,IAAI,CAACxH,IAAI,CAACC,QAAQ,EAAE;MAC5C,IAAI,IAAI,CAACD,IAAI,CAACC,QAAQ,CAACwH,cAAc,CAACD,WAAW,CAAC,EAAE;QAClD,MAAME,OAAO,GAAG,IAAI,CAAC1H,IAAI,CAACC,QAAQ,CAACuH,WAAW,CAAC;QAC/C,IAAIE,OAAO,CAACvH,OAAO,EAAE;UACnBoH,aAAa,IAAI,GAAGC,WAAW,IAAI;QACrC;MACF;IACF;IAEA,IAAIG,aAAa,GAAG,+CAA+C;IACnE,IAAIJ,aAAa,KAAK,EAAE,EAAE;MACxBA,aAAa,GAAGA,aAAa,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAE5C,IAAIL,aAAa,KAAK,EAAE,EAAE;QACxBI,aAAa,IAAI,2CAA2CJ,aAAa,EAAE;MAC7E;IACF;IAEA,IAAI,CAAC,IAAI,CAACvH,IAAI,CAAC6H,KAAK,EAAE;MACpB,IAAI,CAACvE,KAAK,CAACwE,mBAAmB,CAAC;QAC7BC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAEN;OACT,CAAC;MACF;IACF;IACA,IAAI,CAACnE,WAAW,CAAC0E,gBAAgB,CAAC,IAAI,CAAC;IACzC;EACF;EAEAC,eAAeA,CAACC,KAAU;IACxB,IAAI,CAACnE,iBAAiB,GAAGmE,KAAK;IAC9BC,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAE,CAACC,KAAK,CAACC,WAAW,GAAG,mBAAmB;EACjF;EAGA/H,cAAcA,CAAC2H,KAAU;IACvB,IAAI,CAACzH,qBAAqB,GAAGyH,KAAK,CAACK,KAAK;EAC1C;EAEA7G,iBAAiBA,CAACwG,KAAU;IAC1BhD,OAAO,CAACC,GAAG,CAAC+C,KAAK,CAAC;IAClB,IAAI,CAACrF,wBAAwB,GAAGqF,KAAK,CAACK,KAAK;IAC3C,IAAI,CAACxF,iBAAiB,GAAG,IAAI,CAACD,UAAU,CAACiD,MAAM,CAACV,EAAE,IAAIA,EAAE,CAACmD,GAAG,KAAK,IAAI,CAAC3F,wBAAyB,CAACmD,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC3C,cAAc,CAAC8C,mBAAmB,EAAE;EACvJ;EAEArE,gBAAgBA,CAACoG,KAAU;IACzB,IAAI,CAACpE,gBAAgB,GAAGoE,KAAK,CAACK,KAAK,CAAC3H,IAAI;EAC1C;EAEAgB,iBAAiBA,CAACsG,KAAU;IAC1B,IAAI,CAACnF,iBAAiB,GAAGmF,KAAK,CAACK,KAAK;EACtC;EAEAxH,cAAcA,CAACmH,KAAU;IACvB,IAAIA,KAAK,CAACO,QAAQ,KAAK,EAAE,EACvB,IAAI,CAACvE,YAAY,CAACwE,KAAK,GAAGR,KAAK,CAACO,QAAQ,CAACE,MAAM;IACjD,IAAIJ,KAAK,GAAG,IAAI,CAACzI,IAAI,CAACyI,KAAK;IAC3B,IAAI,CAACrE,YAAY,CAAClE,aAAa,GAAGuI,KAAK,CAACvI,aAAa;IACrD,IAAI,CAACkE,YAAY,CAACoC,SAAS,GAAGiC,KAAK,CAACjG,KAAK;IACzC,IAAI,CAAC4B,YAAY,CAACuC,QAAQ,GAAG8B,KAAK,CAAChG,KAAK;IACxC,IAAI,CAAC2B,YAAY,CAACmC,KAAK,GAAG,IAAI,CAAChD,cAAc,CAACuF,wCAAwC,CAAC,IAAI,CAACxE,kBAAkB,CAAC;IAC/G,IAAI,CAACF,YAAY,CAAC2E,WAAW,GAAG,IAAI,CAACxF,cAAc,CAACuF,wCAAwC,CAAC,IAAI,CAACxE,kBAAkB,CAAC;IACrH,IAAI,CAACF,YAAY,CAACgC,KAAK,GAAI,IAAI,CAACnD,iBAAiB,CAACiD,IAAI,GAAG,GAAG,GAAGuC,KAAK,CAACrC,KAAK;IAC1E,IAAI,CAAChC,YAAY,CAAC0C,KAAK,GAAG2B,KAAK,CAAC3B,KAAK;IACrC,IAAI,CAAC1C,YAAY,CAAC4B,MAAM,GAAGyC,KAAK,CAACzC,MAAM,EAAEF,IAAI;IAC7C,IAAI,CAAC1B,YAAY,CAACsB,QAAQ,GAAG+C,KAAK,CAAC/C,QAAQ,EAAEF,GAAG,CAAC,CAAC,CAAC;IACnD,IAAI,CAACpB,YAAY,CAAC2B,SAAS,GAAG,IAAI,CAAChD,wBAAwB,EAAE+C,IAAI;IACjE,IAAI,CAAC1B,YAAY,CAAC2C,SAAS,GAAG0B,KAAK,CAAC1B,SAAS;IAC7C,IAAI,CAACiC,mBAAmB,EAAE;EAC5B;EAEAA,mBAAmBA,CAAA;IACjB,IAAI,CAAChJ,IAAI,CAACyI,KAAK,CAAC7B,SAAS,GAAG,IAAI,CAAC5G,IAAI,CAACyI,KAAK,CAAC7B,SAAS,CAACV,IAAI;IAC1Dd,OAAO,CAACC,GAAG,CAAC,IAAI,CAACrF,IAAI,CAACyI,KAAK,CAAC;IAC5B,IAAI,CAAC9E,IAAI,CAACkB,GAAG,CAAC,IAAI,CAACrB,WAAW,CAACyF,iBAAiB,CAAC,IAAI,CAAC7E,YAAY,CAAC,CAChEa,SAAS,CAACC,GAAG,IAAG;MACfgE,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACjF,YAAY,CAAC,CAAC;MAC/D,IAAI,CAACZ,WAAW,CAAC8F,eAAe,CAAC,IAAI,CAAClF,YAAY,CAACmF,SAAU,CAAC;MAC9D,IAAI,CAAC/F,WAAW,CAACgG,kBAAkB,EAAE;MACrC,IAAI,CAAClG,KAAK,CAACwE,mBAAmB,CAAC;QAC7BC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;MACF,IAAI,CAACjI,IAAI,CAACyJ,cAAc,EAAE;MAC1B;MACA;IACF,CAAC,CAAC,CAAC;EAEP;EAEApJ,YAAYA,CAACqJ,KAAa;IACxB,OAAS,IAAI,CAAC1J,IAAI,CAAC2J,GAAG,CAACD,KAAK,CAAC,EAAEvJ,OAAO,IAAI,IAAI,CAACH,IAAI,CAAC2J,GAAG,CAACD,KAAK,CAAC,EAAEE,OAAO,IAAM,IAAI,CAACzF,SAAS,IAAI,IAAI,CAACnE,IAAI,CAAC2J,GAAG,CAACD,KAAK,CAAC,EAAEvJ,OAAQ;EAC/H;EAEA0J,YAAYA,CAAA;IACV,OAAO,IAAI,CAACrG,WAAW,CAACqG,YAAY,CAAC,IAAI,CAACzF,YAAY,CAAC;EACzD;EAEA5C,mBAAmBA,CAAC4G,KAAU;IAC5BhD,OAAO,CAACC,GAAG,CAAC+C,KAAK,CAAC;IAClB,IAAI0B,CAAC,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,KAAK,CAAC5B,KAAK,CAAC,CAAC;IACnC,IAAI,CAAC9D,kBAAkB,GAAG,IAAI,CAACf,cAAc,CAAC+C,iBAAiB,CAAC8B,KAAK,CAAC;EACxE;EAEA6B,UAAUA,CAAA;IACR,IAAI,CAAC5F,IAAI,GAAG,CAAC,IAAI,CAACA,IAAI;EACxB;EAEA6F,gBAAgBA,CAACtD,SAAiB;IAChC,OAAO,IAAI,CAACrD,cAAc,CAAC2G,gBAAgB,CAACtD,SAAS,CAAC;EACxD;EAEAtH,aAAaA,CAAA;IACX,MAAM6K,SAAS,GAAG,IAAI,CAAC1G,aAAa,CAAC2G,IAAI,CAACxL,sBAAsB,EAAE;MAChEyL,MAAM,EAAE,kBAAkB;MAC1BC,KAAK,EAAE,OAAO;MACdC,UAAU,EAAE,iBAAiB;MAC7BC,UAAU,EAAE,KAAK;MACjBC,eAAe,EAAE,IAAI;MACrBC,KAAK,EAAE,IAAI;MACXC,YAAY,EAAE;QAAC,YAAY,EAAE,OAAO;QAAE,UAAU,EAAE,MAAM;QAAE,eAAe,EAAE,MAAM;QAAE,SAAS,EAAE;MAAK,CAAC;MACpGC,UAAU,EAAE,KAAK;MACjBC,cAAc,EAAE,kBAAkB;MAClCC,IAAI,EAAE;QAAEC,UAAU,EAAE;UAAChE,SAAS,EAAE,IAAI,CAAC3C,YAAY,CAAC2C;QAAS;MAAC;KAC7D,CAAC;IACF,IAAI,CAACxC,IAAI,CAACyG,IAAI,CAACb,SAAS,CAAC;IACzBA,SAAS,CAACc,OAAO,CAAChG,SAAS,CAAE6F,IAAI,IAAI;MACnC,IAAIA,IAAI,EAAE;QACR1F,OAAO,CAACC,GAAG,CAACyF,IAAI,CAAC;QACjB,IAAI,CAAC9K,IAAI,CAAC2J,GAAG,CAAC,WAAW,CAAC,EAAEuB,UAAU,CAACJ,IAAI,CAACA,IAAI,CAAC;QACjD,IAAI,CAAC9K,IAAI,CAAC2J,GAAG,CAAC,WAAW,CAAC,EAAEwB,sBAAsB,CAACL,IAAI,CAACA,IAAI,CAAC;QAC7D,IAAI,CAAC1G,YAAY,CAAC2C,SAAS,GAAG+D,IAAI,CAACA,IAAI;MACzC;IACF,CAAC,CAAC;EACJ;EAEQ9D,qBAAqBA,CAAA;IAC3B,IAAI,CAACrD,IAAI,CAACkB,GAAG,CAAC,IAAI,CAAC7E,IAAI,CAAC2J,GAAG,CAAC,WAAW,CAAC,EAAEyB,YAAY,CAACnG,SAAS,CAAEwD,KAAK,IAAI;MACzE,IAAIA,KAAK,KAAK,IAAI,CAACrE,YAAY,CAAC2C,SAAS,EAAE;QACzC,IAAI,CAACvC,UAAU,GAAG,IAAI;MACxB;IACF,CAAC,CAAC,CAAC;EACL;EAEA;;;;;EAKQ6G,iBAAiBA,CAAA;IACvB,OAAO,IAAI,CAACrL,IAAI,CAACI,KAAK,IAAI,IAAI,CAACoE,UAAU;EAC3C;EAEA8G,aAAaA,CAAA;IACX,OAAO,IAAI,CAAC9H,WAAW,CAAC+H,4BAA4B,CAAC,IAAI,CAACF,iBAAiB,CAACG,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC9H,mBAAmB,CAAC;EACnH;EAAC,QAAA+H,CAAA,G;qBA3RUrI,aAAa,EAAAvE,EAAA,CAAA6M,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAA/M,EAAA,CAAA6M,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAjN,EAAA,CAAA6M,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAnN,EAAA,CAAA6M,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAArN,EAAA,CAAA6M,iBAAA,CAAAS,EAAA,CAAAxN,aAAA,GAAAE,EAAA,CAAA6M,iBAAA,CAAAU,EAAA,CAAAC,mBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAblJ,aAAa;IAAAmJ,SAAA;IAAAC,QAAA,GAAA3N,EAAA,CAAA4N,kBAAA,CAFb,CAAC9N,aAAa,CAAC;IAAA+N,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCpB5BlO,EAAA,CAAAW,SAAA,aAOM;QACNX,EAAA,CAAAC,cAAA,aAA0C;QAEtCD,EAAA,CAAAa,UAAA,IAAAuN,6BAAA,oBAA6F;QA0LjGpO,EAAA,CAAAY,YAAA,EAAM;;;QA1LwBZ,EAAA,CAAAe,SAAA,GAAiE;QAAjEf,EAAA,CAAAgB,UAAA,UAAAmN,GAAA,CAAApJ,SAAA,KAAAoJ,GAAA,CAAAzJ,cAAA,CAAA2J,eAAA,CAAAF,GAAA,CAAA5I,YAAA,EAAiE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}