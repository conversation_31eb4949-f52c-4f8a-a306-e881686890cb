{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class BuyPackageSuggestionBoxComponent {\n  static #_ = this.ɵfac = function BuyPackageSuggestionBoxComponent_Factory(t) {\n    return new (t || BuyPackageSuggestionBoxComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: BuyPackageSuggestionBoxComponent,\n    selectors: [[\"app-buy-package-suggestion-box\"]],\n    decls: 18,\n    vars: 0,\n    consts: [[1, \"col-12\", \"p-3\"], [1, \"shadow-2\", \"border-round\", \"surface-card\", \"mb-3\", \"h-full\", \"flex-column\", \"justify-content-between\", \"flex\"], [1, \"p-4\"], [1, \"flex\", \"align-items-center\"], [1, \"inline-flex\", \"border-circle\", \"align-items-center\", \"justify-content-center\", \"bg-green-100\", \"mr-3\", 2, \"width\", \"38px\", \"height\", \"38px\"], [1, \"pi\", \"pi-globe\", \"text-xl\", \"text-green-600\"], [1, \"text-900\", \"font-medium\", \"text-2xl\"], [1, \"text-900\", \"my-3\", \"text-xl\", \"font-medium\"], [1, \"mt-0\", \"mb-3\", \"text-700\", \"line-height-3\"], [1, \"px-4\", \"py-3\", \"surface-100\", \"text-right\"], [\"pbutton\", \"\", \"pripple\", \"\", \"icon\", \"pi pi-arrow-right\", \"iconpos\", \"right\", \"label\", \"More\", 1, \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button-success\", \"p-button\", \"p-component\"], [\"aria-hidden\", \"true\", 1, \"p-button-icon\", \"p-button-icon-right\", \"pi\", \"pi-arrow-right\"], [1, \"p-button-label\"], [1, \"p-ink\"]],\n    template: function BuyPackageSuggestionBoxComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"span\", 4);\n        i0.ɵɵelement(5, \"i\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"span\", 6);\n        i0.ɵɵtext(7, \"Card Title\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(8, \"div\", 7);\n        i0.ɵɵtext(9, \"Quam adipiscing vitae proin sagittis.\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"p\", 8);\n        i0.ɵɵtext(11, \"Eget sit amet tellus cras adipiscing enim. At quis risus sed vulputate odio. Proin libero nunc consequat interdum varius sit amet.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(12, \"div\", 9)(13, \"button\", 10);\n        i0.ɵɵelement(14, \"span\", 11);\n        i0.ɵɵelementStart(15, \"span\", 12);\n        i0.ɵɵtext(16, \"More\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(17, \"span\", 13);\n        i0.ɵɵelementEnd()()()();\n      }\n    },\n    styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9jbGFzc3Jvb20vcGFja2FnZXMvYnV5LXBhY2thZ2UvYnV5LXBhY2thZ2Utc3VnZ2VzdGlvbi1ib3gvYnV5LXBhY2thZ2Utc3VnZ2VzdGlvbi1ib3guY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGNBQWM7QUFDaEIiLCJzb3VyY2VzQ29udGVudCI6WyI6aG9zdCB7XG4gIGRpc3BsYXk6IGJsb2NrO1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"],\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["BuyPackageSuggestionBoxComponent", "_", "_2", "selectors", "decls", "vars", "consts", "template", "BuyPackageSuggestionBoxComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\buy-package\\buy-package-suggestion-box\\buy-package-suggestion-box.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\buy-package\\buy-package-suggestion-box\\buy-package-suggestion-box.component.html"], "sourcesContent": ["import { CommonModule } from \"@angular/common\";\nimport { ChangeDetectionStrategy, Component } from '@angular/core';\n\n@Component({\n    selector: 'app-buy-package-suggestion-box',\n    templateUrl: './buy-package-suggestion-box.component.html',\n    styleUrl: './buy-package-suggestion-box.component.css',\n    changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class BuyPackageSuggestionBoxComponent { }\n", "<div class=\"col-12 p-3\">\r\n    <div class=\"shadow-2 border-round surface-card mb-3 h-full flex-column justify-content-between flex\">\r\n        <div class=\"p-4\">\r\n            <div class=\"flex align-items-center\"><span\r\n                    class=\"inline-flex border-circle align-items-center justify-content-center bg-green-100 mr-3\"\r\n                    style=\"width: 38px; height: 38px;\"><i class=\"pi pi-globe text-xl text-green-600\"></i></span><span\r\n                    class=\"text-900 font-medium text-2xl\">Card Title</span></div>\r\n            <div class=\"text-900 my-3 text-xl font-medium\">Quam adipiscing vitae proin sagittis.</div>\r\n            <p class=\"mt-0 mb-3 text-700 line-height-3\">Eget sit amet tellus cras adipiscing enim. At quis risus sed\r\n                vulputate odio. Proin libero nunc consequat interdum varius sit amet.</p>\r\n        </div>\r\n        <div class=\"px-4 py-3 surface-100 text-right\"><button pbutton=\"\" pripple=\"\" icon=\"pi pi-arrow-right\"\r\n                iconpos=\"right\" label=\"More\"\r\n                class=\"p-element p-ripple p-button-rounded p-button-success p-button p-component\"><span\r\n                    class=\"p-button-icon p-button-icon-right pi pi-arrow-right\" aria-hidden=\"true\"></span><span\r\n                    class=\"p-button-label\">More</span><span class=\"p-ink\"></span></button></div>\r\n    </div>\r\n</div>"], "mappings": ";AASA,OAAM,MAAOA,gCAAgC;EAAA,QAAAC,CAAA,G;qBAAhCD,gCAAgC;EAAA;EAAA,QAAAE,EAAA,G;UAAhCF,gCAAgC;IAAAG,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0CAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCNIE,EAHjD,CAAAC,cAAA,aAAwB,aACiF,aAChF,aACwB,cAEM;QAAAD,EAAA,CAAAE,SAAA,WAAkD;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAAAH,EAAA,CAAAC,cAAA,cACtD;QAAAD,EAAA,CAAAI,MAAA,iBAAU;QAAOJ,EAAP,CAAAG,YAAA,EAAO,EAAM;QACrEH,EAAA,CAAAC,cAAA,aAA+C;QAAAD,EAAA,CAAAI,MAAA,4CAAqC;QAAAJ,EAAA,CAAAG,YAAA,EAAM;QAC1FH,EAAA,CAAAC,cAAA,YAA4C;QAAAD,EAAA,CAAAI,MAAA,0IAC6B;QAC7EJ,EAD6E,CAAAG,YAAA,EAAI,EAC3E;QACwCH,EAA9C,CAAAC,cAAA,cAA8C,kBAE4C;QAAAD,EAAA,CAAAE,SAAA,gBACQ;QAAAF,EAAA,CAAAC,cAAA,gBAC/D;QAAAD,EAAA,CAAAI,MAAA,YAAI;QAAAJ,EAAA,CAAAG,YAAA,EAAO;QAAAH,EAAA,CAAAE,SAAA,gBAA2B;QAEjFF,EAFiF,CAAAG,YAAA,EAAS,EAAM,EACtF,EACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}