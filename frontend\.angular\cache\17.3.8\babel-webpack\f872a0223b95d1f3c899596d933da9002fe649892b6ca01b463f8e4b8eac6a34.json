{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nexport class GButtonComponent {\n  set text(name) {\n    this.buttonText = name;\n  }\n  get name() {\n    return this.buttonText;\n  }\n  get buttonClass() {\n    return `btn ${this.class} ${this.color}-gradient-lesson-bg`;\n  }\n  constructor() {\n    this.buttonText = '';\n    this.color = 'default';\n    this.type = 'button';\n    this.btnClick = new EventEmitter();\n    this.isDisabled = false;\n    this.class = '';\n  }\n  onClick() {\n    this.btnClick.emit();\n  }\n  ngOnInit() {}\n  static #_ = this.ɵfac = function GButtonComponent_Factory(t) {\n    return new (t || GButtonComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: GButtonComponent,\n    selectors: [[\"g-button\"]],\n    inputs: {\n      text: \"text\",\n      color: \"color\",\n      type: \"type\",\n      isDisabled: \"isDisabled\",\n      class: \"class\"\n    },\n    outputs: {\n      btnClick: \"btnClick\"\n    },\n    decls: 3,\n    vars: 5,\n    consts: [[3, \"click\", \"type\", \"disabled\"]],\n    template: function GButtonComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"button\", 0);\n        i0.ɵɵlistener(\"click\", function GButtonComponent_Template_button_click_0_listener() {\n          return ctx.onClick();\n        });\n        i0.ɵɵelementStart(1, \"span\");\n        i0.ɵɵtext(2);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.buttonClass + \" \" + ctx.color + \"-gradient-lesson-bg\");\n        i0.ɵɵproperty(\"type\", ctx.type)(\"disabled\", ctx.isDisabled);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.buttonText);\n      }\n    },\n    styles: [\".btn[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  color: #ffffff;\\n  padding: 0 1.25rem;\\n  width: 100%;\\n  font-size: clamp(0.88rem, 0.09vw + 0.86rem, 1rem);\\n  vertical-align: middle;\\n  display: flex;\\n  justify-content: center;\\n  border-radius: 50px;\\n  font-stretch: normal;\\n  font-family: \\\"Lato Bold\\\", sans-serif;\\n  line-height: 50px;\\n  letter-spacing: 0.1px;\\n  text-align: left;\\n  gap: 16px;\\n  align-items: center;\\n  border: 1px solid transparent;\\n}\\n.btn[_ngcontent-%COMP%]:hover {\\n  background: #5e71db;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2xheW91dC9nLWJ1dHRvbi9nLWJ1dHRvbi5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNDLGVBQUE7RUFDRyxjQUFBO0VBQ0gsa0JBQUE7RUFDRyxXQUFBO0VBQ0EsaURBQUE7RUFDSCxzQkFBQTtFQUNHLGFBQUE7RUFDQSx1QkFBQTtFQUNBLG1CQUFBO0VBQ0gsb0JBQUE7RUFDRyxvQ0FBQTtFQUNBLGlCQUFBO0VBQ0EscUJBQUE7RUFDQSxnQkFBQTtFQUNBLFNBQUE7RUFDQSxtQkFBQTtFQUNBLDZCQUFBO0FBQ0o7QUFBQztFQUNDLG1CQUFBO0FBRUYiLCJzb3VyY2VzQ29udGVudCI6WyIuYnRuIHtcclxuXHRjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICBjb2xvcjogI2ZmZmZmZjtcclxuXHRwYWRkaW5nOiAwIDEuMjVyZW07XHJcbiAgICB3aWR0aDogMTAwJTtcclxuICAgIGZvbnQtc2l6ZTogY2xhbXAoMC44OHJlbSwgMC4wOXZ3ICsgMC44NnJlbSwgMXJlbSk7XHJcblx0dmVydGljYWwtYWxpZ246IG1pZGRsZTtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICAgIGJvcmRlci1yYWRpdXM6IDUwcHg7XHJcblx0Zm9udC1zdHJldGNoOiBub3JtYWw7XHJcbiAgICBmb250LWZhbWlseTogJ0xhdG8gQm9sZCcsIHNhbnMtc2VyaWY7XHJcbiAgICBsaW5lLWhlaWdodDogNTBweDtcclxuICAgIGxldHRlci1zcGFjaW5nOiAwLjFweDtcclxuICAgIHRleHQtYWxpZ246IGxlZnQ7XHJcbiAgICBnYXA6IDE2cHg7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgYm9yZGVyOiAxcHggc29saWQgdHJhbnNwYXJlbnQ7XHJcblx0Jjpob3ZlciB7XHJcblx0XHRiYWNrZ3JvdW5kOiAjNWU3MWRiO1xyXG5cdH1cclxufVxyXG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "GButtonComponent", "text", "name", "buttonText", "buttonClass", "class", "color", "constructor", "type", "btnClick", "isDisabled", "onClick", "emit", "ngOnInit", "_", "_2", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "GButtonComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵlistener", "GButtonComponent_Template_button_click_0_listener", "ɵɵtext", "ɵɵelementEnd", "ɵɵclassMap", "ɵɵproperty", "ɵɵadvance", "ɵɵtextInterpolate"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\layout\\g-button\\g-button.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\layout\\g-button\\g-button.component.html"], "sourcesContent": ["import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';\r\ntype ColorType = 'default' | 'primary' | 'trial' | 'trial-completed' | 'disabled';\r\n@Component({\r\n\tselector: 'g-button',\r\n\ttemplateUrl: './g-button.component.html',\r\n\tstyleUrls: ['./g-button.component.scss']\r\n})\r\nexport class GButtonComponent implements OnInit {\r\n  \tpublic buttonText = '';\r\n\r\n\t@Input()\r\n\tset text(name: string) {\r\n\t\tthis.buttonText = name;\r\n\t}\r\n\tget name(): string {\r\n\t\treturn this.buttonText;\r\n\t}\r\n\r\n\t@Input() color: ColorType = 'default';\r\n\t@Input() type: string = 'button';\r\n\t@Output() btnClick = new EventEmitter();\r\n\t@Input() isDisabled = false;\r\n\t@Input() class = '';\r\n\r\n\tget buttonClass() {\r\n\t\treturn `btn ${this.class} ${this.color}-gradient-lesson-bg`;\r\n\t}\r\n\r\n\r\n\tconstructor() {}\r\n\r\n\tonClick() {\r\n\t\tthis.btnClick.emit();\r\n\t}\r\n  \r\n  ngOnInit(): void {}\r\n}", "<button\r\n\t[type]=\"type\"\r\n\t(click)=\"onClick()\"\r\n\t[disabled]=\"isDisabled\"\r\n\t[class]=\"buttonClass + ' ' + color + '-gradient-lesson-bg'\">\r\n\t<span>{{ buttonText }}</span>\r\n</button>"], "mappings": "AAAA,SAA2CA,YAAY,QAAQ,eAAe;;AAO9E,OAAM,MAAOC,gBAAgB;EAG5B,IACIC,IAAIA,CAACC,IAAY;IACpB,IAAI,CAACC,UAAU,GAAGD,IAAI;EACvB;EACA,IAAIA,IAAIA,CAAA;IACP,OAAO,IAAI,CAACC,UAAU;EACvB;EAQA,IAAIC,WAAWA,CAAA;IACd,OAAO,OAAO,IAAI,CAACC,KAAK,IAAI,IAAI,CAACC,KAAK,qBAAqB;EAC5D;EAGAC,YAAA;IArBS,KAAAJ,UAAU,GAAG,EAAE;IAUf,KAAAG,KAAK,GAAc,SAAS;IAC5B,KAAAE,IAAI,GAAW,QAAQ;IACtB,KAAAC,QAAQ,GAAG,IAAIV,YAAY,EAAE;IAC9B,KAAAW,UAAU,GAAG,KAAK;IAClB,KAAAL,KAAK,GAAG,EAAE;EAOJ;EAEfM,OAAOA,CAAA;IACN,IAAI,CAACF,QAAQ,CAACG,IAAI,EAAE;EACrB;EAECC,QAAQA,CAAA,GAAU;EAAC,QAAAC,CAAA,G;qBA5BRd,gBAAgB;EAAA;EAAA,QAAAe,EAAA,G;UAAhBf,gBAAgB;IAAAgB,SAAA;IAAAC,MAAA;MAAAhB,IAAA;MAAAK,KAAA;MAAAE,IAAA;MAAAE,UAAA;MAAAL,KAAA;IAAA;IAAAa,OAAA;MAAAT,QAAA;IAAA;IAAAU,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCP7BE,EAAA,CAAAC,cAAA,gBAI6D;QAF5DD,EAAA,CAAAE,UAAA,mBAAAC,kDAAA;UAAA,OAASJ,GAAA,CAAAd,OAAA,EAAS;QAAA,EAAC;QAGnBe,EAAA,CAAAC,cAAA,WAAM;QAAAD,EAAA,CAAAI,MAAA,GAAgB;QACvBJ,EADuB,CAAAK,YAAA,EAAO,EACrB;;;QAFRL,EAAA,CAAAM,UAAA,CAAAP,GAAA,CAAArB,WAAA,SAAAqB,GAAA,CAAAnB,KAAA,yBAA2D;QAD3DoB,EAFA,CAAAO,UAAA,SAAAR,GAAA,CAAAjB,IAAA,CAAa,aAAAiB,GAAA,CAAAf,UAAA,CAEU;QAEjBgB,EAAA,CAAAQ,SAAA,GAAgB;QAAhBR,EAAA,CAAAS,iBAAA,CAAAV,GAAA,CAAAtB,UAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}