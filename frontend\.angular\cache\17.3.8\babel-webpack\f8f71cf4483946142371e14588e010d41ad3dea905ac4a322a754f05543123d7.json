{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { ChangeDetectionStrategy, Component } from '@angular/core';\nimport { PackagesModel } from \"src/app/core/models/package.model\";\nlet BuyPackageSuggestionBoxComponent = class BuyPackageSuggestionBoxComponent {\n  constructor() {\n    this.defaultPackages = PackagesModel.getDefaultPackages();\n    this.selectedPackage = this.defaultPackages[0].type;\n  }\n  selectPackage(type) {\n    this.selectedPackage = type;\n  }\n};\nBuyPackageSuggestionBoxComponent = __decorate([Component({\n  selector: 'app-buy-package-suggestion-box',\n  templateUrl: './buy-package-suggestion-box.component.html',\n  styleUrl: './buy-package-suggestion-box.component.css',\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], BuyPackageSuggestionBoxComponent);\nexport { BuyPackageSuggestionBoxComponent };", "map": {"version": 3, "names": ["ChangeDetectionStrategy", "Component", "PackagesModel", "BuyPackageSuggestionBoxComponent", "constructor", "defaultPackages", "getDefaultPackages", "selected<PERSON><PERSON><PERSON>", "type", "selectPackage", "__decorate", "selector", "templateUrl", "styleUrl", "changeDetection", "OnPush"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\buy-package\\buy-package-suggestion-box\\buy-package-suggestion-box.component.ts"], "sourcesContent": ["import { CommonModule } from \"@angular/common\";\nimport { ChangeDetectionStrategy, Component } from '@angular/core';\nimport { PackagesModel } from \"src/app/core/models/package.model\";\n\n@Component({\n    selector: 'app-buy-package-suggestion-box',\n    templateUrl: './buy-package-suggestion-box.component.html',\n    styleUrl: './buy-package-suggestion-box.component.css',\n    changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class BuyPackageSuggestionBoxComponent {\n\n    public defaultPackages = PackagesModel.getDefaultPackages();\n\n    selectedPackage = this.defaultPackages[0].type;\n\n\n    public selectPackage(type: any) {\n        this.selectedPackage = type;\n    }\n}\n"], "mappings": ";AACA,SAASA,uBAAuB,EAAEC,SAAS,QAAQ,eAAe;AAClE,SAASC,aAAa,QAAQ,mCAAmC;AAQ1D,IAAMC,gCAAgC,GAAtC,MAAMA,gCAAgC;EAAtCC,YAAA;IAEI,KAAAC,eAAe,GAAGH,aAAa,CAACI,kBAAkB,EAAE;IAE3D,KAAAC,eAAe,GAAG,IAAI,CAACF,eAAe,CAAC,CAAC,CAAC,CAACG,IAAI;EAMlD;EAHWC,aAAaA,CAACD,IAAS;IAC1B,IAAI,CAACD,eAAe,GAAGC,IAAI;EAC/B;CACH;AAVYL,gCAAgC,GAAAO,UAAA,EAN5CT,SAAS,CAAC;EACPU,QAAQ,EAAE,gCAAgC;EAC1CC,WAAW,EAAE,6CAA6C;EAC1DC,QAAQ,EAAE,4CAA4C;EACtDC,eAAe,EAAEd,uBAAuB,CAACe;CAC5C,CAAC,C,EACWZ,gCAAgC,CAU5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}