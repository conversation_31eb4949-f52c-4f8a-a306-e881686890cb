{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { ClassroomType } from '../models/classroom.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./auth.service\";\nimport * as i2 from \"./student-trial.service\";\nexport let LayoutService = /*#__PURE__*/(() => {\n  class LayoutService {\n    constructor(authService, studentTrialService) {\n      this.authService = authService;\n      this.studentTrialService = studentTrialService;\n      this.menuItems = [{\n        title: \"Dashboard\",\n        url: \"/dashboard\",\n        icon: \"/assets/icons/dashboard-icon.svg\",\n        lottieIcon: \"https://assets8.lottiefiles.com/packages/lf20_h3TUuoD3DG.json\",\n        disabled: false,\n        role: [\"Student\", \"Teacher\"],\n        disableOnStudentTrial: false\n      }, {\n        title: \"Classrooms\",\n        url: \"/dashboard/classrooms\",\n        icon: \"/assets/icons/classroom.svg\",\n        lottieIcon: \"https://assets8.lottiefiles.com/packages/lf20_mflokyg2.json\",\n        disabled: false,\n        role: [\"Student\", \"Teacher\"],\n        scale: 0.8,\n        disableOnStudentTrial: true\n      }, {\n        title: \"Trials\",\n        url: \"/dashboard/classrooms/trials/page\",\n        icon: \"/assets/icons/classroom.svg\",\n        lottieIcon: \"https://assets4.lottiefiles.com/packages/lf20_voBLTvIbhG.json\",\n        disabled: false,\n        role: [\"Teacher\"],\n        scale: 0.8,\n        disableOnStudentTrial: true\n      },\n      // { title: 'Trials', url: '/dashboard/trials', icon: '/assets/icons/trial.svg', role:  ['Teacher']},\n      {\n        title: \"Calendar\",\n        url: \"/dashboard/calendar\",\n        icon: \"/assets/icons/calendar.svg\",\n        lottieIcon: \"https://assets8.lottiefiles.com/packages/lf20_G2wmKm6yAG.json\",\n        disabled: false,\n        role: [\"Student\", \"Teacher\"],\n        scale: 2.3,\n        disableOnStudentTrial: true\n      }, {\n        title: \"Library\",\n        url: \"/dashboard/library\",\n        icon: \"/assets/icons/library.svg\",\n        lottieIcon: \"https://assets4.lottiefiles.com/packages/lf20_e6hgcbdn.json\",\n        disabled: false,\n        scale: 2.1,\n        role: [\"Student\", \"Teacher\"],\n        disableOnStudentTrial: true\n      },\n      // { title: 'Notes', url: '/dashboard/notes', icon: '/assets/icons/notes.svg', role: ['Student', 'Teacher']},\n      {\n        title: \"Notes\",\n        url: \"/dashboard/notes\",\n        icon: \"/assets/icons/library.svg\",\n        lottieIcon: \"https://assets10.lottiefiles.com/private_files/lf30_cldvedro.json\",\n        disabled: true,\n        role: [\"Teacher\", \"Student\"],\n        disableOnStudentTrial: true\n      }, {\n        title: \"AI Chat\",\n        url: \"/dashboard/ai-chat\",\n        icon: \"/assets/icons/certificate.svg\",\n        lottieIcon: \"/assets/icons/lottie/ai-chat-2.json\",\n        disabled: true,\n        scale: 1.3,\n        role: [\"Student\"],\n        disableOnStudentTrial: true\n      }, {\n        title: \"Homework\",\n        url: \"/dashboard/homework\",\n        icon: \"/assets/icons/homework.svg\",\n        lottieIcon: \"https://assets8.lottiefiles.com/packages/lf20_hylaaytn.json\",\n        disabled: true,\n        role: [\"Student\", \"Teacher\"],\n        disableOnStudentTrial: true\n      }, {\n        title: \"Certificates\",\n        url: \"/dashboard/certificates\",\n        icon: \"/assets/icons/certificate.svg\",\n        lottieIcon: \"/assets/icons/lottie/certificates.json\",\n        disabled: true,\n        role: [\"Student\"],\n        disableOnStudentTrial: true\n      },\n      // {\n      //   title: \"Guides\",\n      //   url: \"/dashboard/guides\",\n      //   icon: \"/assets/icons/certificate.svg\",\n      //   lottieIcon: \"/assets/icons/lottie/guides.json\",\n      //   disabled: false,\n      //   role: [\"Student\", \"Teacher\"],\n      //   disableOnStudentTrial: true,\n      // },\n      {\n        title: \"Packages\",\n        url: \"/dashboard/my-packages\",\n        icon: \"/assets/icons/certificate.svg\",\n        lottieIcon: \"/assets/icons/lottie/packages.json\",\n        disabled: false,\n        scale: 1.5,\n        role: [\"Student\"],\n        disableOnStudentTrial: false\n      }];\n      this.extraMobileMenuItems = [{\n        title: \"Settings\",\n        url: \"/user-profile/info\",\n        icon: \"/assets/icons/dashboard-icon.svg\",\n        lottieIcon: \"https://assets8.lottiefiles.com/packages/lf20_wcdzldes.json\",\n        disabled: false,\n        role: [\"Student\", \"Teacher\"],\n        disableOnStudentTrial: false\n      },\n      // {\n      //   title: \"Notifications\",\n      //   url: \"/notifications\",\n      //   icon: \"/assets/icons/dashboard-icon.svg\",\n      //   lottieIcon: \"https://assets8.lottiefiles.com/packages/lf20_daV8h3.json\",\n      //   disabled: false,\n      //   role: [\"Student\", \"Teacher\"],\n      //   disableOnStudentTrial: false,\n      // },\n      {\n        title: \"Logout\",\n        url: \"\",\n        icon: \"/assets/icons/dashboard-icon.svg\",\n        lottieIcon: \"https://assets9.lottiefiles.com/datafiles/xjFX0JCCOm2Lhp1/data.json\",\n        disabled: false,\n        role: [\"Student\", \"Teacher\"],\n        disableOnStudentTrial: false,\n        method: () => {\n          this.authService.logout();\n        }\n      }];\n      this.currentMenuIndex$ = new BehaviorSubject(0);\n      this.currentMenuIndex = this.currentMenuIndex$.asObservable();\n      this.sideMenuHeight$ = new BehaviorSubject(0);\n      this.sideMenuHeight = this.sideMenuHeight$.asObservable();\n    }\n    setCurrentMenuIndex(index) {\n      this.currentMenuIndex$.next(index);\n    }\n    setSideMenuHeight(height) {\n      this.sideMenuHeight$.next(height);\n    }\n    updateMenuItemDisabledStatus(title, disabled) {\n      const menuItem = this.menuItems.find(item => item.title === title);\n      if (menuItem) {\n        menuItem.disabled = disabled;\n      }\n    }\n    updateAllMenuItemsDisabledStatus(disabled) {\n      this.menuItems.forEach(item => {\n        if (item.title !== \"Homework\" && item.title !== \"Guides\" && item.title !== \"Certificates\") {\n          item.disabled = disabled;\n        }\n      });\n    }\n    updateMenuItemClassroomToTrialsForStudent(classrooms) {\n      this.menuItems.forEach(item => {\n        if (item.title === 'Classrooms') {\n          if (classrooms) {\n            const numberOfPaidClassrooms = classrooms.filter(classroom => classroom.type === ClassroomType.PAID).length;\n            console.log(`The user has ${numberOfPaidClassrooms} paid classrooms.`);\n            if (numberOfPaidClassrooms > 0) {\n              item.url = \"/dashboard/classrooms\";\n            } else {\n              item.url = \"/dashboard/classrooms/trials/true\";\n            }\n          }\n        }\n      });\n    }\n    getMenuItems() {\n      const menuItemsCopy = this.menuItems.slice();\n      const role = this.authService.getUserRole();\n      for (let i = 0; i < menuItemsCopy.length; i++) {\n        if (role === \"Student\") {\n          if (this.studentTrialService.hasTrialRequested() && menuItemsCopy[i].disableOnStudentTrial) {\n            menuItemsCopy[i].disabled = true;\n          }\n          if (menuItemsCopy[i].title === \"Guides\") {\n            menuItemsCopy[i].disabled = true;\n          }\n        } else {\n          if (menuItemsCopy[i].title === \"Guides\") {\n            menuItemsCopy[i].disabled = true;\n          }\n        }\n      }\n      return menuItemsCopy;\n    }\n    getExtraMobileMenuItems() {\n      return this.extraMobileMenuItems;\n    }\n    static #_ = this.ɵfac = function LayoutService_Factory(t) {\n      return new (t || LayoutService)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.StudentTrialService));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: LayoutService,\n      factory: LayoutService.ɵfac,\n      providedIn: \"root\"\n    });\n  }\n  return LayoutService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}