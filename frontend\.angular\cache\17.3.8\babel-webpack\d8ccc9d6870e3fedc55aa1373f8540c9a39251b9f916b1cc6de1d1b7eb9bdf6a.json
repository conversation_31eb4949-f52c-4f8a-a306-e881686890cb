{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport let UserInfoGuard = /*#__PURE__*/(() => {\n  class UserInfoGuard {\n    constructor(authService, router) {\n      this.authService = authService;\n      this.router = router;\n    }\n    canActivate(route, state) {\n      const isUserInfoFullefilled = this.authService.isUserInfoFullfileld();\n      if (!isUserInfoFullefilled) {\n        this.router.navigateByUrl('/profile/(profile:info)', {\n          replaceUrl: true\n        });\n      }\n      return isUserInfoFullefilled; //allow to go to router if true\n    }\n    static #_ = this.ɵfac = function UserInfoGuard_Factory(t) {\n      return new (t || UserInfoGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UserInfoGuard,\n      factory: UserInfoGuard.ɵfac\n    });\n  }\n  return UserInfoGuard;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}