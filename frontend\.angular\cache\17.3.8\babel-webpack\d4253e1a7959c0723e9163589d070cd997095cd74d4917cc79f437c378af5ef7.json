{"ast": null, "code": "import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nimport * as i2 from \"src/app/core/services/student-trial.service\";\nimport * as i3 from \"src/app/core/services/teacher-application.service\";\nimport * as i4 from \"src/app/core/services/toast.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../../../shared/prime/input/prime-input-dropdown/prime-input-dropdown.component\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"primeng/inputtext\";\nconst _c0 = (a0, a1) => ({\n  \"ng-invalid ng-dirty my-invalid-input\": a0,\n  \"my-valid-input\": a1\n});\nexport class DashboardContactUsFormComponent {\n  constructor(generalService, studentTrialService, teacherApplicationService, toast) {\n    this.generalService = generalService;\n    this.studentTrialService = studentTrialService;\n    this.teacherApplicationService = teacherApplicationService;\n    this.toast = toast;\n    this.subs = new SubSink();\n    this.form = new UntypedFormGroup({});\n    this.studentOptions = ['Yes', 'No'];\n    this.selectedMltGroup = null;\n    this.formSubmitted = false;\n    this.selectedPhoneCode = {};\n    this.mltLanguages = [];\n    this.countries = [];\n    this.phoneCodes = this.generalService.getPhoneCodes();\n    this.languages = this.generalService.languages;\n  }\n  ngOnInit() {\n    this.countries = this.generalService.getCountries();\n    this.mltLanguages = this.generalService.mltLanguages;\n    this.subs.sink = this.teacherApplicationService.getLingoTeachingLanguages().subscribe(res => {\n      this.mltLanguages = res;\n    });\n    this.initializeForm();\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  initializeForm() {\n    this.form = new UntypedFormGroup({\n      firstName: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      }),\n      lastName: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      }),\n      phoneCode: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      }),\n      contactNumber: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      }),\n      studentType: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      }),\n      email: new UntypedFormControl(null, {\n        validators: [Validators.required, Validators.email]\n      }),\n      wantToLearnLanguage: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      }),\n      message: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      })\n    });\n  }\n  validate() {\n    if (this.form.invalid) {\n      for (const control of Object.keys(this.form.controls)) {\n        this.form.controls[control].markAsTouched();\n      }\n      return;\n    }\n  }\n  submit() {\n    this.validate();\n    this.formSubmitted = true;\n    if (!this.form.valid) {\n      return false;\n    }\n    fetch(\"https://ipinfo.io/json?token=adb3400371759e\").then(response => response.json()).then(jsonResponse => console.log(jsonResponse.country));\n    this.subs.sink = this.studentTrialService.postContactUs(this.prepareDataBeforeSubmit()).subscribe(res => {\n      if (res) {\n        this.showSuccessMessage();\n        this.form.reset();\n      }\n    });\n    return true;\n  }\n  onPhoneCodeChange(event) {\n    setTimeout(() => {\n      this.selectedPhoneCode = event.value;\n    }, 100);\n  }\n  findCountryImage(phoneCode) {\n    return this.generalService.findCountryImage(phoneCode);\n  }\n  isInvalidControl(controlName) {\n    const control = this.form.get(controlName);\n    return control && control.invalid && (control.touched || this.formSubmitted);\n  }\n  isValidControl(controlName) {\n    const control = this.form.get(controlName);\n    return control && control.valid;\n  }\n  isInvalid() {\n    return this.form.dirty && this.form.invalid;\n  }\n  prepareDataBeforeSubmit() {\n    const studentType = this.form.value.studentType === 'Yes' ? 'New Student' : 'Old Student';\n    const formattedData = {\n      userData: {\n        firstName: this.form.value.firstName,\n        lastName: this.form.value.lastName,\n        email: this.form.value.email,\n        phone: `${this.selectedPhoneCode.code} ${this.form.value.contactNumber}`\n      },\n      course: this.form.value.wantToLearnLanguage,\n      studentType: studentType,\n      message: this.form.value.message\n    };\n    return formattedData;\n  }\n  showSuccessMessage() {\n    this.toast.setShowToastmessage({\n      severity: 'success',\n      summary: '',\n      detail: 'Your message was successfully sent.'\n    });\n  }\n  static #_ = this.ɵfac = function DashboardContactUsFormComponent_Factory(t) {\n    return new (t || DashboardContactUsFormComponent)(i0.ɵɵdirectiveInject(i1.GeneralService), i0.ɵɵdirectiveInject(i2.StudentTrialService), i0.ɵɵdirectiveInject(i3.TeacherApplicationService), i0.ɵɵdirectiveInject(i4.ToastService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: DashboardContactUsFormComponent,\n    selectors: [[\"app-dashboard-contact-us-form\"]],\n    decls: 67,\n    vars: 42,\n    consts: [[\"contactFormWrapper\", \"\"], [1, \"contact-form-wrapper\", \"mb-5\"], [1, \"block-header\", \"justify-content-center\", \"lg:flex\"], [1, \"block-title\"], [1, \"text-0\", \"text-center\", \"font-xl\"], [1, \"surface-section\", \"px-2\", \"lg:px-4\", \"pb-5\", \"pt-3\"], [3, \"formGroup\"], [1, \"grid\"], [1, \"col-12\", \"lg:col-2\", \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"side-heading\", \"uppercase\", \"fl-typo\", \"text-center\", \"font-base\"], [1, \"col-12\", \"lg:col-10\", \"pb-1\"], [1, \"p-fluid\", \"grid\"], [1, \"field\", \"mb-0\", \"col-12\", \"md:col-6\", \"font-sm\"], [1, \"p-float-label\", \"p-input-icon-left\"], [1, \"pi\", \"icon\"], [\"type\", \"text\", \"placeholder\", \"First Name\", \"formControlName\", \"firstName\", \"pInputText\", \"\", 1, \"input-blue\", \"rounded-less\", \"white-bg\", 3, \"ngClass\"], [\"type\", \"text\", \"placeholder\", \"*Last Name\", \"formControlName\", \"lastName\", \"pInputText\", \"\", 1, \"input-blue\", \"rounded-less\", \"white-bg\", 3, \"ngClass\"], [1, \"pi\", \"icon\", \"mail-icon\"], [\"type\", \"text\", \"pInputText\", \"\", \"formControlName\", \"email\", \"placeholder\", \"Email\", 1, \"input-blue\", \"rounded-less\", \"white-bg\", 3, \"ngClass\"], [1, \"flex\", \"sm:flex\"], [\"optionLabel\", \"code\", \"filterBy\", \"code\", \"flagFilter\", \"code\", \"templateValue\", \"code\", \"styleClass\", \"dropdown-blue white-bg phone-code-input tiny rounded-less small-dropdown-items\", 3, \"valueSelected\", \"parentForm\", \"countries\", \"filter\", \"inputName\", \"placeholder\", \"isInvalidControl\", \"isValidControl\", \"withFlags\", \"selectedItemValue\"], [1, \"p-float-label\", \"w-full\"], [\"type\", \"text\", \"placeholder\", \"Phone Number\", \"required\", \"\", \"pInputText\", \"\", \"formControlName\", \"contactNumber\", 1, \"input-blue\", \"rounded-less\", \"white-bg\", \"no-radius-left\", 3, \"ngClass\"], [1, \"col-12\", \"lg:col-10\"], [1, \"field\", \"mb-0\", \"col-12\", \"md:col-6\", \"font-sm\", \"no-padding\"], [3, \"parentForm\", \"countries\", \"inputName\", \"placeholder\", \"isInvalidControl\", \"isValidControl\"], [1, \"p-float-label\"], [\"filterBy\", \"name\", 3, \"parentForm\", \"countries\", \"inputName\", \"placeholder\", \"isInvalidControl\", \"isValidControl\"], [1, \"grid\", \"relative\"], [1, \"p-fluid\", \"grid\", \"font-sm\"], [1, \"field\", \"mb-0\", \"col-12\", \"md:col-12\"], [\"formControlName\", \"message\", \"rows\", \"12\", 1, \"input-blue\", \"textarea-bg\", 3, \"ngClass\"], [1, \"\"], [1, \"fluid\", \"grid\"], [1, \"field\", \"mb-0\", \"col-12\", \"flex\", \"justify-content-center\"], [\"pRipple\", \"\", \"pbutton\", \"\", \"label\", \"Send\", \"icon\", \"pi pi-discord\", 1, \"element\", \"ripple\", \"p-button-raised\", \"p-button-rounded\", \"white-space-nowrap\", \"p-button\", \"btn-gradient-blue\", \"p-component\", \"w-10rem\", \"font-lg\", \"flex\", \"justify-content-center\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"p-button-icon\", \"p-button-icon-right\"], [\"src\", \"/assets/icons/forward-arrow.svg\", \"width\", \"24\", 1, \"ml-2\"]],\n    template: function DashboardContactUsFormComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 1, 0)(2, \"div\", 2)(3, \"span\", 3)(4, \"span\", 4);\n        i0.ɵɵtext(5, \"Contact us\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(6, \"div\", 5)(7, \"form\", 6)(8, \"div\", 7)(9, \"div\", 8)(10, \"h2\", 9);\n        i0.ɵɵtext(11, \"Fill in the form\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(12, \"div\", 10)(13, \"div\", 11)(14, \"div\", 12)(15, \"label\");\n        i0.ɵɵtext(16, \"* First Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"span\", 13);\n        i0.ɵɵelement(18, \"i\", 14)(19, \"input\", 15);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(20, \"div\", 12)(21, \"label\");\n        i0.ɵɵtext(22, \"* Last Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"span\", 13);\n        i0.ɵɵelement(24, \"i\", 14)(25, \"input\", 16);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(26, \"div\", 12)(27, \"label\");\n        i0.ɵɵtext(28, \"* Email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(29, \"span\", 13);\n        i0.ɵɵelement(30, \"i\", 17)(31, \"input\", 18);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(32, \"div\", 12)(33, \"label\");\n        i0.ɵɵtext(34, \"* Phone Number\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(35, \"span\", 19)(36, \"app-prime-input-dropdown\", 20);\n        i0.ɵɵlistener(\"valueSelected\", function DashboardContactUsFormComponent_Template_app_prime_input_dropdown_valueSelected_36_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onPhoneCodeChange($event));\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(37, \"span\", 21);\n        i0.ɵɵelement(38, \"input\", 22);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(39, \"div\", 7);\n        i0.ɵɵelement(40, \"div\", 8);\n        i0.ɵɵelementStart(41, \"div\", 23)(42, \"div\", 11)(43, \"div\", 24)(44, \"label\");\n        i0.ɵɵtext(45, \"* I want to learn/I am learning\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(46, \"app-prime-input-dropdown\", 25);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(47, \"div\", 24)(48, \"label\");\n        i0.ɵɵtext(49, \"* Are you a new student?\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(50, \"span\", 26);\n        i0.ɵɵelement(51, \"app-prime-input-dropdown\", 27);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(52, \"div\", 28);\n        i0.ɵɵelement(53, \"div\", 8);\n        i0.ɵɵelementStart(54, \"div\", 23)(55, \"div\", 29)(56, \"label\");\n        i0.ɵɵtext(57, \"* Message\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(58, \"div\", 30);\n        i0.ɵɵelement(59, \"textarea\", 31);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(60, \"div\", 32)(61, \"div\", 33)(62, \"div\", 34)(63, \"button\", 35);\n        i0.ɵɵlistener(\"click\", function DashboardContactUsFormComponent_Template_button_click_63_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.submit());\n        });\n        i0.ɵɵelement(64, \"span\", 36);\n        i0.ɵɵtext(65, \" Send \");\n        i0.ɵɵelement(66, \"img\", 37);\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"formGroup\", ctx.form);\n        i0.ɵɵadvance(12);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(27, _c0, ctx.isInvalidControl(\"firstName\"), ctx.isValidControl(\"firstName\")));\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(30, _c0, ctx.isInvalidControl(\"lastName\"), ctx.isValidControl(\"lastName\")));\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(33, _c0, ctx.isInvalidControl(\"email\"), ctx.isValidControl(\"email\")));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"parentForm\", ctx.form)(\"countries\", ctx.phoneCodes)(\"filter\", true)(\"inputName\", \"phoneCode\")(\"placeholder\", \"\")(\"isInvalidControl\", ctx.isInvalidControl(\"phoneCode\"))(\"isValidControl\", ctx.isValidControl(\"phoneCode\"))(\"withFlags\", true)(\"selectedItemValue\", ctx.selectedPhoneCode);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(36, _c0, ctx.isInvalidControl(\"contactNumber\"), ctx.isValidControl(\"contactNumber\")));\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"parentForm\", ctx.form)(\"countries\", ctx.mltLanguages)(\"inputName\", \"wantToLearnLanguage\")(\"placeholder\", \"I want to learn/I am learning\")(\"isInvalidControl\", ctx.isInvalidControl(\"wantToLearnLanguage\"))(\"isValidControl\", ctx.isValidControl(\"wantToLearnLanguage\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"parentForm\", ctx.form)(\"countries\", ctx.studentOptions)(\"inputName\", \"studentType\")(\"placeholder\", \"Are you a new student?\")(\"isInvalidControl\", ctx.isInvalidControl(\"studentType\"))(\"isValidControl\", ctx.isValidControl(\"studentType\"));\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(39, _c0, ctx.isInvalidControl(\"message\"), ctx.isValidControl(\"message\")));\n      }\n    },\n    dependencies: [i5.NgClass, i6.PrimeInputDropdownComponent, i7.ɵNgNoValidate, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgControlStatusGroup, i7.RequiredValidator, i7.FormGroupDirective, i7.FormControlName, i8.InputText],\n    styles: [\"@charset \\\"UTF-8\\\";\\n.responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.modal[_ngcontent-%COMP%] {\\n  min-width: 50vw;\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown:not(.p-disabled):hover {\\n  border: 2px solid rgb(228, 185, 84);\\n}\\n\\n  .no-label > .p-button-label {\\n  display: none;\\n}\\n\\ninput[type=file][_ngcontent-%COMP%] {\\n  visibility: hidden;\\n  width: 10px;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%] {\\n  position: relative;\\n  padding: 10px;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%]::-webkit-calendar-picker-indicator {\\n  color: transparent;\\n  background: none;\\n  z-index: 1;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%]:before {\\n  color: transparent;\\n  background: none;\\n  display: block;\\n  font-family: \\\"FontAwesome\\\";\\n  content: \\\"\\\\f073\\\";\\n  \\n\\n  width: 20px;\\n  height: 25px;\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  color: #999;\\n}\\n\\n.image-preview[_ngcontent-%COMP%] {\\n  height: 10rem;\\n  margin: 1rem 0;\\n  border-radius: 50%;\\n  border: 3px solid var(--main-color);\\n}\\n\\n.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 100%;\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-header {\\n  padding: clamp(0.38rem, 0.47vw + 0.28rem, 0.75rem);\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-items .p-dropdown-item {\\n  margin-bottom: clamp(0.38rem, 0.47vw + 0.28rem, 0.75rem);\\n}\\n\\n.country-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: clamp(0.75rem, 0.31vw + 0.69rem, 1rem);\\n  font-size: clamp(0.88rem, 0.16vw + 0.84rem, 1rem);\\n}\\n.country-item[_ngcontent-%COMP%]   .country-name[_ngcontent-%COMP%] {\\n  white-space: pre-line; \\n\\n}\\n.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  \\n\\n\\n\\n\\n\\n  \\n\\n  display: inline-block;\\n  vertical-align: middle;\\n}\\n.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  clip-path: circle();\\n}\\n.country-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 32px;\\n  margin-right: 0.5rem;\\n}\\n.country-item[_ngcontent-%COMP%]   img.smaller[_ngcontent-%COMP%] {\\n  width: 16px;\\n}\\n\\n.info-element[_ngcontent-%COMP%] {\\n  padding: 10px 0;\\n}\\n\\n.responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.contact-form-wrapper[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  background-image: linear-gradient(180deg, hsl(222, 92%, 90%) 0%, hsl(222, 92%, 91%) 30%, hsl(222, 92%, 91%) 51%, hsl(222, 92%, 92%) 64%, hsl(222, 92%, 93%) 72%, hsl(222, 92%, 94%) 79%, hsl(222, 91%, 94%) 85%, hsl(221, 91%, 95%) 90%, hsl(221, 90%, 96%) 95%, hsl(221, 89%, 96%) 100%);\\n  position: relative;\\n  z-index: 2;\\n}\\n.contact-form-wrapper[_ngcontent-%COMP%]   .dropdown-blue.p-dropdown.with-icon[_ngcontent-%COMP%] {\\n  padding: 0 !important;\\n}\\n.contact-form-wrapper[_ngcontent-%COMP%]   .alert-box[_ngcontent-%COMP%] {\\n  padding: 11px;\\n  font-stretch: normal;\\n  font-style: normal;\\n  line-height: normal;\\n  letter-spacing: 0.09px;\\n  text-align: left;\\n  color: var(--blue-9);\\n  background-image: linear-gradient(#ffffff, #e3ebfd);\\n  border-radius: 10px;\\n}\\n.contact-form-wrapper[_ngcontent-%COMP%]   .alert-box[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  opacity: 0.8;\\n  \\n\\n  \\n\\n  \\n\\n  font-size: 12px;\\n}\\n@media (min-width: 300px) and (max-width: 1599px) {\\n  .contact-form-wrapper[_ngcontent-%COMP%]   .alert-box[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    \\n\\n    font-size: 13px;\\n    \\n\\n    font-size: calc(12px + (14 - 12) * (100vw - 300px) / (1600 - 300)) !important;\\n  }\\n}\\n@media (min-width: 1600px) {\\n  .contact-form-wrapper[_ngcontent-%COMP%]   .alert-box[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n.contact-form-wrapper[_ngcontent-%COMP%]   .block-header[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 2;\\n  border-top-left-radius: 16px;\\n  border-top-right-radius: 16px;\\n  min-height: 3rem;\\n  box-shadow: 0px 3px 8px rgba(179, 179, 179, 0.96);\\n  border-radius: 16px;\\n}\\n.contact-form-wrapper[_ngcontent-%COMP%]   .block-header[_ngcontent-%COMP%]:before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  background-image: url(\\\"/assets/images/dashboard/freetrialheader.png\\\");\\n  background-size: cover;\\n  background-repeat: no-repeat;\\n  background-position: center;\\n  width: 100%;\\n  height: 100%;\\n  left: 0;\\n  top: 0;\\n  z-index: 1;\\n  border-radius: 16px;\\n}\\n.contact-form-wrapper[_ngcontent-%COMP%]   .surface-section[_ngcontent-%COMP%] {\\n  background-color: transparent !important;\\n}\\n.contact-form-wrapper[_ngcontent-%COMP%]   .side-heading[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  margin-bottom: 0;\\n  letter-spacing: 0.01em;\\n  text-align: center;\\n  color: #2e3d90;\\n  opacity: 0.33;\\n}\\n.contact-form-wrapper[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  top: 18px;\\n  z-index: 4;\\n  height: 24px;\\n  width: 24px;\\n  background: url(\\\"/assets/images/dashboard/student.svg\\\") no-repeat top left;\\n}\\n.contact-form-wrapper[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n}\\n.contact-form-wrapper[_ngcontent-%COMP%]   .icon.mail-icon[_ngcontent-%COMP%] {\\n  background: url(\\\"/assets/images/dashboard/mail-blue-icon.svg\\\") no-repeat center;\\n}\\n.contact-form-wrapper[_ngcontent-%COMP%]   .icon.globe-icon[_ngcontent-%COMP%] {\\n  background: url(\\\"/assets/images/dashboard/globe-blue-icon.svg\\\") no-repeat top left;\\n}\\n.contact-form-wrapper[_ngcontent-%COMP%]   .icon.birth-icon[_ngcontent-%COMP%] {\\n  background: url(\\\"/assets/images/dashboard/birth-blue-icon.svg\\\") no-repeat top left;\\n}\\n.contact-form-wrapper[_ngcontent-%COMP%]   .icon.availability-icon[_ngcontent-%COMP%] {\\n  background: url(\\\"/assets/images/dashboard/availability-blue-icon.svg\\\") no-repeat top left;\\n}\\n.contact-form-wrapper[_ngcontent-%COMP%]   .icon.timezone-icon[_ngcontent-%COMP%] {\\n  background: url(\\\"/assets/images/dashboard/timezone-blue-icon.svg\\\") no-repeat top left;\\n}\\n.contact-form-wrapper[_ngcontent-%COMP%]   .textarea-bg[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 100%;\\n  box-sizing: border-box;\\n  background: linear-gradient(#ccd6ff 0%, rgba(56, 115, 244, 0.2) 100%);\\n  height: 150px;\\n  border-radius: 10px;\\n  resize: none;\\n}\\n.contact-form-wrapper[_ngcontent-%COMP%]   .textarea-bg[_ngcontent-%COMP%]::placeholder {\\n  opacity: 0.5;\\n}\\n.contact-form-wrapper[_ngcontent-%COMP%]   .info-bubble-blue-bg[_ngcontent-%COMP%] {\\n  background-image: url(\\\"/assets/images/dashboard/trial-form-bubble-blue.svg\\\");\\n  background-size: 100%;\\n  background-repeat: no-repeat;\\n  height: 185px;\\n  width: 216px;\\n}\\n.contact-form-wrapper[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: -1.1rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["UntypedFormControl", "UntypedFormGroup", "Validators", "SubSink", "DashboardContactUsFormComponent", "constructor", "generalService", "studentTrialService", "teacherApplicationService", "toast", "subs", "form", "studentOptions", "selectedMltGroup", "formSubmitted", "selectedPhoneCode", "mltLanguages", "countries", "phoneCodes", "getPhoneCodes", "languages", "ngOnInit", "getCountries", "sink", "getLingoTeachingLanguages", "subscribe", "res", "initializeForm", "ngOnDestroy", "unsubscribe", "firstName", "validators", "required", "lastName", "phoneCode", "contactNumber", "studentType", "email", "wantToLearnLanguage", "message", "validate", "invalid", "control", "Object", "keys", "controls", "<PERSON><PERSON><PERSON><PERSON>ched", "submit", "valid", "fetch", "then", "response", "json", "jsonResponse", "console", "log", "country", "postContactUs", "prepareDataBeforeSubmit", "showSuccessMessage", "reset", "onPhoneCodeChange", "event", "setTimeout", "value", "findCountryImage", "isInvalidControl", "controlName", "get", "touched", "isValidControl", "isInvalid", "dirty", "formattedData", "userData", "phone", "code", "course", "setShowToastmessage", "severity", "summary", "detail", "_", "i0", "ɵɵdirectiveInject", "i1", "GeneralService", "i2", "StudentTrialService", "i3", "TeacherApplicationService", "i4", "ToastService", "_2", "selectors", "decls", "vars", "consts", "template", "DashboardContactUsFormComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "DashboardContactUsFormComponent_Template_app_prime_input_dropdown_valueSelected_36_listener", "$event", "ɵɵrestoreView", "_r1", "ɵɵresetView", "DashboardContactUsFormComponent_Template_button_click_63_listener", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction2", "_c0"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\dashboard\\dashboard-contact-us-form\\dashboard-contact-us-form.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\dashboard\\dashboard-contact-us-form\\dashboard-contact-us-form.component.html"], "sourcesContent": ["import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';\r\nimport { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\r\nimport { Country, Language, Timezone } from 'src/app/core/models/general.model';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { StudentTrialService } from 'src/app/core/services/student-trial.service';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { TeacherApplicationService } from 'src/app/core/services/teacher-application.service';\r\nimport { SubSink } from 'subsink';\r\n\r\n@Component({\r\n  selector: 'app-dashboard-contact-us-form',\r\n  templateUrl: './dashboard-contact-us-form.component.html',\r\n  styleUrls: ['./dashboard-contact-us-form.component.scss']\r\n})\r\nexport class DashboardContactUsFormComponent implements OnInit {\r\n  private subs = new SubSink();\r\n  public form: UntypedFormGroup = new UntypedFormGroup({});\r\n\r\n  studentOptions = ['Yes', 'No'];\r\n\r\n  selectedMltGroup = null;\r\n  formSubmitted = false;\r\n  public selectedTimezone?: Timezone;\r\n  public selectedLanguage?: Language;\r\n  public selectedPhoneCode: any | undefined = {} as any | undefined;\r\n  mltLanguages: Language[] = [] as Language[];\r\n  countries: Country[] = [] as Country[];\r\n  public phoneCodes: any[] = this.generalService.getPhoneCodes();\r\n  public languages: Language[] = this.generalService.languages;\r\n\r\n  constructor(\r\n    private generalService: GeneralService,\r\n    private studentTrialService: StudentTrialService,\r\n    private teacherApplicationService: TeacherApplicationService,\r\n    private toast: ToastService,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.countries = this.generalService.getCountries();\r\n    this.mltLanguages = this.generalService.mltLanguages;\r\n\r\n    this.subs.sink = this.teacherApplicationService.getLingoTeachingLanguages().subscribe((res)=>{\r\n      this.mltLanguages = res;\r\n    });\r\n    this.initializeForm();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  initializeForm() {\r\n    this.form = new UntypedFormGroup({\r\n      firstName: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      lastName: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      phoneCode: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      contactNumber: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      studentType: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      email: new UntypedFormControl(null, {\r\n        validators:  [\r\n          Validators.required,\r\n          Validators.email\r\n        ]\r\n      }),\r\n      wantToLearnLanguage: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      message: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n    });\r\n\r\n  }\r\n  private validate(): void {\r\n    if (this.form.invalid) {\r\n      for (const control of Object.keys(this.form.controls)) {\r\n        this.form.controls[control].markAsTouched();\r\n      }\r\n      return;\r\n    }\r\n\r\n  }\r\n  submit() {\r\n    this.validate();\r\n    this.formSubmitted = true;\r\n    if (!this.form.valid) {\r\n        return false;\r\n    }\r\n    fetch(\"https://ipinfo.io/json?token=adb3400371759e\").then(\r\n      (response) => response.json()\r\n    ).then(\r\n      (jsonResponse) => console.log(jsonResponse.country)\r\n    )\r\n      \r\n    this.subs.sink = this.studentTrialService.postContactUs(this.prepareDataBeforeSubmit()).subscribe((res) => {\r\n      if (res) {\r\n        this.showSuccessMessage();\r\n        this.form.reset();\r\n      }\r\n    });\r\n    return true;\r\n  }\r\n\r\n  onPhoneCodeChange(event: any) {\r\n    setTimeout(() => {\r\n      this.selectedPhoneCode = event.value;\r\n    }, 100);\r\n  }\r\n\r\n  findCountryImage(phoneCode: string) {\r\n    return this.generalService.findCountryImage(phoneCode);\r\n  }\r\n\r\n  isInvalidControl(controlName: string): boolean {\r\n    const control = this.form.get(controlName);\r\n    return control! && (control!.invalid && (control!.touched || this.formSubmitted));\r\n  }\r\n\r\n  isValidControl(controlName: string): boolean {\r\n    const control = this.form.get(controlName);\r\n    return control! && control.valid;\r\n  }\r\n\r\n  isInvalid() {\r\n    return this.form.dirty && this.form.invalid;\r\n  }\r\n\r\n  private prepareDataBeforeSubmit() {\r\n    const studentType = this.form.value.studentType === 'Yes' ? 'New Student' : 'Old Student';\r\n    const formattedData = {\r\n      userData: {\r\n        firstName: this.form.value.firstName,\r\n        lastName: this.form.value.lastName,\r\n        email: this.form.value.email,\r\n        phone: `${this.selectedPhoneCode.code} ${this.form.value.contactNumber}`\r\n      },\r\n      course: this.form.value.wantToLearnLanguage,\r\n      studentType: studentType,\r\n      message: this.form.value.message\r\n    }\r\n    return formattedData;\r\n  }\r\n\r\n  showSuccessMessage() {\r\n    this.toast.setShowToastmessage({\r\n      severity: 'success',\r\n      summary: '',\r\n      detail: 'Your message was successfully sent.'\r\n    });\r\n  }\r\n}\r\n", "<div class=\"contact-form-wrapper mb-5\" #contactFormWrapper>\r\n        <div class=\"block-header justify-content-center  lg:flex\">\r\n            <span class=\"block-title\">\r\n                <span class=\"text-0 text-center font-xl\">Contact us</span>\r\n            </span>\r\n        </div>\r\n        <div class=\"surface-section px-2 lg:px-4 pb-5 pt-3\">\r\n            <form [formGroup]=\"form\">\r\n                <div class=\"grid\">\r\n                    <div class=\"col-12 lg:col-2 flex align-items-center justify-content-center\">\r\n                        <h2 class=\"side-heading uppercase fl-typo text-center font-base\">Fill in the form</h2>                       \r\n                    </div>\r\n                    <div class=\"col-12 lg:col-10 pb-1\">\r\n                        <div class=\"p-fluid grid\">\r\n                            <div class=\"field mb-0 col-12 md:col-6 font-sm\">\r\n                                <label>* First Name</label>\r\n                                <span class=\"p-float-label p-input-icon-left\">\r\n                                    <i class=\"pi icon\"></i>\r\n                                    <input type=\"text\" class=\"input-blue rounded-less white-bg\"\r\n                                    placeholder=\"First Name\"\r\n                                    [ngClass]=\"{ 'ng-invalid ng-dirty my-invalid-input': isInvalidControl('firstName'), 'my-valid-input': isValidControl('firstName') }\"\r\n                                        formControlName=\"firstName\" pInputText>\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"field mb-0 col-12 md:col-6 font-sm\">\r\n                                <label>* Last Name</label>\r\n                                <span class=\"p-float-label p-input-icon-left\">\r\n                                    <i class=\"pi icon\"></i>\r\n                                    <input type=\"text\" class=\"input-blue rounded-less white-bg\"\r\n                                    placeholder=\"*Last Name\"\r\n                                    [ngClass]=\"{ 'ng-invalid ng-dirty my-invalid-input': isInvalidControl('lastName'), 'my-valid-input': isValidControl('lastName') }\"\r\n                                        formControlName=\"lastName\" pInputText>\r\n                                </span>\r\n                            </div>\r\n\r\n\r\n                            <div class=\"field mb-0 col-12 md:col-6 font-sm\">\r\n                                <label>* Email</label>\r\n                                <span class=\"p-float-label p-input-icon-left\">\r\n                                    <i class=\"pi icon mail-icon\"></i>\r\n                                    <input type=\"text\" class=\"input-blue rounded-less white-bg\" pInputText\r\n                                        formControlName=\"email\"\r\n                                        placeholder=\"Email\"\r\n                                        [ngClass]=\"{ 'ng-invalid ng-dirty my-invalid-input': isInvalidControl('email'), 'my-valid-input': isValidControl('email') }\">\r\n                                </span>\r\n                            </div>\r\n\r\n                            <div class=\"field mb-0 col-12 md:col-6 font-sm\">\r\n                                <label>* Phone Number</label>\r\n                                <span class=\" flex sm:flex\">\r\n                                    <app-prime-input-dropdown \r\n                                    optionLabel=\"code\"\r\n                                    [parentForm]=\"form\"\r\n                                    [countries]=\"phoneCodes\"\r\n                                    filterBy=\"code\"\r\n                                    [filter]=\"true\"\r\n                                    [inputName]=\"'phoneCode'\"\r\n                                    [placeholder]=\"''\"\r\n                                    [isInvalidControl]=\"isInvalidControl('phoneCode')\"\r\n                                    [isValidControl]=\"isValidControl('phoneCode')\"\r\n                                    [withFlags]=\"true\"\r\n                                    flagFilter=\"code\"\r\n                                    [selectedItemValue]=\"selectedPhoneCode\"\r\n                                    templateValue=\"code\"\r\n                                    styleClass=\"dropdown-blue white-bg phone-code-input tiny rounded-less small-dropdown-items\"\r\n                                    (valueSelected)=\"onPhoneCodeChange($event)\">\r\n                                    </app-prime-input-dropdown>\r\n                                <span class=\"p-float-label w-full\">\r\n                                    <!-- <i class=\"pi icon\"></i> -->\r\n                                    <input type=\"text\" class=\"input-blue rounded-less white-bg no-radius-left\"\r\n                                    placeholder=\"Phone Number\"\r\n                                        [ngClass]=\"{ 'ng-invalid ng-dirty my-invalid-input': isInvalidControl('contactNumber'), 'my-valid-input': isValidControl('contactNumber') }\"\r\n                                        required pInputText formControlName=\"contactNumber\">\r\n                                    <!-- <label for=\"inputtext-right\">Phone</label> -->\r\n                                </span>\r\n                                </span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"grid\">\r\n                    <div class=\"col-12 lg:col-2 flex align-items-center justify-content-center\">\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-10\">\r\n                        <div class=\"p-fluid grid\">\r\n                            <div class=\"field mb-0 col-12 md:col-6 font-sm no-padding\">\r\n                                <label>* I want to learn/I am learning</label>\r\n                                    <app-prime-input-dropdown\r\n                                    [parentForm]=\"form\"\r\n                                    [countries]=\"mltLanguages\"\r\n                                    [inputName]=\"'wantToLearnLanguage'\"\r\n                                    [placeholder]=\"'I want to learn/I am learning'\"\r\n                                    [isInvalidControl]=\"isInvalidControl('wantToLearnLanguage')\"\r\n                                    [isValidControl]=\"isValidControl('wantToLearnLanguage')\">\r\n                                    </app-prime-input-dropdown>\r\n                            </div>\r\n                            <div class=\"field mb-0 col-12 md:col-6 font-sm no-padding\">\r\n                                <label>* Are you a new student?</label>\r\n                                <span class=\"p-float-label\">\r\n                                    <app-prime-input-dropdown \r\n                                    [parentForm]=\"form\"\r\n                                    [countries]=\"studentOptions\"\r\n                                    filterBy=\"name\"\r\n                                    [inputName]=\"'studentType'\"\r\n                                    [placeholder]=\"'Are you a new student?'\"\r\n                                    [isInvalidControl]=\"isInvalidControl('studentType')\"\r\n                                    [isValidControl]=\"isValidControl('studentType')\">\r\n                                    </app-prime-input-dropdown>\r\n                                </span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid relative\">\r\n                    <div class=\"col-12 lg:col-2 flex align-items-center justify-content-center\"></div>\r\n                    <div class=\"col-12 lg:col-10\">\r\n                        <div class=\"p-fluid grid font-sm\">\r\n                            <label>* Message</label>\r\n                            <div class=\"field mb-0 col-12 md:col-12\">\r\n                                <textarea class=\"input-blue textarea-bg\" formControlName=\"message\" rows=\"12\"\r\n                                [ngClass]=\"{ 'ng-invalid ng-dirty my-invalid-input': isInvalidControl('message'), 'my-valid-input': isValidControl('message') }\"\r\n                                ></textarea>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                </div>\r\n            </form>\r\n        </div>\r\n        <div class=\"\">\r\n            <div class=\"fluid grid\">\r\n                <div class=\"field mb-0 col-12 flex justify-content-center\">\r\n                    <button (click)=\"submit()\" pRipple pbutton label=\"Send\" icon=\"pi pi-discord\"\r\n                        class=\"element ripple p-button-raised p-button-rounded white-space-nowrap p-button btn-gradient-blue p-component w-10rem font-lg flex justify-content-center\">\r\n                        <span class=\"p-button-icon p-button-icon-right\" aria-hidden=\"true\"></span>\r\n                        Send \r\n                        <img src=\"/assets/icons/forward-arrow.svg\" width=\"24\" class=\"ml-2\"/>\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        </div>\r\n</div>"], "mappings": "AACA,SAASA,kBAAkB,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,gBAAgB;AAMjF,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;;;AAOjC,OAAM,MAAOC,+BAA+B;EAgB1CC,YACUC,cAA8B,EAC9BC,mBAAwC,EACxCC,yBAAoD,EACpDC,KAAmB;IAHnB,KAAAH,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,yBAAyB,GAAzBA,yBAAyB;IACzB,KAAAC,KAAK,GAALA,KAAK;IAnBP,KAAAC,IAAI,GAAG,IAAIP,OAAO,EAAE;IACrB,KAAAQ,IAAI,GAAqB,IAAIV,gBAAgB,CAAC,EAAE,CAAC;IAExD,KAAAW,cAAc,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC;IAE9B,KAAAC,gBAAgB,GAAG,IAAI;IACvB,KAAAC,aAAa,GAAG,KAAK;IAGd,KAAAC,iBAAiB,GAAoB,EAAqB;IACjE,KAAAC,YAAY,GAAe,EAAgB;IAC3C,KAAAC,SAAS,GAAc,EAAe;IAC/B,KAAAC,UAAU,GAAU,IAAI,CAACZ,cAAc,CAACa,aAAa,EAAE;IACvD,KAAAC,SAAS,GAAe,IAAI,CAACd,cAAc,CAACc,SAAS;EAOxD;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACJ,SAAS,GAAG,IAAI,CAACX,cAAc,CAACgB,YAAY,EAAE;IACnD,IAAI,CAACN,YAAY,GAAG,IAAI,CAACV,cAAc,CAACU,YAAY;IAEpD,IAAI,CAACN,IAAI,CAACa,IAAI,GAAG,IAAI,CAACf,yBAAyB,CAACgB,yBAAyB,EAAE,CAACC,SAAS,CAAEC,GAAG,IAAG;MAC3F,IAAI,CAACV,YAAY,GAAGU,GAAG;IACzB,CAAC,CAAC;IACF,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAClB,IAAI,CAACmB,WAAW,EAAE;EACzB;EAEAF,cAAcA,CAAA;IACZ,IAAI,CAAChB,IAAI,GAAG,IAAIV,gBAAgB,CAAC;MAC/B6B,SAAS,EAAE,IAAI9B,kBAAkB,CAAC,IAAI,EAAE;QACtC+B,UAAU,EAAE,CAAC7B,UAAU,CAAC8B,QAAQ;OACjC,CAAC;MACFC,QAAQ,EAAE,IAAIjC,kBAAkB,CAAC,IAAI,EAAE;QACrC+B,UAAU,EAAE,CAAC7B,UAAU,CAAC8B,QAAQ;OACjC,CAAC;MACFE,SAAS,EAAE,IAAIlC,kBAAkB,CAAC,IAAI,EAAE;QACtC+B,UAAU,EAAE,CAAC7B,UAAU,CAAC8B,QAAQ;OACjC,CAAC;MACFG,aAAa,EAAE,IAAInC,kBAAkB,CAAC,IAAI,EAAE;QAC1C+B,UAAU,EAAE,CAAC7B,UAAU,CAAC8B,QAAQ;OACjC,CAAC;MACFI,WAAW,EAAE,IAAIpC,kBAAkB,CAAC,IAAI,EAAE;QACxC+B,UAAU,EAAE,CAAC7B,UAAU,CAAC8B,QAAQ;OACjC,CAAC;MACFK,KAAK,EAAE,IAAIrC,kBAAkB,CAAC,IAAI,EAAE;QAClC+B,UAAU,EAAG,CACX7B,UAAU,CAAC8B,QAAQ,EACnB9B,UAAU,CAACmC,KAAK;OAEnB,CAAC;MACFC,mBAAmB,EAAE,IAAItC,kBAAkB,CAAC,IAAI,EAAE;QAChD+B,UAAU,EAAE,CAAC7B,UAAU,CAAC8B,QAAQ;OACjC,CAAC;MACFO,OAAO,EAAE,IAAIvC,kBAAkB,CAAC,IAAI,EAAE;QACpC+B,UAAU,EAAE,CAAC7B,UAAU,CAAC8B,QAAQ;OACjC;KACF,CAAC;EAEJ;EACQQ,QAAQA,CAAA;IACd,IAAI,IAAI,CAAC7B,IAAI,CAAC8B,OAAO,EAAE;MACrB,KAAK,MAAMC,OAAO,IAAIC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjC,IAAI,CAACkC,QAAQ,CAAC,EAAE;QACrD,IAAI,CAAClC,IAAI,CAACkC,QAAQ,CAACH,OAAO,CAAC,CAACI,aAAa,EAAE;MAC7C;MACA;IACF;EAEF;EACAC,MAAMA,CAAA;IACJ,IAAI,CAACP,QAAQ,EAAE;IACf,IAAI,CAAC1B,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC,IAAI,CAACH,IAAI,CAACqC,KAAK,EAAE;MAClB,OAAO,KAAK;IAChB;IACAC,KAAK,CAAC,6CAA6C,CAAC,CAACC,IAAI,CACtDC,QAAQ,IAAKA,QAAQ,CAACC,IAAI,EAAE,CAC9B,CAACF,IAAI,CACHG,YAAY,IAAKC,OAAO,CAACC,GAAG,CAACF,YAAY,CAACG,OAAO,CAAC,CACpD;IAED,IAAI,CAAC9C,IAAI,CAACa,IAAI,GAAG,IAAI,CAAChB,mBAAmB,CAACkD,aAAa,CAAC,IAAI,CAACC,uBAAuB,EAAE,CAAC,CAACjC,SAAS,CAAEC,GAAG,IAAI;MACxG,IAAIA,GAAG,EAAE;QACP,IAAI,CAACiC,kBAAkB,EAAE;QACzB,IAAI,CAAChD,IAAI,CAACiD,KAAK,EAAE;MACnB;IACF,CAAC,CAAC;IACF,OAAO,IAAI;EACb;EAEAC,iBAAiBA,CAACC,KAAU;IAC1BC,UAAU,CAAC,MAAK;MACd,IAAI,CAAChD,iBAAiB,GAAG+C,KAAK,CAACE,KAAK;IACtC,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,gBAAgBA,CAAC/B,SAAiB;IAChC,OAAO,IAAI,CAAC5B,cAAc,CAAC2D,gBAAgB,CAAC/B,SAAS,CAAC;EACxD;EAEAgC,gBAAgBA,CAACC,WAAmB;IAClC,MAAMzB,OAAO,GAAG,IAAI,CAAC/B,IAAI,CAACyD,GAAG,CAACD,WAAW,CAAC;IAC1C,OAAOzB,OAAQ,IAAKA,OAAQ,CAACD,OAAO,KAAKC,OAAQ,CAAC2B,OAAO,IAAI,IAAI,CAACvD,aAAa,CAAE;EACnF;EAEAwD,cAAcA,CAACH,WAAmB;IAChC,MAAMzB,OAAO,GAAG,IAAI,CAAC/B,IAAI,CAACyD,GAAG,CAACD,WAAW,CAAC;IAC1C,OAAOzB,OAAQ,IAAIA,OAAO,CAACM,KAAK;EAClC;EAEAuB,SAASA,CAAA;IACP,OAAO,IAAI,CAAC5D,IAAI,CAAC6D,KAAK,IAAI,IAAI,CAAC7D,IAAI,CAAC8B,OAAO;EAC7C;EAEQiB,uBAAuBA,CAAA;IAC7B,MAAMtB,WAAW,GAAG,IAAI,CAACzB,IAAI,CAACqD,KAAK,CAAC5B,WAAW,KAAK,KAAK,GAAG,aAAa,GAAG,aAAa;IACzF,MAAMqC,aAAa,GAAG;MACpBC,QAAQ,EAAE;QACR5C,SAAS,EAAE,IAAI,CAACnB,IAAI,CAACqD,KAAK,CAAClC,SAAS;QACpCG,QAAQ,EAAE,IAAI,CAACtB,IAAI,CAACqD,KAAK,CAAC/B,QAAQ;QAClCI,KAAK,EAAE,IAAI,CAAC1B,IAAI,CAACqD,KAAK,CAAC3B,KAAK;QAC5BsC,KAAK,EAAE,GAAG,IAAI,CAAC5D,iBAAiB,CAAC6D,IAAI,IAAI,IAAI,CAACjE,IAAI,CAACqD,KAAK,CAAC7B,aAAa;OACvE;MACD0C,MAAM,EAAE,IAAI,CAAClE,IAAI,CAACqD,KAAK,CAAC1B,mBAAmB;MAC3CF,WAAW,EAAEA,WAAW;MACxBG,OAAO,EAAE,IAAI,CAAC5B,IAAI,CAACqD,KAAK,CAACzB;KAC1B;IACD,OAAOkC,aAAa;EACtB;EAEAd,kBAAkBA,CAAA;IAChB,IAAI,CAAClD,KAAK,CAACqE,mBAAmB,CAAC;MAC7BC,QAAQ,EAAE,SAAS;MACnBC,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE;KACT,CAAC;EACJ;EAAC,QAAAC,CAAA,G;qBAjJU9E,+BAA+B,EAAA+E,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,yBAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA/BzF,+BAA+B;IAAA0F,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,yCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QCX5BjB,EAHhB,CAAAmB,cAAA,gBAA2D,aACO,cAC5B,cACmB;QAAAnB,EAAA,CAAAoB,MAAA,iBAAU;QAE3DpB,EAF2D,CAAAqB,YAAA,EAAO,EACvD,EACL;QAKUrB,EAJhB,CAAAmB,cAAA,aAAoD,cACvB,aACH,aAC8D,aACP;QAAAnB,EAAA,CAAAoB,MAAA,wBAAgB;QACrFpB,EADqF,CAAAqB,YAAA,EAAK,EACpF;QAIMrB,EAHZ,CAAAmB,cAAA,eAAmC,eACL,eAC0B,aACrC;QAAAnB,EAAA,CAAAoB,MAAA,oBAAY;QAAApB,EAAA,CAAAqB,YAAA,EAAQ;QAC3BrB,EAAA,CAAAmB,cAAA,gBAA8C;QAE1CnB,EADA,CAAAsB,SAAA,aAAuB,iBAIoB;QAEnDtB,EADI,CAAAqB,YAAA,EAAO,EACL;QAEFrB,EADJ,CAAAmB,cAAA,eAAgD,aACrC;QAAAnB,EAAA,CAAAoB,MAAA,mBAAW;QAAApB,EAAA,CAAAqB,YAAA,EAAQ;QAC1BrB,EAAA,CAAAmB,cAAA,gBAA8C;QAE1CnB,EADA,CAAAsB,SAAA,aAAuB,iBAImB;QAElDtB,EADI,CAAAqB,YAAA,EAAO,EACL;QAIFrB,EADJ,CAAAmB,cAAA,eAAgD,aACrC;QAAAnB,EAAA,CAAAoB,MAAA,eAAO;QAAApB,EAAA,CAAAqB,YAAA,EAAQ;QACtBrB,EAAA,CAAAmB,cAAA,gBAA8C;QAE1CnB,EADA,CAAAsB,SAAA,aAAiC,iBAIgG;QAEzItB,EADI,CAAAqB,YAAA,EAAO,EACL;QAGFrB,EADJ,CAAAmB,cAAA,eAAgD,aACrC;QAAAnB,EAAA,CAAAoB,MAAA,sBAAc;QAAApB,EAAA,CAAAqB,YAAA,EAAQ;QAEzBrB,EADJ,CAAAmB,cAAA,gBAA4B,oCAgBoB;QAA5CnB,EAAA,CAAAuB,UAAA,2BAAAC,4FAAAC,MAAA;UAAAzB,EAAA,CAAA0B,aAAA,CAAAC,GAAA;UAAA,OAAA3B,EAAA,CAAA4B,WAAA,CAAiBV,GAAA,CAAAxC,iBAAA,CAAA+C,MAAA,CAAyB;QAAA,EAAC;QAC3CzB,EAAA,CAAAqB,YAAA,EAA2B;QAC/BrB,EAAA,CAAAmB,cAAA,gBAAmC;QAE/BnB,EAAA,CAAAsB,SAAA,iBAGwD;QAO5EtB,EALgB,CAAAqB,YAAA,EAAO,EACA,EACL,EACJ,EACJ,EACJ;QACNrB,EAAA,CAAAmB,cAAA,cAAkB;QACdnB,EAAA,CAAAsB,SAAA,cACM;QAIMtB,EAHZ,CAAAmB,cAAA,eAA8B,eACA,eACqC,aAChD;QAAAnB,EAAA,CAAAoB,MAAA,uCAA+B;QAAApB,EAAA,CAAAqB,YAAA,EAAQ;QAC1CrB,EAAA,CAAAsB,SAAA,oCAO2B;QACnCtB,EAAA,CAAAqB,YAAA,EAAM;QAEFrB,EADJ,CAAAmB,cAAA,eAA2D,aAChD;QAAAnB,EAAA,CAAAoB,MAAA,gCAAwB;QAAApB,EAAA,CAAAqB,YAAA,EAAQ;QACvCrB,EAAA,CAAAmB,cAAA,gBAA4B;QACxBnB,EAAA,CAAAsB,SAAA,oCAQ2B;QAK/CtB,EAJgB,CAAAqB,YAAA,EAAO,EACL,EACJ,EACJ,EACJ;QAENrB,EAAA,CAAAmB,cAAA,eAA2B;QACvBnB,EAAA,CAAAsB,SAAA,cAAkF;QAG1EtB,EAFR,CAAAmB,cAAA,eAA8B,eACQ,aACvB;QAAAnB,EAAA,CAAAoB,MAAA,iBAAS;QAAApB,EAAA,CAAAqB,YAAA,EAAQ;QACxBrB,EAAA,CAAAmB,cAAA,eAAyC;QACrCnB,EAAA,CAAAsB,SAAA,oBAEY;QAOpCtB,EANoB,CAAAqB,YAAA,EAAM,EACJ,EACJ,EAEJ,EACH,EACL;QAIMrB,EAHZ,CAAAmB,cAAA,eAAc,eACc,eACuC,kBAE2G;QAD1JnB,EAAA,CAAAuB,UAAA,mBAAAM,kEAAA;UAAA7B,EAAA,CAAA0B,aAAA,CAAAC,GAAA;UAAA,OAAA3B,EAAA,CAAA4B,WAAA,CAASV,GAAA,CAAAtD,MAAA,EAAQ;QAAA,EAAC;QAEtBoC,EAAA,CAAAsB,SAAA,gBAA0E;QAC1EtB,EAAA,CAAAoB,MAAA,cACA;QAAApB,EAAA,CAAAsB,SAAA,eAAoE;QAK5FtB,EAJoB,CAAAqB,YAAA,EAAS,EACP,EACJ,EACJ,EACR;;;QAvIYrB,EAAA,CAAA8B,SAAA,GAAkB;QAAlB9B,EAAA,CAAA+B,UAAA,cAAAb,GAAA,CAAA1F,IAAA,CAAkB;QAaAwE,EAAA,CAAA8B,SAAA,IAAoI;QAApI9B,EAAA,CAAA+B,UAAA,YAAA/B,EAAA,CAAAgC,eAAA,KAAAC,GAAA,EAAAf,GAAA,CAAAnC,gBAAA,eAAAmC,GAAA,CAAA/B,cAAA,eAAoI;QAUpIa,EAAA,CAAA8B,SAAA,GAAkI;QAAlI9B,EAAA,CAAA+B,UAAA,YAAA/B,EAAA,CAAAgC,eAAA,KAAAC,GAAA,EAAAf,GAAA,CAAAnC,gBAAA,cAAAmC,GAAA,CAAA/B,cAAA,cAAkI;QAa9Ha,EAAA,CAAA8B,SAAA,GAA4H;QAA5H9B,EAAA,CAAA+B,UAAA,YAAA/B,EAAA,CAAAgC,eAAA,KAAAC,GAAA,EAAAf,GAAA,CAAAnC,gBAAA,WAAAmC,GAAA,CAAA/B,cAAA,WAA4H;QAShIa,EAAA,CAAA8B,SAAA,GAAmB;QAUnB9B,EAVA,CAAA+B,UAAA,eAAAb,GAAA,CAAA1F,IAAA,CAAmB,cAAA0F,GAAA,CAAAnF,UAAA,CACK,gBAET,0BACU,mBACP,qBAAAmF,GAAA,CAAAnC,gBAAA,cACgC,mBAAAmC,GAAA,CAAA/B,cAAA,cACJ,mBAC5B,sBAAA+B,GAAA,CAAAtF,iBAAA,CAEqB;QASnCoE,EAAA,CAAA8B,SAAA,GAA4I;QAA5I9B,EAAA,CAAA+B,UAAA,YAAA/B,EAAA,CAAAgC,eAAA,KAAAC,GAAA,EAAAf,GAAA,CAAAnC,gBAAA,mBAAAmC,GAAA,CAAA/B,cAAA,mBAA4I;QAiBhJa,EAAA,CAAA8B,SAAA,GAAmB;QAKnB9B,EALA,CAAA+B,UAAA,eAAAb,GAAA,CAAA1F,IAAA,CAAmB,cAAA0F,GAAA,CAAArF,YAAA,CACO,oCACS,gDACY,qBAAAqF,GAAA,CAAAnC,gBAAA,wBACa,mBAAAmC,GAAA,CAAA/B,cAAA,wBACJ;QAOxDa,EAAA,CAAA8B,SAAA,GAAmB;QAMnB9B,EANA,CAAA+B,UAAA,eAAAb,GAAA,CAAA1F,IAAA,CAAmB,cAAA0F,GAAA,CAAAzF,cAAA,CACS,4BAED,yCACa,qBAAAyF,GAAA,CAAAnC,gBAAA,gBACY,mBAAAmC,GAAA,CAAA/B,cAAA,gBACJ;QAepDa,EAAA,CAAA8B,SAAA,GAAgI;QAAhI9B,EAAA,CAAA+B,UAAA,YAAA/B,EAAA,CAAAgC,eAAA,KAAAC,GAAA,EAAAf,GAAA,CAAAnC,gBAAA,aAAAmC,GAAA,CAAA/B,cAAA,aAAgI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}