{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ChatComponent } from './chat.component';\nimport { TopMenuContainerLayoutComponent } from 'src/app/shared/layout/top-menu-container-layout/top-menu-container-layout.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: TopMenuContainerLayoutComponent,\n  children: [{\n    path: '',\n    component: ChatComponent\n  }]\n}];\nexport class ChatRoutingModule {\n  static #_ = this.ɵfac = function ChatRoutingModule_Factory(t) {\n    return new (t || ChatRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ChatRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ChatRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "ChatComponent", "TopMenuContainerLayoutComponent", "routes", "path", "component", "children", "ChatRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\chat\\chat-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { ChatComponent } from './chat.component';\r\nimport { TopMenuContainerLayoutComponent } from 'src/app/shared/layout/top-menu-container-layout/top-menu-container-layout.component';\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: TopMenuContainerLayoutComponent,\r\n  children: [\r\n    {\r\n      path: '',\r\n      component: ChatComponent,\r\n    },\r\n  ]\r\n},\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class ChatRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,+BAA+B,QAAQ,qFAAqF;;;AAErI,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEH,+BAA+B;EACtDI,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,EAAE;IACRC,SAAS,EAAEJ;GACZ;CAEJ,CACA;AAMD,OAAM,MAAOM,iBAAiB;EAAA,QAAAC,CAAA,G;qBAAjBD,iBAAiB;EAAA;EAAA,QAAAE,EAAA,G;UAAjBF;EAAiB;EAAA,QAAAG,EAAA,G;cAHlBV,YAAY,CAACW,QAAQ,CAACR,MAAM,CAAC,EAC7BH,YAAY;EAAA;;;2EAEXO,iBAAiB;IAAAK,OAAA,GAAAC,EAAA,CAAAb,YAAA;IAAAc,OAAA,GAFlBd,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}