{"ast": null, "code": "import { map } from \"rxjs/operators\";\nimport { TeacherStatus } from \"../models/teacher.model\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport let ProfileGuard = /*#__PURE__*/(() => {\n  class ProfileGuard {\n    constructor(authService, router) {\n      this.authService = authService;\n      this.router = router;\n    }\n    canActivate(route) {\n      const user = this.authService.getLoggedInUser();\n      const userRole = this.authService.getUserRole();\n      // checks if the user role is 'teacher' and their status is 'active'.\n      // If both conditions are true, it returns true. Otherwise, it calls the method checkTeacherAccessApproval().\n      if (userRole && userRole.toLowerCase() === 'teacher') {\n        if (user.status.toLowerCase() === 'active') {\n          return true;\n        }\n        return this.checkTeacherAccessApproval();\n      }\n      // If the user role is not 'teacher', it simply returns true.\n      else {\n        return true;\n      }\n    }\n    checkTeacherAccessApproval() {\n      return this.authService.getTeacherStatus().pipe(map(data => {\n        if (data.status === TeacherStatus.APPROVED) {\n          return true;\n        } else {\n          this.authService.setDisabledProfileRoutes(true);\n          this.router.navigateByUrl('/user-profile/settings', {\n            replaceUrl: true\n          });\n          return false;\n        }\n      }));\n    }\n    static #_ = this.ɵfac = function ProfileGuard_Factory(t) {\n      return new (t || ProfileGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ProfileGuard,\n      factory: ProfileGuard.ɵfac\n    });\n  }\n  return ProfileGuard;\n})();", "map": {"version": 3, "names": ["map", "TeacherStatus", "Profile<PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "route", "user", "getLoggedInUser", "userRole", "getUserRole", "toLowerCase", "status", "checkTeacherAccessApproval", "getTeacherStatus", "pipe", "data", "APPROVED", "setDisabledProfileRoutes", "navigateByUrl", "replaceUrl", "_", "i0", "ɵɵinject", "i1", "AuthService", "i2", "Router", "_2", "factory", "ɵfac"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\guards\\profile.guard.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\";\r\nimport { ActivatedRouteSnapshot, Router } from \"@angular/router\";\r\nimport { Observable, of } from \"rxjs\";\r\nimport { map } from \"rxjs/operators\";\r\nimport { TeacherStatus, TeacherStatusResponse } from \"../models/teacher.model\";\r\nimport { AuthService } from \"../services/auth.service\";\r\n@Injectable()\r\nexport class ProfileGuard  {\r\n    constructor(\r\n        private authService: AuthService,\r\n        private router: Router,\r\n    ) { }\r\n    canActivate(route: ActivatedRouteSnapshot): true | Observable<boolean> {\r\n        const user= this.authService.getLoggedInUser();\r\n        const userRole = this.authService.getUserRole();\r\n\r\n        // checks if the user role is 'teacher' and their status is 'active'.\r\n        // If both conditions are true, it returns true. Otherwise, it calls the method checkTeacherAccessApproval().\r\n\r\n        if (userRole && userRole.toLowerCase() === 'teacher') {\r\n            if (user.status.toLowerCase() === 'active') {\r\n                return true;\r\n            }\r\n            return this.checkTeacherAccessApproval();\r\n        }\r\n        // If the user role is not 'teacher', it simply returns true.\r\n        else {\r\n            return true;\r\n        }\r\n    }\r\n\r\n    checkTeacherAccessApproval() {\r\n        return this.authService.getTeacherStatus().pipe(\r\n            map((data: TeacherStatusResponse) => {\r\n                if (data.status === TeacherStatus.APPROVED) {\r\n                    return true;\r\n                } else {\r\n                    this.authService.setDisabledProfileRoutes(true);\r\n                    this.router.navigateByUrl('/user-profile/settings', { replaceUrl: true });\r\n                    return false;\r\n                }\r\n            })\r\n        )\r\n    }\r\n}"], "mappings": "AAGA,SAASA,GAAG,QAAQ,gBAAgB;AACpC,SAASC,aAAa,QAA+B,yBAAyB;;;;AAG9E,WAAaC,YAAY;EAAnB,MAAOA,YAAY;IACrBC,YACYC,WAAwB,EACxBC,MAAc;MADd,KAAAD,WAAW,GAAXA,WAAW;MACX,KAAAC,MAAM,GAANA,MAAM;IACd;IACJC,WAAWA,CAACC,KAA6B;MACrC,MAAMC,IAAI,GAAE,IAAI,CAACJ,WAAW,CAACK,eAAe,EAAE;MAC9C,MAAMC,QAAQ,GAAG,IAAI,CAACN,WAAW,CAACO,WAAW,EAAE;MAE/C;MACA;MAEA,IAAID,QAAQ,IAAIA,QAAQ,CAACE,WAAW,EAAE,KAAK,SAAS,EAAE;QAClD,IAAIJ,IAAI,CAACK,MAAM,CAACD,WAAW,EAAE,KAAK,QAAQ,EAAE;UACxC,OAAO,IAAI;QACf;QACA,OAAO,IAAI,CAACE,0BAA0B,EAAE;MAC5C;MACA;MAAA,KACK;QACD,OAAO,IAAI;MACf;IACJ;IAEAA,0BAA0BA,CAAA;MACtB,OAAO,IAAI,CAACV,WAAW,CAACW,gBAAgB,EAAE,CAACC,IAAI,CAC3ChB,GAAG,CAAEiB,IAA2B,IAAI;QAChC,IAAIA,IAAI,CAACJ,MAAM,KAAKZ,aAAa,CAACiB,QAAQ,EAAE;UACxC,OAAO,IAAI;QACf,CAAC,MAAM;UACH,IAAI,CAACd,WAAW,CAACe,wBAAwB,CAAC,IAAI,CAAC;UAC/C,IAAI,CAACd,MAAM,CAACe,aAAa,CAAC,wBAAwB,EAAE;YAAEC,UAAU,EAAE;UAAI,CAAE,CAAC;UACzE,OAAO,KAAK;QAChB;MACJ,CAAC,CAAC,CACL;IACL;IAAC,QAAAC,CAAA,G;uBApCQpB,YAAY,EAAAqB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;IAAA,QAAAC,EAAA,G;aAAZ3B,YAAY;MAAA4B,OAAA,EAAZ5B,YAAY,CAAA6B;IAAA;;SAAZ7B,YAAY;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}