{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport dayGridPlugin from '@fullcalendar/daygrid';\nimport timeGridPlugin from '@fullcalendar/timegrid';\nimport interactionPlugin from '@fullcalendar/interaction';\nimport * as moment from 'moment';\nimport { UserRole } from 'src/app/core/models/user.model';\nimport { DialogService } from 'primeng/dynamicdialog';\nimport { RequestLessonCalendarDialogComponent } from '../calendar-dialogs/request-lesson-calendar-dialog/request-lesson-calendar-dialog.component';\nimport { LessonInfoCalendarDialogComponent } from '../calendar-dialogs/lesson-info-calendar-dialog/lesson-info-calendar-dialog.component';\nimport { LessonStatus } from 'src/app/core/models/lesson.model';\nimport { LessonRatingCalendarDialogComponent } from '../calendar-dialogs/lesson-rating-calendar-dialog/lesson-rating-calendar-dialog.component';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/auth.service\";\nimport * as i2 from \"src/app/core/services/user.service\";\nimport * as i3 from \"src/app/core/services/classroom.service\";\nimport * as i4 from \"src/app/core/services/lesson.service\";\nimport * as i5 from \"src/app/core/services/general.service\";\nimport * as i6 from \"primeng/dynamicdialog\";\nimport * as i7 from \"src/app/core/services/calendar.service\";\nimport * as i8 from \"src/app/core/services/confirm-dialog.service\";\nimport * as i9 from \"@fullcalendar/angular\";\nimport * as i10 from \"primeng/scrollpanel\";\nconst _c0 = [\"calendar\"];\nconst _c1 = () => ({\n  width: \"100%\"\n});\nfunction WeeklyAvailabilityCalendarComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"b\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"i\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const arg_r1 = ctx.arg;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(arg_r1.timeText);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(arg_r1.event.title);\n  }\n}\nconst dayOfWeekMap = {\n  'mon': 1,\n  'tue': 2,\n  'wed': 3,\n  'thu': 4,\n  'fri': 5,\n  'sat': 6,\n  'sun': 0\n};\nlet eventGuid = 0;\nconst TODAY_STR = new Date().toISOString().replace(/T.*$/, ''); // YYYY-MM-DD of today\nexport const INITIAL_EVENTS = [];\nexport function createEventId() {\n  return String(eventGuid++);\n}\nexport class WeeklyAvailabilityCalendarComponent {\n  constructor(changeDetector, authService, userService, classroomService, lessonService, generalService, dialogService, calendarService, confirmDialogService) {\n    this.changeDetector = changeDetector;\n    this.authService = authService;\n    this.userService = userService;\n    this.classroomService = classroomService;\n    this.lessonService = lessonService;\n    this.generalService = generalService;\n    this.dialogService = dialogService;\n    this.calendarService = calendarService;\n    this.confirmDialogService = confirmDialogService;\n    this.subs = new SubSink();\n    this.calendar = {};\n    this.calendarHeight = 'auto';\n    this.dayOffEvents = [];\n    this.businessHours = [];\n    this.lessonEvents = [];\n    this.selectedTeacher = {};\n    this.isTabletOrMobile = false;\n    this.availableTeachersChanged = new EventEmitter();\n    this.calendarRangeDatesChanged = new EventEmitter();\n    this.calendarVisible = true;\n    this.currentEvents = [];\n    this.events = [{\n      title: 'Event 1',\n      start: '2023-04-01T10:00:00',\n      end: '2023-04-01T12:00:00',\n      display: 'inverse-background'\n    }, {\n      title: 'Event 2',\n      start: '2022-01-02T14:00:00',\n      end: '2022-01-02T16:00:00'\n    }];\n    this.calendarOptions = {};\n    this.calendarComponent = {};\n    this.dialogVisible = false;\n    this.user = {};\n    this.availableTeachers = [];\n    this.refs = [];\n    this.UserRoles = UserRole;\n    this.role = UserRole.NONE;\n    this.lessonStatus = LessonStatus;\n  }\n  ngOnInit() {\n    this.user = this.authService.getLoggedInUser();\n    this.role = this.authService.getLoggedInUser().role;\n    this.calendarOptions = {\n      plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin],\n      dayHeaderFormat: {\n        weekday: 'short',\n        day: '2-digit'\n      },\n      headerToolbar: {\n        left: 'prev',\n        center: 'title',\n        right: 'next'\n      },\n      initialView: this.isTabletOrMobile ? 'timeGridThreeDay' : 'timeGridWeek',\n      views: {\n        timeGridThreeDay: {\n          type: 'timeGrid',\n          dayCount: 3\n        }\n      },\n      weekends: true,\n      allDaySlot: false,\n      editable: true,\n      selectable: true,\n      slotDuration: '00:30:00',\n      slotLabelInterval: 30,\n      selectMirror: true,\n      dayMaxEvents: true,\n      eventShortHeight: 50,\n      slotMinTime: '00:00:00',\n      // Set the start time to 9 am\n      scrollTime: '08:00:00',\n      firstDay: 1,\n      height: this.calendarHeight,\n      contentHeight: '100%',\n      longPressDelay: 110,\n      eventLongPressDelay: 110,\n      events: this.currentEvents,\n      initialEvents: INITIAL_EVENTS,\n      select: this.handleDateSelect.bind(this),\n      eventClick: this.handleEventClick.bind(this),\n      eventsSet: this.handleEvents.bind(this),\n      customButtons: {\n        legendButton: {\n          text: 'Legend',\n          click: () => {\n            // Show/hide your custom legend here\n          }\n        }\n      },\n      eventDidMount: info => {\n        const eventElement = info.el;\n        eventElement.style.backgroundImage = this.getEventBackground(info.event);\n        eventElement.style.borderRadius = '8px';\n        eventElement.style.opacity = '1';\n        eventElement.style.padding = '0px';\n        eventElement.style.zIndex = \"1\";\n        const eventContent = this.createEventContent(info.event);\n        if (eventContent) {\n          // eventElement.appendChild(eventContent);\n        }\n      },\n      eventContent: info => {\n        if (info.event.extendedProps.type === 'dayOff') {\n          return {\n            html: ``\n          };\n        } else if (info.event.extendedProps.lesson) {\n          const classroom = info.event.extendedProps.classroom;\n          const lesson = info.event.extendedProps.lesson;\n          const title = this.authService.isStudent ? info.event.title : this.classroomService.getFormattedStudentNames(classroom.classroomStudents);\n          const isTrial = this.classroomService.isTrialClassroom(classroom);\n          const iconName = isTrial ? 'default-icon' : this.lessonService.getStatusNameFromStatusString(lesson.status).toLowerCase().includes(LessonStatus.REQUESTED.toLowerCase()) ? 'requested-white' : this.lessonService.getStatusNameFromStatusString(lesson.status);\n          if (lesson.duration <= 0.75) {\n            const extraInfoHidden = lesson.duration < 0.75 ? 'hidden' : '';\n            return {\n              html: `<div class=\"flex align-items-center flex-column pointer\">\n              <div class=\"flex align-items-center justify-content-between pt-1 flex-row  w-100 px-1 line-height-1\">\n                <div class=\"font-2xs font-semibold event-title flex align-items-center justify-content-between w-100\">\n                ${title}\n                <span><img src=\"/assets/icons/lessons/` + iconName + `.svg\" width=\"14\" /></span>\n                </div>\n                </div>\n                <div class=\"flex justify-content-between px-1 w-100 font-2xs line-height-1 ` + extraInfoHidden + `\">\n                  <span>${moment(lesson.startingDate).format('h:mm A')}</span>\n                  <span>${this.generalService.convertHoursToMinutesWithSuffix(lesson.duration)}</span>\n                </div>\n              </div>`\n            };\n          }\n          return {\n            html: `<div class=\"flex align-items-center flex-column pointer\">\n              <div class=\"flex justify-content-center px-1 pt-1 w-100\">\n                <span><img src=\"/assets/icons/lessons/` + iconName + `.svg\" width=\"16\" /></span>\n              </div>\n              <div class=\"font-xs font-semibold event-title\">${title}</div>\n              <div class=\"flex justify-content-between px-1 w-100 font-xs\">\n                <span>${moment(lesson.startingDate).format('h:mm A')}</span>\n                <span>${this.generalService.convertHoursToMinutesWithSuffix(lesson.duration)}</span>\n              </div>\n            </div>`\n          };\n        } else {\n          return {\n            html: ``\n          };\n        }\n      },\n      selectAllow: function (info) {\n        // Check if the selected range is more than one day\n        return moment(info.end).diff(moment(info.start), 'days') === 0;\n      },\n      datesSet: this.handleDatesRender.bind(this)\n    };\n    this.subs.sink = this.calendarService.teacherChangeListener.subscribe(res => {\n      const menuHeight = res;\n      console.log(res);\n    });\n  }\n  ngAfterContentChecked() {\n    const calendarApi = this.calendarComponent.getApi();\n    if (calendarApi) {}\n  }\n  ngOnChanges() {\n    const calendarApi = this.calendarComponent.getApi();\n    if (calendarApi) {\n      calendarApi.setOption('height', this.calendarHeight);\n      calendarApi.setOption('contentHeight', '100%');\n      calendarApi.removeAllEvents();\n      if (this.businessHours.length > 0) {\n        calendarApi.setOption('businessHours', this.businessHours);\n        calendarApi.setOption('dayCellClassNames', function (info) {\n          return 'custom-cell-class';\n        });\n      } else {\n        calendarApi.setOption('dayCellClassNames', function (info) {\n          return '';\n        });\n      }\n      if (this.role === this.UserRoles.STUDENT) {\n        calendarApi.setOption('selectConstraint', this.businessHours);\n      }\n      // Add events to the calendar\n      calendarApi.addEventSource(this.dayOffEvents);\n      calendarApi.addEventSource(this.lessonEvents);\n      // Set custom cell class for non-business hours\n      // calendarApi.setOption('businessHours', this.businessHours);\n      // calendarApi.setOption('selectConstraint', this.businessHours);\n    }\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  ngAfterViewInit() {\n    const calendarApi = this.calendarComponent.getApi();\n    if (calendarApi) {\n      // this.openDialogWithComponent(LessonInfoCalendarDialogComponent, {});\n    }\n  }\n  handleCalendarToggle() {\n    this.calendarVisible = !this.calendarVisible;\n  }\n  handleWeekendsToggle() {\n    const {\n      calendarOptions\n    } = this;\n    calendarOptions.weekends = !calendarOptions.weekends;\n  }\n  handleDateSelect(selectInfo) {\n    const calendarApi = selectInfo.view.calendar;\n    const updatedSelectInfo = {\n      ...selectInfo,\n      teacher: this.selectedTeacher\n    };\n    console.log(updatedSelectInfo);\n    const timeDifference = moment(updatedSelectInfo.end).diff(moment(updatedSelectInfo.start), 'hours');\n    const currentTime = moment();\n    const isPast = moment(updatedSelectInfo.startStr).isBefore(currentTime);\n    if (isPast && this.authService.isStudent) {\n      this.showTimeSlotSelectionConfirmationDialog(`You cannot book a lesson for a past date and time. Please select a different time slot.`, '/assets/icons/clock-dialog-icon.svg');\n      return;\n    }\n    if (timeDifference > 3) {\n      this.showTimeSlotSelectionConfirmationDialog(`It is not possible to book a lesson for more than 3 hours. <br> Please choose a smaller time slot.`, '/assets/icons/hourglass-dialog.png');\n    } else {\n      this.openDialogWithComponent(RequestLessonCalendarDialogComponent, updatedSelectInfo);\n    }\n    // calendarApi.unselect(); // clear date selection\n  }\n  showTimeSlotSelectionConfirmationDialog(message, icon) {\n    this.confirmDialogService.setProperties({\n      confirmMessage: message,\n      acceptBtnLabel: 'Ok',\n      rejectBtnLabel: '',\n      showHeader: true,\n      dialogType: 'custom',\n      confirmIcon: icon,\n      action: 'charge-lesson',\n      headerClass: 'my-custom-header-class'\n    });\n    this.confirmDialogService.show();\n  }\n  handleEventClick(clickInfo) {\n    // this.dialogVisible = true;\n    const event = clickInfo.event;\n    console.log(event.extendedProps);\n    switch (event.extendedProps.type) {\n      case 'dayOff':\n        console.log('Day off event clicked');\n        break;\n      case 'lesson':\n        this.generalService.openDialogWithComponent(this.dialogService, LessonInfoCalendarDialogComponent, undefined, {\n          dialogService: this.dialogService,\n          ...event.extendedProps\n        });\n        break;\n      case 'businessHour':\n        this.openDialogWithComponent(RequestLessonCalendarDialogComponent, event);\n        break;\n      default:\n        console.log('Unknown event type clicked');\n    }\n  }\n  handleEvents(events) {\n    this.currentEvents = events;\n    this.changeDetector.detectChanges();\n  }\n  handleDatesRender(info) {\n    const start = info.view.activeStart;\n    const end = info.view.activeEnd;\n    const dates = {\n      fromDate: start,\n      toDate: end\n    };\n    this.calendarRangeDatesChanged.emit(dates);\n  }\n  getEventBackground(event) {\n    const {\n      type\n    } = event.extendedProps;\n    switch (type) {\n      case 'dayOff':\n        return 'linear-gradient(180deg, rgba(127,146,248,1) 0%, rgba(50,65,149,1) 100%)';\n      case 'businessHour':\n        return 'linear-gradient(250deg, #AAAAAA)';\n      case 'Trial':\n        if (this.isLessonStatusWithClassroomType(event, LessonStatus.COMPLETED_TRIAL)) {\n          return 'linear-gradient(250deg, #64cfdd, #48c4e0, #2cb8e2, #18abe4, #219de3, #1591e2, #1e85e0, #3177dc, #2a69da, #2b5ad6, #334ad0, #3f37c9)';\n        } else if (this.isLessonStatusWithClassroomType(event, LessonStatus.ARRANGED_TRIAL)) {\n          return 'linear-gradient(0deg, rgba(110,37,255,1) 0%, rgba(167,149,255,1) 100%)';\n        } else {\n          return 'linear-gradient(90deg, rgba(148,136,247,1) 0%, rgba(163,139,255,1) 100%)';\n        }\n      case 'Paid':\n        if (this.isLessonStatusWithClassroomType(event, LessonStatus.REQUESTED)) {\n          return 'linear-gradient(90deg, rgba(108,81,148,1) 0%, rgba(156,80,202,1) 54%)';\n        } else {\n          return 'linear-gradient(180deg, rgba(122,130,240,1) 0%, rgba(88,60,178,1) 100%)';\n        }\n      default:\n        return 'linear-gradient(270deg, rgba(100,179,221,1) 0%, rgba(49,93,175,1) 100%)';\n    }\n  }\n  /**\n   * Checks if the lesson status is with a specific classroom type.\n   * @param event - The event object.\n   * @param type - The classroom type to check against. Default is 'Paid'.\n   * @param status - The lesson status to check against. Default is LessonStatus.ARRANGED.\n   * @returns True if the lesson status is with the specified classroom type, false otherwise.\n   */\n  isLessonStatusWithClassroomType(event, type = 'Paid', status = LessonStatus.ARRANGED) {\n    const lesson = event.extendedProps.lesson;\n    const classroom = event.extendedProps.classroom;\n    // Check if the lesson and classroom exist and if their types match\n    return lesson && classroom && classroom.type === type && this.lessonService.getStatusNameFromStatusString(lesson.status) === status;\n  }\n  createEventContent(event) {\n    if (event.extendedProps.type === 'dayOff') {\n      return null;\n    } else {\n      const eventTitle = event.title;\n      const eventIcon = '<i class=\"pi pi-send\"></i>';\n      const eventContent = document.createElement('div');\n      eventContent.classList.add('fc-event');\n      eventContent.innerHTML = `<div class=\"fc-event-main\">' +\n        '<div class=\"fc-event-icon\">` + eventIcon + `</div>' +\n        '<div class=\"fc-event-title\">` + eventTitle + `</div>' +\n        '</div>`;\n      return eventContent;\n    }\n  }\n  createDayOffEvents(daysOff) {\n    return daysOff.map(dayOff => {\n      const from = new Date(dayOff.period.from);\n      const to = new Date(dayOff.period.to);\n      return {\n        title: dayOff.reason,\n        start: from,\n        display: 'background',\n        extendedProps: {\n          type: 'dayOff',\n          rendering: 'background'\n        },\n        selectable: false,\n        editable: false,\n        allDay: true\n      };\n    });\n  }\n  openDialogWithComponent(component, dialogData, width = '270px') {\n    this.refs.forEach(ref => ref.close());\n    this.refs = [];\n    const dialogRef = this.dialogService.open(component, {\n      header: 'Choose a Product',\n      width: width,\n      showHeader: false,\n      dismissableMask: true,\n      modal: true,\n      contentStyle: {\n        \"max-width\": \"100%\",\n        \"max-height\": \"500px\",\n        \"overflow\": \"auto\",\n        \"border-radius\": \"10px\",\n        \"padding\": \"0px\"\n      },\n      baseZIndex: 10000,\n      maskStyleClass: 'transparent-mask',\n      data: {\n        dialogData: dialogData\n      }\n    });\n    this.refs.push(dialogRef);\n    dialogRef.onClose.subscribe(data => {\n      console.log('Dialog closed with data:', data);\n      if (data) {\n        if (data.action && data.type === 'Available') {\n          this.addSpecifidDateCalendarEvent(data);\n          this.calendarService.setUpdateListener(true);\n        }\n        if (data.action && data.action === 'block') {\n          this.addSpecifidDateCalendarEvent(data);\n          this.calendarService.setUpdateListener(true);\n        }\n        if (data.status) {\n          if (data.status === this.lessonStatus.COMPLETED) {\n            this.openDialogWithComponent(LessonRatingCalendarDialogComponent, data, '50%');\n          } else {\n            this.calendarService.setUpdateListener(true);\n          }\n        }\n      }\n    });\n  }\n  addSpecifidDateCalendarEvent(data) {\n    const start = `${data.date} ${data.from}`;\n    const end = `${data.date} ${data.to}`;\n    const calendarApi = this.calendarComponent.getApi();\n    const event = calendarApi.getEventById('my-event-id');\n    console.log(event);\n    calendarApi.addEvent({\n      title: 'asdsad',\n      start: moment(start, 'DD/MM/YYYY HH:mm').format('YYYY-MM-DDTHH:mm:ss'),\n      end: moment(end, 'DD/MM/YYYY HH:mm').format('YYYY-MM-DDTHH:mm:ss'),\n      display: 'custom-cell-class',\n      classNames: ['custom-cell-class'],\n      extendedProps: {\n        type: 'businessHour',\n        rendering: 'custom-cell-class',\n        id: 'HERE-LOOK'\n      },\n      selectable: true,\n      editable: false,\n      allDay: false\n    });\n  }\n  static #_ = this.ɵfac = function WeeklyAvailabilityCalendarComponent_Factory(t) {\n    return new (t || WeeklyAvailabilityCalendarComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.ClassroomService), i0.ɵɵdirectiveInject(i4.LessonService), i0.ɵɵdirectiveInject(i5.GeneralService), i0.ɵɵdirectiveInject(i6.DialogService), i0.ɵɵdirectiveInject(i7.CalendarService), i0.ɵɵdirectiveInject(i8.ConfirmDialogService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: WeeklyAvailabilityCalendarComponent,\n    selectors: [[\"app-weekly-availability-calendar\"]],\n    viewQuery: function WeeklyAvailabilityCalendarComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.calendar = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.calendarComponent = _t.first);\n      }\n    },\n    inputs: {\n      calendarHeight: \"calendarHeight\",\n      dayOffEvents: \"dayOffEvents\",\n      businessHours: \"businessHours\",\n      lessonEvents: \"lessonEvents\",\n      selectedTeacher: \"selectedTeacher\",\n      isTabletOrMobile: \"isTabletOrMobile\"\n    },\n    outputs: {\n      availableTeachersChanged: \"availableTeachersChanged\",\n      calendarRangeDatesChanged: \"calendarRangeDatesChanged\"\n    },\n    features: [i0.ɵɵProvidersFeature([DialogService]), i0.ɵɵNgOnChangesFeature],\n    decls: 5,\n    vars: 4,\n    consts: [[\"scrollPanel\", \"\"], [\"calendar\", \"\"], [\"styleClass\", \"custombar1 h-30rem lg:h-full\"], [3, \"options\"], [\"fcEventContent\", \"\"]],\n    template: function WeeklyAvailabilityCalendarComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"p-scrollPanel\", 2, 0)(2, \"full-calendar\", 3, 1);\n        i0.ɵɵtemplate(4, WeeklyAvailabilityCalendarComponent_ng_template_4_Template, 4, 2, \"ng-template\", 4);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵstyleMap(i0.ɵɵpureFunction0(3, _c1));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"options\", ctx.calendarOptions);\n      }\n    },\n    dependencies: [i9.FullCalendarComponent, i10.ScrollPanel],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.fc-timegrid-slot-label[_ngcontent-%COMP%] {\\n  min-height: 65px;\\n}\\n\\n.fc-timegrid-slot[_ngcontent-%COMP%] {\\n  height: 3em;\\n}\\n\\n[_nghost-%COMP%]     .fc-direction-ltr .fc-timegrid-slot-label-frame {\\n  line-height: 1.6;\\n}\\n\\n[_nghost-%COMP%]     .fc-event {\\n  border: none;\\n}\\n\\n[_nghost-%COMP%]     .fc-timeGridWeek-view .fc-timegrid-slot {\\n  height: 2rem;\\n}\\n\\n[_nghost-%COMP%]     .fc-toolbar-title {\\n  font-size: 1rem !important;\\n  background-image: linear-gradient(to bottom, #8497ff, #6977da, #4e59b6, #323c93, #122171);\\n  border-radius: 100px;\\n  color: white;\\n  padding: 0.3rem 1rem;\\n}\\n\\n[_nghost-%COMP%]     .fc .fc-non-business {\\n  background: #fff;\\n  border-right: 1px solid #f5f5f5;\\n}\\n[_nghost-%COMP%]     .custom-cell-class {\\n  border: none;\\n  background-color: #E4E9F5 !important;\\n  border-radius: 10px;\\n}\\n[_nghost-%COMP%]     .fc-toolbar {\\n  justify-content: center;\\n}\\n[_nghost-%COMP%]     .fc-toolbar .fc-button {\\n  background-color: transparent !important;\\n  border: none;\\n  box-shadow: none !important;\\n}\\n[_nghost-%COMP%]     .fc-icon {\\n  display: none;\\n}\\n[_nghost-%COMP%]     .fc-prev-button .fa, [_nghost-%COMP%]     .fc-next-button .fa {\\n  display: none; \\n\\n}\\n[_nghost-%COMP%]     .fc-prev-button::before {\\n  content: url(\\\"/assets/icons/calendar-arrow.svg\\\"); \\n\\n  display: block;\\n  transform: rotateY(180deg);\\n  margin-top: 0.5rem;\\n}\\n[_nghost-%COMP%]     .fc-next-button::before {\\n  content: url(\\\"/assets/icons/calendar-arrow.svg\\\"); \\n\\n  display: block;\\n  margin-top: 0.5rem;\\n}\\n[_nghost-%COMP%]     .fc-timegrid-slot-label-cushion {\\n  font-family: \\\"Proxima Nova Thin\\\";\\n  text-transform: uppercase;\\n  font-size: 0.7rem;\\n}\\n[_nghost-%COMP%]     .fc-timegrid-slot-minor {\\n  border-top-style: none;\\n}\\n[_nghost-%COMP%]     .fc.fc-theme-standard .fc-view-harness th {\\n  background-image: linear-gradient(181deg, rgba(0, 44, 207, 0.1882352941), transparent);\\n  border: 0;\\n}\\n[_nghost-%COMP%]     .fc.fc-theme-standard .fc-view-harness th:first-of-type {\\n  border-radius: 10px 0 0 0;\\n}\\n[_nghost-%COMP%]     .fc.fc-theme-standard .fc-view-harness th:last-of-type {\\n  border-radius: 0 10px 0 0;\\n}\\n[_nghost-%COMP%]     .fc .fc-timegrid-col.fc-day-today {\\n  background-color: unset;\\n}\\n[_nghost-%COMP%]     .fc.fc-theme-standard a {\\n  padding: 0.5rem 0;\\n}\\n[_nghost-%COMP%]     .fc thead:after {\\n  content: \\\"@\\\";\\n  display: block;\\n  line-height: 10px;\\n  text-indent: -99999px;\\n}\\n[_nghost-%COMP%]     .fc.fc-theme-standard .fc-view-harness .fc-scrollgrid {\\n  border: 0;\\n}\\n[_nghost-%COMP%]     .fc-day-today .fc-scrollgrid-sync-inner {\\n  background: linear-gradient(rgba(0, 44, 207, 0.0784313725) 0%, rgba(201, 210, 255, 0.0941176471) 100%);\\n}\\n[_nghost-%COMP%]     .fc.fc-theme-standard a {\\n  text-transform: uppercase;\\n  font-family: \\\"Lato Light\\\";\\n  font-size: 0.85rem;\\n  color: #122171;\\n}\\n[_nghost-%COMP%]     .fc .fc-toolbar.fc-header-toolbar {\\n  margin-bottom: 0;\\n}\\n[_nghost-%COMP%]     .fc thead .fc-scroller {\\n  background: white;\\n  overflow: hidden !important;\\n}\\n[_nghost-%COMP%]     .fc.fc-theme-standard .fc-view-harness td {\\n  border-color: transparent;\\n}\\n[_nghost-%COMP%]     ::-webkit-scrollbar {\\n  display: block;\\n  width: 15px;\\n  height: 15px !important;\\n}\\n[_nghost-%COMP%]     ::-webkit-scrollbar-track {\\n  background: linear-gradient(rgba(0, 45, 207, 0.0784313725) 0%, rgba(201, 210, 255, 0.0941176471) 100%);\\n  border-radius: 15px;\\n}\\n[_nghost-%COMP%]     .event-title {\\n  text-transform: capitalize;\\n}\\n[_nghost-%COMP%]     .fc-scroller {\\n  overflow-y: hidden !important;\\n}\\n@media only screen and (min-width: 576px) {\\n  [_nghost-%COMP%]     .fc-scroller {\\n    overflow-y: auto !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "dayGridPlugin", "timeGridPlugin", "interactionPlugin", "moment", "UserRole", "DialogService", "RequestLessonCalendarDialogComponent", "LessonInfoCalendarDialogComponent", "LessonStatus", "LessonRatingCalendarDialogComponent", "SubSink", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "arg_r1", "timeText", "event", "title", "dayOfWeekMap", "eventGuid", "TODAY_STR", "Date", "toISOString", "replace", "INITIAL_EVENTS", "createEventId", "String", "WeeklyAvailabilityCalendarComponent", "constructor", "changeDetector", "authService", "userService", "classroomService", "lessonService", "generalService", "dialogService", "calendarService", "confirmDialogService", "subs", "calendar", "calendarHeight", "dayOffEvents", "businessHours", "lessonEvents", "<PERSON><PERSON><PERSON><PERSON>", "isTabletOrMobile", "availableTeachersChanged", "calendarRangeDatesChanged", "calendarVisible", "currentEvents", "events", "start", "end", "display", "calendarOptions", "calendarComponent", "dialogVisible", "user", "availableTeachers", "refs", "UserRoles", "role", "NONE", "lessonStatus", "ngOnInit", "getLoggedInUser", "plugins", "dayHeaderFormat", "weekday", "day", "headerToolbar", "left", "center", "right", "initialView", "views", "timeGridThreeDay", "type", "dayCount", "weekends", "allDaySlot", "editable", "selectable", "slotDuration", "slotLabelInterval", "selectMirror", "dayMaxEvents", "eventShortHeight", "slotMinTime", "scrollTime", "firstDay", "height", "contentHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "eventLongPress<PERSON>elay", "initialEvents", "select", "handleDateSelect", "bind", "eventClick", "handleEventClick", "eventsSet", "handleEvents", "customButtons", "<PERSON><PERSON><PERSON><PERSON>", "text", "click", "eventDidMount", "info", "eventElement", "el", "style", "backgroundImage", "getEventBackground", "borderRadius", "opacity", "padding", "zIndex", "eventContent", "createEventContent", "extendedProps", "html", "lesson", "classroom", "isStudent", "getFormattedStudentNames", "classroomStudents", "isTrial", "isTrialClassroom", "iconName", "getStatusNameFromStatusString", "status", "toLowerCase", "includes", "REQUESTED", "duration", "extraInfoHidden", "startingDate", "format", "convertHoursToMinutesWithSuffix", "selectAllow", "diff", "datesSet", "handleDatesRender", "sink", "teacher<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subscribe", "res", "menuHeight", "console", "log", "ngAfterContentChecked", "calendarApi", "getApi", "ngOnChanges", "setOption", "removeAllEvents", "length", "STUDENT", "addEventSource", "ngOnDestroy", "unsubscribe", "ngAfterViewInit", "handleCalendarToggle", "handleWeekendsToggle", "selectInfo", "view", "updatedSelectInfo", "teacher", "timeDifference", "currentTime", "isPast", "startStr", "isBefore", "showTimeSlotSelectionConfirmationDialog", "openDialogWithComponent", "message", "icon", "setProperties", "confirmMessage", "acceptBtnLabel", "rejectBtnLabel", "showHeader", "dialogType", "confirmIcon", "action", "headerClass", "show", "clickInfo", "undefined", "detectChanges", "activeStart", "activeEnd", "dates", "fromDate", "toDate", "emit", "isLessonStatusWithClassroomType", "COMPLETED_TRIAL", "ARRANGED_TRIAL", "ARRANGED", "eventTitle", "eventIcon", "document", "createElement", "classList", "add", "innerHTML", "createDayOffEvents", "daysOff", "map", "dayOff", "from", "period", "to", "reason", "rendering", "allDay", "component", "dialogData", "width", "for<PERSON>ach", "ref", "close", "dialogRef", "open", "header", "dismissableMask", "modal", "contentStyle", "baseZIndex", "maskStyleClass", "data", "push", "onClose", "addSpecifidDateCalendarEvent", "setUpdateListener", "COMPLETED", "date", "getEventById", "addEvent", "classNames", "id", "_", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "AuthService", "i2", "UserService", "i3", "ClassroomService", "i4", "LessonService", "i5", "GeneralService", "i6", "i7", "CalendarService", "i8", "ConfirmDialogService", "_2", "selectors", "viewQuery", "WeeklyAvailabilityCalendarComponent_Query", "rf", "ctx", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "WeeklyAvailabilityCalendarComponent_Template", "ɵɵtemplate", "WeeklyAvailabilityCalendarComponent_ng_template_4_Template", "ɵɵstyleMap", "ɵɵpureFunction0", "_c1", "ɵɵproperty"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\calendar\\weekly-availability-calendar\\weekly-availability-calendar.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\calendar\\weekly-availability-calendar\\weekly-availability-calendar.component.html"], "sourcesContent": ["import { AfterContentChecked, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';\r\nimport { CalendarOptions, DateSelectArg, EventApi, EventClickArg } from '@fullcalendar/core'; // useful for typechecking\r\nimport dayGridPlugin from '@fullcalendar/daygrid';\r\nimport timeGridPlugin from '@fullcalendar/timegrid';\r\nimport interactionPlugin from '@fullcalendar/interaction';\r\nimport * as moment from 'moment';\r\n\r\n\r\n// DUMMY EVENTS TODO: real data\r\nimport { EventInput } from '@fullcalendar/core';\r\nimport { UserService } from 'src/app/core/services/user.service';\r\nimport { User, UserRole } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { FullCalendarComponent } from '@fullcalendar/angular';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { switchMap } from 'rxjs/operators';\r\nimport { Dialog } from 'primeng/dialog';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport {DialogService, DynamicDialogRef} from 'primeng/dynamicdialog';\r\nimport { RequestLessonCalendarDialogComponent } from '../calendar-dialogs/request-lesson-calendar-dialog/request-lesson-calendar-dialog.component';\r\nimport { LessonInfoCalendarDialogComponent } from '../calendar-dialogs/lesson-info-calendar-dialog/lesson-info-calendar-dialog.component';\r\nimport { LessonStatus } from 'src/app/core/models/lesson.model';\r\nimport { LessonRatingCalendarDialogComponent } from '../calendar-dialogs/lesson-rating-calendar-dialog/lesson-rating-calendar-dialog.component';\r\nimport { LessonService } from 'src/app/core/services/lesson.service';\r\nimport { CalendarService } from 'src/app/core/services/calendar.service';\r\nimport { SubSink } from 'subsink';\r\nimport { ConfirmDialogService } from 'src/app/core/services/confirm-dialog.service';\r\nimport { Status } from 'src/app/core/models/classroom.model';\r\n\r\nconst dayOfWeekMap: { [key: string]: number } = {\r\n  'mon': 1,\r\n  'tue': 2,\r\n  'wed': 3,\r\n  'thu': 4,\r\n  'fri': 5,\r\n  'sat': 6,\r\n  'sun': 0\r\n};\r\n\r\nlet eventGuid = 0;\r\nconst TODAY_STR = new Date().toISOString().replace(/T.*$/, ''); // YYYY-MM-DD of today\r\n\r\nexport const INITIAL_EVENTS: EventInput[] = [\r\n];\r\n\r\nexport function createEventId() {\r\n  return String(eventGuid++);\r\n}\r\n\r\n@Component({\r\n  selector: 'app-weekly-availability-calendar',\r\n  templateUrl: './weekly-availability-calendar.component.html',\r\n  styleUrls: ['./weekly-availability-calendar.component.scss'],\r\n  providers: [DialogService]\r\n})\r\nexport class WeeklyAvailabilityCalendarComponent implements OnInit, AfterContentChecked {\r\n  private subs = new SubSink();\r\n  @ViewChild('calendar', {static: true}) calendar: ElementRef = {} as ElementRef;\r\n  @Input() calendarHeight = 'auto';\r\n  @Input() dayOffEvents = [];\r\n  @Input() businessHours: any[] = [];\r\n  @Input() lessonEvents = [];\r\n  @Input() selectedTeacher = {} as User;\r\n  @Input() isTabletOrMobile = false;\r\n  \r\n  @Output() availableTeachersChanged = new EventEmitter<any>();\r\n  @Output() calendarRangeDatesChanged = new EventEmitter<any>();\r\n\r\n  calendarVisible = true;\r\n  currentEvents: any[] = [];\r\n  events: any[] = [\r\n    {\r\n      title: 'Event 1',\r\n      start: '2023-04-01T10:00:00',\r\n      end: '2023-04-01T12:00:00',\r\n      display: 'inverse-background',\r\n    },\r\n    {\r\n      title: 'Event 2',\r\n      start: '2022-01-02T14:00:00',\r\n      end: '2022-01-02T16:00:00'\r\n    }\r\n  ];\r\n  calendarOptions: CalendarOptions = {};\r\n\r\n  @ViewChild('calendar', { static: true }) calendarComponent: FullCalendarComponent = {} as FullCalendarComponent;\r\n\r\n  dialogVisible = false;\r\n  user: User = {} as User;\r\n  availableTeachers: any[] = [];\r\n  refs: DynamicDialogRef[] = [];\r\n  UserRoles = UserRole;\r\n  role: UserRole = UserRole.NONE;\r\n  lessonStatus = LessonStatus;\r\n  constructor(\r\n    private changeDetector: ChangeDetectorRef,\r\n    private authService: AuthService,\r\n    private userService: UserService,\r\n    private classroomService: ClassroomService,\r\n    private lessonService: LessonService,\r\n    private generalService: GeneralService,\r\n    public dialogService: DialogService,\r\n    public calendarService: CalendarService,\r\n    public confirmDialogService: ConfirmDialogService,\r\n    ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.user = this.authService.getLoggedInUser();\r\n    this.role = this.authService.getLoggedInUser().role;\r\n    this.calendarOptions = {\r\n      plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin],\r\n      dayHeaderFormat: { weekday: 'short', day: '2-digit' },\r\n      headerToolbar: { left: 'prev', center: 'title', right: 'next' },\r\n      initialView: this.isTabletOrMobile ? 'timeGridThreeDay' : 'timeGridWeek',\r\n      views: {\r\n        timeGridThreeDay: {\r\n          type: 'timeGrid',\r\n          dayCount: 3\r\n        }\r\n      },\r\n      weekends: true,\r\n      allDaySlot: false,\r\n      editable: true,\r\n      selectable: true,\r\n      slotDuration: '00:30:00',\r\n      slotLabelInterval: 30,\r\n      selectMirror: true,\r\n      dayMaxEvents: true,\r\n      eventShortHeight: 50,\r\n      slotMinTime: '00:00:00', // Set the start time to 9 am\r\n      scrollTime: '08:00:00',\r\n      firstDay: 1,\r\n      height: this.calendarHeight,\r\n      contentHeight: '100%',\r\n      longPressDelay: 110,\r\n      eventLongPressDelay: 110,\r\n      events: this.currentEvents,\r\n      initialEvents: INITIAL_EVENTS,\r\n      select: this.handleDateSelect.bind(this),\r\n      eventClick: this.handleEventClick.bind(this),\r\n      eventsSet: this.handleEvents.bind(this),\r\n      customButtons: {\r\n        legendButton: {\r\n          text: 'Legend',\r\n          click: () => {\r\n            // Show/hide your custom legend here\r\n          }\r\n        }\r\n      },\r\n      eventDidMount: (info) => {\r\n        const eventElement = info.el;\r\n        eventElement.style.backgroundImage = this.getEventBackground(info.event);\r\n        eventElement.style.borderRadius = '8px';\r\n        eventElement.style.opacity = '1';\r\n        eventElement.style.padding = '0px';\r\n        eventElement.style.zIndex = \"1\";\r\n        const eventContent = this.createEventContent(info.event);\r\n        if (eventContent) {\r\n          // eventElement.appendChild(eventContent);\r\n        }\r\n      },\r\n      eventContent: (info) => {\r\n        if (info.event.extendedProps.type === 'dayOff') {\r\n          return { html: `` };\r\n        } else if (info.event.extendedProps.lesson) {\r\n          const classroom = info.event.extendedProps.classroom;\r\n          const lesson = info.event.extendedProps.lesson;\r\n          const title = this.authService.isStudent ? info.event.title : this.classroomService.getFormattedStudentNames(classroom.classroomStudents);\r\n          const isTrial = this.classroomService.isTrialClassroom(classroom);\r\n          const iconName = isTrial ? 'default-icon' : this.lessonService.getStatusNameFromStatusString(lesson.status).toLowerCase().includes(LessonStatus.REQUESTED.toLowerCase()) ? 'requested-white' : this.lessonService.getStatusNameFromStatusString(lesson.status);\r\n          if (lesson.duration <= 0.75) {\r\n            const extraInfoHidden = lesson.duration < 0.75 ? 'hidden' : '';\r\n            return {\r\n              html: `<div class=\"flex align-items-center flex-column pointer\">\r\n              <div class=\"flex align-items-center justify-content-between pt-1 flex-row  w-100 px-1 line-height-1\">\r\n                <div class=\"font-2xs font-semibold event-title flex align-items-center justify-content-between w-100\">\r\n                ${title}\r\n                <span><img src=\"/assets/icons/lessons/`+ iconName + `.svg\" width=\"14\" /></span>\r\n                </div>\r\n                </div>\r\n                <div class=\"flex justify-content-between px-1 w-100 font-2xs line-height-1 `+ extraInfoHidden + `\">\r\n                  <span>${moment(lesson.startingDate).format('h:mm A')}</span>\r\n                  <span>${this.generalService.convertHoursToMinutesWithSuffix(lesson.duration)}</span>\r\n                </div>\r\n              </div>`\r\n            };\r\n          }\r\n          return {\r\n            html: `<div class=\"flex align-items-center flex-column pointer\">\r\n              <div class=\"flex justify-content-center px-1 pt-1 w-100\">\r\n                <span><img src=\"/assets/icons/lessons/`+ iconName + `.svg\" width=\"16\" /></span>\r\n              </div>\r\n              <div class=\"font-xs font-semibold event-title\">${title}</div>\r\n              <div class=\"flex justify-content-between px-1 w-100 font-xs\">\r\n                <span>${moment(lesson.startingDate).format('h:mm A')}</span>\r\n                <span>${this.generalService.convertHoursToMinutesWithSuffix(lesson.duration)}</span>\r\n              </div>\r\n            </div>`\r\n          };\r\n        } else {\r\n          return { html: `` };\r\n        }\r\n      },\r\n      selectAllow: function(info) {\r\n        // Check if the selected range is more than one day\r\n        return moment(info.end).diff(moment(info.start), 'days') === 0;\r\n      },\r\n      datesSet: this.handleDatesRender.bind(this),\r\n    };\r\n\r\n    \r\n    this.subs.sink = this.calendarService.teacherChangeListener.subscribe((res) => {\r\n      const menuHeight = res;\r\n      console.log(res);\r\n    });\r\n  }\r\n\r\n  ngAfterContentChecked() {\r\n    const calendarApi = this.calendarComponent.getApi();\r\n    if (calendarApi) {\r\n    }\r\n  }\r\n\r\n  ngOnChanges() {\r\n\r\n    const calendarApi = this.calendarComponent.getApi();\r\n    if (calendarApi) {\r\n      calendarApi.setOption('height', this.calendarHeight);\r\n      calendarApi.setOption('contentHeight', '100%');\r\n      calendarApi.removeAllEvents();\r\n      if (this.businessHours.length > 0) {\r\n        calendarApi.setOption('businessHours', this.businessHours);\r\n        calendarApi.setOption('dayCellClassNames', function (info: any) {\r\n          return 'custom-cell-class';\r\n        });\r\n      } else {\r\n        calendarApi.setOption('dayCellClassNames', function (info: any) {\r\n          return '';\r\n        });\r\n      }\r\n      if (this.role === this.UserRoles.STUDENT) {\r\n        calendarApi.setOption('selectConstraint', this.businessHours);\r\n      }\r\n\r\n      // Add events to the calendar\r\n      calendarApi.addEventSource(this.dayOffEvents);\r\n\r\n      calendarApi.addEventSource(this.lessonEvents);\r\n      // Set custom cell class for non-business hours\r\n\r\n      // calendarApi.setOption('businessHours', this.businessHours);\r\n      // calendarApi.setOption('selectConstraint', this.businessHours);\r\n\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n\r\n    const calendarApi = this.calendarComponent.getApi();\r\n    if (calendarApi) {\r\n      // this.openDialogWithComponent(LessonInfoCalendarDialogComponent, {});\r\n    }\r\n \r\n  }\r\n\r\n  handleCalendarToggle() {\r\n    this.calendarVisible = !this.calendarVisible;\r\n  }\r\n\r\n  handleWeekendsToggle() {\r\n    const { calendarOptions } = this;\r\n    calendarOptions.weekends = !calendarOptions.weekends;\r\n  }\r\n\r\n  handleDateSelect(selectInfo: DateSelectArg) {\r\n    const calendarApi = selectInfo.view.calendar;\r\n    const updatedSelectInfo = {\r\n      ...selectInfo,\r\n      teacher: this.selectedTeacher\r\n    };\r\n    console.log(updatedSelectInfo);\r\n\r\n    const timeDifference = moment(updatedSelectInfo.end).diff(moment(updatedSelectInfo.start), 'hours');\r\n    const currentTime = moment();\r\n    const isPast = moment(updatedSelectInfo.startStr).isBefore(currentTime);\r\n\r\n    if (isPast && this.authService.isStudent) {\r\n      this.showTimeSlotSelectionConfirmationDialog(\r\n        `You cannot book a lesson for a past date and time. Please select a different time slot.`,\r\n        '/assets/icons/clock-dialog-icon.svg'\r\n      );\r\n      return;\r\n    }\r\n\r\n    if (timeDifference > 3) {\r\n      this.showTimeSlotSelectionConfirmationDialog(\r\n        `It is not possible to book a lesson for more than 3 hours. <br> Please choose a smaller time slot.`,\r\n        '/assets/icons/hourglass-dialog.png'\r\n      );\r\n    } else {\r\n      this.openDialogWithComponent(RequestLessonCalendarDialogComponent, updatedSelectInfo);\r\n    }\r\n    // calendarApi.unselect(); // clear date selection\r\n  }\r\n\r\n  private showTimeSlotSelectionConfirmationDialog(message: string, icon: string): void {\r\n    this.confirmDialogService.setProperties({\r\n      confirmMessage: message,\r\n      acceptBtnLabel: 'Ok',\r\n      rejectBtnLabel: '',\r\n      showHeader: true,\r\n      dialogType: 'custom',\r\n      confirmIcon: icon,\r\n      action: 'charge-lesson',\r\n      headerClass: 'my-custom-header-class'\r\n    });\r\n    this.confirmDialogService.show();\r\n  }\r\n\r\n  handleEventClick(clickInfo: EventClickArg) {\r\n    // this.dialogVisible = true;\r\n    const event = clickInfo.event;\r\n    console.log(event.extendedProps);\r\n    switch (event.extendedProps.type) {\r\n      case 'dayOff':\r\n        console.log('Day off event clicked');\r\n        break;\r\n      case 'lesson':\r\n        this.generalService.openDialogWithComponent(this.dialogService, LessonInfoCalendarDialogComponent, undefined,\r\n          { dialogService: this.dialogService, ...event.extendedProps });\r\n        break;\r\n      case 'businessHour':\r\n        this.openDialogWithComponent(RequestLessonCalendarDialogComponent, event);\r\n        break;\r\n      default:\r\n        console.log('Unknown event type clicked');\r\n    }\r\n  }\r\n\r\n  handleEvents(events: EventApi[]) {\r\n    this.currentEvents = events;\r\n    this.changeDetector.detectChanges();\r\n  }\r\n\r\n  handleDatesRender(info: any) {\r\n    const start = info.view.activeStart;\r\n    const end = info.view.activeEnd;\r\n    const dates = {\r\n      fromDate: start,\r\n      toDate: end\r\n    };\r\n    this.calendarRangeDatesChanged.emit(dates);\r\n  }\r\n\r\n  private getEventBackground(event: EventApi): string {\r\n    const { type } = event.extendedProps;\r\n    \r\n    switch (type) {\r\n      case 'dayOff':\r\n        return 'linear-gradient(180deg, rgba(127,146,248,1) 0%, rgba(50,65,149,1) 100%)';\r\n      case 'businessHour':\r\n        return 'linear-gradient(250deg, #AAAAAA)';\r\n      case 'Trial':\r\n        if (this.isLessonStatusWithClassroomType(event, LessonStatus.COMPLETED_TRIAL)) {\r\n          return 'linear-gradient(250deg, #64cfdd, #48c4e0, #2cb8e2, #18abe4, #219de3, #1591e2, #1e85e0, #3177dc, #2a69da, #2b5ad6, #334ad0, #3f37c9)';\r\n        } else if (this.isLessonStatusWithClassroomType(event, LessonStatus.ARRANGED_TRIAL)) {\r\n          return 'linear-gradient(0deg, rgba(110,37,255,1) 0%, rgba(167,149,255,1) 100%)';\r\n        } else {\r\n          return 'linear-gradient(90deg, rgba(148,136,247,1) 0%, rgba(163,139,255,1) 100%)';\r\n        }\r\n      case 'Paid':\r\n        if (this.isLessonStatusWithClassroomType(event, LessonStatus.REQUESTED)) {\r\n          return 'linear-gradient(90deg, rgba(108,81,148,1) 0%, rgba(156,80,202,1) 54%)';\r\n        } else {\r\n          return 'linear-gradient(180deg, rgba(122,130,240,1) 0%, rgba(88,60,178,1) 100%)';\r\n        }\r\n      default:\r\n        return 'linear-gradient(270deg, rgba(100,179,221,1) 0%, rgba(49,93,175,1) 100%)';\r\n    }\r\n  }  \r\n\r\n/**\r\n * Checks if the lesson status is with a specific classroom type.\r\n * @param event - The event object.\r\n * @param type - The classroom type to check against. Default is 'Paid'.\r\n * @param status - The lesson status to check against. Default is LessonStatus.ARRANGED.\r\n * @returns True if the lesson status is with the specified classroom type, false otherwise.\r\n */\r\n  private isLessonStatusWithClassroomType(event: any, type = 'Paid', status = LessonStatus.ARRANGED): boolean {\r\n    const lesson = event.extendedProps.lesson;\r\n    const classroom = event.extendedProps.classroom;\r\n  \r\n// Check if the lesson and classroom exist and if their types match\r\n    return (\r\n      lesson &&\r\n      classroom &&\r\n      classroom.type === type &&\r\n      this.lessonService.getStatusNameFromStatusString(lesson.status) === status\r\n    );\r\n  }\r\n\r\n  private createEventContent(event: EventApi): HTMLElement | null {\r\n    if (event.extendedProps.type === 'dayOff') {\r\n      return null;\r\n    } else {\r\n      const eventTitle = event.title;\r\n      const eventIcon = '<i class=\"pi pi-send\"></i>';\r\n      const eventContent = document.createElement('div');\r\n      eventContent.classList.add('fc-event');\r\n      eventContent.innerHTML = `<div class=\"fc-event-main\">' +\r\n        '<div class=\"fc-event-icon\">` + eventIcon + `</div>' +\r\n        '<div class=\"fc-event-title\">` + eventTitle + `</div>' +\r\n        '</div>`;\r\n      return eventContent;\r\n    }\r\n  }\r\n\r\n  private createDayOffEvents(daysOff: any[]): any[] {\r\n    return daysOff.map((dayOff: any) => {\r\n      const from = new Date(dayOff.period.from);\r\n      const to = new Date(dayOff.period.to);\r\n      return {\r\n        title: dayOff.reason,\r\n        start: from,\r\n        display: 'background',\r\n        extendedProps: {\r\n          type: 'dayOff',\r\n          rendering: 'background'\r\n        },\r\n        selectable: false,\r\n        editable: false,\r\n        allDay: true\r\n      };\r\n    });\r\n  }\r\n\r\n  private openDialogWithComponent(component: any, dialogData?: any, width = '270px') {\r\n    this.refs.forEach(ref => ref.close());\r\n    this.refs = [];\r\n    const dialogRef = (this.dialogService.open(component, {\r\n      header: 'Choose a Product',\r\n      width: width,\r\n      showHeader: false,\r\n      dismissableMask: true,\r\n      modal: true,\r\n      contentStyle: {\"max-width\": \"100%\", \"max-height\": \"500px\", \"overflow\": \"auto\", \"border-radius\": \"10px\", \"padding\": \"0px\"},\r\n      baseZIndex: 10000,\r\n      maskStyleClass: 'transparent-mask',\r\n      data: { dialogData: dialogData }\r\n    }));\r\n    this.refs.push(dialogRef);\r\n    dialogRef.onClose.subscribe((data) => {\r\n      console.log('Dialog closed with data:', data);\r\n      if (data) {\r\n        if (data.action && data.type === 'Available') {\r\n          this.addSpecifidDateCalendarEvent(data);\r\n          this.calendarService.setUpdateListener(true);\r\n        }\r\n        if (data.action && data.action === 'block') {\r\n          this.addSpecifidDateCalendarEvent(data);\r\n          this.calendarService.setUpdateListener(true);\r\n        }\r\n        if (data.status) {\r\n          if (data.status === this.lessonStatus.COMPLETED) {\r\n            this.openDialogWithComponent(LessonRatingCalendarDialogComponent, data, '50%');\r\n          } else {\r\n            this.calendarService.setUpdateListener(true);\r\n          }\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  private addSpecifidDateCalendarEvent(data: any) {\r\n    const start = `${data.date} ${data.from}`;\r\n    const end = `${data.date} ${data.to}`;\r\n\r\n    const calendarApi = this.calendarComponent.getApi();\r\n    const event = calendarApi.getEventById('my-event-id');\r\n    console.log(event);\r\n    calendarApi.addEvent({\r\n      title: 'asdsad',\r\n      start: moment(start, 'DD/MM/YYYY HH:mm').format('YYYY-MM-DDTHH:mm:ss'),\r\n      end: moment(end, 'DD/MM/YYYY HH:mm').format('YYYY-MM-DDTHH:mm:ss'),\r\n      display: 'custom-cell-class',\r\n      classNames: ['custom-cell-class'],\r\n      extendedProps: {\r\n        type: 'businessHour',\r\n        rendering: 'custom-cell-class',\r\n        id: 'HERE-LOOK',\r\n      },\r\n      selectable: true,\r\n      editable: false,\r\n      allDay: false\r\n    });\r\n  }\r\n}\r\n", "<p-scrollPanel #scrollPanel [style]=\"{width: '100%'}\" styleClass=\"custombar1 h-30rem lg:h-full\">\r\n<full-calendar #calendar [options]=\"calendarOptions\" >\r\n    <ng-template fcEventContent let-arg='arg'>\r\n        <b>{{ arg.timeText }}</b>\r\n        <i>{{ arg.event.title }}</i>\r\n      </ng-template>\r\n\r\n</full-calendar>\r\n</p-scrollPanel>\r\n"], "mappings": "AAAA,SAAwEA,YAAY,QAA0C,eAAe;AAE7I,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,iBAAiB,MAAM,2BAA2B;AACzD,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAMhC,SAAeC,QAAQ,QAAQ,gCAAgC;AAO/D,SAAQC,aAAa,QAAyB,uBAAuB;AACrE,SAASC,oCAAoC,QAAQ,6FAA6F;AAClJ,SAASC,iCAAiC,QAAQ,uFAAuF;AACzI,SAASC,YAAY,QAAQ,kCAAkC;AAC/D,SAASC,mCAAmC,QAAQ,2FAA2F;AAG/I,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;;;;;;;ICtBzBC,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IADzBH,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAkB;IAClBP,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAE,KAAA,CAAAC,KAAA,CAAqB;;;ADyBhC,MAAMC,YAAY,GAA8B;EAC9C,KAAK,EAAE,CAAC;EACR,KAAK,EAAE,CAAC;EACR,KAAK,EAAE,CAAC;EACR,KAAK,EAAE,CAAC;EACR,KAAK,EAAE,CAAC;EACR,KAAK,EAAE,CAAC;EACR,KAAK,EAAE;CACR;AAED,IAAIC,SAAS,GAAG,CAAC;AACjB,MAAMC,SAAS,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC;AAEhE,OAAO,MAAMC,cAAc,GAAiB,EAC3C;AAED,OAAM,SAAUC,aAAaA,CAAA;EAC3B,OAAOC,MAAM,CAACP,SAAS,EAAE,CAAC;AAC5B;AAQA,OAAM,MAAOQ,mCAAmC;EAuC9CC,YACUC,cAAiC,EACjCC,WAAwB,EACxBC,WAAwB,EACxBC,gBAAkC,EAClCC,aAA4B,EAC5BC,cAA8B,EAC/BC,aAA4B,EAC5BC,eAAgC,EAChCC,oBAA0C;IARzC,KAAAR,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,oBAAoB,GAApBA,oBAAoB;IA/CrB,KAAAC,IAAI,GAAG,IAAI/B,OAAO,EAAE;IACW,KAAAgC,QAAQ,GAAe,EAAgB;IACrE,KAAAC,cAAc,GAAG,MAAM;IACvB,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,eAAe,GAAG,EAAU;IAC5B,KAAAC,gBAAgB,GAAG,KAAK;IAEvB,KAAAC,wBAAwB,GAAG,IAAIlD,YAAY,EAAO;IAClD,KAAAmD,yBAAyB,GAAG,IAAInD,YAAY,EAAO;IAE7D,KAAAoD,eAAe,GAAG,IAAI;IACtB,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,MAAM,GAAU,CACd;MACEjC,KAAK,EAAE,SAAS;MAChBkC,KAAK,EAAE,qBAAqB;MAC5BC,GAAG,EAAE,qBAAqB;MAC1BC,OAAO,EAAE;KACV,EACD;MACEpC,KAAK,EAAE,SAAS;MAChBkC,KAAK,EAAE,qBAAqB;MAC5BC,GAAG,EAAE;KACN,CACF;IACD,KAAAE,eAAe,GAAoB,EAAE;IAEI,KAAAC,iBAAiB,GAA0B,EAA2B;IAE/G,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,IAAI,GAAS,EAAU;IACvB,KAAAC,iBAAiB,GAAU,EAAE;IAC7B,KAAAC,IAAI,GAAuB,EAAE;IAC7B,KAAAC,SAAS,GAAG3D,QAAQ;IACpB,KAAA4D,IAAI,GAAa5D,QAAQ,CAAC6D,IAAI;IAC9B,KAAAC,YAAY,GAAG1D,YAAY;EAWrB;EAEN2D,QAAQA,CAAA;IACN,IAAI,CAACP,IAAI,GAAG,IAAI,CAAC3B,WAAW,CAACmC,eAAe,EAAE;IAC9C,IAAI,CAACJ,IAAI,GAAG,IAAI,CAAC/B,WAAW,CAACmC,eAAe,EAAE,CAACJ,IAAI;IACnD,IAAI,CAACP,eAAe,GAAG;MACrBY,OAAO,EAAE,CAACrE,aAAa,EAAEC,cAAc,EAAEC,iBAAiB,CAAC;MAC3DoE,eAAe,EAAE;QAAEC,OAAO,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAS,CAAE;MACrDC,aAAa,EAAE;QAAEC,IAAI,EAAE,MAAM;QAAEC,MAAM,EAAE,OAAO;QAAEC,KAAK,EAAE;MAAM,CAAE;MAC/DC,WAAW,EAAE,IAAI,CAAC7B,gBAAgB,GAAG,kBAAkB,GAAG,cAAc;MACxE8B,KAAK,EAAE;QACLC,gBAAgB,EAAE;UAChBC,IAAI,EAAE,UAAU;UAChBC,QAAQ,EAAE;;OAEb;MACDC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,KAAK;MACjBC,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE,UAAU;MACxBC,iBAAiB,EAAE,EAAE;MACrBC,YAAY,EAAE,IAAI;MAClBC,YAAY,EAAE,IAAI;MAClBC,gBAAgB,EAAE,EAAE;MACpBC,WAAW,EAAE,UAAU;MAAE;MACzBC,UAAU,EAAE,UAAU;MACtBC,QAAQ,EAAE,CAAC;MACXC,MAAM,EAAE,IAAI,CAACnD,cAAc;MAC3BoD,aAAa,EAAE,MAAM;MACrBC,cAAc,EAAE,GAAG;MACnBC,mBAAmB,EAAE,GAAG;MACxB5C,MAAM,EAAE,IAAI,CAACD,aAAa;MAC1B8C,aAAa,EAAEvE,cAAc;MAC7BwE,MAAM,EAAE,IAAI,CAACC,gBAAgB,CAACC,IAAI,CAAC,IAAI,CAAC;MACxCC,UAAU,EAAE,IAAI,CAACC,gBAAgB,CAACF,IAAI,CAAC,IAAI,CAAC;MAC5CG,SAAS,EAAE,IAAI,CAACC,YAAY,CAACJ,IAAI,CAAC,IAAI,CAAC;MACvCK,aAAa,EAAE;QACbC,YAAY,EAAE;UACZC,IAAI,EAAE,QAAQ;UACdC,KAAK,EAAEA,CAAA,KAAK;YACV;UAAA;;OAGL;MACDC,aAAa,EAAGC,IAAI,IAAI;QACtB,MAAMC,YAAY,GAAGD,IAAI,CAACE,EAAE;QAC5BD,YAAY,CAACE,KAAK,CAACC,eAAe,GAAG,IAAI,CAACC,kBAAkB,CAACL,IAAI,CAAC5F,KAAK,CAAC;QACxE6F,YAAY,CAACE,KAAK,CAACG,YAAY,GAAG,KAAK;QACvCL,YAAY,CAACE,KAAK,CAACI,OAAO,GAAG,GAAG;QAChCN,YAAY,CAACE,KAAK,CAACK,OAAO,GAAG,KAAK;QAClCP,YAAY,CAACE,KAAK,CAACM,MAAM,GAAG,GAAG;QAC/B,MAAMC,YAAY,GAAG,IAAI,CAACC,kBAAkB,CAACX,IAAI,CAAC5F,KAAK,CAAC;QACxD,IAAIsG,YAAY,EAAE;UAChB;QAAA;MAEJ,CAAC;MACDA,YAAY,EAAGV,IAAI,IAAI;QACrB,IAAIA,IAAI,CAAC5F,KAAK,CAACwG,aAAa,CAAC3C,IAAI,KAAK,QAAQ,EAAE;UAC9C,OAAO;YAAE4C,IAAI,EAAE;UAAE,CAAE;QACrB,CAAC,MAAM,IAAIb,IAAI,CAAC5F,KAAK,CAACwG,aAAa,CAACE,MAAM,EAAE;UAC1C,MAAMC,SAAS,GAAGf,IAAI,CAAC5F,KAAK,CAACwG,aAAa,CAACG,SAAS;UACpD,MAAMD,MAAM,GAAGd,IAAI,CAAC5F,KAAK,CAACwG,aAAa,CAACE,MAAM;UAC9C,MAAMzG,KAAK,GAAG,IAAI,CAACa,WAAW,CAAC8F,SAAS,GAAGhB,IAAI,CAAC5F,KAAK,CAACC,KAAK,GAAG,IAAI,CAACe,gBAAgB,CAAC6F,wBAAwB,CAACF,SAAS,CAACG,iBAAiB,CAAC;UACzI,MAAMC,OAAO,GAAG,IAAI,CAAC/F,gBAAgB,CAACgG,gBAAgB,CAACL,SAAS,CAAC;UACjE,MAAMM,QAAQ,GAAGF,OAAO,GAAG,cAAc,GAAG,IAAI,CAAC9F,aAAa,CAACiG,6BAA6B,CAACR,MAAM,CAACS,MAAM,CAAC,CAACC,WAAW,EAAE,CAACC,QAAQ,CAAChI,YAAY,CAACiI,SAAS,CAACF,WAAW,EAAE,CAAC,GAAG,iBAAiB,GAAG,IAAI,CAACnG,aAAa,CAACiG,6BAA6B,CAACR,MAAM,CAACS,MAAM,CAAC;UAC9P,IAAIT,MAAM,CAACa,QAAQ,IAAI,IAAI,EAAE;YAC3B,MAAMC,eAAe,GAAGd,MAAM,CAACa,QAAQ,GAAG,IAAI,GAAG,QAAQ,GAAG,EAAE;YAC9D,OAAO;cACLd,IAAI,EAAE;;;kBAGFxG,KAAK;uDACgC,GAAEgH,QAAQ,GAAG;;;4FAGwB,GAAEO,eAAe,GAAG;0BACtFxI,MAAM,CAAC0H,MAAM,CAACe,YAAY,CAAC,CAACC,MAAM,CAAC,QAAQ,CAAC;0BAC5C,IAAI,CAACxG,cAAc,CAACyG,+BAA+B,CAACjB,MAAM,CAACa,QAAQ,CAAC;;;aAGjF;UACH;UACA,OAAO;YACLd,IAAI,EAAE;;uDAEqC,GAAEQ,QAAQ,GAAG;;+DAELhH,KAAK;;wBAE5CjB,MAAM,CAAC0H,MAAM,CAACe,YAAY,CAAC,CAACC,MAAM,CAAC,QAAQ,CAAC;wBAC5C,IAAI,CAACxG,cAAc,CAACyG,+BAA+B,CAACjB,MAAM,CAACa,QAAQ,CAAC;;;WAGjF;QACH,CAAC,MAAM;UACL,OAAO;YAAEd,IAAI,EAAE;UAAE,CAAE;QACrB;MACF,CAAC;MACDmB,WAAW,EAAE,SAAAA,CAAShC,IAAI;QACxB;QACA,OAAO5G,MAAM,CAAC4G,IAAI,CAACxD,GAAG,CAAC,CAACyF,IAAI,CAAC7I,MAAM,CAAC4G,IAAI,CAACzD,KAAK,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC;MAChE,CAAC;MACD2F,QAAQ,EAAE,IAAI,CAACC,iBAAiB,CAAC7C,IAAI,CAAC,IAAI;KAC3C;IAGD,IAAI,CAAC5D,IAAI,CAAC0G,IAAI,GAAG,IAAI,CAAC5G,eAAe,CAAC6G,qBAAqB,CAACC,SAAS,CAAEC,GAAG,IAAI;MAC5E,MAAMC,UAAU,GAAGD,GAAG;MACtBE,OAAO,CAACC,GAAG,CAACH,GAAG,CAAC;IAClB,CAAC,CAAC;EACJ;EAEAI,qBAAqBA,CAAA;IACnB,MAAMC,WAAW,GAAG,IAAI,CAACjG,iBAAiB,CAACkG,MAAM,EAAE;IACnD,IAAID,WAAW,EAAE,CACjB;EACF;EAEAE,WAAWA,CAAA;IAET,MAAMF,WAAW,GAAG,IAAI,CAACjG,iBAAiB,CAACkG,MAAM,EAAE;IACnD,IAAID,WAAW,EAAE;MACfA,WAAW,CAACG,SAAS,CAAC,QAAQ,EAAE,IAAI,CAACnH,cAAc,CAAC;MACpDgH,WAAW,CAACG,SAAS,CAAC,eAAe,EAAE,MAAM,CAAC;MAC9CH,WAAW,CAACI,eAAe,EAAE;MAC7B,IAAI,IAAI,CAAClH,aAAa,CAACmH,MAAM,GAAG,CAAC,EAAE;QACjCL,WAAW,CAACG,SAAS,CAAC,eAAe,EAAE,IAAI,CAACjH,aAAa,CAAC;QAC1D8G,WAAW,CAACG,SAAS,CAAC,mBAAmB,EAAE,UAAU/C,IAAS;UAC5D,OAAO,mBAAmB;QAC5B,CAAC,CAAC;MACJ,CAAC,MAAM;QACL4C,WAAW,CAACG,SAAS,CAAC,mBAAmB,EAAE,UAAU/C,IAAS;UAC5D,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;MACA,IAAI,IAAI,CAAC/C,IAAI,KAAK,IAAI,CAACD,SAAS,CAACkG,OAAO,EAAE;QACxCN,WAAW,CAACG,SAAS,CAAC,kBAAkB,EAAE,IAAI,CAACjH,aAAa,CAAC;MAC/D;MAEA;MACA8G,WAAW,CAACO,cAAc,CAAC,IAAI,CAACtH,YAAY,CAAC;MAE7C+G,WAAW,CAACO,cAAc,CAAC,IAAI,CAACpH,YAAY,CAAC;MAC7C;MAEA;MACA;IAEF;EACF;EAEAqH,WAAWA,CAAA;IACT,IAAI,CAAC1H,IAAI,CAAC2H,WAAW,EAAE;EACzB;EAEAC,eAAeA,CAAA;IAEb,MAAMV,WAAW,GAAG,IAAI,CAACjG,iBAAiB,CAACkG,MAAM,EAAE;IACnD,IAAID,WAAW,EAAE;MACf;IAAA;EAGJ;EAEAW,oBAAoBA,CAAA;IAClB,IAAI,CAACnH,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;EAC9C;EAEAoH,oBAAoBA,CAAA;IAClB,MAAM;MAAE9G;IAAe,CAAE,GAAG,IAAI;IAChCA,eAAe,CAACyB,QAAQ,GAAG,CAACzB,eAAe,CAACyB,QAAQ;EACtD;EAEAkB,gBAAgBA,CAACoE,UAAyB;IACxC,MAAMb,WAAW,GAAGa,UAAU,CAACC,IAAI,CAAC/H,QAAQ;IAC5C,MAAMgI,iBAAiB,GAAG;MACxB,GAAGF,UAAU;MACbG,OAAO,EAAE,IAAI,CAAC5H;KACf;IACDyG,OAAO,CAACC,GAAG,CAACiB,iBAAiB,CAAC;IAE9B,MAAME,cAAc,GAAGzK,MAAM,CAACuK,iBAAiB,CAACnH,GAAG,CAAC,CAACyF,IAAI,CAAC7I,MAAM,CAACuK,iBAAiB,CAACpH,KAAK,CAAC,EAAE,OAAO,CAAC;IACnG,MAAMuH,WAAW,GAAG1K,MAAM,EAAE;IAC5B,MAAM2K,MAAM,GAAG3K,MAAM,CAACuK,iBAAiB,CAACK,QAAQ,CAAC,CAACC,QAAQ,CAACH,WAAW,CAAC;IAEvE,IAAIC,MAAM,IAAI,IAAI,CAAC7I,WAAW,CAAC8F,SAAS,EAAE;MACxC,IAAI,CAACkD,uCAAuC,CAC1C,yFAAyF,EACzF,qCAAqC,CACtC;MACD;IACF;IAEA,IAAIL,cAAc,GAAG,CAAC,EAAE;MACtB,IAAI,CAACK,uCAAuC,CAC1C,oGAAoG,EACpG,oCAAoC,CACrC;IACH,CAAC,MAAM;MACL,IAAI,CAACC,uBAAuB,CAAC5K,oCAAoC,EAAEoK,iBAAiB,CAAC;IACvF;IACA;EACF;EAEQO,uCAAuCA,CAACE,OAAe,EAAEC,IAAY;IAC3E,IAAI,CAAC5I,oBAAoB,CAAC6I,aAAa,CAAC;MACtCC,cAAc,EAAEH,OAAO;MACvBI,cAAc,EAAE,IAAI;MACpBC,cAAc,EAAE,EAAE;MAClBC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,QAAQ;MACpBC,WAAW,EAAEP,IAAI;MACjBQ,MAAM,EAAE,eAAe;MACvBC,WAAW,EAAE;KACd,CAAC;IACF,IAAI,CAACrJ,oBAAoB,CAACsJ,IAAI,EAAE;EAClC;EAEAvF,gBAAgBA,CAACwF,SAAwB;IACvC;IACA,MAAM5K,KAAK,GAAG4K,SAAS,CAAC5K,KAAK;IAC7BqI,OAAO,CAACC,GAAG,CAACtI,KAAK,CAACwG,aAAa,CAAC;IAChC,QAAQxG,KAAK,CAACwG,aAAa,CAAC3C,IAAI;MAC9B,KAAK,QAAQ;QACXwE,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;QACpC;MACF,KAAK,QAAQ;QACX,IAAI,CAACpH,cAAc,CAAC6I,uBAAuB,CAAC,IAAI,CAAC5I,aAAa,EAAE/B,iCAAiC,EAAEyL,SAAS,EAC1G;UAAE1J,aAAa,EAAE,IAAI,CAACA,aAAa;UAAE,GAAGnB,KAAK,CAACwG;QAAa,CAAE,CAAC;QAChE;MACF,KAAK,cAAc;QACjB,IAAI,CAACuD,uBAAuB,CAAC5K,oCAAoC,EAAEa,KAAK,CAAC;QACzE;MACF;QACEqI,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IAC7C;EACF;EAEAhD,YAAYA,CAACpD,MAAkB;IAC7B,IAAI,CAACD,aAAa,GAAGC,MAAM;IAC3B,IAAI,CAACrB,cAAc,CAACiK,aAAa,EAAE;EACrC;EAEA/C,iBAAiBA,CAACnC,IAAS;IACzB,MAAMzD,KAAK,GAAGyD,IAAI,CAAC0D,IAAI,CAACyB,WAAW;IACnC,MAAM3I,GAAG,GAAGwD,IAAI,CAAC0D,IAAI,CAAC0B,SAAS;IAC/B,MAAMC,KAAK,GAAG;MACZC,QAAQ,EAAE/I,KAAK;MACfgJ,MAAM,EAAE/I;KACT;IACD,IAAI,CAACL,yBAAyB,CAACqJ,IAAI,CAACH,KAAK,CAAC;EAC5C;EAEQhF,kBAAkBA,CAACjG,KAAe;IACxC,MAAM;MAAE6D;IAAI,CAAE,GAAG7D,KAAK,CAACwG,aAAa;IAEpC,QAAQ3C,IAAI;MACV,KAAK,QAAQ;QACX,OAAO,yEAAyE;MAClF,KAAK,cAAc;QACjB,OAAO,kCAAkC;MAC3C,KAAK,OAAO;QACV,IAAI,IAAI,CAACwH,+BAA+B,CAACrL,KAAK,EAAEX,YAAY,CAACiM,eAAe,CAAC,EAAE;UAC7E,OAAO,qIAAqI;QAC9I,CAAC,MAAM,IAAI,IAAI,CAACD,+BAA+B,CAACrL,KAAK,EAAEX,YAAY,CAACkM,cAAc,CAAC,EAAE;UACnF,OAAO,wEAAwE;QACjF,CAAC,MAAM;UACL,OAAO,0EAA0E;QACnF;MACF,KAAK,MAAM;QACT,IAAI,IAAI,CAACF,+BAA+B,CAACrL,KAAK,EAAEX,YAAY,CAACiI,SAAS,CAAC,EAAE;UACvE,OAAO,uEAAuE;QAChF,CAAC,MAAM;UACL,OAAO,yEAAyE;QAClF;MACF;QACE,OAAO,yEAAyE;IACpF;EACF;EAEF;;;;;;;EAOU+D,+BAA+BA,CAACrL,KAAU,EAAE6D,IAAI,GAAG,MAAM,EAAEsD,MAAM,GAAG9H,YAAY,CAACmM,QAAQ;IAC/F,MAAM9E,MAAM,GAAG1G,KAAK,CAACwG,aAAa,CAACE,MAAM;IACzC,MAAMC,SAAS,GAAG3G,KAAK,CAACwG,aAAa,CAACG,SAAS;IAEnD;IACI,OACED,MAAM,IACNC,SAAS,IACTA,SAAS,CAAC9C,IAAI,KAAKA,IAAI,IACvB,IAAI,CAAC5C,aAAa,CAACiG,6BAA6B,CAACR,MAAM,CAACS,MAAM,CAAC,KAAKA,MAAM;EAE9E;EAEQZ,kBAAkBA,CAACvG,KAAe;IACxC,IAAIA,KAAK,CAACwG,aAAa,CAAC3C,IAAI,KAAK,QAAQ,EAAE;MACzC,OAAO,IAAI;IACb,CAAC,MAAM;MACL,MAAM4H,UAAU,GAAGzL,KAAK,CAACC,KAAK;MAC9B,MAAMyL,SAAS,GAAG,4BAA4B;MAC9C,MAAMpF,YAAY,GAAGqF,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAClDtF,YAAY,CAACuF,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;MACtCxF,YAAY,CAACyF,SAAS,GAAG;qCACM,GAAGL,SAAS,GAAG;sCACd,GAAGD,UAAU,GAAG;gBACtC;MACV,OAAOnF,YAAY;IACrB;EACF;EAEQ0F,kBAAkBA,CAACC,OAAc;IACvC,OAAOA,OAAO,CAACC,GAAG,CAAEC,MAAW,IAAI;MACjC,MAAMC,IAAI,GAAG,IAAI/L,IAAI,CAAC8L,MAAM,CAACE,MAAM,CAACD,IAAI,CAAC;MACzC,MAAME,EAAE,GAAG,IAAIjM,IAAI,CAAC8L,MAAM,CAACE,MAAM,CAACC,EAAE,CAAC;MACrC,OAAO;QACLrM,KAAK,EAAEkM,MAAM,CAACI,MAAM;QACpBpK,KAAK,EAAEiK,IAAI;QACX/J,OAAO,EAAE,YAAY;QACrBmE,aAAa,EAAE;UACb3C,IAAI,EAAE,QAAQ;UACd2I,SAAS,EAAE;SACZ;QACDtI,UAAU,EAAE,KAAK;QACjBD,QAAQ,EAAE,KAAK;QACfwI,MAAM,EAAE;OACT;IACH,CAAC,CAAC;EACJ;EAEQ1C,uBAAuBA,CAAC2C,SAAc,EAAEC,UAAgB,EAAEC,KAAK,GAAG,OAAO;IAC/E,IAAI,CAACjK,IAAI,CAACkK,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,KAAK,EAAE,CAAC;IACrC,IAAI,CAACpK,IAAI,GAAG,EAAE;IACd,MAAMqK,SAAS,GAAI,IAAI,CAAC7L,aAAa,CAAC8L,IAAI,CAACP,SAAS,EAAE;MACpDQ,MAAM,EAAE,kBAAkB;MAC1BN,KAAK,EAAEA,KAAK;MACZtC,UAAU,EAAE,KAAK;MACjB6C,eAAe,EAAE,IAAI;MACrBC,KAAK,EAAE,IAAI;MACXC,YAAY,EAAE;QAAC,WAAW,EAAE,MAAM;QAAE,YAAY,EAAE,OAAO;QAAE,UAAU,EAAE,MAAM;QAAE,eAAe,EAAE,MAAM;QAAE,SAAS,EAAE;MAAK,CAAC;MACzHC,UAAU,EAAE,KAAK;MACjBC,cAAc,EAAE,kBAAkB;MAClCC,IAAI,EAAE;QAAEb,UAAU,EAAEA;MAAU;KAC/B,CAAE;IACH,IAAI,CAAChK,IAAI,CAAC8K,IAAI,CAACT,SAAS,CAAC;IACzBA,SAAS,CAACU,OAAO,CAACxF,SAAS,CAAEsF,IAAI,IAAI;MACnCnF,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEkF,IAAI,CAAC;MAC7C,IAAIA,IAAI,EAAE;QACR,IAAIA,IAAI,CAAC/C,MAAM,IAAI+C,IAAI,CAAC3J,IAAI,KAAK,WAAW,EAAE;UAC5C,IAAI,CAAC8J,4BAA4B,CAACH,IAAI,CAAC;UACvC,IAAI,CAACpM,eAAe,CAACwM,iBAAiB,CAAC,IAAI,CAAC;QAC9C;QACA,IAAIJ,IAAI,CAAC/C,MAAM,IAAI+C,IAAI,CAAC/C,MAAM,KAAK,OAAO,EAAE;UAC1C,IAAI,CAACkD,4BAA4B,CAACH,IAAI,CAAC;UACvC,IAAI,CAACpM,eAAe,CAACwM,iBAAiB,CAAC,IAAI,CAAC;QAC9C;QACA,IAAIJ,IAAI,CAACrG,MAAM,EAAE;UACf,IAAIqG,IAAI,CAACrG,MAAM,KAAK,IAAI,CAACpE,YAAY,CAAC8K,SAAS,EAAE;YAC/C,IAAI,CAAC9D,uBAAuB,CAACzK,mCAAmC,EAAEkO,IAAI,EAAE,KAAK,CAAC;UAChF,CAAC,MAAM;YACL,IAAI,CAACpM,eAAe,CAACwM,iBAAiB,CAAC,IAAI,CAAC;UAC9C;QACF;MACF;IACF,CAAC,CAAC;EACJ;EAEQD,4BAA4BA,CAACH,IAAS;IAC5C,MAAMrL,KAAK,GAAG,GAAGqL,IAAI,CAACM,IAAI,IAAIN,IAAI,CAACpB,IAAI,EAAE;IACzC,MAAMhK,GAAG,GAAG,GAAGoL,IAAI,CAACM,IAAI,IAAIN,IAAI,CAAClB,EAAE,EAAE;IAErC,MAAM9D,WAAW,GAAG,IAAI,CAACjG,iBAAiB,CAACkG,MAAM,EAAE;IACnD,MAAMzI,KAAK,GAAGwI,WAAW,CAACuF,YAAY,CAAC,aAAa,CAAC;IACrD1F,OAAO,CAACC,GAAG,CAACtI,KAAK,CAAC;IAClBwI,WAAW,CAACwF,QAAQ,CAAC;MACnB/N,KAAK,EAAE,QAAQ;MACfkC,KAAK,EAAEnD,MAAM,CAACmD,KAAK,EAAE,kBAAkB,CAAC,CAACuF,MAAM,CAAC,qBAAqB,CAAC;MACtEtF,GAAG,EAAEpD,MAAM,CAACoD,GAAG,EAAE,kBAAkB,CAAC,CAACsF,MAAM,CAAC,qBAAqB,CAAC;MAClErF,OAAO,EAAE,mBAAmB;MAC5B4L,UAAU,EAAE,CAAC,mBAAmB,CAAC;MACjCzH,aAAa,EAAE;QACb3C,IAAI,EAAE,cAAc;QACpB2I,SAAS,EAAE,mBAAmB;QAC9B0B,EAAE,EAAE;OACL;MACDhK,UAAU,EAAE,IAAI;MAChBD,QAAQ,EAAE,KAAK;MACfwI,MAAM,EAAE;KACT,CAAC;EACJ;EAAC,QAAA0B,CAAA,G;qBA5bUxN,mCAAmC,EAAAnB,EAAA,CAAA4O,iBAAA,CAAA5O,EAAA,CAAA6O,iBAAA,GAAA7O,EAAA,CAAA4O,iBAAA,CAAAE,EAAA,CAAAC,WAAA,GAAA/O,EAAA,CAAA4O,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAAjP,EAAA,CAAA4O,iBAAA,CAAAM,EAAA,CAAAC,gBAAA,GAAAnP,EAAA,CAAA4O,iBAAA,CAAAQ,EAAA,CAAAC,aAAA,GAAArP,EAAA,CAAA4O,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAAvP,EAAA,CAAA4O,iBAAA,CAAAY,EAAA,CAAA9P,aAAA,GAAAM,EAAA,CAAA4O,iBAAA,CAAAa,EAAA,CAAAC,eAAA,GAAA1P,EAAA,CAAA4O,iBAAA,CAAAe,EAAA,CAAAC,oBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAnC1O,mCAAmC;IAAA2O,SAAA;IAAAC,SAAA,WAAAC,0CAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;qCAFnC,CAACvQ,aAAa,CAAC,GAAAM,EAAA,CAAAmQ,oBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6CAAAP,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCpD5BjQ,EADA,CAAAC,cAAA,0BAAgG,0BAC1C;QAClDD,EAAA,CAAAyQ,UAAA,IAAAC,0DAAA,yBAA0C;QAM9C1Q,EADA,CAAAG,YAAA,EAAgB,EACA;;;QARYH,EAAA,CAAA2Q,UAAA,CAAA3Q,EAAA,CAAA4Q,eAAA,IAAAC,GAAA,EAAyB;QAC5B7Q,EAAA,CAAAI,SAAA,GAA2B;QAA3BJ,EAAA,CAAA8Q,UAAA,YAAAZ,GAAA,CAAApN,eAAA,CAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}