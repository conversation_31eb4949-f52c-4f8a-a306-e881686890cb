{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nconst BACKEND_LMS_TEACHER_URL = environment.apiUrl + \"/LMS/\";\nconst BACKEND_LMS_URL = environment.apiUrl + \"/LMS/\";\nexport let LingoletteService = /*#__PURE__*/(() => {\n  class LingoletteService {\n    constructor(http, router) {\n      this.http = http;\n      this.router = router;\n    }\n    getLingoletteUserList() {\n      return this.http.post(BACKEND_LMS_URL + \"LingoletteUserList\", {});\n    }\n    createLingoletteUser(userInfo) {\n      return this.http.post(BACKEND_LMS_URL + \"LingoletteUserAdd\", userInfo);\n    }\n    createLingoletteUserSession(userId) {\n      return this.http.post(BACKEND_LMS_URL + \"LingoletteUserSession\", {\n        userId: userId\n      });\n    }\n    removeLingoletteUser(userId) {\n      return this.http.post(BACKEND_LMS_URL + \"LingoletteUserRemove\", {\n        userId: userId\n      });\n    }\n    static #_ = this.ɵfac = function LingoletteService_Factory(t) {\n      return new (t || LingoletteService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: LingoletteService,\n      factory: LingoletteService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return LingoletteService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}