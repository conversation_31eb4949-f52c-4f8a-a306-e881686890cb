{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nconst BACKEND_URL_LMS = environment.apiUrl + \"/LMS/\";\nexport class CertificateService {\n  // public dummyCertificates: Certificate[] = [\n  //   {\n  //     id: \"1\",\n  //     language: \"English\",\n  //     level: \"B1\",\n  //     hours: \"12\",\n  //     type: \"completion\",\n  //     filePath: \"/assets/files/Dia.pdf\"\n  //   },\n  //   { \n  //     id: \"2\",\n  //     language: \"English\",\n  //     level: \"B2\",\n  //     hours: \"12\",\n  //     type: \"completion\",\n  //     filePath: \"/assets/files/Dia.pdf\"\n  //   },\n  //   {\n  //     id: \"3\",\n  //     language: \"Spanish\",\n  //     level: \"A1\",\n  //     hours: \"2\",\n  //     type: \"attendance\",\n  //     filePath: \"/assets/files/Dia.pdf\"\n  //   },\n  //   {\n  //     id: \"4\",\n  //     language: \"Italian\",\n  //     level: \"A1\",\n  //     hours: \"4\",\n  //     type: \"attendance\",\n  //     filePath: \"/assets/files/Dia.pdf\"\n  //   },\n  // ]\n  constructor(http) {\n    this.http = http;\n  }\n  getListOfCertifications() {\n    return this.http.post(BACKEND_URL_LMS + 'GenerateListOfCertifications', {});\n  }\n  generateCertificationPdf(level, language, type, hours) {\n    const requestBody = {\n      level: level,\n      language: language,\n      hours: hours\n    };\n    if (type == \"completion\") {\n      return this.http.post(BACKEND_URL_LMS + 'CreateCertOfCompletionPDFFile', requestBody, {\n        responseType: 'text'\n      });\n    } else {\n      return this.http.post(BACKEND_URL_LMS + 'CreateCertOfAttendancePDFFile', requestBody, {\n        responseType: 'text'\n      });\n    }\n  }\n  static #_ = this.ɵfac = function CertificateService_Factory(t) {\n    return new (t || CertificateService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: CertificateService,\n    factory: CertificateService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["environment", "BACKEND_URL_LMS", "apiUrl", "CertificateService", "constructor", "http", "getListOfCertifications", "post", "generateCertificationPdf", "level", "language", "type", "hours", "requestBody", "responseType", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\services\\certificate.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { Certificate } from '../models/certificate.model';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { environment } from 'src/environments/environment';\r\nimport { User, UserAvailability, UserAvailabilityOff, UserAvailabilityType, UserStatus } from '../models/user.model';\r\nconst BACKEND_URL_LMS = environment.apiUrl + \"/LMS/\";\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class CertificateService {\r\n  // public dummyCertificates: Certificate[] = [\r\n  //   {\r\n  //     id: \"1\",\r\n  //     language: \"English\",\r\n  //     level: \"B1\",\r\n  //     hours: \"12\",\r\n  //     type: \"completion\",\r\n  //     filePath: \"/assets/files/Dia.pdf\"\r\n  //   },\r\n  //   { \r\n  //     id: \"2\",\r\n  //     language: \"English\",\r\n  //     level: \"B2\",\r\n  //     hours: \"12\",\r\n  //     type: \"completion\",\r\n  //     filePath: \"/assets/files/Dia.pdf\"\r\n  //   },\r\n  //   {\r\n  //     id: \"3\",\r\n  //     language: \"Spanish\",\r\n  //     level: \"A1\",\r\n  //     hours: \"2\",\r\n  //     type: \"attendance\",\r\n  //     filePath: \"/assets/files/Dia.pdf\"\r\n  //   },\r\n  //   {\r\n  //     id: \"4\",\r\n  //     language: \"Italian\",\r\n  //     level: \"A1\",\r\n  //     hours: \"4\",\r\n  //     type: \"attendance\",\r\n  //     filePath: \"/assets/files/Dia.pdf\"\r\n  //   },\r\n  // ]\r\n  constructor(private http: HttpClient) { }\r\n\r\n  public getListOfCertifications() {\r\n    return this.http.post<any>(BACKEND_URL_LMS + 'GenerateListOfCertifications', {});\r\n  }\r\n\r\n  public generateCertificationPdf(level: string, language: string, type: string, hours?: number) {\r\n    const requestBody = {\r\n      level: level,\r\n      language: language,\r\n      hours: hours\r\n    };\r\n    if (type == \"completion\") {\r\n      return this.http.post<any>(BACKEND_URL_LMS + 'CreateCertOfCompletionPDFFile', requestBody, { responseType: 'text' as 'json' });\r\n    } else {\r\n      return this.http.post<any>(BACKEND_URL_LMS + 'CreateCertOfAttendancePDFFile', requestBody, { responseType: 'text' as 'json' });\r\n    }\r\n  }\r\n}\r\n"], "mappings": "AAKA,SAASA,WAAW,QAAQ,8BAA8B;;;AAE1D,MAAMC,eAAe,GAAGD,WAAW,CAACE,MAAM,GAAG,OAAO;AAIpD,OAAM,MAAOC,kBAAkB;EAC7B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAgB;EAEjCC,uBAAuBA,CAAA;IAC5B,OAAO,IAAI,CAACD,IAAI,CAACE,IAAI,CAAMN,eAAe,GAAG,8BAA8B,EAAE,EAAE,CAAC;EAClF;EAEOO,wBAAwBA,CAACC,KAAa,EAAEC,QAAgB,EAAEC,IAAY,EAAEC,KAAc;IAC3F,MAAMC,WAAW,GAAG;MAClBJ,KAAK,EAAEA,KAAK;MACZC,QAAQ,EAAEA,QAAQ;MAClBE,KAAK,EAAEA;KACR;IACD,IAAID,IAAI,IAAI,YAAY,EAAE;MACxB,OAAO,IAAI,CAACN,IAAI,CAACE,IAAI,CAAMN,eAAe,GAAG,+BAA+B,EAAEY,WAAW,EAAE;QAAEC,YAAY,EAAE;MAAgB,CAAE,CAAC;IAChI,CAAC,MAAM;MACL,OAAO,IAAI,CAACT,IAAI,CAACE,IAAI,CAAMN,eAAe,GAAG,+BAA+B,EAAEY,WAAW,EAAE;QAAEC,YAAY,EAAE;MAAgB,CAAE,CAAC;IAChI;EACF;EAAC,QAAAC,CAAA,G;qBApDUZ,kBAAkB,EAAAa,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAlBjB,kBAAkB;IAAAkB,OAAA,EAAlBlB,kBAAkB,CAAAmB,IAAA;IAAAC,UAAA,EAFjB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}