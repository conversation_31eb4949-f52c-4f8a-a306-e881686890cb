{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { LibraryComponent } from './library/library.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: LibraryComponent\n}];\nexport class LibraryRoutingModule {\n  static #_ = this.ɵfac = function LibraryRoutingModule_Factory(t) {\n    return new (t || LibraryRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: LibraryRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(LibraryRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "LibraryComponent", "routes", "path", "component", "LibraryRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\library\\library-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { LibraryComponent } from './library/library.component';\r\n\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '', component: LibraryComponent,\r\n  },\r\n\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class LibraryRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,gBAAgB,QAAQ,6BAA6B;;;AAG9D,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEH;CACtB,CAEF;AAMD,OAAM,MAAOI,oBAAoB;EAAA,QAAAC,CAAA,G;qBAApBD,oBAAoB;EAAA;EAAA,QAAAE,EAAA,G;UAApBF;EAAoB;EAAA,QAAAG,EAAA,G;cAHrBR,YAAY,CAACS,QAAQ,CAACP,MAAM,CAAC,EAC7BF,YAAY;EAAA;;;2EAEXK,oBAAoB;IAAAK,OAAA,GAAAC,EAAA,CAAAX,YAAA;IAAAY,OAAA,GAFrBZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}