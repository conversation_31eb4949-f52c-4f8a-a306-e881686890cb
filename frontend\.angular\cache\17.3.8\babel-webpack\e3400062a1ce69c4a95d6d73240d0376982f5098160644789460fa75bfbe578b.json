{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { SubSink } from 'subsink';\nimport * as moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/calendar.service\";\nimport * as i2 from \"src/app/core/services/auth.service\";\nimport * as i3 from \"src/app/core/services/classroom.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"../../../block-viewer/block-viewer.component\";\nconst _c0 = [\"daysTableWrapper\"];\nconst _c1 = (a0, a1, a2) => ({\n  \"reversed-gradient\": a0,\n  \"block-gradient\": a1,\n  \"availability-cal\": a2\n});\nconst _c2 = (a0, a1, a2, a3, a4, a5, a6, a7) => ({\n  \"active\": a0,\n  \"high-bg\": a1,\n  \"low-bg\": a2,\n  \"is-past\": a3,\n  \"no-bg\": a4,\n  \"circle\": a5,\n  \"today\": a6,\n  \"day-circle\": a7\n});\nconst _c3 = (a0, a1, a2, a3, a4, a5, a6) => ({\n  \"active\": a0,\n  \"high-bg\": a1,\n  \"low-bg\": a2,\n  \"is-past\": a3,\n  \"no-bg\": a4,\n  \"circle\": a5,\n  \"day-circle\": a6\n});\nfunction CalendarAgendaComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function CalendarAgendaComponent_ng_container_5_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBackwardMonth());\n    });\n    i0.ɵɵelement(2, \"span\", 10)(3, \"span\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function CalendarAgendaComponent_ng_container_5_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goForwardMonth());\n    });\n    i0.ɵɵelement(5, \"span\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction CalendarAgendaComponent_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const day_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(day_r3);\n  }\n}\nfunction CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_container_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 19);\n    i0.ɵɵelement(2, \"i\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_container_2_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 21)(2, \"div\", 22)(3, \"div\", 23)(4, \"div\", 24);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const status_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"top\", ctx_r1.getItemsPositionTop(i_r7), \"px\")(\"right\", ctx_r1.getItemsPositionRight(i_r7), \"px\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", status_r6.status + \"-gradient-lesson-bg\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", status_r6.count, \"\");\n  }\n}\nfunction CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 17)(2, \"div\", 18);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_container_2_ng_container_4_Template, 3, 0, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_container_2_ng_container_5_Template, 6, 6, \"ng-container\", 8);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const day_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(day_r5.availability);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction8(6, _c2, ctx_r1.selectedDay === day_r5.number, ctx_r1.isHighAvailability(day_r5.number) && !ctx_r1.isDayInThePast(day_r5.number), !ctx_r1.isHighAvailability(day_r5.number) && ctx_r1.showAvailability && !ctx_r1.isDayInThePast(day_r5.number), ctx_r1.showAvailability && ctx_r1.isDayInThePast(day_r5.number), ctx_r1.isDayOff(day_r5.number), ctx_r1.showAvailability, !ctx_r1.showAvailability, ctx_r1.getStatusArray(ctx_r1.getTodaysLessons(day_r5.number)).length > 0 || !ctx_r1.showAvailability));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(day_r5.number);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedDay === day_r5.number && ctx_r1.showAvailability);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getStatusArray(ctx_r1.getTodaysLessons(day_r5.number)));\n  }\n}\nfunction CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_span_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 19);\n    i0.ɵɵelement(2, \"i\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 17)(1, \"div\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_span_0_ng_container_3_Template, 3, 0, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(day_r5.availability);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction7(5, _c3, ctx_r1.selectedDay === day_r5.number, ctx_r1.isHighAvailability(day_r5.number) && !ctx_r1.isDayInThePast(day_r5.number), !ctx_r1.isHighAvailability(day_r5.number) && ctx_r1.showAvailability && !ctx_r1.isDayInThePast(day_r5.number), ctx_r1.showAvailability && ctx_r1.isDayInThePast(day_r5.number), ctx_r1.isDayOff(day_r5.number), ctx_r1.showAvailability, ctx_r1.getStatusArray(ctx_r1.getTodaysLessons(day_r5.number)).length > 0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(day_r5.number);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedDay === day_r5.number && ctx_r1.showAvailability);\n  }\n}\nfunction CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 21)(2, \"div\", 22)(3, \"div\", 23)(4, \"div\", 24);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const status_r8 = ctx.$implicit;\n    const i_r9 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"top\", ctx_r1.getItemsPositionTop(i_r9), \"px\")(\"right\", ctx_r1.getItemsPositionRight(i_r9), \"px\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", status_r8.status + \"-gradient-lesson-bg\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", status_r8.count, \"\");\n  }\n}\nfunction CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_ng_container_1_ng_container_1_Template, 6, 6, \"ng-container\", 8);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const day_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getStatusArray(ctx_r1.getTodaysLessons(day_r5.number)));\n  }\n}\nfunction CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_span_0_Template, 4, 13, \"span\", 25)(1, CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_ng_container_1_Template, 2, 1, \"ng-container\", 5);\n  }\n  if (rf & 2) {\n    const day_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngIf\", day_r5.number);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", day_r5.number);\n  }\n}\nfunction CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\", 15);\n    i0.ɵɵlistener(\"click\", function CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_Template_td_click_1_listener() {\n      const day_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onDayClicked(day_r5.number));\n    });\n    i0.ɵɵtemplate(2, CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_container_2_Template, 6, 15, \"ng-container\", 16)(3, CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_Template, 2, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const day_r5 = ctx.$implicit;\n    const elseBlock_r10 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isTodayDate(day_r5.number))(\"ngIfElse\", elseBlock_r10);\n  }\n}\nfunction CalendarAgendaComponent_ng_container_13_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"tr\");\n    i0.ɵɵtemplate(2, CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_Template, 5, 2, \"ng-container\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const week_r11 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", week_r11);\n  }\n}\nfunction CalendarAgendaComponent_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CalendarAgendaComponent_ng_container_13_ng_container_1_Template, 3, 1, \"ng-container\", 8);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getWeeksInMonth(ctx_r1.currentDate));\n  }\n}\nexport class CalendarAgendaComponent {\n  constructor(cdr, elementRef, calendarService, authService, classroomService) {\n    this.cdr = cdr;\n    this.elementRef = elementRef;\n    this.calendarService = calendarService;\n    this.authService = authService;\n    this.classroomService = classroomService;\n    this.subs = new SubSink();\n    this.enableButtons = true;\n    this.reverseGradient = false;\n    this.title = 'Agenda';\n    this.lessons = [];\n    this.calHeaderClass = 'cal-header flex justify-content-center align-items-center';\n    this.currentSelectedDay = null;\n    this.userId = '';\n    this.showAvailability = false;\n    this.dayClicked = new EventEmitter();\n    this.agendaHeight = new EventEmitter();\n    this.currentMonthChanged = new EventEmitter();\n    this.weekdays = ['M', 'T', 'W', 'T', 'F', 'S', 'S'];\n    this.currentDate = new Date();\n    this.selectedDays = {};\n    this.monthAvailability = [];\n    this.loaded = false;\n  }\n  ngOnInit() {\n    if (this.currentSelectedDay) {\n      this.currentDate = new Date(this.currentSelectedDay);\n    } else {\n      this.currentDate = new Date();\n    }\n    this.user = this.authService.getLoggedInUser();\n    if (this.showAvailability) {\n      // this.loadAvailability();\n    } else {\n      this.loaded = true;\n    }\n  }\n  ngOnChanges(changes) {\n    if (this.lessons.length > 0) {\n      this.groupLessonsByDateAndStatus();\n    }\n    if (changes.userId && !changes.userId.firstChange) {\n      this.userId = changes.userId.currentValue;\n      if (changes.currentSelectedDay && changes.currentSelectedDay.currentValue) {\n        this.currentDate = changes.currentSelectedDay.currentValue;\n      }\n      this.loadAvailability();\n      this.cdr.detectChanges();\n    }\n    if (changes.currentSelectedDay && !changes.currentSelectedDay.firstChange) {\n      this.currentDate = changes.currentSelectedDay.currentValue;\n      this.currentDate.setMonth(changes.currentSelectedDay.currentValue.getMonth());\n      this.setSelectedDay(this.currentDate.getDate());\n      if (this.showAvailability) {\n        this.loadAvailability();\n      }\n      this.cdr.detectChanges();\n    }\n  }\n  ngAfterViewInit() {\n    this.calculateAgendaHeight();\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  getDaysInMonth(date) {\n    const year = date.getFullYear();\n    const month = date.getMonth();\n    return new Date(year, month + 1, 0).getDate();\n  }\n  getWeeksInMonth(date) {\n    const weeks = [];\n    const startOfMonthDate = new Date(date.getFullYear(), date.getMonth(), 1);\n    const firstDayOfMonth = (startOfMonthDate.getDay() + 6) % 7; // 0 for Sunday, 1 for Monday, etc.\n    const daysInMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();\n    const startOfCalendarDate = new Date(date.getFullYear(), date.getMonth(), 1 - firstDayOfMonth);\n    const numWeeksInMonth = Math.ceil((firstDayOfMonth + daysInMonth) / 7); // Round up to ensure we show all days\n    const daysInCalendar = numWeeksInMonth * 7;\n    for (let i = 0; i < daysInCalendar; i += 7) {\n      const week = [];\n      for (let j = 0; j < 7; j++) {\n        const dayNumber = i + j + 1 - firstDayOfMonth;\n        const dayDate = new Date(date.getFullYear(), date.getMonth(), dayNumber);\n        const isPrevMonth = dayNumber < 1;\n        const isNextMonth = dayNumber > daysInMonth;\n        const isCurrentMonth = !isPrevMonth && !isNextMonth;\n        const day = {\n          number: isCurrentMonth ? dayNumber : '',\n          name: this.weekdays[j],\n          isPrevMonth,\n          isNextMonth,\n          isCurrentMonth\n        };\n        week.push(day);\n      }\n      weeks.push(week);\n    }\n    return weeks;\n  }\n  isTodayDate(day) {\n    const currentDate = moment();\n    const clickedDate = moment(this.currentDate).date(day);\n    return clickedDate.isSame(currentDate, 'day') && clickedDate.isSame(this.currentDate, 'month');\n  }\n  getCurrentMonth() {\n    this.calendarService.setCalendarAgendaMonthListener(moment(this.currentDate).toDate());\n    this.currentMonthChanged.emit(moment(this.currentDate).month());\n    return moment(this.currentDate).format('MMMM');\n  }\n  goForwardMonth() {\n    this.currentDate.setMonth(this.currentDate.getMonth() + 1);\n    this.checkAvailability();\n    this.calculateAgendaHeight();\n  }\n  goBackwardMonth() {\n    this.currentDate.setMonth(this.currentDate.getMonth() - 1);\n    this.checkAvailability();\n    this.calculateAgendaHeight();\n  }\n  setSelectedDay(day) {\n    this.selectedDays = {};\n    const key = `${this.currentDate.getFullYear()}-${this.currentDate.getMonth()}`;\n    this.selectedDays[key] = day;\n  }\n  get selectedDay() {\n    const key = `${this.currentDate.getFullYear()}-${this.currentDate.getMonth()}`;\n    return this.selectedDays[key];\n  }\n  onDayClicked(day) {\n    if (this.authService.isStudent) {\n      if (this.showAvailability && this.isDayInThePast(day)) {\n        return;\n      }\n    }\n    const clickedDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), day);\n    this.dayClicked.emit(clickedDate);\n    this.classroomService.setSelectedAgendaDate(clickedDate);\n    this.setSelectedDay(day);\n  }\n  isCurrentMonth(dayNumber) {\n    return new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), dayNumber).getMonth() === this.currentDate.getMonth();\n  }\n  isNextMonth(dayNumber) {\n    return new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), dayNumber).getMonth() === this.currentDate.getMonth() + 1;\n  }\n  loadAvailability() {\n    if (!this.userId) {\n      return;\n    }\n    if (this.showAvailability) {\n      const data = {\n        ...this.calculateMonthBounds(this.currentDate),\n        userId: this.userId\n      };\n      this.subs.sink = this.getAvailabilityFromRange(data).subscribe(res => {\n        this.monthAvailability = res.calendarAvailability;\n        this.daysOff = res.daysOff;\n        this.loaded = true;\n        this.cdr.detectChanges();\n      });\n    } else {\n      this.loaded = true;\n    }\n  }\n  isDayOff(dayNumber) {\n    if (this.showAvailability) {\n      const dateString = this.currentDate.getFullYear() + '-' + (this.currentDate.getMonth() + 1) + '-' + dayNumber;\n      const currentDate = new Date(dateString);\n      for (const dayOff of this.daysOff) {\n        const from = new Date(dayOff.period.from);\n        const to = new Date(dayOff.period.to);\n        if (currentDate >= from && currentDate <= to) {\n          return true;\n        }\n      }\n    }\n    return false;\n  }\n  isDayInThePast(dayNumber) {\n    const dateString = this.currentDate.getFullYear() + '-' + (this.currentDate.getMonth() + 1) + '-' + dayNumber;\n    const today = new Date();\n    const currentDate = new Date(dateString);\n    // Set hours, minutes, seconds, and milliseconds to 0\n    today.setHours(0, 0, 0, 0);\n    currentDate.setHours(0, 0, 0, 0);\n    return currentDate < today;\n  }\n  isHighAvailability(dayNumber) {\n    if (this.showAvailability) {\n      const dateString = `${this.currentDate.getFullYear()}-${this.currentDate.getMonth() + 1}-${dayNumber}`;\n      const currentDate = new Date(dateString);\n      const availability = this.monthAvailability.find(availability => {\n        const availabilityDateParts = availability.date.split('/').map(Number);\n        const availabilityDate = new Date(availabilityDateParts[2], availabilityDateParts[1] - 1, availabilityDateParts[0]);\n        return currentDate.toDateString() === availabilityDate.toDateString();\n      });\n      if (availability) {\n        return Math.abs(availability.availableHours) >= 4;\n      }\n    }\n    return false;\n  }\n  getTodaysLessons(day) {\n    const foundDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), day);\n    const lessonGroups = this.lessons.reduce((groups, lesson) => {\n      const date = new Date(lesson.startingDate).toLocaleDateString();\n      const status = lesson.status;\n      if (!groups[date]) {\n        groups[date] = {};\n      }\n      if (!groups[date][status]) {\n        groups[date][status] = {\n          count: 0\n        };\n      }\n      groups[date][status].count++;\n      return groups;\n    }, {});\n    const todaysLessons = lessonGroups[foundDate.toLocaleDateString()] || {};\n    const todaysLessonCounts = Object.keys(todaysLessons).reduce((counts, status) => {\n      counts[status] = todaysLessons[status].count;\n      return counts;\n    }, {});\n    return todaysLessonCounts;\n  }\n  /**\n   * Converts a status object to an array of objects with status and count properties.\n   * @param {Object} status - The status object to convert.\n   * @returns {Object[]} An array of objects with status and count properties.\n   */\n  getStatusArray(status) {\n    return Object.entries(status).map(([key, value]) => {\n      return {\n        status: key.toLowerCase().replace(' ', '-'),\n        count: value\n      };\n    });\n  }\n  getItemsPositionTop(i) {\n    if (i === 0) {\n      return 2;\n    } else if (i === 1) {\n      return 15;\n    } else if (i === 2) {\n      return 29;\n    } else if (i === 3) {\n      return 40;\n    } else if (i === 4) {\n      return 38;\n    }\n    return 10;\n  }\n  getItemsPositionRight(i) {\n    if (i === 0) {\n      return 11;\n    } else if (i === 1) {\n      return 3;\n    } else if (i === 2) {\n      return 3;\n    } else if (i === 3) {\n      return 12;\n    } else if (i === 4) {\n      return 26;\n    }\n    return 10;\n  }\n  getAvailabilityFromRange(requestData) {\n    return this.calendarService.retrieveCalendarAvailability(requestData);\n  }\n  /**\n   * Checks the availability of the retrieve availability endpoint.\n   * If showAvailability is true, it will load the availability data.\n   * Otherwise, it will mark the calendar as loaded.\n   *\n   * @returns {void}\n   */\n  checkAvailability() {\n    if (this.showAvailability) {\n      this.loaded = false;\n      this.loadAvailability();\n    } else {\n      this.loaded = true;\n    }\n  }\n  calculateMonthBounds(date) {\n    const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);\n    const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);\n    firstDay.setDate(firstDay.getDate() + 1);\n    lastDay.setDate(lastDay.getDate() + 1);\n    const firstDayFormatted = firstDay.toISOString();\n    const lastDayFormatted = lastDay.toISOString();\n    return {\n      fromDate: firstDayFormatted,\n      toDate: lastDayFormatted\n    };\n  }\n  groupLessonsByDateAndStatus() {\n    this.lessons = [].concat(...this.lessons);\n    console.log(this.lessons);\n    const lessonGroups = this.lessons.reduce((groups, lesson) => {\n      const startingDate = moment(lesson.startingDate);\n      const dateString = startingDate.format('YYYY-MM-DD');\n      const status = lesson.status;\n      if (!groups[dateString]) {\n        groups[dateString] = {};\n      }\n      if (!groups[dateString][status]) {\n        groups[dateString][status] = {\n          count: 0\n        };\n      }\n      groups[dateString][status].count++;\n      return groups;\n    }, {});\n  }\n  calculateAgendaHeight() {\n    setTimeout(() => {\n      const elementHeight = this.elementRef.nativeElement.offsetHeight;\n      console.log('Element height:', elementHeight);\n      // this.agendaHeight.emit(elementHeight);\n      this.calendarService.setCalendarAgendaHeightListener(elementHeight);\n    }, 10); // Wait for 1 second before calculating the height\n  }\n  static #_ = this.ɵfac = function CalendarAgendaComponent_Factory(t) {\n    return new (t || CalendarAgendaComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.CalendarService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ClassroomService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: CalendarAgendaComponent,\n    selectors: [[\"app-calendar-agenda\"]],\n    viewQuery: function CalendarAgendaComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.daysTableWrapper = _t.first);\n      }\n    },\n    inputs: {\n      enableButtons: \"enableButtons\",\n      reverseGradient: \"reverseGradient\",\n      title: \"title\",\n      lessons: \"lessons\",\n      calHeaderClass: \"calHeaderClass\",\n      currentSelectedDay: \"currentSelectedDay\",\n      userId: \"userId\",\n      showAvailability: \"showAvailability\"\n    },\n    outputs: {\n      dayClicked: \"dayClicked\",\n      agendaHeight: \"agendaHeight\",\n      currentMonthChanged: \"currentMonthChanged\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 14,\n    vars: 14,\n    consts: [[\"daysTableWrapper\", \"\"], [\"elseBlock\", \"\"], [\"headerBlockClass\", \"bg-cover \", \"blockClass\", \"card\", 3, \"header\", \"headerBackgroundImage\", \"headerClass\"], [1, \"p-datepicker\", \"shadow-none\", \"p-0\", 3, \"ngClass\"], [1, \"relative\", \"md:mr-2\", \"font-sm\"], [4, \"ngIf\"], [1, \"table-container\"], [1, \"p-datepicker-calendar\"], [4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"pripple\", \"\", 1, \"p-ripple\", \"p-element\", \"p-datepicker-prev\", \"p-link\", \"ng-star-inserted\", 3, \"click\"], [1, \"p-datepicker-prev-icon\", \"pi\", \"pi-chevron-left\"], [1, \"p-ink\"], [\"type\", \"button\", 1, \"p-ripple\", \"p-element\", \"p-datepicker-next\", \"p-link\", 3, \"click\"], [1, \"p-datepicker-next-icon\", \"pi\", \"pi-chevron-right\"], [1, \"cal-font-size\"], [1, \"cal-font-size\", \"cursor-pointer\", 3, \"click\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"ngClass\"], [1, \"text\"], [1, \"selected-check\", \"border-circle\", \"low-bg\", \"flex\", \"align-items-center\", \"justify-content-center\", \"absolute\"], [1, \"pi\", \"pi-check\", \"font-3xs\"], [1, \"circle-me\", \"absolute\"], [1, \"circle-me\", \"counts\", \"border-round-3xl\"], [1, \"count\", \"border-circle\", 3, \"ngClass\"], [1, \"count-status\"], [3, \"ngClass\", \"class\", 4, \"ngIf\"]],\n    template: function CalendarAgendaComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"app-block-viewer\", 2)(1, \"div\", 3)(2, \"div\")(3, \"div\", 4);\n        i0.ɵɵtext(4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(5, CalendarAgendaComponent_ng_container_5_Template, 6, 0, \"ng-container\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 6, 0)(8, \"table\", 7)(9, \"thead\")(10, \"tr\");\n        i0.ɵɵtemplate(11, CalendarAgendaComponent_ng_container_11_Template, 3, 1, \"ng-container\", 8);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(12, \"tbody\");\n        i0.ɵɵtemplate(13, CalendarAgendaComponent_ng_container_13_Template, 2, 1, \"ng-container\", 5);\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵpropertyInterpolate(\"header\", ctx.title);\n        i0.ɵɵproperty(\"headerBackgroundImage\", ctx.reverseGradient ? \"\" : \"/assets/images/dashboard/gradient-sm-1.png\")(\"headerClass\", \"justify-content-start\" + (ctx.reverseGradient ? \" text-primary\" : \"\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(10, _c1, ctx.reverseGradient, !ctx.reverseGradient, ctx.showAvailability));\n        i0.ɵɵadvance();\n        i0.ɵɵclassMap(ctx.calHeaderClass);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.getCurrentMonth());\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.enableButtons);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngForOf\", ctx.weekdays);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.loaded);\n      }\n    },\n    dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.BlockViewerComponent],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.cal-header[_ngcontent-%COMP%] {\\n  align-items: center;\\n  position: absolute;\\n  top: -28px;\\n  right: 0px;\\n  color: #fff;\\n  z-index: 5;\\n}\\n.cal-header[_ngcontent-%COMP%]   .p-datepicker-prev[_ngcontent-%COMP%], .cal-header[_ngcontent-%COMP%]   .p-datepicker-next[_ngcontent-%COMP%] {\\n  width: 2rem;\\n}\\n\\n.p-datepicker[_ngcontent-%COMP%] {\\n  position: relative;\\n  border-radius: 10px;\\n}\\n\\n.table-container[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n}\\n\\n.p-datepicker-calendar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 100%;\\n  margin: 0 auto;\\n  table-layout: fixed;\\n}\\n\\n.availability-cal[_ngcontent-%COMP%]   .p-datepicker-calendar[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:not(.p-highlight):not(.p-disabled):hover {\\n  background: rgba(30, 45, 253, 0.22) !important;\\n}\\n\\n.p-datepicker-calendar[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  width: 14.2857142857%;\\n  height: 3.5rem;\\n  text-align: center;\\n  vertical-align: middle;\\n  position: relative;\\n  border: none;\\n  padding: 0;\\n}\\n.p-datepicker-calendar[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%] {\\n  color: #ffffff;\\n  background-color: #2740AC;\\n  border: 1px solid #2E3D90;\\n}\\n.p-datepicker-calendar[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:not(.p-highlight):not(.p-disabled):hover {\\n  background: rgba(30, 45, 253, 0.08) !important;\\n}\\n\\n.p-datepicker-calendar[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: block;\\n  margin: 0 auto;\\n  text-align: center;\\n  line-height: 2.5rem;\\n  border-radius: 50px;\\n}\\n.p-datepicker-calendar[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:hover {\\n  color: var(--primary-color);\\n  background-color: #2740AC;\\n}\\n\\n.p-datepicker-calendar[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  width: 34px;\\n  height: 34px;\\n  border-radius: 50%;\\n  overflow: visible;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.p-datepicker-calendar[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  display: block;\\n  width: 100%;\\n  height: auto;\\n}\\n\\n.p-datepicker-calendar[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  text-align: center;\\n}\\n\\n.current-day[_ngcontent-%COMP%] {\\n  border-radius: 50px;\\n  border-color: transparent;\\n  color: #fff;\\n  background-image: linear-gradient(135deg, hsl(234, 72%, 56%) 0%, hsl(235, 73%, 58%) 37%, hsl(235, 74%, 61%) 61%, hsl(235, 76%, 62%) 75%, hsl(235, 77%, 64%) 83%, hsl(235, 78%, 66%) 88%, hsl(235, 80%, 68%) 92%, hsl(235, 82%, 70%) 95%, hsl(235, 83%, 72%) 98%, hsl(235, 85%, 73%) 100%);\\n}\\n\\n.p-datepicker-prev[_ngcontent-%COMP%], .p-datepicker-next[_ngcontent-%COMP%] {\\n  color: #fff;\\n}\\n\\n.reversed-gradient[_ngcontent-%COMP%]   .cal-header[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n}\\n.reversed-gradient[_ngcontent-%COMP%]   .p-datepicker-prev[_ngcontent-%COMP%], .reversed-gradient[_ngcontent-%COMP%]   .p-datepicker-next[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n}\\n\\n.prev-month[_ngcontent-%COMP%] {\\n  color: #ffff00;\\n}\\n\\n.next-month[_ngcontent-%COMP%] {\\n  color: #0000ff;\\n}\\n\\n.low-bg[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(to bottom, #9baaff, #7c8aec, #5e6ad9, #3f4bc4, #152caf);\\n}\\n\\n.high-bg[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(to bottom, #64cfdd, #48c4e0, #2cb8e2, #18abe4, #219de3, #1591e2, #1e85e0, #3177dc, #2a69da, #2b5ad6, #334ad0, #3f37c9);\\n}\\n\\n.no-bg[_ngcontent-%COMP%], .is-past[_ngcontent-%COMP%] {\\n  background: #DFE8FC;\\n}\\n\\n.circle[_ngcontent-%COMP%] {\\n  height: 0.75rem;\\n  width: 0.75rem;\\n  border-radius: 50%;\\n  color: #fff;\\n  border: 0;\\n}\\n\\n.day-circle[_ngcontent-%COMP%] {\\n  border: 1px solid var(--primary-color);\\n}\\n.day-circle.today[_ngcontent-%COMP%] {\\n  color: white !important;\\n  background: linear-gradient(#002ccf 0%, #a796ff 100%);\\n}\\n.day-circle.today[_ngcontent-%COMP%]:hover {\\n  color: var(--primary-color) !important;\\n}\\n\\n.circle-me[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 50%;\\n  color: #fff;\\n  font-size: 10px;\\n  font-weight: bold;\\n  cursor: pointer;\\n}\\n\\n.circle-me[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  right: 0;\\n  width: 20px;\\n  height: 20px;\\n}\\n\\n.counts[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  right: 0;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.count[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: row;\\n  align-items: center;\\n  justify-content: center;\\n  margin-bottom: 2px;\\n  width: 14px;\\n  height: 14px;\\n}\\n\\n.count-number[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: bold;\\n  margin-right: 2px;\\n}\\n\\n.count-status[_ngcontent-%COMP%] {\\n  font-size: 10px;\\n  text-transform: uppercase;\\n}\\n\\n.selected-check[_ngcontent-%COMP%] {\\n  top: -5px;\\n  right: -2px;\\n  padding: 3px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9zYXNzL19taXhpbnMuc2NzcyIsIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL3ByaW1lL3ByaW1lL2NhbGVuZGFyLWFnZW5kYS9jYWxlbmRhci1hZ2VuZGEuY29tcG9uZW50LnNjc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBNElNO0VBQWdCLGNBQUE7RUFBZ0IsWUFBQTtFQUFjLFdBQUE7QUN4SXBEOztBRGdKVTtFQTRCSTtJQUErQixVQUZ2QjtJQUVxQyxnQkE1QzlDO0lBNEMwRSxnQkFkakI7SUFjMkMsV0FBQTtFQ3BLL0c7RURvS1k7SUFBK0IsVUFGdkI7SUFFcUMsaUJBVlk7SUFVZ0IsZUE1QzFFO0lBNENvRyxXQUFBO0VDOUovRztFRDhKWTtJQUErQixVQUZ2QjtJQUVxQyxnQkE1QzlDO0lBNEMwRSxnQkFkakI7SUFjMkMsV0FBQTtFQ3hKL0c7RUR3Slk7SUFBK0IsVUFGdkI7SUFFcUMsaUJBVlk7SUFVZ0IsZUE1QzFFO0lBNENvRyxXQUFBO0VDbEovRztBQUNGO0FEcUhVO0VBNEJJO0lBQStCLFlBRnZCO0lBRXFDLGdCQTVDOUM7SUE0QzBFLGdCQWRqQjtJQWMyQyxXQUFBO0VDMUkvRztFRDBJWTtJQUErQixZQUZ2QjtJQUVxQyxnQkE1QzlDO0lBNEMwRSxlQTVDMUU7SUE0Q29HLFdBQUE7RUNwSS9HO0VEb0lZO0lBQStCLFlBRnZCO0lBRXFDLGdCQTVDOUM7SUE0QzBFLGVBNUMxRTtJQTRDb0csV0FBQTtFQzlIL0c7RUQ4SFk7SUFBK0IsWUFGdkI7SUFFcUMsaUJBVlk7SUFVZ0IsZUE1QzFFO0lBNENvRyxXQUFBO0VDeEgvRztBQUNGO0FEbUZNO0VBQWdCLGNBQUE7RUFBZ0IsWUFBQTtFQUFjLFdBQUE7QUM5RXBEOztBRHNGVTtFQTRCSTtJQUErQixVQUZ2QjtJQUVxQyxnQkE1QzlDO0lBNEMwRSxnQkFkakI7SUFjMkMsV0FBQTtFQzFHL0c7RUQwR1k7SUFBK0IsVUFGdkI7SUFFcUMsZ0JBNUM5QztJQTRDMEUsZUE1QzFFO0lBNENvRyxXQUFBO0VDcEcvRztFRG9HWTtJQUErQixVQUZ2QjtJQUVxQyxpQkFWWTtJQVVnQixlQTVDMUU7SUE0Q29HLFdBQUE7RUM5Ri9HO0FBQ0Y7QUR5RE07RUFBZ0IsY0FBQTtFQUFnQixZQUFBO0VBQWMsV0FBQTtBQ3BEcEQ7O0FENERVO0VBNEJJO0lBQStCLFlBRnZCO0lBRXFDLGdCQTVDOUM7SUE0QzBFLGdCQWRqQjtJQWMyQyxXQUFBO0VDaEYvRztFRGdGWTtJQUErQixZQUZ2QjtJQUVxQyxpQkFWWTtJQVVnQixlQTVDMUU7SUE0Q29HLFdBQUE7RUMxRS9HO0FBQ0Y7QURzSUEscUJBQUE7QUEwQkEscUJBQUE7QUEwQkE7MEJBQUE7QUEwQ0U7RUFDRSxtQkFBQTtFQUNBLGtCQUFBO0FDOU5KO0FEZ09JO0VBQ0UsY0FBQTtBQzlOTjtBRDRMTTtFQWlDRjtJQUtNLFdBQUE7SUFDQSxnQkFBQTtFQzlOUjtBQUNGO0FEc0xNO0VBaUNGO0lBS00sV0FBQTtJQUNBLGVBQUE7RUN4TlI7QUFDRjtBRGdMTTtFQWlDRjtJQUtNLFdBQUE7SUFDQSxlQUFBO0VDbE5SO0FBQ0Y7QUQwS007RUFpQ0Y7SUFLTSxXQUFBO0lBQ0EsZ0JBQUE7RUM1TVI7QUFDRjtBRG9LTTtFQWlDRjtJQUtNLFdBQUE7SUFDQSxpQkFBQTtFQ3RNUjtBQUNGO0FEOEpNO0VBaUNGO0lBS00sV0FBQTtJQUNBLGlCQUFBO0VDaE1SO0FBQ0Y7QUR3Sk07RUFpQ0Y7SUFLTSxXQUFBO0lBQ0EsaUJBQUE7RUMxTFI7QUFDRjs7QURnTUk7RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLFdBQUE7QUM3TE47QUQySU07RUE4Q0Y7SUFVTSxnQkFBQTtFQy9MUjtBQUNGO0FEc0lNO0VBOENGO0lBVU0sZUFBQTtFQzFMUjtBQUNGO0FEaUlNO0VBOENGO0lBVU0sZUFBQTtFQ3JMUjtBQUNGO0FENEhNO0VBOENGO0lBVU0sZ0JBQUE7RUNoTFI7QUFDRjtBRHVITTtFQThDRjtJQVVNLGlCQUFBO0VDM0tSO0FBQ0Y7QURrSE07RUE4Q0Y7SUFVTSxpQkFBQTtFQ3RLUjtBQUNGO0FENkdNO0VBOENGO0lBVU0saUJBQUE7RUNqS1I7QUFDRjs7QURzSkk7RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLFdBQUE7QUNuSk47QURpR007RUE4Q0Y7SUFVTSxlQUFBO0VDckpSO0FBQ0Y7QUQ0Rk07RUE4Q0Y7SUFVTSxlQUFBO0VDaEpSO0FBQ0Y7QUR1Rk07RUE4Q0Y7SUFVTSxnQkFBQTtFQzNJUjtBQUNGO0FEa0ZNO0VBOENGO0lBVU0saUJBQUE7RUN0SVI7QUFDRjtBRDZFTTtFQThDRjtJQVVNLGlCQUFBO0VDaklSO0FBQ0Y7QUR3RU07RUE4Q0Y7SUFVTSxpQkFBQTtFQzVIUjtBQUNGOztBRGlISTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EsV0FBQTtBQzlHTjtBRDRETTtFQThDRjtJQVVNLGVBQUE7RUNoSFI7QUFDRjtBRHVETTtFQThDRjtJQVVNLGdCQUFBO0VDM0dSO0FBQ0Y7QURrRE07RUE4Q0Y7SUFVTSxpQkFBQTtFQ3RHUjtBQUNGO0FENkNNO0VBOENGO0lBVU0saUJBQUE7RUNqR1I7QUFDRjtBRHdDTTtFQThDRjtJQVVNLGlCQUFBO0VDNUZSO0FBQ0Y7O0FEaUZJO0VBQ0UsY0FBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSxXQUFBO0FDOUVOO0FENEJNO0VBOENGO0lBVU0sZ0JBQUE7RUNoRlI7QUFDRjtBRHVCTTtFQThDRjtJQVVNLGlCQUFBO0VDM0VSO0FBQ0Y7QURrQk07RUE4Q0Y7SUFVTSxpQkFBQTtFQ3RFUjtBQUNGO0FEYU07RUE4Q0Y7SUFVTSxpQkFBQTtFQ2pFUjtBQUNGOztBRHNESTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EsV0FBQTtBQ25ETjtBRENNO0VBOENGO0lBVU0saUJBQUE7RUNyRFI7QUFDRjtBREpNO0VBOENGO0lBVU0saUJBQUE7RUNoRFI7QUFDRjtBRFRNO0VBOENGO0lBVU0saUJBQUE7RUMzQ1I7QUFDRjs7QURnQ0k7RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLFdBQUE7QUM3Qk47QURyQk07RUE4Q0Y7SUFVTSxpQkFBQTtFQy9CUjtBQUNGO0FEMUJNO0VBOENGO0lBVU0saUJBQUE7RUMxQlI7QUFDRjs7QURlSTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EsV0FBQTtBQ1pOO0FEdENNO0VBOENGO0lBVU0saUJBQUE7RUNkUjtBQUNGOztBQXBWQTtFQUNFLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSxVQUFBO0VBQ0EsVUFBQTtFQUNBLFdBQUE7RUFDQSxVQUFBO0FBdVZGO0FBclZJO0VBQ0ksV0FBQTtBQXVWUjs7QUFqVkE7RUFDSSxrQkFBQTtFQUNBLG1CQUFBO0FBb1ZKOztBQWpWQTtFQUNFLGdCQUFBO0FBb1ZGOztBQWpWQTtFQUNFLFdBQUE7RUFDQSxlQUFBO0VBQ0EsY0FBQTtFQUNBLG1CQUFBO0FBb1ZGOztBQS9VSTtFQUNFLDhDQUFBO0FBa1ZOOztBQTdVSTtFQUNFLHFCQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0VBQ0Esc0JBQUE7RUFDQSxrQkFBQTtFQUNBLFlBQUE7RUFDQSxVQUFBO0FBZ1ZOO0FBOVVNO0VBQ0UsY0FBQTtFQUNBLHlCQUFBO0VBQ0EseUJBQUE7QUFnVlI7QUE3VU07RUFDRSw4Q0FBQTtBQStVUjs7QUEzVUU7RUFDRSxjQUFBO0VBR0EsY0FBQTtFQUNBLGtCQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFBQTtBQTRVSjtBQXpVSTtFQUNFLDJCQUFBO0VBQ0EseUJBQUE7QUEyVU47O0FBdFVFO0VBQ0Usa0JBQUE7RUFDQSxRQUFBO0VBQ0EsU0FBQTtFQUNBLGdDQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtFQUNBLGlCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7QUF5VUo7O0FBdFVFO0VBQ0UsY0FBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0FBeVVKOztBQXRVRTtFQUNFLGtCQUFBO0VBQ0EsUUFBQTtFQUNBLFNBQUE7RUFDQSxnQ0FBQTtFQUNBLGtCQUFBO0FBeVVKOztBQXRVRTtFQUNFLG1CQUFBO0VBQ0EseUJBQUE7RUFDQSxXQUFBO0VBQ0EseVJBQUE7QUF5VUo7O0FBMVRFO0VBQ0UsV0FBQTtBQTZUSjs7QUF6VEk7RUFDRSwyQkFBQTtBQTRUTjtBQXpURTtFQUNFLDJCQUFBO0FBMlRKOztBQXhURTtFQUNFLGNBQUE7QUEyVEo7O0FBeFRFO0VBQ0UsY0FBQTtBQTJUSjs7QUF4VEU7RUFDRSx5RkFwSnFCO0FBK2N6Qjs7QUF4VEU7RUFDRSx3SkF6SnNCO0FBb2QxQjs7QUF4VEU7RUFDRSxtQkEzSm9CO0FBc2R4Qjs7QUF4VEU7RUFDRSxlQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0VBQ0EsV0FBQTtFQUNBLFNBQUE7QUEyVEo7O0FBeFRFO0VBQ0Usc0NBQUE7QUEyVEo7QUExVEk7RUFDRSx1QkFBQTtFQUVBLHFEQUFBO0FBMlROO0FBelRNO0VBQ0Usc0NBQUE7QUEyVFI7O0FBclRFO0VBQ0Usa0JBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGtCQUFBO0VBQ0EsV0FBQTtFQUNBLGVBQUE7RUFDQSxpQkFBQTtFQUNBLGVBQUE7QUF3VEo7O0FBclRFO0VBQ0Usa0JBQUE7RUFDQSxNQUFBO0VBQ0EsUUFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0FBd1RKOztBQXJURTtFQUNFLGtCQUFBO0VBQ0EsTUFBQTtFQUNBLFFBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0FBd1RKOztBQXJURTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxrQkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0FBd1RKOztBQXJURTtFQUNFLGVBQUE7RUFDQSxpQkFBQTtFQUNBLGlCQUFBO0FBd1RKOztBQXJURTtFQUNFLGVBQUE7RUFDQSx5QkFBQTtBQXdUSjs7QUFyVEU7RUFDRSxTQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7QUF3VEoiLCJzb3VyY2VzQ29udGVudCI6WyJAaW1wb3J0ICdmbHVpZCc7XHJcblxyXG5cclxuXHJcbi8vIGUuZ1xyXG4vLyAub3V0ZXItYm94IHtcclxuLy8gICAgIEBpbmNsdWRlIGFzcGVjdC1yYXRpbyg0LCAzKTtcclxuLy8gIH1cclxuQG1peGluIGFzcGVjdC1yYXRpbygkd2lkdGgsICRoZWlnaHQpIHtcclxuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICAgICY6YmVmb3JlIHtcclxuICAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgICAgY29udGVudDogXCJcIjtcclxuICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgcGFkZGluZy10b3A6ICgkaGVpZ2h0IC8gJHdpZHRoKSAqIDEwMCU7XHJcbiAgICB9XHJcbiAgICA+IC5pbm5lci1ib3gge1xyXG4gICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICAgdG9wOiAwO1xyXG4gICAgICAgbGVmdDogMDtcclxuICAgICAgIHJpZ2h0OiAwO1xyXG4gICAgICAgYm90dG9tOiAwO1xyXG4gICAgfVxyXG4gfVxyXG5cclxuICRzbTogNTc2cHggIWRlZmF1bHQ7XHJcbiAkbWQ6IDc2OHB4ICFkZWZhdWx0O1xyXG4gJGxnOiA5OTJweCAhZGVmYXVsdDtcclxuICR4bDogMTIwMHB4ICFkZWZhdWx0O1xyXG4gJHh4bDogMTQwMHB4ICFkZWZhdWx0O1xyXG4gJG1sOiAxODAwcHggIWRlZmF1bHQ7XHJcbiAkcWhkOiAyNTYwcHggIWRlZmF1bHQ7XHJcbiAkXzJrOiAyMDQ4cHggIWRlZmF1bHQ7IFxyXG4gJGd1dHRlcjogLjVyZW0gIWRlZmF1bHQ7XHJcbiBcclxuICRmaWVsZE1hcmdpbjogMXJlbSAhZGVmYXVsdDtcclxuICRmaWVsZExhYmVsTWFyZ2luOiAuNXJlbSAhZGVmYXVsdDtcclxuICRoZWxwZXJUZXh0TWFyZ2luOiAuMjVyZW0gIWRlZmF1bHQ7XHJcbiBcclxuICRzcGFjZXI6IDFyZW0gIWRlZmF1bHQ7XHJcbiBcclxuICRicmVha3BvaW50czogKFxyXG4gICAgICdzbSc6ICRzbSxcclxuICAgICAnbWQnOiAkbWQsXHJcbiAgICAgJ2xnJzogJGxnLFxyXG4gICAgICd4bCc6ICR4bCxcclxuICAgICAneHhsJzogJHh4bCxcclxuICAgICAncWhkJzogJHFoZCxcclxuICAgICAnMmsnOiAkXzJrLFxyXG4gKSAhZGVmYXVsdDtcclxuLy8gZS5nXHJcbi8vIEBpbmNsdWRlIGJyZWFrcG9pbnQobGFyZ2UpIHtcclxuLy8gICAgIGRpdiB7XHJcbi8vICAgICAgICBmb250LXNpemU6IDJyZW07XHJcbi8vICAgICAgICBsaW5lLWhlaWdodDogMS40O1xyXG4vLyAgICAgfVxyXG4vLyAgfVxyXG4gXHJcbkBtaXhpbiBicmVha3BvaW50KCRwb2ludCkge1xyXG5cclxuICAgIEBpZiAkcG9pbnQgPT0gcWhkIHtcclxuICAgICAgQG1lZGlhIG9ubHkgc2NyZWVuIGFuZCAobWluLXdpZHRoOiAkeGwpIGFuZCAobWF4LXdpZHRoOiAkcWhkKSB7XHJcbiAgICAgICAgICBAY29udGVudDtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gICAgQGlmICRwb2ludCA9PSBfMmsge1xyXG4gICAgICBAbWVkaWEgb25seSBzY3JlZW4gYW5kIChtaW4td2lkdGg6ICRfMmspIHtcclxuICAgICAgICAgIEBjb250ZW50O1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgICBAaWYgJHBvaW50ID09IHh4bGFyZ2Uge1xyXG4gICAgICBAbWVkaWEgb25seSBzY3JlZW4gYW5kIChtaW4td2lkdGg6ICR4eGwpe1xyXG4gICAgICAgICAgQGNvbnRlbnQ7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICAgIEBpZiAkcG9pbnQgPT1sYXJnZSB7XHJcbiAgICAgICAgQG1lZGlhIG9ubHkgc2NyZWVuIGFuZCAobWluLXdpZHRoOiAkeGwpIHtcclxuICAgICAgICAgICAgQGNvbnRlbnQ7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIEBlbHNlIGlmICRwb2ludCA9PWRlc2t0b3Age1xyXG4gICAgICAgIEBtZWRpYSBvbmx5IHNjcmVlbiBhbmQgKG1pbi13aWR0aDogJGxnKSB7XHJcbiAgICAgICAgICAgIEBjb250ZW50O1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBAZWxzZSBpZiAkcG9pbnQgPT1zbWFsbC1sYXB0b3Age1xyXG4gICAgICAgIEBtZWRpYSBvbmx5IHNjcmVlbiBhbmQgKG1heC13aWR0aDogJGxnKSAgYW5kIChtYXgtaGVpZ2h0OiAkbGcpIHtcclxuICAgICAgICAgICAgQGNvbnRlbnQ7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIEBlbHNlIGlmICRwb2ludCA9PWxhcHRvcCB7XHJcbiAgICAgICAgQG1lZGlhIG9ubHkgc2NyZWVuIGFuZCAobWluLXdpZHRoOiAgJG1kKSB7XHJcbiAgICAgICAgICAgIEBjb250ZW50O1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBAZWxzZSBpZiAkcG9pbnQgPT1zbWFsbC1oZWlnaHQtbGFwdG9wIHtcclxuICAgICAgICBAbWVkaWEgb25seSBzY3JlZW4gYW5kIChtaW4td2lkdGg6ICRtZCkgIGFuZCAobWF4LWhlaWdodDogJG1kKSB7XHJcbiAgICAgICAgICAgIEBjb250ZW50O1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBAZWxzZSBpZiAkcG9pbnQgPT10YWJsZXQge1xyXG4gICAgICAgIEBtZWRpYSBvbmx5IHNjcmVlbiBhbmQgKG1pbi13aWR0aDogJHNtKSB7XHJcbiAgICAgICAgICAgIEBjb250ZW50O1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIEBlbHNlIGlmICRwb2ludCA9PW1vYmlsZSB7XHJcbiAgICAgIEBtZWRpYSBvbmx5IHNjcmVlbiBhbmQgKG1heC13aWR0aDogNzY4cHgpIHtcclxuICAgICAgICAgIEBjb250ZW50O1xyXG4gICAgICB9XHJcbiAgICB9XHJcbn1cclxuXHJcbi8vIGUuZyBAaW5jbHVkZSBmb250LXNpemUoMTRweClcclxuQGZ1bmN0aW9uIGNhbGN1bGF0ZVJlbSgkc2l6ZSkge1xyXG4gICAgJHJlbVNpemU6ICRzaXplIC8gMTZweDtcclxuICAgIEByZXR1cm4gJHJlbVNpemUgKiAxcmVtO1xyXG59XHJcblxyXG5AbWl4aW4gZm9udC1zaXplKCRzaXplKSB7XHJcbiAgICBmb250LXNpemU6IGNhbGN1bGF0ZVJlbSgkc2l6ZSk7XHJcbn1cclxuXHJcblxyXG5AbWl4aW4gZ3JpZHMoJGdyaWRzKSB7XHJcbiAgICAvLyBTRVRVUFxyXG4gICAgJHRvdGFsLWNvbHVtbnM6IDEyO1xyXG4gICAgJGJyZWFrcG9pbnRzOiAoeHhzOjMyMHB4LCB4czo0ODBweCwgc206NzY4cHgsIG1kOjk5MnB4LCBsZzoxMjAwcHgpO1xyXG4gICAgJGd1dHRlcjogMSU7XHJcbiAgICBcclxuICAgIC8vIFdpZHRoIG9mIG9uZSBjb2x1bW5cclxuICAgICR1bml0LXdpZHRoOiAoMTAwJSAtICRndXR0ZXIgKiAyICogKCR0b3RhbC1jb2x1bW5zIC0gMSkpIC8gJHRvdGFsLWNvbHVtbnM7XHJcbiAgICBcclxuICAgIEBlYWNoICRzZWwsICRzaXplcyBpbiAkZ3JpZHNcclxuICAgIHtcclxuICAgICAgLy8gQ2xlYXIgZml4XHJcbiAgICAgICN7JHNlbH06YWZ0ZXIgeyBkaXNwbGF5OiB0YWJsZTsgY29udGVudDogXCIgXCI7IGNsZWFyOmJvdGg7IH1cclxuICAgICBcclxuICAgICAgQGVhY2ggJGJyZWFrcG9pbnQsICR3aWR0aCBpbiAkYnJlYWtwb2ludHNcclxuICAgICAge1xyXG4gICAgICAgICRjb2xzOiBtYXAtZ2V0KCRzaXplcywgJGJyZWFrcG9pbnQpO1xyXG4gICAgICAgIFxyXG4gICAgICAgIEBpZiAkY29scyAhPSBudWxsXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgQG1lZGlhIG9ubHkgc2NyZWVuIGFuZCAobWluLXdpZHRoOiAkd2lkdGgpIFxyXG4gICAgICAgICAge1xyXG4gICAgICAgICAgICAkY3VycmVudC1sZWZ0OiAwO1xyXG4gICAgICAgICAgICBcclxuICAgICAgICAgICAgQGZvciAkaSBmcm9tIDEgdGhyb3VnaCBsZW5ndGgoJGNvbHMpIHtcclxuICAgICAgICAgICAgICAkY29sOiBudGgoJGNvbHMsICRpKTtcclxuICBcclxuICAgICAgICAgICAgICAkcHJvcGVydHk6IG51bGw7ICR2YWx1ZTogbnVsbDsgJG1hcmdpbi1sZWZ0OiBudWxsOyAkbWFyZ2luLXJpZ2h0OiBudWxsO1xyXG4gICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgIC8vIElmIHRoZSBuZXh0IGNvbHVtbiBwdXNoZXMgb3ZlciB0aGUgYm91bmR5IHRoZW4gcmVzZXQgZmx1c2ggdG8gdGhlIGxlZnRcclxuICAgICAgICAgICAgICBAaWYgJGN1cnJlbnQtbGVmdCArICRjb2wgPiAkdG90YWwtY29sdW1ucyB7XHJcbiAgICAgICAgICAgICAgICAkY3VycmVudC1sZWZ0OiAwO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICBAaWYgJGN1cnJlbnQtbGVmdCAlICR0b3RhbC1jb2x1bW5zID09IDAgeyAkbWFyZ2luLWxlZnQ6IDBweDsgfSBAZWxzZSB7ICRtYXJnaW4tbGVmdDogJGd1dHRlcjsgIH1cclxuICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAkY3VycmVudC1sZWZ0OiAkY3VycmVudC1sZWZ0ICsgJGNvbDtcclxuICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICBAaWYgJGN1cnJlbnQtbGVmdCAlICR0b3RhbC1jb2x1bW5zID09IDAgeyAkbWFyZ2luLXJpZ2h0OiAwcHg7IH0gQGVsc2UgeyAkbWFyZ2luLXJpZ2h0OiAkZ3V0dGVyOyB9XHJcbiAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgLy8gSWYgdGhlIHJvdyBpcyBmdWxsIHRoZW4gZ2V0IHJlYWR5IGZvciB0aGUgbmV4dCByb3dcclxuICAgICAgICAgICAgICBAaWYgJGN1cnJlbnQtbGVmdCA9PSAkdG90YWwtY29sdW1ucyB7XHJcbiAgICAgICAgICAgICAgICAkY3VycmVudC1sZWZ0OiAwO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAvLyBTdW0gdGhlIHVuaXQgd2lkdGhzIHBsdXMgdGhlIHdpZHRoIG9mIHRoZSBndXR0ZXJzXHJcbiAgICAgICAgICAgICAgJHdpZHRoOiAoJHVuaXQtd2lkdGggKiAkY29sKSArICgoJGNvbCAtIDEpICogKCRndXR0ZXIgKiAyKSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAjeyRzZWx9ID4gKjpudGgtY2hpbGQoI3skaX0pIHsgd2lkdGg6JHdpZHRoOyBtYXJnaW4tcmlnaHQ6JG1hcmdpbi1yaWdodDsgbWFyZ2luLWxlZnQ6JG1hcmdpbi1sZWZ0OyBmbG9hdDpsZWZ0OyB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcblxyXG4gICAgQG1peGluIGludmFsaWQtc3RhdGUtaWNvbiB7XHJcbiAgICAgIHBhZGRpbmctcmlnaHQ6IDIwcHggIWltcG9ydGFudDtcclxuICAgICAgYmFja2dyb3VuZC1pbWFnZTogdXJsKFwiZGF0YTppbWFnZS9zdmcreG1sLCUzY3N2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMiAxMicgd2lkdGg9JzEyJyBoZWlnaHQ9JzEyJyBmaWxsPSdub25lJyBzdHJva2U9JyUyMzNGMzdDOSclM2UlM2NjaXJjbGUgY3g9JzYnIGN5PSc2JyByPSc0LjUnLyUzZSUzY3BhdGggc3Ryb2tlLWxpbmVqb2luPSdyb3VuZCcgZD0nTTUuOCAzLjZoLjRMNiA2LjV6Jy8lM2UlM2NjaXJjbGUgY3g9JzYnIGN5PSc4LjInIHI9Jy42JyBmaWxsPSclMjMzRjM3QzknIHN0cm9rZT0nbm9uZScvJTNlJTNjL3N2ZyUzZVwiKTtcclxuICAgICAgYmFja2dyb3VuZC1yZXBlYXQ6IG5vLXJlcGVhdDtcclxuICAgICAgYmFja2dyb3VuZC1wb3NpdGlvbjogcmlnaHQgY2FsYygwLjM3NWVtICsgMC4xODc1cmVtKSBjZW50ZXI7XHJcbiAgICAgIGJhY2tncm91bmQtc2l6ZTogY2FsYygwLjc1ZW0gKyAwLjM3NXJlbSkgY2FsYygwLjc1ZW0gKyAwLjM3NXJlbSk7XHJcbiAgICB9XHJcbiAgXHJcbiAgICBAbWl4aW4gdmFsaWQtc3RhdGUtaWNvbiB7XHJcbiAgICAgIGJhY2tncm91bmQtaW1hZ2U6IHVybChcImRhdGE6aW1hZ2Uvc3ZnK3htbCwlM2NzdmcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB2aWV3Qm94PScwIDAgOCA4JyUzZSUzY3BhdGggZmlsbD0nJTIzMTk4NzU0JyBkPSdNMi4zIDYuNzNMLjYgNC41M2MtLjQtMS4wNC40Ni0xLjQgMS4xLS44bDEuMSAxLjQgMy40LTMuOGMuNi0uNjMgMS42LS4yNyAxLjIuN2wtNCA0LjZjLS40My41LS44LjQtMS4xLjF6Jy8lM2UlM2Mvc3ZnJTNlXCIpO1xyXG4gICAgICBiYWNrZ3JvdW5kLXJlcGVhdDogbm8tcmVwZWF0O1xyXG4gICAgICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiBjZW50ZXI7XHJcbiAgICAgIGJhY2tncm91bmQtc2l6ZTogY2FsYygwLjc1ZW0gKyAwLjM3NXJlbSkgY2FsYygwLjc1ZW0gKyAwLjM3NXJlbSk7XHJcbiAgICB9XHJcbiAgLy8gUmVnaXN0ZXIgdGhlIGdyaWRzXHJcbiAgQGluY2x1ZGUgZ3JpZHMoKFxyXG4gICAgKCcucmVzcG9uc2l2ZS1mb3VyLWNvbC1ncmlkJywgKG1kOigzLCAzLCAzLCAzKSwgc206KDYsIDYsIDYsIDYpKSksXHJcbiAgICAoJy5yZXNwb25zaXZlLW5lc3RlZC1ncmlkJywgKG1kOig0LCA0LCA0KSkpLFxyXG4gICAgKCcudHdvLWNvbC1ncmlkJywgKHNtOigzLCA5KSkpLFxyXG4gICkpO1xyXG4gIFxyXG5cclxuICBAbWl4aW4gYXNwZWN0LXJhdGlvKCR3aWR0aCwgJGhlaWdodCkge1xyXG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gICAgJjpiZWZvcmV7XHJcbiAgICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICAgICAgY29udGVudDogXCIgXCI7XHJcbiAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgcGFkZGluZy10b3A6ICgkaGVpZ2h0IC8gJHdpZHRoKSAqIDEwMCU7XHJcbiAgICB9XHJcblxyXG4gICAgPiAuY29udGVudCB7XHJcbiAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICAgIHRvcDogMDtcclxuICAgICAgICBsZWZ0OiAwO1xyXG4gICAgICAgIHJpZ2h0OiAwO1xyXG4gICAgICAgIGJvdHRvbTogMDtcclxuICAgIH1cclxufVxyXG5cclxuICBAbWl4aW4gcmVzcG9uc2l2ZS1yYXRpbygkeCwkeSwgJHBzZXVkbzogZmFsc2UpIHtcclxuICAgICRwYWRkaW5nOiB1bnF1b3RlKCAoICR5IC8gJHggKSAqIDEwMCArICclJyApO1xyXG4gICAgQGlmICRwc2V1ZG8ge1xyXG4gICAgICAgICY6YmVmb3JlIHtcclxuICAgICAgICAgICAgQGluY2x1ZGUgcHNldWRvKCRwb3M6IHJlbGF0aXZlKTtcclxuICAgICAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgICAgIHBhZGRpbmctdG9wOiAkcGFkZGluZztcclxuICAgICAgICB9XHJcbiAgICB9IEBlbHNlIHtcclxuICAgICAgICBwYWRkaW5nLXRvcDogJHBhZGRpbmc7XHJcbiAgICB9XHJcbn1cclxuXHJcbi8qIERlZmluZSB0aGUgbWl4aW4gKi9cclxuQG1peGluIGZsdWlkLXR5cG9ncmFwaHkoJG1pbkZvbnQsICRtYXhGb250LCAkbWluQnJlYWtwb2ludCwgJG1heEJyZWFrcG9pbnQpIHtcclxuXHJcbiAgLyogRGVmaW5lIHZhcmlhYmxlIGZvciBtZWRpYSBxdWVyeSAqL1xyXG4gICRtYXhMZXNzT25lOiAkbWF4QnJlYWtwb2ludCAtIDE7XHJcblxyXG4gIC8qIERlZmluZSB2YXJpYWJsZSBmb3IgZmFsbGJhY2sgKi9cclxuICAkYXZnOiAoJG1heEZvbnQgKyAkbWluRm9udCkgLyAyO1xyXG5cclxuICAvKiBCYXNlIGZvbnQgc2l6ZSAqL1xyXG4gIGZvbnQtc2l6ZTogI3skbWluRm9udH1weDtcclxuXHJcbiAgQG1lZGlhIChtaW4td2lkdGg6ICN7JG1pbkJyZWFrcG9pbnR9cHgpIGFuZCAobWF4LXdpZHRoOiAjeyRtYXhMZXNzT25lfXB4KSB7XHJcblxyXG4gICAgLyogQWRkcyBhIGZhbGxiYWNrIGZvciB1bnN1cHBvcnRlZCBicm93c2VycyAqL1xyXG4gICAgZm9udC1zaXplOiAjeyRhdmd9cHg7XHJcblxyXG4gICAgLyogVGhlIGZsdWlkIHR5cG9ncmFwaHkgbWFnaWMgw7DCn8KMwp8gICovXHJcbiAgICBmb250LXNpemU6IGNhbGMoI3skbWluRm9udH1weCArICgjeyRtYXhGb250fSAtICN7JG1pbkZvbnR9KSAqICgxMDB2dyAtICN7JG1pbkJyZWFrcG9pbnR9cHgpIC8gKCN7JG1heEJyZWFrcG9pbnR9IC0gI3skbWluQnJlYWtwb2ludH0pKSFpbXBvcnRhbnRcclxuICB9XHJcblxyXG4gIEBtZWRpYSAobWluLXdpZHRoOiAjeyRtYXhCcmVha3BvaW50fXB4KSB7XHJcbiAgICBmb250LXNpemU6ICN7JG1heEZvbnR9cHg7XHJcbiAgfVxyXG59XHJcblxyXG4vKiBEZWZpbmUgdGhlIG1peGluICovXHJcbkBtaXhpbiBmbHVpZC1wcm9wZXJ0eSgkcHJvcGVydHksICRtaW5Gb250LCAkbWF4Rm9udCwgJG1pbkJyZWFrcG9pbnQsICRtYXhCcmVha3BvaW50KSB7XHJcblxyXG4gIC8qIERlZmluZSB2YXJpYWJsZSBmb3IgbWVkaWEgcXVlcnkgKi9cclxuICAkbWF4TGVzc09uZTogJG1heEJyZWFrcG9pbnQgLSAxO1xyXG5cclxuICAvKiBEZWZpbmUgdmFyaWFibGUgZm9yIGZhbGxiYWNrICovXHJcbiAgJGF2ZzogKCRtYXhGb250ICsgJG1pbkZvbnQpIC8gMjtcclxuXHJcbiAgLyogQmFzZSBmb250IHNpemUgKi9cclxuICAjeyRwcm9wZXJ0eX06ICN7JG1pbkZvbnR9cHg7XHJcblxyXG4gIEBtZWRpYSAobWluLXdpZHRoOiAjeyRtaW5CcmVha3BvaW50fXB4KSBhbmQgKG1heC13aWR0aDogI3skbWF4TGVzc09uZX1weCkge1xyXG5cclxuICAgIC8qIEFkZHMgYSBmYWxsYmFjayBmb3IgdW5zdXBwb3J0ZWQgYnJvd3NlcnMgKi9cclxuICAgICN7JHByb3BlcnR5fTogI3skYXZnfXB4O1xyXG5cclxuICAgIC8qIFRoZSBmbHVpZCB0eXBvZ3JhcGh5IG1hZ2ljIMOwwp/CjMKfICAqL1xyXG4gICAgI3skcHJvcGVydHl9OiBjYWxjKCN7JG1pbkZvbnR9cHggKyAoI3skbWF4Rm9udH0gLSAjeyRtaW5Gb250fSkgKiAoMTAwdncgLSAjeyRtaW5CcmVha3BvaW50fXB4KSAvICgjeyRtYXhCcmVha3BvaW50fSAtICN7JG1pbkJyZWFrcG9pbnR9KSlcclxuICB9XHJcblxyXG4gIEBtZWRpYSAobWluLXdpZHRoOiAjeyRtYXhCcmVha3BvaW50fXB4KSB7XHJcbiAgICAjeyRwcm9wZXJ0eX06ICN7JG1heEZvbnR9cHg7XHJcbiAgfVxyXG59XHJcblxyXG4vKiBCb3JkZXIgUmFkaXVzXHJcbi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xyXG5cclxuQG1peGluIGJvcmRlci1yYWRpdXMoJHJhZGl1cykge1xyXG4gIC13ZWJraXQtYm9yZGVyLXJhZGl1czogJHJhZGl1cztcclxuICBib3JkZXItcmFkaXVzOiAkcmFkaXVzO1xyXG4gIGJhY2tncm91bmQtY2xpcDogcGFkZGluZy1ib3g7ICAvKiBzdG9wcyBiZyBjb2xvciBmcm9tIGxlYWtpbmcgb3V0c2lkZSB0aGUgYm9yZGVyOiAqL1xyXG59XHJcblxyXG4gIC8vIENPTlRBSU5FUiBNSVhJTlxyXG5cclxuICBAbWl4aW4gbWluKCRicCwgJG1heDogXCJudWxsXCIsICRkZXZpY2U6IFwic2NyZWVuXCIpIHtcclxuICAgIEBpZiAkbWF4ID09IFwibnVsbFwiIHtcclxuICAgICAgQG1lZGlhIG9ubHkgI3skZGV2aWNlfSBhbmQgKG1pbi13aWR0aDogI3skYnB9KSB7XHJcbiAgICAgICAgQGNvbnRlbnQ7XHJcbiAgICAgIH1cclxuICAgIH0gQGVsc2Uge1xyXG4gICAgICBAbWVkaWEgb25seSAjeyRkZXZpY2V9IGFuZCAobWluLXdpZHRoOiAjeyRicH0pIGFuZCAobWF4LXdpZHRoOiAjeyRtYXh9KSB7XHJcbiAgICAgICAgQGNvbnRlbnQ7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbiAgXHJcbiAgQGZ1bmN0aW9uIGJwKCRicCkge1xyXG4gICAgQHJldHVybiBtYXAtZ2V0KCRicmVha3BvaW50cywgJGJwKTtcclxuICB9XHJcblxyXG4gIEBmdW5jdGlvbiBjb250YWluZXIoJGNvbnRhaW5lci1zaXplLCAkdHJ1ZS12YWw6IGZhbHNlKSB7XHJcbiAgICBAcmV0dXJuIG1hcC1nZXQoJGNvbnRhaW5lci1zaXplcywgJGNvbnRhaW5lci1zaXplKTtcclxuICB9XHJcbiAgXHJcbiAgJGNvbnRhaW5lci1zaXplczogKFxyXG4gICAgc206IDEwMHZ3LFxyXG4gICAgbWQ6IDk1dncsXHJcbiAgICBsZzogOTB2dyxcclxuICAgIHhsOiA5OTZweCxcclxuICAgIHh4bDogMTA1MHB4LFxyXG4gICAgcWhkOiAxMjY0cHgsXHJcbiAgICBfMms6IDEyNjRweCxcclxuICApO1xyXG4vLyAgIGxnOiAkbGcgLSA1MHB4LFxyXG4vLyAgIHhsOiAkeGwgLSA2MHB4LFxyXG4gIC5jb250YWluZXIge1xyXG4gICAgcGFkZGluZy1yaWdodDogMXJlbTtcclxuICAgIHBhZGRpbmctbGVmdDogMXJlbTtcclxuICBcclxuICAgICY6bm90KC5pcy1mbHVpZCkge1xyXG4gICAgICBtYXJnaW46IDAgYXV0bztcclxuICBcclxuICAgICAgQGVhY2ggJGJwLCAkY29udGFpbmVyLXNpemUgaW4gJGNvbnRhaW5lci1zaXplcyB7XHJcbiAgICAgICAgQGluY2x1ZGUgbWluKCN7YnAoI3skYnB9KX0pIHtcclxuICAgICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgICAgbWF4LXdpZHRoOiBjb250YWluZXIoI3skYnB9KTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbiAgXHJcbiAgQGVhY2ggJGJwLCAkY29udGFpbmVyLXNpemUgaW4gJGNvbnRhaW5lci1zaXplcyB7XHJcbiAgICAuY29udGFpbmVyLSN7JGJwfSB7XHJcbiAgICAgIG1hcmdpbjogMCBhdXRvO1xyXG4gICAgICBwYWRkaW5nLXJpZ2h0OiAxcmVtO1xyXG4gICAgICBwYWRkaW5nLWxlZnQ6IDFyZW07XHJcbiAgICAgIHdpZHRoOiAxMDAlO1xyXG4gIFxyXG4gICAgICAkaTogaW5kZXgoJGNvbnRhaW5lci1zaXplcywgJGJwICRjb250YWluZXItc2l6ZSk7XHJcbiAgXHJcbiAgICAgIEBmb3IgJGogZnJvbSAkaSB0aHJvdWdoIGxlbmd0aCgkY29udGFpbmVyLXNpemVzKSB7XHJcbiAgICAgICAgQGluY2x1ZGUgbWluKCN7YnAobnRoKG50aCgkY29udGFpbmVyLXNpemVzLCAkaiksIDEpKX0pIHtcclxuICAgICAgICAgIG1heC13aWR0aDogY29udGFpbmVyKCN7bnRoKG50aCgkY29udGFpbmVyLXNpemVzLCAkaiksIDEpfSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfSIsIkBpbXBvcnQgJ21peGlucyc7XHJcbiRoaWdoLWF2YWlsYWJpbGl0eS1jb2xvcjogbGluZWFyLWdyYWRpZW50KHRvIGJvdHRvbSwgIzY0Y2ZkZCwgIzQ4YzRlMCwgIzJjYjhlMiwgIzE4YWJlNCwgIzIxOWRlMywgIzE1OTFlMiwgIzFlODVlMCwgIzMxNzdkYywgIzJhNjlkYSwgIzJiNWFkNiwgIzMzNGFkMCwgIzNmMzdjOSk7XHJcbiRsb3ctYXZhaWxhYmlsaXR5LWNvbG9yOiBsaW5lYXItZ3JhZGllbnQodG8gYm90dG9tLCAjOWJhYWZmLCAjN2M4YWVjLCAjNWU2YWQ5LCAjM2Y0YmM0LCAjMTUyY2FmKTtcclxuJG5vLWF2YWlsYWJpbGl0eS1jb2xvcjogI0RGRThGQztcclxuXHJcbi5jYWwtaGVhZGVyIHtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICB0b3A6IC0yOHB4O1xyXG4gIHJpZ2h0OiAwcHg7XHJcbiAgY29sb3I6ICNmZmY7XHJcbiAgei1pbmRleDogNTtcclxuXHJcbiAgICAucC1kYXRlcGlja2VyLXByZXYsIC5wLWRhdGVwaWNrZXItbmV4dCB7XHJcbiAgICAgICAgd2lkdGg6IDJyZW07XHJcblxyXG4gICAgfVxyXG59XHJcblxyXG5cclxuLnAtZGF0ZXBpY2tlciB7XHJcbiAgICBwb3NpdGlvbjogcmVsYXRpdmU7XHJcbiAgICBib3JkZXItcmFkaXVzOiAxMHB4O1xyXG59XHJcblxyXG4udGFibGUtY29udGFpbmVyIHtcclxuICBvdmVyZmxvdy14OiBhdXRvO1xyXG59XHJcblxyXG4ucC1kYXRlcGlja2VyLWNhbGVuZGFyIHtcclxuICB3aWR0aDogMTAwJTtcclxuICBtYXgtd2lkdGg6IDEwMCU7XHJcbiAgbWFyZ2luOiAwIGF1dG87XHJcbiAgdGFibGUtbGF5b3V0OiBmaXhlZDtcclxufVxyXG5cclxuLmF2YWlsYWJpbGl0eS1jYWwge1xyXG4gIC5wLWRhdGVwaWNrZXItY2FsZW5kYXIgdGQge1xyXG4gICAgc3Bhbjpub3QoLnAtaGlnaGxpZ2h0KTpub3QoLnAtZGlzYWJsZWQpOmhvdmVyIHtcclxuICAgICAgYmFja2dyb3VuZDogcmdiKDMwIDQ1IDI1MyAvIDIyJSkgIWltcG9ydGFudDtcclxuICAgIH1cclxuICB9XHJcbn1cclxuICBcclxuICAgIC5wLWRhdGVwaWNrZXItY2FsZW5kYXIgdGQge1xyXG4gICAgICB3aWR0aDogY2FsYygxMDAlIC8gNyk7XHJcbiAgICAgIGhlaWdodDogMy41cmVtO1xyXG4gICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgICAgIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7XHJcbiAgICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICAgICAgYm9yZGVyOiBub25lO1xyXG4gICAgICBwYWRkaW5nOiAwO1xyXG4gIFxyXG4gICAgICAuYWN0aXZlIHtcclxuICAgICAgICBjb2xvcjogI2ZmZmZmZjtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMjc0MEFDO1xyXG4gICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICMyRTNEOTA7XHJcbiAgICAgIH1cclxuICBcclxuICAgICAgc3Bhbjpub3QoLnAtaGlnaGxpZ2h0KTpub3QoLnAtZGlzYWJsZWQpOmhvdmVyIHtcclxuICAgICAgICBiYWNrZ3JvdW5kOiByZ2IoMzAgNDUgMjUzIC8gOCUpICFpbXBvcnRhbnQ7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICBcclxuICAucC1kYXRlcGlja2VyLWNhbGVuZGFyIHRkIHNwYW4ge1xyXG4gICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICAvLyB3aWR0aDogMTAwJTtcclxuICAgIC8vIGhlaWdodDogMTAwJTtcclxuICAgIG1hcmdpbjogMCBhdXRvO1xyXG4gICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gICAgbGluZS1oZWlnaHQ6IDIuNXJlbTtcclxuICAgIGJvcmRlci1yYWRpdXM6IDUwcHg7XHJcbiAgICAvLyBwYWRkaW5nOiAje2ZsdWlkKDRweCwgOHB4LCAzMjBweCwgMTYwMHB4KX07XHJcbiAgICAvLyB3aWR0aDogI3tmbHVpZCgyNnB4LCAzMnB4LCAzMjBweCwgMTYwMHB4KX07XHJcbiAgICAmOmhvdmVyIHtcclxuICAgICAgY29sb3I6IHZhcigtLXByaW1hcnktY29sb3IpO1xyXG4gICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMjc0MEFDO1xyXG4gICAgICAgIC8vIGJvcmRlcjogMXB4IHNvbGlkICMyRTNEOTA7XHJcbiAgICAgIH1cclxuICB9XHJcbiAgXHJcbiAgLnAtZGF0ZXBpY2tlci1jYWxlbmRhciB0ZCAuY2lyY2xlIHtcclxuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgIHRvcDogNTAlO1xyXG4gICAgbGVmdDogNTAlO1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGUoLTUwJSwgLTUwJSk7XHJcbiAgICB3aWR0aDogMzRweDtcclxuICAgIGhlaWdodDogMzRweDtcclxuICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICAgIG92ZXJmbG93OiB2aXNpYmxlO1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICB9XHJcbiAgXHJcbiAgLnAtZGF0ZXBpY2tlci1jYWxlbmRhciB0ZCAuY2lyY2xlIGltZyB7XHJcbiAgICBkaXNwbGF5OiBibG9jaztcclxuICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgaGVpZ2h0OiBhdXRvO1xyXG4gIH1cclxuICBcclxuICAucC1kYXRlcGlja2VyLWNhbGVuZGFyIHRkIC5jaXJjbGUgLnRleHQge1xyXG4gICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgdG9wOiA1MCU7XHJcbiAgICBsZWZ0OiA1MCU7XHJcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZSgtNTAlLCAtNTAlKTtcclxuICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICB9XHJcbiAgXHJcbiAgLmN1cnJlbnQtZGF5IHtcclxuICAgIGJvcmRlci1yYWRpdXM6IDUwcHg7XHJcbiAgICBib3JkZXItY29sb3I6IHRyYW5zcGFyZW50O1xyXG4gICAgY29sb3I6ICNmZmY7XHJcbiAgICBiYWNrZ3JvdW5kLWltYWdlOiBsaW5lYXItZ3JhZGllbnQoXHJcbiAgICAgIDEzNWRlZyxcclxuICAgICAgaHNsKDIzNGRlZyA3MiUgNTYlKSAwJSxcclxuICAgICAgaHNsKDIzNWRlZyA3MyUgNTglKSAzNyUsXHJcbiAgICAgIGhzbCgyMzVkZWcgNzQlIDYxJSkgNjElLFxyXG4gICAgICBoc2woMjM1ZGVnIDc2JSA2MiUpIDc1JSxcclxuICAgICAgaHNsKDIzNWRlZyA3NyUgNjQlKSA4MyUsXHJcbiAgICAgIGhzbCgyMzVkZWcgNzglIDY2JSkgODglLFxyXG4gICAgICBoc2woMjM1ZGVnIDgwJSA2OCUpIDkyJSxcclxuICAgICAgaHNsKDIzNWRlZyA4MiUgNzAlKSA5NSUsXHJcbiAgICAgIGhzbCgyMzVkZWcgODMlIDcyJSkgOTglLFxyXG4gICAgICBoc2woMjM1ZGVnIDg1JSA3MyUpIDEwMCVcclxuICAgICk7XHJcbiAgfVxyXG5cclxuICAucC1kYXRlcGlja2VyLXByZXYsIC5wLWRhdGVwaWNrZXItbmV4dCB7XHJcbiAgICBjb2xvcjogI2ZmZjtcclxuICB9XHJcblxyXG4gIC5yZXZlcnNlZC1ncmFkaWVudCB7XHJcbiAgICAuY2FsLWhlYWRlciB7XHJcbiAgICAgIGNvbG9yOiB2YXIoLS1wcmltYXJ5LWNvbG9yKVxyXG4gICAgfVxyXG4gICAgXHJcbiAgLnAtZGF0ZXBpY2tlci1wcmV2LCAucC1kYXRlcGlja2VyLW5leHQge1xyXG4gICAgY29sb3I6IHZhcigtLXByaW1hcnktY29sb3IpXHJcbiAgfVxyXG4gIH1cclxuICAucHJldi1tb250aCB7XHJcbiAgICBjb2xvcjogI2ZmZmYwMDtcclxuICB9XHJcbiAgXHJcbiAgLm5leHQtbW9udGgge1xyXG4gICAgY29sb3I6ICMwMDAwZmY7XHJcbiAgfVxyXG5cclxuICAubG93LWJnIHtcclxuICAgIGJhY2tncm91bmQtaW1hZ2U6ICRsb3ctYXZhaWxhYmlsaXR5LWNvbG9yO1xyXG4gIH1cclxuXHJcbiAgLmhpZ2gtYmcge1xyXG4gICAgYmFja2dyb3VuZC1pbWFnZTogJGhpZ2gtYXZhaWxhYmlsaXR5LWNvbG9yO1xyXG4gIH1cclxuICBcclxuICAubm8tYmcsIC5pcy1wYXN0IHtcclxuICAgIGJhY2tncm91bmQ6ICRuby1hdmFpbGFiaWxpdHktY29sb3I7XHJcbiAgfVxyXG5cclxuICAuY2lyY2xlIHtcclxuICAgIGhlaWdodDogMC43NXJlbTtcclxuICAgIHdpZHRoOiAwLjc1cmVtO1xyXG4gICAgYm9yZGVyLXJhZGl1czogNTAlO1xyXG4gICAgY29sb3I6ICNmZmY7XHJcbiAgICBib3JkZXI6IDA7XHJcbiAgfVxyXG5cclxuICAuZGF5LWNpcmNsZSB7XHJcbiAgICBib3JkZXI6MXB4IHNvbGlkIHZhcigtLXByaW1hcnktY29sb3IpO1xyXG4gICAgJi50b2RheSB7XHJcbiAgICAgIGNvbG9yOiB3aGl0ZSFpbXBvcnRhbnQ7XHJcbiAgICAgIC8vIGJvcmRlcjogdHJhbnNwYXJlbnQ7XHJcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgjMDAyY2NmIDAlLCAjYTc5NmZmIDEwMCUpO1xyXG4gICAgICAgIFxyXG4gICAgICAmOmhvdmVyIHtcclxuICAgICAgICBjb2xvcjogdmFyKC0tcHJpbWFyeS1jb2xvcikhaW1wb3J0YW50O1xyXG4gICAgICAgICAgLy8gYm9yZGVyOiAxcHggc29saWQgIzJFM0Q5MDtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICAuY2lyY2xlLW1lIHtcclxuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICBib3JkZXItcmFkaXVzOiA1MCU7XHJcbiAgICBjb2xvcjogI2ZmZjtcclxuICAgIGZvbnQtc2l6ZTogMTBweDtcclxuICAgIGZvbnQtd2VpZ2h0OiBib2xkO1xyXG4gICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gIH1cclxuICBcclxuICAuY2lyY2xlLW1lIGltZyB7XHJcbiAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICB0b3A6IDA7XHJcbiAgICByaWdodDogMDtcclxuICAgIHdpZHRoOiAyMHB4O1xyXG4gICAgaGVpZ2h0OiAyMHB4O1xyXG4gIH1cclxuICBcclxuICAuY291bnRzIHtcclxuICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgIHRvcDogMDtcclxuICAgIHJpZ2h0OiAwO1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgfVxyXG4gIFxyXG4gIC5jb3VudCB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgZmxleC1kaXJlY3Rpb246IHJvdztcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICAgIG1hcmdpbi1ib3R0b206IDJweDtcclxuICAgIHdpZHRoOiAxNHB4O1xyXG4gICAgaGVpZ2h0OiAxNHB4O1xyXG4gIH1cclxuICBcclxuICAuY291bnQtbnVtYmVyIHtcclxuICAgIGZvbnQtc2l6ZTogMTJweDtcclxuICAgIGZvbnQtd2VpZ2h0OiBib2xkO1xyXG4gICAgbWFyZ2luLXJpZ2h0OiAycHg7XHJcbiAgfVxyXG4gIFxyXG4gIC5jb3VudC1zdGF0dXMge1xyXG4gICAgZm9udC1zaXplOiAxMHB4O1xyXG4gICAgdGV4dC10cmFuc2Zvcm06IHVwcGVyY2FzZTtcclxuICB9XHJcblxyXG4gIC5zZWxlY3RlZC1jaGVjayB7XHJcbiAgICB0b3A6IC01cHg7XHJcbiAgICByaWdodDogLTJweDtcclxuICAgIHBhZGRpbmc6IDNweDtcclxuICB9Il0sInNvdXJjZVJvb3QiOiIifQ== */\"],\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "SubSink", "moment", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "CalendarAgendaComponent_ng_container_5_Template_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "goBackwardMonth", "ɵɵelement", "ɵɵelementEnd", "CalendarAgendaComponent_ng_container_5_Template_button_click_4_listener", "goForward<PERSON><PERSON>h", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate", "day_r3", "ɵɵstyleProp", "getItemsPositionTop", "i_r7", "getItemsPositionRight", "ɵɵproperty", "status_r6", "status", "ɵɵtextInterpolate1", "count", "ɵɵtemplate", "CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_container_2_ng_container_4_Template", "CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_container_2_ng_container_5_Template", "ɵɵclassMap", "day_r5", "availability", "ɵɵpureFunction8", "_c2", "selected<PERSON>ay", "number", "isHighAvailability", "isDayInThePast", "showAvailability", "is<PERSON>ay<PERSON>ff", "getStatusArray", "getTodaysLessons", "length", "CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_span_0_ng_container_3_Template", "ɵɵpureFunction7", "_c3", "i_r9", "status_r8", "CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_ng_container_1_ng_container_1_Template", "CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_span_0_Template", "CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_ng_container_1_Template", "CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_Template_td_click_1_listener", "_r4", "$implicit", "onDayClicked", "CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_container_2_Template", "CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_Template", "ɵɵtemplateRefExtractor", "isTodayDate", "elseBlock_r10", "CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_Template", "week_r11", "CalendarAgendaComponent_ng_container_13_ng_container_1_Template", "getWeeksInMonth", "currentDate", "CalendarAgendaComponent", "constructor", "cdr", "elementRef", "calendarService", "authService", "classroomService", "subs", "enableButtons", "reverseGradient", "title", "lessons", "calHeaderClass", "currentSelectedDay", "userId", "dayClicked", "agendaHeight", "currentMonthChanged", "weekdays", "Date", "selectedDays", "monthAvailability", "loaded", "ngOnInit", "user", "getLoggedInUser", "ngOnChanges", "changes", "groupLessonsByDateAndStatus", "firstChange", "currentValue", "loadAvailability", "detectChanges", "setMonth", "getMonth", "setSelectedDay", "getDate", "ngAfterViewInit", "calculateAgendaHeight", "ngOnDestroy", "unsubscribe", "getDaysInMonth", "date", "year", "getFullYear", "month", "weeks", "startOfMonthDate", "firstDayOfMonth", "getDay", "daysInMonth", "startOfCalendarDate", "numWeeksInMonth", "Math", "ceil", "daysInCalendar", "i", "week", "j", "dayNumber", "dayDate", "isPrevMonth", "isNextMonth", "isCurrentMonth", "day", "name", "push", "clickedDate", "isSame", "getCurrentMonth", "setCalendarAgendaMonthListener", "toDate", "emit", "format", "checkAvailability", "key", "isStudent", "setSelectedAgendaDate", "data", "calculateMonthBounds", "sink", "getAvailabilityFromRange", "subscribe", "res", "calendarAvailability", "daysOff", "dateString", "dayOff", "from", "period", "to", "today", "setHours", "find", "availabilityDateParts", "split", "map", "Number", "availabilityDate", "toDateString", "abs", "availableHours", "foundDate", "lessonGroups", "reduce", "groups", "lesson", "startingDate", "toLocaleDateString", "todaysLessons", "todaysLessonCounts", "Object", "keys", "counts", "entries", "value", "toLowerCase", "replace", "requestData", "retrieveCalendarAvailability", "firstDay", "lastDay", "setDate", "firstDayFormatted", "toISOString", "lastDayFormatted", "fromDate", "concat", "console", "log", "setTimeout", "elementHeight", "nativeElement", "offsetHeight", "setCalendarAgendaHeightListener", "_", "ɵɵdirectiveInject", "ChangeDetectorRef", "ElementRef", "i1", "CalendarService", "i2", "AuthService", "i3", "ClassroomService", "_2", "selectors", "viewQuery", "CalendarAgendaComponent_Query", "rf", "ctx", "CalendarAgendaComponent_ng_container_5_Template", "CalendarAgendaComponent_ng_container_11_Template", "CalendarAgendaComponent_ng_container_13_Template", "ɵɵpropertyInterpolate", "ɵɵpureFunction3", "_c1"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\prime\\prime\\calendar-agenda\\calendar-agenda.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\prime\\prime\\calendar-agenda\\calendar-agenda.component.html"], "sourcesContent": ["import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { CalendarService } from 'src/app/core/services/calendar.service';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { SubSink } from 'subsink';\r\nimport * as moment from 'moment';\r\n\r\ninterface Day {\r\n  number: number;\r\n  name: string;\r\n  availability: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-calendar-agenda',\r\n  templateUrl: './calendar-agenda.component.html',\r\n  styleUrls: ['./calendar-agenda.component.scss'],\r\n  changeDetection: ChangeDetectionStrategy.OnPush\r\n})\r\nexport class CalendarAgendaComponent implements OnInit, OnChanges, AfterViewInit, OnD<PERSON>roy {\r\n  @ViewChild('daysTableWrapper', {static: true}) public daysTableWrapper: any;\r\n  private subs = new SubSink();\r\n  @Input() enableButtons = true;\r\n  @Input() reverseGradient = false;\r\n  @Input() title = 'Agenda';\r\n  @Input() lessons: any = [];\r\n  @Input() calHeaderClass = 'cal-header flex justify-content-center align-items-center';\r\n  @Input() currentSelectedDay = null;\r\n  @Input() userId = '';\r\n  @Input() showAvailability: boolean = false;\r\n  @Output() dayClicked = new EventEmitter<Date>();\r\n  @Output() agendaHeight = new EventEmitter<number>();\r\n  @Output() currentMonthChanged = new EventEmitter<number>();\r\n  weekdays: string[] = ['M', 'T', 'W', 'T', 'F', 'S', 'S'];\r\n  currentDate: Date = new Date();\r\n  selectedDays: { [key: string]: number } = {};\r\n  user: any;\r\n  availability: any;\r\n  monthAvailability: any[] = [];\r\n  daysOff: any;\r\n  loaded = false;\r\n  \r\n  constructor(\r\n    private cdr: ChangeDetectorRef,\r\n    private elementRef: ElementRef,\r\n    private calendarService: CalendarService,   \r\n    private authService: AuthService, \r\n    private classroomService: ClassroomService, \r\n    ) { }\r\n\r\n  ngOnInit(): void {\r\n\r\n    if (this.currentSelectedDay) {\r\n      this.currentDate = new Date(this.currentSelectedDay);\r\n    } else {\r\n      this.currentDate = new Date();\r\n    }\r\n    this.user = this.authService.getLoggedInUser();\r\n    if (this.showAvailability) {\r\n      // this.loadAvailability();\r\n    } else {\r\n      this.loaded = true;\r\n    }\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges) {\r\n    if (this.lessons.length > 0) {\r\n      this.groupLessonsByDateAndStatus();\r\n\r\n    }\r\n    if (changes.userId && !changes.userId.firstChange) {\r\n      this.userId = changes.userId.currentValue;\r\n      if (changes.currentSelectedDay && changes.currentSelectedDay.currentValue) {\r\n        this.currentDate = (changes.currentSelectedDay.currentValue);\r\n      }\r\n      this.loadAvailability();\r\n      this.cdr.detectChanges();\r\n    }\r\n    if (changes.currentSelectedDay && !changes.currentSelectedDay.firstChange) {\r\n      this.currentDate = (changes.currentSelectedDay.currentValue);\r\n      this.currentDate.setMonth(changes.currentSelectedDay.currentValue.getMonth());\r\n      this.setSelectedDay(this.currentDate.getDate());\r\n      if (this.showAvailability) {\r\n        this.loadAvailability();\r\n      }\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.calculateAgendaHeight();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  getDaysInMonth(date: Date): number {\r\n    const year = date.getFullYear();\r\n    const month = date.getMonth();\r\n    return new Date(year, month + 1, 0).getDate();\r\n  }\r\n  \r\n  getWeeksInMonth(date: Date): Day[][] {\r\n    const weeks: Day[][] = [];\r\n    const startOfMonthDate = new Date(date.getFullYear(), date.getMonth(), 1);\r\n    const firstDayOfMonth = (startOfMonthDate.getDay() + 6) % 7; // 0 for Sunday, 1 for Monday, etc.\r\n    const daysInMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();\r\n  \r\n    const startOfCalendarDate = new Date(date.getFullYear(), date.getMonth(), 1 - firstDayOfMonth);\r\n    const numWeeksInMonth = Math.ceil((firstDayOfMonth + daysInMonth) / 7); // Round up to ensure we show all days\r\n    const daysInCalendar = numWeeksInMonth * 7;\r\n  \r\n    for (let i = 0; i < daysInCalendar; i += 7) {\r\n      const week: any[] = [];\r\n      for (let j = 0; j < 7; j++) {\r\n        const dayNumber = (i + j + 1) - firstDayOfMonth;\r\n        const dayDate = new Date(date.getFullYear(), date.getMonth(), dayNumber);\r\n        const isPrevMonth = dayNumber < 1;\r\n        const isNextMonth = dayNumber > daysInMonth;\r\n        const isCurrentMonth = !isPrevMonth && !isNextMonth;\r\n\r\n        const day = {\r\n          number: isCurrentMonth ? dayNumber : '',\r\n          name: this.weekdays[j],\r\n          isPrevMonth,\r\n          isNextMonth,\r\n          isCurrentMonth,\r\n        };\r\n        week.push(day);\r\n      }\r\n      weeks.push(week);\r\n    }\r\n    return weeks;\r\n  } \r\n  isTodayDate(day: number): boolean {\r\n    const currentDate = moment();\r\n    const clickedDate = moment(this.currentDate).date(day);\r\n  \r\n    return (\r\n      clickedDate.isSame(currentDate, 'day') &&\r\n      clickedDate.isSame(this.currentDate, 'month')\r\n    );\r\n  }\r\n\r\n  getCurrentMonth(): string {\r\n    this.calendarService.setCalendarAgendaMonthListener(moment(this.currentDate).toDate());\r\n    this.currentMonthChanged.emit(moment(this.currentDate).month());\r\n    return moment(this.currentDate).format('MMMM');\r\n  }\r\n\r\n  goForwardMonth(): void {\r\n    this.currentDate.setMonth(this.currentDate.getMonth() + 1);\r\n    this.checkAvailability();\r\n    this.calculateAgendaHeight();\r\n  }\r\n\r\n  goBackwardMonth(): void {\r\n    this.currentDate.setMonth(this.currentDate.getMonth() - 1);\r\n    this.checkAvailability();\r\n    this.calculateAgendaHeight();\r\n  }\r\n\r\n  setSelectedDay(day: number): void {\r\n    this.selectedDays = {};\r\n    const key = `${this.currentDate.getFullYear()}-${this.currentDate.getMonth()}`;\r\n    this.selectedDays[key] = day;\r\n  }\r\n\r\n  get selectedDay(): number {\r\n    const key = `${this.currentDate.getFullYear()}-${this.currentDate.getMonth()}`;\r\n    return this.selectedDays[key];\r\n  }\r\n\r\n  onDayClicked(day: number): void {\r\n    if (this.authService.isStudent) {\r\n      if (this.showAvailability && this.isDayInThePast(day)) {\r\n        return;\r\n      }\r\n    }\r\n    const clickedDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), day);\r\n    this.dayClicked.emit(clickedDate);\r\n    this.classroomService.setSelectedAgendaDate(clickedDate);\r\n    this.setSelectedDay(day);\r\n  }\r\n\r\n  isCurrentMonth(dayNumber: number): boolean {\r\n    return new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), dayNumber).getMonth() === this.currentDate.getMonth();\r\n  }\r\n\r\n  isNextMonth(dayNumber: number): boolean {\r\n    return new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), dayNumber).getMonth() === this.currentDate.getMonth() + 1;\r\n  }\r\n\r\n  loadAvailability(): void {\r\n    if (!this.userId) {\r\n      return;\r\n    }\r\n    if(this.showAvailability){\r\n      const data = {\r\n        ...this.calculateMonthBounds(this.currentDate),\r\n        userId: this.userId\r\n      };\r\n      this.subs.sink = this.getAvailabilityFromRange(data).subscribe((res) => {\r\n        this.monthAvailability = res.calendarAvailability;\r\n        this.daysOff = res.daysOff;\r\n        this.loaded = true;\r\n        this.cdr.detectChanges();\r\n      });\r\n    }\r\n    else {\r\n      this.loaded = true;\r\n    }\r\n  }\r\n\r\n  isDayOff(dayNumber: number): boolean {\r\n    if(this.showAvailability){\r\n      const dateString = this.currentDate.getFullYear() + '-' + (this.currentDate.getMonth() + 1) + '-' + dayNumber;\r\n      const currentDate = new Date(dateString);\r\n    \r\n      for (const dayOff of this.daysOff) {\r\n        const from = new Date(dayOff.period.from);\r\n        const to = new Date(dayOff.period.to);\r\n    \r\n        if (currentDate >= from && currentDate <= to) {\r\n          return true;\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  }\r\n\r\n  isDayInThePast(dayNumber: number): boolean {\r\n    const dateString = this.currentDate.getFullYear() + '-' + (this.currentDate.getMonth() + 1) + '-' + dayNumber;\r\n    const today = new Date();\r\n    const currentDate = new Date(dateString);\r\n\r\n    // Set hours, minutes, seconds, and milliseconds to 0\r\n    today.setHours(0, 0, 0, 0);\r\n    currentDate.setHours(0, 0, 0, 0);\r\n\r\n    return currentDate < today;\r\n  }\r\n\r\n  isHighAvailability(dayNumber: number): boolean {\r\n    if (this.showAvailability) {\r\n      const dateString = `${this.currentDate.getFullYear()}-${this.currentDate.getMonth() + 1}-${dayNumber}`;\r\n      const currentDate = new Date(dateString);\r\n  \r\n      const availability = this.monthAvailability.find((availability) => {\r\n        const availabilityDateParts = availability.date.split('/').map(Number);\r\n        const availabilityDate = new Date(availabilityDateParts[2], availabilityDateParts[1] - 1, availabilityDateParts[0]);\r\n  \r\n        return currentDate.toDateString() === availabilityDate.toDateString();\r\n      });\r\n  \r\n      if (availability) {\r\n        return Math.abs(availability.availableHours) >= 4;\r\n      }\r\n    }\r\n    \r\n    return false;\r\n  }\r\n\r\n   \r\n  getTodaysLessons(day: number): any[] {\r\n    const foundDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), day);\r\n    const lessonGroups = this.lessons.reduce((groups: any, lesson: any) => {\r\n      const date = new Date(lesson.startingDate).toLocaleDateString();\r\n      const status = lesson.status;\r\n      if (!groups[date]) {\r\n        groups[date] = {};\r\n      }\r\n      if (!groups[date][status]) {\r\n        groups[date][status] = { count: 0 };\r\n      }\r\n      groups[date][status].count++;\r\n      return groups;\r\n    }, {});\r\n    const todaysLessons = lessonGroups[foundDate.toLocaleDateString()] || {};\r\n    const todaysLessonCounts = Object.keys(todaysLessons).reduce((counts: any, status: string) => {\r\n      counts[status] = todaysLessons[status].count;\r\n      return counts;\r\n    }, {});\r\n\r\n    return todaysLessonCounts;\r\n  }\r\n\r\n  /**\r\n   * Converts a status object to an array of objects with status and count properties.\r\n   * @param {Object} status - The status object to convert.\r\n   * @returns {Object[]} An array of objects with status and count properties.\r\n   */\r\n  getStatusArray(status: {}): Object[] {\r\n    return Object.entries(status).map(([key, value]) => {\r\n      return { status: key.toLowerCase().replace(' ', '-'), count: value };\r\n    });\r\n  }\r\n\r\n  getItemsPositionTop(i: number) {\r\n    if (i === 0) {\r\n      return 2;\r\n    } else if (i === 1) {\r\n      return 15;\r\n    } else if (i === 2) {\r\n      return 29;\r\n    } else if (i === 3) {\r\n      return 40;\r\n    } else if (i === 4) {\r\n      return 38;\r\n    }\r\n    return 10;\r\n  }\r\n\r\n  getItemsPositionRight(i: number) {\r\n    if (i === 0) {\r\n      return 11;\r\n    } else if (i === 1) {\r\n      return 3;\r\n    } else if (i === 2) {\r\n      return 3;\r\n    } else if (i === 3) {\r\n      return 12;\r\n    } else if (i === 4) {\r\n      return 26;\r\n    }\r\n    return 10;\r\n  }\r\n  \r\n  private getAvailabilityFromRange(requestData: any): Observable<any> {\r\n    return this.calendarService.retrieveCalendarAvailability(requestData);\r\n  }\r\n\r\n  /**\r\n   * Checks the availability of the retrieve availability endpoint.\r\n   * If showAvailability is true, it will load the availability data.\r\n   * Otherwise, it will mark the calendar as loaded.\r\n   *\r\n   * @returns {void}\r\n   */\r\n  private checkAvailability(): void {\r\n    if (this.showAvailability) {\r\n      this.loaded = false;\r\n      this.loadAvailability();\r\n    } else {\r\n      this.loaded = true;\r\n    }\r\n  }\r\n  \r\n  private calculateMonthBounds(date: Date): { fromDate: string, toDate: string } {\r\n    const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);\r\n    const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);\r\n    firstDay.setDate(firstDay.getDate() + 1);\r\n\r\n    lastDay.setDate(lastDay.getDate() + 1);\r\n    const firstDayFormatted = firstDay.toISOString();\r\n    const lastDayFormatted = lastDay.toISOString();\r\n  \r\n    return { fromDate: firstDayFormatted, toDate: lastDayFormatted };\r\n  }\r\n\r\n  private groupLessonsByDateAndStatus() {\r\n    this.lessons = [].concat(...this.lessons) as [];\r\n    console.log(this.lessons);\r\n    const lessonGroups = this.lessons.reduce((groups: any, lesson: any) => {\r\n      const startingDate = moment(lesson.startingDate);\r\n      const dateString = startingDate.format('YYYY-MM-DD');\r\n      const status = lesson.status;\r\n      if (!groups[dateString]) {\r\n        groups[dateString] = {};\r\n      }\r\n      if (!groups[dateString][status]) {\r\n        groups[dateString][status] = { count: 0 };\r\n      }\r\n      groups[dateString][status].count++;\r\n      return groups;\r\n    }, {});\r\n  }\r\n\r\n  private calculateAgendaHeight() {\r\n    setTimeout(() => {\r\n    const elementHeight = this.elementRef.nativeElement.offsetHeight;\r\n    console.log('Element height:', elementHeight);\r\n    // this.agendaHeight.emit(elementHeight);\r\n    this.calendarService.setCalendarAgendaHeightListener(elementHeight);\r\n    }, 10); // Wait for 1 second before calculating the height\r\n\r\n  }\r\n}", "\r\n<app-block-viewer header=\"{{title}}\" headerBlockClass=\"bg-cover \" [headerBackgroundImage]=\"reverseGradient ? '' : '/assets/images/dashboard/gradient-sm-1.png'\" blockClass=\"card\"\r\n[headerClass]=\"'justify-content-start' + (reverseGradient ? ' text-primary' : '') \" >\r\n  <div class=\"p-datepicker  shadow-none p-0\" [ngClass]=\"{ 'reversed-gradient': reverseGradient, 'block-gradient' : !reverseGradient, 'availability-cal': showAvailability }\">\r\n    <div [class]=\"calHeaderClass\">\r\n      <div class=\"relative md:mr-2 font-sm\">{{ getCurrentMonth() }}</div>\r\n      <ng-container *ngIf=\"enableButtons\">\r\n        <button type=\"button\" (click)=\"goBackwardMonth()\" pripple=\"\" class=\"p-ripple p-element p-datepicker-prev p-link ng-star-inserted\">\r\n          <span class=\"p-datepicker-prev-icon pi pi-chevron-left\"></span>\r\n          <span class=\"p-ink\"></span>\r\n        </button>\r\n        <button (click)=\"goForwardMonth()\" type=\"button\" class=\"p-ripple p-element p-datepicker-next p-link\">\r\n          <span class=\"p-datepicker-next-icon pi pi-chevron-right\"></span>\r\n        </button>\r\n      </ng-container>\r\n    </div>\r\n    <div class=\"table-container\" #daysTableWrapper>\r\n      <table class=\"p-datepicker-calendar\">\r\n        <thead>\r\n          <tr>\r\n            <ng-container *ngFor=\"let day of weekdays; let i = index;\">\r\n              <th class=\"cal-font-size\">{{ day }}</th>\r\n            </ng-container>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <ng-container *ngIf=\"loaded\">\r\n            <ng-container *ngFor=\"let week of getWeeksInMonth(currentDate)\">\r\n              <tr>\r\n                <ng-container *ngFor=\"let day of week\">\r\n                  <td class=\"cal-font-size cursor-pointer\" (click)=\"onDayClicked(day.number)\">\r\n                    <ng-container *ngIf=\"isTodayDate(day.number); else elseBlock\">\r\n                      <span \r\n                      [ngClass]=\"{ 'active': selectedDay === day.number, \r\n                      'high-bg': isHighAvailability(day.number) && !isDayInThePast(day.number),\r\n                      'low-bg': !isHighAvailability(day.number) && showAvailability && !isDayInThePast(day.number), \r\n                      'is-past': showAvailability && isDayInThePast(day.number), \r\n                      'no-bg': isDayOff(day.number), \r\n                      'circle': showAvailability ,\r\n                      'today': !showAvailability,\r\n                      'day-circle': getStatusArray(getTodaysLessons(day.number)).length > 0 || !showAvailability }\" \r\n                      [class]=\"day.availability\">\r\n                        <div class=\"text\" >{{day.number}}</div>\r\n                        <ng-container *ngIf=\"(selectedDay === day.number) && showAvailability\">\r\n                          <div class=\"selected-check border-circle low-bg flex align-items-center justify-content-center absolute\">\r\n                          <i class=\"pi pi-check font-3xs\"></i>\r\n                        </div>\r\n                        </ng-container>\r\n                      </span>\r\n                      \r\n                    <ng-container *ngFor=\"let status of getStatusArray(getTodaysLessons(day.number)); let i = index;\">\r\n                      <div class=\"circle-me absolute\" [style.top.px]=\"getItemsPositionTop(i)\" [style.right.px]=\"getItemsPositionRight(i)\">\r\n                        <div class=\"circle-me counts border-round-3xl\">\r\n                          <div class=\"count border-circle\" [ngClass]=\"status.status + '-gradient-lesson-bg'\">\r\n                            <div class=\"count-status\"> {{status.count}}</div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                  </ng-container>\r\n                    </ng-container>\r\n                    <ng-template #elseBlock>\r\n                      <span *ngIf=\"day.number\" \r\n                      [ngClass]=\"{ 'active': selectedDay === day.number, \r\n                      'high-bg': isHighAvailability(day.number) && !isDayInThePast(day.number),\r\n                      'low-bg': !isHighAvailability(day.number) && showAvailability && !isDayInThePast(day.number), \r\n                      'is-past': showAvailability && isDayInThePast(day.number), \r\n                      'no-bg': isDayOff(day.number), \r\n                      'circle': showAvailability ,\r\n                      'day-circle': getStatusArray(getTodaysLessons(day.number)).length > 0}\" \r\n                      [class]=\"day.availability\">\r\n                      <div>{{ day.number }}</div>\r\n                      <ng-container *ngIf=\"(selectedDay === day.number) && showAvailability\">\r\n                        <div class=\"selected-check border-circle low-bg flex align-items-center justify-content-center absolute\">\r\n                        <i class=\"pi pi-check font-3xs\"></i>\r\n                      </div>\r\n                      </ng-container>\r\n                    </span>\r\n                    <ng-container *ngIf=\"day.number\" >\r\n                    <ng-container *ngFor=\"let status of getStatusArray(getTodaysLessons(day.number)); let i = index;\">\r\n                      <div class=\"circle-me absolute\" [style.top.px]=\"getItemsPositionTop(i)\" [style.right.px]=\"getItemsPositionRight(i)\">\r\n                        <div class=\"circle-me counts border-round-3xl\">\r\n                          <div class=\"count border-circle\" [ngClass]=\"status.status + '-gradient-lesson-bg'\">\r\n                            <div class=\"count-status\"> {{status.count}}</div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                  </ng-container>\r\n                </ng-container>\r\n                    </ng-template>\r\n                  </td>\r\n                </ng-container>\r\n              </tr>\r\n            </ng-container>\r\n          </ng-container>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </div>\r\n</app-block-viewer>"], "mappings": "AAAA,SAA2FA,YAAY,QAA+E,eAAe;AAKrM,SAASC,OAAO,QAAQ,SAAS;AACjC,OAAO,KAAKC,MAAM,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICA1BC,EAAA,CAAAC,uBAAA,GAAoC;IAClCD,EAAA,CAAAE,cAAA,gBAAkI;IAA5GF,EAAA,CAAAG,UAAA,mBAAAC,wEAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC;IAE/CV,EADA,CAAAW,SAAA,eAA+D,eACpC;IAC7BX,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAE,cAAA,iBAAqG;IAA7FF,EAAA,CAAAG,UAAA,mBAAAU,wEAAA;MAAAb,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAO,cAAA,EAAgB;IAAA,EAAC;IAChCd,EAAA,CAAAW,SAAA,eAAgE;IAClEX,EAAA,CAAAY,YAAA,EAAS;;;;;;IAOLZ,EAAA,CAAAC,uBAAA,GAA2D;IACzDD,EAAA,CAAAE,cAAA,aAA0B;IAAAF,EAAA,CAAAe,MAAA,GAAS;IAAAf,EAAA,CAAAY,YAAA,EAAK;;;;;IAAdZ,EAAA,CAAAgB,SAAA,GAAS;IAAThB,EAAA,CAAAiB,iBAAA,CAAAC,MAAA,CAAS;;;;;IAsBzBlB,EAAA,CAAAC,uBAAA,GAAuE;IACrED,EAAA,CAAAE,cAAA,cAAyG;IACzGF,EAAA,CAAAW,SAAA,YAAoC;IACtCX,EAAA,CAAAY,YAAA,EAAM;;;;;;IAIVZ,EAAA,CAAAC,uBAAA,GAAkG;IAI1FD,EAHN,CAAAE,cAAA,cAAoH,cACnE,cACsC,cACvD;IAACF,EAAA,CAAAe,MAAA,GAAgB;IAGjDf,EAHiD,CAAAY,YAAA,EAAM,EAC7C,EACF,EACF;;;;;;;IAN0BZ,EAAA,CAAAgB,SAAA,EAAuC;IAAChB,EAAxC,CAAAmB,WAAA,QAAAZ,MAAA,CAAAa,mBAAA,CAAAC,IAAA,QAAuC,UAAAd,MAAA,CAAAe,qBAAA,CAAAD,IAAA,QAA4C;IAE9ErB,EAAA,CAAAgB,SAAA,GAAiD;IAAjDhB,EAAA,CAAAuB,UAAA,YAAAC,SAAA,CAAAC,MAAA,yBAAiD;IACrDzB,EAAA,CAAAgB,SAAA,GAAgB;IAAhBhB,EAAA,CAAA0B,kBAAA,MAAAF,SAAA,CAAAG,KAAA,KAAgB;;;;;IAvBnD3B,EAAA,CAAAC,uBAAA,GAA8D;IAW1DD,EAVF,CAAAE,cAAA,eAS2B,cACN;IAAAF,EAAA,CAAAe,MAAA,GAAc;IAAAf,EAAA,CAAAY,YAAA,EAAM;IACvCZ,EAAA,CAAA4B,UAAA,IAAAC,4GAAA,0BAAuE;IAKzE7B,EAAA,CAAAY,YAAA,EAAO;IAETZ,EAAA,CAAA4B,UAAA,IAAAE,4GAAA,0BAAkG;;;;;;IAThG9B,EAAA,CAAAgB,SAAA,EAA0B;IAA1BhB,EAAA,CAAA+B,UAAA,CAAAC,MAAA,CAAAC,YAAA,CAA0B;IAR1BjC,EAAA,CAAAuB,UAAA,YAAAvB,EAAA,CAAAkC,eAAA,IAAAC,GAAA,EAAA5B,MAAA,CAAA6B,WAAA,KAAAJ,MAAA,CAAAK,MAAA,EAAA9B,MAAA,CAAA+B,kBAAA,CAAAN,MAAA,CAAAK,MAAA,MAAA9B,MAAA,CAAAgC,cAAA,CAAAP,MAAA,CAAAK,MAAA,IAAA9B,MAAA,CAAA+B,kBAAA,CAAAN,MAAA,CAAAK,MAAA,KAAA9B,MAAA,CAAAiC,gBAAA,KAAAjC,MAAA,CAAAgC,cAAA,CAAAP,MAAA,CAAAK,MAAA,GAAA9B,MAAA,CAAAiC,gBAAA,IAAAjC,MAAA,CAAAgC,cAAA,CAAAP,MAAA,CAAAK,MAAA,GAAA9B,MAAA,CAAAkC,QAAA,CAAAT,MAAA,CAAAK,MAAA,GAAA9B,MAAA,CAAAiC,gBAAA,GAAAjC,MAAA,CAAAiC,gBAAA,EAAAjC,MAAA,CAAAmC,cAAA,CAAAnC,MAAA,CAAAoC,gBAAA,CAAAX,MAAA,CAAAK,MAAA,GAAAO,MAAA,SAAArC,MAAA,CAAAiC,gBAAA,EAO6F;IAExExC,EAAA,CAAAgB,SAAA,GAAc;IAAdhB,EAAA,CAAAiB,iBAAA,CAAAe,MAAA,CAAAK,MAAA,CAAc;IAClBrC,EAAA,CAAAgB,SAAA,EAAsD;IAAtDhB,EAAA,CAAAuB,UAAA,SAAAhB,MAAA,CAAA6B,WAAA,KAAAJ,MAAA,CAAAK,MAAA,IAAA9B,MAAA,CAAAiC,gBAAA,CAAsD;IAOxCxC,EAAA,CAAAgB,SAAA,EAAiD;IAAjDhB,EAAA,CAAAuB,UAAA,YAAAhB,MAAA,CAAAmC,cAAA,CAAAnC,MAAA,CAAAoC,gBAAA,CAAAX,MAAA,CAAAK,MAAA,GAAiD;;;;;IAqBhFrC,EAAA,CAAAC,uBAAA,GAAuE;IACrED,EAAA,CAAAE,cAAA,cAAyG;IACzGF,EAAA,CAAAW,SAAA,YAAoC;IACtCX,EAAA,CAAAY,YAAA,EAAM;;;;;;IAJNZ,EATA,CAAAE,cAAA,eAQ2B,UACtB;IAAAF,EAAA,CAAAe,MAAA,GAAgB;IAAAf,EAAA,CAAAY,YAAA,EAAM;IAC3BZ,EAAA,CAAA4B,UAAA,IAAAiB,kHAAA,0BAAuE;IAKzE7C,EAAA,CAAAY,YAAA,EAAO;;;;;IAPLZ,EAAA,CAAA+B,UAAA,CAAAC,MAAA,CAAAC,YAAA,CAA0B;IAP1BjC,EAAA,CAAAuB,UAAA,YAAAvB,EAAA,CAAA8C,eAAA,IAAAC,GAAA,EAAAxC,MAAA,CAAA6B,WAAA,KAAAJ,MAAA,CAAAK,MAAA,EAAA9B,MAAA,CAAA+B,kBAAA,CAAAN,MAAA,CAAAK,MAAA,MAAA9B,MAAA,CAAAgC,cAAA,CAAAP,MAAA,CAAAK,MAAA,IAAA9B,MAAA,CAAA+B,kBAAA,CAAAN,MAAA,CAAAK,MAAA,KAAA9B,MAAA,CAAAiC,gBAAA,KAAAjC,MAAA,CAAAgC,cAAA,CAAAP,MAAA,CAAAK,MAAA,GAAA9B,MAAA,CAAAiC,gBAAA,IAAAjC,MAAA,CAAAgC,cAAA,CAAAP,MAAA,CAAAK,MAAA,GAAA9B,MAAA,CAAAkC,QAAA,CAAAT,MAAA,CAAAK,MAAA,GAAA9B,MAAA,CAAAiC,gBAAA,EAAAjC,MAAA,CAAAmC,cAAA,CAAAnC,MAAA,CAAAoC,gBAAA,CAAAX,MAAA,CAAAK,MAAA,GAAAO,MAAA,MAMuE;IAElE5C,EAAA,CAAAgB,SAAA,GAAgB;IAAhBhB,EAAA,CAAAiB,iBAAA,CAAAe,MAAA,CAAAK,MAAA,CAAgB;IACNrC,EAAA,CAAAgB,SAAA,EAAsD;IAAtDhB,EAAA,CAAAuB,UAAA,SAAAhB,MAAA,CAAA6B,WAAA,KAAAJ,MAAA,CAAAK,MAAA,IAAA9B,MAAA,CAAAiC,gBAAA,CAAsD;;;;;IAOvExC,EAAA,CAAAC,uBAAA,GAAkG;IAI1FD,EAHN,CAAAE,cAAA,cAAoH,cACnE,cACsC,cACvD;IAACF,EAAA,CAAAe,MAAA,GAAgB;IAGjDf,EAHiD,CAAAY,YAAA,EAAM,EAC7C,EACF,EACF;;;;;;;IAN0BZ,EAAA,CAAAgB,SAAA,EAAuC;IAAChB,EAAxC,CAAAmB,WAAA,QAAAZ,MAAA,CAAAa,mBAAA,CAAA4B,IAAA,QAAuC,UAAAzC,MAAA,CAAAe,qBAAA,CAAA0B,IAAA,QAA4C;IAE9EhD,EAAA,CAAAgB,SAAA,GAAiD;IAAjDhB,EAAA,CAAAuB,UAAA,YAAA0B,SAAA,CAAAxB,MAAA,yBAAiD;IACrDzB,EAAA,CAAAgB,SAAA,GAAgB;IAAhBhB,EAAA,CAAA0B,kBAAA,MAAAuB,SAAA,CAAAtB,KAAA,KAAgB;;;;;IALnD3B,EAAA,CAAAC,uBAAA,GAAkC;IAClCD,EAAA,CAAA4B,UAAA,IAAAsB,0HAAA,0BAAkG;;;;;;IAAjElD,EAAA,CAAAgB,SAAA,EAAiD;IAAjDhB,EAAA,CAAAuB,UAAA,YAAAhB,MAAA,CAAAmC,cAAA,CAAAnC,MAAA,CAAAoC,gBAAA,CAAAX,MAAA,CAAAK,MAAA,GAAiD;;;;;IADlFrC,EAhBE,CAAA4B,UAAA,IAAAuB,mGAAA,oBAQ2B,IAAAC,2GAAA,0BAQK;;;;IAhBzBpD,EAAA,CAAAuB,UAAA,SAAAS,MAAA,CAAAK,MAAA,CAAgB;IAgBVrC,EAAA,CAAAgB,SAAA,EAAgB;IAAhBhB,EAAA,CAAAuB,UAAA,SAAAS,MAAA,CAAAK,MAAA,CAAgB;;;;;;IAhDnCrC,EAAA,CAAAC,uBAAA,GAAuC;IACrCD,EAAA,CAAAE,cAAA,aAA4E;IAAnCF,EAAA,CAAAG,UAAA,mBAAAkD,mGAAA;MAAA,MAAArB,MAAA,GAAAhC,EAAA,CAAAK,aAAA,CAAAiD,GAAA,EAAAC,SAAA;MAAA,MAAAhD,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAiD,YAAA,CAAAxB,MAAA,CAAAK,MAAA,CAAwB;IAAA,EAAC;IA8BzErC,EA7BA,CAAA4B,UAAA,IAAA6B,6FAAA,4BAA8D,IAAAC,4FAAA,gCAAA1D,EAAA,CAAA2D,sBAAA,CA6BtC;IA6B1B3D,EAAA,CAAAY,YAAA,EAAK;;;;;;;IA1DYZ,EAAA,CAAAgB,SAAA,GAA+B;IAAAhB,EAA/B,CAAAuB,UAAA,SAAAhB,MAAA,CAAAqD,WAAA,CAAA5B,MAAA,CAAAK,MAAA,EAA+B,aAAAwB,aAAA,CAAc;;;;;IAJpE7D,EAAA,CAAAC,uBAAA,GAAgE;IAC9DD,EAAA,CAAAE,cAAA,SAAI;IACFF,EAAA,CAAA4B,UAAA,IAAAkC,8EAAA,0BAAuC;IA8DzC9D,EAAA,CAAAY,YAAA,EAAK;;;;;IA9D2BZ,EAAA,CAAAgB,SAAA,GAAO;IAAPhB,EAAA,CAAAuB,UAAA,YAAAwC,QAAA,CAAO;;;;;IAH3C/D,EAAA,CAAAC,uBAAA,GAA6B;IAC3BD,EAAA,CAAA4B,UAAA,IAAAoC,+DAAA,0BAAgE;;;;;IAAjChE,EAAA,CAAAgB,SAAA,EAA+B;IAA/BhB,EAAA,CAAAuB,UAAA,YAAAhB,MAAA,CAAA0D,eAAA,CAAA1D,MAAA,CAAA2D,WAAA,EAA+B;;;ADP1E,OAAM,MAAOC,uBAAuB;EAuBlCC,YACUC,GAAsB,EACtBC,UAAsB,EACtBC,eAAgC,EAChCC,WAAwB,EACxBC,gBAAkC;IAJlC,KAAAJ,GAAG,GAAHA,GAAG;IACH,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;IA1BlB,KAAAC,IAAI,GAAG,IAAI5E,OAAO,EAAE;IACnB,KAAA6E,aAAa,GAAG,IAAI;IACpB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,KAAK,GAAG,QAAQ;IAChB,KAAAC,OAAO,GAAQ,EAAE;IACjB,KAAAC,cAAc,GAAG,2DAA2D;IAC5E,KAAAC,kBAAkB,GAAG,IAAI;IACzB,KAAAC,MAAM,GAAG,EAAE;IACX,KAAAzC,gBAAgB,GAAY,KAAK;IAChC,KAAA0C,UAAU,GAAG,IAAIrF,YAAY,EAAQ;IACrC,KAAAsF,YAAY,GAAG,IAAItF,YAAY,EAAU;IACzC,KAAAuF,mBAAmB,GAAG,IAAIvF,YAAY,EAAU;IAC1D,KAAAwF,QAAQ,GAAa,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;IACxD,KAAAnB,WAAW,GAAS,IAAIoB,IAAI,EAAE;IAC9B,KAAAC,YAAY,GAA8B,EAAE;IAG5C,KAAAC,iBAAiB,GAAU,EAAE;IAE7B,KAAAC,MAAM,GAAG,KAAK;EAQR;EAENC,QAAQA,CAAA;IAEN,IAAI,IAAI,CAACV,kBAAkB,EAAE;MAC3B,IAAI,CAACd,WAAW,GAAG,IAAIoB,IAAI,CAAC,IAAI,CAACN,kBAAkB,CAAC;IACtD,CAAC,MAAM;MACL,IAAI,CAACd,WAAW,GAAG,IAAIoB,IAAI,EAAE;IAC/B;IACA,IAAI,CAACK,IAAI,GAAG,IAAI,CAACnB,WAAW,CAACoB,eAAe,EAAE;IAC9C,IAAI,IAAI,CAACpD,gBAAgB,EAAE;MACzB;IAAA,CACD,MAAM;MACL,IAAI,CAACiD,MAAM,GAAG,IAAI;IACpB;EACF;EAEAI,WAAWA,CAACC,OAAsB;IAChC,IAAI,IAAI,CAAChB,OAAO,CAAClC,MAAM,GAAG,CAAC,EAAE;MAC3B,IAAI,CAACmD,2BAA2B,EAAE;IAEpC;IACA,IAAID,OAAO,CAACb,MAAM,IAAI,CAACa,OAAO,CAACb,MAAM,CAACe,WAAW,EAAE;MACjD,IAAI,CAACf,MAAM,GAAGa,OAAO,CAACb,MAAM,CAACgB,YAAY;MACzC,IAAIH,OAAO,CAACd,kBAAkB,IAAIc,OAAO,CAACd,kBAAkB,CAACiB,YAAY,EAAE;QACzE,IAAI,CAAC/B,WAAW,GAAI4B,OAAO,CAACd,kBAAkB,CAACiB,YAAa;MAC9D;MACA,IAAI,CAACC,gBAAgB,EAAE;MACvB,IAAI,CAAC7B,GAAG,CAAC8B,aAAa,EAAE;IAC1B;IACA,IAAIL,OAAO,CAACd,kBAAkB,IAAI,CAACc,OAAO,CAACd,kBAAkB,CAACgB,WAAW,EAAE;MACzE,IAAI,CAAC9B,WAAW,GAAI4B,OAAO,CAACd,kBAAkB,CAACiB,YAAa;MAC5D,IAAI,CAAC/B,WAAW,CAACkC,QAAQ,CAACN,OAAO,CAACd,kBAAkB,CAACiB,YAAY,CAACI,QAAQ,EAAE,CAAC;MAC7E,IAAI,CAACC,cAAc,CAAC,IAAI,CAACpC,WAAW,CAACqC,OAAO,EAAE,CAAC;MAC/C,IAAI,IAAI,CAAC/D,gBAAgB,EAAE;QACzB,IAAI,CAAC0D,gBAAgB,EAAE;MACzB;MACA,IAAI,CAAC7B,GAAG,CAAC8B,aAAa,EAAE;IAC1B;EACF;EAEAK,eAAeA,CAAA;IACb,IAAI,CAACC,qBAAqB,EAAE;EAC9B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAChC,IAAI,CAACiC,WAAW,EAAE;EACzB;EAEAC,cAAcA,CAACC,IAAU;IACvB,MAAMC,IAAI,GAAGD,IAAI,CAACE,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGH,IAAI,CAACR,QAAQ,EAAE;IAC7B,OAAO,IAAIf,IAAI,CAACwB,IAAI,EAAEE,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,CAACT,OAAO,EAAE;EAC/C;EAEAtC,eAAeA,CAAC4C,IAAU;IACxB,MAAMI,KAAK,GAAY,EAAE;IACzB,MAAMC,gBAAgB,GAAG,IAAI5B,IAAI,CAACuB,IAAI,CAACE,WAAW,EAAE,EAAEF,IAAI,CAACR,QAAQ,EAAE,EAAE,CAAC,CAAC;IACzE,MAAMc,eAAe,GAAG,CAACD,gBAAgB,CAACE,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;IAC7D,MAAMC,WAAW,GAAG,IAAI/B,IAAI,CAACuB,IAAI,CAACE,WAAW,EAAE,EAAEF,IAAI,CAACR,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAACE,OAAO,EAAE;IAElF,MAAMe,mBAAmB,GAAG,IAAIhC,IAAI,CAACuB,IAAI,CAACE,WAAW,EAAE,EAAEF,IAAI,CAACR,QAAQ,EAAE,EAAE,CAAC,GAAGc,eAAe,CAAC;IAC9F,MAAMI,eAAe,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACN,eAAe,GAAGE,WAAW,IAAI,CAAC,CAAC,CAAC,CAAC;IACxE,MAAMK,cAAc,GAAGH,eAAe,GAAG,CAAC;IAE1C,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,cAAc,EAAEC,CAAC,IAAI,CAAC,EAAE;MAC1C,MAAMC,IAAI,GAAU,EAAE;MACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1B,MAAMC,SAAS,GAAIH,CAAC,GAAGE,CAAC,GAAG,CAAC,GAAIV,eAAe;QAC/C,MAAMY,OAAO,GAAG,IAAIzC,IAAI,CAACuB,IAAI,CAACE,WAAW,EAAE,EAAEF,IAAI,CAACR,QAAQ,EAAE,EAAEyB,SAAS,CAAC;QACxE,MAAME,WAAW,GAAGF,SAAS,GAAG,CAAC;QACjC,MAAMG,WAAW,GAAGH,SAAS,GAAGT,WAAW;QAC3C,MAAMa,cAAc,GAAG,CAACF,WAAW,IAAI,CAACC,WAAW;QAEnD,MAAME,GAAG,GAAG;UACV9F,MAAM,EAAE6F,cAAc,GAAGJ,SAAS,GAAG,EAAE;UACvCM,IAAI,EAAE,IAAI,CAAC/C,QAAQ,CAACwC,CAAC,CAAC;UACtBG,WAAW;UACXC,WAAW;UACXC;SACD;QACDN,IAAI,CAACS,IAAI,CAACF,GAAG,CAAC;MAChB;MACAlB,KAAK,CAACoB,IAAI,CAACT,IAAI,CAAC;IAClB;IACA,OAAOX,KAAK;EACd;EACArD,WAAWA,CAACuE,GAAW;IACrB,MAAMjE,WAAW,GAAGnE,MAAM,EAAE;IAC5B,MAAMuI,WAAW,GAAGvI,MAAM,CAAC,IAAI,CAACmE,WAAW,CAAC,CAAC2C,IAAI,CAACsB,GAAG,CAAC;IAEtD,OACEG,WAAW,CAACC,MAAM,CAACrE,WAAW,EAAE,KAAK,CAAC,IACtCoE,WAAW,CAACC,MAAM,CAAC,IAAI,CAACrE,WAAW,EAAE,OAAO,CAAC;EAEjD;EAEAsE,eAAeA,CAAA;IACb,IAAI,CAACjE,eAAe,CAACkE,8BAA8B,CAAC1I,MAAM,CAAC,IAAI,CAACmE,WAAW,CAAC,CAACwE,MAAM,EAAE,CAAC;IACtF,IAAI,CAACtD,mBAAmB,CAACuD,IAAI,CAAC5I,MAAM,CAAC,IAAI,CAACmE,WAAW,CAAC,CAAC8C,KAAK,EAAE,CAAC;IAC/D,OAAOjH,MAAM,CAAC,IAAI,CAACmE,WAAW,CAAC,CAAC0E,MAAM,CAAC,MAAM,CAAC;EAChD;EAEA9H,cAAcA,CAAA;IACZ,IAAI,CAACoD,WAAW,CAACkC,QAAQ,CAAC,IAAI,CAAClC,WAAW,CAACmC,QAAQ,EAAE,GAAG,CAAC,CAAC;IAC1D,IAAI,CAACwC,iBAAiB,EAAE;IACxB,IAAI,CAACpC,qBAAqB,EAAE;EAC9B;EAEA/F,eAAeA,CAAA;IACb,IAAI,CAACwD,WAAW,CAACkC,QAAQ,CAAC,IAAI,CAAClC,WAAW,CAACmC,QAAQ,EAAE,GAAG,CAAC,CAAC;IAC1D,IAAI,CAACwC,iBAAiB,EAAE;IACxB,IAAI,CAACpC,qBAAqB,EAAE;EAC9B;EAEAH,cAAcA,CAAC6B,GAAW;IACxB,IAAI,CAAC5C,YAAY,GAAG,EAAE;IACtB,MAAMuD,GAAG,GAAG,GAAG,IAAI,CAAC5E,WAAW,CAAC6C,WAAW,EAAE,IAAI,IAAI,CAAC7C,WAAW,CAACmC,QAAQ,EAAE,EAAE;IAC9E,IAAI,CAACd,YAAY,CAACuD,GAAG,CAAC,GAAGX,GAAG;EAC9B;EAEA,IAAI/F,WAAWA,CAAA;IACb,MAAM0G,GAAG,GAAG,GAAG,IAAI,CAAC5E,WAAW,CAAC6C,WAAW,EAAE,IAAI,IAAI,CAAC7C,WAAW,CAACmC,QAAQ,EAAE,EAAE;IAC9E,OAAO,IAAI,CAACd,YAAY,CAACuD,GAAG,CAAC;EAC/B;EAEAtF,YAAYA,CAAC2E,GAAW;IACtB,IAAI,IAAI,CAAC3D,WAAW,CAACuE,SAAS,EAAE;MAC9B,IAAI,IAAI,CAACvG,gBAAgB,IAAI,IAAI,CAACD,cAAc,CAAC4F,GAAG,CAAC,EAAE;QACrD;MACF;IACF;IACA,MAAMG,WAAW,GAAG,IAAIhD,IAAI,CAAC,IAAI,CAACpB,WAAW,CAAC6C,WAAW,EAAE,EAAE,IAAI,CAAC7C,WAAW,CAACmC,QAAQ,EAAE,EAAE8B,GAAG,CAAC;IAC9F,IAAI,CAACjD,UAAU,CAACyD,IAAI,CAACL,WAAW,CAAC;IACjC,IAAI,CAAC7D,gBAAgB,CAACuE,qBAAqB,CAACV,WAAW,CAAC;IACxD,IAAI,CAAChC,cAAc,CAAC6B,GAAG,CAAC;EAC1B;EAEAD,cAAcA,CAACJ,SAAiB;IAC9B,OAAO,IAAIxC,IAAI,CAAC,IAAI,CAACpB,WAAW,CAAC6C,WAAW,EAAE,EAAE,IAAI,CAAC7C,WAAW,CAACmC,QAAQ,EAAE,EAAEyB,SAAS,CAAC,CAACzB,QAAQ,EAAE,KAAK,IAAI,CAACnC,WAAW,CAACmC,QAAQ,EAAE;EACpI;EAEA4B,WAAWA,CAACH,SAAiB;IAC3B,OAAO,IAAIxC,IAAI,CAAC,IAAI,CAACpB,WAAW,CAAC6C,WAAW,EAAE,EAAE,IAAI,CAAC7C,WAAW,CAACmC,QAAQ,EAAE,EAAEyB,SAAS,CAAC,CAACzB,QAAQ,EAAE,KAAK,IAAI,CAACnC,WAAW,CAACmC,QAAQ,EAAE,GAAG,CAAC;EACxI;EAEAH,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACjB,MAAM,EAAE;MAChB;IACF;IACA,IAAG,IAAI,CAACzC,gBAAgB,EAAC;MACvB,MAAMyG,IAAI,GAAG;QACX,GAAG,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAChF,WAAW,CAAC;QAC9Ce,MAAM,EAAE,IAAI,CAACA;OACd;MACD,IAAI,CAACP,IAAI,CAACyE,IAAI,GAAG,IAAI,CAACC,wBAAwB,CAACH,IAAI,CAAC,CAACI,SAAS,CAAEC,GAAG,IAAI;QACrE,IAAI,CAAC9D,iBAAiB,GAAG8D,GAAG,CAACC,oBAAoB;QACjD,IAAI,CAACC,OAAO,GAAGF,GAAG,CAACE,OAAO;QAC1B,IAAI,CAAC/D,MAAM,GAAG,IAAI;QAClB,IAAI,CAACpB,GAAG,CAAC8B,aAAa,EAAE;MAC1B,CAAC,CAAC;IACJ,CAAC,MACI;MACH,IAAI,CAACV,MAAM,GAAG,IAAI;IACpB;EACF;EAEAhD,QAAQA,CAACqF,SAAiB;IACxB,IAAG,IAAI,CAACtF,gBAAgB,EAAC;MACvB,MAAMiH,UAAU,GAAG,IAAI,CAACvF,WAAW,CAAC6C,WAAW,EAAE,GAAG,GAAG,IAAI,IAAI,CAAC7C,WAAW,CAACmC,QAAQ,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGyB,SAAS;MAC7G,MAAM5D,WAAW,GAAG,IAAIoB,IAAI,CAACmE,UAAU,CAAC;MAExC,KAAK,MAAMC,MAAM,IAAI,IAAI,CAACF,OAAO,EAAE;QACjC,MAAMG,IAAI,GAAG,IAAIrE,IAAI,CAACoE,MAAM,CAACE,MAAM,CAACD,IAAI,CAAC;QACzC,MAAME,EAAE,GAAG,IAAIvE,IAAI,CAACoE,MAAM,CAACE,MAAM,CAACC,EAAE,CAAC;QAErC,IAAI3F,WAAW,IAAIyF,IAAI,IAAIzF,WAAW,IAAI2F,EAAE,EAAE;UAC5C,OAAO,IAAI;QACb;MACF;IACF;IACA,OAAO,KAAK;EACd;EAEAtH,cAAcA,CAACuF,SAAiB;IAC9B,MAAM2B,UAAU,GAAG,IAAI,CAACvF,WAAW,CAAC6C,WAAW,EAAE,GAAG,GAAG,IAAI,IAAI,CAAC7C,WAAW,CAACmC,QAAQ,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGyB,SAAS;IAC7G,MAAMgC,KAAK,GAAG,IAAIxE,IAAI,EAAE;IACxB,MAAMpB,WAAW,GAAG,IAAIoB,IAAI,CAACmE,UAAU,CAAC;IAExC;IACAK,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1B7F,WAAW,CAAC6F,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAEhC,OAAO7F,WAAW,GAAG4F,KAAK;EAC5B;EAEAxH,kBAAkBA,CAACwF,SAAiB;IAClC,IAAI,IAAI,CAACtF,gBAAgB,EAAE;MACzB,MAAMiH,UAAU,GAAG,GAAG,IAAI,CAACvF,WAAW,CAAC6C,WAAW,EAAE,IAAI,IAAI,CAAC7C,WAAW,CAACmC,QAAQ,EAAE,GAAG,CAAC,IAAIyB,SAAS,EAAE;MACtG,MAAM5D,WAAW,GAAG,IAAIoB,IAAI,CAACmE,UAAU,CAAC;MAExC,MAAMxH,YAAY,GAAG,IAAI,CAACuD,iBAAiB,CAACwE,IAAI,CAAE/H,YAAY,IAAI;QAChE,MAAMgI,qBAAqB,GAAGhI,YAAY,CAAC4E,IAAI,CAACqD,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;QACtE,MAAMC,gBAAgB,GAAG,IAAI/E,IAAI,CAAC2E,qBAAqB,CAAC,CAAC,CAAC,EAAEA,qBAAqB,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEA,qBAAqB,CAAC,CAAC,CAAC,CAAC;QAEnH,OAAO/F,WAAW,CAACoG,YAAY,EAAE,KAAKD,gBAAgB,CAACC,YAAY,EAAE;MACvE,CAAC,CAAC;MAEF,IAAIrI,YAAY,EAAE;QAChB,OAAOuF,IAAI,CAAC+C,GAAG,CAACtI,YAAY,CAACuI,cAAc,CAAC,IAAI,CAAC;MACnD;IACF;IAEA,OAAO,KAAK;EACd;EAGA7H,gBAAgBA,CAACwF,GAAW;IAC1B,MAAMsC,SAAS,GAAG,IAAInF,IAAI,CAAC,IAAI,CAACpB,WAAW,CAAC6C,WAAW,EAAE,EAAE,IAAI,CAAC7C,WAAW,CAACmC,QAAQ,EAAE,EAAE8B,GAAG,CAAC;IAC5F,MAAMuC,YAAY,GAAG,IAAI,CAAC5F,OAAO,CAAC6F,MAAM,CAAC,CAACC,MAAW,EAAEC,MAAW,KAAI;MACpE,MAAMhE,IAAI,GAAG,IAAIvB,IAAI,CAACuF,MAAM,CAACC,YAAY,CAAC,CAACC,kBAAkB,EAAE;MAC/D,MAAMtJ,MAAM,GAAGoJ,MAAM,CAACpJ,MAAM;MAC5B,IAAI,CAACmJ,MAAM,CAAC/D,IAAI,CAAC,EAAE;QACjB+D,MAAM,CAAC/D,IAAI,CAAC,GAAG,EAAE;MACnB;MACA,IAAI,CAAC+D,MAAM,CAAC/D,IAAI,CAAC,CAACpF,MAAM,CAAC,EAAE;QACzBmJ,MAAM,CAAC/D,IAAI,CAAC,CAACpF,MAAM,CAAC,GAAG;UAAEE,KAAK,EAAE;QAAC,CAAE;MACrC;MACAiJ,MAAM,CAAC/D,IAAI,CAAC,CAACpF,MAAM,CAAC,CAACE,KAAK,EAAE;MAC5B,OAAOiJ,MAAM;IACf,CAAC,EAAE,EAAE,CAAC;IACN,MAAMI,aAAa,GAAGN,YAAY,CAACD,SAAS,CAACM,kBAAkB,EAAE,CAAC,IAAI,EAAE;IACxE,MAAME,kBAAkB,GAAGC,MAAM,CAACC,IAAI,CAACH,aAAa,CAAC,CAACL,MAAM,CAAC,CAACS,MAAW,EAAE3J,MAAc,KAAI;MAC3F2J,MAAM,CAAC3J,MAAM,CAAC,GAAGuJ,aAAa,CAACvJ,MAAM,CAAC,CAACE,KAAK;MAC5C,OAAOyJ,MAAM;IACf,CAAC,EAAE,EAAE,CAAC;IAEN,OAAOH,kBAAkB;EAC3B;EAEA;;;;;EAKAvI,cAAcA,CAACjB,MAAU;IACvB,OAAOyJ,MAAM,CAACG,OAAO,CAAC5J,MAAM,CAAC,CAAC0I,GAAG,CAAC,CAAC,CAACrB,GAAG,EAAEwC,KAAK,CAAC,KAAI;MACjD,OAAO;QAAE7J,MAAM,EAAEqH,GAAG,CAACyC,WAAW,EAAE,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;QAAE7J,KAAK,EAAE2J;MAAK,CAAE;IACtE,CAAC,CAAC;EACJ;EAEAlK,mBAAmBA,CAACuG,CAAS;IAC3B,IAAIA,CAAC,KAAK,CAAC,EAAE;MACX,OAAO,CAAC;IACV,CAAC,MAAM,IAAIA,CAAC,KAAK,CAAC,EAAE;MAClB,OAAO,EAAE;IACX,CAAC,MAAM,IAAIA,CAAC,KAAK,CAAC,EAAE;MAClB,OAAO,EAAE;IACX,CAAC,MAAM,IAAIA,CAAC,KAAK,CAAC,EAAE;MAClB,OAAO,EAAE;IACX,CAAC,MAAM,IAAIA,CAAC,KAAK,CAAC,EAAE;MAClB,OAAO,EAAE;IACX;IACA,OAAO,EAAE;EACX;EAEArG,qBAAqBA,CAACqG,CAAS;IAC7B,IAAIA,CAAC,KAAK,CAAC,EAAE;MACX,OAAO,EAAE;IACX,CAAC,MAAM,IAAIA,CAAC,KAAK,CAAC,EAAE;MAClB,OAAO,CAAC;IACV,CAAC,MAAM,IAAIA,CAAC,KAAK,CAAC,EAAE;MAClB,OAAO,CAAC;IACV,CAAC,MAAM,IAAIA,CAAC,KAAK,CAAC,EAAE;MAClB,OAAO,EAAE;IACX,CAAC,MAAM,IAAIA,CAAC,KAAK,CAAC,EAAE;MAClB,OAAO,EAAE;IACX;IACA,OAAO,EAAE;EACX;EAEQyB,wBAAwBA,CAACqC,WAAgB;IAC/C,OAAO,IAAI,CAAClH,eAAe,CAACmH,4BAA4B,CAACD,WAAW,CAAC;EACvE;EAEA;;;;;;;EAOQ5C,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAACrG,gBAAgB,EAAE;MACzB,IAAI,CAACiD,MAAM,GAAG,KAAK;MACnB,IAAI,CAACS,gBAAgB,EAAE;IACzB,CAAC,MAAM;MACL,IAAI,CAACT,MAAM,GAAG,IAAI;IACpB;EACF;EAEQyD,oBAAoBA,CAACrC,IAAU;IACrC,MAAM8E,QAAQ,GAAG,IAAIrG,IAAI,CAACuB,IAAI,CAACE,WAAW,EAAE,EAAEF,IAAI,CAACR,QAAQ,EAAE,EAAE,CAAC,CAAC;IACjE,MAAMuF,OAAO,GAAG,IAAItG,IAAI,CAACuB,IAAI,CAACE,WAAW,EAAE,EAAEF,IAAI,CAACR,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;IACpEsF,QAAQ,CAACE,OAAO,CAACF,QAAQ,CAACpF,OAAO,EAAE,GAAG,CAAC,CAAC;IAExCqF,OAAO,CAACC,OAAO,CAACD,OAAO,CAACrF,OAAO,EAAE,GAAG,CAAC,CAAC;IACtC,MAAMuF,iBAAiB,GAAGH,QAAQ,CAACI,WAAW,EAAE;IAChD,MAAMC,gBAAgB,GAAGJ,OAAO,CAACG,WAAW,EAAE;IAE9C,OAAO;MAAEE,QAAQ,EAAEH,iBAAiB;MAAEpD,MAAM,EAAEsD;IAAgB,CAAE;EAClE;EAEQjG,2BAA2BA,CAAA;IACjC,IAAI,CAACjB,OAAO,GAAG,EAAE,CAACoH,MAAM,CAAC,GAAG,IAAI,CAACpH,OAAO,CAAO;IAC/CqH,OAAO,CAACC,GAAG,CAAC,IAAI,CAACtH,OAAO,CAAC;IACzB,MAAM4F,YAAY,GAAG,IAAI,CAAC5F,OAAO,CAAC6F,MAAM,CAAC,CAACC,MAAW,EAAEC,MAAW,KAAI;MACpE,MAAMC,YAAY,GAAG/K,MAAM,CAAC8K,MAAM,CAACC,YAAY,CAAC;MAChD,MAAMrB,UAAU,GAAGqB,YAAY,CAAClC,MAAM,CAAC,YAAY,CAAC;MACpD,MAAMnH,MAAM,GAAGoJ,MAAM,CAACpJ,MAAM;MAC5B,IAAI,CAACmJ,MAAM,CAACnB,UAAU,CAAC,EAAE;QACvBmB,MAAM,CAACnB,UAAU,CAAC,GAAG,EAAE;MACzB;MACA,IAAI,CAACmB,MAAM,CAACnB,UAAU,CAAC,CAAChI,MAAM,CAAC,EAAE;QAC/BmJ,MAAM,CAACnB,UAAU,CAAC,CAAChI,MAAM,CAAC,GAAG;UAAEE,KAAK,EAAE;QAAC,CAAE;MAC3C;MACAiJ,MAAM,CAACnB,UAAU,CAAC,CAAChI,MAAM,CAAC,CAACE,KAAK,EAAE;MAClC,OAAOiJ,MAAM;IACf,CAAC,EAAE,EAAE,CAAC;EACR;EAEQnE,qBAAqBA,CAAA;IAC3B4F,UAAU,CAAC,MAAK;MAChB,MAAMC,aAAa,GAAG,IAAI,CAAChI,UAAU,CAACiI,aAAa,CAACC,YAAY;MAChEL,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEE,aAAa,CAAC;MAC7C;MACA,IAAI,CAAC/H,eAAe,CAACkI,+BAA+B,CAACH,aAAa,CAAC;IACnE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EAEV;EAAC,QAAAI,CAAA,G;qBAhXUvI,uBAAuB,EAAAnE,EAAA,CAAA2M,iBAAA,CAAA3M,EAAA,CAAA4M,iBAAA,GAAA5M,EAAA,CAAA2M,iBAAA,CAAA3M,EAAA,CAAA6M,UAAA,GAAA7M,EAAA,CAAA2M,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA/M,EAAA,CAAA2M,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAjN,EAAA,CAAA2M,iBAAA,CAAAO,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAvBjJ,uBAAuB;IAAAkJ,SAAA;IAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QCf9BxN,EAJN,CAAAE,cAAA,0BACqF,aACwF,UAC3I,aACU;QAAAF,EAAA,CAAAe,MAAA,GAAuB;QAAAf,EAAA,CAAAY,YAAA,EAAM;QACnEZ,EAAA,CAAA4B,UAAA,IAAA8L,+CAAA,0BAAoC;QAStC1N,EAAA,CAAAY,YAAA,EAAM;QAIAZ,EAHN,CAAAE,cAAA,gBAA+C,eACR,YAC5B,UACD;QACFF,EAAA,CAAA4B,UAAA,KAAA+L,gDAAA,0BAA2D;QAI/D3N,EADE,CAAAY,YAAA,EAAK,EACC;QACRZ,EAAA,CAAAE,cAAA,aAAO;QACLF,EAAA,CAAA4B,UAAA,KAAAgM,gDAAA,0BAA6B;QAwEvC5N,EAJQ,CAAAY,YAAA,EAAQ,EACF,EACJ,EACF,EACW;;;QAjGDZ,EAAA,CAAA6N,qBAAA,WAAAJ,GAAA,CAAA5I,KAAA,CAAkB;QACpC7E,EADkE,CAAAuB,UAAA,0BAAAkM,GAAA,CAAA7I,eAAA,qDAA6F,2CAAA6I,GAAA,CAAA7I,eAAA,yBAC5E;QACtC5E,EAAA,CAAAgB,SAAA,EAA+H;QAA/HhB,EAAA,CAAAuB,UAAA,YAAAvB,EAAA,CAAA8N,eAAA,KAAAC,GAAA,EAAAN,GAAA,CAAA7I,eAAA,GAAA6I,GAAA,CAAA7I,eAAA,EAAA6I,GAAA,CAAAjL,gBAAA,EAA+H;QACnKxC,EAAA,CAAAgB,SAAA,EAAwB;QAAxBhB,EAAA,CAAA+B,UAAA,CAAA0L,GAAA,CAAA1I,cAAA,CAAwB;QACW/E,EAAA,CAAAgB,SAAA,GAAuB;QAAvBhB,EAAA,CAAAiB,iBAAA,CAAAwM,GAAA,CAAAjF,eAAA,GAAuB;QAC9CxI,EAAA,CAAAgB,SAAA,EAAmB;QAAnBhB,EAAA,CAAAuB,UAAA,SAAAkM,GAAA,CAAA9I,aAAA,CAAmB;QAcE3E,EAAA,CAAAgB,SAAA,GAAa;QAAbhB,EAAA,CAAAuB,UAAA,YAAAkM,GAAA,CAAApI,QAAA,CAAa;QAM9BrF,EAAA,CAAAgB,SAAA,GAAY;QAAZhB,EAAA,CAAAuB,UAAA,SAAAkM,GAAA,CAAAhI,MAAA,CAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}