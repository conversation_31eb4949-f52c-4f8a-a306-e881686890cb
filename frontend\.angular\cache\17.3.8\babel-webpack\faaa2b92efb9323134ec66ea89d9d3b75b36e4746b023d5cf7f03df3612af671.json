{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"primeng/inputswitch\";\nfunction ClassroomStatusFiltersComponent_ng_container_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"label\", 4);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-inputSwitch\", 5);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ClassroomStatusFiltersComponent_ng_container_0_div_2_Template_p_inputSwitch_ngModelChange_3_listener($event) {\n      const switch_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(switch_r2.value, $event) || (switch_r2.value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_0_div_2_Template_p_inputSwitch_onChange_3_listener() {\n      const switch_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(switch_r2.label));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const switch_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(switch_r2.label);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", switch_r2.value);\n  }\n}\nfunction ClassroomStatusFiltersComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵtemplate(2, ClassroomStatusFiltersComponent_ng_container_0_div_2_Template, 4, 2, \"div\", 2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.switchesWithLabels);\n  }\n}\nfunction ClassroomStatusFiltersComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 6)(2, \"div\", 3)(3, \"label\", 4);\n    i0.ɵɵtext(4, \"Ongoing\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_1_Template_p_inputSwitch_onChange_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"Ongoing\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 3)(7, \"label\", 4);\n    i0.ɵɵtext(8, \"Completed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_1_Template_p_inputSwitch_onChange_9_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"Completed\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 3)(11, \"label\", 4);\n    i0.ɵɵtext(12, \"Expired\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_1_Template_p_inputSwitch_onChange_13_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"Expired\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 3)(15, \"label\", 4);\n    i0.ɵɵtext(16, \"All\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_1_Template_p_inputSwitch_onChange_17_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"All\"));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.Ongoing);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.Completed);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.Expired);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.All);\n  }\n}\nfunction ClassroomStatusFiltersComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 8)(3, \"label\", 4);\n    i0.ɵɵtext(4, \"Ongoing\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_2_Template_p_inputSwitch_onChange_5_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"Active\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 8)(7, \"label\", 4);\n    i0.ɵɵtext(8, \"Completed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_2_Template_p_inputSwitch_onChange_9_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"Completed\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 8)(11, \"label\", 4);\n    i0.ɵɵtext(12, \"Dismissed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_2_Template_p_inputSwitch_onChange_13_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"Dismissed\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 8)(15, \"label\", 4);\n    i0.ɵɵtext(16, \"All\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_2_Template_p_inputSwitch_onChange_17_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"All\"));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.Active);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.Completed);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.Dismissed);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.All);\n  }\n}\nfunction ClassroomStatusFiltersComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 3)(3, \"label\", 9);\n    i0.ɵɵtext(4, \"Arranged\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_3_Template_p_inputSwitch_onChange_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"Arranged\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 3)(7, \"label\", 9);\n    i0.ɵɵtext(8, \"Completed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_3_Template_p_inputSwitch_onChange_9_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"Completed\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 3)(11, \"label\", 9);\n    i0.ɵɵtext(12, \"Canceled\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_3_Template_p_inputSwitch_onChange_13_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"Canceled\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 3)(15, \"label\", 9);\n    i0.ɵɵtext(16, \"No Show\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_3_Template_p_inputSwitch_onChange_17_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"NoShow\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 3)(19, \"label\", 9);\n    i0.ɵɵtext(20, \"All\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_3_Template_p_inputSwitch_onChange_21_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"All\"));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.Arranged);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.Completed);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.Canceled);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.NoShow);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.All);\n  }\n}\nexport class ClassroomStatusFiltersComponent {\n  constructor() {\n    this.isTrial = false;\n    this.isLessonsFilter = false;\n    this.switchToggled = new EventEmitter();\n  }\n  ngOnInit() {\n    console.log(this.switchesWithLabels);\n  }\n  ngOnChanges() {\n    console.log(this.switches);\n  }\n  toggleSwitch(switchName) {\n    this.switchToggled.emit(switchName);\n  }\n  static #_ = this.ɵfac = function ClassroomStatusFiltersComponent_Factory(t) {\n    return new (t || ClassroomStatusFiltersComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ClassroomStatusFiltersComponent,\n    selectors: [[\"app-classroom-status-filters\"]],\n    inputs: {\n      switches: \"switches\",\n      switchesWithLabels: \"switchesWithLabels\",\n      isTrial: \"isTrial\",\n      isLessonsFilter: \"isLessonsFilter\"\n    },\n    outputs: {\n      switchToggled: \"switchToggled\"\n    },\n    features: [i0.ɵɵNgOnChangesFeature],\n    decls: 4,\n    vars: 4,\n    consts: [[4, \"ngIf\"], [1, \"flex\", \"flex-wrap\"], [\"class\", \"field-checkbox my-0\", 4, \"ngFor\", \"ngForOf\"], [1, \"field-checkbox\", \"my-0\"], [1, \"lesson-filter-label\", \"font-xs\", \"mr-2\"], [1, \"flex\", \"small-input-switch\", \"p-mr-2\", 3, \"ngModelChange\", \"onChange\", \"ngModel\"], [1, \"flex\", \"flex-wrap\", \"gap-2\", \"sm:gap-3\"], [1, \"flex\", \"small-input-switch\", \"p-mr-2\", 3, \"onChange\", \"ngModel\"], [1, \"field-checkbox\", \"my-2\", \"sm:my-0\"], [1, \"lesson-filter-label\", \"font-xs\", \"mr-1\"]],\n    template: function ClassroomStatusFiltersComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, ClassroomStatusFiltersComponent_ng_container_0_Template, 3, 1, \"ng-container\", 0)(1, ClassroomStatusFiltersComponent_ng_container_1_Template, 18, 4, \"ng-container\", 0)(2, ClassroomStatusFiltersComponent_ng_container_2_Template, 18, 4, \"ng-container\", 0)(3, ClassroomStatusFiltersComponent_ng_container_3_Template, 22, 5, \"ng-container\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.switchesWithLabels);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isTrial && !ctx.isLessonsFilter && !ctx.switchesWithLabels);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isTrial);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isLessonsFilter);\n      }\n    },\n    dependencies: [i1.NgForOf, i1.NgIf, i2.NgControlStatus, i2.NgModel, i3.InputSwitch],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtwoWayListener", "ClassroomStatusFiltersComponent_ng_container_0_div_2_Template_p_inputSwitch_ngModelChange_3_listener", "$event", "switch_r2", "ɵɵrestoreView", "_r1", "$implicit", "ɵɵtwoWayBindingSet", "value", "ɵɵresetView", "ɵɵlistener", "ClassroomStatusFiltersComponent_ng_container_0_div_2_Template_p_inputSwitch_onChange_3_listener", "ctx_r2", "ɵɵnextContext", "toggleSwitch", "label", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵtwoWayProperty", "ɵɵelementContainerStart", "ɵɵtemplate", "ClassroomStatusFiltersComponent_ng_container_0_div_2_Template", "ɵɵproperty", "switchesWithLabels", "ClassroomStatusFiltersComponent_ng_container_1_Template_p_inputSwitch_onChange_5_listener", "_r4", "ClassroomStatusFiltersComponent_ng_container_1_Template_p_inputSwitch_onChange_9_listener", "ClassroomStatusFiltersComponent_ng_container_1_Template_p_inputSwitch_onChange_13_listener", "ClassroomStatusFiltersComponent_ng_container_1_Template_p_inputSwitch_onChange_17_listener", "switches", "Ongoing", "Completed", "Expired", "All", "ClassroomStatusFiltersComponent_ng_container_2_Template_p_inputSwitch_onChange_5_listener", "_r5", "ClassroomStatusFiltersComponent_ng_container_2_Template_p_inputSwitch_onChange_9_listener", "ClassroomStatusFiltersComponent_ng_container_2_Template_p_inputSwitch_onChange_13_listener", "ClassroomStatusFiltersComponent_ng_container_2_Template_p_inputSwitch_onChange_17_listener", "Active", "Dismissed", "ClassroomStatusFiltersComponent_ng_container_3_Template_p_inputSwitch_onChange_5_listener", "_r6", "ClassroomStatusFiltersComponent_ng_container_3_Template_p_inputSwitch_onChange_9_listener", "ClassroomStatusFiltersComponent_ng_container_3_Template_p_inputSwitch_onChange_13_listener", "ClassroomStatusFiltersComponent_ng_container_3_Template_p_inputSwitch_onChange_17_listener", "ClassroomStatusFiltersComponent_ng_container_3_Template_p_inputSwitch_onChange_21_listener", "Arranged", "Canceled", "NoShow", "ClassroomStatusFiltersComponent", "constructor", "isTrial", "is<PERSON><PERSON>onsFilter", "switchToggled", "ngOnInit", "console", "log", "ngOnChanges", "switchName", "emit", "_", "_2", "selectors", "inputs", "outputs", "features", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "ClassroomStatusFiltersComponent_Template", "rf", "ctx", "ClassroomStatusFiltersComponent_ng_container_0_Template", "ClassroomStatusFiltersComponent_ng_container_1_Template", "ClassroomStatusFiltersComponent_ng_container_2_Template", "ClassroomStatusFiltersComponent_ng_container_3_Template"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\classroom\\classroom-status-filters\\classroom-status-filters.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\classroom\\classroom-status-filters\\classroom-status-filters.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-classroom-status-filters',\r\n  templateUrl: './classroom-status-filters.component.html',\r\n  styleUrls: ['./classroom-status-filters.component.scss']\r\n})\r\nexport class ClassroomStatusFiltersComponent implements OnInit {\r\n  @Input() switches: any;\r\n  @Input() switchesWithLabels!: { label: string, value: boolean }[];\r\n  @Input() isTrial = false;\r\n  @Input() isLessonsFilter = false;\r\n  @Output() switchToggled = new EventEmitter<string>();\r\n  constructor() { }\r\n\r\n  ngOnInit(): void {\r\n    console.log(this.switchesWithLabels);\r\n  }\r\n\r\n  ngOnChanges() {\r\n    console.log(this.switches);\r\n  }\r\n\r\n  toggleSwitch(switchName: string) {\r\n    this.switchToggled.emit(switchName);\r\n  }\r\n\r\n}\r\n", "<ng-container *ngIf=\"switchesWithLabels\">\r\n  <div class=\"flex flex-wrap\">\r\n    <div class=\"field-checkbox my-0\" *ngFor=\"let switch of switchesWithLabels\">\r\n      <label class=\"lesson-filter-label font-xs mr-2\">{{ switch.label }}</label>\r\n      <p-inputSwitch class=\"flex small-input-switch p-mr-2\" [(ngModel)]=\"switch.value\"\r\n        (onChange)=\"toggleSwitch(switch.label)\"></p-inputSwitch>\r\n    </div>\r\n  </div>\r\n</ng-container>\r\n\r\n<ng-container *ngIf=\"!isTrial && !isLessonsFilter && !switchesWithLabels\">\r\n<div class=\"flex flex-wrap gap-2 sm:gap-3\">\r\n  <div class=\"field-checkbox my-0\">\r\n    <label class=\"lesson-filter-label font-xs mr-2\">Ongoing</label>\r\n    <p-inputSwitch class=\"flex small-input-switch p-mr-2\" [ngModel]=\"switches.Ongoing\"\r\n      (onChange)=\"toggleSwitch('Ongoing')\"></p-inputSwitch>\r\n  </div>\r\n  <div class=\"field-checkbox my-0\">\r\n    <label class=\"lesson-filter-label font-xs mr-2\">Completed</label>\r\n    <p-inputSwitch class=\"flex small-input-switch p-mr-2\" [ngModel]=\"switches.Completed\"\r\n      (onChange)=\"toggleSwitch('Completed')\"></p-inputSwitch>\r\n  </div>\r\n  <div class=\"field-checkbox my-0\">\r\n    <label class=\"lesson-filter-label font-xs mr-2\">Expired</label>\r\n    <p-inputSwitch class=\"flex small-input-switch p-mr-2\" [ngModel]=\"switches.Expired\"\r\n      (onChange)=\"toggleSwitch('Expired')\"></p-inputSwitch>\r\n  </div>\r\n  <div class=\"field-checkbox my-0\">\r\n    <label class=\"lesson-filter-label font-xs mr-2\">All</label>\r\n    <p-inputSwitch class=\"flex small-input-switch p-mr-2\" [ngModel]=\"switches.All\"\r\n      (onChange)=\"toggleSwitch('All')\"></p-inputSwitch>\r\n  </div>\r\n  <!-- <div class=\"field-checkbox my-0\">\r\n    <label class=\"lesson-filter-label font-xs mr-2\">Freeze</label>\r\n    <p-inputSwitch class=\"flex small-input-switch p-mr-2\" [ngModel]=\"switches.Freeze\"\r\n      (onChange)=\"toggleSwitch('Freeze')\"></p-inputSwitch>\r\n  </div> -->\r\n</div>\r\n</ng-container>\r\n\r\n<ng-container *ngIf=\"isTrial\">\r\n  <div class=\"flex flex-wrap\">\r\n    <div class=\"field-checkbox my-2 sm:my-0\">\r\n      <label class=\"lesson-filter-label font-xs mr-2\">Ongoing</label>\r\n      <p-inputSwitch class=\"flex small-input-switch p-mr-2\" [ngModel]=\"switches.Active\"\r\n        (onChange)=\"toggleSwitch('Active')\"></p-inputSwitch>\r\n    </div>\r\n    <div class=\"field-checkbox my-2 sm:my-0\">\r\n      <label class=\"lesson-filter-label font-xs mr-2\">Completed</label>\r\n      <p-inputSwitch class=\"flex small-input-switch p-mr-2\" [ngModel]=\"switches.Completed\"\r\n        (onChange)=\"toggleSwitch('Completed')\"></p-inputSwitch>\r\n    </div>\r\n    <div class=\"field-checkbox my-2 sm:my-0\">\r\n      <label class=\"lesson-filter-label font-xs mr-2\">Dismissed</label>\r\n      <p-inputSwitch class=\"flex small-input-switch p-mr-2\" [ngModel]=\"switches.Dismissed\"\r\n        (onChange)=\"toggleSwitch('Dismissed')\"></p-inputSwitch>\r\n    </div>\r\n    <div class=\"field-checkbox my-2 sm:my-0\">\r\n      <label class=\"lesson-filter-label font-xs mr-2\">All</label>\r\n      <p-inputSwitch class=\"flex small-input-switch p-mr-2\" [ngModel]=\"switches.All\"\r\n        (onChange)=\"toggleSwitch('All')\"></p-inputSwitch>\r\n    </div>\r\n  </div>\r\n  </ng-container>\r\n\r\n  \r\n\r\n<ng-container *ngIf=\"isLessonsFilter\">\r\n  <div class=\"flex flex-wrap\">\r\n    <div class=\"field-checkbox my-0\">\r\n      <label class=\"lesson-filter-label font-xs mr-1\">Arranged</label>\r\n      <p-inputSwitch class=\"flex small-input-switch p-mr-2\" [ngModel]=\"switches.Arranged\"\r\n        (onChange)=\"toggleSwitch('Arranged')\"></p-inputSwitch>\r\n    </div>\r\n    <div class=\"field-checkbox my-0\">\r\n      <label class=\"lesson-filter-label font-xs mr-1\">Completed</label>\r\n      <p-inputSwitch class=\"flex small-input-switch p-mr-2\" [ngModel]=\"switches.Completed\"\r\n        (onChange)=\"toggleSwitch('Completed')\"></p-inputSwitch>\r\n    </div>\r\n    <div class=\"field-checkbox my-0\">\r\n      <label class=\"lesson-filter-label font-xs mr-1\">Canceled</label>\r\n      <p-inputSwitch class=\"flex small-input-switch p-mr-2\" [ngModel]=\"switches.Canceled\"\r\n        (onChange)=\"toggleSwitch('Canceled')\"></p-inputSwitch>\r\n    </div>\r\n    <div class=\"field-checkbox my-0\">\r\n      <label class=\"lesson-filter-label font-xs mr-1\">No Show</label>\r\n      <p-inputSwitch class=\"flex small-input-switch p-mr-2\" [ngModel]=\"switches.NoShow\"\r\n        (onChange)=\"toggleSwitch('NoShow')\"></p-inputSwitch>\r\n    </div>\r\n    <div class=\"field-checkbox my-0\">\r\n      <label class=\"lesson-filter-label font-xs mr-1\">All</label>\r\n      <p-inputSwitch class=\"flex small-input-switch p-mr-2\" [ngModel]=\"switches.All\"\r\n        (onChange)=\"toggleSwitch('All')\"></p-inputSwitch>\r\n    </div>\r\n  </div>\r\n  </ng-container>"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;;;;;;;;ICGxEC,EADF,CAAAC,cAAA,aAA2E,eACzB;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1EH,EAAA,CAAAC,cAAA,uBAC0C;IADYD,EAAA,CAAAI,gBAAA,2BAAAC,qGAAAC,MAAA;MAAA,MAAAC,SAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAJ,SAAA,CAAAK,KAAA,EAAAN,MAAA,MAAAC,SAAA,CAAAK,KAAA,GAAAN,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAA0B;IAC9EN,EAAA,CAAAc,UAAA,sBAAAC,gGAAA;MAAA,MAAAR,SAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAM,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAa,WAAA,CAAYG,MAAA,CAAAE,YAAA,CAAAX,SAAA,CAAAY,KAAA,CAA0B;IAAA,EAAC;IAC3CnB,EAD4C,CAAAG,YAAA,EAAgB,EACtD;;;;IAH4CH,EAAA,CAAAoB,SAAA,GAAkB;IAAlBpB,EAAA,CAAAqB,iBAAA,CAAAd,SAAA,CAAAY,KAAA,CAAkB;IACZnB,EAAA,CAAAoB,SAAA,EAA0B;IAA1BpB,EAAA,CAAAsB,gBAAA,YAAAf,SAAA,CAAAK,KAAA,CAA0B;;;;;IAJtFZ,EAAA,CAAAuB,uBAAA,GAAyC;IACvCvB,EAAA,CAAAC,cAAA,aAA4B;IAC1BD,EAAA,CAAAwB,UAAA,IAAAC,6DAAA,iBAA2E;IAK7EzB,EAAA,CAAAG,YAAA,EAAM;;;;;IALgDH,EAAA,CAAAoB,SAAA,GAAqB;IAArBpB,EAAA,CAAA0B,UAAA,YAAAV,MAAA,CAAAW,kBAAA,CAAqB;;;;;;IAQ7E3B,EAAA,CAAAuB,uBAAA,GAA0E;IAGtEvB,EAFJ,CAAAC,cAAA,aAA2C,aACR,eACiB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/DH,EAAA,CAAAC,cAAA,uBACuC;IAArCD,EAAA,CAAAc,UAAA,sBAAAc,0FAAA;MAAA5B,EAAA,CAAAQ,aAAA,CAAAqB,GAAA;MAAA,MAAAb,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAa,WAAA,CAAYG,MAAA,CAAAE,YAAA,CAAa,SAAS,CAAC;IAAA,EAAC;IACxClB,EADyC,CAAAG,YAAA,EAAgB,EACnD;IAEJH,EADF,CAAAC,cAAA,aAAiC,eACiB;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACjEH,EAAA,CAAAC,cAAA,uBACyC;IAAvCD,EAAA,CAAAc,UAAA,sBAAAgB,0FAAA;MAAA9B,EAAA,CAAAQ,aAAA,CAAAqB,GAAA;MAAA,MAAAb,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAa,WAAA,CAAYG,MAAA,CAAAE,YAAA,CAAa,WAAW,CAAC;IAAA,EAAC;IAC1ClB,EAD2C,CAAAG,YAAA,EAAgB,EACrD;IAEJH,EADF,CAAAC,cAAA,cAAiC,gBACiB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/DH,EAAA,CAAAC,cAAA,wBACuC;IAArCD,EAAA,CAAAc,UAAA,sBAAAiB,2FAAA;MAAA/B,EAAA,CAAAQ,aAAA,CAAAqB,GAAA;MAAA,MAAAb,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAa,WAAA,CAAYG,MAAA,CAAAE,YAAA,CAAa,SAAS,CAAC;IAAA,EAAC;IACxClB,EADyC,CAAAG,YAAA,EAAgB,EACnD;IAEJH,EADF,CAAAC,cAAA,cAAiC,gBACiB;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC3DH,EAAA,CAAAC,cAAA,wBACmC;IAAjCD,EAAA,CAAAc,UAAA,sBAAAkB,2FAAA;MAAAhC,EAAA,CAAAQ,aAAA,CAAAqB,GAAA;MAAA,MAAAb,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAa,WAAA,CAAYG,MAAA,CAAAE,YAAA,CAAa,KAAK,CAAC;IAAA,EAAC;IAOtClB,EAPuC,CAAAG,YAAA,EAAgB,EAC/C,EAMF;;;;;IAvBoDH,EAAA,CAAAoB,SAAA,GAA4B;IAA5BpB,EAAA,CAAA0B,UAAA,YAAAV,MAAA,CAAAiB,QAAA,CAAAC,OAAA,CAA4B;IAK5BlC,EAAA,CAAAoB,SAAA,GAA8B;IAA9BpB,EAAA,CAAA0B,UAAA,YAAAV,MAAA,CAAAiB,QAAA,CAAAE,SAAA,CAA8B;IAK9BnC,EAAA,CAAAoB,SAAA,GAA4B;IAA5BpB,EAAA,CAAA0B,UAAA,YAAAV,MAAA,CAAAiB,QAAA,CAAAG,OAAA,CAA4B;IAK5BpC,EAAA,CAAAoB,SAAA,GAAwB;IAAxBpB,EAAA,CAAA0B,UAAA,YAAAV,MAAA,CAAAiB,QAAA,CAAAI,GAAA,CAAwB;;;;;;IAWlFrC,EAAA,CAAAuB,uBAAA,GAA8B;IAGxBvB,EAFJ,CAAAC,cAAA,aAA4B,aACe,eACS;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/DH,EAAA,CAAAC,cAAA,uBACsC;IAApCD,EAAA,CAAAc,UAAA,sBAAAwB,0FAAA;MAAAtC,EAAA,CAAAQ,aAAA,CAAA+B,GAAA;MAAA,MAAAvB,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAa,WAAA,CAAYG,MAAA,CAAAE,YAAA,CAAa,QAAQ,CAAC;IAAA,EAAC;IACvClB,EADwC,CAAAG,YAAA,EAAgB,EAClD;IAEJH,EADF,CAAAC,cAAA,aAAyC,eACS;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACjEH,EAAA,CAAAC,cAAA,uBACyC;IAAvCD,EAAA,CAAAc,UAAA,sBAAA0B,0FAAA;MAAAxC,EAAA,CAAAQ,aAAA,CAAA+B,GAAA;MAAA,MAAAvB,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAa,WAAA,CAAYG,MAAA,CAAAE,YAAA,CAAa,WAAW,CAAC;IAAA,EAAC;IAC1ClB,EAD2C,CAAAG,YAAA,EAAgB,EACrD;IAEJH,EADF,CAAAC,cAAA,cAAyC,gBACS;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACjEH,EAAA,CAAAC,cAAA,wBACyC;IAAvCD,EAAA,CAAAc,UAAA,sBAAA2B,2FAAA;MAAAzC,EAAA,CAAAQ,aAAA,CAAA+B,GAAA;MAAA,MAAAvB,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAa,WAAA,CAAYG,MAAA,CAAAE,YAAA,CAAa,WAAW,CAAC;IAAA,EAAC;IAC1ClB,EAD2C,CAAAG,YAAA,EAAgB,EACrD;IAEJH,EADF,CAAAC,cAAA,cAAyC,gBACS;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC3DH,EAAA,CAAAC,cAAA,wBACmC;IAAjCD,EAAA,CAAAc,UAAA,sBAAA4B,2FAAA;MAAA1C,EAAA,CAAAQ,aAAA,CAAA+B,GAAA;MAAA,MAAAvB,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAa,WAAA,CAAYG,MAAA,CAAAE,YAAA,CAAa,KAAK,CAAC;IAAA,EAAC;IAEtClB,EAFuC,CAAAG,YAAA,EAAgB,EAC/C,EACF;;;;;IAlBoDH,EAAA,CAAAoB,SAAA,GAA2B;IAA3BpB,EAAA,CAAA0B,UAAA,YAAAV,MAAA,CAAAiB,QAAA,CAAAU,MAAA,CAA2B;IAK3B3C,EAAA,CAAAoB,SAAA,GAA8B;IAA9BpB,EAAA,CAAA0B,UAAA,YAAAV,MAAA,CAAAiB,QAAA,CAAAE,SAAA,CAA8B;IAK9BnC,EAAA,CAAAoB,SAAA,GAA8B;IAA9BpB,EAAA,CAAA0B,UAAA,YAAAV,MAAA,CAAAiB,QAAA,CAAAW,SAAA,CAA8B;IAK9B5C,EAAA,CAAAoB,SAAA,GAAwB;IAAxBpB,EAAA,CAAA0B,UAAA,YAAAV,MAAA,CAAAiB,QAAA,CAAAI,GAAA,CAAwB;;;;;;IAQpFrC,EAAA,CAAAuB,uBAAA,GAAsC;IAGhCvB,EAFJ,CAAAC,cAAA,aAA4B,aACO,eACiB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAChEH,EAAA,CAAAC,cAAA,uBACwC;IAAtCD,EAAA,CAAAc,UAAA,sBAAA+B,0FAAA;MAAA7C,EAAA,CAAAQ,aAAA,CAAAsC,GAAA;MAAA,MAAA9B,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAa,WAAA,CAAYG,MAAA,CAAAE,YAAA,CAAa,UAAU,CAAC;IAAA,EAAC;IACzClB,EAD0C,CAAAG,YAAA,EAAgB,EACpD;IAEJH,EADF,CAAAC,cAAA,aAAiC,eACiB;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACjEH,EAAA,CAAAC,cAAA,uBACyC;IAAvCD,EAAA,CAAAc,UAAA,sBAAAiC,0FAAA;MAAA/C,EAAA,CAAAQ,aAAA,CAAAsC,GAAA;MAAA,MAAA9B,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAa,WAAA,CAAYG,MAAA,CAAAE,YAAA,CAAa,WAAW,CAAC;IAAA,EAAC;IAC1ClB,EAD2C,CAAAG,YAAA,EAAgB,EACrD;IAEJH,EADF,CAAAC,cAAA,cAAiC,gBACiB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAChEH,EAAA,CAAAC,cAAA,wBACwC;IAAtCD,EAAA,CAAAc,UAAA,sBAAAkC,2FAAA;MAAAhD,EAAA,CAAAQ,aAAA,CAAAsC,GAAA;MAAA,MAAA9B,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAa,WAAA,CAAYG,MAAA,CAAAE,YAAA,CAAa,UAAU,CAAC;IAAA,EAAC;IACzClB,EAD0C,CAAAG,YAAA,EAAgB,EACpD;IAEJH,EADF,CAAAC,cAAA,cAAiC,gBACiB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/DH,EAAA,CAAAC,cAAA,wBACsC;IAApCD,EAAA,CAAAc,UAAA,sBAAAmC,2FAAA;MAAAjD,EAAA,CAAAQ,aAAA,CAAAsC,GAAA;MAAA,MAAA9B,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAa,WAAA,CAAYG,MAAA,CAAAE,YAAA,CAAa,QAAQ,CAAC;IAAA,EAAC;IACvClB,EADwC,CAAAG,YAAA,EAAgB,EAClD;IAEJH,EADF,CAAAC,cAAA,cAAiC,gBACiB;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC3DH,EAAA,CAAAC,cAAA,wBACmC;IAAjCD,EAAA,CAAAc,UAAA,sBAAAoC,2FAAA;MAAAlD,EAAA,CAAAQ,aAAA,CAAAsC,GAAA;MAAA,MAAA9B,MAAA,GAAAhB,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAa,WAAA,CAAYG,MAAA,CAAAE,YAAA,CAAa,KAAK,CAAC;IAAA,EAAC;IAEtClB,EAFuC,CAAAG,YAAA,EAAgB,EAC/C,EACF;;;;;IAvBoDH,EAAA,CAAAoB,SAAA,GAA6B;IAA7BpB,EAAA,CAAA0B,UAAA,YAAAV,MAAA,CAAAiB,QAAA,CAAAkB,QAAA,CAA6B;IAK7BnD,EAAA,CAAAoB,SAAA,GAA8B;IAA9BpB,EAAA,CAAA0B,UAAA,YAAAV,MAAA,CAAAiB,QAAA,CAAAE,SAAA,CAA8B;IAK9BnC,EAAA,CAAAoB,SAAA,GAA6B;IAA7BpB,EAAA,CAAA0B,UAAA,YAAAV,MAAA,CAAAiB,QAAA,CAAAmB,QAAA,CAA6B;IAK7BpD,EAAA,CAAAoB,SAAA,GAA2B;IAA3BpB,EAAA,CAAA0B,UAAA,YAAAV,MAAA,CAAAiB,QAAA,CAAAoB,MAAA,CAA2B;IAK3BrD,EAAA,CAAAoB,SAAA,GAAwB;IAAxBpB,EAAA,CAAA0B,UAAA,YAAAV,MAAA,CAAAiB,QAAA,CAAAI,GAAA,CAAwB;;;ADpFpF,OAAM,MAAOiB,+BAA+B;EAM1CC,YAAA;IAHS,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,eAAe,GAAG,KAAK;IACtB,KAAAC,aAAa,GAAG,IAAI3D,YAAY,EAAU;EACpC;EAEhB4D,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,IAAI,CAAClC,kBAAkB,CAAC;EACtC;EAEAmC,WAAWA,CAAA;IACTF,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC5B,QAAQ,CAAC;EAC5B;EAEAf,YAAYA,CAAC6C,UAAkB;IAC7B,IAAI,CAACL,aAAa,CAACM,IAAI,CAACD,UAAU,CAAC;EACrC;EAAC,QAAAE,CAAA,G;qBAlBUX,+BAA+B;EAAA;EAAA,QAAAY,EAAA,G;UAA/BZ,+BAA+B;IAAAa,SAAA;IAAAC,MAAA;MAAAnC,QAAA;MAAAN,kBAAA;MAAA6B,OAAA;MAAAC,eAAA;IAAA;IAAAY,OAAA;MAAAX,aAAA;IAAA;IAAAY,QAAA,GAAAtE,EAAA,CAAAuE,oBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,yCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC4D5C7E,EAnEA,CAAAwB,UAAA,IAAAuD,uDAAA,0BAAyC,IAAAC,uDAAA,2BAUiC,IAAAC,uDAAA,2BA8B5C,IAAAC,uDAAA,2BA2BQ;;;QAnEvBlF,EAAA,CAAA0B,UAAA,SAAAoD,GAAA,CAAAnD,kBAAA,CAAwB;QAUxB3B,EAAA,CAAAoB,SAAA,EAAyD;QAAzDpB,EAAA,CAAA0B,UAAA,UAAAoD,GAAA,CAAAtB,OAAA,KAAAsB,GAAA,CAAArB,eAAA,KAAAqB,GAAA,CAAAnD,kBAAA,CAAyD;QA8BzD3B,EAAA,CAAAoB,SAAA,EAAa;QAAbpB,EAAA,CAAA0B,UAAA,SAAAoD,GAAA,CAAAtB,OAAA,CAAa;QA2BbxD,EAAA,CAAAoB,SAAA,EAAqB;QAArBpB,EAAA,CAAA0B,UAAA,SAAAoD,GAAA,CAAArB,eAAA,CAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}