{"ast": null, "code": "// This file can be replaced during build by using the `fileReplacements` array.\n// `ng build` replaces `environment.ts` with `environment.prod.ts`.\n// The list of file replacements can be found in `angular.json`.\nexport const environment = {\n  production: false,\n  apiUrl: \"https://mlt.api.kiddobrains.com\",\n  leadDynoPrivateKey: '165d71696287208d956a45cffb6d8112b66f0e81',\n  isChatEnabled: true,\n  firebase: {\n    apiKey: \"AIzaSyAZ8-2Odth2Ws7jMTqI4q8tpnYSzpg27wg\",\n    authDomain: \"smartorders1-e5070.firebaseapp.com\",\n    projectId: \"smartorders1-e5070\",\n    storageBucket: \"smartorders1-e5070.appspot.com\",\n    messagingSenderId: \"244835475497\",\n    appId: \"1:244835475497:web:5c86fa0ef15dea2d8dbc38\",\n    measurementId: \"G-VDYKYKWY7Z\"\n  }\n};\n/*\n * For easier debugging in development mode, you can import the following file\n * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.\n *\n * This import should be commented out in production mode because it will have a negative impact\n * on performance if an error is thrown.\n */\n// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.", "map": {"version": 3, "names": ["environment", "production", "apiUrl", "leadDynoPrivateKey", "isChatEnabled", "firebase", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "measurementId"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\environments\\environment.ts"], "sourcesContent": ["// This file can be replaced during build by using the `fileReplacements` array.\r\n// `ng build` replaces `environment.ts` with `environment.prod.ts`.\r\n// The list of file replacements can be found in `angular.json`.\r\n\r\nexport const environment = {\r\n  production: false,\r\n  apiUrl: \"https://mlt.api.kiddobrains.com\",\r\n  leadDynoPrivateKey: '165d71696287208d956a45cffb6d8112b66f0e81',\r\n  isChatEnabled: true,\r\n\r\n  firebase: {\r\n    apiKey: \"AIzaSyAZ8-2Odth2Ws7jMTqI4q8tpnYSzpg27wg\",\r\n    authDomain: \"smartorders1-e5070.firebaseapp.com\",\r\n    projectId: \"smartorders1-e5070\",\r\n    storageBucket: \"smartorders1-e5070.appspot.com\",\r\n    messagingSenderId: \"244835475497\",\r\n    appId: \"1:244835475497:web:5c86fa0ef15dea2d8dbc38\",\r\n    measurementId: \"G-VDYKYKWY7Z\"\r\n  }\r\n};\r\n\r\n/*\r\n * For easier debugging in development mode, you can import the following file\r\n * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.\r\n *\r\n * This import should be commented out in production mode because it will have a negative impact\r\n * on performance if an error is thrown.\r\n */\r\n// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.\r\n"], "mappings": "AAAA;AACA;AACA;AAEA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,MAAM,EAAE,iCAAiC;EACzCC,kBAAkB,EAAE,0CAA0C;EAC9DC,aAAa,EAAE,IAAI;EAEnBC,QAAQ,EAAE;IACRC,MAAM,EAAE,yCAAyC;IACjDC,UAAU,EAAE,oCAAoC;IAChDC,SAAS,EAAE,oBAAoB;IAC/BC,aAAa,EAAE,gCAAgC;IAC/CC,iBAAiB,EAAE,cAAc;IACjCC,KAAK,EAAE,2CAA2C;IAClDC,aAAa,EAAE;;CAElB;AAED;;;;;;;AAOA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}