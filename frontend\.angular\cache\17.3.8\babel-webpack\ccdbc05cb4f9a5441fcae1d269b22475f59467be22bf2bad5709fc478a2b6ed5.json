{"ast": null, "code": "import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nimport * as i2 from \"src/app/core/services/student-trial.service\";\nimport * as i3 from \"src/app/core/services/teacher-application.service\";\nimport * as i4 from \"src/app/core/services/toast.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../../../shared/prime/input/prime-input-dropdown/prime-input-dropdown.component\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"primeng/inputtext\";\nconst _c0 = (a0, a1) => ({\n  \"ng-invalid ng-dirty my-invalid-input\": a0,\n  \"my-valid-input\": a1\n});\nexport let DashboardContactUsFormComponent = /*#__PURE__*/(() => {\n  class DashboardContactUsFormComponent {\n    constructor(generalService, studentTrialService, teacherApplicationService, toast) {\n      this.generalService = generalService;\n      this.studentTrialService = studentTrialService;\n      this.teacherApplicationService = teacherApplicationService;\n      this.toast = toast;\n      this.subs = new SubSink();\n      this.form = new UntypedFormGroup({});\n      this.studentOptions = ['Yes', 'No'];\n      this.selectedMltGroup = null;\n      this.formSubmitted = false;\n      this.selectedPhoneCode = {};\n      this.mltLanguages = [];\n      this.countries = [];\n      this.phoneCodes = this.generalService.getPhoneCodes();\n      this.languages = this.generalService.languages;\n    }\n    ngOnInit() {\n      this.countries = this.generalService.getCountries();\n      this.mltLanguages = this.generalService.mltLanguages;\n      this.subs.sink = this.teacherApplicationService.getLingoTeachingLanguages().subscribe(res => {\n        this.mltLanguages = res;\n      });\n      this.initializeForm();\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n    }\n    initializeForm() {\n      this.form = new UntypedFormGroup({\n        firstName: new UntypedFormControl(null, {\n          validators: [Validators.required]\n        }),\n        lastName: new UntypedFormControl(null, {\n          validators: [Validators.required]\n        }),\n        phoneCode: new UntypedFormControl(null, {\n          validators: [Validators.required]\n        }),\n        contactNumber: new UntypedFormControl(null, {\n          validators: [Validators.required]\n        }),\n        studentType: new UntypedFormControl(null, {\n          validators: [Validators.required]\n        }),\n        email: new UntypedFormControl(null, {\n          validators: [Validators.required, Validators.email]\n        }),\n        wantToLearnLanguage: new UntypedFormControl(null, {\n          validators: [Validators.required]\n        }),\n        message: new UntypedFormControl(null, {\n          validators: [Validators.required]\n        })\n      });\n    }\n    validate() {\n      if (this.form.invalid) {\n        for (const control of Object.keys(this.form.controls)) {\n          this.form.controls[control].markAsTouched();\n        }\n        return;\n      }\n    }\n    submit() {\n      this.validate();\n      this.formSubmitted = true;\n      if (!this.form.valid) {\n        return false;\n      }\n      fetch(\"https://ipinfo.io/json?token=adb3400371759e\").then(response => response.json()).then(jsonResponse => console.log(jsonResponse.country));\n      this.subs.sink = this.studentTrialService.postContactUs(this.prepareDataBeforeSubmit()).subscribe(res => {\n        if (res) {\n          this.showSuccessMessage();\n          this.form.reset();\n        }\n      });\n      return true;\n    }\n    onPhoneCodeChange(event) {\n      setTimeout(() => {\n        this.selectedPhoneCode = event.value;\n      }, 100);\n    }\n    findCountryImage(phoneCode) {\n      return this.generalService.findCountryImage(phoneCode);\n    }\n    isInvalidControl(controlName) {\n      const control = this.form.get(controlName);\n      return control && control.invalid && (control.touched || this.formSubmitted);\n    }\n    isValidControl(controlName) {\n      const control = this.form.get(controlName);\n      return control && control.valid;\n    }\n    isInvalid() {\n      return this.form.dirty && this.form.invalid;\n    }\n    prepareDataBeforeSubmit() {\n      const studentType = this.form.value.studentType === 'Yes' ? 'New Student' : 'Old Student';\n      const formattedData = {\n        userData: {\n          firstName: this.form.value.firstName,\n          lastName: this.form.value.lastName,\n          email: this.form.value.email,\n          phone: `${this.selectedPhoneCode.code} ${this.form.value.contactNumber}`\n        },\n        course: this.form.value.wantToLearnLanguage,\n        studentType: studentType,\n        message: this.form.value.message\n      };\n      return formattedData;\n    }\n    showSuccessMessage() {\n      this.toast.setShowToastmessage({\n        severity: 'success',\n        summary: '',\n        detail: 'Your message was successfully sent.'\n      });\n    }\n    static #_ = this.ɵfac = function DashboardContactUsFormComponent_Factory(t) {\n      return new (t || DashboardContactUsFormComponent)(i0.ɵɵdirectiveInject(i1.GeneralService), i0.ɵɵdirectiveInject(i2.StudentTrialService), i0.ɵɵdirectiveInject(i3.TeacherApplicationService), i0.ɵɵdirectiveInject(i4.ToastService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardContactUsFormComponent,\n      selectors: [[\"app-dashboard-contact-us-form\"]],\n      decls: 67,\n      vars: 42,\n      consts: [[\"contactFormWrapper\", \"\"], [1, \"contact-form-wrapper\", \"mb-5\"], [1, \"block-header\", \"justify-content-center\", \"lg:flex\"], [1, \"block-title\"], [1, \"text-0\", \"text-center\", \"font-xl\"], [1, \"surface-section\", \"px-2\", \"lg:px-4\", \"pb-5\", \"pt-3\"], [3, \"formGroup\"], [1, \"grid\"], [1, \"col-12\", \"lg:col-2\", \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"side-heading\", \"uppercase\", \"fl-typo\", \"text-center\", \"font-base\"], [1, \"col-12\", \"lg:col-10\", \"pb-1\"], [1, \"p-fluid\", \"grid\"], [1, \"field\", \"mb-0\", \"col-12\", \"md:col-6\", \"font-sm\"], [1, \"p-float-label\", \"p-input-icon-left\"], [1, \"pi\", \"icon\"], [\"type\", \"text\", \"placeholder\", \"First Name\", \"formControlName\", \"firstName\", \"pInputText\", \"\", 1, \"input-blue\", \"rounded-less\", \"white-bg\", 3, \"ngClass\"], [\"type\", \"text\", \"placeholder\", \"*Last Name\", \"formControlName\", \"lastName\", \"pInputText\", \"\", 1, \"input-blue\", \"rounded-less\", \"white-bg\", 3, \"ngClass\"], [1, \"pi\", \"icon\", \"mail-icon\"], [\"type\", \"text\", \"pInputText\", \"\", \"formControlName\", \"email\", \"placeholder\", \"Email\", 1, \"input-blue\", \"rounded-less\", \"white-bg\", 3, \"ngClass\"], [1, \"flex\", \"sm:flex\"], [\"optionLabel\", \"code\", \"filterBy\", \"code\", \"flagFilter\", \"code\", \"templateValue\", \"code\", \"styleClass\", \"dropdown-blue white-bg phone-code-input tiny rounded-less small-dropdown-items\", 3, \"valueSelected\", \"parentForm\", \"countries\", \"filter\", \"inputName\", \"placeholder\", \"isInvalidControl\", \"isValidControl\", \"withFlags\", \"selectedItemValue\"], [1, \"p-float-label\", \"w-full\"], [\"type\", \"text\", \"placeholder\", \"Phone Number\", \"required\", \"\", \"pInputText\", \"\", \"formControlName\", \"contactNumber\", 1, \"input-blue\", \"rounded-less\", \"white-bg\", \"no-radius-left\", 3, \"ngClass\"], [1, \"col-12\", \"lg:col-10\"], [1, \"field\", \"mb-0\", \"col-12\", \"md:col-6\", \"font-sm\", \"no-padding\"], [3, \"parentForm\", \"countries\", \"inputName\", \"placeholder\", \"isInvalidControl\", \"isValidControl\"], [1, \"p-float-label\"], [\"filterBy\", \"name\", 3, \"parentForm\", \"countries\", \"inputName\", \"placeholder\", \"isInvalidControl\", \"isValidControl\"], [1, \"grid\", \"relative\"], [1, \"p-fluid\", \"grid\", \"font-sm\"], [1, \"field\", \"mb-0\", \"col-12\", \"md:col-12\"], [\"formControlName\", \"message\", \"rows\", \"12\", 1, \"input-blue\", \"textarea-bg\", 3, \"ngClass\"], [1, \"\"], [1, \"fluid\", \"grid\"], [1, \"field\", \"mb-0\", \"col-12\", \"flex\", \"justify-content-center\"], [\"pRipple\", \"\", \"pbutton\", \"\", \"label\", \"Send\", \"icon\", \"pi pi-discord\", 1, \"element\", \"ripple\", \"p-button-raised\", \"p-button-rounded\", \"white-space-nowrap\", \"p-button\", \"btn-gradient-blue\", \"p-component\", \"w-10rem\", \"font-lg\", \"flex\", \"justify-content-center\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"p-button-icon\", \"p-button-icon-right\"], [\"src\", \"/assets/icons/forward-arrow.svg\", \"width\", \"24\", 1, \"ml-2\"]],\n      template: function DashboardContactUsFormComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1, 0)(2, \"div\", 2)(3, \"span\", 3)(4, \"span\", 4);\n          i0.ɵɵtext(5, \"Contact us\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(6, \"div\", 5)(7, \"form\", 6)(8, \"div\", 7)(9, \"div\", 8)(10, \"h2\", 9);\n          i0.ɵɵtext(11, \"Fill in the form\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"div\", 10)(13, \"div\", 11)(14, \"div\", 12)(15, \"label\");\n          i0.ɵɵtext(16, \"* First Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"span\", 13);\n          i0.ɵɵelement(18, \"i\", 14)(19, \"input\", 15);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 12)(21, \"label\");\n          i0.ɵɵtext(22, \"* Last Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"span\", 13);\n          i0.ɵɵelement(24, \"i\", 14)(25, \"input\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 12)(27, \"label\");\n          i0.ɵɵtext(28, \"* Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"span\", 13);\n          i0.ɵɵelement(30, \"i\", 17)(31, \"input\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 12)(33, \"label\");\n          i0.ɵɵtext(34, \"* Phone Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"span\", 19)(36, \"app-prime-input-dropdown\", 20);\n          i0.ɵɵlistener(\"valueSelected\", function DashboardContactUsFormComponent_Template_app_prime_input_dropdown_valueSelected_36_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onPhoneCodeChange($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"span\", 21);\n          i0.ɵɵelement(38, \"input\", 22);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(39, \"div\", 7);\n          i0.ɵɵelement(40, \"div\", 8);\n          i0.ɵɵelementStart(41, \"div\", 23)(42, \"div\", 11)(43, \"div\", 24)(44, \"label\");\n          i0.ɵɵtext(45, \"* I want to learn/I am learning\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(46, \"app-prime-input-dropdown\", 25);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"div\", 24)(48, \"label\");\n          i0.ɵɵtext(49, \"* Are you a new student?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"span\", 26);\n          i0.ɵɵelement(51, \"app-prime-input-dropdown\", 27);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(52, \"div\", 28);\n          i0.ɵɵelement(53, \"div\", 8);\n          i0.ɵɵelementStart(54, \"div\", 23)(55, \"div\", 29)(56, \"label\");\n          i0.ɵɵtext(57, \"* Message\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"div\", 30);\n          i0.ɵɵelement(59, \"textarea\", 31);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(60, \"div\", 32)(61, \"div\", 33)(62, \"div\", 34)(63, \"button\", 35);\n          i0.ɵɵlistener(\"click\", function DashboardContactUsFormComponent_Template_button_click_63_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.submit());\n          });\n          i0.ɵɵelement(64, \"span\", 36);\n          i0.ɵɵtext(65, \" Send \");\n          i0.ɵɵelement(66, \"img\", 37);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"formGroup\", ctx.form);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(27, _c0, ctx.isInvalidControl(\"firstName\"), ctx.isValidControl(\"firstName\")));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(30, _c0, ctx.isInvalidControl(\"lastName\"), ctx.isValidControl(\"lastName\")));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(33, _c0, ctx.isInvalidControl(\"email\"), ctx.isValidControl(\"email\")));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"parentForm\", ctx.form)(\"countries\", ctx.phoneCodes)(\"filter\", true)(\"inputName\", \"phoneCode\")(\"placeholder\", \"\")(\"isInvalidControl\", ctx.isInvalidControl(\"phoneCode\"))(\"isValidControl\", ctx.isValidControl(\"phoneCode\"))(\"withFlags\", true)(\"selectedItemValue\", ctx.selectedPhoneCode);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(36, _c0, ctx.isInvalidControl(\"contactNumber\"), ctx.isValidControl(\"contactNumber\")));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"parentForm\", ctx.form)(\"countries\", ctx.mltLanguages)(\"inputName\", \"wantToLearnLanguage\")(\"placeholder\", \"I want to learn/I am learning\")(\"isInvalidControl\", ctx.isInvalidControl(\"wantToLearnLanguage\"))(\"isValidControl\", ctx.isValidControl(\"wantToLearnLanguage\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"parentForm\", ctx.form)(\"countries\", ctx.studentOptions)(\"inputName\", \"studentType\")(\"placeholder\", \"Are you a new student?\")(\"isInvalidControl\", ctx.isInvalidControl(\"studentType\"))(\"isValidControl\", ctx.isValidControl(\"studentType\"));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(39, _c0, ctx.isInvalidControl(\"message\"), ctx.isValidControl(\"message\")));\n        }\n      },\n      dependencies: [i5.NgClass, i6.PrimeInputDropdownComponent, i7.ɵNgNoValidate, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgControlStatusGroup, i7.RequiredValidator, i7.FormGroupDirective, i7.FormControlName, i8.InputText],\n      styles: [\"@charset \\\"UTF-8\\\";.modal[_ngcontent-%COMP%]{min-width:50vw}[_nghost-%COMP%]     .p-dropdown:not(.p-disabled):hover{border:2px solid rgb(228,185,84)}  .no-label>.p-button-label{display:none}input[type=file][_ngcontent-%COMP%]{visibility:hidden;width:10px}input[type=date][_ngcontent-%COMP%]{position:relative;padding:10px}input[type=date][_ngcontent-%COMP%]::-webkit-calendar-picker-indicator{color:transparent;background:none;z-index:1}input[type=date][_ngcontent-%COMP%]:before{color:transparent;background:none;display:block;font-family:FontAwesome;content:\\\"\\\\f073\\\";width:20px;height:25px;position:absolute;top:12px;right:12px;color:#999}.image-preview[_ngcontent-%COMP%]{height:10rem;margin:1rem 0;border-radius:50%;border:3px solid var(--main-color)}.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{height:100%}[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-header{padding:clamp(.38rem,.47vw + .28rem,.75rem)}[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-items .p-dropdown-item{margin-bottom:clamp(.38rem,.47vw + .28rem,.75rem)}.country-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:clamp(.75rem,.31vw + .69rem,1rem);font-size:clamp(.88rem,.16vw + .84rem,1rem)}.country-item[_ngcontent-%COMP%]   .country-name[_ngcontent-%COMP%]{white-space:pre-line}.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;display:inline-block;vertical-align:middle}.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;clip-path:circle()}.country-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:32px;margin-right:.5rem}.country-item[_ngcontent-%COMP%]   img.smaller[_ngcontent-%COMP%]{width:16px}.info-element[_ngcontent-%COMP%]{padding:10px 0}.responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}.contact-form-wrapper[_ngcontent-%COMP%]{border-radius:16px;background-image:linear-gradient(180deg,#cedcfd,#d3e0fd 30% 51%,#d8e3fd 64%,#dde7fe 72%,#e2eafe 79% 85%,#e7eefe 90%,#ecf1fe 95% 100%);position:relative;z-index:2}.contact-form-wrapper[_ngcontent-%COMP%]   .dropdown-blue.p-dropdown.with-icon[_ngcontent-%COMP%]{padding:0!important}.contact-form-wrapper[_ngcontent-%COMP%]   .alert-box[_ngcontent-%COMP%]{padding:11px;font-stretch:normal;font-style:normal;line-height:normal;letter-spacing:.09px;text-align:left;color:var(--blue-9);background-image:linear-gradient(#fff,#e3ebfd);border-radius:10px}.contact-form-wrapper[_ngcontent-%COMP%]   .alert-box[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{opacity:.8;font-size:12px}@media (min-width: 300px) and (max-width: 1599px){.contact-form-wrapper[_ngcontent-%COMP%]   .alert-box[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:13px;font-size:calc(12px + (14 - 12) * (100vw - 300px) / (1600 - 300))!important}}@media (min-width: 1600px){.contact-form-wrapper[_ngcontent-%COMP%]   .alert-box[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{font-size:14px}}.contact-form-wrapper[_ngcontent-%COMP%]   .block-header[_ngcontent-%COMP%]{position:relative;z-index:2;min-height:3rem;box-shadow:0 3px 8px #b3b3b3f5;border-radius:16px}.contact-form-wrapper[_ngcontent-%COMP%]   .block-header[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;background-image:url(/assets/images/dashboard/freetrialheader.png);background-size:cover;background-repeat:no-repeat;background-position:center;width:100%;height:100%;left:0;top:0;z-index:1;border-radius:16px}.contact-form-wrapper[_ngcontent-%COMP%]   .surface-section[_ngcontent-%COMP%]{background-color:transparent!important}.contact-form-wrapper[_ngcontent-%COMP%]   .side-heading[_ngcontent-%COMP%]{font-weight:700;margin-bottom:0;letter-spacing:.01em;text-align:center;color:#2e3d90;opacity:.33}.contact-form-wrapper[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]{top:18px;z-index:4;height:24px;width:24px;background:url(/assets/images/dashboard/student.svg) no-repeat top left}.contact-form-wrapper[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]:before{content:\\\"\\\"}.contact-form-wrapper[_ngcontent-%COMP%]   .icon.mail-icon[_ngcontent-%COMP%]{background:url(/assets/images/dashboard/mail-blue-icon.svg) no-repeat center}.contact-form-wrapper[_ngcontent-%COMP%]   .icon.globe-icon[_ngcontent-%COMP%]{background:url(/assets/images/dashboard/globe-blue-icon.svg) no-repeat top left}.contact-form-wrapper[_ngcontent-%COMP%]   .icon.birth-icon[_ngcontent-%COMP%]{background:url(/assets/images/dashboard/birth-blue-icon.svg) no-repeat top left}.contact-form-wrapper[_ngcontent-%COMP%]   .icon.availability-icon[_ngcontent-%COMP%]{background:url(/assets/images/dashboard/availability-blue-icon.svg) no-repeat top left}.contact-form-wrapper[_ngcontent-%COMP%]   .icon.timezone-icon[_ngcontent-%COMP%]{background:url(/assets/images/dashboard/timezone-blue-icon.svg) no-repeat top left}.contact-form-wrapper[_ngcontent-%COMP%]   .textarea-bg[_ngcontent-%COMP%]{display:block;width:100%;box-sizing:border-box;background:linear-gradient(#ccd6ff,#3873f433);height:150px;border-radius:10px;resize:none}.contact-form-wrapper[_ngcontent-%COMP%]   .textarea-bg[_ngcontent-%COMP%]::placeholder{opacity:.5}.contact-form-wrapper[_ngcontent-%COMP%]   .info-bubble-blue-bg[_ngcontent-%COMP%]{background-image:url(/assets/images/dashboard/trial-form-bubble-blue.svg);background-size:100%;background-repeat:no-repeat;height:185px;width:216px}.contact-form-wrapper[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{position:absolute;bottom:-1.1rem}\"]\n    });\n  }\n  return DashboardContactUsFormComponent;\n})();", "map": {"version": 3, "names": ["UntypedFormControl", "UntypedFormGroup", "Validators", "SubSink", "DashboardContactUsFormComponent", "constructor", "generalService", "studentTrialService", "teacherApplicationService", "toast", "subs", "form", "studentOptions", "selectedMltGroup", "formSubmitted", "selectedPhoneCode", "mltLanguages", "countries", "phoneCodes", "getPhoneCodes", "languages", "ngOnInit", "getCountries", "sink", "getLingoTeachingLanguages", "subscribe", "res", "initializeForm", "ngOnDestroy", "unsubscribe", "firstName", "validators", "required", "lastName", "phoneCode", "contactNumber", "studentType", "email", "wantToLearnLanguage", "message", "validate", "invalid", "control", "Object", "keys", "controls", "<PERSON><PERSON><PERSON><PERSON>ched", "submit", "valid", "fetch", "then", "response", "json", "jsonResponse", "console", "log", "country", "postContactUs", "prepareDataBeforeSubmit", "showSuccessMessage", "reset", "onPhoneCodeChange", "event", "setTimeout", "value", "findCountryImage", "isInvalidControl", "controlName", "get", "touched", "isValidControl", "isInvalid", "dirty", "formattedData", "userData", "phone", "code", "course", "setShowToastmessage", "severity", "summary", "detail", "_", "i0", "ɵɵdirectiveInject", "i1", "GeneralService", "i2", "StudentTrialService", "i3", "TeacherApplicationService", "i4", "ToastService", "_2", "selectors", "decls", "vars", "consts", "template", "DashboardContactUsFormComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "DashboardContactUsFormComponent_Template_app_prime_input_dropdown_valueSelected_36_listener", "$event", "ɵɵrestoreView", "_r1", "ɵɵresetView", "DashboardContactUsFormComponent_Template_button_click_63_listener", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction2", "_c0"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\dashboard\\dashboard-contact-us-form\\dashboard-contact-us-form.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\dashboard\\dashboard-contact-us-form\\dashboard-contact-us-form.component.html"], "sourcesContent": ["import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';\r\nimport { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\r\nimport { Country, Language, Timezone } from 'src/app/core/models/general.model';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { StudentTrialService } from 'src/app/core/services/student-trial.service';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { TeacherApplicationService } from 'src/app/core/services/teacher-application.service';\r\nimport { SubSink } from 'subsink';\r\n\r\n@Component({\r\n  selector: 'app-dashboard-contact-us-form',\r\n  templateUrl: './dashboard-contact-us-form.component.html',\r\n  styleUrls: ['./dashboard-contact-us-form.component.scss']\r\n})\r\nexport class DashboardContactUsFormComponent implements OnInit {\r\n  private subs = new SubSink();\r\n  public form: UntypedFormGroup = new UntypedFormGroup({});\r\n\r\n  studentOptions = ['Yes', 'No'];\r\n\r\n  selectedMltGroup = null;\r\n  formSubmitted = false;\r\n  public selectedTimezone?: Timezone;\r\n  public selectedLanguage?: Language;\r\n  public selectedPhoneCode: any | undefined = {} as any | undefined;\r\n  mltLanguages: Language[] = [] as Language[];\r\n  countries: Country[] = [] as Country[];\r\n  public phoneCodes: any[] = this.generalService.getPhoneCodes();\r\n  public languages: Language[] = this.generalService.languages;\r\n\r\n  constructor(\r\n    private generalService: GeneralService,\r\n    private studentTrialService: StudentTrialService,\r\n    private teacherApplicationService: TeacherApplicationService,\r\n    private toast: ToastService,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.countries = this.generalService.getCountries();\r\n    this.mltLanguages = this.generalService.mltLanguages;\r\n\r\n    this.subs.sink = this.teacherApplicationService.getLingoTeachingLanguages().subscribe((res)=>{\r\n      this.mltLanguages = res;\r\n    });\r\n    this.initializeForm();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  initializeForm() {\r\n    this.form = new UntypedFormGroup({\r\n      firstName: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      lastName: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      phoneCode: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      contactNumber: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      studentType: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      email: new UntypedFormControl(null, {\r\n        validators:  [\r\n          Validators.required,\r\n          Validators.email\r\n        ]\r\n      }),\r\n      wantToLearnLanguage: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      message: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n    });\r\n\r\n  }\r\n  private validate(): void {\r\n    if (this.form.invalid) {\r\n      for (const control of Object.keys(this.form.controls)) {\r\n        this.form.controls[control].markAsTouched();\r\n      }\r\n      return;\r\n    }\r\n\r\n  }\r\n  submit() {\r\n    this.validate();\r\n    this.formSubmitted = true;\r\n    if (!this.form.valid) {\r\n        return false;\r\n    }\r\n    fetch(\"https://ipinfo.io/json?token=adb3400371759e\").then(\r\n      (response) => response.json()\r\n    ).then(\r\n      (jsonResponse) => console.log(jsonResponse.country)\r\n    )\r\n      \r\n    this.subs.sink = this.studentTrialService.postContactUs(this.prepareDataBeforeSubmit()).subscribe((res) => {\r\n      if (res) {\r\n        this.showSuccessMessage();\r\n        this.form.reset();\r\n      }\r\n    });\r\n    return true;\r\n  }\r\n\r\n  onPhoneCodeChange(event: any) {\r\n    setTimeout(() => {\r\n      this.selectedPhoneCode = event.value;\r\n    }, 100);\r\n  }\r\n\r\n  findCountryImage(phoneCode: string) {\r\n    return this.generalService.findCountryImage(phoneCode);\r\n  }\r\n\r\n  isInvalidControl(controlName: string): boolean {\r\n    const control = this.form.get(controlName);\r\n    return control! && (control!.invalid && (control!.touched || this.formSubmitted));\r\n  }\r\n\r\n  isValidControl(controlName: string): boolean {\r\n    const control = this.form.get(controlName);\r\n    return control! && control.valid;\r\n  }\r\n\r\n  isInvalid() {\r\n    return this.form.dirty && this.form.invalid;\r\n  }\r\n\r\n  private prepareDataBeforeSubmit() {\r\n    const studentType = this.form.value.studentType === 'Yes' ? 'New Student' : 'Old Student';\r\n    const formattedData = {\r\n      userData: {\r\n        firstName: this.form.value.firstName,\r\n        lastName: this.form.value.lastName,\r\n        email: this.form.value.email,\r\n        phone: `${this.selectedPhoneCode.code} ${this.form.value.contactNumber}`\r\n      },\r\n      course: this.form.value.wantToLearnLanguage,\r\n      studentType: studentType,\r\n      message: this.form.value.message\r\n    }\r\n    return formattedData;\r\n  }\r\n\r\n  showSuccessMessage() {\r\n    this.toast.setShowToastmessage({\r\n      severity: 'success',\r\n      summary: '',\r\n      detail: 'Your message was successfully sent.'\r\n    });\r\n  }\r\n}\r\n", "<div class=\"contact-form-wrapper mb-5\" #contactFormWrapper>\r\n        <div class=\"block-header justify-content-center  lg:flex\">\r\n            <span class=\"block-title\">\r\n                <span class=\"text-0 text-center font-xl\">Contact us</span>\r\n            </span>\r\n        </div>\r\n        <div class=\"surface-section px-2 lg:px-4 pb-5 pt-3\">\r\n            <form [formGroup]=\"form\">\r\n                <div class=\"grid\">\r\n                    <div class=\"col-12 lg:col-2 flex align-items-center justify-content-center\">\r\n                        <h2 class=\"side-heading uppercase fl-typo text-center font-base\">Fill in the form</h2>                       \r\n                    </div>\r\n                    <div class=\"col-12 lg:col-10 pb-1\">\r\n                        <div class=\"p-fluid grid\">\r\n                            <div class=\"field mb-0 col-12 md:col-6 font-sm\">\r\n                                <label>* First Name</label>\r\n                                <span class=\"p-float-label p-input-icon-left\">\r\n                                    <i class=\"pi icon\"></i>\r\n                                    <input type=\"text\" class=\"input-blue rounded-less white-bg\"\r\n                                    placeholder=\"First Name\"\r\n                                    [ngClass]=\"{ 'ng-invalid ng-dirty my-invalid-input': isInvalidControl('firstName'), 'my-valid-input': isValidControl('firstName') }\"\r\n                                        formControlName=\"firstName\" pInputText>\r\n                                </span>\r\n                            </div>\r\n                            <div class=\"field mb-0 col-12 md:col-6 font-sm\">\r\n                                <label>* Last Name</label>\r\n                                <span class=\"p-float-label p-input-icon-left\">\r\n                                    <i class=\"pi icon\"></i>\r\n                                    <input type=\"text\" class=\"input-blue rounded-less white-bg\"\r\n                                    placeholder=\"*Last Name\"\r\n                                    [ngClass]=\"{ 'ng-invalid ng-dirty my-invalid-input': isInvalidControl('lastName'), 'my-valid-input': isValidControl('lastName') }\"\r\n                                        formControlName=\"lastName\" pInputText>\r\n                                </span>\r\n                            </div>\r\n\r\n\r\n                            <div class=\"field mb-0 col-12 md:col-6 font-sm\">\r\n                                <label>* Email</label>\r\n                                <span class=\"p-float-label p-input-icon-left\">\r\n                                    <i class=\"pi icon mail-icon\"></i>\r\n                                    <input type=\"text\" class=\"input-blue rounded-less white-bg\" pInputText\r\n                                        formControlName=\"email\"\r\n                                        placeholder=\"Email\"\r\n                                        [ngClass]=\"{ 'ng-invalid ng-dirty my-invalid-input': isInvalidControl('email'), 'my-valid-input': isValidControl('email') }\">\r\n                                </span>\r\n                            </div>\r\n\r\n                            <div class=\"field mb-0 col-12 md:col-6 font-sm\">\r\n                                <label>* Phone Number</label>\r\n                                <span class=\" flex sm:flex\">\r\n                                    <app-prime-input-dropdown \r\n                                    optionLabel=\"code\"\r\n                                    [parentForm]=\"form\"\r\n                                    [countries]=\"phoneCodes\"\r\n                                    filterBy=\"code\"\r\n                                    [filter]=\"true\"\r\n                                    [inputName]=\"'phoneCode'\"\r\n                                    [placeholder]=\"''\"\r\n                                    [isInvalidControl]=\"isInvalidControl('phoneCode')\"\r\n                                    [isValidControl]=\"isValidControl('phoneCode')\"\r\n                                    [withFlags]=\"true\"\r\n                                    flagFilter=\"code\"\r\n                                    [selectedItemValue]=\"selectedPhoneCode\"\r\n                                    templateValue=\"code\"\r\n                                    styleClass=\"dropdown-blue white-bg phone-code-input tiny rounded-less small-dropdown-items\"\r\n                                    (valueSelected)=\"onPhoneCodeChange($event)\">\r\n                                    </app-prime-input-dropdown>\r\n                                <span class=\"p-float-label w-full\">\r\n                                    <!-- <i class=\"pi icon\"></i> -->\r\n                                    <input type=\"text\" class=\"input-blue rounded-less white-bg no-radius-left\"\r\n                                    placeholder=\"Phone Number\"\r\n                                        [ngClass]=\"{ 'ng-invalid ng-dirty my-invalid-input': isInvalidControl('contactNumber'), 'my-valid-input': isValidControl('contactNumber') }\"\r\n                                        required pInputText formControlName=\"contactNumber\">\r\n                                    <!-- <label for=\"inputtext-right\">Phone</label> -->\r\n                                </span>\r\n                                </span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"grid\">\r\n                    <div class=\"col-12 lg:col-2 flex align-items-center justify-content-center\">\r\n                    </div>\r\n                    <div class=\"col-12 lg:col-10\">\r\n                        <div class=\"p-fluid grid\">\r\n                            <div class=\"field mb-0 col-12 md:col-6 font-sm no-padding\">\r\n                                <label>* I want to learn/I am learning</label>\r\n                                    <app-prime-input-dropdown\r\n                                    [parentForm]=\"form\"\r\n                                    [countries]=\"mltLanguages\"\r\n                                    [inputName]=\"'wantToLearnLanguage'\"\r\n                                    [placeholder]=\"'I want to learn/I am learning'\"\r\n                                    [isInvalidControl]=\"isInvalidControl('wantToLearnLanguage')\"\r\n                                    [isValidControl]=\"isValidControl('wantToLearnLanguage')\">\r\n                                    </app-prime-input-dropdown>\r\n                            </div>\r\n                            <div class=\"field mb-0 col-12 md:col-6 font-sm no-padding\">\r\n                                <label>* Are you a new student?</label>\r\n                                <span class=\"p-float-label\">\r\n                                    <app-prime-input-dropdown \r\n                                    [parentForm]=\"form\"\r\n                                    [countries]=\"studentOptions\"\r\n                                    filterBy=\"name\"\r\n                                    [inputName]=\"'studentType'\"\r\n                                    [placeholder]=\"'Are you a new student?'\"\r\n                                    [isInvalidControl]=\"isInvalidControl('studentType')\"\r\n                                    [isValidControl]=\"isValidControl('studentType')\">\r\n                                    </app-prime-input-dropdown>\r\n                                </span>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"grid relative\">\r\n                    <div class=\"col-12 lg:col-2 flex align-items-center justify-content-center\"></div>\r\n                    <div class=\"col-12 lg:col-10\">\r\n                        <div class=\"p-fluid grid font-sm\">\r\n                            <label>* Message</label>\r\n                            <div class=\"field mb-0 col-12 md:col-12\">\r\n                                <textarea class=\"input-blue textarea-bg\" formControlName=\"message\" rows=\"12\"\r\n                                [ngClass]=\"{ 'ng-invalid ng-dirty my-invalid-input': isInvalidControl('message'), 'my-valid-input': isValidControl('message') }\"\r\n                                ></textarea>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                </div>\r\n            </form>\r\n        </div>\r\n        <div class=\"\">\r\n            <div class=\"fluid grid\">\r\n                <div class=\"field mb-0 col-12 flex justify-content-center\">\r\n                    <button (click)=\"submit()\" pRipple pbutton label=\"Send\" icon=\"pi pi-discord\"\r\n                        class=\"element ripple p-button-raised p-button-rounded white-space-nowrap p-button btn-gradient-blue p-component w-10rem font-lg flex justify-content-center\">\r\n                        <span class=\"p-button-icon p-button-icon-right\" aria-hidden=\"true\"></span>\r\n                        Send \r\n                        <img src=\"/assets/icons/forward-arrow.svg\" width=\"24\" class=\"ml-2\"/>\r\n                    </button>\r\n                </div>\r\n            </div>\r\n        </div>\r\n</div>"], "mappings": "AACA,SAASA,kBAAkB,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,gBAAgB;AAMjF,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;;;AAOjC,WAAaC,+BAA+B;EAAtC,MAAOA,+BAA+B;IAgB1CC,YACUC,cAA8B,EAC9BC,mBAAwC,EACxCC,yBAAoD,EACpDC,KAAmB;MAHnB,KAAAH,cAAc,GAAdA,cAAc;MACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;MACnB,KAAAC,yBAAyB,GAAzBA,yBAAyB;MACzB,KAAAC,KAAK,GAALA,KAAK;MAnBP,KAAAC,IAAI,GAAG,IAAIP,OAAO,EAAE;MACrB,KAAAQ,IAAI,GAAqB,IAAIV,gBAAgB,CAAC,EAAE,CAAC;MAExD,KAAAW,cAAc,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC;MAE9B,KAAAC,gBAAgB,GAAG,IAAI;MACvB,KAAAC,aAAa,GAAG,KAAK;MAGd,KAAAC,iBAAiB,GAAoB,EAAqB;MACjE,KAAAC,YAAY,GAAe,EAAgB;MAC3C,KAAAC,SAAS,GAAc,EAAe;MAC/B,KAAAC,UAAU,GAAU,IAAI,CAACZ,cAAc,CAACa,aAAa,EAAE;MACvD,KAAAC,SAAS,GAAe,IAAI,CAACd,cAAc,CAACc,SAAS;IAOxD;IAEJC,QAAQA,CAAA;MACN,IAAI,CAACJ,SAAS,GAAG,IAAI,CAACX,cAAc,CAACgB,YAAY,EAAE;MACnD,IAAI,CAACN,YAAY,GAAG,IAAI,CAACV,cAAc,CAACU,YAAY;MAEpD,IAAI,CAACN,IAAI,CAACa,IAAI,GAAG,IAAI,CAACf,yBAAyB,CAACgB,yBAAyB,EAAE,CAACC,SAAS,CAAEC,GAAG,IAAG;QAC3F,IAAI,CAACV,YAAY,GAAGU,GAAG;MACzB,CAAC,CAAC;MACF,IAAI,CAACC,cAAc,EAAE;IACvB;IAEAC,WAAWA,CAAA;MACT,IAAI,CAAClB,IAAI,CAACmB,WAAW,EAAE;IACzB;IAEAF,cAAcA,CAAA;MACZ,IAAI,CAAChB,IAAI,GAAG,IAAIV,gBAAgB,CAAC;QAC/B6B,SAAS,EAAE,IAAI9B,kBAAkB,CAAC,IAAI,EAAE;UACtC+B,UAAU,EAAE,CAAC7B,UAAU,CAAC8B,QAAQ;SACjC,CAAC;QACFC,QAAQ,EAAE,IAAIjC,kBAAkB,CAAC,IAAI,EAAE;UACrC+B,UAAU,EAAE,CAAC7B,UAAU,CAAC8B,QAAQ;SACjC,CAAC;QACFE,SAAS,EAAE,IAAIlC,kBAAkB,CAAC,IAAI,EAAE;UACtC+B,UAAU,EAAE,CAAC7B,UAAU,CAAC8B,QAAQ;SACjC,CAAC;QACFG,aAAa,EAAE,IAAInC,kBAAkB,CAAC,IAAI,EAAE;UAC1C+B,UAAU,EAAE,CAAC7B,UAAU,CAAC8B,QAAQ;SACjC,CAAC;QACFI,WAAW,EAAE,IAAIpC,kBAAkB,CAAC,IAAI,EAAE;UACxC+B,UAAU,EAAE,CAAC7B,UAAU,CAAC8B,QAAQ;SACjC,CAAC;QACFK,KAAK,EAAE,IAAIrC,kBAAkB,CAAC,IAAI,EAAE;UAClC+B,UAAU,EAAG,CACX7B,UAAU,CAAC8B,QAAQ,EACnB9B,UAAU,CAACmC,KAAK;SAEnB,CAAC;QACFC,mBAAmB,EAAE,IAAItC,kBAAkB,CAAC,IAAI,EAAE;UAChD+B,UAAU,EAAE,CAAC7B,UAAU,CAAC8B,QAAQ;SACjC,CAAC;QACFO,OAAO,EAAE,IAAIvC,kBAAkB,CAAC,IAAI,EAAE;UACpC+B,UAAU,EAAE,CAAC7B,UAAU,CAAC8B,QAAQ;SACjC;OACF,CAAC;IAEJ;IACQQ,QAAQA,CAAA;MACd,IAAI,IAAI,CAAC7B,IAAI,CAAC8B,OAAO,EAAE;QACrB,KAAK,MAAMC,OAAO,IAAIC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjC,IAAI,CAACkC,QAAQ,CAAC,EAAE;UACrD,IAAI,CAAClC,IAAI,CAACkC,QAAQ,CAACH,OAAO,CAAC,CAACI,aAAa,EAAE;QAC7C;QACA;MACF;IAEF;IACAC,MAAMA,CAAA;MACJ,IAAI,CAACP,QAAQ,EAAE;MACf,IAAI,CAAC1B,aAAa,GAAG,IAAI;MACzB,IAAI,CAAC,IAAI,CAACH,IAAI,CAACqC,KAAK,EAAE;QAClB,OAAO,KAAK;MAChB;MACAC,KAAK,CAAC,6CAA6C,CAAC,CAACC,IAAI,CACtDC,QAAQ,IAAKA,QAAQ,CAACC,IAAI,EAAE,CAC9B,CAACF,IAAI,CACHG,YAAY,IAAKC,OAAO,CAACC,GAAG,CAACF,YAAY,CAACG,OAAO,CAAC,CACpD;MAED,IAAI,CAAC9C,IAAI,CAACa,IAAI,GAAG,IAAI,CAAChB,mBAAmB,CAACkD,aAAa,CAAC,IAAI,CAACC,uBAAuB,EAAE,CAAC,CAACjC,SAAS,CAAEC,GAAG,IAAI;QACxG,IAAIA,GAAG,EAAE;UACP,IAAI,CAACiC,kBAAkB,EAAE;UACzB,IAAI,CAAChD,IAAI,CAACiD,KAAK,EAAE;QACnB;MACF,CAAC,CAAC;MACF,OAAO,IAAI;IACb;IAEAC,iBAAiBA,CAACC,KAAU;MAC1BC,UAAU,CAAC,MAAK;QACd,IAAI,CAAChD,iBAAiB,GAAG+C,KAAK,CAACE,KAAK;MACtC,CAAC,EAAE,GAAG,CAAC;IACT;IAEAC,gBAAgBA,CAAC/B,SAAiB;MAChC,OAAO,IAAI,CAAC5B,cAAc,CAAC2D,gBAAgB,CAAC/B,SAAS,CAAC;IACxD;IAEAgC,gBAAgBA,CAACC,WAAmB;MAClC,MAAMzB,OAAO,GAAG,IAAI,CAAC/B,IAAI,CAACyD,GAAG,CAACD,WAAW,CAAC;MAC1C,OAAOzB,OAAQ,IAAKA,OAAQ,CAACD,OAAO,KAAKC,OAAQ,CAAC2B,OAAO,IAAI,IAAI,CAACvD,aAAa,CAAE;IACnF;IAEAwD,cAAcA,CAACH,WAAmB;MAChC,MAAMzB,OAAO,GAAG,IAAI,CAAC/B,IAAI,CAACyD,GAAG,CAACD,WAAW,CAAC;MAC1C,OAAOzB,OAAQ,IAAIA,OAAO,CAACM,KAAK;IAClC;IAEAuB,SAASA,CAAA;MACP,OAAO,IAAI,CAAC5D,IAAI,CAAC6D,KAAK,IAAI,IAAI,CAAC7D,IAAI,CAAC8B,OAAO;IAC7C;IAEQiB,uBAAuBA,CAAA;MAC7B,MAAMtB,WAAW,GAAG,IAAI,CAACzB,IAAI,CAACqD,KAAK,CAAC5B,WAAW,KAAK,KAAK,GAAG,aAAa,GAAG,aAAa;MACzF,MAAMqC,aAAa,GAAG;QACpBC,QAAQ,EAAE;UACR5C,SAAS,EAAE,IAAI,CAACnB,IAAI,CAACqD,KAAK,CAAClC,SAAS;UACpCG,QAAQ,EAAE,IAAI,CAACtB,IAAI,CAACqD,KAAK,CAAC/B,QAAQ;UAClCI,KAAK,EAAE,IAAI,CAAC1B,IAAI,CAACqD,KAAK,CAAC3B,KAAK;UAC5BsC,KAAK,EAAE,GAAG,IAAI,CAAC5D,iBAAiB,CAAC6D,IAAI,IAAI,IAAI,CAACjE,IAAI,CAACqD,KAAK,CAAC7B,aAAa;SACvE;QACD0C,MAAM,EAAE,IAAI,CAAClE,IAAI,CAACqD,KAAK,CAAC1B,mBAAmB;QAC3CF,WAAW,EAAEA,WAAW;QACxBG,OAAO,EAAE,IAAI,CAAC5B,IAAI,CAACqD,KAAK,CAACzB;OAC1B;MACD,OAAOkC,aAAa;IACtB;IAEAd,kBAAkBA,CAAA;MAChB,IAAI,CAAClD,KAAK,CAACqE,mBAAmB,CAAC;QAC7BC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;IACJ;IAAC,QAAAC,CAAA,G;uBAjJU9E,+BAA+B,EAAA+E,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,mBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,yBAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,YAAA;IAAA;IAAA,QAAAC,EAAA,G;YAA/BzF,+BAA+B;MAAA0F,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCX5BjB,EAHhB,CAAAmB,cAAA,gBAA2D,aACO,cAC5B,cACmB;UAAAnB,EAAA,CAAAoB,MAAA,iBAAU;UAE3DpB,EAF2D,CAAAqB,YAAA,EAAO,EACvD,EACL;UAKUrB,EAJhB,CAAAmB,cAAA,aAAoD,cACvB,aACH,aAC8D,aACP;UAAAnB,EAAA,CAAAoB,MAAA,wBAAgB;UACrFpB,EADqF,CAAAqB,YAAA,EAAK,EACpF;UAIMrB,EAHZ,CAAAmB,cAAA,eAAmC,eACL,eAC0B,aACrC;UAAAnB,EAAA,CAAAoB,MAAA,oBAAY;UAAApB,EAAA,CAAAqB,YAAA,EAAQ;UAC3BrB,EAAA,CAAAmB,cAAA,gBAA8C;UAE1CnB,EADA,CAAAsB,SAAA,aAAuB,iBAIoB;UAEnDtB,EADI,CAAAqB,YAAA,EAAO,EACL;UAEFrB,EADJ,CAAAmB,cAAA,eAAgD,aACrC;UAAAnB,EAAA,CAAAoB,MAAA,mBAAW;UAAApB,EAAA,CAAAqB,YAAA,EAAQ;UAC1BrB,EAAA,CAAAmB,cAAA,gBAA8C;UAE1CnB,EADA,CAAAsB,SAAA,aAAuB,iBAImB;UAElDtB,EADI,CAAAqB,YAAA,EAAO,EACL;UAIFrB,EADJ,CAAAmB,cAAA,eAAgD,aACrC;UAAAnB,EAAA,CAAAoB,MAAA,eAAO;UAAApB,EAAA,CAAAqB,YAAA,EAAQ;UACtBrB,EAAA,CAAAmB,cAAA,gBAA8C;UAE1CnB,EADA,CAAAsB,SAAA,aAAiC,iBAIgG;UAEzItB,EADI,CAAAqB,YAAA,EAAO,EACL;UAGFrB,EADJ,CAAAmB,cAAA,eAAgD,aACrC;UAAAnB,EAAA,CAAAoB,MAAA,sBAAc;UAAApB,EAAA,CAAAqB,YAAA,EAAQ;UAEzBrB,EADJ,CAAAmB,cAAA,gBAA4B,oCAgBoB;UAA5CnB,EAAA,CAAAuB,UAAA,2BAAAC,4FAAAC,MAAA;YAAAzB,EAAA,CAAA0B,aAAA,CAAAC,GAAA;YAAA,OAAA3B,EAAA,CAAA4B,WAAA,CAAiBV,GAAA,CAAAxC,iBAAA,CAAA+C,MAAA,CAAyB;UAAA,EAAC;UAC3CzB,EAAA,CAAAqB,YAAA,EAA2B;UAC/BrB,EAAA,CAAAmB,cAAA,gBAAmC;UAE/BnB,EAAA,CAAAsB,SAAA,iBAGwD;UAO5EtB,EALgB,CAAAqB,YAAA,EAAO,EACA,EACL,EACJ,EACJ,EACJ;UACNrB,EAAA,CAAAmB,cAAA,cAAkB;UACdnB,EAAA,CAAAsB,SAAA,cACM;UAIMtB,EAHZ,CAAAmB,cAAA,eAA8B,eACA,eACqC,aAChD;UAAAnB,EAAA,CAAAoB,MAAA,uCAA+B;UAAApB,EAAA,CAAAqB,YAAA,EAAQ;UAC1CrB,EAAA,CAAAsB,SAAA,oCAO2B;UACnCtB,EAAA,CAAAqB,YAAA,EAAM;UAEFrB,EADJ,CAAAmB,cAAA,eAA2D,aAChD;UAAAnB,EAAA,CAAAoB,MAAA,gCAAwB;UAAApB,EAAA,CAAAqB,YAAA,EAAQ;UACvCrB,EAAA,CAAAmB,cAAA,gBAA4B;UACxBnB,EAAA,CAAAsB,SAAA,oCAQ2B;UAK/CtB,EAJgB,CAAAqB,YAAA,EAAO,EACL,EACJ,EACJ,EACJ;UAENrB,EAAA,CAAAmB,cAAA,eAA2B;UACvBnB,EAAA,CAAAsB,SAAA,cAAkF;UAG1EtB,EAFR,CAAAmB,cAAA,eAA8B,eACQ,aACvB;UAAAnB,EAAA,CAAAoB,MAAA,iBAAS;UAAApB,EAAA,CAAAqB,YAAA,EAAQ;UACxBrB,EAAA,CAAAmB,cAAA,eAAyC;UACrCnB,EAAA,CAAAsB,SAAA,oBAEY;UAOpCtB,EANoB,CAAAqB,YAAA,EAAM,EACJ,EACJ,EAEJ,EACH,EACL;UAIMrB,EAHZ,CAAAmB,cAAA,eAAc,eACc,eACuC,kBAE2G;UAD1JnB,EAAA,CAAAuB,UAAA,mBAAAM,kEAAA;YAAA7B,EAAA,CAAA0B,aAAA,CAAAC,GAAA;YAAA,OAAA3B,EAAA,CAAA4B,WAAA,CAASV,GAAA,CAAAtD,MAAA,EAAQ;UAAA,EAAC;UAEtBoC,EAAA,CAAAsB,SAAA,gBAA0E;UAC1EtB,EAAA,CAAAoB,MAAA,cACA;UAAApB,EAAA,CAAAsB,SAAA,eAAoE;UAK5FtB,EAJoB,CAAAqB,YAAA,EAAS,EACP,EACJ,EACJ,EACR;;;UAvIYrB,EAAA,CAAA8B,SAAA,GAAkB;UAAlB9B,EAAA,CAAA+B,UAAA,cAAAb,GAAA,CAAA1F,IAAA,CAAkB;UAaAwE,EAAA,CAAA8B,SAAA,IAAoI;UAApI9B,EAAA,CAAA+B,UAAA,YAAA/B,EAAA,CAAAgC,eAAA,KAAAC,GAAA,EAAAf,GAAA,CAAAnC,gBAAA,eAAAmC,GAAA,CAAA/B,cAAA,eAAoI;UAUpIa,EAAA,CAAA8B,SAAA,GAAkI;UAAlI9B,EAAA,CAAA+B,UAAA,YAAA/B,EAAA,CAAAgC,eAAA,KAAAC,GAAA,EAAAf,GAAA,CAAAnC,gBAAA,cAAAmC,GAAA,CAAA/B,cAAA,cAAkI;UAa9Ha,EAAA,CAAA8B,SAAA,GAA4H;UAA5H9B,EAAA,CAAA+B,UAAA,YAAA/B,EAAA,CAAAgC,eAAA,KAAAC,GAAA,EAAAf,GAAA,CAAAnC,gBAAA,WAAAmC,GAAA,CAAA/B,cAAA,WAA4H;UAShIa,EAAA,CAAA8B,SAAA,GAAmB;UAUnB9B,EAVA,CAAA+B,UAAA,eAAAb,GAAA,CAAA1F,IAAA,CAAmB,cAAA0F,GAAA,CAAAnF,UAAA,CACK,gBAET,0BACU,mBACP,qBAAAmF,GAAA,CAAAnC,gBAAA,cACgC,mBAAAmC,GAAA,CAAA/B,cAAA,cACJ,mBAC5B,sBAAA+B,GAAA,CAAAtF,iBAAA,CAEqB;UASnCoE,EAAA,CAAA8B,SAAA,GAA4I;UAA5I9B,EAAA,CAAA+B,UAAA,YAAA/B,EAAA,CAAAgC,eAAA,KAAAC,GAAA,EAAAf,GAAA,CAAAnC,gBAAA,mBAAAmC,GAAA,CAAA/B,cAAA,mBAA4I;UAiBhJa,EAAA,CAAA8B,SAAA,GAAmB;UAKnB9B,EALA,CAAA+B,UAAA,eAAAb,GAAA,CAAA1F,IAAA,CAAmB,cAAA0F,GAAA,CAAArF,YAAA,CACO,oCACS,gDACY,qBAAAqF,GAAA,CAAAnC,gBAAA,wBACa,mBAAAmC,GAAA,CAAA/B,cAAA,wBACJ;UAOxDa,EAAA,CAAA8B,SAAA,GAAmB;UAMnB9B,EANA,CAAA+B,UAAA,eAAAb,GAAA,CAAA1F,IAAA,CAAmB,cAAA0F,GAAA,CAAAzF,cAAA,CACS,4BAED,yCACa,qBAAAyF,GAAA,CAAAnC,gBAAA,gBACY,mBAAAmC,GAAA,CAAA/B,cAAA,gBACJ;UAepDa,EAAA,CAAA8B,SAAA,GAAgI;UAAhI9B,EAAA,CAAA+B,UAAA,YAAA/B,EAAA,CAAAgC,eAAA,KAAAC,GAAA,EAAAf,GAAA,CAAAnC,gBAAA,aAAAmC,GAAA,CAAA/B,cAAA,aAAgI;;;;;;;SD3GnJlE,+BAA+B;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}