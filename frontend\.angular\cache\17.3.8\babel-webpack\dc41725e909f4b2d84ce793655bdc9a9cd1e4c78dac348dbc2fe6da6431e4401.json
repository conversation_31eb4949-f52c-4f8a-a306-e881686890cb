{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Level, Status } from 'src/app/core/models/classroom.model';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nimport * as i2 from \"src/app/core/services/classroom.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/progress-spinner\";\nfunction StatsComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵelement(1, \"mat-spinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction StatsComponent_div_1_div_167_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵelement(1, \"div\");\n    i0.ɵɵelementStart(2, \"div\", 15);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r1 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i_r1 % 2 == 0 ? \"stats-number gray-bg\" : \"stats-number\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMapInterpolate1(\"circle-bullet bullet-\", i_r1, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.dataPackageHoursClassroomsStats[0]);\n  }\n}\nfunction StatsComponent_div_1_div_171_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataPackageCountClassroomsStat_r3 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    i0.ɵɵproperty(\"ngClass\", i_r4 % 2 == 0 ? \"stats-number gray-bg\" : \"stats-number\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(dataPackageCountClassroomsStat_r3);\n  }\n}\nfunction StatsComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"div\", 6)(4, \"div\", 7);\n    i0.ɵɵtext(5, \" Classrooms \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 8);\n    i0.ɵɵelement(7, \"canvas\", 9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 10)(9, \"div\", 11)(10, \"div\", 12);\n    i0.ɵɵtext(11, \" Classroom Type \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 13);\n    i0.ɵɵelement(13, \"div\", 14);\n    i0.ɵɵelementStart(14, \"div\", 15);\n    i0.ɵɵtext(15, \"Ongoing\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 16);\n    i0.ɵɵelement(17, \"div\", 17);\n    i0.ɵɵelementStart(18, \"div\", 15);\n    i0.ɵɵtext(19, \"Expired\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 13);\n    i0.ɵɵelement(21, \"div\", 18);\n    i0.ɵɵelementStart(22, \"div\", 15);\n    i0.ɵɵtext(23, \"On Hold\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 16);\n    i0.ɵɵelement(25, \"div\", 19);\n    i0.ɵɵelementStart(26, \"div\", 15);\n    i0.ɵɵtext(27, \"Inactive\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 20);\n    i0.ɵɵtext(29, \"Total\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 21)(31, \"div\", 12);\n    i0.ɵɵtext(32, \" Students \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 13);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"div\", 16);\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 13);\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 16);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"div\", 13);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelement(43, \"div\", 22);\n    i0.ɵɵelementStart(44, \"div\", 23)(45, \"div\", 5)(46, \"div\", 24)(47, \"div\", 7);\n    i0.ɵɵtext(48, \" Levels+ \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 8);\n    i0.ɵɵelement(50, \"canvas\", 25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"div\", 26)(52, \"div\", 27)(53, \"div\", 12);\n    i0.ɵɵtext(54, \" Level \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"div\", 13);\n    i0.ɵɵelement(56, \"div\", 14);\n    i0.ɵɵelementStart(57, \"div\", 15);\n    i0.ɵɵtext(58, \"A1\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(59, \"div\", 16);\n    i0.ɵɵelement(60, \"div\", 28);\n    i0.ɵɵelementStart(61, \"div\", 15);\n    i0.ɵɵtext(62, \"A2\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"div\", 13);\n    i0.ɵɵelement(64, \"div\", 17);\n    i0.ɵɵelementStart(65, \"div\", 15);\n    i0.ɵɵtext(66, \"B1\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(67, \"div\", 16);\n    i0.ɵɵelement(68, \"div\", 18);\n    i0.ɵɵelementStart(69, \"div\", 15);\n    i0.ɵɵtext(70, \"B2\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(71, \"div\", 13);\n    i0.ɵɵelement(72, \"div\", 19);\n    i0.ɵɵelementStart(73, \"div\", 15);\n    i0.ɵɵtext(74, \"C1\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(75, \"div\", 16);\n    i0.ɵɵelement(76, \"div\", 19);\n    i0.ɵɵelementStart(77, \"div\", 15);\n    i0.ɵɵtext(78, \"C2\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(79, \"div\", 20);\n    i0.ɵɵtext(80, \"All\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(81, \"div\", 27)(82, \"div\", 12);\n    i0.ɵɵtext(83, \" Students \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(84, \"div\", 13)(85, \"div\", 15);\n    i0.ɵɵtext(86);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(87, \"div\", 16)(88, \"div\", 15);\n    i0.ɵɵtext(89);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(90, \"div\", 13)(91, \"div\", 15);\n    i0.ɵɵtext(92);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(93, \"div\", 16)(94, \"div\", 15);\n    i0.ɵɵtext(95);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(96, \"div\", 13)(97, \"div\", 15);\n    i0.ɵɵtext(98);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(99, \"div\", 16)(100, \"div\", 15);\n    i0.ɵɵtext(101);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(102, \"div\", 27)(103, \"div\", 12);\n    i0.ɵɵtext(104, \" Number of Hours \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(105, \"div\", 13);\n    i0.ɵɵtext(106);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(107, \"div\", 16);\n    i0.ɵɵtext(108);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(109, \"div\", 13);\n    i0.ɵɵtext(110);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(111, \"div\", 16);\n    i0.ɵɵtext(112);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(113, \"div\", 13);\n    i0.ɵɵtext(114);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(115, \"div\", 16);\n    i0.ɵɵtext(116);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelement(117, \"div\", 22);\n    i0.ɵɵelementStart(118, \"div\", 4)(119, \"div\", 5)(120, \"div\", 6)(121, \"div\", 7);\n    i0.ɵɵtext(122, \" Classroom Sizes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(123, \"div\", 8);\n    i0.ɵɵelement(124, \"canvas\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(125, \"div\", 10)(126, \"div\", 11)(127, \"div\", 12);\n    i0.ɵɵtext(128, \" Classroom Type \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(129, \"div\", 13);\n    i0.ɵɵelement(130, \"div\", 14);\n    i0.ɵɵelementStart(131, \"div\", 15);\n    i0.ɵɵtext(132);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(133, \"div\", 16);\n    i0.ɵɵelement(134, \"div\", 28);\n    i0.ɵɵelementStart(135, \"div\", 15);\n    i0.ɵɵtext(136);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(137, \"div\", 13);\n    i0.ɵɵelement(138, \"div\", 17);\n    i0.ɵɵelementStart(139, \"div\", 15);\n    i0.ɵɵtext(140);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(141, \"div\", 16);\n    i0.ɵɵelement(142, \"div\", 18);\n    i0.ɵɵelementStart(143, \"div\", 15);\n    i0.ɵɵtext(144);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(145, \"div\", 21)(146, \"div\", 12);\n    i0.ɵɵtext(147, \" No \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(148, \"div\", 13);\n    i0.ɵɵtext(149);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(150, \"div\", 16);\n    i0.ɵɵtext(151);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(152, \"div\", 13);\n    i0.ɵɵtext(153);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(154, \"div\", 16);\n    i0.ɵɵtext(155);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(156, \"div\", 4)(157, \"div\", 5)(158, \"div\", 6)(159, \"div\", 7);\n    i0.ɵɵtext(160, \" Packages \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(161, \"div\", 8);\n    i0.ɵɵelement(162, \"canvas\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(163, \"div\", 10)(164, \"div\", 11)(165, \"div\", 12);\n    i0.ɵɵtext(166, \" Number of Hours \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(167, StatsComponent_div_1_div_167_Template, 4, 5, \"div\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(168, \"div\", 21)(169, \"div\", 12);\n    i0.ɵɵtext(170, \" Students \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(171, StatsComponent_div_1_div_171_Template, 2, 2, \"div\", 31);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelement(172, \"div\", 22);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(34);\n    i0.ɵɵtextInterpolate(ctx_r1.statusClassroomsStats == null ? null : ctx_r1.statusClassroomsStats.ongoing);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.statusClassroomsStats == null ? null : ctx_r1.statusClassroomsStats.expired);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.statusClassroomsStats == null ? null : ctx_r1.statusClassroomsStats.onHold);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.statusClassroomsStats == null ? null : ctx_r1.statusClassroomsStats.inActive);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.statusClassroomsStats == null ? null : ctx_r1.statusClassroomsStats.total);\n    i0.ɵɵadvance(44);\n    i0.ɵɵtextInterpolate(ctx_r1.dataStudentsCount[0]);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.dataStudentsCount[1]);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.dataStudentsCount[2]);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.dataStudentsCount[3]);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.dataStudentsCount[4]);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.dataStudentsCount[5]);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.dataHoursCount[0]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.dataHoursCount[1]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.dataHoursCount[2]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.dataHoursCount[3]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.dataHoursCount[4]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.dataHoursCount[5]);\n    i0.ɵɵadvance(16);\n    i0.ɵɵtextInterpolate(ctx_r1.dataClassroomSizesLabels[0]);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.dataClassroomSizesLabels[1]);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.dataClassroomSizesLabels[2]);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.dataClassroomSizesLabels[3]);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.dataClassroomSizesStats[0]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.dataClassroomSizesStats[1]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.dataClassroomSizesStats[2]);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.dataClassroomSizesStats[3]);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.dataPackageHoursClassroomsStats);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.dataPackageCountClassroomsStats);\n  }\n}\nexport let StatsComponent = /*#__PURE__*/(() => {\n  class StatsComponent {\n    constructor(generalService, classroomService) {\n      this.generalService = generalService;\n      this.classroomService = classroomService;\n      this.classroomsType = \"\";\n      this.subs = new SubSink();\n      this.isTablet = false;\n      this.statusClassroomsStats = {};\n      this.dataClassroomSizesStats = [];\n      this.dataClassroomSizesLabels = [];\n      this.dataStudentsCount = [];\n      this.dataHoursCount = [];\n      this.packageClassroomsStats = [];\n      this.dataPackageHoursClassroomsStats = [];\n      this.dataPackageCountClassroomsStats = [];\n      this.isLoading = true;\n    }\n    ngOnInit() {\n      this.subs.sink = this.generalService.deviceKind.subscribe(res => {\n        this.isTablet = res.is1024;\n      });\n    }\n    ngAfterViewInit() {\n      setTimeout(() => {\n        this.isLoading = false;\n        this.createStatusClassroomStats();\n        this.createLevelClassroomStats();\n        this.createClassroomSizesStats();\n        this.createPackagesStats();\n        // this.createDoughnut(\n        //   'doughnut',\n        //   'chartStatusCertificates',\n        //   [Status.ON_GOING, Status.PENDING, Status.EXPIRED, Status.ON_HOLD, Status.INACTIVE],\n        //   [this.statusClassroomsStats!.on_going, this.statusClassroomsStats!.pending, this.statusClassroomsStats!.expired, this.statusClassroomsStats!.on_hold, this.statusClassroomsStats!.inactive]\n        // ).then(res => {\n        //   // this.chartStatusClassroomsStats = res;\n        // });\n        // this.createPolarArea(\n        //   'chartSizes',\n        //   [Status.ON_GOING, Status.PENDING, Status.EXPIRED, Status.ON_HOLD, Status.INACTIVE],\n        //   [this.statusClassroomsStats!.on_going, this.statusClassroomsStats!.pending, this.statusClassroomsStats!.expired, this.statusClassroomsStats!.on_hold, this.statusClassroomsStats!.inactive]\n        // ).then(res => {\n        //   // this.chartStatusClassroomsStats = res;\n        // });\n        // this.createPolarArea(\n        //   'chartPackages',\n        //   [Status.ON_GOING, Status.PENDING, Status.EXPIRED, Status.ON_HOLD, Status.INACTIVE],\n        //   [this.statusClassroomsStats!.on_going, this.statusClassroomsStats!.pending, this.statusClassroomsStats!.expired, this.statusClassroomsStats!.on_hold, this.statusClassroomsStats!.inactive]\n        // ).then(res => {\n        //   // this.chartStatusClassroomsStats = res;\n        // });\n        // this.createLine(\n        //   'chartRetention',\n        //   [Status.ON_GOING, Status.PENDING, Status.EXPIRED, Status.ON_HOLD, Status.INACTIVE],\n        //   [this.statusClassroomsStats!.on_going, this.statusClassroomsStats!.pending, this.statusClassroomsStats!.expired, this.statusClassroomsStats!.on_hold, this.statusClassroomsStats!.inactive]\n        // ).then(res => {\n        //   // this.chartStatusClassroomsStats = res;\n        // });\n        // this.createDoughnut(\n        //   'pie',\n        //   'chartRenewal',\n        //   [Status.ON_GOING, Status.PENDING, Status.EXPIRED, Status.ON_HOLD, Status.INACTIVE],\n        //   [this.statusClassroomsStats!.on_going, this.statusClassroomsStats!.pending, this.statusClassroomsStats!.expired, this.statusClassroomsStats!.on_hold, this.statusClassroomsStats!.inactive]\n        // ).then(res => {\n        //   // this.chartStatusClassroomsStats = res;\n        // });\n        // this.createDoughnut(\n        //   'pie',\n        //   'chartGoals',\n        //   [Status.ON_GOING, Status.PENDING, Status.EXPIRED, Status.ON_HOLD, Status.INACTIVE],\n        //   [this.statusClassroomsStats!.on_going, this.statusClassroomsStats!.pending, this.statusClassroomsStats!.expired, this.statusClassroomsStats!.on_hold, this.statusClassroomsStats!.inactive]\n        // ).then(res => {\n        //   // this.chartStatusClassroomsStats = res;\n        // });\n      }, 2000);\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n    }\n    createStatusClassroomStats() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        let stats = yield _this.classroomService.getStatusClassroomStats();\n        console.log(stats);\n        _this.statusClassroomsStats = stats;\n        _this.createDoughnut('doughnut', 'chartStatusClassroomsStats', [Status.ON_GOING, Status.EXPIRED, Status.ON_HOLD, Status.INACTIVE], [_this.statusClassroomsStats.ongoing, _this.statusClassroomsStats.expired, _this.statusClassroomsStats.onHold, _this.statusClassroomsStats.inActive]).then(res => {});\n        return stats;\n      })();\n    }\n    createLevelClassroomStats() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        let stats = yield _this2.classroomService.getStudentsHoursStatsOnEachLevelStats();\n        console.log(stats);\n        _this2.levelClassroomsStats = stats;\n        for (let level of _this2.levelClassroomsStats) {\n          _this2.dataStudentsCount.push(level.studentCount);\n          _this2.dataHoursCount.push(level.hoursCount);\n        }\n        _this2.createBar('chartLevels', [Level.A1, Level.A2, Level.B1, Level.B2, Level.C1, Level.C2], _this2.dataStudentsCount, _this2.dataHoursCount).then(res => {});\n        return stats;\n      })();\n    }\n    createPackagesStats() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        let stats = yield _this3.classroomService.getPackageStats();\n        console.log(stats);\n        _this3.packageClassroomsStats = stats;\n        for (let pack of _this3.packageClassroomsStats) {\n          _this3.dataPackageHoursClassroomsStats.push(pack.packageHours.toString());\n          _this3.dataPackageCountClassroomsStats.push(pack.packageCount);\n        }\n        _this3.createPolarArea('chartPackages', _this3.dataPackageHoursClassroomsStats, _this3.dataPackageCountClassroomsStats).then(res => {});\n        return stats;\n      })();\n    }\n    createClassroomSizesStats() {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        let stats = yield _this4.classroomService.getClassroomSizesStats();\n        console.log(stats);\n        _this4.classroomSizesStats = stats;\n        Object.entries(_this4.classroomSizesStats).forEach(entry => {\n          _this4.dataClassroomSizesLabels?.push(entry[0]);\n          _this4.dataClassroomSizesStats?.push(entry[1]);\n          // this.dataClassroomSizesStats?.push(2)\n        });\n        _this4.createPolarArea('chartSizes', _this4.dataClassroomSizesLabels, _this4.dataClassroomSizesStats).then(res => {});\n        return stats;\n      })();\n    }\n    getGradient(chartArea, index, gradients) {\n      let width, height;\n      const chartWidth = chartArea.right - chartArea.left;\n      const chartHeight = chartArea.bottom - chartArea.top;\n      if (gradients[index] === null || width !== chartWidth || height !== chartHeight) {\n        width = chartWidth;\n        height = chartHeight;\n      }\n      return gradients[index];\n    }\n    // getStatusClassroomsStatsGradients(ctx: any, chartArea: any): any[] {\n    //   let gradients: any[] = [];\n    //   gradients[0] = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);\n    //   gradients[0].addColorStop(0, \"#b99ac8\");\n    //   gradients[0].addColorStop(0.26, \"#a18cc0\");\n    //   gradients[0].addColorStop(0.81, \"#666aad\");\n    //   gradients[0].addColorStop(1.0, \"#515ea7\");\n    //   gradients[1] = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);\n    //   gradients[1].addColorStop(0, \"#eb7fb1\");\n    //   gradients[1].addColorStop(0.31, \"#e667a5\");\n    //   gradients[1].addColorStop(0.98, \"#dc2c87\");\n    //   gradients[1].addColorStop(1, \"#dc2b87\");\n    //   gradients[2] = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);\n    //   gradients[2].addColorStop(0, \"#ef7ff1\");\n    //   gradients[2].addColorStop(0.31, \"#ef67f5\");\n    //   gradients[2].addColorStop(0.98, \"#df2cf7\");\n    //   gradients[2].addColorStop(1, \"#df2b8f\");\n    //   gradients[3] = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);\n    //   gradients[3].addColorStop(1, \"#7accd6\");\n    //   gradients[4] = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);\n    //   gradients[4].addColorStop(0, \"#e38ab9\");\n    //   gradients[4].addColorStop(0.34, \"#d572ac\");\n    //   gradients[4].addColorStop(0.96, \"#b1378c\");\n    //   gradients[4].addColorStop(1, \"#af348a\");\n    //   gradients[5] = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);\n    //   gradients[5].addColorStop(0, \"#72cef2\");\n    //   gradients[5].addColorStop(0.27, \"#5ac7f0\");\n    //   gradients[5].addColorStop(0.83, \"#1fb7ed\");\n    //   gradients[5].addColorStop(1, \"#0eb2ec\");\n    //   return gradients;\n    // }\n    createDoughnut(type, id, labels, data) {\n      return new Promise((resolve, reject) => {\n        // const doughnut: Chart = new Chart(id, {\n        //   type: type,\n        //   data: {\n        //     labels: labels,\n        //     datasets: [{\n        //       data: data,\n        //       backgroundColor: [\n        //         '#A4A2E6',\n        //         '#4CC9F0',\n        //         '#F72585',\n        //         '#4895EF',\n        //         '#B5179E',\n        //         '#F27769',\n        //         '#7209B7',\n        //         '#3F37C9',\n        //       ]\n        //     }]\n        //   },\n        //   options: {\n        //     responsive: true,\n        //     legend: {\n        //       display: false,\n        //     }\n        //   },\n        // });\n        // if (doughnut) {\n        //   resolve(doughnut);\n        // } else {\n        //   reject('No doughnut found for: ' + id);\n        // }\n      });\n    }\n    createLine(id, labels, data) {\n      // return new Promise((resolve, reject) => {\n      //   const doughnut: Chart = new Chart(id, {\n      //     type: 'line',\n      //     data: {\n      //       labels: labels,\n      //       datasets: [{\n      //         data: data,\n      //       }]\n      //     },\n      //     options: {\n      //       responsive: true,\n      //       plugins: {\n      //         legend: {\n      //           position: 'top',\n      //         },\n      //         title: {\n      //           display: true,\n      //           text: 'Chart.js Line Chart'\n      //         }\n      //       }\n      //     },\n      //   });\n      //   if (doughnut) {\n      //     resolve(doughnut);\n      //   } else {\n      //     reject('No doughnut found for: ' + id);\n      //   }\n      // });\n    }\n    createPolarArea(id, labels, data) {\n      return new Promise((resolve, reject) => {\n        // const doughnut: Chart = new Chart(id, {\n        //   type: 'polarArea',\n        //   data: {\n        //     labels: labels,\n        //     datasets: [{\n        //       data: data,\n        //       backgroundColor: [\n        //         '#A4A2E6',\n        //         '#4CC9F0',\n        //         '#F72585',\n        //         '#4895EF',\n        //         '#B5179E',\n        //         '#F27769',\n        //         '#7209B7',\n        //         '#3F37C9',\n        //       ]\n        //     }]\n        //   },\n        //   options: {\n        //     legend: {\n        //       display: false,\n        //       position: \"right\",\n        //       labels: {\n        //         usePointStyle: true,\n        //         boxWidth: 8,\n        //         fontSize: 15,\n        //         fontColor: \"black\",\n        //         // padding: 9\n        //       },\n        //     }\n        //   },\n        // });\n        // if (doughnut) {\n        //   resolve(doughnut);\n        // } else {\n        //   reject('No doughnut found for: ' + id);\n        // }\n      });\n    }\n    createBar(id, labels, dataStudentsCount, dataHoursCount) {\n      return new Promise((resolve, reject) => {\n        // const doughnut: Chart = new Chart(id, {\n        //   type: 'horizontalBar',\n        //   data: {\n        //     labels: labels,\n        //     datasets: [\n        //       {\n        //         data: dataStudentsCount,\n        //         backgroundColor: \"#f72585\"\n        //         // barThickness: 2\n        //       },\n        //       {\n        //         data: dataHoursCount,\n        //         backgroundColor: \"#4895ef\"\n        //         // barThickness: 2\n        //       }\n        //     ]\n        //   },\n        //   options: {\n        //     responsive: true,\n        //     legend: {\n        //       display: false,\n        //     }\n        //   },\n        // });\n        // if (doughnut) {\n        //   resolve(doughnut);\n        // } else {\n        //   reject('No doughnut found for: ' + id);\n        // }\n      });\n    }\n    static #_ = this.ɵfac = function StatsComponent_Factory(t) {\n      return new (t || StatsComponent)(i0.ɵɵdirectiveInject(i1.GeneralService), i0.ɵɵdirectiveInject(i2.ClassroomService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: StatsComponent,\n      selectors: [[\"app-stats\"]],\n      inputs: {\n        classroomsType: \"classroomsType\"\n      },\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"spinner\", 4, \"ngIf\"], [\"class\", \"classrooms-stats\", 4, \"ngIf\"], [1, \"spinner\"], [1, \"classrooms-stats\"], [1, \"stats-section\"], [1, \"stats-col\"], [1, \"stats-chart\"], [1, \"stats-title\"], [2, \"padding\", \"20px 40px\"], [\"id\", \"chartStatusClassroomsStats\", \"width\", \"1\", \"height\", \"1\"], [1, \"stats\"], [1, \"col-2-left\"], [1, \"stats-section-title\"], [1, \"stats-number\", \"gray-bg\"], [1, \"circle-bullet\", \"bullet-0\"], [1, \"m-l-5\"], [1, \"stats-number\"], [1, \"circle-bullet\", \"bullet-2\"], [1, \"circle-bullet\", \"bullet-3\"], [1, \"circle-bullet\", \"bullet-4\"], [1, \"stats-number\", \"m-l-5\", \"gray-bg\"], [1, \"col-2-right\"], [1, \"seperator\"], [1, \"stats-section-100\"], [1, \"stats-chart-100\"], [\"id\", \"chartLevels\", \"width\", \"1\", \"height\", \"1\"], [1, \"stats-100\"], [1, \"col-3-even\"], [1, \"circle-bullet\", \"bullet-1\"], [\"id\", \"chartSizes\", \"width\", \"1\", \"height\", \"1\"], [\"id\", \"chartPackages\", \"width\", \"1\", \"height\", \"1\"], [3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [3, \"ngClass\"]],\n      template: function StatsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, StatsComponent_div_0_Template, 2, 0, \"div\", 0)(1, StatsComponent_div_1_Template, 173, 27, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i4.MatProgressSpinner],\n      styles: [\".circle-bullet[_ngcontent-%COMP%]{width:8px;height:8px;border-radius:50%;margin-right:5px;margin-left:5px}.bullet-0[_ngcontent-%COMP%]{background-color:#a4a2e6}.bullet-1[_ngcontent-%COMP%]{background-color:#4cc9f0}.bullet-2[_ngcontent-%COMP%]{background-color:#f72585}.bullet-3[_ngcontent-%COMP%]{background-color:#4895ef}.bullet-4[_ngcontent-%COMP%]{background-color:#b5179e}.bullet-5[_ngcontent-%COMP%]{background-color:#f27769}.bullet-6[_ngcontent-%COMP%]{background-color:#7209b7}.bullet-7[_ngcontent-%COMP%]{background-color:#3f37c9}.classrooms-stats[_ngcontent-%COMP%]{font-size:15px;justify-content:space-evenly;width:100%;flex-wrap:wrap}.stats-section[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]{margin-top:30px}@media screen and (max-width: 1024px){.stats-section[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]{width:100%;flex-basis:100%}}.stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]{display:flex;position:relative;flex-wrap:wrap}.stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-chart[_ngcontent-%COMP%], .stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-chart-100[_ngcontent-%COMP%], .stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-chart-30[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-chart[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-chart-100[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-chart-30[_ngcontent-%COMP%]{display:flex;flex-direction:column}.stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-chart[_ngcontent-%COMP%]   .stats-title[_ngcontent-%COMP%], .stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-chart-100[_ngcontent-%COMP%]   .stats-title[_ngcontent-%COMP%], .stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-chart-30[_ngcontent-%COMP%]   .stats-title[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-chart[_ngcontent-%COMP%]   .stats-title[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-chart-100[_ngcontent-%COMP%]   .stats-title[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-chart-30[_ngcontent-%COMP%]   .stats-title[_ngcontent-%COMP%]{color:var(--main-color);font-weight:700;padding:0 20px 20px;text-align:center}.stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-chart-30[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-chart-30[_ngcontent-%COMP%]{width:30%}@media screen and (max-width: 768px){.stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-chart-30[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-chart-30[_ngcontent-%COMP%]{width:100%}}.stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-chart[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-chart[_ngcontent-%COMP%]{width:50%}@media screen and (max-width: 768px){.stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-chart[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-chart[_ngcontent-%COMP%]{width:100%}}.stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-chart-100[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-chart-100[_ngcontent-%COMP%]{width:33%}@media screen and (max-width: 768px){.stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-chart-100[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-chart-100[_ngcontent-%COMP%]{width:100%}}.stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats[_ngcontent-%COMP%], .stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-100[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-100[_ngcontent-%COMP%]{display:flex;margin-top:30px}.stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats[_ngcontent-%COMP%]   .col-2-left[_ngcontent-%COMP%], .stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-100[_ngcontent-%COMP%]   .col-2-left[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats[_ngcontent-%COMP%]   .col-2-left[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-100[_ngcontent-%COMP%]   .col-2-left[_ngcontent-%COMP%]{width:650%}.stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats[_ngcontent-%COMP%]   .col-2-right[_ngcontent-%COMP%], .stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-100[_ngcontent-%COMP%]   .col-2-right[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats[_ngcontent-%COMP%]   .col-2-right[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-100[_ngcontent-%COMP%]   .col-2-right[_ngcontent-%COMP%]{width:35%}.stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats[_ngcontent-%COMP%]   .col-3-even[_ngcontent-%COMP%], .stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-100[_ngcontent-%COMP%]   .col-3-even[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats[_ngcontent-%COMP%]   .col-3-even[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-100[_ngcontent-%COMP%]   .col-3-even[_ngcontent-%COMP%]{width:33.3333%}.stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats[_ngcontent-%COMP%]   .col-2-even[_ngcontent-%COMP%], .stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-100[_ngcontent-%COMP%]   .col-2-even[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats[_ngcontent-%COMP%]   .col-2-even[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-100[_ngcontent-%COMP%]   .col-2-even[_ngcontent-%COMP%]{width:50%}.stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-70[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-70[_ngcontent-%COMP%]{width:70%}@media screen and (max-width: 768px){.stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-70[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-70[_ngcontent-%COMP%]{width:100%}}.stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats[_ngcontent-%COMP%]{width:50%}@media screen and (max-width: 768px){.stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats[_ngcontent-%COMP%]{width:100%}}.stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-100[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-100[_ngcontent-%COMP%]{width:66%;display:flex;justify-content:space-between}@media screen and (max-width: 768px){.stats-section[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-100[_ngcontent-%COMP%], .stats-section-100[_ngcontent-%COMP%]   .stats-col[_ngcontent-%COMP%]   .stats-100[_ngcontent-%COMP%]{width:100%}}.stats-section[_ngcontent-%COMP%]{width:50%}.stats-section-100[_ngcontent-%COMP%]{width:100%}.stats-section-title[_ngcontent-%COMP%]{color:#93949e}.stats-number[_ngcontent-%COMP%]{display:flex;align-items:center;padding:7px 0}.gray-bg[_ngcontent-%COMP%]{background-color:#f6f6f6}.seperator[_ngcontent-%COMP%]{height:1px;background-color:#707070;width:100%;margin-top:30px}.spinner[_ngcontent-%COMP%]{width:100%;height:500px;display:flex;justify-content:center;margin-top:200px}\"]\n    });\n  }\n  return StatsComponent;\n})();", "map": {"version": 3, "names": ["Level", "Status", "SubSink", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵproperty", "i_r1", "ɵɵadvance", "ɵɵclassMapInterpolate1", "ɵɵtextInterpolate", "ctx_r1", "dataPackageHoursClassroomsStats", "i_r4", "dataPackageCountClassroomsStat_r3", "ɵɵtemplate", "StatsComponent_div_1_div_167_Template", "StatsComponent_div_1_div_171_Template", "statusClassroomsStats", "ongoing", "expired", "onHold", "inActive", "total", "dataStudentsCount", "dataHoursCount", "dataClassroomSizesLabels", "dataClassroomSizesStats", "dataPackageCountClassroomsStats", "StatsComponent", "constructor", "generalService", "classroomService", "classroomsType", "subs", "isTablet", "packageClassroomsStats", "isLoading", "ngOnInit", "sink", "deviceKind", "subscribe", "res", "is1024", "ngAfterViewInit", "setTimeout", "createStatusClassroomStats", "createLevelClassroomStats", "createClassroomSizesStats", "createPackagesStats", "ngOnDestroy", "unsubscribe", "_this", "_asyncToGenerator", "stats", "getStatusClassroomStats", "console", "log", "createDoughnut", "ON_GOING", "EXPIRED", "ON_HOLD", "INACTIVE", "then", "_this2", "getStudentsHoursStatsOnEachLevelStats", "levelClassroomsStats", "level", "push", "studentCount", "hoursCount", "createBar", "A1", "A2", "B1", "B2", "C1", "C2", "_this3", "getPackageStats", "pack", "packageHours", "toString", "packageCount", "createPolarArea", "_this4", "getClassroomSizesStats", "classroomSizesStats", "Object", "entries", "for<PERSON>ach", "entry", "getGradient", "chartArea", "index", "gradients", "width", "height", "chartWidth", "right", "left", "chartHeight", "bottom", "top", "type", "id", "labels", "data", "Promise", "resolve", "reject", "createLine", "_", "ɵɵdirectiveInject", "i1", "GeneralService", "i2", "ClassroomService", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "StatsComponent_Template", "rf", "ctx", "StatsComponent_div_0_Template", "StatsComponent_div_1_Template"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\stats\\stats.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\stats\\stats.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\r\nimport { CertificateClassroomsStats, ClassroomSizesStats, Level, LevelClassroomsStats, PackageClassroomsStats, Status, StatusClassroomsStats } from 'src/app/core/models/classroom.model';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { SubSink } from 'subsink';\r\n\r\n@Component({\r\n  selector: 'app-stats',\r\n  templateUrl: './stats.component.html',\r\n  styleUrls: ['./stats.component.scss']\r\n})\r\nexport class StatsComponent implements OnInit {\r\n  @Input() classroomsType: string = \"\";\r\n  private subs = new SubSink();\r\n  isTablet: boolean = false;\r\n\r\n  statusClassroomsStats: StatusClassroomsStats = {} as StatusClassroomsStats;\r\n  chartStatusClassroomsStats?: any;\r\n\r\n  classroomSizesStats?: ClassroomSizesStats;\r\n  dataClassroomSizesStats: number[] = [];\r\n  dataClassroomSizesLabels: string[] = [];\r\n\r\n  levelClassroomsStats?: LevelClassroomsStats[];\r\n  dataStudentsCount: number[] = []\r\n  dataHoursCount: number[] = []\r\n\r\n  certificateClassroomsStats?: CertificateClassroomsStats;\r\n  chartCertificateClassroomsStats?: any;\r\n\r\n  packageClassroomsStats: PackageClassroomsStats[] = [];\r\n  dataPackageHoursClassroomsStats: string[] = [];\r\n  dataPackageCountClassroomsStats: number[] = [];\r\n\r\n  numberOfStudentsOnEachLevel?: any;\r\n  numberOfTotalOnEachLevel?: any;\r\n\r\n  isLoading: boolean = true;\r\n\r\n  constructor(\r\n    private generalService: GeneralService,\r\n    private classroomService: ClassroomService\r\n  ) {\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.subs.sink = this.generalService.deviceKind.subscribe(res => {\r\n      this.isTablet = res.is1024;\r\n    })\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    setTimeout(() => {\r\n      this.isLoading = false;\r\n      this.createStatusClassroomStats();\r\n      this.createLevelClassroomStats();\r\n      this.createClassroomSizesStats();\r\n      this.createPackagesStats();\r\n      // this.createDoughnut(\r\n      //   'doughnut',\r\n      //   'chartStatusCertificates',\r\n      //   [Status.ON_GOING, Status.PENDING, Status.EXPIRED, Status.ON_HOLD, Status.INACTIVE],\r\n      //   [this.statusClassroomsStats!.on_going, this.statusClassroomsStats!.pending, this.statusClassroomsStats!.expired, this.statusClassroomsStats!.on_hold, this.statusClassroomsStats!.inactive]\r\n      // ).then(res => {\r\n      //   // this.chartStatusClassroomsStats = res;\r\n      // });\r\n\r\n      // this.createPolarArea(\r\n      //   'chartSizes',\r\n      //   [Status.ON_GOING, Status.PENDING, Status.EXPIRED, Status.ON_HOLD, Status.INACTIVE],\r\n      //   [this.statusClassroomsStats!.on_going, this.statusClassroomsStats!.pending, this.statusClassroomsStats!.expired, this.statusClassroomsStats!.on_hold, this.statusClassroomsStats!.inactive]\r\n      // ).then(res => {\r\n      //   // this.chartStatusClassroomsStats = res;\r\n      // });\r\n\r\n      // this.createPolarArea(\r\n      //   'chartPackages',\r\n      //   [Status.ON_GOING, Status.PENDING, Status.EXPIRED, Status.ON_HOLD, Status.INACTIVE],\r\n      //   [this.statusClassroomsStats!.on_going, this.statusClassroomsStats!.pending, this.statusClassroomsStats!.expired, this.statusClassroomsStats!.on_hold, this.statusClassroomsStats!.inactive]\r\n      // ).then(res => {\r\n      //   // this.chartStatusClassroomsStats = res;\r\n      // });\r\n\r\n      // this.createLine(\r\n      //   'chartRetention',\r\n      //   [Status.ON_GOING, Status.PENDING, Status.EXPIRED, Status.ON_HOLD, Status.INACTIVE],\r\n      //   [this.statusClassroomsStats!.on_going, this.statusClassroomsStats!.pending, this.statusClassroomsStats!.expired, this.statusClassroomsStats!.on_hold, this.statusClassroomsStats!.inactive]\r\n      // ).then(res => {\r\n      //   // this.chartStatusClassroomsStats = res;\r\n      // });\r\n\r\n      // this.createDoughnut(\r\n      //   'pie',\r\n      //   'chartRenewal',\r\n      //   [Status.ON_GOING, Status.PENDING, Status.EXPIRED, Status.ON_HOLD, Status.INACTIVE],\r\n      //   [this.statusClassroomsStats!.on_going, this.statusClassroomsStats!.pending, this.statusClassroomsStats!.expired, this.statusClassroomsStats!.on_hold, this.statusClassroomsStats!.inactive]\r\n      // ).then(res => {\r\n      //   // this.chartStatusClassroomsStats = res;\r\n      // });\r\n\r\n      // this.createDoughnut(\r\n      //   'pie',\r\n      //   'chartGoals',\r\n      //   [Status.ON_GOING, Status.PENDING, Status.EXPIRED, Status.ON_HOLD, Status.INACTIVE],\r\n      //   [this.statusClassroomsStats!.on_going, this.statusClassroomsStats!.pending, this.statusClassroomsStats!.expired, this.statusClassroomsStats!.on_hold, this.statusClassroomsStats!.inactive]\r\n      // ).then(res => {\r\n      //   // this.chartStatusClassroomsStats = res;\r\n      // });\r\n    }, 2000);\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  async createStatusClassroomStats() {\r\n    let stats = await this.classroomService.getStatusClassroomStats();\r\n    console.log(stats)\r\n    this.statusClassroomsStats = stats as StatusClassroomsStats;\r\n    this.createDoughnut(\r\n      'doughnut',\r\n      'chartStatusClassroomsStats',\r\n      [Status.ON_GOING, Status.EXPIRED, Status.ON_HOLD, Status.INACTIVE],\r\n      [this.statusClassroomsStats!.ongoing, this.statusClassroomsStats!.expired, this.statusClassroomsStats!.onHold, this.statusClassroomsStats!.inActive]\r\n    ).then(res => {\r\n    });\r\n    return stats\r\n  }\r\n\r\n  async createLevelClassroomStats() {\r\n    let stats = await this.classroomService.getStudentsHoursStatsOnEachLevelStats();\r\n    console.log(stats)\r\n    this.levelClassroomsStats = stats;\r\n    for (let level of this.levelClassroomsStats as any) {\r\n      this.dataStudentsCount.push(level.studentCount)\r\n      this.dataHoursCount.push(level.hoursCount)\r\n    }\r\n\r\n    this.createBar(\r\n      'chartLevels',\r\n      [Level.A1, Level.A2, Level.B1, Level.B2, Level.C1, Level.C2],\r\n      this.dataStudentsCount,\r\n      this.dataHoursCount\r\n    ).then(res => {\r\n    });\r\n    return stats\r\n  }\r\n\r\n  async createPackagesStats() {\r\n    let stats = await this.classroomService.getPackageStats();\r\n    console.log(stats)\r\n    this.packageClassroomsStats = stats as any;\r\n    for (let pack of this.packageClassroomsStats) {\r\n      this.dataPackageHoursClassroomsStats.push(pack.packageHours.toString())\r\n      this.dataPackageCountClassroomsStats.push(pack.packageCount)\r\n    }\r\n    this.createPolarArea(\r\n      'chartPackages',\r\n      this.dataPackageHoursClassroomsStats,\r\n      this.dataPackageCountClassroomsStats\r\n    ).then(res => {\r\n    });\r\n    return stats\r\n  }\r\n\r\n  async createClassroomSizesStats() {\r\n    let stats = await this.classroomService.getClassroomSizesStats();\r\n    console.log(stats)\r\n    this.classroomSizesStats = stats;\r\n    Object.entries(this.classroomSizesStats!).forEach((entry: any) => {\r\n      this.dataClassroomSizesLabels?.push(entry[0])\r\n      this.dataClassroomSizesStats?.push(entry[1])\r\n      // this.dataClassroomSizesStats?.push(2)\r\n    });\r\n    this.createPolarArea(\r\n      'chartSizes',\r\n      this.dataClassroomSizesLabels!,\r\n      this.dataClassroomSizesStats!\r\n    ).then(res => {\r\n    });\r\n    return stats\r\n  }\r\n\r\n  getGradient(chartArea: any, index: number, gradients: any[]) {\r\n    let width, height;\r\n    const chartWidth = chartArea.right - chartArea.left;\r\n    const chartHeight = chartArea.bottom - chartArea.top;\r\n    if (gradients[index] === null || width !== chartWidth || height !== chartHeight) {\r\n      width = chartWidth;\r\n      height = chartHeight;\r\n    }\r\n    return gradients[index];\r\n  }\r\n\r\n  // getStatusClassroomsStatsGradients(ctx: any, chartArea: any): any[] {\r\n  //   let gradients: any[] = [];\r\n  //   gradients[0] = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);\r\n  //   gradients[0].addColorStop(0, \"#b99ac8\");\r\n  //   gradients[0].addColorStop(0.26, \"#a18cc0\");\r\n  //   gradients[0].addColorStop(0.81, \"#666aad\");\r\n  //   gradients[0].addColorStop(1.0, \"#515ea7\");\r\n  //   gradients[1] = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);\r\n  //   gradients[1].addColorStop(0, \"#eb7fb1\");\r\n  //   gradients[1].addColorStop(0.31, \"#e667a5\");\r\n  //   gradients[1].addColorStop(0.98, \"#dc2c87\");\r\n  //   gradients[1].addColorStop(1, \"#dc2b87\");\r\n  //   gradients[2] = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);\r\n  //   gradients[2].addColorStop(0, \"#ef7ff1\");\r\n  //   gradients[2].addColorStop(0.31, \"#ef67f5\");\r\n  //   gradients[2].addColorStop(0.98, \"#df2cf7\");\r\n  //   gradients[2].addColorStop(1, \"#df2b8f\");\r\n  //   gradients[3] = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);\r\n  //   gradients[3].addColorStop(1, \"#7accd6\");\r\n  //   gradients[4] = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);\r\n  //   gradients[4].addColorStop(0, \"#e38ab9\");\r\n  //   gradients[4].addColorStop(0.34, \"#d572ac\");\r\n  //   gradients[4].addColorStop(0.96, \"#b1378c\");\r\n  //   gradients[4].addColorStop(1, \"#af348a\");\r\n  //   gradients[5] = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);\r\n  //   gradients[5].addColorStop(0, \"#72cef2\");\r\n  //   gradients[5].addColorStop(0.27, \"#5ac7f0\");\r\n  //   gradients[5].addColorStop(0.83, \"#1fb7ed\");\r\n  //   gradients[5].addColorStop(1, \"#0eb2ec\");\r\n  //   return gradients;\r\n  // }\r\n\r\n  createDoughnut(type: string, id: string, labels: string[], data: number[]) {\r\n\r\n    return new Promise((resolve, reject) => {\r\n      // const doughnut: Chart = new Chart(id, {\r\n      //   type: type,\r\n      //   data: {\r\n      //     labels: labels,\r\n      //     datasets: [{\r\n      //       data: data,\r\n      //       backgroundColor: [\r\n      //         '#A4A2E6',\r\n      //         '#4CC9F0',\r\n      //         '#F72585',\r\n      //         '#4895EF',\r\n      //         '#B5179E',\r\n      //         '#F27769',\r\n      //         '#7209B7',\r\n      //         '#3F37C9',\r\n      //       ]\r\n      //     }]\r\n      //   },\r\n      //   options: {\r\n      //     responsive: true,\r\n      //     legend: {\r\n      //       display: false,\r\n      //     }\r\n      //   },\r\n      // });\r\n      // if (doughnut) {\r\n      //   resolve(doughnut);\r\n      // } else {\r\n      //   reject('No doughnut found for: ' + id);\r\n      // }\r\n    });\r\n  }\r\n\r\n  createLine(id: string, labels: string[], data: number[]) {\r\n    // return new Promise((resolve, reject) => {\r\n    //   const doughnut: Chart = new Chart(id, {\r\n    //     type: 'line',\r\n    //     data: {\r\n    //       labels: labels,\r\n    //       datasets: [{\r\n    //         data: data,\r\n    //       }]\r\n    //     },\r\n    //     options: {\r\n    //       responsive: true,\r\n    //       plugins: {\r\n    //         legend: {\r\n    //           position: 'top',\r\n    //         },\r\n    //         title: {\r\n    //           display: true,\r\n    //           text: 'Chart.js Line Chart'\r\n    //         }\r\n    //       }\r\n    //     },\r\n\r\n    //   });\r\n    //   if (doughnut) {\r\n    //     resolve(doughnut);\r\n    //   } else {\r\n    //     reject('No doughnut found for: ' + id);\r\n    //   }\r\n    // });\r\n  }\r\n\r\n  createPolarArea(id: string, labels: string[], data: number[]) {\r\n    return new Promise((resolve, reject) => {\r\n      // const doughnut: Chart = new Chart(id, {\r\n      //   type: 'polarArea',\r\n      //   data: {\r\n      //     labels: labels,\r\n      //     datasets: [{\r\n      //       data: data,\r\n      //       backgroundColor: [\r\n      //         '#A4A2E6',\r\n      //         '#4CC9F0',\r\n      //         '#F72585',\r\n      //         '#4895EF',\r\n      //         '#B5179E',\r\n      //         '#F27769',\r\n      //         '#7209B7',\r\n      //         '#3F37C9',\r\n      //       ]\r\n      //     }]\r\n      //   },\r\n      //   options: {\r\n      //     legend: {\r\n      //       display: false,\r\n      //       position: \"right\",\r\n      //       labels: {\r\n      //         usePointStyle: true,\r\n      //         boxWidth: 8,\r\n      //         fontSize: 15,\r\n      //         fontColor: \"black\",\r\n      //         // padding: 9\r\n      //       },\r\n      //     }\r\n      //   },\r\n\r\n      // });\r\n      // if (doughnut) {\r\n      //   resolve(doughnut);\r\n      // } else {\r\n      //   reject('No doughnut found for: ' + id);\r\n      // }\r\n    });\r\n  }\r\n\r\n  createBar(id: string, labels: string[], dataStudentsCount: number[], dataHoursCount: number[]) {\r\n    return new Promise((resolve, reject) => {\r\n      // const doughnut: Chart = new Chart(id, {\r\n      //   type: 'horizontalBar',\r\n      //   data: {\r\n      //     labels: labels,\r\n      //     datasets: [\r\n      //       {\r\n      //         data: dataStudentsCount,\r\n      //         backgroundColor: \"#f72585\"\r\n      //         // barThickness: 2\r\n      //       },\r\n      //       {\r\n      //         data: dataHoursCount,\r\n      //         backgroundColor: \"#4895ef\"\r\n      //         // barThickness: 2\r\n      //       }\r\n      //     ]\r\n      //   },\r\n      //   options: {\r\n      //     responsive: true,\r\n      //     legend: {\r\n      //       display: false,\r\n      //     }\r\n      //   },\r\n      // });\r\n      // if (doughnut) {\r\n      //   resolve(doughnut);\r\n      // } else {\r\n      //   reject('No doughnut found for: ' + id);\r\n      // }\r\n    });\r\n  }\r\n}\r\n", "<div *ngIf=\"isLoading\" class=\"spinner\">\r\n  <mat-spinner></mat-spinner>\r\n</div>\r\n\r\n<div *ngIf=\"!isLoading\" class=\"classrooms-stats\">\r\n\r\n\r\n  <div class=\"stats-section\">\r\n    <div class=\"stats-col\">\r\n      <div class=\"stats-chart\">\r\n        <div class=\"stats-title\">\r\n          Classrooms\r\n        </div>\r\n        <div style=\"padding:20px 40px\">\r\n          <canvas id=\"chartStatusClassroomsStats\" width=\"1\" height=\"1\"></canvas>\r\n        </div>\r\n      </div>\r\n      <div class=\"stats\">\r\n        <div class=\"col-2-left\">\r\n          <div class=\"stats-section-title\">\r\n            Classroom Type\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">\r\n            <div class=\"circle-bullet bullet-0\"></div>\r\n            <div class=\"m-l-5\">Ongoing</div>\r\n          </div>\r\n          <!-- <div class=\"stats-number\">\r\n            <div class=\"circle-bullet bullet-1\"></div>\r\n            <div class=\"m-l-5\">Pending</div>\r\n          </div> -->\r\n          <div class=\"stats-number\">\r\n            <div class=\"circle-bullet bullet-2\"></div>\r\n            <div class=\"m-l-5\">Expired</div>\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">\r\n            <div class=\"circle-bullet bullet-3\"></div>\r\n            <div class=\"m-l-5\">On Hold</div>\r\n          </div>\r\n          <div class=\"stats-number\">\r\n            <div class=\"circle-bullet bullet-4\"></div>\r\n            <div class=\"m-l-5\">Inactive</div>\r\n          </div>\r\n          <div class=\"stats-number m-l-5 gray-bg\">Total</div>\r\n        </div>\r\n        <div class=\"col-2-right\">\r\n          <div class=\"stats-section-title\">\r\n            Students\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.ongoing}}</div>\r\n          <!-- <div class=\"stats-number\">{{statusClassroomsStats?.pending}}</div> -->\r\n          <div class=\"stats-number\">{{statusClassroomsStats?.expired}}</div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.onHold}}</div>\r\n          <div class=\"stats-number\">{{statusClassroomsStats?.inActive}}</div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.total}}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <!-- \r\n  <div class=\"stats-section\">\r\n    <div class=\"stats-col\">\r\n      <div class=\"stats-chart\">\r\n        <div class=\"stats-title\">\r\n          Certificates Aquired\r\n        </div>\r\n        <div style=\"padding:20px 40px\">\r\n          <canvas id=\"chartStatusCertificates\" width=\"1\" height=\"1\"></canvas>\r\n        </div>\r\n      </div>\r\n      <div class=\"stats\">\r\n        <div class=\"col-2-left\">\r\n          <div class=\"stats-section-title\">\r\n            Certificates\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">\r\n            <div class=\"circle-bullet bullet-0\"></div>\r\n            <div class=\"m-l-5\">A1</div>\r\n          </div>\r\n          <div class=\"stats-number\">\r\n            <div class=\"circle-bullet bullet-1\"></div>\r\n            <div class=\"m-l-5\">A2</div>\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">\r\n            <div class=\"circle-bullet bullet-2\"></div>\r\n            <div class=\"m-l-5\">B1</div>\r\n          </div>\r\n          <div class=\"stats-number\">\r\n            <div class=\"circle-bullet bullet-3\"></div>\r\n            <div class=\"m-l-5\">B2</div>\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">\r\n            <div class=\"circle-bullet bullet-4\"></div>\r\n            <div class=\"m-l-5\">C1</div>\r\n          </div>\r\n          <div class=\"stats-number\">\r\n            <div class=\"circle-bullet bullet-4\"></div>\r\n            <div class=\"m-l-5\">C2</div>\r\n          </div>\r\n          <div class=\"stats-number m-l-5 gray-bg\">All</div>\r\n        </div>\r\n        <div class=\"col-2-right\">\r\n          <div class=\"stats-section-title\">\r\n            No\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.on_going}}</div>\r\n          <div class=\"stats-number\">{{statusClassroomsStats?.pending}}</div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.expired}}</div>\r\n          <div class=\"stats-number\">{{statusClassroomsStats?.on_hold}}</div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.inactive}}</div>\r\n          <div class=\"stats-number\">{{statusClassroomsStats?.total}}</div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.total}}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  -->\r\n  <div class=\"seperator\"></div>\r\n\r\n  <div class=\"stats-section-100\">\r\n    <div class=\"stats-col\">\r\n      <div class=\"stats-chart-100\">\r\n        <div class=\"stats-title\">\r\n          Levels+\r\n        </div>\r\n        <div style=\"padding:20px 40px\">\r\n          <canvas id=\"chartLevels\" width=\"1\" height=\"1\"></canvas>\r\n        </div>\r\n      </div>\r\n      <div class=\"stats-100\">\r\n        <div class=\"col-3-even\">\r\n          <div class=\"stats-section-title\">\r\n            Level\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">\r\n            <div class=\"circle-bullet bullet-0\"></div>\r\n            <div class=\"m-l-5\">A1</div>\r\n          </div>\r\n          <div class=\"stats-number\">\r\n            <div class=\"circle-bullet bullet-1\"></div>\r\n            <div class=\"m-l-5\">A2</div>\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">\r\n            <div class=\"circle-bullet bullet-2\"></div>\r\n            <div class=\"m-l-5\">B1</div>\r\n          </div>\r\n          <div class=\"stats-number\">\r\n            <div class=\"circle-bullet bullet-3\"></div>\r\n            <div class=\"m-l-5\">B2</div>\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">\r\n            <div class=\"circle-bullet bullet-4\"></div>\r\n            <div class=\"m-l-5\">C1</div>\r\n          </div>\r\n          <div class=\"stats-number\">\r\n            <div class=\"circle-bullet bullet-4\"></div>\r\n            <div class=\"m-l-5\">C2</div>\r\n          </div>\r\n          <div class=\"stats-number m-l-5 gray-bg\">All</div>\r\n        </div>\r\n        <div class=\"col-3-even\">\r\n          <div class=\"stats-section-title\">\r\n            Students\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">\r\n            <div class=\"m-l-5\">{{dataStudentsCount[0]}}</div>\r\n          </div>\r\n          <div class=\"stats-number\">\r\n            <div class=\"m-l-5\">{{dataStudentsCount[1]}}</div>\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">\r\n            <div class=\"m-l-5\">{{dataStudentsCount[2]}}</div>\r\n          </div>\r\n          <div class=\"stats-number\">\r\n            <div class=\"m-l-5\">{{dataStudentsCount[3]}}</div>\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">\r\n            <div class=\"m-l-5\">{{dataStudentsCount[4]}}</div>\r\n          </div>\r\n          <div class=\"stats-number\">\r\n            <div class=\"m-l-5\">{{dataStudentsCount[5]}}</div>\r\n          </div>\r\n          <!-- <div class=\"stats-number m-l-5 gray-bg\">All</div> -->\r\n        </div>\r\n        <div class=\"col-3-even\">\r\n          <div class=\"stats-section-title\">\r\n            Number of Hours\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">{{dataHoursCount[0]}}</div>\r\n          <div class=\"stats-number\">{{dataHoursCount[1]}}</div>\r\n          <div class=\"stats-number gray-bg\">{{dataHoursCount[2]}}</div>\r\n          <div class=\"stats-number\">{{dataHoursCount[3]}}</div>\r\n          <div class=\"stats-number gray-bg\">{{dataHoursCount[4]}}</div>\r\n          <div class=\"stats-number\">{{dataHoursCount[5]}}</div>\r\n          <!-- <div class=\"stats-number gray-bg\">All}</div> -->\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"seperator\"></div>\r\n\r\n  <div class=\"stats-section\">\r\n    <div class=\"stats-col\">\r\n      <div class=\"stats-chart\">\r\n        <div class=\"stats-title\">\r\n          Classroom Sizes\r\n        </div>\r\n        <div style=\"padding:20px 40px\">\r\n          <canvas id=\"chartSizes\" width=\"1\" height=\"1\"></canvas>\r\n        </div>\r\n      </div>\r\n      <div class=\"stats\">\r\n        <div class=\"col-2-left\">\r\n          <div class=\"stats-section-title\">\r\n            Classroom Type\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">\r\n            <div class=\"circle-bullet bullet-0\"></div>\r\n            <div class=\"m-l-5\">{{dataClassroomSizesLabels[0]}}</div>\r\n          </div>\r\n          <div class=\"stats-number\">\r\n            <div class=\"circle-bullet bullet-1\"></div>\r\n            <div class=\"m-l-5\">{{dataClassroomSizesLabels[1]}}</div>\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">\r\n            <div class=\"circle-bullet bullet-2\"></div>\r\n            <div class=\"m-l-5\">{{dataClassroomSizesLabels[2]}}</div>\r\n          </div>\r\n          <div class=\"stats-number\">\r\n            <div class=\"circle-bullet bullet-3\"></div>\r\n            <div class=\"m-l-5\">{{dataClassroomSizesLabels[3]}}</div>\r\n          </div>\r\n          <!-- <div class=\"stats-number m-l-5\">Total</div> -->\r\n        </div>\r\n        <div class=\"col-2-right\">\r\n          <div class=\"stats-section-title\">\r\n            No\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">{{dataClassroomSizesStats[0]}}</div>\r\n          <div class=\"stats-number\">{{dataClassroomSizesStats[1]}}</div>\r\n          <div class=\"stats-number gray-bg\">{{dataClassroomSizesStats[2]}}</div>\r\n          <div class=\"stats-number\">{{dataClassroomSizesStats[3]}}</div>\r\n          <!-- <div class=\"stats-number\">All</div> -->\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"stats-section\">\r\n    <div class=\"stats-col\">\r\n      <div class=\"stats-chart\">\r\n        <div class=\"stats-title\">\r\n          Packages\r\n        </div>\r\n        <div style=\"padding:20px 40px\">\r\n          <canvas id=\"chartPackages\" width=\"1\" height=\"1\"></canvas>\r\n        </div>\r\n      </div>\r\n      <div class=\"stats\">\r\n        <div class=\"col-2-left\">\r\n          <div class=\"stats-section-title\">\r\n            Number of Hours\r\n          </div>\r\n          <div *ngFor=\"let dataPackageHoursClassroomsStat of dataPackageHoursClassroomsStats; let i=index\" [ngClass]=\"i%2==0? 'stats-number gray-bg': 'stats-number'\">\r\n            <div class=\"circle-bullet bullet-{{i}}\"></div>\r\n            <div class=\"m-l-5\">{{dataPackageHoursClassroomsStats[0]}}</div>\r\n          </div>\r\n          <!-- <div class=\"stats-number m-l-5\">All</div> -->\r\n        </div>\r\n        <div class=\"col-2-right\">\r\n          <div class=\"stats-section-title\">\r\n            Students\r\n          </div>\r\n          <div *ngFor=\"let dataPackageCountClassroomsStat of dataPackageCountClassroomsStats; let i=index\" [ngClass]=\"i%2==0? 'stats-number gray-bg': 'stats-number'\">{{dataPackageCountClassroomsStat}}</div>\r\n          <!-- <div class=\"stats-number\">All</div> -->\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"seperator\"></div>\r\n  <!-- \r\n\r\n  <div class=\"stats-section-100\">\r\n    <div class=\"stats-col\">\r\n      <div class=\"stats-chart\">\r\n        <div class=\"stats-title\">\r\n          Retention\r\n        </div>\r\n        <div style=\"padding:20px 40px\">\r\n          <canvas id=\"chartRetention\"></canvas>\r\n        </div>\r\n      </div>\r\n      <div class=\"stats\">\r\n        <div class=\"col-3-even\">\r\n          <div class=\"stats-section-title\">\r\n            Classroom Type\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">\r\n            <div class=\"circle-bullet bullet-0\"></div>\r\n            <div class=\"m-l-5\">Ongoing</div>\r\n          </div>\r\n          <div class=\"stats-number\">\r\n            <div class=\"circle-bullet bullet-1\"></div>\r\n            <div class=\"m-l-5\">Pending</div>\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">\r\n            <div class=\"circle-bullet bullet-2\"></div>\r\n            <div class=\"m-l-5\">Expired</div>\r\n          </div>\r\n          <div class=\"stats-number\">\r\n            <div class=\"circle-bullet bullet-3\"></div>\r\n            <div class=\"m-l-5\">On Hold</div>\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">\r\n            <div class=\"circle-bullet bullet-4\"></div>\r\n            <div class=\"m-l-5\">Inactive</div>\r\n          </div>\r\n          <div class=\"stats-number m-l-5\">Total</div>\r\n        </div>\r\n        <div class=\"col-3-even\">\r\n          <div class=\"stats-section-title\">\r\n            No\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.on_going}}</div>\r\n          <div class=\"stats-number\">{{statusClassroomsStats?.pending}}</div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.expired}}</div>\r\n          <div class=\"stats-number\">{{statusClassroomsStats?.on_hold}}</div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.inactive}}</div>\r\n          <div class=\"stats-number\">{{statusClassroomsStats?.total}}</div>\r\n        </div>\r\n        <div class=\"col-3-even\">\r\n          <div class=\"stats-section-title\">\r\n            No\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.on_going}}</div>\r\n          <div class=\"stats-number\">{{statusClassroomsStats?.pending}}</div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.expired}}</div>\r\n          <div class=\"stats-number\">{{statusClassroomsStats?.on_hold}}</div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.inactive}}</div>\r\n          <div class=\"stats-number\">{{statusClassroomsStats?.total}}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"seperator\"></div>\r\n\r\n  <div class=\"stats-section-100\">\r\n    <div class=\"stats-col\">\r\n      <div class=\"stats-chart\">\r\n        <div class=\"stats-title\">\r\n          Average Package Renewal Period\r\n        </div>\r\n        <div style=\"padding:20px 40px\">\r\n          <canvas id=\"chartRenewal\" width=\"1\" height=\"1\"></canvas>\r\n        </div>\r\n      </div>\r\n      <div class=\"stats\">\r\n        <div class=\"col-3-even\">\r\n          <div class=\"stats-section-title\">\r\n            Classroom Type\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">\r\n            <div class=\"circle-bullet bullet-0\"></div>\r\n            <div class=\"m-l-5\">Ongoing</div>\r\n          </div>\r\n          <div class=\"stats-number\">\r\n            <div class=\"circle-bullet bullet-1\"></div>\r\n            <div class=\"m-l-5\">Pending</div>\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">\r\n            <div class=\"circle-bullet bullet-2\"></div>\r\n            <div class=\"m-l-5\">Expired</div>\r\n          </div>\r\n          <div class=\"stats-number\">\r\n            <div class=\"circle-bullet bullet-3\"></div>\r\n            <div class=\"m-l-5\">On Hold</div>\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">\r\n            <div class=\"circle-bullet bullet-4\"></div>\r\n            <div class=\"m-l-5\">Inactive</div>\r\n          </div>\r\n          <div class=\"stats-number m-l-5\">Total</div>\r\n        </div>\r\n        <div class=\"col-3-even\">\r\n          <div class=\"stats-section-title\">\r\n            No\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.on_going}}</div>\r\n          <div class=\"stats-number\">{{statusClassroomsStats?.pending}}</div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.expired}}</div>\r\n          <div class=\"stats-number\">{{statusClassroomsStats?.on_hold}}</div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.inactive}}</div>\r\n          <div class=\"stats-number\">{{statusClassroomsStats?.total}}</div>\r\n        </div>\r\n        <div class=\"col-3-even\">\r\n          <div class=\"stats-section-title\">\r\n            No\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.on_going}}</div>\r\n          <div class=\"stats-number\">{{statusClassroomsStats?.pending}}</div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.expired}}</div>\r\n          <div class=\"stats-number\">{{statusClassroomsStats?.on_hold}}</div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.inactive}}</div>\r\n          <div class=\"stats-number\">{{statusClassroomsStats?.total}}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"seperator\"></div>\r\n\r\n  <div class=\"stats-section-100\">\r\n    <div class=\"stats-col\">\r\n      <div class=\"stats-chart\">\r\n        <div class=\"stats-title\">\r\n          Students Goals\r\n        </div>\r\n        <div style=\"padding:20px 40px\">\r\n          <canvas id=\"chartGoals\" width=\"1\" height=\"1\"></canvas>\r\n        </div>\r\n      </div>\r\n      <div class=\"stats\">\r\n        <div class=\"col-3-even\">\r\n          <div class=\"stats-section-title\">\r\n            Classroom Type\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">\r\n            <div class=\"circle-bullet bullet-0\"></div>\r\n            <div class=\"m-l-5\">Ongoing</div>\r\n          </div>\r\n          <div class=\"stats-number\">\r\n            <div class=\"circle-bullet bullet-1\"></div>\r\n            <div class=\"m-l-5\">Pending</div>\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">\r\n            <div class=\"circle-bullet bullet-2\"></div>\r\n            <div class=\"m-l-5\">Expired</div>\r\n          </div>\r\n          <div class=\"stats-number\">\r\n            <div class=\"circle-bullet bullet-3\"></div>\r\n            <div class=\"m-l-5\">On Hold</div>\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">\r\n            <div class=\"circle-bullet bullet-4\"></div>\r\n            <div class=\"m-l-5\">Inactive</div>\r\n          </div>\r\n          <div class=\"stats-number m-l-5\">Total</div>\r\n        </div>\r\n        <div class=\"col-3-even\">\r\n          <div class=\"stats-section-title\">\r\n            No\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.on_going}}</div>\r\n          <div class=\"stats-number\">{{statusClassroomsStats?.pending}}</div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.expired}}</div>\r\n          <div class=\"stats-number\">{{statusClassroomsStats?.on_hold}}</div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.inactive}}</div>\r\n          <div class=\"stats-number\">{{statusClassroomsStats?.total}}</div>\r\n        </div>\r\n        <div class=\"col-3-even\">\r\n          <div class=\"stats-section-title\">\r\n            No\r\n          </div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.on_going}}</div>\r\n          <div class=\"stats-number\">{{statusClassroomsStats?.pending}}</div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.expired}}</div>\r\n          <div class=\"stats-number\">{{statusClassroomsStats?.on_hold}}</div>\r\n          <div class=\"stats-number gray-bg\">{{statusClassroomsStats?.inactive}}</div>\r\n          <div class=\"stats-number\">{{statusClassroomsStats?.total}}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div> -->\r\n</div>"], "mappings": ";AACA,SAA0DA,KAAK,EAAgDC,MAAM,QAA+B,qCAAqC;AAGzL,SAASC,OAAO,QAAQ,SAAS;;;;;;;;ICJjCC,EAAA,CAAAC,cAAA,aAAuC;IACrCD,EAAA,CAAAE,SAAA,kBAA2B;IAC7BF,EAAA,CAAAG,YAAA,EAAM;;;;;IAqQIH,EAAA,CAAAC,cAAA,cAA4J;IAC1JD,EAAA,CAAAE,SAAA,UAA8C;IAC9CF,EAAA,CAAAC,cAAA,cAAmB;IAAAD,EAAA,CAAAI,MAAA,GAAsC;IAC3DJ,EAD2D,CAAAG,YAAA,EAAM,EAC3D;;;;;IAH2FH,EAAA,CAAAK,UAAA,YAAAC,IAAA,oDAA0D;IACpJN,EAAA,CAAAO,SAAA,EAAkC;IAAlCP,EAAA,CAAAQ,sBAAA,0BAAAF,IAAA,KAAkC;IACpBN,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAC,+BAAA,IAAsC;;;;;IAQ3DX,EAAA,CAAAC,cAAA,cAA4J;IAAAD,EAAA,CAAAI,MAAA,GAAkC;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAAnGH,EAAA,CAAAK,UAAA,YAAAO,IAAA,oDAA0D;IAACZ,EAAA,CAAAO,SAAA,EAAkC;IAAlCP,EAAA,CAAAS,iBAAA,CAAAI,iCAAA,CAAkC;;;;;IAvQhMb,EANR,CAAAC,cAAA,aAAiD,aAGpB,aACF,aACI,aACE;IACvBD,EAAA,CAAAI,MAAA,mBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAA+B;IAC7BD,EAAA,CAAAE,SAAA,gBAAsE;IAE1EF,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,cAAmB,cACO,eACW;IAC/BD,EAAA,CAAAI,MAAA,wBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAkC;IAChCD,EAAA,CAAAE,SAAA,eAA0C;IAC1CF,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAI,MAAA,eAAO;IAC5BJ,EAD4B,CAAAG,YAAA,EAAM,EAC5B;IAKNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAE,SAAA,eAA0C;IAC1CF,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAI,MAAA,eAAO;IAC5BJ,EAD4B,CAAAG,YAAA,EAAM,EAC5B;IACNH,EAAA,CAAAC,cAAA,eAAkC;IAChCD,EAAA,CAAAE,SAAA,eAA0C;IAC1CF,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAI,MAAA,eAAO;IAC5BJ,EAD4B,CAAAG,YAAA,EAAM,EAC5B;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAE,SAAA,eAA0C;IAC1CF,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAI,MAAA,gBAAQ;IAC7BJ,EAD6B,CAAAG,YAAA,EAAM,EAC7B;IACNH,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAI,MAAA,aAAK;IAC/CJ,EAD+C,CAAAG,YAAA,EAAM,EAC/C;IAEJH,EADF,CAAAC,cAAA,eAAyB,eACU;IAC/BD,EAAA,CAAAI,MAAA,kBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAI,MAAA,IAAkC;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAE1EH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAI,MAAA,IAAkC;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAClEH,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAI,MAAA,IAAiC;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACzEH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAI,MAAA,IAAmC;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACnEH,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAI,MAAA,IAAgC;IAI1EJ,EAJ0E,CAAAG,YAAA,EAAM,EACpE,EACF,EACF,EACF;IA2DNH,EAAA,CAAAE,SAAA,eAA6B;IAKvBF,EAHN,CAAAC,cAAA,eAA+B,cACN,eACQ,cACF;IACvBD,EAAA,CAAAI,MAAA,iBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAAE,SAAA,kBAAuD;IAE3DF,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAuB,eACG,eACW;IAC/BD,EAAA,CAAAI,MAAA,eACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAkC;IAChCD,EAAA,CAAAE,SAAA,eAA0C;IAC1CF,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAI,MAAA,UAAE;IACvBJ,EADuB,CAAAG,YAAA,EAAM,EACvB;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAE,SAAA,eAA0C;IAC1CF,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAI,MAAA,UAAE;IACvBJ,EADuB,CAAAG,YAAA,EAAM,EACvB;IACNH,EAAA,CAAAC,cAAA,eAAkC;IAChCD,EAAA,CAAAE,SAAA,eAA0C;IAC1CF,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAI,MAAA,UAAE;IACvBJ,EADuB,CAAAG,YAAA,EAAM,EACvB;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAE,SAAA,eAA0C;IAC1CF,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAI,MAAA,UAAE;IACvBJ,EADuB,CAAAG,YAAA,EAAM,EACvB;IACNH,EAAA,CAAAC,cAAA,eAAkC;IAChCD,EAAA,CAAAE,SAAA,eAA0C;IAC1CF,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAI,MAAA,UAAE;IACvBJ,EADuB,CAAAG,YAAA,EAAM,EACvB;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAE,SAAA,eAA0C;IAC1CF,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAI,MAAA,UAAE;IACvBJ,EADuB,CAAAG,YAAA,EAAM,EACvB;IACNH,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAI,MAAA,WAAG;IAC7CJ,EAD6C,CAAAG,YAAA,EAAM,EAC7C;IAEJH,EADF,CAAAC,cAAA,eAAwB,eACW;IAC/BD,EAAA,CAAAI,MAAA,kBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAkC,eACb;IAAAD,EAAA,CAAAI,MAAA,IAAwB;IAC7CJ,EAD6C,CAAAG,YAAA,EAAM,EAC7C;IAEJH,EADF,CAAAC,cAAA,eAA0B,eACL;IAAAD,EAAA,CAAAI,MAAA,IAAwB;IAC7CJ,EAD6C,CAAAG,YAAA,EAAM,EAC7C;IAEJH,EADF,CAAAC,cAAA,eAAkC,eACb;IAAAD,EAAA,CAAAI,MAAA,IAAwB;IAC7CJ,EAD6C,CAAAG,YAAA,EAAM,EAC7C;IAEJH,EADF,CAAAC,cAAA,eAA0B,eACL;IAAAD,EAAA,CAAAI,MAAA,IAAwB;IAC7CJ,EAD6C,CAAAG,YAAA,EAAM,EAC7C;IAEJH,EADF,CAAAC,cAAA,eAAkC,eACb;IAAAD,EAAA,CAAAI,MAAA,IAAwB;IAC7CJ,EAD6C,CAAAG,YAAA,EAAM,EAC7C;IAEJH,EADF,CAAAC,cAAA,eAA0B,gBACL;IAAAD,EAAA,CAAAI,MAAA,KAAwB;IAG/CJ,EAH+C,CAAAG,YAAA,EAAM,EAC7C,EAEF;IAEJH,EADF,CAAAC,cAAA,gBAAwB,gBACW;IAC/BD,EAAA,CAAAI,MAAA,0BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAkC;IAAAD,EAAA,CAAAI,MAAA,KAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC7DH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAI,MAAA,KAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACrDH,EAAA,CAAAC,cAAA,gBAAkC;IAAAD,EAAA,CAAAI,MAAA,KAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC7DH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAI,MAAA,KAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACrDH,EAAA,CAAAC,cAAA,gBAAkC;IAAAD,EAAA,CAAAI,MAAA,KAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC7DH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAI,MAAA,KAAqB;IAKvDJ,EALuD,CAAAG,YAAA,EAAM,EAEjD,EACF,EACF,EACF;IAENH,EAAA,CAAAE,SAAA,gBAA6B;IAKvBF,EAHN,CAAAC,cAAA,eAA2B,eACF,eACI,eACE;IACvBD,EAAA,CAAAI,MAAA,0BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAAE,SAAA,mBAAsD;IAE1DF,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,gBAAmB,gBACO,gBACW;IAC/BD,EAAA,CAAAI,MAAA,yBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAkC;IAChCD,EAAA,CAAAE,SAAA,gBAA0C;IAC1CF,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAI,MAAA,KAA+B;IACpDJ,EADoD,CAAAG,YAAA,EAAM,EACpD;IACNH,EAAA,CAAAC,cAAA,gBAA0B;IACxBD,EAAA,CAAAE,SAAA,gBAA0C;IAC1CF,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAI,MAAA,KAA+B;IACpDJ,EADoD,CAAAG,YAAA,EAAM,EACpD;IACNH,EAAA,CAAAC,cAAA,gBAAkC;IAChCD,EAAA,CAAAE,SAAA,gBAA0C;IAC1CF,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAI,MAAA,KAA+B;IACpDJ,EADoD,CAAAG,YAAA,EAAM,EACpD;IACNH,EAAA,CAAAC,cAAA,gBAA0B;IACxBD,EAAA,CAAAE,SAAA,gBAA0C;IAC1CF,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAI,MAAA,KAA+B;IAGtDJ,EAHsD,CAAAG,YAAA,EAAM,EACpD,EAEF;IAEJH,EADF,CAAAC,cAAA,gBAAyB,gBACU;IAC/BD,EAAA,CAAAI,MAAA,aACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAkC;IAAAD,EAAA,CAAAI,MAAA,KAA8B;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACtEH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAI,MAAA,KAA8B;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IAC9DH,EAAA,CAAAC,cAAA,gBAAkC;IAAAD,EAAA,CAAAI,MAAA,KAA8B;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACtEH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAI,MAAA,KAA8B;IAKhEJ,EALgE,CAAAG,YAAA,EAAM,EAE1D,EACF,EACF,EACF;IAKAH,EAHN,CAAAC,cAAA,eAA2B,eACF,eACI,eACE;IACvBD,EAAA,CAAAI,MAAA,mBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAA+B;IAC7BD,EAAA,CAAAE,SAAA,mBAAyD;IAE7DF,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,gBAAmB,gBACO,gBACW;IAC/BD,EAAA,CAAAI,MAAA,0BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAc,UAAA,MAAAC,qCAAA,kBAA4J;IAK9Jf,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,gBAAyB,gBACU;IAC/BD,EAAA,CAAAI,MAAA,mBACF;IAAAJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAc,UAAA,MAAAE,qCAAA,kBAA4J;IAKpKhB,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;IAENH,EAAA,CAAAE,SAAA,gBAA6B;IAmM/BF,EAAA,CAAAG,YAAA,EAAM;;;;IA3asCH,EAAA,CAAAO,SAAA,IAAkC;IAAlCP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAO,qBAAA,kBAAAP,MAAA,CAAAO,qBAAA,CAAAC,OAAA,CAAkC;IAE1ClB,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAO,qBAAA,kBAAAP,MAAA,CAAAO,qBAAA,CAAAE,OAAA,CAAkC;IAC1BnB,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAO,qBAAA,kBAAAP,MAAA,CAAAO,qBAAA,CAAAG,MAAA,CAAiC;IACzCpB,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAO,qBAAA,kBAAAP,MAAA,CAAAO,qBAAA,CAAAI,QAAA,CAAmC;IAC3BrB,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAO,qBAAA,kBAAAP,MAAA,CAAAO,qBAAA,CAAAK,KAAA,CAAgC;IA+G7CtB,EAAA,CAAAO,SAAA,IAAwB;IAAxBP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAa,iBAAA,IAAwB;IAGxBvB,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAa,iBAAA,IAAwB;IAGxBvB,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAa,iBAAA,IAAwB;IAGxBvB,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAa,iBAAA,IAAwB;IAGxBvB,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAa,iBAAA,IAAwB;IAGxBvB,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAa,iBAAA,IAAwB;IAQXvB,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAc,cAAA,IAAqB;IAC7BxB,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAc,cAAA,IAAqB;IACbxB,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAc,cAAA,IAAqB;IAC7BxB,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAc,cAAA,IAAqB;IACbxB,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAc,cAAA,IAAqB;IAC7BxB,EAAA,CAAAO,SAAA,GAAqB;IAArBP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAc,cAAA,IAAqB;IA0B1BxB,EAAA,CAAAO,SAAA,IAA+B;IAA/BP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAe,wBAAA,IAA+B;IAI/BzB,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAe,wBAAA,IAA+B;IAI/BzB,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAe,wBAAA,IAA+B;IAI/BzB,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAe,wBAAA,IAA+B;IAQlBzB,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAgB,uBAAA,IAA8B;IACtC1B,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAgB,uBAAA,IAA8B;IACtB1B,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAgB,uBAAA,IAA8B;IACtC1B,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAAS,iBAAA,CAAAC,MAAA,CAAAgB,uBAAA,IAA8B;IAsBR1B,EAAA,CAAAO,SAAA,IAAoC;IAApCP,EAAA,CAAAK,UAAA,YAAAK,MAAA,CAAAC,+BAAA,CAAoC;IAUpCX,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAK,UAAA,YAAAK,MAAA,CAAAiB,+BAAA,CAAoC;;;ADtQ9F,WAAaC,cAAc;EAArB,MAAOA,cAAc;IA4BzBC,YACUC,cAA8B,EAC9BC,gBAAkC;MADlC,KAAAD,cAAc,GAAdA,cAAc;MACd,KAAAC,gBAAgB,GAAhBA,gBAAgB;MA7BjB,KAAAC,cAAc,GAAW,EAAE;MAC5B,KAAAC,IAAI,GAAG,IAAIlC,OAAO,EAAE;MAC5B,KAAAmC,QAAQ,GAAY,KAAK;MAEzB,KAAAjB,qBAAqB,GAA0B,EAA2B;MAI1E,KAAAS,uBAAuB,GAAa,EAAE;MACtC,KAAAD,wBAAwB,GAAa,EAAE;MAGvC,KAAAF,iBAAiB,GAAa,EAAE;MAChC,KAAAC,cAAc,GAAa,EAAE;MAK7B,KAAAW,sBAAsB,GAA6B,EAAE;MACrD,KAAAxB,+BAA+B,GAAa,EAAE;MAC9C,KAAAgB,+BAA+B,GAAa,EAAE;MAK9C,KAAAS,SAAS,GAAY,IAAI;IAMzB;IAEAC,QAAQA,CAAA;MACN,IAAI,CAACJ,IAAI,CAACK,IAAI,GAAG,IAAI,CAACR,cAAc,CAACS,UAAU,CAACC,SAAS,CAACC,GAAG,IAAG;QAC9D,IAAI,CAACP,QAAQ,GAAGO,GAAG,CAACC,MAAM;MAC5B,CAAC,CAAC;IACJ;IAEAC,eAAeA,CAAA;MACbC,UAAU,CAAC,MAAK;QACd,IAAI,CAACR,SAAS,GAAG,KAAK;QACtB,IAAI,CAACS,0BAA0B,EAAE;QACjC,IAAI,CAACC,yBAAyB,EAAE;QAChC,IAAI,CAACC,yBAAyB,EAAE;QAChC,IAAI,CAACC,mBAAmB,EAAE;QAC1B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACF,CAAC,EAAE,IAAI,CAAC;IACV;IAEAC,WAAWA,CAAA;MACT,IAAI,CAAChB,IAAI,CAACiB,WAAW,EAAE;IACzB;IAEML,0BAA0BA,CAAA;MAAA,IAAAM,KAAA;MAAA,OAAAC,iBAAA;QAC9B,IAAIC,KAAK,SAASF,KAAI,CAACpB,gBAAgB,CAACuB,uBAAuB,EAAE;QACjEC,OAAO,CAACC,GAAG,CAACH,KAAK,CAAC;QAClBF,KAAI,CAAClC,qBAAqB,GAAGoC,KAA8B;QAC3DF,KAAI,CAACM,cAAc,CACjB,UAAU,EACV,4BAA4B,EAC5B,CAAC3D,MAAM,CAAC4D,QAAQ,EAAE5D,MAAM,CAAC6D,OAAO,EAAE7D,MAAM,CAAC8D,OAAO,EAAE9D,MAAM,CAAC+D,QAAQ,CAAC,EAClE,CAACV,KAAI,CAAClC,qBAAsB,CAACC,OAAO,EAAEiC,KAAI,CAAClC,qBAAsB,CAACE,OAAO,EAAEgC,KAAI,CAAClC,qBAAsB,CAACG,MAAM,EAAE+B,KAAI,CAAClC,qBAAsB,CAACI,QAAQ,CAAC,CACrJ,CAACyC,IAAI,CAACrB,GAAG,IAAG,CACb,CAAC,CAAC;QACF,OAAOY,KAAK;MAAA;IACd;IAEMP,yBAAyBA,CAAA;MAAA,IAAAiB,MAAA;MAAA,OAAAX,iBAAA;QAC7B,IAAIC,KAAK,SAASU,MAAI,CAAChC,gBAAgB,CAACiC,qCAAqC,EAAE;QAC/ET,OAAO,CAACC,GAAG,CAACH,KAAK,CAAC;QAClBU,MAAI,CAACE,oBAAoB,GAAGZ,KAAK;QACjC,KAAK,IAAIa,KAAK,IAAIH,MAAI,CAACE,oBAA2B,EAAE;UAClDF,MAAI,CAACxC,iBAAiB,CAAC4C,IAAI,CAACD,KAAK,CAACE,YAAY,CAAC;UAC/CL,MAAI,CAACvC,cAAc,CAAC2C,IAAI,CAACD,KAAK,CAACG,UAAU,CAAC;QAC5C;QAEAN,MAAI,CAACO,SAAS,CACZ,aAAa,EACb,CAACzE,KAAK,CAAC0E,EAAE,EAAE1E,KAAK,CAAC2E,EAAE,EAAE3E,KAAK,CAAC4E,EAAE,EAAE5E,KAAK,CAAC6E,EAAE,EAAE7E,KAAK,CAAC8E,EAAE,EAAE9E,KAAK,CAAC+E,EAAE,CAAC,EAC5Db,MAAI,CAACxC,iBAAiB,EACtBwC,MAAI,CAACvC,cAAc,CACpB,CAACsC,IAAI,CAACrB,GAAG,IAAG,CACb,CAAC,CAAC;QACF,OAAOY,KAAK;MAAA;IACd;IAEML,mBAAmBA,CAAA;MAAA,IAAA6B,MAAA;MAAA,OAAAzB,iBAAA;QACvB,IAAIC,KAAK,SAASwB,MAAI,CAAC9C,gBAAgB,CAAC+C,eAAe,EAAE;QACzDvB,OAAO,CAACC,GAAG,CAACH,KAAK,CAAC;QAClBwB,MAAI,CAAC1C,sBAAsB,GAAGkB,KAAY;QAC1C,KAAK,IAAI0B,IAAI,IAAIF,MAAI,CAAC1C,sBAAsB,EAAE;UAC5C0C,MAAI,CAAClE,+BAA+B,CAACwD,IAAI,CAACY,IAAI,CAACC,YAAY,CAACC,QAAQ,EAAE,CAAC;UACvEJ,MAAI,CAAClD,+BAA+B,CAACwC,IAAI,CAACY,IAAI,CAACG,YAAY,CAAC;QAC9D;QACAL,MAAI,CAACM,eAAe,CAClB,eAAe,EACfN,MAAI,CAAClE,+BAA+B,EACpCkE,MAAI,CAAClD,+BAA+B,CACrC,CAACmC,IAAI,CAACrB,GAAG,IAAG,CACb,CAAC,CAAC;QACF,OAAOY,KAAK;MAAA;IACd;IAEMN,yBAAyBA,CAAA;MAAA,IAAAqC,MAAA;MAAA,OAAAhC,iBAAA;QAC7B,IAAIC,KAAK,SAAS+B,MAAI,CAACrD,gBAAgB,CAACsD,sBAAsB,EAAE;QAChE9B,OAAO,CAACC,GAAG,CAACH,KAAK,CAAC;QAClB+B,MAAI,CAACE,mBAAmB,GAAGjC,KAAK;QAChCkC,MAAM,CAACC,OAAO,CAACJ,MAAI,CAACE,mBAAoB,CAAC,CAACG,OAAO,CAAEC,KAAU,IAAI;UAC/DN,MAAI,CAAC3D,wBAAwB,EAAE0C,IAAI,CAACuB,KAAK,CAAC,CAAC,CAAC,CAAC;UAC7CN,MAAI,CAAC1D,uBAAuB,EAAEyC,IAAI,CAACuB,KAAK,CAAC,CAAC,CAAC,CAAC;UAC5C;QACF,CAAC,CAAC;QACFN,MAAI,CAACD,eAAe,CAClB,YAAY,EACZC,MAAI,CAAC3D,wBAAyB,EAC9B2D,MAAI,CAAC1D,uBAAwB,CAC9B,CAACoC,IAAI,CAACrB,GAAG,IAAG,CACb,CAAC,CAAC;QACF,OAAOY,KAAK;MAAA;IACd;IAEAsC,WAAWA,CAACC,SAAc,EAAEC,KAAa,EAAEC,SAAgB;MACzD,IAAIC,KAAK,EAAEC,MAAM;MACjB,MAAMC,UAAU,GAAGL,SAAS,CAACM,KAAK,GAAGN,SAAS,CAACO,IAAI;MACnD,MAAMC,WAAW,GAAGR,SAAS,CAACS,MAAM,GAAGT,SAAS,CAACU,GAAG;MACpD,IAAIR,SAAS,CAACD,KAAK,CAAC,KAAK,IAAI,IAAIE,KAAK,KAAKE,UAAU,IAAID,MAAM,KAAKI,WAAW,EAAE;QAC/EL,KAAK,GAAGE,UAAU;QAClBD,MAAM,GAAGI,WAAW;MACtB;MACA,OAAON,SAAS,CAACD,KAAK,CAAC;IACzB;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEApC,cAAcA,CAAC8C,IAAY,EAAEC,EAAU,EAAEC,MAAgB,EAAEC,IAAc;MAEvE,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAAA,CACD,CAAC;IACJ;IAEAC,UAAUA,CAACN,EAAU,EAAEC,MAAgB,EAAEC,IAAc;MACrD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA;IAGFvB,eAAeA,CAACqB,EAAU,EAAEC,MAAgB,EAAEC,IAAc;MAC1D,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA;QACA;QACA;QACA;QACA;QACA;MAAA,CACD,CAAC;IACJ;IAEAvC,SAASA,CAACkC,EAAU,EAAEC,MAAgB,EAAElF,iBAA2B,EAAEC,cAAwB;MAC3F,OAAO,IAAImF,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAAA,CACD,CAAC;IACJ;IAAC,QAAAE,CAAA,G;uBAtWUnF,cAAc,EAAA5B,EAAA,CAAAgH,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAlH,EAAA,CAAAgH,iBAAA,CAAAG,EAAA,CAAAC,gBAAA;IAAA;IAAA,QAAAC,EAAA,G;YAAdzF,cAAc;MAAA0F,SAAA;MAAAC,MAAA;QAAAvF,cAAA;MAAA;MAAAwF,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP3B7H,EAJA,CAAAc,UAAA,IAAAiH,6BAAA,iBAAuC,IAAAC,6BAAA,oBAIU;;;UAJ3ChI,EAAA,CAAAK,UAAA,SAAAyH,GAAA,CAAA1F,SAAA,CAAe;UAIfpC,EAAA,CAAAO,SAAA,EAAgB;UAAhBP,EAAA,CAAAK,UAAA,UAAAyH,GAAA,CAAA1F,SAAA,CAAgB;;;;;;;SDOTR,cAAc;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}