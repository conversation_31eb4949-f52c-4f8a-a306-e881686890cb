{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { TeacherApplicationDeactivateGuard } from 'src/app/core/guards/teacher-application.guard';\nimport { TopMenuContainerLayoutComponent } from 'src/app/shared/layout/top-menu-container-layout/top-menu-container-layout.component';\nimport { ContactInfoComponent } from './contact-info/contact-info.component';\nimport { EducationComponent } from './education/education.component';\nimport { ExperienceComponent } from './experience/experience.component';\nimport { GeneralInfoComponent } from './general-info/general-info.component';\nimport { ResumeComponent } from './resume/resume.component';\nimport { ReviewComponent } from './review/review.component';\nimport { StatusComponent } from './status/status.component';\nimport { TeacherApplicationComponent } from './teacher-application.component';\nimport { TeacherAvailabilityComponent } from './teacher-availability/teacher-availability.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: TopMenuContainerLayoutComponent,\n  children: [{\n    path: '',\n    component: TeacherApplicationComponent,\n    children: [{\n      path: 'info',\n      component: GeneralInfoComponent,\n      canDeactivate: [TeacherApplicationDeactivateGuard]\n    }, {\n      path: 'contact',\n      component: ContactInfoComponent\n    }, {\n      path: 'education',\n      component: EducationComponent,\n      canDeactivate: [TeacherApplicationDeactivateGuard]\n    }, {\n      path: 'experience',\n      component: ExperienceComponent,\n      canDeactivate: [TeacherApplicationDeactivateGuard]\n    }, {\n      path: 'resume',\n      component: ResumeComponent,\n      canDeactivate: [TeacherApplicationDeactivateGuard]\n    }, {\n      path: 'availability',\n      component: TeacherAvailabilityComponent\n    }, {\n      path: 'review',\n      component: ReviewComponent\n    }, {\n      path: 'status',\n      component: StatusComponent\n    }]\n  }]\n}];\n// const routes: Routes = [\n//     { path: 'info', component: GeneralInfoComponent, },\n//     { path: 'contact', component: ContactInfoComponent, },\n//     { path: 'education', component: EducationComponent, },\n//     { path: 'experience', component: ExperienceComponent, },\n//     { path: 'resume', component: ResumeComponent, },\n//     { path: 'availability', component: TeacherAvailabilityComponent, },\n//     { path: 'review', component: ReviewComponent, },\n//     { path: 'status', component: StatusComponent, },\n// ];\nexport class TeacherApplicationRoutingModule {\n  static #_ = this.ɵfac = function TeacherApplicationRoutingModule_Factory(t) {\n    return new (t || TeacherApplicationRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: TeacherApplicationRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(TeacherApplicationRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "TeacherApplicationDeactivateGuard", "TopMenuContainerLayoutComponent", "ContactInfoComponent", "EducationComponent", "ExperienceComponent", "GeneralInfoComponent", "ResumeComponent", "ReviewComponent", "StatusComponent", "TeacherApplicationComponent", "TeacherAvailabilityComponent", "routes", "path", "component", "children", "canDeactivate", "TeacherApplicationRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\teacher-application\\teacher-application-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { TeacherApplicationDeactivateGuard } from 'src/app/core/guards/teacher-application.guard';\r\nimport { TopMenuContainerLayoutComponent } from 'src/app/shared/layout/top-menu-container-layout/top-menu-container-layout.component';\r\nimport { ContactInfoComponent } from './contact-info/contact-info.component';\r\nimport { EducationComponent } from './education/education.component';\r\nimport { ExperienceComponent } from './experience/experience.component';\r\nimport { GeneralInfoComponent } from './general-info/general-info.component';\r\nimport { ResumeComponent } from './resume/resume.component';\r\nimport { ReviewComponent } from './review/review.component';\r\nimport { StatusComponent } from './status/status.component';\r\nimport { TeacherApplicationComponent } from './teacher-application.component';\r\nimport { TeacherAvailabilityComponent } from './teacher-availability/teacher-availability.component';\r\n\r\n\r\nconst routes: Routes = [\r\n    { path: '', component: TopMenuContainerLayoutComponent,\r\n    \r\n    children: [\r\n      {\r\n        path: '',\r\n        component: TeacherApplicationComponent,\r\n        \r\n    children: [\r\n      {\r\n        path: 'info',\r\n        component: GeneralInfoComponent,\r\n        canDeactivate: [TeacherApplicationDeactivateGuard],\r\n      },\r\n        { path: 'contact', component: ContactInfoComponent, },\r\n        { path: 'education', component: EducationComponent,\r\n        canDeactivate: [TeacherApplicationDeactivateGuard] },\r\n        { path: 'experience', component: ExperienceComponent,\r\n        canDeactivate: [TeacherApplicationDeactivateGuard], },\r\n        { path: 'resume', component: ResumeComponent,\r\n        canDeactivate: [TeacherApplicationDeactivateGuard] },\r\n        { path: 'availability', component: TeacherAvailabilityComponent, },\r\n        { path: 'review', component: ReviewComponent, },\r\n        { path: 'status', component: StatusComponent, },\r\n    ]\r\n      },\r\n    ]\r\n  },\r\n];\r\n// const routes: Routes = [\r\n//     { path: 'info', component: GeneralInfoComponent, },\r\n//     { path: 'contact', component: ContactInfoComponent, },\r\n//     { path: 'education', component: EducationComponent, },\r\n//     { path: 'experience', component: ExperienceComponent, },\r\n//     { path: 'resume', component: ResumeComponent, },\r\n//     { path: 'availability', component: TeacherAvailabilityComponent, },\r\n//     { path: 'review', component: ReviewComponent, },\r\n//     { path: 'status', component: StatusComponent, },\r\n// ];\r\n\r\n@NgModule({\r\n    imports: [RouterModule.forChild(routes)],\r\n    exports: [RouterModule]\r\n})\r\nexport class TeacherApplicationRoutingModule { }"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,iCAAiC,QAAQ,+CAA+C;AACjG,SAASC,+BAA+B,QAAQ,qFAAqF;AACrI,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,kBAAkB,QAAQ,iCAAiC;AACpE,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,2BAA2B,QAAQ,iCAAiC;AAC7E,SAASC,4BAA4B,QAAQ,uDAAuD;;;AAGpG,MAAMC,MAAM,GAAW,CACnB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEZ,+BAA+B;EAEtDa,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,EAAE;IACRC,SAAS,EAAEJ,2BAA2B;IAE1CK,QAAQ,EAAE,CACR;MACEF,IAAI,EAAE,MAAM;MACZC,SAAS,EAAER,oBAAoB;MAC/BU,aAAa,EAAE,CAACf,iCAAiC;KAClD,EACC;MAAEY,IAAI,EAAE,SAAS;MAAEC,SAAS,EAAEX;IAAoB,CAAG,EACrD;MAAEU,IAAI,EAAE,WAAW;MAAEC,SAAS,EAAEV,kBAAkB;MAClDY,aAAa,EAAE,CAACf,iCAAiC;IAAC,CAAE,EACpD;MAAEY,IAAI,EAAE,YAAY;MAAEC,SAAS,EAAET,mBAAmB;MACpDW,aAAa,EAAE,CAACf,iCAAiC;IAAC,CAAG,EACrD;MAAEY,IAAI,EAAE,QAAQ;MAAEC,SAAS,EAAEP,eAAe;MAC5CS,aAAa,EAAE,CAACf,iCAAiC;IAAC,CAAE,EACpD;MAAEY,IAAI,EAAE,cAAc;MAAEC,SAAS,EAAEH;IAA4B,CAAG,EAClE;MAAEE,IAAI,EAAE,QAAQ;MAAEC,SAAS,EAAEN;IAAe,CAAG,EAC/C;MAAEK,IAAI,EAAE,QAAQ;MAAEC,SAAS,EAAEL;IAAe,CAAG;GAEhD;CAEJ,CACF;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA,OAAM,MAAOQ,+BAA+B;EAAA,QAAAC,CAAA,G;qBAA/BD,+BAA+B;EAAA;EAAA,QAAAE,EAAA,G;UAA/BF;EAA+B;EAAA,QAAAG,EAAA,G;cAH9BpB,YAAY,CAACqB,QAAQ,CAACT,MAAM,CAAC,EAC7BZ,YAAY;EAAA;;;2EAEbiB,+BAA+B;IAAAK,OAAA,GAAAC,EAAA,CAAAvB,YAAA;IAAAwB,OAAA,GAF9BxB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}