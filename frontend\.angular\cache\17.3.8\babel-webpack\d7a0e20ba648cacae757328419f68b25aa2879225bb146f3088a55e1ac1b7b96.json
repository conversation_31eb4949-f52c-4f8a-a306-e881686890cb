{"ast": null, "code": "import { UntypedFormArray, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { SubSink } from 'subsink';\nimport { Observable } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nimport * as i2 from \"src/app/core/services/teacher-application.service\";\nimport * as i3 from \"src/app/core/services/toast.service\";\nimport * as i4 from \"src/app/core/services/auth.service\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"../../../shared/upload-files/upload-files.component\";\nimport * as i10 from \"primeng/button\";\nimport * as i11 from \"primeng/ripple\";\nconst _c0 = a0 => ({\n  \"more-height-div\": a0\n});\nfunction EducationComponent_ng_container_2_div_5_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function EducationComponent_ng_container_2_div_5_div_6_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.addEducation());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EducationComponent_ng_container_2_div_5_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function EducationComponent_ng_container_2_div_5_div_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const i_r4 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeEducation(i_r4));\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EducationComponent_ng_container_2_div_5_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtext(1, \"* Degrees / Diplomas is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EducationComponent_ng_container_2_div_5_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtext(1, \"Institute is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EducationComponent_ng_container_2_div_5_div_18_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32, 1)(3, \"div\", 33)(4, \"div\");\n    i0.ɵɵtext(5, \"Add files\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"img\", 34);\n    i0.ɵɵlistener(\"click\", function EducationComponent_ng_container_2_div_5_div_18_div_4_Template_img_click_6_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const i_r4 = i0.ɵɵnextContext(2).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.hideFileUpload(i_r4));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\");\n    i0.ɵɵtext(8, \" Upload Degree/Diploma \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"app-upload-files\", 35);\n    i0.ɵɵlistener(\"onUploadFinished\", function EducationComponent_ng_container_2_div_5_div_18_div_4_Template_app_upload_files_onUploadFinished_9_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const i_r4 = i0.ɵɵnextContext(2).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.uploadFinished($event, i_r4));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"dndUI\", true)(\"multiple\", false)(\"isTeacherApplication\", true)(\"postUrl\", ctx_r1.UPLOAD_TEACHER_APPLICATION_FILES_URL);\n  }\n}\nfunction EducationComponent_ng_container_2_div_5_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function EducationComponent_ng_container_2_div_5_div_18_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const i_r4 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openFileUploadDialog(i_r4));\n    });\n    i0.ɵɵtext(2, \"Upload File \");\n    i0.ɵɵelementStart(3, \"img\", 30);\n    i0.ɵɵlistener(\"click\", function EducationComponent_ng_container_2_div_5_div_18_Template_img_click_3_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const i_r4 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.hideFileUpload(i_r4));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(4, EducationComponent_ng_container_2_div_5_div_18_div_4_Template, 10, 4, \"div\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r4 = i0.ɵɵnextContext().index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.educationfilePaths(i_r4).controls.length === 0 && ctx_r1.showFileUpload[i_r4]);\n  }\n}\nfunction EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_2_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const i_r4 = i0.ɵɵnextContext(3).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openFileUploadDialog(i_r4));\n    });\n    i0.ɵɵtext(1, \"Upload File \");\n    i0.ɵɵelementStart(2, \"img\", 30);\n    i0.ɵɵlistener(\"click\", function EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_2_Template_img_click_2_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const i_r4 = i0.ɵɵnextContext(3).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.hideFileUpload(i_r4));\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵelement(1, \"img\", 40);\n    i0.ɵɵelementStart(2, \"span\", 41);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"img\", 42);\n    i0.ɵɵlistener(\"click\", function EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_3_Template_img_click_4_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const skillIndex_r9 = i0.ɵɵnextContext().index;\n      const i_r4 = i0.ɵɵnextContext(2).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeEducationFileField(i_r4, skillIndex_r9, ctx_r1.getFileName(ctx_r1.form.value.education[i_r4].fileUploads[skillIndex_r9].filePath)));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const skillIndex_r9 = i0.ɵɵnextContext().index;\n    const i_r4 = i0.ɵɵnextContext(2).index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getFileName(ctx_r1.form.value.education[i_r4].fileUploads[skillIndex_r9].filePath));\n  }\n}\nfunction EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32, 1)(3, \"div\", 33)(4, \"div\");\n    i0.ɵɵtext(5, \"Add files\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"img\", 34);\n    i0.ɵɵlistener(\"click\", function EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_4_Template_img_click_6_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const skillIndex_r9 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.hideFileUpload(skillIndex_r9));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\");\n    i0.ɵɵtext(8, \" Upload Degree/Diploma \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"app-upload-files\", 43);\n    i0.ɵɵlistener(\"onUploadFinished\", function EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_4_Template_app_upload_files_onUploadFinished_9_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const skillIndex_r9 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.uploadFinished($event, skillIndex_r9));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"dndUI\", true)(\"multiple\", false)(\"postUrl\", ctx_r1.UPLOAD_TEACHER_APPLICATION_FILES_URL);\n  }\n}\nfunction EducationComponent_ng_container_2_div_5_ng_template_19_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 15);\n    i0.ɵɵtemplate(2, EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_2_Template, 3, 0, \"div\", 37)(3, EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_3_Template, 5, 1, \"div\", 38)(4, EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_4_Template, 10, 3, \"div\", 31);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const skillIndex_r9 = ctx.index;\n    const i_r4 = i0.ɵɵnextContext(2).index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroupName\", skillIndex_r9);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.form.value.education[i_r4].fileUploads.length === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.form.value.education[i_r4].fileUploads[skillIndex_r9].length !== 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showFileUpload[skillIndex_r9]);\n  }\n}\nfunction EducationComponent_ng_container_2_div_5_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, EducationComponent_ng_container_2_div_5_ng_template_19_div_0_Template, 5, 4, \"div\", 36);\n  }\n  if (rf & 2) {\n    const i_r4 = i0.ɵɵnextContext().index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.educationfilePaths(i_r4).controls);\n  }\n}\nfunction EducationComponent_ng_container_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵelementContainerStart(1, 15);\n    i0.ɵɵelementStart(2, \"div\", 16)(3, \"div\", 17)(4, \"div\", 18);\n    i0.ɵɵtext(5, \"Degrees / Diplomas * \");\n    i0.ɵɵtemplate(6, EducationComponent_ng_container_2_div_5_div_6_Template, 2, 0, \"div\", 19)(7, EducationComponent_ng_container_2_div_5_div_7_Template, 2, 0, \"div\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"input\", 20);\n    i0.ɵɵtemplate(9, EducationComponent_ng_container_2_div_5_div_9_Template, 2, 0, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 17)(11, \"div\", 18);\n    i0.ɵɵtext(12, \"Institute *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 22);\n    i0.ɵɵtemplate(14, EducationComponent_ng_container_2_div_5_div_14_Template, 2, 0, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 23)(16, \"div\", 18);\n    i0.ɵɵtext(17, \"Upload Degree/Diploma\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, EducationComponent_ng_container_2_div_5_div_18_Template, 5, 1, \"div\", 24)(19, EducationComponent_ng_container_2_div_5_ng_template_19_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r4 = ctx.index;\n    const tplwithFilePaths_r11 = i0.ɵɵreference(20);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroupName\", i_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c0, ctx_r1.educationfilePaths(i_r4).length > 0));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", i_r4 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r4 > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(i_r4));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(i_r4));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.educationfilePaths(i_r4).controls.length === 0)(\"ngIfElse\", tplwithFilePaths_r11);\n  }\n}\nfunction EducationComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"form\", 10)(2, \"div\", 11)(3, \"div\");\n    i0.ɵɵelementContainerStart(4, 12);\n    i0.ɵɵtemplate(5, EducationComponent_ng_container_2_div_5_Template, 21, 10, \"div\", 13);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.form);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.education.controls);\n  }\n}\nexport class EducationComponent {\n  constructor(generalService, teacherService, toastService, authService, confirmationService, router, fb, location) {\n    this.generalService = generalService;\n    this.teacherService = teacherService;\n    this.toastService = toastService;\n    this.authService = authService;\n    this.confirmationService = confirmationService;\n    this.router = router;\n    this.fb = fb;\n    this.location = location;\n    this.subs = new SubSink();\n    this.form = new UntypedFormGroup({});\n    this.isTablet = false;\n    this.teacher = this.teacherService.dummyTeacher;\n    this.tryToSave = false;\n    this.isLoading = false;\n    this.speakLanguageCounter = 0;\n    this.error = false;\n    this.UPLOAD_TEACHER_APPLICATION_FILES_URL = '/LMS/UploadTeacherApplicationFiles';\n    this.teacherEducation = [];\n    this.showFileUpload = [];\n    this.formChanged = false;\n  }\n  ngOnInit() {\n    this.teacherService.setCurrentStepIndex(1);\n    this.subs.add(this.teacherService.submitOnMenuClickListener.subscribe(res => {\n      if (res) {\n        this.onSubmit();\n      }\n    }));\n    this.subs.add(this.teacherService.getTeacherApplicationStep2().subscribe(res => {\n      if (res) {\n        this.updateStepFormValues(res);\n      }\n      if (res.education === null) {\n        this.addEducation({});\n      }\n      this.initFormChangedListener();\n    }));\n    this.subs.add(this.generalService.deviceKind.subscribe(res => {\n      this.isTablet = res.is1024;\n    }));\n    this.form = new UntypedFormGroup({\n      education: new UntypedFormArray([])\n    });\n    this.teacherEducation = this.teacher.education;\n    this.teacherEducation.forEach(element => {\n      this.showFileUpload.push(false);\n      this.education.push(this.fb.group(element));\n    });\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n    this.teacherService.setSubmitOnMenuClickListener(false);\n  }\n  get education() {\n    return this.form.get('education');\n  }\n  /**\n  Update the form values for step 2 of the teacher application\n  @param {TeacherApplicationStep2} teacherApplicationStep2 - The data for step 2 of the teacher application\n  */\n  updateStepFormValues(teacherApplicationStep2) {\n    if (!this.generalService.isNullishObject(teacherApplicationStep2.education)) {\n      teacherApplicationStep2.education.forEach((element, index) => {\n        this.addEducation(element);\n        element.fileUploads.forEach(fileUploadElement => {\n          this.addEducationFileField(index, fileUploadElement.filePath);\n        });\n      });\n      console.log(teacherApplicationStep2.education);\n    }\n  }\n  /**\n  Add an education to the form\n  @param {TeacherApplicationEducation} element - The education to add to the form\n  */\n  addEducation(element) {\n    const group = new UntypedFormGroup({\n      name: new UntypedFormControl(element ? element.name : '', Validators.required),\n      institute: new UntypedFormControl(element ? element.institute : '', Validators.required),\n      fileUploads: this.fb.array([])\n    });\n    this.education.push(group);\n  }\n  /**\n  Remove an education from the form\n  @param {number} index - The index of the education to remove\n  */\n  removeEducation(index) {\n    this.education.removeAt(index);\n  }\n  initFormChangedListener() {\n    this.subs.add(this.form.valueChanges.subscribe(val => {\n      console.log(val);\n      this.formChanged = true;\n    }));\n  }\n  /**\n  Submit the form data\n  */\n  onSubmit() {\n    this.tryToSave = true;\n    if (!this.form.valid) {\n      this.error = true;\n      return;\n    }\n    this.formChanged = false;\n    this.subs.add(this.teacherService.updateTeacherApplicationStep2(this.form.value).subscribe(res => {\n      if (res) {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Your Education info were updated.'\n        });\n      }\n      this.router.navigateByUrl('/teacher/experience', {\n        replaceUrl: true\n      });\n    }));\n  }\n  back() {\n    this.location.back();\n  }\n  ifFieldValid(i) {\n    this.teacherService.setStepValid(1, this.form, 'teacher-education-route');\n    return this.tryToSave && this.form.get('education').controls[i].invalid;\n  }\n  goBack() {\n    this.router.navigateByUrl('/teacher/info', {\n      replaceUrl: true\n    });\n  }\n  /**\n  * Handles the completion of a file upload\n  *\n  * @param {filePath: { dbPath: string; }} event - Event object containing information about the uploaded file\n  * @param {number} i - Index of the file in the array of files\n  *\n  */\n  uploadFinished(event, i) {\n    this.hideFileUpload(i);\n    this.addEducationFileField(i, event.filePath.dbPath);\n  }\n  /**\n   * Opens the file upload dialog\n   *\n   * @param {number} fileIndex - Index of the file in the array of files\n   *\n   */\n  openFileUploadDialog(fileIndex) {\n    this.showFileUpload[fileIndex] = true;\n    document.getElementById('blur_bg')?.classList.add('yes-visibility');\n  }\n  /**\n  Hides the file upload for a specific index.\n  @param {number} fileIndex - The index of the file upload to be hidden.\n  */\n  hideFileUpload(fileIndex) {\n    this.showFileUpload[fileIndex] = false;\n    document.getElementById('blur_bg')?.classList.remove('yes-visibility');\n  }\n  /**\n  Get the file name from a file path\n  @param {string} fileName - The file path\n  @returns {string} The file name\n  */\n  getFileName(fileName) {\n    return this.generalService.getFileName2(fileName);\n  }\n  removeEmployee(empIndex) {\n    this.education.removeAt(empIndex);\n  }\n  educationfilePaths(empIndex) {\n    return this.education.at(empIndex).get('fileUploads');\n  }\n  newFilePathFormGroup(name) {\n    return this.fb.group({\n      filePath: name\n    });\n  }\n  addEducationFileField(empIndex, name) {\n    this.educationfilePaths(empIndex).push(this.newFilePathFormGroup(name));\n  }\n  removeEducationFileField(empIndex, skillIndex, filename) {\n    this.educationfilePaths(empIndex).removeAt(skillIndex);\n    this.subs.add(this.teacherService.deleteTeacherApplicationFiles(filename).subscribe(result => {\n      console.log(result);\n    }));\n  }\n  canDeactivate() {\n    if (this.formChanged) {\n      return new Observable(observer => {\n        this.confirmationService.confirm({\n          header: '',\n          key: 'stepLeaveConfirmation',\n          message: 'Are you sure you want to leave this page? Your changes will be lost.',\n          accept: () => {\n            observer.next(true);\n            observer.complete();\n          },\n          reject: () => {\n            observer.next(false);\n            observer.complete();\n          }\n        });\n      });\n    } else {\n      return true;\n    }\n  }\n  static #_ = this.ɵfac = function EducationComponent_Factory(t) {\n    return new (t || EducationComponent)(i0.ɵɵdirectiveInject(i1.GeneralService), i0.ɵɵdirectiveInject(i2.TeacherApplicationService), i0.ɵɵdirectiveInject(i3.ToastService), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i5.ConfirmationService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.UntypedFormBuilder), i0.ɵɵdirectiveInject(i8.Location));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: EducationComponent,\n    selectors: [[\"app-education\"]],\n    decls: 10,\n    vars: 1,\n    consts: [[\"tplwithFilePaths\", \"\"], [\"fileUpload\", \"\", \"taskFiles\", \"\"], [1, \"md:mt-3\"], [1, \"profile-info\"], [4, \"ngIf\"], [1, \"btns\", \"md:ml-5\", \"mt-4\"], [\"pRipple\", \"\", 1, \"rounded-blue-button\", \"transparent\", 3, \"click\"], [\"src\", \"/assets/icons/arrow-left-blue.svg\"], [\"pRipple\", \"\", 1, \"rounded-blue-button\", 3, \"click\"], [\"src\", \"/assets/icons/arrow-right.svg\"], [3, \"formGroup\"], [1, \"profile-info-section\"], [\"formArrayName\", \"education\"], [\"style\", \"position: relative;\", 4, \"ngFor\", \"ngForOf\"], [2, \"position\", \"relative\"], [3, \"formGroupName\"], [1, \"input-fields\", 3, \"ngClass\"], [1, \"input-field\", \"col--1of3\"], [1, \"input-element-title\"], [\"class\", \"hvr-grow\", 4, \"ngIf\"], [\"formControlName\", \"name\", \"type\", \"text\", \"placeholder\", \"e.g. Bachelor of Spanish\", 1, \"input-element\"], [\"class\", \"input-error\", 4, \"ngIf\"], [\"formControlName\", \"institute\", \"type\", \"text\", \"placeholder\", \"e.g. Aristotle University\", 1, \"input-element\"], [\"formArrayName\", \"fileUploads\", 1, \"input-field\", \"col--1of3\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"hvr-grow\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-plus\", 1, \"p-button-raised\", \"p-button-rounded\", \"plus-btn-circle\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-minus\", 1, \"p-button-raised\", \"p-button-rounded\", \"minus-btn-circle\", 3, \"click\"], [1, \"input-error\"], [1, \"rounded-blue-button\", \"upload-button\", \"transparent\", 3, \"click\"], [\"src\", \"/assets/icons/cloud-icon.svg\", \"alt\", \"cloud-icon\", 3, \"click\"], [\"class\", \"modal\", 4, \"ngIf\"], [1, \"modal\"], [1, \"popup-title\"], [\"src\", \"/assets/icons/close.png\", 1, \"close-img\", \"close-img-abs\", \"hvr-glow\", 3, \"click\"], [\"title\", \"Degree/Diploma Paper\", 3, \"onUploadFinished\", \"dndUI\", \"multiple\", \"isTeacherApplication\", \"postUrl\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"rounded-blue-button upload-button transparent\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"file-path\", 4, \"ngIf\"], [1, \"file-path\"], [\"src\", \"/assets/icons/education-degree-white.svg\", 1, \"close-img\", \"close-img-abs\", \"hvr-glow\"], [1, \"file-path-text\"], [\"src\", \"/assets/icons/trash-white.svg\", \"alt\", \"trash-white.svg\", 1, \"delete-file-icon-img\", \"hvr-glow\", 3, \"click\"], [\"title\", \"Degree/Diploma Paper\", 3, \"onUploadFinished\", \"dndUI\", \"multiple\", \"postUrl\"]],\n    template: function EducationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"div\", 2);\n        i0.ɵɵelementStart(1, \"div\", 3);\n        i0.ɵɵtemplate(2, EducationComponent_ng_container_2_Template, 6, 2, \"ng-container\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 5)(4, \"button\", 6);\n        i0.ɵɵlistener(\"click\", function EducationComponent_Template_button_click_4_listener() {\n          return ctx.goBack();\n        });\n        i0.ɵɵelement(5, \"img\", 7);\n        i0.ɵɵtext(6, \" Back\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"button\", 8);\n        i0.ɵɵlistener(\"click\", function EducationComponent_Template_button_click_7_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵtext(8, \"Next \");\n        i0.ɵɵelement(9, \"img\", 9);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n      }\n    },\n    dependencies: [i8.NgClass, i8.NgForOf, i8.NgIf, i7.ɵNgNoValidate, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgControlStatusGroup, i7.FormGroupDirective, i7.FormControlName, i7.FormGroupName, i7.FormArrayName, i9.UploadFilesComponent, i10.ButtonDirective, i11.Ripple],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.modal[_ngcontent-%COMP%] {\\n  min-width: 50vw;\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown:not(.p-disabled):hover {\\n  border: 2px solid rgb(228, 185, 84);\\n}\\n\\n  .no-label > .p-button-label {\\n  display: none;\\n}\\n\\ninput[type=file][_ngcontent-%COMP%] {\\n  visibility: hidden;\\n  width: 10px;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%] {\\n  position: relative;\\n  padding: 10px;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%]::-webkit-calendar-picker-indicator {\\n  color: transparent;\\n  background: none;\\n  z-index: 1;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%]:before {\\n  color: transparent;\\n  background: none;\\n  display: block;\\n  font-family: \\\"FontAwesome\\\";\\n  content: \\\"\\\\f073\\\";\\n  \\n\\n  width: 20px;\\n  height: 25px;\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  color: #999;\\n}\\n\\n.image-preview[_ngcontent-%COMP%] {\\n  height: 10rem;\\n  margin: 1rem 0;\\n  border-radius: 50%;\\n  border: 3px solid var(--main-color);\\n}\\n\\n.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 100%;\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-header {\\n  padding: clamp(0.38rem, 0.47vw + 0.28rem, 0.75rem);\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-items .p-dropdown-item {\\n  margin-bottom: clamp(0.38rem, 0.47vw + 0.28rem, 0.75rem);\\n}\\n\\n.country-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: clamp(0.75rem, 0.31vw + 0.69rem, 1rem);\\n  font-size: clamp(0.88rem, 0.16vw + 0.84rem, 1rem);\\n}\\n.country-item[_ngcontent-%COMP%]   .country-name[_ngcontent-%COMP%] {\\n  white-space: pre-line; \\n\\n}\\n.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  \\n\\n\\n\\n\\n\\n  \\n\\n  display: inline-block;\\n  vertical-align: middle;\\n}\\n.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  clip-path: circle();\\n}\\n.country-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 32px;\\n  margin-right: 0.5rem;\\n}\\n.country-item[_ngcontent-%COMP%]   img.smaller[_ngcontent-%COMP%] {\\n  width: 16px;\\n}\\n\\n.info-element[_ngcontent-%COMP%] {\\n  padding: 10px 0;\\n}\\n\\n.btns[_ngcontent-%COMP%]   div[_ngcontent-%COMP%] {\\n  margin: 15px;\\n}\\n\\n.white-button[_ngcontent-%COMP%] {\\n  padding: 10px;\\n}\\n\\n.add-btn[_ngcontent-%COMP%] {\\n  width: 20px;\\n}\\n\\n.light-purple-circle-button[_ngcontent-%COMP%] {\\n  width: 15px;\\n  height: 15px;\\n}\\n\\n.input-fields[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n  align-items: center;\\n}\\n\\n.trash[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  cursor: pointer;\\n}\\n.trash[_ngcontent-%COMP%]    > img[_ngcontent-%COMP%] {\\n  width: 30px;\\n  margin-top: 5px;\\n}\\n\\n.w-90[_ngcontent-%COMP%] {\\n  width: 87% !important;\\n  flex-basis: 87% !important;\\n}\\n\\n.add-degree[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-top: 10px;\\n}\\n.add-degree[_ngcontent-%COMP%]   .add-degree-input[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n  width: 30%;\\n}\\n\\n.delete-file-icon-img[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  cursor: pointer;\\n}\\n\\n.title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-top: 10px;\\n}\\n.title[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  padding: 10px 10px 10px 0;\\n  font-size: 18px;\\n}\\n\\n.more-height-div[_ngcontent-%COMP%]   .input-element-title[_ngcontent-%COMP%] {\\n  height: 30px;\\n}\\n.more-height-div[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%] {\\n  height: 140px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["UntypedFormArray", "UntypedFormControl", "UntypedFormGroup", "Validators", "SubSink", "Observable", "i0", "ɵɵelementStart", "ɵɵlistener", "EducationComponent_ng_container_2_div_5_div_6_Template_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "addEducation", "ɵɵelementEnd", "EducationComponent_ng_container_2_div_5_div_7_Template_button_click_1_listener", "_r3", "i_r4", "index", "removeEducation", "ɵɵtext", "EducationComponent_ng_container_2_div_5_div_18_div_4_Template_img_click_6_listener", "_r6", "hideFileUpload", "EducationComponent_ng_container_2_div_5_div_18_div_4_Template_app_upload_files_onUploadFinished_9_listener", "$event", "uploadFinished", "ɵɵadvance", "ɵɵproperty", "UPLOAD_TEACHER_APPLICATION_FILES_URL", "EducationComponent_ng_container_2_div_5_div_18_Template_div_click_1_listener", "_r5", "openFileUploadDialog", "EducationComponent_ng_container_2_div_5_div_18_Template_img_click_3_listener", "ɵɵtemplate", "EducationComponent_ng_container_2_div_5_div_18_div_4_Template", "educationfilePaths", "controls", "length", "showFileUpload", "EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_2_Template_div_click_0_listener", "_r7", "EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_2_Template_img_click_2_listener", "ɵɵelement", "EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_3_Template_img_click_4_listener", "_r8", "skillIndex_r9", "removeEducationFileField", "getFileName", "form", "value", "education", "fileUploads", "filePath", "ɵɵtextInterpolate", "EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_4_Template_img_click_6_listener", "_r10", "EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_4_Template_app_upload_files_onUploadFinished_9_listener", "EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_2_Template", "EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_3_Template", "EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_4_Template", "EducationComponent_ng_container_2_div_5_ng_template_19_div_0_Template", "ɵɵelementContainerStart", "EducationComponent_ng_container_2_div_5_div_6_Template", "EducationComponent_ng_container_2_div_5_div_7_Template", "EducationComponent_ng_container_2_div_5_div_9_Template", "EducationComponent_ng_container_2_div_5_div_14_Template", "EducationComponent_ng_container_2_div_5_div_18_Template", "EducationComponent_ng_container_2_div_5_ng_template_19_Template", "ɵɵtemplateRefExtractor", "ɵɵpureFunction1", "_c0", "ifFieldValid", "tplwithFilePaths_r11", "EducationComponent_ng_container_2_div_5_Template", "EducationComponent", "constructor", "generalService", "teacherService", "toastService", "authService", "confirmationService", "router", "fb", "location", "subs", "isTablet", "teacher", "dummy<PERSON><PERSON><PERSON>", "tryToSave", "isLoading", "speakLanguageCounter", "error", "teacherEducation", "formChanged", "ngOnInit", "setCurrentStepIndex", "add", "submitOnMenuClickListener", "subscribe", "res", "onSubmit", "getTeacherApplicationStep2", "updateStepFormValues", "initFormChangedListener", "deviceKind", "is1024", "for<PERSON>ach", "element", "push", "group", "ngOnDestroy", "unsubscribe", "setSubmitOnMenuClickListener", "get", "teacherApplicationStep2", "isNullishObject", "fileUploadElement", "addEducationFileField", "console", "log", "name", "required", "institute", "array", "removeAt", "valueChanges", "val", "valid", "updateTeacherApplicationStep2", "setShowToastmessage", "severity", "summary", "detail", "navigateByUrl", "replaceUrl", "back", "i", "setStepValid", "invalid", "goBack", "event", "db<PERSON><PERSON>", "fileIndex", "document", "getElementById", "classList", "remove", "fileName", "getFileName2", "removeEmployee", "empIndex", "at", "newFilePathFormGroup", "skillIndex", "filename", "deleteTeacherApplicationFiles", "result", "canDeactivate", "observer", "confirm", "header", "key", "message", "accept", "next", "complete", "reject", "_", "ɵɵdirectiveInject", "i1", "GeneralService", "i2", "TeacherApplicationService", "i3", "ToastService", "i4", "AuthService", "i5", "ConfirmationService", "i6", "Router", "i7", "UntypedFormBuilder", "i8", "Location", "_2", "selectors", "decls", "vars", "consts", "template", "EducationComponent_Template", "rf", "ctx", "EducationComponent_ng_container_2_Template", "EducationComponent_Template_button_click_4_listener", "EducationComponent_Template_button_click_7_listener"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\teacher-application\\education\\education.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\teacher-application\\education\\education.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Location } from '@angular/common';\r\nimport { UntypedFormArray, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { Teacher, TeacherApplicationEducation, TeacherApplicationStep2, TeacherEducation } from 'src/app/core/models/teacher.model';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { TeacherApplicationService } from 'src/app/core/services/teacher-application.service';\r\nimport { SubSink } from 'subsink';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { Observable } from 'rxjs';\r\nimport { ConfirmationService } from 'primeng/api';\r\n\r\n@Component({\r\n  selector: 'app-education',\r\n  templateUrl: './education.component.html',\r\n  styleUrls: ['./education.component.scss']\r\n})\r\nexport class EducationComponent implements OnInit {\r\n\r\n  private subs = new SubSink();\r\n  public form: UntypedFormGroup = new UntypedFormGroup({});\r\n  public isTablet: boolean = false;\r\n  public teacher: Teacher = this.teacherService.dummyTeacher;\r\n  tryToSave: boolean = false;\r\n  isLoading: boolean = false;\r\n  speakLanguageCounter: number = 0;\r\n  public error: boolean = false;\r\n  public UPLOAD_TEACHER_APPLICATION_FILES_URL = '/LMS/UploadTeacherApplicationFiles';\r\n  teacherEducation: TeacherEducation[] = [];\r\n  showFileUpload: boolean[] = [];\r\n  formChanged = false;\r\n\r\n  constructor(\r\n    private generalService: GeneralService,\r\n    private teacherService: TeacherApplicationService,\r\n    private toastService: ToastService,\r\n    private authService: AuthService,\r\n    private confirmationService: ConfirmationService,\r\n    private router: Router,\r\n    private fb: UntypedFormBuilder,\r\n    private location: Location\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.teacherService.setCurrentStepIndex(1);\r\n    this.subs.add(this.teacherService.submitOnMenuClickListener.subscribe(res => {\r\n      if (res) {\r\n        this.onSubmit();\r\n      }\r\n    }));\r\n\r\n    this.subs.add(this.teacherService.getTeacherApplicationStep2().subscribe(res => {\r\n      if (res) {\r\n        this.updateStepFormValues(res);\r\n      }\r\n      if (res.education === null) {\r\n        this.addEducation({} as TeacherApplicationEducation)\r\n      }\r\n      \r\n      this.initFormChangedListener();\r\n    }));\r\n\r\n    this.subs.add(this.generalService.deviceKind.subscribe(res => {\r\n      this.isTablet = res.is1024;\r\n    }));\r\n    this.form = new UntypedFormGroup({\r\n      education: new UntypedFormArray([]),\r\n    });\r\n    this.teacherEducation = this.teacher.education\r\n    this.teacherEducation.forEach(element => {\r\n      this.showFileUpload.push(false)\r\n      this.education.push(this.fb.group(element))\r\n    });\r\n\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n    this.teacherService.setSubmitOnMenuClickListener(false);\r\n  }\r\n\r\n  get education(): UntypedFormArray {\r\n    return this.form.get('education') as UntypedFormArray;\r\n  }\r\n\r\n  /**\r\nUpdate the form values for step 2 of the teacher application\r\n@param {TeacherApplicationStep2} teacherApplicationStep2 - The data for step 2 of the teacher application\r\n*/\r\n  updateStepFormValues(teacherApplicationStep2: TeacherApplicationStep2) {\r\n\r\n    if (!this.generalService.isNullishObject(teacherApplicationStep2.education)) {\r\n      teacherApplicationStep2.education.forEach((element, index) => {\r\n        this.addEducation(element);\r\n        element.fileUploads.forEach(fileUploadElement => {\r\n          this.addEducationFileField(index, fileUploadElement.filePath);\r\n        });\r\n      });\r\n      console.log(teacherApplicationStep2.education);\r\n\r\n    }\r\n  }\r\n\r\n  /**\r\n  Add an education to the form\r\n  @param {TeacherApplicationEducation} element - The education to add to the form\r\n  */\r\n  addEducation(element: TeacherApplicationEducation) {\r\n    const group = new UntypedFormGroup({\r\n      name: new UntypedFormControl(element ? element.name : '', Validators.required),\r\n      institute: new UntypedFormControl(element ? element.institute : '', Validators.required),\r\n      fileUploads: this.fb.array([])\r\n    });\r\n    this.education.push(group);\r\n  }\r\n\r\n  /**\r\n  Remove an education from the form\r\n  @param {number} index - The index of the education to remove\r\n  */\r\n  removeEducation(index: number) {\r\n    this.education.removeAt(index);\r\n  }\r\n  \r\n\r\n  initFormChangedListener() {\r\n    this.subs.add(this.form.valueChanges.subscribe(val => {\r\n      console.log(val);\r\n      this.formChanged = true;\r\n    }));\r\n  }\r\n\r\n  /**\r\n  Submit the form data\r\n  */\r\n  onSubmit() {\r\n    this.tryToSave = true;\r\n    if (!this.form.valid) {\r\n      this.error = true;\r\n      return;\r\n    }\r\n    this.formChanged = false;\r\n    this.subs.add(this.teacherService.updateTeacherApplicationStep2(this.form.value).subscribe(res => {\r\n      if (res) {\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'success',\r\n          summary: '',\r\n          detail: 'Your Education info were updated.'\r\n        });\r\n      }\r\n      this.router.navigateByUrl('/teacher/experience', { replaceUrl: true });\r\n    }));\r\n  }\r\n\r\n  back() {\r\n    this.location.back();\r\n  }\r\n\r\n  ifFieldValid(i: number) {\r\n    this.teacherService.setStepValid(1, this.form, 'teacher-education-route');\r\n    return ((this.tryToSave && (<UntypedFormArray>this.form.get('education')).controls[i].invalid))\r\n  }\r\n\r\n  goBack() {\r\n    this.router.navigateByUrl('/teacher/info', { replaceUrl: true });\r\n  }\r\n\r\n  /**\r\n * Handles the completion of a file upload\r\n *\r\n * @param {filePath: { dbPath: string; }} event - Event object containing information about the uploaded file\r\n * @param {number} i - Index of the file in the array of files\r\n *\r\n */\r\n  uploadFinished(event: { filePath: { dbPath: string; } }, i: number) {\r\n    this.hideFileUpload(i);\r\n    this.addEducationFileField(i, event.filePath.dbPath);\r\n  }\r\n\r\n  /**\r\n   * Opens the file upload dialog\r\n   *\r\n   * @param {number} fileIndex - Index of the file in the array of files\r\n   *\r\n   */\r\n  openFileUploadDialog(fileIndex: number) {\r\n    this.showFileUpload[fileIndex] = true\r\n    document.getElementById('blur_bg')?.classList.add('yes-visibility');\r\n  }\r\n\r\n  /**\r\n  Hides the file upload for a specific index.\r\n  @param {number} fileIndex - The index of the file upload to be hidden.\r\n  */\r\n  hideFileUpload(fileIndex: number) {\r\n    this.showFileUpload[fileIndex] = false;\r\n    document.getElementById('blur_bg')?.classList.remove('yes-visibility');\r\n  }\r\n\r\n  /**\r\n  Get the file name from a file path\r\n  @param {string} fileName - The file path\r\n  @returns {string} The file name\r\n  */\r\n  getFileName(fileName: string): string {\r\n    return this.generalService.getFileName2(fileName);\r\n  }\r\n\r\n  removeEmployee(empIndex: number) {\r\n    this.education.removeAt(empIndex);\r\n  }\r\n\r\n  educationfilePaths(empIndex: number): UntypedFormArray {\r\n    return this.education\r\n      .at(empIndex)\r\n      .get('fileUploads') as UntypedFormArray;\r\n  }\r\n\r\n  newFilePathFormGroup(name: string): UntypedFormGroup {\r\n    return this.fb.group({\r\n      filePath: name\r\n    });\r\n  }\r\n\r\n  addEducationFileField(empIndex: number, name: string) {\r\n    this.educationfilePaths(empIndex).push(this.newFilePathFormGroup(name));\r\n  }\r\n\r\n  removeEducationFileField(empIndex: number, skillIndex: number, filename: string) {\r\n    this.educationfilePaths(empIndex).removeAt(skillIndex);\r\n    this.subs.add(this.teacherService.deleteTeacherApplicationFiles(filename).subscribe(result => {\r\n      console.log(result);\r\n    }));\r\n  }\r\n  \r\n  canDeactivate(): Observable<boolean> | boolean {\r\n    if (this.formChanged) {\r\n      return new Observable((observer: any) => {\r\n        this.confirmationService.confirm({\r\n          header: '',\r\n          key: 'stepLeaveConfirmation',\r\n          message: 'Are you sure you want to leave this page? Your changes will be lost.',\r\n          accept: () => {\r\n            observer.next(true);\r\n            observer.complete();\r\n          },\r\n          reject: () => {\r\n            observer.next(false);\r\n            observer.complete();\r\n          }\r\n        });\r\n      });\r\n    } else {\r\n      return (true);\r\n    }\r\n  }\r\n}\r\n", "<div class=\"md:mt-3\"></div>\r\n<div class=\"profile-info\">\r\n    <ng-container *ngIf=\"!isLoading\">\r\n    <form [formGroup]=\"form\">\r\n        <div class=\"profile-info-section\">\r\n            <!-- <div class=\"title\">\r\n                <div class=\"title-text\">* Degrees / Diplomas</div>\r\n                <img (click)=\"addEducation()\" src=\"/assets/icons/add.png\" class=\"add hvr-grow\" style=\"width:20px\">\r\n                <div (click)=\"addEducation()\" class=\"link-purple-color\" style=\"margin-left:10px; font-weight:bold;\">Add\r\n                    New</div>\r\n            </div> -->\r\n            <div>\r\n                <ng-container formArrayName=\"education\">\r\n                    <div *ngFor=\"let _ of education.controls; index as i\" style=\"position: relative;\">\r\n                        <ng-container [formGroupName]=\"i\">\r\n\r\n                            <div class=\"input-fields\" [ngClass]=\"{'more-height-div': educationfilePaths(i).length > 0}\">\r\n                                <div class=\"input-field col--1of3\">\r\n                                    <div class=\"input-element-title\">Degrees / Diplomas *\r\n                                        <div *ngIf=\"i === 0\" class=\"hvr-grow\">\r\n                                            <button pButton type=\"button\" (click)=\"addEducation()\" class=\"p-button-raised p-button-rounded plus-btn-circle\" icon=\"pi pi-plus\"></button>\r\n                                        </div>\r\n                                        <div *ngIf=\"i > 0\" class=\"hvr-grow\">\r\n                                            <button pButton type=\"button\" (click)=\"removeEducation(i)\" class=\"p-button-raised p-button-rounded minus-btn-circle\" icon=\"pi pi-minus\"></button>\r\n                                        </div>\r\n                                    </div>\r\n                                    <input class=\"input-element\" formControlName=\"name\" type=\"text\"\r\n                                        placeholder=\"e.g. Bachelor of Spanish\">\r\n                                    <div *ngIf=\"ifFieldValid(i)\" class=\"input-error\">* Degrees / Diplomas is required\r\n                                    </div>\r\n                                </div>\r\n\r\n                                <div class=\"input-field col--1of3\">\r\n                                    <div class=\"input-element-title\">Institute *</div>\r\n                                    <input class=\"input-element\" formControlName=\"institute\" type=\"text\"\r\n                                        placeholder=\"e.g. Aristotle University\">\r\n                                    <div *ngIf=\"ifFieldValid(i)\" class=\"input-error\">Institute is required\r\n                                    </div>\r\n                                </div>\r\n                        <div class=\"input-field col--1of3\" formArrayName=\"fileUploads\">\r\n                            <div class=\"input-element-title\">Upload Degree/Diploma</div>\r\n                            <div *ngIf=\"educationfilePaths(i).controls.length === 0; else tplwithFilePaths\">\r\n                                <div (click)=\"openFileUploadDialog(i)\" class=\"rounded-blue-button upload-button transparent\">Upload File\r\n                                    <img (click)=\"hideFileUpload(i)\" src=\"/assets/icons/cloud-icon.svg\" alt=\"cloud-icon\" />\r\n                                </div>\r\n                                <div #fileUpload *ngIf=\"educationfilePaths(i).controls.length === 0 && showFileUpload[i]\" class=\"modal\"\r\n                                    #taskFiles>\r\n                                    <div class=\"popup-title\">\r\n                                        <div>Add files</div>\r\n                                        <img (click)=\"hideFileUpload(i)\" src=\"/assets/icons/close.png\" class=\"close-img close-img-abs hvr-glow\">\r\n                                    </div>\r\n                                    <div>\r\n                                        Upload Degree/Diploma\r\n                                    </div>\r\n                                    <app-upload-files [dndUI]=\"true\" [multiple]=\"false\" title=\"Degree/Diploma Paper\"\r\n                                    [isTeacherApplication]=\"true\"\r\n                                        [postUrl]=\"UPLOAD_TEACHER_APPLICATION_FILES_URL\" (onUploadFinished)=\"uploadFinished($event, i)\">\r\n                                    </app-upload-files>\r\n                                </div>\r\n                            </div>\r\n                            <ng-template #tplwithFilePaths>\r\n                                <div *ngFor=\"let skill of educationfilePaths(i).controls; let skillIndex=index\">\r\n                                    <div [formGroupName]=\"skillIndex\">\r\n                                        <div *ngIf=\"form.value.education[i].fileUploads.length === 1\" (click)=\"openFileUploadDialog(i)\"\r\n                                            class=\"rounded-blue-button upload-button transparent\">Upload File\r\n                                            <img (click)=\"hideFileUpload(i)\" src=\"/assets/icons/cloud-icon.svg\" alt=\"cloud-icon\" />\r\n                                        </div>\r\n                                        <!-- *ngIf=\"form.value.education[i].filePath!==''\"  -->\r\n                                        <div *ngIf=\"form.value.education[i].fileUploads[skillIndex].length !== 1\" class=\"file-path\">\r\n                                            <img src=\"/assets/icons/education-degree-white.svg\" class=\"close-img close-img-abs hvr-glow\">\r\n                                            <span class=\"file-path-text\">{{getFileName(form.value.education[i].fileUploads[skillIndex].filePath)}}</span>\r\n                                            <img src=\"/assets/icons/trash-white.svg\" alt=\"trash-white.svg\" class=\"delete-file-icon-img hvr-glow\"\r\n                                                (click)=\"removeEducationFileField(i, skillIndex, getFileName(form.value.education[i].fileUploads[skillIndex].filePath))\">\r\n                                        </div>\r\n                                        <div #fileUpload *ngIf=\"showFileUpload[skillIndex]\" class=\"modal\" #taskFiles>\r\n                                            <div class=\"popup-title\">\r\n                                                <div>Add files</div>\r\n                                                <img (click)=\"hideFileUpload(skillIndex)\" src=\"/assets/icons/close.png\"\r\n                                                    class=\"close-img close-img-abs hvr-glow\">\r\n                                            </div>\r\n                                            <div>\r\n                                                Upload Degree/Diploma\r\n                                            </div>\r\n                                            <app-upload-files [dndUI]=\"true\" [multiple]=\"false\" title=\"Degree/Diploma Paper\"\r\n                                                [postUrl]=\"UPLOAD_TEACHER_APPLICATION_FILES_URL\"\r\n                                                (onUploadFinished)=\"uploadFinished($event, skillIndex)\">\r\n                                            </app-upload-files>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </ng-template>\r\n                        </div>\r\n                        </div>\r\n                        <!-- <div *ngIf=\"ifFieldValid(i)\" class=\"input-error\">* Input field and file upload are required\r\n                        </div> -->\r\n                        </ng-container>\r\n                    </div>\r\n                </ng-container>\r\n            </div>\r\n        </div>\r\n    </form>\r\n    </ng-container>\r\n</div>\r\n<div class=\"btns md:ml-5 mt-4\">\r\n    <button pRipple class=\"rounded-blue-button transparent\" (click)=\"goBack()\"> <img src=\"/assets/icons/arrow-left-blue.svg\" />\r\n        Back</button>\r\n    <button pRipple (click)=\"onSubmit()\" class=\"rounded-blue-button\">Next <img src=\"/assets/icons/arrow-right.svg\" /></button>\r\n    <!-- <div (click)=\"onSubmit()\" class=\"light-purple-button\">Next</div> -->\r\n</div>"], "mappings": "AAEA,SAASA,gBAAgB,EAAsBC,kBAAkB,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,gBAAgB;AAKvH,SAASC,OAAO,QAAQ,SAAS;AAGjC,SAASC,UAAU,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;ICUWC,EADJ,CAAAC,cAAA,cAAsC,iBACgG;IAApGD,EAAA,CAAAE,UAAA,mBAAAC,+EAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IAC1DT,EADsI,CAAAU,YAAA,EAAS,EACzI;;;;;;IAEFV,EADJ,CAAAC,cAAA,cAAoC,iBACwG;IAA1GD,EAAA,CAAAE,UAAA,mBAAAS,+EAAA;MAAAX,EAAA,CAAAI,aAAA,CAAAQ,GAAA;MAAA,MAAAC,IAAA,GAAAb,EAAA,CAAAO,aAAA,GAAAO,KAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAS,eAAA,CAAAF,IAAA,CAAkB;IAAA,EAAC;IAC9Db,EAD4I,CAAAU,YAAA,EAAS,EAC/I;;;;;IAIVV,EAAA,CAAAC,cAAA,cAAiD;IAAAD,EAAA,CAAAgB,MAAA,wCACjD;IAAAhB,EAAA,CAAAU,YAAA,EAAM;;;;;IAONV,EAAA,CAAAC,cAAA,cAAiD;IAAAD,EAAA,CAAAgB,MAAA,6BACjD;IAAAhB,EAAA,CAAAU,YAAA,EAAM;;;;;;IAWFV,EAHR,CAAAC,cAAA,iBACe,cACc,UAChB;IAAAD,EAAA,CAAAgB,MAAA,gBAAS;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IACpBV,EAAA,CAAAC,cAAA,cAAwG;IAAnGD,EAAA,CAAAE,UAAA,mBAAAe,mFAAA;MAAAjB,EAAA,CAAAI,aAAA,CAAAc,GAAA;MAAA,MAAAL,IAAA,GAAAb,EAAA,CAAAO,aAAA,IAAAO,KAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAa,cAAA,CAAAN,IAAA,CAAiB;IAAA,EAAC;IACpCb,EADI,CAAAU,YAAA,EAAwG,EACtG;IACNV,EAAA,CAAAC,cAAA,UAAK;IACDD,EAAA,CAAAgB,MAAA,8BACJ;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,2BAEoG;IAA/CD,EAAA,CAAAE,UAAA,8BAAAkB,2GAAAC,MAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAc,GAAA;MAAA,MAAAL,IAAA,GAAAb,EAAA,CAAAO,aAAA,IAAAO,KAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAoBF,MAAA,CAAAgB,cAAA,CAAAD,MAAA,EAAAR,IAAA,CAAyB;IAAA,EAAC;IAEvGb,EADI,CAAAU,YAAA,EAAmB,EACjB;;;;IAJgBV,EAAA,CAAAuB,SAAA,GAAc;IAE5BvB,EAFc,CAAAwB,UAAA,eAAc,mBAAmB,8BACtB,YAAAlB,MAAA,CAAAmB,oCAAA,CACuB;;;;;;IAdxDzB,EADJ,CAAAC,cAAA,UAAgF,cACiB;IAAxFD,EAAA,CAAAE,UAAA,mBAAAwB,6EAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,MAAAd,IAAA,GAAAb,EAAA,CAAAO,aAAA,GAAAO,KAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,oBAAA,CAAAf,IAAA,CAAuB;IAAA,EAAC;IAAuDb,EAAA,CAAAgB,MAAA,mBACzF;IAAAhB,EAAA,CAAAC,cAAA,cAAuF;IAAlFD,EAAA,CAAAE,UAAA,mBAAA2B,6EAAA;MAAA7B,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,MAAAd,IAAA,GAAAb,EAAA,CAAAO,aAAA,GAAAO,KAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAa,cAAA,CAAAN,IAAA,CAAiB;IAAA,EAAC;IACpCb,EADI,CAAAU,YAAA,EAAuF,EACrF;IACNV,EAAA,CAAA8B,UAAA,IAAAC,6DAAA,mBACe;IAanB/B,EAAA,CAAAU,YAAA,EAAM;;;;;IAdgBV,EAAA,CAAAuB,SAAA,GAAsE;IAAtEvB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAA0B,kBAAA,CAAAnB,IAAA,EAAAoB,QAAA,CAAAC,MAAA,UAAA5B,MAAA,CAAA6B,cAAA,CAAAtB,IAAA,EAAsE;;;;;;IAkBhFb,EAAA,CAAAC,cAAA,cAC0D;IADID,EAAA,CAAAE,UAAA,mBAAAkC,iGAAA;MAAApC,EAAA,CAAAI,aAAA,CAAAiC,GAAA;MAAA,MAAAxB,IAAA,GAAAb,EAAA,CAAAO,aAAA,IAAAO,KAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,oBAAA,CAAAf,IAAA,CAAuB;IAAA,EAAC;IACrCb,EAAA,CAAAgB,MAAA,mBACtD;IAAAhB,EAAA,CAAAC,cAAA,cAAuF;IAAlFD,EAAA,CAAAE,UAAA,mBAAAoC,iGAAA;MAAAtC,EAAA,CAAAI,aAAA,CAAAiC,GAAA;MAAA,MAAAxB,IAAA,GAAAb,EAAA,CAAAO,aAAA,IAAAO,KAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAa,cAAA,CAAAN,IAAA,CAAiB;IAAA,EAAC;IACpCb,EADI,CAAAU,YAAA,EAAuF,EACrF;;;;;;IAENV,EAAA,CAAAC,cAAA,cAA4F;IACxFD,EAAA,CAAAuC,SAAA,cAA6F;IAC7FvC,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAgB,MAAA,GAAyE;IAAAhB,EAAA,CAAAU,YAAA,EAAO;IAC7GV,EAAA,CAAAC,cAAA,cAC6H;IAAzHD,EAAA,CAAAE,UAAA,mBAAAsC,iGAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAAqC,GAAA;MAAA,MAAAC,aAAA,GAAA1C,EAAA,CAAAO,aAAA,GAAAO,KAAA;MAAA,MAAAD,IAAA,GAAAb,EAAA,CAAAO,aAAA,IAAAO,KAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqC,wBAAA,CAAA9B,IAAA,EAAA6B,aAAA,EAAwCpC,MAAA,CAAAsC,WAAA,CAAAtC,MAAA,CAAAuC,IAAA,CAAAC,KAAA,CAAAC,SAAA,CAAAlC,IAAA,EAAAmC,WAAA,CAAAN,aAAA,EAAAO,QAAA,CAAqE,CAAC;IAAA,EAAC;IAChIjD,EAFI,CAAAU,YAAA,EAC6H,EAC3H;;;;;;IAH2BV,EAAA,CAAAuB,SAAA,GAAyE;IAAzEvB,EAAA,CAAAkD,iBAAA,CAAA5C,MAAA,CAAAsC,WAAA,CAAAtC,MAAA,CAAAuC,IAAA,CAAAC,KAAA,CAAAC,SAAA,CAAAlC,IAAA,EAAAmC,WAAA,CAAAN,aAAA,EAAAO,QAAA,EAAyE;;;;;;IAMlGjD,EAFR,CAAAC,cAAA,iBAA6E,cAChD,UAChB;IAAAD,EAAA,CAAAgB,MAAA,gBAAS;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IACpBV,EAAA,CAAAC,cAAA,cAC6C;IADxCD,EAAA,CAAAE,UAAA,mBAAAiD,iGAAA;MAAAnD,EAAA,CAAAI,aAAA,CAAAgD,IAAA;MAAA,MAAAV,aAAA,GAAA1C,EAAA,CAAAO,aAAA,GAAAO,KAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAa,cAAA,CAAAuB,aAAA,CAA0B;IAAA,EAAC;IAE7C1C,EAFI,CAAAU,YAAA,EAC6C,EAC3C;IACNV,EAAA,CAAAC,cAAA,UAAK;IACDD,EAAA,CAAAgB,MAAA,8BACJ;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,2BAE4D;IAAxDD,EAAA,CAAAE,UAAA,8BAAAmD,yHAAAhC,MAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAgD,IAAA;MAAA,MAAAV,aAAA,GAAA1C,EAAA,CAAAO,aAAA,GAAAO,KAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAoBF,MAAA,CAAAgB,cAAA,CAAAD,MAAA,EAAAqB,aAAA,CAAkC;IAAA,EAAC;IAE/D1C,EADI,CAAAU,YAAA,EAAmB,EACjB;;;;IAJgBV,EAAA,CAAAuB,SAAA,GAAc;IAC5BvB,EADc,CAAAwB,UAAA,eAAc,mBAAmB,YAAAlB,MAAA,CAAAmB,oCAAA,CACC;;;;;IAtB5DzB,EADJ,CAAAC,cAAA,UAAgF,cAC1C;IAY9BD,EAXA,CAAA8B,UAAA,IAAAwB,2EAAA,kBAC0D,IAAAC,2EAAA,kBAIkC,IAAAC,2EAAA,mBAMf;IAerFxD,EADI,CAAAU,YAAA,EAAM,EACJ;;;;;;IA3BGV,EAAA,CAAAuB,SAAA,EAA4B;IAA5BvB,EAAA,CAAAwB,UAAA,kBAAAkB,aAAA,CAA4B;IACvB1C,EAAA,CAAAuB,SAAA,EAAsD;IAAtDvB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAuC,IAAA,CAAAC,KAAA,CAAAC,SAAA,CAAAlC,IAAA,EAAAmC,WAAA,CAAAd,MAAA,OAAsD;IAKtDlC,EAAA,CAAAuB,SAAA,EAAkE;IAAlEvB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAuC,IAAA,CAAAC,KAAA,CAAAC,SAAA,CAAAlC,IAAA,EAAAmC,WAAA,CAAAN,aAAA,EAAAR,MAAA,OAAkE;IAMtDlC,EAAA,CAAAuB,SAAA,EAAgC;IAAhCvB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAA6B,cAAA,CAAAO,aAAA,EAAgC;;;;;IAb1D1C,EAAA,CAAA8B,UAAA,IAAA2B,qEAAA,kBAAgF;;;;;IAAzDzD,EAAA,CAAAwB,UAAA,YAAAlB,MAAA,CAAA0B,kBAAA,CAAAnB,IAAA,EAAAoB,QAAA,CAAmC;;;;;IAhDtEjC,EAAA,CAAAC,cAAA,cAAkF;IAC9ED,EAAA,CAAA0D,uBAAA,OAAkC;IAItB1D,EAFR,CAAAC,cAAA,cAA4F,cACrD,cACE;IAAAD,EAAA,CAAAgB,MAAA,4BAC7B;IAGAhB,EAHA,CAAA8B,UAAA,IAAA6B,sDAAA,kBAAsC,IAAAC,sDAAA,kBAGF;IAGxC5D,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAuC,SAAA,gBAC2C;IAC3CvC,EAAA,CAAA8B,UAAA,IAAA+B,sDAAA,kBAAiD;IAErD7D,EAAA,CAAAU,YAAA,EAAM;IAGFV,EADJ,CAAAC,cAAA,eAAmC,eACE;IAAAD,EAAA,CAAAgB,MAAA,mBAAW;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IAClDV,EAAA,CAAAuC,SAAA,iBAC4C;IAC5CvC,EAAA,CAAA8B,UAAA,KAAAgC,uDAAA,kBAAiD;IAErD9D,EAAA,CAAAU,YAAA,EAAM;IAEVV,EADJ,CAAAC,cAAA,eAA+D,eAC1B;IAAAD,EAAA,CAAAgB,MAAA,6BAAqB;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IAoB5DV,EAnBA,CAAA8B,UAAA,KAAAiC,uDAAA,kBAAgF,KAAAC,+DAAA,gCAAAhE,EAAA,CAAAiE,sBAAA,CAmBjD;IAgCnCjE,EADA,CAAAU,YAAA,EAAM,EACA;;IAIVV,EAAA,CAAAU,YAAA,EAAM;;;;;;IAlFYV,EAAA,CAAAuB,SAAA,EAAmB;IAAnBvB,EAAA,CAAAwB,UAAA,kBAAAX,IAAA,CAAmB;IAEHb,EAAA,CAAAuB,SAAA,EAAiE;IAAjEvB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAkE,eAAA,IAAAC,GAAA,EAAA7D,MAAA,CAAA0B,kBAAA,CAAAnB,IAAA,EAAAqB,MAAA,MAAiE;IAGzElC,EAAA,CAAAuB,SAAA,GAAa;IAAbvB,EAAA,CAAAwB,UAAA,SAAAX,IAAA,OAAa;IAGbb,EAAA,CAAAuB,SAAA,EAAW;IAAXvB,EAAA,CAAAwB,UAAA,SAAAX,IAAA,KAAW;IAMfb,EAAA,CAAAuB,SAAA,GAAqB;IAArBvB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAA8D,YAAA,CAAAvD,IAAA,EAAqB;IAQrBb,EAAA,CAAAuB,SAAA,GAAqB;IAArBvB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAA8D,YAAA,CAAAvD,IAAA,EAAqB;IAK7Bb,EAAA,CAAAuB,SAAA,GAAmD;IAAAvB,EAAnD,CAAAwB,UAAA,SAAAlB,MAAA,CAAA0B,kBAAA,CAAAnB,IAAA,EAAAoB,QAAA,CAAAC,MAAA,OAAmD,aAAAmC,oBAAA,CAAqB;;;;;IAvCtGrE,EAAA,CAAA0D,uBAAA,GAAiC;IASzB1D,EARR,CAAAC,cAAA,eAAyB,cACa,UAOzB;IACDD,EAAA,CAAA0D,uBAAA,OAAwC;IACpC1D,EAAA,CAAA8B,UAAA,IAAAwC,gDAAA,oBAAkF;;IAuFlGtE,EAFQ,CAAAU,YAAA,EAAM,EACJ,EACH;;;;;IAjGDV,EAAA,CAAAuB,SAAA,EAAkB;IAAlBvB,EAAA,CAAAwB,UAAA,cAAAlB,MAAA,CAAAuC,IAAA,CAAkB;IAUW7C,EAAA,CAAAuB,SAAA,GAAuB;IAAvBvB,EAAA,CAAAwB,UAAA,YAAAlB,MAAA,CAAAyC,SAAA,CAAAd,QAAA,CAAuB;;;ADK9D,OAAM,MAAOsC,kBAAkB;EAe7BC,YACUC,cAA8B,EAC9BC,cAAyC,EACzCC,YAA0B,EAC1BC,WAAwB,EACxBC,mBAAwC,EACxCC,MAAc,EACdC,EAAsB,EACtBC,QAAkB;IAPlB,KAAAP,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,QAAQ,GAARA,QAAQ;IArBV,KAAAC,IAAI,GAAG,IAAInF,OAAO,EAAE;IACrB,KAAA+C,IAAI,GAAqB,IAAIjD,gBAAgB,CAAC,EAAE,CAAC;IACjD,KAAAsF,QAAQ,GAAY,KAAK;IACzB,KAAAC,OAAO,GAAY,IAAI,CAACT,cAAc,CAACU,YAAY;IAC1D,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,oBAAoB,GAAW,CAAC;IACzB,KAAAC,KAAK,GAAY,KAAK;IACtB,KAAA/D,oCAAoC,GAAG,oCAAoC;IAClF,KAAAgE,gBAAgB,GAAuB,EAAE;IACzC,KAAAtD,cAAc,GAAc,EAAE;IAC9B,KAAAuD,WAAW,GAAG,KAAK;EAWf;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACjB,cAAc,CAACkB,mBAAmB,CAAC,CAAC,CAAC;IAC1C,IAAI,CAACX,IAAI,CAACY,GAAG,CAAC,IAAI,CAACnB,cAAc,CAACoB,yBAAyB,CAACC,SAAS,CAACC,GAAG,IAAG;MAC1E,IAAIA,GAAG,EAAE;QACP,IAAI,CAACC,QAAQ,EAAE;MACjB;IACF,CAAC,CAAC,CAAC;IAEH,IAAI,CAAChB,IAAI,CAACY,GAAG,CAAC,IAAI,CAACnB,cAAc,CAACwB,0BAA0B,EAAE,CAACH,SAAS,CAACC,GAAG,IAAG;MAC7E,IAAIA,GAAG,EAAE;QACP,IAAI,CAACG,oBAAoB,CAACH,GAAG,CAAC;MAChC;MACA,IAAIA,GAAG,CAACjD,SAAS,KAAK,IAAI,EAAE;QAC1B,IAAI,CAACtC,YAAY,CAAC,EAAiC,CAAC;MACtD;MAEA,IAAI,CAAC2F,uBAAuB,EAAE;IAChC,CAAC,CAAC,CAAC;IAEH,IAAI,CAACnB,IAAI,CAACY,GAAG,CAAC,IAAI,CAACpB,cAAc,CAAC4B,UAAU,CAACN,SAAS,CAACC,GAAG,IAAG;MAC3D,IAAI,CAACd,QAAQ,GAAGc,GAAG,CAACM,MAAM;IAC5B,CAAC,CAAC,CAAC;IACH,IAAI,CAACzD,IAAI,GAAG,IAAIjD,gBAAgB,CAAC;MAC/BmD,SAAS,EAAE,IAAIrD,gBAAgB,CAAC,EAAE;KACnC,CAAC;IACF,IAAI,CAAC+F,gBAAgB,GAAG,IAAI,CAACN,OAAO,CAACpC,SAAS;IAC9C,IAAI,CAAC0C,gBAAgB,CAACc,OAAO,CAACC,OAAO,IAAG;MACtC,IAAI,CAACrE,cAAc,CAACsE,IAAI,CAAC,KAAK,CAAC;MAC/B,IAAI,CAAC1D,SAAS,CAAC0D,IAAI,CAAC,IAAI,CAAC1B,EAAE,CAAC2B,KAAK,CAACF,OAAO,CAAC,CAAC;IAC7C,CAAC,CAAC;EAEJ;EAEAG,WAAWA,CAAA;IACT,IAAI,CAAC1B,IAAI,CAAC2B,WAAW,EAAE;IACvB,IAAI,CAAClC,cAAc,CAACmC,4BAA4B,CAAC,KAAK,CAAC;EACzD;EAEA,IAAI9D,SAASA,CAAA;IACX,OAAO,IAAI,CAACF,IAAI,CAACiE,GAAG,CAAC,WAAW,CAAqB;EACvD;EAEA;;;;EAIAX,oBAAoBA,CAACY,uBAAgD;IAEnE,IAAI,CAAC,IAAI,CAACtC,cAAc,CAACuC,eAAe,CAACD,uBAAuB,CAAChE,SAAS,CAAC,EAAE;MAC3EgE,uBAAuB,CAAChE,SAAS,CAACwD,OAAO,CAAC,CAACC,OAAO,EAAE1F,KAAK,KAAI;QAC3D,IAAI,CAACL,YAAY,CAAC+F,OAAO,CAAC;QAC1BA,OAAO,CAACxD,WAAW,CAACuD,OAAO,CAACU,iBAAiB,IAAG;UAC9C,IAAI,CAACC,qBAAqB,CAACpG,KAAK,EAAEmG,iBAAiB,CAAChE,QAAQ,CAAC;QAC/D,CAAC,CAAC;MACJ,CAAC,CAAC;MACFkE,OAAO,CAACC,GAAG,CAACL,uBAAuB,CAAChE,SAAS,CAAC;IAEhD;EACF;EAEA;;;;EAIAtC,YAAYA,CAAC+F,OAAoC;IAC/C,MAAME,KAAK,GAAG,IAAI9G,gBAAgB,CAAC;MACjCyH,IAAI,EAAE,IAAI1H,kBAAkB,CAAC6G,OAAO,GAAGA,OAAO,CAACa,IAAI,GAAG,EAAE,EAAExH,UAAU,CAACyH,QAAQ,CAAC;MAC9EC,SAAS,EAAE,IAAI5H,kBAAkB,CAAC6G,OAAO,GAAGA,OAAO,CAACe,SAAS,GAAG,EAAE,EAAE1H,UAAU,CAACyH,QAAQ,CAAC;MACxFtE,WAAW,EAAE,IAAI,CAAC+B,EAAE,CAACyC,KAAK,CAAC,EAAE;KAC9B,CAAC;IACF,IAAI,CAACzE,SAAS,CAAC0D,IAAI,CAACC,KAAK,CAAC;EAC5B;EAEA;;;;EAIA3F,eAAeA,CAACD,KAAa;IAC3B,IAAI,CAACiC,SAAS,CAAC0E,QAAQ,CAAC3G,KAAK,CAAC;EAChC;EAGAsF,uBAAuBA,CAAA;IACrB,IAAI,CAACnB,IAAI,CAACY,GAAG,CAAC,IAAI,CAAChD,IAAI,CAAC6E,YAAY,CAAC3B,SAAS,CAAC4B,GAAG,IAAG;MACnDR,OAAO,CAACC,GAAG,CAACO,GAAG,CAAC;MAChB,IAAI,CAACjC,WAAW,GAAG,IAAI;IACzB,CAAC,CAAC,CAAC;EACL;EAEA;;;EAGAO,QAAQA,CAAA;IACN,IAAI,CAACZ,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC,IAAI,CAACxC,IAAI,CAAC+E,KAAK,EAAE;MACpB,IAAI,CAACpC,KAAK,GAAG,IAAI;MACjB;IACF;IACA,IAAI,CAACE,WAAW,GAAG,KAAK;IACxB,IAAI,CAACT,IAAI,CAACY,GAAG,CAAC,IAAI,CAACnB,cAAc,CAACmD,6BAA6B,CAAC,IAAI,CAAChF,IAAI,CAACC,KAAK,CAAC,CAACiD,SAAS,CAACC,GAAG,IAAG;MAC/F,IAAIA,GAAG,EAAE;QACP,IAAI,CAACrB,YAAY,CAACmD,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;MACJ;MACA,IAAI,CAACnD,MAAM,CAACoD,aAAa,CAAC,qBAAqB,EAAE;QAAEC,UAAU,EAAE;MAAI,CAAE,CAAC;IACxE,CAAC,CAAC,CAAC;EACL;EAEAC,IAAIA,CAAA;IACF,IAAI,CAACpD,QAAQ,CAACoD,IAAI,EAAE;EACtB;EAEAhE,YAAYA,CAACiE,CAAS;IACpB,IAAI,CAAC3D,cAAc,CAAC4D,YAAY,CAAC,CAAC,EAAE,IAAI,CAACzF,IAAI,EAAE,yBAAyB,CAAC;IACzE,OAAS,IAAI,CAACwC,SAAS,IAAuB,IAAI,CAACxC,IAAI,CAACiE,GAAG,CAAC,WAAW,CAAE,CAAC7E,QAAQ,CAACoG,CAAC,CAAC,CAACE,OAAO;EAC/F;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAAC1D,MAAM,CAACoD,aAAa,CAAC,eAAe,EAAE;MAAEC,UAAU,EAAE;IAAI,CAAE,CAAC;EAClE;EAEA;;;;;;;EAOA7G,cAAcA,CAACmH,KAAwC,EAAEJ,CAAS;IAChE,IAAI,CAAClH,cAAc,CAACkH,CAAC,CAAC;IACtB,IAAI,CAACnB,qBAAqB,CAACmB,CAAC,EAAEI,KAAK,CAACxF,QAAQ,CAACyF,MAAM,CAAC;EACtD;EAEA;;;;;;EAMA9G,oBAAoBA,CAAC+G,SAAiB;IACpC,IAAI,CAACxG,cAAc,CAACwG,SAAS,CAAC,GAAG,IAAI;IACrCC,QAAQ,CAACC,cAAc,CAAC,SAAS,CAAC,EAAEC,SAAS,CAACjD,GAAG,CAAC,gBAAgB,CAAC;EACrE;EAEA;;;;EAIA1E,cAAcA,CAACwH,SAAiB;IAC9B,IAAI,CAACxG,cAAc,CAACwG,SAAS,CAAC,GAAG,KAAK;IACtCC,QAAQ,CAACC,cAAc,CAAC,SAAS,CAAC,EAAEC,SAAS,CAACC,MAAM,CAAC,gBAAgB,CAAC;EACxE;EAEA;;;;;EAKAnG,WAAWA,CAACoG,QAAgB;IAC1B,OAAO,IAAI,CAACvE,cAAc,CAACwE,YAAY,CAACD,QAAQ,CAAC;EACnD;EAEAE,cAAcA,CAACC,QAAgB;IAC7B,IAAI,CAACpG,SAAS,CAAC0E,QAAQ,CAAC0B,QAAQ,CAAC;EACnC;EAEAnH,kBAAkBA,CAACmH,QAAgB;IACjC,OAAO,IAAI,CAACpG,SAAS,CAClBqG,EAAE,CAACD,QAAQ,CAAC,CACZrC,GAAG,CAAC,aAAa,CAAqB;EAC3C;EAEAuC,oBAAoBA,CAAChC,IAAY;IAC/B,OAAO,IAAI,CAACtC,EAAE,CAAC2B,KAAK,CAAC;MACnBzD,QAAQ,EAAEoE;KACX,CAAC;EACJ;EAEAH,qBAAqBA,CAACiC,QAAgB,EAAE9B,IAAY;IAClD,IAAI,CAACrF,kBAAkB,CAACmH,QAAQ,CAAC,CAAC1C,IAAI,CAAC,IAAI,CAAC4C,oBAAoB,CAAChC,IAAI,CAAC,CAAC;EACzE;EAEA1E,wBAAwBA,CAACwG,QAAgB,EAAEG,UAAkB,EAAEC,QAAgB;IAC7E,IAAI,CAACvH,kBAAkB,CAACmH,QAAQ,CAAC,CAAC1B,QAAQ,CAAC6B,UAAU,CAAC;IACtD,IAAI,CAACrE,IAAI,CAACY,GAAG,CAAC,IAAI,CAACnB,cAAc,CAAC8E,6BAA6B,CAACD,QAAQ,CAAC,CAACxD,SAAS,CAAC0D,MAAM,IAAG;MAC3FtC,OAAO,CAACC,GAAG,CAACqC,MAAM,CAAC;IACrB,CAAC,CAAC,CAAC;EACL;EAEAC,aAAaA,CAAA;IACX,IAAI,IAAI,CAAChE,WAAW,EAAE;MACpB,OAAO,IAAI3F,UAAU,CAAE4J,QAAa,IAAI;QACtC,IAAI,CAAC9E,mBAAmB,CAAC+E,OAAO,CAAC;UAC/BC,MAAM,EAAE,EAAE;UACVC,GAAG,EAAE,uBAAuB;UAC5BC,OAAO,EAAE,sEAAsE;UAC/EC,MAAM,EAAEA,CAAA,KAAK;YACXL,QAAQ,CAACM,IAAI,CAAC,IAAI,CAAC;YACnBN,QAAQ,CAACO,QAAQ,EAAE;UACrB,CAAC;UACDC,MAAM,EAAEA,CAAA,KAAK;YACXR,QAAQ,CAACM,IAAI,CAAC,KAAK,CAAC;YACpBN,QAAQ,CAACO,QAAQ,EAAE;UACrB;SACD,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAQ,IAAI;IACd;EACF;EAAC,QAAAE,CAAA,G;qBA9OU7F,kBAAkB,EAAAvE,EAAA,CAAAqK,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAvK,EAAA,CAAAqK,iBAAA,CAAAG,EAAA,CAAAC,yBAAA,GAAAzK,EAAA,CAAAqK,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAA3K,EAAA,CAAAqK,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA7K,EAAA,CAAAqK,iBAAA,CAAAS,EAAA,CAAAC,mBAAA,GAAA/K,EAAA,CAAAqK,iBAAA,CAAAW,EAAA,CAAAC,MAAA,GAAAjL,EAAA,CAAAqK,iBAAA,CAAAa,EAAA,CAAAC,kBAAA,GAAAnL,EAAA,CAAAqK,iBAAA,CAAAe,EAAA,CAAAC,QAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlB/G,kBAAkB;IAAAgH,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QClB/B7L,EAAA,CAAAuC,SAAA,aAA2B;QAC3BvC,EAAA,CAAAC,cAAA,aAA0B;QACtBD,EAAA,CAAA8B,UAAA,IAAAiK,0CAAA,0BAAiC;QAoGrC/L,EAAA,CAAAU,YAAA,EAAM;QAEFV,EADJ,CAAAC,cAAA,aAA+B,gBACgD;QAAnBD,EAAA,CAAAE,UAAA,mBAAA8L,oDAAA;UAAA,OAASF,GAAA,CAAAtD,MAAA,EAAQ;QAAA,EAAC;QAAExI,EAAA,CAAAuC,SAAA,aAA+C;QACvHvC,EAAA,CAAAgB,MAAA,YAAI;QAAAhB,EAAA,CAAAU,YAAA,EAAS;QACjBV,EAAA,CAAAC,cAAA,gBAAiE;QAAjDD,EAAA,CAAAE,UAAA,mBAAA+L,oDAAA;UAAA,OAASH,GAAA,CAAA7F,QAAA,EAAU;QAAA,EAAC;QAA6BjG,EAAA,CAAAgB,MAAA,YAAK;QAAAhB,EAAA,CAAAuC,SAAA,aAA2C;QAErHvC,EAFqH,CAAAU,YAAA,EAAS,EAExH;;;QA1GaV,EAAA,CAAAuB,SAAA,GAAgB;QAAhBvB,EAAA,CAAAwB,UAAA,UAAAsK,GAAA,CAAAxG,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}