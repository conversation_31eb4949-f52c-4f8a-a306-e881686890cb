{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./notes/notes.component\";\nexport let NoteComponent = /*#__PURE__*/(() => {\n  class NoteComponent {\n    constructor() {}\n    ngOnInit() {}\n    static #_ = this.ɵfac = function NoteComponent_Factory(t) {\n      return new (t || NoteComponent)();\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NoteComponent,\n      selectors: [[\"app-note\"]],\n      decls: 1,\n      vars: 0,\n      template: function NoteComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"app-notes\");\n        }\n      },\n      dependencies: [i1.NotesComponent]\n    });\n  }\n  return NoteComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}