{"ast": null, "code": "import { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, forwardRef, booleanAttribute, numberAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, HostListener, NgModule } from '@angular/core';\nimport { Header, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { <PERSON>Handler } from 'primeng/dom';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport { UniqueComponentId } from 'primeng/utils';\n\n/**\n * AccordionTab is a helper component for Accordion.\n * @group Components\n */\nconst _c0 = [\"*\", [[\"p-header\"]]];\nconst _c1 = [\"*\", \"p-header\"];\nconst _c2 = a0 => ({\n  $implicit: a0\n});\nconst _c3 = a0 => ({\n  transitionParams: a0\n});\nconst _c4 = a0 => ({\n  value: \"visible\",\n  params: a0\n});\nconst _c5 = a0 => ({\n  value: \"hidden\",\n  params: a0\n});\nfunction AccordionTab_ng_container_3_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(ctx_r0.accordion.collapseIcon);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.iconClass);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction AccordionTab_ng_container_3_ng_container_1_ChevronDownIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronDownIcon\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.iconClass);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction AccordionTab_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AccordionTab_ng_container_3_ng_container_1_span_1_Template, 1, 4, \"span\", 9)(2, AccordionTab_ng_container_3_ng_container_1_ChevronDownIcon_2_Template, 1, 2, \"ChevronDownIcon\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.accordion.collapseIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.accordion.collapseIcon);\n  }\n}\nfunction AccordionTab_ng_container_3_ng_container_2_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(ctx_r0.accordion.expandIcon);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.iconClass);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction AccordionTab_ng_container_3_ng_container_2_ChevronRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.iconClass);\n    i0.ɵɵattribute(\"aria-hidden\", true);\n  }\n}\nfunction AccordionTab_ng_container_3_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AccordionTab_ng_container_3_ng_container_2_span_1_Template, 1, 4, \"span\", 9)(2, AccordionTab_ng_container_3_ng_container_2_ChevronRightIcon_2_Template, 1, 2, \"ChevronRightIcon\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.accordion.expandIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.accordion.expandIcon);\n  }\n}\nfunction AccordionTab_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AccordionTab_ng_container_3_ng_container_1_Template, 3, 2, \"ng-container\", 3)(2, AccordionTab_ng_container_3_ng_container_2_Template, 3, 2, \"ng-container\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selected);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.selected);\n  }\n}\nfunction AccordionTab_4_ng_template_0_Template(rf, ctx) {}\nfunction AccordionTab_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, AccordionTab_4_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction AccordionTab_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.header, \" \");\n  }\n}\nfunction AccordionTab_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AccordionTab_ng_content_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 1, [\"*ngIf\", \"hasHeaderFacet\"]);\n  }\n}\nfunction AccordionTab_ng_container_11_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction AccordionTab_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, AccordionTab_ng_container_11_ng_container_1_Template, 1, 0, \"ng-container\", 6);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.contentTemplate);\n  }\n}\nconst _c6 = [\"*\"];\nlet AccordionTab = /*#__PURE__*/(() => {\n  class AccordionTab {\n    el;\n    changeDetector;\n    /**\n     * Current id state as a string.\n     * @group Props\n     */\n    id;\n    /**\n     * Used to define the header of the tab.\n     * @group Props\n     */\n    header;\n    /**\n     * Inline style of the tab header.\n     * @group Props\n     */\n    headerStyle;\n    /**\n     * Inline style of the tab.\n     * @group Props\n     */\n    tabStyle;\n    /**\n     * Inline style of the tab content.\n     * @group Props\n     */\n    contentStyle;\n    /**\n     * Style class of the tab.\n     * @group Props\n     */\n    tabStyleClass;\n    /**\n     * Style class of the tab header.\n     * @group Props\n     */\n    headerStyleClass;\n    /**\n     * Style class of the tab content.\n     * @group Props\n     */\n    contentStyleClass;\n    /**\n     * Whether the tab is disabled.\n     * @group Props\n     */\n    disabled;\n    /**\n     * Whether a lazy loaded panel should avoid getting loaded again on reselection.\n     * @group Props\n     */\n    cache = true;\n    /**\n     * Transition options of the animation.\n     * @group Props\n     */\n    transitionOptions = '400ms cubic-bezier(0.86, 0, 0.07, 1)';\n    /**\n     * Position of the icon.\n     * @group Props\n     */\n    iconPos = 'start';\n    /**\n     * The value that returns the selection.\n     * @group Props\n     */\n    get selected() {\n      return this._selected;\n    }\n    set selected(val) {\n      this._selected = val;\n      if (!this.loaded) {\n        if (this._selected && this.cache) {\n          this.loaded = true;\n        }\n        this.changeDetector.detectChanges();\n      }\n    }\n    /**\n     * The aria-level that each accordion header will have. The default value is 2 as per W3C specifications\n     * @group Props\n     */\n    headerAriaLevel = 2;\n    /**\n     * Event triggered by changing the choice.\n     * @param {boolean} value - Boolean value indicates that the option is changed.\n     * @group Emits\n     */\n    selectedChange = new EventEmitter();\n    headerFacet;\n    templates;\n    _selected = false;\n    get iconClass() {\n      if (this.iconPos === 'end') {\n        return 'p-accordion-toggle-icon-end';\n      } else {\n        return 'p-accordion-toggle-icon';\n      }\n    }\n    contentTemplate;\n    headerTemplate;\n    iconTemplate;\n    loaded = false;\n    accordion;\n    constructor(accordion, el, changeDetector) {\n      this.el = el;\n      this.changeDetector = changeDetector;\n      this.accordion = accordion;\n      this.id = UniqueComponentId();\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'content':\n            this.contentTemplate = item.template;\n            break;\n          case 'header':\n            this.headerTemplate = item.template;\n            break;\n          case 'icon':\n            this.iconTemplate = item.template;\n            break;\n          default:\n            this.contentTemplate = item.template;\n            break;\n        }\n      });\n    }\n    toggle(event) {\n      if (this.disabled) {\n        return false;\n      }\n      let index = this.findTabIndex();\n      if (this.selected) {\n        this.selected = false;\n        this.accordion.onClose.emit({\n          originalEvent: event,\n          index: index\n        });\n      } else {\n        if (!this.accordion.multiple) {\n          for (var i = 0; i < this.accordion.tabs.length; i++) {\n            if (this.accordion.tabs[i].selected) {\n              this.accordion.tabs[i].selected = false;\n              this.accordion.tabs[i].selectedChange.emit(false);\n              this.accordion.tabs[i].changeDetector.markForCheck();\n            }\n          }\n        }\n        this.selected = true;\n        this.loaded = true;\n        this.accordion.onOpen.emit({\n          originalEvent: event,\n          index: index\n        });\n      }\n      this.selectedChange.emit(this.selected);\n      this.accordion.updateActiveIndex();\n      this.changeDetector.markForCheck();\n      event?.preventDefault();\n    }\n    findTabIndex() {\n      let index = -1;\n      for (var i = 0; i < this.accordion.tabs.length; i++) {\n        if (this.accordion.tabs[i] == this) {\n          index = i;\n          break;\n        }\n      }\n      return index;\n    }\n    get hasHeaderFacet() {\n      return this.headerFacet && this.headerFacet.length > 0;\n    }\n    onKeydown(event) {\n      switch (event.code) {\n        case 'Enter':\n        case 'Space':\n          this.toggle(event);\n          event.preventDefault();\n          break;\n        default:\n          break;\n      }\n    }\n    getTabHeaderActionId(tabId) {\n      return `${tabId}_header_action`;\n    }\n    getTabContentId(tabId) {\n      return `${tabId}_content`;\n    }\n    ngOnDestroy() {\n      this.accordion.tabs.splice(this.findTabIndex(), 1);\n    }\n    static ɵfac = function AccordionTab_Factory(t) {\n      return new (t || AccordionTab)(i0.ɵɵdirectiveInject(forwardRef(() => Accordion)), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: AccordionTab,\n      selectors: [[\"p-accordionTab\"]],\n      contentQueries: function AccordionTab_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, Header, 4);\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.headerFacet = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        id: \"id\",\n        header: \"header\",\n        headerStyle: \"headerStyle\",\n        tabStyle: \"tabStyle\",\n        contentStyle: \"contentStyle\",\n        tabStyleClass: \"tabStyleClass\",\n        headerStyleClass: \"headerStyleClass\",\n        contentStyleClass: \"contentStyleClass\",\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n        cache: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"cache\", \"cache\", booleanAttribute],\n        transitionOptions: \"transitionOptions\",\n        iconPos: \"iconPos\",\n        selected: \"selected\",\n        headerAriaLevel: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"headerAriaLevel\", \"headerAriaLevel\", numberAttribute]\n      },\n      outputs: {\n        selectedChange: \"selectedChange\"\n      },\n      features: [i0.ɵɵInputTransformsFeature],\n      ngContentSelectors: _c1,\n      decls: 12,\n      vars: 44,\n      consts: [[1, \"p-accordion-tab\", 3, \"ngClass\", \"ngStyle\"], [\"role\", \"heading\", 1, \"p-accordion-header\"], [\"role\", \"button\", 1, \"p-accordion-header-link\", 3, \"click\", \"keydown\", \"ngClass\", \"ngStyle\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"p-accordion-header-text\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [\"role\", \"region\", 1, \"p-toggleable-content\"], [1, \"p-accordion-content\", 3, \"ngClass\", \"ngStyle\"], [3, \"class\", \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\"], [1, \"p-accordion-header-text\"]],\n      template: function AccordionTab_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"a\", 2);\n          i0.ɵɵlistener(\"click\", function AccordionTab_Template_a_click_2_listener($event) {\n            return ctx.toggle($event);\n          })(\"keydown\", function AccordionTab_Template_a_keydown_2_listener($event) {\n            return ctx.onKeydown($event);\n          });\n          i0.ɵɵtemplate(3, AccordionTab_ng_container_3_Template, 3, 2, \"ng-container\", 3)(4, AccordionTab_4_Template, 1, 0, null, 4)(5, AccordionTab_span_5_Template, 2, 1, \"span\", 5)(6, AccordionTab_ng_container_6_Template, 1, 0, \"ng-container\", 6)(7, AccordionTab_ng_content_7_Template, 1, 0, \"ng-content\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8);\n          i0.ɵɵprojection(10);\n          i0.ɵɵtemplate(11, AccordionTab_ng_container_11_Template, 2, 1, \"ng-container\", 3);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"p-accordion-tab-active\", ctx.selected);\n          i0.ɵɵproperty(\"ngClass\", ctx.tabStyleClass)(\"ngStyle\", ctx.tabStyle);\n          i0.ɵɵattribute(\"data-pc-name\", \"accordiontab\");\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"p-highlight\", ctx.selected)(\"p-disabled\", ctx.disabled);\n          i0.ɵɵattribute(\"aria-level\", ctx.headerAriaLevel)(\"data-p-disabled\", ctx.disabled)(\"data-pc-section\", \"header\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", ctx.headerStyleClass)(\"ngStyle\", ctx.headerStyle);\n          i0.ɵɵattribute(\"tabindex\", ctx.disabled ? null : 0)(\"id\", ctx.getTabHeaderActionId(ctx.id))(\"aria-controls\", ctx.getTabContentId(ctx.id))(\"aria-expanded\", ctx.selected)(\"aria-disabled\", ctx.disabled)(\"data-pc-section\", \"headeraction\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.iconTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.iconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(34, _c2, ctx.selected));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.hasHeaderFacet);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.headerTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.hasHeaderFacet);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"@tabContent\", ctx.selected ? i0.ɵɵpureFunction1(38, _c4, i0.ɵɵpureFunction1(36, _c3, ctx.transitionOptions)) : i0.ɵɵpureFunction1(42, _c5, i0.ɵɵpureFunction1(40, _c3, ctx.transitionOptions)));\n          i0.ɵɵattribute(\"id\", ctx.getTabContentId(ctx.id))(\"aria-hidden\", !ctx.selected)(\"aria-labelledby\", ctx.getTabHeaderActionId(ctx.id))(\"data-pc-section\", \"toggleablecontent\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", ctx.contentStyleClass)(\"ngStyle\", ctx.contentStyle);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.contentTemplate && (ctx.cache ? ctx.loaded : ctx.selected));\n        }\n      },\n      dependencies: () => [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, ChevronRightIcon, ChevronDownIcon],\n      styles: [\"@layer primeng{.p-accordion-header-link{cursor:pointer;display:flex;align-items:center;-webkit-user-select:none;user-select:none;position:relative;text-decoration:none}.p-accordion-header-link:focus{z-index:1}.p-accordion-header-text{line-height:1}.p-accordion .p-toggleable-content{overflow:hidden}.p-accordion .p-accordion-tab-active>.p-toggleable-content:not(.ng-animating){overflow:inherit}.p-accordion-toggle-icon-end{order:1;margin-left:auto}.p-accordion-toggle-icon{order:0}}\\n\"],\n      encapsulation: 2,\n      data: {\n        animation: [trigger('tabContent', [state('hidden', style({\n          height: '0',\n          visibility: 'hidden'\n        })), state('visible', style({\n          height: '*',\n          visibility: 'visible'\n        })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => *', animate(0))])]\n      },\n      changeDetection: 0\n    });\n  }\n  return AccordionTab;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Accordion groups a collection of contents in tabs.\n * @group Components\n */\nlet Accordion = /*#__PURE__*/(() => {\n  class Accordion {\n    el;\n    changeDetector;\n    /**\n     * When enabled, multiple tabs can be activated at the same time.\n     * @group Props\n     */\n    multiple = false;\n    /**\n     * Inline style of the tab header and content.\n     * @group Props\n     */\n    style;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Icon of a collapsed tab.\n     * @group Props\n     */\n    expandIcon;\n    /**\n     * Icon of an expanded tab.\n     * @group Props\n     */\n    collapseIcon;\n    /**\n     * Index of the active tab or an array of indexes in multiple mode.\n     * @group Props\n     */\n    get activeIndex() {\n      return this._activeIndex;\n    }\n    set activeIndex(val) {\n      this._activeIndex = val;\n      if (this.preventActiveIndexPropagation) {\n        this.preventActiveIndexPropagation = false;\n        return;\n      }\n      this.updateSelectionState();\n    }\n    /**\n     * When enabled, the focused tab is activated.\n     * @group Props\n     */\n    selectOnFocus = false;\n    /**\n     * The aria-level that each accordion header will have. The default value is 2 as per W3C specifications\n     * @group Props\n     */\n    get headerAriaLevel() {\n      return this._headerAriaLevel;\n    }\n    set headerAriaLevel(val) {\n      if (typeof val === 'number' && val > 0) {\n        this._headerAriaLevel = val;\n      } else if (this._headerAriaLevel !== 2) {\n        this._headerAriaLevel = 2;\n      }\n    }\n    /**\n     * Callback to invoke when an active tab is collapsed by clicking on the header.\n     * @param {AccordionTabCloseEvent} event - Custom tab close event.\n     * @group Emits\n     */\n    onClose = new EventEmitter();\n    /**\n     * Callback to invoke when a tab gets expanded.\n     * @param {AccordionTabOpenEvent} event - Custom tab open event.\n     * @group Emits\n     */\n    onOpen = new EventEmitter();\n    /**\n     * Returns the active index.\n     * @param {number | number[]} value - New index.\n     * @group Emits\n     */\n    activeIndexChange = new EventEmitter();\n    tabList;\n    tabListSubscription = null;\n    _activeIndex;\n    _headerAriaLevel = 2;\n    preventActiveIndexPropagation = false;\n    tabs = [];\n    constructor(el, changeDetector) {\n      this.el = el;\n      this.changeDetector = changeDetector;\n    }\n    onKeydown(event) {\n      switch (event.code) {\n        case 'ArrowDown':\n          this.onTabArrowDownKey(event);\n          break;\n        case 'ArrowUp':\n          this.onTabArrowUpKey(event);\n          break;\n        case 'Home':\n          if (!event.shiftKey) {\n            this.onTabHomeKey(event);\n          }\n          break;\n        case 'End':\n          if (!event.shiftKey) {\n            this.onTabEndKey(event);\n          }\n          break;\n      }\n    }\n    focusedElementIsAccordionHeader() {\n      return document.activeElement.tagName.toLowerCase() === 'a' && document.activeElement.classList.contains('p-accordion-header-link');\n    }\n    onTabArrowDownKey(event) {\n      if (this.focusedElementIsAccordionHeader()) {\n        const nextHeaderAction = this.findNextHeaderAction(event.target.parentElement.parentElement.parentElement);\n        nextHeaderAction ? this.changeFocusedTab(nextHeaderAction) : this.onTabHomeKey(event);\n        event.preventDefault();\n      }\n    }\n    onTabArrowUpKey(event) {\n      if (this.focusedElementIsAccordionHeader()) {\n        const prevHeaderAction = this.findPrevHeaderAction(event.target.parentElement.parentElement.parentElement);\n        prevHeaderAction ? this.changeFocusedTab(prevHeaderAction) : this.onTabEndKey(event);\n        event.preventDefault();\n      }\n    }\n    onTabHomeKey(event) {\n      const firstHeaderAction = this.findFirstHeaderAction();\n      this.changeFocusedTab(firstHeaderAction);\n      event.preventDefault();\n    }\n    changeFocusedTab(element) {\n      if (element) {\n        DomHandler.focus(element);\n        if (this.selectOnFocus) {\n          this.tabs.forEach((tab, i) => {\n            let selected = this.multiple ? this._activeIndex.includes(i) : i === this._activeIndex;\n            if (this.multiple) {\n              if (!this._activeIndex) {\n                this._activeIndex = [];\n              }\n              if (tab.id == element.id) {\n                tab.selected = !tab.selected;\n                if (!this._activeIndex.includes(i)) {\n                  this._activeIndex.push(i);\n                } else {\n                  this._activeIndex = this._activeIndex.filter(ind => ind !== i);\n                }\n              }\n            } else {\n              if (tab.id == element.id) {\n                tab.selected = !tab.selected;\n                this._activeIndex = i;\n              } else {\n                tab.selected = false;\n              }\n            }\n            tab.selectedChange.emit(selected);\n            this.activeIndexChange.emit(this._activeIndex);\n            tab.changeDetector.markForCheck();\n          });\n        }\n      }\n    }\n    findNextHeaderAction(tabElement, selfCheck = false) {\n      const nextTabElement = selfCheck ? tabElement : tabElement.nextElementSibling;\n      const headerElement = DomHandler.findSingle(nextTabElement, '[data-pc-section=\"header\"]');\n      return headerElement ? DomHandler.getAttribute(headerElement, 'data-p-disabled') ? this.findNextHeaderAction(headerElement.parentElement.parentElement) : DomHandler.findSingle(headerElement, '[data-pc-section=\"headeraction\"]') : null;\n    }\n    findPrevHeaderAction(tabElement, selfCheck = false) {\n      const prevTabElement = selfCheck ? tabElement : tabElement.previousElementSibling;\n      const headerElement = DomHandler.findSingle(prevTabElement, '[data-pc-section=\"header\"]');\n      return headerElement ? DomHandler.getAttribute(headerElement, 'data-p-disabled') ? this.findPrevHeaderAction(headerElement.parentElement.parentElement) : DomHandler.findSingle(headerElement, '[data-pc-section=\"headeraction\"]') : null;\n    }\n    findFirstHeaderAction() {\n      const firstEl = this.el.nativeElement.firstElementChild.childNodes[0];\n      return this.findNextHeaderAction(firstEl, true);\n    }\n    findLastHeaderAction() {\n      const childNodes = this.el.nativeElement.firstElementChild.childNodes;\n      const lastEl = childNodes[childNodes.length - 1];\n      return this.findPrevHeaderAction(lastEl, true);\n    }\n    onTabEndKey(event) {\n      const lastHeaderAction = this.findLastHeaderAction();\n      this.changeFocusedTab(lastHeaderAction);\n      event.preventDefault();\n    }\n    ngAfterContentInit() {\n      this.initTabs();\n      this.tabListSubscription = this.tabList.changes.subscribe(_ => {\n        this.initTabs();\n      });\n    }\n    initTabs() {\n      this.tabs = this.tabList.toArray();\n      this.tabs.forEach(tab => {\n        tab.headerAriaLevel = this._headerAriaLevel;\n      });\n      this.updateSelectionState();\n      this.changeDetector.markForCheck();\n    }\n    getBlockableElement() {\n      return this.el.nativeElement.children[0];\n    }\n    updateSelectionState() {\n      if (this.tabs && this.tabs.length && this._activeIndex != null) {\n        for (let i = 0; i < this.tabs.length; i++) {\n          let selected = this.multiple ? this._activeIndex.includes(i) : i === this._activeIndex;\n          let changed = selected !== this.tabs[i].selected;\n          if (changed) {\n            this.tabs[i].selected = selected;\n            this.tabs[i].selectedChange.emit(selected);\n            this.tabs[i].changeDetector.markForCheck();\n          }\n        }\n      }\n    }\n    isTabActive(index) {\n      return this.multiple ? this._activeIndex && this._activeIndex.includes(index) : this._activeIndex === index;\n    }\n    getTabProp(tab, name) {\n      return tab.props ? tab.props[name] : undefined;\n    }\n    updateActiveIndex() {\n      let index = this.multiple ? [] : null;\n      this.tabs.forEach((tab, i) => {\n        if (tab.selected) {\n          if (this.multiple) {\n            index.push(i);\n          } else {\n            index = i;\n            return;\n          }\n        }\n      });\n      this.preventActiveIndexPropagation = true;\n      this._activeIndex = index;\n      this.activeIndexChange.emit(index);\n    }\n    ngOnDestroy() {\n      if (this.tabListSubscription) {\n        this.tabListSubscription.unsubscribe();\n      }\n    }\n    static ɵfac = function Accordion_Factory(t) {\n      return new (t || Accordion)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Accordion,\n      selectors: [[\"p-accordion\"]],\n      contentQueries: function Accordion_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, AccordionTab, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tabList = _t);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      hostBindings: function Accordion_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function Accordion_keydown_HostBindingHandler($event) {\n            return ctx.onKeydown($event);\n          });\n        }\n      },\n      inputs: {\n        multiple: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"multiple\", \"multiple\", booleanAttribute],\n        style: \"style\",\n        styleClass: \"styleClass\",\n        expandIcon: \"expandIcon\",\n        collapseIcon: \"collapseIcon\",\n        activeIndex: \"activeIndex\",\n        selectOnFocus: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"selectOnFocus\", \"selectOnFocus\", booleanAttribute],\n        headerAriaLevel: \"headerAriaLevel\"\n      },\n      outputs: {\n        onClose: \"onClose\",\n        onOpen: \"onOpen\",\n        activeIndexChange: \"activeIndexChange\"\n      },\n      features: [i0.ɵɵInputTransformsFeature],\n      ngContentSelectors: _c6,\n      decls: 2,\n      vars: 4,\n      consts: [[3, \"ngClass\", \"ngStyle\"]],\n      template: function Accordion_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngClass\", \"p-accordion p-component\")(\"ngStyle\", ctx.style);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgStyle],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return Accordion;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet AccordionModule = /*#__PURE__*/(() => {\n  class AccordionModule {\n    static ɵfac = function AccordionModule_Factory(t) {\n      return new (t || AccordionModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: AccordionModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, ChevronRightIcon, ChevronDownIcon, SharedModule]\n    });\n  }\n  return AccordionModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Accordion, AccordionModule, AccordionTab };", "map": {"version": 3, "names": ["trigger", "state", "style", "transition", "animate", "i1", "CommonModule", "i0", "EventEmitter", "forwardRef", "booleanAttribute", "numberAttribute", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Input", "Output", "ContentChildren", "HostListener", "NgModule", "Header", "PrimeTemplate", "SharedModule", "<PERSON><PERSON><PERSON><PERSON>", "ChevronDownIcon", "ChevronRightIcon", "UniqueComponentId", "_c0", "_c1", "_c2", "a0", "$implicit", "_c3", "transitionParams", "_c4", "value", "params", "_c5", "AccordionTab_ng_container_3_ng_container_1_span_1_Template", "rf", "ctx", "ɵɵelement", "ctx_r0", "ɵɵnextContext", "ɵɵclassMap", "accordion", "collapseIcon", "ɵɵproperty", "iconClass", "ɵɵattribute", "AccordionTab_ng_container_3_ng_container_1_ChevronDownIcon_2_Template", "AccordionTab_ng_container_3_ng_container_1_Template", "ɵɵelementContainerStart", "ɵɵtemplate", "ɵɵelementContainerEnd", "ɵɵadvance", "AccordionTab_ng_container_3_ng_container_2_span_1_Template", "expandIcon", "AccordionTab_ng_container_3_ng_container_2_ChevronRightIcon_2_Template", "AccordionTab_ng_container_3_ng_container_2_Template", "AccordionTab_ng_container_3_Template", "selected", "AccordionTab_4_ng_template_0_Template", "AccordionTab_4_Template", "AccordionTab_span_5_Template", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtextInterpolate1", "header", "AccordionTab_ng_container_6_Template", "ɵɵelementContainer", "AccordionTab_ng_content_7_Template", "ɵɵprojection", "AccordionTab_ng_container_11_ng_container_1_Template", "AccordionTab_ng_container_11_Template", "contentTemplate", "_c6", "AccordionTab", "el", "changeDetector", "id", "headerStyle", "tabStyle", "contentStyle", "tabStyleClass", "headerStyleClass", "contentStyleClass", "disabled", "cache", "transitionOptions", "iconPos", "_selected", "val", "loaded", "detectChanges", "headerAriaLevel", "<PERSON><PERSON><PERSON><PERSON>", "headerFacet", "templates", "headerTemplate", "iconTemplate", "constructor", "ngAfterContentInit", "for<PERSON>ach", "item", "getType", "template", "toggle", "event", "index", "findTabIndex", "onClose", "emit", "originalEvent", "multiple", "i", "tabs", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onOpen", "updateActiveIndex", "preventDefault", "hasHeaderFacet", "onKeydown", "code", "getTabHeaderActionId", "tabId", "getTabContentId", "ngOnDestroy", "splice", "ɵfac", "AccordionTab_Factory", "t", "ɵɵdirectiveInject", "Accordion", "ElementRef", "ChangeDetectorRef", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "AccordionTab_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "hostAttrs", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "outputs", "features", "ɵɵInputTransformsFeature", "ngContentSelectors", "decls", "vars", "consts", "AccordionTab_Template", "ɵɵprojectionDef", "ɵɵlistener", "AccordionTab_Template_a_click_2_listener", "$event", "AccordionTab_Template_a_keydown_2_listener", "ɵɵclassProp", "ɵɵpureFunction1", "dependencies", "Ng<PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "styles", "encapsulation", "data", "animation", "height", "visibility", "changeDetection", "ngDevMode", "styleClass", "activeIndex", "_activeIndex", "preventActiveIndexPropagation", "updateSelectionState", "selectOnFocus", "_headerAriaLevel", "activeIndexChange", "tabList", "tabListSubscription", "onTabArrowDownKey", "onTabArrowUpKey", "shift<PERSON>ey", "onTabHomeKey", "onTabEndKey", "focusedElementIsAccordionHeader", "document", "activeElement", "tagName", "toLowerCase", "classList", "contains", "nextHeaderAction", "findNextHeaderAction", "target", "parentElement", "changeFocusedTab", "prevHeaderAction", "findPrevHeaderAction", "firstHeaderAction", "findFirstHeaderAction", "element", "focus", "tab", "includes", "push", "filter", "ind", "tabElement", "<PERSON><PERSON><PERSON><PERSON>", "nextTabElement", "nextElement<PERSON><PERSON>ling", "headerElement", "findSingle", "getAttribute", "prevTabElement", "previousElementSibling", "firstEl", "nativeElement", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "childNodes", "findLastHeaderAction", "lastEl", "lastHeaderAction", "initTabs", "changes", "subscribe", "_", "toArray", "getBlockableElement", "children", "changed", "isTabActive", "getTabProp", "name", "props", "undefined", "unsubscribe", "Accordion_Factory", "Accordion_ContentQueries", "hostBindings", "Accordion_HostBindings", "Accordion_keydown_HostBindingHandler", "Accordion_Template", "AccordionModule", "AccordionModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/primeng/fesm2022/primeng-accordion.mjs"], "sourcesContent": ["import { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, forwardRef, booleanAttribute, numberAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, HostListener, NgModule } from '@angular/core';\nimport { Header, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport { UniqueComponentId } from 'primeng/utils';\n\n/**\n * AccordionTab is a helper component for Accordion.\n * @group Components\n */\nclass AccordionTab {\n    el;\n    changeDetector;\n    /**\n     * Current id state as a string.\n     * @group Props\n     */\n    id;\n    /**\n     * Used to define the header of the tab.\n     * @group Props\n     */\n    header;\n    /**\n     * Inline style of the tab header.\n     * @group Props\n     */\n    headerStyle;\n    /**\n     * Inline style of the tab.\n     * @group Props\n     */\n    tabStyle;\n    /**\n     * Inline style of the tab content.\n     * @group Props\n     */\n    contentStyle;\n    /**\n     * Style class of the tab.\n     * @group Props\n     */\n    tabStyleClass;\n    /**\n     * Style class of the tab header.\n     * @group Props\n     */\n    headerStyleClass;\n    /**\n     * Style class of the tab content.\n     * @group Props\n     */\n    contentStyleClass;\n    /**\n     * Whether the tab is disabled.\n     * @group Props\n     */\n    disabled;\n    /**\n     * Whether a lazy loaded panel should avoid getting loaded again on reselection.\n     * @group Props\n     */\n    cache = true;\n    /**\n     * Transition options of the animation.\n     * @group Props\n     */\n    transitionOptions = '400ms cubic-bezier(0.86, 0, 0.07, 1)';\n    /**\n     * Position of the icon.\n     * @group Props\n     */\n    iconPos = 'start';\n    /**\n     * The value that returns the selection.\n     * @group Props\n     */\n    get selected() {\n        return this._selected;\n    }\n    set selected(val) {\n        this._selected = val;\n        if (!this.loaded) {\n            if (this._selected && this.cache) {\n                this.loaded = true;\n            }\n            this.changeDetector.detectChanges();\n        }\n    }\n    /**\n     * The aria-level that each accordion header will have. The default value is 2 as per W3C specifications\n     * @group Props\n     */\n    headerAriaLevel = 2;\n    /**\n     * Event triggered by changing the choice.\n     * @param {boolean} value - Boolean value indicates that the option is changed.\n     * @group Emits\n     */\n    selectedChange = new EventEmitter();\n    headerFacet;\n    templates;\n    _selected = false;\n    get iconClass() {\n        if (this.iconPos === 'end') {\n            return 'p-accordion-toggle-icon-end';\n        }\n        else {\n            return 'p-accordion-toggle-icon';\n        }\n    }\n    contentTemplate;\n    headerTemplate;\n    iconTemplate;\n    loaded = false;\n    accordion;\n    constructor(accordion, el, changeDetector) {\n        this.el = el;\n        this.changeDetector = changeDetector;\n        this.accordion = accordion;\n        this.id = UniqueComponentId();\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'content':\n                    this.contentTemplate = item.template;\n                    break;\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n                case 'icon':\n                    this.iconTemplate = item.template;\n                    break;\n                default:\n                    this.contentTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    toggle(event) {\n        if (this.disabled) {\n            return false;\n        }\n        let index = this.findTabIndex();\n        if (this.selected) {\n            this.selected = false;\n            this.accordion.onClose.emit({ originalEvent: event, index: index });\n        }\n        else {\n            if (!this.accordion.multiple) {\n                for (var i = 0; i < this.accordion.tabs.length; i++) {\n                    if (this.accordion.tabs[i].selected) {\n                        this.accordion.tabs[i].selected = false;\n                        this.accordion.tabs[i].selectedChange.emit(false);\n                        this.accordion.tabs[i].changeDetector.markForCheck();\n                    }\n                }\n            }\n            this.selected = true;\n            this.loaded = true;\n            this.accordion.onOpen.emit({ originalEvent: event, index: index });\n        }\n        this.selectedChange.emit(this.selected);\n        this.accordion.updateActiveIndex();\n        this.changeDetector.markForCheck();\n        event?.preventDefault();\n    }\n    findTabIndex() {\n        let index = -1;\n        for (var i = 0; i < this.accordion.tabs.length; i++) {\n            if (this.accordion.tabs[i] == this) {\n                index = i;\n                break;\n            }\n        }\n        return index;\n    }\n    get hasHeaderFacet() {\n        return this.headerFacet && this.headerFacet.length > 0;\n    }\n    onKeydown(event) {\n        switch (event.code) {\n            case 'Enter':\n            case 'Space':\n                this.toggle(event);\n                event.preventDefault();\n                break;\n            default:\n                break;\n        }\n    }\n    getTabHeaderActionId(tabId) {\n        return `${tabId}_header_action`;\n    }\n    getTabContentId(tabId) {\n        return `${tabId}_content`;\n    }\n    ngOnDestroy() {\n        this.accordion.tabs.splice(this.findTabIndex(), 1);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: AccordionTab, deps: [{ token: forwardRef(() => Accordion) }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"17.3.7\", type: AccordionTab, selector: \"p-accordionTab\", inputs: { id: \"id\", header: \"header\", headerStyle: \"headerStyle\", tabStyle: \"tabStyle\", contentStyle: \"contentStyle\", tabStyleClass: \"tabStyleClass\", headerStyleClass: \"headerStyleClass\", contentStyleClass: \"contentStyleClass\", disabled: [\"disabled\", \"disabled\", booleanAttribute], cache: [\"cache\", \"cache\", booleanAttribute], transitionOptions: \"transitionOptions\", iconPos: \"iconPos\", selected: \"selected\", headerAriaLevel: [\"headerAriaLevel\", \"headerAriaLevel\", numberAttribute] }, outputs: { selectedChange: \"selectedChange\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"headerFacet\", predicate: Header }, { propertyName: \"templates\", predicate: PrimeTemplate }], ngImport: i0, template: `\n        <div class=\"p-accordion-tab\" [class.p-accordion-tab-active]=\"selected\" [ngClass]=\"tabStyleClass\" [ngStyle]=\"tabStyle\" [attr.data-pc-name]=\"'accordiontab'\">\n            <div class=\"p-accordion-header\" role=\"heading\" [attr.aria-level]=\"headerAriaLevel\" [class.p-highlight]=\"selected\" [class.p-disabled]=\"disabled\" [attr.data-p-disabled]=\"disabled\" [attr.data-pc-section]=\"'header'\">\n                <a\n                    [ngClass]=\"headerStyleClass\"\n                    [ngStyle]=\"headerStyle\"\n                    role=\"button\"\n                    class=\"p-accordion-header-link\"\n                    (click)=\"toggle($event)\"\n                    (keydown)=\"onKeydown($event)\"\n                    [attr.tabindex]=\"disabled ? null : 0\"\n                    [attr.id]=\"getTabHeaderActionId(id)\"\n                    [attr.aria-controls]=\"getTabContentId(id)\"\n                    [attr.aria-expanded]=\"selected\"\n                    [attr.aria-disabled]=\"disabled\"\n                    [attr.data-pc-section]=\"'headeraction'\"\n                >\n                    <ng-container *ngIf=\"!iconTemplate\">\n                        <ng-container *ngIf=\"selected\">\n                            <span *ngIf=\"accordion.collapseIcon\" [class]=\"accordion.collapseIcon\" [ngClass]=\"iconClass\" [attr.aria-hidden]=\"true\"></span>\n                            <ChevronDownIcon *ngIf=\"!accordion.collapseIcon\" [ngClass]=\"iconClass\" [attr.aria-hidden]=\"true\" />\n                        </ng-container>\n                        <ng-container *ngIf=\"!selected\">\n                            <span *ngIf=\"accordion.expandIcon\" [class]=\"accordion.expandIcon\" [ngClass]=\"iconClass\" [attr.aria-hidden]=\"true\"></span>\n                            <ChevronRightIcon *ngIf=\"!accordion.expandIcon\" [ngClass]=\"iconClass\" [attr.aria-hidden]=\"true\" />\n                        </ng-container>\n                    </ng-container>\n                    <ng-template *ngTemplateOutlet=\"iconTemplate; context: { $implicit: selected }\"></ng-template>\n                    <span class=\"p-accordion-header-text\" *ngIf=\"!hasHeaderFacet\">\n                        {{ header }}\n                    </span>\n                    <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                    <ng-content select=\"p-header\" *ngIf=\"hasHeaderFacet\"></ng-content>\n                </a>\n            </div>\n            <div\n                [attr.id]=\"getTabContentId(id)\"\n                class=\"p-toggleable-content\"\n                [@tabContent]=\"selected ? { value: 'visible', params: { transitionParams: transitionOptions } } : { value: 'hidden', params: { transitionParams: transitionOptions } }\"\n                role=\"region\"\n                [attr.aria-hidden]=\"!selected\"\n                [attr.aria-labelledby]=\"getTabHeaderActionId(id)\"\n                [attr.data-pc-section]=\"'toggleablecontent'\"\n            >\n                <div class=\"p-accordion-content\" [ngClass]=\"contentStyleClass\" [ngStyle]=\"contentStyle\">\n                    <ng-content></ng-content>\n                    <ng-container *ngIf=\"contentTemplate && (cache ? loaded : selected)\">\n                        <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                    </ng-container>\n                </div>\n            </div>\n        </div>\n    `, isInline: true, styles: [\"@layer primeng{.p-accordion-header-link{cursor:pointer;display:flex;align-items:center;-webkit-user-select:none;user-select:none;position:relative;text-decoration:none}.p-accordion-header-link:focus{z-index:1}.p-accordion-header-text{line-height:1}.p-accordion .p-toggleable-content{overflow:hidden}.p-accordion .p-accordion-tab-active>.p-toggleable-content:not(.ng-animating){overflow:inherit}.p-accordion-toggle-icon-end{order:1;margin-left:auto}.p-accordion-toggle-icon{order:0}}\\n\"], dependencies: [{ kind: \"directive\", type: i0.forwardRef(() => i1.NgClass), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgIf), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgTemplateOutlet), selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgStyle), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"component\", type: i0.forwardRef(() => ChevronRightIcon), selector: \"ChevronRightIcon\" }, { kind: \"component\", type: i0.forwardRef(() => ChevronDownIcon), selector: \"ChevronDownIcon\" }], animations: [\n            trigger('tabContent', [\n                state('hidden', style({\n                    height: '0',\n                    visibility: 'hidden'\n                })),\n                state('visible', style({\n                    height: '*',\n                    visibility: 'visible'\n                })),\n                transition('visible <=> hidden', [animate('{{transitionParams}}')]),\n                transition('void => *', animate(0))\n            ])\n        ], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: AccordionTab, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-accordionTab', template: `\n        <div class=\"p-accordion-tab\" [class.p-accordion-tab-active]=\"selected\" [ngClass]=\"tabStyleClass\" [ngStyle]=\"tabStyle\" [attr.data-pc-name]=\"'accordiontab'\">\n            <div class=\"p-accordion-header\" role=\"heading\" [attr.aria-level]=\"headerAriaLevel\" [class.p-highlight]=\"selected\" [class.p-disabled]=\"disabled\" [attr.data-p-disabled]=\"disabled\" [attr.data-pc-section]=\"'header'\">\n                <a\n                    [ngClass]=\"headerStyleClass\"\n                    [ngStyle]=\"headerStyle\"\n                    role=\"button\"\n                    class=\"p-accordion-header-link\"\n                    (click)=\"toggle($event)\"\n                    (keydown)=\"onKeydown($event)\"\n                    [attr.tabindex]=\"disabled ? null : 0\"\n                    [attr.id]=\"getTabHeaderActionId(id)\"\n                    [attr.aria-controls]=\"getTabContentId(id)\"\n                    [attr.aria-expanded]=\"selected\"\n                    [attr.aria-disabled]=\"disabled\"\n                    [attr.data-pc-section]=\"'headeraction'\"\n                >\n                    <ng-container *ngIf=\"!iconTemplate\">\n                        <ng-container *ngIf=\"selected\">\n                            <span *ngIf=\"accordion.collapseIcon\" [class]=\"accordion.collapseIcon\" [ngClass]=\"iconClass\" [attr.aria-hidden]=\"true\"></span>\n                            <ChevronDownIcon *ngIf=\"!accordion.collapseIcon\" [ngClass]=\"iconClass\" [attr.aria-hidden]=\"true\" />\n                        </ng-container>\n                        <ng-container *ngIf=\"!selected\">\n                            <span *ngIf=\"accordion.expandIcon\" [class]=\"accordion.expandIcon\" [ngClass]=\"iconClass\" [attr.aria-hidden]=\"true\"></span>\n                            <ChevronRightIcon *ngIf=\"!accordion.expandIcon\" [ngClass]=\"iconClass\" [attr.aria-hidden]=\"true\" />\n                        </ng-container>\n                    </ng-container>\n                    <ng-template *ngTemplateOutlet=\"iconTemplate; context: { $implicit: selected }\"></ng-template>\n                    <span class=\"p-accordion-header-text\" *ngIf=\"!hasHeaderFacet\">\n                        {{ header }}\n                    </span>\n                    <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n                    <ng-content select=\"p-header\" *ngIf=\"hasHeaderFacet\"></ng-content>\n                </a>\n            </div>\n            <div\n                [attr.id]=\"getTabContentId(id)\"\n                class=\"p-toggleable-content\"\n                [@tabContent]=\"selected ? { value: 'visible', params: { transitionParams: transitionOptions } } : { value: 'hidden', params: { transitionParams: transitionOptions } }\"\n                role=\"region\"\n                [attr.aria-hidden]=\"!selected\"\n                [attr.aria-labelledby]=\"getTabHeaderActionId(id)\"\n                [attr.data-pc-section]=\"'toggleablecontent'\"\n            >\n                <div class=\"p-accordion-content\" [ngClass]=\"contentStyleClass\" [ngStyle]=\"contentStyle\">\n                    <ng-content></ng-content>\n                    <ng-container *ngIf=\"contentTemplate && (cache ? loaded : selected)\">\n                        <ng-container *ngTemplateOutlet=\"contentTemplate\"></ng-container>\n                    </ng-container>\n                </div>\n            </div>\n        </div>\n    `, animations: [\n                        trigger('tabContent', [\n                            state('hidden', style({\n                                height: '0',\n                                visibility: 'hidden'\n                            })),\n                            state('visible', style({\n                                height: '*',\n                                visibility: 'visible'\n                            })),\n                            transition('visible <=> hidden', [animate('{{transitionParams}}')]),\n                            transition('void => *', animate(0))\n                        ])\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        class: 'p-element'\n                    }, styles: [\"@layer primeng{.p-accordion-header-link{cursor:pointer;display:flex;align-items:center;-webkit-user-select:none;user-select:none;position:relative;text-decoration:none}.p-accordion-header-link:focus{z-index:1}.p-accordion-header-text{line-height:1}.p-accordion .p-toggleable-content{overflow:hidden}.p-accordion .p-accordion-tab-active>.p-toggleable-content:not(.ng-animating){overflow:inherit}.p-accordion-toggle-icon-end{order:1;margin-left:auto}.p-accordion-toggle-icon{order:0}}\\n\"] }]\n        }], ctorParameters: () => [{ type: Accordion, decorators: [{\n                    type: Inject,\n                    args: [forwardRef(() => Accordion)]\n                }] }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }], propDecorators: { id: [{\n                type: Input\n            }], header: [{\n                type: Input\n            }], headerStyle: [{\n                type: Input\n            }], tabStyle: [{\n                type: Input\n            }], contentStyle: [{\n                type: Input\n            }], tabStyleClass: [{\n                type: Input\n            }], headerStyleClass: [{\n                type: Input\n            }], contentStyleClass: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], cache: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], transitionOptions: [{\n                type: Input\n            }], iconPos: [{\n                type: Input\n            }], selected: [{\n                type: Input\n            }], headerAriaLevel: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], selectedChange: [{\n                type: Output\n            }], headerFacet: [{\n                type: ContentChildren,\n                args: [Header]\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }] } });\n/**\n * Accordion groups a collection of contents in tabs.\n * @group Components\n */\nclass Accordion {\n    el;\n    changeDetector;\n    /**\n     * When enabled, multiple tabs can be activated at the same time.\n     * @group Props\n     */\n    multiple = false;\n    /**\n     * Inline style of the tab header and content.\n     * @group Props\n     */\n    style;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Icon of a collapsed tab.\n     * @group Props\n     */\n    expandIcon;\n    /**\n     * Icon of an expanded tab.\n     * @group Props\n     */\n    collapseIcon;\n    /**\n     * Index of the active tab or an array of indexes in multiple mode.\n     * @group Props\n     */\n    get activeIndex() {\n        return this._activeIndex;\n    }\n    set activeIndex(val) {\n        this._activeIndex = val;\n        if (this.preventActiveIndexPropagation) {\n            this.preventActiveIndexPropagation = false;\n            return;\n        }\n        this.updateSelectionState();\n    }\n    /**\n     * When enabled, the focused tab is activated.\n     * @group Props\n     */\n    selectOnFocus = false;\n    /**\n     * The aria-level that each accordion header will have. The default value is 2 as per W3C specifications\n     * @group Props\n     */\n    get headerAriaLevel() {\n        return this._headerAriaLevel;\n    }\n    set headerAriaLevel(val) {\n        if (typeof val === 'number' && val > 0) {\n            this._headerAriaLevel = val;\n        }\n        else if (this._headerAriaLevel !== 2) {\n            this._headerAriaLevel = 2;\n        }\n    }\n    /**\n     * Callback to invoke when an active tab is collapsed by clicking on the header.\n     * @param {AccordionTabCloseEvent} event - Custom tab close event.\n     * @group Emits\n     */\n    onClose = new EventEmitter();\n    /**\n     * Callback to invoke when a tab gets expanded.\n     * @param {AccordionTabOpenEvent} event - Custom tab open event.\n     * @group Emits\n     */\n    onOpen = new EventEmitter();\n    /**\n     * Returns the active index.\n     * @param {number | number[]} value - New index.\n     * @group Emits\n     */\n    activeIndexChange = new EventEmitter();\n    tabList;\n    tabListSubscription = null;\n    _activeIndex;\n    _headerAriaLevel = 2;\n    preventActiveIndexPropagation = false;\n    tabs = [];\n    constructor(el, changeDetector) {\n        this.el = el;\n        this.changeDetector = changeDetector;\n    }\n    onKeydown(event) {\n        switch (event.code) {\n            case 'ArrowDown':\n                this.onTabArrowDownKey(event);\n                break;\n            case 'ArrowUp':\n                this.onTabArrowUpKey(event);\n                break;\n            case 'Home':\n                if (!event.shiftKey) {\n                    this.onTabHomeKey(event);\n                }\n                break;\n            case 'End':\n                if (!event.shiftKey) {\n                    this.onTabEndKey(event);\n                }\n                break;\n        }\n    }\n    focusedElementIsAccordionHeader() {\n        return document.activeElement.tagName.toLowerCase() === 'a' && document.activeElement.classList.contains('p-accordion-header-link');\n    }\n    onTabArrowDownKey(event) {\n        if (this.focusedElementIsAccordionHeader()) {\n            const nextHeaderAction = this.findNextHeaderAction(event.target.parentElement.parentElement.parentElement);\n            nextHeaderAction ? this.changeFocusedTab(nextHeaderAction) : this.onTabHomeKey(event);\n            event.preventDefault();\n        }\n    }\n    onTabArrowUpKey(event) {\n        if (this.focusedElementIsAccordionHeader()) {\n            const prevHeaderAction = this.findPrevHeaderAction(event.target.parentElement.parentElement.parentElement);\n            prevHeaderAction ? this.changeFocusedTab(prevHeaderAction) : this.onTabEndKey(event);\n            event.preventDefault();\n        }\n    }\n    onTabHomeKey(event) {\n        const firstHeaderAction = this.findFirstHeaderAction();\n        this.changeFocusedTab(firstHeaderAction);\n        event.preventDefault();\n    }\n    changeFocusedTab(element) {\n        if (element) {\n            DomHandler.focus(element);\n            if (this.selectOnFocus) {\n                this.tabs.forEach((tab, i) => {\n                    let selected = this.multiple ? this._activeIndex.includes(i) : i === this._activeIndex;\n                    if (this.multiple) {\n                        if (!this._activeIndex) {\n                            this._activeIndex = [];\n                        }\n                        if (tab.id == element.id) {\n                            tab.selected = !tab.selected;\n                            if (!this._activeIndex.includes(i)) {\n                                this._activeIndex.push(i);\n                            }\n                            else {\n                                this._activeIndex = this._activeIndex.filter((ind) => ind !== i);\n                            }\n                        }\n                    }\n                    else {\n                        if (tab.id == element.id) {\n                            tab.selected = !tab.selected;\n                            this._activeIndex = i;\n                        }\n                        else {\n                            tab.selected = false;\n                        }\n                    }\n                    tab.selectedChange.emit(selected);\n                    this.activeIndexChange.emit(this._activeIndex);\n                    tab.changeDetector.markForCheck();\n                });\n            }\n        }\n    }\n    findNextHeaderAction(tabElement, selfCheck = false) {\n        const nextTabElement = selfCheck ? tabElement : tabElement.nextElementSibling;\n        const headerElement = DomHandler.findSingle(nextTabElement, '[data-pc-section=\"header\"]');\n        return headerElement ? (DomHandler.getAttribute(headerElement, 'data-p-disabled') ? this.findNextHeaderAction(headerElement.parentElement.parentElement) : DomHandler.findSingle(headerElement, '[data-pc-section=\"headeraction\"]')) : null;\n    }\n    findPrevHeaderAction(tabElement, selfCheck = false) {\n        const prevTabElement = selfCheck ? tabElement : tabElement.previousElementSibling;\n        const headerElement = DomHandler.findSingle(prevTabElement, '[data-pc-section=\"header\"]');\n        return headerElement ? (DomHandler.getAttribute(headerElement, 'data-p-disabled') ? this.findPrevHeaderAction(headerElement.parentElement.parentElement) : DomHandler.findSingle(headerElement, '[data-pc-section=\"headeraction\"]')) : null;\n    }\n    findFirstHeaderAction() {\n        const firstEl = this.el.nativeElement.firstElementChild.childNodes[0];\n        return this.findNextHeaderAction(firstEl, true);\n    }\n    findLastHeaderAction() {\n        const childNodes = this.el.nativeElement.firstElementChild.childNodes;\n        const lastEl = childNodes[childNodes.length - 1];\n        return this.findPrevHeaderAction(lastEl, true);\n    }\n    onTabEndKey(event) {\n        const lastHeaderAction = this.findLastHeaderAction();\n        this.changeFocusedTab(lastHeaderAction);\n        event.preventDefault();\n    }\n    ngAfterContentInit() {\n        this.initTabs();\n        this.tabListSubscription = this.tabList.changes.subscribe((_) => {\n            this.initTabs();\n        });\n    }\n    initTabs() {\n        this.tabs = this.tabList.toArray();\n        this.tabs.forEach((tab) => {\n            tab.headerAriaLevel = this._headerAriaLevel;\n        });\n        this.updateSelectionState();\n        this.changeDetector.markForCheck();\n    }\n    getBlockableElement() {\n        return this.el.nativeElement.children[0];\n    }\n    updateSelectionState() {\n        if (this.tabs && this.tabs.length && this._activeIndex != null) {\n            for (let i = 0; i < this.tabs.length; i++) {\n                let selected = this.multiple ? this._activeIndex.includes(i) : i === this._activeIndex;\n                let changed = selected !== this.tabs[i].selected;\n                if (changed) {\n                    this.tabs[i].selected = selected;\n                    this.tabs[i].selectedChange.emit(selected);\n                    this.tabs[i].changeDetector.markForCheck();\n                }\n            }\n        }\n    }\n    isTabActive(index) {\n        return this.multiple ? this._activeIndex && this._activeIndex.includes(index) : this._activeIndex === index;\n    }\n    getTabProp(tab, name) {\n        return tab.props ? tab.props[name] : undefined;\n    }\n    updateActiveIndex() {\n        let index = this.multiple ? [] : null;\n        this.tabs.forEach((tab, i) => {\n            if (tab.selected) {\n                if (this.multiple) {\n                    index.push(i);\n                }\n                else {\n                    index = i;\n                    return;\n                }\n            }\n        });\n        this.preventActiveIndexPropagation = true;\n        this._activeIndex = index;\n        this.activeIndexChange.emit(index);\n    }\n    ngOnDestroy() {\n        if (this.tabListSubscription) {\n            this.tabListSubscription.unsubscribe();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: Accordion, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"17.3.7\", type: Accordion, selector: \"p-accordion\", inputs: { multiple: [\"multiple\", \"multiple\", booleanAttribute], style: \"style\", styleClass: \"styleClass\", expandIcon: \"expandIcon\", collapseIcon: \"collapseIcon\", activeIndex: \"activeIndex\", selectOnFocus: [\"selectOnFocus\", \"selectOnFocus\", booleanAttribute], headerAriaLevel: \"headerAriaLevel\" }, outputs: { onClose: \"onClose\", onOpen: \"onOpen\", activeIndexChange: \"activeIndexChange\" }, host: { listeners: { \"keydown\": \"onKeydown($event)\" }, classAttribute: \"p-element\" }, queries: [{ propertyName: \"tabList\", predicate: AccordionTab, descendants: true }], ngImport: i0, template: `\n        <div [ngClass]=\"'p-accordion p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <ng-content></ng-content>\n        </div>\n    `, isInline: true, dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: Accordion, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'p-accordion',\n                    template: `\n        <div [ngClass]=\"'p-accordion p-component'\" [ngStyle]=\"style\" [class]=\"styleClass\">\n            <ng-content></ng-content>\n        </div>\n    `,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    host: {\n                        class: 'p-element'\n                    }\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }], propDecorators: { multiple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], expandIcon: [{\n                type: Input\n            }], collapseIcon: [{\n                type: Input\n            }], activeIndex: [{\n                type: Input\n            }], selectOnFocus: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], headerAriaLevel: [{\n                type: Input\n            }], onClose: [{\n                type: Output\n            }], onOpen: [{\n                type: Output\n            }], activeIndexChange: [{\n                type: Output\n            }], tabList: [{\n                type: ContentChildren,\n                args: [AccordionTab, { descendants: true }]\n            }], onKeydown: [{\n                type: HostListener,\n                args: ['keydown', ['$event']]\n            }] } });\nclass AccordionModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: AccordionModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.7\", ngImport: i0, type: AccordionModule, declarations: [Accordion, AccordionTab], imports: [CommonModule, ChevronRightIcon, ChevronDownIcon], exports: [Accordion, AccordionTab, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: AccordionModule, imports: [CommonModule, ChevronRightIcon, ChevronDownIcon, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: AccordionModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, ChevronRightIcon, ChevronDownIcon],\n                    exports: [Accordion, AccordionTab, SharedModule],\n                    declarations: [Accordion, AccordionTab]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Accordion, AccordionModule, AccordionTab };\n"], "mappings": "AAAA,SAASA,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAChF,OAAO,KAAKC,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,eAAe,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AAClN,SAASC,MAAM,EAAEC,aAAa,EAAEC,YAAY,QAAQ,aAAa;AACjE,SAASC,UAAU,QAAQ,aAAa;AACxC,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,gBAAgB,QAAQ,4BAA4B;AAC7D,SAASC,iBAAiB,QAAQ,eAAe;;AAEjD;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAC,EAAA;EAAAC,SAAA,EAAAD;AAAA;AAAA,MAAAE,GAAA,GAAAF,EAAA;EAAAG,gBAAA,EAAAH;AAAA;AAAA,MAAAI,GAAA,GAAAJ,EAAA;EAAAK,KAAA;EAAAC,MAAA,EAAAN;AAAA;AAAA,MAAAO,GAAA,GAAAP,EAAA;EAAAK,KAAA;EAAAC,MAAA,EAAAN;AAAA;AAAA,SAAAQ,2DAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAmM6FjC,EAAE,CAAAmC,SAAA,cAoByD,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GApB5DpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAsC,UAAA,CAAAF,MAAA,CAAAG,SAAA,CAAAC,YAoBC,CAAC;IApBJxC,EAAE,CAAAyC,UAAA,YAAAL,MAAA,CAAAM,SAoBuB,CAAC;IApB1B1C,EAAE,CAAA2C,WAAA;EAAA;AAAA;AAAA,SAAAC,sEAAAX,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFjC,EAAE,CAAAmC,SAAA,yBAqB+B,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GArBlCpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAyC,UAAA,YAAAL,MAAA,CAAAM,SAqBE,CAAC;IArBL1C,EAAE,CAAA2C,WAAA;EAAA;AAAA;AAAA,SAAAE,oDAAAZ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFjC,EAAE,CAAA8C,uBAAA,EAmBzC,CAAC;IAnBsC9C,EAAE,CAAA+C,UAAA,IAAAf,0DAAA,iBAoBkD,CAAC,IAAAY,qEAAA,6BACpB,CAAC;IArBlC5C,EAAE,CAAAgD,qBAAA;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAG,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAiD,SAAA,CAoBjC,CAAC;IApB8BjD,EAAE,CAAAyC,UAAA,SAAAL,MAAA,CAAAG,SAAA,CAAAC,YAoBjC,CAAC;IApB8BxC,EAAE,CAAAiD,SAAA,CAqBrB,CAAC;IArBkBjD,EAAE,CAAAyC,UAAA,UAAAL,MAAA,CAAAG,SAAA,CAAAC,YAqBrB,CAAC;EAAA;AAAA;AAAA,SAAAU,2DAAAjB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArBkBjC,EAAE,CAAAmC,SAAA,cAwBqD,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GAxBxDpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAsC,UAAA,CAAAF,MAAA,CAAAG,SAAA,CAAAY,UAwBH,CAAC;IAxBAnD,EAAE,CAAAyC,UAAA,YAAAL,MAAA,CAAAM,SAwBmB,CAAC;IAxBtB1C,EAAE,CAAA2C,WAAA;EAAA;AAAA;AAAA,SAAAS,uEAAAnB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFjC,EAAE,CAAAmC,SAAA,0BAyB8B,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GAzBjCpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAyC,UAAA,YAAAL,MAAA,CAAAM,SAyBC,CAAC;IAzBJ1C,EAAE,CAAA2C,WAAA;EAAA;AAAA;AAAA,SAAAU,oDAAApB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFjC,EAAE,CAAA8C,uBAAA,EAuBxC,CAAC;IAvBqC9C,EAAE,CAAA+C,UAAA,IAAAG,0DAAA,iBAwB8C,CAAC,IAAAE,sEAAA,8BACjB,CAAC;IAzBjCpD,EAAE,CAAAgD,qBAAA;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAG,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAiD,SAAA,CAwBnC,CAAC;IAxBgCjD,EAAE,CAAAyC,UAAA,SAAAL,MAAA,CAAAG,SAAA,CAAAY,UAwBnC,CAAC;IAxBgCnD,EAAE,CAAAiD,SAAA,CAyBtB,CAAC;IAzBmBjD,EAAE,CAAAyC,UAAA,UAAAL,MAAA,CAAAG,SAAA,CAAAY,UAyBtB,CAAC;EAAA;AAAA;AAAA,SAAAG,qCAAArB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzBmBjC,EAAE,CAAA8C,uBAAA,EAkBxC,CAAC;IAlBqC9C,EAAE,CAAA+C,UAAA,IAAAF,mDAAA,yBAmBzC,CAAC,IAAAQ,mDAAA,yBAIA,CAAC;IAvBqCrD,EAAE,CAAAgD,qBAAA;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAG,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAiD,SAAA,CAmB3C,CAAC;IAnBwCjD,EAAE,CAAAyC,UAAA,SAAAL,MAAA,CAAAmB,QAmB3C,CAAC;IAnBwCvD,EAAE,CAAAiD,SAAA,CAuB1C,CAAC;IAvBuCjD,EAAE,CAAAyC,UAAA,UAAAL,MAAA,CAAAmB,QAuB1C,CAAC;EAAA;AAAA;AAAA,SAAAC,sCAAAvB,EAAA,EAAAC,GAAA;AAAA,SAAAuB,wBAAAxB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvBuCjC,EAAE,CAAA+C,UAAA,IAAAS,qCAAA,qBA4BI,CAAC;EAAA;AAAA;AAAA,SAAAE,6BAAAzB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5BPjC,EAAE,CAAA2D,cAAA,cA6Bd,CAAC;IA7BW3D,EAAE,CAAA4D,MAAA,EA+B5E,CAAC;IA/ByE5D,EAAE,CAAA6D,YAAA,CA+BrE,CAAC;EAAA;EAAA,IAAA5B,EAAA;IAAA,MAAAG,MAAA,GA/BkEpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAiD,SAAA,CA+B5E,CAAC;IA/ByEjD,EAAE,CAAA8D,kBAAA,MAAA1B,MAAA,CAAA2B,MAAA,KA+B5E,CAAC;EAAA;AAAA;AAAA,SAAAC,qCAAA/B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/ByEjC,EAAE,CAAAiE,kBAAA,EAgCZ,CAAC;EAAA;AAAA;AAAA,SAAAC,mCAAAjC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhCSjC,EAAE,CAAAmE,YAAA,kCAiCV,CAAC;EAAA;AAAA;AAAA,SAAAC,qDAAAnC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjCOjC,EAAE,CAAAiE,kBAAA,EAgDP,CAAC;EAAA;AAAA;AAAA,SAAAI,sCAAApC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhDIjC,EAAE,CAAA8C,uBAAA,EA+CP,CAAC;IA/CI9C,EAAE,CAAA+C,UAAA,IAAAqB,oDAAA,yBAgDtB,CAAC;IAhDmBpE,EAAE,CAAAgD,qBAAA;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAG,MAAA,GAAFpC,EAAE,CAAAqC,aAAA;IAAFrC,EAAE,CAAAiD,SAAA,CAgDxB,CAAC;IAhDqBjD,EAAE,CAAAyC,UAAA,qBAAAL,MAAA,CAAAkC,eAgDxB,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,IA/OlEC,YAAY;EAAlB,MAAMA,YAAY,CAAC;IACfC,EAAE;IACFC,cAAc;IACd;AACJ;AACA;AACA;IACIC,EAAE;IACF;AACJ;AACA;AACA;IACIZ,MAAM;IACN;AACJ;AACA;AACA;IACIa,WAAW;IACX;AACJ;AACA;AACA;IACIC,QAAQ;IACR;AACJ;AACA;AACA;IACIC,YAAY;IACZ;AACJ;AACA;AACA;IACIC,aAAa;IACb;AACJ;AACA;AACA;IACIC,gBAAgB;IAChB;AACJ;AACA;AACA;IACIC,iBAAiB;IACjB;AACJ;AACA;AACA;IACIC,QAAQ;IACR;AACJ;AACA;AACA;IACIC,KAAK,GAAG,IAAI;IACZ;AACJ;AACA;AACA;IACIC,iBAAiB,GAAG,sCAAsC;IAC1D;AACJ;AACA;AACA;IACIC,OAAO,GAAG,OAAO;IACjB;AACJ;AACA;AACA;IACI,IAAI9B,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAAC+B,SAAS;IACzB;IACA,IAAI/B,QAAQA,CAACgC,GAAG,EAAE;MACd,IAAI,CAACD,SAAS,GAAGC,GAAG;MACpB,IAAI,CAAC,IAAI,CAACC,MAAM,EAAE;QACd,IAAI,IAAI,CAACF,SAAS,IAAI,IAAI,CAACH,KAAK,EAAE;UAC9B,IAAI,CAACK,MAAM,GAAG,IAAI;QACtB;QACA,IAAI,CAACd,cAAc,CAACe,aAAa,CAAC,CAAC;MACvC;IACJ;IACA;AACJ;AACA;AACA;IACIC,eAAe,GAAG,CAAC;IACnB;AACJ;AACA;AACA;AACA;IACIC,cAAc,GAAG,IAAI1F,YAAY,CAAC,CAAC;IACnC2F,WAAW;IACXC,SAAS;IACTP,SAAS,GAAG,KAAK;IACjB,IAAI5C,SAASA,CAAA,EAAG;MACZ,IAAI,IAAI,CAAC2C,OAAO,KAAK,KAAK,EAAE;QACxB,OAAO,6BAA6B;MACxC,CAAC,MACI;QACD,OAAO,yBAAyB;MACpC;IACJ;IACAf,eAAe;IACfwB,cAAc;IACdC,YAAY;IACZP,MAAM,GAAG,KAAK;IACdjD,SAAS;IACTyD,WAAWA,CAACzD,SAAS,EAAEkC,EAAE,EAAEC,cAAc,EAAE;MACvC,IAAI,CAACD,EAAE,GAAGA,EAAE;MACZ,IAAI,CAACC,cAAc,GAAGA,cAAc;MACpC,IAAI,CAACnC,SAAS,GAAGA,SAAS;MAC1B,IAAI,CAACoC,EAAE,GAAGvD,iBAAiB,CAAC,CAAC;IACjC;IACA6E,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAACJ,SAAS,CAACK,OAAO,CAAEC,IAAI,IAAK;QAC7B,QAAQA,IAAI,CAACC,OAAO,CAAC,CAAC;UAClB,KAAK,SAAS;YACV,IAAI,CAAC9B,eAAe,GAAG6B,IAAI,CAACE,QAAQ;YACpC;UACJ,KAAK,QAAQ;YACT,IAAI,CAACP,cAAc,GAAGK,IAAI,CAACE,QAAQ;YACnC;UACJ,KAAK,MAAM;YACP,IAAI,CAACN,YAAY,GAAGI,IAAI,CAACE,QAAQ;YACjC;UACJ;YACI,IAAI,CAAC/B,eAAe,GAAG6B,IAAI,CAACE,QAAQ;YACpC;QACR;MACJ,CAAC,CAAC;IACN;IACAC,MAAMA,CAACC,KAAK,EAAE;MACV,IAAI,IAAI,CAACrB,QAAQ,EAAE;QACf,OAAO,KAAK;MAChB;MACA,IAAIsB,KAAK,GAAG,IAAI,CAACC,YAAY,CAAC,CAAC;MAC/B,IAAI,IAAI,CAAClD,QAAQ,EAAE;QACf,IAAI,CAACA,QAAQ,GAAG,KAAK;QACrB,IAAI,CAAChB,SAAS,CAACmE,OAAO,CAACC,IAAI,CAAC;UAAEC,aAAa,EAAEL,KAAK;UAAEC,KAAK,EAAEA;QAAM,CAAC,CAAC;MACvE,CAAC,MACI;QACD,IAAI,CAAC,IAAI,CAACjE,SAAS,CAACsE,QAAQ,EAAE;UAC1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACvE,SAAS,CAACwE,IAAI,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;YACjD,IAAI,IAAI,CAACvE,SAAS,CAACwE,IAAI,CAACD,CAAC,CAAC,CAACvD,QAAQ,EAAE;cACjC,IAAI,CAAChB,SAAS,CAACwE,IAAI,CAACD,CAAC,CAAC,CAACvD,QAAQ,GAAG,KAAK;cACvC,IAAI,CAAChB,SAAS,CAACwE,IAAI,CAACD,CAAC,CAAC,CAACnB,cAAc,CAACgB,IAAI,CAAC,KAAK,CAAC;cACjD,IAAI,CAACpE,SAAS,CAACwE,IAAI,CAACD,CAAC,CAAC,CAACpC,cAAc,CAACuC,YAAY,CAAC,CAAC;YACxD;UACJ;QACJ;QACA,IAAI,CAAC1D,QAAQ,GAAG,IAAI;QACpB,IAAI,CAACiC,MAAM,GAAG,IAAI;QAClB,IAAI,CAACjD,SAAS,CAAC2E,MAAM,CAACP,IAAI,CAAC;UAAEC,aAAa,EAAEL,KAAK;UAAEC,KAAK,EAAEA;QAAM,CAAC,CAAC;MACtE;MACA,IAAI,CAACb,cAAc,CAACgB,IAAI,CAAC,IAAI,CAACpD,QAAQ,CAAC;MACvC,IAAI,CAAChB,SAAS,CAAC4E,iBAAiB,CAAC,CAAC;MAClC,IAAI,CAACzC,cAAc,CAACuC,YAAY,CAAC,CAAC;MAClCV,KAAK,EAAEa,cAAc,CAAC,CAAC;IAC3B;IACAX,YAAYA,CAAA,EAAG;MACX,IAAID,KAAK,GAAG,CAAC,CAAC;MACd,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACvE,SAAS,CAACwE,IAAI,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;QACjD,IAAI,IAAI,CAACvE,SAAS,CAACwE,IAAI,CAACD,CAAC,CAAC,IAAI,IAAI,EAAE;UAChCN,KAAK,GAAGM,CAAC;UACT;QACJ;MACJ;MACA,OAAON,KAAK;IAChB;IACA,IAAIa,cAAcA,CAAA,EAAG;MACjB,OAAO,IAAI,CAACzB,WAAW,IAAI,IAAI,CAACA,WAAW,CAACoB,MAAM,GAAG,CAAC;IAC1D;IACAM,SAASA,CAACf,KAAK,EAAE;MACb,QAAQA,KAAK,CAACgB,IAAI;QACd,KAAK,OAAO;QACZ,KAAK,OAAO;UACR,IAAI,CAACjB,MAAM,CAACC,KAAK,CAAC;UAClBA,KAAK,CAACa,cAAc,CAAC,CAAC;UACtB;QACJ;UACI;MACR;IACJ;IACAI,oBAAoBA,CAACC,KAAK,EAAE;MACxB,OAAQ,GAAEA,KAAM,gBAAe;IACnC;IACAC,eAAeA,CAACD,KAAK,EAAE;MACnB,OAAQ,GAAEA,KAAM,UAAS;IAC7B;IACAE,WAAWA,CAAA,EAAG;MACV,IAAI,CAACpF,SAAS,CAACwE,IAAI,CAACa,MAAM,CAAC,IAAI,CAACnB,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC;IACtD;IACA,OAAOoB,IAAI,YAAAC,qBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFvD,YAAY,EAAtBxE,EAAE,CAAAgI,iBAAA,CAAsC9H,UAAU,CAAC,MAAM+H,SAAS,CAAC,GAAnEjI,EAAE,CAAAgI,iBAAA,CAA8EhI,EAAE,CAACkI,UAAU,GAA7FlI,EAAE,CAAAgI,iBAAA,CAAwGhI,EAAE,CAACmI,iBAAiB;IAAA;IACvN,OAAOC,IAAI,kBAD8EpI,EAAE,CAAAqI,iBAAA;MAAAC,IAAA,EACJ9D,YAAY;MAAA+D,SAAA;MAAAC,cAAA,WAAAC,4BAAAxG,EAAA,EAAAC,GAAA,EAAAwG,QAAA;QAAA,IAAAzG,EAAA;UADVjC,EAAE,CAAA2I,cAAA,CAAAD,QAAA,EACqpB5H,MAAM;UAD7pBd,EAAE,CAAA2I,cAAA,CAAAD,QAAA,EACusB3H,aAAa;QAAA;QAAA,IAAAkB,EAAA;UAAA,IAAA2G,EAAA;UADttB5I,EAAE,CAAA6I,cAAA,CAAAD,EAAA,GAAF5I,EAAE,CAAA8I,WAAA,QAAA5G,GAAA,CAAA0D,WAAA,GAAAgD,EAAA;UAAF5I,EAAE,CAAA6I,cAAA,CAAAD,EAAA,GAAF5I,EAAE,CAAA8I,WAAA,QAAA5G,GAAA,CAAA2D,SAAA,GAAA+C,EAAA;QAAA;MAAA;MAAAG,SAAA;MAAAC,MAAA;QAAArE,EAAA;QAAAZ,MAAA;QAAAa,WAAA;QAAAC,QAAA;QAAAC,YAAA;QAAAC,aAAA;QAAAC,gBAAA;QAAAC,iBAAA;QAAAC,QAAA,GAAFlF,EAAE,CAAAiJ,YAAA,CAAAC,0BAAA,0BAC6S/I,gBAAgB;QAAAgF,KAAA,GAD/TnF,EAAE,CAAAiJ,YAAA,CAAAC,0BAAA,oBAC0V/I,gBAAgB;QAAAiF,iBAAA;QAAAC,OAAA;QAAA9B,QAAA;QAAAmC,eAAA,GAD5W1F,EAAE,CAAAiJ,YAAA,CAAAC,0BAAA,wCACuf9I,eAAe;MAAA;MAAA+I,OAAA;QAAAxD,cAAA;MAAA;MAAAyD,QAAA,GADxgBpJ,EAAE,CAAAqJ,wBAAA;MAAAC,kBAAA,EAAAhI,GAAA;MAAAiI,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAApD,QAAA,WAAAqD,sBAAAzH,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFjC,EAAE,CAAA2J,eAAA,CAAAtI,GAAA;UAAFrB,EAAE,CAAA2D,cAAA,YAEmE,CAAC,YAC4D,CAAC,UAchN,CAAC;UAjB4E3D,EAAE,CAAA4J,UAAA,mBAAAC,yCAAAC,MAAA;YAAA,OASlE5H,GAAA,CAAAoE,MAAA,CAAAwD,MAAa,CAAC;UAAA,EAAC,qBAAAC,2CAAAD,MAAA;YAAA,OACb5H,GAAA,CAAAoF,SAAA,CAAAwC,MAAgB,CAAC;UAAA,EAAC;UAV4C9J,EAAE,CAAA+C,UAAA,IAAAO,oCAAA,yBAkBxC,CAAC,IAAAG,uBAAA,eAU2C,CAAC,IAAAC,4BAAA,iBACnB,CAAC,IAAAM,oCAAA,yBAGd,CAAC,IAAAE,kCAAA,uBACG,CAAC;UAjCoBlE,EAAE,CAAA6D,YAAA,CAkC5E,CAAC,CACH,CAAC;UAnC2E7D,EAAE,CAAA2D,cAAA,YA4CnF,CAAC,YAC0F,CAAC;UA7CX3D,EAAE,CAAAmE,YAAA,GA8CnD,CAAC;UA9CgDnE,EAAE,CAAA+C,UAAA,KAAAsB,qCAAA,yBA+CP,CAAC;UA/CIrE,EAAE,CAAA6D,YAAA,CAkD1E,CAAC,CACL,CAAC,CACL,CAAC;QAAA;QAAA,IAAA5B,EAAA;UApD+EjC,EAAE,CAAAgK,WAAA,2BAAA9H,GAAA,CAAAqB,QAElB,CAAC;UAFevD,EAAE,CAAAyC,UAAA,YAAAP,GAAA,CAAA6C,aAEQ,CAAC,YAAA7C,GAAA,CAAA2C,QAAoB,CAAC;UAFhC7E,EAAE,CAAA2C,WAAA;UAAF3C,EAAE,CAAAiD,SAAA,CAG6B,CAAC;UAHhCjD,EAAE,CAAAgK,WAAA,gBAAA9H,GAAA,CAAAqB,QAG6B,CAAC,eAAArB,GAAA,CAAAgD,QAA6B,CAAC;UAH9DlF,EAAE,CAAA2C,WAAA,eAAAT,GAAA,CAAAwD,eAAA,qBAAAxD,GAAA,CAAAgD,QAAA;UAAFlF,EAAE,CAAAiD,SAAA,CAKhD,CAAC;UAL6CjD,EAAE,CAAAyC,UAAA,YAAAP,GAAA,CAAA8C,gBAKhD,CAAC,YAAA9C,GAAA,CAAA0C,WACN,CAAC;UANkD5E,EAAE,CAAA2C,WAAA,aAAAT,GAAA,CAAAgD,QAAA,mBAAAhD,GAAA,CAAAsF,oBAAA,CAAAtF,GAAA,CAAAyC,EAAA,oBAAAzC,GAAA,CAAAwF,eAAA,CAAAxF,GAAA,CAAAyC,EAAA,oBAAAzC,GAAA,CAAAqB,QAAA,mBAAArB,GAAA,CAAAgD,QAAA;UAAFlF,EAAE,CAAAiD,SAAA,CAkB1C,CAAC;UAlBuCjD,EAAE,CAAAyC,UAAA,UAAAP,GAAA,CAAA6D,YAkB1C,CAAC;UAlBuC/F,EAAE,CAAAiD,SAAA,CA4B9B,CAAC;UA5B2BjD,EAAE,CAAAyC,UAAA,qBAAAP,GAAA,CAAA6D,YA4B9B,CAAC,4BA5B2B/F,EAAE,CAAAiK,eAAA,KAAA1I,GAAA,EAAAW,GAAA,CAAAqB,QAAA,CA4BE,CAAC;UA5BLvD,EAAE,CAAAiD,SAAA,CA6BhB,CAAC;UA7BajD,EAAE,CAAAyC,UAAA,UAAAP,GAAA,CAAAmF,cA6BhB,CAAC;UA7BarH,EAAE,CAAAiD,SAAA,CAgC7B,CAAC;UAhC0BjD,EAAE,CAAAyC,UAAA,qBAAAP,GAAA,CAAA4D,cAgC7B,CAAC;UAhC0B9F,EAAE,CAAAiD,SAAA,CAiCzB,CAAC;UAjCsBjD,EAAE,CAAAyC,UAAA,SAAAP,GAAA,CAAAmF,cAiCzB,CAAC;UAjCsBrH,EAAE,CAAAiD,SAAA,CAuCuF,CAAC;UAvC1FjD,EAAE,CAAAyC,UAAA,gBAAAP,GAAA,CAAAqB,QAAA,GAAFvD,EAAE,CAAAiK,eAAA,KAAArI,GAAA,EAAF5B,EAAE,CAAAiK,eAAA,KAAAvI,GAAA,EAAAQ,GAAA,CAAAkD,iBAAA,KAAFpF,EAAE,CAAAiK,eAAA,KAAAlI,GAAA,EAAF/B,EAAE,CAAAiK,eAAA,KAAAvI,GAAA,EAAAQ,GAAA,CAAAkD,iBAAA,EAuCuF,CAAC;UAvC1FpF,EAAE,CAAA2C,WAAA,OAAAT,GAAA,CAAAwF,eAAA,CAAAxF,GAAA,CAAAyC,EAAA,mBAAAzC,GAAA,CAAAqB,QAAA,qBAAArB,GAAA,CAAAsF,oBAAA,CAAAtF,GAAA,CAAAyC,EAAA;UAAF3E,EAAE,CAAAiD,SAAA,CA6ClB,CAAC;UA7CejD,EAAE,CAAAyC,UAAA,YAAAP,GAAA,CAAA+C,iBA6ClB,CAAC,YAAA/C,GAAA,CAAA4C,YAAwB,CAAC;UA7CV9E,EAAE,CAAAiD,SAAA,EA+CT,CAAC;UA/CMjD,EAAE,CAAAyC,UAAA,SAAAP,GAAA,CAAAoC,eAAA,KAAApC,GAAA,CAAAiD,KAAA,GAAAjD,GAAA,CAAAsD,MAAA,GAAAtD,GAAA,CAAAqB,QAAA,CA+CT,CAAC;QAAA;MAAA;MAAA2G,YAAA,EAAAA,CAAA,MAMgfpK,EAAE,CAACqK,OAAO,EAAyGrK,EAAE,CAACsK,IAAI,EAAkHtK,EAAE,CAACuK,gBAAgB,EAAyKvK,EAAE,CAACwK,OAAO,EAAgGnJ,gBAAgB,EAAkFD,eAAe;MAAAqJ,MAAA;MAAAC,aAAA;MAAAC,IAAA;QAAAC,SAAA,EAA+C,CAC7uCjL,OAAO,CAAC,YAAY,EAAE,CAClBC,KAAK,CAAC,QAAQ,EAAEC,KAAK,CAAC;UAClBgL,MAAM,EAAE,GAAG;UACXC,UAAU,EAAE;QAChB,CAAC,CAAC,CAAC,EACHlL,KAAK,CAAC,SAAS,EAAEC,KAAK,CAAC;UACnBgL,MAAM,EAAE,GAAG;UACXC,UAAU,EAAE;QAChB,CAAC,CAAC,CAAC,EACHhL,UAAU,CAAC,oBAAoB,EAAE,CAACC,OAAO,CAAC,sBAAsB,CAAC,CAAC,CAAC,EACnED,UAAU,CAAC,WAAW,EAAEC,OAAO,CAAC,CAAC,CAAC,CAAC,CACtC,CAAC;MACL;MAAAgL,eAAA;IAAA;EACT;EAAC,OAlQKrG,YAAY;AAAA;AAmQlB;EAAA,QAAAsG,SAAA,oBAAAA,SAAA;AAAA;AAiHA;AACA;AACA;AACA;AAHA,IAIM7C,SAAS;EAAf,MAAMA,SAAS,CAAC;IACZxD,EAAE;IACFC,cAAc;IACd;AACJ;AACA;AACA;IACImC,QAAQ,GAAG,KAAK;IAChB;AACJ;AACA;AACA;IACIlH,KAAK;IACL;AACJ;AACA;AACA;IACIoL,UAAU;IACV;AACJ;AACA;AACA;IACI5H,UAAU;IACV;AACJ;AACA;AACA;IACIX,YAAY;IACZ;AACJ;AACA;AACA;IACI,IAAIwI,WAAWA,CAAA,EAAG;MACd,OAAO,IAAI,CAACC,YAAY;IAC5B;IACA,IAAID,WAAWA,CAACzF,GAAG,EAAE;MACjB,IAAI,CAAC0F,YAAY,GAAG1F,GAAG;MACvB,IAAI,IAAI,CAAC2F,6BAA6B,EAAE;QACpC,IAAI,CAACA,6BAA6B,GAAG,KAAK;QAC1C;MACJ;MACA,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC/B;IACA;AACJ;AACA;AACA;IACIC,aAAa,GAAG,KAAK;IACrB;AACJ;AACA;AACA;IACI,IAAI1F,eAAeA,CAAA,EAAG;MAClB,OAAO,IAAI,CAAC2F,gBAAgB;IAChC;IACA,IAAI3F,eAAeA,CAACH,GAAG,EAAE;MACrB,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,GAAG,CAAC,EAAE;QACpC,IAAI,CAAC8F,gBAAgB,GAAG9F,GAAG;MAC/B,CAAC,MACI,IAAI,IAAI,CAAC8F,gBAAgB,KAAK,CAAC,EAAE;QAClC,IAAI,CAACA,gBAAgB,GAAG,CAAC;MAC7B;IACJ;IACA;AACJ;AACA;AACA;AACA;IACI3E,OAAO,GAAG,IAAIzG,YAAY,CAAC,CAAC;IAC5B;AACJ;AACA;AACA;AACA;IACIiH,MAAM,GAAG,IAAIjH,YAAY,CAAC,CAAC;IAC3B;AACJ;AACA;AACA;AACA;IACIqL,iBAAiB,GAAG,IAAIrL,YAAY,CAAC,CAAC;IACtCsL,OAAO;IACPC,mBAAmB,GAAG,IAAI;IAC1BP,YAAY;IACZI,gBAAgB,GAAG,CAAC;IACpBH,6BAA6B,GAAG,KAAK;IACrCnE,IAAI,GAAG,EAAE;IACTf,WAAWA,CAACvB,EAAE,EAAEC,cAAc,EAAE;MAC5B,IAAI,CAACD,EAAE,GAAGA,EAAE;MACZ,IAAI,CAACC,cAAc,GAAGA,cAAc;IACxC;IACA4C,SAASA,CAACf,KAAK,EAAE;MACb,QAAQA,KAAK,CAACgB,IAAI;QACd,KAAK,WAAW;UACZ,IAAI,CAACkE,iBAAiB,CAAClF,KAAK,CAAC;UAC7B;QACJ,KAAK,SAAS;UACV,IAAI,CAACmF,eAAe,CAACnF,KAAK,CAAC;UAC3B;QACJ,KAAK,MAAM;UACP,IAAI,CAACA,KAAK,CAACoF,QAAQ,EAAE;YACjB,IAAI,CAACC,YAAY,CAACrF,KAAK,CAAC;UAC5B;UACA;QACJ,KAAK,KAAK;UACN,IAAI,CAACA,KAAK,CAACoF,QAAQ,EAAE;YACjB,IAAI,CAACE,WAAW,CAACtF,KAAK,CAAC;UAC3B;UACA;MACR;IACJ;IACAuF,+BAA+BA,CAAA,EAAG;MAC9B,OAAOC,QAAQ,CAACC,aAAa,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,GAAG,IAAIH,QAAQ,CAACC,aAAa,CAACG,SAAS,CAACC,QAAQ,CAAC,yBAAyB,CAAC;IACvI;IACAX,iBAAiBA,CAAClF,KAAK,EAAE;MACrB,IAAI,IAAI,CAACuF,+BAA+B,CAAC,CAAC,EAAE;QACxC,MAAMO,gBAAgB,GAAG,IAAI,CAACC,oBAAoB,CAAC/F,KAAK,CAACgG,MAAM,CAACC,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;QAC1GH,gBAAgB,GAAG,IAAI,CAACI,gBAAgB,CAACJ,gBAAgB,CAAC,GAAG,IAAI,CAACT,YAAY,CAACrF,KAAK,CAAC;QACrFA,KAAK,CAACa,cAAc,CAAC,CAAC;MAC1B;IACJ;IACAsE,eAAeA,CAACnF,KAAK,EAAE;MACnB,IAAI,IAAI,CAACuF,+BAA+B,CAAC,CAAC,EAAE;QACxC,MAAMY,gBAAgB,GAAG,IAAI,CAACC,oBAAoB,CAACpG,KAAK,CAACgG,MAAM,CAACC,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC;QAC1GE,gBAAgB,GAAG,IAAI,CAACD,gBAAgB,CAACC,gBAAgB,CAAC,GAAG,IAAI,CAACb,WAAW,CAACtF,KAAK,CAAC;QACpFA,KAAK,CAACa,cAAc,CAAC,CAAC;MAC1B;IACJ;IACAwE,YAAYA,CAACrF,KAAK,EAAE;MAChB,MAAMqG,iBAAiB,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;MACtD,IAAI,CAACJ,gBAAgB,CAACG,iBAAiB,CAAC;MACxCrG,KAAK,CAACa,cAAc,CAAC,CAAC;IAC1B;IACAqF,gBAAgBA,CAACK,OAAO,EAAE;MACtB,IAAIA,OAAO,EAAE;QACT7L,UAAU,CAAC8L,KAAK,CAACD,OAAO,CAAC;QACzB,IAAI,IAAI,CAAC1B,aAAa,EAAE;UACpB,IAAI,CAACrE,IAAI,CAACb,OAAO,CAAC,CAAC8G,GAAG,EAAElG,CAAC,KAAK;YAC1B,IAAIvD,QAAQ,GAAG,IAAI,CAACsD,QAAQ,GAAG,IAAI,CAACoE,YAAY,CAACgC,QAAQ,CAACnG,CAAC,CAAC,GAAGA,CAAC,KAAK,IAAI,CAACmE,YAAY;YACtF,IAAI,IAAI,CAACpE,QAAQ,EAAE;cACf,IAAI,CAAC,IAAI,CAACoE,YAAY,EAAE;gBACpB,IAAI,CAACA,YAAY,GAAG,EAAE;cAC1B;cACA,IAAI+B,GAAG,CAACrI,EAAE,IAAImI,OAAO,CAACnI,EAAE,EAAE;gBACtBqI,GAAG,CAACzJ,QAAQ,GAAG,CAACyJ,GAAG,CAACzJ,QAAQ;gBAC5B,IAAI,CAAC,IAAI,CAAC0H,YAAY,CAACgC,QAAQ,CAACnG,CAAC,CAAC,EAAE;kBAChC,IAAI,CAACmE,YAAY,CAACiC,IAAI,CAACpG,CAAC,CAAC;gBAC7B,CAAC,MACI;kBACD,IAAI,CAACmE,YAAY,GAAG,IAAI,CAACA,YAAY,CAACkC,MAAM,CAAEC,GAAG,IAAKA,GAAG,KAAKtG,CAAC,CAAC;gBACpE;cACJ;YACJ,CAAC,MACI;cACD,IAAIkG,GAAG,CAACrI,EAAE,IAAImI,OAAO,CAACnI,EAAE,EAAE;gBACtBqI,GAAG,CAACzJ,QAAQ,GAAG,CAACyJ,GAAG,CAACzJ,QAAQ;gBAC5B,IAAI,CAAC0H,YAAY,GAAGnE,CAAC;cACzB,CAAC,MACI;gBACDkG,GAAG,CAACzJ,QAAQ,GAAG,KAAK;cACxB;YACJ;YACAyJ,GAAG,CAACrH,cAAc,CAACgB,IAAI,CAACpD,QAAQ,CAAC;YACjC,IAAI,CAAC+H,iBAAiB,CAAC3E,IAAI,CAAC,IAAI,CAACsE,YAAY,CAAC;YAC9C+B,GAAG,CAACtI,cAAc,CAACuC,YAAY,CAAC,CAAC;UACrC,CAAC,CAAC;QACN;MACJ;IACJ;IACAqF,oBAAoBA,CAACe,UAAU,EAAEC,SAAS,GAAG,KAAK,EAAE;MAChD,MAAMC,cAAc,GAAGD,SAAS,GAAGD,UAAU,GAAGA,UAAU,CAACG,kBAAkB;MAC7E,MAAMC,aAAa,GAAGxM,UAAU,CAACyM,UAAU,CAACH,cAAc,EAAE,4BAA4B,CAAC;MACzF,OAAOE,aAAa,GAAIxM,UAAU,CAAC0M,YAAY,CAACF,aAAa,EAAE,iBAAiB,CAAC,GAAG,IAAI,CAACnB,oBAAoB,CAACmB,aAAa,CAACjB,aAAa,CAACA,aAAa,CAAC,GAAGvL,UAAU,CAACyM,UAAU,CAACD,aAAa,EAAE,kCAAkC,CAAC,GAAI,IAAI;IAC/O;IACAd,oBAAoBA,CAACU,UAAU,EAAEC,SAAS,GAAG,KAAK,EAAE;MAChD,MAAMM,cAAc,GAAGN,SAAS,GAAGD,UAAU,GAAGA,UAAU,CAACQ,sBAAsB;MACjF,MAAMJ,aAAa,GAAGxM,UAAU,CAACyM,UAAU,CAACE,cAAc,EAAE,4BAA4B,CAAC;MACzF,OAAOH,aAAa,GAAIxM,UAAU,CAAC0M,YAAY,CAACF,aAAa,EAAE,iBAAiB,CAAC,GAAG,IAAI,CAACd,oBAAoB,CAACc,aAAa,CAACjB,aAAa,CAACA,aAAa,CAAC,GAAGvL,UAAU,CAACyM,UAAU,CAACD,aAAa,EAAE,kCAAkC,CAAC,GAAI,IAAI;IAC/O;IACAZ,qBAAqBA,CAAA,EAAG;MACpB,MAAMiB,OAAO,GAAG,IAAI,CAACrJ,EAAE,CAACsJ,aAAa,CAACC,iBAAiB,CAACC,UAAU,CAAC,CAAC,CAAC;MACrE,OAAO,IAAI,CAAC3B,oBAAoB,CAACwB,OAAO,EAAE,IAAI,CAAC;IACnD;IACAI,oBAAoBA,CAAA,EAAG;MACnB,MAAMD,UAAU,GAAG,IAAI,CAACxJ,EAAE,CAACsJ,aAAa,CAACC,iBAAiB,CAACC,UAAU;MACrE,MAAME,MAAM,GAAGF,UAAU,CAACA,UAAU,CAACjH,MAAM,GAAG,CAAC,CAAC;MAChD,OAAO,IAAI,CAAC2F,oBAAoB,CAACwB,MAAM,EAAE,IAAI,CAAC;IAClD;IACAtC,WAAWA,CAACtF,KAAK,EAAE;MACf,MAAM6H,gBAAgB,GAAG,IAAI,CAACF,oBAAoB,CAAC,CAAC;MACpD,IAAI,CAACzB,gBAAgB,CAAC2B,gBAAgB,CAAC;MACvC7H,KAAK,CAACa,cAAc,CAAC,CAAC;IAC1B;IACAnB,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAACoI,QAAQ,CAAC,CAAC;MACf,IAAI,CAAC7C,mBAAmB,GAAG,IAAI,CAACD,OAAO,CAAC+C,OAAO,CAACC,SAAS,CAAEC,CAAC,IAAK;QAC7D,IAAI,CAACH,QAAQ,CAAC,CAAC;MACnB,CAAC,CAAC;IACN;IACAA,QAAQA,CAAA,EAAG;MACP,IAAI,CAACtH,IAAI,GAAG,IAAI,CAACwE,OAAO,CAACkD,OAAO,CAAC,CAAC;MAClC,IAAI,CAAC1H,IAAI,CAACb,OAAO,CAAE8G,GAAG,IAAK;QACvBA,GAAG,CAACtH,eAAe,GAAG,IAAI,CAAC2F,gBAAgB;MAC/C,CAAC,CAAC;MACF,IAAI,CAACF,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACzG,cAAc,CAACuC,YAAY,CAAC,CAAC;IACtC;IACAyH,mBAAmBA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACjK,EAAE,CAACsJ,aAAa,CAACY,QAAQ,CAAC,CAAC,CAAC;IAC5C;IACAxD,oBAAoBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAACpE,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,MAAM,IAAI,IAAI,CAACiE,YAAY,IAAI,IAAI,EAAE;QAC5D,KAAK,IAAInE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACC,IAAI,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;UACvC,IAAIvD,QAAQ,GAAG,IAAI,CAACsD,QAAQ,GAAG,IAAI,CAACoE,YAAY,CAACgC,QAAQ,CAACnG,CAAC,CAAC,GAAGA,CAAC,KAAK,IAAI,CAACmE,YAAY;UACtF,IAAI2D,OAAO,GAAGrL,QAAQ,KAAK,IAAI,CAACwD,IAAI,CAACD,CAAC,CAAC,CAACvD,QAAQ;UAChD,IAAIqL,OAAO,EAAE;YACT,IAAI,CAAC7H,IAAI,CAACD,CAAC,CAAC,CAACvD,QAAQ,GAAGA,QAAQ;YAChC,IAAI,CAACwD,IAAI,CAACD,CAAC,CAAC,CAACnB,cAAc,CAACgB,IAAI,CAACpD,QAAQ,CAAC;YAC1C,IAAI,CAACwD,IAAI,CAACD,CAAC,CAAC,CAACpC,cAAc,CAACuC,YAAY,CAAC,CAAC;UAC9C;QACJ;MACJ;IACJ;IACA4H,WAAWA,CAACrI,KAAK,EAAE;MACf,OAAO,IAAI,CAACK,QAAQ,GAAG,IAAI,CAACoE,YAAY,IAAI,IAAI,CAACA,YAAY,CAACgC,QAAQ,CAACzG,KAAK,CAAC,GAAG,IAAI,CAACyE,YAAY,KAAKzE,KAAK;IAC/G;IACAsI,UAAUA,CAAC9B,GAAG,EAAE+B,IAAI,EAAE;MAClB,OAAO/B,GAAG,CAACgC,KAAK,GAAGhC,GAAG,CAACgC,KAAK,CAACD,IAAI,CAAC,GAAGE,SAAS;IAClD;IACA9H,iBAAiBA,CAAA,EAAG;MAChB,IAAIX,KAAK,GAAG,IAAI,CAACK,QAAQ,GAAG,EAAE,GAAG,IAAI;MACrC,IAAI,CAACE,IAAI,CAACb,OAAO,CAAC,CAAC8G,GAAG,EAAElG,CAAC,KAAK;QAC1B,IAAIkG,GAAG,CAACzJ,QAAQ,EAAE;UACd,IAAI,IAAI,CAACsD,QAAQ,EAAE;YACfL,KAAK,CAAC0G,IAAI,CAACpG,CAAC,CAAC;UACjB,CAAC,MACI;YACDN,KAAK,GAAGM,CAAC;YACT;UACJ;QACJ;MACJ,CAAC,CAAC;MACF,IAAI,CAACoE,6BAA6B,GAAG,IAAI;MACzC,IAAI,CAACD,YAAY,GAAGzE,KAAK;MACzB,IAAI,CAAC8E,iBAAiB,CAAC3E,IAAI,CAACH,KAAK,CAAC;IACtC;IACAmB,WAAWA,CAAA,EAAG;MACV,IAAI,IAAI,CAAC6D,mBAAmB,EAAE;QAC1B,IAAI,CAACA,mBAAmB,CAAC0D,WAAW,CAAC,CAAC;MAC1C;IACJ;IACA,OAAOrH,IAAI,YAAAsH,kBAAApH,CAAA;MAAA,YAAAA,CAAA,IAAwFE,SAAS,EApbnBjI,EAAE,CAAAgI,iBAAA,CAobmChI,EAAE,CAACkI,UAAU,GApblDlI,EAAE,CAAAgI,iBAAA,CAob6DhI,EAAE,CAACmI,iBAAiB;IAAA;IAC5K,OAAOC,IAAI,kBArb8EpI,EAAE,CAAAqI,iBAAA;MAAAC,IAAA,EAqbJL,SAAS;MAAAM,SAAA;MAAAC,cAAA,WAAA4G,yBAAAnN,EAAA,EAAAC,GAAA,EAAAwG,QAAA;QAAA,IAAAzG,EAAA;UArbPjC,EAAE,CAAA2I,cAAA,CAAAD,QAAA,EAqb0iBlE,YAAY;QAAA;QAAA,IAAAvC,EAAA;UAAA,IAAA2G,EAAA;UArbxjB5I,EAAE,CAAA6I,cAAA,CAAAD,EAAA,GAAF5I,EAAE,CAAA8I,WAAA,QAAA5G,GAAA,CAAAqJ,OAAA,GAAA3C,EAAA;QAAA;MAAA;MAAAG,SAAA;MAAAsG,YAAA,WAAAC,uBAAArN,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFjC,EAAE,CAAA4J,UAAA,qBAAA2F,qCAAAzF,MAAA;YAAA,OAqbJ5H,GAAA,CAAAoF,SAAA,CAAAwC,MAAgB,CAAC;UAAA,CAAT,CAAC;QAAA;MAAA;MAAAd,MAAA;QAAAnC,QAAA,GArbP7G,EAAE,CAAAiJ,YAAA,CAAAC,0BAAA,0BAqb6E/I,gBAAgB;QAAAR,KAAA;QAAAoL,UAAA;QAAA5H,UAAA;QAAAX,YAAA;QAAAwI,WAAA;QAAAI,aAAA,GArb/FpL,EAAE,CAAAiJ,YAAA,CAAAC,0BAAA,oCAqbgR/I,gBAAgB;QAAAuF,eAAA;MAAA;MAAAyD,OAAA;QAAAzC,OAAA;QAAAQ,MAAA;QAAAoE,iBAAA;MAAA;MAAAlC,QAAA,GArblSpJ,EAAE,CAAAqJ,wBAAA;MAAAC,kBAAA,EAAA/E,GAAA;MAAAgF,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAApD,QAAA,WAAAmJ,mBAAAvN,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFjC,EAAE,CAAA2J,eAAA;UAAF3J,EAAE,CAAA2D,cAAA,YAsbN,CAAC;UAtbG3D,EAAE,CAAAmE,YAAA,EAub3D,CAAC;UAvbwDnE,EAAE,CAAA6D,YAAA,CAwblF,CAAC;QAAA;QAAA,IAAA5B,EAAA;UAxb+EjC,EAAE,CAAAsC,UAAA,CAAAJ,GAAA,CAAA6I,UAsbP,CAAC;UAtbI/K,EAAE,CAAAyC,UAAA,qCAsb9C,CAAC,YAAAP,GAAA,CAAAvC,KAAiB,CAAC;QAAA;MAAA;MAAAuK,YAAA,GAGHpK,EAAE,CAACqK,OAAO,EAAoFrK,EAAE,CAACwK,OAAO;MAAAE,aAAA;MAAAK,eAAA;IAAA;EACzK;EAAC,OAjQK5C,SAAS;AAAA;AAkQf;EAAA,QAAA6C,SAAA,oBAAAA,SAAA;AAAA;AA4CoB,IACd2E,eAAe;EAArB,MAAMA,eAAe,CAAC;IAClB,OAAO5H,IAAI,YAAA6H,wBAAA3H,CAAA;MAAA,YAAAA,CAAA,IAAwF0H,eAAe;IAAA;IAClH,OAAOE,IAAI,kBA1e8E3P,EAAE,CAAA4P,gBAAA;MAAAtH,IAAA,EA0eSmH;IAAe;IACnH,OAAOI,IAAI,kBA3e8E7P,EAAE,CAAA8P,gBAAA;MAAAC,OAAA,GA2eoChQ,YAAY,EAAEoB,gBAAgB,EAAED,eAAe,EAAEF,YAAY;IAAA;EAChM;EAAC,OAJKyO,eAAe;AAAA;AAKrB;EAAA,QAAA3E,SAAA,oBAAAA,SAAA;AAAA;;AASA;AACA;AACA;;AAEA,SAAS7C,SAAS,EAAEwH,eAAe,EAAEjL,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}