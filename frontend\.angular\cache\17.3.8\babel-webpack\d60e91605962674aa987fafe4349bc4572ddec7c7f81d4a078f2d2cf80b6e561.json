{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { take } from 'rxjs/operators';\nimport { slideInOut } from 'src/app/helpers/my-animations';\nimport * as data from '../../../../core/models/data';\nimport { UserRole } from 'src/app/core/models/user.model';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/core/services/classroom.service\";\nimport * as i4 from \"src/app/core/services/lesson.service\";\nimport * as i5 from \"src/app/core/services/package.service\";\nimport * as i6 from \"src/app/core/services/note.service\";\nimport * as i7 from \"src/app/core/services/auth.service\";\nimport * as i8 from \"src/app/core/services/calendar.service\";\nimport * as i9 from \"../../../../shared/loader/loader.component\";\nimport * as i10 from \"../../../../shared/layout/g-level-circle/g-level-circle.component\";\nimport * as i11 from \"../../../../shared/layout/g-header-back-button/g-header-back-button.component\";\nconst _c0 = () => [\"lessons-menu-item-active\"];\nfunction ClassComponent_div_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵelement(2, \"g-level-circle\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scale\", 1)(\"name\", ctx_r1.classroom == null ? null : ctx_r1.classroom.activeLevel);\n  }\n}\nfunction ClassComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5)(2, \"div\", 6)(3, \"div\", 7)(4, \"g-header-back-button\", 8);\n    i0.ɵɵlistener(\"click\", function ClassComponent_div_2_Template_g_header_back_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBack());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"span\", 9)(6, \"span\", 10);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 11);\n    i0.ɵɵtemplate(9, ClassComponent_div_2_div_9_Template, 3, 2, \"div\", 12);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate3(\"\", ctx_r1.classroom.language, \" \", ctx_r1.classroom.type === \"Trial\" ? \"Trial\" : \"\", \" Classroom: \", ctx_r1.classroomService.getFormattedStudentNames(ctx_r1.classroom.classroomStudents), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isTrial);\n  }\n}\nfunction ClassComponent_ng_container_3_ng_container_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 23);\n  }\n}\nfunction ClassComponent_ng_container_3_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 20);\n    i0.ɵɵelement(2, \"img\", 21);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ClassComponent_ng_container_3_ng_container_4_div_4_Template, 1, 0, \"div\", 22);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const menuItem_r3 = ctx.$implicit;\n    const last_r4 = ctx.last;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", menuItem_r3.url)(\"routerLinkActive\", i0.ɵɵpureFunction0(5, _c0));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", menuItem_r3.icon, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", menuItem_r3.label, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !last_r4);\n  }\n}\nfunction ClassComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 16)(2, \"div\", 17)(3, \"div\", 18);\n    i0.ɵɵtemplate(4, ClassComponent_ng_container_3_ng_container_4_Template, 5, 6, \"ng-container\", 19);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.classMenuItems);\n  }\n}\nfunction ClassComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 24)(2, \"div\", 25);\n    i0.ɵɵelement(3, \"app-loader\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"scale\", 1.6);\n  }\n}\nfunction ClassComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"router-outlet\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nexport let ClassComponent = /*#__PURE__*/(() => {\n  class ClassComponent {\n    constructor(location, router, activatedRoute, classroomService, lessonService, packageService, noteService, authService, calendarService) {\n      this.location = location;\n      this.router = router;\n      this.activatedRoute = activatedRoute;\n      this.classroomService = classroomService;\n      this.lessonService = lessonService;\n      this.packageService = packageService;\n      this.noteService = noteService;\n      this.authService = authService;\n      this.calendarService = calendarService;\n      this.subs = new SubSink();\n      this.classroomLessons = [];\n      this.classroomTitle = \"\";\n      this.showDetails = false;\n      // showDetails: boolean = false;\n      this.showInfo = true;\n      // showInfo: boolean = true;\n      this.showReports = false;\n      this.showRatings = false;\n      // showRatings: boolean = true;\n      this.isTrial = false;\n      this.classroomPackages = [];\n      this.showTabLinks = true;\n      this.isLoadingClassroom = true;\n      this.classMenuItems = [];\n      this.dummyLesson = data.dummyLesson.classroom;\n    }\n    ngOnInit() {\n      // let routeExtras = this.router.getCurrentNavigation()!.extras.state;\n      this.classroomId = this.activatedRoute.snapshot.paramMap.get(\"classroom_id\");\n      // console.log(routeExtras);\n      this.getClassroomPackages();\n      this.activatedRoute.params.pipe(take(1)).subscribe(params => this.isTrial = params['isTrial']);\n      // if (this.classroomService.sessionUserClassrooms.length > 0) {\n      //   this.classroom = this.classroomService.sessionUserClassrooms.filter(el => el.id == this.classroomId)[0]\n      //   this.classroomTitle = this.classroomService.getClassroomTitle(this.classroom);\n      // }\n      this.fetchClassroom();\n      this.initUpdateListener();\n      // this.router.events.subscribe(event => {\n      //   if (event instanceof NavigationStart) {\n      //     console.log(event.url.includes('teacher'));\n      //     if (event.url.includes('teacher')) {\n      //       this.showTabLinks = false;\n      //     } else {\n      //       this.showTabLinks = true;\n      //     }\n      //   }\n      // });\n      // this.activatedRoute.url.subscribe(url => {\n      //   console.log(url);\n      //   // Check if the current route is '/dashboard/classrooms/lessons/2/info'\n      //   if (url[0].path === 'dashboard' && url[1].path === 'classrooms' && url[2].path === 'lessons' && url[4].path !== 'teacher') {\n      //     this.showTabLinks = true;\n      //   } else {\n      //     this.showTabLinks = false;\n      //   }\n      // });\n    }\n    updateTabLinks() {\n      this.activatedRoute.firstChild.data.subscribe(data => {\n        console.log(data);\n        this.showTabLinks = data.showTabs;\n        if (this.activatedRoute.firstChild.snapshot.children.length > 0) {\n          this.activatedRoute.firstChild.firstChild.data.subscribe(childData => {\n            this.showTabLinks = childData.showTabs;\n            // Use childData to control visibility of specific tabs\n          });\n        }\n      });\n    }\n    ngAfterContentInit() {}\n    ngOnDestroy() {\n      // TODO: check if we need to clear the selected classroom from service \n      // this.classroomService.setSelectedClassroom({} as Classroom);\n      this.subs.unsubscribe();\n      this.classroomService.setSelectedClassroomUpdate(false);\n    }\n    goBack() {\n      this.location.back();\n    }\n    goToDetails() {\n      // this.showDetails = true;\n      // this.showInfo = false;\n      // this.showReports = false;\n      // this.showRatings = false;\n      this.router.navigate(['/dashboard/classrooms/lessons', this.classroom.id, 'details']);\n      // this.router.navigateByUrl('/dashboard/classrooms/lessons/2/details', {replaceUrl: true});\n    }\n    info() {\n      // this.showDetails = false;\n      // this.showInfo = true;\n      // this.showReports = false;\n      // this.showRatings = false;\n      this.router.navigate(['/dashboard/classrooms/lessons', 2, 'info']);\n      // this.router.navigateByUrl('/dashboard/classrooms/lessons/2/info', {replaceUrl: true});\n    }\n    reports() {\n      this.showDetails = false;\n      this.showInfo = false;\n      this.showReports = true;\n      this.showRatings = false;\n      this.router.navigateByUrl('/dashboard/classrooms/lessons/2/details', {\n        replaceUrl: true\n      });\n    }\n    ratings() {\n      this.showDetails = false;\n      this.showInfo = false;\n      this.showReports = false;\n      this.showRatings = true;\n    }\n    getClassroomPackages() {\n      this.packageService.getClassroomPackages(this.classroomId).subscribe(res => {\n        this.classroomPackages = res;\n      });\n    }\n    isActiveUrl() {\n      return this.router.url.startsWith(this.router.url);\n    }\n    getClassMenuItems(classroomId, userRole) {\n      const menuItems = [{\n        label: 'Class',\n        icon: '/assets/icons/classroom/classroom-group-icon.svg',\n        url: `/dashboard/classrooms/lessons/${classroomId}/details`,\n        role: [UserRole.STUDENT, UserRole.TEACHER]\n      }, {\n        label: 'Info',\n        icon: '/assets/icons/classroom/info.svg',\n        url: `/dashboard/classrooms/lessons/${classroomId}/info`,\n        role: [UserRole.STUDENT, UserRole.TEACHER]\n      }\n      // {\n      //   label: 'Progress',\n      //   icon: '/assets/icons/classroom/progress.svg',\n      //   url: `/dashboard/classrooms/lessons/${classroomId}/progress`,\n      //   role: [UserRole.STUDENT],\n      // },\n      ];\n      return menuItems.filter(item => item.role.includes(userRole));\n    }\n    fetchClassroom() {\n      this.subs.add(this.classroomService.getClassroom(this.classroomId).pipe(take(1)).subscribe(res => {\n        this.isLoadingClassroom = false;\n        this.classroom = res;\n        this.classroomService.setSelectedClassroom(this.classroom);\n        this.classroomService.setSelectedClassroomUpdate(true);\n        console.log(res);\n        // this.noteService.setCurrentSelectedClassroomId(parseInt(this.classroom!.id))\n        this.classroomTitle = this.classroomService.getClassroomTitle(this.classroom);\n        this.isTrial = this.classroom.type === 'Trial';\n        // this.lessonService.getClassroomLessons(this.classroom.id).pipe(take(1)).subscribe(res => {\n        //   this.classroomLessons = res;\n        // });\n        this.updateTabLinks();\n        this.classMenuItems = this.getClassMenuItems(this.classroom.id, this.authService.getUserRole());\n        this.router.events.subscribe(event => {\n          if (event instanceof NavigationEnd) {\n            this.updateTabLinks();\n          }\n        });\n      }));\n    }\n    initUpdateListener() {\n      this.subs.add(this.calendarService.updateListener.subscribe(res => {\n        if (res) {\n          this.fetchClassroom();\n          this.getClassroomPackages();\n        }\n      }));\n    }\n    static #_ = this.ɵfac = function ClassComponent_Factory(t) {\n      return new (t || ClassComponent)(i0.ɵɵdirectiveInject(i1.Location), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ClassroomService), i0.ɵɵdirectiveInject(i4.LessonService), i0.ɵɵdirectiveInject(i5.PackageService), i0.ɵɵdirectiveInject(i6.NoteService), i0.ɵɵdirectiveInject(i7.AuthService), i0.ɵɵdirectiveInject(i8.CalendarService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ClassComponent,\n      selectors: [[\"app-class\"]],\n      decls: 6,\n      vars: 4,\n      consts: [[1, \"card\", \"mb-4\"], [1, \"border-round-xl\"], [\"class\", \"\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"\"], [1, \"border-round-xl\", \"overflow-hidden\"], [1, \"block-header\", \"gradient-header\", \"relative\", \"justify-content-center\", \"lg:flex\"], [1, \"absolute\", \"pl-1\", \"md:pl-2\", \"left-0\"], [3, \"click\"], [1, \"block-title\", \"pl-2\", \"sm:pl-0\"], [1, \"text-0\", \"capitalize\", \"max-w-30rem\"], [1, \"flex\", \"justify-content-end\"], [\"class\", \"lesson-level\", 4, \"ngIf\"], [1, \"lesson-level\"], [1, \"absolute\", \"right-1\", \"v-align\", 2, \"right\", \"10px\"], [3, \"scale\", \"name\"], [1, \"lessons\", \"mt-4\"], [\"id\", \"class\"], [1, \"lessons-menu\", \"flex\", \"align-items-center\"], [4, \"ngFor\", \"ngForOf\"], [1, \"lessons-menu-item\", \"flex\", \"align-items-center\", \"gap-2\", \"text-primary\", 3, \"routerLink\", \"routerLinkActive\"], [3, \"src\"], [\"class\", \"seperator\", 4, \"ngIf\"], [1, \"seperator\"], [1, \"relative\", \"h-20rem\"], [1, \"abs-centered\"], [3, \"scale\"]],\n      template: function ClassComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵtemplate(2, ClassComponent_div_2_Template, 10, 4, \"div\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, ClassComponent_ng_container_3_Template, 5, 1, \"ng-container\", 3)(4, ClassComponent_ng_container_4_Template, 4, 1, \"ng-container\", 3)(5, ClassComponent_ng_container_5_Template, 2, 0, \"ng-container\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.classroom);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showTabLinks && ctx.classMenuItems.length > 0 && !ctx.isTrial);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingClassroom);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingClassroom);\n        }\n      },\n      dependencies: [i1.NgForOf, i1.NgIf, i2.RouterOutlet, i2.RouterLink, i2.RouterLinkActive, i9.LoaderComponent, i10.GLevelCircleComponent, i11.GHeaderBackButtonComponent],\n      styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}.gradient-header[_ngcontent-%COMP%]{background-image:url(/assets/images/classroom-gradient-bg.png);background-repeat:no-repeat;background-position:center center;min-height:60px;border-radius:8px;box-shadow:0 3px 6px #00000029}.lessons[_ngcontent-%COMP%]{width:100%;font-size:17px}.lessons-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;width:100%;font-family:Raleway-Bold;padding:15px 0}.lessons-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]{display:flex;align-items:center;cursor:pointer}.lessons-header[_ngcontent-%COMP%]   .classroom-title[_ngcontent-%COMP%]{text-align:center}.lessons-header[_ngcontent-%COMP%]   .lesson-level[_ngcontent-%COMP%]{background-color:var(--main-color);color:var(--white);text-align:center;transition:all .3s linear;width:33px;height:33px;line-height:33px;border-radius:50%}.lessons-menu[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.lessons-menu[_ngcontent-%COMP%]   .lessons-menu-item[_ngcontent-%COMP%]{color:var(--main-color);cursor:pointer;text-align:center;position:relative;padding:0 12px}.lessons-menu[_ngcontent-%COMP%]   .lessons-menu-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{height:16px}.lessons-menu[_ngcontent-%COMP%]   .lessons-menu-item-active[_ngcontent-%COMP%], .lessons-menu[_ngcontent-%COMP%]   .lessons-menu-item[_ngcontent-%COMP%]:hover{font-weight:700}.lessons-menu[_ngcontent-%COMP%]   .lessons-menu-item-active[_ngcontent-%COMP%]:after, .lessons-menu[_ngcontent-%COMP%]   .lessons-menu-item[_ngcontent-%COMP%]:hover:after{content:\\\"\\\";position:absolute;bottom:-5px;border-bottom:1px solid var(--primary-color);height:2px;width:100%;left:0}.lessons-menu[_ngcontent-%COMP%]   .seperator[_ngcontent-%COMP%]{height:20px;width:1px;background-color:var(--primary-color);margin:0 14px}\"],\n      data: {\n        animation: [slideInOut]\n      }\n    });\n  }\n  return ClassComponent;\n})();", "map": {"version": 3, "names": ["NavigationEnd", "take", "slideInOut", "data", "UserRole", "SubSink", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r1", "classroom", "activeLevel", "ɵɵlistener", "ClassComponent_div_2_Template_g_header_back_button_click_4_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "goBack", "ɵɵtext", "ɵɵtemplate", "ClassComponent_div_2_div_9_Template", "ɵɵtextInterpolate3", "language", "type", "classroomService", "getFormattedStudentNames", "classroomStudents", "isTrial", "ɵɵelementContainerStart", "ClassComponent_ng_container_3_ng_container_4_div_4_Template", "menuItem_r3", "url", "ɵɵpureFunction0", "_c0", "icon", "ɵɵsanitizeUrl", "ɵɵtextInterpolate1", "label", "last_r4", "ClassComponent_ng_container_3_ng_container_4_Template", "classMenuItems", "ClassComponent", "constructor", "location", "router", "activatedRoute", "lessonService", "packageService", "noteService", "authService", "calendarService", "subs", "classroomLessons", "classroomTitle", "showDetails", "showInfo", "showReports", "showRatings", "classroomPackages", "showTabLinks", "isLoadingClassroom", "du<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "classroomId", "snapshot", "paramMap", "get", "getClassroomPackages", "params", "pipe", "subscribe", "fetchClassroom", "initUpdateListener", "updateTabLinks", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "showTabs", "children", "length", "childData", "ngAfterContentInit", "ngOnDestroy", "unsubscribe", "setSelectedClassroomUpdate", "back", "goToDetails", "navigate", "id", "info", "reports", "navigateByUrl", "replaceUrl", "ratings", "res", "isActiveUrl", "startsWith", "getClassMenuItems", "userRole", "menuItems", "role", "STUDENT", "TEACHER", "filter", "item", "includes", "add", "getClassroom", "setSelectedClassroom", "getClassroomTitle", "getUserRole", "events", "event", "updateListener", "_", "ɵɵdirectiveInject", "i1", "Location", "i2", "Router", "ActivatedRoute", "i3", "ClassroomService", "i4", "LessonService", "i5", "PackageService", "i6", "NoteService", "i7", "AuthService", "i8", "CalendarService", "_2", "selectors", "decls", "vars", "consts", "template", "ClassComponent_Template", "rf", "ctx", "ClassComponent_div_2_Template", "ClassComponent_ng_container_3_Template", "ClassComponent_ng_container_4_Template", "ClassComponent_ng_container_5_Template"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\classrooms\\class\\class.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\classrooms\\class\\class.component.html"], "sourcesContent": ["import { Location } from '@angular/common';\r\nimport { Component, OnDestroy, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, NavigationEnd, NavigationStart, Params, Router } from '@angular/router';\r\nimport { take } from 'rxjs/operators';\r\nimport { Classroom } from 'src/app/core/models/classroom.model';\r\nimport { Lesson } from 'src/app/core/models/lesson.model';\r\nimport { Package } from 'src/app/core/models/package.model';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { LessonService } from 'src/app/core/services/lesson.service';\r\nimport { NoteService } from 'src/app/core/services/note.service';\r\nimport { PackageService } from 'src/app/core/services/package.service';\r\nimport { slideInOut } from 'src/app/helpers/my-animations';\r\nimport * as data from '../../../../core/models/data';\r\nimport { UserRole } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { CalendarService } from 'src/app/core/services/calendar.service';\r\nimport { SubSink } from 'subsink';\r\n\r\n\r\n@Component({\r\n  selector: 'app-class',\r\n  templateUrl: './class.component.html',\r\n  styleUrls: ['./class.component.scss'],\r\n  animations: [slideInOut]\r\n})\r\nexport class ClassComponent implements OnInit, OnDestroy {\r\n  private subs = new SubSink();\r\n  private classroomId?: string;\r\n  public classroom?: any;\r\n  public classroomLessons: Lesson[] = [];\r\n  public classroomTitle: string = \"\";\r\n  showDetails: boolean = false;\r\n  // showDetails: boolean = false;\r\n  showInfo: boolean = true;\r\n  // showInfo: boolean = true;\r\n  showReports: boolean = false;\r\n  showRatings: boolean = false;\r\n  // showRatings: boolean = true;\r\n  isTrial: boolean = false;\r\n  classroomPackages: Package[] = []\r\n  showTabLinks = true;\r\n  isLoadingClassroom = true;\r\n  \r\n  classMenuItems: any[] = [];\r\n  dummyLesson = data.dummyLesson.classroom;\r\n  constructor(\r\n    private location: Location,\r\n    private router: Router,\r\n    private activatedRoute: ActivatedRoute,\r\n    private classroomService: ClassroomService,\r\n    private lessonService: LessonService,\r\n    private packageService: PackageService,\r\n    private noteService: NoteService,\r\n    private authService: AuthService,\r\n    private calendarService: CalendarService,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    // let routeExtras = this.router.getCurrentNavigation()!.extras.state;\r\n    this.classroomId = this.activatedRoute.snapshot.paramMap.get(\"classroom_id\")!;\r\n    // console.log(routeExtras);\r\n    this.getClassroomPackages();\r\n    this.activatedRoute.params.pipe(take(1)).subscribe((params: Params) => this.isTrial = params['isTrial']);\r\n    // if (this.classroomService.sessionUserClassrooms.length > 0) {\r\n    //   this.classroom = this.classroomService.sessionUserClassrooms.filter(el => el.id == this.classroomId)[0]\r\n    //   this.classroomTitle = this.classroomService.getClassroomTitle(this.classroom);\r\n    // }\r\n\r\n    this.fetchClassroom();\r\n    this.initUpdateListener();\r\n    // this.router.events.subscribe(event => {\r\n    //   if (event instanceof NavigationStart) {\r\n    //     console.log(event.url.includes('teacher'));\r\n    //     if (event.url.includes('teacher')) {\r\n    //       this.showTabLinks = false;\r\n    //     } else {\r\n    //       this.showTabLinks = true;\r\n    //     }\r\n    //   }\r\n    // });\r\n    // this.activatedRoute.url.subscribe(url => {\r\n    //   console.log(url);\r\n    //   // Check if the current route is '/dashboard/classrooms/lessons/2/info'\r\n    //   if (url[0].path === 'dashboard' && url[1].path === 'classrooms' && url[2].path === 'lessons' && url[4].path !== 'teacher') {\r\n    //     this.showTabLinks = true;\r\n    //   } else {\r\n    //     this.showTabLinks = false;\r\n    //   }\r\n    // });\r\n  }\r\n  updateTabLinks() {\r\n    this.activatedRoute.firstChild!.data.subscribe(data => {\r\n      console.log(data);\r\n      this.showTabLinks = data.showTabs;\r\n      if (this.activatedRoute.firstChild!.snapshot.children.length > 0) {\r\n        this.activatedRoute.firstChild!.firstChild!.data.subscribe(childData => {\r\n          this.showTabLinks = childData.showTabs;\r\n          // Use childData to control visibility of specific tabs\r\n        });\r\n      }\r\n    });\r\n  }\r\n  ngAfterContentInit() {\r\n\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    // TODO: check if we need to clear the selected classroom from service \r\n    // this.classroomService.setSelectedClassroom({} as Classroom);\r\n    this.subs.unsubscribe();\r\n    this.classroomService.setSelectedClassroomUpdate(false);\r\n  }\r\n\r\n  goBack() {\r\n    this.location.back();\r\n  }\r\n\r\n  goToDetails() {\r\n    // this.showDetails = true;\r\n    // this.showInfo = false;\r\n    // this.showReports = false;\r\n    // this.showRatings = false;\r\n    this.router.navigate(['/dashboard/classrooms/lessons', this.classroom.id, 'details']);\r\n    // this.router.navigateByUrl('/dashboard/classrooms/lessons/2/details', {replaceUrl: true});\r\n  }\r\n\r\n  info() {\r\n    // this.showDetails = false;\r\n    // this.showInfo = true;\r\n    // this.showReports = false;\r\n    // this.showRatings = false;\r\n    this.router.navigate(['/dashboard/classrooms/lessons', 2, 'info']);\r\n    // this.router.navigateByUrl('/dashboard/classrooms/lessons/2/info', {replaceUrl: true});\r\n  }\r\n\r\n  reports() {\r\n    this.showDetails = false;\r\n    this.showInfo = false;\r\n    this.showReports = true;\r\n    this.showRatings = false;\r\n    this.router.navigateByUrl('/dashboard/classrooms/lessons/2/details', {replaceUrl: true});\r\n  }\r\n\r\n  ratings() {\r\n    this.showDetails = false;\r\n    this.showInfo = false;\r\n    this.showReports = false;\r\n    this.showRatings = true;\r\n  }\r\n\r\n  getClassroomPackages() {\r\n    this.packageService.getClassroomPackages(this.classroomId!).subscribe(res => {\r\n      this.classroomPackages = res\r\n    })\r\n  }\r\n  \r\n  isActiveUrl(): boolean {\r\n    return this.router.url.startsWith(this.router.url);\r\n  }\r\n\r\n  getClassMenuItems(classroomId: string, userRole: UserRole): any[] {\r\n    const menuItems = [\r\n      {\r\n        label: 'Class',\r\n        icon: '/assets/icons/classroom/classroom-group-icon.svg',\r\n        url: `/dashboard/classrooms/lessons/${classroomId}/details`,\r\n        role: [UserRole.STUDENT, UserRole.TEACHER],\r\n      },\r\n      {\r\n        label: 'Info',\r\n        icon: '/assets/icons/classroom/info.svg',\r\n        url: `/dashboard/classrooms/lessons/${classroomId}/info`,\r\n        role: [UserRole.STUDENT, UserRole.TEACHER],\r\n      },\r\n      // {\r\n      //   label: 'Progress',\r\n      //   icon: '/assets/icons/classroom/progress.svg',\r\n      //   url: `/dashboard/classrooms/lessons/${classroomId}/progress`,\r\n      //   role: [UserRole.STUDENT],\r\n      // },\r\n    ];\r\n  \r\n    return menuItems.filter(item => item.role.includes(userRole));\r\n  }\r\n\r\n  private fetchClassroom() {\r\n    this.subs.add(this.classroomService.getClassroom(this.classroomId!).pipe(take(1)).subscribe(res => {\r\n\r\n      this.isLoadingClassroom = false;\r\n      this.classroom = res;\r\n      this.classroomService.setSelectedClassroom(this.classroom);\r\n      this.classroomService.setSelectedClassroomUpdate(true);\r\n      console.log(res);\r\n      // this.noteService.setCurrentSelectedClassroomId(parseInt(this.classroom!.id))\r\n      this.classroomTitle = this.classroomService.getClassroomTitle(this.classroom);\r\n      this.isTrial = this.classroom.type === 'Trial';\r\n      // this.lessonService.getClassroomLessons(this.classroom.id).pipe(take(1)).subscribe(res => {\r\n      //   this.classroomLessons = res;\r\n      // });\r\n      this.updateTabLinks();\r\n      this.classMenuItems = this.getClassMenuItems(this.classroom.id, this.authService.getUserRole());\r\n      this.router.events.subscribe(event => {\r\n        if (event instanceof NavigationEnd) {\r\n          this.updateTabLinks();\r\n        }\r\n      });\r\n    }));\r\n  }\r\n\r\n  private initUpdateListener() {\r\n    this.subs.add(this.calendarService.updateListener.subscribe((res: any) => {\r\n      if (res) {\r\n        this.fetchClassroom();\r\n        this.getClassroomPackages();\r\n      }\r\n    }));\r\n  }\r\n}\r\n", "\r\n<div class=\"card mb-4\">\r\n<div class=\"border-round-xl\">\r\n    <div *ngIf=\"classroom\" class=\"\">\r\n        <div class=\"border-round-xl  overflow-hidden\">\r\n            <div class=\"block-header gradient-header  relative justify-content-center  lg:flex\">\r\n                <div class=\"absolute pl-1 md:pl-2 left-0\">\r\n                   <g-header-back-button (click)=\"goBack()\"></g-header-back-button>\r\n                </div>\r\n                <span class=\"block-title pl-2 sm:pl-0\">\r\n                    <span class=\"text-0 capitalize max-w-30rem\">{{classroom.language}} {{classroom.type === 'Trial' ? 'Trial' : ''}} Classroom:\r\n                        {{classroomService.getFormattedStudentNames(classroom.classroomStudents)}}\r\n                    </span>\r\n                </span>\r\n                <div class=\"flex justify-content-end\">\r\n                    <div *ngIf=\"!isTrial\" class=\"lesson-level\">\r\n                        <div class=\"absolute right-1 v-align\" style=\"right: 10px;\">\r\n                            <g-level-circle [scale]=\"1\" [name]=\"classroom?.activeLevel\"></g-level-circle>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n\r\n<ng-container *ngIf=\"showTabLinks && (classMenuItems.length > 0) && !isTrial\">\r\n    <div class=\"lessons mt-4\">\r\n        <div id=\"class\">\r\n            <!-- <app-next-lesson *ngIf=\"!isTrial\"></app-next-lesson> -->\r\n            <div class=\"lessons-menu flex align-items-center\">\r\n                <ng-container *ngFor=\"let menuItem of classMenuItems; let last = last\">\r\n                    <a class=\"lessons-menu-item flex align-items-center gap-2 text-primary\" [routerLink]=\"menuItem.url\"\r\n                        [routerLinkActive]=\"['lessons-menu-item-active']\"><img\r\n                            [src]=\"menuItem.icon\"> {{menuItem.label}}</a>\r\n                    <div class=\"seperator\" *ngIf=\"!last\"></div>\r\n                </ng-container>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</ng-container>\r\n\r\n<ng-container *ngIf=\"isLoadingClassroom\">\r\n    <div class=\"relative h-20rem\">\r\n        <div class=\"abs-centered\">\r\n            <app-loader [scale]=\"1.6\"></app-loader>\r\n        </div>\r\n    </div>\r\n</ng-container>\r\n\r\n<ng-container *ngIf=\"!isLoadingClassroom\">\r\n    <router-outlet></router-outlet>\r\n</ng-container>\r\n</div>"], "mappings": "AAEA,SAAyBA,aAAa,QAAyC,iBAAiB;AAChG,SAASC,IAAI,QAAQ,gBAAgB;AAQrC,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,OAAO,KAAKC,IAAI,MAAM,8BAA8B;AACpD,SAASC,QAAQ,QAAQ,gCAAgC;AAGzD,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;;;;;ICATC,EADJ,CAAAC,cAAA,cAA2C,cACoB;IACvDD,EAAA,CAAAE,SAAA,yBAA6E;IAErFF,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IAFkBH,EAAA,CAAAI,SAAA,GAAW;IAACJ,EAAZ,CAAAK,UAAA,YAAW,SAAAC,MAAA,CAAAC,SAAA,kBAAAD,MAAA,CAAAC,SAAA,CAAAC,WAAA,CAAgC;;;;;;IAVpER,EAJf,CAAAC,cAAA,aAAgC,aACkB,aAC0C,aACtC,8BACE;IAAnBD,EAAA,CAAAS,UAAA,mBAAAC,oEAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAN,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAAS,MAAA,EAAQ;IAAA,EAAC;IAC3Cf,EAD4C,CAAAG,YAAA,EAAuB,EAC7D;IAEFH,EADJ,CAAAC,cAAA,cAAuC,eACS;IAAAD,EAAA,CAAAgB,MAAA,GAE5C;IACJhB,EADI,CAAAG,YAAA,EAAO,EACJ;IACPH,EAAA,CAAAC,cAAA,cAAsC;IAClCD,EAAA,CAAAiB,UAAA,IAAAC,mCAAA,kBAA2C;IAQ3DlB,EAHY,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ;;;;IAbsDH,EAAA,CAAAI,SAAA,GAE5C;IAF4CJ,EAAA,CAAAmB,kBAAA,KAAAb,MAAA,CAAAC,SAAA,CAAAa,QAAA,OAAAd,MAAA,CAAAC,SAAA,CAAAc,IAAA,6CAAAf,MAAA,CAAAgB,gBAAA,CAAAC,wBAAA,CAAAjB,MAAA,CAAAC,SAAA,CAAAiB,iBAAA,OAE5C;IAGMxB,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAK,UAAA,UAAAC,MAAA,CAAAmB,OAAA,CAAc;;;;;IAoBpBzB,EAAA,CAAAE,SAAA,cAA2C;;;;;IAJ/CF,EAAA,CAAA0B,uBAAA,GAAuE;IACnE1B,EAAA,CAAAC,cAAA,YACsD;IAAAD,EAAA,CAAAE,SAAA,cACxB;IAACF,EAAA,CAAAgB,MAAA,GAAkB;IAAAhB,EAAA,CAAAG,YAAA,EAAI;IACrDH,EAAA,CAAAiB,UAAA,IAAAU,2DAAA,kBAAqC;;;;;;IAHmC3B,EAAA,CAAAI,SAAA,EAA2B;IAC/FJ,EADoE,CAAAK,UAAA,eAAAuB,WAAA,CAAAC,GAAA,CAA2B,qBAAA7B,EAAA,CAAA8B,eAAA,IAAAC,GAAA,EAC9C;IAC7C/B,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAK,UAAA,QAAAuB,WAAA,CAAAI,IAAA,EAAAhC,EAAA,CAAAiC,aAAA,CAAqB;IAAEjC,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAkC,kBAAA,MAAAN,WAAA,CAAAO,KAAA,KAAkB;IACzBnC,EAAA,CAAAI,SAAA,EAAW;IAAXJ,EAAA,CAAAK,UAAA,UAAA+B,OAAA,CAAW;;;;;IATvDpC,EAAA,CAAA0B,uBAAA,GAA8E;IAIlE1B,EAHR,CAAAC,cAAA,cAA0B,cACN,cAEsC;IAC9CD,EAAA,CAAAiB,UAAA,IAAAoB,qDAAA,2BAAuE;IAQnFrC,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;;;;;IARyCH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,UAAA,YAAAC,MAAA,CAAAgC,cAAA,CAAmB;;;;;IAWtEtC,EAAA,CAAA0B,uBAAA,GAAyC;IAEjC1B,EADJ,CAAAC,cAAA,cAA8B,cACA;IACtBD,EAAA,CAAAE,SAAA,qBAAuC;IAE/CF,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IAFcH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAK,UAAA,cAAa;;;;;IAKrCL,EAAA,CAAA0B,uBAAA,GAA0C;IACtC1B,EAAA,CAAAE,SAAA,oBAA+B;;;;AD1BnC,WAAaqC,cAAc;EAArB,MAAOA,cAAc;IAoBzBC,YACUC,QAAkB,EAClBC,MAAc,EACdC,cAA8B,EAC9BrB,gBAAkC,EAClCsB,aAA4B,EAC5BC,cAA8B,EAC9BC,WAAwB,EACxBC,WAAwB,EACxBC,eAAgC;MARhC,KAAAP,QAAQ,GAARA,QAAQ;MACR,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,cAAc,GAAdA,cAAc;MACd,KAAArB,gBAAgB,GAAhBA,gBAAgB;MAChB,KAAAsB,aAAa,GAAbA,aAAa;MACb,KAAAC,cAAc,GAAdA,cAAc;MACd,KAAAC,WAAW,GAAXA,WAAW;MACX,KAAAC,WAAW,GAAXA,WAAW;MACX,KAAAC,eAAe,GAAfA,eAAe;MA5BjB,KAAAC,IAAI,GAAG,IAAIlD,OAAO,EAAE;MAGrB,KAAAmD,gBAAgB,GAAa,EAAE;MAC/B,KAAAC,cAAc,GAAW,EAAE;MAClC,KAAAC,WAAW,GAAY,KAAK;MAC5B;MACA,KAAAC,QAAQ,GAAY,IAAI;MACxB;MACA,KAAAC,WAAW,GAAY,KAAK;MAC5B,KAAAC,WAAW,GAAY,KAAK;MAC5B;MACA,KAAA9B,OAAO,GAAY,KAAK;MACxB,KAAA+B,iBAAiB,GAAc,EAAE;MACjC,KAAAC,YAAY,GAAG,IAAI;MACnB,KAAAC,kBAAkB,GAAG,IAAI;MAEzB,KAAApB,cAAc,GAAU,EAAE;MAC1B,KAAAqB,WAAW,GAAG9D,IAAI,CAAC8D,WAAW,CAACpD,SAAS;IAWpC;IAEJqD,QAAQA,CAAA;MACN;MACA,IAAI,CAACC,WAAW,GAAG,IAAI,CAAClB,cAAc,CAACmB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,cAAc,CAAE;MAC7E;MACA,IAAI,CAACC,oBAAoB,EAAE;MAC3B,IAAI,CAACtB,cAAc,CAACuB,MAAM,CAACC,IAAI,CAACxE,IAAI,CAAC,CAAC,CAAC,CAAC,CAACyE,SAAS,CAAEF,MAAc,IAAK,IAAI,CAACzC,OAAO,GAAGyC,MAAM,CAAC,SAAS,CAAC,CAAC;MACxG;MACA;MACA;MACA;MAEA,IAAI,CAACG,cAAc,EAAE;MACrB,IAAI,CAACC,kBAAkB,EAAE;MACzB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACF;IACAC,cAAcA,CAAA;MACZ,IAAI,CAAC5B,cAAc,CAAC6B,UAAW,CAAC3E,IAAI,CAACuE,SAAS,CAACvE,IAAI,IAAG;QACpD4E,OAAO,CAACC,GAAG,CAAC7E,IAAI,CAAC;QACjB,IAAI,CAAC4D,YAAY,GAAG5D,IAAI,CAAC8E,QAAQ;QACjC,IAAI,IAAI,CAAChC,cAAc,CAAC6B,UAAW,CAACV,QAAQ,CAACc,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;UAChE,IAAI,CAAClC,cAAc,CAAC6B,UAAW,CAACA,UAAW,CAAC3E,IAAI,CAACuE,SAAS,CAACU,SAAS,IAAG;YACrE,IAAI,CAACrB,YAAY,GAAGqB,SAAS,CAACH,QAAQ;YACtC;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;IACAI,kBAAkBA,CAAA,GAElB;IAEAC,WAAWA,CAAA;MACT;MACA;MACA,IAAI,CAAC/B,IAAI,CAACgC,WAAW,EAAE;MACvB,IAAI,CAAC3D,gBAAgB,CAAC4D,0BAA0B,CAAC,KAAK,CAAC;IACzD;IAEAnE,MAAMA,CAAA;MACJ,IAAI,CAAC0B,QAAQ,CAAC0C,IAAI,EAAE;IACtB;IAEAC,WAAWA,CAAA;MACT;MACA;MACA;MACA;MACA,IAAI,CAAC1C,MAAM,CAAC2C,QAAQ,CAAC,CAAC,+BAA+B,EAAE,IAAI,CAAC9E,SAAS,CAAC+E,EAAE,EAAE,SAAS,CAAC,CAAC;MACrF;IACF;IAEAC,IAAIA,CAAA;MACF;MACA;MACA;MACA;MACA,IAAI,CAAC7C,MAAM,CAAC2C,QAAQ,CAAC,CAAC,+BAA+B,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;MAClE;IACF;IAEAG,OAAOA,CAAA;MACL,IAAI,CAACpC,WAAW,GAAG,KAAK;MACxB,IAAI,CAACC,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACC,WAAW,GAAG,IAAI;MACvB,IAAI,CAACC,WAAW,GAAG,KAAK;MACxB,IAAI,CAACb,MAAM,CAAC+C,aAAa,CAAC,yCAAyC,EAAE;QAACC,UAAU,EAAE;MAAI,CAAC,CAAC;IAC1F;IAEAC,OAAOA,CAAA;MACL,IAAI,CAACvC,WAAW,GAAG,KAAK;MACxB,IAAI,CAACC,QAAQ,GAAG,KAAK;MACrB,IAAI,CAACC,WAAW,GAAG,KAAK;MACxB,IAAI,CAACC,WAAW,GAAG,IAAI;IACzB;IAEAU,oBAAoBA,CAAA;MAClB,IAAI,CAACpB,cAAc,CAACoB,oBAAoB,CAAC,IAAI,CAACJ,WAAY,CAAC,CAACO,SAAS,CAACwB,GAAG,IAAG;QAC1E,IAAI,CAACpC,iBAAiB,GAAGoC,GAAG;MAC9B,CAAC,CAAC;IACJ;IAEAC,WAAWA,CAAA;MACT,OAAO,IAAI,CAACnD,MAAM,CAACb,GAAG,CAACiE,UAAU,CAAC,IAAI,CAACpD,MAAM,CAACb,GAAG,CAAC;IACpD;IAEAkE,iBAAiBA,CAAClC,WAAmB,EAAEmC,QAAkB;MACvD,MAAMC,SAAS,GAAG,CAChB;QACE9D,KAAK,EAAE,OAAO;QACdH,IAAI,EAAE,kDAAkD;QACxDH,GAAG,EAAE,iCAAiCgC,WAAW,UAAU;QAC3DqC,IAAI,EAAE,CAACpG,QAAQ,CAACqG,OAAO,EAAErG,QAAQ,CAACsG,OAAO;OAC1C,EACD;QACEjE,KAAK,EAAE,MAAM;QACbH,IAAI,EAAE,kCAAkC;QACxCH,GAAG,EAAE,iCAAiCgC,WAAW,OAAO;QACxDqC,IAAI,EAAE,CAACpG,QAAQ,CAACqG,OAAO,EAAErG,QAAQ,CAACsG,OAAO;;MAE3C;MACA;MACA;MACA;MACA;MACA;MAAA,CACD;MAED,OAAOH,SAAS,CAACI,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACJ,IAAI,CAACK,QAAQ,CAACP,QAAQ,CAAC,CAAC;IAC/D;IAEQ3B,cAAcA,CAAA;MACpB,IAAI,CAACpB,IAAI,CAACuD,GAAG,CAAC,IAAI,CAAClF,gBAAgB,CAACmF,YAAY,CAAC,IAAI,CAAC5C,WAAY,CAAC,CAACM,IAAI,CAACxE,IAAI,CAAC,CAAC,CAAC,CAAC,CAACyE,SAAS,CAACwB,GAAG,IAAG;QAEhG,IAAI,CAAClC,kBAAkB,GAAG,KAAK;QAC/B,IAAI,CAACnD,SAAS,GAAGqF,GAAG;QACpB,IAAI,CAACtE,gBAAgB,CAACoF,oBAAoB,CAAC,IAAI,CAACnG,SAAS,CAAC;QAC1D,IAAI,CAACe,gBAAgB,CAAC4D,0BAA0B,CAAC,IAAI,CAAC;QACtDT,OAAO,CAACC,GAAG,CAACkB,GAAG,CAAC;QAChB;QACA,IAAI,CAACzC,cAAc,GAAG,IAAI,CAAC7B,gBAAgB,CAACqF,iBAAiB,CAAC,IAAI,CAACpG,SAAS,CAAC;QAC7E,IAAI,CAACkB,OAAO,GAAG,IAAI,CAAClB,SAAS,CAACc,IAAI,KAAK,OAAO;QAC9C;QACA;QACA;QACA,IAAI,CAACkD,cAAc,EAAE;QACrB,IAAI,CAACjC,cAAc,GAAG,IAAI,CAACyD,iBAAiB,CAAC,IAAI,CAACxF,SAAS,CAAC+E,EAAE,EAAE,IAAI,CAACvC,WAAW,CAAC6D,WAAW,EAAE,CAAC;QAC/F,IAAI,CAAClE,MAAM,CAACmE,MAAM,CAACzC,SAAS,CAAC0C,KAAK,IAAG;UACnC,IAAIA,KAAK,YAAYpH,aAAa,EAAE;YAClC,IAAI,CAAC6E,cAAc,EAAE;UACvB;QACF,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC;IACL;IAEQD,kBAAkBA,CAAA;MACxB,IAAI,CAACrB,IAAI,CAACuD,GAAG,CAAC,IAAI,CAACxD,eAAe,CAAC+D,cAAc,CAAC3C,SAAS,CAAEwB,GAAQ,IAAI;QACvE,IAAIA,GAAG,EAAE;UACP,IAAI,CAACvB,cAAc,EAAE;UACrB,IAAI,CAACJ,oBAAoB,EAAE;QAC7B;MACF,CAAC,CAAC,CAAC;IACL;IAAC,QAAA+C,CAAA,G;uBA/LUzE,cAAc,EAAAvC,EAAA,CAAAiH,iBAAA,CAAAC,EAAA,CAAAC,QAAA,GAAAnH,EAAA,CAAAiH,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAArH,EAAA,CAAAiH,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAAtH,EAAA,CAAAiH,iBAAA,CAAAM,EAAA,CAAAC,gBAAA,GAAAxH,EAAA,CAAAiH,iBAAA,CAAAQ,EAAA,CAAAC,aAAA,GAAA1H,EAAA,CAAAiH,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAA5H,EAAA,CAAAiH,iBAAA,CAAAY,EAAA,CAAAC,WAAA,GAAA9H,EAAA,CAAAiH,iBAAA,CAAAc,EAAA,CAAAC,WAAA,GAAAhI,EAAA,CAAAiH,iBAAA,CAAAgB,EAAA,CAAAC,eAAA;IAAA;IAAA,QAAAC,EAAA,G;YAAd5F,cAAc;MAAA6F,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCvB3B1I,EADA,CAAAC,cAAA,aAAuB,aACM;UACzBD,EAAA,CAAAiB,UAAA,IAAA2H,6BAAA,kBAAgC;UAqBpC5I,EAAA,CAAAG,YAAA,EAAM;UA0BNH,EAxBA,CAAAiB,UAAA,IAAA4H,sCAAA,0BAA8E,IAAAC,sCAAA,0BAgBrC,IAAAC,sCAAA,0BAQC;UAG1C/I,EAAA,CAAAG,YAAA,EAAM;;;UAlDIH,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAK,UAAA,SAAAsI,GAAA,CAAApI,SAAA,CAAe;UAuBVP,EAAA,CAAAI,SAAA,EAA6D;UAA7DJ,EAAA,CAAAK,UAAA,SAAAsI,GAAA,CAAAlF,YAAA,IAAAkF,GAAA,CAAArG,cAAA,CAAAuC,MAAA,SAAA8D,GAAA,CAAAlH,OAAA,CAA6D;UAgB7DzB,EAAA,CAAAI,SAAA,EAAwB;UAAxBJ,EAAA,CAAAK,UAAA,SAAAsI,GAAA,CAAAjF,kBAAA,CAAwB;UAQxB1D,EAAA,CAAAI,SAAA,EAAyB;UAAzBJ,EAAA,CAAAK,UAAA,UAAAsI,GAAA,CAAAjF,kBAAA,CAAyB;;;;;;mBD3B1B,CAAC9D,UAAU;MAAC;IAAA;;SAEb2C,cAAc;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}