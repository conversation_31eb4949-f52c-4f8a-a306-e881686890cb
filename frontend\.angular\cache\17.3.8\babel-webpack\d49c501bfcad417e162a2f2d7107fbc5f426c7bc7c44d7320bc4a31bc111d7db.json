{"ast": null, "code": "import { EventEmitter } from \"@angular/core\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"primeng/menu\";\nfunction SingleLibraryFolderItemComponent_i_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"i\", 6);\n    i0.ɵɵlistener(\"click\", function SingleLibraryFolderItemComponent_i_0_Template_i_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      i0.ɵɵnextContext();\n      const menu4_r3 = i0.ɵɵreference(2);\n      return i0.ɵɵresetView(menu4_r3.toggle($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nexport let SingleLibraryFolderItemComponent = /*#__PURE__*/(() => {\n  class SingleLibraryFolderItemComponent {\n    constructor() {\n      this.hasLimitedOptions = false;\n      this.folder = {};\n      this.deleteFolder = new EventEmitter();\n      this.openFolder = new EventEmitter();\n    }\n    ngOnInit() {}\n    getFolderContextMenu(item) {\n      const context = item;\n      return [{\n        label: \"Delete folder\",\n        icon: \"pi pi-fw pi-trash\",\n        command: event => {\n          this.deleteFolder.emit(context);\n        }\n      }];\n    }\n    openFolderClicked() {\n      this.openFolder.emit(this.folder);\n    }\n    static #_ = this.ɵfac = function SingleLibraryFolderItemComponent_Factory(t) {\n      return new (t || SingleLibraryFolderItemComponent)();\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SingleLibraryFolderItemComponent,\n      selectors: [[\"app-single-library-folder-item\"]],\n      inputs: {\n        hasLimitedOptions: \"hasLimitedOptions\",\n        folder: \"folder\"\n      },\n      outputs: {\n        deleteFolder: \"deleteFolder\",\n        openFolder: \"openFolder\"\n      },\n      decls: 7,\n      vars: 3,\n      consts: [[\"menu4\", \"\"], [\"class\", \"ml-auto pointer pi pi-ellipsis-h lession-more-icon absolute center folder-options-icon text-primary\", 3, \"click\", 4, \"ngIf\"], [\"popup\", \"popup\", \"appendTo\", \"body\", 3, \"model\"], [1, \"flex\", \"flex-column\", \"align-items-center\", 3, \"click\"], [\"src\", \"/assets/icons/folder-icon.svg\", \"width\", \"40\"], [1, \"folder-name-tag\", \"w-full\", \"text-center\", \"font-xs\", \"px-1\"], [1, \"ml-auto\", \"pointer\", \"pi\", \"pi-ellipsis-h\", \"lession-more-icon\", \"absolute\", \"center\", \"folder-options-icon\", \"text-primary\", 3, \"click\"]],\n      template: function SingleLibraryFolderItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵtemplate(0, SingleLibraryFolderItemComponent_i_0_Template, 1, 0, \"i\", 1);\n          i0.ɵɵelement(1, \"p-menu\", 2, 0);\n          i0.ɵɵelementStart(3, \"div\", 3);\n          i0.ɵɵlistener(\"click\", function SingleLibraryFolderItemComponent_Template_div_click_3_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.openFolderClicked());\n          });\n          i0.ɵɵelement(4, \"img\", 4);\n          i0.ɵɵelementStart(5, \"div\", 5);\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.hasLimitedOptions);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"model\", ctx.getFolderContextMenu(ctx.folder));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.folder.name);\n        }\n      },\n      dependencies: [i1.NgIf, i2.Menu],\n      styles: [\".folder-options-icon[_ngcontent-%COMP%]{right:-15px;transform:rotate(90deg)}.folder-name-tag[_ngcontent-%COMP%]{width:auto;height:18.68px;border-radius:9.34px;background:#1a266c;color:#fff}\"],\n      changeDetection: 0\n    });\n  }\n  return SingleLibraryFolderItemComponent;\n})();", "map": {"version": 3, "names": ["EventEmitter", "i0", "ɵɵelementStart", "ɵɵlistener", "SingleLibraryFolderItemComponent_i_0_Template_i_click_0_listener", "$event", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "menu4_r3", "ɵɵreference", "ɵɵresetView", "toggle", "ɵɵelementEnd", "SingleLibraryFolderItemComponent", "constructor", "hasLimitedOptions", "folder", "deleteFolder", "openFolder", "ngOnInit", "getFolderContextMenu", "item", "context", "label", "icon", "command", "event", "emit", "openFolderClicked", "_", "_2", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "SingleLibraryFolderItemComponent_Template", "rf", "ctx", "ɵɵtemplate", "SingleLibraryFolderItemComponent_i_0_Template", "ɵɵelement", "SingleLibraryFolderItemComponent_Template_div_click_3_listener", "_r1", "ɵɵtext", "ɵɵproperty", "ɵɵadvance", "ɵɵtextInterpolate", "name"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\library\\library\\components\\single-library-folder-item\\single-library-folder-item.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\library\\library\\components\\single-library-folder-item\\single-library-folder-item.component.html"], "sourcesContent": ["import {\r\n  ChangeDetectionStrategy,\r\n  Component,\r\n  EventEmitter,\r\n  Input,\r\n  OnInit,\r\n  Output,\r\n} from \"@angular/core\";\r\nimport { MenuItem } from \"primeng/api\";\r\nimport { Folder } from \"src/app/core/models/library.model\";\r\n\r\n@Component({\r\n  selector: \"app-single-library-folder-item\",\r\n  templateUrl: \"./single-library-folder-item.component.html\",\r\n  styleUrls: [\"./single-library-folder-item.component.scss\"],\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n})\r\nexport class SingleLibraryFolderItemComponent implements OnInit {\r\n  @Input() hasLimitedOptions = false;\r\n  @Input() folder = {} as Folder;\r\n  @Output() deleteFolder = new EventEmitter<Folder>();\r\n  @Output() openFolder = new EventEmitter<Folder>();\r\n  constructor() {}\r\n\r\n  ngOnInit(): void {}\r\n\r\n  getFolderContextMenu(item: any): MenuItem[] {\r\n    const context = item;\r\n    return [\r\n      {\r\n        label: \"Delete folder\",\r\n        icon: \"pi pi-fw pi-trash\",\r\n        command: (event) => {\r\n          this.deleteFolder.emit(context);\r\n        },\r\n      },\r\n    ];\r\n  }\r\n\r\n  openFolderClicked() {\r\n    this.openFolder.emit(this.folder);\r\n  }\r\n}\r\n", "<i *ngIf=\"!hasLimitedOptions\" class=\"ml-auto pointer pi pi-ellipsis-h lession-more-icon absolute center folder-options-icon text-primary\"\r\n(click)=\"menu4.toggle($event)\"></i>\r\n<p-menu #menu4 popup=\"popup\" [model]=\"getFolderContextMenu(folder)\"\r\nappendTo=\"body\"></p-menu>\r\n<div class=\"flex flex-column align-items-center\" (click)=\"openFolderClicked()\">\r\n<img src=\"/assets/icons/folder-icon.svg\" width=\"40\">\r\n<div class=\"folder-name-tag w-full text-center font-xs px-1\">{{folder.name}}</div>\r\n</div>"], "mappings": "AAAA,SAGEA,YAAY,QAIP,eAAe;;;;;;;ICPtBC,EAAA,CAAAC,cAAA,WAC+B;IAA/BD,EAAA,CAAAE,UAAA,mBAAAC,iEAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAC,QAAA,GAAAR,EAAA,CAAAS,WAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,QAAA,CAAAG,MAAA,CAAAP,MAAA,CAAoB;IAAA,EAAC;IAACJ,EAAA,CAAAY,YAAA,EAAI;;;ADgBnC,WAAaC,gCAAgC;EAAvC,MAAOA,gCAAgC;IAK3CC,YAAA;MAJS,KAAAC,iBAAiB,GAAG,KAAK;MACzB,KAAAC,MAAM,GAAG,EAAY;MACpB,KAAAC,YAAY,GAAG,IAAIlB,YAAY,EAAU;MACzC,KAAAmB,UAAU,GAAG,IAAInB,YAAY,EAAU;IAClC;IAEfoB,QAAQA,CAAA,GAAU;IAElBC,oBAAoBA,CAACC,IAAS;MAC5B,MAAMC,OAAO,GAAGD,IAAI;MACpB,OAAO,CACL;QACEE,KAAK,EAAE,eAAe;QACtBC,IAAI,EAAE,mBAAmB;QACzBC,OAAO,EAAGC,KAAK,IAAI;UACjB,IAAI,CAACT,YAAY,CAACU,IAAI,CAACL,OAAO,CAAC;QACjC;OACD,CACF;IACH;IAEAM,iBAAiBA,CAAA;MACf,IAAI,CAACV,UAAU,CAACS,IAAI,CAAC,IAAI,CAACX,MAAM,CAAC;IACnC;IAAC,QAAAa,CAAA,G;uBAxBUhB,gCAAgC;IAAA;IAAA,QAAAiB,EAAA,G;YAAhCjB,gCAAgC;MAAAkB,SAAA;MAAAC,MAAA;QAAAjB,iBAAA;QAAAC,MAAA;MAAA;MAAAiB,OAAA;QAAAhB,YAAA;QAAAC,UAAA;MAAA;MAAAgB,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0CAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCjB7CvC,EAAA,CAAAyC,UAAA,IAAAC,6CAAA,eAC+B;UAC/B1C,EAAA,CAAA2C,SAAA,mBACyB;UACzB3C,EAAA,CAAAC,cAAA,aAA+E;UAA9BD,EAAA,CAAAE,UAAA,mBAAA0C,+DAAA;YAAA5C,EAAA,CAAAK,aAAA,CAAAwC,GAAA;YAAA,OAAA7C,EAAA,CAAAU,WAAA,CAAS8B,GAAA,CAAAZ,iBAAA,EAAmB;UAAA,EAAC;UAC9E5B,EAAA,CAAA2C,SAAA,aAAoD;UACpD3C,EAAA,CAAAC,cAAA,aAA6D;UAAAD,EAAA,CAAA8C,MAAA,GAAe;UAC5E9C,EAD4E,CAAAY,YAAA,EAAM,EAC5E;;;UAPFZ,EAAA,CAAA+C,UAAA,UAAAP,GAAA,CAAAzB,iBAAA,CAAwB;UAECf,EAAA,CAAAgD,SAAA,EAAsC;UAAtChD,EAAA,CAAA+C,UAAA,UAAAP,GAAA,CAAApB,oBAAA,CAAAoB,GAAA,CAAAxB,MAAA,EAAsC;UAINhB,EAAA,CAAAgD,SAAA,GAAe;UAAfhD,EAAA,CAAAiD,iBAAA,CAAAT,GAAA,CAAAxB,MAAA,CAAAkC,IAAA,CAAe;;;;;;;;SDW/DrC,gCAAgC;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}