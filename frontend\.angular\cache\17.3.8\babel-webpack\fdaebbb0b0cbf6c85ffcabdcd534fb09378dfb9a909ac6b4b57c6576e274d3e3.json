{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { DialogService } from 'primeng/dynamicdialog';\nimport { take } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport { LibraryFileActionsDialogComponent } from '../library-file-actions-dialog/library-file-actions-dialog.component';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/library.service\";\nimport * as i2 from \"src/app/core/services/general.service\";\nimport * as i3 from \"primeng/dynamicdialog\";\nimport * as i4 from \"src/app/core/services/toast.service\";\nimport * as i5 from \"src/app/core/services/auth.service\";\nimport * as i6 from \"@angular/common/http\";\nimport * as i7 from \"@angular/common\";\nconst _c0 = (a0, a1) => ({\n  \"checked\": a0,\n  \"purple-hover\": a1\n});\nconst _c1 = (a0, a1) => ({\n  \"bg-grad-purple\": a0,\n  \"bg-grad-blue\": a1\n});\nfunction LibraryFileRowItemComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"g-level-circle\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const level_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", level_r2);\n  }\n}\nfunction LibraryFileRowItemComponent_img_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 21);\n  }\n}\nfunction LibraryFileRowItemComponent_p_chip_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-chip\")(1, \"span\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r3 = ctx.$implicit;\n    i0.ɵɵclassMapInterpolate1(\"class-rating \", category_r3.toLowerCase(), \" border-round-xl px-0\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r3);\n  }\n}\nfunction LibraryFileRowItemComponent_div_17_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 24);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pTooltip\", i0.ɵɵpipeBind2(2, 2, ctx_r3.classroomFile.categories, 1).join(\", \"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" +\", ctx_r3.classroomFile.categories.length - 1, \" \");\n  }\n}\nfunction LibraryFileRowItemComponent_div_17_ng_template_2_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵpipe(1, \"slice\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"pTooltip\", i0.ɵɵpipeBind2(1, 2, ctx_r3.classroomFile.categories, 2).join(\", \"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" +\", ctx_r3.classroomFile.categories.length - 2, \" \");\n  }\n}\nfunction LibraryFileRowItemComponent_div_17_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, LibraryFileRowItemComponent_div_17_ng_template_2_span_0_Template, 3, 5, \"span\", 25);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.classroomFile.categories.length > 2);\n  }\n}\nfunction LibraryFileRowItemComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, LibraryFileRowItemComponent_div_17_ng_container_1_Template, 4, 5, \"ng-container\", 23)(2, LibraryFileRowItemComponent_div_17_ng_template_2_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notMobileBlock_r5 = i0.ɵɵreference(3);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.isLarge)(\"ngIfElse\", notMobileBlock_r5);\n  }\n}\nfunction LibraryFileRowItemComponent_div_19_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 29);\n  }\n}\nfunction LibraryFileRowItemComponent_div_19_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 30);\n  }\n}\nfunction LibraryFileRowItemComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function LibraryFileRowItemComponent_div_19_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.checkLibraryItem(ctx_r3.checkLibMenu));\n    });\n    i0.ɵɵtemplate(1, LibraryFileRowItemComponent_div_19_img_1_Template, 1, 0, \"img\", 27)(2, LibraryFileRowItemComponent_div_19_img_2_Template, 1, 0, \"img\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.checked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.checked);\n  }\n}\nfunction LibraryFileRowItemComponent_img_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 31);\n  }\n}\nexport class LibraryFileRowItemComponent {\n  constructor(libraryService, generalService, dialogService, toastService, authService, http) {\n    this.libraryService = libraryService;\n    this.generalService = generalService;\n    this.dialogService = dialogService;\n    this.toastService = toastService;\n    this.authService = authService;\n    this.http = http;\n    this.classroom = {};\n    this.classroomFile = {};\n    this.appendDialogActionsTo = 'allFiles';\n    this.showGroupActions = false;\n    this.hasLimitedOptions = false;\n    this.availableActions = [];\n    this.isSmallScreen = false;\n    this.showSharedWithUsers = true;\n    this.isInTab = false;\n    this.fileDeleted = new EventEmitter();\n    this.libraryChecked = new EventEmitter();\n    this.fileUnshared = new EventEmitter();\n    this.StudentIdsToSend = [];\n    this.isDisabled = false;\n    this.isDisabledWholeClass = false;\n    this.moveFileId = 0;\n    this.moveFolderId = 0;\n    this.isMoveRadioCheked = false;\n    this.showRename = false;\n    this.refs = [];\n    this.subs = new SubSink();\n    this.checked = false;\n    this.sharedWithUsers = [];\n    this.menuItems = [];\n    this.isLarge = false;\n    this.hasMultipleCategoriesSlice = 1;\n  }\n  ngOnInit() {\n    if (this.showSharedWithUsers) {\n      this.getUsersLibraryHasBeenSharedWith();\n    }\n    this.subs.sink = this.libraryService.showGroupActions.subscribe(res => {\n      if (!res) {\n        this.checked = false;\n      }\n      // if (this.libraryService.isPreviewingFromChat()) {\n      //   this.checked = true;\n      // }\n    });\n    this.subs.add(this.generalService.deviceKind.pipe().subscribe(res => {\n      if (res) {\n        if (res.w768up) {\n          this.isLarge = true;\n          this.hasMultipleCategoriesSlice = 2;\n        }\n      }\n    }));\n    this.prepareMenuItemsForItem(this.classroomFile);\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  onSelect(studentId) {\n    this.isDisabled = false;\n    if (this.StudentIdsToSend.indexOf(studentId) !== -1) this.StudentIdsToSend.splice(this.StudentIdsToSend.indexOf(studentId), 1);else this.StudentIdsToSend.push(studentId);\n    if (this.isDisabledWholeClass && this.StudentIdsToSend.length === 0) this.isDisabledWholeClass = false;else this.isDisabledWholeClass = true;\n  }\n  showMoveFilePopup(fileId) {\n    document.getElementById('lib-file-share-menu-' + fileId).style.display = 'none';\n    document.getElementById('lib-file-move-menu-' + fileId).style.display = 'block';\n    document.getElementById('lib-file-move-menu-' + fileId).style.top = '55%';\n  }\n  // openSharedByFileMenu(fileId: any) {\n  //   alert(fileId);\n  //   [].forEach.call(document.getElementsByClassName('lib-file-sharedby-menu'), (el: any) => {\n  //     el.style.display = 'none';\n  //   })\n  //   setTimeout(() => {\n  //     document.getElementById('lib-file-sharedby-menu-' + fileId)!.style.display = 'flex';\n  //     document.getElementById('lib-file-sharedby-menu-' + fileId)!.style.top = (880 + document.getElementById('lib-file-sharedby-menu-' + fileId)!.offsetTop) + \"px\";\n  //   }, 100);\n  //   // this.showShare = true;\n  // }\n  // showSharedByMoveFilePopup(fileId: any) {\n  //   document.getElementById('lib-file-sharedby-move-menu-' + fileId)!.style.display = 'block';\n  //   document.getElementById('lib-file-sharedby-move-menu-' + fileId)!.style.top = '55%';\n  // }\n  getFileName(path) {\n    if (path) {\n      let n = path.lastIndexOf(\"/\");\n      if (n === -1) n = path.lastIndexOf(\"\\\\\");\n      let result = path.substring(n + 1);\n      return result;\n    }\n    return path;\n  }\n  prepareMenuItemsForItem(item) {\n    let context = item;\n    const menuItems = [{\n      label: 'View',\n      icon: 'pi pi-fw pi-external-link',\n      command: event => {\n        this.openFileInNewTab(context.path);\n      }\n    }, {\n      label: 'Copy URL',\n      icon: 'pi pi-fw pi-copy',\n      command: event => {\n        this.copyURL(context.path);\n      }\n    }, {\n      label: 'Download',\n      icon: 'pi pi-fw pi-cloud-download',\n      command: event => {\n        this.downloadFile(context.path);\n      }\n    }, {\n      label: 'Share',\n      icon: 'pi pi-fw pi-share-alt',\n      command: event => {\n        if (this.authService.isStudent) {\n          this.shareWithTeacher(context);\n        } else {\n          const currentSelectedClassroom = this.libraryService.getCurrentSelectedClassroom();\n          if (Object.keys(currentSelectedClassroom).length > 0) {\n            const classroomStudents = this.libraryService.getCurrentSelectedClassroom().classroomStudents;\n            // if only one student, share immediately\n            if (classroomStudents.length === 1 && this.authService.isTeacher) {\n              let send = {\n                fileIds: [context.fileId],\n                classroomId: this.libraryService.getCurrentSelectedClassroom().id,\n                studentIds: [classroomStudents[0].aspUserId]\n              };\n              this.sendShareToApi(send);\n            }\n          } else {\n            this.openDialogWithComponent(LibraryFileActionsDialogComponent, {\n              action: 'shareWith',\n              dialogsubTitle: 'Share \"' + this.getFileName(context.name) + '\" with...',\n              dialogTitle: 'Share file with...',\n              libraryFile: context\n            });\n          }\n          console.log(this.libraryService.getCurrentSelectedClassroom().classroomStudents);\n          // this.openDialogWithComponent(LibraryFileActionsDialogComponent,\n          //   {\n          //     action: 'shareWith',\n          //     dialogsubTitle: 'Share \"' + this.getFileName(context.name) + '\" with...',\n          //     dialogTitle: 'Share file with...',\n          //     libraryFile: context\n          //   });\n        }\n      }\n    }, {\n      label: 'Unshare',\n      icon: 'pi pi-fw pi-share-alt',\n      command: event => {\n        let send = {\n          fileId: this.classroomFile.fileId,\n          classroomId: this.libraryService.getCurrentSelectedClassroom().id,\n          studentIds: [this.libraryService.getCurrentSelectedClassroom().teacher?.aspUserId]\n        };\n        if (this.authService.isStudent) {\n          this.sendUnShareToApi(send);\n        } else {\n          this.sendUnShareToApi(send);\n          // this.openDialogWithComponent(LibraryFileActionsDialogComponent,\n          //   {\n          //     action: 'unShareWith',\n          //     dialogsubTitle: 'Share \"' + this.getFileName(context.name) + '\" with...',\n          //     dialogTitle: 'Share file with...',\n          //     libraryFile: context\n          //   });\n        }\n      }\n    }, {\n      label: 'Move',\n      icon: 'pi pi-fw pi-folder-open',\n      command: event => {\n        this.openDialogWithComponent(LibraryFileActionsDialogComponent, {\n          action: 'move',\n          dialogsubTitle: 'Move \"' + this.getFileName(context.name) + '\" to folder...',\n          dialogTitle: 'Move file to folder',\n          libraryFile: context,\n          folders: this.folders\n        });\n      }\n    }, {\n      label: 'Edit',\n      icon: 'pi pi-fw pi-file-edit',\n      command: event => {\n        this.generalService.openDialogWithComponent(this.dialogService, LibraryFileActionsDialogComponent, 440, {\n          action: 'editFileLevelsCategories',\n          dialogsubTitle: '',\n          dialogTitle: 'Edit Library File',\n          libraryFile: context\n        }, null, result => {\n          console.log(result);\n          if (result && result.action === 'editFileLevelsCategories') {\n            if (!this.generalService.isNullishObject(result)) {\n              console.log(this.classroomFile);\n              console.log(result);\n              this.classroomFile = {\n                ...result.result\n              };\n              context = {\n                ...result.result\n              };\n            }\n          }\n        });\n      }\n    }, {\n      label: 'Delete',\n      icon: 'pi pi-fw pi-trash',\n      command: event => {\n        this.deleteFile(item.fileId);\n      }\n    }];\n    this.menuItems = menuItems.filter(menuItem => {\n      const menuLabel = menuItem.label;\n      return this.availableActions.includes(menuLabel);\n    });\n  }\n  showSharePopup(fileId) {\n    document.getElementById('lib-file-move-menu-' + fileId).style.display = 'none';\n    document.getElementById('lib-file-share-menu-' + fileId).style.display = 'block';\n    document.getElementById('lib-file-share-menu-' + fileId).style.top = '25%';\n  }\n  rename(fileId, newName) {\n    // const input = document.getElementById('fileName') as HTMLInputElement | null;\n    // console.log(input?.value)\n    // this.classroomFile.name = input!.value\n    this.subs.add(this.libraryService.updateLibraryFileName(fileId, newName).pipe(take(1)).subscribe(res => {\n      this.toastService.setShowToastmessage({\n        severity: 'success',\n        summary: '',\n        detail: 'File name name has been updated.'\n      });\n      console.log(res);\n      // this.showRename = !this.showRename\n    }));\n  }\n  checkLibraryItem(el) {\n    this.checked = !this.checked;\n    // let bg = this.checked ? '#2d2a4b' : 'white';\n    // el!.style.backgroundColor = bg;\n    if (this.checked) {\n      this.libraryService.checkedLibraries.push(this.classroomFile);\n      this.libraryChecked.emit({\n        checked: true,\n        file: this.classroomFile\n      });\n    } else {\n      this.libraryService.checkedLibraries = this.libraryService.checkedLibraries.filter(el => {\n        return !(el.path == this.classroomFile.path);\n      });\n      this.libraryChecked.emit({\n        checked: false,\n        file: this.classroomFile\n      });\n    }\n  }\n  getUniqueSharedUsers() {\n    // exclude from sharedWithUsers if not in classroomStudents\n    const filteredUsers = this.sharedWithUsers.filter(user => {\n      return this.classroomStudents.find(student => {\n        return student.aspUserId === user.aspUserId;\n      });\n    });\n    const uniqueNames = Object.values(filteredUsers.reduce((acc, user) => {\n      if (!acc[user.aspUserId]) {\n        acc[user.aspUserId] = user.firstName;\n      }\n      return acc;\n    }, {}));\n    return uniqueNames.join(', ');\n  }\n  onWholeClassSelect() {\n    if (this.isDisabled) this.isDisabled = false;else this.isDisabled = true;\n    if (this.StudentIdsToSend.length === 0) {\n      this.classroom.classroomStudents.forEach(student => {\n        this.StudentIdsToSend.push(student.id);\n      });\n    } else this.StudentIdsToSend = [];\n  }\n  moveFile(libraryFile, moveFolder) {\n    this.subs.add(this.libraryService.moveFileToFolder(libraryFile.fileId, moveFolder.folderId).subscribe(response => {\n      if (!this.generalService.isNullishObject(response)) {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'File moved successfully.'\n        });\n        this.libraryService.setUpdateFolderListener(libraryFile, moveFolder);\n        this.libraryService.setUpdateListener(true);\n      }\n    }));\n    this.ngOnInit();\n  }\n  copyURL(filePath) {\n    this.fileURL = environment.apiUrl + \"/\" + filePath;\n    const selBox = document.createElement('textarea');\n    selBox.style.position = 'fixed';\n    selBox.style.left = '0';\n    selBox.style.top = '0';\n    selBox.style.opacity = '0';\n    selBox.value = this.fileURL;\n    document.body.appendChild(selBox);\n    selBox.focus();\n    selBox.select();\n    document.execCommand('copy');\n    document.body.removeChild(selBox);\n    this.toastService.setShowToastmessage({\n      severity: 'success',\n      summary: '',\n      detail: 'File path copied successfully.'\n    });\n  }\n  deleteFile(fileId) {\n    this.fileDeleted.emit(this.classroomFile);\n    this.ngOnDestroy();\n  }\n  getUsersLibraryHasBeenSharedWith() {\n    if (this.classroomFile.fileId) {\n      return this.subs.add(this.libraryService.getUsersLibraryHasBeenSharedWith(this.classroomFile.fileId).pipe(take(1)).subscribe(res => {\n        this.sharedWithUsers = res;\n      }));\n    }\n    return [];\n  }\n  shareWithTeacher(context) {\n    let send = {\n      fileIds: [context.fileId],\n      classroomId: this.libraryService.getCurrentSelectedClassroom().id,\n      studentIds: [this.libraryService.getCurrentSelectedClassroom().teacher?.aspUserId]\n    };\n    this.sendShareToApi(send);\n  }\n  shareLibraryFile(map) {\n    console.log(this.classroomFile);\n    map.forEach((users, classroomId) => {\n      let ids = [];\n      let fileIds = [];\n      for (let user of users) {\n        ids.push(user.aspUserId);\n      }\n      fileIds.push(this.classroomFile.fileId);\n      let send = {\n        fileIds: fileIds,\n        classroomId: classroomId,\n        studentIds: ids\n      };\n      this.sendShareToApi(send);\n    });\n  }\n  unShareLibraryFile(map) {\n    console.log(this.classroomFile);\n    map.forEach((users, classroomId) => {\n      let ids = [];\n      let fileIds = [];\n      for (let user of users) {\n        ids.push(user.aspUserId);\n      }\n      fileIds.push(this.classroomFile.fileId);\n      let send = {\n        fileId: this.classroomFile.fileId,\n        classroomId: classroomId\n      };\n      this.sendUnShareToApi(send);\n    });\n  }\n  openFileInNewTab(url) {\n    const fileURL = environment.apiUrl + \"/\" + url;\n    window.open(fileURL, '_blank');\n  }\n  sendShareToApi(sendParams) {\n    this.subs.add(this.libraryService.shareFileWithClassUsers(sendParams).pipe(take(1)).subscribe(res => {\n      console.log(this.classroom);\n      this.toastService.setShowToastmessage({\n        severity: 'success',\n        summary: '',\n        detail: 'Library file has been shared.'\n      });\n      if (this.authService.isTeacher) {\n        this.libraryService.setCurrentSelectedClassroomId(parseInt(sendParams.classroomId));\n      }\n    }));\n  }\n  sendUnShareToApi(sendParams) {\n    console.log(this.classroom);\n    this.subs.add(this.libraryService.unShareLibrary(sendParams).pipe(take(1)).subscribe(res => {\n      this.toastService.setShowToastmessage({\n        severity: 'success',\n        summary: '',\n        detail: 'Library file has been unshared.'\n      });\n      this.fileUnshared.emit(res);\n      this.libraryService.setCurrentSelectedClassroomId(parseInt(sendParams.classroomId));\n    }));\n  }\n  openDialogWithComponent(component, dialogData, width = 360) {\n    this.generalService.openDialogWithComponent(this.dialogService, component, 400, dialogData, null, result => {\n      if (result && result.action === 'shareWith') {\n        this.shareLibraryFile(result.map.map);\n      } else if (result && result.action === 'unShareWith') {\n        this.unShareLibraryFile(result.map.map);\n      } else if (result && result.action === 'move') {\n        this.moveFile(result.libraryFile, result.folder);\n      }\n    });\n    // this.refs.forEach(ref => ref.close());\n    // this.refs = [];\n    // const element = document.querySelector('#' + this.appendDialogActionsTo);\n    // const rect = element!.getBoundingClientRect();\n    // const centerX = rect.left + rect.width / 2 - width / 2;\n    // const centerY = rect.top;\n    // const dialogRef = (this.dialogService.open(component, {\n    //   header: '',\n    //   width: width + 'px',\n    //   showHeader: false,\n    //   dismissableMask: true,\n    //   modal: true,\n    //   contentStyle: {\n    //     \"max-width\": \"100%\", \"max-height\": \"400px\", \"overflow\": \"auto\", \"border-radius\": \"10px\", \"padding\": \"0px\",\n    //   },\n    //   style: { 'left': `${centerX}px`, 'top': `${centerY}px`, 'position': `fixed` },\n    //   baseZIndex: 10000,\n    //   maskStyleClass: 'transparent-mask',\n    //   data: { dialogData: dialogData },\n    // }));\n    // this.refs.push(dialogRef);\n    // dialogRef.onClose.subscribe((data: any) => {\n    //   console.log('Dialog closed with data:', data);\n    //   if (data) {\n    //     switch (data.action) {\n    //       case 'rename':\n    //         this.rename(data.libraryFile.fileId, data.libraryFile.name);\n    //         break;\n    //       case 'shareWith':\n    //         this.shareLibraryFiles(data.map.map);\n    //         break;\n    //       case 'move':\n    //         this.moveFile(data.libraryFile.fileId, data.folder.folderId);\n    //         break;\n    //       default:\n    //         break;\n    //     }\n    //   }\n    // });\n  }\n  downloadFile(url) {\n    const fileURL = environment.apiUrl + \"/\" + url;\n    this.subs.add(this.http.get(fileURL, {\n      responseType: 'blob'\n    }).subscribe(blob => {\n      const link = document.createElement('a');\n      link.href = window.URL.createObjectURL(blob);\n      link.download = this.getFileName(fileURL);\n      link.click();\n    }));\n  }\n  static #_ = this.ɵfac = function LibraryFileRowItemComponent_Factory(t) {\n    return new (t || LibraryFileRowItemComponent)(i0.ɵɵdirectiveInject(i1.LibraryService), i0.ɵɵdirectiveInject(i2.GeneralService), i0.ɵɵdirectiveInject(i3.DialogService), i0.ɵɵdirectiveInject(i4.ToastService), i0.ɵɵdirectiveInject(i5.AuthService), i0.ɵɵdirectiveInject(i6.HttpClient));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LibraryFileRowItemComponent,\n    selectors: [[\"app-library-file-row-item\"]],\n    inputs: {\n      classroom: \"classroom\",\n      classroomFile: \"classroomFile\",\n      folders: \"folders\",\n      appendDialogActionsTo: \"appendDialogActionsTo\",\n      showGroupActions: \"showGroupActions\",\n      hasLimitedOptions: \"hasLimitedOptions\",\n      availableActions: \"availableActions\",\n      isSmallScreen: \"isSmallScreen\",\n      showSharedWithUsers: \"showSharedWithUsers\",\n      isInTab: \"isInTab\",\n      classroomStudents: \"classroomStudents\"\n    },\n    outputs: {\n      fileDeleted: \"fileDeleted\",\n      libraryChecked: \"libraryChecked\",\n      fileUnshared: \"fileUnshared\"\n    },\n    features: [i0.ɵɵProvidersFeature([DialogService])],\n    decls: 23,\n    vars: 24,\n    consts: [[\"menu3\", \"\"], [\"notMobileBlock\", \"\"], [1, \"library-file-row\", 3, \"ngClass\"], [1, \"w-3rem\", \"h-3rem\", \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-grad-blue\", \"border-circle\", \"mr-1\", \"flex-shrink-0\", 3, \"ngClass\"], [\"src\", \"/assets/icons/file-white.svg\", \"height\", \"24\"], [1, \"w-full\", \"flex\", \"sm:flex-row\", \"align-items-center\", \"justify-content-between\"], [1, \"library-file-details\", \"py-1\", \"font-sm\", \"flex\", \"flex-column\"], [1, \"file-name\", \"file-path-text\", \"max-w-10rem\", \"sm:max-w-15rem\", \"pointer\", 3, \"click\"], [1, \"flex\", \"gap-1\"], [4, \"ngFor\", \"ngForOf\"], [\"src\", \"/assets/icons/library/share-sm-icon.svg\", \"width\", \"10\", 4, \"ngIf\"], [1, \"font-xs\", \"file-path-text\", \"max-w-10rem\"], [1, \"library-file-meta\"], [1, \"text-900\", \"w-full\", \"md:flex-order-0\", \"flex-order-1\", \"flex\", \"align-items-end\", \"justify-content-end\", \"gap-1\"], [3, \"class\", 4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [1, \"library-actions-button\", \"mx-2\", 3, \"click\", \"id\"], [\"style\", \"position: relative;\", 3, \"click\", 4, \"ngIf\"], [\"src\", \"/assets/icons/library/library-more-actions.svg\", \"class\", \"pointer hvr-grow\", \"style\", \"width:1.75rem; height:1.75rem\", 4, \"ngIf\"], [\"popup\", \"popup\", \"appendTo\", \"body\", 3, \"model\"], [\"size\", \"2xs\", 3, \"name\"], [\"src\", \"/assets/icons/library/share-sm-icon.svg\", \"width\", \"10\"], [1, \"font-xs\", \"text-white\", \"py-1\", \"px-0\", \"line-height-1\"], [4, \"ngIf\", \"ngIfElse\"], [\"tooltipPosition\", \"top\", 1, \"font-xs\", \"text-muted\", \"mt-2\", \"more-circle\", 3, \"pTooltip\"], [\"class\", \"font-xs text-muted mt-2 more-circle\", \"tooltipPosition\", \"top\", 3, \"pTooltip\", 4, \"ngIf\"], [2, \"position\", \"relative\", 3, \"click\"], [\"src\", \"/assets/icons/library/group-item-empty.svg\", \"class\", \"pointer\", \"style\", \"width:1.75rem; height:1.75rem\", 4, \"ngIf\"], [\"src\", \"/assets/icons/library/group-item-full.svg\", \"class\", \"pointer\", \"style\", \"width:1.75rem; height:1.75rem\", 4, \"ngIf\"], [\"src\", \"/assets/icons/library/group-item-empty.svg\", 1, \"pointer\", 2, \"width\", \"1.75rem\", \"height\", \"1.75rem\"], [\"src\", \"/assets/icons/library/group-item-full.svg\", 1, \"pointer\", 2, \"width\", \"1.75rem\", \"height\", \"1.75rem\"], [\"src\", \"/assets/icons/library/library-more-actions.svg\", 1, \"pointer\", \"hvr-grow\", 2, \"width\", \"1.75rem\", \"height\", \"1.75rem\"]],\n    template: function LibraryFileRowItemComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"li\", 2);\n        i0.ɵɵtext(1);\n        i0.ɵɵelementStart(2, \"div\", 3);\n        i0.ɵɵelement(3, \"img\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 5)(5, \"div\", 6)(6, \"span\", 7);\n        i0.ɵɵlistener(\"click\", function LibraryFileRowItemComponent_Template_span_click_6_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.openFileInNewTab(ctx.classroomFile.path));\n        });\n        i0.ɵɵtext(7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 8);\n        i0.ɵɵtemplate(9, LibraryFileRowItemComponent_div_9_Template, 2, 1, \"div\", 9)(10, LibraryFileRowItemComponent_img_10_Template, 1, 0, \"img\", 10);\n        i0.ɵɵelementStart(11, \"span\", 11);\n        i0.ɵɵtext(12);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(13, \"div\", 12)(14, \"div\", 13);\n        i0.ɵɵtemplate(15, LibraryFileRowItemComponent_p_chip_15_Template, 3, 4, \"p-chip\", 14);\n        i0.ɵɵpipe(16, \"slice\");\n        i0.ɵɵtemplate(17, LibraryFileRowItemComponent_div_17_Template, 4, 2, \"div\", 15);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(18, \"div\", 16);\n        i0.ɵɵlistener(\"click\", function LibraryFileRowItemComponent_Template_div_click_18_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          const menu3_r6 = i0.ɵɵreference(22);\n          return i0.ɵɵresetView(!ctx.showGroupActions ? menu3_r6.toggle($event) : null);\n        });\n        i0.ɵɵtemplate(19, LibraryFileRowItemComponent_div_19_Template, 3, 2, \"div\", 17)(20, LibraryFileRowItemComponent_img_20_Template, 1, 0, \"img\", 18);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(21, \"p-menu\", 19, 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(18, _c0, ctx.checked && ctx.showGroupActions, ctx.isInTab));\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" \", ctx.classroom == null ? null : ctx.classroom.id, \" \");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(21, _c1, ctx.isInTab, !ctx.isInTab));\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.getFileName(ctx.classroomFile.name));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.classroomFile.levels);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.sharedWithUsers.length > 0);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\" \", ctx.getUniqueSharedUsers(), \" \");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(16, 14, ctx.classroomFile.categories, 0, ctx.hasMultipleCategoriesSlice));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.classroomFile.categories.length > 1);\n        i0.ɵɵadvance();\n        i0.ɵɵpropertyInterpolate1(\"id\", \"lib-file-burger-\", ctx.classroomFile.fileId, \"\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showGroupActions || ctx.libraryService.isPreviewingFromChat());\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.showGroupActions && !ctx.libraryService.isPreviewingFromChat());\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"model\", ctx.menuItems);\n      }\n    },\n    dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i7.SlicePipe],\n    styles: [\".bg-grad-blue[_ngcontent-%COMP%] {\\n  background: linear-gradient(#657aef 0%, #1a266c 100%);\\n}\\n\\n.bg-grad-purple[_ngcontent-%COMP%] {\\n  background: linear-gradient(#7f8dfa 0%, #4f29a2 100%);\\n  border: 1px solid #7d89f6;\\n}\\n\\n.library-actions-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  line-height: 1;\\n}\\n\\n.library-file-row[_ngcontent-%COMP%] {\\n  border-radius: 40px;\\n  display: flex;\\n  background-color: #fff;\\n  align-items: center;\\n  margin-bottom: 10px;\\n  transition: all 0.3s ease-in-out;\\n}\\n.library-file-row[_ngcontent-%COMP%]:hover, .library-file-row.checked[_ngcontent-%COMP%] {\\n  color: #ffffff;\\n  background: rgb(0, 44, 207);\\n  background: linear-gradient(90deg, rgb(0, 44, 207) 0%, rgba(201, 210, 255, 0.0956757703) 100%);\\n}\\n.library-file-row.purple-hover[_ngcontent-%COMP%]:hover, .library-file-row.purple-hover.checked[_ngcontent-%COMP%] {\\n  background: linear-gradient(90deg, rgb(102, 89, 205) 0%, rgba(201, 210, 255, 0.0956757703) 100%);\\n}\\n.library-file-row[_ngcontent-%COMP%]   svg.library-file-icon[_ngcontent-%COMP%] {\\n  padding: 8px;\\n  margin-top: 5px;\\n}\\n.library-file-row[_ngcontent-%COMP%]   .library-file-details[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n}\\n.library-file-row[_ngcontent-%COMP%]   .library-file-details[_ngcontent-%COMP%]   .library-file-meta[_ngcontent-%COMP%] {\\n  margin-top: 5px;\\n  display: flex;\\n}\\n.library-file-row[_ngcontent-%COMP%]   .library-file-details[_ngcontent-%COMP%]   .library-file-meta[_ngcontent-%COMP%]   span.file-level[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background: #444070;\\n  padding: 6px;\\n  border-radius: 50px;\\n}\\n.library-file-row[_ngcontent-%COMP%]   .library-file-details[_ngcontent-%COMP%]   .library-file-meta[_ngcontent-%COMP%]   span.file-category[_ngcontent-%COMP%] {\\n  color: #fff;\\n  background-color: #A44FD0;\\n  padding: 6px;\\n  border-radius: 50px;\\n  margin-left: 6px;\\n}\\n.library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%] {\\n  width: 180px;\\n  position: absolute;\\n  left: 92.5%;\\n  top: 69px;\\n  background-color: white;\\n  border-radius: 12px;\\n  border: 1px solid var(--main-color);\\n  box-sizing: border-box;\\n  transition-duration: 0.2s;\\n  transition-property: transform;\\n  background-color: white;\\n  z-index: 1;\\n  flex-direction: column;\\n  padding: 10px;\\n}\\n@media screen and (max-width: 1124px) {\\n  .library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%] {\\n    right: 30px;\\n  }\\n}\\n.library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]   .lib-file-menu-col[_ngcontent-%COMP%] {\\n  margin-top: 5px;\\n  display: flex;\\n  align-items: center;\\n}\\n.library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]   .lib-file-menu-col[_ngcontent-%COMP%]   .lib-file-menu-text[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n  cursor: pointer;\\n}\\n.library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]   .lib-file-menu-col[_ngcontent-%COMP%]   .lib-file-menu-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  display: flex;\\n  justify-content: center;\\n}\\n.library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n\\n  .p-menu {\\n  border: 1px solid #1a266c !important;\\n}\\n\\n  .p-menu .p-menuitem-link {\\n  padding: 0.5rem 1rem !important;\\n}\\n\\n.more-circle[_ngcontent-%COMP%] {\\n  background: linear-gradient(#657aef 0%, #1a266c 100%);\\n  color: #fff;\\n  padding: 5px 7px;\\n  border-radius: 100px;\\n  line-height: 1;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "DialogService", "take", "environment", "LibraryFileActionsDialogComponent", "SubSink", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "level_r2", "ɵɵtext", "ɵɵclassMapInterpolate1", "category_r3", "toLowerCase", "ɵɵtextInterpolate", "ɵɵelementContainerStart", "ɵɵpipeBind2", "ctx_r3", "classroomFile", "categories", "join", "ɵɵtextInterpolate1", "length", "ɵɵtemplate", "LibraryFileRowItemComponent_div_17_ng_template_2_span_0_Template", "LibraryFileRowItemComponent_div_17_ng_container_1_Template", "LibraryFileRowItemComponent_div_17_ng_template_2_Template", "ɵɵtemplateRefExtractor", "is<PERSON>arge", "notMobileBlock_r5", "ɵɵlistener", "LibraryFileRowItemComponent_div_19_Template_div_click_0_listener", "ɵɵrestoreView", "_r7", "ɵɵnextContext", "ɵɵresetView", "checkLibraryItem", "checkLibMenu", "LibraryFileRowItemComponent_div_19_img_1_Template", "LibraryFileRowItemComponent_div_19_img_2_Template", "checked", "LibraryFileRowItemComponent", "constructor", "libraryService", "generalService", "dialogService", "toastService", "authService", "http", "classroom", "appendDialogActionsTo", "showGroupActions", "hasLimitedOptions", "availableActions", "isSmallScreen", "showSharedWithUsers", "isInTab", "fileDeleted", "libraryChecked", "fileUnshared", "StudentIdsToSend", "isDisabled", "isDisabledWholeClass", "moveFileId", "moveFolderId", "isMoveRadioCheked", "showRename", "refs", "subs", "sharedWithUsers", "menuItems", "hasMultipleCategoriesSlice", "ngOnInit", "getUsersLibraryHasBeenSharedWith", "sink", "subscribe", "res", "add", "deviceKind", "pipe", "w768up", "prepareMenuItemsForItem", "ngOnDestroy", "unsubscribe", "onSelect", "studentId", "indexOf", "splice", "push", "showMoveFilePopup", "fileId", "document", "getElementById", "style", "display", "top", "getFileName", "path", "n", "lastIndexOf", "result", "substring", "item", "context", "label", "icon", "command", "event", "openFileInNewTab", "copyURL", "downloadFile", "isStudent", "shareWithTeacher", "currentSelectedClassroom", "getCurrentSelectedClassroom", "Object", "keys", "classroomStudents", "<PERSON><PERSON><PERSON>er", "send", "fileIds", "classroomId", "id", "studentIds", "aspUserId", "sendShareToApi", "openDialogWithComponent", "action", "dialogsubTitle", "name", "dialogTitle", "libraryFile", "console", "log", "teacher", "sendUnShareToApi", "folders", "isNullishObject", "deleteFile", "filter", "menuItem", "menuLabel", "includes", "showSharePopup", "rename", "newName", "updateLibraryFileName", "setShowToastmessage", "severity", "summary", "detail", "el", "checkedLibraries", "emit", "file", "getUniqueSharedUsers", "filteredUsers", "user", "find", "student", "uniqueNames", "values", "reduce", "acc", "firstName", "onWholeClassSelect", "for<PERSON>ach", "moveFile", "moveFolder", "moveFileToFolder", "folderId", "response", "setUpdateFolderListener", "setUpdateListener", "filePath", "fileURL", "apiUrl", "selBox", "createElement", "position", "left", "opacity", "value", "body", "append<PERSON><PERSON><PERSON>", "focus", "select", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "shareLibraryFile", "map", "users", "ids", "unShareLibraryFile", "url", "window", "open", "sendParams", "shareFileWithClassUsers", "setCurrentSelectedClassroomId", "parseInt", "unShareLibrary", "component", "dialogData", "width", "folder", "get", "responseType", "blob", "link", "href", "URL", "createObjectURL", "download", "click", "_", "ɵɵdirectiveInject", "i1", "LibraryService", "i2", "GeneralService", "i3", "i4", "ToastService", "i5", "AuthService", "i6", "HttpClient", "_2", "selectors", "inputs", "outputs", "features", "ɵɵProvidersFeature", "decls", "vars", "consts", "template", "LibraryFileRowItemComponent_Template", "rf", "ctx", "LibraryFileRowItemComponent_Template_span_click_6_listener", "_r1", "LibraryFileRowItemComponent_div_9_Template", "LibraryFileRowItemComponent_img_10_Template", "LibraryFileRowItemComponent_p_chip_15_Template", "LibraryFileRowItemComponent_div_17_Template", "LibraryFileRowItemComponent_Template_div_click_18_listener", "$event", "menu3_r6", "ɵɵreference", "toggle", "LibraryFileRowItemComponent_div_19_Template", "LibraryFileRowItemComponent_img_20_Template", "ɵɵpureFunction2", "_c0", "_c1", "levels", "ɵɵpipeBind3", "ɵɵpropertyInterpolate1", "isPreviewingFromChat"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\library\\library\\components\\library-file-row-item\\library-file-row-item.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\library\\library\\components\\library-file-row-item\\library-file-row-item.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';\r\nimport { take } from 'rxjs/operators';\r\nimport { Classroom } from 'src/app/core/models/classroom.model';\r\nimport { LibraryService } from 'src/app/core/services/library.service';\r\nimport { environment } from 'src/environments/environment';\r\nimport { LibraryFileActionsDialogComponent } from '../library-file-actions-dialog/library-file-actions-dialog.component';\r\nimport { Folder, LibraryFile } from 'src/app/core/models/library.model';\r\nimport { User } from 'src/app/core/models/user.model';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { SubSink } from 'subsink';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\n\r\n@Component({\r\n  selector: 'app-library-file-row-item',\r\n  templateUrl: './library-file-row-item.component.html',\r\n  styleUrls: ['./library-file-row-item.component.scss'],\r\n  providers: [DialogService]\r\n})\r\nexport class LibraryFileRowItemComponent implements OnInit, OnDestroy {\r\n\r\n  @Input() classroom = {} as Classroom;\r\n  @Input() classroomFile: any = {} as any;\r\n  @Input() folders: any;\r\n  @Input() appendDialogActionsTo = 'allFiles';\r\n  @Input() showGroupActions = false;\r\n  @Input() hasLimitedOptions = false;\r\n  @Input() availableActions: string[] = [] as string[];\r\n  @Input() isSmallScreen = false;\r\n  @Input() showSharedWithUsers = true;\r\n  @Input() isInTab = false;\r\n  @Input() classroomStudents: any;\r\n\r\n  @Output() fileDeleted = new EventEmitter();\r\n  @Output() libraryChecked = new EventEmitter();\r\n  @Output() fileUnshared = new EventEmitter();\r\n  libraryFiles: any;\r\n  StudentIdsToSend: string[] = [];\r\n  isDisabled = false;\r\n  isDisabledWholeClass = false;\r\n  fileURL: string | undefined;\r\n  moveFileId = 0;\r\n  moveFolderId = 0;\r\n  isMoveRadioCheked = false;\r\n  showRename: boolean = false;\r\n  refs: DynamicDialogRef[] = [];\r\n  private subs = new SubSink();\r\n  checked: boolean = false;\r\n  sharedWithUsers = [];\r\n  menuItems: MenuItem[] = [];\r\n  isLarge = false;\r\n  hasMultipleCategoriesSlice = 1;\r\n  constructor(\r\n    public libraryService: LibraryService,\r\n    private generalService: GeneralService,\r\n    private dialogService: DialogService,\r\n    private toastService: ToastService,\r\n    private authService: AuthService,\r\n    private http: HttpClient,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    if (this.showSharedWithUsers) {\r\n      this.getUsersLibraryHasBeenSharedWith();\r\n    }\r\n    this.subs.sink = this.libraryService.showGroupActions.subscribe(res => {\r\n      if (!res) {\r\n        this.checked = false;\r\n      }\r\n      // if (this.libraryService.isPreviewingFromChat()) {\r\n      //   this.checked = true;\r\n      // }\r\n    });\r\n    \r\n    this.subs.add(this.generalService.deviceKind.pipe().subscribe(res => {\r\n      if (res) {\r\n        if (res.w768up) {\r\n          this.isLarge = true;\r\n          this.hasMultipleCategoriesSlice = 2;\r\n        }\r\n      }\r\n    }));\r\n    this.prepareMenuItemsForItem(this.classroomFile);\r\n\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  onSelect(studentId: string) {\r\n    this.isDisabled = false;\r\n    if (this.StudentIdsToSend.indexOf(studentId) !== -1)\r\n      this.StudentIdsToSend.splice(this.StudentIdsToSend.indexOf(studentId), 1);\r\n    else\r\n      this.StudentIdsToSend.push(studentId);\r\n\r\n    if (this.isDisabledWholeClass && this.StudentIdsToSend.length === 0)\r\n      this.isDisabledWholeClass = false;\r\n    else\r\n      this.isDisabledWholeClass = true;\r\n  }\r\n\r\n  showMoveFilePopup(fileId: any) {\r\n    document.getElementById('lib-file-share-menu-' + fileId)!.style.display = 'none';\r\n    document.getElementById('lib-file-move-menu-' + fileId)!.style.display = 'block';\r\n    document.getElementById('lib-file-move-menu-' + fileId)!.style.top = '55%';\r\n  }\r\n\r\n  // openSharedByFileMenu(fileId: any) {\r\n  //   alert(fileId);\r\n  //   [].forEach.call(document.getElementsByClassName('lib-file-sharedby-menu'), (el: any) => {\r\n  //     el.style.display = 'none';\r\n  //   })\r\n  //   setTimeout(() => {\r\n  //     document.getElementById('lib-file-sharedby-menu-' + fileId)!.style.display = 'flex';\r\n  //     document.getElementById('lib-file-sharedby-menu-' + fileId)!.style.top = (880 + document.getElementById('lib-file-sharedby-menu-' + fileId)!.offsetTop) + \"px\";\r\n  //   }, 100);\r\n  //   // this.showShare = true;\r\n  // }\r\n\r\n  // showSharedByMoveFilePopup(fileId: any) {\r\n  //   document.getElementById('lib-file-sharedby-move-menu-' + fileId)!.style.display = 'block';\r\n  //   document.getElementById('lib-file-sharedby-move-menu-' + fileId)!.style.top = '55%';\r\n  // }\r\n\r\n  getFileName(path: string) {\r\n    if (path) {\r\n      let n = path.lastIndexOf(\"/\");\r\n      if (n === -1)\r\n        n = path.lastIndexOf(\"\\\\\");\r\n      let result = path.substring(n + 1);\r\n      return result;\r\n    }\r\n    return path\r\n  }\r\n\r\n  prepareMenuItemsForItem(item: any) {\r\n    let context: LibraryFile = item;\r\n\r\n    const menuItems: MenuItem[] = [\r\n      {\r\n        label: 'View', icon: 'pi pi-fw pi-external-link',\r\n        command: (event) => {\r\n          this.openFileInNewTab(context.path);\r\n        }\r\n      },\r\n      {\r\n        label: 'Copy URL', icon: 'pi pi-fw pi-copy',\r\n        command: (event) => {\r\n          this.copyURL(context.path);\r\n        }\r\n      },\r\n      {\r\n        label: 'Download', icon: 'pi pi-fw pi-cloud-download',\r\n        command: (event) => {\r\n          this.downloadFile(context.path);\r\n        }\r\n      },\r\n      {\r\n        label: 'Share', icon: 'pi pi-fw pi-share-alt',\r\n        command: (event) => {\r\n          if (this.authService.isStudent) {\r\n            this.shareWithTeacher(context);\r\n          } else {\r\n            const currentSelectedClassroom = this.libraryService.getCurrentSelectedClassroom()\r\n            if (Object.keys(currentSelectedClassroom).length > 0) {\r\n              const classroomStudents = this.libraryService.getCurrentSelectedClassroom().classroomStudents;\r\n              // if only one student, share immediately\r\n              if (classroomStudents.length === 1 && this.authService.isTeacher) {\r\n                let send: any = {\r\n                  fileIds: [context.fileId],\r\n                  classroomId: this.libraryService.getCurrentSelectedClassroom().id,\r\n                  studentIds: [classroomStudents[0].aspUserId]\r\n                }\r\n                this.sendShareToApi(send);\r\n              }\r\n            } else {\r\n              this.openDialogWithComponent(LibraryFileActionsDialogComponent,\r\n                {\r\n                  action: 'shareWith',\r\n                  dialogsubTitle: 'Share \"' + this.getFileName(context.name) + '\" with...',\r\n                  dialogTitle: 'Share file with...',\r\n                  libraryFile: context\r\n                });\r\n            }\r\n\r\n            console.log(this.libraryService.getCurrentSelectedClassroom().classroomStudents);\r\n            // this.openDialogWithComponent(LibraryFileActionsDialogComponent,\r\n            //   {\r\n            //     action: 'shareWith',\r\n            //     dialogsubTitle: 'Share \"' + this.getFileName(context.name) + '\" with...',\r\n            //     dialogTitle: 'Share file with...',\r\n            //     libraryFile: context\r\n            //   });\r\n          }\r\n        }\r\n      },\r\n      {\r\n        label: 'Unshare', icon: 'pi pi-fw pi-share-alt',\r\n        command: (event) => {\r\n          let send: any = {\r\n            fileId: this.classroomFile.fileId!,\r\n            classroomId: this.libraryService.getCurrentSelectedClassroom().id,\r\n            studentIds: [this.libraryService.getCurrentSelectedClassroom().teacher?.aspUserId]\r\n          }\r\n          if (this.authService.isStudent) {\r\n            this.sendUnShareToApi(send);\r\n          } else {\r\n            \r\n            this.sendUnShareToApi(send);\r\n            // this.openDialogWithComponent(LibraryFileActionsDialogComponent,\r\n            //   {\r\n            //     action: 'unShareWith',\r\n            //     dialogsubTitle: 'Share \"' + this.getFileName(context.name) + '\" with...',\r\n            //     dialogTitle: 'Share file with...',\r\n            //     libraryFile: context\r\n            //   });\r\n          }\r\n        }\r\n      },\r\n      {\r\n        label: 'Move', icon: 'pi pi-fw pi-folder-open',\r\n        command: (event) => {\r\n          this.openDialogWithComponent(LibraryFileActionsDialogComponent,\r\n            {\r\n              action: 'move',\r\n              dialogsubTitle: 'Move \"' + this.getFileName(context.name) + '\" to folder...',\r\n              dialogTitle: 'Move file to folder',\r\n              libraryFile: context,\r\n              folders: this.folders\r\n            });\r\n        }\r\n      },\r\n      {\r\n        label: 'Edit', icon: 'pi pi-fw pi-file-edit',\r\n        command: (event) => {\r\n          this.generalService.openDialogWithComponent(this.dialogService, LibraryFileActionsDialogComponent, 440,\r\n            {\r\n              action: 'editFileLevelsCategories',\r\n              dialogsubTitle: '',\r\n              dialogTitle: 'Edit Library File',\r\n              libraryFile: context,\r\n            }, null, (result: any) => {\r\n              console.log(result);\r\n              if (result && result.action === 'editFileLevelsCategories') {\r\n                if (!this.generalService.isNullishObject(result)) {\r\n\r\n                  console.log(this.classroomFile);\r\n                  console.log(result);\r\n                  this.classroomFile = { ...result.result };\r\n                  context = { ...result.result };\r\n                }\r\n              }\r\n            });\r\n        }\r\n      },\r\n      {\r\n        label: 'Delete', icon: 'pi pi-fw pi-trash',\r\n        command: (event) => {\r\n          this.deleteFile(item.fileId);\r\n        }\r\n      }\r\n    ];\r\n\r\n    this.menuItems = menuItems.filter((menuItem: MenuItem) => {\r\n        const menuLabel = menuItem.label as string;\r\n        return this.availableActions.includes(menuLabel);\r\n    });\r\n\r\n  }\r\n\r\n  showSharePopup(fileId: number) {\r\n    document.getElementById('lib-file-move-menu-' + fileId)!.style.display = 'none';\r\n    document.getElementById('lib-file-share-menu-' + fileId)!.style.display = 'block';\r\n    document.getElementById('lib-file-share-menu-' + fileId)!.style.top = '25%';\r\n  }\r\n\r\n  rename(fileId: number, newName: string) {\r\n    // const input = document.getElementById('fileName') as HTMLInputElement | null;\r\n    // console.log(input?.value)\r\n    // this.classroomFile.name = input!.value\r\n    this.subs.add(this.libraryService.updateLibraryFileName(fileId, newName).pipe(take(1)).subscribe(res => {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'success',\r\n        summary: '',\r\n        detail: 'File name name has been updated.'\r\n      });\r\n      console.log(res)\r\n      // this.showRename = !this.showRename\r\n    }));\r\n  }\r\n\r\n  checkLibraryItem(el: any) {\r\n    this.checked = !this.checked;\r\n    // let bg = this.checked ? '#2d2a4b' : 'white';\r\n    // el!.style.backgroundColor = bg;\r\n    if (this.checked) {\r\n      this.libraryService.checkedLibraries.push(this.classroomFile);\r\n      this.libraryChecked.emit({ checked: true, file: this.classroomFile })\r\n    } else {\r\n      this.libraryService.checkedLibraries = this.libraryService.checkedLibraries.filter((el: LibraryFile) => {\r\n        return !(el.path == this.classroomFile.path)\r\n      });\r\n      this.libraryChecked.emit({ checked: false, file: this.classroomFile })\r\n    }\r\n  }\r\n\r\n  getUniqueSharedUsers(): string {\r\n    // exclude from sharedWithUsers if not in classroomStudents\r\n    const filteredUsers = this.sharedWithUsers.filter((user: User) => {\r\n      return this.classroomStudents.find((student: User) => {\r\n        return student.aspUserId === user.aspUserId;\r\n      });\r\n    });\r\n\r\n    const uniqueNames = Object.values(filteredUsers.reduce((acc: any, user: any) => {\r\n      if (!acc[user.aspUserId]) {\r\n        acc[user.aspUserId] = user.firstName;\r\n      }\r\n      return acc;\r\n    }, {}));\r\n    return uniqueNames.join(', ');\r\n  }\r\n\r\n  private onWholeClassSelect() {\r\n    if (this.isDisabled)\r\n      this.isDisabled = false;\r\n    else\r\n      this.isDisabled = true;\r\n\r\n    if (this.StudentIdsToSend.length === 0) {\r\n      this.classroom.classroomStudents.forEach((student: { id: string; }) => {\r\n        this.StudentIdsToSend.push(student.id);\r\n      });\r\n    }\r\n    else\r\n      this.StudentIdsToSend = [];\r\n  }\r\n\r\n  private moveFile(libraryFile: LibraryFile, moveFolder: Folder) {\r\n    this.subs.add(this.libraryService.moveFileToFolder(libraryFile.fileId!, moveFolder.folderId!).subscribe((response) => {\r\n      if (!this.generalService.isNullishObject(response)) {\r\n\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'success',\r\n          summary: '',\r\n          detail: 'File moved successfully.'\r\n        });\r\n        this.libraryService.setUpdateFolderListener(libraryFile, moveFolder);\r\n        this.libraryService.setUpdateListener(true);\r\n      }\r\n    }));\r\n    this.ngOnInit();\r\n  }\r\n\r\n  private copyURL(filePath: any) {\r\n    this.fileURL = environment.apiUrl + \"/\" + filePath;\r\n    const selBox = document.createElement('textarea');\r\n    selBox.style.position = 'fixed';\r\n    selBox.style.left = '0';\r\n    selBox.style.top = '0';\r\n    selBox.style.opacity = '0';\r\n    selBox.value = this.fileURL;\r\n    document.body.appendChild(selBox);\r\n    selBox.focus();\r\n    selBox.select();\r\n    document.execCommand('copy');\r\n    document.body.removeChild(selBox);\r\n    this.toastService.setShowToastmessage({\r\n      severity: 'success',\r\n      summary: '',\r\n      detail: 'File path copied successfully.'\r\n    });\r\n  }\r\n\r\n  private deleteFile(fileId: any) {\r\n    this.fileDeleted.emit(this.classroomFile);\r\n    this.ngOnDestroy();\r\n  }\r\n\r\n  private getUsersLibraryHasBeenSharedWith() {\r\n    if (this.classroomFile.fileId) {\r\n      return this.subs.add(this.libraryService.getUsersLibraryHasBeenSharedWith(this.classroomFile.fileId!).pipe(take(1)).subscribe(res => {\r\n        this.sharedWithUsers = res\r\n      }))\r\n    }\r\n    return []\r\n  }\r\n\r\n  private shareWithTeacher(context: any) {\r\n    let send: any = {\r\n      fileIds: [context.fileId],\r\n      classroomId: this.libraryService.getCurrentSelectedClassroom().id,\r\n      studentIds: [this.libraryService.getCurrentSelectedClassroom().teacher?.aspUserId]\r\n    }\r\n    this.sendShareToApi(send);\r\n  }\r\n\r\n  private shareLibraryFile(map: any) {\r\n    console.log(this.classroomFile);\r\n    map.forEach((users: User[], classroomId: number) => {\r\n      let ids: string[] = []\r\n      let fileIds: number[] = []\r\n      for (let user of users) {\r\n        ids.push(user.aspUserId!)\r\n      }\r\n      fileIds.push(this.classroomFile.fileId!)\r\n      let send: any = {\r\n        fileIds: fileIds,\r\n        classroomId: classroomId,\r\n        studentIds: ids\r\n      }\r\n      this.sendShareToApi(send);\r\n    });\r\n  }\r\n\r\n  private unShareLibraryFile(map: any) {\r\n    console.log(this.classroomFile);\r\n    map.forEach((users: User[], classroomId: number) => {\r\n      let ids: string[] = []\r\n      let fileIds: number[] = []\r\n      for (let user of users) {\r\n        ids.push(user.aspUserId!)\r\n      }\r\n      fileIds.push(this.classroomFile.fileId!)\r\n      let send: any = {\r\n        fileId: this.classroomFile.fileId!,\r\n        classroomId: classroomId\r\n      }\r\n      this.sendUnShareToApi(send);\r\n    });\r\n  }\r\n\r\n  openFileInNewTab(url: string): void {\r\n    const fileURL = environment.apiUrl + \"/\" + url;\r\n    window.open(fileURL, '_blank');\r\n  }\r\n\r\n  private sendShareToApi(sendParams: any) {\r\n    this.subs.add(this.libraryService.shareFileWithClassUsers(sendParams).pipe(take(1)).subscribe(res => {\r\n\r\n      console.log(this.classroom);\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'success',\r\n        summary: '',\r\n        detail: 'Library file has been shared.'\r\n      });\r\n      if (this.authService.isTeacher) {\r\n        this.libraryService.setCurrentSelectedClassroomId(parseInt(sendParams.classroomId));\r\n      }\r\n    }));\r\n  }\r\n\r\n  private sendUnShareToApi(sendParams: any) {\r\n    console.log(this.classroom);\r\n    this.subs.add(this.libraryService.unShareLibrary(sendParams).pipe(take(1)).subscribe(res => {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'success',\r\n        summary: '',\r\n        detail: 'Library file has been unshared.'\r\n      });\r\n      this.fileUnshared.emit(res);\r\n      this.libraryService.setCurrentSelectedClassroomId(parseInt(sendParams.classroomId));\r\n    }));\r\n  }\r\n\r\n  private openDialogWithComponent(component: any, dialogData?: any, width = 360) {\r\n\r\n    this.generalService.openDialogWithComponent(this.dialogService, component, 400, dialogData, null, (result: any) => {\r\n      if (result && result.action === 'shareWith') {\r\n        this.shareLibraryFile(result.map.map);\r\n      } else if (result && result.action === 'unShareWith') {\r\n        this.unShareLibraryFile(result.map.map);\r\n      } else if (result && result.action === 'move') {\r\n        this.moveFile(result.libraryFile, result.folder);\r\n      }\r\n    });\r\n\r\n    // this.refs.forEach(ref => ref.close());\r\n    // this.refs = [];\r\n    // const element = document.querySelector('#' + this.appendDialogActionsTo);\r\n    // const rect = element!.getBoundingClientRect();\r\n    // const centerX = rect.left + rect.width / 2 - width / 2;\r\n    // const centerY = rect.top;\r\n    // const dialogRef = (this.dialogService.open(component, {\r\n    //   header: '',\r\n    //   width: width + 'px',\r\n    //   showHeader: false,\r\n    //   dismissableMask: true,\r\n    //   modal: true,\r\n    //   contentStyle: {\r\n    //     \"max-width\": \"100%\", \"max-height\": \"400px\", \"overflow\": \"auto\", \"border-radius\": \"10px\", \"padding\": \"0px\",\r\n    //   },\r\n    //   style: { 'left': `${centerX}px`, 'top': `${centerY}px`, 'position': `fixed` },\r\n    //   baseZIndex: 10000,\r\n    //   maskStyleClass: 'transparent-mask',\r\n    //   data: { dialogData: dialogData },\r\n    // }));\r\n    // this.refs.push(dialogRef);\r\n    // dialogRef.onClose.subscribe((data: any) => {\r\n    //   console.log('Dialog closed with data:', data);\r\n    //   if (data) {\r\n    //     switch (data.action) {\r\n    //       case 'rename':\r\n    //         this.rename(data.libraryFile.fileId, data.libraryFile.name);\r\n    //         break;\r\n    //       case 'shareWith':\r\n    //         this.shareLibraryFiles(data.map.map);\r\n    //         break;\r\n    //       case 'move':\r\n    //         this.moveFile(data.libraryFile.fileId, data.folder.folderId);\r\n    //         break;\r\n\r\n    //       default:\r\n    //         break;\r\n    //     }\r\n    //   }\r\n    // });\r\n  }\r\n  \r\n  private downloadFile(url: string): void {\r\n    const fileURL = environment.apiUrl + \"/\" + url;\r\n    this.subs.add(this.http.get(fileURL, { responseType: 'blob' })\r\n      .subscribe((blob: Blob) => { // Specify the type of blob as Blob\r\n        const link = document.createElement('a');\r\n        link.href = window.URL.createObjectURL(blob);\r\n        link.download = this.getFileName(fileURL);\r\n        link.click();\r\n      }));\r\n  }\r\n\r\n}\r\n", "<li class=\"library-file-row\" [ngClass]=\"{'checked': checked && showGroupActions, 'purple-hover': isInTab}\">\r\n    {{classroom?.id}}\r\n    <div\r\n        class=\"w-3rem h-3rem flex align-items-center justify-content-center bg-grad-blue border-circle mr-1 flex-shrink-0\"\r\n        [ngClass]=\"{'bg-grad-purple': isInTab, 'bg-grad-blue': !isInTab}\">\r\n        <img src=\"/assets/icons/file-white.svg\" height=\"24\">\r\n    </div>\r\n    <div class=\"w-full flex sm:flex-row align-items-center justify-content-between\">\r\n        <div class=\"library-file-details py-1 font-sm flex flex-column\">\r\n            <span (click)=\"openFileInNewTab(classroomFile.path)\" class=\"file-name file-path-text max-w-10rem sm:max-w-15rem pointer\">{{getFileName(classroomFile.name)}}</span>\r\n            <div class=\"flex gap-1\">\r\n                <div *ngFor=\"let level of classroomFile.levels\">\r\n                    <g-level-circle size=\"2xs\" [name]=\"level\"></g-level-circle>\r\n                </div>\r\n                <img src=\"/assets/icons/library/share-sm-icon.svg\" *ngIf=\"this.sharedWithUsers.length > 0\" width=\"10\">\r\n                <span class=\"font-xs file-path-text max-w-10rem\">\r\n                    {{ getUniqueSharedUsers() }}\r\n                </span>\r\n            </div>\r\n        </div>\r\n        <div class=\"library-file-meta\">\r\n            <div class=\"text-900 w-full md:flex-order-0 flex-order-1 flex align-items-end justify-content-end gap-1\">\r\n                <p-chip *ngFor=\"let category of classroomFile.categories | slice:0:hasMultipleCategoriesSlice\"\r\n                    class=\"class-rating {{category.toLowerCase()}} border-round-xl px-0\"><span\r\n                        class=\"font-xs text-white  py-1 px-0 line-height-1\">{{category}}</span></p-chip>\r\n                        <!-- <span *ngIf=\"classroomFile.categories.length > 1\"  class=\"font-xs text-muted mt-2 more-circle\"\r\n                            [pTooltip]=\"(classroomFile.categories | slice:hasMultipleCategoriesSlice).join(', ')\"\r\n                            tooltipPosition=\"top\">\r\n                            +{{classroomFile.categories.length - hasMultipleCategoriesSlice}}\r\n                          </span> -->\r\n                          <div *ngIf=\"classroomFile.categories.length > 1\">\r\n                            <ng-container *ngIf=\"!isLarge; else notMobileBlock\" >\r\n                                <span class=\"font-xs text-muted mt-2 more-circle\"\r\n                                    [pTooltip]=\"(classroomFile.categories | slice:1).join(', ')\"\r\n                                    tooltipPosition=\"top\">\r\n                                    +{{classroomFile.categories.length - 1}}\r\n                                  </span>\r\n                            </ng-container>\r\n                            <ng-template #notMobileBlock>\r\n                                <span *ngIf=\"classroomFile.categories.length > 2\"  class=\"font-xs text-muted mt-2 more-circle\"\r\n                                    [pTooltip]=\"(classroomFile.categories | slice:2).join(', ')\"\r\n                                    tooltipPosition=\"top\">\r\n                                    +{{classroomFile.categories.length - 2}}\r\n                                </span>\r\n                            </ng-template>\r\n                        </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <div class=\"library-actions-button mx-2\" id=\"lib-file-burger-{{classroomFile.fileId}}\" (click)=\"!showGroupActions ? menu3.toggle($event) : null\">\r\n\r\n        <div *ngIf=\"showGroupActions || this.libraryService.isPreviewingFromChat()\" (click)=\"checkLibraryItem(checkLibMenu)\"\r\n            style=\"position: relative;\">\r\n            <img *ngIf=\"!checked\" src=\"/assets/icons/library/group-item-empty.svg\" class=\"pointer\" style=\"width:1.75rem; height:1.75rem\">\r\n            <img *ngIf=\"checked\" src=\"/assets/icons/library/group-item-full.svg\" class=\"pointer\" style=\"width:1.75rem; height:1.75rem\">\r\n            <!-- <div id=\"check-lib-{{libraryFile.filePath}}-1\" #checkLibMenu class=\"inner-circle-group\"></div> -->\r\n        </div>\r\n        <img *ngIf=\"!showGroupActions && !this.libraryService.isPreviewingFromChat()\" src=\"/assets/icons/library/library-more-actions.svg\" class=\"pointer hvr-grow\" style=\"width:1.75rem; height:1.75rem\">\r\n        <!-- <i class=\"ml-auto pi pi-ellipsis-h lession-more-icon center text-primary\"></i> -->\r\n    </div>\r\n\r\n</li>\r\n<p-menu #menu3 popup=\"popup\" [model]=\"menuItems\" appendTo=\"body\"></p-menu>\r\n"], "mappings": "AAAA,SAAoBA,YAAY,QAA0C,eAAe;AAEzF,SAASC,aAAa,QAA0B,uBAAuB;AACvE,SAASC,IAAI,QAAQ,gBAAgB;AAGrC,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,iCAAiC,QAAQ,sEAAsE;AAIxH,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;ICAjBC,EAAA,CAAAC,cAAA,UAAgD;IAC5CD,EAAA,CAAAE,SAAA,yBAA2D;IAC/DF,EAAA,CAAAG,YAAA,EAAM;;;;IADyBH,EAAA,CAAAI,SAAA,EAAc;IAAdJ,EAAA,CAAAK,UAAA,SAAAC,QAAA,CAAc;;;;;IAE7CN,EAAA,CAAAE,SAAA,cAAsG;;;;;IAS7BF,EADzE,CAAAC,cAAA,aACyE,eACb;IAAAD,EAAA,CAAAO,MAAA,GAAY;IAAOP,EAAP,CAAAG,YAAA,EAAO,EAAS;;;;IADpFH,EAAA,CAAAQ,sBAAA,kBAAAC,WAAA,CAAAC,WAAA,4BAAoE;IACZV,EAAA,CAAAI,SAAA,GAAY;IAAZJ,EAAA,CAAAW,iBAAA,CAAAF,WAAA,CAAY;;;;;IAO5DT,EAAA,CAAAY,uBAAA,GAAqD;IACjDZ,EAAA,CAAAC,cAAA,eAE0B;;IACtBD,EAAA,CAAAO,MAAA,GACF;IAAAP,EAAA,CAAAG,YAAA,EAAO;;;;;IAHLH,EAAA,CAAAI,SAAA,EAA4D;IAA5DJ,EAAA,CAAAK,UAAA,aAAAL,EAAA,CAAAa,WAAA,OAAAC,MAAA,CAAAC,aAAA,CAAAC,UAAA,KAAAC,IAAA,OAA4D;IAE5DjB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkB,kBAAA,OAAAJ,MAAA,CAAAC,aAAA,CAAAC,UAAA,CAAAG,MAAA,UACF;;;;;IAGFnB,EAAA,CAAAC,cAAA,eAE0B;;IACtBD,EAAA,CAAAO,MAAA,GACJ;IAAAP,EAAA,CAAAG,YAAA,EAAO;;;;IAHHH,EAAA,CAAAK,UAAA,aAAAL,EAAA,CAAAa,WAAA,OAAAC,MAAA,CAAAC,aAAA,CAAAC,UAAA,KAAAC,IAAA,OAA4D;IAE5DjB,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAkB,kBAAA,OAAAJ,MAAA,CAAAC,aAAA,CAAAC,UAAA,CAAAG,MAAA,UACJ;;;;;IAJAnB,EAAA,CAAAoB,UAAA,IAAAC,gEAAA,mBAE0B;;;;IAFnBrB,EAAA,CAAAK,UAAA,SAAAS,MAAA,CAAAC,aAAA,CAAAC,UAAA,CAAAG,MAAA,KAAyC;;;;;IATtDnB,EAAA,CAAAC,cAAA,UAAiD;IAQ/CD,EAPA,CAAAoB,UAAA,IAAAE,0DAAA,2BAAqD,IAAAC,yDAAA,gCAAAvB,EAAA,CAAAwB,sBAAA,CAOxB;IAOjCxB,EAAA,CAAAG,YAAA,EAAM;;;;;IAdaH,EAAA,CAAAI,SAAA,EAAgB;IAAAJ,EAAhB,CAAAK,UAAA,UAAAS,MAAA,CAAAW,OAAA,CAAgB,aAAAC,iBAAA,CAAmB;;;;;IAsBlE1B,EAAA,CAAAE,SAAA,cAA6H;;;;;IAC7HF,EAAA,CAAAE,SAAA,cAA2H;;;;;;IAH/HF,EAAA,CAAAC,cAAA,cACgC;IAD4CD,EAAA,CAAA2B,UAAA,mBAAAC,iEAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAC,GAAA;MAAA,MAAAhB,MAAA,GAAAd,EAAA,CAAA+B,aAAA;MAAA,OAAA/B,EAAA,CAAAgC,WAAA,CAASlB,MAAA,CAAAmB,gBAAA,CAAAnB,MAAA,CAAAoB,YAAA,CAA8B;IAAA,EAAC;IAGhHlC,EADA,CAAAoB,UAAA,IAAAe,iDAAA,kBAA6H,IAAAC,iDAAA,kBACF;IAE/HpC,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAI,SAAA,EAAc;IAAdJ,EAAA,CAAAK,UAAA,UAAAS,MAAA,CAAAuB,OAAA,CAAc;IACdrC,EAAA,CAAAI,SAAA,EAAa;IAAbJ,EAAA,CAAAK,UAAA,SAAAS,MAAA,CAAAuB,OAAA,CAAa;;;;;IAGvBrC,EAAA,CAAAE,SAAA,cAAkM;;;ADnC1M,OAAM,MAAOoC,2BAA2B;EAiCtCC,YACSC,cAA8B,EAC7BC,cAA8B,EAC9BC,aAA4B,EAC5BC,YAA0B,EAC1BC,WAAwB,EACxBC,IAAgB;IALjB,KAAAL,cAAc,GAAdA,cAAc;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,IAAI,GAAJA,IAAI;IArCL,KAAAC,SAAS,GAAG,EAAe;IAC3B,KAAA/B,aAAa,GAAQ,EAAS;IAE9B,KAAAgC,qBAAqB,GAAG,UAAU;IAClC,KAAAC,gBAAgB,GAAG,KAAK;IACxB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,gBAAgB,GAAa,EAAc;IAC3C,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,mBAAmB,GAAG,IAAI;IAC1B,KAAAC,OAAO,GAAG,KAAK;IAGd,KAAAC,WAAW,GAAG,IAAI5D,YAAY,EAAE;IAChC,KAAA6D,cAAc,GAAG,IAAI7D,YAAY,EAAE;IACnC,KAAA8D,YAAY,GAAG,IAAI9D,YAAY,EAAE;IAE3C,KAAA+D,gBAAgB,GAAa,EAAE;IAC/B,KAAAC,UAAU,GAAG,KAAK;IAClB,KAAAC,oBAAoB,GAAG,KAAK;IAE5B,KAAAC,UAAU,GAAG,CAAC;IACd,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,UAAU,GAAY,KAAK;IAC3B,KAAAC,IAAI,GAAuB,EAAE;IACrB,KAAAC,IAAI,GAAG,IAAIlE,OAAO,EAAE;IAC5B,KAAAsC,OAAO,GAAY,KAAK;IACxB,KAAA6B,eAAe,GAAG,EAAE;IACpB,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAA1C,OAAO,GAAG,KAAK;IACf,KAAA2C,0BAA0B,GAAG,CAAC;EAQ1B;EAEJC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACjB,mBAAmB,EAAE;MAC5B,IAAI,CAACkB,gCAAgC,EAAE;IACzC;IACA,IAAI,CAACL,IAAI,CAACM,IAAI,GAAG,IAAI,CAAC/B,cAAc,CAACQ,gBAAgB,CAACwB,SAAS,CAACC,GAAG,IAAG;MACpE,IAAI,CAACA,GAAG,EAAE;QACR,IAAI,CAACpC,OAAO,GAAG,KAAK;MACtB;MACA;MACA;MACA;IACF,CAAC,CAAC;IAEF,IAAI,CAAC4B,IAAI,CAACS,GAAG,CAAC,IAAI,CAACjC,cAAc,CAACkC,UAAU,CAACC,IAAI,EAAE,CAACJ,SAAS,CAACC,GAAG,IAAG;MAClE,IAAIA,GAAG,EAAE;QACP,IAAIA,GAAG,CAACI,MAAM,EAAE;UACd,IAAI,CAACpD,OAAO,GAAG,IAAI;UACnB,IAAI,CAAC2C,0BAA0B,GAAG,CAAC;QACrC;MACF;IACF,CAAC,CAAC,CAAC;IACH,IAAI,CAACU,uBAAuB,CAAC,IAAI,CAAC/D,aAAa,CAAC;EAElD;EAEAgE,WAAWA,CAAA;IACT,IAAI,CAACd,IAAI,CAACe,WAAW,EAAE;EACzB;EAEAC,QAAQA,CAACC,SAAiB;IACxB,IAAI,CAACxB,UAAU,GAAG,KAAK;IACvB,IAAI,IAAI,CAACD,gBAAgB,CAAC0B,OAAO,CAACD,SAAS,CAAC,KAAK,CAAC,CAAC,EACjD,IAAI,CAACzB,gBAAgB,CAAC2B,MAAM,CAAC,IAAI,CAAC3B,gBAAgB,CAAC0B,OAAO,CAACD,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,KAE1E,IAAI,CAACzB,gBAAgB,CAAC4B,IAAI,CAACH,SAAS,CAAC;IAEvC,IAAI,IAAI,CAACvB,oBAAoB,IAAI,IAAI,CAACF,gBAAgB,CAACtC,MAAM,KAAK,CAAC,EACjE,IAAI,CAACwC,oBAAoB,GAAG,KAAK,CAAC,KAElC,IAAI,CAACA,oBAAoB,GAAG,IAAI;EACpC;EAEA2B,iBAAiBA,CAACC,MAAW;IAC3BC,QAAQ,CAACC,cAAc,CAAC,sBAAsB,GAAGF,MAAM,CAAE,CAACG,KAAK,CAACC,OAAO,GAAG,MAAM;IAChFH,QAAQ,CAACC,cAAc,CAAC,qBAAqB,GAAGF,MAAM,CAAE,CAACG,KAAK,CAACC,OAAO,GAAG,OAAO;IAChFH,QAAQ,CAACC,cAAc,CAAC,qBAAqB,GAAGF,MAAM,CAAE,CAACG,KAAK,CAACE,GAAG,GAAG,KAAK;EAC5E;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EAEAC,WAAWA,CAACC,IAAY;IACtB,IAAIA,IAAI,EAAE;MACR,IAAIC,CAAC,GAAGD,IAAI,CAACE,WAAW,CAAC,GAAG,CAAC;MAC7B,IAAID,CAAC,KAAK,CAAC,CAAC,EACVA,CAAC,GAAGD,IAAI,CAACE,WAAW,CAAC,IAAI,CAAC;MAC5B,IAAIC,MAAM,GAAGH,IAAI,CAACI,SAAS,CAACH,CAAC,GAAG,CAAC,CAAC;MAClC,OAAOE,MAAM;IACf;IACA,OAAOH,IAAI;EACb;EAEAhB,uBAAuBA,CAACqB,IAAS;IAC/B,IAAIC,OAAO,GAAgBD,IAAI;IAE/B,MAAMhC,SAAS,GAAe,CAC5B;MACEkC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,2BAA2B;MAChDC,OAAO,EAAGC,KAAK,IAAI;QACjB,IAAI,CAACC,gBAAgB,CAACL,OAAO,CAACN,IAAI,CAAC;MACrC;KACD,EACD;MACEO,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE,kBAAkB;MAC3CC,OAAO,EAAGC,KAAK,IAAI;QACjB,IAAI,CAACE,OAAO,CAACN,OAAO,CAACN,IAAI,CAAC;MAC5B;KACD,EACD;MACEO,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE,4BAA4B;MACrDC,OAAO,EAAGC,KAAK,IAAI;QACjB,IAAI,CAACG,YAAY,CAACP,OAAO,CAACN,IAAI,CAAC;MACjC;KACD,EACD;MACEO,KAAK,EAAE,OAAO;MAAEC,IAAI,EAAE,uBAAuB;MAC7CC,OAAO,EAAGC,KAAK,IAAI;QACjB,IAAI,IAAI,CAAC5D,WAAW,CAACgE,SAAS,EAAE;UAC9B,IAAI,CAACC,gBAAgB,CAACT,OAAO,CAAC;QAChC,CAAC,MAAM;UACL,MAAMU,wBAAwB,GAAG,IAAI,CAACtE,cAAc,CAACuE,2BAA2B,EAAE;UAClF,IAAIC,MAAM,CAACC,IAAI,CAACH,wBAAwB,CAAC,CAAC3F,MAAM,GAAG,CAAC,EAAE;YACpD,MAAM+F,iBAAiB,GAAG,IAAI,CAAC1E,cAAc,CAACuE,2BAA2B,EAAE,CAACG,iBAAiB;YAC7F;YACA,IAAIA,iBAAiB,CAAC/F,MAAM,KAAK,CAAC,IAAI,IAAI,CAACyB,WAAW,CAACuE,SAAS,EAAE;cAChE,IAAIC,IAAI,GAAQ;gBACdC,OAAO,EAAE,CAACjB,OAAO,CAACb,MAAM,CAAC;gBACzB+B,WAAW,EAAE,IAAI,CAAC9E,cAAc,CAACuE,2BAA2B,EAAE,CAACQ,EAAE;gBACjEC,UAAU,EAAE,CAACN,iBAAiB,CAAC,CAAC,CAAC,CAACO,SAAS;eAC5C;cACD,IAAI,CAACC,cAAc,CAACN,IAAI,CAAC;YAC3B;UACF,CAAC,MAAM;YACL,IAAI,CAACO,uBAAuB,CAAC7H,iCAAiC,EAC5D;cACE8H,MAAM,EAAE,WAAW;cACnBC,cAAc,EAAE,SAAS,GAAG,IAAI,CAAChC,WAAW,CAACO,OAAO,CAAC0B,IAAI,CAAC,GAAG,WAAW;cACxEC,WAAW,EAAE,oBAAoB;cACjCC,WAAW,EAAE5B;aACd,CAAC;UACN;UAEA6B,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC1F,cAAc,CAACuE,2BAA2B,EAAE,CAACG,iBAAiB,CAAC;UAChF;UACA;UACA;UACA;UACA;UACA;UACA;QACF;MACF;KACD,EACD;MACEb,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE,uBAAuB;MAC/CC,OAAO,EAAGC,KAAK,IAAI;QACjB,IAAIY,IAAI,GAAQ;UACd7B,MAAM,EAAE,IAAI,CAACxE,aAAa,CAACwE,MAAO;UAClC+B,WAAW,EAAE,IAAI,CAAC9E,cAAc,CAACuE,2BAA2B,EAAE,CAACQ,EAAE;UACjEC,UAAU,EAAE,CAAC,IAAI,CAAChF,cAAc,CAACuE,2BAA2B,EAAE,CAACoB,OAAO,EAAEV,SAAS;SAClF;QACD,IAAI,IAAI,CAAC7E,WAAW,CAACgE,SAAS,EAAE;UAC9B,IAAI,CAACwB,gBAAgB,CAAChB,IAAI,CAAC;QAC7B,CAAC,MAAM;UAEL,IAAI,CAACgB,gBAAgB,CAAChB,IAAI,CAAC;UAC3B;UACA;UACA;UACA;UACA;UACA;UACA;QACF;MACF;KACD,EACD;MACEf,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,yBAAyB;MAC9CC,OAAO,EAAGC,KAAK,IAAI;QACjB,IAAI,CAACmB,uBAAuB,CAAC7H,iCAAiC,EAC5D;UACE8H,MAAM,EAAE,MAAM;UACdC,cAAc,EAAE,QAAQ,GAAG,IAAI,CAAChC,WAAW,CAACO,OAAO,CAAC0B,IAAI,CAAC,GAAG,gBAAgB;UAC5EC,WAAW,EAAE,qBAAqB;UAClCC,WAAW,EAAE5B,OAAO;UACpBiC,OAAO,EAAE,IAAI,CAACA;SACf,CAAC;MACN;KACD,EACD;MACEhC,KAAK,EAAE,MAAM;MAAEC,IAAI,EAAE,uBAAuB;MAC5CC,OAAO,EAAGC,KAAK,IAAI;QACjB,IAAI,CAAC/D,cAAc,CAACkF,uBAAuB,CAAC,IAAI,CAACjF,aAAa,EAAE5C,iCAAiC,EAAE,GAAG,EACpG;UACE8H,MAAM,EAAE,0BAA0B;UAClCC,cAAc,EAAE,EAAE;UAClBE,WAAW,EAAE,mBAAmB;UAChCC,WAAW,EAAE5B;SACd,EAAE,IAAI,EAAGH,MAAW,IAAI;UACvBgC,OAAO,CAACC,GAAG,CAACjC,MAAM,CAAC;UACnB,IAAIA,MAAM,IAAIA,MAAM,CAAC2B,MAAM,KAAK,0BAA0B,EAAE;YAC1D,IAAI,CAAC,IAAI,CAACnF,cAAc,CAAC6F,eAAe,CAACrC,MAAM,CAAC,EAAE;cAEhDgC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACnH,aAAa,CAAC;cAC/BkH,OAAO,CAACC,GAAG,CAACjC,MAAM,CAAC;cACnB,IAAI,CAAClF,aAAa,GAAG;gBAAE,GAAGkF,MAAM,CAACA;cAAM,CAAE;cACzCG,OAAO,GAAG;gBAAE,GAAGH,MAAM,CAACA;cAAM,CAAE;YAChC;UACF;QACF,CAAC,CAAC;MACN;KACD,EACD;MACEI,KAAK,EAAE,QAAQ;MAAEC,IAAI,EAAE,mBAAmB;MAC1CC,OAAO,EAAGC,KAAK,IAAI;QACjB,IAAI,CAAC+B,UAAU,CAACpC,IAAI,CAACZ,MAAM,CAAC;MAC9B;KACD,CACF;IAED,IAAI,CAACpB,SAAS,GAAGA,SAAS,CAACqE,MAAM,CAAEC,QAAkB,IAAI;MACrD,MAAMC,SAAS,GAAGD,QAAQ,CAACpC,KAAe;MAC1C,OAAO,IAAI,CAACnD,gBAAgB,CAACyF,QAAQ,CAACD,SAAS,CAAC;IACpD,CAAC,CAAC;EAEJ;EAEAE,cAAcA,CAACrD,MAAc;IAC3BC,QAAQ,CAACC,cAAc,CAAC,qBAAqB,GAAGF,MAAM,CAAE,CAACG,KAAK,CAACC,OAAO,GAAG,MAAM;IAC/EH,QAAQ,CAACC,cAAc,CAAC,sBAAsB,GAAGF,MAAM,CAAE,CAACG,KAAK,CAACC,OAAO,GAAG,OAAO;IACjFH,QAAQ,CAACC,cAAc,CAAC,sBAAsB,GAAGF,MAAM,CAAE,CAACG,KAAK,CAACE,GAAG,GAAG,KAAK;EAC7E;EAEAiD,MAAMA,CAACtD,MAAc,EAAEuD,OAAe;IACpC;IACA;IACA;IACA,IAAI,CAAC7E,IAAI,CAACS,GAAG,CAAC,IAAI,CAAClC,cAAc,CAACuG,qBAAqB,CAACxD,MAAM,EAAEuD,OAAO,CAAC,CAAClE,IAAI,CAAChF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC4E,SAAS,CAACC,GAAG,IAAG;MACrG,IAAI,CAAC9B,YAAY,CAACqG,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;MACFlB,OAAO,CAACC,GAAG,CAACzD,GAAG,CAAC;MAChB;IACF,CAAC,CAAC,CAAC;EACL;EAEAxC,gBAAgBA,CAACmH,EAAO;IACtB,IAAI,CAAC/G,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5B;IACA;IACA,IAAI,IAAI,CAACA,OAAO,EAAE;MAChB,IAAI,CAACG,cAAc,CAAC6G,gBAAgB,CAAChE,IAAI,CAAC,IAAI,CAACtE,aAAa,CAAC;MAC7D,IAAI,CAACwC,cAAc,CAAC+F,IAAI,CAAC;QAAEjH,OAAO,EAAE,IAAI;QAAEkH,IAAI,EAAE,IAAI,CAACxI;MAAa,CAAE,CAAC;IACvE,CAAC,MAAM;MACL,IAAI,CAACyB,cAAc,CAAC6G,gBAAgB,GAAG,IAAI,CAAC7G,cAAc,CAAC6G,gBAAgB,CAACb,MAAM,CAAEY,EAAe,IAAI;QACrG,OAAO,EAAEA,EAAE,CAACtD,IAAI,IAAI,IAAI,CAAC/E,aAAa,CAAC+E,IAAI,CAAC;MAC9C,CAAC,CAAC;MACF,IAAI,CAACvC,cAAc,CAAC+F,IAAI,CAAC;QAAEjH,OAAO,EAAE,KAAK;QAAEkH,IAAI,EAAE,IAAI,CAACxI;MAAa,CAAE,CAAC;IACxE;EACF;EAEAyI,oBAAoBA,CAAA;IAClB;IACA,MAAMC,aAAa,GAAG,IAAI,CAACvF,eAAe,CAACsE,MAAM,CAAEkB,IAAU,IAAI;MAC/D,OAAO,IAAI,CAACxC,iBAAiB,CAACyC,IAAI,CAAEC,OAAa,IAAI;QACnD,OAAOA,OAAO,CAACnC,SAAS,KAAKiC,IAAI,CAACjC,SAAS;MAC7C,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,MAAMoC,WAAW,GAAG7C,MAAM,CAAC8C,MAAM,CAACL,aAAa,CAACM,MAAM,CAAC,CAACC,GAAQ,EAAEN,IAAS,KAAI;MAC7E,IAAI,CAACM,GAAG,CAACN,IAAI,CAACjC,SAAS,CAAC,EAAE;QACxBuC,GAAG,CAACN,IAAI,CAACjC,SAAS,CAAC,GAAGiC,IAAI,CAACO,SAAS;MACtC;MACA,OAAOD,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC,CAAC;IACP,OAAOH,WAAW,CAAC5I,IAAI,CAAC,IAAI,CAAC;EAC/B;EAEQiJ,kBAAkBA,CAAA;IACxB,IAAI,IAAI,CAACxG,UAAU,EACjB,IAAI,CAACA,UAAU,GAAG,KAAK,CAAC,KAExB,IAAI,CAACA,UAAU,GAAG,IAAI;IAExB,IAAI,IAAI,CAACD,gBAAgB,CAACtC,MAAM,KAAK,CAAC,EAAE;MACtC,IAAI,CAAC2B,SAAS,CAACoE,iBAAiB,CAACiD,OAAO,CAAEP,OAAwB,IAAI;QACpE,IAAI,CAACnG,gBAAgB,CAAC4B,IAAI,CAACuE,OAAO,CAACrC,EAAE,CAAC;MACxC,CAAC,CAAC;IACJ,CAAC,MAEC,IAAI,CAAC9D,gBAAgB,GAAG,EAAE;EAC9B;EAEQ2G,QAAQA,CAACpC,WAAwB,EAAEqC,UAAkB;IAC3D,IAAI,CAACpG,IAAI,CAACS,GAAG,CAAC,IAAI,CAAClC,cAAc,CAAC8H,gBAAgB,CAACtC,WAAW,CAACzC,MAAO,EAAE8E,UAAU,CAACE,QAAS,CAAC,CAAC/F,SAAS,CAAEgG,QAAQ,IAAI;MACnH,IAAI,CAAC,IAAI,CAAC/H,cAAc,CAAC6F,eAAe,CAACkC,QAAQ,CAAC,EAAE;QAElD,IAAI,CAAC7H,YAAY,CAACqG,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAAC3G,cAAc,CAACiI,uBAAuB,CAACzC,WAAW,EAAEqC,UAAU,CAAC;QACpE,IAAI,CAAC7H,cAAc,CAACkI,iBAAiB,CAAC,IAAI,CAAC;MAC7C;IACF,CAAC,CAAC,CAAC;IACH,IAAI,CAACrG,QAAQ,EAAE;EACjB;EAEQqC,OAAOA,CAACiE,QAAa;IAC3B,IAAI,CAACC,OAAO,GAAG/K,WAAW,CAACgL,MAAM,GAAG,GAAG,GAAGF,QAAQ;IAClD,MAAMG,MAAM,GAAGtF,QAAQ,CAACuF,aAAa,CAAC,UAAU,CAAC;IACjDD,MAAM,CAACpF,KAAK,CAACsF,QAAQ,GAAG,OAAO;IAC/BF,MAAM,CAACpF,KAAK,CAACuF,IAAI,GAAG,GAAG;IACvBH,MAAM,CAACpF,KAAK,CAACE,GAAG,GAAG,GAAG;IACtBkF,MAAM,CAACpF,KAAK,CAACwF,OAAO,GAAG,GAAG;IAC1BJ,MAAM,CAACK,KAAK,GAAG,IAAI,CAACP,OAAO;IAC3BpF,QAAQ,CAAC4F,IAAI,CAACC,WAAW,CAACP,MAAM,CAAC;IACjCA,MAAM,CAACQ,KAAK,EAAE;IACdR,MAAM,CAACS,MAAM,EAAE;IACf/F,QAAQ,CAACgG,WAAW,CAAC,MAAM,CAAC;IAC5BhG,QAAQ,CAAC4F,IAAI,CAACK,WAAW,CAACX,MAAM,CAAC;IACjC,IAAI,CAACnI,YAAY,CAACqG,mBAAmB,CAAC;MACpCC,QAAQ,EAAE,SAAS;MACnBC,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE;KACT,CAAC;EACJ;EAEQZ,UAAUA,CAAChD,MAAW;IAC5B,IAAI,CAACjC,WAAW,CAACgG,IAAI,CAAC,IAAI,CAACvI,aAAa,CAAC;IACzC,IAAI,CAACgE,WAAW,EAAE;EACpB;EAEQT,gCAAgCA,CAAA;IACtC,IAAI,IAAI,CAACvD,aAAa,CAACwE,MAAM,EAAE;MAC7B,OAAO,IAAI,CAACtB,IAAI,CAACS,GAAG,CAAC,IAAI,CAAClC,cAAc,CAAC8B,gCAAgC,CAAC,IAAI,CAACvD,aAAa,CAACwE,MAAO,CAAC,CAACX,IAAI,CAAChF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC4E,SAAS,CAACC,GAAG,IAAG;QAClI,IAAI,CAACP,eAAe,GAAGO,GAAG;MAC5B,CAAC,CAAC,CAAC;IACL;IACA,OAAO,EAAE;EACX;EAEQoC,gBAAgBA,CAACT,OAAY;IACnC,IAAIgB,IAAI,GAAQ;MACdC,OAAO,EAAE,CAACjB,OAAO,CAACb,MAAM,CAAC;MACzB+B,WAAW,EAAE,IAAI,CAAC9E,cAAc,CAACuE,2BAA2B,EAAE,CAACQ,EAAE;MACjEC,UAAU,EAAE,CAAC,IAAI,CAAChF,cAAc,CAACuE,2BAA2B,EAAE,CAACoB,OAAO,EAAEV,SAAS;KAClF;IACD,IAAI,CAACC,cAAc,CAACN,IAAI,CAAC;EAC3B;EAEQsE,gBAAgBA,CAACC,GAAQ;IAC/B1D,OAAO,CAACC,GAAG,CAAC,IAAI,CAACnH,aAAa,CAAC;IAC/B4K,GAAG,CAACxB,OAAO,CAAC,CAACyB,KAAa,EAAEtE,WAAmB,KAAI;MACjD,IAAIuE,GAAG,GAAa,EAAE;MACtB,IAAIxE,OAAO,GAAa,EAAE;MAC1B,KAAK,IAAIqC,IAAI,IAAIkC,KAAK,EAAE;QACtBC,GAAG,CAACxG,IAAI,CAACqE,IAAI,CAACjC,SAAU,CAAC;MAC3B;MACAJ,OAAO,CAAChC,IAAI,CAAC,IAAI,CAACtE,aAAa,CAACwE,MAAO,CAAC;MACxC,IAAI6B,IAAI,GAAQ;QACdC,OAAO,EAAEA,OAAO;QAChBC,WAAW,EAAEA,WAAW;QACxBE,UAAU,EAAEqE;OACb;MACD,IAAI,CAACnE,cAAc,CAACN,IAAI,CAAC;IAC3B,CAAC,CAAC;EACJ;EAEQ0E,kBAAkBA,CAACH,GAAQ;IACjC1D,OAAO,CAACC,GAAG,CAAC,IAAI,CAACnH,aAAa,CAAC;IAC/B4K,GAAG,CAACxB,OAAO,CAAC,CAACyB,KAAa,EAAEtE,WAAmB,KAAI;MACjD,IAAIuE,GAAG,GAAa,EAAE;MACtB,IAAIxE,OAAO,GAAa,EAAE;MAC1B,KAAK,IAAIqC,IAAI,IAAIkC,KAAK,EAAE;QACtBC,GAAG,CAACxG,IAAI,CAACqE,IAAI,CAACjC,SAAU,CAAC;MAC3B;MACAJ,OAAO,CAAChC,IAAI,CAAC,IAAI,CAACtE,aAAa,CAACwE,MAAO,CAAC;MACxC,IAAI6B,IAAI,GAAQ;QACd7B,MAAM,EAAE,IAAI,CAACxE,aAAa,CAACwE,MAAO;QAClC+B,WAAW,EAAEA;OACd;MACD,IAAI,CAACc,gBAAgB,CAAChB,IAAI,CAAC;IAC7B,CAAC,CAAC;EACJ;EAEAX,gBAAgBA,CAACsF,GAAW;IAC1B,MAAMnB,OAAO,GAAG/K,WAAW,CAACgL,MAAM,GAAG,GAAG,GAAGkB,GAAG;IAC9CC,MAAM,CAACC,IAAI,CAACrB,OAAO,EAAE,QAAQ,CAAC;EAChC;EAEQlD,cAAcA,CAACwE,UAAe;IACpC,IAAI,CAACjI,IAAI,CAACS,GAAG,CAAC,IAAI,CAAClC,cAAc,CAAC2J,uBAAuB,CAACD,UAAU,CAAC,CAACtH,IAAI,CAAChF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC4E,SAAS,CAACC,GAAG,IAAG;MAElGwD,OAAO,CAACC,GAAG,CAAC,IAAI,CAACpF,SAAS,CAAC;MAC3B,IAAI,CAACH,YAAY,CAACqG,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;MACF,IAAI,IAAI,CAACvG,WAAW,CAACuE,SAAS,EAAE;QAC9B,IAAI,CAAC3E,cAAc,CAAC4J,6BAA6B,CAACC,QAAQ,CAACH,UAAU,CAAC5E,WAAW,CAAC,CAAC;MACrF;IACF,CAAC,CAAC,CAAC;EACL;EAEQc,gBAAgBA,CAAC8D,UAAe;IACtCjE,OAAO,CAACC,GAAG,CAAC,IAAI,CAACpF,SAAS,CAAC;IAC3B,IAAI,CAACmB,IAAI,CAACS,GAAG,CAAC,IAAI,CAAClC,cAAc,CAAC8J,cAAc,CAACJ,UAAU,CAAC,CAACtH,IAAI,CAAChF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC4E,SAAS,CAACC,GAAG,IAAG;MACzF,IAAI,CAAC9B,YAAY,CAACqG,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;MACF,IAAI,CAAC3F,YAAY,CAAC8F,IAAI,CAAC7E,GAAG,CAAC;MAC3B,IAAI,CAACjC,cAAc,CAAC4J,6BAA6B,CAACC,QAAQ,CAACH,UAAU,CAAC5E,WAAW,CAAC,CAAC;IACrF,CAAC,CAAC,CAAC;EACL;EAEQK,uBAAuBA,CAAC4E,SAAc,EAAEC,UAAgB,EAAEC,KAAK,GAAG,GAAG;IAE3E,IAAI,CAAChK,cAAc,CAACkF,uBAAuB,CAAC,IAAI,CAACjF,aAAa,EAAE6J,SAAS,EAAE,GAAG,EAAEC,UAAU,EAAE,IAAI,EAAGvG,MAAW,IAAI;MAChH,IAAIA,MAAM,IAAIA,MAAM,CAAC2B,MAAM,KAAK,WAAW,EAAE;QAC3C,IAAI,CAAC8D,gBAAgB,CAACzF,MAAM,CAAC0F,GAAG,CAACA,GAAG,CAAC;MACvC,CAAC,MAAM,IAAI1F,MAAM,IAAIA,MAAM,CAAC2B,MAAM,KAAK,aAAa,EAAE;QACpD,IAAI,CAACkE,kBAAkB,CAAC7F,MAAM,CAAC0F,GAAG,CAACA,GAAG,CAAC;MACzC,CAAC,MAAM,IAAI1F,MAAM,IAAIA,MAAM,CAAC2B,MAAM,KAAK,MAAM,EAAE;QAC7C,IAAI,CAACwC,QAAQ,CAACnE,MAAM,CAAC+B,WAAW,EAAE/B,MAAM,CAACyG,MAAM,CAAC;MAClD;IACF,CAAC,CAAC;IAEF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;EACF;EAEQ/F,YAAYA,CAACoF,GAAW;IAC9B,MAAMnB,OAAO,GAAG/K,WAAW,CAACgL,MAAM,GAAG,GAAG,GAAGkB,GAAG;IAC9C,IAAI,CAAC9H,IAAI,CAACS,GAAG,CAAC,IAAI,CAAC7B,IAAI,CAAC8J,GAAG,CAAC/B,OAAO,EAAE;MAAEgC,YAAY,EAAE;IAAM,CAAE,CAAC,CAC3DpI,SAAS,CAAEqI,IAAU,IAAI;MACxB,MAAMC,IAAI,GAAGtH,QAAQ,CAACuF,aAAa,CAAC,GAAG,CAAC;MACxC+B,IAAI,CAACC,IAAI,GAAGf,MAAM,CAACgB,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;MAC5CC,IAAI,CAACI,QAAQ,GAAG,IAAI,CAACrH,WAAW,CAAC+E,OAAO,CAAC;MACzCkC,IAAI,CAACK,KAAK,EAAE;IACd,CAAC,CAAC,CAAC;EACP;EAAC,QAAAC,CAAA,G;qBA/fU9K,2BAA2B,EAAAtC,EAAA,CAAAqN,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAvN,EAAA,CAAAqN,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAzN,EAAA,CAAAqN,iBAAA,CAAAK,EAAA,CAAA/N,aAAA,GAAAK,EAAA,CAAAqN,iBAAA,CAAAM,EAAA,CAAAC,YAAA,GAAA5N,EAAA,CAAAqN,iBAAA,CAAAQ,EAAA,CAAAC,WAAA,GAAA9N,EAAA,CAAAqN,iBAAA,CAAAU,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA3B3L,2BAA2B;IAAA4L,SAAA;IAAAC,MAAA;MAAArL,SAAA;MAAA/B,aAAA;MAAAsH,OAAA;MAAAtF,qBAAA;MAAAC,gBAAA;MAAAC,iBAAA;MAAAC,gBAAA;MAAAC,aAAA;MAAAC,mBAAA;MAAAC,OAAA;MAAA6D,iBAAA;IAAA;IAAAkH,OAAA;MAAA9K,WAAA;MAAAC,cAAA;MAAAC,YAAA;IAAA;IAAA6K,QAAA,GAAArO,EAAA,CAAAsO,kBAAA,CAF3B,CAAC3O,aAAa,CAAC;IAAA4O,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QCpB5B5O,EAAA,CAAAC,cAAA,YAA2G;QACvGD,EAAA,CAAAO,MAAA,GACA;QAAAP,EAAA,CAAAC,cAAA,aAEsE;QAClED,EAAA,CAAAE,SAAA,aAAoD;QACxDF,EAAA,CAAAG,YAAA,EAAM;QAGEH,EAFR,CAAAC,cAAA,aAAgF,aACZ,cAC6D;QAAnHD,EAAA,CAAA2B,UAAA,mBAAAmN,2DAAA;UAAA9O,EAAA,CAAA6B,aAAA,CAAAkN,GAAA;UAAA,OAAA/O,EAAA,CAAAgC,WAAA,CAAS6M,GAAA,CAAApI,gBAAA,CAAAoI,GAAA,CAAA9N,aAAA,CAAA+E,IAAA,CAAoC;QAAA,EAAC;QAAqE9F,EAAA,CAAAO,MAAA,GAAmC;QAAAP,EAAA,CAAAG,YAAA,EAAO;QACnKH,EAAA,CAAAC,cAAA,aAAwB;QAIpBD,EAHA,CAAAoB,UAAA,IAAA4N,0CAAA,iBAAgD,KAAAC,2CAAA,kBAGsD;QACtGjP,EAAA,CAAAC,cAAA,gBAAiD;QAC7CD,EAAA,CAAAO,MAAA,IACJ;QAERP,EAFQ,CAAAG,YAAA,EAAO,EACL,EACJ;QAEFH,EADJ,CAAAC,cAAA,eAA+B,eAC8E;QACrGD,EAAA,CAAAoB,UAAA,KAAA8N,8CAAA,qBACyE;;QAO/DlP,EAAA,CAAAoB,UAAA,KAAA+N,2CAAA,kBAAiD;QAkBvEnP,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;QACNH,EAAA,CAAAC,cAAA,eAAiJ;QAA1DD,EAAA,CAAA2B,UAAA,mBAAAyN,2DAAAC,MAAA;UAAArP,EAAA,CAAA6B,aAAA,CAAAkN,GAAA;UAAA,MAAAO,QAAA,GAAAtP,EAAA,CAAAuP,WAAA;UAAA,OAAAvP,EAAA,CAAAgC,WAAA,EAAA6M,GAAA,CAAA7L,gBAAA,GAA6BsM,QAAA,CAAAE,MAAA,CAAAH,MAAA,CAAoB,GAAG,IAAI;QAAA,EAAC;QAQ5IrP,EANA,CAAAoB,UAAA,KAAAqO,2CAAA,kBACgC,KAAAC,2CAAA,kBAKkK;QAI1M1P,EAFI,CAAAG,YAAA,EAAM,EAEL;QACLH,EAAA,CAAAE,SAAA,qBAA0E;;;QA9D7CF,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAA2P,eAAA,KAAAC,GAAA,EAAAf,GAAA,CAAAxM,OAAA,IAAAwM,GAAA,CAAA7L,gBAAA,EAAA6L,GAAA,CAAAxL,OAAA,EAA6E;QACtGrD,EAAA,CAAAI,SAAA,EACA;QADAJ,EAAA,CAAAkB,kBAAA,MAAA2N,GAAA,CAAA/L,SAAA,kBAAA+L,GAAA,CAAA/L,SAAA,CAAAyE,EAAA,MACA;QAEIvH,EAAA,CAAAI,SAAA,EAAiE;QAAjEJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAA2P,eAAA,KAAAE,GAAA,EAAAhB,GAAA,CAAAxL,OAAA,GAAAwL,GAAA,CAAAxL,OAAA,EAAiE;QAK4DrD,EAAA,CAAAI,SAAA,GAAmC;QAAnCJ,EAAA,CAAAW,iBAAA,CAAAkO,GAAA,CAAAhJ,WAAA,CAAAgJ,GAAA,CAAA9N,aAAA,CAAA+G,IAAA,EAAmC;QAEjI9H,EAAA,CAAAI,SAAA,GAAuB;QAAvBJ,EAAA,CAAAK,UAAA,YAAAwO,GAAA,CAAA9N,aAAA,CAAA+O,MAAA,CAAuB;QAGM9P,EAAA,CAAAI,SAAA,EAAqC;QAArCJ,EAAA,CAAAK,UAAA,SAAAwO,GAAA,CAAA3K,eAAA,CAAA/C,MAAA,KAAqC;QAErFnB,EAAA,CAAAI,SAAA,GACJ;QADIJ,EAAA,CAAAkB,kBAAA,MAAA2N,GAAA,CAAArF,oBAAA,QACJ;QAK6BxJ,EAAA,CAAAI,SAAA,GAAgE;QAAhEJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAA+P,WAAA,SAAAlB,GAAA,CAAA9N,aAAA,CAAAC,UAAA,KAAA6N,GAAA,CAAAzK,0BAAA,EAAgE;QAQ7EpE,EAAA,CAAAI,SAAA,GAAyC;QAAzCJ,EAAA,CAAAK,UAAA,SAAAwO,GAAA,CAAA9N,aAAA,CAAAC,UAAA,CAAAG,MAAA,KAAyC;QAmB5BnB,EAAA,CAAAI,SAAA,EAA6C;QAA7CJ,EAAA,CAAAgQ,sBAAA,2BAAAnB,GAAA,CAAA9N,aAAA,CAAAwE,MAAA,KAA6C;QAE5EvF,EAAA,CAAAI,SAAA,EAAoE;QAApEJ,EAAA,CAAAK,UAAA,SAAAwO,GAAA,CAAA7L,gBAAA,IAAA6L,GAAA,CAAArM,cAAA,CAAAyN,oBAAA,GAAoE;QAMpEjQ,EAAA,CAAAI,SAAA,EAAsE;QAAtEJ,EAAA,CAAAK,UAAA,UAAAwO,GAAA,CAAA7L,gBAAA,KAAA6L,GAAA,CAAArM,cAAA,CAAAyN,oBAAA,GAAsE;QAKvDjQ,EAAA,CAAAI,SAAA,EAAmB;QAAnBJ,EAAA,CAAAK,UAAA,UAAAwO,GAAA,CAAA1K,SAAA,CAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}