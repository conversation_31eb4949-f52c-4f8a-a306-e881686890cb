{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport { catchError, retry, take } from 'rxjs/operators';\nimport { PaymentStep, PaymentEventType, PAYMENT_CONSTANTS } from '../models/payment.model';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./package.service\";\nimport * as i2 from \"./toast.service\";\nexport class PaymentService {\n  constructor(packageService, toastService) {\n    this.packageService = packageService;\n    this.toastService = toastService;\n    this.paymentState$ = new BehaviorSubject({\n      isLoading: false,\n      isProcessing: false,\n      error: null,\n      currentStep: PaymentStep.FORM_VALIDATION,\n      orderResponse: null\n    });\n    this.paymentState = this.paymentState$.asObservable();\n  }\n  /**\n   * Initialize payment process with comprehensive error handling\n   */\n  initializePayment(paymentData, isSplitPayment = false) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.updatePaymentState({\n          isLoading: true,\n          currentStep: PaymentStep.FORM_VALIDATION,\n          error: null\n        });\n        // Validate payment data\n        const validationResult = _this.validatePaymentData(paymentData);\n        if (!validationResult.isValid) {\n          const error = _this.createPaymentError('VALIDATION_ERROR', 'Payment data validation failed', _this.formatValidationErrors(validationResult.errors));\n          _this.handlePaymentError(error);\n          return {\n            success: false,\n            error\n          };\n        }\n        // Create order\n        _this.updatePaymentState({\n          currentStep: PaymentStep.ORDER_CREATION\n        });\n        const orderResponse = yield _this.createRevolutOrder(paymentData, isSplitPayment);\n        _this.updatePaymentState({\n          orderResponse,\n          currentStep: PaymentStep.PAYMENT_PROCESSING\n        });\n        // Initialize Revolut Pay\n        yield _this.initializeRevolutPay(orderResponse, paymentData);\n        _this.trackPaymentEvent(PaymentEventType.PAYMENT_INITIATED, paymentData);\n        return {\n          success: true,\n          orderId: orderResponse.id\n        };\n      } catch (error) {\n        const paymentError = _this.handleUnexpectedError(error);\n        _this.handlePaymentError(paymentError);\n        return {\n          success: false,\n          error: paymentError\n        };\n      }\n    })();\n  }\n  /**\n   * Initialize Revolut Pay with proper error handling\n   */\n  initializeRevolutPay(orderResponse, paymentData) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      return new Promise((resolve, reject) => {\n        const container = _this2.getPaymentContainer();\n        if (!container) {\n          reject(new Error('Payment container not found'));\n          return;\n        }\n        _this2.clearPaymentContainer(container);\n        RevolutCheckout(orderResponse.public_id, environment.production ? 'prod' : 'sandbox').then(instance => {\n          const config = {\n            target: container,\n            buttonStyle: {\n              variant: 'dark',\n              size: 'large',\n              radius: 'medium'\n            },\n            onSuccess: () => _this2.handlePaymentSuccess(orderResponse, paymentData),\n            onError: error => _this2.handleRevolutError(error),\n            onCancel: () => _this2.handlePaymentCancel()\n          };\n          instance.revolutPay(config);\n          resolve();\n        }).catch(error => {\n          reject(_this2.createPaymentError('REVOLUT_INIT_ERROR', 'Failed to initialize Revolut Pay', 'Could not initialize payment. Please try again.'));\n        });\n      });\n    })();\n  }\n  /**\n   * Handle successful payment\n   */\n  handlePaymentSuccess(orderResponse, paymentData) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this3.updatePaymentState({\n          currentStep: PaymentStep.PAYMENT_COMPLETION,\n          isProcessing: true\n        });\n        const completionRequest = {\n          ...paymentData,\n          token: orderResponse.public_id,\n          merchant_order_ext_ref: orderResponse.merchant_order_ext_ref\n        };\n        yield _this3.completePayment(completionRequest);\n        _this3.updatePaymentState({\n          currentStep: PaymentStep.SUCCESS,\n          isLoading: false,\n          isProcessing: false\n        });\n        _this3.trackPaymentEvent(PaymentEventType.PAYMENT_SUCCESS, paymentData);\n        _this3.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: 'Payment Successful',\n          detail: 'Your payment has been processed successfully!'\n        });\n      } catch (error) {\n        const paymentError = _this3.handleUnexpectedError(error);\n        _this3.handlePaymentError(paymentError);\n      }\n    })();\n  }\n  /**\n   * Handle Revolut-specific errors\n   */\n  handleRevolutError(error) {\n    const paymentError = this.createPaymentError(error.code || 'REVOLUT_ERROR', error.message || 'Payment failed', this.getRevolutErrorMessage(error));\n    this.handlePaymentError(paymentError);\n    this.trackPaymentEvent(PaymentEventType.PAYMENT_FAILED, null, {\n      error\n    });\n  }\n  /**\n   * Handle payment cancellation\n   */\n  handlePaymentCancel() {\n    this.updatePaymentState({\n      isLoading: false,\n      isProcessing: false,\n      currentStep: PaymentStep.FORM_VALIDATION\n    });\n    this.toastService.setShowToastmessage({\n      severity: 'info',\n      summary: 'Payment Cancelled',\n      detail: 'You have cancelled the payment process.'\n    });\n    this.trackPaymentEvent(PaymentEventType.PAYMENT_CANCELLED, null);\n  }\n  /**\n   * Create Revolut order with retry logic\n   */\n  createRevolutOrder(paymentData, isSplitPayment) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      const createOrder$ = isSplitPayment ? _this4.packageService.createSplitRevolutOrder(paymentData) : _this4.packageService.createRevolutOrder(paymentData);\n      return createOrder$.pipe(retry(PAYMENT_CONSTANTS.RETRY_ATTEMPTS), catchError(error => {\n        throw _this4.createPaymentError('ORDER_CREATION_FAILED', 'Failed to create payment order', 'Unable to create payment order. Please try again.');\n      }), take(1)).toPromise();\n    })();\n  }\n  /**\n   * Complete payment order\n   */\n  completePayment(request) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      const completeOrder$ = request.classroomId ? _this5.packageService.completeSplitOrder(request) : _this5.packageService.completeOrder(request);\n      return completeOrder$.pipe(retry(PAYMENT_CONSTANTS.RETRY_ATTEMPTS), catchError(error => {\n        throw _this5.createPaymentError('PAYMENT_COMPLETION_FAILED', 'Failed to complete payment', 'Payment processing failed. Please contact support.');\n      }), take(1)).toPromise();\n    })();\n  }\n  /**\n   * Validate payment data with comprehensive security checks\n   */\n  validatePaymentData(data) {\n    const errors = [];\n    // Required field validations\n    if (!data.language?.trim()) {\n      errors.push({\n        field: 'language',\n        message: 'Language is required',\n        code: 'REQUIRED'\n      });\n    }\n    if (!data.level?.trim()) {\n      errors.push({\n        field: 'level',\n        message: 'Level is required',\n        code: 'REQUIRED'\n      });\n    }\n    if (!data.studentsNumber?.trim()) {\n      errors.push({\n        field: 'studentsNumber',\n        message: 'Number of students is required',\n        code: 'REQUIRED'\n      });\n    }\n    if (!data.packageType?.trim()) {\n      errors.push({\n        field: 'packageType',\n        message: 'Package type is required',\n        code: 'REQUIRED'\n      });\n    }\n    // Hours validation with security checks\n    if (!data.hours || !this.isValidHours(data.hours)) {\n      errors.push({\n        field: 'hours',\n        message: 'Valid hours amount is required (1-1000)',\n        code: 'INVALID'\n      });\n    }\n    // Classroom ID validation\n    if (data.classroomId < 0) {\n      errors.push({\n        field: 'classroomId',\n        message: 'Invalid classroom ID',\n        code: 'INVALID'\n      });\n    }\n    // Personal details validation\n    if (!data.details) {\n      errors.push({\n        field: 'details',\n        message: 'Personal details are required',\n        code: 'REQUIRED'\n      });\n    } else {\n      this.validatePersonalDetails(data.details, errors);\n    }\n    // Security validations\n    this.performSecurityValidations(data, errors);\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n  /**\n   * Validate personal details\n   */\n  validatePersonalDetails(details, errors) {\n    if (!details.email || !this.isValidEmail(details.email)) {\n      errors.push({\n        field: 'email',\n        message: 'Valid email is required',\n        code: 'INVALID'\n      });\n    }\n    if (!details.firstname?.trim() || details.firstname.length > 50) {\n      errors.push({\n        field: 'firstname',\n        message: 'First name is required (max 50 characters)',\n        code: 'INVALID'\n      });\n    }\n    if (!details.lastname?.trim() || details.lastname.length > 50) {\n      errors.push({\n        field: 'lastname',\n        message: 'Last name is required (max 50 characters)',\n        code: 'INVALID'\n      });\n    }\n    if (!details.phone?.trim() || !this.isValidPhone(details.phone)) {\n      errors.push({\n        field: 'phone',\n        message: 'Valid phone number is required',\n        code: 'INVALID'\n      });\n    }\n    if (!details.country?.trim()) {\n      errors.push({\n        field: 'country',\n        message: 'Country is required',\n        code: 'REQUIRED'\n      });\n    }\n    if (!details.city?.trim() || details.city.length > 100) {\n      errors.push({\n        field: 'city',\n        message: 'City is required (max 100 characters)',\n        code: 'INVALID'\n      });\n    }\n    if (!details.postcode?.trim() || !this.isValidPostcode(details.postcode)) {\n      errors.push({\n        field: 'postcode',\n        message: 'Valid postcode is required',\n        code: 'INVALID'\n      });\n    }\n    // Optional fields validation\n    if (details.company && details.company.length > 100) {\n      errors.push({\n        field: 'company',\n        message: 'Company name too long (max 100 characters)',\n        code: 'INVALID'\n      });\n    }\n    if (details.vat && !this.isValidVAT(details.vat)) {\n      errors.push({\n        field: 'vat',\n        message: 'Invalid VAT number format',\n        code: 'INVALID'\n      });\n    }\n  }\n  /**\n   * Perform security validations\n   */\n  performSecurityValidations(data, errors) {\n    // Check for potential XSS attempts\n    const suspiciousFields = ['language', 'level', 'packageType'];\n    for (const field of suspiciousFields) {\n      if (this.containsSuspiciousContent(data[field])) {\n        errors.push({\n          field,\n          message: 'Invalid characters detected',\n          code: 'SECURITY_VIOLATION'\n        });\n      }\n    }\n    // Validate amount ranges (prevent extremely high values)\n    const hours = parseFloat(data.hours);\n    if (hours > 1000) {\n      errors.push({\n        field: 'hours',\n        message: 'Hours amount exceeds maximum allowed',\n        code: 'SECURITY_VIOLATION'\n      });\n    }\n    // Check for SQL injection patterns in text fields\n    if (data.details) {\n      const textFields = ['firstname', 'lastname', 'company', 'address', 'city'];\n      for (const field of textFields) {\n        const value = data.details[field];\n        if (typeof value === 'string' && this.containsSQLInjectionPattern(value)) {\n          errors.push({\n            field,\n            message: 'Invalid characters detected',\n            code: 'SECURITY_VIOLATION'\n          });\n        }\n      }\n    }\n  }\n  /**\n   * Utility methods\n   */\n  getPaymentContainer() {\n    return document.getElementById(PAYMENT_CONSTANTS.BUTTON_CONTAINER_ID);\n  }\n  clearPaymentContainer(container) {\n    while (container.firstChild) {\n      container.removeChild(container.firstChild);\n    }\n  }\n  updatePaymentState(updates) {\n    const currentState = this.paymentState$.value;\n    this.paymentState$.next({\n      ...currentState,\n      ...updates\n    });\n  }\n  createPaymentError(code, message, userMessage) {\n    return {\n      code,\n      message,\n      userMessage,\n      retryable: this.isRetryableError(code),\n      timestamp: new Date()\n    };\n  }\n  handlePaymentError(error) {\n    this.updatePaymentState({\n      error,\n      isLoading: false,\n      isProcessing: false,\n      currentStep: PaymentStep.ERROR\n    });\n    this.toastService.setShowToastmessage({\n      severity: 'error',\n      summary: 'Payment Error',\n      detail: error.userMessage\n    });\n  }\n  handleUnexpectedError(error) {\n    console.error('Unexpected payment error:', error);\n    return this.createPaymentError('UNEXPECTED_ERROR', error?.message || 'An unexpected error occurred', 'An unexpected error occurred. Please try again.');\n  }\n  getRevolutErrorMessage(error) {\n    const errorMessages = {\n      'PAYMENT_DECLINED': 'Your payment was declined. Please try a different payment method.',\n      'INSUFFICIENT_FUNDS': 'Insufficient funds. Please check your account balance.',\n      'INVALID_CARD': 'Invalid card details. Please check your information.',\n      'EXPIRED_CARD': 'Your card has expired. Please use a different card.',\n      'AUTHENTICATION_FAILED': 'Payment authentication failed. Please try again.',\n      'NETWORK_ERROR': 'Network error. Please check your connection and try again.'\n    };\n    return errorMessages[error.code] || 'Payment failed. Please try again.';\n  }\n  formatValidationErrors(errors) {\n    return errors.map(error => error.message).join(', ');\n  }\n  isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  }\n  isRetryableError(code) {\n    const retryableCodes = ['NETWORK_ERROR', 'TIMEOUT', 'SERVER_ERROR'];\n    return retryableCodes.includes(code);\n  }\n  trackPaymentEvent(eventType, paymentData, metadata) {\n    // Implementation for payment analytics tracking\n    console.log('Payment event:', {\n      eventType,\n      paymentData,\n      metadata\n    });\n  }\n  /**\n   * Public methods for component interaction\n   */\n  resetPaymentState() {\n    this.paymentState$.next({\n      isLoading: false,\n      isProcessing: false,\n      error: null,\n      currentStep: PaymentStep.FORM_VALIDATION,\n      orderResponse: null\n    });\n  }\n  retryPayment() {\n    const currentState = this.paymentState$.value;\n    if (currentState.error?.retryable && currentState.orderResponse) {\n      this.updatePaymentState({\n        error: null,\n        isLoading: true\n      });\n      // Retry logic would go here\n    }\n  }\n  static #_ = this.ɵfac = function PaymentService_Factory(t) {\n    return new (t || PaymentService)(i0.ɵɵinject(i1.PackageService), i0.ɵɵinject(i2.ToastService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: PaymentService,\n    factory: PaymentService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "catchError", "retry", "take", "PaymentStep", "PaymentEventType", "PAYMENT_CONSTANTS", "environment", "PaymentService", "constructor", "packageService", "toastService", "paymentState$", "isLoading", "isProcessing", "error", "currentStep", "FORM_VALIDATION", "orderResponse", "paymentState", "asObservable", "initializePayment", "paymentData", "isSplitPayment", "_this", "_asyncToGenerator", "updatePaymentState", "validationResult", "validatePaymentData", "<PERSON><PERSON><PERSON><PERSON>", "createPaymentError", "formatValidationErrors", "errors", "handlePaymentError", "success", "ORDER_CREATION", "createRevolutOrder", "PAYMENT_PROCESSING", "initializeRevolutPay", "trackPaymentEvent", "PAYMENT_INITIATED", "orderId", "id", "paymentError", "handleUnexpectedError", "_this2", "Promise", "resolve", "reject", "container", "getPaymentContainer", "Error", "clearPaymentContainer", "RevolutCheckout", "public_id", "production", "then", "instance", "config", "target", "buttonStyle", "variant", "size", "radius", "onSuccess", "handlePaymentSuccess", "onError", "handleRevolutError", "onCancel", "handlePaymentCancel", "revolutPay", "catch", "_this3", "PAYMENT_COMPLETION", "completionRequest", "token", "merchant_order_ext_ref", "completePayment", "SUCCESS", "PAYMENT_SUCCESS", "setShowToastmessage", "severity", "summary", "detail", "code", "message", "getRevolutErrorMessage", "PAYMENT_FAILED", "PAYMENT_CANCELLED", "_this4", "createOrder$", "createSplitRevolutOrder", "pipe", "RETRY_ATTEMPTS", "to<PERSON>romise", "request", "_this5", "completeOrder$", "classroomId", "completeSplitOrder", "completeOrder", "data", "language", "trim", "push", "field", "level", "studentsNumber", "packageType", "hours", "isValidHours", "details", "validatePersonalDetails", "performSecurityValidations", "length", "email", "isValidEmail", "firstname", "lastname", "phone", "isValidPhone", "country", "city", "postcode", "isValidPostcode", "company", "vat", "isValidVAT", "suspiciousFields", "contains<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>nt", "parseFloat", "textFields", "value", "containsSQLInjectionPattern", "document", "getElementById", "BUTTON_CONTAINER_ID", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "updates", "currentState", "next", "userMessage", "retryable", "isRetryableError", "timestamp", "Date", "ERROR", "console", "errorMessages", "map", "join", "emailRegex", "test", "retryableCodes", "includes", "eventType", "metadata", "log", "resetPaymentState", "retryPayment", "_", "i0", "ɵɵinject", "i1", "PackageService", "i2", "ToastService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\services\\payment.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable, throwError, timer } from 'rxjs';\nimport { catchError, retry, retryWhen, switchMap, take, tap } from 'rxjs/operators';\nimport { \n  PaymentState, \n  PaymentStep, \n  PaymentError, \n  RevolutOrderRequest, \n  RevolutOrderResponse, \n  PaymentCompletionRequest,\n  RevolutCheckoutInstance,\n  RevolutPayConfig,\n  RevolutPaymentError,\n  PaymentValidationResult,\n  PaymentValidationError,\n  PaymentMethod,\n  PaymentAnalyticsEvent,\n  PaymentEventType,\n  PAYMENT_CONSTANTS,\n  PaymentResult\n} from '../models/payment.model';\nimport { PackageService } from './package.service';\nimport { ToastService } from './toast.service';\nimport { environment } from 'src/environments/environment';\n\ndeclare const RevolutCheckout: any;\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class PaymentService {\n  private paymentState$ = new BehaviorSubject<PaymentState>({\n    isLoading: false,\n    isProcessing: false,\n    error: null,\n    currentStep: PaymentStep.FORM_VALIDATION,\n    orderResponse: null\n  });\n\n  public readonly paymentState: Observable<PaymentState> = this.paymentState$.asObservable();\n\n  constructor(\n    private packageService: PackageService,\n    private toastService: ToastService\n  ) {}\n\n  /**\n   * Initialize payment process with comprehensive error handling\n   */\n  async initializePayment(\n    paymentData: RevolutOrderRequest, \n    isSplitPayment: boolean = false\n  ): Promise<PaymentResult> {\n    try {\n      this.updatePaymentState({ \n        isLoading: true, \n        currentStep: PaymentStep.FORM_VALIDATION,\n        error: null \n      });\n\n      // Validate payment data\n      const validationResult = this.validatePaymentData(paymentData);\n      if (!validationResult.isValid) {\n        const error = this.createPaymentError(\n          'VALIDATION_ERROR',\n          'Payment data validation failed',\n          this.formatValidationErrors(validationResult.errors)\n        );\n        this.handlePaymentError(error);\n        return { success: false, error };\n      }\n\n      // Create order\n      this.updatePaymentState({ currentStep: PaymentStep.ORDER_CREATION });\n      const orderResponse = await this.createRevolutOrder(paymentData, isSplitPayment);\n      \n      this.updatePaymentState({ \n        orderResponse,\n        currentStep: PaymentStep.PAYMENT_PROCESSING \n      });\n\n      // Initialize Revolut Pay\n      await this.initializeRevolutPay(orderResponse, paymentData);\n\n      this.trackPaymentEvent(PaymentEventType.PAYMENT_INITIATED, paymentData);\n\n      return { success: true, orderId: orderResponse.id };\n\n    } catch (error) {\n      const paymentError = this.handleUnexpectedError(error);\n      this.handlePaymentError(paymentError);\n      return { success: false, error: paymentError };\n    }\n  }\n\n  /**\n   * Initialize Revolut Pay with proper error handling\n   */\n  private async initializeRevolutPay(\n    orderResponse: RevolutOrderResponse,\n    paymentData: RevolutOrderRequest\n  ): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const container = this.getPaymentContainer();\n      if (!container) {\n        reject(new Error('Payment container not found'));\n        return;\n      }\n\n      this.clearPaymentContainer(container);\n\n      RevolutCheckout(orderResponse.public_id, environment.production ? 'prod' : 'sandbox')\n        .then((instance: RevolutCheckoutInstance) => {\n          const config: RevolutPayConfig = {\n            target: container,\n            buttonStyle: {\n              variant: 'dark',\n              size: 'large',\n              radius: 'medium'\n            },\n            onSuccess: () => this.handlePaymentSuccess(orderResponse, paymentData),\n            onError: (error: RevolutPaymentError) => this.handleRevolutError(error),\n            onCancel: () => this.handlePaymentCancel()\n          };\n\n          instance.revolutPay(config);\n          resolve();\n        })\n        .catch((error: any) => {\n          reject(this.createPaymentError(\n            'REVOLUT_INIT_ERROR',\n            'Failed to initialize Revolut Pay',\n            'Could not initialize payment. Please try again.'\n          ));\n        });\n    });\n  }\n\n  /**\n   * Handle successful payment\n   */\n  private async handlePaymentSuccess(\n    orderResponse: RevolutOrderResponse,\n    paymentData: RevolutOrderRequest\n  ): Promise<void> {\n    try {\n      this.updatePaymentState({ \n        currentStep: PaymentStep.PAYMENT_COMPLETION,\n        isProcessing: true \n      });\n\n      const completionRequest: PaymentCompletionRequest = {\n        ...paymentData,\n        token: orderResponse.public_id,\n        merchant_order_ext_ref: orderResponse.merchant_order_ext_ref\n      };\n\n      await this.completePayment(completionRequest);\n\n      this.updatePaymentState({ \n        currentStep: PaymentStep.SUCCESS,\n        isLoading: false,\n        isProcessing: false \n      });\n\n      this.trackPaymentEvent(PaymentEventType.PAYMENT_SUCCESS, paymentData);\n      \n      this.toastService.setShowToastmessage({\n        severity: 'success',\n        summary: 'Payment Successful',\n        detail: 'Your payment has been processed successfully!'\n      });\n\n    } catch (error) {\n      const paymentError = this.handleUnexpectedError(error);\n      this.handlePaymentError(paymentError);\n    }\n  }\n\n  /**\n   * Handle Revolut-specific errors\n   */\n  private handleRevolutError(error: RevolutPaymentError): void {\n    const paymentError = this.createPaymentError(\n      error.code || 'REVOLUT_ERROR',\n      error.message || 'Payment failed',\n      this.getRevolutErrorMessage(error)\n    );\n\n    this.handlePaymentError(paymentError);\n    this.trackPaymentEvent(PaymentEventType.PAYMENT_FAILED, null, { error });\n  }\n\n  /**\n   * Handle payment cancellation\n   */\n  private handlePaymentCancel(): void {\n    this.updatePaymentState({ \n      isLoading: false,\n      isProcessing: false,\n      currentStep: PaymentStep.FORM_VALIDATION \n    });\n\n    this.toastService.setShowToastmessage({\n      severity: 'info',\n      summary: 'Payment Cancelled',\n      detail: 'You have cancelled the payment process.'\n    });\n\n    this.trackPaymentEvent(PaymentEventType.PAYMENT_CANCELLED, null);\n  }\n\n  /**\n   * Create Revolut order with retry logic\n   */\n  private async createRevolutOrder(\n    paymentData: RevolutOrderRequest,\n    isSplitPayment: boolean\n  ): Promise<RevolutOrderResponse> {\n    const createOrder$ = isSplitPayment \n      ? this.packageService.createSplitRevolutOrder(paymentData)\n      : this.packageService.createRevolutOrder(paymentData);\n\n    return createOrder$.pipe(\n      retry(PAYMENT_CONSTANTS.RETRY_ATTEMPTS),\n      catchError(error => {\n        throw this.createPaymentError(\n          'ORDER_CREATION_FAILED',\n          'Failed to create payment order',\n          'Unable to create payment order. Please try again.'\n        );\n      }),\n      take(1)\n    ).toPromise() as Promise<RevolutOrderResponse>;\n  }\n\n  /**\n   * Complete payment order\n   */\n  private async completePayment(request: PaymentCompletionRequest): Promise<any> {\n    const completeOrder$ = request.classroomId \n      ? this.packageService.completeSplitOrder(request)\n      : this.packageService.completeOrder(request);\n\n    return completeOrder$.pipe(\n      retry(PAYMENT_CONSTANTS.RETRY_ATTEMPTS),\n      catchError(error => {\n        throw this.createPaymentError(\n          'PAYMENT_COMPLETION_FAILED',\n          'Failed to complete payment',\n          'Payment processing failed. Please contact support.'\n        );\n      }),\n      take(1)\n    ).toPromise();\n  }\n\n  /**\n   * Validate payment data with comprehensive security checks\n   */\n  private validatePaymentData(data: RevolutOrderRequest): PaymentValidationResult {\n    const errors: PaymentValidationError[] = [];\n\n    // Required field validations\n    if (!data.language?.trim()) {\n      errors.push({ field: 'language', message: 'Language is required', code: 'REQUIRED' });\n    }\n\n    if (!data.level?.trim()) {\n      errors.push({ field: 'level', message: 'Level is required', code: 'REQUIRED' });\n    }\n\n    if (!data.studentsNumber?.trim()) {\n      errors.push({ field: 'studentsNumber', message: 'Number of students is required', code: 'REQUIRED' });\n    }\n\n    if (!data.packageType?.trim()) {\n      errors.push({ field: 'packageType', message: 'Package type is required', code: 'REQUIRED' });\n    }\n\n    // Hours validation with security checks\n    if (!data.hours || !this.isValidHours(data.hours)) {\n      errors.push({ field: 'hours', message: 'Valid hours amount is required (1-1000)', code: 'INVALID' });\n    }\n\n    // Classroom ID validation\n    if (data.classroomId < 0) {\n      errors.push({ field: 'classroomId', message: 'Invalid classroom ID', code: 'INVALID' });\n    }\n\n    // Personal details validation\n    if (!data.details) {\n      errors.push({ field: 'details', message: 'Personal details are required', code: 'REQUIRED' });\n    } else {\n      this.validatePersonalDetails(data.details, errors);\n    }\n\n    // Security validations\n    this.performSecurityValidations(data, errors);\n\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n\n  /**\n   * Validate personal details\n   */\n  private validatePersonalDetails(details: any, errors: PaymentValidationError[]): void {\n    if (!details.email || !this.isValidEmail(details.email)) {\n      errors.push({ field: 'email', message: 'Valid email is required', code: 'INVALID' });\n    }\n\n    if (!details.firstname?.trim() || details.firstname.length > 50) {\n      errors.push({ field: 'firstname', message: 'First name is required (max 50 characters)', code: 'INVALID' });\n    }\n\n    if (!details.lastname?.trim() || details.lastname.length > 50) {\n      errors.push({ field: 'lastname', message: 'Last name is required (max 50 characters)', code: 'INVALID' });\n    }\n\n    if (!details.phone?.trim() || !this.isValidPhone(details.phone)) {\n      errors.push({ field: 'phone', message: 'Valid phone number is required', code: 'INVALID' });\n    }\n\n    if (!details.country?.trim()) {\n      errors.push({ field: 'country', message: 'Country is required', code: 'REQUIRED' });\n    }\n\n    if (!details.city?.trim() || details.city.length > 100) {\n      errors.push({ field: 'city', message: 'City is required (max 100 characters)', code: 'INVALID' });\n    }\n\n    if (!details.postcode?.trim() || !this.isValidPostcode(details.postcode)) {\n      errors.push({ field: 'postcode', message: 'Valid postcode is required', code: 'INVALID' });\n    }\n\n    // Optional fields validation\n    if (details.company && details.company.length > 100) {\n      errors.push({ field: 'company', message: 'Company name too long (max 100 characters)', code: 'INVALID' });\n    }\n\n    if (details.vat && !this.isValidVAT(details.vat)) {\n      errors.push({ field: 'vat', message: 'Invalid VAT number format', code: 'INVALID' });\n    }\n  }\n\n  /**\n   * Perform security validations\n   */\n  private performSecurityValidations(data: RevolutOrderRequest, errors: PaymentValidationError[]): void {\n    // Check for potential XSS attempts\n    const suspiciousFields = ['language', 'level', 'packageType'];\n    for (const field of suspiciousFields) {\n      if (this.containsSuspiciousContent(data[field as keyof RevolutOrderRequest] as string)) {\n        errors.push({ field, message: 'Invalid characters detected', code: 'SECURITY_VIOLATION' });\n      }\n    }\n\n    // Validate amount ranges (prevent extremely high values)\n    const hours = parseFloat(data.hours);\n    if (hours > 1000) {\n      errors.push({ field: 'hours', message: 'Hours amount exceeds maximum allowed', code: 'SECURITY_VIOLATION' });\n    }\n\n    // Check for SQL injection patterns in text fields\n    if (data.details) {\n      const textFields = ['firstname', 'lastname', 'company', 'address', 'city'];\n      for (const field of textFields) {\n        const value = data.details[field as keyof typeof data.details];\n        if (typeof value === 'string' && this.containsSQLInjectionPattern(value)) {\n          errors.push({ field, message: 'Invalid characters detected', code: 'SECURITY_VIOLATION' });\n        }\n      }\n    }\n  }\n\n  /**\n   * Utility methods\n   */\n  private getPaymentContainer(): HTMLElement | null {\n    return document.getElementById(PAYMENT_CONSTANTS.BUTTON_CONTAINER_ID);\n  }\n\n  private clearPaymentContainer(container: HTMLElement): void {\n    while (container.firstChild) {\n      container.removeChild(container.firstChild);\n    }\n  }\n\n  private updatePaymentState(updates: Partial<PaymentState>): void {\n    const currentState = this.paymentState$.value;\n    this.paymentState$.next({ ...currentState, ...updates });\n  }\n\n  private createPaymentError(code: string, message: string, userMessage: string): PaymentError {\n    return {\n      code,\n      message,\n      userMessage,\n      retryable: this.isRetryableError(code),\n      timestamp: new Date()\n    };\n  }\n\n  private handlePaymentError(error: PaymentError): void {\n    this.updatePaymentState({ \n      error,\n      isLoading: false,\n      isProcessing: false,\n      currentStep: PaymentStep.ERROR \n    });\n\n    this.toastService.setShowToastmessage({\n      severity: 'error',\n      summary: 'Payment Error',\n      detail: error.userMessage\n    });\n  }\n\n  private handleUnexpectedError(error: any): PaymentError {\n    console.error('Unexpected payment error:', error);\n    return this.createPaymentError(\n      'UNEXPECTED_ERROR',\n      error?.message || 'An unexpected error occurred',\n      'An unexpected error occurred. Please try again.'\n    );\n  }\n\n  private getRevolutErrorMessage(error: RevolutPaymentError): string {\n    const errorMessages: Record<string, string> = {\n      'PAYMENT_DECLINED': 'Your payment was declined. Please try a different payment method.',\n      'INSUFFICIENT_FUNDS': 'Insufficient funds. Please check your account balance.',\n      'INVALID_CARD': 'Invalid card details. Please check your information.',\n      'EXPIRED_CARD': 'Your card has expired. Please use a different card.',\n      'AUTHENTICATION_FAILED': 'Payment authentication failed. Please try again.',\n      'NETWORK_ERROR': 'Network error. Please check your connection and try again.'\n    };\n\n    return errorMessages[error.code] || 'Payment failed. Please try again.';\n  }\n\n  private formatValidationErrors(errors: PaymentValidationError[]): string {\n    return errors.map(error => error.message).join(', ');\n  }\n\n  private isValidEmail(email: string): boolean {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  }\n\n  private isRetryableError(code: string): boolean {\n    const retryableCodes = ['NETWORK_ERROR', 'TIMEOUT', 'SERVER_ERROR'];\n    return retryableCodes.includes(code);\n  }\n\n  private trackPaymentEvent(\n    eventType: PaymentEventType, \n    paymentData: RevolutOrderRequest | null,\n    metadata?: Record<string, any>\n  ): void {\n    // Implementation for payment analytics tracking\n    console.log('Payment event:', { eventType, paymentData, metadata });\n  }\n\n  /**\n   * Public methods for component interaction\n   */\n  public resetPaymentState(): void {\n    this.paymentState$.next({\n      isLoading: false,\n      isProcessing: false,\n      error: null,\n      currentStep: PaymentStep.FORM_VALIDATION,\n      orderResponse: null\n    });\n  }\n\n  public retryPayment(): void {\n    const currentState = this.paymentState$.value;\n    if (currentState.error?.retryable && currentState.orderResponse) {\n      this.updatePaymentState({ error: null, isLoading: true });\n      // Retry logic would go here\n    }\n  }\n}\n"], "mappings": ";AACA,SAASA,eAAe,QAAuC,MAAM;AACrE,SAASC,UAAU,EAAEC,KAAK,EAAwBC,IAAI,QAAa,gBAAgB;AACnF,SAEEC,WAAW,EAYXC,gBAAgB,EAChBC,iBAAiB,QAEZ,yBAAyB;AAGhC,SAASC,WAAW,QAAQ,8BAA8B;;;;AAO1D,OAAM,MAAOC,cAAc;EAWzBC,YACUC,cAA8B,EAC9BC,YAA0B;IAD1B,KAAAD,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IAZd,KAAAC,aAAa,GAAG,IAAIZ,eAAe,CAAe;MACxDa,SAAS,EAAE,KAAK;MAChBC,YAAY,EAAE,KAAK;MACnBC,KAAK,EAAE,IAAI;MACXC,WAAW,EAAEZ,WAAW,CAACa,eAAe;MACxCC,aAAa,EAAE;KAChB,CAAC;IAEc,KAAAC,YAAY,GAA6B,IAAI,CAACP,aAAa,CAACQ,YAAY,EAAE;EAKvF;EAEH;;;EAGMC,iBAAiBA,CACrBC,WAAgC,EAChCC,cAAA,GAA0B,KAAK;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAE/B,IAAI;QACFD,KAAI,CAACE,kBAAkB,CAAC;UACtBb,SAAS,EAAE,IAAI;UACfG,WAAW,EAAEZ,WAAW,CAACa,eAAe;UACxCF,KAAK,EAAE;SACR,CAAC;QAEF;QACA,MAAMY,gBAAgB,GAAGH,KAAI,CAACI,mBAAmB,CAACN,WAAW,CAAC;QAC9D,IAAI,CAACK,gBAAgB,CAACE,OAAO,EAAE;UAC7B,MAAMd,KAAK,GAAGS,KAAI,CAACM,kBAAkB,CACnC,kBAAkB,EAClB,gCAAgC,EAChCN,KAAI,CAACO,sBAAsB,CAACJ,gBAAgB,CAACK,MAAM,CAAC,CACrD;UACDR,KAAI,CAACS,kBAAkB,CAAClB,KAAK,CAAC;UAC9B,OAAO;YAAEmB,OAAO,EAAE,KAAK;YAAEnB;UAAK,CAAE;QAClC;QAEA;QACAS,KAAI,CAACE,kBAAkB,CAAC;UAAEV,WAAW,EAAEZ,WAAW,CAAC+B;QAAc,CAAE,CAAC;QACpE,MAAMjB,aAAa,SAASM,KAAI,CAACY,kBAAkB,CAACd,WAAW,EAAEC,cAAc,CAAC;QAEhFC,KAAI,CAACE,kBAAkB,CAAC;UACtBR,aAAa;UACbF,WAAW,EAAEZ,WAAW,CAACiC;SAC1B,CAAC;QAEF;QACA,MAAMb,KAAI,CAACc,oBAAoB,CAACpB,aAAa,EAAEI,WAAW,CAAC;QAE3DE,KAAI,CAACe,iBAAiB,CAAClC,gBAAgB,CAACmC,iBAAiB,EAAElB,WAAW,CAAC;QAEvE,OAAO;UAAEY,OAAO,EAAE,IAAI;UAAEO,OAAO,EAAEvB,aAAa,CAACwB;QAAE,CAAE;MAErD,CAAC,CAAC,OAAO3B,KAAK,EAAE;QACd,MAAM4B,YAAY,GAAGnB,KAAI,CAACoB,qBAAqB,CAAC7B,KAAK,CAAC;QACtDS,KAAI,CAACS,kBAAkB,CAACU,YAAY,CAAC;QACrC,OAAO;UAAET,OAAO,EAAE,KAAK;UAAEnB,KAAK,EAAE4B;QAAY,CAAE;MAChD;IAAC;EACH;EAEA;;;EAGcL,oBAAoBA,CAChCpB,aAAmC,EACnCI,WAAgC;IAAA,IAAAuB,MAAA;IAAA,OAAApB,iBAAA;MAEhC,OAAO,IAAIqB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;QACrC,MAAMC,SAAS,GAAGJ,MAAI,CAACK,mBAAmB,EAAE;QAC5C,IAAI,CAACD,SAAS,EAAE;UACdD,MAAM,CAAC,IAAIG,KAAK,CAAC,6BAA6B,CAAC,CAAC;UAChD;QACF;QAEAN,MAAI,CAACO,qBAAqB,CAACH,SAAS,CAAC;QAErCI,eAAe,CAACnC,aAAa,CAACoC,SAAS,EAAE/C,WAAW,CAACgD,UAAU,GAAG,MAAM,GAAG,SAAS,CAAC,CAClFC,IAAI,CAAEC,QAAiC,IAAI;UAC1C,MAAMC,MAAM,GAAqB;YAC/BC,MAAM,EAAEV,SAAS;YACjBW,WAAW,EAAE;cACXC,OAAO,EAAE,MAAM;cACfC,IAAI,EAAE,OAAO;cACbC,MAAM,EAAE;aACT;YACDC,SAAS,EAAEA,CAAA,KAAMnB,MAAI,CAACoB,oBAAoB,CAAC/C,aAAa,EAAEI,WAAW,CAAC;YACtE4C,OAAO,EAAGnD,KAA0B,IAAK8B,MAAI,CAACsB,kBAAkB,CAACpD,KAAK,CAAC;YACvEqD,QAAQ,EAAEA,CAAA,KAAMvB,MAAI,CAACwB,mBAAmB;WACzC;UAEDZ,QAAQ,CAACa,UAAU,CAACZ,MAAM,CAAC;UAC3BX,OAAO,EAAE;QACX,CAAC,CAAC,CACDwB,KAAK,CAAExD,KAAU,IAAI;UACpBiC,MAAM,CAACH,MAAI,CAACf,kBAAkB,CAC5B,oBAAoB,EACpB,kCAAkC,EAClC,iDAAiD,CAClD,CAAC;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;IAAC;EACL;EAEA;;;EAGcmC,oBAAoBA,CAChC/C,aAAmC,EACnCI,WAAgC;IAAA,IAAAkD,MAAA;IAAA,OAAA/C,iBAAA;MAEhC,IAAI;QACF+C,MAAI,CAAC9C,kBAAkB,CAAC;UACtBV,WAAW,EAAEZ,WAAW,CAACqE,kBAAkB;UAC3C3D,YAAY,EAAE;SACf,CAAC;QAEF,MAAM4D,iBAAiB,GAA6B;UAClD,GAAGpD,WAAW;UACdqD,KAAK,EAAEzD,aAAa,CAACoC,SAAS;UAC9BsB,sBAAsB,EAAE1D,aAAa,CAAC0D;SACvC;QAED,MAAMJ,MAAI,CAACK,eAAe,CAACH,iBAAiB,CAAC;QAE7CF,MAAI,CAAC9C,kBAAkB,CAAC;UACtBV,WAAW,EAAEZ,WAAW,CAAC0E,OAAO;UAChCjE,SAAS,EAAE,KAAK;UAChBC,YAAY,EAAE;SACf,CAAC;QAEF0D,MAAI,CAACjC,iBAAiB,CAAClC,gBAAgB,CAAC0E,eAAe,EAAEzD,WAAW,CAAC;QAErEkD,MAAI,CAAC7D,YAAY,CAACqE,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,oBAAoB;UAC7BC,MAAM,EAAE;SACT,CAAC;MAEJ,CAAC,CAAC,OAAOpE,KAAK,EAAE;QACd,MAAM4B,YAAY,GAAG6B,MAAI,CAAC5B,qBAAqB,CAAC7B,KAAK,CAAC;QACtDyD,MAAI,CAACvC,kBAAkB,CAACU,YAAY,CAAC;MACvC;IAAC;EACH;EAEA;;;EAGQwB,kBAAkBA,CAACpD,KAA0B;IACnD,MAAM4B,YAAY,GAAG,IAAI,CAACb,kBAAkB,CAC1Cf,KAAK,CAACqE,IAAI,IAAI,eAAe,EAC7BrE,KAAK,CAACsE,OAAO,IAAI,gBAAgB,EACjC,IAAI,CAACC,sBAAsB,CAACvE,KAAK,CAAC,CACnC;IAED,IAAI,CAACkB,kBAAkB,CAACU,YAAY,CAAC;IACrC,IAAI,CAACJ,iBAAiB,CAAClC,gBAAgB,CAACkF,cAAc,EAAE,IAAI,EAAE;MAAExE;IAAK,CAAE,CAAC;EAC1E;EAEA;;;EAGQsD,mBAAmBA,CAAA;IACzB,IAAI,CAAC3C,kBAAkB,CAAC;MACtBb,SAAS,EAAE,KAAK;MAChBC,YAAY,EAAE,KAAK;MACnBE,WAAW,EAAEZ,WAAW,CAACa;KAC1B,CAAC;IAEF,IAAI,CAACN,YAAY,CAACqE,mBAAmB,CAAC;MACpCC,QAAQ,EAAE,MAAM;MAChBC,OAAO,EAAE,mBAAmB;MAC5BC,MAAM,EAAE;KACT,CAAC;IAEF,IAAI,CAAC5C,iBAAiB,CAAClC,gBAAgB,CAACmF,iBAAiB,EAAE,IAAI,CAAC;EAClE;EAEA;;;EAGcpD,kBAAkBA,CAC9Bd,WAAgC,EAChCC,cAAuB;IAAA,IAAAkE,MAAA;IAAA,OAAAhE,iBAAA;MAEvB,MAAMiE,YAAY,GAAGnE,cAAc,GAC/BkE,MAAI,CAAC/E,cAAc,CAACiF,uBAAuB,CAACrE,WAAW,CAAC,GACxDmE,MAAI,CAAC/E,cAAc,CAAC0B,kBAAkB,CAACd,WAAW,CAAC;MAEvD,OAAOoE,YAAY,CAACE,IAAI,CACtB1F,KAAK,CAACI,iBAAiB,CAACuF,cAAc,CAAC,EACvC5F,UAAU,CAACc,KAAK,IAAG;QACjB,MAAM0E,MAAI,CAAC3D,kBAAkB,CAC3B,uBAAuB,EACvB,gCAAgC,EAChC,mDAAmD,CACpD;MACH,CAAC,CAAC,EACF3B,IAAI,CAAC,CAAC,CAAC,CACR,CAAC2F,SAAS,EAAmC;IAAC;EACjD;EAEA;;;EAGcjB,eAAeA,CAACkB,OAAiC;IAAA,IAAAC,MAAA;IAAA,OAAAvE,iBAAA;MAC7D,MAAMwE,cAAc,GAAGF,OAAO,CAACG,WAAW,GACtCF,MAAI,CAACtF,cAAc,CAACyF,kBAAkB,CAACJ,OAAO,CAAC,GAC/CC,MAAI,CAACtF,cAAc,CAAC0F,aAAa,CAACL,OAAO,CAAC;MAE9C,OAAOE,cAAc,CAACL,IAAI,CACxB1F,KAAK,CAACI,iBAAiB,CAACuF,cAAc,CAAC,EACvC5F,UAAU,CAACc,KAAK,IAAG;QACjB,MAAMiF,MAAI,CAAClE,kBAAkB,CAC3B,2BAA2B,EAC3B,4BAA4B,EAC5B,oDAAoD,CACrD;MACH,CAAC,CAAC,EACF3B,IAAI,CAAC,CAAC,CAAC,CACR,CAAC2F,SAAS,EAAE;IAAC;EAChB;EAEA;;;EAGQlE,mBAAmBA,CAACyE,IAAyB;IACnD,MAAMrE,MAAM,GAA6B,EAAE;IAE3C;IACA,IAAI,CAACqE,IAAI,CAACC,QAAQ,EAAEC,IAAI,EAAE,EAAE;MAC1BvE,MAAM,CAACwE,IAAI,CAAC;QAAEC,KAAK,EAAE,UAAU;QAAEpB,OAAO,EAAE,sBAAsB;QAAED,IAAI,EAAE;MAAU,CAAE,CAAC;IACvF;IAEA,IAAI,CAACiB,IAAI,CAACK,KAAK,EAAEH,IAAI,EAAE,EAAE;MACvBvE,MAAM,CAACwE,IAAI,CAAC;QAAEC,KAAK,EAAE,OAAO;QAAEpB,OAAO,EAAE,mBAAmB;QAAED,IAAI,EAAE;MAAU,CAAE,CAAC;IACjF;IAEA,IAAI,CAACiB,IAAI,CAACM,cAAc,EAAEJ,IAAI,EAAE,EAAE;MAChCvE,MAAM,CAACwE,IAAI,CAAC;QAAEC,KAAK,EAAE,gBAAgB;QAAEpB,OAAO,EAAE,gCAAgC;QAAED,IAAI,EAAE;MAAU,CAAE,CAAC;IACvG;IAEA,IAAI,CAACiB,IAAI,CAACO,WAAW,EAAEL,IAAI,EAAE,EAAE;MAC7BvE,MAAM,CAACwE,IAAI,CAAC;QAAEC,KAAK,EAAE,aAAa;QAAEpB,OAAO,EAAE,0BAA0B;QAAED,IAAI,EAAE;MAAU,CAAE,CAAC;IAC9F;IAEA;IACA,IAAI,CAACiB,IAAI,CAACQ,KAAK,IAAI,CAAC,IAAI,CAACC,YAAY,CAACT,IAAI,CAACQ,KAAK,CAAC,EAAE;MACjD7E,MAAM,CAACwE,IAAI,CAAC;QAAEC,KAAK,EAAE,OAAO;QAAEpB,OAAO,EAAE,yCAAyC;QAAED,IAAI,EAAE;MAAS,CAAE,CAAC;IACtG;IAEA;IACA,IAAIiB,IAAI,CAACH,WAAW,GAAG,CAAC,EAAE;MACxBlE,MAAM,CAACwE,IAAI,CAAC;QAAEC,KAAK,EAAE,aAAa;QAAEpB,OAAO,EAAE,sBAAsB;QAAED,IAAI,EAAE;MAAS,CAAE,CAAC;IACzF;IAEA;IACA,IAAI,CAACiB,IAAI,CAACU,OAAO,EAAE;MACjB/E,MAAM,CAACwE,IAAI,CAAC;QAAEC,KAAK,EAAE,SAAS;QAAEpB,OAAO,EAAE,+BAA+B;QAAED,IAAI,EAAE;MAAU,CAAE,CAAC;IAC/F,CAAC,MAAM;MACL,IAAI,CAAC4B,uBAAuB,CAACX,IAAI,CAACU,OAAO,EAAE/E,MAAM,CAAC;IACpD;IAEA;IACA,IAAI,CAACiF,0BAA0B,CAACZ,IAAI,EAAErE,MAAM,CAAC;IAE7C,OAAO;MACLH,OAAO,EAAEG,MAAM,CAACkF,MAAM,KAAK,CAAC;MAC5BlF;KACD;EACH;EAEA;;;EAGQgF,uBAAuBA,CAACD,OAAY,EAAE/E,MAAgC;IAC5E,IAAI,CAAC+E,OAAO,CAACI,KAAK,IAAI,CAAC,IAAI,CAACC,YAAY,CAACL,OAAO,CAACI,KAAK,CAAC,EAAE;MACvDnF,MAAM,CAACwE,IAAI,CAAC;QAAEC,KAAK,EAAE,OAAO;QAAEpB,OAAO,EAAE,yBAAyB;QAAED,IAAI,EAAE;MAAS,CAAE,CAAC;IACtF;IAEA,IAAI,CAAC2B,OAAO,CAACM,SAAS,EAAEd,IAAI,EAAE,IAAIQ,OAAO,CAACM,SAAS,CAACH,MAAM,GAAG,EAAE,EAAE;MAC/DlF,MAAM,CAACwE,IAAI,CAAC;QAAEC,KAAK,EAAE,WAAW;QAAEpB,OAAO,EAAE,4CAA4C;QAAED,IAAI,EAAE;MAAS,CAAE,CAAC;IAC7G;IAEA,IAAI,CAAC2B,OAAO,CAACO,QAAQ,EAAEf,IAAI,EAAE,IAAIQ,OAAO,CAACO,QAAQ,CAACJ,MAAM,GAAG,EAAE,EAAE;MAC7DlF,MAAM,CAACwE,IAAI,CAAC;QAAEC,KAAK,EAAE,UAAU;QAAEpB,OAAO,EAAE,2CAA2C;QAAED,IAAI,EAAE;MAAS,CAAE,CAAC;IAC3G;IAEA,IAAI,CAAC2B,OAAO,CAACQ,KAAK,EAAEhB,IAAI,EAAE,IAAI,CAAC,IAAI,CAACiB,YAAY,CAACT,OAAO,CAACQ,KAAK,CAAC,EAAE;MAC/DvF,MAAM,CAACwE,IAAI,CAAC;QAAEC,KAAK,EAAE,OAAO;QAAEpB,OAAO,EAAE,gCAAgC;QAAED,IAAI,EAAE;MAAS,CAAE,CAAC;IAC7F;IAEA,IAAI,CAAC2B,OAAO,CAACU,OAAO,EAAElB,IAAI,EAAE,EAAE;MAC5BvE,MAAM,CAACwE,IAAI,CAAC;QAAEC,KAAK,EAAE,SAAS;QAAEpB,OAAO,EAAE,qBAAqB;QAAED,IAAI,EAAE;MAAU,CAAE,CAAC;IACrF;IAEA,IAAI,CAAC2B,OAAO,CAACW,IAAI,EAAEnB,IAAI,EAAE,IAAIQ,OAAO,CAACW,IAAI,CAACR,MAAM,GAAG,GAAG,EAAE;MACtDlF,MAAM,CAACwE,IAAI,CAAC;QAAEC,KAAK,EAAE,MAAM;QAAEpB,OAAO,EAAE,uCAAuC;QAAED,IAAI,EAAE;MAAS,CAAE,CAAC;IACnG;IAEA,IAAI,CAAC2B,OAAO,CAACY,QAAQ,EAAEpB,IAAI,EAAE,IAAI,CAAC,IAAI,CAACqB,eAAe,CAACb,OAAO,CAACY,QAAQ,CAAC,EAAE;MACxE3F,MAAM,CAACwE,IAAI,CAAC;QAAEC,KAAK,EAAE,UAAU;QAAEpB,OAAO,EAAE,4BAA4B;QAAED,IAAI,EAAE;MAAS,CAAE,CAAC;IAC5F;IAEA;IACA,IAAI2B,OAAO,CAACc,OAAO,IAAId,OAAO,CAACc,OAAO,CAACX,MAAM,GAAG,GAAG,EAAE;MACnDlF,MAAM,CAACwE,IAAI,CAAC;QAAEC,KAAK,EAAE,SAAS;QAAEpB,OAAO,EAAE,4CAA4C;QAAED,IAAI,EAAE;MAAS,CAAE,CAAC;IAC3G;IAEA,IAAI2B,OAAO,CAACe,GAAG,IAAI,CAAC,IAAI,CAACC,UAAU,CAAChB,OAAO,CAACe,GAAG,CAAC,EAAE;MAChD9F,MAAM,CAACwE,IAAI,CAAC;QAAEC,KAAK,EAAE,KAAK;QAAEpB,OAAO,EAAE,2BAA2B;QAAED,IAAI,EAAE;MAAS,CAAE,CAAC;IACtF;EACF;EAEA;;;EAGQ6B,0BAA0BA,CAACZ,IAAyB,EAAErE,MAAgC;IAC5F;IACA,MAAMgG,gBAAgB,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,aAAa,CAAC;IAC7D,KAAK,MAAMvB,KAAK,IAAIuB,gBAAgB,EAAE;MACpC,IAAI,IAAI,CAACC,yBAAyB,CAAC5B,IAAI,CAACI,KAAkC,CAAW,CAAC,EAAE;QACtFzE,MAAM,CAACwE,IAAI,CAAC;UAAEC,KAAK;UAAEpB,OAAO,EAAE,6BAA6B;UAAED,IAAI,EAAE;QAAoB,CAAE,CAAC;MAC5F;IACF;IAEA;IACA,MAAMyB,KAAK,GAAGqB,UAAU,CAAC7B,IAAI,CAACQ,KAAK,CAAC;IACpC,IAAIA,KAAK,GAAG,IAAI,EAAE;MAChB7E,MAAM,CAACwE,IAAI,CAAC;QAAEC,KAAK,EAAE,OAAO;QAAEpB,OAAO,EAAE,sCAAsC;QAAED,IAAI,EAAE;MAAoB,CAAE,CAAC;IAC9G;IAEA;IACA,IAAIiB,IAAI,CAACU,OAAO,EAAE;MAChB,MAAMoB,UAAU,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,CAAC;MAC1E,KAAK,MAAM1B,KAAK,IAAI0B,UAAU,EAAE;QAC9B,MAAMC,KAAK,GAAG/B,IAAI,CAACU,OAAO,CAACN,KAAkC,CAAC;QAC9D,IAAI,OAAO2B,KAAK,KAAK,QAAQ,IAAI,IAAI,CAACC,2BAA2B,CAACD,KAAK,CAAC,EAAE;UACxEpG,MAAM,CAACwE,IAAI,CAAC;YAAEC,KAAK;YAAEpB,OAAO,EAAE,6BAA6B;YAAED,IAAI,EAAE;UAAoB,CAAE,CAAC;QAC5F;MACF;IACF;EACF;EAEA;;;EAGQlC,mBAAmBA,CAAA;IACzB,OAAOoF,QAAQ,CAACC,cAAc,CAACjI,iBAAiB,CAACkI,mBAAmB,CAAC;EACvE;EAEQpF,qBAAqBA,CAACH,SAAsB;IAClD,OAAOA,SAAS,CAACwF,UAAU,EAAE;MAC3BxF,SAAS,CAACyF,WAAW,CAACzF,SAAS,CAACwF,UAAU,CAAC;IAC7C;EACF;EAEQ/G,kBAAkBA,CAACiH,OAA8B;IACvD,MAAMC,YAAY,GAAG,IAAI,CAAChI,aAAa,CAACwH,KAAK;IAC7C,IAAI,CAACxH,aAAa,CAACiI,IAAI,CAAC;MAAE,GAAGD,YAAY;MAAE,GAAGD;IAAO,CAAE,CAAC;EAC1D;EAEQ7G,kBAAkBA,CAACsD,IAAY,EAAEC,OAAe,EAAEyD,WAAmB;IAC3E,OAAO;MACL1D,IAAI;MACJC,OAAO;MACPyD,WAAW;MACXC,SAAS,EAAE,IAAI,CAACC,gBAAgB,CAAC5D,IAAI,CAAC;MACtC6D,SAAS,EAAE,IAAIC,IAAI;KACpB;EACH;EAEQjH,kBAAkBA,CAAClB,KAAmB;IAC5C,IAAI,CAACW,kBAAkB,CAAC;MACtBX,KAAK;MACLF,SAAS,EAAE,KAAK;MAChBC,YAAY,EAAE,KAAK;MACnBE,WAAW,EAAEZ,WAAW,CAAC+I;KAC1B,CAAC;IAEF,IAAI,CAACxI,YAAY,CAACqE,mBAAmB,CAAC;MACpCC,QAAQ,EAAE,OAAO;MACjBC,OAAO,EAAE,eAAe;MACxBC,MAAM,EAAEpE,KAAK,CAAC+H;KACf,CAAC;EACJ;EAEQlG,qBAAqBA,CAAC7B,KAAU;IACtCqI,OAAO,CAACrI,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACjD,OAAO,IAAI,CAACe,kBAAkB,CAC5B,kBAAkB,EAClBf,KAAK,EAAEsE,OAAO,IAAI,8BAA8B,EAChD,iDAAiD,CAClD;EACH;EAEQC,sBAAsBA,CAACvE,KAA0B;IACvD,MAAMsI,aAAa,GAA2B;MAC5C,kBAAkB,EAAE,mEAAmE;MACvF,oBAAoB,EAAE,wDAAwD;MAC9E,cAAc,EAAE,sDAAsD;MACtE,cAAc,EAAE,qDAAqD;MACrE,uBAAuB,EAAE,kDAAkD;MAC3E,eAAe,EAAE;KAClB;IAED,OAAOA,aAAa,CAACtI,KAAK,CAACqE,IAAI,CAAC,IAAI,mCAAmC;EACzE;EAEQrD,sBAAsBA,CAACC,MAAgC;IAC7D,OAAOA,MAAM,CAACsH,GAAG,CAACvI,KAAK,IAAIA,KAAK,CAACsE,OAAO,CAAC,CAACkE,IAAI,CAAC,IAAI,CAAC;EACtD;EAEQnC,YAAYA,CAACD,KAAa;IAChC,MAAMqC,UAAU,GAAG,4BAA4B;IAC/C,OAAOA,UAAU,CAACC,IAAI,CAACtC,KAAK,CAAC;EAC/B;EAEQ6B,gBAAgBA,CAAC5D,IAAY;IACnC,MAAMsE,cAAc,GAAG,CAAC,eAAe,EAAE,SAAS,EAAE,cAAc,CAAC;IACnE,OAAOA,cAAc,CAACC,QAAQ,CAACvE,IAAI,CAAC;EACtC;EAEQ7C,iBAAiBA,CACvBqH,SAA2B,EAC3BtI,WAAuC,EACvCuI,QAA8B;IAE9B;IACAT,OAAO,CAACU,GAAG,CAAC,gBAAgB,EAAE;MAAEF,SAAS;MAAEtI,WAAW;MAAEuI;IAAQ,CAAE,CAAC;EACrE;EAEA;;;EAGOE,iBAAiBA,CAAA;IACtB,IAAI,CAACnJ,aAAa,CAACiI,IAAI,CAAC;MACtBhI,SAAS,EAAE,KAAK;MAChBC,YAAY,EAAE,KAAK;MACnBC,KAAK,EAAE,IAAI;MACXC,WAAW,EAAEZ,WAAW,CAACa,eAAe;MACxCC,aAAa,EAAE;KAChB,CAAC;EACJ;EAEO8I,YAAYA,CAAA;IACjB,MAAMpB,YAAY,GAAG,IAAI,CAAChI,aAAa,CAACwH,KAAK;IAC7C,IAAIQ,YAAY,CAAC7H,KAAK,EAAEgI,SAAS,IAAIH,YAAY,CAAC1H,aAAa,EAAE;MAC/D,IAAI,CAACQ,kBAAkB,CAAC;QAAEX,KAAK,EAAE,IAAI;QAAEF,SAAS,EAAE;MAAI,CAAE,CAAC;MACzD;IACF;EACF;EAAC,QAAAoJ,CAAA,G;qBAvcUzJ,cAAc,EAAA0J,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAdhK,cAAc;IAAAiK,OAAA,EAAdjK,cAAc,CAAAkK,IAAA;IAAAC,UAAA,EAFb;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}