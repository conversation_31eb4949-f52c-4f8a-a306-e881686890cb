{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { signal } from '@angular/core';\nimport { UserRole } from '../models/user.model';\nimport { environment } from 'src/environments/environment';\nimport { BehaviorSubject, Subject } from 'rxjs';\nimport jwt_decode from 'jwt-decode';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"./toast.service\";\nimport * as i4 from \"./user.service\";\nconst BACKEND_URL = environment.apiUrl + \"/Auth/\";\nconst BACKEND_TEACHER_URL = environment.apiUrl + \"/Teacher/\";\nconst BACKEND_LMS_URL = environment.apiUrl + \"/LMS/\";\nexport let AuthService = /*#__PURE__*/(() => {\n  class AuthService {\n    constructor(http, router, toastService, userService) {\n      this.http = http;\n      this.router = router;\n      this.toastService = toastService;\n      this.userService = userService;\n      this.isAuthenticated = false;\n      this.isAuthenticatedSignal = signal(false);\n      this.firebaseLoggedIn = signal(false);\n      this.firebaseLogoutEvent = signal(false);\n      this.authStatusListener = new Subject();\n      this.UserRoles = UserRole;\n      this._userId = null; // private property to store userId value\n      this.showRegistrationSuccessfullMessage$ = new BehaviorSubject(false);\n      this.showRegistrationSuccessfullMessage = this.showRegistrationSuccessfullMessage$.asObservable();\n      this.disabledProfileRoutes = false;\n    }\n    get isStudent() {\n      return this.getUserRole() === UserRole.STUDENT;\n    }\n    get isTeacher() {\n      return this.getUserRole() === UserRole.TEACHER;\n    }\n    get teachingLanguage() {\n      return this.getUser().teachingLanguage;\n    }\n    getToken() {\n      return this.token;\n    }\n    getIsAuth() {\n      return this.isAuthenticated;\n    }\n    getUserId() {\n      return this.userId || this._userId; // return userId from private property if it exists, otherwise from local storage\n    }\n    setUserId(userId) {\n      this._userId = userId; // set userId value in private property\n    }\n    getUserRole() {\n      if (this.token) {\n        const tokenInfo = this.getDecodedAccessToken(this.token); // decode token\n        return tokenInfo.role;\n      }\n      return null;\n    }\n    getLoggedInUser() {\n      const authInformation = this.getAuthData();\n      if (authInformation) {\n        let user = JSON.parse(authInformation?.user);\n        // // Mutate timeZone\n        // if (user.timeZone === 'Etc/UTC') {\n        //   user.timeZone = \"Europe/London\";\n        // }\n        return user;\n      }\n      return {};\n    }\n    setAuthStatusListener(status) {\n      this.authStatusListener.next(status);\n    }\n    getAuthStatusListener() {\n      return this.authStatusListener.asObservable();\n    }\n    createUser(email, password, endpoint) {\n      return this.http.post(BACKEND_LMS_URL + endpoint + \"?email=\" + email + \"&pwd=\" + password, {\n        email: email,\n        pwd: password\n      });\n    }\n    confirmUser(userId, code) {\n      this.http.post(BACKEND_LMS_URL + 'ConfirmEmailAccount?userId = ' + userId + '&code=' + code + '', {\n        userId: userId,\n        code: code\n      }).subscribe(res => {\n        if (res) {\n          this.toastService.setShowToastmessage({\n            severity: 'success',\n            summary: '',\n            detail: 'Your account has been confirmed.'\n          });\n          this.router.navigateByUrl('/auth', {\n            replaceUrl: true\n          });\n        }\n      });\n    }\n    forgot(email) {\n      this.http.post(BACKEND_LMS_URL + \"sendForgotPassCode?UsernameResetPass=\" + email, {}).subscribe( /*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* (response) {});\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }());\n    }\n    confirmForgot(email, code, password) {\n      let c = {\n        code: code,\n        email: email,\n        pwd: password,\n        pwdRetype: password\n      };\n      return this.http.post(BACKEND_URL + \"SetPassword\", c);\n    }\n    login(email, password) {\n      const authData = {\n        username: email,\n        password: password\n      };\n      return this.http.post(BACKEND_LMS_URL + \"authenticate\", authData);\n    }\n    getDecodedAccessToken(token) {\n      try {\n        return jwt_decode(token);\n      } catch (Error) {\n        return null;\n      }\n    }\n    setToken(tokenValue) {\n      this.token = tokenValue;\n    }\n    setIsAuthenticated(value) {\n      this.isAuthenticated = value;\n      this.isAuthenticatedSignal.set(value);\n    }\n    setAuthTimer(duration) {\n      this.tokenTimer = setTimeout(() => {\n        this.logout();\n      }, duration * 1000);\n    }\n    setDisabledProfileRoutes(value) {\n      this.disabledProfileRoutes = value;\n    }\n    setShowRegistrationSuccessfullMessage(value) {\n      this.showRegistrationSuccessfullMessage$.next(value);\n    }\n    saveAuthData(token, expirationDate, userId, user, role) {\n      localStorage.setItem(\"token\", token);\n      localStorage.setItem(\"expiration\", expirationDate.toISOString());\n      localStorage.setItem(\"userId\", userId);\n      localStorage.setItem(\"user\", JSON.stringify(user));\n      localStorage.setItem(\"role\", role);\n      if (userId) {\n        this._userId = userId; // set userId value in private property\n      }\n    }\n    autoAuthUser() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        const authInformation = _this.getAuthData();\n        if (!authInformation) {\n          return;\n        }\n        const now = new Date();\n        const expiresIn = authInformation.expirationDate.getTime() - now.getTime();\n        if (expiresIn > 0) {\n          _this.token = authInformation.token;\n          const tokenInfo = _this.getDecodedAccessToken(_this.token); // decode token\n          _this.isAuthenticated = true;\n          _this.userId = authInformation.userId;\n          _this.user = JSON.parse(authInformation.user);\n          if (authInformation.userId) {\n            _this._userId = authInformation.userId; // set userId value in private property\n          }\n          _this.setAuthTimer(expiresIn / 1000);\n          _this.authStatusListener.next(true);\n        }\n      })();\n    }\n    getAuthData() {\n      const token = localStorage.getItem(\"token\");\n      const expirationDate = localStorage.getItem(\"expiration\");\n      const userId = localStorage.getItem(\"userId\");\n      const user = localStorage.getItem(\"user\");\n      const teacherStatus = localStorage.getItem(\"teacherStatus\");\n      const role = localStorage.getItem(\"role\");\n      if (!token || !expirationDate) {\n        return;\n      }\n      return {\n        token: token,\n        expirationDate: new Date(expirationDate),\n        userId: userId,\n        user: user,\n        teacherStatus: teacherStatus,\n        role: role\n      };\n    }\n    logout() {\n      this.token = null;\n      this.isAuthenticated = false;\n      this.authStatusListener.next(false);\n      this.userId = null;\n      this.user = null;\n      clearTimeout(this.tokenTimer);\n      this.clearAuthData();\n      this.router.navigate(['/auth/login']);\n    }\n    clearAuthData() {\n      localStorage.removeItem(\"token\");\n      localStorage.removeItem(\"expiration\");\n      localStorage.removeItem(\"userId\");\n      localStorage.removeItem(\"user\");\n      localStorage.removeItem(\"role\");\n      localStorage.removeItem(\"teacherStatus\");\n      this.teacherStatus = undefined;\n      this._userId = null;\n      this.firebaseLogoutEvent.set(true);\n      this.firebaseLoggedIn.set(false);\n    }\n    isUserInfoFullfileld() {\n      if (this.user?.email === \"\" || this.user?.firstName === \"\" || this.user?.lastName === \"\" || this.user?.skype === \"\" || this.user?.timeZone === \"\" || this.user?.email === \"Not Given\" || this.user?.firstName === \"Not Given\" || this.user?.lastName === \"Not Given\" || this.user?.skype === \"Not Given\" || this.user?.timeZone === \"Not Given\") {\n        return false;\n      }\n      return true;\n    }\n    getTeacherStatus() {\n      return this.http.get(BACKEND_LMS_URL + \"GetTeacherApplicationStatus\");\n    }\n    getTeacherStatusPromise() {\n      return this.http.get(BACKEND_LMS_URL + \"GetTeacherApplicationStatus\").toPromise();\n    }\n    setTeacherStatus(status) {\n      this.teacherStatus = status;\n    }\n    getUserPhoto() {\n      const authInformation = this.getAuthData();\n      if (authInformation) {\n        this.user = JSON.parse(authInformation?.user);\n        let photo = this.user?.photo && this.user?.photo !== 'Not Given' ? environment.apiUrl + \"/\" + this.user?.photo : \"/assets/images/avatar.svg\";\n        return photo;\n      }\n      return \"\";\n    }\n    getUser() {\n      const authInformation = this.getAuthData();\n      this.user = JSON.parse(authInformation?.user);\n      return this.user;\n    }\n    //TODO GOOGLE LOGIN\n    // public signInWithGoogle = ()=> {\n    //   return this._externalAuthService.signIn(GoogleLoginProvider.PROVIDER_ID);\n    // }\n    // public signOutExternal = () => {\n    //   this._externalAuthService.signOut();\n    // }\n    saveLeadDynoAffiliateIdFromRoute(next) {\n      const leadDynoAffiliateId = next.queryParams['afmc'];\n      if (leadDynoAffiliateId !== null && leadDynoAffiliateId !== undefined) {\n        console.log('leadDynoAffiliateId', leadDynoAffiliateId);\n        localStorage.setItem('leadDynoAffiliateId', leadDynoAffiliateId);\n      }\n    }\n    static #_ = this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router), i0.ɵɵinject(i3.ToastService), i0.ɵɵinject(i4.UserService));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return AuthService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}