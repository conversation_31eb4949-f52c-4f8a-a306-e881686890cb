{"ast": null, "code": "import { LessonStatus } from \"src/app/core/models/lesson.model\";\nimport { SubSink } from \"subsink\";\nimport * as moment from \"moment\";\nimport * as data from \"../../../core/models/data\";\nimport { DialogService } from \"primeng/dynamicdialog\";\nimport { LessonInfoCalendarDialogComponent } from \"../../calendar/calendar-dialogs/lesson-info-calendar-dialog/lesson-info-calendar-dialog.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/lesson.service\";\nimport * as i2 from \"src/app/core/services/general.service\";\nimport * as i3 from \"src/app/core/services/classroom.service\";\nimport * as i4 from \"src/app/core/services/auth.service\";\nimport * as i5 from \"primeng/dynamicdialog\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/scrollpanel\";\nimport * as i9 from \"./next-lesson-item/next-lesson-item.component\";\nconst _c0 = [\"hour\"];\nconst _c1 = [\"minute\"];\nconst _c2 = [\"days\"];\nconst _c3 = () => ({\n  width: \"100%\",\n  height: \"118px\"\n});\nfunction NextLessonComponent_ng_container_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 8)(2, \"div\", 9);\n    i0.ɵɵelement(3, \"img\", 10);\n    i0.ɵɵelementStart(4, \"div\", 11);\n    i0.ɵɵtext(5, \" No upcoming arranged lessons yet. \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction NextLessonComponent_ng_container_0_ng_container_3_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", ctx_r0.countDown.days, \" \", ctx_r0.countDown.days === 1 ? \"day\" : \"days\", \" \");\n  }\n}\nfunction NextLessonComponent_ng_container_0_ng_container_3_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.countDown.hours, \" \", ctx_r0.countDown.hours === 1 ? \"hour\" : \"hours\", \" \");\n  }\n}\nfunction NextLessonComponent_ng_container_0_ng_container_3_ng_container_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" and \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction NextLessonComponent_ng_container_0_ng_container_3_span_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 13, 4);\n  }\n}\nfunction NextLessonComponent_ng_container_0_ng_container_3_span_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.countDown.minutes, \" minutes\");\n  }\n}\nfunction NextLessonComponent_ng_container_0_ng_container_3_ng_container_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function NextLessonComponent_ng_container_0_ng_container_3_ng_container_17_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.lessonCardClicked(ctx_r0.todayLessons[0]));\n    });\n    i0.ɵɵtext(2, \" View Lesson \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction NextLessonComponent_ng_container_0_ng_container_3_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function NextLessonComponent_ng_container_0_ng_container_3_ng_template_18_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.generalService.goToBellbirdLink(ctx_r0.todayLessons[0]));\n    });\n    i0.ɵɵtext(1, \" JOIN LESSON\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NextLessonComponent_ng_container_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 8)(2, \"div\", 9);\n    i0.ɵɵelement(3, \"img\", 10);\n    i0.ɵɵelementContainerStart(4);\n    i0.ɵɵelementStart(5, \"div\", 12)(6, \"div\", 11);\n    i0.ɵɵtext(7, \" The next lesson is starting in: \");\n    i0.ɵɵelementStart(8, \"span\", 13, 1);\n    i0.ɵɵtemplate(10, NextLessonComponent_ng_container_0_ng_container_3_span_10_Template, 2, 2, \"span\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"span\", 13, 2);\n    i0.ɵɵtemplate(13, NextLessonComponent_ng_container_0_ng_container_3_span_13_Template, 2, 2, \"span\", 14)(14, NextLessonComponent_ng_container_0_ng_container_3_ng_container_14_Template, 2, 0, \"ng-container\", 5)(15, NextLessonComponent_ng_container_0_ng_container_3_span_15_Template, 2, 0, \"span\", 14)(16, NextLessonComponent_ng_container_0_ng_container_3_span_16_Template, 2, 1, \"span\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, NextLessonComponent_ng_container_0_ng_container_3_ng_container_17_Template, 3, 0, \"ng-container\", 7)(18, NextLessonComponent_ng_container_0_ng_container_3_ng_template_18_Template, 2, 0, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const join_r4 = i0.ɵɵreference(19);\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.countDown.days > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.countDown.hours > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.countDown.hours > 0 && ctx_r0.countDown.minutes > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.countDown.minutes > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.countDown.minutes > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.generalService.isDifferenceWithin30Minutes(ctx_r0.todayLessons[0].startingDate.toString(), ctx_r0.generalService.getCurrentTimeInTimezone(ctx_r0.user.timeZone), ctx_r0.todayLessons[0]))(\"ngIfElse\", join_r4);\n  }\n}\nfunction NextLessonComponent_ng_container_0_ng_template_4_ng_container_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"app-next-lesson-item\", 19);\n    i0.ɵɵlistener(\"onLessonCardClicked\", function NextLessonComponent_ng_container_0_ng_template_4_ng_container_0_div_1_Template_app_next_lesson_item_onLessonCardClicked_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r0.lessonCardClicked($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const filteredUpcomingLesson_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"todayLesson\", filteredUpcomingLesson_r6);\n  }\n}\nfunction NextLessonComponent_ng_container_0_ng_template_4_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NextLessonComponent_ng_container_0_ng_template_4_ng_container_0_div_1_Template, 2, 1, \"div\", 17);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.filteredUpcomingLessons);\n  }\n}\nfunction NextLessonComponent_ng_container_0_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NextLessonComponent_ng_container_0_ng_template_4_ng_container_0_Template, 2, 1, \"ng-container\", 5);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.filteredUpcomingLessons.length > 0);\n  }\n}\nfunction NextLessonComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p-scrollPanel\", 6);\n    i0.ɵɵtemplate(2, NextLessonComponent_ng_container_0_ng_container_2_Template, 6, 0, \"ng-container\", 5)(3, NextLessonComponent_ng_container_0_ng_container_3_Template, 20, 7, \"ng-container\", 7)(4, NextLessonComponent_ng_container_0_ng_template_4_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const upcomingLessons_r7 = i0.ɵɵreference(5);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(5, _c3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.todayLessons.length === 0 && ctx_r0.filteredUpcomingLessons.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.todayLessons.length === 1 && ctx_r0.filteredUpcomingLessons.length === 0)(\"ngIfElse\", upcomingLessons_r7);\n  }\n}\nexport class NextLessonComponent {\n  constructor(lessonService, generalService, classroomService, authService, dialogService) {\n    this.lessonService = lessonService;\n    this.generalService = generalService;\n    this.classroomService = classroomService;\n    this.authService = authService;\n    this.dialogService = dialogService;\n    this.todayLessons = [];\n    this.lessons = [];\n    this.isViewlistMode = true;\n    this.hasDays = false;\n    this.hasHours = false;\n    this.hasMinutes = false;\n    this.lesson = {};\n    this.subs = new SubSink();\n    this.dummyLesson = data.dummyLesson;\n    this.countDown = {};\n    this.filteredUpcomingLessons = [];\n    this.user = {};\n  }\n  ngOnInit() {\n    this.lessons = this.lessons.flatMap(arr => arr);\n    this.user = this.authService.getLoggedInUser();\n    console.log(this.user);\n    this.filteredUpcomingLessons = this.filterRecentLessons(this.lessons);\n    this.todayLessons = this.filterTodayUpcomingLessons(this.lessons);\n    const local = this.generalService.getCurrentTimeInTimezone(this.user.timeZone);\n    console.log(local);\n    this.userTime = this.generalService.convertTimeToHourOfDayFormat(local, this.user.timeZone);\n    console.log(this.userTime);\n  }\n  goToClassRoom(classroom) {\n    this.generalService.navigateToLessonDetails(classroom, this.authService.getUserRole());\n  }\n  lessonCardClicked(lesson) {\n    const dialogData = {\n      type: \"lesson\",\n      lesson: lesson,\n      classroom: lesson.classroom,\n      dialogService: this.dialogService\n    };\n    if (this.generalService.isWithin30Mins(lesson, this.user)) {\n      this.generalService.goToBellbirdLink(lesson);\n      return;\n    }\n    this.generalService.openDialogWithComponent(this.dialogService, LessonInfoCalendarDialogComponent, 320, dialogData, null, result => {\n      // if (result && result.action === 'shareWithMultiple') {\n      //   this.shareLibraryFiles(result.map.map);\n      // }\n    });\n  }\n  ngAfterViewInit() {\n    if (this.todayLessons.length > 0) {\n      this.interval = setInterval(() => {\n        this.countDown = this.lessonService.getTimeToNextLesson(this.todayLessons[0], this.user.timeZone);\n      }, 1000);\n    }\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n    clearInterval(this.interval);\n  }\n  hasNextLessonValues() {\n    this.hasHours = this.hasValueGreaterThanZero(this.hour.nativeElement);\n    this.hasDays = this.hasValueGreaterThanZero(this.days.nativeElement);\n    this.hasMinutes = this.hasValueGreaterThanZero(this.minute.nativeElement);\n    return this.hasHours && this.hasDays && this.hasMinutes;\n  }\n  getNextLesson() {\n    return this.lessonService.getNextLesson();\n  }\n  open() {\n    window.open(this.lesson.bbbLink, \"_blank\");\n  }\n  /**\n   * This code defines a private function named \"hasValueGreaterThanZero\" in the \"next-lesson.component.ts\" file.\n   * The function takes an ElementRef as a parameter and checks if the parsed integer value of its innerHTML property is greater than zero.\n   * It returns a boolean value indicating the result.\n   */\n  hasValueGreaterThanZero(element) {\n    return parseInt(element.nativeElement.innerHTML) > 0;\n  }\n  /**\n   * The method filters the lessons based on their starting date, keeping only the ones that are after or at the same time as the current moment.\n   * The filtered lessons are then sorted using a sorting function provided by a general service.\n   * The sorted and filtered array of lessons is returned as the result.\n   *\n   * @param lessons - The array of lessons to filter.\n   * @returns The filtered and sorted array of Recent Lessons.\n   */\n  filterRecentLessons(lessons) {\n    const now = moment(this.generalService.getCurrentTimeInTimezone(this.user.timeZone));\n    return lessons.filter(lesson => {\n      const lessonDate = moment(lesson.startingDate);\n      const endTime = moment(lesson.startingDate).add(lesson.duration, 'hours'); // Calculate lesson end time\n      // Check if lesson ended recently and it's marked as arranged\n      return endTime.isSameOrAfter(now, 'minute') && lesson.status.toLowerCase().includes(LessonStatus.ARRANGED);\n    }).sort(this.generalService.sortByStartingDate);\n  }\n  /**\n   * Filters the given array of lessons to include only today's upcoming lessons.\n   * Lessons are considered upcoming if their starting date is the same as today or later.\n   * The filtered array is then sorted by the lesson's starting date.\n   *\n   * @param lessons - The array of lessons to filter.\n   * @returns The filtered and sorted array of today's upcoming lessons.\n   */\n  filterTodayUpcomingLessons(lessons) {\n    const now = this.generalService.getCurrentTimeInTimezone(this.user.timeZone);\n    const today = moment().startOf(\"day\");\n    return lessons.filter(lesson => {\n      const lessonDate = moment(lesson.startingDate);\n      const endTime = moment(lesson.startingDate).add(lesson.duration, 'hours'); // Calculate lesson end time\n      // Check if lesson starts today and it's not already past\n      return lessonDate.isSame(today, 'day') && lessonDate.isSameOrAfter(now) && moment(now).isBefore(endTime);\n    }).sort(this.generalService.sortByStartingDate);\n  }\n  static #_ = this.ɵfac = function NextLessonComponent_Factory(t) {\n    return new (t || NextLessonComponent)(i0.ɵɵdirectiveInject(i1.LessonService), i0.ɵɵdirectiveInject(i2.GeneralService), i0.ɵɵdirectiveInject(i3.ClassroomService), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i5.DialogService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NextLessonComponent,\n    selectors: [[\"app-next-lesson\"]],\n    viewQuery: function NextLessonComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n        i0.ɵɵviewQuery(_c1, 7);\n        i0.ɵɵviewQuery(_c2, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.hour = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.minute = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.days = _t.first);\n      }\n    },\n    inputs: {\n      todayLessons: \"todayLessons\",\n      lessons: \"lessons\",\n      isViewlistMode: \"isViewlistMode\"\n    },\n    features: [i0.ɵɵProvidersFeature([DialogService])],\n    decls: 1,\n    vars: 1,\n    consts: [[\"upcomingLessons\", \"\"], [\"days\", \"\"], [\"hour\", \"\"], [\"join\", \"\"], [\"minute\", \"\"], [4, \"ngIf\"], [\"styleClass\", \"custombar1\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"next-lesson\"], [1, \"flex\", \"py-2\", \"px-6\", \"gap-3\", \"align-items-center\", \"text-primary\", \"h-100\"], [\"src\", \"/assets/images/dashboard/idea.svg\", \"height\", \"64\"], [1, \"inline-block\"], [1, \"flex\", \"flex-column\", \"gap-1\"], [1, \"font-bold\"], [\"class\", \"font-bold\", 4, \"ngIf\"], [\"pButton\", \"\", 1, \"p-button-xs\", \"p-button\", \"p-button-rounded\", \"w-full\", \"gray-border-button\", \"max-w-max\", \"p-button-outlined\", 3, \"click\"], [\"pButton\", \"\", 1, \"p-button-xs\", \"p-button\", \"p-button-rounded\", \"w-full\", \"gray-border-button\", \"max-w-max\", \"text-white\", 3, \"click\"], [\"class\", \"flex w-full\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"w-full\"], [1, \"w-100\", 3, \"onLessonCardClicked\", \"todayLesson\"]],\n    template: function NextLessonComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NextLessonComponent_ng_container_0_Template, 6, 6, \"ng-container\", 5);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isViewlistMode);\n      }\n    },\n    dependencies: [i6.NgForOf, i6.NgIf, i7.ButtonDirective, i8.ScrollPanel, i9.NextLessonItemComponent],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.next-lesson[_ngcontent-%COMP%] {\\n  border-radius: 10px;\\n  color: var(--white);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 10px 0;\\n  height: 100%;\\n}\\n.next-lesson[_ngcontent-%COMP%]   .join-lesson-button[_ngcontent-%COMP%] {\\n  border-radius: 30px;\\n  color: var(--main-color);\\n  background-color: var(--white);\\n  text-align: center;\\n  padding: 5px 30px;\\n  margin-left: 15px;\\n  cursor: pointer;\\n  transition: all 0.3 ease;\\n}\\n.next-lesson[_ngcontent-%COMP%]   .join-lesson-button[_ngcontent-%COMP%]:hover {\\n  transition: all 0.3 ease;\\n  background-color: var(--light-purple);\\n  color: white;\\n}\\n\\n.user-color[_ngcontent-%COMP%] {\\n  color: #2D2A4B;\\n}\\n\\n.class-filters[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 3;\\n  width: 100%;\\n  height: auto;\\n  color: #666;\\n  text-align: center;\\n  box-sizing: border-box;\\n  border: 6px solid transparent;\\n  background-clip: padding-box, border-box;\\n  background-origin: padding-box, border-box;\\n  background-image: linear-gradient(#fff, #fff), linear-gradient(#8497FF, #2E3D90);\\n  padding-bottom: 1rem;\\n}\\n@media only screen and (min-width: 768px) {\\n  .class-filters[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n}\\n@media only screen and (min-width: 576px) {\\n  .class-filters[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n    padding-left: 11rem;\\n  }\\n}\\n.class-filters[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%] {\\n  position: absolute;\\n  display: inline-block;\\n  left: -35px;\\n  top: -10px;\\n}\\n@media only screen and (max-width: 768px) {\\n  .class-filters[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%] {\\n    position: relative;\\n    left: auto;\\n    width: 100%;\\n    top: auto;\\n    height: 54px;\\n    background-image: linear-gradient(180deg, hsl(231, 100%, 76%) 0%, hsl(231, 82%, 71%) 11%, hsl(231, 69%, 67%) 22%, hsl(232, 60%, 63%) 33%, hsl(232, 52%, 59%) 44%, hsl(232, 46%, 54%) 56%, hsl(232, 41%, 50%) 67%, hsl(232, 44%, 46%) 78%, hsl(231, 47%, 42%) 89%, hsl(231, 52%, 37%) 100%);\\n  }\\n}\\n.class-filters[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  display: none;\\n  width: 100%;\\n  height: auto;\\n}\\n.class-filters[_ngcontent-%COMP%]   .text-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  text-align: center;\\n  color: white;\\n  padding: 10px;\\n}\\n.class-filters[_ngcontent-%COMP%]   .text-overlay[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["LessonStatus", "SubSink", "moment", "data", "DialogService", "LessonInfoCalendarDialogComponent", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate2", "ctx_r0", "countDown", "days", "hours", "ɵɵtextInterpolate1", "minutes", "ɵɵlistener", "NextLessonComponent_ng_container_0_ng_container_3_ng_container_17_Template_button_click_1_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "lessonCardClicked", "todayLessons", "NextLessonComponent_ng_container_0_ng_container_3_ng_template_18_Template_button_click_0_listener", "_r3", "generalService", "goToBellbirdLink", "ɵɵtemplate", "NextLessonComponent_ng_container_0_ng_container_3_span_10_Template", "NextLessonComponent_ng_container_0_ng_container_3_span_13_Template", "NextLessonComponent_ng_container_0_ng_container_3_ng_container_14_Template", "NextLessonComponent_ng_container_0_ng_container_3_span_15_Template", "NextLessonComponent_ng_container_0_ng_container_3_span_16_Template", "NextLessonComponent_ng_container_0_ng_container_3_ng_container_17_Template", "NextLessonComponent_ng_container_0_ng_container_3_ng_template_18_Template", "ɵɵtemplateRefExtractor", "ɵɵproperty", "isDifferenceWithin30Minutes", "startingDate", "toString", "getCurrentTimeInTimezone", "user", "timeZone", "join_r4", "NextLessonComponent_ng_container_0_ng_template_4_ng_container_0_div_1_Template_app_next_lesson_item_onLessonCardClicked_1_listener", "$event", "_r5", "filteredUpcomingLesson_r6", "NextLessonComponent_ng_container_0_ng_template_4_ng_container_0_div_1_Template", "filteredUpcomingLessons", "NextLessonComponent_ng_container_0_ng_template_4_ng_container_0_Template", "length", "NextLessonComponent_ng_container_0_ng_container_2_Template", "NextLessonComponent_ng_container_0_ng_container_3_Template", "NextLessonComponent_ng_container_0_ng_template_4_Template", "ɵɵstyleMap", "ɵɵpureFunction0", "_c3", "upcomingLessons_r7", "NextLessonComponent", "constructor", "lessonService", "classroomService", "authService", "dialogService", "lessons", "isViewlistMode", "hasDays", "hasHours", "hasMinutes", "lesson", "subs", "du<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "flatMap", "arr", "getLoggedInUser", "console", "log", "filterRecentLessons", "filterTodayUpcomingLessons", "local", "userTime", "convertTimeToHourOfDayFormat", "goToClassRoom", "classroom", "navigateToLessonDetails", "getUserRole", "dialogData", "type", "isWithin30Mins", "openDialogWithComponent", "result", "ngAfterViewInit", "interval", "setInterval", "getTimeToNextLesson", "ngOnDestroy", "unsubscribe", "clearInterval", "hasNextLessonValues", "hasValueGreaterThanZero", "hour", "nativeElement", "minute", "getNextLesson", "open", "window", "bbbLink", "element", "parseInt", "innerHTML", "now", "filter", "lessonDate", "endTime", "add", "duration", "isSameOrAfter", "status", "toLowerCase", "includes", "ARRANGED", "sort", "sortByStartingDate", "today", "startOf", "isSame", "isBefore", "_", "ɵɵdirectiveInject", "i1", "LessonService", "i2", "GeneralService", "i3", "ClassroomService", "i4", "AuthService", "i5", "_2", "selectors", "viewQuery", "NextLessonComponent_Query", "rf", "ctx", "decls", "vars", "consts", "template", "NextLessonComponent_Template", "NextLessonComponent_ng_container_0_Template"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\next-lesson\\next-lesson.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\next-lesson\\next-lesson.component.html"], "sourcesContent": ["import { Component, ElementRef, Input, OnInit, ViewChild } from \"@angular/core\";\r\nimport { Lesson, LessonStatus } from \"src/app/core/models/lesson.model\";\r\nimport { LessonService } from \"src/app/core/services/lesson.service\";\r\nimport { SubSink } from \"subsink\";\r\nimport * as moment from \"moment\";\r\nimport * as data from \"../../../core/models/data\";\r\nimport { GeneralService } from \"src/app/core/services/general.service\";\r\nimport { Classroom } from \"src/app/core/models/classroom.model\";\r\nimport { AuthService } from \"src/app/core/services/auth.service\";\r\nimport { ClassroomService } from \"src/app/core/services/classroom.service\";\r\nimport { DialogService } from \"primeng/dynamicdialog\";\r\nimport { LessonInfoCalendarDialogComponent } from \"../../calendar/calendar-dialogs/lesson-info-calendar-dialog/lesson-info-calendar-dialog.component\";\r\nimport { User } from \"src/app/core/models/user.model\";\r\n\r\n@Component({\r\n  selector: \"app-next-lesson\",\r\n  templateUrl: \"./next-lesson.component.html\",\r\n  styleUrls: [\"./next-lesson.component.scss\"],\r\n  providers: [DialogService],\r\n})\r\nexport class NextLessonComponent implements OnInit {\r\n  @ViewChild(\"hour\", { static: true }) public hour: any;\r\n  @ViewChild(\"minute\", { static: true }) public minute: any;\r\n  @ViewChild(\"days\", { static: true }) public days: any;\r\n  @Input() todayLessons = [] as Lesson[];\r\n  @Input() lessons = [] as Lesson[];\r\n  @Input() isViewlistMode = true;\r\n  hasDays: boolean = false;\r\n  hasHours: boolean = false;\r\n  hasMinutes: boolean = false;\r\n  hours?: number;\r\n  lesson: any | null = {} as any;\r\n  private subs = new SubSink();\r\n  dummyLesson = data.dummyLesson;\r\n  countDown = {};\r\n  private interval: any; // Variable to store the interval reference\r\n  filteredUpcomingLessons = [] as Lesson[];\r\n  public user: User = {} as User;\r\n  userTime!: string;\r\n\r\n  constructor(\r\n    public lessonService: LessonService,\r\n    public generalService: GeneralService,\r\n    public classroomService: ClassroomService,\r\n    private authService: AuthService,\r\n    private dialogService: DialogService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.lessons = this.lessons.flatMap((arr) => arr);\r\n    this.user = this.authService.getLoggedInUser();\r\n    console.log(this.user)\r\n    this.filteredUpcomingLessons = this.filterRecentLessons(this.lessons);\r\n    this.todayLessons = this.filterTodayUpcomingLessons(this.lessons);\r\n\r\n    \r\n    const local = this.generalService.getCurrentTimeInTimezone(this.user.timeZone!);\r\n    console.log(local);\r\n    this.userTime = this.generalService.convertTimeToHourOfDayFormat(local, this.user.timeZone!);\r\n    console.log(this.userTime);\r\n  }\r\n\r\n  goToClassRoom(classroom: Classroom) {\r\n    this.generalService.navigateToLessonDetails(\r\n      classroom,\r\n      this.authService.getUserRole()\r\n    );\r\n  }\r\n\r\n  lessonCardClicked(lesson: Lesson) {\r\n    const dialogData = {\r\n      type: \"lesson\",\r\n      lesson: lesson,\r\n      classroom: lesson.classroom,\r\n      dialogService: this.dialogService,\r\n    };\r\n\r\n    if (this.generalService.isWithin30Mins(lesson, this.user)) {\r\n      this.generalService.goToBellbirdLink(lesson);\r\n      return;\r\n    }\r\n    this.generalService.openDialogWithComponent(\r\n      this.dialogService,\r\n      LessonInfoCalendarDialogComponent,\r\n      320,\r\n      dialogData,\r\n      null,\r\n      (result: any) => {\r\n        // if (result && result.action === 'shareWithMultiple') {\r\n        //   this.shareLibraryFiles(result.map.map);\r\n        // }\r\n      }\r\n    );\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    if (this.todayLessons.length > 0) {\r\n      this.interval = setInterval(() => {\r\n        this.countDown = this.lessonService.getTimeToNextLesson(\r\n          this.todayLessons[0],\r\n          this.user.timeZone!\r\n        );\r\n      }, 1000);\r\n    }\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n    clearInterval(this.interval);\r\n  }\r\n\r\n  hasNextLessonValues() {\r\n    this.hasHours = this.hasValueGreaterThanZero(this.hour.nativeElement);\r\n    this.hasDays = this.hasValueGreaterThanZero(this.days.nativeElement);\r\n    this.hasMinutes = this.hasValueGreaterThanZero(this.minute.nativeElement);\r\n    return this.hasHours && this.hasDays && this.hasMinutes;\r\n  }\r\n\r\n  getNextLesson() {\r\n    return this.lessonService.getNextLesson();\r\n  }\r\n\r\n  open() {\r\n    window.open(this.lesson!.bbbLink, \"_blank\");\r\n  }\r\n\r\n  /**\r\n   * This code defines a private function named \"hasValueGreaterThanZero\" in the \"next-lesson.component.ts\" file.\r\n   * The function takes an ElementRef as a parameter and checks if the parsed integer value of its innerHTML property is greater than zero.\r\n   * It returns a boolean value indicating the result.\r\n   */\r\n\r\n  private hasValueGreaterThanZero(element: ElementRef): boolean {\r\n    return parseInt(element.nativeElement.innerHTML) > 0;\r\n  }\r\n\r\n  /**\r\n   * The method filters the lessons based on their starting date, keeping only the ones that are after or at the same time as the current moment.\r\n   * The filtered lessons are then sorted using a sorting function provided by a general service.\r\n   * The sorted and filtered array of lessons is returned as the result.\r\n   *\r\n   * @param lessons - The array of lessons to filter.\r\n   * @returns The filtered and sorted array of Recent Lessons.\r\n   */\r\nprivate filterRecentLessons(lessons: Lesson[]): Lesson[] {\r\n  const now = moment(this.generalService.getCurrentTimeInTimezone(this.user.timeZone!));\r\n\r\n  return lessons\r\n    .filter((lesson: Lesson) => {\r\n      const lessonDate = moment(lesson.startingDate);\r\n      const endTime = moment(lesson.startingDate).add(lesson.duration, 'hours'); // Calculate lesson end time\r\n      \r\n      // Check if lesson ended recently and it's marked as arranged\r\n      return (\r\n        endTime.isSameOrAfter(now, 'minute') &&\r\n        lesson.status.toLowerCase().includes(LessonStatus.ARRANGED)\r\n      );\r\n    })\r\n    .sort(this.generalService.sortByStartingDate);\r\n}\r\n\r\n\r\n  /**\r\n   * Filters the given array of lessons to include only today's upcoming lessons.\r\n   * Lessons are considered upcoming if their starting date is the same as today or later.\r\n   * The filtered array is then sorted by the lesson's starting date.\r\n   *\r\n   * @param lessons - The array of lessons to filter.\r\n   * @returns The filtered and sorted array of today's upcoming lessons.\r\n   */\r\n  private filterTodayUpcomingLessons(lessons: Lesson[]): Lesson[] {\r\n    const now = this.generalService.getCurrentTimeInTimezone(this.user.timeZone!);\r\n    const today = moment().startOf(\"day\");\r\n  \r\n    return lessons.filter((lesson: Lesson) => {\r\n      const lessonDate = moment(lesson.startingDate);\r\n      const endTime = moment(lesson.startingDate).add(lesson.duration, 'hours'); // Calculate lesson end time\r\n  \r\n      // Check if lesson starts today and it's not already past\r\n      return (\r\n        lessonDate.isSame(today, 'day') && \r\n        lessonDate.isSameOrAfter(now) &&\r\n        moment(now).isBefore(endTime)\r\n      );\r\n    }).sort(this.generalService.sortByStartingDate);\r\n  }\r\n  \r\n}\r\n", "<ng-container *ngIf=\"isViewlistMode\">\r\n    <p-scrollPanel [style]=\"{width: '100%', height: '118px'}\" styleClass=\"custombar1\">\r\n\r\n        <ng-container *ngIf=\"todayLessons.length === 0 && filteredUpcomingLessons.length === 0\">\r\n            <div class=\"next-lesson\">\r\n                <div class=\"flex py-2 px-6 gap-3 align-items-center text-primary h-100\">\r\n                    <img src=\"/assets/images/dashboard/idea.svg\" height=\"64\" />\r\n                    <div class=\"inline-block\">\r\n                        No upcoming arranged lessons yet.\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </ng-container>\r\n        <ng-container *ngIf=\"todayLessons.length === 1 && filteredUpcomingLessons.length === 0; else upcomingLessons\">\r\n            <div class=\"next-lesson\">\r\n                <div class=\"flex py-2 px-6 gap-3 align-items-center text-primary h-100\">\r\n                    <img src=\"/assets/images/dashboard/idea.svg\" height=\"64\" />\r\n                    <ng-container>\r\n                        <div class=\"flex flex-column gap-1\">\r\n                            <div class=\"inline-block\">\r\n                                The next lesson\r\n                                is starting in:\r\n                                <span #days class=\"font-bold\">\r\n                                    <span *ngIf=\"countDown.days > 0\" class=\"font-bold\">\r\n                                        {{countDown.days}} {{countDown.days === 1 ? 'day' : 'days'}}\r\n                                    </span>\r\n                                </span>\r\n                                <span #hour class=\"font-bold\"></span><span *ngIf=\"countDown.hours > 0\"\r\n                                    class=\"font-bold\">{{countDown.hours}} {{\r\n                                    countDown.hours === 1 ? 'hour' : 'hours' }}\r\n                                </span>\r\n                                <ng-container *ngIf=\"countDown.hours > 0 && countDown.minutes > 0\"> and </ng-container>\r\n                                <span #minute class=\"font-bold\" *ngIf=\"countDown.minutes > 0\"></span><span\r\n                                    class=\"font-bold\" *ngIf=\"countDown.minutes > 0\">\r\n                                    {{countDown.minutes}} minutes</span>\r\n                            </div>\r\n                            <ng-container *ngIf=\"!generalService.isDifferenceWithin30Minutes(todayLessons[0].startingDate.toString(), \r\n                            this.generalService.getCurrentTimeInTimezone(this.user.timeZone!), todayLessons[0]); else join\">\r\n                                <button (click)=\"lessonCardClicked(todayLessons[0])\" pButton\r\n                                class=\" p-button-xs p-button p-button-rounded w-full gray-border-button max-w-max p-button-outlined\">\r\n                            \r\n                                View Lesson\r\n                            </button>\r\n                            </ng-container>\r\n                            <ng-template #join>\r\n                                <button (click)=\"generalService.goToBellbirdLink(todayLessons[0])\" pButton\r\n                                class=\" p-button-xs p-button p-button-rounded w-full gray-border-button max-w-max text-white\">\r\n                                JOIN LESSON</button>\r\n                            </ng-template>\r\n\r\n                        </div>\r\n                    </ng-container>\r\n                </div>\r\n            </div>\r\n        </ng-container>\r\n\r\n        <ng-template #upcomingLessons>\r\n            <ng-container *ngIf=\"filteredUpcomingLessons.length > 0; let i = index;\">\r\n                <div class=\"flex w-full\" *ngFor=\"let filteredUpcomingLesson of filteredUpcomingLessons; let i = index;\">\r\n                    <app-next-lesson-item class=\"w-100\" (onLessonCardClicked)=\"lessonCardClicked($event)\"\r\n                        [todayLesson]=\"filteredUpcomingLesson\"></app-next-lesson-item>\r\n                </div>\r\n            </ng-container>\r\n            <!-- <div class=\"flex w-full\" *ngFor=\"let todayLesson of todayLessons; let i = index;\">\r\n                <app-next-lesson-item (onLessonCardClicked)=\"lessonCardClicked(todayLesson)\"\r\n                    class=\"w-100\" [todayLesson]=\"todayLesson\"></app-next-lesson-item>\r\n            </div> -->\r\n        </ng-template>\r\n    </p-scrollPanel>\r\n</ng-container>\r\n<!-- \r\n<ng-container *ngIf=\"!isViewlistMode\">\r\n\r\n\r\n    <div class=\" border-round-xl  overflow-hidden mb-4\" *ngIf=\"todayLessons.length === 1; else upcomingLessons\">\r\n        <div class=\"class-filters border-round-xl\">\r\n            <div class=\"image-container\">\r\n                <img src=\"/assets/images/dashboard/class-filters-cut.svg\" class=\"md:block\" alt=\"class-filters img\" />\r\n                <div class=\"text-overlay w-full\">\r\n                    <h2 class=\"font-xl\">Next lesson</h2>\r\n                </div>\r\n            </div>\r\n            <div\r\n                class=\"filters-content bg-white border-round-xl flex flex-column sm:flex-row align-items-center justify-content-center gap-4\">\r\n                <div class=\"inline-block\">\r\n                    The next lesson\r\n                    is starting in:\r\n                    <span #days class=\"font-bold\">\r\n                        <span *ngIf=\"countDown.days > 0\" class=\"font-bold\">\r\n                            {{countDown.days}} {{countDown.days === 1 ? 'day' : 'days'}}\r\n                        </span>\r\n                    </span>\r\n                    <span #hour class=\"font-bold\"></span><span *ngIf=\"countDown.hours > 0\"\r\n                        class=\"font-bold\">{{countDown.hours}} {{\r\n                        countDown.hours === 1 ? 'hour' : 'hours' }}\r\n                    </span>\r\n                    <ng-container *ngIf=\"countDown.hours > 0 && countDown.minutes > 0\"> and </ng-container>\r\n                    <span #minute class=\"font-bold\" *ngIf=\"countDown.minutes > 0\"></span><span class=\"font-bold\"\r\n                        *ngIf=\"countDown.minutes > 0\">\r\n                        {{countDown.minutes}} minutes</span>\r\n                </div>\r\n                <button (click)=\"generalService.goToJitsiMeetLink(todayLessons[0])\" pButton\r\n                class=\" p-button-xs p-button p-button-rounded w-full gray-border-button max-w-max\"\r\n                [ngClass]=\"{'text-white': generalService.isWithin30Mins(todayLessons[0], user), 'p-button-outlined': !generalService.isWithin30Mins(todayLessons[0], user)}\">\r\n                {{todayLessons[0].googleMeetURL ? 'JOIN LESSON' : 'JOIN LESSON'}}</button>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</ng-container> -->"], "mappings": "AACA,SAAiBA,YAAY,QAAQ,kCAAkC;AAEvE,SAASC,OAAO,QAAQ,SAAS;AACjC,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,OAAO,KAAKC,IAAI,MAAM,2BAA2B;AAKjD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,iCAAiC,QAAQ,mGAAmG;;;;;;;;;;;;;;;;;;;;ICR7IC,EAAA,CAAAC,uBAAA,GAAwF;IAEhFD,EADJ,CAAAE,cAAA,aAAyB,aACmD;IACpEF,EAAA,CAAAG,SAAA,cAA2D;IAC3DH,EAAA,CAAAE,cAAA,cAA0B;IACtBF,EAAA,CAAAI,MAAA,0CACJ;IAERJ,EAFQ,CAAAK,YAAA,EAAM,EACJ,EACJ;;;;;;IAYkBL,EAAA,CAAAE,cAAA,eAAmD;IAC/CF,EAAA,CAAAI,MAAA,GACJ;IAAAJ,EAAA,CAAAK,YAAA,EAAO;;;;IADHL,EAAA,CAAAM,SAAA,EACJ;IADIN,EAAA,CAAAO,kBAAA,MAAAC,MAAA,CAAAC,SAAA,CAAAC,IAAA,OAAAF,MAAA,CAAAC,SAAA,CAAAC,IAAA,6BACJ;;;;;IAEiCV,EAAA,CAAAE,cAAA,eACf;IAAAF,EAAA,CAAAI,MAAA,GAEtB;IAAAJ,EAAA,CAAAK,YAAA,EAAO;;;;IAFeL,EAAA,CAAAM,SAAA,EAEtB;IAFsBN,EAAA,CAAAO,kBAAA,KAAAC,MAAA,CAAAC,SAAA,CAAAE,KAAA,OAAAH,MAAA,CAAAC,SAAA,CAAAE,KAAA,+BAEtB;;;;;IACAX,EAAA,CAAAC,uBAAA,GAAmE;IAACD,EAAA,CAAAI,MAAA,YAAI;;;;;;IACxEJ,EAAA,CAAAG,SAAA,kBAAqE;;;;;IAAAH,EAAA,CAAAE,cAAA,eACjB;IAChDF,EAAA,CAAAI,MAAA,GAA6B;IAAAJ,EAAA,CAAAK,YAAA,EAAO;;;;IAApCL,EAAA,CAAAM,SAAA,EAA6B;IAA7BN,EAAA,CAAAY,kBAAA,MAAAJ,MAAA,CAAAC,SAAA,CAAAI,OAAA,aAA6B;;;;;;IAErCb,EAAA,CAAAC,uBAAA,GACgG;IAC5FD,EAAA,CAAAE,cAAA,iBACqG;IAD7FF,EAAA,CAAAc,UAAA,mBAAAC,mGAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAC,GAAA;MAAA,MAAAT,MAAA,GAAAR,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASX,MAAA,CAAAY,iBAAA,CAAAZ,MAAA,CAAAa,YAAA,CAA+B,CAAC,EAAE;IAAA,EAAC;IAGpDrB,EAAA,CAAAI,MAAA,oBACJ;IAAAJ,EAAA,CAAAK,YAAA,EAAS;;;;;;;IAGLL,EAAA,CAAAE,cAAA,iBAC8F;IADtFF,EAAA,CAAAc,UAAA,mBAAAQ,kGAAA;MAAAtB,EAAA,CAAAgB,aAAA,CAAAO,GAAA;MAAA,MAAAf,MAAA,GAAAR,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAASX,MAAA,CAAAgB,cAAA,CAAAC,gBAAA,CAAAjB,MAAA,CAAAa,YAAA,CAA6C,CAAC,EAAE;IAAA,EAAC;IAElErB,EAAA,CAAAI,MAAA,mBAAW;IAAAJ,EAAA,CAAAK,YAAA,EAAS;;;;;IAlC5CL,EAAA,CAAAC,uBAAA,GAA8G;IAEtGD,EADJ,CAAAE,cAAA,aAAyB,aACmD;IACpEF,EAAA,CAAAG,SAAA,cAA2D;IAC3DH,EAAA,CAAAC,uBAAA,GAAc;IAEND,EADJ,CAAAE,cAAA,cAAoC,cACN;IACtBF,EAAA,CAAAI,MAAA,wCAEA;IAAAJ,EAAA,CAAAE,cAAA,kBAA8B;IAC1BF,EAAA,CAAA0B,UAAA,KAAAC,kEAAA,mBAAmD;IAGvD3B,EAAA,CAAAK,YAAA,EAAO;IACPL,EAAA,CAAAG,SAAA,mBAAqC;IAKgCH,EALhC,CAAA0B,UAAA,KAAAE,kEAAA,mBACf,KAAAC,0EAAA,0BAG6C,KAAAC,kEAAA,mBACL,KAAAC,kEAAA,mBACV;IAExD/B,EAAA,CAAAK,YAAA,EAAM;IASNL,EARA,CAAA0B,UAAA,KAAAM,0EAAA,0BACgG,KAAAC,yEAAA,gCAAAjC,EAAA,CAAAkC,sBAAA,CAO7E;IAMvBlC,EAAA,CAAAK,YAAA,EAAM;;IAGlBL,EADI,CAAAK,YAAA,EAAM,EACJ;;;;;;IA9ByBL,EAAA,CAAAM,SAAA,IAAwB;IAAxBN,EAAA,CAAAmC,UAAA,SAAA3B,MAAA,CAAAC,SAAA,CAAAC,IAAA,KAAwB;IAISV,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAmC,UAAA,SAAA3B,MAAA,CAAAC,SAAA,CAAAE,KAAA,KAAyB;IAItDX,EAAA,CAAAM,SAAA,EAAkD;IAAlDN,EAAA,CAAAmC,UAAA,SAAA3B,MAAA,CAAAC,SAAA,CAAAE,KAAA,QAAAH,MAAA,CAAAC,SAAA,CAAAI,OAAA,KAAkD;IAChCb,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAmC,UAAA,SAAA3B,MAAA,CAAAC,SAAA,CAAAI,OAAA,KAA2B;IACrCb,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAmC,UAAA,SAAA3B,MAAA,CAAAC,SAAA,CAAAI,OAAA,KAA2B;IAGvCb,EAAA,CAAAM,SAAA,EACqE;IAAAN,EADrE,CAAAmC,UAAA,UAAA3B,MAAA,CAAAgB,cAAA,CAAAY,2BAAA,CAAA5B,MAAA,CAAAa,YAAA,IAAAgB,YAAA,CAAAC,QAAA,IAAA9B,MAAA,CAAAgB,cAAA,CAAAe,wBAAA,CAAA/B,MAAA,CAAAgC,IAAA,CAAAC,QAAA,GAAAjC,MAAA,CAAAa,YAAA,KACqE,aAAAqB,OAAA,CAAS;;;;;;IAsBrG1C,EADJ,CAAAE,cAAA,cAAwG,+BAEzD;IADPF,EAAA,CAAAc,UAAA,iCAAA6B,mIAAAC,MAAA;MAAA5C,EAAA,CAAAgB,aAAA,CAAA6B,GAAA;MAAA,MAAArC,MAAA,GAAAR,EAAA,CAAAkB,aAAA;MAAA,OAAAlB,EAAA,CAAAmB,WAAA,CAAuBX,MAAA,CAAAY,iBAAA,CAAAwB,MAAA,CAAyB;IAAA,EAAC;IAEzF5C,EAD+C,CAAAK,YAAA,EAAuB,EAChE;;;;IADEL,EAAA,CAAAM,SAAA,EAAsC;IAAtCN,EAAA,CAAAmC,UAAA,gBAAAW,yBAAA,CAAsC;;;;;IAHlD9C,EAAA,CAAAC,uBAAA,GAAyE;IACrED,EAAA,CAAA0B,UAAA,IAAAqB,8EAAA,kBAAwG;;;;;IAA5C/C,EAAA,CAAAM,SAAA,EAA4B;IAA5BN,EAAA,CAAAmC,UAAA,YAAA3B,MAAA,CAAAwC,uBAAA,CAA4B;;;;;IAD5FhD,EAAA,CAAA0B,UAAA,IAAAuB,wEAAA,0BAAyE;;;;IAA1DjD,EAAA,CAAAmC,UAAA,SAAA3B,MAAA,CAAAwC,uBAAA,CAAAE,MAAA,KAA0C;;;;;IAzDrElD,EAAA,CAAAC,uBAAA,GAAqC;IACjCD,EAAA,CAAAE,cAAA,uBAAkF;IAuD9EF,EArDA,CAAA0B,UAAA,IAAAyB,0DAAA,0BAAwF,IAAAC,0DAAA,2BAUsB,IAAAC,yDAAA,gCAAArD,EAAA,CAAAkC,sBAAA,CA2ChF;IAYlClC,EAAA,CAAAK,YAAA,EAAgB;;;;;;IAnEDL,EAAA,CAAAM,SAAA,EAA0C;IAA1CN,EAAA,CAAAsD,UAAA,CAAAtD,EAAA,CAAAuD,eAAA,IAAAC,GAAA,EAA0C;IAEtCxD,EAAA,CAAAM,SAAA,EAAuE;IAAvEN,EAAA,CAAAmC,UAAA,SAAA3B,MAAA,CAAAa,YAAA,CAAA6B,MAAA,UAAA1C,MAAA,CAAAwC,uBAAA,CAAAE,MAAA,OAAuE;IAUvElD,EAAA,CAAAM,SAAA,EAAyE;IAAAN,EAAzE,CAAAmC,UAAA,SAAA3B,MAAA,CAAAa,YAAA,CAAA6B,MAAA,UAAA1C,MAAA,CAAAwC,uBAAA,CAAAE,MAAA,OAAyE,aAAAO,kBAAA,CAAoB;;;ADOpH,OAAM,MAAOC,mBAAmB;EAoB9BC,YACSC,aAA4B,EAC5BpC,cAA8B,EAC9BqC,gBAAkC,EACjCC,WAAwB,EACxBC,aAA4B;IAJ7B,KAAAH,aAAa,GAAbA,aAAa;IACb,KAAApC,cAAc,GAAdA,cAAc;IACd,KAAAqC,gBAAgB,GAAhBA,gBAAgB;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IArBd,KAAA1C,YAAY,GAAG,EAAc;IAC7B,KAAA2C,OAAO,GAAG,EAAc;IACxB,KAAAC,cAAc,GAAG,IAAI;IAC9B,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,UAAU,GAAY,KAAK;IAE3B,KAAAC,MAAM,GAAe,EAAS;IACtB,KAAAC,IAAI,GAAG,IAAI3E,OAAO,EAAE;IAC5B,KAAA4E,WAAW,GAAG1E,IAAI,CAAC0E,WAAW;IAC9B,KAAA9D,SAAS,GAAG,EAAE;IAEd,KAAAuC,uBAAuB,GAAG,EAAc;IACjC,KAAAR,IAAI,GAAS,EAAU;EAS1B;EAEJgC,QAAQA,CAAA;IACN,IAAI,CAACR,OAAO,GAAG,IAAI,CAACA,OAAO,CAACS,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAAC;IACjD,IAAI,CAAClC,IAAI,GAAG,IAAI,CAACsB,WAAW,CAACa,eAAe,EAAE;IAC9CC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACrC,IAAI,CAAC;IACtB,IAAI,CAACQ,uBAAuB,GAAG,IAAI,CAAC8B,mBAAmB,CAAC,IAAI,CAACd,OAAO,CAAC;IACrE,IAAI,CAAC3C,YAAY,GAAG,IAAI,CAAC0D,0BAA0B,CAAC,IAAI,CAACf,OAAO,CAAC;IAGjE,MAAMgB,KAAK,GAAG,IAAI,CAACxD,cAAc,CAACe,wBAAwB,CAAC,IAAI,CAACC,IAAI,CAACC,QAAS,CAAC;IAC/EmC,OAAO,CAACC,GAAG,CAACG,KAAK,CAAC;IAClB,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACzD,cAAc,CAAC0D,4BAA4B,CAACF,KAAK,EAAE,IAAI,CAACxC,IAAI,CAACC,QAAS,CAAC;IAC5FmC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACI,QAAQ,CAAC;EAC5B;EAEAE,aAAaA,CAACC,SAAoB;IAChC,IAAI,CAAC5D,cAAc,CAAC6D,uBAAuB,CACzCD,SAAS,EACT,IAAI,CAACtB,WAAW,CAACwB,WAAW,EAAE,CAC/B;EACH;EAEAlE,iBAAiBA,CAACiD,MAAc;IAC9B,MAAMkB,UAAU,GAAG;MACjBC,IAAI,EAAE,QAAQ;MACdnB,MAAM,EAAEA,MAAM;MACde,SAAS,EAAEf,MAAM,CAACe,SAAS;MAC3BrB,aAAa,EAAE,IAAI,CAACA;KACrB;IAED,IAAI,IAAI,CAACvC,cAAc,CAACiE,cAAc,CAACpB,MAAM,EAAE,IAAI,CAAC7B,IAAI,CAAC,EAAE;MACzD,IAAI,CAAChB,cAAc,CAACC,gBAAgB,CAAC4C,MAAM,CAAC;MAC5C;IACF;IACA,IAAI,CAAC7C,cAAc,CAACkE,uBAAuB,CACzC,IAAI,CAAC3B,aAAa,EAClBhE,iCAAiC,EACjC,GAAG,EACHwF,UAAU,EACV,IAAI,EACHI,MAAW,IAAI;MACd;MACA;MACA;IAAA,CACD,CACF;EACH;EAEAC,eAAeA,CAAA;IACb,IAAI,IAAI,CAACvE,YAAY,CAAC6B,MAAM,GAAG,CAAC,EAAE;MAChC,IAAI,CAAC2C,QAAQ,GAAGC,WAAW,CAAC,MAAK;QAC/B,IAAI,CAACrF,SAAS,GAAG,IAAI,CAACmD,aAAa,CAACmC,mBAAmB,CACrD,IAAI,CAAC1E,YAAY,CAAC,CAAC,CAAC,EACpB,IAAI,CAACmB,IAAI,CAACC,QAAS,CACpB;MACH,CAAC,EAAE,IAAI,CAAC;IACV;EACF;EAEAuD,WAAWA,CAAA;IACT,IAAI,CAAC1B,IAAI,CAAC2B,WAAW,EAAE;IACvBC,aAAa,CAAC,IAAI,CAACL,QAAQ,CAAC;EAC9B;EAEAM,mBAAmBA,CAAA;IACjB,IAAI,CAAChC,QAAQ,GAAG,IAAI,CAACiC,uBAAuB,CAAC,IAAI,CAACC,IAAI,CAACC,aAAa,CAAC;IACrE,IAAI,CAACpC,OAAO,GAAG,IAAI,CAACkC,uBAAuB,CAAC,IAAI,CAAC1F,IAAI,CAAC4F,aAAa,CAAC;IACpE,IAAI,CAAClC,UAAU,GAAG,IAAI,CAACgC,uBAAuB,CAAC,IAAI,CAACG,MAAM,CAACD,aAAa,CAAC;IACzE,OAAO,IAAI,CAACnC,QAAQ,IAAI,IAAI,CAACD,OAAO,IAAI,IAAI,CAACE,UAAU;EACzD;EAEAoC,aAAaA,CAAA;IACX,OAAO,IAAI,CAAC5C,aAAa,CAAC4C,aAAa,EAAE;EAC3C;EAEAC,IAAIA,CAAA;IACFC,MAAM,CAACD,IAAI,CAAC,IAAI,CAACpC,MAAO,CAACsC,OAAO,EAAE,QAAQ,CAAC;EAC7C;EAEA;;;;;EAMQP,uBAAuBA,CAACQ,OAAmB;IACjD,OAAOC,QAAQ,CAACD,OAAO,CAACN,aAAa,CAACQ,SAAS,CAAC,GAAG,CAAC;EACtD;EAEA;;;;;;;;EAQMhC,mBAAmBA,CAACd,OAAiB;IAC3C,MAAM+C,GAAG,GAAGnH,MAAM,CAAC,IAAI,CAAC4B,cAAc,CAACe,wBAAwB,CAAC,IAAI,CAACC,IAAI,CAACC,QAAS,CAAC,CAAC;IAErF,OAAOuB,OAAO,CACXgD,MAAM,CAAE3C,MAAc,IAAI;MACzB,MAAM4C,UAAU,GAAGrH,MAAM,CAACyE,MAAM,CAAChC,YAAY,CAAC;MAC9C,MAAM6E,OAAO,GAAGtH,MAAM,CAACyE,MAAM,CAAChC,YAAY,CAAC,CAAC8E,GAAG,CAAC9C,MAAM,CAAC+C,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;MAE3E;MACA,OACEF,OAAO,CAACG,aAAa,CAACN,GAAG,EAAE,QAAQ,CAAC,IACpC1C,MAAM,CAACiD,MAAM,CAACC,WAAW,EAAE,CAACC,QAAQ,CAAC9H,YAAY,CAAC+H,QAAQ,CAAC;IAE/D,CAAC,CAAC,CACDC,IAAI,CAAC,IAAI,CAAClG,cAAc,CAACmG,kBAAkB,CAAC;EACjD;EAGE;;;;;;;;EAQQ5C,0BAA0BA,CAACf,OAAiB;IAClD,MAAM+C,GAAG,GAAG,IAAI,CAACvF,cAAc,CAACe,wBAAwB,CAAC,IAAI,CAACC,IAAI,CAACC,QAAS,CAAC;IAC7E,MAAMmF,KAAK,GAAGhI,MAAM,EAAE,CAACiI,OAAO,CAAC,KAAK,CAAC;IAErC,OAAO7D,OAAO,CAACgD,MAAM,CAAE3C,MAAc,IAAI;MACvC,MAAM4C,UAAU,GAAGrH,MAAM,CAACyE,MAAM,CAAChC,YAAY,CAAC;MAC9C,MAAM6E,OAAO,GAAGtH,MAAM,CAACyE,MAAM,CAAChC,YAAY,CAAC,CAAC8E,GAAG,CAAC9C,MAAM,CAAC+C,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;MAE3E;MACA,OACEH,UAAU,CAACa,MAAM,CAACF,KAAK,EAAE,KAAK,CAAC,IAC/BX,UAAU,CAACI,aAAa,CAACN,GAAG,CAAC,IAC7BnH,MAAM,CAACmH,GAAG,CAAC,CAACgB,QAAQ,CAACb,OAAO,CAAC;IAEjC,CAAC,CAAC,CAACQ,IAAI,CAAC,IAAI,CAAClG,cAAc,CAACmG,kBAAkB,CAAC;EACjD;EAAC,QAAAK,CAAA,G;qBArKUtE,mBAAmB,EAAA1D,EAAA,CAAAiI,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAnI,EAAA,CAAAiI,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAArI,EAAA,CAAAiI,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAvI,EAAA,CAAAiI,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAAzI,EAAA,CAAAiI,iBAAA,CAAAS,EAAA,CAAA5I,aAAA;EAAA;EAAA,QAAA6I,EAAA,G;UAAnBjF,mBAAmB;IAAAkF,SAAA;IAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;qCAFnB,CAACjJ,aAAa,CAAC;IAAAmJ,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAN,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QClB5B/I,EAAA,CAAA0B,UAAA,IAAA4H,2CAAA,0BAAqC;;;QAAtBtJ,EAAA,CAAAmC,UAAA,SAAA6G,GAAA,CAAA/E,cAAA,CAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}