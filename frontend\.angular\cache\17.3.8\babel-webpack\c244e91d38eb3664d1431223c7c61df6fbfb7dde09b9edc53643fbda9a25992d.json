{"ast": null, "code": "import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { SubSink } from 'subsink';\nimport { DialogService } from 'primeng/dynamicdialog';\nimport { EditBioDialogComponent } from '../edit-bio-dialog/edit-bio-dialog.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/toast.service\";\nimport * as i2 from \"src/app/core/services/general.service\";\nimport * as i3 from \"src/app/core/services/user.service\";\nimport * as i4 from \"src/app/core/services/auth.service\";\nimport * as i5 from \"primeng/dynamicdialog\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"../../../../shared/upload-files/upload-files.component\";\nimport * as i11 from \"../../../../shared/prime/input/prime-input-dropdown/prime-input-dropdown.component\";\nimport * as i12 from \"primeng/button\";\nimport * as i13 from \"../../../../core/directives/button-disable.directive\";\nimport * as i14 from \"primeng/inputtext\";\nimport * as i15 from \"primeng/calendar\";\nimport * as i16 from \"primeng/ripple\";\nconst _c0 = a0 => ({\n  \"ng-invalid ng-dirty\": a0\n});\nfunction InfoComponent_form_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵlistener(\"click\", function InfoComponent_form_2_div_9_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openBioDialog());\n    });\n    i0.ɵɵelementStart(1, \"span\", 43);\n    i0.ɵɵtext(2, \" Add Bio \");\n    i0.ɵɵelement(3, \"img\", 44);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction InfoComponent_form_2_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1, \"* First Name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InfoComponent_form_2_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1, \"* Last Name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InfoComponent_form_2_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1, \"* preferred Name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InfoComponent_form_2_div_30_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1, \"* Personal Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InfoComponent_form_2_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"label\", 24);\n    i0.ɵɵtext(2, \"Personal Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 47);\n    i0.ɵɵtemplate(4, InfoComponent_form_2_div_30_div_4_Template, 2, 0, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c0, ctx_r1.form.controls.personalEmail.invalid && ctx_r1.form.controls.personalEmail.dirty));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"personalEmail\"));\n  }\n}\nfunction InfoComponent_form_2_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1, \"* Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InfoComponent_form_2_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"label\", 48);\n    i0.ɵɵtext(2, \"Country of Origin\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"app-prime-input-dropdown\", 28);\n    i0.ɵɵlistener(\"valueSelected\", function InfoComponent_form_2_div_41_Template_app_prime_input_dropdown_valueSelected_3_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onOriginChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"parentForm\", ctx_r1.form)(\"countries\", ctx_r1.countries)(\"inputName\", \"origin\")(\"withFlags\", true)(\"placeholder\", \"* Country of origin\")(\"selectedItemValue\", ctx_r1.selectedOriginCountry);\n  }\n}\nfunction InfoComponent_form_2_ng_template_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const timezone_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(timezone_r5.text);\n  }\n}\nfunction InfoComponent_form_2_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1, \"* Timezone is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InfoComponent_form_2_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵtext(1, \"* Skype is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction InfoComponent_form_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 3)(1, \"div\", 4);\n    i0.ɵɵelement(2, \"div\", 5);\n    i0.ɵɵelementStart(3, \"h5\", 6);\n    i0.ɵɵtext(4, \"Avatar\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 7)(6, \"div\", 8)(7, \"div\", 9)(8, \"app-upload-files\", 10);\n    i0.ɵɵlistener(\"onUploadFinished\", function InfoComponent_form_2_Template_app_upload_files_onUploadFinished_8_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.uploadFinished($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, InfoComponent_form_2_div_9_Template, 4, 0, \"div\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"h5\", 6);\n    i0.ɵɵtext(11, \"Personal Info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 7)(13, \"div\", 12)(14, \"div\", 13)(15, \"label\", 14);\n    i0.ɵɵtext(16, \"* First Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(17, \"input\", 15);\n    i0.ɵɵtemplate(18, InfoComponent_form_2_div_18_Template, 2, 0, \"div\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 13)(20, \"label\", 17);\n    i0.ɵɵtext(21, \"* Last Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"input\", 18);\n    i0.ɵɵtemplate(23, InfoComponent_form_2_div_23_Template, 2, 0, \"div\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 13)(25, \"label\", 19);\n    i0.ɵɵtext(26, \"Preferred Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(27, \"input\", 20);\n    i0.ɵɵtemplate(28, InfoComponent_form_2_div_28_Template, 2, 0, \"div\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 12);\n    i0.ɵɵtemplate(30, InfoComponent_form_2_div_30_Template, 5, 4, \"div\", 21);\n    i0.ɵɵelementStart(31, \"div\", 13)(32, \"label\", 22);\n    i0.ɵɵtext(33, \"* Date of Birth\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"p-calendar\", 23);\n    i0.ɵɵlistener(\"onSelect\", function InfoComponent_form_2_Template_p_calendar_onSelect_34_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onBirthDateSelected($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 13)(36, \"label\", 24);\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(38, \"input\", 25);\n    i0.ɵɵtemplate(39, InfoComponent_form_2_div_39_Template, 2, 0, \"div\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 12);\n    i0.ɵɵtemplate(41, InfoComponent_form_2_div_41_Template, 4, 6, \"div\", 26);\n    i0.ɵɵelementStart(42, \"div\", 13)(43, \"label\", 27);\n    i0.ɵɵtext(44, \"* Country of Residence\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"app-prime-input-dropdown\", 28);\n    i0.ɵɵlistener(\"valueSelected\", function InfoComponent_form_2_Template_app_prime_input_dropdown_valueSelected_45_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onResidenceChange($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 29)(47, \"label\", 30);\n    i0.ɵɵtext(48, \"Phone number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 31)(50, \"div\", 32)(51, \"app-prime-input-dropdown\", 33);\n    i0.ɵɵlistener(\"valueSelected\", function InfoComponent_form_2_Template_app_prime_input_dropdown_valueSelected_51_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPhoneCodeChange($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(52, \"input\", 34);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(53, \"div\", 12)(54, \"div\", 35)(55, \"label\", 36);\n    i0.ɵɵtext(56, \"* Timezone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"p-dropdown\", 37);\n    i0.ɵɵlistener(\"onChange\", function InfoComponent_form_2_Template_p_dropdown_onChange_57_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTimeZoneChange($event));\n    });\n    i0.ɵɵtemplate(58, InfoComponent_form_2_ng_template_58_Template, 3, 1, \"ng-template\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(59, InfoComponent_form_2_div_59_Template, 2, 0, \"div\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"div\", 39)(61, \"label\", 30);\n    i0.ɵɵtext(62, \"Skype\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(63, \"input\", 40);\n    i0.ɵɵtemplate(64, InfoComponent_form_2_div_64_Template, 2, 0, \"div\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(65, \"div\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function InfoComponent_form_2_Template_button_click_66_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onSubmit());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.form);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"isProfile\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isTeacher);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(37, _c0, ctx_r1.form.controls.fname.invalid && ctx_r1.form.controls.fname.dirty));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"fname\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(39, _c0, ctx_r1.form.controls.lname.invalid && ctx_r1.form.controls.lname.dirty));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"lname\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(41, _c0, ctx_r1.form.controls.preferredName.invalid && ctx_r1.form.controls.preferredName.dirty));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"preferredName\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.userRole === \"teacher\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"keepInvalid\", true)(\"firstDayOfWeek\", 1)(\"ngClass\", i0.ɵɵpureFunction1(43, _c0, ctx_r1.form.controls.birth.invalid && ctx_r1.form.controls.birth.dirty));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"* \", ctx_r1.userRole === \"teacher\" ? \"MLT\" : \"\", \" Email\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(45, _c0, ctx_r1.form.controls.email.invalid && ctx_r1.form.controls.email.dirty));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"email\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.userRole === \"student\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"parentForm\", ctx_r1.form)(\"countries\", ctx_r1.countries)(\"inputName\", \"residence\")(\"withFlags\", true)(\"placeholder\", \"* Country of Residence\")(\"selectedItemValue\", ctx_r1.selectedResidenceCountry);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"parentForm\", ctx_r1.form)(\"countries\", ctx_r1.phoneCodes)(\"filter\", true)(\"inputName\", \"phoneCode\")(\"placeholder\", \"\")(\"showTextAfterImage\", false)(\"withFlags\", true)(\"selectedItemValue\", ctx_r1.selectedPhoneCode);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"options\", ctx_r1.timezones)(\"filter\", !ctx_r1.isTablet)(\"showClear\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"timeZone\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"skype\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disableTime\", 1000);\n  }\n}\nexport let InfoComponent = /*#__PURE__*/(() => {\n  class InfoComponent {\n    constructor(toast, generalService, userService, authService, dialogService, confirmationService) {\n      this.toast = toast;\n      this.generalService = generalService;\n      this.userService = userService;\n      this.authService = authService;\n      this.dialogService = dialogService;\n      this.confirmationService = confirmationService;\n      this.subs = new SubSink();\n      this.form = new UntypedFormGroup({});\n      this.isLoading = false;\n      this.countries = this.generalService.getCountries();\n      this.selectedOriginCountry = {};\n      this.selectedResidenceCountry = {};\n      this.selectedPhoneCode = {};\n      this.timezones = this.generalService.getTimezones();\n      this.phoneCodes = this.generalService.getPhoneCodes();\n      this.selectedTimezone = \"\";\n      this.imageChangedEvent = '';\n      this.croppedImage = '';\n      this.tryToSave = false;\n      this.isTablet = false;\n      this.loggedInUser = {};\n      this.edit = false;\n      this.formattedBirthDate = \"\";\n      this.userRole = 'teacher';\n      this.refs = [];\n      this.bioChanged = false;\n    }\n    ngOnInit() {\n      this.userRole = this.authService.getUserRole().toLowerCase();\n      this.edit = this.authService.isUserInfoFullfileld() ? false : true;\n      this.subs.add(this.userService.getUserById(this.authService.getLoggedInUser().aspUserId).subscribe(res => {\n        this.loggedInUser = res;\n        this.loadInitialProfileInfo();\n      }));\n      console.log(this.timezones.find(el => el.utc.includes(this.loggedInUser.timeZone)));\n      this.subs.add(this.generalService.deviceKind.subscribe(res => {\n        this.isTablet = res.is1024;\n      }));\n      console.log(this.loggedInUser);\n      // if (this.userRole === 'teacher') {\n      //   this.form.addControl('introduce', new UntypedFormControl(this.loggedInUser.introduce));\n      // }\n    }\n    loadInitialProfileInfo() {\n      this.selectedResidenceCountry = this.countries.find(obj => obj.name === this.loggedInUser.residence);\n      this.selectedOriginCountry = this.countries.find(obj => obj.name === this.loggedInUser.origin);\n      // TODO make dynamic when API get changes\n      this.selectedPhoneCode = this.phoneCodes.filter(el => el.code === this.generalService.extractCountryPrefix(this.loggedInUser.phone))[0] || this.generalService.getDefaultPhoneCode();\n      this.formattedBirthDate = this.generalService.convertToDDMMYYYY(this.loggedInUser.bdate);\n      this.form = new UntypedFormGroup({\n        // image: new UntypedFormControl(null, {\n        //   // validators: [Validators.required],\n        //   // asyncValidators: [mimeType]\n        // }),\n        fname: new UntypedFormControl(this.loggedInUser.firstName, {\n          validators: [Validators.required]\n        }),\n        lname: new UntypedFormControl(this.loggedInUser.lastName, {\n          validators: [Validators.required]\n        }),\n        preferredName: new UntypedFormControl(this.loggedInUser.firstName, {\n          validators: []\n        }),\n        birth: new UntypedFormControl(this.formattedBirthDate, {\n          validators: [Validators.required]\n        }),\n        phoneCode: new UntypedFormControl(this.selectedPhoneCode, {\n          validators: [Validators.required]\n        }),\n        phone: new UntypedFormControl(this.generalService.extractPhoneNumber(this.loggedInUser.phone), {\n          validators: []\n        }),\n        skype: new UntypedFormControl(this.loggedInUser.skype, {\n          validators: [Validators.required]\n        }),\n        personalEmail: new UntypedFormControl(this.loggedInUser.email, {\n          validators: []\n        }),\n        email: new UntypedFormControl(this.loggedInUser.email, {\n          validators: [Validators.required]\n        }),\n        residence: new UntypedFormControl(this.selectedResidenceCountry, {\n          validators: [Validators.required]\n        }),\n        timeZone: new UntypedFormControl(this.timezones.find(el => el.utc.includes(this.loggedInUser.timeZone)), {\n          validators: [Validators.required]\n        }),\n        introduce: new UntypedFormControl(this.loggedInUser.introduce, {\n          validators: []\n        })\n      });\n      this.subscribeToBioChanges();\n      if (this.userRole === 'student') {\n        this.form.addControl('origin', new UntypedFormControl(this.countries.filter(el => el.name === this.loggedInUser.origin)[0], Validators.required));\n        this.form.removeControl('personalEmail');\n      }\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n    }\n    editMode() {\n      this.edit = true;\n    }\n    cancelEdit() {\n      this.edit = false;\n    }\n    onSubmit() {\n      this.tryToSave = true;\n      let invalidInputs = '';\n      for (const controlName in this.form.controls) {\n        if (this.form.controls.hasOwnProperty(controlName)) {\n          const control = this.form.controls[controlName];\n          if (control.invalid) {\n            invalidInputs += `${controlName}, `;\n          }\n        }\n      }\n      let detailMessage = 'Please enter all required fields to continue.';\n      if (invalidInputs !== '') {\n        invalidInputs = invalidInputs.slice(0, -2); // Remove the trailing comma and space\n        if (invalidInputs !== '') {\n          detailMessage += `Invalid input for the following fields: ${invalidInputs}`;\n        }\n      }\n      if (!this.form.valid) {\n        this.toast.setShowToastmessage({\n          severity: 'warn',\n          summary: '',\n          detail: detailMessage\n        });\n        return;\n      }\n      this.userService.setUpdateProfile(true);\n      // view uploadFinished function here\n    }\n    fileChangeEvent(event) {\n      this.imageChangedEvent = event;\n      document.getElementById('cropped-img').style.borderColor = \"var(--main-color)\";\n    }\n    onOriginChange(event) {\n      this.selectedOriginCountry = event.value;\n    }\n    onResidenceChange(event) {\n      console.log(event);\n      this.selectedResidenceCountry = event.value;\n      this.selectedPhoneCode = this.phoneCodes.filter(el => el.iso === this.selectedResidenceCountry.code)[0] || this.generalService.getDefaultPhoneCode();\n    }\n    onTimeZoneChange(event) {\n      this.selectedTimezone = event.value.text;\n    }\n    onPhoneCodeChange(event) {\n      this.selectedPhoneCode = event.value;\n    }\n    uploadFinished(event) {\n      if (event.filePath !== '') this.loggedInUser.photo = event.filePath.dbPath;\n      let value = this.form.value;\n      this.loggedInUser.personalEmail = value.personalEmail;\n      this.loggedInUser.firstName = value.fname;\n      this.loggedInUser.lastName = value.lname;\n      this.loggedInUser.bdate = this.generalService.convertDateStringToIsoFormatWithZeroTime(this.formattedBirthDate);\n      this.loggedInUser.dateOfBirth = this.generalService.convertDateStringToIsoFormatWithZeroTime(this.formattedBirthDate);\n      this.loggedInUser.phone = this.selectedPhoneCode.code + ' ' + value.phone;\n      this.loggedInUser.skype = value.skype;\n      this.loggedInUser.origin = value.origin?.name;\n      this.loggedInUser.timeZone = value.timeZone?.utc[0];\n      this.loggedInUser.residence = this.selectedResidenceCountry?.name;\n      this.loggedInUser.introduce = value.introduce;\n      this.updateProfileAndAPI();\n    }\n    updateProfileAndAPI() {\n      this.form.value.phoneCode = this.form.value.phoneCode.code;\n      console.log(this.form.value);\n      this.subs.add(this.userService.updateUserProfile(this.loggedInUser).subscribe(res => {\n        localStorage.setItem(\"user\", JSON.stringify(this.loggedInUser));\n        this.userService.updateAvatarUrl(this.loggedInUser.avatarUrl);\n        this.userService.updateUserListener();\n        this.toast.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Changes were saved successfully!'\n        });\n        this.form.markAsPristine();\n        // this.toastr.success(\"Changes were saved successfully!\")\n        // this.edit = false;\n      }));\n    }\n    ifFieldValid(field) {\n      return this.form.get(field)?.invalid && this.form.get(field)?.touched || this.tryToSave && this.form.get(field)?.invalid;\n    }\n    getUserPhoto() {\n      return this.userService.getUserPhoto(this.loggedInUser);\n    }\n    onBirthDateSelected(event) {\n      console.log(event);\n      let d = new Date(Date.parse(event));\n      this.formattedBirthDate = this.generalService.convertToDDMMYYYY(event);\n    }\n    toggleEdit() {\n      this.edit = !this.edit;\n    }\n    findCountryImage(phoneCode) {\n      return this.generalService.findCountryImage(phoneCode);\n    }\n    openBioDialog() {\n      const dialogRef = this.dialogService.open(EditBioDialogComponent, {\n        header: 'Choose a Product',\n        width: '26rem',\n        styleClass: 'border-round-xl',\n        showHeader: false,\n        dismissableMask: true,\n        modal: true,\n        contentStyle: {\n          \"max-height\": \"500px\",\n          \"overflow\": \"auto\",\n          \"border-radius\": \"12px\",\n          \"padding\": \"0px\"\n        },\n        baseZIndex: 10000,\n        maskStyleClass: 'transparent-mask',\n        data: {\n          dialogData: {\n            introduce: this.loggedInUser.introduce\n          }\n        }\n      });\n      this.refs.push(dialogRef);\n      dialogRef.onClose.subscribe(data => {\n        if (data) {\n          console.log(data);\n          this.form.get('introduce')?.patchValue(data.data);\n          this.form.get('introduce')?.updateValueAndValidity(data.data);\n          this.loggedInUser.introduce = data.data;\n        }\n      });\n    }\n    subscribeToBioChanges() {\n      this.subs.add(this.form.get('introduce')?.valueChanges.subscribe(value => {\n        if (value !== this.loggedInUser.introduce) {\n          this.bioChanged = true;\n        }\n      }));\n    }\n    /**\n     * Checks if there are any unsaved changes in the form or in the bio field.\n     *\n     * @returns A boolean value indicating whether there are unsaved changes in the form or in the bio field.\n     */\n    hasUnsavedChanges() {\n      return this.form.dirty || this.bioChanged;\n    }\n    canDeactivate() {\n      return this.userService.comfirmDiscardUnsavedChanges(this.hasUnsavedChanges.bind(this), this.confirmationService);\n    }\n    static #_ = this.ɵfac = function InfoComponent_Factory(t) {\n      return new (t || InfoComponent)(i0.ɵɵdirectiveInject(i1.ToastService), i0.ɵɵdirectiveInject(i2.GeneralService), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i5.DialogService), i0.ɵɵdirectiveInject(i6.ConfirmationService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: InfoComponent,\n      selectors: [[\"app-info\"]],\n      features: [i0.ɵɵProvidersFeature([DialogService])],\n      decls: 3,\n      vars: 1,\n      consts: [[1, \"btns\"], [1, \"profile-info\", \"md:mt-4\", \"md:mx-4\"], [3, \"formGroup\", 4, \"ngIf\"], [3, \"formGroup\"], [1, \"profile-info-section\", 2, \"padding-top\", \"0\"], [1, \"profile-info-section-title\"], [1, \"text-2xl\", \"text-primary\", \"font-semibold\"], [1, \"p-fluid\"], [1, \"p-formgrid\", \"grid\", \"justify-content-between\"], [1, \"profile-info-section-file\"], [\"postUrl\", \"/Upload/UploadAvatarAsync\", 3, \"onUploadFinished\", \"isProfile\"], [\"class\", \"light-purple-button border-round-3xl font-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"p-formgrid\", \"grid\"], [1, \"field\", \"col-12\", \"md:col\"], [\"htmlFor\", \"fname\", 1, \"font-medium\"], [\"pInputText\", \"\", \"formControlName\", \"fname\", \"type\", \"text\", 1, \"input-blue\", \"gradient\", \"rounded\", 3, \"ngClass\"], [\"class\", \"input-error\", 4, \"ngIf\"], [\"htmlFor\", \"lname\", 1, \"font-medium\"], [\"pInputText\", \"\", \"formControlName\", \"lname\", \"type\", \"text\", 1, \"input-blue\", \"gradient\", \"rounded\", 3, \"ngClass\"], [\"htmlFor\", \"email2\", 1, \"font-medium\"], [\"pInputText\", \"\", \"formControlName\", \"preferredName\", \"type\", \"text\", 1, \"input-blue\", \"gradient\", \"rounded\", 3, \"ngClass\"], [\"class\", \"field col-12 md:col disabled-div\", 4, \"ngIf\"], [\"htmlFor\", \"birth\", 1, \"font-medium\"], [\"dateFormat\", \"dd/mm/yy\", \"styleClass\", \"date-element\", \"inputStyleClass\", \"input-blue gradient rounded\", \"formControlName\", \"birth\", 3, \"onSelect\", \"keepInvalid\", \"firstDayOfWeek\", \"ngClass\"], [\"htmlFor\", \"email\", 1, \"font-medium\"], [\"pInputText\", \"\", \"formControlName\", \"email\", \"type\", \"email\", 1, \"input-blue\", \"gradient\", \"rounded\", 3, \"ngClass\"], [\"class\", \"field col-12 md:col\", 4, \"ngIf\"], [\"htmlFor\", \"residence\", 1, \"font-medium\"], [\"optionLabel\", \"name\", \"filterBy\", \"name\", \"flagFilter\", \"name\", \"templateValue\", \"name\", \"styleClass\", \"dropdown-blue rounded\", 3, \"valueSelected\", \"parentForm\", \"countries\", \"inputName\", \"withFlags\", \"placeholder\", \"selectedItemValue\"], [1, \"field\", \"col-12\", \"md:col\", \"flex\", \"flex-column\"], [\"htmlFor\", \"skype\", 1, \"font-medium\"], [1, \"inline-flex\"], [1, \"\"], [\"optionLabel\", \"code\", \"filterBy\", \"code\", \"flagFilter\", \"code\", \"templateValue\", \"code\", \"styleClass\", \"dropdown-blue phone-code-input rounded\", 3, \"valueSelected\", \"parentForm\", \"countries\", \"filter\", \"inputName\", \"placeholder\", \"showTextAfterImage\", \"withFlags\", \"selectedItemValue\"], [\"type\", \"text\", \"placeholder\", \"Phone Number\", \"pInputText\", \"\", \"formControlName\", \"phone\", 1, \"input-blue\", \"gradient\", \"rounded\", \"no-radius-left\"], [1, \"field\", \"col-12\", \"md:col-6\"], [\"htmlFor\", \"timeZone\", 1, \"font-medium\"], [\"autocomplete\", \"autocomplete_off_hack_xfr4!k\", \"optionLabel\", \"text\", \"filterBy\", \"text\", \"placeholder\", \"Select timezone\", \"formControlName\", \"timeZone\", \"styleClass\", \"dropdown-blue rounded\", 3, \"onChange\", \"options\", \"filter\", \"showClear\"], [\"pTemplate\", \"item\"], [1, \"field\", \"col\", \"md:col-6\"], [\"pInputText\", \"\", \"formControlName\", \"skype\", \"type\", \"email\", 1, \"input-blue\", \"gradient\", \"rounded\"], [\"disableOnClick\", \"\", \"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"CONFIRM\", \"icon\", \"pi pi-check-circle\", \"iconPos\", \"right\", 1, \"p-button-lg\", \"p-button-raised\", \"p-button-rounded\", \"blue-gradient-btn\", \"mt-3\", 3, \"click\", \"disableTime\"], [1, \"light-purple-button\", \"border-round-3xl\", \"font-sm\", 3, \"click\"], [1, \"text-primary\", \"px-3\", \"flex\", \"justify-content-center\", \"align-items-center\"], [\"src\", \"/assets/images/dashboard/calendar/pencil.svg\", \"height\", \"10\", 1, \"ml-1\", \"w-1rem\"], [1, \"input-error\"], [1, \"field\", \"col-12\", \"md:col\", \"disabled-div\"], [\"pInputText\", \"\", \"formControlName\", \"personalEmail\", \"type\", \"email\", 1, \"input-blue\", \"gradient\", \"rounded\", 3, \"ngClass\"], [\"htmlFor\", \"origin\", 1, \"font-medium\"], [1, \"country-item\"], [1, \"country-name\"]],\n      template: function InfoComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"div\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1);\n          i0.ɵɵtemplate(2, InfoComponent_form_2_Template, 67, 47, \"form\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && !ctx.generalService.isNullishObject(ctx.loggedInUser));\n        }\n      },\n      dependencies: [i7.NgClass, i7.NgIf, i8.ɵNgNoValidate, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgControlStatusGroup, i8.FormGroupDirective, i8.FormControlName, i9.Dropdown, i6.PrimeTemplate, i10.UploadFilesComponent, i11.PrimeInputDropdownComponent, i12.ButtonDirective, i13.DisableOnClickDirective, i14.InputText, i15.Calendar, i16.Ripple],\n      styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}.modal[_ngcontent-%COMP%]{min-width:50vw}[_nghost-%COMP%]     .p-dropdown:not(.p-disabled):hover{border:2px solid rgb(228,185,84)}  .no-label>.p-button-label{display:none}input[type=file][_ngcontent-%COMP%]{visibility:hidden;width:10px}input[type=date][_ngcontent-%COMP%]{position:relative;padding:10px}input[type=date][_ngcontent-%COMP%]::-webkit-calendar-picker-indicator{color:transparent;background:none;z-index:1}input[type=date][_ngcontent-%COMP%]:before{color:transparent;background:none;display:block;font-family:FontAwesome;content:\\\"\\\\f073\\\";width:20px;height:25px;position:absolute;top:12px;right:12px;color:#999}.image-preview[_ngcontent-%COMP%]{height:10rem;margin:1rem 0;border-radius:50%;border:3px solid var(--main-color)}.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{height:100%}[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-header{padding:clamp(.38rem,.47vw + .28rem,.75rem)}[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-items .p-dropdown-item{margin-bottom:clamp(.38rem,.47vw + .28rem,.75rem)}.country-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:clamp(.75rem,.31vw + .69rem,1rem);font-size:clamp(.88rem,.16vw + .84rem,1rem)}.country-item[_ngcontent-%COMP%]   .country-name[_ngcontent-%COMP%]{white-space:pre-line}.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;display:inline-block;vertical-align:middle}.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;clip-path:circle()}.country-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:32px;margin-right:.5rem}.country-item[_ngcontent-%COMP%]   img.smaller[_ngcontent-%COMP%]{width:16px}.info-element[_ngcontent-%COMP%]{padding:10px 0}\"]\n    });\n  }\n  return InfoComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}