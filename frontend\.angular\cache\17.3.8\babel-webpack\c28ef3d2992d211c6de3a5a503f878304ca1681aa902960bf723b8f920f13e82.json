{"ast": null, "code": "import { inject, signal } from '@angular/core';\nimport { serverTimestamp, Timestamp } from 'firebase/firestore';\nimport * as moment from 'moment';\nimport { Observable, combineLatest, firstValueFrom } from 'rxjs';\nimport { map, switchMap } from 'rxjs/operators';\nimport { UserService } from './user.service';\nimport { GeneralService } from './general.service';\nimport { environment } from 'src/environments/environment';\nimport { getAuth, signInAnonymously } from \"firebase/auth\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/fire/compat/firestore\";\nimport * as i2 from \"@angular/fire/compat/auth\";\nimport * as i3 from \"@angular/common/http\";\nconst BACKEND_URL = environment.apiUrl;\nconst BACKEND_LMS_URL = environment.apiUrl + \"/LMS/\";\nexport class ChatService {\n  constructor(db, afAuth, http) {\n    this.db = db;\n    this.afAuth = afAuth;\n    this.http = http;\n    this.userService = inject(UserService);\n    this.generalService = inject(GeneralService);\n    this.isChatEnabled = signal(environment.isChatEnabled);\n    this.showUploadFile = signal(false);\n    this.unreadCount = signal(0);\n    this.currentRoom = signal({});\n    this.showBubbleChat = signal(false);\n    this.dbPath = '/chats';\n    this.dbUnreadPath = '/chats';\n    this.usersRef = this.db.collection('users');\n    this.readCount = 0;\n    this.secondsToCheckOnlineStatus = 10;\n    this.chatsRef = db.collection(this.dbPath);\n    this.usersRef = db.collection('/users');\n    this.unreadCountsRef = db.collection(this.dbUnreadPath);\n  }\n  deleteChatFilesFromLibraryFolder(fileUrl) {\n    console.log(fileUrl);\n    return this.http.post(BACKEND_LMS_URL + \"DeleteChatFilesFromLibraryFolder?fileUrl=\" + fileUrl, {});\n  }\n  // Sign in anonymously\n  signInAnonymously() {\n    return new Observable(observer => {\n      this.afAuth.signInAnonymously().then(userCredential => {\n        console.log('Signed in anonymously:', userCredential.user);\n        observer.next(userCredential);\n        observer.complete();\n      }).catch(error => {\n        console.error('Error signing in anonymously:', error);\n        observer.error(error);\n      });\n    });\n  }\n  signInAnonym() {\n    const auth = getAuth();\n    signInAnonymously(auth).then(() => {\n      // Signed in..\n    }).catch(error => {\n      const errorCode = error.code;\n      const errorMessage = error.message;\n      // ...\n    });\n  }\n  getLastMessage(classroomId) {\n    const chatRef = this.chatsRef.doc(`${classroomId}/`);\n    return chatRef.snapshotChanges().pipe(map(action => {\n      const data = action.payload.data();\n      const id = action.payload.id;\n      return {\n        id,\n        ...data\n      };\n    }));\n  }\n  // Fetch all chats for a classroom\n  getClassroomChats(classroomId) {\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    return this.chatsRef.doc(classroomId).collection('chats').snapshotChanges().pipe(map(actions => actions.map(a => {\n      const data = a.payload.doc.data();\n      const id = a.payload.doc.id;\n      return {\n        id,\n        ...data\n      };\n    })));\n  }\n  // Fetch messages for a specific chat room\n  getChatMessages(chatId, limit, lastMessage) {\n    const messagesQuery = this.db.collection(`${this.dbPath}/${chatId}/messages`, ref => {\n      let query = ref.orderBy('timestamp', 'desc').limit(limit);\n      if (lastMessage) {\n        query = query.startAfter(lastMessage.timestamp);\n      }\n      return query;\n    });\n    return messagesQuery.snapshotChanges().pipe(map(actions => actions.map(a => {\n      const data = a.payload.doc.data();\n      const id = a.payload.doc.id;\n      return {\n        id,\n        ...data\n      };\n    })));\n  }\n  // Create a new chat room\n  createChatRoom(classroomId, participants) {\n    const chat = {\n      classroomId: classroomId,\n      createdAt: moment().format('YYYY-MM-DD HH:mm:ss'),\n      lastMessage: '',\n      lastMessageAt: serverTimestamp(),\n      lastMessageFrom: '',\n      lastMessageFromName: '',\n      lastMessageFromImage: '',\n      lastMessageIsFile: false,\n      lastMessageDeleted: false,\n      lastMessageId: '',\n      lastProcessedMessageId: ''\n    };\n    return this.chatsRef.add(chat);\n  }\n  // Add a new message to a chat room\n  addMessage(classroomId, message, user) {\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    const messageRef = chatRef.collection('messages').doc();\n    const unreadMessagechatRef = this.unreadCountsRef.doc(`${classroomId}`);\n    const unreadMessagechatRefmessageRef = unreadMessagechatRef.collection('messages').doc();\n    const messageId = messageRef.ref.id;\n    return firstValueFrom(chatRef.get()).then(snapshot => {\n      const updateData = {\n        createdAt: moment().format('YYYY-MM-DD HH:mm:ss'),\n        lastMessage: message.content,\n        lastMessageAt: serverTimestamp(),\n        classroomId: classroomId,\n        lastMessageFrom: message.senderId,\n        lastMessageFromName: this.generalService.getPersonFullName(user),\n        lastMessageFromImage: this.userService.getUserPhoto(user?.avatarUrl),\n        lastMessageIsFile: message.isFileFromLibrary || message.isUploadFile,\n        lastMessageDeleted: false,\n        lastMessageId: messageId,\n        // Accessing id through ref\n        lastProcessedMessageId: ''\n      };\n      if (!snapshot.exists) {\n        // Retrieve users in the chat, including teacher and students\n        const usersInChat = [this.currentRoom().teacher, ...this.currentRoom().classroomStudents];\n        const updatedUnreadCounts = {};\n        usersInChat.forEach(chatUser => {\n          if (chatUser.aspUserId !== message.senderId) {\n            updatedUnreadCounts[chatUser.aspUserId] = 1; // Initialize unread count to 1 for new chat\n          }\n        });\n        updateData.unreadCounts = updatedUnreadCounts;\n        return unreadMessagechatRef.set(updateData).then(() => {\n          return messageRef.set(message).then(() => {\n            return messageRef.update({\n              id: messageRef.ref.id\n            }).then(() => {\n              return this.updateChatLastMessage(unreadMessagechatRef, classroomId);\n            });\n          });\n        });\n      } else {\n        // Existing chat room, update last message and unread counts\n        return this.updateLastMessage(chatRef, message, user, false, messageId).then(() => {\n          return messageRef.set(message).then(() => {\n            return messageRef.update({\n              id: messageRef.ref.id\n            });\n          });\n        });\n      }\n    });\n  }\n  updateLastMessage(chatRef, updatedMessage, user, isLastMessageDeleted = false, messageId = '') {\n    const messageRef = chatRef.collection('messages').doc();\n    const updateData = {\n      lastMessage: updatedMessage.content,\n      lastMessageAt: serverTimestamp(),\n      lastMessageFrom: updatedMessage.senderId,\n      lastMessageFromImage: this.userService.getUserPhoto(user?.avatarUrl),\n      lastMessageFromName: this.generalService.getPersonFullName(user),\n      lastMessageIsFile: updatedMessage.isFileFromLibrary || updatedMessage.isUploadFile,\n      lastMessageDeleted: isLastMessageDeleted,\n      lastMessageId: messageId\n    };\n    return firstValueFrom(chatRef.get()).then(snapshot => {\n      console.log('current room', this.currentRoom());\n      const updatedUnreadCounts = {};\n      // Retrieve users in the chat, including teacher and students\n      const usersInChat = [this.currentRoom().teacher, ...this.currentRoom().classroomStudents];\n      usersInChat.forEach(chatUser => {\n        if (chatUser.aspUserId !== updatedMessage.senderId) {\n          updatedUnreadCounts[chatUser.aspUserId] = (snapshot.exists ? snapshot.data().unreadCounts?.[chatUser.aspUserId] || 0 : 0) + 1;\n        }\n      });\n      updateData.unreadCounts = updatedUnreadCounts;\n      return chatRef.update(updateData);\n    });\n  }\n  editMessage(classroomId, messageId, updatedMessage, user, updateLastMessage = false) {\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    const messageRef = chatRef.collection('messages').doc(`${messageId}`);\n    const messagesRef = chatRef.collection('messages', ref => ref.orderBy('timestamp', 'desc').limit(1));\n    const lastMessageDoc = messagesRef.ref.doc();\n    const lastMessageId = lastMessageDoc.id;\n    console.log(lastMessageId);\n    return messageRef.get().toPromise().then(snapshot => {\n      if (snapshot.exists) {\n        return messageRef.update({\n          ...updatedMessage\n        }).then(() => {\n          if (updateLastMessage) {\n            return this.updateLastMessage(chatRef, updatedMessage, user, false, updatedMessage.id);\n          }\n          return Promise.resolve();\n        });\n      } else {\n        throw new Error('Message does not exist');\n      }\n    });\n  }\n  getLastUnreadMessagesCountForUser(classroomId, userId) {\n    const chatRef = this.unreadCountsRef.doc(`${classroomId}`);\n    return chatRef.valueChanges().pipe(map(data => {\n      const unreadCounts = data?.unreadCounts;\n      return unreadCounts ? unreadCounts[userId] || 0 : 0;\n    }));\n  }\n  markLastMessagesAsRead(classroomId, userId) {\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    return this.markLastMessagesAsReadInLastMessage(chatRef, userId);\n  }\n  markLastMessagesAsReadInLastMessage(chatRunreadCountsRefef, userId) {\n    return chatRunreadCountsRefef.get().toPromise().then(snapshot => {\n      if (snapshot.exists) {\n        const data = snapshot.data();\n        const currentCount = data.unreadCounts?.[userId] || 0;\n        // Only update if the unread count for the user is greater than zero\n        if (currentCount > 0) {\n          const updatedUnreadCounts = {\n            ...data.unreadCounts,\n            [userId]: 0\n          };\n          return chatRunreadCountsRefef.update({\n            unreadCounts: updatedUnreadCounts\n          });\n        }\n      }\n      return Promise.resolve(); // If the document doesn't exist or no update is needed\n    });\n  }\n  // Delete a specific message from a chat room\n  deleteMessage(classroomId, message, user, updateLastMessage = false) {\n    console.log(classroomId);\n    console.log(message);\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    const messageRef = chatRef.collection('messages').doc(message.id);\n    return messageRef.delete().then(() => {\n      if (updateLastMessage) {\n        return this.getLatestMessage(classroomId).then(latestMessage => {\n          return this.updateLastMessage(chatRef, {\n            ...message,\n            content: 'Message Deleted'\n          }, user, true, latestMessage?.id).then(() => {\n            // return this.updateChatLastMessage(chatRef);\n          });\n        });\n      } else {\n        return this.updateChatLastMessage(chatRef, classroomId);\n      }\n    });\n  }\n  updateChatLastMessage(chatRef, classroomId) {\n    return this.getLatestMessage(classroomId).then(snapshot => {\n      console.log(snapshot);\n      if (snapshot) {\n        const latestMessage = snapshot;\n        return chatRef.update({\n          lastMessage: latestMessage.content,\n          lastMessageAt: latestMessage.createdAt,\n          lastMessageFrom: latestMessage.senderId,\n          lastMessageFromName: latestMessage.senderName,\n          lastMessageFromImage: latestMessage.senderImage,\n          lastMessageIsFile: latestMessage.isFileFromLibrary || latestMessage.isUploadFile,\n          lastMessageDeleted: false,\n          lastMessageId: latestMessage.id\n        });\n      } else {\n        // Handle case where there are no messages left in the collection\n        return chatRef.update({\n          lastMessage: '',\n          lastMessageAt: null,\n          lastMessageFrom: '',\n          lastMessageFromName: '',\n          lastMessageFromImage: '',\n          lastMessageIsFile: false,\n          lastMessageDeleted: false,\n          lastMessageId: ''\n        });\n      }\n    });\n  }\n  getLatestMessage(classroomId) {\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    return firstValueFrom(chatRef.collection('messages', ref => ref.orderBy('timestamp', 'desc').limit(1)).get()).then(snapshot => {\n      if (!snapshot.empty) {\n        const latestMessage = snapshot.docs[0].data();\n        return latestMessage;\n      } else {\n        return null; // No messages found\n      }\n    });\n  }\n  // Update a chat room document\n  updateChatRoom(classroomId, data) {\n    return this.chatsRef.doc(classroomId).update(data);\n  }\n  // Delete a chat room document\n  deleteChatRoom(classroomId) {\n    return this.chatsRef.doc(classroomId).delete();\n  }\n  addUnreadMessage(classroomId, message) {\n    const messageRef = this.db.collection(`${this.dbUnreadPath}/${classroomId}/messages`).doc(message.id);\n    // Set the initial readStatus for the new message\n    const initialReadStatus = {\n      // Initialize the readStatus for all users, defaulting to false (unread)\n      [message.senderId]: false // Mark the sender as false (unread)\n      // You can add more user IDs here if needed, depending on your application logic\n    };\n    return messageRef.set({\n      readStatus: initialReadStatus,\n      // Add the readStatus field\n      timestamp: serverTimestamp() // Ensure there's a timestamp for ordering\n    });\n  }\n  markMessagesAsRead(classroomId, userId) {\n    return this.db.collection(`${this.dbUnreadPath}/${classroomId}/messages`).get().toPromise().then(querySnapshot => {\n      const batch = this.db.firestore.batch();\n      let hasUpdates = false; // Flag to track if there are any updates\n      querySnapshot.forEach(doc => {\n        const data = doc.data();\n        // Ensure readStatus is a valid map or initialize it\n        const readStatusUpdate = data.readStatus || {};\n        // Only update if the userId is not already marked as read\n        if (!readStatusUpdate[userId]) {\n          readStatusUpdate[userId] = true;\n          // Update the document with the new or modified readStatus map\n          batch.update(doc.ref, {\n            readStatus: readStatusUpdate\n          });\n          hasUpdates = true; // Set the flag to true if an update is made\n        }\n      });\n      // Only commit the batch if there are updates\n      if (hasUpdates) {\n        return batch.commit();\n      }\n      // If no updates were made, resolve immediately\n      return Promise.resolve();\n    });\n  }\n  // Mark messages as read\n  markAllMessagesAsRead(classroomId) {\n    return this.db.collection(`${this.dbUnreadPath}/${classroomId}/messages`).get().toPromise().then(querySnapshot => {\n      const batch = this.db.firestore.batch();\n      querySnapshot.forEach(doc => {\n        const messageData = doc.data();\n        const updatedReadStatus = {};\n        // Set readStatus for all users in the message's readStatus map to true\n        for (const userId in messageData.readStatus) {\n          if (messageData.readStatus.hasOwnProperty(userId)) {\n            const a = `readStatus.${userId}`;\n            updatedReadStatus[a] = true;\n          }\n        }\n        batch.update(doc.ref, updatedReadStatus);\n      });\n      return batch.commit();\n    });\n  }\n  // Get unread messages count for a user in a classroom\n  getUnreadMessagesCount(classroomId, userId) {\n    return this.db.collection(`${this.dbUnreadPath}/${classroomId}/messages`).snapshotChanges().pipe(map(actions => actions.filter(action => {\n      const data = action.payload.doc.data();\n      return !data.readStatus.hasOwnProperty(userId);\n    }).length));\n  }\n  // Get total unread messages count for a user across all classrooms\n  getUnreadMessagesCountForUser(userId) {\n    return this.chatsRef.snapshotChanges().pipe(switchMap(chatSnapshots => {\n      const chatObservables = chatSnapshots.map(snapshot => {\n        const chatId = snapshot.payload.doc.id;\n        return this.getUnreadMessagesCount(chatId, userId);\n      });\n      return combineLatest(chatObservables);\n    }), map(unreadCounts => unreadCounts.reduce((acc, count) => acc + count, 0)));\n  }\n  // Or, to get the user object directly\n  getFireBaseUser() {\n    return this.afAuth.authState;\n  }\n  setUserStatus(userId, status) {\n    const userRef = this.usersRef.doc(userId);\n    return userRef.set({\n      status,\n      lastOnlineTime: Timestamp.now()\n    }, {\n      merge: true\n    });\n  }\n  setUserLastOnlineTime(userId, time) {\n    const userRef = this.usersRef.doc(userId);\n    return userRef.set({\n      status: 'online',\n      lastOnlineTime: time\n    }, {\n      merge: true\n    });\n  }\n  getUserStatus(userId) {\n    return this.usersRef.doc(userId).valueChanges().pipe(map(user => user?.status || 'offline'));\n  }\n  getOnlineUsers() {\n    return this.db.collection('/users', ref => ref.where('status', '==', 'online')).snapshotChanges().pipe(map(actions => actions.map(a => {\n      const data = a.payload.doc.data();\n      const id = a.payload.doc.id;\n      return {\n        ...data,\n        id\n      }; // Corrected merge of id and data\n    })));\n  }\n  /**\n   * Retrieves all users in the Firestore 'users' collection, including their status compared to the last online time.\n   * The status is determined by comparing the current time with the user's last online time. If the user was online\n   * within the last `secondsToCheckOnlineStatus` seconds, the status is set to 'online', otherwise it is set to 'offline'.\n   * If the user does not have a last online time, the status is set to 'offline'.\n   * @returns An Observable of an array of ChatUserStatus objects, where each object contains the user's id, status, and last online time.\n   */\n  getOnlineUsersComparedToLastOnlineTime() {\n    return this.db.collection('/users').snapshotChanges().pipe(map(actions => {\n      const currentTime = Timestamp.now();\n      return actions.map(a => {\n        const data = a.payload.doc.data();\n        const id = a.payload.doc.id;\n        const lastOnlineTime = data.lastOnlineTime;\n        if (lastOnlineTime) {\n          if (!currentTime.seconds || !lastOnlineTime.seconds) {\n            return {\n              ...data,\n              id,\n              status: 'offline'\n            };\n          }\n          const timeDifference = currentTime?.seconds - lastOnlineTime?.seconds;\n          // Check if the user was online within the last 10 seconds\n          if (timeDifference <= this.secondsToCheckOnlineStatus) {\n            return {\n              ...data,\n              id,\n              status: 'online'\n            };\n          } else {\n            return {\n              ...data,\n              id,\n              status: 'offline'\n            };\n          }\n        }\n        return {\n          ...data,\n          id,\n          status: 'offline'\n        };\n      });\n    }));\n  }\n  static #_ = this.ɵfac = function ChatService_Factory(t) {\n    return new (t || ChatService)(i0.ɵɵinject(i1.AngularFirestore), i0.ɵɵinject(i2.AngularFireAuth), i0.ɵɵinject(i3.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ChatService,\n    factory: ChatService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["inject", "signal", "serverTimestamp", "Timestamp", "moment", "Observable", "combineLatest", "firstValueFrom", "map", "switchMap", "UserService", "GeneralService", "environment", "getAuth", "signInAnonymously", "BACKEND_URL", "apiUrl", "BACKEND_LMS_URL", "ChatService", "constructor", "db", "afAuth", "http", "userService", "generalService", "isChatEnabled", "showUploadFile", "unreadCount", "currentRoom", "showBubbleChat", "db<PERSON><PERSON>", "dbUnreadPath", "usersRef", "collection", "readCount", "secondsToCheckOnlineStatus", "chatsRef", "unreadCountsRef", "deleteChatFilesFromLibraryFolder", "fileUrl", "console", "log", "post", "observer", "then", "userCredential", "user", "next", "complete", "catch", "error", "signInAnonym", "auth", "errorCode", "code", "errorMessage", "message", "getLastMessage", "classroomId", "chatRef", "doc", "snapshotChanges", "pipe", "action", "data", "payload", "id", "getClassroomChats", "actions", "a", "getChatMessages", "chatId", "limit", "lastMessage", "messagesQuery", "ref", "query", "orderBy", "startAfter", "timestamp", "createChatRoom", "participants", "chat", "createdAt", "format", "lastMessageAt", "lastMessageFrom", "lastMessageFromName", "lastMessageFromImage", "lastMessageIsFile", "lastMessageDeleted", "lastMessageId", "lastProcessedMessageId", "add", "addMessage", "messageRef", "unreadMessagechatRef", "unreadMessagechatRefmessageRef", "messageId", "get", "snapshot", "updateData", "content", "senderId", "getPersonFullName", "getUserPhoto", "avatarUrl", "isFileFromLibrary", "isUploadFile", "exists", "usersInChat", "teacher", "classroomStudents", "updatedUnreadCounts", "for<PERSON>ach", "chatUser", "aspUserId", "unreadCounts", "set", "update", "updateChatLastMessage", "updateLastMessage", "updatedMessage", "isLastMessageDeleted", "editMessage", "messagesRef", "lastMessageDoc", "to<PERSON>romise", "Promise", "resolve", "Error", "getLastUnreadMessagesCountForUser", "userId", "valueChanges", "markLastMessagesAsRead", "markLastMessagesAsReadInLastMessage", "chatRunreadCountsRefef", "currentCount", "deleteMessage", "delete", "getLatestMessage", "latestMessage", "sender<PERSON>ame", "senderImage", "empty", "docs", "updateChatRoom", "deleteChatRoom", "addUnreadMessage", "initialReadStatus", "readStatus", "markMessagesAsRead", "querySnapshot", "batch", "firestore", "hasUpdates", "readStatusUpdate", "commit", "markAllMessagesAsRead", "messageData", "updatedReadStatus", "hasOwnProperty", "getUnreadMessagesCount", "filter", "length", "getUnreadMessagesCountForUser", "chatSnapshots", "chatObservables", "reduce", "acc", "count", "getFireBaseUser", "authState", "setUserStatus", "status", "userRef", "lastOnlineTime", "now", "merge", "setUserLastOnlineTime", "time", "getUserStatus", "getOnlineUsers", "where", "getOnlineUsersComparedToLastOnlineTime", "currentTime", "seconds", "timeDifference", "_", "i0", "ɵɵinject", "i1", "AngularFirestore", "i2", "AngularFireAuth", "i3", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\services\\chat.service.ts"], "sourcesContent": ["import { Injectable, inject, signal } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { AngularFirestore, AngularFirestoreCollection, AngularFirestoreDocument } from '@angular/fire/compat/firestore';\r\nimport { FieldValue, serverTimestamp, Timestamp } from 'firebase/firestore';\r\nimport * as moment from 'moment';\r\nimport { Observable, combineLatest, defer, firstValueFrom, forkJoin, from } from 'rxjs';\r\nimport { map, switchMap } from 'rxjs/operators';\r\nimport { UserService } from './user.service';\r\nimport { GeneralService } from './general.service';\r\nimport { environment } from 'src/environments/environment';\r\nconst BACKEND_URL = environment.apiUrl;\r\nconst BACKEND_LMS_URL = environment.apiUrl + \"/LMS/\";\r\nimport { getAuth, signInAnonymously } from \"firebase/auth\";\r\nimport { AngularFireAuth } from '@angular/fire/compat/auth';\r\n\r\ninterface Chat {\r\n  createdAt: string;\r\n  lastMessage: string;\r\n  lastMessageAt: FieldValue;\r\n  lastMessageFrom: string;\r\n  lastMessageFromName: string;\r\n  lastMessageFromImage: string;\r\n  lastMessageIsFile: boolean;\r\n  lastMessageDeleted: boolean;\r\n  lastMessageId: string;\r\n  lastProcessedMessageId: string;\r\n  classroomId: string;\r\n}\r\n\r\nexport interface ChatUserStatus {\r\n  id?: string;\r\n  status: 'online' | 'offline';\r\n  lastOnlineTime: FieldValue;\r\n}\r\n\r\n\r\nexport interface Message {\r\n  id?: string;\r\n  senderId: string;\r\n  content: string;\r\n  timestamp: FieldValue;\r\n  isFileFromLibrary: boolean;\r\n  isUploadFile: boolean;\r\n  readStatus: {}\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ChatService {\r\n  userService = inject(UserService);\r\n  generalService = inject(GeneralService);\r\n  isChatEnabled = signal(environment.isChatEnabled);\r\n  showUploadFile = signal(false);\r\n  unreadCount = signal(0);\r\n  currentRoom = signal({} as any);\r\n  showBubbleChat = signal(false);\r\n  private dbPath = '/chats';\r\n  private dbUnreadPath = '/chats';\r\n  chatsRef: AngularFirestoreCollection<Chat>;\r\n  unreadCountsRef: AngularFirestoreCollection<any>;\r\n  private usersRef: AngularFirestoreCollection<ChatUserStatus> = this.db.collection('users');\r\n  readCount = 0;\r\n  readonly secondsToCheckOnlineStatus = 10;\r\n\r\n\r\n  constructor(private db: AngularFirestore, private afAuth: AngularFireAuth, private http: HttpClient) {\r\n    this.chatsRef = db.collection(this.dbPath);\r\n    this.usersRef = db.collection('/users');\r\n    this.unreadCountsRef = db.collection(this.dbUnreadPath);\r\n  }\r\n\r\n\r\n  deleteChatFilesFromLibraryFolder(fileUrl: any): Observable<any> {\r\n    console.log(fileUrl)\r\n    return this.http.post<any>(BACKEND_LMS_URL + \"DeleteChatFilesFromLibraryFolder?fileUrl=\" + fileUrl, {})\r\n  }\r\n\r\n  // Sign in anonymously\r\n  signInAnonymously(): Observable<any> {\r\n    return new Observable(observer => {\r\n      this.afAuth.signInAnonymously()\r\n        .then(userCredential => {\r\n          console.log('Signed in anonymously:', userCredential.user);\r\n          observer.next(userCredential);\r\n          observer.complete();\r\n        })\r\n        .catch(error => {\r\n          console.error('Error signing in anonymously:', error);\r\n          observer.error(error);\r\n        });\r\n    });\r\n  }\r\n\r\n  signInAnonym() {\r\n    const auth = getAuth();\r\n    signInAnonymously(auth)\r\n      .then(() => {\r\n        // Signed in..\r\n      })\r\n      .catch((error) => {\r\n        const errorCode = error.code;\r\n        const errorMessage = error.message;\r\n        // ...\r\n      });\r\n  }\r\n\r\n  getLastMessage(classroomId: string): Observable<Message> {\r\n    const chatRef = this.chatsRef.doc<Message>(`${classroomId}/`);\r\n\r\n    return chatRef.snapshotChanges().pipe(\r\n      map(action => {\r\n        const data = action.payload.data() as Message;\r\n        const id = action.payload.id;\r\n        return { id, ...data };\r\n      })\r\n    );\r\n  }\r\n\r\n  // Fetch all chats for a classroom\r\n  getClassroomChats(classroomId: string): Observable<Chat[]> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    return this.chatsRef.doc(classroomId).collection<Chat>('chats')\r\n      .snapshotChanges().pipe(\r\n        map(actions => actions.map(a => {\r\n          const data = a.payload.doc.data() as Chat;\r\n          const id = a.payload.doc.id;\r\n          return { id, ...data };\r\n        }))\r\n      );\r\n  }\r\n\r\n  // Fetch messages for a specific chat room\r\n  getChatMessages(chatId: string, limit: number, lastMessage?: Message): Observable<Message[]> {\r\n    const messagesQuery = this.db.collection<Message>(`${this.dbPath}/${chatId}/messages`, ref => {\r\n      let query = ref.orderBy('timestamp', 'desc').limit(limit);\r\n      if (lastMessage) {\r\n        query = query.startAfter(lastMessage.timestamp);\r\n      }\r\n      return query;\r\n    });\r\n\r\n    return messagesQuery.snapshotChanges().pipe(\r\n      map(actions => actions.map(a => {\r\n        const data = a.payload.doc.data() as Message;\r\n        const id = a.payload.doc.id;\r\n        return { id, ...data };\r\n      }))\r\n    );\r\n  }\r\n\r\n  // Create a new chat room\r\n  createChatRoom(classroomId: string, participants: string[]) {\r\n    const chat: Chat = {\r\n      classroomId: classroomId,\r\n      createdAt: moment().format('YYYY-MM-DD HH:mm:ss'),\r\n      lastMessage: '',\r\n      lastMessageAt: serverTimestamp(),\r\n      lastMessageFrom: '',\r\n      lastMessageFromName: '',\r\n      lastMessageFromImage: '',\r\n      lastMessageIsFile: false,\r\n      lastMessageDeleted: false,\r\n      lastMessageId: '',\r\n      lastProcessedMessageId: '',\r\n    };\r\n    return this.chatsRef.add(chat);\r\n  }\r\n\r\n  // Add a new message to a chat room\r\n  addMessage(classroomId: string, message: Message, user: any): Promise<void> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    const messageRef = chatRef.collection('messages').doc();\r\n\r\n\r\n    const unreadMessagechatRef = this.unreadCountsRef.doc(`${classroomId}`);\r\n    const unreadMessagechatRefmessageRef = unreadMessagechatRef.collection('messages').doc();\r\n\r\n    const messageId = (messageRef.ref.id);\r\n    return firstValueFrom(chatRef.get()).then(snapshot => {\r\n      const updateData: any = {\r\n        createdAt: moment().format('YYYY-MM-DD HH:mm:ss'),\r\n        lastMessage: message.content,\r\n        lastMessageAt: serverTimestamp(),\r\n        classroomId: classroomId,\r\n        lastMessageFrom: message.senderId,\r\n        lastMessageFromName: this.generalService.getPersonFullName(user),\r\n        lastMessageFromImage: this.userService.getUserPhoto(user?.avatarUrl),\r\n        lastMessageIsFile: message.isFileFromLibrary || message.isUploadFile,\r\n        lastMessageDeleted: false,\r\n        lastMessageId: messageId, // Accessing id through ref\r\n        lastProcessedMessageId: '',\r\n      };\r\n\r\n      if (!snapshot.exists) {\r\n        // Retrieve users in the chat, including teacher and students\r\n        const usersInChat = [this.currentRoom().teacher, ...this.currentRoom().classroomStudents];\r\n        const updatedUnreadCounts: any = {};\r\n        usersInChat.forEach((chatUser: any) => {\r\n          if (chatUser.aspUserId !== message.senderId) {\r\n            updatedUnreadCounts[chatUser.aspUserId] = 1; // Initialize unread count to 1 for new chat\r\n          }\r\n        });\r\n        updateData.unreadCounts = updatedUnreadCounts;\r\n\r\n        return unreadMessagechatRef.set(updateData).then(() => {\r\n          return messageRef.set(message).then(() => {\r\n            return messageRef.update({ id: messageRef.ref.id }).then(() => {\r\n              return this.updateChatLastMessage(unreadMessagechatRef, classroomId);\r\n            })\r\n          });\r\n        });\r\n      } else {\r\n        // Existing chat room, update last message and unread counts\r\n        return this.updateLastMessage(chatRef, message, user, false, messageId).then(() => {\r\n          return messageRef.set(message).then(() => {\r\n            return messageRef.update({ id: messageRef.ref.id });\r\n          })\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  private updateLastMessage(chatRef: AngularFirestoreDocument<any>,\r\n    updatedMessage: Message, user: any, isLastMessageDeleted = false, messageId = ''): Promise<void> {\r\n\r\n    const messageRef = chatRef.collection('messages').doc();\r\n    const updateData: any = {\r\n      lastMessage: updatedMessage.content,\r\n      lastMessageAt: serverTimestamp(),\r\n      lastMessageFrom: updatedMessage.senderId,\r\n      lastMessageFromImage: this.userService.getUserPhoto(user?.avatarUrl),\r\n      lastMessageFromName: this.generalService.getPersonFullName(user),\r\n      lastMessageIsFile: updatedMessage.isFileFromLibrary || updatedMessage.isUploadFile,\r\n      lastMessageDeleted: isLastMessageDeleted,\r\n      lastMessageId: messageId\r\n    };\r\n\r\n    return firstValueFrom(chatRef.get()).then(snapshot => {\r\n      console.log('current room', this.currentRoom());\r\n      const updatedUnreadCounts: any = {};\r\n      // Retrieve users in the chat, including teacher and students\r\n      const usersInChat = [this.currentRoom().teacher, ...this.currentRoom().classroomStudents];\r\n\r\n      usersInChat.forEach((chatUser: any) => {\r\n        if (chatUser.aspUserId !== updatedMessage.senderId) {\r\n          updatedUnreadCounts[chatUser.aspUserId] = (snapshot!.exists ? (snapshot!.data() as any).unreadCounts?.[chatUser.aspUserId] || 0 : 0) + 1;\r\n        }\r\n      });\r\n\r\n      updateData.unreadCounts = updatedUnreadCounts;\r\n      return chatRef.update(updateData);\r\n    });\r\n  }\r\n\r\n  editMessage(classroomId: string, messageId: string, updatedMessage: Message, user: any, updateLastMessage = false): Promise<void> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    const messageRef = chatRef.collection('messages').doc(`${messageId}`);\r\n    const messagesRef = chatRef.collection('messages', ref => ref.orderBy('timestamp', 'desc').limit(1));\r\n\r\n    const lastMessageDoc = messagesRef.ref.doc();\r\n    const lastMessageId = lastMessageDoc.id;\r\n    console.log(lastMessageId);\r\n    return messageRef.get().toPromise().then(snapshot => {\r\n      if (snapshot!.exists) {\r\n\r\n\r\n        return messageRef.update({\r\n          ...updatedMessage,\r\n        }).then(() => {\r\n          if (updateLastMessage) {\r\n            return this.updateLastMessage(chatRef, updatedMessage, user, false, updatedMessage.id!);\r\n          }\r\n          return Promise.resolve();\r\n        });\r\n      } else {\r\n        throw new Error('Message does not exist');\r\n      }\r\n    });\r\n  }\r\n\r\n  getLastUnreadMessagesCountForUser(classroomId: string, userId: string): Observable<number> {\r\n    const chatRef = this.unreadCountsRef.doc(`${classroomId}`);\r\n\r\n    return chatRef.valueChanges().pipe(\r\n      map(data => {\r\n        const unreadCounts = (data as any)?.unreadCounts;\r\n        return unreadCounts ? unreadCounts[userId] || 0 : 0;\r\n      })\r\n    );\r\n  }\r\n\r\n  markLastMessagesAsRead(classroomId: string, userId: string): Promise<void> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    return this.markLastMessagesAsReadInLastMessage(chatRef, userId);\r\n  }\r\n\r\n  markLastMessagesAsReadInLastMessage(chatRunreadCountsRefef: AngularFirestoreDocument<any>, userId: string): Promise<void> {\r\n    return chatRunreadCountsRefef.get().toPromise().then(snapshot => {\r\n      if (snapshot!.exists) {\r\n        const data = snapshot!.data() as any;\r\n        const currentCount = data.unreadCounts?.[userId] || 0;\r\n\r\n        // Only update if the unread count for the user is greater than zero\r\n        if (currentCount > 0) {\r\n          const updatedUnreadCounts = { ...data.unreadCounts, [userId]: 0 };\r\n          return chatRunreadCountsRefef.update({ unreadCounts: updatedUnreadCounts });\r\n        }\r\n      }\r\n      return Promise.resolve(); // If the document doesn't exist or no update is needed\r\n    });\r\n  }\r\n\r\n  // Delete a specific message from a chat room\r\n  deleteMessage(classroomId: string, message: any, user: any, updateLastMessage = false): Promise<void> {\r\n    console.log(classroomId);\r\n    console.log(message);\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    const messageRef = chatRef.collection('messages').doc(message.id);\r\n\r\n    return messageRef.delete().then(() => {\r\n      if (updateLastMessage) {\r\n        return this.getLatestMessage(classroomId).then(latestMessage => {\r\n          return this.updateLastMessage(chatRef, { ...message, content: 'Message Deleted' }, user, true, latestMessage?.id).then(() => {\r\n            // return this.updateChatLastMessage(chatRef);\r\n          });\r\n        })\r\n\r\n      } else {\r\n        return this.updateChatLastMessage(chatRef, classroomId);\r\n      }\r\n    });\r\n  }\r\n\r\n  private updateChatLastMessage(chatRef: AngularFirestoreDocument<any>, classroomId: string): Promise<void> {\r\n    return this.getLatestMessage(classroomId).then((snapshot: any | null) => {\r\n\r\n      console.log(snapshot);\r\n      if (snapshot) {\r\n        const latestMessage = snapshot;\r\n        return chatRef.update({\r\n          lastMessage: latestMessage.content,\r\n          lastMessageAt: latestMessage.createdAt,\r\n          lastMessageFrom: latestMessage.senderId,\r\n          lastMessageFromName: latestMessage.senderName,\r\n          lastMessageFromImage: latestMessage.senderImage,\r\n          lastMessageIsFile: latestMessage.isFileFromLibrary || latestMessage.isUploadFile,\r\n          lastMessageDeleted: false,\r\n          lastMessageId: latestMessage.id,\r\n        });\r\n      } else {\r\n        // Handle case where there are no messages left in the collection\r\n        return chatRef.update({\r\n          lastMessage: '',\r\n          lastMessageAt: null,\r\n          lastMessageFrom: '',\r\n          lastMessageFromName: '',\r\n          lastMessageFromImage: '',\r\n          lastMessageIsFile: false,\r\n          lastMessageDeleted: false,\r\n          lastMessageId: '',\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  getLatestMessage(classroomId: string): Promise<Message | null> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    return firstValueFrom(chatRef.collection('messages', ref => ref.orderBy('timestamp', 'desc').limit(1)).get()).then(snapshot => {\r\n      if (!snapshot.empty) {\r\n        const latestMessage = snapshot.docs[0].data() as Message;\r\n        return latestMessage;\r\n      } else {\r\n        return null; // No messages found\r\n      }\r\n    });\r\n  }\r\n\r\n  // Update a chat room document\r\n  updateChatRoom(classroomId: string, data: Partial<Chat>): Promise<void> {\r\n    return this.chatsRef.doc(classroomId).update(data);\r\n  }\r\n\r\n  // Delete a chat room document\r\n  deleteChatRoom(classroomId: string): Promise<void> {\r\n    return this.chatsRef.doc(classroomId).delete();\r\n  }\r\n\r\n  addUnreadMessage(classroomId: string, message: Message): Promise<void> {\r\n    const messageRef = this.db.collection(`${this.dbUnreadPath}/${classroomId}/messages`).doc(message.id);\r\n\r\n    // Set the initial readStatus for the new message\r\n    const initialReadStatus = {\r\n      // Initialize the readStatus for all users, defaulting to false (unread)\r\n      [message.senderId]: false, // Mark the sender as false (unread)\r\n      // You can add more user IDs here if needed, depending on your application logic\r\n    };\r\n\r\n    return messageRef.set({\r\n      readStatus: initialReadStatus, // Add the readStatus field\r\n      timestamp: serverTimestamp(), // Ensure there's a timestamp for ordering\r\n    });\r\n  }\r\n\r\n  markMessagesAsRead(classroomId: string, userId: string): Promise<void> {\r\n    return this.db.collection(`${this.dbUnreadPath}/${classroomId}/messages`).get().toPromise().then(querySnapshot => {\r\n      const batch = this.db.firestore.batch();\r\n      let hasUpdates = false; // Flag to track if there are any updates\r\n\r\n      querySnapshot!.forEach(doc => {\r\n        const data = doc.data();\r\n\r\n        // Ensure readStatus is a valid map or initialize it\r\n        const readStatusUpdate = (data as any).readStatus || {};\r\n\r\n        // Only update if the userId is not already marked as read\r\n        if (!readStatusUpdate[userId]) {\r\n          readStatusUpdate[userId] = true;\r\n          // Update the document with the new or modified readStatus map\r\n          batch.update(doc.ref, { readStatus: readStatusUpdate });\r\n          hasUpdates = true; // Set the flag to true if an update is made\r\n        }\r\n      });\r\n\r\n      // Only commit the batch if there are updates\r\n      if (hasUpdates) {\r\n        return batch.commit();\r\n      }\r\n\r\n      // If no updates were made, resolve immediately\r\n      return Promise.resolve();\r\n    });\r\n  }\r\n\r\n  // Mark messages as read\r\n  markAllMessagesAsRead(classroomId: string): Promise<void> {\r\n    return this.db.collection(`${this.dbUnreadPath}/${classroomId}/messages`).get().toPromise().then(querySnapshot => {\r\n      const batch = this.db.firestore.batch();\r\n      querySnapshot!.forEach(doc => {\r\n        const messageData = doc.data() as Message;\r\n        const updatedReadStatus = {};\r\n\r\n        // Set readStatus for all users in the message's readStatus map to true\r\n        for (const userId in messageData.readStatus) {\r\n          if (messageData.readStatus.hasOwnProperty(userId)) {\r\n            const a = `readStatus.${userId}`;\r\n            (updatedReadStatus as any)[a] = true;\r\n          }\r\n        }\r\n\r\n        batch.update(doc.ref, updatedReadStatus);\r\n      });\r\n      return batch.commit();\r\n    });\r\n  }\r\n\r\n  // Get unread messages count for a user in a classroom\r\n  getUnreadMessagesCount(classroomId: string, userId: string): Observable<number> {\r\n    return this.db.collection<Message>(`${this.dbUnreadPath}/${classroomId}/messages`).snapshotChanges().pipe(\r\n      map(actions => actions.filter(action => {\r\n        const data = action.payload.doc.data() as Message;\r\n        return !data.readStatus.hasOwnProperty(userId);\r\n      }).length)\r\n    );\r\n  }\r\n\r\n\r\n  // Get total unread messages count for a user across all classrooms\r\n  getUnreadMessagesCountForUser(userId: string): Observable<number> {\r\n    return this.chatsRef.snapshotChanges().pipe(\r\n      switchMap(chatSnapshots => {\r\n        const chatObservables = chatSnapshots.map(snapshot => {\r\n          const chatId = snapshot.payload.doc.id;\r\n          return this.getUnreadMessagesCount(chatId, userId);\r\n        });\r\n        return combineLatest(chatObservables);\r\n      }),\r\n      map(unreadCounts => unreadCounts.reduce((acc, count) => acc + count, 0))\r\n    );\r\n  }\r\n\r\n  // Or, to get the user object directly\r\n  getFireBaseUser(): Observable<any> {\r\n    return this.afAuth.authState;\r\n  }\r\n\r\n  setUserStatus(userId: string, status: 'online' | 'offline'): Promise<void> {\r\n    const userRef = this.usersRef.doc(userId);\r\n    return userRef.set({ status, lastOnlineTime: Timestamp.now() }, { merge: true });\r\n  }\r\n\r\n  setUserLastOnlineTime(userId: string, time: FieldValue): Promise<void> {\r\n    const userRef = this.usersRef.doc(userId);\r\n    return userRef.set({ status: 'online', lastOnlineTime: time }, { merge: true });\r\n  }\r\n\r\n  getUserStatus(userId: string): Observable<'online' | 'offline'> {\r\n    return this.usersRef.doc<ChatUserStatus>(userId).valueChanges().pipe(\r\n      map(user => user?.status || 'offline')\r\n    );\r\n  }\r\n\r\n  getOnlineUsers(): Observable<ChatUserStatus[]> {\r\n    return this.db.collection<ChatUserStatus>('/users', ref => ref.where('status', '==', 'online'))\r\n      .snapshotChanges().pipe(\r\n        map(actions => actions.map(a => {\r\n          const data = a.payload.doc.data() as ChatUserStatus;\r\n          const id = a.payload.doc.id;\r\n          return { ...data, id }; // Corrected merge of id and data\r\n        }))\r\n      );\r\n  }\r\n\r\n  /**\r\n   * Retrieves all users in the Firestore 'users' collection, including their status compared to the last online time.\r\n   * The status is determined by comparing the current time with the user's last online time. If the user was online\r\n   * within the last `secondsToCheckOnlineStatus` seconds, the status is set to 'online', otherwise it is set to 'offline'.\r\n   * If the user does not have a last online time, the status is set to 'offline'.\r\n   * @returns An Observable of an array of ChatUserStatus objects, where each object contains the user's id, status, and last online time.\r\n   */\r\n  getOnlineUsersComparedToLastOnlineTime(): Observable<ChatUserStatus[]> {\r\n    return this.db.collection<ChatUserStatus>('/users')\r\n      .snapshotChanges().pipe(\r\n        map(actions => {\r\n          const currentTime = Timestamp.now();\r\n          return actions.map(a => {\r\n            const data = a.payload.doc.data() as ChatUserStatus;\r\n            const id = a.payload.doc.id;\r\n            const lastOnlineTime = data.lastOnlineTime as Timestamp;\r\n            if (lastOnlineTime) {\r\n              if (!currentTime.seconds || !lastOnlineTime.seconds) {\r\n                return { ...data, id, status: 'offline' };\r\n              }\r\n              const timeDifference = currentTime?.seconds - lastOnlineTime?.seconds;\r\n\r\n              // Check if the user was online within the last 10 seconds\r\n              if (timeDifference <= this.secondsToCheckOnlineStatus) {\r\n                return { ...data, id, status: 'online' };\r\n              } else {\r\n                return { ...data, id, status: 'offline' };\r\n              }\r\n            }\r\n            return { ...data, id, status: 'offline' };\r\n\r\n          });\r\n        })\r\n      );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAAA,SAAqBA,MAAM,EAAEC,MAAM,QAAQ,eAAe;AAG1D,SAAqBC,eAAe,EAAEC,SAAS,QAAQ,oBAAoB;AAC3E,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,UAAU,EAAEC,aAAa,EAASC,cAAc,QAAwB,MAAM;AACvF,SAASC,GAAG,EAAEC,SAAS,QAAQ,gBAAgB;AAC/C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,WAAW,QAAQ,8BAA8B;AAG1D,SAASC,OAAO,EAAEC,iBAAiB,QAAQ,eAAe;;;;;AAF1D,MAAMC,WAAW,GAAGH,WAAW,CAACI,MAAM;AACtC,MAAMC,eAAe,GAAGL,WAAW,CAACI,MAAM,GAAG,OAAO;AAsCpD,OAAM,MAAOE,WAAW;EAiBtBC,YAAoBC,EAAoB,EAAUC,MAAuB,EAAUC,IAAgB;IAA/E,KAAAF,EAAE,GAAFA,EAAE;IAA4B,KAAAC,MAAM,GAANA,MAAM;IAA2B,KAAAC,IAAI,GAAJA,IAAI;IAhBvF,KAAAC,WAAW,GAAGvB,MAAM,CAACU,WAAW,CAAC;IACjC,KAAAc,cAAc,GAAGxB,MAAM,CAACW,cAAc,CAAC;IACvC,KAAAc,aAAa,GAAGxB,MAAM,CAACW,WAAW,CAACa,aAAa,CAAC;IACjD,KAAAC,cAAc,GAAGzB,MAAM,CAAC,KAAK,CAAC;IAC9B,KAAA0B,WAAW,GAAG1B,MAAM,CAAC,CAAC,CAAC;IACvB,KAAA2B,WAAW,GAAG3B,MAAM,CAAC,EAAS,CAAC;IAC/B,KAAA4B,cAAc,GAAG5B,MAAM,CAAC,KAAK,CAAC;IACtB,KAAA6B,MAAM,GAAG,QAAQ;IACjB,KAAAC,YAAY,GAAG,QAAQ;IAGvB,KAAAC,QAAQ,GAA+C,IAAI,CAACZ,EAAE,CAACa,UAAU,CAAC,OAAO,CAAC;IAC1F,KAAAC,SAAS,GAAG,CAAC;IACJ,KAAAC,0BAA0B,GAAG,EAAE;IAItC,IAAI,CAACC,QAAQ,GAAGhB,EAAE,CAACa,UAAU,CAAC,IAAI,CAACH,MAAM,CAAC;IAC1C,IAAI,CAACE,QAAQ,GAAGZ,EAAE,CAACa,UAAU,CAAC,QAAQ,CAAC;IACvC,IAAI,CAACI,eAAe,GAAGjB,EAAE,CAACa,UAAU,CAAC,IAAI,CAACF,YAAY,CAAC;EACzD;EAGAO,gCAAgCA,CAACC,OAAY;IAC3CC,OAAO,CAACC,GAAG,CAACF,OAAO,CAAC;IACpB,OAAO,IAAI,CAACjB,IAAI,CAACoB,IAAI,CAAMzB,eAAe,GAAG,2CAA2C,GAAGsB,OAAO,EAAE,EAAE,CAAC;EACzG;EAEA;EACAzB,iBAAiBA,CAAA;IACf,OAAO,IAAIT,UAAU,CAACsC,QAAQ,IAAG;MAC/B,IAAI,CAACtB,MAAM,CAACP,iBAAiB,EAAE,CAC5B8B,IAAI,CAACC,cAAc,IAAG;QACrBL,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEI,cAAc,CAACC,IAAI,CAAC;QAC1DH,QAAQ,CAACI,IAAI,CAACF,cAAc,CAAC;QAC7BF,QAAQ,CAACK,QAAQ,EAAE;MACrB,CAAC,CAAC,CACDC,KAAK,CAACC,KAAK,IAAG;QACbV,OAAO,CAACU,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrDP,QAAQ,CAACO,KAAK,CAACA,KAAK,CAAC;MACvB,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;EAEAC,YAAYA,CAAA;IACV,MAAMC,IAAI,GAAGvC,OAAO,EAAE;IACtBC,iBAAiB,CAACsC,IAAI,CAAC,CACpBR,IAAI,CAAC,MAAK;MACT;IAAA,CACD,CAAC,CACDK,KAAK,CAAEC,KAAK,IAAI;MACf,MAAMG,SAAS,GAAGH,KAAK,CAACI,IAAI;MAC5B,MAAMC,YAAY,GAAGL,KAAK,CAACM,OAAO;MAClC;IACF,CAAC,CAAC;EACN;EAEAC,cAAcA,CAACC,WAAmB;IAChC,MAAMC,OAAO,GAAG,IAAI,CAACvB,QAAQ,CAACwB,GAAG,CAAU,GAAGF,WAAW,GAAG,CAAC;IAE7D,OAAOC,OAAO,CAACE,eAAe,EAAE,CAACC,IAAI,CACnCtD,GAAG,CAACuD,MAAM,IAAG;MACX,MAAMC,IAAI,GAAGD,MAAM,CAACE,OAAO,CAACD,IAAI,EAAa;MAC7C,MAAME,EAAE,GAAGH,MAAM,CAACE,OAAO,CAACC,EAAE;MAC5B,OAAO;QAAEA,EAAE;QAAE,GAAGF;MAAI,CAAE;IACxB,CAAC,CAAC,CACH;EACH;EAEA;EACAG,iBAAiBA,CAACT,WAAmB;IACnC,MAAMC,OAAO,GAAG,IAAI,CAACvB,QAAQ,CAACwB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IACnD,OAAO,IAAI,CAACtB,QAAQ,CAACwB,GAAG,CAACF,WAAW,CAAC,CAACzB,UAAU,CAAO,OAAO,CAAC,CAC5D4B,eAAe,EAAE,CAACC,IAAI,CACrBtD,GAAG,CAAC4D,OAAO,IAAIA,OAAO,CAAC5D,GAAG,CAAC6D,CAAC,IAAG;MAC7B,MAAML,IAAI,GAAGK,CAAC,CAACJ,OAAO,CAACL,GAAG,CAACI,IAAI,EAAU;MACzC,MAAME,EAAE,GAAGG,CAAC,CAACJ,OAAO,CAACL,GAAG,CAACM,EAAE;MAC3B,OAAO;QAAEA,EAAE;QAAE,GAAGF;MAAI,CAAE;IACxB,CAAC,CAAC,CAAC,CACJ;EACL;EAEA;EACAM,eAAeA,CAACC,MAAc,EAAEC,KAAa,EAAEC,WAAqB;IAClE,MAAMC,aAAa,GAAG,IAAI,CAACtD,EAAE,CAACa,UAAU,CAAU,GAAG,IAAI,CAACH,MAAM,IAAIyC,MAAM,WAAW,EAAEI,GAAG,IAAG;MAC3F,IAAIC,KAAK,GAAGD,GAAG,CAACE,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAACL,KAAK,CAACA,KAAK,CAAC;MACzD,IAAIC,WAAW,EAAE;QACfG,KAAK,GAAGA,KAAK,CAACE,UAAU,CAACL,WAAW,CAACM,SAAS,CAAC;MACjD;MACA,OAAOH,KAAK;IACd,CAAC,CAAC;IAEF,OAAOF,aAAa,CAACb,eAAe,EAAE,CAACC,IAAI,CACzCtD,GAAG,CAAC4D,OAAO,IAAIA,OAAO,CAAC5D,GAAG,CAAC6D,CAAC,IAAG;MAC7B,MAAML,IAAI,GAAGK,CAAC,CAACJ,OAAO,CAACL,GAAG,CAACI,IAAI,EAAa;MAC5C,MAAME,EAAE,GAAGG,CAAC,CAACJ,OAAO,CAACL,GAAG,CAACM,EAAE;MAC3B,OAAO;QAAEA,EAAE;QAAE,GAAGF;MAAI,CAAE;IACxB,CAAC,CAAC,CAAC,CACJ;EACH;EAEA;EACAgB,cAAcA,CAACtB,WAAmB,EAAEuB,YAAsB;IACxD,MAAMC,IAAI,GAAS;MACjBxB,WAAW,EAAEA,WAAW;MACxByB,SAAS,EAAE/E,MAAM,EAAE,CAACgF,MAAM,CAAC,qBAAqB,CAAC;MACjDX,WAAW,EAAE,EAAE;MACfY,aAAa,EAAEnF,eAAe,EAAE;MAChCoF,eAAe,EAAE,EAAE;MACnBC,mBAAmB,EAAE,EAAE;MACvBC,oBAAoB,EAAE,EAAE;MACxBC,iBAAiB,EAAE,KAAK;MACxBC,kBAAkB,EAAE,KAAK;MACzBC,aAAa,EAAE,EAAE;MACjBC,sBAAsB,EAAE;KACzB;IACD,OAAO,IAAI,CAACxD,QAAQ,CAACyD,GAAG,CAACX,IAAI,CAAC;EAChC;EAEA;EACAY,UAAUA,CAACpC,WAAmB,EAAEF,OAAgB,EAAEV,IAAS;IACzD,MAAMa,OAAO,GAAG,IAAI,CAACvB,QAAQ,CAACwB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IACnD,MAAMqC,UAAU,GAAGpC,OAAO,CAAC1B,UAAU,CAAC,UAAU,CAAC,CAAC2B,GAAG,EAAE;IAGvD,MAAMoC,oBAAoB,GAAG,IAAI,CAAC3D,eAAe,CAACuB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IACvE,MAAMuC,8BAA8B,GAAGD,oBAAoB,CAAC/D,UAAU,CAAC,UAAU,CAAC,CAAC2B,GAAG,EAAE;IAExF,MAAMsC,SAAS,GAAIH,UAAU,CAACpB,GAAG,CAACT,EAAG;IACrC,OAAO3D,cAAc,CAACoD,OAAO,CAACwC,GAAG,EAAE,CAAC,CAACvD,IAAI,CAACwD,QAAQ,IAAG;MACnD,MAAMC,UAAU,GAAQ;QACtBlB,SAAS,EAAE/E,MAAM,EAAE,CAACgF,MAAM,CAAC,qBAAqB,CAAC;QACjDX,WAAW,EAAEjB,OAAO,CAAC8C,OAAO;QAC5BjB,aAAa,EAAEnF,eAAe,EAAE;QAChCwD,WAAW,EAAEA,WAAW;QACxB4B,eAAe,EAAE9B,OAAO,CAAC+C,QAAQ;QACjChB,mBAAmB,EAAE,IAAI,CAAC/D,cAAc,CAACgF,iBAAiB,CAAC1D,IAAI,CAAC;QAChE0C,oBAAoB,EAAE,IAAI,CAACjE,WAAW,CAACkF,YAAY,CAAC3D,IAAI,EAAE4D,SAAS,CAAC;QACpEjB,iBAAiB,EAAEjC,OAAO,CAACmD,iBAAiB,IAAInD,OAAO,CAACoD,YAAY;QACpElB,kBAAkB,EAAE,KAAK;QACzBC,aAAa,EAAEO,SAAS;QAAE;QAC1BN,sBAAsB,EAAE;OACzB;MAED,IAAI,CAACQ,QAAQ,CAACS,MAAM,EAAE;QACpB;QACA,MAAMC,WAAW,GAAG,CAAC,IAAI,CAAClF,WAAW,EAAE,CAACmF,OAAO,EAAE,GAAG,IAAI,CAACnF,WAAW,EAAE,CAACoF,iBAAiB,CAAC;QACzF,MAAMC,mBAAmB,GAAQ,EAAE;QACnCH,WAAW,CAACI,OAAO,CAAEC,QAAa,IAAI;UACpC,IAAIA,QAAQ,CAACC,SAAS,KAAK5D,OAAO,CAAC+C,QAAQ,EAAE;YAC3CU,mBAAmB,CAACE,QAAQ,CAACC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;UAC/C;QACF,CAAC,CAAC;QACFf,UAAU,CAACgB,YAAY,GAAGJ,mBAAmB;QAE7C,OAAOjB,oBAAoB,CAACsB,GAAG,CAACjB,UAAU,CAAC,CAACzD,IAAI,CAAC,MAAK;UACpD,OAAOmD,UAAU,CAACuB,GAAG,CAAC9D,OAAO,CAAC,CAACZ,IAAI,CAAC,MAAK;YACvC,OAAOmD,UAAU,CAACwB,MAAM,CAAC;cAAErD,EAAE,EAAE6B,UAAU,CAACpB,GAAG,CAACT;YAAE,CAAE,CAAC,CAACtB,IAAI,CAAC,MAAK;cAC5D,OAAO,IAAI,CAAC4E,qBAAqB,CAACxB,oBAAoB,EAAEtC,WAAW,CAAC;YACtE,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,OAAO,IAAI,CAAC+D,iBAAiB,CAAC9D,OAAO,EAAEH,OAAO,EAAEV,IAAI,EAAE,KAAK,EAAEoD,SAAS,CAAC,CAACtD,IAAI,CAAC,MAAK;UAChF,OAAOmD,UAAU,CAACuB,GAAG,CAAC9D,OAAO,CAAC,CAACZ,IAAI,CAAC,MAAK;YACvC,OAAOmD,UAAU,CAACwB,MAAM,CAAC;cAAErD,EAAE,EAAE6B,UAAU,CAACpB,GAAG,CAACT;YAAE,CAAE,CAAC;UACrD,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EAEQuD,iBAAiBA,CAAC9D,OAAsC,EAC9D+D,cAAuB,EAAE5E,IAAS,EAAE6E,oBAAoB,GAAG,KAAK,EAAEzB,SAAS,GAAG,EAAE;IAEhF,MAAMH,UAAU,GAAGpC,OAAO,CAAC1B,UAAU,CAAC,UAAU,CAAC,CAAC2B,GAAG,EAAE;IACvD,MAAMyC,UAAU,GAAQ;MACtB5B,WAAW,EAAEiD,cAAc,CAACpB,OAAO;MACnCjB,aAAa,EAAEnF,eAAe,EAAE;MAChCoF,eAAe,EAAEoC,cAAc,CAACnB,QAAQ;MACxCf,oBAAoB,EAAE,IAAI,CAACjE,WAAW,CAACkF,YAAY,CAAC3D,IAAI,EAAE4D,SAAS,CAAC;MACpEnB,mBAAmB,EAAE,IAAI,CAAC/D,cAAc,CAACgF,iBAAiB,CAAC1D,IAAI,CAAC;MAChE2C,iBAAiB,EAAEiC,cAAc,CAACf,iBAAiB,IAAIe,cAAc,CAACd,YAAY;MAClFlB,kBAAkB,EAAEiC,oBAAoB;MACxChC,aAAa,EAAEO;KAChB;IAED,OAAO3F,cAAc,CAACoD,OAAO,CAACwC,GAAG,EAAE,CAAC,CAACvD,IAAI,CAACwD,QAAQ,IAAG;MACnD5D,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAACb,WAAW,EAAE,CAAC;MAC/C,MAAMqF,mBAAmB,GAAQ,EAAE;MACnC;MACA,MAAMH,WAAW,GAAG,CAAC,IAAI,CAAClF,WAAW,EAAE,CAACmF,OAAO,EAAE,GAAG,IAAI,CAACnF,WAAW,EAAE,CAACoF,iBAAiB,CAAC;MAEzFF,WAAW,CAACI,OAAO,CAAEC,QAAa,IAAI;QACpC,IAAIA,QAAQ,CAACC,SAAS,KAAKM,cAAc,CAACnB,QAAQ,EAAE;UAClDU,mBAAmB,CAACE,QAAQ,CAACC,SAAS,CAAC,GAAG,CAAChB,QAAS,CAACS,MAAM,GAAIT,QAAS,CAACpC,IAAI,EAAU,CAACqD,YAAY,GAAGF,QAAQ,CAACC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;QAC1I;MACF,CAAC,CAAC;MAEFf,UAAU,CAACgB,YAAY,GAAGJ,mBAAmB;MAC7C,OAAOtD,OAAO,CAAC4D,MAAM,CAAClB,UAAU,CAAC;IACnC,CAAC,CAAC;EACJ;EAEAuB,WAAWA,CAAClE,WAAmB,EAAEwC,SAAiB,EAAEwB,cAAuB,EAAE5E,IAAS,EAAE2E,iBAAiB,GAAG,KAAK;IAC/G,MAAM9D,OAAO,GAAG,IAAI,CAACvB,QAAQ,CAACwB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IACnD,MAAMqC,UAAU,GAAGpC,OAAO,CAAC1B,UAAU,CAAC,UAAU,CAAC,CAAC2B,GAAG,CAAC,GAAGsC,SAAS,EAAE,CAAC;IACrE,MAAM2B,WAAW,GAAGlE,OAAO,CAAC1B,UAAU,CAAC,UAAU,EAAE0C,GAAG,IAAIA,GAAG,CAACE,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAACL,KAAK,CAAC,CAAC,CAAC,CAAC;IAEpG,MAAMsD,cAAc,GAAGD,WAAW,CAAClD,GAAG,CAACf,GAAG,EAAE;IAC5C,MAAM+B,aAAa,GAAGmC,cAAc,CAAC5D,EAAE;IACvC1B,OAAO,CAACC,GAAG,CAACkD,aAAa,CAAC;IAC1B,OAAOI,UAAU,CAACI,GAAG,EAAE,CAAC4B,SAAS,EAAE,CAACnF,IAAI,CAACwD,QAAQ,IAAG;MAClD,IAAIA,QAAS,CAACS,MAAM,EAAE;QAGpB,OAAOd,UAAU,CAACwB,MAAM,CAAC;UACvB,GAAGG;SACJ,CAAC,CAAC9E,IAAI,CAAC,MAAK;UACX,IAAI6E,iBAAiB,EAAE;YACrB,OAAO,IAAI,CAACA,iBAAiB,CAAC9D,OAAO,EAAE+D,cAAc,EAAE5E,IAAI,EAAE,KAAK,EAAE4E,cAAc,CAACxD,EAAG,CAAC;UACzF;UACA,OAAO8D,OAAO,CAACC,OAAO,EAAE;QAC1B,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;MAC3C;IACF,CAAC,CAAC;EACJ;EAEAC,iCAAiCA,CAACzE,WAAmB,EAAE0E,MAAc;IACnE,MAAMzE,OAAO,GAAG,IAAI,CAACtB,eAAe,CAACuB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IAE1D,OAAOC,OAAO,CAAC0E,YAAY,EAAE,CAACvE,IAAI,CAChCtD,GAAG,CAACwD,IAAI,IAAG;MACT,MAAMqD,YAAY,GAAIrD,IAAY,EAAEqD,YAAY;MAChD,OAAOA,YAAY,GAAGA,YAAY,CAACe,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;IACrD,CAAC,CAAC,CACH;EACH;EAEAE,sBAAsBA,CAAC5E,WAAmB,EAAE0E,MAAc;IACxD,MAAMzE,OAAO,GAAG,IAAI,CAACvB,QAAQ,CAACwB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IACnD,OAAO,IAAI,CAAC6E,mCAAmC,CAAC5E,OAAO,EAAEyE,MAAM,CAAC;EAClE;EAEAG,mCAAmCA,CAACC,sBAAqD,EAAEJ,MAAc;IACvG,OAAOI,sBAAsB,CAACrC,GAAG,EAAE,CAAC4B,SAAS,EAAE,CAACnF,IAAI,CAACwD,QAAQ,IAAG;MAC9D,IAAIA,QAAS,CAACS,MAAM,EAAE;QACpB,MAAM7C,IAAI,GAAGoC,QAAS,CAACpC,IAAI,EAAS;QACpC,MAAMyE,YAAY,GAAGzE,IAAI,CAACqD,YAAY,GAAGe,MAAM,CAAC,IAAI,CAAC;QAErD;QACA,IAAIK,YAAY,GAAG,CAAC,EAAE;UACpB,MAAMxB,mBAAmB,GAAG;YAAE,GAAGjD,IAAI,CAACqD,YAAY;YAAE,CAACe,MAAM,GAAG;UAAC,CAAE;UACjE,OAAOI,sBAAsB,CAACjB,MAAM,CAAC;YAAEF,YAAY,EAAEJ;UAAmB,CAAE,CAAC;QAC7E;MACF;MACA,OAAOe,OAAO,CAACC,OAAO,EAAE,CAAC,CAAC;IAC5B,CAAC,CAAC;EACJ;EAEA;EACAS,aAAaA,CAAChF,WAAmB,EAAEF,OAAY,EAAEV,IAAS,EAAE2E,iBAAiB,GAAG,KAAK;IACnFjF,OAAO,CAACC,GAAG,CAACiB,WAAW,CAAC;IACxBlB,OAAO,CAACC,GAAG,CAACe,OAAO,CAAC;IACpB,MAAMG,OAAO,GAAG,IAAI,CAACvB,QAAQ,CAACwB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IACnD,MAAMqC,UAAU,GAAGpC,OAAO,CAAC1B,UAAU,CAAC,UAAU,CAAC,CAAC2B,GAAG,CAACJ,OAAO,CAACU,EAAE,CAAC;IAEjE,OAAO6B,UAAU,CAAC4C,MAAM,EAAE,CAAC/F,IAAI,CAAC,MAAK;MACnC,IAAI6E,iBAAiB,EAAE;QACrB,OAAO,IAAI,CAACmB,gBAAgB,CAAClF,WAAW,CAAC,CAACd,IAAI,CAACiG,aAAa,IAAG;UAC7D,OAAO,IAAI,CAACpB,iBAAiB,CAAC9D,OAAO,EAAE;YAAE,GAAGH,OAAO;YAAE8C,OAAO,EAAE;UAAiB,CAAE,EAAExD,IAAI,EAAE,IAAI,EAAE+F,aAAa,EAAE3E,EAAE,CAAC,CAACtB,IAAI,CAAC,MAAK;YAC1H;UAAA,CACD,CAAC;QACJ,CAAC,CAAC;MAEJ,CAAC,MAAM;QACL,OAAO,IAAI,CAAC4E,qBAAqB,CAAC7D,OAAO,EAAED,WAAW,CAAC;MACzD;IACF,CAAC,CAAC;EACJ;EAEQ8D,qBAAqBA,CAAC7D,OAAsC,EAAED,WAAmB;IACvF,OAAO,IAAI,CAACkF,gBAAgB,CAAClF,WAAW,CAAC,CAACd,IAAI,CAAEwD,QAAoB,IAAI;MAEtE5D,OAAO,CAACC,GAAG,CAAC2D,QAAQ,CAAC;MACrB,IAAIA,QAAQ,EAAE;QACZ,MAAMyC,aAAa,GAAGzC,QAAQ;QAC9B,OAAOzC,OAAO,CAAC4D,MAAM,CAAC;UACpB9C,WAAW,EAAEoE,aAAa,CAACvC,OAAO;UAClCjB,aAAa,EAAEwD,aAAa,CAAC1D,SAAS;UACtCG,eAAe,EAAEuD,aAAa,CAACtC,QAAQ;UACvChB,mBAAmB,EAAEsD,aAAa,CAACC,UAAU;UAC7CtD,oBAAoB,EAAEqD,aAAa,CAACE,WAAW;UAC/CtD,iBAAiB,EAAEoD,aAAa,CAAClC,iBAAiB,IAAIkC,aAAa,CAACjC,YAAY;UAChFlB,kBAAkB,EAAE,KAAK;UACzBC,aAAa,EAAEkD,aAAa,CAAC3E;SAC9B,CAAC;MACJ,CAAC,MAAM;QACL;QACA,OAAOP,OAAO,CAAC4D,MAAM,CAAC;UACpB9C,WAAW,EAAE,EAAE;UACfY,aAAa,EAAE,IAAI;UACnBC,eAAe,EAAE,EAAE;UACnBC,mBAAmB,EAAE,EAAE;UACvBC,oBAAoB,EAAE,EAAE;UACxBC,iBAAiB,EAAE,KAAK;UACxBC,kBAAkB,EAAE,KAAK;UACzBC,aAAa,EAAE;SAChB,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EAEAiD,gBAAgBA,CAAClF,WAAmB;IAClC,MAAMC,OAAO,GAAG,IAAI,CAACvB,QAAQ,CAACwB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IACnD,OAAOnD,cAAc,CAACoD,OAAO,CAAC1B,UAAU,CAAC,UAAU,EAAE0C,GAAG,IAAIA,GAAG,CAACE,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAACL,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC2B,GAAG,EAAE,CAAC,CAACvD,IAAI,CAACwD,QAAQ,IAAG;MAC5H,IAAI,CAACA,QAAQ,CAAC4C,KAAK,EAAE;QACnB,MAAMH,aAAa,GAAGzC,QAAQ,CAAC6C,IAAI,CAAC,CAAC,CAAC,CAACjF,IAAI,EAAa;QACxD,OAAO6E,aAAa;MACtB,CAAC,MAAM;QACL,OAAO,IAAI,CAAC,CAAC;MACf;IACF,CAAC,CAAC;EACJ;EAEA;EACAK,cAAcA,CAACxF,WAAmB,EAAEM,IAAmB;IACrD,OAAO,IAAI,CAAC5B,QAAQ,CAACwB,GAAG,CAACF,WAAW,CAAC,CAAC6D,MAAM,CAACvD,IAAI,CAAC;EACpD;EAEA;EACAmF,cAAcA,CAACzF,WAAmB;IAChC,OAAO,IAAI,CAACtB,QAAQ,CAACwB,GAAG,CAACF,WAAW,CAAC,CAACiF,MAAM,EAAE;EAChD;EAEAS,gBAAgBA,CAAC1F,WAAmB,EAAEF,OAAgB;IACpD,MAAMuC,UAAU,GAAG,IAAI,CAAC3E,EAAE,CAACa,UAAU,CAAC,GAAG,IAAI,CAACF,YAAY,IAAI2B,WAAW,WAAW,CAAC,CAACE,GAAG,CAACJ,OAAO,CAACU,EAAE,CAAC;IAErG;IACA,MAAMmF,iBAAiB,GAAG;MACxB;MACA,CAAC7F,OAAO,CAAC+C,QAAQ,GAAG,KAAK,CAAE;MAC3B;KACD;IAED,OAAOR,UAAU,CAACuB,GAAG,CAAC;MACpBgC,UAAU,EAAED,iBAAiB;MAAE;MAC/BtE,SAAS,EAAE7E,eAAe,EAAE,CAAE;KAC/B,CAAC;EACJ;EAEAqJ,kBAAkBA,CAAC7F,WAAmB,EAAE0E,MAAc;IACpD,OAAO,IAAI,CAAChH,EAAE,CAACa,UAAU,CAAC,GAAG,IAAI,CAACF,YAAY,IAAI2B,WAAW,WAAW,CAAC,CAACyC,GAAG,EAAE,CAAC4B,SAAS,EAAE,CAACnF,IAAI,CAAC4G,aAAa,IAAG;MAC/G,MAAMC,KAAK,GAAG,IAAI,CAACrI,EAAE,CAACsI,SAAS,CAACD,KAAK,EAAE;MACvC,IAAIE,UAAU,GAAG,KAAK,CAAC,CAAC;MAExBH,aAAc,CAACtC,OAAO,CAACtD,GAAG,IAAG;QAC3B,MAAMI,IAAI,GAAGJ,GAAG,CAACI,IAAI,EAAE;QAEvB;QACA,MAAM4F,gBAAgB,GAAI5F,IAAY,CAACsF,UAAU,IAAI,EAAE;QAEvD;QACA,IAAI,CAACM,gBAAgB,CAACxB,MAAM,CAAC,EAAE;UAC7BwB,gBAAgB,CAACxB,MAAM,CAAC,GAAG,IAAI;UAC/B;UACAqB,KAAK,CAAClC,MAAM,CAAC3D,GAAG,CAACe,GAAG,EAAE;YAAE2E,UAAU,EAAEM;UAAgB,CAAE,CAAC;UACvDD,UAAU,GAAG,IAAI,CAAC,CAAC;QACrB;MACF,CAAC,CAAC;MAEF;MACA,IAAIA,UAAU,EAAE;QACd,OAAOF,KAAK,CAACI,MAAM,EAAE;MACvB;MAEA;MACA,OAAO7B,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA;EACA6B,qBAAqBA,CAACpG,WAAmB;IACvC,OAAO,IAAI,CAACtC,EAAE,CAACa,UAAU,CAAC,GAAG,IAAI,CAACF,YAAY,IAAI2B,WAAW,WAAW,CAAC,CAACyC,GAAG,EAAE,CAAC4B,SAAS,EAAE,CAACnF,IAAI,CAAC4G,aAAa,IAAG;MAC/G,MAAMC,KAAK,GAAG,IAAI,CAACrI,EAAE,CAACsI,SAAS,CAACD,KAAK,EAAE;MACvCD,aAAc,CAACtC,OAAO,CAACtD,GAAG,IAAG;QAC3B,MAAMmG,WAAW,GAAGnG,GAAG,CAACI,IAAI,EAAa;QACzC,MAAMgG,iBAAiB,GAAG,EAAE;QAE5B;QACA,KAAK,MAAM5B,MAAM,IAAI2B,WAAW,CAACT,UAAU,EAAE;UAC3C,IAAIS,WAAW,CAACT,UAAU,CAACW,cAAc,CAAC7B,MAAM,CAAC,EAAE;YACjD,MAAM/D,CAAC,GAAG,cAAc+D,MAAM,EAAE;YAC/B4B,iBAAyB,CAAC3F,CAAC,CAAC,GAAG,IAAI;UACtC;QACF;QAEAoF,KAAK,CAAClC,MAAM,CAAC3D,GAAG,CAACe,GAAG,EAAEqF,iBAAiB,CAAC;MAC1C,CAAC,CAAC;MACF,OAAOP,KAAK,CAACI,MAAM,EAAE;IACvB,CAAC,CAAC;EACJ;EAEA;EACAK,sBAAsBA,CAACxG,WAAmB,EAAE0E,MAAc;IACxD,OAAO,IAAI,CAAChH,EAAE,CAACa,UAAU,CAAU,GAAG,IAAI,CAACF,YAAY,IAAI2B,WAAW,WAAW,CAAC,CAACG,eAAe,EAAE,CAACC,IAAI,CACvGtD,GAAG,CAAC4D,OAAO,IAAIA,OAAO,CAAC+F,MAAM,CAACpG,MAAM,IAAG;MACrC,MAAMC,IAAI,GAAGD,MAAM,CAACE,OAAO,CAACL,GAAG,CAACI,IAAI,EAAa;MACjD,OAAO,CAACA,IAAI,CAACsF,UAAU,CAACW,cAAc,CAAC7B,MAAM,CAAC;IAChD,CAAC,CAAC,CAACgC,MAAM,CAAC,CACX;EACH;EAGA;EACAC,6BAA6BA,CAACjC,MAAc;IAC1C,OAAO,IAAI,CAAChG,QAAQ,CAACyB,eAAe,EAAE,CAACC,IAAI,CACzCrD,SAAS,CAAC6J,aAAa,IAAG;MACxB,MAAMC,eAAe,GAAGD,aAAa,CAAC9J,GAAG,CAAC4F,QAAQ,IAAG;QACnD,MAAM7B,MAAM,GAAG6B,QAAQ,CAACnC,OAAO,CAACL,GAAG,CAACM,EAAE;QACtC,OAAO,IAAI,CAACgG,sBAAsB,CAAC3F,MAAM,EAAE6D,MAAM,CAAC;MACpD,CAAC,CAAC;MACF,OAAO9H,aAAa,CAACiK,eAAe,CAAC;IACvC,CAAC,CAAC,EACF/J,GAAG,CAAC6G,YAAY,IAAIA,YAAY,CAACmD,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC,CAAC,CACzE;EACH;EAEA;EACAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACtJ,MAAM,CAACuJ,SAAS;EAC9B;EAEAC,aAAaA,CAACzC,MAAc,EAAE0C,MAA4B;IACxD,MAAMC,OAAO,GAAG,IAAI,CAAC/I,QAAQ,CAAC4B,GAAG,CAACwE,MAAM,CAAC;IACzC,OAAO2C,OAAO,CAACzD,GAAG,CAAC;MAAEwD,MAAM;MAAEE,cAAc,EAAE7K,SAAS,CAAC8K,GAAG;IAAE,CAAE,EAAE;MAAEC,KAAK,EAAE;IAAI,CAAE,CAAC;EAClF;EAEAC,qBAAqBA,CAAC/C,MAAc,EAAEgD,IAAgB;IACpD,MAAML,OAAO,GAAG,IAAI,CAAC/I,QAAQ,CAAC4B,GAAG,CAACwE,MAAM,CAAC;IACzC,OAAO2C,OAAO,CAACzD,GAAG,CAAC;MAAEwD,MAAM,EAAE,QAAQ;MAAEE,cAAc,EAAEI;IAAI,CAAE,EAAE;MAAEF,KAAK,EAAE;IAAI,CAAE,CAAC;EACjF;EAEAG,aAAaA,CAACjD,MAAc;IAC1B,OAAO,IAAI,CAACpG,QAAQ,CAAC4B,GAAG,CAAiBwE,MAAM,CAAC,CAACC,YAAY,EAAE,CAACvE,IAAI,CAClEtD,GAAG,CAACsC,IAAI,IAAIA,IAAI,EAAEgI,MAAM,IAAI,SAAS,CAAC,CACvC;EACH;EAEAQ,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAClK,EAAE,CAACa,UAAU,CAAiB,QAAQ,EAAE0C,GAAG,IAAIA,GAAG,CAAC4G,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAC5F1H,eAAe,EAAE,CAACC,IAAI,CACrBtD,GAAG,CAAC4D,OAAO,IAAIA,OAAO,CAAC5D,GAAG,CAAC6D,CAAC,IAAG;MAC7B,MAAML,IAAI,GAAGK,CAAC,CAACJ,OAAO,CAACL,GAAG,CAACI,IAAI,EAAoB;MACnD,MAAME,EAAE,GAAGG,CAAC,CAACJ,OAAO,CAACL,GAAG,CAACM,EAAE;MAC3B,OAAO;QAAE,GAAGF,IAAI;QAAEE;MAAE,CAAE,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CACJ;EACL;EAEA;;;;;;;EAOAsH,sCAAsCA,CAAA;IACpC,OAAO,IAAI,CAACpK,EAAE,CAACa,UAAU,CAAiB,QAAQ,CAAC,CAChD4B,eAAe,EAAE,CAACC,IAAI,CACrBtD,GAAG,CAAC4D,OAAO,IAAG;MACZ,MAAMqH,WAAW,GAAGtL,SAAS,CAAC8K,GAAG,EAAE;MACnC,OAAO7G,OAAO,CAAC5D,GAAG,CAAC6D,CAAC,IAAG;QACrB,MAAML,IAAI,GAAGK,CAAC,CAACJ,OAAO,CAACL,GAAG,CAACI,IAAI,EAAoB;QACnD,MAAME,EAAE,GAAGG,CAAC,CAACJ,OAAO,CAACL,GAAG,CAACM,EAAE;QAC3B,MAAM8G,cAAc,GAAGhH,IAAI,CAACgH,cAA2B;QACvD,IAAIA,cAAc,EAAE;UAClB,IAAI,CAACS,WAAW,CAACC,OAAO,IAAI,CAACV,cAAc,CAACU,OAAO,EAAE;YACnD,OAAO;cAAE,GAAG1H,IAAI;cAAEE,EAAE;cAAE4G,MAAM,EAAE;YAAS,CAAE;UAC3C;UACA,MAAMa,cAAc,GAAGF,WAAW,EAAEC,OAAO,GAAGV,cAAc,EAAEU,OAAO;UAErE;UACA,IAAIC,cAAc,IAAI,IAAI,CAACxJ,0BAA0B,EAAE;YACrD,OAAO;cAAE,GAAG6B,IAAI;cAAEE,EAAE;cAAE4G,MAAM,EAAE;YAAQ,CAAE;UAC1C,CAAC,MAAM;YACL,OAAO;cAAE,GAAG9G,IAAI;cAAEE,EAAE;cAAE4G,MAAM,EAAE;YAAS,CAAE;UAC3C;QACF;QACA,OAAO;UAAE,GAAG9G,IAAI;UAAEE,EAAE;UAAE4G,MAAM,EAAE;QAAS,CAAE;MAE3C,CAAC,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EAAC,QAAAc,CAAA,G;qBAlfU1K,WAAW,EAAA2K,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAXnL,WAAW;IAAAoL,OAAA,EAAXpL,WAAW,CAAAqL,IAAA;IAAAC,UAAA,EAFV;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}