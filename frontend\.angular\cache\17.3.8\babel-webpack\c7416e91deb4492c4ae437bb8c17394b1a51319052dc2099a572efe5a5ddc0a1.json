{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ClassroomRateBoxComponent } from './classroom-rate-box/classroom-rate-box.component';\nimport { ClassroomComponent } from './classroom.component';\nimport { ClassInfoComponent } from './classrooms/class/class-info/class-info.component';\nimport { ClassComponent } from './classrooms/class/class.component';\nimport { TeacherProfileComponent } from './classrooms/class/class-info/components/teacher-profile/teacher-profile.component';\nimport { ClassDetailsComponent } from './classrooms/class/class-details/class-details.component';\nimport { ClassProgressComponent } from './classrooms/class/class-progress/class-progress.component';\nimport { TeacherRateComponent } from './classrooms/class/class-info/teacher-rate/teacher-rate.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ClassroomComponent\n}, {\n  path: 'trials',\n  component: ClassroomComponent\n}, {\n  path: 'trials/:in_page',\n  component: ClassroomComponent\n}, {\n  path: 'lessons/:classroom_id',\n  component: ClassComponent,\n  data: {\n    showTabs: true\n  },\n  children: [{\n    path: 'details',\n    component: ClassDetailsComponent,\n    data: {\n      showTabs: true\n    }\n  }, {\n    path: 'info',\n    children: [{\n      path: '',\n      component: ClassInfoComponent,\n      data: {\n        showTabs: true\n      }\n    }, {\n      path: 'teacher',\n      component: TeacherProfileComponent,\n      data: {\n        showTabs: false\n      }\n    }, {\n      path: 'teacher-rate',\n      component: TeacherRateComponent,\n      data: {\n        showTabs: false\n      }\n    }, {\n      path: 'rate-trial',\n      component: ClassroomRateBoxComponent,\n      data: {\n        showTabs: true\n      }\n    }]\n  }, {\n    path: 'progress',\n    component: ClassProgressComponent,\n    data: {\n      showTabs: true\n    }\n  }]\n}, {\n  path: 'rate-trial',\n  component: ClassroomRateBoxComponent,\n  data: {\n    showTabs: true\n  }\n}];\nexport let ClassroomRoutingModule = /*#__PURE__*/(() => {\n  class ClassroomRoutingModule {\n    static #_ = this.ɵfac = function ClassroomRoutingModule_Factory(t) {\n      return new (t || ClassroomRoutingModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ClassroomRoutingModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return ClassroomRoutingModule;\n})();", "map": {"version": 3, "names": ["RouterModule", "ClassroomRateBoxComponent", "ClassroomComponent", "ClassInfoComponent", "ClassComponent", "TeacherProfileComponent", "ClassDetailsComponent", "ClassProgressComponent", "TeacherRateComponent", "routes", "path", "component", "data", "showTabs", "children", "ClassroomRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\classroom-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { ClassroomRateBoxComponent } from './classroom-rate-box/classroom-rate-box.component';\r\nimport { ClassroomComponent } from './classroom.component';\r\nimport { ClassInfoComponent } from './classrooms/class/class-info/class-info.component';\r\nimport { ClassComponent } from './classrooms/class/class.component';\r\nimport { TeacherProfileComponent } from './classrooms/class/class-info/components/teacher-profile/teacher-profile.component';\r\nimport { ClassDetailsComponent } from './classrooms/class/class-details/class-details.component';\r\nimport { ClassProgressComponent } from './classrooms/class/class-progress/class-progress.component';\r\nimport { TeacherRateComponent } from './classrooms/class/class-info/teacher-rate/teacher-rate.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '',\r\n    component: ClassroomComponent,\r\n  },\r\n  {\r\n    path: 'trials',\r\n    component: ClassroomComponent,\r\n  },\r\n  {\r\n    path: 'trials/:in_page',\r\n    component: ClassroomComponent,\r\n  },\r\n  {\r\n    path: 'lessons/:classroom_id',\r\n    component: ClassComponent,\r\n    data: { showTabs: true },\r\n    children: [\r\n      {\r\n        path: 'details',\r\n        component: ClassDetailsComponent,\r\n        data: { showTabs: true }\r\n      },\r\n      {\r\n        path: 'info',\r\n        children: [\r\n          {\r\n            path: '',\r\n            component: ClassInfoComponent,\r\n            data: { showTabs: true }\r\n          },\r\n          {\r\n            path: 'teacher',\r\n            component: TeacherProfileComponent,\r\n            data: { showTabs: false }\r\n          },\r\n          {\r\n            path: 'teacher-rate',\r\n            component: TeacherRateComponent,\r\n            data: { showTabs: false }\r\n          },\r\n          {\r\n            path: 'rate-trial',\r\n            component: ClassroomRateBoxComponent,\r\n            data: { showTabs: true }\r\n          },\r\n        ]\r\n      },\r\n      {\r\n        path: 'progress',\r\n        component: ClassProgressComponent,\r\n        data: { showTabs: true }\r\n      },\r\n    ]\r\n  },\r\n  {\r\n    path: 'rate-trial',\r\n    component: ClassroomRateBoxComponent,\r\n    data: { showTabs: true }\r\n  },\r\n];\r\n\r\n@NgModule({\r\n    imports: [RouterModule.forChild(routes)],\r\n    exports: [RouterModule]\r\n})\r\nexport class ClassroomRoutingModule { }"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,yBAAyB,QAAQ,mDAAmD;AAC7F,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,kBAAkB,QAAQ,oDAAoD;AACvF,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,uBAAuB,QAAQ,oFAAoF;AAC5H,SAASC,qBAAqB,QAAQ,0DAA0D;AAChG,SAASC,sBAAsB,QAAQ,4DAA4D;AACnG,SAASC,oBAAoB,QAAQ,mEAAmE;;;AAExG,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAET;CACZ,EACD;EACEQ,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAET;CACZ,EACD;EACEQ,IAAI,EAAE,iBAAiB;EACvBC,SAAS,EAAET;CACZ,EACD;EACEQ,IAAI,EAAE,uBAAuB;EAC7BC,SAAS,EAAEP,cAAc;EACzBQ,IAAI,EAAE;IAAEC,QAAQ,EAAE;EAAI,CAAE;EACxBC,QAAQ,EAAE,CACR;IACEJ,IAAI,EAAE,SAAS;IACfC,SAAS,EAAEL,qBAAqB;IAChCM,IAAI,EAAE;MAAEC,QAAQ,EAAE;IAAI;GACvB,EACD;IACEH,IAAI,EAAE,MAAM;IACZI,QAAQ,EAAE,CACR;MACEJ,IAAI,EAAE,EAAE;MACRC,SAAS,EAAER,kBAAkB;MAC7BS,IAAI,EAAE;QAAEC,QAAQ,EAAE;MAAI;KACvB,EACD;MACEH,IAAI,EAAE,SAAS;MACfC,SAAS,EAAEN,uBAAuB;MAClCO,IAAI,EAAE;QAAEC,QAAQ,EAAE;MAAK;KACxB,EACD;MACEH,IAAI,EAAE,cAAc;MACpBC,SAAS,EAAEH,oBAAoB;MAC/BI,IAAI,EAAE;QAAEC,QAAQ,EAAE;MAAK;KACxB,EACD;MACEH,IAAI,EAAE,YAAY;MAClBC,SAAS,EAAEV,yBAAyB;MACpCW,IAAI,EAAE;QAAEC,QAAQ,EAAE;MAAI;KACvB;GAEJ,EACD;IACEH,IAAI,EAAE,UAAU;IAChBC,SAAS,EAAEJ,sBAAsB;IACjCK,IAAI,EAAE;MAAEC,QAAQ,EAAE;IAAI;GACvB;CAEJ,EACD;EACEH,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEV,yBAAyB;EACpCW,IAAI,EAAE;IAAEC,QAAQ,EAAE;EAAI;CACvB,CACF;AAMD,WAAaE,sBAAsB;EAA7B,MAAOA,sBAAsB;IAAA,QAAAC,CAAA,G;uBAAtBD,sBAAsB;IAAA;IAAA,QAAAE,EAAA,G;YAAtBF;IAAsB;IAAA,QAAAG,EAAA,G;gBAHrBlB,YAAY,CAACmB,QAAQ,CAACV,MAAM,CAAC,EAC7BT,YAAY;IAAA;;SAEbe,sBAAsB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}