{"ast": null, "code": "import * as moment from 'moment';\nimport { ClassroomType, Status } from 'src/app/core/models/classroom.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/core/services/classroom.service\";\nimport * as i3 from \"src/app/core/services/auth.service\";\nimport * as i4 from \"src/app/core/services/general.service\";\nconst _c0 = [\"requestLesson\"];\nexport let ClassroomsComponent = /*#__PURE__*/(() => {\n  class ClassroomsComponent {\n    constructor(router, classroomService, authService, generalService) {\n      this.router = router;\n      this.classroomService = classroomService;\n      this.authService = authService;\n      this.generalService = generalService;\n      this.filteredClassrooms = [];\n      this.inTrial = false;\n      this.role = \"Teacher\";\n      this.selectedClassroomToRequestLesson = {};\n      this.disableExtendButton = true;\n      this.showProg = false;\n      this.trialClassRooms = [];\n      this.showRequestLesson = false;\n    }\n    ngOnInit() {\n      console.log(this.filteredClassrooms);\n      this.role = this.authService.getUserRole();\n    }\n    ngAfterContentInit() {\n      setTimeout(() => {\n        this.showProg = true;\n      }, 200);\n    }\n    getExtendPackageDisable(classroom) {\n      if (moment(new Date(classroom.activePackage?.expiresOn).getTime()).add('2', 'w').toDate().getTime() > moment(new Date()).add('2', 'w').toDate().getTime()) {\n        return true;\n      }\n      return false;\n    }\n    navigateToClassroom(classroom) {\n      let isTrial = classroom.type === ClassroomType.TRIAL ? {\n        isTrial: true\n      } : {};\n      this.router.navigate(['/classrooms/lessons/' + classroom.id, isTrial]);\n    }\n    navigateToBuyPackage(classroom) {\n      this.router.navigate(['/buy-package/' + classroom.id]);\n    }\n    navigateToExtendPackage(classroom) {\n      this.router.navigate(['/extend-package/' + classroom.id]);\n    }\n    getClassroomTitle(classroom) {\n      return this.classroomService.getClassroomTitle(classroom);\n    }\n    getClassroomTitleBackgroundColor(status) {\n      if (status === Status.INACTIVE) {\n        return '#7D7D7D';\n      }\n      if (status === Status.TRIAL) {\n        return '#a4a2e6';\n      }\n      return '#2d2a4b';\n    }\n    getClassroomBorderColor(status) {\n      if (status === Status.INACTIVE) {\n        return '#7D7D7D';\n      }\n      if (status === Status.TRIAL) {\n        return '#a4a2e6';\n      }\n      return '#2d2a4b';\n    }\n    getClassroomStatusColor(status) {\n      if (status === Status.PENDING) {\n        return '#3f37c9';\n      }\n      if (status === Status.EXPIRED) {\n        return '#8E8BC9';\n      }\n      if (status === Status.ON_HOLD) {\n        return '#f27769';\n      }\n      if (status === Status.INACTIVE) {\n        return '#7D7D7D';\n      }\n      if (status === Status.TRIAL) {\n        return '#a4a2e6';\n      }\n      return '#2d2a4b';\n    }\n    getPercent(classroom) {\n      return 100 - classroom.activePackage?.hoursLeft * 100 / classroom.activePackage?.totalHours;\n    }\n    getSubtitle(classroom) {\n      return `${classroom.hoursLeft} hours left`;\n    }\n    getClassroomType(classroom) {\n      return this.classroomService.getClassroomType(classroom.users);\n    }\n    onShowRequestLesson(classroom) {\n      this.classroomService.classroomToArrangeLesson = classroom;\n      this.router.navigate(['/reschedule']);\n    }\n    static #_ = this.ɵfac = function ClassroomsComponent_Factory(t) {\n      return new (t || ClassroomsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ClassroomService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.GeneralService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ClassroomsComponent,\n      selectors: [[\"app-classrooms\"]],\n      viewQuery: function ClassroomsComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.requestLesson = _t.first);\n        }\n      },\n      inputs: {\n        filteredClassrooms: \"filteredClassrooms\",\n        inTrial: \"inTrial\"\n      },\n      decls: 0,\n      vars: 0,\n      template: function ClassroomsComponent_Template(rf, ctx) {},\n      styles: [\".classrooms-classrooms[_ngcontent-%COMP%]{margin-top:30px}.col--1of4[_ngcontent-%COMP%]{display:flex;justify-content:center}.classrooms-classrooms-content[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]{justify-content:left}.classrooms-classrooms-content[_ngcontent-%COMP%]   .teacher-classroom[_ngcontent-%COMP%]{width:192px;height:192px}.classrooms-classrooms-content[_ngcontent-%COMP%]   .student-classroom[_ngcontent-%COMP%]{width:100%}.classrooms-classrooms-content[_ngcontent-%COMP%]   .classroom[_ngcontent-%COMP%]{margin:15px 0;cursor:pointer;border-radius:20px;border:3px solid var(--main-color);display:flex;flex-direction:column;align-items:center;position:relative}.classrooms-classrooms-content[_ngcontent-%COMP%]   .classroom[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{border-radius:20px;background-color:var(--main-color);font-weight:700;font-size:20px;width:100%;border:3px solid var(--main-color);text-align:center;color:#fff;filter:drop-shadow(0px 3px 6px rgba(0,0,0,.16));margin-top:-1px;height:42px;line-height:42px}.classrooms-classrooms-content[_ngcontent-%COMP%]   .classroom[_ngcontent-%COMP%]   .classroom-level[_ngcontent-%COMP%], .classrooms-classrooms-content[_ngcontent-%COMP%]   .classroom[_ngcontent-%COMP%]   .hours[_ngcontent-%COMP%], .classrooms-classrooms-content[_ngcontent-%COMP%]   .classroom[_ngcontent-%COMP%]   .classroom-status[_ngcontent-%COMP%]{width:80%;padding-bottom:5px;margin:8px;font-size:15px}.classrooms-classrooms-content[_ngcontent-%COMP%]   .classroom[_ngcontent-%COMP%]   .classroom-level[_ngcontent-%COMP%], .classrooms-classrooms-content[_ngcontent-%COMP%]   .classroom[_ngcontent-%COMP%]   .hours[_ngcontent-%COMP%]{border-bottom:1px solid var(--main-color)}.classrooms-classrooms-content[_ngcontent-%COMP%]   .classroom[_ngcontent-%COMP%]   .classroom-level[_ngcontent-%COMP%]{margin-top:11px}.classrooms-classrooms-content[_ngcontent-%COMP%]   .classroom[_ngcontent-%COMP%]   .classroom-status[_ngcontent-%COMP%]{margin-top:5.5px;margin-bottom:18px}.classrooms-classrooms-content[_ngcontent-%COMP%]   .classroom[_ngcontent-%COMP%]   .students-count[_ngcontent-%COMP%]{width:30px;height:30px;line-height:30px;border-radius:50%;font-size:15px;color:#000;text-align:center;border:3px solid var(--main-color);background:#fff;bottom:-18px;position:absolute}@media screen and (max-width: 768px){.classrooms-classrooms-content[_ngcontent-%COMP%]   .classroom[_ngcontent-%COMP%]{margin:12px 5px}}.empty-classroom[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;flex-direction:column}.sections[_ngcontent-%COMP%]{font-size:15px;width:100%;display:flex;justify-content:space-evenly;flex-wrap:wrap}.sections[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]{width:33%}.sections[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]{font-weight:700;color:#aaaab3;width:100%;text-align:center;padding:15px 0}.sections[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .section-details[_ngcontent-%COMP%]{display:flex}.progress[_ngcontent-%COMP%]{width:40%}.progress-details[_ngcontent-%COMP%], .package-details[_ngcontent-%COMP%]{display:flex;width:100%}.progress-details[_ngcontent-%COMP%]   .details-left[_ngcontent-%COMP%], .progress-details[_ngcontent-%COMP%]   .details-right[_ngcontent-%COMP%], .package-details[_ngcontent-%COMP%]   .details-left[_ngcontent-%COMP%], .package-details[_ngcontent-%COMP%]   .details-right[_ngcontent-%COMP%]{padding:0 5px;width:50%}.progress-details[_ngcontent-%COMP%]   .details-left[_ngcontent-%COMP%] > div[_ngcontent-%COMP%], .progress-details[_ngcontent-%COMP%]   .details-right[_ngcontent-%COMP%] > div[_ngcontent-%COMP%], .package-details[_ngcontent-%COMP%]   .details-left[_ngcontent-%COMP%] > div[_ngcontent-%COMP%], .package-details[_ngcontent-%COMP%]   .details-right[_ngcontent-%COMP%] > div[_ngcontent-%COMP%]{margin-bottom:10px}.progress-details[_ngcontent-%COMP%]   .details-left[_ngcontent-%COMP%], .package-details[_ngcontent-%COMP%]   .details-left[_ngcontent-%COMP%]{color:#707070}.progress-details[_ngcontent-%COMP%]   .details-right[_ngcontent-%COMP%], .package-details[_ngcontent-%COMP%]   .details-right[_ngcontent-%COMP%]{font-weight:700}.package-details[_ngcontent-%COMP%]   .details-left[_ngcontent-%COMP%]{text-align:end}.package-btns[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;width:30%;position:absolute;bottom:10px}.package-btns[_ngcontent-%COMP%]   .gray-button[_ngcontent-%COMP%], .package-btns[_ngcontent-%COMP%]   .main-color-button[_ngcontent-%COMP%]{color:#fff;font-size:12px;margin:5px 0;width:70%;border-radius:24px}.package-btns[_ngcontent-%COMP%]   .disabled-btn[_ngcontent-%COMP%]{font-size:12px;margin:5px 0;width:70%;border-radius:24px}.package-btns[_ngcontent-%COMP%]   .light-purple-button[_ngcontent-%COMP%]{font-size:18px;width:100%;border-radius:24px;margin:5px 0}.teacher[_ngcontent-%COMP%]{display:flex;width:100%;align-items:center}.teacher[_ngcontent-%COMP%]   .teacher-title[_ngcontent-%COMP%]{color:#707070}.teacher[_ngcontent-%COMP%]   .teacher-name[_ngcontent-%COMP%]{font-weight:700;margin-top:8px}.lesson-details[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap}.lesson[_ngcontent-%COMP%]{margin:13px;cursor:pointer;border-radius:20px;display:flex;flex-direction:column;align-items:center;justify-content:center;position:relative;width:140px;height:140px;box-sizing:border-box;z-index:2;background:#fff;font-weight:700}.lesson[_ngcontent-%COMP%]   .copy-lesson[_ngcontent-%COMP%]{position:absolute;width:140px;height:140px;border-radius:20px;z-index:-1}.lesson[_ngcontent-%COMP%]   .copy-lesson-2[_ngcontent-%COMP%]{position:absolute;width:130px;height:130px;border-radius:16px;z-index:-1;background-color:#fff}\"]\n    });\n  }\n  return ClassroomsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}