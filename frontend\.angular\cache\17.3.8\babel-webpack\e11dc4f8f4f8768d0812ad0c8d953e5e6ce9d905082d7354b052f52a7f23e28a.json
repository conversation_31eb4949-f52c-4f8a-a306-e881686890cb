{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, inject, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ContentChildren, NgModule } from '@angular/core';\nimport { PrimeNGConfig, TranslationKeys, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { TimesCircleIcon } from 'primeng/icons/timescircle';\n\n/**\n * Chip represents people using icons, labels and images.\n * @group Components\n */\nconst _c0 = [\"*\"];\nfunction Chip_div_0_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 6);\n    i0.ɵɵlistener(\"error\", function Chip_div_0_img_2_Template_img_error_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.imageError($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.image, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.alt);\n  }\n}\nfunction Chip_div_0_ng_template_3_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(ctx_r1.icon);\n    i0.ɵɵproperty(\"ngClass\", \"p-chip-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"icon\");\n  }\n}\nfunction Chip_div_0_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Chip_div_0_ng_template_3_span_0_Template, 1, 4, \"span\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.icon);\n  }\n}\nfunction Chip_div_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵattribute(\"data-pc-section\", \"label\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.label);\n  }\n}\nfunction Chip_div_0_ng_container_6_ng_container_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵlistener(\"click\", function Chip_div_0_ng_container_6_ng_container_1_span_1_Template_span_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.close($event));\n    })(\"keydown\", function Chip_div_0_ng_container_6_ng_container_1_span_1_Template_span_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onKeydown($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassMap(ctx_r1.removeIcon);\n    i0.ɵɵproperty(\"ngClass\", \"pi-chip-remove-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"removeicon\")(\"aria-label\", ctx_r1.removeAriaLabel);\n  }\n}\nfunction Chip_div_0_ng_container_6_ng_container_1_TimesCircleIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"TimesCircleIcon\", 14);\n    i0.ɵɵlistener(\"click\", function Chip_div_0_ng_container_6_ng_container_1_TimesCircleIcon_2_Template_TimesCircleIcon_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.close($event));\n    })(\"keydown\", function Chip_div_0_ng_container_6_ng_container_1_TimesCircleIcon_2_Template_TimesCircleIcon_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onKeydown($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassMap(\"pi-chip-remove-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"removeicon\")(\"aria-label\", ctx_r1.removeAriaLabel);\n  }\n}\nfunction Chip_div_0_ng_container_6_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Chip_div_0_ng_container_6_ng_container_1_span_1_Template, 1, 5, \"span\", 11)(2, Chip_div_0_ng_container_6_ng_container_1_TimesCircleIcon_2_Template, 1, 4, \"TimesCircleIcon\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.removeIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.removeIcon);\n  }\n}\nfunction Chip_div_0_ng_container_6_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Chip_div_0_ng_container_6_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Chip_div_0_ng_container_6_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Chip_div_0_ng_container_6_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 15);\n    i0.ɵɵlistener(\"click\", function Chip_div_0_ng_container_6_span_2_Template_span_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.close($event));\n    })(\"keydown\", function Chip_div_0_ng_container_6_span_2_Template_span_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onKeydown($event));\n    });\n    i0.ɵɵtemplate(1, Chip_div_0_ng_container_6_span_2_1_Template, 1, 0, null, 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵattribute(\"data-pc-section\", \"removeicon\")(\"aria-label\", ctx_r1.removeAriaLabel);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.removeIconTemplate);\n  }\n}\nfunction Chip_div_0_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Chip_div_0_ng_container_6_ng_container_1_Template, 3, 2, \"ng-container\", 5)(2, Chip_div_0_ng_container_6_span_2_Template, 2, 3, \"span\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.removeIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.removeIconTemplate);\n  }\n}\nfunction Chip_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵprojection(1);\n    i0.ɵɵtemplate(2, Chip_div_0_img_2_Template, 1, 2, \"img\", 3)(3, Chip_div_0_ng_template_3_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(5, Chip_div_0_div_5_Template, 2, 2, \"div\", 4)(6, Chip_div_0_ng_container_6_Template, 3, 2, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const iconTemplate_r6 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.styleClass);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.containerClass())(\"ngStyle\", ctx_r1.style);\n    i0.ɵɵattribute(\"data-pc-name\", \"chip\")(\"aria-label\", ctx_r1.label)(\"data-pc-section\", \"root\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.image)(\"ngIfElse\", iconTemplate_r6);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.label);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.removable);\n  }\n}\nclass Chip {\n  /**\n   * Defines the text to display.\n   * @group Props\n   */\n  label;\n  /**\n   * Defines the icon to display.\n   * @group Props\n   */\n  icon;\n  /**\n   * Defines the image to display.\n   * @group Props\n   */\n  image;\n  /**\n   * Alt attribute of the image.\n   * @group Props\n   */\n  alt;\n  /**\n   * Inline style of the element.\n   * @group Props\n   */\n  style;\n  /**\n   * Class of the element.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Whether to display a remove icon.\n   * @group Props\n   */\n  removable = false;\n  /**\n   * Icon of the remove element.\n   * @group Props\n   */\n  removeIcon;\n  /**\n   * Callback to invoke when a chip is removed.\n   * @param {MouseEvent} event - Mouse event.\n   * @group Emits\n   */\n  onRemove = new EventEmitter();\n  /**\n   * This event is triggered if an error occurs while loading an image file.\n   * @param {Event} event - Browser event.\n   * @group Emits\n   */\n  onImageError = new EventEmitter();\n  config = inject(PrimeNGConfig);\n  visible = true;\n  removeIconTemplate;\n  get removeAriaLabel() {\n    return this.config.getTranslation(TranslationKeys.ARIA)['removeLabel'];\n  }\n  templates;\n  ngAfterContentInit() {\n    this.templates.forEach(item => {\n      switch (item.getType()) {\n        case 'removeicon':\n          this.removeIconTemplate = item.template;\n          break;\n        default:\n          this.removeIconTemplate = item.template;\n          break;\n      }\n    });\n  }\n  containerClass() {\n    return {\n      'p-chip p-component': true,\n      'p-chip-image': this.image != null\n    };\n  }\n  close(event) {\n    this.visible = false;\n    this.onRemove.emit(event);\n  }\n  onKeydown(event) {\n    if (event.key === 'Enter' || event.key === 'Backspace') {\n      this.close(event);\n    }\n  }\n  imageError(event) {\n    this.onImageError.emit(event);\n  }\n  static ɵfac = function Chip_Factory(t) {\n    return new (t || Chip)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: Chip,\n    selectors: [[\"p-chip\"]],\n    contentQueries: function Chip_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      label: \"label\",\n      icon: \"icon\",\n      image: \"image\",\n      alt: \"alt\",\n      style: \"style\",\n      styleClass: \"styleClass\",\n      removable: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"removable\", \"removable\", booleanAttribute],\n      removeIcon: \"removeIcon\"\n    },\n    outputs: {\n      onRemove: \"onRemove\",\n      onImageError: \"onImageError\"\n    },\n    features: [i0.ɵɵInputTransformsFeature],\n    ngContentSelectors: _c0,\n    decls: 1,\n    vars: 1,\n    consts: [[\"iconTemplate\", \"\"], [3, \"ngClass\", \"class\", \"ngStyle\", 4, \"ngIf\"], [3, \"ngClass\", \"ngStyle\"], [3, \"src\", \"alt\", \"error\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"p-chip-text\", 4, \"ngIf\"], [4, \"ngIf\"], [3, \"error\", \"src\", \"alt\"], [3, \"class\", \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\"], [1, \"p-chip-text\"], [\"tabindex\", \"0\", \"class\", \"pi-chip-remove-icon\", \"role\", \"button\", 3, \"click\", \"keydown\", 4, \"ngIf\"], [\"tabindex\", \"0\", \"role\", \"button\", 3, \"class\", \"ngClass\", \"click\", \"keydown\", 4, \"ngIf\"], [\"tabindex\", \"0\", \"role\", \"button\", 3, \"class\", \"click\", \"keydown\", 4, \"ngIf\"], [\"tabindex\", \"0\", \"role\", \"button\", 3, \"click\", \"keydown\", \"ngClass\"], [\"tabindex\", \"0\", \"role\", \"button\", 3, \"click\", \"keydown\"], [\"tabindex\", \"0\", \"role\", \"button\", 1, \"pi-chip-remove-icon\", 3, \"click\", \"keydown\"], [4, \"ngTemplateOutlet\"]],\n    template: function Chip_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵtemplate(0, Chip_div_0_Template, 7, 11, \"div\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.visible);\n      }\n    },\n    dependencies: () => [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, TimesCircleIcon],\n    styles: [\"@layer primeng{.p-chip{display:inline-flex;align-items:center}.p-chip-text,.p-chip-icon.pi,.pi-chip-remove-icon.pi{line-height:1.5}.pi-chip-remove-icon{cursor:pointer}.p-chip img{border-radius:50%}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Chip, [{\n    type: Component,\n    args: [{\n      selector: 'p-chip',\n      template: `\n        <div [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\" *ngIf=\"visible\" [attr.data-pc-name]=\"'chip'\" [attr.aria-label]=\"label\" [attr.data-pc-section]=\"'root'\">\n            <ng-content></ng-content>\n            <img [src]=\"image\" *ngIf=\"image; else iconTemplate\" (error)=\"imageError($event)\" [alt]=\"alt\" />\n            <ng-template #iconTemplate><span *ngIf=\"icon\" [class]=\"icon\" [ngClass]=\"'p-chip-icon'\" [attr.data-pc-section]=\"'icon'\"></span></ng-template>\n            <div class=\"p-chip-text\" *ngIf=\"label\" [attr.data-pc-section]=\"'label'\">{{ label }}</div>\n            <ng-container *ngIf=\"removable\">\n                <ng-container *ngIf=\"!removeIconTemplate\">\n                    <span\n                        tabindex=\"0\"\n                        *ngIf=\"removeIcon\"\n                        [class]=\"removeIcon\"\n                        [ngClass]=\"'pi-chip-remove-icon'\"\n                        [attr.data-pc-section]=\"'removeicon'\"\n                        (click)=\"close($event)\"\n                        (keydown)=\"onKeydown($event)\"\n                        [attr.aria-label]=\"removeAriaLabel\"\n                        role=\"button\"\n                    ></span>\n                    <TimesCircleIcon tabindex=\"0\" *ngIf=\"!removeIcon\" [class]=\"'pi-chip-remove-icon'\" [attr.data-pc-section]=\"'removeicon'\" (click)=\"close($event)\" (keydown)=\"onKeydown($event)\" [attr.aria-label]=\"removeAriaLabel\" role=\"button\" />\n                </ng-container>\n                <span *ngIf=\"removeIconTemplate\" tabindex=\"0\" [attr.data-pc-section]=\"'removeicon'\" class=\"pi-chip-remove-icon\" (click)=\"close($event)\" (keydown)=\"onKeydown($event)\" [attr.aria-label]=\"removeAriaLabel\" role=\"button\">\n                    <ng-template *ngTemplateOutlet=\"removeIconTemplate\"></ng-template>\n                </span>\n            </ng-container>\n        </div>\n    `,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-chip{display:inline-flex;align-items:center}.p-chip-text,.p-chip-icon.pi,.pi-chip-remove-icon.pi{line-height:1.5}.pi-chip-remove-icon{cursor:pointer}.p-chip img{border-radius:50%}}\\n\"]\n    }]\n  }], null, {\n    label: [{\n      type: Input\n    }],\n    icon: [{\n      type: Input\n    }],\n    image: [{\n      type: Input\n    }],\n    alt: [{\n      type: Input\n    }],\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    removable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    removeIcon: [{\n      type: Input\n    }],\n    onRemove: [{\n      type: Output\n    }],\n    onImageError: [{\n      type: Output\n    }],\n    templates: [{\n      type: ContentChildren,\n      args: [PrimeTemplate]\n    }]\n  });\n})();\nclass ChipModule {\n  static ɵfac = function ChipModule_Factory(t) {\n    return new (t || ChipModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ChipModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, TimesCircleIcon, SharedModule, SharedModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ChipModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, TimesCircleIcon, SharedModule],\n      exports: [Chip, SharedModule],\n      declarations: [Chip]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Chip, ChipModule };", "map": {"version": 3, "names": ["i1", "CommonModule", "i0", "EventEmitter", "inject", "booleanAttribute", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "Output", "ContentChildren", "NgModule", "PrimeNGConfig", "Translation<PERSON>eys", "PrimeTemplate", "SharedModule", "TimesCircleIcon", "_c0", "Chip_div_0_img_2_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "Chip_div_0_img_2_Template_img_error_0_listener", "$event", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "imageError", "ɵɵelementEnd", "ɵɵproperty", "image", "ɵɵsanitizeUrl", "alt", "Chip_div_0_ng_template_3_span_0_Template", "ɵɵelement", "ɵɵclassMap", "icon", "ɵɵattribute", "Chip_div_0_ng_template_3_Template", "ɵɵtemplate", "Chip_div_0_div_5_Template", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate", "label", "Chip_div_0_ng_container_6_ng_container_1_span_1_Template", "_r3", "Chip_div_0_ng_container_6_ng_container_1_span_1_Template_span_click_0_listener", "close", "Chip_div_0_ng_container_6_ng_container_1_span_1_Template_span_keydown_0_listener", "onKeydown", "removeIcon", "removeAriaLabel", "Chip_div_0_ng_container_6_ng_container_1_TimesCircleIcon_2_Template", "_r4", "Chip_div_0_ng_container_6_ng_container_1_TimesCircleIcon_2_Template_TimesCircleIcon_click_0_listener", "Chip_div_0_ng_container_6_ng_container_1_TimesCircleIcon_2_Template_TimesCircleIcon_keydown_0_listener", "Chip_div_0_ng_container_6_ng_container_1_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "Chip_div_0_ng_container_6_span_2_1_ng_template_0_Template", "Chip_div_0_ng_container_6_span_2_1_Template", "Chip_div_0_ng_container_6_span_2_Template", "_r5", "Chip_div_0_ng_container_6_span_2_Template_span_click_0_listener", "Chip_div_0_ng_container_6_span_2_Template_span_keydown_0_listener", "removeIconTemplate", "Chip_div_0_ng_container_6_Template", "Chip_div_0_Template", "ɵɵprojection", "ɵɵtemplateRefExtractor", "iconTemplate_r6", "ɵɵreference", "styleClass", "containerClass", "style", "removable", "Chip", "onRemove", "onImageError", "config", "visible", "getTranslation", "ARIA", "templates", "ngAfterContentInit", "for<PERSON>ach", "item", "getType", "template", "event", "emit", "key", "ɵfac", "Chip_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "Chip_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "hostAttrs", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "outputs", "features", "ɵɵInputTransformsFeature", "ngContentSelectors", "decls", "vars", "consts", "Chip_Template", "ɵɵprojectionDef", "dependencies", "Ng<PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "OnPush", "None", "host", "class", "transform", "ChipModule", "ChipModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/primeng/fesm2022/primeng-chip.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, inject, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ContentChildren, NgModule } from '@angular/core';\nimport { PrimeNGConfig, TranslationKeys, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { TimesCircleIcon } from 'primeng/icons/timescircle';\n\n/**\n * Chip represents people using icons, labels and images.\n * @group Components\n */\nclass Chip {\n    /**\n     * Defines the text to display.\n     * @group Props\n     */\n    label;\n    /**\n     * Defines the icon to display.\n     * @group Props\n     */\n    icon;\n    /**\n     * Defines the image to display.\n     * @group Props\n     */\n    image;\n    /**\n     * Alt attribute of the image.\n     * @group Props\n     */\n    alt;\n    /**\n     * Inline style of the element.\n     * @group Props\n     */\n    style;\n    /**\n     * Class of the element.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Whether to display a remove icon.\n     * @group Props\n     */\n    removable = false;\n    /**\n     * Icon of the remove element.\n     * @group Props\n     */\n    removeIcon;\n    /**\n     * Callback to invoke when a chip is removed.\n     * @param {MouseEvent} event - Mouse event.\n     * @group Emits\n     */\n    onRemove = new EventEmitter();\n    /**\n     * This event is triggered if an error occurs while loading an image file.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onImageError = new EventEmitter();\n    config = inject(PrimeNGConfig);\n    visible = true;\n    removeIconTemplate;\n    get removeAriaLabel() {\n        return this.config.getTranslation(TranslationKeys.ARIA)['removeLabel'];\n    }\n    templates;\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'removeicon':\n                    this.removeIconTemplate = item.template;\n                    break;\n                default:\n                    this.removeIconTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    containerClass() {\n        return {\n            'p-chip p-component': true,\n            'p-chip-image': this.image != null\n        };\n    }\n    close(event) {\n        this.visible = false;\n        this.onRemove.emit(event);\n    }\n    onKeydown(event) {\n        if (event.key === 'Enter' || event.key === 'Backspace') {\n            this.close(event);\n        }\n    }\n    imageError(event) {\n        this.onImageError.emit(event);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: Chip, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"17.3.7\", type: Chip, selector: \"p-chip\", inputs: { label: \"label\", icon: \"icon\", image: \"image\", alt: \"alt\", style: \"style\", styleClass: \"styleClass\", removable: [\"removable\", \"removable\", booleanAttribute], removeIcon: \"removeIcon\" }, outputs: { onRemove: \"onRemove\", onImageError: \"onImageError\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"templates\", predicate: PrimeTemplate }], ngImport: i0, template: `\n        <div [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\" *ngIf=\"visible\" [attr.data-pc-name]=\"'chip'\" [attr.aria-label]=\"label\" [attr.data-pc-section]=\"'root'\">\n            <ng-content></ng-content>\n            <img [src]=\"image\" *ngIf=\"image; else iconTemplate\" (error)=\"imageError($event)\" [alt]=\"alt\" />\n            <ng-template #iconTemplate><span *ngIf=\"icon\" [class]=\"icon\" [ngClass]=\"'p-chip-icon'\" [attr.data-pc-section]=\"'icon'\"></span></ng-template>\n            <div class=\"p-chip-text\" *ngIf=\"label\" [attr.data-pc-section]=\"'label'\">{{ label }}</div>\n            <ng-container *ngIf=\"removable\">\n                <ng-container *ngIf=\"!removeIconTemplate\">\n                    <span\n                        tabindex=\"0\"\n                        *ngIf=\"removeIcon\"\n                        [class]=\"removeIcon\"\n                        [ngClass]=\"'pi-chip-remove-icon'\"\n                        [attr.data-pc-section]=\"'removeicon'\"\n                        (click)=\"close($event)\"\n                        (keydown)=\"onKeydown($event)\"\n                        [attr.aria-label]=\"removeAriaLabel\"\n                        role=\"button\"\n                    ></span>\n                    <TimesCircleIcon tabindex=\"0\" *ngIf=\"!removeIcon\" [class]=\"'pi-chip-remove-icon'\" [attr.data-pc-section]=\"'removeicon'\" (click)=\"close($event)\" (keydown)=\"onKeydown($event)\" [attr.aria-label]=\"removeAriaLabel\" role=\"button\" />\n                </ng-container>\n                <span *ngIf=\"removeIconTemplate\" tabindex=\"0\" [attr.data-pc-section]=\"'removeicon'\" class=\"pi-chip-remove-icon\" (click)=\"close($event)\" (keydown)=\"onKeydown($event)\" [attr.aria-label]=\"removeAriaLabel\" role=\"button\">\n                    <ng-template *ngTemplateOutlet=\"removeIconTemplate\"></ng-template>\n                </span>\n            </ng-container>\n        </div>\n    `, isInline: true, styles: [\"@layer primeng{.p-chip{display:inline-flex;align-items:center}.p-chip-text,.p-chip-icon.pi,.pi-chip-remove-icon.pi{line-height:1.5}.pi-chip-remove-icon{cursor:pointer}.p-chip img{border-radius:50%}}\\n\"], dependencies: [{ kind: \"directive\", type: i0.forwardRef(() => i1.NgClass), selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgIf), selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgTemplateOutlet), selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i0.forwardRef(() => i1.NgStyle), selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"component\", type: i0.forwardRef(() => TimesCircleIcon), selector: \"TimesCircleIcon\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: Chip, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-chip', template: `\n        <div [ngClass]=\"containerClass()\" [class]=\"styleClass\" [ngStyle]=\"style\" *ngIf=\"visible\" [attr.data-pc-name]=\"'chip'\" [attr.aria-label]=\"label\" [attr.data-pc-section]=\"'root'\">\n            <ng-content></ng-content>\n            <img [src]=\"image\" *ngIf=\"image; else iconTemplate\" (error)=\"imageError($event)\" [alt]=\"alt\" />\n            <ng-template #iconTemplate><span *ngIf=\"icon\" [class]=\"icon\" [ngClass]=\"'p-chip-icon'\" [attr.data-pc-section]=\"'icon'\"></span></ng-template>\n            <div class=\"p-chip-text\" *ngIf=\"label\" [attr.data-pc-section]=\"'label'\">{{ label }}</div>\n            <ng-container *ngIf=\"removable\">\n                <ng-container *ngIf=\"!removeIconTemplate\">\n                    <span\n                        tabindex=\"0\"\n                        *ngIf=\"removeIcon\"\n                        [class]=\"removeIcon\"\n                        [ngClass]=\"'pi-chip-remove-icon'\"\n                        [attr.data-pc-section]=\"'removeicon'\"\n                        (click)=\"close($event)\"\n                        (keydown)=\"onKeydown($event)\"\n                        [attr.aria-label]=\"removeAriaLabel\"\n                        role=\"button\"\n                    ></span>\n                    <TimesCircleIcon tabindex=\"0\" *ngIf=\"!removeIcon\" [class]=\"'pi-chip-remove-icon'\" [attr.data-pc-section]=\"'removeicon'\" (click)=\"close($event)\" (keydown)=\"onKeydown($event)\" [attr.aria-label]=\"removeAriaLabel\" role=\"button\" />\n                </ng-container>\n                <span *ngIf=\"removeIconTemplate\" tabindex=\"0\" [attr.data-pc-section]=\"'removeicon'\" class=\"pi-chip-remove-icon\" (click)=\"close($event)\" (keydown)=\"onKeydown($event)\" [attr.aria-label]=\"removeAriaLabel\" role=\"button\">\n                    <ng-template *ngTemplateOutlet=\"removeIconTemplate\"></ng-template>\n                </span>\n            </ng-container>\n        </div>\n    `, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        class: 'p-element'\n                    }, styles: [\"@layer primeng{.p-chip{display:inline-flex;align-items:center}.p-chip-text,.p-chip-icon.pi,.pi-chip-remove-icon.pi{line-height:1.5}.pi-chip-remove-icon{cursor:pointer}.p-chip img{border-radius:50%}}\\n\"] }]\n        }], propDecorators: { label: [{\n                type: Input\n            }], icon: [{\n                type: Input\n            }], image: [{\n                type: Input\n            }], alt: [{\n                type: Input\n            }], style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], removable: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], removeIcon: [{\n                type: Input\n            }], onRemove: [{\n                type: Output\n            }], onImageError: [{\n                type: Output\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }] } });\nclass ChipModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: ChipModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.7\", ngImport: i0, type: ChipModule, declarations: [Chip], imports: [CommonModule, TimesCircleIcon, SharedModule], exports: [Chip, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: ChipModule, imports: [CommonModule, TimesCircleIcon, SharedModule, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: ChipModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, TimesCircleIcon, SharedModule],\n                    exports: [Chip, SharedModule],\n                    declarations: [Chip]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { Chip, ChipModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AACvK,SAASC,aAAa,EAAEC,eAAe,EAAEC,aAAa,EAAEC,YAAY,QAAQ,aAAa;AACzF,SAASC,eAAe,QAAQ,2BAA2B;;AAE3D;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,SAAAC,0BAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GA8F6FpB,EAAE,CAAAqB,gBAAA;IAAFrB,EAAE,CAAAsB,cAAA,YAIW,CAAC;IAJdtB,EAAE,CAAAuB,UAAA,mBAAAC,+CAAAC,MAAA;MAAFzB,EAAE,CAAA0B,aAAA,CAAAN,GAAA;MAAA,MAAAO,MAAA,GAAF3B,EAAE,CAAA4B,aAAA;MAAA,OAAF5B,EAAE,CAAA6B,WAAA,CAItBF,MAAA,CAAAG,UAAA,CAAAL,MAAiB,CAAC;IAAA,EAAC;IAJCzB,EAAE,CAAA+B,YAAA,CAIW,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAS,MAAA,GAJd3B,EAAE,CAAA4B,aAAA;IAAF5B,EAAE,CAAAgC,UAAA,QAAAL,MAAA,CAAAM,KAAA,EAAFjC,EAAE,CAAAkC,aAIlE,CAAC,QAAAP,MAAA,CAAAQ,GAAyE,CAAC;EAAA;AAAA;AAAA,SAAAC,yCAAAlB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJXlB,EAAE,CAAAqC,SAAA,aAK0C,CAAC;EAAA;EAAA,IAAAnB,EAAA;IAAA,MAAAS,MAAA,GAL7C3B,EAAE,CAAA4B,aAAA;IAAF5B,EAAE,CAAAsC,UAAA,CAAAX,MAAA,CAAAY,IAKxB,CAAC;IALqBvC,EAAE,CAAAgC,UAAA,yBAKE,CAAC;IALLhC,EAAE,CAAAwC,WAAA;EAAA;AAAA;AAAA,SAAAC,kCAAAvB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFlB,EAAE,CAAA0C,UAAA,IAAAN,wCAAA,iBAKmC,CAAC;EAAA;EAAA,IAAAlB,EAAA;IAAA,MAAAS,MAAA,GALtC3B,EAAE,CAAA4B,aAAA;IAAF5B,EAAE,CAAAgC,UAAA,SAAAL,MAAA,CAAAY,IAKxC,CAAC;EAAA;AAAA;AAAA,SAAAI,0BAAAzB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IALqClB,EAAE,CAAAsB,cAAA,YAMZ,CAAC;IANStB,EAAE,CAAA4C,MAAA,EAMD,CAAC;IANF5C,EAAE,CAAA+B,YAAA,CAMK,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAS,MAAA,GANR3B,EAAE,CAAA4B,aAAA;IAAF5B,EAAE,CAAAwC,WAAA;IAAFxC,EAAE,CAAA6C,SAAA,CAMD,CAAC;IANF7C,EAAE,CAAA8C,iBAAA,CAAAnB,MAAA,CAAAoB,KAMD,CAAC;EAAA;AAAA;AAAA,SAAAC,yDAAA9B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA+B,GAAA,GANFjD,EAAE,CAAAqB,gBAAA;IAAFrB,EAAE,CAAAsB,cAAA,cAmB3E,CAAC;IAnBwEtB,EAAE,CAAAuB,UAAA,mBAAA2B,+EAAAzB,MAAA;MAAFzB,EAAE,CAAA0B,aAAA,CAAAuB,GAAA;MAAA,MAAAtB,MAAA,GAAF3B,EAAE,CAAA4B,aAAA;MAAA,OAAF5B,EAAE,CAAA6B,WAAA,CAe9DF,MAAA,CAAAwB,KAAA,CAAA1B,MAAY,CAAC;IAAA,EAAC,qBAAA2B,iFAAA3B,MAAA;MAf8CzB,EAAE,CAAA0B,aAAA,CAAAuB,GAAA;MAAA,MAAAtB,MAAA,GAAF3B,EAAE,CAAA4B,aAAA;MAAA,OAAF5B,EAAE,CAAA6B,WAAA,CAgB5DF,MAAA,CAAA0B,SAAA,CAAA5B,MAAgB,CAAC;IAAA,EAAC;IAhBwCzB,EAAE,CAAA+B,YAAA,CAmBpE,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAS,MAAA,GAnBiE3B,EAAE,CAAA4B,aAAA;IAAF5B,EAAE,CAAAsC,UAAA,CAAAX,MAAA,CAAA2B,UAYpD,CAAC;IAZiDtD,EAAE,CAAAgC,UAAA,iCAavC,CAAC;IAboChC,EAAE,CAAAwC,WAAA,gDAAAb,MAAA,CAAA4B,eAAA;EAAA;AAAA;AAAA,SAAAC,oEAAAtC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAuC,GAAA,GAAFzD,EAAE,CAAAqB,gBAAA;IAAFrB,EAAE,CAAAsB,cAAA,yBAoBsJ,CAAC;IApBzJtB,EAAE,CAAAuB,UAAA,mBAAAmC,qGAAAjC,MAAA;MAAFzB,EAAE,CAAA0B,aAAA,CAAA+B,GAAA;MAAA,MAAA9B,MAAA,GAAF3B,EAAE,CAAA4B,aAAA;MAAA,OAAF5B,EAAE,CAAA6B,WAAA,CAoBsDF,MAAA,CAAAwB,KAAA,CAAA1B,MAAY,CAAC;IAAA,EAAC,qBAAAkC,uGAAAlC,MAAA;MApBtEzB,EAAE,CAAA0B,aAAA,CAAA+B,GAAA;MAAA,MAAA9B,MAAA,GAAF3B,EAAE,CAAA4B,aAAA;MAAA,OAAF5B,EAAE,CAAA6B,WAAA,CAoBgFF,MAAA,CAAA0B,SAAA,CAAA5B,MAAgB,CAAC;IAAA,EAAC;IApBpGzB,EAAE,CAAA+B,YAAA,CAoBsJ,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAS,MAAA,GApBzJ3B,EAAE,CAAA4B,aAAA;IAAF5B,EAAE,CAAAsC,UAAA,sBAoBK,CAAC;IApBRtC,EAAE,CAAAwC,WAAA,gDAAAb,MAAA,CAAA4B,eAAA;EAAA;AAAA;AAAA,SAAAK,kDAAA1C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFlB,EAAE,CAAA6D,uBAAA,EAQtC,CAAC;IARmC7D,EAAE,CAAA0C,UAAA,IAAAM,wDAAA,kBAmB3E,CAAC,IAAAQ,mEAAA,6BACgO,CAAC;IApBzJxD,EAAE,CAAA8D,qBAAA;EAAA;EAAA,IAAA5C,EAAA;IAAA,MAAAS,MAAA,GAAF3B,EAAE,CAAA4B,aAAA;IAAF5B,EAAE,CAAA6C,SAAA,CAWvD,CAAC;IAXoD7C,EAAE,CAAAgC,UAAA,SAAAL,MAAA,CAAA2B,UAWvD,CAAC;IAXoDtD,EAAE,CAAA6C,SAAA,CAoB5B,CAAC;IApByB7C,EAAE,CAAAgC,UAAA,UAAAL,MAAA,CAAA2B,UAoB5B,CAAC;EAAA;AAAA;AAAA,SAAAS,0DAAA7C,EAAA,EAAAC,GAAA;AAAA,SAAA6C,4CAAA9C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApByBlB,EAAE,CAAA0C,UAAA,IAAAqB,yDAAA,qBAuBxB,CAAC;EAAA;AAAA;AAAA,SAAAE,0CAAA/C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAgD,GAAA,GAvBqBlE,EAAE,CAAAqB,gBAAA;IAAFrB,EAAE,CAAAsB,cAAA,cAsBwI,CAAC;IAtB3ItB,EAAE,CAAAuB,UAAA,mBAAA4C,gEAAA1C,MAAA;MAAFzB,EAAE,CAAA0B,aAAA,CAAAwC,GAAA;MAAA,MAAAvC,MAAA,GAAF3B,EAAE,CAAA4B,aAAA;MAAA,OAAF5B,EAAE,CAAA6B,WAAA,CAsB0CF,MAAA,CAAAwB,KAAA,CAAA1B,MAAY,CAAC;IAAA,EAAC,qBAAA2C,kEAAA3C,MAAA;MAtB1DzB,EAAE,CAAA0B,aAAA,CAAAwC,GAAA;MAAA,MAAAvC,MAAA,GAAF3B,EAAE,CAAA4B,aAAA;MAAA,OAAF5B,EAAE,CAAA6B,WAAA,CAsBoEF,MAAA,CAAA0B,SAAA,CAAA5B,MAAgB,CAAC;IAAA,EAAC;IAtBxFzB,EAAE,CAAA0C,UAAA,IAAAsB,2CAAA,gBAuBxB,CAAC;IAvBqBhE,EAAE,CAAA+B,YAAA,CAwBzE,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAS,MAAA,GAxBsE3B,EAAE,CAAA4B,aAAA;IAAF5B,EAAE,CAAAwC,WAAA,gDAAAb,MAAA,CAAA4B,eAAA;IAAFvD,EAAE,CAAA6C,SAAA,CAuB1B,CAAC;IAvBuB7C,EAAE,CAAAgC,UAAA,qBAAAL,MAAA,CAAA0C,kBAuB1B,CAAC;EAAA;AAAA;AAAA,SAAAC,mCAAApD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvBuBlB,EAAE,CAAA6D,uBAAA,EAOpD,CAAC;IAPiD7D,EAAE,CAAA0C,UAAA,IAAAkB,iDAAA,yBAQtC,CAAC,IAAAK,yCAAA,kBAc6K,CAAC;IAtB3IjE,EAAE,CAAA8D,qBAAA;EAAA;EAAA,IAAA5C,EAAA;IAAA,MAAAS,MAAA,GAAF3B,EAAE,CAAA4B,aAAA;IAAF5B,EAAE,CAAA6C,SAAA,CAQxC,CAAC;IARqC7C,EAAE,CAAAgC,UAAA,UAAAL,MAAA,CAAA0C,kBAQxC,CAAC;IARqCrE,EAAE,CAAA6C,SAAA,CAsBjD,CAAC;IAtB8C7C,EAAE,CAAAgC,UAAA,SAAAL,MAAA,CAAA0C,kBAsBjD,CAAC;EAAA;AAAA;AAAA,SAAAE,oBAAArD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtB8ClB,EAAE,CAAAsB,cAAA,YAEwF,CAAC;IAF3FtB,EAAE,CAAAwE,YAAA,EAG3D,CAAC;IAHwDxE,EAAE,CAAA0C,UAAA,IAAAzB,yBAAA,gBAIW,CAAC,IAAAwB,iCAAA,gCAJdzC,EAAE,CAAAyE,sBAKzD,CAAC,IAAA9B,yBAAA,gBAC4C,CAAC,IAAA2B,kCAAA,yBACzC,CAAC;IAPiDtE,EAAE,CAAA+B,YAAA,CA0BlF,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAwD,eAAA,GA1B+E1E,EAAE,CAAA2E,WAAA;IAAA,MAAAhD,MAAA,GAAF3B,EAAE,CAAA4B,aAAA;IAAF5B,EAAE,CAAAsC,UAAA,CAAAX,MAAA,CAAAiD,UAElC,CAAC;IAF+B5E,EAAE,CAAAgC,UAAA,YAAAL,MAAA,CAAAkD,cAAA,EAEvD,CAAC,YAAAlD,MAAA,CAAAmD,KAAsC,CAAC;IAFa9E,EAAE,CAAAwC,WAAA,uCAAAb,MAAA,CAAAoB,KAAA;IAAF/C,EAAE,CAAA6C,SAAA,EAInD,CAAC;IAJgD7C,EAAE,CAAAgC,UAAA,SAAAL,MAAA,CAAAM,KAInD,CAAC,aAAAyC,eAAgB,CAAC;IAJ+B1E,EAAE,CAAA6C,SAAA,EAM/C,CAAC;IAN4C7C,EAAE,CAAAgC,UAAA,SAAAL,MAAA,CAAAoB,KAM/C,CAAC;IAN4C/C,EAAE,CAAA6C,SAAA,CAOtD,CAAC;IAPmD7C,EAAE,CAAAgC,UAAA,SAAAL,MAAA,CAAAoD,SAOtD,CAAC;EAAA;AAAA;AAjG1C,MAAMC,IAAI,CAAC;EACP;AACJ;AACA;AACA;EACIjC,KAAK;EACL;AACJ;AACA;AACA;EACIR,IAAI;EACJ;AACJ;AACA;AACA;EACIN,KAAK;EACL;AACJ;AACA;AACA;EACIE,GAAG;EACH;AACJ;AACA;AACA;EACI2C,KAAK;EACL;AACJ;AACA;AACA;EACIF,UAAU;EACV;AACJ;AACA;AACA;EACIG,SAAS,GAAG,KAAK;EACjB;AACJ;AACA;AACA;EACIzB,UAAU;EACV;AACJ;AACA;AACA;AACA;EACI2B,QAAQ,GAAG,IAAIhF,YAAY,CAAC,CAAC;EAC7B;AACJ;AACA;AACA;AACA;EACIiF,YAAY,GAAG,IAAIjF,YAAY,CAAC,CAAC;EACjCkF,MAAM,GAAGjF,MAAM,CAACS,aAAa,CAAC;EAC9ByE,OAAO,GAAG,IAAI;EACdf,kBAAkB;EAClB,IAAId,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC4B,MAAM,CAACE,cAAc,CAACzE,eAAe,CAAC0E,IAAI,CAAC,CAAC,aAAa,CAAC;EAC1E;EACAC,SAAS;EACTC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACD,SAAS,CAACE,OAAO,CAAEC,IAAI,IAAK;MAC7B,QAAQA,IAAI,CAACC,OAAO,CAAC,CAAC;QAClB,KAAK,YAAY;UACb,IAAI,CAACtB,kBAAkB,GAAGqB,IAAI,CAACE,QAAQ;UACvC;QACJ;UACI,IAAI,CAACvB,kBAAkB,GAAGqB,IAAI,CAACE,QAAQ;UACvC;MACR;IACJ,CAAC,CAAC;EACN;EACAf,cAAcA,CAAA,EAAG;IACb,OAAO;MACH,oBAAoB,EAAE,IAAI;MAC1B,cAAc,EAAE,IAAI,CAAC5C,KAAK,IAAI;IAClC,CAAC;EACL;EACAkB,KAAKA,CAAC0C,KAAK,EAAE;IACT,IAAI,CAACT,OAAO,GAAG,KAAK;IACpB,IAAI,CAACH,QAAQ,CAACa,IAAI,CAACD,KAAK,CAAC;EAC7B;EACAxC,SAASA,CAACwC,KAAK,EAAE;IACb,IAAIA,KAAK,CAACE,GAAG,KAAK,OAAO,IAAIF,KAAK,CAACE,GAAG,KAAK,WAAW,EAAE;MACpD,IAAI,CAAC5C,KAAK,CAAC0C,KAAK,CAAC;IACrB;EACJ;EACA/D,UAAUA,CAAC+D,KAAK,EAAE;IACd,IAAI,CAACX,YAAY,CAACY,IAAI,CAACD,KAAK,CAAC;EACjC;EACA,OAAOG,IAAI,YAAAC,aAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFlB,IAAI;EAAA;EACvG,OAAOmB,IAAI,kBAD8EnG,EAAE,CAAAoG,iBAAA;IAAAC,IAAA,EACJrB,IAAI;IAAAsB,SAAA;IAAAC,cAAA,WAAAC,oBAAAtF,EAAA,EAAAC,GAAA,EAAAsF,QAAA;MAAA,IAAAvF,EAAA;QADFlB,EAAE,CAAA0G,cAAA,CAAAD,QAAA,EACmX5F,aAAa;MAAA;MAAA,IAAAK,EAAA;QAAA,IAAAyF,EAAA;QADlY3G,EAAE,CAAA4G,cAAA,CAAAD,EAAA,GAAF3G,EAAE,CAAA6G,WAAA,QAAA1F,GAAA,CAAAoE,SAAA,GAAAoB,EAAA;MAAA;IAAA;IAAAG,SAAA;IAAAC,MAAA;MAAAhE,KAAA;MAAAR,IAAA;MAAAN,KAAA;MAAAE,GAAA;MAAA2C,KAAA;MAAAF,UAAA;MAAAG,SAAA,GAAF/E,EAAE,CAAAgH,YAAA,CAAAC,0BAAA,4BAC0K9G,gBAAgB;MAAAmD,UAAA;IAAA;IAAA4D,OAAA;MAAAjC,QAAA;MAAAC,YAAA;IAAA;IAAAiC,QAAA,GAD5LnH,EAAE,CAAAoH,wBAAA;IAAAC,kBAAA,EAAArG,GAAA;IAAAsG,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAA5B,QAAA,WAAA6B,cAAAvG,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFlB,EAAE,CAAA0H,eAAA;QAAF1H,EAAE,CAAA0C,UAAA,IAAA6B,mBAAA,iBAEwF,CAAC;MAAA;MAAA,IAAArD,EAAA;QAF3FlB,EAAE,CAAAgC,UAAA,SAAAb,GAAA,CAAAiE,OAED,CAAC;MAAA;IAAA;IAAAuC,YAAA,EAAAA,CAAA,MAyB4M7H,EAAE,CAAC8H,OAAO,EAAyG9H,EAAE,CAAC+H,IAAI,EAAkH/H,EAAE,CAACgI,gBAAgB,EAAyKhI,EAAE,CAACiI,OAAO,EAAgGhH,eAAe;IAAAiH,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AAC50B;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA7B6FnI,EAAE,CAAAoI,iBAAA,CA6BJpD,IAAI,EAAc,CAAC;IAClGqB,IAAI,EAAEjG,SAAS;IACfiI,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,QAAQ;MAAE1C,QAAQ,EAAG;AACpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAEsC,eAAe,EAAE7H,uBAAuB,CAACkI,MAAM;MAAEN,aAAa,EAAE3H,iBAAiB,CAACkI,IAAI;MAAEC,IAAI,EAAE;QAC7EC,KAAK,EAAE;MACX,CAAC;MAAEV,MAAM,EAAE,CAAC,0MAA0M;IAAE,CAAC;EACrO,CAAC,CAAC,QAAkB;IAAEjF,KAAK,EAAE,CAAC;MACtBsD,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAEgC,IAAI,EAAE,CAAC;MACP8D,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAE0B,KAAK,EAAE,CAAC;MACRoE,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAE4B,GAAG,EAAE,CAAC;MACNkE,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAEuE,KAAK,EAAE,CAAC;MACRuB,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAEqE,UAAU,EAAE,CAAC;MACbyB,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAEwE,SAAS,EAAE,CAAC;MACZsB,IAAI,EAAE9F,KAAK;MACX8H,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAExI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEmD,UAAU,EAAE,CAAC;MACb+C,IAAI,EAAE9F;IACV,CAAC,CAAC;IAAE0E,QAAQ,EAAE,CAAC;MACXoB,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAE0E,YAAY,EAAE,CAAC;MACfmB,IAAI,EAAE7F;IACV,CAAC,CAAC;IAAE+E,SAAS,EAAE,CAAC;MACZc,IAAI,EAAE5F,eAAe;MACrB4H,IAAI,EAAE,CAACxH,aAAa;IACxB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAM+H,UAAU,CAAC;EACb,OAAO5C,IAAI,YAAA6C,mBAAA3C,CAAA;IAAA,YAAAA,CAAA,IAAwF0C,UAAU;EAAA;EAC7G,OAAOE,IAAI,kBAvF8E9I,EAAE,CAAA+I,gBAAA;IAAA1C,IAAA,EAuFSuC;EAAU;EAC9G,OAAOI,IAAI,kBAxF8EhJ,EAAE,CAAAiJ,gBAAA;IAAAC,OAAA,GAwF+BnJ,YAAY,EAAEgB,eAAe,EAAED,YAAY,EAAEA,YAAY;EAAA;AACvL;AACA;EAAA,QAAAqH,SAAA,oBAAAA,SAAA,KA1F6FnI,EAAE,CAAAoI,iBAAA,CA0FJQ,UAAU,EAAc,CAAC;IACxGvC,IAAI,EAAE3F,QAAQ;IACd2H,IAAI,EAAE,CAAC;MACCa,OAAO,EAAE,CAACnJ,YAAY,EAAEgB,eAAe,EAAED,YAAY,CAAC;MACtDqI,OAAO,EAAE,CAACnE,IAAI,EAAElE,YAAY,CAAC;MAC7BsI,YAAY,EAAE,CAACpE,IAAI;IACvB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASA,IAAI,EAAE4D,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}