{"ast": null, "code": "import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { take } from 'rxjs/operators';\nimport { PackageType } from 'src/app/core/models/package.model';\nimport { slideInOut } from 'src/app/helpers/my-animations';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/core/services/classroom.service\";\nimport * as i4 from \"src/app/core/services/package.service\";\nimport * as i5 from \"src/app/core/services/general.service\";\nimport * as i6 from \"@angular/platform-browser\";\nimport * as i7 from \"primeng/api\";\nimport * as i8 from \"@angular/forms\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"../packages-progress/packages-progress.component\";\nconst _c0 = [\"everypay\"];\nfunction ExtendPackageComponent_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Expiration Date: \");\n    i0.ɵɵelementStart(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(4, 1, ctx_r1.activePackages[0].expiresOn, \"dd-MM-yyyy\"));\n  }\n}\nfunction ExtendPackageComponent_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵlistener(\"click\", function ExtendPackageComponent_div_30_Template_div_click_0_listener() {\n      const ctx_r3 = i0.ɵɵrestoreView(_r3);\n      const hour_r5 = ctx_r3.$implicit;\n      const i_r6 = ctx_r3.index;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.selectHours(hour_r5, i_r6));\n    });\n    i0.ɵɵelementStart(1, \"div\", 45);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 46);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const hour_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵpropertyInterpolate1(\"id\", \"hour-btn-\", hour_r5.price, \"\");\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.getBtnStyle(true));\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"id\", \"hour-btn-hours-\", hour_r5.price, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", hour_r5.extendTime, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"id\", \"hour-btn-per-\", hour_r5.price, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", hour_r5.price, \"\\u20AC \");\n  }\n}\nfunction ExtendPackageComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtext(1, \"* First Name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExtendPackageComponent_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtext(1, \"* Last Name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExtendPackageComponent_ng_template_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵelement(1, \"img\", 49);\n    i0.ɵɵelementStart(2, \"div\", 50);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const country_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", country_r7.image, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(country_r7.name);\n  }\n}\nfunction ExtendPackageComponent_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtext(1, \"* Country is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExtendPackageComponent_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 51);\n  }\n}\nfunction ExtendPackageComponent_div_64_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtext(1, \"* Tax Office is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExtendPackageComponent_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22);\n    i0.ɵɵtext(2, \"* Tax Office\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 32);\n    i0.ɵɵtemplate(4, ExtendPackageComponent_div_64_div_4_Template, 2, 0, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.isSubmitted || ctx_r1.errorControl.tax.touched) && (ctx_r1.errorControl.tax.errors == null ? null : ctx_r1.errorControl.tax.errors.required));\n  }\n}\nfunction ExtendPackageComponent_div_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtext(1, \"* Street Adrdess is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExtendPackageComponent_div_74_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtext(1, \"* Street Number is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExtendPackageComponent_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtext(1, \"* City is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExtendPackageComponent_div_84_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtext(1, \"* Postcode is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExtendPackageComponent_div_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtext(1, \"* Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExtendPackageComponent_div_94_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtext(1, \"* Phone is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExtendPackageComponent_div_95_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtext(1, \"* T.I.N. / V.A.T. is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ExtendPackageComponent_div_95_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22);\n    i0.ɵɵtext(2, \"* T.I.N. / V.A.T.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 52);\n    i0.ɵɵtemplate(4, ExtendPackageComponent_div_95_div_4_Template, 2, 0, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.isSubmitted || ctx_r1.errorControl.tin.touched) && (ctx_r1.errorControl.tin.errors == null ? null : ctx_r1.errorControl.tin.errors.required));\n  }\n}\nexport class ExtendPackageComponent {\n  constructor(location, activatedRoute, classroomService, packageService, generalService, sanitizer, router) {\n    this.location = location;\n    this.activatedRoute = activatedRoute;\n    this.classroomService = classroomService;\n    this.packageService = packageService;\n    this.generalService = generalService;\n    this.sanitizer = sanitizer;\n    this.router = router;\n    this.subs = new SubSink();\n    this.classroomId = \"\";\n    this.activePackages = [];\n    this.selectedHoursIndex = 0;\n    this.selectedPackageToBuy = {};\n    this.classroom = {};\n    this.mltPackagesToBuy = [];\n    this.form = new UntypedFormGroup({});\n    this.buyerUserDetails = {};\n    this.mltPricesExtend = [];\n    this.countries = this.generalService.countries;\n    this.everypayLink = 'https://mlt.ui.kiddobrains.com/extention-payment.html?guid=guid';\n    this.isReceipt = true;\n    this.isInvoice = false;\n    this.isSubmitted = false;\n  }\n  ngOnInit() {\n    this.packageService.getExtentionOptions().pipe(take(1)).subscribe(res => {\n      this.mltPricesExtend = res;\n    });\n    this.classroomId = this.activatedRoute.snapshot.paramMap.get(\"classroom_id\");\n    this.subs.sink = this.classroomService.getClassroom(this.classroomId).subscribe(res => {\n      this.classroom = res;\n      this.activePackages.push(res.activePackage);\n      this.selectedPackageToBuy.type = res.activePackage?.type;\n    });\n    this.packageService.getPackagesToBuy().pipe(take(1)).subscribe(res => {\n      this.mltPackagesToBuy = res;\n    });\n    this.initializeForm();\n    this.everypayLink = this.transform('');\n  }\n  transform(guid) {\n    return this.sanitizer.bypassSecurityTrustResourceUrl('https://mlt.ui.kiddobrains.com/extention-payment.html?guid=' + guid + \"&amount=\" + this.mltPricesExtend[this.selectedHoursIndex]?.price);\n  }\n  selectHours(hour, i) {\n    this.deSelectedBtnStyle(`hour-btn-${this.mltPricesExtend[this.selectedHoursIndex].price}`, `hour-btn-hours-${this.mltPricesExtend[this.selectedHoursIndex].price}`);\n    this.deSelectedBtnStyle(`hour-btn-${this.mltPricesExtend[this.selectedHoursIndex].price}`, `hour-btn-per-${this.mltPricesExtend[this.selectedHoursIndex].price}`);\n    this.selectedHoursIndex = i;\n    this.selectedBtnStyle(`hour-btn-${this.mltPricesExtend[this.selectedHoursIndex].price}`, `hour-btn-hours-${this.mltPricesExtend[this.selectedHoursIndex].price}`);\n    this.selectedBtnStyle(`hour-btn-${this.mltPricesExtend[this.selectedHoursIndex].price}`, `hour-btn-per-${this.mltPricesExtend[this.selectedHoursIndex].price}`);\n  }\n  deSelectedBtnStyle(id, extraId) {\n    let btn = document.getElementById(id);\n    btn.style.color = \"#A4A2E6\";\n    btn.style.backgroundColor = \"white\";\n    btn.style.background = \"white\";\n    if (extraId) document.getElementById(extraId).style.color = \"#2d2a4b\";\n  }\n  selectedBtnStyle(id, extraId) {\n    let btn = document.getElementById(id);\n    btn.style.backgroundColor = this.getCasualColor(this.selectedPackageToBuy.type);\n    btn.style.color = \"white\";\n    if (extraId) document.getElementById(extraId).style.color = \"white\";\n  }\n  getCasualColor(type) {\n    if (type == PackageType.REGULAR) {\n      return \"#A4A2E6\";\n    }\n    if (type == PackageType.FLEX) {\n      return \"#4040CD\";\n    }\n    if (type == PackageType.PREMIUM) {\n      return \"#B5179E\";\n    }\n    return \"\";\n  }\n  getBtnStyle(notToBeDisabled = false) {\n    return {\n      'border': `1px solid ${this.getCasualColor(this.selectedPackageToBuy.type)}`,\n      'color': `${this.getCasualColor(this.selectedPackageToBuy.type)}`\n    };\n  }\n  goBack() {\n    this.location.back();\n  }\n  getRadioStyle(packageType) {\n    if (this.selectedPackageToBuy.type == packageType.type) {\n      return {\n        'border': `3px solid ${this.getCasualColor(this.selectedPackageToBuy.type)}`,\n        'background': `${this.getCasualColor(this.selectedPackageToBuy.type)}`\n      };\n    }\n    return {\n      'border': \"2px solid #707070\",\n      'background': \"white\"\n    };\n  }\n  getTotalPriceOfPackage(packageToBuy) {\n    return packageToBuy.priceHourly[this.selectedHoursIndex].price + packageToBuy.costPlus;\n  }\n  chooseReceipt() {\n    this.isReceipt = true;\n    this.isInvoice = false;\n  }\n  chooseInvoice() {\n    this.isReceipt = false;\n    this.isInvoice = true;\n  }\n  initializeForm() {\n    this.form = new UntypedFormGroup({\n      fname: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      }),\n      lname: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      }),\n      company: new UntypedFormControl(null, {}),\n      profession: new UntypedFormControl(null, {}),\n      country: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      }),\n      street: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      }),\n      number: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      }),\n      city: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      }),\n      postcode: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      }),\n      tax: new UntypedFormControl(null, {}),\n      email: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      }),\n      phone: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      }),\n      tin: new UntypedFormControl(null, {})\n    });\n  }\n  get errorControl() {\n    return this.form.controls;\n  }\n  purchase() {\n    this.isSubmitted = true;\n    if (this.selectedHoursIndex == 0) {\n      return;\n    }\n    if (this.form.valid) {\n      let formValue = this.form.value;\n      this.buyerUserDetails.fname = formValue.fname;\n      this.buyerUserDetails.lname = formValue.lname;\n      this.buyerUserDetails.email = formValue.email;\n      this.buyerUserDetails.city = formValue.city;\n      this.buyerUserDetails.company = formValue.company;\n      this.buyerUserDetails.streetName = formValue.street;\n      this.buyerUserDetails.streetNumber = formValue.number;\n      this.buyerUserDetails.tax = formValue.tax;\n      this.buyerUserDetails.profession = formValue.profession;\n      this.buyerUserDetails.postcode = formValue.postcode;\n      this.buyerUserDetails.tin = formValue.tin;\n      this.buyerUserDetails.phone = formValue.phone;\n      this.buyerUserDetails.country = formValue.country.name;\n    } else {\n      return;\n    }\n    let req = {\n      packageId: this.activePackages[0].id,\n      extetionTime: this.mltPricesExtend[this.selectedHoursIndex].extendTime,\n      buyerUserDetails: this.buyerUserDetails\n    };\n    this.packageService.buyExtension(req).pipe(take(1)).subscribe(res => {\n      this.everypayLink = this.transform(res.result);\n      this.generalService.slideNativeElements(true, this.everypay.nativeElement);\n    });\n  }\n  closePayment() {\n    this.generalService.slideNativeElements(false, this.everypay.nativeElement);\n    this.router.navigate(['dashboard']);\n  }\n  static #_ = this.ɵfac = function ExtendPackageComponent_Factory(t) {\n    return new (t || ExtendPackageComponent)(i0.ɵɵdirectiveInject(i1.Location), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ClassroomService), i0.ɵɵdirectiveInject(i4.PackageService), i0.ɵɵdirectiveInject(i5.GeneralService), i0.ɵɵdirectiveInject(i6.DomSanitizer), i0.ɵɵdirectiveInject(i2.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ExtendPackageComponent,\n    selectors: [[\"app-extend-package\"]],\n    viewQuery: function ExtendPackageComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.everypay = _t.first);\n      }\n    },\n    decls: 106,\n    vars: 22,\n    consts: [[\"everypay\", \"\"], [1, \"buy-package\"], [\"id\", \"class\"], [1, \"buy-package-header\"], [1, \"back-button\", 2, \"position\", \"absolute\", 3, \"click\"], [\"src\", \"/assets/icons/back-main-color.svg\"], [2, \"margin-left\", \"20px\"], [1, \"buy-package-title\", 2, \"width\", \"100%\"], [2, \"background\", \"white\", \"border-radius\", \"24px\"], [1, \"section\"], [2, \"font-weight\", \"bold\", \"padding\", \"10px 0\"], [2, \"width\", \"81%\", \"margin-left\", \"auto\"], [3, \"packages\"], [2, \"display\", \"flex\", \"justify-content\", \"space-between\", \"font-size\", \"13px\", \"color\", \"#93949E\", \"padding\", \"10px 20px\"], [4, \"ngIf\"], [1, \"top-btns\", \"top-btns-hours\"], [\"class\", \"btn btn-hours hvr-glow\", \"style\", \"height:100px;\", 3, \"ngStyle\", \"id\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"left\"], [2, \"font-weight\", \"bold\", \"padding\", \"10px 5px\"], [3, \"formGroup\"], [1, \"input-fields\"], [1, \"input-field\"], [1, \"input-element-title\"], [\"formControlName\", \"fname\", \"type\", \"text\", 1, \"input-element\"], [\"class\", \"input-error\", 4, \"ngIf\"], [\"formControlName\", \"lname\", \"type\", \"text\", 1, \"input-element\"], [\"formControlName\", \"company\", \"type\", \"text\", 1, \"input-element\"], [\"formControlName\", \"profession\", \"type\", \"text\", 1, \"input-element\"], [\"autocomplete\", \"autocomplete_off_hack_xfr4!k\", \"optionLabel\", \"name\", \"filterBy\", \"name\", \"placeholder\", \"Select Country\", \"formControlName\", \"country\", 3, \"options\", \"showClear\"], [\"pTemplate\", \"item\"], [\"style\", \"width:100%\", 4, \"ngIf\"], [\"class\", \"input-field\", 4, \"ngIf\"], [\"formControlName\", \"street\", \"type\", \"text\", 1, \"input-element\"], [\"formControlName\", \"number\", \"type\", \"text\", 1, \"input-element\"], [\"formControlName\", \"city\", \"type\", \"text\", 1, \"input-element\"], [\"formControlName\", \"postcode\", \"type\", \"text\", 1, \"input-element\"], [\"formControlName\", \"email\", \"type\", \"text\", 1, \"input-element\"], [\"formControlName\", \"phone\", \"type\", \"text\", 1, \"input-element\"], [1, \"btns\"], [1, \"main-color-button\", 3, \"click\"], [1, \"modal\", \"no-visibility\"], [1, \"popup-title\"], [\"src\", \"/assets/icons/close.png\", 1, \"close-img\", \"close-img-abs\", \"hvr-glow\", 3, \"click\"], [\"height\", \"800px\", \"title\", \"Package Payment\", \"frameborder\", \"0\", \"allowfullscreen\", \"\", 2, \"width\", \"100%\", \"display\", \"flex\", \"justify-content\", \"center\", 3, \"src\"], [1, \"btn\", \"btn-hours\", \"hvr-glow\", 2, \"height\", \"100px\", 3, \"click\", \"ngStyle\", \"id\"], [1, \"hours-info\", 3, \"id\"], [1, \"price\", 3, \"id\"], [1, \"input-error\"], [1, \"country-item\"], [3, \"src\"], [1, \"country-name\"], [2, \"width\", \"100%\"], [\"formControlName\", \"tin\", \"type\", \"text\", 1, \"input-element\"]],\n    template: function ExtendPackageComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4);\n        i0.ɵɵlistener(\"click\", function ExtendPackageComponent_Template_div_click_3_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.goBack());\n        });\n        i0.ɵɵelement(4, \"img\", 5);\n        i0.ɵɵelementStart(5, \"div\", 6);\n        i0.ɵɵtext(6, \"Back\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"div\", 7);\n        i0.ɵɵtext(8, \" Extend Package \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(9, \"div\", 8)(10, \"div\", 9)(11, \"div\", 10);\n        i0.ɵɵtext(12, \" Active Package \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"div\", 11);\n        i0.ɵɵelement(14, \"app-packages-progress\", 12);\n        i0.ɵɵelementStart(15, \"div\", 13)(16, \"div\");\n        i0.ɵɵtext(17, \" Language: \");\n        i0.ɵɵelementStart(18, \"strong\");\n        i0.ɵɵtext(19);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(20, \"div\");\n        i0.ɵɵtext(21, \" Level: \");\n        i0.ɵɵelementStart(22, \"strong\");\n        i0.ɵɵtext(23);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(24, ExtendPackageComponent_div_24_Template, 5, 4, \"div\", 14);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(25, \"div\", 9)(26, \"div\", 10);\n        i0.ɵɵtext(27, \" Select Extention \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"div\", 11)(29, \"div\", 15);\n        i0.ɵɵtemplate(30, ExtendPackageComponent_div_30_Template, 5, 9, \"div\", 16);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(31, \"div\", 17)(32, \"div\", 18);\n        i0.ɵɵtext(33, \" Add Billing Details \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(34, \"form\", 19)(35, \"div\", 20)(36, \"div\", 21);\n        i0.ɵɵtext(37, \" Select Billing Type \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(38, \"div\", 21);\n        i0.ɵɵelementStart(39, \"div\", 21)(40, \"div\", 22);\n        i0.ɵɵtext(41, \"* First Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(42, \"input\", 23);\n        i0.ɵɵtemplate(43, ExtendPackageComponent_div_43_Template, 2, 0, \"div\", 24);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(44, \"div\", 21)(45, \"div\", 22);\n        i0.ɵɵtext(46, \"* Last Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(47, \"input\", 25);\n        i0.ɵɵtemplate(48, ExtendPackageComponent_div_48_Template, 2, 0, \"div\", 24);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(49, \"div\", 21)(50, \"div\", 22);\n        i0.ɵɵtext(51, \"Company (optional)\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(52, \"input\", 26);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(53, \"div\", 21)(54, \"div\", 22);\n        i0.ɵɵtext(55, \"Profession (optional)\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(56, \"input\", 27);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(57, \"div\", 21)(58, \"div\", 22);\n        i0.ɵɵtext(59, \"* Country\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(60, \"p-dropdown\", 28);\n        i0.ɵɵtemplate(61, ExtendPackageComponent_ng_template_61_Template, 4, 2, \"ng-template\", 29);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(62, ExtendPackageComponent_div_62_Template, 2, 0, \"div\", 24);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(63, ExtendPackageComponent_div_63_Template, 1, 0, \"div\", 30)(64, ExtendPackageComponent_div_64_Template, 5, 1, \"div\", 31);\n        i0.ɵɵelementStart(65, \"div\", 21)(66, \"div\", 22);\n        i0.ɵɵtext(67, \"* Street Address\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(68, \"input\", 32);\n        i0.ɵɵtemplate(69, ExtendPackageComponent_div_69_Template, 2, 0, \"div\", 24);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(70, \"div\", 21)(71, \"div\", 22);\n        i0.ɵɵtext(72, \"* Street No\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(73, \"input\", 33);\n        i0.ɵɵtemplate(74, ExtendPackageComponent_div_74_Template, 2, 0, \"div\", 24);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(75, \"div\", 21)(76, \"div\", 22);\n        i0.ɵɵtext(77, \"* City\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(78, \"input\", 34);\n        i0.ɵɵtemplate(79, ExtendPackageComponent_div_79_Template, 2, 0, \"div\", 24);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(80, \"div\", 21)(81, \"div\", 22);\n        i0.ɵɵtext(82, \"* Postcode\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(83, \"input\", 35);\n        i0.ɵɵtemplate(84, ExtendPackageComponent_div_84_Template, 2, 0, \"div\", 24);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(85, \"div\", 21)(86, \"div\", 22);\n        i0.ɵɵtext(87, \"* Email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(88, \"input\", 36);\n        i0.ɵɵtemplate(89, ExtendPackageComponent_div_89_Template, 2, 0, \"div\", 24);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(90, \"div\", 21)(91, \"div\", 22);\n        i0.ɵɵtext(92, \"* Phone\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(93, \"input\", 37);\n        i0.ɵɵtemplate(94, ExtendPackageComponent_div_94_Template, 2, 0, \"div\", 24);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(95, ExtendPackageComponent_div_95_Template, 5, 1, \"div\", 31);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(96, \"div\", 38)(97, \"div\", 39);\n        i0.ɵɵlistener(\"click\", function ExtendPackageComponent_Template_div_click_97_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.purchase());\n        });\n        i0.ɵɵtext(98, \" Purchase \");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(99, \"div\", 40, 0)(101, \"div\", 41)(102, \"div\");\n        i0.ɵɵtext(103, \"Payment\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(104, \"img\", 42);\n        i0.ɵɵlistener(\"click\", function ExtendPackageComponent_Template_img_click_104_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.closePayment());\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(105, \"iframe\", 43);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"@slideInOut\", undefined);\n        i0.ɵɵadvance(14);\n        i0.ɵɵproperty(\"packages\", ctx.activePackages);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.classroom.language);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(ctx.classroom.activeLevel);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.activePackages[0]);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngForOf\", ctx.mltPricesExtend);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"formGroup\", ctx.form);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngIf\", (ctx.isSubmitted || ctx.errorControl.fname.touched) && (ctx.errorControl.fname.errors == null ? null : ctx.errorControl.fname.errors.required));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", (ctx.isSubmitted || ctx.errorControl.lname.touched) && (ctx.errorControl.lname.errors == null ? null : ctx.errorControl.lname.errors.required));\n        i0.ɵɵadvance(12);\n        i0.ɵɵproperty(\"options\", ctx.countries)(\"showClear\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", (ctx.isSubmitted || ctx.errorControl.country.touched) && (ctx.errorControl.country.errors == null ? null : ctx.errorControl.country.errors.required));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isReceipt);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isInvoice);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", (ctx.isSubmitted || ctx.errorControl.street.touched) && (ctx.errorControl.street.errors == null ? null : ctx.errorControl.street.errors.required));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", (ctx.isSubmitted || ctx.errorControl.number.touched) && (ctx.errorControl.number.errors == null ? null : ctx.errorControl.number.errors.required));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", (ctx.isSubmitted || ctx.errorControl.city.touched) && (ctx.errorControl.city.errors == null ? null : ctx.errorControl.city.errors.required));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", (ctx.isSubmitted || ctx.errorControl.postcode.touched) && (ctx.errorControl.postcode.errors == null ? null : ctx.errorControl.postcode.errors.required));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", (ctx.isSubmitted || ctx.errorControl.email.touched) && (ctx.errorControl.email.errors == null ? null : ctx.errorControl.email.errors.required));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", (ctx.isSubmitted || ctx.errorControl.phone.touched) && (ctx.errorControl.phone.errors == null ? null : ctx.errorControl.phone.errors.required));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isInvoice);\n        i0.ɵɵadvance(10);\n        i0.ɵɵproperty(\"src\", ctx.everypayLink, i0.ɵɵsanitizeResourceUrl);\n      }\n    },\n    dependencies: [i1.NgForOf, i1.NgIf, i1.NgStyle, i7.PrimeTemplate, i8.ɵNgNoValidate, i8.DefaultValueAccessor, i8.NgControlStatus, i8.NgControlStatusGroup, i8.FormGroupDirective, i8.FormControlName, i9.Dropdown, i10.PackagesProgressComponent, i1.DatePipe],\n    styles: [\"[_nghost-%COMP%]     .e-frame.e-check {\\n  background-color: var(--main-color) !important;\\n  width: 20px !important;\\n  height: 20px !important;\\n}\\n[_nghost-%COMP%]     .e-checkbox-wrapper .e-frame {\\n  width: 20px !important;\\n  height: 20px !important;\\n  line-height: 16px;\\n}\\n[_nghost-%COMP%]     .e-checkbox-wrapper .e-checkbox:focus + .e-frame {\\n  width: 20px !important;\\n  height: 20px !important;\\n}\\n[_nghost-%COMP%]     .e-icons.e-check:before {\\n  font-size: 13px !important;\\n}\\n[_nghost-%COMP%]     .e-radio:focus + label::before {\\n  border-color: var(--main-color) !important;\\n  box-shadow: none !important;\\n}\\n[_nghost-%COMP%]     .e-radio:focus + label::before {\\n  border-color: #757575 !important;\\n  box-shadow: none !important;\\n}\\n[_nghost-%COMP%]     .e-radio:hover + label::before {\\n  border-color: #757575 !important;\\n}\\n[_nghost-%COMP%]     .e-radio:checked + label::before {\\n  background-color: #fff !important;\\n  border-color: var(--main-color) !important;\\n}\\n[_nghost-%COMP%]     .e-radio:checked + label::after {\\n  background-color: var(--main-color) !important;\\n  color: var(--main-color) !important;\\n}\\n[_nghost-%COMP%]     .e-radio:checked + label:active .e-ripple-element {\\n  background-color: rgba(227, 22, 91, 0.26) !important;\\n}\\n[_nghost-%COMP%]     .e-radio:checked + .e-focus .e-ripple-container {\\n  background-color: rgba(227, 22, 91, 0.26) !important;\\n}\\n[_nghost-%COMP%]     .e-radio:checked + .e-focus::before {\\n  outline: #fff 0 solid !important;\\n  outline-offset: 0 !important;\\n}\\n[_nghost-%COMP%]     .e-radio:checked:focus + label::before {\\n  border-color: var(--main-color) !important;\\n}\\n[_nghost-%COMP%]     .e-radio:checked:focus + label::after {\\n  background-color: var(--main-color) !important;\\n}\\n[_nghost-%COMP%]     .e-radio:checked + label:hover::before {\\n  border-color: var(--main-color) !important;\\n}\\n[_nghost-%COMP%]     .e-radio:checked + label:hover::after {\\n  background-color: var(--main-color) !important;\\n}\\n[_nghost-%COMP%]     .e-radio + label::after {\\n  border: 2px solid !important;\\n  height: 12px !important;\\n  left: 3px !important;\\n  top: 3px !important;\\n  width: 12px !important;\\n}\\n[_nghost-%COMP%]     .e-label {\\n  font-size: 15px !important;\\n}\\n\\n.responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.buy-package[_ngcontent-%COMP%] {\\n  width: 100%;\\n  font-size: 17px;\\n  background: rgb(255, 255, 255);\\n  border-radius: 14px;\\n}\\n\\n.section[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  background-color: var(--white);\\n  border-radius: 14px;\\n  margin-top: 0;\\n  margin-bottom: 30px;\\n}\\n@media only screen and (min-width: 576px) {\\n  .section[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n  }\\n}\\n.section[_ngcontent-%COMP%]   .left[_ngcontent-%COMP%] {\\n  width: 100% !important;\\n  padding: 20px;\\n}\\n@media only screen and (min-width: 576px) {\\n  .section[_ngcontent-%COMP%]   .left[_ngcontent-%COMP%] {\\n    width: 70% !important;\\n  }\\n}\\n.section[_ngcontent-%COMP%]   .left[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  width: 100%;\\n  text-align: center;\\n  font-family: \\\"Proxima Nova Bold\\\";\\n}\\n.section[_ngcontent-%COMP%]   .right[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-top: -10px;\\n  padding: 20px;\\n  border-radius: 0 0 14px 0;\\n  color: white;\\n  transition: all 0.2s ease-in;\\n  position: sticky;\\n  background-image: linear-gradient(to bottom, rgb(117, 136, 244), rgb(95, 110, 209), rgb(73, 85, 175), rgb(50, 61, 142), rgb(27, 39, 110));\\n}\\n@media only screen and (min-width: 576px) {\\n  .section[_ngcontent-%COMP%]   .right[_ngcontent-%COMP%] {\\n    width: 30%;\\n  }\\n}\\n.section[_ngcontent-%COMP%]   .right[_ngcontent-%COMP%]   .seperator[_ngcontent-%COMP%] {\\n  border-top: 1px solid white;\\n  width: 100%;\\n  margin: 15px 0;\\n}\\n.section[_ngcontent-%COMP%]   .right[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-family: \\\"Proxima Nova Bold\\\";\\n}\\n.section[_ngcontent-%COMP%]   .right[_ngcontent-%COMP%]   .small-title[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  font-family: \\\"Proxima Nova Bold\\\";\\n}\\n.section[_ngcontent-%COMP%]   .right[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-top: 10px;\\n  font-size: 15px;\\n  text-align: start;\\n}\\n.section[_ngcontent-%COMP%]   .right[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .row-right[_ngcontent-%COMP%] {\\n  font-family: \\\"Proxima Nova Bold\\\";\\n}\\n.section[_ngcontent-%COMP%]   .right[_ngcontent-%COMP%]   .row-first[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n}\\n\\n.package-type[_ngcontent-%COMP%] {\\n  border-radius: 5px;\\n  margin-top: 20px;\\n  padding: 10px;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: space-between;\\n  align-items: center;\\n  border-color: transparent !important;\\n}\\n.package-type[_ngcontent-%COMP%]   .package-type-section[_ngcontent-%COMP%] {\\n  flex-direction: column;\\n  align-items: center;\\n  display: flex;\\n  background-color: rgba(204, 214, 255, 0.54);\\n  width: 100%;\\n  color: #3345A7;\\n  border-radius: 10px 10px 5px 5px;\\n}\\n.package-type[_ngcontent-%COMP%]   .package-type-section[_ngcontent-%COMP%]   .package-info[_ngcontent-%COMP%] {\\n  color: #000;\\n  font-size: 13px;\\n}\\n.package-type[_ngcontent-%COMP%]   .package-type-section[_ngcontent-%COMP%]   .my-radio[_ngcontent-%COMP%] {\\n  border: 2px solid #2f338d;\\n  border-radius: 50%;\\n  width: 36px;\\n  height: 36px;\\n  background-color: white;\\n  margin-left: 10px;\\n  background-clip: content-box !important;\\n  cursor: pointer;\\n  top: -3px;\\n  left: -15px;\\n  position: relative;\\n}\\n.package-type[_ngcontent-%COMP%]   .package-type-section[_ngcontent-%COMP%]   .type-name[_ngcontent-%COMP%] {\\n  color: white;\\n  padding: 5px 15px;\\n  font-size: 0.95rem;\\n  text-align: center;\\n  font-family: \\\"Proxima Nova Bold\\\";\\n  border-radius: 10px;\\n  width: 100%;\\n}\\n\\n.top-btns[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 100%;\\n  justify-content: center;\\n  margin: 20px 0;\\n  flex-wrap: wrap;\\n  box-sizing: border-box;\\n}\\n.top-btns[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease-in;\\n  padding: 2px;\\n  border-radius: 12px;\\n  margin: 10px;\\n  text-align: center;\\n  cursor: pointer;\\n}\\n.top-btns[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   .btn-language[_ngcontent-%COMP%] {\\n  font-family: \\\"Proxima Nova Bold\\\";\\n}\\n.top-btns[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   .btn-teacher-name[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: rgb(101, 122, 239);\\n  transition: all 0.2s ease-in;\\n}\\n.top-btns[_ngcontent-%COMP%]   .btn-classroom[_ngcontent-%COMP%] {\\n  height: 3rem;\\n  color: #3345a7;\\n}\\n.top-btns[_ngcontent-%COMP%]   .btn-classroom.new[_ngcontent-%COMP%] {\\n  background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0.33, #002ccf), color-stop(0.67, #a796ff));\\n}\\n.top-btns[_ngcontent-%COMP%]   .btn-classroom.new[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  background-color: var(--white);\\n  margin: 10px;\\n  width: 100%;\\n  min-height: 100%;\\n  border-radius: inherit;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n@media only screen and (min-width: 992px) and (min-width: 576px) {\\n  .top-btns[_ngcontent-%COMP%]   .btn-classroom.lg\\\\:col-3[_ngcontent-%COMP%] {\\n    width: 20% !important;\\n  }\\n}\\n.top-btns[_ngcontent-%COMP%]   .btn-hours[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  height: auto;\\n  width: 33%;\\n  transition: all 0.2s ease-in;\\n}\\n@media only screen and (min-width: 576px) {\\n  .top-btns[_ngcontent-%COMP%]   .btn-hours[_ngcontent-%COMP%] {\\n    width: 18%;\\n  }\\n}\\n.top-btns[_ngcontent-%COMP%]   .btn-hours[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%] {\\n  margin: 5px 0;\\n}\\n.top-btns[_ngcontent-%COMP%]   .btn-hours[_ngcontent-%COMP%]   .hours-info[_ngcontent-%COMP%] {\\n  transition: all 0.2s ease-in;\\n  color: #000;\\n}\\n.top-btns[_ngcontent-%COMP%]   .btn-new[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  font-family: \\\"Proxima Nova Bold\\\";\\n}\\n.top-btns[_ngcontent-%COMP%]   .btn-level[_ngcontent-%COMP%] {\\n  border-radius: 50%;\\n  width: 40px;\\n  height: 40px;\\n}\\n\\n.top-btns-hours[_ngcontent-%COMP%] {\\n  flex-wrap: wrap;\\n}\\n@media only screen and (min-width: 576px) {\\n  .top-btns-hours[_ngcontent-%COMP%] {\\n    flex-wrap: nowrap;\\n  }\\n}\\n\\n.buy-package-header[_ngcontent-%COMP%] {\\n  background-image: url(\\\"/assets/images/payment.png\\\");\\n  align-items: center;\\n  width: 100%;\\n  border: 0 !important;\\n  padding: 10px;\\n  box-sizing: border-box;\\n  position: sticky;\\n  padding: 0.5rem 1rem;\\n  border-top-left-radius: 4px;\\n  border-top-right-radius: 4px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  background-size: cover;\\n  background-repeat: no-repeat;\\n  background-position: center;\\n  border-radius: 12px;\\n  min-height: 37px;\\n  background-size: inherit;\\n  color: var(--white);\\n  font-family: \\\"Proxima Nova Regular\\\", sans-serif;\\n  letter-spacing: 0.05rem;\\n  z-index: 2;\\n}\\n.buy-package-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  cursor: pointer;\\n}\\n.buy-package-header[_ngcontent-%COMP%]   .buy-package-title[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.buy-package-header[_ngcontent-%COMP%]   .steps[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n.buy-package-header[_ngcontent-%COMP%]   .steps[_ngcontent-%COMP%]   .package-step[_ngcontent-%COMP%] {\\n  color: var(--white);\\n  text-align: center;\\n  transition: all 0.3s linear;\\n  width: 33px;\\n  height: 33px;\\n  line-height: 33px;\\n  border-radius: 50%;\\n  margin: 0 10px;\\n}\\n\\n[_nghost-%COMP%]     .e-label {\\n  font-size: 15px !important;\\n}\\n[_nghost-%COMP%]     .p-dropdown {\\n  width: 100%;\\n  padding: 4.5px;\\n  border-radius: 10px;\\n  margin-top: 10px;\\n}\\n[_nghost-%COMP%]     .dropdown-blue {\\n  width: 75px !important;\\n}\\n[_nghost-%COMP%]     .dropdown-blue .with-icon {\\n  padding-left: 0 !important;\\n}\\n[_nghost-%COMP%]     .fwidth .dropdown-blue {\\n  width: 100% !important;\\n}\\n\\n.country-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.country-item[_ngcontent-%COMP%]   .country-name[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n}\\n.country-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 40px;\\n}\\n\\n.disabled[_ngcontent-%COMP%] {\\n  background: white;\\n  color: lightgray;\\n  border: 1px solid lightgray;\\n}\\n\\n  .mat-tooltip {\\n  \\n\\n  \\n\\n  color: yellow;\\n  font-size: 15px;\\n  background-color: #707070;\\n}\\n\\n.review[_ngcontent-%COMP%] {\\n  padding: 30px;\\n  box-sizing: border-box;\\n}\\n.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%] {\\n  margin-top: 30px;\\n}\\n.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-title[_ngcontent-%COMP%] {\\n  color: var(--main-color);\\n  font-size: 20px;\\n  font-family: \\\"Proxima Nova Bold\\\";\\n}\\n.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%] {\\n  color: var(--main-color);\\n  font-size: 15px;\\n  display: flex;\\n  flex-wrap: wrap;\\n}\\n.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-25[_ngcontent-%COMP%], .review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-50[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-25[_ngcontent-%COMP%]   .info[_ngcontent-%COMP%], .review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-50[_ngcontent-%COMP%]   .info[_ngcontent-%COMP%] {\\n  color: #3345A7;\\n  font-family: \\\"Proxima Nova Bold\\\";\\n}\\n.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-25[_ngcontent-%COMP%]   .availability[_ngcontent-%COMP%], .review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-50[_ngcontent-%COMP%]   .availability[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-25[_ngcontent-%COMP%]   .availability[_ngcontent-%COMP%]   .col-50[_ngcontent-%COMP%], .review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-50[_ngcontent-%COMP%]   .availability[_ngcontent-%COMP%]   .col-50[_ngcontent-%COMP%] {\\n  width: 50%;\\n}\\n.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-25[_ngcontent-%COMP%] {\\n  width: 25%;\\n  flex-basis: 25%;\\n}\\n.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-50[_ngcontent-%COMP%] {\\n  width: 50%;\\n  flex-basis: 50%;\\n}\\n.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-100[_ngcontent-%COMP%] {\\n  width: 100%;\\n  flex-basis: 100%;\\n}\\n.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n\\n.btns[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 0px;\\n  background-color: white;\\n  border-bottom: 1px solid lightgray;\\n}\\n\\n.section-step-3[_ngcontent-%COMP%] {\\n  border-radius: 28px;\\n  background: linear-gradient(#fff 0%, rgba(56, 115, 244, 0.2) 100%);\\n  border-radius: 28px 28px 8px 8px;\\n}\\n\\n.section-content[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  transition: height 0.3s ease-out;\\n}\\n\\n.fixed-height[_ngcontent-%COMP%] {\\n  height: 200px !important;\\n  overflow-y: scroll !important;\\n}\\n\\n.section-step-3-title[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border: 1px solid #2E3D90;\\n  font-size: 15px;\\n  text-align: center;\\n  padding: 0.3rem 0;\\n  box-sizing: border-box;\\n  border-radius: 28px;\\n  position: relative;\\n  color: #2E3D90;\\n}\\n.section-step-3-title[_ngcontent-%COMP%]   .toggle-section[_ngcontent-%COMP%]   .section-arrow[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(to top, rgb(117, 136, 244), rgb(95, 110, 209), rgb(73, 85, 175), rgb(50, 61, 142), rgb(27, 39, 110));\\n  transform: rotate(180deg);\\n  position: absolute;\\n  top: 0;\\n  right: 0;\\n  overflow: hidden;\\n  transition-duration: 0.2s;\\n  transition-property: transform;\\n  cursor: pointer;\\n  border-radius: 50%;\\n  width: 1.8rem;\\n}\\n\\n.end[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  flex-direction: column;\\n  align-items: center;\\n  height: 470px;\\n}\\n.end[_ngcontent-%COMP%]   .end-msg[_ngcontent-%COMP%] {\\n  margin-top: 30px;\\n  font-family: \\\"Proxima Nova Bold\\\";\\n}\\n\\n.white-button[_ngcontent-%COMP%] {\\n  width: 200px;\\n  margin-top: 15px;\\n}\\n\\nbutton.p-element[_ngcontent-%COMP%] {\\n  color: var(--white);\\n}\\n\\n.cursor-none[_ngcontent-%COMP%] {\\n  cursor: default !important;\\n}\\n\\n.p-custom[_ngcontent-%COMP%] {\\n  padding: 0.3rem !important;\\n}\\n\\n.hvr-glow[_ngcontent-%COMP%]:hover, .hvr-glow[_ngcontent-%COMP%]:focus, .hvr-glow[_ngcontent-%COMP%]:active {\\n  box-shadow: 0 0 4px rgba(0, 0, 0, 0.46);\\n}\\n\\n.level[_ngcontent-%COMP%] {\\n  position: relative;\\n  color: #fff !important;\\n  font-weight: normal !important;\\n}\\n.level[_ngcontent-%COMP%]   div[_ngcontent-%COMP%] {\\n  font-family: \\\"Proxima Nova Regular\\\";\\n  z-index: 1;\\n}\\n.level[_ngcontent-%COMP%]:after {\\n  content: \\\" \\\";\\n  display: block;\\n  position: absolute;\\n  left: 0;\\n  top: 0;\\n  width: 100%;\\n  height: 100%;\\n  background-image: url(/assets/images/dashboard/goals/level-bubble.png) !important;\\n  background-repeat: no-repeat;\\n  background-position: 50% 0;\\n  background-size: 40px;\\n  z-index: 0;\\n  opacity: 0.8;\\n}\\n\\n.level-disabled[_ngcontent-%COMP%] {\\n  border: 1px solid #CCD6FF;\\n  background-image: none;\\n}\\n.level-disabled[_ngcontent-%COMP%]:after {\\n  opacity: 0.2;\\n}\\n\\n.level-default[_ngcontent-%COMP%] {\\n  border: 1px solid rgb(101, 122, 239);\\n  color: rgb(101, 122, 239);\\n  background-image: none;\\n}\\n\\n.level-selected[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 4px rgba(0, 0, 0, 0.46);\\n  margin: 4px;\\n  background-color: transparent;\\n  color: #fff;\\n  border: none;\\n  overflow: hidden;\\n  float: left;\\n  border-radius: 50%;\\n}\\n.level-selected[_ngcontent-%COMP%]:after {\\n  opacity: 1;\\n}\\n\\n.custom-border[_ngcontent-%COMP%] {\\n  border-radius: 0 0 10px 10px !important;\\n}\\n\\n.selected[_ngcontent-%COMP%]::before {\\n  content: url(/assets/icons/check-blue.svg);\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  top: 55%;\\n  z-index: 2;\\n}\\n\\n.input-fields[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%] {\\n  margin-top: 0 !important;\\n}\\n\\n.payment-step[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n}\\n\\n.prev-button-outlined[_ngcontent-%COMP%], .next-button-outlined[_ngcontent-%COMP%] {\\n  border-radius: 50px;\\n  background-color: #7082E6 !important;\\n}\\n.prev-button-outlined[_ngcontent-%COMP%]:hover, .next-button-outlined[_ngcontent-%COMP%]:hover {\\n  background-color: #7082E6 !important;\\n  box-shadow: none !important;\\n  color: #fff !important;\\n}\\n.prev-button-outlined.inactive[_ngcontent-%COMP%], .next-button-outlined.inactive[_ngcontent-%COMP%] {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n}\\n.prev-button-outlined.inactive[_ngcontent-%COMP%]:hover, .next-button-outlined.inactive[_ngcontent-%COMP%]:hover {\\n  color: #fff;\\n  border-color: #fff;\\n}\\n\\n.split-pay-button-outlined[_ngcontent-%COMP%] {\\n  border-radius: 50px;\\n  box-shadow: none;\\n  background-color: #2FB9D3 !important;\\n}\\n.split-pay-button-outlined[_ngcontent-%COMP%]:hover {\\n  background-color: #7082E6 !important;\\n  box-shadow: none !important;\\n  color: #fff !important;\\n}\\n.split-pay-button-outlined.inactive[_ngcontent-%COMP%] {\\n  opacity: 0.3;\\n  cursor: not-allowed;\\n}\\n.split-pay-button-outlined.inactive[_ngcontent-%COMP%]:hover {\\n  color: #fff;\\n  border-color: #fff;\\n}\\n\\n.buy-button[_ngcontent-%COMP%] {\\n  box-shadow: none !important;\\n  background: linear-gradient(#9baaff 0%, #152caf 100%);\\n  border-radius: 50px;\\n}\\n.buy-button[_ngcontent-%COMP%]:hover {\\n  background: linear-gradient(#152caf 0%, #9baaff 100%) !important;\\n  box-shadow: none !important;\\n  color: #fff !important;\\n}\\n\\n.input-element[_ngcontent-%COMP%] {\\n  font-size: 0.85rem !important;\\n  font-family: inherit;\\n  outline: none !important;\\n}\\n\\n.split-text[_ngcontent-%COMP%] {\\n  color: #CCD6FF;\\n}\\n\\n[_nghost-%COMP%]  .terms-checkbox .p-checkbox {\\n  width: 18px;\\n  height: 18px;\\n}\\n[_nghost-%COMP%]  .terms-checkbox .p-checkbox-label {\\n  font-size: 16px;\\n  cursor: pointer;\\n}\\n@media screen and (max-width: 768px) {\\n  [_nghost-%COMP%]  .terms-checkbox .p-checkbox-label {\\n    font-size: 14px;\\n  }\\n}\\n[_nghost-%COMP%]  .terms-checkbox .p-checkbox .p-checkbox-box.p-highlight .p-checkbox-icon.pi-check:before {\\n  top: 1px;\\n  left: -5px;\\n}\\n[_nghost-%COMP%]  .terms-checkbox .p-checkbox .p-checkbox-box {\\n  border-radius: 50px;\\n}\\n[_nghost-%COMP%]  .terms-checkbox .p-checkbox .p-checkbox-box.p-highlight {\\n  border-radius: 50px;\\n}\\n[_nghost-%COMP%]  .terms-checkbox .p-checkbox .p-checkbox-box .p-checkbox-icon {\\n  transform: scale(1.3);\\n}\\n[_nghost-%COMP%]  .terms-checkbox .p-component .p-checkbox-box {\\n  width: 18px !important;\\n  height: 18px !important;\\n}\\n@media only screen and (max-width: 768px) {\\n  .bottom-buttons[_ngcontent-%COMP%] {\\n    bottom: 0;\\n    left: 0;\\n    width: 100%;\\n    position: absolute;\\n    background-color: #2e3b8f;\\n  }\\n}\\n\\n.buy-package-header[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n.section[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  box-shadow: 0px 0px 0px white;\\n}\\n\\n.top-btns[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  flex-direction: column;\\n}\\n\\n.left[_ngcontent-%COMP%] {\\n  padding: 15px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hc3NldHMvc3R5bGVzL215LXJhZGlvLnNjc3MiLCJ3ZWJwYWNrOi8vLi9zcmMvYXBwL21vZHVsZXMvY2xhc3Nyb29tL3BhY2thZ2VzL2V4dGVuZC1wYWNrYWdlL2V4dGVuZC1wYWNrYWdlLmNvbXBvbmVudC5zY3NzIiwid2VicGFjazovLy4vc3JjL3Nhc3MvX21peGlucy5zY3NzIiwid2VicGFjazovLy4vc3JjL2FwcC9tb2R1bGVzL2NsYXNzcm9vbS9wYWNrYWdlcy9idXktcGFja2FnZS9idXktcGFja2FnZS5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFJRTtFQUNFLDhDQUFBO0VBQ0Esc0JBQUE7RUFDQSx1QkFBQTtBQ0hKO0FES0U7RUFDRSxzQkFBQTtFQUNBLHVCQUFBO0VBQ0EsaUJBQUE7QUNISjtBREtFO0VBQ0Usc0JBQUE7RUFDQSx1QkFBQTtBQ0hKO0FES0U7RUFDRSwwQkFBQTtBQ0hKO0FEUUU7RUFDRSwwQ0FBQTtFQUNBLDJCQUFBO0FDTko7QURRRTtFQUNFLGdDQUFBO0VBQ0EsMkJBQUE7QUNOSjtBRFNFO0VBQ0UsZ0NBQUE7QUNQSjtBRFVFO0VBQ0UsaUNBQUE7RUFDQSwwQ0FBQTtBQ1JKO0FEV0U7RUFDRSw4Q0FBQTtFQUNBLG1DQUFBO0FDVEo7QURZRTtFQUNFLG9EQUFBO0FDVko7QURhRTtFQUNFLG9EQUFBO0FDWEo7QURjRTtFQUNFLGdDQUFBO0VBQ0EsNEJBQUE7QUNaSjtBRGVFO0VBQ0UsMENBQUE7QUNiSjtBRGdCRTtFQUNFLDhDQUFBO0FDZEo7QURpQkU7RUFDRSwwQ0FBQTtBQ2ZKO0FEa0JFO0VBQ0UsOENBQUE7QUNoQko7QURtQkU7RUFDRSw0QkFBQTtFQUNBLHVCQUFBO0VBQ0Esb0JBQUE7RUFDQSxtQkFBQTtFQUNBLHNCQUFBO0FDakJKO0FEb0JFO0VBQ0UsMEJBQUE7QUNsQko7O0FDeUVNO0VBQWdCLGNBQUE7RUFBZ0IsWUFBQTtFQUFjLFdBQUE7QURuRXBEOztBQzJFVTtFQTRCSTtJQUErQixVQUZ2QjtJQUVxQyxnQkE1QzlDO0lBNEMwRSxnQkFkakI7SUFjMkMsV0FBQTtFRC9GL0c7RUMrRlk7SUFBK0IsVUFGdkI7SUFFcUMsaUJBVlk7SUFVZ0IsZUE1QzFFO0lBNENvRyxXQUFBO0VEekYvRztFQ3lGWTtJQUErQixVQUZ2QjtJQUVxQyxnQkE1QzlDO0lBNEMwRSxnQkFkakI7SUFjMkMsV0FBQTtFRG5GL0c7RUNtRlk7SUFBK0IsVUFGdkI7SUFFcUMsaUJBVlk7SUFVZ0IsZUE1QzFFO0lBNENvRyxXQUFBO0VEN0UvRztBQUNGO0FDZ0RVO0VBNEJJO0lBQStCLFlBRnZCO0lBRXFDLGdCQTVDOUM7SUE0QzBFLGdCQWRqQjtJQWMyQyxXQUFBO0VEckUvRztFQ3FFWTtJQUErQixZQUZ2QjtJQUVxQyxnQkE1QzlDO0lBNEMwRSxlQTVDMUU7SUE0Q29HLFdBQUE7RUQvRC9HO0VDK0RZO0lBQStCLFlBRnZCO0lBRXFDLGdCQTVDOUM7SUE0QzBFLGVBNUMxRTtJQTRDb0csV0FBQTtFRHpEL0c7RUN5RFk7SUFBK0IsWUFGdkI7SUFFcUMsaUJBVlk7SUFVZ0IsZUE1QzFFO0lBNENvRyxXQUFBO0VEbkQvRztBQUNGO0FDY007RUFBZ0IsY0FBQTtFQUFnQixZQUFBO0VBQWMsV0FBQTtBRFRwRDs7QUNpQlU7RUE0Qkk7SUFBK0IsVUFGdkI7SUFFcUMsZ0JBNUM5QztJQTRDMEUsZ0JBZGpCO0lBYzJDLFdBQUE7RURyQy9HO0VDcUNZO0lBQStCLFVBRnZCO0lBRXFDLGdCQTVDOUM7SUE0QzBFLGVBNUMxRTtJQTRDb0csV0FBQTtFRC9CL0c7RUMrQlk7SUFBK0IsVUFGdkI7SUFFcUMsaUJBVlk7SUFVZ0IsZUE1QzFFO0lBNENvRyxXQUFBO0VEekIvRztBQUNGO0FDWk07RUFBZ0IsY0FBQTtFQUFnQixZQUFBO0VBQWMsV0FBQTtBRGlCcEQ7O0FDVFU7RUE0Qkk7SUFBK0IsWUFGdkI7SUFFcUMsZ0JBNUM5QztJQTRDMEUsZ0JBZGpCO0lBYzJDLFdBQUE7RURYL0c7RUNXWTtJQUErQixZQUZ2QjtJQUVxQyxpQkFWWTtJQVVnQixlQTVDMUU7SUE0Q29HLFdBQUE7RURML0c7QUFDRjtBQ2lFQSxxQkFBQTtBQTBCQSxxQkFBQTtBQTBCQTswQkFBQTtBQTBDRTtFQUNFLG1CQUFBO0VBQ0Esa0JBQUE7QUR6Sko7QUMySkk7RUFDRSxjQUFBO0FEekpOO0FDdUhNO0VBaUNGO0lBS00sV0FBQTtJQUNBLGdCQUFBO0VEekpSO0FBQ0Y7QUNpSE07RUFpQ0Y7SUFLTSxXQUFBO0lBQ0EsZUFBQTtFRG5KUjtBQUNGO0FDMkdNO0VBaUNGO0lBS00sV0FBQTtJQUNBLGVBQUE7RUQ3SVI7QUFDRjtBQ3FHTTtFQWlDRjtJQUtNLFdBQUE7SUFDQSxnQkFBQTtFRHZJUjtBQUNGO0FDK0ZNO0VBaUNGO0lBS00sV0FBQTtJQUNBLGlCQUFBO0VEaklSO0FBQ0Y7QUN5Rk07RUFpQ0Y7SUFLTSxXQUFBO0lBQ0EsaUJBQUE7RUQzSFI7QUFDRjtBQ21GTTtFQWlDRjtJQUtNLFdBQUE7SUFDQSxpQkFBQTtFRHJIUjtBQUNGOztBQzJISTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EsV0FBQTtBRHhITjtBQ3NFTTtFQThDRjtJQVVNLGdCQUFBO0VEMUhSO0FBQ0Y7QUNpRU07RUE4Q0Y7SUFVTSxlQUFBO0VEckhSO0FBQ0Y7QUM0RE07RUE4Q0Y7SUFVTSxlQUFBO0VEaEhSO0FBQ0Y7QUN1RE07RUE4Q0Y7SUFVTSxnQkFBQTtFRDNHUjtBQUNGO0FDa0RNO0VBOENGO0lBVU0saUJBQUE7RUR0R1I7QUFDRjtBQzZDTTtFQThDRjtJQVVNLGlCQUFBO0VEakdSO0FBQ0Y7QUN3Q007RUE4Q0Y7SUFVTSxpQkFBQTtFRDVGUjtBQUNGOztBQ2lGSTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EsV0FBQTtBRDlFTjtBQzRCTTtFQThDRjtJQVVNLGVBQUE7RURoRlI7QUFDRjtBQ3VCTTtFQThDRjtJQVVNLGVBQUE7RUQzRVI7QUFDRjtBQ2tCTTtFQThDRjtJQVVNLGdCQUFBO0VEdEVSO0FBQ0Y7QUNhTTtFQThDRjtJQVVNLGlCQUFBO0VEakVSO0FBQ0Y7QUNRTTtFQThDRjtJQVVNLGlCQUFBO0VENURSO0FBQ0Y7QUNHTTtFQThDRjtJQVVNLGlCQUFBO0VEdkRSO0FBQ0Y7O0FDNENJO0VBQ0UsY0FBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSxXQUFBO0FEekNOO0FDVE07RUE4Q0Y7SUFVTSxlQUFBO0VEM0NSO0FBQ0Y7QUNkTTtFQThDRjtJQVVNLGdCQUFBO0VEdENSO0FBQ0Y7QUNuQk07RUE4Q0Y7SUFVTSxpQkFBQTtFRGpDUjtBQUNGO0FDeEJNO0VBOENGO0lBVU0saUJBQUE7RUQ1QlI7QUFDRjtBQzdCTTtFQThDRjtJQVVNLGlCQUFBO0VEdkJSO0FBQ0Y7O0FDWUk7RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLFdBQUE7QURUTjtBQ3pDTTtFQThDRjtJQVVNLGdCQUFBO0VEWFI7QUFDRjtBQzlDTTtFQThDRjtJQVVNLGlCQUFBO0VETlI7QUFDRjtBQ25ETTtFQThDRjtJQVVNLGlCQUFBO0VERFI7QUFDRjtBQ3hETTtFQThDRjtJQVVNLGlCQUFBO0VESVI7QUFDRjs7QUNmSTtFQUNFLGNBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0VBQ0EsV0FBQTtBRGtCTjtBQ3BFTTtFQThDRjtJQVVNLGlCQUFBO0VEZ0JSO0FBQ0Y7QUN6RU07RUE4Q0Y7SUFVTSxpQkFBQTtFRHFCUjtBQUNGO0FDOUVNO0VBOENGO0lBVU0saUJBQUE7RUQwQlI7QUFDRjs7QUNyQ0k7RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLFdBQUE7QUR3Q047QUMxRk07RUE4Q0Y7SUFVTSxpQkFBQTtFRHNDUjtBQUNGO0FDL0ZNO0VBOENGO0lBVU0saUJBQUE7RUQyQ1I7QUFDRjs7QUN0REk7RUFDRSxjQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLFdBQUE7QUR5RE47QUMzR007RUE4Q0Y7SUFVTSxpQkFBQTtFRHVEUjtBQUNGOztBRTNaQTtFQUNJLFdBQUE7RUFDQSxlQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtBRjhaSjs7QUUzWkE7RUFDSSxhQUFBO0VBQ0Esc0JBQUE7RUFDQSw4QkFBQTtFQUNBLG1CQUFBO0VBRUEsYUFBQTtFQUNBLG1CQUFBO0FGNlpKO0FDcFVRO0VDaEdSO0lBVVEsbUJBQUE7RUY4Wk47QUFDRjtBRTVaSTtFQUNJLHNCQUFBO0VBTUEsYUFBQTtBRnlaUjtBQzdVUTtFQ25GSjtJQUlRLHFCQUFBO0VGZ2FWO0FBQ0Y7QUU1WlE7RUFDSSxXQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQ0FBQTtBRjhaWjtBRTFaSTtFQUNJLFdBQUE7RUFNQSxpQkFBQTtFQUNBLGFBQUE7RUFDQSx5QkFBQTtFQUNBLFlBQUE7RUFDQSw0QkFBQTtFQUNBLGdCQUFBO0VBQ0EseUlBQUE7QUZ1WlI7QUNqV1E7RUNuRUo7SUFJUSxVQUFBO0VGb2FWO0FBQ0Y7QUUxWlE7RUFDSSwyQkFBQTtFQUNBLFdBQUE7RUFDQSxjQUFBO0FGNFpaO0FFelpRO0VBQ0ksZUFBQTtFQUNBLGdDQUFBO0FGMlpaO0FFeFpRO0VBQ0ksZUFBQTtFQUNBLGdDQUFBO0FGMFpaO0FFdlpRO0VBQ0ksYUFBQTtFQUNBLDhCQUFBO0VBQ0EsZ0JBQUE7RUFDQSxlQUFBO0VBQ0EsaUJBQUE7QUZ5Wlo7QUV2Wlk7RUFDSSxnQ0FBQTtBRnlaaEI7QUVyWlE7RUFDSSxnQkFBQTtBRnVaWjs7QUVsWkE7RUFDSSxrQkFBQTtFQUNBLGdCQUFBO0VBQ0EsYUFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLDhCQUFBO0VBQ0EsbUJBQUE7RUFDQSxvQ0FBQTtBRnFaSjtBRW5aSTtFQUNJLHNCQUFBO0VBQ0EsbUJBQUE7RUFDQSxhQUFBO0VBQ0EsMkNBQUE7RUFDQSxXQUFBO0VBQ0EsY0FBQTtFQUNBLGdDQUFBO0FGcVpSO0FFblpRO0VBQ0ksV0FBQTtFQUNBLGVBQUE7QUZxWlo7QUVsWlE7RUFDSSx5QkFBQTtFQUNBLGtCQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSx1QkFBQTtFQUNBLGlCQUFBO0VBQ0EsdUNBQUE7RUFDQSxlQUFBO0VBQ0EsU0FBQTtFQUNBLFdBQUE7RUFDQSxrQkFBQTtBRm9aWjtBRWpaUTtFQUNJLFlBQUE7RUFDQSxpQkFBQTtFQUNBLGtCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQ0FBQTtFQUNBLG1CQUFBO0VBQ0EsV0FBQTtBRm1aWjs7QUU5WUE7RUFDSSxhQUFBO0VBQ0EsV0FBQTtFQUNBLHVCQUFBO0VBQ0EsY0FBQTtFQUNBLGVBQUE7RUFDQSxzQkFBQTtBRmlaSjtBRS9ZSTtFQUNJLDRCQUFBO0VBQ0EsWUFBQTtFQUNBLG1CQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsZUFBQTtBRmlaUjtBRS9ZUTtFQUNJLGdDQUFBO0FGaVpaO0FFOVlRO0VBQ0ksZUFBQTtFQUNBLHlCQUFBO0VBQ0EsNEJBQUE7QUZnWlo7QUU1WUk7RUFFSSxZQUFBO0VBQ0EsY0FBQTtBRjZZUjtBRTNZUTtFQUNJLHVIQUFBO0FGNllaO0FFM1lZO0VBQ0ksOEJBQUE7RUFDQSxZQUFBO0VBQ0EsV0FBQTtFQUNBLGdCQUFBO0VBQ0Esc0JBQUE7RUFDQSxhQUFBO0VBQ0EsdUJBQUE7RUFDQSxtQkFBQTtBRjZZaEI7QUN4ZFE7RUNnRkk7SUFFUSxxQkFBQTtFRjBZbEI7QUFDRjtBRXJZSTtFQUNJLGFBQUE7RUFDQSxZQUFBO0VBRUEsVUFBQTtFQU1BLDRCQUFBO0FGaVlSO0FDbmVRO0VDd0ZKO0lBT1EsVUFBQTtFRndZVjtBQUNGO0FFcFlRO0VBQ0ksYUFBQTtBRnNZWjtBRW5ZUTtFQUNJLDRCQUFBO0VBQ0EsV0FBQTtBRnFZWjtBRWpZSTtFQUNJLGFBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0VBQ0EsdUJBQUE7RUFDQSxnQ0FBQTtBRm1ZUjtBRWhZSTtFQUNJLGtCQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7QUZrWVI7O0FFOVhBO0VBQ0ksZUFBQTtBRmlZSjtBQy9mUTtFQzZIUjtJQUlRLGlCQUFBO0VGa1lOO0FBQ0Y7O0FFL1hBO0VBQ0ksbURBQUE7RUFDQSxtQkFBQTtFQUNBLFdBQUE7RUFDQSxvQkFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLGdCQUFBO0VBQ0Esb0JBQUE7RUFDQSwyQkFBQTtFQUNBLDRCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsOEJBQUE7RUFDQSxzQkFBQTtFQUNBLDRCQUFBO0VBQ0EsMkJBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0Esd0JBQUE7RUFDQSxtQkFBQTtFQUNBLCtDQUFBO0VBQ0EsdUJBQUE7RUFDQSxVQUFBO0FGa1lKO0FFaFlJO0VBQ0ksYUFBQTtFQUNBLG1CQUFBO0VBQ0EsZUFBQTtBRmtZUjtBRS9YSTtFQUNJLGtCQUFBO0FGaVlSO0FFOVhJO0VBQ0ksYUFBQTtBRmdZUjtBRTlYUTtFQUNJLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSwyQkFBQTtFQUNBLFdBQUE7RUFDQSxZQUFBO0VBQ0EsaUJBQUE7RUFDQSxrQkFBQTtFQUNBLGNBQUE7QUZnWVo7O0FFMVhJO0VBQ0ksMEJBQUE7QUY2WFI7QUUxWEk7RUFDSSxXQUFBO0VBQ0EsY0FBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7QUY0WFI7QUV6WEk7RUFDSSxzQkFBQTtBRjJYUjtBRXpYUTtFQUNJLDBCQUFBO0FGMlhaO0FFdFhRO0VBQ0ksc0JBQUE7QUZ3WFo7O0FFblhBO0VBQ0ksYUFBQTtFQUNBLG1CQUFBO0FGc1hKO0FFcFhJO0VBQ0ksaUJBQUE7QUZzWFI7QUVuWEk7RUFDSSxXQUFBO0FGcVhSOztBRWpYQTtFQUNJLGlCQUFBO0VBQ0EsZ0JBQUE7RUFDQSwyQkFBQTtBRm9YSjs7QUVqWEE7RUFDSSxnQ0FBQTtFQUNBLFNBQUE7RUFDQSxhQUFBO0VBQ0EsZUFBQTtFQUNBLHlCQUFBO0FGb1hKOztBRWpYQTtFQUNJLGFBQUE7RUFDQSxzQkFBQTtBRm9YSjtBRWxYSTtFQUNJLGdCQUFBO0FGb1hSO0FFbFhRO0VBQ0ksd0JBQUE7RUFDQSxlQUFBO0VBQ0EsZ0NBQUE7QUZvWFo7QUVqWFE7RUFDSSx3QkFBQTtFQUNBLGVBQUE7RUFDQSxhQUFBO0VBQ0EsZUFBQTtBRm1YWjtBRWpYWTs7RUFFSSxhQUFBO0VBQ0Esc0JBQUE7QUZtWGhCO0FFalhnQjs7RUFDSSxjQUFBO0VBQ0EsZ0NBQUE7QUZvWHBCO0FFalhnQjs7RUFDSSxhQUFBO0FGb1hwQjtBRWxYb0I7O0VBQ0ksVUFBQTtBRnFYeEI7QUVoWFk7RUFDSSxVQUFBO0VBQ0EsZUFBQTtBRmtYaEI7QUUvV1k7RUFDSSxVQUFBO0VBQ0EsZUFBQTtBRmlYaEI7QUU5V1k7RUFDSSxXQUFBO0VBQ0EsZ0JBQUE7QUZnWGhCO0FFM1dJO0VBQ0ksYUFBQTtBRjZXUjs7QUV6V0E7RUFDSSxnQkFBQTtFQUNBLFFBQUE7RUFDQSx1QkFBQTtFQUNBLGtDQUFBO0FGNFdKOztBRXpXQTtFQUNJLG1CQUFBO0VBQ0Esa0VBQUE7RUFDQSxnQ0FBQTtBRjRXSjs7QUV6V0E7RUFDSSxnQkFBQTtFQUNBLGdDQUFBO0FGNFdKOztBRXpXQTtFQUNJLHdCQUFBO0VBQ0EsNkJBQUE7QUY0V0o7O0FFeldBO0VBQ0ksV0FBQTtFQUNBLHlCQUFBO0VBQ0EsZUFBQTtFQUNBLGtCQUFBO0VBQ0EsaUJBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSxjQUFBO0FGNFdKO0FFeldRO0VBQ0ksc0lBQUE7RUFDQSx5QkFBQTtFQUNBLGtCQUFBO0VBQ0EsTUFBQTtFQUNBLFFBQUE7RUFDQSxnQkFBQTtFQUNBLHlCQUFBO0VBQ0EsOEJBQUE7RUFDQSxlQUFBO0VBQ0Esa0JBQUE7RUFDQSxhQUFBO0FGMldaOztBRXRXQTtFQUNJLGFBQUE7RUFDQSx1QkFBQTtFQUNBLHNCQUFBO0VBQ0EsbUJBQUE7RUFDQSxhQUFBO0FGeVdKO0FFdldJO0VBQ0ksZ0JBQUE7RUFDQSxnQ0FBQTtBRnlXUjs7QUVyV0E7RUFDSSxZQUFBO0VBQ0EsZ0JBQUE7QUZ3V0o7O0FFcldBO0VBQ0ksbUJBQUE7QUZ3V0o7O0FFcldBO0VBQ0ksMEJBQUE7QUZ3V0o7O0FFcldBO0VBQ0ksMEJBQUE7QUZ3V0o7O0FFcldBOzs7RUFHSSx1Q0FBQTtBRndXSjs7QUVyV0E7RUFDSSxrQkFBQTtFQUNBLHNCQUFBO0VBQ0EsOEJBQUE7QUZ3V0o7QUV0V0k7RUFDSSxtQ0FBQTtFQUNBLFVBQUE7QUZ3V1I7QUVyV0k7RUFDSSxZQUFBO0VBQ0EsY0FBQTtFQUNBLGtCQUFBO0VBQ0EsT0FBQTtFQUNBLE1BQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGlGQUFBO0VBQ0EsNEJBQUE7RUFDQSwwQkFBQTtFQUNBLHFCQUFBO0VBQ0EsVUFBQTtFQUNBLFlBQUE7QUZ1V1I7O0FFbldBO0VBQ0kseUJBQUE7RUFDQSxzQkFBQTtBRnNXSjtBRXBXSTtFQUNJLFlBQUE7QUZzV1I7O0FFbFdBO0VBQ0ksb0NBQUE7RUFDQSx5QkFBQTtFQUNBLHNCQUFBO0FGcVdKOztBRWxXQTtFQUNJLHVDQUFBO0VBQ0EsV0FBQTtFQUNBLDZCQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxnQkFBQTtFQUNBLFdBQUE7RUFDQSxrQkFBQTtBRnFXSjtBRW5XSTtFQUNJLFVBQUE7QUZxV1I7O0FFaldBO0VBQ0ksdUNBQUE7QUZvV0o7O0FFaldBO0VBQ0ksMENBQUE7RUFDQSxrQkFBQTtFQUNBLE1BQUE7RUFDQSxPQUFBO0VBQ0EsU0FBQTtFQUNBLGdDQUFBO0VBQ0EsUUFBQTtFQUNBLFVBQUE7QUZvV0o7O0FFaldBO0VBQ0ksd0JBQUE7QUZvV0o7O0FFaldBO0VBQ0ksYUFBQTtFQUNBLHVCQUFBO0FGb1dKOztBRWpXQTs7RUFFSSxtQkFBQTtFQUNBLG9DQUFBO0FGb1dKO0FFbFdJOztFQUNJLG9DQUFBO0VBQ0EsMkJBQUE7RUFDQSxzQkFBQTtBRnFXUjtBRWxXSTs7RUFDSSxZQUFBO0VBQ0EsbUJBQUE7QUZxV1I7QUVuV1E7O0VBQ0ksV0FBQTtFQUNBLGtCQUFBO0FGc1daOztBRTdWQTtFQUNJLG1CQUFBO0VBQ0EsZ0JBQUE7RUFDQSxvQ0FBQTtBRmdXSjtBRTlWSTtFQUNJLG9DQUFBO0VBQ0EsMkJBQUE7RUFDQSxzQkFBQTtBRmdXUjtBRTdWSTtFQUNJLFlBQUE7RUFDQSxtQkFBQTtBRitWUjtBRTdWUTtFQUNJLFdBQUE7RUFDQSxrQkFBQTtBRitWWjs7QUUxVkE7RUFDSSwyQkFBQTtFQUNBLHFEQUFBO0VBQ0EsbUJBQUE7QUY2Vko7QUUzVkk7RUFDSSxnRUFBQTtFQUNBLDJCQUFBO0VBQ0Esc0JBQUE7QUY2VlI7O0FFelZBO0VBQ0ksNkJBQUE7RUFDQSxvQkFBQTtFQUNBLHdCQUFBO0FGNFZKOztBRXpWQTtFQUNJLGNBQUE7QUY0Vko7O0FFdlZJO0VBQ0ksV0FBQTtFQUNBLFlBQUE7QUYwVlI7QUV2Vkk7RUFDSSxlQUFBO0VBQ0EsZUFBQTtBRnlWUjtBRXZWUTtFQUpKO0lBS1EsZUFBQTtFRjBWVjtBQUNGO0FFdlZJO0VBQ0ksUUFBQTtFQUNBLFVBQUE7QUZ5VlI7QUV0Vkk7RUFDSSxtQkFBQTtBRndWUjtBRXJWSTtFQUNJLG1CQUFBO0FGdVZSO0FFcFZJO0VBQ0kscUJBQUE7QUZzVlI7QUVqVlE7RUFDSSxzQkFBQTtFQUNBLHVCQUFBO0FGbVZaO0FDMzRCTTtFQ2lrQk47SUFFSyxTQUFBO0lBQ0EsT0FBQTtJQUNBLFdBQUE7SUFDQSxrQkFBQTtJQUNBLHlCQUFBO0VGNFVIO0FBQ0Y7O0FBamdDQTtFQUNJLGFBQUE7QUFvZ0NKOztBQWpnQ0E7RUFDSSxhQUFBO0VBQ0EsNkJBQUE7QUFvZ0NKOztBQWpnQ0E7RUFDSSxTQUFBO0FBb2dDSjs7QUFqZ0NBO0VBQ0ksYUFBQTtFQUNBLHVCQUFBO0VBQ0Esc0JBQUE7QUFvZ0NKOztBQWpnQ0E7RUFDSSxhQUFBO0FBb2dDSiIsInNvdXJjZXNDb250ZW50IjpbIjpob3N0IDo6bmctZGVlcCB7XHJcblxyXG5cclxuICAvLyBjaGVja2JveFxyXG4gIC5lLWZyYW1lLmUtY2hlY2sge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tbWFpbi1jb2xvcikgIWltcG9ydGFudDtcclxuICAgIHdpZHRoOiAyMHB4ICFpbXBvcnRhbnQ7XHJcbiAgICBoZWlnaHQ6IDIwcHggIWltcG9ydGFudDtcclxuICB9XHJcbiAgLmUtY2hlY2tib3gtd3JhcHBlciAuZS1mcmFtZSB7XHJcbiAgICB3aWR0aDogMjBweCAhaW1wb3J0YW50O1xyXG4gICAgaGVpZ2h0OiAyMHB4ICFpbXBvcnRhbnQ7XHJcbiAgICBsaW5lLWhlaWdodDogMTZweDtcclxuICB9XHJcbiAgLmUtY2hlY2tib3gtd3JhcHBlciAuZS1jaGVja2JveDpmb2N1cyArIC5lLWZyYW1lIHtcclxuICAgIHdpZHRoOiAyMHB4ICFpbXBvcnRhbnQ7XHJcbiAgICBoZWlnaHQ6IDIwcHggIWltcG9ydGFudDtcclxuICB9XHJcbiAgLmUtaWNvbnMuZS1jaGVjazpiZWZvcmUge1xyXG4gICAgZm9udC1zaXplOiAxM3B4ICFpbXBvcnRhbnQ7XHJcbiAgfVxyXG5cclxuXHJcblxyXG4gIC5lLXJhZGlvOmZvY3VzICsgbGFiZWw6OmJlZm9yZSB7XHJcbiAgICBib3JkZXItY29sb3I6IHZhcigtLW1haW4tY29sb3IpICFpbXBvcnRhbnQ7XHJcbiAgICBib3gtc2hhZG93OiBub25lICFpbXBvcnRhbnQ7XHJcbiAgfVxyXG4gIC5lLXJhZGlvOmZvY3VzICsgbGFiZWw6OmJlZm9yZSB7XHJcbiAgICBib3JkZXItY29sb3I6ICM3NTc1NzUgIWltcG9ydGFudDtcclxuICAgIGJveC1zaGFkb3c6IG5vbmUgIWltcG9ydGFudDtcclxuICB9XHJcblxyXG4gIC5lLXJhZGlvOmhvdmVyICsgbGFiZWw6OmJlZm9yZSB7XHJcbiAgICBib3JkZXItY29sb3I6ICM3NTc1NzUgIWltcG9ydGFudDtcclxuICB9XHJcblxyXG4gIC5lLXJhZGlvOmNoZWNrZWQgKyBsYWJlbDo6YmVmb3JlIHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmYgIWltcG9ydGFudDtcclxuICAgIGJvcmRlci1jb2xvcjogdmFyKC0tbWFpbi1jb2xvcikgIWltcG9ydGFudDtcclxuICB9XHJcblxyXG4gIC5lLXJhZGlvOmNoZWNrZWQgKyBsYWJlbDo6YWZ0ZXIge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tbWFpbi1jb2xvcikgIWltcG9ydGFudDtcclxuICAgIGNvbG9yOiB2YXIoLS1tYWluLWNvbG9yKSAhaW1wb3J0YW50O1xyXG4gIH1cclxuXHJcbiAgLmUtcmFkaW86Y2hlY2tlZCArIGxhYmVsOmFjdGl2ZSAuZS1yaXBwbGUtZWxlbWVudCB7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDIyNywgMjIsIDkxLCAwLjI2KSAhaW1wb3J0YW50O1xyXG4gIH1cclxuXHJcbiAgLmUtcmFkaW86Y2hlY2tlZCArIC5lLWZvY3VzIC5lLXJpcHBsZS1jb250YWluZXIge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyMjcsIDIyLCA5MSwgMC4yNikgIWltcG9ydGFudDtcclxuICB9XHJcblxyXG4gIC5lLXJhZGlvOmNoZWNrZWQgKyAuZS1mb2N1czo6YmVmb3JlIHtcclxuICAgIG91dGxpbmU6ICNmZmYgMCBzb2xpZCAhaW1wb3J0YW50O1xyXG4gICAgb3V0bGluZS1vZmZzZXQ6IDAgIWltcG9ydGFudDtcclxuICB9XHJcblxyXG4gIC5lLXJhZGlvOmNoZWNrZWQ6Zm9jdXMgKyBsYWJlbDo6YmVmb3JlIHtcclxuICAgIGJvcmRlci1jb2xvcjogdmFyKC0tbWFpbi1jb2xvcikgIWltcG9ydGFudDtcclxuICB9XHJcblxyXG4gIC5lLXJhZGlvOmNoZWNrZWQ6Zm9jdXMgKyBsYWJlbDo6YWZ0ZXIge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tbWFpbi1jb2xvcikgIWltcG9ydGFudDtcclxuICB9XHJcblxyXG4gIC5lLXJhZGlvOmNoZWNrZWQgKyBsYWJlbDpob3Zlcjo6YmVmb3JlIHtcclxuICAgIGJvcmRlci1jb2xvcjogdmFyKC0tbWFpbi1jb2xvcikgIWltcG9ydGFudDtcclxuICB9XHJcblxyXG4gIC5lLXJhZGlvOmNoZWNrZWQgKyBsYWJlbDpob3Zlcjo6YWZ0ZXIge1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tbWFpbi1jb2xvcikgIWltcG9ydGFudDtcclxuICB9XHJcblxyXG4gIC5lLXJhZGlvICsgbGFiZWw6OmFmdGVyIHtcclxuICAgIGJvcmRlcjogMnB4IHNvbGlkICFpbXBvcnRhbnQ7XHJcbiAgICBoZWlnaHQ6IDEycHggIWltcG9ydGFudDtcclxuICAgIGxlZnQ6IDNweCAhaW1wb3J0YW50O1xyXG4gICAgdG9wOiAzcHggIWltcG9ydGFudDtcclxuICAgIHdpZHRoOiAxMnB4ICFpbXBvcnRhbnQ7XHJcbiAgfVxyXG5cclxuICAuZS1sYWJlbCB7XHJcbiAgICBmb250LXNpemU6IDE1cHggIWltcG9ydGFudDtcclxuICB9XHJcbn1cclxuIiwiQGltcG9ydCBcIi4uL2J1eS1wYWNrYWdlL2J1eS1wYWNrYWdlLmNvbXBvbmVudC5zY3NzXCI7XHJcblxyXG4uYnV5LXBhY2thZ2UtaGVhZGVyIHtcclxuICAgIHBhZGRpbmc6IDIwcHg7XHJcbn1cclxuXHJcbi5zZWN0aW9uIHtcclxuICAgIHBhZGRpbmc6IDIwcHg7XHJcbiAgICBib3gtc2hhZG93OiAwcHggMHB4IDBweCB3aGl0ZTtcclxufVxyXG5cclxuLnRvcC1idG5zIHtcclxuICAgIG1hcmdpbjogMDtcclxufVxyXG5cclxuLmJ0biB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG59XHJcblxyXG4ubGVmdCB7XHJcbiAgICBwYWRkaW5nOjE1cHg7XHJcbn0iLCJAaW1wb3J0ICdmbHVpZCc7XHJcblxyXG5cclxuXHJcbi8vIGUuZ1xyXG4vLyAub3V0ZXItYm94IHtcclxuLy8gICAgIEBpbmNsdWRlIGFzcGVjdC1yYXRpbyg0LCAzKTtcclxuLy8gIH1cclxuQG1peGluIGFzcGVjdC1yYXRpbygkd2lkdGgsICRoZWlnaHQpIHtcclxuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICAgICY6YmVmb3JlIHtcclxuICAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgICAgY29udGVudDogXCJcIjtcclxuICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgcGFkZGluZy10b3A6ICgkaGVpZ2h0IC8gJHdpZHRoKSAqIDEwMCU7XHJcbiAgICB9XHJcbiAgICA+IC5pbm5lci1ib3gge1xyXG4gICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICAgdG9wOiAwO1xyXG4gICAgICAgbGVmdDogMDtcclxuICAgICAgIHJpZ2h0OiAwO1xyXG4gICAgICAgYm90dG9tOiAwO1xyXG4gICAgfVxyXG4gfVxyXG5cclxuICRzbTogNTc2cHggIWRlZmF1bHQ7XHJcbiAkbWQ6IDc2OHB4ICFkZWZhdWx0O1xyXG4gJGxnOiA5OTJweCAhZGVmYXVsdDtcclxuICR4bDogMTIwMHB4ICFkZWZhdWx0O1xyXG4gJHh4bDogMTQwMHB4ICFkZWZhdWx0O1xyXG4gJG1sOiAxODAwcHggIWRlZmF1bHQ7XHJcbiAkcWhkOiAyNTYwcHggIWRlZmF1bHQ7XHJcbiAkXzJrOiAyMDQ4cHggIWRlZmF1bHQ7IFxyXG4gJGd1dHRlcjogLjVyZW0gIWRlZmF1bHQ7XHJcbiBcclxuICRmaWVsZE1hcmdpbjogMXJlbSAhZGVmYXVsdDtcclxuICRmaWVsZExhYmVsTWFyZ2luOiAuNXJlbSAhZGVmYXVsdDtcclxuICRoZWxwZXJUZXh0TWFyZ2luOiAuMjVyZW0gIWRlZmF1bHQ7XHJcbiBcclxuICRzcGFjZXI6IDFyZW0gIWRlZmF1bHQ7XHJcbiBcclxuICRicmVha3BvaW50czogKFxyXG4gICAgICdzbSc6ICRzbSxcclxuICAgICAnbWQnOiAkbWQsXHJcbiAgICAgJ2xnJzogJGxnLFxyXG4gICAgICd4bCc6ICR4bCxcclxuICAgICAneHhsJzogJHh4bCxcclxuICAgICAncWhkJzogJHFoZCxcclxuICAgICAnMmsnOiAkXzJrLFxyXG4gKSAhZGVmYXVsdDtcclxuLy8gZS5nXHJcbi8vIEBpbmNsdWRlIGJyZWFrcG9pbnQobGFyZ2UpIHtcclxuLy8gICAgIGRpdiB7XHJcbi8vICAgICAgICBmb250LXNpemU6IDJyZW07XHJcbi8vICAgICAgICBsaW5lLWhlaWdodDogMS40O1xyXG4vLyAgICAgfVxyXG4vLyAgfVxyXG4gXHJcbkBtaXhpbiBicmVha3BvaW50KCRwb2ludCkge1xyXG5cclxuICAgIEBpZiAkcG9pbnQgPT0gcWhkIHtcclxuICAgICAgQG1lZGlhIG9ubHkgc2NyZWVuIGFuZCAobWluLXdpZHRoOiAkeGwpIGFuZCAobWF4LXdpZHRoOiAkcWhkKSB7XHJcbiAgICAgICAgICBAY29udGVudDtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gICAgQGlmICRwb2ludCA9PSBfMmsge1xyXG4gICAgICBAbWVkaWEgb25seSBzY3JlZW4gYW5kIChtaW4td2lkdGg6ICRfMmspIHtcclxuICAgICAgICAgIEBjb250ZW50O1xyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgICBAaWYgJHBvaW50ID09IHh4bGFyZ2Uge1xyXG4gICAgICBAbWVkaWEgb25seSBzY3JlZW4gYW5kIChtaW4td2lkdGg6ICR4eGwpe1xyXG4gICAgICAgICAgQGNvbnRlbnQ7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICAgIEBpZiAkcG9pbnQgPT1sYXJnZSB7XHJcbiAgICAgICAgQG1lZGlhIG9ubHkgc2NyZWVuIGFuZCAobWluLXdpZHRoOiAkeGwpIHtcclxuICAgICAgICAgICAgQGNvbnRlbnQ7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIEBlbHNlIGlmICRwb2ludCA9PWRlc2t0b3Age1xyXG4gICAgICAgIEBtZWRpYSBvbmx5IHNjcmVlbiBhbmQgKG1pbi13aWR0aDogJGxnKSB7XHJcbiAgICAgICAgICAgIEBjb250ZW50O1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBAZWxzZSBpZiAkcG9pbnQgPT1zbWFsbC1sYXB0b3Age1xyXG4gICAgICAgIEBtZWRpYSBvbmx5IHNjcmVlbiBhbmQgKG1heC13aWR0aDogJGxnKSAgYW5kIChtYXgtaGVpZ2h0OiAkbGcpIHtcclxuICAgICAgICAgICAgQGNvbnRlbnQ7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIEBlbHNlIGlmICRwb2ludCA9PWxhcHRvcCB7XHJcbiAgICAgICAgQG1lZGlhIG9ubHkgc2NyZWVuIGFuZCAobWluLXdpZHRoOiAgJG1kKSB7XHJcbiAgICAgICAgICAgIEBjb250ZW50O1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBAZWxzZSBpZiAkcG9pbnQgPT1zbWFsbC1oZWlnaHQtbGFwdG9wIHtcclxuICAgICAgICBAbWVkaWEgb25seSBzY3JlZW4gYW5kIChtaW4td2lkdGg6ICRtZCkgIGFuZCAobWF4LWhlaWdodDogJG1kKSB7XHJcbiAgICAgICAgICAgIEBjb250ZW50O1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBAZWxzZSBpZiAkcG9pbnQgPT10YWJsZXQge1xyXG4gICAgICAgIEBtZWRpYSBvbmx5IHNjcmVlbiBhbmQgKG1pbi13aWR0aDogJHNtKSB7XHJcbiAgICAgICAgICAgIEBjb250ZW50O1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuICAgIEBlbHNlIGlmICRwb2ludCA9PW1vYmlsZSB7XHJcbiAgICAgIEBtZWRpYSBvbmx5IHNjcmVlbiBhbmQgKG1heC13aWR0aDogNzY4cHgpIHtcclxuICAgICAgICAgIEBjb250ZW50O1xyXG4gICAgICB9XHJcbiAgICB9XHJcbn1cclxuXHJcbi8vIGUuZyBAaW5jbHVkZSBmb250LXNpemUoMTRweClcclxuQGZ1bmN0aW9uIGNhbGN1bGF0ZVJlbSgkc2l6ZSkge1xyXG4gICAgJHJlbVNpemU6ICRzaXplIC8gMTZweDtcclxuICAgIEByZXR1cm4gJHJlbVNpemUgKiAxcmVtO1xyXG59XHJcblxyXG5AbWl4aW4gZm9udC1zaXplKCRzaXplKSB7XHJcbiAgICBmb250LXNpemU6IGNhbGN1bGF0ZVJlbSgkc2l6ZSk7XHJcbn1cclxuXHJcblxyXG5AbWl4aW4gZ3JpZHMoJGdyaWRzKSB7XHJcbiAgICAvLyBTRVRVUFxyXG4gICAgJHRvdGFsLWNvbHVtbnM6IDEyO1xyXG4gICAgJGJyZWFrcG9pbnRzOiAoeHhzOjMyMHB4LCB4czo0ODBweCwgc206NzY4cHgsIG1kOjk5MnB4LCBsZzoxMjAwcHgpO1xyXG4gICAgJGd1dHRlcjogMSU7XHJcbiAgICBcclxuICAgIC8vIFdpZHRoIG9mIG9uZSBjb2x1bW5cclxuICAgICR1bml0LXdpZHRoOiAoMTAwJSAtICRndXR0ZXIgKiAyICogKCR0b3RhbC1jb2x1bW5zIC0gMSkpIC8gJHRvdGFsLWNvbHVtbnM7XHJcbiAgICBcclxuICAgIEBlYWNoICRzZWwsICRzaXplcyBpbiAkZ3JpZHNcclxuICAgIHtcclxuICAgICAgLy8gQ2xlYXIgZml4XHJcbiAgICAgICN7JHNlbH06YWZ0ZXIgeyBkaXNwbGF5OiB0YWJsZTsgY29udGVudDogXCIgXCI7IGNsZWFyOmJvdGg7IH1cclxuICAgICBcclxuICAgICAgQGVhY2ggJGJyZWFrcG9pbnQsICR3aWR0aCBpbiAkYnJlYWtwb2ludHNcclxuICAgICAge1xyXG4gICAgICAgICRjb2xzOiBtYXAtZ2V0KCRzaXplcywgJGJyZWFrcG9pbnQpO1xyXG4gICAgICAgIFxyXG4gICAgICAgIEBpZiAkY29scyAhPSBudWxsXHJcbiAgICAgICAge1xyXG4gICAgICAgICAgQG1lZGlhIG9ubHkgc2NyZWVuIGFuZCAobWluLXdpZHRoOiAkd2lkdGgpIFxyXG4gICAgICAgICAge1xyXG4gICAgICAgICAgICAkY3VycmVudC1sZWZ0OiAwO1xyXG4gICAgICAgICAgICBcclxuICAgICAgICAgICAgQGZvciAkaSBmcm9tIDEgdGhyb3VnaCBsZW5ndGgoJGNvbHMpIHtcclxuICAgICAgICAgICAgICAkY29sOiBudGgoJGNvbHMsICRpKTtcclxuICBcclxuICAgICAgICAgICAgICAkcHJvcGVydHk6IG51bGw7ICR2YWx1ZTogbnVsbDsgJG1hcmdpbi1sZWZ0OiBudWxsOyAkbWFyZ2luLXJpZ2h0OiBudWxsO1xyXG4gICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICAgIC8vIElmIHRoZSBuZXh0IGNvbHVtbiBwdXNoZXMgb3ZlciB0aGUgYm91bmR5IHRoZW4gcmVzZXQgZmx1c2ggdG8gdGhlIGxlZnRcclxuICAgICAgICAgICAgICBAaWYgJGN1cnJlbnQtbGVmdCArICRjb2wgPiAkdG90YWwtY29sdW1ucyB7XHJcbiAgICAgICAgICAgICAgICAkY3VycmVudC1sZWZ0OiAwO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICBAaWYgJGN1cnJlbnQtbGVmdCAlICR0b3RhbC1jb2x1bW5zID09IDAgeyAkbWFyZ2luLWxlZnQ6IDBweDsgfSBAZWxzZSB7ICRtYXJnaW4tbGVmdDogJGd1dHRlcjsgIH1cclxuICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAkY3VycmVudC1sZWZ0OiAkY3VycmVudC1sZWZ0ICsgJGNvbDtcclxuICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICBAaWYgJGN1cnJlbnQtbGVmdCAlICR0b3RhbC1jb2x1bW5zID09IDAgeyAkbWFyZ2luLXJpZ2h0OiAwcHg7IH0gQGVsc2UgeyAkbWFyZ2luLXJpZ2h0OiAkZ3V0dGVyOyB9XHJcbiAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgLy8gSWYgdGhlIHJvdyBpcyBmdWxsIHRoZW4gZ2V0IHJlYWR5IGZvciB0aGUgbmV4dCByb3dcclxuICAgICAgICAgICAgICBAaWYgJGN1cnJlbnQtbGVmdCA9PSAkdG90YWwtY29sdW1ucyB7XHJcbiAgICAgICAgICAgICAgICAkY3VycmVudC1sZWZ0OiAwO1xyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAvLyBTdW0gdGhlIHVuaXQgd2lkdGhzIHBsdXMgdGhlIHdpZHRoIG9mIHRoZSBndXR0ZXJzXHJcbiAgICAgICAgICAgICAgJHdpZHRoOiAoJHVuaXQtd2lkdGggKiAkY29sKSArICgoJGNvbCAtIDEpICogKCRndXR0ZXIgKiAyKSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICBcclxuICAgICAgICAgICAgICAjeyRzZWx9ID4gKjpudGgtY2hpbGQoI3skaX0pIHsgd2lkdGg6JHdpZHRoOyBtYXJnaW4tcmlnaHQ6JG1hcmdpbi1yaWdodDsgbWFyZ2luLWxlZnQ6JG1hcmdpbi1sZWZ0OyBmbG9hdDpsZWZ0OyB9XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcblxyXG4gICAgQG1peGluIGludmFsaWQtc3RhdGUtaWNvbiB7XHJcbiAgICAgIHBhZGRpbmctcmlnaHQ6IDIwcHggIWltcG9ydGFudDtcclxuICAgICAgYmFja2dyb3VuZC1pbWFnZTogdXJsKFwiZGF0YTppbWFnZS9zdmcreG1sLCUzY3N2ZyB4bWxucz0naHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmcnIHZpZXdCb3g9JzAgMCAxMiAxMicgd2lkdGg9JzEyJyBoZWlnaHQ9JzEyJyBmaWxsPSdub25lJyBzdHJva2U9JyUyMzNGMzdDOSclM2UlM2NjaXJjbGUgY3g9JzYnIGN5PSc2JyByPSc0LjUnLyUzZSUzY3BhdGggc3Ryb2tlLWxpbmVqb2luPSdyb3VuZCcgZD0nTTUuOCAzLjZoLjRMNiA2LjV6Jy8lM2UlM2NjaXJjbGUgY3g9JzYnIGN5PSc4LjInIHI9Jy42JyBmaWxsPSclMjMzRjM3QzknIHN0cm9rZT0nbm9uZScvJTNlJTNjL3N2ZyUzZVwiKTtcclxuICAgICAgYmFja2dyb3VuZC1yZXBlYXQ6IG5vLXJlcGVhdDtcclxuICAgICAgYmFja2dyb3VuZC1wb3NpdGlvbjogcmlnaHQgY2FsYygwLjM3NWVtICsgMC4xODc1cmVtKSBjZW50ZXI7XHJcbiAgICAgIGJhY2tncm91bmQtc2l6ZTogY2FsYygwLjc1ZW0gKyAwLjM3NXJlbSkgY2FsYygwLjc1ZW0gKyAwLjM3NXJlbSk7XHJcbiAgICB9XHJcbiAgXHJcbiAgICBAbWl4aW4gdmFsaWQtc3RhdGUtaWNvbiB7XHJcbiAgICAgIGJhY2tncm91bmQtaW1hZ2U6IHVybChcImRhdGE6aW1hZ2Uvc3ZnK3htbCwlM2NzdmcgeG1sbnM9J2h0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnJyB2aWV3Qm94PScwIDAgOCA4JyUzZSUzY3BhdGggZmlsbD0nJTIzMTk4NzU0JyBkPSdNMi4zIDYuNzNMLjYgNC41M2MtLjQtMS4wNC40Ni0xLjQgMS4xLS44bDEuMSAxLjQgMy40LTMuOGMuNi0uNjMgMS42LS4yNyAxLjIuN2wtNCA0LjZjLS40My41LS44LjQtMS4xLjF6Jy8lM2UlM2Mvc3ZnJTNlXCIpO1xyXG4gICAgICBiYWNrZ3JvdW5kLXJlcGVhdDogbm8tcmVwZWF0O1xyXG4gICAgICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiBjZW50ZXI7XHJcbiAgICAgIGJhY2tncm91bmQtc2l6ZTogY2FsYygwLjc1ZW0gKyAwLjM3NXJlbSkgY2FsYygwLjc1ZW0gKyAwLjM3NXJlbSk7XHJcbiAgICB9XHJcbiAgLy8gUmVnaXN0ZXIgdGhlIGdyaWRzXHJcbiAgQGluY2x1ZGUgZ3JpZHMoKFxyXG4gICAgKCcucmVzcG9uc2l2ZS1mb3VyLWNvbC1ncmlkJywgKG1kOigzLCAzLCAzLCAzKSwgc206KDYsIDYsIDYsIDYpKSksXHJcbiAgICAoJy5yZXNwb25zaXZlLW5lc3RlZC1ncmlkJywgKG1kOig0LCA0LCA0KSkpLFxyXG4gICAgKCcudHdvLWNvbC1ncmlkJywgKHNtOigzLCA5KSkpLFxyXG4gICkpO1xyXG4gIFxyXG5cclxuICBAbWl4aW4gYXNwZWN0LXJhdGlvKCR3aWR0aCwgJGhlaWdodCkge1xyXG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gICAgJjpiZWZvcmV7XHJcbiAgICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICAgICAgY29udGVudDogXCIgXCI7XHJcbiAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgcGFkZGluZy10b3A6ICgkaGVpZ2h0IC8gJHdpZHRoKSAqIDEwMCU7XHJcbiAgICB9XHJcblxyXG4gICAgPiAuY29udGVudCB7XHJcbiAgICAgICAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gICAgICAgIHRvcDogMDtcclxuICAgICAgICBsZWZ0OiAwO1xyXG4gICAgICAgIHJpZ2h0OiAwO1xyXG4gICAgICAgIGJvdHRvbTogMDtcclxuICAgIH1cclxufVxyXG5cclxuICBAbWl4aW4gcmVzcG9uc2l2ZS1yYXRpbygkeCwkeSwgJHBzZXVkbzogZmFsc2UpIHtcclxuICAgICRwYWRkaW5nOiB1bnF1b3RlKCAoICR5IC8gJHggKSAqIDEwMCArICclJyApO1xyXG4gICAgQGlmICRwc2V1ZG8ge1xyXG4gICAgICAgICY6YmVmb3JlIHtcclxuICAgICAgICAgICAgQGluY2x1ZGUgcHNldWRvKCRwb3M6IHJlbGF0aXZlKTtcclxuICAgICAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgICAgIHBhZGRpbmctdG9wOiAkcGFkZGluZztcclxuICAgICAgICB9XHJcbiAgICB9IEBlbHNlIHtcclxuICAgICAgICBwYWRkaW5nLXRvcDogJHBhZGRpbmc7XHJcbiAgICB9XHJcbn1cclxuXHJcbi8qIERlZmluZSB0aGUgbWl4aW4gKi9cclxuQG1peGluIGZsdWlkLXR5cG9ncmFwaHkoJG1pbkZvbnQsICRtYXhGb250LCAkbWluQnJlYWtwb2ludCwgJG1heEJyZWFrcG9pbnQpIHtcclxuXHJcbiAgLyogRGVmaW5lIHZhcmlhYmxlIGZvciBtZWRpYSBxdWVyeSAqL1xyXG4gICRtYXhMZXNzT25lOiAkbWF4QnJlYWtwb2ludCAtIDE7XHJcblxyXG4gIC8qIERlZmluZSB2YXJpYWJsZSBmb3IgZmFsbGJhY2sgKi9cclxuICAkYXZnOiAoJG1heEZvbnQgKyAkbWluRm9udCkgLyAyO1xyXG5cclxuICAvKiBCYXNlIGZvbnQgc2l6ZSAqL1xyXG4gIGZvbnQtc2l6ZTogI3skbWluRm9udH1weDtcclxuXHJcbiAgQG1lZGlhIChtaW4td2lkdGg6ICN7JG1pbkJyZWFrcG9pbnR9cHgpIGFuZCAobWF4LXdpZHRoOiAjeyRtYXhMZXNzT25lfXB4KSB7XHJcblxyXG4gICAgLyogQWRkcyBhIGZhbGxiYWNrIGZvciB1bnN1cHBvcnRlZCBicm93c2VycyAqL1xyXG4gICAgZm9udC1zaXplOiAjeyRhdmd9cHg7XHJcblxyXG4gICAgLyogVGhlIGZsdWlkIHR5cG9ncmFwaHkgbWFnaWMgw7DCn8KMwp8gICovXHJcbiAgICBmb250LXNpemU6IGNhbGMoI3skbWluRm9udH1weCArICgjeyRtYXhGb250fSAtICN7JG1pbkZvbnR9KSAqICgxMDB2dyAtICN7JG1pbkJyZWFrcG9pbnR9cHgpIC8gKCN7JG1heEJyZWFrcG9pbnR9IC0gI3skbWluQnJlYWtwb2ludH0pKSFpbXBvcnRhbnRcclxuICB9XHJcblxyXG4gIEBtZWRpYSAobWluLXdpZHRoOiAjeyRtYXhCcmVha3BvaW50fXB4KSB7XHJcbiAgICBmb250LXNpemU6ICN7JG1heEZvbnR9cHg7XHJcbiAgfVxyXG59XHJcblxyXG4vKiBEZWZpbmUgdGhlIG1peGluICovXHJcbkBtaXhpbiBmbHVpZC1wcm9wZXJ0eSgkcHJvcGVydHksICRtaW5Gb250LCAkbWF4Rm9udCwgJG1pbkJyZWFrcG9pbnQsICRtYXhCcmVha3BvaW50KSB7XHJcblxyXG4gIC8qIERlZmluZSB2YXJpYWJsZSBmb3IgbWVkaWEgcXVlcnkgKi9cclxuICAkbWF4TGVzc09uZTogJG1heEJyZWFrcG9pbnQgLSAxO1xyXG5cclxuICAvKiBEZWZpbmUgdmFyaWFibGUgZm9yIGZhbGxiYWNrICovXHJcbiAgJGF2ZzogKCRtYXhGb250ICsgJG1pbkZvbnQpIC8gMjtcclxuXHJcbiAgLyogQmFzZSBmb250IHNpemUgKi9cclxuICAjeyRwcm9wZXJ0eX06ICN7JG1pbkZvbnR9cHg7XHJcblxyXG4gIEBtZWRpYSAobWluLXdpZHRoOiAjeyRtaW5CcmVha3BvaW50fXB4KSBhbmQgKG1heC13aWR0aDogI3skbWF4TGVzc09uZX1weCkge1xyXG5cclxuICAgIC8qIEFkZHMgYSBmYWxsYmFjayBmb3IgdW5zdXBwb3J0ZWQgYnJvd3NlcnMgKi9cclxuICAgICN7JHByb3BlcnR5fTogI3skYXZnfXB4O1xyXG5cclxuICAgIC8qIFRoZSBmbHVpZCB0eXBvZ3JhcGh5IG1hZ2ljIMOwwp/CjMKfICAqL1xyXG4gICAgI3skcHJvcGVydHl9OiBjYWxjKCN7JG1pbkZvbnR9cHggKyAoI3skbWF4Rm9udH0gLSAjeyRtaW5Gb250fSkgKiAoMTAwdncgLSAjeyRtaW5CcmVha3BvaW50fXB4KSAvICgjeyRtYXhCcmVha3BvaW50fSAtICN7JG1pbkJyZWFrcG9pbnR9KSlcclxuICB9XHJcblxyXG4gIEBtZWRpYSAobWluLXdpZHRoOiAjeyRtYXhCcmVha3BvaW50fXB4KSB7XHJcbiAgICAjeyRwcm9wZXJ0eX06ICN7JG1heEZvbnR9cHg7XHJcbiAgfVxyXG59XHJcblxyXG4vKiBCb3JkZXIgUmFkaXVzXHJcbi0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xyXG5cclxuQG1peGluIGJvcmRlci1yYWRpdXMoJHJhZGl1cykge1xyXG4gIC13ZWJraXQtYm9yZGVyLXJhZGl1czogJHJhZGl1cztcclxuICBib3JkZXItcmFkaXVzOiAkcmFkaXVzO1xyXG4gIGJhY2tncm91bmQtY2xpcDogcGFkZGluZy1ib3g7ICAvKiBzdG9wcyBiZyBjb2xvciBmcm9tIGxlYWtpbmcgb3V0c2lkZSB0aGUgYm9yZGVyOiAqL1xyXG59XHJcblxyXG4gIC8vIENPTlRBSU5FUiBNSVhJTlxyXG5cclxuICBAbWl4aW4gbWluKCRicCwgJG1heDogXCJudWxsXCIsICRkZXZpY2U6IFwic2NyZWVuXCIpIHtcclxuICAgIEBpZiAkbWF4ID09IFwibnVsbFwiIHtcclxuICAgICAgQG1lZGlhIG9ubHkgI3skZGV2aWNlfSBhbmQgKG1pbi13aWR0aDogI3skYnB9KSB7XHJcbiAgICAgICAgQGNvbnRlbnQ7XHJcbiAgICAgIH1cclxuICAgIH0gQGVsc2Uge1xyXG4gICAgICBAbWVkaWEgb25seSAjeyRkZXZpY2V9IGFuZCAobWluLXdpZHRoOiAjeyRicH0pIGFuZCAobWF4LXdpZHRoOiAjeyRtYXh9KSB7XHJcbiAgICAgICAgQGNvbnRlbnQ7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbiAgXHJcbiAgQGZ1bmN0aW9uIGJwKCRicCkge1xyXG4gICAgQHJldHVybiBtYXAtZ2V0KCRicmVha3BvaW50cywgJGJwKTtcclxuICB9XHJcblxyXG4gIEBmdW5jdGlvbiBjb250YWluZXIoJGNvbnRhaW5lci1zaXplLCAkdHJ1ZS12YWw6IGZhbHNlKSB7XHJcbiAgICBAcmV0dXJuIG1hcC1nZXQoJGNvbnRhaW5lci1zaXplcywgJGNvbnRhaW5lci1zaXplKTtcclxuICB9XHJcbiAgXHJcbiAgJGNvbnRhaW5lci1zaXplczogKFxyXG4gICAgc206IDEwMHZ3LFxyXG4gICAgbWQ6IDk1dncsXHJcbiAgICBsZzogOTB2dyxcclxuICAgIHhsOiA5OTZweCxcclxuICAgIHh4bDogMTA1MHB4LFxyXG4gICAgcWhkOiAxMjY0cHgsXHJcbiAgICBfMms6IDEyNjRweCxcclxuICApO1xyXG4vLyAgIGxnOiAkbGcgLSA1MHB4LFxyXG4vLyAgIHhsOiAkeGwgLSA2MHB4LFxyXG4gIC5jb250YWluZXIge1xyXG4gICAgcGFkZGluZy1yaWdodDogMXJlbTtcclxuICAgIHBhZGRpbmctbGVmdDogMXJlbTtcclxuICBcclxuICAgICY6bm90KC5pcy1mbHVpZCkge1xyXG4gICAgICBtYXJnaW46IDAgYXV0bztcclxuICBcclxuICAgICAgQGVhY2ggJGJwLCAkY29udGFpbmVyLXNpemUgaW4gJGNvbnRhaW5lci1zaXplcyB7XHJcbiAgICAgICAgQGluY2x1ZGUgbWluKCN7YnAoI3skYnB9KX0pIHtcclxuICAgICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgICAgbWF4LXdpZHRoOiBjb250YWluZXIoI3skYnB9KTtcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9XHJcbiAgXHJcbiAgQGVhY2ggJGJwLCAkY29udGFpbmVyLXNpemUgaW4gJGNvbnRhaW5lci1zaXplcyB7XHJcbiAgICAuY29udGFpbmVyLSN7JGJwfSB7XHJcbiAgICAgIG1hcmdpbjogMCBhdXRvO1xyXG4gICAgICBwYWRkaW5nLXJpZ2h0OiAxcmVtO1xyXG4gICAgICBwYWRkaW5nLWxlZnQ6IDFyZW07XHJcbiAgICAgIHdpZHRoOiAxMDAlO1xyXG4gIFxyXG4gICAgICAkaTogaW5kZXgoJGNvbnRhaW5lci1zaXplcywgJGJwICRjb250YWluZXItc2l6ZSk7XHJcbiAgXHJcbiAgICAgIEBmb3IgJGogZnJvbSAkaSB0aHJvdWdoIGxlbmd0aCgkY29udGFpbmVyLXNpemVzKSB7XHJcbiAgICAgICAgQGluY2x1ZGUgbWluKCN7YnAobnRoKG50aCgkY29udGFpbmVyLXNpemVzLCAkaiksIDEpKX0pIHtcclxuICAgICAgICAgIG1heC13aWR0aDogY29udGFpbmVyKCN7bnRoKG50aCgkY29udGFpbmVyLXNpemVzLCAkaiksIDEpfSk7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9XHJcbiAgfSIsIkBpbXBvcnQgXCIuLi8uLi8uLi8uLi8uLi9hc3NldHMvc3R5bGVzL215LXJhZGlvLnNjc3NcIjtcclxuQGltcG9ydCBcIm1peGluc1wiO1xyXG5cclxuLmJ1eS1wYWNrYWdlIHtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgZm9udC1zaXplOiAxN3B4O1xyXG4gICAgYmFja2dyb3VuZDogcmdiKDI1NSwgMjU1LCAyNTUpO1xyXG4gICAgYm9yZGVyLXJhZGl1czogMTRweDtcclxufVxyXG5cclxuLnNlY3Rpb24ge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS13aGl0ZSk7XHJcbiAgICBib3JkZXItcmFkaXVzOiAxNHB4O1xyXG4gICAgLy8gYm94LXNoYWRvdzogMHB4IDNweCA2cHggcmdiYSgwLCAwLCAwLCAwLjE2KTtcclxuICAgIG1hcmdpbi10b3A6IDA7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAzMHB4O1xyXG5cclxuICAgIEBpbmNsdWRlIGJyZWFrcG9pbnQodGFibGV0KSB7XHJcbiAgICAgICAgZmxleC1kaXJlY3Rpb246IHJvdztcclxuICAgIH1cclxuXHJcbiAgICAubGVmdCB7XHJcbiAgICAgICAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDtcclxuXHJcbiAgICAgICAgQGluY2x1ZGUgYnJlYWtwb2ludCh0YWJsZXQpIHtcclxuICAgICAgICAgICAgd2lkdGg6IDcwJSAhaW1wb3J0YW50O1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgcGFkZGluZzogMjBweDtcclxuXHJcbiAgICAgICAgLnRpdGxlIHtcclxuICAgICAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICAgICAgICAgICAgZm9udC1mYW1pbHk6IFwiUHJveGltYSBOb3ZhIEJvbGRcIjtcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLnJpZ2h0IHtcclxuICAgICAgICB3aWR0aDogMTAwJTtcclxuXHJcbiAgICAgICAgQGluY2x1ZGUgYnJlYWtwb2ludCh0YWJsZXQpIHtcclxuICAgICAgICAgICAgd2lkdGg6IDMwJTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIG1hcmdpbi10b3A6IC0xMHB4O1xyXG4gICAgICAgIHBhZGRpbmc6IDIwcHg7XHJcbiAgICAgICAgYm9yZGVyLXJhZGl1czogMCAwIDE0cHggMDtcclxuICAgICAgICBjb2xvcjogd2hpdGU7XHJcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZS1pbjtcclxuICAgICAgICBwb3NpdGlvbjogc3RpY2t5O1xyXG4gICAgICAgIGJhY2tncm91bmQtaW1hZ2U6IGxpbmVhci1ncmFkaWVudCh0byBib3R0b20sIHJnYigxMTcsIDEzNiwgMjQ0KSwgcmdiKDk1LCAxMTAsIDIwOSksIHJnYig3MywgODUsIDE3NSksIHJnYig1MCwgNjEsIDE0MiksIHJnYigyNywgMzksIDExMCkpO1xyXG5cclxuICAgICAgICAuc2VwZXJhdG9yIHtcclxuICAgICAgICAgICAgYm9yZGVyLXRvcDogMXB4IHNvbGlkIHdoaXRlO1xyXG4gICAgICAgICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgICAgICAgbWFyZ2luOiAxNXB4IDA7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAudGl0bGUge1xyXG4gICAgICAgICAgICBmb250LXNpemU6IDE4cHg7XHJcbiAgICAgICAgICAgIGZvbnQtZmFtaWx5OiBcIlByb3hpbWEgTm92YSBCb2xkXCI7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuc21hbGwtdGl0bGUge1xyXG4gICAgICAgICAgICBmb250LXNpemU6IDE1cHg7XHJcbiAgICAgICAgICAgIGZvbnQtZmFtaWx5OiBcIlByb3hpbWEgTm92YSBCb2xkXCI7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAucm93IHtcclxuICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gICAgICAgICAgICBtYXJnaW4tdG9wOiAxMHB4O1xyXG4gICAgICAgICAgICBmb250LXNpemU6IDE1cHg7XHJcbiAgICAgICAgICAgIHRleHQtYWxpZ246IHN0YXJ0O1xyXG5cclxuICAgICAgICAgICAgLnJvdy1yaWdodCB7XHJcbiAgICAgICAgICAgICAgICBmb250LWZhbWlseTogXCJQcm94aW1hIE5vdmEgQm9sZFwiO1xyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAucm93LWZpcnN0IHtcclxuICAgICAgICAgICAgbWFyZ2luLXRvcDogMjBweDtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbn1cclxuXHJcbi5wYWNrYWdlLXR5cGUge1xyXG4gICAgYm9yZGVyLXJhZGl1czogNXB4O1xyXG4gICAgbWFyZ2luLXRvcDogMjBweDtcclxuICAgIHBhZGRpbmc6IDEwcHg7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcclxuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBib3JkZXItY29sb3I6IHRyYW5zcGFyZW50ICFpbXBvcnRhbnQ7XHJcblxyXG4gICAgLnBhY2thZ2UtdHlwZS1zZWN0aW9uIHtcclxuICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKDIwNCwgMjE0LCAyNTUsIDAuNTQpO1xyXG4gICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgIGNvbG9yOiAjMzM0NUE3O1xyXG4gICAgICAgIGJvcmRlci1yYWRpdXM6IDEwcHggMTBweCA1cHggNXB4O1xyXG5cclxuICAgICAgICAucGFja2FnZS1pbmZvIHtcclxuICAgICAgICAgICAgY29sb3I6ICMwMDA7XHJcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTNweDtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIC5teS1yYWRpbyB7XHJcbiAgICAgICAgICAgIGJvcmRlcjogMnB4IHNvbGlkICMyZjMzOGQ7XHJcbiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICAgICAgICAgICAgd2lkdGg6IDM2cHg7XHJcbiAgICAgICAgICAgIGhlaWdodDogMzZweDtcclxuICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogd2hpdGU7XHJcbiAgICAgICAgICAgIG1hcmdpbi1sZWZ0OiAxMHB4O1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWNsaXA6IGNvbnRlbnQtYm94ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgICAgICAgICAgdG9wOiAtM3B4O1xyXG4gICAgICAgICAgICBsZWZ0OiAtMTVweDtcclxuICAgICAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgLnR5cGUtbmFtZSB7XHJcbiAgICAgICAgICAgIGNvbG9yOiB3aGl0ZTtcclxuICAgICAgICAgICAgcGFkZGluZzogNXB4IDE1cHg7XHJcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMC45NXJlbTtcclxuICAgICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gICAgICAgICAgICBmb250LWZhbWlseTogXCJQcm94aW1hIE5vdmEgQm9sZFwiO1xyXG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiAxMHB4O1xyXG4gICAgICAgICAgICB3aWR0aDogMTAwJTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbn1cclxuXHJcbi50b3AtYnRucyB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICAgIG1hcmdpbjogMjBweCAwO1xyXG4gICAgZmxleC13cmFwOiB3cmFwO1xyXG4gICAgYm94LXNpemluZzogYm9yZGVyLWJveDtcclxuXHJcbiAgICAuYnRuIHtcclxuICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlLWluO1xyXG4gICAgICAgIHBhZGRpbmc6IDJweDtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xyXG4gICAgICAgIG1hcmdpbjogMTBweDtcclxuICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG5cclxuICAgICAgICAuYnRuLWxhbmd1YWdlIHtcclxuICAgICAgICAgICAgZm9udC1mYW1pbHk6ICdQcm94aW1hIE5vdmEgQm9sZCc7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuYnRuLXRlYWNoZXItbmFtZSB7XHJcbiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDtcclxuICAgICAgICAgICAgY29sb3I6IHJnYigxMDEsIDEyMiwgMjM5KTtcclxuICAgICAgICAgICAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZS1pbjtcclxuICAgICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLmJ0bi1jbGFzc3Jvb20ge1xyXG4gICAgICAgIC8vIG1pbi13aWR0aDogNy41cmVtO1xyXG4gICAgICAgIGhlaWdodDogM3JlbTtcclxuICAgICAgICBjb2xvcjogIzMzNDVhNztcclxuXHJcbiAgICAgICAgJi5uZXcge1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWltYWdlOiAtd2Via2l0LWdyYWRpZW50KGxpbmVhciwgbGVmdCBib3R0b20sIGxlZnQgdG9wLCBjb2xvci1zdG9wKDAuMzMsICMwMDJjY2YpLCBjb2xvci1zdG9wKDAuNjcsICNhNzk2ZmYpKTtcclxuXHJcbiAgICAgICAgICAgIHNwYW4ge1xyXG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0td2hpdGUpO1xyXG4gICAgICAgICAgICAgICAgbWFyZ2luOiAxMHB4O1xyXG4gICAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgICAgICAgICBtaW4taGVpZ2h0OiAxMDAlO1xyXG4gICAgICAgICAgICAgICAgYm9yZGVyLXJhZGl1czogaW5oZXJpdDtcclxuICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICAgICAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIEBtZWRpYSBzY3JlZW4gYW5kIChtaW4td2lkdGg6IDk5MnB4KSB7XHJcbiAgICAgICAgICAgICYubGdcXDpjb2wtMyB7XHJcbiAgICAgICAgICAgICAgICBAaW5jbHVkZSBicmVha3BvaW50KHRhYmxldCkge1xyXG4gICAgICAgICAgICAgICAgICAgIHdpZHRoOiAyMCUgIWltcG9ydGFudDtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAuYnRuLWhvdXJzIHtcclxuICAgICAgICBwYWRkaW5nOiAxMHB4O1xyXG4gICAgICAgIGhlaWdodDogYXV0bztcclxuXHJcbiAgICAgICAgd2lkdGg6IDMzJTtcclxuXHJcbiAgICAgICAgQGluY2x1ZGUgYnJlYWtwb2ludCh0YWJsZXQpIHtcclxuICAgICAgICAgICAgd2lkdGg6IDE4JTtcclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2UtaW47XHJcblxyXG4gICAgICAgIC5wcmljZSB7XHJcbiAgICAgICAgICAgIG1hcmdpbjogNXB4IDA7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAuaG91cnMtaW5mbyB7XHJcbiAgICAgICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2UtaW47XHJcbiAgICAgICAgICAgIGNvbG9yOiAjMDAwO1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAuYnRuLW5ldyB7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICAgICAgZm9udC1mYW1pbHk6IFwiUHJveGltYSBOb3ZhIEJvbGRcIjtcclxuICAgIH1cclxuXHJcbiAgICAuYnRuLWxldmVsIHtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7XHJcbiAgICAgICAgd2lkdGg6IDQwcHg7XHJcbiAgICAgICAgaGVpZ2h0OiA0MHB4O1xyXG4gICAgfVxyXG59XHJcblxyXG4udG9wLWJ0bnMtaG91cnMge1xyXG4gICAgZmxleC13cmFwOiB3cmFwO1xyXG5cclxuICAgIEBpbmNsdWRlIGJyZWFrcG9pbnQodGFibGV0KSB7XHJcbiAgICAgICAgZmxleC13cmFwOiBub3dyYXA7XHJcbiAgICB9XHJcbn1cclxuXHJcbi5idXktcGFja2FnZS1oZWFkZXIge1xyXG4gICAgYmFja2dyb3VuZC1pbWFnZTogdXJsKCcvYXNzZXRzL2ltYWdlcy9wYXltZW50LnBuZycpO1xyXG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgYm9yZGVyOiAwICFpbXBvcnRhbnQ7XHJcbiAgICBwYWRkaW5nOiAxMHB4O1xyXG4gICAgYm94LXNpemluZzogYm9yZGVyLWJveDtcclxuICAgIHBvc2l0aW9uOiBzdGlja3k7XHJcbiAgICBwYWRkaW5nOiAwLjVyZW0gMXJlbTtcclxuICAgIGJvcmRlci10b3AtbGVmdC1yYWRpdXM6IDRweDtcclxuICAgIGJvcmRlci10b3AtcmlnaHQtcmFkaXVzOiA0cHg7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICAgIGJhY2tncm91bmQtc2l6ZTogY292ZXI7XHJcbiAgICBiYWNrZ3JvdW5kLXJlcGVhdDogbm8tcmVwZWF0O1xyXG4gICAgYmFja2dyb3VuZC1wb3NpdGlvbjogY2VudGVyO1xyXG4gICAgYm9yZGVyLXJhZGl1czogMTJweDtcclxuICAgIG1pbi1oZWlnaHQ6IDM3cHg7XHJcbiAgICBiYWNrZ3JvdW5kLXNpemU6IGluaGVyaXQ7XHJcbiAgICBjb2xvcjogdmFyKC0td2hpdGUpO1xyXG4gICAgZm9udC1mYW1pbHk6IFwiUHJveGltYSBOb3ZhIFJlZ3VsYXJcIiwgc2Fucy1zZXJpZjtcclxuICAgIGxldHRlci1zcGFjaW5nOiAwLjA1cmVtO1xyXG4gICAgei1pbmRleDogMjtcclxuXHJcbiAgICAuYmFjay1idXR0b24ge1xyXG4gICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICB9XHJcblxyXG4gICAgLmJ1eS1wYWNrYWdlLXRpdGxlIHtcclxuICAgICAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgICB9XHJcblxyXG4gICAgLnN0ZXBzIHtcclxuICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG5cclxuICAgICAgICAucGFja2FnZS1zdGVwIHtcclxuICAgICAgICAgICAgY29sb3I6IHZhcigtLXdoaXRlKTtcclxuICAgICAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gICAgICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBsaW5lYXI7XHJcbiAgICAgICAgICAgIHdpZHRoOiAzM3B4O1xyXG4gICAgICAgICAgICBoZWlnaHQ6IDMzcHg7XHJcbiAgICAgICAgICAgIGxpbmUtaGVpZ2h0OiAzM3B4O1xyXG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7XHJcbiAgICAgICAgICAgIG1hcmdpbjogMCAxMHB4O1xyXG4gICAgICAgIH1cclxuICAgIH1cclxufVxyXG5cclxuOmhvc3QgOjpuZy1kZWVwIHtcclxuICAgIC5lLWxhYmVsIHtcclxuICAgICAgICBmb250LXNpemU6IDE1cHggIWltcG9ydGFudDtcclxuICAgIH1cclxuXHJcbiAgICAucC1kcm9wZG93biB7XHJcbiAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgcGFkZGluZzogNC41cHg7XHJcbiAgICAgICAgYm9yZGVyLXJhZGl1czogMTBweDtcclxuICAgICAgICBtYXJnaW4tdG9wOiAxMHB4O1xyXG4gICAgfVxyXG5cclxuICAgIC5kcm9wZG93bi1ibHVlIHtcclxuICAgICAgICB3aWR0aDogNzVweCAhaW1wb3J0YW50O1xyXG5cclxuICAgICAgICAud2l0aC1pY29uIHtcclxuICAgICAgICAgICAgcGFkZGluZy1sZWZ0OiAwICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIC5md2lkdGgge1xyXG4gICAgICAgIC5kcm9wZG93bi1ibHVlIHtcclxuICAgICAgICAgICAgd2lkdGg6IDEwMCUgIWltcG9ydGFudDtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbn1cclxuXHJcbi5jb3VudHJ5LWl0ZW0ge1xyXG4gICAgZGlzcGxheTogZmxleDtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcblxyXG4gICAgLmNvdW50cnktbmFtZSB7XHJcbiAgICAgICAgbWFyZ2luLWxlZnQ6IDEwcHg7XHJcbiAgICB9XHJcblxyXG4gICAgaW1nIHtcclxuICAgICAgICB3aWR0aDogNDBweDtcclxuICAgIH1cclxufVxyXG5cclxuLmRpc2FibGVkIHtcclxuICAgIGJhY2tncm91bmQ6IHdoaXRlO1xyXG4gICAgY29sb3I6IGxpZ2h0Z3JheTtcclxuICAgIGJvcmRlcjogMXB4IHNvbGlkIGxpZ2h0Z3JheTtcclxufVxyXG5cclxuOjpuZy1kZWVwIC5tYXQtdG9vbHRpcCB7XHJcbiAgICAvKiB5b3VyIG93biBjdXN0b20gc3R5bGVzIGhlcmUgKi9cclxuICAgIC8qIGUuZy4gKi9cclxuICAgIGNvbG9yOiB5ZWxsb3c7XHJcbiAgICBmb250LXNpemU6IDE1cHg7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNzA3MDcwO1xyXG59XHJcblxyXG4ucmV2aWV3IHtcclxuICAgIHBhZGRpbmc6IDMwcHg7XHJcbiAgICBib3gtc2l6aW5nOiBib3JkZXItYm94O1xyXG5cclxuICAgIC5yZXZpZXctc2VjdGlvbiB7XHJcbiAgICAgICAgbWFyZ2luLXRvcDogMzBweDtcclxuXHJcbiAgICAgICAgLnJldmlldy1zZWN0aW9uLXRpdGxlIHtcclxuICAgICAgICAgICAgY29sb3I6IHZhcigtLW1haW4tY29sb3IpO1xyXG4gICAgICAgICAgICBmb250LXNpemU6IDIwcHg7XHJcbiAgICAgICAgICAgIGZvbnQtZmFtaWx5OiBcIlByb3hpbWEgTm92YSBCb2xkXCI7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICAucmV2aWV3LXNlY3Rpb24tY29udGVudCB7XHJcbiAgICAgICAgICAgIGNvbG9yOiB2YXIoLS1tYWluLWNvbG9yKTtcclxuICAgICAgICAgICAgZm9udC1zaXplOiAxNXB4O1xyXG4gICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICBmbGV4LXdyYXA6IHdyYXA7XHJcblxyXG4gICAgICAgICAgICAuY29sLTI1LFxyXG4gICAgICAgICAgICAuY29sLTUwIHtcclxuICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICAgICAgICAgICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG5cclxuICAgICAgICAgICAgICAgIC5pbmZvIHtcclxuICAgICAgICAgICAgICAgICAgICBjb2xvcjogIzMzNDVBNztcclxuICAgICAgICAgICAgICAgICAgICBmb250LWZhbWlseTogXCJQcm94aW1hIE5vdmEgQm9sZFwiO1xyXG4gICAgICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgICAgIC5hdmFpbGFiaWxpdHkge1xyXG4gICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7XHJcblxyXG4gICAgICAgICAgICAgICAgICAgIC5jb2wtNTAge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB3aWR0aDogNTAlO1xyXG4gICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgfVxyXG5cclxuICAgICAgICAgICAgLmNvbC0yNSB7XHJcbiAgICAgICAgICAgICAgICB3aWR0aDogMjUlO1xyXG4gICAgICAgICAgICAgICAgZmxleC1iYXNpczogMjUlO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICAuY29sLTUwIHtcclxuICAgICAgICAgICAgICAgIHdpZHRoOiA1MCU7XHJcbiAgICAgICAgICAgICAgICBmbGV4LWJhc2lzOiA1MCU7XHJcbiAgICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICAgIC5jb2wtMTAwIHtcclxuICAgICAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgICAgICAgICAgZmxleC1iYXNpczogMTAwJTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAucmV2aWV3LXNlY3Rpb246Zmlyc3QtY2hpbGQge1xyXG4gICAgICAgIG1hcmdpbi10b3A6IDA7XHJcbiAgICB9XHJcbn1cclxuXHJcbi5idG5zIHtcclxuICAgIHBvc2l0aW9uOiBzdGlja3k7XHJcbiAgICB0b3A6IDBweDtcclxuICAgIGJhY2tncm91bmQtY29sb3I6IHdoaXRlO1xyXG4gICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIGxpZ2h0Z3JheTtcclxufVxyXG5cclxuLnNlY3Rpb24tc3RlcC0zIHtcclxuICAgIGJvcmRlci1yYWRpdXM6IDI4cHg7XHJcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoI2ZmZiAwJSwgcmdiYSg1NiwgMTE1LCAyNDQsIDAuMikgMTAwJSk7XHJcbiAgICBib3JkZXItcmFkaXVzOiAyOHB4IDI4cHggOHB4IDhweDtcclxufVxyXG5cclxuLnNlY3Rpb24tY29udGVudCB7XHJcbiAgICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gICAgdHJhbnNpdGlvbjogaGVpZ2h0IDAuM3MgZWFzZS1vdXQ7XHJcbn1cclxuXHJcbi5maXhlZC1oZWlnaHQge1xyXG4gICAgaGVpZ2h0OiAyMDBweCAhaW1wb3J0YW50O1xyXG4gICAgb3ZlcmZsb3cteTogc2Nyb2xsICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbi5zZWN0aW9uLXN0ZXAtMy10aXRsZSB7XHJcbiAgICB3aWR0aDogMTAwJTtcclxuICAgIGJvcmRlcjogMXB4IHNvbGlkICMyRTNEOTA7XHJcbiAgICBmb250LXNpemU6IDE1cHg7XHJcbiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgICBwYWRkaW5nOiAwLjNyZW0gMDtcclxuICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7XHJcbiAgICBib3JkZXItcmFkaXVzOiAyOHB4O1xyXG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gICAgY29sb3I6ICMyRTNEOTA7XHJcblxyXG4gICAgLnRvZ2dsZS1zZWN0aW9uIHtcclxuICAgICAgICAuc2VjdGlvbi1hcnJvdyB7XHJcbiAgICAgICAgICAgIGJhY2tncm91bmQtaW1hZ2U6IGxpbmVhci1ncmFkaWVudCh0byB0b3AsIHJnYigxMTcsIDEzNiwgMjQ0KSwgcmdiKDk1LCAxMTAsIDIwOSksIHJnYig3MywgODUsIDE3NSksIHJnYig1MCwgNjEsIDE0MiksIHJnYigyNywgMzksIDExMCkpO1xyXG4gICAgICAgICAgICB0cmFuc2Zvcm06IHJvdGF0ZSgxODBkZWcpO1xyXG4gICAgICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgICAgICAgIHRvcDogMDtcclxuICAgICAgICAgICAgcmlnaHQ6IDA7XHJcbiAgICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47XHJcbiAgICAgICAgICAgIHRyYW5zaXRpb24tZHVyYXRpb246IDAuMnM7XHJcbiAgICAgICAgICAgIHRyYW5zaXRpb24tcHJvcGVydHk6IHRyYW5zZm9ybTtcclxuICAgICAgICAgICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7XHJcbiAgICAgICAgICAgIHdpZHRoOiAxLjhyZW07XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59XHJcblxyXG4uZW5kIHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgaGVpZ2h0OiA0NzBweDtcclxuXHJcbiAgICAuZW5kLW1zZyB7XHJcbiAgICAgICAgbWFyZ2luLXRvcDogMzBweDtcclxuICAgICAgICBmb250LWZhbWlseTogXCJQcm94aW1hIE5vdmEgQm9sZFwiO1xyXG4gICAgfVxyXG59XHJcblxyXG4ud2hpdGUtYnV0dG9uIHtcclxuICAgIHdpZHRoOiAyMDBweDtcclxuICAgIG1hcmdpbi10b3A6IDE1cHg7XHJcbn1cclxuXHJcbmJ1dHRvbi5wLWVsZW1lbnQge1xyXG4gICAgY29sb3I6IHZhcigtLXdoaXRlKTtcclxufVxyXG5cclxuLmN1cnNvci1ub25lIHtcclxuICAgIGN1cnNvcjogZGVmYXVsdCAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4ucC1jdXN0b20ge1xyXG4gICAgcGFkZGluZzogMC4zcmVtICFpbXBvcnRhbnQ7XHJcbn1cclxuXHJcbi5odnItZ2xvdzpob3ZlcixcclxuLmh2ci1nbG93OmZvY3VzLFxyXG4uaHZyLWdsb3c6YWN0aXZlIHtcclxuICAgIGJveC1zaGFkb3c6IDAgMCA0cHggcmdiYSgwLCAwLCAwLCAwLjQ2KTtcclxufVxyXG5cclxuLmxldmVsIHtcclxuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICAgIGNvbG9yOiAjZmZmICFpbXBvcnRhbnQ7XHJcbiAgICBmb250LXdlaWdodDogbm9ybWFsICFpbXBvcnRhbnQ7XHJcblxyXG4gICAgZGl2IHtcclxuICAgICAgICBmb250LWZhbWlseTogJ1Byb3hpbWEgTm92YSBSZWd1bGFyJztcclxuICAgICAgICB6LWluZGV4OiAxO1xyXG4gICAgfVxyXG5cclxuICAgICY6YWZ0ZXIge1xyXG4gICAgICAgIGNvbnRlbnQ6ICcgJztcclxuICAgICAgICBkaXNwbGF5OiBibG9jaztcclxuICAgICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgICAgbGVmdDogMDtcclxuICAgICAgICB0b3A6IDA7XHJcbiAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgaGVpZ2h0OiAxMDAlO1xyXG4gICAgICAgIGJhY2tncm91bmQtaW1hZ2U6IHVybCgvYXNzZXRzL2ltYWdlcy9kYXNoYm9hcmQvZ29hbHMvbGV2ZWwtYnViYmxlLnBuZykgIWltcG9ydGFudDtcclxuICAgICAgICBiYWNrZ3JvdW5kLXJlcGVhdDogbm8tcmVwZWF0O1xyXG4gICAgICAgIGJhY2tncm91bmQtcG9zaXRpb246IDUwJSAwO1xyXG4gICAgICAgIGJhY2tncm91bmQtc2l6ZTogNDBweDtcclxuICAgICAgICB6LWluZGV4OiAwO1xyXG4gICAgICAgIG9wYWNpdHk6IDAuODtcclxuICAgIH1cclxufVxyXG5cclxuLmxldmVsLWRpc2FibGVkIHtcclxuICAgIGJvcmRlcjogMXB4IHNvbGlkICNDQ0Q2RkY7XHJcbiAgICBiYWNrZ3JvdW5kLWltYWdlOiBub25lO1xyXG5cclxuICAgICY6YWZ0ZXIge1xyXG4gICAgICAgIG9wYWNpdHk6IDAuMjtcclxuICAgIH1cclxufVxyXG5cclxuLmxldmVsLWRlZmF1bHQge1xyXG4gICAgYm9yZGVyOiAxcHggc29saWQgcmdiKDEwMSwgMTIyLCAyMzkpO1xyXG4gICAgY29sb3I6IHJnYigxMDEsIDEyMiwgMjM5KTtcclxuICAgIGJhY2tncm91bmQtaW1hZ2U6IG5vbmU7XHJcbn1cclxuXHJcbi5sZXZlbC1zZWxlY3RlZCB7XHJcbiAgICBib3gtc2hhZG93OiAwIDAgNHB4IHJnYmEoMCwgMCwgMCwgMC40Nik7XHJcbiAgICBtYXJnaW46IDRweDtcclxuICAgIGJhY2tncm91bmQtY29sb3I6IHRyYW5zcGFyZW50O1xyXG4gICAgY29sb3I6ICNmZmY7XHJcbiAgICBib3JkZXI6IG5vbmU7XHJcbiAgICBvdmVyZmxvdzogaGlkZGVuO1xyXG4gICAgZmxvYXQ6IGxlZnQ7XHJcbiAgICBib3JkZXItcmFkaXVzOiA1MCU7XHJcblxyXG4gICAgJjphZnRlciB7XHJcbiAgICAgICAgb3BhY2l0eTogMTtcclxuICAgIH1cclxufVxyXG5cclxuLmN1c3RvbS1ib3JkZXIge1xyXG4gICAgYm9yZGVyLXJhZGl1czogMCAwIDEwcHggMTBweCAhaW1wb3J0YW50O1xyXG59XHJcblxyXG4uc2VsZWN0ZWQ6OmJlZm9yZSB7XHJcbiAgICBjb250ZW50OiB1cmwoL2Fzc2V0cy9pY29ucy9jaGVjay1ibHVlLnN2Zyk7XHJcbiAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICB0b3A6IDA7XHJcbiAgICBsZWZ0OiAwO1xyXG4gICAgbGVmdDogNTAlO1xyXG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGUoLTUwJSwgLTUwJSk7XHJcbiAgICB0b3A6IDU1JTtcclxuICAgIHotaW5kZXg6IDI7XHJcbn1cclxuXHJcbi5pbnB1dC1maWVsZHMgLmlucHV0LWZpZWxkIHtcclxuICAgIG1hcmdpbi10b3A6IDAgIWltcG9ydGFudDtcclxufVxyXG5cclxuLnBheW1lbnQtc3RlcCB7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbn1cclxuXHJcbi5wcmV2LWJ1dHRvbi1vdXRsaW5lZCxcclxuLm5leHQtYnV0dG9uLW91dGxpbmVkIHtcclxuICAgIGJvcmRlci1yYWRpdXM6IDUwcHg7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjNzA4MkU2ICFpbXBvcnRhbnQ7XHJcblxyXG4gICAgJjpob3ZlciB7XHJcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzcwODJFNiAhaW1wb3J0YW50O1xyXG4gICAgICAgIGJveC1zaGFkb3c6IG5vbmUgIWltcG9ydGFudDtcclxuICAgICAgICBjb2xvcjogI2ZmZiAhaW1wb3J0YW50O1xyXG4gICAgfVxyXG5cclxuICAgICYuaW5hY3RpdmUge1xyXG4gICAgICAgIG9wYWNpdHk6IDAuMztcclxuICAgICAgICBjdXJzb3I6IG5vdC1hbGxvd2VkO1xyXG5cclxuICAgICAgICAmOmhvdmVyIHtcclxuICAgICAgICAgICAgY29sb3I6ICNmZmY7XHJcbiAgICAgICAgICAgIGJvcmRlci1jb2xvcjogI2ZmZjtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbn1cclxuXHJcbi5wcmV2LWJ1dHRvbi1vdXRsaW5lZCB7XHJcbiAgICAvLyBvcGFjaXR5OiAwLjY7XHJcbn1cclxuXHJcbi5zcGxpdC1wYXktYnV0dG9uLW91dGxpbmVkIHtcclxuICAgIGJvcmRlci1yYWRpdXM6IDUwcHg7XHJcbiAgICBib3gtc2hhZG93OiBub25lO1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogIzJGQjlEMyAhaW1wb3J0YW50O1xyXG5cclxuICAgICY6aG92ZXIge1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICM3MDgyRTYgIWltcG9ydGFudDtcclxuICAgICAgICBib3gtc2hhZG93OiBub25lICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgY29sb3I6ICNmZmYgIWltcG9ydGFudDtcclxuICAgIH1cclxuXHJcbiAgICAmLmluYWN0aXZlIHtcclxuICAgICAgICBvcGFjaXR5OiAwLjM7XHJcbiAgICAgICAgY3Vyc29yOiBub3QtYWxsb3dlZDtcclxuXHJcbiAgICAgICAgJjpob3ZlciB7XHJcbiAgICAgICAgICAgIGNvbG9yOiAjZmZmO1xyXG4gICAgICAgICAgICBib3JkZXItY29sb3I6ICNmZmY7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59XHJcblxyXG4uYnV5LWJ1dHRvbiB7XHJcbiAgICBib3gtc2hhZG93OiBub25lICFpbXBvcnRhbnQ7XHJcbiAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoIzliYWFmZiAwJSwgIzE1MmNhZiAxMDAlKTtcclxuICAgIGJvcmRlci1yYWRpdXM6IDUwcHg7XHJcblxyXG4gICAgJjpob3ZlciB7XHJcbiAgICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KCMxNTJjYWYgMCUsICM5YmFhZmYgMTAwJSkgIWltcG9ydGFudDtcclxuICAgICAgICBib3gtc2hhZG93OiBub25lICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgY29sb3I6ICNmZmYgIWltcG9ydGFudDtcclxuICAgIH1cclxufVxyXG5cclxuLmlucHV0LWVsZW1lbnQge1xyXG4gICAgZm9udC1zaXplOiAwLjg1cmVtICFpbXBvcnRhbnQ7XHJcbiAgICBmb250LWZhbWlseTogaW5oZXJpdDtcclxuICAgIG91dGxpbmU6IG5vbmUgIWltcG9ydGFudDtcclxufVxyXG5cclxuLnNwbGl0LXRleHQge1xyXG4gICAgY29sb3I6ICNDQ0Q2RkY7XHJcbn1cclxuXHJcbjpob3N0OjpuZy1kZWVwIC50ZXJtcy1jaGVja2JveCB7XHJcblxyXG4gICAgLnAtY2hlY2tib3gge1xyXG4gICAgICAgIHdpZHRoOiAxOHB4O1xyXG4gICAgICAgIGhlaWdodDogMThweDtcclxuICAgIH1cclxuXHJcbiAgICAucC1jaGVja2JveC1sYWJlbCB7XHJcbiAgICAgICAgZm9udC1zaXplOiAxNnB4O1xyXG4gICAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuXHJcbiAgICAgICAgQG1lZGlhIHNjcmVlbiBhbmQgKG1heC13aWR0aDogNzY4cHgpIHtcclxuICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4O1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAucC1jaGVja2JveCAucC1jaGVja2JveC1ib3gucC1oaWdobGlnaHQgLnAtY2hlY2tib3gtaWNvbi5waS1jaGVjazpiZWZvcmUge1xyXG4gICAgICAgIHRvcDogMXB4O1xyXG4gICAgICAgIGxlZnQ6IC01cHg7XHJcbiAgICB9XHJcblxyXG4gICAgLnAtY2hlY2tib3ggLnAtY2hlY2tib3gtYm94IHtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiA1MHB4O1xyXG4gICAgfVxyXG5cclxuICAgIC5wLWNoZWNrYm94IC5wLWNoZWNrYm94LWJveC5wLWhpZ2hsaWdodCB7XHJcbiAgICAgICAgYm9yZGVyLXJhZGl1czogNTBweDtcclxuICAgIH1cclxuXHJcbiAgICAucC1jaGVja2JveCAucC1jaGVja2JveC1ib3ggLnAtY2hlY2tib3gtaWNvbiB7XHJcbiAgICAgICAgdHJhbnNmb3JtOiBzY2FsZSgxLjMpO1xyXG4gICAgfVxyXG5cclxuICAgIC5wLWNvbXBvbmVudCB7XHJcblxyXG4gICAgICAgIC5wLWNoZWNrYm94LWJveCB7XHJcbiAgICAgICAgICAgIHdpZHRoOiAxOHB4ICFpbXBvcnRhbnQ7XHJcbiAgICAgICAgICAgIGhlaWdodDogMThweCAhaW1wb3J0YW50O1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICAudGVhY2hlci1jaGVja2JveC1pbm5lciB7fVxyXG5cclxuICAgIC5wLWNoZWNrYm94LWxhYmVsIHt9XHJcbn1cclxuXHJcbi5ib3R0b20tYnV0dG9ucyB7XHJcbiAgICBAaW5jbHVkZSBicmVha3BvaW50KG1vYmlsZSkge1xyXG4gICAgIGJvdHRvbTogMDtcclxuICAgICBsZWZ0OiAwO1xyXG4gICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjMmUzYjhmO1xyXG4gICAgfVxyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"],\n    data: {\n      animation: [slideInOut]\n    }\n  });\n}", "map": {"version": 3, "names": ["UntypedFormControl", "UntypedFormGroup", "Validators", "take", "PackageType", "slideInOut", "SubSink", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind2", "ctx_r1", "activePackages", "expiresOn", "ɵɵlistener", "ExtendPackageComponent_div_30_Template_div_click_0_listener", "ctx_r3", "ɵɵrestoreView", "_r3", "hour_r5", "$implicit", "i_r6", "index", "ɵɵnextContext", "ɵɵresetView", "selectHours", "ɵɵpropertyInterpolate1", "price", "ɵɵproperty", "getBtnStyle", "ɵɵtextInterpolate1", "extendTime", "ɵɵelement", "country_r7", "image", "ɵɵsanitizeUrl", "name", "ɵɵtemplate", "ExtendPackageComponent_div_64_div_4_Template", "isSubmitted", "errorControl", "tax", "touched", "errors", "required", "ExtendPackageComponent_div_95_div_4_Template", "tin", "ExtendPackageComponent", "constructor", "location", "activatedRoute", "classroomService", "packageService", "generalService", "sanitizer", "router", "subs", "classroomId", "selectedHoursIndex", "selectedP<PERSON>ageToBuy", "classroom", "mltPackagesToBuy", "form", "buyerUserDetails", "mltPricesExtend", "countries", "everypayLink", "isReceipt", "isInvoice", "ngOnInit", "getExtentionOptions", "pipe", "subscribe", "res", "snapshot", "paramMap", "get", "sink", "getClassroom", "push", "activePackage", "type", "getPackagesToBuy", "initializeForm", "transform", "guid", "bypassSecurityTrustResourceUrl", "hour", "i", "deSelectedBtnStyle", "selectedBtnStyle", "id", "extraId", "btn", "document", "getElementById", "style", "color", "backgroundColor", "background", "getCasualColor", "REGULAR", "FLEX", "PREMIUM", "notToBeDisabled", "goBack", "back", "getRadioStyle", "packageType", "getTotalPriceOfPackage", "packageToBuy", "priceHourly", "costPlus", "chooseReceipt", "chooseInvoice", "fname", "validators", "lname", "company", "profession", "country", "street", "number", "city", "postcode", "email", "phone", "controls", "purchase", "valid", "formValue", "value", "streetName", "streetNumber", "req", "packageId", "extetionTime", "buyExtension", "result", "slideNativeElements", "everypay", "nativeElement", "closePayment", "navigate", "_", "ɵɵdirectiveInject", "i1", "Location", "i2", "ActivatedRoute", "i3", "ClassroomService", "i4", "PackageService", "i5", "GeneralService", "i6", "Dom<PERSON><PERSON><PERSON>zer", "Router", "_2", "selectors", "viewQuery", "ExtendPackageComponent_Query", "rf", "ctx", "ExtendPackageComponent_Template_div_click_3_listener", "_r1", "ExtendPackageComponent_div_24_Template", "ExtendPackageComponent_div_30_Template", "ExtendPackageComponent_div_43_Template", "ExtendPackageComponent_div_48_Template", "ExtendPackageComponent_ng_template_61_Template", "ExtendPackageComponent_div_62_Template", "ExtendPackageComponent_div_63_Template", "ExtendPackageComponent_div_64_Template", "ExtendPackageComponent_div_69_Template", "ExtendPackageComponent_div_74_Template", "ExtendPackageComponent_div_79_Template", "ExtendPackageComponent_div_84_Template", "ExtendPackageComponent_div_89_Template", "ExtendPackageComponent_div_94_Template", "ExtendPackageComponent_div_95_Template", "ExtendPackageComponent_Template_div_click_97_listener", "ExtendPackageComponent_Template_img_click_104_listener", "undefined", "language", "activeLevel", "ɵɵsanitizeResourceUrl"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\extend-package\\extend-package.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\extend-package\\extend-package.component.html"], "sourcesContent": ["import { Location } from '@angular/common';\r\nimport { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\r\nimport { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { take } from 'rxjs/operators';\r\nimport { Classroom, ClassroomType } from 'src/app/core/models/classroom.model';\r\nimport { BuyerUserDetails, Package, PackageToBuy, PackageToExtend, PackageType } from 'src/app/core/models/package.model';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { PackageService } from 'src/app/core/services/package.service';\r\nimport { slideInOut } from 'src/app/helpers/my-animations';\r\nimport { SubSink } from 'subsink';\r\n\r\n@Component({\r\n  selector: 'app-extend-package',\r\n  templateUrl: './extend-package.component.html',\r\n  styleUrls: ['./extend-package.component.scss'],\r\n  animations: [slideInOut]\r\n})\r\nexport class ExtendPackageComponent implements OnInit {\r\n  @ViewChild('everypay') public everypay: any;\r\n\r\n  private subs = new SubSink();\r\n  classroomId: string = \"\";\r\n  public activePackages: Package[] = [];\r\n  public selectedHoursIndex: number = 0;\r\n  public selectedPackageToBuy: PackageToBuy = {} as PackageToBuy;\r\n  public classroom: Classroom = {} as Classroom;\r\n  mltPackagesToBuy: PackageToBuy[] = [];\r\n  public form: UntypedFormGroup = new UntypedFormGroup({});\r\n  public buyerUserDetails: BuyerUserDetails = {} as BuyerUserDetails\r\n\r\n  mltPricesExtend: PackageToExtend[] = []\r\n  public countries = this.generalService.countries\r\n  everypayLink: SafeResourceUrl = 'https://mlt.ui.kiddobrains.com/extention-payment.html?guid=guid'\r\n\r\n\r\n  constructor(\r\n    private location: Location,\r\n    private activatedRoute: ActivatedRoute,\r\n    private classroomService: ClassroomService,\r\n    private packageService: PackageService,\r\n    private generalService: GeneralService,\r\n    private sanitizer: DomSanitizer,\r\n    private router: Router\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n\r\n    this.packageService.getExtentionOptions().pipe(take(1)).subscribe(res => {\r\n      this.mltPricesExtend = res;\r\n    })\r\n    this.classroomId = this.activatedRoute.snapshot.paramMap.get(\"classroom_id\")!\r\n    this.subs.sink = this.classroomService.getClassroom(this.classroomId).subscribe(res => {\r\n      this.classroom = res;\r\n      this.activePackages.push(res.activePackage!)\r\n      this.selectedPackageToBuy.type = res.activePackage?.type!\r\n    })\r\n    this.packageService.getPackagesToBuy().pipe(take(1)).subscribe(res => {\r\n      this.mltPackagesToBuy = res;\r\n    })\r\n    this.initializeForm();\r\n    this.everypayLink = this.transform('');\r\n\r\n  }\r\n\r\n  transform(guid: string) {\r\n    return this.sanitizer.bypassSecurityTrustResourceUrl('https://mlt.ui.kiddobrains.com/extention-payment.html?guid=' + guid + \"&amount=\" + this.mltPricesExtend[this.selectedHoursIndex]?.price);\r\n  }\r\n\r\n  selectHours(hour: any, i: number) {\r\n    this.deSelectedBtnStyle(`hour-btn-${this.mltPricesExtend[this.selectedHoursIndex].price}`, `hour-btn-hours-${this.mltPricesExtend[this.selectedHoursIndex].price}`)\r\n    this.deSelectedBtnStyle(`hour-btn-${this.mltPricesExtend[this.selectedHoursIndex].price}`, `hour-btn-per-${this.mltPricesExtend[this.selectedHoursIndex].price}`)\r\n    this.selectedHoursIndex = i;\r\n    this.selectedBtnStyle(`hour-btn-${this.mltPricesExtend[this.selectedHoursIndex].price}`, `hour-btn-hours-${this.mltPricesExtend[this.selectedHoursIndex].price}`)\r\n    this.selectedBtnStyle(`hour-btn-${this.mltPricesExtend[this.selectedHoursIndex].price}`, `hour-btn-per-${this.mltPricesExtend[this.selectedHoursIndex].price}`)\r\n  }\r\n\r\n  deSelectedBtnStyle(id: string, extraId?: string) {\r\n    let btn = document.getElementById(id)\r\n    btn!.style.color = \"#A4A2E6\";\r\n    btn!.style.backgroundColor = \"white\";\r\n    btn!.style.background = \"white\";\r\n    if (extraId)\r\n      document.getElementById(extraId)!.style.color = \"#2d2a4b\"\r\n  }\r\n\r\n  selectedBtnStyle(id: string, extraId?: string) {\r\n    let btn = document.getElementById(id)\r\n    btn!.style.backgroundColor = this.getCasualColor(this.selectedPackageToBuy.type);\r\n    btn!.style.color = \"white\";\r\n    if (extraId)\r\n      document.getElementById(extraId)!.style.color = \"white\"\r\n  }\r\n\r\n  getCasualColor(type: string) {\r\n    if (type == PackageType.REGULAR) {\r\n      return \"#A4A2E6\"\r\n    }\r\n    if (type == PackageType.FLEX) {\r\n      return \"#4040CD\"\r\n    }\r\n    if (type == PackageType.PREMIUM) {\r\n      return \"#B5179E\"\r\n    }\r\n    return \"\"\r\n  }\r\n\r\n  getBtnStyle(notToBeDisabled: boolean = false) {\r\n    return {\r\n      'border': `1px solid ${this.getCasualColor(this.selectedPackageToBuy.type)}`,\r\n      'color': `${this.getCasualColor(this.selectedPackageToBuy.type)}`\r\n    }\r\n  }\r\n\r\n  goBack() {\r\n    this.location.back();\r\n  }\r\n\r\n  getRadioStyle(packageType: any) {\r\n    if (this.selectedPackageToBuy.type == packageType.type) {\r\n      return {\r\n        'border': `3px solid ${this.getCasualColor(this.selectedPackageToBuy.type)}`,\r\n        'background': `${this.getCasualColor(this.selectedPackageToBuy.type)}`\r\n      }\r\n    }\r\n    return {\r\n      'border': \"2px solid #707070\",\r\n      'background': \"white\"\r\n    }\r\n  }\r\n\r\n  getTotalPriceOfPackage(packageToBuy: PackageToBuy) {\r\n    return packageToBuy.priceHourly[this.selectedHoursIndex].price + packageToBuy.costPlus;\r\n  }\r\n  isReceipt: boolean = true;\r\n  isInvoice: boolean = false;\r\n  isSubmitted: boolean = false;\r\n\r\n  chooseReceipt() {\r\n    this.isReceipt = true\r\n    this.isInvoice = false\r\n  }\r\n\r\n  chooseInvoice() {\r\n    this.isReceipt = false\r\n    this.isInvoice = true\r\n  }\r\n\r\n  initializeForm() {\r\n    this.form = new UntypedFormGroup({\r\n      fname: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      lname: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      company: new UntypedFormControl(null, {}),\r\n      profession: new UntypedFormControl(null, {}),\r\n      country: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      street: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      number: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      city: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      postcode: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      tax: new UntypedFormControl(null, {\r\n      }),\r\n      email: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      phone: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      tin: new UntypedFormControl(null, {\r\n      }),\r\n    })\r\n  }\r\n\r\n  get errorControl() {\r\n    return this.form.controls\r\n  }\r\n\r\n\r\n  purchase() {\r\n    this.isSubmitted = true;\r\n    if(this.selectedHoursIndex == 0){\r\n      return;\r\n    }\r\n    if (this.form.valid) {\r\n      let formValue = this.form.value\r\n      this.buyerUserDetails.fname = formValue.fname\r\n      this.buyerUserDetails.lname = formValue.lname\r\n      this.buyerUserDetails.email = formValue.email\r\n      this.buyerUserDetails.city = formValue.city\r\n      this.buyerUserDetails.company = formValue.company\r\n      this.buyerUserDetails.streetName = formValue.street\r\n      this.buyerUserDetails.streetNumber = formValue.number\r\n      this.buyerUserDetails.tax = formValue.tax\r\n      this.buyerUserDetails.profession = formValue.profession\r\n      this.buyerUserDetails.postcode = formValue.postcode\r\n      this.buyerUserDetails.tin = formValue.tin\r\n      this.buyerUserDetails.phone = formValue.phone\r\n      this.buyerUserDetails.country = formValue.country.name\r\n    } else {\r\n      return\r\n    }\r\n    let req = {\r\n      packageId : this.activePackages[0].id,\r\n      extetionTime : this.mltPricesExtend[this.selectedHoursIndex].extendTime,\r\n      buyerUserDetails: this.buyerUserDetails\r\n    }\r\n    this.packageService.buyExtension(req).pipe(take(1)).subscribe((res: any) => {\r\n      this.everypayLink = this.transform(res.result);\r\n      this.generalService.slideNativeElements(true, this.everypay.nativeElement)\r\n    })\r\n  }\r\n\r\n  closePayment() {\r\n    this.generalService.slideNativeElements(false, this.everypay.nativeElement)\r\n    this.router.navigate(['dashboard'])\r\n  }\r\n}\r\n", "<div [@slideInOut] class=\"buy-package\">\r\n    <div id=\"class\">\r\n        <div class=\"buy-package-header\">\r\n            <div class=\"back-button\" (click)=\"goBack()\" style=\"position: absolute\">\r\n                <img src=\"/assets/icons/back-main-color.svg\">\r\n                <div style=\"margin-left:20px\">Back</div>\r\n            </div>\r\n            <div class=\"buy-package-title\" style=\"width:100%\">\r\n                Extend Package\r\n            </div>\r\n        </div>\r\n        <div style=\"background: white; border-radius: 24px;\">\r\n            <div class=\"section\">\r\n                <div style=\"font-weight:bold; padding: 10px 0\">\r\n                    Active Package\r\n                </div>\r\n                <div style=\"width: 81%; margin-left: auto;\">\r\n                    <app-packages-progress [packages]=\"activePackages\"></app-packages-progress>\r\n                    <div\r\n                        style=\"display: flex; justify-content: space-between; font-size:13px; color: #93949E; padding:10px 20px;\">\r\n                        <div>\r\n                            Language: <strong>{{classroom.language}}</strong> \r\n                        </div>\r\n                        <div>\r\n                            Level: <strong>{{classroom.activeLevel}}</strong> \r\n                        </div>\r\n                        <div *ngIf=\"activePackages[0]\">\r\n                            Expiration Date: <strong>{{activePackages[0].expiresOn | date: 'dd-MM-yyyy'}}</strong> \r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"section\">\r\n                <div style=\"font-weight:bold; padding: 10px 0\">\r\n                    Select Extention\r\n                </div>\r\n                <div style=\"width: 81%; margin-left: auto;\">\r\n                    <div class=\"top-btns top-btns-hours\">\r\n                        <div *ngFor=\"let hour of mltPricesExtend; let i = index\" class=\"btn btn-hours hvr-glow\" style=\"height:100px;\"\r\n                            [ngStyle]=\"getBtnStyle(true)\" id=\"hour-btn-{{hour.price}}\" (click)=\"selectHours(hour, i)\">\r\n                            <div class=\"hours-info\" id=\"hour-btn-hours-{{hour.price}}\">\r\n                                {{hour.extendTime}}\r\n                            </div>\r\n                            <div class=\"price\" id=\"hour-btn-per-{{hour.price}}\">\r\n                                {{hour.price}}&euro;\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"left\">\r\n                <div style=\"font-weight:bold; padding: 10px 5px\">\r\n                    Add Billing Details\r\n                </div>\r\n                <form [formGroup]=\"form\">\r\n                    <div class=\"input-fields\">\r\n                        <div class=\"input-field\">\r\n                            Select Billing Type\r\n                        </div>\r\n                        <div class=\"input-field\">\r\n                            <!-- TODO: add new radiobuttons from primeNG -->\r\n                            <!-- <ejs-radiobutton class=\"e-info\" (change)=\"chooseReceipt()\" style=\"font-size:18px\"\r\n                                label=\"Receipt\" name=\"default\" [checked]=\"true\"></ejs-radiobutton>\r\n                            <ejs-radiobutton class=\"e-info\" (change)=\"chooseInvoice()\" style=\"margin-left:30px;\"\r\n                                label=\"Invoice\" name=\"default\"></ejs-radiobutton> -->\r\n                        </div>\r\n                        <div class=\"input-field\">\r\n                            <div class=\"input-element-title\">* First Name</div>\r\n                            <input class=\"input-element\" formControlName=\"fname\" type=\"text\">\r\n                            <div *ngIf=\"(isSubmitted || errorControl.fname.touched) && errorControl.fname.errors?.required\"\r\n                                class=\"input-error\">* First Name is required</div>\r\n                        </div>\r\n                        <div class=\"input-field\">\r\n                            <div class=\"input-element-title\">* Last Name</div>\r\n                            <input class=\"input-element\" formControlName=\"lname\" type=\"text\">\r\n                            <div *ngIf=\"(isSubmitted || errorControl.lname.touched) && errorControl.lname.errors?.required\"\r\n                                class=\"input-error\">* Last Name is required</div>\r\n                        </div>\r\n                        <div class=\"input-field\">\r\n                            <div class=\"input-element-title\">Company (optional)</div>\r\n                            <input class=\"input-element\" formControlName=\"company\" type=\"text\">\r\n                        </div>\r\n                        <div class=\"input-field\">\r\n                            <div class=\"input-element-title\">Profession (optional)</div>\r\n                            <input class=\"input-element\" formControlName=\"profession\" type=\"text\">\r\n                        </div>\r\n                        <div class=\"input-field\">\r\n                            <div class=\"input-element-title\">* Country</div>\r\n                            <p-dropdown autocomplete=\"autocomplete_off_hack_xfr4!k\" [options]=\"countries\"\r\n                                optionLabel=\"name\" filterBy=\"name\" [showClear]=\"true\" placeholder=\"Select Country\"\r\n                                formControlName=\"country\">\r\n                                <ng-template let-country pTemplate=\"item\">\r\n                                    <div class=\"country-item\">\r\n                                        <img [src]=\"country.image\">\r\n                                        <div class=\"country-name\">{{country.name}}</div>\r\n                                    </div>\r\n                                </ng-template>\r\n                            </p-dropdown>\r\n                            <div *ngIf=\"(isSubmitted || errorControl.country.touched) && errorControl.country.errors?.required\"\r\n                                class=\"input-error\">* Country is required</div>\r\n                        </div>\r\n                        <div *ngIf=\"isReceipt\" style=\"width:100%\"></div>\r\n                        <div *ngIf=\"isInvoice\" class=\"input-field\">\r\n                            <div class=\"input-element-title\">* Tax Office</div>\r\n                            <input class=\"input-element\" formControlName=\"street\" type=\"text\">\r\n                            <div *ngIf=\"(isSubmitted || errorControl.tax.touched) && errorControl.tax.errors?.required\"\r\n                                class=\"input-error\">* Tax Office is required</div>\r\n                        </div>\r\n                        <div class=\"input-field\">\r\n                            <div class=\"input-element-title\">* Street Address</div>\r\n                            <input class=\"input-element\" formControlName=\"street\" type=\"text\">\r\n                            <div *ngIf=\"(isSubmitted || errorControl.street.touched) && errorControl.street.errors?.required\"\r\n                                class=\"input-error\">* Street Adrdess is required</div>\r\n                        </div>\r\n                        <div class=\"input-field\">\r\n                            <div class=\"input-element-title\">* Street No</div>\r\n                            <input class=\"input-element\" formControlName=\"number\" type=\"text\">\r\n                            <div *ngIf=\"(isSubmitted || errorControl.number.touched) && errorControl.number.errors?.required\"\r\n                                class=\"input-error\">* Street Number is required</div>\r\n                        </div>\r\n                        <div class=\"input-field\">\r\n                            <div class=\"input-element-title\">* City</div>\r\n                            <input class=\"input-element\" formControlName=\"city\" type=\"text\">\r\n                            <div *ngIf=\"(isSubmitted || errorControl.city.touched) && errorControl.city.errors?.required\"\r\n                                class=\"input-error\">* City is required</div>\r\n                        </div>\r\n                        <div class=\"input-field\">\r\n                            <div class=\"input-element-title\">* Postcode</div>\r\n                            <input class=\"input-element\" formControlName=\"postcode\" type=\"text\">\r\n                            <div *ngIf=\"(isSubmitted || errorControl.postcode.touched) && errorControl.postcode.errors?.required\"\r\n                                class=\"input-error\">* Postcode is required</div>\r\n                        </div>\r\n                        <div class=\"input-field\">\r\n                            <div class=\"input-element-title\">* Email</div>\r\n                            <input class=\"input-element\" formControlName=\"email\" type=\"text\">\r\n                            <div *ngIf=\"(isSubmitted || errorControl.email.touched) && errorControl.email.errors?.required\"\r\n                                class=\"input-error\">* Email is required</div>\r\n                        </div>\r\n                        <div class=\"input-field\">\r\n                            <div class=\"input-element-title\">* Phone</div>\r\n                            <input class=\"input-element\" formControlName=\"phone\" type=\"text\">\r\n                            <div *ngIf=\"(isSubmitted || errorControl.phone.touched) && errorControl.phone.errors?.required\"\r\n                                class=\"input-error\">* Phone is required</div>\r\n                        </div>\r\n                        <div *ngIf=\"isInvoice\" class=\"input-field\">\r\n                            <div class=\"input-element-title\">* T.I.N. / V.A.T.</div>\r\n                            <input class=\"input-element\" formControlName=\"tin\" type=\"text\">\r\n                            <div *ngIf=\"(isSubmitted || errorControl.tin.touched) && errorControl.tin.errors?.required\"\r\n                                class=\"input-error\">* T.I.N. / V.A.T. is required</div>\r\n                        </div>\r\n                    </div>\r\n                </form>\r\n                <div class=\"btns\">\r\n                    <div class=\"main-color-button\" (click)=\"purchase()\">\r\n                        Purchase\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n\r\n<div class=\"modal no-visibility\" #everypay>\r\n    <div class=\"popup-title\">\r\n        <div>Payment</div>\r\n        <img (click)=\"closePayment()\" src=\"/assets/icons/close.png\" class=\"close-img close-img-abs hvr-glow\">\r\n    </div>\r\n    <iframe style=\"width:100%; display: flex; justify-content:center\" height=\"800px\" [src]=\"everypayLink\" title=\"Package Payment\" frameborder=\"0\" allowfullscreen></iframe>\r\n</div>"], "mappings": "AAEA,SAASA,kBAAkB,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,gBAAgB;AAGjF,SAASC,IAAI,QAAQ,gBAAgB;AAErC,SAAmEC,WAAW,QAAQ,mCAAmC;AAIzH,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;;;;ICcTC,EAAA,CAAAC,cAAA,UAA+B;IAC3BD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAE,MAAA,GAAoD;;IACjFF,EADiF,CAAAG,YAAA,EAAS,EACpF;;;;IADuBH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAM,WAAA,OAAAC,MAAA,CAAAC,cAAA,IAAAC,SAAA,gBAAoD;;;;;;IAWjFT,EAAA,CAAAC,cAAA,cAC8F;IAA/BD,EAAA,CAAAU,UAAA,mBAAAC,4DAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,OAAA,GAAAH,MAAA,CAAAI,SAAA;MAAA,MAAAC,IAAA,GAAAL,MAAA,CAAAM,KAAA;MAAA,MAAAX,MAAA,GAAAP,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASb,MAAA,CAAAc,WAAA,CAAAN,OAAA,EAAAE,IAAA,CAAoB;IAAA,EAAC;IACzFjB,EAAA,CAAAC,cAAA,cAA2D;IACvDD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAoD;IAChDD,EAAA,CAAAE,MAAA,GACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;;;;;IAP4BH,EAAA,CAAAsB,sBAAA,oBAAAP,OAAA,CAAAQ,KAAA,KAA4B;IAA1DvB,EAAA,CAAAwB,UAAA,YAAAjB,MAAA,CAAAkB,WAAA,OAA6B;IACLzB,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAsB,sBAAA,0BAAAP,OAAA,CAAAQ,KAAA,KAAkC;IACtDvB,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAA0B,kBAAA,MAAAX,OAAA,CAAAY,UAAA,MACJ;IACmB3B,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAsB,sBAAA,wBAAAP,OAAA,CAAAQ,KAAA,KAAgC;IAC/CvB,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAA0B,kBAAA,MAAAX,OAAA,CAAAQ,KAAA,YACJ;;;;;IAwBAvB,EAAA,CAAAC,cAAA,cACwB;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAKtDH,EAAA,CAAAC,cAAA,cACwB;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAgB7CH,EAAA,CAAAC,cAAA,cAA0B;IACtBD,EAAA,CAAA4B,SAAA,cAA2B;IAC3B5B,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAC9CF,EAD8C,CAAAG,YAAA,EAAM,EAC9C;;;;IAFGH,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAwB,UAAA,QAAAK,UAAA,CAAAC,KAAA,EAAA9B,EAAA,CAAA+B,aAAA,CAAqB;IACA/B,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAwB,UAAA,CAAAG,IAAA,CAAgB;;;;;IAItDhC,EAAA,CAAAC,cAAA,cACwB;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAEvDH,EAAA,CAAA4B,SAAA,cAAgD;;;;;IAI5C5B,EAAA,CAAAC,cAAA,cACwB;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHtDH,EADJ,CAAAC,cAAA,cAA2C,cACN;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACnDH,EAAA,CAAA4B,SAAA,gBAAkE;IAClE5B,EAAA,CAAAiC,UAAA,IAAAC,4CAAA,kBACwB;IAC5BlC,EAAA,CAAAG,YAAA,EAAM;;;;IAFIH,EAAA,CAAAI,SAAA,GAAoF;IAApFJ,EAAA,CAAAwB,UAAA,UAAAjB,MAAA,CAAA4B,WAAA,IAAA5B,MAAA,CAAA6B,YAAA,CAAAC,GAAA,CAAAC,OAAA,MAAA/B,MAAA,CAAA6B,YAAA,CAAAC,GAAA,CAAAE,MAAA,kBAAAhC,MAAA,CAAA6B,YAAA,CAAAC,GAAA,CAAAE,MAAA,CAAAC,QAAA,EAAoF;;;;;IAM1FxC,EAAA,CAAAC,cAAA,cACwB;IAAAD,EAAA,CAAAE,MAAA,mCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAK1DH,EAAA,CAAAC,cAAA,cACwB;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAKzDH,EAAA,CAAAC,cAAA,cACwB;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAKhDH,EAAA,CAAAC,cAAA,cACwB;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAKpDH,EAAA,CAAAC,cAAA,cACwB;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAKjDH,EAAA,CAAAC,cAAA,cACwB;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAKjDH,EAAA,CAAAC,cAAA,cACwB;IAAAD,EAAA,CAAAE,MAAA,oCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAH3DH,EADJ,CAAAC,cAAA,cAA2C,cACN;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACxDH,EAAA,CAAA4B,SAAA,gBAA+D;IAC/D5B,EAAA,CAAAiC,UAAA,IAAAQ,4CAAA,kBACwB;IAC5BzC,EAAA,CAAAG,YAAA,EAAM;;;;IAFIH,EAAA,CAAAI,SAAA,GAAoF;IAApFJ,EAAA,CAAAwB,UAAA,UAAAjB,MAAA,CAAA4B,WAAA,IAAA5B,MAAA,CAAA6B,YAAA,CAAAM,GAAA,CAAAJ,OAAA,MAAA/B,MAAA,CAAA6B,YAAA,CAAAM,GAAA,CAAAH,MAAA,kBAAAhC,MAAA,CAAA6B,YAAA,CAAAM,GAAA,CAAAH,MAAA,CAAAC,QAAA,EAAoF;;;AD/HtH,OAAM,MAAOG,sBAAsB;EAkBjCC,YACUC,QAAkB,EAClBC,cAA8B,EAC9BC,gBAAkC,EAClCC,cAA8B,EAC9BC,cAA8B,EAC9BC,SAAuB,EACvBC,MAAc;IANd,KAAAN,QAAQ,GAARA,QAAQ;IACR,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,MAAM,GAANA,MAAM;IAtBR,KAAAC,IAAI,GAAG,IAAIrD,OAAO,EAAE;IAC5B,KAAAsD,WAAW,GAAW,EAAE;IACjB,KAAA7C,cAAc,GAAc,EAAE;IAC9B,KAAA8C,kBAAkB,GAAW,CAAC;IAC9B,KAAAC,oBAAoB,GAAiB,EAAkB;IACvD,KAAAC,SAAS,GAAc,EAAe;IAC7C,KAAAC,gBAAgB,GAAmB,EAAE;IAC9B,KAAAC,IAAI,GAAqB,IAAIhE,gBAAgB,CAAC,EAAE,CAAC;IACjD,KAAAiE,gBAAgB,GAAqB,EAAsB;IAElE,KAAAC,eAAe,GAAsB,EAAE;IAChC,KAAAC,SAAS,GAAG,IAAI,CAACZ,cAAc,CAACY,SAAS;IAChD,KAAAC,YAAY,GAAoB,iEAAiE;IAqGjG,KAAAC,SAAS,GAAY,IAAI;IACzB,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAA7B,WAAW,GAAY,KAAK;EA5FxB;EAEJ8B,QAAQA,CAAA;IAEN,IAAI,CAACjB,cAAc,CAACkB,mBAAmB,EAAE,CAACC,IAAI,CAACvE,IAAI,CAAC,CAAC,CAAC,CAAC,CAACwE,SAAS,CAACC,GAAG,IAAG;MACtE,IAAI,CAACT,eAAe,GAAGS,GAAG;IAC5B,CAAC,CAAC;IACF,IAAI,CAAChB,WAAW,GAAG,IAAI,CAACP,cAAc,CAACwB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,cAAc,CAAE;IAC7E,IAAI,CAACpB,IAAI,CAACqB,IAAI,GAAG,IAAI,CAAC1B,gBAAgB,CAAC2B,YAAY,CAAC,IAAI,CAACrB,WAAW,CAAC,CAACe,SAAS,CAACC,GAAG,IAAG;MACpF,IAAI,CAACb,SAAS,GAAGa,GAAG;MACpB,IAAI,CAAC7D,cAAc,CAACmE,IAAI,CAACN,GAAG,CAACO,aAAc,CAAC;MAC5C,IAAI,CAACrB,oBAAoB,CAACsB,IAAI,GAAGR,GAAG,CAACO,aAAa,EAAEC,IAAK;IAC3D,CAAC,CAAC;IACF,IAAI,CAAC7B,cAAc,CAAC8B,gBAAgB,EAAE,CAACX,IAAI,CAACvE,IAAI,CAAC,CAAC,CAAC,CAAC,CAACwE,SAAS,CAACC,GAAG,IAAG;MACnE,IAAI,CAACZ,gBAAgB,GAAGY,GAAG;IAC7B,CAAC,CAAC;IACF,IAAI,CAACU,cAAc,EAAE;IACrB,IAAI,CAACjB,YAAY,GAAG,IAAI,CAACkB,SAAS,CAAC,EAAE,CAAC;EAExC;EAEAA,SAASA,CAACC,IAAY;IACpB,OAAO,IAAI,CAAC/B,SAAS,CAACgC,8BAA8B,CAAC,6DAA6D,GAAGD,IAAI,GAAG,UAAU,GAAG,IAAI,CAACrB,eAAe,CAAC,IAAI,CAACN,kBAAkB,CAAC,EAAE/B,KAAK,CAAC;EAChM;EAEAF,WAAWA,CAAC8D,IAAS,EAAEC,CAAS;IAC9B,IAAI,CAACC,kBAAkB,CAAC,YAAY,IAAI,CAACzB,eAAe,CAAC,IAAI,CAACN,kBAAkB,CAAC,CAAC/B,KAAK,EAAE,EAAE,kBAAkB,IAAI,CAACqC,eAAe,CAAC,IAAI,CAACN,kBAAkB,CAAC,CAAC/B,KAAK,EAAE,CAAC;IACnK,IAAI,CAAC8D,kBAAkB,CAAC,YAAY,IAAI,CAACzB,eAAe,CAAC,IAAI,CAACN,kBAAkB,CAAC,CAAC/B,KAAK,EAAE,EAAE,gBAAgB,IAAI,CAACqC,eAAe,CAAC,IAAI,CAACN,kBAAkB,CAAC,CAAC/B,KAAK,EAAE,CAAC;IACjK,IAAI,CAAC+B,kBAAkB,GAAG8B,CAAC;IAC3B,IAAI,CAACE,gBAAgB,CAAC,YAAY,IAAI,CAAC1B,eAAe,CAAC,IAAI,CAACN,kBAAkB,CAAC,CAAC/B,KAAK,EAAE,EAAE,kBAAkB,IAAI,CAACqC,eAAe,CAAC,IAAI,CAACN,kBAAkB,CAAC,CAAC/B,KAAK,EAAE,CAAC;IACjK,IAAI,CAAC+D,gBAAgB,CAAC,YAAY,IAAI,CAAC1B,eAAe,CAAC,IAAI,CAACN,kBAAkB,CAAC,CAAC/B,KAAK,EAAE,EAAE,gBAAgB,IAAI,CAACqC,eAAe,CAAC,IAAI,CAACN,kBAAkB,CAAC,CAAC/B,KAAK,EAAE,CAAC;EACjK;EAEA8D,kBAAkBA,CAACE,EAAU,EAAEC,OAAgB;IAC7C,IAAIC,GAAG,GAAGC,QAAQ,CAACC,cAAc,CAACJ,EAAE,CAAC;IACrCE,GAAI,CAACG,KAAK,CAACC,KAAK,GAAG,SAAS;IAC5BJ,GAAI,CAACG,KAAK,CAACE,eAAe,GAAG,OAAO;IACpCL,GAAI,CAACG,KAAK,CAACG,UAAU,GAAG,OAAO;IAC/B,IAAIP,OAAO,EACTE,QAAQ,CAACC,cAAc,CAACH,OAAO,CAAE,CAACI,KAAK,CAACC,KAAK,GAAG,SAAS;EAC7D;EAEAP,gBAAgBA,CAACC,EAAU,EAAEC,OAAgB;IAC3C,IAAIC,GAAG,GAAGC,QAAQ,CAACC,cAAc,CAACJ,EAAE,CAAC;IACrCE,GAAI,CAACG,KAAK,CAACE,eAAe,GAAG,IAAI,CAACE,cAAc,CAAC,IAAI,CAACzC,oBAAoB,CAACsB,IAAI,CAAC;IAChFY,GAAI,CAACG,KAAK,CAACC,KAAK,GAAG,OAAO;IAC1B,IAAIL,OAAO,EACTE,QAAQ,CAACC,cAAc,CAACH,OAAO,CAAE,CAACI,KAAK,CAACC,KAAK,GAAG,OAAO;EAC3D;EAEAG,cAAcA,CAACnB,IAAY;IACzB,IAAIA,IAAI,IAAIhF,WAAW,CAACoG,OAAO,EAAE;MAC/B,OAAO,SAAS;IAClB;IACA,IAAIpB,IAAI,IAAIhF,WAAW,CAACqG,IAAI,EAAE;MAC5B,OAAO,SAAS;IAClB;IACA,IAAIrB,IAAI,IAAIhF,WAAW,CAACsG,OAAO,EAAE;MAC/B,OAAO,SAAS;IAClB;IACA,OAAO,EAAE;EACX;EAEA1E,WAAWA,CAAC2E,eAAA,GAA2B,KAAK;IAC1C,OAAO;MACL,QAAQ,EAAE,aAAa,IAAI,CAACJ,cAAc,CAAC,IAAI,CAACzC,oBAAoB,CAACsB,IAAI,CAAC,EAAE;MAC5E,OAAO,EAAE,GAAG,IAAI,CAACmB,cAAc,CAAC,IAAI,CAACzC,oBAAoB,CAACsB,IAAI,CAAC;KAChE;EACH;EAEAwB,MAAMA,CAAA;IACJ,IAAI,CAACxD,QAAQ,CAACyD,IAAI,EAAE;EACtB;EAEAC,aAAaA,CAACC,WAAgB;IAC5B,IAAI,IAAI,CAACjD,oBAAoB,CAACsB,IAAI,IAAI2B,WAAW,CAAC3B,IAAI,EAAE;MACtD,OAAO;QACL,QAAQ,EAAE,aAAa,IAAI,CAACmB,cAAc,CAAC,IAAI,CAACzC,oBAAoB,CAACsB,IAAI,CAAC,EAAE;QAC5E,YAAY,EAAE,GAAG,IAAI,CAACmB,cAAc,CAAC,IAAI,CAACzC,oBAAoB,CAACsB,IAAI,CAAC;OACrE;IACH;IACA,OAAO;MACL,QAAQ,EAAE,mBAAmB;MAC7B,YAAY,EAAE;KACf;EACH;EAEA4B,sBAAsBA,CAACC,YAA0B;IAC/C,OAAOA,YAAY,CAACC,WAAW,CAAC,IAAI,CAACrD,kBAAkB,CAAC,CAAC/B,KAAK,GAAGmF,YAAY,CAACE,QAAQ;EACxF;EAKAC,aAAaA,CAAA;IACX,IAAI,CAAC9C,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;EACxB;EAEA8C,aAAaA,CAAA;IACX,IAAI,CAAC/C,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,SAAS,GAAG,IAAI;EACvB;EAEAe,cAAcA,CAAA;IACZ,IAAI,CAACrB,IAAI,GAAG,IAAIhE,gBAAgB,CAAC;MAC/BqH,KAAK,EAAE,IAAItH,kBAAkB,CAAC,IAAI,EAAE;QAClCuH,UAAU,EAAE,CAACrH,UAAU,CAAC6C,QAAQ;OACjC,CAAC;MACFyE,KAAK,EAAE,IAAIxH,kBAAkB,CAAC,IAAI,EAAE;QAClCuH,UAAU,EAAE,CAACrH,UAAU,CAAC6C,QAAQ;OACjC,CAAC;MACF0E,OAAO,EAAE,IAAIzH,kBAAkB,CAAC,IAAI,EAAE,EAAE,CAAC;MACzC0H,UAAU,EAAE,IAAI1H,kBAAkB,CAAC,IAAI,EAAE,EAAE,CAAC;MAC5C2H,OAAO,EAAE,IAAI3H,kBAAkB,CAAC,IAAI,EAAE;QACpCuH,UAAU,EAAE,CAACrH,UAAU,CAAC6C,QAAQ;OACjC,CAAC;MACF6E,MAAM,EAAE,IAAI5H,kBAAkB,CAAC,IAAI,EAAE;QACnCuH,UAAU,EAAE,CAACrH,UAAU,CAAC6C,QAAQ;OACjC,CAAC;MACF8E,MAAM,EAAE,IAAI7H,kBAAkB,CAAC,IAAI,EAAE;QACnCuH,UAAU,EAAE,CAACrH,UAAU,CAAC6C,QAAQ;OACjC,CAAC;MACF+E,IAAI,EAAE,IAAI9H,kBAAkB,CAAC,IAAI,EAAE;QACjCuH,UAAU,EAAE,CAACrH,UAAU,CAAC6C,QAAQ;OACjC,CAAC;MACFgF,QAAQ,EAAE,IAAI/H,kBAAkB,CAAC,IAAI,EAAE;QACrCuH,UAAU,EAAE,CAACrH,UAAU,CAAC6C,QAAQ;OACjC,CAAC;MACFH,GAAG,EAAE,IAAI5C,kBAAkB,CAAC,IAAI,EAAE,EACjC,CAAC;MACFgI,KAAK,EAAE,IAAIhI,kBAAkB,CAAC,IAAI,EAAE;QAClCuH,UAAU,EAAE,CAACrH,UAAU,CAAC6C,QAAQ;OACjC,CAAC;MACFkF,KAAK,EAAE,IAAIjI,kBAAkB,CAAC,IAAI,EAAE;QAClCuH,UAAU,EAAE,CAACrH,UAAU,CAAC6C,QAAQ;OACjC,CAAC;MACFE,GAAG,EAAE,IAAIjD,kBAAkB,CAAC,IAAI,EAAE,EACjC;KACF,CAAC;EACJ;EAEA,IAAI2C,YAAYA,CAAA;IACd,OAAO,IAAI,CAACsB,IAAI,CAACiE,QAAQ;EAC3B;EAGAC,QAAQA,CAAA;IACN,IAAI,CAACzF,WAAW,GAAG,IAAI;IACvB,IAAG,IAAI,CAACmB,kBAAkB,IAAI,CAAC,EAAC;MAC9B;IACF;IACA,IAAI,IAAI,CAACI,IAAI,CAACmE,KAAK,EAAE;MACnB,IAAIC,SAAS,GAAG,IAAI,CAACpE,IAAI,CAACqE,KAAK;MAC/B,IAAI,CAACpE,gBAAgB,CAACoD,KAAK,GAAGe,SAAS,CAACf,KAAK;MAC7C,IAAI,CAACpD,gBAAgB,CAACsD,KAAK,GAAGa,SAAS,CAACb,KAAK;MAC7C,IAAI,CAACtD,gBAAgB,CAAC8D,KAAK,GAAGK,SAAS,CAACL,KAAK;MAC7C,IAAI,CAAC9D,gBAAgB,CAAC4D,IAAI,GAAGO,SAAS,CAACP,IAAI;MAC3C,IAAI,CAAC5D,gBAAgB,CAACuD,OAAO,GAAGY,SAAS,CAACZ,OAAO;MACjD,IAAI,CAACvD,gBAAgB,CAACqE,UAAU,GAAGF,SAAS,CAACT,MAAM;MACnD,IAAI,CAAC1D,gBAAgB,CAACsE,YAAY,GAAGH,SAAS,CAACR,MAAM;MACrD,IAAI,CAAC3D,gBAAgB,CAACtB,GAAG,GAAGyF,SAAS,CAACzF,GAAG;MACzC,IAAI,CAACsB,gBAAgB,CAACwD,UAAU,GAAGW,SAAS,CAACX,UAAU;MACvD,IAAI,CAACxD,gBAAgB,CAAC6D,QAAQ,GAAGM,SAAS,CAACN,QAAQ;MACnD,IAAI,CAAC7D,gBAAgB,CAACjB,GAAG,GAAGoF,SAAS,CAACpF,GAAG;MACzC,IAAI,CAACiB,gBAAgB,CAAC+D,KAAK,GAAGI,SAAS,CAACJ,KAAK;MAC7C,IAAI,CAAC/D,gBAAgB,CAACyD,OAAO,GAAGU,SAAS,CAACV,OAAO,CAACpF,IAAI;IACxD,CAAC,MAAM;MACL;IACF;IACA,IAAIkG,GAAG,GAAG;MACRC,SAAS,EAAG,IAAI,CAAC3H,cAAc,CAAC,CAAC,CAAC,CAAC+E,EAAE;MACrC6C,YAAY,EAAG,IAAI,CAACxE,eAAe,CAAC,IAAI,CAACN,kBAAkB,CAAC,CAAC3B,UAAU;MACvEgC,gBAAgB,EAAE,IAAI,CAACA;KACxB;IACD,IAAI,CAACX,cAAc,CAACqF,YAAY,CAACH,GAAG,CAAC,CAAC/D,IAAI,CAACvE,IAAI,CAAC,CAAC,CAAC,CAAC,CAACwE,SAAS,CAAEC,GAAQ,IAAI;MACzE,IAAI,CAACP,YAAY,GAAG,IAAI,CAACkB,SAAS,CAACX,GAAG,CAACiE,MAAM,CAAC;MAC9C,IAAI,CAACrF,cAAc,CAACsF,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAACC,QAAQ,CAACC,aAAa,CAAC;IAC5E,CAAC,CAAC;EACJ;EAEAC,YAAYA,CAAA;IACV,IAAI,CAACzF,cAAc,CAACsF,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAACC,QAAQ,CAACC,aAAa,CAAC;IAC3E,IAAI,CAACtF,MAAM,CAACwF,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;EACrC;EAAC,QAAAC,CAAA,G;qBAlNUjG,sBAAsB,EAAA3C,EAAA,CAAA6I,iBAAA,CAAAC,EAAA,CAAAC,QAAA,GAAA/I,EAAA,CAAA6I,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAjJ,EAAA,CAAA6I,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAnJ,EAAA,CAAA6I,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAArJ,EAAA,CAAA6I,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAAvJ,EAAA,CAAA6I,iBAAA,CAAAW,EAAA,CAAAC,YAAA,GAAAzJ,EAAA,CAAA6I,iBAAA,CAAAG,EAAA,CAAAU,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAtBhH,sBAAsB;IAAAiH,SAAA;IAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;QCjBvB/J,EAHZ,CAAAC,cAAA,aAAuC,aACnB,aACoB,aAC2C;QAA9CD,EAAA,CAAAU,UAAA,mBAAAuJ,qDAAA;UAAAjK,EAAA,CAAAa,aAAA,CAAAqJ,GAAA;UAAA,OAAAlK,EAAA,CAAAoB,WAAA,CAAS4I,GAAA,CAAA3D,MAAA,EAAQ;QAAA,EAAC;QACvCrG,EAAA,CAAA4B,SAAA,aAA6C;QAC7C5B,EAAA,CAAAC,cAAA,aAA8B;QAAAD,EAAA,CAAAE,MAAA,WAAI;QACtCF,EADsC,CAAAG,YAAA,EAAM,EACtC;QACNH,EAAA,CAAAC,cAAA,aAAkD;QAC9CD,EAAA,CAAAE,MAAA,uBACJ;QACJF,EADI,CAAAG,YAAA,EAAM,EACJ;QAGEH,EAFR,CAAAC,cAAA,aAAqD,cAC5B,eAC8B;QAC3CD,EAAA,CAAAE,MAAA,wBACJ;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,eAA4C;QACxCD,EAAA,CAAA4B,SAAA,iCAA2E;QAGvE5B,EAFJ,CAAAC,cAAA,eAC8G,WACrG;QACDD,EAAA,CAAAE,MAAA,mBAAU;QAAAF,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAE,MAAA,IAAsB;QAC5CF,EAD4C,CAAAG,YAAA,EAAS,EAC/C;QACNH,EAAA,CAAAC,cAAA,WAAK;QACDD,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAE,MAAA,IAAyB;QAC5CF,EAD4C,CAAAG,YAAA,EAAS,EAC/C;QACNH,EAAA,CAAAiC,UAAA,KAAAkI,sCAAA,kBAA+B;QAK3CnK,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;QAEFH,EADJ,CAAAC,cAAA,cAAqB,eAC8B;QAC3CD,EAAA,CAAAE,MAAA,0BACJ;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAEFH,EADJ,CAAAC,cAAA,eAA4C,eACH;QACjCD,EAAA,CAAAiC,UAAA,KAAAmI,sCAAA,kBAC8F;QAU1GpK,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;QAEFH,EADJ,CAAAC,cAAA,eAAkB,eACmC;QAC7CD,EAAA,CAAAE,MAAA,6BACJ;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAGEH,EAFR,CAAAC,cAAA,gBAAyB,eACK,eACG;QACrBD,EAAA,CAAAE,MAAA,6BACJ;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAA4B,SAAA,eAMM;QAEF5B,EADJ,CAAAC,cAAA,eAAyB,eACY;QAAAD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACnDH,EAAA,CAAA4B,SAAA,iBAAiE;QACjE5B,EAAA,CAAAiC,UAAA,KAAAoI,sCAAA,kBACwB;QAC5BrK,EAAA,CAAAG,YAAA,EAAM;QAEFH,EADJ,CAAAC,cAAA,eAAyB,eACY;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAClDH,EAAA,CAAA4B,SAAA,iBAAiE;QACjE5B,EAAA,CAAAiC,UAAA,KAAAqI,sCAAA,kBACwB;QAC5BtK,EAAA,CAAAG,YAAA,EAAM;QAEFH,EADJ,CAAAC,cAAA,eAAyB,eACY;QAAAD,EAAA,CAAAE,MAAA,0BAAkB;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACzDH,EAAA,CAAA4B,SAAA,iBAAmE;QACvE5B,EAAA,CAAAG,YAAA,EAAM;QAEFH,EADJ,CAAAC,cAAA,eAAyB,eACY;QAAAD,EAAA,CAAAE,MAAA,6BAAqB;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAC5DH,EAAA,CAAA4B,SAAA,iBAAsE;QAC1E5B,EAAA,CAAAG,YAAA,EAAM;QAEFH,EADJ,CAAAC,cAAA,eAAyB,eACY;QAAAD,EAAA,CAAAE,MAAA,iBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAChDH,EAAA,CAAAC,cAAA,sBAE8B;QAC1BD,EAAA,CAAAiC,UAAA,KAAAsI,8CAAA,0BAA0C;QAM9CvK,EAAA,CAAAG,YAAA,EAAa;QACbH,EAAA,CAAAiC,UAAA,KAAAuI,sCAAA,kBACwB;QAC5BxK,EAAA,CAAAG,YAAA,EAAM;QAENH,EADA,CAAAiC,UAAA,KAAAwI,sCAAA,kBAA0C,KAAAC,sCAAA,kBACC;QAOvC1K,EADJ,CAAAC,cAAA,eAAyB,eACY;QAAAD,EAAA,CAAAE,MAAA,wBAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACvDH,EAAA,CAAA4B,SAAA,iBAAkE;QAClE5B,EAAA,CAAAiC,UAAA,KAAA0I,sCAAA,kBACwB;QAC5B3K,EAAA,CAAAG,YAAA,EAAM;QAEFH,EADJ,CAAAC,cAAA,eAAyB,eACY;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAClDH,EAAA,CAAA4B,SAAA,iBAAkE;QAClE5B,EAAA,CAAAiC,UAAA,KAAA2I,sCAAA,kBACwB;QAC5B5K,EAAA,CAAAG,YAAA,EAAM;QAEFH,EADJ,CAAAC,cAAA,eAAyB,eACY;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAC7CH,EAAA,CAAA4B,SAAA,iBAAgE;QAChE5B,EAAA,CAAAiC,UAAA,KAAA4I,sCAAA,kBACwB;QAC5B7K,EAAA,CAAAG,YAAA,EAAM;QAEFH,EADJ,CAAAC,cAAA,eAAyB,eACY;QAAAD,EAAA,CAAAE,MAAA,kBAAU;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACjDH,EAAA,CAAA4B,SAAA,iBAAoE;QACpE5B,EAAA,CAAAiC,UAAA,KAAA6I,sCAAA,kBACwB;QAC5B9K,EAAA,CAAAG,YAAA,EAAM;QAEFH,EADJ,CAAAC,cAAA,eAAyB,eACY;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAC9CH,EAAA,CAAA4B,SAAA,iBAAiE;QACjE5B,EAAA,CAAAiC,UAAA,KAAA8I,sCAAA,kBACwB;QAC5B/K,EAAA,CAAAG,YAAA,EAAM;QAEFH,EADJ,CAAAC,cAAA,eAAyB,eACY;QAAAD,EAAA,CAAAE,MAAA,eAAO;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAC9CH,EAAA,CAAA4B,SAAA,iBAAiE;QACjE5B,EAAA,CAAAiC,UAAA,KAAA+I,sCAAA,kBACwB;QAC5BhL,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAiC,UAAA,KAAAgJ,sCAAA,kBAA2C;QAOnDjL,EADI,CAAAG,YAAA,EAAM,EACH;QAEHH,EADJ,CAAAC,cAAA,eAAkB,eACsC;QAArBD,EAAA,CAAAU,UAAA,mBAAAwK,sDAAA;UAAAlL,EAAA,CAAAa,aAAA,CAAAqJ,GAAA;UAAA,OAAAlK,EAAA,CAAAoB,WAAA,CAAS4I,GAAA,CAAApC,QAAA,EAAU;QAAA,EAAC;QAC/C5H,EAAA,CAAAE,MAAA,kBACJ;QAKpBF,EALoB,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ,EACJ,EACJ;QAIEH,EAFR,CAAAC,cAAA,kBAA2C,gBACd,YAChB;QAAAD,EAAA,CAAAE,MAAA,gBAAO;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAClBH,EAAA,CAAAC,cAAA,gBAAqG;QAAhGD,EAAA,CAAAU,UAAA,mBAAAyK,uDAAA;UAAAnL,EAAA,CAAAa,aAAA,CAAAqJ,GAAA;UAAA,OAAAlK,EAAA,CAAAoB,WAAA,CAAS4I,GAAA,CAAAtB,YAAA,EAAc;QAAA,EAAC;QACjC1I,EADI,CAAAG,YAAA,EAAqG,EACnG;QACNH,EAAA,CAAA4B,SAAA,mBAAuK;QAC3K5B,EAAA,CAAAG,YAAA,EAAM;;;QAxKDH,EAAA,CAAAwB,UAAA,gBAAA4J,SAAA,CAAa;QAiByBpL,EAAA,CAAAI,SAAA,IAA2B;QAA3BJ,EAAA,CAAAwB,UAAA,aAAAwI,GAAA,CAAAxJ,cAAA,CAA2B;QAIxBR,EAAA,CAAAI,SAAA,GAAsB;QAAtBJ,EAAA,CAAAK,iBAAA,CAAA2J,GAAA,CAAAxG,SAAA,CAAA6H,QAAA,CAAsB;QAGzBrL,EAAA,CAAAI,SAAA,GAAyB;QAAzBJ,EAAA,CAAAK,iBAAA,CAAA2J,GAAA,CAAAxG,SAAA,CAAA8H,WAAA,CAAyB;QAEtCtL,EAAA,CAAAI,SAAA,EAAuB;QAAvBJ,EAAA,CAAAwB,UAAA,SAAAwI,GAAA,CAAAxJ,cAAA,IAAuB;QAYPR,EAAA,CAAAI,SAAA,GAAoB;QAApBJ,EAAA,CAAAwB,UAAA,YAAAwI,GAAA,CAAApG,eAAA,CAAoB;QAgB5C5D,EAAA,CAAAI,SAAA,GAAkB;QAAlBJ,EAAA,CAAAwB,UAAA,cAAAwI,GAAA,CAAAtG,IAAA,CAAkB;QAeN1D,EAAA,CAAAI,SAAA,GAAwF;QAAxFJ,EAAA,CAAAwB,UAAA,UAAAwI,GAAA,CAAA7H,WAAA,IAAA6H,GAAA,CAAA5H,YAAA,CAAA2E,KAAA,CAAAzE,OAAA,MAAA0H,GAAA,CAAA5H,YAAA,CAAA2E,KAAA,CAAAxE,MAAA,kBAAAyH,GAAA,CAAA5H,YAAA,CAAA2E,KAAA,CAAAxE,MAAA,CAAAC,QAAA,EAAwF;QAMxFxC,EAAA,CAAAI,SAAA,GAAwF;QAAxFJ,EAAA,CAAAwB,UAAA,UAAAwI,GAAA,CAAA7H,WAAA,IAAA6H,GAAA,CAAA5H,YAAA,CAAA6E,KAAA,CAAA3E,OAAA,MAAA0H,GAAA,CAAA5H,YAAA,CAAA6E,KAAA,CAAA1E,MAAA,kBAAAyH,GAAA,CAAA5H,YAAA,CAAA6E,KAAA,CAAA1E,MAAA,CAAAC,QAAA,EAAwF;QAatCxC,EAAA,CAAAI,SAAA,IAAqB;QACtCJ,EADiB,CAAAwB,UAAA,YAAAwI,GAAA,CAAAnG,SAAA,CAAqB,mBACpB;QASnD7D,EAAA,CAAAI,SAAA,GAA4F;QAA5FJ,EAAA,CAAAwB,UAAA,UAAAwI,GAAA,CAAA7H,WAAA,IAAA6H,GAAA,CAAA5H,YAAA,CAAAgF,OAAA,CAAA9E,OAAA,MAAA0H,GAAA,CAAA5H,YAAA,CAAAgF,OAAA,CAAA7E,MAAA,kBAAAyH,GAAA,CAAA5H,YAAA,CAAAgF,OAAA,CAAA7E,MAAA,CAAAC,QAAA,EAA4F;QAGhGxC,EAAA,CAAAI,SAAA,EAAe;QAAfJ,EAAA,CAAAwB,UAAA,SAAAwI,GAAA,CAAAjG,SAAA,CAAe;QACf/D,EAAA,CAAAI,SAAA,EAAe;QAAfJ,EAAA,CAAAwB,UAAA,SAAAwI,GAAA,CAAAhG,SAAA,CAAe;QASXhE,EAAA,CAAAI,SAAA,GAA0F;QAA1FJ,EAAA,CAAAwB,UAAA,UAAAwI,GAAA,CAAA7H,WAAA,IAAA6H,GAAA,CAAA5H,YAAA,CAAAiF,MAAA,CAAA/E,OAAA,MAAA0H,GAAA,CAAA5H,YAAA,CAAAiF,MAAA,CAAA9E,MAAA,kBAAAyH,GAAA,CAAA5H,YAAA,CAAAiF,MAAA,CAAA9E,MAAA,CAAAC,QAAA,EAA0F;QAM1FxC,EAAA,CAAAI,SAAA,GAA0F;QAA1FJ,EAAA,CAAAwB,UAAA,UAAAwI,GAAA,CAAA7H,WAAA,IAAA6H,GAAA,CAAA5H,YAAA,CAAAkF,MAAA,CAAAhF,OAAA,MAAA0H,GAAA,CAAA5H,YAAA,CAAAkF,MAAA,CAAA/E,MAAA,kBAAAyH,GAAA,CAAA5H,YAAA,CAAAkF,MAAA,CAAA/E,MAAA,CAAAC,QAAA,EAA0F;QAM1FxC,EAAA,CAAAI,SAAA,GAAsF;QAAtFJ,EAAA,CAAAwB,UAAA,UAAAwI,GAAA,CAAA7H,WAAA,IAAA6H,GAAA,CAAA5H,YAAA,CAAAmF,IAAA,CAAAjF,OAAA,MAAA0H,GAAA,CAAA5H,YAAA,CAAAmF,IAAA,CAAAhF,MAAA,kBAAAyH,GAAA,CAAA5H,YAAA,CAAAmF,IAAA,CAAAhF,MAAA,CAAAC,QAAA,EAAsF;QAMtFxC,EAAA,CAAAI,SAAA,GAA8F;QAA9FJ,EAAA,CAAAwB,UAAA,UAAAwI,GAAA,CAAA7H,WAAA,IAAA6H,GAAA,CAAA5H,YAAA,CAAAoF,QAAA,CAAAlF,OAAA,MAAA0H,GAAA,CAAA5H,YAAA,CAAAoF,QAAA,CAAAjF,MAAA,kBAAAyH,GAAA,CAAA5H,YAAA,CAAAoF,QAAA,CAAAjF,MAAA,CAAAC,QAAA,EAA8F;QAM9FxC,EAAA,CAAAI,SAAA,GAAwF;QAAxFJ,EAAA,CAAAwB,UAAA,UAAAwI,GAAA,CAAA7H,WAAA,IAAA6H,GAAA,CAAA5H,YAAA,CAAAqF,KAAA,CAAAnF,OAAA,MAAA0H,GAAA,CAAA5H,YAAA,CAAAqF,KAAA,CAAAlF,MAAA,kBAAAyH,GAAA,CAAA5H,YAAA,CAAAqF,KAAA,CAAAlF,MAAA,CAAAC,QAAA,EAAwF;QAMxFxC,EAAA,CAAAI,SAAA,GAAwF;QAAxFJ,EAAA,CAAAwB,UAAA,UAAAwI,GAAA,CAAA7H,WAAA,IAAA6H,GAAA,CAAA5H,YAAA,CAAAsF,KAAA,CAAApF,OAAA,MAAA0H,GAAA,CAAA5H,YAAA,CAAAsF,KAAA,CAAAnF,MAAA,kBAAAyH,GAAA,CAAA5H,YAAA,CAAAsF,KAAA,CAAAnF,MAAA,CAAAC,QAAA,EAAwF;QAG5FxC,EAAA,CAAAI,SAAA,EAAe;QAAfJ,EAAA,CAAAwB,UAAA,SAAAwI,GAAA,CAAAhG,SAAA,CAAe;QAuBwChE,EAAA,CAAAI,SAAA,IAAoB;QAApBJ,EAAA,CAAAwB,UAAA,QAAAwI,GAAA,CAAAlG,YAAA,EAAA9D,EAAA,CAAAuL,qBAAA,CAAoB;;;;;;iBDrJ3F,CAACzL,UAAU;IAAC;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}