{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nexport class ClickOutsideDirective {\n  constructor(elementRef) {\n    this.elementRef = elementRef;\n    this.clickOutside = new EventEmitter();\n  }\n  onClick(target) {\n    const clickedInside = this.elementRef.nativeElement.contains(target);\n    if (!clickedInside) {\n      this.clickOutside.emit();\n    }\n  }\n  static #_ = this.ɵfac = function ClickOutsideDirective_Factory(t) {\n    return new (t || ClickOutsideDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static #_2 = this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n    type: ClickOutsideDirective,\n    selectors: [[\"\", \"clickOutside\", \"\"]],\n    hostBindings: function ClickOutsideDirective_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function ClickOutsideDirective_click_HostBindingHandler($event) {\n          return ctx.onClick($event.target);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    outputs: {\n      clickOutside: \"clickOutside\"\n    }\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "ClickOutsideDirective", "constructor", "elementRef", "clickOutside", "onClick", "target", "clickedInside", "nativeElement", "contains", "emit", "_", "i0", "ɵɵdirectiveInject", "ElementRef", "_2", "selectors", "hostBindings", "ClickOutsideDirective_HostBindings", "rf", "ctx", "ɵɵlistener", "ClickOutsideDirective_click_HostBindingHandler", "$event", "ɵɵresolveDocument"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\directives\\click-outside.directive.ts"], "sourcesContent": ["import { Directive, Input, Output, EventEmitter, ElementRef, HostListener } from '@angular/core';\r\n\r\n@Directive({\r\n    selector: '[clickOutside]',\r\n})\r\nexport class ClickOutsideDirective {\r\n\r\n    @Output() clickOutside = new EventEmitter<void>();\r\n\r\n    constructor(private elementRef: ElementRef) { }\r\n\r\n    @HostListener('document:click', ['$event.target'])\r\n    public onClick(target: any) {\r\n        const clickedInside = this.elementRef.nativeElement.contains(target);\r\n        if (!clickedInside) {\r\n            this.clickOutside.emit();\r\n        }\r\n    }\r\n}"], "mappings": "AAAA,SAAmCA,YAAY,QAAkC,eAAe;;AAKhG,OAAM,MAAOC,qBAAqB;EAI9BC,YAAoBC,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;IAFpB,KAAAC,YAAY,GAAG,IAAIJ,YAAY,EAAQ;EAEH;EAGvCK,OAAOA,CAACC,MAAW;IACtB,MAAMC,aAAa,GAAG,IAAI,CAACJ,UAAU,CAACK,aAAa,CAACC,QAAQ,CAACH,MAAM,CAAC;IACpE,IAAI,CAACC,aAAa,EAAE;MAChB,IAAI,CAACH,YAAY,CAACM,IAAI,EAAE;IAC5B;EACJ;EAAC,QAAAC,CAAA,G;qBAZQV,qBAAqB,EAAAW,EAAA,CAAAC,iBAAA,CAAAD,EAAA,CAAAE,UAAA;EAAA;EAAA,QAAAC,EAAA,G;UAArBd,qBAAqB;IAAAe,SAAA;IAAAC,YAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAArBP,EAAA,CAAAS,UAAA,mBAAAC,+CAAAC,MAAA;UAAA,OAAAH,GAAA,CAAAf,OAAA,CAAAkB,MAAA,CAAAjB,MAAA,CAAsB;QAAA,UAAAM,EAAA,CAAAY,iBAAA,CAAD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}