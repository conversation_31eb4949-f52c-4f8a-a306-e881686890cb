{"ast": null, "code": "import { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/auth.service\";\nimport * as i2 from \"src/app/core/services/package.service\";\nimport * as i3 from \"../packages/packages.component\";\nexport let TeacherBillingComponent = /*#__PURE__*/(() => {\n  class TeacherBillingComponent {\n    constructor(authService, packageService) {\n      this.authService = authService;\n      this.packageService = packageService;\n      this.classroom = {};\n      this.subs = new SubSink();\n      this.activePackages = [];\n    }\n    ngOnInit() {}\n    ngAfterViewInit() {\n      // this.packageService.paintPackage();\n      this.activePackages.push(this.classroom.activePackage);\n      console.log(this.activePackages);\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n    }\n    getPackages() {}\n    static #_ = this.ɵfac = function TeacherBillingComponent_Factory(t) {\n      return new (t || TeacherBillingComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.PackageService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TeacherBillingComponent,\n      selectors: [[\"app-teacher-billing\"]],\n      inputs: {\n        classroom: \"classroom\"\n      },\n      decls: 2,\n      vars: 1,\n      consts: [[3, \"classroom\"]],\n      template: function TeacherBillingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"div\")(1, \"app-packages\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"classroom\", ctx.classroom);\n        }\n      },\n      dependencies: [i3.PackagesComponent]\n    });\n  }\n  return TeacherBillingComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}