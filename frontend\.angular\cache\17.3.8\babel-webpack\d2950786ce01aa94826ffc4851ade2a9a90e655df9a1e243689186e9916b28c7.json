{"ast": null, "code": "import * as i0 from \"@angular/core\";\nconst _c0 = [\"loaderEl\"];\nexport let LoaderComponent = /*#__PURE__*/(() => {\n  class LoaderComponent {\n    constructor() {\n      this.size = '320';\n      this.scale = '1';\n      this.loaderEl = {};\n    }\n    ngOnInit() {\n      this.addLottiePlayerToStep(this.loaderEl);\n    }\n    ngAfterViewInit() {}\n    addLottiePlayerToStep(el) {\n      const lottiePlayer = `<div  class=\"animation-wrapper\"><lottie-player src=\"/assets/js/lottie-json/loader.json\" background=\"transparent\" \n    speed=\"1\" style=\"width: ` + this.size + `px; height: ` + this.size + `px; transform: scale(` + this.scale + `);\" autoplay loop></lottie-player></div>`;\n      el.nativeElement.innerHTML = lottiePlayer;\n    }\n    static #_ = this.ɵfac = function LoaderComponent_Factory(t) {\n      return new (t || LoaderComponent)();\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoaderComponent,\n      selectors: [[\"app-loader\"]],\n      viewQuery: function LoaderComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.loaderEl = _t.first);\n        }\n      },\n      inputs: {\n        size: \"size\",\n        scale: \"scale\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 0,\n      consts: [[\"loaderEl\", \"\"], [1, \"loader-wrapper\"]],\n      template: function LoaderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"div\", 1, 0);\n        }\n      },\n      changeDetection: 0\n    });\n  }\n  return LoaderComponent;\n})();", "map": {"version": 3, "names": ["LoaderComponent", "constructor", "size", "scale", "loaderEl", "ngOnInit", "addLottiePlayerToStep", "ngAfterViewInit", "el", "lottiePlayer", "nativeElement", "innerHTML", "_", "_2", "selectors", "viewQuery", "LoaderComponent_Query", "rf", "ctx", "i0", "ɵɵelement"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\loader\\loader.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\loader\\loader.component.html"], "sourcesContent": ["import { AfterViewInit, ChangeDetectionStrategy, Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';\r\n\r\n@Component({\r\n  selector: 'app-loader',\r\n  templateUrl: './loader.component.html',\r\n  styleUrls: ['./loader.component.scss'],\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n  standalone: true\r\n})\r\nexport class LoaderComponent implements OnInit, AfterViewInit {\r\n  @Input() size = '320';\r\n  @Input() scale = '1';\r\n  @ViewChild('loaderEl', { static: true }) loaderEl: ElementRef = {} as ElementRef;\r\n  constructor() { }\r\n\r\n  ngOnInit(): void {\r\n    this.addLottiePlayerToStep(this.loaderEl);\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n  }\r\n\r\n  private addLottiePlayerToStep(el: ElementRef): void {\r\n    const lottiePlayer = `<div  class=\"animation-wrapper\"><lottie-player src=\"/assets/js/lottie-json/loader.json\" background=\"transparent\" \r\n    speed=\"1\" style=\"width: `+ this.size + `px; height: ` + this.size + `px; transform: scale(` + this.scale + `);\" autoplay loop></lottie-player></div>`;\r\n    el.nativeElement.innerHTML = lottiePlayer;\r\n  }\r\n\r\n}\r\n", "\r\n<div class=\"loader-wrapper\" #loaderEl></div>"], "mappings": ";;AASA,WAAaA,eAAe;EAAtB,MAAOA,eAAe;IAI1BC,YAAA;MAHS,KAAAC,IAAI,GAAG,KAAK;MACZ,KAAAC,KAAK,GAAG,GAAG;MACqB,KAAAC,QAAQ,GAAe,EAAgB;IAChE;IAEhBC,QAAQA,CAAA;MACN,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAACF,QAAQ,CAAC;IAC3C;IAEAG,eAAeA,CAAA,GACf;IAEQD,qBAAqBA,CAACE,EAAc;MAC1C,MAAMC,YAAY,GAAG;6BACI,GAAE,IAAI,CAACP,IAAI,GAAG,cAAc,GAAG,IAAI,CAACA,IAAI,GAAG,uBAAuB,GAAG,IAAI,CAACC,KAAK,GAAG,0CAA0C;MACrJK,EAAE,CAACE,aAAa,CAACC,SAAS,GAAGF,YAAY;IAC3C;IAAC,QAAAG,CAAA,G;uBAjBUZ,eAAe;IAAA;IAAA,QAAAa,EAAA,G;YAAfb,eAAe;MAAAc,SAAA;MAAAC,SAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;UCR5BE,EAAA,CAAAC,SAAA,gBAA4C;;;;;;SDQ/BpB,eAAe;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}