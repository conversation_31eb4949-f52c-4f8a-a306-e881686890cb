{"ast": null, "code": "import { switchMap } from 'rxjs/operators';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/notification.service\";\nimport * as i2 from \"src/app/core/services/general.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/router\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"../../../../shared/layout/g-header-back-button/g-header-back-button.component\";\nimport * as i7 from \"../../../../shared/notification/notification-row-item/notification-row-item.component\";\nimport * as i8 from \"primeng/inputtext\";\nimport * as i9 from \"primeng/calendar\";\nconst _c0 = a0 => ({\n  \"text-primary active font-bold\": a0\n});\nfunction NotificationOverviewComponent_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function NotificationOverviewComponent_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleFilterVisibility());\n    });\n    i0.ɵɵelement(1, \"span\", 27);\n    i0.ɵɵelementStart(2, \"span\", 28);\n    i0.ɵɵtext(3, \"Filters\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"img\", 29);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotificationOverviewComponent_ng_container_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 30)(2, \"span\", 31);\n    i0.ɵɵlistener(\"click\", function NotificationOverviewComponent_ng_container_20_Template_span_click_2_listener() {\n      const filterType_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.applyFilter(filterType_r4.type));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const filterType_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"hidden\", ctx_r1.showFiltersTablet);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r1.currentFilterType === filterType_r4.type));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", filterType_r4.name, \"\");\n  }\n}\nfunction NotificationOverviewComponent_ng_container_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 32);\n    i0.ɵɵelement(2, \"img\", 33);\n    i0.ɵɵelementStart(3, \"div\", 34)(4, \"span\", 35);\n    i0.ɵɵtext(5, \"You don\\u2019t have any notifications yet\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction NotificationOverviewComponent_ng_container_33_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-notification-row-item\", 36);\n    i0.ɵɵlistener(\"removeNotification\", function NotificationOverviewComponent_ng_container_33_Template_app_notification_row_item_removeNotification_1_listener() {\n      const notif_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.removeNotificationSelected(notif_r6.id));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const notif_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"notification\", notif_r6)(\"slimStyle\", false);\n  }\n}\nexport class NotificationOverviewComponent {\n  constructor(notificationService, generalService) {\n    this.notificationService = notificationService;\n    this.generalService = generalService;\n    this.filterTypes = [{\n      name: 'All',\n      type: ''\n    }, {\n      name: 'Lessons',\n      type: 'Lessons'\n    }, {\n      name: 'Packages',\n      type: 'Packages'\n    }, {\n      name: 'Availability',\n      type: 'DaysOff'\n    }, {\n      name: 'Trials',\n      type: 'Trials'\n    }, {\n      name: 'Student Actions',\n      type: 'Student'\n    }, {\n      name: 'Homework',\n      type: 'Homework'\n    }];\n    this.fromDate = '';\n    this.toDate = '';\n    this.currentDate = new Date();\n    this.notifications = [];\n    this.searchValue = '';\n    this.currentFilterType = '';\n    this.filteredNotifications = [];\n    this.isTablet = false;\n    this.showFiltersTablet = false;\n    this.unreadNotifications = [];\n    this.isLoadingNotifications = true;\n    this.subs = new SubSink();\n  }\n  ngOnInit() {\n    this.subs.sink = this.notificationService.get().pipe(switchMap(notifications => {\n      this.notifications = notifications;\n      this.filteredNotifications = this.notifications.reverse();\n      // Filter notifications to get only the unread ones\n      this.unreadNotifications = this.notifications.filter(notification => !notification.isRead);\n      // Get an array of IDs for the unread notifications\n      const unreadNotificationIds = this.unreadNotifications.map(notification => notification.id);\n      // Use the IDs of the unread notifications to mark them as read\n      return this.notificationService.markAsReadBulk(unreadNotificationIds);\n    })).subscribe(() => {\n      this.isLoadingNotifications = false;\n    }, error => {\n      this.isLoadingNotifications = false;\n    });\n    this.subs.sink = this.generalService.deviceKind.subscribe(res => {\n      this.isTablet = res.is1024;\n      console.log(this.isTablet);\n      this.showFiltersTablet = this.isTablet;\n    });\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  applyFilter(filterType) {\n    console.log(`Filter type: ${filterType}`);\n    // Update the current filter type\n    this.currentFilterType = filterType;\n    // Filter the notifications based on the selected filter type and search value\n    this.filteredNotifications = this.notifications.filter(notification => {\n      let dateCheck = true;\n      // If a fromDate and toDate were selected, check if the notification's creation date is in range\n      if (this.fromDate && this.toDate) {\n        // Add 1 to the from and to dates to ensure that notifications created on the exact date are included\n        const dateFrom = new Date(this.fromDate).setDate(new Date(this.fromDate).getDate() + 1);\n        const dateTo = new Date(this.toDate).setDate(new Date(this.toDate).getDate() + 1);\n        // Get the creation date of the notification\n        const createdAt = new Date(this.generalService.convertWithoutTimeAndZ(notification.createdAt));\n        // Check if the creation date falls within the selected range\n        dateCheck = createdAt >= new Date(dateFrom) && createdAt <= new Date(dateTo);\n      }\n      // Check if the notification's description includes the search value (case-insensitive)\n      let searchCheck = this.searchValue === '' || notification.description.toLowerCase().includes(this.searchValue.toLowerCase());\n      // Check if the notification's type matches the selected filter type\n      let filterCategoryCheck = !filterType || notification.type === this.currentFilterType;\n      // Return the notification if it passes all three checks (date, search, and filter type)\n      return dateCheck && searchCheck && filterCategoryCheck;\n    });\n  }\n  onSearchInput(event) {\n    this.searchValue = event.target.value;\n    this.applyFilter(this.currentFilterType);\n  }\n  onFromDateSelection(event) {\n    this.fromDate = this.generalService.convertToIsoDate(event);\n    this.applyFilter('');\n  }\n  onToDateSelection(event) {\n    this.toDate = this.generalService.convertToIsoDate(event);\n    this.applyFilter('');\n  }\n  removeNotificationSelected(notificationId) {\n    this.subs.sink = this.notificationService.delete(notificationId).subscribe(res => {\n      if (res) {\n        this.filteredNotifications = this.filteredNotifications.filter(item => item.id !== notificationId);\n      }\n    });\n  }\n  onCalendarClose(event) {\n    console.log(event);\n  }\n  toggleFilterVisibility() {\n    this.showFiltersTablet = !this.showFiltersTablet;\n  }\n  static #_ = this.ɵfac = function NotificationOverviewComponent_Factory(t) {\n    return new (t || NotificationOverviewComponent)(i0.ɵɵdirectiveInject(i1.NotificationService), i0.ɵɵdirectiveInject(i2.GeneralService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NotificationOverviewComponent,\n    selectors: [[\"app-notification-overview\"]],\n    decls: 34,\n    vars: 10,\n    consts: [[\"id\", \"notifications-overview\", 1, \"notifications-overview\"], [1, \"surface-section\"], [1, \"block-header\", \"relative\", \"justify-content-center\", \"lg:flex\"], [1, \"absolute\", \"pl-1\", \"md:pl-2\", \"left-0\"], [\"routerLink\", \"/dashboard\", \"label\", \"Back to Dashboard\"], [1, \"block-title\"], [1, \"text-0\"], [1, \"surface-section\", \"px-2\", \"py-2\", \"md:px-26\", \"lg:px-2\", \"text-center\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"\", \"class\", \"p-button-text\", 3, \"click\", 4, \"ngIf\"], [1, \"grid\", \"w-full\"], [1, \"col-12\", \"lg:col-3\", \"lg:pr-5\"], [3, \"hidden\"], [1, \"p-input-icon-right\", \"w-full\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Search\", 1, \"w-full\", \"input-blue\", \"rounded\", 3, \"input\"], [1, \"pi\", \"pi-search\", \"text-primary\"], [1, \"lg:px-3\", \"lg:pt-4\", 3, \"hidden\"], [1, \"font-bold\"], [1, \"list-none\", \"p-0\", \"m-0\", \"flex-grow-1\"], [4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"align-items-start\", \"mb-3\", \"text-base\", \"flex-column\"], [1, \"filter-link\", \"cursor-auto\"], [\"placeholder\", \"DD/MM/YY\", \"dateFormat\", \"dd/mm/yy\", 3, \"onClose\", \"onSelect\", \"maxDate\", \"showIcon\"], [\"placeholder\", \"DD/MM/YY\", \"dateFormat\", \"dd/mm/yy\", 3, \"onSelect\", \"maxDate\", \"showIcon\"], [1, \"col-12\", \"lg:col-9\"], [1, \"list-none\", \"p-0\", \"m-0\", \"max-h-30rem\", \"overflow-y-auto\"], [4, \"ngIf\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"\", 1, \"p-button-text\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"p-button-icon\", \"p-button-icon-right\"], [1, \"p-button-label\", \"mr-2\"], [\"src\", \"/assets/icons/notifications/notif-filter.svg\", 1, \"contact-icon-img\"], [1, \"flex\", \"align-items-center\", \"mb-3\", \"text-base\", 3, \"hidden\"], [1, \"filter-link\", 3, \"click\", \"ngClass\"], [1, \"surface-section\", \"px-4\", \"py-8\", \"md:px-6\", \"lg:px-8\", \"text-center\"], [\"src\", \"/assets/icons/notifications/bell.png\", \"alt\", \"bell\", \"width\", \"150\"], [1, \"mt-3\", \"font-bold\", \"text-xl\"], [1, \"text-900\"], [3, \"removeNotification\", \"notification\", \"slimStyle\"]],\n    template: function NotificationOverviewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n        i0.ɵɵelement(4, \"g-header-back-button\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"span\", 5)(6, \"span\", 6);\n        i0.ɵɵtext(7, \"Notifications\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"div\", 7);\n        i0.ɵɵtemplate(9, NotificationOverviewComponent_button_9_Template, 5, 0, \"button\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"div\", 9)(11, \"div\", 10)(12, \"div\", 11)(13, \"span\", 12)(14, \"input\", 13);\n        i0.ɵɵlistener(\"input\", function NotificationOverviewComponent_Template_input_input_14_listener($event) {\n          return ctx.onSearchInput($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(15, \"i\", 14);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"div\", 15)(17, \"h6\", 16);\n        i0.ɵɵtext(18, \"Filter by category\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"ul\", 17);\n        i0.ɵɵtemplate(20, NotificationOverviewComponent_ng_container_20_Template, 4, 5, \"ng-container\", 18);\n        i0.ɵɵelementStart(21, \"li\", 19)(22, \"span\", 20);\n        i0.ɵɵtext(23, \"Extras\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"span\", 20);\n        i0.ɵɵtext(25, \"From:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(26, \"p-calendar\", 21);\n        i0.ɵɵlistener(\"onClose\", function NotificationOverviewComponent_Template_p_calendar_onClose_26_listener($event) {\n          return ctx.onCalendarClose($event);\n        })(\"onSelect\", function NotificationOverviewComponent_Template_p_calendar_onSelect_26_listener($event) {\n          return ctx.onFromDateSelection($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(27, \"span\", 20);\n        i0.ɵɵtext(28, \"To:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(29, \"p-calendar\", 22);\n        i0.ɵɵlistener(\"onSelect\", function NotificationOverviewComponent_Template_p_calendar_onSelect_29_listener($event) {\n          return ctx.onToDateSelection($event);\n        });\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(30, \"div\", 23)(31, \"ul\", 24);\n        i0.ɵɵtemplate(32, NotificationOverviewComponent_ng_container_32_Template, 6, 0, \"ng-container\", 25)(33, NotificationOverviewComponent_ng_container_33_Template, 2, 2, \"ng-container\", 18);\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngIf\", ctx.isTablet);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"hidden\", ctx.showFiltersTablet);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"hidden\", ctx.showFiltersTablet);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngForOf\", ctx.filterTypes);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"maxDate\", ctx.currentDate)(\"showIcon\", true);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"maxDate\", ctx.currentDate)(\"showIcon\", true);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.filteredNotifications.length === 0 && !ctx.isLoadingNotifications);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngForOf\", ctx.filteredNotifications);\n      }\n    },\n    dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i4.RouterLink, i5.ButtonDirective, i6.GHeaderBackButtonComponent, i7.NotificationRowItemComponent, i8.InputText, i9.Calendar],\n    styles: [\".notifications-overview[_ngcontent-%COMP%] {\\n  border-radius: 10px;\\n  background-color: var(--white);\\n}\\n.notifications-overview[_ngcontent-%COMP%]     .p-calendar.p-calendar-w-btn {\\n  border: none;\\n  flex-direction: row-reverse;\\n}\\n.notifications-overview[_ngcontent-%COMP%]     .p-calendar.p-calendar-w-btn .p-inputtext {\\n  padding: 0.5rem 0.75rem;\\n}\\n.notifications-overview[_ngcontent-%COMP%]     .p-calendar.p-calendar-w-btn .p-inputtext::placeholder {\\n  \\n\\n  color: var(--primary-color);\\n}\\n.notifications-overview[_ngcontent-%COMP%]     .p-calendar.p-calendar-w-btn .p-inputtext::-ms-input-placeholder {\\n  \\n\\n  color: var(--primary-color);\\n}\\n.notifications-overview[_ngcontent-%COMP%]     .p-calendar.p-calendar-w-btn .p-datepicker-trigger {\\n  padding: 0;\\n  justify-content: flex-start;\\n  width: 1rem;\\n}\\n.notifications-overview[_ngcontent-%COMP%]   .block-header[_ngcontent-%COMP%] {\\n  background-image: url(\\\"/assets/images/notification-overview-header.png\\\");\\n  background-size: cover;\\n  background-repeat: no-repeat;\\n  background-position: center;\\n  border-radius: 10px;\\n}\\n.notifications-overview[_ngcontent-%COMP%]   .filter-link[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  opacity: 0.5;\\n}\\n.notifications-overview[_ngcontent-%COMP%]   .filter-link.active[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n.notifications-overview[_ngcontent-%COMP%]   .filter-link[_ngcontent-%COMP%]:hover {\\n  opacity: 0.8;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["switchMap", "SubSink", "i0", "ɵɵelementStart", "ɵɵlistener", "NotificationOverviewComponent_button_9_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "toggleFilterVisibility", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵelementContainerStart", "NotificationOverviewComponent_ng_container_20_Template_span_click_2_listener", "filterType_r4", "_r3", "$implicit", "applyFilter", "type", "ɵɵadvance", "ɵɵproperty", "showFiltersTablet", "ɵɵpureFunction1", "_c0", "currentFilterType", "ɵɵtextInterpolate1", "name", "NotificationOverviewComponent_ng_container_33_Template_app_notification_row_item_removeNotification_1_listener", "notif_r6", "_r5", "removeNotificationSelected", "id", "NotificationOverviewComponent", "constructor", "notificationService", "generalService", "filterTypes", "fromDate", "toDate", "currentDate", "Date", "notifications", "searchValue", "filteredNotifications", "isTablet", "unreadNotifications", "isLoadingNotifications", "subs", "ngOnInit", "sink", "get", "pipe", "reverse", "filter", "notification", "isRead", "unreadNotificationIds", "map", "markAsReadBulk", "subscribe", "error", "deviceKind", "res", "is1024", "console", "log", "ngOnDestroy", "unsubscribe", "filterType", "date<PERSON><PERSON><PERSON>", "dateFrom", "setDate", "getDate", "dateTo", "createdAt", "convertWithoutTimeAndZ", "searchCheck", "description", "toLowerCase", "includes", "filterCategoryCheck", "onSearchInput", "event", "target", "value", "onFromDateSelection", "convertToIsoDate", "onToDateSelection", "notificationId", "delete", "item", "onCalendarClose", "_", "ɵɵdirectiveInject", "i1", "NotificationService", "i2", "GeneralService", "_2", "selectors", "decls", "vars", "consts", "template", "NotificationOverviewComponent_Template", "rf", "ctx", "ɵɵtemplate", "NotificationOverviewComponent_button_9_Template", "NotificationOverviewComponent_Template_input_input_14_listener", "$event", "NotificationOverviewComponent_ng_container_20_Template", "NotificationOverviewComponent_Template_p_calendar_onClose_26_listener", "NotificationOverviewComponent_Template_p_calendar_onSelect_26_listener", "NotificationOverviewComponent_Template_p_calendar_onSelect_29_listener", "NotificationOverviewComponent_ng_container_32_Template", "NotificationOverviewComponent_ng_container_33_Template", "length"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\notifications\\components\\notification-overview\\notification-overview.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\notifications\\components\\notification-overview\\notification-overview.component.html"], "sourcesContent": ["import { Component, OnDestroy, OnInit } from '@angular/core';\r\nimport { switchMap } from 'rxjs/operators';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { NotificationService } from 'src/app/core/services/notification.service';\r\nimport { SubSink } from 'subsink';\r\n\r\n@Component({\r\n  selector: 'app-notification-overview',\r\n  templateUrl: './notification-overview.component.html',\r\n  styleUrls: ['./notification-overview.component.scss']\r\n})\r\nexport class NotificationOverviewComponent implements OnInit, OnDestroy {\r\n\r\n  filterTypes = [\r\n    {\r\n      name: 'All',\r\n      type: '',\r\n    },\r\n    {\r\n      name: 'Lessons',\r\n      type: 'Lessons',\r\n    },\r\n    {\r\n      name: 'Packages',\r\n      type: 'Packages',\r\n    },\r\n    {\r\n      name: 'Availability',\r\n      type: 'DaysOff',\r\n    },\r\n    {\r\n      name: 'Trials',\r\n      type: 'Trials',\r\n    },\r\n    {\r\n      name: 'Student Actions',\r\n      type: 'Student',\r\n    },\r\n    {\r\n      name: 'Homework',\r\n      type: 'Homework',\r\n    },\r\n  ];\r\n  fromDate = '';\r\n  toDate = '';\r\n  currentDate = new Date();\r\n  notifications: any[] = [];\r\n  searchValue: string = '';\r\n  currentFilterType: string = '';\r\n  filteredNotifications: any[] = [];\r\n  isTablet = false;\r\n  showFiltersTablet = false;\r\n  unreadNotifications: any[] = [];\r\n  isLoadingNotifications = true;\r\n  private subs = new SubSink();\r\n  constructor(\r\n    private notificationService: NotificationService,\r\n    private generalService: GeneralService,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n\r\n    this.subs.sink = this.notificationService.get()\r\n      .pipe(switchMap((notifications: any) => {\r\n        this.notifications = notifications;\r\n        this.filteredNotifications = this.notifications.reverse();\r\n\r\n        // Filter notifications to get only the unread ones\r\n        this.unreadNotifications = this.notifications.filter(\r\n          (notification) => !notification.isRead\r\n        );\r\n\r\n        // Get an array of IDs for the unread notifications\r\n        const unreadNotificationIds = this.unreadNotifications.map(\r\n          (notification) => notification.id\r\n        );\r\n\r\n        // Use the IDs of the unread notifications to mark them as read\r\n        return this.notificationService.markAsReadBulk(unreadNotificationIds);\r\n      })).subscribe(\r\n        () => {\r\n          this.isLoadingNotifications = false;\r\n        },\r\n        (error) => {\r\n          this.isLoadingNotifications = false;\r\n        }\r\n      );\r\n\r\n    this.subs.sink = this.generalService.deviceKind.subscribe(res => {\r\n      this.isTablet = res.is1024;\r\n      console.log(this.isTablet);\r\n      this.showFiltersTablet = this.isTablet;\r\n    })\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  applyFilter(filterType: string) {\r\n    console.log(`Filter type: ${filterType}`);\r\n    // Update the current filter type\r\n    this.currentFilterType = filterType;\r\n    // Filter the notifications based on the selected filter type and search value\r\n    this.filteredNotifications = this.notifications.filter(notification => {\r\n      let dateCheck = true;\r\n\r\n      // If a fromDate and toDate were selected, check if the notification's creation date is in range\r\n      if (this.fromDate && this.toDate) {\r\n        // Add 1 to the from and to dates to ensure that notifications created on the exact date are included\r\n        const dateFrom = new Date(this.fromDate).setDate(new Date(this.fromDate).getDate() + 1);\r\n        const dateTo = new Date(this.toDate).setDate(new Date(this.toDate).getDate() + 1);\r\n\r\n        // Get the creation date of the notification\r\n        const createdAt = new Date(this.generalService.convertWithoutTimeAndZ(notification.createdAt));\r\n\r\n        // Check if the creation date falls within the selected range\r\n        dateCheck = createdAt >= new Date(dateFrom) && createdAt <= new Date(dateTo);\r\n      }\r\n      // Check if the notification's description includes the search value (case-insensitive)\r\n      let searchCheck = this.searchValue === '' || (notification.description.toLowerCase().includes(this.searchValue.toLowerCase()));\r\n\r\n      // Check if the notification's type matches the selected filter type\r\n      let filterCategoryCheck = !filterType || notification.type === this.currentFilterType;\r\n      // Return the notification if it passes all three checks (date, search, and filter type)\r\n      return dateCheck && searchCheck && filterCategoryCheck;\r\n    });\r\n  }\r\n\r\n  onSearchInput(event: any): void {\r\n    this.searchValue = event.target.value;\r\n    this.applyFilter(this.currentFilterType);\r\n  }\r\n\r\n  onFromDateSelection(event: any): void {\r\n    this.fromDate = this.generalService.convertToIsoDate(event);\r\n    this.applyFilter('');\r\n  }\r\n\r\n  onToDateSelection(event: any): void {\r\n    this.toDate = this.generalService.convertToIsoDate(event);\r\n    this.applyFilter('');\r\n  }\r\n\r\n  removeNotificationSelected(notificationId: number) {\r\n    this.subs.sink = this.notificationService.delete(notificationId).subscribe((res) => {\r\n      if (res) {\r\n        this.filteredNotifications = this.filteredNotifications.filter(item => item.id !== notificationId);\r\n      }\r\n    });\r\n  }\r\n\r\n  onCalendarClose(event: any) {\r\n    console.log(event);\r\n  }\r\n\r\n  toggleFilterVisibility() {\r\n    this.showFiltersTablet = !this.showFiltersTablet;\r\n  }\r\n}\r\n", "<div id=\"notifications-overview\" class=\"notifications-overview\">\r\n    <div class=\"surface-section\">\r\n        <div class=\"block-header relative justify-content-center  lg:flex\">\r\n\r\n            <div class=\"absolute pl-1 md:pl-2 left-0\">\r\n                \r\n                <g-header-back-button routerLink=\"/dashboard\" label=\"Back to Dashboard\"></g-header-back-button>\r\n                <!-- <button routerLink=\"/dashboard\" pButton pRipple type=\"button\" label=\"Back to Dashboard\" icon=\"pi pi-angle-left\" iconPos=\"left\"\r\n                     class=\"p-button-outlined p-button-sm p-button-rounded text-white\" styleClass=\"\"></button> -->\r\n            </div>\r\n            <span class=\"block-title\">\r\n                <span class=\"text-0\">Notifications</span>\r\n            </span>\r\n        </div>\r\n        <div class=\"surface-section px-2 py-2 md:px-26 lg:px-2 text-center\">\r\n            <button *ngIf=\"isTablet\" (click)=\"toggleFilterVisibility()\" pButton type=\"button\" label=\"\"\r\n                class=\"p-button-text\">\r\n                <span class=\"p-button-icon p-button-icon-right\" aria-hidden=\"true\"></span>\r\n                <span class=\"p-button-label mr-2\">Filters</span>\r\n                <img class=\"contact-icon-img\" src=\"/assets/icons/notifications/notif-filter.svg\" />\r\n            </button>\r\n        </div>\r\n        <div class=\"grid w-full\">\r\n            <div class=\"col-12 lg:col-3 lg:pr-5\">\r\n                <div [hidden]=\"showFiltersTablet\">\r\n                    <span class=\"p-input-icon-right w-full\">\r\n                        <input type=\"text\" pInputText placeholder=\"Search\" class=\" w-full input-blue rounded\"\r\n                            (input)=\"onSearchInput($event)\" />\r\n                        <i class=\"pi pi-search text-primary\"></i>\r\n                    </span>\r\n                </div>\r\n                <div class=\"lg:px-3 lg:pt-4\" [hidden]=\"showFiltersTablet\">\r\n                    <h6 class=\"font-bold\">Filter by category</h6>\r\n                    <ul class=\"list-none p-0 m-0 flex-grow-1\">\r\n                        <ng-container *ngFor=\"let filterType of filterTypes;\">\r\n                            <li class=\"flex align-items-center mb-3 text-base\" [hidden]=\"showFiltersTablet\">\r\n                                <span class=\"filter-link\"\r\n                                    [ngClass]=\"{'text-primary active font-bold': currentFilterType === filterType.type}\"\r\n                                    (click)=\"applyFilter(filterType.type)\">\r\n                                    {{filterType.name}}</span>\r\n                            </li>\r\n                        </ng-container>\r\n                        <li class=\"flex align-items-start mb-3 text-base flex-column\">\r\n                            <span class=\"filter-link cursor-auto\">Extras</span>\r\n                            <span class=\"filter-link cursor-auto\">From:</span>\r\n                            <p-calendar [maxDate]=\"currentDate\" [showIcon]=\"true\" placeholder=\"DD/MM/YY\"\r\n                                dateFormat=\"dd/mm/yy\" (onClose)=\"onCalendarClose($event)\"\r\n                                (onSelect)=\"onFromDateSelection($event)\"></p-calendar>\r\n\r\n                            <span class=\"filter-link cursor-auto\">To:</span>\r\n                            <p-calendar [maxDate]=\"currentDate\" [showIcon]=\"true\" placeholder=\"DD/MM/YY\"\r\n                                dateFormat=\"dd/mm/yy\" (onSelect)=\"onToDateSelection($event)\"></p-calendar>\r\n                        </li>\r\n                    </ul>\r\n                </div>\r\n            </div>\r\n            <div class=\"col-12 lg:col-9\">\r\n                <ul class=\"list-none p-0 m-0 max-h-30rem overflow-y-auto\">\r\n                    <ng-container *ngIf=\"(filteredNotifications.length === 0) && !isLoadingNotifications\">\r\n                        <div class=\"surface-section px-4 py-8 md:px-6 lg:px-8 text-center\">\r\n                            <img src=\"/assets/icons/notifications/bell.png\" alt=\"bell\" width=\"150\" />\r\n                            <div class=\"mt-3 font-bold text-xl\">\r\n                                <span class=\"text-900\">You don’t have any notifications yet</span>\r\n                            </div>\r\n                        </div>\r\n                    </ng-container>\r\n                    \r\n                    <ng-container *ngFor=\"let notif of filteredNotifications; let i=index;\">\r\n                        <app-notification-row-item \r\n                        [notification]=\"notif\" \r\n                        [slimStyle]=\"false\"\r\n                        (removeNotification)=\"removeNotificationSelected(notif.id)\">\r\n                    </app-notification-row-item>\r\n                    </ng-container>\r\n\r\n                    \r\n                </ul>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": "AACA,SAASA,SAAS,QAAQ,gBAAgB;AAG1C,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;;;;;;ICWrBC,EAAA,CAAAC,cAAA,iBAC0B;IADDD,EAAA,CAAAE,UAAA,mBAAAC,wEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,sBAAA,EAAwB;IAAA,EAAC;IAEvDT,EAAA,CAAAU,SAAA,eAA0E;IAC1EV,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAW,MAAA,cAAO;IAAAX,EAAA,CAAAY,YAAA,EAAO;IAChDZ,EAAA,CAAAU,SAAA,cAAmF;IACvFV,EAAA,CAAAY,YAAA,EAAS;;;;;;IAcGZ,EAAA,CAAAa,uBAAA,GAAsD;IAE9Cb,EADJ,CAAAC,cAAA,aAAgF,eAGjC;IAAvCD,EAAA,CAAAE,UAAA,mBAAAY,6EAAA;MAAA,MAAAC,aAAA,GAAAf,EAAA,CAAAI,aAAA,CAAAY,GAAA,EAAAC,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAY,WAAA,CAAAH,aAAA,CAAAI,IAAA,CAA4B;IAAA,EAAC;IACtCnB,EAAA,CAAAW,MAAA,GAAmB;IAC3BX,EAD2B,CAAAY,YAAA,EAAO,EAC7B;;;;;;IAL8CZ,EAAA,CAAAoB,SAAA,EAA4B;IAA5BpB,EAAA,CAAAqB,UAAA,WAAAf,MAAA,CAAAgB,iBAAA,CAA4B;IAEvEtB,EAAA,CAAAoB,SAAA,EAAoF;IAApFpB,EAAA,CAAAqB,UAAA,YAAArB,EAAA,CAAAuB,eAAA,IAAAC,GAAA,EAAAlB,MAAA,CAAAmB,iBAAA,KAAAV,aAAA,CAAAI,IAAA,EAAoF;IAEpFnB,EAAA,CAAAoB,SAAA,EAAmB;IAAnBpB,EAAA,CAAA0B,kBAAA,MAAAX,aAAA,CAAAY,IAAA,KAAmB;;;;;IAmBnC3B,EAAA,CAAAa,uBAAA,GAAsF;IAClFb,EAAA,CAAAC,cAAA,cAAmE;IAC/DD,EAAA,CAAAU,SAAA,cAAyE;IAErEV,EADJ,CAAAC,cAAA,cAAoC,eACT;IAAAD,EAAA,CAAAW,MAAA,gDAAoC;IAEnEX,EAFmE,CAAAY,YAAA,EAAO,EAChE,EACJ;;;;;;;IAGVZ,EAAA,CAAAa,uBAAA,GAAwE;IACpEb,EAAA,CAAAC,cAAA,oCAG4D;IAA5DD,EAAA,CAAAE,UAAA,gCAAA0B,+GAAA;MAAA,MAAAC,QAAA,GAAA7B,EAAA,CAAAI,aAAA,CAAA0B,GAAA,EAAAb,SAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAsBF,MAAA,CAAAyB,0BAAA,CAAAF,QAAA,CAAAG,EAAA,CAAoC;IAAA,EAAC;IAC/DhC,EAAA,CAAAY,YAAA,EAA4B;;;;;IAHxBZ,EAAA,CAAAoB,SAAA,EAAsB;IACtBpB,EADA,CAAAqB,UAAA,iBAAAQ,QAAA,CAAsB,oBACH;;;AD3D3C,OAAM,MAAOI,6BAA6B;EA4CxCC,YACUC,mBAAwC,EACxCC,cAA8B;IAD9B,KAAAD,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,cAAc,GAAdA,cAAc;IA5CxB,KAAAC,WAAW,GAAG,CACZ;MACEV,IAAI,EAAE,KAAK;MACXR,IAAI,EAAE;KACP,EACD;MACEQ,IAAI,EAAE,SAAS;MACfR,IAAI,EAAE;KACP,EACD;MACEQ,IAAI,EAAE,UAAU;MAChBR,IAAI,EAAE;KACP,EACD;MACEQ,IAAI,EAAE,cAAc;MACpBR,IAAI,EAAE;KACP,EACD;MACEQ,IAAI,EAAE,QAAQ;MACdR,IAAI,EAAE;KACP,EACD;MACEQ,IAAI,EAAE,iBAAiB;MACvBR,IAAI,EAAE;KACP,EACD;MACEQ,IAAI,EAAE,UAAU;MAChBR,IAAI,EAAE;KACP,CACF;IACD,KAAAmB,QAAQ,GAAG,EAAE;IACb,KAAAC,MAAM,GAAG,EAAE;IACX,KAAAC,WAAW,GAAG,IAAIC,IAAI,EAAE;IACxB,KAAAC,aAAa,GAAU,EAAE;IACzB,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAlB,iBAAiB,GAAW,EAAE;IAC9B,KAAAmB,qBAAqB,GAAU,EAAE;IACjC,KAAAC,QAAQ,GAAG,KAAK;IAChB,KAAAvB,iBAAiB,GAAG,KAAK;IACzB,KAAAwB,mBAAmB,GAAU,EAAE;IAC/B,KAAAC,sBAAsB,GAAG,IAAI;IACrB,KAAAC,IAAI,GAAG,IAAIjD,OAAO,EAAE;EAIxB;EAEJkD,QAAQA,CAAA;IAEN,IAAI,CAACD,IAAI,CAACE,IAAI,GAAG,IAAI,CAACf,mBAAmB,CAACgB,GAAG,EAAE,CAC5CC,IAAI,CAACtD,SAAS,CAAE4C,aAAkB,IAAI;MACrC,IAAI,CAACA,aAAa,GAAGA,aAAa;MAClC,IAAI,CAACE,qBAAqB,GAAG,IAAI,CAACF,aAAa,CAACW,OAAO,EAAE;MAEzD;MACA,IAAI,CAACP,mBAAmB,GAAG,IAAI,CAACJ,aAAa,CAACY,MAAM,CACjDC,YAAY,IAAK,CAACA,YAAY,CAACC,MAAM,CACvC;MAED;MACA,MAAMC,qBAAqB,GAAG,IAAI,CAACX,mBAAmB,CAACY,GAAG,CACvDH,YAAY,IAAKA,YAAY,CAACvB,EAAE,CAClC;MAED;MACA,OAAO,IAAI,CAACG,mBAAmB,CAACwB,cAAc,CAACF,qBAAqB,CAAC;IACvE,CAAC,CAAC,CAAC,CAACG,SAAS,CACX,MAAK;MACH,IAAI,CAACb,sBAAsB,GAAG,KAAK;IACrC,CAAC,EACAc,KAAK,IAAI;MACR,IAAI,CAACd,sBAAsB,GAAG,KAAK;IACrC,CAAC,CACF;IAEH,IAAI,CAACC,IAAI,CAACE,IAAI,GAAG,IAAI,CAACd,cAAc,CAAC0B,UAAU,CAACF,SAAS,CAACG,GAAG,IAAG;MAC9D,IAAI,CAAClB,QAAQ,GAAGkB,GAAG,CAACC,MAAM;MAC1BC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACrB,QAAQ,CAAC;MAC1B,IAAI,CAACvB,iBAAiB,GAAG,IAAI,CAACuB,QAAQ;IACxC,CAAC,CAAC;EACJ;EAEAsB,WAAWA,CAAA;IACT,IAAI,CAACnB,IAAI,CAACoB,WAAW,EAAE;EACzB;EAEAlD,WAAWA,CAACmD,UAAkB;IAC5BJ,OAAO,CAACC,GAAG,CAAC,gBAAgBG,UAAU,EAAE,CAAC;IACzC;IACA,IAAI,CAAC5C,iBAAiB,GAAG4C,UAAU;IACnC;IACA,IAAI,CAACzB,qBAAqB,GAAG,IAAI,CAACF,aAAa,CAACY,MAAM,CAACC,YAAY,IAAG;MACpE,IAAIe,SAAS,GAAG,IAAI;MAEpB;MACA,IAAI,IAAI,CAAChC,QAAQ,IAAI,IAAI,CAACC,MAAM,EAAE;QAChC;QACA,MAAMgC,QAAQ,GAAG,IAAI9B,IAAI,CAAC,IAAI,CAACH,QAAQ,CAAC,CAACkC,OAAO,CAAC,IAAI/B,IAAI,CAAC,IAAI,CAACH,QAAQ,CAAC,CAACmC,OAAO,EAAE,GAAG,CAAC,CAAC;QACvF,MAAMC,MAAM,GAAG,IAAIjC,IAAI,CAAC,IAAI,CAACF,MAAM,CAAC,CAACiC,OAAO,CAAC,IAAI/B,IAAI,CAAC,IAAI,CAACF,MAAM,CAAC,CAACkC,OAAO,EAAE,GAAG,CAAC,CAAC;QAEjF;QACA,MAAME,SAAS,GAAG,IAAIlC,IAAI,CAAC,IAAI,CAACL,cAAc,CAACwC,sBAAsB,CAACrB,YAAY,CAACoB,SAAS,CAAC,CAAC;QAE9F;QACAL,SAAS,GAAGK,SAAS,IAAI,IAAIlC,IAAI,CAAC8B,QAAQ,CAAC,IAAII,SAAS,IAAI,IAAIlC,IAAI,CAACiC,MAAM,CAAC;MAC9E;MACA;MACA,IAAIG,WAAW,GAAG,IAAI,CAAClC,WAAW,KAAK,EAAE,IAAKY,YAAY,CAACuB,WAAW,CAACC,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACrC,WAAW,CAACoC,WAAW,EAAE,CAAE;MAE9H;MACA,IAAIE,mBAAmB,GAAG,CAACZ,UAAU,IAAId,YAAY,CAACpC,IAAI,KAAK,IAAI,CAACM,iBAAiB;MACrF;MACA,OAAO6C,SAAS,IAAIO,WAAW,IAAII,mBAAmB;IACxD,CAAC,CAAC;EACJ;EAEAC,aAAaA,CAACC,KAAU;IACtB,IAAI,CAACxC,WAAW,GAAGwC,KAAK,CAACC,MAAM,CAACC,KAAK;IACrC,IAAI,CAACnE,WAAW,CAAC,IAAI,CAACO,iBAAiB,CAAC;EAC1C;EAEA6D,mBAAmBA,CAACH,KAAU;IAC5B,IAAI,CAAC7C,QAAQ,GAAG,IAAI,CAACF,cAAc,CAACmD,gBAAgB,CAACJ,KAAK,CAAC;IAC3D,IAAI,CAACjE,WAAW,CAAC,EAAE,CAAC;EACtB;EAEAsE,iBAAiBA,CAACL,KAAU;IAC1B,IAAI,CAAC5C,MAAM,GAAG,IAAI,CAACH,cAAc,CAACmD,gBAAgB,CAACJ,KAAK,CAAC;IACzD,IAAI,CAACjE,WAAW,CAAC,EAAE,CAAC;EACtB;EAEAa,0BAA0BA,CAAC0D,cAAsB;IAC/C,IAAI,CAACzC,IAAI,CAACE,IAAI,GAAG,IAAI,CAACf,mBAAmB,CAACuD,MAAM,CAACD,cAAc,CAAC,CAAC7B,SAAS,CAAEG,GAAG,IAAI;MACjF,IAAIA,GAAG,EAAE;QACP,IAAI,CAACnB,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,CAACU,MAAM,CAACqC,IAAI,IAAIA,IAAI,CAAC3D,EAAE,KAAKyD,cAAc,CAAC;MACpG;IACF,CAAC,CAAC;EACJ;EAEAG,eAAeA,CAACT,KAAU;IACxBlB,OAAO,CAACC,GAAG,CAACiB,KAAK,CAAC;EACpB;EAEA1E,sBAAsBA,CAAA;IACpB,IAAI,CAACa,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;EAClD;EAAC,QAAAuE,CAAA,G;qBAnJU5D,6BAA6B,EAAAjC,EAAA,CAAA8F,iBAAA,CAAAC,EAAA,CAAAC,mBAAA,GAAAhG,EAAA,CAAA8F,iBAAA,CAAAG,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA7BlE,6BAA6B;IAAAmE,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCP9B1G,EAJZ,CAAAC,cAAA,aAAgE,aAC/B,aAC0C,aAErB;QAEtCD,EAAA,CAAAU,SAAA,8BAA+F;QAGnGV,EAAA,CAAAY,YAAA,EAAM;QAEFZ,EADJ,CAAAC,cAAA,cAA0B,cACD;QAAAD,EAAA,CAAAW,MAAA,oBAAa;QAE1CX,EAF0C,CAAAY,YAAA,EAAO,EACtC,EACL;QACNZ,EAAA,CAAAC,cAAA,aAAoE;QAChED,EAAA,CAAA4G,UAAA,IAAAC,+CAAA,oBAC0B;QAK9B7G,EAAA,CAAAY,YAAA,EAAM;QAKUZ,EAJhB,CAAAC,cAAA,cAAyB,eACgB,eACC,gBACU,iBAEE;QAAlCD,EAAA,CAAAE,UAAA,mBAAA4G,+DAAAC,MAAA;UAAA,OAASJ,GAAA,CAAAzB,aAAA,CAAA6B,MAAA,CAAqB;QAAA,EAAC;QADnC/G,EAAA,CAAAY,YAAA,EACsC;QACtCZ,EAAA,CAAAU,SAAA,aAAyC;QAEjDV,EADI,CAAAY,YAAA,EAAO,EACL;QAEFZ,EADJ,CAAAC,cAAA,eAA0D,cAChC;QAAAD,EAAA,CAAAW,MAAA,0BAAkB;QAAAX,EAAA,CAAAY,YAAA,EAAK;QAC7CZ,EAAA,CAAAC,cAAA,cAA0C;QACtCD,EAAA,CAAA4G,UAAA,KAAAI,sDAAA,2BAAsD;QASlDhH,EADJ,CAAAC,cAAA,cAA8D,gBACpB;QAAAD,EAAA,CAAAW,MAAA,cAAM;QAAAX,EAAA,CAAAY,YAAA,EAAO;QACnDZ,EAAA,CAAAC,cAAA,gBAAsC;QAAAD,EAAA,CAAAW,MAAA,aAAK;QAAAX,EAAA,CAAAY,YAAA,EAAO;QAClDZ,EAAA,CAAAC,cAAA,sBAE6C;QAAzCD,EADsB,CAAAE,UAAA,qBAAA+G,sEAAAF,MAAA;UAAA,OAAWJ,GAAA,CAAAf,eAAA,CAAAmB,MAAA,CAAuB;QAAA,EAAC,sBAAAG,uEAAAH,MAAA;UAAA,OAC7CJ,GAAA,CAAArB,mBAAA,CAAAyB,MAAA,CAA2B;QAAA,EAAC;QAAC/G,EAAA,CAAAY,YAAA,EAAa;QAE1DZ,EAAA,CAAAC,cAAA,gBAAsC;QAAAD,EAAA,CAAAW,MAAA,WAAG;QAAAX,EAAA,CAAAY,YAAA,EAAO;QAChDZ,EAAA,CAAAC,cAAA,sBACiE;QAAvCD,EAAA,CAAAE,UAAA,sBAAAiH,uEAAAJ,MAAA;UAAA,OAAYJ,GAAA,CAAAnB,iBAAA,CAAAuB,MAAA,CAAyB;QAAA,EAAC;QAIhF/G,EAJiF,CAAAY,YAAA,EAAa,EAC7E,EACJ,EACH,EACJ;QAEFZ,EADJ,CAAAC,cAAA,eAA6B,cACiC;QAUtDD,EATA,CAAA4G,UAAA,KAAAQ,sDAAA,2BAAsF,KAAAC,sDAAA,2BASd;QAa5FrH,EAJgB,CAAAY,YAAA,EAAK,EACH,EACJ,EACJ,EACJ;;;QAjEeZ,EAAA,CAAAoB,SAAA,GAAc;QAAdpB,EAAA,CAAAqB,UAAA,SAAAsF,GAAA,CAAA9D,QAAA,CAAc;QASd7C,EAAA,CAAAoB,SAAA,GAA4B;QAA5BpB,EAAA,CAAAqB,UAAA,WAAAsF,GAAA,CAAArF,iBAAA,CAA4B;QAOJtB,EAAA,CAAAoB,SAAA,GAA4B;QAA5BpB,EAAA,CAAAqB,UAAA,WAAAsF,GAAA,CAAArF,iBAAA,CAA4B;QAGZtB,EAAA,CAAAoB,SAAA,GAAe;QAAfpB,EAAA,CAAAqB,UAAA,YAAAsF,GAAA,CAAAtE,WAAA,CAAe;QAWpCrC,EAAA,CAAAoB,SAAA,GAAuB;QAACpB,EAAxB,CAAAqB,UAAA,YAAAsF,GAAA,CAAAnE,WAAA,CAAuB,kBAAkB;QAKzCxC,EAAA,CAAAoB,SAAA,GAAuB;QAACpB,EAAxB,CAAAqB,UAAA,YAAAsF,GAAA,CAAAnE,WAAA,CAAuB,kBAAkB;QAQ9CxC,EAAA,CAAAoB,SAAA,GAAqE;QAArEpB,EAAA,CAAAqB,UAAA,SAAAsF,GAAA,CAAA/D,qBAAA,CAAA0E,MAAA,WAAAX,GAAA,CAAA5D,sBAAA,CAAqE;QASpD/C,EAAA,CAAAoB,SAAA,EAA0B;QAA1BpB,EAAA,CAAAqB,UAAA,YAAAsF,GAAA,CAAA/D,qBAAA,CAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}