{"ast": null, "code": "import { take } from 'rxjs/operators';\nimport { dummyReportRequests } from 'src/app/core/models/report.model';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/rating-and-report.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/button\";\nimport * as i4 from \"../../../../../shared/layout/g-level-circle/g-level-circle.component\";\nimport * as i5 from \"./report/report.component\";\nconst _c0 = [\"datepickerFrom\"];\nconst _c1 = [\"datepickerTo\"];\nfunction ClassReportsComponent_div_0_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"b\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 10);\n    i0.ɵɵelement(7, \"g-level-circle\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 12)(9, \"div\", 13)(10, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function ClassReportsComponent_div_0_div_4_Template_button_click_10_listener() {\n      const report_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.viewReport(report_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function ClassReportsComponent_div_0_div_4_Template_button_click_11_listener() {\n      const report_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.viewReport(report_r2));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function ClassReportsComponent_div_0_div_4_Template_button_click_12_listener() {\n      const report_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.viewReport(report_r2));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const report_r2 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(report_r2.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(report_r2.dateCreated);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"name\", \"A1\");\n  }\n}\nfunction ClassReportsComponent_div_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 18);\n    i0.ɵɵtext(2, \" There are no reports yet. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"img\", 19);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClassReportsComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3);\n    i0.ɵɵelement(2, \"div\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 5);\n    i0.ɵɵtemplate(4, ClassReportsComponent_div_0_div_4_Template, 13, 3, \"div\", 6)(5, ClassReportsComponent_div_0_div_5_Template, 4, 0, \"div\", 7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.reports);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.reports.length == 0 && !ctx_r2.isLoading);\n  }\n}\nfunction ClassReportsComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"app-report\", 21);\n    i0.ɵɵlistener(\"closeReport\", function ClassReportsComponent_div_1_Template_app_report_closeReport_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCloseReport());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"stats\", ctx_r2.stats)(\"classroom\", ctx_r2.classroom)(\"report\", ctx_r2.reportToView)(\"mode\", ctx_r2.reportMode);\n  }\n}\nexport class ClassReportsComponent {\n  constructor(ratingAndReportService) {\n    this.ratingAndReportService = ratingAndReportService;\n    this.classroom = {};\n    this.subs = new SubSink();\n    this.showFilters = false;\n    this.showReport = false;\n    this.reportToView = {};\n    this.reportMode = 'create';\n    this.reports = dummyReportRequests;\n    this.isLoading = true;\n  }\n  ngOnInit() {\n    this.subs.sink = this.ratingAndReportService.updateListener.subscribe(res => {\n      this.getReports();\n    });\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  getReports() {\n    this.ratingAndReportService.getClassroomReports(this.classroom).pipe(take(1)).subscribe(res => {\n      this.isLoading = false;\n      this.reports = res;\n    });\n  }\n  createReport() {\n    this.reportToView = {};\n    this.reportMode = 'create';\n    this.showReport = true;\n  }\n  onCloseReport() {\n    this.showReport = false;\n  }\n  viewReport(report) {\n    this.reportMode = 'view';\n    let request = {\n      classId: parseInt(this.classroom.id),\n      studentIds: report.studentIds,\n      start: report.period.from,\n      end: report.period.to\n    };\n    this.reportToView = report;\n    this.ratingAndReportService.getClassroomReport(request).pipe(take(1)).subscribe(res => {\n      this.stats = res;\n      console.log(res);\n      this.showReport = true;\n    });\n  }\n  // editReport(report: CreateReportRequest) {\n  //   this.reportMode = 'edit';\n  //   this.reportToView = CreateReportRequest;\n  //   this.showReport = true;\n  // }\n  fromRange(el) {\n    this.fromValue = el.value;\n  }\n  toRange(el) {\n    this.toValue = el.value;\n  }\n  applyFilters() {\n    this.showFilters = false;\n  }\n  onFocusFrom() {\n    this.datepickerObj.show();\n  }\n  onFocusTo() {\n    this.datepickerObjTo.show();\n  }\n  static #_ = this.ɵfac = function ClassReportsComponent_Factory(t) {\n    return new (t || ClassReportsComponent)(i0.ɵɵdirectiveInject(i1.RatingAndReportService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ClassReportsComponent,\n    selectors: [[\"app-class-reports\"]],\n    viewQuery: function ClassReportsComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.datepickerObj = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.datepickerObjTo = _t.first);\n      }\n    },\n    inputs: {\n      classroom: \"classroom\"\n    },\n    decls: 2,\n    vars: 2,\n    consts: [[\"class\", \"class-reports\", 4, \"ngIf\"], [\"class\", \"section class-reports\", 4, \"ngIf\"], [1, \"class-reports\"], [1, \"class-reports-header\"], [2, \"position\", \"relative\"], [1, \"class-reports-list\"], [\"class\", \"report\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"no-data\", 4, \"ngIf\"], [1, \"report\"], [1, \"font-sm\", \"flex\", \"flex-column\"], [1, \"text-center\", \"level\"], [\"size\", \"xs\", 3, \"name\"], [1, \"reports-list-actions\"], [1, \"flex\", \"justify-content-end\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"View\", \"icon\", \"pi pi-eye\", 1, \"p-button-text\", \"p-button-xs\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Edit\", \"icon\", \"pi pi-pencil\", 1, \"p-button-text\", \"p-button-xs\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Download\", \"icon\", \"pi pi-cloud-download\", 1, \"p-button-text\", \"p-button-xs\", 3, \"click\"], [1, \"no-data\"], [1, \"title\"], [\"src\", \"/assets/icons/empty-classroom.png\", \"height\", \"150\", 2, \"width\", \"auto\"], [1, \"section\", \"class-reports\"], [3, \"closeReport\", \"stats\", \"classroom\", \"report\", \"mode\"]],\n    template: function ClassReportsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, ClassReportsComponent_div_0_Template, 6, 2, \"div\", 0)(1, ClassReportsComponent_div_1_Template, 2, 4, \"div\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", !ctx.showReport);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showReport);\n      }\n    },\n    dependencies: [i2.NgForOf, i2.NgIf, i3.ButtonDirective, i4.GLevelCircleComponent, i5.ReportComponent],\n    styles: [\".section[_ngcontent-%COMP%] {\\n  background-color: var(--white);\\n  border-top-left-radius: 28px;\\n  border-top-right-radius: 28px;\\n  border-bottom-left-radius: 28px;\\n  border-bottom-right-radius: 28px;\\n  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);\\n  margin-top: 30px;\\n  margin-bottom: 30px;\\n}\\n.section[_ngcontent-%COMP%]   .section-content[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  transition: height 0.3s ease-out;\\n}\\n\\n.class-reports[_ngcontent-%COMP%]   .class-reports-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.class-reports[_ngcontent-%COMP%]   .class-reports-header[_ngcontent-%COMP%]   .header-info[_ngcontent-%COMP%] {\\n  color: #707070;\\n}\\n.class-reports[_ngcontent-%COMP%]   .class-reports-header[_ngcontent-%COMP%]   .search[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n}\\n.class-reports[_ngcontent-%COMP%]   .class-reports-header[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n  margin: 5px;\\n}\\n.class-reports[_ngcontent-%COMP%]   .class-reports-list[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n}\\n.class-reports[_ngcontent-%COMP%]   .class-reports-list[_ngcontent-%COMP%]   .report[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-top: 10px;\\n  padding-bottom: 10px;\\n  border-bottom: 1px solid #f4f4f8;\\n}\\n.class-reports[_ngcontent-%COMP%]   .class-reports-list[_ngcontent-%COMP%]   .report[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.class-reports[_ngcontent-%COMP%]   .class-reports-list[_ngcontent-%COMP%]   .report[_ngcontent-%COMP%]    > .level[_ngcontent-%COMP%] {\\n  width: 30%;\\n}\\n.class-reports[_ngcontent-%COMP%]   .class-reports-list[_ngcontent-%COMP%]   .report[_ngcontent-%COMP%]   .report-title[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #aaaab3;\\n  margin-bottom: 10px;\\n}\\n.class-reports[_ngcontent-%COMP%]   .class-reports-list[_ngcontent-%COMP%]   .report[_ngcontent-%COMP%]   .reports-list-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  flex-direction: column;\\n}\\n.class-reports[_ngcontent-%COMP%]   .class-reports-list[_ngcontent-%COMP%]   .report[_ngcontent-%COMP%]   .reports-list-actions[_ngcontent-%COMP%]   .actions[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n.class-reports[_ngcontent-%COMP%]   .class-reports-list[_ngcontent-%COMP%]   .report[_ngcontent-%COMP%]   .reports-list-actions[_ngcontent-%COMP%]   .report-title-dummy[_ngcontent-%COMP%] {\\n  color: white;\\n  width: 100%;\\n}\\n\\n.header-item-list[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 30px;\\n  left: 0;\\n  width: 150px;\\n  padding: 10px;\\n  border-radius: 8px;\\n  background-color: white;\\n  border: 1px solid var(--main-color);\\n  z-index: 1;\\n  transform: translateX(-50%);\\n  left: 50%;\\n}\\n.header-item-list[_ngcontent-%COMP%]    > .item[_ngcontent-%COMP%] {\\n  padding-bottom: 5px;\\n  border-bottom: 1px solid var(--main-color);\\n  margin-top: 10px;\\n}\\n.header-item-list[_ngcontent-%COMP%]    > .item[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.header-item-list[_ngcontent-%COMP%]    > .range[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n}\\n\\n[_nghost-%COMP%]     .p-button .p-button-icon-left {\\n  margin-right: 0;\\n  font-size: 0.7rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["take", "dummyReportRequests", "SubSink", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "ClassReportsComponent_div_0_div_4_Template_button_click_10_listener", "report_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "viewReport", "ClassReportsComponent_div_0_div_4_Template_button_click_11_listener", "ClassReportsComponent_div_0_div_4_Template_button_click_12_listener", "ɵɵadvance", "ɵɵtextInterpolate", "title", "dateCreated", "ɵɵproperty", "ɵɵtemplate", "ClassReportsComponent_div_0_div_4_Template", "ClassReportsComponent_div_0_div_5_Template", "reports", "length", "isLoading", "ClassReportsComponent_div_1_Template_app_report_closeReport_1_listener", "_r4", "onCloseReport", "stats", "classroom", "reportToView", "reportMode", "ClassReportsComponent", "constructor", "ratingAndReportService", "subs", "showFilters", "showReport", "ngOnInit", "sink", "updateListener", "subscribe", "res", "getReports", "ngOnDestroy", "unsubscribe", "getClassroomReports", "pipe", "createReport", "report", "request", "classId", "parseInt", "id", "studentIds", "start", "period", "from", "end", "to", "getClassroomReport", "console", "log", "fromRange", "el", "fromValue", "value", "to<PERSON><PERSON><PERSON>", "toValue", "applyFilters", "onFocusFrom", "datepickerObj", "show", "onFocusTo", "datepickerObjTo", "_", "ɵɵdirectiveInject", "i1", "RatingAndReportService", "_2", "selectors", "viewQuery", "ClassReportsComponent_Query", "rf", "ctx", "ClassReportsComponent_div_0_Template", "ClassReportsComponent_div_1_Template"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\classrooms\\class\\class-reports\\class-reports.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\classrooms\\class\\class-reports\\class-reports.component.html"], "sourcesContent": ["import { Component, Input, OnInit, ViewChild } from '@angular/core';\r\nimport { take } from 'rxjs/operators';\r\nimport { Classroom } from 'src/app/core/models/classroom.model';\r\nimport { CreateReportRequest, dummyReportRequests, getReportRequest, Report } from 'src/app/core/models/report.model';\r\nimport { RatingAndReportService } from 'src/app/core/services/rating-and-report.service';\r\nimport { SubSink } from 'subsink';\r\n\r\n@Component({\r\n  selector: 'app-class-reports',\r\n  templateUrl: './class-reports.component.html',\r\n  styleUrls: ['./class-reports.component.scss']\r\n})\r\nexport class ClassReportsComponent implements OnInit {\r\n  @Input() classroom: Classroom = {} as Classroom;\r\n  private subs = new SubSink()\r\n  public showFilters: boolean = false;\r\n  public showReport: boolean = false;\r\n  public reportToView: CreateReportRequest = {} as CreateReportRequest;\r\n  term: any\r\n  reportMode: 'create' | 'view' | 'edit' = 'create';\r\n  public fromValue?: Date;\r\n  public toValue?: Date;\r\n  public reports: CreateReportRequest[] = dummyReportRequests;\r\n  stats: any;\r\n  period: any\r\n  isLoading: boolean = true;\r\n\r\n  constructor(\r\n    private ratingAndReportService: RatingAndReportService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.subs.sink = this.ratingAndReportService.updateListener.subscribe(res => {\r\n      this.getReports()\r\n    })\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  getReports() {\r\n    this.ratingAndReportService.getClassroomReports(this.classroom).pipe(take(1)).subscribe(res => {\r\n      this.isLoading = false;\r\n      this.reports = res;\r\n    })\r\n  }\r\n\r\n  createReport() {\r\n    this.reportToView = {} as CreateReportRequest;\r\n    this.reportMode = 'create';\r\n    this.showReport = true\r\n  }\r\n\r\n  onCloseReport() {\r\n    this.showReport = false\r\n  }\r\n\r\n  viewReport(report: CreateReportRequest) {\r\n    this.reportMode = 'view';\r\n    let request: getReportRequest = {\r\n      classId: parseInt(this.classroom.id!),\r\n      studentIds: report.studentIds,\r\n      start: report.period.from,\r\n      end: report.period.to,\r\n    }\r\n    this.reportToView = report\r\n    this.ratingAndReportService.getClassroomReport(request).pipe(take(1)).subscribe(res => {\r\n      this.stats = res;\r\n      console.log(res)\r\n      this.showReport = true;\r\n    })\r\n  }\r\n\r\n  // editReport(report: CreateReportRequest) {\r\n  //   this.reportMode = 'edit';\r\n  //   this.reportToView = CreateReportRequest;\r\n  //   this.showReport = true;\r\n  // }\r\n\r\n  fromRange(el: any) {\r\n    this.fromValue = el.value;\r\n  }\r\n\r\n  toRange(el: any) {\r\n    this.toValue = el.value;\r\n  }\r\n\r\n  applyFilters() {\r\n    this.showFilters = false;\r\n  }\r\n\r\n  @ViewChild('datepickerFrom')\r\n  public datepickerObj: any;\r\n  onFocusFrom(): void {\r\n    this.datepickerObj.show()\r\n  }\r\n\r\n  @ViewChild('datepickerTo')\r\n  public datepickerObjTo: any;\r\n  onFocusTo(): void {\r\n    this.datepickerObjTo.show()\r\n  }\r\n}\r\n", "<div *ngIf=\"!showReport\" class=\"class-reports\">\r\n    <div class=\"class-reports-header\">\r\n        <!-- <div class=\"header-info\">\r\n            Start Date: <strong>Dummy date</strong>\r\n        </div>\r\n        <div class=\"header-info\" style=\"margin-left:20px\">\r\n            Hours: <strong>35</strong>\r\n        </div> -->\r\n        <!-- <div class=\"search\">\r\n            <input type=\"text\" [(ngModel)]=\"term\" class=\"searchTerm\" placeholder=\"Search Reports\">\r\n        </div> -->\r\n        <div style=\"position: relative\">\r\n            <!-- <div (click)=\"showFilters = !showFilters\" class=\"filters-button hvr-glow\">\r\n                <div>Filters</div><img src=\"/assets/icons/filters.svg\">\r\n            </div> -->\r\n            <!-- <div *ngIf=\"showFilters\" class=\"header-item-list\">\r\n                <div class=\"popup-title\">\r\n                    <div>Filters</div>\r\n                    <img (click)=\"showFilters = false;\" src=\"/assets/icons/close.png\" class=\"close-img close-img-abs hvr-glow\">\r\n                </div>\r\n                <div class=\"range\">\r\n                    <strong>\r\n                        From:\r\n                    </strong>\r\n                    <ejs-datepicker #datepickerFrom (focus)='onFocusFrom($event)' class=\"item\" format='dd-MM-yyyy' #from (change)=\"fromRange(from)\"\r\n                        [value]=\"fromValue\"></ejs-datepicker>\r\n                </div>\r\n                <div class=\"range\">\r\n                    <strong>\r\n                        To:\r\n                    </strong>\r\n                    <ejs-datepicker #datepickerTo (focus)='onFocusTo($event)' class=\"item\" format='dd-MM-yyyy' #to (change)=\"toRange(to)\" [value]=\"toValue\">\r\n                    </ejs-datepicker>\r\n                </div>\r\n                <div class=\"light-purple-button m-t-15\" (click)=\"applyFilters()\">\r\n                    Apply\r\n                </div>\r\n            </div> -->\r\n        </div>\r\n        <!-- <div (click)=\"createReport()\" class=\"light-purple-button\">\r\n            <div>Create Report</div><img src=\"/assets/icons/plus.png\">\r\n        </div> -->\r\n    </div>\r\n    <div class=\"class-reports-list\">\r\n\r\n        <div *ngFor=\"let report of reports; let i=index\" class=\"report\">\r\n            <div class=\"font-sm flex flex-column\">\r\n                <b>{{report.title}}</b>\r\n                <span>{{report.dateCreated}}</span>\r\n            </div>\r\n            <div class=\"text-center level\">\r\n                <g-level-circle size=\"xs\" [name]=\"'A1'\"></g-level-circle>\r\n            </div>\r\n            <!-- <div class=\"text-center\">\r\n                <div *ngIf=\"i===0\" class=\"report-title\">Date Created</div>\r\n                {{report.dateCreated | date: 'EEEE M/d/yyyy'}}\r\n            </div>\r\n            <div class=\"text-center\">\r\n                <div *ngIf=\"i===0\" class=\"report-title\">Date Range</div>\r\n                {{report.period.from | date: 'd/M/yyyy'}}-{{report.period.to | date: 'd/M/yyyy'}}\r\n            </div> -->\r\n            <div class=\"reports-list-actions\">                \r\n                <div class=\" flex justify-content-end\">\r\n                    <button pButton pRipple label=\"View\" (click)=\"viewReport(report)\" icon=\"pi pi-eye\" \r\n                    class=\"p-button-text p-button-xs\">\r\n                    </button>\r\n                    <button pButton pRipple label=\"Edit\" (click)=\"viewReport(report)\" icon=\"pi pi-pencil\" \r\n                    class=\"p-button-text p-button-xs\">\r\n                    </button>\r\n                    <button pButton pRipple label=\"Download\" (click)=\"viewReport(report)\" icon=\"pi pi-cloud-download\" \r\n                    class=\"p-button-text p-button-xs\">\r\n                    </button>\r\n                </div>\r\n\r\n            </div>\r\n        </div>\r\n        <div *ngIf=\"reports.length == 0 && !isLoading\" class=\"no-data\">\r\n            <div class=\"title\">\r\n                There are no reports yet.\r\n            </div>\r\n            <img src=\"/assets/icons/empty-classroom.png\" style=\"width: auto;\" height=\"150\">\r\n        </div>\r\n    </div>\r\n</div>\r\n\r\n<div *ngIf=\"showReport\" class=\"section class-reports\">\r\n    <app-report [stats]=\"stats\" (closeReport)=\"onCloseReport()\" [classroom]=\"classroom\" [report]=\"reportToView\" [mode]=\"reportMode\">\r\n    </app-report>\r\n</div>"], "mappings": "AACA,SAASA,IAAI,QAAQ,gBAAgB;AAErC,SAA8BC,mBAAmB,QAAkC,kCAAkC;AAErH,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;IC0CjBC,EAFR,CAAAC,cAAA,aAAgE,aACtB,QAC/B;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACvBH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAChCF,EADgC,CAAAG,YAAA,EAAO,EACjC;IACNH,EAAA,CAAAC,cAAA,cAA+B;IAC3BD,EAAA,CAAAI,SAAA,yBAAyD;IAC7DJ,EAAA,CAAAG,YAAA,EAAM;IAWEH,EAFR,CAAAC,cAAA,cAAkC,cACS,kBAED;IADGD,EAAA,CAAAK,UAAA,mBAAAC,oEAAA;MAAA,MAAAC,SAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,UAAA,CAAAP,SAAA,CAAkB;IAAA,EAAC;IAEjEP,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACkC;IADGD,EAAA,CAAAK,UAAA,mBAAAU,oEAAA;MAAA,MAAAR,SAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,UAAA,CAAAP,SAAA,CAAkB;IAAA,EAAC;IAEjEP,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBACkC;IADOD,EAAA,CAAAK,UAAA,mBAAAW,oEAAA;MAAA,MAAAT,SAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,UAAA,CAAAP,SAAA,CAAkB;IAAA,EAAC;IAMjFP,EAJY,CAAAG,YAAA,EAAS,EACP,EAEJ,EACJ;;;;IA5BKH,EAAA,CAAAiB,SAAA,GAAgB;IAAhBjB,EAAA,CAAAkB,iBAAA,CAAAX,SAAA,CAAAY,KAAA,CAAgB;IACbnB,EAAA,CAAAiB,SAAA,GAAsB;IAAtBjB,EAAA,CAAAkB,iBAAA,CAAAX,SAAA,CAAAa,WAAA,CAAsB;IAGFpB,EAAA,CAAAiB,SAAA,GAAa;IAAbjB,EAAA,CAAAqB,UAAA,cAAa;;;;;IA0B3CrB,EADJ,CAAAC,cAAA,cAA+D,cACxC;IACfD,EAAA,CAAAE,MAAA,kCACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,SAAA,cAA+E;IACnFJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAhFVH,EADJ,CAAAC,cAAA,aAA+C,aACT;IAU9BD,EAAA,CAAAI,SAAA,aA2BM;IAIVJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAAgC;IAiC5BD,EA/BA,CAAAsB,UAAA,IAAAC,0CAAA,kBAAgE,IAAAC,0CAAA,iBA+BD;IAOvExB,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IAtC0BH,EAAA,CAAAiB,SAAA,GAAY;IAAZjB,EAAA,CAAAqB,UAAA,YAAAV,MAAA,CAAAc,OAAA,CAAY;IA+B9BzB,EAAA,CAAAiB,SAAA,EAAuC;IAAvCjB,EAAA,CAAAqB,UAAA,SAAAV,MAAA,CAAAc,OAAA,CAAAC,MAAA,UAAAf,MAAA,CAAAgB,SAAA,CAAuC;;;;;;IAUjD3B,EADJ,CAAAC,cAAA,cAAsD,qBAC8E;IAApGD,EAAA,CAAAK,UAAA,yBAAAuB,uEAAA;MAAA5B,EAAA,CAAAQ,aAAA,CAAAqB,GAAA;MAAA,MAAAlB,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAeF,MAAA,CAAAmB,aAAA,EAAe;IAAA,EAAC;IAE/D9B,EADI,CAAAG,YAAA,EAAa,EACX;;;;IAFUH,EAAA,CAAAiB,SAAA,EAAe;IAAiFjB,EAAhG,CAAAqB,UAAA,UAAAV,MAAA,CAAAoB,KAAA,CAAe,cAAApB,MAAA,CAAAqB,SAAA,CAAwD,WAAArB,MAAA,CAAAsB,YAAA,CAAwB,SAAAtB,MAAA,CAAAuB,UAAA,CAAoB;;;AD1EnI,OAAM,MAAOC,qBAAqB;EAehCC,YACUC,sBAA8C;IAA9C,KAAAA,sBAAsB,GAAtBA,sBAAsB;IAfvB,KAAAL,SAAS,GAAc,EAAe;IACvC,KAAAM,IAAI,GAAG,IAAIvC,OAAO,EAAE;IACrB,KAAAwC,WAAW,GAAY,KAAK;IAC5B,KAAAC,UAAU,GAAY,KAAK;IAC3B,KAAAP,YAAY,GAAwB,EAAyB;IAEpE,KAAAC,UAAU,GAA+B,QAAQ;IAG1C,KAAAT,OAAO,GAA0B3B,mBAAmB;IAG3D,KAAA6B,SAAS,GAAY,IAAI;EAIrB;EAEJc,QAAQA,CAAA;IACN,IAAI,CAACH,IAAI,CAACI,IAAI,GAAG,IAAI,CAACL,sBAAsB,CAACM,cAAc,CAACC,SAAS,CAACC,GAAG,IAAG;MAC1E,IAAI,CAACC,UAAU,EAAE;IACnB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACT,IAAI,CAACU,WAAW,EAAE;EACzB;EAEAF,UAAUA,CAAA;IACR,IAAI,CAACT,sBAAsB,CAACY,mBAAmB,CAAC,IAAI,CAACjB,SAAS,CAAC,CAACkB,IAAI,CAACrD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC+C,SAAS,CAACC,GAAG,IAAG;MAC5F,IAAI,CAAClB,SAAS,GAAG,KAAK;MACtB,IAAI,CAACF,OAAO,GAAGoB,GAAG;IACpB,CAAC,CAAC;EACJ;EAEAM,YAAYA,CAAA;IACV,IAAI,CAAClB,YAAY,GAAG,EAAyB;IAC7C,IAAI,CAACC,UAAU,GAAG,QAAQ;IAC1B,IAAI,CAACM,UAAU,GAAG,IAAI;EACxB;EAEAV,aAAaA,CAAA;IACX,IAAI,CAACU,UAAU,GAAG,KAAK;EACzB;EAEA1B,UAAUA,CAACsC,MAA2B;IACpC,IAAI,CAAClB,UAAU,GAAG,MAAM;IACxB,IAAImB,OAAO,GAAqB;MAC9BC,OAAO,EAAEC,QAAQ,CAAC,IAAI,CAACvB,SAAS,CAACwB,EAAG,CAAC;MACrCC,UAAU,EAAEL,MAAM,CAACK,UAAU;MAC7BC,KAAK,EAAEN,MAAM,CAACO,MAAM,CAACC,IAAI;MACzBC,GAAG,EAAET,MAAM,CAACO,MAAM,CAACG;KACpB;IACD,IAAI,CAAC7B,YAAY,GAAGmB,MAAM;IAC1B,IAAI,CAACf,sBAAsB,CAAC0B,kBAAkB,CAACV,OAAO,CAAC,CAACH,IAAI,CAACrD,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC+C,SAAS,CAACC,GAAG,IAAG;MACpF,IAAI,CAACd,KAAK,GAAGc,GAAG;MAChBmB,OAAO,CAACC,GAAG,CAACpB,GAAG,CAAC;MAChB,IAAI,CAACL,UAAU,GAAG,IAAI;IACxB,CAAC,CAAC;EACJ;EAEA;EACA;EACA;EACA;EACA;EAEA0B,SAASA,CAACC,EAAO;IACf,IAAI,CAACC,SAAS,GAAGD,EAAE,CAACE,KAAK;EAC3B;EAEAC,OAAOA,CAACH,EAAO;IACb,IAAI,CAACI,OAAO,GAAGJ,EAAE,CAACE,KAAK;EACzB;EAEAG,YAAYA,CAAA;IACV,IAAI,CAACjC,WAAW,GAAG,KAAK;EAC1B;EAIAkC,WAAWA,CAAA;IACT,IAAI,CAACC,aAAa,CAACC,IAAI,EAAE;EAC3B;EAIAC,SAASA,CAAA;IACP,IAAI,CAACC,eAAe,CAACF,IAAI,EAAE;EAC7B;EAAC,QAAAG,CAAA,G;qBA1FU3C,qBAAqB,EAAAnC,EAAA,CAAA+E,iBAAA,CAAAC,EAAA,CAAAC,sBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAArB/C,qBAAqB;IAAAgD,SAAA;IAAAC,SAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;QCyElCtF,EArFA,CAAAsB,UAAA,IAAAkE,oCAAA,iBAA+C,IAAAC,oCAAA,iBAqFO;;;QArFhDzF,EAAA,CAAAqB,UAAA,UAAAkE,GAAA,CAAA/C,UAAA,CAAiB;QAqFjBxC,EAAA,CAAAiB,SAAA,EAAgB;QAAhBjB,EAAA,CAAAqB,UAAA,SAAAkE,GAAA,CAAA/C,UAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}