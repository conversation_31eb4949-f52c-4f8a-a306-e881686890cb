{"ast": null, "code": "import { DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, ElementRef, booleanAttribute, Directive, Input, NgModule } from '@angular/core';\nimport { <PERSON>Hand<PERSON> } from 'primeng/dom';\n\n/**\n * AutoFocus manages focus on focusable element on load.\n * @group Components\n */\nlet AutoFocus = /*#__PURE__*/(() => {\n  class AutoFocus {\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    autofocus = false;\n    focused = false;\n    platformId = inject(PLATFORM_ID);\n    document = inject(DOCUMENT);\n    host = inject(ElementRef);\n    ngAfterContentChecked() {\n      // This sets the `attr.autofocus` which is different than the Input `autofocus` attribute.\n      if (this.autofocus === false) {\n        this.host.nativeElement.removeAttribute('autofocus');\n      } else {\n        this.host.nativeElement.setAttribute('autofocus', true);\n      }\n      if (!this.focused) {\n        this.autoFocus();\n      }\n    }\n    ngAfterViewChecked() {\n      if (!this.focused) {\n        this.autoFocus();\n      }\n    }\n    autoFocus() {\n      if (isPlatformBrowser(this.platformId) && this.autofocus) {\n        setTimeout(() => {\n          const focusableElements = DomHandler.getFocusableElements(this.host?.nativeElement);\n          if (focusableElements.length === 0) {\n            this.host.nativeElement.focus();\n          }\n          if (focusableElements.length > 0) {\n            focusableElements[0].focus();\n          }\n          this.focused = true;\n        });\n      }\n    }\n    static ɵfac = function AutoFocus_Factory(t) {\n      return new (t || AutoFocus)();\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: AutoFocus,\n      selectors: [[\"\", \"pAutoFocus\", \"\"]],\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        autofocus: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autofocus\", \"autofocus\", booleanAttribute]\n      },\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature]\n    });\n  }\n  return AutoFocus;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet AutoFocusModule = /*#__PURE__*/(() => {\n  class AutoFocusModule {\n    static ɵfac = function AutoFocusModule_Factory(t) {\n      return new (t || AutoFocusModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: AutoFocusModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n  return AutoFocusModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AutoFocus, AutoFocusModule };", "map": {"version": 3, "names": ["DOCUMENT", "isPlatformBrowser", "i0", "inject", "PLATFORM_ID", "ElementRef", "booleanAttribute", "Directive", "Input", "NgModule", "<PERSON><PERSON><PERSON><PERSON>", "AutoFocus", "autofocus", "focused", "platformId", "document", "host", "ngAfterContentChecked", "nativeElement", "removeAttribute", "setAttribute", "autoFocus", "ngAfterViewChecked", "setTimeout", "focusableElements", "getFocusableElements", "length", "focus", "ɵfac", "AutoFocus_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "standalone", "features", "ɵɵInputTransformsFeature", "ngDevMode", "AutoFocusModule", "AutoFocusModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/primeng/fesm2022/primeng-autofocus.mjs"], "sourcesContent": ["import { DOCUMENT, isPlatformBrowser } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, PLATFORM_ID, ElementRef, booleanAttribute, Directive, Input, NgModule } from '@angular/core';\nimport { <PERSON>Hand<PERSON> } from 'primeng/dom';\n\n/**\n * AutoFocus manages focus on focusable element on load.\n * @group Components\n */\nclass AutoFocus {\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    autofocus = false;\n    focused = false;\n    platformId = inject(PLATFORM_ID);\n    document = inject(DOCUMENT);\n    host = inject(ElementRef);\n    ngAfterContentChecked() {\n        // This sets the `attr.autofocus` which is different than the Input `autofocus` attribute.\n        if (this.autofocus === false) {\n            this.host.nativeElement.removeAttribute('autofocus');\n        }\n        else {\n            this.host.nativeElement.setAttribute('autofocus', true);\n        }\n        if (!this.focused) {\n            this.autoFocus();\n        }\n    }\n    ngAfterViewChecked() {\n        if (!this.focused) {\n            this.autoFocus();\n        }\n    }\n    autoFocus() {\n        if (isPlatformBrowser(this.platformId) && this.autofocus) {\n            setTimeout(() => {\n                const focusableElements = DomHandler.getFocusableElements(this.host?.nativeElement);\n                if (focusableElements.length === 0) {\n                    this.host.nativeElement.focus();\n                }\n                if (focusableElements.length > 0) {\n                    focusableElements[0].focus();\n                }\n                this.focused = true;\n            });\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: AutoFocus, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.3.7\", type: AutoFocus, isStandalone: true, selector: \"[pAutoFocus]\", inputs: { autofocus: [\"autofocus\", \"autofocus\", booleanAttribute] }, host: { classAttribute: \"p-element\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: AutoFocus, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[pAutoFocus]',\n                    standalone: true,\n                    host: {\n                        class: 'p-element'\n                    }\n                }]\n        }], propDecorators: { autofocus: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\nclass AutoFocusModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: AutoFocusModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.7\", ngImport: i0, type: AutoFocusModule, imports: [AutoFocus], exports: [AutoFocus] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: AutoFocusModule });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: AutoFocusModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [AutoFocus],\n                    exports: [AutoFocus]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AutoFocus, AutoFocusModule };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,iBAAiB,QAAQ,iBAAiB;AAC7D,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,WAAW,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAC7G,SAASC,UAAU,QAAQ,aAAa;;AAExC;AACA;AACA;AACA;AAHA,IAIMC,SAAS;EAAf,MAAMA,SAAS,CAAC;IACZ;AACJ;AACA;AACA;IACIC,SAAS,GAAG,KAAK;IACjBC,OAAO,GAAG,KAAK;IACfC,UAAU,GAAGX,MAAM,CAACC,WAAW,CAAC;IAChCW,QAAQ,GAAGZ,MAAM,CAACH,QAAQ,CAAC;IAC3BgB,IAAI,GAAGb,MAAM,CAACE,UAAU,CAAC;IACzBY,qBAAqBA,CAAA,EAAG;MACpB;MACA,IAAI,IAAI,CAACL,SAAS,KAAK,KAAK,EAAE;QAC1B,IAAI,CAACI,IAAI,CAACE,aAAa,CAACC,eAAe,CAAC,WAAW,CAAC;MACxD,CAAC,MACI;QACD,IAAI,CAACH,IAAI,CAACE,aAAa,CAACE,YAAY,CAAC,WAAW,EAAE,IAAI,CAAC;MAC3D;MACA,IAAI,CAAC,IAAI,CAACP,OAAO,EAAE;QACf,IAAI,CAACQ,SAAS,CAAC,CAAC;MACpB;IACJ;IACAC,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAAC,IAAI,CAACT,OAAO,EAAE;QACf,IAAI,CAACQ,SAAS,CAAC,CAAC;MACpB;IACJ;IACAA,SAASA,CAAA,EAAG;MACR,IAAIpB,iBAAiB,CAAC,IAAI,CAACa,UAAU,CAAC,IAAI,IAAI,CAACF,SAAS,EAAE;QACtDW,UAAU,CAAC,MAAM;UACb,MAAMC,iBAAiB,GAAGd,UAAU,CAACe,oBAAoB,CAAC,IAAI,CAACT,IAAI,EAAEE,aAAa,CAAC;UACnF,IAAIM,iBAAiB,CAACE,MAAM,KAAK,CAAC,EAAE;YAChC,IAAI,CAACV,IAAI,CAACE,aAAa,CAACS,KAAK,CAAC,CAAC;UACnC;UACA,IAAIH,iBAAiB,CAACE,MAAM,GAAG,CAAC,EAAE;YAC9BF,iBAAiB,CAAC,CAAC,CAAC,CAACG,KAAK,CAAC,CAAC;UAChC;UACA,IAAI,CAACd,OAAO,GAAG,IAAI;QACvB,CAAC,CAAC;MACN;IACJ;IACA,OAAOe,IAAI,YAAAC,kBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFnB,SAAS;IAAA;IAC5G,OAAOoB,IAAI,kBAD8E7B,EAAE,CAAA8B,iBAAA;MAAAC,IAAA,EACJtB,SAAS;MAAAuB,SAAA;MAAAC,SAAA;MAAAC,MAAA;QAAAxB,SAAA,GADPV,EAAE,CAAAmC,YAAA,CAAAC,0BAAA,4BACqGhC,gBAAgB;MAAA;MAAAiC,UAAA;MAAAC,QAAA,GADvHtC,EAAE,CAAAuC,wBAAA;IAAA;EAE/F;EAAC,OA3CK9B,SAAS;AAAA;AA4Cf;EAAA,QAAA+B,SAAA,oBAAAA,SAAA;AAAA;AAYoB,IACdC,eAAe;EAArB,MAAMA,eAAe,CAAC;IAClB,OAAOf,IAAI,YAAAgB,wBAAAd,CAAA;MAAA,YAAAA,CAAA,IAAwFa,eAAe;IAAA;IAClH,OAAOE,IAAI,kBAlB8E3C,EAAE,CAAA4C,gBAAA;MAAAb,IAAA,EAkBSU;IAAe;IACnH,OAAOI,IAAI,kBAnB8E7C,EAAE,CAAA8C,gBAAA;EAoB/F;EAAC,OAJKL,eAAe;AAAA;AAKrB;EAAA,QAAAD,SAAA,oBAAAA,SAAA;AAAA;;AAQA;AACA;AACA;;AAEA,SAAS/B,SAAS,EAAEgC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}