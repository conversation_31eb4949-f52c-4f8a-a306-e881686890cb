{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { _getProvider, getApp, _registerComponent, registerVersion } from '@firebase/app';\nimport { Component } from '@firebase/component';\nimport { Deferred, ErrorFactory, isIndexedDBAvailable, uuidv4, getGlobal, base64, issuedAtTime, calculateBackoffMillis, getModularInstance } from '@firebase/util';\nimport { Logger } from '@firebase/logger';\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst APP_CHECK_STATES = new Map();\nconst DEFAULT_STATE = {\n  activated: false,\n  tokenObservers: []\n};\nconst DEBUG_STATE = {\n  initialized: false,\n  enabled: false\n};\n/**\r\n * Gets a reference to the state object.\r\n */\nfunction getStateReference(app) {\n  return APP_CHECK_STATES.get(app) || Object.assign({}, DEFAULT_STATE);\n}\n/**\r\n * Set once on initialization. The map should hold the same reference to the\r\n * same object until this entry is deleted.\r\n */\nfunction setInitialState(app, state) {\n  APP_CHECK_STATES.set(app, state);\n  return APP_CHECK_STATES.get(app);\n}\nfunction getDebugState() {\n  return DEBUG_STATE;\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst BASE_ENDPOINT = 'https://content-firebaseappcheck.googleapis.com/v1';\nconst EXCHANGE_RECAPTCHA_TOKEN_METHOD = 'exchangeRecaptchaV3Token';\nconst EXCHANGE_RECAPTCHA_ENTERPRISE_TOKEN_METHOD = 'exchangeRecaptchaEnterpriseToken';\nconst EXCHANGE_DEBUG_TOKEN_METHOD = 'exchangeDebugToken';\nconst TOKEN_REFRESH_TIME = {\n  /**\r\n   * The offset time before token natural expiration to run the refresh.\r\n   * This is currently 5 minutes.\r\n   */\n  OFFSET_DURATION: 5 * 60 * 1000,\n  /**\r\n   * This is the first retrial wait after an error. This is currently\r\n   * 30 seconds.\r\n   */\n  RETRIAL_MIN_WAIT: 30 * 1000,\n  /**\r\n   * This is the maximum retrial wait, currently 16 minutes.\r\n   */\n  RETRIAL_MAX_WAIT: 16 * 60 * 1000\n};\n/**\r\n * One day in millis, for certain error code backoffs.\r\n */\nconst ONE_DAY = 24 * 60 * 60 * 1000;\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Port from auth proactiverefresh.js\r\n *\r\n */\n// TODO: move it to @firebase/util?\n// TODO: allow to config whether refresh should happen in the background\nclass Refresher {\n  constructor(operation, retryPolicy, getWaitDuration, lowerBound, upperBound) {\n    this.operation = operation;\n    this.retryPolicy = retryPolicy;\n    this.getWaitDuration = getWaitDuration;\n    this.lowerBound = lowerBound;\n    this.upperBound = upperBound;\n    this.pending = null;\n    this.nextErrorWaitInterval = lowerBound;\n    if (lowerBound > upperBound) {\n      throw new Error('Proactive refresh lower bound greater than upper bound!');\n    }\n  }\n  start() {\n    this.nextErrorWaitInterval = this.lowerBound;\n    this.process(true).catch(() => {\n      /* we don't care about the result */\n    });\n  }\n  stop() {\n    if (this.pending) {\n      this.pending.reject('cancelled');\n      this.pending = null;\n    }\n  }\n  isRunning() {\n    return !!this.pending;\n  }\n  process(hasSucceeded) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.stop();\n      try {\n        _this.pending = new Deferred();\n        _this.pending.promise.catch(_e => {\n          /* ignore */\n        });\n        yield sleep(_this.getNextRun(hasSucceeded));\n        // Why do we resolve a promise, then immediate wait for it?\n        // We do it to make the promise chain cancellable.\n        // We can call stop() which rejects the promise before the following line execute, which makes\n        // the code jump to the catch block.\n        // TODO: unit test this\n        _this.pending.resolve();\n        yield _this.pending.promise;\n        _this.pending = new Deferred();\n        _this.pending.promise.catch(_e => {\n          /* ignore */\n        });\n        yield _this.operation();\n        _this.pending.resolve();\n        yield _this.pending.promise;\n        _this.process(true).catch(() => {\n          /* we don't care about the result */\n        });\n      } catch (error) {\n        if (_this.retryPolicy(error)) {\n          _this.process(false).catch(() => {\n            /* we don't care about the result */\n          });\n        } else {\n          _this.stop();\n        }\n      }\n    })();\n  }\n  getNextRun(hasSucceeded) {\n    if (hasSucceeded) {\n      // If last operation succeeded, reset next error wait interval and return\n      // the default wait duration.\n      this.nextErrorWaitInterval = this.lowerBound;\n      // Return typical wait duration interval after a successful operation.\n      return this.getWaitDuration();\n    } else {\n      // Get next error wait interval.\n      const currentErrorWaitInterval = this.nextErrorWaitInterval;\n      // Double interval for next consecutive error.\n      this.nextErrorWaitInterval *= 2;\n      // Make sure next wait interval does not exceed the maximum upper bound.\n      if (this.nextErrorWaitInterval > this.upperBound) {\n        this.nextErrorWaitInterval = this.upperBound;\n      }\n      return currentErrorWaitInterval;\n    }\n  }\n}\nfunction sleep(ms) {\n  return new Promise(resolve => {\n    setTimeout(resolve, ms);\n  });\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst ERRORS = {\n  [\"already-initialized\" /* AppCheckError.ALREADY_INITIALIZED */]: 'You have already called initializeAppCheck() for FirebaseApp {$appName} with ' + 'different options. To avoid this error, call initializeAppCheck() with the ' + 'same options as when it was originally called. This will return the ' + 'already initialized instance.',\n  [\"use-before-activation\" /* AppCheckError.USE_BEFORE_ACTIVATION */]: 'App Check is being used before initializeAppCheck() is called for FirebaseApp {$appName}. ' + 'Call initializeAppCheck() before instantiating other Firebase services.',\n  [\"fetch-network-error\" /* AppCheckError.FETCH_NETWORK_ERROR */]: 'Fetch failed to connect to a network. Check Internet connection. ' + 'Original error: {$originalErrorMessage}.',\n  [\"fetch-parse-error\" /* AppCheckError.FETCH_PARSE_ERROR */]: 'Fetch client could not parse response.' + ' Original error: {$originalErrorMessage}.',\n  [\"fetch-status-error\" /* AppCheckError.FETCH_STATUS_ERROR */]: 'Fetch server returned an HTTP error status. HTTP status: {$httpStatus}.',\n  [\"storage-open\" /* AppCheckError.STORAGE_OPEN */]: 'Error thrown when opening storage. Original error: {$originalErrorMessage}.',\n  [\"storage-get\" /* AppCheckError.STORAGE_GET */]: 'Error thrown when reading from storage. Original error: {$originalErrorMessage}.',\n  [\"storage-set\" /* AppCheckError.STORAGE_WRITE */]: 'Error thrown when writing to storage. Original error: {$originalErrorMessage}.',\n  [\"recaptcha-error\" /* AppCheckError.RECAPTCHA_ERROR */]: 'ReCAPTCHA error.',\n  [\"throttled\" /* AppCheckError.THROTTLED */]: `Requests throttled due to {$httpStatus} error. Attempts allowed again after {$time}`\n};\nconst ERROR_FACTORY = new ErrorFactory('appCheck', 'AppCheck', ERRORS);\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction getRecaptcha(isEnterprise = false) {\n  var _a;\n  if (isEnterprise) {\n    return (_a = self.grecaptcha) === null || _a === void 0 ? void 0 : _a.enterprise;\n  }\n  return self.grecaptcha;\n}\nfunction ensureActivated(app) {\n  if (!getStateReference(app).activated) {\n    throw ERROR_FACTORY.create(\"use-before-activation\" /* AppCheckError.USE_BEFORE_ACTIVATION */, {\n      appName: app.name\n    });\n  }\n}\nfunction getDurationString(durationInMillis) {\n  const totalSeconds = Math.round(durationInMillis / 1000);\n  const days = Math.floor(totalSeconds / (3600 * 24));\n  const hours = Math.floor((totalSeconds - days * 3600 * 24) / 3600);\n  const minutes = Math.floor((totalSeconds - days * 3600 * 24 - hours * 3600) / 60);\n  const seconds = totalSeconds - days * 3600 * 24 - hours * 3600 - minutes * 60;\n  let result = '';\n  if (days) {\n    result += pad(days) + 'd:';\n  }\n  if (hours) {\n    result += pad(hours) + 'h:';\n  }\n  result += pad(minutes) + 'm:' + pad(seconds) + 's';\n  return result;\n}\nfunction pad(value) {\n  if (value === 0) {\n    return '00';\n  }\n  return value >= 10 ? value.toString() : '0' + value;\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction exchangeToken(_x, _x2) {\n  return _exchangeToken.apply(this, arguments);\n}\nfunction _exchangeToken() {\n  _exchangeToken = _asyncToGenerator(function* ({\n    url,\n    body\n  }, heartbeatServiceProvider) {\n    const headers = {\n      'Content-Type': 'application/json'\n    };\n    // If heartbeat service exists, add heartbeat header string to the header.\n    const heartbeatService = heartbeatServiceProvider.getImmediate({\n      optional: true\n    });\n    if (heartbeatService) {\n      const heartbeatsHeader = yield heartbeatService.getHeartbeatsHeader();\n      if (heartbeatsHeader) {\n        headers['X-Firebase-Client'] = heartbeatsHeader;\n      }\n    }\n    const options = {\n      method: 'POST',\n      body: JSON.stringify(body),\n      headers\n    };\n    let response;\n    try {\n      response = yield fetch(url, options);\n    } catch (originalError) {\n      throw ERROR_FACTORY.create(\"fetch-network-error\" /* AppCheckError.FETCH_NETWORK_ERROR */, {\n        originalErrorMessage: originalError === null || originalError === void 0 ? void 0 : originalError.message\n      });\n    }\n    if (response.status !== 200) {\n      throw ERROR_FACTORY.create(\"fetch-status-error\" /* AppCheckError.FETCH_STATUS_ERROR */, {\n        httpStatus: response.status\n      });\n    }\n    let responseBody;\n    try {\n      // JSON parsing throws SyntaxError if the response body isn't a JSON string.\n      responseBody = yield response.json();\n    } catch (originalError) {\n      throw ERROR_FACTORY.create(\"fetch-parse-error\" /* AppCheckError.FETCH_PARSE_ERROR */, {\n        originalErrorMessage: originalError === null || originalError === void 0 ? void 0 : originalError.message\n      });\n    }\n    // Protobuf duration format.\n    // https://developers.google.com/protocol-buffers/docs/reference/java/com/google/protobuf/Duration\n    const match = responseBody.ttl.match(/^([\\d.]+)(s)$/);\n    if (!match || !match[2] || isNaN(Number(match[1]))) {\n      throw ERROR_FACTORY.create(\"fetch-parse-error\" /* AppCheckError.FETCH_PARSE_ERROR */, {\n        originalErrorMessage: `ttl field (timeToLive) is not in standard Protobuf Duration ` + `format: ${responseBody.ttl}`\n      });\n    }\n    const timeToLiveAsNumber = Number(match[1]) * 1000;\n    const now = Date.now();\n    return {\n      token: responseBody.token,\n      expireTimeMillis: now + timeToLiveAsNumber,\n      issuedAtTimeMillis: now\n    };\n  });\n  return _exchangeToken.apply(this, arguments);\n}\nfunction getExchangeRecaptchaV3TokenRequest(app, reCAPTCHAToken) {\n  const {\n    projectId,\n    appId,\n    apiKey\n  } = app.options;\n  return {\n    url: `${BASE_ENDPOINT}/projects/${projectId}/apps/${appId}:${EXCHANGE_RECAPTCHA_TOKEN_METHOD}?key=${apiKey}`,\n    body: {\n      'recaptcha_v3_token': reCAPTCHAToken\n    }\n  };\n}\nfunction getExchangeRecaptchaEnterpriseTokenRequest(app, reCAPTCHAToken) {\n  const {\n    projectId,\n    appId,\n    apiKey\n  } = app.options;\n  return {\n    url: `${BASE_ENDPOINT}/projects/${projectId}/apps/${appId}:${EXCHANGE_RECAPTCHA_ENTERPRISE_TOKEN_METHOD}?key=${apiKey}`,\n    body: {\n      'recaptcha_enterprise_token': reCAPTCHAToken\n    }\n  };\n}\nfunction getExchangeDebugTokenRequest(app, debugToken) {\n  const {\n    projectId,\n    appId,\n    apiKey\n  } = app.options;\n  return {\n    url: `${BASE_ENDPOINT}/projects/${projectId}/apps/${appId}:${EXCHANGE_DEBUG_TOKEN_METHOD}?key=${apiKey}`,\n    body: {\n      // eslint-disable-next-line\n      debug_token: debugToken\n    }\n  };\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst DB_NAME = 'firebase-app-check-database';\nconst DB_VERSION = 1;\nconst STORE_NAME = 'firebase-app-check-store';\nconst DEBUG_TOKEN_KEY = 'debug-token';\nlet dbPromise = null;\nfunction getDBPromise() {\n  if (dbPromise) {\n    return dbPromise;\n  }\n  dbPromise = new Promise((resolve, reject) => {\n    try {\n      const request = indexedDB.open(DB_NAME, DB_VERSION);\n      request.onsuccess = event => {\n        resolve(event.target.result);\n      };\n      request.onerror = event => {\n        var _a;\n        reject(ERROR_FACTORY.create(\"storage-open\" /* AppCheckError.STORAGE_OPEN */, {\n          originalErrorMessage: (_a = event.target.error) === null || _a === void 0 ? void 0 : _a.message\n        }));\n      };\n      request.onupgradeneeded = event => {\n        const db = event.target.result;\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (event.oldVersion) {\n          case 0:\n            db.createObjectStore(STORE_NAME, {\n              keyPath: 'compositeKey'\n            });\n        }\n      };\n    } catch (e) {\n      reject(ERROR_FACTORY.create(\"storage-open\" /* AppCheckError.STORAGE_OPEN */, {\n        originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\n      }));\n    }\n  });\n  return dbPromise;\n}\nfunction readTokenFromIndexedDB(app) {\n  return read(computeKey(app));\n}\nfunction writeTokenToIndexedDB(app, token) {\n  return write(computeKey(app), token);\n}\nfunction writeDebugTokenToIndexedDB(token) {\n  return write(DEBUG_TOKEN_KEY, token);\n}\nfunction readDebugTokenFromIndexedDB() {\n  return read(DEBUG_TOKEN_KEY);\n}\nfunction write(_x3, _x4) {\n  return _write.apply(this, arguments);\n}\nfunction _write() {\n  _write = _asyncToGenerator(function* (key, value) {\n    const db = yield getDBPromise();\n    const transaction = db.transaction(STORE_NAME, 'readwrite');\n    const store = transaction.objectStore(STORE_NAME);\n    const request = store.put({\n      compositeKey: key,\n      value\n    });\n    return new Promise((resolve, reject) => {\n      request.onsuccess = _event => {\n        resolve();\n      };\n      transaction.onerror = event => {\n        var _a;\n        reject(ERROR_FACTORY.create(\"storage-set\" /* AppCheckError.STORAGE_WRITE */, {\n          originalErrorMessage: (_a = event.target.error) === null || _a === void 0 ? void 0 : _a.message\n        }));\n      };\n    });\n  });\n  return _write.apply(this, arguments);\n}\nfunction read(_x5) {\n  return _read.apply(this, arguments);\n}\nfunction _read() {\n  _read = _asyncToGenerator(function* (key) {\n    const db = yield getDBPromise();\n    const transaction = db.transaction(STORE_NAME, 'readonly');\n    const store = transaction.objectStore(STORE_NAME);\n    const request = store.get(key);\n    return new Promise((resolve, reject) => {\n      request.onsuccess = event => {\n        const result = event.target.result;\n        if (result) {\n          resolve(result.value);\n        } else {\n          resolve(undefined);\n        }\n      };\n      transaction.onerror = event => {\n        var _a;\n        reject(ERROR_FACTORY.create(\"storage-get\" /* AppCheckError.STORAGE_GET */, {\n          originalErrorMessage: (_a = event.target.error) === null || _a === void 0 ? void 0 : _a.message\n        }));\n      };\n    });\n  });\n  return _read.apply(this, arguments);\n}\nfunction computeKey(app) {\n  return `${app.options.appId}-${app.name}`;\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst logger = new Logger('@firebase/app-check');\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Always resolves. In case of an error reading from indexeddb, resolve with undefined\r\n */\nfunction readTokenFromStorage(_x6) {\n  return _readTokenFromStorage.apply(this, arguments);\n}\n/**\r\n * Always resolves. In case of an error writing to indexeddb, print a warning and resolve the promise\r\n */\nfunction _readTokenFromStorage() {\n  _readTokenFromStorage = _asyncToGenerator(function* (app) {\n    if (isIndexedDBAvailable()) {\n      let token = undefined;\n      try {\n        token = yield readTokenFromIndexedDB(app);\n      } catch (e) {\n        // swallow the error and return undefined\n        logger.warn(`Failed to read token from IndexedDB. Error: ${e}`);\n      }\n      return token;\n    }\n    return undefined;\n  });\n  return _readTokenFromStorage.apply(this, arguments);\n}\nfunction writeTokenToStorage(app, token) {\n  if (isIndexedDBAvailable()) {\n    return writeTokenToIndexedDB(app, token).catch(e => {\n      // swallow the error and resolve the promise\n      logger.warn(`Failed to write token to IndexedDB. Error: ${e}`);\n    });\n  }\n  return Promise.resolve();\n}\nfunction readOrCreateDebugTokenFromStorage() {\n  return _readOrCreateDebugTokenFromStorage.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction _readOrCreateDebugTokenFromStorage() {\n  _readOrCreateDebugTokenFromStorage = _asyncToGenerator(function* () {\n    /**\r\n     * Theoretically race condition can happen if we read, then write in 2 separate transactions.\r\n     * But it won't happen here, because this function will be called exactly once.\r\n     */\n    let existingDebugToken = undefined;\n    try {\n      existingDebugToken = yield readDebugTokenFromIndexedDB();\n    } catch (_e) {\n      // failed to read from indexeddb. We assume there is no existing debug token, and generate a new one.\n    }\n    if (!existingDebugToken) {\n      // create a new debug token\n      const newToken = uuidv4();\n      // We don't need to block on writing to indexeddb\n      // In case persistence failed, a new debug token will be generated everytime the page is refreshed.\n      // It renders the debug token useless because you have to manually register(whitelist) the new token in the firebase console again and again.\n      // If you see this error trying to use debug token, it probably means you are using a browser that doesn't support indexeddb.\n      // You should switch to a different browser that supports indexeddb\n      writeDebugTokenToIndexedDB(newToken).catch(e => logger.warn(`Failed to persist debug token to IndexedDB. Error: ${e}`));\n      return newToken;\n    } else {\n      return existingDebugToken;\n    }\n  });\n  return _readOrCreateDebugTokenFromStorage.apply(this, arguments);\n}\nfunction isDebugMode() {\n  const debugState = getDebugState();\n  return debugState.enabled;\n}\nfunction getDebugToken() {\n  return _getDebugToken.apply(this, arguments);\n}\nfunction _getDebugToken() {\n  _getDebugToken = _asyncToGenerator(function* () {\n    const state = getDebugState();\n    if (state.enabled && state.token) {\n      return state.token.promise;\n    } else {\n      // should not happen!\n      throw Error(`\n            Can't get debug token in production mode.\n        `);\n    }\n  });\n  return _getDebugToken.apply(this, arguments);\n}\nfunction initializeDebugMode() {\n  const globals = getGlobal();\n  const debugState = getDebugState();\n  // Set to true if this function has been called, whether or not\n  // it enabled debug mode.\n  debugState.initialized = true;\n  if (typeof globals.FIREBASE_APPCHECK_DEBUG_TOKEN !== 'string' && globals.FIREBASE_APPCHECK_DEBUG_TOKEN !== true) {\n    return;\n  }\n  debugState.enabled = true;\n  const deferredToken = new Deferred();\n  debugState.token = deferredToken;\n  if (typeof globals.FIREBASE_APPCHECK_DEBUG_TOKEN === 'string') {\n    deferredToken.resolve(globals.FIREBASE_APPCHECK_DEBUG_TOKEN);\n  } else {\n    deferredToken.resolve(readOrCreateDebugTokenFromStorage());\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n// Initial hardcoded value agreed upon across platforms for initial launch.\n// Format left open for possible dynamic error values and other fields in the future.\nconst defaultTokenErrorData = {\n  error: 'UNKNOWN_ERROR'\n};\n/**\r\n * Stringify and base64 encode token error data.\r\n *\r\n * @param tokenError Error data, currently hardcoded.\r\n */\nfunction formatDummyToken(tokenErrorData) {\n  return base64.encodeString(JSON.stringify(tokenErrorData), /* webSafe= */false);\n}\n/**\r\n * This function always resolves.\r\n * The result will contain an error field if there is any error.\r\n * In case there is an error, the token field in the result will be populated with a dummy value\r\n */\nfunction getToken$2(_x7) {\n  return _getToken$.apply(this, arguments);\n}\n/**\r\n * Internal API for limited use tokens. Skips all FAC state and simply calls\r\n * the underlying provider.\r\n */\nfunction _getToken$() {\n  _getToken$ = _asyncToGenerator(function* (appCheck, forceRefresh = false) {\n    const app = appCheck.app;\n    ensureActivated(app);\n    const state = getStateReference(app);\n    /**\r\n     * First check if there is a token in memory from a previous `getToken()` call.\r\n     */\n    let token = state.token;\n    let error = undefined;\n    /**\r\n     * If an invalid token was found in memory, clear token from\r\n     * memory and unset the local variable `token`.\r\n     */\n    if (token && !isValid(token)) {\n      state.token = undefined;\n      token = undefined;\n    }\n    /**\r\n     * If there is no valid token in memory, try to load token from indexedDB.\r\n     */\n    if (!token) {\n      // cachedTokenPromise contains the token found in IndexedDB or undefined if not found.\n      const cachedToken = yield state.cachedTokenPromise;\n      if (cachedToken) {\n        if (isValid(cachedToken)) {\n          token = cachedToken;\n        } else {\n          // If there was an invalid token in the indexedDB cache, clear it.\n          yield writeTokenToStorage(app, undefined);\n        }\n      }\n    }\n    // Return the cached token (from either memory or indexedDB) if it's valid\n    if (!forceRefresh && token && isValid(token)) {\n      return {\n        token: token.token\n      };\n    }\n    // Only set to true if this `getToken()` call is making the actual\n    // REST call to the exchange endpoint, versus waiting for an already\n    // in-flight call (see debug and regular exchange endpoint paths below)\n    let shouldCallListeners = false;\n    /**\r\n     * DEBUG MODE\r\n     * If debug mode is set, and there is no cached token, fetch a new App\r\n     * Check token using the debug token, and return it directly.\r\n     */\n    if (isDebugMode()) {\n      // Avoid making another call to the exchange endpoint if one is in flight.\n      if (!state.exchangeTokenPromise) {\n        state.exchangeTokenPromise = exchangeToken(getExchangeDebugTokenRequest(app, yield getDebugToken()), appCheck.heartbeatServiceProvider).finally(() => {\n          // Clear promise when settled - either resolved or rejected.\n          state.exchangeTokenPromise = undefined;\n        });\n        shouldCallListeners = true;\n      }\n      const tokenFromDebugExchange = yield state.exchangeTokenPromise;\n      // Write debug token to indexedDB.\n      yield writeTokenToStorage(app, tokenFromDebugExchange);\n      // Write debug token to state.\n      state.token = tokenFromDebugExchange;\n      return {\n        token: tokenFromDebugExchange.token\n      };\n    }\n    /**\r\n     * There are no valid tokens in memory or indexedDB and we are not in\r\n     * debug mode.\r\n     * Request a new token from the exchange endpoint.\r\n     */\n    try {\n      // Avoid making another call to the exchange endpoint if one is in flight.\n      if (!state.exchangeTokenPromise) {\n        // state.provider is populated in initializeAppCheck()\n        // ensureActivated() at the top of this function checks that\n        // initializeAppCheck() has been called.\n        state.exchangeTokenPromise = state.provider.getToken().finally(() => {\n          // Clear promise when settled - either resolved or rejected.\n          state.exchangeTokenPromise = undefined;\n        });\n        shouldCallListeners = true;\n      }\n      token = yield getStateReference(app).exchangeTokenPromise;\n    } catch (e) {\n      if (e.code === `appCheck/${\"throttled\" /* AppCheckError.THROTTLED */}`) {\n        // Warn if throttled, but do not treat it as an error.\n        logger.warn(e.message);\n      } else {\n        // `getToken()` should never throw, but logging error text to console will aid debugging.\n        logger.error(e);\n      }\n      // Always save error to be added to dummy token.\n      error = e;\n    }\n    let interopTokenResult;\n    if (!token) {\n      // If token is undefined, there must be an error.\n      // Return a dummy token along with the error.\n      interopTokenResult = makeDummyTokenResult(error);\n    } else if (error) {\n      if (isValid(token)) {\n        // It's also possible a valid token exists, but there's also an error.\n        // (Such as if the token is almost expired, tries to refresh, and\n        // the exchange request fails.)\n        // We add a special error property here so that the refresher will\n        // count this as a failed attempt and use the backoff instead of\n        // retrying repeatedly with no delay, but any 3P listeners will not\n        // be hindered in getting the still-valid token.\n        interopTokenResult = {\n          token: token.token,\n          internalError: error\n        };\n      } else {\n        // No invalid tokens should make it to this step. Memory and cached tokens\n        // are checked. Other tokens are from fresh exchanges. But just in case.\n        interopTokenResult = makeDummyTokenResult(error);\n      }\n    } else {\n      interopTokenResult = {\n        token: token.token\n      };\n      // write the new token to the memory state as well as the persistent storage.\n      // Only do it if we got a valid new token\n      state.token = token;\n      yield writeTokenToStorage(app, token);\n    }\n    if (shouldCallListeners) {\n      notifyTokenListeners(app, interopTokenResult);\n    }\n    return interopTokenResult;\n  });\n  return _getToken$.apply(this, arguments);\n}\nfunction getLimitedUseToken$1(_x8) {\n  return _getLimitedUseToken$.apply(this, arguments);\n}\nfunction _getLimitedUseToken$() {\n  _getLimitedUseToken$ = _asyncToGenerator(function* (appCheck) {\n    const app = appCheck.app;\n    ensureActivated(app);\n    const {\n      provider\n    } = getStateReference(app);\n    if (isDebugMode()) {\n      const debugToken = yield getDebugToken();\n      const {\n        token\n      } = yield exchangeToken(getExchangeDebugTokenRequest(app, debugToken), appCheck.heartbeatServiceProvider);\n      return {\n        token\n      };\n    } else {\n      // provider is definitely valid since we ensure AppCheck was activated\n      const {\n        token\n      } = yield provider.getToken();\n      return {\n        token\n      };\n    }\n  });\n  return _getLimitedUseToken$.apply(this, arguments);\n}\nfunction addTokenListener(appCheck, type, listener, onError) {\n  const {\n    app\n  } = appCheck;\n  const state = getStateReference(app);\n  const tokenObserver = {\n    next: listener,\n    error: onError,\n    type\n  };\n  state.tokenObservers = [...state.tokenObservers, tokenObserver];\n  // Invoke the listener async immediately if there is a valid token\n  // in memory.\n  if (state.token && isValid(state.token)) {\n    const validToken = state.token;\n    Promise.resolve().then(() => {\n      listener({\n        token: validToken.token\n      });\n      initTokenRefresher(appCheck);\n    }).catch(() => {\n      /* we don't care about exceptions thrown in listeners */\n    });\n  }\n  /**\r\n   * Wait for any cached token promise to resolve before starting the token\r\n   * refresher. The refresher checks to see if there is an existing token\r\n   * in state and calls the exchange endpoint if not. We should first let the\r\n   * IndexedDB check have a chance to populate state if it can.\r\n   *\r\n   * Listener call isn't needed here because cachedTokenPromise will call any\r\n   * listeners that exist when it resolves.\r\n   */\n  // state.cachedTokenPromise is always populated in `activate()`.\n  void state.cachedTokenPromise.then(() => initTokenRefresher(appCheck));\n}\nfunction removeTokenListener(app, listener) {\n  const state = getStateReference(app);\n  const newObservers = state.tokenObservers.filter(tokenObserver => tokenObserver.next !== listener);\n  if (newObservers.length === 0 && state.tokenRefresher && state.tokenRefresher.isRunning()) {\n    state.tokenRefresher.stop();\n  }\n  state.tokenObservers = newObservers;\n}\n/**\r\n * Logic to create and start refresher as needed.\r\n */\nfunction initTokenRefresher(appCheck) {\n  const {\n    app\n  } = appCheck;\n  const state = getStateReference(app);\n  // Create the refresher but don't start it if `isTokenAutoRefreshEnabled`\n  // is not true.\n  let refresher = state.tokenRefresher;\n  if (!refresher) {\n    refresher = createTokenRefresher(appCheck);\n    state.tokenRefresher = refresher;\n  }\n  if (!refresher.isRunning() && state.isTokenAutoRefreshEnabled) {\n    refresher.start();\n  }\n}\nfunction createTokenRefresher(appCheck) {\n  const {\n    app\n  } = appCheck;\n  return new Refresher(\n  /*#__PURE__*/\n  // Keep in mind when this fails for any reason other than the ones\n  // for which we should retry, it will effectively stop the proactive refresh.\n  _asyncToGenerator(function* () {\n    const state = getStateReference(app);\n    // If there is no token, we will try to load it from storage and use it\n    // If there is a token, we force refresh it because we know it's going to expire soon\n    let result;\n    if (!state.token) {\n      result = yield getToken$2(appCheck);\n    } else {\n      result = yield getToken$2(appCheck, true);\n    }\n    /**\r\n     * getToken() always resolves. In case the result has an error field defined, it means\r\n     * the operation failed, and we should retry.\r\n     */\n    if (result.error) {\n      throw result.error;\n    }\n    /**\r\n     * A special `internalError` field reflects that there was an error\r\n     * getting a new token from the exchange endpoint, but there's still a\r\n     * previous token that's valid for now and this should be passed to 2P/3P\r\n     * requests for a token. But we want this callback (`this.operation` in\r\n     * `Refresher`) to throw in order to kick off the Refresher's retry\r\n     * backoff. (Setting `hasSucceeded` to false.)\r\n     */\n    if (result.internalError) {\n      throw result.internalError;\n    }\n  }), () => {\n    return true;\n  }, () => {\n    const state = getStateReference(app);\n    if (state.token) {\n      // issuedAtTime + (50% * total TTL) + 5 minutes\n      let nextRefreshTimeMillis = state.token.issuedAtTimeMillis + (state.token.expireTimeMillis - state.token.issuedAtTimeMillis) * 0.5 + 5 * 60 * 1000;\n      // Do not allow refresh time to be past (expireTime - 5 minutes)\n      const latestAllowableRefresh = state.token.expireTimeMillis - 5 * 60 * 1000;\n      nextRefreshTimeMillis = Math.min(nextRefreshTimeMillis, latestAllowableRefresh);\n      return Math.max(0, nextRefreshTimeMillis - Date.now());\n    } else {\n      return 0;\n    }\n  }, TOKEN_REFRESH_TIME.RETRIAL_MIN_WAIT, TOKEN_REFRESH_TIME.RETRIAL_MAX_WAIT);\n}\nfunction notifyTokenListeners(app, token) {\n  const observers = getStateReference(app).tokenObservers;\n  for (const observer of observers) {\n    try {\n      if (observer.type === \"EXTERNAL\" /* ListenerType.EXTERNAL */ && token.error != null) {\n        // If this listener was added by a 3P call, send any token error to\n        // the supplied error handler. A 3P observer always has an error\n        // handler.\n        observer.error(token.error);\n      } else {\n        // If the token has no error field, always return the token.\n        // If this is a 2P listener, return the token, whether or not it\n        // has an error field.\n        observer.next(token);\n      }\n    } catch (e) {\n      // Errors in the listener function itself are always ignored.\n    }\n  }\n}\nfunction isValid(token) {\n  return token.expireTimeMillis - Date.now() > 0;\n}\nfunction makeDummyTokenResult(error) {\n  return {\n    token: formatDummyToken(defaultTokenErrorData),\n    error\n  };\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * AppCheck Service class.\r\n */\nclass AppCheckService {\n  constructor(app, heartbeatServiceProvider) {\n    this.app = app;\n    this.heartbeatServiceProvider = heartbeatServiceProvider;\n  }\n  _delete() {\n    const {\n      tokenObservers\n    } = getStateReference(this.app);\n    for (const tokenObserver of tokenObservers) {\n      removeTokenListener(this.app, tokenObserver.next);\n    }\n    return Promise.resolve();\n  }\n}\nfunction factory(app, heartbeatServiceProvider) {\n  return new AppCheckService(app, heartbeatServiceProvider);\n}\nfunction internalFactory(appCheck) {\n  return {\n    getToken: forceRefresh => getToken$2(appCheck, forceRefresh),\n    getLimitedUseToken: () => getLimitedUseToken$1(appCheck),\n    addTokenListener: listener => addTokenListener(appCheck, \"INTERNAL\" /* ListenerType.INTERNAL */, listener),\n    removeTokenListener: listener => removeTokenListener(appCheck.app, listener)\n  };\n}\nconst name = \"@firebase/app-check\";\nconst version = \"0.8.4\";\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst RECAPTCHA_URL = 'https://www.google.com/recaptcha/api.js';\nconst RECAPTCHA_ENTERPRISE_URL = 'https://www.google.com/recaptcha/enterprise.js';\nfunction initializeV3(app, siteKey) {\n  const initialized = new Deferred();\n  const state = getStateReference(app);\n  state.reCAPTCHAState = {\n    initialized\n  };\n  const divId = makeDiv(app);\n  const grecaptcha = getRecaptcha(false);\n  if (!grecaptcha) {\n    loadReCAPTCHAV3Script(() => {\n      const grecaptcha = getRecaptcha(false);\n      if (!grecaptcha) {\n        // it shouldn't happen.\n        throw new Error('no recaptcha');\n      }\n      queueWidgetRender(app, siteKey, grecaptcha, divId, initialized);\n    });\n  } else {\n    queueWidgetRender(app, siteKey, grecaptcha, divId, initialized);\n  }\n  return initialized.promise;\n}\nfunction initializeEnterprise(app, siteKey) {\n  const initialized = new Deferred();\n  const state = getStateReference(app);\n  state.reCAPTCHAState = {\n    initialized\n  };\n  const divId = makeDiv(app);\n  const grecaptcha = getRecaptcha(true);\n  if (!grecaptcha) {\n    loadReCAPTCHAEnterpriseScript(() => {\n      const grecaptcha = getRecaptcha(true);\n      if (!grecaptcha) {\n        // it shouldn't happen.\n        throw new Error('no recaptcha');\n      }\n      queueWidgetRender(app, siteKey, grecaptcha, divId, initialized);\n    });\n  } else {\n    queueWidgetRender(app, siteKey, grecaptcha, divId, initialized);\n  }\n  return initialized.promise;\n}\n/**\r\n * Add listener to render the widget and resolve the promise when\r\n * the grecaptcha.ready() event fires.\r\n */\nfunction queueWidgetRender(app, siteKey, grecaptcha, container, initialized) {\n  grecaptcha.ready(() => {\n    // Invisible widgets allow us to set a different siteKey for each widget,\n    // so we use them to support multiple apps\n    renderInvisibleWidget(app, siteKey, grecaptcha, container);\n    initialized.resolve(grecaptcha);\n  });\n}\n/**\r\n * Add invisible div to page.\r\n */\nfunction makeDiv(app) {\n  const divId = `fire_app_check_${app.name}`;\n  const invisibleDiv = document.createElement('div');\n  invisibleDiv.id = divId;\n  invisibleDiv.style.display = 'none';\n  document.body.appendChild(invisibleDiv);\n  return divId;\n}\nfunction getToken$1(_x9) {\n  return _getToken$2.apply(this, arguments);\n}\n/**\r\n *\r\n * @param app\r\n * @param container - Id of a HTML element.\r\n */\nfunction _getToken$2() {\n  _getToken$2 = _asyncToGenerator(function* (app) {\n    ensureActivated(app);\n    // ensureActivated() guarantees that reCAPTCHAState is set\n    const reCAPTCHAState = getStateReference(app).reCAPTCHAState;\n    const recaptcha = yield reCAPTCHAState.initialized.promise;\n    return new Promise((resolve, _reject) => {\n      // Updated after initialization is complete.\n      const reCAPTCHAState = getStateReference(app).reCAPTCHAState;\n      recaptcha.ready(() => {\n        resolve(\n        // widgetId is guaranteed to be available if reCAPTCHAState.initialized.promise resolved.\n        recaptcha.execute(reCAPTCHAState.widgetId, {\n          action: 'fire_app_check'\n        }));\n      });\n    });\n  });\n  return _getToken$2.apply(this, arguments);\n}\nfunction renderInvisibleWidget(app, siteKey, grecaptcha, container) {\n  const widgetId = grecaptcha.render(container, {\n    sitekey: siteKey,\n    size: 'invisible',\n    // Success callback - set state\n    callback: () => {\n      getStateReference(app).reCAPTCHAState.succeeded = true;\n    },\n    // Failure callback - set state\n    'error-callback': () => {\n      getStateReference(app).reCAPTCHAState.succeeded = false;\n    }\n  });\n  const state = getStateReference(app);\n  state.reCAPTCHAState = Object.assign(Object.assign({}, state.reCAPTCHAState), {\n    // state.reCAPTCHAState is set in the initialize()\n    widgetId\n  });\n}\nfunction loadReCAPTCHAV3Script(onload) {\n  const script = document.createElement('script');\n  script.src = RECAPTCHA_URL;\n  script.onload = onload;\n  document.head.appendChild(script);\n}\nfunction loadReCAPTCHAEnterpriseScript(onload) {\n  const script = document.createElement('script');\n  script.src = RECAPTCHA_ENTERPRISE_URL;\n  script.onload = onload;\n  document.head.appendChild(script);\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * App Check provider that can obtain a reCAPTCHA V3 token and exchange it\r\n * for an App Check token.\r\n *\r\n * @public\r\n */\nclass ReCaptchaV3Provider {\n  /**\r\n   * Create a ReCaptchaV3Provider instance.\r\n   * @param siteKey - ReCAPTCHA V3 siteKey.\r\n   */\n  constructor(_siteKey) {\n    this._siteKey = _siteKey;\n    /**\r\n     * Throttle requests on certain error codes to prevent too many retries\r\n     * in a short time.\r\n     */\n    this._throttleData = null;\n  }\n  /**\r\n   * Returns an App Check token.\r\n   * @internal\r\n   */\n  getToken() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      var _a, _b, _c;\n      throwIfThrottled(_this2._throttleData);\n      // Top-level `getToken()` has already checked that App Check is initialized\n      // and therefore this._app and this._heartbeatServiceProvider are available.\n      const attestedClaimsToken = yield getToken$1(_this2._app).catch(_e => {\n        // reCaptcha.execute() throws null which is not very descriptive.\n        throw ERROR_FACTORY.create(\"recaptcha-error\" /* AppCheckError.RECAPTCHA_ERROR */);\n      });\n      // Check if a failure state was set by the recaptcha \"error-callback\".\n      if (!((_a = getStateReference(_this2._app).reCAPTCHAState) === null || _a === void 0 ? void 0 : _a.succeeded)) {\n        throw ERROR_FACTORY.create(\"recaptcha-error\" /* AppCheckError.RECAPTCHA_ERROR */);\n      }\n      let result;\n      try {\n        result = yield exchangeToken(getExchangeRecaptchaV3TokenRequest(_this2._app, attestedClaimsToken), _this2._heartbeatServiceProvider);\n      } catch (e) {\n        if ((_b = e.code) === null || _b === void 0 ? void 0 : _b.includes(\"fetch-status-error\" /* AppCheckError.FETCH_STATUS_ERROR */)) {\n          _this2._throttleData = setBackoff(Number((_c = e.customData) === null || _c === void 0 ? void 0 : _c.httpStatus), _this2._throttleData);\n          throw ERROR_FACTORY.create(\"throttled\" /* AppCheckError.THROTTLED */, {\n            time: getDurationString(_this2._throttleData.allowRequestsAfter - Date.now()),\n            httpStatus: _this2._throttleData.httpStatus\n          });\n        } else {\n          throw e;\n        }\n      }\n      // If successful, clear throttle data.\n      _this2._throttleData = null;\n      return result;\n    })();\n  }\n  /**\r\n   * @internal\r\n   */\n  initialize(app) {\n    this._app = app;\n    this._heartbeatServiceProvider = _getProvider(app, 'heartbeat');\n    initializeV3(app, this._siteKey).catch(() => {\n      /* we don't care about the initialization result */\n    });\n  }\n  /**\r\n   * @internal\r\n   */\n  isEqual(otherProvider) {\n    if (otherProvider instanceof ReCaptchaV3Provider) {\n      return this._siteKey === otherProvider._siteKey;\n    } else {\n      return false;\n    }\n  }\n}\n/**\r\n * App Check provider that can obtain a reCAPTCHA Enterprise token and exchange it\r\n * for an App Check token.\r\n *\r\n * @public\r\n */\nclass ReCaptchaEnterpriseProvider {\n  /**\r\n   * Create a ReCaptchaEnterpriseProvider instance.\r\n   * @param siteKey - reCAPTCHA Enterprise score-based site key.\r\n   */\n  constructor(_siteKey) {\n    this._siteKey = _siteKey;\n    /**\r\n     * Throttle requests on certain error codes to prevent too many retries\r\n     * in a short time.\r\n     */\n    this._throttleData = null;\n  }\n  /**\r\n   * Returns an App Check token.\r\n   * @internal\r\n   */\n  getToken() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      var _a, _b, _c;\n      throwIfThrottled(_this3._throttleData);\n      // Top-level `getToken()` has already checked that App Check is initialized\n      // and therefore this._app and this._heartbeatServiceProvider are available.\n      const attestedClaimsToken = yield getToken$1(_this3._app).catch(_e => {\n        // reCaptcha.execute() throws null which is not very descriptive.\n        throw ERROR_FACTORY.create(\"recaptcha-error\" /* AppCheckError.RECAPTCHA_ERROR */);\n      });\n      // Check if a failure state was set by the recaptcha \"error-callback\".\n      if (!((_a = getStateReference(_this3._app).reCAPTCHAState) === null || _a === void 0 ? void 0 : _a.succeeded)) {\n        throw ERROR_FACTORY.create(\"recaptcha-error\" /* AppCheckError.RECAPTCHA_ERROR */);\n      }\n      let result;\n      try {\n        result = yield exchangeToken(getExchangeRecaptchaEnterpriseTokenRequest(_this3._app, attestedClaimsToken), _this3._heartbeatServiceProvider);\n      } catch (e) {\n        if ((_b = e.code) === null || _b === void 0 ? void 0 : _b.includes(\"fetch-status-error\" /* AppCheckError.FETCH_STATUS_ERROR */)) {\n          _this3._throttleData = setBackoff(Number((_c = e.customData) === null || _c === void 0 ? void 0 : _c.httpStatus), _this3._throttleData);\n          throw ERROR_FACTORY.create(\"throttled\" /* AppCheckError.THROTTLED */, {\n            time: getDurationString(_this3._throttleData.allowRequestsAfter - Date.now()),\n            httpStatus: _this3._throttleData.httpStatus\n          });\n        } else {\n          throw e;\n        }\n      }\n      // If successful, clear throttle data.\n      _this3._throttleData = null;\n      return result;\n    })();\n  }\n  /**\r\n   * @internal\r\n   */\n  initialize(app) {\n    this._app = app;\n    this._heartbeatServiceProvider = _getProvider(app, 'heartbeat');\n    initializeEnterprise(app, this._siteKey).catch(() => {\n      /* we don't care about the initialization result */\n    });\n  }\n  /**\r\n   * @internal\r\n   */\n  isEqual(otherProvider) {\n    if (otherProvider instanceof ReCaptchaEnterpriseProvider) {\n      return this._siteKey === otherProvider._siteKey;\n    } else {\n      return false;\n    }\n  }\n}\n/**\r\n * Custom provider class.\r\n * @public\r\n */\nclass CustomProvider {\n  constructor(_customProviderOptions) {\n    this._customProviderOptions = _customProviderOptions;\n  }\n  /**\r\n   * @internal\r\n   */\n  getToken() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      // custom provider\n      const customToken = yield _this4._customProviderOptions.getToken();\n      // Try to extract IAT from custom token, in case this token is not\n      // being newly issued. JWT timestamps are in seconds since epoch.\n      const issuedAtTimeSeconds = issuedAtTime(customToken.token);\n      // Very basic validation, use current timestamp as IAT if JWT\n      // has no `iat` field or value is out of bounds.\n      const issuedAtTimeMillis = issuedAtTimeSeconds !== null && issuedAtTimeSeconds < Date.now() && issuedAtTimeSeconds > 0 ? issuedAtTimeSeconds * 1000 : Date.now();\n      return Object.assign(Object.assign({}, customToken), {\n        issuedAtTimeMillis\n      });\n    })();\n  }\n  /**\r\n   * @internal\r\n   */\n  initialize(app) {\n    this._app = app;\n  }\n  /**\r\n   * @internal\r\n   */\n  isEqual(otherProvider) {\n    if (otherProvider instanceof CustomProvider) {\n      return this._customProviderOptions.getToken.toString() === otherProvider._customProviderOptions.getToken.toString();\n    } else {\n      return false;\n    }\n  }\n}\n/**\r\n * Set throttle data to block requests until after a certain time\r\n * depending on the failed request's status code.\r\n * @param httpStatus - Status code of failed request.\r\n * @param throttleData - `ThrottleData` object containing previous throttle\r\n * data state.\r\n * @returns Data about current throttle state and expiration time.\r\n */\nfunction setBackoff(httpStatus, throttleData) {\n  /**\r\n   * Block retries for 1 day for the following error codes:\r\n   *\r\n   * 404: Likely malformed URL.\r\n   *\r\n   * 403:\r\n   * - Attestation failed\r\n   * - Wrong API key\r\n   * - Project deleted\r\n   */\n  if (httpStatus === 404 || httpStatus === 403) {\n    return {\n      backoffCount: 1,\n      allowRequestsAfter: Date.now() + ONE_DAY,\n      httpStatus\n    };\n  } else {\n    /**\r\n     * For all other error codes, the time when it is ok to retry again\r\n     * is based on exponential backoff.\r\n     */\n    const backoffCount = throttleData ? throttleData.backoffCount : 0;\n    const backoffMillis = calculateBackoffMillis(backoffCount, 1000, 2);\n    return {\n      backoffCount: backoffCount + 1,\n      allowRequestsAfter: Date.now() + backoffMillis,\n      httpStatus\n    };\n  }\n}\nfunction throwIfThrottled(throttleData) {\n  if (throttleData) {\n    if (Date.now() - throttleData.allowRequestsAfter <= 0) {\n      // If before, throw.\n      throw ERROR_FACTORY.create(\"throttled\" /* AppCheckError.THROTTLED */, {\n        time: getDurationString(throttleData.allowRequestsAfter - Date.now()),\n        httpStatus: throttleData.httpStatus\n      });\n    }\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Activate App Check for the given app. Can be called only once per app.\r\n * @param app - the {@link @firebase/app#FirebaseApp} to activate App Check for\r\n * @param options - App Check initialization options\r\n * @public\r\n */\nfunction initializeAppCheck(app = getApp(), options) {\n  app = getModularInstance(app);\n  const provider = _getProvider(app, 'app-check');\n  // Ensure initializeDebugMode() is only called once.\n  if (!getDebugState().initialized) {\n    initializeDebugMode();\n  }\n  // Log a message containing the debug token when `initializeAppCheck()`\n  // is called in debug mode.\n  if (isDebugMode()) {\n    // Do not block initialization to get the token for the message.\n    void getDebugToken().then(token =>\n    // Not using logger because I don't think we ever want this accidentally hidden.\n    console.log(`App Check debug token: ${token}. You will need to add it to your app's App Check settings in the Firebase console for it to work.`));\n  }\n  if (provider.isInitialized()) {\n    const existingInstance = provider.getImmediate();\n    const initialOptions = provider.getOptions();\n    if (initialOptions.isTokenAutoRefreshEnabled === options.isTokenAutoRefreshEnabled && initialOptions.provider.isEqual(options.provider)) {\n      return existingInstance;\n    } else {\n      throw ERROR_FACTORY.create(\"already-initialized\" /* AppCheckError.ALREADY_INITIALIZED */, {\n        appName: app.name\n      });\n    }\n  }\n  const appCheck = provider.initialize({\n    options\n  });\n  _activate(app, options.provider, options.isTokenAutoRefreshEnabled);\n  // If isTokenAutoRefreshEnabled is false, do not send any requests to the\n  // exchange endpoint without an explicit call from the user either directly\n  // or through another Firebase library (storage, functions, etc.)\n  if (getStateReference(app).isTokenAutoRefreshEnabled) {\n    // Adding a listener will start the refresher and fetch a token if needed.\n    // This gets a token ready and prevents a delay when an internal library\n    // requests the token.\n    // Listener function does not need to do anything, its base functionality\n    // of calling getToken() already fetches token and writes it to memory/storage.\n    addTokenListener(appCheck, \"INTERNAL\" /* ListenerType.INTERNAL */, () => {});\n  }\n  return appCheck;\n}\n/**\r\n * Activate App Check\r\n * @param app - Firebase app to activate App Check for.\r\n * @param provider - reCAPTCHA v3 provider or\r\n * custom token provider.\r\n * @param isTokenAutoRefreshEnabled - If true, the SDK automatically\r\n * refreshes App Check tokens as needed. If undefined, defaults to the\r\n * value of `app.automaticDataCollectionEnabled`, which defaults to\r\n * false and can be set in the app config.\r\n */\nfunction _activate(app, provider, isTokenAutoRefreshEnabled) {\n  // Create an entry in the APP_CHECK_STATES map. Further changes should\n  // directly mutate this object.\n  const state = setInitialState(app, Object.assign({}, DEFAULT_STATE));\n  state.activated = true;\n  state.provider = provider; // Read cached token from storage if it exists and store it in memory.\n  state.cachedTokenPromise = readTokenFromStorage(app).then(cachedToken => {\n    if (cachedToken && isValid(cachedToken)) {\n      state.token = cachedToken;\n      // notify all listeners with the cached token\n      notifyTokenListeners(app, {\n        token: cachedToken.token\n      });\n    }\n    return cachedToken;\n  });\n  // Use value of global `automaticDataCollectionEnabled` (which\n  // itself defaults to false if not specified in config) if\n  // `isTokenAutoRefreshEnabled` param was not provided by user.\n  state.isTokenAutoRefreshEnabled = isTokenAutoRefreshEnabled === undefined ? app.automaticDataCollectionEnabled : isTokenAutoRefreshEnabled;\n  state.provider.initialize(app);\n}\n/**\r\n * Set whether App Check will automatically refresh tokens as needed.\r\n *\r\n * @param appCheckInstance - The App Check service instance.\r\n * @param isTokenAutoRefreshEnabled - If true, the SDK automatically\r\n * refreshes App Check tokens as needed. This overrides any value set\r\n * during `initializeAppCheck()`.\r\n * @public\r\n */\nfunction setTokenAutoRefreshEnabled(appCheckInstance, isTokenAutoRefreshEnabled) {\n  const app = appCheckInstance.app;\n  const state = getStateReference(app);\n  // This will exist if any product libraries have called\n  // `addTokenListener()`\n  if (state.tokenRefresher) {\n    if (isTokenAutoRefreshEnabled === true) {\n      state.tokenRefresher.start();\n    } else {\n      state.tokenRefresher.stop();\n    }\n  }\n  state.isTokenAutoRefreshEnabled = isTokenAutoRefreshEnabled;\n}\n/**\r\n * Get the current App Check token. Attaches to the most recent\r\n * in-flight request if one is present. Returns null if no token\r\n * is present and no token requests are in-flight.\r\n *\r\n * @param appCheckInstance - The App Check service instance.\r\n * @param forceRefresh - If true, will always try to fetch a fresh token.\r\n * If false, will use a cached token if found in storage.\r\n * @public\r\n */\nfunction getToken(_x10, _x11) {\n  return _getToken.apply(this, arguments);\n}\n/**\r\n * Requests a Firebase App Check token. This method should be used\r\n * only if you need to authorize requests to a non-Firebase backend.\r\n *\r\n * Returns limited-use tokens that are intended for use with your\r\n * non-Firebase backend endpoints that are protected with\r\n * <a href=\"https://firebase.google.com/docs/app-check/custom-resource-backend#replay-protection\">\r\n * Replay Protection</a>. This method\r\n * does not affect the token generation behavior of the\r\n * #getAppCheckToken() method.\r\n *\r\n * @param appCheckInstance - The App Check service instance.\r\n * @returns The limited use token.\r\n * @public\r\n */\nfunction _getToken() {\n  _getToken = _asyncToGenerator(function* (appCheckInstance, forceRefresh) {\n    const result = yield getToken$2(appCheckInstance, forceRefresh);\n    if (result.error) {\n      throw result.error;\n    }\n    return {\n      token: result.token\n    };\n  });\n  return _getToken.apply(this, arguments);\n}\nfunction getLimitedUseToken(appCheckInstance) {\n  return getLimitedUseToken$1(appCheckInstance);\n}\n/**\r\n * Wraps `addTokenListener`/`removeTokenListener` methods in an `Observer`\r\n * pattern for public use.\r\n */\nfunction onTokenChanged(appCheckInstance, onNextOrObserver, onError,\n/**\r\n * NOTE: Although an `onCompletion` callback can be provided, it will\r\n * never be called because the token stream is never-ending.\r\n * It is added only for API consistency with the observer pattern, which\r\n * we follow in JS APIs.\r\n */\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nonCompletion) {\n  let nextFn = () => {};\n  let errorFn = () => {};\n  if (onNextOrObserver.next != null) {\n    nextFn = onNextOrObserver.next.bind(onNextOrObserver);\n  } else {\n    nextFn = onNextOrObserver;\n  }\n  if (onNextOrObserver.error != null) {\n    errorFn = onNextOrObserver.error.bind(onNextOrObserver);\n  } else if (onError) {\n    errorFn = onError;\n  }\n  addTokenListener(appCheckInstance, \"EXTERNAL\" /* ListenerType.EXTERNAL */, nextFn, errorFn);\n  return () => removeTokenListener(appCheckInstance.app, nextFn);\n}\n\n/**\r\n * The Firebase App Check Web SDK.\r\n *\r\n * @remarks\r\n * Firebase App Check does not work in a Node.js environment using `ReCaptchaV3Provider` or\r\n * `ReCaptchaEnterpriseProvider`, but can be used in Node.js if you use\r\n * `CustomProvider` and write your own attestation method.\r\n *\r\n * @packageDocumentation\r\n */\nconst APP_CHECK_NAME = 'app-check';\nconst APP_CHECK_NAME_INTERNAL = 'app-check-internal';\nfunction registerAppCheck() {\n  // The public interface\n  _registerComponent(new Component(APP_CHECK_NAME, container => {\n    // getImmediate for FirebaseApp will always succeed\n    const app = container.getProvider('app').getImmediate();\n    const heartbeatServiceProvider = container.getProvider('heartbeat');\n    return factory(app, heartbeatServiceProvider);\n  }, \"PUBLIC\" /* ComponentType.PUBLIC */).setInstantiationMode(\"EXPLICIT\" /* InstantiationMode.EXPLICIT */)\n  /**\r\n   * Initialize app-check-internal after app-check is initialized to make AppCheck available to\r\n   * other Firebase SDKs\r\n   */.setInstanceCreatedCallback((container, _identifier, _appcheckService) => {\n    container.getProvider(APP_CHECK_NAME_INTERNAL).initialize();\n  }));\n  // The internal interface used by other Firebase products\n  _registerComponent(new Component(APP_CHECK_NAME_INTERNAL, container => {\n    const appCheck = container.getProvider('app-check').getImmediate();\n    return internalFactory(appCheck);\n  }, \"PUBLIC\" /* ComponentType.PUBLIC */).setInstantiationMode(\"EXPLICIT\" /* InstantiationMode.EXPLICIT */));\n  registerVersion(name, version);\n}\nregisterAppCheck();\nexport { CustomProvider, ReCaptchaEnterpriseProvider, ReCaptchaV3Provider, getLimitedUseToken, getToken, initializeAppCheck, onTokenChanged, setTokenAutoRefreshEnabled };\n//# sourceMappingURL=index.esm2017.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}