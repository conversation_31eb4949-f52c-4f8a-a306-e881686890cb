{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { SharedLinkRoutingModule } from './shared-link-routing.module';\nimport { SharedCertificateComponent } from './shared-certificate.component';\nimport * as i0 from \"@angular/core\";\nexport class SharedLinkModule {\n  static #_ = this.ɵfac = function SharedLinkModule_Factory(t) {\n    return new (t || SharedLinkModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: SharedLinkModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, SharedLinkRoutingModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SharedLinkModule, {\n    declarations: [SharedCertificateComponent],\n    imports: [CommonModule, SharedLinkRoutingModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "SharedLinkRoutingModule", "SharedCertificateComponent", "SharedLinkModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\shared-link\\shared-link.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { SharedLinkRoutingModule } from './shared-link-routing.module';\r\nimport { SharedCertificateComponent } from './shared-certificate.component';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    SharedCertificateComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    SharedLinkRoutingModule\r\n  ]\r\n})\r\nexport class SharedLinkModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,uBAAuB,QAAQ,8BAA8B;AACtE,SAASC,0BAA0B,QAAQ,gCAAgC;;AAY3E,OAAM,MAAOC,gBAAgB;EAAA,QAAAC,CAAA,G;qBAAhBD,gBAAgB;EAAA;EAAA,QAAAE,EAAA,G;UAAhBF;EAAgB;EAAA,QAAAG,EAAA,G;cAJzBN,YAAY,EACZC,uBAAuB;EAAA;;;2EAGdE,gBAAgB;IAAAI,YAAA,GAPzBL,0BAA0B;IAAAM,OAAA,GAG1BR,YAAY,EACZC,uBAAuB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}