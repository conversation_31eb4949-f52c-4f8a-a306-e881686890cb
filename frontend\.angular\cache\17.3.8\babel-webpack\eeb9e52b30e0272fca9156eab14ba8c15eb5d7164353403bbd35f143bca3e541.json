{"ast": null, "code": "import { skip } from 'rxjs/operators';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/layout.service\";\nimport * as i2 from \"../packages.component\";\nconst _c0 = [\"packagesWrapper\"];\nexport let PackagesBlockPageComponent = /*#__PURE__*/(() => {\n  class PackagesBlockPageComponent {\n    constructor(layoutService) {\n      this.layoutService = layoutService;\n      this.subs = new SubSink();\n      this.packagesWrapper = {};\n    }\n    ngOnInit() {}\n    ngAfterViewInit() {\n      this.subs.sink = this.layoutService.sideMenuHeight.pipe(skip(1)).subscribe(res => {\n        if (res !== 0) {\n          this.packagesWrapper.nativeElement.style.minHeight = res + 'px';\n        }\n      });\n    }\n    static #_ = this.ɵfac = function PackagesBlockPageComponent_Factory(t) {\n      return new (t || PackagesBlockPageComponent)(i0.ɵɵdirectiveInject(i1.LayoutService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PackagesBlockPageComponent,\n      selectors: [[\"app-packages-block-page\"]],\n      viewQuery: function PackagesBlockPageComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.packagesWrapper = _t.first);\n        }\n      },\n      decls: 8,\n      vars: 0,\n      consts: [[\"packagesWrapper\", \"\"], [\"id\", \"profile\", 1, \"profile\", \"flex-column\", \"border-round-xl\"], [1, \"block-header\", \"justify-content-center\", \"hidden\", \"lg:flex\"], [1, \"block-title\"], [1, \"text-0\"], [1, \"mt-2\", \"h-full\"]],\n      template: function PackagesBlockPageComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1, 0)(2, \"div\", 2)(3, \"span\", 3)(4, \"span\", 4);\n          i0.ɵɵtext(5, \"My Packages\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(6, \"div\", 5);\n          i0.ɵɵelement(7, \"app-packages\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      dependencies: [i2.PackagesComponent],\n      styles: [\".profile[_ngcontent-%COMP%]{border-radius:10px;background-color:var(--white);display:flex}.profile[_ngcontent-%COMP%]   .block-header[_ngcontent-%COMP%]{background:url(/assets/images/profile-header-bg.png) no-repeat center;border-radius:10px;background-size:100%}\"]\n    });\n  }\n  return PackagesBlockPageComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}