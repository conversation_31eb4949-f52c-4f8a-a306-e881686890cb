{"ast": null, "code": "import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { SubSink } from 'subsink';\nimport * as moment from 'moment';\nimport { NoteListenersService, NoteViewAccess } from 'src/app/core/services/note-listeners.service';\nimport { LibraryFileActionsDialogComponent } from 'src/app/modules/library/library/components/library-file-actions-dialog/library-file-actions-dialog.component';\nimport { DialogService } from 'primeng/dynamicdialog';\nimport { tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nimport * as i2 from \"src/app/core/services/note.service\";\nimport * as i3 from \"src/app/core/services/toast.service\";\nimport * as i4 from \"src/app/core/services/classroom.service\";\nimport * as i5 from \"src/app/core/services/auth.service\";\nimport * as i6 from \"src/app/core/services/note-listeners.service\";\nimport * as i7 from \"src/app/core/services/note-actions.service\";\nimport * as i8 from \"primeng/dynamicdialog\";\nimport * as i9 from \"src/app/core/services/confirm-dialog.service\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"primeng/tabview\";\nimport * as i12 from \"@angular/forms\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nimport * as i14 from \"./note-details/note-details.component\";\nimport * as i15 from \"src/app/core/pipes/filter.pipe\";\nconst _c0 = a0 => ({\n  \"filter-blue\": a0\n});\nconst _c1 = (a0, a1) => [a0, a1];\nconst _c2 = (a0, a1) => ({\n  classroom: a0,\n  filteredNotes: a1\n});\nconst _c3 = (a0, a1, a2) => ({\n  classroom: a0,\n  filteredNotes: a1,\n  student: a2\n});\nconst _c4 = a0 => ({\n  term: a0\n});\nfunction NotesListComponent_img_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 14);\n    i0.ɵɵlistener(\"click\", function NotesListComponent_img_9_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showAddNote = !ctx_r1.showAddNote);\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotesListComponent_div_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function NotesListComponent_div_11_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onShareMultiple());\n    });\n    i0.ɵɵelementStart(1, \"div\", 18);\n    i0.ɵɵelement(2, \"img\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 20);\n    i0.ɵɵtext(4, \" Share \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotesListComponent_div_11_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function NotesListComponent_div_11_div_2_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onUnShareMultiple());\n    });\n    i0.ɵɵelementStart(1, \"div\", 18);\n    i0.ɵɵelement(2, \"img\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 20);\n    i0.ɵɵtext(4, \" Unshare \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotesListComponent_div_11_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function NotesListComponent_div_11_div_8_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onRemoveNoteMultiple());\n    });\n    i0.ɵɵelementStart(1, \"div\", 18);\n    i0.ɵɵelement(2, \"img\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 20);\n    i0.ɵɵtext(4, \" Delete \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotesListComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtemplate(1, NotesListComponent_div_11_div_1_Template, 5, 0, \"div\", 16)(2, NotesListComponent_div_11_div_2_Template, 5, 0, \"div\", 16);\n    i0.ɵɵelementStart(3, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function NotesListComponent_div_11_Template_div_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.downloadMultiple());\n    });\n    i0.ɵɵelementStart(4, \"div\", 18);\n    i0.ɵɵelement(5, \"img\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 20);\n    i0.ɵɵtext(7, \" Download \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, NotesListComponent_div_11_div_8_Template, 5, 0, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpureFunction2(3, _c1, ctx_r1.noteViewAccess.TEACHER_VIEW_STUDENTS_CLASSROOM_NOTES, ctx_r1.noteViewAccess.STUDENT_VIEW_TEACHERS_CLASSROOM_NOTES).includes(ctx_r1.currentNotesView));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentNotesView !== ctx_r1.noteViewAccess.TEACHER_VIEW_NOTES && ctx_r1.currentNotesView !== ctx_r1.noteViewAccess.STUDENT_VIEW_TEACHERS_CLASSROOM_NOTES);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasAdd);\n  }\n}\nfunction NotesListComponent_ng_container_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-note-details\", 23);\n    i0.ɵɵlistener(\"closedNewNote\", function NotesListComponent_ng_container_12_div_1_Template_app_note_details_closedNewNote_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onClosedNewNote());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"type\", \"create\")(\"classroom\", ctx_r1.classroom)(\"showGroupActions\", ctx_r1.showGroupActions)(\"view\", ctx_r1.noteListenersService.getNotesViewAccess(ctx_r1.hasAdd, ctx_r1.withClassroom));\n  }\n}\nfunction NotesListComponent_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NotesListComponent_ng_container_12_div_1_Template, 2, 4, \"div\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.classroomStudents && ctx_r1.classroomStudents.length === 1 || !ctx_r1.withClassroom) && ctx_r1.hasAdd);\n  }\n}\nfunction NotesListComponent_div_13_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NotesListComponent_div_13_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NotesListComponent_div_13_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const notesGeneralTemplate_r8 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", notesGeneralTemplate_r8)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c2, ctx_r1.classroom, ctx_r1.filteredNotes));\n  }\n}\nfunction NotesListComponent_div_13_ng_container_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NotesListComponent_div_13_ng_container_2_ng_container_5_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NotesListComponent_div_13_ng_container_2_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p-tabPanel\", 31);\n    i0.ɵɵtemplate(2, NotesListComponent_div_13_ng_container_2_ng_container_5_ng_container_2_Template, 1, 0, \"ng-container\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const selectedStudent_r9 = ctx.$implicit;\n    i0.ɵɵnextContext(2);\n    const notesGeneralTemplate_r8 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"header\", selectedStudent_r9.firstName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", notesGeneralTemplate_r8)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction3(3, _c3, ctx_r1.classroom, ctx_r1.filteredNotes, selectedStudent_r9));\n  }\n}\nfunction NotesListComponent_div_13_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p-tabView\", 27)(2, \"p-tabPanel\", 28)(3, \"div\", 29);\n    i0.ɵɵtemplate(4, NotesListComponent_div_13_ng_container_2_ng_container_4_Template, 1, 0, \"ng-container\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, NotesListComponent_div_13_ng_container_2_ng_container_5_Template, 3, 7, \"ng-container\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const notesGeneralTemplate_r8 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"scrollable\", ctx_r1.classroomStudents.length > 4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", notesGeneralTemplate_r8)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(4, _c2, ctx_r1.classroom, ctx_r1.filteredNotes));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.classroomStudents);\n  }\n}\nfunction NotesListComponent_div_13_ng_template_3_ng_container_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-note-details\", 23);\n    i0.ɵɵlistener(\"closedNewNote\", function NotesListComponent_div_13_ng_template_3_ng_container_0_div_1_Template_app_note_details_closedNewNote_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onClosedNewNote());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const classroom_r11 = i0.ɵɵnextContext(2).classroom;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"type\", \"create\")(\"classroom\", classroom_r11)(\"showGroupActions\", ctx_r1.showGroupActions)(\"view\", ctx_r1.noteListenersService.getNotesViewAccess(ctx_r1.hasAdd, ctx_r1.withClassroom));\n  }\n}\nfunction NotesListComponent_div_13_ng_template_3_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NotesListComponent_div_13_ng_template_3_ng_container_0_div_1_Template, 2, 4, \"div\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.classroomStudents && ctx_r1.classroomStudents.length > 1);\n  }\n}\nfunction NotesListComponent_div_13_ng_template_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-note-details\", 33);\n    i0.ɵɵlistener(\"noteChecked\", function NotesListComponent_div_13_ng_template_3_div_1_Template_app_note_details_noteChecked_1_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onNoteChecked($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const note_r13 = ctx.$implicit;\n    const classroom_r11 = i0.ɵɵnextContext().classroom;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"isInMine\", ctx_r1.hasAdd)(\"note\", note_r13)(\"classroom\", classroom_r11)(\"withClassroom\", ctx_r1.withClassroom)(\"showGroupActions\", ctx_r1.showGroupActions)(\"view\", ctx_r1.noteListenersService.getNotesViewAccess(ctx_r1.hasAdd, ctx_r1.withClassroom));\n  }\n}\nfunction NotesListComponent_div_13_ng_template_3_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"img\", 37);\n    i0.ɵɵlistener(\"click\", function NotesListComponent_div_13_ng_template_3_div_3_div_1_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.showAddNote = !ctx_r1.showAddNote);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"p\", 38);\n    i0.ɵɵtext(3, \" Add your first note! \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotesListComponent_div_13_ng_template_3_div_3_ng_container_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" No notes found. \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction NotesListComponent_div_13_ng_template_3_div_3_ng_container_2_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" No notes found. \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction NotesListComponent_div_13_ng_template_3_div_3_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 36);\n    i0.ɵɵelement(2, \"img\", 39);\n    i0.ɵɵelementStart(3, \"p\", 38);\n    i0.ɵɵtemplate(4, NotesListComponent_div_13_ng_template_3_div_3_ng_container_2_ng_container_4_Template, 2, 0, \"ng-container\", 12)(5, NotesListComponent_div_13_ng_template_3_div_3_ng_container_2_ng_container_5_Template, 2, 0, \"ng-container\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isStudent);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isTeacher);\n  }\n}\nfunction NotesListComponent_div_13_ng_template_3_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtemplate(1, NotesListComponent_div_13_ng_template_3_div_3_div_1_Template, 4, 0, \"div\", 35)(2, NotesListComponent_div_13_ng_template_3_div_3_ng_container_2_Template, 6, 2, \"ng-container\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const student_r15 = i0.ɵɵnextContext().student;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"fromRoot\", true)(\"scrollWindow\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.hasAdd || ctx_r1.getFilteredNotes(student_r15).length == 0) && ctx_r1.getFilteredNotes(student_r15).length == 0 && !student_r15 && ctx_r1.hasAdd);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hasAdd || ctx_r1.getFilteredNotes(student_r15).length === 0 && student_r15);\n  }\n}\nfunction NotesListComponent_div_13_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NotesListComponent_div_13_ng_template_3_ng_container_0_Template, 2, 1, \"ng-container\", 12)(1, NotesListComponent_div_13_ng_template_3_div_1_Template, 2, 6, \"div\", 30);\n    i0.ɵɵpipe(2, \"filter\");\n    i0.ɵɵtemplate(3, NotesListComponent_div_13_ng_template_3_div_3_Template, 3, 4, \"div\", 32);\n  }\n  if (rf & 2) {\n    const student_r15 = ctx.student;\n    const filteredNotes_r16 = ctx.filteredNotes;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentNotesView === ctx_r1.noteViewAccess.TEACHER_VIEW_CLASSROOM_NOTES && ctx_r1.showAddNote);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind2(2, 3, ctx_r1.getFilteredNotes(student_r15), i0.ɵɵpureFunction1(6, _c4, ctx_r1.term)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", filteredNotes_r16 && ctx_r1.getFilteredNotes(student_r15).length == 0 && !ctx_r1.showAddNote);\n  }\n}\nfunction NotesListComponent_div_13_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"mat-spinner\", 41);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotesListComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtemplate(1, NotesListComponent_div_13_ng_container_1_Template, 2, 5, \"ng-container\", 12)(2, NotesListComponent_div_13_ng_container_2_Template, 6, 7, \"ng-container\", 12)(3, NotesListComponent_div_13_ng_template_3_Template, 4, 8, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(5, NotesListComponent_div_13_div_5_Template, 2, 0, \"div\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.classroomStudents && ctx_r1.classroomStudents.length === 1 || !ctx_r1.withClassroom || ctx_r1.authService.isStudent);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.withClassroom && ctx_r1.authService.isTeacher && ctx_r1.classroomStudents && ctx_r1.classroomStudents.length > 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n  }\n}\nexport let NotesListComponent = /*#__PURE__*/(() => {\n  class NotesListComponent {\n    constructor(generalService, noteService, toastService, classroomService, cd, authService, noteListenersService, noteActionsService, dialogService, confirmDialogService) {\n      this.generalService = generalService;\n      this.noteService = noteService;\n      this.toastService = toastService;\n      this.classroomService = classroomService;\n      this.cd = cd;\n      this.authService = authService;\n      this.noteListenersService = noteListenersService;\n      this.noteActionsService = noteActionsService;\n      this.dialogService = dialogService;\n      this.confirmDialogService = confirmDialogService;\n      this.notes = [];\n      this.viewTitle = 'My Notes';\n      this.withClassroom = false;\n      this.hasAdd = true;\n      this.classroom = {};\n      this.subs = new SubSink();\n      this.showFilters = false;\n      this.showGroupActions = true;\n      this.showAddNote = false;\n      this.tryToSave = false;\n      this.showTextError = false;\n      this.isSharedFilter = false;\n      this.isNotSharedFilter = false;\n      this.isPinnedFilter = false;\n      this.isFavoritedFilter = false;\n      this.fromFilterValue = {};\n      this.toFilterValue = {};\n      this.datePickerOpen = false;\n      this.filteredNotes = [];\n      this.classrooms = [];\n      this.showSend = false;\n      this.showSendAndShare = false;\n      this.usersToSend = [];\n      this.currentSelectedClassroomId = 0;\n      this.showingMine = true;\n      this.inClassroom = false;\n      this.isLoading = false;\n      this.textEditor = '';\n      this.checkedNotes = [];\n      this.classroomStudents = [];\n      this.user = {};\n      this.role = \"\";\n      this.maxSize = 6;\n      this.directionLinks = true;\n      this.autoHide = false;\n      this.to = {};\n      this.from = {};\n      this.form = new UntypedFormGroup({});\n      this.isInMine = false;\n      this.noteViewAccess = NoteViewAccess;\n    }\n    ngOnInit() {\n      this.user = this.authService.getLoggedInUser();\n      this.role = this.user.role;\n      this.initClassroomListener();\n      // this.notSharedFilter(true);\n      this.form = new UntypedFormGroup({\n        title: new UntypedFormControl(null, {\n          validators: [Validators.required]\n        }),\n        text: new UntypedFormControl(null, {\n          validators: [Validators.required]\n        })\n      });\n    }\n    ngOnChanges() {\n      if (this.classroom) {\n        this.classroomStudents = this.classroom.classroomStudents;\n      }\n      this.filteredNotes = this.notes;\n    }\n    ngAfterViewInit() {\n      this.showGroupActions = false;\n      this.cd.detectChanges();\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n      this.noteListenersService.setGroupActions(false);\n      this.noteService.checkedNotes = [];\n    }\n    ngAfterContentInit() {}\n    get currentNotesView() {\n      return this.noteListenersService.getNotesViewAccess(this.hasAdd, this.withClassroom);\n    }\n    //listener to change ui in nite details\n    setGroupActions() {\n      this.showGroupActions = !this.showGroupActions;\n      if (!this.showGroupActions) {\n        this.noteService.checkedNotes = [];\n      }\n      setTimeout(() => {\n        this.noteListenersService.setGroupActions(this.showGroupActions);\n      }, 300);\n    }\n    /**\n     * Filters the notes based on whether they are shared with the given student.\n     *\n     * @param targetStudent - The student user to filter by.\n     * @returns An array of notes that are shared with the given student.\n     */\n    getFilteredNotes(targetStudent) {\n      if (this.hasAdd) {\n        return this.filteredNotes.filter(note => {\n          return targetStudent ? note.sharedWithUsers.some(user => user.aspUserId === targetStudent.aspUserId) : true;\n        });\n      } else {\n        return this.filteredNotes.filter(note => {\n          return targetStudent ? note.createdBy.aspUserId === targetStudent.aspUserId : true;\n        });\n      }\n    }\n    areNotFavorited() {\n      for (let note of this.noteService.checkedNotes) {\n        if (note.favouritedBy.filter(user => user.id == this.authService.getUserId()).length == 0) return true;\n      }\n      return false;\n    }\n    areNotPinned() {\n      for (let note of this.noteService.checkedNotes) {\n        if (!note.pinned) return true;\n      }\n      return false;\n    }\n    /**\n     * Filters the notes based on the selected filters and returns the filtered notes.\n     *\n     * @return {Note[]} An array of Note objects that pass the applied filters.\n     */\n    getNotesFiltered() {\n      let filteredNotes = [];\n      for (let note of this.notes) {\n        if (this.isPinnedFilter) {\n          if (!note.pinned) {\n            continue;\n          }\n        }\n        if (this.isFavoritedFilter) {\n          if (note.favouritedBy.filter(user => user.aspUserId == this.authService.getUserId()).length == 0) {\n            continue;\n          }\n        }\n        //get shared \n        if (this.from.value) {\n          if (!(note.created.getTime() > this.from.value.getTime())) {\n            continue;\n          }\n        }\n        if (this.to.value) {\n          if (!(note.created.getTime() < moment(this.to.value).add('1', 'd').toDate().getTime())) {\n            continue;\n          }\n        }\n        if (this.isSharedFilter) {\n          if (note.sharedWithUsers.length == 0) {\n            continue;\n          }\n        }\n        if (this.isNotSharedFilter) {\n          if (note.sharedWithUsers.length > 0) {\n            continue;\n          }\n        }\n        filteredNotes.push(note);\n      }\n      return filteredNotes;\n    }\n    ifFieldValid(field) {\n      return this.form.get(field)?.invalid && this.form.get(field)?.touched || this.tryToSave && this.form.get(field)?.invalid;\n    }\n    isTextValid() {\n      this.showTextError = !this.textEditor && this.textEditor === '' ? true : false;\n    }\n    close(event) {\n      this.showSend = false;\n      this.showSendAndShare = false;\n    }\n    onClosedNewNote() {\n      console.log('he');\n      this.showAddNote = false;\n    }\n    onNoteChecked(event) {\n      console.log(event);\n      if (event.checked) {\n        this.noteService.checkedNotes.push(event.note);\n      } else {\n        this.noteService.checkedNotes = this.noteService.checkedNotes.filter(el => el.id !== event.note.id);\n      }\n    }\n    onShareMultiple() {\n      const checkedNotes = this.noteService.checkedNotes;\n      if (checkedNotes.length === 0) {\n        this.validateCheckedNotes('Please select at least one note.');\n        return;\n      }\n      const isTeacher = this.authService.isTeacher;\n      if (isTeacher) {\n        this.handleTeacherShareMultiple(checkedNotes);\n      } else {\n        this.handleStudentShareMultiple(checkedNotes);\n      }\n    }\n    onUnShareMultiple() {\n      if (this.noteService.checkedNotes.length === 0) {\n        this.validateCheckedNotes('Please select at least one note.');\n        return;\n      }\n      this.noteService.checkedNotes.forEach(note => {\n        const unshareWith = note.sharedWithUsers.map(user => user.aspUserId);\n        const sendForUnshare = {\n          noteId: note.id,\n          classroomId: Number(this.classroom.id),\n          studentIds: this.getUserIdsForUnshare(unshareWith, note)\n        };\n        this.noteActionsService.unShareNote(sendForUnshare, 'Notes unshared successfully');\n      });\n      this.setGroupActions();\n    }\n    downloadMultiple() {\n      if (this.noteService.checkedNotes.length === 0) {\n        this.validateCheckedNotes('Please select at least one note.');\n        return;\n      }\n      for (let note of this.noteService.checkedNotes) {\n        this.noteActionsService.download(note);\n      }\n    }\n    onRemoveNoteMultiple() {\n      this.initConfirmDeleteNoteDialogListener();\n      if (this.noteService.checkedNotes.length === 0) {\n        this.validateCheckedNotes('Please select at least one note.');\n        return;\n      }\n      this.confirmDialogService.setProperties({\n        confirmMessage: `\n      Are you sure you want to delete the selected notes?`,\n        acceptBtnLabel: 'Yes',\n        rejectBtnLabel: 'No',\n        action: 'delete-note',\n        confirmIcon: '/assets/icons/clock-dialog-icon.svg',\n        showHeader: true,\n        dialogType: 'custom',\n        headerClass: 'my-custom-header-class'\n      });\n      this.confirmDialogService.show();\n    }\n    getUserIdsForUnshare(unshareWith, note) {\n      if (this.authService.isTeacher) {\n        return unshareWith;\n      } else if (this.noteService.isMyNote(note)) {\n        return [this.classroom.teacher.aspUserId];\n      }\n      return [this.user.aspUserId];\n    }\n    handleTeacherShareMultiple(checkedNotes) {\n      if (![this.noteViewAccess.TEACHER_VIEW_STUDENTS_CLASSROOM_NOTES, this.noteViewAccess.TEACHER_VIEW_NOTES].includes(this.currentNotesView)) {\n        if (this.classroom.classroomStudents.length === 1) {\n          this.shareNotesMultipleWithClassroom(checkedNotes);\n        } else {\n          this.openShareMultipleWithNoteDialog(checkedNotes);\n        }\n      } else {\n        this.openShareMultipleWithNoteDialog(checkedNotes);\n      }\n    }\n    handleStudentShareMultiple(checkedNotes) {\n      const unshareWith = [];\n      for (let note of checkedNotes) {\n        for (let user of note.sharedWithUsers) {\n          unshareWith.push(user.aspUserId);\n        }\n        const sendForShare = {\n          noteId: note.id,\n          classroomId: +this.classroom.id,\n          studentIds: [this.classroom.teacher.aspUserId]\n        };\n        this.noteActionsService.shareNote(sendForShare, 'Notes unshared successfully');\n      }\n      this.setGroupActions();\n    }\n    shareNotesMultipleWithClassroom(checkedNotes) {\n      for (let note of checkedNotes) {\n        const sendForShare = {\n          noteId: note.id,\n          classroomId: +this.classroom.id,\n          studentIds: [this.classroom.classroomStudents[0].aspUserId]\n        };\n        this.noteActionsService.shareNote(sendForShare, 'Notes shared successfully');\n      }\n    }\n    openShareMultipleWithNoteDialog(checkedNotes) {\n      this.generalService.openDialogWithComponent(this.dialogService, LibraryFileActionsDialogComponent, 440, {\n        action: 'shareWith',\n        dialogsubTitle: '',\n        dialogTitle: 'Share Note with...',\n        preselectedClassroom: this.classroom\n      }, null, result => {\n        console.log(result);\n        if (result && result.action === 'shareWith') {\n          if (!this.generalService.isNullishObject(result)) {\n            for (let note of checkedNotes) {\n              this.noteActionsService.sendOrShare(note, result, this.authService.isStudent);\n            }\n            this.setGroupActions();\n            console.log(result);\n          }\n        }\n      });\n    }\n    initConfirmDeleteNoteDialogListener() {\n      this.confirmDeleteNoteSubscription = this.confirmDialogService.acceptSelected$.pipe(tap(res => {\n        this.confirmDialogService.hide();\n        console.log(res);\n        switch (res.action) {\n          case 'delete-note':\n            console.log('deltenetote');\n            this.onRemoveNoteMultipleAccepted();\n        }\n      })).subscribe();\n      this.subs.add(this.confirmDeleteNoteSubscription);\n    }\n    onRemoveNoteMultipleAccepted() {\n      console.log(this.noteService.checkedNotes);\n      for (let note of this.noteService.checkedNotes) {\n        console.log(note);\n        this.noteActionsService.afterAcceptRemoveNote(note);\n      }\n      this.confirmDeleteNoteSubscription.unsubscribe();\n      this.setGroupActions();\n    }\n    validateCheckedNotes(msg) {\n      if (this.noteService.checkedNotes.length === 0) {\n        this.toastService.setShowToastmessage({\n          severity: 'warn',\n          summary: '',\n          detail: msg\n        });\n        return;\n      }\n    }\n    initClassroomListener() {\n      this.subs.add(this.noteListenersService.currentSelectedClassroom.subscribe(res => {\n        if (this.generalService.isNullishObject(res)) {\n          return;\n        }\n        console.log(this.filteredNotes);\n        this.classroom = res;\n        this.classroomStudents = this.classroom.classroomStudents;\n      }));\n    }\n    static #_ = this.ɵfac = function NotesListComponent_Factory(t) {\n      return new (t || NotesListComponent)(i0.ɵɵdirectiveInject(i1.GeneralService), i0.ɵɵdirectiveInject(i2.NoteService), i0.ɵɵdirectiveInject(i3.ToastService), i0.ɵɵdirectiveInject(i4.ClassroomService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.AuthService), i0.ɵɵdirectiveInject(i6.NoteListenersService), i0.ɵɵdirectiveInject(i7.NoteActionsService), i0.ɵɵdirectiveInject(i8.DialogService), i0.ɵɵdirectiveInject(i9.ConfirmDialogService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NotesListComponent,\n      selectors: [[\"app-notes-list\"]],\n      inputs: {\n        notes: \"notes\",\n        viewTitle: \"viewTitle\",\n        withClassroom: \"withClassroom\",\n        hasAdd: \"hasAdd\",\n        classroom: \"classroom\"\n      },\n      features: [i0.ɵɵProvidersFeature([DialogService, NoteListenersService]), i0.ɵɵNgOnChangesFeature],\n      decls: 14,\n      vars: 9,\n      consts: [[\"notesGeneralTemplate\", \"\"], [1, \"notes-header\"], [1, \"notes-header-title\"], [1, \"notes-header-actions\"], [1, \"notes-header-actions\", \"gap-2\"], [1, \"p-input-icon-right\", \"w-full\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Search\", 1, \"h-2rem\", \"w-full\", \"input-blue\", \"gradient-blue\", \"rounded\", 3, \"ngModelChange\", \"ngModel\"], [1, \"pi\", \"pi-search\", \"text-primary\"], [\"src\", \"/assets/icons/library/group-actions.svg\", \"title\", \"Group Actions\", 1, \"hvr-grow\", 3, \"click\", \"ngClass\"], [\"title\", \"Create Note\", \"src\", \"/assets/icons/library/add-lib.svg\", \"class\", \"add hvr-grow add-note-btn-icon\", 3, \"click\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\"], [\"class\", \"note-menu-row block-gradient border-round-xl font-sm my-2 p-1 px-3 gap-2\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"notes-height\", 4, \"ngIf\"], [\"title\", \"Create Note\", \"src\", \"/assets/icons/library/add-lib.svg\", 1, \"add\", \"hvr-grow\", \"add-note-btn-icon\", 3, \"click\"], [1, \"note-menu-row\", \"block-gradient\", \"border-round-xl\", \"font-sm\", \"my-2\", \"p-1\", \"px-3\", \"gap-2\"], [\"class\", \"note-menu-col\", 3, \"click\", 4, \"ngIf\"], [1, \"note-menu-col\", 3, \"click\"], [1, \"note-menu-icon\"], [\"src\", \"/assets/icons/cloud-download.svg\", \"height\", \"14\"], [1, \"note-menu-text\", \"link-main-color\"], [\"src\", \"/assets/icons/library/share-sm-icon.svg\", \"height\", \"14\"], [\"src\", \"/assets/icons/library/delete.svg\", \"height\", \"14\"], [3, \"closedNewNote\", \"note\", \"type\", \"classroom\", \"showGroupActions\", \"view\"], [1, \"notes-height\"], [\"style\", \"width:100%;height:800px; display: flex; justify-content: center;\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"w-full\", \"notes-height\", 3, \"scrollable\"], [\"header\", \"All\"], [\"id\", \"classroom-info\", 1, \"h-full\"], [4, \"ngFor\", \"ngForOf\"], [1, \"\", 3, \"header\"], [\"class\", \"asked-for-help-main scrollable h-full notes-height\", 3, \"fromRoot\", \"scrollWindow\", 4, \"ngIf\"], [3, \"noteChecked\", \"isInMine\", \"note\", \"classroom\", \"withClassroom\", \"showGroupActions\", \"view\"], [1, \"asked-for-help-main\", \"scrollable\", \"h-full\", \"notes-height\", 3, \"fromRoot\", \"scrollWindow\"], [\"class\", \"flex flex-column gap-1 align-items-center justify-content-center h-full\", 4, \"ngIf\"], [1, \"flex\", \"flex-column\", \"gap-1\", \"align-items-center\", \"justify-content-center\", \"h-full\"], [\"src\", \"/assets/icons/library/add-lib.svg\", 1, \"add\", \"lg\", \"hvr-grow\", 3, \"click\"], [1, \"font-base\", \"max-w-14rem\", \"text-center\"], [\"src\", \"/assets/icons/library/file_manager_emp.png\", \"alt\", \"file manager empty icon\", 1, \"add\", \"empty\", \"hvr-grow\"], [2, \"width\", \"100%\", \"height\", \"800px\", \"display\", \"flex\", \"justify-content\", \"center\"], [1, \"spinner\", 2, \"margin-top\", \"200px\"]],\n      template: function NotesListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n          i0.ɵɵtext(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"span\", 5)(6, \"input\", 6);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function NotesListComponent_Template_input_ngModelChange_6_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.term, $event) || (ctx.term = $event);\n            return $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"i\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"img\", 8);\n          i0.ɵɵlistener(\"click\", function NotesListComponent_Template_img_click_8_listener() {\n            return ctx.setGroupActions();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(9, NotesListComponent_img_9_Template, 1, 0, \"img\", 9);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"div\", 10);\n          i0.ɵɵtemplate(11, NotesListComponent_div_11_Template, 9, 6, \"div\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(12, NotesListComponent_ng_container_12_Template, 2, 1, \"ng-container\", 12)(13, NotesListComponent_div_13_Template, 6, 3, \"div\", 13);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.viewTitle, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.term);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c0, ctx.showGroupActions));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.hasAdd);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.showGroupActions);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (ctx.currentNotesView === ctx.noteViewAccess.TEACHER_VIEW_CLASSROOM_NOTES || ctx.currentNotesView === ctx.noteViewAccess.STUDENT_VIEW_CLASSROOM_NOTES || ctx.currentNotesView === ctx.noteViewAccess.TEACHER_VIEW_NOTES) && ctx.showAddNote);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i10.NgClass, i10.NgForOf, i10.NgIf, i10.NgTemplateOutlet, i11.TabView, i11.TabPanel, i12.DefaultValueAccessor, i12.NgControlStatus, i12.NgModel, i13.MatProgressSpinner, i14.NoteDetailsComponent, i15.FilterPipe],\n      styles: [\".modal[_ngcontent-%COMP%]{min-width:50vw}[_nghost-%COMP%]     .p-dropdown:not(.p-disabled):hover{border:2px solid rgb(228,185,84)}  .no-label>.p-button-label{display:none}input[type=file][_ngcontent-%COMP%]{visibility:hidden;width:10px}input[type=date][_ngcontent-%COMP%]{position:relative;padding:10px}input[type=date][_ngcontent-%COMP%]::-webkit-calendar-picker-indicator{color:transparent;background:none;z-index:1}input[type=date][_ngcontent-%COMP%]:before{color:transparent;background:none;display:block;font-family:FontAwesome;content:\\\"\\\\f073\\\";width:20px;height:25px;position:absolute;top:12px;right:12px;color:#999}.image-preview[_ngcontent-%COMP%]{height:10rem;margin:1rem 0;border-radius:50%;border:3px solid var(--main-color)}.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{height:100%}[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-header{padding:clamp(.38rem,.47vw + .28rem,.75rem)}[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-items .p-dropdown-item{margin-bottom:clamp(.38rem,.47vw + .28rem,.75rem)}.country-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:clamp(.75rem,.31vw + .69rem,1rem);font-size:clamp(.88rem,.16vw + .84rem,1rem)}.country-item[_ngcontent-%COMP%]   .country-name[_ngcontent-%COMP%]{white-space:pre-line}.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;display:inline-block;vertical-align:middle}.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;clip-path:circle()}.country-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:32px;margin-right:.5rem}.country-item[_ngcontent-%COMP%]   img.smaller[_ngcontent-%COMP%]{width:16px}.info-element[_ngcontent-%COMP%]{padding:10px 0}.responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}.note[_ngcontent-%COMP%]{border-radius:40px;border:1px solid #eaf0f5;margin-top:2px;position:relative}.note[_ngcontent-%COMP%]:before{pointer-events:none;content:\\\"\\\";position:absolute;inset:0;border-radius:30px;padding:2px}.note.isFavNote[_ngcontent-%COMP%]{background-image:linear-gradient(90deg,#3874f5,#6d6beb 29%,#9167e0 53%,#aa60d2 68%,#bd59c5 77%,#ca53b4 84%,#d54da6 89%,#dc5096,#e15188 97%,#e3547a)}.note.isFavNote[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]{color:#fff}.note.isFavNote[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .note.isFavNote[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]{border:1px solid white}.note.isFavNote[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .note.isFavNote[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%]{background-color:#fff}.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]{cursor:pointer;display:flex;align-items:center;color:var(--main-color)}.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .section-arrow[_ngcontent-%COMP%]{width:40px}.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;width:100%}@media screen and (max-width: 768px){.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;margin-left:20px}}.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%]{margin-left:20px}@media screen and (max-width: 768px){.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%]{margin-left:0}}.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%]   .note-dates[_ngcontent-%COMP%]{display:flex;margin-top:10px}@media screen and (max-width: 768px){.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%]   .note-dates[_ngcontent-%COMP%]{flex-direction:column}}.note-info[_ngcontent-%COMP%]{display:flex;flex-direction:column}@media only screen and (max-width: 768px){.note-info[_ngcontent-%COMP%]{margin-top:0}}.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]{display:flex}@media only screen and (max-width: 768px){.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]{justify-content:end}}.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]{display:flex;align-items:center;margin-right:10px;margin-left:auto}@media only screen and (max-width: 768px){.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]{margin-left:0}}.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-icon[_ngcontent-%COMP%], .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-text[_ngcontent-%COMP%]{margin-left:4px;cursor:pointer}@media screen and (max-width: 768px){.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-icon[_ngcontent-%COMP%], .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-text[_ngcontent-%COMP%]{margin-left:0}}.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-icon[_ngcontent-%COMP%]{width:20px;display:flex;justify-content:center}.note-info[_ngcontent-%COMP%]   .note-info-bottom[_ngcontent-%COMP%]{display:flex;justify-content:end;text-align:right;margin-right:10px;font-size:.725rem;color:#2d3b8e}.note-content[_ngcontent-%COMP%]{overflow:hidden;transition:height .3s ease-out}.note-text-content[_ngcontent-%COMP%]{padding:20px 30px}.update-button[_ngcontent-%COMP%]{padding:5px 30px;background-color:var(--light-purple);text-align:center;border-radius:10px;color:#fff;margin-top:15px;cursor:pointer}.update-button[_ngcontent-%COMP%]:hover{background-color:var(--main-color)}.section-arrow[_ngcontent-%COMP%]{overflow:hidden;transition-duration:.2s;transition-property:transform;cursor:pointer}.note-burger[_ngcontent-%COMP%], .note-burger-group[_ngcontent-%COMP%]{position:relative;border-radius:50%;min-width:36px;min-height:36px;border:2px solid #6563ec;display:flex;align-items:center;box-sizing:border-box;justify-content:center;cursor:pointer}.note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%]{width:4px;height:4px;border-radius:50%;background-color:#6563ec;margin:2px}.note-burger[_ngcontent-%COMP%]   .inner-circle-group[_ngcontent-%COMP%], .note-burger-group[_ngcontent-%COMP%]   .inner-circle-group[_ngcontent-%COMP%]{width:34px;height:34px;border-radius:50%;background-color:transparent}.note-menu[_ngcontent-%COMP%]{width:300px;position:absolute;right:30px;top:69px;border-radius:12px;border:1px solid var(--main-color);box-sizing:border-box;transition-duration:.2s;transition-property:transform;background-color:#fff;z-index:1;display:none;flex-direction:column;padding:10px}@media screen and (max-width: 1124px){.note-menu[_ngcontent-%COMP%]{right:30px}}.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]{display:flex;flex-direction:column}.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]{margin-top:5px;display:flex;align-items:center}.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-text[_ngcontent-%COMP%]{margin-left:10px;cursor:pointer}.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-icon[_ngcontent-%COMP%]{width:20px;display:flex;justify-content:center}.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]:first-child{margin-top:0}.plain[_ngcontent-%COMP%]{margin:-13px 2px 0;border-top:0;border-radius:0 0 12px 12px;position:relative}.filter-white[_ngcontent-%COMP%]{filter:invert(100%) sepia(0%) saturate(1%) hue-rotate(328deg) brightness(200%) contrast(101%)}.note-info-width[_ngcontent-%COMP%]{min-width:10rem}.note-title-text[_ngcontent-%COMP%]{font-weight:400;font-size:1rem;letter-spacing:.01em;text-align:left;max-width:17rem}@media only screen and (max-width: 768px){.note-title-text[_ngcontent-%COMP%]{max-width:9rem;font-size:.825rem}}.accordion[_ngcontent-%COMP%]{border-radius:4px;margin-top:10px}.header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:8px;cursor:pointer;border-radius:31px;box-shadow:3px 3px 6px 2px #00000029;z-index:4;position:relative;border:1px solid transparent}.header.isPinned[_ngcontent-%COMP%]{color:#fff;background-image:linear-gradient(90deg,#5060c3 0% 13%,#5060c3 25% 37%,#5060c3 50% 63%,#5060c3 75% 87%,#5060c3)}.header.isPinned[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.isPinned[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]{border:2px solid white}.header.isPinned[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.isPinned[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%]{background-color:#fff}.header.isPinned[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{background:transparent;border:2px solid white}.header.isPinnedAndFavourite[_ngcontent-%COMP%]{color:#fff;background-image:linear-gradient(90deg,#4253b3,#495abc,#5060c3,#5767cb,#5f6ed3 44%,#6674db 56%,#6a78e2,#717fea,#7887f2,#808efa)}.header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]{border:2px solid white}.header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%]{background-color:#fff}.header.isPinnedAndFavourite[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{background:transparent;border:2px solid white}.header.opened[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]{border:1px solid transparent;color:#fff;background-color:#7f8dfa}.header.opened.activated[_ngcontent-%COMP%], .header.opened2.activated[_ngcontent-%COMP%]{background-color:#7f8dfa}.header.opened[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.opened[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]{border:2px solid white}.header.opened[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.opened[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%]{background-color:#fff}.header.opened[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{border:1px solid white;background:transparent;width:36px;height:36px;border-radius:50%;display:flex;justify-content:center;align-items:center}.content[_ngcontent-%COMP%]{padding:10px}.down-arrow[_ngcontent-%COMP%]{transition:transform .3s ease-in-out}.down-arrow.pi-chevron-up[_ngcontent-%COMP%]{transform:rotate(360deg)}.circle[_ngcontent-%COMP%]{background-image:linear-gradient(180deg,#9280fa,#8d7cf8,#8779f6,#8275f5,#7d72f3 44%,#736af0 56%,#6c68ee,#6664ed,#6161ea,#595ee8);width:36px;height:36px;border-radius:50%;display:flex;justify-content:center;align-items:center}.circle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#fff;font-size:20px}.rotate[_ngcontent-%COMP%]{transform:rotate(180deg);transition:transform .3s ease-in-out}.accordion-content[_ngcontent-%COMP%]{max-height:0;overflow:hidden}.open[_ngcontent-%COMP%]{background-color:#fff;margin-top:-20px;border-bottom-left-radius:30px;border-bottom-right-radius:30px;height:370px;max-height:370px;padding:20px 3px 3px}@media only screen and (max-width: 768px){.open[_ngcontent-%COMP%]{height:410px;max-height:410px}}.btn-action[_ngcontent-%COMP%]{width:200px}@media only screen and (max-width: 768px){.btn-action[_ngcontent-%COMP%]{width:auto}}[_nghost-%COMP%]     .p-editor-container .p-editor-toolbar.ql-snow{border:none;padding:0!important}[_nghost-%COMP%]     .p-editor-container .p-editor-content .ql-editor{background:transparent;padding:4px}[_nghost-%COMP%]     .p-editor-container .p-editor-content .ql-editor p{font-size:1rem}[_nghost-%COMP%]     .p-editor-container .p-editor-content .ql-editor[contenteditable=true]{border:1px solid var(--my-gray);border-radius:10px}[_nghost-%COMP%]     .circle.ql-snow{border:none}[_nghost-%COMP%]     .p-editor-container .p-editor-content.ql-snow{border:none}[_nghost-%COMP%]     .ql-snow .ql-tooltip{transform:translate(80%,-50%)}@media only screen and (max-width: 768px){[_nghost-%COMP%]     .ql-snow .ql-tooltip{transform:translate(45%,-50%)}}  .p-menu .p-menuitem-link{padding:.5rem 1rem!important}#add-note[_ngcontent-%COMP%]{background-color:var(--gray);padding:30px;border-radius:12px;margin-top:30px}.add-note-btn-icon[_ngcontent-%COMP%]{width:25px;height:25px}.notes-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.notes-header[_ngcontent-%COMP%]   .notes-header-title[_ngcontent-%COMP%]{font-size:18px;color:var(--main-color);font-weight:700}.notes-header[_ngcontent-%COMP%]   .notes-header-actions[_ngcontent-%COMP%]{display:flex;align-items:center}.notes-header[_ngcontent-%COMP%]   .notes-header-actions[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{margin-right:.7em}.notes-header[_ngcontent-%COMP%]   .notes-header-actions[_ngcontent-%COMP%]   .light-purple-circle-button[_ngcontent-%COMP%]{width:20px;height:20px}.note-menu-row[_ngcontent-%COMP%]{display:flex;justify-content:space-evenly;border-radius:40px}.note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]{display:flex;align-items:center;position:relative}.note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-text[_ngcontent-%COMP%]{cursor:pointer}.note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-icon[_ngcontent-%COMP%]{width:20px;display:flex;justify-content:center}.rte[_ngcontent-%COMP%]{margin-top:20px;border-radius:20px;z-index:0!important}[_nghost-%COMP%]     .e-toolbar-items{border-radius:20px!important}[_nghost-%COMP%]     .e-rte-toolbar{border-radius:20px 20px 0 0!important}[_nghost-%COMP%]     .e-rte-content{border-radius:0 0 20px 20px!important;border-top:0px!important}[_nghost-%COMP%]     .e-date-icon{position:absolute!important;left:0!important}[_nghost-%COMP%]     .e-input{margin-left:30px!important}.notes-popup[_ngcontent-%COMP%]{position:absolute;top:40px;width:200px;border-radius:12px;background-color:#fff;color:var(--main-color);right:0;box-shadow:0 -4px 4px #00000040;max-height:400px;z-index:10000}.notes-filters-title[_ngcontent-%COMP%]{font-size:18px;font-weight:700}.notes-filters-filters[_ngcontent-%COMP%]{font-size:15px}.notes-filters-filters[_ngcontent-%COMP%]   .filter-row[_ngcontent-%COMP%]{margin-top:15px;display:flex;align-items:center}.notes-filters-filters[_ngcontent-%COMP%]   .filter-row[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]{margin-left:15px}.notes-filters-filters[_ngcontent-%COMP%]   .filter-col[_ngcontent-%COMP%]{margin-top:15px}.send-classroom-title[_ngcontent-%COMP%]{font-size:18px;font-weight:700}.send-classroom-user[_ngcontent-%COMP%]{font-size:15px;margin-left:10px;display:flex}.send-button[_ngcontent-%COMP%]{background-color:var(--main-color);width:100%;position:sticky;bottom:0;left:0;padding:8px;box-sizing:border-box;color:#fff;text-align:center;border-radius:12px;cursor:pointer}.notes-height[_ngcontent-%COMP%]{height:calc(100% - 50px)}.btns[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{width:100%}[_nghost-%COMP%]     .ngx-pagination{padding:0!important}.filter-blue[_ngcontent-%COMP%]{opacity:.7}.add[_ngcontent-%COMP%]{width:32px;height:32px}.add.lg[_ngcontent-%COMP%]{width:2.1rem;height:2.1rem}.add.empty[_ngcontent-%COMP%]{width:7.1rem;height:auto}  .p-tabview .p-tabview-panels{background-color:transparent;padding:0;justify-content:center;height:100%;display:flex;flex-direction:column}  .p-tabview .p-tabview-nav{background:transparent;border:none}  .p-tabview .p-tabview-nav li .p-tabview-nav-link,   .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link{background:transparent;border:none}  .p-tabview .p-tabview-nav .p-tabview-ink-bar{background-color:#8a7af7;bottom:5px}\"]\n    });\n  }\n  return NotesListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}