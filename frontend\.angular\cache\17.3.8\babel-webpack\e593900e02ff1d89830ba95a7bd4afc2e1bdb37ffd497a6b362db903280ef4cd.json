{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { take } from 'rxjs/operators';\nimport { slideInOut } from 'src/app/helpers/my-animations';\nimport * as data from '../../../../core/models/data';\nimport { UserRole } from 'src/app/core/models/user.model';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/core/services/classroom.service\";\nimport * as i4 from \"src/app/core/services/lesson.service\";\nimport * as i5 from \"src/app/core/services/package.service\";\nimport * as i6 from \"src/app/core/services/note.service\";\nimport * as i7 from \"src/app/core/services/auth.service\";\nimport * as i8 from \"src/app/core/services/calendar.service\";\nimport * as i9 from \"../../../../shared/loader/loader.component\";\nimport * as i10 from \"../../../../shared/layout/g-level-circle/g-level-circle.component\";\nimport * as i11 from \"../../../../shared/layout/g-header-back-button/g-header-back-button.component\";\nconst _c0 = () => [\"lessons-menu-item-active\"];\nfunction ClassComponent_div_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵelement(2, \"g-level-circle\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scale\", 1)(\"name\", ctx_r1.classroom == null ? null : ctx_r1.classroom.activeLevel);\n  }\n}\nfunction ClassComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5)(2, \"div\", 6)(3, \"div\", 7)(4, \"g-header-back-button\", 8);\n    i0.ɵɵlistener(\"click\", function ClassComponent_div_2_Template_g_header_back_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBack());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"span\", 9)(6, \"span\", 10);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 11);\n    i0.ɵɵtemplate(9, ClassComponent_div_2_div_9_Template, 3, 2, \"div\", 12);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate3(\"\", ctx_r1.classroom.language, \" \", ctx_r1.classroom.type === \"Trial\" ? \"Trial\" : \"\", \" Classroom: \", ctx_r1.classroomService.getFormattedStudentNames(ctx_r1.classroom.classroomStudents), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isTrial);\n  }\n}\nfunction ClassComponent_ng_container_3_ng_container_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 23);\n  }\n}\nfunction ClassComponent_ng_container_3_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 20);\n    i0.ɵɵelement(2, \"img\", 21);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ClassComponent_ng_container_3_ng_container_4_div_4_Template, 1, 0, \"div\", 22);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const menuItem_r3 = ctx.$implicit;\n    const last_r4 = ctx.last;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", menuItem_r3.url)(\"routerLinkActive\", i0.ɵɵpureFunction0(5, _c0));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", menuItem_r3.icon, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", menuItem_r3.label, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !last_r4);\n  }\n}\nfunction ClassComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 16)(2, \"div\", 17)(3, \"div\", 18);\n    i0.ɵɵtemplate(4, ClassComponent_ng_container_3_ng_container_4_Template, 5, 6, \"ng-container\", 19);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.classMenuItems);\n  }\n}\nfunction ClassComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 24)(2, \"div\", 25);\n    i0.ɵɵelement(3, \"app-loader\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"scale\", 1.6);\n  }\n}\nfunction ClassComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"router-outlet\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nexport class ClassComponent {\n  constructor(location, router, activatedRoute, classroomService, lessonService, packageService, noteService, authService, calendarService) {\n    this.location = location;\n    this.router = router;\n    this.activatedRoute = activatedRoute;\n    this.classroomService = classroomService;\n    this.lessonService = lessonService;\n    this.packageService = packageService;\n    this.noteService = noteService;\n    this.authService = authService;\n    this.calendarService = calendarService;\n    this.subs = new SubSink();\n    this.classroomLessons = [];\n    this.classroomTitle = \"\";\n    this.showDetails = false;\n    // showDetails: boolean = false;\n    this.showInfo = true;\n    // showInfo: boolean = true;\n    this.showReports = false;\n    this.showRatings = false;\n    // showRatings: boolean = true;\n    this.isTrial = false;\n    this.classroomPackages = [];\n    this.showTabLinks = true;\n    this.isLoadingClassroom = true;\n    this.classMenuItems = [];\n    this.dummyLesson = data.dummyLesson.classroom;\n  }\n  ngOnInit() {\n    // let routeExtras = this.router.getCurrentNavigation()!.extras.state;\n    this.classroomId = this.activatedRoute.snapshot.paramMap.get(\"classroom_id\");\n    // console.log(routeExtras);\n    this.getClassroomPackages();\n    this.activatedRoute.params.pipe(take(1)).subscribe(params => this.isTrial = params['isTrial']);\n    // if (this.classroomService.sessionUserClassrooms.length > 0) {\n    //   this.classroom = this.classroomService.sessionUserClassrooms.filter(el => el.id == this.classroomId)[0]\n    //   this.classroomTitle = this.classroomService.getClassroomTitle(this.classroom);\n    // }\n    this.fetchClassroom();\n    this.initUpdateListener();\n    // this.router.events.subscribe(event => {\n    //   if (event instanceof NavigationStart) {\n    //     console.log(event.url.includes('teacher'));\n    //     if (event.url.includes('teacher')) {\n    //       this.showTabLinks = false;\n    //     } else {\n    //       this.showTabLinks = true;\n    //     }\n    //   }\n    // });\n    // this.activatedRoute.url.subscribe(url => {\n    //   console.log(url);\n    //   // Check if the current route is '/dashboard/classrooms/lessons/2/info'\n    //   if (url[0].path === 'dashboard' && url[1].path === 'classrooms' && url[2].path === 'lessons' && url[4].path !== 'teacher') {\n    //     this.showTabLinks = true;\n    //   } else {\n    //     this.showTabLinks = false;\n    //   }\n    // });\n  }\n  updateTabLinks() {\n    this.activatedRoute.firstChild.data.subscribe(data => {\n      console.log(data);\n      this.showTabLinks = data.showTabs;\n      if (this.activatedRoute.firstChild.snapshot.children.length > 0) {\n        this.activatedRoute.firstChild.firstChild.data.subscribe(childData => {\n          this.showTabLinks = childData.showTabs;\n          // Use childData to control visibility of specific tabs\n        });\n      }\n    });\n  }\n  ngAfterContentInit() {}\n  ngOnDestroy() {\n    // TODO: check if we need to clear the selected classroom from service \n    // this.classroomService.setSelectedClassroom({} as Classroom);\n    this.subs.unsubscribe();\n    this.classroomService.setSelectedClassroomUpdate(false);\n  }\n  goBack() {\n    this.location.back();\n  }\n  goToDetails() {\n    // this.showDetails = true;\n    // this.showInfo = false;\n    // this.showReports = false;\n    // this.showRatings = false;\n    this.router.navigate(['/dashboard/classrooms/lessons', this.classroom.id, 'details']);\n    // this.router.navigateByUrl('/dashboard/classrooms/lessons/2/details', {replaceUrl: true});\n  }\n  info() {\n    // this.showDetails = false;\n    // this.showInfo = true;\n    // this.showReports = false;\n    // this.showRatings = false;\n    this.router.navigate(['/dashboard/classrooms/lessons', 2, 'info']);\n    // this.router.navigateByUrl('/dashboard/classrooms/lessons/2/info', {replaceUrl: true});\n  }\n  reports() {\n    this.showDetails = false;\n    this.showInfo = false;\n    this.showReports = true;\n    this.showRatings = false;\n    this.router.navigateByUrl('/dashboard/classrooms/lessons/2/details', {\n      replaceUrl: true\n    });\n  }\n  ratings() {\n    this.showDetails = false;\n    this.showInfo = false;\n    this.showReports = false;\n    this.showRatings = true;\n  }\n  getClassroomPackages() {\n    this.packageService.getClassroomPackages(this.classroomId).subscribe(res => {\n      this.classroomPackages = res;\n    });\n  }\n  isActiveUrl() {\n    return this.router.url.startsWith(this.router.url);\n  }\n  getClassMenuItems(classroomId, userRole) {\n    const menuItems = [{\n      label: 'Class',\n      icon: '/assets/icons/classroom/classroom-group-icon.svg',\n      url: `/dashboard/classrooms/lessons/${classroomId}/details`,\n      role: [UserRole.STUDENT, UserRole.TEACHER]\n    }, {\n      label: 'Info',\n      icon: '/assets/icons/classroom/info.svg',\n      url: `/dashboard/classrooms/lessons/${classroomId}/info`,\n      role: [UserRole.STUDENT, UserRole.TEACHER]\n    }\n    // {\n    //   label: 'Progress',\n    //   icon: '/assets/icons/classroom/progress.svg',\n    //   url: `/dashboard/classrooms/lessons/${classroomId}/progress`,\n    //   role: [UserRole.STUDENT],\n    // },\n    ];\n    return menuItems.filter(item => item.role.includes(userRole));\n  }\n  fetchClassroom() {\n    this.subs.add(this.classroomService.getClassroom(this.classroomId).pipe(take(1)).subscribe(res => {\n      this.isLoadingClassroom = false;\n      this.classroom = res;\n      this.classroomService.setSelectedClassroom(this.classroom);\n      this.classroomService.setSelectedClassroomUpdate(true);\n      console.log(res);\n      // this.noteService.setCurrentSelectedClassroomId(parseInt(this.classroom!.id))\n      this.classroomTitle = this.classroomService.getClassroomTitle(this.classroom);\n      this.isTrial = this.classroom.type === 'Trial';\n      // this.lessonService.getClassroomLessons(this.classroom.id).pipe(take(1)).subscribe(res => {\n      //   this.classroomLessons = res;\n      // });\n      this.updateTabLinks();\n      this.classMenuItems = this.getClassMenuItems(this.classroom.id, this.authService.getUserRole());\n      this.router.events.subscribe(event => {\n        if (event instanceof NavigationEnd) {\n          this.updateTabLinks();\n        }\n      });\n    }));\n  }\n  initUpdateListener() {\n    this.subs.add(this.calendarService.updateListener.subscribe(res => {\n      if (res) {\n        this.fetchClassroom();\n        this.getClassroomPackages();\n      }\n    }));\n  }\n  static #_ = this.ɵfac = function ClassComponent_Factory(t) {\n    return new (t || ClassComponent)(i0.ɵɵdirectiveInject(i1.Location), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ClassroomService), i0.ɵɵdirectiveInject(i4.LessonService), i0.ɵɵdirectiveInject(i5.PackageService), i0.ɵɵdirectiveInject(i6.NoteService), i0.ɵɵdirectiveInject(i7.AuthService), i0.ɵɵdirectiveInject(i8.CalendarService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ClassComponent,\n    selectors: [[\"app-class\"]],\n    decls: 6,\n    vars: 4,\n    consts: [[1, \"card\", \"mb-4\"], [1, \"border-round-xl\"], [\"class\", \"\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"\"], [1, \"border-round-xl\", \"overflow-hidden\"], [1, \"block-header\", \"gradient-header\", \"relative\", \"justify-content-center\", \"lg:flex\"], [1, \"absolute\", \"pl-1\", \"md:pl-2\", \"left-0\"], [3, \"click\"], [1, \"block-title\", \"pl-2\", \"sm:pl-0\"], [1, \"text-0\", \"capitalize\", \"max-w-30rem\"], [1, \"flex\", \"justify-content-end\"], [\"class\", \"lesson-level\", 4, \"ngIf\"], [1, \"lesson-level\"], [1, \"absolute\", \"right-1\", \"v-align\", 2, \"right\", \"10px\"], [3, \"scale\", \"name\"], [1, \"lessons\", \"mt-4\"], [\"id\", \"class\"], [1, \"lessons-menu\", \"flex\", \"align-items-center\"], [4, \"ngFor\", \"ngForOf\"], [1, \"lessons-menu-item\", \"flex\", \"align-items-center\", \"gap-2\", \"text-primary\", 3, \"routerLink\", \"routerLinkActive\"], [3, \"src\"], [\"class\", \"seperator\", 4, \"ngIf\"], [1, \"seperator\"], [1, \"relative\", \"h-20rem\"], [1, \"abs-centered\"], [3, \"scale\"]],\n    template: function ClassComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵtemplate(2, ClassComponent_div_2_Template, 10, 4, \"div\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(3, ClassComponent_ng_container_3_Template, 5, 1, \"ng-container\", 3)(4, ClassComponent_ng_container_4_Template, 4, 1, \"ng-container\", 3)(5, ClassComponent_ng_container_5_Template, 2, 0, \"ng-container\", 3);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.classroom);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showTabLinks && ctx.classMenuItems.length > 0 && !ctx.isTrial);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoadingClassroom);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingClassroom);\n      }\n    },\n    dependencies: [i1.NgForOf, i1.NgIf, i2.RouterOutlet, i2.RouterLink, i2.RouterLinkActive, i9.LoaderComponent, i10.GLevelCircleComponent, i11.GHeaderBackButtonComponent],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.gradient-header[_ngcontent-%COMP%] {\\n  background-image: url(\\\"/assets/images/classroom-gradient-bg.png\\\");\\n  background-repeat: no-repeat;\\n  background-position: center center;\\n  min-height: 60px;\\n  border-radius: 8px;\\n  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);\\n}\\n\\n.lessons[_ngcontent-%COMP%] {\\n  width: 100%;\\n  font-size: 17px;\\n}\\n\\n.lessons-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  width: 100%;\\n  font-family: \\\"Raleway-Bold\\\";\\n  padding: 15px 0;\\n}\\n.lessons-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  cursor: pointer;\\n}\\n.lessons-header[_ngcontent-%COMP%]   .classroom-title[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.lessons-header[_ngcontent-%COMP%]   .lesson-level[_ngcontent-%COMP%] {\\n  background-color: var(--main-color);\\n  color: var(--white);\\n  text-align: center;\\n  transition: all 0.3s linear;\\n  width: 33px;\\n  height: 33px;\\n  line-height: 33px;\\n  border-radius: 50%;\\n}\\n\\n.lessons-menu[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n.lessons-menu[_ngcontent-%COMP%]   .lessons-menu-item[_ngcontent-%COMP%] {\\n  color: var(--main-color);\\n  cursor: pointer;\\n  text-align: center;\\n  position: relative;\\n  padding: 0 12px;\\n}\\n.lessons-menu[_ngcontent-%COMP%]   .lessons-menu-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 16px;\\n}\\n.lessons-menu[_ngcontent-%COMP%]   .lessons-menu-item-active[_ngcontent-%COMP%], .lessons-menu[_ngcontent-%COMP%]   .lessons-menu-item[_ngcontent-%COMP%]:hover {\\n  font-weight: bold;\\n}\\n.lessons-menu[_ngcontent-%COMP%]   .lessons-menu-item-active[_ngcontent-%COMP%]::after, .lessons-menu[_ngcontent-%COMP%]   .lessons-menu-item[_ngcontent-%COMP%]:hover::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -5px;\\n  border-bottom: 1px solid var(--primary-color);\\n  height: 2px;\\n  width: 100%;\\n  left: 0;\\n}\\n.lessons-menu[_ngcontent-%COMP%]   .seperator[_ngcontent-%COMP%] {\\n  height: 20px;\\n  width: 1px;\\n  background-color: var(--primary-color);\\n  margin: 0 14px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n    data: {\n      animation: [slideInOut]\n    }\n  });\n}", "map": {"version": 3, "names": ["NavigationEnd", "take", "slideInOut", "data", "UserRole", "SubSink", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r1", "classroom", "activeLevel", "ɵɵlistener", "ClassComponent_div_2_Template_g_header_back_button_click_4_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "goBack", "ɵɵtext", "ɵɵtemplate", "ClassComponent_div_2_div_9_Template", "ɵɵtextInterpolate3", "language", "type", "classroomService", "getFormattedStudentNames", "classroomStudents", "isTrial", "ɵɵelementContainerStart", "ClassComponent_ng_container_3_ng_container_4_div_4_Template", "menuItem_r3", "url", "ɵɵpureFunction0", "_c0", "icon", "ɵɵsanitizeUrl", "ɵɵtextInterpolate1", "label", "last_r4", "ClassComponent_ng_container_3_ng_container_4_Template", "classMenuItems", "ClassComponent", "constructor", "location", "router", "activatedRoute", "lessonService", "packageService", "noteService", "authService", "calendarService", "subs", "classroomLessons", "classroomTitle", "showDetails", "showInfo", "showReports", "showRatings", "classroomPackages", "showTabLinks", "isLoadingClassroom", "du<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "classroomId", "snapshot", "paramMap", "get", "getClassroomPackages", "params", "pipe", "subscribe", "fetchClassroom", "initUpdateListener", "updateTabLinks", "<PERSON><PERSON><PERSON><PERSON>", "console", "log", "showTabs", "children", "length", "childData", "ngAfterContentInit", "ngOnDestroy", "unsubscribe", "setSelectedClassroomUpdate", "back", "goToDetails", "navigate", "id", "info", "reports", "navigateByUrl", "replaceUrl", "ratings", "res", "isActiveUrl", "startsWith", "getClassMenuItems", "userRole", "menuItems", "role", "STUDENT", "TEACHER", "filter", "item", "includes", "add", "getClassroom", "setSelectedClassroom", "getClassroomTitle", "getUserRole", "events", "event", "updateListener", "_", "ɵɵdirectiveInject", "i1", "Location", "i2", "Router", "ActivatedRoute", "i3", "ClassroomService", "i4", "LessonService", "i5", "PackageService", "i6", "NoteService", "i7", "AuthService", "i8", "CalendarService", "_2", "selectors", "decls", "vars", "consts", "template", "ClassComponent_Template", "rf", "ctx", "ClassComponent_div_2_Template", "ClassComponent_ng_container_3_Template", "ClassComponent_ng_container_4_Template", "ClassComponent_ng_container_5_Template"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\classrooms\\class\\class.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\classrooms\\class\\class.component.html"], "sourcesContent": ["import { Location } from '@angular/common';\r\nimport { Component, OnDestroy, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, NavigationEnd, NavigationStart, Params, Router } from '@angular/router';\r\nimport { take } from 'rxjs/operators';\r\nimport { Classroom } from 'src/app/core/models/classroom.model';\r\nimport { Lesson } from 'src/app/core/models/lesson.model';\r\nimport { Package } from 'src/app/core/models/package.model';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { LessonService } from 'src/app/core/services/lesson.service';\r\nimport { NoteService } from 'src/app/core/services/note.service';\r\nimport { PackageService } from 'src/app/core/services/package.service';\r\nimport { slideInOut } from 'src/app/helpers/my-animations';\r\nimport * as data from '../../../../core/models/data';\r\nimport { UserRole } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { CalendarService } from 'src/app/core/services/calendar.service';\r\nimport { SubSink } from 'subsink';\r\n\r\n\r\n@Component({\r\n  selector: 'app-class',\r\n  templateUrl: './class.component.html',\r\n  styleUrls: ['./class.component.scss'],\r\n  animations: [slideInOut]\r\n})\r\nexport class ClassComponent implements OnInit, OnDestroy {\r\n  private subs = new SubSink();\r\n  private classroomId?: string;\r\n  public classroom?: any;\r\n  public classroomLessons: Lesson[] = [];\r\n  public classroomTitle: string = \"\";\r\n  showDetails: boolean = false;\r\n  // showDetails: boolean = false;\r\n  showInfo: boolean = true;\r\n  // showInfo: boolean = true;\r\n  showReports: boolean = false;\r\n  showRatings: boolean = false;\r\n  // showRatings: boolean = true;\r\n  isTrial: boolean = false;\r\n  classroomPackages: Package[] = []\r\n  showTabLinks = true;\r\n  isLoadingClassroom = true;\r\n  \r\n  classMenuItems: any[] = [];\r\n  dummyLesson = data.dummyLesson.classroom;\r\n  constructor(\r\n    private location: Location,\r\n    private router: Router,\r\n    private activatedRoute: ActivatedRoute,\r\n    private classroomService: ClassroomService,\r\n    private lessonService: LessonService,\r\n    private packageService: PackageService,\r\n    private noteService: NoteService,\r\n    private authService: AuthService,\r\n    private calendarService: CalendarService,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    // let routeExtras = this.router.getCurrentNavigation()!.extras.state;\r\n    this.classroomId = this.activatedRoute.snapshot.paramMap.get(\"classroom_id\")!;\r\n    // console.log(routeExtras);\r\n    this.getClassroomPackages();\r\n    this.activatedRoute.params.pipe(take(1)).subscribe((params: Params) => this.isTrial = params['isTrial']);\r\n    // if (this.classroomService.sessionUserClassrooms.length > 0) {\r\n    //   this.classroom = this.classroomService.sessionUserClassrooms.filter(el => el.id == this.classroomId)[0]\r\n    //   this.classroomTitle = this.classroomService.getClassroomTitle(this.classroom);\r\n    // }\r\n\r\n    this.fetchClassroom();\r\n    this.initUpdateListener();\r\n    // this.router.events.subscribe(event => {\r\n    //   if (event instanceof NavigationStart) {\r\n    //     console.log(event.url.includes('teacher'));\r\n    //     if (event.url.includes('teacher')) {\r\n    //       this.showTabLinks = false;\r\n    //     } else {\r\n    //       this.showTabLinks = true;\r\n    //     }\r\n    //   }\r\n    // });\r\n    // this.activatedRoute.url.subscribe(url => {\r\n    //   console.log(url);\r\n    //   // Check if the current route is '/dashboard/classrooms/lessons/2/info'\r\n    //   if (url[0].path === 'dashboard' && url[1].path === 'classrooms' && url[2].path === 'lessons' && url[4].path !== 'teacher') {\r\n    //     this.showTabLinks = true;\r\n    //   } else {\r\n    //     this.showTabLinks = false;\r\n    //   }\r\n    // });\r\n  }\r\n  updateTabLinks() {\r\n    this.activatedRoute.firstChild!.data.subscribe(data => {\r\n      console.log(data);\r\n      this.showTabLinks = data.showTabs;\r\n      if (this.activatedRoute.firstChild!.snapshot.children.length > 0) {\r\n        this.activatedRoute.firstChild!.firstChild!.data.subscribe(childData => {\r\n          this.showTabLinks = childData.showTabs;\r\n          // Use childData to control visibility of specific tabs\r\n        });\r\n      }\r\n    });\r\n  }\r\n  ngAfterContentInit() {\r\n\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    // TODO: check if we need to clear the selected classroom from service \r\n    // this.classroomService.setSelectedClassroom({} as Classroom);\r\n    this.subs.unsubscribe();\r\n    this.classroomService.setSelectedClassroomUpdate(false);\r\n  }\r\n\r\n  goBack() {\r\n    this.location.back();\r\n  }\r\n\r\n  goToDetails() {\r\n    // this.showDetails = true;\r\n    // this.showInfo = false;\r\n    // this.showReports = false;\r\n    // this.showRatings = false;\r\n    this.router.navigate(['/dashboard/classrooms/lessons', this.classroom.id, 'details']);\r\n    // this.router.navigateByUrl('/dashboard/classrooms/lessons/2/details', {replaceUrl: true});\r\n  }\r\n\r\n  info() {\r\n    // this.showDetails = false;\r\n    // this.showInfo = true;\r\n    // this.showReports = false;\r\n    // this.showRatings = false;\r\n    this.router.navigate(['/dashboard/classrooms/lessons', 2, 'info']);\r\n    // this.router.navigateByUrl('/dashboard/classrooms/lessons/2/info', {replaceUrl: true});\r\n  }\r\n\r\n  reports() {\r\n    this.showDetails = false;\r\n    this.showInfo = false;\r\n    this.showReports = true;\r\n    this.showRatings = false;\r\n    this.router.navigateByUrl('/dashboard/classrooms/lessons/2/details', {replaceUrl: true});\r\n  }\r\n\r\n  ratings() {\r\n    this.showDetails = false;\r\n    this.showInfo = false;\r\n    this.showReports = false;\r\n    this.showRatings = true;\r\n  }\r\n\r\n  getClassroomPackages() {\r\n    this.packageService.getClassroomPackages(this.classroomId!).subscribe(res => {\r\n      this.classroomPackages = res\r\n    })\r\n  }\r\n  \r\n  isActiveUrl(): boolean {\r\n    return this.router.url.startsWith(this.router.url);\r\n  }\r\n\r\n  getClassMenuItems(classroomId: string, userRole: UserRole): any[] {\r\n    const menuItems = [\r\n      {\r\n        label: 'Class',\r\n        icon: '/assets/icons/classroom/classroom-group-icon.svg',\r\n        url: `/dashboard/classrooms/lessons/${classroomId}/details`,\r\n        role: [UserRole.STUDENT, UserRole.TEACHER],\r\n      },\r\n      {\r\n        label: 'Info',\r\n        icon: '/assets/icons/classroom/info.svg',\r\n        url: `/dashboard/classrooms/lessons/${classroomId}/info`,\r\n        role: [UserRole.STUDENT, UserRole.TEACHER],\r\n      },\r\n      // {\r\n      //   label: 'Progress',\r\n      //   icon: '/assets/icons/classroom/progress.svg',\r\n      //   url: `/dashboard/classrooms/lessons/${classroomId}/progress`,\r\n      //   role: [UserRole.STUDENT],\r\n      // },\r\n    ];\r\n  \r\n    return menuItems.filter(item => item.role.includes(userRole));\r\n  }\r\n\r\n  private fetchClassroom() {\r\n    this.subs.add(this.classroomService.getClassroom(this.classroomId!).pipe(take(1)).subscribe(res => {\r\n\r\n      this.isLoadingClassroom = false;\r\n      this.classroom = res;\r\n      this.classroomService.setSelectedClassroom(this.classroom);\r\n      this.classroomService.setSelectedClassroomUpdate(true);\r\n      console.log(res);\r\n      // this.noteService.setCurrentSelectedClassroomId(parseInt(this.classroom!.id))\r\n      this.classroomTitle = this.classroomService.getClassroomTitle(this.classroom);\r\n      this.isTrial = this.classroom.type === 'Trial';\r\n      // this.lessonService.getClassroomLessons(this.classroom.id).pipe(take(1)).subscribe(res => {\r\n      //   this.classroomLessons = res;\r\n      // });\r\n      this.updateTabLinks();\r\n      this.classMenuItems = this.getClassMenuItems(this.classroom.id, this.authService.getUserRole());\r\n      this.router.events.subscribe(event => {\r\n        if (event instanceof NavigationEnd) {\r\n          this.updateTabLinks();\r\n        }\r\n      });\r\n    }));\r\n  }\r\n\r\n  private initUpdateListener() {\r\n    this.subs.add(this.calendarService.updateListener.subscribe((res: any) => {\r\n      if (res) {\r\n        this.fetchClassroom();\r\n        this.getClassroomPackages();\r\n      }\r\n    }));\r\n  }\r\n}\r\n", "\r\n<div class=\"card mb-4\">\r\n<div class=\"border-round-xl\">\r\n    <div *ngIf=\"classroom\" class=\"\">\r\n        <div class=\"border-round-xl  overflow-hidden\">\r\n            <div class=\"block-header gradient-header  relative justify-content-center  lg:flex\">\r\n                <div class=\"absolute pl-1 md:pl-2 left-0\">\r\n                   <g-header-back-button (click)=\"goBack()\"></g-header-back-button>\r\n                </div>\r\n                <span class=\"block-title pl-2 sm:pl-0\">\r\n                    <span class=\"text-0 capitalize max-w-30rem\">{{classroom.language}} {{classroom.type === 'Trial' ? 'Trial' : ''}} Classroom:\r\n                        {{classroomService.getFormattedStudentNames(classroom.classroomStudents)}}\r\n                    </span>\r\n                </span>\r\n                <div class=\"flex justify-content-end\">\r\n                    <div *ngIf=\"!isTrial\" class=\"lesson-level\">\r\n                        <div class=\"absolute right-1 v-align\" style=\"right: 10px;\">\r\n                            <g-level-circle [scale]=\"1\" [name]=\"classroom?.activeLevel\"></g-level-circle>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n\r\n<ng-container *ngIf=\"showTabLinks && (classMenuItems.length > 0) && !isTrial\">\r\n    <div class=\"lessons mt-4\">\r\n        <div id=\"class\">\r\n            <!-- <app-next-lesson *ngIf=\"!isTrial\"></app-next-lesson> -->\r\n            <div class=\"lessons-menu flex align-items-center\">\r\n                <ng-container *ngFor=\"let menuItem of classMenuItems; let last = last\">\r\n                    <a class=\"lessons-menu-item flex align-items-center gap-2 text-primary\" [routerLink]=\"menuItem.url\"\r\n                        [routerLinkActive]=\"['lessons-menu-item-active']\"><img\r\n                            [src]=\"menuItem.icon\"> {{menuItem.label}}</a>\r\n                    <div class=\"seperator\" *ngIf=\"!last\"></div>\r\n                </ng-container>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</ng-container>\r\n\r\n<ng-container *ngIf=\"isLoadingClassroom\">\r\n    <div class=\"relative h-20rem\">\r\n        <div class=\"abs-centered\">\r\n            <app-loader [scale]=\"1.6\"></app-loader>\r\n        </div>\r\n    </div>\r\n</ng-container>\r\n\r\n<ng-container *ngIf=\"!isLoadingClassroom\">\r\n    <router-outlet></router-outlet>\r\n</ng-container>\r\n</div>"], "mappings": "AAEA,SAAyBA,aAAa,QAAyC,iBAAiB;AAChG,SAASC,IAAI,QAAQ,gBAAgB;AAQrC,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,OAAO,KAAKC,IAAI,MAAM,8BAA8B;AACpD,SAASC,QAAQ,QAAQ,gCAAgC;AAGzD,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;;;;;ICATC,EADJ,CAAAC,cAAA,cAA2C,cACoB;IACvDD,EAAA,CAAAE,SAAA,yBAA6E;IAErFF,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IAFkBH,EAAA,CAAAI,SAAA,GAAW;IAACJ,EAAZ,CAAAK,UAAA,YAAW,SAAAC,MAAA,CAAAC,SAAA,kBAAAD,MAAA,CAAAC,SAAA,CAAAC,WAAA,CAAgC;;;;;;IAVpER,EAJf,CAAAC,cAAA,aAAgC,aACkB,aAC0C,aACtC,8BACE;IAAnBD,EAAA,CAAAS,UAAA,mBAAAC,oEAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAN,MAAA,GAAAN,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASR,MAAA,CAAAS,MAAA,EAAQ;IAAA,EAAC;IAC3Cf,EAD4C,CAAAG,YAAA,EAAuB,EAC7D;IAEFH,EADJ,CAAAC,cAAA,cAAuC,eACS;IAAAD,EAAA,CAAAgB,MAAA,GAE5C;IACJhB,EADI,CAAAG,YAAA,EAAO,EACJ;IACPH,EAAA,CAAAC,cAAA,cAAsC;IAClCD,EAAA,CAAAiB,UAAA,IAAAC,mCAAA,kBAA2C;IAQ3DlB,EAHY,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ;;;;IAbsDH,EAAA,CAAAI,SAAA,GAE5C;IAF4CJ,EAAA,CAAAmB,kBAAA,KAAAb,MAAA,CAAAC,SAAA,CAAAa,QAAA,OAAAd,MAAA,CAAAC,SAAA,CAAAc,IAAA,6CAAAf,MAAA,CAAAgB,gBAAA,CAAAC,wBAAA,CAAAjB,MAAA,CAAAC,SAAA,CAAAiB,iBAAA,OAE5C;IAGMxB,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAK,UAAA,UAAAC,MAAA,CAAAmB,OAAA,CAAc;;;;;IAoBpBzB,EAAA,CAAAE,SAAA,cAA2C;;;;;IAJ/CF,EAAA,CAAA0B,uBAAA,GAAuE;IACnE1B,EAAA,CAAAC,cAAA,YACsD;IAAAD,EAAA,CAAAE,SAAA,cACxB;IAACF,EAAA,CAAAgB,MAAA,GAAkB;IAAAhB,EAAA,CAAAG,YAAA,EAAI;IACrDH,EAAA,CAAAiB,UAAA,IAAAU,2DAAA,kBAAqC;;;;;;IAHmC3B,EAAA,CAAAI,SAAA,EAA2B;IAC/FJ,EADoE,CAAAK,UAAA,eAAAuB,WAAA,CAAAC,GAAA,CAA2B,qBAAA7B,EAAA,CAAA8B,eAAA,IAAAC,GAAA,EAC9C;IAC7C/B,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAK,UAAA,QAAAuB,WAAA,CAAAI,IAAA,EAAAhC,EAAA,CAAAiC,aAAA,CAAqB;IAAEjC,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAkC,kBAAA,MAAAN,WAAA,CAAAO,KAAA,KAAkB;IACzBnC,EAAA,CAAAI,SAAA,EAAW;IAAXJ,EAAA,CAAAK,UAAA,UAAA+B,OAAA,CAAW;;;;;IATvDpC,EAAA,CAAA0B,uBAAA,GAA8E;IAIlE1B,EAHR,CAAAC,cAAA,cAA0B,cACN,cAEsC;IAC9CD,EAAA,CAAAiB,UAAA,IAAAoB,qDAAA,2BAAuE;IAQnFrC,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;;;;;IARyCH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,UAAA,YAAAC,MAAA,CAAAgC,cAAA,CAAmB;;;;;IAWtEtC,EAAA,CAAA0B,uBAAA,GAAyC;IAEjC1B,EADJ,CAAAC,cAAA,cAA8B,cACA;IACtBD,EAAA,CAAAE,SAAA,qBAAuC;IAE/CF,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IAFcH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAK,UAAA,cAAa;;;;;IAKrCL,EAAA,CAAA0B,uBAAA,GAA0C;IACtC1B,EAAA,CAAAE,SAAA,oBAA+B;;;;AD1BnC,OAAM,MAAOqC,cAAc;EAoBzBC,YACUC,QAAkB,EAClBC,MAAc,EACdC,cAA8B,EAC9BrB,gBAAkC,EAClCsB,aAA4B,EAC5BC,cAA8B,EAC9BC,WAAwB,EACxBC,WAAwB,EACxBC,eAAgC;IARhC,KAAAP,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAArB,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAsB,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IA5BjB,KAAAC,IAAI,GAAG,IAAIlD,OAAO,EAAE;IAGrB,KAAAmD,gBAAgB,GAAa,EAAE;IAC/B,KAAAC,cAAc,GAAW,EAAE;IAClC,KAAAC,WAAW,GAAY,KAAK;IAC5B;IACA,KAAAC,QAAQ,GAAY,IAAI;IACxB;IACA,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAAC,WAAW,GAAY,KAAK;IAC5B;IACA,KAAA9B,OAAO,GAAY,KAAK;IACxB,KAAA+B,iBAAiB,GAAc,EAAE;IACjC,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,kBAAkB,GAAG,IAAI;IAEzB,KAAApB,cAAc,GAAU,EAAE;IAC1B,KAAAqB,WAAW,GAAG9D,IAAI,CAAC8D,WAAW,CAACpD,SAAS;EAWpC;EAEJqD,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,WAAW,GAAG,IAAI,CAAClB,cAAc,CAACmB,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,cAAc,CAAE;IAC7E;IACA,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACtB,cAAc,CAACuB,MAAM,CAACC,IAAI,CAACxE,IAAI,CAAC,CAAC,CAAC,CAAC,CAACyE,SAAS,CAAEF,MAAc,IAAK,IAAI,CAACzC,OAAO,GAAGyC,MAAM,CAAC,SAAS,CAAC,CAAC;IACxG;IACA;IACA;IACA;IAEA,IAAI,CAACG,cAAc,EAAE;IACrB,IAAI,CAACC,kBAAkB,EAAE;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EACAC,cAAcA,CAAA;IACZ,IAAI,CAAC5B,cAAc,CAAC6B,UAAW,CAAC3E,IAAI,CAACuE,SAAS,CAACvE,IAAI,IAAG;MACpD4E,OAAO,CAACC,GAAG,CAAC7E,IAAI,CAAC;MACjB,IAAI,CAAC4D,YAAY,GAAG5D,IAAI,CAAC8E,QAAQ;MACjC,IAAI,IAAI,CAAChC,cAAc,CAAC6B,UAAW,CAACV,QAAQ,CAACc,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;QAChE,IAAI,CAAClC,cAAc,CAAC6B,UAAW,CAACA,UAAW,CAAC3E,IAAI,CAACuE,SAAS,CAACU,SAAS,IAAG;UACrE,IAAI,CAACrB,YAAY,GAAGqB,SAAS,CAACH,QAAQ;UACtC;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EACAI,kBAAkBA,CAAA,GAElB;EAEAC,WAAWA,CAAA;IACT;IACA;IACA,IAAI,CAAC/B,IAAI,CAACgC,WAAW,EAAE;IACvB,IAAI,CAAC3D,gBAAgB,CAAC4D,0BAA0B,CAAC,KAAK,CAAC;EACzD;EAEAnE,MAAMA,CAAA;IACJ,IAAI,CAAC0B,QAAQ,CAAC0C,IAAI,EAAE;EACtB;EAEAC,WAAWA,CAAA;IACT;IACA;IACA;IACA;IACA,IAAI,CAAC1C,MAAM,CAAC2C,QAAQ,CAAC,CAAC,+BAA+B,EAAE,IAAI,CAAC9E,SAAS,CAAC+E,EAAE,EAAE,SAAS,CAAC,CAAC;IACrF;EACF;EAEAC,IAAIA,CAAA;IACF;IACA;IACA;IACA;IACA,IAAI,CAAC7C,MAAM,CAAC2C,QAAQ,CAAC,CAAC,+BAA+B,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;IAClE;EACF;EAEAG,OAAOA,CAAA;IACL,IAAI,CAACpC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACb,MAAM,CAAC+C,aAAa,CAAC,yCAAyC,EAAE;MAACC,UAAU,EAAE;IAAI,CAAC,CAAC;EAC1F;EAEAC,OAAOA,CAAA;IACL,IAAI,CAACvC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,WAAW,GAAG,IAAI;EACzB;EAEAU,oBAAoBA,CAAA;IAClB,IAAI,CAACpB,cAAc,CAACoB,oBAAoB,CAAC,IAAI,CAACJ,WAAY,CAAC,CAACO,SAAS,CAACwB,GAAG,IAAG;MAC1E,IAAI,CAACpC,iBAAiB,GAAGoC,GAAG;IAC9B,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,OAAO,IAAI,CAACnD,MAAM,CAACb,GAAG,CAACiE,UAAU,CAAC,IAAI,CAACpD,MAAM,CAACb,GAAG,CAAC;EACpD;EAEAkE,iBAAiBA,CAAClC,WAAmB,EAAEmC,QAAkB;IACvD,MAAMC,SAAS,GAAG,CAChB;MACE9D,KAAK,EAAE,OAAO;MACdH,IAAI,EAAE,kDAAkD;MACxDH,GAAG,EAAE,iCAAiCgC,WAAW,UAAU;MAC3DqC,IAAI,EAAE,CAACpG,QAAQ,CAACqG,OAAO,EAAErG,QAAQ,CAACsG,OAAO;KAC1C,EACD;MACEjE,KAAK,EAAE,MAAM;MACbH,IAAI,EAAE,kCAAkC;MACxCH,GAAG,EAAE,iCAAiCgC,WAAW,OAAO;MACxDqC,IAAI,EAAE,CAACpG,QAAQ,CAACqG,OAAO,EAAErG,QAAQ,CAACsG,OAAO;;IAE3C;IACA;IACA;IACA;IACA;IACA;IAAA,CACD;IAED,OAAOH,SAAS,CAACI,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACJ,IAAI,CAACK,QAAQ,CAACP,QAAQ,CAAC,CAAC;EAC/D;EAEQ3B,cAAcA,CAAA;IACpB,IAAI,CAACpB,IAAI,CAACuD,GAAG,CAAC,IAAI,CAAClF,gBAAgB,CAACmF,YAAY,CAAC,IAAI,CAAC5C,WAAY,CAAC,CAACM,IAAI,CAACxE,IAAI,CAAC,CAAC,CAAC,CAAC,CAACyE,SAAS,CAACwB,GAAG,IAAG;MAEhG,IAAI,CAAClC,kBAAkB,GAAG,KAAK;MAC/B,IAAI,CAACnD,SAAS,GAAGqF,GAAG;MACpB,IAAI,CAACtE,gBAAgB,CAACoF,oBAAoB,CAAC,IAAI,CAACnG,SAAS,CAAC;MAC1D,IAAI,CAACe,gBAAgB,CAAC4D,0BAA0B,CAAC,IAAI,CAAC;MACtDT,OAAO,CAACC,GAAG,CAACkB,GAAG,CAAC;MAChB;MACA,IAAI,CAACzC,cAAc,GAAG,IAAI,CAAC7B,gBAAgB,CAACqF,iBAAiB,CAAC,IAAI,CAACpG,SAAS,CAAC;MAC7E,IAAI,CAACkB,OAAO,GAAG,IAAI,CAAClB,SAAS,CAACc,IAAI,KAAK,OAAO;MAC9C;MACA;MACA;MACA,IAAI,CAACkD,cAAc,EAAE;MACrB,IAAI,CAACjC,cAAc,GAAG,IAAI,CAACyD,iBAAiB,CAAC,IAAI,CAACxF,SAAS,CAAC+E,EAAE,EAAE,IAAI,CAACvC,WAAW,CAAC6D,WAAW,EAAE,CAAC;MAC/F,IAAI,CAAClE,MAAM,CAACmE,MAAM,CAACzC,SAAS,CAAC0C,KAAK,IAAG;QACnC,IAAIA,KAAK,YAAYpH,aAAa,EAAE;UAClC,IAAI,CAAC6E,cAAc,EAAE;QACvB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;EACL;EAEQD,kBAAkBA,CAAA;IACxB,IAAI,CAACrB,IAAI,CAACuD,GAAG,CAAC,IAAI,CAACxD,eAAe,CAAC+D,cAAc,CAAC3C,SAAS,CAAEwB,GAAQ,IAAI;MACvE,IAAIA,GAAG,EAAE;QACP,IAAI,CAACvB,cAAc,EAAE;QACrB,IAAI,CAACJ,oBAAoB,EAAE;MAC7B;IACF,CAAC,CAAC,CAAC;EACL;EAAC,QAAA+C,CAAA,G;qBA/LUzE,cAAc,EAAAvC,EAAA,CAAAiH,iBAAA,CAAAC,EAAA,CAAAC,QAAA,GAAAnH,EAAA,CAAAiH,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAArH,EAAA,CAAAiH,iBAAA,CAAAG,EAAA,CAAAE,cAAA,GAAAtH,EAAA,CAAAiH,iBAAA,CAAAM,EAAA,CAAAC,gBAAA,GAAAxH,EAAA,CAAAiH,iBAAA,CAAAQ,EAAA,CAAAC,aAAA,GAAA1H,EAAA,CAAAiH,iBAAA,CAAAU,EAAA,CAAAC,cAAA,GAAA5H,EAAA,CAAAiH,iBAAA,CAAAY,EAAA,CAAAC,WAAA,GAAA9H,EAAA,CAAAiH,iBAAA,CAAAc,EAAA,CAAAC,WAAA,GAAAhI,EAAA,CAAAiH,iBAAA,CAAAgB,EAAA,CAAAC,eAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAd5F,cAAc;IAAA6F,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCvB3B1I,EADA,CAAAC,cAAA,aAAuB,aACM;QACzBD,EAAA,CAAAiB,UAAA,IAAA2H,6BAAA,kBAAgC;QAqBpC5I,EAAA,CAAAG,YAAA,EAAM;QA0BNH,EAxBA,CAAAiB,UAAA,IAAA4H,sCAAA,0BAA8E,IAAAC,sCAAA,0BAgBrC,IAAAC,sCAAA,0BAQC;QAG1C/I,EAAA,CAAAG,YAAA,EAAM;;;QAlDIH,EAAA,CAAAI,SAAA,GAAe;QAAfJ,EAAA,CAAAK,UAAA,SAAAsI,GAAA,CAAApI,SAAA,CAAe;QAuBVP,EAAA,CAAAI,SAAA,EAA6D;QAA7DJ,EAAA,CAAAK,UAAA,SAAAsI,GAAA,CAAAlF,YAAA,IAAAkF,GAAA,CAAArG,cAAA,CAAAuC,MAAA,SAAA8D,GAAA,CAAAlH,OAAA,CAA6D;QAgB7DzB,EAAA,CAAAI,SAAA,EAAwB;QAAxBJ,EAAA,CAAAK,UAAA,SAAAsI,GAAA,CAAAjF,kBAAA,CAAwB;QAQxB1D,EAAA,CAAAI,SAAA,EAAyB;QAAzBJ,EAAA,CAAAK,UAAA,UAAAsI,GAAA,CAAAjF,kBAAA,CAAyB;;;;;;iBD3B1B,CAAC9D,UAAU;IAAC;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}