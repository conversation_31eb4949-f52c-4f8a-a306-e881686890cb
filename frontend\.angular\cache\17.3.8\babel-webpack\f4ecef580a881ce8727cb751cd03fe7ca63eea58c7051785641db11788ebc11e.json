{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nexport let ToastService = /*#__PURE__*/(() => {\n  class ToastService {\n    constructor() {\n      this.subs = new SubSink();\n      this.showToastmessage$ = new BehaviorSubject({});\n      this.showToastmessage = this.showToastmessage$.asObservable();\n    }\n    setShowToastmessage(toastObject) {\n      this.showToastmessage$.next(toastObject);\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n    }\n    static #_ = this.ɵfac = function ToastService_Factory(t) {\n      return new (t || ToastService)();\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ToastService,\n      factory: ToastService.ɵfac\n    });\n  }\n  return ToastService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}