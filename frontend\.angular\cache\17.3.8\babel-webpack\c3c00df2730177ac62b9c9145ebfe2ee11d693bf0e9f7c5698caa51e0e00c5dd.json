{"ast": null, "code": "import { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, TemplateRef, Directive, Inject, Output, ContentChild, NgModule } from '@angular/core';\n\n/**\n * Defer postpones the loading the content that is initially not in the viewport until it becomes visible on scroll.\n * @group Components\n */\nclass DeferredLoader {\n  document;\n  platformId;\n  el;\n  renderer;\n  viewContainer;\n  cd;\n  /**\n   * Callback to invoke when deferred content is loaded.\n   * @param {Event} event - Browser event.\n   * @group Emits\n   */\n  onLoad = new EventEmitter();\n  template;\n  documentScrollListener;\n  view;\n  window;\n  constructor(document, platformId, el, renderer, viewContainer, cd) {\n    this.document = document;\n    this.platformId = platformId;\n    this.el = el;\n    this.renderer = renderer;\n    this.viewContainer = viewContainer;\n    this.cd = cd;\n    this.window = this.document.defaultView;\n  }\n  ngAfterViewInit() {\n    if (isPlatformBrowser(this.platformId)) {\n      if (this.shouldLoad()) {\n        this.load();\n      }\n      if (!this.isLoaded()) {\n        this.documentScrollListener = this.renderer.listen(this.window, 'scroll', () => {\n          if (this.shouldLoad()) {\n            this.load();\n            this.documentScrollListener && this.documentScrollListener();\n            this.documentScrollListener = null;\n          }\n        });\n      }\n    }\n  }\n  shouldLoad() {\n    if (this.isLoaded()) {\n      return false;\n    } else {\n      let rect = this.el.nativeElement.getBoundingClientRect();\n      let docElement = this.document.documentElement;\n      let winHeight = docElement.clientHeight;\n      return winHeight >= rect.top;\n    }\n  }\n  load() {\n    this.view = this.viewContainer.createEmbeddedView(this.template);\n    this.onLoad.emit();\n    this.cd.detectChanges();\n  }\n  isLoaded() {\n    return this.view != null && isPlatformBrowser(this.platformId);\n  }\n  ngOnDestroy() {\n    this.view = null;\n    if (this.documentScrollListener) {\n      this.documentScrollListener();\n    }\n  }\n  static ɵfac = function DeferredLoader_Factory(t) {\n    return new (t || DeferredLoader)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(PLATFORM_ID), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: DeferredLoader,\n    selectors: [[\"\", \"pDefer\", \"\"]],\n    contentQueries: function DeferredLoader_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, TemplateRef, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    outputs: {\n      onLoad: \"onLoad\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DeferredLoader, [{\n    type: Directive,\n    args: [{\n      selector: '[pDefer]',\n      host: {\n        class: 'p-element'\n      }\n    }]\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }], {\n    onLoad: [{\n      type: Output\n    }],\n    template: [{\n      type: ContentChild,\n      args: [TemplateRef]\n    }]\n  });\n})();\nclass DeferModule {\n  static ɵfac = function DeferModule_Factory(t) {\n    return new (t || DeferModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: DeferModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DeferModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule],\n      exports: [DeferredLoader],\n      declarations: [DeferredLoader]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DeferModule, DeferredLoader };", "map": {"version": 3, "names": ["isPlatformBrowser", "DOCUMENT", "CommonModule", "i0", "EventEmitter", "PLATFORM_ID", "TemplateRef", "Directive", "Inject", "Output", "ContentChild", "NgModule", "De<PERSON><PERSON><PERSON><PERSON>", "document", "platformId", "el", "renderer", "viewContainer", "cd", "onLoad", "template", "documentScrollListener", "view", "window", "constructor", "defaultView", "ngAfterViewInit", "shouldLoad", "load", "isLoaded", "listen", "rect", "nativeElement", "getBoundingClientRect", "doc<PERSON><PERSON>", "documentElement", "winHeight", "clientHeight", "top", "createEmbeddedView", "emit", "detectChanges", "ngOnDestroy", "ɵfac", "DeferredLoader_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "Renderer2", "ViewContainerRef", "ChangeDetectorRef", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "contentQueries", "DeferredLoader_ContentQueries", "rf", "ctx", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "outputs", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "class", "Document", "decorators", "undefined", "DeferModule", "DeferModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/primeng/fesm2022/primeng-defer.mjs"], "sourcesContent": ["import { isPlatformBrowser, DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, PLATFORM_ID, TemplateRef, Directive, Inject, Output, ContentChild, NgModule } from '@angular/core';\n\n/**\n * Defer postpones the loading the content that is initially not in the viewport until it becomes visible on scroll.\n * @group Components\n */\nclass DeferredLoader {\n    document;\n    platformId;\n    el;\n    renderer;\n    viewContainer;\n    cd;\n    /**\n     * Callback to invoke when deferred content is loaded.\n     * @param {Event} event - Browser event.\n     * @group Emits\n     */\n    onLoad = new EventEmitter();\n    template;\n    documentScrollListener;\n    view;\n    window;\n    constructor(document, platformId, el, renderer, viewContainer, cd) {\n        this.document = document;\n        this.platformId = platformId;\n        this.el = el;\n        this.renderer = renderer;\n        this.viewContainer = viewContainer;\n        this.cd = cd;\n        this.window = this.document.defaultView;\n    }\n    ngAfterViewInit() {\n        if (isPlatformBrowser(this.platformId)) {\n            if (this.shouldLoad()) {\n                this.load();\n            }\n            if (!this.isLoaded()) {\n                this.documentScrollListener = this.renderer.listen(this.window, 'scroll', () => {\n                    if (this.shouldLoad()) {\n                        this.load();\n                        this.documentScrollListener && this.documentScrollListener();\n                        this.documentScrollListener = null;\n                    }\n                });\n            }\n        }\n    }\n    shouldLoad() {\n        if (this.isLoaded()) {\n            return false;\n        }\n        else {\n            let rect = this.el.nativeElement.getBoundingClientRect();\n            let docElement = this.document.documentElement;\n            let winHeight = docElement.clientHeight;\n            return winHeight >= rect.top;\n        }\n    }\n    load() {\n        this.view = this.viewContainer.createEmbeddedView(this.template);\n        this.onLoad.emit();\n        this.cd.detectChanges();\n    }\n    isLoaded() {\n        return this.view != null && isPlatformBrowser(this.platformId);\n    }\n    ngOnDestroy() {\n        this.view = null;\n        if (this.documentScrollListener) {\n            this.documentScrollListener();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: DeferredLoader, deps: [{ token: DOCUMENT }, { token: PLATFORM_ID }, { token: i0.ElementRef }, { token: i0.Renderer2 }, { token: i0.ViewContainerRef }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.7\", type: DeferredLoader, selector: \"[pDefer]\", outputs: { onLoad: \"onLoad\" }, host: { classAttribute: \"p-element\" }, queries: [{ propertyName: \"template\", first: true, predicate: TemplateRef, descendants: true }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: DeferredLoader, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[pDefer]',\n                    host: {\n                        class: 'p-element'\n                    }\n                }]\n        }], ctorParameters: () => [{ type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.ElementRef }, { type: i0.Renderer2 }, { type: i0.ViewContainerRef }, { type: i0.ChangeDetectorRef }], propDecorators: { onLoad: [{\n                type: Output\n            }], template: [{\n                type: ContentChild,\n                args: [TemplateRef]\n            }] } });\nclass DeferModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: DeferModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.7\", ngImport: i0, type: DeferModule, declarations: [DeferredLoader], imports: [CommonModule], exports: [DeferredLoader] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: DeferModule, imports: [CommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: DeferModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [DeferredLoader],\n                    declarations: [DeferredLoader]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { DeferModule, DeferredLoader };\n"], "mappings": "AAAA,SAASA,iBAAiB,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,iBAAiB;AAC3E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,WAAW,EAAEC,WAAW,EAAEC,SAAS,EAAEC,MAAM,EAAEC,MAAM,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;;AAEzH;AACA;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACjBC,QAAQ;EACRC,UAAU;EACVC,EAAE;EACFC,QAAQ;EACRC,aAAa;EACbC,EAAE;EACF;AACJ;AACA;AACA;AACA;EACIC,MAAM,GAAG,IAAIf,YAAY,CAAC,CAAC;EAC3BgB,QAAQ;EACRC,sBAAsB;EACtBC,IAAI;EACJC,MAAM;EACNC,WAAWA,CAACX,QAAQ,EAAEC,UAAU,EAAEC,EAAE,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,EAAE,EAAE;IAC/D,IAAI,CAACL,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACK,MAAM,GAAG,IAAI,CAACV,QAAQ,CAACY,WAAW;EAC3C;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI1B,iBAAiB,CAAC,IAAI,CAACc,UAAU,CAAC,EAAE;MACpC,IAAI,IAAI,CAACa,UAAU,CAAC,CAAC,EAAE;QACnB,IAAI,CAACC,IAAI,CAAC,CAAC;MACf;MACA,IAAI,CAAC,IAAI,CAACC,QAAQ,CAAC,CAAC,EAAE;QAClB,IAAI,CAACR,sBAAsB,GAAG,IAAI,CAACL,QAAQ,CAACc,MAAM,CAAC,IAAI,CAACP,MAAM,EAAE,QAAQ,EAAE,MAAM;UAC5E,IAAI,IAAI,CAACI,UAAU,CAAC,CAAC,EAAE;YACnB,IAAI,CAACC,IAAI,CAAC,CAAC;YACX,IAAI,CAACP,sBAAsB,IAAI,IAAI,CAACA,sBAAsB,CAAC,CAAC;YAC5D,IAAI,CAACA,sBAAsB,GAAG,IAAI;UACtC;QACJ,CAAC,CAAC;MACN;IACJ;EACJ;EACAM,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACE,QAAQ,CAAC,CAAC,EAAE;MACjB,OAAO,KAAK;IAChB,CAAC,MACI;MACD,IAAIE,IAAI,GAAG,IAAI,CAAChB,EAAE,CAACiB,aAAa,CAACC,qBAAqB,CAAC,CAAC;MACxD,IAAIC,UAAU,GAAG,IAAI,CAACrB,QAAQ,CAACsB,eAAe;MAC9C,IAAIC,SAAS,GAAGF,UAAU,CAACG,YAAY;MACvC,OAAOD,SAAS,IAAIL,IAAI,CAACO,GAAG;IAChC;EACJ;EACAV,IAAIA,CAAA,EAAG;IACH,IAAI,CAACN,IAAI,GAAG,IAAI,CAACL,aAAa,CAACsB,kBAAkB,CAAC,IAAI,CAACnB,QAAQ,CAAC;IAChE,IAAI,CAACD,MAAM,CAACqB,IAAI,CAAC,CAAC;IAClB,IAAI,CAACtB,EAAE,CAACuB,aAAa,CAAC,CAAC;EAC3B;EACAZ,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACP,IAAI,IAAI,IAAI,IAAItB,iBAAiB,CAAC,IAAI,CAACc,UAAU,CAAC;EAClE;EACA4B,WAAWA,CAAA,EAAG;IACV,IAAI,CAACpB,IAAI,GAAG,IAAI;IAChB,IAAI,IAAI,CAACD,sBAAsB,EAAE;MAC7B,IAAI,CAACA,sBAAsB,CAAC,CAAC;IACjC;EACJ;EACA,OAAOsB,IAAI,YAAAC,uBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFjC,cAAc,EAAxBT,EAAE,CAAA2C,iBAAA,CAAwC7C,QAAQ,GAAlDE,EAAE,CAAA2C,iBAAA,CAA6DzC,WAAW,GAA1EF,EAAE,CAAA2C,iBAAA,CAAqF3C,EAAE,CAAC4C,UAAU,GAApG5C,EAAE,CAAA2C,iBAAA,CAA+G3C,EAAE,CAAC6C,SAAS,GAA7H7C,EAAE,CAAA2C,iBAAA,CAAwI3C,EAAE,CAAC8C,gBAAgB,GAA7J9C,EAAE,CAAA2C,iBAAA,CAAwK3C,EAAE,CAAC+C,iBAAiB;EAAA;EACvR,OAAOC,IAAI,kBAD8EhD,EAAE,CAAAiD,iBAAA;IAAAC,IAAA,EACJzC,cAAc;IAAA0C,SAAA;IAAAC,cAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA,EAAAC,QAAA;MAAA,IAAAF,EAAA;QADZtD,EAAE,CAAAyD,cAAA,CAAAD,QAAA,EACsKrD,WAAW;MAAA;MAAA,IAAAmD,EAAA;QAAA,IAAAI,EAAA;QADnL1D,EAAE,CAAA2D,cAAA,CAAAD,EAAA,GAAF1D,EAAE,CAAA4D,WAAA,QAAAL,GAAA,CAAAtC,QAAA,GAAAyC,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAC,SAAA;IAAAC,OAAA;MAAA/C,MAAA;IAAA;EAAA;AAE/F;AACA;EAAA,QAAAgD,SAAA,oBAAAA,SAAA,KAH6FhE,EAAE,CAAAiE,iBAAA,CAGJxD,cAAc,EAAc,CAAC;IAC5GyC,IAAI,EAAE9C,SAAS;IACf8D,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,UAAU;MACpBC,IAAI,EAAE;QACFC,KAAK,EAAE;MACX;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEnB,IAAI,EAAEoB,QAAQ;IAAEC,UAAU,EAAE,CAAC;MAC9CrB,IAAI,EAAE7C,MAAM;MACZ6D,IAAI,EAAE,CAACpE,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEoD,IAAI,EAAEsB,SAAS;IAAED,UAAU,EAAE,CAAC;MAClCrB,IAAI,EAAE7C,MAAM;MACZ6D,IAAI,EAAE,CAAChE,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAEgD,IAAI,EAAElD,EAAE,CAAC4C;EAAW,CAAC,EAAE;IAAEM,IAAI,EAAElD,EAAE,CAAC6C;EAAU,CAAC,EAAE;IAAEK,IAAI,EAAElD,EAAE,CAAC8C;EAAiB,CAAC,EAAE;IAAEI,IAAI,EAAElD,EAAE,CAAC+C;EAAkB,CAAC,CAAC,EAAkB;IAAE/B,MAAM,EAAE,CAAC;MAClJkC,IAAI,EAAE5C;IACV,CAAC,CAAC;IAAEW,QAAQ,EAAE,CAAC;MACXiC,IAAI,EAAE3C,YAAY;MAClB2D,IAAI,EAAE,CAAC/D,WAAW;IACtB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMsE,WAAW,CAAC;EACd,OAAOjC,IAAI,YAAAkC,oBAAAhC,CAAA;IAAA,YAAAA,CAAA,IAAwF+B,WAAW;EAAA;EAC9G,OAAOE,IAAI,kBAzB8E3E,EAAE,CAAA4E,gBAAA;IAAA1B,IAAA,EAyBSuB;EAAW;EAC/G,OAAOI,IAAI,kBA1B8E7E,EAAE,CAAA8E,gBAAA;IAAAC,OAAA,GA0BgChF,YAAY;EAAA;AAC3I;AACA;EAAA,QAAAiE,SAAA,oBAAAA,SAAA,KA5B6FhE,EAAE,CAAAiE,iBAAA,CA4BJQ,WAAW,EAAc,CAAC;IACzGvB,IAAI,EAAE1C,QAAQ;IACd0D,IAAI,EAAE,CAAC;MACCa,OAAO,EAAE,CAAChF,YAAY,CAAC;MACvBiF,OAAO,EAAE,CAACvE,cAAc,CAAC;MACzBwE,YAAY,EAAE,CAACxE,cAAc;IACjC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASgE,WAAW,EAAEhE,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}