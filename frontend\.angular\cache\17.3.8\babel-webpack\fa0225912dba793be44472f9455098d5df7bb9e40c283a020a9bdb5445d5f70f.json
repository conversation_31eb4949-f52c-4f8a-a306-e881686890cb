{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { SubSink } from 'subsink';\nimport * as moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/calendar.service\";\nimport * as i2 from \"src/app/core/services/auth.service\";\nimport * as i3 from \"src/app/core/services/classroom.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"../../../block-viewer/block-viewer.component\";\nconst _c0 = [\"daysTableWrapper\"];\nconst _c1 = (a0, a1, a2) => ({\n  \"reversed-gradient\": a0,\n  \"block-gradient\": a1,\n  \"availability-cal\": a2\n});\nconst _c2 = (a0, a1, a2, a3, a4, a5, a6, a7) => ({\n  \"active\": a0,\n  \"high-bg\": a1,\n  \"low-bg\": a2,\n  \"is-past\": a3,\n  \"no-bg\": a4,\n  \"circle\": a5,\n  \"today\": a6,\n  \"day-circle\": a7\n});\nconst _c3 = (a0, a1, a2, a3, a4, a5, a6) => ({\n  \"active\": a0,\n  \"high-bg\": a1,\n  \"low-bg\": a2,\n  \"is-past\": a3,\n  \"no-bg\": a4,\n  \"circle\": a5,\n  \"day-circle\": a6\n});\nfunction CalendarAgendaComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function CalendarAgendaComponent_ng_container_5_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBackwardMonth());\n    });\n    i0.ɵɵelement(2, \"span\", 10)(3, \"span\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function CalendarAgendaComponent_ng_container_5_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goForwardMonth());\n    });\n    i0.ɵɵelement(5, \"span\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction CalendarAgendaComponent_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"th\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const day_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(day_r3);\n  }\n}\nfunction CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_container_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 19);\n    i0.ɵɵelement(2, \"i\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_container_2_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 21)(2, \"div\", 22)(3, \"div\", 23)(4, \"div\", 24);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const status_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"top\", ctx_r1.getItemsPositionTop(i_r7), \"px\")(\"right\", ctx_r1.getItemsPositionRight(i_r7), \"px\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", status_r6.status + \"-gradient-lesson-bg\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", status_r6.count, \"\");\n  }\n}\nfunction CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 17)(2, \"div\", 18);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_container_2_ng_container_4_Template, 3, 0, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_container_2_ng_container_5_Template, 6, 6, \"ng-container\", 8);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const day_r5 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(day_r5.availability);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction8(6, _c2, ctx_r1.selectedDay === day_r5.number, ctx_r1.isHighAvailability(day_r5.number) && !ctx_r1.isDayInThePast(day_r5.number), !ctx_r1.isHighAvailability(day_r5.number) && ctx_r1.showAvailability && !ctx_r1.isDayInThePast(day_r5.number), ctx_r1.showAvailability && ctx_r1.isDayInThePast(day_r5.number), ctx_r1.isDayOff(day_r5.number), ctx_r1.showAvailability, !ctx_r1.showAvailability, ctx_r1.getStatusArray(ctx_r1.getTodaysLessons(day_r5.number)).length > 0 || !ctx_r1.showAvailability));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(day_r5.number);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedDay === day_r5.number && ctx_r1.showAvailability);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getStatusArray(ctx_r1.getTodaysLessons(day_r5.number)));\n  }\n}\nfunction CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_span_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 19);\n    i0.ɵɵelement(2, \"i\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 17)(1, \"div\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_span_0_ng_container_3_Template, 3, 0, \"ng-container\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(day_r5.availability);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction7(5, _c3, ctx_r1.selectedDay === day_r5.number, ctx_r1.isHighAvailability(day_r5.number) && !ctx_r1.isDayInThePast(day_r5.number), !ctx_r1.isHighAvailability(day_r5.number) && ctx_r1.showAvailability && !ctx_r1.isDayInThePast(day_r5.number), ctx_r1.showAvailability && ctx_r1.isDayInThePast(day_r5.number), ctx_r1.isDayOff(day_r5.number), ctx_r1.showAvailability, ctx_r1.getStatusArray(ctx_r1.getTodaysLessons(day_r5.number)).length > 0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(day_r5.number);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedDay === day_r5.number && ctx_r1.showAvailability);\n  }\n}\nfunction CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 21)(2, \"div\", 22)(3, \"div\", 23)(4, \"div\", 24);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const status_r8 = ctx.$implicit;\n    const i_r9 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"top\", ctx_r1.getItemsPositionTop(i_r9), \"px\")(\"right\", ctx_r1.getItemsPositionRight(i_r9), \"px\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", status_r8.status + \"-gradient-lesson-bg\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", status_r8.count, \"\");\n  }\n}\nfunction CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_ng_container_1_ng_container_1_Template, 6, 6, \"ng-container\", 8);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const day_r5 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getStatusArray(ctx_r1.getTodaysLessons(day_r5.number)));\n  }\n}\nfunction CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_span_0_Template, 4, 13, \"span\", 25)(1, CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_ng_container_1_Template, 2, 1, \"ng-container\", 5);\n  }\n  if (rf & 2) {\n    const day_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngIf\", day_r5.number);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", day_r5.number);\n  }\n}\nfunction CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"td\", 15);\n    i0.ɵɵlistener(\"click\", function CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_Template_td_click_1_listener() {\n      const day_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onDayClicked(day_r5.number));\n    });\n    i0.ɵɵtemplate(2, CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_container_2_Template, 6, 15, \"ng-container\", 16)(3, CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_Template, 2, 2, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const day_r5 = ctx.$implicit;\n    const elseBlock_r10 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isTodayDate(day_r5.number))(\"ngIfElse\", elseBlock_r10);\n  }\n}\nfunction CalendarAgendaComponent_ng_container_13_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"tr\");\n    i0.ɵɵtemplate(2, CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_Template, 5, 2, \"ng-container\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const week_r11 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", week_r11);\n  }\n}\nfunction CalendarAgendaComponent_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, CalendarAgendaComponent_ng_container_13_ng_container_1_Template, 3, 1, \"ng-container\", 8);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getWeeksInMonth(ctx_r1.currentDate));\n  }\n}\nexport let CalendarAgendaComponent = /*#__PURE__*/(() => {\n  class CalendarAgendaComponent {\n    constructor(cdr, elementRef, calendarService, authService, classroomService) {\n      this.cdr = cdr;\n      this.elementRef = elementRef;\n      this.calendarService = calendarService;\n      this.authService = authService;\n      this.classroomService = classroomService;\n      this.subs = new SubSink();\n      this.enableButtons = true;\n      this.reverseGradient = false;\n      this.title = 'Agenda';\n      this.lessons = [];\n      this.calHeaderClass = 'cal-header flex justify-content-center align-items-center';\n      this.currentSelectedDay = null;\n      this.userId = '';\n      this.showAvailability = false;\n      this.dayClicked = new EventEmitter();\n      this.agendaHeight = new EventEmitter();\n      this.currentMonthChanged = new EventEmitter();\n      this.weekdays = ['M', 'T', 'W', 'T', 'F', 'S', 'S'];\n      this.currentDate = new Date();\n      this.selectedDays = {};\n      this.monthAvailability = [];\n      this.loaded = false;\n    }\n    ngOnInit() {\n      if (this.currentSelectedDay) {\n        this.currentDate = new Date(this.currentSelectedDay);\n      } else {\n        this.currentDate = new Date();\n      }\n      this.user = this.authService.getLoggedInUser();\n      if (this.showAvailability) {\n        // this.loadAvailability();\n      } else {\n        this.loaded = true;\n      }\n    }\n    ngOnChanges(changes) {\n      if (this.lessons.length > 0) {\n        this.groupLessonsByDateAndStatus();\n      }\n      if (changes.userId && !changes.userId.firstChange) {\n        this.userId = changes.userId.currentValue;\n        if (changes.currentSelectedDay && changes.currentSelectedDay.currentValue) {\n          this.currentDate = changes.currentSelectedDay.currentValue;\n        }\n        this.loadAvailability();\n        this.cdr.detectChanges();\n      }\n      if (changes.currentSelectedDay && !changes.currentSelectedDay.firstChange) {\n        this.currentDate = changes.currentSelectedDay.currentValue;\n        this.currentDate.setMonth(changes.currentSelectedDay.currentValue.getMonth());\n        this.setSelectedDay(this.currentDate.getDate());\n        if (this.showAvailability) {\n          this.loadAvailability();\n        }\n        this.cdr.detectChanges();\n      }\n    }\n    ngAfterViewInit() {\n      this.calculateAgendaHeight();\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n    }\n    getDaysInMonth(date) {\n      const year = date.getFullYear();\n      const month = date.getMonth();\n      return new Date(year, month + 1, 0).getDate();\n    }\n    getWeeksInMonth(date) {\n      const weeks = [];\n      const startOfMonthDate = new Date(date.getFullYear(), date.getMonth(), 1);\n      const firstDayOfMonth = (startOfMonthDate.getDay() + 6) % 7; // 0 for Sunday, 1 for Monday, etc.\n      const daysInMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();\n      const startOfCalendarDate = new Date(date.getFullYear(), date.getMonth(), 1 - firstDayOfMonth);\n      const numWeeksInMonth = Math.ceil((firstDayOfMonth + daysInMonth) / 7); // Round up to ensure we show all days\n      const daysInCalendar = numWeeksInMonth * 7;\n      for (let i = 0; i < daysInCalendar; i += 7) {\n        const week = [];\n        for (let j = 0; j < 7; j++) {\n          const dayNumber = i + j + 1 - firstDayOfMonth;\n          const dayDate = new Date(date.getFullYear(), date.getMonth(), dayNumber);\n          const isPrevMonth = dayNumber < 1;\n          const isNextMonth = dayNumber > daysInMonth;\n          const isCurrentMonth = !isPrevMonth && !isNextMonth;\n          const day = {\n            number: isCurrentMonth ? dayNumber : '',\n            name: this.weekdays[j],\n            isPrevMonth,\n            isNextMonth,\n            isCurrentMonth\n          };\n          week.push(day);\n        }\n        weeks.push(week);\n      }\n      return weeks;\n    }\n    isTodayDate(day) {\n      const currentDate = moment();\n      const clickedDate = moment(this.currentDate).date(day);\n      return clickedDate.isSame(currentDate, 'day') && clickedDate.isSame(this.currentDate, 'month');\n    }\n    getCurrentMonth() {\n      this.calendarService.setCalendarAgendaMonthListener(moment(this.currentDate).toDate());\n      this.currentMonthChanged.emit(moment(this.currentDate).month());\n      return moment(this.currentDate).format('MMMM');\n    }\n    goForwardMonth() {\n      this.currentDate.setMonth(this.currentDate.getMonth() + 1);\n      this.checkAvailability();\n      this.calculateAgendaHeight();\n    }\n    goBackwardMonth() {\n      this.currentDate.setMonth(this.currentDate.getMonth() - 1);\n      this.checkAvailability();\n      this.calculateAgendaHeight();\n    }\n    setSelectedDay(day) {\n      this.selectedDays = {};\n      const key = `${this.currentDate.getFullYear()}-${this.currentDate.getMonth()}`;\n      this.selectedDays[key] = day;\n    }\n    get selectedDay() {\n      const key = `${this.currentDate.getFullYear()}-${this.currentDate.getMonth()}`;\n      return this.selectedDays[key];\n    }\n    onDayClicked(day) {\n      if (this.authService.isStudent) {\n        if (this.showAvailability && this.isDayInThePast(day)) {\n          return;\n        }\n      }\n      const clickedDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), day);\n      this.dayClicked.emit(clickedDate);\n      this.classroomService.setSelectedAgendaDate(clickedDate);\n      this.setSelectedDay(day);\n    }\n    isCurrentMonth(dayNumber) {\n      return new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), dayNumber).getMonth() === this.currentDate.getMonth();\n    }\n    isNextMonth(dayNumber) {\n      return new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), dayNumber).getMonth() === this.currentDate.getMonth() + 1;\n    }\n    loadAvailability() {\n      if (!this.userId) {\n        return;\n      }\n      if (this.showAvailability) {\n        const data = {\n          ...this.calculateMonthBounds(this.currentDate),\n          userId: this.userId\n        };\n        this.subs.sink = this.getAvailabilityFromRange(data).subscribe(res => {\n          this.monthAvailability = res.calendarAvailability;\n          this.daysOff = res.daysOff;\n          this.loaded = true;\n          this.cdr.detectChanges();\n        });\n      } else {\n        this.loaded = true;\n      }\n    }\n    isDayOff(dayNumber) {\n      if (this.showAvailability) {\n        const dateString = this.currentDate.getFullYear() + '-' + (this.currentDate.getMonth() + 1) + '-' + dayNumber;\n        const currentDate = new Date(dateString);\n        for (const dayOff of this.daysOff) {\n          const from = new Date(dayOff.period.from);\n          const to = new Date(dayOff.period.to);\n          if (currentDate >= from && currentDate <= to) {\n            return true;\n          }\n        }\n      }\n      return false;\n    }\n    isDayInThePast(dayNumber) {\n      const dateString = this.currentDate.getFullYear() + '-' + (this.currentDate.getMonth() + 1) + '-' + dayNumber;\n      const today = new Date();\n      const currentDate = new Date(dateString);\n      // Set hours, minutes, seconds, and milliseconds to 0\n      today.setHours(0, 0, 0, 0);\n      currentDate.setHours(0, 0, 0, 0);\n      return currentDate < today;\n    }\n    isHighAvailability(dayNumber) {\n      if (this.showAvailability) {\n        const dateString = `${this.currentDate.getFullYear()}-${this.currentDate.getMonth() + 1}-${dayNumber}`;\n        const currentDate = new Date(dateString);\n        const availability = this.monthAvailability.find(availability => {\n          const availabilityDateParts = availability.date.split('/').map(Number);\n          const availabilityDate = new Date(availabilityDateParts[2], availabilityDateParts[1] - 1, availabilityDateParts[0]);\n          return currentDate.toDateString() === availabilityDate.toDateString();\n        });\n        if (availability) {\n          return Math.abs(availability.availableHours) >= 4;\n        }\n      }\n      return false;\n    }\n    getTodaysLessons(day) {\n      const foundDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), day);\n      const lessonGroups = this.lessons.reduce((groups, lesson) => {\n        const date = new Date(lesson.startingDate).toLocaleDateString();\n        const status = lesson.status;\n        if (!groups[date]) {\n          groups[date] = {};\n        }\n        if (!groups[date][status]) {\n          groups[date][status] = {\n            count: 0\n          };\n        }\n        groups[date][status].count++;\n        return groups;\n      }, {});\n      const todaysLessons = lessonGroups[foundDate.toLocaleDateString()] || {};\n      const todaysLessonCounts = Object.keys(todaysLessons).reduce((counts, status) => {\n        counts[status] = todaysLessons[status].count;\n        return counts;\n      }, {});\n      return todaysLessonCounts;\n    }\n    /**\n     * Converts a status object to an array of objects with status and count properties.\n     * @param {Object} status - The status object to convert.\n     * @returns {Object[]} An array of objects with status and count properties.\n     */\n    getStatusArray(status) {\n      return Object.entries(status).map(([key, value]) => {\n        return {\n          status: key.toLowerCase().replace(' ', '-'),\n          count: value\n        };\n      });\n    }\n    getItemsPositionTop(i) {\n      if (i === 0) {\n        return 2;\n      } else if (i === 1) {\n        return 15;\n      } else if (i === 2) {\n        return 29;\n      } else if (i === 3) {\n        return 40;\n      } else if (i === 4) {\n        return 38;\n      }\n      return 10;\n    }\n    getItemsPositionRight(i) {\n      if (i === 0) {\n        return 11;\n      } else if (i === 1) {\n        return 3;\n      } else if (i === 2) {\n        return 3;\n      } else if (i === 3) {\n        return 12;\n      } else if (i === 4) {\n        return 26;\n      }\n      return 10;\n    }\n    getAvailabilityFromRange(requestData) {\n      return this.calendarService.retrieveCalendarAvailability(requestData);\n    }\n    /**\n     * Checks the availability of the retrieve availability endpoint.\n     * If showAvailability is true, it will load the availability data.\n     * Otherwise, it will mark the calendar as loaded.\n     *\n     * @returns {void}\n     */\n    checkAvailability() {\n      if (this.showAvailability) {\n        this.loaded = false;\n        this.loadAvailability();\n      } else {\n        this.loaded = true;\n      }\n    }\n    calculateMonthBounds(date) {\n      const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);\n      const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);\n      firstDay.setDate(firstDay.getDate() + 1);\n      lastDay.setDate(lastDay.getDate() + 1);\n      const firstDayFormatted = firstDay.toISOString();\n      const lastDayFormatted = lastDay.toISOString();\n      return {\n        fromDate: firstDayFormatted,\n        toDate: lastDayFormatted\n      };\n    }\n    groupLessonsByDateAndStatus() {\n      this.lessons = [].concat(...this.lessons);\n      console.log(this.lessons);\n      const lessonGroups = this.lessons.reduce((groups, lesson) => {\n        const startingDate = moment(lesson.startingDate);\n        const dateString = startingDate.format('YYYY-MM-DD');\n        const status = lesson.status;\n        if (!groups[dateString]) {\n          groups[dateString] = {};\n        }\n        if (!groups[dateString][status]) {\n          groups[dateString][status] = {\n            count: 0\n          };\n        }\n        groups[dateString][status].count++;\n        return groups;\n      }, {});\n    }\n    calculateAgendaHeight() {\n      setTimeout(() => {\n        const elementHeight = this.elementRef.nativeElement.offsetHeight;\n        console.log('Element height:', elementHeight);\n        // this.agendaHeight.emit(elementHeight);\n        this.calendarService.setCalendarAgendaHeightListener(elementHeight);\n      }, 10); // Wait for 1 second before calculating the height\n    }\n    static #_ = this.ɵfac = function CalendarAgendaComponent_Factory(t) {\n      return new (t || CalendarAgendaComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.CalendarService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ClassroomService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CalendarAgendaComponent,\n      selectors: [[\"app-calendar-agenda\"]],\n      viewQuery: function CalendarAgendaComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.daysTableWrapper = _t.first);\n        }\n      },\n      inputs: {\n        enableButtons: \"enableButtons\",\n        reverseGradient: \"reverseGradient\",\n        title: \"title\",\n        lessons: \"lessons\",\n        calHeaderClass: \"calHeaderClass\",\n        currentSelectedDay: \"currentSelectedDay\",\n        userId: \"userId\",\n        showAvailability: \"showAvailability\"\n      },\n      outputs: {\n        dayClicked: \"dayClicked\",\n        agendaHeight: \"agendaHeight\",\n        currentMonthChanged: \"currentMonthChanged\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 14,\n      vars: 14,\n      consts: [[\"daysTableWrapper\", \"\"], [\"elseBlock\", \"\"], [\"headerBlockClass\", \"bg-cover \", \"blockClass\", \"card\", 3, \"header\", \"headerBackgroundImage\", \"headerClass\"], [1, \"p-datepicker\", \"shadow-none\", \"p-0\", 3, \"ngClass\"], [1, \"relative\", \"md:mr-2\", \"font-sm\"], [4, \"ngIf\"], [1, \"table-container\"], [1, \"p-datepicker-calendar\"], [4, \"ngFor\", \"ngForOf\"], [\"type\", \"button\", \"pripple\", \"\", 1, \"p-ripple\", \"p-element\", \"p-datepicker-prev\", \"p-link\", \"ng-star-inserted\", 3, \"click\"], [1, \"p-datepicker-prev-icon\", \"pi\", \"pi-chevron-left\"], [1, \"p-ink\"], [\"type\", \"button\", 1, \"p-ripple\", \"p-element\", \"p-datepicker-next\", \"p-link\", 3, \"click\"], [1, \"p-datepicker-next-icon\", \"pi\", \"pi-chevron-right\"], [1, \"cal-font-size\"], [1, \"cal-font-size\", \"cursor-pointer\", 3, \"click\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"ngClass\"], [1, \"text\"], [1, \"selected-check\", \"border-circle\", \"low-bg\", \"flex\", \"align-items-center\", \"justify-content-center\", \"absolute\"], [1, \"pi\", \"pi-check\", \"font-3xs\"], [1, \"circle-me\", \"absolute\"], [1, \"circle-me\", \"counts\", \"border-round-3xl\"], [1, \"count\", \"border-circle\", 3, \"ngClass\"], [1, \"count-status\"], [3, \"ngClass\", \"class\", 4, \"ngIf\"]],\n      template: function CalendarAgendaComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"app-block-viewer\", 2)(1, \"div\", 3)(2, \"div\")(3, \"div\", 4);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(5, CalendarAgendaComponent_ng_container_5_Template, 6, 0, \"ng-container\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 6, 0)(8, \"table\", 7)(9, \"thead\")(10, \"tr\");\n          i0.ɵɵtemplate(11, CalendarAgendaComponent_ng_container_11_Template, 3, 1, \"ng-container\", 8);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"tbody\");\n          i0.ɵɵtemplate(13, CalendarAgendaComponent_ng_container_13_Template, 2, 1, \"ng-container\", 5);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵpropertyInterpolate(\"header\", ctx.title);\n          i0.ɵɵproperty(\"headerBackgroundImage\", ctx.reverseGradient ? \"\" : \"/assets/images/dashboard/gradient-sm-1.png\")(\"headerClass\", \"justify-content-start\" + (ctx.reverseGradient ? \" text-primary\" : \"\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(10, _c1, ctx.reverseGradient, !ctx.reverseGradient, ctx.showAvailability));\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(ctx.calHeaderClass);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getCurrentMonth());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.enableButtons);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.weekdays);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.loaded);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.BlockViewerComponent],\n      styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}.cal-header[_ngcontent-%COMP%]{align-items:center;position:absolute;top:-28px;right:0;color:#fff;z-index:5}.cal-header[_ngcontent-%COMP%]   .p-datepicker-prev[_ngcontent-%COMP%], .cal-header[_ngcontent-%COMP%]   .p-datepicker-next[_ngcontent-%COMP%]{width:2rem}.p-datepicker[_ngcontent-%COMP%]{position:relative;border-radius:10px}.table-container[_ngcontent-%COMP%]{overflow-x:auto}.p-datepicker-calendar[_ngcontent-%COMP%]{width:100%;max-width:100%;margin:0 auto;table-layout:fixed}.availability-cal[_ngcontent-%COMP%]   .p-datepicker-calendar[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:not(.p-highlight):not(.p-disabled):hover{background:#1e2dfd38!important}.p-datepicker-calendar[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{width:14.2857142857%;height:3.5rem;text-align:center;vertical-align:middle;position:relative;border:none;padding:0}.p-datepicker-calendar[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .active[_ngcontent-%COMP%]{color:#fff;background-color:#2740ac;border:1px solid #2E3D90}.p-datepicker-calendar[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:not(.p-highlight):not(.p-disabled):hover{background:#1e2dfd14!important}.p-datepicker-calendar[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{display:block;margin:0 auto;text-align:center;line-height:2.5rem;border-radius:50px}.p-datepicker-calendar[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:hover{color:var(--primary-color);background-color:#2740ac}.p-datepicker-calendar[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:34px;height:34px;border-radius:50%;overflow:visible;display:flex;align-items:center;justify-content:center}.p-datepicker-calendar[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{display:block;width:100%;height:auto}.p-datepicker-calendar[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);text-align:center}.current-day[_ngcontent-%COMP%]{border-radius:50px;border-color:transparent;color:#fff;background-image:linear-gradient(135deg,#3e4ee0,#4653e2 37%,#525ee5 61%,#5461e8 75%,#5d68ea 83%,#6570ec 88%,#6c77ef 92%,#747ef1,#7c86f3 98%,#8089f5)}.p-datepicker-prev[_ngcontent-%COMP%], .p-datepicker-next[_ngcontent-%COMP%]{color:#fff}.reversed-gradient[_ngcontent-%COMP%]   .cal-header[_ngcontent-%COMP%], .reversed-gradient[_ngcontent-%COMP%]   .p-datepicker-prev[_ngcontent-%COMP%], .reversed-gradient[_ngcontent-%COMP%]   .p-datepicker-next[_ngcontent-%COMP%]{color:var(--primary-color)}.prev-month[_ngcontent-%COMP%]{color:#ff0}.next-month[_ngcontent-%COMP%]{color:#00f}.low-bg[_ngcontent-%COMP%]{background-image:linear-gradient(to bottom,#9baaff,#7c8aec,#5e6ad9,#3f4bc4,#152caf)}.high-bg[_ngcontent-%COMP%]{background-image:linear-gradient(to bottom,#64cfdd,#48c4e0,#2cb8e2,#18abe4,#219de3,#1591e2,#1e85e0,#3177dc,#2a69da,#2b5ad6,#334ad0,#3f37c9)}.no-bg[_ngcontent-%COMP%], .is-past[_ngcontent-%COMP%]{background:#dfe8fc}.circle[_ngcontent-%COMP%]{height:.75rem;width:.75rem;border-radius:50%;color:#fff;border:0}.day-circle[_ngcontent-%COMP%]{border:1px solid var(--primary-color)}.day-circle.today[_ngcontent-%COMP%]{color:#fff!important;background:linear-gradient(#002ccf,#a796ff)}.day-circle.today[_ngcontent-%COMP%]:hover{color:var(--primary-color)!important}.circle-me[_ngcontent-%COMP%]{position:relative;display:flex;align-items:center;justify-content:center;border-radius:50%;color:#fff;font-size:10px;font-weight:700;cursor:pointer}.circle-me[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{position:absolute;top:0;right:0;width:20px;height:20px}.counts[_ngcontent-%COMP%]{position:absolute;top:0;right:0;display:flex;flex-direction:column;align-items:center;justify-content:center}.count[_ngcontent-%COMP%]{display:flex;flex-direction:row;align-items:center;justify-content:center;margin-bottom:2px;width:14px;height:14px}.count-number[_ngcontent-%COMP%]{font-size:12px;font-weight:700;margin-right:2px}.count-status[_ngcontent-%COMP%]{font-size:10px;text-transform:uppercase}.selected-check[_ngcontent-%COMP%]{top:-5px;right:-2px;padding:3px}\"],\n      changeDetection: 0\n    });\n  }\n  return CalendarAgendaComponent;\n})();", "map": {"version": 3, "names": ["EventEmitter", "SubSink", "moment", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "CalendarAgendaComponent_ng_container_5_Template_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "goBackwardMonth", "ɵɵelement", "ɵɵelementEnd", "CalendarAgendaComponent_ng_container_5_Template_button_click_4_listener", "goForward<PERSON><PERSON>h", "ɵɵtext", "ɵɵadvance", "ɵɵtextInterpolate", "day_r3", "ɵɵstyleProp", "getItemsPositionTop", "i_r7", "getItemsPositionRight", "ɵɵproperty", "status_r6", "status", "ɵɵtextInterpolate1", "count", "ɵɵtemplate", "CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_container_2_ng_container_4_Template", "CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_container_2_ng_container_5_Template", "ɵɵclassMap", "day_r5", "availability", "ɵɵpureFunction8", "_c2", "selected<PERSON>ay", "number", "isHighAvailability", "isDayInThePast", "showAvailability", "is<PERSON>ay<PERSON>ff", "getStatusArray", "getTodaysLessons", "length", "CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_span_0_ng_container_3_Template", "ɵɵpureFunction7", "_c3", "i_r9", "status_r8", "CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_ng_container_1_ng_container_1_Template", "CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_span_0_Template", "CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_ng_container_1_Template", "CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_Template_td_click_1_listener", "_r4", "$implicit", "onDayClicked", "CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_container_2_Template", "CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_ng_template_3_Template", "ɵɵtemplateRefExtractor", "isTodayDate", "elseBlock_r10", "CalendarAgendaComponent_ng_container_13_ng_container_1_ng_container_2_Template", "week_r11", "CalendarAgendaComponent_ng_container_13_ng_container_1_Template", "getWeeksInMonth", "currentDate", "CalendarAgendaComponent", "constructor", "cdr", "elementRef", "calendarService", "authService", "classroomService", "subs", "enableButtons", "reverseGradient", "title", "lessons", "calHeaderClass", "currentSelectedDay", "userId", "dayClicked", "agendaHeight", "currentMonthChanged", "weekdays", "Date", "selectedDays", "monthAvailability", "loaded", "ngOnInit", "user", "getLoggedInUser", "ngOnChanges", "changes", "groupLessonsByDateAndStatus", "firstChange", "currentValue", "loadAvailability", "detectChanges", "setMonth", "getMonth", "setSelectedDay", "getDate", "ngAfterViewInit", "calculateAgendaHeight", "ngOnDestroy", "unsubscribe", "getDaysInMonth", "date", "year", "getFullYear", "month", "weeks", "startOfMonthDate", "firstDayOfMonth", "getDay", "daysInMonth", "startOfCalendarDate", "numWeeksInMonth", "Math", "ceil", "daysInCalendar", "i", "week", "j", "dayNumber", "dayDate", "isPrevMonth", "isNextMonth", "isCurrentMonth", "day", "name", "push", "clickedDate", "isSame", "getCurrentMonth", "setCalendarAgendaMonthListener", "toDate", "emit", "format", "checkAvailability", "key", "isStudent", "setSelectedAgendaDate", "data", "calculateMonthBounds", "sink", "getAvailabilityFromRange", "subscribe", "res", "calendarAvailability", "daysOff", "dateString", "dayOff", "from", "period", "to", "today", "setHours", "find", "availabilityDateParts", "split", "map", "Number", "availabilityDate", "toDateString", "abs", "availableHours", "foundDate", "lessonGroups", "reduce", "groups", "lesson", "startingDate", "toLocaleDateString", "todaysLessons", "todaysLessonCounts", "Object", "keys", "counts", "entries", "value", "toLowerCase", "replace", "requestData", "retrieveCalendarAvailability", "firstDay", "lastDay", "setDate", "firstDayFormatted", "toISOString", "lastDayFormatted", "fromDate", "concat", "console", "log", "setTimeout", "elementHeight", "nativeElement", "offsetHeight", "setCalendarAgendaHeightListener", "_", "ɵɵdirectiveInject", "ChangeDetectorRef", "ElementRef", "i1", "CalendarService", "i2", "AuthService", "i3", "ClassroomService", "_2", "selectors", "viewQuery", "CalendarAgendaComponent_Query", "rf", "ctx", "CalendarAgendaComponent_ng_container_5_Template", "CalendarAgendaComponent_ng_container_11_Template", "CalendarAgendaComponent_ng_container_13_Template", "ɵɵpropertyInterpolate", "ɵɵpureFunction3", "_c1"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\prime\\prime\\calendar-agenda\\calendar-agenda.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\prime\\prime\\calendar-agenda\\calendar-agenda.component.html"], "sourcesContent": ["import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { CalendarService } from 'src/app/core/services/calendar.service';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { SubSink } from 'subsink';\r\nimport * as moment from 'moment';\r\n\r\ninterface Day {\r\n  number: number;\r\n  name: string;\r\n  availability: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-calendar-agenda',\r\n  templateUrl: './calendar-agenda.component.html',\r\n  styleUrls: ['./calendar-agenda.component.scss'],\r\n  changeDetection: ChangeDetectionStrategy.OnPush\r\n})\r\nexport class CalendarAgendaComponent implements OnInit, OnChanges, AfterViewInit, OnD<PERSON>roy {\r\n  @ViewChild('daysTableWrapper', {static: true}) public daysTableWrapper: any;\r\n  private subs = new SubSink();\r\n  @Input() enableButtons = true;\r\n  @Input() reverseGradient = false;\r\n  @Input() title = 'Agenda';\r\n  @Input() lessons: any = [];\r\n  @Input() calHeaderClass = 'cal-header flex justify-content-center align-items-center';\r\n  @Input() currentSelectedDay = null;\r\n  @Input() userId = '';\r\n  @Input() showAvailability: boolean = false;\r\n  @Output() dayClicked = new EventEmitter<Date>();\r\n  @Output() agendaHeight = new EventEmitter<number>();\r\n  @Output() currentMonthChanged = new EventEmitter<number>();\r\n  weekdays: string[] = ['M', 'T', 'W', 'T', 'F', 'S', 'S'];\r\n  currentDate: Date = new Date();\r\n  selectedDays: { [key: string]: number } = {};\r\n  user: any;\r\n  availability: any;\r\n  monthAvailability: any[] = [];\r\n  daysOff: any;\r\n  loaded = false;\r\n  \r\n  constructor(\r\n    private cdr: ChangeDetectorRef,\r\n    private elementRef: ElementRef,\r\n    private calendarService: CalendarService,   \r\n    private authService: AuthService, \r\n    private classroomService: ClassroomService, \r\n    ) { }\r\n\r\n  ngOnInit(): void {\r\n\r\n    if (this.currentSelectedDay) {\r\n      this.currentDate = new Date(this.currentSelectedDay);\r\n    } else {\r\n      this.currentDate = new Date();\r\n    }\r\n    this.user = this.authService.getLoggedInUser();\r\n    if (this.showAvailability) {\r\n      // this.loadAvailability();\r\n    } else {\r\n      this.loaded = true;\r\n    }\r\n  }\r\n\r\n  ngOnChanges(changes: SimpleChanges) {\r\n    if (this.lessons.length > 0) {\r\n      this.groupLessonsByDateAndStatus();\r\n\r\n    }\r\n    if (changes.userId && !changes.userId.firstChange) {\r\n      this.userId = changes.userId.currentValue;\r\n      if (changes.currentSelectedDay && changes.currentSelectedDay.currentValue) {\r\n        this.currentDate = (changes.currentSelectedDay.currentValue);\r\n      }\r\n      this.loadAvailability();\r\n      this.cdr.detectChanges();\r\n    }\r\n    if (changes.currentSelectedDay && !changes.currentSelectedDay.firstChange) {\r\n      this.currentDate = (changes.currentSelectedDay.currentValue);\r\n      this.currentDate.setMonth(changes.currentSelectedDay.currentValue.getMonth());\r\n      this.setSelectedDay(this.currentDate.getDate());\r\n      if (this.showAvailability) {\r\n        this.loadAvailability();\r\n      }\r\n      this.cdr.detectChanges();\r\n    }\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.calculateAgendaHeight();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  getDaysInMonth(date: Date): number {\r\n    const year = date.getFullYear();\r\n    const month = date.getMonth();\r\n    return new Date(year, month + 1, 0).getDate();\r\n  }\r\n  \r\n  getWeeksInMonth(date: Date): Day[][] {\r\n    const weeks: Day[][] = [];\r\n    const startOfMonthDate = new Date(date.getFullYear(), date.getMonth(), 1);\r\n    const firstDayOfMonth = (startOfMonthDate.getDay() + 6) % 7; // 0 for Sunday, 1 for Monday, etc.\r\n    const daysInMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();\r\n  \r\n    const startOfCalendarDate = new Date(date.getFullYear(), date.getMonth(), 1 - firstDayOfMonth);\r\n    const numWeeksInMonth = Math.ceil((firstDayOfMonth + daysInMonth) / 7); // Round up to ensure we show all days\r\n    const daysInCalendar = numWeeksInMonth * 7;\r\n  \r\n    for (let i = 0; i < daysInCalendar; i += 7) {\r\n      const week: any[] = [];\r\n      for (let j = 0; j < 7; j++) {\r\n        const dayNumber = (i + j + 1) - firstDayOfMonth;\r\n        const dayDate = new Date(date.getFullYear(), date.getMonth(), dayNumber);\r\n        const isPrevMonth = dayNumber < 1;\r\n        const isNextMonth = dayNumber > daysInMonth;\r\n        const isCurrentMonth = !isPrevMonth && !isNextMonth;\r\n\r\n        const day = {\r\n          number: isCurrentMonth ? dayNumber : '',\r\n          name: this.weekdays[j],\r\n          isPrevMonth,\r\n          isNextMonth,\r\n          isCurrentMonth,\r\n        };\r\n        week.push(day);\r\n      }\r\n      weeks.push(week);\r\n    }\r\n    return weeks;\r\n  } \r\n  isTodayDate(day: number): boolean {\r\n    const currentDate = moment();\r\n    const clickedDate = moment(this.currentDate).date(day);\r\n  \r\n    return (\r\n      clickedDate.isSame(currentDate, 'day') &&\r\n      clickedDate.isSame(this.currentDate, 'month')\r\n    );\r\n  }\r\n\r\n  getCurrentMonth(): string {\r\n    this.calendarService.setCalendarAgendaMonthListener(moment(this.currentDate).toDate());\r\n    this.currentMonthChanged.emit(moment(this.currentDate).month());\r\n    return moment(this.currentDate).format('MMMM');\r\n  }\r\n\r\n  goForwardMonth(): void {\r\n    this.currentDate.setMonth(this.currentDate.getMonth() + 1);\r\n    this.checkAvailability();\r\n    this.calculateAgendaHeight();\r\n  }\r\n\r\n  goBackwardMonth(): void {\r\n    this.currentDate.setMonth(this.currentDate.getMonth() - 1);\r\n    this.checkAvailability();\r\n    this.calculateAgendaHeight();\r\n  }\r\n\r\n  setSelectedDay(day: number): void {\r\n    this.selectedDays = {};\r\n    const key = `${this.currentDate.getFullYear()}-${this.currentDate.getMonth()}`;\r\n    this.selectedDays[key] = day;\r\n  }\r\n\r\n  get selectedDay(): number {\r\n    const key = `${this.currentDate.getFullYear()}-${this.currentDate.getMonth()}`;\r\n    return this.selectedDays[key];\r\n  }\r\n\r\n  onDayClicked(day: number): void {\r\n    if (this.authService.isStudent) {\r\n      if (this.showAvailability && this.isDayInThePast(day)) {\r\n        return;\r\n      }\r\n    }\r\n    const clickedDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), day);\r\n    this.dayClicked.emit(clickedDate);\r\n    this.classroomService.setSelectedAgendaDate(clickedDate);\r\n    this.setSelectedDay(day);\r\n  }\r\n\r\n  isCurrentMonth(dayNumber: number): boolean {\r\n    return new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), dayNumber).getMonth() === this.currentDate.getMonth();\r\n  }\r\n\r\n  isNextMonth(dayNumber: number): boolean {\r\n    return new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), dayNumber).getMonth() === this.currentDate.getMonth() + 1;\r\n  }\r\n\r\n  loadAvailability(): void {\r\n    if (!this.userId) {\r\n      return;\r\n    }\r\n    if(this.showAvailability){\r\n      const data = {\r\n        ...this.calculateMonthBounds(this.currentDate),\r\n        userId: this.userId\r\n      };\r\n      this.subs.sink = this.getAvailabilityFromRange(data).subscribe((res) => {\r\n        this.monthAvailability = res.calendarAvailability;\r\n        this.daysOff = res.daysOff;\r\n        this.loaded = true;\r\n        this.cdr.detectChanges();\r\n      });\r\n    }\r\n    else {\r\n      this.loaded = true;\r\n    }\r\n  }\r\n\r\n  isDayOff(dayNumber: number): boolean {\r\n    if(this.showAvailability){\r\n      const dateString = this.currentDate.getFullYear() + '-' + (this.currentDate.getMonth() + 1) + '-' + dayNumber;\r\n      const currentDate = new Date(dateString);\r\n    \r\n      for (const dayOff of this.daysOff) {\r\n        const from = new Date(dayOff.period.from);\r\n        const to = new Date(dayOff.period.to);\r\n    \r\n        if (currentDate >= from && currentDate <= to) {\r\n          return true;\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  }\r\n\r\n  isDayInThePast(dayNumber: number): boolean {\r\n    const dateString = this.currentDate.getFullYear() + '-' + (this.currentDate.getMonth() + 1) + '-' + dayNumber;\r\n    const today = new Date();\r\n    const currentDate = new Date(dateString);\r\n\r\n    // Set hours, minutes, seconds, and milliseconds to 0\r\n    today.setHours(0, 0, 0, 0);\r\n    currentDate.setHours(0, 0, 0, 0);\r\n\r\n    return currentDate < today;\r\n  }\r\n\r\n  isHighAvailability(dayNumber: number): boolean {\r\n    if (this.showAvailability) {\r\n      const dateString = `${this.currentDate.getFullYear()}-${this.currentDate.getMonth() + 1}-${dayNumber}`;\r\n      const currentDate = new Date(dateString);\r\n  \r\n      const availability = this.monthAvailability.find((availability) => {\r\n        const availabilityDateParts = availability.date.split('/').map(Number);\r\n        const availabilityDate = new Date(availabilityDateParts[2], availabilityDateParts[1] - 1, availabilityDateParts[0]);\r\n  \r\n        return currentDate.toDateString() === availabilityDate.toDateString();\r\n      });\r\n  \r\n      if (availability) {\r\n        return Math.abs(availability.availableHours) >= 4;\r\n      }\r\n    }\r\n    \r\n    return false;\r\n  }\r\n\r\n   \r\n  getTodaysLessons(day: number): any[] {\r\n    const foundDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), day);\r\n    const lessonGroups = this.lessons.reduce((groups: any, lesson: any) => {\r\n      const date = new Date(lesson.startingDate).toLocaleDateString();\r\n      const status = lesson.status;\r\n      if (!groups[date]) {\r\n        groups[date] = {};\r\n      }\r\n      if (!groups[date][status]) {\r\n        groups[date][status] = { count: 0 };\r\n      }\r\n      groups[date][status].count++;\r\n      return groups;\r\n    }, {});\r\n    const todaysLessons = lessonGroups[foundDate.toLocaleDateString()] || {};\r\n    const todaysLessonCounts = Object.keys(todaysLessons).reduce((counts: any, status: string) => {\r\n      counts[status] = todaysLessons[status].count;\r\n      return counts;\r\n    }, {});\r\n\r\n    return todaysLessonCounts;\r\n  }\r\n\r\n  /**\r\n   * Converts a status object to an array of objects with status and count properties.\r\n   * @param {Object} status - The status object to convert.\r\n   * @returns {Object[]} An array of objects with status and count properties.\r\n   */\r\n  getStatusArray(status: {}): Object[] {\r\n    return Object.entries(status).map(([key, value]) => {\r\n      return { status: key.toLowerCase().replace(' ', '-'), count: value };\r\n    });\r\n  }\r\n\r\n  getItemsPositionTop(i: number) {\r\n    if (i === 0) {\r\n      return 2;\r\n    } else if (i === 1) {\r\n      return 15;\r\n    } else if (i === 2) {\r\n      return 29;\r\n    } else if (i === 3) {\r\n      return 40;\r\n    } else if (i === 4) {\r\n      return 38;\r\n    }\r\n    return 10;\r\n  }\r\n\r\n  getItemsPositionRight(i: number) {\r\n    if (i === 0) {\r\n      return 11;\r\n    } else if (i === 1) {\r\n      return 3;\r\n    } else if (i === 2) {\r\n      return 3;\r\n    } else if (i === 3) {\r\n      return 12;\r\n    } else if (i === 4) {\r\n      return 26;\r\n    }\r\n    return 10;\r\n  }\r\n  \r\n  private getAvailabilityFromRange(requestData: any): Observable<any> {\r\n    return this.calendarService.retrieveCalendarAvailability(requestData);\r\n  }\r\n\r\n  /**\r\n   * Checks the availability of the retrieve availability endpoint.\r\n   * If showAvailability is true, it will load the availability data.\r\n   * Otherwise, it will mark the calendar as loaded.\r\n   *\r\n   * @returns {void}\r\n   */\r\n  private checkAvailability(): void {\r\n    if (this.showAvailability) {\r\n      this.loaded = false;\r\n      this.loadAvailability();\r\n    } else {\r\n      this.loaded = true;\r\n    }\r\n  }\r\n  \r\n  private calculateMonthBounds(date: Date): { fromDate: string, toDate: string } {\r\n    const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);\r\n    const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);\r\n    firstDay.setDate(firstDay.getDate() + 1);\r\n\r\n    lastDay.setDate(lastDay.getDate() + 1);\r\n    const firstDayFormatted = firstDay.toISOString();\r\n    const lastDayFormatted = lastDay.toISOString();\r\n  \r\n    return { fromDate: firstDayFormatted, toDate: lastDayFormatted };\r\n  }\r\n\r\n  private groupLessonsByDateAndStatus() {\r\n    this.lessons = [].concat(...this.lessons) as [];\r\n    console.log(this.lessons);\r\n    const lessonGroups = this.lessons.reduce((groups: any, lesson: any) => {\r\n      const startingDate = moment(lesson.startingDate);\r\n      const dateString = startingDate.format('YYYY-MM-DD');\r\n      const status = lesson.status;\r\n      if (!groups[dateString]) {\r\n        groups[dateString] = {};\r\n      }\r\n      if (!groups[dateString][status]) {\r\n        groups[dateString][status] = { count: 0 };\r\n      }\r\n      groups[dateString][status].count++;\r\n      return groups;\r\n    }, {});\r\n  }\r\n\r\n  private calculateAgendaHeight() {\r\n    setTimeout(() => {\r\n    const elementHeight = this.elementRef.nativeElement.offsetHeight;\r\n    console.log('Element height:', elementHeight);\r\n    // this.agendaHeight.emit(elementHeight);\r\n    this.calendarService.setCalendarAgendaHeightListener(elementHeight);\r\n    }, 10); // Wait for 1 second before calculating the height\r\n\r\n  }\r\n}", "\r\n<app-block-viewer header=\"{{title}}\" headerBlockClass=\"bg-cover \" [headerBackgroundImage]=\"reverseGradient ? '' : '/assets/images/dashboard/gradient-sm-1.png'\" blockClass=\"card\"\r\n[headerClass]=\"'justify-content-start' + (reverseGradient ? ' text-primary' : '') \" >\r\n  <div class=\"p-datepicker  shadow-none p-0\" [ngClass]=\"{ 'reversed-gradient': reverseGradient, 'block-gradient' : !reverseGradient, 'availability-cal': showAvailability }\">\r\n    <div [class]=\"calHeaderClass\">\r\n      <div class=\"relative md:mr-2 font-sm\">{{ getCurrentMonth() }}</div>\r\n      <ng-container *ngIf=\"enableButtons\">\r\n        <button type=\"button\" (click)=\"goBackwardMonth()\" pripple=\"\" class=\"p-ripple p-element p-datepicker-prev p-link ng-star-inserted\">\r\n          <span class=\"p-datepicker-prev-icon pi pi-chevron-left\"></span>\r\n          <span class=\"p-ink\"></span>\r\n        </button>\r\n        <button (click)=\"goForwardMonth()\" type=\"button\" class=\"p-ripple p-element p-datepicker-next p-link\">\r\n          <span class=\"p-datepicker-next-icon pi pi-chevron-right\"></span>\r\n        </button>\r\n      </ng-container>\r\n    </div>\r\n    <div class=\"table-container\" #daysTableWrapper>\r\n      <table class=\"p-datepicker-calendar\">\r\n        <thead>\r\n          <tr>\r\n            <ng-container *ngFor=\"let day of weekdays; let i = index;\">\r\n              <th class=\"cal-font-size\">{{ day }}</th>\r\n            </ng-container>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <ng-container *ngIf=\"loaded\">\r\n            <ng-container *ngFor=\"let week of getWeeksInMonth(currentDate)\">\r\n              <tr>\r\n                <ng-container *ngFor=\"let day of week\">\r\n                  <td class=\"cal-font-size cursor-pointer\" (click)=\"onDayClicked(day.number)\">\r\n                    <ng-container *ngIf=\"isTodayDate(day.number); else elseBlock\">\r\n                      <span \r\n                      [ngClass]=\"{ 'active': selectedDay === day.number, \r\n                      'high-bg': isHighAvailability(day.number) && !isDayInThePast(day.number),\r\n                      'low-bg': !isHighAvailability(day.number) && showAvailability && !isDayInThePast(day.number), \r\n                      'is-past': showAvailability && isDayInThePast(day.number), \r\n                      'no-bg': isDayOff(day.number), \r\n                      'circle': showAvailability ,\r\n                      'today': !showAvailability,\r\n                      'day-circle': getStatusArray(getTodaysLessons(day.number)).length > 0 || !showAvailability }\" \r\n                      [class]=\"day.availability\">\r\n                        <div class=\"text\" >{{day.number}}</div>\r\n                        <ng-container *ngIf=\"(selectedDay === day.number) && showAvailability\">\r\n                          <div class=\"selected-check border-circle low-bg flex align-items-center justify-content-center absolute\">\r\n                          <i class=\"pi pi-check font-3xs\"></i>\r\n                        </div>\r\n                        </ng-container>\r\n                      </span>\r\n                      \r\n                    <ng-container *ngFor=\"let status of getStatusArray(getTodaysLessons(day.number)); let i = index;\">\r\n                      <div class=\"circle-me absolute\" [style.top.px]=\"getItemsPositionTop(i)\" [style.right.px]=\"getItemsPositionRight(i)\">\r\n                        <div class=\"circle-me counts border-round-3xl\">\r\n                          <div class=\"count border-circle\" [ngClass]=\"status.status + '-gradient-lesson-bg'\">\r\n                            <div class=\"count-status\"> {{status.count}}</div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                  </ng-container>\r\n                    </ng-container>\r\n                    <ng-template #elseBlock>\r\n                      <span *ngIf=\"day.number\" \r\n                      [ngClass]=\"{ 'active': selectedDay === day.number, \r\n                      'high-bg': isHighAvailability(day.number) && !isDayInThePast(day.number),\r\n                      'low-bg': !isHighAvailability(day.number) && showAvailability && !isDayInThePast(day.number), \r\n                      'is-past': showAvailability && isDayInThePast(day.number), \r\n                      'no-bg': isDayOff(day.number), \r\n                      'circle': showAvailability ,\r\n                      'day-circle': getStatusArray(getTodaysLessons(day.number)).length > 0}\" \r\n                      [class]=\"day.availability\">\r\n                      <div>{{ day.number }}</div>\r\n                      <ng-container *ngIf=\"(selectedDay === day.number) && showAvailability\">\r\n                        <div class=\"selected-check border-circle low-bg flex align-items-center justify-content-center absolute\">\r\n                        <i class=\"pi pi-check font-3xs\"></i>\r\n                      </div>\r\n                      </ng-container>\r\n                    </span>\r\n                    <ng-container *ngIf=\"day.number\" >\r\n                    <ng-container *ngFor=\"let status of getStatusArray(getTodaysLessons(day.number)); let i = index;\">\r\n                      <div class=\"circle-me absolute\" [style.top.px]=\"getItemsPositionTop(i)\" [style.right.px]=\"getItemsPositionRight(i)\">\r\n                        <div class=\"circle-me counts border-round-3xl\">\r\n                          <div class=\"count border-circle\" [ngClass]=\"status.status + '-gradient-lesson-bg'\">\r\n                            <div class=\"count-status\"> {{status.count}}</div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                  </ng-container>\r\n                </ng-container>\r\n                    </ng-template>\r\n                  </td>\r\n                </ng-container>\r\n              </tr>\r\n            </ng-container>\r\n          </ng-container>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </div>\r\n</app-block-viewer>"], "mappings": "AAAA,SAA2FA,YAAY,QAA+E,eAAe;AAKrM,SAASC,OAAO,QAAQ,SAAS;AACjC,OAAO,KAAKC,MAAM,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICA1BC,EAAA,CAAAC,uBAAA,GAAoC;IAClCD,EAAA,CAAAE,cAAA,gBAAkI;IAA5GF,EAAA,CAAAG,UAAA,mBAAAC,wEAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAG,eAAA,EAAiB;IAAA,EAAC;IAE/CV,EADA,CAAAW,SAAA,eAA+D,eACpC;IAC7BX,EAAA,CAAAY,YAAA,EAAS;IACTZ,EAAA,CAAAE,cAAA,iBAAqG;IAA7FF,EAAA,CAAAG,UAAA,mBAAAU,wEAAA;MAAAb,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAO,cAAA,EAAgB;IAAA,EAAC;IAChCd,EAAA,CAAAW,SAAA,eAAgE;IAClEX,EAAA,CAAAY,YAAA,EAAS;;;;;;IAOLZ,EAAA,CAAAC,uBAAA,GAA2D;IACzDD,EAAA,CAAAE,cAAA,aAA0B;IAAAF,EAAA,CAAAe,MAAA,GAAS;IAAAf,EAAA,CAAAY,YAAA,EAAK;;;;;IAAdZ,EAAA,CAAAgB,SAAA,GAAS;IAAThB,EAAA,CAAAiB,iBAAA,CAAAC,MAAA,CAAS;;;;;IAsBzBlB,EAAA,CAAAC,uBAAA,GAAuE;IACrED,EAAA,CAAAE,cAAA,cAAyG;IACzGF,EAAA,CAAAW,SAAA,YAAoC;IACtCX,EAAA,CAAAY,YAAA,EAAM;;;;;;IAIVZ,EAAA,CAAAC,uBAAA,GAAkG;IAI1FD,EAHN,CAAAE,cAAA,cAAoH,cACnE,cACsC,cACvD;IAACF,EAAA,CAAAe,MAAA,GAAgB;IAGjDf,EAHiD,CAAAY,YAAA,EAAM,EAC7C,EACF,EACF;;;;;;;IAN0BZ,EAAA,CAAAgB,SAAA,EAAuC;IAAChB,EAAxC,CAAAmB,WAAA,QAAAZ,MAAA,CAAAa,mBAAA,CAAAC,IAAA,QAAuC,UAAAd,MAAA,CAAAe,qBAAA,CAAAD,IAAA,QAA4C;IAE9ErB,EAAA,CAAAgB,SAAA,GAAiD;IAAjDhB,EAAA,CAAAuB,UAAA,YAAAC,SAAA,CAAAC,MAAA,yBAAiD;IACrDzB,EAAA,CAAAgB,SAAA,GAAgB;IAAhBhB,EAAA,CAAA0B,kBAAA,MAAAF,SAAA,CAAAG,KAAA,KAAgB;;;;;IAvBnD3B,EAAA,CAAAC,uBAAA,GAA8D;IAW1DD,EAVF,CAAAE,cAAA,eAS2B,cACN;IAAAF,EAAA,CAAAe,MAAA,GAAc;IAAAf,EAAA,CAAAY,YAAA,EAAM;IACvCZ,EAAA,CAAA4B,UAAA,IAAAC,4GAAA,0BAAuE;IAKzE7B,EAAA,CAAAY,YAAA,EAAO;IAETZ,EAAA,CAAA4B,UAAA,IAAAE,4GAAA,0BAAkG;;;;;;IAThG9B,EAAA,CAAAgB,SAAA,EAA0B;IAA1BhB,EAAA,CAAA+B,UAAA,CAAAC,MAAA,CAAAC,YAAA,CAA0B;IAR1BjC,EAAA,CAAAuB,UAAA,YAAAvB,EAAA,CAAAkC,eAAA,IAAAC,GAAA,EAAA5B,MAAA,CAAA6B,WAAA,KAAAJ,MAAA,CAAAK,MAAA,EAAA9B,MAAA,CAAA+B,kBAAA,CAAAN,MAAA,CAAAK,MAAA,MAAA9B,MAAA,CAAAgC,cAAA,CAAAP,MAAA,CAAAK,MAAA,IAAA9B,MAAA,CAAA+B,kBAAA,CAAAN,MAAA,CAAAK,MAAA,KAAA9B,MAAA,CAAAiC,gBAAA,KAAAjC,MAAA,CAAAgC,cAAA,CAAAP,MAAA,CAAAK,MAAA,GAAA9B,MAAA,CAAAiC,gBAAA,IAAAjC,MAAA,CAAAgC,cAAA,CAAAP,MAAA,CAAAK,MAAA,GAAA9B,MAAA,CAAAkC,QAAA,CAAAT,MAAA,CAAAK,MAAA,GAAA9B,MAAA,CAAAiC,gBAAA,GAAAjC,MAAA,CAAAiC,gBAAA,EAAAjC,MAAA,CAAAmC,cAAA,CAAAnC,MAAA,CAAAoC,gBAAA,CAAAX,MAAA,CAAAK,MAAA,GAAAO,MAAA,SAAArC,MAAA,CAAAiC,gBAAA,EAO6F;IAExExC,EAAA,CAAAgB,SAAA,GAAc;IAAdhB,EAAA,CAAAiB,iBAAA,CAAAe,MAAA,CAAAK,MAAA,CAAc;IAClBrC,EAAA,CAAAgB,SAAA,EAAsD;IAAtDhB,EAAA,CAAAuB,UAAA,SAAAhB,MAAA,CAAA6B,WAAA,KAAAJ,MAAA,CAAAK,MAAA,IAAA9B,MAAA,CAAAiC,gBAAA,CAAsD;IAOxCxC,EAAA,CAAAgB,SAAA,EAAiD;IAAjDhB,EAAA,CAAAuB,UAAA,YAAAhB,MAAA,CAAAmC,cAAA,CAAAnC,MAAA,CAAAoC,gBAAA,CAAAX,MAAA,CAAAK,MAAA,GAAiD;;;;;IAqBhFrC,EAAA,CAAAC,uBAAA,GAAuE;IACrED,EAAA,CAAAE,cAAA,cAAyG;IACzGF,EAAA,CAAAW,SAAA,YAAoC;IACtCX,EAAA,CAAAY,YAAA,EAAM;;;;;;IAJNZ,EATA,CAAAE,cAAA,eAQ2B,UACtB;IAAAF,EAAA,CAAAe,MAAA,GAAgB;IAAAf,EAAA,CAAAY,YAAA,EAAM;IAC3BZ,EAAA,CAAA4B,UAAA,IAAAiB,kHAAA,0BAAuE;IAKzE7C,EAAA,CAAAY,YAAA,EAAO;;;;;IAPLZ,EAAA,CAAA+B,UAAA,CAAAC,MAAA,CAAAC,YAAA,CAA0B;IAP1BjC,EAAA,CAAAuB,UAAA,YAAAvB,EAAA,CAAA8C,eAAA,IAAAC,GAAA,EAAAxC,MAAA,CAAA6B,WAAA,KAAAJ,MAAA,CAAAK,MAAA,EAAA9B,MAAA,CAAA+B,kBAAA,CAAAN,MAAA,CAAAK,MAAA,MAAA9B,MAAA,CAAAgC,cAAA,CAAAP,MAAA,CAAAK,MAAA,IAAA9B,MAAA,CAAA+B,kBAAA,CAAAN,MAAA,CAAAK,MAAA,KAAA9B,MAAA,CAAAiC,gBAAA,KAAAjC,MAAA,CAAAgC,cAAA,CAAAP,MAAA,CAAAK,MAAA,GAAA9B,MAAA,CAAAiC,gBAAA,IAAAjC,MAAA,CAAAgC,cAAA,CAAAP,MAAA,CAAAK,MAAA,GAAA9B,MAAA,CAAAkC,QAAA,CAAAT,MAAA,CAAAK,MAAA,GAAA9B,MAAA,CAAAiC,gBAAA,EAAAjC,MAAA,CAAAmC,cAAA,CAAAnC,MAAA,CAAAoC,gBAAA,CAAAX,MAAA,CAAAK,MAAA,GAAAO,MAAA,MAMuE;IAElE5C,EAAA,CAAAgB,SAAA,GAAgB;IAAhBhB,EAAA,CAAAiB,iBAAA,CAAAe,MAAA,CAAAK,MAAA,CAAgB;IACNrC,EAAA,CAAAgB,SAAA,EAAsD;IAAtDhB,EAAA,CAAAuB,UAAA,SAAAhB,MAAA,CAAA6B,WAAA,KAAAJ,MAAA,CAAAK,MAAA,IAAA9B,MAAA,CAAAiC,gBAAA,CAAsD;;;;;IAOvExC,EAAA,CAAAC,uBAAA,GAAkG;IAI1FD,EAHN,CAAAE,cAAA,cAAoH,cACnE,cACsC,cACvD;IAACF,EAAA,CAAAe,MAAA,GAAgB;IAGjDf,EAHiD,CAAAY,YAAA,EAAM,EAC7C,EACF,EACF;;;;;;;IAN0BZ,EAAA,CAAAgB,SAAA,EAAuC;IAAChB,EAAxC,CAAAmB,WAAA,QAAAZ,MAAA,CAAAa,mBAAA,CAAA4B,IAAA,QAAuC,UAAAzC,MAAA,CAAAe,qBAAA,CAAA0B,IAAA,QAA4C;IAE9EhD,EAAA,CAAAgB,SAAA,GAAiD;IAAjDhB,EAAA,CAAAuB,UAAA,YAAA0B,SAAA,CAAAxB,MAAA,yBAAiD;IACrDzB,EAAA,CAAAgB,SAAA,GAAgB;IAAhBhB,EAAA,CAAA0B,kBAAA,MAAAuB,SAAA,CAAAtB,KAAA,KAAgB;;;;;IALnD3B,EAAA,CAAAC,uBAAA,GAAkC;IAClCD,EAAA,CAAA4B,UAAA,IAAAsB,0HAAA,0BAAkG;;;;;;IAAjElD,EAAA,CAAAgB,SAAA,EAAiD;IAAjDhB,EAAA,CAAAuB,UAAA,YAAAhB,MAAA,CAAAmC,cAAA,CAAAnC,MAAA,CAAAoC,gBAAA,CAAAX,MAAA,CAAAK,MAAA,GAAiD;;;;;IADlFrC,EAhBE,CAAA4B,UAAA,IAAAuB,mGAAA,oBAQ2B,IAAAC,2GAAA,0BAQK;;;;IAhBzBpD,EAAA,CAAAuB,UAAA,SAAAS,MAAA,CAAAK,MAAA,CAAgB;IAgBVrC,EAAA,CAAAgB,SAAA,EAAgB;IAAhBhB,EAAA,CAAAuB,UAAA,SAAAS,MAAA,CAAAK,MAAA,CAAgB;;;;;;IAhDnCrC,EAAA,CAAAC,uBAAA,GAAuC;IACrCD,EAAA,CAAAE,cAAA,aAA4E;IAAnCF,EAAA,CAAAG,UAAA,mBAAAkD,mGAAA;MAAA,MAAArB,MAAA,GAAAhC,EAAA,CAAAK,aAAA,CAAAiD,GAAA,EAAAC,SAAA;MAAA,MAAAhD,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAiD,YAAA,CAAAxB,MAAA,CAAAK,MAAA,CAAwB;IAAA,EAAC;IA8BzErC,EA7BA,CAAA4B,UAAA,IAAA6B,6FAAA,4BAA8D,IAAAC,4FAAA,gCAAA1D,EAAA,CAAA2D,sBAAA,CA6BtC;IA6B1B3D,EAAA,CAAAY,YAAA,EAAK;;;;;;;IA1DYZ,EAAA,CAAAgB,SAAA,GAA+B;IAAAhB,EAA/B,CAAAuB,UAAA,SAAAhB,MAAA,CAAAqD,WAAA,CAAA5B,MAAA,CAAAK,MAAA,EAA+B,aAAAwB,aAAA,CAAc;;;;;IAJpE7D,EAAA,CAAAC,uBAAA,GAAgE;IAC9DD,EAAA,CAAAE,cAAA,SAAI;IACFF,EAAA,CAAA4B,UAAA,IAAAkC,8EAAA,0BAAuC;IA8DzC9D,EAAA,CAAAY,YAAA,EAAK;;;;;IA9D2BZ,EAAA,CAAAgB,SAAA,GAAO;IAAPhB,EAAA,CAAAuB,UAAA,YAAAwC,QAAA,CAAO;;;;;IAH3C/D,EAAA,CAAAC,uBAAA,GAA6B;IAC3BD,EAAA,CAAA4B,UAAA,IAAAoC,+DAAA,0BAAgE;;;;;IAAjChE,EAAA,CAAAgB,SAAA,EAA+B;IAA/BhB,EAAA,CAAAuB,UAAA,YAAAhB,MAAA,CAAA0D,eAAA,CAAA1D,MAAA,CAAA2D,WAAA,EAA+B;;;ADP1E,WAAaC,uBAAuB;EAA9B,MAAOA,uBAAuB;IAuBlCC,YACUC,GAAsB,EACtBC,UAAsB,EACtBC,eAAgC,EAChCC,WAAwB,EACxBC,gBAAkC;MAJlC,KAAAJ,GAAG,GAAHA,GAAG;MACH,KAAAC,UAAU,GAAVA,UAAU;MACV,KAAAC,eAAe,GAAfA,eAAe;MACf,KAAAC,WAAW,GAAXA,WAAW;MACX,KAAAC,gBAAgB,GAAhBA,gBAAgB;MA1BlB,KAAAC,IAAI,GAAG,IAAI5E,OAAO,EAAE;MACnB,KAAA6E,aAAa,GAAG,IAAI;MACpB,KAAAC,eAAe,GAAG,KAAK;MACvB,KAAAC,KAAK,GAAG,QAAQ;MAChB,KAAAC,OAAO,GAAQ,EAAE;MACjB,KAAAC,cAAc,GAAG,2DAA2D;MAC5E,KAAAC,kBAAkB,GAAG,IAAI;MACzB,KAAAC,MAAM,GAAG,EAAE;MACX,KAAAzC,gBAAgB,GAAY,KAAK;MAChC,KAAA0C,UAAU,GAAG,IAAIrF,YAAY,EAAQ;MACrC,KAAAsF,YAAY,GAAG,IAAItF,YAAY,EAAU;MACzC,KAAAuF,mBAAmB,GAAG,IAAIvF,YAAY,EAAU;MAC1D,KAAAwF,QAAQ,GAAa,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MACxD,KAAAnB,WAAW,GAAS,IAAIoB,IAAI,EAAE;MAC9B,KAAAC,YAAY,GAA8B,EAAE;MAG5C,KAAAC,iBAAiB,GAAU,EAAE;MAE7B,KAAAC,MAAM,GAAG,KAAK;IAQR;IAENC,QAAQA,CAAA;MAEN,IAAI,IAAI,CAACV,kBAAkB,EAAE;QAC3B,IAAI,CAACd,WAAW,GAAG,IAAIoB,IAAI,CAAC,IAAI,CAACN,kBAAkB,CAAC;MACtD,CAAC,MAAM;QACL,IAAI,CAACd,WAAW,GAAG,IAAIoB,IAAI,EAAE;MAC/B;MACA,IAAI,CAACK,IAAI,GAAG,IAAI,CAACnB,WAAW,CAACoB,eAAe,EAAE;MAC9C,IAAI,IAAI,CAACpD,gBAAgB,EAAE;QACzB;MAAA,CACD,MAAM;QACL,IAAI,CAACiD,MAAM,GAAG,IAAI;MACpB;IACF;IAEAI,WAAWA,CAACC,OAAsB;MAChC,IAAI,IAAI,CAAChB,OAAO,CAAClC,MAAM,GAAG,CAAC,EAAE;QAC3B,IAAI,CAACmD,2BAA2B,EAAE;MAEpC;MACA,IAAID,OAAO,CAACb,MAAM,IAAI,CAACa,OAAO,CAACb,MAAM,CAACe,WAAW,EAAE;QACjD,IAAI,CAACf,MAAM,GAAGa,OAAO,CAACb,MAAM,CAACgB,YAAY;QACzC,IAAIH,OAAO,CAACd,kBAAkB,IAAIc,OAAO,CAACd,kBAAkB,CAACiB,YAAY,EAAE;UACzE,IAAI,CAAC/B,WAAW,GAAI4B,OAAO,CAACd,kBAAkB,CAACiB,YAAa;QAC9D;QACA,IAAI,CAACC,gBAAgB,EAAE;QACvB,IAAI,CAAC7B,GAAG,CAAC8B,aAAa,EAAE;MAC1B;MACA,IAAIL,OAAO,CAACd,kBAAkB,IAAI,CAACc,OAAO,CAACd,kBAAkB,CAACgB,WAAW,EAAE;QACzE,IAAI,CAAC9B,WAAW,GAAI4B,OAAO,CAACd,kBAAkB,CAACiB,YAAa;QAC5D,IAAI,CAAC/B,WAAW,CAACkC,QAAQ,CAACN,OAAO,CAACd,kBAAkB,CAACiB,YAAY,CAACI,QAAQ,EAAE,CAAC;QAC7E,IAAI,CAACC,cAAc,CAAC,IAAI,CAACpC,WAAW,CAACqC,OAAO,EAAE,CAAC;QAC/C,IAAI,IAAI,CAAC/D,gBAAgB,EAAE;UACzB,IAAI,CAAC0D,gBAAgB,EAAE;QACzB;QACA,IAAI,CAAC7B,GAAG,CAAC8B,aAAa,EAAE;MAC1B;IACF;IAEAK,eAAeA,CAAA;MACb,IAAI,CAACC,qBAAqB,EAAE;IAC9B;IAEAC,WAAWA,CAAA;MACT,IAAI,CAAChC,IAAI,CAACiC,WAAW,EAAE;IACzB;IAEAC,cAAcA,CAACC,IAAU;MACvB,MAAMC,IAAI,GAAGD,IAAI,CAACE,WAAW,EAAE;MAC/B,MAAMC,KAAK,GAAGH,IAAI,CAACR,QAAQ,EAAE;MAC7B,OAAO,IAAIf,IAAI,CAACwB,IAAI,EAAEE,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,CAACT,OAAO,EAAE;IAC/C;IAEAtC,eAAeA,CAAC4C,IAAU;MACxB,MAAMI,KAAK,GAAY,EAAE;MACzB,MAAMC,gBAAgB,GAAG,IAAI5B,IAAI,CAACuB,IAAI,CAACE,WAAW,EAAE,EAAEF,IAAI,CAACR,QAAQ,EAAE,EAAE,CAAC,CAAC;MACzE,MAAMc,eAAe,GAAG,CAACD,gBAAgB,CAACE,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;MAC7D,MAAMC,WAAW,GAAG,IAAI/B,IAAI,CAACuB,IAAI,CAACE,WAAW,EAAE,EAAEF,IAAI,CAACR,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAACE,OAAO,EAAE;MAElF,MAAMe,mBAAmB,GAAG,IAAIhC,IAAI,CAACuB,IAAI,CAACE,WAAW,EAAE,EAAEF,IAAI,CAACR,QAAQ,EAAE,EAAE,CAAC,GAAGc,eAAe,CAAC;MAC9F,MAAMI,eAAe,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACN,eAAe,GAAGE,WAAW,IAAI,CAAC,CAAC,CAAC,CAAC;MACxE,MAAMK,cAAc,GAAGH,eAAe,GAAG,CAAC;MAE1C,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,cAAc,EAAEC,CAAC,IAAI,CAAC,EAAE;QAC1C,MAAMC,IAAI,GAAU,EAAE;QACtB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC1B,MAAMC,SAAS,GAAIH,CAAC,GAAGE,CAAC,GAAG,CAAC,GAAIV,eAAe;UAC/C,MAAMY,OAAO,GAAG,IAAIzC,IAAI,CAACuB,IAAI,CAACE,WAAW,EAAE,EAAEF,IAAI,CAACR,QAAQ,EAAE,EAAEyB,SAAS,CAAC;UACxE,MAAME,WAAW,GAAGF,SAAS,GAAG,CAAC;UACjC,MAAMG,WAAW,GAAGH,SAAS,GAAGT,WAAW;UAC3C,MAAMa,cAAc,GAAG,CAACF,WAAW,IAAI,CAACC,WAAW;UAEnD,MAAME,GAAG,GAAG;YACV9F,MAAM,EAAE6F,cAAc,GAAGJ,SAAS,GAAG,EAAE;YACvCM,IAAI,EAAE,IAAI,CAAC/C,QAAQ,CAACwC,CAAC,CAAC;YACtBG,WAAW;YACXC,WAAW;YACXC;WACD;UACDN,IAAI,CAACS,IAAI,CAACF,GAAG,CAAC;QAChB;QACAlB,KAAK,CAACoB,IAAI,CAACT,IAAI,CAAC;MAClB;MACA,OAAOX,KAAK;IACd;IACArD,WAAWA,CAACuE,GAAW;MACrB,MAAMjE,WAAW,GAAGnE,MAAM,EAAE;MAC5B,MAAMuI,WAAW,GAAGvI,MAAM,CAAC,IAAI,CAACmE,WAAW,CAAC,CAAC2C,IAAI,CAACsB,GAAG,CAAC;MAEtD,OACEG,WAAW,CAACC,MAAM,CAACrE,WAAW,EAAE,KAAK,CAAC,IACtCoE,WAAW,CAACC,MAAM,CAAC,IAAI,CAACrE,WAAW,EAAE,OAAO,CAAC;IAEjD;IAEAsE,eAAeA,CAAA;MACb,IAAI,CAACjE,eAAe,CAACkE,8BAA8B,CAAC1I,MAAM,CAAC,IAAI,CAACmE,WAAW,CAAC,CAACwE,MAAM,EAAE,CAAC;MACtF,IAAI,CAACtD,mBAAmB,CAACuD,IAAI,CAAC5I,MAAM,CAAC,IAAI,CAACmE,WAAW,CAAC,CAAC8C,KAAK,EAAE,CAAC;MAC/D,OAAOjH,MAAM,CAAC,IAAI,CAACmE,WAAW,CAAC,CAAC0E,MAAM,CAAC,MAAM,CAAC;IAChD;IAEA9H,cAAcA,CAAA;MACZ,IAAI,CAACoD,WAAW,CAACkC,QAAQ,CAAC,IAAI,CAAClC,WAAW,CAACmC,QAAQ,EAAE,GAAG,CAAC,CAAC;MAC1D,IAAI,CAACwC,iBAAiB,EAAE;MACxB,IAAI,CAACpC,qBAAqB,EAAE;IAC9B;IAEA/F,eAAeA,CAAA;MACb,IAAI,CAACwD,WAAW,CAACkC,QAAQ,CAAC,IAAI,CAAClC,WAAW,CAACmC,QAAQ,EAAE,GAAG,CAAC,CAAC;MAC1D,IAAI,CAACwC,iBAAiB,EAAE;MACxB,IAAI,CAACpC,qBAAqB,EAAE;IAC9B;IAEAH,cAAcA,CAAC6B,GAAW;MACxB,IAAI,CAAC5C,YAAY,GAAG,EAAE;MACtB,MAAMuD,GAAG,GAAG,GAAG,IAAI,CAAC5E,WAAW,CAAC6C,WAAW,EAAE,IAAI,IAAI,CAAC7C,WAAW,CAACmC,QAAQ,EAAE,EAAE;MAC9E,IAAI,CAACd,YAAY,CAACuD,GAAG,CAAC,GAAGX,GAAG;IAC9B;IAEA,IAAI/F,WAAWA,CAAA;MACb,MAAM0G,GAAG,GAAG,GAAG,IAAI,CAAC5E,WAAW,CAAC6C,WAAW,EAAE,IAAI,IAAI,CAAC7C,WAAW,CAACmC,QAAQ,EAAE,EAAE;MAC9E,OAAO,IAAI,CAACd,YAAY,CAACuD,GAAG,CAAC;IAC/B;IAEAtF,YAAYA,CAAC2E,GAAW;MACtB,IAAI,IAAI,CAAC3D,WAAW,CAACuE,SAAS,EAAE;QAC9B,IAAI,IAAI,CAACvG,gBAAgB,IAAI,IAAI,CAACD,cAAc,CAAC4F,GAAG,CAAC,EAAE;UACrD;QACF;MACF;MACA,MAAMG,WAAW,GAAG,IAAIhD,IAAI,CAAC,IAAI,CAACpB,WAAW,CAAC6C,WAAW,EAAE,EAAE,IAAI,CAAC7C,WAAW,CAACmC,QAAQ,EAAE,EAAE8B,GAAG,CAAC;MAC9F,IAAI,CAACjD,UAAU,CAACyD,IAAI,CAACL,WAAW,CAAC;MACjC,IAAI,CAAC7D,gBAAgB,CAACuE,qBAAqB,CAACV,WAAW,CAAC;MACxD,IAAI,CAAChC,cAAc,CAAC6B,GAAG,CAAC;IAC1B;IAEAD,cAAcA,CAACJ,SAAiB;MAC9B,OAAO,IAAIxC,IAAI,CAAC,IAAI,CAACpB,WAAW,CAAC6C,WAAW,EAAE,EAAE,IAAI,CAAC7C,WAAW,CAACmC,QAAQ,EAAE,EAAEyB,SAAS,CAAC,CAACzB,QAAQ,EAAE,KAAK,IAAI,CAACnC,WAAW,CAACmC,QAAQ,EAAE;IACpI;IAEA4B,WAAWA,CAACH,SAAiB;MAC3B,OAAO,IAAIxC,IAAI,CAAC,IAAI,CAACpB,WAAW,CAAC6C,WAAW,EAAE,EAAE,IAAI,CAAC7C,WAAW,CAACmC,QAAQ,EAAE,EAAEyB,SAAS,CAAC,CAACzB,QAAQ,EAAE,KAAK,IAAI,CAACnC,WAAW,CAACmC,QAAQ,EAAE,GAAG,CAAC;IACxI;IAEAH,gBAAgBA,CAAA;MACd,IAAI,CAAC,IAAI,CAACjB,MAAM,EAAE;QAChB;MACF;MACA,IAAG,IAAI,CAACzC,gBAAgB,EAAC;QACvB,MAAMyG,IAAI,GAAG;UACX,GAAG,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAAChF,WAAW,CAAC;UAC9Ce,MAAM,EAAE,IAAI,CAACA;SACd;QACD,IAAI,CAACP,IAAI,CAACyE,IAAI,GAAG,IAAI,CAACC,wBAAwB,CAACH,IAAI,CAAC,CAACI,SAAS,CAAEC,GAAG,IAAI;UACrE,IAAI,CAAC9D,iBAAiB,GAAG8D,GAAG,CAACC,oBAAoB;UACjD,IAAI,CAACC,OAAO,GAAGF,GAAG,CAACE,OAAO;UAC1B,IAAI,CAAC/D,MAAM,GAAG,IAAI;UAClB,IAAI,CAACpB,GAAG,CAAC8B,aAAa,EAAE;QAC1B,CAAC,CAAC;MACJ,CAAC,MACI;QACH,IAAI,CAACV,MAAM,GAAG,IAAI;MACpB;IACF;IAEAhD,QAAQA,CAACqF,SAAiB;MACxB,IAAG,IAAI,CAACtF,gBAAgB,EAAC;QACvB,MAAMiH,UAAU,GAAG,IAAI,CAACvF,WAAW,CAAC6C,WAAW,EAAE,GAAG,GAAG,IAAI,IAAI,CAAC7C,WAAW,CAACmC,QAAQ,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGyB,SAAS;QAC7G,MAAM5D,WAAW,GAAG,IAAIoB,IAAI,CAACmE,UAAU,CAAC;QAExC,KAAK,MAAMC,MAAM,IAAI,IAAI,CAACF,OAAO,EAAE;UACjC,MAAMG,IAAI,GAAG,IAAIrE,IAAI,CAACoE,MAAM,CAACE,MAAM,CAACD,IAAI,CAAC;UACzC,MAAME,EAAE,GAAG,IAAIvE,IAAI,CAACoE,MAAM,CAACE,MAAM,CAACC,EAAE,CAAC;UAErC,IAAI3F,WAAW,IAAIyF,IAAI,IAAIzF,WAAW,IAAI2F,EAAE,EAAE;YAC5C,OAAO,IAAI;UACb;QACF;MACF;MACA,OAAO,KAAK;IACd;IAEAtH,cAAcA,CAACuF,SAAiB;MAC9B,MAAM2B,UAAU,GAAG,IAAI,CAACvF,WAAW,CAAC6C,WAAW,EAAE,GAAG,GAAG,IAAI,IAAI,CAAC7C,WAAW,CAACmC,QAAQ,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGyB,SAAS;MAC7G,MAAMgC,KAAK,GAAG,IAAIxE,IAAI,EAAE;MACxB,MAAMpB,WAAW,GAAG,IAAIoB,IAAI,CAACmE,UAAU,CAAC;MAExC;MACAK,KAAK,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAC1B7F,WAAW,CAAC6F,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAEhC,OAAO7F,WAAW,GAAG4F,KAAK;IAC5B;IAEAxH,kBAAkBA,CAACwF,SAAiB;MAClC,IAAI,IAAI,CAACtF,gBAAgB,EAAE;QACzB,MAAMiH,UAAU,GAAG,GAAG,IAAI,CAACvF,WAAW,CAAC6C,WAAW,EAAE,IAAI,IAAI,CAAC7C,WAAW,CAACmC,QAAQ,EAAE,GAAG,CAAC,IAAIyB,SAAS,EAAE;QACtG,MAAM5D,WAAW,GAAG,IAAIoB,IAAI,CAACmE,UAAU,CAAC;QAExC,MAAMxH,YAAY,GAAG,IAAI,CAACuD,iBAAiB,CAACwE,IAAI,CAAE/H,YAAY,IAAI;UAChE,MAAMgI,qBAAqB,GAAGhI,YAAY,CAAC4E,IAAI,CAACqD,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;UACtE,MAAMC,gBAAgB,GAAG,IAAI/E,IAAI,CAAC2E,qBAAqB,CAAC,CAAC,CAAC,EAAEA,qBAAqB,CAAC,CAAC,CAAC,GAAG,CAAC,EAAEA,qBAAqB,CAAC,CAAC,CAAC,CAAC;UAEnH,OAAO/F,WAAW,CAACoG,YAAY,EAAE,KAAKD,gBAAgB,CAACC,YAAY,EAAE;QACvE,CAAC,CAAC;QAEF,IAAIrI,YAAY,EAAE;UAChB,OAAOuF,IAAI,CAAC+C,GAAG,CAACtI,YAAY,CAACuI,cAAc,CAAC,IAAI,CAAC;QACnD;MACF;MAEA,OAAO,KAAK;IACd;IAGA7H,gBAAgBA,CAACwF,GAAW;MAC1B,MAAMsC,SAAS,GAAG,IAAInF,IAAI,CAAC,IAAI,CAACpB,WAAW,CAAC6C,WAAW,EAAE,EAAE,IAAI,CAAC7C,WAAW,CAACmC,QAAQ,EAAE,EAAE8B,GAAG,CAAC;MAC5F,MAAMuC,YAAY,GAAG,IAAI,CAAC5F,OAAO,CAAC6F,MAAM,CAAC,CAACC,MAAW,EAAEC,MAAW,KAAI;QACpE,MAAMhE,IAAI,GAAG,IAAIvB,IAAI,CAACuF,MAAM,CAACC,YAAY,CAAC,CAACC,kBAAkB,EAAE;QAC/D,MAAMtJ,MAAM,GAAGoJ,MAAM,CAACpJ,MAAM;QAC5B,IAAI,CAACmJ,MAAM,CAAC/D,IAAI,CAAC,EAAE;UACjB+D,MAAM,CAAC/D,IAAI,CAAC,GAAG,EAAE;QACnB;QACA,IAAI,CAAC+D,MAAM,CAAC/D,IAAI,CAAC,CAACpF,MAAM,CAAC,EAAE;UACzBmJ,MAAM,CAAC/D,IAAI,CAAC,CAACpF,MAAM,CAAC,GAAG;YAAEE,KAAK,EAAE;UAAC,CAAE;QACrC;QACAiJ,MAAM,CAAC/D,IAAI,CAAC,CAACpF,MAAM,CAAC,CAACE,KAAK,EAAE;QAC5B,OAAOiJ,MAAM;MACf,CAAC,EAAE,EAAE,CAAC;MACN,MAAMI,aAAa,GAAGN,YAAY,CAACD,SAAS,CAACM,kBAAkB,EAAE,CAAC,IAAI,EAAE;MACxE,MAAME,kBAAkB,GAAGC,MAAM,CAACC,IAAI,CAACH,aAAa,CAAC,CAACL,MAAM,CAAC,CAACS,MAAW,EAAE3J,MAAc,KAAI;QAC3F2J,MAAM,CAAC3J,MAAM,CAAC,GAAGuJ,aAAa,CAACvJ,MAAM,CAAC,CAACE,KAAK;QAC5C,OAAOyJ,MAAM;MACf,CAAC,EAAE,EAAE,CAAC;MAEN,OAAOH,kBAAkB;IAC3B;IAEA;;;;;IAKAvI,cAAcA,CAACjB,MAAU;MACvB,OAAOyJ,MAAM,CAACG,OAAO,CAAC5J,MAAM,CAAC,CAAC0I,GAAG,CAAC,CAAC,CAACrB,GAAG,EAAEwC,KAAK,CAAC,KAAI;QACjD,OAAO;UAAE7J,MAAM,EAAEqH,GAAG,CAACyC,WAAW,EAAE,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;UAAE7J,KAAK,EAAE2J;QAAK,CAAE;MACtE,CAAC,CAAC;IACJ;IAEAlK,mBAAmBA,CAACuG,CAAS;MAC3B,IAAIA,CAAC,KAAK,CAAC,EAAE;QACX,OAAO,CAAC;MACV,CAAC,MAAM,IAAIA,CAAC,KAAK,CAAC,EAAE;QAClB,OAAO,EAAE;MACX,CAAC,MAAM,IAAIA,CAAC,KAAK,CAAC,EAAE;QAClB,OAAO,EAAE;MACX,CAAC,MAAM,IAAIA,CAAC,KAAK,CAAC,EAAE;QAClB,OAAO,EAAE;MACX,CAAC,MAAM,IAAIA,CAAC,KAAK,CAAC,EAAE;QAClB,OAAO,EAAE;MACX;MACA,OAAO,EAAE;IACX;IAEArG,qBAAqBA,CAACqG,CAAS;MAC7B,IAAIA,CAAC,KAAK,CAAC,EAAE;QACX,OAAO,EAAE;MACX,CAAC,MAAM,IAAIA,CAAC,KAAK,CAAC,EAAE;QAClB,OAAO,CAAC;MACV,CAAC,MAAM,IAAIA,CAAC,KAAK,CAAC,EAAE;QAClB,OAAO,CAAC;MACV,CAAC,MAAM,IAAIA,CAAC,KAAK,CAAC,EAAE;QAClB,OAAO,EAAE;MACX,CAAC,MAAM,IAAIA,CAAC,KAAK,CAAC,EAAE;QAClB,OAAO,EAAE;MACX;MACA,OAAO,EAAE;IACX;IAEQyB,wBAAwBA,CAACqC,WAAgB;MAC/C,OAAO,IAAI,CAAClH,eAAe,CAACmH,4BAA4B,CAACD,WAAW,CAAC;IACvE;IAEA;;;;;;;IAOQ5C,iBAAiBA,CAAA;MACvB,IAAI,IAAI,CAACrG,gBAAgB,EAAE;QACzB,IAAI,CAACiD,MAAM,GAAG,KAAK;QACnB,IAAI,CAACS,gBAAgB,EAAE;MACzB,CAAC,MAAM;QACL,IAAI,CAACT,MAAM,GAAG,IAAI;MACpB;IACF;IAEQyD,oBAAoBA,CAACrC,IAAU;MACrC,MAAM8E,QAAQ,GAAG,IAAIrG,IAAI,CAACuB,IAAI,CAACE,WAAW,EAAE,EAAEF,IAAI,CAACR,QAAQ,EAAE,EAAE,CAAC,CAAC;MACjE,MAAMuF,OAAO,GAAG,IAAItG,IAAI,CAACuB,IAAI,CAACE,WAAW,EAAE,EAAEF,IAAI,CAACR,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;MACpEsF,QAAQ,CAACE,OAAO,CAACF,QAAQ,CAACpF,OAAO,EAAE,GAAG,CAAC,CAAC;MAExCqF,OAAO,CAACC,OAAO,CAACD,OAAO,CAACrF,OAAO,EAAE,GAAG,CAAC,CAAC;MACtC,MAAMuF,iBAAiB,GAAGH,QAAQ,CAACI,WAAW,EAAE;MAChD,MAAMC,gBAAgB,GAAGJ,OAAO,CAACG,WAAW,EAAE;MAE9C,OAAO;QAAEE,QAAQ,EAAEH,iBAAiB;QAAEpD,MAAM,EAAEsD;MAAgB,CAAE;IAClE;IAEQjG,2BAA2BA,CAAA;MACjC,IAAI,CAACjB,OAAO,GAAG,EAAE,CAACoH,MAAM,CAAC,GAAG,IAAI,CAACpH,OAAO,CAAO;MAC/CqH,OAAO,CAACC,GAAG,CAAC,IAAI,CAACtH,OAAO,CAAC;MACzB,MAAM4F,YAAY,GAAG,IAAI,CAAC5F,OAAO,CAAC6F,MAAM,CAAC,CAACC,MAAW,EAAEC,MAAW,KAAI;QACpE,MAAMC,YAAY,GAAG/K,MAAM,CAAC8K,MAAM,CAACC,YAAY,CAAC;QAChD,MAAMrB,UAAU,GAAGqB,YAAY,CAAClC,MAAM,CAAC,YAAY,CAAC;QACpD,MAAMnH,MAAM,GAAGoJ,MAAM,CAACpJ,MAAM;QAC5B,IAAI,CAACmJ,MAAM,CAACnB,UAAU,CAAC,EAAE;UACvBmB,MAAM,CAACnB,UAAU,CAAC,GAAG,EAAE;QACzB;QACA,IAAI,CAACmB,MAAM,CAACnB,UAAU,CAAC,CAAChI,MAAM,CAAC,EAAE;UAC/BmJ,MAAM,CAACnB,UAAU,CAAC,CAAChI,MAAM,CAAC,GAAG;YAAEE,KAAK,EAAE;UAAC,CAAE;QAC3C;QACAiJ,MAAM,CAACnB,UAAU,CAAC,CAAChI,MAAM,CAAC,CAACE,KAAK,EAAE;QAClC,OAAOiJ,MAAM;MACf,CAAC,EAAE,EAAE,CAAC;IACR;IAEQnE,qBAAqBA,CAAA;MAC3B4F,UAAU,CAAC,MAAK;QAChB,MAAMC,aAAa,GAAG,IAAI,CAAChI,UAAU,CAACiI,aAAa,CAACC,YAAY;QAChEL,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEE,aAAa,CAAC;QAC7C;QACA,IAAI,CAAC/H,eAAe,CAACkI,+BAA+B,CAACH,aAAa,CAAC;MACnE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IAEV;IAAC,QAAAI,CAAA,G;uBAhXUvI,uBAAuB,EAAAnE,EAAA,CAAA2M,iBAAA,CAAA3M,EAAA,CAAA4M,iBAAA,GAAA5M,EAAA,CAAA2M,iBAAA,CAAA3M,EAAA,CAAA6M,UAAA,GAAA7M,EAAA,CAAA2M,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA/M,EAAA,CAAA2M,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAjN,EAAA,CAAA2M,iBAAA,CAAAO,EAAA,CAAAC,gBAAA;IAAA;IAAA,QAAAC,EAAA,G;YAAvBjJ,uBAAuB;MAAAkJ,SAAA;MAAAC,SAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UCf9BxN,EAJN,CAAAE,cAAA,0BACqF,aACwF,UAC3I,aACU;UAAAF,EAAA,CAAAe,MAAA,GAAuB;UAAAf,EAAA,CAAAY,YAAA,EAAM;UACnEZ,EAAA,CAAA4B,UAAA,IAAA8L,+CAAA,0BAAoC;UAStC1N,EAAA,CAAAY,YAAA,EAAM;UAIAZ,EAHN,CAAAE,cAAA,gBAA+C,eACR,YAC5B,UACD;UACFF,EAAA,CAAA4B,UAAA,KAAA+L,gDAAA,0BAA2D;UAI/D3N,EADE,CAAAY,YAAA,EAAK,EACC;UACRZ,EAAA,CAAAE,cAAA,aAAO;UACLF,EAAA,CAAA4B,UAAA,KAAAgM,gDAAA,0BAA6B;UAwEvC5N,EAJQ,CAAAY,YAAA,EAAQ,EACF,EACJ,EACF,EACW;;;UAjGDZ,EAAA,CAAA6N,qBAAA,WAAAJ,GAAA,CAAA5I,KAAA,CAAkB;UACpC7E,EADkE,CAAAuB,UAAA,0BAAAkM,GAAA,CAAA7I,eAAA,qDAA6F,2CAAA6I,GAAA,CAAA7I,eAAA,yBAC5E;UACtC5E,EAAA,CAAAgB,SAAA,EAA+H;UAA/HhB,EAAA,CAAAuB,UAAA,YAAAvB,EAAA,CAAA8N,eAAA,KAAAC,GAAA,EAAAN,GAAA,CAAA7I,eAAA,GAAA6I,GAAA,CAAA7I,eAAA,EAAA6I,GAAA,CAAAjL,gBAAA,EAA+H;UACnKxC,EAAA,CAAAgB,SAAA,EAAwB;UAAxBhB,EAAA,CAAA+B,UAAA,CAAA0L,GAAA,CAAA1I,cAAA,CAAwB;UACW/E,EAAA,CAAAgB,SAAA,GAAuB;UAAvBhB,EAAA,CAAAiB,iBAAA,CAAAwM,GAAA,CAAAjF,eAAA,GAAuB;UAC9CxI,EAAA,CAAAgB,SAAA,EAAmB;UAAnBhB,EAAA,CAAAuB,UAAA,SAAAkM,GAAA,CAAA9I,aAAA,CAAmB;UAcE3E,EAAA,CAAAgB,SAAA,GAAa;UAAbhB,EAAA,CAAAuB,UAAA,YAAAkM,GAAA,CAAApI,QAAA,CAAa;UAM9BrF,EAAA,CAAAgB,SAAA,GAAY;UAAZhB,EAAA,CAAAuB,UAAA,SAAAkM,GAAA,CAAAhI,MAAA,CAAY;;;;;;;;SDNxBtB,uBAAuB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}