{"ast": null, "code": "import { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/core/services/general.service\";\nimport * as i3 from \"src/app/core/services/classroom.service\";\nimport * as i4 from \"src/app/core/services/rating-and-report.service\";\nimport * as i5 from \"src/app/core/services/auth.service\";\nimport * as i6 from \"src/app/core/services/user.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"../../../../../../../shared/prime/prime/calendar-agenda/calendar-agenda.component\";\nimport * as i9 from \"../../../../../../../shared/lesson/lesson-schedule-box/lesson-schedule-box.component\";\nimport * as i10 from \"../../../../../../../shared/lesson/mini-lesson-info-card/mini-lesson-info-card.component\";\nimport * as i11 from \"primeng/button\";\nimport * as i12 from \"primeng/chip\";\nimport * as i13 from \"primeng/tooltip\";\nimport * as i14 from \"../../../../../../library/library/library.component\";\nconst _c0 = a0 => ({\n  \"disabled-link\": a0\n});\nconst _c1 = a0 => [a0];\nfunction TeacherProfileComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"div\", 51)(2, \"div\", 52);\n    i0.ɵɵtext(3, \"Rate your experience \");\n    i0.ɵɵelement(4, \"img\", 53);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, ctx_r0.hasRated))(\"pTooltip\", ctx_r0.hasRated ? \"Already rated by student\" : \"\")(\"routerLink\", i0.ɵɵpureFunction1(5, _c1, ctx_r0.classroomService.getClassroomRateUrl(ctx_r0.classRoomData)));\n  }\n}\nfunction TeacherProfileComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"div\", 54)(2, \"div\", 55)(3, \"div\", 56)(4, \"p\", 57);\n    i0.ɵɵtext(5, \"Skype\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 58);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 55)(9, \"div\", 56)(10, \"p\", 57);\n    i0.ɵɵtext(11, \"E-mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 58);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 55)(15, \"div\", 59)(16, \"p\", 57);\n    i0.ɵɵtext(17, \"Birthday\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\", 58);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 59)(22, \"p\", 57);\n    i0.ɵɵtext(23, \"Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"p\", 58);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 55)(27, \"div\", 56)(28, \"p\", 57);\n    i0.ɵɵtext(29, \"Timezone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"p\", 58);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r0.generalService.replaceImportedWithDash(ctx_r0.classRoomData == null ? null : ctx_r0.classRoomData.teacher.skype));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r0.teacherData.userName);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 6, ctx_r0.classRoomData == null ? null : ctx_r0.classRoomData.teacher.dob, \"d/M/y\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r0.generalService.replaceImportedWithDash(ctx_r0.classRoomData == null ? null : ctx_r0.classRoomData.teacher.residence));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r0.generalService.convertTimezoneValueToText(ctx_r0.teacherData == null ? null : ctx_r0.teacherData.timeZone).text, \" - \", ctx_r0.generalService.getUserLocalTime(ctx_r0.generalService.convertTimezoneValueToText(ctx_r0.teacherData == null ? null : ctx_r0.teacherData.timeZone).text, false), \" \");\n  }\n}\nfunction TeacherProfileComponent_ng_container_67_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 61);\n    i0.ɵɵelement(2, \"app-mini-lesson-info-card\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const lesson_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"classroom\", ctx_r0.classRoomData)(\"lesson\", lesson_r2);\n  }\n}\nfunction TeacherProfileComponent_ng_container_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TeacherProfileComponent_ng_container_67_ng_container_1_Template, 3, 2, \"ng-container\", 60);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.classRoomData.lessons.sort(ctx_r0.generalService.sortLessonsByRecentFirst));\n  }\n}\nexport let TeacherProfileComponent = /*#__PURE__*/(() => {\n  class TeacherProfileComponent {\n    constructor(router, route, generalService, classroomService, ratingAndReportService, authService, userService) {\n      this.router = router;\n      this.route = route;\n      this.generalService = generalService;\n      this.classroomService = classroomService;\n      this.ratingAndReportService = ratingAndReportService;\n      this.authService = authService;\n      this.userService = userService;\n      this.subs = new SubSink();\n      this.teacherData = {};\n      this.hasRated = false;\n    }\n    ngOnInit() {\n      const data = {\n        efficient: \"Good\",\n        evaluate: \"Good\",\n        explain: \"Neutral\",\n        focused: \"Neutral\",\n        friendly: \"Neutral\",\n        interest: \"Good\",\n        polite: \"Neutral\",\n        preferences: \"Good\",\n        schedule: \"Neutral\",\n        trialLevel: \"Neutral\"\n      };\n      this.classRoomData = this.classroomService.getSelectedClassroom();\n      this.authService.getUserRole();\n      this.subs.add(this.ratingAndReportService.getTeacherTrialRating(this.classRoomData.id).subscribe(res => {\n        if (res) {\n          this.hasRated = !this.generalService.isObjectEmpty(res);\n        }\n      }));\n      this.fetchTeacherData();\n      this.initUpdateListener();\n      // console.log(this.router.getCurrentNavigation()!.extras.state);\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n    }\n    goToTrialRatingPage() {\n      this.router.navigate(['/dashboard/classrooms/rate-trial']);\n    }\n    getMedianRating(ratings) {\n      const ratingValues = Object.values(ratings);\n      const sortedRatings = ratingValues.sort();\n      const middleIndex = Math.floor(sortedRatings.length / 2);\n      if (sortedRatings.length % 2 === 0) {\n        const middleValue1 = sortedRatings[middleIndex - 1];\n        const middleValue2 = sortedRatings[middleIndex];\n        return (middleValue1 + middleValue2) / 2;\n      } else {\n        return sortedRatings[middleIndex];\n      }\n    }\n    initUpdateListener() {\n      this.subs.add(this.classroomService.classroomUpdateListener.subscribe(res => {\n        if (res) {\n          this.classRoomData = this.classroomService.getSelectedClassroom();\n        }\n      }));\n    }\n    /**\n    * Fetches the user data for the teacher associated with the classroom.\n    */\n    fetchTeacherData() {\n      // Subscribe to the getUserById observable\n      this.subs.add(this.userService.getUserById(this.classRoomData.teacher.aspUserId).subscribe(res => {\n        this.teacherData = res;\n      }));\n    }\n    static #_ = this.ɵfac = function TeacherProfileComponent_Factory(t) {\n      return new (t || TeacherProfileComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.GeneralService), i0.ɵɵdirectiveInject(i3.ClassroomService), i0.ɵɵdirectiveInject(i4.RatingAndReportService), i0.ɵɵdirectiveInject(i5.AuthService), i0.ɵɵdirectiveInject(i6.UserService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TeacherProfileComponent,\n      selectors: [[\"app-teacher-profile\"]],\n      decls: 75,\n      vars: 19,\n      consts: [[1, \"grid\", \"p-2\"], [1, \"col-12\", \"lg:col-6\"], [1, \"gradient-block\", \"py-2\", \"px-4\", \"border-round-xl\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"mb-0\", \"text-primary\", \"font-bold\"], [1, \"my-1\", \"relative\"], [\"class\", \" flex justify-content-center\", 4, \"ngIf\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"md:align-items-center\", \"md:justify-content-between\", \"mb-4\"], [1, \"flex\", \"max-w-17rem\", \"align-items-center\", \"gap-2\"], [1, \"avatar-circle\", \"w-4rem\"], [1, \"border-circle\", 3, \"error\", \"src\"], [1, \"info\", \"bg-blue-400\", \"border-round-xl\"], [1, \"content\", \"text-white\"], [\"class\", \"grid\", 4, \"ngIf\"], [1, \"flex\", \"flex-column\", \"justify-content-between\", \"h-full\", \"gap-2\"], [1, \"gradient-block\", \"py-2\", \"px-4\", \"border-round-xl\", \"h-full\"], [1, \"bio__title\", \"mb-0\", \"text-primary\", \"font-bold\"], [1, \"bio__subtitle\", \"text-primary\"], [1, \"bio__detail\", \"font-sm\"], [1, \"gradient-block\", \"py-2\", \"px-4\", \"border-round-xl\", \"hidden\"], [1, \"flex\", \"justify-content-between\", \"mb-3\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-outlined\", \"p-1\", \"p-button-rounded\", \"button-outlined\", \"p-button-sm\"], [1, \"flex\", \"align-items-center\", \"gap-1\"], [1, \"fl-typo\", \"s12-16\"], [1, \"pi\", \"pi-star\"], [1, \"flex\", \"gap-3\", \"flex-column\", \"sm:flex-row\", \"justify-content-center\", \"align-items-center\"], [1, \"circle\", \"text-center\", \"text-primary\"], [1, \"flex\", \"flex-column\", \"gap-3\"], [1, \"text-3xl\"], [1, \"flex\"], [1, \"pi\", \"pi-star-fill\"], [1, \"flex\", \"flex-column\", \"align-items-center\"], [1, \"flex\", \"justify-content-center\"], [\"src\", \"/assets/icons/classroom/rate/smile.svg\", 1, \"rating-icon\"], [1, \"text-md\", \"mb-1\"], [1, \"font-sm\", \"line-height-1\", \"max-w-4rem\"], [1, \"flex\", \"flex-column\", \"gap-2\"], [1, \"flex\", \"flex-row\", \"align-items-center\", \"justify-content-center\", \"gap-1\", \"ml-3\"], [\"src\", \"/assets/images/dashboard/classRoomData?.teacher.svg\", \"width\", \"20\", \"alt\", \"Student Image\"], [1, \"font-xs\", \"line-height-1\", \"text-left\", \"max-w-3rem\"], [1, \"w-full\"], [1, \"col-12\"], [1, \"gradient-block\"], [1, \"flex\", \"justify-content-start\", \"p-4\", \"grid\"], [\"tooltipPosition\", \"top\", 1, \"col-6\", \"md:col-2\", 3, \"pTooltip\"], [\"cardType\", \"request\", 3, \"ngClass\", \"classroom\"], [4, \"ngIf\"], [1, \"grid\"], [3, \"lessons\", \"reverseGradient\"], [3, \"classroom\", \"lessons\", \"reverseGradient\"], [3, \"selectedClassroom\"], [1, \"flex\", \"align-items-center\", \"gap-1\", 3, \"ngClass\", \"pTooltip\", \"routerLink\"], [1, \"gray-border-button\", \"font-xs\", \"p-1\", \"border-round-3xl\", \"line-height-1\"], [\"src\", \"/assets/icons/lessons/rating-stars.svg\", \"height\", \"14\", 1, \"ml-1\"], [1, \"flex\", \"flex-column\", \"md:ml-3\"], [1, \"flex\", \"gap-2\", \"mb-2\"], [1, \"flex\", \"flex-column\"], [1, \"person__title\"], [1, \"person__detail\"], [1, \"flex\", \"flex-column\", \"w-8rem\"], [4, \"ngFor\", \"ngForOf\"], [1, \"col-6\", \"md:col-2\"], [3, \"classroom\", \"lesson\"]],\n      template: function TeacherProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h5\", 4);\n          i0.ɵɵtext(5, \"Your Teacher\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 5);\n          i0.ɵɵtemplate(7, TeacherProfileComponent_div_7_Template, 5, 7, \"div\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"li\", 7)(9, \"div\", 8)(10, \"div\", 9)(11, \"img\", 10);\n          i0.ɵɵlistener(\"error\", function TeacherProfileComponent_Template_img_error_11_listener($event) {\n            return ctx.generalService.setDefaultUserAvatar($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"p-chip\", 11)(13, \"div\", 12);\n          i0.ɵɵtext(14);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(15, TeacherProfileComponent_div_15_Template, 32, 9, \"div\", 13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(16, \"div\", 1)(17, \"div\", 14)(18, \"div\", 15)(19, \"h5\", 16);\n          i0.ɵɵtext(20, \"Bio\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"p\", 17);\n          i0.ɵɵtext(22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"p\", 18);\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 19)(26, \"div\", 20)(27, \"h5\", 16);\n          i0.ɵɵtext(28, \"Ratings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"button\", 21)(30, \"div\", 22)(31, \"span\", 23);\n          i0.ɵɵtext(32, \"See Reviews\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(33, \"i\", 24);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(34, \"div\", 25)(35, \"div\", 26)(36, \"div\", 27)(37, \"span\", 28);\n          i0.ɵɵtext(38, \"5\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"div\", 29);\n          i0.ɵɵelement(40, \"i\", 30)(41, \"i\", 30)(42, \"i\", 30)(43, \"i\", 30)(44, \"i\", 30);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(45, \"div\", 26)(46, \"div\", 31)(47, \"div\", 32);\n          i0.ɵɵelement(48, \"img\", 33);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"span\", 34);\n          i0.ɵɵtext(50, \"Excellent!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"span\", 35);\n          i0.ɵɵtext(52, \"Satisfaction rate\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(53, \"div\", 26)(54, \"div\", 36)(55, \"span\", 28);\n          i0.ɵɵtext(56, \"37\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"div\", 37);\n          i0.ɵɵelement(58, \"img\", 38);\n          i0.ɵɵelementStart(59, \"span\", 39);\n          i0.ɵɵtext(60, \"Total Students\");\n          i0.ɵɵelementEnd()()()()()()()();\n          i0.ɵɵelementStart(61, \"div\", 40)(62, \"div\", 41)(63, \"div\", 42)(64, \"div\", 43)(65, \"div\", 44);\n          i0.ɵɵelement(66, \"app-mini-lesson-info-card\", 45);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(67, TeacherProfileComponent_ng_container_67_Template, 2, 1, \"ng-container\", 46);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(68, \"div\", 47)(69, \"div\", 1)(70, \"div\", 42);\n          i0.ɵɵelement(71, \"app-calendar-agenda\", 48);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(72, \"div\", 1);\n          i0.ɵɵelement(73, \"app-lesson-schedule-box\", 49);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(74, \"app-library\", 50);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ctx.classroomService.isTrialClassroomWithCompletedLesson(ctx.classRoomData));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"src\", ctx.generalService.getDomainFileNamePath(ctx.classRoomData == null ? null : ctx.classRoomData.teacher.avatarUrl), i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate2(\"\", ctx.classRoomData == null ? null : ctx.classRoomData.teacher.firstName, \" \", ctx.classRoomData == null ? null : ctx.classRoomData.teacher.lastName, \"\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.generalService.isObjectEmpty(ctx.teacherData));\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate1(\"\", ctx.classRoomData == null ? null : ctx.classRoomData.teacher.firstName, \"\\u2019s Introduction\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.generalService.replaceImportedWithDash(ctx.classRoomData == null ? null : ctx.classRoomData.teacher.introduce), \" \");\n          i0.ɵɵadvance(41);\n          i0.ɵɵproperty(\"pTooltip\", !ctx.classroomService.hasClassroomPackageHoursLeft(ctx.classRoomData) ? \"No available hours left in classroom\" : \"\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c0, !ctx.classroomService.hasClassroomPackageHoursLeft(ctx.classRoomData)))(\"classroom\", ctx.classRoomData);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.classRoomData.lessons.length > 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"lessons\", ctx.classRoomData.lessons)(\"reverseGradient\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"classroom\", ctx.classRoomData)(\"lessons\", ctx.classRoomData.lessons)(\"reverseGradient\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"selectedClassroom\", ctx.classRoomData);\n        }\n      },\n      dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i1.RouterLink, i8.CalendarAgendaComponent, i9.LessonScheduleBoxComponent, i10.MiniLessonInfoCardComponent, i11.ButtonDirective, i12.Chip, i13.Tooltip, i14.LibraryComponent, i7.DatePipe],\n      styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}.gradient-block[_ngcontent-%COMP%]{background-image:linear-gradient(180deg,#dde2f8,#dde3f8 20%,#e1e6f9 38%,#e5eafa 53% 65%,#e9edfb 74%,#edf1fc 82%,#edf0fc,#f1f4fd,#f6f8fe)}.person__title[_ngcontent-%COMP%]{font-size:clamp(.75rem,.07vw + .74rem,.88rem);margin:0;color:#2d2a4b}.person__detail[_ngcontent-%COMP%]{margin-bottom:.5rem;font-weight:700;font-size:clamp(.88rem,.07vw + .86rem,1rem);color:#2d2a4b;word-break:break-all}.bio__subtitle[_ngcontent-%COMP%]{font-size:clamp(.75rem,.07vw + .74rem,.88rem)}.bio__detail[_ngcontent-%COMP%]{line-height:clamp(.88rem,.22vw + .83rem,1.25rem)}.circle[_ngcontent-%COMP%]{min-width:100px;min-height:100px;border-radius:200px;background-color:#ccd6ff;color:#fff;display:flex;justify-content:center;align-items:center}[_nghost-%COMP%]     .gradient-accordion .p-accordion-header-link{z-index:4;background:url(/assets/images/lessons-gradient-box-bg.png) no-repeat center center!important;border-radius:20px!important;padding:.725rem}[_nghost-%COMP%]     .gradient-accordion .p-accordion .p-accordion-header:not(.p-disabled).p-highlight .p-accordion-header-link{background:none!important;background:url(/assets/images/lessons-gradient-box-bg.png) no-repeat center center!important;border-radius:12px!important;box-shadow:none!important}[_nghost-%COMP%]     .gradient-accordion .p-accordion-header-text{color:#fff;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%)}[_nghost-%COMP%]     .gradient-accordion .p-accordion-toggle-icon{border:1px solid #fff;border-radius:50px;color:#fff;display:flex;align-items:center;justify-content:center;width:36px;height:36px}[_nghost-%COMP%]     .gradient-accordion .p-accordion .p-accordion-tab{box-shadow:none!important}[_nghost-%COMP%]     .gradient-accordion .p-accordion-header-link:focus{outline:none}[_nghost-%COMP%]     .gradient-accordion .p-accordion-header{background-size:cover;border:0;text-align:center}\"]\n    });\n  }\n  return TeacherProfileComponent;\n})();", "map": {"version": 3, "names": ["SubSink", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "ctx_r0", "hasRated", "_c1", "classroomService", "getClassroomRateUrl", "classRoomData", "ɵɵtextInterpolate", "generalService", "replaceImportedWithDash", "teacher", "skype", "teacher<PERSON><PERSON>", "userName", "ɵɵpipeBind2", "dob", "residence", "ɵɵtextInterpolate2", "convertTimezoneValueToText", "timeZone", "text", "getUserLocalTime", "ɵɵelementContainerStart", "lesson_r2", "ɵɵtemplate", "TeacherProfileComponent_ng_container_67_ng_container_1_Template", "lessons", "sort", "sortLessonsByRecentFirst", "TeacherProfileComponent", "constructor", "router", "route", "ratingAndReportService", "authService", "userService", "subs", "ngOnInit", "data", "efficient", "evaluate", "explain", "focused", "friendly", "interest", "polite", "preferences", "schedule", "trialLevel", "getSelectedClassroom", "getUserRole", "add", "getTeacherTrialRating", "id", "subscribe", "res", "isObjectEmpty", "fetchTeacherData", "initUpdateListener", "ngOnDestroy", "unsubscribe", "goToTrialRatingPage", "navigate", "getMedianRating", "ratings", "ratingValues", "Object", "values", "sortedRatings", "middleIndex", "Math", "floor", "length", "middleValue1", "middleValue2", "classroomUpdateListener", "getUserById", "aspUserId", "_", "ɵɵdirectiveInject", "i1", "Router", "ActivatedRoute", "i2", "GeneralService", "i3", "ClassroomService", "i4", "RatingAndReportService", "i5", "AuthService", "i6", "UserService", "_2", "selectors", "decls", "vars", "consts", "template", "TeacherProfileComponent_Template", "rf", "ctx", "TeacherProfileComponent_div_7_Template", "ɵɵlistener", "TeacherProfileComponent_Template_img_error_11_listener", "$event", "setDefaultUserAvatar", "TeacherProfileComponent_div_15_Template", "TeacherProfileComponent_ng_container_67_Template", "isTrialClassroomWithCompletedLesson", "getDomainFileNamePath", "avatarUrl", "ɵɵsanitizeUrl", "firstName", "lastName", "ɵɵtextInterpolate1", "introduce", "hasClassroomPackageHoursLeft"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\classrooms\\class\\class-info\\components\\teacher-profile\\teacher-profile.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\classrooms\\class\\class-info\\components\\teacher-profile\\teacher-profile.component.html"], "sourcesContent": ["import { Component, OnDestroy, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { take } from 'rxjs/operators';\r\nimport { User } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { RatingAndReportService } from 'src/app/core/services/rating-and-report.service';\r\nimport { UserService } from 'src/app/core/services/user.service';\r\nimport { SubSink } from 'subsink';\r\n\r\n@Component({\r\n  selector: 'app-teacher-profile',\r\n  templateUrl: './teacher-profile.component.html',\r\n  styleUrls: ['./teacher-profile.component.scss']\r\n})\r\nexport class TeacherProfileComponent implements OnInit, OnDestroy {\r\n  private subs = new SubSink();\r\n  classRoomData: any;\r\n  teacherData: User = {} as User;\r\n  hasRated = false;\r\n  constructor(\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    public generalService: GeneralService,\r\n    public classroomService: ClassroomService,\r\n    public ratingAndReportService: RatingAndReportService,\r\n    public authService: AuthService,\r\n    public userService: UserService,\r\n  ) {\r\n  }\r\n\r\n  ngOnInit(): void {\r\n\r\n    const data = {\r\n      efficient: \"Good\",\r\n      evaluate: \"Good\",\r\n      explain: \"Neutral\",\r\n      focused: \"Neutral\",\r\n      friendly: \"Neutral\",\r\n      interest: \"Good\",\r\n      polite: \"Neutral\",\r\n      preferences: \"Good\",\r\n      schedule: \"Neutral\",\r\n      trialLevel: \"Neutral\"\r\n    };\r\n\r\n    this.classRoomData = this.classroomService.getSelectedClassroom();\r\n    this.authService.getUserRole();\r\n    this.subs.add(this.ratingAndReportService.getTeacherTrialRating(this.classRoomData.id).subscribe((res)=> {\r\n      if (res) {\r\n        this.hasRated = !this.generalService.isObjectEmpty(res);\r\n      }\r\n    }));\r\n\r\n    this.fetchTeacherData();\r\n    this.initUpdateListener();\r\n    // console.log(this.router.getCurrentNavigation()!.extras.state);\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  goToTrialRatingPage() {\r\n    this.router.navigate(['/dashboard/classrooms/rate-trial']);\r\n  }\r\n\r\n  getMedianRating(ratings: any): string | number {\r\n    const ratingValues = Object.values(ratings);\r\n    const sortedRatings = ratingValues.sort();\r\n  \r\n    const middleIndex = Math.floor(sortedRatings.length / 2);\r\n  \r\n    if (sortedRatings.length % 2 === 0) {\r\n      const middleValue1 = sortedRatings[middleIndex - 1] as number;\r\n      const middleValue2 = sortedRatings[middleIndex] as number;\r\n      return (middleValue1 + middleValue2) / 2;\r\n    } else {\r\n      return sortedRatings[middleIndex] as string;\r\n    }\r\n  }\r\n\r\n  private initUpdateListener() {\r\n    this.subs.add(this.classroomService.classroomUpdateListener.subscribe((res: any) => {\r\n      if (res) {\r\n        this.classRoomData = this.classroomService.getSelectedClassroom();\r\n      }\r\n    }));\r\n  }\r\n\r\n  /**\r\n * Fetches the user data for the teacher associated with the classroom.\r\n */\r\n  private fetchTeacherData(): void {\r\n    // Subscribe to the getUserById observable\r\n    this.subs.add(\r\n      this.userService.getUserById(this.classRoomData.teacher.aspUserId).subscribe((res: User) => {\r\n        this.teacherData = res;\r\n      })\r\n    );\r\n  }\r\n}\r\n", "<div class=\"grid p-2\">\r\n    <div class=\"col-12 lg:col-6\">\r\n\r\n        <div class=\"gradient-block py-2 px-4 border-round-xl\">\r\n\r\n            <div class=\"flex align-items-center justify-content-between\">\r\n            <h5 class=\"mb-0 text-primary font-bold\">Your Teacher</h5>\r\n\r\n            <div class=\" my-1 relative\">\r\n                <div class=\" flex justify-content-center\" *ngIf=\"classroomService.isTrialClassroomWithCompletedLesson(classRoomData)\">\r\n                    <div class=\"flex align-items-center gap-1\" \r\n                    [ngClass]=\"{'disabled-link':  hasRated}\" \r\n                    [pTooltip]=\"hasRated ? 'Already rated by student' : ''\"\r\n                    [routerLink]=\"[classroomService.getClassroomRateUrl(classRoomData)]\">\r\n                        <div class=\"gray-border-button font-xs p-1 border-round-3xl line-height-1\">Rate your experience <img\r\n                            src=\"/assets/icons/lessons/rating-stars.svg\" height=\"14\" class=\"ml-1\" /></div>\r\n                    </div>\r\n            </div>\r\n            <!-- <p-chip class=\" info bg-blue-400 border-round-xl\">\r\n                <div class=\"content text-white\">{{classRoomData?.teacher.firstName}} {{classRoomData?.teacher.lastName}}</div>\r\n            </p-chip> -->\r\n        </div>\r\n    </div>\r\n\r\n        <li class=\"flex flex-column md:flex-row md:align-items-center md:justify-content-between mb-4\">\r\n            <div class=\"flex max-w-17rem align-items-center gap-2\">\r\n        \r\n                <div class=\"avatar-circle w-4rem\">\r\n                    <img [src]=\"generalService.getDomainFileNamePath(classRoomData?.teacher.avatarUrl)\"\r\n                        (error)=\"generalService.setDefaultUserAvatar($event)\" class=\"border-circle\">\r\n                </div>\r\n        \r\n                <p-chip class=\" info bg-blue-400 border-round-xl\">\r\n                    <div class=\"content text-white\">{{classRoomData?.teacher.firstName}} {{classRoomData?.teacher.lastName}}</div>\r\n                </p-chip>\r\n\r\n                <!-- *ngIf=\"classroomService.isTrialClassroomWithCompletedFirstLesson(classRoomData)\" -->\r\n                \r\n            \r\n            </div>\r\n        \r\n        </li>\r\n\r\n            <div class=\"grid\" *ngIf=\"!this.generalService.isObjectEmpty(teacherData)\">\r\n                <div class=\"flex flex-column md:ml-3\">\r\n                    <div class=\"flex gap-2 mb-2\">\r\n                        <div class=\"flex flex-column\">\r\n                            <p class=\"person__title\">Skype</p>\r\n                            <p class=\"person__detail\">{{generalService.replaceImportedWithDash(classRoomData?.teacher.skype)}}</p>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"flex gap-2 mb-2\">\r\n                        <div class=\"flex flex-column\">\r\n                            <p class=\"person__title\">E-mail</p>\r\n                            <p class=\"person__detail\">{{(teacherData.userName)}}</p>\r\n                        </div>\r\n                    </div>\r\n                    <!-- <h5 class=\"pb-0 mb-1 text-primary font-bold\"><span>{{classRoomData?.teacher.firstName}}’s Information</span></h5> -->\r\n                    <div class=\"flex gap-2 mb-2\">\r\n                        \r\n                        <div class=\"flex flex-column w-8rem\">\r\n                            <p class=\"person__title\">Birthday</p>\r\n                            <p class=\"person__detail\">{{classRoomData?.teacher.dob | date: 'd/M/y'}}</p>\r\n                        </div>\r\n                        <div class=\"flex flex-column w-8rem\">\r\n                            <p class=\"person__title\">Location</p>\r\n                            <p class=\"person__detail\">{{generalService.replaceImportedWithDash(classRoomData?.teacher.residence)}}</p>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"flex gap-2 mb-2\">\r\n                        <div class=\"flex flex-column\">\r\n                            <p class=\"person__title\">Timezone</p>\r\n                            <p class=\"person__detail\">\r\n                                \r\n                                {{generalService.convertTimezoneValueToText(teacherData?.timeZone).text}} - \r\n                {{generalService.getUserLocalTime(generalService.convertTimezoneValueToText(teacherData?.timeZone).text, false)}}\r\n                            </p>\r\n                        </div>\r\n                    </div>\r\n                    \r\n                </div>\r\n                \r\n            </div>\r\n        </div>\r\n    </div>\r\n    <!-- ends col6 -->\r\n\r\n    <div class=\"col-12 lg:col-6\">\r\n        <div class=\" flex flex-column justify-content-between h-full gap-2\">\r\n        <div class=\"gradient-block py-2 px-4 border-round-xl h-full\">\r\n            <h5 class=\"bio__title mb-0 text-primary font-bold\">Bio</h5>\r\n            <p class=\"bio__subtitle text-primary\">{{classRoomData?.teacher.firstName}}’s Introduction</p>\r\n            <p class=\"bio__detail font-sm\">\r\n                {{generalService.replaceImportedWithDash(classRoomData?.teacher.introduce)}}\r\n            </p>\r\n        </div>\r\n\r\n        <div class=\"gradient-block py-2 px-4 border-round-xl hidden\">\r\n            <div class=\"flex justify-content-between mb-3\">\r\n                <h5 class=\"bio__title mb-0 text-primary font-bold\">Ratings</h5>\r\n                <button pButton type=\"button\"\r\n                    class=\"p-button-outlined p-1 p-button-rounded button-outlined p-button-sm\">\r\n                    <div class=\"flex align-items-center gap-1\">\r\n                        <span class=\"fl-typo s12-16\">See Reviews</span>\r\n                        <i class=\"pi pi-star\"></i>\r\n                    </div>\r\n                </button>\r\n            </div>\r\n\r\n            <div class=\"flex gap-3 flex-column sm:flex-row justify-content-center align-items-center\">\r\n                <div class=\"circle text-center text-primary\">\r\n                    <div class=\"flex flex-column gap-3\">\r\n                        <span class=\"text-3xl\">5</span>\r\n                        <div class=\"flex\">\r\n                            <i class=\"pi pi-star-fill\"></i>\r\n                            <i class=\"pi pi-star-fill\"></i>\r\n                            <i class=\"pi pi-star-fill\"></i>\r\n                            <i class=\"pi pi-star-fill\"></i>\r\n                            <i class=\"pi pi-star-fill\"></i>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"circle text-center   text-primary\">\r\n                    <div class=\"flex flex-column align-items-center\">\r\n                        <div class=\"flex justify-content-center\">\r\n                            <img class=\"rating-icon\" src=\"/assets/icons/classroom/rate/smile.svg\" />\r\n                        </div>\r\n                        <span class=\"text-md mb-1\">Excellent!</span>\r\n                        <span class=\"font-sm line-height-1 max-w-4rem\">Satisfaction rate</span>\r\n\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"circle text-center text-primary\">\r\n                    <div class=\"flex flex-column gap-2\">\r\n\r\n                        <span class=\"text-3xl\">37</span>\r\n                        <div class=\"flex flex-row align-items-center justify-content-center gap-1 ml-3\">\r\n                            <img src=\"/assets/images/dashboard/classRoomData?.teacher.svg\" width=\"20\" alt=\"Student Image\">\r\n                            <span class=\"font-xs line-height-1 text-left max-w-3rem\">Total Students</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n\r\n    <!-- ends col6 -->\r\n<!-- <ng-container *ngIf=\"!classroomService.isTrialClassroom(classRoomData)\">\r\n    <app-teacher-rate [classroom]=\"classRoomData\" [showBlockGradient]=\"false\"></app-teacher-rate>\r\n</ng-container>  -->\r\n<div class=\"w-full\">\r\n    <div class=\"col-12\">\r\n        <div class=\"gradient-block\">\r\n            <div class=\"flex justify-content-start p-4 grid\">\r\n\r\n                <div class=\" col-6 md:col-2\" [pTooltip]=\"!classroomService.hasClassroomPackageHoursLeft(classRoomData) ? 'No available hours left in classroom': ''\" tooltipPosition=\"top\">\r\n                <app-mini-lesson-info-card [ngClass]=\"{'disabled-link': !classroomService.hasClassroomPackageHoursLeft(classRoomData)}\" cardType=\"request\"  \r\n                [classroom]=\"classRoomData\">\r\n            </app-mini-lesson-info-card>\r\n                </div>\r\n              <ng-container *ngIf=\"classRoomData.lessons.length > 0\">\r\n                <ng-container *ngFor=\"let lesson of classRoomData.lessons.sort(this.generalService.sortLessonsByRecentFirst); let i = index;\">\r\n                  <div class=\"col-6 md:col-2\">\r\n                    <app-mini-lesson-info-card [classroom]=\"classRoomData\" [lesson]=\"lesson\"></app-mini-lesson-info-card>\r\n                  </div>\r\n                </ng-container>\r\n              </ng-container>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n\r\n</div>\r\n\r\n<div class=\"grid\">\r\n    <div class=\"col-12 lg:col-6\">\r\n        <div class=\"gradient-block\">\r\n        <app-calendar-agenda [lessons]=\"classRoomData.lessons\" [reverseGradient]=\"true\"></app-calendar-agenda>\r\n        </div>\r\n    </div>\r\n\r\n    <div class=\"col-12 lg:col-6\">\r\n        <app-lesson-schedule-box [classroom]=\"classRoomData\" [lessons]=\"classRoomData.lessons\" [reverseGradient]=\"true\"></app-lesson-schedule-box>\r\n    </div>\r\n\r\n</div>\r\n\r\n\r\n\r\n<!-- <g-accordion title=\"Notes\" backgroundImage=\"/assets/images/lessons-gradient-box-bg.png\" elementTitle=\"element-notes\">\r\n \r\n    <div class=\"flex flex-column py-4 gap-3 align-items-center justify-content-center\">\r\n        <img #notesArrowSrc src=\"/assets/icons/classroom/sticky-notes.svg\" class=\"max-w-5rem\">\r\n        <p class=\"font-xl text-main font-semibold\">\r\n            \r\n            No notes have been shared with you.\r\n        </p>\r\n    </div>\r\n</g-accordion> -->\r\n\r\n\r\n<app-library [selectedClassroom]=\"classRoomData\"></app-library>\r\n"], "mappings": "AASA,SAASA,OAAO,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;;;;ICKTC,EALR,CAAAC,cAAA,cAAsH,cAI7C,cACU;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,SAAA,cACpB;IAExFH,EAFwF,CAAAI,YAAA,EAAM,EAChF,EACR;;;;IANEJ,EAAA,CAAAK,SAAA,EAAwC;IAExCL,EAFA,CAAAM,UAAA,YAAAN,EAAA,CAAAO,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,QAAA,EAAwC,aAAAD,MAAA,CAAAC,QAAA,mCACe,eAAAV,EAAA,CAAAO,eAAA,IAAAI,GAAA,EAAAF,MAAA,CAAAG,gBAAA,CAAAC,mBAAA,CAAAJ,MAAA,CAAAK,aAAA,GACa;;;;;IAkC5Dd,EAJhB,CAAAC,cAAA,cAA0E,cAChC,cACL,cACK,YACD;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAI,YAAA,EAAI;IAClCJ,EAAA,CAAAC,cAAA,YAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAwE;IAE1GF,EAF0G,CAAAI,YAAA,EAAI,EACpG,EACJ;IAGEJ,EAFR,CAAAC,cAAA,cAA6B,cACK,aACD;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAI,YAAA,EAAI;IACnCJ,EAAA,CAAAC,cAAA,aAA0B;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAE5DF,EAF4D,CAAAI,YAAA,EAAI,EACtD,EACJ;IAKEJ,EAHR,CAAAC,cAAA,eAA6B,eAEY,aACR;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAI,YAAA,EAAI;IACrCJ,EAAA,CAAAC,cAAA,aAA0B;IAAAD,EAAA,CAAAE,MAAA,IAA8C;;IAC5EF,EAD4E,CAAAI,YAAA,EAAI,EAC1E;IAEFJ,EADJ,CAAAC,cAAA,eAAqC,aACR;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAI,YAAA,EAAI;IACrCJ,EAAA,CAAAC,cAAA,aAA0B;IAAAD,EAAA,CAAAE,MAAA,IAA4E;IAE9GF,EAF8G,CAAAI,YAAA,EAAI,EACxG,EACJ;IAGEJ,EAFR,CAAAC,cAAA,eAA6B,eACK,aACD;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAI,YAAA,EAAI;IACrCJ,EAAA,CAAAC,cAAA,aAA0B;IAEtBD,EAAA,CAAAE,MAAA,IAEJ;IAMhBF,EANgB,CAAAI,YAAA,EAAI,EACF,EACJ,EAEJ,EAEJ;;;;IAlCoCJ,EAAA,CAAAK,SAAA,GAAwE;IAAxEL,EAAA,CAAAe,iBAAA,CAAAN,MAAA,CAAAO,cAAA,CAAAC,uBAAA,CAAAR,MAAA,CAAAK,aAAA,kBAAAL,MAAA,CAAAK,aAAA,CAAAI,OAAA,CAAAC,KAAA,EAAwE;IAMxEnB,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAe,iBAAA,CAAAN,MAAA,CAAAW,WAAA,CAAAC,QAAA,CAA0B;IAQ1BrB,EAAA,CAAAK,SAAA,GAA8C;IAA9CL,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAsB,WAAA,QAAAb,MAAA,CAAAK,aAAA,kBAAAL,MAAA,CAAAK,aAAA,CAAAI,OAAA,CAAAK,GAAA,WAA8C;IAI9CvB,EAAA,CAAAK,SAAA,GAA4E;IAA5EL,EAAA,CAAAe,iBAAA,CAAAN,MAAA,CAAAO,cAAA,CAAAC,uBAAA,CAAAR,MAAA,CAAAK,aAAA,kBAAAL,MAAA,CAAAK,aAAA,CAAAI,OAAA,CAAAM,SAAA,EAA4E;IAQlGxB,EAAA,CAAAK,SAAA,GAEJ;IAFIL,EAAA,CAAAyB,kBAAA,MAAAhB,MAAA,CAAAO,cAAA,CAAAU,0BAAA,CAAAjB,MAAA,CAAAW,WAAA,kBAAAX,MAAA,CAAAW,WAAA,CAAAO,QAAA,EAAAC,IAAA,SAAAnB,MAAA,CAAAO,cAAA,CAAAa,gBAAA,CAAApB,MAAA,CAAAO,cAAA,CAAAU,0BAAA,CAAAjB,MAAA,CAAAW,WAAA,kBAAAX,MAAA,CAAAW,WAAA,CAAAO,QAAA,EAAAC,IAAA,cAEJ;;;;;IAwFZ5B,EAAA,CAAA8B,uBAAA,GAA8H;IAC5H9B,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAG,SAAA,oCAAqG;IACvGH,EAAA,CAAAI,YAAA,EAAM;;;;;;IADuBJ,EAAA,CAAAK,SAAA,GAA2B;IAACL,EAA5B,CAAAM,UAAA,cAAAG,MAAA,CAAAK,aAAA,CAA2B,WAAAiB,SAAA,CAAkB;;;;;IAH9E/B,EAAA,CAAA8B,uBAAA,GAAuD;IACrD9B,EAAA,CAAAgC,UAAA,IAAAC,+DAAA,2BAA8H;;;;;IAA7FjC,EAAA,CAAAK,SAAA,EAA6E;IAA7EL,EAAA,CAAAM,UAAA,YAAAG,MAAA,CAAAK,aAAA,CAAAoB,OAAA,CAAAC,IAAA,CAAA1B,MAAA,CAAAO,cAAA,CAAAoB,wBAAA,EAA6E;;;ADpJ9H,WAAaC,uBAAuB;EAA9B,MAAOA,uBAAuB;IAKlCC,YACUC,MAAc,EACdC,KAAqB,EACtBxB,cAA8B,EAC9BJ,gBAAkC,EAClC6B,sBAA8C,EAC9CC,WAAwB,EACxBC,WAAwB;MANvB,KAAAJ,MAAM,GAANA,MAAM;MACN,KAAAC,KAAK,GAALA,KAAK;MACN,KAAAxB,cAAc,GAAdA,cAAc;MACd,KAAAJ,gBAAgB,GAAhBA,gBAAgB;MAChB,KAAA6B,sBAAsB,GAAtBA,sBAAsB;MACtB,KAAAC,WAAW,GAAXA,WAAW;MACX,KAAAC,WAAW,GAAXA,WAAW;MAXZ,KAAAC,IAAI,GAAG,IAAI7C,OAAO,EAAE;MAE5B,KAAAqB,WAAW,GAAS,EAAU;MAC9B,KAAAV,QAAQ,GAAG,KAAK;IAUhB;IAEAmC,QAAQA,CAAA;MAEN,MAAMC,IAAI,GAAG;QACXC,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,SAAS;QAClBC,OAAO,EAAE,SAAS;QAClBC,QAAQ,EAAE,SAAS;QACnBC,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAE,SAAS;QACjBC,WAAW,EAAE,MAAM;QACnBC,QAAQ,EAAE,SAAS;QACnBC,UAAU,EAAE;OACb;MAED,IAAI,CAAC1C,aAAa,GAAG,IAAI,CAACF,gBAAgB,CAAC6C,oBAAoB,EAAE;MACjE,IAAI,CAACf,WAAW,CAACgB,WAAW,EAAE;MAC9B,IAAI,CAACd,IAAI,CAACe,GAAG,CAAC,IAAI,CAAClB,sBAAsB,CAACmB,qBAAqB,CAAC,IAAI,CAAC9C,aAAa,CAAC+C,EAAE,CAAC,CAACC,SAAS,CAAEC,GAAG,IAAG;QACtG,IAAIA,GAAG,EAAE;UACP,IAAI,CAACrD,QAAQ,GAAG,CAAC,IAAI,CAACM,cAAc,CAACgD,aAAa,CAACD,GAAG,CAAC;QACzD;MACF,CAAC,CAAC,CAAC;MAEH,IAAI,CAACE,gBAAgB,EAAE;MACvB,IAAI,CAACC,kBAAkB,EAAE;MACzB;IACF;IAEAC,WAAWA,CAAA;MACT,IAAI,CAACvB,IAAI,CAACwB,WAAW,EAAE;IACzB;IAEAC,mBAAmBA,CAAA;MACjB,IAAI,CAAC9B,MAAM,CAAC+B,QAAQ,CAAC,CAAC,kCAAkC,CAAC,CAAC;IAC5D;IAEAC,eAAeA,CAACC,OAAY;MAC1B,MAAMC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAACH,OAAO,CAAC;MAC3C,MAAMI,aAAa,GAAGH,YAAY,CAACtC,IAAI,EAAE;MAEzC,MAAM0C,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACH,aAAa,CAACI,MAAM,GAAG,CAAC,CAAC;MAExD,IAAIJ,aAAa,CAACI,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;QAClC,MAAMC,YAAY,GAAGL,aAAa,CAACC,WAAW,GAAG,CAAC,CAAW;QAC7D,MAAMK,YAAY,GAAGN,aAAa,CAACC,WAAW,CAAW;QACzD,OAAO,CAACI,YAAY,GAAGC,YAAY,IAAI,CAAC;MAC1C,CAAC,MAAM;QACL,OAAON,aAAa,CAACC,WAAW,CAAW;MAC7C;IACF;IAEQX,kBAAkBA,CAAA;MACxB,IAAI,CAACtB,IAAI,CAACe,GAAG,CAAC,IAAI,CAAC/C,gBAAgB,CAACuE,uBAAuB,CAACrB,SAAS,CAAEC,GAAQ,IAAI;QACjF,IAAIA,GAAG,EAAE;UACP,IAAI,CAACjD,aAAa,GAAG,IAAI,CAACF,gBAAgB,CAAC6C,oBAAoB,EAAE;QACnE;MACF,CAAC,CAAC,CAAC;IACL;IAEA;;;IAGQQ,gBAAgBA,CAAA;MACtB;MACA,IAAI,CAACrB,IAAI,CAACe,GAAG,CACX,IAAI,CAAChB,WAAW,CAACyC,WAAW,CAAC,IAAI,CAACtE,aAAa,CAACI,OAAO,CAACmE,SAAS,CAAC,CAACvB,SAAS,CAAEC,GAAS,IAAI;QACzF,IAAI,CAAC3C,WAAW,GAAG2C,GAAG;MACxB,CAAC,CAAC,CACH;IACH;IAAC,QAAAuB,CAAA,G;uBArFUjD,uBAAuB,EAAArC,EAAA,CAAAuF,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAzF,EAAA,CAAAuF,iBAAA,CAAAC,EAAA,CAAAE,cAAA,GAAA1F,EAAA,CAAAuF,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAA5F,EAAA,CAAAuF,iBAAA,CAAAM,EAAA,CAAAC,gBAAA,GAAA9F,EAAA,CAAAuF,iBAAA,CAAAQ,EAAA,CAAAC,sBAAA,GAAAhG,EAAA,CAAAuF,iBAAA,CAAAU,EAAA,CAAAC,WAAA,GAAAlG,EAAA,CAAAuF,iBAAA,CAAAY,EAAA,CAAAC,WAAA;IAAA;IAAA,QAAAC,EAAA,G;YAAvBhE,uBAAuB;MAAAiE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVxB5G,EANZ,CAAAC,cAAA,aAAsB,aACW,aAE6B,aAEW,YACrB;UAAAD,EAAA,CAAAE,MAAA,mBAAY;UAAAF,EAAA,CAAAI,YAAA,EAAK;UAEzDJ,EAAA,CAAAC,cAAA,aAA4B;UACxBD,EAAA,CAAAgC,UAAA,IAAA8E,sCAAA,iBAAsH;UAalI9G,EADI,CAAAI,YAAA,EAAM,EACJ;UAMUJ,EAJZ,CAAAC,cAAA,YAA+F,aACpC,cAEjB,eAEkD;UAA5ED,EAAA,CAAA+G,UAAA,mBAAAC,uDAAAC,MAAA;YAAA,OAASJ,GAAA,CAAA7F,cAAA,CAAAkG,oBAAA,CAAAD,MAAA,CAA2C;UAAA,EAAC;UAC7DjH,EAFI,CAAAI,YAAA,EACgF,EAC9E;UAGFJ,EADJ,CAAAC,cAAA,kBAAkD,eACd;UAAAD,EAAA,CAAAE,MAAA,IAAwE;UAQpHF,EARoH,CAAAI,YAAA,EAAM,EACzG,EAKP,EAEL;UAEDJ,EAAA,CAAAgC,UAAA,KAAAmF,uCAAA,mBAA0E;UAyClFnH,EADI,CAAAI,YAAA,EAAM,EACJ;UAMEJ,EAHR,CAAAC,cAAA,cAA6B,eAC2C,eACP,cACN;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAI,YAAA,EAAK;UAC3DJ,EAAA,CAAAC,cAAA,aAAsC;UAAAD,EAAA,CAAAE,MAAA,IAAmD;UAAAF,EAAA,CAAAI,YAAA,EAAI;UAC7FJ,EAAA,CAAAC,cAAA,aAA+B;UAC3BD,EAAA,CAAAE,MAAA,IACJ;UACJF,EADI,CAAAI,YAAA,EAAI,EACF;UAIEJ,EAFR,CAAAC,cAAA,eAA6D,eACV,cACQ;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAI,YAAA,EAAK;UAIvDJ,EAHR,CAAAC,cAAA,kBAC+E,eAChC,gBACV;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAI,YAAA,EAAO;UAC/CJ,EAAA,CAAAG,SAAA,aAA0B;UAGtCH,EAFQ,CAAAI,YAAA,EAAM,EACD,EACP;UAKMJ,EAHZ,CAAAC,cAAA,eAA0F,eACzC,eACL,gBACT;UAAAD,EAAA,CAAAE,MAAA,SAAC;UAAAF,EAAA,CAAAI,YAAA,EAAO;UAC/BJ,EAAA,CAAAC,cAAA,eAAkB;UAKdD,EAJA,CAAAG,SAAA,aAA+B,aACA,aACA,aACA,aACA;UAG3CH,EAFQ,CAAAI,YAAA,EAAM,EACJ,EACJ;UAIEJ,EAFR,CAAAC,cAAA,eAA+C,eACM,eACJ;UACrCD,EAAA,CAAAG,SAAA,eAAwE;UAC5EH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAI,YAAA,EAAO;UAC5CJ,EAAA,CAAAC,cAAA,gBAA+C;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAGxEF,EAHwE,CAAAI,YAAA,EAAO,EAErE,EACJ;UAKEJ,EAHR,CAAAC,cAAA,eAA6C,eACL,gBAET;UAAAD,EAAA,CAAAE,MAAA,UAAE;UAAAF,EAAA,CAAAI,YAAA,EAAO;UAChCJ,EAAA,CAAAC,cAAA,eAAgF;UAC5ED,EAAA,CAAAG,SAAA,eAA8F;UAC9FH,EAAA,CAAAC,cAAA,gBAAyD;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAOnGF,EAPmG,CAAAI,YAAA,EAAO,EAC5E,EACJ,EACJ,EACJ,EACJ,EACJ,EACJ;UAWUJ,EALhB,CAAAC,cAAA,eAAoB,eACI,eACY,eACyB,eAE8H;UAC3KD,EAAA,CAAAG,SAAA,qCAEwB;UACxBH,EAAA,CAAAI,YAAA,EAAM;UACRJ,EAAA,CAAAgC,UAAA,KAAAoF,gDAAA,2BAAuD;UAYrEpH,EALY,CAAAI,YAAA,EAAM,EACJ,EACJ,EACJ,EAEA;UAIEJ,EAFR,CAAAC,cAAA,eAAkB,cACe,eACG;UAC5BD,EAAA,CAAAG,SAAA,+BAAsG;UAE1GH,EADI,CAAAI,YAAA,EAAM,EACJ;UAENJ,EAAA,CAAAC,cAAA,cAA6B;UACzBD,EAAA,CAAAG,SAAA,mCAA0I;UAGlJH,EAFI,CAAAI,YAAA,EAAM,EAEJ;UAgBNJ,EAAA,CAAAG,SAAA,uBAA+D;;;UAnMJH,EAAA,CAAAK,SAAA,GAAyE;UAAzEL,EAAA,CAAAM,UAAA,SAAAuG,GAAA,CAAAjG,gBAAA,CAAAyG,mCAAA,CAAAR,GAAA,CAAA/F,aAAA,EAAyE;UAmB3Gd,EAAA,CAAAK,SAAA,GAA8E;UAA9EL,EAAA,CAAAM,UAAA,QAAAuG,GAAA,CAAA7F,cAAA,CAAAsG,qBAAA,CAAAT,GAAA,CAAA/F,aAAA,kBAAA+F,GAAA,CAAA/F,aAAA,CAAAI,OAAA,CAAAqG,SAAA,GAAAvH,EAAA,CAAAwH,aAAA,CAA8E;UAKnDxH,EAAA,CAAAK,SAAA,GAAwE;UAAxEL,EAAA,CAAAyB,kBAAA,KAAAoF,GAAA,CAAA/F,aAAA,kBAAA+F,GAAA,CAAA/F,aAAA,CAAAI,OAAA,CAAAuG,SAAA,OAAAZ,GAAA,CAAA/F,aAAA,kBAAA+F,GAAA,CAAA/F,aAAA,CAAAI,OAAA,CAAAwG,QAAA,KAAwE;UAU7F1H,EAAA,CAAAK,SAAA,EAAqD;UAArDL,EAAA,CAAAM,UAAA,UAAAuG,GAAA,CAAA7F,cAAA,CAAAgD,aAAA,CAAA6C,GAAA,CAAAzF,WAAA,EAAqD;UAgDlCpB,EAAA,CAAAK,SAAA,GAAmD;UAAnDL,EAAA,CAAA2H,kBAAA,KAAAd,GAAA,CAAA/F,aAAA,kBAAA+F,GAAA,CAAA/F,aAAA,CAAAI,OAAA,CAAAuG,SAAA,yBAAmD;UAErFzH,EAAA,CAAAK,SAAA,GACJ;UADIL,EAAA,CAAA2H,kBAAA,MAAAd,GAAA,CAAA7F,cAAA,CAAAC,uBAAA,CAAA4F,GAAA,CAAA/F,aAAA,kBAAA+F,GAAA,CAAA/F,aAAA,CAAAI,OAAA,CAAA0G,SAAA,OACJ;UAgEiC5H,EAAA,CAAAK,SAAA,IAAuH;UAAvHL,EAAA,CAAAM,UAAA,cAAAuG,GAAA,CAAAjG,gBAAA,CAAAiH,4BAAA,CAAAhB,GAAA,CAAA/F,aAAA,gDAAuH;UACzHd,EAAA,CAAAK,SAAA,EAA4F;UACvHL,EAD2B,CAAAM,UAAA,YAAAN,EAAA,CAAAO,eAAA,KAAAC,GAAA,GAAAqG,GAAA,CAAAjG,gBAAA,CAAAiH,4BAAA,CAAAhB,GAAA,CAAA/F,aAAA,GAA4F,cAAA+F,GAAA,CAAA/F,aAAA,CAC5F;UAGdd,EAAA,CAAAK,SAAA,EAAsC;UAAtCL,EAAA,CAAAM,UAAA,SAAAuG,GAAA,CAAA/F,aAAA,CAAAoB,OAAA,CAAA8C,MAAA,KAAsC;UAiBtChF,EAAA,CAAAK,SAAA,GAAiC;UAACL,EAAlC,CAAAM,UAAA,YAAAuG,GAAA,CAAA/F,aAAA,CAAAoB,OAAA,CAAiC,yBAAyB;UAKtDlC,EAAA,CAAAK,SAAA,GAA2B;UAAmCL,EAA9D,CAAAM,UAAA,cAAAuG,GAAA,CAAA/F,aAAA,CAA2B,YAAA+F,GAAA,CAAA/F,aAAA,CAAAoB,OAAA,CAAkC,yBAAyB;UAmB1GlC,EAAA,CAAAK,SAAA,EAAmC;UAAnCL,EAAA,CAAAM,UAAA,sBAAAuG,GAAA,CAAA/F,aAAA,CAAmC;;;;;;;SD5LnCuB,uBAAuB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}