{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/homework.service\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"primeng/checkbox\";\nexport class ChooseTagsComponent {\n  constructor(homeworkService) {\n    this.homeworkService = homeworkService;\n    this.applyToAll = false;\n    this.file = {};\n    this.libraryFile = {};\n    this.chooseTag = new EventEmitter();\n    this.tagsSubmitted = new EventEmitter();\n    this.tagsToAll = [];\n  }\n  ngOnInit() {\n    console.log(this.libraryFile);\n    this.homeworkService.tagToAll.subscribe(res => {\n      if (res.tag !== \"\") {\n        if (res.add) {\n          this.tagsToAll.push(res.tag);\n        } else {\n          let taskIndex = this.tagsToAll.findIndex(el => {\n            return el == res.tag;\n          });\n          this.tagsToAll.splice(taskIndex, 1);\n        }\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.homeworkService.setTagToAll({\n      tag: \"\",\n      add: false\n    });\n  }\n  onChooseTag(event, tag, isLevel, isCategory) {\n    this.chooseTag.emit({\n      checked: event.checked,\n      tag: tag,\n      isLevel: isLevel,\n      isCategory: isCategory,\n      applyToAll: this.applyToAll,\n      file: this.file\n    });\n    if (this.applyToAll) {\n      this.homeworkService.setTagToAll({\n        tag: tag,\n        add: event.checked\n      });\n    }\n  }\n  getIsChecked(el) {\n    return this.tagsToAll.includes(el);\n  }\n  getIsCheckedCategory(el) {\n    return this.libraryFile && this.libraryFile.categories ? this.libraryFile.categories.includes(el) : this.tagsToAll.includes(el);\n  }\n  getIsCheckedLevel(el) {\n    return this.libraryFile && this.libraryFile.levels ? this.libraryFile.levels.includes(el) : this.tagsToAll.includes(el);\n  }\n  onTagsSubmitted() {\n    this.tagsSubmitted.emit();\n  }\n  static #_ = this.ɵfac = function ChooseTagsComponent_Factory(t) {\n    return new (t || ChooseTagsComponent)(i0.ɵɵdirectiveInject(i1.HomeworkService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ChooseTagsComponent,\n    selectors: [[\"app-choose-tags\"]],\n    inputs: {\n      applyToAll: \"applyToAll\",\n      file: \"file\",\n      libraryFile: \"libraryFile\"\n    },\n    outputs: {\n      chooseTag: \"chooseTag\",\n      tagsSubmitted: \"tagsSubmitted\"\n    },\n    decls: 44,\n    vars: 30,\n    consts: [[1, \"library-upload-details\"], [1, \"library-upload-details-right\"], [1, \"library-upload-checkboxes\"], [1, \"flex\", \"w-full\"], [1, \"w-5rem\", \"text-right\", \"mr-2\"], [1, \"font-xs\", \"text-primary\", \"mr-2\"], [1, \"w-full\", \"flex\", \"flex-wrap\", \"gap-1\", \"align-items-center\", \"justify-content-start\"], [1, \"library-upload-checkbox\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"A1\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"A2\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"B1\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"B2\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"C1\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"C2\", 3, \"onChange\", \"ngModel\", \"binary\"], [1, \"font-xs\", \"text-primary\"], [1, \"w-full\", \"flex\", \"flex-wrap\", \"align-items-center\", \"justify-content-start\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"Reading\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"Writing\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"Listening\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"Speaking\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"Grammar\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"Vocabulary\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"Test\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"Homework\", 3, \"onChange\", \"ngModel\", \"binary\"], [\"name\", \"groupname\", \"styleClass\", \"primary-blue\", \"labelStyleClass\", \"primary-blue\", \"label\", \"Units\", 3, \"onChange\", \"ngModel\", \"binary\"]],\n    template: function ChooseTagsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n        i0.ɵɵtext(6, \" Level: \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"div\", 6)(8, \"div\", 7)(9, \"p-checkbox\", 8);\n        i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_9_listener($event) {\n          return ctx.onChooseTag($event, \"A1\", true, false);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 7)(11, \"p-checkbox\", 9);\n        i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_11_listener($event) {\n          return ctx.onChooseTag($event, \"A2\", true, false);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(12, \"div\", 7)(13, \"p-checkbox\", 10);\n        i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_13_listener($event) {\n          return ctx.onChooseTag($event, \"B1\", true, false);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"div\", 7)(15, \"p-checkbox\", 11);\n        i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_15_listener($event) {\n          return ctx.onChooseTag($event, \"B2\", true, false);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"div\", 7)(17, \"p-checkbox\", 12);\n        i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_17_listener($event) {\n          return ctx.onChooseTag($event, \"C1\", true, false);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"div\", 7)(19, \"p-checkbox\", 13);\n        i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_19_listener($event) {\n          return ctx.onChooseTag($event, \"C2\", true, false);\n        });\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(20, \"div\", 2)(21, \"div\", 3)(22, \"div\", 4)(23, \"div\", 14);\n        i0.ɵɵtext(24, \" Categories: \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(25, \"div\", 15)(26, \"div\", 7)(27, \"p-checkbox\", 16);\n        i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_27_listener($event) {\n          return ctx.onChooseTag($event, \"Reading\", false, true);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(28, \"div\", 7)(29, \"p-checkbox\", 17);\n        i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_29_listener($event) {\n          return ctx.onChooseTag($event, \"Writing\", false, true);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(30, \"div\", 7)(31, \"p-checkbox\", 18);\n        i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_31_listener($event) {\n          return ctx.onChooseTag($event, \"Listening\", false, true);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(32, \"div\", 7)(33, \"p-checkbox\", 19);\n        i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_33_listener($event) {\n          return ctx.onChooseTag($event, \"Speaking\", false, true);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(34, \"div\", 7)(35, \"p-checkbox\", 20);\n        i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_35_listener($event) {\n          return ctx.onChooseTag($event, \"Grammar\", false, true);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(36, \"div\", 7)(37, \"p-checkbox\", 21);\n        i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_37_listener($event) {\n          return ctx.onChooseTag($event, \"Vocabulary\", false, true);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(38, \"div\", 7)(39, \"p-checkbox\", 22);\n        i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_39_listener($event) {\n          return ctx.onChooseTag($event, \"Test\", false, true);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(40, \"div\", 7)(41, \"p-checkbox\", 23);\n        i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_41_listener($event) {\n          return ctx.onChooseTag($event, \"Homework\", false, true);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(42, \"div\", 7)(43, \"p-checkbox\", 24);\n        i0.ɵɵlistener(\"onChange\", function ChooseTagsComponent_Template_p_checkbox_onChange_43_listener($event) {\n          return ctx.onChooseTag($event, \"Units\", false, true);\n        });\n        i0.ɵɵelementEnd()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedLevel(\"A1\"))(\"binary\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedLevel(\"A2\"))(\"binary\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedLevel(\"B1\"))(\"binary\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedLevel(\"B2\"))(\"binary\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedLevel(\"C1\"))(\"binary\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedLevel(\"C2\"))(\"binary\", true);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedCategory(\"Reading\"))(\"binary\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedCategory(\"Writing\"))(\"binary\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedCategory(\"Listening\"))(\"binary\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedCategory(\"Speaking\"))(\"binary\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedCategory(\"Grammar\"))(\"binary\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedCategory(\"Vocabulary\"))(\"binary\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedCategory(\"Test\"))(\"binary\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedCategory(\"Homework\"))(\"binary\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngModel\", ctx.getIsCheckedCategory(\"Units\"))(\"binary\", true);\n      }\n    },\n    dependencies: [i2.NgControlStatus, i2.NgModel, i3.Checkbox],\n    styles: [\".library-upload-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.library-upload-details[_ngcontent-%COMP%]   .library-upload-details-left[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%], .library-upload-details[_ngcontent-%COMP%]   .library-upload-details-right[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n}\\n.library-upload-details[_ngcontent-%COMP%]   .library-upload-details-right[_ngcontent-%COMP%]   .library-upload-checkboxes[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n.library-upload-details[_ngcontent-%COMP%]   .library-upload-details-right[_ngcontent-%COMP%]   .library-upload-checkboxes[_ngcontent-%COMP%]   .library-upload-checkbox[_ngcontent-%COMP%] {\\n  margin-right: 20px;\\n  margin-bottom: 10px;\\n  display: flex;\\n}\\n.library-upload-details[_ngcontent-%COMP%]   .library-upload-details-right[_ngcontent-%COMP%]   .library-upload-checkboxes[_ngcontent-%COMP%]   .library-upload-checkbox[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n}\\n\\n.e-checkbox-wrapper[_ngcontent-%COMP%]   .e-label[_ngcontent-%COMP%] {\\n  color: var(--primary-color);\\n}\\n\\n.e-checkbox-wrapper.e-primary[_ngcontent-%COMP%]   .e-frame[_ngcontent-%COMP%] {\\n  border-radius: 50px;\\n  border-color: #5BB7D0;\\n}\\n.e-checkbox-wrapper.e-primary[_ngcontent-%COMP%]   .e-frame.e-check[_ngcontent-%COMP%] {\\n  background-color: #5BB7D0;\\n}\\n\\n[_nghost-%COMP%]     p-checkbox {\\n  width: 5.4rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL3VwbG9hZC1maWxlcy9jaG9vc2UtdGFncy9jaG9vc2UtdGFncy5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGFBQUE7RUFDQSxzQkFBQTtBQUNKO0FBRVE7O0VBQ0ksZ0JBQUE7QUFDWjtBQUlRO0VBQ0ksYUFBQTtBQUZaO0FBR1k7RUFDSSxrQkFBQTtFQUNBLG1CQUFBO0VBQ0EsYUFBQTtBQURoQjtBQUVnQjtFQUNJLGlCQUFBO0FBQXBCOztBQU1BO0VBRUksMkJBQUE7QUFKSjs7QUFPQTtFQUNJLG1CQUFBO0VBQ0EscUJBQUE7QUFKSjtBQUtJO0VBQ0kseUJBQUE7QUFIUjs7QUFTSTtFQUNJLGFBQUE7QUFOUiIsInNvdXJjZXNDb250ZW50IjpbIi5saWJyYXJ5LXVwbG9hZC1kZXRhaWxzIHtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgLmxpYnJhcnktdXBsb2FkLWRldGFpbHMtbGVmdCxcclxuICAgIC5saWJyYXJ5LXVwbG9hZC1kZXRhaWxzLXJpZ2h0IHtcclxuICAgICAgICA+IGRpdiB7XHJcbiAgICAgICAgICAgIG1hcmdpbi10b3A6IDE1cHg7XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG4gICAgLmxpYnJhcnktdXBsb2FkLWRldGFpbHMtcmlnaHQge1xyXG4gICAgICAgIC8vIG1hcmdpbi1sZWZ0OiAxMHB4O1xyXG4gICAgICAgIC5saWJyYXJ5LXVwbG9hZC1jaGVja2JveGVzIHtcclxuICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAgLmxpYnJhcnktdXBsb2FkLWNoZWNrYm94IHtcclxuICAgICAgICAgICAgICAgIG1hcmdpbi1yaWdodDogMjBweDtcclxuICAgICAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDEwcHg7XHJcbiAgICAgICAgICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgICAgICAgICAgPiBkaXYge1xyXG4gICAgICAgICAgICAgICAgICAgIG1hcmdpbi1sZWZ0OiAxMHB4O1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgfVxyXG4gICAgfVxyXG59XHJcbi5lLWNoZWNrYm94LXdyYXBwZXIgLmUtbGFiZWwge1xyXG4gICAgXHJcbiAgICBjb2xvcjogdmFyKC0tcHJpbWFyeS1jb2xvcik7XHJcbn1cclxuXHJcbi5lLWNoZWNrYm94LXdyYXBwZXIuZS1wcmltYXJ5IC5lLWZyYW1lIHtcclxuICAgIGJvcmRlci1yYWRpdXM6IDUwcHg7XHJcbiAgICBib3JkZXItY29sb3I6ICM1QkI3RDA7XHJcbiAgICAmLmUtY2hlY2sge1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICM1QkI3RDA7XHJcbiAgICB9XHJcbn1cclxuXHJcbjpob3N0IDo6bmctZGVlcCB7IFxyXG5cclxuICAgIHAtY2hlY2tib3gge1xyXG4gICAgICAgIHdpZHRoOiA1LjRyZW07XHJcbiAgICB9XHJcblxyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "ChooseTagsComponent", "constructor", "homeworkService", "applyToAll", "file", "libraryFile", "chooseTag", "tagsSubmitted", "tagsToAll", "ngOnInit", "console", "log", "tagToAll", "subscribe", "res", "tag", "add", "push", "taskIndex", "findIndex", "el", "splice", "ngOnDestroy", "setTagToAll", "onChooseTag", "event", "isLevel", "isCategory", "emit", "checked", "getIsChecked", "includes", "getIsCheckedCategory", "categories", "getIsCheckedLevel", "levels", "onTagsSubmitted", "_", "i0", "ɵɵdirectiveInject", "i1", "HomeworkService", "_2", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "ChooseTagsComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ChooseTagsComponent_Template_p_checkbox_onChange_9_listener", "$event", "ChooseTagsComponent_Template_p_checkbox_onChange_11_listener", "ChooseTagsComponent_Template_p_checkbox_onChange_13_listener", "ChooseTagsComponent_Template_p_checkbox_onChange_15_listener", "ChooseTagsComponent_Template_p_checkbox_onChange_17_listener", "ChooseTagsComponent_Template_p_checkbox_onChange_19_listener", "ChooseTagsComponent_Template_p_checkbox_onChange_27_listener", "ChooseTagsComponent_Template_p_checkbox_onChange_29_listener", "ChooseTagsComponent_Template_p_checkbox_onChange_31_listener", "ChooseTagsComponent_Template_p_checkbox_onChange_33_listener", "ChooseTagsComponent_Template_p_checkbox_onChange_35_listener", "ChooseTagsComponent_Template_p_checkbox_onChange_37_listener", "ChooseTagsComponent_Template_p_checkbox_onChange_39_listener", "ChooseTagsComponent_Template_p_checkbox_onChange_41_listener", "ChooseTagsComponent_Template_p_checkbox_onChange_43_listener", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\upload-files\\choose-tags\\choose-tags.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\upload-files\\choose-tags\\choose-tags.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\r\nimport { Category, Homework } from 'src/app/core/models/homework.model';\r\nimport { Library, LibraryFile } from 'src/app/core/models/library.model';\r\nimport { HomeworkService } from 'src/app/core/services/homework.service';\r\n\r\n@Component({\r\n  selector: 'app-choose-tags',\r\n  templateUrl: './choose-tags.component.html',\r\n  styleUrls: ['./choose-tags.component.scss']\r\n})\r\nexport class ChooseTagsComponent implements OnInit {\r\n  @Input() applyToAll: boolean = false;\r\n  @Input() file: File = {} as File;\r\n  @Input() libraryFile: LibraryFile = {} as LibraryFile;\r\n  @Output() chooseTag = new EventEmitter();\r\n  @Output() tagsSubmitted = new EventEmitter();\r\n  tagsToAll: string[] = []\r\n\r\n\r\n  constructor(\r\n    private homeworkService: HomeworkService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    console.log(this.libraryFile);\r\n    this.homeworkService.tagToAll.subscribe(res => {\r\n      if (res.tag !== \"\") {\r\n        if (res.add) {\r\n          this.tagsToAll.push(res.tag)\r\n        } else {\r\n          let taskIndex = this.tagsToAll.findIndex(((el: any) => {\r\n            return el == res.tag\r\n          }));\r\n          this.tagsToAll.splice(taskIndex, 1);\r\n        }\r\n      }\r\n    })\r\n  }\r\n\r\n  ngOnDestroy(){\r\n    this.homeworkService.setTagToAll({ tag: \"\", add: false })\r\n  }\r\n\r\n  onChooseTag(event: any, tag: string, isLevel: boolean, isCategory: boolean) {\r\n    this.chooseTag.emit({\r\n      checked: event.checked,\r\n      tag: tag,\r\n      isLevel: isLevel,\r\n      isCategory: isCategory,\r\n      applyToAll: this.applyToAll,\r\n      file: this.file\r\n    })\r\n    if (this.applyToAll) {\r\n      this.homeworkService.setTagToAll({ tag: tag, add: event.checked })\r\n    }\r\n  }\r\n\r\n  getIsChecked(el: string) {\r\n    return this.tagsToAll.includes(el);\r\n  }\r\n\r\n  getIsCheckedCategory(el: any) {\r\n    return this.libraryFile && this.libraryFile.categories ? this.libraryFile.categories.includes(el) : this.tagsToAll.includes(el);\r\n  }\r\n\r\n  getIsCheckedLevel(el: any) {\r\n    return this.libraryFile && this.libraryFile.levels ? this.libraryFile.levels.includes(el) : this.tagsToAll.includes(el);\r\n  }\r\n\r\n  onTagsSubmitted() {\r\n    this.tagsSubmitted.emit();\r\n  }\r\n\r\n}\r\n", "<div class=\"library-upload-details\">\r\n    <div class=\"library-upload-details-right\">\r\n        <div class=\"library-upload-checkboxes \">\r\n            <div class=\"flex w-full\">\r\n            <div class=\"w-5rem text-right mr-2\">\r\n                <div class=\"font-xs text-primary mr-2\">\r\n                    Level:\r\n                </div>\r\n            </div>\r\n\r\n    <div class=\"w-full flex flex-wrap gap-1 align-items-center justify-content-start\">\r\n            <div class=\"library-upload-checkbox\">\r\n                \r\n                <p-checkbox name=\"groupname\" styleClass=\"primary-blue\" labelStyleClass=\"primary-blue\" label=\"A1\" [ngModel]=\"getIsCheckedLevel('A1')\" \r\n                [binary]=\"true\" (onChange)=\"onChooseTag($event, 'A1', true, false)\"></p-checkbox>\r\n             \r\n            </div>\r\n            <div class=\"library-upload-checkbox\">\r\n                <p-checkbox name=\"groupname\" styleClass=\"primary-blue\" labelStyleClass=\"primary-blue\" label=\"A2\" [ngModel]=\"getIsCheckedLevel('A2')\" \r\n                [binary]=\"true\" (onChange)=\"onChooseTag($event, 'A2', true, false)\"></p-checkbox>\r\n            </div>\r\n            <div class=\"library-upload-checkbox\">\r\n                <p-checkbox name=\"groupname\" styleClass=\"primary-blue\" labelStyleClass=\"primary-blue\" label=\"B1\" [ngModel]=\"getIsCheckedLevel('B1')\" \r\n                [binary]=\"true\" (onChange)=\"onChooseTag($event, 'B1', true, false)\"></p-checkbox>\r\n            </div>\r\n            <div class=\"library-upload-checkbox\">\r\n                <p-checkbox name=\"groupname\" styleClass=\"primary-blue\" labelStyleClass=\"primary-blue\" label=\"B2\" [ngModel]=\"getIsCheckedLevel('B2')\" \r\n                [binary]=\"true\" (onChange)=\"onChooseTag($event, 'B2', true, false)\"></p-checkbox>\r\n            </div>\r\n            <div class=\"library-upload-checkbox\">\r\n                <p-checkbox name=\"groupname\" styleClass=\"primary-blue\" labelStyleClass=\"primary-blue\" label=\"C1\" [ngModel]=\"getIsCheckedLevel('C1')\" \r\n                [binary]=\"true\" (onChange)=\"onChooseTag($event, 'C1', true, false)\"></p-checkbox>\r\n            </div>\r\n            <div class=\"library-upload-checkbox\">\r\n                <p-checkbox name=\"groupname\" styleClass=\"primary-blue\" labelStyleClass=\"primary-blue\" label=\"C2\" [ngModel]=\"getIsCheckedLevel('C2')\" \r\n                [binary]=\"true\" (onChange)=\"onChooseTag($event, 'C2', true, false)\"></p-checkbox>\r\n            </div>\r\n        </div>\r\n\r\n    </div>\r\n\r\n    </div>\r\n    <!-- ends library-upload-checkboxes -->\r\n        <div class=\"library-upload-checkboxes\">\r\n            \r\n            <div class=\"flex w-full\">\r\n            <div class=\"w-5rem text-right mr-2\">\r\n                <div class=\"font-xs text-primary\">\r\n                    Categories:\r\n                </div>\r\n            </div> \r\n\r\n            <div class=\"w-full flex flex-wrap align-items-center justify-content-start\">\r\n            <div class=\"library-upload-checkbox\">\r\n                <p-checkbox name=\"groupname\" styleClass=\"primary-blue\" labelStyleClass=\"primary-blue\" label=\"Reading\" [ngModel]=\"getIsCheckedCategory('Reading')\" \r\n                [binary]=\"true\" (onChange)=\"onChooseTag($event, 'Reading', false, true)\"></p-checkbox>\r\n            </div>\r\n            <div class=\"library-upload-checkbox\">\r\n                <p-checkbox name=\"groupname\" styleClass=\"primary-blue\" labelStyleClass=\"primary-blue\" label=\"Writing\" [ngModel]=\"getIsCheckedCategory('Writing')\" \r\n                [binary]=\"true\" (onChange)=\"onChooseTag($event, 'Writing', false, true)\"></p-checkbox>\r\n            </div>\r\n            <div class=\"library-upload-checkbox\">\r\n                <p-checkbox name=\"groupname\" styleClass=\"primary-blue\" labelStyleClass=\"primary-blue\" label=\"Listening\" [ngModel]=\"getIsCheckedCategory('Listening')\" \r\n                [binary]=\"true\" (onChange)=\"onChooseTag($event, 'Listening', false, true)\"></p-checkbox>\r\n            </div>\r\n            <div class=\"library-upload-checkbox\">\r\n                <p-checkbox name=\"groupname\" styleClass=\"primary-blue\" labelStyleClass=\"primary-blue\" label=\"Speaking\" [ngModel]=\"getIsCheckedCategory('Speaking')\" \r\n                [binary]=\"true\" (onChange)=\"onChooseTag($event, 'Speaking', false, true)\"></p-checkbox>\r\n            </div>\r\n            <div class=\"library-upload-checkbox\">\r\n                <p-checkbox name=\"groupname\" styleClass=\"primary-blue\" labelStyleClass=\"primary-blue\" label=\"Grammar\" [ngModel]=\"getIsCheckedCategory('Grammar')\" \r\n                [binary]=\"true\" (onChange)=\"onChooseTag($event, 'Grammar', false, true)\"></p-checkbox>\r\n            </div>\r\n            <div class=\"library-upload-checkbox\">\r\n                <p-checkbox name=\"groupname\" styleClass=\"primary-blue\" labelStyleClass=\"primary-blue\" label=\"Vocabulary\" [ngModel]=\"getIsCheckedCategory('Vocabulary')\" \r\n                [binary]=\"true\" (onChange)=\"onChooseTag($event, 'Vocabulary', false, true)\"></p-checkbox>\r\n            </div>\r\n            <div class=\"library-upload-checkbox\">\r\n                <p-checkbox name=\"groupname\" styleClass=\"primary-blue\" labelStyleClass=\"primary-blue\" label=\"Test\" [ngModel]=\"getIsCheckedCategory('Test')\" \r\n                [binary]=\"true\" (onChange)=\"onChooseTag($event, 'Test', false, true)\"></p-checkbox>\r\n            </div>\r\n            <div class=\"library-upload-checkbox\">\r\n                <p-checkbox name=\"groupname\" styleClass=\"primary-blue\" labelStyleClass=\"primary-blue\" label=\"Homework\" [ngModel]=\"getIsCheckedCategory('Homework')\" \r\n                [binary]=\"true\" (onChange)=\"onChooseTag($event, 'Homework', false, true)\"></p-checkbox>\r\n            </div>\r\n            <div class=\"library-upload-checkbox\">\r\n                <p-checkbox name=\"groupname\" styleClass=\"primary-blue\" labelStyleClass=\"primary-blue\" label=\"Units\" [ngModel]=\"getIsCheckedCategory('Units')\" \r\n                [binary]=\"true\" (onChange)=\"onChooseTag($event, 'Units', false, true)\"></p-checkbox>\r\n            </div>\r\n        </div>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;;;;;AAU9E,OAAM,MAAOC,mBAAmB;EAS9BC,YACUC,eAAgC;IAAhC,KAAAA,eAAe,GAAfA,eAAe;IAThB,KAAAC,UAAU,GAAY,KAAK;IAC3B,KAAAC,IAAI,GAAS,EAAU;IACvB,KAAAC,WAAW,GAAgB,EAAiB;IAC3C,KAAAC,SAAS,GAAG,IAAIP,YAAY,EAAE;IAC9B,KAAAQ,aAAa,GAAG,IAAIR,YAAY,EAAE;IAC5C,KAAAS,SAAS,GAAa,EAAE;EAKpB;EAEJC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACN,WAAW,CAAC;IAC7B,IAAI,CAACH,eAAe,CAACU,QAAQ,CAACC,SAAS,CAACC,GAAG,IAAG;MAC5C,IAAIA,GAAG,CAACC,GAAG,KAAK,EAAE,EAAE;QAClB,IAAID,GAAG,CAACE,GAAG,EAAE;UACX,IAAI,CAACR,SAAS,CAACS,IAAI,CAACH,GAAG,CAACC,GAAG,CAAC;QAC9B,CAAC,MAAM;UACL,IAAIG,SAAS,GAAG,IAAI,CAACV,SAAS,CAACW,SAAS,CAAGC,EAAO,IAAI;YACpD,OAAOA,EAAE,IAAIN,GAAG,CAACC,GAAG;UACtB,CAAE,CAAC;UACH,IAAI,CAACP,SAAS,CAACa,MAAM,CAACH,SAAS,EAAE,CAAC,CAAC;QACrC;MACF;IACF,CAAC,CAAC;EACJ;EAEAI,WAAWA,CAAA;IACT,IAAI,CAACpB,eAAe,CAACqB,WAAW,CAAC;MAAER,GAAG,EAAE,EAAE;MAAEC,GAAG,EAAE;IAAK,CAAE,CAAC;EAC3D;EAEAQ,WAAWA,CAACC,KAAU,EAAEV,GAAW,EAAEW,OAAgB,EAAEC,UAAmB;IACxE,IAAI,CAACrB,SAAS,CAACsB,IAAI,CAAC;MAClBC,OAAO,EAAEJ,KAAK,CAACI,OAAO;MACtBd,GAAG,EAAEA,GAAG;MACRW,OAAO,EAAEA,OAAO;MAChBC,UAAU,EAAEA,UAAU;MACtBxB,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BC,IAAI,EAAE,IAAI,CAACA;KACZ,CAAC;IACF,IAAI,IAAI,CAACD,UAAU,EAAE;MACnB,IAAI,CAACD,eAAe,CAACqB,WAAW,CAAC;QAAER,GAAG,EAAEA,GAAG;QAAEC,GAAG,EAAES,KAAK,CAACI;MAAO,CAAE,CAAC;IACpE;EACF;EAEAC,YAAYA,CAACV,EAAU;IACrB,OAAO,IAAI,CAACZ,SAAS,CAACuB,QAAQ,CAACX,EAAE,CAAC;EACpC;EAEAY,oBAAoBA,CAACZ,EAAO;IAC1B,OAAO,IAAI,CAACf,WAAW,IAAI,IAAI,CAACA,WAAW,CAAC4B,UAAU,GAAG,IAAI,CAAC5B,WAAW,CAAC4B,UAAU,CAACF,QAAQ,CAACX,EAAE,CAAC,GAAG,IAAI,CAACZ,SAAS,CAACuB,QAAQ,CAACX,EAAE,CAAC;EACjI;EAEAc,iBAAiBA,CAACd,EAAO;IACvB,OAAO,IAAI,CAACf,WAAW,IAAI,IAAI,CAACA,WAAW,CAAC8B,MAAM,GAAG,IAAI,CAAC9B,WAAW,CAAC8B,MAAM,CAACJ,QAAQ,CAACX,EAAE,CAAC,GAAG,IAAI,CAACZ,SAAS,CAACuB,QAAQ,CAACX,EAAE,CAAC;EACzH;EAEAgB,eAAeA,CAAA;IACb,IAAI,CAAC7B,aAAa,CAACqB,IAAI,EAAE;EAC3B;EAAC,QAAAS,CAAA,G;qBA7DUrC,mBAAmB,EAAAsC,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,eAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAnB1C,mBAAmB;IAAA2C,SAAA;IAAAC,MAAA;MAAAzC,UAAA;MAAAC,IAAA;MAAAC,WAAA;IAAA;IAAAwC,OAAA;MAAAvC,SAAA;MAAAC,aAAA;IAAA;IAAAuC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCLhBb,EALhB,CAAAe,cAAA,aAAoC,aACU,aACE,aACX,aACW,aACO;QACnCf,EAAA,CAAAgB,MAAA,eACJ;QACJhB,EADI,CAAAiB,YAAA,EAAM,EACJ;QAKFjB,EAHZ,CAAAe,cAAA,aAAkF,aACrC,oBAGmC;QAApDf,EAAA,CAAAkB,UAAA,sBAAAC,4DAAAC,MAAA;UAAA,OAAYN,GAAA,CAAA5B,WAAA,CAAAkC,MAAA,EAAoB,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;QAAA,EAAC;QAEvEpB,EAFwE,CAAAiB,YAAA,EAAa,EAE/E;QAEFjB,EADJ,CAAAe,cAAA,cAAqC,qBAEmC;QAApDf,EAAA,CAAAkB,UAAA,sBAAAG,6DAAAD,MAAA;UAAA,OAAYN,GAAA,CAAA5B,WAAA,CAAAkC,MAAA,EAAoB,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;QAAA,EAAC;QACvEpB,EADwE,CAAAiB,YAAA,EAAa,EAC/E;QAEFjB,EADJ,CAAAe,cAAA,cAAqC,sBAEmC;QAApDf,EAAA,CAAAkB,UAAA,sBAAAI,6DAAAF,MAAA;UAAA,OAAYN,GAAA,CAAA5B,WAAA,CAAAkC,MAAA,EAAoB,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;QAAA,EAAC;QACvEpB,EADwE,CAAAiB,YAAA,EAAa,EAC/E;QAEFjB,EADJ,CAAAe,cAAA,cAAqC,sBAEmC;QAApDf,EAAA,CAAAkB,UAAA,sBAAAK,6DAAAH,MAAA;UAAA,OAAYN,GAAA,CAAA5B,WAAA,CAAAkC,MAAA,EAAoB,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;QAAA,EAAC;QACvEpB,EADwE,CAAAiB,YAAA,EAAa,EAC/E;QAEFjB,EADJ,CAAAe,cAAA,cAAqC,sBAEmC;QAApDf,EAAA,CAAAkB,UAAA,sBAAAM,6DAAAJ,MAAA;UAAA,OAAYN,GAAA,CAAA5B,WAAA,CAAAkC,MAAA,EAAoB,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;QAAA,EAAC;QACvEpB,EADwE,CAAAiB,YAAA,EAAa,EAC/E;QAEFjB,EADJ,CAAAe,cAAA,cAAqC,sBAEmC;QAApDf,EAAA,CAAAkB,UAAA,sBAAAO,6DAAAL,MAAA;UAAA,OAAYN,GAAA,CAAA5B,WAAA,CAAAkC,MAAA,EAAoB,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;QAAA,EAAC;QAM/EpB,EANgF,CAAAiB,YAAA,EAAa,EAC/E,EACJ,EAEJ,EAEA;QAMMjB,EAJR,CAAAe,cAAA,cAAuC,cAEV,cACW,eACE;QAC9Bf,EAAA,CAAAgB,MAAA,qBACJ;QACJhB,EADI,CAAAiB,YAAA,EAAM,EACJ;QAIFjB,EAFJ,CAAAe,cAAA,eAA4E,cACvC,sBAEwC;QAAzDf,EAAA,CAAAkB,UAAA,sBAAAQ,6DAAAN,MAAA;UAAA,OAAYN,GAAA,CAAA5B,WAAA,CAAAkC,MAAA,EAAoB,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC;QAAA,EAAC;QAC5EpB,EAD6E,CAAAiB,YAAA,EAAa,EACpF;QAEFjB,EADJ,CAAAe,cAAA,cAAqC,sBAEwC;QAAzDf,EAAA,CAAAkB,UAAA,sBAAAS,6DAAAP,MAAA;UAAA,OAAYN,GAAA,CAAA5B,WAAA,CAAAkC,MAAA,EAAoB,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC;QAAA,EAAC;QAC5EpB,EAD6E,CAAAiB,YAAA,EAAa,EACpF;QAEFjB,EADJ,CAAAe,cAAA,cAAqC,sBAE0C;QAA3Df,EAAA,CAAAkB,UAAA,sBAAAU,6DAAAR,MAAA;UAAA,OAAYN,GAAA,CAAA5B,WAAA,CAAAkC,MAAA,EAAoB,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC;QAAA,EAAC;QAC9EpB,EAD+E,CAAAiB,YAAA,EAAa,EACtF;QAEFjB,EADJ,CAAAe,cAAA,cAAqC,sBAEyC;QAA1Df,EAAA,CAAAkB,UAAA,sBAAAW,6DAAAT,MAAA;UAAA,OAAYN,GAAA,CAAA5B,WAAA,CAAAkC,MAAA,EAAoB,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC;QAAA,EAAC;QAC7EpB,EAD8E,CAAAiB,YAAA,EAAa,EACrF;QAEFjB,EADJ,CAAAe,cAAA,cAAqC,sBAEwC;QAAzDf,EAAA,CAAAkB,UAAA,sBAAAY,6DAAAV,MAAA;UAAA,OAAYN,GAAA,CAAA5B,WAAA,CAAAkC,MAAA,EAAoB,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC;QAAA,EAAC;QAC5EpB,EAD6E,CAAAiB,YAAA,EAAa,EACpF;QAEFjB,EADJ,CAAAe,cAAA,cAAqC,sBAE2C;QAA5Df,EAAA,CAAAkB,UAAA,sBAAAa,6DAAAX,MAAA;UAAA,OAAYN,GAAA,CAAA5B,WAAA,CAAAkC,MAAA,EAAoB,YAAY,EAAE,KAAK,EAAE,IAAI,CAAC;QAAA,EAAC;QAC/EpB,EADgF,CAAAiB,YAAA,EAAa,EACvF;QAEFjB,EADJ,CAAAe,cAAA,cAAqC,sBAEqC;QAAtDf,EAAA,CAAAkB,UAAA,sBAAAc,6DAAAZ,MAAA;UAAA,OAAYN,GAAA,CAAA5B,WAAA,CAAAkC,MAAA,EAAoB,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC;QAAA,EAAC;QACzEpB,EAD0E,CAAAiB,YAAA,EAAa,EACjF;QAEFjB,EADJ,CAAAe,cAAA,cAAqC,sBAEyC;QAA1Df,EAAA,CAAAkB,UAAA,sBAAAe,6DAAAb,MAAA;UAAA,OAAYN,GAAA,CAAA5B,WAAA,CAAAkC,MAAA,EAAoB,UAAU,EAAE,KAAK,EAAE,IAAI,CAAC;QAAA,EAAC;QAC7EpB,EAD8E,CAAAiB,YAAA,EAAa,EACrF;QAEFjB,EADJ,CAAAe,cAAA,cAAqC,sBAEsC;QAAvDf,EAAA,CAAAkB,UAAA,sBAAAgB,6DAAAd,MAAA;UAAA,OAAYN,GAAA,CAAA5B,WAAA,CAAAkC,MAAA,EAAoB,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC;QAAA,EAAC;QAKtFpB,EALuF,CAAAiB,YAAA,EAAa,EAClF,EACJ,EACA,EACJ,EACJ,EA5F8B;;;QAa6EjB,EAAA,CAAAmC,SAAA,GAAmC;QACpInC,EADiG,CAAAoC,UAAA,YAAAtB,GAAA,CAAAlB,iBAAA,OAAmC,gBACrH;QAIkFI,EAAA,CAAAmC,SAAA,GAAmC;QACpInC,EADiG,CAAAoC,UAAA,YAAAtB,GAAA,CAAAlB,iBAAA,OAAmC,gBACrH;QAGkFI,EAAA,CAAAmC,SAAA,GAAmC;QACpInC,EADiG,CAAAoC,UAAA,YAAAtB,GAAA,CAAAlB,iBAAA,OAAmC,gBACrH;QAGkFI,EAAA,CAAAmC,SAAA,GAAmC;QACpInC,EADiG,CAAAoC,UAAA,YAAAtB,GAAA,CAAAlB,iBAAA,OAAmC,gBACrH;QAGkFI,EAAA,CAAAmC,SAAA,GAAmC;QACpInC,EADiG,CAAAoC,UAAA,YAAAtB,GAAA,CAAAlB,iBAAA,OAAmC,gBACrH;QAGkFI,EAAA,CAAAmC,SAAA,GAAmC;QACpInC,EADiG,CAAAoC,UAAA,YAAAtB,GAAA,CAAAlB,iBAAA,OAAmC,gBACrH;QAmBuFI,EAAA,CAAAmC,SAAA,GAA2C;QACjJnC,EADsG,CAAAoC,UAAA,YAAAtB,GAAA,CAAApB,oBAAA,YAA2C,gBAClI;QAGuFM,EAAA,CAAAmC,SAAA,GAA2C;QACjJnC,EADsG,CAAAoC,UAAA,YAAAtB,GAAA,CAAApB,oBAAA,YAA2C,gBAClI;QAGyFM,EAAA,CAAAmC,SAAA,GAA6C;QACrJnC,EADwG,CAAAoC,UAAA,YAAAtB,GAAA,CAAApB,oBAAA,cAA6C,gBACtI;QAGwFM,EAAA,CAAAmC,SAAA,GAA4C;QACnJnC,EADuG,CAAAoC,UAAA,YAAAtB,GAAA,CAAApB,oBAAA,aAA4C,gBACpI;QAGuFM,EAAA,CAAAmC,SAAA,GAA2C;QACjJnC,EADsG,CAAAoC,UAAA,YAAAtB,GAAA,CAAApB,oBAAA,YAA2C,gBAClI;QAG0FM,EAAA,CAAAmC,SAAA,GAA8C;QACvJnC,EADyG,CAAAoC,UAAA,YAAAtB,GAAA,CAAApB,oBAAA,eAA8C,gBACxI;QAGoFM,EAAA,CAAAmC,SAAA,GAAwC;QAC3InC,EADmG,CAAAoC,UAAA,YAAAtB,GAAA,CAAApB,oBAAA,SAAwC,gBAC5H;QAGwFM,EAAA,CAAAmC,SAAA,GAA4C;QACnJnC,EADuG,CAAAoC,UAAA,YAAAtB,GAAA,CAAApB,oBAAA,aAA4C,gBACpI;QAGqFM,EAAA,CAAAmC,SAAA,GAAyC;QAC7InC,EADoG,CAAAoC,UAAA,YAAAtB,GAAA,CAAApB,oBAAA,UAAyC,gBAC9H", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}