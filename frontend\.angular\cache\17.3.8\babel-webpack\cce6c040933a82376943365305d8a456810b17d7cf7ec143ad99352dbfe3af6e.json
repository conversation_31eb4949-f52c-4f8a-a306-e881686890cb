{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { StatsComponent } from './stats/stats.component';\nimport { ClassroomsComponent } from './classrooms/classrooms.component';\nimport { PackagesComponent } from './packages/packages.component';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport { ClassroomComponent } from './classroom.component';\nimport { NoteModule } from '../note/note.module';\nimport { NextLessonComponent } from './next-lesson/next-lesson.component';\nimport { TeacherBillingComponent } from './teacher-billing/teacher-billing.component';\nimport { PackagesProgressComponent } from './packages/packages-progress/packages-progress.component';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { HomeworkModule } from '../homework/homework.module';\nimport { LibraryModule } from '../library/library.module';\nimport { ClassComponent } from './classrooms/class/class.component';\nimport { ClassInfoComponent } from './classrooms/class/class-info/class-info.component';\nimport { ClassDetailsComponent } from './classrooms/class/class-details/class-details.component';\nimport { ClassStatsComponent } from './classrooms/class/class-stats/class-stats.component';\nimport { ClassReportsComponent } from './classrooms/class/class-reports/class-reports.component';\nimport { ReportComponent } from './classrooms/class/class-reports/report/report.component';\nimport { ClassroomRoutingModule } from './classroom-routing.module';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { ClassRatingsComponent } from './classrooms/class/class-ratings/class-ratings.component';\nimport { BuyPackageComponent } from './packages/buy-package/buy-package.component';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { TeacherRateComponent } from './classrooms/class/class-info/teacher-rate/teacher-rate.component';\nimport { TeacherRatesListComponent } from './classrooms/class/class-info/teacher-rates-list/teacher-rates-list.component';\nimport { ExtendPackageComponent } from './packages/extend-package/extend-package.component';\nimport { ClassroomRateBoxComponent } from './classroom-rate-box/classroom-rate-box.component';\nimport { TeacherProfileComponent } from './classrooms/class/class-info/components/teacher-profile/teacher-profile.component';\nimport { PackageAccordionItemComponent } from './packages/package-accordion-item/package-accordion-item.component';\nimport { PackageContentInfoComponent } from './packages/package-content-info/package-content-info.component';\nimport { CarouselModule } from 'primeng/carousel';\nimport { ClassInfoStudentAccordionItemComponent } from './classrooms/class/class-info/components/class-info-student-accordion-item/class-info-student-accordion-item.component';\nimport { ClassProgressComponent } from './classrooms/class/class-progress/class-progress.component';\nimport { ScrollPanelModule } from 'primeng/scrollpanel';\nimport { PackagesBlockPageComponent } from './packages/packages-block-page/packages-block-page.component';\nimport { NextLessonItemComponent } from './next-lesson/next-lesson-item/next-lesson-item.component';\nimport * as i0 from \"@angular/core\";\nexport class ClassroomModule {\n  static #_ = this.ɵfac = function ClassroomModule_Factory(t) {\n    return new (t || ClassroomModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ClassroomModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, ClassroomRoutingModule, SharedModule, NoteModule, FormsModule, HomeworkModule, LibraryModule, MatProgressSpinnerModule, FormsModule, ReactiveFormsModule, DropdownModule, MatTooltipModule, CarouselModule, ScrollPanelModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ClassroomModule, {\n    declarations: [ClassroomComponent, StatsComponent, ClassroomsComponent, PackagesComponent, ClassComponent, ClassInfoComponent, ClassDetailsComponent, ClassStatsComponent, NextLessonComponent, TeacherBillingComponent, PackagesProgressComponent, ClassReportsComponent, ReportComponent, ClassRatingsComponent, BuyPackageComponent, TeacherRateComponent, TeacherRatesListComponent, ExtendPackageComponent, ClassroomRateBoxComponent, TeacherProfileComponent, PackageAccordionItemComponent, PackageContentInfoComponent, ClassInfoStudentAccordionItemComponent, ClassProgressComponent, PackagesBlockPageComponent, NextLessonItemComponent],\n    imports: [CommonModule, ClassroomRoutingModule, SharedModule, NoteModule, FormsModule, HomeworkModule, LibraryModule, MatProgressSpinnerModule, FormsModule, ReactiveFormsModule, DropdownModule, MatTooltipModule, CarouselModule, ScrollPanelModule],\n    exports: [PackagesComponent, ClassroomsComponent, ClassroomComponent, NextLessonComponent, BuyPackageComponent, TeacherProfileComponent]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "StatsComponent", "ClassroomsComponent", "PackagesComponent", "SharedModule", "ClassroomComponent", "NoteModule", "NextLessonComponent", "TeacherBillingComponent", "PackagesProgressComponent", "FormsModule", "ReactiveFormsModule", "HomeworkModule", "LibraryModule", "ClassComponent", "ClassInfoComponent", "ClassDetailsComponent", "ClassStatsComponent", "ClassReportsComponent", "ReportComponent", "ClassroomRoutingModule", "MatProgressSpinnerModule", "ClassRatingsComponent", "BuyPackageComponent", "DropdownModule", "MatTooltipModule", "TeacherRateComponent", "TeacherRatesListComponent", "ExtendPackageComponent", "ClassroomRateBoxComponent", "TeacherProfileComponent", "PackageAccordionItemComponent", "PackageContentInfoComponent", "CarouselModule", "ClassInfoStudentAccordionItemComponent", "ClassProgressComponent", "ScrollPanelModule", "PackagesBlockPageComponent", "NextLessonItemComponent", "ClassroomModule", "_", "_2", "_3", "declarations", "imports", "exports"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\classroom.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { StatsComponent } from './stats/stats.component';\r\nimport { ClassroomsComponent } from './classrooms/classrooms.component';\r\nimport { PackagesComponent } from './packages/packages.component';\r\nimport { SharedModule } from 'src/app/shared/shared.module';\r\nimport { ClassroomComponent } from './classroom.component';\r\nimport { NoteModule } from '../note/note.module';\r\nimport { NextLessonComponent } from './next-lesson/next-lesson.component';\r\nimport { TeacherBillingComponent } from './teacher-billing/teacher-billing.component';\r\nimport { PackagesProgressComponent } from './packages/packages-progress/packages-progress.component';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { HomeworkModule } from '../homework/homework.module';\r\nimport { LibraryModule } from '../library/library.module';\r\nimport { ClassComponent } from './classrooms/class/class.component';\r\nimport { ClassInfoComponent } from './classrooms/class/class-info/class-info.component';\r\nimport { ClassDetailsComponent } from './classrooms/class/class-details/class-details.component';\r\nimport { ClassStatsComponent } from './classrooms/class/class-stats/class-stats.component';\r\nimport { ClassReportsComponent } from './classrooms/class/class-reports/class-reports.component';\r\nimport { ReportComponent } from './classrooms/class/class-reports/report/report.component';\r\nimport { ClassroomRoutingModule } from './classroom-routing.module';\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { ClassRatingsComponent } from './classrooms/class/class-ratings/class-ratings.component';\r\nimport { BuyPackageComponent } from './packages/buy-package/buy-package.component';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport {MatTooltipModule} from '@angular/material/tooltip';\r\nimport { TeacherRateComponent } from './classrooms/class/class-info/teacher-rate/teacher-rate.component';\r\nimport { TeacherRatesListComponent } from './classrooms/class/class-info/teacher-rates-list/teacher-rates-list.component';\r\nimport { ExtendPackageComponent } from './packages/extend-package/extend-package.component';\r\nimport { SafePipe } from 'src/app/core/pipes/save.pipe';\r\nimport { ClassroomRateBoxComponent } from './classroom-rate-box/classroom-rate-box.component';\r\nimport { TeacherProfileComponent } from './classrooms/class/class-info/components/teacher-profile/teacher-profile.component';\r\nimport { PackageAccordionItemComponent } from './packages/package-accordion-item/package-accordion-item.component';\r\nimport { PackageContentInfoComponent } from './packages/package-content-info/package-content-info.component';\r\nimport { CarouselModule } from 'primeng/carousel';\r\nimport { ClassInfoStudentAccordionItemComponent } from './classrooms/class/class-info/components/class-info-student-accordion-item/class-info-student-accordion-item.component';\r\nimport { ClassProgressComponent } from './classrooms/class/class-progress/class-progress.component';\r\nimport { ScrollPanelModule } from 'primeng/scrollpanel';\r\nimport { PackagesBlockPageComponent } from './packages/packages-block-page/packages-block-page.component';\r\nimport { NextLessonItemComponent } from './next-lesson/next-lesson-item/next-lesson-item.component';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ClassroomComponent,\r\n    StatsComponent,\r\n    ClassroomsComponent,\r\n    PackagesComponent,\r\n    ClassComponent,\r\n    ClassInfoComponent,\r\n    ClassDetailsComponent,\r\n    ClassStatsComponent,\r\n    NextLessonComponent,\r\n    TeacherBillingComponent,\r\n    PackagesProgressComponent,\r\n    ClassReportsComponent,\r\n    ReportComponent,\r\n    ClassRatingsComponent,\r\n    BuyPackageComponent,\r\n    TeacherRateComponent,\r\n    TeacherRatesListComponent,\r\n    ExtendPackageComponent,\r\n    ClassroomRateBoxComponent,\r\n    TeacherProfileComponent,\r\n    PackageAccordionItemComponent,\r\n    PackageContentInfoComponent,\r\n    ClassInfoStudentAccordionItemComponent,\r\n    ClassProgressComponent,\r\n    PackagesBlockPageComponent,\r\n    NextLessonItemComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    ClassroomRoutingModule,\r\n    SharedModule,\r\n    NoteModule,\r\n    FormsModule,\r\n    HomeworkModule,\r\n    LibraryModule,\r\n    MatProgressSpinnerModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    DropdownModule,\r\n    MatTooltipModule,\r\n    CarouselModule,\r\n    ScrollPanelModule,\r\n  ],\r\n  exports: [\r\n    PackagesComponent,\r\n    ClassroomsComponent,\r\n    ClassroomComponent,\r\n    NextLessonComponent,\r\n    BuyPackageComponent,\r\n    TeacherProfileComponent,\r\n  ],\r\n  providers: []\r\n\r\n})\r\nexport class ClassroomModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,mCAAmC;AACvE,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,uBAAuB,QAAQ,6CAA6C;AACrF,SAASC,yBAAyB,QAAQ,0DAA0D;AACpG,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,cAAc,QAAQ,6BAA6B;AAC5D,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,kBAAkB,QAAQ,oDAAoD;AACvF,SAASC,qBAAqB,QAAQ,0DAA0D;AAChG,SAASC,mBAAmB,QAAQ,sDAAsD;AAC1F,SAASC,qBAAqB,QAAQ,0DAA0D;AAChG,SAASC,eAAe,QAAQ,0DAA0D;AAC1F,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,qBAAqB,QAAQ,0DAA0D;AAChG,SAASC,mBAAmB,QAAQ,8CAA8C;AAClF,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAAQC,gBAAgB,QAAO,2BAA2B;AAC1D,SAASC,oBAAoB,QAAQ,mEAAmE;AACxG,SAASC,yBAAyB,QAAQ,+EAA+E;AACzH,SAASC,sBAAsB,QAAQ,oDAAoD;AAE3F,SAASC,yBAAyB,QAAQ,mDAAmD;AAC7F,SAASC,uBAAuB,QAAQ,oFAAoF;AAC5H,SAASC,6BAA6B,QAAQ,oEAAoE;AAClH,SAASC,2BAA2B,QAAQ,gEAAgE;AAC5G,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,sCAAsC,QAAQ,wHAAwH;AAC/K,SAASC,sBAAsB,QAAQ,4DAA4D;AACnG,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,0BAA0B,QAAQ,8DAA8D;AACzG,SAASC,uBAAuB,QAAQ,2DAA2D;;AA0DnG,OAAM,MAAOC,eAAe;EAAA,QAAAC,CAAA,G;qBAAfD,eAAe;EAAA;EAAA,QAAAE,EAAA,G;UAAfF;EAAe;EAAA,QAAAG,EAAA,G;cA1BxB1C,YAAY,EACZoB,sBAAsB,EACtBhB,YAAY,EACZE,UAAU,EACVI,WAAW,EACXE,cAAc,EACdC,aAAa,EACbQ,wBAAwB,EACxBX,WAAW,EACXC,mBAAmB,EACnBa,cAAc,EACdC,gBAAgB,EAChBQ,cAAc,EACdG,iBAAiB;EAAA;;;2EAaRG,eAAe;IAAAI,YAAA,GAtDxBtC,kBAAkB,EAClBJ,cAAc,EACdC,mBAAmB,EACnBC,iBAAiB,EACjBW,cAAc,EACdC,kBAAkB,EAClBC,qBAAqB,EACrBC,mBAAmB,EACnBV,mBAAmB,EACnBC,uBAAuB,EACvBC,yBAAyB,EACzBS,qBAAqB,EACrBC,eAAe,EACfG,qBAAqB,EACrBC,mBAAmB,EACnBG,oBAAoB,EACpBC,yBAAyB,EACzBC,sBAAsB,EACtBC,yBAAyB,EACzBC,uBAAuB,EACvBC,6BAA6B,EAC7BC,2BAA2B,EAC3BE,sCAAsC,EACtCC,sBAAsB,EACtBE,0BAA0B,EAC1BC,uBAAuB;IAAAM,OAAA,GAGvB5C,YAAY,EACZoB,sBAAsB,EACtBhB,YAAY,EACZE,UAAU,EACVI,WAAW,EACXE,cAAc,EACdC,aAAa,EACbQ,wBAAwB,EACxBX,WAAW,EACXC,mBAAmB,EACnBa,cAAc,EACdC,gBAAgB,EAChBQ,cAAc,EACdG,iBAAiB;IAAAS,OAAA,GAGjB1C,iBAAiB,EACjBD,mBAAmB,EACnBG,kBAAkB,EAClBE,mBAAmB,EACnBgB,mBAAmB,EACnBO,uBAAuB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}