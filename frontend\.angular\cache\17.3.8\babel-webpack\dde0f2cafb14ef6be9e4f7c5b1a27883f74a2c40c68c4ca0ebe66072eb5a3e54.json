{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"primeng/inputswitch\";\nfunction NotificationAlertsSettingsComponent_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h4\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 14)(4, \"label\", 15);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"p-inputSwitch\", 16);\n    i0.ɵɵelementStart(7, \"label\", 17);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const emailNotificationItem_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(emailNotificationItem_r1.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(emailNotificationItem_r1.labels[0]);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(emailNotificationItem_r1.labels[1]);\n  }\n}\nfunction NotificationAlertsSettingsComponent_ng_container_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"h4\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 14)(4, \"label\", 15);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"p-inputSwitch\", 16);\n    i0.ɵɵelementStart(7, \"label\", 17);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const emailNotificationItem_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(emailNotificationItem_r2.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(emailNotificationItem_r2.labels[0]);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(emailNotificationItem_r2.labels[1]);\n  }\n}\nexport let NotificationAlertsSettingsComponent = /*#__PURE__*/(() => {\n  class NotificationAlertsSettingsComponent {\n    constructor() {\n      this.emailNotificationSettings = [{\n        id: 'setting1',\n        title: 'Upcoming Lessons',\n        labels: ['No', 'Yes']\n      }, {\n        id: 'setting2',\n        title: 'Lessons Status',\n        labels: ['No', 'Yes']\n      }, {\n        id: 'setting3',\n        title: 'Shared Files',\n        labels: ['No', 'Yes']\n      }, {\n        id: 'setting4',\n        title: 'Shared Notes',\n        labels: ['No', 'Yes']\n      }, {\n        id: 'setting5',\n        title: 'Shared Homework',\n        labels: ['No', 'Yes']\n      }, {\n        id: 'setting6',\n        title: 'Ratings',\n        labels: ['No', 'Yes']\n      }];\n      this.platformNotificationSettings = [{\n        id: 'setting1',\n        title: 'Upcoming Lessons',\n        labels: ['No', 'Yes']\n      }, {\n        id: 'setting2',\n        title: 'Lessons Status',\n        labels: ['No', 'Yes']\n      }, {\n        id: 'setting3',\n        title: 'Shared Files',\n        labels: ['No', 'Yes']\n      }, {\n        id: 'setting4',\n        title: 'Shared Notes',\n        labels: ['No', 'Yes']\n      }, {\n        id: 'setting5',\n        title: 'Shared Homework',\n        labels: ['No', 'Yes']\n      }, {\n        id: 'setting6',\n        title: 'Ratings',\n        labels: ['No', 'Yes']\n      }];\n    }\n    ngOnInit() {}\n    static #_ = this.ɵfac = function NotificationAlertsSettingsComponent_Factory(t) {\n      return new (t || NotificationAlertsSettingsComponent)();\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NotificationAlertsSettingsComponent,\n      selectors: [[\"app-notification-alerts-settings\"]],\n      decls: 22,\n      vars: 2,\n      consts: [[1, \"p-2\", \"pt-4\"], [1, \"text-primary\", \"font-semibold\"], [1, \"grid\"], [1, \"col\", 2, \"border-right\", \"1px solid rgba(46, 61, 144, 0.54)\"], [1, \"\"], [1, \"text-black\", \"mr-5\"], [1, \"email\"], [1, \"flex\", \"flex-column\", \"align-items-start\", \"justify-content-start\"], [4, \"ngFor\", \"ngForOf\"], [1, \"col\", \"col-offset-1\"], [1, \"pl-2\"], [1, \"text-black\"], [1, \"platform\"], [1, \"mt-0\", \"mb-1\", \"input-title\", \"font-base\", \"font-semibold\"], [1, \"field-checkbox\", \"mt-0\", \"p-2\", \"input-border\", \"mb-3\"], [1, \"lesson-filter-label\", \"font-xs\", \"mr-1\"], [1, \"flex\", \"small-input-switch\", \"p-mr-2\", \"p-ml-2\"], [1, \"lesson-filter-label\", \"font-xs\"]],\n      template: function NotificationAlertsSettingsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h3\", 1);\n          i0.ɵɵtext(2, \"Notification Alerts\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 2)(4, \"div\", 3)(5, \"div\", 4)(6, \"h4\", 5);\n          i0.ɵɵtext(7, \" I want to receive \");\n          i0.ɵɵelementStart(8, \"span\", 6);\n          i0.ɵɵtext(9, \"email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(10, \" notifications for... \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"div\", 7);\n          i0.ɵɵtemplate(12, NotificationAlertsSettingsComponent_ng_container_12_Template, 9, 3, \"ng-container\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 9)(14, \"div\", 10)(15, \"h4\", 11);\n          i0.ɵɵtext(16, \" I want to receive \");\n          i0.ɵɵelementStart(17, \"span\", 12);\n          i0.ɵɵtext(18, \"platform\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(19, \" notifications for... \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 7);\n          i0.ɵɵtemplate(21, NotificationAlertsSettingsComponent_ng_container_21_Template, 9, 3, \"ng-container\", 8);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngForOf\", ctx.emailNotificationSettings);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.platformNotificationSettings);\n        }\n      },\n      dependencies: [i1.NgForOf, i2.InputSwitch],\n      styles: [\"[_nghost-%COMP%]     .small-input-switch .p-inputswitch{height:1rem;width:2.4rem}[_nghost-%COMP%]     .small-input-switch .p-inputswitch .p-inputswitch-slider:before{width:.8rem;height:.8rem;margin-top:-.45rem}span.email[_ngcontent-%COMP%]{text-decoration:underline;text-decoration-color:#3245b7;text-decoration-thickness:.2em}span.platform[_ngcontent-%COMP%]{text-decoration:underline;text-decoration-color:#2fb9d3;text-decoration-thickness:.2em}.main-title[_ngcontent-%COMP%]{color:#000}.input-title[_ngcontent-%COMP%]{color:#2d2a4b}.input-border[_ngcontent-%COMP%]{background:transparent;border:.5px solid #2e3d90;border-radius:100px}\"]\n    });\n  }\n  return NotificationAlertsSettingsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}