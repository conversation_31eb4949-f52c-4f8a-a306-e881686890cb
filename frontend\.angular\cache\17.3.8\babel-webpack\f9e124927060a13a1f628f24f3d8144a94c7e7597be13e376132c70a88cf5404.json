{"ast": null, "code": "import * as moment from 'moment';\nimport { ClassroomType, Status } from 'src/app/core/models/classroom.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/core/services/classroom.service\";\nimport * as i3 from \"src/app/core/services/auth.service\";\nimport * as i4 from \"src/app/core/services/general.service\";\nconst _c0 = [\"requestLesson\"];\nexport class ClassroomsComponent {\n  constructor(router, classroomService, authService, generalService) {\n    this.router = router;\n    this.classroomService = classroomService;\n    this.authService = authService;\n    this.generalService = generalService;\n    this.filteredClassrooms = [];\n    this.inTrial = false;\n    this.role = \"Teacher\";\n    this.selectedClassroomToRequestLesson = {};\n    this.disableExtendButton = true;\n    this.showProg = false;\n    this.trialClassRooms = [];\n    this.showRequestLesson = false;\n  }\n  ngOnInit() {\n    console.log(this.filteredClassrooms);\n    this.role = this.authService.getUserRole();\n  }\n  ngAfterContentInit() {\n    setTimeout(() => {\n      this.showProg = true;\n    }, 200);\n  }\n  getExtendPackageDisable(classroom) {\n    if (moment(new Date(classroom.activePackage?.expiresOn).getTime()).add('2', 'w').toDate().getTime() > moment(new Date()).add('2', 'w').toDate().getTime()) {\n      return true;\n    }\n    return false;\n  }\n  navigateToClassroom(classroom) {\n    let isTrial = classroom.type === ClassroomType.TRIAL ? {\n      isTrial: true\n    } : {};\n    this.router.navigate(['/classrooms/lessons/' + classroom.id, isTrial]);\n  }\n  navigateToBuyPackage(classroom) {\n    this.router.navigate(['/buy-package/' + classroom.id]);\n  }\n  navigateToExtendPackage(classroom) {\n    this.router.navigate(['/extend-package/' + classroom.id]);\n  }\n  getClassroomTitle(classroom) {\n    return this.classroomService.getClassroomTitle(classroom);\n  }\n  getClassroomTitleBackgroundColor(status) {\n    if (status === Status.INACTIVE) {\n      return '#7D7D7D';\n    }\n    if (status === Status.TRIAL) {\n      return '#a4a2e6';\n    }\n    return '#2d2a4b';\n  }\n  getClassroomBorderColor(status) {\n    if (status === Status.INACTIVE) {\n      return '#7D7D7D';\n    }\n    if (status === Status.TRIAL) {\n      return '#a4a2e6';\n    }\n    return '#2d2a4b';\n  }\n  getClassroomStatusColor(status) {\n    if (status === Status.PENDING) {\n      return '#3f37c9';\n    }\n    if (status === Status.EXPIRED) {\n      return '#8E8BC9';\n    }\n    if (status === Status.ON_HOLD) {\n      return '#f27769';\n    }\n    if (status === Status.INACTIVE) {\n      return '#7D7D7D';\n    }\n    if (status === Status.TRIAL) {\n      return '#a4a2e6';\n    }\n    return '#2d2a4b';\n  }\n  getPercent(classroom) {\n    return 100 - classroom.activePackage?.hoursLeft * 100 / classroom.activePackage?.totalHours;\n  }\n  getSubtitle(classroom) {\n    return `${classroom.hoursLeft} hours left`;\n  }\n  getClassroomType(classroom) {\n    return this.classroomService.getClassroomType(classroom.users);\n  }\n  onShowRequestLesson(classroom) {\n    this.classroomService.classroomToArrangeLesson = classroom;\n    this.router.navigate(['/reschedule']);\n  }\n  static #_ = this.ɵfac = function ClassroomsComponent_Factory(t) {\n    return new (t || ClassroomsComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.ClassroomService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.GeneralService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ClassroomsComponent,\n    selectors: [[\"app-classrooms\"]],\n    viewQuery: function ClassroomsComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.requestLesson = _t.first);\n      }\n    },\n    inputs: {\n      filteredClassrooms: \"filteredClassrooms\",\n      inTrial: \"inTrial\"\n    },\n    decls: 0,\n    vars: 0,\n    template: function ClassroomsComponent_Template(rf, ctx) {},\n    styles: [\".classrooms-classrooms[_ngcontent-%COMP%] {\\n  margin-top: 30px;\\n}\\n\\n.col--1of4[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n}\\n\\n.classrooms-classrooms-content[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%] {\\n  justify-content: left;\\n}\\n.classrooms-classrooms-content[_ngcontent-%COMP%]   .teacher-classroom[_ngcontent-%COMP%] {\\n  width: 192px;\\n  height: 192px;\\n}\\n.classrooms-classrooms-content[_ngcontent-%COMP%]   .student-classroom[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.classrooms-classrooms-content[_ngcontent-%COMP%]   .classroom[_ngcontent-%COMP%] {\\n  margin: 15px 0;\\n  cursor: pointer;\\n  border-radius: 20px;\\n  border: 3px solid var(--main-color);\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  position: relative;\\n}\\n.classrooms-classrooms-content[_ngcontent-%COMP%]   .classroom[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  border-radius: 20px;\\n  background-color: var(--main-color);\\n  font-weight: bold;\\n  font-size: 20px;\\n  width: 100%;\\n  border: 3px solid var(--main-color);\\n  text-align: center;\\n  color: white;\\n  filter: drop-shadow(0px 3px 6px rgba(0, 0, 0, 0.16));\\n  margin-top: -1px;\\n  height: 42px;\\n  line-height: 42px;\\n}\\n.classrooms-classrooms-content[_ngcontent-%COMP%]   .classroom[_ngcontent-%COMP%]   .classroom-level[_ngcontent-%COMP%], .classrooms-classrooms-content[_ngcontent-%COMP%]   .classroom[_ngcontent-%COMP%]   .hours[_ngcontent-%COMP%], .classrooms-classrooms-content[_ngcontent-%COMP%]   .classroom[_ngcontent-%COMP%]   .classroom-status[_ngcontent-%COMP%] {\\n  width: 80%;\\n  padding-bottom: 5px;\\n  margin: 8px;\\n  font-size: 15px;\\n}\\n.classrooms-classrooms-content[_ngcontent-%COMP%]   .classroom[_ngcontent-%COMP%]   .classroom-level[_ngcontent-%COMP%], .classrooms-classrooms-content[_ngcontent-%COMP%]   .classroom[_ngcontent-%COMP%]   .hours[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid var(--main-color);\\n}\\n.classrooms-classrooms-content[_ngcontent-%COMP%]   .classroom[_ngcontent-%COMP%]   .classroom-level[_ngcontent-%COMP%] {\\n  margin-top: 11px;\\n}\\n.classrooms-classrooms-content[_ngcontent-%COMP%]   .classroom[_ngcontent-%COMP%]   .classroom-status[_ngcontent-%COMP%] {\\n  margin-top: 5.5px;\\n  margin-bottom: 18px;\\n}\\n.classrooms-classrooms-content[_ngcontent-%COMP%]   .classroom[_ngcontent-%COMP%]   .students-count[_ngcontent-%COMP%] {\\n  width: 30px;\\n  height: 30px;\\n  line-height: 30px;\\n  border-radius: 50%;\\n  font-size: 15px;\\n  color: black;\\n  text-align: center;\\n  border: 3px solid var(--main-color);\\n  background: white;\\n  bottom: -18px;\\n  position: absolute;\\n}\\n@media screen and (max-width: 768px) {\\n  .classrooms-classrooms-content[_ngcontent-%COMP%]   .classroom[_ngcontent-%COMP%] {\\n    margin: 12px 5px;\\n  }\\n}\\n\\n.empty-classroom[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  flex-direction: column;\\n}\\n\\n.sections[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  width: 100%;\\n  display: flex;\\n  justify-content: space-evenly;\\n  flex-wrap: wrap;\\n}\\n.sections[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%] {\\n  width: 33%;\\n}\\n.sections[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  color: #aaaab3;\\n  width: 100%;\\n  text-align: center;\\n  padding: 15px 0;\\n}\\n.sections[_ngcontent-%COMP%]   .section[_ngcontent-%COMP%]   .section-details[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.progress[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n\\n.progress-details[_ngcontent-%COMP%], .package-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 100%;\\n}\\n.progress-details[_ngcontent-%COMP%]   .details-left[_ngcontent-%COMP%], .progress-details[_ngcontent-%COMP%]   .details-right[_ngcontent-%COMP%], .package-details[_ngcontent-%COMP%]   .details-left[_ngcontent-%COMP%], .package-details[_ngcontent-%COMP%]   .details-right[_ngcontent-%COMP%] {\\n  padding: 0 5px;\\n  width: 50%;\\n}\\n.progress-details[_ngcontent-%COMP%]   .details-left[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%], .progress-details[_ngcontent-%COMP%]   .details-right[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%], .package-details[_ngcontent-%COMP%]   .details-left[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%], .package-details[_ngcontent-%COMP%]   .details-right[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n  margin-bottom: 10px;\\n}\\n.progress-details[_ngcontent-%COMP%]   .details-left[_ngcontent-%COMP%], .package-details[_ngcontent-%COMP%]   .details-left[_ngcontent-%COMP%] {\\n  color: #707070;\\n}\\n.progress-details[_ngcontent-%COMP%]   .details-right[_ngcontent-%COMP%], .package-details[_ngcontent-%COMP%]   .details-right[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n}\\n\\n.package-details[_ngcontent-%COMP%]   .details-left[_ngcontent-%COMP%] {\\n  text-align: end;\\n}\\n\\n.package-btns[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  width: 30%;\\n  position: absolute;\\n  bottom: 10px;\\n}\\n.package-btns[_ngcontent-%COMP%]   .gray-button[_ngcontent-%COMP%], .package-btns[_ngcontent-%COMP%]   .main-color-button[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 12px;\\n  margin: 5px 0;\\n  width: 70%;\\n  border-radius: 24px;\\n}\\n.package-btns[_ngcontent-%COMP%]   .disabled-btn[_ngcontent-%COMP%] {\\n  width: 70%;\\n  font-size: 12px;\\n  margin: 5px 0;\\n  width: 70%;\\n  border-radius: 24px;\\n}\\n.package-btns[_ngcontent-%COMP%]   .light-purple-button[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 100%;\\n  border-radius: 24px;\\n  margin: 5px 0;\\n}\\n\\n.teacher[_ngcontent-%COMP%] {\\n  display: flex;\\n  width: 100%;\\n  align-items: center;\\n}\\n.teacher[_ngcontent-%COMP%]   .teacher-title[_ngcontent-%COMP%] {\\n  color: #707070;\\n}\\n.teacher[_ngcontent-%COMP%]   .teacher-name[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  margin-top: 8px;\\n}\\n\\n.lesson-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n}\\n\\n.lesson[_ngcontent-%COMP%] {\\n  margin: 13px;\\n  cursor: pointer;\\n  border-radius: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  width: 140px;\\n  height: 140px;\\n  box-sizing: border-box;\\n  z-index: 2;\\n  background: white;\\n  font-weight: bold;\\n}\\n.lesson[_ngcontent-%COMP%]   .copy-lesson[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 140px;\\n  height: 140px;\\n  border-radius: 20px;\\n  z-index: -1;\\n}\\n.lesson[_ngcontent-%COMP%]   .copy-lesson-2[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 130px;\\n  height: 130px;\\n  border-radius: 16px;\\n  z-index: -1;\\n  background-color: white;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["moment", "ClassroomType", "Status", "ClassroomsComponent", "constructor", "router", "classroomService", "authService", "generalService", "filteredClassrooms", "inTrial", "role", "selectedClassroomToRequestLesson", "disableExtend<PERSON><PERSON>on", "showProg", "trialClassRooms", "showRequestLesson", "ngOnInit", "console", "log", "getUserRole", "ngAfterContentInit", "setTimeout", "getExtendPackageDisable", "classroom", "Date", "activePackage", "expiresOn", "getTime", "add", "toDate", "navigateToClassroom", "isTrial", "type", "TRIAL", "navigate", "id", "navigateToBuyPackage", "navigateToExtendPackage", "getClassroomTitle", "getClassroomTitleBackgroundColor", "status", "INACTIVE", "getClassroomBorderColor", "getClassroomStatusColor", "PENDING", "EXPIRED", "ON_HOLD", "getPercent", "hoursLeft", "totalHours", "getSubtitle", "getClassroomType", "users", "onShowRequestLesson", "classroomToArrangeLesson", "_", "i0", "ɵɵdirectiveInject", "i1", "Router", "i2", "ClassroomService", "i3", "AuthService", "i4", "GeneralService", "_2", "selectors", "viewQuery", "ClassroomsComponent_Query", "rf", "ctx"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\classrooms\\classrooms.component.ts"], "sourcesContent": ["import { Component, Input, OnInit, ViewChild } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport * as moment from 'moment';\r\nimport { Classroom, ClassroomType, Status } from 'src/app/core/models/classroom.model';\r\nimport { Lesson } from 'src/app/core/models/lesson.model';\r\nimport { UserRole } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\n\r\n@Component({\r\n  selector: 'app-classrooms',\r\n  templateUrl: './classrooms.component.html',\r\n  styleUrls: ['./classrooms.component.scss']\r\n})\r\nexport class ClassroomsComponent implements OnInit {\r\n  @ViewChild('requestLesson') public requestLesson: any;\r\n  @Input() filteredClassrooms: Classroom[] = [];\r\n  @Input() inTrial: boolean = false;\r\n  public role: UserRole = \"Teacher\" as UserRole\r\n  selectedClassroomToRequestLesson: Classroom = {} as Classroom;\r\n  public disableExtendButton: boolean = true;\r\n  showProg: boolean = false;\r\n  trialClassRooms: Classroom[] = [];\r\n  constructor(\r\n    private router: Router,\r\n    private classroomService: ClassroomService,\r\n    private authService: AuthService,\r\n    private generalService: GeneralService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    console.log(this.filteredClassrooms)\r\n    this.role = this.authService.getUserRole();\r\n  }\r\n\r\n  ngAfterContentInit() {\r\n    setTimeout(() => {\r\n      this.showProg = true;\r\n    }, 200);\r\n  }\r\n\r\n  getExtendPackageDisable(classroom: Classroom) {\r\n    if (moment(new Date(classroom.activePackage?.expiresOn!).getTime()!).add('2', 'w').toDate().getTime() > moment(new Date()).add('2', 'w').toDate().getTime()) {\r\n      return true\r\n    }\r\n    return false;\r\n  }\r\n\r\n  navigateToClassroom(classroom: Classroom) {\r\n    let isTrial = classroom.type === ClassroomType.TRIAL ? { isTrial: true } : {}\r\n    this.router.navigate(['/classrooms/lessons/' + classroom.id, isTrial])\r\n  }\r\n\r\n  navigateToBuyPackage(classroom: Classroom) {\r\n    this.router.navigate(['/buy-package/' + classroom.id])\r\n  }\r\n\r\n  navigateToExtendPackage(classroom: Classroom) {\r\n    this.router.navigate(['/extend-package/' + classroom.id])\r\n  }\r\n\r\n  getClassroomTitle(classroom: Classroom) {\r\n    return this.classroomService.getClassroomTitle(classroom);\r\n  }\r\n\r\n  getClassroomTitleBackgroundColor(status: string): string {\r\n    if (status === Status.INACTIVE) {\r\n      return '#7D7D7D'\r\n    }\r\n    if (status === Status.TRIAL) {\r\n      return '#a4a2e6'\r\n    }\r\n    return '#2d2a4b'\r\n\r\n  }\r\n\r\n  getClassroomBorderColor(status: string): string {\r\n    if (status === Status.INACTIVE) {\r\n      return '#7D7D7D'\r\n    }\r\n    if (status === Status.TRIAL) {\r\n      return '#a4a2e6'\r\n    }\r\n    return '#2d2a4b'\r\n  }\r\n\r\n  getClassroomStatusColor(status: string): string {\r\n    if (status === Status.PENDING) {\r\n      return '#3f37c9';\r\n    }\r\n    if (status === Status.EXPIRED) {\r\n      return '#8E8BC9';\r\n    }\r\n    if (status === Status.ON_HOLD) {\r\n      return '#f27769';\r\n    }\r\n    if (status === Status.INACTIVE) {\r\n      return '#7D7D7D';\r\n    }\r\n    if (status === Status.TRIAL) {\r\n      return '#a4a2e6';\r\n    }\r\n    return '#2d2a4b';\r\n  }\r\n\r\n  getPercent(classroom: Classroom) {\r\n    return 100 - (classroom.activePackage?.hoursLeft! * 100) / classroom.activePackage?.totalHours!\r\n  }\r\n\r\n  getSubtitle(classroom: Classroom) {\r\n    return `${classroom.hoursLeft} hours left`\r\n  }\r\n\r\n  getClassroomType(classroom: Classroom) {\r\n    return this.classroomService.getClassroomType(classroom.users)\r\n  }\r\n  showRequestLesson: boolean = false;\r\n  onShowRequestLesson(classroom?: Classroom) {\r\n    this.classroomService.classroomToArrangeLesson = classroom!\r\n    this.router.navigate(['/reschedule'])\r\n  }\r\n\r\n}\r\n"], "mappings": "AAEA,OAAO,KAAKA,MAAM,MAAM,QAAQ;AAChC,SAAoBC,aAAa,EAAEC,MAAM,QAAQ,qCAAqC;;;;;;;AAYtF,OAAM,MAAOC,mBAAmB;EAS9BC,YACUC,MAAc,EACdC,gBAAkC,EAClCC,WAAwB,EACxBC,cAA8B;IAH9B,KAAAH,MAAM,GAANA,MAAM;IACN,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IAXf,KAAAC,kBAAkB,GAAgB,EAAE;IACpC,KAAAC,OAAO,GAAY,KAAK;IAC1B,KAAAC,IAAI,GAAa,SAAqB;IAC7C,KAAAC,gCAAgC,GAAc,EAAe;IACtD,KAAAC,mBAAmB,GAAY,IAAI;IAC1C,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,eAAe,GAAgB,EAAE;IA8FjC,KAAAC,iBAAiB,GAAY,KAAK;EAxF9B;EAEJC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACV,kBAAkB,CAAC;IACpC,IAAI,CAACE,IAAI,GAAG,IAAI,CAACJ,WAAW,CAACa,WAAW,EAAE;EAC5C;EAEAC,kBAAkBA,CAAA;IAChBC,UAAU,CAAC,MAAK;MACd,IAAI,CAACR,QAAQ,GAAG,IAAI;IACtB,CAAC,EAAE,GAAG,CAAC;EACT;EAEAS,uBAAuBA,CAACC,SAAoB;IAC1C,IAAIxB,MAAM,CAAC,IAAIyB,IAAI,CAACD,SAAS,CAACE,aAAa,EAAEC,SAAU,CAAC,CAACC,OAAO,EAAG,CAAC,CAACC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,MAAM,EAAE,CAACF,OAAO,EAAE,GAAG5B,MAAM,CAAC,IAAIyB,IAAI,EAAE,CAAC,CAACI,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,MAAM,EAAE,CAACF,OAAO,EAAE,EAAE;MAC3J,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAG,mBAAmBA,CAACP,SAAoB;IACtC,IAAIQ,OAAO,GAAGR,SAAS,CAACS,IAAI,KAAKhC,aAAa,CAACiC,KAAK,GAAG;MAAEF,OAAO,EAAE;IAAI,CAAE,GAAG,EAAE;IAC7E,IAAI,CAAC3B,MAAM,CAAC8B,QAAQ,CAAC,CAAC,sBAAsB,GAAGX,SAAS,CAACY,EAAE,EAAEJ,OAAO,CAAC,CAAC;EACxE;EAEAK,oBAAoBA,CAACb,SAAoB;IACvC,IAAI,CAACnB,MAAM,CAAC8B,QAAQ,CAAC,CAAC,eAAe,GAAGX,SAAS,CAACY,EAAE,CAAC,CAAC;EACxD;EAEAE,uBAAuBA,CAACd,SAAoB;IAC1C,IAAI,CAACnB,MAAM,CAAC8B,QAAQ,CAAC,CAAC,kBAAkB,GAAGX,SAAS,CAACY,EAAE,CAAC,CAAC;EAC3D;EAEAG,iBAAiBA,CAACf,SAAoB;IACpC,OAAO,IAAI,CAAClB,gBAAgB,CAACiC,iBAAiB,CAACf,SAAS,CAAC;EAC3D;EAEAgB,gCAAgCA,CAACC,MAAc;IAC7C,IAAIA,MAAM,KAAKvC,MAAM,CAACwC,QAAQ,EAAE;MAC9B,OAAO,SAAS;IAClB;IACA,IAAID,MAAM,KAAKvC,MAAM,CAACgC,KAAK,EAAE;MAC3B,OAAO,SAAS;IAClB;IACA,OAAO,SAAS;EAElB;EAEAS,uBAAuBA,CAACF,MAAc;IACpC,IAAIA,MAAM,KAAKvC,MAAM,CAACwC,QAAQ,EAAE;MAC9B,OAAO,SAAS;IAClB;IACA,IAAID,MAAM,KAAKvC,MAAM,CAACgC,KAAK,EAAE;MAC3B,OAAO,SAAS;IAClB;IACA,OAAO,SAAS;EAClB;EAEAU,uBAAuBA,CAACH,MAAc;IACpC,IAAIA,MAAM,KAAKvC,MAAM,CAAC2C,OAAO,EAAE;MAC7B,OAAO,SAAS;IAClB;IACA,IAAIJ,MAAM,KAAKvC,MAAM,CAAC4C,OAAO,EAAE;MAC7B,OAAO,SAAS;IAClB;IACA,IAAIL,MAAM,KAAKvC,MAAM,CAAC6C,OAAO,EAAE;MAC7B,OAAO,SAAS;IAClB;IACA,IAAIN,MAAM,KAAKvC,MAAM,CAACwC,QAAQ,EAAE;MAC9B,OAAO,SAAS;IAClB;IACA,IAAID,MAAM,KAAKvC,MAAM,CAACgC,KAAK,EAAE;MAC3B,OAAO,SAAS;IAClB;IACA,OAAO,SAAS;EAClB;EAEAc,UAAUA,CAACxB,SAAoB;IAC7B,OAAO,GAAG,GAAIA,SAAS,CAACE,aAAa,EAAEuB,SAAU,GAAG,GAAG,GAAIzB,SAAS,CAACE,aAAa,EAAEwB,UAAW;EACjG;EAEAC,WAAWA,CAAC3B,SAAoB;IAC9B,OAAO,GAAGA,SAAS,CAACyB,SAAS,aAAa;EAC5C;EAEAG,gBAAgBA,CAAC5B,SAAoB;IACnC,OAAO,IAAI,CAAClB,gBAAgB,CAAC8C,gBAAgB,CAAC5B,SAAS,CAAC6B,KAAK,CAAC;EAChE;EAEAC,mBAAmBA,CAAC9B,SAAqB;IACvC,IAAI,CAAClB,gBAAgB,CAACiD,wBAAwB,GAAG/B,SAAU;IAC3D,IAAI,CAACnB,MAAM,CAAC8B,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAAC,QAAAqB,CAAA,G;qBA1GUrD,mBAAmB,EAAAsD,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAnBhE,mBAAmB;IAAAiE,SAAA;IAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}