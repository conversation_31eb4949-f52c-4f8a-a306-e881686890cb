{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component } from '@angular/core';\nimport { BaseIcon } from 'primeng/baseicon';\nimport { UniqueComponentId } from 'primeng/utils';\nlet PlusIcon = /*#__PURE__*/(() => {\n  class PlusIcon extends BaseIcon {\n    pathId;\n    ngOnInit() {\n      this.pathId = 'url(#' + UniqueComponentId() + ')';\n    }\n    static ɵfac = /* @__PURE__ */(() => {\n      let ɵPlusIcon_BaseFactory;\n      return function PlusIcon_Factory(t) {\n        return (ɵPlusIcon_BaseFactory || (ɵPlusIcon_BaseFactory = i0.ɵɵgetInheritedFactory(PlusIcon)))(t || PlusIcon);\n      };\n    })();\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: PlusIcon,\n      selectors: [[\"PlusIcon\"]],\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 7,\n      consts: [[\"width\", \"14\", \"height\", \"14\", \"viewBox\", \"0 0 14 14\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M7.67742 6.32258V0.677419C7.67742 0.497757 7.60605 0.325452 7.47901 0.198411C7.35197 0.0713707 7.17966 0 7 0C6.82034 0 6.64803 0.0713707 6.52099 0.198411C6.39395 0.325452 6.32258 0.497757 6.32258 0.677419V6.32258H0.677419C0.497757 6.32258 0.325452 6.39395 0.198411 6.52099C0.0713707 6.64803 0 6.82034 0 7C0 7.17966 0.0713707 7.35197 0.198411 7.47901C0.325452 7.60605 0.497757 7.67742 0.677419 7.67742H6.32258V13.3226C6.32492 13.5015 6.39704 13.6725 6.52358 13.799C6.65012 13.9255 6.82106 13.9977 7 14C7.17966 14 7.35197 13.9286 7.47901 13.8016C7.60605 13.6745 7.67742 13.5022 7.67742 13.3226V7.67742H13.3226C13.5022 7.67742 13.6745 7.60605 13.8016 7.47901C13.9286 7.35197 14 7.17966 14 7C13.9977 6.82106 13.9255 6.65012 13.799 6.52358C13.6725 6.39704 13.5015 6.32492 13.3226 6.32258H7.67742Z\", \"fill\", \"currentColor\"], [3, \"id\"], [\"width\", \"14\", \"height\", \"14\", \"fill\", \"white\"]],\n      template: function PlusIcon_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(0, \"svg\", 0)(1, \"g\");\n          i0.ɵɵelement(2, \"path\", 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"defs\")(4, \"clipPath\", 2);\n          i0.ɵɵelement(5, \"rect\", 3);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.getClassNames());\n          i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel)(\"aria-hidden\", ctx.ariaHidden)(\"role\", ctx.role);\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"clip-path\", ctx.pathId);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"id\", ctx.pathId);\n        }\n      },\n      encapsulation: 2\n    });\n  }\n  return PlusIcon;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { PlusIcon };\n//# sourceMappingURL=primeng-icons-plus.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}