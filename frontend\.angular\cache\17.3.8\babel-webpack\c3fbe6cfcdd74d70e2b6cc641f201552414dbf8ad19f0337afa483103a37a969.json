{"ast": null, "code": "import { UntypedFormArray, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { SubSink } from 'subsink';\nimport { Observable } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nimport * as i2 from \"src/app/core/services/teacher-application.service\";\nimport * as i3 from \"src/app/core/services/toast.service\";\nimport * as i4 from \"src/app/core/services/auth.service\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@angular/forms\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"../../../shared/upload-files/upload-files.component\";\nimport * as i10 from \"primeng/button\";\nimport * as i11 from \"primeng/ripple\";\nconst _c0 = a0 => ({\n  \"more-height-div\": a0\n});\nfunction EducationComponent_ng_container_2_div_5_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function EducationComponent_ng_container_2_div_5_div_6_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.addEducation());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EducationComponent_ng_container_2_div_5_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function EducationComponent_ng_container_2_div_5_div_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const i_r4 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeEducation(i_r4));\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EducationComponent_ng_container_2_div_5_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtext(1, \"* Degrees / Diplomas is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EducationComponent_ng_container_2_div_5_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵtext(1, \"Institute is required \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction EducationComponent_ng_container_2_div_5_div_18_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32, 1)(3, \"div\", 33)(4, \"div\");\n    i0.ɵɵtext(5, \"Add files\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"img\", 34);\n    i0.ɵɵlistener(\"click\", function EducationComponent_ng_container_2_div_5_div_18_div_4_Template_img_click_6_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const i_r4 = i0.ɵɵnextContext(2).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.hideFileUpload(i_r4));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\");\n    i0.ɵɵtext(8, \" Upload Degree/Diploma \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"app-upload-files\", 35);\n    i0.ɵɵlistener(\"onUploadFinished\", function EducationComponent_ng_container_2_div_5_div_18_div_4_Template_app_upload_files_onUploadFinished_9_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const i_r4 = i0.ɵɵnextContext(2).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.uploadFinished($event, i_r4));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"dndUI\", true)(\"multiple\", false)(\"isTeacherApplication\", true)(\"postUrl\", ctx_r1.UPLOAD_TEACHER_APPLICATION_FILES_URL);\n  }\n}\nfunction EducationComponent_ng_container_2_div_5_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function EducationComponent_ng_container_2_div_5_div_18_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const i_r4 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openFileUploadDialog(i_r4));\n    });\n    i0.ɵɵtext(2, \"Upload File \");\n    i0.ɵɵelementStart(3, \"img\", 30);\n    i0.ɵɵlistener(\"click\", function EducationComponent_ng_container_2_div_5_div_18_Template_img_click_3_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const i_r4 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.hideFileUpload(i_r4));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(4, EducationComponent_ng_container_2_div_5_div_18_div_4_Template, 10, 4, \"div\", 31);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r4 = i0.ɵɵnextContext().index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.educationfilePaths(i_r4).controls.length === 0 && ctx_r1.showFileUpload[i_r4]);\n  }\n}\nfunction EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_2_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const i_r4 = i0.ɵɵnextContext(3).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openFileUploadDialog(i_r4));\n    });\n    i0.ɵɵtext(1, \"Upload File \");\n    i0.ɵɵelementStart(2, \"img\", 30);\n    i0.ɵɵlistener(\"click\", function EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_2_Template_img_click_2_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const i_r4 = i0.ɵɵnextContext(3).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.hideFileUpload(i_r4));\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵelement(1, \"img\", 40);\n    i0.ɵɵelementStart(2, \"span\", 41);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"img\", 42);\n    i0.ɵɵlistener(\"click\", function EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_3_Template_img_click_4_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const skillIndex_r9 = i0.ɵɵnextContext().index;\n      const i_r4 = i0.ɵɵnextContext(2).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeEducationFileField(i_r4, skillIndex_r9, ctx_r1.getFileName(ctx_r1.form.value.education[i_r4].fileUploads[skillIndex_r9].filePath)));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const skillIndex_r9 = i0.ɵɵnextContext().index;\n    const i_r4 = i0.ɵɵnextContext(2).index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getFileName(ctx_r1.form.value.education[i_r4].fileUploads[skillIndex_r9].filePath));\n  }\n}\nfunction EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32, 1)(3, \"div\", 33)(4, \"div\");\n    i0.ɵɵtext(5, \"Add files\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"img\", 34);\n    i0.ɵɵlistener(\"click\", function EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_4_Template_img_click_6_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const skillIndex_r9 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.hideFileUpload(skillIndex_r9));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\");\n    i0.ɵɵtext(8, \" Upload Degree/Diploma \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"app-upload-files\", 43);\n    i0.ɵɵlistener(\"onUploadFinished\", function EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_4_Template_app_upload_files_onUploadFinished_9_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const skillIndex_r9 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.uploadFinished($event, skillIndex_r9));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"dndUI\", true)(\"multiple\", false)(\"postUrl\", ctx_r1.UPLOAD_TEACHER_APPLICATION_FILES_URL);\n  }\n}\nfunction EducationComponent_ng_container_2_div_5_ng_template_19_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 15);\n    i0.ɵɵtemplate(2, EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_2_Template, 3, 0, \"div\", 37)(3, EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_3_Template, 5, 1, \"div\", 38)(4, EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_4_Template, 10, 3, \"div\", 31);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const skillIndex_r9 = ctx.index;\n    const i_r4 = i0.ɵɵnextContext(2).index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroupName\", skillIndex_r9);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.form.value.education[i_r4].fileUploads.length === 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.form.value.education[i_r4].fileUploads[skillIndex_r9].length !== 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showFileUpload[skillIndex_r9]);\n  }\n}\nfunction EducationComponent_ng_container_2_div_5_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, EducationComponent_ng_container_2_div_5_ng_template_19_div_0_Template, 5, 4, \"div\", 36);\n  }\n  if (rf & 2) {\n    const i_r4 = i0.ɵɵnextContext().index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.educationfilePaths(i_r4).controls);\n  }\n}\nfunction EducationComponent_ng_container_2_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵelementContainerStart(1, 15);\n    i0.ɵɵelementStart(2, \"div\", 16)(3, \"div\", 17)(4, \"div\", 18);\n    i0.ɵɵtext(5, \"Degrees / Diplomas * \");\n    i0.ɵɵtemplate(6, EducationComponent_ng_container_2_div_5_div_6_Template, 2, 0, \"div\", 19)(7, EducationComponent_ng_container_2_div_5_div_7_Template, 2, 0, \"div\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"input\", 20);\n    i0.ɵɵtemplate(9, EducationComponent_ng_container_2_div_5_div_9_Template, 2, 0, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 17)(11, \"div\", 18);\n    i0.ɵɵtext(12, \"Institute *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"input\", 22);\n    i0.ɵɵtemplate(14, EducationComponent_ng_container_2_div_5_div_14_Template, 2, 0, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 23)(16, \"div\", 18);\n    i0.ɵɵtext(17, \"Upload Degree/Diploma\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, EducationComponent_ng_container_2_div_5_div_18_Template, 5, 1, \"div\", 24)(19, EducationComponent_ng_container_2_div_5_ng_template_19_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r4 = ctx.index;\n    const tplwithFilePaths_r11 = i0.ɵɵreference(20);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroupName\", i_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c0, ctx_r1.educationfilePaths(i_r4).length > 0));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", i_r4 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r4 > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(i_r4));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(i_r4));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.educationfilePaths(i_r4).controls.length === 0)(\"ngIfElse\", tplwithFilePaths_r11);\n  }\n}\nfunction EducationComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"form\", 10)(2, \"div\", 11)(3, \"div\");\n    i0.ɵɵelementContainerStart(4, 12);\n    i0.ɵɵtemplate(5, EducationComponent_ng_container_2_div_5_Template, 21, 10, \"div\", 13);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.form);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.education.controls);\n  }\n}\nexport let EducationComponent = /*#__PURE__*/(() => {\n  class EducationComponent {\n    constructor(generalService, teacherService, toastService, authService, confirmationService, router, fb, location) {\n      this.generalService = generalService;\n      this.teacherService = teacherService;\n      this.toastService = toastService;\n      this.authService = authService;\n      this.confirmationService = confirmationService;\n      this.router = router;\n      this.fb = fb;\n      this.location = location;\n      this.subs = new SubSink();\n      this.form = new UntypedFormGroup({});\n      this.isTablet = false;\n      this.teacher = this.teacherService.dummyTeacher;\n      this.tryToSave = false;\n      this.isLoading = false;\n      this.speakLanguageCounter = 0;\n      this.error = false;\n      this.UPLOAD_TEACHER_APPLICATION_FILES_URL = '/LMS/UploadTeacherApplicationFiles';\n      this.teacherEducation = [];\n      this.showFileUpload = [];\n      this.formChanged = false;\n    }\n    ngOnInit() {\n      this.teacherService.setCurrentStepIndex(1);\n      this.subs.add(this.teacherService.submitOnMenuClickListener.subscribe(res => {\n        if (res) {\n          this.onSubmit();\n        }\n      }));\n      this.subs.add(this.teacherService.getTeacherApplicationStep2().subscribe(res => {\n        if (res) {\n          this.updateStepFormValues(res);\n        }\n        if (res.education === null) {\n          this.addEducation({});\n        }\n        this.initFormChangedListener();\n      }));\n      this.subs.add(this.generalService.deviceKind.subscribe(res => {\n        this.isTablet = res.is1024;\n      }));\n      this.form = new UntypedFormGroup({\n        education: new UntypedFormArray([])\n      });\n      this.teacherEducation = this.teacher.education;\n      this.teacherEducation.forEach(element => {\n        this.showFileUpload.push(false);\n        this.education.push(this.fb.group(element));\n      });\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n      this.teacherService.setSubmitOnMenuClickListener(false);\n    }\n    get education() {\n      return this.form.get('education');\n    }\n    /**\n    Update the form values for step 2 of the teacher application\n    @param {TeacherApplicationStep2} teacherApplicationStep2 - The data for step 2 of the teacher application\n    */\n    updateStepFormValues(teacherApplicationStep2) {\n      if (!this.generalService.isNullishObject(teacherApplicationStep2.education)) {\n        teacherApplicationStep2.education.forEach((element, index) => {\n          this.addEducation(element);\n          element.fileUploads.forEach(fileUploadElement => {\n            this.addEducationFileField(index, fileUploadElement.filePath);\n          });\n        });\n        console.log(teacherApplicationStep2.education);\n      }\n    }\n    /**\n    Add an education to the form\n    @param {TeacherApplicationEducation} element - The education to add to the form\n    */\n    addEducation(element) {\n      const group = new UntypedFormGroup({\n        name: new UntypedFormControl(element ? element.name : '', Validators.required),\n        institute: new UntypedFormControl(element ? element.institute : '', Validators.required),\n        fileUploads: this.fb.array([])\n      });\n      this.education.push(group);\n    }\n    /**\n    Remove an education from the form\n    @param {number} index - The index of the education to remove\n    */\n    removeEducation(index) {\n      this.education.removeAt(index);\n    }\n    initFormChangedListener() {\n      this.subs.add(this.form.valueChanges.subscribe(val => {\n        console.log(val);\n        this.formChanged = true;\n      }));\n    }\n    /**\n    Submit the form data\n    */\n    onSubmit() {\n      this.tryToSave = true;\n      if (!this.form.valid) {\n        this.error = true;\n        return;\n      }\n      this.formChanged = false;\n      this.subs.add(this.teacherService.updateTeacherApplicationStep2(this.form.value).subscribe(res => {\n        if (res) {\n          this.toastService.setShowToastmessage({\n            severity: 'success',\n            summary: '',\n            detail: 'Your Education info were updated.'\n          });\n        }\n        this.router.navigateByUrl('/teacher/experience', {\n          replaceUrl: true\n        });\n      }));\n    }\n    back() {\n      this.location.back();\n    }\n    ifFieldValid(i) {\n      this.teacherService.setStepValid(1, this.form, 'teacher-education-route');\n      return this.tryToSave && this.form.get('education').controls[i].invalid;\n    }\n    goBack() {\n      this.router.navigateByUrl('/teacher/info', {\n        replaceUrl: true\n      });\n    }\n    /**\n    * Handles the completion of a file upload\n    *\n    * @param {filePath: { dbPath: string; }} event - Event object containing information about the uploaded file\n    * @param {number} i - Index of the file in the array of files\n    *\n    */\n    uploadFinished(event, i) {\n      this.hideFileUpload(i);\n      this.addEducationFileField(i, event.filePath.dbPath);\n    }\n    /**\n     * Opens the file upload dialog\n     *\n     * @param {number} fileIndex - Index of the file in the array of files\n     *\n     */\n    openFileUploadDialog(fileIndex) {\n      this.showFileUpload[fileIndex] = true;\n      document.getElementById('blur_bg')?.classList.add('yes-visibility');\n    }\n    /**\n    Hides the file upload for a specific index.\n    @param {number} fileIndex - The index of the file upload to be hidden.\n    */\n    hideFileUpload(fileIndex) {\n      this.showFileUpload[fileIndex] = false;\n      document.getElementById('blur_bg')?.classList.remove('yes-visibility');\n    }\n    /**\n    Get the file name from a file path\n    @param {string} fileName - The file path\n    @returns {string} The file name\n    */\n    getFileName(fileName) {\n      return this.generalService.getFileName2(fileName);\n    }\n    removeEmployee(empIndex) {\n      this.education.removeAt(empIndex);\n    }\n    educationfilePaths(empIndex) {\n      return this.education.at(empIndex).get('fileUploads');\n    }\n    newFilePathFormGroup(name) {\n      return this.fb.group({\n        filePath: name\n      });\n    }\n    addEducationFileField(empIndex, name) {\n      this.educationfilePaths(empIndex).push(this.newFilePathFormGroup(name));\n    }\n    removeEducationFileField(empIndex, skillIndex, filename) {\n      this.educationfilePaths(empIndex).removeAt(skillIndex);\n      this.subs.add(this.teacherService.deleteTeacherApplicationFiles(filename).subscribe(result => {\n        console.log(result);\n      }));\n    }\n    canDeactivate() {\n      if (this.formChanged) {\n        return new Observable(observer => {\n          this.confirmationService.confirm({\n            header: '',\n            key: 'stepLeaveConfirmation',\n            message: 'Are you sure you want to leave this page? Your changes will be lost.',\n            accept: () => {\n              observer.next(true);\n              observer.complete();\n            },\n            reject: () => {\n              observer.next(false);\n              observer.complete();\n            }\n          });\n        });\n      } else {\n        return true;\n      }\n    }\n    static #_ = this.ɵfac = function EducationComponent_Factory(t) {\n      return new (t || EducationComponent)(i0.ɵɵdirectiveInject(i1.GeneralService), i0.ɵɵdirectiveInject(i2.TeacherApplicationService), i0.ɵɵdirectiveInject(i3.ToastService), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i5.ConfirmationService), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.UntypedFormBuilder), i0.ɵɵdirectiveInject(i8.Location));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EducationComponent,\n      selectors: [[\"app-education\"]],\n      decls: 10,\n      vars: 1,\n      consts: [[\"tplwithFilePaths\", \"\"], [\"fileUpload\", \"\", \"taskFiles\", \"\"], [1, \"md:mt-3\"], [1, \"profile-info\"], [4, \"ngIf\"], [1, \"btns\", \"md:ml-5\", \"mt-4\"], [\"pRipple\", \"\", 1, \"rounded-blue-button\", \"transparent\", 3, \"click\"], [\"src\", \"/assets/icons/arrow-left-blue.svg\"], [\"pRipple\", \"\", 1, \"rounded-blue-button\", 3, \"click\"], [\"src\", \"/assets/icons/arrow-right.svg\"], [3, \"formGroup\"], [1, \"profile-info-section\"], [\"formArrayName\", \"education\"], [\"style\", \"position: relative;\", 4, \"ngFor\", \"ngForOf\"], [2, \"position\", \"relative\"], [3, \"formGroupName\"], [1, \"input-fields\", 3, \"ngClass\"], [1, \"input-field\", \"col--1of3\"], [1, \"input-element-title\"], [\"class\", \"hvr-grow\", 4, \"ngIf\"], [\"formControlName\", \"name\", \"type\", \"text\", \"placeholder\", \"e.g. Bachelor of Spanish\", 1, \"input-element\"], [\"class\", \"input-error\", 4, \"ngIf\"], [\"formControlName\", \"institute\", \"type\", \"text\", \"placeholder\", \"e.g. Aristotle University\", 1, \"input-element\"], [\"formArrayName\", \"fileUploads\", 1, \"input-field\", \"col--1of3\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"hvr-grow\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-plus\", 1, \"p-button-raised\", \"p-button-rounded\", \"plus-btn-circle\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-minus\", 1, \"p-button-raised\", \"p-button-rounded\", \"minus-btn-circle\", 3, \"click\"], [1, \"input-error\"], [1, \"rounded-blue-button\", \"upload-button\", \"transparent\", 3, \"click\"], [\"src\", \"/assets/icons/cloud-icon.svg\", \"alt\", \"cloud-icon\", 3, \"click\"], [\"class\", \"modal\", 4, \"ngIf\"], [1, \"modal\"], [1, \"popup-title\"], [\"src\", \"/assets/icons/close.png\", 1, \"close-img\", \"close-img-abs\", \"hvr-glow\", 3, \"click\"], [\"title\", \"Degree/Diploma Paper\", 3, \"onUploadFinished\", \"dndUI\", \"multiple\", \"isTeacherApplication\", \"postUrl\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"rounded-blue-button upload-button transparent\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"file-path\", 4, \"ngIf\"], [1, \"file-path\"], [\"src\", \"/assets/icons/education-degree-white.svg\", 1, \"close-img\", \"close-img-abs\", \"hvr-glow\"], [1, \"file-path-text\"], [\"src\", \"/assets/icons/trash-white.svg\", \"alt\", \"trash-white.svg\", 1, \"delete-file-icon-img\", \"hvr-glow\", 3, \"click\"], [\"title\", \"Degree/Diploma Paper\", 3, \"onUploadFinished\", \"dndUI\", \"multiple\", \"postUrl\"]],\n      template: function EducationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"div\", 2);\n          i0.ɵɵelementStart(1, \"div\", 3);\n          i0.ɵɵtemplate(2, EducationComponent_ng_container_2_Template, 6, 2, \"ng-container\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 5)(4, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function EducationComponent_Template_button_click_4_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵelement(5, \"img\", 7);\n          i0.ɵɵtext(6, \" Back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function EducationComponent_Template_button_click_7_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtext(8, \"Next \");\n          i0.ɵɵelement(9, \"img\", 9);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i8.NgClass, i8.NgForOf, i8.NgIf, i7.ɵNgNoValidate, i7.DefaultValueAccessor, i7.NgControlStatus, i7.NgControlStatusGroup, i7.FormGroupDirective, i7.FormControlName, i7.FormGroupName, i7.FormArrayName, i9.UploadFilesComponent, i10.ButtonDirective, i11.Ripple],\n      styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}.modal[_ngcontent-%COMP%]{min-width:50vw}[_nghost-%COMP%]     .p-dropdown:not(.p-disabled):hover{border:2px solid rgb(228,185,84)}  .no-label>.p-button-label{display:none}input[type=file][_ngcontent-%COMP%]{visibility:hidden;width:10px}input[type=date][_ngcontent-%COMP%]{position:relative;padding:10px}input[type=date][_ngcontent-%COMP%]::-webkit-calendar-picker-indicator{color:transparent;background:none;z-index:1}input[type=date][_ngcontent-%COMP%]:before{color:transparent;background:none;display:block;font-family:FontAwesome;content:\\\"\\\\f073\\\";width:20px;height:25px;position:absolute;top:12px;right:12px;color:#999}.image-preview[_ngcontent-%COMP%]{height:10rem;margin:1rem 0;border-radius:50%;border:3px solid var(--main-color)}.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{height:100%}[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-header{padding:clamp(.38rem,.47vw + .28rem,.75rem)}[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-items .p-dropdown-item{margin-bottom:clamp(.38rem,.47vw + .28rem,.75rem)}.country-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:clamp(.75rem,.31vw + .69rem,1rem);font-size:clamp(.88rem,.16vw + .84rem,1rem)}.country-item[_ngcontent-%COMP%]   .country-name[_ngcontent-%COMP%]{white-space:pre-line}.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;display:inline-block;vertical-align:middle}.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;clip-path:circle()}.country-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:32px;margin-right:.5rem}.country-item[_ngcontent-%COMP%]   img.smaller[_ngcontent-%COMP%]{width:16px}.info-element[_ngcontent-%COMP%]{padding:10px 0}.btns[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{margin:15px}.white-button[_ngcontent-%COMP%]{padding:10px}.add-btn[_ngcontent-%COMP%]{width:20px}.light-purple-circle-button[_ngcontent-%COMP%]{width:15px;height:15px}.input-fields[_ngcontent-%COMP%]{margin-top:10px;align-items:center}.trash[_ngcontent-%COMP%]{margin-left:auto;cursor:pointer}.trash[_ngcontent-%COMP%] > img[_ngcontent-%COMP%]{width:30px;margin-top:5px}.w-90[_ngcontent-%COMP%]{width:87%!important;flex-basis:87%!important}.add-degree[_ngcontent-%COMP%]{display:flex;margin-top:10px}.add-degree[_ngcontent-%COMP%]   .add-degree-input[_ngcontent-%COMP%]{margin-right:10px;width:30%}.delete-file-icon-img[_ngcontent-%COMP%]{margin-left:auto;cursor:pointer}.title[_ngcontent-%COMP%]{display:flex;align-items:center;margin-top:10px}.title[_ngcontent-%COMP%]   .title-text[_ngcontent-%COMP%]{font-weight:700;padding:10px 10px 10px 0;font-size:18px}.more-height-div[_ngcontent-%COMP%]   .input-element-title[_ngcontent-%COMP%]{height:30px}.more-height-div[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]{height:140px}\"]\n    });\n  }\n  return EducationComponent;\n})();", "map": {"version": 3, "names": ["UntypedFormArray", "UntypedFormControl", "UntypedFormGroup", "Validators", "SubSink", "Observable", "i0", "ɵɵelementStart", "ɵɵlistener", "EducationComponent_ng_container_2_div_5_div_6_Template_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "addEducation", "ɵɵelementEnd", "EducationComponent_ng_container_2_div_5_div_7_Template_button_click_1_listener", "_r3", "i_r4", "index", "removeEducation", "ɵɵtext", "EducationComponent_ng_container_2_div_5_div_18_div_4_Template_img_click_6_listener", "_r6", "hideFileUpload", "EducationComponent_ng_container_2_div_5_div_18_div_4_Template_app_upload_files_onUploadFinished_9_listener", "$event", "uploadFinished", "ɵɵadvance", "ɵɵproperty", "UPLOAD_TEACHER_APPLICATION_FILES_URL", "EducationComponent_ng_container_2_div_5_div_18_Template_div_click_1_listener", "_r5", "openFileUploadDialog", "EducationComponent_ng_container_2_div_5_div_18_Template_img_click_3_listener", "ɵɵtemplate", "EducationComponent_ng_container_2_div_5_div_18_div_4_Template", "educationfilePaths", "controls", "length", "showFileUpload", "EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_2_Template_div_click_0_listener", "_r7", "EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_2_Template_img_click_2_listener", "ɵɵelement", "EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_3_Template_img_click_4_listener", "_r8", "skillIndex_r9", "removeEducationFileField", "getFileName", "form", "value", "education", "fileUploads", "filePath", "ɵɵtextInterpolate", "EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_4_Template_img_click_6_listener", "_r10", "EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_4_Template_app_upload_files_onUploadFinished_9_listener", "EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_2_Template", "EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_3_Template", "EducationComponent_ng_container_2_div_5_ng_template_19_div_0_div_4_Template", "EducationComponent_ng_container_2_div_5_ng_template_19_div_0_Template", "ɵɵelementContainerStart", "EducationComponent_ng_container_2_div_5_div_6_Template", "EducationComponent_ng_container_2_div_5_div_7_Template", "EducationComponent_ng_container_2_div_5_div_9_Template", "EducationComponent_ng_container_2_div_5_div_14_Template", "EducationComponent_ng_container_2_div_5_div_18_Template", "EducationComponent_ng_container_2_div_5_ng_template_19_Template", "ɵɵtemplateRefExtractor", "ɵɵpureFunction1", "_c0", "ifFieldValid", "tplwithFilePaths_r11", "EducationComponent_ng_container_2_div_5_Template", "EducationComponent", "constructor", "generalService", "teacherService", "toastService", "authService", "confirmationService", "router", "fb", "location", "subs", "isTablet", "teacher", "dummy<PERSON><PERSON><PERSON>", "tryToSave", "isLoading", "speakLanguageCounter", "error", "teacherEducation", "formChanged", "ngOnInit", "setCurrentStepIndex", "add", "submitOnMenuClickListener", "subscribe", "res", "onSubmit", "getTeacherApplicationStep2", "updateStepFormValues", "initFormChangedListener", "deviceKind", "is1024", "for<PERSON>ach", "element", "push", "group", "ngOnDestroy", "unsubscribe", "setSubmitOnMenuClickListener", "get", "teacherApplicationStep2", "isNullishObject", "fileUploadElement", "addEducationFileField", "console", "log", "name", "required", "institute", "array", "removeAt", "valueChanges", "val", "valid", "updateTeacherApplicationStep2", "setShowToastmessage", "severity", "summary", "detail", "navigateByUrl", "replaceUrl", "back", "i", "setStepValid", "invalid", "goBack", "event", "db<PERSON><PERSON>", "fileIndex", "document", "getElementById", "classList", "remove", "fileName", "getFileName2", "removeEmployee", "empIndex", "at", "newFilePathFormGroup", "skillIndex", "filename", "deleteTeacherApplicationFiles", "result", "canDeactivate", "observer", "confirm", "header", "key", "message", "accept", "next", "complete", "reject", "_", "ɵɵdirectiveInject", "i1", "GeneralService", "i2", "TeacherApplicationService", "i3", "ToastService", "i4", "AuthService", "i5", "ConfirmationService", "i6", "Router", "i7", "UntypedFormBuilder", "i8", "Location", "_2", "selectors", "decls", "vars", "consts", "template", "EducationComponent_Template", "rf", "ctx", "EducationComponent_ng_container_2_Template", "EducationComponent_Template_button_click_4_listener", "EducationComponent_Template_button_click_7_listener"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\teacher-application\\education\\education.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\teacher-application\\education\\education.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Location } from '@angular/common';\r\nimport { UntypedFormArray, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { Teacher, TeacherApplicationEducation, TeacherApplicationStep2, TeacherEducation } from 'src/app/core/models/teacher.model';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { TeacherApplicationService } from 'src/app/core/services/teacher-application.service';\r\nimport { SubSink } from 'subsink';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { Observable } from 'rxjs';\r\nimport { ConfirmationService } from 'primeng/api';\r\n\r\n@Component({\r\n  selector: 'app-education',\r\n  templateUrl: './education.component.html',\r\n  styleUrls: ['./education.component.scss']\r\n})\r\nexport class EducationComponent implements OnInit {\r\n\r\n  private subs = new SubSink();\r\n  public form: UntypedFormGroup = new UntypedFormGroup({});\r\n  public isTablet: boolean = false;\r\n  public teacher: Teacher = this.teacherService.dummyTeacher;\r\n  tryToSave: boolean = false;\r\n  isLoading: boolean = false;\r\n  speakLanguageCounter: number = 0;\r\n  public error: boolean = false;\r\n  public UPLOAD_TEACHER_APPLICATION_FILES_URL = '/LMS/UploadTeacherApplicationFiles';\r\n  teacherEducation: TeacherEducation[] = [];\r\n  showFileUpload: boolean[] = [];\r\n  formChanged = false;\r\n\r\n  constructor(\r\n    private generalService: GeneralService,\r\n    private teacherService: TeacherApplicationService,\r\n    private toastService: ToastService,\r\n    private authService: AuthService,\r\n    private confirmationService: ConfirmationService,\r\n    private router: Router,\r\n    private fb: UntypedFormBuilder,\r\n    private location: Location\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.teacherService.setCurrentStepIndex(1);\r\n    this.subs.add(this.teacherService.submitOnMenuClickListener.subscribe(res => {\r\n      if (res) {\r\n        this.onSubmit();\r\n      }\r\n    }));\r\n\r\n    this.subs.add(this.teacherService.getTeacherApplicationStep2().subscribe(res => {\r\n      if (res) {\r\n        this.updateStepFormValues(res);\r\n      }\r\n      if (res.education === null) {\r\n        this.addEducation({} as TeacherApplicationEducation)\r\n      }\r\n      \r\n      this.initFormChangedListener();\r\n    }));\r\n\r\n    this.subs.add(this.generalService.deviceKind.subscribe(res => {\r\n      this.isTablet = res.is1024;\r\n    }));\r\n    this.form = new UntypedFormGroup({\r\n      education: new UntypedFormArray([]),\r\n    });\r\n    this.teacherEducation = this.teacher.education\r\n    this.teacherEducation.forEach(element => {\r\n      this.showFileUpload.push(false)\r\n      this.education.push(this.fb.group(element))\r\n    });\r\n\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n    this.teacherService.setSubmitOnMenuClickListener(false);\r\n  }\r\n\r\n  get education(): UntypedFormArray {\r\n    return this.form.get('education') as UntypedFormArray;\r\n  }\r\n\r\n  /**\r\nUpdate the form values for step 2 of the teacher application\r\n@param {TeacherApplicationStep2} teacherApplicationStep2 - The data for step 2 of the teacher application\r\n*/\r\n  updateStepFormValues(teacherApplicationStep2: TeacherApplicationStep2) {\r\n\r\n    if (!this.generalService.isNullishObject(teacherApplicationStep2.education)) {\r\n      teacherApplicationStep2.education.forEach((element, index) => {\r\n        this.addEducation(element);\r\n        element.fileUploads.forEach(fileUploadElement => {\r\n          this.addEducationFileField(index, fileUploadElement.filePath);\r\n        });\r\n      });\r\n      console.log(teacherApplicationStep2.education);\r\n\r\n    }\r\n  }\r\n\r\n  /**\r\n  Add an education to the form\r\n  @param {TeacherApplicationEducation} element - The education to add to the form\r\n  */\r\n  addEducation(element: TeacherApplicationEducation) {\r\n    const group = new UntypedFormGroup({\r\n      name: new UntypedFormControl(element ? element.name : '', Validators.required),\r\n      institute: new UntypedFormControl(element ? element.institute : '', Validators.required),\r\n      fileUploads: this.fb.array([])\r\n    });\r\n    this.education.push(group);\r\n  }\r\n\r\n  /**\r\n  Remove an education from the form\r\n  @param {number} index - The index of the education to remove\r\n  */\r\n  removeEducation(index: number) {\r\n    this.education.removeAt(index);\r\n  }\r\n  \r\n\r\n  initFormChangedListener() {\r\n    this.subs.add(this.form.valueChanges.subscribe(val => {\r\n      console.log(val);\r\n      this.formChanged = true;\r\n    }));\r\n  }\r\n\r\n  /**\r\n  Submit the form data\r\n  */\r\n  onSubmit() {\r\n    this.tryToSave = true;\r\n    if (!this.form.valid) {\r\n      this.error = true;\r\n      return;\r\n    }\r\n    this.formChanged = false;\r\n    this.subs.add(this.teacherService.updateTeacherApplicationStep2(this.form.value).subscribe(res => {\r\n      if (res) {\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'success',\r\n          summary: '',\r\n          detail: 'Your Education info were updated.'\r\n        });\r\n      }\r\n      this.router.navigateByUrl('/teacher/experience', { replaceUrl: true });\r\n    }));\r\n  }\r\n\r\n  back() {\r\n    this.location.back();\r\n  }\r\n\r\n  ifFieldValid(i: number) {\r\n    this.teacherService.setStepValid(1, this.form, 'teacher-education-route');\r\n    return ((this.tryToSave && (<UntypedFormArray>this.form.get('education')).controls[i].invalid))\r\n  }\r\n\r\n  goBack() {\r\n    this.router.navigateByUrl('/teacher/info', { replaceUrl: true });\r\n  }\r\n\r\n  /**\r\n * Handles the completion of a file upload\r\n *\r\n * @param {filePath: { dbPath: string; }} event - Event object containing information about the uploaded file\r\n * @param {number} i - Index of the file in the array of files\r\n *\r\n */\r\n  uploadFinished(event: { filePath: { dbPath: string; } }, i: number) {\r\n    this.hideFileUpload(i);\r\n    this.addEducationFileField(i, event.filePath.dbPath);\r\n  }\r\n\r\n  /**\r\n   * Opens the file upload dialog\r\n   *\r\n   * @param {number} fileIndex - Index of the file in the array of files\r\n   *\r\n   */\r\n  openFileUploadDialog(fileIndex: number) {\r\n    this.showFileUpload[fileIndex] = true\r\n    document.getElementById('blur_bg')?.classList.add('yes-visibility');\r\n  }\r\n\r\n  /**\r\n  Hides the file upload for a specific index.\r\n  @param {number} fileIndex - The index of the file upload to be hidden.\r\n  */\r\n  hideFileUpload(fileIndex: number) {\r\n    this.showFileUpload[fileIndex] = false;\r\n    document.getElementById('blur_bg')?.classList.remove('yes-visibility');\r\n  }\r\n\r\n  /**\r\n  Get the file name from a file path\r\n  @param {string} fileName - The file path\r\n  @returns {string} The file name\r\n  */\r\n  getFileName(fileName: string): string {\r\n    return this.generalService.getFileName2(fileName);\r\n  }\r\n\r\n  removeEmployee(empIndex: number) {\r\n    this.education.removeAt(empIndex);\r\n  }\r\n\r\n  educationfilePaths(empIndex: number): UntypedFormArray {\r\n    return this.education\r\n      .at(empIndex)\r\n      .get('fileUploads') as UntypedFormArray;\r\n  }\r\n\r\n  newFilePathFormGroup(name: string): UntypedFormGroup {\r\n    return this.fb.group({\r\n      filePath: name\r\n    });\r\n  }\r\n\r\n  addEducationFileField(empIndex: number, name: string) {\r\n    this.educationfilePaths(empIndex).push(this.newFilePathFormGroup(name));\r\n  }\r\n\r\n  removeEducationFileField(empIndex: number, skillIndex: number, filename: string) {\r\n    this.educationfilePaths(empIndex).removeAt(skillIndex);\r\n    this.subs.add(this.teacherService.deleteTeacherApplicationFiles(filename).subscribe(result => {\r\n      console.log(result);\r\n    }));\r\n  }\r\n  \r\n  canDeactivate(): Observable<boolean> | boolean {\r\n    if (this.formChanged) {\r\n      return new Observable((observer: any) => {\r\n        this.confirmationService.confirm({\r\n          header: '',\r\n          key: 'stepLeaveConfirmation',\r\n          message: 'Are you sure you want to leave this page? Your changes will be lost.',\r\n          accept: () => {\r\n            observer.next(true);\r\n            observer.complete();\r\n          },\r\n          reject: () => {\r\n            observer.next(false);\r\n            observer.complete();\r\n          }\r\n        });\r\n      });\r\n    } else {\r\n      return (true);\r\n    }\r\n  }\r\n}\r\n", "<div class=\"md:mt-3\"></div>\r\n<div class=\"profile-info\">\r\n    <ng-container *ngIf=\"!isLoading\">\r\n    <form [formGroup]=\"form\">\r\n        <div class=\"profile-info-section\">\r\n            <!-- <div class=\"title\">\r\n                <div class=\"title-text\">* Degrees / Diplomas</div>\r\n                <img (click)=\"addEducation()\" src=\"/assets/icons/add.png\" class=\"add hvr-grow\" style=\"width:20px\">\r\n                <div (click)=\"addEducation()\" class=\"link-purple-color\" style=\"margin-left:10px; font-weight:bold;\">Add\r\n                    New</div>\r\n            </div> -->\r\n            <div>\r\n                <ng-container formArrayName=\"education\">\r\n                    <div *ngFor=\"let _ of education.controls; index as i\" style=\"position: relative;\">\r\n                        <ng-container [formGroupName]=\"i\">\r\n\r\n                            <div class=\"input-fields\" [ngClass]=\"{'more-height-div': educationfilePaths(i).length > 0}\">\r\n                                <div class=\"input-field col--1of3\">\r\n                                    <div class=\"input-element-title\">Degrees / Diplomas *\r\n                                        <div *ngIf=\"i === 0\" class=\"hvr-grow\">\r\n                                            <button pButton type=\"button\" (click)=\"addEducation()\" class=\"p-button-raised p-button-rounded plus-btn-circle\" icon=\"pi pi-plus\"></button>\r\n                                        </div>\r\n                                        <div *ngIf=\"i > 0\" class=\"hvr-grow\">\r\n                                            <button pButton type=\"button\" (click)=\"removeEducation(i)\" class=\"p-button-raised p-button-rounded minus-btn-circle\" icon=\"pi pi-minus\"></button>\r\n                                        </div>\r\n                                    </div>\r\n                                    <input class=\"input-element\" formControlName=\"name\" type=\"text\"\r\n                                        placeholder=\"e.g. Bachelor of Spanish\">\r\n                                    <div *ngIf=\"ifFieldValid(i)\" class=\"input-error\">* Degrees / Diplomas is required\r\n                                    </div>\r\n                                </div>\r\n\r\n                                <div class=\"input-field col--1of3\">\r\n                                    <div class=\"input-element-title\">Institute *</div>\r\n                                    <input class=\"input-element\" formControlName=\"institute\" type=\"text\"\r\n                                        placeholder=\"e.g. Aristotle University\">\r\n                                    <div *ngIf=\"ifFieldValid(i)\" class=\"input-error\">Institute is required\r\n                                    </div>\r\n                                </div>\r\n                        <div class=\"input-field col--1of3\" formArrayName=\"fileUploads\">\r\n                            <div class=\"input-element-title\">Upload Degree/Diploma</div>\r\n                            <div *ngIf=\"educationfilePaths(i).controls.length === 0; else tplwithFilePaths\">\r\n                                <div (click)=\"openFileUploadDialog(i)\" class=\"rounded-blue-button upload-button transparent\">Upload File\r\n                                    <img (click)=\"hideFileUpload(i)\" src=\"/assets/icons/cloud-icon.svg\" alt=\"cloud-icon\" />\r\n                                </div>\r\n                                <div #fileUpload *ngIf=\"educationfilePaths(i).controls.length === 0 && showFileUpload[i]\" class=\"modal\"\r\n                                    #taskFiles>\r\n                                    <div class=\"popup-title\">\r\n                                        <div>Add files</div>\r\n                                        <img (click)=\"hideFileUpload(i)\" src=\"/assets/icons/close.png\" class=\"close-img close-img-abs hvr-glow\">\r\n                                    </div>\r\n                                    <div>\r\n                                        Upload Degree/Diploma\r\n                                    </div>\r\n                                    <app-upload-files [dndUI]=\"true\" [multiple]=\"false\" title=\"Degree/Diploma Paper\"\r\n                                    [isTeacherApplication]=\"true\"\r\n                                        [postUrl]=\"UPLOAD_TEACHER_APPLICATION_FILES_URL\" (onUploadFinished)=\"uploadFinished($event, i)\">\r\n                                    </app-upload-files>\r\n                                </div>\r\n                            </div>\r\n                            <ng-template #tplwithFilePaths>\r\n                                <div *ngFor=\"let skill of educationfilePaths(i).controls; let skillIndex=index\">\r\n                                    <div [formGroupName]=\"skillIndex\">\r\n                                        <div *ngIf=\"form.value.education[i].fileUploads.length === 1\" (click)=\"openFileUploadDialog(i)\"\r\n                                            class=\"rounded-blue-button upload-button transparent\">Upload File\r\n                                            <img (click)=\"hideFileUpload(i)\" src=\"/assets/icons/cloud-icon.svg\" alt=\"cloud-icon\" />\r\n                                        </div>\r\n                                        <!-- *ngIf=\"form.value.education[i].filePath!==''\"  -->\r\n                                        <div *ngIf=\"form.value.education[i].fileUploads[skillIndex].length !== 1\" class=\"file-path\">\r\n                                            <img src=\"/assets/icons/education-degree-white.svg\" class=\"close-img close-img-abs hvr-glow\">\r\n                                            <span class=\"file-path-text\">{{getFileName(form.value.education[i].fileUploads[skillIndex].filePath)}}</span>\r\n                                            <img src=\"/assets/icons/trash-white.svg\" alt=\"trash-white.svg\" class=\"delete-file-icon-img hvr-glow\"\r\n                                                (click)=\"removeEducationFileField(i, skillIndex, getFileName(form.value.education[i].fileUploads[skillIndex].filePath))\">\r\n                                        </div>\r\n                                        <div #fileUpload *ngIf=\"showFileUpload[skillIndex]\" class=\"modal\" #taskFiles>\r\n                                            <div class=\"popup-title\">\r\n                                                <div>Add files</div>\r\n                                                <img (click)=\"hideFileUpload(skillIndex)\" src=\"/assets/icons/close.png\"\r\n                                                    class=\"close-img close-img-abs hvr-glow\">\r\n                                            </div>\r\n                                            <div>\r\n                                                Upload Degree/Diploma\r\n                                            </div>\r\n                                            <app-upload-files [dndUI]=\"true\" [multiple]=\"false\" title=\"Degree/Diploma Paper\"\r\n                                                [postUrl]=\"UPLOAD_TEACHER_APPLICATION_FILES_URL\"\r\n                                                (onUploadFinished)=\"uploadFinished($event, skillIndex)\">\r\n                                            </app-upload-files>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </ng-template>\r\n                        </div>\r\n                        </div>\r\n                        <!-- <div *ngIf=\"ifFieldValid(i)\" class=\"input-error\">* Input field and file upload are required\r\n                        </div> -->\r\n                        </ng-container>\r\n                    </div>\r\n                </ng-container>\r\n            </div>\r\n        </div>\r\n    </form>\r\n    </ng-container>\r\n</div>\r\n<div class=\"btns md:ml-5 mt-4\">\r\n    <button pRipple class=\"rounded-blue-button transparent\" (click)=\"goBack()\"> <img src=\"/assets/icons/arrow-left-blue.svg\" />\r\n        Back</button>\r\n    <button pRipple (click)=\"onSubmit()\" class=\"rounded-blue-button\">Next <img src=\"/assets/icons/arrow-right.svg\" /></button>\r\n    <!-- <div (click)=\"onSubmit()\" class=\"light-purple-button\">Next</div> -->\r\n</div>"], "mappings": "AAEA,SAASA,gBAAgB,EAAsBC,kBAAkB,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,gBAAgB;AAKvH,SAASC,OAAO,QAAQ,SAAS;AAGjC,SAASC,UAAU,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;ICUWC,EADJ,CAAAC,cAAA,cAAsC,iBACgG;IAApGD,EAAA,CAAAE,UAAA,mBAAAC,+EAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IAC1DT,EADsI,CAAAU,YAAA,EAAS,EACzI;;;;;;IAEFV,EADJ,CAAAC,cAAA,cAAoC,iBACwG;IAA1GD,EAAA,CAAAE,UAAA,mBAAAS,+EAAA;MAAAX,EAAA,CAAAI,aAAA,CAAAQ,GAAA;MAAA,MAAAC,IAAA,GAAAb,EAAA,CAAAO,aAAA,GAAAO,KAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAS,eAAA,CAAAF,IAAA,CAAkB;IAAA,EAAC;IAC9Db,EAD4I,CAAAU,YAAA,EAAS,EAC/I;;;;;IAIVV,EAAA,CAAAC,cAAA,cAAiD;IAAAD,EAAA,CAAAgB,MAAA,wCACjD;IAAAhB,EAAA,CAAAU,YAAA,EAAM;;;;;IAONV,EAAA,CAAAC,cAAA,cAAiD;IAAAD,EAAA,CAAAgB,MAAA,6BACjD;IAAAhB,EAAA,CAAAU,YAAA,EAAM;;;;;;IAWFV,EAHR,CAAAC,cAAA,iBACe,cACc,UAChB;IAAAD,EAAA,CAAAgB,MAAA,gBAAS;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IACpBV,EAAA,CAAAC,cAAA,cAAwG;IAAnGD,EAAA,CAAAE,UAAA,mBAAAe,mFAAA;MAAAjB,EAAA,CAAAI,aAAA,CAAAc,GAAA;MAAA,MAAAL,IAAA,GAAAb,EAAA,CAAAO,aAAA,IAAAO,KAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAa,cAAA,CAAAN,IAAA,CAAiB;IAAA,EAAC;IACpCb,EADI,CAAAU,YAAA,EAAwG,EACtG;IACNV,EAAA,CAAAC,cAAA,UAAK;IACDD,EAAA,CAAAgB,MAAA,8BACJ;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,2BAEoG;IAA/CD,EAAA,CAAAE,UAAA,8BAAAkB,2GAAAC,MAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAc,GAAA;MAAA,MAAAL,IAAA,GAAAb,EAAA,CAAAO,aAAA,IAAAO,KAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAoBF,MAAA,CAAAgB,cAAA,CAAAD,MAAA,EAAAR,IAAA,CAAyB;IAAA,EAAC;IAEvGb,EADI,CAAAU,YAAA,EAAmB,EACjB;;;;IAJgBV,EAAA,CAAAuB,SAAA,GAAc;IAE5BvB,EAFc,CAAAwB,UAAA,eAAc,mBAAmB,8BACtB,YAAAlB,MAAA,CAAAmB,oCAAA,CACuB;;;;;;IAdxDzB,EADJ,CAAAC,cAAA,UAAgF,cACiB;IAAxFD,EAAA,CAAAE,UAAA,mBAAAwB,6EAAA;MAAA1B,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,MAAAd,IAAA,GAAAb,EAAA,CAAAO,aAAA,GAAAO,KAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,oBAAA,CAAAf,IAAA,CAAuB;IAAA,EAAC;IAAuDb,EAAA,CAAAgB,MAAA,mBACzF;IAAAhB,EAAA,CAAAC,cAAA,cAAuF;IAAlFD,EAAA,CAAAE,UAAA,mBAAA2B,6EAAA;MAAA7B,EAAA,CAAAI,aAAA,CAAAuB,GAAA;MAAA,MAAAd,IAAA,GAAAb,EAAA,CAAAO,aAAA,GAAAO,KAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAa,cAAA,CAAAN,IAAA,CAAiB;IAAA,EAAC;IACpCb,EADI,CAAAU,YAAA,EAAuF,EACrF;IACNV,EAAA,CAAA8B,UAAA,IAAAC,6DAAA,mBACe;IAanB/B,EAAA,CAAAU,YAAA,EAAM;;;;;IAdgBV,EAAA,CAAAuB,SAAA,GAAsE;IAAtEvB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAA0B,kBAAA,CAAAnB,IAAA,EAAAoB,QAAA,CAAAC,MAAA,UAAA5B,MAAA,CAAA6B,cAAA,CAAAtB,IAAA,EAAsE;;;;;;IAkBhFb,EAAA,CAAAC,cAAA,cAC0D;IADID,EAAA,CAAAE,UAAA,mBAAAkC,iGAAA;MAAApC,EAAA,CAAAI,aAAA,CAAAiC,GAAA;MAAA,MAAAxB,IAAA,GAAAb,EAAA,CAAAO,aAAA,IAAAO,KAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAsB,oBAAA,CAAAf,IAAA,CAAuB;IAAA,EAAC;IACrCb,EAAA,CAAAgB,MAAA,mBACtD;IAAAhB,EAAA,CAAAC,cAAA,cAAuF;IAAlFD,EAAA,CAAAE,UAAA,mBAAAoC,iGAAA;MAAAtC,EAAA,CAAAI,aAAA,CAAAiC,GAAA;MAAA,MAAAxB,IAAA,GAAAb,EAAA,CAAAO,aAAA,IAAAO,KAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAa,cAAA,CAAAN,IAAA,CAAiB;IAAA,EAAC;IACpCb,EADI,CAAAU,YAAA,EAAuF,EACrF;;;;;;IAENV,EAAA,CAAAC,cAAA,cAA4F;IACxFD,EAAA,CAAAuC,SAAA,cAA6F;IAC7FvC,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAgB,MAAA,GAAyE;IAAAhB,EAAA,CAAAU,YAAA,EAAO;IAC7GV,EAAA,CAAAC,cAAA,cAC6H;IAAzHD,EAAA,CAAAE,UAAA,mBAAAsC,iGAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAAqC,GAAA;MAAA,MAAAC,aAAA,GAAA1C,EAAA,CAAAO,aAAA,GAAAO,KAAA;MAAA,MAAAD,IAAA,GAAAb,EAAA,CAAAO,aAAA,IAAAO,KAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqC,wBAAA,CAAA9B,IAAA,EAAA6B,aAAA,EAAwCpC,MAAA,CAAAsC,WAAA,CAAAtC,MAAA,CAAAuC,IAAA,CAAAC,KAAA,CAAAC,SAAA,CAAAlC,IAAA,EAAAmC,WAAA,CAAAN,aAAA,EAAAO,QAAA,CAAqE,CAAC;IAAA,EAAC;IAChIjD,EAFI,CAAAU,YAAA,EAC6H,EAC3H;;;;;;IAH2BV,EAAA,CAAAuB,SAAA,GAAyE;IAAzEvB,EAAA,CAAAkD,iBAAA,CAAA5C,MAAA,CAAAsC,WAAA,CAAAtC,MAAA,CAAAuC,IAAA,CAAAC,KAAA,CAAAC,SAAA,CAAAlC,IAAA,EAAAmC,WAAA,CAAAN,aAAA,EAAAO,QAAA,EAAyE;;;;;;IAMlGjD,EAFR,CAAAC,cAAA,iBAA6E,cAChD,UAChB;IAAAD,EAAA,CAAAgB,MAAA,gBAAS;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IACpBV,EAAA,CAAAC,cAAA,cAC6C;IADxCD,EAAA,CAAAE,UAAA,mBAAAiD,iGAAA;MAAAnD,EAAA,CAAAI,aAAA,CAAAgD,IAAA;MAAA,MAAAV,aAAA,GAAA1C,EAAA,CAAAO,aAAA,GAAAO,KAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAa,cAAA,CAAAuB,aAAA,CAA0B;IAAA,EAAC;IAE7C1C,EAFI,CAAAU,YAAA,EAC6C,EAC3C;IACNV,EAAA,CAAAC,cAAA,UAAK;IACDD,EAAA,CAAAgB,MAAA,8BACJ;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,2BAE4D;IAAxDD,EAAA,CAAAE,UAAA,8BAAAmD,yHAAAhC,MAAA;MAAArB,EAAA,CAAAI,aAAA,CAAAgD,IAAA;MAAA,MAAAV,aAAA,GAAA1C,EAAA,CAAAO,aAAA,GAAAO,KAAA;MAAA,MAAAR,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAoBF,MAAA,CAAAgB,cAAA,CAAAD,MAAA,EAAAqB,aAAA,CAAkC;IAAA,EAAC;IAE/D1C,EADI,CAAAU,YAAA,EAAmB,EACjB;;;;IAJgBV,EAAA,CAAAuB,SAAA,GAAc;IAC5BvB,EADc,CAAAwB,UAAA,eAAc,mBAAmB,YAAAlB,MAAA,CAAAmB,oCAAA,CACC;;;;;IAtB5DzB,EADJ,CAAAC,cAAA,UAAgF,cAC1C;IAY9BD,EAXA,CAAA8B,UAAA,IAAAwB,2EAAA,kBAC0D,IAAAC,2EAAA,kBAIkC,IAAAC,2EAAA,mBAMf;IAerFxD,EADI,CAAAU,YAAA,EAAM,EACJ;;;;;;IA3BGV,EAAA,CAAAuB,SAAA,EAA4B;IAA5BvB,EAAA,CAAAwB,UAAA,kBAAAkB,aAAA,CAA4B;IACvB1C,EAAA,CAAAuB,SAAA,EAAsD;IAAtDvB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAuC,IAAA,CAAAC,KAAA,CAAAC,SAAA,CAAAlC,IAAA,EAAAmC,WAAA,CAAAd,MAAA,OAAsD;IAKtDlC,EAAA,CAAAuB,SAAA,EAAkE;IAAlEvB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAAuC,IAAA,CAAAC,KAAA,CAAAC,SAAA,CAAAlC,IAAA,EAAAmC,WAAA,CAAAN,aAAA,EAAAR,MAAA,OAAkE;IAMtDlC,EAAA,CAAAuB,SAAA,EAAgC;IAAhCvB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAA6B,cAAA,CAAAO,aAAA,EAAgC;;;;;IAb1D1C,EAAA,CAAA8B,UAAA,IAAA2B,qEAAA,kBAAgF;;;;;IAAzDzD,EAAA,CAAAwB,UAAA,YAAAlB,MAAA,CAAA0B,kBAAA,CAAAnB,IAAA,EAAAoB,QAAA,CAAmC;;;;;IAhDtEjC,EAAA,CAAAC,cAAA,cAAkF;IAC9ED,EAAA,CAAA0D,uBAAA,OAAkC;IAItB1D,EAFR,CAAAC,cAAA,cAA4F,cACrD,cACE;IAAAD,EAAA,CAAAgB,MAAA,4BAC7B;IAGAhB,EAHA,CAAA8B,UAAA,IAAA6B,sDAAA,kBAAsC,IAAAC,sDAAA,kBAGF;IAGxC5D,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAuC,SAAA,gBAC2C;IAC3CvC,EAAA,CAAA8B,UAAA,IAAA+B,sDAAA,kBAAiD;IAErD7D,EAAA,CAAAU,YAAA,EAAM;IAGFV,EADJ,CAAAC,cAAA,eAAmC,eACE;IAAAD,EAAA,CAAAgB,MAAA,mBAAW;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IAClDV,EAAA,CAAAuC,SAAA,iBAC4C;IAC5CvC,EAAA,CAAA8B,UAAA,KAAAgC,uDAAA,kBAAiD;IAErD9D,EAAA,CAAAU,YAAA,EAAM;IAEVV,EADJ,CAAAC,cAAA,eAA+D,eAC1B;IAAAD,EAAA,CAAAgB,MAAA,6BAAqB;IAAAhB,EAAA,CAAAU,YAAA,EAAM;IAoB5DV,EAnBA,CAAA8B,UAAA,KAAAiC,uDAAA,kBAAgF,KAAAC,+DAAA,gCAAAhE,EAAA,CAAAiE,sBAAA,CAmBjD;IAgCnCjE,EADA,CAAAU,YAAA,EAAM,EACA;;IAIVV,EAAA,CAAAU,YAAA,EAAM;;;;;;IAlFYV,EAAA,CAAAuB,SAAA,EAAmB;IAAnBvB,EAAA,CAAAwB,UAAA,kBAAAX,IAAA,CAAmB;IAEHb,EAAA,CAAAuB,SAAA,EAAiE;IAAjEvB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAkE,eAAA,IAAAC,GAAA,EAAA7D,MAAA,CAAA0B,kBAAA,CAAAnB,IAAA,EAAAqB,MAAA,MAAiE;IAGzElC,EAAA,CAAAuB,SAAA,GAAa;IAAbvB,EAAA,CAAAwB,UAAA,SAAAX,IAAA,OAAa;IAGbb,EAAA,CAAAuB,SAAA,EAAW;IAAXvB,EAAA,CAAAwB,UAAA,SAAAX,IAAA,KAAW;IAMfb,EAAA,CAAAuB,SAAA,GAAqB;IAArBvB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAA8D,YAAA,CAAAvD,IAAA,EAAqB;IAQrBb,EAAA,CAAAuB,SAAA,GAAqB;IAArBvB,EAAA,CAAAwB,UAAA,SAAAlB,MAAA,CAAA8D,YAAA,CAAAvD,IAAA,EAAqB;IAK7Bb,EAAA,CAAAuB,SAAA,GAAmD;IAAAvB,EAAnD,CAAAwB,UAAA,SAAAlB,MAAA,CAAA0B,kBAAA,CAAAnB,IAAA,EAAAoB,QAAA,CAAAC,MAAA,OAAmD,aAAAmC,oBAAA,CAAqB;;;;;IAvCtGrE,EAAA,CAAA0D,uBAAA,GAAiC;IASzB1D,EARR,CAAAC,cAAA,eAAyB,cACa,UAOzB;IACDD,EAAA,CAAA0D,uBAAA,OAAwC;IACpC1D,EAAA,CAAA8B,UAAA,IAAAwC,gDAAA,oBAAkF;;IAuFlGtE,EAFQ,CAAAU,YAAA,EAAM,EACJ,EACH;;;;;IAjGDV,EAAA,CAAAuB,SAAA,EAAkB;IAAlBvB,EAAA,CAAAwB,UAAA,cAAAlB,MAAA,CAAAuC,IAAA,CAAkB;IAUW7C,EAAA,CAAAuB,SAAA,GAAuB;IAAvBvB,EAAA,CAAAwB,UAAA,YAAAlB,MAAA,CAAAyC,SAAA,CAAAd,QAAA,CAAuB;;;ADK9D,WAAasC,kBAAkB;EAAzB,MAAOA,kBAAkB;IAe7BC,YACUC,cAA8B,EAC9BC,cAAyC,EACzCC,YAA0B,EAC1BC,WAAwB,EACxBC,mBAAwC,EACxCC,MAAc,EACdC,EAAsB,EACtBC,QAAkB;MAPlB,KAAAP,cAAc,GAAdA,cAAc;MACd,KAAAC,cAAc,GAAdA,cAAc;MACd,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,WAAW,GAAXA,WAAW;MACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;MACnB,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,EAAE,GAAFA,EAAE;MACF,KAAAC,QAAQ,GAARA,QAAQ;MArBV,KAAAC,IAAI,GAAG,IAAInF,OAAO,EAAE;MACrB,KAAA+C,IAAI,GAAqB,IAAIjD,gBAAgB,CAAC,EAAE,CAAC;MACjD,KAAAsF,QAAQ,GAAY,KAAK;MACzB,KAAAC,OAAO,GAAY,IAAI,CAACT,cAAc,CAACU,YAAY;MAC1D,KAAAC,SAAS,GAAY,KAAK;MAC1B,KAAAC,SAAS,GAAY,KAAK;MAC1B,KAAAC,oBAAoB,GAAW,CAAC;MACzB,KAAAC,KAAK,GAAY,KAAK;MACtB,KAAA/D,oCAAoC,GAAG,oCAAoC;MAClF,KAAAgE,gBAAgB,GAAuB,EAAE;MACzC,KAAAtD,cAAc,GAAc,EAAE;MAC9B,KAAAuD,WAAW,GAAG,KAAK;IAWf;IAEJC,QAAQA,CAAA;MACN,IAAI,CAACjB,cAAc,CAACkB,mBAAmB,CAAC,CAAC,CAAC;MAC1C,IAAI,CAACX,IAAI,CAACY,GAAG,CAAC,IAAI,CAACnB,cAAc,CAACoB,yBAAyB,CAACC,SAAS,CAACC,GAAG,IAAG;QAC1E,IAAIA,GAAG,EAAE;UACP,IAAI,CAACC,QAAQ,EAAE;QACjB;MACF,CAAC,CAAC,CAAC;MAEH,IAAI,CAAChB,IAAI,CAACY,GAAG,CAAC,IAAI,CAACnB,cAAc,CAACwB,0BAA0B,EAAE,CAACH,SAAS,CAACC,GAAG,IAAG;QAC7E,IAAIA,GAAG,EAAE;UACP,IAAI,CAACG,oBAAoB,CAACH,GAAG,CAAC;QAChC;QACA,IAAIA,GAAG,CAACjD,SAAS,KAAK,IAAI,EAAE;UAC1B,IAAI,CAACtC,YAAY,CAAC,EAAiC,CAAC;QACtD;QAEA,IAAI,CAAC2F,uBAAuB,EAAE;MAChC,CAAC,CAAC,CAAC;MAEH,IAAI,CAACnB,IAAI,CAACY,GAAG,CAAC,IAAI,CAACpB,cAAc,CAAC4B,UAAU,CAACN,SAAS,CAACC,GAAG,IAAG;QAC3D,IAAI,CAACd,QAAQ,GAAGc,GAAG,CAACM,MAAM;MAC5B,CAAC,CAAC,CAAC;MACH,IAAI,CAACzD,IAAI,GAAG,IAAIjD,gBAAgB,CAAC;QAC/BmD,SAAS,EAAE,IAAIrD,gBAAgB,CAAC,EAAE;OACnC,CAAC;MACF,IAAI,CAAC+F,gBAAgB,GAAG,IAAI,CAACN,OAAO,CAACpC,SAAS;MAC9C,IAAI,CAAC0C,gBAAgB,CAACc,OAAO,CAACC,OAAO,IAAG;QACtC,IAAI,CAACrE,cAAc,CAACsE,IAAI,CAAC,KAAK,CAAC;QAC/B,IAAI,CAAC1D,SAAS,CAAC0D,IAAI,CAAC,IAAI,CAAC1B,EAAE,CAAC2B,KAAK,CAACF,OAAO,CAAC,CAAC;MAC7C,CAAC,CAAC;IAEJ;IAEAG,WAAWA,CAAA;MACT,IAAI,CAAC1B,IAAI,CAAC2B,WAAW,EAAE;MACvB,IAAI,CAAClC,cAAc,CAACmC,4BAA4B,CAAC,KAAK,CAAC;IACzD;IAEA,IAAI9D,SAASA,CAAA;MACX,OAAO,IAAI,CAACF,IAAI,CAACiE,GAAG,CAAC,WAAW,CAAqB;IACvD;IAEA;;;;IAIAX,oBAAoBA,CAACY,uBAAgD;MAEnE,IAAI,CAAC,IAAI,CAACtC,cAAc,CAACuC,eAAe,CAACD,uBAAuB,CAAChE,SAAS,CAAC,EAAE;QAC3EgE,uBAAuB,CAAChE,SAAS,CAACwD,OAAO,CAAC,CAACC,OAAO,EAAE1F,KAAK,KAAI;UAC3D,IAAI,CAACL,YAAY,CAAC+F,OAAO,CAAC;UAC1BA,OAAO,CAACxD,WAAW,CAACuD,OAAO,CAACU,iBAAiB,IAAG;YAC9C,IAAI,CAACC,qBAAqB,CAACpG,KAAK,EAAEmG,iBAAiB,CAAChE,QAAQ,CAAC;UAC/D,CAAC,CAAC;QACJ,CAAC,CAAC;QACFkE,OAAO,CAACC,GAAG,CAACL,uBAAuB,CAAChE,SAAS,CAAC;MAEhD;IACF;IAEA;;;;IAIAtC,YAAYA,CAAC+F,OAAoC;MAC/C,MAAME,KAAK,GAAG,IAAI9G,gBAAgB,CAAC;QACjCyH,IAAI,EAAE,IAAI1H,kBAAkB,CAAC6G,OAAO,GAAGA,OAAO,CAACa,IAAI,GAAG,EAAE,EAAExH,UAAU,CAACyH,QAAQ,CAAC;QAC9EC,SAAS,EAAE,IAAI5H,kBAAkB,CAAC6G,OAAO,GAAGA,OAAO,CAACe,SAAS,GAAG,EAAE,EAAE1H,UAAU,CAACyH,QAAQ,CAAC;QACxFtE,WAAW,EAAE,IAAI,CAAC+B,EAAE,CAACyC,KAAK,CAAC,EAAE;OAC9B,CAAC;MACF,IAAI,CAACzE,SAAS,CAAC0D,IAAI,CAACC,KAAK,CAAC;IAC5B;IAEA;;;;IAIA3F,eAAeA,CAACD,KAAa;MAC3B,IAAI,CAACiC,SAAS,CAAC0E,QAAQ,CAAC3G,KAAK,CAAC;IAChC;IAGAsF,uBAAuBA,CAAA;MACrB,IAAI,CAACnB,IAAI,CAACY,GAAG,CAAC,IAAI,CAAChD,IAAI,CAAC6E,YAAY,CAAC3B,SAAS,CAAC4B,GAAG,IAAG;QACnDR,OAAO,CAACC,GAAG,CAACO,GAAG,CAAC;QAChB,IAAI,CAACjC,WAAW,GAAG,IAAI;MACzB,CAAC,CAAC,CAAC;IACL;IAEA;;;IAGAO,QAAQA,CAAA;MACN,IAAI,CAACZ,SAAS,GAAG,IAAI;MACrB,IAAI,CAAC,IAAI,CAACxC,IAAI,CAAC+E,KAAK,EAAE;QACpB,IAAI,CAACpC,KAAK,GAAG,IAAI;QACjB;MACF;MACA,IAAI,CAACE,WAAW,GAAG,KAAK;MACxB,IAAI,CAACT,IAAI,CAACY,GAAG,CAAC,IAAI,CAACnB,cAAc,CAACmD,6BAA6B,CAAC,IAAI,CAAChF,IAAI,CAACC,KAAK,CAAC,CAACiD,SAAS,CAACC,GAAG,IAAG;QAC/F,IAAIA,GAAG,EAAE;UACP,IAAI,CAACrB,YAAY,CAACmD,mBAAmB,CAAC;YACpCC,QAAQ,EAAE,SAAS;YACnBC,OAAO,EAAE,EAAE;YACXC,MAAM,EAAE;WACT,CAAC;QACJ;QACA,IAAI,CAACnD,MAAM,CAACoD,aAAa,CAAC,qBAAqB,EAAE;UAAEC,UAAU,EAAE;QAAI,CAAE,CAAC;MACxE,CAAC,CAAC,CAAC;IACL;IAEAC,IAAIA,CAAA;MACF,IAAI,CAACpD,QAAQ,CAACoD,IAAI,EAAE;IACtB;IAEAhE,YAAYA,CAACiE,CAAS;MACpB,IAAI,CAAC3D,cAAc,CAAC4D,YAAY,CAAC,CAAC,EAAE,IAAI,CAACzF,IAAI,EAAE,yBAAyB,CAAC;MACzE,OAAS,IAAI,CAACwC,SAAS,IAAuB,IAAI,CAACxC,IAAI,CAACiE,GAAG,CAAC,WAAW,CAAE,CAAC7E,QAAQ,CAACoG,CAAC,CAAC,CAACE,OAAO;IAC/F;IAEAC,MAAMA,CAAA;MACJ,IAAI,CAAC1D,MAAM,CAACoD,aAAa,CAAC,eAAe,EAAE;QAAEC,UAAU,EAAE;MAAI,CAAE,CAAC;IAClE;IAEA;;;;;;;IAOA7G,cAAcA,CAACmH,KAAwC,EAAEJ,CAAS;MAChE,IAAI,CAAClH,cAAc,CAACkH,CAAC,CAAC;MACtB,IAAI,CAACnB,qBAAqB,CAACmB,CAAC,EAAEI,KAAK,CAACxF,QAAQ,CAACyF,MAAM,CAAC;IACtD;IAEA;;;;;;IAMA9G,oBAAoBA,CAAC+G,SAAiB;MACpC,IAAI,CAACxG,cAAc,CAACwG,SAAS,CAAC,GAAG,IAAI;MACrCC,QAAQ,CAACC,cAAc,CAAC,SAAS,CAAC,EAAEC,SAAS,CAACjD,GAAG,CAAC,gBAAgB,CAAC;IACrE;IAEA;;;;IAIA1E,cAAcA,CAACwH,SAAiB;MAC9B,IAAI,CAACxG,cAAc,CAACwG,SAAS,CAAC,GAAG,KAAK;MACtCC,QAAQ,CAACC,cAAc,CAAC,SAAS,CAAC,EAAEC,SAAS,CAACC,MAAM,CAAC,gBAAgB,CAAC;IACxE;IAEA;;;;;IAKAnG,WAAWA,CAACoG,QAAgB;MAC1B,OAAO,IAAI,CAACvE,cAAc,CAACwE,YAAY,CAACD,QAAQ,CAAC;IACnD;IAEAE,cAAcA,CAACC,QAAgB;MAC7B,IAAI,CAACpG,SAAS,CAAC0E,QAAQ,CAAC0B,QAAQ,CAAC;IACnC;IAEAnH,kBAAkBA,CAACmH,QAAgB;MACjC,OAAO,IAAI,CAACpG,SAAS,CAClBqG,EAAE,CAACD,QAAQ,CAAC,CACZrC,GAAG,CAAC,aAAa,CAAqB;IAC3C;IAEAuC,oBAAoBA,CAAChC,IAAY;MAC/B,OAAO,IAAI,CAACtC,EAAE,CAAC2B,KAAK,CAAC;QACnBzD,QAAQ,EAAEoE;OACX,CAAC;IACJ;IAEAH,qBAAqBA,CAACiC,QAAgB,EAAE9B,IAAY;MAClD,IAAI,CAACrF,kBAAkB,CAACmH,QAAQ,CAAC,CAAC1C,IAAI,CAAC,IAAI,CAAC4C,oBAAoB,CAAChC,IAAI,CAAC,CAAC;IACzE;IAEA1E,wBAAwBA,CAACwG,QAAgB,EAAEG,UAAkB,EAAEC,QAAgB;MAC7E,IAAI,CAACvH,kBAAkB,CAACmH,QAAQ,CAAC,CAAC1B,QAAQ,CAAC6B,UAAU,CAAC;MACtD,IAAI,CAACrE,IAAI,CAACY,GAAG,CAAC,IAAI,CAACnB,cAAc,CAAC8E,6BAA6B,CAACD,QAAQ,CAAC,CAACxD,SAAS,CAAC0D,MAAM,IAAG;QAC3FtC,OAAO,CAACC,GAAG,CAACqC,MAAM,CAAC;MACrB,CAAC,CAAC,CAAC;IACL;IAEAC,aAAaA,CAAA;MACX,IAAI,IAAI,CAAChE,WAAW,EAAE;QACpB,OAAO,IAAI3F,UAAU,CAAE4J,QAAa,IAAI;UACtC,IAAI,CAAC9E,mBAAmB,CAAC+E,OAAO,CAAC;YAC/BC,MAAM,EAAE,EAAE;YACVC,GAAG,EAAE,uBAAuB;YAC5BC,OAAO,EAAE,sEAAsE;YAC/EC,MAAM,EAAEA,CAAA,KAAK;cACXL,QAAQ,CAACM,IAAI,CAAC,IAAI,CAAC;cACnBN,QAAQ,CAACO,QAAQ,EAAE;YACrB,CAAC;YACDC,MAAM,EAAEA,CAAA,KAAK;cACXR,QAAQ,CAACM,IAAI,CAAC,KAAK,CAAC;cACpBN,QAAQ,CAACO,QAAQ,EAAE;YACrB;WACD,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,OAAQ,IAAI;MACd;IACF;IAAC,QAAAE,CAAA,G;uBA9OU7F,kBAAkB,EAAAvE,EAAA,CAAAqK,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAvK,EAAA,CAAAqK,iBAAA,CAAAG,EAAA,CAAAC,yBAAA,GAAAzK,EAAA,CAAAqK,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAA3K,EAAA,CAAAqK,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA7K,EAAA,CAAAqK,iBAAA,CAAAS,EAAA,CAAAC,mBAAA,GAAA/K,EAAA,CAAAqK,iBAAA,CAAAW,EAAA,CAAAC,MAAA,GAAAjL,EAAA,CAAAqK,iBAAA,CAAAa,EAAA,CAAAC,kBAAA,GAAAnL,EAAA,CAAAqK,iBAAA,CAAAe,EAAA,CAAAC,QAAA;IAAA;IAAA,QAAAC,EAAA,G;YAAlB/G,kBAAkB;MAAAgH,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClB/B7L,EAAA,CAAAuC,SAAA,aAA2B;UAC3BvC,EAAA,CAAAC,cAAA,aAA0B;UACtBD,EAAA,CAAA8B,UAAA,IAAAiK,0CAAA,0BAAiC;UAoGrC/L,EAAA,CAAAU,YAAA,EAAM;UAEFV,EADJ,CAAAC,cAAA,aAA+B,gBACgD;UAAnBD,EAAA,CAAAE,UAAA,mBAAA8L,oDAAA;YAAA,OAASF,GAAA,CAAAtD,MAAA,EAAQ;UAAA,EAAC;UAAExI,EAAA,CAAAuC,SAAA,aAA+C;UACvHvC,EAAA,CAAAgB,MAAA,YAAI;UAAAhB,EAAA,CAAAU,YAAA,EAAS;UACjBV,EAAA,CAAAC,cAAA,gBAAiE;UAAjDD,EAAA,CAAAE,UAAA,mBAAA+L,oDAAA;YAAA,OAASH,GAAA,CAAA7F,QAAA,EAAU;UAAA,EAAC;UAA6BjG,EAAA,CAAAgB,MAAA,YAAK;UAAAhB,EAAA,CAAAuC,SAAA,aAA2C;UAErHvC,EAFqH,CAAAU,YAAA,EAAS,EAExH;;;UA1GaV,EAAA,CAAAuB,SAAA,GAAgB;UAAhBvB,EAAA,CAAAwB,UAAA,UAAAsK,GAAA,CAAAxG,SAAA,CAAgB;;;;;;;SDgBtBf,kBAAkB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}