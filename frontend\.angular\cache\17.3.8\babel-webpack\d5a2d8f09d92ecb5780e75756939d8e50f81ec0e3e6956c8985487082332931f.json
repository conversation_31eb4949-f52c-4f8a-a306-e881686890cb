{"ast": null, "code": "import { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, forwardRef, numberAttribute, booleanAttribute, Component, ViewEncapsulation, Inject, Input, Output, ViewChild, signal, computed, ChangeDetectionStrategy, ContentChildren, NgModule } from '@angular/core';\nimport * as i2 from '@angular/router';\nimport { RouterModule } from '@angular/router';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { <PERSON><PERSON>and<PERSON> } from 'primeng/dom';\nimport { AngleDownIcon } from 'primeng/icons/angledown';\nimport { AngleRightIcon } from 'primeng/icons/angleright';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport * as i3 from 'primeng/tooltip';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ObjectUtils, UniqueComponentId } from 'primeng/utils';\nconst _c0 = [\"list\"];\nconst _c1 = a0 => ({\n  \"p-submenu-list\": true,\n  \"p-panelmenu-root-list\": a0\n});\nconst _c2 = a0 => ({\n  \"p-disabled\": a0\n});\nconst _c3 = () => ({\n  exact: false\n});\nconst _c4 = a0 => ({\n  $implicit: a0\n});\nfunction PanelMenuSub_ng_template_2_li_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"li\", 7);\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_container_1_ng_container_1_AngleDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleDownIcon\", 21);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(6).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\")(\"ngStyle\", ctx_r3.getItemProp(processedItem_r3, \"iconStyle\"));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_container_1_ng_container_1_AngleRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleRightIcon\", 21);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(6).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\")(\"ngStyle\", ctx_r3.getItemProp(processedItem_r3, \"iconStyle\"));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_container_1_ng_container_1_AngleDownIcon_1_Template, 1, 2, \"AngleDownIcon\", 20)(2, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_container_1_ng_container_1_AngleRightIcon_2_Template, 1, 2, \"AngleRightIcon\", 20);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(5).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isItemActive(processedItem_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.isItemActive(processedItem_r3));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_container_1_2_ng_template_0_Template(rf, ctx) {}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_container_1_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_container_1_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_container_1_ng_container_1_Template, 3, 2, \"ng-container\", 10)(2, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_container_1_2_Template, 1, 0, null, 19);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.panelMenu.submenuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.panelMenu.submenuIconTemplate);\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 22);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", processedItem_r3.icon)(\"ngStyle\", ctx_r3.getItemProp(processedItem_r3, \"iconStyle\"));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r3.getItemProp(processedItem_r3, \"label\"));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 24);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r3.getItemProp(processedItem_r3, \"label\"), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    i0.ɵɵproperty(\"ngClass\", processedItem_r3.badgeStyleClass);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(processedItem_r3.badge);\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 15);\n    i0.ɵɵtemplate(1, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_container_1_Template, 3, 2, \"ng-container\", 10)(2, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_span_2_Template, 1, 2, \"span\", 16)(3, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_span_3_Template, 2, 1, \"span\", 17)(4, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_ng_template_4_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(6, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_span_6_Template, 2, 2, \"span\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlLabel_r5 = i0.ɵɵreference(5);\n    const processedItem_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c2, ctx_r3.getItemProp(processedItem_r3, \"disabled\")))(\"target\", ctx_r3.getItemProp(processedItem_r3, \"target\"));\n    i0.ɵɵattribute(\"href\", ctx_r3.getItemProp(processedItem_r3, \"url\"), i0.ɵɵsanitizeUrl)(\"data-pc-section\", \"action\")(\"tabindex\", !!ctx_r3.parentExpanded ? \"0\" : \"-1\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isItemGroup(processedItem_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", processedItem_r3.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (processedItem_r3.item == null ? null : processedItem_r3.item.escape) !== false)(\"ngIfElse\", htmlLabel_r5);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", processedItem_r3.badge);\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_container_1_ng_container_1_AngleDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleDownIcon\", 21);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(6).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\")(\"ngStyle\", ctx_r3.getItemProp(processedItem_r3, \"iconStyle\"));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_container_1_ng_container_1_AngleRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"AngleRightIcon\", 21);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(6).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\")(\"ngStyle\", ctx_r3.getItemProp(processedItem_r3, \"iconStyle\"));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_container_1_ng_container_1_AngleDownIcon_1_Template, 1, 2, \"AngleDownIcon\", 20)(2, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_container_1_ng_container_1_AngleRightIcon_2_Template, 1, 2, \"AngleRightIcon\", 20);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(5).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isItemActive(processedItem_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.isItemActive(processedItem_r3));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_container_1_2_ng_template_0_Template(rf, ctx) {}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_container_1_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_container_1_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_container_1_ng_container_1_Template, 3, 2, \"ng-container\", 10)(2, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_container_1_2_Template, 1, 0, null, 19);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.panelMenu.submenuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.panelMenu.submenuIconTemplate);\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 22);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", processedItem_r3.icon)(\"ngStyle\", ctx_r3.getItemProp(processedItem_r3, \"iconStyle\"));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r3.getItemProp(processedItem_r3, \"label\"));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 24);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r3.getItemProp(processedItem_r3, \"label\"), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getItemProp(processedItem_r3, \"badgeStyleClass\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r3.getItemProp(processedItem_r3, \"badge\"));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 26);\n    i0.ɵɵtemplate(1, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_container_1_Template, 3, 2, \"ng-container\", 10)(2, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_span_2_Template, 1, 2, \"span\", 16)(3, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_span_3_Template, 2, 1, \"span\", 17)(4, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_ng_template_4_Template, 1, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(6, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_span_6_Template, 2, 2, \"span\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlRouteLabel_r6 = i0.ɵɵreference(5);\n    const processedItem_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", ctx_r3.getItemProp(processedItem_r3, \"routerLink\"))(\"queryParams\", ctx_r3.getItemProp(processedItem_r3, \"queryParams\"))(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", ctx_r3.getItemProp(processedItem_r3, \"routerLinkActiveOptions\") || i0.ɵɵpureFunction0(20, _c3))(\"ngClass\", i0.ɵɵpureFunction1(21, _c2, ctx_r3.getItemProp(processedItem_r3, \"disabled\")))(\"target\", ctx_r3.getItemProp(processedItem_r3, \"target\"))(\"fragment\", ctx_r3.getItemProp(processedItem_r3, \"fragment\"))(\"queryParamsHandling\", ctx_r3.getItemProp(processedItem_r3, \"queryParamsHandling\"))(\"preserveFragment\", ctx_r3.getItemProp(processedItem_r3, \"preserveFragment\"))(\"skipLocationChange\", ctx_r3.getItemProp(processedItem_r3, \"skipLocationChange\"))(\"replaceUrl\", ctx_r3.getItemProp(processedItem_r3, \"replaceUrl\"))(\"state\", ctx_r3.getItemProp(processedItem_r3, \"state\"));\n    i0.ɵɵattribute(\"title\", ctx_r3.getItemProp(processedItem_r3, \"title\"))(\"data-pc-section\", \"action\")(\"tabindex\", !!ctx_r3.parentExpanded ? \"0\" : \"-1\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isItemGroup(processedItem_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", processedItem_r3.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.getItemProp(processedItem_r3, \"escape\") !== false)(\"ngIfElse\", htmlRouteLabel_r6);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", processedItem_r3.badge);\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_1_Template, 7, 12, \"a\", 13)(2, PanelMenuSub_ng_template_2_li_1_ng_container_2_a_2_Template, 7, 23, \"a\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.getItemProp(processedItem_r3, \"routerLink\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.getItemProp(processedItem_r3, \"routerLink\"));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_3_1_ng_template_0_Template(rf, ctx) {}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_3_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PanelMenuSub_ng_template_2_li_1_ng_container_3_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenuSub_ng_template_2_li_1_ng_container_3_1_Template, 1, 0, null, 27);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, processedItem_r3.item));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_p_panelMenuSub_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-panelMenuSub\", 28);\n    i0.ɵɵlistener(\"itemToggle\", function PanelMenuSub_ng_template_2_li_1_p_panelMenuSub_5_Template_p_panelMenuSub_itemToggle_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r3.onItemToggle($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const processedItem_r3 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"id\", ctx_r3.getItemId(processedItem_r3) + \"_list\")(\"panelId\", ctx_r3.panelId)(\"items\", processedItem_r3 == null ? null : processedItem_r3.items)(\"itemTemplate\", ctx_r3.itemTemplate)(\"transitionOptions\", ctx_r3.transitionOptions)(\"focusedItemId\", ctx_r3.focusedItemId)(\"activeItemPath\", ctx_r3.activeItemPath)(\"level\", ctx_r3.level + 1)(\"parentExpanded\", !!ctx_r3.parentExpanded && ctx_r3.isItemExpanded(processedItem_r3));\n  }\n}\nfunction PanelMenuSub_ng_template_2_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 8)(1, \"div\", 9);\n    i0.ɵɵlistener(\"click\", function PanelMenuSub_ng_template_2_li_1_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const processedItem_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onItemClick($event, processedItem_r3));\n    });\n    i0.ɵɵtemplate(2, PanelMenuSub_ng_template_2_li_1_ng_container_2_Template, 3, 2, \"ng-container\", 10)(3, PanelMenuSub_ng_template_2_li_1_ng_container_3_Template, 2, 4, \"ng-container\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 11);\n    i0.ɵɵtemplate(5, PanelMenuSub_ng_template_2_li_1_p_panelMenuSub_5_Template, 1, 9, \"p-panelMenuSub\", 12);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r7 = i0.ɵɵnextContext();\n    const processedItem_r3 = ctx_r7.$implicit;\n    const index_r9 = ctx_r7.index;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r3.getItemProp(processedItem_r3, \"styleClass\"));\n    i0.ɵɵclassProp(\"p-hidden\", processedItem_r3.visible === false)(\"p-focus\", ctx_r3.isItemFocused(processedItem_r3) && !ctx_r3.isItemDisabled(processedItem_r3));\n    i0.ɵɵproperty(\"ngClass\", ctx_r3.getItemClass(processedItem_r3))(\"ngStyle\", ctx_r3.getItemProp(processedItem_r3, \"style\"))(\"pTooltip\", ctx_r3.getItemProp(processedItem_r3, \"tooltip\"))(\"tooltipOptions\", ctx_r3.getItemProp(processedItem_r3, \"tooltipOptions\"));\n    i0.ɵɵattribute(\"id\", ctx_r3.getItemId(processedItem_r3))(\"aria-label\", ctx_r3.getItemProp(processedItem_r3, \"label\"))(\"aria-expanded\", ctx_r3.isItemGroup(processedItem_r3) ? ctx_r3.isItemActive(processedItem_r3) : undefined)(\"aria-level\", ctx_r3.level + 1)(\"aria-setsize\", ctx_r3.getAriaSetSize())(\"aria-posinset\", ctx_r3.getAriaPosInset(index_r9))(\"data-p-disabled\", ctx_r3.isItemDisabled(processedItem_r3));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"@submenu\", ctx_r3.getAnimation(processedItem_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isItemVisible(processedItem_r3) && ctx_r3.isItemGroup(processedItem_r3) && ctx_r3.isItemExpanded(processedItem_r3));\n  }\n}\nfunction PanelMenuSub_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PanelMenuSub_ng_template_2_li_0_Template, 1, 0, \"li\", 5)(1, PanelMenuSub_ng_template_2_li_1_Template, 6, 21, \"li\", 6);\n  }\n  if (rf & 2) {\n    const processedItem_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", processedItem_r3.separator);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !processedItem_r3.separator && ctx_r3.isItemVisible(processedItem_r3));\n  }\n}\nconst _c5 = [\"submenu\"];\nconst _c6 = [\"container\"];\nconst _c7 = (a0, a1) => ({\n  \"p-component p-panelmenu-header\": true,\n  \"p-highlight\": a0,\n  \"p-disabled\": a1\n});\nconst _c8 = a0 => ({\n  \"p-panelmenu-expanded\": a0\n});\nfunction PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_container_1_ng_container_1_ChevronDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronDownIcon\", 20);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_container_1_ng_container_1_ChevronRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\", 20);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_container_1_ng_container_1_ChevronDownIcon_1_Template, 1, 1, \"ChevronDownIcon\", 19)(2, PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_container_1_ng_container_1_ChevronRightIcon_2_Template, 1, 1, \"ChevronRightIcon\", 19);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext(5).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isItemActive(item_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isItemActive(item_r3));\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_container_1_2_ng_template_0_Template(rf, ctx) {}\nfunction PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_container_1_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_container_1_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_container_1_ng_container_1_Template, 3, 2, \"ng-container\", 9)(2, PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_container_1_2_Template, 1, 0, null, 18);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.submenuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r4.submenuIconTemplate);\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_ng_container_3_a_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 21);\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", item_r3.icon)(\"ngStyle\", ctx_r4.getItemProp(item_r3, \"iconStyle\"));\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_ng_container_3_a_1_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r4.getItemProp(item_r3, \"label\"));\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 23);\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r4.getItemProp(item_r3, \"label\"), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_ng_container_3_a_1_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r4.getItemProp(item_r3, \"badgeStyleClass\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r4.getItemProp(item_r3, \"badge\"));\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_ng_container_3_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 14);\n    i0.ɵɵtemplate(1, PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_container_1_Template, 3, 2, \"ng-container\", 9)(2, PanelMenu_ng_container_2_div_1_ng_container_3_a_1_span_2_Template, 1, 2, \"span\", 15)(3, PanelMenu_ng_container_2_div_1_ng_container_3_a_1_span_3_Template, 2, 1, \"span\", 16)(4, PanelMenu_ng_container_2_div_1_ng_container_3_a_1_ng_template_4_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(6, PanelMenu_ng_container_2_div_1_ng_container_3_a_1_span_6_Template, 2, 2, \"span\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlLabel_r6 = i0.ɵɵreference(5);\n    const item_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"target\", ctx_r4.getItemProp(item_r3, \"target\"));\n    i0.ɵɵattribute(\"href\", ctx_r4.getItemProp(item_r3, \"url\"), i0.ɵɵsanitizeUrl)(\"tabindex\", -1)(\"title\", ctx_r4.getItemProp(item_r3, \"title\"))(\"data-pc-section\", \"headeraction\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isItemGroup(item_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.getItemProp(item_r3, \"escape\") !== false)(\"ngIfElse\", htmlLabel_r6);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.getItemProp(item_r3, \"badge\"));\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenu_ng_container_2_div_1_ng_container_3_a_1_Template, 7, 10, \"a\", 13);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.getItemProp(item_r3, \"routerLink\"));\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_a_5_ng_container_1_ng_container_1_ChevronDownIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronDownIcon\", 20);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_a_5_ng_container_1_ng_container_1_ChevronRightIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\", 20);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-submenu-icon\");\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_a_5_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenu_ng_container_2_div_1_a_5_ng_container_1_ng_container_1_ChevronDownIcon_1_Template, 1, 1, \"ChevronDownIcon\", 19)(2, PanelMenu_ng_container_2_div_1_a_5_ng_container_1_ng_container_1_ChevronRightIcon_2_Template, 1, 1, \"ChevronRightIcon\", 19);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext(4).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isItemActive(item_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.isItemActive(item_r3));\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_a_5_ng_container_1_2_ng_template_0_Template(rf, ctx) {}\nfunction PanelMenu_ng_container_2_div_1_a_5_ng_container_1_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PanelMenu_ng_container_2_div_1_a_5_ng_container_1_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_a_5_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenu_ng_container_2_div_1_a_5_ng_container_1_ng_container_1_Template, 3, 2, \"ng-container\", 9)(2, PanelMenu_ng_container_2_div_1_a_5_ng_container_1_2_Template, 1, 0, null, 18);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.submenuIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r4.submenuIconTemplate);\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_a_5_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 21);\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", item_r3.icon)(\"ngStyle\", ctx_r4.getItemProp(item_r3, \"iconStyle\"));\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_a_5_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r4.getItemProp(item_r3, \"label\"));\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_a_5_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 23);\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r4.getItemProp(item_r3, \"label\"), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_a_5_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext(3).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r4.getItemProp(item_r3, \"badgeStyleClass\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r4.getItemProp(item_r3, \"badge\"));\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_a_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 25);\n    i0.ɵɵtemplate(1, PanelMenu_ng_container_2_div_1_a_5_ng_container_1_Template, 3, 2, \"ng-container\", 9)(2, PanelMenu_ng_container_2_div_1_a_5_span_2_Template, 1, 2, \"span\", 15)(3, PanelMenu_ng_container_2_div_1_a_5_span_3_Template, 2, 1, \"span\", 16)(4, PanelMenu_ng_container_2_div_1_a_5_ng_template_4_Template, 1, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(6, PanelMenu_ng_container_2_div_1_a_5_span_6_Template, 2, 2, \"span\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const htmlRouteLabel_r7 = i0.ɵɵreference(5);\n    const item_r3 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"routerLink\", ctx_r4.getItemProp(item_r3, \"routerLink\"))(\"queryParams\", ctx_r4.getItemProp(item_r3, \"queryParams\"))(\"routerLinkActive\", \"p-menuitem-link-active\")(\"routerLinkActiveOptions\", ctx_r4.getItemProp(item_r3, \"routerLinkActiveOptions\") || i0.ɵɵpureFunction0(18, _c3))(\"target\", ctx_r4.getItemProp(item_r3, \"target\"))(\"fragment\", ctx_r4.getItemProp(item_r3, \"fragment\"))(\"queryParamsHandling\", ctx_r4.getItemProp(item_r3, \"queryParamsHandling\"))(\"preserveFragment\", ctx_r4.getItemProp(item_r3, \"preserveFragment\"))(\"skipLocationChange\", ctx_r4.getItemProp(item_r3, \"skipLocationChange\"))(\"replaceUrl\", ctx_r4.getItemProp(item_r3, \"replaceUrl\"))(\"state\", ctx_r4.getItemProp(item_r3, \"state\"));\n    i0.ɵɵattribute(\"tabindex\", -1)(\"data-pc-section\", \"headeraction\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isItemGroup(item_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.getItemProp(item_r3, \"escape\") !== false)(\"ngIfElse\", htmlRouteLabel_r7);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.getItemProp(item_r3, \"badge\"));\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵlistener(\"@rootItem.done\", function PanelMenu_ng_container_2_div_1_div_6_Template_div_animation_rootItem_done_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.onToggleDone());\n    });\n    i0.ɵɵelementStart(1, \"div\", 27)(2, \"p-panelMenuList\", 28);\n    i0.ɵɵlistener(\"headerFocus\", function PanelMenu_ng_container_2_div_1_div_6_Template_p_panelMenuList_headerFocus_2_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r4 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r4.updateFocusedHeader($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    const item_r3 = ctx_r1.$implicit;\n    const i_r4 = ctx_r1.index;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(14, _c8, ctx_r4.isItemActive(item_r3)))(\"@rootItem\", ctx_r4.getAnimation(item_r3));\n    i0.ɵɵattribute(\"id\", ctx_r4.getContentId(item_r3, i_r4))(\"aria-labelledby\", ctx_r4.getHeaderId(item_r3, i_r4))(\"data-pc-section\", \"toggleablecontent\");\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"data-pc-section\", \"menucontent\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"panelId\", ctx_r4.getPanelId(i_r4, item_r3))(\"items\", ctx_r4.getItemProp(item_r3, \"items\"))(\"itemTemplate\", ctx_r4.itemTemplate)(\"transitionOptions\", ctx_r4.transitionOptions)(\"root\", true)(\"activeItem\", ctx_r4.activeItem())(\"tabindex\", ctx_r4.tabindex)(\"parentExpanded\", ctx_r4.isItemActive(item_r3));\n  }\n}\nfunction PanelMenu_ng_container_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7);\n    i0.ɵɵlistener(\"click\", function PanelMenu_ng_container_2_div_1_Template_div_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      const item_r3 = ctx_r1.$implicit;\n      const i_r4 = ctx_r1.index;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onHeaderClick($event, item_r3, i_r4));\n    })(\"keydown\", function PanelMenu_ng_container_2_div_1_Template_div_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      const item_r3 = ctx_r1.$implicit;\n      const i_r4 = ctx_r1.index;\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.onHeaderKeyDown($event, item_r3, i_r4));\n    });\n    i0.ɵɵelementStart(2, \"div\", 8);\n    i0.ɵɵtemplate(3, PanelMenu_ng_container_2_div_1_ng_container_3_Template, 2, 1, \"ng-container\", 9)(4, PanelMenu_ng_container_2_div_1_ng_container_4_Template, 1, 0, \"ng-container\", 10)(5, PanelMenu_ng_container_2_div_1_a_5_Template, 7, 19, \"a\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, PanelMenu_ng_container_2_div_1_div_6_Template, 3, 16, \"div\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const item_r3 = ctx_r1.$implicit;\n    const i_r4 = ctx_r1.index;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r4.getItemProp(item_r3, \"headerClass\"))(\"ngStyle\", ctx_r4.getItemProp(item_r3, \"style\"));\n    i0.ɵɵattribute(\"data-pc-section\", \"panel\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r4.getItemProp(item_r3, \"styleClass\"));\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(23, _c7, ctx_r4.isItemActive(item_r3), ctx_r4.isItemDisabled(item_r3)))(\"ngStyle\", ctx_r4.getItemProp(item_r3, \"style\"))(\"pTooltip\", ctx_r4.getItemProp(item_r3, \"tooltip\"))(\"tabindex\", 0)(\"tooltipOptions\", ctx_r4.getItemProp(item_r3, \"tooltipOptions\"));\n    i0.ɵɵattribute(\"id\", ctx_r4.getHeaderId(item_r3, i_r4))(\"aria-expanded\", ctx_r4.isItemActive(item_r3))(\"aria-label\", ctx_r4.getItemProp(item_r3, \"label\"))(\"aria-controls\", ctx_r4.getContentId(item_r3, i_r4))(\"aria-disabled\", ctx_r4.isItemDisabled(item_r3))(\"data-p-highlight\", ctx_r4.isItemActive(item_r3))(\"data-p-disabled\", ctx_r4.isItemDisabled(item_r3))(\"data-pc-section\", \"header\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.itemTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r4.itemTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(26, _c4, item_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.getItemProp(item_r3, \"routerLink\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isItemGroup(item_r3));\n  }\n}\nfunction PanelMenu_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PanelMenu_ng_container_2_div_1_Template, 7, 28, \"div\", 5);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isItemVisible(item_r3));\n  }\n}\nconst _c9 = \"@layer primeng{.p-panelmenu .p-panelmenu-header-action{display:flex;align-items:center;-webkit-user-select:none;user-select:none;cursor:pointer;position:relative;text-decoration:none}.p-panelmenu .p-panelmenu-header-action:focus{z-index:1}.p-panelmenu .p-submenu-list{margin:0;padding:0;list-style:none}.p-panelmenu .p-menuitem-link{display:flex;align-items:center;-webkit-user-select:none;user-select:none;cursor:pointer;text-decoration:none;position:relative;overflow:hidden;outline:none}.p-panelmenu .p-menuitem-text{line-height:1}.p-panelmenu-expanded.p-toggleable-content:not(.ng-animating),.p-panelmenu .p-submenu-expanded:not(.ng-animating){overflow:visible}.p-panelmenu .p-toggleable-content,.p-panelmenu .p-submenu-list{overflow:hidden}}\\n\";\nlet PanelMenuSub = /*#__PURE__*/(() => {\n  class PanelMenuSub {\n    panelMenu;\n    el;\n    panelId;\n    focusedItemId;\n    items;\n    itemTemplate;\n    level = 0;\n    activeItemPath;\n    root;\n    tabindex;\n    transitionOptions;\n    parentExpanded;\n    itemToggle = new EventEmitter();\n    menuFocus = new EventEmitter();\n    menuBlur = new EventEmitter();\n    menuKeyDown = new EventEmitter();\n    listViewChild;\n    constructor(panelMenu, el) {\n      this.panelMenu = panelMenu;\n      this.el = el;\n    }\n    getItemId(processedItem) {\n      return processedItem.item?.id ?? `${this.panelId}_${processedItem.key}`;\n    }\n    getItemKey(processedItem) {\n      return this.getItemId(processedItem);\n    }\n    getItemClass(processedItem) {\n      return {\n        'p-menuitem': true,\n        'p-disabled': this.isItemDisabled(processedItem)\n      };\n    }\n    getItemProp(processedItem, name, params) {\n      return processedItem && processedItem.item ? ObjectUtils.getItemValue(processedItem.item[name], params) : undefined;\n    }\n    getItemLabel(processedItem) {\n      return this.getItemProp(processedItem, 'label');\n    }\n    isItemExpanded(processedItem) {\n      return processedItem.expanded;\n    }\n    isItemActive(processedItem) {\n      return this.isItemExpanded(processedItem) || this.activeItemPath.some(path => path && path.key === processedItem.key);\n    }\n    isItemVisible(processedItem) {\n      return this.getItemProp(processedItem, 'visible') !== false;\n    }\n    isItemDisabled(processedItem) {\n      return this.getItemProp(processedItem, 'disabled');\n    }\n    isItemFocused(processedItem) {\n      return this.focusedItemId === this.getItemId(processedItem);\n    }\n    isItemGroup(processedItem) {\n      return ObjectUtils.isNotEmpty(processedItem.items);\n    }\n    getAnimation(processedItem) {\n      return this.isItemActive(processedItem) ? {\n        value: 'visible',\n        params: {\n          transitionParams: this.transitionOptions,\n          height: '*'\n        }\n      } : {\n        value: 'hidden',\n        params: {\n          transitionParams: this.transitionOptions,\n          height: '0'\n        }\n      };\n    }\n    getAriaSetSize() {\n      return this.items.filter(processedItem => this.isItemVisible(processedItem) && !this.getItemProp(processedItem, 'separator')).length;\n    }\n    getAriaPosInset(index) {\n      return index - this.items.slice(0, index).filter(processedItem => this.isItemVisible(processedItem) && this.getItemProp(processedItem, 'separator')).length + 1;\n    }\n    onItemClick(event, processedItem) {\n      if (!this.isItemDisabled(processedItem)) {\n        this.getItemProp(processedItem, 'command', {\n          originalEvent: event,\n          item: processedItem.item\n        });\n        this.itemToggle.emit({\n          processedItem,\n          expanded: !this.isItemActive(processedItem)\n        });\n      }\n    }\n    onItemToggle(event) {\n      this.itemToggle.emit(event);\n    }\n    static ɵfac = function PanelMenuSub_Factory(t) {\n      return new (t || PanelMenuSub)(i0.ɵɵdirectiveInject(forwardRef(() => PanelMenu)), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: PanelMenuSub,\n      selectors: [[\"p-panelMenuSub\"]],\n      viewQuery: function PanelMenuSub_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listViewChild = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        panelId: \"panelId\",\n        focusedItemId: \"focusedItemId\",\n        items: \"items\",\n        itemTemplate: \"itemTemplate\",\n        level: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"level\", \"level\", numberAttribute],\n        activeItemPath: \"activeItemPath\",\n        root: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"root\", \"root\", booleanAttribute],\n        tabindex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabindex\", \"tabindex\", numberAttribute],\n        transitionOptions: \"transitionOptions\",\n        parentExpanded: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"parentExpanded\", \"parentExpanded\", booleanAttribute]\n      },\n      outputs: {\n        itemToggle: \"itemToggle\",\n        menuFocus: \"menuFocus\",\n        menuBlur: \"menuBlur\",\n        menuKeyDown: \"menuKeyDown\"\n      },\n      features: [i0.ɵɵInputTransformsFeature],\n      decls: 3,\n      vars: 8,\n      consts: [[\"list\", \"\"], [\"htmlLabel\", \"\"], [\"htmlRouteLabel\", \"\"], [\"role\", \"tree\", 3, \"focusin\", \"focusout\", \"keydown\", \"ngClass\", \"tabindex\"], [\"ngFor\", \"\", 3, \"ngForOf\"], [\"class\", \"p-menuitem-separator\", \"role\", \"separator\", 4, \"ngIf\"], [\"role\", \"treeitem\", 3, \"ngClass\", \"class\", \"p-hidden\", \"p-focus\", \"ngStyle\", \"pTooltip\", \"tooltipOptions\", 4, \"ngIf\"], [\"role\", \"separator\", 1, \"p-menuitem-separator\"], [\"role\", \"treeitem\", 3, \"ngClass\", \"ngStyle\", \"pTooltip\", \"tooltipOptions\"], [1, \"p-menuitem-content\", 3, \"click\"], [4, \"ngIf\"], [1, \"p-toggleable-content\"], [3, \"id\", \"panelId\", \"items\", \"itemTemplate\", \"transitionOptions\", \"focusedItemId\", \"activeItemPath\", \"level\", \"parentExpanded\", \"itemToggle\", 4, \"ngIf\"], [\"class\", \"p-menuitem-link\", 3, \"ngClass\", \"target\", 4, \"ngIf\"], [\"class\", \"p-menuitem-link\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"ngClass\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", 4, \"ngIf\"], [1, \"p-menuitem-link\", 3, \"ngClass\", \"target\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"p-menuitem-badge\", 3, \"ngClass\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [3, \"styleClass\", \"ngStyle\", 4, \"ngIf\"], [3, \"styleClass\", \"ngStyle\"], [1, \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [1, \"p-menuitem-badge\", 3, \"ngClass\"], [1, \"p-menuitem-link\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"ngClass\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"itemToggle\", \"id\", \"panelId\", \"items\", \"itemTemplate\", \"transitionOptions\", \"focusedItemId\", \"activeItemPath\", \"level\", \"parentExpanded\"]],\n      template: function PanelMenuSub_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"ul\", 3, 0);\n          i0.ɵɵlistener(\"focusin\", function PanelMenuSub_Template_ul_focusin_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.menuFocus.emit($event));\n          })(\"focusout\", function PanelMenuSub_Template_ul_focusout_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.menuBlur.emit($event));\n          })(\"keydown\", function PanelMenuSub_Template_ul_keydown_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.menuKeyDown.emit($event));\n          });\n          i0.ɵɵtemplate(2, PanelMenuSub_ng_template_2_Template, 2, 2, \"ng-template\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c1, ctx.root))(\"tabindex\", -1);\n          i0.ɵɵattribute(\"aria-activedescendant\", ctx.focusedItemId)(\"data-pc-section\", \"menu\")(\"aria-hidden\", !ctx.parentExpanded);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.items);\n        }\n      },\n      dependencies: () => [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.RouterLink, i2.RouterLinkActive, i3.Tooltip, AngleDownIcon, AngleRightIcon, PanelMenuSub],\n      encapsulation: 2,\n      data: {\n        animation: [trigger('submenu', [state('hidden', style({\n          height: '0'\n        })), state('visible', style({\n          height: '*'\n        })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => *', animate(0))])]\n      }\n    });\n  }\n  return PanelMenuSub;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet PanelMenuList = /*#__PURE__*/(() => {\n  class PanelMenuList {\n    el;\n    panelId;\n    id;\n    items;\n    itemTemplate;\n    parentExpanded;\n    expanded;\n    transitionOptions;\n    root;\n    tabindex;\n    activeItem;\n    itemToggle = new EventEmitter();\n    headerFocus = new EventEmitter();\n    subMenuViewChild;\n    searchTimeout;\n    searchValue;\n    focused;\n    focusedItem = signal(null);\n    activeItemPath = signal([]);\n    processedItems = signal([]);\n    visibleItems = computed(() => {\n      const processedItems = this.processedItems();\n      return this.flatItems(processedItems);\n    });\n    get focusedItemId() {\n      const focusedItem = this.focusedItem();\n      return focusedItem && focusedItem.item?.id ? focusedItem.item.id : ObjectUtils.isNotEmpty(this.focusedItem()) ? `${this.panelId}_${this.focusedItem().key}` : undefined;\n    }\n    constructor(el) {\n      this.el = el;\n    }\n    ngOnChanges(changes) {\n      this.processedItems.set(this.createProcessedItems(changes?.items?.currentValue || this.items || []));\n    }\n    getItemProp(processedItem, name) {\n      return processedItem && processedItem.item ? ObjectUtils.getItemValue(processedItem.item[name]) : undefined;\n    }\n    getItemLabel(processedItem) {\n      return this.getItemProp(processedItem, 'label');\n    }\n    isItemVisible(processedItem) {\n      return this.getItemProp(processedItem, 'visible') !== false;\n    }\n    isItemDisabled(processedItem) {\n      return this.getItemProp(processedItem, 'disabled');\n    }\n    isItemActive(processedItem) {\n      return this.activeItemPath().some(path => path.key === processedItem.parentKey);\n    }\n    isItemGroup(processedItem) {\n      return ObjectUtils.isNotEmpty(processedItem.items);\n    }\n    isElementInPanel(event, element) {\n      const panel = event.currentTarget.closest('[data-pc-section=\"panel\"]');\n      return panel && panel.contains(element);\n    }\n    isItemMatched(processedItem) {\n      return this.isValidItem(processedItem) && this.getItemLabel(processedItem).toLocaleLowerCase().startsWith(this.searchValue.toLocaleLowerCase());\n    }\n    isVisibleItem(processedItem) {\n      return !!processedItem && (processedItem.level === 0 || this.isItemActive(processedItem)) && this.isItemVisible(processedItem);\n    }\n    isValidItem(processedItem) {\n      return !!processedItem && !this.isItemDisabled(processedItem) && !processedItem.separator;\n    }\n    findFirstItem() {\n      return this.visibleItems().find(processedItem => this.isValidItem(processedItem));\n    }\n    findLastItem() {\n      return ObjectUtils.findLast(this.visibleItems(), processedItem => this.isValidItem(processedItem));\n    }\n    findItemByEventTarget(target) {\n      let parentNode = target;\n      while (parentNode && parentNode.tagName?.toLowerCase() !== 'li') {\n        parentNode = parentNode?.parentNode;\n      }\n      return parentNode?.id && this.visibleItems().find(processedItem => this.isValidItem(processedItem) && `${this.panelId}_${processedItem.key}` === parentNode.id);\n    }\n    createProcessedItems(items, level = 0, parent = {}, parentKey = '') {\n      const processedItems = [];\n      items && items.forEach((item, index) => {\n        const key = (parentKey !== '' ? parentKey + '_' : '') + index;\n        const newItem = {\n          icon: item.icon,\n          expanded: item.expanded,\n          separator: item.separator,\n          item,\n          index,\n          level,\n          key,\n          parent,\n          parentKey\n        };\n        newItem['items'] = this.createProcessedItems(item.items, level + 1, newItem, key);\n        processedItems.push(newItem);\n      });\n      return processedItems;\n    }\n    findProcessedItemByItemKey(key, processedItems, level = 0) {\n      processedItems = processedItems || this.processedItems();\n      if (processedItems && processedItems.length) {\n        for (let i = 0; i < processedItems.length; i++) {\n          const processedItem = processedItems[i];\n          if (this.getItemProp(processedItem, 'key') === key) return processedItem;\n          const matchedItem = this.findProcessedItemByItemKey(key, processedItem.items, level + 1);\n          if (matchedItem) return matchedItem;\n        }\n      }\n    }\n    flatItems(processedItems, processedFlattenItems = []) {\n      processedItems && processedItems.forEach(processedItem => {\n        if (this.isVisibleItem(processedItem)) {\n          processedFlattenItems.push(processedItem);\n          this.flatItems(processedItem.items, processedFlattenItems);\n        }\n      });\n      return processedFlattenItems;\n    }\n    changeFocusedItem(event) {\n      const {\n        originalEvent,\n        processedItem,\n        focusOnNext,\n        selfCheck,\n        allowHeaderFocus = true\n      } = event;\n      if (ObjectUtils.isNotEmpty(this.focusedItem()) && this.focusedItem().key !== processedItem.key) {\n        this.focusedItem.set(processedItem);\n        this.scrollInView();\n      } else if (allowHeaderFocus) {\n        this.headerFocus.emit({\n          originalEvent,\n          focusOnNext,\n          selfCheck\n        });\n      }\n    }\n    scrollInView() {\n      const element = DomHandler.findSingle(this.subMenuViewChild.listViewChild.nativeElement, `li[id=\"${`${this.focusedItemId}`}\"]`);\n      if (element) {\n        element.scrollIntoView && element.scrollIntoView({\n          block: 'nearest',\n          inline: 'nearest'\n        });\n      }\n    }\n    onFocus(event) {\n      if (!this.focused) {\n        this.focused = true;\n        const focusedItem = this.focusedItem() || (this.isElementInPanel(event, event.relatedTarget) ? this.findItemByEventTarget(event.target) || this.findFirstItem() : this.findLastItem());\n        if (event.relatedTarget !== null) this.focusedItem.set(focusedItem);\n      }\n    }\n    onBlur(event) {\n      const target = event.relatedTarget;\n      if (this.focused && !this.el.nativeElement.contains(target)) {\n        this.focused = false;\n        this.focusedItem.set(null);\n        this.searchValue = '';\n      }\n    }\n    onItemToggle(event) {\n      const {\n        processedItem,\n        expanded\n      } = event;\n      processedItem.expanded = !processedItem.expanded;\n      const activeItemPath = this.activeItemPath().filter(p => p.parentKey !== processedItem.parentKey);\n      expanded && activeItemPath.push(processedItem);\n      this.activeItemPath.set(activeItemPath);\n      this.processedItems.update(value => value.map(i => i === processedItem ? processedItem : i));\n      this.focusedItem.set(processedItem);\n    }\n    onKeyDown(event) {\n      const metaKey = event.metaKey || event.ctrlKey;\n      switch (event.code) {\n        case 'ArrowDown':\n          this.onArrowDownKey(event);\n          break;\n        case 'ArrowUp':\n          this.onArrowUpKey(event);\n          break;\n        case 'ArrowLeft':\n          this.onArrowLeftKey(event);\n          break;\n        case 'ArrowRight':\n          this.onArrowRightKey(event);\n          break;\n        case 'Home':\n          this.onHomeKey(event);\n          break;\n        case 'End':\n          this.onEndKey(event);\n          break;\n        case 'Space':\n          this.onSpaceKey(event);\n          break;\n        case 'Enter':\n          this.onEnterKey(event);\n          break;\n        case 'Escape':\n        case 'Tab':\n        case 'PageDown':\n        case 'PageUp':\n        case 'Backspace':\n        case 'ShiftLeft':\n        case 'ShiftRight':\n          //NOOP\n          break;\n        default:\n          if (!metaKey && ObjectUtils.isPrintableCharacter(event.key)) {\n            this.searchItems(event, event.key);\n          }\n          break;\n      }\n    }\n    onArrowDownKey(event) {\n      const processedItem = ObjectUtils.isNotEmpty(this.focusedItem()) ? this.findNextItem(this.focusedItem()) : this.findFirstItem();\n      this.changeFocusedItem({\n        originalEvent: event,\n        processedItem,\n        focusOnNext: true\n      });\n      event.preventDefault();\n    }\n    onArrowUpKey(event) {\n      const processedItem = ObjectUtils.isNotEmpty(this.focusedItem()) ? this.findPrevItem(this.focusedItem()) : this.findLastItem();\n      this.changeFocusedItem({\n        originalEvent: event,\n        processedItem,\n        selfCheck: true\n      });\n      event.preventDefault();\n    }\n    onArrowLeftKey(event) {\n      if (ObjectUtils.isNotEmpty(this.focusedItem())) {\n        const matched = this.activeItemPath().some(p => p.key === this.focusedItem().key);\n        if (matched) {\n          const activeItemPath = this.activeItemPath().filter(p => p.key !== this.focusedItem().key);\n          this.activeItemPath.set(activeItemPath);\n        } else {\n          const focusedItem = ObjectUtils.isNotEmpty(this.focusedItem().parent) ? this.focusedItem().parent : this.focusedItem();\n          this.focusedItem.set(focusedItem);\n        }\n        event.preventDefault();\n      }\n    }\n    onArrowRightKey(event) {\n      if (ObjectUtils.isNotEmpty(this.focusedItem())) {\n        const grouped = this.isItemGroup(this.focusedItem());\n        if (grouped) {\n          const matched = this.activeItemPath().some(p => p.key === this.focusedItem().key);\n          if (matched) {\n            this.onArrowDownKey(event);\n          } else {\n            const activeItemPath = this.activeItemPath().filter(p => p.parentKey !== this.focusedItem().parentKey);\n            activeItemPath.push(this.focusedItem());\n            this.activeItemPath.set(activeItemPath);\n          }\n        }\n        event.preventDefault();\n      }\n    }\n    onHomeKey(event) {\n      this.changeFocusedItem({\n        originalEvent: event,\n        processedItem: this.findFirstItem(),\n        allowHeaderFocus: false\n      });\n      event.preventDefault();\n    }\n    onEndKey(event) {\n      this.changeFocusedItem({\n        originalEvent: event,\n        processedItem: this.findLastItem(),\n        focusOnNext: true,\n        allowHeaderFocus: false\n      });\n      event.preventDefault();\n    }\n    onEnterKey(event) {\n      if (ObjectUtils.isNotEmpty(this.focusedItem())) {\n        const element = DomHandler.findSingle(this.subMenuViewChild.listViewChild.nativeElement, `li[id=\"${`${this.focusedItemId}`}\"]`);\n        const anchorElement = element && (DomHandler.findSingle(element, '[data-pc-section=\"action\"]') || DomHandler.findSingle(element, 'a,button'));\n        anchorElement ? anchorElement.click() : element && element.click();\n      }\n      event.preventDefault();\n    }\n    onSpaceKey(event) {\n      this.onEnterKey(event);\n    }\n    findNextItem(processedItem) {\n      const index = this.visibleItems().findIndex(item => item.key === processedItem.key);\n      const matchedItem = index < this.visibleItems().length - 1 ? this.visibleItems().slice(index + 1).find(pItem => this.isValidItem(pItem)) : undefined;\n      return matchedItem || processedItem;\n    }\n    findPrevItem(processedItem) {\n      const index = this.visibleItems().findIndex(item => item.key === processedItem.key);\n      const matchedItem = index > 0 ? ObjectUtils.findLast(this.visibleItems().slice(0, index), pItem => this.isValidItem(pItem)) : undefined;\n      return matchedItem || processedItem;\n    }\n    searchItems(event, char) {\n      this.searchValue = (this.searchValue || '') + char;\n      let matchedItem = null;\n      let matched = false;\n      if (ObjectUtils.isNotEmpty(this.focusedItem())) {\n        const focusedItemIndex = this.visibleItems().findIndex(processedItem => processedItem.key === this.focusedItem().key);\n        matchedItem = this.visibleItems().slice(focusedItemIndex).find(processedItem => this.isItemMatched(processedItem));\n        matchedItem = ObjectUtils.isEmpty(matchedItem) ? this.visibleItems().slice(0, focusedItemIndex).find(processedItem => this.isItemMatched(processedItem)) : matchedItem;\n      } else {\n        matchedItem = this.visibleItems().find(processedItem => this.isItemMatched(processedItem));\n      }\n      if (ObjectUtils.isNotEmpty(matchedItem)) {\n        matched = true;\n      }\n      if (ObjectUtils.isEmpty(matchedItem) && ObjectUtils.isEmpty(this.focusedItem())) {\n        matchedItem = this.findFirstItem();\n      }\n      if (ObjectUtils.isNotEmpty(matchedItem)) {\n        this.changeFocusedItem({\n          originalEvent: event,\n          processedItem: matchedItem,\n          allowHeaderFocus: false\n        });\n      }\n      if (this.searchTimeout) {\n        clearTimeout(this.searchTimeout);\n      }\n      this.searchTimeout = setTimeout(() => {\n        this.searchValue = '';\n        this.searchTimeout = null;\n      }, 500);\n      return matched;\n    }\n    static ɵfac = function PanelMenuList_Factory(t) {\n      return new (t || PanelMenuList)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: PanelMenuList,\n      selectors: [[\"p-panelMenuList\"]],\n      viewQuery: function PanelMenuList_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c5, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.subMenuViewChild = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        panelId: \"panelId\",\n        id: \"id\",\n        items: \"items\",\n        itemTemplate: \"itemTemplate\",\n        parentExpanded: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"parentExpanded\", \"parentExpanded\", booleanAttribute],\n        expanded: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"expanded\", \"expanded\", booleanAttribute],\n        transitionOptions: \"transitionOptions\",\n        root: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"root\", \"root\", booleanAttribute],\n        tabindex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabindex\", \"tabindex\", numberAttribute],\n        activeItem: \"activeItem\"\n      },\n      outputs: {\n        itemToggle: \"itemToggle\",\n        headerFocus: \"headerFocus\"\n      },\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature],\n      decls: 2,\n      vars: 10,\n      consts: [[\"submenu\", \"\"], [3, \"itemToggle\", \"keydown\", \"menuFocus\", \"menuBlur\", \"root\", \"id\", \"panelId\", \"tabindex\", \"itemTemplate\", \"focusedItemId\", \"activeItemPath\", \"transitionOptions\", \"items\", \"parentExpanded\"]],\n      template: function PanelMenuList_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"p-panelMenuSub\", 1, 0);\n          i0.ɵɵlistener(\"itemToggle\", function PanelMenuList_Template_p_panelMenuSub_itemToggle_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onItemToggle($event));\n          })(\"keydown\", function PanelMenuList_Template_p_panelMenuSub_keydown_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onKeyDown($event));\n          })(\"menuFocus\", function PanelMenuList_Template_p_panelMenuSub_menuFocus_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFocus($event));\n          })(\"menuBlur\", function PanelMenuList_Template_p_panelMenuSub_menuBlur_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBlur($event));\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"root\", true)(\"id\", ctx.panelId + \"_list\")(\"panelId\", ctx.panelId)(\"tabindex\", ctx.tabindex)(\"itemTemplate\", ctx.itemTemplate)(\"focusedItemId\", ctx.focused ? ctx.focusedItemId : undefined)(\"activeItemPath\", ctx.activeItemPath())(\"transitionOptions\", ctx.transitionOptions)(\"items\", ctx.processedItems())(\"parentExpanded\", ctx.parentExpanded);\n        }\n      },\n      dependencies: [PanelMenuSub],\n      styles: [\"@layer primeng{.p-panelmenu .p-panelmenu-header-action{display:flex;align-items:center;-webkit-user-select:none;user-select:none;cursor:pointer;position:relative;text-decoration:none}.p-panelmenu .p-panelmenu-header-action:focus{z-index:1}.p-panelmenu .p-submenu-list{margin:0;padding:0;list-style:none}.p-panelmenu .p-menuitem-link{display:flex;align-items:center;-webkit-user-select:none;user-select:none;cursor:pointer;text-decoration:none;position:relative;overflow:hidden;outline:none}.p-panelmenu .p-menuitem-text{line-height:1}.p-panelmenu-expanded.p-toggleable-content:not(.ng-animating),.p-panelmenu .p-submenu-expanded:not(.ng-animating){overflow:visible}.p-panelmenu .p-toggleable-content,.p-panelmenu .p-submenu-list{overflow:hidden}}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return PanelMenuList;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * PanelMenu is a hybrid of Accordion and Tree components.\n * @group Components\n */\nlet PanelMenu = /*#__PURE__*/(() => {\n  class PanelMenu {\n    cd;\n    /**\n     * An array of menuitems.\n     * @group Props\n     */\n    model;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Whether multiple tabs can be activated at the same time or not.\n     * @group Props\n     */\n    multiple = false;\n    /**\n     * Transition options of the animation.\n     * @group Props\n     */\n    transitionOptions = '400ms cubic-bezier(0.86, 0, 0.07, 1)';\n    /**\n     * Current id state as a string.\n     * @group Props\n     */\n    id;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    tabindex = 0;\n    templates;\n    containerViewChild;\n    submenuIconTemplate;\n    itemTemplate;\n    animating;\n    activeItem = signal(null);\n    ngOnInit() {\n      this.id = this.id || UniqueComponentId();\n    }\n    ngAfterContentInit() {\n      this.templates?.forEach(item => {\n        switch (item.getType()) {\n          case 'submenuicon':\n            this.submenuIconTemplate = item.template;\n            break;\n          case 'item':\n            this.itemTemplate = item.template;\n            break;\n          default:\n            this.itemTemplate = item.template;\n            break;\n        }\n      });\n    }\n    constructor(cd) {\n      this.cd = cd;\n    }\n    /**\n     * Collapses open panels.\n     * @group Method\n     */\n    collapseAll() {\n      for (let item of this.model) {\n        if (item.expanded) {\n          item.expanded = false;\n        }\n      }\n      this.cd.detectChanges();\n    }\n    onToggleDone() {\n      this.animating = false;\n      this.cd.markForCheck();\n    }\n    changeActiveItem(event, item, index, selfActive = false) {\n      if (!this.isItemDisabled(item)) {\n        const activeItem = selfActive ? item : this.activeItem && ObjectUtils.equals(item, this.activeItem) ? null : item;\n        this.activeItem.set(activeItem);\n      }\n    }\n    getAnimation(item) {\n      return item.expanded ? {\n        value: 'visible',\n        params: {\n          transitionParams: this.animating ? this.transitionOptions : '0ms',\n          height: '*'\n        }\n      } : {\n        value: 'hidden',\n        params: {\n          transitionParams: this.transitionOptions,\n          height: '0'\n        }\n      };\n    }\n    getItemProp(item, name) {\n      return item ? ObjectUtils.getItemValue(item[name]) : undefined;\n    }\n    getItemLabel(item) {\n      return this.getItemProp(item, 'label');\n    }\n    isItemActive(item) {\n      return item.expanded;\n    }\n    isItemVisible(item) {\n      return this.getItemProp(item, 'visible') !== false;\n    }\n    isItemDisabled(item) {\n      return this.getItemProp(item, 'disabled');\n    }\n    isItemGroup(item) {\n      return ObjectUtils.isNotEmpty(item.items);\n    }\n    getPanelId(index, item) {\n      return item && item.id ? item.id : `${this.id}_${index}`;\n    }\n    getHeaderId(item, index) {\n      return item.id ? item.id + '_header' : `${this.getPanelId(index)}_header`;\n    }\n    getContentId(item, index) {\n      return item.id ? item.id + '_content' : `${this.getPanelId(index)}_content`;\n    }\n    updateFocusedHeader(event) {\n      const {\n        originalEvent,\n        focusOnNext,\n        selfCheck\n      } = event;\n      const panelElement = originalEvent.currentTarget.closest('[data-pc-section=\"panel\"]');\n      const header = selfCheck ? DomHandler.findSingle(panelElement, '[data-pc-section=\"header\"]') : focusOnNext ? this.findNextHeader(panelElement) : this.findPrevHeader(panelElement);\n      header ? this.changeFocusedHeader(originalEvent, header) : focusOnNext ? this.onHeaderHomeKey(originalEvent) : this.onHeaderEndKey(originalEvent);\n    }\n    changeFocusedHeader(event, element) {\n      element && DomHandler.focus(element);\n    }\n    findNextHeader(panelElement, selfCheck = false) {\n      const nextPanelElement = selfCheck ? panelElement : panelElement.nextElementSibling;\n      const headerElement = DomHandler.findSingle(nextPanelElement, '[data-pc-section=\"header\"]');\n      return headerElement ? DomHandler.getAttribute(headerElement, 'data-p-disabled') ? this.findNextHeader(headerElement.parentElement) : headerElement : null;\n    }\n    findPrevHeader(panelElement, selfCheck = false) {\n      const prevPanelElement = selfCheck ? panelElement : panelElement.previousElementSibling;\n      const headerElement = DomHandler.findSingle(prevPanelElement, '[data-pc-section=\"header\"]');\n      return headerElement ? DomHandler.getAttribute(headerElement, 'data-p-disabled') ? this.findPrevHeader(headerElement.parentElement) : headerElement : null;\n    }\n    findFirstHeader() {\n      return this.findNextHeader(this.containerViewChild.nativeElement.firstElementChild, true);\n    }\n    findLastHeader() {\n      return this.findPrevHeader(this.containerViewChild.nativeElement.lastElementChild, true);\n    }\n    onHeaderClick(event, item, index) {\n      if (this.isItemDisabled(item)) {\n        event.preventDefault();\n        return;\n      }\n      if (item.command) {\n        item.command({\n          originalEvent: event,\n          item\n        });\n      }\n      if (!this.multiple) {\n        for (let modelItem of this.model) {\n          if (item !== modelItem && modelItem.expanded) {\n            modelItem.expanded = false;\n          }\n        }\n      }\n      item.expanded = !item.expanded;\n      this.changeActiveItem(event, item, index);\n      this.animating = true;\n      DomHandler.focus(event.currentTarget);\n    }\n    onHeaderKeyDown(event, item, index) {\n      switch (event.code) {\n        case 'ArrowDown':\n          this.onHeaderArrowDownKey(event);\n          break;\n        case 'ArrowUp':\n          this.onHeaderArrowUpKey(event);\n          break;\n        case 'Home':\n          this.onHeaderHomeKey(event);\n          break;\n        case 'End':\n          this.onHeaderEndKey(event);\n          break;\n        case 'Enter':\n        case 'Space':\n          this.onHeaderEnterKey(event, item, index);\n          break;\n        default:\n          break;\n      }\n    }\n    onHeaderArrowDownKey(event) {\n      const rootList = DomHandler.getAttribute(event.currentTarget, 'data-p-highlight') === true ? DomHandler.findSingle(event.currentTarget.nextElementSibling, '[data-pc-section=\"menu\"]') : null;\n      rootList ? DomHandler.focus(rootList) : this.updateFocusedHeader({\n        originalEvent: event,\n        focusOnNext: true\n      });\n      event.preventDefault();\n    }\n    onHeaderArrowUpKey(event) {\n      const prevHeader = this.findPrevHeader(event.currentTarget.parentElement) || this.findLastHeader();\n      const rootList = DomHandler.getAttribute(prevHeader, 'data-p-highlight') === true ? DomHandler.findSingle(prevHeader.nextElementSibling, '[data-pc-section=\"menu\"]') : null;\n      rootList ? DomHandler.focus(rootList) : this.updateFocusedHeader({\n        originalEvent: event,\n        focusOnNext: false\n      });\n      event.preventDefault();\n    }\n    onHeaderHomeKey(event) {\n      this.changeFocusedHeader(event, this.findFirstHeader());\n      event.preventDefault();\n    }\n    onHeaderEndKey(event) {\n      this.changeFocusedHeader(event, this.findLastHeader());\n      event.preventDefault();\n    }\n    onHeaderEnterKey(event, item, index) {\n      const headerAction = DomHandler.findSingle(event.currentTarget, '[data-pc-section=\"headeraction\"]');\n      headerAction ? headerAction.click() : this.onHeaderClick(event, item, index);\n      event.preventDefault();\n    }\n    static ɵfac = function PanelMenu_Factory(t) {\n      return new (t || PanelMenu)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: PanelMenu,\n      selectors: [[\"p-panelMenu\"]],\n      contentQueries: function PanelMenu_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function PanelMenu_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c6, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        model: \"model\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        multiple: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"multiple\", \"multiple\", booleanAttribute],\n        transitionOptions: \"transitionOptions\",\n        id: \"id\",\n        tabindex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabindex\", \"tabindex\", numberAttribute]\n      },\n      features: [i0.ɵɵInputTransformsFeature],\n      decls: 3,\n      vars: 5,\n      consts: [[\"container\", \"\"], [\"htmlLabel\", \"\"], [\"htmlRouteLabel\", \"\"], [3, \"ngStyle\", \"ngClass\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"p-panelmenu-panel\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [1, \"p-panelmenu-panel\", 3, \"ngClass\", \"ngStyle\"], [\"role\", \"button\", 3, \"click\", \"keydown\", \"ngClass\", \"ngStyle\", \"pTooltip\", \"tabindex\", \"tooltipOptions\"], [1, \"p-panelmenu-header-content\"], [4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"class\", \"p-panelmenu-header-action\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\", 4, \"ngIf\"], [\"class\", \"p-toggleable-content\", \"role\", \"region\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"p-panelmenu-header-action\", 3, \"target\", 4, \"ngIf\"], [1, \"p-panelmenu-header-action\", 3, \"target\"], [\"class\", \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\", 4, \"ngIf\"], [\"class\", \"p-menuitem-text\", 4, \"ngIf\", \"ngIfElse\"], [\"class\", \"p-menuitem-badge\", 3, \"ngClass\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\"], [3, \"styleClass\", 4, \"ngIf\"], [3, \"styleClass\"], [1, \"p-menuitem-icon\", 3, \"ngClass\", \"ngStyle\"], [1, \"p-menuitem-text\"], [1, \"p-menuitem-text\", 3, \"innerHTML\"], [1, \"p-menuitem-badge\", 3, \"ngClass\"], [1, \"p-panelmenu-header-action\", 3, \"routerLink\", \"queryParams\", \"routerLinkActive\", \"routerLinkActiveOptions\", \"target\", \"fragment\", \"queryParamsHandling\", \"preserveFragment\", \"skipLocationChange\", \"replaceUrl\", \"state\"], [\"role\", \"region\", 1, \"p-toggleable-content\", 3, \"ngClass\"], [1, \"p-panelmenu-content\"], [3, \"headerFocus\", \"panelId\", \"items\", \"itemTemplate\", \"transitionOptions\", \"root\", \"activeItem\", \"tabindex\", \"parentExpanded\"]],\n      template: function PanelMenu_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 3, 0);\n          i0.ɵɵtemplate(2, PanelMenu_ng_container_2_Template, 2, 1, \"ng-container\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"ngClass\", \"p-panelmenu p-component\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.model);\n        }\n      },\n      dependencies: () => [i1.NgClass, i1.NgForOf, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle, i2.RouterLink, i2.RouterLinkActive, i3.Tooltip, ChevronDownIcon, ChevronRightIcon, PanelMenuList],\n      styles: [_c9],\n      encapsulation: 2,\n      data: {\n        animation: [trigger('rootItem', [state('hidden', style({\n          height: '0'\n        })), state('visible', style({\n          height: '*'\n        })), transition('visible <=> hidden', [animate('{{transitionParams}}')]), transition('void => *', animate(0))])]\n      },\n      changeDetection: 0\n    });\n  }\n  return PanelMenu;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet PanelMenuModule = /*#__PURE__*/(() => {\n  class PanelMenuModule {\n    static ɵfac = function PanelMenuModule_Factory(t) {\n      return new (t || PanelMenuModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: PanelMenuModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, RouterModule, TooltipModule, SharedModule, AngleDownIcon, AngleRightIcon, ChevronDownIcon, ChevronRightIcon, RouterModule, TooltipModule, SharedModule]\n    });\n  }\n  return PanelMenuModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { PanelMenu, PanelMenuList, PanelMenuModule, PanelMenuSub };\n//# sourceMappingURL=primeng-panelmenu.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}