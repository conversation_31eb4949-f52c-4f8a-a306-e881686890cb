{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\n//this will take a request and allows us to continue\n//we manipulating an incoming request and we are adding our token in the autorization header\nexport class AuthInterceptor {\n  constructor(authService) {\n    this.authService = authService;\n  }\n  intercept(req, next) {\n    const authToken = this.authService.getToken();\n    //manipualte the req to hold the token\n    const authRequest = req.clone({});\n    return next.handle(authRequest);\n  }\n  static #_ = this.ɵfac = function AuthInterceptor_Factory(t) {\n    return new (t || AuthInterceptor)(i0.ɵɵinject(i1.AuthService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthInterceptor,\n    factory: AuthInterceptor.ɵfac\n  });\n}", "map": {"version": 3, "names": ["AuthInterceptor", "constructor", "authService", "intercept", "req", "next", "authToken", "getToken", "authRequest", "clone", "handle", "_", "i0", "ɵɵinject", "i1", "AuthService", "_2", "factory", "ɵfac"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\interceptors\\auth-interceptor.ts"], "sourcesContent": ["import { <PERSON>tt<PERSON><PERSON><PERSON><PERSON>, <PERSON>ttpInterceptor, HttpRequest } from \"@angular/common/http\";\r\nimport { Injectable } from \"@angular/core\";\r\nimport { AuthService } from \"../services/auth.service\";\r\n\r\n@Injectable()//so we can inject services into that service\r\n//this will take a request and allows us to continue\r\n//we manipulating an incoming request and we are adding our token in the autorization header\r\nexport class AuthInterceptor implements HttpInterceptor {\r\n    constructor(private authService: AuthService) { }\r\n\r\n    intercept(req: HttpRequest<any>, next: HttpHandler) {\r\n        const authToken = this.authService.getToken();\r\n        //manipualte the req to hold the token\r\n        const authRequest = req.clone({\r\n        });\r\n        return next.handle(authRequest);\r\n    }\r\n}"], "mappings": ";;AAKA;AACA;AACA,OAAM,MAAOA,eAAe;EACxBC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;EAAiB;EAEhDC,SAASA,CAACC,GAAqB,EAAEC,IAAiB;IAC9C,MAAMC,SAAS,GAAG,IAAI,CAACJ,WAAW,CAACK,QAAQ,EAAE;IAC7C;IACA,MAAMC,WAAW,GAAGJ,GAAG,CAACK,KAAK,CAAC,EAC7B,CAAC;IACF,OAAOJ,IAAI,CAACK,MAAM,CAACF,WAAW,CAAC;EACnC;EAAC,QAAAG,CAAA,G;qBATQX,eAAe,EAAAY,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAfhB,eAAe;IAAAiB,OAAA,EAAfjB,eAAe,CAAAkB;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}