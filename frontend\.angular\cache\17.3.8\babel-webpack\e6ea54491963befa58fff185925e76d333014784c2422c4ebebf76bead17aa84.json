{"ast": null, "code": "import { take } from 'rxjs/operators';\nimport { UserRole } from 'src/app/core/models/user.model';\nimport { SubSink } from 'subsink';\nimport { DialogService } from 'primeng/dynamicdialog';\nimport { LibraryFileActionsDialogComponent } from '../library-file-actions-dialog/library-file-actions-dialog.component';\nimport { FilterPipe } from 'src/app/core/pipes/filter.pipe';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/library.service\";\nimport * as i2 from \"src/app/core/services/classroom.service\";\nimport * as i3 from \"src/app/core/services/general.service\";\nimport * as i4 from \"src/app/core/services/auth.service\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"src/app/core/services/toast.service\";\nimport * as i7 from \"src/app/core/services/layout.service\";\nimport * as i8 from \"primeng/dynamicdialog\";\nimport * as i9 from \"src/app/core/services/user.service\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"../../../../../shared/classrooms-check-list/classrooms-check-list.component\";\nimport * as i12 from \"../../../../../shared/prime/confirm-dialog/confirm-dialog.component\";\nimport * as i13 from \"../../../../../shared/block-viewer/block-viewer.component\";\nimport * as i14 from \"primeng/tabview\";\nimport * as i15 from \"@angular/forms\";\nimport * as i16 from \"../library-file-row-item/library-file-row-item.component\";\nimport * as i17 from \"../single-library-folder-item/single-library-folder-item.component\";\nimport * as i18 from \"src/app/core/pipes/filter.pipe\";\nconst _c0 = [\"addLibrary\"];\nconst _c1 = [\"leftSide\"];\nconst _c2 = [\"mainWrapper\"];\nconst _c3 = a0 => ({\n  \"no-visibility\": a0\n});\nconst _c4 = a0 => ({\n  classroom: a0\n});\nconst _c5 = a0 => ({\n  classroom: a0,\n  uniqueFiles: true\n});\nconst _c6 = (a0, a1) => ({\n  classroom: a0,\n  student: a1\n});\nconst _c7 = a0 => ({\n  \"more\": a0\n});\nconst _c8 = a0 => ({\n  \"purple-folder-bg\": a0\n});\nconst _c9 = a0 => ({\n  \"filter-blue\": a0\n});\nconst _c10 = a0 => ({\n  term: a0\n});\nfunction SingleLibraryComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-confirm-dialog\", 8);\n    i0.ɵɵlistener(\"rejectSelected\", function SingleLibraryComponent_ng_container_0_Template_app_confirm_dialog_rejectSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onRejectFolderDelete($event));\n    })(\"confirmSelected\", function SingleLibraryComponent_ng_container_0_Template_app_confirm_dialog_confirmSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onAcceptFolderDelete($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"confirmMessage\", ctx_r2.deleteFolderConfirmData.message);\n  }\n}\nfunction SingleLibraryComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-confirm-dialog\", 9);\n    i0.ɵɵlistener(\"rejectSelected\", function SingleLibraryComponent_ng_container_1_Template_app_confirm_dialog_rejectSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onRejectFileDelete($event));\n    })(\"confirmSelected\", function SingleLibraryComponent_ng_container_1_Template_app_confirm_dialog_confirmSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onAcceptFileDelete($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"confirmMessage\", ctx_r2.deleteFileConfirmData.message);\n  }\n}\nfunction SingleLibraryComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-confirm-dialog\", 9);\n    i0.ɵɵlistener(\"rejectSelected\", function SingleLibraryComponent_ng_container_2_Template_app_confirm_dialog_rejectSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onRejectFileDelete($event));\n    })(\"confirmSelected\", function SingleLibraryComponent_ng_container_2_Template_app_confirm_dialog_confirmSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onAcceptFileDeleteMultiple($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"confirmMessage\", ctx_r2.deleteMultipleFilesConfirmData.message);\n  }\n}\nfunction SingleLibraryComponent_ng_container_9_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction SingleLibraryComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, SingleLibraryComponent_ng_container_9_ng_container_1_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    const classInfoGeneralTemplate_r6 = i0.ɵɵreference(12);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", classInfoGeneralTemplate_r6)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, ctx_r2.classroom));\n  }\n}\nfunction SingleLibraryComponent_ng_container_10_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction SingleLibraryComponent_ng_container_10_ng_container_5_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction SingleLibraryComponent_ng_container_10_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p-tabPanel\", 15);\n    i0.ɵɵtemplate(2, SingleLibraryComponent_ng_container_10_ng_container_5_ng_container_2_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const selectedStudent_r8 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    const classInfoGeneralTemplate_r6 = i0.ɵɵreference(12);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"header\", selectedStudent_r8.firstName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", classInfoGeneralTemplate_r6)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(3, _c6, ctx_r2.classroom, selectedStudent_r8));\n  }\n}\nfunction SingleLibraryComponent_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p-tabView\", 11);\n    i0.ɵɵlistener(\"onChange\", function SingleLibraryComponent_ng_container_10_Template_p_tabView_onChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onTabChangeFilterFilesPerStudent($event));\n    });\n    i0.ɵɵelementStart(2, \"p-tabPanel\", 12)(3, \"div\", 13);\n    i0.ɵɵtemplate(4, SingleLibraryComponent_ng_container_10_ng_container_4_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, SingleLibraryComponent_ng_container_10_ng_container_5_Template, 3, 6, \"ng-container\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    const classInfoGeneralTemplate_r6 = i0.ɵɵreference(12);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"scrollable\", ctx_r2.classroomStudents.length > 4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", classInfoGeneralTemplate_r6)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c5, ctx_r2.classroom));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.classroomStudents);\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_img_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 43);\n    i0.ɵɵlistener(\"click\", function SingleLibraryComponent_ng_template_11_img_7_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.setGroupActions());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c9, ctx_r2.showGroupActions));\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_img_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 44);\n    i0.ɵɵlistener(\"click\", function SingleLibraryComponent_ng_template_11_img_8_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onShowUploadDialog());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_div_10_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"app-classrooms-check-list\", 56);\n    i0.ɵɵlistener(\"close\", function SingleLibraryComponent_ng_template_11_div_10_div_16_Template_app_classrooms_check_list_close_1_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.showShare = !ctx_r2.showShare);\n    })(\"checkedList\", function SingleLibraryComponent_ng_template_11_div_10_div_16_Template_app_classrooms_check_list_checkedList_1_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onCheckedList($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 57);\n    i0.ɵɵlistener(\"click\", function SingleLibraryComponent_ng_template_11_div_10_div_16_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.shareLibraryFiles());\n    });\n    i0.ɵɵtext(3, \" Share \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 45)(1, \"div\", 46)(2, \"div\", 47);\n    i0.ɵɵelement(3, \"img\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 49);\n    i0.ɵɵlistener(\"click\", function SingleLibraryComponent_ng_template_11_div_10_Template_div_click_4_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.openShareDialog());\n    });\n    i0.ɵɵtext(5, \" Share \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 50);\n    i0.ɵɵlistener(\"click\", function SingleLibraryComponent_ng_template_11_div_10_Template_div_click_6_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.moveMultipleFiles());\n    });\n    i0.ɵɵelementStart(7, \"div\", 47);\n    i0.ɵɵelement(8, \"img\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 52);\n    i0.ɵɵtext(10, \" Move \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 50);\n    i0.ɵɵlistener(\"click\", function SingleLibraryComponent_ng_template_11_div_10_Template_div_click_11_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.deleteMultipleFiles());\n    });\n    i0.ɵɵelementStart(12, \"div\", 47);\n    i0.ɵɵelement(13, \"img\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 52);\n    i0.ɵɵtext(15, \" Delete \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, SingleLibraryComponent_ng_template_11_div_10_div_16_Template, 4, 0, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showShare);\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"app-single-library-folder-item\", 59);\n    i0.ɵɵlistener(\"deleteFolder\", function SingleLibraryComponent_ng_template_11_div_18_Template_app_single_library_folder_item_deleteFolder_1_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.deleteFolderSelected($event));\n    })(\"openFolder\", function SingleLibraryComponent_ng_template_11_div_18_Template_app_single_library_folder_item_openFolder_1_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.openFolderSelected($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const innerFolder_r15 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"folder\", innerFolder_r15)(\"hasLimitedOptions\", ctx_r2.hasLimitedOptions);\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_img_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 60);\n    i0.ɵɵlistener(\"click\", function SingleLibraryComponent_ng_template_11_img_19_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.createFolder());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_img_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 61);\n    i0.ɵɵlistener(\"click\", function SingleLibraryComponent_ng_template_11_img_24_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.openFolder(ctx_r2.folderPath[ctx_r2.folderPath.length - 2]));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 62);\n    i0.ɵɵlistener(\"click\", function SingleLibraryComponent_ng_template_11_div_30_Template_div_click_0_listener() {\n      const folder_r19 = i0.ɵɵrestoreView(_r18).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.openFolder(folder_r19));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const folder_r19 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \\u00A0\", folder_r19.name, \"\\u00A0/ \");\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_ng_container_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 63)(2, \"i\", 64);\n    i0.ɵɵlistener(\"click\", function SingleLibraryComponent_ng_template_11_ng_container_32_Template_i_click_2_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.createFolder());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"input\", 65);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SingleLibraryComponent_ng_template_11_ng_container_32_Template_input_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.folderName, $event) || (ctx_r2.folderName = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function SingleLibraryComponent_ng_template_11_ng_container_32_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onCreateFolder());\n    });\n    i0.ɵɵelementStart(5, \"span\", 67);\n    i0.ɵɵtext(6, \"Create\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.folderName);\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_ng_container_33_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" No files found. \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_ng_container_33_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" No files found. \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_ng_container_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 68);\n    i0.ɵɵelement(2, \"img\", 69);\n    i0.ɵɵelementStart(3, \"p\", 70);\n    i0.ɵɵtemplate(4, SingleLibraryComponent_ng_template_11_ng_container_33_ng_container_4_Template, 2, 0, \"ng-container\", 3)(5, SingleLibraryComponent_ng_template_11_ng_container_33_ng_container_5_Template, 2, 0, \"ng-container\", 3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.authService.isStudent);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.authService.isTeacher);\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_ng_container_34_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 68)(2, \"img\", 71);\n    i0.ɵɵlistener(\"click\", function SingleLibraryComponent_ng_template_11_ng_container_34_Template_img_click_2_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onShowUploadDialog());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 70);\n    i0.ɵɵtext(4, \" Upload your first file! \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_div_35_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-library-file-row-item\", 74);\n    i0.ɵɵlistener(\"fileDeleted\", function SingleLibraryComponent_ng_template_11_div_35_ng_container_2_Template_app_library_file_row_item_fileDeleted_1_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onFileDeleted($event));\n    })(\"libraryChecked\", function SingleLibraryComponent_ng_template_11_div_35_ng_container_2_Template_app_library_file_row_item_libraryChecked_1_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onLibraryChecked($event));\n    })(\"fileUnshared\", function SingleLibraryComponent_ng_template_11_div_35_ng_container_2_Template_app_library_file_row_item_fileUnshared_1_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onFileUnshared($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const libFiles_r23 = ctx.$implicit;\n    const classroom_r24 = i0.ɵɵnextContext(2).classroom;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"showGroupActions\", ctx_r2.showGroupActions)(\"showSharedWithUsers\", ctx_r2.withClassroom)(\"classroom\", classroom_r24)(\"classroomFile\", libFiles_r23)(\"hasLimitedOptions\", ctx_r2.hasLimitedOptions)(\"isSmallScreen\", ctx_r2.isSmallScreen)(\"isInTab\", ctx_r2.isInTab)(\"folders\", ctx_r2.folders)(\"availableActions\", ctx_r2.availableActions)(\"classroomStudents\", ctx_r2.classroomStudents);\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"ul\", 73);\n    i0.ɵɵtemplate(2, SingleLibraryComponent_ng_template_11_div_35_ng_container_2_Template, 2, 10, \"ng-container\", 14);\n    i0.ɵɵpipe(3, \"filter\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const uniqueFiles_r25 = i0.ɵɵnextContext().uniqueFiles;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"height\", ctx_r2.fileListHeight + \"px\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind2(3, 3, ctx_r2.getFilteredFiles(uniqueFiles_r25), i0.ɵɵpureFunction1(6, _c10, ctx_r2.term)));\n  }\n}\nfunction SingleLibraryComponent_ng_template_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 17)(4, \"span\", 18)(5, \"input\", 19);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function SingleLibraryComponent_ng_template_11_Template_input_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.term, $event) || (ctx_r2.term = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"i\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, SingleLibraryComponent_ng_template_11_img_7_Template, 1, 3, \"img\", 21)(8, SingleLibraryComponent_ng_template_11_img_8_Template, 1, 0, \"img\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 23);\n    i0.ɵɵtemplate(10, SingleLibraryComponent_ng_template_11_div_10_Template, 17, 1, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 25)(12, \"div\", 26)(13, \"div\", 27);\n    i0.ɵɵelement(14, \"img\", 28);\n    i0.ɵɵelementStart(15, \"h4\", 29);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 30);\n    i0.ɵɵtemplate(18, SingleLibraryComponent_ng_template_11_div_18_Template, 2, 2, \"div\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, SingleLibraryComponent_ng_template_11_img_19_Template, 1, 0, \"img\", 32);\n    i0.ɵɵelement(20, \"div\", 33);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 34)(22, \"div\", 35)(23, \"div\", 36);\n    i0.ɵɵtemplate(24, SingleLibraryComponent_ng_template_11_img_24_Template, 1, 0, \"img\", 37);\n    i0.ɵɵtext(25, \" \\u00A0 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"img\", 38);\n    i0.ɵɵtext(27, \" \\u00A0 \");\n    i0.ɵɵelement(28, \"img\", 39);\n    i0.ɵɵtext(29, \" \\u00A0 \");\n    i0.ɵɵtemplate(30, SingleLibraryComponent_ng_template_11_div_30_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 41);\n    i0.ɵɵtemplate(32, SingleLibraryComponent_ng_template_11_ng_container_32_Template, 7, 1, \"ng-container\", 3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(33, SingleLibraryComponent_ng_template_11_ng_container_33_Template, 6, 2, \"ng-container\", 3)(34, SingleLibraryComponent_ng_template_11_ng_container_34_Template, 5, 0, \"ng-container\", 3)(35, SingleLibraryComponent_ng_template_11_div_35_Template, 4, 8, \"div\", 42);\n  }\n  if (rf & 2) {\n    const uniqueFiles_r25 = ctx.uniqueFiles;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c7, ctx_r2.isInTab));\n    i0.ɵɵadvance();\n    i0.ɵɵclassMapInterpolate1(\"notes-header-title hidden md:block font-2xl opaque-box-rtl \", ctx_r2.extraGradientClass, \" sm:col-4\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.libraryTitle, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.term);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.canShare);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.canUpload);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showGroupActions);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(21, _c8, ctx_r2.isInTab));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.libraryToAdd.folder.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.innerFolders);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.canUpload);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.folderPath.length > 1);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.folderPath);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showInput);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.files.length === 0 && !ctx_r2.canUpload);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.files.length === 0 && ctx_r2.canUpload);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getFilteredFiles(uniqueFiles_r25).length > 0);\n  }\n}\nfunction SingleLibraryComponent_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" hi\\n\");\n  }\n}\nexport let SingleLibraryComponent = /*#__PURE__*/(() => {\n  class SingleLibraryComponent {\n    constructor(libraryService, classroomService, generalService, authService, confirmationService, toastService, layoutService, dialogService, userService) {\n      this.libraryService = libraryService;\n      this.classroomService = classroomService;\n      this.generalService = generalService;\n      this.authService = authService;\n      this.confirmationService = confirmationService;\n      this.toastService = toastService;\n      this.layoutService = layoutService;\n      this.dialogService = dialogService;\n      this.userService = userService;\n      this.showHeader = true;\n      this.isMyFiles = false;\n      this.showShare = false;\n      this.StudentIdsToSend = [];\n      this.folderPath = [this.libraryService.rootFolder];\n      this.isDisabled = false;\n      this.isDisabledWholeClass = false;\n      this.isMoveRadioCheked = false;\n      this.moveFileId = 0;\n      this.moveFolderId = 0;\n      this.UserRoles = UserRole;\n      this.role = \"\";\n      this.loggedInUser = {};\n      this.inHomework = false;\n      this.task = {};\n      this.classroomIdToAddLib = 0;\n      this.withClassroom = false;\n      this.canUpload = true;\n      this.canShare = true;\n      this.hasLimitedOptions = false;\n      this.availableActions = [];\n      this.libraryTitle = 'My Files';\n      this.isInTab = false;\n      this.extraGradientClass = 'purple';\n      this.fileListHeight = 0;\n      this.subs = new SubSink();\n      this.showFilters = false;\n      this.showGroupActions = false;\n      this.showAddLibrary = false;\n      this.foldersWithFiles = [];\n      this.allLibrariesWithFiles = [];\n      this.title = \"\";\n      this.currentPathLibraries = [];\n      this.innerFolders = [];\n      this.libraryToAdd = {};\n      this.folderName = \"\";\n      this.showInput = false;\n      this.user = {};\n      this.folders = [];\n      this.inClassroom = true;\n      this.showingMine = true;\n      this.files = [];\n      this.filteredFiles = [];\n      this.libsSharedWith = [];\n      this.libsSharedBy = [];\n      this.studentsToShareWith = [];\n      this.deleteFolderConfirmData = {\n        showDialog: false,\n        message: 'Delete folder?'\n      };\n      this.deleteFileConfirmData = {\n        showDialog: false,\n        message: 'Delete File?'\n      };\n      this.deleteMultipleFilesConfirmData = {\n        showDialog: false,\n        message: 'Delete selected files?'\n      };\n      this.checkedLibraryFiles = [];\n      this.isSmallScreen = false;\n      this.currentUploadFolder = {};\n      this.classroomStudentsIds = [];\n    }\n    ngOnInit() {\n      // if (this.libraryService.isPreviewingFromChat()) {\n      //   this.libraryService.setGroupActions(true);\n      // }\n      // set unique student ids...\n      if (this.classroomStudents) {\n        this.classroomStudents = Array.from(new Set(this.classroomStudents.map(student => student.aspUserId))).map(aspUserId => this.classroomStudents.find(student => student.aspUserId === aspUserId));\n        this.classroomStudentsIds = this.classroomStudents.map(student => student.aspUserId);\n      }\n      if (this.classroomStudents && this.classroomStudents.length === 1) {\n        this.setStudentsToShareWith(this.classroomStudents[0].aspUserId);\n      }\n      this.oncollapseNotesLeftSideChanged(true);\n      console.log(this.classroomIdToAddLib);\n      // this.generalService.slideNativeElements(true, this.addLibrary.nativeElement);\n      this.loggedInUser = this.authService.getLoggedInUser();\n      this.role = this.authService.getUserRole();\n      this.items = [{\n        label: 'Add New',\n        icon: 'pi pi-fw pi-plus',\n        command: event => {}\n      }, {\n        label: 'Remove',\n        icon: 'pi pi-fw pi-minus'\n      }];\n      this.subs.sink = this.generalService.deviceKind.subscribe(deviceKind => {\n        this.isSmallScreen = !deviceKind.w768up;\n      });\n      this.subs.sink = this.libraryService.showGroupActions.subscribe(res => {\n        this.showGroupActions = res;\n      });\n      this.subs.sink = this.layoutService.sideMenuHeight.subscribe(res => {\n        // this.leftSide.nativeElement.style.maxHeight = (res - 100 + 'px');\n        // this.mainWrapper.nativeElement.style.height = (res -60 + 'px');\n        // this.classroomWrapper.nativeElement.style.height = (res - extraHeight.wrapper + 'px');\n        // this.scrollHeight = (res - extraHeight.scrollWrapper + 'px');/assets/icons/library/arrow-left.svg\n      });\n      this.subs.sink = this.libraryService.updateFolderListener.subscribe(res => {\n        if (res.folder.folderId > 0) {\n          for (let lib of this.allLibrariesWithFiles) {\n            let found = false;\n            for (let file of lib.libraryFiles) {\n              if (file.fileId == res.libraryFile.fileId) {\n                found = true;\n              }\n            }\n            if (found) {\n              lib.folderId = res.folder.folderId;\n              lib.name = res.folder.name;\n              lib.parent = res.folder.parent;\n            }\n          }\n          this.openFolder(this.libraryToAdd.folder);\n        }\n      });\n      this.initOpenFolderListener();\n      this.getClassRooms();\n      // IRAKLIS SOURCE\n      this.subs.sink = this.libraryService.updateListener.subscribe(res => {\n        if (res) {\n          this.getFoldersWithFiles();\n        }\n      });\n      this.subs.sink = this.libraryService.deleteListener.subscribe(res => {\n        if (res > 0) {\n          for (let lib of this.allLibrariesWithFiles) {\n            let found = false;\n            for (let file of lib.libraryFiles) {\n              if (file.fileId == res) {\n                found = true;\n              }\n            }\n            if (found) {\n              lib.libraryFiles = lib.libraryFiles.filter(el => el.fileId != res);\n            }\n          }\n          this.getFoldersWithFiles();\n        }\n      });\n      this.libraryToAdd = this.libraryService.initializeEmptyLibraryObject();\n      this.user = this.authService.getLoggedInUser();\n      this.role = this.user.role;\n    }\n    ngOnChanges() {\n      this.initFiles();\n      this.getFoldersWithFiles();\n    }\n    ngAfterViewInit() {}\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n      this.libraryService.setGroupActions(false);\n    }\n    openShareDialog() {\n      if (this.checkedLibraryFiles.length === 0) {\n        this.toastService.setShowToastmessage({\n          severity: 'warn',\n          summary: '',\n          detail: 'Please choose at least one file!'\n        });\n        return;\n      }\n      this.libraryService.openDialogWithComponent(this.dialogService, LibraryFileActionsDialogComponent, 400, {\n        action: 'shareWithMultiple',\n        dialogsubTitle: 'Share multiple with...',\n        dialogTitle: 'Share file with...'\n      }, 'allFiles', result => {\n        if (result && result.action === 'shareWithMultiple') {\n          this.shareLibraryFiles(result.map.map);\n        }\n      });\n    }\n    shareMultipleLibraryFiles() {}\n    onLibraryChecked(event) {\n      console.log(event);\n      if (event.checked) {\n        this.checkedLibraryFiles.push(event.file);\n      } else {\n        this.checkedLibraryFiles = this.checkedLibraryFiles.filter(el => el.path != event.file.path);\n      }\n      this.libraryService.checkedLibraryFiles.set(this.checkedLibraryFiles);\n      console.log(this.checkedLibraryFiles);\n    }\n    shareLibraryFiles(map) {\n      map.forEach((users, classroomId) => {\n        let ids = [];\n        let fileIds = [];\n        for (let user of users) {\n          ids.push(user.aspUserId);\n        }\n        for (let lib of this.checkedLibraryFiles) {\n          fileIds.push(lib.fileId);\n        }\n        let send = {\n          fileIds: fileIds,\n          classroomId: classroomId,\n          studentIds: ids\n        };\n        this.sendShareToApi(send);\n      });\n    }\n    sendShareToApi(sendParams) {\n      this.libraryService.shareFileWithClassUsers(sendParams).pipe(take(1)).subscribe(res => {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Library has been sent.'\n        });\n      });\n    }\n    onShowAddLibrary() {\n      this.showAddLibrary = !this.showAddLibrary;\n      this.libraryToAdd.folder = this.folderPath[this.folderPath.length - 1];\n      // this.generalService.slideNativeElements(this.showAddLibrary, this.addLibrary.nativeElement);\n    }\n    deleteFolderSelected(folder) {\n      this.deleteFolder(folder.folderId);\n    }\n    openFolderSelected(folder) {\n      this.openFolder(folder);\n    }\n    getClassRooms() {\n      this.subs.add(this.classroomService.getLMSUserClassrooms(this.loggedInUser.id).subscribe(response => {\n        this.teacherClassrooms = response;\n      }));\n    }\n    // createLibFolder(){\n    //   this.libraryService.createFolder().subscribe( (response) => {\n    //   });\n    // }\n    deleteLibFolder(folderId) {\n      this.libraryService.deleteFolder(folderId).subscribe(response => {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Folder Deleted successfully.'\n        });\n      });\n    }\n    getFileName(path) {\n      if (path) {\n        let n = path.lastIndexOf(\"/\");\n        if (n === -1) n = path.lastIndexOf(\"\\\\\");\n        let result = path.substring(n + 1);\n        return result;\n      }\n      return path;\n    }\n    showSharePopup(fileId) {\n      document.getElementById('lib-file-move-menu-' + fileId).style.display = 'none';\n      document.getElementById('lib-file-share-menu-' + fileId).style.display = 'block';\n      document.getElementById('lib-file-share-menu-' + fileId).style.top = '25%';\n    }\n    // shareLibraryFiles(fileId: number, classroomID: number) {\n    //   let fileIds = []\n    //   fileIds.push(fileId)\n    //   let body: any = {\n    //     fileIds: fileIds,\n    //     classroomId: this.classroomId,\n    //     studentIds: this.StudentIdsToSend\n    //   }\n    //   if (this.StudentIdsToSend.length === 0){\n    //   }\n    //   else{\n    //     this.libraryService.shareFileWithClassUsers(body).pipe(take(1)).subscribe(res => {\n    //     })\n    //   }\n    //   this.ngOnInit();\n    // }\n    onFolderSelect(fileId, folderId) {\n      this.isMoveRadioCheked = true;\n      this.moveFileId = fileId;\n      this.moveFolderId = folderId;\n    }\n    moveFile() {\n      this.moveFileAPI(this.moveFileId, this.moveFolderId);\n      // this.libraryService.moveFileToFolder(this.moveFileId, this.moveFolderId).subscribe( (response) => {\n      //   this.toastService.setShowToastmessage({\n      //     severity: 'success',\n      //     summary: '',\n      //     detail: 'File moved successfully.'\n      //   });\n      // });\n      this.ngOnInit();\n    }\n    // IRAKLIS SOURCE\n    initFiles() {\n      this.filteredFiles = [];\n      let i = 0;\n      for (let folderWithFiles of this.foldersWithFiles) {\n        for (let libraryFile of folderWithFiles.libraryFiles) {\n          libraryFile.dummyId = i;\n          i++;\n          this.filteredFiles.push(libraryFile);\n        }\n      }\n      this.files = this.filteredFiles;\n    }\n    getFoldersWithFiles() {\n      this.initializeFilesWithFolders(this.allLibrariesWithFiles);\n    }\n    initializeFilesWithFolders(res) {\n      this.allLibrariesWithFiles = res;\n      this.folders = [];\n      for (const lib of this.allLibrariesWithFiles) {\n        const folder = {\n          name: lib.name,\n          parent: lib.parent,\n          folderId: lib.folderId\n        };\n        if (!this.folders.some(el => el.name === folder.name && el.parent === folder.parent)) {\n          this.folders.push(folder);\n        }\n      }\n      this.foldersWithFiles = this.libraryService.getLibrariesOnRootFolders(this.allLibrariesWithFiles);\n      this.initFiles();\n      this.getInnerFolders('home');\n    }\n    getInnerFolders(parent) {\n      this.innerFolders = [];\n      let copyFolders = this.allLibrariesWithFiles.filter(el => el.parent == parent);\n      for (let innerFolder of copyFolders) {\n        if (!this.innerFolders.some(el => el.parent == innerFolder.parent && el.name == innerFolder.name)) {\n          this.innerFolders.push(innerFolder);\n        }\n      }\n    }\n    openFolder(folder) {\n      if (!folder) {\n        this.getInnerFolders('home');\n        return;\n      }\n      this.libraryToAdd.folder = folder;\n      console.log(this.libraryToAdd.folder);\n      this.foldersWithFiles = this.libraryService.getLibrariesOfFolder(folder, this.allLibrariesWithFiles);\n      this.initFiles();\n      if (folder.name == 'home') {\n        this.getInnerFolders('home');\n      } else {\n        this.getInnerFolders(folder.name);\n      }\n      this.folderPath = this.libraryService.getFolderPathOfFolder(folder, [], this.folders);\n      this.folderPath.unshift(this.libraryService.rootFolder);\n      this.libraryToAdd.folder = this.folderPath[this.folderPath.length - 1];\n    }\n    deleteFolder(id) {\n      const folderToDelete = this.innerFolders.filter(el => el.folderId === id);\n      this.libraryService.setFolderToDelete(folderToDelete[0]);\n      this.deleteFolderConfirmData.showDialog = true;\n      this.deleteFolderConfirmData.message = 'Delete the folder \"' + folderToDelete[0].name + '\"?';\n      // this.libraryService.deleteFolder(id).pipe(take(1)).subscribe(res => {\n      //   this.innerFolders = this.innerFolders.filter(el => el.folderId != id)\n      //   console.log(this.innerFolders)\n      // })\n    }\n    onRejectFolderDelete(event) {\n      this.libraryService.setFolderToDelete({});\n      this.deleteFolderConfirmData.showDialog = false;\n    }\n    onAcceptFolderDelete(event) {\n      this.libraryService.deleteFolder(this.libraryService.getFolderToDelete().folderId).pipe(take(1)).subscribe(res => {\n        this.innerFolders = this.innerFolders.filter(el => el.folderId != this.libraryService.getFolderToDelete().folderId);\n        console.log(this.innerFolders);\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Folder has been deleted.'\n        });\n        this.removeFolderByFolderId(this.libraryService.getFolderToDelete());\n        this.libraryService.setFolderToDelete({});\n        this.libraryService.setUpdateListener(true);\n      });\n      this.deleteFolderConfirmData.showDialog = false;\n    }\n    onTagChoosen(event) {\n      let isLevel = event.isLevel;\n      if (event.checked) {\n        if (event.applyToAll) {\n          for (let file of this.libraryToAdd.libraryFiles) {\n            let arrLibrary = isLevel ? file.levels : file.categories;\n            arrLibrary.push(event.tag);\n          }\n        } else {\n          let file = this.libraryToAdd.libraryFiles[event.file.index];\n          let arrLibrary = isLevel ? file.levels : file.categories;\n          arrLibrary.push(event.tag);\n        }\n      } else {\n        if (event.applyToAll) {\n          for (let file of this.libraryToAdd.libraryFiles) {\n            let arrLibrary = isLevel ? file.levels : file.categories;\n            let index2 = arrLibrary.findIndex(el => el == event.tag);\n            arrLibrary.splice(index2, 1);\n          }\n        } else {\n          let file = this.libraryToAdd.libraryFiles[event.file.index];\n          let arrLibrary = isLevel ? file.levels : file.categories;\n          let index2 = arrLibrary.findIndex(el => el == event.tag);\n          arrLibrary.splice(index2, 1);\n        }\n      }\n    }\n    onCreateFolder() {\n      this.libraryService.getUserCreatedLibFolders().pipe(take(1)).subscribe(res => {\n        let found = false;\n        for (let r of res) {\n          if (r.parent == this.folderPath[this.folderPath.length - 1].name && r.name == this.folderName) {\n            found = true;\n            break;\n          }\n        }\n        if (!found) {\n          this.showInput = false;\n          let newFolder = {\n            parent: this.folderPath[this.folderPath.length - 1].name,\n            name: this.folderName\n          };\n          this.libraryService.createFolder(newFolder).pipe(take(1)).subscribe(res => {\n            this.innerFolders.push(res);\n            this.allLibrariesWithFiles.push({\n              parent: this.folderPath[this.folderPath.length - 1].name,\n              name: this.folderName,\n              libraryFiles: [],\n              createdBy: this.user,\n              folderId: 10000\n            });\n            this.toastService.setShowToastmessage({\n              severity: 'success',\n              summary: '',\n              detail: 'Folder created.'\n            });\n            this.folderName = '';\n            this.libraryService.setUpdateListener(true);\n          });\n        } else {\n          this.toastService.setShowToastmessage({\n            severity: 'warn',\n            summary: '',\n            detail: 'Folder already exists.'\n          });\n        }\n      });\n    }\n    createFolder() {\n      this.showInput = !this.showInput;\n    }\n    onShowUploadDialog() {\n      const targetElementName = this.libraryService.isPreviewingFromChat() ? 'notifications-overview' : 'mainContentColumn';\n      const myElement = document.getElementById(targetElementName);\n      // this.libraryToAdd = this.libraryService.initializeEmptyLibraryObject();\n      // this.libraryToAdd.libraryFiles = [];\n      this.userService.setNewFileUpload({});\n      console.log(myElement.getBoundingClientRect().width);\n      const dialogWidth = myElement.getBoundingClientRect().width - 10;\n      this.generalService.openDialogWithComponent(this.dialogService, LibraryFileActionsDialogComponent, dialogWidth.toFixed(), {\n        action: 'upload',\n        dialogsubTitle: '',\n        dialogTitle: 'Upload to library...',\n        folder: this.libraryToAdd.folder,\n        libraryToAdd: this.libraryToAdd\n      }, targetElementName, result => {\n        if (result && result.action === 'upload') {\n          console.log(result);\n          this.libraryToAdd.libraryFiles = result.libraryFiles;\n          this.onSubmit();\n          // this.moveFile(result.map.map);\n        }\n      }, myElement.getBoundingClientRect().height - 90 + 'px');\n    }\n    onSubmit() {\n      if (this.libraryToAdd.libraryFiles.length > 0) {\n        this.libraryToAdd.classroom = this.classroomIdToAddLib;\n        let toSend = {\n          folderId: this.libraryToAdd.folder.folderId,\n          classroomId: this.classroomIdToAddLib,\n          name: this.libraryToAdd.folder.name,\n          parent: this.libraryToAdd.folder.parent,\n          libFiles: this.libraryToAdd.libraryFiles,\n          StudentIds: this.studentsToShareWith\n        };\n        // return;\n        if (this.classroomIdToAddLib > 0) {\n          this.libraryService.uploadClassroomFilesToFolder(toSend).pipe(take(1)).subscribe(res => {\n            this.toastService.setShowToastmessage({\n              severity: 'success',\n              summary: '',\n              detail: 'Your files have been uploaded to Classroom library.'\n            });\n            this.userService.setUploadFiles(true);\n            this.mapLibraryToAddFiles(res);\n            this.e();\n          });\n        } else {\n          this.libraryService.uploadFilesToFolder(toSend).pipe(take(1)).subscribe(res => {\n            this.toastService.setShowToastmessage({\n              severity: 'success',\n              summary: '',\n              detail: 'Your files have been uploaded to library folder: ' + toSend.name\n            });\n            this.currentUploadFolder = this.innerFolders.filter(el => el.folderId === toSend.folderId)[0];\n            this.userService.setUploadFiles(true);\n            this.mapLibraryToAddFiles(res);\n            this.e();\n            console.log(this.currentUploadFolder);\n            // this.libraryService.setOpenFolderListener(currentFolder[0]);\n            // this.libraryService.setUpdateFolderListener(toSend.libFiles[0], currentFolder[0])\n          });\n        }\n      } else {\n        this.toastService.setShowToastmessage({\n          severity: 'warn',\n          summary: '',\n          detail: 'Please add files'\n        });\n      }\n    }\n    e() {\n      this.filteredFiles.concat(this.libraryToAdd.libraryFiles);\n      this.foldersWithFiles.push({\n        folderId: this.libraryToAdd.folder.folderId,\n        name: this.libraryToAdd.folder.name,\n        parent: this.libraryToAdd.folder.parent,\n        libraryFiles: this.libraryToAdd.libraryFiles,\n        createdBy: this.user\n      });\n      // this.getFoldersWithFiles();\n      // this.showAddLibrary = !this.showAddLibrary\n      // this.generalService.slideNativeElements(this.showAddLibrary, this.addLibrary.nativeElement);\n      console.log(this.filteredFiles);\n      this.allLibrariesWithFiles.push({\n        folderId: this.libraryToAdd.folder.folderId,\n        name: this.libraryToAdd.folder.name,\n        parent: this.libraryToAdd.folder.parent,\n        libraryFiles: this.libraryToAdd.libraryFiles,\n        createdBy: this.user\n      });\n      // this.files.push(this.files[this.files.length - 1]);\n      this.initFiles();\n      this.libraryToAdd = this.libraryService.initializeEmptyLibraryObject();\n      // this.libraryToAdd.libraryFiles = [];\n      // this.libraryService.setUpdateListener(true)\n      this.userService.setNewFileUpload({});\n    }\n    // addes also fileId to a new uploaded file in the library\n    mapLibraryToAddFiles(res) {\n      this.libraryToAdd.libraryFiles = res.libFiles.map((file, index) => ({\n        ...file,\n        fileId: res.fileIds[index]\n      }));\n    }\n    uploadFinished(event) {\n      console.log(event);\n      this.libraryToAdd.libraryFiles?.push({\n        path: event.filePath.dbPath,\n        levels: [],\n        categories: [],\n        name: event.filePath.dbPath,\n        fileId: event.filePath.dbPath\n      });\n    }\n    /**\n    This method is triggered when a file is deleted from the classroom files.\n    It filters out the deleted file from the libraryFiles array of each folder in the allLibrariesWithFiles array.\n    @param {any} event - The event object containing the ID of the deleted file.\n    @returns {void}\n    */\n    onFileDeleted(event) {\n      this.deleteFileConfirmData.showDialog = true;\n      this.deleteFileConfirmData.message = 'Delete file \"' + event.name + '\"?';\n      // this.allLibrariesWithFiles = this.allLibrariesWithFiles..filter(file => file.fileId !== libFile.fileId);\n      this.libraryService.setLibraryFile(event);\n    }\n    deleteFile(fileId) {\n      this.subs.add(this.libraryService.delete(fileId).subscribe(res => {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Files has been deleted successfully.'\n        });\n      }));\n    }\n    moveMultipleFiles() {\n      if (this.checkedLibraryFiles.length === 0) {\n        this.toastService.setShowToastmessage({\n          severity: 'warn',\n          summary: '',\n          detail: 'Please choose at least one file!'\n        });\n        return;\n      }\n      this.libraryService.openDialogWithComponent(this.dialogService, LibraryFileActionsDialogComponent, 400, {\n        action: 'moveMultiple',\n        dialogsubTitle: 'Move Files to folder...',\n        dialogTitle: 'Choose folder...',\n        folders: this.folders\n      }, null, result => {\n        if (result && result.action === 'moveMultiple') {\n          console.log(result);\n          console.log(this.checkedLibraryFiles);\n          this.checkedLibraryFiles.forEach(libFile => {\n            this.moveFileAPI(libFile.fileId, result.folder.folderId);\n            this.libraryService.setUpdateListener(true);\n          });\n          // this.moveFile(result.map.map);\n        }\n      });\n    }\n    deleteMultipleFiles() {\n      if (this.checkedLibraryFiles.length === 0) {\n        this.toastService.setShowToastmessage({\n          severity: 'warn',\n          summary: '',\n          detail: 'Please choose at least one file!'\n        });\n        return;\n      }\n      this.deleteMultipleFilesConfirmData.showDialog = true;\n      this.deleteMultipleFilesConfirmData.message = 'Delete the selected files ?';\n    }\n    onAcceptFileDeleteMultiple(event) {\n      const libFiles = this.checkedLibraryFiles;\n      libFiles.forEach(libFile => {\n        this.filterDelteFromLibraryFiles(libFile.fileId);\n        this.deleteFileAPI(libFile);\n      });\n      this.deleteFileConfirmData.showDialog = false;\n      this.deleteMultipleFilesConfirmData.showDialog = false;\n      this.checkedLibraryFiles = [];\n    }\n    filterDelteFromLibraryFiles(...fileIds) {\n      if (fileIds.length === 1) {\n        this.filteredFiles = this.filteredFiles.filter(el => el.fileId !== fileIds[0]);\n      } else {\n        this.filteredFiles = this.filteredFiles.filter(el => !fileIds.includes(el.fileId));\n      }\n    }\n    onRejectFileDelete(event) {\n      this.deleteFileConfirmData.showDialog = false;\n      this.deleteMultipleFilesConfirmData.showDialog = false;\n      this.libraryService.setLibraryFile({});\n    }\n    onAcceptFileDelete(event) {\n      const libFile = this.libraryService.getLibraryFile();\n      this.filterDelteFromLibraryFiles(libFile.fileId);\n      this.deleteFileAPI(libFile);\n    }\n    deleteFileAPI(libFile) {\n      this.subs.add(this.libraryService.delete(libFile.fileId).subscribe(res => {\n        if (res) {\n          this.deleteFileConfirmData.showDialog = false;\n          this.toastService.setShowToastmessage({\n            severity: 'success',\n            summary: '',\n            detail: 'Files has been deleted successfully..'\n          });\n          this.removeLibraryFileByFileId(libFile);\n        }\n      }));\n    }\n    removeLibraryFileByFileId(libFile) {\n      this.allLibrariesWithFiles = this.allLibrariesWithFiles.map(file => {\n        const filteredLibraryFiles = file.libraryFiles.filter(libraryFile => libraryFile.fileId !== libFile.fileId);\n        return {\n          ...file,\n          libraryFiles: filteredLibraryFiles\n        };\n      });\n    }\n    removeFolderByFolderId(folder) {\n      this.allLibrariesWithFiles = this.allLibrariesWithFiles.filter(file => {\n        return file.folderId !== folder.folderId;\n      });\n    }\n    moveFileAPI(fileId, moveFolderId) {\n      this.subs.add(this.libraryService.moveFileToFolder(fileId, moveFolderId).subscribe(response => {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'File moved successfully.'\n        });\n        this.checkedLibraryFiles = [];\n        this.showGroupActions = false;\n        // this.openFolder(this.libraryToAdd.folder);\n        this.libraryService.setUpdateListener(true);\n      }));\n    }\n    oncollapseNotesLeftSideChanged(event) {\n      this.isLeftsideCollapsed = event;\n    }\n    setGroupActions() {\n      this.showGroupActions = !this.showGroupActions;\n      // this.libraryService.setGroupActions(this.showGroupActions);\n      setTimeout(() => {\n        // this.libraryService.setGroupActions(this.showGroupActions);\n      }, 300);\n    }\n    onTabChangeFilterFilesPerStudent(event) {\n      if (event.index > 0) {\n        this.studentsToShareWith = [];\n        const currentStudent = this.classroomStudents[event.index - 1];\n        this.setStudentsToShareWith(this.classroomStudents[event.index - 1].aspUserId);\n        this.filteredFiles = this.files.filter(folder => folder.sharedWith.id === currentStudent.aspUserId);\n      } else {\n        this.filteredFiles = this.files;\n        this.setStudentsToShareWith();\n      }\n    }\n    setStudentsToShareWith(studentId) {\n      this.studentsToShareWith = studentId ? [studentId] : this.classroomStudents.map(user => user.aspUserId);\n    }\n    getFilteredFiles(unique = false) {\n      if (!unique) {\n        return this.filteredFiles;\n      } else {\n        const uniqueFilteredFiles = this.filteredFiles = this.filteredFiles.filter((file, index, self) => index === self.findIndex(f => f.fileId === file.fileId));\n        return uniqueFilteredFiles;\n      }\n    }\n    onFileUnshared(event) {\n      console.log(event);\n      this.libraryService.setUpdateListener(true);\n    }\n    initOpenFolderListener() {\n      this.subs.sink = this.libraryService.libraryUpdatedListener.subscribe(res => {\n        if (res && this.currentUploadFolder) {\n          if (!this.generalService.isNullishObject(this.currentUploadFolder)) {\n            this.openFolder(this.currentUploadFolder);\n            console.log(this.libraryToAdd.folder);\n          }\n        }\n      });\n    }\n    static #_ = this.ɵfac = function SingleLibraryComponent_Factory(t) {\n      return new (t || SingleLibraryComponent)(i0.ɵɵdirectiveInject(i1.LibraryService), i0.ɵɵdirectiveInject(i2.ClassroomService), i0.ɵɵdirectiveInject(i3.GeneralService), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i5.ConfirmationService), i0.ɵɵdirectiveInject(i6.ToastService), i0.ɵɵdirectiveInject(i7.LayoutService), i0.ɵɵdirectiveInject(i8.DialogService), i0.ɵɵdirectiveInject(i9.UserService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SingleLibraryComponent,\n      selectors: [[\"app-single-library\"]],\n      viewQuery: function SingleLibraryComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n          i0.ɵɵviewQuery(_c1, 7);\n          i0.ɵɵviewQuery(_c2, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.addLibrary = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.leftSide = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.mainWrapper = _t.first);\n        }\n      },\n      inputs: {\n        showHeader: \"showHeader\",\n        inHomework: \"inHomework\",\n        task: \"task\",\n        classroomIdToAddLib: \"classroomIdToAddLib\",\n        classroomStudents: \"classroomStudents\",\n        withClassroom: \"withClassroom\",\n        canUpload: \"canUpload\",\n        canShare: \"canShare\",\n        hasLimitedOptions: \"hasLimitedOptions\",\n        availableActions: \"availableActions\",\n        leftSideHeight: \"leftSideHeight\",\n        libraryTitle: \"libraryTitle\",\n        isInTab: \"isInTab\",\n        extraGradientClass: \"extraGradientClass\",\n        fileListHeight: \"fileListHeight\",\n        allLibrariesWithFiles: \"allLibrariesWithFiles\",\n        title: \"title\"\n      },\n      features: [i0.ɵɵProvidersFeature([DialogService, FilterPipe]), i0.ɵɵNgOnChangesFeature],\n      decls: 15,\n      vars: 17,\n      consts: [[\"mainWrapper\", \"\"], [\"classInfoGeneralTemplate\", \"\"], [\"classInfoStudentTemplate\", \"\"], [4, \"ngIf\"], [1, \"notes\"], [1, \"modal\", \"p-0\", \"max-w-12rem\", 3, \"ngClass\"], [\"headerBackgroundImage\", \"/assets/images/dashboard/calendar/calendar-dialog-lesson-info-arranged.png\", \"blockClass\", \"border-radius-bottom-10\", \"headerBlockClass\", \"py-1 border-round-lg bg-cover sticky top-0 z-5 \", \"containerClass\", \"block-gradient-reverse px-3 py-2 relative\", 3, \"closeDialogEvent\", \"header\", \"headerClass\", \"headerTextClass\", \"showCloseDialogIcon\"], [1, \"block-gradient\", \"p-3\"], [\"dialogKey\", \"key1\", \"headerClass\", \"text-center flex align-items-center flex-column bg-white\", \"acceptBtnLabel\", \"Yes\", \"rejectBtnLabel\", \"No\", 3, \"rejectSelected\", \"confirmSelected\", \"rejectBtnIcon\", \"confirmMessage\"], [\"dialogKey\", \"key2\", \"headerClass\", \"text-center flex align-items-center flex-column bg-white\", \"acceptBtnLabel\", \"Yes\", \"rejectBtnLabel\", \"No\", 3, \"rejectSelected\", \"confirmSelected\", \"rejectBtnIcon\", \"confirmMessage\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"w-full\", 3, \"onChange\", \"scrollable\"], [\"header\", \"All\"], [\"id\", \"classroom-info\", 1, \"\"], [4, \"ngFor\", \"ngForOf\"], [3, \"header\"], [1, \"notes-header\", \"mb-2\", \"lg:absolute\", \"gap-3\", \"abs-header-top\", \"lg:flex\", 3, \"ngClass\"], [1, \"flex\", \"align-items-center\", \"justify-content-end\", \"notes-header-actions\", \"gap-2\", 2, \"margin-right\", \"5px\"], [1, \"p-input-icon-right\", \"w-full\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Search\", 1, \"h-2rem\", \"w-full\", \"input-blue\", \"gradient-blue\", \"rounded\", 3, \"ngModelChange\", \"ngModel\"], [1, \"pi\", \"pi-search\", \"text-primary\"], [\"title\", \"Group Actions\", \"src\", \"/assets/icons/library/group-actions.svg\", \"class\", \"hvr-grow pointer\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"title\", \"Upload File\", \"src\", \"/assets/icons/library/add-lib.svg\", \"class\", \"add hvr-grow\", 3, \"click\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\"], [\"class\", \"note-menu-row block-gradient border-round-xl font-sm my-2 p-1 px-3 gap-2\", 4, \"ngIf\"], [1, \"my-folders\"], [1, \"flex\", \"flex-row\"], [1, \"flex\", \"flex-column\", \"p-4\", \"my-folder\", \"align-items-center\", 3, \"ngClass\"], [\"src\", \"/assets/icons/my-folder.svg\", \"width\", \"40\"], [1, \"text-primary\", \"m-0\", \"font-base\", \"text-center\", \"font-semibold\", \"pt-1\", \"capitalize\"], [1, \"flex\", \"flex-row\", \"gap-4\", \"ml-4\", \"align-items-center\", \"horizontal-scroll-menu\", \"px-3\"], [\"class\", \"flex flex-column relative\", 4, \"ngFor\", \"ngForOf\"], [\"title\", \"Create new subfolder\", \"class\", \"ml-auto px-3 pointer hvr-grow\", \"src\", \"/assets/icons/folder-add.svg\", \"width\", \"60\", 3, \"click\", 4, \"ngIf\"], [1, \"inner-folders\"], [1, \"folder-path\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\"], [1, \"folder-nav-buttons\"], [\"src\", \"/assets/icons/arr-left.svg\", \"height\", \"18\", \"class\", \"pointer\", 3, \"click\", 4, \"ngIf\"], [\"src\", \"/assets/icons/folder-start.png\"], [\"src\", \"/assets/icons/arr-right.svg\"], [\"class\", \"link-main-color font-sm capitalize\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"ml-auto\"], [\"class\", \"allFiles\", \"id\", \"allFiles\", 4, \"ngIf\"], [\"title\", \"Group Actions\", \"src\", \"/assets/icons/library/group-actions.svg\", 1, \"hvr-grow\", \"pointer\", 3, \"click\", \"ngClass\"], [\"title\", \"Upload File\", \"src\", \"/assets/icons/library/add-lib.svg\", 1, \"add\", \"hvr-grow\", 3, \"click\"], [1, \"note-menu-row\", \"block-gradient\", \"border-round-xl\", \"font-sm\", \"my-2\", \"p-1\", \"px-3\", \"gap-2\"], [1, \"note-menu-col\"], [1, \"note-menu-icon\"], [\"src\", \"/assets/icons/library/share-sm-icon.svg\", \"height\", \"14\"], [1, \"note-menu-text\", \"link-main-color\", 3, \"click\"], [1, \"note-menu-col\", 3, \"click\"], [\"src\", \"/assets/icons/library/move.svg\", \"height\", \"14\"], [1, \"note-menu-text\", \"link-main-color\"], [\"src\", \"/assets/icons/library/delete.svg\", \"height\", \"14\"], [\"class\", \"send\", 4, \"ngIf\"], [1, \"send\"], [3, \"close\", \"checkedList\"], [1, \"main-color-button\", \"share-btn\", 3, \"click\"], [1, \"flex\", \"flex-column\", \"relative\"], [3, \"deleteFolder\", \"openFolder\", \"folder\", \"hasLimitedOptions\"], [\"title\", \"Create new subfolder\", \"src\", \"/assets/icons/folder-add.svg\", \"width\", \"60\", 1, \"ml-auto\", \"px-3\", \"pointer\", \"hvr-grow\", 3, \"click\"], [\"src\", \"/assets/icons/arr-left.svg\", \"height\", \"18\", 1, \"pointer\", 3, \"click\"], [1, \"link-main-color\", \"font-sm\", \"capitalize\", 3, \"click\"], [1, \"flex\", \"w-auto\", \"align-items-center\"], [1, \"pi\", \"pi-times-circle\", \"mr-2\", \"pointer\", \"icon-times-blue\", 3, \"click\"], [\"type\", \"text\", \"pinputtext\", \"\", \"placeholder\", \"Name your folder...\", 1, \"h-2rem\", \"input-blue\", \"gradient-blue\", \"rounded\", \"btn-right\", \"mr-2\", 3, \"ngModelChange\", \"ngModel\"], [\"type\", \"button\", \"pbutton\", \"\", \"pripple\", \"\", \"label\", \"Search\", 1, \"p-element\", \"p-button\", \"p-button-xs\", \"p-component\", \"btn-gradient-blue\", 2, \"height\", \"32px\", 3, \"click\"], [1, \"p-button-label\"], [1, \"flex\", \"flex-column\", \"gap-1\", \"align-items-center\", \"justify-content-center\"], [\"src\", \"/assets/icons/library/file_manager_emp.png\", \"alt\", \"file manager empty icon\", 1, \"add\", \"empty\", \"hvr-grow\"], [1, \"font-base\", \"max-w-14rem\", \"text-center\"], [\"src\", \"/assets/icons/library/add-lib.svg\", 1, \"add\", \"lg\", \"hvr-grow\", 3, \"click\"], [\"id\", \"allFiles\", 1, \"allFiles\"], [1, \"library-file-list\"], [\"appendDialogActionsTo\", \"allFiles\", 3, \"fileDeleted\", \"libraryChecked\", \"fileUnshared\", \"showGroupActions\", \"showSharedWithUsers\", \"classroom\", \"classroomFile\", \"hasLimitedOptions\", \"isSmallScreen\", \"isInTab\", \"folders\", \"availableActions\", \"classroomStudents\"]],\n      template: function SingleLibraryComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵtemplate(0, SingleLibraryComponent_ng_container_0_Template, 2, 1, \"ng-container\", 3)(1, SingleLibraryComponent_ng_container_1_Template, 2, 1, \"ng-container\", 3)(2, SingleLibraryComponent_ng_container_2_Template, 2, 1, \"ng-container\", 3);\n          i0.ɵɵelementStart(3, \"div\", 4, 0)(5, \"div\", 5)(6, \"app-block-viewer\", 6);\n          i0.ɵɵlistener(\"closeDialogEvent\", function SingleLibraryComponent_Template_app_block_viewer_closeDialogEvent_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onShowAddLibrary());\n          });\n          i0.ɵɵelement(7, \"div\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\");\n          i0.ɵɵtemplate(9, SingleLibraryComponent_ng_container_9_Template, 2, 4, \"ng-container\", 3)(10, SingleLibraryComponent_ng_container_10_Template, 6, 6, \"ng-container\", 3)(11, SingleLibraryComponent_ng_template_11_Template, 36, 23, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(13, SingleLibraryComponent_ng_template_13_Template, 1, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.deleteFolderConfirmData.showDialog);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.deleteFileConfirmData.showDialog);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.deleteMultipleFilesConfirmData.showDialog);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c3, !ctx.showAddLibrary));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"header\", \"Upload files in folder \" + ctx.libraryToAdd.folder.name)(\"headerClass\", \"justify-content-center my-0\")(\"headerTextClass\", \"font-base font-semibold justify-content-center capitalize\")(\"showCloseDialogIcon\", true);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassMapInterpolate1(\"block-gradient-reverse \", ctx.extraGradientClass, \" py-2 md:py-0 border-round-xl px-1\");\n          i0.ɵɵstyleProp(\"height\", !ctx.isSmallScreen ? ctx.leftSideHeight + (ctx.authService.isStudent ? -15 : 0) + \"px\" : \"500px\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.withClassroom || ctx.authService.isStudent || ctx.classroomStudents.length === 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.classroomStudents && ctx.withClassroom && ctx.authService.isTeacher && ctx.classroomStudents.length > 1);\n        }\n      },\n      dependencies: [i10.NgClass, i10.NgForOf, i10.NgIf, i10.NgTemplateOutlet, i11.ClassroomsCheckListComponent, i12.ConfirmDialogComponent, i13.BlockViewerComponent, i14.TabView, i14.TabPanel, i15.DefaultValueAccessor, i15.NgControlStatus, i15.NgModel, i16.LibraryFileRowItemComponent, i17.SingleLibraryFolderItemComponent, i18.FilterPipe],\n      styles: [\".notes[_ngcontent-%COMP%]{background-color:#fff;padding:10px 0;border-radius:28px;font-size:15px;position:relative}.notes[_ngcontent-%COMP%]   .notes-right-side[_ngcontent-%COMP%]{width:100%;box-sizing:border-box;border-radius:28px;transition:width .3s ease-in-out;height:100%}@media only screen and (min-width: 992px){.notes[_ngcontent-%COMP%]   .notes-right-side[_ngcontent-%COMP%]{width:75%}}@media only screen and (min-width: 992px){.notes[_ngcontent-%COMP%]   .notes-right-side.expanded[_ngcontent-%COMP%]{width:97%}}.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]{width:100%;padding:4px 10px;border-radius:12px;transition:width .3s ease-in-out;box-shadow:0 3px 6px #00000029;box-sizing:border-box;align-items:center;height:100%;top:86px;background-color:#fff}@media only screen and (min-width: 992px){.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]{width:25%}}@media only screen and (min-width: 992px){.notes[_ngcontent-%COMP%]   .notes-left-side.collapsed[_ngcontent-%COMP%]{width:3%}}.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]   .link-main-color[_ngcontent-%COMP%]{width:150px;text-align:center;border-radius:50px}.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]   .classroom-title[_ngcontent-%COMP%]{padding:10px}.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]   .classroom-title.one-line[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]   .seperator[_ngcontent-%COMP%]{width:100%;height:1px;background-color:#d9ddf0;margin:3px 0}.tabs[_ngcontent-%COMP%]{position:relative;height:35rem}@media only screen and (min-width: 768px){.tabs[_ngcontent-%COMP%]{height:31rem}}@media only screen and (min-width: 768px) and (max-height: 768px){.tabs[_ngcontent-%COMP%]{height:28rem}}@media only screen and (max-width: 768px){.tabs[_ngcontent-%COMP%]{height:40rem}}.tab[_ngcontent-%COMP%]{float:left;width:48%;margin-right:1%;margin-left:1%}@media only screen and (min-width: 768px){.tab[_ngcontent-%COMP%]{margin-top:-2px}}.tab-label[_ngcontent-%COMP%]{position:relative;display:block;line-height:2.75em;height:3.7em;padding:0 1.318em;cursor:pointer;top:0;width:100%;transition:all .25s;border:1px solid #96a3e8;border-radius:31.5px;justify-content:center}@media only screen and (min-width: 768px){.tab-label[_ngcontent-%COMP%]{height:4em;justify-content:start}}.tab-content[_ngcontent-%COMP%]{height:100%;position:absolute;z-index:1;top:4.5em;left:0;color:#2c3e50;opacity:0}@media only screen and (max-width: 768px){.tab-content[_ngcontent-%COMP%]{top:4.5em;height:max-content}}.tab-switch[_ngcontent-%COMP%]:checked + .tab-label[_ngcontent-%COMP%]{background:#fff;color:#fff;border-bottom:0;border-right:.125rem solid #fff;border:none;transition:all .35s;z-index:1;opacity:1;background-image:linear-gradient(180deg,#9280fa,#8d7cf8,#8779f6,#8275f5,#7d72f3 44%,#736af0 56%,#6c68ee,#6664ed,#6161ea,#595ee8);border-radius:31.5px;width:100%}[_nghost-%COMP%]     .p-dropdown:not(.p-disabled):hover{border:2px solid rgb(228,185,84)}  .no-label>.p-button-label{display:none}input[type=file][_ngcontent-%COMP%]{visibility:hidden;width:10px}input[type=date][_ngcontent-%COMP%]{position:relative;padding:10px}input[type=date][_ngcontent-%COMP%]::-webkit-calendar-picker-indicator{color:transparent;background:none;z-index:1}input[type=date][_ngcontent-%COMP%]:before{color:transparent;background:none;display:block;font-family:FontAwesome;content:\\\"\\\\f073\\\";width:20px;height:25px;position:absolute;top:12px;right:12px;color:#999}.image-preview[_ngcontent-%COMP%]{height:10rem;margin:1rem 0;border-radius:50%;border:3px solid var(--main-color)}.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{height:100%}[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-header{padding:clamp(.38rem,.47vw + .28rem,.75rem)}[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-items .p-dropdown-item{margin-bottom:clamp(.38rem,.47vw + .28rem,.75rem)}.country-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:clamp(.75rem,.31vw + .69rem,1rem);font-size:clamp(.88rem,.16vw + .84rem,1rem)}.country-item[_ngcontent-%COMP%]   .country-name[_ngcontent-%COMP%]{white-space:pre-line}.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;display:inline-block;vertical-align:middle}.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;clip-path:circle()}.country-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:32px;margin-right:.5rem}.country-item[_ngcontent-%COMP%]   img.smaller[_ngcontent-%COMP%]{width:16px}.info-element[_ngcontent-%COMP%]{padding:10px 0}.note[_ngcontent-%COMP%]{border-radius:40px;border:1px solid #eaf0f5;margin-top:2px;position:relative}.note[_ngcontent-%COMP%]:before{pointer-events:none;content:\\\"\\\";position:absolute;inset:0;border-radius:30px;padding:2px}.note.isFavNote[_ngcontent-%COMP%]{background-image:linear-gradient(90deg,#3874f5,#6d6beb 29%,#9167e0 53%,#aa60d2 68%,#bd59c5 77%,#ca53b4 84%,#d54da6 89%,#dc5096,#e15188 97%,#e3547a)}.note.isFavNote[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]{color:#fff}.note.isFavNote[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .note.isFavNote[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]{border:1px solid white}.note.isFavNote[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .note.isFavNote[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%]{background-color:#fff}.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]{cursor:pointer;display:flex;align-items:center;color:var(--main-color)}.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .section-arrow[_ngcontent-%COMP%]{width:40px}.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;width:100%}@media screen and (max-width: 768px){.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start;margin-left:20px}}.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%]{margin-left:20px}@media screen and (max-width: 768px){.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%]{margin-left:0}}.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%]   .note-dates[_ngcontent-%COMP%]{display:flex;margin-top:10px}@media screen and (max-width: 768px){.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%]   .note-dates[_ngcontent-%COMP%]{flex-direction:column}}.note-info[_ngcontent-%COMP%]{display:flex;flex-direction:column}@media only screen and (max-width: 768px){.note-info[_ngcontent-%COMP%]{margin-top:0}}.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]{display:flex}@media only screen and (max-width: 768px){.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]{justify-content:end}}.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]{display:flex;align-items:center;margin-right:10px;margin-left:auto}@media only screen and (max-width: 768px){.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]{margin-left:0}}.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-icon[_ngcontent-%COMP%], .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-text[_ngcontent-%COMP%]{margin-left:4px;cursor:pointer}@media screen and (max-width: 768px){.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-icon[_ngcontent-%COMP%], .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-text[_ngcontent-%COMP%]{margin-left:0}}.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-icon[_ngcontent-%COMP%]{width:20px;display:flex;justify-content:center}.note-info[_ngcontent-%COMP%]   .note-info-bottom[_ngcontent-%COMP%]{display:flex;justify-content:end;text-align:right;margin-right:10px;font-size:.725rem;color:#2d3b8e}.note-content[_ngcontent-%COMP%]{overflow:hidden;transition:height .3s ease-out}.note-text-content[_ngcontent-%COMP%]{padding:20px 30px}.update-button[_ngcontent-%COMP%]{padding:5px 30px;background-color:var(--light-purple);text-align:center;border-radius:10px;color:#fff;margin-top:15px;cursor:pointer}.update-button[_ngcontent-%COMP%]:hover{background-color:var(--main-color)}.section-arrow[_ngcontent-%COMP%]{overflow:hidden;transition-duration:.2s;transition-property:transform;cursor:pointer}.note-burger[_ngcontent-%COMP%], .note-burger-group[_ngcontent-%COMP%]{position:relative;border-radius:50%;min-width:36px;min-height:36px;border:2px solid #6563ec;display:flex;align-items:center;box-sizing:border-box;justify-content:center;cursor:pointer}.note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%]{width:4px;height:4px;border-radius:50%;background-color:#6563ec;margin:2px}.note-burger[_ngcontent-%COMP%]   .inner-circle-group[_ngcontent-%COMP%], .note-burger-group[_ngcontent-%COMP%]   .inner-circle-group[_ngcontent-%COMP%]{width:34px;height:34px;border-radius:50%;background-color:transparent}.note-menu[_ngcontent-%COMP%]{width:300px;position:absolute;right:30px;top:69px;border-radius:12px;border:1px solid var(--main-color);box-sizing:border-box;transition-duration:.2s;transition-property:transform;background-color:#fff;z-index:1;display:none;flex-direction:column;padding:10px}@media screen and (max-width: 1124px){.note-menu[_ngcontent-%COMP%]{right:30px}}.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]{display:flex;flex-direction:column}.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]{margin-top:5px;display:flex;align-items:center}.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-text[_ngcontent-%COMP%]{margin-left:10px;cursor:pointer}.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-icon[_ngcontent-%COMP%]{width:20px;display:flex;justify-content:center}.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]:first-child{margin-top:0}.plain[_ngcontent-%COMP%]{margin:-13px 2px 0;border-top:0;border-radius:0 0 12px 12px;position:relative}.filter-white[_ngcontent-%COMP%]{filter:invert(100%) sepia(0%) saturate(1%) hue-rotate(328deg) brightness(200%) contrast(101%)}.note-info-width[_ngcontent-%COMP%]{min-width:10rem}.note-title-text[_ngcontent-%COMP%]{font-weight:400;font-size:1rem;letter-spacing:.01em;text-align:left;max-width:17rem}@media only screen and (max-width: 768px){.note-title-text[_ngcontent-%COMP%]{max-width:9rem;font-size:.825rem}}.accordion[_ngcontent-%COMP%]{border-radius:4px;margin-top:10px}.header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:8px;cursor:pointer;border-radius:31px;box-shadow:3px 3px 6px 2px #00000029;z-index:4;position:relative;border:1px solid transparent}.header.isPinned[_ngcontent-%COMP%]{color:#fff;background-image:linear-gradient(90deg,#5060c3 0% 13%,#5060c3 25% 37%,#5060c3 50% 63%,#5060c3 75% 87%,#5060c3)}.header.isPinned[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.isPinned[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]{border:2px solid white}.header.isPinned[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.isPinned[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%]{background-color:#fff}.header.isPinned[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{background:transparent;border:2px solid white}.header.isPinnedAndFavourite[_ngcontent-%COMP%]{color:#fff;background-image:linear-gradient(90deg,#4253b3,#495abc,#5060c3,#5767cb,#5f6ed3 44%,#6674db 56%,#6a78e2,#717fea,#7887f2,#808efa)}.header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]{border:2px solid white}.header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%]{background-color:#fff}.header.isPinnedAndFavourite[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{background:transparent;border:2px solid white}.header.opened[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]{border:1px solid transparent;color:#fff;background-color:#7f8dfa}.header.opened.activated[_ngcontent-%COMP%], .header.opened2.activated[_ngcontent-%COMP%]{background-color:#7f8dfa}.header.opened[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.opened[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]{border:2px solid white}.header.opened[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.opened[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%]{background-color:#fff}.header.opened[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]{border:1px solid white;background:transparent;width:36px;height:36px;border-radius:50%;display:flex;justify-content:center;align-items:center}.content[_ngcontent-%COMP%]{padding:10px}.down-arrow[_ngcontent-%COMP%]{transition:transform .3s ease-in-out}.down-arrow.pi-chevron-up[_ngcontent-%COMP%]{transform:rotate(360deg)}.circle[_ngcontent-%COMP%]{background-image:linear-gradient(180deg,#9280fa,#8d7cf8,#8779f6,#8275f5,#7d72f3 44%,#736af0 56%,#6c68ee,#6664ed,#6161ea,#595ee8);width:36px;height:36px;border-radius:50%;display:flex;justify-content:center;align-items:center}.circle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#fff;font-size:20px}.rotate[_ngcontent-%COMP%]{transform:rotate(180deg);transition:transform .3s ease-in-out}.accordion-content[_ngcontent-%COMP%]{max-height:0;overflow:hidden}.open[_ngcontent-%COMP%]{background-color:#fff;margin-top:-20px;border-bottom-left-radius:30px;border-bottom-right-radius:30px;height:370px;max-height:370px;padding:20px 3px 3px}@media only screen and (max-width: 768px){.open[_ngcontent-%COMP%]{height:410px;max-height:410px}}.btn-action[_ngcontent-%COMP%]{width:200px}@media only screen and (max-width: 768px){.btn-action[_ngcontent-%COMP%]{width:auto}}[_nghost-%COMP%]     .p-editor-container .p-editor-toolbar.ql-snow{border:none;padding:0!important}[_nghost-%COMP%]     .p-editor-container .p-editor-content .ql-editor{background:transparent;padding:4px}[_nghost-%COMP%]     .p-editor-container .p-editor-content .ql-editor p{font-size:1rem}[_nghost-%COMP%]     .p-editor-container .p-editor-content .ql-editor[contenteditable=true]{border:1px solid var(--my-gray);border-radius:10px}[_nghost-%COMP%]     .circle.ql-snow{border:none}[_nghost-%COMP%]     .p-editor-container .p-editor-content.ql-snow{border:none}[_nghost-%COMP%]     .ql-snow .ql-tooltip{transform:translate(80%,-50%)}@media only screen and (max-width: 768px){[_nghost-%COMP%]     .ql-snow .ql-tooltip{transform:translate(45%,-50%)}}  .p-menu .p-menuitem-link{padding:.5rem 1rem!important}#add-note[_ngcontent-%COMP%]{background-color:var(--gray);padding:30px;border-radius:12px;margin-top:30px}.add-note-btn-icon[_ngcontent-%COMP%]{width:25px;height:25px}.notes-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center}.notes-header[_ngcontent-%COMP%]   .notes-header-title[_ngcontent-%COMP%]{font-size:18px;color:var(--main-color);margin-left:10px;font-weight:700}.notes-header[_ngcontent-%COMP%]   .notes-header-actions[_ngcontent-%COMP%]{display:flex;align-items:center}.notes-header[_ngcontent-%COMP%]   .notes-header-actions[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{margin-right:.7em}.notes-header[_ngcontent-%COMP%]   .notes-header-actions[_ngcontent-%COMP%]   .light-purple-circle-button[_ngcontent-%COMP%]{width:20px;height:20px}.note-menu-row[_ngcontent-%COMP%]{display:flex;justify-content:space-evenly;border-radius:40px}.note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]{display:flex;align-items:center;position:relative}.note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-text[_ngcontent-%COMP%]{cursor:pointer}.note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-icon[_ngcontent-%COMP%]{width:20px;display:flex;justify-content:center}.rte[_ngcontent-%COMP%]{margin-top:20px;border-radius:20px;z-index:0!important}[_nghost-%COMP%]     .e-toolbar-items{border-radius:20px!important}[_nghost-%COMP%]     .e-rte-toolbar{border-radius:20px 20px 0 0!important}[_nghost-%COMP%]     .e-rte-content{border-radius:0 0 20px 20px!important;border-top:0px!important}[_nghost-%COMP%]     .e-date-icon{position:absolute!important;left:0!important}[_nghost-%COMP%]     .e-input{margin-left:30px!important}.notes-popup[_ngcontent-%COMP%]{position:absolute;top:40px;width:200px;border-radius:12px;background-color:#fff;color:var(--main-color);right:0;box-shadow:0 -4px 4px #00000040;max-height:400px;z-index:10000}.notes-filters-title[_ngcontent-%COMP%]{font-size:18px;font-weight:700}.notes-filters-filters[_ngcontent-%COMP%]{font-size:15px}.notes-filters-filters[_ngcontent-%COMP%]   .filter-row[_ngcontent-%COMP%]{margin-top:15px;display:flex;align-items:center}.notes-filters-filters[_ngcontent-%COMP%]   .filter-row[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%]{margin-left:15px}.notes-filters-filters[_ngcontent-%COMP%]   .filter-col[_ngcontent-%COMP%]{margin-top:15px}.send-classroom-title[_ngcontent-%COMP%]{font-size:18px;font-weight:700}.send-classroom-user[_ngcontent-%COMP%]{font-size:15px;margin-left:10px;display:flex}.send-button[_ngcontent-%COMP%]{background-color:var(--main-color);width:100%;position:sticky;bottom:0;left:0;padding:8px;box-sizing:border-box;color:#fff;text-align:center;border-radius:12px;cursor:pointer}.notes-height[_ngcontent-%COMP%]{height:calc(100% - 50px)}.btns[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{width:100%}[_nghost-%COMP%]     .ngx-pagination{padding:0!important}.add[_ngcontent-%COMP%]{width:32px;height:32px}.add.lg[_ngcontent-%COMP%]{width:2.1rem;height:2.1rem}.add.empty[_ngcontent-%COMP%]{width:7.1rem;height:auto}  .p-tabview .p-tabview-panels{background-color:transparent;padding:0;justify-content:center;height:100%;display:flex;flex-direction:column}.responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}.modal[_ngcontent-%COMP%]{min-width:50vw}@media only screen and (max-width: 768px){.modal[_ngcontent-%COMP%]{min-width:80vw}}@keyframes _ngcontent-%COMP%_collapse{0%{width:auto}to{width:0}}.notes[_ngcontent-%COMP%]{background-color:transparent;padding:0;justify-content:space-evenly}.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]{padding:10px;border-radius:12px;top:86px;transition:width .3s ease-in-out}.notes[_ngcontent-%COMP%]   .notes-left-side.collapsed[_ngcontent-%COMP%]{width:3%}.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#00000050}.notes[_ngcontent-%COMP%]   .notes-right-side[_ngcontent-%COMP%]{border-radius:28px;transition:width .3s ease-in-out}.notes[_ngcontent-%COMP%]   .notes-right-side.expanded[_ngcontent-%COMP%]{width:97%}.notes[_ngcontent-%COMP%]   .add[_ngcontent-%COMP%]{width:32px;height:32px}.notes[_ngcontent-%COMP%]   .add.lg[_ngcontent-%COMP%]{width:2.1rem;height:2.1rem}.notes[_ngcontent-%COMP%]   .add.empty[_ngcontent-%COMP%]{width:7.1rem;height:auto}.notes[_ngcontent-%COMP%]   .folder-nav-buttons[_ngcontent-%COMP%]{border-right:1.5px solid #002CCF;margin-right:10px;display:flex;justify-content:center;align-items:self-end;gap:5px}.notes[_ngcontent-%COMP%]   .folder-nav-buttons[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:11px}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]{list-style:none;padding:0;margin:8px;overflow:auto}@media only screen and (max-width: 768px){.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]{margin:0;width:100%}}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .bg-grad-blue[_ngcontent-%COMP%]{background:linear-gradient(#657aef,#1a266c)}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]{border-radius:40px;display:flex;background-color:#fff;align-items:center;margin-bottom:10px}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   svg.library-file-icon[_ngcontent-%COMP%]{padding:8px;margin-top:5px}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .library-file-details[_ngcontent-%COMP%]{width:100%;display:flex}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .library-file-details[_ngcontent-%COMP%]   .library-file-meta[_ngcontent-%COMP%]{margin-top:5px;display:flex}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .library-file-details[_ngcontent-%COMP%]   .library-file-meta[_ngcontent-%COMP%]   span.file-level[_ngcontent-%COMP%]{color:#fff;background:#444070;padding:6px;border-radius:50px}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .library-file-details[_ngcontent-%COMP%]   .library-file-meta[_ngcontent-%COMP%]   span.file-category[_ngcontent-%COMP%]{color:#fff;background-color:#a44fd0;padding:6px;border-radius:50px;margin-left:6px}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]{width:180px;position:absolute;left:92.5%;top:69px;border-radius:12px;border:1px solid var(--main-color);box-sizing:border-box;transition-duration:.2s;transition-property:transform;background-color:#fff;z-index:1;display:none;flex-direction:column;padding:10px}@media screen and (max-width: 1124px){.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]{right:30px}}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]{display:flex;flex-direction:column}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]   .lib-file-menu-col[_ngcontent-%COMP%]{margin-top:5px;display:flex;align-items:center}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]   .lib-file-menu-col[_ngcontent-%COMP%]   .lib-file-menu-text[_ngcontent-%COMP%]{margin-left:10px;cursor:pointer}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]   .lib-file-menu-col[_ngcontent-%COMP%]   .lib-file-menu-icon[_ngcontent-%COMP%]{width:20px;display:flex;justify-content:center}.notes[_ngcontent-%COMP%]   .library-file-list[_ngcontent-%COMP%]   .library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]:first-child{margin-top:0}.libs[_ngcontent-%COMP%]{background:#fff;padding:30px;margin-bottom:15px;border-radius:28px}[_nghost-%COMP%]     .tawk-card-primary{background-color:var(--main-color)!important}.inner-folders[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap}.inner-folders[_ngcontent-%COMP%]   .inner-folder[_ngcontent-%COMP%]{margin:29px 15px 15px;display:flex;align-items:center;justify-content:center;color:var(--main-color);font-weight:700}.inner-folders[_ngcontent-%COMP%]   .inner-folder[_ngcontent-%COMP%]::selection{background-color:#fff}.inner-folders[_ngcontent-%COMP%]   .new-folder[_ngcontent-%COMP%]{color:var(--main-color);background-color:#fff}.inner-folders[_ngcontent-%COMP%]   .new-folder[_ngcontent-%COMP%]::selection{color:var(--main-color);background-color:#fff}.folder-path[_ngcontent-%COMP%]{display:flex;margin:15px 0;font-size:18px;align-items:center}.close-img[_ngcontent-%COMP%]{width:20px;position:absolute;padding:5px;border-radius:50%;border:1px solid black;left:90px;z-index:10;background:#fff;top:11px}.my-folder[_ngcontent-%COMP%]{border-radius:8px;position:relative;z-index:2}.my-folder[_ngcontent-%COMP%]:before{position:absolute;width:100%;height:100%;content:\\\"\\\";background:linear-gradient(#002ccf,#c9d2ff00);opacity:.65;border-top-left-radius:10px;border-top-right-radius:10px;z-index:-1;left:50%;transform:translate(-50%);top:0}.my-folder.purple-folder-bg[_ngcontent-%COMP%]:before{background:linear-gradient(#6262eb,#c9d2ff00)}.wrapper[_ngcontent-%COMP%]{max-width:50rem;width:100%;margin:0 auto}.tabs[_ngcontent-%COMP%]{position:relative;margin:3rem 0;height:18.75rem}.tabs[_ngcontent-%COMP%]:before, .tabs[_ngcontent-%COMP%]:after{content:\\\"\\\";display:table}.tabs[_ngcontent-%COMP%]:after{clear:both}.tab[_ngcontent-%COMP%]{float:left;width:50%}.tab-switch[_ngcontent-%COMP%]{display:none}.tab-label[_ngcontent-%COMP%]{position:relative;display:block;line-height:2.75em;height:4em;padding:0 1.618em;cursor:pointer;top:0;opacity:.6;width:76%;transition:all .25s}.tab-label[_ngcontent-%COMP%]:hover{top:-.25rem;transition:top .25s}.tab-content[_ngcontent-%COMP%]{height:100%;position:absolute;z-index:1;top:4em;left:0;color:#2c3e50;opacity:0;overflow:auto}.tab-switch[_ngcontent-%COMP%]:checked + .tab-label[_ngcontent-%COMP%]{background:#fff;color:#2c3e50;border-bottom:0;border-right:.125rem solid #fff;transition:all .35s;z-index:1;opacity:1;background-image:linear-gradient(180deg,#6580e2,#758ce6,#8296e8,#8e9feb,#9faeef 44%,#acb8f1 56%,#b8c3f4,#c5cef6,#d2d9f9,#dfe4fb);border-top-left-radius:.5rem;border-top-right-radius:.5rem;width:100%}.tab-switch[_ngcontent-%COMP%]:checked + label[_ngcontent-%COMP%] + .tab-content[_ngcontent-%COMP%]{z-index:2;opacity:1;transition:all .35s;width:100%}.horizontal-scroll-menu[_ngcontent-%COMP%]{overflow:auto;white-space:nowrap}.folder-options-icon[_ngcontent-%COMP%]{right:-15px;transform:rotate(90deg)}  .p-tabview .p-tabview-panels{background-color:transparent;padding:0}  .p-tabview .p-tabview-nav{background:transparent;border:none}  .p-tabview .p-tabview-nav li .p-tabview-nav-link,   .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link{background:transparent;border:none}  .p-tabview .p-tabview-nav .p-tabview-ink-bar{background-color:#8a7af7;bottom:5px}  .p-tabview .p-tabview-title{color:#4354b4}.filter-blue[_ngcontent-%COMP%]{opacity:.7}.abs-header-top[_ngcontent-%COMP%]{top:-68px;width:100%;z-index:4}.abs-header-top.more[_ngcontent-%COMP%]{top:-130px}@media only screen and (max-width: 768px){.abs-header-top.more[_ngcontent-%COMP%]{top:-218px;flex-direction:column}}.icon-times-blue[_ngcontent-%COMP%]{color:#6a79e2}*[_ngcontent-%COMP%]::-webkit-scrollbar{width:10px}@media only screen and (max-width: 768px){.allFiles[_ngcontent-%COMP%]{max-height:23rem}}\"]\n    });\n  }\n  return SingleLibraryComponent;\n})();", "map": {"version": 3, "names": ["take", "UserRole", "SubSink", "DialogService", "LibraryFileActionsDialogComponent", "FilterPipe", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "SingleLibraryComponent_ng_container_0_Template_app_confirm_dialog_rejectSelected_1_listener", "$event", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onRejectFolderDelete", "SingleLibraryComponent_ng_container_0_Template_app_confirm_dialog_confirmSelected_1_listener", "onAcceptFolderDelete", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "deleteFolderConfirmData", "message", "SingleLibraryComponent_ng_container_1_Template_app_confirm_dialog_rejectSelected_1_listener", "_r4", "onRejectFileDelete", "SingleLibraryComponent_ng_container_1_Template_app_confirm_dialog_confirmSelected_1_listener", "onAcceptFileDelete", "deleteFileConfirmData", "SingleLibraryComponent_ng_container_2_Template_app_confirm_dialog_rejectSelected_1_listener", "_r5", "SingleLibraryComponent_ng_container_2_Template_app_confirm_dialog_confirmSelected_1_listener", "onAcceptFileDeleteMultiple", "deleteMultipleFilesConfirmData", "ɵɵelementContainer", "ɵɵtemplate", "SingleLibraryComponent_ng_container_9_ng_container_1_Template", "classInfoGeneralTemplate_r6", "ɵɵpureFunction1", "_c4", "classroom", "SingleLibraryComponent_ng_container_10_ng_container_5_ng_container_2_Template", "selectedStudent_r8", "firstName", "ɵɵpureFunction2", "_c6", "SingleLibraryComponent_ng_container_10_Template_p_tabView_onChange_1_listener", "_r7", "onTabChangeFilterFilesPerStudent", "SingleLibraryComponent_ng_container_10_ng_container_4_Template", "SingleLibraryComponent_ng_container_10_ng_container_5_Template", "classroomStudents", "length", "_c5", "SingleLibraryComponent_ng_template_11_img_7_Template_img_click_0_listener", "_r10", "setGroupActions", "_c9", "showGroupActions", "SingleLibraryComponent_ng_template_11_img_8_Template_img_click_0_listener", "_r11", "onShowUploadDialog", "SingleLibraryComponent_ng_template_11_div_10_div_16_Template_app_classrooms_check_list_close_1_listener", "_r13", "showShare", "SingleLibraryComponent_ng_template_11_div_10_div_16_Template_app_classrooms_check_list_checkedList_1_listener", "onCheckedList", "SingleLibraryComponent_ng_template_11_div_10_div_16_Template_div_click_2_listener", "shareLibraryFiles", "ɵɵtext", "ɵɵelement", "SingleLibraryComponent_ng_template_11_div_10_Template_div_click_4_listener", "_r12", "openShareDialog", "SingleLibraryComponent_ng_template_11_div_10_Template_div_click_6_listener", "moveMultipleFiles", "SingleLibraryComponent_ng_template_11_div_10_Template_div_click_11_listener", "deleteMultipleFiles", "SingleLibraryComponent_ng_template_11_div_10_div_16_Template", "SingleLibraryComponent_ng_template_11_div_18_Template_app_single_library_folder_item_deleteFolder_1_listener", "_r14", "deleteFolderSelected", "SingleLibraryComponent_ng_template_11_div_18_Template_app_single_library_folder_item_openFolder_1_listener", "openFolderSelected", "innerFolder_r15", "hasLimitedOptions", "SingleLibraryComponent_ng_template_11_img_19_Template_img_click_0_listener", "_r16", "createFolder", "SingleLibraryComponent_ng_template_11_img_24_Template_img_click_0_listener", "_r17", "openFolder", "folderPath", "SingleLibraryComponent_ng_template_11_div_30_Template_div_click_0_listener", "folder_r19", "_r18", "$implicit", "ɵɵtextInterpolate1", "name", "SingleLibraryComponent_ng_template_11_ng_container_32_Template_i_click_2_listener", "_r20", "ɵɵtwoWayListener", "SingleLibraryComponent_ng_template_11_ng_container_32_Template_input_ngModelChange_3_listener", "ɵɵtwoWayBindingSet", "folderName", "SingleLibraryComponent_ng_template_11_ng_container_32_Template_button_click_4_listener", "onCreateFolder", "ɵɵtwoWayProperty", "SingleLibraryComponent_ng_template_11_ng_container_33_ng_container_4_Template", "SingleLibraryComponent_ng_template_11_ng_container_33_ng_container_5_Template", "authService", "isStudent", "<PERSON><PERSON><PERSON>er", "SingleLibraryComponent_ng_template_11_ng_container_34_Template_img_click_2_listener", "_r21", "SingleLibraryComponent_ng_template_11_div_35_ng_container_2_Template_app_library_file_row_item_fileDeleted_1_listener", "_r22", "onFileDeleted", "SingleLibraryComponent_ng_template_11_div_35_ng_container_2_Template_app_library_file_row_item_libraryChecked_1_listener", "onLibraryChecked", "SingleLibraryComponent_ng_template_11_div_35_ng_container_2_Template_app_library_file_row_item_fileUnshared_1_listener", "onFileUnshared", "withClassroom", "classroom_r24", "libFiles_r23", "isSmallScreen", "isInTab", "folders", "availableActions", "SingleLibraryComponent_ng_template_11_div_35_ng_container_2_Template", "ɵɵstyleProp", "fileListHeight", "ɵɵpipeBind2", "getFilteredFiles", "uniqueFiles_r25", "_c10", "term", "SingleLibraryComponent_ng_template_11_Template_input_ngModelChange_5_listener", "_r9", "SingleLibraryComponent_ng_template_11_img_7_Template", "SingleLibraryComponent_ng_template_11_img_8_Template", "SingleLibraryComponent_ng_template_11_div_10_Template", "SingleLibraryComponent_ng_template_11_div_18_Template", "SingleLibraryComponent_ng_template_11_img_19_Template", "SingleLibraryComponent_ng_template_11_img_24_Template", "SingleLibraryComponent_ng_template_11_div_30_Template", "SingleLibraryComponent_ng_template_11_ng_container_32_Template", "SingleLibraryComponent_ng_template_11_ng_container_33_Template", "SingleLibraryComponent_ng_template_11_ng_container_34_Template", "SingleLibraryComponent_ng_template_11_div_35_Template", "_c7", "ɵɵclassMapInterpolate1", "extraGradientClass", "libraryTitle", "canShare", "canUpload", "_c8", "libraryToAdd", "folder", "innerFolders", "showInput", "files", "SingleLibraryComponent", "constructor", "libraryService", "classroomService", "generalService", "confirmationService", "toastService", "layoutService", "dialogService", "userService", "showHeader", "isMyFiles", "StudentIdsToSend", "rootFolder", "isDisabled", "isDisabledWholeClass", "isMoveRadioCheked", "moveFileId", "moveFolderId", "UserRoles", "role", "loggedInUser", "inHomework", "task", "classroomIdToAddLib", "subs", "showFilters", "showAddLibrary", "foldersWithFiles", "allLibrariesWithFiles", "title", "currentPathLibraries", "user", "inClassroom", "showingMine", "filteredFiles", "libsSharedWith", "libsSharedBy", "studentsToShareWith", "showDialog", "checkedLibraryFiles", "currentUploadFolder", "classroomStudentsIds", "ngOnInit", "Array", "from", "Set", "map", "student", "aspUserId", "find", "setStudentsToShareWith", "oncollapseNotesLeftSideChanged", "console", "log", "getLoggedInUser", "getUserRole", "items", "label", "icon", "command", "event", "sink", "deviceKind", "subscribe", "w768up", "res", "sideMenuHeight", "updateFolderListener", "folderId", "lib", "found", "file", "libraryFiles", "fileId", "libraryFile", "parent", "initOpenFolderListener", "getClassRooms", "updateListener", "getFoldersWithFiles", "deleteListener", "filter", "el", "initializeEmptyLibraryObject", "ngOnChanges", "initFiles", "ngAfterViewInit", "ngOnDestroy", "unsubscribe", "setShowToastmessage", "severity", "summary", "detail", "openDialogWithComponent", "action", "dialogsubTitle", "dialogTitle", "result", "shareMultipleLibraryFiles", "checked", "push", "path", "set", "for<PERSON>ach", "users", "classroomId", "ids", "fileIds", "send", "studentIds", "sendShareToApi", "sendParams", "shareFileWithClassUsers", "pipe", "onShowAddLibrary", "deleteFolder", "add", "getLMSUserClassrooms", "id", "response", "teacherClassrooms", "deleteLibFolder", "getFileName", "n", "lastIndexOf", "substring", "showSharePopup", "document", "getElementById", "style", "display", "top", "onFolderSelect", "moveFile", "moveFileAPI", "i", "folderWithFiles", "dummyId", "initializeFilesWithFolders", "some", "getLibrariesOnRootFolders", "getInnerFolders", "copyFolders", "innerFolder", "getLibrariesOfFolder", "getFolderPathOfFolder", "unshift", "folderToDelete", "setFolderToDelete", "getFolderToDelete", "removeFolderByFolderId", "setUpdateListener", "onTagChoosen", "isLevel", "applyToAll", "arrLibrary", "levels", "categories", "tag", "index", "index2", "findIndex", "splice", "getUserCreatedLibFolders", "r", "newFolder", "created<PERSON>y", "targetElementName", "isPreviewingFromChat", "myElement", "setNewFileUpload", "getBoundingClientRect", "width", "dialogWidth", "toFixed", "onSubmit", "height", "toSend", "libFiles", "StudentIds", "uploadClassroomFilesToFolder", "setUploadFiles", "mapLibraryToAddFiles", "e", "uploadFilesToFolder", "concat", "uploadFinished", "filePath", "db<PERSON><PERSON>", "setLibraryFile", "deleteFile", "delete", "libFile", "filterDelteFromLibraryFiles", "deleteFileAPI", "includes", "getLibraryFile", "removeLibraryFileByFileId", "filteredLibraryFiles", "moveFileToFolder", "isLeftsideCollapsed", "setTimeout", "currentStudent", "sharedWith", "studentId", "unique", "uniqueFilteredFiles", "self", "f", "libraryUpdatedListener", "isNullishObject", "_", "ɵɵdirectiveInject", "i1", "LibraryService", "i2", "ClassroomService", "i3", "GeneralService", "i4", "AuthService", "i5", "ConfirmationService", "i6", "ToastService", "i7", "LayoutService", "i8", "i9", "UserService", "_2", "selectors", "viewQuery", "SingleLibraryComponent_Query", "rf", "ctx", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "SingleLibraryComponent_Template", "SingleLibraryComponent_ng_container_0_Template", "SingleLibraryComponent_ng_container_1_Template", "SingleLibraryComponent_ng_container_2_Template", "SingleLibraryComponent_Template_app_block_viewer_closeDialogEvent_6_listener", "_r1", "SingleLibraryComponent_ng_container_9_Template", "SingleLibraryComponent_ng_container_10_Template", "SingleLibraryComponent_ng_template_11_Template", "ɵɵtemplateRefExtractor", "SingleLibraryComponent_ng_template_13_Template", "_c3", "leftSideHeight"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\library\\library\\components\\single-library\\single-library.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\library\\library\\components\\single-library\\single-library.component.html"], "sourcesContent": ["import { Component, Input, OnInit, ViewChild } from '@angular/core';\r\nimport { ConfirmationService, MenuItem } from 'primeng/api';\r\nimport { filter, switchMap, take, tap } from 'rxjs/operators';\r\nimport { Classroom, ClassroomType } from 'src/app/core/models/classroom.model';\r\nimport { HomeworkTask } from 'src/app/core/models/homework.model';\r\nimport { Folder, FoldersWithFiles, Library, LibraryFile, UploadFilesToFolderRequest } from 'src/app/core/models/library.model';\r\nimport { User, UserRole } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { LibraryService } from 'src/app/core/services/library.service';\r\nimport { environment } from 'src/environments/environment';\r\nimport { SubSink } from 'subsink';\r\nimport {MessageService} from 'primeng/api';\r\nimport { fork<PERSON>oin } from 'rxjs';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { LayoutService } from 'src/app/core/services/layout.service';\r\nimport { DialogService } from 'primeng/dynamicdialog';\r\nimport { LibraryFileActionsDialogComponent } from '../library-file-actions-dialog/library-file-actions-dialog.component';\r\nimport { UserService } from 'src/app/core/services/user.service';\r\nimport { FilterPipe } from 'src/app/core/pipes/filter.pipe';\r\n\r\n@Component({\r\n  selector: 'app-single-library',\r\n  templateUrl: './single-library.component.html',\r\n  styleUrls: ['./single-library.component.scss'],\r\n  providers: [DialogService, FilterPipe]\r\n})\r\nexport class SingleLibraryComponent implements OnInit {\r\n\r\n  @ViewChild('addLibrary', {static: true}) public addLibrary: any;\r\n  @ViewChild('leftSide', {static: true}) public leftSide: any;\r\n  @ViewChild('mainWrapper', {static: true}) public mainWrapper: any;\r\n  \r\n  @Input() showHeader = true;\r\n  teacherClassrooms: any;\r\n  sharedByMeLibFolders: any;\r\n  sharedWithMeLibFolders: any;\r\n  isMyFiles = false;\r\n  showShare: boolean = false;\r\n  StudentIdsToSend: string[] = [];\r\n  folderPath: Folder[] = [this.libraryService.rootFolder];\r\n  fileURL: string | undefined;\r\n  classroomId: number | undefined;\r\n  isDisabled = false;\r\n  isDisabledWholeClass = false;\r\n  isMoveRadioCheked = false;\r\n  moveFileId = 0;\r\n  moveFolderId = 0; \r\n  items!: MenuItem[];\r\n  UserRoles = UserRole;\r\n  public role: string = \"\";\r\n  public loggedInUser: User = {} as User;\r\n  @Input() inHomework: boolean = false;\r\n  @Input() task: HomeworkTask = {} as HomeworkTask;\r\n  @Input() classroomIdToAddLib: number = 0;\r\n  @Input() classroomStudents: any;\r\n  @Input() withClassroom = false;\r\n  @Input() canUpload = true;\r\n  @Input() canShare = true;\r\n  @Input() hasLimitedOptions = false;\r\n  @Input() availableActions = [];\r\n  @Input() leftSideHeight!: number;\r\n  @Input() libraryTitle = 'My Files';\r\n  @Input() isInTab = false;\r\n  @Input() extraGradientClass = 'purple';\r\n  @Input() fileListHeight = 0;\r\n  \r\n\r\n  private subs = new SubSink()\r\n  term: any;\r\n  showFilters: boolean = false;\r\n  showGroupActions: boolean = false;\r\n  showAddLibrary: boolean = false;\r\n  foldersWithFiles: FoldersWithFiles[] = [];\r\n  @Input() allLibrariesWithFiles: FoldersWithFiles[] = [];\r\n  @Input() title: string = \"\";\r\n  currentPathLibraries: Library[] = [];\r\n  innerFolders: Folder[] = [];\r\n  libraryToAdd: Library = {} as Library;\r\n  folderName: string = \"\";\r\n  showInput: boolean = false;\r\n  user: User = {} as User;\r\n  folders: Folder[] = []\r\n  inClassroom: boolean = true;\r\n  showingMine: boolean = true;\r\n  files: LibraryFile[] = [];\r\n  filteredFiles: LibraryFile[] = [];\r\n  libsSharedWith: FoldersWithFiles[] = [];\r\n  libsSharedBy: FoldersWithFiles[] = [];\r\n  studentsToShareWith: any[] = [];\r\n  deleteFolderConfirmData = {\r\n    showDialog: false,\r\n    message: 'Delete folder?'\r\n  }\r\n  deleteFileConfirmData = {\r\n    showDialog: false,\r\n    message: 'Delete File?'\r\n  }\r\n  deleteMultipleFilesConfirmData = {\r\n    showDialog: false,\r\n    message: 'Delete selected files?'\r\n  }\r\n  isLeftsideCollapsed: any;\r\n  checkedLibraryFiles: LibraryFile[] = [];\r\n  isSmallScreen = false;\r\n  currentUploadFolder = {} as Folder;\r\n  classroomStudentsIds = [];\r\n  constructor(\r\n    private libraryService: LibraryService,\r\n    private classroomService: ClassroomService,\r\n    private generalService: GeneralService,\r\n    public authService: AuthService,\r\n    private confirmationService: ConfirmationService,\r\n    private toastService: ToastService,\r\n    private layoutService: LayoutService,\r\n    private dialogService: DialogService,\r\n    private userService: UserService,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n\r\n    // if (this.libraryService.isPreviewingFromChat()) {\r\n    //   this.libraryService.setGroupActions(true);\r\n    // }\r\n    // set unique student ids...\r\n    if (this.classroomStudents) {\r\n      this.classroomStudents = Array.from(new Set(this.classroomStudents.map((student: any) => student.aspUserId)))\r\n      .map(aspUserId => this.classroomStudents.find((student: any) => student.aspUserId === aspUserId)) as User[];\r\n      this.classroomStudentsIds = this.classroomStudents.map((student: any) => student.aspUserId);\r\n    }\r\n    if (this.classroomStudents && this.classroomStudents.length === 1) {\r\n      this.setStudentsToShareWith(this.classroomStudents[0].aspUserId);\r\n    }\r\n    this.oncollapseNotesLeftSideChanged(true);\r\n    console.log(this.classroomIdToAddLib);\r\n    // this.generalService.slideNativeElements(true, this.addLibrary.nativeElement);\r\n    this.loggedInUser = this.authService.getLoggedInUser();\r\n    this.role = this.authService.getUserRole();\r\n    this.items = [\r\n      {\r\n        label: 'Add New', icon: 'pi pi-fw pi-plus',\r\n        command: (event) => {\r\n        }\r\n      },\r\n      { label: 'Remove', icon: 'pi pi-fw pi-minus' }\r\n    ];\r\n\r\n    this.subs.sink = this.generalService.deviceKind.subscribe(deviceKind => {\r\n      this.isSmallScreen = !deviceKind.w768up;\r\n    });\r\n\r\n    this.subs.sink = this.libraryService.showGroupActions.subscribe(res => {\r\n      this.showGroupActions = res\r\n    })\r\n\r\n    this.subs.sink = this.layoutService.sideMenuHeight.subscribe((res) => {\r\n      // this.leftSide.nativeElement.style.maxHeight = (res - 100 + 'px');\r\n      // this.mainWrapper.nativeElement.style.height = (res -60 + 'px');\r\n\r\n      // this.classroomWrapper.nativeElement.style.height = (res - extraHeight.wrapper + 'px');\r\n      // this.scrollHeight = (res - extraHeight.scrollWrapper + 'px');/assets/icons/library/arrow-left.svg\r\n    });\r\n\r\n    this.subs.sink = this.libraryService.updateFolderListener.subscribe(res => {\r\n      if (res.folder.folderId! > 0) {\r\n        for (let lib of this.allLibrariesWithFiles) {\r\n          let found: boolean = false;\r\n          for (let file of lib.libraryFiles) {\r\n            if (file.fileId == res.libraryFile.fileId) {\r\n              found = true;\r\n            }\r\n          }\r\n          if (found) {\r\n            lib.folderId = res.folder.folderId!\r\n            lib.name = res.folder.name\r\n            lib.parent = res.folder.parent\r\n          }\r\n        }\r\n\r\n        this.openFolder(this.libraryToAdd.folder);\r\n      }\r\n    })\r\n\r\n    this.initOpenFolderListener();\r\n\r\n    this.getClassRooms();\r\n\r\n    // IRAKLIS SOURCE\r\n\r\n    this.subs.sink = this.libraryService.updateListener.subscribe(res => {\r\n      if (res) {\r\n        this.getFoldersWithFiles();\r\n      }\r\n    })\r\n\r\n    this.subs.sink = this.libraryService.deleteListener.subscribe(res => {\r\n      if (res > 0) {\r\n\r\n        for (let lib of this.allLibrariesWithFiles) {\r\n          let found: boolean = false;\r\n          for (let file of lib.libraryFiles) {\r\n            if (file.fileId == res) {\r\n              found = true;\r\n            }\r\n          }\r\n          if (found) {\r\n            lib.libraryFiles = lib.libraryFiles.filter(el => el.fileId != res)\r\n          }\r\n        }\r\n\r\n        this.getFoldersWithFiles();\r\n      }\r\n    })\r\n    this.libraryToAdd = this.libraryService.initializeEmptyLibraryObject()\r\n    this.user = this.authService.getLoggedInUser();\r\n    this.role = this.user.role!\r\n  }\r\n\r\n  ngOnChanges() {\r\n    this.initFiles();\r\n    this.getFoldersWithFiles();\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n    this.libraryService.setGroupActions(false);\r\n  }\r\n\r\n  openShareDialog() {\r\n    if (this.checkedLibraryFiles.length === 0) {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'warn',\r\n        summary: '',\r\n        detail: 'Please choose at least one file!'\r\n      });\r\n      return;\r\n    }\r\n    this.libraryService.openDialogWithComponent(this.dialogService, LibraryFileActionsDialogComponent, 400, \r\n      {\r\n        action: 'shareWithMultiple',\r\n        dialogsubTitle: 'Share multiple with...',\r\n        dialogTitle: 'Share file with...',\r\n      }, 'allFiles', (result: any) => {\r\n      if (result && result.action === 'shareWithMultiple') {\r\n        this.shareLibraryFiles(result.map.map);\r\n      }\r\n    });\r\n  }\r\n\r\n  shareMultipleLibraryFiles() {\r\n  }\r\n\r\n  onLibraryChecked(event: any) {\r\n    console.log(event);\r\n    if (event.checked) {\r\n      this.checkedLibraryFiles.push(event.file)\r\n    } else {\r\n      this.checkedLibraryFiles = this.checkedLibraryFiles.filter(el => el.path != event.file.path)\r\n    }\r\n    this.libraryService.checkedLibraryFiles.set(this.checkedLibraryFiles);\r\n    console.log(this.checkedLibraryFiles);\r\n  }\r\n\r\n  shareLibraryFiles(map: any) {\r\n    map.forEach((users: User[], classroomId: number) => {\r\n      let ids: string[] = []\r\n      let fileIds: number[] = []\r\n      for (let user of users) {\r\n        ids.push(user.aspUserId!)\r\n      }\r\n      for (let lib of this.checkedLibraryFiles) {\r\n        fileIds.push(lib.fileId!)\r\n      }\r\n      let send: any = {\r\n        fileIds: fileIds,\r\n        classroomId: classroomId,\r\n        studentIds: ids\r\n      }\r\n      this.sendShareToApi(send);\r\n    });\r\n  }\r\n\r\n  sendShareToApi(sendParams: LibraryFile[]) {\r\n    this.libraryService.shareFileWithClassUsers(sendParams).pipe(take(1)).subscribe(res => {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'success',\r\n        summary: '',\r\n        detail: 'Library has been sent.'\r\n      });\r\n    })\r\n  }\r\n\r\n  onShowAddLibrary() {\r\n    this.showAddLibrary = !this.showAddLibrary\r\n    this.libraryToAdd.folder = this.folderPath[this.folderPath.length - 1];\r\n    // this.generalService.slideNativeElements(this.showAddLibrary, this.addLibrary.nativeElement);\r\n  }\r\n\r\n  deleteFolderSelected(folder: Folder) {\r\n    this.deleteFolder(folder.folderId);\r\n  }\r\n\r\n  openFolderSelected(folder: Folder) {\r\n    this.openFolder(folder);\r\n  }\r\n\r\n  getClassRooms(){\r\n    this.subs.add(this.classroomService.getLMSUserClassrooms(this.loggedInUser.id).subscribe((response)=>{\r\n      this.teacherClassrooms = response;\r\n    }));\r\n  }\r\n\r\n  // createLibFolder(){\r\n  //   this.libraryService.createFolder().subscribe( (response) => {\r\n\r\n  //   });\r\n  // }\r\n\r\n\r\n  deleteLibFolder(folderId: number){\r\n    this.libraryService.deleteFolder(folderId).subscribe( (response) => {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'success',\r\n        summary: '',\r\n        detail: 'Folder Deleted successfully.'\r\n      });\r\n    });\r\n  }\r\n\r\n  getFileName(path: string) {\r\n    if (path) {\r\n      let n = path.lastIndexOf(\"/\");\r\n      if(n === -1)\r\n        n = path.lastIndexOf(\"\\\\\");\r\n      let result = path.substring(n + 1);\r\n      return result;\r\n    }\r\n    return path\r\n  }\r\n\r\n  showSharePopup(fileId: number){\r\n    document.getElementById('lib-file-move-menu-' + fileId)!.style.display = 'none';\r\n    document.getElementById('lib-file-share-menu-' + fileId)!.style.display = 'block';\r\n    document.getElementById('lib-file-share-menu-' + fileId)!.style.top = '25%';\r\n  }\r\n  \r\n  // shareLibraryFiles(fileId: number, classroomID: number) {\r\n  //   let fileIds = []\r\n  //   fileIds.push(fileId)\r\n  //   let body: any = {\r\n  //     fileIds: fileIds,\r\n  //     classroomId: this.classroomId,\r\n  //     studentIds: this.StudentIdsToSend\r\n  //   }\r\n  //   if (this.StudentIdsToSend.length === 0){\r\n\r\n  //   }\r\n  //   else{\r\n  //     this.libraryService.shareFileWithClassUsers(body).pipe(take(1)).subscribe(res => {\r\n\r\n  //     })\r\n  //   }\r\n  //   this.ngOnInit();\r\n  // }\r\n\r\n  onFolderSelect(fileId: any, folderId: any){\r\n    this.isMoveRadioCheked = true;\r\n    this.moveFileId = fileId;\r\n    this.moveFolderId = folderId;\r\n  }\r\n\r\n  moveFile(){\r\n    this.moveFileAPI(this.moveFileId, this.moveFolderId);\r\n    // this.libraryService.moveFileToFolder(this.moveFileId, this.moveFolderId).subscribe( (response) => {\r\n    //   this.toastService.setShowToastmessage({\r\n    //     severity: 'success',\r\n    //     summary: '',\r\n    //     detail: 'File moved successfully.'\r\n    //   });\r\n    // });\r\n    this.ngOnInit();\r\n  }\r\n\r\n  // IRAKLIS SOURCE\r\n\r\n  initFiles() {\r\n    this.filteredFiles = [];\r\n    let i = 0;\r\n    for (let folderWithFiles of this.foldersWithFiles) {\r\n      for (let libraryFile of folderWithFiles.libraryFiles) {\r\n        libraryFile.dummyId = i;\r\n        i++;\r\n        this.filteredFiles.push(libraryFile)\r\n      }\r\n    }\r\n    this.files = this.filteredFiles;\r\n  }\r\n\r\n  getFoldersWithFiles() {\r\n    this.initializeFilesWithFolders(this.allLibrariesWithFiles);\r\n  }\r\n\r\n  initializeFilesWithFolders(res: FoldersWithFiles[]) {\r\n    this.allLibrariesWithFiles = res;\r\n    this.folders = [];\r\n    for (const lib of this.allLibrariesWithFiles) {\r\n      const folder: Folder = {\r\n        name: lib.name,\r\n        parent: lib.parent,\r\n        folderId: lib.folderId,\r\n      };\r\n      if (!this.folders.some(el => el.name === folder.name && el.parent === folder.parent)) {\r\n        this.folders.push(folder);\r\n      }\r\n    }\r\n    this.foldersWithFiles = this.libraryService.getLibrariesOnRootFolders(this.allLibrariesWithFiles);\r\n    this.initFiles();\r\n    this.getInnerFolders('home');\r\n  }\r\n\r\n  getInnerFolders(parent: string) {\r\n    this.innerFolders = []\r\n    let copyFolders = this.allLibrariesWithFiles.filter(el => el.parent == parent)\r\n    for (let innerFolder of copyFolders) {\r\n      if (!this.innerFolders.some(el => el.parent == innerFolder.parent && el.name == innerFolder.name)) {\r\n        this.innerFolders.push(innerFolder)\r\n      }\r\n    }\r\n  }\r\n\r\n  openFolder(folder: Folder) {\r\n    if (!folder) {\r\n      this.getInnerFolders('home')\r\n      return;\r\n    }\r\n\r\n    this.libraryToAdd.folder = folder\r\n    console.log(this.libraryToAdd.folder);\r\n    this.foldersWithFiles = this.libraryService.getLibrariesOfFolder(folder, this.allLibrariesWithFiles);\r\n    this.initFiles()\r\n    if (folder.name == 'home') {\r\n      this.getInnerFolders('home')\r\n    } else {\r\n      this.getInnerFolders(folder.name)\r\n    }\r\n    this.folderPath = this.libraryService.getFolderPathOfFolder(folder, [], this.folders);\r\n    this.folderPath.unshift(this.libraryService.rootFolder)\r\n    this.libraryToAdd.folder = this.folderPath[this.folderPath.length - 1];\r\n  }\r\n\r\n  deleteFolder(id: any) {\r\n    const folderToDelete = this.innerFolders.filter(el => el.folderId === id);\r\n    this.libraryService.setFolderToDelete(folderToDelete[0]);\r\n    this.deleteFolderConfirmData.showDialog = true;\r\n    this.deleteFolderConfirmData.message = 'Delete the folder \"' + folderToDelete[0].name + '\"?';\r\n    // this.libraryService.deleteFolder(id).pipe(take(1)).subscribe(res => {\r\n    //   this.innerFolders = this.innerFolders.filter(el => el.folderId != id)\r\n    //   console.log(this.innerFolders)\r\n\r\n    // })\r\n  }\r\n\r\n  onRejectFolderDelete(event: any) {\r\n    this.libraryService.setFolderToDelete({} as Folder);\r\n    this.deleteFolderConfirmData.showDialog = false;\r\n  }\r\n\r\n  onAcceptFolderDelete(event: any) {\r\n    this.libraryService.deleteFolder(this.libraryService.getFolderToDelete().folderId).pipe(take(1)).subscribe(res => {\r\n      this.innerFolders = this.innerFolders.filter(el => el.folderId != this.libraryService.getFolderToDelete().folderId)\r\n      console.log(this.innerFolders)\r\n      \r\n      this.toastService.setShowToastmessage({\r\n        severity: 'success',\r\n        summary: '',\r\n        detail: 'Folder has been deleted.'\r\n      });\r\n      this.removeFolderByFolderId(this.libraryService.getFolderToDelete())\r\n      this.libraryService.setFolderToDelete({} as Folder);\r\n      this.libraryService.setUpdateListener(true);\r\n    })\r\n    this.deleteFolderConfirmData.showDialog = false;\r\n  }\r\n\r\n  onTagChoosen(event: any) {\r\n    let isLevel = event.isLevel;\r\n    if (event.checked) {\r\n      if (event.applyToAll) {\r\n        for (let file of this.libraryToAdd.libraryFiles) {\r\n          let arrLibrary: string[] = isLevel ? file.levels : file.categories;\r\n          arrLibrary.push(event.tag)\r\n        }\r\n      } else {\r\n        let file = this.libraryToAdd.libraryFiles[event.file.index]\r\n        let arrLibrary: string[] = isLevel ? file.levels : file.categories;\r\n        arrLibrary.push(event.tag)\r\n      }\r\n    } else {\r\n      if (event.applyToAll) {\r\n        for (let file of this.libraryToAdd.libraryFiles) {\r\n          let arrLibrary: string[] = isLevel ? file.levels : file.categories;\r\n          let index2 = arrLibrary.findIndex(((el: string) => el == event.tag));\r\n          arrLibrary.splice(index2, 1);\r\n        }\r\n      } else {\r\n        let file = this.libraryToAdd.libraryFiles[event.file.index]\r\n        let arrLibrary: string[] = isLevel ? file.levels : file.categories;\r\n        let index2 = arrLibrary.findIndex(((el: string) => el == event.tag));\r\n        arrLibrary.splice(index2, 1);\r\n      }\r\n    }\r\n  }\r\n  onCreateFolder() {\r\n    this.libraryService.getUserCreatedLibFolders().pipe(take(1)).subscribe(res => {\r\n      let found = false;\r\n      for (let r of res) {\r\n        if (r.parent == this.folderPath[this.folderPath.length - 1].name && r.name == this.folderName) {\r\n          found = true;\r\n          break;\r\n        }\r\n      }\r\n      if (!found) {\r\n        this.showInput = false;\r\n        let newFolder: Folder = {\r\n          parent: this.folderPath[this.folderPath.length - 1].name,\r\n          name: this.folderName\r\n        }\r\n        this.libraryService.createFolder(newFolder).pipe(take(1)).subscribe(res => {\r\n          this.innerFolders.push(res)\r\n          this.allLibrariesWithFiles.push({\r\n            parent: this.folderPath[this.folderPath.length - 1].name,\r\n            name: this.folderName,\r\n            libraryFiles: [],\r\n            createdBy: this.user,\r\n            folderId: 10000\r\n          });\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'success',\r\n          summary: '',\r\n          detail: 'Folder created.'\r\n        });\r\n        this.folderName = '';\r\n        this.libraryService.setUpdateListener(true);\r\n        })\r\n      } else {\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'warn',\r\n          summary: '',\r\n          detail: 'Folder already exists.'\r\n        });\r\n      }\r\n    })\r\n  }\r\n\r\n  createFolder() {\r\n    this.showInput = !this.showInput;\r\n  }\r\n  \r\n  onShowUploadDialog() {\r\n    const targetElementName = this.libraryService.isPreviewingFromChat() ? 'notifications-overview' : 'mainContentColumn';\r\n    const myElement = document.getElementById(targetElementName);\r\n    // this.libraryToAdd = this.libraryService.initializeEmptyLibraryObject();\r\n    // this.libraryToAdd.libraryFiles = [];\r\n    this.userService.setNewFileUpload({});\r\n    console.log(myElement!.getBoundingClientRect().width);\r\n    const dialogWidth = myElement!.getBoundingClientRect().width - 10;\r\n\r\n    this.generalService.openDialogWithComponent(this.dialogService, LibraryFileActionsDialogComponent, \r\n      dialogWidth.toFixed(), \r\n      {\r\n        action: 'upload',\r\n        dialogsubTitle: '',\r\n        dialogTitle: 'Upload to library...',\r\n        folder: this.libraryToAdd.folder,\r\n        libraryToAdd: this.libraryToAdd,\r\n      }, targetElementName, (result: any) => {\r\n      if (result && result.action === 'upload') {\r\n        console.log(result);\r\n        this.libraryToAdd.libraryFiles = result.libraryFiles;\r\n        this.onSubmit();\r\n        // this.moveFile(result.map.map);\r\n      }\r\n    }, myElement!.getBoundingClientRect().height - 90 + 'px');\r\n  }\r\n  \r\n  onSubmit() {\r\n    if (this.libraryToAdd.libraryFiles.length > 0) {\r\n      this.libraryToAdd.classroom = this.classroomIdToAddLib;\r\n      let toSend: UploadFilesToFolderRequest = {\r\n        folderId: this.libraryToAdd.folder.folderId!,\r\n        classroomId: this.classroomIdToAddLib,\r\n        name: this.libraryToAdd.folder.name,\r\n        parent: this.libraryToAdd.folder.parent,\r\n        libFiles: this.libraryToAdd.libraryFiles,\r\n        StudentIds: this.studentsToShareWith\r\n      }\r\n\r\n      // return;\r\n      if (this.classroomIdToAddLib > 0) {\r\n        this.libraryService.uploadClassroomFilesToFolder(toSend).pipe(take(1)).subscribe(res => {\r\n          this.toastService.setShowToastmessage({\r\n            severity: 'success',\r\n            summary: '',\r\n            detail: 'Your files have been uploaded to Classroom library.'\r\n          });\r\n\r\n          this.userService.setUploadFiles(true);\r\n          this.mapLibraryToAddFiles(res);\r\n          this.e()\r\n        })\r\n      } else {\r\n        this.libraryService.uploadFilesToFolder(toSend).pipe(take(1)).subscribe((res: any) => {\r\n          this.toastService.setShowToastmessage({\r\n            severity: 'success',\r\n            summary: '',\r\n            detail: 'Your files have been uploaded to library folder: ' + toSend.name\r\n          });\r\n\r\n          this.currentUploadFolder = this.innerFolders.filter(el => el.folderId === toSend.folderId)[0]\r\n          this.userService.setUploadFiles(true);\r\n          this.mapLibraryToAddFiles(res);\r\n          this.e()\r\n          console.log(this.currentUploadFolder);\r\n          // this.libraryService.setOpenFolderListener(currentFolder[0]);\r\n          // this.libraryService.setUpdateFolderListener(toSend.libFiles[0], currentFolder[0])\r\n        \r\n        })\r\n      }\r\n\r\n    } else {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'warn',\r\n        summary: '',\r\n        detail: 'Please add files'\r\n      });\r\n    }\r\n  }\r\n\r\n  e() {\r\n\r\n    this.filteredFiles.concat(this.libraryToAdd.libraryFiles)\r\n    this.foldersWithFiles.push({\r\n      folderId: this.libraryToAdd.folder.folderId!,\r\n      name: this.libraryToAdd.folder.name,\r\n      parent: this.libraryToAdd.folder.parent,\r\n      libraryFiles: this.libraryToAdd.libraryFiles,\r\n      createdBy: this.user\r\n    })\r\n    // this.getFoldersWithFiles();\r\n    // this.showAddLibrary = !this.showAddLibrary\r\n    // this.generalService.slideNativeElements(this.showAddLibrary, this.addLibrary.nativeElement);\r\n\r\n    console.log(this.filteredFiles);\r\n    this.allLibrariesWithFiles.push({\r\n      folderId: this.libraryToAdd.folder.folderId!,\r\n      name: this.libraryToAdd.folder.name,\r\n      parent: this.libraryToAdd.folder.parent,\r\n      libraryFiles: this.libraryToAdd.libraryFiles,\r\n      createdBy: this.user\r\n    })\r\n    // this.files.push(this.files[this.files.length - 1]);\r\n    this.initFiles()\r\n    this.libraryToAdd = this.libraryService.initializeEmptyLibraryObject();\r\n    // this.libraryToAdd.libraryFiles = [];\r\n\r\n    // this.libraryService.setUpdateListener(true)\r\n    this.userService.setNewFileUpload({});\r\n  }\r\n\r\n  // addes also fileId to a new uploaded file in the library\r\n  mapLibraryToAddFiles(res: any) {\r\n    this.libraryToAdd.libraryFiles = res.libFiles.map((file: {}, index: number) => ({\r\n      ...file,\r\n      fileId: res.fileIds![index]\r\n    }));\r\n  }\r\n\r\n  uploadFinished(event: any) {\r\n    console.log(event);\r\n    this.libraryToAdd.libraryFiles?.push({ \r\n      path: event.filePath.dbPath, \r\n      levels: [], \r\n      categories: [], \r\n      name: event.filePath.dbPath, \r\n      fileId: event.filePath.dbPath });\r\n  }\r\n\r\n  /**\r\n  This method is triggered when a file is deleted from the classroom files.\r\n  It filters out the deleted file from the libraryFiles array of each folder in the allLibrariesWithFiles array.\r\n  @param {any} event - The event object containing the ID of the deleted file.\r\n  @returns {void}\r\n  */\r\n  onFileDeleted(event: any) {\r\n    this.deleteFileConfirmData.showDialog = true;\r\n    this.deleteFileConfirmData.message = 'Delete file \"' + event.name + '\"?';\r\n    // this.allLibrariesWithFiles = this.allLibrariesWithFiles..filter(file => file.fileId !== libFile.fileId);\r\n    this.libraryService.setLibraryFile(event);\r\n  }\r\n\r\n  deleteFile(fileId : any) {\r\n    this.subs.add(this.libraryService.delete(fileId).subscribe(res => {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'success',\r\n        summary: '',\r\n        detail: 'Files has been deleted successfully.'\r\n      });\r\n    }));\r\n  }\r\n\r\n  moveMultipleFiles() {\r\n    if (this.checkedLibraryFiles.length === 0) {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'warn',\r\n        summary: '',\r\n        detail: 'Please choose at least one file!'\r\n      });\r\n      return;\r\n    }\r\n    this.libraryService.openDialogWithComponent(this.dialogService, LibraryFileActionsDialogComponent, 400, \r\n      {\r\n        action: 'moveMultiple',\r\n        dialogsubTitle: 'Move Files to folder...',\r\n        dialogTitle: 'Choose folder...',\r\n        folders: this.folders\r\n      }, null, (result: any) => {\r\n      if (result && result.action === 'moveMultiple') {\r\n        console.log(result);\r\n        console.log(this.checkedLibraryFiles);\r\n        this.checkedLibraryFiles.forEach(libFile => {\r\n          this.moveFileAPI(libFile.fileId!, result.folder.folderId);\r\n          this.libraryService.setUpdateListener(true)\r\n        });\r\n        // this.moveFile(result.map.map);\r\n      }\r\n    });\r\n  }\r\n\r\n  deleteMultipleFiles() {\r\n    if (this.checkedLibraryFiles.length === 0) {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'warn',\r\n        summary: '',\r\n        detail: 'Please choose at least one file!'\r\n      });\r\n      return;\r\n    }\r\n    this.deleteMultipleFilesConfirmData.showDialog = true;\r\n    this.deleteMultipleFilesConfirmData.message = 'Delete the selected files ?';\r\n  }\r\n\r\n  onAcceptFileDeleteMultiple(event: any) {\r\n    const libFiles = this.checkedLibraryFiles;\r\n    libFiles.forEach(libFile => {\r\n      this.filterDelteFromLibraryFiles(libFile.fileId);\r\n      this.deleteFileAPI(libFile);\r\n    });\r\n    this.deleteFileConfirmData.showDialog = false;\r\n    this.deleteMultipleFilesConfirmData.showDialog = false;\r\n    this.checkedLibraryFiles = [];\r\n  }\r\n\r\n  filterDelteFromLibraryFiles(...fileIds: any[]) {\r\n    if (fileIds.length === 1) {\r\n      this.filteredFiles = this.filteredFiles.filter(el => el.fileId !== fileIds[0]);\r\n    } else {\r\n      this.filteredFiles = this.filteredFiles.filter(el => !fileIds.includes(el.fileId));\r\n    }\r\n  }\r\n  \r\n  onRejectFileDelete(event: any) {\r\n    this.deleteFileConfirmData.showDialog = false;\r\n    this.deleteMultipleFilesConfirmData.showDialog = false;\r\n    this.libraryService.setLibraryFile({} as LibraryFile);\r\n  }\r\n\r\n  onAcceptFileDelete(event: any) {\r\n    const libFile = this.libraryService.getLibraryFile();\r\n    this.filterDelteFromLibraryFiles(libFile.fileId);\r\n    this.deleteFileAPI(libFile);\r\n  }\r\n\r\n  private deleteFileAPI(libFile: LibraryFile) {\r\n    this.subs.add(this.libraryService.delete(libFile.fileId).subscribe((res: boolean) => {\r\n\r\n      if (res) {\r\n        this.deleteFileConfirmData.showDialog = false;\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'success',\r\n          summary: '',\r\n          detail: 'Files has been deleted successfully..'\r\n        });\r\n        this.removeLibraryFileByFileId(libFile);\r\n      }\r\n\r\n    }));\r\n  }\r\n\r\n  private removeLibraryFileByFileId(libFile: LibraryFile) {\r\n    this.allLibrariesWithFiles = this.allLibrariesWithFiles.map(file => {\r\n      const filteredLibraryFiles = file.libraryFiles.filter(libraryFile => libraryFile.fileId !== libFile.fileId);\r\n      return { ...file, libraryFiles: filteredLibraryFiles };\r\n    });\r\n  }\r\n\r\n  private removeFolderByFolderId(folder: Folder) {\r\n    this.allLibrariesWithFiles = this.allLibrariesWithFiles.filter(file => {\r\n      return file.folderId !== folder.folderId;\r\n    });\r\n  }\r\n\r\n  private moveFileAPI(fileId: number, moveFolderId: number) {\r\n    this.subs.add(this.libraryService.moveFileToFolder(fileId, moveFolderId).subscribe( (response) => {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'success',\r\n        summary: '',\r\n        detail: 'File moved successfully.'\r\n      });\r\n      this.checkedLibraryFiles = [];\r\n      this.showGroupActions = false;\r\n      // this.openFolder(this.libraryToAdd.folder);\r\n      \r\n      this.libraryService.setUpdateListener(true)\r\n    }));\r\n  }\r\n\r\n  oncollapseNotesLeftSideChanged(event: any) {\r\n    this.isLeftsideCollapsed = event;\r\n  }\r\n\r\n  setGroupActions() {\r\n    this.showGroupActions = !this.showGroupActions;\r\n    // this.libraryService.setGroupActions(this.showGroupActions);\r\n    setTimeout(() => {\r\n      // this.libraryService.setGroupActions(this.showGroupActions);\r\n    }, 300);\r\n  }\r\n\r\n  onTabChangeFilterFilesPerStudent(event: any) {\r\n    if (event.index > 0) {\r\n      this.studentsToShareWith = [];\r\n      const currentStudent = this.classroomStudents[event.index - 1];\r\n      this.setStudentsToShareWith(this.classroomStudents[event.index - 1].aspUserId);\r\n\r\n      this.filteredFiles = this.files.filter(folder =>\r\n        folder.sharedWith!.id === currentStudent.aspUserId\r\n      );\r\n    } else {\r\n      this.filteredFiles = this.files;\r\n      this.setStudentsToShareWith();\r\n    }\r\n  }\r\n\r\n  setStudentsToShareWith(studentId?: string) {\r\n    this.studentsToShareWith = studentId? [studentId] : this.classroomStudents.map((user: User) => user.aspUserId);\r\n  }\r\n\r\n  getFilteredFiles(unique = false) {\r\n    if (!unique) {\r\n      return this.filteredFiles;\r\n    } else {\r\n      const uniqueFilteredFiles = this.filteredFiles = this.filteredFiles.filter((file, index, self) =>\r\n        index === self.findIndex((f) => (\r\n          f.fileId === file.fileId\r\n        )));\r\n      return uniqueFilteredFiles;\r\n    }\r\n  }\r\n\r\n  onFileUnshared(event: any) {\r\n    console.log(event);\r\n    this.libraryService.setUpdateListener(true)\r\n  }\r\n\r\n  initOpenFolderListener() {\r\n    this.subs.sink = this.libraryService.libraryUpdatedListener.subscribe((res: boolean) => {\r\n      if ((res && this.currentUploadFolder)) {\r\n        if (!this.generalService.isNullishObject(this.currentUploadFolder)) {\r\n\r\n          this.openFolder(this.currentUploadFolder);\r\n          console.log(this.libraryToAdd.folder);\r\n        }\r\n  \r\n      }\r\n    });\r\n  }\r\n\r\n}", "<ng-container *ngIf=\"deleteFolderConfirmData.showDialog\">\r\n    <app-confirm-dialog (rejectSelected)=\"onRejectFolderDelete($event)\" (confirmSelected)=\"onAcceptFolderDelete($event)\"\r\n        dialogKey=\"key1\" headerClass=\"text-center flex align-items-center flex-column bg-white\" [rejectBtnIcon]=\"\"\r\n        acceptBtnLabel=\"Yes\" [confirmMessage]=\"deleteFolderConfirmData.message\"\r\n        rejectBtnLabel=\"No\"></app-confirm-dialog>\r\n</ng-container>\r\n<ng-container *ngIf=\"deleteFileConfirmData.showDialog\">\r\n    <app-confirm-dialog (rejectSelected)=\"onRejectFileDelete($event)\" (confirmSelected)=\"onAcceptFileDelete($event)\"\r\n        dialogKey=\"key2\" headerClass=\"text-center flex align-items-center flex-column bg-white\" [rejectBtnIcon]=\"\"\r\n        acceptBtnLabel=\"Yes\" [confirmMessage]=\"deleteFileConfirmData.message\" rejectBtnLabel=\"No\"></app-confirm-dialog>\r\n</ng-container>\r\n<ng-container *ngIf=\"deleteMultipleFilesConfirmData.showDialog\">\r\n    <app-confirm-dialog (rejectSelected)=\"onRejectFileDelete($event)\" (confirmSelected)=\"onAcceptFileDeleteMultiple($event)\"\r\n        dialogKey=\"key2\" headerClass=\"text-center flex align-items-center flex-column bg-white\" [rejectBtnIcon]=\"\"\r\n        acceptBtnLabel=\"Yes\" [confirmMessage]=\"deleteMultipleFilesConfirmData.message\" rejectBtnLabel=\"No\"></app-confirm-dialog>\r\n</ng-container>\r\n\r\n\r\n\r\n<div class=\"notes\" #mainWrapper>\r\n\r\n    \r\n<div class=\"modal  p-0 max-w-12rem\" [ngClass]=\"{ 'no-visibility' : !this.showAddLibrary}\">\r\n    <app-block-viewer [header]=\"'Upload files in folder ' + libraryToAdd.folder.name\"\r\n    headerBackgroundImage=\"/assets/images/dashboard/calendar/calendar-dialog-lesson-info-arranged.png\"\r\n    blockClass=\"border-radius-bottom-10\" headerBlockClass=\"py-1 border-round-lg bg-cover sticky top-0 z-5 \"\r\n    containerClass=\"block-gradient-reverse px-3 py-2 relative\" [headerClass]=\"'justify-content-center my-0'\"\r\n    [headerTextClass]=\"'font-base font-semibold justify-content-center capitalize'\" [showCloseDialogIcon]=\"true\"\r\n    (closeDialogEvent)=\"onShowAddLibrary()\">\r\n    <div class=\"block-gradient p-3\">\r\n        <!-- <app-upload-files (onSubmit)=\"onSubmit()\" class=\"p-20\" title=\"Library\"\r\n        [fileDropImage]=\"'/assets/icons/library/upload-file-lib.svg'\" [fileDropImageWidth]=\"10\"\r\n        [isProfile]=\"false\"\r\n        [teacherFilesLength]=\"libraryToAdd.libraryFiles.length\" [dndUI]=\"true\" [showSubmitButton]=\"true\"\r\n        [checkboxUI]=\"true\" postUrl=\"/Upload/UploadLibraryFile\" (onUploadFinished)=\"uploadFinished($event)\"\r\n        (tagChoosen)=\"onTagChoosen($event)\">\r\n        </app-upload-files> -->\r\n    </div>\r\n    </app-block-viewer>\r\n</div>\r\n\r\n    <div class=\"block-gradient-reverse {{extraGradientClass}} py-2 md:py-0 border-round-xl px-1\"  [style.height]=\"!isSmallScreen ? leftSideHeight +(authService.isStudent ? -15 : 0) + 'px' : '500px'\">\r\n        \r\n        <ng-container *ngIf=\"!withClassroom || authService.isStudent || classroomStudents.length === 1\">\r\n            <ng-container *ngTemplateOutlet=\"classInfoGeneralTemplate; context: { classroom: classroom }\">\r\n            </ng-container>\r\n        </ng-container>\r\n\r\n        <ng-container *ngIf=\"classroomStudents && withClassroom && authService.isTeacher && classroomStudents.length > 1\">\r\n            <p-tabView [scrollable]=\"classroomStudents.length > 4\" (onChange)=\"onTabChangeFilterFilesPerStudent($event)\" class=\"w-full\">\r\n                <p-tabPanel header=\"All\">\r\n                    <div id=\"classroom-info\" class=\"\">\r\n                        <ng-container *ngTemplateOutlet=\"classInfoGeneralTemplate; context: { classroom: classroom, uniqueFiles: true }\">\r\n                        </ng-container>\r\n\r\n                    </div>\r\n                </p-tabPanel>\r\n                <ng-container *ngFor=\"let selectedStudent of classroomStudents; index as i\">\r\n                    <p-tabPanel [header]=\"selectedStudent.firstName\">\r\n                        <ng-container\r\n                            *ngTemplateOutlet=\"classInfoGeneralTemplate; context: { classroom: classroom, student: selectedStudent }\">\r\n                        </ng-container>\r\n                    </p-tabPanel>\r\n                </ng-container>\r\n            </p-tabView>\r\n        </ng-container>\r\n\r\n        <ng-template #classInfoGeneralTemplate let-classroom=\"classroom\" let-student=\"student\" let-uniqueFiles=\"uniqueFiles\">\r\n            <div class=\"notes-header mb-2 lg:absolute gap-3 abs-header-top lg:flex\" [ngClass]=\"{'more': isInTab}\">\r\n                <div class=\"notes-header-title hidden md:block font-2xl opaque-box-rtl {{extraGradientClass}} sm:col-4\">\r\n                    {{libraryTitle}}\r\n                </div>\r\n                <div class=\"flex align-items-center justify-content-end notes-header-actions gap-2\" style=\"margin-right: 5px;\">\r\n                    <span class=\"p-input-icon-right w-full\">\r\n                        <input type=\"text\" [(ngModel)]=\"term\" pInputText placeholder=\"Search\"\r\n                            class=\"h-2rem w-full input-blue gradient-blue rounded\" />\r\n                        <i class=\"pi pi-search text-primary\"></i>\r\n                    </span>\r\n                    <!-- <button pButton type=\"button\" label=\"\" class=\"p-button-text \" styleClass=\"min-w-6rem\"\r\n                        style=\"min-width: 6rem;\">\r\n                        <span class=\"p-button-icon p-button-icon-right\" aria-hidden=\"true\"></span>\r\n                        <span class=\"p-button-label mr-2\">Filters</span>\r\n                        <img class=\"contact-icon-img\" src=\"/assets/icons/notifications/notif-filter.svg\" />\r\n                    </button> -->\r\n\r\n                    <img *ngIf=\"canShare\" title=\"Group Actions\" src=\"/assets/icons/library/group-actions.svg\" (click)=\"setGroupActions()\" class=\"hvr-grow pointer\"\r\n                    [ngClass]=\"{'filter-blue': showGroupActions}\">\r\n                    <img *ngIf=\"canUpload\" title=\"Upload File\" src=\"/assets/icons/library/add-lib.svg\" (click)=\"onShowUploadDialog()\" class=\"add hvr-grow\">\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"flex align-items-center justify-content-center\">\r\n                <div *ngIf=\"showGroupActions\" class=\"note-menu-row block-gradient border-round-xl font-sm my-2 p-1 px-3 gap-2\">\r\n                    <!--  (click)=\"showShare = !showShare\" -->\r\n                    <div class=\"note-menu-col\">\r\n                        <div class=\"note-menu-icon\">\r\n                            <img src=\"/assets/icons/library/share-sm-icon.svg\" height=\"14\">\r\n                        </div>\r\n                        <div class=\"note-menu-text link-main-color\" (click)=\"openShareDialog()\">\r\n                            Share\r\n                        </div>\r\n                        <!-- <div class=\"note-menu-icon\" style=\"margin-left:auto\">\r\n                            <img src=\"/assets/icons/arrow-note.svg\">\r\n                        </div> -->\r\n                    </div>\r\n                    <div class=\"note-menu-col\" (click)=\"moveMultipleFiles()\">\r\n                        <div class=\"note-menu-icon\">\r\n                            <img src=\"/assets/icons/library/move.svg\" height=\"14\">\r\n                        </div>\r\n                        <div class=\"note-menu-text link-main-color\">\r\n                            Move\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"note-menu-col\" (click)=\"deleteMultipleFiles()\">\r\n                        <div class=\"note-menu-icon\">\r\n                            <img src=\"/assets/icons/library/delete.svg\" height=\"14\">\r\n                        </div>\r\n                        <div class=\"note-menu-text link-main-color\">\r\n                            Delete\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"send\" *ngIf=\"showShare\">\r\n                        <app-classrooms-check-list (close)=\"showShare = !showShare\" (checkedList)=\"onCheckedList($event)\">\r\n                        </app-classrooms-check-list>\r\n                        <div class=\"main-color-button share-btn\" (click)=\"shareLibraryFiles()\">\r\n                            Share\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"my-folders\">\r\n                <div class=\"flex flex-row\">\r\n                    <div class=\"flex flex-column p-4 my-folder align-items-center\"  [ngClass]=\"{'purple-folder-bg': isInTab}\">\r\n                        <img src=\"/assets/icons/my-folder.svg\" width=\"40\">\r\n                        <h4 class=\"text-primary m-0 font-base text-center font-semibold pt-1 capitalize\">\r\n                            {{libraryToAdd.folder.name}}\r\n                        </h4>\r\n                    </div>\r\n\r\n                    <div class=\"flex flex-row gap-4 ml-4 align-items-center horizontal-scroll-menu px-3\">\r\n                        <div class=\"flex flex-column relative\" *ngFor=\"let innerFolder of innerFolders\">\r\n                            <app-single-library-folder-item \r\n                            [folder]=\"innerFolder\"\r\n                            [hasLimitedOptions]=\"hasLimitedOptions\" \r\n                            (deleteFolder)=\"deleteFolderSelected($event)\"\r\n                            (openFolder)=\"openFolderSelected($event)\"\r\n                            ></app-single-library-folder-item>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <img *ngIf=\"canUpload\" title=\"Create new subfolder\" (click)=\"createFolder()\" class=\"ml-auto px-3 pointer hvr-grow\" src=\"/assets/icons/folder-add.svg\" width=\"60\">\r\n\r\n\r\n\r\n                    <div class=\"inner-folders\">\r\n\r\n                        <!-- <div *ngFor=\"let innerFolder of innerFolders\" class=\"hvr-grow\" style=\" position:relative; margin:15px; cursor: pointer\">\r\n        <img src=\"/assets/icons/fol.svg\" style=\"width:100px; height:100px\" (click)=\"openFolder(innerFolder)\">\r\n        <div style=\"position:absolute; top:50px; width:100px; text-align: center; font-weight: bold\" (click)=\"openFolder(innerFolder)\">\r\n            {{innerFolder.name}}\r\n        </div>\r\n        <img src=\"/assets/icons/close.png\" class=\"close-img close-img-abs hvr-glow\" (click)=\"showDelete(innerFolder.folderId)\">\r\n        <img src=\"/assets/icons/close.png\" class=\"close-img close-img-abs hvr-glow\" (click)=\"showDelete(innerFolder.folderId)\">\r\n        <div id=\"inner-{{innerFolder.folderId}}\" style=\"display: none; background: white; z-index:10;\">\r\n            Are you sure you want to delete folder?\r\n            <div class=\"btns\">\r\n                <div (click)=\"deleteFolder(innerFolder.folderId)\" class=\"white-button\">Yes</div>\r\n                <div (click)=\"cancelDelete(innerFolder.folderId)\" class=\"main-color-button\">No</div>\r\n            </div>\r\n        </div>\r\n    </div> -->\r\n\r\n                        <!-- <div class=\"main-color-button\" (click)=\"onCancelCreateFolder()\">Cancel</div> -->\r\n                    </div>\r\n\r\n                </div>\r\n            </div>\r\n            <div class=\"folder-path\">\r\n<!--  *ngIf=\"showInput\" -->\r\n\r\n\r\n<div class=\"flex align-items-center justify-content-between\">\r\n                <div class=\"folder-nav-buttons\">\r\n\r\n                    <img *ngIf=\"folderPath.length > 1\" (click)=\"openFolder(folderPath[folderPath.length-2])\"\r\n                        src=\"/assets/icons/arr-left.svg\" height=\"18\" class=\"pointer\">\r\n                    &nbsp;\r\n\r\n                    <!-- <img src=\"/assets/icons/arr-right.svg\" height=\"18\"> -->\r\n                </div>\r\n\r\n                <img src=\"/assets/icons/folder-start.png\">\r\n                &nbsp;\r\n\r\n                <img src=\"/assets/icons/arr-right.svg\">\r\n                &nbsp;\r\n                <div *ngFor=\"let folder of folderPath\" (click)=\"openFolder(folder)\" class=\"link-main-color font-sm capitalize\">\r\n                    &nbsp;{{folder.name}}&nbsp;/\r\n                </div>\r\n            </div>\r\n                <div class=\"ml-auto\">\r\n                    <ng-container *ngIf=\"showInput\">\r\n                        <div class=\"flex w-auto align-items-center\">\r\n                            <i class=\"pi pi-times-circle mr-2 pointer icon-times-blue\" (click)=\"createFolder()\"></i>\r\n                            <input type=\"text\"  [(ngModel)]=\"folderName\" pinputtext=\"\" placeholder=\"Name your folder...\" \r\n                                class=\"h-2rem input-blue gradient-blue rounded btn-right mr-2\">\r\n                            <button type=\"button\" (click)=\"onCreateFolder()\" pbutton=\"\" pripple=\"\" label=\"Search\" class=\"p-element p-button p-button-xs p-component btn-gradient-blue\"\r\n                            style=\"height: 32px;\"><span\r\n                                    class=\"p-button-label\">Create</span></button>\r\n                        </div>\r\n    <!-- \r\n                        <input [(ngModel)]=\"folderName\" placeholder=\"Name your folder...\" class=\"h-2rem input-blue gradient-blue rounded\" type=\"text\">\r\n                        <div class=\"light-purple-button\" (click)=\"onCreateFolder()\">Create</div> -->\r\n                    </ng-container>\r\n                    <!-- <button pButton type=\"button\" label=\"\" \r\n            iconPos=\"right\" class=\"p-button-sm p-button-outlined p-button-rounded\">\r\n        <div class=\"flex gap-1\">\r\n           <span> Create subfolder </span> <img src=\"/assets/icons/folder-add.svg\" height=\"16\" />\r\n        </div>\r\n        </button> -->\r\n                </div>\r\n            </div>\r\n            <ng-container *ngIf=\"files.length === 0 && !canUpload\">\r\n                <div class=\"flex flex-column gap-1 align-items-center justify-content-center\">\r\n                    <img src=\"/assets/icons/library/file_manager_emp.png\" alt=\"file manager empty icon\" class=\"add empty hvr-grow\">\r\n                    <p class=\"font-base max-w-14rem text-center\">\r\n                        <ng-container *ngIf=\"authService.isStudent\">\r\n                            No files found.\r\n                        </ng-container>\r\n                        <ng-container *ngIf=\"authService.isTeacher\">\r\n                            No files found.\r\n                        </ng-container>\r\n                    </p>\r\n                </div>\r\n            </ng-container>\r\n\r\n            <ng-container *ngIf=\"files.length === 0 && canUpload\">\r\n                <div class=\"flex flex-column gap-1 align-items-center justify-content-center\">\r\n                    <img src=\"/assets/icons/library/add-lib.svg\" (click)=\"onShowUploadDialog()\" class=\"add lg hvr-grow\">\r\n                    <p class=\"font-base max-w-14rem text-center\">\r\n                        <!-- There are currently no files {{withClassroom ? 'in this classroom' : ''}}. -->\r\n                        Upload your first file!\r\n                    </p>\r\n                </div>\r\n            </ng-container>\r\n            <div class=\"allFiles\" id=\"allFiles\" *ngIf=\"getFilteredFiles(uniqueFiles).length > 0\">\r\n                    <ul class=\"library-file-list\"  [style.height]=\"fileListHeight + 'px'\">\r\n                        <!--  | filter: term -->\r\n                        <ng-container *ngFor=\"let libFiles of getFilteredFiles(uniqueFiles) | filter: { term: term }\">\r\n                            <app-library-file-row-item [showGroupActions]=\"showGroupActions\"\r\n                                [showSharedWithUsers]=\"withClassroom\"\r\n                                appendDialogActionsTo=\"allFiles\" [classroom]=\"classroom\" [classroomFile]=\"libFiles\"\r\n                                [hasLimitedOptions]=\"hasLimitedOptions\"\r\n                                [isSmallScreen]=\"isSmallScreen\"\r\n                                [isInTab]=\"isInTab\"\r\n                                (fileDeleted)=\"onFileDeleted($event)\" [folders]=\"folders\"\r\n                                [availableActions]=\"availableActions\"\r\n                                (libraryChecked)=\"onLibraryChecked($event)\"\r\n                                (fileUnshared)=\"onFileUnshared($event)\"\r\n                                [classroomStudents]=\"classroomStudents\"></app-library-file-row-item>\r\n                        </ng-container>\r\n\r\n                    </ul>\r\n            </div>\r\n        </ng-template>\r\n\r\n    </div>\r\n    <!-- ends block-gradient -->\r\n</div>\r\n\r\n<ng-template #classInfoStudentTemplate let-student=\"student\" let-classroom=\"classroom\">\r\n    hi\r\n</ng-template>"], "mappings": "AAEA,SAA4BA,IAAI,QAAa,gBAAgB;AAI7D,SAAeC,QAAQ,QAAQ,gCAAgC;AAM/D,SAASC,OAAO,QAAQ,SAAS;AAKjC,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,iCAAiC,QAAQ,sEAAsE;AAExH,SAASC,UAAU,QAAQ,gCAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICpB3DC,EAAA,CAAAC,uBAAA,GAAyD;IACrDD,EAAA,CAAAE,cAAA,4BAGwB;IAH4CF,EAAhD,CAAAG,UAAA,4BAAAC,4FAAAC,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAkBF,MAAA,CAAAG,oBAAA,CAAAN,MAAA,CAA4B;IAAA,EAAC,6BAAAO,6FAAAP,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAoBF,MAAA,CAAAK,oBAAA,CAAAR,MAAA,CAA4B;IAAA,EAAC;IAG5FL,EAAA,CAAAc,YAAA,EAAqB;;;;;IADpBd,EAAA,CAAAe,SAAA,EAAkD;IAAlDf,EAAA,CAAAgB,UAAA,mBAAAR,MAAA,CAAAS,uBAAA,CAAAC,OAAA,CAAkD;;;;;;IAG/ElB,EAAA,CAAAC,uBAAA,GAAuD;IACnDD,EAAA,CAAAE,cAAA,4BAE8F;IAF5BF,EAA9C,CAAAG,UAAA,4BAAAgB,4FAAAd,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAc,GAAA;MAAA,MAAAZ,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAkBF,MAAA,CAAAa,kBAAA,CAAAhB,MAAA,CAA0B;IAAA,EAAC,6BAAAiB,6FAAAjB,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAc,GAAA;MAAA,MAAAZ,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAoBF,MAAA,CAAAe,kBAAA,CAAAlB,MAAA,CAA0B;IAAA,EAAC;IAElBL,EAAA,CAAAc,YAAA,EAAqB;;;;;IAA1Fd,EAAA,CAAAe,SAAA,EAAgD;IAAhDf,EAAA,CAAAgB,UAAA,mBAAAR,MAAA,CAAAgB,qBAAA,CAAAN,OAAA,CAAgD;;;;;;IAE7ElB,EAAA,CAAAC,uBAAA,GAAgE;IAC5DD,EAAA,CAAAE,cAAA,4BAEuG;IAFrCF,EAA9C,CAAAG,UAAA,4BAAAsB,4FAAApB,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAoB,GAAA;MAAA,MAAAlB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAkBF,MAAA,CAAAa,kBAAA,CAAAhB,MAAA,CAA0B;IAAA,EAAC,6BAAAsB,6FAAAtB,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAoB,GAAA;MAAA,MAAAlB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAoBF,MAAA,CAAAoB,0BAAA,CAAAvB,MAAA,CAAkC;IAAA,EAAC;IAEjBL,EAAA,CAAAc,YAAA,EAAqB;;;;;IAAnGd,EAAA,CAAAe,SAAA,EAAyD;IAAzDf,EAAA,CAAAgB,UAAA,mBAAAR,MAAA,CAAAqB,8BAAA,CAAAX,OAAA,CAAyD;;;;;IA8B1ElB,EAAA,CAAA8B,kBAAA,GACe;;;;;IAFnB9B,EAAA,CAAAC,uBAAA,GAAgG;IAC5FD,EAAA,CAAA+B,UAAA,IAAAC,6DAAA,2BAA8F;;;;;;IAA/EhC,EAAA,CAAAe,SAAA,EAA4C;IAAAf,EAA5C,CAAAgB,UAAA,qBAAAiB,2BAAA,CAA4C,4BAAAjC,EAAA,CAAAkC,eAAA,IAAAC,GAAA,EAAA3B,MAAA,CAAA4B,SAAA,EAAiC;;;;;IAQhFpC,EAAA,CAAA8B,kBAAA,GACe;;;;;IAMf9B,EAAA,CAAA8B,kBAAA,GAEe;;;;;IAJvB9B,EAAA,CAAAC,uBAAA,GAA4E;IACxED,EAAA,CAAAE,cAAA,qBAAiD;IAC7CF,EAAA,CAAA+B,UAAA,IAAAM,6EAAA,2BAC8G;IAElHrC,EAAA,CAAAc,YAAA,EAAa;;;;;;;IAJDd,EAAA,CAAAe,SAAA,EAAoC;IAApCf,EAAA,CAAAgB,UAAA,WAAAsB,kBAAA,CAAAC,SAAA,CAAoC;IAEvCvC,EAAA,CAAAe,SAAA,EAA4C;IAAAf,EAA5C,CAAAgB,UAAA,qBAAAiB,2BAAA,CAA4C,4BAAAjC,EAAA,CAAAwC,eAAA,IAAAC,GAAA,EAAAjC,MAAA,CAAA4B,SAAA,EAAAE,kBAAA,EAA2D;;;;;;IAZ5HtC,EAAA,CAAAC,uBAAA,GAAkH;IAC9GD,EAAA,CAAAE,cAAA,oBAA4H;IAArEF,EAAA,CAAAG,UAAA,sBAAAuC,8EAAArC,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAqC,GAAA;MAAA,MAAAnC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAYF,MAAA,CAAAoC,gCAAA,CAAAvC,MAAA,CAAwC;IAAA,EAAC;IAEpGL,EADJ,CAAAE,cAAA,qBAAyB,cACa;IAC9BF,EAAA,CAAA+B,UAAA,IAAAc,8DAAA,2BAAiH;IAIzH7C,EADI,CAAAc,YAAA,EAAM,EACG;IACbd,EAAA,CAAA+B,UAAA,IAAAe,8DAAA,2BAA4E;IAOhF9C,EAAA,CAAAc,YAAA,EAAY;;;;;;IAfDd,EAAA,CAAAe,SAAA,EAA2C;IAA3Cf,EAAA,CAAAgB,UAAA,eAAAR,MAAA,CAAAuC,iBAAA,CAAAC,MAAA,KAA2C;IAG3BhD,EAAA,CAAAe,SAAA,GAA4C;IAAAf,EAA5C,CAAAgB,UAAA,qBAAAiB,2BAAA,CAA4C,4BAAAjC,EAAA,CAAAkC,eAAA,IAAAe,GAAA,EAAAzC,MAAA,CAAA4B,SAAA,EAAoD;IAK7EpC,EAAA,CAAAe,SAAA,EAAsB;IAAtBf,EAAA,CAAAgB,UAAA,YAAAR,MAAA,CAAAuC,iBAAA,CAAsB;;;;;;IA4B5D/C,EAAA,CAAAE,cAAA,cAC8C;IAD4CF,EAAA,CAAAG,UAAA,mBAAA+C,0EAAA;MAAAlD,EAAA,CAAAM,aAAA,CAAA6C,IAAA;MAAA,MAAA3C,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA4C,eAAA,EAAiB;IAAA,EAAC;IAArHpD,EAAA,CAAAc,YAAA,EAC8C;;;;IAA9Cd,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAkC,eAAA,IAAAmB,GAAA,EAAA7C,MAAA,CAAA8C,gBAAA,EAA6C;;;;;;IAC7CtD,EAAA,CAAAE,cAAA,cAAuI;IAApDF,EAAA,CAAAG,UAAA,mBAAAoD,0EAAA;MAAAvD,EAAA,CAAAM,aAAA,CAAAkD,IAAA;MAAA,MAAAhD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAiD,kBAAA,EAAoB;IAAA,EAAC;IAAjHzD,EAAA,CAAAc,YAAA,EAAuI;;;;;;IAmCnId,EADJ,CAAAE,cAAA,cAAoC,oCACkE;IAAtCF,EAAjC,CAAAG,UAAA,mBAAAuD,wGAAA;MAAA1D,EAAA,CAAAM,aAAA,CAAAqD,IAAA;MAAA,MAAAnD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAAoD,SAAA,IAAApD,MAAA,CAAAoD,SAAA;IAAA,EAAgC,yBAAAC,8GAAAxD,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAqD,IAAA;MAAA,MAAAnD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAgBF,MAAA,CAAAsD,aAAA,CAAAzD,MAAA,CAAqB;IAAA,EAAC;IACjGL,EAAA,CAAAc,YAAA,EAA4B;IAC5Bd,EAAA,CAAAE,cAAA,cAAuE;IAA9BF,EAAA,CAAAG,UAAA,mBAAA4D,kFAAA;MAAA/D,EAAA,CAAAM,aAAA,CAAAqD,IAAA;MAAA,MAAAnD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAwD,iBAAA,EAAmB;IAAA,EAAC;IAClEhE,EAAA,CAAAiE,MAAA,cACJ;IACJjE,EADI,CAAAc,YAAA,EAAM,EACJ;;;;;;IAhCFd,EAHR,CAAAE,cAAA,cAA+G,cAEhF,cACK;IACxBF,EAAA,CAAAkE,SAAA,cAA+D;IACnElE,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAE,cAAA,cAAwE;IAA5BF,EAAA,CAAAG,UAAA,mBAAAgE,2EAAA;MAAAnE,EAAA,CAAAM,aAAA,CAAA8D,IAAA;MAAA,MAAA5D,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA6D,eAAA,EAAiB;IAAA,EAAC;IACnErE,EAAA,CAAAiE,MAAA,cACJ;IAIJjE,EAJI,CAAAc,YAAA,EAAM,EAIJ;IACNd,EAAA,CAAAE,cAAA,cAAyD;IAA9BF,EAAA,CAAAG,UAAA,mBAAAmE,2EAAA;MAAAtE,EAAA,CAAAM,aAAA,CAAA8D,IAAA;MAAA,MAAA5D,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA+D,iBAAA,EAAmB;IAAA,EAAC;IACpDvE,EAAA,CAAAE,cAAA,cAA4B;IACxBF,EAAA,CAAAkE,SAAA,cAAsD;IAC1DlE,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAE,cAAA,cAA4C;IACxCF,EAAA,CAAAiE,MAAA,cACJ;IACJjE,EADI,CAAAc,YAAA,EAAM,EACJ;IACNd,EAAA,CAAAE,cAAA,eAA2D;IAAhCF,EAAA,CAAAG,UAAA,mBAAAqE,4EAAA;MAAAxE,EAAA,CAAAM,aAAA,CAAA8D,IAAA;MAAA,MAAA5D,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAiE,mBAAA,EAAqB;IAAA,EAAC;IACtDzE,EAAA,CAAAE,cAAA,eAA4B;IACxBF,EAAA,CAAAkE,SAAA,eAAwD;IAC5DlE,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAE,cAAA,eAA4C;IACxCF,EAAA,CAAAiE,MAAA,gBACJ;IACJjE,EADI,CAAAc,YAAA,EAAM,EACJ;IACNd,EAAA,CAAA+B,UAAA,KAAA2C,4DAAA,kBAAoC;IAOxC1E,EAAA,CAAAc,YAAA,EAAM;;;;IAPiBd,EAAA,CAAAe,SAAA,IAAe;IAAff,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAAoD,SAAA,CAAe;;;;;;IAqB1B5D,EADJ,CAAAE,cAAA,cAAgF,yCAM3E;IADDF,EADA,CAAAG,UAAA,0BAAAwE,6GAAAtE,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAsE,IAAA;MAAA,MAAApE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAgBF,MAAA,CAAAqE,oBAAA,CAAAxE,MAAA,CAA4B;IAAA,EAAC,wBAAAyE,2GAAAzE,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAsE,IAAA;MAAA,MAAApE,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAC/BF,MAAA,CAAAuE,kBAAA,CAAA1E,MAAA,CAA0B;IAAA,EAAC;IAE7CL,EADK,CAAAc,YAAA,EAAiC,EAChC;;;;;IALFd,EAAA,CAAAe,SAAA,EAAsB;IACtBf,EADA,CAAAgB,UAAA,WAAAgE,eAAA,CAAsB,sBAAAxE,MAAA,CAAAyE,iBAAA,CACiB;;;;;;IAO/CjF,EAAA,CAAAE,cAAA,cAAiK;IAA7GF,EAAA,CAAAG,UAAA,mBAAA+E,2EAAA;MAAAlF,EAAA,CAAAM,aAAA,CAAA6E,IAAA;MAAA,MAAA3E,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA4E,YAAA,EAAc;IAAA,EAAC;IAA5EpF,EAAA,CAAAc,YAAA,EAAiK;;;;;;IAkCjKd,EAAA,CAAAE,cAAA,cACiE;IAD9BF,EAAA,CAAAG,UAAA,mBAAAkF,2EAAA;MAAArF,EAAA,CAAAM,aAAA,CAAAgF,IAAA;MAAA,MAAA9E,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA+E,UAAA,CAAA/E,MAAA,CAAAgF,UAAA,CAAAhF,MAAA,CAAAgF,UAAA,CAAAxC,MAAA,GAAwC,CAAC,EAAE;IAAA,EAAC;IAAxFhD,EAAA,CAAAc,YAAA,EACiE;;;;;;IAWrEd,EAAA,CAAAE,cAAA,cAA+G;IAAxEF,EAAA,CAAAG,UAAA,mBAAAsF,2EAAA;MAAA,MAAAC,UAAA,GAAA1F,EAAA,CAAAM,aAAA,CAAAqF,IAAA,EAAAC,SAAA;MAAA,MAAApF,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA+E,UAAA,CAAAG,UAAA,CAAkB;IAAA,EAAC;IAC/D1F,EAAA,CAAAiE,MAAA,GACJ;IAAAjE,EAAA,CAAAc,YAAA,EAAM;;;;IADFd,EAAA,CAAAe,SAAA,EACJ;IADIf,EAAA,CAAA6F,kBAAA,YAAAH,UAAA,CAAAI,IAAA,aACJ;;;;;;IAGI9F,EAAA,CAAAC,uBAAA,GAAgC;IAExBD,EADJ,CAAAE,cAAA,cAA4C,YAC4C;IAAzBF,EAAA,CAAAG,UAAA,mBAAA4F,kFAAA;MAAA/F,EAAA,CAAAM,aAAA,CAAA0F,IAAA;MAAA,MAAAxF,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA4E,YAAA,EAAc;IAAA,EAAC;IAACpF,EAAA,CAAAc,YAAA,EAAI;IACxFd,EAAA,CAAAE,cAAA,gBACmE;IAD/CF,EAAA,CAAAiG,gBAAA,2BAAAC,8FAAA7F,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAA0F,IAAA;MAAA,MAAAxF,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAT,EAAA,CAAAmG,kBAAA,CAAA3F,MAAA,CAAA4F,UAAA,EAAA/F,MAAA,MAAAG,MAAA,CAAA4F,UAAA,GAAA/F,MAAA;MAAA,OAAAL,EAAA,CAAAU,WAAA,CAAAL,MAAA;IAAA,EAAwB;IAA5CL,EAAA,CAAAc,YAAA,EACmE;IACnEd,EAAA,CAAAE,cAAA,iBACsB;IADAF,EAAA,CAAAG,UAAA,mBAAAkG,uFAAA;MAAArG,EAAA,CAAAM,aAAA,CAAA0F,IAAA;MAAA,MAAAxF,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA8F,cAAA,EAAgB;IAAA,EAAC;IAC1BtG,EAAA,CAAAE,cAAA,eACS;IAAAF,EAAA,CAAAiE,MAAA,aAAM;IACzCjE,EADyC,CAAAc,YAAA,EAAO,EAAS,EACnD;;;;;IALkBd,EAAA,CAAAe,SAAA,GAAwB;IAAxBf,EAAA,CAAAuG,gBAAA,YAAA/F,MAAA,CAAA4F,UAAA,CAAwB;;;;;IAsBhDpG,EAAA,CAAAC,uBAAA,GAA4C;IACxCD,EAAA,CAAAiE,MAAA,wBACJ;;;;;;IACAjE,EAAA,CAAAC,uBAAA,GAA4C;IACxCD,EAAA,CAAAiE,MAAA,wBACJ;;;;;;IATZjE,EAAA,CAAAC,uBAAA,GAAuD;IACnDD,EAAA,CAAAE,cAAA,cAA8E;IAC1EF,EAAA,CAAAkE,SAAA,cAA+G;IAC/GlE,EAAA,CAAAE,cAAA,YAA6C;IAIzCF,EAHA,CAAA+B,UAAA,IAAAyE,6EAAA,0BAA4C,IAAAC,6EAAA,0BAGA;IAIpDzG,EADI,CAAAc,YAAA,EAAI,EACF;;;;;IAPiBd,EAAA,CAAAe,SAAA,GAA2B;IAA3Bf,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAAkG,WAAA,CAAAC,SAAA,CAA2B;IAG3B3G,EAAA,CAAAe,SAAA,EAA2B;IAA3Bf,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAAkG,WAAA,CAAAE,SAAA,CAA2B;;;;;;IAOtD5G,EAAA,CAAAC,uBAAA,GAAsD;IAE9CD,EADJ,CAAAE,cAAA,cAA8E,cAC0B;IAAvDF,EAAA,CAAAG,UAAA,mBAAA0G,oFAAA;MAAA7G,EAAA,CAAAM,aAAA,CAAAwG,IAAA;MAAA,MAAAtG,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAiD,kBAAA,EAAoB;IAAA,EAAC;IAA3EzD,EAAA,CAAAc,YAAA,EAAoG;IACpGd,EAAA,CAAAE,cAAA,YAA6C;IAEzCF,EAAA,CAAAiE,MAAA,gCACJ;IACJjE,EADI,CAAAc,YAAA,EAAI,EACF;;;;;;;IAKEd,EAAA,CAAAC,uBAAA,GAA8F;IAC1FD,EAAA,CAAAE,cAAA,oCAU4C;IADxCF,EAHA,CAAAG,UAAA,yBAAA4G,sHAAA1G,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAeF,MAAA,CAAAyG,aAAA,CAAA5G,MAAA,CAAqB;IAAA,EAAC,4BAAA6G,yHAAA7G,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAEnBF,MAAA,CAAA2G,gBAAA,CAAA9G,MAAA,CAAwB;IAAA,EAAC,0BAAA+G,uHAAA/G,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAA0G,IAAA;MAAA,MAAAxG,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAC3BF,MAAA,CAAA6G,cAAA,CAAAhH,MAAA,CAAsB;IAAA,EAAC;IACCL,EAAA,CAAAc,YAAA,EAA4B;;;;;;;IAV7Cd,EAAA,CAAAe,SAAA,EAAqC;IAU5Df,EAVuB,CAAAgB,UAAA,qBAAAR,MAAA,CAAA8C,gBAAA,CAAqC,wBAAA9C,MAAA,CAAA8G,aAAA,CACvB,cAAAC,aAAA,CACmB,kBAAAC,YAAA,CAA2B,sBAAAhH,MAAA,CAAAyE,iBAAA,CAC5C,kBAAAzE,MAAA,CAAAiH,aAAA,CACR,YAAAjH,MAAA,CAAAkH,OAAA,CACZ,YAAAlH,MAAA,CAAAmH,OAAA,CACsC,qBAAAnH,MAAA,CAAAoH,gBAAA,CACpB,sBAAApH,MAAA,CAAAuC,iBAAA,CAGE;;;;;IAbnD/C,EADR,CAAAE,cAAA,cAAqF,aACP;IAElEF,EAAA,CAAA+B,UAAA,IAAA8F,oEAAA,4BAA8F;;IAe1G7H,EADQ,CAAAc,YAAA,EAAK,EACP;;;;;IAjBiCd,EAAA,CAAAe,SAAA,EAAsC;IAAtCf,EAAA,CAAA8H,WAAA,WAAAtH,MAAA,CAAAuH,cAAA,QAAsC;IAE9B/H,EAAA,CAAAe,SAAA,EAAyD;IAAzDf,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAgI,WAAA,OAAAxH,MAAA,CAAAyH,gBAAA,CAAAC,eAAA,GAAAlI,EAAA,CAAAkC,eAAA,IAAAiG,IAAA,EAAA3H,MAAA,CAAA4H,IAAA,GAAyD;;;;;;IApLpGpI,EADJ,CAAAE,cAAA,cAAsG,UACM;IACpGF,EAAA,CAAAiE,MAAA,GACJ;IAAAjE,EAAA,CAAAc,YAAA,EAAM;IAGEd,EAFR,CAAAE,cAAA,cAA+G,eACnE,gBAEyB;IAD1CF,EAAA,CAAAiG,gBAAA,2BAAAoC,8EAAAhI,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAgI,GAAA;MAAA,MAAA9H,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAAT,EAAA,CAAAmG,kBAAA,CAAA3F,MAAA,CAAA4H,IAAA,EAAA/H,MAAA,MAAAG,MAAA,CAAA4H,IAAA,GAAA/H,MAAA;MAAA,OAAAL,EAAA,CAAAU,WAAA,CAAAL,MAAA;IAAA,EAAkB;IAArCL,EAAA,CAAAc,YAAA,EAC6D;IAC7Dd,EAAA,CAAAkE,SAAA,YAAyC;IAC7ClE,EAAA,CAAAc,YAAA,EAAO;IAUPd,EAFA,CAAA+B,UAAA,IAAAwG,oDAAA,kBAC8C,IAAAC,oDAAA,kBACyF;IAE/IxI,EADI,CAAAc,YAAA,EAAM,EACJ;IAENd,EAAA,CAAAE,cAAA,cAA4D;IACxDF,EAAA,CAAA+B,UAAA,KAAA0G,qDAAA,mBAA+G;IAqCnHzI,EAAA,CAAAc,YAAA,EAAM;IAIEd,EAFR,CAAAE,cAAA,eAAwB,eACO,eACmF;IACtGF,EAAA,CAAAkE,SAAA,eAAkD;IAClDlE,EAAA,CAAAE,cAAA,cAAiF;IAC7EF,EAAA,CAAAiE,MAAA,IACJ;IACJjE,EADI,CAAAc,YAAA,EAAK,EACH;IAENd,EAAA,CAAAE,cAAA,eAAqF;IACjFF,EAAA,CAAA+B,UAAA,KAAA2G,qDAAA,kBAAgF;IAQpF1I,EAAA,CAAAc,YAAA,EAAM;IAENd,EAAA,CAAA+B,UAAA,KAAA4G,qDAAA,kBAAiK;IAIjK3I,EAAA,CAAAkE,SAAA,eAmBM;IAGdlE,EADI,CAAAc,YAAA,EAAM,EACJ;IAMFd,EALJ,CAAAE,cAAA,eAAyB,eAIwB,eACb;IAE5BF,EAAA,CAAA+B,UAAA,KAAA6G,qDAAA,kBACiE;IACjE5I,EAAA,CAAAiE,MAAA,gBAEA;IACJjE,EAAA,CAAAc,YAAA,EAAM;IAENd,EAAA,CAAAkE,SAAA,eAA0C;IAC1ClE,EAAA,CAAAiE,MAAA,gBAEA;IAAAjE,EAAA,CAAAkE,SAAA,eAAuC;IACvClE,EAAA,CAAAiE,MAAA,gBACA;IAAAjE,EAAA,CAAA+B,UAAA,KAAA8G,qDAAA,kBAA+G;IAGnH7I,EAAA,CAAAc,YAAA,EAAM;IACFd,EAAA,CAAAE,cAAA,eAAqB;IACjBF,EAAA,CAAA+B,UAAA,KAAA+G,8DAAA,0BAAgC;IAoBxC9I,EADI,CAAAc,YAAA,EAAM,EACJ;IAwBNd,EAvBA,CAAA+B,UAAA,KAAAgH,8DAAA,0BAAuD,KAAAC,8DAAA,0BAcD,KAAAC,qDAAA,kBAS+B;;;;;IAlLbjJ,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAkC,eAAA,KAAAgH,GAAA,EAAA1I,MAAA,CAAAkH,OAAA,EAA6B;IAC5F1H,EAAA,CAAAe,SAAA,EAAkG;IAAlGf,EAAA,CAAAmJ,sBAAA,gEAAA3I,MAAA,CAAA4I,kBAAA,cAAkG;IACnGpJ,EAAA,CAAAe,SAAA,EACJ;IADIf,EAAA,CAAA6F,kBAAA,MAAArF,MAAA,CAAA6I,YAAA,MACJ;IAG2BrJ,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAuG,gBAAA,YAAA/F,MAAA,CAAA4H,IAAA,CAAkB;IAWnCpI,EAAA,CAAAe,SAAA,GAAc;IAAdf,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAA8I,QAAA,CAAc;IAEdtJ,EAAA,CAAAe,SAAA,EAAe;IAAff,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAA+I,SAAA,CAAe;IAKnBvJ,EAAA,CAAAe,SAAA,GAAsB;IAAtBf,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAA8C,gBAAA,CAAsB;IAyCwCtD,EAAA,CAAAe,SAAA,GAAyC;IAAzCf,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAkC,eAAA,KAAAsH,GAAA,EAAAhJ,MAAA,CAAAkH,OAAA,EAAyC;IAGjG1H,EAAA,CAAAe,SAAA,GACJ;IADIf,EAAA,CAAA6F,kBAAA,MAAArF,MAAA,CAAAiJ,YAAA,CAAAC,MAAA,CAAA5D,IAAA,MACJ;IAI+D9F,EAAA,CAAAe,SAAA,GAAe;IAAff,EAAA,CAAAgB,UAAA,YAAAR,MAAA,CAAAmJ,YAAA,CAAe;IAU5E3J,EAAA,CAAAe,SAAA,EAAe;IAAff,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAA+I,SAAA,CAAe;IAkCfvJ,EAAA,CAAAe,SAAA,GAA2B;IAA3Bf,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAAgF,UAAA,CAAAxC,MAAA,KAA2B;IAYbhD,EAAA,CAAAe,SAAA,GAAa;IAAbf,EAAA,CAAAgB,UAAA,YAAAR,MAAA,CAAAgF,UAAA,CAAa;IAKlBxF,EAAA,CAAAe,SAAA,GAAe;IAAff,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAAoJ,SAAA,CAAe;IAqBvB5J,EAAA,CAAAe,SAAA,EAAsC;IAAtCf,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAAqJ,KAAA,CAAA7G,MAAA,WAAAxC,MAAA,CAAA+I,SAAA,CAAsC;IActCvJ,EAAA,CAAAe,SAAA,EAAqC;IAArCf,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAAqJ,KAAA,CAAA7G,MAAA,UAAAxC,MAAA,CAAA+I,SAAA,CAAqC;IASfvJ,EAAA,CAAAe,SAAA,EAA8C;IAA9Cf,EAAA,CAAAgB,UAAA,SAAAR,MAAA,CAAAyH,gBAAA,CAAAC,eAAA,EAAAlF,MAAA,KAA8C;;;;;IA0B3FhD,EAAA,CAAAiE,MAAA,YACJ;;;ADrPA,WAAa6F,sBAAsB;EAA7B,MAAOA,sBAAsB;IAgFjCC,YACUC,cAA8B,EAC9BC,gBAAkC,EAClCC,cAA8B,EAC/BxD,WAAwB,EACvByD,mBAAwC,EACxCC,YAA0B,EAC1BC,aAA4B,EAC5BC,aAA4B,EAC5BC,WAAwB;MARxB,KAAAP,cAAc,GAAdA,cAAc;MACd,KAAAC,gBAAgB,GAAhBA,gBAAgB;MAChB,KAAAC,cAAc,GAAdA,cAAc;MACf,KAAAxD,WAAW,GAAXA,WAAW;MACV,KAAAyD,mBAAmB,GAAnBA,mBAAmB;MACnB,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,WAAW,GAAXA,WAAW;MAnFZ,KAAAC,UAAU,GAAG,IAAI;MAI1B,KAAAC,SAAS,GAAG,KAAK;MACjB,KAAA7G,SAAS,GAAY,KAAK;MAC1B,KAAA8G,gBAAgB,GAAa,EAAE;MAC/B,KAAAlF,UAAU,GAAa,CAAC,IAAI,CAACwE,cAAc,CAACW,UAAU,CAAC;MAGvD,KAAAC,UAAU,GAAG,KAAK;MAClB,KAAAC,oBAAoB,GAAG,KAAK;MAC5B,KAAAC,iBAAiB,GAAG,KAAK;MACzB,KAAAC,UAAU,GAAG,CAAC;MACd,KAAAC,YAAY,GAAG,CAAC;MAEhB,KAAAC,SAAS,GAAGtL,QAAQ;MACb,KAAAuL,IAAI,GAAW,EAAE;MACjB,KAAAC,YAAY,GAAS,EAAU;MAC7B,KAAAC,UAAU,GAAY,KAAK;MAC3B,KAAAC,IAAI,GAAiB,EAAkB;MACvC,KAAAC,mBAAmB,GAAW,CAAC;MAE/B,KAAAhE,aAAa,GAAG,KAAK;MACrB,KAAAiC,SAAS,GAAG,IAAI;MAChB,KAAAD,QAAQ,GAAG,IAAI;MACf,KAAArE,iBAAiB,GAAG,KAAK;MACzB,KAAA2C,gBAAgB,GAAG,EAAE;MAErB,KAAAyB,YAAY,GAAG,UAAU;MACzB,KAAA3B,OAAO,GAAG,KAAK;MACf,KAAA0B,kBAAkB,GAAG,QAAQ;MAC7B,KAAArB,cAAc,GAAG,CAAC;MAGnB,KAAAwD,IAAI,GAAG,IAAI3L,OAAO,EAAE;MAE5B,KAAA4L,WAAW,GAAY,KAAK;MAC5B,KAAAlI,gBAAgB,GAAY,KAAK;MACjC,KAAAmI,cAAc,GAAY,KAAK;MAC/B,KAAAC,gBAAgB,GAAuB,EAAE;MAChC,KAAAC,qBAAqB,GAAuB,EAAE;MAC9C,KAAAC,KAAK,GAAW,EAAE;MAC3B,KAAAC,oBAAoB,GAAc,EAAE;MACpC,KAAAlC,YAAY,GAAa,EAAE;MAC3B,KAAAF,YAAY,GAAY,EAAa;MACrC,KAAArD,UAAU,GAAW,EAAE;MACvB,KAAAwD,SAAS,GAAY,KAAK;MAC1B,KAAAkC,IAAI,GAAS,EAAU;MACvB,KAAAnE,OAAO,GAAa,EAAE;MACtB,KAAAoE,WAAW,GAAY,IAAI;MAC3B,KAAAC,WAAW,GAAY,IAAI;MAC3B,KAAAnC,KAAK,GAAkB,EAAE;MACzB,KAAAoC,aAAa,GAAkB,EAAE;MACjC,KAAAC,cAAc,GAAuB,EAAE;MACvC,KAAAC,YAAY,GAAuB,EAAE;MACrC,KAAAC,mBAAmB,GAAU,EAAE;MAC/B,KAAAnL,uBAAuB,GAAG;QACxBoL,UAAU,EAAE,KAAK;QACjBnL,OAAO,EAAE;OACV;MACD,KAAAM,qBAAqB,GAAG;QACtB6K,UAAU,EAAE,KAAK;QACjBnL,OAAO,EAAE;OACV;MACD,KAAAW,8BAA8B,GAAG;QAC/BwK,UAAU,EAAE,KAAK;QACjBnL,OAAO,EAAE;OACV;MAED,KAAAoL,mBAAmB,GAAkB,EAAE;MACvC,KAAA7E,aAAa,GAAG,KAAK;MACrB,KAAA8E,mBAAmB,GAAG,EAAY;MAClC,KAAAC,oBAAoB,GAAG,EAAE;IAWrB;IAEJC,QAAQA,CAAA;MAEN;MACA;MACA;MACA;MACA,IAAI,IAAI,CAAC1J,iBAAiB,EAAE;QAC1B,IAAI,CAACA,iBAAiB,GAAG2J,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAAC,IAAI,CAAC7J,iBAAiB,CAAC8J,GAAG,CAAEC,OAAY,IAAKA,OAAO,CAACC,SAAS,CAAC,CAAC,CAAC,CAC5GF,GAAG,CAACE,SAAS,IAAI,IAAI,CAAChK,iBAAiB,CAACiK,IAAI,CAAEF,OAAY,IAAKA,OAAO,CAACC,SAAS,KAAKA,SAAS,CAAC,CAAW;QAC3G,IAAI,CAACP,oBAAoB,GAAG,IAAI,CAACzJ,iBAAiB,CAAC8J,GAAG,CAAEC,OAAY,IAAKA,OAAO,CAACC,SAAS,CAAC;MAC7F;MACA,IAAI,IAAI,CAAChK,iBAAiB,IAAI,IAAI,CAACA,iBAAiB,CAACC,MAAM,KAAK,CAAC,EAAE;QACjE,IAAI,CAACiK,sBAAsB,CAAC,IAAI,CAAClK,iBAAiB,CAAC,CAAC,CAAC,CAACgK,SAAS,CAAC;MAClE;MACA,IAAI,CAACG,8BAA8B,CAAC,IAAI,CAAC;MACzCC,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC9B,mBAAmB,CAAC;MACrC;MACA,IAAI,CAACH,YAAY,GAAG,IAAI,CAACzE,WAAW,CAAC2G,eAAe,EAAE;MACtD,IAAI,CAACnC,IAAI,GAAG,IAAI,CAACxE,WAAW,CAAC4G,WAAW,EAAE;MAC1C,IAAI,CAACC,KAAK,GAAG,CACX;QACEC,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE,kBAAkB;QAC1CC,OAAO,EAAGC,KAAK,IAAI,CACnB;OACD,EACD;QAAEH,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAmB,CAAE,CAC/C;MAED,IAAI,CAAClC,IAAI,CAACqC,IAAI,GAAG,IAAI,CAAC1D,cAAc,CAAC2D,UAAU,CAACC,SAAS,CAACD,UAAU,IAAG;QACrE,IAAI,CAACpG,aAAa,GAAG,CAACoG,UAAU,CAACE,MAAM;MACzC,CAAC,CAAC;MAEF,IAAI,CAACxC,IAAI,CAACqC,IAAI,GAAG,IAAI,CAAC5D,cAAc,CAAC1G,gBAAgB,CAACwK,SAAS,CAACE,GAAG,IAAG;QACpE,IAAI,CAAC1K,gBAAgB,GAAG0K,GAAG;MAC7B,CAAC,CAAC;MAEF,IAAI,CAACzC,IAAI,CAACqC,IAAI,GAAG,IAAI,CAACvD,aAAa,CAAC4D,cAAc,CAACH,SAAS,CAAEE,GAAG,IAAI;QACnE;QACA;QAEA;QACA;MAAA,CACD,CAAC;MAEF,IAAI,CAACzC,IAAI,CAACqC,IAAI,GAAG,IAAI,CAAC5D,cAAc,CAACkE,oBAAoB,CAACJ,SAAS,CAACE,GAAG,IAAG;QACxE,IAAIA,GAAG,CAACtE,MAAM,CAACyE,QAAS,GAAG,CAAC,EAAE;UAC5B,KAAK,IAAIC,GAAG,IAAI,IAAI,CAACzC,qBAAqB,EAAE;YAC1C,IAAI0C,KAAK,GAAY,KAAK;YAC1B,KAAK,IAAIC,IAAI,IAAIF,GAAG,CAACG,YAAY,EAAE;cACjC,IAAID,IAAI,CAACE,MAAM,IAAIR,GAAG,CAACS,WAAW,CAACD,MAAM,EAAE;gBACzCH,KAAK,GAAG,IAAI;cACd;YACF;YACA,IAAIA,KAAK,EAAE;cACTD,GAAG,CAACD,QAAQ,GAAGH,GAAG,CAACtE,MAAM,CAACyE,QAAS;cACnCC,GAAG,CAACtI,IAAI,GAAGkI,GAAG,CAACtE,MAAM,CAAC5D,IAAI;cAC1BsI,GAAG,CAACM,MAAM,GAAGV,GAAG,CAACtE,MAAM,CAACgF,MAAM;YAChC;UACF;UAEA,IAAI,CAACnJ,UAAU,CAAC,IAAI,CAACkE,YAAY,CAACC,MAAM,CAAC;QAC3C;MACF,CAAC,CAAC;MAEF,IAAI,CAACiF,sBAAsB,EAAE;MAE7B,IAAI,CAACC,aAAa,EAAE;MAEpB;MAEA,IAAI,CAACrD,IAAI,CAACqC,IAAI,GAAG,IAAI,CAAC5D,cAAc,CAAC6E,cAAc,CAACf,SAAS,CAACE,GAAG,IAAG;QAClE,IAAIA,GAAG,EAAE;UACP,IAAI,CAACc,mBAAmB,EAAE;QAC5B;MACF,CAAC,CAAC;MAEF,IAAI,CAACvD,IAAI,CAACqC,IAAI,GAAG,IAAI,CAAC5D,cAAc,CAAC+E,cAAc,CAACjB,SAAS,CAACE,GAAG,IAAG;QAClE,IAAIA,GAAG,GAAG,CAAC,EAAE;UAEX,KAAK,IAAII,GAAG,IAAI,IAAI,CAACzC,qBAAqB,EAAE;YAC1C,IAAI0C,KAAK,GAAY,KAAK;YAC1B,KAAK,IAAIC,IAAI,IAAIF,GAAG,CAACG,YAAY,EAAE;cACjC,IAAID,IAAI,CAACE,MAAM,IAAIR,GAAG,EAAE;gBACtBK,KAAK,GAAG,IAAI;cACd;YACF;YACA,IAAIA,KAAK,EAAE;cACTD,GAAG,CAACG,YAAY,GAAGH,GAAG,CAACG,YAAY,CAACS,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACT,MAAM,IAAIR,GAAG,CAAC;YACpE;UACF;UAEA,IAAI,CAACc,mBAAmB,EAAE;QAC5B;MACF,CAAC,CAAC;MACF,IAAI,CAACrF,YAAY,GAAG,IAAI,CAACO,cAAc,CAACkF,4BAA4B,EAAE;MACtE,IAAI,CAACpD,IAAI,GAAG,IAAI,CAACpF,WAAW,CAAC2G,eAAe,EAAE;MAC9C,IAAI,CAACnC,IAAI,GAAG,IAAI,CAACY,IAAI,CAACZ,IAAK;IAC7B;IAEAiE,WAAWA,CAAA;MACT,IAAI,CAACC,SAAS,EAAE;MAChB,IAAI,CAACN,mBAAmB,EAAE;IAC5B;IAEAO,eAAeA,CAAA,GACf;IAEAC,WAAWA,CAAA;MACT,IAAI,CAAC/D,IAAI,CAACgE,WAAW,EAAE;MACvB,IAAI,CAACvF,cAAc,CAAC5G,eAAe,CAAC,KAAK,CAAC;IAC5C;IAEAiB,eAAeA,CAAA;MACb,IAAI,IAAI,CAACiI,mBAAmB,CAACtJ,MAAM,KAAK,CAAC,EAAE;QACzC,IAAI,CAACoH,YAAY,CAACoF,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACF;MACF;MACA,IAAI,CAAC3F,cAAc,CAAC4F,uBAAuB,CAAC,IAAI,CAACtF,aAAa,EAAExK,iCAAiC,EAAE,GAAG,EACpG;QACE+P,MAAM,EAAE,mBAAmB;QAC3BC,cAAc,EAAE,wBAAwB;QACxCC,WAAW,EAAE;OACd,EAAE,UAAU,EAAGC,MAAW,IAAI;QAC/B,IAAIA,MAAM,IAAIA,MAAM,CAACH,MAAM,KAAK,mBAAmB,EAAE;UACnD,IAAI,CAAC7L,iBAAiB,CAACgM,MAAM,CAACnD,GAAG,CAACA,GAAG,CAAC;QACxC;MACF,CAAC,CAAC;IACJ;IAEAoD,yBAAyBA,CAAA,GACzB;IAEA9I,gBAAgBA,CAACwG,KAAU;MACzBR,OAAO,CAACC,GAAG,CAACO,KAAK,CAAC;MAClB,IAAIA,KAAK,CAACuC,OAAO,EAAE;QACjB,IAAI,CAAC5D,mBAAmB,CAAC6D,IAAI,CAACxC,KAAK,CAACW,IAAI,CAAC;MAC3C,CAAC,MAAM;QACL,IAAI,CAAChC,mBAAmB,GAAG,IAAI,CAACA,mBAAmB,CAAC0C,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACmB,IAAI,IAAIzC,KAAK,CAACW,IAAI,CAAC8B,IAAI,CAAC;MAC9F;MACA,IAAI,CAACpG,cAAc,CAACsC,mBAAmB,CAAC+D,GAAG,CAAC,IAAI,CAAC/D,mBAAmB,CAAC;MACrEa,OAAO,CAACC,GAAG,CAAC,IAAI,CAACd,mBAAmB,CAAC;IACvC;IAEAtI,iBAAiBA,CAAC6I,GAAQ;MACxBA,GAAG,CAACyD,OAAO,CAAC,CAACC,KAAa,EAAEC,WAAmB,KAAI;QACjD,IAAIC,GAAG,GAAa,EAAE;QACtB,IAAIC,OAAO,GAAa,EAAE;QAC1B,KAAK,IAAI5E,IAAI,IAAIyE,KAAK,EAAE;UACtBE,GAAG,CAACN,IAAI,CAACrE,IAAI,CAACiB,SAAU,CAAC;QAC3B;QACA,KAAK,IAAIqB,GAAG,IAAI,IAAI,CAAC9B,mBAAmB,EAAE;UACxCoE,OAAO,CAACP,IAAI,CAAC/B,GAAG,CAACI,MAAO,CAAC;QAC3B;QACA,IAAImC,IAAI,GAAQ;UACdD,OAAO,EAAEA,OAAO;UAChBF,WAAW,EAAEA,WAAW;UACxBI,UAAU,EAAEH;SACb;QACD,IAAI,CAACI,cAAc,CAACF,IAAI,CAAC;MAC3B,CAAC,CAAC;IACJ;IAEAE,cAAcA,CAACC,UAAyB;MACtC,IAAI,CAAC9G,cAAc,CAAC+G,uBAAuB,CAACD,UAAU,CAAC,CAACE,IAAI,CAACtR,IAAI,CAAC,CAAC,CAAC,CAAC,CAACoO,SAAS,CAACE,GAAG,IAAG;QACpF,IAAI,CAAC5D,YAAY,CAACoF,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;MACJ,CAAC,CAAC;IACJ;IAEAsB,gBAAgBA,CAAA;MACd,IAAI,CAACxF,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;MAC1C,IAAI,CAAChC,YAAY,CAACC,MAAM,GAAG,IAAI,CAAClE,UAAU,CAAC,IAAI,CAACA,UAAU,CAACxC,MAAM,GAAG,CAAC,CAAC;MACtE;IACF;IAEA6B,oBAAoBA,CAAC6E,MAAc;MACjC,IAAI,CAACwH,YAAY,CAACxH,MAAM,CAACyE,QAAQ,CAAC;IACpC;IAEApJ,kBAAkBA,CAAC2E,MAAc;MAC/B,IAAI,CAACnE,UAAU,CAACmE,MAAM,CAAC;IACzB;IAEAkF,aAAaA,CAAA;MACX,IAAI,CAACrD,IAAI,CAAC4F,GAAG,CAAC,IAAI,CAAClH,gBAAgB,CAACmH,oBAAoB,CAAC,IAAI,CAACjG,YAAY,CAACkG,EAAE,CAAC,CAACvD,SAAS,CAAEwD,QAAQ,IAAG;QACnG,IAAI,CAACC,iBAAiB,GAAGD,QAAQ;MACnC,CAAC,CAAC,CAAC;IACL;IAEA;IACA;IAEA;IACA;IAGAE,eAAeA,CAACrD,QAAgB;MAC9B,IAAI,CAACnE,cAAc,CAACkH,YAAY,CAAC/C,QAAQ,CAAC,CAACL,SAAS,CAAGwD,QAAQ,IAAI;QACjE,IAAI,CAAClH,YAAY,CAACoF,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;MACJ,CAAC,CAAC;IACJ;IAEA8B,WAAWA,CAACrB,IAAY;MACtB,IAAIA,IAAI,EAAE;QACR,IAAIsB,CAAC,GAAGtB,IAAI,CAACuB,WAAW,CAAC,GAAG,CAAC;QAC7B,IAAGD,CAAC,KAAK,CAAC,CAAC,EACTA,CAAC,GAAGtB,IAAI,CAACuB,WAAW,CAAC,IAAI,CAAC;QAC5B,IAAI3B,MAAM,GAAGI,IAAI,CAACwB,SAAS,CAACF,CAAC,GAAG,CAAC,CAAC;QAClC,OAAO1B,MAAM;MACf;MACA,OAAOI,IAAI;IACb;IAEAyB,cAAcA,CAACrD,MAAc;MAC3BsD,QAAQ,CAACC,cAAc,CAAC,qBAAqB,GAAGvD,MAAM,CAAE,CAACwD,KAAK,CAACC,OAAO,GAAG,MAAM;MAC/EH,QAAQ,CAACC,cAAc,CAAC,sBAAsB,GAAGvD,MAAM,CAAE,CAACwD,KAAK,CAACC,OAAO,GAAG,OAAO;MACjFH,QAAQ,CAACC,cAAc,CAAC,sBAAsB,GAAGvD,MAAM,CAAE,CAACwD,KAAK,CAACE,GAAG,GAAG,KAAK;IAC7E;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IAEA;IACA;IACA;IACA;IAEAC,cAAcA,CAAC3D,MAAW,EAAEL,QAAa;MACvC,IAAI,CAACrD,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACC,UAAU,GAAGyD,MAAM;MACxB,IAAI,CAACxD,YAAY,GAAGmD,QAAQ;IAC9B;IAEAiE,QAAQA,CAAA;MACN,IAAI,CAACC,WAAW,CAAC,IAAI,CAACtH,UAAU,EAAE,IAAI,CAACC,YAAY,CAAC;MACpD;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACyB,QAAQ,EAAE;IACjB;IAEA;IAEA2C,SAASA,CAAA;MACP,IAAI,CAACnD,aAAa,GAAG,EAAE;MACvB,IAAIqG,CAAC,GAAG,CAAC;MACT,KAAK,IAAIC,eAAe,IAAI,IAAI,CAAC7G,gBAAgB,EAAE;QACjD,KAAK,IAAI+C,WAAW,IAAI8D,eAAe,CAAChE,YAAY,EAAE;UACpDE,WAAW,CAAC+D,OAAO,GAAGF,CAAC;UACvBA,CAAC,EAAE;UACH,IAAI,CAACrG,aAAa,CAACkE,IAAI,CAAC1B,WAAW,CAAC;QACtC;MACF;MACA,IAAI,CAAC5E,KAAK,GAAG,IAAI,CAACoC,aAAa;IACjC;IAEA6C,mBAAmBA,CAAA;MACjB,IAAI,CAAC2D,0BAA0B,CAAC,IAAI,CAAC9G,qBAAqB,CAAC;IAC7D;IAEA8G,0BAA0BA,CAACzE,GAAuB;MAChD,IAAI,CAACrC,qBAAqB,GAAGqC,GAAG;MAChC,IAAI,CAACrG,OAAO,GAAG,EAAE;MACjB,KAAK,MAAMyG,GAAG,IAAI,IAAI,CAACzC,qBAAqB,EAAE;QAC5C,MAAMjC,MAAM,GAAW;UACrB5D,IAAI,EAAEsI,GAAG,CAACtI,IAAI;UACd4I,MAAM,EAAEN,GAAG,CAACM,MAAM;UAClBP,QAAQ,EAAEC,GAAG,CAACD;SACf;QACD,IAAI,CAAC,IAAI,CAACxG,OAAO,CAAC+K,IAAI,CAACzD,EAAE,IAAIA,EAAE,CAACnJ,IAAI,KAAK4D,MAAM,CAAC5D,IAAI,IAAImJ,EAAE,CAACP,MAAM,KAAKhF,MAAM,CAACgF,MAAM,CAAC,EAAE;UACpF,IAAI,CAAC/G,OAAO,CAACwI,IAAI,CAACzG,MAAM,CAAC;QAC3B;MACF;MACA,IAAI,CAACgC,gBAAgB,GAAG,IAAI,CAAC1B,cAAc,CAAC2I,yBAAyB,CAAC,IAAI,CAAChH,qBAAqB,CAAC;MACjG,IAAI,CAACyD,SAAS,EAAE;MAChB,IAAI,CAACwD,eAAe,CAAC,MAAM,CAAC;IAC9B;IAEAA,eAAeA,CAAClE,MAAc;MAC5B,IAAI,CAAC/E,YAAY,GAAG,EAAE;MACtB,IAAIkJ,WAAW,GAAG,IAAI,CAAClH,qBAAqB,CAACqD,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACP,MAAM,IAAIA,MAAM,CAAC;MAC9E,KAAK,IAAIoE,WAAW,IAAID,WAAW,EAAE;QACnC,IAAI,CAAC,IAAI,CAAClJ,YAAY,CAAC+I,IAAI,CAACzD,EAAE,IAAIA,EAAE,CAACP,MAAM,IAAIoE,WAAW,CAACpE,MAAM,IAAIO,EAAE,CAACnJ,IAAI,IAAIgN,WAAW,CAAChN,IAAI,CAAC,EAAE;UACjG,IAAI,CAAC6D,YAAY,CAACwG,IAAI,CAAC2C,WAAW,CAAC;QACrC;MACF;IACF;IAEAvN,UAAUA,CAACmE,MAAc;MACvB,IAAI,CAACA,MAAM,EAAE;QACX,IAAI,CAACkJ,eAAe,CAAC,MAAM,CAAC;QAC5B;MACF;MAEA,IAAI,CAACnJ,YAAY,CAACC,MAAM,GAAGA,MAAM;MACjCyD,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC3D,YAAY,CAACC,MAAM,CAAC;MACrC,IAAI,CAACgC,gBAAgB,GAAG,IAAI,CAAC1B,cAAc,CAAC+I,oBAAoB,CAACrJ,MAAM,EAAE,IAAI,CAACiC,qBAAqB,CAAC;MACpG,IAAI,CAACyD,SAAS,EAAE;MAChB,IAAI1F,MAAM,CAAC5D,IAAI,IAAI,MAAM,EAAE;QACzB,IAAI,CAAC8M,eAAe,CAAC,MAAM,CAAC;MAC9B,CAAC,MAAM;QACL,IAAI,CAACA,eAAe,CAAClJ,MAAM,CAAC5D,IAAI,CAAC;MACnC;MACA,IAAI,CAACN,UAAU,GAAG,IAAI,CAACwE,cAAc,CAACgJ,qBAAqB,CAACtJ,MAAM,EAAE,EAAE,EAAE,IAAI,CAAC/B,OAAO,CAAC;MACrF,IAAI,CAACnC,UAAU,CAACyN,OAAO,CAAC,IAAI,CAACjJ,cAAc,CAACW,UAAU,CAAC;MACvD,IAAI,CAAClB,YAAY,CAACC,MAAM,GAAG,IAAI,CAAClE,UAAU,CAAC,IAAI,CAACA,UAAU,CAACxC,MAAM,GAAG,CAAC,CAAC;IACxE;IAEAkO,YAAYA,CAACG,EAAO;MAClB,MAAM6B,cAAc,GAAG,IAAI,CAACvJ,YAAY,CAACqF,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACd,QAAQ,KAAKkD,EAAE,CAAC;MACzE,IAAI,CAACrH,cAAc,CAACmJ,iBAAiB,CAACD,cAAc,CAAC,CAAC,CAAC,CAAC;MACxD,IAAI,CAACjS,uBAAuB,CAACoL,UAAU,GAAG,IAAI;MAC9C,IAAI,CAACpL,uBAAuB,CAACC,OAAO,GAAG,qBAAqB,GAAGgS,cAAc,CAAC,CAAC,CAAC,CAACpN,IAAI,GAAG,IAAI;MAC5F;MACA;MACA;MAEA;IACF;IAEAnF,oBAAoBA,CAACgN,KAAU;MAC7B,IAAI,CAAC3D,cAAc,CAACmJ,iBAAiB,CAAC,EAAY,CAAC;MACnD,IAAI,CAAClS,uBAAuB,CAACoL,UAAU,GAAG,KAAK;IACjD;IAEAxL,oBAAoBA,CAAC8M,KAAU;MAC7B,IAAI,CAAC3D,cAAc,CAACkH,YAAY,CAAC,IAAI,CAAClH,cAAc,CAACoJ,iBAAiB,EAAE,CAACjF,QAAQ,CAAC,CAAC6C,IAAI,CAACtR,IAAI,CAAC,CAAC,CAAC,CAAC,CAACoO,SAAS,CAACE,GAAG,IAAG;QAC/G,IAAI,CAACrE,YAAY,GAAG,IAAI,CAACA,YAAY,CAACqF,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACd,QAAQ,IAAI,IAAI,CAACnE,cAAc,CAACoJ,iBAAiB,EAAE,CAACjF,QAAQ,CAAC;QACnHhB,OAAO,CAACC,GAAG,CAAC,IAAI,CAACzD,YAAY,CAAC;QAE9B,IAAI,CAACS,YAAY,CAACoF,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAAC0D,sBAAsB,CAAC,IAAI,CAACrJ,cAAc,CAACoJ,iBAAiB,EAAE,CAAC;QACpE,IAAI,CAACpJ,cAAc,CAACmJ,iBAAiB,CAAC,EAAY,CAAC;QACnD,IAAI,CAACnJ,cAAc,CAACsJ,iBAAiB,CAAC,IAAI,CAAC;MAC7C,CAAC,CAAC;MACF,IAAI,CAACrS,uBAAuB,CAACoL,UAAU,GAAG,KAAK;IACjD;IAEAkH,YAAYA,CAAC5F,KAAU;MACrB,IAAI6F,OAAO,GAAG7F,KAAK,CAAC6F,OAAO;MAC3B,IAAI7F,KAAK,CAACuC,OAAO,EAAE;QACjB,IAAIvC,KAAK,CAAC8F,UAAU,EAAE;UACpB,KAAK,IAAInF,IAAI,IAAI,IAAI,CAAC7E,YAAY,CAAC8E,YAAY,EAAE;YAC/C,IAAImF,UAAU,GAAaF,OAAO,GAAGlF,IAAI,CAACqF,MAAM,GAAGrF,IAAI,CAACsF,UAAU;YAClEF,UAAU,CAACvD,IAAI,CAACxC,KAAK,CAACkG,GAAG,CAAC;UAC5B;QACF,CAAC,MAAM;UACL,IAAIvF,IAAI,GAAG,IAAI,CAAC7E,YAAY,CAAC8E,YAAY,CAACZ,KAAK,CAACW,IAAI,CAACwF,KAAK,CAAC;UAC3D,IAAIJ,UAAU,GAAaF,OAAO,GAAGlF,IAAI,CAACqF,MAAM,GAAGrF,IAAI,CAACsF,UAAU;UAClEF,UAAU,CAACvD,IAAI,CAACxC,KAAK,CAACkG,GAAG,CAAC;QAC5B;MACF,CAAC,MAAM;QACL,IAAIlG,KAAK,CAAC8F,UAAU,EAAE;UACpB,KAAK,IAAInF,IAAI,IAAI,IAAI,CAAC7E,YAAY,CAAC8E,YAAY,EAAE;YAC/C,IAAImF,UAAU,GAAaF,OAAO,GAAGlF,IAAI,CAACqF,MAAM,GAAGrF,IAAI,CAACsF,UAAU;YAClE,IAAIG,MAAM,GAAGL,UAAU,CAACM,SAAS,CAAG/E,EAAU,IAAKA,EAAE,IAAItB,KAAK,CAACkG,GAAI,CAAC;YACpEH,UAAU,CAACO,MAAM,CAACF,MAAM,EAAE,CAAC,CAAC;UAC9B;QACF,CAAC,MAAM;UACL,IAAIzF,IAAI,GAAG,IAAI,CAAC7E,YAAY,CAAC8E,YAAY,CAACZ,KAAK,CAACW,IAAI,CAACwF,KAAK,CAAC;UAC3D,IAAIJ,UAAU,GAAaF,OAAO,GAAGlF,IAAI,CAACqF,MAAM,GAAGrF,IAAI,CAACsF,UAAU;UAClE,IAAIG,MAAM,GAAGL,UAAU,CAACM,SAAS,CAAG/E,EAAU,IAAKA,EAAE,IAAItB,KAAK,CAACkG,GAAI,CAAC;UACpEH,UAAU,CAACO,MAAM,CAACF,MAAM,EAAE,CAAC,CAAC;QAC9B;MACF;IACF;IACAzN,cAAcA,CAAA;MACZ,IAAI,CAAC0D,cAAc,CAACkK,wBAAwB,EAAE,CAAClD,IAAI,CAACtR,IAAI,CAAC,CAAC,CAAC,CAAC,CAACoO,SAAS,CAACE,GAAG,IAAG;QAC3E,IAAIK,KAAK,GAAG,KAAK;QACjB,KAAK,IAAI8F,CAAC,IAAInG,GAAG,EAAE;UACjB,IAAImG,CAAC,CAACzF,MAAM,IAAI,IAAI,CAAClJ,UAAU,CAAC,IAAI,CAACA,UAAU,CAACxC,MAAM,GAAG,CAAC,CAAC,CAAC8C,IAAI,IAAIqO,CAAC,CAACrO,IAAI,IAAI,IAAI,CAACM,UAAU,EAAE;YAC7FiI,KAAK,GAAG,IAAI;YACZ;UACF;QACF;QACA,IAAI,CAACA,KAAK,EAAE;UACV,IAAI,CAACzE,SAAS,GAAG,KAAK;UACtB,IAAIwK,SAAS,GAAW;YACtB1F,MAAM,EAAE,IAAI,CAAClJ,UAAU,CAAC,IAAI,CAACA,UAAU,CAACxC,MAAM,GAAG,CAAC,CAAC,CAAC8C,IAAI;YACxDA,IAAI,EAAE,IAAI,CAACM;WACZ;UACD,IAAI,CAAC4D,cAAc,CAAC5E,YAAY,CAACgP,SAAS,CAAC,CAACpD,IAAI,CAACtR,IAAI,CAAC,CAAC,CAAC,CAAC,CAACoO,SAAS,CAACE,GAAG,IAAG;YACxE,IAAI,CAACrE,YAAY,CAACwG,IAAI,CAACnC,GAAG,CAAC;YAC3B,IAAI,CAACrC,qBAAqB,CAACwE,IAAI,CAAC;cAC9BzB,MAAM,EAAE,IAAI,CAAClJ,UAAU,CAAC,IAAI,CAACA,UAAU,CAACxC,MAAM,GAAG,CAAC,CAAC,CAAC8C,IAAI;cACxDA,IAAI,EAAE,IAAI,CAACM,UAAU;cACrBmI,YAAY,EAAE,EAAE;cAChB8F,SAAS,EAAE,IAAI,CAACvI,IAAI;cACpBqC,QAAQ,EAAE;aACX,CAAC;YACJ,IAAI,CAAC/D,YAAY,CAACoF,mBAAmB,CAAC;cACpCC,QAAQ,EAAE,SAAS;cACnBC,OAAO,EAAE,EAAE;cACXC,MAAM,EAAE;aACT,CAAC;YACF,IAAI,CAACvJ,UAAU,GAAG,EAAE;YACpB,IAAI,CAAC4D,cAAc,CAACsJ,iBAAiB,CAAC,IAAI,CAAC;UAC3C,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAAClJ,YAAY,CAACoF,mBAAmB,CAAC;YACpCC,QAAQ,EAAE,MAAM;YAChBC,OAAO,EAAE,EAAE;YACXC,MAAM,EAAE;WACT,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;IAEAvK,YAAYA,CAAA;MACV,IAAI,CAACwE,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAClC;IAEAnG,kBAAkBA,CAAA;MAChB,MAAM6Q,iBAAiB,GAAG,IAAI,CAACtK,cAAc,CAACuK,oBAAoB,EAAE,GAAG,wBAAwB,GAAG,mBAAmB;MACrH,MAAMC,SAAS,GAAG1C,QAAQ,CAACC,cAAc,CAACuC,iBAAiB,CAAC;MAC5D;MACA;MACA,IAAI,CAAC/J,WAAW,CAACkK,gBAAgB,CAAC,EAAE,CAAC;MACrCtH,OAAO,CAACC,GAAG,CAACoH,SAAU,CAACE,qBAAqB,EAAE,CAACC,KAAK,CAAC;MACrD,MAAMC,WAAW,GAAGJ,SAAU,CAACE,qBAAqB,EAAE,CAACC,KAAK,GAAG,EAAE;MAEjE,IAAI,CAACzK,cAAc,CAAC0F,uBAAuB,CAAC,IAAI,CAACtF,aAAa,EAAExK,iCAAiC,EAC/F8U,WAAW,CAACC,OAAO,EAAE,EACrB;QACEhF,MAAM,EAAE,QAAQ;QAChBC,cAAc,EAAE,EAAE;QAClBC,WAAW,EAAE,sBAAsB;QACnCrG,MAAM,EAAE,IAAI,CAACD,YAAY,CAACC,MAAM;QAChCD,YAAY,EAAE,IAAI,CAACA;OACpB,EAAE6K,iBAAiB,EAAGtE,MAAW,IAAI;QACtC,IAAIA,MAAM,IAAIA,MAAM,CAACH,MAAM,KAAK,QAAQ,EAAE;UACxC1C,OAAO,CAACC,GAAG,CAAC4C,MAAM,CAAC;UACnB,IAAI,CAACvG,YAAY,CAAC8E,YAAY,GAAGyB,MAAM,CAACzB,YAAY;UACpD,IAAI,CAACuG,QAAQ,EAAE;UACf;QACF;MACF,CAAC,EAAEN,SAAU,CAACE,qBAAqB,EAAE,CAACK,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC;IAC3D;IAEAD,QAAQA,CAAA;MACN,IAAI,IAAI,CAACrL,YAAY,CAAC8E,YAAY,CAACvL,MAAM,GAAG,CAAC,EAAE;QAC7C,IAAI,CAACyG,YAAY,CAACrH,SAAS,GAAG,IAAI,CAACkJ,mBAAmB;QACtD,IAAI0J,MAAM,GAA+B;UACvC7G,QAAQ,EAAE,IAAI,CAAC1E,YAAY,CAACC,MAAM,CAACyE,QAAS;UAC5CqC,WAAW,EAAE,IAAI,CAAClF,mBAAmB;UACrCxF,IAAI,EAAE,IAAI,CAAC2D,YAAY,CAACC,MAAM,CAAC5D,IAAI;UACnC4I,MAAM,EAAE,IAAI,CAACjF,YAAY,CAACC,MAAM,CAACgF,MAAM;UACvCuG,QAAQ,EAAE,IAAI,CAACxL,YAAY,CAAC8E,YAAY;UACxC2G,UAAU,EAAE,IAAI,CAAC9I;SAClB;QAED;QACA,IAAI,IAAI,CAACd,mBAAmB,GAAG,CAAC,EAAE;UAChC,IAAI,CAACtB,cAAc,CAACmL,4BAA4B,CAACH,MAAM,CAAC,CAAChE,IAAI,CAACtR,IAAI,CAAC,CAAC,CAAC,CAAC,CAACoO,SAAS,CAACE,GAAG,IAAG;YACrF,IAAI,CAAC5D,YAAY,CAACoF,mBAAmB,CAAC;cACpCC,QAAQ,EAAE,SAAS;cACnBC,OAAO,EAAE,EAAE;cACXC,MAAM,EAAE;aACT,CAAC;YAEF,IAAI,CAACpF,WAAW,CAAC6K,cAAc,CAAC,IAAI,CAAC;YACrC,IAAI,CAACC,oBAAoB,CAACrH,GAAG,CAAC;YAC9B,IAAI,CAACsH,CAAC,EAAE;UACV,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI,CAACtL,cAAc,CAACuL,mBAAmB,CAACP,MAAM,CAAC,CAAChE,IAAI,CAACtR,IAAI,CAAC,CAAC,CAAC,CAAC,CAACoO,SAAS,CAAEE,GAAQ,IAAI;YACnF,IAAI,CAAC5D,YAAY,CAACoF,mBAAmB,CAAC;cACpCC,QAAQ,EAAE,SAAS;cACnBC,OAAO,EAAE,EAAE;cACXC,MAAM,EAAE,mDAAmD,GAAGqF,MAAM,CAAClP;aACtE,CAAC;YAEF,IAAI,CAACyG,mBAAmB,GAAG,IAAI,CAAC5C,YAAY,CAACqF,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACd,QAAQ,KAAK6G,MAAM,CAAC7G,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC7F,IAAI,CAAC5D,WAAW,CAAC6K,cAAc,CAAC,IAAI,CAAC;YACrC,IAAI,CAACC,oBAAoB,CAACrH,GAAG,CAAC;YAC9B,IAAI,CAACsH,CAAC,EAAE;YACRnI,OAAO,CAACC,GAAG,CAAC,IAAI,CAACb,mBAAmB,CAAC;YACrC;YACA;UAEF,CAAC,CAAC;QACJ;MAEF,CAAC,MAAM;QACL,IAAI,CAACnC,YAAY,CAACoF,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;MACJ;IACF;IAEA2F,CAACA,CAAA;MAEC,IAAI,CAACrJ,aAAa,CAACuJ,MAAM,CAAC,IAAI,CAAC/L,YAAY,CAAC8E,YAAY,CAAC;MACzD,IAAI,CAAC7C,gBAAgB,CAACyE,IAAI,CAAC;QACzBhC,QAAQ,EAAE,IAAI,CAAC1E,YAAY,CAACC,MAAM,CAACyE,QAAS;QAC5CrI,IAAI,EAAE,IAAI,CAAC2D,YAAY,CAACC,MAAM,CAAC5D,IAAI;QACnC4I,MAAM,EAAE,IAAI,CAACjF,YAAY,CAACC,MAAM,CAACgF,MAAM;QACvCH,YAAY,EAAE,IAAI,CAAC9E,YAAY,CAAC8E,YAAY;QAC5C8F,SAAS,EAAE,IAAI,CAACvI;OACjB,CAAC;MACF;MACA;MACA;MAEAqB,OAAO,CAACC,GAAG,CAAC,IAAI,CAACnB,aAAa,CAAC;MAC/B,IAAI,CAACN,qBAAqB,CAACwE,IAAI,CAAC;QAC9BhC,QAAQ,EAAE,IAAI,CAAC1E,YAAY,CAACC,MAAM,CAACyE,QAAS;QAC5CrI,IAAI,EAAE,IAAI,CAAC2D,YAAY,CAACC,MAAM,CAAC5D,IAAI;QACnC4I,MAAM,EAAE,IAAI,CAACjF,YAAY,CAACC,MAAM,CAACgF,MAAM;QACvCH,YAAY,EAAE,IAAI,CAAC9E,YAAY,CAAC8E,YAAY;QAC5C8F,SAAS,EAAE,IAAI,CAACvI;OACjB,CAAC;MACF;MACA,IAAI,CAACsD,SAAS,EAAE;MAChB,IAAI,CAAC3F,YAAY,GAAG,IAAI,CAACO,cAAc,CAACkF,4BAA4B,EAAE;MACtE;MAEA;MACA,IAAI,CAAC3E,WAAW,CAACkK,gBAAgB,CAAC,EAAE,CAAC;IACvC;IAEA;IACAY,oBAAoBA,CAACrH,GAAQ;MAC3B,IAAI,CAACvE,YAAY,CAAC8E,YAAY,GAAGP,GAAG,CAACiH,QAAQ,CAACpI,GAAG,CAAC,CAACyB,IAAQ,EAAEwF,KAAa,MAAM;QAC9E,GAAGxF,IAAI;QACPE,MAAM,EAAER,GAAG,CAAC0C,OAAQ,CAACoD,KAAK;OAC3B,CAAC,CAAC;IACL;IAEA2B,cAAcA,CAAC9H,KAAU;MACvBR,OAAO,CAACC,GAAG,CAACO,KAAK,CAAC;MAClB,IAAI,CAAClE,YAAY,CAAC8E,YAAY,EAAE4B,IAAI,CAAC;QACnCC,IAAI,EAAEzC,KAAK,CAAC+H,QAAQ,CAACC,MAAM;QAC3BhC,MAAM,EAAE,EAAE;QACVC,UAAU,EAAE,EAAE;QACd9N,IAAI,EAAE6H,KAAK,CAAC+H,QAAQ,CAACC,MAAM;QAC3BnH,MAAM,EAAEb,KAAK,CAAC+H,QAAQ,CAACC;OAAQ,CAAC;IACpC;IAEA;;;;;;IAMA1O,aAAaA,CAAC0G,KAAU;MACtB,IAAI,CAACnM,qBAAqB,CAAC6K,UAAU,GAAG,IAAI;MAC5C,IAAI,CAAC7K,qBAAqB,CAACN,OAAO,GAAG,eAAe,GAAGyM,KAAK,CAAC7H,IAAI,GAAG,IAAI;MACxE;MACA,IAAI,CAACkE,cAAc,CAAC4L,cAAc,CAACjI,KAAK,CAAC;IAC3C;IAEAkI,UAAUA,CAACrH,MAAY;MACrB,IAAI,CAACjD,IAAI,CAAC4F,GAAG,CAAC,IAAI,CAACnH,cAAc,CAAC8L,MAAM,CAACtH,MAAM,CAAC,CAACV,SAAS,CAACE,GAAG,IAAG;QAC/D,IAAI,CAAC5D,YAAY,CAACoF,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;MACJ,CAAC,CAAC,CAAC;IACL;IAEApL,iBAAiBA,CAAA;MACf,IAAI,IAAI,CAAC+H,mBAAmB,CAACtJ,MAAM,KAAK,CAAC,EAAE;QACzC,IAAI,CAACoH,YAAY,CAACoF,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACF;MACF;MACA,IAAI,CAAC3F,cAAc,CAAC4F,uBAAuB,CAAC,IAAI,CAACtF,aAAa,EAAExK,iCAAiC,EAAE,GAAG,EACpG;QACE+P,MAAM,EAAE,cAAc;QACtBC,cAAc,EAAE,yBAAyB;QACzCC,WAAW,EAAE,kBAAkB;QAC/BpI,OAAO,EAAE,IAAI,CAACA;OACf,EAAE,IAAI,EAAGqI,MAAW,IAAI;QACzB,IAAIA,MAAM,IAAIA,MAAM,CAACH,MAAM,KAAK,cAAc,EAAE;UAC9C1C,OAAO,CAACC,GAAG,CAAC4C,MAAM,CAAC;UACnB7C,OAAO,CAACC,GAAG,CAAC,IAAI,CAACd,mBAAmB,CAAC;UACrC,IAAI,CAACA,mBAAmB,CAACgE,OAAO,CAACyF,OAAO,IAAG;YACzC,IAAI,CAAC1D,WAAW,CAAC0D,OAAO,CAACvH,MAAO,EAAEwB,MAAM,CAACtG,MAAM,CAACyE,QAAQ,CAAC;YACzD,IAAI,CAACnE,cAAc,CAACsJ,iBAAiB,CAAC,IAAI,CAAC;UAC7C,CAAC,CAAC;UACF;QACF;MACF,CAAC,CAAC;IACJ;IAEA7O,mBAAmBA,CAAA;MACjB,IAAI,IAAI,CAAC6H,mBAAmB,CAACtJ,MAAM,KAAK,CAAC,EAAE;QACzC,IAAI,CAACoH,YAAY,CAACoF,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACF;MACF;MACA,IAAI,CAAC9N,8BAA8B,CAACwK,UAAU,GAAG,IAAI;MACrD,IAAI,CAACxK,8BAA8B,CAACX,OAAO,GAAG,6BAA6B;IAC7E;IAEAU,0BAA0BA,CAAC+L,KAAU;MACnC,MAAMsH,QAAQ,GAAG,IAAI,CAAC3I,mBAAmB;MACzC2I,QAAQ,CAAC3E,OAAO,CAACyF,OAAO,IAAG;QACzB,IAAI,CAACC,2BAA2B,CAACD,OAAO,CAACvH,MAAM,CAAC;QAChD,IAAI,CAACyH,aAAa,CAACF,OAAO,CAAC;MAC7B,CAAC,CAAC;MACF,IAAI,CAACvU,qBAAqB,CAAC6K,UAAU,GAAG,KAAK;MAC7C,IAAI,CAACxK,8BAA8B,CAACwK,UAAU,GAAG,KAAK;MACtD,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC/B;IAEA0J,2BAA2BA,CAAC,GAAGtF,OAAc;MAC3C,IAAIA,OAAO,CAAC1N,MAAM,KAAK,CAAC,EAAE;QACxB,IAAI,CAACiJ,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC+C,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACT,MAAM,KAAKkC,OAAO,CAAC,CAAC,CAAC,CAAC;MAChF,CAAC,MAAM;QACL,IAAI,CAACzE,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC+C,MAAM,CAACC,EAAE,IAAI,CAACyB,OAAO,CAACwF,QAAQ,CAACjH,EAAE,CAACT,MAAM,CAAC,CAAC;MACpF;IACF;IAEAnN,kBAAkBA,CAACsM,KAAU;MAC3B,IAAI,CAACnM,qBAAqB,CAAC6K,UAAU,GAAG,KAAK;MAC7C,IAAI,CAACxK,8BAA8B,CAACwK,UAAU,GAAG,KAAK;MACtD,IAAI,CAACrC,cAAc,CAAC4L,cAAc,CAAC,EAAiB,CAAC;IACvD;IAEArU,kBAAkBA,CAACoM,KAAU;MAC3B,MAAMoI,OAAO,GAAG,IAAI,CAAC/L,cAAc,CAACmM,cAAc,EAAE;MACpD,IAAI,CAACH,2BAA2B,CAACD,OAAO,CAACvH,MAAM,CAAC;MAChD,IAAI,CAACyH,aAAa,CAACF,OAAO,CAAC;IAC7B;IAEQE,aAAaA,CAACF,OAAoB;MACxC,IAAI,CAACxK,IAAI,CAAC4F,GAAG,CAAC,IAAI,CAACnH,cAAc,CAAC8L,MAAM,CAACC,OAAO,CAACvH,MAAM,CAAC,CAACV,SAAS,CAAEE,GAAY,IAAI;QAElF,IAAIA,GAAG,EAAE;UACP,IAAI,CAACxM,qBAAqB,CAAC6K,UAAU,GAAG,KAAK;UAC7C,IAAI,CAACjC,YAAY,CAACoF,mBAAmB,CAAC;YACpCC,QAAQ,EAAE,SAAS;YACnBC,OAAO,EAAE,EAAE;YACXC,MAAM,EAAE;WACT,CAAC;UACF,IAAI,CAACyG,yBAAyB,CAACL,OAAO,CAAC;QACzC;MAEF,CAAC,CAAC,CAAC;IACL;IAEQK,yBAAyBA,CAACL,OAAoB;MACpD,IAAI,CAACpK,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,CAACkB,GAAG,CAACyB,IAAI,IAAG;QACjE,MAAM+H,oBAAoB,GAAG/H,IAAI,CAACC,YAAY,CAACS,MAAM,CAACP,WAAW,IAAIA,WAAW,CAACD,MAAM,KAAKuH,OAAO,CAACvH,MAAM,CAAC;QAC3G,OAAO;UAAE,GAAGF,IAAI;UAAEC,YAAY,EAAE8H;QAAoB,CAAE;MACxD,CAAC,CAAC;IACJ;IAEQhD,sBAAsBA,CAAC3J,MAAc;MAC3C,IAAI,CAACiC,qBAAqB,GAAG,IAAI,CAACA,qBAAqB,CAACqD,MAAM,CAACV,IAAI,IAAG;QACpE,OAAOA,IAAI,CAACH,QAAQ,KAAKzE,MAAM,CAACyE,QAAQ;MAC1C,CAAC,CAAC;IACJ;IAEQkE,WAAWA,CAAC7D,MAAc,EAAExD,YAAoB;MACtD,IAAI,CAACO,IAAI,CAAC4F,GAAG,CAAC,IAAI,CAACnH,cAAc,CAACsM,gBAAgB,CAAC9H,MAAM,EAAExD,YAAY,CAAC,CAAC8C,SAAS,CAAGwD,QAAQ,IAAI;QAC/F,IAAI,CAAClH,YAAY,CAACoF,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAACrD,mBAAmB,GAAG,EAAE;QAC7B,IAAI,CAAChJ,gBAAgB,GAAG,KAAK;QAC7B;QAEA,IAAI,CAAC0G,cAAc,CAACsJ,iBAAiB,CAAC,IAAI,CAAC;MAC7C,CAAC,CAAC,CAAC;IACL;IAEApG,8BAA8BA,CAACS,KAAU;MACvC,IAAI,CAAC4I,mBAAmB,GAAG5I,KAAK;IAClC;IAEAvK,eAAeA,CAAA;MACb,IAAI,CAACE,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;MAC9C;MACAkT,UAAU,CAAC,MAAK;QACd;MAAA,CACD,EAAE,GAAG,CAAC;IACT;IAEA5T,gCAAgCA,CAAC+K,KAAU;MACzC,IAAIA,KAAK,CAACmG,KAAK,GAAG,CAAC,EAAE;QACnB,IAAI,CAAC1H,mBAAmB,GAAG,EAAE;QAC7B,MAAMqK,cAAc,GAAG,IAAI,CAAC1T,iBAAiB,CAAC4K,KAAK,CAACmG,KAAK,GAAG,CAAC,CAAC;QAC9D,IAAI,CAAC7G,sBAAsB,CAAC,IAAI,CAAClK,iBAAiB,CAAC4K,KAAK,CAACmG,KAAK,GAAG,CAAC,CAAC,CAAC/G,SAAS,CAAC;QAE9E,IAAI,CAACd,aAAa,GAAG,IAAI,CAACpC,KAAK,CAACmF,MAAM,CAACtF,MAAM,IAC3CA,MAAM,CAACgN,UAAW,CAACrF,EAAE,KAAKoF,cAAc,CAAC1J,SAAS,CACnD;MACH,CAAC,MAAM;QACL,IAAI,CAACd,aAAa,GAAG,IAAI,CAACpC,KAAK;QAC/B,IAAI,CAACoD,sBAAsB,EAAE;MAC/B;IACF;IAEAA,sBAAsBA,CAAC0J,SAAkB;MACvC,IAAI,CAACvK,mBAAmB,GAAGuK,SAAS,GAAE,CAACA,SAAS,CAAC,GAAG,IAAI,CAAC5T,iBAAiB,CAAC8J,GAAG,CAAEf,IAAU,IAAKA,IAAI,CAACiB,SAAS,CAAC;IAChH;IAEA9E,gBAAgBA,CAAC2O,MAAM,GAAG,KAAK;MAC7B,IAAI,CAACA,MAAM,EAAE;QACX,OAAO,IAAI,CAAC3K,aAAa;MAC3B,CAAC,MAAM;QACL,MAAM4K,mBAAmB,GAAG,IAAI,CAAC5K,aAAa,GAAG,IAAI,CAACA,aAAa,CAAC+C,MAAM,CAAC,CAACV,IAAI,EAAEwF,KAAK,EAAEgD,IAAI,KAC3FhD,KAAK,KAAKgD,IAAI,CAAC9C,SAAS,CAAE+C,CAAC,IACzBA,CAAC,CAACvI,MAAM,KAAKF,IAAI,CAACE,MACnB,CAAC,CAAC;QACL,OAAOqI,mBAAmB;MAC5B;IACF;IAEAxP,cAAcA,CAACsG,KAAU;MACvBR,OAAO,CAACC,GAAG,CAACO,KAAK,CAAC;MAClB,IAAI,CAAC3D,cAAc,CAACsJ,iBAAiB,CAAC,IAAI,CAAC;IAC7C;IAEA3E,sBAAsBA,CAAA;MACpB,IAAI,CAACpD,IAAI,CAACqC,IAAI,GAAG,IAAI,CAAC5D,cAAc,CAACgN,sBAAsB,CAAClJ,SAAS,CAAEE,GAAY,IAAI;QACrF,IAAKA,GAAG,IAAI,IAAI,CAACzB,mBAAmB,EAAG;UACrC,IAAI,CAAC,IAAI,CAACrC,cAAc,CAAC+M,eAAe,CAAC,IAAI,CAAC1K,mBAAmB,CAAC,EAAE;YAElE,IAAI,CAAChH,UAAU,CAAC,IAAI,CAACgH,mBAAmB,CAAC;YACzCY,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC3D,YAAY,CAACC,MAAM,CAAC;UACvC;QAEF;MACF,CAAC,CAAC;IACJ;IAAC,QAAAwN,CAAA,G;uBA71BUpN,sBAAsB,EAAA9J,EAAA,CAAAmX,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAArX,EAAA,CAAAmX,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAvX,EAAA,CAAAmX,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAzX,EAAA,CAAAmX,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA3X,EAAA,CAAAmX,iBAAA,CAAAS,EAAA,CAAAC,mBAAA,GAAA7X,EAAA,CAAAmX,iBAAA,CAAAW,EAAA,CAAAC,YAAA,GAAA/X,EAAA,CAAAmX,iBAAA,CAAAa,EAAA,CAAAC,aAAA,GAAAjY,EAAA,CAAAmX,iBAAA,CAAAe,EAAA,CAAArY,aAAA,GAAAG,EAAA,CAAAmX,iBAAA,CAAAgB,EAAA,CAAAC,WAAA;IAAA;IAAA,QAAAC,EAAA,G;YAAtBvO,sBAAsB;MAAAwO,SAAA;MAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAFtB,CAAC5Y,aAAa,EAAEE,UAAU,CAAC,GAAAC,EAAA,CAAA2Y,oBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,gCAAAP,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCfxCzY,EAXA,CAAA+B,UAAA,IAAAkX,8CAAA,0BAAyD,IAAAC,8CAAA,0BAMF,IAAAC,8CAAA,0BAKS;UAY5DnZ,EAJJ,CAAAE,cAAA,gBAAgC,aAG0D,0BAM9C;UAAxCF,EAAA,CAAAG,UAAA,8BAAAiZ,6EAAA;YAAApZ,EAAA,CAAAM,aAAA,CAAA+Y,GAAA;YAAA,OAAArZ,EAAA,CAAAU,WAAA,CAAoBgY,GAAA,CAAAzH,gBAAA,EAAkB;UAAA,EAAC;UACvCjR,EAAA,CAAAkE,SAAA,aAQM;UAEVlE,EADI,CAAAc,YAAA,EAAmB,EACjB;UAEFd,EAAA,CAAAE,cAAA,UAAmM;UA0B/LF,EAxBA,CAAA+B,UAAA,IAAAuX,8CAAA,0BAAgG,KAAAC,+CAAA,0BAKkB,KAAAC,8CAAA,kCAAAxZ,EAAA,CAAAyZ,sBAAA,CAmBG;UA0M7HzZ,EAFI,CAAAc,YAAA,EAAM,EAEJ;UAENd,EAAA,CAAA+B,UAAA,KAAA2X,8CAAA,gCAAA1Z,EAAA,CAAAyZ,sBAAA,CAAuF;;;UA/QxEzZ,EAAA,CAAAgB,UAAA,SAAA0X,GAAA,CAAAzX,uBAAA,CAAAoL,UAAA,CAAwC;UAMxCrM,EAAA,CAAAe,SAAA,EAAsC;UAAtCf,EAAA,CAAAgB,UAAA,SAAA0X,GAAA,CAAAlX,qBAAA,CAAA6K,UAAA,CAAsC;UAKtCrM,EAAA,CAAAe,SAAA,EAA+C;UAA/Cf,EAAA,CAAAgB,UAAA,SAAA0X,GAAA,CAAA7W,8BAAA,CAAAwK,UAAA,CAA+C;UAW1BrM,EAAA,CAAAe,SAAA,GAAqD;UAArDf,EAAA,CAAAgB,UAAA,YAAAhB,EAAA,CAAAkC,eAAA,KAAAyX,GAAA,GAAAjB,GAAA,CAAAjN,cAAA,EAAqD;UACnEzL,EAAA,CAAAe,SAAA,EAA+D;UAIDf,EAJ9D,CAAAgB,UAAA,uCAAA0X,GAAA,CAAAjP,YAAA,CAAAC,MAAA,CAAA5D,IAAA,CAA+D,8CAGuB,gFACzB,6BAA6B;UAcvG9F,EAAA,CAAAe,SAAA,GAAuF;UAAvFf,EAAA,CAAAmJ,sBAAA,4BAAAuP,GAAA,CAAAtP,kBAAA,uCAAuF;UAAEpJ,EAAA,CAAA8H,WAAA,YAAA4Q,GAAA,CAAAjR,aAAA,GAAAiR,GAAA,CAAAkB,cAAA,IAAAlB,GAAA,CAAAhS,WAAA,CAAAC,SAAA,6BAAoG;UAE/K3G,EAAA,CAAAe,SAAA,EAA+E;UAA/Ef,EAAA,CAAAgB,UAAA,UAAA0X,GAAA,CAAApR,aAAA,IAAAoR,GAAA,CAAAhS,WAAA,CAAAC,SAAA,IAAA+R,GAAA,CAAA3V,iBAAA,CAAAC,MAAA,OAA+E;UAK/EhD,EAAA,CAAAe,SAAA,EAAiG;UAAjGf,EAAA,CAAAgB,UAAA,SAAA0X,GAAA,CAAA3V,iBAAA,IAAA2V,GAAA,CAAApR,aAAA,IAAAoR,GAAA,CAAAhS,WAAA,CAAAE,SAAA,IAAA8R,GAAA,CAAA3V,iBAAA,CAAAC,MAAA,KAAiG;;;;;;;SDpB3G8G,sBAAsB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}