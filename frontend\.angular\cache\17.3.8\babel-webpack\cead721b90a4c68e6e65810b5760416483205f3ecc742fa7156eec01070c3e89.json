{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { GuidesRoutingModule } from './guides-routing.module';\nimport { GuidesComponent } from './components/guides/guides.component';\nimport { SharedModule } from 'src/app/shared/shared.module';\nlet GuidesModule = class GuidesModule {};\nGuidesModule = __decorate([NgModule({\n  declarations: [GuidesComponent],\n  imports: [CommonModule, GuidesRoutingModule, SharedModule]\n})], GuidesModule);\nexport { GuidesModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "GuidesRoutingModule", "GuidesComponent", "SharedModule", "GuidesModule", "__decorate", "declarations", "imports"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\guides\\guides.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { GuidesRoutingModule } from './guides-routing.module';\r\nimport { GuidesComponent } from './components/guides/guides.component';\r\nimport { SharedModule } from 'src/app/shared/shared.module';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    GuidesComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    GuidesRoutingModule,\r\n    SharedModule,\r\n  ]\r\n})\r\nexport class GuidesModule { }\r\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,YAAY,QAAQ,8BAA8B;AAapD,IAAMC,YAAY,GAAlB,MAAMA,YAAY,GAAI;AAAhBA,YAAY,GAAAC,UAAA,EAVxBN,QAAQ,CAAC;EACRO,YAAY,EAAE,CACZJ,eAAe,CAChB;EACDK,OAAO,EAAE,CACPP,YAAY,EACZC,mBAAmB,EACnBE,YAAY;CAEf,CAAC,C,EACWC,YAAY,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}