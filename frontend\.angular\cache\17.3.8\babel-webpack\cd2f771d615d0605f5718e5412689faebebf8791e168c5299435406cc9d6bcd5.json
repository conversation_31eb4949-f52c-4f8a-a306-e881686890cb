{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SharedCertificateComponent } from './shared-certificate.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: ':permalink',\n  component: SharedCertificateComponent\n}];\nexport class SharedLinkRoutingModule {\n  static #_ = this.ɵfac = function SharedLinkRoutingModule_Factory(t) {\n    return new (t || SharedLinkRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: SharedLinkRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SharedLinkRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "SharedCertificateComponent", "routes", "path", "component", "SharedLinkRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\shared-link\\shared-link-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { SharedCertificateComponent } from './shared-certificate.component';\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: ':permalink',\r\n    component: SharedCertificateComponent,\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class SharedLinkRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,0BAA0B,QAAQ,gCAAgC;;;AAE3E,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,YAAY;EAClBC,SAAS,EAAEH;CACZ,CACF;AAMD,OAAM,MAAOI,uBAAuB;EAAA,QAAAC,CAAA,G;qBAAvBD,uBAAuB;EAAA;EAAA,QAAAE,EAAA,G;UAAvBF;EAAuB;EAAA,QAAAG,EAAA,G;cAHxBR,YAAY,CAACS,QAAQ,CAACP,MAAM,CAAC,EAC7BF,YAAY;EAAA;;;2EAEXK,uBAAuB;IAAAK,OAAA,GAAAC,EAAA,CAAAX,YAAA;IAAAY,OAAA,GAFxBZ,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}