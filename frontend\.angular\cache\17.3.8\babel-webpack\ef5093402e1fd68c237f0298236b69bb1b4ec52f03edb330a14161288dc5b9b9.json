{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { AuthRoutingModule } from './auth-routing.module';\nimport { FormsModule } from '@angular/forms';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { ConfirmComponent } from './confirm/confirm.component';\nimport { ForgotComponent } from './forgot/forgot.component';\nimport { ButtonModule } from 'primeng/button';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { LoaderComponent } from 'src/app/shared/loader/loader.component';\nimport { RecaptchaModule } from 'ng-recaptcha';\nimport * as i0 from \"@angular/core\";\nexport class AuthModule {\n  static #_ = this.ɵfac = function AuthModule_Factory(t) {\n    return new (t || AuthModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: AuthModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, FormsModule, AuthRoutingModule, MatProgressSpinnerModule, ButtonModule, CheckboxModule, RecaptchaModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AuthModule, {\n    declarations: [ConfirmComponent, ForgotComponent],\n    imports: [LoaderComponent, CommonModule, FormsModule, AuthRoutingModule, MatProgressSpinnerModule, ButtonModule, CheckboxModule, RecaptchaModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "AuthRoutingModule", "FormsModule", "MatProgressSpinnerModule", "ConfirmComponent", "ForgotComponent", "ButtonModule", "CheckboxModule", "LoaderComponent", "RecaptchaModule", "AuthModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\auth\\auth.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { AuthRoutingModule } from './auth-routing.module';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { ConfirmComponent } from './confirm/confirm.component';\r\n\r\nimport { ForgotComponent } from './forgot/forgot.component';\r\nimport {ButtonModule} from 'primeng/button';\r\nimport {CheckboxModule} from 'primeng/checkbox';\r\nimport { LoaderComponent } from 'src/app/shared/loader/loader.component';\r\nimport { RecaptchaModule } from 'ng-recaptcha';\r\n\r\n@NgModule({\r\n  declarations: [\r\n    ConfirmComponent,\r\n    ForgotComponent,\r\n  ],\r\n  imports: [\r\n    LoaderComponent,\r\n    CommonModule,\r\n    FormsModule,\r\n    AuthRoutingModule,\r\n    MatProgressSpinnerModule,\r\n    ButtonModule,\r\n    CheckboxModule,\r\n    RecaptchaModule,\r\n  ],\r\n  providers: [\r\n  //TODO GOOGLE LOGIN\r\n\r\n    // {\r\n    //   provide: HTTP_INTERCEPTORS,\r\n    //   useClass: ErrorHandlerService,\r\n    //   multi: true\r\n    // },\r\n    // {\r\n    //   provide: 'SocialAuthServiceConfig',\r\n    //   useValue: {\r\n    //     autoLogin: false,\r\n    //     providers: [\r\n    //       {\r\n    //         id: GoogleLoginProvider.PROVIDER_ID,\r\n    //         provider: new GoogleLoginProvider(\r\n    //           '************-m2mlgsroh8vsmt0tg82lmo4cnt0cuqs3.apps.googleusercontent.com'\r\n    //         )\r\n    //       },\r\n    //     ],\r\n    //   } as SocialAuthServiceConfig\r\n    // }\r\n  ]\r\n})\r\nexport class AuthModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,gBAAgB,QAAQ,6BAA6B;AAE9D,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAAQC,YAAY,QAAO,gBAAgB;AAC3C,SAAQC,cAAc,QAAO,kBAAkB;AAC/C,SAASC,eAAe,QAAQ,wCAAwC;AACxE,SAASC,eAAe,QAAQ,cAAc;;AAyC9C,OAAM,MAAOC,UAAU;EAAA,QAAAC,CAAA,G;qBAAVD,UAAU;EAAA;EAAA,QAAAE,EAAA,G;UAAVF;EAAU;EAAA,QAAAG,EAAA,G;cAhCnBb,YAAY,EACZE,WAAW,EACXD,iBAAiB,EACjBE,wBAAwB,EACxBG,YAAY,EACZC,cAAc,EACdE,eAAe;EAAA;;;2EA0BNC,UAAU;IAAAI,YAAA,GArCnBV,gBAAgB,EAChBC,eAAe;IAAAU,OAAA,GAGfP,eAAe,EACfR,YAAY,EACZE,WAAW,EACXD,iBAAiB,EACjBE,wBAAwB,EACxBG,YAAY,EACZC,cAAc,EACdE,eAAe;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}