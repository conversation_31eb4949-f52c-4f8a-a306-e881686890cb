{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Thai [th]\n//! author : <PERSON><PERSON><PERSON><PERSON> : https://github.com/sirn\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var th = moment.defineLocale('th', {\n    months: 'มกราคม_กุมภาพันธ์_มีนาคม_เมษายน_พฤษภาคม_มิถุนายน_กรกฎาคม_สิงหาคม_กันยายน_ตุลาคม_พฤศจิกายน_ธันวาคม'.split('_'),\n    monthsShort: 'ม.ค._ก.พ._มี.ค._เม.ย._พ.ค._มิ.ย._ก.ค._ส.ค._ก.ย._ต.ค._พ.ย._ธ.ค.'.split('_'),\n    monthsParseExact: true,\n    weekdays: 'อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัสบดี_ศุกร์_เสาร์'.split('_'),\n    weekdaysShort: 'อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัส_ศุกร์_เสาร์'.split('_'),\n    // yes, three characters difference\n    weekdaysMin: 'อา._จ._อ._พ._พฤ._ศ._ส.'.split('_'),\n    weekdaysParseExact: true,\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY เวลา H:mm',\n      LLLL: 'วันddddที่ D MMMM YYYY เวลา H:mm'\n    },\n    meridiemParse: /ก่อนเที่ยง|หลังเที่ยง/,\n    isPM: function (input) {\n      return input === 'หลังเที่ยง';\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 12) {\n        return 'ก่อนเที่ยง';\n      } else {\n        return 'หลังเที่ยง';\n      }\n    },\n    calendar: {\n      sameDay: '[วันนี้ เวลา] LT',\n      nextDay: '[พรุ่งนี้ เวลา] LT',\n      nextWeek: 'dddd[หน้า เวลา] LT',\n      lastDay: '[เมื่อวานนี้ เวลา] LT',\n      lastWeek: '[วัน]dddd[ที่แล้ว เวลา] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'อีก %s',\n      past: '%sที่แล้ว',\n      s: 'ไม่กี่วินาที',\n      ss: '%d วินาที',\n      m: '1 นาที',\n      mm: '%d นาที',\n      h: '1 ชั่วโมง',\n      hh: '%d ชั่วโมง',\n      d: '1 วัน',\n      dd: '%d วัน',\n      w: '1 สัปดาห์',\n      ww: '%d สัปดาห์',\n      M: '1 เดือน',\n      MM: '%d เดือน',\n      y: '1 ปี',\n      yy: '%d ปี'\n    }\n  });\n  return th;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}