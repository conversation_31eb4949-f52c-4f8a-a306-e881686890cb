{"ast": null, "code": "import { inject } from '@angular/core';\nimport { SubSink } from 'subsink';\nimport { timer } from 'rxjs';\nimport { map, share } from 'rxjs/operators';\nimport { UserService } from 'src/app/core/services/user.service';\nimport { ToastService } from 'src/app/core/services/toast.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nimport * as i2 from \"src/app/core/services/classroom.service\";\nimport * as i3 from \"src/app/core/services/auth.service\";\nimport * as i4 from \"src/app/core/services/rating-and-report.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/tooltip\";\nconst _c0 = [\"enrollNowTemplate\"];\nconst _c1 = [\"counter\"];\nconst _c2 = [\"lottieEl\"];\nconst _c3 = a0 => ({\n  \"disabled-link\": a0\n});\nfunction IntroGradientComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 12)(2, \"div\", 13)(3, \"div\", 14)(4, \"div\", 15)(5, \"p\", 16);\n    i0.ɵɵtext(6, \"Welcome back \");\n    i0.ɵɵelementStart(7, \"span\", 17);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"p\", 18)(10, \"span\", 19);\n    i0.ɵɵelement(11, \"span\", null, 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.user.firstName, \"!\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r0.localTime, \" \", ctx_r0.convertTimezoneValueToText().text, \"\");\n  }\n}\nfunction IntroGradientComponent_ng_container_4_ng_container_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 30)(2, \"div\", 31)(3, \"span\", 32)(4, \"div\", 33)(5, \"img\", 34);\n    i0.ɵɵlistener(\"error\", function IntroGradientComponent_ng_container_4_ng_container_16_Template_img_error_5_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.generalService.setDefaultUserAvatar($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 35)(7, \"div\", 36);\n    i0.ɵɵtext(8, \"Your teacher\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 37);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"p\", 38);\n    i0.ɵɵtext(12, \" Your new teacher has been assigned and will be in touch with you very soon! \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"src\", ctx_r0.generalService.getDomainFileNamePath(ctx_r0.teacher.avatarUrl), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.generalService.getShortFullName(ctx_r0.teacher));\n  }\n}\nfunction IntroGradientComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 20)(2, \"div\", 13)(3, \"div\", 21)(4, \"div\", 22)(5, \"p\", 23);\n    i0.ɵɵtext(6, \"Refer a friend! \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 24)(8, \"span\", 25)(9, \"span\", 26);\n    i0.ɵɵtext(10, \"Your Referral code: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 27);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"i\", 28);\n    i0.ɵɵlistener(\"click\", function IntroGradientComponent_ng_container_4_Template_i_click_13_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.copyText(ctx_r0.userService.generateReferralCodeFromUser(ctx_r0.user)));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \" Share the link with your friends and win a free lesson for both of you when they purchase a package. \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(16, IntroGradientComponent_ng_container_4_ng_container_16_Template, 13, 2, \"ng-container\", 29);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(12);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.userService.generateReferralCodeFromUser(ctx_r0.user), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.generalService.isNullishObject(ctx_r0.teacher));\n  }\n}\nfunction IntroGradientComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 20)(2, \"div\", 13)(3, \"div\", 39)(4, \"div\", 15)(5, \"p\", 16);\n    i0.ɵɵtext(6, \"Hi \");\n    i0.ɵɵelementStart(7, \"span\", 17);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"p\", 40)(10, \"span\", 17);\n    i0.ɵɵtext(11, \"Welcome to your Course\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"p\", 41)(13, \"span\", 17);\n    i0.ɵɵelement(14, \"span\", null, 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerStart(17);\n    i0.ɵɵelementStart(18, \"div\", 42)(19, \"div\", 43)(20, \"span\", 32);\n    i0.ɵɵelement(21, \"img\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 45)(23, \"div\", 46);\n    i0.ɵɵtext(24, \" We have received your course booking! \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 47);\n    i0.ɵɵtext(26, \" Please wait while we find the most suitable teacher for you. \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.user.firstName, \"!\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.user.timeZone, \"\");\n  }\n}\nfunction IntroGradientComponent_ng_container_6_ng_container_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 30)(2, \"div\", 31)(3, \"span\", 32)(4, \"div\", 33)(5, \"img\", 34);\n    i0.ɵɵlistener(\"error\", function IntroGradientComponent_ng_container_6_ng_container_14_Template_img_error_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.generalService.setDefaultUserAvatar($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 35)(7, \"div\", 36);\n    i0.ɵɵtext(8, \"Your teacher\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 37);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"p\", 38);\n    i0.ɵɵtext(12, \" Your new teacher has been assigned and will be in touch with you very soon! \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"src\", ctx_r0.generalService.getDomainFileNamePath(ctx_r0.teacher.avatarUrl), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r0.generalService.getShortFullName(ctx_r0.teacher));\n  }\n}\nfunction IntroGradientComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 20)(2, \"div\", 13)(3, \"div\", 21)(4, \"div\", 15)(5, \"p\", 48);\n    i0.ɵɵtext(6, \"Welcome back \");\n    i0.ɵɵelementStart(7, \"span\", 17);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"p\", 41)(10, \"span\", 17);\n    i0.ɵɵelement(11, \"span\", null, 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(14, IntroGradientComponent_ng_container_6_ng_container_14_Template, 13, 2, \"ng-container\", 29);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.user.firstName, \"!\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.localTime, \" \", ctx_r0.convertTimezoneValueToText().text, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.generalService.isNullishObject(ctx_r0.teacher));\n  }\n}\nfunction IntroGradientComponent_div_7_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 59);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const enrollNowTemplate_r5 = i0.ɵɵreference(18);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", enrollNowTemplate_r5);\n  }\n}\nfunction IntroGradientComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 49)(2, \"div\", 50)(3, \"div\", 51)(4, \"div\", 52)(5, \"div\", 53)(6, \"div\", 54);\n    i0.ɵɵelement(7, \"img\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 56);\n    i0.ɵɵtext(9, \"We have received your Free Trial request!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 57);\n    i0.ɵɵtext(11, \"Please wait while we find the most suitable teacher for you.\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(12, IntroGradientComponent_div_7_div_12_Template, 1, 1, \"div\", 58);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isEnrollNowVisible);\n  }\n}\nfunction IntroGradientComponent_div_8_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"div\", 70)(2, \"div\", 71)(3, \"img\", 34);\n    i0.ɵɵlistener(\"error\", function IntroGradientComponent_div_8_div_3_Template_img_error_3_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.generalService.setDefaultUserAvatar($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 72);\n    i0.ɵɵtext(5, \"Your teacher \");\n    i0.ɵɵelementStart(6, \"b\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r0.generalService.getDomainFileNamePath(ctx_r0.classRoom.teacher.avatarUrl), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.generalService.getShortFullName(ctx_r0.classRoom.teacher), \" \");\n  }\n}\nfunction IntroGradientComponent_div_8_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 59);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const enrollNowTemplate_r5 = i0.ɵɵreference(18);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", enrollNowTemplate_r5);\n  }\n}\nfunction IntroGradientComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 60)(2, \"div\", 61);\n    i0.ɵɵtemplate(3, IntroGradientComponent_div_8_div_3_Template, 8, 2, \"div\", 62);\n    i0.ɵɵelementStart(4, \"div\", 63)(5, \"div\", 52)(6, \"div\", 53);\n    i0.ɵɵelement(7, \"div\", 54);\n    i0.ɵɵelementStart(8, \"div\", 64);\n    i0.ɵɵtext(9, \"Your \");\n    i0.ɵɵelementStart(10, \"b\", 65);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" trial lesson has been dismissed! \");\n    i0.ɵɵelement(13, \"br\");\n    i0.ɵɵtext(14, \"Please get in touch to rearrange. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function IntroGradientComponent_div_8_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.generalService.openExternalLink(\"https://www.mylingotrip.com/contact\"));\n    });\n    i0.ɵɵelementStart(16, \"div\", 67)(17, \"span\", 68);\n    i0.ɵɵtext(18, \"Contact Us\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵtemplate(19, IntroGradientComponent_div_8_div_19_Template, 1, 1, \"div\", 58);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.generalService.isNullishObject(ctx_r0.classRoom));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r0.classRoom.language);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isEnrollNowVisible);\n  }\n}\nfunction IntroGradientComponent_div_9_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"div\", 70)(2, \"div\", 71)(3, \"img\", 34);\n    i0.ɵɵlistener(\"error\", function IntroGradientComponent_div_9_div_3_Template_img_error_3_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.generalService.setDefaultUserAvatar($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p\", 72);\n    i0.ɵɵtext(5, \"Your teacher \");\n    i0.ɵɵelementStart(6, \"b\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r0.generalService.getDomainFileNamePath(ctx_r0.classRoom.teacher.avatarUrl), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.generalService.getShortFullName(ctx_r0.classRoom.teacher), \" \");\n  }\n}\nfunction IntroGradientComponent_div_9_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 75);\n    i0.ɵɵtext(2, \"Your \");\n    i0.ɵɵelementStart(3, \"b\", 65);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" trial has been completed! How did you find it?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 76)(7, \"div\", 67)(8, \"span\", 68);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"img\", 77);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.classRoom.language);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c3, ctx_r0.hasRated))(\"pTooltip\", ctx_r0.hasRated ? \"Already rated\" : \"\")(\"routerLink\", ctx_r0.classroomService.getClassroomRateUrl(ctx_r0.classRoom));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.hasRated ? \"Already rated\" : \"Rate your experience\");\n  }\n}\nfunction IntroGradientComponent_div_9_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵtext(1, \"Your newly assigned teacher will be in touch with you very soon!\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction IntroGradientComponent_div_9_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 59);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const enrollNowTemplate_r5 = i0.ɵɵreference(18);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", enrollNowTemplate_r5);\n  }\n}\nfunction IntroGradientComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 60)(2, \"div\", 73);\n    i0.ɵɵtemplate(3, IntroGradientComponent_div_9_div_3_Template, 8, 2, \"div\", 62);\n    i0.ɵɵelementStart(4, \"div\", 63)(5, \"div\", 52)(6, \"div\", 53);\n    i0.ɵɵelement(7, \"div\", 54);\n    i0.ɵɵtemplate(8, IntroGradientComponent_div_9_ng_container_8_Template, 11, 7, \"ng-container\", 74)(9, IntroGradientComponent_div_9_ng_template_9_Template, 2, 0, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(11, IntroGradientComponent_div_9_div_11_Template, 1, 1, \"div\", 58);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ongoingTrial_r9 = i0.ɵɵreference(10);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.generalService.isNullishObject(ctx_r0.classRoom));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.classroomService.isTrialClassroomWithCompletedFirstLesson(ctx_r0.classRoom))(\"ngIfElse\", ongoingTrial_r9);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isEnrollNowVisible);\n  }\n}\nfunction IntroGradientComponent_div_10_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 59);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const enrollNowTemplate_r5 = i0.ɵɵreference(18);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", enrollNowTemplate_r5);\n  }\n}\nfunction IntroGradientComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 78)(2, \"div\", 79)(3, \"div\", 80)(4, \"div\", 81)(5, \"div\", 71)(6, \"img\", 34);\n    i0.ɵɵlistener(\"error\", function IntroGradientComponent_div_10_Template_img_error_6_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.generalService.setDefaultUserAvatar($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\", 72);\n    i0.ɵɵtext(8, \"Your teacher \");\n    i0.ɵɵelementStart(9, \"b\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 82)(12, \"div\", 52)(13, \"div\", 53);\n    i0.ɵɵelement(14, \"div\", 54);\n    i0.ɵɵelementStart(15, \"div\", 64);\n    i0.ɵɵtext(16, \"Your trial lesson has been arranged for\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 83);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(20, IntroGradientComponent_div_10_div_20_Template, 1, 1, \"div\", 58);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"src\", ctx_r0.generalService.getDomainFileNamePath(ctx_r0.classRoom.teacher.avatarUrl), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.generalService.getShortFullName(ctx_r0.classRoom.teacher), \"\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(19, 4, ctx_r0.classRoom.lessons[0].startingDate, \"dd/MM/YY\"), \" at 13:00 \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isEnrollNowVisible);\n  }\n}\nfunction IntroGradientComponent_div_11_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 59);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const enrollNowTemplate_r5 = i0.ɵɵreference(18);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", enrollNowTemplate_r5);\n  }\n}\nfunction IntroGradientComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 84)(2, \"div\", 13)(3, \"div\", 85)(4, \"div\", 86);\n    i0.ɵɵelement(5, \"img\", 87);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 88)(7, \"div\", 52)(8, \"div\", 53)(9, \"div\", 56);\n    i0.ɵɵtext(10, \"Oops, we couldn\\u2019t reach you \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 89);\n    i0.ɵɵtext(12, \"Please fill in the form below and a member of our team will be in touch with you.\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(13, IntroGradientComponent_div_11_div_13_Template, 1, 1, \"div\", 58);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isEnrollNowVisible);\n  }\n}\nfunction IntroGradientComponent_div_12_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 59);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const contactUsTemplate_r11 = i0.ɵɵreference(22);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", contactUsTemplate_r11);\n  }\n}\nfunction IntroGradientComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 60)(2, \"div\", 90)(3, \"div\", 91)(4, \"div\", 70)(5, \"div\", 92)(6, \"div\", 93)(7, \"div\", 94);\n    i0.ɵɵelement(8, \"img\", 95);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(9, \"div\", 96)(10, \"div\", 52)(11, \"div\", 53);\n    i0.ɵɵelement(12, \"div\", 54);\n    i0.ɵɵelementContainerStart(13);\n    i0.ɵɵelementStart(14, \"div\", 97);\n    i0.ɵɵtext(15, \" Limited-Time Offer \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 98);\n    i0.ɵɵtext(17, \" Get 10% off our 50-hour packages until February 28. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 99);\n    i0.ɵɵtext(19, \" Contact us now to claim your discount! \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(20, IntroGradientComponent_div_12_div_20_Template, 1, 1, \"div\", 58);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(20);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isEnrollNowVisible);\n  }\n}\nfunction IntroGradientComponent_div_13_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 59);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const getCodeTemplate_r12 = i0.ɵɵreference(24);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", getCodeTemplate_r12);\n  }\n}\nfunction IntroGradientComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 60)(2, \"div\", 90)(3, \"div\", 91)(4, \"div\", 70)(5, \"div\", 92)(6, \"div\", 93)(7, \"div\", 94);\n    i0.ɵɵelement(8, \"img\", 95);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(9, \"div\", 96)(10, \"div\", 52)(11, \"div\", 53);\n    i0.ɵɵelement(12, \"div\", 54);\n    i0.ɵɵelementContainerStart(13);\n    i0.ɵɵelementStart(14, \"div\", 97);\n    i0.ɵɵtext(15, \" Invite a Friend & Get a Free Lesson! \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 98);\n    i0.ɵɵtext(17, \" Share your referral code \\u2013 when your friend signs up & purchases a lesson, both of you get one extra lesson for free! \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 99);\n    i0.ɵɵtext(19, \" Contact us now to claim your discount! \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(20, IntroGradientComponent_div_13_div_20_Template, 1, 1, \"div\", 58);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(20);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isEnrollNowVisible);\n  }\n}\nfunction IntroGradientComponent_div_14_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 59);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const enrollAiChatTemplate_r13 = i0.ɵɵreference(20);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", enrollAiChatTemplate_r13);\n  }\n}\nfunction IntroGradientComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 60)(2, \"div\", 73)(3, \"div\", 100)(4, \"div\", 70)(5, \"div\", 92)(6, \"div\", 93);\n    i0.ɵɵelement(7, \"div\", 94, 6);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(9, \"div\", 63)(10, \"div\", 52)(11, \"div\", 53);\n    i0.ɵɵelement(12, \"div\", 54);\n    i0.ɵɵelementContainerStart(13);\n    i0.ɵɵelementStart(14, \"div\", 101);\n    i0.ɵɵtext(15, \"Accelerate Your Language Journey with Our Interactive AI Chat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 57);\n    i0.ɵɵtext(17, \" Available with Flexible & Premium Packages!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(18, IntroGradientComponent_div_14_div_18_Template, 1, 1, \"div\", 58);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(18);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isEnrollNowVisible);\n  }\n}\nfunction IntroGradientComponent_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 60)(2, \"div\", 73)(3, \"div\", 102)(4, \"div\", 52)(5, \"div\", 53);\n    i0.ɵɵelement(6, \"div\", 54);\n    i0.ɵɵelementContainerStart(7);\n    i0.ɵɵelementStart(8, \"div\", 103);\n    i0.ɵɵtext(9, \" Stay Connected, Stay Inspired! \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 104);\n    i0.ɵɵtext(11, \" Find us on Social Media and be part of a global community of language learners. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 105)(13, \"ul\", 106)(14, \"li\", 107)(15, \"a\", 108);\n    i0.ɵɵelement(16, \"img\", 109);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"li\", 110)(18, \"a\", 108);\n    i0.ɵɵelement(19, \"i\", 111);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"li\", 112)(21, \"a\", 108);\n    i0.ɵɵelement(22, \"img\", 113);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"li\", 114)(24, \"a\", 108);\n    i0.ɵɵelement(25, \"i\", 115);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"li\", 116)(27, \"a\", 108);\n    i0.ɵɵelement(28, \"i\", 117);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"href\", ctx_r0.generalService.socialLinks.facebook, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", ctx_r0.generalService.socialLinks.twitter, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", ctx_r0.generalService.socialLinks.linkedin, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", ctx_r0.generalService.socialLinks.instagram, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"href\", ctx_r0.generalService.socialLinks.tiktok, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction IntroGradientComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Default content\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction IntroGradientComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 118)(1, \"div\", 119)(2, \"div\", 120);\n    i0.ɵɵtext(3, \"Can't wait to start? \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\")(5, \"button\", 121);\n    i0.ɵɵlistener(\"click\", function IntroGradientComponent_ng_template_17_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.generalService.navigateToBuyPackage());\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction IntroGradientComponent_ng_template_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 118)(1, \"div\", 119)(2, \"div\", 120);\n    i0.ɵɵtext(3, \"Can't wait to start? \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\")(5, \"button\", 122);\n    i0.ɵɵlistener(\"click\", function IntroGradientComponent_ng_template_19_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.generalService.navigateToAiChat());\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction IntroGradientComponent_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 118)(1, \"div\", 119)(2, \"div\", 120);\n    i0.ɵɵtext(3, \" Get the Offer \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\")(5, \"button\", 123);\n    i0.ɵɵlistener(\"click\", function IntroGradientComponent_ng_template_21_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.generalService.openExternalLink(\"https://www.mylingotrip.com/contact\"));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction IntroGradientComponent_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 118)(1, \"div\", 119)(2, \"div\", 120);\n    i0.ɵɵtext(3, \" Get the Offer \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\")(5, \"button\", 123);\n    i0.ɵɵlistener(\"click\", function IntroGradientComponent_ng_template_23_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.generalService.openExternalLink(\"https://docs.google.com/forms/d/e/1FAIpQLSftI8I9-HuPRhYiT4RluOCTSr5VBwgJSQ1eHQxOrD64XNmkcQ/viewform?usp=header\"));\n    });\n    i0.ɵɵelementEnd()()()();\n  }\n}\nexport class IntroGradientComponent {\n  constructor(generalService, classroomService, authService, ratingAndReportService, zone, renderer) {\n    this.generalService = generalService;\n    this.classroomService = classroomService;\n    this.authService = authService;\n    this.ratingAndReportService = ratingAndReportService;\n    this.zone = zone;\n    this.renderer = renderer;\n    this.subs = new SubSink();\n    this.myTemplate = {};\n    this.isEnrollNowVisible = true;\n    this.assignedTeacher = false;\n    this.leftIntroText = false;\n    this.centerTrialText = false;\n    this.freeTrialReceivedRequest = false;\n    this.bannerMode = 'primary';\n    this.teacher = {};\n    this.classRoom = {};\n    this.localTime = '';\n    this.myCounter = {};\n    this.lottieEl = {};\n    this.userService = inject(UserService);\n    this.toastService = inject(ToastService);\n    this.timezones = this.generalService.getTimezones();\n    this.user = {};\n    this.hasRated = false;\n  }\n  ngOnInit() {\n    this.user = this.authService.getLoggedInUser();\n    // console.log((this.bannerMode));\n    // Get the offset value from the JSON data\n    const offset = this.timezones.find(el => el.utc.includes(this.user.timeZone));\n    const local = this.generalService.getCurrentTimeInTimezone(this.user.timeZone);\n    // console.log(local);\n    this.localTime = this.generalService.convertTimeToHourOfDayFormat(local, this.user.timeZone);\n    if (this.bannerMode === 'primary' || this.bannerMode === 'primary-assigned') {\n      this.startLocalTimeTimer(offset);\n    }\n    if (this.bannerMode === 'trial-assigned' && !this.generalService.isObjectEmpty(this.classRoom)) {\n      this.subs.add(this.ratingAndReportService.getTeacherTrialRating(+this.classRoom.id).subscribe(res => {\n        if (res) {\n          this.hasRated = !this.generalService.isObjectEmpty(res);\n        }\n      }));\n    }\n  }\n  ngAfterViewInit() {\n    if (this.myCounter && this.myCounter.nativeElement && this.isValidTimezone()) {\n      this.zone.runOutsideAngular(() => {\n        setInterval(() => {\n          if (this.bannerMode === 'primary' || this.bannerMode === 'primary-received') {\n            this.renderer.setProperty(this.myCounter.nativeElement, 'textContent', this.generalService.getUserLocalTime(this.user.timeZone));\n          }\n        }, 1000);\n      });\n    }\n    if (this.bannerMode === 'aichat-promo') {\n      this.addLottiePlayer(this.lottieEl);\n    }\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  showTemplate() {\n    this.isEnrollNowVisible = true;\n  }\n  isValidTimezone() {\n    const timeZone = this.user.timeZone;\n    let containsNumber = false;\n    for (let i = 0; i < timeZone.length; i++) {\n      if (!isNaN(parseInt(timeZone[i]))) {\n        containsNumber = true;\n        break;\n      }\n    }\n    return containsNumber;\n  }\n  convertTimezoneValueToText() {\n    return this.timezones.find(el => el.utc.includes(this.user.timeZone));\n  }\n  startLocalTimeTimer(offset) {\n    this.subs.sink = timer(0, 1000).pipe(map(() => new Date()), share()).subscribe(time => {\n      const local = this.generalService.getCurrentTimeInTimezone(this.user.timeZone);\n      this.localTime = this.generalService.convertTimeToHourOfDayFormat(local, this.user.timeZone);\n      // console.log(this.localTime);\n    });\n  }\n  // Method to copy text to clipboard\n  copyText(text) {\n    const textarea = document.createElement('textarea');\n    textarea.style.position = 'fixed';\n    textarea.style.opacity = '0';\n    textarea.value = text;\n    document.body.appendChild(textarea);\n    textarea.select();\n    try {\n      document.execCommand('copy');\n      console.log('Text copied to clipboard');\n      this.toastService.setShowToastmessage({\n        severity: 'success',\n        summary: '',\n        detail: 'Referral code copied to clipboard'\n      });\n    } catch (err) {\n      console.error('Failed to copy text: ', err);\n    } finally {\n      document.body.removeChild(textarea);\n    }\n  }\n  addLottiePlayer(el) {\n    console.log(el);\n    if (!el) {\n      return;\n    }\n    const lottiePlayer = `<lottie-player src=\"/assets/icons/lottie/ai-chat-2.json\"  \nbackground=\"transparent\"  speed=\"1\"  style=\"width: 120px; margin: 0px auto; transform: scale(1.4); \" autoplay loop></lottie-player>`;\n    el.nativeElement.innerHTML = lottiePlayer;\n  }\n  static #_ = this.ɵfac = function IntroGradientComponent_Factory(t) {\n    return new (t || IntroGradientComponent)(i0.ɵɵdirectiveInject(i1.GeneralService), i0.ɵɵdirectiveInject(i2.ClassroomService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.RatingAndReportService), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.Renderer2));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: IntroGradientComponent,\n    selectors: [[\"app-intro-gradient\"]],\n    viewQuery: function IntroGradientComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(_c2, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.myTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.myCounter = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.lottieEl = _t.first);\n      }\n    },\n    inputs: {\n      assignedTeacher: \"assignedTeacher\",\n      leftIntroText: \"leftIntroText\",\n      centerTrialText: \"centerTrialText\",\n      freeTrialReceivedRequest: \"freeTrialReceivedRequest\",\n      bannerMode: \"bannerMode\",\n      teacher: \"teacher\",\n      classRoom: \"classRoom\"\n    },\n    decls: 25,\n    vars: 14,\n    consts: [[\"enrollNowTemplate\", \"\"], [\"enrollAiChatTemplate\", \"\"], [\"contactUsTemplate\", \"\"], [\"getCodeTemplate\", \"\"], [\"counter\", \"\"], [\"ongoingTrial\", \"\"], [\"lottieEl\", \"\"], [1, \"typography\", \"typography--all\"], [1, \"intro-container\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [4, \"ngSwitchDefault\"], [1, \"bg-gradient-primary\", \"border-round-xl\", \"banner-height\"], [1, \"grid\", \"grid-nogutter\", \"h-full\"], [1, \"p-4\", \"text-50\", \"flex\", \"justify-content-center\", \"sm:justify-content-between\", \"align-items-center\", \"flex-row\", \"w-100\"], [1, \"flex-column\"], [1, \"mb-0\", \"typography__heading--xlarge\"], [1, \"font-semibold\"], [1, \"font-base\"], [1, \"font-semibold\", \"font-2xl\"], [1, \"bg-gradient-primary-received\", \"border-round-xl\", \"banner-height\"], [1, \"lg:px-4\", \"md:px-1\", \"text-50\", \"flex\", \"justify-content-center\", \"sm:justify-content-between\", \"align-items-center\", \"flex-column\", \"sm:flex-row\", \"w-100\"], [1, \"flex-column\", \"p-2\"], [1, \"mb-0\", \"text-xl\", \"flex\", \"align-items-center\", \"gap-2\"], [1, \"typography__heading--medium\", \"flex\", \"align-items-start\", \"flex-column\"], [1, \"flex\", \"align-items-center\", \"justify-content-start\", \"gap-2\"], [1, \"line-height-1\"], [1, \"font-semibold\", \"referral-wrapper\"], [1, \"pi\", \"pi-copy\", \"cursor-pointer\", 3, \"click\"], [4, \"ngIf\"], [1, \"align-items-center\", \"justify-content-end\", \"ml-auto\", \"sm:w-6\", \"flex\"], [1, \"w-4\", \"px-2\", \"text-center\", \"flex-auto\"], [1, \"inline-block\", 2, \"border-radius\", \"10px\"], [1, \"avatar-circle\"], [\"width\", \"130\", \"alt\", \"Classroom Image\", 3, \"error\", \"src\"], [1, \"typography__heading--small\"], [1, \"text-white\", \"font-medium\"], [1, \"text-white\", \"font-semibold\", \"line-height-1\"], [1, \"w-6\", \"text-center\", \"font-sm\"], [1, \"lg:px-4\", \"md:px-1\", \"text-50\", \"flex\", \"justify-content-center\", \"sm:justify-content-between\", \"align-items-center\", \"flex-row\", \"w-100\"], [1, \"mt-0\", \"mb-0\", \"font-xl\"], [1, \"typography__heading--medium\"], [1, \"align-items-center\", \"justify-content-end\", \"ml-auto\", \"w-6\", \"flex\"], [1, \"w-3\", \"px-1\", \"text-center\", \"flex-auto\"], [\"src\", \"/assets/images/dashboard/banners/course-received.svg\"], [1, \"w-6\", \"text-center\"], [1, \"font-semibold\", \"typography__heading--medium\"], [1, \"mt-2\", \"typography__heading--small\"], [1, \"mb-0\", \"text-xl\"], [1, \"bg-gradient-trial-received\", \"overflow-hidden\", \"border-round-xl\", \"banner-height\"], [1, \"grid\", \"grid-nogutter\", \"h-full\", \"flex-column\", \"sm:flex-row\", \"sm:justify-content-around\"], [1, \"sm:col-6\", \"md:col-offset-3\", \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"px-1\", \"py-1\", \"md:px-1\", \"lg:px-1\"], [1, \"text-700\", \"text-center\"], [1, \"text-white\", \"font-bold\", \"my-1\"], [\"src\", \"/assets/images/dashboard/banners/banner-check.svg\", \"width\", \"28\", 1, \"w\"], [1, \"text-white\", \"font-bold\", \"typography__heading--medium\"], [1, \"text-white\", \"sm:mt-2\", \"typography__heading--small\", \"lg:w-10\", \"mx-auto\"], [\"class\", \"my-custom-location\", 3, \"ngTemplateOutlet\", 4, \"ngIf\"], [1, \"my-custom-location\", 3, \"ngTemplateOutlet\"], [1, \"bg-gradient-trial-received\", \"overflow-hidden\", \"border-round-xl\", \"banner-height\", \"md:mt-0\"], [1, \"grid\", \"grid-nogutter\", \"h-full\", \"w-full\", \"block\", \"sm:flex\"], [\"class\", \"md:col-3\", 4, \"ngIf\"], [1, \"sm:col-6\", \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"text-white\", \"font-md\"], [1, \"capitalize\"], [\"pRipple\", \"\", \"pButton\", \"\", 1, \"mt-2\", \"p-button-outlined\", \"p-button-outlined\", \"p-button-rounded\", \"button-outlined\", \"p-button-sm\", \"text-white\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"gap-1\"], [1, \"text-base\", \"s12-16\"], [1, \"md:col-3\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"flex-column\", \"sm:m-3\"], [1, \"avatar-circle\", \"hidden\", \"sm:block\"], [1, \"text-white\", \"text-center\", \"typography__heading--small\"], [1, \"grid\", \"grid-nogutter\", \"h-full\", \"w-full\", \"block\", \"sm:flex\", \"p-2\", \"sm:p-0\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"text-white\", \"text-xs\", \"sm:text-base\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"sm:mt-2\", \"p-button-outlined\", \"p-button-rounded\", \"button-outlined\", \"p-button-sm\", \"text-white\", 3, \"ngClass\", \"pTooltip\", \"routerLink\"], [\"src\", \"/assets/icons/lessons/rating-stars.svg\", \"height\", \"16\", 1, \"white-filter\"], [1, \"bg-gradient-trial-arranged\", \"overflow-hidden\", \"border-round-xl\", \"banner-height\"], [1, \"grid\", \"grid-nogutter\", \"h-full\", \"justify-content-center\"], [1, \"col-12\", \"md:col-3\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"flex-column\", \"m-0\", \"sm:m-3\"], [1, \"col-12\", \"md:col-6\", \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"text-white\", \"typography__heading--large\", \"font-bold\", \"mt-2\"], [1, \"bg-gradient-unreachable\", \"overflow-hidden\", \"border-round-xl\", \"banner-height\"], [1, \"md:col-3\", \"flex\", \"justify-content-center\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"flex-column\", \"m-3\"], [\"src\", \"/assets/images/dashboard/banners/whiteoops.svg\", \"width\", \"64\", 1, \"w\"], [1, \"md:col-6\", \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"text-white\", \"sm:mt-2\"], [1, \"grid\", \"grid-nogutter\", \"h-full\", \"w-full\", \"block\", \"sm:flex\", \"justify-content-center\", \"p-2\", \"sm:p-0\"], [1, \"md:col-2\", \"hidden\", \"sm:flex\", \"justify-content-center\", \"align-items-center\"], [1, \"text-white\", \"font-bold\", \"my-1\", \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"text-center\", \"w-full\", \"hidden\", \"sm:block\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", 2, \"mix-blend-mode\", \"luminosity\"], [\"src\", \"/assets/icons/discount2.png\", \"width\", \"64\", 1, \"w\"], [1, \"sm:col-7\", \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"text-white\", \"text-lg\", \"sm:text-2xl\", \"font-bold\"], [1, \"text-white\", \"sm:mt-2\", \"text-lg\", \"lg:w-full\", \"mx-0\"], [1, \"text-white\", \"sm:mt-2\", \"typography__heading--medium\", \"lg:w-full\", \"mx-auto\"], [1, \"md:col-3\", \"flex\", \"justify-content-center\", \"align-items-center\"], [1, \"text-white\", \"text-md\", \"sm:text-base\"], [1, \"sm:col-6\", \"w-full\", \"mx-auto\", \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"text-white\", \"text-lg\", \"sm:text-lg\", \"font-bold\"], [1, \"text-white\", \"sm:mt-2\", \"typography__heading--medium\", \"mx-auto\"], [1, \"mt-2\", \"mx-auto\"], [1, \"wrapper\", \"m-0\"], [1, \"icon\", \"facebook\"], [\"target\", \"_blank\", 3, \"href\"], [\"src\", \"/assets/icons/social/facebook.svg\", \"width\", \"16\", 1, \"filter-white\"], [1, \"icon\", \"twitter\"], [1, \"pi\", \"pi-twitter\"], [1, \"icon\", \"linkedin\"], [\"src\", \"/assets/icons/social/linkedin.svg\", \"width\", \"16\", 1, \"filter-white\"], [1, \"icon\", \"instagram\"], [1, \"pi\", \"pi-instagram\"], [1, \"icon\", \"tiktok\"], [1, \"pi\", \"pi-tiktok\"], [1, \"md:col-3\", \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"flex\", \"flex-column\", \"align-items-center\"], [1, \"text-white\", \"sm:mb-2\", \"font-bold\", \"typography__heading--medium\", \"hidden\", \"sm:block\"], [\"type\", \"button\", \"icon\", \"pi pi-arrow-right\", \"iconPos\", \"right\", \"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Enroll now\", 1, \"p-button-outlined\", \"p-button-rounded\", \"p-button-sm\", \"p-white\", 3, \"click\"], [\"type\", \"button\", \"icon\", \"pi pi-arrow-right\", \"iconPos\", \"right\", \"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Practice now\", 1, \"p-button-outlined\", \"p-button-rounded\", \"p-button-sm\", \"p-white\", 3, \"click\"], [\"type\", \"button\", \"icon\", \"pi pi-arrow-right\", \"iconPos\", \"right\", \"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Contact Us\", 1, \"p-button-outlined\", \"p-button-rounded\", \"p-button-sm\", \"p-white\", 3, \"click\"]],\n    template: function IntroGradientComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8);\n        i0.ɵɵelementContainerStart(2, 9);\n        i0.ɵɵtemplate(3, IntroGradientComponent_ng_container_3_Template, 14, 3, \"ng-container\", 10)(4, IntroGradientComponent_ng_container_4_Template, 17, 2, \"ng-container\", 10)(5, IntroGradientComponent_ng_container_5_Template, 27, 2, \"ng-container\", 10)(6, IntroGradientComponent_ng_container_6_Template, 15, 4, \"ng-container\", 10)(7, IntroGradientComponent_div_7_Template, 13, 1, \"div\", 10)(8, IntroGradientComponent_div_8_Template, 20, 3, \"div\", 10)(9, IntroGradientComponent_div_9_Template, 12, 4, \"div\", 10)(10, IntroGradientComponent_div_10_Template, 21, 7, \"div\", 10)(11, IntroGradientComponent_div_11_Template, 14, 1, \"div\", 10)(12, IntroGradientComponent_div_12_Template, 21, 1, \"div\", 10)(13, IntroGradientComponent_div_13_Template, 21, 1, \"div\", 10)(14, IntroGradientComponent_div_14_Template, 19, 1, \"div\", 10)(15, IntroGradientComponent_div_15_Template, 29, 5, \"div\", 10)(16, IntroGradientComponent_div_16_Template, 2, 0, \"div\", 11);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(17, IntroGradientComponent_ng_template_17_Template, 6, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(19, IntroGradientComponent_ng_template_19_Template, 6, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(21, IntroGradientComponent_ng_template_21_Template, 6, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(23, IntroGradientComponent_ng_template_23_Template, 6, 0, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngSwitch\", ctx.bannerMode);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngSwitchCase\", \"primary\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngSwitchCase\", \"primary-referral-code\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngSwitchCase\", \"primary-received\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngSwitchCase\", \"primary-assigned\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngSwitchCase\", \"trial-received\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngSwitchCase\", \"trial-dismissed\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngSwitchCase\", \"trial-assigned\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngSwitchCase\", \"trial-arranged\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngSwitchCase\", \"unreachable\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngSwitchCase\", \"primary-offer\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngSwitchCase\", \"referral-promo\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngSwitchCase\", \"aichat-promo\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngSwitchCase\", \"social-promo\");\n      }\n    },\n    dependencies: [i5.NgClass, i5.NgIf, i5.NgTemplateOutlet, i5.NgSwitch, i5.NgSwitchCase, i5.NgSwitchDefault, i6.RouterLink, i7.ButtonDirective, i8.Tooltip, i5.DatePipe],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.intro-container[_ngcontent-%COMP%] {\\n  background-size: cover;\\n}\\n\\n.banner-height[_ngcontent-%COMP%] {\\n  height: 150px;\\n}\\n@media only screen and (min-width: 576px) {\\n  .banner-height[_ngcontent-%COMP%] {\\n    height: 150px;\\n  }\\n}\\n\\n.bg-gradient-primary[_ngcontent-%COMP%], .bg-gradient-primary-received[_ngcontent-%COMP%] {\\n  background-image: url(\\\"/assets/images/gradient-bg-2.jpg\\\");\\n  background-size: cover;\\n}\\n\\n.bg-gradient-trial-arranged[_ngcontent-%COMP%] {\\n  background-repeat: no-repeat;\\n  background-size: cover;\\n  background: url(\\\"/assets/images/dashboard/banners/before-assignment-lesson-gradient.svg\\\") no-repeat center center;\\n  background-size: unset;\\n}\\n\\n.bg-gradient-trial-received[_ngcontent-%COMP%] {\\n  background-image: url(\\\"/assets/images/dashboard/banners/trial-received-gradient.png\\\");\\n  background-repeat: no-repeat;\\n  background-size: auto;\\n  background-position: center center;\\n}\\n\\n.bg-gradient-unreachable[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(45deg, hsl(225, 68%, 54%) 0%, hsl(227, 69%, 58%) 11%, hsl(228, 70%, 61%) 22%, hsl(229, 71%, 64%) 33%, hsl(229, 72%, 67%) 44%, hsl(230, 73%, 70%) 56%, hsl(230, 75%, 72%) 67%, hsl(230, 77%, 75%) 78%, hsl(231, 79%, 77%) 89%, hsl(231, 82%, 80%) 100%);\\n}\\n\\n.avatar-circle[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n}\\n\\n.typography[_ngcontent-%COMP%] {\\n  \\n\\n  \\n\\n  \\n\\n}\\n.typography--all[_ngcontent-%COMP%] {\\n  letter-spacing: 0.01em;\\n}\\n.typography__heading--xlarge[_ngcontent-%COMP%] {\\n  font-size: clamp(1.25rem, 0.47vw + 1.16rem, 1.63rem);\\n}\\n.typography__heading--large[_ngcontent-%COMP%] {\\n  font-size: clamp(0.88rem, 0.63vw + 0.75rem, 1.38rem);\\n}\\n.typography__heading--medium[_ngcontent-%COMP%] {\\n  font-size: clamp(0.75rem, 0.31vw + 0.69rem, 1rem);\\n}\\n.typography__heading--small[_ngcontent-%COMP%] {\\n  font-size: clamp(0.75rem, 0.16vw + 0.72rem, 0.88rem);\\n}\\n.typography__description--large[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n}\\n.typography__description--medium[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n.typography__description--small[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n\\n  .p-button.p-white {\\n  background: transparent;\\n  color: white;\\n  border: 1px solid white;\\n}\\n  .p-button.p-white:enabled:hover {\\n  background: transparent;\\n  color: white;\\n  border: 1px solid white;\\n}\\n\\n.white-filter[_ngcontent-%COMP%] {\\n  filter: invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(150%) contrast(100%);\\n}\\n\\n@media only screen and (max-width: 768px) {\\n  [_nghost-%COMP%]     .p-button-sm {\\n    padding: 0.32475rem 0.475rem;\\n  }\\n}\\n\\n.referral-wrapper[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: bold;\\n  border: 1px dashed rgb(213, 216, 219);\\n  padding: 2px 14px;\\n  line-height: 26px;\\n  letter-spacing: 1px;\\n  text-align: center;\\n  -webkit-user-select: all;\\n          user-select: all;\\n  border-radius: 4px;\\n}\\n\\n.wrapper[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  list-style: none;\\n}\\n\\n.wrapper[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  position: relative;\\n  background: #ffffff;\\n  border-radius: 50%;\\n  padding: 8px;\\n  margin: 0 5px;\\n  width: 32px;\\n  height: 32px;\\n  font-size: 16px;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  flex-direction: column;\\n  box-shadow: 0 10px 10px rgba(0, 0, 0, 0.1);\\n  cursor: pointer;\\n  transition: all 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);\\n  line-height: 1;\\n  color: white;\\n  background-image: linear-gradient(to top, #132172, #8497ff);\\n}\\n.wrapper[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]:hover {\\n  filter: brightness(110%);\\n}\\n.wrapper[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%] {\\n  font-size: 0.825rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["inject", "SubSink", "timer", "map", "share", "UserService", "ToastService", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "user", "firstName", "ɵɵtextInterpolate2", "localTime", "convertTimezoneValueToText", "text", "ɵɵlistener", "IntroGradientComponent_ng_container_4_ng_container_16_Template_img_error_5_listener", "$event", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "generalService", "setDefaultUserAvatar", "ɵɵproperty", "getDomainFileNamePath", "teacher", "avatarUrl", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "getShortFullName", "IntroGradientComponent_ng_container_4_Template_i_click_13_listener", "_r2", "copyText", "userService", "generateReferralCodeFromUser", "ɵɵtemplate", "IntroGradientComponent_ng_container_4_ng_container_16_Template", "isNullishObject", "timeZone", "IntroGradientComponent_ng_container_6_ng_container_14_Template_img_error_5_listener", "_r4", "IntroGradientComponent_ng_container_6_ng_container_14_Template", "enrollNowTemplate_r5", "IntroGradientComponent_div_7_div_12_Template", "isEnrollNowVisible", "IntroGradientComponent_div_8_div_3_Template_img_error_3_listener", "_r7", "classRoom", "IntroGradientComponent_div_8_div_3_Template", "IntroGradientComponent_div_8_Template_button_click_15_listener", "_r6", "openExternalLink", "IntroGradientComponent_div_8_div_19_Template", "language", "IntroGradientComponent_div_9_div_3_Template_img_error_3_listener", "_r8", "ɵɵpureFunction1", "_c3", "hasRated", "classroomService", "getClassroomRateUrl", "IntroGradientComponent_div_9_div_3_Template", "IntroGradientComponent_div_9_ng_container_8_Template", "IntroGradientComponent_div_9_ng_template_9_Template", "ɵɵtemplateRefExtractor", "IntroGradientComponent_div_9_div_11_Template", "isTrialClassroomWithCompletedFirstLesson", "ongoingTrial_r9", "IntroGradientComponent_div_10_Template_img_error_6_listener", "_r10", "IntroGradientComponent_div_10_div_20_Template", "ɵɵpipeBind2", "lessons", "startingDate", "IntroGradientComponent_div_11_div_13_Template", "contactUsTemplate_r11", "IntroGradientComponent_div_12_div_20_Template", "getCodeTemplate_r12", "IntroGradientComponent_div_13_div_20_Template", "enrollAiChatTemplate_r13", "IntroGradientComponent_div_14_div_18_Template", "socialLinks", "facebook", "twitter", "linkedin", "instagram", "tiktok", "IntroGradientComponent_ng_template_17_Template_button_click_5_listener", "_r14", "navigateToBuyPackage", "IntroGradientComponent_ng_template_19_Template_button_click_5_listener", "_r15", "navigateToAiChat", "IntroGradientComponent_ng_template_21_Template_button_click_5_listener", "_r16", "IntroGradientComponent_ng_template_23_Template_button_click_5_listener", "_r17", "IntroGradientComponent", "constructor", "authService", "ratingAndReportService", "zone", "renderer", "subs", "myTemplate", "<PERSON><PERSON><PERSON><PERSON>", "leftIntroText", "centerTrialText", "freeTrialReceivedRequest", "bannerMode", "myCounter", "<PERSON><PERSON><PERSON><PERSON>", "toastService", "timezones", "getTimezones", "ngOnInit", "getLoggedInUser", "offset", "find", "el", "utc", "includes", "local", "getCurrentTimeInTimezone", "convertTimeToHourOfDayFormat", "startLocalTimeTimer", "isObjectEmpty", "add", "getTeacherTrialRating", "id", "subscribe", "res", "ngAfterViewInit", "nativeElement", "isValidTimezone", "runOutsideAngular", "setInterval", "setProperty", "getUserLocalTime", "addLottiePlayer", "ngOnDestroy", "unsubscribe", "showTemplate", "containsNumber", "i", "length", "isNaN", "parseInt", "sink", "pipe", "Date", "time", "textarea", "document", "createElement", "style", "position", "opacity", "value", "body", "append<PERSON><PERSON><PERSON>", "select", "execCommand", "console", "log", "setShowToastmessage", "severity", "summary", "detail", "err", "error", "<PERSON><PERSON><PERSON><PERSON>", "lottiePlayer", "innerHTML", "_", "ɵɵdirectiveInject", "i1", "GeneralService", "i2", "ClassroomService", "i3", "AuthService", "i4", "RatingAndReportService", "NgZone", "Renderer2", "_2", "selectors", "viewQuery", "IntroGradientComponent_Query", "rf", "ctx", "IntroGradientComponent_ng_container_3_Template", "IntroGradientComponent_ng_container_4_Template", "IntroGradientComponent_ng_container_5_Template", "IntroGradientComponent_ng_container_6_Template", "IntroGradientComponent_div_7_Template", "IntroGradientComponent_div_8_Template", "IntroGradientComponent_div_9_Template", "IntroGradientComponent_div_10_Template", "IntroGradientComponent_div_11_Template", "IntroGradientComponent_div_12_Template", "IntroGradientComponent_div_13_Template", "IntroGradientComponent_div_14_Template", "IntroGradientComponent_div_15_Template", "IntroGradientComponent_div_16_Template", "IntroGradientComponent_ng_template_17_Template", "IntroGradientComponent_ng_template_19_Template", "IntroGradientComponent_ng_template_21_Template", "IntroGradientComponent_ng_template_23_Template"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\dashboard\\intro-gradient\\intro-gradient.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\dashboard\\intro-gradient\\intro-gradient.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, Component, ElementRef, inject, Input, NgZone, OnInit, Renderer2, TemplateRef, ViewChild } from '@angular/core';\r\nimport { User } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport * as moment from 'moment';\r\nimport { Timezone } from 'src/app/core/models/general.model';\r\nimport { Classroom } from 'src/app/core/models/classroom.model';\r\nimport { SubSink } from 'subsink';\r\nimport { RatingAndReportService } from 'src/app/core/services/rating-and-report.service';\r\nimport { timer } from 'rxjs';\r\nimport { map, share } from 'rxjs/operators';\r\nimport { UserService } from 'src/app/core/services/user.service';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\ntype BannerMode = 'primary' | 'primary-received' | 'primary-assigned' | 'trial-assigned' | 'trial-completed' | 'trial-received' | 'trial-arranged' | 'unreachable' | 'aichat-promo';\r\n\r\n@Component({\r\n  selector: 'app-intro-gradient',\r\n  templateUrl: './intro-gradient.component.html',\r\n  styleUrls: ['./intro-gradient.component.scss'],\r\n})\r\nexport class IntroGradientComponent implements OnInit {\r\n  private subs = new SubSink();\r\n  @ViewChild('enrollNowTemplate') myTemplate: TemplateRef<any> = {} as TemplateRef<any>;\r\n  isEnrollNowVisible = true;\r\n  @Input() assignedTeacher = false;\r\n  @Input() leftIntroText = false;\r\n  @Input() centerTrialText = false;\r\n  @Input() freeTrialReceivedRequest = false;\r\n  @Input() bannerMode: BannerMode = 'primary';\r\n  @Input() teacher: {} = {};\r\n  @Input() classRoom: Classroom = {} as Classroom;\r\n  localTime = '';\r\n  @ViewChild('counter', { static: false }) myCounter: ElementRef = {} as ElementRef;\r\n  @ViewChild('lottieEl') lottieEl: ElementRef = {} as ElementRef;\r\n  userService = inject(UserService);\r\n  toastService = inject(ToastService);\r\n  public timezones: Timezone[] = this.generalService.getTimezones();\r\n  public user: User = {} as User;\r\n  hasRated = false;\r\n  constructor(\r\n    public generalService: GeneralService,\r\n    public classroomService: ClassroomService,\r\n    private authService: AuthService,\r\n    public ratingAndReportService: RatingAndReportService,\r\n    private zone: NgZone,\r\n    private renderer: Renderer2\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.user = this.authService.getLoggedInUser();\r\n    // console.log((this.bannerMode));\r\n    // Get the offset value from the JSON data\r\n    const offset = this.timezones.find(el => el.utc.includes(this.user.timeZone!))\r\n\r\n\r\n    const local = this.generalService.getCurrentTimeInTimezone(this.user.timeZone!);\r\n    // console.log(local);\r\n    this.localTime = this.generalService.convertTimeToHourOfDayFormat(local, this.user.timeZone!);\r\n\r\n\r\n    if ((this.bannerMode === 'primary' || this.bannerMode === 'primary-assigned')) {\r\n      this.startLocalTimeTimer(offset!);\r\n    }\r\n\r\n    if (this.bannerMode === 'trial-assigned' && !this.generalService.isObjectEmpty(this.classRoom)) {\r\n      this.subs.add(this.ratingAndReportService.getTeacherTrialRating(+this.classRoom.id).subscribe((res) => {\r\n        if (res) {\r\n          this.hasRated = !this.generalService.isObjectEmpty(res);\r\n        }\r\n      }));\r\n    }\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    if (this.myCounter && this.myCounter.nativeElement && this.isValidTimezone()) {\r\n\r\n      this.zone.runOutsideAngular(() => {\r\n        setInterval(() => {\r\n          if (this.bannerMode === 'primary' || this.bannerMode === 'primary-received') {\r\n            this.renderer.setProperty(this.myCounter.nativeElement, 'textContent',\r\n              this.generalService.getUserLocalTime(this.user.timeZone!));\r\n          }\r\n        }, 1000);\r\n      });\r\n    }\r\n    if (this.bannerMode === 'aichat-promo') {\r\n      this.addLottiePlayer(this.lottieEl);\r\n    }\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  showTemplate() {\r\n    this.isEnrollNowVisible = true;\r\n  }\r\n\r\n  isValidTimezone() {\r\n    const timeZone = this.user.timeZone;\r\n    let containsNumber = false;\r\n\r\n    for (let i = 0; i < timeZone!.length; i++) {\r\n      if (!isNaN(parseInt(timeZone![i]))) {\r\n        containsNumber = true;\r\n        break;\r\n      }\r\n    }\r\n\r\n    return containsNumber;\r\n  }\r\n\r\n  convertTimezoneValueToText() {\r\n    return (this.timezones.find(el => el.utc.includes(this.user.timeZone!)));\r\n  }\r\n\r\n  startLocalTimeTimer(offset: Timezone) {\r\n    this.subs.sink = timer(0, 1000)\r\n      .pipe(\r\n        map(() => new Date()),\r\n        share()\r\n      )\r\n      .subscribe(time => {\r\n        const local = this.generalService.getCurrentTimeInTimezone(this.user.timeZone!);\r\n        this.localTime = this.generalService.convertTimeToHourOfDayFormat(local, this.user.timeZone!);\r\n        // console.log(this.localTime);\r\n      });\r\n  }\r\n\r\n  // Method to copy text to clipboard\r\n  copyText(text: string) {\r\n    const textarea = document.createElement('textarea');\r\n    textarea.style.position = 'fixed';\r\n    textarea.style.opacity = '0';\r\n    textarea.value = text;\r\n    document.body.appendChild(textarea);\r\n    textarea.select();\r\n    try {\r\n      document.execCommand('copy');\r\n      console.log('Text copied to clipboard');\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'success',\r\n        summary: '',\r\n        detail: 'Referral code copied to clipboard',\r\n      });\r\n    } catch (err) {\r\n      console.error('Failed to copy text: ', err);\r\n    } finally {\r\n      document.body.removeChild(textarea);\r\n    }\r\n  }\r\n\r\n  private addLottiePlayer(el: ElementRef): void {\r\n    console.log(el);\r\n    if (!el) {\r\n      return;\r\n    }\r\n    const lottiePlayer = `<lottie-player src=\"/assets/icons/lottie/ai-chat-2.json\"  \r\nbackground=\"transparent\"  speed=\"1\"  style=\"width: 120px; margin: 0px auto; transform: scale(1.4); \" autoplay loop></lottie-player>`;\r\n    el.nativeElement.innerHTML = lottiePlayer;\r\n  }\r\n\r\n}\r\n", "<div class=\"typography typography--all\">\r\n    <div class=\"intro-container\">\r\n\r\n\r\n        <ng-container [ngSwitch]=\"bannerMode\">\r\n            <ng-container *ngSwitchCase=\"'primary'\">\r\n\r\n                <div class=\" bg-gradient-primary border-round-xl banner-height\">\r\n                    <div class=\"grid grid-nogutter h-full\">\r\n                        <div\r\n                            class=\"p-4 text-50 flex justify-content-center sm:justify-content-between align-items-center flex-row w-100 \">\r\n                            <div class=\"flex-column\">\r\n                                <p class=\"mb-0 typography__heading--xlarge\">Welcome back <span\r\n                                        class=\"font-semibold\">{{this.user.firstName}}!</span>\r\n                                </p>\r\n                                <p class=\"font-base\"><span class=\"font-semibold font-2xl\"> <span #counter></span>\r\n                                    </span> {{localTime}} {{convertTimezoneValueToText().text}}</p>\r\n                            </div>\r\n                            <!-- <div>\r\n                                <i class=\"pi pi-chevron-right text-2xl\"></i>\r\n                            </div> -->\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </ng-container>\r\n\r\n\r\n            <ng-container *ngSwitchCase=\"'primary-referral-code'\">\r\n\r\n                <div class=\" bg-gradient-primary-received border-round-xl banner-height\">\r\n                    <div class=\"grid grid-nogutter h-full\">\r\n                        <div\r\n                            class=\"lg:px-4 md:px-1 text-50 flex justify-content-center sm:justify-content-between align-items-center flex-column sm:flex-row w-100 \">\r\n                            <div class=\"flex-column p-2\">\r\n                                <p class=\"mb-0 text-xl flex align-items-center gap-2\">Refer a friend!\r\n\r\n                                </p>\r\n                                <p class=\" typography__heading--medium flex align-items-start flex-column\">\r\n                                    <span class=\"flex align-items-center justify-content-start gap-2\">\r\n                                        <span class=\"line-height-1\">Your Referral code: </span>\r\n                                        <span class=\"font-semibold referral-wrapper\">\r\n                                            {{userService.generateReferralCodeFromUser(this.user)}}\r\n                                        </span>\r\n                                        <i (click)=\"copyText(userService.generateReferralCodeFromUser(this.user))\"\r\n                                            class=\"pi pi-copy cursor-pointer\"></i>\r\n                                    </span>\r\n                                    <span>\r\n                                        Share the link with your friends and win a free lesson for both of you when they\r\n                                        purchase a package.\r\n                                    </span>\r\n                                </p>\r\n                            </div>\r\n                            <ng-container *ngIf=\"!generalService.isNullishObject(teacher)\">\r\n                                <div class=\"align-items-center justify-content-end ml-auto sm:w-6 flex\">\r\n                                    <div class=\"w-4 px-2 text-center flex-auto\"><span class=\"inline-block\"\r\n                                            style=\"border-radius: 10px;\">\r\n                                            <!-- <i class=\"pi pi-lock text-4xl text-blue-500\"></i> -->\r\n                                            <div class=\"avatar-circle\">\r\n                                                <img [src]=\"generalService.getDomainFileNamePath(teacher.avatarUrl)\"\r\n                                                    (error)=\"generalService.setDefaultUserAvatar($event)\" width=\"130\"\r\n                                                    alt=\"Classroom Image\">\r\n                                            </div>\r\n                                        </span>\r\n                                        <div class=\" typography__heading--small\">\r\n                                            <div class=\"text-white font-medium\">Your teacher</div><span\r\n                                                class=\"text-white font-semibold line-height-1\">{{generalService.getShortFullName(teacher)}}</span>\r\n                                        </div>\r\n                                    </div>\r\n                                    <p class=\"w-6 text-center font-sm\">\r\n                                        Your new teacher has\r\n                                        been assigned and will\r\n                                        be in touch with you\r\n                                        very soon!\r\n                                    </p>\r\n                                </div>\r\n                            </ng-container>\r\n                            <!-- <div>\r\n                            <i class=\"pi pi-chevron-right text-2xl\"></i>\r\n                        </div> -->\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </ng-container>\r\n\r\n\r\n            <ng-container *ngSwitchCase=\"'primary-received'\">\r\n\r\n                <div class=\" bg-gradient-primary-received border-round-xl banner-height\">\r\n                    <div class=\"grid grid-nogutter h-full\">\r\n                        <div\r\n                            class=\"lg:px-4 md:px-1 text-50 flex justify-content-center sm:justify-content-between align-items-center flex-row w-100 \">\r\n                            <div class=\"flex-column\">\r\n                                <p class=\"mb-0 typography__heading--xlarge\">Hi <span\r\n                                        class=\"font-semibold\">{{this.user.firstName}}!</span>\r\n                                </p>\r\n                                <p class=\"mt-0 mb-0 font-xl\"><span class=\"font-semibold\">Welcome to your Course</span>\r\n                                </p>\r\n                                <p class=\" typography__heading--medium\"><span class=\"font-semibold\"> <span\r\n                                            #counter></span></span> {{this.user.timeZone}}</p>\r\n                            </div>\r\n                            <ng-container>\r\n                                <div class=\"align-items-center justify-content-end ml-auto w-6 flex\">\r\n                                    <div class=\"w-3 px-1 text-center flex-auto\"><span class=\"inline-block\"\r\n                                            style=\"border-radius: 10px;\">\r\n                                            <!-- <i class=\"pi pi-lock text-4xl text-blue-500\"></i> -->\r\n                                            <img src=\"/assets/images/dashboard/banners/course-received.svg\">\r\n                                        </span>\r\n                                    </div>\r\n                                    <div class=\"w-6 text-center \">\r\n                                        <div class=\"font-semibold typography__heading--medium\">\r\n                                            We have received\r\n                                            your course booking!\r\n                                        </div>\r\n                                        <div class=\"mt-2 typography__heading--small\">\r\n                                            Please wait while we find the\r\n                                            most suitable teacher for you.\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </ng-container>\r\n                            <!-- <div>\r\n                            <i class=\"pi pi-chevron-right text-2xl\"></i>\r\n                        </div> -->\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </ng-container>\r\n\r\n            <ng-container *ngSwitchCase=\"'primary-assigned'\">\r\n\r\n                <div class=\" bg-gradient-primary-received border-round-xl banner-height\">\r\n                    <div class=\"grid grid-nogutter h-full\">\r\n                        <div\r\n                            class=\"lg:px-4 md:px-1 text-50 flex justify-content-center sm:justify-content-between align-items-center flex-column sm:flex-row w-100 \">\r\n                            <div class=\"flex-column\">\r\n                                <p class=\"mb-0 text-xl\">Welcome back <span\r\n                                        class=\"font-semibold\">{{this.user.firstName}}!</span>\r\n                                </p>\r\n                                <p class=\" typography__heading--medium\"><span class=\"font-semibold\"> <span\r\n                                            #counter></span></span>{{localTime}} {{convertTimezoneValueToText().text}}\r\n                                </p>\r\n                            </div>\r\n                            <ng-container *ngIf=\"!generalService.isNullishObject(teacher)\">\r\n                                <div class=\"align-items-center justify-content-end ml-auto sm:w-6 flex\">\r\n                                    <div class=\"w-4 px-2 text-center flex-auto\"><span class=\"inline-block\"\r\n                                            style=\"border-radius: 10px;\">\r\n                                            <!-- <i class=\"pi pi-lock text-4xl text-blue-500\"></i> -->\r\n                                            <div class=\"avatar-circle\">\r\n                                                <img [src]=\"generalService.getDomainFileNamePath(teacher.avatarUrl)\"\r\n                                                    (error)=\"generalService.setDefaultUserAvatar($event)\" width=\"130\"\r\n                                                    alt=\"Classroom Image\">\r\n                                            </div>\r\n                                        </span>\r\n                                        <div class=\" typography__heading--small\">\r\n                                            <div class=\"text-white font-medium\">Your teacher</div><span\r\n                                                class=\"text-white font-semibold line-height-1\">{{generalService.getShortFullName(teacher)}}</span>\r\n                                        </div>\r\n                                    </div>\r\n                                    <p class=\"w-6 text-center font-sm\">\r\n                                        Your new teacher has\r\n                                        been assigned and will\r\n                                        be in touch with you\r\n                                        very soon!\r\n                                    </p>\r\n                                </div>\r\n                            </ng-container>\r\n                            <!-- <div>\r\n                            <i class=\"pi pi-chevron-right text-2xl\"></i>\r\n                        </div> -->\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </ng-container>\r\n\r\n            <div *ngSwitchCase=\"'trial-received'\">\r\n                <div class=\" bg-gradient-trial-received overflow-hidden border-round-xl banner-height\">\r\n                    <div class=\"grid grid-nogutter h-full flex-column sm:flex-row sm:justify-content-around\">\r\n                        <div class=\"sm:col-6 md:col-offset-3 flex align-items-center justify-content-center\">\r\n                            <div class=\"px-1 py-1 md:px-1 lg:px-1\">\r\n                                <div class=\"text-700 text-center\">\r\n                                    <div class=\"text-white font-bold my-1\">\r\n\r\n                                        <img src=\"/assets/images/dashboard/banners/banner-check.svg\" class=\"w\"\r\n                                            width=\"28\">\r\n\r\n                                    </div>\r\n                                    <div class=\"text-white font-bold typography__heading--medium\">We have received your\r\n                                        Free\r\n                                        Trial request!</div>\r\n                                    <div class=\"text-white sm:mt-2 typography__heading--small lg:w-10 mx-auto\">Please\r\n                                        wait\r\n                                        while we find the most\r\n                                        suitable teacher for you.</div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"my-custom-location\" *ngIf=\"isEnrollNowVisible\"\r\n                            [ngTemplateOutlet]=\"enrollNowTemplate\"></div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div *ngSwitchCase=\"'trial-dismissed'\">\r\n                <div class=\" bg-gradient-trial-received overflow-hidden border-round-xl banner-height md:mt-0\">\r\n                    <div class=\"grid grid-nogutter h-full  w-full block sm:flex\">\r\n\r\n                        <div class=\"md:col-3\" *ngIf=\"!generalService.isNullishObject(classRoom)\">\r\n\r\n                            <div class=\" flex justify-content-center align-items-center flex-column sm:m-3\">\r\n                                <div class=\"avatar-circle hidden sm:block\">\r\n                                    <img [src]=\"generalService.getDomainFileNamePath(classRoom.teacher.avatarUrl)\"\r\n                                        (error)=\"generalService.setDefaultUserAvatar($event)\" width=\"130\"\r\n                                        alt=\"Classroom Image\">\r\n                                </div>\r\n                                <p class=\"text-white text-center typography__heading--small\">Your teacher <b>\r\n\r\n                                        {{generalService.getShortFullName(classRoom.teacher)}}\r\n                                    </b></p>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"sm:col-6 flex align-items-center justify-content-center\">\r\n                            <div class=\"px-1 py-1 md:px-1 lg:px-1\">\r\n\r\n                                <div class=\"text-700 text-center\">\r\n                                    <div class=\"text-white font-bold my-1\">\r\n                                    </div>\r\n                                    <div class=\"text-white font-md\">Your <b\r\n                                            class=\"capitalize\">{{classRoom.language}}</b> trial lesson has been\r\n                                        dismissed!\r\n                                        <br>Please get in touch to rearrange.\r\n                                    </div>\r\n                                    <button\r\n                                        (click)=\"generalService.openExternalLink('https://www.mylingotrip.com/contact')\"\r\n                                        pRipple pButton\r\n                                        class=\"mt-2 p-button-outlined p-button-outlined p-button-rounded button-outlined p-button-sm text-white\">\r\n                                        <div class=\"flex align-items-center gap-1\">\r\n                                            <span class=\"text-base s12-16\">Contact Us</span>\r\n                                        </div>\r\n                                    </button>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"my-custom-location\" *ngIf=\"isEnrollNowVisible\"\r\n                            [ngTemplateOutlet]=\"enrollNowTemplate\"></div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div *ngSwitchCase=\"'trial-assigned'\">\r\n                <div class=\" bg-gradient-trial-received overflow-hidden border-round-xl banner-height md:mt-0\">\r\n                    <div class=\"grid grid-nogutter h-full  w-full block sm:flex p-2 sm:p-0\">\r\n\r\n                        <div class=\"md:col-3\" *ngIf=\"!generalService.isNullishObject(classRoom)\">\r\n\r\n                            <div class=\" flex justify-content-center align-items-center flex-column sm:m-3\">\r\n                                <div class=\"avatar-circle hidden sm:block\">\r\n                                    <img [src]=\"generalService.getDomainFileNamePath(classRoom.teacher.avatarUrl)\"\r\n                                        (error)=\"generalService.setDefaultUserAvatar($event)\" width=\"130\"\r\n                                        alt=\"Classroom Image\">\r\n                                </div>\r\n                                <p class=\"text-white text-center typography__heading--small\">Your teacher <b>\r\n\r\n                                        {{generalService.getShortFullName(classRoom.teacher)}}\r\n                                    </b></p>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"sm:col-6 flex align-items-center justify-content-center\">\r\n                            <div class=\"px-1 py-1 md:px-1 lg:px-1\">\r\n\r\n                                <div class=\"text-700 text-center\">\r\n                                    <div class=\"text-white font-bold my-1\">\r\n                                    </div>\r\n                                    <ng-container\r\n                                        *ngIf=\"classroomService.isTrialClassroomWithCompletedFirstLesson(classRoom); else ongoingTrial\">\r\n                                        <div class=\"text-white text-xs sm:text-base\">Your <b\r\n                                                class=\"capitalize\">{{classRoom.language}}</b> trial has been completed!\r\n                                            How did you find it?</div>\r\n\r\n                                        <button pButton type=\"button\" [ngClass]=\"{'disabled-link':  hasRated}\"\r\n                                            [pTooltip]=\"hasRated ? 'Already rated' : ''\"\r\n                                            [routerLink]=\"classroomService.getClassroomRateUrl(classRoom)\"\r\n                                            class=\"sm:mt-2 p-button-outlined p-button-rounded button-outlined p-button-sm text-white\">\r\n\r\n                                            <div class=\"flex align-items-center gap-1\">\r\n                                                <span class=\"text-base s12-16\">{{hasRated ? 'Already rated' : 'Rate your\r\n                                                    experience'}}</span>\r\n                                                <img src=\"/assets/icons/lessons/rating-stars.svg\" height=\"16\"\r\n                                                    class=\"white-filter\" />\r\n                                            </div>\r\n\r\n                                        </button>\r\n\r\n                                    </ng-container>\r\n                                    <ng-template #ongoingTrial>\r\n                                        <div class=\"text-white font-md\">Your newly assigned teacher will be in touch\r\n                                            with you very soon!</div>\r\n                                    </ng-template>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"my-custom-location\" *ngIf=\"isEnrollNowVisible\"\r\n                            [ngTemplateOutlet]=\"enrollNowTemplate\"></div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div *ngSwitchCase=\"'trial-arranged'\">\r\n                <div class=\"bg-gradient-trial-arranged overflow-hidden border-round-xl banner-height\">\r\n                    <div class=\"grid grid-nogutter h-full justify-content-center\">\r\n\r\n                        <div class=\"col-12 md:col-3\">\r\n\r\n                            <div class=\" flex justify-content-center align-items-center flex-column m-0 sm:m-3\">\r\n                                <div class=\"avatar-circle hidden sm:block\">\r\n                                    <img [src]=\"generalService.getDomainFileNamePath(classRoom.teacher.avatarUrl)\"\r\n                                        (error)=\"generalService.setDefaultUserAvatar($event)\" width=\"130\"\r\n                                        alt=\"Classroom Image\">\r\n                                </div>\r\n                                <p class=\"text-white text-center typography__heading--small\">Your teacher <b>\r\n                                        {{generalService.getShortFullName(classRoom.teacher)}}</b></p>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"col-12 md:col-6 flex align-items-center justify-content-center\">\r\n                            <div class=\"px-1 py-1 md:px-1 lg:px-1\">\r\n\r\n                                <div class=\"text-700 text-center\">\r\n                                    <div class=\"text-white font-bold my-1\">\r\n\r\n                                    </div>\r\n                                    <div class=\"text-white  font-md\">Your trial lesson has been\r\n                                        arranged for</div>\r\n                                    <div class=\"text-white typography__heading--large font-bold mt-2\">\r\n                                        {{classRoom.lessons[0].startingDate | date: 'dd/MM/YY'}}\r\n                                        at 13:00\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"my-custom-location\" *ngIf=\"isEnrollNowVisible\"\r\n                            [ngTemplateOutlet]=\"enrollNowTemplate\"></div>\r\n\r\n                        <!-- <div *ngIf=\"isEnrollNowVisible\" [ngTemplateOutlet]=\"enrollNowTemplate\"></div> -->\r\n\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div *ngSwitchCase=\"'unreachable'\">\r\n                <div class=\"bg-gradient-unreachable overflow-hidden border-round-xl banner-height\">\r\n                    <div class=\"grid grid-nogutter h-full\">\r\n                        <div class=\"md:col-3 flex justify-content-center\">\r\n                            <div class=\" flex justify-content-center align-items-center flex-column m-3\">\r\n\r\n                                <img src=\"/assets/images/dashboard/banners/whiteoops.svg\" class=\"w\" width=\"64\">\r\n\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"md:col-6 flex align-items-center justify-content-center\">\r\n\r\n                            <div class=\"px-1 py-1 md:px-1 lg:px-1\">\r\n                                <div class=\"text-700 text-center\">\r\n                                    <div class=\"text-white font-bold typography__heading--medium\">Oops, we couldn’t\r\n                                        reach you </div>\r\n                                    <div class=\"text-white sm:mt-2\">Please fill in the form below and a member\r\n                                        of our team will be in touch with you.</div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"my-custom-location\" *ngIf=\"isEnrollNowVisible\"\r\n                            [ngTemplateOutlet]=\"enrollNowTemplate\"></div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            \r\n            <div *ngSwitchCase=\"'primary-offer'\">\r\n\r\n                <div class=\" bg-gradient-trial-received overflow-hidden border-round-xl banner-height md:mt-0\">\r\n                    <div class=\"grid grid-nogutter h-full  w-full block sm:flex justify-content-center p-2 sm:p-0\">\r\n\r\n                        <div class=\"md:col-2 hidden sm:flex justify-content-center align-items-center\">\r\n\r\n                            <div class=\" flex justify-content-center align-items-center flex-column sm:m-3\">\r\n                                <div class=\"text-white font-bold my-1 flex align-items-center justify-content-center\">\r\n\r\n                                    <div class=\"text-center w-full hidden sm:block\">\r\n                                        <div class=\"flex align-items-center justify-content-center\"\r\n                                            style=\" mix-blend-mode: luminosity;\">\r\n                                            <img src=\"/assets/icons/discount2.png\" class=\"w\"\r\n                                            width=\"64\">\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"sm:col-7 flex align-items-center justify-content-center\">\r\n                            <div class=\"px-1 py-1 md:px-1 lg:px-1\">\r\n\r\n                                <div class=\"text-700 text-center\">\r\n                                    <div class=\"text-white font-bold my-1\">\r\n                                    </div>\r\n                                    <ng-container>\r\n                                        <div class=\"text-white text-lg sm:text-2xl font-bold\">\r\n                                            \r\n                                            Limited-Time Offer\r\n                                        \r\n                                        </div>\r\n\r\n                                        <div class=\"text-white sm:mt-2 text-lg lg:w-full mx-0\">\r\n                                            Get 10% off our 50-hour packages until February 28. \r\n                                        </div>\r\n                                        <div class=\"text-white sm:mt-2 typography__heading--medium lg:w-full mx-auto\">\r\n                                            Contact us now to claim your discount!\r\n                                        </div>\r\n\r\n                                    </ng-container>\r\n\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"my-custom-location\" *ngIf=\"isEnrollNowVisible\"\r\n                            [ngTemplateOutlet]=\"contactUsTemplate\"></div>\r\n                    </div>\r\n                </div>\r\n\r\n            </div>\r\n\r\n            \r\n            <div *ngSwitchCase=\"'referral-promo'\">\r\n\r\n                <div class=\" bg-gradient-trial-received overflow-hidden border-round-xl banner-height md:mt-0\">\r\n                    <div class=\"grid grid-nogutter h-full  w-full block sm:flex justify-content-center p-2 sm:p-0\">\r\n\r\n                        <div class=\"md:col-2 hidden sm:flex justify-content-center align-items-center\">\r\n\r\n                            <div class=\" flex justify-content-center align-items-center flex-column sm:m-3\">\r\n                                <div class=\"text-white font-bold my-1 flex align-items-center justify-content-center\">\r\n\r\n                                    <div class=\"text-center w-full hidden sm:block\">\r\n                                        <div class=\"flex align-items-center justify-content-center\"\r\n                                            style=\" mix-blend-mode: luminosity;\">\r\n                                            <img src=\"/assets/icons/discount2.png\" class=\"w\"\r\n                                            width=\"64\">\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"sm:col-7 flex align-items-center justify-content-center\">\r\n                            <div class=\"px-1 py-1 md:px-1 lg:px-1\">\r\n\r\n                                <div class=\"text-700 text-center\">\r\n                                    <div class=\"text-white font-bold my-1\">\r\n                                    </div>\r\n                                    <ng-container>\r\n                                        <div class=\"text-white text-lg sm:text-2xl font-bold\">\r\n                                            \r\n                                            Invite a Friend & Get a Free Lesson! \r\n                                        \r\n                                        </div>\r\n\r\n                                        <div class=\"text-white sm:mt-2 text-lg lg:w-full mx-0\">\r\n                                            Share your referral code – when your friend signs up & purchases a lesson, both of you get one extra lesson for free!\r\n                                        </div>\r\n                                        <div class=\"text-white sm:mt-2 typography__heading--medium lg:w-full mx-auto\">\r\n                                            Contact us now to claim your discount!\r\n                                        </div>\r\n\r\n                                    </ng-container>\r\n\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"my-custom-location\" *ngIf=\"isEnrollNowVisible\"\r\n                            [ngTemplateOutlet]=\"getCodeTemplate\"></div>\r\n                    </div>\r\n                </div>\r\n\r\n            </div>\r\n\r\n            <div *ngSwitchCase=\"'aichat-promo'\">\r\n\r\n                <div class=\" bg-gradient-trial-received overflow-hidden border-round-xl banner-height md:mt-0\">\r\n                    <div class=\"grid grid-nogutter h-full  w-full block sm:flex p-2 sm:p-0\">\r\n\r\n                        <div class=\"md:col-3 flex justify-content-center align-items-center\">\r\n\r\n                            <div class=\" flex justify-content-center align-items-center flex-column sm:m-3\">\r\n                                <div class=\"text-white font-bold my-1 flex align-items-center justify-content-center\">\r\n\r\n                                    <div class=\"text-center w-full hidden sm:block\">\r\n                                        <div class=\"flex align-items-center justify-content-center\" #lottieEl\r\n                                            style=\" mix-blend-mode: luminosity;\"></div>\r\n                                    </div>\r\n\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"sm:col-6 flex align-items-center justify-content-center\">\r\n                            <div class=\"px-1 py-1 md:px-1 lg:px-1\">\r\n\r\n                                <div class=\"text-700 text-center\">\r\n                                    <div class=\"text-white font-bold my-1\">\r\n                                    </div>\r\n                                    <ng-container>\r\n                                        <div class=\"text-white text-md sm:text-base\">Accelerate Your\r\n                                            Language Journey with Our Interactive AI Chat</div>\r\n\r\n                                        <div class=\"text-white sm:mt-2 typography__heading--small lg:w-10 mx-auto\">\r\n                                            Available\r\n                                            with Flexible & Premium Packages!</div>\r\n\r\n                                    </ng-container>\r\n\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"my-custom-location\" *ngIf=\"isEnrollNowVisible\"\r\n                            [ngTemplateOutlet]=\"enrollAiChatTemplate\"></div>\r\n                    </div>\r\n                </div>\r\n\r\n            </div>\r\n\r\n\r\n\r\n\r\n            <div *ngSwitchCase=\"'social-promo'\">\r\n\r\n                <div class=\" bg-gradient-trial-received overflow-hidden border-round-xl banner-height md:mt-0\">\r\n                    <div class=\"grid grid-nogutter h-full  w-full block sm:flex p-2 sm:p-0\">\r\n\r\n\r\n\r\n                        <div class=\"sm:col-6 w-full mx-auto flex align-items-center justify-content-center\">\r\n                            <div class=\"px-1 py-1 md:px-1 lg:px-1\">\r\n\r\n                                <div class=\"text-700 text-center\">\r\n                                    <div class=\"text-white font-bold my-1\">\r\n                                    </div>\r\n                                    <ng-container>\r\n                                        <div class=\"text-white text-lg sm:text-lg font-bold\">\r\n                                            Stay Connected, Stay Inspired!\r\n                                        </div>\r\n\r\n                                        <div class=\"text-white sm:mt-2 typography__heading--medium mx-auto\">\r\n                                            Find us on Social Media and be part of a global community of language\r\n                                            learners.\r\n                                        </div>\r\n\r\n                                        <div class=\"mt-2 mx-auto\">\r\n                                            <ul class=\"wrapper m-0\">\r\n                                                <li class=\"icon facebook\">\r\n                                                    <a [href]=\"this.generalService.socialLinks.facebook\"\r\n                                                        target=\"_blank\">\r\n                                                        <img class=\"filter-white\"\r\n                                                            src=\"/assets/icons/social/facebook.svg\" width=\"16\" />\r\n                                                    </a>\r\n                                                </li>\r\n                                                <li class=\"icon twitter\">\r\n                                                    <a [href]=\"this.generalService.socialLinks.twitter\" target=\"_blank\">\r\n                                                         <i class=\"pi pi-twitter\"></i>\r\n                                                    </a>\r\n                                                </li>\r\n                                                <li class=\"icon linkedin\">\r\n                                                    <a [href]=\"this.generalService.socialLinks.linkedin\" target=\"_blank\">\r\n                                                        <img class=\"filter-white\"\r\n                                                            src=\"/assets/icons/social/linkedin.svg\" width=\"16\" />\r\n                                                    </a>\r\n                                                </li>\r\n                                                <li class=\"icon instagram\">\r\n                                                    <a [href]=\"this.generalService.socialLinks.instagram\" target=\"_blank\">\r\n                                                        <i class=\"pi pi-instagram\"></i>\r\n                                                    </a>\r\n                                                </li>\r\n                                                <li class=\"icon tiktok\">\r\n                                                    <a [href]=\"this.generalService.socialLinks.tiktok\" target=\"_blank\">\r\n                                                        <i class=\"pi pi-tiktok\"></i>\r\n                                                    </a>\r\n                                                </li>\r\n                                            </ul>\r\n                                        </div>\r\n                                    </ng-container>\r\n\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n\r\n                    </div>\r\n                </div>\r\n\r\n            </div>\r\n\r\n            <div *ngSwitchDefault>Default content</div>\r\n        </ng-container>\r\n    </div>\r\n</div>\r\n\r\n\r\n<ng-template #enrollNowTemplate>\r\n\r\n    <div class=\"md:col-3 flex align-items-center justify-content-center\">\r\n        <div class=\"flex flex-column align-items-center\">\r\n            <div class=\"text-white sm:mb-2 font-bold typography__heading--medium hidden sm:block\">Can't wait to start?\r\n            </div>\r\n            <p><button (click)=\"generalService.navigateToBuyPackage()\" type=\"button\"\r\n                    class=\"p-button-outlined p-button-rounded p-button-sm p-white\" icon=\"pi pi-arrow-right\"\r\n                    iconPos=\"right\" pButton pRipple label=\"Enroll now\"></button></p>\r\n        </div>\r\n    </div>\r\n</ng-template>\r\n\r\n<ng-template #enrollAiChatTemplate>\r\n\r\n    <div class=\"md:col-3 flex align-items-center justify-content-center\">\r\n        <div class=\"flex flex-column align-items-center\">\r\n            <div class=\"text-white sm:mb-2 font-bold typography__heading--medium hidden sm:block\">Can't wait to\r\n                start?\r\n            </div>\r\n            <p><button (click)=\"generalService.navigateToAiChat()\" type=\"button\"\r\n                    class=\"p-button-outlined p-button-rounded p-button-sm p-white\" icon=\"pi pi-arrow-right\"\r\n                    iconPos=\"right\" pButton pRipple label=\"Practice now\"></button></p>\r\n        </div>\r\n    </div>\r\n</ng-template>\r\n\r\n<ng-template #contactUsTemplate>\r\n\r\n    <div class=\"md:col-3 flex align-items-center justify-content-center\">\r\n        <div class=\"flex flex-column align-items-center\">\r\n            <div class=\"text-white sm:mb-2 font-bold typography__heading--medium hidden sm:block\">\r\n                Get the Offer\r\n            </div>\r\n            <p><button (click)=\"generalService.openExternalLink('https://www.mylingotrip.com/contact')\" type=\"button\"\r\n                    class=\"p-button-outlined p-button-rounded p-button-sm p-white\" icon=\"pi pi-arrow-right\"\r\n                    iconPos=\"right\" pButton pRipple label=\"Contact Us\"></button></p>\r\n        </div>\r\n    </div>\r\n</ng-template>\r\n\r\n\r\n\r\n<ng-template #getCodeTemplate>\r\n\r\n    <div class=\"md:col-3 flex align-items-center justify-content-center\">\r\n        <div class=\"flex flex-column align-items-center\">\r\n            <div class=\"text-white sm:mb-2 font-bold typography__heading--medium hidden sm:block\">\r\n                Get the Offer\r\n            </div>\r\n            <p><button (click)=\"generalService.openExternalLink('https://docs.google.com/forms/d/e/1FAIpQLSftI8I9-HuPRhYiT4RluOCTSr5VBwgJSQ1eHQxOrD64XNmkcQ/viewform?usp=header')\" type=\"button\"\r\n                    class=\"p-button-outlined p-button-rounded p-button-sm p-white\" icon=\"pi pi-arrow-right\"\r\n                    iconPos=\"right\" pButton pRipple label=\"Contact Us\"></button></p>\r\n        </div>\r\n    </div>\r\n</ng-template>"], "mappings": "AAAA,SAAyDA,MAAM,QAAkE,eAAe;AAQhJ,SAASC,OAAO,QAAQ,SAAS;AAEjC,SAASC,KAAK,QAAQ,MAAM;AAC5B,SAASC,GAAG,EAAEC,KAAK,QAAQ,gBAAgB;AAC3C,SAASC,WAAW,QAAQ,oCAAoC;AAChE,SAASC,YAAY,QAAQ,qCAAqC;;;;;;;;;;;;;;;;;;ICRtDC,EAAA,CAAAC,uBAAA,GAAwC;IAOpBD,EALhB,CAAAE,cAAA,cAAgE,cACrB,cAE+E,cACrF,YACuB;IAAAF,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAE,cAAA,eAC3B;IAAAF,EAAA,CAAAG,MAAA,GAAwB;IACtDH,EADsD,CAAAI,YAAA,EAAO,EACzD;IACiBJ,EAArB,CAAAE,cAAA,YAAqB,gBAAqC;IAACF,EAAA,CAAAK,SAAA,qBAAsB;IAC7EL,EAAA,CAAAI,YAAA,EAAO;IAACJ,EAAA,CAAAG,MAAA,IAAmD;IAO/EH,EAP+E,CAAAI,YAAA,EAAI,EACjE,EAIJ,EACJ,EACJ;;;;;IAVwCJ,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAO,kBAAA,KAAAC,MAAA,CAAAC,IAAA,CAAAC,SAAA,MAAwB;IAG1CV,EAAA,CAAAM,SAAA,GAAmD;IAAnDN,EAAA,CAAAW,kBAAA,MAAAH,MAAA,CAAAI,SAAA,OAAAJ,MAAA,CAAAK,0BAAA,GAAAC,IAAA,KAAmD;;;;;;IAoCnEd,EAAA,CAAAC,uBAAA,GAA+D;IAM3CD,EALhB,CAAAE,cAAA,cAAwE,cACxB,eACP,cAEF,cAGG;IADtBF,EAAA,CAAAe,UAAA,mBAAAC,oFAAAC,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA;MAAA,MAAAX,MAAA,GAAAR,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASb,MAAA,CAAAc,cAAA,CAAAC,oBAAA,CAAAN,MAAA,CAA2C;IAAA,EAAC;IAGjEjB,EAJQ,CAAAI,YAAA,EAE0B,EACxB,EACH;IAEHJ,EADJ,CAAAE,cAAA,cAAyC,cACD;IAAAF,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAAAJ,EAAA,CAAAE,cAAA,eACH;IAAAF,EAAA,CAAAG,MAAA,IAA4C;IAEvGH,EAFuG,CAAAI,YAAA,EAAO,EACpG,EACJ;IACNJ,EAAA,CAAAE,cAAA,aAAmC;IAC/BF,EAAA,CAAAG,MAAA,qFAIJ;IACJH,EADI,CAAAI,YAAA,EAAI,EACF;;;;;IAhBeJ,EAAA,CAAAM,SAAA,GAA+D;IAA/DN,EAAA,CAAAwB,UAAA,QAAAhB,MAAA,CAAAc,cAAA,CAAAG,qBAAA,CAAAjB,MAAA,CAAAkB,OAAA,CAAAC,SAAA,GAAA3B,EAAA,CAAA4B,aAAA,CAA+D;IAOrB5B,EAAA,CAAAM,SAAA,GAA4C;IAA5CN,EAAA,CAAA6B,iBAAA,CAAArB,MAAA,CAAAc,cAAA,CAAAQ,gBAAA,CAAAtB,MAAA,CAAAkB,OAAA,EAA4C;;;;;;IAtC/H1B,EAAA,CAAAC,uBAAA,GAAsD;IAOlCD,EALhB,CAAAE,cAAA,cAAyE,cAC9B,cAE0G,cAC5G,YAC6B;IAAAF,EAAA,CAAAG,MAAA,uBAEtD;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAGIJ,EAFR,CAAAE,cAAA,YAA2E,eACL,eAClC;IAAAF,EAAA,CAAAG,MAAA,4BAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvDJ,EAAA,CAAAE,cAAA,gBAA6C;IACzCF,EAAA,CAAAG,MAAA,IACJ;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAE,cAAA,aACsC;IADnCF,EAAA,CAAAe,UAAA,mBAAAgB,mEAAA;MAAA/B,EAAA,CAAAkB,aAAA,CAAAc,GAAA;MAAA,MAAAxB,MAAA,GAAAR,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASb,MAAA,CAAAyB,QAAA,CAASzB,MAAA,CAAA0B,WAAA,CAAAC,4BAAA,CAAA3B,MAAA,CAAAC,IAAA,CAAmD,CAAC;IAAA,EAAC;IAE9ET,EAD0C,CAAAI,YAAA,EAAI,EACvC;IACPJ,EAAA,CAAAE,cAAA,YAAM;IACFF,EAAA,CAAAG,MAAA,8GAEJ;IAERH,EAFQ,CAAAI,YAAA,EAAO,EACP,EACF;IACNJ,EAAA,CAAAoC,UAAA,KAAAC,8DAAA,4BAA+D;IA6B3ErC,EAFQ,CAAAI,YAAA,EAAM,EACJ,EACJ;;;;;IAxCsBJ,EAAA,CAAAM,SAAA,IACJ;IADIN,EAAA,CAAAO,kBAAA,MAAAC,MAAA,CAAA0B,WAAA,CAAAC,4BAAA,CAAA3B,MAAA,CAAAC,IAAA,OACJ;IAUGT,EAAA,CAAAM,SAAA,GAA8C;IAA9CN,EAAA,CAAAwB,UAAA,UAAAhB,MAAA,CAAAc,cAAA,CAAAgB,eAAA,CAAA9B,MAAA,CAAAkB,OAAA,EAA8C;;;;;IAiC7E1B,EAAA,CAAAC,uBAAA,GAAiD;IAO7BD,EALhB,CAAAE,cAAA,cAAyE,cAC9B,cAE2F,cACjG,YACuB;IAAAF,EAAA,CAAAG,MAAA,UAAG;IAAAH,EAAA,CAAAE,cAAA,eACjB;IAAAF,EAAA,CAAAG,MAAA,GAAwB;IACtDH,EADsD,CAAAI,YAAA,EAAO,EACzD;IACyBJ,EAA7B,CAAAE,cAAA,YAA6B,gBAA4B;IAAAF,EAAA,CAAAG,MAAA,8BAAsB;IAC/EH,EAD+E,CAAAI,YAAA,EAAO,EAClF;IACoCJ,EAAxC,CAAAE,cAAA,aAAwC,gBAA4B;IAACF,EAAA,CAAAK,SAAA,qBACzC;IAAAL,EAAA,CAAAI,YAAA,EAAO;IAACJ,EAAA,CAAAG,MAAA,IAAsB;IAC9DH,EAD8D,CAAAI,YAAA,EAAI,EAC5D;IACNJ,EAAA,CAAAC,uBAAA,IAAc;IAEsCD,EADhD,CAAAE,cAAA,eAAqE,eACrB,gBACP;IAE7BF,EAAA,CAAAK,SAAA,eAAgE;IAExEL,EADI,CAAAI,YAAA,EAAO,EACL;IAEFJ,EADJ,CAAAE,cAAA,eAA8B,eAC6B;IACnDF,EAAA,CAAAG,MAAA,+CAEJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,eAA6C;IACzCF,EAAA,CAAAG,MAAA,sEAEJ;IAERH,EAFQ,CAAAI,YAAA,EAAM,EACJ,EACJ;;IAOtBJ,EAFQ,CAAAI,YAAA,EAAM,EACJ,EACJ;;;;;IAhCwCJ,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAO,kBAAA,KAAAC,MAAA,CAAAC,IAAA,CAAAC,SAAA,MAAwB;IAKlBV,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAO,kBAAA,MAAAC,MAAA,CAAAC,IAAA,CAAA8B,QAAA,KAAsB;;;;;;IA4C9DvC,EAAA,CAAAC,uBAAA,GAA+D;IAM3CD,EALhB,CAAAE,cAAA,cAAwE,cACxB,eACP,cAEF,cAGG;IADtBF,EAAA,CAAAe,UAAA,mBAAAyB,oFAAAvB,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAuB,GAAA;MAAA,MAAAjC,MAAA,GAAAR,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASb,MAAA,CAAAc,cAAA,CAAAC,oBAAA,CAAAN,MAAA,CAA2C;IAAA,EAAC;IAGjEjB,EAJQ,CAAAI,YAAA,EAE0B,EACxB,EACH;IAEHJ,EADJ,CAAAE,cAAA,cAAyC,cACD;IAAAF,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAAAJ,EAAA,CAAAE,cAAA,eACH;IAAAF,EAAA,CAAAG,MAAA,IAA4C;IAEvGH,EAFuG,CAAAI,YAAA,EAAO,EACpG,EACJ;IACNJ,EAAA,CAAAE,cAAA,aAAmC;IAC/BF,EAAA,CAAAG,MAAA,qFAIJ;IACJH,EADI,CAAAI,YAAA,EAAI,EACF;;;;;IAhBeJ,EAAA,CAAAM,SAAA,GAA+D;IAA/DN,EAAA,CAAAwB,UAAA,QAAAhB,MAAA,CAAAc,cAAA,CAAAG,qBAAA,CAAAjB,MAAA,CAAAkB,OAAA,CAAAC,SAAA,GAAA3B,EAAA,CAAA4B,aAAA,CAA+D;IAOrB5B,EAAA,CAAAM,SAAA,GAA4C;IAA5CN,EAAA,CAAA6B,iBAAA,CAAArB,MAAA,CAAAc,cAAA,CAAAQ,gBAAA,CAAAtB,MAAA,CAAAkB,OAAA,EAA4C;;;;;IA3B/H1B,EAAA,CAAAC,uBAAA,GAAiD;IAO7BD,EALhB,CAAAE,cAAA,cAAyE,cAC9B,cAE0G,cAChH,YACG;IAAAF,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAE,cAAA,eACP;IAAAF,EAAA,CAAAG,MAAA,GAAwB;IACtDH,EADsD,CAAAI,YAAA,EAAO,EACzD;IACoCJ,EAAxC,CAAAE,cAAA,YAAwC,gBAA4B;IAACF,EAAA,CAAAK,SAAA,qBACzC;IAAAL,EAAA,CAAAI,YAAA,EAAO;IAAAJ,EAAA,CAAAG,MAAA,IACnC;IACJH,EADI,CAAAI,YAAA,EAAI,EACF;IACNJ,EAAA,CAAAoC,UAAA,KAAAM,8DAAA,4BAA+D;IA6B3E1C,EAFQ,CAAAI,YAAA,EAAM,EACJ,EACJ;;;;;IAnCwCJ,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAO,kBAAA,KAAAC,MAAA,CAAAC,IAAA,CAAAC,SAAA,MAAwB;IAGnBV,EAAA,CAAAM,SAAA,GACnC;IADmCN,EAAA,CAAAW,kBAAA,KAAAH,MAAA,CAAAI,SAAA,OAAAJ,MAAA,CAAAK,0BAAA,GAAAC,IAAA,MACnC;IAEWd,EAAA,CAAAM,SAAA,EAA8C;IAA9CN,EAAA,CAAAwB,UAAA,UAAAhB,MAAA,CAAAc,cAAA,CAAAgB,eAAA,CAAA9B,MAAA,CAAAkB,OAAA,EAA8C;;;;;IAsDjE1B,EAAA,CAAAK,SAAA,cACiD;;;;;IAA7CL,EAAA,CAAAwB,UAAA,qBAAAmB,oBAAA,CAAsC;;;;;IAjB9B3C,EANxB,CAAAE,cAAA,UAAsC,cACqD,cACM,cACA,cAC1C,cACD,cACS;IAEnCF,EAAA,CAAAK,SAAA,cACe;IAEnBL,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,cAA8D;IAAAF,EAAA,CAAAG,MAAA,gDAE5C;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACxBJ,EAAA,CAAAE,cAAA,eAA2E;IAAAF,EAAA,CAAAG,MAAA,oEAG9C;IAGzCH,EAHyC,CAAAI,YAAA,EAAM,EACjC,EACJ,EACJ;IACNJ,EAAA,CAAAoC,UAAA,KAAAQ,4CAAA,kBAC2C;IAGvD5C,EAFQ,CAAAI,YAAA,EAAM,EACJ,EACJ;;;;IAJuCJ,EAAA,CAAAM,SAAA,IAAwB;IAAxBN,EAAA,CAAAwB,UAAA,SAAAhB,MAAA,CAAAqC,kBAAA,CAAwB;;;;;;IAc7C7C,EAJZ,CAAAE,cAAA,cAAyE,cAEW,cACjC,cAGb;IADtBF,EAAA,CAAAe,UAAA,mBAAA+B,iEAAA7B,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAA6B,GAAA;MAAA,MAAAvC,MAAA,GAAAR,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASb,MAAA,CAAAc,cAAA,CAAAC,oBAAA,CAAAN,MAAA,CAA2C;IAAA,EAAC;IAE7DjB,EAHI,CAAAI,YAAA,EAE0B,EACxB;IACNJ,EAAA,CAAAE,cAAA,YAA6D;IAAAF,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAE,cAAA,QAAG;IAErEF,EAAA,CAAAG,MAAA,GACJ;IAEZH,EAFY,CAAAI,YAAA,EAAI,EAAI,EACV,EACJ;;;;IATWJ,EAAA,CAAAM,SAAA,GAAyE;IAAzEN,EAAA,CAAAwB,UAAA,QAAAhB,MAAA,CAAAc,cAAA,CAAAG,qBAAA,CAAAjB,MAAA,CAAAwC,SAAA,CAAAtB,OAAA,CAAAC,SAAA,GAAA3B,EAAA,CAAA4B,aAAA,CAAyE;IAM1E5B,EAAA,CAAAM,SAAA,GACJ;IADIN,EAAA,CAAAO,kBAAA,MAAAC,MAAA,CAAAc,cAAA,CAAAQ,gBAAA,CAAAtB,MAAA,CAAAwC,SAAA,CAAAtB,OAAA,OACJ;;;;;IA2BZ1B,EAAA,CAAAK,SAAA,cACiD;;;;;IAA7CL,EAAA,CAAAwB,UAAA,qBAAAmB,oBAAA,CAAsC;;;;;;IAzC9C3C,EAFR,CAAAE,cAAA,UAAuC,cAC4D,cAC9B;IAEzDF,EAAA,CAAAoC,UAAA,IAAAa,2CAAA,kBAAyE;IAkBjEjD,EAHR,CAAAE,cAAA,cAAqE,cAC1B,cAED;IAC9BF,EAAA,CAAAK,SAAA,cACM;IACNL,EAAA,CAAAE,cAAA,cAAgC;IAAAF,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAE,cAAA,aACV;IAAAF,EAAA,CAAAG,MAAA,IAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAACJ,EAAA,CAAAG,MAAA,0CAElD;IAAAH,EAAA,CAAAK,SAAA,UAAI;IAAAL,EAAA,CAAAG,MAAA,0CACR;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,kBAG6G;IAFzGF,EAAA,CAAAe,UAAA,mBAAAmC,+DAAA;MAAAlD,EAAA,CAAAkB,aAAA,CAAAiC,GAAA;MAAA,MAAA3C,MAAA,GAAAR,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASb,MAAA,CAAAc,cAAA,CAAA8B,gBAAA,CAAgC,qCAAqC,CAAC;IAAA,EAAC;IAI5EpD,EADJ,CAAAE,cAAA,eAA2C,gBACR;IAAAF,EAAA,CAAAG,MAAA,kBAAU;IAK7DH,EAL6D,CAAAI,YAAA,EAAO,EAC9C,EACD,EACP,EACJ,EACJ;IAENJ,EAAA,CAAAoC,UAAA,KAAAiB,4CAAA,kBAC2C;IAGvDrD,EAFQ,CAAAI,YAAA,EAAM,EACJ,EACJ;;;;IA1C6BJ,EAAA,CAAAM,SAAA,GAAgD;IAAhDN,EAAA,CAAAwB,UAAA,UAAAhB,MAAA,CAAAc,cAAA,CAAAgB,eAAA,CAAA9B,MAAA,CAAAwC,SAAA,EAAgD;IAsBhChD,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAA6B,iBAAA,CAAArB,MAAA,CAAAwC,SAAA,CAAAM,QAAA,CAAsB;IAgB5BtD,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAwB,UAAA,SAAAhB,MAAA,CAAAqC,kBAAA,CAAwB;;;;;;IAc7C7C,EAJZ,CAAAE,cAAA,cAAyE,cAEW,cACjC,cAGb;IADtBF,EAAA,CAAAe,UAAA,mBAAAwC,iEAAAtC,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAsC,GAAA;MAAA,MAAAhD,MAAA,GAAAR,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASb,MAAA,CAAAc,cAAA,CAAAC,oBAAA,CAAAN,MAAA,CAA2C;IAAA,EAAC;IAE7DjB,EAHI,CAAAI,YAAA,EAE0B,EACxB;IACNJ,EAAA,CAAAE,cAAA,YAA6D;IAAAF,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAE,cAAA,QAAG;IAErEF,EAAA,CAAAG,MAAA,GACJ;IAEZH,EAFY,CAAAI,YAAA,EAAI,EAAI,EACV,EACJ;;;;IATWJ,EAAA,CAAAM,SAAA,GAAyE;IAAzEN,EAAA,CAAAwB,UAAA,QAAAhB,MAAA,CAAAc,cAAA,CAAAG,qBAAA,CAAAjB,MAAA,CAAAwC,SAAA,CAAAtB,OAAA,CAAAC,SAAA,GAAA3B,EAAA,CAAA4B,aAAA,CAAyE;IAM1E5B,EAAA,CAAAM,SAAA,GACJ;IADIN,EAAA,CAAAO,kBAAA,MAAAC,MAAA,CAAAc,cAAA,CAAAQ,gBAAA,CAAAtB,MAAA,CAAAwC,SAAA,CAAAtB,OAAA,OACJ;;;;;IAUA1B,EAAA,CAAAC,uBAAA,GACoG;IAChGD,EAAA,CAAAE,cAAA,cAA6C;IAAAF,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAE,cAAA,YACvB;IAAAF,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAACJ,EAAA,CAAAG,MAAA,sDAC9B;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAQtBJ,EANR,CAAAE,cAAA,iBAG8F,cAE/C,eACR;IAAAF,EAAA,CAAAG,MAAA,GACd;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACxBJ,EAAA,CAAAK,SAAA,eAC2B;IAGnCL,EAFI,CAAAI,YAAA,EAAM,EAED;;;;;IAfkBJ,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAA6B,iBAAA,CAAArB,MAAA,CAAAwC,SAAA,CAAAM,QAAA,CAAsB;IAGnBtD,EAAA,CAAAM,SAAA,GAAwC;IAElEN,EAF0B,CAAAwB,UAAA,YAAAxB,EAAA,CAAAyD,eAAA,IAAAC,GAAA,EAAAlD,MAAA,CAAAmD,QAAA,EAAwC,aAAAnD,MAAA,CAAAmD,QAAA,wBACtB,eAAAnD,MAAA,CAAAoD,gBAAA,CAAAC,mBAAA,CAAArD,MAAA,CAAAwC,SAAA,EACkB;IAI3BhD,EAAA,CAAAM,SAAA,GACd;IADcN,EAAA,CAAA6B,iBAAA,CAAArB,MAAA,CAAAmD,QAAA,4CACd;;;;;IASzB3D,EAAA,CAAAE,cAAA,cAAgC;IAAAF,EAAA,CAAAG,MAAA,uEACT;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;IAM7CJ,EAAA,CAAAK,SAAA,cACiD;;;;;IAA7CL,EAAA,CAAAwB,UAAA,qBAAAmB,oBAAA,CAAsC;;;;;IArD9C3C,EAFR,CAAAE,cAAA,UAAsC,cAC6D,cACnB;IAEpEF,EAAA,CAAAoC,UAAA,IAAA0B,2CAAA,kBAAyE;IAkBjE9D,EAHR,CAAAE,cAAA,cAAqE,cAC1B,cAED;IAC9BF,EAAA,CAAAK,SAAA,cACM;IAsBNL,EArBA,CAAAoC,UAAA,IAAA2B,oDAAA,4BACoG,IAAAC,mDAAA,gCAAAhE,EAAA,CAAAiE,sBAAA,CAoBzE;IAMvCjE,EAFQ,CAAAI,YAAA,EAAM,EACJ,EACJ;IAENJ,EAAA,CAAAoC,UAAA,KAAA8B,4CAAA,kBAC2C;IAGvDlE,EAFQ,CAAAI,YAAA,EAAM,EACJ,EACJ;;;;;IAtD6BJ,EAAA,CAAAM,SAAA,GAAgD;IAAhDN,EAAA,CAAAwB,UAAA,UAAAhB,MAAA,CAAAc,cAAA,CAAAgB,eAAA,CAAA9B,MAAA,CAAAwC,SAAA,EAAgD;IAsBtDhD,EAAA,CAAAM,SAAA,GAA4E;IAAAN,EAA5E,CAAAwB,UAAA,SAAAhB,MAAA,CAAAoD,gBAAA,CAAAO,wCAAA,CAAA3D,MAAA,CAAAwC,SAAA,EAA4E,aAAAoB,eAAA,CAAiB;IA4B7EpE,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAwB,UAAA,SAAAhB,MAAA,CAAAqC,kBAAA,CAAwB;;;;;IAuCzD7C,EAAA,CAAAK,SAAA,cACiD;;;;;IAA7CL,EAAA,CAAAwB,UAAA,qBAAAmB,oBAAA,CAAsC;;;;;;IA1B9B3C,EARxB,CAAAE,cAAA,UAAsC,cACoD,cACpB,cAE7B,cAE2D,cACrC,cAGb;IADtBF,EAAA,CAAAe,UAAA,mBAAAsD,4DAAApD,MAAA;MAAAjB,EAAA,CAAAkB,aAAA,CAAAoD,IAAA;MAAA,MAAA9D,MAAA,GAAAR,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASb,MAAA,CAAAc,cAAA,CAAAC,oBAAA,CAAAN,MAAA,CAA2C;IAAA,EAAC;IAE7DjB,EAHI,CAAAI,YAAA,EAE0B,EACxB;IACNJ,EAAA,CAAAE,cAAA,YAA6D;IAAAF,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAE,cAAA,QAAG;IACrEF,EAAA,CAAAG,MAAA,IAAsD;IAEtEH,EAFsE,CAAAI,YAAA,EAAI,EAAI,EACpE,EACJ;IAKEJ,EAHR,CAAAE,cAAA,eAA4E,eACjC,eAED;IAC9BF,EAAA,CAAAK,SAAA,eAEM;IACNL,EAAA,CAAAE,cAAA,eAAiC;IAAAF,EAAA,CAAAG,MAAA,+CACjB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACtBJ,EAAA,CAAAE,cAAA,eAAkE;IAC9DF,EAAA,CAAAG,MAAA,IAEJ;;IAGZH,EAHY,CAAAI,YAAA,EAAM,EACJ,EACJ,EACJ;IACNJ,EAAA,CAAAoC,UAAA,KAAAmC,6CAAA,kBAC2C;IAMvDvE,EAFQ,CAAAI,YAAA,EAAM,EACJ,EACJ;;;;IAhCuBJ,EAAA,CAAAM,SAAA,GAAyE;IAAzEN,EAAA,CAAAwB,UAAA,QAAAhB,MAAA,CAAAc,cAAA,CAAAG,qBAAA,CAAAjB,MAAA,CAAAwC,SAAA,CAAAtB,OAAA,CAAAC,SAAA,GAAA3B,EAAA,CAAA4B,aAAA,CAAyE;IAK1E5B,EAAA,CAAAM,SAAA,GAAsD;IAAtDN,EAAA,CAAAO,kBAAA,MAAAC,MAAA,CAAAc,cAAA,CAAAQ,gBAAA,CAAAtB,MAAA,CAAAwC,SAAA,CAAAtB,OAAA,MAAsD;IActD1B,EAAA,CAAAM,SAAA,GAEJ;IAFIN,EAAA,CAAAO,kBAAA,MAAAP,EAAA,CAAAwE,WAAA,QAAAhE,MAAA,CAAAwC,SAAA,CAAAyB,OAAA,IAAAC,YAAA,4BAEJ;IAIqB1E,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAwB,UAAA,SAAAhB,MAAA,CAAAqC,kBAAA,CAAwB;;;;;IA8BzD7C,EAAA,CAAAK,SAAA,cACiD;;;;;IAA7CL,EAAA,CAAAwB,UAAA,qBAAAmB,oBAAA,CAAsC;;;;;IAlBtC3C,EAJhB,CAAAE,cAAA,UAAmC,cACoD,cACxC,cACe,cAC+B;IAEzEF,EAAA,CAAAK,SAAA,cAA+E;IAGvFL,EADI,CAAAI,YAAA,EAAM,EACJ;IAKMJ,EAJZ,CAAAE,cAAA,cAAqE,cAE1B,cACD,cACgC;IAAAF,EAAA,CAAAG,MAAA,yCAChD;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACpBJ,EAAA,CAAAE,cAAA,eAAgC;IAAAF,EAAA,CAAAG,MAAA,yFACU;IAGtDH,EAHsD,CAAAI,YAAA,EAAM,EAC9C,EACJ,EACJ;IACNJ,EAAA,CAAAoC,UAAA,KAAAuC,6CAAA,kBAC2C;IAGvD3E,EAFQ,CAAAI,YAAA,EAAM,EACJ,EACJ;;;;IAJuCJ,EAAA,CAAAM,SAAA,IAAwB;IAAxBN,EAAA,CAAAwB,UAAA,SAAAhB,MAAA,CAAAqC,kBAAA,CAAwB;;;;;IAuDzD7C,EAAA,CAAAK,SAAA,cACiD;;;;;IAA7CL,EAAA,CAAAwB,UAAA,qBAAAoD,qBAAA,CAAsC;;;;;IAtC1B5E,EAX5B,CAAAE,cAAA,UAAqC,cAE8D,cACI,cAEZ,cAEK,cACU,cAElC,cAEH;IACrCF,EAAA,CAAAK,SAAA,cACW;IAM/BL,EALgB,CAAAI,YAAA,EAAM,EACJ,EAEJ,EACJ,EACJ;IAKEJ,EAHR,CAAAE,cAAA,cAAqE,eAC1B,eAED;IAC9BF,EAAA,CAAAK,SAAA,eACM;IACNL,EAAA,CAAAC,uBAAA,IAAc;IACVD,EAAA,CAAAE,cAAA,eAAsD;IAElDF,EAAA,CAAAG,MAAA,4BAEJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAE,cAAA,eAAuD;IACnDF,EAAA,CAAAG,MAAA,6DACJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,eAA8E;IAC1EF,EAAA,CAAAG,MAAA,gDACJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;;IAMtBJ,EAFQ,CAAAI,YAAA,EAAM,EACJ,EACJ;IAENJ,EAAA,CAAAoC,UAAA,KAAAyC,6CAAA,kBAC2C;IAIvD7E,EAHQ,CAAAI,YAAA,EAAM,EACJ,EAEJ;;;;IALuCJ,EAAA,CAAAM,SAAA,IAAwB;IAAxBN,EAAA,CAAAwB,UAAA,SAAAhB,MAAA,CAAAqC,kBAAA,CAAwB;;;;;IAwDzD7C,EAAA,CAAAK,SAAA,cAC+C;;;;;IAA3CL,EAAA,CAAAwB,UAAA,qBAAAsD,mBAAA,CAAoC;;;;;IAtCxB9E,EAX5B,CAAAE,cAAA,UAAsC,cAE6D,cACI,cAEZ,cAEK,cACU,cAElC,cAEH;IACrCF,EAAA,CAAAK,SAAA,cACW;IAM/BL,EALgB,CAAAI,YAAA,EAAM,EACJ,EAEJ,EACJ,EACJ;IAKEJ,EAHR,CAAAE,cAAA,cAAqE,eAC1B,eAED;IAC9BF,EAAA,CAAAK,SAAA,eACM;IACNL,EAAA,CAAAC,uBAAA,IAAc;IACVD,EAAA,CAAAE,cAAA,eAAsD;IAElDF,EAAA,CAAAG,MAAA,8CAEJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAE,cAAA,eAAuD;IACnDF,EAAA,CAAAG,MAAA,oIACJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,eAA8E;IAC1EF,EAAA,CAAAG,MAAA,gDACJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;;IAMtBJ,EAFQ,CAAAI,YAAA,EAAM,EACJ,EACJ;IAENJ,EAAA,CAAAoC,UAAA,KAAA2C,6CAAA,kBACyC;IAIrD/E,EAHQ,CAAAI,YAAA,EAAM,EACJ,EAEJ;;;;IALuCJ,EAAA,CAAAM,SAAA,IAAwB;IAAxBN,EAAA,CAAAwB,UAAA,SAAAhB,MAAA,CAAAqC,kBAAA,CAAwB;;;;;IA8CzD7C,EAAA,CAAAK,SAAA,cACoD;;;;;IAAhDL,EAAA,CAAAwB,UAAA,qBAAAwD,wBAAA,CAAyC;;;;;IA9BjChF,EAVxB,CAAAE,cAAA,UAAoC,cAE+D,cACnB,eAEC,cAEe,cACU,cAElC;IAC5CF,EAAA,CAAAK,SAAA,iBAC+C;IAK/DL,EAJY,CAAAI,YAAA,EAAM,EAEJ,EACJ,EACJ;IAKEJ,EAHR,CAAAE,cAAA,cAAqE,eAC1B,eAED;IAC9BF,EAAA,CAAAK,SAAA,eACM;IACNL,EAAA,CAAAC,uBAAA,IAAc;IACVD,EAAA,CAAAE,cAAA,gBAA6C;IAAAF,EAAA,CAAAG,MAAA,qEACI;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAEvDJ,EAAA,CAAAE,cAAA,eAA2E;IACvEF,EAAA,CAAAG,MAAA,oDACiC;IAAAH,EAAA,CAAAI,YAAA,EAAM;;IAM3DJ,EAFQ,CAAAI,YAAA,EAAM,EACJ,EACJ;IAENJ,EAAA,CAAAoC,UAAA,KAAA6C,6CAAA,kBAC8C;IAI1DjF,EAHQ,CAAAI,YAAA,EAAM,EACJ,EAEJ;;;;IALuCJ,EAAA,CAAAM,SAAA,IAAwB;IAAxBN,EAAA,CAAAwB,UAAA,SAAAhB,MAAA,CAAAqC,kBAAA,CAAwB;;;;;IAoBjD7C,EAVpB,CAAAE,cAAA,UAAoC,cAE+D,cACnB,eAIgB,cACzC,cAED;IAC9BF,EAAA,CAAAK,SAAA,cACM;IACNL,EAAA,CAAAC,uBAAA,GAAc;IACVD,EAAA,CAAAE,cAAA,eAAqD;IACjDF,EAAA,CAAAG,MAAA,uCACJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAE,cAAA,gBAAoE;IAChEF,EAAA,CAAAG,MAAA,yFAEJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAKMJ,EAHZ,CAAAE,cAAA,gBAA0B,eACE,eACM,cAEF;IAChBF,EAAA,CAAAK,SAAA,gBACyD;IAEjEL,EADI,CAAAI,YAAA,EAAI,EACH;IAEDJ,EADJ,CAAAE,cAAA,eAAyB,cAC+C;IAC/DF,EAAA,CAAAK,SAAA,cAA6B;IAEtCL,EADI,CAAAI,YAAA,EAAI,EACH;IAEDJ,EADJ,CAAAE,cAAA,eAA0B,cAC+C;IACjEF,EAAA,CAAAK,SAAA,gBACyD;IAEjEL,EADI,CAAAI,YAAA,EAAI,EACH;IAEDJ,EADJ,CAAAE,cAAA,eAA2B,cAC+C;IAClEF,EAAA,CAAAK,SAAA,cAA+B;IAEvCL,EADI,CAAAI,YAAA,EAAI,EACH;IAEDJ,EADJ,CAAAE,cAAA,eAAwB,cAC+C;IAC/DF,EAAA,CAAAK,SAAA,cAA4B;IAI5CL,EAHY,CAAAI,YAAA,EAAI,EACH,EACJ,EACH;;IAUlCJ,EAPoB,CAAAI,YAAA,EAAM,EACJ,EACJ,EAEJ,EACJ,EAEJ;;;;IAtCqCJ,EAAA,CAAAM,SAAA,IAAiD;IAAjDN,EAAA,CAAAwB,UAAA,SAAAhB,MAAA,CAAAc,cAAA,CAAA4D,WAAA,CAAAC,QAAA,EAAAnF,EAAA,CAAA4B,aAAA,CAAiD;IAOjD5B,EAAA,CAAAM,SAAA,GAAgD;IAAhDN,EAAA,CAAAwB,UAAA,SAAAhB,MAAA,CAAAc,cAAA,CAAA4D,WAAA,CAAAE,OAAA,EAAApF,EAAA,CAAA4B,aAAA,CAAgD;IAKhD5B,EAAA,CAAAM,SAAA,GAAiD;IAAjDN,EAAA,CAAAwB,UAAA,SAAAhB,MAAA,CAAAc,cAAA,CAAA4D,WAAA,CAAAG,QAAA,EAAArF,EAAA,CAAA4B,aAAA,CAAiD;IAMjD5B,EAAA,CAAAM,SAAA,GAAkD;IAAlDN,EAAA,CAAAwB,UAAA,SAAAhB,MAAA,CAAAc,cAAA,CAAA4D,WAAA,CAAAI,SAAA,EAAAtF,EAAA,CAAA4B,aAAA,CAAkD;IAKlD5B,EAAA,CAAAM,SAAA,GAA+C;IAA/CN,EAAA,CAAAwB,UAAA,SAAAhB,MAAA,CAAAc,cAAA,CAAA4D,WAAA,CAAAK,MAAA,EAAAvF,EAAA,CAAA4B,aAAA,CAA+C;;;;;IAiB1F5B,EAAA,CAAAE,cAAA,UAAsB;IAAAF,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;;IAU3CJ,EAFR,CAAAE,cAAA,eAAqE,eAChB,eACyC;IAAAF,EAAA,CAAAG,MAAA,4BACtF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACHJ,EAAH,CAAAE,cAAA,QAAG,kBAEwD;IAFhDF,EAAA,CAAAe,UAAA,mBAAAyE,uEAAA;MAAAxF,EAAA,CAAAkB,aAAA,CAAAuE,IAAA;MAAA,MAAAjF,MAAA,GAAAR,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASb,MAAA,CAAAc,cAAA,CAAAoE,oBAAA,EAAqC;IAAA,EAAC;IAIlE1F,EAFmE,CAAAI,YAAA,EAAS,EAAI,EACtE,EACJ;;;;;;IAOEJ,EAFR,CAAAE,cAAA,eAAqE,eAChB,eACyC;IAAAF,EAAA,CAAAG,MAAA,4BAEtF;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACHJ,EAAH,CAAAE,cAAA,QAAG,kBAE0D;IAFlDF,EAAA,CAAAe,UAAA,mBAAA4E,uEAAA;MAAA3F,EAAA,CAAAkB,aAAA,CAAA0E,IAAA;MAAA,MAAApF,MAAA,GAAAR,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASb,MAAA,CAAAc,cAAA,CAAAuE,gBAAA,EAAiC;IAAA,EAAC;IAI9D7F,EAFqE,CAAAI,YAAA,EAAS,EAAI,EACxE,EACJ;;;;;;IAOEJ,EAFR,CAAAE,cAAA,eAAqE,eAChB,eACyC;IAClFF,EAAA,CAAAG,MAAA,sBACJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACHJ,EAAH,CAAAE,cAAA,QAAG,kBAEwD;IAFhDF,EAAA,CAAAe,UAAA,mBAAA+E,uEAAA;MAAA9F,EAAA,CAAAkB,aAAA,CAAA6E,IAAA;MAAA,MAAAvF,MAAA,GAAAR,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASb,MAAA,CAAAc,cAAA,CAAA8B,gBAAA,CAAgC,qCAAqC,CAAC;IAAA,EAAC;IAInGpD,EAFmE,CAAAI,YAAA,EAAS,EAAI,EACtE,EACJ;;;;;;IASEJ,EAFR,CAAAE,cAAA,eAAqE,eAChB,eACyC;IAClFF,EAAA,CAAAG,MAAA,sBACJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACHJ,EAAH,CAAAE,cAAA,QAAG,kBAEwD;IAFhDF,EAAA,CAAAe,UAAA,mBAAAiF,uEAAA;MAAAhG,EAAA,CAAAkB,aAAA,CAAA+E,IAAA;MAAA,MAAAzF,MAAA,GAAAR,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASb,MAAA,CAAAc,cAAA,CAAA8B,gBAAA,CAAgC,gHAAgH,CAAC;IAAA,EAAC;IAI9KpD,EAFmE,CAAAI,YAAA,EAAS,EAAI,EACtE,EACJ;;;ADroBV,OAAM,MAAO8F,sBAAsB;EAmBjCC,YACS7E,cAA8B,EAC9BsC,gBAAkC,EACjCwC,WAAwB,EACzBC,sBAA8C,EAC7CC,IAAY,EACZC,QAAmB;IALpB,KAAAjF,cAAc,GAAdA,cAAc;IACd,KAAAsC,gBAAgB,GAAhBA,gBAAgB;IACf,KAAAwC,WAAW,GAAXA,WAAW;IACZ,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACrB,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,QAAQ,GAARA,QAAQ;IAxBV,KAAAC,IAAI,GAAG,IAAI9G,OAAO,EAAE;IACI,KAAA+G,UAAU,GAAqB,EAAsB;IACrF,KAAA5D,kBAAkB,GAAG,IAAI;IAChB,KAAA6D,eAAe,GAAG,KAAK;IACvB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,wBAAwB,GAAG,KAAK;IAChC,KAAAC,UAAU,GAAe,SAAS;IAClC,KAAApF,OAAO,GAAO,EAAE;IAChB,KAAAsB,SAAS,GAAc,EAAe;IAC/C,KAAApC,SAAS,GAAG,EAAE;IAC2B,KAAAmG,SAAS,GAAe,EAAgB;IAC1D,KAAAC,QAAQ,GAAe,EAAgB;IAC9D,KAAA9E,WAAW,GAAGzC,MAAM,CAACK,WAAW,CAAC;IACjC,KAAAmH,YAAY,GAAGxH,MAAM,CAACM,YAAY,CAAC;IAC5B,KAAAmH,SAAS,GAAe,IAAI,CAAC5F,cAAc,CAAC6F,YAAY,EAAE;IAC1D,KAAA1G,IAAI,GAAS,EAAU;IAC9B,KAAAkD,QAAQ,GAAG,KAAK;EAQZ;EAEJyD,QAAQA,CAAA;IACN,IAAI,CAAC3G,IAAI,GAAG,IAAI,CAAC2F,WAAW,CAACiB,eAAe,EAAE;IAC9C;IACA;IACA,MAAMC,MAAM,GAAG,IAAI,CAACJ,SAAS,CAACK,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACC,GAAG,CAACC,QAAQ,CAAC,IAAI,CAACjH,IAAI,CAAC8B,QAAS,CAAC,CAAC;IAG9E,MAAMoF,KAAK,GAAG,IAAI,CAACrG,cAAc,CAACsG,wBAAwB,CAAC,IAAI,CAACnH,IAAI,CAAC8B,QAAS,CAAC;IAC/E;IACA,IAAI,CAAC3B,SAAS,GAAG,IAAI,CAACU,cAAc,CAACuG,4BAA4B,CAACF,KAAK,EAAE,IAAI,CAAClH,IAAI,CAAC8B,QAAS,CAAC;IAG7F,IAAK,IAAI,CAACuE,UAAU,KAAK,SAAS,IAAI,IAAI,CAACA,UAAU,KAAK,kBAAkB,EAAG;MAC7E,IAAI,CAACgB,mBAAmB,CAACR,MAAO,CAAC;IACnC;IAEA,IAAI,IAAI,CAACR,UAAU,KAAK,gBAAgB,IAAI,CAAC,IAAI,CAACxF,cAAc,CAACyG,aAAa,CAAC,IAAI,CAAC/E,SAAS,CAAC,EAAE;MAC9F,IAAI,CAACwD,IAAI,CAACwB,GAAG,CAAC,IAAI,CAAC3B,sBAAsB,CAAC4B,qBAAqB,CAAC,CAAC,IAAI,CAACjF,SAAS,CAACkF,EAAE,CAAC,CAACC,SAAS,CAAEC,GAAG,IAAI;QACpG,IAAIA,GAAG,EAAE;UACP,IAAI,CAACzE,QAAQ,GAAG,CAAC,IAAI,CAACrC,cAAc,CAACyG,aAAa,CAACK,GAAG,CAAC;QACzD;MACF,CAAC,CAAC,CAAC;IACL;EACF;EAEAC,eAAeA,CAAA;IACb,IAAI,IAAI,CAACtB,SAAS,IAAI,IAAI,CAACA,SAAS,CAACuB,aAAa,IAAI,IAAI,CAACC,eAAe,EAAE,EAAE;MAE5E,IAAI,CAACjC,IAAI,CAACkC,iBAAiB,CAAC,MAAK;QAC/BC,WAAW,CAAC,MAAK;UACf,IAAI,IAAI,CAAC3B,UAAU,KAAK,SAAS,IAAI,IAAI,CAACA,UAAU,KAAK,kBAAkB,EAAE;YAC3E,IAAI,CAACP,QAAQ,CAACmC,WAAW,CAAC,IAAI,CAAC3B,SAAS,CAACuB,aAAa,EAAE,aAAa,EACnE,IAAI,CAAChH,cAAc,CAACqH,gBAAgB,CAAC,IAAI,CAAClI,IAAI,CAAC8B,QAAS,CAAC,CAAC;UAC9D;QACF,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,CAAC;IACJ;IACA,IAAI,IAAI,CAACuE,UAAU,KAAK,cAAc,EAAE;MACtC,IAAI,CAAC8B,eAAe,CAAC,IAAI,CAAC5B,QAAQ,CAAC;IACrC;EACF;EAEA6B,WAAWA,CAAA;IACT,IAAI,CAACrC,IAAI,CAACsC,WAAW,EAAE;EACzB;EAEAC,YAAYA,CAAA;IACV,IAAI,CAAClG,kBAAkB,GAAG,IAAI;EAChC;EAEA0F,eAAeA,CAAA;IACb,MAAMhG,QAAQ,GAAG,IAAI,CAAC9B,IAAI,CAAC8B,QAAQ;IACnC,IAAIyG,cAAc,GAAG,KAAK;IAE1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1G,QAAS,CAAC2G,MAAM,EAAED,CAAC,EAAE,EAAE;MACzC,IAAI,CAACE,KAAK,CAACC,QAAQ,CAAC7G,QAAS,CAAC0G,CAAC,CAAC,CAAC,CAAC,EAAE;QAClCD,cAAc,GAAG,IAAI;QACrB;MACF;IACF;IAEA,OAAOA,cAAc;EACvB;EAEAnI,0BAA0BA,CAAA;IACxB,OAAQ,IAAI,CAACqG,SAAS,CAACK,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACC,GAAG,CAACC,QAAQ,CAAC,IAAI,CAACjH,IAAI,CAAC8B,QAAS,CAAC,CAAC;EACzE;EAEAuF,mBAAmBA,CAACR,MAAgB;IAClC,IAAI,CAACd,IAAI,CAAC6C,IAAI,GAAG1J,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAC5B2J,IAAI,CACH1J,GAAG,CAAC,MAAM,IAAI2J,IAAI,EAAE,CAAC,EACrB1J,KAAK,EAAE,CACR,CACAsI,SAAS,CAACqB,IAAI,IAAG;MAChB,MAAM7B,KAAK,GAAG,IAAI,CAACrG,cAAc,CAACsG,wBAAwB,CAAC,IAAI,CAACnH,IAAI,CAAC8B,QAAS,CAAC;MAC/E,IAAI,CAAC3B,SAAS,GAAG,IAAI,CAACU,cAAc,CAACuG,4BAA4B,CAACF,KAAK,EAAE,IAAI,CAAClH,IAAI,CAAC8B,QAAS,CAAC;MAC7F;IACF,CAAC,CAAC;EACN;EAEA;EACAN,QAAQA,CAACnB,IAAY;IACnB,MAAM2I,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;IACnDF,QAAQ,CAACG,KAAK,CAACC,QAAQ,GAAG,OAAO;IACjCJ,QAAQ,CAACG,KAAK,CAACE,OAAO,GAAG,GAAG;IAC5BL,QAAQ,CAACM,KAAK,GAAGjJ,IAAI;IACrB4I,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,QAAQ,CAAC;IACnCA,QAAQ,CAACS,MAAM,EAAE;IACjB,IAAI;MACFR,QAAQ,CAACS,WAAW,CAAC,MAAM,CAAC;MAC5BC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACvC,IAAI,CAACpD,YAAY,CAACqD,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZN,OAAO,CAACO,KAAK,CAAC,uBAAuB,EAAED,GAAG,CAAC;IAC7C,CAAC,SAAS;MACRhB,QAAQ,CAACM,IAAI,CAACY,WAAW,CAACnB,QAAQ,CAAC;IACrC;EACF;EAEQb,eAAeA,CAACpB,EAAc;IACpC4C,OAAO,CAACC,GAAG,CAAC7C,EAAE,CAAC;IACf,IAAI,CAACA,EAAE,EAAE;MACP;IACF;IACA,MAAMqD,YAAY,GAAG;oIAC2G;IAChIrD,EAAE,CAACc,aAAa,CAACwC,SAAS,GAAGD,YAAY;EAC3C;EAAC,QAAAE,CAAA,G;qBA5IU7E,sBAAsB,EAAAlG,EAAA,CAAAgL,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAlL,EAAA,CAAAgL,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAApL,EAAA,CAAAgL,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAtL,EAAA,CAAAgL,iBAAA,CAAAO,EAAA,CAAAC,sBAAA,GAAAxL,EAAA,CAAAgL,iBAAA,CAAAhL,EAAA,CAAAyL,MAAA,GAAAzL,EAAA,CAAAgL,iBAAA,CAAAhL,EAAA,CAAA0L,SAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAtBzF,sBAAsB;IAAA0F,SAAA;IAAAC,SAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;QCpB/B/L,EADJ,CAAAE,cAAA,aAAwC,aACP;QAGzBF,EAAA,CAAAC,uBAAA,MAAsC;QA0lBlCD,EAzlBA,CAAAoC,UAAA,IAAA6J,8CAAA,4BAAwC,IAAAC,8CAAA,4BAsBc,IAAAC,8CAAA,4BA0DL,IAAAC,8CAAA,4BA2CA,IAAAC,qCAAA,mBA8CX,IAAAC,qCAAA,mBA4BC,IAAAC,qCAAA,mBAgDD,KAAAC,sCAAA,mBA4DA,KAAAC,sCAAA,mBA0CH,KAAAC,sCAAA,mBA4BE,KAAAC,sCAAA,mBAwDC,KAAAC,sCAAA,mBAuDF,KAAAC,sCAAA,mBAiDA,KAAAC,sCAAA,kBAkEd;;QAGlC9M,EADI,CAAAI,YAAA,EAAM,EACJ;QA8CNJ,EA3CA,CAAAoC,UAAA,KAAA2K,8CAAA,gCAAA/M,EAAA,CAAAiE,sBAAA,CAAgC,KAAA+I,8CAAA,gCAAAhN,EAAA,CAAAiE,sBAAA,CAaG,KAAAgJ,8CAAA,gCAAAjN,EAAA,CAAAiE,sBAAA,CAcH,KAAAiJ,8CAAA,gCAAAlN,EAAA,CAAAiE,sBAAA,CAgBF;;;QA3oBRjE,EAAA,CAAAM,SAAA,GAAuB;QAAvBN,EAAA,CAAAwB,UAAA,aAAAwK,GAAA,CAAAlF,UAAA,CAAuB;QAClB9G,EAAA,CAAAM,SAAA,EAAuB;QAAvBN,EAAA,CAAAwB,UAAA,2BAAuB;QAsBvBxB,EAAA,CAAAM,SAAA,EAAqC;QAArCN,EAAA,CAAAwB,UAAA,yCAAqC;QA0DrCxB,EAAA,CAAAM,SAAA,EAAgC;QAAhCN,EAAA,CAAAwB,UAAA,oCAAgC;QA2ChCxB,EAAA,CAAAM,SAAA,EAAgC;QAAhCN,EAAA,CAAAwB,UAAA,oCAAgC;QA8CzCxB,EAAA,CAAAM,SAAA,EAA8B;QAA9BN,EAAA,CAAAwB,UAAA,kCAA8B;QA4B9BxB,EAAA,CAAAM,SAAA,EAA+B;QAA/BN,EAAA,CAAAwB,UAAA,mCAA+B;QAgD/BxB,EAAA,CAAAM,SAAA,EAA8B;QAA9BN,EAAA,CAAAwB,UAAA,kCAA8B;QA4D9BxB,EAAA,CAAAM,SAAA,EAA8B;QAA9BN,EAAA,CAAAwB,UAAA,kCAA8B;QA0C9BxB,EAAA,CAAAM,SAAA,EAA2B;QAA3BN,EAAA,CAAAwB,UAAA,+BAA2B;QA4B3BxB,EAAA,CAAAM,SAAA,EAA6B;QAA7BN,EAAA,CAAAwB,UAAA,iCAA6B;QAwD7BxB,EAAA,CAAAM,SAAA,EAA8B;QAA9BN,EAAA,CAAAwB,UAAA,kCAA8B;QAuD9BxB,EAAA,CAAAM,SAAA,EAA4B;QAA5BN,EAAA,CAAAwB,UAAA,gCAA4B;QAiD5BxB,EAAA,CAAAM,SAAA,EAA4B;QAA5BN,EAAA,CAAAwB,UAAA,gCAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}