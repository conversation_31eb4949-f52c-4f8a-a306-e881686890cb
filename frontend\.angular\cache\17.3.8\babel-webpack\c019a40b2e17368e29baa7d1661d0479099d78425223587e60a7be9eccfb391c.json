{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { forkJoin, of } from 'rxjs';\nimport { catchError, finalize, map, skip, switchMap, take } from 'rxjs/operators';\nimport { Status } from 'src/app/core/models/classroom.model';\nimport { LessonStatus } from 'src/app/core/models/lesson.model';\nimport { StudentTrialStatus } from 'src/app/core/models/student.model';\nimport { UserRole } from 'src/app/core/models/user.model';\nimport { SubSink } from 'subsink';\nimport * as data from '../../core/models/data';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nimport * as i2 from \"src/app/core/services/auth.service\";\nimport * as i3 from \"src/app/core/services/lesson.service\";\nimport * as i4 from \"src/app/core/services/blog.service\";\nimport * as i5 from \"src/app/core/services/tutorial.service\";\nimport * as i6 from \"src/app/core/services/guide.service\";\nimport * as i7 from \"src/app/core/services/goal.service\";\nimport * as i8 from \"src/app/core/services/notification.service\";\nimport * as i9 from \"src/app/core/services/classroom.service\";\nimport * as i10 from \"src/app/core/services/student-trial.service\";\nimport * as i11 from \"src/app/core/services/calendar.service\";\nimport * as i12 from \"src/app/core/services/user.service\";\nimport * as i13 from \"src/app/core/services/layout.service\";\nimport * as i14 from \"src/app/core/services/toast.service\";\nimport * as i15 from \"src/app/core/services/jitsi-meet.service\";\nimport * as i16 from \"src/app/core/services/rating-and-report.service\";\nimport * as i17 from \"@angular/common\";\nimport * as i18 from \"../classroom/next-lesson/next-lesson.component\";\nimport * as i19 from \"../../shared/block-viewer/block-viewer.component\";\nimport * as i20 from \"../../shared/prime/prime/calendar-agenda/calendar-agenda.component\";\nimport * as i21 from \"../../shared/lesson/lesson-schedule-box/lesson-schedule-box.component\";\nimport * as i22 from \"../../shared/goal/goal-steps-box/goal-steps-box.component\";\nimport * as i23 from \"../../shared/prime/text-call-to-action-button/text-call-to-action-button.component\";\nimport * as i24 from \"../../shared/classroom/classroom-card/classroom-card.component\";\nimport * as i25 from \"primeng/api\";\nimport * as i26 from \"../../shared/classroom/classroom-status-filters/classroom-status-filters.component\";\nimport * as i27 from \"primeng/scrollpanel\";\nimport * as i28 from \"primeng/carousel\";\nimport * as i29 from \"./intro-gradient/intro-gradient.component\";\nimport * as i30 from \"./dashboard-notifications/dashboard-notifications.component\";\nimport * as i31 from \"./dashboard-next-steps/dashboard-next-steps.component\";\nimport * as i32 from \"./dashboard-benefits/dashboard-benefits.component\";\nimport * as i33 from \"./calendar-preview-week/calendar-preview-week.component\";\nimport * as i34 from \"./dashboard-contact-us-form/dashboard-contact-us-form.component\";\nimport * as i35 from \"./dashboard-teacher-classrooms/dashboard-teacher-classrooms.component\";\nimport * as i36 from \"../../shared/loader/loader.component\";\nimport * as i37 from \"./dashboard-request-trial-form/dashboard-request-trial-form.component\";\nimport * as i38 from \"./redirect-to-galaxy/redirect-to-galaxy.component\";\nconst _c0 = [\"ignoredOrLostTrialStatusTemplate\"];\nconst _c1 = [\"newOrForwaredTrialStatusTemplate\"];\nconst _c2 = [\"paidOrActiveTemplate\"];\nconst _c3 = [\"classroomCardsWrapper\"];\nconst DashboardComponent_Defer_2_DepsFn = () => [i17.NgIf, i17.NgTemplateOutlet, i17.NgSwitch, i17.NgSwitchCase, i17.NgSwitchDefault, i19.BlockViewerComponent, i20.CalendarAgendaComponent, i21.LessonScheduleBoxComponent, i22.GoalStepsBoxComponent, i23.TextCallToActionButtonComponent, i36.LoaderComponent, i24.ClassroomCardComponent, i25.PrimeTemplate, i27.ScrollPanel, i28.Carousel, i29.IntroGradientComponent, i30.DashboardNotificationsComponent, i31.DashboardNextStepsComponent, i32.DashboardBenefitsComponent, i37.DashboardRequestTrialFormComponent, i33.CalendarPreviewWeekComponent, i38.RedirectToGalaxyComponent, i17.AsyncPipe];\nconst _c4 = () => ({\n  width: \"100%\",\n  height: \"118px\"\n});\nconst _c5 = () => [];\nconst _c6 = a0 => ({\n  $implicit: a0\n});\nconst _c7 = () => ({});\nconst _c8 = () => ({\n  icon: \"/assets/images/dashboard/next-steps-1-icon.svg\",\n  title: \"Chose a package and begin learning!\",\n  description: \"Once you you have purchased a package, we will contact you for more information regarding your level, your availability, your goals, and any specific difficulties or requirements. That way, we can match you with the most suitable of our teachers.\"\n});\nconst _c9 = () => ({\n  icon: \"/assets/images/dashboard/next-steps-1-icon.svg\",\n  title: \"Arrange your first trial lesson\",\n  description: \"Once you you have purchased a package, we will contact you for more information regarding your level, your availability, your goals, and any specific difficulties or requirements. That way, we can match you with the most suitable of our teachers.\"\n});\nconst _c10 = () => ({\n  width: \"100%\",\n  height: \"119px\"\n});\nfunction DashboardComponent_Defer_0_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 13)(2, \"div\", 14);\n    i0.ɵɵelement(3, \"app-loader\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DashboardComponent_Defer_0_ng_container_2_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    const teacherTemplate_r3 = i0.ɵɵreference(27);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", teacherTemplate_r3)(\"ngTemplateOutletContext\", ctx_r1.templateContext);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_2_ng_container_1_Template, 1, 0, \"ng-container\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    const newOrForwaredTrialStatusTemplate_r4 = i0.ɵɵreference(15);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", newOrForwaredTrialStatusTemplate_r4)(\"ngTemplateOutletContext\", ctx_r1.templateContext);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_3_ng_container_1_Template, 1, 0, \"ng-container\", 19);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(5);\n    const ignoredOrLostTrialStatusTemplate_r5 = i0.ɵɵreference(17);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ignoredOrLostTrialStatusTemplate_r5);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_4_ng_container_1_Template, 1, 0, \"ng-container\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    const paidOrActiveTemplate_r6 = i0.ɵɵreference(19);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", paidOrActiveTemplate_r6)(\"ngTemplateOutletContext\", ctx_r1.templateContext);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_5_app_redirect_to_galaxy_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-redirect-to-galaxy\");\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_5_app_redirect_to_galaxy_1_Template, 1, 0, \"app-redirect-to-galaxy\", 11);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const status_r7 = i0.ɵɵnextContext().ngIf;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", status_r7.toLowerCase() === ctx_r1.StudentStatus.GALAXY.toLowerCase());\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_6_p_carousel_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-intro-gradient\", 35);\n  }\n  if (rf & 2) {\n    const banner_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"bannerMode\", banner_r9.bannerMode);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_6_p_carousel_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-carousel\", 33);\n    i0.ɵɵtemplate(1, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_6_p_carousel_3_ng_template_1_Template, 1, 1, \"ng-template\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵproperty(\"value\", ctx_r1.banners)(\"autoplayInterval\", 5000)(\"verticalViewPortHeight\", \"130px\")(\"orientation\", \"vertical\")(\"numVisible\", 1)(\"page\", 1)(\"numScroll\", 1)(\"showNavigators\", false)(\"showIndicators\", ctx_r1.banners.length > 1 ? true : false)(\"circular\", ctx_r1.banners.length > 1 ? true : false);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 20)(2, \"div\", 21);\n    i0.ɵɵtemplate(3, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_6_p_carousel_3_Template, 2, 10, \"p-carousel\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 20)(5, \"div\", 23);\n    i0.ɵɵelement(6, \"app-goal-steps-box\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 23)(8, \"app-block-viewer\", 25)(9, \"p-scrollPanel\", 26);\n    i0.ɵɵelement(10, \"app-dashboard-notifications\", 27);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 20)(12, \"div\", 28)(13, \"app-calendar-agenda\", 29);\n    i0.ɵɵlistener(\"dayClicked\", function DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_6_Template_app_calendar_agenda_dayClicked_13_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.onAgendaDayClicked($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 23);\n    i0.ɵɵelement(15, \"app-lesson-schedule-box\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(16, \"app-classroom-card\", 31)(17, \"app-dashboard-next-steps\", 32)(18, \"app-dashboard-benefits\")(19, \"app-goal-steps-box\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.banners.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"isSmall\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(10, _c4));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"slimStyle\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"lessons\", ctx_r1.context.lessons);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"lessons\", i0.ɵɵpureFunction0(11, _c5))(\"disableAddNewLessonButton\", true)(\"currentDate\", ctx_r1.currentAgendaDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"activeStep\", 2);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_7_ng_container_1_Template, 1, 0, \"ng-container\", 19);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(5);\n    const ignoredOrLostTrialStatusTemplate_r5 = i0.ɵɵreference(17);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ignoredOrLostTrialStatusTemplate_r5);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_8_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_8_ng_container_1_Template, 1, 0, \"ng-container\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    const newOrForwaredTrialStatusTemplate_r4 = i0.ɵɵreference(15);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", newOrForwaredTrialStatusTemplate_r4)(\"ngTemplateOutletContext\", ctx_r1.templateContext);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_9_app_dashboard_request_trial_form_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-dashboard-request-trial-form\", 39);\n    i0.ɵɵlistener(\"formSubmitted\", function DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_9_app_dashboard_request_trial_form_8_Template_app_dashboard_request_trial_form_formSubmitted_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(6);\n      return i0.ɵɵresetView(ctx_r1.onTrialRequestFormSubmitted($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 20)(2, \"div\", 23);\n    i0.ɵɵelement(3, \"app-calendar-preview-week\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 23)(5, \"app-block-viewer\", 36)(6, \"p-scrollPanel\", 26);\n    i0.ɵɵelement(7, \"app-dashboard-notifications\", 27);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(8, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_9_app_dashboard_request_trial_form_8_Template, 1, 0, \"app-dashboard-request-trial-form\", 37);\n    i0.ɵɵelementStart(9, \"div\", 38);\n    i0.ɵɵelement(10, \"app-dashboard-next-steps\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 38);\n    i0.ɵɵelement(12, \"app-dashboard-benefits\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"containerHeight\", 118);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(5, _c4));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"slimStyle\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.studentHasTrialRequest);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" Unknown status. \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelementContainerStart(1, 16);\n    i0.ɵɵtemplate(2, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_2_Template, 2, 2, \"ng-container\", 17)(3, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_3_Template, 2, 1, \"ng-container\", 17)(4, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_4_Template, 2, 2, \"ng-container\", 17)(5, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_5_Template, 2, 1, \"ng-container\", 17)(6, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_6_Template, 20, 12, \"ng-container\", 17)(7, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_7_Template, 2, 1, \"ng-container\", 17)(8, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_8_Template, 2, 2, \"ng-container\", 17)(9, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_9_Template, 13, 6, \"ng-container\", 17)(10, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_10_Template, 2, 0, \"ng-container\", 18);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r7 = ctx.ngIf;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitch\", status_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.StudentStatus.FORWARDED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.StudentStatus.IGNORED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.StudentStatus.HAS_PAYMENT);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.StudentStatus.GALAXY);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.StudentStatus.ASSIGNED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.StudentStatus.LOST);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.StudentStatus.NEW);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.StudentStatus.NONE);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_Template, 11, 9, \"div\", 11);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelement(3, \"app-text-call-to-action-button\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(2, 1, ctx_r1.studentStatus$));\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DashboardComponent_Defer_0_ng_container_2_ng_container_1_Template, 2, 2, \"ng-container\", 11)(2, DashboardComponent_Defer_0_ng_container_2_ng_container_2_Template, 4, 3, \"ng-container\", 11);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.role == ctx_r1.UserRoles.TEACHER);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.role == ctx_r1.UserRoles.STUDENT);\n  }\n}\nfunction DashboardComponent_Defer_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, DashboardComponent_Defer_0_ng_container_0_Template, 4, 0, \"ng-container\", 11);\n    i0.ɵɵelementStart(1, \"div\", 12);\n    i0.ɵɵtemplate(2, DashboardComponent_Defer_0_ng_container_2_Template, 3, 2, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showLoader);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.showLoader);\n  }\n}\nfunction DashboardComponent_DeferPlaceholder_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_ng_template_14_div_1_p_carousel_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-intro-gradient\", 45);\n  }\n  if (rf & 2) {\n    let tmp_13_0;\n    const banner_r12 = ctx.$implicit;\n    const context_r13 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"classRoom\", (tmp_13_0 = banner_r12.classRoom) !== null && tmp_13_0 !== undefined ? tmp_13_0 : i0.ɵɵpureFunction0(3, _c7))(\"teacher\", context_r13 && context_r13.trialClassRooms.length > 0 ? context_r13.trialClassRooms[0].teacher : null)(\"bannerMode\", banner_r12.bannerMode);\n  }\n}\nfunction DashboardComponent_ng_template_14_div_1_p_carousel_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-carousel\", 44);\n    i0.ɵɵtemplate(1, DashboardComponent_ng_template_14_div_1_p_carousel_1_ng_template_1_Template, 1, 4, \"ng-template\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"value\", ctx_r1.banners)(\"autoplayInterval\", 5000)(\"responsiveOptions\", ctx_r1.responsiveOptions)(\"verticalViewPortHeight\", \"130px\")(\"numVisible\", 1)(\"page\", 1)(\"numScroll\", 1)(\"showNavigators\", false)(\"showIndicators\", ctx_r1.banners.length > 1 ? true : false)(\"circular\", ctx_r1.banners.length > 1 ? true : false);\n  }\n}\nfunction DashboardComponent_ng_template_14_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtemplate(1, DashboardComponent_ng_template_14_div_1_p_carousel_1_Template, 2, 10, \"p-carousel\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.banners.length > 0);\n  }\n}\nfunction DashboardComponent_ng_template_14_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction DashboardComponent_ng_template_14_ng_container_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction DashboardComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtemplate(1, DashboardComponent_ng_template_14_div_1_Template, 2, 1, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 20)(3, \"div\", 23);\n    i0.ɵɵelement(4, \"app-goal-steps-box\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 23)(6, \"app-block-viewer\", 41)(7, \"p-scrollPanel\", 26);\n    i0.ɵɵelement(8, \"app-dashboard-notifications\", 27);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(9, \"div\", 20)(10, \"div\", 28)(11, \"app-calendar-agenda\", 29);\n    i0.ɵɵlistener(\"dayClicked\", function DashboardComponent_ng_template_14_Template_app_calendar_agenda_dayClicked_11_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAgendaDayClicked($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 23);\n    i0.ɵɵtemplate(13, DashboardComponent_ng_template_14_ng_container_13_Template, 1, 0, \"ng-container\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(14, DashboardComponent_ng_template_14_ng_container_14_Template, 1, 0, \"ng-container\", 15);\n    i0.ɵɵelementStart(15, \"div\", 42);\n    i0.ɵɵelement(16, \"app-dashboard-next-steps\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 38);\n    i0.ɵɵelement(18, \"app-dashboard-benefits\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 38);\n    i0.ɵɵelement(20, \"app-goal-steps-box\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const context_r13 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    const classroomCardsTemplate_r14 = i0.ɵɵreference(21);\n    const lessonsBoxTemplate_r15 = i0.ɵɵreference(23);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showBanners);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"isSmall\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"containerHeight\", 119);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(12, _c4));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"slimStyle\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"lessons\", context_r13.lessons);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", lessonsBoxTemplate_r15)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(13, _c6, ctx_r1.templateContext.$implicit));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", classroomCardsTemplate_r14)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(15, _c6, ctx_r1.templateContext.$implicit));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"activeStep\", ctx_r1.getNextStepNumber(context_r13.trialClassRooms));\n  }\n}\nfunction DashboardComponent_ng_template_16_p_carousel_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-intro-gradient\", 35);\n  }\n  if (rf & 2) {\n    const banner_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"bannerMode\", banner_r16.bannerMode);\n  }\n}\nfunction DashboardComponent_ng_template_16_p_carousel_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-carousel\", 33);\n    i0.ɵɵtemplate(1, DashboardComponent_ng_template_16_p_carousel_2_ng_template_1_Template, 1, 1, \"ng-template\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", ctx_r1.banners)(\"autoplayInterval\", 5000)(\"verticalViewPortHeight\", \"130px\")(\"orientation\", \"vertical\")(\"numVisible\", 1)(\"page\", 1)(\"numScroll\", 1)(\"showNavigators\", false)(\"showIndicators\", ctx_r1.banners.length > 1 ? true : false)(\"circular\", ctx_r1.banners.length > 1 ? true : false);\n  }\n}\nfunction DashboardComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21);\n    i0.ɵɵtemplate(2, DashboardComponent_ng_template_16_p_carousel_2_Template, 2, 10, \"p-carousel\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 20)(4, \"div\", 23);\n    i0.ɵɵelement(5, \"app-calendar-preview-week\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 23)(7, \"app-block-viewer\", 46)(8, \"p-scrollPanel\", 26);\n    i0.ɵɵelement(9, \"app-dashboard-notifications\", 27);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelement(10, \"app-dashboard-contact-us-form\")(11, \"app-dashboard-benefits\");\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.banners.length > 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(4, _c4));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"slimStyle\", true);\n  }\n}\nfunction DashboardComponent_ng_template_18_p_carousel_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-intro-gradient\", 52);\n  }\n  if (rf & 2) {\n    const banner_r18 = ctx.$implicit;\n    i0.ɵɵproperty(\"classRoom\", banner_r18.classRoom)(\"bannerMode\", banner_r18.bannerMode);\n  }\n}\nfunction DashboardComponent_ng_template_18_p_carousel_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-carousel\", 51);\n    i0.ɵɵtemplate(1, DashboardComponent_ng_template_18_p_carousel_2_ng_template_1_Template, 1, 2, \"ng-template\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", ctx_r1.banners)(\"autoplayInterval\", 5000)(\"verticalViewPortHeight\", \"130px\")(\"showNavigators\", false)(\"showIndicators\", ctx_r1.banners.length > 1 ? true : false)(\"circular\", ctx_r1.banners.length > 1 ? true : false);\n  }\n}\nfunction DashboardComponent_ng_template_18_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction DashboardComponent_ng_template_18_ng_container_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction DashboardComponent_ng_template_18_ng_container_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 38);\n    i0.ɵɵelement(2, \"app-dashboard-next-steps\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"app-dashboard-benefits\");\n    i0.ɵɵelementStart(4, \"div\", 38);\n    i0.ɵɵelement(5, \"app-goal-steps-box\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const context_r19 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"activeStep\", context_r19.trialClassRooms.length > 0 ? 3 : 2)(\"stepOneInfo\", i0.ɵɵpureFunction0(3, _c8))(\"stepThreeInfo\", i0.ɵɵpureFunction0(4, _c9));\n  }\n}\nfunction DashboardComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21);\n    i0.ɵɵtemplate(2, DashboardComponent_ng_template_18_p_carousel_2_Template, 2, 6, \"p-carousel\", 47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 20)(4, \"div\", 23)(5, \"app-block-viewer\", 48);\n    i0.ɵɵelement(6, \"app-next-lesson\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 23)(8, \"app-block-viewer\", 50)(9, \"p-scrollPanel\", 26);\n    i0.ɵɵelement(10, \"app-dashboard-notifications\", 27);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 20)(12, \"div\", 28)(13, \"app-calendar-agenda\", 29);\n    i0.ɵɵlistener(\"dayClicked\", function DashboardComponent_ng_template_18_Template_app_calendar_agenda_dayClicked_13_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAgendaDayClicked($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 28);\n    i0.ɵɵtemplate(15, DashboardComponent_ng_template_18_ng_container_15_Template, 1, 0, \"ng-container\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, DashboardComponent_ng_template_18_ng_container_16_Template, 1, 0, \"ng-container\", 15)(17, DashboardComponent_ng_template_18_ng_container_17_Template, 6, 5, \"ng-container\", 11);\n  }\n  if (rf & 2) {\n    const context_r19 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    const classroomCardsTemplate_r14 = i0.ɵɵreference(21);\n    const lessonsBoxTemplate_r15 = i0.ɵɵreference(23);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.banners.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"containerHeight\", 129);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"lessons\", context_r19.lessons)(\"todayLessons\", context_r19.todaysLessons);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(13, _c4));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"slimStyle\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"lessons\", context_r19.lessons);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", lessonsBoxTemplate_r15)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(14, _c6, ctx_r1.templateContext.$implicit));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", classroomCardsTemplate_r14)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(16, _c6, ctx_r1.templateContext.$implicit));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", context_r19.trialClassRooms.length === 0);\n  }\n}\nfunction DashboardComponent_ng_template_20_ng_container_0_ng_container_3_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 65);\n    i0.ɵɵelement(2, \"app-classroom-card\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const trialClassroom_r21 = ctx.$implicit;\n    const index_r22 = ctx.i;\n    const context_r23 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", context_r23.filteredTrialClassRooms.length === 1 ? \"mt-3\" : index_r22 > 0 ? \"classroom-cards-space\" : \"mt-3\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"classRoom\", trialClassroom_r21);\n  }\n}\nfunction DashboardComponent_ng_template_20_ng_container_0_ng_container_3_ng_container_11_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-classroom-card\", 70);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const trialClassroom_r24 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"classRoom\", trialClassroom_r24);\n  }\n}\nfunction DashboardComponent_ng_template_20_ng_container_0_ng_container_3_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 67);\n    i0.ɵɵelementContainerStart(2);\n    i0.ɵɵelementStart(3, \"div\", 68);\n    i0.ɵɵelement(4, \"img\", 69);\n    i0.ɵɵelementStart(5, \"strong\");\n    i0.ɵɵtext(6, \"No classrooms found.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(7, DashboardComponent_ng_template_20_ng_container_0_ng_container_3_ng_container_11_ng_container_7_Template, 2, 1, \"ng-container\", 64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.filteredClassrooms);\n  }\n}\nfunction DashboardComponent_ng_template_20_ng_container_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 56)(2, \"div\", 57)(3, \"div\", 58);\n    i0.ɵɵelement(4, \"img\", 59);\n    i0.ɵɵelementStart(5, \"div\", 60)(6, \"h2\", 61);\n    i0.ɵɵtext(7, \"Classrooms\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 62)(9, \"app-classroom-status-filters\", 63);\n    i0.ɵɵlistener(\"switchToggled\", function DashboardComponent_ng_template_20_ng_container_0_ng_container_3_Template_app_classroom_status_filters_switchToggled_9_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.toggleSwitch($event));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(10, DashboardComponent_ng_template_20_ng_container_0_ng_container_3_ng_container_10_Template, 3, 2, \"ng-container\", 64)(11, DashboardComponent_ng_template_20_ng_container_0_ng_container_3_ng_container_11_Template, 8, 1, \"ng-container\", 11);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const context_r23 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"isTrial\", false)(\"switches\", ctx_r1.switches);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", context_r23.filteredTrialClassRooms);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", context_r23.filteredTrialClassRooms.length === 0);\n  }\n}\nfunction DashboardComponent_ng_template_20_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 54)(2, \"div\", 55);\n    i0.ɵɵtemplate(3, DashboardComponent_ng_template_20_ng_container_0_ng_container_3_Template, 12, 4, \"ng-container\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const context_r23 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", context_r23 && context_r23.trialClassRooms);\n  }\n}\nfunction DashboardComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, DashboardComponent_ng_template_20_ng_container_0_Template, 4, 1, \"ng-container\", 11);\n  }\n  if (rf & 2) {\n    const context_r23 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", context_r23 && context_r23.trialClassRooms && context_r23.trialClassRooms.length > 0);\n  }\n}\nfunction DashboardComponent_ng_template_22_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-block-viewer\", 71);\n    i0.ɵɵelement(2, \"app-lesson-schedule-box\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const context_r25 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"containerHeight\", 386);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"scrollHeight\", 340)(\"classRooms\", context_r25.trialClassRooms)(\"lessons\", context_r25.lessons)(\"disableAddNewLessonButton\", context_r25.classRooms.length === 0 && context_r25.trialClassRooms.length === 0);\n  }\n}\nfunction DashboardComponent_ng_template_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, DashboardComponent_ng_template_22_ng_container_0_Template, 3, 5, \"ng-container\", 11);\n  }\n  if (rf & 2) {\n    const context_r25 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", context_r25 && context_r25.trialClassRooms);\n  }\n}\nfunction DashboardComponent_ng_template_24_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-lesson-schedule-box\", 73);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const context_r26 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"classRooms\", context_r26.classRooms)(\"lessons\", context_r26.lessons)(\"disableAddNewLessonButton\", context_r26.classRooms.length === 0);\n  }\n}\nfunction DashboardComponent_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"app-block-viewer\", 71);\n    i0.ɵɵtemplate(1, DashboardComponent_ng_template_24_ng_container_1_Template, 2, 3, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const context_r26 = ctx.$implicit;\n    i0.ɵɵproperty(\"containerHeight\", 500);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", context_r26.classRooms);\n  }\n}\nfunction DashboardComponent_ng_template_26_p_carousel_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-intro-gradient\", 78);\n  }\n  if (rf & 2) {\n    const banner_r28 = ctx.$implicit;\n    const context_r29 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"teacher\", context_r29 && context_r29.trialClassRooms.length > 0 ? context_r29.trialClassRooms[0].teacher : null)(\"bannerMode\", banner_r28.bannerMode);\n  }\n}\nfunction DashboardComponent_ng_template_26_p_carousel_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-carousel\", 44);\n    i0.ɵɵtemplate(1, DashboardComponent_ng_template_26_p_carousel_2_ng_template_1_Template, 1, 2, \"ng-template\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", ctx_r1.banners)(\"autoplayInterval\", 5000)(\"responsiveOptions\", ctx_r1.responsiveOptions)(\"verticalViewPortHeight\", \"130px\")(\"numVisible\", 1)(\"page\", 1)(\"numScroll\", 1)(\"showNavigators\", false)(\"showIndicators\", ctx_r1.banners.length > 1 ? true : false)(\"circular\", ctx_r1.banners.length > 1 ? true : false);\n  }\n}\nfunction DashboardComponent_ng_template_26_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction DashboardComponent_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21);\n    i0.ɵɵtemplate(2, DashboardComponent_ng_template_26_p_carousel_2_Template, 2, 10, \"p-carousel\", 43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 20)(4, \"div\", 23)(5, \"app-block-viewer\", 48);\n    i0.ɵɵelement(6, \"app-next-lesson\", 74);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 23)(8, \"app-block-viewer\", 75)(9, \"p-scrollPanel\", 26);\n    i0.ɵɵelement(10, \"app-dashboard-notifications\", 27);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 76)(12, \"div\", 28)(13, \"app-calendar-agenda\", 29);\n    i0.ɵɵlistener(\"dayClicked\", function DashboardComponent_ng_template_26_Template_app_calendar_agenda_dayClicked_13_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAgendaDayClicked($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 28);\n    i0.ɵɵtemplate(15, DashboardComponent_ng_template_26_ng_container_15_Template, 1, 0, \"ng-container\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 38);\n    i0.ɵɵelement(17, \"app-dashboard-teacher-classrooms\", 77);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"app-text-call-to-action-button\");\n  }\n  if (rf & 2) {\n    const context_r29 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    const lessonsBoxTemplateTeacher_r30 = i0.ɵɵreference(25);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.banners.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"containerHeight\", 129);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"lessons\", context_r29.lessons);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"containerHeight\", 119);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(11, _c10));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"slimStyle\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"lessons\", context_r29.lessons);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", lessonsBoxTemplateTeacher_r30)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(12, _c6, ctx_r1.templateContext.$implicit));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"classrooms\", context_r29.classRooms);\n  }\n}\nexport let DashboardComponent = /*#__PURE__*/(() => {\n  class DashboardComponent {\n    constructor(generalService, authService, lessonService, blogService, tutorialService, guideService, goalService, notificationService, classroomService, studentTrialService, calendarService, userService, layoutService, toastService, cdr, jitsiMeetService, ratingAndReportService, renderer) {\n      this.generalService = generalService;\n      this.authService = authService;\n      this.lessonService = lessonService;\n      this.blogService = blogService;\n      this.tutorialService = tutorialService;\n      this.guideService = guideService;\n      this.goalService = goalService;\n      this.notificationService = notificationService;\n      this.classroomService = classroomService;\n      this.studentTrialService = studentTrialService;\n      this.calendarService = calendarService;\n      this.userService = userService;\n      this.layoutService = layoutService;\n      this.toastService = toastService;\n      this.cdr = cdr;\n      this.jitsiMeetService = jitsiMeetService;\n      this.ratingAndReportService = ratingAndReportService;\n      this.renderer = renderer;\n      this.ignoredOrLostTrialStatusTemplate = {};\n      this.newOrForwaredTrialStatusTemplate = {};\n      this.paidOrActiveTemplate = {};\n      this.classroomCardsWrapper = {};\n      this.subs = new SubSink();\n      this.loggedInUser = {};\n      this.allLessons = [];\n      this.todaysLessons = [];\n      this.oldValue = 0;\n      this.newValue = 0;\n      this.blog = this.blogService.dummyBlogs;\n      this.guides = [];\n      this.showModal = false;\n      this.suggestForm = {};\n      this.controlErrors = {};\n      this.suggestError = false;\n      this.suggestErrorLength = false;\n      this.recommendToUsers = [];\n      this.role = \"\";\n      this.studentGoals = [];\n      this.allLessonsLoading = true;\n      this.todaysLessonsLoading = true;\n      this.showGoalsMenu = false;\n      this.notifications = [];\n      this.tutorials = [];\n      this.recHistory = [];\n      this.userClassrooms = [];\n      this.loadingGoals = true;\n      this.loadingClassrooms = true;\n      this.today = new Date();\n      this.studentHasTrialRequest = false;\n      this.showLoader = true;\n      this.banners = [];\n      this.verticalViewPortHeight = 130;\n      this.agendaHeight = 0;\n      this.responsiveOptions = [];\n      this.LessonStatus = LessonStatus;\n      this.lessons = data.dummyLessons;\n      this.studentStatus$ = this.studentTrialService.studentStatus$;\n      this.StudentStatus = StudentTrialStatus;\n      this.currentAgendaDate = new Date();\n      this.divHeight = '140px';\n      this.UserRoles = UserRole;\n      this.user = {};\n      this.switches = {\n        All: false,\n        Ongoing: true,\n        Completed: false,\n        Expired: false\n      };\n      this.autoplayInterval = 5000;\n      this.showBanners = false;\n      this.templateContext = {\n        $implicit: {\n          hasAssignedLesson: false,\n          trialClassRooms: [],\n          filteredTrialClassRooms: [],\n          classRooms: [],\n          lessons: [],\n          todaysLessons: [],\n          leftMenuHeight: 0,\n          hasPayment: false\n        }\n      };\n    }\n    // TODO: add real dates\n    getStyle(date) {\n      const myDates = ['2023-01-31', '2023-02-15', '2023-02-21', '2018-03-05'];\n      const year = date.year.toString();\n      const month = (date.month + 1).toString().padStart(2, '0');\n      const day = date.day.toString().padStart(2, '0');\n      const dateString = `${year}-${month}-${day}`;\n      const returnedStatus = myDates.includes(dateString);\n      return returnedStatus;\n    }\n    executeCommand(data) {\n      console.log('this.jitsiService.getParticipants():', this.jitsiMeetService.getParticipants());\n      this.jitsiMeetService.api.executeCommand('sendEndpointTextMessage', this.jitsiMeetService.getParticipants(), 'mover a principal');\n    }\n    ngOnInit() {\n      this.user = this.authService.getLoggedInUser();\n      this.role = this.authService.getUserRole();\n      this.studentHasTrialRequest = this.studentTrialService.isTrialRequested;\n      this.allLessonsLoading = true;\n      this.loadingClassrooms = true;\n      this.subs.add(this.generalService.deviceKind.pipe().subscribe(res => {\n        if (res) {\n          if (res.w576up) {\n            this.verticalViewPortHeight = 130;\n            // this.renderer.addClass(this.document.getElementsByClassName('app')[0], 'h-full');\n          } else {\n            this.verticalViewPortHeight = 200;\n          }\n        }\n      }));\n      this.adjustHeightListener();\n      this.loadInitialDataBasedOnRole(this.role);\n      this.initUpdateListener();\n      this.suggestForm = new UntypedFormGroup({\n        suggestion: new UntypedFormControl(null, {\n          validators: [Validators.required, Validators.minLength(20)]\n        })\n      });\n      // this.subs.sink = this.blogService.get().pipe(take(1)).subscribe(res => {\n      //   this.blog = res;\n      // })\n      this.subs.sink = this.blogService.getRecHistory().pipe(take(1)).subscribe(res => {\n        let toret = [];\n        for (let h of res) {\n          if (!toret.some(el => el.studentName == h.studentName && el.articleTitle == h.articleTitle)) {\n            toret.push(h);\n          }\n        }\n        this.recHistory = toret;\n      });\n    }\n    ngAfterViewInit() {\n      // document.getElementById('classrooms-big-title')!.style.right = -(window.innerWidth / 2 + 160) + \"px\";\n      // document.getElementById('tutorials-big-title')!.style.right = -(window.innerWidth / 2) + \"px\";\n      // document.getElementById('blog-big-title')!.style.left = -(window.innerWidth / 3.2) + \"px\";\n      // document.getElementById('guides-big-title')!.style.left = -(window.innerWidth / 3.2) + \"px\";\n    }\n    loadScript(url) {\n      console.log('preparing to load...');\n      let node = document.createElement('script');\n      node.setAttribute(\"id\", \"lottie-player\");\n      node.src = url;\n      node.type = 'text/javascript';\n      node.async = true;\n      node.charset = 'utf-8';\n      document.getElementsByTagName('head')[0].appendChild(node);\n    }\n    ngAfterContentInit() {\n      if (this.role == UserRole.STUDENT) {\n        this.subs.add(this.goalService.updateListener.subscribe(res => {\n          this.subs.sink = this.goalService.getGoals().subscribe(res => {\n            this.loadingGoals = false;\n            this.studentGoals = res;\n          });\n        }));\n      }\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n    }\n    onResize() {\n      this.adjustHeightListener();\n    }\n    getNotificationColor(type) {\n      if (type === LessonStatus.ARRANGED) {\n        return '#c056f5';\n      } else if (type === LessonStatus.COMPLETED) {\n        return '#3b9bed';\n      } else {\n        return '#F27769';\n      }\n    }\n    getNotificationBd(read) {\n      if (read) {\n        return \"white\";\n      } else {\n        return \"#efefef\";\n      }\n    }\n    getLessonColor(status) {\n      if (status === LessonStatus.ARRANGED) {\n        return '#c056f5';\n      } else if (status === LessonStatus.ARRANGED_TRIAL) {\n        return '#E5B0FC';\n      } else if (status === LessonStatus.COMPLETED) {\n        return '#3b9bed';\n      } else {\n        return '#F27769';\n      }\n    }\n    toggleSection(sectionElement, sectionArrowImgSrc) {\n      this.generalService.toggleSection(sectionElement, sectionArrowImgSrc, true);\n    }\n    onWindowScroll($event) {\n      this.newValue = window.pageYOffset;\n      if (this.newValue > 400) this.generalService.gradientScale(this.newValue, this.oldValue, 'classroom-gradient');\n      if (this.newValue > 1000) this.generalService.gradientScale(this.newValue, this.oldValue, 'blog-gradient');\n      if (this.newValue > 1500) this.generalService.gradientScale(this.newValue, this.oldValue, 'tutorial-gradient');\n      if (this.newValue > 2000) this.generalService.gradientScale(this.newValue, this.oldValue, 'guide-gradient');\n      this.oldValue = this.newValue;\n    }\n    toggleReadAbout() {\n      this.showModal = !this.showModal;\n      this.generalService.slideElements(this.showModal, 'read-about');\n    }\n    toggleSuggestIdea() {\n      this.showModal = !this.showModal;\n      this.generalService.slideElements(this.showModal, 'suggest-idea');\n    }\n    toggleHistory() {\n      this.showModal = !this.showModal;\n      this.generalService.slideElements(this.showModal, 'recommendation-history');\n    }\n    toggleViewGoals() {\n      this.showModal = !this.showModal;\n      this.generalService.slideElements(this.showModal, 'view-goals');\n    }\n    toggleSetGoals() {\n      this.showModal = !this.showModal;\n      this.generalService.slideElements(this.showModal, 'set-goals');\n    }\n    toggleEditGoals() {\n      this.showModal = !this.showModal;\n      this.generalService.slideElements(this.showModal, 'edit-goals');\n    }\n    submitSuggestion() {\n      this.suggestError = false;\n      this.suggestErrorLength = false;\n      if (!this.suggestForm.valid) {\n        this.getFormValidationErrors();\n        return;\n      }\n      this.toggleSuggestIdea();\n    }\n    getFormValidationErrors() {\n      Object.keys(this.suggestForm.controls).forEach(key => {\n        this.controlErrors = this.suggestForm.get(key).errors;\n        if (this.controlErrors != null) {\n          Object.keys(this.controlErrors).forEach(keyError => {\n            if (keyError == 'required') {\n              this.suggestError = true;\n            }\n            if (keyError == 'minlength') {\n              this.suggestErrorLength = true;\n            }\n          });\n        }\n      });\n    }\n    toggleRecommentTo(element) {\n      [].forEach.call(document.getElementsByClassName('recommend-list'), el => {\n        if (el.id !== element.id) el.style.display = 'none';\n      });\n      if (element.style.display === 'none' || element.style.display === '') {\n        element.style.display = 'flex';\n      } else {\n        element.style.display = 'none';\n      }\n    }\n    onAgendaHeight(event) {\n      this.agendaHeight = event;\n    }\n    closeRecommentListsOnClickOutside() {\n      document.addEventListener('click', event => {\n        setTimeout(() => {\n          let ignoreClickOnMeElement = document.getElementById('blog-right-side');\n          var isClickInsideElement = ignoreClickOnMeElement?.contains(event.target);\n          if (!isClickInsideElement) {\n            [].forEach.call(document.getElementsByClassName('recommend-list'), el => {\n              el.style.display = 'none';\n            });\n          }\n        }, 0);\n      });\n    }\n    showGuide(el) {\n      if (el.offsetHeight == 0) {\n        document.getElementById(el.id)?.classList.remove('no-visibility');\n        document.getElementById(el.id)?.classList.add('yes-visibility');\n        el.style.height = \"600px\";\n      } else {\n        document.getElementById(el.id)?.classList.remove('yes-visibility');\n        document.getElementById(el.id)?.classList.add('no-visibility');\n        el.style.height = \"0\";\n      }\n    }\n    onRecommentCheckedList(event) {\n      this.recommendToUsers = event.usersChecked;\n    }\n    rec(id) {\n      let ids = [];\n      for (let user of this.recommendToUsers) {\n        ids.push(user.id);\n      }\n      this.blogService.share(ids, id).pipe(take(1)).subscribe(res => {});\n    }\n    viewArticle(blogItem) {\n      window.open(blogItem.link, \"_blank\");\n    }\n    onTrialRequestFormSubmitted(isSent) {\n      if (isSent) {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Trial request has been sent.'\n        });\n        window.scrollTo(0, 0);\n        this.studentTrialService.updateStudentTrialStatus(StudentTrialStatus.NEW);\n        this.updateBanners(StudentTrialStatus.NEW, []);\n      }\n    }\n    loadInitialData() {\n      this.banners = [];\n      const bannerObservables = [];\n      const userClassrooms$ = this.classroomService.getLMSUserClassrooms(this.user.id);\n      const tutorials$ = this.tutorialService.getTutorials();\n      const userHasPayment$ = this.userService.getUserHasPayment();\n      const headers = new HttpHeaders({\n        'X-Bypass-Error-Interceptor': 'true'\n      });\n      const statusTrial$ = this.studentTrialService.getStudentTrialStatus(headers);\n      const studentHasTrialRequest$ = this.studentTrialService.getStudentHasTrialRequest();\n      this.showLoader = true;\n      this.subs.sink = studentHasTrialRequest$.pipe(switchMap(hasTrialRequest => {\n        if (hasTrialRequest) {\n          const trialObservables = {\n            userClassrooms: userClassrooms$.pipe(catchError(error => of(error))),\n            statusTrial: statusTrial$.pipe(catchError(error => of(false))),\n            userHasPayment: userHasPayment$.pipe(catchError(error => of(error))),\n            tutorials: tutorials$.pipe(catchError(error => of(error)))\n          };\n          return forkJoin(trialObservables);\n        } else {\n          return of(hasTrialRequest);\n        }\n      }), switchMap(results => {\n        // false is for no  trial status found (currently backend returns 400 error code if no trial is found)\n        if (results === false) {\n          return of(false);\n        }\n        const classroomIds = results.userClassrooms.map(classroom => classroom.id);\n        if (classroomIds.length === 0) {\n          return of({\n            ...results,\n            lessons: []\n          });\n        }\n        const lessonObservables = classroomIds.map(classroomId => this.classroomService.getClassroomLessons(classroomId));\n        return forkJoin(lessonObservables).pipe(map(lessons => ({\n          ...results,\n          lessons\n        })));\n      }), catchError(error => {\n        console.error(error);\n        this.showLoader = false;\n        return of({});\n      }), finalize(() => {\n        this.allLessonsLoading = false;\n        this.loadingClassrooms = false;\n        this.showLoader = false;\n      })).subscribe(res => {\n        this.showLoader = false;\n        console.log(res);\n        if (res === false) {\n          this.studentTrialService.updateStudentTrialStatus(StudentTrialStatus.NONE);\n        } else {\n          const trialClassRooms = res.userClassrooms;\n          console.log(res);\n          let newStatus = StudentTrialStatus.NONE;\n          if (!res.statusTrial && !res.userHasPayment) {\n            newStatus = trialClassRooms.length > 0 ? StudentTrialStatus.FORWARDED : StudentTrialStatus.NONE;\n          } else {\n            newStatus = res.userHasPayment ? StudentTrialStatus.HAS_PAYMENT : res.statusTrial.toLowerCase();\n            if (newStatus.toLowerCase().includes('Trial'.toLowerCase())) {\n              newStatus = StudentTrialStatus.NEW;\n            }\n          }\n          // set status for dashboard\n          this.templateContext.$implicit.hasAssignedLesson = true;\n          this.templateContext.$implicit.hasPayment = res.userHasPayment;\n          this.templateContext.$implicit.trialClassRooms = trialClassRooms;\n          this.templateContext.$implicit.filteredTrialClassRooms = trialClassRooms;\n          // If there are no ongoing classrooms, toggle the switch to the \"All\" position.\n          // If there are ongoing classrooms, toggle the switch to the \"Ongoing\" position.\n          if (this.getOngoingClassrooms().length === 0) {\n            this.toggleSwitch('All');\n          } else {\n            this.toggleSwitch('Ongoing');\n          }\n          this.templateContext.$implicit.lessons = res.lessons;\n          // this.layoutService.updateMenuItemDisabledStatus(\"Classrooms\", trialClassRooms.length === 0);\n          // if (this.isStudentActive(trialClassRooms, res.userHasPayment)) {\n          //   this.layoutService.updateAllMenuItemsDisabledStatus(false);\n          // } else {\n          //   this.layoutService.updateMenuItemClassroomToTrialsForStudent();\n          // }\n          trialClassRooms.forEach(classroom => {\n            if (this.classroomService.isLastLessonWithinOneMonth(classroom)) {\n              if (this.classroomService.isTrialClassroomWithCompletedFirstLesson(classroom)) {\n                const ratingObservable = this.ratingAndReportService.getTeacherTrialRating(+classroom.id).pipe(map(res => {\n                  if (res) {\n                    const hasRated = !this.generalService.isObjectEmpty(res);\n                    return hasRated ? null : {\n                      bannerMode: 'trial-assigned',\n                      classRoom: classroom\n                    };\n                  }\n                  return {\n                    bannerMode: 'trial-assigned',\n                    classRoom: classroom\n                  };\n                }));\n                bannerObservables.push(ratingObservable);\n              } else {\n                // Determine the appropriate banner based on classroom status\n                if (this.classroomService.isFirstLessonStatusInTrialClassroom(classroom, LessonStatus.ARRANGED)) {\n                  this.banners.push({\n                    bannerMode: 'trial-arranged',\n                    classRoom: classroom\n                  });\n                } else if (this.classroomService.isTrialClassroomDismissed(classroom)) {\n                  this.banners.push({\n                    bannerMode: 'trial-dismissed',\n                    classRoom: classroom\n                  });\n                } else {\n                  this.banners.push({\n                    bannerMode: 'trial-assigned',\n                    classRoom: classroom\n                  });\n                }\n              }\n            }\n          });\n          // Use forkJoin to wait for all banner observables to complete\n          if (bannerObservables.length > 0) {\n            forkJoin(bannerObservables).subscribe(bannerResults => {\n              this.banners.push(...bannerResults.filter(banner => banner !== null)); // Filter out null results\n              this.updateBanners(newStatus, trialClassRooms);\n            });\n          } else {\n            // If there are no banner observables, just call updateBanners directly\n            this.updateBanners(newStatus, trialClassRooms);\n          }\n          // Adding banner in the second position\n          // TODO: add when referral system is ready\n          // this.banners.splice(1, 0, { bannerMode: 'primary-referral-code' });\n          this.studentTrialService.updateStudentTrialStatus(newStatus);\n        }\n      });\n    }\n    loadInitialDataForTeacher() {\n      this.banners = [];\n      const userClassrooms$ = this.classroomService.getLMSUserClassrooms(this.user.id);\n      const headers = new HttpHeaders({\n        'X-Bypass-Error-Interceptor': 'true'\n      });\n      this.subs.add(userClassrooms$.pipe(switchMap(userClassrooms => {\n        if (userClassrooms) {\n          const lessonObservables = userClassrooms.map(classroom => this.classroomService.getClassroomLessons(classroom.id));\n          const observables = {\n            userClassrooms: userClassrooms$,\n            lessons: forkJoin(lessonObservables)\n          };\n          return forkJoin(observables);\n        } else {\n          return of(userClassrooms);\n        }\n      }), catchError(error => {\n        console.error(error);\n        this.showLoader = false;\n        return of({});\n      }), finalize(() => {\n        this.updateBanners('', []);\n        this.showLoader = false;\n      })).subscribe(data => {\n        this.showLoader = false;\n        // this.updateBanners('', []);\n        this.templateContext.$implicit.classRooms = data.userClassrooms;\n        // filter out empty lessons\n        this.templateContext.$implicit.lessons = data.lessons.filter(lessons => lessons.length > 0);\n        this.templateContext.$implicit.todaysLessons = data.todaysLessons;\n      }));\n    }\n    // TODO: check if this is needed\n    // getKeyByValue(object: any, value: any): string | undefined {\n    //   for (const key in object) {\n    //     if (object[key] === value) {\n    //       return key;\n    //     }\n    //   }\n    //   return undefined;\n    // }\n    updateBanners(status, trialClassRooms) {\n      // TODO: check for more statuses\n      // this.studentTrialService.studentStatus$.subscribe((status: StudentTrialStatus) => {\n      if (this.role === this.UserRoles.TEACHER) {\n        this.banners.push({\n          bannerMode: 'primary'\n        });\n      } else {\n        this.banners.push({\n          bannerMode: 'aichat-promo'\n        });\n        this.banners.push({\n          bannerMode: 'social-promo'\n        });\n      }\n      switch (status) {\n        case StudentTrialStatus.HAS_PAYMENT:\n          this.banners.unshift({\n            bannerMode: trialClassRooms.length > 0 ? 'primary-assigned' : 'primary-received'\n          });\n          break;\n        case StudentTrialStatus.ASSIGNED:\n          this.banners.unshift({\n            bannerMode: 'trial-assigned'\n          });\n          break;\n        case StudentTrialStatus.FORWARDED:\n          trialClassRooms.length > 0 ? null : this.banners.unshift({\n            bannerMode: 'trial-received'\n          });\n          break;\n        case StudentTrialStatus.NEW:\n        case StudentTrialStatus.NONE:\n          this.banners.unshift({\n            bannerMode: 'trial-received'\n          });\n          break;\n        case StudentTrialStatus.IGNORED:\n        case StudentTrialStatus.LOST:\n          this.banners.push({\n            bannerMode: 'unreachable'\n          });\n          break;\n        case StudentTrialStatus.GALAXY:\n          break;\n      }\n      if (this.role === this.UserRoles.STUDENT) {\n        // TODO: add when referral system is ready\n        this.banners.unshift({\n          bannerMode: 'primary-offer'\n        });\n      }\n      this.showBanners = true;\n    }\n    isStudentActive(classrooms, hasPayment) {\n      return classrooms.length > 0 && hasPayment;\n    }\n    onAgendaDayClicked(date) {\n      const dateF = new Date(date);\n      this.currentAgendaDate = dateF;\n      const formattedDate = dateF.toLocaleDateString('en-GB');\n      console.log(formattedDate);\n    }\n    adjustHeightListener() {\n      this.subs.sink = this.layoutService.sideMenuHeight.pipe(skip(1)).subscribe(res => {\n        if (res !== 0) {\n          this.templateContext.$implicit.leftMenuHeight = res;\n          this.cdr.detectChanges(); // Manually trigger change detection // TODO: improve\n        }\n      });\n    }\n    getNextStepNumber(classrooms) {\n      if (!classrooms || classrooms.length === 0) {\n        return 1;\n      }\n      const stepToShow = classrooms.some(classroom => this.classroomService.isTrialClassroomWithCompletedLesson(classroom)) ? 3 : 2;\n      return stepToShow;\n    }\n    toggleSwitch(key) {\n      console.log(key);\n      this.resetSwitches();\n      switch (key) {\n        case 'All':\n        case 'Ongoing':\n        case 'Active':\n        case 'Completed':\n        case 'Expired':\n        case 'Dismissed':\n        case 'Freeze':\n          this.switches[key] = !this.switches[key];\n          break;\n      }\n      this.filterLessons();\n    }\n    resetSwitches() {\n      for (const key in this.switches) {\n        this.switches[key] = false;\n      }\n    }\n    /**\n    * Filters the list of lessons based on the selected level and status filters.\n    * @function\n    * @returns {void}\n    */\n    filterLessons(filterOngoing = false) {\n      // Filter the lessons based on the selected switches\n      this.templateContext.$implicit.filteredTrialClassRooms = this.templateContext.$implicit.trialClassRooms.filter(lesson => {\n        let levelFilter;\n        // Filter by \"Ongoing\" switch\n        if (this.switches.Ongoing || filterOngoing) {\n          // Return lessons that have an \"ongoing\" status, \"arranged\" status, or \"pending\" status\n          return (lesson.status.toLowerCase().includes(Status.ON_GOING.toLowerCase()) || lesson.status.toLowerCase().includes(LessonStatus.ARRANGED.toLowerCase()) || lesson.status.toLowerCase().includes(Status.PENDING.toLowerCase())) && (!levelFilter || levelFilter(lesson));\n        }\n        // Filter by \"Completed\" switch\n        if (this.switches.Completed) {\n          // Return lessons that have a \"completed\" status\n          return lesson.status.toLowerCase().includes(Status.COMPLETED.toLowerCase()) && (!levelFilter || levelFilter(lesson));\n        }\n        // Filter by \"Expired\" switch\n        if (this.switches.Expired) {\n          // Return lessons that have an \"expired\" or \"dismissed\" status\n          return (lesson.status.toLowerCase().includes(Status.EXPIRED.toLowerCase()) || lesson.status.toLowerCase().includes(Status.DISMISSED.toLowerCase())) && (!levelFilter || levelFilter(lesson));\n        }\n        // Filter by \"Dismissed\" switch\n        if (this.switches.Dismissed) {\n          // Return lessons that have a \"dismissed\" status\n          return lesson.status.toLowerCase().includes(Status.DISMISSED.toLowerCase()) && (!levelFilter || levelFilter(lesson));\n        }\n        // Filter by \"All\" switch\n        if (this.switches.All) {\n          // Return all lessons\n          return true && (!levelFilter || levelFilter(lesson));\n        }\n        // Return false for all other cases\n        return false;\n      }).reverse();\n    }\n    getOngoingClassrooms() {\n      const classroomsForCheck = this.templateContext.$implicit.filteredTrialClassRooms;\n      const ongoingClassrooms = classroomsForCheck.filter(lesson => {\n        return lesson.status.toLowerCase().includes(Status.ON_GOING.toLowerCase()) || lesson.status.toLowerCase().includes(LessonStatus.ARRANGED.toLowerCase()) || lesson.status.toLowerCase().includes(Status.PENDING.toLowerCase());\n      });\n      return ongoingClassrooms;\n    }\n    loadInitialDataBasedOnRole(role) {\n      if (role === UserRole.STUDENT) {\n        this.loadInitialData();\n      } else {\n        this.loadInitialDataForTeacher();\n      }\n    }\n    initUpdateListener() {\n      this.subs.add(this.calendarService.updateListener.subscribe(res => {\n        if (res) {\n          this.loadInitialDataBasedOnRole(this.role);\n        }\n      }));\n    }\n    static #_ = this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.GeneralService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.LessonService), i0.ɵɵdirectiveInject(i4.BlogService), i0.ɵɵdirectiveInject(i5.TutorialService), i0.ɵɵdirectiveInject(i6.GuideService), i0.ɵɵdirectiveInject(i7.GoalService), i0.ɵɵdirectiveInject(i8.NotificationService), i0.ɵɵdirectiveInject(i9.ClassroomService), i0.ɵɵdirectiveInject(i10.StudentTrialService), i0.ɵɵdirectiveInject(i11.CalendarService), i0.ɵɵdirectiveInject(i12.UserService), i0.ɵɵdirectiveInject(i13.LayoutService), i0.ɵɵdirectiveInject(i14.ToastService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i15.JitsiMeetService), i0.ɵɵdirectiveInject(i16.RatingAndReportService), i0.ɵɵdirectiveInject(i0.Renderer2));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      viewQuery: function DashboardComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n          i0.ɵɵviewQuery(_c1, 7);\n          i0.ɵɵviewQuery(_c2, 7);\n          i0.ɵɵviewQuery(_c3, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ignoredOrLostTrialStatusTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.newOrForwaredTrialStatusTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paidOrActiveTemplate = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.classroomCardsWrapper = _t.first);\n        }\n      },\n      hostBindings: function DashboardComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function DashboardComponent_resize_HostBindingHandler() {\n            return ctx.onResize();\n          }, false, i0.ɵɵresolveWindow)(\"scroll\", function DashboardComponent_scroll_HostBindingHandler($event) {\n            return ctx.onWindowScroll($event);\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      decls: 28,\n      vars: 0,\n      consts: [[\"newOrForwaredTrialStatusTemplate\", \"\"], [\"ignoredOrLostTrialStatusTemplate\", \"\"], [\"paidOrActiveTemplate\", \"\"], [\"classroomCardsTemplate\", \"\"], [\"lessonsBoxTemplate\", \"\"], [\"lessonsBoxTemplateTeacher\", \"\"], [\"teacherTemplate\", \"\"], [\"id\", \"set-goals\", 1, \"modal\", \"no-visibility\"], [1, \"popup-title\"], [\"src\", \"/assets/icons/close.png\", 1, \"close-img\", \"close-img-abs\", \"hvr-glow\", 3, \"click\"], [\"id\", \"edit-goals\", 1, \"modal\", \"no-visibility\"], [4, \"ngIf\"], [1, \"dashboard-wrapper\", \"pt-3\", \"px-2\", \"sm:px-3\", \"md:pt-0\", \"md:px-0\"], [1, \"grid\"], [1, \"m-auto\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [4, \"ngSwitchDefault\"], [4, \"ngTemplateOutlet\"], [1, \"grid\", \"flex\", \"justify-content-between\"], [1, \"col-12\"], [\"indicatorsContentClass\", \"bulletStyle\", 3, \"value\", \"autoplayInterval\", \"verticalViewPortHeight\", \"orientation\", \"numVisible\", \"page\", \"numScroll\", \"showNavigators\", \"showIndicators\", \"circular\", 4, \"ngIf\"], [1, \"col-12\", \"md:col-6\"], [3, \"isSmall\"], [\"header\", \"Notifications\", \"headerBackgroundImage\", \"/assets/images/dashboard/gradient-sm-1.png\", \"blockClass\", \"block-gradient border-radius-bottom-10\", \"containerClass\", \"\", \"actionButtonText\", \"See All\", \"actionButtonRoute\", \"/notifications\"], [\"styleClass\", \"custombar1\"], [3, \"slimStyle\"], [1, \"col-12\", \"md:col-6\", \"h-full\"], [3, \"dayClicked\", \"lessons\"], [3, \"lessons\", \"disableAddNewLessonButton\", \"currentDate\"], [\"color\", \"trial\"], [3, \"activeStep\"], [\"indicatorsContentClass\", \"bulletStyle\", 3, \"value\", \"autoplayInterval\", \"verticalViewPortHeight\", \"orientation\", \"numVisible\", \"page\", \"numScroll\", \"showNavigators\", \"showIndicators\", \"circular\"], [\"pTemplate\", \"item\"], [3, \"bannerMode\"], [\"header\", \"Notifications\", \"headerBackgroundImage\", \"/assets/images/dashboard/gradient-sm-1.png\", \"blockClass\", \"block-gradient border-round-xl h-full\", \"containerClass\", \"\", \"actionButtonText\", \"See All\", \"actionButtonRoute\", \"/notifications\", 3, \"containerHeight\"], [3, \"formSubmitted\", 4, \"ngIf\"], [1, \"classroom-cards-space\"], [3, \"formSubmitted\"], [\"class\", \"col-12\", 4, \"ngIf\"], [\"headerBlockClass\", \"bg-cover \", \"header\", \"Notifications\", \"headerBackgroundImage\", \"/assets/images/dashboard/gradient-sm-1.png\", \"blockClass\", \"block-gradient border-radius-bottom-10 h-full\", \"containerClass\", \"\", \"actionButtonText\", \"See All\", \"actionButtonRoute\", \"/notifications\", 3, \"containerHeight\"], [1, \"mt-5\"], [\"indicatorsContentClass\", \"bulletStyle\", 3, \"value\", \"autoplayInterval\", \"responsiveOptions\", \"verticalViewPortHeight\", \"numVisible\", \"page\", \"numScroll\", \"showNavigators\", \"showIndicators\", \"circular\", 4, \"ngIf\"], [\"indicatorsContentClass\", \"bulletStyle\", 3, \"value\", \"autoplayInterval\", \"responsiveOptions\", \"verticalViewPortHeight\", \"numVisible\", \"page\", \"numScroll\", \"showNavigators\", \"showIndicators\", \"circular\"], [3, \"classRoom\", \"teacher\", \"bannerMode\"], [\"header\", \"Notifications\", \"headerBackgroundImage\", \"/assets/images/dashboard/gradient-sm-1.png\", \"blockClass\", \"block-gradient border-radius-bottom-10\", \"containerHeight\", \"118\", \"containerClass\", \"\", \"actionButtonText\", \"See All\", \"actionButtonRoute\", \"/notifications\"], [\"indicatorsContentClass\", \"bulletStyle\", 3, \"value\", \"autoplayInterval\", \"verticalViewPortHeight\", \"showNavigators\", \"showIndicators\", \"circular\", 4, \"ngIf\"], [\"headerBlockClass\", \"bg-cover \", \"header\", \"Coming up\", \"headerBackgroundImage\", \"/assets/images/dashboard/gradient-sm-1.png\", \"blockClass\", \"block-gradient relative h-full border-radius-bottom-10\", \"containerClass\", \"\", 3, \"containerHeight\"], [3, \"lessons\", \"todayLessons\"], [\"header\", \"Notifications\", \"headerBackgroundImage\", \"/assets/images/dashboard/gradient-sm-1.png\", \"blockClass\", \"block-gradient border-radius-bottom-10 h-full\", \"containerClass\", \"\", \"actionButtonText\", \"See All\", \"actionButtonRoute\", \"/notifications\"], [\"indicatorsContentClass\", \"bulletStyle\", 3, \"value\", \"autoplayInterval\", \"verticalViewPortHeight\", \"showNavigators\", \"showIndicators\", \"circular\"], [3, \"classRoom\", \"bannerMode\"], [3, \"activeStep\", \"stepOneInfo\", \"stepThreeInfo\"], [\"id\", \"classroomBox\", 1, \"relative\", \"h-full\"], [1, \"relative\"], [1, \"border-round-xl\", \"overflow-hidden\", \"mt-4\"], [1, \"class-filters\", \"border-round-xl\"], [1, \"image-container\"], [\"src\", \"/assets/images/dashboard/class-filters-cut.svg\", \"alt\", \"class-filters img\", 1, \"md:block\"], [1, \"text-overlay\"], [1, \"font-3xl\"], [1, \"filters-content\", \"bg-white\", \"border-round-xl\"], [1, \"block\", \"relative\", \"mt-4\", \"md:mt-0\", \"mx-2\", \"md:mx-0\", 3, \"switchToggled\", \"isTrial\", \"switches\"], [4, \"ngFor\", \"ngForOf\"], [3, \"ngClass\"], [\"color\", \"expired\", 3, \"classRoom\"], [1, \"m-1\", \"h-full\"], [1, \"flex\", \"h-full\", \"align-items-center\", \"justify-content-center\", \"flex-column\"], [\"src\", \"/assets/icons/empty-classroom.png\", \"height\", \"280\"], [\"color\", \"expired\", 1, \"sm:pb-2\", \"block\", 3, \"classRoom\"], [\"header\", \" \", \"headerBackgroundImage\", \"/assets/images/dashboard/gradient-sm-1.png\", \"blockClass\", \"relative card\", \"containerClass\", \"\", 3, \"containerHeight\"], [3, \"scrollHeight\", \"classRooms\", \"lessons\", \"disableAddNewLessonButton\"], [3, \"classRooms\", \"lessons\", \"disableAddNewLessonButton\"], [3, \"lessons\"], [\"headerBlockClass\", \"bg-cover \", \"header\", \"Notifications\", \"headerBackgroundImage\", \"/assets/images/dashboard/gradient-sm-1.png\", \"blockClass\", \"block-gradient border-radius-bottom-10\", \"containerClass\", \"\", \"actionButtonText\", \"See All\", \"actionButtonRoute\", \"/notifications\", 3, \"containerHeight\"], [1, \"grid\", \"flex\", \"justify-content-between\", \"h-full\", \"flex-wrap\"], [3, \"classrooms\"], [3, \"teacher\", \"bannerMode\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵtemplate(0, DashboardComponent_Defer_0_Template, 3, 2)(1, DashboardComponent_DeferPlaceholder_1_Template, 2, 0);\n          i0.ɵɵdefer(2, 0, DashboardComponent_Defer_2_DepsFn, null, 1);\n          i0.ɵɵdeferOnViewport(0, -1);\n          i0.ɵɵelementStart(4, \"div\", 7)(5, \"div\", 8)(6, \"div\");\n          i0.ɵɵtext(7, \"Set new goal\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"img\", 9);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_img_click_8_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleSetGoals());\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"div\", 10)(10, \"div\", 8)(11, \"div\");\n          i0.ɵɵtext(12, \"Edit goals\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"img\", 9);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_img_click_13_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.toggleEditGoals());\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(14, DashboardComponent_ng_template_14_Template, 21, 17, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(16, DashboardComponent_ng_template_16_Template, 12, 5, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(18, DashboardComponent_ng_template_18_Template, 18, 18, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(20, DashboardComponent_ng_template_20_Template, 1, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(22, DashboardComponent_ng_template_22_Template, 1, 1, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor)(24, DashboardComponent_ng_template_24_Template, 2, 2, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor)(26, DashboardComponent_ng_template_26_Template, 19, 14, \"ng-template\", null, 6, i0.ɵɵtemplateRefExtractor);\n        }\n      },\n      dependencies: [i17.NgClass, i17.NgForOf, i17.NgIf, i17.NgTemplateOutlet, i18.NextLessonComponent, i19.BlockViewerComponent, i20.CalendarAgendaComponent, i21.LessonScheduleBoxComponent, i22.GoalStepsBoxComponent, i23.TextCallToActionButtonComponent, i24.ClassroomCardComponent, i25.PrimeTemplate, i26.ClassroomStatusFiltersComponent, i27.ScrollPanel, i28.Carousel, i29.IntroGradientComponent, i30.DashboardNotificationsComponent, i31.DashboardNextStepsComponent, i32.DashboardBenefitsComponent, i33.CalendarPreviewWeekComponent, i34.DashboardContactUsFormComponent, i35.DashboardTeacherClassroomsComponent],\n      styles: [\".burger-menu[_ngcontent-%COMP%]{width:24px;height:24px;cursor:pointer}.burger-menu[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]{position:relative}.burger-menu[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]   .burger-inner[_ngcontent-%COMP%]{position:absolute;width:22px;left:0;top:11px;border:1px solid #ffffff;transition:border-color .2s ease-in .2s}.burger-menu[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]   .burger-inner[_ngcontent-%COMP%]:before{position:absolute;content:\\\"\\\";top:-6px;left:-1px;width:22px;border:1px solid #ffffff;transition:top .2s ease-in .3s,transform ease-in .2s}.burger-menu[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]   .burger-inner[_ngcontent-%COMP%]:after{position:absolute;content:\\\"\\\";top:4px;left:-1px;width:22px;border:1px solid #ffffff;transition:top .2s ease-in .3s,transform ease-in .2s}.burger-menu.active[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]   .burger-inner[_ngcontent-%COMP%]{border-color:transparent;transition:border-color ease-in .1s}.burger-menu.active[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]   .burger-inner[_ngcontent-%COMP%]:before{top:-1px;transform:rotate(-45deg);transition:top ease-out .2s,transform .2s ease-out .3s}.burger-menu.active[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]   .burger-inner[_ngcontent-%COMP%]:after{top:-1px;transform:rotate(45deg);transition:top ease-out .2s,transform .2s ease-out .3s}.responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}.circled[_ngcontent-%COMP%]{border-radius:50%;width:34px;height:34px;border:1px solid #2E3D90;color:#000;text-align:center;font-size:20px;vertical-align:middle;display:flex;align-items:center;justify-content:center}.p-datepicker-inline[_ngcontent-%COMP%]{width:100%}[_nghost-%COMP%]     .p-calendar.calendar-control{opacity:1}[_nghost-%COMP%]     .p-calendar.calendar-control .p-datepicker{border:0;background:linear-gradient(#fff,#3873f41c)}[_nghost-%COMP%]     .p-calendar.calendar-control .p-datepicker table td{padding:.5rem}[_nghost-%COMP%]     .p-calendar.calendar-control .p-datepicker .p-datepicker-header{border-bottom:0 none;position:absolute;top:-54px;right:0;background:transparent;color:#fff}[_nghost-%COMP%]     .p-calendar.calendar-control .p-datepicker .p-datepicker-header .p-datepicker-prev, [_nghost-%COMP%]     .p-calendar.calendar-control .p-datepicker .p-datepicker-header .p-datepicker-next{color:#fff}[_nghost-%COMP%]     .p-calendar.calendar-control .p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-month, [_nghost-%COMP%]     .p-calendar.calendar-control .p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-year{color:#fff}[_nghost-%COMP%]     .p-calendar.calendar-control .p-datepicker table td>span{overflow:visible}.dashboard[_ngcontent-%COMP%]{padding:5px 22px 22px;border-radius:10px;background-color:var(--white);width:var(--page-width);box-sizing:border-box;margin-top:10px;position:relative}.first-section[_ngcontent-%COMP%]{position:relative;display:flex;margin-top:10px;font-size:15px}.left[_ngcontent-%COMP%], .right[_ngcontent-%COMP%]{position:relative;width:50%;box-sizing:border-box;background-color:#fff;border-radius:10px;padding:15px;min-height:150px;max-height:150px;overflow-y:scroll;overflow-x:hidden}.left[_ngcontent-%COMP%]{margin-right:5px}.left-title[_ngcontent-%COMP%], .left-title-student[_ngcontent-%COMP%]{width:100%;display:flex;justify-content:space-between;align-items:center}.left-title[_ngcontent-%COMP%]   .left-title-student-text[_ngcontent-%COMP%], .left-title-student[_ngcontent-%COMP%]   .left-title-student-text[_ngcontent-%COMP%]{border-bottom:1px solid var(--main-color);width:100%;font-weight:700}.left-title[_ngcontent-%COMP%]   .gray-border-button[_ngcontent-%COMP%], .left-title-student[_ngcontent-%COMP%]   .gray-border-button[_ngcontent-%COMP%]{padding:5px}.left-title[_ngcontent-%COMP%]{border-bottom:1px solid var(--main-color)}.notification[_ngcontent-%COMP%]{display:flex;padding:10px;align-items:center;cursor:pointer}.notification[_ngcontent-%COMP%]   .lesson-circle[_ngcontent-%COMP%]{margin-right:10px;width:10px;height:10px;border-radius:50%}.first-section-lesson[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin:10px;align-items:center}.first-section-lesson[_ngcontent-%COMP%]   .lesson-circle[_ngcontent-%COMP%]{width:10px;height:10px;border-radius:50%}.first-section-lesson[_ngcontent-%COMP%]   .lesson-title[_ngcontent-%COMP%]{margin-left:10px;width:30%}.first-section-lesson[_ngcontent-%COMP%]   .lesson-hours[_ngcontent-%COMP%]{width:20%}.first-section-lesson[_ngcontent-%COMP%]   .gray-border-button[_ngcontent-%COMP%]{font-size:15px;padding:5px;border-radius:12px;box-sizing:border-box;text-align:center;width:30%}.right[_ngcontent-%COMP%]{margin-left:5px}.gradient[_ngcontent-%COMP%], .gradient-blog[_ngcontent-%COMP%]{position:absolute}.gradient[_ngcontent-%COMP%]{width:calc(var(--app-content-width) + var(--app-content-width) / 1.1);top:-400px;right:calc(-1 * var(--app-content-width) / 1.2);z-index:-1}.gradient-blog[_ngcontent-%COMP%]{width:calc(var(--app-content-width) + var(--app-content-width) / 1.1);top:-380px;left:calc(-1 * var(--app-content-width) * 1.2)}.classrooms-big-title[_ngcontent-%COMP%], .blog-big-title[_ngcontent-%COMP%]{font-size:120px;transform-origin:0 0;transform:rotate(90deg);position:absolute;top:0;font-weight:700;color:var(--main-color);color:#fff}.rotate-minus-90[_ngcontent-%COMP%]{transform:rotate(-90deg)}.blog[_ngcontent-%COMP%]{display:flex}.blog[_ngcontent-%COMP%]   .blog-left-side[_ngcontent-%COMP%], .blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]{height:723px}.blog[_ngcontent-%COMP%]   .blog-left-side[_ngcontent-%COMP%]{color:#fff;padding:15px;box-sizing:border-box;width:700px;background-size:cover;border-radius:12px 0 0 12px;position:relative;font-size:15px}.blog[_ngcontent-%COMP%]   .blog-left-side[_ngcontent-%COMP%]   .blog-left-side-title[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.blog[_ngcontent-%COMP%]   .blog-left-side[_ngcontent-%COMP%]   .blog-left-side-title[_ngcontent-%COMP%]   .blog-title-content[_ngcontent-%COMP%]{display:flex;align-items:center}.blog[_ngcontent-%COMP%]   .blog-left-side[_ngcontent-%COMP%]   .blog-left-side-title[_ngcontent-%COMP%]   .blog-title-content[_ngcontent-%COMP%]   .content-text[_ngcontent-%COMP%]{margin-left:15px}.blog[_ngcontent-%COMP%]   .blog-left-side[_ngcontent-%COMP%]   .main-info[_ngcontent-%COMP%]{position:absolute;bottom:15px}.blog[_ngcontent-%COMP%]   .blog-left-side[_ngcontent-%COMP%]   .main-info[_ngcontent-%COMP%]   .main-info-date[_ngcontent-%COMP%]{font-weight:700}.blog[_ngcontent-%COMP%]   .blog-left-side[_ngcontent-%COMP%]   .main-info[_ngcontent-%COMP%]   .main-info-title[_ngcontent-%COMP%]{font-size:60px;font-weight:700}.blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]{border-radius:0 12px 12px 0;width:363px;font-size:15px;z-index:90}.blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]{width:100%;height:221px;background:#fdfdfe;filter:drop-shadow(0px 3px 6px rgba(0,0,0,.16));padding:10px}.blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   [class$=-button][_ngcontent-%COMP%]{padding:5px}.blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]{display:flex}.blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-content-left[_ngcontent-%COMP%]{width:60%}.blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-content-left[_ngcontent-%COMP%]   .item-content-date[_ngcontent-%COMP%], .blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-content-left[_ngcontent-%COMP%]   .item-content-description[_ngcontent-%COMP%]{color:#93949e;margin-top:15px}.blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-content-left[_ngcontent-%COMP%]   .item-content-date[_ngcontent-%COMP%]{font-size:12px}.blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-content-left[_ngcontent-%COMP%]   .item-content-title[_ngcontent-%COMP%]{font-weight:700;margin-top:15px}.blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-content-left[_ngcontent-%COMP%]   .item-content-tags[_ngcontent-%COMP%]{display:flex;align-items:center;margin-top:15px;font-size:12px}.blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-content-right[_ngcontent-%COMP%]{font-size:12px;width:40%}.blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]   .item-1[_ngcontent-%COMP%]{border-top-right-radius:12px}.blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]   .item-3[_ngcontent-%COMP%]{border-bottom-right-radius:12px}.tag[_ngcontent-%COMP%]{margin-left:15px}.tag[_ngcontent-%COMP%]:first-child{margin-left:0}.section[_ngcontent-%COMP%]{background-color:var(--white);border-radius:28px;margin-top:0}.section[_ngcontent-%COMP%]   .section-content[_ngcontent-%COMP%]{overflow:hidden;transition:height .3s ease-out;margin:10px;height:0}.section-title[_ngcontent-%COMP%]{width:100%;border:1px solid #444070;font-size:25px;text-align:center;padding:15px 0;box-sizing:border-box;font-family:Raleway-Bold;border-radius:28px;position:relative;background:#fff}.section-title[_ngcontent-%COMP%]   .toggle-section[_ngcontent-%COMP%]{position:absolute;top:9px;left:10px}.section-title[_ngcontent-%COMP%]   .toggle-section[_ngcontent-%COMP%]   .section-arrow[_ngcontent-%COMP%]{position:absolute;overflow:hidden;transition-duration:.2s;transition-property:transform;cursor:pointer}.tutorials-title[_ngcontent-%COMP%]{width:100%;border-bottom:1px solid var(--main-color);font-weight:700;margin-bottom:15px;padding-bottom:10px}.guide[_ngcontent-%COMP%]{margin-top:10px;position:relative}.guide[_ngcontent-%COMP%]   .guide-main[_ngcontent-%COMP%]{display:flex;align-items:center;margin-top:10px;position:relative}.guide[_ngcontent-%COMP%]   .guide-pdf[_ngcontent-%COMP%]{margin-top:30px;border-radius:30px;transition-duration:.2s;transition-property:transform}.guide-right[_ngcontent-%COMP%]{margin-left:auto;display:flex}.guide-right[_ngcontent-%COMP%]   .main-color-button[_ngcontent-%COMP%]{margin-left:10px}.blog-title[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin:10px 0;position:relative}.input-field[_ngcontent-%COMP%], .input-fields[_ngcontent-%COMP%]{width:100%;flex-basis:100%}.suggest-form[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center}.history-title[_ngcontent-%COMP%], .history-list[_ngcontent-%COMP%]{display:flex;margin-top:15px}.history-title[_ngcontent-%COMP%]   .w50[_ngcontent-%COMP%], .history-list[_ngcontent-%COMP%]   .w50[_ngcontent-%COMP%]{width:50%}.history-title[_ngcontent-%COMP%]   .w30[_ngcontent-%COMP%], .history-list[_ngcontent-%COMP%]   .w30[_ngcontent-%COMP%]{width:30%}.history-title[_ngcontent-%COMP%]   .w20[_ngcontent-%COMP%], .history-list[_ngcontent-%COMP%]   .w20[_ngcontent-%COMP%]{width:20%}.history-title[_ngcontent-%COMP%]{color:#93949e}.modal[_ngcontent-%COMP%]{width:60%}.recommend-list[_ngcontent-%COMP%]{display:none;border-radius:12px;background-color:#fff;border:1px solid var(--main-color);position:absolute;top:230px;right:0;z-index:1;flex-direction:column;max-height:300px;overflow:auto}.recommend-list[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{color:#93949e;margin-top:10px}.recommend-list[_ngcontent-%COMP%]   .recommend-list-item[_ngcontent-%COMP%]{margin-top:10px}.rec-button[_ngcontent-%COMP%]{position:sticky;bottom:0;margin-top:10px}.goal-burger[_ngcontent-%COMP%]{margin-left:10px;position:relative;border-radius:8px;width:30px;height:30px;padding:5px 0;border:1px solid var(--main-color);display:flex;align-items:center;box-sizing:border-box;justify-content:center;cursor:pointer}.goal-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%]{width:3px;height:3px;border-radius:50%;background-color:var(--main-color);margin:2px}.goal-burger[_ngcontent-%COMP%]:hover{background-color:var(--main-color)}.goal-burger[_ngcontent-%COMP%]:hover   .inner-circle[_ngcontent-%COMP%]{background-color:#fff}.goal-menu[_ngcontent-%COMP%]{position:absolute;right:0;top:0;background-color:#fff;border-radius:8px;border:1px solid lightgray;padding:10px}.goal-menu[_ngcontent-%COMP%]   .goal-actions[_ngcontent-%COMP%]{margin-top:15px}.goal-menu[_ngcontent-%COMP%]   .goal-actions[_ngcontent-%COMP%]   .link-main-color[_ngcontent-%COMP%]{margin:5px 0}[_nghost-%COMP%]     .bulletStyle{padding:.5rem}[_nghost-%COMP%]     .bulletStyle .p-carousel-indicator{margin-bottom:0}[_nghost-%COMP%]     .bulletStyle .p-carousel-indicator.p-highlight button{background-color:#283583!important}[_nghost-%COMP%]     .bulletStyle .p-carousel-indicator button{background:#aaa!important;border-radius:100px;height:8px;width:8px}.class-filters[_ngcontent-%COMP%]{position:relative;z-index:3;width:100%;height:auto;color:#666;text-align:center;box-sizing:border-box;border:6px solid transparent;background-clip:padding-box,border-box;background-origin:padding-box,border-box;background-image:linear-gradient(#fff,#fff),linear-gradient(#8497ff,#2e3d90);padding-bottom:1rem}@media only screen and (min-width: 768px){.class-filters[_ngcontent-%COMP%]{padding:1rem}}@media only screen and (min-width: 576px){.class-filters[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%]{padding-left:11rem}}.class-filters[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]{position:absolute;display:inline-block;left:-35px;top:-10px}@media only screen and (max-width: 768px){.class-filters[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]{position:relative;left:auto;width:100%;top:auto;height:54px;background-image:linear-gradient(180deg,#8597ff,#788bf2,#7182e5,#6877d9,#606fcd 44%,#5462c0 56%,#4b59b4,#424fa9,#39489d,#2d3c8f)}}.class-filters[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{display:none;width:100%;height:auto}.class-filters[_ngcontent-%COMP%]   .text-overlay[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);text-align:center;color:#fff;padding:10px}.class-filters[_ngcontent-%COMP%]   .text-overlay[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{margin:0}[_nghost-%COMP%]     .small-input-switch .p-inputswitch{height:1rem;width:1.8rem}[_nghost-%COMP%]     .small-input-switch .p-inputswitch.p-inputswitch-checked .p-inputswitch-slider:before{transform:translate(1rem);background-image:linear-gradient(90deg,#6f89fb,#647cf7,#5e77f3,#526cef,#4861ea 44%,#415ae6 56%,#3751e1,#2c4add,#1f3dd6,#0b36d0)}[_nghost-%COMP%]     .small-input-switch .p-inputswitch .p-inputswitch-slider{background-image:linear-gradient(90deg,#e4e8f6,#dfe4f6,#dbe1f5,#d6dcf5,#cdd5f3 44%,#c9d1f3 56%,#c4cdf3,#c0caf2,#bbc6f2,#b7c2f1)}[_nghost-%COMP%]     .small-input-switch .p-inputswitch .p-inputswitch-slider:before{width:.8rem;height:.8rem;margin-top:-.4rem}\"]\n    });\n  }\n  return DashboardComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}