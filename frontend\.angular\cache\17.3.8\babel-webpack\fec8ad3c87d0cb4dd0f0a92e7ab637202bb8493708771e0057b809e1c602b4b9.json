{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Czech [cs]\n//! author : petrbela : https://github.com/petrbela\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var months = {\n      standalone: 'leden_únor_březen_duben_květen_červen_červenec_srpen_září_říjen_listopad_prosinec'.split('_'),\n      format: 'ledna_února_března_dubna_května_června_července_srpna_září_října_listopadu_prosince'.split('_'),\n      isFormat: /DD?[o.]?(\\[[^\\[\\]]*\\]|\\s)+MMMM/\n    },\n    monthsShort = 'led_úno_bře_dub_kvě_čvn_čvc_srp_zář_říj_lis_pro'.split('_'),\n    monthsParse = [/^led/i, /^úno/i, /^bře/i, /^dub/i, /^kvě/i, /^(čvn|červen$|června)/i, /^(čvc|červenec|července)/i, /^srp/i, /^zář/i, /^říj/i, /^lis/i, /^pro/i],\n    // NOTE: 'červen' is substring of 'červenec'; therefore 'červenec' must precede 'červen' in the regex to be fully matched.\n    // Otherwise parser matches '1. červenec' as '1. červen' + 'ec'.\n    monthsRegex = /^(leden|únor|březen|duben|květen|červenec|července|červen|června|srpen|září|říjen|listopad|prosinec|led|úno|bře|dub|kvě|čvn|čvc|srp|zář|říj|lis|pro)/i;\n  function plural(n) {\n    return n > 1 && n < 5 && ~~(n / 10) !== 1;\n  }\n  function translate(number, withoutSuffix, key, isFuture) {\n    var result = number + ' ';\n    switch (key) {\n      case 's':\n        // a few seconds / in a few seconds / a few seconds ago\n        return withoutSuffix || isFuture ? 'pár sekund' : 'pár sekundami';\n      case 'ss':\n        // 9 seconds / in 9 seconds / 9 seconds ago\n        if (withoutSuffix || isFuture) {\n          return result + (plural(number) ? 'sekundy' : 'sekund');\n        } else {\n          return result + 'sekundami';\n        }\n      case 'm':\n        // a minute / in a minute / a minute ago\n        return withoutSuffix ? 'minuta' : isFuture ? 'minutu' : 'minutou';\n      case 'mm':\n        // 9 minutes / in 9 minutes / 9 minutes ago\n        if (withoutSuffix || isFuture) {\n          return result + (plural(number) ? 'minuty' : 'minut');\n        } else {\n          return result + 'minutami';\n        }\n      case 'h':\n        // an hour / in an hour / an hour ago\n        return withoutSuffix ? 'hodina' : isFuture ? 'hodinu' : 'hodinou';\n      case 'hh':\n        // 9 hours / in 9 hours / 9 hours ago\n        if (withoutSuffix || isFuture) {\n          return result + (plural(number) ? 'hodiny' : 'hodin');\n        } else {\n          return result + 'hodinami';\n        }\n      case 'd':\n        // a day / in a day / a day ago\n        return withoutSuffix || isFuture ? 'den' : 'dnem';\n      case 'dd':\n        // 9 days / in 9 days / 9 days ago\n        if (withoutSuffix || isFuture) {\n          return result + (plural(number) ? 'dny' : 'dní');\n        } else {\n          return result + 'dny';\n        }\n      case 'M':\n        // a month / in a month / a month ago\n        return withoutSuffix || isFuture ? 'měsíc' : 'měsícem';\n      case 'MM':\n        // 9 months / in 9 months / 9 months ago\n        if (withoutSuffix || isFuture) {\n          return result + (plural(number) ? 'měsíce' : 'měsíců');\n        } else {\n          return result + 'měsíci';\n        }\n      case 'y':\n        // a year / in a year / a year ago\n        return withoutSuffix || isFuture ? 'rok' : 'rokem';\n      case 'yy':\n        // 9 years / in 9 years / 9 years ago\n        if (withoutSuffix || isFuture) {\n          return result + (plural(number) ? 'roky' : 'let');\n        } else {\n          return result + 'lety';\n        }\n    }\n  }\n  var cs = moment.defineLocale('cs', {\n    months: months,\n    monthsShort: monthsShort,\n    monthsRegex: monthsRegex,\n    monthsShortRegex: monthsRegex,\n    // NOTE: 'červen' is substring of 'červenec'; therefore 'červenec' must precede 'červen' in the regex to be fully matched.\n    // Otherwise parser matches '1. červenec' as '1. červen' + 'ec'.\n    monthsStrictRegex: /^(leden|ledna|února|únor|březen|března|duben|dubna|květen|května|červenec|července|červen|června|srpen|srpna|září|říjen|října|listopadu|listopad|prosinec|prosince)/i,\n    monthsShortStrictRegex: /^(led|úno|bře|dub|kvě|čvn|čvc|srp|zář|říj|lis|pro)/i,\n    monthsParse: monthsParse,\n    longMonthsParse: monthsParse,\n    shortMonthsParse: monthsParse,\n    weekdays: 'neděle_pondělí_úterý_středa_čtvrtek_pátek_sobota'.split('_'),\n    weekdaysShort: 'ne_po_út_st_čt_pá_so'.split('_'),\n    weekdaysMin: 'ne_po_út_st_čt_pá_so'.split('_'),\n    longDateFormat: {\n      LT: 'H:mm',\n      LTS: 'H:mm:ss',\n      L: 'DD.MM.YYYY',\n      LL: 'D. MMMM YYYY',\n      LLL: 'D. MMMM YYYY H:mm',\n      LLLL: 'dddd D. MMMM YYYY H:mm',\n      l: 'D. M. YYYY'\n    },\n    calendar: {\n      sameDay: '[dnes v] LT',\n      nextDay: '[zítra v] LT',\n      nextWeek: function () {\n        switch (this.day()) {\n          case 0:\n            return '[v neděli v] LT';\n          case 1:\n          case 2:\n            return '[v] dddd [v] LT';\n          case 3:\n            return '[ve středu v] LT';\n          case 4:\n            return '[ve čtvrtek v] LT';\n          case 5:\n            return '[v pátek v] LT';\n          case 6:\n            return '[v sobotu v] LT';\n        }\n      },\n      lastDay: '[včera v] LT',\n      lastWeek: function () {\n        switch (this.day()) {\n          case 0:\n            return '[minulou neděli v] LT';\n          case 1:\n          case 2:\n            return '[minulé] dddd [v] LT';\n          case 3:\n            return '[minulou středu v] LT';\n          case 4:\n          case 5:\n            return '[minulý] dddd [v] LT';\n          case 6:\n            return '[minulou sobotu v] LT';\n        }\n      },\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'za %s',\n      past: 'před %s',\n      s: translate,\n      ss: translate,\n      m: translate,\n      mm: translate,\n      h: translate,\n      hh: translate,\n      d: translate,\n      dd: translate,\n      M: translate,\n      MM: translate,\n      y: translate,\n      yy: translate\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}\\./,\n    ordinal: '%d.',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return cs;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}