{"ast": null, "code": "import { formatDate } from '@angular/common';\nimport { PackageType } from 'src/app/core/models/package.model';\nimport { SubSink } from 'subsink';\nimport * as moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nimport * as i2 from \"src/app/core/services/package.service\";\nimport * as i3 from \"src/app/core/services/auth.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"../../../shared/layout/g-level-circle/g-level-circle.component\";\nimport * as i7 from \"../../../shared/classroom/classroom-status-filters/classroom-status-filters.component\";\nimport * as i8 from \"./package-accordion-item/package-accordion-item.component\";\nimport * as i9 from \"./package-content-info/package-content-info.component\";\nconst _c0 = a0 => ({\n  \"trial-gradient\": a0\n});\nconst _c1 = (a0, a1) => ({\n  \"sm:col-1\": a0,\n  \"sm:col-2\": a1\n});\nfunction PackagesComponent_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function PackagesComponent_ng_container_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.generalService.navigateToBuyPackage(!ctx_r1.generalService.isNullishObject(ctx_r1.classroom) ? ctx_r1.classroom : null));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction PackagesComponent_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 9)(2, \"div\", 10)(3, \"strong\");\n    i0.ɵɵtext(4, \"There are no packages available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"img\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 12)(7, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function PackagesComponent_ng_container_8_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.generalService.navigateToBuyPackage());\n    });\n    i0.ɵɵelementStart(8, \"span\", 14);\n    i0.ɵɵtext(9, \"Buy Package\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"img\", 15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction PackagesComponent_ng_container_10_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 20);\n    i0.ɵɵtext(2, \"Language\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const package_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(package_r4.packLanguage.trim() !== \"\" ? package_r4.packLanguage : \"-\");\n  }\n}\nfunction PackagesComponent_ng_container_10_ng_container_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-package-content-info\", 38);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const package_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"type\", \"Package Expiration\")(\"date\", ctx_r1.formatDate(package_r4.expiresOn));\n  }\n}\nfunction PackagesComponent_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 16)(2, \"app-package-accordion-item\")(3, \"div\", 17)(4, \"div\", 18)(5, \"div\", 19)(6, \"div\", 20);\n    i0.ɵɵtext(7, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 21);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(10, PackagesComponent_ng_container_10_div_10_Template, 5, 1, \"div\", 22);\n    i0.ɵɵelementStart(11, \"div\", 23)(12, \"div\", 20);\n    i0.ɵɵtext(13, \"Total hours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 24);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 25)(17, \"div\", 20);\n    i0.ɵɵtext(18, \"Hours Left\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 24);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 25)(22, \"div\", 20);\n    i0.ɵɵtext(23, \"Level\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 26);\n    i0.ɵɵelement(25, \"g-level-circle\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 25)(27, \"div\", 20);\n    i0.ɵɵtext(28, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 21);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 25)(32, \"div\", 20);\n    i0.ɵɵtext(33, \"Expires on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 21);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 28)(37, \"div\", 20);\n    i0.ɵɵtext(38, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 29);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(41, \"div\", 30)(42, \"div\", 31)(43, \"div\", 32);\n    i0.ɵɵtext(44, \" Events \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"div\", 33)(46, \"div\", 34);\n    i0.ɵɵtext(47, \" Type \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"div\", 35);\n    i0.ɵɵtext(49, \" Duration \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 35);\n    i0.ɵɵtext(51, \" From \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 36);\n    i0.ɵɵtext(53, \" To \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"div\", 37);\n    i0.ɵɵtext(55, \" Date \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(56, \"app-package-content-info\", 38);\n    i0.ɵɵtemplate(57, PackagesComponent_ng_container_10_ng_container_57_Template, 2, 2, \"ng-container\", 6);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const package_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(13, _c0, package_r4.type === \"Trial\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.authService.isStudent ? package_r4.classroomTeacher : ctx_r1.generalService.getPersonFullName(package_r4.paidByStudent));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isStudent);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(15, _c1, ctx_r1.authService.isStudent, ctx_r1.authService.isTeacher));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(package_r4.totalHours);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(package_r4.hoursLeft);\n    i0.ɵɵadvance(5);\n    i0.ɵɵpropertyInterpolate(\"name\", package_r4.packageLevel === \"\" || package_r4.packageLevel === \"No Lesson\" || package_r4.packageLevel === \"No Classroom\" ? \"TBD\" : package_r4.packageLevel);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(package_r4.type);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(package_r4.type === \"Trial\" ? \"-\" : ctx_r1.formatDate(package_r4.expiresOn));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(package_r4.state.toLowerCase());\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"type\", package_r4.type === \"Trial\" ? \"Trial Created\" : \"Package Purchase\")(\"date\", ctx_r1.formatDate(package_r4.purchasedAt));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", package_r4.type !== \"Trial\");\n  }\n}\nexport class PackagesComponent {\n  constructor(generalService, packageService, authService, ngZone) {\n    this.generalService = generalService;\n    this.packageService = packageService;\n    this.authService = authService;\n    this.ngZone = ngZone;\n    this.classroom = {};\n    this.subs = new SubSink();\n    this.packages = [];\n    this.filteredPackages = [];\n    this.viewImgSrc = \"/assets/icons/down.svg\";\n    this.filtersOpen = false;\n    this.packageFilterHelper = this.packageService.packageFilterHelper;\n    this.packageFilter = this.packageService.getCurrentPackageFilter();\n    this.packageFilterWithoutTheCurrent = this.packageService.getPackageFilterWithoutTheCurrent();\n    this.packageFilterSwitches = [{\n      label: 'Classrooms',\n      value: false\n    }, {\n      label: PackageType.TRIAL,\n      value: false\n    }, {\n      label: PackageType.GIFT,\n      value: false\n    }, {\n      label: 'All',\n      value: true\n    }];\n  }\n  ngOnInit() {\n    this.getPackages();\n  }\n  ngAfterViewInit() {}\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  getPackages() {\n    this.subs.sink = this.packageService.getAllUserPackages(this.authService.getUserId()).subscribe(res => {\n      console.log(res);\n      this.packages = res;\n      this.filteredPackages = res;\n      this.toggleSwitch('All');\n    });\n  }\n  getDurationInDays(date1, date2) {\n    return this.generalService.getDatesDiff(date1, date2).days;\n  }\n  togglePackage(event, viewImgElement, packageElement, viewImgElementSrc) {\n    let id = packageElement.getAttribute('id');\n    let setOpen = viewImgElement.getAttribute('open') === 'false' ? 'true' : 'false';\n    var section = document.getElementById(id);\n    viewImgElement.setAttribute('open', setOpen);\n    if (setOpen === 'true') {\n      console.log(\"open\");\n      viewImgElementSrc.style.transform = \"rotate(180deg)\";\n      this.generalService.expandSection(section);\n    } else {\n      viewImgElementSrc.style.transform = \"rotate(0)\";\n      this.generalService.collapseSection(section);\n    }\n  }\n  toggleFilters() {\n    if (this.filtersOpen) {\n      this.generalService.slideOutElement('blur_bg');\n      this.generalService.slideOutElement('package-filters');\n    } else {\n      this.generalService.slideInElement('blur_bg');\n      this.generalService.slideInElement('package-filters');\n    }\n    this.filtersOpen = !this.filtersOpen;\n  }\n  getDateLessonLevelEnds(lesson_level_ended) {\n    if (lesson_level_ended.toString().includes('1970')) {\n      return 'present';\n    } else {\n      return formatDate(lesson_level_ended, 'dd-MM-yyyy', 'en_US');\n    }\n  }\n  formatDate(dateString) {\n    const date = moment(dateString);\n    return date.format('D/MM/YYYY');\n  }\n  toggleSwitch(event) {\n    this.resetSwitches();\n    switch (event) {\n      case PackageType.GIFT:\n      case PackageType.TRIAL:\n      case 'Classrooms':\n      case 'All':\n        this.toggleFoundSwitch(event);\n        break;\n    }\n    this.filteredPackages = this.filterPackages(event);\n  }\n  resetSwitches() {\n    for (const key in this.packageFilterSwitches) {\n      this.packageFilterSwitches[key].value = false;\n    }\n  }\n  /**\n  * Filters the packages based on the specified type and classroom ID.\n  *\n  * @param {string} type - The type of package to filter by.\n  * @returns {Package[]} - The filtered packages.\n  */\n  filterPackages(type) {\n    const filteredPackages = this.packages.filter(pkg => {\n      if (type === 'All') {\n        return true;\n      }\n      if (type === 'Classrooms') {\n        return [PackageType.FLEX, PackageType.PREMIUM, PackageType.REGULAR].includes(pkg.type);\n      } else {\n        return pkg.type === type;\n      }\n    });\n    console.log(filteredPackages);\n    return this.generalService.isNullishObject(this.classroom) ? filteredPackages : filteredPackages.filter(pkg => pkg.classroomId === this.classroom.id);\n  }\n  /**\n  Toggles the value of a switch object with the given label.\n  @param {string} label - The label of the switch object to toggle.\n  */\n  toggleFoundSwitch(label) {\n    const switchObj = this.packageFilterSwitches.find(s => s.label === label);\n    if (switchObj) {\n      switchObj.value = !switchObj.value;\n    }\n  }\n  static #_ = this.ɵfac = function PackagesComponent_Factory(t) {\n    return new (t || PackagesComponent)(i0.ɵɵdirectiveInject(i1.GeneralService), i0.ɵɵdirectiveInject(i2.PackageService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i0.NgZone));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PackagesComponent,\n    selectors: [[\"app-packages\"]],\n    inputs: {\n      classroom: \"classroom\"\n    },\n    decls: 11,\n    vars: 6,\n    consts: [[1, \"packages\", \"overflow-y-auto\", \"mb-4\"], [\"id\", \"packages-top\", 1, \"packages-top\", \"pt-0\", \"pb-4\"], [1, \"packages-header\", \"flex-column\", \"sm:flex-row\", \"gap-2\", \"sm:gap-0\", \"p-0\", \"my-4\"], [1, \"packages-header-title\", \"text-primary\", \"m-0\", \"font-bold\", \"font-xl\"], [1, \"ml-auto\", \"sm:mr-2\"], [3, \"switchesWithLabelsChange\", \"switchToggled\", \"switchesWithLabels\"], [4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"Buy Package\", \"icon\", \"\", \"iconPos\", \"left\", 1, \"p-button-sm\", \"p-button-raised\", \"p-button-rounded\", \"blue-gradient-btn\", \"font-2xs\", \"col-12\", \"sm:col-2\", 2, \"padding\", \"0.5rem 0.875rem\", 3, \"click\"], [1, \"flex\", \"w-full\", \"h-full\", \"py-3\", \"flex-column\", \"align-items-center\", \"justify-content-center\"], [1, \"flex\", \"h-full\", \"align-items-center\", \"justify-content-center\", \"flex-column\"], [\"src\", \"/assets/icons/empty-classroom.png\", \"height\", \"280\"], [1, \"flex\", \"gap-2\", \"align-items-center\", \"justify-content-around\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"\", \"icon\", \"\", \"iconPos\", \"left\", 1, \"p-button-sm\", \"p-button-raised\", \"p-button-rounded\", \"blue-gradient-btn\", \"flex\", \"gap-3\", 3, \"click\"], [1, \"font-lg\"], [\"src\", \"/assets/icons/arrow-right.svg\"], [1, \"w-100\", \"block-gradient\", \"border-round-xl\", 3, \"ngClass\"], [\"title\", \"\", 1, \"grid\"], [1, \"grid\", \"grid-nogutter\", \"surface-border\", \"px-2\", \"pt-2\", \"justify-content-between\", \"w-full\"], [1, \"col-4\", \"mt-3\", \"sm:col-2\", \"font-2xs\", \"text-center\", \"sm:text-left\"], [1, \"text-500\", \"font-medium\", \"mb-1\"], [1, \"text-900\"], [\"class\", \"col-4 mt-3 sm:col-1 font-2xs text-center sm:text-left\", 4, \"ngIf\"], [1, \"col-4\", \"mt-3\", \"sm:col-1\", \"font-2xs\", \"text-center\", 3, \"ngClass\"], [1, \"text-900\", \"text-center\"], [1, \"col-4\", \"mt-3\", \"sm:col-1\", \"font-2xs\", \"text-center\"], [1, \"text-900\", \"flex\", \"justify-content-center\"], [\"size\", \"xs\", \"textSize\", \"2xs\", 3, \"name\"], [1, \"col-4\", \"mt-3\", \"sm:col-1\", \"font-2xs\", \"text-center\", \"sm:mr-1\"], [1, \"text-900\", \"capitalize\"], [\"content\", \"\", 1, \"p-2\"], [1, \"w-full\", \"font-sm\", \"justify-content-center\"], [1, \"package-content-title\", \"font-bold\"], [1, \"package-content-header\"], [1, \"col-20-per\", \"col-20-per-left\", \"color-gray-1\"], [1, \"col-20-per\", \"color-gray-1\"], [1, \"col-20-per\", \"col-20-per-right\", \"color-gray-1\"], [1, \"col-20-per\", \"col-25-per-right\", \"color-gray-1\"], [3, \"type\", \"date\"], [1, \"col-4\", \"mt-3\", \"sm:col-1\", \"font-2xs\", \"text-center\", \"sm:text-left\"]],\n    template: function PackagesComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h3\", 3);\n        i0.ɵɵtext(4, \"All Packages\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 4)(6, \"app-classroom-status-filters\", 5);\n        i0.ɵɵtwoWayListener(\"switchesWithLabelsChange\", function PackagesComponent_Template_app_classroom_status_filters_switchesWithLabelsChange_6_listener($event) {\n          i0.ɵɵtwoWayBindingSet(ctx.packageFilterSwitches, $event) || (ctx.packageFilterSwitches = $event);\n          return $event;\n        });\n        i0.ɵɵlistener(\"switchToggled\", function PackagesComponent_Template_app_classroom_status_filters_switchToggled_6_listener($event) {\n          return ctx.toggleSwitch($event);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(7, PackagesComponent_ng_container_7_Template, 2, 0, \"ng-container\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(8, PackagesComponent_ng_container_8_Template, 11, 0, \"ng-container\", 6);\n        i0.ɵɵelementStart(9, \"div\");\n        i0.ɵɵtemplate(10, PackagesComponent_ng_container_10_Template, 58, 18, \"ng-container\", 7);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(6);\n        i0.ɵɵtwoWayProperty(\"switchesWithLabels\", ctx.packageFilterSwitches);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.authService.isStudent);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.filteredPackages.length === 0);\n        i0.ɵɵadvance();\n        i0.ɵɵclassMap(ctx.filteredPackages.length === 0 ? \"flex align-items-center justify-content-center w-full h-full\" : \"\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngForOf\", ctx.filteredPackages);\n      }\n    },\n    dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.ButtonDirective, i6.GLevelCircleComponent, i7.ClassroomStatusFiltersComponent, i8.PackageAccordionItemComponent, i9.PackageContentInfoComponent],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.packages[_ngcontent-%COMP%] {\\n  position: relative;\\n  background-color: var(--white);\\n  border-radius: 40px;\\n  width: 100%;\\n}\\n\\n.packages-top[_ngcontent-%COMP%] {\\n  top: 0;\\n  width: 100%;\\n  padding: 20px 30px 0 30px;\\n  box-sizing: border-box;\\n}\\n@media screen and (max-width: 1366px) {\\n  .packages-top[_ngcontent-%COMP%] {\\n    padding: 20px;\\n    position: relative;\\n  }\\n}\\n@media only screen and (max-width: 768px) {\\n  .packages-top[_ngcontent-%COMP%] {\\n    padding: 0;\\n  }\\n}\\n.packages-top[_ngcontent-%COMP%]   .package-header[_ngcontent-%COMP%], .packages-top[_ngcontent-%COMP%]   .package-header-filters[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  flex-wrap: wrap;\\n  position: relative;\\n}\\n.packages-top[_ngcontent-%COMP%]   .package-header-filters[_ngcontent-%COMP%] {\\n  position: absolute;\\n  background-color: var(--white);\\n  color: white !important;\\n  z-index: 200;\\n  top: 100px;\\n  border-radius: 10px;\\n  width: 90%;\\n  padding: 10px 0;\\n}\\n\\n.packages-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 30px 0;\\n}\\n.package-content-title[_ngcontent-%COMP%] {\\n  width: 100%;\\n  text-align: center;\\n}\\n\\n.package-content-header[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  flex-wrap: wrap;\\n  font-size: 12px;\\n  margin-top: 15px;\\n}\\n\\n.package-content-info[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  justify-content: space-evenly;\\n  border-bottom: 0.5px solid var(--gray-border-2);\\n  padding: 10px 0;\\n}\\n\\n.package-content-info[_ngcontent-%COMP%]:last-child {\\n  border-bottom: 0px solid var(--gray-border-2);\\n}\\n\\n.filters-button[_ngcontent-%COMP%] {\\n  z-index: 200;\\n}\\n\\n.color-gray-1[_ngcontent-%COMP%] {\\n  color: var(--gray-1);\\n}\\n\\n.package[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  transition: height 0.3s ease-out;\\n  height: auto;\\n  width: 100%;\\n  height: 0;\\n  margin-top: 20px;\\n}\\n\\n.arrow[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  transition-duration: 0.2s;\\n  transition-property: transform;\\n  cursor: pointer;\\n}\\n\\n.package-filter[_ngcontent-%COMP%] {\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: start;\\n}\\n\\n.white-button[_ngcontent-%COMP%] {\\n  margin: 10px;\\n}\\n\\n.btns[_ngcontent-%COMP%] {\\n  margin: auto;\\n}\\n\\n@media screen and (max-width: 1024px) {\\n  .cut-on-tablet[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .col-18-per-tablet[_ngcontent-%COMP%] {\\n    flex-basis: 18%;\\n    width: 18%;\\n  }\\n  .col-20-per-tablet[_ngcontent-%COMP%] {\\n    flex-basis: 20%;\\n    width: 20%;\\n  }\\n  .col-8-per-tablet[_ngcontent-%COMP%] {\\n    flex-basis: 8%;\\n    width: 8%;\\n  }\\n  .col-14-per-tablet[_ngcontent-%COMP%] {\\n    flex-basis: 14%;\\n    width: 14%;\\n  }\\n}\\n@media screen and (max-width: 768px) {\\n  .cut-on-mobile[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .col-24-per-mobile[_ngcontent-%COMP%] {\\n    flex-basis: 24%;\\n    width: 24%;\\n  }\\n}\\n[_nghost-%COMP%]     .e-date-icon {\\n  position: absolute !important;\\n  left: 0 !important;\\n}\\n[_nghost-%COMP%]     .e-input {\\n  margin-left: 30px !important;\\n}\\n[_nghost-%COMP%]     .e-date-modal {\\n  width: 400px;\\n}\\n\\n[_nghost-%COMP%]     .small-input-switch .p-inputswitch {\\n  height: 1rem;\\n  width: 1.8rem;\\n}\\n[_nghost-%COMP%]     .small-input-switch .p-inputswitch.p-inputswitch-checked .p-inputswitch-slider::before {\\n  transform: translateX(1rem);\\n  background-image: linear-gradient(90deg, hsl(229, 95%, 71%) 0%, hsl(230, 90%, 68%) 11%, hsl(230, 86%, 66%) 22%, hsl(230, 83%, 63%) 33%, hsl(231, 79%, 60%) 44%, hsl(231, 77%, 58%) 56%, hsl(231, 74%, 55%) 67%, hsl(230, 72%, 52%) 78%, hsl(230, 75%, 48%) 89%, hsl(227, 90%, 43%) 100%);\\n}\\n[_nghost-%COMP%]     .small-input-switch .p-inputswitch .p-inputswitch-slider {\\n  background-image: linear-gradient(90deg, hsl(227, 51%, 93%) 0%, hsl(227, 55%, 92%) 11%, hsl(227, 58%, 91%) 22%, hsl(228, 61%, 90%) 33%, hsl(228, 62%, 88%) 44%, hsl(228, 64%, 87%) 56%, hsl(228, 65%, 86%) 67%, hsl(228, 66%, 85%) 78%, hsl(228, 67%, 84%) 89%, hsl(228, 67%, 83%) 100%);\\n}\\n[_nghost-%COMP%]     .small-input-switch .p-inputswitch .p-inputswitch-slider::before {\\n  width: 0.8rem;\\n  height: 0.8rem;\\n  margin-top: -0.4rem;\\n}\\n\\n.trial-gradient[_ngcontent-%COMP%] {\\n  background: linear-gradient(#fff 0%, rgba(63, 209, 255, 0.2) 100%) !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["formatDate", "PackageType", "SubSink", "moment", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "PackagesComponent_ng_container_7_Template_button_click_1_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "generalService", "navigateToBuyPackage", "isNullishObject", "classroom", "ɵɵelementEnd", "ɵɵtext", "ɵɵelement", "PackagesComponent_ng_container_8_Template_button_click_7_listener", "_r3", "ɵɵadvance", "ɵɵtextInterpolate", "package_r4", "packLanguage", "trim", "ɵɵproperty", "expiresOn", "ɵɵtemplate", "PackagesComponent_ng_container_10_div_10_Template", "PackagesComponent_ng_container_10_ng_container_57_Template", "ɵɵpureFunction1", "_c0", "type", "authService", "isStudent", "<PERSON><PERSON><PERSON><PERSON>", "getPersonFullName", "paidByStudent", "ɵɵpureFunction2", "_c1", "<PERSON><PERSON><PERSON>er", "totalHours", "hoursLeft", "ɵɵpropertyInterpolate", "packageLevel", "state", "toLowerCase", "purchasedAt", "PackagesComponent", "constructor", "packageService", "ngZone", "subs", "packages", "filteredPackages", "viewImgSrc", "filtersOpen", "packageFilterHelper", "packageFilter", "getCurrentPackageFilter", "packageFilterWithoutTheCurrent", "getPackageFilterWithoutTheCurrent", "packageFilterSwitches", "label", "value", "TRIAL", "GIFT", "ngOnInit", "getPackages", "ngAfterViewInit", "ngOnDestroy", "unsubscribe", "sink", "getAllUserPackages", "getUserId", "subscribe", "res", "console", "log", "toggleSwitch", "getDurationInDays", "date1", "date2", "getDatesDiff", "days", "togglePackage", "event", "viewImgElement", "packageElement", "viewImgElementSrc", "id", "getAttribute", "<PERSON><PERSON><PERSON>", "section", "document", "getElementById", "setAttribute", "style", "transform", "expandSection", "collapseSection", "toggleFilters", "slideOutElement", "slideInElement", "getDateLessonLevelEnds", "lesson_level_ended", "toString", "includes", "dateString", "date", "format", "resetSwitches", "toggleFoundSwitch", "filterPackages", "key", "filter", "pkg", "FLEX", "PREMIUM", "REGULAR", "classroomId", "switchObj", "find", "s", "_", "ɵɵdirectiveInject", "i1", "GeneralService", "i2", "PackageService", "i3", "AuthService", "NgZone", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "PackagesComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "PackagesComponent_Template_app_classroom_status_filters_switchesWithLabelsChange_6_listener", "$event", "ɵɵtwoWayBindingSet", "PackagesComponent_Template_app_classroom_status_filters_switchToggled_6_listener", "PackagesComponent_ng_container_7_Template", "PackagesComponent_ng_container_8_Template", "PackagesComponent_ng_container_10_Template", "ɵɵtwoWayProperty", "length", "ɵɵclassMap"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\packages.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\packages.component.html"], "sourcesContent": ["import { formatDate } from '@angular/common';\r\nimport { Component, Input, NgZone, OnInit, ViewChild } from '@angular/core';\r\nimport { PackageFilter, Package, PackageFilterHelper, PackageLessonsLevel, PackageState, PackageType } from 'src/app/core/models/package.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { PackageService } from 'src/app/core/services/package.service';\r\nimport { SubSink } from 'subsink';\r\nimport * as moment from 'moment';\r\nimport { Classroom } from 'src/app/core/models/classroom.model';\r\n\r\n@Component({\r\n  selector: 'app-packages',\r\n  templateUrl: './packages.component.html',\r\n  styleUrls: ['./packages.component.scss']\r\n})\r\nexport class PackagesComponent implements OnInit {\r\n  @Input() classroom: Classroom = {} as Classroom;\r\n  private subs = new SubSink();\r\n  public packages: Package[] = [];\r\n  public filteredPackages: Package[] = [];\r\n  public viewImgSrc: string = \"/assets/icons/down.svg\";\r\n  public filtersOpen: boolean = false;\r\n  public packageFilterHelper: PackageFilterHelper = this.packageService.packageFilterHelper;\r\n  public packageFilter: PackageFilter = this.packageService.getCurrentPackageFilter();\r\n  public packageFilterWithoutTheCurrent: PackageFilterHelper = this.packageService.getPackageFilterWithoutTheCurrent();\r\n\r\n  packageFilterSwitches = [\r\n    { label: 'Classrooms', value: false },\r\n    { label: PackageType.TRIAL, value: false },\r\n    { label: PackageType.GIFT, value: false },\r\n    { label: 'All', value: true }\r\n  ];\r\n\r\n  constructor(\r\n    public generalService: GeneralService,\r\n    private packageService: PackageService,\r\n    public authService: AuthService,\r\n    private ngZone: NgZone\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.getPackages();\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  getPackages() {\r\n    this.subs.sink = this.packageService.getAllUserPackages(this.authService.getUserId()!).subscribe(res => {\r\n      console.log(res);\r\n      this.packages = res;\r\n      this.filteredPackages = res;\r\n\r\n\r\n      this.toggleSwitch('All');\r\n    })\r\n  }\r\n\r\n  getDurationInDays(date1: Date, date2: Date) {\r\n    return this.generalService.getDatesDiff(date1, date2).days\r\n  }\r\n\r\n  togglePackage(event: any, viewImgElement: any, packageElement: any, viewImgElementSrc: any) {\r\n    let id = packageElement.getAttribute('id');\r\n    let setOpen = viewImgElement.getAttribute('open') === 'false' ? 'true' : 'false'\r\n    var section = document.getElementById(id);\r\n    viewImgElement.setAttribute('open', setOpen)\r\n    if (setOpen === 'true') {\r\n      console.log(\"open\");\r\n      viewImgElementSrc.style.transform = \"rotate(180deg)\"\r\n      this.generalService.expandSection(section)\r\n    } else {\r\n      viewImgElementSrc.style.transform = \"rotate(0)\"\r\n      this.generalService.collapseSection(section)\r\n    }\r\n  }\r\n\r\n  toggleFilters() {\r\n    if (this.filtersOpen) {\r\n      this.generalService.slideOutElement('blur_bg');\r\n      this.generalService.slideOutElement('package-filters');\r\n    } else {\r\n      this.generalService.slideInElement('blur_bg');\r\n      this.generalService.slideInElement('package-filters');\r\n    }\r\n    this.filtersOpen = !this.filtersOpen\r\n  }\r\n\r\n  getDateLessonLevelEnds(lesson_level_ended: any) {\r\n    if(lesson_level_ended.toString().includes('1970')){\r\n      return 'present'\r\n    }else{\r\n      return formatDate(lesson_level_ended, 'dd-MM-yyyy', 'en_US')\r\n    }\r\n  }\r\n\r\n  formatDate(dateString: string): string {\r\n    const date = moment(dateString);\r\n    return date.format('D/MM/YYYY');\r\n  }\r\n\r\n  toggleSwitch(event: string) {\r\n    this.resetSwitches();\r\n    switch (event) {\r\n      case PackageType.GIFT:\r\n      case PackageType.TRIAL:\r\n      case 'Classrooms':\r\n      case 'All':\r\n        this.toggleFoundSwitch(event);\r\n        break;\r\n    }\r\n    this.filteredPackages = this.filterPackages(event);\r\n\r\n  }\r\n\r\n  private resetSwitches() {\r\n    for (const key in this.packageFilterSwitches) {\r\n      this.packageFilterSwitches[key].value = false;\r\n    }\r\n  }\r\n\r\n  /**\r\n * Filters the packages based on the specified type and classroom ID.\r\n *\r\n * @param {string} type - The type of package to filter by.\r\n * @returns {Package[]} - The filtered packages.\r\n */\r\n  private filterPackages(type: string) {\r\n    const filteredPackages = this.packages.filter(pkg => {\r\n      if (type === 'All') {\r\n        return true;\r\n      }\r\n      if (type === 'Classrooms') {\r\n        return [PackageType.FLEX, PackageType.PREMIUM, PackageType.REGULAR].includes(pkg.type);\r\n      } else {\r\n        return pkg.type === type;\r\n      }\r\n    });\r\n\r\n    console.log(filteredPackages);\r\n    return this.generalService.isNullishObject(this.classroom)\r\n      ? filteredPackages\r\n      : filteredPackages.filter(pkg => pkg.classroomId === this.classroom.id);\r\n  }\r\n\r\n  /**\r\n  Toggles the value of a switch object with the given label.\r\n  @param {string} label - The label of the switch object to toggle. \r\n  */\r\n  private toggleFoundSwitch(label: string) {\r\n    const switchObj = this.packageFilterSwitches.find(s => s.label === label);\r\n    if (switchObj) {\r\n      switchObj.value = !switchObj.value;\r\n    }\r\n  }\r\n\r\n}\r\n", "\r\n                <!-- hjere -->\r\n               \r\n                \r\n<div class=\"packages overflow-y-auto mb-4\" >\r\n    <div id=\"packages-top\" class=\"packages-top pt-0 pb-4\">\r\n        <div class=\"packages-header flex-column sm:flex-row gap-2 sm:gap-0 p-0 my-4\">\r\n            <h3 class=\"packages-header-title text-primary m-0 font-bold font-xl\">All Packages</h3>\r\n            <div class=\"ml-auto sm:mr-2\">\r\n                <app-classroom-status-filters\r\n                [(switchesWithLabels)]=\"packageFilterSwitches\"\r\n                (switchToggled)=\"toggleSwitch($event)\"\r\n                ></app-classroom-status-filters>\r\n            </div>\r\n\r\n            <ng-container *ngIf=\"authService.isStudent\">\r\n            <button (click)=\"generalService.navigateToBuyPackage(!this.generalService.isNullishObject(this.classroom) ? this.classroom : null)\" \r\n            pButton pRipple type=\"button\" label=\"Buy Package\" icon=\"\" iconPos=\"left\"\r\n            class=\"p-button-sm p-button-raised p-button-rounded blue-gradient-btn font-2xs col-12 sm:col-2\" style=\" padding: 0.5rem 0.875rem;\"></button>\r\n            </ng-container>\r\n            \r\n        </div>\r\n        <ng-container *ngIf=\"filteredPackages.length === 0\">\r\n            <div class=\"flex w-full h-full py-3 flex-column align-items-center justify-content-center\">\r\n\r\n                <div class=\"flex h-full align-items-center justify-content-center flex-column\">\r\n                    <strong>There are no packages available</strong>\r\n                    <img src=\"/assets/icons/empty-classroom.png\" height=\"280\">\r\n                </div>\r\n                \r\n                <div class=\"flex gap-2 align-items-center justify-content-around\"> \r\n                    <button  pButton pRipple  (click)=\"generalService.navigateToBuyPackage()\" type=\"button\" label=\"\" icon=\"\" iconPos=\"left\" class=\"p-button-sm p-button-raised p-button-rounded blue-gradient-btn flex gap-3\">\r\n                        <span class=\"font-lg\">Buy Package</span> <img src=\"/assets/icons/arrow-right.svg\" />\r\n                    </button>\r\n                    <!-- TODO: enable pricing button -->\r\n                    <!-- <a href=\"https://www.mylingotrip.com/pricing/\" target=\"_blank\" (click)=\"generalService.navigateToBuyPackage()\"\r\n                        class=\"font-lg gray-border-button border-round-3xl gap-2 px-3 py-2 hover:text-white\">\r\n                        <span class=\"font-lg text-primary hover:text-white\">See Pricing</span>  <img src=\"/assets/icons/arr-right.svg\" />\r\n                    </a> -->\r\n                </div>\r\n            </div>\r\n        </ng-container>\r\n<div [class]=\"filteredPackages.length === 0 ? 'flex align-items-center justify-content-center w-full h-full' : ''\">\r\n      \r\n\r\n    <ng-container *ngFor=\"let package of filteredPackages;let i = index;\">\r\n        <div  class=\"w-100 block-gradient border-round-xl\" [ngClass]=\"{'trial-gradient': package.type ==='Trial'}\">\r\n            <app-package-accordion-item>\r\n\r\n              <div title class=\"grid\">\r\n                <div class=\"grid grid-nogutter surface-border px-2 pt-2 justify-content-between w-full\">\r\n                    <div class=\"col-4 mt-3 sm:col-2 font-2xs text-center sm:text-left\">\r\n                        <div class=\"text-500 font-medium mb-1\">Name</div>\r\n                        <div class=\"text-900\">{{ authService.isStudent ? package.classroomTeacher : \r\n                            generalService.getPersonFullName(package.paidByStudent) }}</div>\r\n                    </div>\r\n                    <div class=\"col-4 mt-3 sm:col-1 font-2xs text-center sm:text-left\" *ngIf=\"authService.isStudent\">\r\n                        <div class=\"text-500 font-medium mb-1\">Language</div>\r\n                        <div class=\"text-900 capitalize\">{{ (package.packLanguage).trim() !== '' ? package.packLanguage : '-' }}</div>\r\n                    </div>\r\n                    <div class=\"col-4 mt-3 sm:col-1 font-2xs text-center\"\r\n                    [ngClass]=\"{'sm:col-1': authService.isStudent, 'sm:col-2': authService.isTeacher }\">\r\n                        <div class=\"text-500 font-medium mb-1\">Total hours</div>\r\n                        <div class=\"text-900 text-center\">{{ package.totalHours }}</div>\r\n                    </div>\r\n                    <div class=\"col-4 mt-3 sm:col-1 font-2xs text-center\">\r\n                        <div class=\"text-500 font-medium mb-1\">Hours Left</div>\r\n                        <div class=\"text-900 text-center\">{{ package.hoursLeft }}</div>\r\n                    </div>\r\n                    <div class=\"col-4 mt-3 sm:col-1 font-2xs text-center\">\r\n                        <div class=\"text-500 font-medium mb-1\">Level</div>\r\n                        <div class=\"text-900 flex justify-content-center\">\r\n                            <g-level-circle size=\"xs\" textSize=\"2xs\" name=\"{{ (package.packageLevel === '') || (package.packageLevel === ('No Lesson') || package.packageLevel === ('No Classroom')) ? 'TBD' : package.packageLevel }}\"></g-level-circle>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"col-4 mt-3 sm:col-1 font-2xs text-center\">\r\n                        <div class=\"text-500 font-medium mb-1\">Type</div>\r\n                        <div class=\"text-900\">{{ package.type }}</div>\r\n                    </div>\r\n                    <div class=\"col-4 mt-3 sm:col-1 font-2xs text-center\">\r\n                        <div class=\"text-500 font-medium mb-1\">Expires on</div>\r\n                        <div class=\"text-900\">{{ package.type ==='Trial' ? '-' : formatDate(package.expiresOn) }}</div>\r\n                    </div>\r\n                    <div class=\"col-4 mt-3 sm:col-1 font-2xs text-center sm:mr-1\">\r\n                        <div class=\"text-500 font-medium mb-1\">Status</div>\r\n                        <div class=\"text-900 capitalize\">{{ package.state.toLowerCase() }}</div>\r\n                    </div>\r\n                </div>\r\n\r\n              </div>\r\n              <div content class=\"p-2\">\r\n                <div class=\"w-full font-sm justify-content-center\">\r\n                    <div class=\"package-content-title font-bold\">\r\n                        Events\r\n                    </div>\r\n                    <div class=\"package-content-header\">\r\n                        <div class=\"col-20-per col-20-per-left color-gray-1\">\r\n                            Type\r\n                        </div>\r\n                        <div class=\"col-20-per color-gray-1\">\r\n                            Duration\r\n                        </div>\r\n                        <div class=\"col-20-per color-gray-1\">\r\n                            From\r\n                        </div>\r\n                        <div class=\"col-20-per col-20-per-right color-gray-1\">\r\n                            To\r\n                        </div>\r\n                        <div class=\"col-20-per col-25-per-right color-gray-1\">\r\n                            Date\r\n                        </div>\r\n                    </div>\r\n                    <app-package-content-info [type]=\"package.type ==='Trial' ? 'Trial Created' : 'Package Purchase'\" [date]=\"formatDate(package.purchasedAt)\"></app-package-content-info>\r\n                    <!-- <app-package-content-info [type]=\"'Lessons Started'\" [date]=\"formatDate(package.stratedAt)\"></app-package-content-info> -->\r\n                    <ng-container *ngIf=\"package.type !=='Trial'\">\r\n                    <app-package-content-info [type]=\"'Package Expiration'\" [date]=\"formatDate(package.expiresOn)\"></app-package-content-info>\r\n                    </ng-container>\r\n                    <!-- <app-package-content-info *ngIf=\"package.extentions.length > 0\" [type]=\"'Teacher Extensions'\"></app-package-content-info> -->\r\n                    <!-- <app-package-content-info [type]=\"'Package Extension'\"></app-package-content-info> -->\r\n\r\n                    <!-- ends row -->\r\n                    <!-- <div class=\"package-content-header\">\r\n                        <div class=\"col-25-per col-25-per-left color-gray-1\">\r\n                            From\r\n                        </div>\r\n                        <div class=\"col-25-per color-gray-1\">\r\n                            To\r\n                        </div>\r\n                        <div class=\"col-25-per color-gray-1\">\r\n                            Duration\r\n                        </div>\r\n                        <div class=\"col-25-per col-25-per-right color-gray-1\">\r\n                            Reasoning\r\n                        </div>\r\n                    </div>\r\n                    <div  class=\"package-content-info\">\r\n                        <div class=\"col-25-per col-25-per-left\">\r\n                         Test\r\n                        </div>\r\n                        <div class=\"col-25-per\">\r\n                            Test\r\n                        </div>\r\n\r\n                        <div class=\"col-25-per\">\r\n                           2 days\r\n                        </div>\r\n                        <div class=\"col-25-per col-25-per-right\">\r\n                            Test\r\n                        </div>\r\n                    </div> -->\r\n                </div>\r\n              </div>\r\n            </app-package-accordion-item>\r\n          </div>\r\n    </ng-container>\r\n        <!-- Packages Row -->\r\n        <!-- <div *ngFor=\"let package of filteredPackages\" class=\"package-header\"> -->\r\n\r\n            <!-- Package Header -->\r\n            <!-- <div class=\"col-11-per col-18-per-tablet col-24-per-mobile bold\">{{package.totalHours}}</div>\r\n            <div class=\"col-11-per col-18-per-tablet col-24-per-mobile bold\">{{package.hoursLeft}}</div>\r\n            <div class=\"col-14-per cut-on-tablet bold\">{{package.purchasedAt | date : 'dd-MM-yyyy' }}</div>\r\n            <div class=\"col-14-per col-20-per-tablet cut-on-mobile bold\">{{package.stratedAt | date : 'dd-MM-yyyy' }}\r\n            </div>\r\n            <div class=\"col-14-per col-20-per-tablet cut-on-mobile bold\">{{package.expiresOn | date : 'dd-MM-yyyy' }}\r\n            </div>\r\n            <div class=\"col-5-per col-8-per- col-24-per-mobile bold\">{{package.currentPackageLevel}}</div>\r\n            <div class=\"col-10-per bold cut-on-tablet\">{{package.type}}</div>\r\n            <div class=\"col-8-per bold cut-on-tablet\">{{package.extensions.length > 0 ? 'Yes': 'No'}}</div>\r\n            <div class=\"col-10-per col-14-per-tablet col-24-per-mobile bold\">{{package.state}}</div>\r\n            <div #viewImgElement [attr.open]=\"false\" class=\"col-2-per bold col-2-per-right\"\r\n                (click)=\"togglePackage($event.target, viewImgElement, packageElement, viewImgElementSrc)\"><img\r\n                    #viewImgElementSrc class=\"arrow\" src=\"/assets/icons/down.svg\"></div> -->\r\n            <!-- <div #packageElement [attr.id]=\"package.id\" class=\"package\"> -->\r\n                <!-- Package Extensions -->\r\n                <!-- <div *ngIf=\"package.extensions.length > 0\" style=\"width:100%; margin-top:20px;\">\r\n                    <div class=\"package-content-title\">\r\n                        Extensions\r\n                    </div>\r\n                    <div class=\"package-content-header\">\r\n                        <div class=\"col-25-per col-25-per-left color-gray-1\">\r\n                            From\r\n                        </div>\r\n                        <div class=\"col-25-per color-gray-1\">\r\n                            To\r\n                        </div>\r\n                        <div class=\"col-25-per color-gray-1\">\r\n                            Duration\r\n                        </div>\r\n                        <div class=\"col-25-per col-25-per-right color-gray-1\">\r\n                            Reasoning\r\n                        </div>\r\n                    </div>\r\n                    <div *ngFor=\"let extension of package.extensions\" class=\"package-content-info\">\r\n                        <div class=\"col-25-per col-25-per-left\">\r\n                            {{extension.fromDate | date : 'dd-MM-yyyy'}}\r\n                        </div>\r\n                        <div class=\"col-25-per\">\r\n                            {{extension.toDate | date : 'dd-MM-yyyy'}}\r\n                        </div>\r\n\r\n                        <div class=\"col-25-per\">\r\n                            {{getDurationInDays(extension.fromDate, extension.toDate)}} days\r\n                        </div>\r\n                        <div class=\"col-25-per col-25-per-right\">\r\n                            {{extension.reason}}\r\n                        </div>\r\n                    </div>\r\n                </div> -->\r\n                <!-- Package Levels -->\r\n                <!-- <div style=\"width:100%; margin-top:20px;\">\r\n                    <div class=\"package-content-title\">\r\n                        Levels\r\n                    </div>\r\n                    <div class=\"package-content-header\">\r\n                        <div class=\"col-25-per col-25-per-left color-gray-1\">\r\n                            Level\r\n                        </div>\r\n                        <div class=\"col-25-per color-gray-1\">\r\n                            From\r\n                        </div>\r\n                        <div class=\"col-25-per color-gray-1\">\r\n                            To\r\n                        </div>\r\n                        <div class=\"col-25-per col-25-per-right color-gray-1\">\r\n                            Hours Completed\r\n                        </div>\r\n                    </div>\r\n                    <div *ngFor=\"let lesson_level of package.lessons_level\" class=\"package-content-info\">\r\n                        <div class=\"col-25-per col-25-per-left\">\r\n                            {{lesson_level.level}}\r\n                        </div>\r\n                        <div class=\"col-25-per\">\r\n                            {{lesson_level.lessonsStarted | date : 'dd-MM-yyyy'}}\r\n                        </div>\r\n\r\n                        <div class=\"col-25-per\">\r\n                            {{getDateLessonLevelEnds(lesson_level.lessonsEnded)}}\r\n                        </div>\r\n                        <div class=\"col-25-per col-25-per-right\">\r\n                            {{lesson_level.hoursCompleted}}\r\n                        </div>\r\n                    </div>\r\n                </div> -->\r\n            <!-- </div>\r\n            <hr style=\"width:100%\"> -->\r\n        <!-- </div> -->\r\n    </div>\r\n    </div>\r\n</div>"], "mappings": "AAAA,SAASA,UAAU,QAAQ,iBAAiB;AAE5C,SAAyFC,WAAW,QAAQ,mCAAmC;AAI/I,SAASC,OAAO,QAAQ,SAAS;AACjC,OAAO,KAAKC,MAAM,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;ICQpBC,EAAA,CAAAC,uBAAA,GAA4C;IAC5CD,EAAA,CAAAE,cAAA,gBAEmI;IAF3HF,EAAA,CAAAG,UAAA,mBAAAC,kEAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAG,cAAA,CAAAC,oBAAA,EAAqCJ,MAAA,CAAAG,cAAA,CAAAE,eAAA,CAAAL,MAAA,CAAAM,SAAA,CAAmD,GAAAN,MAAA,CAAAM,SAAA,GAAoB,IAAI,CAAC;IAAA,EAAC;IAEAb,EAAA,CAAAc,YAAA,EAAS;;;;;;;IAIhJd,EAAA,CAAAC,uBAAA,GAAoD;IAIxCD,EAHR,CAAAE,cAAA,aAA2F,cAER,aACnE;IAAAF,EAAA,CAAAe,MAAA,sCAA+B;IAAAf,EAAA,CAAAc,YAAA,EAAS;IAChDd,EAAA,CAAAgB,SAAA,cAA0D;IAC9DhB,EAAA,CAAAc,YAAA,EAAM;IAGFd,EADJ,CAAAE,cAAA,cAAkE,iBAC4I;IAAhLF,EAAA,CAAAG,UAAA,mBAAAc,kEAAA;MAAAjB,EAAA,CAAAK,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAG,cAAA,CAAAC,oBAAA,EAAqC;IAAA,EAAC;IACrEX,EAAA,CAAAE,cAAA,eAAsB;IAAAF,EAAA,CAAAe,MAAA,kBAAW;IAAAf,EAAA,CAAAc,YAAA,EAAO;IAACd,EAAA,CAAAgB,SAAA,eAA2C;IAQhGhB,EAPQ,CAAAc,YAAA,EAAS,EAMP,EACJ;;;;;;IAiBMd,EADJ,CAAAE,cAAA,cAAiG,cACtD;IAAAF,EAAA,CAAAe,MAAA,eAAQ;IAAAf,EAAA,CAAAc,YAAA,EAAM;IACrDd,EAAA,CAAAE,cAAA,cAAiC;IAAAF,EAAA,CAAAe,MAAA,GAAuE;IAC5Gf,EAD4G,CAAAc,YAAA,EAAM,EAC5G;;;;IAD+Bd,EAAA,CAAAmB,SAAA,GAAuE;IAAvEnB,EAAA,CAAAoB,iBAAA,CAAAC,UAAA,CAAAC,YAAA,CAAAC,IAAA,YAAAF,UAAA,CAAAC,YAAA,OAAuE;;;;;IAwD5GtB,EAAA,CAAAC,uBAAA,GAA8C;IAC9CD,EAAA,CAAAgB,SAAA,mCAA0H;;;;;;IAAhGhB,EAAA,CAAAmB,SAAA,EAA6B;IAACnB,EAA9B,CAAAwB,UAAA,8BAA6B,SAAAjB,MAAA,CAAAX,UAAA,CAAAyB,UAAA,CAAAI,SAAA,EAAuC;;;;;IAtE9GzB,EAAA,CAAAC,uBAAA,GAAsE;IAOlDD,EANhB,CAAAE,cAAA,cAA2G,iCAC3E,cAEF,cACkE,cACjB,cACxB;IAAAF,EAAA,CAAAe,MAAA,WAAI;IAAAf,EAAA,CAAAc,YAAA,EAAM;IACjDd,EAAA,CAAAE,cAAA,cAAsB;IAAAF,EAAA,CAAAe,MAAA,GACwC;IAClEf,EADkE,CAAAc,YAAA,EAAM,EAClE;IACNd,EAAA,CAAA0B,UAAA,KAAAC,iDAAA,kBAAiG;IAM7F3B,EAFJ,CAAAE,cAAA,eACoF,eACzC;IAAAF,EAAA,CAAAe,MAAA,mBAAW;IAAAf,EAAA,CAAAc,YAAA,EAAM;IACxDd,EAAA,CAAAE,cAAA,eAAkC;IAAAF,EAAA,CAAAe,MAAA,IAAwB;IAC9Df,EAD8D,CAAAc,YAAA,EAAM,EAC9D;IAEFd,EADJ,CAAAE,cAAA,eAAsD,eACX;IAAAF,EAAA,CAAAe,MAAA,kBAAU;IAAAf,EAAA,CAAAc,YAAA,EAAM;IACvDd,EAAA,CAAAE,cAAA,eAAkC;IAAAF,EAAA,CAAAe,MAAA,IAAuB;IAC7Df,EAD6D,CAAAc,YAAA,EAAM,EAC7D;IAEFd,EADJ,CAAAE,cAAA,eAAsD,eACX;IAAAF,EAAA,CAAAe,MAAA,aAAK;IAAAf,EAAA,CAAAc,YAAA,EAAM;IAClDd,EAAA,CAAAE,cAAA,eAAkD;IAC9CF,EAAA,CAAAgB,SAAA,0BAA6N;IAErOhB,EADI,CAAAc,YAAA,EAAM,EACJ;IAEFd,EADJ,CAAAE,cAAA,eAAsD,eACX;IAAAF,EAAA,CAAAe,MAAA,YAAI;IAAAf,EAAA,CAAAc,YAAA,EAAM;IACjDd,EAAA,CAAAE,cAAA,eAAsB;IAAAF,EAAA,CAAAe,MAAA,IAAkB;IAC5Cf,EAD4C,CAAAc,YAAA,EAAM,EAC5C;IAEFd,EADJ,CAAAE,cAAA,eAAsD,eACX;IAAAF,EAAA,CAAAe,MAAA,kBAAU;IAAAf,EAAA,CAAAc,YAAA,EAAM;IACvDd,EAAA,CAAAE,cAAA,eAAsB;IAAAF,EAAA,CAAAe,MAAA,IAAmE;IAC7Ff,EAD6F,CAAAc,YAAA,EAAM,EAC7F;IAEFd,EADJ,CAAAE,cAAA,eAA8D,eACnB;IAAAF,EAAA,CAAAe,MAAA,cAAM;IAAAf,EAAA,CAAAc,YAAA,EAAM;IACnDd,EAAA,CAAAE,cAAA,eAAiC;IAAAF,EAAA,CAAAe,MAAA,IAAiC;IAI5Ef,EAJ4E,CAAAc,YAAA,EAAM,EACtE,EACJ,EAEF;IAGAd,EAFN,CAAAE,cAAA,eAAyB,eAC4B,eACF;IACzCF,EAAA,CAAAe,MAAA,gBACJ;IAAAf,EAAA,CAAAc,YAAA,EAAM;IAEFd,EADJ,CAAAE,cAAA,eAAoC,eACqB;IACjDF,EAAA,CAAAe,MAAA,cACJ;IAAAf,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAE,cAAA,eAAqC;IACjCF,EAAA,CAAAe,MAAA,kBACJ;IAAAf,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAE,cAAA,eAAqC;IACjCF,EAAA,CAAAe,MAAA,cACJ;IAAAf,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAE,cAAA,eAAsD;IAClDF,EAAA,CAAAe,MAAA,YACJ;IAAAf,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAE,cAAA,eAAsD;IAClDF,EAAA,CAAAe,MAAA,cACJ;IACJf,EADI,CAAAc,YAAA,EAAM,EACJ;IACNd,EAAA,CAAAgB,SAAA,oCAAsK;IAEtKhB,EAAA,CAAA0B,UAAA,KAAAE,0DAAA,0BAA8C;IAuCxD5B,EAHM,CAAAc,YAAA,EAAM,EACF,EACqB,EACzB;;;;;;IA3G2Cd,EAAA,CAAAmB,SAAA,EAAuD;IAAvDnB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAA6B,eAAA,KAAAC,GAAA,EAAAT,UAAA,CAAAU,IAAA,cAAuD;IAOpE/B,EAAA,CAAAmB,SAAA,GACwC;IADxCnB,EAAA,CAAAoB,iBAAA,CAAAb,MAAA,CAAAyB,WAAA,CAAAC,SAAA,GAAAZ,UAAA,CAAAa,gBAAA,GAAA3B,MAAA,CAAAG,cAAA,CAAAyB,iBAAA,CAAAd,UAAA,CAAAe,aAAA,EACwC;IAEEpC,EAAA,CAAAmB,SAAA,EAA2B;IAA3BnB,EAAA,CAAAwB,UAAA,SAAAjB,MAAA,CAAAyB,WAAA,CAAAC,SAAA,CAA2B;IAK/FjC,EAAA,CAAAmB,SAAA,EAAmF;IAAnFnB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAqC,eAAA,KAAAC,GAAA,EAAA/B,MAAA,CAAAyB,WAAA,CAAAC,SAAA,EAAA1B,MAAA,CAAAyB,WAAA,CAAAO,SAAA,EAAmF;IAE7CvC,EAAA,CAAAmB,SAAA,GAAwB;IAAxBnB,EAAA,CAAAoB,iBAAA,CAAAC,UAAA,CAAAmB,UAAA,CAAwB;IAIxBxC,EAAA,CAAAmB,SAAA,GAAuB;IAAvBnB,EAAA,CAAAoB,iBAAA,CAAAC,UAAA,CAAAoB,SAAA,CAAuB;IAKZzC,EAAA,CAAAmB,SAAA,GAAkK;IAAlKnB,EAAA,CAAA0C,qBAAA,SAAArB,UAAA,CAAAsB,YAAA,WAAAtB,UAAA,CAAAsB,YAAA,oBAAAtB,UAAA,CAAAsB,YAAA,8BAAAtB,UAAA,CAAAsB,YAAA,CAAkK;IAKzL3C,EAAA,CAAAmB,SAAA,GAAkB;IAAlBnB,EAAA,CAAAoB,iBAAA,CAAAC,UAAA,CAAAU,IAAA,CAAkB;IAIlB/B,EAAA,CAAAmB,SAAA,GAAmE;IAAnEnB,EAAA,CAAAoB,iBAAA,CAAAC,UAAA,CAAAU,IAAA,qBAAAxB,MAAA,CAAAX,UAAA,CAAAyB,UAAA,CAAAI,SAAA,EAAmE;IAIxDzB,EAAA,CAAAmB,SAAA,GAAiC;IAAjCnB,EAAA,CAAAoB,iBAAA,CAAAC,UAAA,CAAAuB,KAAA,CAAAC,WAAA,GAAiC;IA2B5C7C,EAAA,CAAAmB,SAAA,IAAuE;IAACnB,EAAxE,CAAAwB,UAAA,SAAAH,UAAA,CAAAU,IAAA,oDAAuE,SAAAxB,MAAA,CAAAX,UAAA,CAAAyB,UAAA,CAAAyB,WAAA,EAAyC;IAE3H9C,EAAA,CAAAmB,SAAA,EAA6B;IAA7BnB,EAAA,CAAAwB,UAAA,SAAAH,UAAA,CAAAU,IAAA,aAA6B;;;ADnGhE,OAAM,MAAOgB,iBAAiB;EAkB5BC,YACStC,cAA8B,EAC7BuC,cAA8B,EAC/BjB,WAAwB,EACvBkB,MAAc;IAHf,KAAAxC,cAAc,GAAdA,cAAc;IACb,KAAAuC,cAAc,GAAdA,cAAc;IACf,KAAAjB,WAAW,GAAXA,WAAW;IACV,KAAAkB,MAAM,GAANA,MAAM;IArBP,KAAArC,SAAS,GAAc,EAAe;IACvC,KAAAsC,IAAI,GAAG,IAAIrD,OAAO,EAAE;IACrB,KAAAsD,QAAQ,GAAc,EAAE;IACxB,KAAAC,gBAAgB,GAAc,EAAE;IAChC,KAAAC,UAAU,GAAW,wBAAwB;IAC7C,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAAC,mBAAmB,GAAwB,IAAI,CAACP,cAAc,CAACO,mBAAmB;IAClF,KAAAC,aAAa,GAAkB,IAAI,CAACR,cAAc,CAACS,uBAAuB,EAAE;IAC5E,KAAAC,8BAA8B,GAAwB,IAAI,CAACV,cAAc,CAACW,iCAAiC,EAAE;IAEpH,KAAAC,qBAAqB,GAAG,CACtB;MAAEC,KAAK,EAAE,YAAY;MAAEC,KAAK,EAAE;IAAK,CAAE,EACrC;MAAED,KAAK,EAAEjE,WAAW,CAACmE,KAAK;MAAED,KAAK,EAAE;IAAK,CAAE,EAC1C;MAAED,KAAK,EAAEjE,WAAW,CAACoE,IAAI;MAAEF,KAAK,EAAE;IAAK,CAAE,EACzC;MAAED,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAI,CAAE,CAC9B;EAOG;EAEJG,QAAQA,CAAA;IACN,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,eAAeA,CAAA,GACf;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAClB,IAAI,CAACmB,WAAW,EAAE;EACzB;EAEAH,WAAWA,CAAA;IACT,IAAI,CAAChB,IAAI,CAACoB,IAAI,GAAG,IAAI,CAACtB,cAAc,CAACuB,kBAAkB,CAAC,IAAI,CAACxC,WAAW,CAACyC,SAAS,EAAG,CAAC,CAACC,SAAS,CAACC,GAAG,IAAG;MACrGC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;MAChB,IAAI,CAACvB,QAAQ,GAAGuB,GAAG;MACnB,IAAI,CAACtB,gBAAgB,GAAGsB,GAAG;MAG3B,IAAI,CAACG,YAAY,CAAC,KAAK,CAAC;IAC1B,CAAC,CAAC;EACJ;EAEAC,iBAAiBA,CAACC,KAAW,EAAEC,KAAW;IACxC,OAAO,IAAI,CAACvE,cAAc,CAACwE,YAAY,CAACF,KAAK,EAAEC,KAAK,CAAC,CAACE,IAAI;EAC5D;EAEAC,aAAaA,CAACC,KAAU,EAAEC,cAAmB,EAAEC,cAAmB,EAAEC,iBAAsB;IACxF,IAAIC,EAAE,GAAGF,cAAc,CAACG,YAAY,CAAC,IAAI,CAAC;IAC1C,IAAIC,OAAO,GAAGL,cAAc,CAACI,YAAY,CAAC,MAAM,CAAC,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;IAChF,IAAIE,OAAO,GAAGC,QAAQ,CAACC,cAAc,CAACL,EAAE,CAAC;IACzCH,cAAc,CAACS,YAAY,CAAC,MAAM,EAAEJ,OAAO,CAAC;IAC5C,IAAIA,OAAO,KAAK,MAAM,EAAE;MACtBf,OAAO,CAACC,GAAG,CAAC,MAAM,CAAC;MACnBW,iBAAiB,CAACQ,KAAK,CAACC,SAAS,GAAG,gBAAgB;MACpD,IAAI,CAACvF,cAAc,CAACwF,aAAa,CAACN,OAAO,CAAC;IAC5C,CAAC,MAAM;MACLJ,iBAAiB,CAACQ,KAAK,CAACC,SAAS,GAAG,WAAW;MAC/C,IAAI,CAACvF,cAAc,CAACyF,eAAe,CAACP,OAAO,CAAC;IAC9C;EACF;EAEAQ,aAAaA,CAAA;IACX,IAAI,IAAI,CAAC7C,WAAW,EAAE;MACpB,IAAI,CAAC7C,cAAc,CAAC2F,eAAe,CAAC,SAAS,CAAC;MAC9C,IAAI,CAAC3F,cAAc,CAAC2F,eAAe,CAAC,iBAAiB,CAAC;IACxD,CAAC,MAAM;MACL,IAAI,CAAC3F,cAAc,CAAC4F,cAAc,CAAC,SAAS,CAAC;MAC7C,IAAI,CAAC5F,cAAc,CAAC4F,cAAc,CAAC,iBAAiB,CAAC;IACvD;IACA,IAAI,CAAC/C,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;EACtC;EAEAgD,sBAAsBA,CAACC,kBAAuB;IAC5C,IAAGA,kBAAkB,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAC;MAChD,OAAO,SAAS;IAClB,CAAC,MAAI;MACH,OAAO9G,UAAU,CAAC4G,kBAAkB,EAAE,YAAY,EAAE,OAAO,CAAC;IAC9D;EACF;EAEA5G,UAAUA,CAAC+G,UAAkB;IAC3B,MAAMC,IAAI,GAAG7G,MAAM,CAAC4G,UAAU,CAAC;IAC/B,OAAOC,IAAI,CAACC,MAAM,CAAC,WAAW,CAAC;EACjC;EAEA/B,YAAYA,CAACO,KAAa;IACxB,IAAI,CAACyB,aAAa,EAAE;IACpB,QAAQzB,KAAK;MACX,KAAKxF,WAAW,CAACoE,IAAI;MACrB,KAAKpE,WAAW,CAACmE,KAAK;MACtB,KAAK,YAAY;MACjB,KAAK,KAAK;QACR,IAAI,CAAC+C,iBAAiB,CAAC1B,KAAK,CAAC;QAC7B;IACJ;IACA,IAAI,CAAChC,gBAAgB,GAAG,IAAI,CAAC2D,cAAc,CAAC3B,KAAK,CAAC;EAEpD;EAEQyB,aAAaA,CAAA;IACnB,KAAK,MAAMG,GAAG,IAAI,IAAI,CAACpD,qBAAqB,EAAE;MAC5C,IAAI,CAACA,qBAAqB,CAACoD,GAAG,CAAC,CAAClD,KAAK,GAAG,KAAK;IAC/C;EACF;EAEA;;;;;;EAMQiD,cAAcA,CAACjF,IAAY;IACjC,MAAMsB,gBAAgB,GAAG,IAAI,CAACD,QAAQ,CAAC8D,MAAM,CAACC,GAAG,IAAG;MAClD,IAAIpF,IAAI,KAAK,KAAK,EAAE;QAClB,OAAO,IAAI;MACb;MACA,IAAIA,IAAI,KAAK,YAAY,EAAE;QACzB,OAAO,CAAClC,WAAW,CAACuH,IAAI,EAAEvH,WAAW,CAACwH,OAAO,EAAExH,WAAW,CAACyH,OAAO,CAAC,CAACZ,QAAQ,CAACS,GAAG,CAACpF,IAAI,CAAC;MACxF,CAAC,MAAM;QACL,OAAOoF,GAAG,CAACpF,IAAI,KAAKA,IAAI;MAC1B;IACF,CAAC,CAAC;IAEF6C,OAAO,CAACC,GAAG,CAACxB,gBAAgB,CAAC;IAC7B,OAAO,IAAI,CAAC3C,cAAc,CAACE,eAAe,CAAC,IAAI,CAACC,SAAS,CAAC,GACtDwC,gBAAgB,GAChBA,gBAAgB,CAAC6D,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACI,WAAW,KAAK,IAAI,CAAC1G,SAAS,CAAC4E,EAAE,CAAC;EAC3E;EAEA;;;;EAIQsB,iBAAiBA,CAACjD,KAAa;IACrC,MAAM0D,SAAS,GAAG,IAAI,CAAC3D,qBAAqB,CAAC4D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5D,KAAK,KAAKA,KAAK,CAAC;IACzE,IAAI0D,SAAS,EAAE;MACbA,SAAS,CAACzD,KAAK,GAAG,CAACyD,SAAS,CAACzD,KAAK;IACpC;EACF;EAAC,QAAA4D,CAAA,G;qBA/IU5E,iBAAiB,EAAA/C,EAAA,CAAA4H,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA9H,EAAA,CAAA4H,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAhI,EAAA,CAAA4H,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAlI,EAAA,CAAA4H,iBAAA,CAAA5H,EAAA,CAAAmI,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAjBrF,iBAAiB;IAAAsF,SAAA;IAAAC,MAAA;MAAAzH,SAAA;IAAA;IAAA0H,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCRlB5I,EAHZ,CAAAE,cAAA,aAA4C,aACc,aAC2B,YACJ;QAAAF,EAAA,CAAAe,MAAA,mBAAY;QAAAf,EAAA,CAAAc,YAAA,EAAK;QAElFd,EADJ,CAAAE,cAAA,aAA6B,sCAIxB;QAFDF,EAAA,CAAA8I,gBAAA,sCAAAC,4FAAAC,MAAA;UAAAhJ,EAAA,CAAAiJ,kBAAA,CAAAJ,GAAA,CAAAhF,qBAAA,EAAAmF,MAAA,MAAAH,GAAA,CAAAhF,qBAAA,GAAAmF,MAAA;UAAA,OAAAA,MAAA;QAAA,EAA8C;QAC9ChJ,EAAA,CAAAG,UAAA,2BAAA+I,iFAAAF,MAAA;UAAA,OAAiBH,GAAA,CAAA/D,YAAA,CAAAkE,MAAA,CAAoB;QAAA,EAAC;QAE1ChJ,EADK,CAAAc,YAAA,EAA+B,EAC9B;QAENd,EAAA,CAAA0B,UAAA,IAAAyH,yCAAA,0BAA4C;QAMhDnJ,EAAA,CAAAc,YAAA,EAAM;QACNd,EAAA,CAAA0B,UAAA,IAAA0H,yCAAA,2BAAoD;QAoB5DpJ,EAAA,CAAAE,cAAA,UAAmH;QAG/GF,EAAA,CAAA0B,UAAA,KAAA2H,0CAAA,4BAAsE;QA4M1ErJ,EAFI,CAAAc,YAAA,EAAM,EACA,EACJ;;;QA/OUd,EAAA,CAAAmB,SAAA,GAA8C;QAA9CnB,EAAA,CAAAsJ,gBAAA,uBAAAT,GAAA,CAAAhF,qBAAA,CAA8C;QAKnC7D,EAAA,CAAAmB,SAAA,EAA2B;QAA3BnB,EAAA,CAAAwB,UAAA,SAAAqH,GAAA,CAAA7G,WAAA,CAAAC,SAAA,CAA2B;QAO/BjC,EAAA,CAAAmB,SAAA,EAAmC;QAAnCnB,EAAA,CAAAwB,UAAA,SAAAqH,GAAA,CAAAxF,gBAAA,CAAAkG,MAAA,OAAmC;QAoBrDvJ,EAAA,CAAAmB,SAAA,EAA6G;QAA7GnB,EAAA,CAAAwJ,UAAA,CAAAX,GAAA,CAAAxF,gBAAA,CAAAkG,MAAA,6EAA6G;QAG5EvJ,EAAA,CAAAmB,SAAA,EAAoB;QAApBnB,EAAA,CAAAwB,UAAA,YAAAqH,GAAA,CAAAxF,gBAAA,CAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}