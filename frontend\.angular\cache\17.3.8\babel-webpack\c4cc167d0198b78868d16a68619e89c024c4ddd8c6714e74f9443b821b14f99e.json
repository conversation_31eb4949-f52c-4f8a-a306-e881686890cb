{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nexport class TextCallToActionButtonComponent {\n  constructor(generalService) {\n    this.generalService = generalService;\n  }\n  onWindowScroll() {\n    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;\n  }\n  scrollToTop() {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  }\n  ngOnInit() {}\n  static #_ = this.ɵfac = function TextCallToActionButtonComponent_Factory(t) {\n    return new (t || TextCallToActionButtonComponent)(i0.ɵɵdirectiveInject(i1.GeneralService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TextCallToActionButtonComponent,\n    selectors: [[\"app-text-call-to-action-button\"]],\n    hostBindings: function TextCallToActionButtonComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"scroll\", function TextCallToActionButtonComponent_scroll_HostBindingHandler() {\n          return ctx.onWindowScroll();\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    decls: 10,\n    vars: 0,\n    consts: [[1, \"px-4\", \"py-8\", \"md:px-6\", \"lg:px-8\", \"text-center\"], [1, \"mb-4\", \"font-bold\", \"text-xl\"], [1, \"text-900\"], [1, \"text-700\", \"mb-6\"], [\"pRipple\", \"\", \"pbutton\", \"\", \"pripple\", \"\", \"label\", \"Join Now\", \"icon\", \"pi pi-discord\", 1, \"p-element\", \"p-ripple\", \"font-bold\", \"px-5\", \"py-3\", \"p-button-raised\", \"p-button-rounded\", \"white-space-nowrap\", \"p-button\", \"p-component\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"p-button-icon\", \"p-button-icon-right\"], [1, \"p-button-label\"], [\"src\", \"/assets/icons/contact-us-face-icon.svg\", 1, \"contact-icon-img\"]],\n    template: function TextCallToActionButtonComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"span\", 2);\n        i0.ɵɵtext(3, \"For any inquiries please contact us\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(4, \"div\", 3)(5, \"button\", 4);\n        i0.ɵɵlistener(\"click\", function TextCallToActionButtonComponent_Template_button_click_5_listener() {\n          return ctx.generalService.openExternalLink(\"https://www.mylingotrip.com/contact\");\n        });\n        i0.ɵɵelement(6, \"span\", 5);\n        i0.ɵɵelementStart(7, \"span\", 6);\n        i0.ɵɵtext(8, \"Get in touch\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(9, \"img\", 7);\n        i0.ɵɵelementEnd()()();\n      }\n    },\n    styles: [\".contact-icon-img[_ngcontent-%COMP%] {\\n  width: 42px;\\n  padding-left: 10px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL3ByaW1lL3RleHQtY2FsbC10by1hY3Rpb24tYnV0dG9uL3RleHQtY2FsbC10by1hY3Rpb24tYnV0dG9uLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksV0FBQTtFQUNBLGtCQUFBO0FBQ0oiLCJzb3VyY2VzQ29udGVudCI6WyIuY29udGFjdC1pY29uLWltZyB7XHJcbiAgICB3aWR0aDogNDJweDtcclxuICAgIHBhZGRpbmctbGVmdDogMTBweDtcclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"],\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["TextCallToActionButtonComponent", "constructor", "generalService", "onWindowScroll", "scrollPosition", "window", "pageYOffset", "document", "documentElement", "scrollTop", "body", "scrollToTop", "scrollTo", "top", "behavior", "ngOnInit", "_", "i0", "ɵɵdirectiveInject", "i1", "GeneralService", "_2", "selectors", "hostBindings", "TextCallToActionButtonComponent_HostBindings", "rf", "ctx", "ɵɵlistener", "TextCallToActionButtonComponent_scroll_HostBindingHandler", "ɵɵresolveWindow", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "TextCallToActionButtonComponent_Template_button_click_5_listener", "openExternalLink", "ɵɵelement"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\prime\\text-call-to-action-button\\text-call-to-action-button.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\prime\\text-call-to-action-button\\text-call-to-action-button.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, Component, HostListener, OnInit } from '@angular/core';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\n\r\n@Component({\r\n  selector: 'app-text-call-to-action-button',\r\n  templateUrl: './text-call-to-action-button.component.html',\r\n  styleUrls: ['./text-call-to-action-button.component.scss'],\r\n  changeDetection: ChangeDetectionStrategy.OnPush,\r\n})\r\nexport class TextCallToActionButtonComponent implements OnInit {\r\n\r\n  constructor(public generalService: GeneralService) { }\r\n\r\n  @HostListener('window:scroll', [])\r\n  onWindowScroll() {\r\n    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;\r\n  }\r\n\r\n  scrollToTop() {\r\n    window.scrollTo({ top: 0, behavior: 'smooth' });\r\n  }\r\n  \r\n  ngOnInit(): void {\r\n  }\r\n\r\n}\r\n", "<div class=\"px-4 py-8 md:px-6 lg:px-8 text-center\">\r\n    <div class=\"mb-4 font-bold text-xl\"><span class=\"text-900\">For any inquiries please contact us</span></div>\r\n    <div class=\"text-700 mb-6\">\r\n\r\n        <button (click)=\"generalService.openExternalLink('https://www.mylingotrip.com/contact')\" pRipple pbutton=\"\" pripple=\"\" label=\"Join Now\" icon=\"pi pi-discord\"\r\n            class=\"p-element p-ripple font-bold px-5 py-3 p-button-raised p-button-rounded white-space-nowrap p-button p-component\">\r\n            <span class=\"p-button-icon p-button-icon-right\" aria-hidden=\"true\"></span>\r\n            <span class=\"p-button-label\" >Get in touch</span>\r\n            <img class=\"contact-icon-img\" src=\"/assets/icons/contact-us-face-icon.svg\" />\r\n        </button>\r\n\r\n    </div>\r\n</div>"], "mappings": ";;AASA,OAAM,MAAOA,+BAA+B;EAE1CC,YAAmBC,cAA8B;IAA9B,KAAAA,cAAc,GAAdA,cAAc;EAAoB;EAGrDC,cAAcA,CAAA;IACZ,MAAMC,cAAc,GAAGC,MAAM,CAACC,WAAW,IAAIC,QAAQ,CAACC,eAAe,CAACC,SAAS,IAAIF,QAAQ,CAACG,IAAI,CAACD,SAAS,IAAI,CAAC;EACjH;EAEAE,WAAWA,CAAA;IACTN,MAAM,CAACO,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAQ,CAAE,CAAC;EACjD;EAEAC,QAAQA,CAAA,GACR;EAAC,QAAAC,CAAA,G;qBAdUhB,+BAA+B,EAAAiB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA/BrB,+BAA+B;IAAAsB,SAAA;IAAAC,YAAA,WAAAC,6CAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAA/BR,EAAA,CAAAU,UAAA,oBAAAC,0DAAA;UAAA,OAAAF,GAAA,CAAAvB,cAAA,EAAgB;QAAA,UAAAc,EAAA,CAAAY,eAAA,CAAe;;;;;;;;QCRJZ,EADxC,CAAAa,cAAA,aAAmD,aACX,cAAuB;QAAAb,EAAA,CAAAc,MAAA,0CAAmC;QAAOd,EAAP,CAAAe,YAAA,EAAO,EAAM;QAGvGf,EAFJ,CAAAa,cAAA,aAA2B,gBAGqG;QADpHb,EAAA,CAAAU,UAAA,mBAAAM,iEAAA;UAAA,OAASP,GAAA,CAAAxB,cAAA,CAAAgC,gBAAA,CAAgC,qCAAqC,CAAC;QAAA,EAAC;QAEpFjB,EAAA,CAAAkB,SAAA,cAA0E;QAC1ElB,EAAA,CAAAa,cAAA,cAA8B;QAAAb,EAAA,CAAAc,MAAA,mBAAY;QAAAd,EAAA,CAAAe,YAAA,EAAO;QACjDf,EAAA,CAAAkB,SAAA,aAA6E;QAIzFlB,EAHQ,CAAAe,YAAA,EAAS,EAEP,EACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}