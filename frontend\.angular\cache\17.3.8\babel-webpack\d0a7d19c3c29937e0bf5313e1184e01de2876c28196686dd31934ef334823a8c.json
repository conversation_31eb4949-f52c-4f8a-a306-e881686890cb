{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"primeng/tooltip\";\nimport * as i4 from \"../layout/g-header-back-button/g-header-back-button.component\";\nconst _c0 = [[[\"\", \"slot\", \"start\"]], \"*\"];\nconst _c1 = [\"[slot=start]\", \"*\"];\nconst _c2 = a0 => ({\n  \"max-height.px\": a0\n});\nconst _c3 = a0 => ({\n  \"background-image\": a0\n});\nconst _c4 = a0 => ({\n  \"justify-content-between\": a0\n});\nconst _c5 = a0 => ({\n  \"block-action-active\": true,\n  \"disabled-link\": a0\n});\nfunction BlockViewerComponent_div_1_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 11);\n    i0.ɵɵelement(1, \"img\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"src\", \"/assets/icons/\", ctx_r0.headerIcon, \".svg\", i0.ɵɵsanitizeUrl);\n  }\n}\nfunction BlockViewerComponent_div_1_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.secondaryTitleRight);\n  }\n}\nfunction BlockViewerComponent_div_1_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 14);\n    i0.ɵɵtext(1, \"New\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BlockViewerComponent_div_1_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function BlockViewerComponent_div_1_ng_container_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.closeDialogIconClicked());\n    });\n    i0.ɵɵelement(2, \"span\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction BlockViewerComponent_div_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"a\", 18);\n    i0.ɵɵlistener(\"click\", function BlockViewerComponent_div_1_div_8_Template_a_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.actionRoute($event));\n    });\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(2, _c5, ctx_r0.disabledButton));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.actionButtonText);\n  }\n}\nfunction BlockViewerComponent_div_1_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 19)(2, \"g-header-back-button\", 20);\n    i0.ɵɵlistener(\"click\", function BlockViewerComponent_div_1_ng_container_9_Template_g_header_back_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.goBack());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r0.backButtonLabel);\n  }\n}\nfunction BlockViewerComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"span\", 4);\n    i0.ɵɵtemplate(2, BlockViewerComponent_div_1_span_2_Template, 2, 2, \"span\", 5);\n    i0.ɵɵelementStart(3, \"span\", 6);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, BlockViewerComponent_div_1_span_5_Template, 2, 1, \"span\", 7)(6, BlockViewerComponent_div_1_span_6_Template, 2, 0, \"span\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, BlockViewerComponent_div_1_ng_container_7_Template, 3, 0, \"ng-container\", 9)(8, BlockViewerComponent_div_1_div_8_Template, 4, 4, \"div\", 10)(9, BlockViewerComponent_div_1_ng_container_9_Template, 3, 1, \"ng-container\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r0.headerBlockClass + \"py-2 block-header\");\n    i0.ɵɵproperty(\"ngStyle\", ctx_r0.headerBackgroundImage && i0.ɵɵpureFunction1(16, _c3, \"url(\" + ctx_r0.headerBackgroundImage + \")\"));\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r0.headerClass);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(18, _c4, ctx_r0.secondaryTitleRight || ctx_r0.actionButtonText));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.headerIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r0.headerTextClass ? ctx_r0.headerTextClass : \"font-sm\");\n    i0.ɵɵproperty(\"pTooltip\", ctx_r0.tooltipLabel ? ctx_r0.tooltipLabel : null);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.header);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.secondaryTitleRight);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.new);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showCloseDialogIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.actionButtonText);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.backButtonLabel);\n  }\n}\nexport let BlockViewerComponent = /*#__PURE__*/(() => {\n  class BlockViewerComponent {\n    constructor(router, location) {\n      this.router = router;\n      this.location = location;\n      this.new = false;\n      this.disabledButton = false;\n      this.showCloseDialogIcon = false;\n      this.backButtonLabel = '';\n      this.tooltipLabel = '';\n      this.closeDialogEvent = new EventEmitter();\n    }\n    ngOnInit() {}\n    actionRoute(event) {\n      this.router.navigate([this.actionButtonRoute]);\n    }\n    closeDialogIconClicked() {\n      this.closeDialogEvent.emit();\n    }\n    goBack() {\n      this.location.back();\n    }\n    static #_ = this.ɵfac = function BlockViewerComponent_Factory(t) {\n      return new (t || BlockViewerComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.Location));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BlockViewerComponent,\n      selectors: [[\"app-block-viewer\"]],\n      inputs: {\n        header: \"header\",\n        headerBackgroundImage: \"headerBackgroundImage\",\n        containerClass: \"containerClass\",\n        blockClass: \"blockClass\",\n        headerBlockClass: \"headerBlockClass\",\n        headerClass: \"headerClass\",\n        headerTextClass: \"headerTextClass\",\n        previewStyle: \"previewStyle\",\n        actionButtonText: \"actionButtonText\",\n        actionButtonRoute: \"actionButtonRoute\",\n        secondaryTitleRight: \"secondaryTitleRight\",\n        new: \"new\",\n        disabledButton: \"disabledButton\",\n        containerHeight: \"containerHeight\",\n        headerIcon: \"headerIcon\",\n        showCloseDialogIcon: \"showCloseDialogIcon\",\n        backButtonLabel: \"backButtonLabel\",\n        tooltipLabel: \"tooltipLabel\"\n      },\n      outputs: {\n        closeDialogEvent: \"closeDialogEvent\"\n      },\n      ngContentSelectors: _c1,\n      decls: 6,\n      vars: 8,\n      consts: [[1, \"block-section\"], [3, \"class\", \"ngStyle\", 4, \"ngIf\"], [1, \"block-content\"], [3, \"ngStyle\"], [1, \"block-title\", \"w-full\", \"align-items-center\", 3, \"ngClass\"], [\"class\", \"header-icon flex mr-2\", 4, \"ngIf\"], [\"tooltipPosition\", \"top\", 3, \"pTooltip\"], [\"class\", \" text-base font-medium\", 4, \"ngIf\"], [\"class\", \"badge-new\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"block-actions text-sm\", 4, \"ngIf\"], [1, \"header-icon\", \"flex\", \"mr-2\"], [\"height\", \"21\", 3, \"src\"], [1, \"text-base\", \"font-medium\"], [1, \"badge-new\"], [\"type\", \"button\", 1, \"p-dialog-header-maximize\", \"p-link\", 3, \"click\"], [1, \"p-dialog-header-close-icon\", \"pi\", \"pi-times\", \"p-1\", \"border-circle\"], [1, \"block-actions\", \"text-sm\"], [\"tabindex\", \"0\", 3, \"click\", \"ngClass\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"absolute\", 2, \"z-index\", \"4\"], [3, \"click\", \"label\"]],\n      template: function BlockViewerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, BlockViewerComponent_div_1_Template, 10, 20, \"div\", 1);\n          i0.ɵɵprojection(2);\n          i0.ɵɵelementStart(3, \"div\", 2)(4, \"div\", 3);\n          i0.ɵɵprojection(5, 1);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.blockClass);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.header);\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassMap(ctx.containerClass);\n          i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(6, _c2, ctx.containerHeight));\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgIf, i2.NgStyle, i3.Tooltip, i4.GHeaderBackButtonComponent],\n      styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}.block-section[_ngcontent-%COMP%]{background:linear-gradient(to bottom,transparent 0%,#fff 10%)}.block-section.bigger-header[_ngcontent-%COMP%]   .block-header[_ngcontent-%COMP%]{min-height:64px}@media only screen and (max-width: 768px){.block-section.bigger-header[_ngcontent-%COMP%]   .block-header[_ngcontent-%COMP%]{min-height:55px}.block-section.bigger-header[_ngcontent-%COMP%]   .block-header[_ngcontent-%COMP%]   .font-sm[_ngcontent-%COMP%]{font-size:1.5rem}}.block-section.bigger-header[_ngcontent-%COMP%]   .block-header[_ngcontent-%COMP%]   .block-title[_ngcontent-%COMP%]{font-family:Proxima Nova Semibold,sans-serif}.block-header[_ngcontent-%COMP%]{padding:.5rem 1rem;display:flex;align-items:center;justify-content:space-between;background-size:cover;background-repeat:no-repeat;background-position:center;border-radius:12px;min-height:37px;background-size:inherit}.block-header.blue-dark[_ngcontent-%COMP%]{background-image:linear-gradient(180deg,#6779e0,#6273d5,#5c6dcc,#5363c1,#4e5db7,#4756ae 60%,#3f4ea2 71%,#384799,#32428f 91%,#2c3b87)}.block-header.purple-gradient[_ngcontent-%COMP%]{background-image:linear-gradient(180deg,#8d7cf8,#8779f6,#8275f5,#7c71f4,#796ef2 44%,#716af0 56%,#6c68ee,#6b68ed,#6565ec,#5d5fea)}.block-header.blue-completed[_ngcontent-%COMP%]{background-image:linear-gradient(90deg,#315daf,#3264b3 15%,#366bba 29%,#3874c2 42%,#3b79c4 54%,#4080c9,#4487cf 74%,#498fd4,#4b94d8 92%,#509bdc)}.block-header.blue-completed-trial[_ngcontent-%COMP%]{background:linear-gradient(#64cfdd,#48d 60.34%,#657aef);background-image:linear-gradient(85deg,#468add,#3a8fdf,#3395e1,#329ee2,#32a4e2 44%,#33aae1 56%,#3db0e1,#48b7e0,#54bcde,#5fc2dd)}.block-header[_ngcontent-%COMP%]   .block-title[_ngcontent-%COMP%]{font-family:Proxima Nova Regular,sans-serif;display:inline-flex;align-items:center;color:#fff}.block-header[_ngcontent-%COMP%]   .block-title[_ngcontent-%COMP%]   .badge-free[_ngcontent-%COMP%]{border-radius:4px;padding:.25rem .5rem;background-color:var(--orange-500);color:#fff;margin-left:1rem;font-family:Proxima Nova Semibold,sans-serif;font-size:.875rem}.block-header[_ngcontent-%COMP%]   .block-actions[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between;-webkit-user-select:none;user-select:none;margin-left:1rem}.block-header[_ngcontent-%COMP%]   .block-actions[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{display:flex;align-items:center;margin-right:.75rem;padding:.5rem 1rem;border-radius:4px;border:1px solid transparent;transition:background-color .2s;cursor:pointer}.block-header[_ngcontent-%COMP%]   .block-actions[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:last-child{margin-right:0}.block-header[_ngcontent-%COMP%]   .block-actions[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:not(.block-action-disabled):hover{background-color:var(--surface-hover);color:inherit}.block-header[_ngcontent-%COMP%]   .block-actions[_ngcontent-%COMP%]   a.block-action-active[_ngcontent-%COMP%]{background:transparent;border:1px solid #fff;border-radius:50px;padding:0rem 1rem;color:#fff;min-width:100px;justify-content:center}.block-header[_ngcontent-%COMP%]   .block-actions[_ngcontent-%COMP%]   a.block-action-copy[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:var(--primary-color);font-size:1.25rem}.block-header[_ngcontent-%COMP%]   .block-actions[_ngcontent-%COMP%]   a.block-action-disabled[_ngcontent-%COMP%]{opacity:.6;cursor:auto!important}.block-header[_ngcontent-%COMP%]   .block-actions[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:.5rem}.block-content[_ngcontent-%COMP%]{padding:0;border-top:0 none}@media screen and (max-width: 575px){.block-header[_ngcontent-%COMP%]{flex-direction:row;align-items:start}.block-header[_ngcontent-%COMP%]   .block-actions[_ngcontent-%COMP%]{margin-top:0;margin-left:0}}.dash__menu-mobile[_ngcontent-%COMP%]{background-image:linear-gradient(65deg,#4c65f6,#4e64f4,#4f62f3 30%,#5567f1 44%,#5666f0 57%,#5864ee,#5d67ee 77%,#5f66ec,#6067eb 93%,#6266ea)}.grad-blue-title[_ngcontent-%COMP%]{background-image:linear-gradient(180deg,#6779e0,#6273d5,#5c6dcc,#5363c1,#4e5db7,#4756ae 60%,#3f4ea2 71%,#384799,#32428f 91%,#2c3b87)}.p-dialog-header-close-icon[_ngcontent-%COMP%]{background-color:#fff;position:absolute;z-index:5555;top:6px;right:14px}@media only screen and (min-width: 768px) and (max-width: 769px){.dash__menu-mobile[_ngcontent-%COMP%]{display:none}}\"],\n      changeDetection: 0\n    });\n  }\n  return BlockViewerComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}