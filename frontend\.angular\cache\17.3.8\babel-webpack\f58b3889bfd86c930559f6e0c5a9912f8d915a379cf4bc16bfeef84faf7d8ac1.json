{"ast": null, "code": "import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nimport * as i2 from \"src/app/core/services/teacher-application.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nfunction ContactInfoComponent_form_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtext(1, \"* Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContactInfoComponent_form_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtext(1, \"* Phone is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContactInfoComponent_form_1_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtext(1, \"* Skype is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ContactInfoComponent_form_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"form\", 5)(1, \"div\", 6)(2, \"div\", 7)(3, \"div\", 8)(4, \"div\", 9);\n    i0.ɵɵtext(5, \"* E-mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"input\", 10);\n    i0.ɵɵtemplate(7, ContactInfoComponent_form_1_div_7_Template, 2, 0, \"div\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 8)(9, \"div\", 9);\n    i0.ɵɵtext(10, \"* Phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"input\", 12);\n    i0.ɵɵtemplate(12, ContactInfoComponent_form_1_div_12_Template, 2, 0, \"div\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 8)(14, \"div\", 9);\n    i0.ɵɵtext(15, \"* Skype\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"input\", 13);\n    i0.ɵɵtemplate(17, ContactInfoComponent_form_1_div_17_Template, 2, 0, \"div\", 11);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.form);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.ifFieldValid(\"email\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.ifFieldValid(\"phone\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.ifFieldValid(\"skype\"));\n  }\n}\nexport let ContactInfoComponent = /*#__PURE__*/(() => {\n  class ContactInfoComponent {\n    constructor(generalService, teacherService, router, location) {\n      this.generalService = generalService;\n      this.teacherService = teacherService;\n      this.router = router;\n      this.location = location;\n      this.subs = new SubSink();\n      this.form = new UntypedFormGroup({});\n      this.isTablet = false;\n      this.teacher = this.teacherService.dummyTeacher;\n      this.tryToSave = false;\n      this.isLoading = false;\n    }\n    ngOnInit() {\n      this.subs.add(this.teacherService.submitOnMenuClickListener.subscribe(res => {\n        if (res) {\n          this.onSubmit();\n        }\n      }));\n      this.teacherService.setCurrentStepIndex(1);\n      this.subs.add(this.generalService.deviceKind.subscribe(res => {\n        this.isTablet = res.is1024;\n      }));\n      this.form = new UntypedFormGroup({\n        email: new UntypedFormControl(this.teacher.email, {\n          validators: [Validators.required]\n        }),\n        phone: new UntypedFormControl(this.teacher.phone, {\n          validators: [Validators.required]\n        }),\n        skype: new UntypedFormControl(this.teacher.skype, {\n          validators: [Validators.required]\n        })\n      });\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n      this.teacherService.setSubmitOnMenuClickListener(false);\n    }\n    onSubmit() {\n      this.tryToSave = true;\n      if (!this.form.valid) {\n        return;\n      }\n      this.teacher.email = this.form.value.email;\n      this.teacher.phone = this.form.value.phone;\n      this.teacher.skype = this.form.value.skype;\n      this.router.navigateByUrl('/teacher/education', {\n        replaceUrl: true\n      });\n    }\n    goBack() {\n      this.router.navigateByUrl('/teacher/info', {\n        replaceUrl: true\n      });\n    }\n    ifFieldValid(field) {\n      this.teacherService.setStepValid(1, this.form, 'teacher-contact-route');\n      return this.form.get(field)?.invalid && this.form.get(field)?.touched || this.tryToSave && this.form.get(field)?.invalid;\n    }\n    static #_ = this.ɵfac = function ContactInfoComponent_Factory(t) {\n      return new (t || ContactInfoComponent)(i0.ɵɵdirectiveInject(i1.GeneralService), i0.ɵɵdirectiveInject(i2.TeacherApplicationService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.Location));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ContactInfoComponent,\n      selectors: [[\"app-contact-info\"]],\n      decls: 7,\n      vars: 1,\n      consts: [[1, \"profile-info\"], [3, \"formGroup\", 4, \"ngIf\"], [1, \"btns\"], [1, \"link-main-color\", 3, \"click\"], [1, \"light-purple-button\", 3, \"click\"], [3, \"formGroup\"], [1, \"profile-info-section\", 2, \"padding-top\", \"0\"], [1, \"input-fields\"], [1, \"input-field\"], [1, \"input-element-title\"], [\"formControlName\", \"email\", \"type\", \"text\", 1, \"input-element\"], [\"class\", \"input-error\", 4, \"ngIf\"], [\"formControlName\", \"phone\", \"type\", \"text\", 1, \"input-element\"], [\"formControlName\", \"skype\", \"type\", \"text\", 1, \"input-element\"], [1, \"input-error\"]],\n      template: function ContactInfoComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, ContactInfoComponent_form_1_Template, 18, 4, \"form\", 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵlistener(\"click\", function ContactInfoComponent_Template_div_click_3_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵtext(4, \"Back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4);\n          i0.ɵɵlistener(\"click\", function ContactInfoComponent_Template_div_click_5_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtext(6, \"Next\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i4.NgIf, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.FormGroupDirective, i5.FormControlName],\n      styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}.modal[_ngcontent-%COMP%]{min-width:50vw}[_nghost-%COMP%]     .p-dropdown:not(.p-disabled):hover{border:2px solid rgb(228,185,84)}  .no-label>.p-button-label{display:none}input[type=file][_ngcontent-%COMP%]{visibility:hidden;width:10px}input[type=date][_ngcontent-%COMP%]{position:relative;padding:10px}input[type=date][_ngcontent-%COMP%]::-webkit-calendar-picker-indicator{color:transparent;background:none;z-index:1}input[type=date][_ngcontent-%COMP%]:before{color:transparent;background:none;display:block;font-family:FontAwesome;content:\\\"\\\\f073\\\";width:20px;height:25px;position:absolute;top:12px;right:12px;color:#999}.image-preview[_ngcontent-%COMP%]{height:10rem;margin:1rem 0;border-radius:50%;border:3px solid var(--main-color)}.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{height:100%}[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-header{padding:clamp(.38rem,.47vw + .28rem,.75rem)}[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-items .p-dropdown-item{margin-bottom:clamp(.38rem,.47vw + .28rem,.75rem)}.country-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:clamp(.75rem,.31vw + .69rem,1rem);font-size:clamp(.88rem,.16vw + .84rem,1rem)}.country-item[_ngcontent-%COMP%]   .country-name[_ngcontent-%COMP%]{white-space:pre-line}.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;display:inline-block;vertical-align:middle}.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;clip-path:circle()}.country-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:32px;margin-right:.5rem}.country-item[_ngcontent-%COMP%]   img.smaller[_ngcontent-%COMP%]{width:16px}.info-element[_ngcontent-%COMP%]{padding:10px 0}.btns[_ngcontent-%COMP%]{justify-content:end;bottom:20px;right:20px}.btns[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{margin:15px}.white-button[_ngcontent-%COMP%]{padding:10px}.input-fields[_ngcontent-%COMP%]{flex-direction:column}\"]\n    });\n  }\n  return ContactInfoComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}