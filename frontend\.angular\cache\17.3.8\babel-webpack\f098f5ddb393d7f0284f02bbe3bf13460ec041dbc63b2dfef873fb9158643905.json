{"ast": null, "code": "import { take } from 'rxjs/operators';\nimport { Feature, LikelyOptions } from 'src/app/core/models/experience.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/rating-and-report.service\";\nimport * as i2 from \"src/app/core/services/toast.service\";\nexport let RateExperienceComponent = /*#__PURE__*/(() => {\n  class RateExperienceComponent {\n    constructor(ratingService, toastService) {\n      this.ratingService = ratingService;\n      this.toastService = toastService;\n      this.studentExperience = {};\n      this.LikelyOptions = LikelyOptions;\n      this.Feature = Feature;\n      this.missing = {};\n      this.missingInput = \"\";\n      this.improve = {};\n      this.improveInput = \"\";\n      this.platformIssues = {};\n      this.platformIssuesInput = \"\";\n      this.teacherIssues = {};\n      this.teacherIssuesInput = \"\";\n      this.features = [];\n    }\n    ngOnInit() {}\n    submit() {\n      this.missing.reason = this.missingInput;\n      this.improve.reason = this.improveInput;\n      this.platformIssues.reason = this.platformIssuesInput;\n      this.teacherIssues.reason = this.teacherIssuesInput;\n      this.studentExperience.somethingMissing = this.missing;\n      this.studentExperience.improve = this.improve;\n      this.studentExperience.platformIssues = this.platformIssues;\n      this.studentExperience.teacherIssues = this.teacherIssues;\n      this.studentExperience.featuresUsed = this.features;\n      this.ratingService.submitStudentExperience(this.studentExperience).pipe(take(1)).subscribe(res => {\n        console.log(res);\n      });\n    }\n    chooseLanguageLessons(answer) {\n      this.studentExperience.languageLessons = answer;\n    }\n    chooseTeacher(answer) {\n      this.studentExperience.designatedTeacher = answer;\n    }\n    chooseSchedule(answer) {\n      this.studentExperience.schedule = answer;\n    }\n    chooseServices(answer) {\n      this.studentExperience.services = answer;\n    }\n    chooseProgress(answer) {\n      this.studentExperience.progress = answer;\n    }\n    chooseCustomerService(answer) {\n      this.studentExperience.customerService = answer;\n    }\n    chooseIssues(answer) {\n      this.studentExperience.issuesResolved = answer;\n    }\n    chooseRecommend(answer) {\n      this.studentExperience.recommendPlatform = answer;\n    }\n    choosePositiveReview(answer) {\n      this.studentExperience.positiveReview = answer;\n    }\n    chooseMissing(answer) {\n      this.missing.answer = answer;\n    }\n    chooseImprove(answer) {\n      this.improve.answer = answer;\n    }\n    chooseContinue(answer) {\n      this.studentExperience.useAgain = answer;\n    }\n    chooseFeature(feature) {\n      if (this.features.includes(feature)) {\n        let index = this.features.findIndex(el => el == feature);\n        this.features.splice(index, 1);\n      } else {\n        this.features.push(feature);\n      }\n    }\n    chooseDesiredLevel(event) {\n      this.studentExperience.desiredLevelReached = event.checked;\n    }\n    chooseCertificateWanted(event) {\n      this.studentExperience.certificateWanted = event.checked;\n    }\n    chooseBizy(event) {\n      this.studentExperience.busySchedule = event.checked;\n    }\n    chooseFinancial(event) {\n      this.studentExperience.financial = event.checked;\n    }\n    chooseInterest(event) {\n      this.studentExperience.interest = event.checked;\n    }\n    choosePlatformIssues(event) {\n      this.platformIssues.answer = event.checked;\n    }\n    chooseTeacherIssues(event) {\n      this.teacherIssues.answer = event.checked;\n    }\n    static #_ = this.ɵfac = function RateExperienceComponent_Factory(t) {\n      return new (t || RateExperienceComponent)(i0.ɵɵdirectiveInject(i1.RatingAndReportService), i0.ɵɵdirectiveInject(i2.ToastService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RateExperienceComponent,\n      selectors: [[\"app-rate-experience\"]],\n      decls: 0,\n      vars: 0,\n      template: function RateExperienceComponent_Template(rf, ctx) {},\n      styles: [\"[_nghost-%COMP%]     .e-frame.e-check{background-color:var(--main-color)!important;width:20px!important;height:20px!important}[_nghost-%COMP%]     .e-checkbox-wrapper .e-frame{width:20px!important;height:20px!important;line-height:16px}[_nghost-%COMP%]     .e-checkbox-wrapper .e-checkbox:focus+.e-frame{width:20px!important;height:20px!important}[_nghost-%COMP%]     .e-icons.e-check:before{font-size:13px!important}[_nghost-%COMP%]     .e-radio:focus+label:before{border-color:var(--main-color)!important;box-shadow:none!important}[_nghost-%COMP%]     .e-radio:focus+label:before{border-color:#757575!important;box-shadow:none!important}[_nghost-%COMP%]     .e-radio:hover+label:before{border-color:#757575!important}[_nghost-%COMP%]     .e-radio:checked+label:before{background-color:#fff!important;border-color:var(--main-color)!important}[_nghost-%COMP%]     .e-radio:checked+label:after{background-color:var(--main-color)!important;color:var(--main-color)!important}[_nghost-%COMP%]     .e-radio:checked+label:active .e-ripple-element{background-color:#e3165b42!important}[_nghost-%COMP%]     .e-radio:checked+.e-focus .e-ripple-container{background-color:#e3165b42!important}[_nghost-%COMP%]     .e-radio:checked+.e-focus:before{outline:#fff 0 solid!important;outline-offset:0!important}[_nghost-%COMP%]     .e-radio:checked:focus+label:before{border-color:var(--main-color)!important}[_nghost-%COMP%]     .e-radio:checked:focus+label:after{background-color:var(--main-color)!important}[_nghost-%COMP%]     .e-radio:checked+label:hover:before{border-color:var(--main-color)!important}[_nghost-%COMP%]     .e-radio:checked+label:hover:after{background-color:var(--main-color)!important}[_nghost-%COMP%]     .e-radio+label:after{border:2px solid!important;height:12px!important;left:3px!important;top:3px!important;width:12px!important}[_nghost-%COMP%]     .e-label{font-size:15px!important}.no[_ngcontent-%COMP%]{margin-right:29px}.rate[_ngcontent-%COMP%]{padding:20px;width:90%}.rate-title[_ngcontent-%COMP%]{font-weight:700;color:var(--main-color);font-size:18px;margin:20px}.rate-subtitle[_ngcontent-%COMP%]{color:#93949e;font-weight:700;font-size:15px;margin:20px}.questions[_ngcontent-%COMP%]{margin-top:50px}.rate-question[_ngcontent-%COMP%]{font-weight:700;font-size:15px;padding:20px;display:flex;flex-wrap:wrap;justify-content:space-evenly}.rate-question[_ngcontent-%COMP%]   .question[_ngcontent-%COMP%]{width:50%;padding-right:15px;box-sizing:border-box}.rate-question[_ngcontent-%COMP%]   .answer[_ngcontent-%COMP%]{display:flex;width:50%;justify-content:space-between;flex-wrap:wrap}.rate-question[_ngcontent-%COMP%]   .answer-with-input[_ngcontent-%COMP%]{width:50%;display:flex;flex-direction:column}.rate-question[_ngcontent-%COMP%]   .answer-with-input[_ngcontent-%COMP%]   .answer-radio[_ngcontent-%COMP%]{display:flex;width:100%;justify-content:space-between}.rate-question[_ngcontent-%COMP%]   .answer-with-input[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%]{margin-top:10px}.answer-checkbox[_ngcontent-%COMP%]{width:50%;padding:20px;font-weight:700!important}\"]\n    });\n  }\n  return RateExperienceComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}