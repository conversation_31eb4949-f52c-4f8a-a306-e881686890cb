{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../classroom/packages/packages.component\";\nexport let PackageHistoryComponent = /*#__PURE__*/(() => {\n  class PackageHistoryComponent {\n    constructor() {}\n    ngOnInit() {}\n    static #_ = this.ɵfac = function PackageHistoryComponent_Factory(t) {\n      return new (t || PackageHistoryComponent)();\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PackageHistoryComponent,\n      selectors: [[\"app-package-history\"]],\n      decls: 1,\n      vars: 0,\n      template: function PackageHistoryComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"app-packages\");\n        }\n      },\n      dependencies: [i1.PackagesComponent]\n    });\n  }\n  return PackageHistoryComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}