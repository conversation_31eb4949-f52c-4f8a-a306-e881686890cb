{"ast": null, "code": "import { BehaviorSubject, Observable } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport { UserAvailabilityType } from '../models/user.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nconst BACKEND_URL = environment.apiUrl + \"/User/\";\nconst BACKEND_URL_LMS = environment.apiUrl + \"/LMS/\";\nexport let UserService = /*#__PURE__*/(() => {\n  class UserService {\n    constructor(http) {\n      this.http = http;\n      this.update$ = new BehaviorSubject(false);\n      this.update = this.update$.asObservable();\n      this.updateProfile$ = new BehaviorSubject(false);\n      this.updateProfile = this.updateProfile$.asObservable();\n      this.uploadFiles$ = new BehaviorSubject(false);\n      this.uploadFiles = this.uploadFiles$.asObservable();\n      this.newFileUpload$ = new BehaviorSubject({});\n      this.newFileUpload = this.newFileUpload$.asObservable();\n    }\n    updateUserListener() {\n      this.update$.next(true);\n    }\n    updateUserProfile(user) {\n      this.update$.next(true);\n      return this.http.post(BACKEND_URL_LMS + \"UpdateUserProfile\", user);\n    }\n    setUpdateProfile(update) {\n      this.updateProfile$.next(update);\n    }\n    setUploadFiles(update) {\n      this.uploadFiles$.next(update);\n    }\n    setNewFileUpload(update) {\n      this.newFileUpload$.next(update);\n    }\n    setUserPassword(user) {\n      return this.http.post(BACKEND_URL_LMS + \"SetPassword\", user);\n    }\n    getUserPhoto(user) {\n      let photo = user?.avatarUrl && user?.avatarUrl !== 'Not Given' ? environment.apiUrl + \"/\" + user?.avatarUrl : \"/assets/images/default-user-avatar.png\";\n      return photo;\n    }\n    getUserAvatar(user) {\n      let photo = user?.photo && user?.photo !== 'Not Given' ? environment.apiUrl + \"/\" + user?.photo : \"/assets/images/avatar.svg\";\n      return photo;\n    }\n    updateAvatarUrl(newAvatarUrl) {\n      // Step 1: Retrieve the existing value from localStorage\n      const existingValue = localStorage.getItem('user');\n      if (existingValue) {\n        // Step 2: Modify the retrieved value\n        const parsedValue = JSON.parse(existingValue);\n        parsedValue.avatarUrl = newAvatarUrl;\n        // Step 3: Store the updated value back into localStorage\n        localStorage.setItem('user', JSON.stringify(parsedValue));\n      }\n    }\n    getUserAvailability(userId) {\n      return this.http.get(BACKEND_URL_LMS + \"GetUserAvailability?userId=\" + userId).pipe(map(res => {\n        Object.keys(res).forEach(key => {\n          if (res[key] == null) res[key] = [];\n        });\n        return res;\n      }));\n    }\n    getUserDaysOff(userId) {\n      return this.http.get(environment.apiUrl + \"/Teacher/GetUserDaysOff?userId=\" + userId).pipe(map(res => {\n        for (let dayOff of res) {\n          dayOff.period.from = new Date(new Date(dayOff.period.from).setHours(0, 0));\n          dayOff.period.to = new Date(new Date(dayOff.period.to).setHours(0, 0));\n        }\n        return res;\n      }));\n    }\n    getUserAbsences(userId) {\n      console.log(userId);\n      return this.http.get(environment.apiUrl + \"/Lesson/GetUserAbsences?userId=\" + userId);\n    }\n    getUserHasPayment() {\n      return this.http.get(BACKEND_URL_LMS + 'StudentHasPayment');\n    }\n    getUserById(userId) {\n      return this.http.get(BACKEND_URL + 'GetUserById?userId=' + userId);\n    }\n    calculateAge(birthday) {\n      var ageDifMs = Date.now() - birthday.getTime();\n      var ageDate = new Date(ageDifMs); // miliseconds from epoch\n      return Math.abs(ageDate.getUTCFullYear() - 1970);\n    }\n    getEmptyAvailability() {\n      return {\n        id: \"availability_2\",\n        type: UserAvailabilityType.AVAILABLE,\n        mon: [],\n        tue: [],\n        wed: [],\n        thu: [],\n        fri: [],\n        sat: [],\n        sun: []\n      };\n    }\n    /**\n     * Checks if there are any unsaved changes and prompts the user with a confirmation message if there are.\n     * @param {function} hasUnsavedChanges - A function that returns a boolean value indicating whether there are unsaved changes.\n     * @param {object} confirmationService - An object that provides methods for displaying confirmation messages to the user.\n     * @returns {Observable|boolean} - An Observable that emits a boolean value indicating whether the user accepts or rejects the confirmation message, or a boolean value of true if there are no unsaved changes.\n     */\n    comfirmDiscardUnsavedChanges(hasUnsavedChanges, confirmationService) {\n      if (hasUnsavedChanges()) {\n        return new Observable(observer => {\n          confirmationService.confirm({\n            header: '',\n            key: 'stepLeaveConfirmation',\n            message: 'Unsaved changes will be discarded. <br> Do you still wish to navigate away from this page?.',\n            accept: () => {\n              observer.next(true);\n              observer.complete();\n            },\n            reject: () => {\n              observer.next(false);\n              observer.complete();\n            }\n          });\n        });\n      } else {\n        return true;\n      }\n    }\n    generateReferralCodeFromUser(user) {\n      const firstNamePart = user.firstName.substring(0, 3).toLowerCase().replace(/\\s+/g, '');\n      const lastNamePart = user.lastName.toLowerCase().replace(/\\s+/g, '');\n      return `${firstNamePart}.${lastNamePart}.ref`;\n    }\n    static #_ = this.ɵfac = function UserService_Factory(t) {\n      return new (t || UserService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UserService,\n      factory: UserService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return UserService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}