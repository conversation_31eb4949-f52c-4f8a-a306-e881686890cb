{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Punjabi (India) [pa-in]\n//! author : <PERSON><PERSON><PERSON> : https://github.com/harpreetkhalsagtbit\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var symbolMap = {\n      1: '੧',\n      2: '੨',\n      3: '੩',\n      4: '੪',\n      5: '੫',\n      6: '੬',\n      7: '੭',\n      8: '੮',\n      9: '੯',\n      0: '੦'\n    },\n    numberMap = {\n      '੧': '1',\n      '੨': '2',\n      '੩': '3',\n      '੪': '4',\n      '੫': '5',\n      '੬': '6',\n      '੭': '7',\n      '੮': '8',\n      '੯': '9',\n      '੦': '0'\n    };\n  var paIn = moment.defineLocale('pa-in', {\n    // There are months name as per Nanakshahi Calendar but they are not used as rigidly in modern Punjabi.\n    months: 'ਜਨਵਰੀ_ਫ਼ਰਵਰੀ_ਮਾਰਚ_ਅਪ੍ਰੈਲ_ਮਈ_ਜੂਨ_ਜੁਲਾਈ_ਅਗਸਤ_ਸਤੰਬਰ_ਅਕਤੂਬਰ_ਨਵੰਬਰ_ਦਸੰਬਰ'.split('_'),\n    monthsShort: 'ਜਨਵਰੀ_ਫ਼ਰਵਰੀ_ਮਾਰਚ_ਅਪ੍ਰੈਲ_ਮਈ_ਜੂਨ_ਜੁਲਾਈ_ਅਗਸਤ_ਸਤੰਬਰ_ਅਕਤੂਬਰ_ਨਵੰਬਰ_ਦਸੰਬਰ'.split('_'),\n    weekdays: 'ਐਤਵਾਰ_ਸੋਮਵਾਰ_ਮੰਗਲਵਾਰ_ਬੁਧਵਾਰ_ਵੀਰਵਾਰ_ਸ਼ੁੱਕਰਵਾਰ_ਸ਼ਨੀਚਰਵਾਰ'.split('_'),\n    weekdaysShort: 'ਐਤ_ਸੋਮ_ਮੰਗਲ_ਬੁਧ_ਵੀਰ_ਸ਼ੁਕਰ_ਸ਼ਨੀ'.split('_'),\n    weekdaysMin: 'ਐਤ_ਸੋਮ_ਮੰਗਲ_ਬੁਧ_ਵੀਰ_ਸ਼ੁਕਰ_ਸ਼ਨੀ'.split('_'),\n    longDateFormat: {\n      LT: 'A h:mm ਵਜੇ',\n      LTS: 'A h:mm:ss ਵਜੇ',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY, A h:mm ਵਜੇ',\n      LLLL: 'dddd, D MMMM YYYY, A h:mm ਵਜੇ'\n    },\n    calendar: {\n      sameDay: '[ਅਜ] LT',\n      nextDay: '[ਕਲ] LT',\n      nextWeek: '[ਅਗਲਾ] dddd, LT',\n      lastDay: '[ਕਲ] LT',\n      lastWeek: '[ਪਿਛਲੇ] dddd, LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s ਵਿੱਚ',\n      past: '%s ਪਿਛਲੇ',\n      s: 'ਕੁਝ ਸਕਿੰਟ',\n      ss: '%d ਸਕਿੰਟ',\n      m: 'ਇਕ ਮਿੰਟ',\n      mm: '%d ਮਿੰਟ',\n      h: 'ਇੱਕ ਘੰਟਾ',\n      hh: '%d ਘੰਟੇ',\n      d: 'ਇੱਕ ਦਿਨ',\n      dd: '%d ਦਿਨ',\n      M: 'ਇੱਕ ਮਹੀਨਾ',\n      MM: '%d ਮਹੀਨੇ',\n      y: 'ਇੱਕ ਸਾਲ',\n      yy: '%d ਸਾਲ'\n    },\n    preparse: function (string) {\n      return string.replace(/[੧੨੩੪੫੬੭੮੯੦]/g, function (match) {\n        return numberMap[match];\n      });\n    },\n    postformat: function (string) {\n      return string.replace(/\\d/g, function (match) {\n        return symbolMap[match];\n      });\n    },\n    // Punjabi notation for meridiems are quite fuzzy in practice. While there exists\n    // a rigid notion of a 'Pahar' it is not used as rigidly in modern Punjabi.\n    meridiemParse: /ਰਾਤ|ਸਵੇਰ|ਦੁਪਹਿਰ|ਸ਼ਾਮ/,\n    meridiemHour: function (hour, meridiem) {\n      if (hour === 12) {\n        hour = 0;\n      }\n      if (meridiem === 'ਰਾਤ') {\n        return hour < 4 ? hour : hour + 12;\n      } else if (meridiem === 'ਸਵੇਰ') {\n        return hour;\n      } else if (meridiem === 'ਦੁਪਹਿਰ') {\n        return hour >= 10 ? hour : hour + 12;\n      } else if (meridiem === 'ਸ਼ਾਮ') {\n        return hour + 12;\n      }\n    },\n    meridiem: function (hour, minute, isLower) {\n      if (hour < 4) {\n        return 'ਰਾਤ';\n      } else if (hour < 10) {\n        return 'ਸਵੇਰ';\n      } else if (hour < 17) {\n        return 'ਦੁਪਹਿਰ';\n      } else if (hour < 20) {\n        return 'ਸ਼ਾਮ';\n      } else {\n        return 'ਰਾਤ';\n      }\n    },\n    week: {\n      dow: 0,\n      // Sunday is the first day of the week.\n      doy: 6 // The week that contains Jan 6th is the first week of the year.\n    }\n  });\n  return paIn;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}