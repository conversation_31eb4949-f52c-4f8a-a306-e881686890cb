{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport { StudentTrialStatus } from \"../models/student.model\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nconst BACKEND_LMS_URL = environment.apiUrl + \"/LMS/\";\nexport class StudentTrialService {\n  constructor(http) {\n    this.http = http;\n    this.studentStatusSubject = new BehaviorSubject(StudentTrialStatus.NONE);\n    this.studentStatus$ = this.studentStatusSubject.asObservable();\n    // load the trial request status from sessionStorage\n    this.isTrialRequested = JSON.parse(localStorage.getItem('isTrialRequested')) || false;\n  }\n  setTrialRequested(value) {\n    // set the trial request status and save to localStorage\n    this.isTrialRequested = value;\n    localStorage.setItem('isTrialRequested', JSON.stringify(this.isTrialRequested));\n    // localStorage.setItem('isTrialRequested', JSON.stringify(value));\n  }\n  hasTrialRequested() {\n    return localStorage.getItem('isTrialRequested');\n  }\n  updateStudentTrialStatus(status) {\n    this.studentStatusSubject.next(status);\n  }\n  getStudentHasTrialRequest() {\n    return this.http.get(BACKEND_LMS_URL + 'StudentHasTrialRequest');\n  }\n  getStudentTrialStatus(headers) {\n    return this.http.get(BACKEND_LMS_URL + 'GetStatusTrial', {\n      headers,\n      responseType: 'text'\n    });\n  }\n  postRequestTrial(params) {\n    return this.http.post(BACKEND_LMS_URL + 'RequestTrial', params);\n  }\n  postRequestTrialInner(params) {\n    return this.http.post(BACKEND_LMS_URL + 'RequestFreeTrialInner', params);\n  }\n  postContactUs(params) {\n    return this.http.post(BACKEND_LMS_URL + 'ContactUs', params);\n  }\n  static #_ = this.ɵfac = function StudentTrialService_Factory(t) {\n    return new (t || StudentTrialService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: StudentTrialService,\n    factory: StudentTrialService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "environment", "StudentTrialStatus", "BACKEND_LMS_URL", "apiUrl", "StudentTrialService", "constructor", "http", "studentStatusSubject", "NONE", "studentStatus$", "asObservable", "isTrialRequested", "JSON", "parse", "localStorage", "getItem", "setTrialRequested", "value", "setItem", "stringify", "hasTrialRequested", "updateStudentTrialStatus", "status", "next", "getStudentHasTrialRequest", "get", "getStudentTrialStatus", "headers", "responseType", "postRequestTrial", "params", "post", "postRequestTrialInner", "postContactUs", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\services\\student-trial.service.ts"], "sourcesContent": ["import { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\nimport { RequestTrialRequest, StudentTrialStatus, ContactUsRequest } from \"../models/student.model\";\r\nconst BACKEND_LMS_URL = environment.apiUrl + \"/LMS/\";\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class StudentTrialService {\r\n  isTrialRequested: boolean;\r\n  private studentStatusSubject = new BehaviorSubject<StudentTrialStatus>(StudentTrialStatus.NONE);\r\n  studentStatus$ = this.studentStatusSubject.asObservable();\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n  ) {\r\n    // load the trial request status from sessionStorage\r\n    this.isTrialRequested = JSON.parse(localStorage.getItem('isTrialRequested') as string) || false;\r\n  }\r\n\r\n  setTrialRequested(value: boolean) {\r\n    // set the trial request status and save to localStorage\r\n    this.isTrialRequested = value;\r\n    localStorage.setItem('isTrialRequested', JSON.stringify(this.isTrialRequested));\r\n    // localStorage.setItem('isTrialRequested', JSON.stringify(value));\r\n  }\r\n\r\n  hasTrialRequested() {\r\n    return localStorage.getItem('isTrialRequested');\r\n  }\r\n\r\n  updateStudentTrialStatus(status: StudentTrialStatus) {\r\n    this.studentStatusSubject.next(status);\r\n  }\r\n\r\n  public getStudentHasTrialRequest(): Observable<any> {\r\n    return this.http.get(BACKEND_LMS_URL + 'StudentHasTrialRequest');\r\n  }\r\n\r\n  public getStudentTrialStatus(headers?: HttpHeaders): Observable<any> {\r\n    return this.http.get(BACKEND_LMS_URL + 'GetStatusTrial', { headers, responseType: 'text' });\r\n  }\r\n  \r\n  public postRequestTrial(params: RequestTrialRequest): Observable<RequestTrialRequest> {\r\n    return this.http.post<RequestTrialRequest>(BACKEND_LMS_URL + 'RequestTrial', params);\r\n  }\r\n\r\n  public postRequestTrialInner(params: RequestTrialRequest): Observable<RequestTrialRequest> {\r\n    return this.http.post<RequestTrialRequest>(BACKEND_LMS_URL + 'RequestFreeTrialInner', params);\r\n  }\r\n  \r\n  public postContactUs(params: ContactUsRequest): Observable<ContactUsRequest> {\r\n    return this.http.post<ContactUsRequest>(BACKEND_LMS_URL + 'ContactUs', params);\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,eAAe,QAAoB,MAAM;AAClD,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAA8BC,kBAAkB,QAA0B,yBAAyB;;;AACnG,MAAMC,eAAe,GAAGF,WAAW,CAACG,MAAM,GAAG,OAAO;AAKpD,OAAM,MAAOC,mBAAmB;EAK9BC,YACUC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAJN,KAAAC,oBAAoB,GAAG,IAAIR,eAAe,CAAqBE,kBAAkB,CAACO,IAAI,CAAC;IAC/F,KAAAC,cAAc,GAAG,IAAI,CAACF,oBAAoB,CAACG,YAAY,EAAE;IAKvD;IACA,IAAI,CAACC,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAW,CAAC,IAAI,KAAK;EACjG;EAEAC,iBAAiBA,CAACC,KAAc;IAC9B;IACA,IAAI,CAACN,gBAAgB,GAAGM,KAAK;IAC7BH,YAAY,CAACI,OAAO,CAAC,kBAAkB,EAAEN,IAAI,CAACO,SAAS,CAAC,IAAI,CAACR,gBAAgB,CAAC,CAAC;IAC/E;EACF;EAEAS,iBAAiBA,CAAA;IACf,OAAON,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;EACjD;EAEAM,wBAAwBA,CAACC,MAA0B;IACjD,IAAI,CAACf,oBAAoB,CAACgB,IAAI,CAACD,MAAM,CAAC;EACxC;EAEOE,yBAAyBA,CAAA;IAC9B,OAAO,IAAI,CAAClB,IAAI,CAACmB,GAAG,CAACvB,eAAe,GAAG,wBAAwB,CAAC;EAClE;EAEOwB,qBAAqBA,CAACC,OAAqB;IAChD,OAAO,IAAI,CAACrB,IAAI,CAACmB,GAAG,CAACvB,eAAe,GAAG,gBAAgB,EAAE;MAAEyB,OAAO;MAAEC,YAAY,EAAE;IAAM,CAAE,CAAC;EAC7F;EAEOC,gBAAgBA,CAACC,MAA2B;IACjD,OAAO,IAAI,CAACxB,IAAI,CAACyB,IAAI,CAAsB7B,eAAe,GAAG,cAAc,EAAE4B,MAAM,CAAC;EACtF;EAEOE,qBAAqBA,CAACF,MAA2B;IACtD,OAAO,IAAI,CAACxB,IAAI,CAACyB,IAAI,CAAsB7B,eAAe,GAAG,uBAAuB,EAAE4B,MAAM,CAAC;EAC/F;EAEOG,aAAaA,CAACH,MAAwB;IAC3C,OAAO,IAAI,CAACxB,IAAI,CAACyB,IAAI,CAAmB7B,eAAe,GAAG,WAAW,EAAE4B,MAAM,CAAC;EAChF;EAAC,QAAAI,CAAA,G;qBA7CU9B,mBAAmB,EAAA+B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAnBnC,mBAAmB;IAAAoC,OAAA,EAAnBpC,mBAAmB,CAAAqC,IAAA;IAAAC,UAAA,EAFlB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}