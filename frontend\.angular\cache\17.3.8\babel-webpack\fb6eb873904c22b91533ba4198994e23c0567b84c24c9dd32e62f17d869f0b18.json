{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ImageCropperModule } from 'ngx-image-cropper';\nimport { WindowService } from '../core/services/window.service';\nimport { CalendarModule } from 'primeng/calendar';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { MessageModule } from 'primeng/message';\nimport { MessagesModule } from 'primeng/messages';\nimport { ToastModule } from 'primeng/toast';\nimport { ConfirmationService, MessageService } from 'primeng/api';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { RippleModule } from 'primeng/ripple';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { MenuModule } from 'primeng/menu';\nimport { ScrollPanelModule } from 'primeng/scrollpanel';\nimport { PanelMenuModule } from 'primeng/panelmenu';\nimport { OverlayPanelModule } from 'primeng/overlaypanel';\nimport { BadgeModule } from 'primeng/badge';\nimport { DeferModule } from 'primeng/defer';\nimport { ChipModule } from 'primeng/chip';\nimport { CarouselModule } from 'primeng/carousel';\nimport { InputSwitchModule } from 'primeng/inputswitch';\nimport { AccordionModule } from 'primeng/accordion';\nimport { ButtonModule } from 'primeng/button';\nimport { ProgressBarModule } from 'primeng/progressbar';\nimport { TabViewModule } from 'primeng/tabview';\nimport { TagModule } from 'primeng/tag';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { StepsModule } from 'primeng/steps';\nimport { DialogModule } from 'primeng/dialog';\nimport { SliderModule } from 'primeng/slider';\nimport { RadioButtonModule as PrimeNgRadioButtonModule } from 'primeng/radiobutton';\nimport * as i0 from \"@angular/core\";\nexport let SharedModule = /*#__PURE__*/(() => {\n  class SharedModule {\n    static #_ = this.ɵfac = function SharedModule_Factory(t) {\n      return new (t || SharedModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SharedModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [WindowService, MessageService, ConfirmationService],\n      imports: [CommonModule, DropdownModule, FormsModule, ReactiveFormsModule, ImageCropperModule, CalendarModule, TooltipModule, MessageModule, MessagesModule, ToastModule, InputTextModule, RippleModule, ConfirmDialogModule, MenuModule, ScrollPanelModule, PanelMenuModule, OverlayPanelModule, BadgeModule, DeferModule, ChipModule, CarouselModule, InputSwitchModule, AccordionModule, ProgressBarModule, TabViewModule, TagModule, CheckboxModule, StepsModule, DialogModule, SliderModule, PrimeNgRadioButtonModule, InputSwitchModule, ButtonModule, ChipModule, AccordionModule, ProgressBarModule, TabViewModule, DialogModule, CheckboxModule, InputSwitchModule, SliderModule, PrimeNgRadioButtonModule, MenuModule, InputSwitchModule, ScrollPanelModule, OverlayPanelModule, TooltipModule]\n    });\n  }\n  return SharedModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}