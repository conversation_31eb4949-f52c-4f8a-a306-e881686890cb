{"ast": null, "code": "import { inject } from '@angular/core';\nimport { BehaviorSubject, map } from 'rxjs';\nimport { ClassroomType, Level } from '../models/classroom.model';\nimport { Dimentions } from '../models/general.model';\nimport { PackageDurationHours } from '../models/package.model';\nimport { saveAs } from 'file-saver';\nimport * as data from '../models/data';\nimport * as moment from 'moment-timezone';\nimport { environment } from 'src/environments/environment';\nimport { UserRole } from '../models/user.model';\nimport { AuthService } from './auth.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@angular/platform-browser\";\nconst API_URL = environment.apiUrl + \"/\";\nconst IP_COUNTRY_API = \"https://ipapi.co/json/\";\nexport class GeneralService {\n  constructor(router, http, sanitizer) {\n    this.router = router;\n    this.http = http;\n    this.sanitizer = sanitizer;\n    this.authService = inject(AuthService);\n    this.deviceKind$ = new BehaviorSubject({\n      w576down: false,\n      is576: false,\n      is768: false,\n      is992: false,\n      is1024: false,\n      is1366: false,\n      isBig: true\n    });\n    this.mobileMenuTitle$ = new BehaviorSubject('test');\n    this.deviceKind = this.deviceKind$.asObservable();\n    this.mobileMenuTitle = this.mobileMenuTitle$.asObservable();\n    this.socialLinks = {\n      linkedin: 'https://gr.linkedin.com/company/mylingotrip',\n      twitter: 'https://twitter.com/MyLingoTrip',\n      facebook: 'https://www.facebook.com/mylingotrip',\n      youtube: 'https://www.youtube.com/c/MyLingoTrip',\n      instagram: 'https://www.instagram.com/mylingotrip/',\n      tiktok: 'https://www.tiktok.com/@mylingotrip'\n    };\n    this.mltLanguages = [{\n      name: \"English\",\n      code: \"ENG\"\n    }, {\n      name: \"French\",\n      code: \"FR\"\n    }, {\n      name: \"German\",\n      code: \"GER\"\n    }, {\n      name: \"Italian\",\n      code: \"IT\"\n    }, {\n      name: \"Spanish\",\n      code: \"SP\"\n    }, {\n      name: \"Greek\",\n      code: \"GRE\"\n    }, {\n      name: \"Swedish\",\n      code: \"SW\"\n    }, {\n      name: \"Dutch\",\n      code: \"DUT\"\n    }, {\n      name: \"Russian\",\n      code: \"RUS\"\n    }, {\n      name: \"Japanese\",\n      code: \"JPN\"\n    }, {\n      name: \"Korean\",\n      code: \"KOR\"\n    }, {\n      name: \"Chinese\",\n      code: \"CHI\"\n    }];\n    this.mltLevels = [\n    // 'Any',\n    Level.A1, Level.A2, Level.B1, Level.B2, Level.C1, Level.C2, Level.NT21, Level.NT22, Level.TBD, Level.BS];\n    this.languagesToLearn = [{\n      title: \"English\",\n      link: \"\"\n    }, {\n      title: \"Frensh\",\n      link: \"\"\n    }, {\n      title: \"German\",\n      link: \"\"\n    }, {\n      title: \"Italian\",\n      link: \"\"\n    }, {\n      title: \"Spanish\",\n      link: \"\"\n    }, {\n      title: \"Greek\",\n      link: \"\"\n    }, {\n      title: \"Swedish\",\n      link: \"\"\n    }, {\n      title: \"Dutch\",\n      link: \"\"\n    }, {\n      title: \"Russian\",\n      link: \"\"\n    }, {\n      title: \"Japanese\",\n      link: \"\"\n    }, {\n      title: \"Korean\",\n      link: \"\"\n    }, {\n      title: \"Chinese\",\n      link: \"\"\n    }];\n    this.socialMedia = [{\n      icon: \"facebook.svg\",\n      link: \"\"\n    }, {\n      icon: \"linkedin.svg\",\n      link: \"\"\n    }, {\n      icon: \"youtube.svg\",\n      link: \"\"\n    }, {\n      icon: \"instagram.svg\",\n      link: \"\"\n    }, {\n      icon: \"tweeter.svg\",\n      link: \"\"\n    }];\n    this.languageLevels = [{\n      levelName: \"A1\",\n      showOnlyOnCode: [],\n      visible: true\n    }, {\n      levelName: \"A2\",\n      showOnlyOnCode: [],\n      visible: true\n    }, {\n      levelName: \"B1\",\n      showOnlyOnCode: [],\n      visible: true\n    }, {\n      levelName: \"B2\",\n      showOnlyOnCode: [],\n      visible: true\n    }, {\n      levelName: \"C1\",\n      showOnlyOnCode: [],\n      visible: true\n    }, {\n      levelName: \"C2\",\n      showOnlyOnCode: [],\n      visible: true\n    }, {\n      levelName: \"NT2.1\",\n      showOnlyOnCode: ['nl'],\n      visible: false\n    }, {\n      levelName: \"NT2.2\",\n      showOnlyOnCode: ['nl'],\n      visible: false\n    }];\n    this.packageDurationHours = PackageDurationHours;\n    this.countries = data.countries;\n    this.timezones = data.timezones;\n    this.languages = data.languages;\n    this.refs = [];\n  }\n  getLanguagesToLearn() {\n    return this.languagesToLearn;\n  }\n  getSocialMedia() {\n    return this.socialMedia;\n  }\n  getCountries() {\n    return data.countries;\n  }\n  getTimezones() {\n    return data.timezones;\n  }\n  getPhoneCodes() {\n    return data.phoneCodes;\n  }\n  getLanguageLevels() {\n    return this.languageLevels;\n  }\n  setDevice() {\n    var w576down = window.innerWidth < Dimentions.w576;\n    var w576up = window.innerWidth >= Dimentions.w576;\n    var w768up = window.innerWidth >= Dimentions.w768;\n    var w992up = window.innerWidth >= Dimentions.w992;\n    var w1024up = window.innerWidth >= Dimentions.w1024;\n    var w1366up = window.innerWidth >= Dimentions.w1366;\n    var w1500up = window.innerWidth >= Dimentions.w1500;\n    var w576 = window.innerWidth <= Dimentions.w576;\n    var w768 = window.innerWidth <= Dimentions.w768;\n    var w992 = window.innerWidth <= Dimentions.w992;\n    var w1024 = window.innerWidth <= Dimentions.w1024;\n    var w1366 = window.innerWidth <= Dimentions.w1366;\n    var w1500 = window.innerWidth <= Dimentions.w1500;\n    var isBig = window.innerWidth > Dimentions.w1366;\n    this.deviceKind$.next({\n      w576down: w576down,\n      is576: w576,\n      is768: w768,\n      is992: w992,\n      is1024: w1024,\n      is1366: w1366,\n      w576up: w576up,\n      w768up: w768up,\n      w992up: w992up,\n      w1024up: w1024up,\n      w1366up: w1366up,\n      w1500up: w1500up,\n      isBig: isBig\n    });\n  }\n  slideInElement(id) {\n    document.getElementById(id)?.classList.remove('no-visibility');\n    document.getElementById(id)?.classList.add('yes-visibility');\n    document.body.style.overflowY = 'hidden';\n  }\n  slideOutElement(id) {\n    document.getElementById(id)?.classList.remove('yes-visibility');\n    document.getElementById(id)?.classList.add('no-visibility');\n    document.body.style.overflowY = 'scroll';\n  }\n  slideInNativeElement(el) {\n    el.classList.remove('no-visibility');\n    el.classList.add('yes-visibility');\n    document.body.style.overflowY = 'hidden';\n  }\n  slideOutNativeElement(el) {\n    el.classList.remove('yes-visibility');\n    el.classList.add('no-visibility');\n    document.body.style.overflowY = 'scroll';\n  }\n  getDatesDiff(date1, date2) {\n    var diff = (date1.getTime() - date2.getTime()) / 1000;\n    var months = diff / (60 * 60 * 24 * 7);\n    var weeks = diff / (60 * 60 * 24 * 7);\n    var days = diff / (60 * 60 * 24);\n    var hours = diff / (60 * 60);\n    var minutes = diff / 60;\n    return {\n      weeks: Math.abs(Math.round(weeks)),\n      days: Math.abs(Math.round(days)),\n      hours: Math.abs(Math.round(hours)),\n      minutes: Math.abs(Math.round(minutes))\n    };\n  }\n  hideClassElements(classElements) {\n    [].forEach.call(classElements, el => {\n      el.style.display = 'none';\n    });\n  }\n  showClassElements(classElements, display) {\n    [].forEach.call(classElements, el => {\n      el.style.display = display;\n    });\n  }\n  collapseSection(element) {\n    // get the height of the element's inner content, regardless of its actual size\n    var sectionHeight = element.scrollHeight;\n    // temporarily disable all css transitions\n    var elementTransition = element.style.transition;\n    element.style.transition = '';\n    // on the next frame (as soon as the previous style change has taken effect),\n    // explicitly set the element's height to its current pixel height, so we \n    // aren't transitioning out of 'auto'\n    requestAnimationFrame(function () {\n      element.style.height = sectionHeight + 'px';\n      element.style.transition = elementTransition;\n      // on the next frame (as soon as the previous style change has taken effect),\n      // have the element transition to height: 0\n      requestAnimationFrame(function () {\n        element.style.height = 0 + 'px';\n      });\n    });\n    // mark the section as \"currently collapsed\"\n    element.setAttribute('data-collapsed', 'true');\n  }\n  expandSection(element) {\n    // get the height of the element's inner content, regardless of its actual size\n    var sectionHeight = element.scrollHeight;\n    // have the element transition to the height of its inner content\n    element.style.height = sectionHeight + 'px';\n    // mark the section as \"currently not collapsed\"\n    element.setAttribute('data-collapsed', 'false');\n  }\n  toggleSection(sectionElement, sectionArrowImgSrc, inherit, note) {\n    let id = sectionElement.getAttribute('id');\n    let setOpen = '';\n    if (!note?.id) {\n      setOpen = sectionElement.getAttribute('open') === 'false' ? 'true' : 'false';\n    } else {\n      setOpen = note?.open;\n    }\n    var section = document.getElementById(id);\n    sectionElement.setAttribute('open', setOpen);\n    if (note?.id) {\n      clearInterval(this.iv);\n    }\n    clearTimeout(this.timeout);\n    if (setOpen === 'true') {\n      this.timeout = setTimeout(() => {\n        if (inherit) {\n          sectionElement.style.overflow = \"inherit\";\n        }\n      }, 300);\n      sectionArrowImgSrc.style.transform = \"rotate(180deg)\";\n      this.expandSection(section);\n      if (note?.id) {\n        this.iv = window.setInterval(() => {\n          if (document.getElementById('notes-section-' + note?.id) && document.getElementById('defaultRTE-2-' + note?.id)) document.getElementById('notes-section-' + note?.id).style.height = document.getElementById('defaultRTE-2-' + note?.id).clientHeight + 50 + \"px\";\n        }, 10);\n      }\n    } else {\n      sectionElement.style.overflow = \"hidden\";\n      sectionArrowImgSrc.style.transform = \"rotate(0deg)\";\n      this.collapseSection(section);\n      if (note?.id) {\n        clearInterval(this.iv);\n      }\n    }\n  }\n  getRandomInt(max) {\n    return Math.floor(Math.random() * max);\n  }\n  refreshComponent(url) {\n    this.router.navigateByUrl('/', {\n      skipLocationChange: true\n    }).then(() => {\n      this.router.navigate([url]);\n    });\n  }\n  gradientScale(newValue, oldValue, element) {\n    if (document.getElementById(element)) {\n      let resize;\n      let minus = 0;\n      let divide = 1;\n      if (element === 'classroom-gradient') {\n        minus = 600;\n        divide = 1.2;\n      }\n      if (element === 'tutorial-gradient') {\n        minus = 1000;\n        divide = 0.6;\n      }\n      if (element === 'guide-gradient') {\n        minus = 1000;\n        divide = 0.4;\n      }\n      if (element === 'blog-gradient') {\n        minus = 1000;\n        divide = 0.8;\n      }\n      if (element === 'top-left-gradient') {\n        minus = 0;\n        divide = 1.5;\n      }\n      if (oldValue < newValue) {\n        resize = document.getElementById('app-content').offsetWidth + document.getElementById('app-content').offsetWidth / divide - window.scrollY + minus;\n      } else if (oldValue > newValue) {\n        resize = document.getElementById('app-content').offsetWidth + document.getElementById('app-content').offsetWidth / divide - window.scrollY + minus;\n      }\n      // if(window.scrollY > minus){\n      document.getElementById(element).style.width = resize + \"px\";\n      document.getElementById(element).style.height = resize + \"px\";\n      // }\n    }\n  }\n  slideElements(showModal, id) {\n    if (showModal) {\n      this.slideInElement('blur_bg');\n      this.slideInElement(id);\n    } else {\n      this.slideOutElement('blur_bg');\n      this.slideOutElement(id);\n    }\n  }\n  slideNativeElements(showModal, el) {\n    if (showModal) {\n      this.slideInElement('blur_bg');\n      this.slideInNativeElement(el);\n    } else {\n      this.slideOutElement('blur_bg');\n      this.slideOutNativeElement(el);\n    }\n  }\n  toIsoString(date) {\n    return new Date(date.toString().split('GMT')[0] + ' UTC').toISOString();\n  }\n  getFileName(str) {\n    var n = str.lastIndexOf('/');\n    var n2 = str.lastIndexOf('.');\n    var result = str.substring(n + 1, n2);\n    return result;\n  }\n  getTeacherFileName(teacherFile) {\n    if (teacherFile && teacherFile.lastIndexOf('\\\\')) {\n      let n = teacherFile.lastIndexOf('\\\\');\n      let result = teacherFile.substring(n + 1);\n      return result;\n    }\n    return teacherFile;\n  }\n  getFileName2(str) {\n    var n = str.lastIndexOf('\\\\');\n    var n2 = str.lastIndexOf('.');\n    var result = str.substring(n + 1, n2);\n    return result;\n  }\n  getDomainFileNamePath(str) {\n    var n = str.lastIndexOf('\\\\');\n    var n2 = str.lastIndexOf('.');\n    var result = str.substring(n + 1, n2);\n    const convertedString = str.replace(/\\\\/g, \"/\");\n    return API_URL + convertedString;\n  }\n  getFileNameAndExtension(filePath) {\n    const fileName = filePath.split('\\\\').pop();\n    return fileName;\n  }\n  getDayFromNumber(day) {\n    if (day == 0) return \"Sunday\";\n    if (day == 1) return \"Monday\";\n    if (day == 2) return \"Tuesday\";\n    if (day == 3) return \"Wednesday\";\n    if (day == 4) return \"Thursday\";\n    if (day == 5) return \"Friday\";\n    if (day == 6) return \"Saturday\";\n    return \"\";\n  }\n  getMonthFromNumber(day) {\n    if (day == 0) return \"January\";\n    if (day == 1) return \"February\";\n    if (day == 2) return \"Martch\";\n    if (day == 3) return \"April\";\n    if (day == 4) return \"May\";\n    if (day == 5) return \"June\";\n    if (day == 6) return \"July\";\n    if (day == 7) return \"August\";\n    if (day == 8) return \"September\";\n    if (day == 9) return \"October\";\n    if (day == 10) return \"November\";\n    if (day == 11) return \"December\";\n    return \"\";\n  }\n  downloadLink(url) {\n    this.http.get(url, {\n      responseType: \"blob\"\n    }).subscribe(blob => {\n      saveAs(blob, this.getTeacherFileName(url));\n    });\n  }\n  getColorByIndex(index) {\n    if (index == 0) {\n      return \"#003f5c\";\n    }\n    if (index == 1) {\n      return \"#2f4b7c\";\n    }\n    if (index == 2) {\n      return \"#665191\";\n    }\n    if (index == 3) {\n      return \"#a05195\";\n    }\n    if (index == 4) {\n      return \"#d45087\";\n    }\n    if (index == 5) {\n      return \"#f95d6a\";\n    }\n    if (index == 6) {\n      return \"#ff7c43\";\n    }\n    if (index == 7) {\n      return \"#ffa600\";\n    }\n    return \"white\";\n  }\n  downLoadfiles(targetUrl) {\n    this.http.get(targetUrl).subscribe(res => {\n      var a = document.createElement(\"a\");\n      a.href = URL.createObjectURL(res.blob());\n      a.download = targetUrl;\n      // start download\n      a.click();\n    });\n  }\n  updateMobileMenuTitle(targetUrl) {\n    this.mobileMenuTitle$.next(targetUrl);\n  }\n  isNullishObject(obj) {\n    if (obj === null) {\n      return true;\n    }\n    const isNullish = Object.values(obj).every(value => {\n      if (value === null) {\n        return true;\n      }\n      return false;\n    });\n    return isNullish;\n  }\n  formatDate(d, separator) {\n    const yyyy = d.getFullYear().toString();\n    const mm = (d.getMonth() + 1).toString();\n    const dd = d.getDate().toString();\n    return yyyy + separator + (mm[1] ? mm : \"0\" + mm[0]) + separator + (dd[1] ? dd : \"0\" + dd[0]);\n  }\n  formatDateToDMY(dateTimeString) {\n    const date = new Date(dateTimeString);\n    const formattedDate = date.toLocaleDateString(\"en-GB\", {\n      day: \"2-digit\",\n      month: \"2-digit\",\n      year: \"numeric\"\n    });\n    return formattedDate;\n  }\n  /**\n   * Converts the given date string to the `dd/mm/yyyy` format\n   * @param {string} dateString - The date string to be converted.\n   * @returns {string} The converted date in `dd/mm/yyyy` format.\n   */\n  convertToDDMMYYYY(dateString) {\n    let date = new Date(dateString);\n    let day = date.getDate().toString().padStart(2, \"0\");\n    let month = (date.getMonth() + 1).toString().padStart(2, \"0\");\n    let year = date.getFullYear();\n    return `${day}/${month}/${year}`;\n  }\n  /**\n  Converts a date string in dd/MM/yyyy format to ISO format with zero time.\n  @param {string} dateString - The date string in dd/MM/yyyy format to convert.\n  @returns {string} The ISO format string with zero time.\n  */\n  convertDateStringToIsoFormatWithZeroTime(dateString) {\n    let [day, month, year] = dateString.split(\"/\");\n    let isoDateString = `${year}-${month.padStart(2, \"0\")}-${day.padStart(2, \"0\")}T00:00:00.000`;\n    return isoDateString;\n  }\n  /**\n   * Converts a given datetime string to ISO date format\n   * @param {string} dateTimeString - The datetime string to be converted\n   * @returns {string} The ISO date string\n   */\n  convertToIsoDate(dateTimeString) {\n    const date = new Date(dateTimeString);\n    const formattedDate = date.toISOString();\n    return this.convertWithoutTimeAndZ(formattedDate);\n  }\n  /**\n  Converts a date string to an ISO string without time and Z\n  @param {string} dateString - The date string to be converted\n  @returns {string} The converted ISO string\n  */\n  convertWithoutTimeAndZ(dateString) {\n    const date = new Date(dateString);\n    date.setUTCHours(0, 0, 0, 0);\n    date.setDate(date.getDate());\n    const isoDateString = date.toISOString().replace('Z', '');\n    return isoDateString;\n  }\n  /**\n   * Converts a date string to a formatted time string.\n   *\n   * @param {string} dateString - The date string to convert.\n   * @returns {string} A formatted time string in 24-hour format (e.g. \"14:30\").\n   */\n  convertToTime(dateString) {\n    const date = new Date(dateString);\n    const options = {\n      hour12: false,\n      hour: 'numeric',\n      minute: 'numeric'\n    };\n    return date.toLocaleTimeString('en-US', options);\n  }\n  /**\n   * Converts a camelCase string into a spaced string.\n   *\n   * @param {string} camelCaseString - The camelCase string to be converted.\n   * @returns {string} A spaced string.\n   */\n  convertCamelCase(camelCaseString) {\n    let spacedString = '';\n    for (let i = 0; i < camelCaseString.length; i++) {\n      const originalChar = camelCaseString[i];\n      const upperCaseChar = camelCaseString[i].toUpperCase();\n      if (originalChar === upperCaseChar) {\n        spacedString += ' ' + originalChar;\n      } else {\n        spacedString += originalChar;\n      }\n    }\n    return spacedString;\n  }\n  /**\n  Check if a form field is valid.\n  @param form - The form to check.\n  @param field - The field to check.\n  @param tryToSave - Whether the form is being submitted.\n  @returns Whether the field is invalid and has been touched, or if the form is being submitted and the field is invalid.\n  */\n  ifFieldValid(form, field, tryToSave) {\n    return form.get(field)?.invalid && form.get(field)?.touched || tryToSave && form.get(field)?.invalid;\n  }\n  getUserLocalTime(timeZoneUTC, isHour12 = true) {\n    if (!timeZoneUTC) {\n      return;\n    }\n    const filteredTimezones = data.timezones.filter(timezone => timezone.text === timeZoneUTC);\n    if (filteredTimezones.length === 0) {\n      return;\n    }\n    const date = new Date();\n    const options = {\n      timeZone: filteredTimezones[0].utc[0],\n      hour: 'numeric',\n      minute: 'numeric',\n      hour12: isHour12 // set to true to use 12-hour format\n    };\n    // Check if the current date is within DST period\n    const isDstActive = () => {\n      const now = new Date();\n      const startDst = new Date(now.getFullYear(), 2, 31 - (5 * now.getFullYear() / 4 + 4) % 7, 1);\n      const endDst = new Date(now.getFullYear(), 9, 31 - (5 * now.getFullYear() / 4 + 1) % 7, 1);\n      return now.getTime() >= startDst.getTime() && now.getTime() < endDst.getTime();\n    };\n    // Adjust the offset if DST is active\n    if (isDstActive()) {\n      date.setHours(date.getHours() + 1);\n    }\n    const localTime = new Intl.DateTimeFormat('en-US', options).format(date);\n    return localTime;\n  }\n  scaleElementDown(element, transformOrigin, zoomValue, divElement) {\n    let zoom = zoomValue;\n    var width = 100;\n    zoom = zoom - 0.1;\n    width = 100 / zoom;\n    if (divElement) {\n      divElement.style.transformOrigin = transformOrigin;\n      divElement.style.transform = \"scale(\" + zoom + \")\";\n      divElement.style.width = width + \"%\";\n      // divElement.style.height = width + \"vh\";\n    } else {\n      element.nativeElement.style.transformOrigin = transformOrigin;\n      element.nativeElement.style.transform = \"scale(\" + zoom + \")\";\n      element.nativeElement.style.width = width + \"%\";\n      element.nativeElement.style.height = width + \"vh\";\n    }\n  }\n  scaleElementReset(element, divElement) {\n    var zoom = 1;\n    var width = 100;\n    zoom = zoom;\n    width = 100 / zoom;\n    if (divElement) {\n      divElement.style.transformOrigin = \"unset\";\n      divElement.style.transform = \"none\";\n      divElement.style.width = width + \"%\";\n    } else {\n      element.nativeElement.style.transformOrigin = \"unset\";\n      element.nativeElement.style.transform = \"none\";\n      element.nativeElement.style.width = width + \"%\";\n    }\n  }\n  /**\n   * Finds the image of the country with the given phone code.\n   *\n   * @param {string} phoneCode The phone code of the country.\n   * @returns {string} The image of the country, or undefined if the country is not found.\n   */\n  findCountryImage(phoneCode) {\n    const countries = this.countries;\n    const phoneCodes = this.getPhoneCodes();\n    const filteredCountries = countries.filter(country => {\n      const countryCode = phoneCodes.find(pc => pc.code === phoneCode)?.iso;\n      return country.code === countryCode;\n    });\n    const countryImage = filteredCountries[0]?.image;\n    return countryImage;\n  }\n  findCountryImageByName(countryName) {\n    const country = this.getCountries().find(c => c.name === countryName);\n    return country ? country.image : '';\n  }\n  getHeaderName(person) {\n    return person.firstName === 'NotGiven' || person.firstName === 'Not Given' ? '' : person.firstName + \" \" + person.lastName.charAt(0) + \".\";\n  }\n  getShortFullName(person) {\n    if (person.lastName === \"\") {\n      return person.firstName;\n    } else {\n      return person.firstName + \" \" + person.lastName.charAt(0) + \".\";\n    }\n  }\n  /**\n   * The function takes a person object and returns their full name by concatenating their first name and\n   * last name.\n   * @param {any} person - The `person` parameter is an object that represents a person. It should have\n   * properties `firstName` and `lastName` which are strings representing the person's first name and\n   * last name respectively.\n   * @returns a string, which is the full name of the person.\n   */\n  getPersonFullName(person) {\n    if (person.firstName === \"\" && person.lastName === \"\") {\n      return \"\";\n    } else if (person.firstName === \"\") {\n      return person.lastName;\n    } else if (person.lastName === \"\") {\n      return person.firstName;\n    } else {\n      return person.firstName + \" \" + person.lastName;\n    }\n  }\n  capitalize(str) {\n    if (!str) return str;\n    return str.charAt(0).toUpperCase() + str.slice(1);\n  }\n  /**\n   * Calculates the age from a given date of birth.\n   * @param dob The date of birth as a string in the format \"YYYY-MM-DDTHH:mm:ss\".\n   * @returns The age in years.\n   */\n  calculateAge(dob) {\n    const dateOfBirth = new Date(dob);\n    const ageMs = Date.now() - dateOfBirth.getTime();\n    const ageDate = new Date(ageMs);\n    const age = Math.abs(ageDate.getUTCFullYear() - 1970);\n    return age;\n  }\n  /**\n   * Sets the default user avatar image.\n   * @param event The event that triggered the function.\n   */\n  setDefaultUserAvatar(event) {\n    const imgElement = event.target;\n    imgElement.src = 'assets/images/default-user-avatar.png';\n  }\n  convertMinutesToHours(minutes) {\n    return minutes / 60;\n  }\n  convertHoursToMinutesWithSuffix(hours, fullSuffix = false) {\n    const decimalPart = hours % 1;\n    const wholeHours = Math.floor(hours);\n    let minutes = wholeHours * 60;\n    if (decimalPart > 0) {\n      minutes += decimalPart * 60;\n    }\n    // Handle special cases where decimalPart is 0.25, 0.50, 0.75, etc.\n    if (decimalPart % 0.25 === 0) {\n      minutes += (decimalPart * 60 - Math.floor(decimalPart * 60)) * 15;\n    }\n    // Return zero hours if minutes is zero.\n    if (minutes === 0) {\n      return '0 hours';\n    } else if (minutes < 60) {\n      // Return minutes with suffix if less than 60.\n      return `${minutes > 0 ? minutes : ''}${fullSuffix && minutes > 0 ? ' Minutes' : 'M'}`;\n    } else {\n      const hours = Math.floor(minutes / 60);\n      const minuteSuffix = fullSuffix ? minutes === 60 ? ' Minute' : ' Mins' : 'M';\n      const hourSuffix = fullSuffix ? hours === 1 ? ' Hour' : ' Hours' : 'H';\n      // Return hours and minutes with suffix.\n      if (hours === 0) {\n        return `${minutes > 0 ? minutes : ''}${fullSuffix && minutes > 0 ? minuteSuffix : ''}`;\n      } else {\n        return `${hours}${hourSuffix}${minutes % 60 > 0 ? ` ${minutes % 60}${fullSuffix ? minuteSuffix : 'M'}` : ''}`;\n      }\n    }\n  }\n  formatSelectedDateTimeForLesson(selectedDay, selectedTimeSlot) {\n    const selectedDateTimeString = `${selectedDay.toDateString()} ${selectedTimeSlot}`;\n    const formattedDate = moment.utc(selectedDateTimeString, 'ddd MMM DD YYYY HH:mm').toISOString();\n    return formattedDate;\n  }\n  getMinutesDifference(start, end) {\n    const format = 'HH:mm A'; // Use 24-hour clock format\n    const startTime = moment(start, format);\n    const endTime = moment(end, format);\n    const diffInMilliseconds = endTime.diff(startTime);\n    const diffInMinutes = Math.round(diffInMilliseconds / 1000 / 60);\n    return diffInMinutes;\n  }\n  formatDateWithoutOffset(dateTimeString) {\n    return moment.utc(dateTimeString).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');\n  }\n  formatLessonStartingDate(dateTimeString) {\n    return moment.parseZone(dateTimeString).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');\n  }\n  /**\n   * The function `navigateToLessonDetails` navigates to the lesson details page based on the classroom\n   * type and user role.\n   * @param {Classroom} classroom - The `classroom` parameter represents the classroom object that\n   * contains information about the classroom, such as its id and type.\n   * @param {UserRole} role - The `role` parameter represents the user role, which can be either\n   * `TEACHER` or something else.\n   */\n  navigateToLessonDetails(classroom, role) {\n    const navigationExtras = {\n      state: {\n        classRoom: classroom\n      }\n    };\n    if (classroom.type === 'Trial') {\n      if (role === UserRole.TEACHER) {\n        this.router.navigate(['/dashboard/classrooms/lessons', classroom.id, 'details'], navigationExtras);\n      } else {\n        // TODO add real lesson\n        this.router.navigate(['dashboard/classrooms/lessons', classroom.id, 'info', 'teacher'], {\n          state: {\n            classRoom: classroom\n          }\n        });\n      }\n    } else {\n      this.router.navigate(['/dashboard/classrooms/lessons', classroom.id, 'details'], navigationExtras);\n    }\n  }\n  /**\n  Returns an array of strings that represents a link to a lesson details page.\n  @param {Classroom} classroom - The classroom object that the lesson belongs to.\n  @param {UserRole} role - The user's role in the classroom.\n  @returns {string[]} An array of strings that represents a link to a lesson details page.\n  */\n  getLessonDetailsLink(classroom, role) {\n    let link = ['/dashboard/classrooms/lessons', classroom.id, 'details'];\n    if (classroom.type.toLowerCase() === ClassroomType.TRIAL.toLowerCase() && role === UserRole.STUDENT) {\n      link = ['/dashboard/classrooms/lessons', classroom.id, 'info', 'teacher'];\n    }\n    return link;\n  }\n  /**\n  Opens a dialog box with the specified component, width, and height.\n  @param {any} dialogService - The dialog service object.\n  @param {any} component - The component to be displayed in the dialog box.\n  @param {any} width - The width of the dialog box.\n  @param {any} [dialogData] - Optional data to be passed to the dialog box component.\n  @param {any} [appendDialogActionsTo] - Optional ID of an HTML element where the dialog box actions should be appended.\n  @param {function} [resultHandler] - Optional function to handle the result of the dialog box.\n  @param {string} [height] - Optional height of the dialog box.\n  */\n  openDialogWithComponent(dialogService, component, width, dialogData, appendDialogActionsTo, resultHandler, height) {\n    this.refs.forEach(ref => ref.close());\n    this.refs = [];\n    let centerX = '50%';\n    let centerY = '50%';\n    if (appendDialogActionsTo) {\n      if (Object.keys(appendDialogActionsTo).length > 0) {\n        const element = document.querySelector('#' + appendDialogActionsTo);\n        const rect = element.getBoundingClientRect();\n        centerX = rect.left + 10 + rect.width / 2 - width / 2 + 'px';\n        centerY = rect.top + window.scrollY + 'px'; // Add window.scrollY to account for scrolling\n      }\n    }\n    if (!width) {\n      width = 290;\n    }\n    const dialogRef = dialogService.open(component, {\n      header: '',\n      width: this.containsOnlyNumbers(width) ? width + 'px' : width,\n      height: height ? height : 'auto',\n      showHeader: false,\n      dismissableMask: true,\n      modal: true,\n      contentStyle: {\n        \"max-width\": \"100%\",\n        \"max-height\": height ? height : \"400px\",\n        \"overflow\": \"auto\",\n        \"border-radius\": \"10px\",\n        \"padding\": \"0px\"\n      },\n      style: appendDialogActionsTo ? {\n        'left': centerX,\n        'top': centerY,\n        'position': `fixed`\n      } : {},\n      baseZIndex: 10000,\n      maskStyleClass: 'transparent-mask',\n      data: {\n        dialogData: dialogData\n      }\n    });\n    this.refs.push(dialogRef);\n    dialogRef.onClose.subscribe(data => {\n      console.log('Dialog closed with data:', data);\n      if (resultHandler) {\n        resultHandler(data);\n      }\n    });\n  }\n  destroyComponent(componentRef) {\n    if (componentRef) {\n      componentRef.destroy();\n    }\n  }\n  navigateToBookingSystem() {\n    this.router.navigateByUrl('/dashboard/calendar/booking-system');\n  }\n  navigateToAiChat() {\n    this.router.navigateByUrl('/dashboard/ai-chat');\n  }\n  isObjectEmpty(obj) {\n    return Object.keys(obj).length === 0;\n  }\n  isDayInThePast(dayNumber, currentDate) {\n    const dateString = currentDate.getFullYear() + '-' + (currentDate.getMonth() + 1) + '-' + dayNumber;\n    const today = new Date();\n    const currentDateFormatted = new Date(dateString);\n    // Set hours, minutes, seconds, and milliseconds to 0\n    today.setHours(0, 0, 0, 0);\n    currentDate.setHours(0, 0, 0, 0);\n    return currentDateFormatted < today;\n  }\n  getPercentScreenWidthInPixels(percentageVal = 0.8) {\n    const screenWidthInPixels = window.innerWidth;\n    const percentage = percentageVal;\n    const result = screenWidthInPixels * percentage;\n    return result;\n  }\n  containsOnlyNumbers(input) {\n    // Regular expression pattern to match only numbers\n    const pattern = /^[0-9]+$/;\n    return pattern.test(input);\n  }\n  // Method to navigate to the lesson details page\n  navigateToBuyPackage(classroom) {\n    const navigationExtras = {};\n    if (classroom) {\n      navigationExtras.queryParams = {\n        'classroom': JSON.stringify(classroom)\n      };\n    }\n    this.router.navigate(['dashboard/buy-package'], navigationExtras);\n  }\n  openExternalLink(url) {\n    window.open(url, \"_blank\");\n  }\n  replaceImportedWithDash(val) {\n    if (!val) {\n      return '';\n    }\n    if (val === \"Imported\" || val.includes('Not Given')) {\n      return \"-\";\n    }\n    return val;\n  }\n  replaceNotGivenWith(val, replaceStr) {\n    if (!val) {\n      return '';\n    }\n    if (val === \"Imported\" || val.includes('Not Given')) {\n      return replaceStr ? replaceStr : \"-\";\n    }\n    return val;\n  }\n  getSafeSkypeUrl(url) {\n    const skypeUrl = `skype:` + url;\n    return this.sanitizer.bypassSecurityTrustUrl(skypeUrl);\n  }\n  sortByStartingDate(a, b) {\n    const startDateA = moment(a.startingDate);\n    const startDateB = moment(b.startingDate);\n    if (startDateA.isBefore(startDateB, 'minute')) {\n      return -1;\n    } else if (startDateA.isAfter(startDateB, 'minute')) {\n      return 1;\n    } else {\n      return 0;\n    }\n  }\n  sortLessonsByDate(a, b) {\n    const dateA = moment(a.startingDate);\n    const dateB = moment(b.startingDate);\n    return dateB.diff(dateA);\n  }\n  extractCountryPrefix(phoneNumber) {\n    if (!phoneNumber) {\n      return '';\n    }\n    const parts = phoneNumber.split(' ');\n    if (parts.length > 0) {\n      return parts[0]; // remove the \"+\" prefix\n    }\n    return '';\n  }\n  getDefaultPhoneCode() {\n    const ukPhoneCode = this.getPhoneCodes().find(el => el.iso === 'GB');\n    return ukPhoneCode || this.getPhoneCodes()[0] || '';\n  }\n  extractPhoneNumber(phoneNumber) {\n    if (!phoneNumber) {\n      return '';\n    }\n    const parts = phoneNumber.split(' ');\n    if (parts.length > 1) {\n      return parts[1]; // remove the \"+\" prefix\n    }\n    return '';\n  }\n  getLessonTime(lessonDate, timeFormat = 'HH:mm') {\n    let lessonTime;\n    let lessonDateString = typeof lessonDate === 'string' ? lessonDate : moment(lessonDate).toISOString();\n    if (lessonDateString.endsWith('Z')) {\n      lessonTime = moment.utc(lessonDateString).format(timeFormat);\n    } else {\n      lessonTime = moment(lessonDateString).format(timeFormat);\n    }\n    return lessonTime;\n  }\n  sortLessonsByRecentFirst(a, b) {\n    const dateB = moment(b.startingDate);\n    const dateA = moment(a.startingDate);\n    return dateB.diff(dateA);\n  }\n  isWithinTimeRange(dateString, timeRange) {\n    const now = moment();\n    const date = moment(dateString);\n    const minutesDifference = Math.abs(now.diff(date, 'minutes'));\n    const hoursDifference = minutesDifference / 60;\n    console.log(hoursDifference);\n    // check if the date is in the past\n    const isPast = now.isAfter(date);\n    console.log(isPast);\n    // return true only if the date is NOT in the past and the difference in hours is within the time range\n    return !isPast && hoursDifference >= timeRange;\n  }\n  goToGoogleMeetLink(lesson) {\n    if (lesson.googleMeetURL) {\n      const urlPattern = /^(http|https):\\/\\/[^ \"]+$/;\n      if (urlPattern.test(lesson.googleMeetURL)) {\n        const sanitizedURL = encodeURI(lesson.googleMeetURL);\n        window.open(sanitizedURL, \"_blank\");\n      } else {\n        console.error(\"Invalid Google Meet URL for the lesson.\");\n      }\n    } else {\n      console.error(\"Google Meet URL is missing for the lesson.\");\n    }\n  }\n  goToBellbirdLink(lesson) {\n    if (lesson.bellBirdMeetingURL) {\n      const urlPattern = /^(http|https):\\/\\/[^ \"]+$/;\n      if (urlPattern.test(lesson.bellBirdMeetingURL)) {\n        const sanitizedURL = encodeURI(lesson.bellBirdMeetingURL);\n        window.open(sanitizedURL, \"_blank\");\n      } else {\n        console.error(\"Invalid Google Meet URL for the lesson.\");\n      }\n    } else {\n      console.error(\"Google Meet URL is missing for the lesson.\");\n    }\n  }\n  goToJitsiMeetLink(lesson) {\n    if (lesson) {\n      this.router.navigate(['/meet'], {\n        queryParams: {\n          id: lesson.id,\n          classroomId: lesson.classroomId\n        }\n      });\n    } else {\n      console.error(\"Google Meet URL is missing for the lesson.\");\n    }\n  }\n  convertTimezoneValueToText(timezone) {\n    return data.timezones.find(el => el.utc.includes(timezone));\n  }\n  isWithinMinutes(minutes = 15, time, dateTime) {\n    const currentTime = moment();\n    const targetDateTime = moment(dateTime);\n    // Parse the target time\n    const [hours, minutesOfDay] = time.split(':').map(Number);\n    const targetTime = moment().hours(hours).minutes(minutesOfDay);\n    // Check if the target date is today\n    if (!targetDateTime.isSame(currentTime, 'day')) {\n      return false;\n    }\n    // Calculate the time difference in minutes\n    const timeDifference = targetTime.diff(currentTime, 'minutes');\n    // Check if the time difference is within the specified minutes\n    return Math.abs(timeDifference) <= minutes;\n  }\n  getCurrentTime(timezone) {\n    // Get the offset value from the JSON data\n    const timezoneData = data.timezones.find(tz => tz.utc.includes(timezone));\n    if (!timezoneData) {\n      throw new Error('Timezone not found in data.');\n    }\n    const offset = timezoneData.offset;\n    // Create a moment object with the current timestamp\n    const currentMoment = moment();\n    // Apply the offset to the moment object\n    const convertedMoment = currentMoment.utcOffset(offset);\n    // Format the converted moment object to display the current time\n    const currentTime = convertedMoment.format('HH:mm');\n    return currentTime;\n  }\n  getCurrentTimeInTimezone(timezone) {\n    const currentTime = moment(); // Use the current time\n    const date = moment.tz(currentTime, timezone);\n    return date.format('YYYY-MM-DDTHH:mm:ss');\n  }\n  convertTimeToHourOfDayFormat(time, timezone) {\n    const date = moment.tz(moment(), timezone);\n    return date.format('hh:mm A');\n  }\n  getTimeDifferenceInMinutes(date1, date2) {\n    const userTimezone = this.authService.getLoggedInUser().timeZone;\n    const momentDate1 = moment.tz(date1, userTimezone); // Assuming date1 is in UTC timezone\n    const momentDate2 = moment.tz(date2, userTimezone); // Assuming date2 is in UTC timezone\n    const differenceInMinutes = momentDate1.diff(momentDate2, 'minutes');\n    return differenceInMinutes;\n  }\n  isDifferenceWithin30Minutes(date1, date2, lesson) {\n    const endTime = moment(lesson.startingDate).add(lesson.duration, 'hours');\n    const differenceInMinutes = this.getTimeDifferenceInMinutes(date1, date2);\n    const lessonDurationInMinutes = lesson.duration * 60;\n    // If date2 is before date1 or endTime, consider the difference as 30 minutes\n    if (moment(date2).isBefore(date1) || moment(date2).isBefore(endTime)) {\n      return Math.abs(differenceInMinutes) <= lessonDurationInMinutes;\n    }\n    // Calculate the maximum allowable time difference including the lesson duration and 30 minutes\n    const maxAllowedDifference = lesson.duration * 60 + 30; // Convert lesson duration to minutes\n    // Check if the absolute difference between date1 and date2 is within the allowable range\n    return Math.abs(differenceInMinutes) <= maxAllowedDifference;\n  }\n  isWithin30Mins(lesson, user) {\n    const userTimeZone = this.convertTimezoneValueToText(user.timeZone).text;\n    const userLocalTime = this.getUserLocalTime(this.convertTimezoneValueToText(user.timeZone).text, false);\n    const currentTimeInUserTimezone = this.getCurrentTime(user.timeZone);\n    const lessonStartingDateInUserTimezone = lesson.startingDate.toString();\n    // console.log(this.showTimeDifference(userTimeZone, lessonStartingDateInUserTimezone));\n    return this.isWithinMinutes(30, currentTimeInUserTimezone, lessonStartingDateInUserTimezone);\n  }\n  showTimeDifference(startTime, endTime) {\n    const startMoment = moment(startTime, 'HH:mm');\n    const endMoment = moment(endTime, 'HH:mm');\n    const duration = moment.duration(endMoment.diff(startMoment));\n    const hours = Math.floor(duration.asHours());\n    const minutes = Math.floor(duration.asMinutes()) % 60;\n    return `${hours} hours and ${minutes} minutes`;\n  }\n  getCountryCode() {\n    return this.http.get(IP_COUNTRY_API).pipe(map(response => {\n      if (response) {\n        return response;\n      } else {\n        throw new Error('Country code not found in response');\n      }\n    }));\n  }\n  static #_ = this.ɵfac = function GeneralService_Factory(t) {\n    return new (t || GeneralService)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(i2.HttpClient), i0.ɵɵinject(i3.DomSanitizer));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: GeneralService,\n    factory: GeneralService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["inject", "BehaviorSubject", "map", "ClassroomType", "Level", "Dimentions", "PackageDurationHours", "saveAs", "data", "moment", "environment", "UserRole", "AuthService", "API_URL", "apiUrl", "IP_COUNTRY_API", "GeneralService", "constructor", "router", "http", "sanitizer", "authService", "deviceKind$", "w576down", "is576", "is768", "is992", "is1024", "is1366", "isBig", "mobileMenuTitle$", "deviceKind", "asObservable", "mobileMenuTitle", "socialLinks", "linkedin", "twitter", "facebook", "youtube", "instagram", "tiktok", "mltLanguages", "name", "code", "mltLevels", "A1", "A2", "B1", "B2", "C1", "C2", "NT21", "NT22", "TBD", "BS", "languagesToLearn", "title", "link", "socialMedia", "icon", "languageLevels", "levelName", "showOnlyOnCode", "visible", "packageDurationHours", "countries", "timezones", "languages", "refs", "getLanguagesToLearn", "getSocialMedia", "getCountries", "getTimezones", "getPhoneCodes", "phoneCodes", "getLanguageLevels", "setDevice", "window", "innerWidth", "w576", "w576up", "w768up", "w768", "w992up", "w992", "w1024up", "w1024", "w1366up", "w1366", "w1500up", "w1500", "next", "slideInElement", "id", "document", "getElementById", "classList", "remove", "add", "body", "style", "overflowY", "slideOutElement", "slideInNativeElement", "el", "slideOutNativeElement", "getDatesDiff", "date1", "date2", "diff", "getTime", "months", "weeks", "days", "hours", "minutes", "Math", "abs", "round", "hideClassElements", "classElements", "for<PERSON>ach", "call", "display", "showClassElements", "collapseSection", "element", "sectionHeight", "scrollHeight", "elementTransition", "transition", "requestAnimationFrame", "height", "setAttribute", "expandSection", "toggleSection", "sectionElement", "sectionArrowImgSrc", "inherit", "note", "getAttribute", "<PERSON><PERSON><PERSON>", "open", "section", "clearInterval", "iv", "clearTimeout", "timeout", "setTimeout", "overflow", "transform", "setInterval", "clientHeight", "getRandomInt", "max", "floor", "random", "refreshComponent", "url", "navigateByUrl", "skipLocationChange", "then", "navigate", "gradientScale", "newValue", "oldValue", "resize", "minus", "divide", "offsetWidth", "scrollY", "width", "slideElements", "showModal", "slideNativeElements", "toIsoString", "date", "Date", "toString", "split", "toISOString", "getFileName", "str", "n", "lastIndexOf", "n2", "result", "substring", "getTeacherFileName", "teacherFile", "getFileName2", "getDomainFileNamePath", "convertedString", "replace", "getFileNameAndExtension", "filePath", "fileName", "pop", "getDayFromNumber", "day", "getMonthFromNumber", "downloadLink", "get", "responseType", "subscribe", "blob", "getColorByIndex", "index", "downLoadfiles", "targetUrl", "res", "a", "createElement", "href", "URL", "createObjectURL", "download", "click", "updateMobileMenuTitle", "isNullishObject", "obj", "<PERSON><PERSON><PERSON><PERSON>", "Object", "values", "every", "value", "formatDate", "d", "separator", "yyyy", "getFullYear", "mm", "getMonth", "dd", "getDate", "formatDateToDMY", "dateTimeString", "formattedDate", "toLocaleDateString", "month", "year", "convertToDDMMYYYY", "dateString", "padStart", "convertDateStringToIsoFormatWithZeroTime", "isoDateString", "convertToIsoDate", "convertWithoutTimeAndZ", "setUTCHours", "setDate", "convertToTime", "options", "hour12", "hour", "minute", "toLocaleTimeString", "convertCamelCase", "camelCaseString", "spacedString", "i", "length", "originalChar", "upperCaseChar", "toUpperCase", "ifFieldValid", "form", "field", "tryToSave", "invalid", "touched", "getUserLocalTime", "timeZoneUTC", "isHour12", "filteredTimezones", "filter", "timezone", "text", "timeZone", "utc", "isDstActive", "now", "startDst", "endDst", "setHours", "getHours", "localTime", "Intl", "DateTimeFormat", "format", "scaleElementDown", "transform<PERSON><PERSON>in", "zoomValue", "divElement", "zoom", "nativeElement", "scaleElementReset", "findCountryImage", "phoneCode", "filteredCountries", "country", "countryCode", "find", "pc", "iso", "countryImage", "image", "findCountryImageByName", "countryName", "c", "getHeaderName", "person", "firstName", "lastName", "char<PERSON>t", "getShortFullName", "getPersonFullName", "capitalize", "slice", "calculateAge", "dob", "dateOfBirth", "ageMs", "ageDate", "age", "getUTCFullYear", "setDefaultUserAvatar", "event", "imgElement", "target", "src", "convertMinutesToHours", "convertHoursToMinutesWithSuffix", "fullSuffix", "decimalPart", "wholeHours", "minuteSuffix", "hourSuffix", "formatSelectedDateTimeForLesson", "selected<PERSON>ay", "selectedTimeSlot", "selectedDateTimeString", "toDateString", "getMinutesDifference", "start", "end", "startTime", "endTime", "diffInMilliseconds", "diffInMinutes", "formatDateWithoutOffset", "formatLessonStartingDate", "parseZone", "navigateToLessonDetails", "classroom", "role", "navigationExtras", "state", "classRoom", "type", "TEACHER", "getLessonDetailsLink", "toLowerCase", "TRIAL", "STUDENT", "openDialogWithComponent", "dialogService", "component", "dialogData", "appendDialogActionsTo", "<PERSON><PERSON><PERSON><PERSON>", "ref", "close", "centerX", "centerY", "keys", "querySelector", "rect", "getBoundingClientRect", "left", "top", "dialogRef", "header", "containsOnlyNumbers", "showHeader", "dismissableMask", "modal", "contentStyle", "baseZIndex", "maskStyleClass", "push", "onClose", "console", "log", "destroyComponent", "componentRef", "destroy", "navigateToBookingSystem", "navigateToAiChat", "isObjectEmpty", "isDayInThePast", "dayNumber", "currentDate", "today", "currentDateFormatted", "getPercentScreenWidthInPixels", "percentageVal", "screenWidthInPixels", "percentage", "input", "pattern", "test", "navigateToBuyPackage", "queryParams", "JSON", "stringify", "openExternalLink", "replaceImportedWithDash", "val", "includes", "replaceNotGivenWith", "replaceStr", "getSafeSkypeUrl", "skypeUrl", "bypassSecurityTrustUrl", "sortByStartingDate", "b", "startDateA", "startingDate", "startDateB", "isBefore", "isAfter", "sortLessonsByDate", "dateA", "dateB", "extractCountryPrefix", "phoneNumber", "parts", "getDefaultPhoneCode", "ukPhoneCode", "extractPhoneNumber", "getLessonTime", "lessonDate", "timeFormat", "lessonTime", "lessonDateString", "endsWith", "sortLessonsByRecentFirst", "isWithinTimeRange", "timeRange", "minutesDifference", "hoursDifference", "isPast", "goToGoogleMeetLink", "lesson", "googleMeetURL", "urlPattern", "sanitizedURL", "encodeURI", "error", "goToBellbirdLink", "bellBirdMeetingURL", "goToJitsiMeetLink", "classroomId", "convertTimezoneValueToText", "isWithinMinutes", "time", "dateTime", "currentTime", "targetDateTime", "minutesOfDay", "Number", "targetTime", "isSame", "timeDifference", "getCurrentTime", "timezoneData", "tz", "Error", "offset", "currentMoment", "convertedMoment", "utcOffset", "getCurrentTimeInTimezone", "convertTimeToHourOfDayFormat", "getTimeDifferenceInMinutes", "userTimezone", "getLoggedInUser", "momentDate1", "momentDate2", "differenceInMinutes", "isDifferenceWithin30Minutes", "duration", "lessonDurationInMinutes", "maxAllowedDifference", "isWithin30Mins", "user", "userTimeZone", "userLocalTime", "currentTimeInUserTimezone", "lessonStartingDateInUserTimezone", "showTimeDifference", "startMoment", "endMoment", "asHours", "asMinutes", "getCountryCode", "pipe", "response", "_", "i0", "ɵɵinject", "i1", "Router", "i2", "HttpClient", "i3", "Dom<PERSON><PERSON><PERSON>zer", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\services\\general.service.ts"], "sourcesContent": ["import { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { ElementRef, Injectable, inject } from '@angular/core';\r\nimport { NavigationExtras, Router } from '@angular/router';\r\nimport { BehaviorSubject, from, map, Observable, of } from 'rxjs';\r\nimport { Classroom, ClassroomType, Level } from '../models/classroom.model';\r\nimport { Country, DeviceKind, Dimentions, Language, LanguageToLearn, SocialMedia, Timezone, ToastMessage } from '../models/general.model';\r\nimport { PackageDurationHours, PackagePriceHourly, PackageToBuy, PackageType } from '../models/package.model';\r\nimport { saveAs } from 'file-saver';\r\nimport * as data from '../models/data';\r\nimport * as moment from 'moment-timezone';\r\nimport { UntypedFormGroup } from '@angular/forms';\r\nimport { environment } from 'src/environments/environment';\r\nimport { User, UserRole } from '../models/user.model';\r\nimport { DomSanitizer, SafeUrl } from '@angular/platform-browser';\r\nimport { Lesson } from '../models/lesson.model';\r\nimport { AuthService } from './auth.service';\r\nconst API_URL = environment.apiUrl + \"/\";\r\nconst IP_COUNTRY_API = \"https://ipapi.co/json/\";\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class GeneralService {\r\n  authService = inject(AuthService);\r\n  private deviceKind$: BehaviorSubject<DeviceKind> = new BehaviorSubject<DeviceKind>({ w576down: false, is576: false, is768: false, is992: false, is1024: false, is1366: false, isBig: true });\r\n  private mobileMenuTitle$: BehaviorSubject<string> = new BehaviorSubject<string>('test');\r\n\r\n  public readonly deviceKind: Observable<DeviceKind> = this.deviceKind$.asObservable();\r\n  public readonly mobileMenuTitle: Observable<string> = this.mobileMenuTitle$.asObservable();\r\n\r\n  socialLinks =\r\n    {\r\n      linkedin: 'https://gr.linkedin.com/company/mylingotrip',\r\n      twitter: 'https://twitter.com/MyLingoTrip',\r\n      facebook: 'https://www.facebook.com/mylingotrip',\r\n      youtube: 'https://www.youtube.com/c/MyLingoTrip',\r\n      instagram: 'https://www.instagram.com/mylingotrip/',\r\n      tiktok: 'https://www.tiktok.com/@mylingotrip'\r\n    };\r\n\r\n  public readonly mltLanguages: Language[] = [\r\n    { name: \"English\", code: \"ENG\" },\r\n    { name: \"French\", code: \"FR\" },\r\n    { name: \"German\", code: \"GER\" },\r\n    { name: \"Italian\", code: \"IT\" },\r\n    { name: \"Spanish\", code: \"SP\" },\r\n    { name: \"Greek\", code: \"GRE\" },\r\n    { name: \"Swedish\", code: \"SW\" },\r\n    { name: \"Dutch\", code: \"DUT\" },\r\n    { name: \"Russian\", code: \"RUS\" },\r\n    { name: \"Japanese\", code: \"JPN\" },\r\n    { name: \"Korean\", code: \"KOR\" },\r\n    { name: \"Chinese\", code: \"CHI\" },\r\n  ]\r\n\r\n  public readonly mltLevels = [\r\n    // 'Any',\r\n    Level.A1,\r\n    Level.A2,\r\n    Level.B1,\r\n    Level.B2,\r\n    Level.C1,\r\n    Level.C2,\r\n    Level.NT21,\r\n    Level.NT22,\r\n    Level.TBD,\r\n    Level.BS\r\n  ];\r\n\r\n  public readonly languagesToLearn: LanguageToLearn[] = [\r\n    {\r\n      title: \"English\",\r\n      link: \"\"\r\n    },\r\n    {\r\n      title: \"Frensh\",\r\n      link: \"\"\r\n    },\r\n    {\r\n      title: \"German\",\r\n      link: \"\"\r\n    },\r\n    {\r\n      title: \"Italian\",\r\n      link: \"\"\r\n    },\r\n    {\r\n      title: \"Spanish\",\r\n      link: \"\"\r\n    },\r\n    {\r\n      title: \"Greek\",\r\n      link: \"\"\r\n    },\r\n    {\r\n      title: \"Swedish\",\r\n      link: \"\"\r\n    },\r\n    {\r\n      title: \"Dutch\",\r\n      link: \"\"\r\n    },\r\n    {\r\n      title: \"Russian\",\r\n      link: \"\"\r\n    },\r\n    {\r\n      title: \"Japanese\",\r\n      link: \"\"\r\n    },\r\n    {\r\n      title: \"Korean\",\r\n      link: \"\"\r\n    },\r\n    {\r\n      title: \"Chinese\",\r\n      link: \"\"\r\n    }\r\n  ]\r\n\r\n  public readonly socialMedia: SocialMedia[] = [\r\n    {\r\n      icon: \"facebook.svg\",\r\n      link: \"\"\r\n    },\r\n    {\r\n      icon: \"linkedin.svg\",\r\n      link: \"\"\r\n    },\r\n    {\r\n      icon: \"youtube.svg\",\r\n      link: \"\"\r\n    },\r\n    {\r\n      icon: \"instagram.svg\",\r\n      link: \"\"\r\n    },\r\n    {\r\n      icon: \"tweeter.svg\",\r\n      link: \"\"\r\n    },\r\n  ]\r\n\r\n  public readonly languageLevels: any[] = [\r\n    {\r\n      levelName: \"A1\",\r\n      showOnlyOnCode: [],\r\n      visible: true,\r\n    },\r\n    {\r\n      levelName: \"A2\",\r\n      showOnlyOnCode: [],\r\n      visible: true,\r\n    },\r\n    {\r\n      levelName: \"B1\",\r\n      showOnlyOnCode: [],\r\n      visible: true,\r\n    },\r\n    {\r\n      levelName: \"B2\",\r\n      showOnlyOnCode: [],\r\n      visible: true,\r\n    },\r\n    {\r\n      levelName: \"C1\",\r\n      showOnlyOnCode: [],\r\n      visible: true,\r\n    },\r\n    {\r\n      levelName: \"C2\",\r\n      showOnlyOnCode: [],\r\n      visible: true,\r\n    },\r\n    {\r\n      levelName: \"NT2.1\",\r\n      showOnlyOnCode: ['nl'],\r\n      visible: false,\r\n    },\r\n    {\r\n      levelName: \"NT2.2\",\r\n      showOnlyOnCode: ['nl'],\r\n      visible: false,\r\n    }];\r\n\r\n  public readonly packageDurationHours = PackageDurationHours;\r\n\r\n  public readonly countries = data.countries\r\n  public readonly timezones = data.timezones\r\n  public readonly languages = data.languages\r\n  public mltPackages: any;\r\n  private refs: any[] = [];\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private http: HttpClient,\r\n    private sanitizer: DomSanitizer,\r\n  ) { }\r\n\r\n  getLanguagesToLearn(): LanguageToLearn[] {\r\n    return this.languagesToLearn;\r\n  }\r\n\r\n  getSocialMedia(): SocialMedia[] {\r\n    return this.socialMedia;\r\n  }\r\n\r\n  getCountries(): Country[] {\r\n    return data.countries;\r\n  }\r\n\r\n  getTimezones(): Timezone[] {\r\n    return data.timezones;\r\n  }\r\n\r\n  getPhoneCodes(): any[] {\r\n    return data.phoneCodes;\r\n  }\r\n\r\n  getLanguageLevels(): any[] {\r\n    return this.languageLevels;\r\n  }\r\n\r\n  public setDevice() {\r\n    var w576down = window.innerWidth < Dimentions.w576;\r\n    var w576up = window.innerWidth >= Dimentions.w576;\r\n    var w768up = window.innerWidth >= Dimentions.w768;\r\n    var w992up = window.innerWidth >= Dimentions.w992;\r\n    var w1024up = window.innerWidth >= Dimentions.w1024;\r\n    var w1366up = window.innerWidth >= Dimentions.w1366;\r\n    var w1500up = window.innerWidth >= Dimentions.w1500;\r\n\r\n    var w576 = window.innerWidth <= Dimentions.w576;\r\n    var w768 = window.innerWidth <= Dimentions.w768;\r\n    var w992 = window.innerWidth <= Dimentions.w992;\r\n    var w1024 = window.innerWidth <= Dimentions.w1024;\r\n    var w1366 = window.innerWidth <= Dimentions.w1366;\r\n    var w1500 = window.innerWidth <= Dimentions.w1500;\r\n    var isBig = window.innerWidth > Dimentions.w1366;\r\n    this.deviceKind$.next({\r\n      w576down: w576down,\r\n      is576: w576,\r\n      is768: w768,\r\n      is992: w992,\r\n      is1024: w1024,\r\n      is1366: w1366,\r\n      w576up: w576up,\r\n      w768up: w768up,\r\n      w992up: w992up,\r\n      w1024up: w1024up,\r\n      w1366up: w1366up,\r\n      w1500up: w1500up,\r\n      isBig: isBig\r\n    });\r\n  }\r\n\r\n  public slideInElement(id: string) {\r\n    document.getElementById(id)?.classList.remove('no-visibility');\r\n    document.getElementById(id)?.classList.add('yes-visibility');\r\n    document.body.style.overflowY = 'hidden';\r\n  }\r\n\r\n  public slideOutElement(id: string) {\r\n    document.getElementById(id)?.classList.remove('yes-visibility');\r\n    document.getElementById(id)?.classList.add('no-visibility');\r\n    document.body.style.overflowY = 'scroll';\r\n  }\r\n\r\n  public slideInNativeElement(el: any) {\r\n    el.classList.remove('no-visibility');\r\n    el.classList.add('yes-visibility');\r\n    document.body.style.overflowY = 'hidden';\r\n  }\r\n\r\n  public slideOutNativeElement(el: any) {\r\n    el.classList.remove('yes-visibility');\r\n    el.classList.add('no-visibility');\r\n    document.body.style.overflowY = 'scroll';\r\n  }\r\n\r\n  getDatesDiff(date1: Date, date2: Date) {\r\n    var diff = (date1.getTime() - date2.getTime()) / 1000;\r\n    var months = diff / (60 * 60 * 24 * 7);\r\n    var weeks = diff / (60 * 60 * 24 * 7);\r\n    var days = diff / (60 * 60 * 24);\r\n    var hours = diff / (60 * 60);\r\n    var minutes = diff / (60);\r\n    return {\r\n      weeks: Math.abs(Math.round(weeks)),\r\n      days: Math.abs(Math.round(days)),\r\n      hours: Math.abs(Math.round(hours)),\r\n      minutes: Math.abs(Math.round(minutes)),\r\n    }\r\n  }\r\n\r\n  public hideClassElements(classElements: HTMLCollectionOf<Element>) {\r\n    [].forEach.call(classElements, (el: any) => {\r\n      el.style.display = 'none';\r\n    });\r\n  }\r\n\r\n  public showClassElements(classElements: HTMLCollectionOf<Element>, display: string) {\r\n    [].forEach.call(classElements, (el: any) => {\r\n      el.style.display = display;\r\n    });\r\n  }\r\n\r\n  public collapseSection(element: any) {\r\n    // get the height of the element's inner content, regardless of its actual size\r\n    var sectionHeight = element.scrollHeight;\r\n\r\n    // temporarily disable all css transitions\r\n    var elementTransition = element.style.transition;\r\n    element.style.transition = '';\r\n\r\n    // on the next frame (as soon as the previous style change has taken effect),\r\n    // explicitly set the element's height to its current pixel height, so we \r\n    // aren't transitioning out of 'auto'\r\n    requestAnimationFrame(function () {\r\n      element.style.height = sectionHeight + 'px';\r\n      element.style.transition = elementTransition;\r\n\r\n      // on the next frame (as soon as the previous style change has taken effect),\r\n      // have the element transition to height: 0\r\n      requestAnimationFrame(function () {\r\n        element.style.height = 0 + 'px';\r\n      });\r\n    });\r\n\r\n    // mark the section as \"currently collapsed\"\r\n    element.setAttribute('data-collapsed', 'true');\r\n  }\r\n\r\n  public expandSection(element: any) {\r\n    // get the height of the element's inner content, regardless of its actual size\r\n    var sectionHeight = element.scrollHeight;\r\n\r\n    // have the element transition to the height of its inner content\r\n    element.style.height = sectionHeight + 'px';\r\n\r\n    // mark the section as \"currently not collapsed\"\r\n    element.setAttribute('data-collapsed', 'false');\r\n  }\r\n  iv: any\r\n  timeout: any;\r\n  toggleSection(sectionElement: any, sectionArrowImgSrc: any, inherit: boolean, note?: { id: string, open: string }) {\r\n    let id = sectionElement.getAttribute('id');\r\n    let setOpen = ''\r\n    if (!note?.id) {\r\n      setOpen = sectionElement.getAttribute('open') === 'false' ? 'true' : 'false'\r\n    } else {\r\n      setOpen = note?.open\r\n    }\r\n    var section = document.getElementById(id);\r\n    sectionElement.setAttribute('open', setOpen);\r\n    if (note?.id) {\r\n      clearInterval(this.iv)\r\n    }\r\n    clearTimeout(this.timeout);\r\n\r\n    if (setOpen === 'true') {\r\n      this.timeout = setTimeout(() => {\r\n        if (inherit) {\r\n          sectionElement.style.overflow = \"inherit\"\r\n        }\r\n      }, 300);\r\n      sectionArrowImgSrc.style.transform = \"rotate(180deg)\"\r\n      this.expandSection(section)\r\n      if (note?.id) {\r\n        this.iv = window.setInterval(() => { // this shit is to adjust the height of rte..\r\n          if (document.getElementById('notes-section-' + note?.id) && document.getElementById('defaultRTE-2-' + note?.id))\r\n            document.getElementById('notes-section-' + note?.id)!.style.height = document.getElementById('defaultRTE-2-' + note?.id)!.clientHeight + 50 + \"px\";\r\n        }, 10)\r\n      }\r\n    } else {\r\n      sectionElement.style.overflow = \"hidden\"\r\n      sectionArrowImgSrc.style.transform = \"rotate(0deg)\"\r\n      this.collapseSection(section);\r\n      if (note?.id) {\r\n        clearInterval(this.iv)\r\n      }\r\n\r\n    }\r\n  }\r\n\r\n  getRandomInt(max: number) {\r\n    return Math.floor(Math.random() * max);\r\n  }\r\n\r\n  refreshComponent(url: string) {\r\n    this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {\r\n      this.router.navigate([url]);\r\n    });\r\n  }\r\n\r\n  gradientScale(newValue: number, oldValue: number, element: string) {\r\n    if (document.getElementById(element)) {\r\n      let resize;\r\n      let minus = 0;\r\n      let divide = 1;\r\n      if (element === 'classroom-gradient') {\r\n        minus = 600;\r\n        divide = 1.2;\r\n      }\r\n      if (element === 'tutorial-gradient') {\r\n        minus = 1000;\r\n        divide = 0.6;\r\n      }\r\n      if (element === 'guide-gradient') {\r\n        minus = 1000;\r\n        divide = 0.4;\r\n      }\r\n      if (element === 'blog-gradient') {\r\n        minus = 1000;\r\n        divide = 0.8\r\n      }\r\n      if (element === 'top-left-gradient') {\r\n        minus = 0;\r\n        divide = 1.5;\r\n      }\r\n      if (oldValue < newValue) {\r\n        resize = document.getElementById('app-content')!.offsetWidth + document.getElementById('app-content')!.offsetWidth / divide - window.scrollY + minus;\r\n      } else if (oldValue > newValue) {\r\n        resize = document.getElementById('app-content')!.offsetWidth + document.getElementById('app-content')!.offsetWidth / divide - window.scrollY + minus;\r\n      }\r\n      // if(window.scrollY > minus){\r\n      document.getElementById(element)!.style.width = resize + \"px\";\r\n      document.getElementById(element)!.style.height = resize + \"px\";\r\n      // }\r\n    }\r\n  }\r\n\r\n  slideElements(showModal: boolean, id: string) {\r\n    if (showModal) {\r\n      this.slideInElement('blur_bg');\r\n      this.slideInElement(id);\r\n    } else {\r\n      this.slideOutElement('blur_bg');\r\n      this.slideOutElement(id);\r\n    }\r\n  }\r\n\r\n  slideNativeElements(showModal: boolean, el: string) {\r\n    if (showModal) {\r\n      this.slideInElement('blur_bg');\r\n      this.slideInNativeElement(el);\r\n    } else {\r\n      this.slideOutElement('blur_bg');\r\n      this.slideOutNativeElement(el);\r\n    }\r\n  }\r\n\r\n  toIsoString(date: Date) {\r\n    return new Date(date.toString().split('GMT')[0] + ' UTC').toISOString()\r\n  }\r\n\r\n  getFileName(str: string) {\r\n    var n = str.lastIndexOf('/');\r\n    var n2 = str.lastIndexOf('.');\r\n    var result = str.substring(n + 1, n2);\r\n    return result;\r\n  }\r\n\r\n  getTeacherFileName(teacherFile: string) {\r\n    if (teacherFile && teacherFile.lastIndexOf('\\\\')) {\r\n      let n = teacherFile.lastIndexOf('\\\\');\r\n      let result = teacherFile.substring(n + 1);\r\n      return result;\r\n    }\r\n    return teacherFile\r\n  }\r\n\r\n  getFileName2(str: string) {\r\n    var n = str.lastIndexOf('\\\\');\r\n    var n2 = str.lastIndexOf('.');\r\n    var result = str.substring(n + 1, n2);\r\n    return result;\r\n  }\r\n\r\n  getDomainFileNamePath(str: string) {\r\n    var n = str.lastIndexOf('\\\\');\r\n    var n2 = str.lastIndexOf('.');\r\n    var result = str.substring(n + 1, n2);\r\n    const convertedString = str.replace(/\\\\/g, \"/\");\r\n    return API_URL + convertedString;\r\n  }\r\n\r\n  getFileNameAndExtension(filePath: string): string | undefined {\r\n    const fileName = filePath.split('\\\\').pop();\r\n    return fileName;\r\n  }\r\n\r\n  getDayFromNumber(day: number) {\r\n    if (day == 0) return \"Sunday\"\r\n    if (day == 1) return \"Monday\"\r\n    if (day == 2) return \"Tuesday\"\r\n    if (day == 3) return \"Wednesday\"\r\n    if (day == 4) return \"Thursday\"\r\n    if (day == 5) return \"Friday\"\r\n    if (day == 6) return \"Saturday\"\r\n    return \"\"\r\n  }\r\n\r\n  getMonthFromNumber(day: number) {\r\n    if (day == 0) return \"January\"\r\n    if (day == 1) return \"February\"\r\n    if (day == 2) return \"Martch\"\r\n    if (day == 3) return \"April\"\r\n    if (day == 4) return \"May\"\r\n    if (day == 5) return \"June\"\r\n    if (day == 6) return \"July\"\r\n    if (day == 7) return \"August\"\r\n    if (day == 8) return \"September\"\r\n    if (day == 9) return \"October\"\r\n    if (day == 10) return \"November\"\r\n    if (day == 11) return \"December\"\r\n    return \"\"\r\n  }\r\n  downloadLink(url: string) {\r\n    this.http.get(url, { responseType: \"blob\" })\r\n      .subscribe(blob => {\r\n        saveAs(blob, this.getTeacherFileName(url));\r\n      });\r\n  }\r\n\r\n  getColorByIndex(index: number) {\r\n    if (index == 0) {\r\n      return \"#003f5c\"\r\n    }\r\n    if (index == 1) {\r\n      return \"#2f4b7c\"\r\n    }\r\n    if (index == 2) {\r\n      return \"#665191\"\r\n    }\r\n    if (index == 3) {\r\n      return \"#a05195\"\r\n    }\r\n    if (index == 4) {\r\n      return \"#d45087\"\r\n    }\r\n    if (index == 5) {\r\n      return \"#f95d6a\"\r\n    }\r\n    if (index == 6) {\r\n      return \"#ff7c43\"\r\n    }\r\n    if (index == 7) {\r\n      return \"#ffa600\"\r\n    }\r\n    return \"white\"\r\n  }\r\n\r\n  downLoadfiles(targetUrl: string) {\r\n    this.http.get(targetUrl)\r\n      .subscribe((res: any) => {\r\n        var a = document.createElement(\"a\");\r\n        a.href = URL.createObjectURL(res.blob());\r\n        a.download = targetUrl;\r\n        // start download\r\n        a.click();\r\n      })\r\n  }\r\n\r\n  updateMobileMenuTitle(targetUrl: string) {\r\n    this.mobileMenuTitle$.next(targetUrl);\r\n  }\r\n\r\n  public isNullishObject(obj: {}): boolean {\r\n    if (obj === null) {\r\n      return true;\r\n    }\r\n    const isNullish = Object.values(obj).every(value => {\r\n      if (value === null) {\r\n        return true;\r\n      }\r\n\r\n      return false;\r\n    });\r\n\r\n    return isNullish;\r\n  }\r\n\r\n  formatDate(d: Date, separator: string) {\r\n    const yyyy = d.getFullYear().toString();\r\n    const mm = (d.getMonth() + 1).toString();\r\n    const dd = d.getDate().toString();\r\n\r\n    return (yyyy + separator + (mm[1] ? mm : \"0\" + mm[0]) + separator + (dd[1] ? dd : \"0\" + dd[0]));\r\n  }\r\n\r\n  formatDateToDMY(dateTimeString: string): string {\r\n    const date = new Date(dateTimeString);\r\n    const formattedDate = date.toLocaleDateString(\"en-GB\", {\r\n      day: \"2-digit\",\r\n      month: \"2-digit\",\r\n      year: \"numeric\"\r\n    });\r\n\r\n    return formattedDate;\r\n  }\r\n\r\n  /**\r\n   * Converts the given date string to the `dd/mm/yyyy` format\r\n   * @param {string} dateString - The date string to be converted.\r\n   * @returns {string} The converted date in `dd/mm/yyyy` format.\r\n   */\r\n  convertToDDMMYYYY(dateString: string): string {\r\n    let date = new Date(dateString);\r\n    let day = date.getDate().toString().padStart(2, \"0\");\r\n    let month = (date.getMonth() + 1).toString().padStart(2, \"0\");\r\n    let year = date.getFullYear();\r\n\r\n    return `${day}/${month}/${year}`;\r\n  }\r\n\r\n  /**\r\n  Converts a date string in dd/MM/yyyy format to ISO format with zero time.\r\n  @param {string} dateString - The date string in dd/MM/yyyy format to convert.\r\n  @returns {string} The ISO format string with zero time.\r\n  */\r\n  convertDateStringToIsoFormatWithZeroTime(dateString: string): string {\r\n    let [day, month, year] = dateString.split(\"/\");\r\n    let isoDateString = `${year}-${month.padStart(2, \"0\")}-${day.padStart(2, \"0\")}T00:00:00.000`;\r\n    return isoDateString;\r\n  }\r\n\r\n  /**\r\n   * Converts a given datetime string to ISO date format\r\n   * @param {string} dateTimeString - The datetime string to be converted\r\n   * @returns {string} The ISO date string\r\n   */\r\n  convertToIsoDate(dateTimeString: string): string {\r\n    const date = new Date(dateTimeString);\r\n    const formattedDate = date.toISOString();\r\n\r\n    return this.convertWithoutTimeAndZ(formattedDate);\r\n  }\r\n\r\n  /**\r\n  Converts a date string to an ISO string without time and Z\r\n  @param {string} dateString - The date string to be converted\r\n  @returns {string} The converted ISO string\r\n  */\r\n  convertWithoutTimeAndZ(dateString: string): string {\r\n    const date = new Date(dateString);\r\n\r\n    date.setUTCHours(0, 0, 0, 0);\r\n    date.setDate(date.getDate());\r\n\r\n    const isoDateString = date.toISOString().replace('Z', '');\r\n\r\n    return isoDateString;\r\n  }\r\n\r\n  /**\r\n   * Converts a date string to a formatted time string.\r\n   *\r\n   * @param {string} dateString - The date string to convert.\r\n   * @returns {string} A formatted time string in 24-hour format (e.g. \"14:30\").\r\n   */\r\n  convertToTime(dateString: string) {\r\n    const date = new Date(dateString);\r\n    const options = { hour12: false, hour: 'numeric', minute: 'numeric' };\r\n    return date.toLocaleTimeString('en-US', options as any);\r\n  }\r\n\r\n  /**\r\n   * Converts a camelCase string into a spaced string.\r\n   * \r\n   * @param {string} camelCaseString - The camelCase string to be converted.\r\n   * @returns {string} A spaced string.\r\n   */\r\n  convertCamelCase(camelCaseString: string): string {\r\n    let spacedString = '';\r\n\r\n    for (let i = 0; i < camelCaseString.length; i++) {\r\n      const originalChar = camelCaseString[i];\r\n      const upperCaseChar = camelCaseString[i].toUpperCase();\r\n\r\n      if (originalChar === upperCaseChar) {\r\n        spacedString += ' ' + originalChar;\r\n      } else {\r\n        spacedString += originalChar;\r\n      }\r\n    }\r\n\r\n    return spacedString;\r\n  }\r\n\r\n  /**\r\n  Check if a form field is valid.\r\n  @param form - The form to check.\r\n  @param field - The field to check.\r\n  @param tryToSave - Whether the form is being submitted.\r\n  @returns Whether the field is invalid and has been touched, or if the form is being submitted and the field is invalid.\r\n  */\r\n  ifFieldValid(form: UntypedFormGroup, field: string, tryToSave: boolean) {\r\n    return ((form.get(field)?.invalid && form.get(field)?.touched) || (tryToSave && form.get(field)?.invalid));\r\n  }\r\n\r\n  getUserLocalTime(timeZoneUTC: string, isHour12 = true) {\r\n    if (!timeZoneUTC) {\r\n      return;\r\n    }\r\n\r\n    const filteredTimezones = data.timezones.filter(timezone => timezone.text === timeZoneUTC);\r\n    if (filteredTimezones.length === 0) {\r\n      return;\r\n    }\r\n\r\n    const date = new Date();\r\n    const options = {\r\n      timeZone: filteredTimezones[0].utc[0],\r\n      hour: 'numeric',\r\n      minute: 'numeric',\r\n      hour12: isHour12 // set to true to use 12-hour format\r\n    };\r\n\r\n    // Check if the current date is within DST period\r\n    const isDstActive = () => {\r\n      const now = new Date();\r\n      const startDst = new Date(now.getFullYear(), 2, 31 - ((5 * now.getFullYear() / 4 + 4) % 7), 1);\r\n      const endDst = new Date(now.getFullYear(), 9, 31 - ((5 * now.getFullYear() / 4 + 1) % 7), 1);\r\n      return now.getTime() >= startDst.getTime() && now.getTime() < endDst.getTime();\r\n    };\r\n\r\n    // Adjust the offset if DST is active\r\n    if (isDstActive()) {\r\n      date.setHours(date.getHours() + 1);\r\n    }\r\n\r\n    const localTime = new Intl.DateTimeFormat('en-US', options as any).format(date);\r\n    return localTime;\r\n  }\r\n\r\n  scaleElementDown(element: ElementRef, transformOrigin: string, zoomValue: number, divElement?: HTMLElement) {\r\n    let zoom = zoomValue;\r\n    var width = 100;\r\n    zoom = zoom - 0.1;\r\n    width = 100 / zoom;\r\n    if (divElement) {\r\n\r\n      divElement.style.transformOrigin = transformOrigin;\r\n      divElement.style.transform = \"scale(\" + zoom + \")\";\r\n      divElement.style.width = width + \"%\";\r\n      // divElement.style.height = width + \"vh\";\r\n    } else {\r\n\r\n      element.nativeElement.style.transformOrigin = transformOrigin;\r\n      element.nativeElement.style.transform = \"scale(\" + zoom + \")\";\r\n      element.nativeElement.style.width = width + \"%\";\r\n      element.nativeElement.style.height = width + \"vh\";\r\n    }\r\n\r\n  }\r\n  scaleElementReset(element: ElementRef, divElement?: HTMLElement) {\r\n    var zoom = 1;\r\n    var width = 100;\r\n    zoom = zoom;\r\n    width = 100 / zoom;\r\n    if (divElement) {\r\n\r\n      divElement.style.transformOrigin = \"unset\";\r\n      divElement.style.transform = \"none\";\r\n      divElement.style.width = width + \"%\";\r\n    } else {\r\n      element.nativeElement.style.transformOrigin = \"unset\";\r\n      element.nativeElement.style.transform = \"none\";\r\n      element.nativeElement.style.width = width + \"%\";\r\n    }\r\n\r\n  }\r\n\r\n  /**\r\n   * Finds the image of the country with the given phone code.\r\n   *\r\n   * @param {string} phoneCode The phone code of the country.\r\n   * @returns {string} The image of the country, or undefined if the country is not found.\r\n   */\r\n  findCountryImage(phoneCode: string) {\r\n    const countries = this.countries;\r\n    const phoneCodes = this.getPhoneCodes();\r\n\r\n    const filteredCountries = countries.filter((country) => {\r\n      const countryCode = phoneCodes.find((pc) => pc.code === phoneCode)?.iso;\r\n      return country.code === countryCode;\r\n    });\r\n\r\n    const countryImage = filteredCountries[0]?.image;\r\n\r\n    return countryImage;\r\n  }\r\n\r\n  findCountryImageByName(countryName: string): string {\r\n    const country = this.getCountries().find(c => c.name === countryName);\r\n    return country ? country.image : '';\r\n  }\r\n\r\n  getHeaderName(person: any): string {\r\n    return (person.firstName === 'NotGiven') || (person.firstName === 'Not Given') ? '' : (person.firstName + \" \" + person.lastName.charAt(0) + \".\");\r\n  }\r\n\r\n  getShortFullName(person: any): string {\r\n    if (person.lastName === \"\") {\r\n      return person.firstName;\r\n    } else {\r\n      return person.firstName + \" \" + person.lastName.charAt(0) + \".\";\r\n    }\r\n  }\r\n\r\n  /**\r\n   * The function takes a person object and returns their full name by concatenating their first name and\r\n   * last name.\r\n   * @param {any} person - The `person` parameter is an object that represents a person. It should have\r\n   * properties `firstName` and `lastName` which are strings representing the person's first name and\r\n   * last name respectively.\r\n   * @returns a string, which is the full name of the person.\r\n   */\r\n  getPersonFullName(person: any): string {\r\n    if (person.firstName === \"\" && person.lastName === \"\") {\r\n      return \"\";\r\n    } else if (person.firstName === \"\") {\r\n      return person.lastName;\r\n    } else if (person.lastName === \"\") {\r\n      return person.firstName;\r\n    } else {\r\n      return person.firstName + \" \" + person.lastName;\r\n    }\r\n  }\r\n\r\n  capitalize(str: string): string {\r\n    if (!str) return str;\r\n    return str.charAt(0).toUpperCase() + str.slice(1);\r\n  }\r\n\r\n  /**\r\n   * Calculates the age from a given date of birth.\r\n   * @param dob The date of birth as a string in the format \"YYYY-MM-DDTHH:mm:ss\".\r\n   * @returns The age in years.\r\n   */\r\n  calculateAge(dob: string): number {\r\n    const dateOfBirth = new Date(dob);\r\n    const ageMs = Date.now() - dateOfBirth.getTime();\r\n    const ageDate = new Date(ageMs);\r\n    const age = Math.abs(ageDate.getUTCFullYear() - 1970);\r\n\r\n    return age;\r\n  }\r\n\r\n  /**\r\n   * Sets the default user avatar image.\r\n   * @param event The event that triggered the function.\r\n   */\r\n  setDefaultUserAvatar(event: Event) {\r\n    const imgElement = event.target as HTMLImageElement;\r\n    imgElement.src = 'assets/images/default-user-avatar.png';\r\n  }\r\n\r\n  convertMinutesToHours(minutes: number): number {\r\n    return minutes / 60;\r\n  }\r\n\r\n  convertHoursToMinutesWithSuffix(hours: number, fullSuffix = false): string {\r\n    const decimalPart = hours % 1;\r\n    const wholeHours = Math.floor(hours);\r\n    let minutes = wholeHours * 60;\r\n\r\n    if (decimalPart > 0) {\r\n      minutes += decimalPart * 60;\r\n    }\r\n\r\n    // Handle special cases where decimalPart is 0.25, 0.50, 0.75, etc.\r\n    if (decimalPart % 0.25 === 0) {\r\n      minutes += (decimalPart * 60 - Math.floor(decimalPart * 60)) * 15;\r\n    }\r\n\r\n    // Return zero hours if minutes is zero.\r\n    if (minutes === 0) {\r\n      return '0 hours';\r\n    } else if (minutes < 60) {\r\n      // Return minutes with suffix if less than 60.\r\n      return `${minutes > 0 ? minutes : ''}${fullSuffix && minutes > 0 ? ' Minutes' : 'M'}`;\r\n    } else {\r\n      const hours = Math.floor(minutes / 60);\r\n      const minuteSuffix = fullSuffix ? (minutes === 60 ? ' Minute' : ' Mins') : 'M';\r\n      const hourSuffix = fullSuffix ? (hours === 1 ? ' Hour' : ' Hours') : 'H';\r\n\r\n      // Return hours and minutes with suffix.\r\n      if (hours === 0) {\r\n        return `${minutes > 0 ? minutes : ''}${fullSuffix && minutes > 0 ? minuteSuffix : ''}`;\r\n      } else {\r\n        return `${hours}${hourSuffix}${minutes % 60 > 0 ? ` ${minutes % 60}${fullSuffix ? minuteSuffix : 'M'}` : ''}`;\r\n      }\r\n    }\r\n  }\r\n\r\n  formatSelectedDateTimeForLesson(selectedDay: Date, selectedTimeSlot: string): string {\r\n    const selectedDateTimeString = `${selectedDay.toDateString()} ${selectedTimeSlot}`;\r\n    const formattedDate = moment.utc(selectedDateTimeString, 'ddd MMM DD YYYY HH:mm').toISOString();\r\n    return formattedDate;\r\n  }\r\n\r\n  getMinutesDifference(start: string, end: string): number {\r\n    const format = 'HH:mm A'; // Use 24-hour clock format\r\n    const startTime = moment(start, format);\r\n    const endTime = moment(end, format);\r\n\r\n    const diffInMilliseconds = endTime.diff(startTime);\r\n    const diffInMinutes = Math.round(diffInMilliseconds / 1000 / 60);\r\n\r\n    return diffInMinutes;\r\n  }\r\n\r\n  formatDateWithoutOffset(dateTimeString: string): string {\r\n    return moment.utc(dateTimeString).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');\r\n  }\r\n\r\n  formatLessonStartingDate(dateTimeString: string): string {\r\n    return moment.parseZone(dateTimeString).format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');\r\n  }\r\n\r\n  /**\r\n   * The function `navigateToLessonDetails` navigates to the lesson details page based on the classroom\r\n   * type and user role.\r\n   * @param {Classroom} classroom - The `classroom` parameter represents the classroom object that\r\n   * contains information about the classroom, such as its id and type.\r\n   * @param {UserRole} role - The `role` parameter represents the user role, which can be either\r\n   * `TEACHER` or something else.\r\n   */\r\n  navigateToLessonDetails(classroom: Classroom, role: UserRole) {\r\n    const navigationExtras: NavigationExtras = {\r\n      state: { classRoom: classroom }\r\n    };\r\n    if (classroom.type === 'Trial') {\r\n      if (role === UserRole.TEACHER) {\r\n        this.router.navigate(['/dashboard/classrooms/lessons', classroom.id, 'details'], navigationExtras);\r\n      } else {\r\n        // TODO add real lesson\r\n        this.router.navigate(['dashboard/classrooms/lessons', classroom.id, 'info', 'teacher'], {\r\n          state: { classRoom: classroom }\r\n        });\r\n      }\r\n\r\n    } else {\r\n      this.router.navigate(['/dashboard/classrooms/lessons', classroom.id, 'details'], navigationExtras);\r\n    }\r\n  }\r\n\r\n  /**\r\n  Returns an array of strings that represents a link to a lesson details page.\r\n  @param {Classroom} classroom - The classroom object that the lesson belongs to.\r\n  @param {UserRole} role - The user's role in the classroom.\r\n  @returns {string[]} An array of strings that represents a link to a lesson details page.\r\n  */\r\n  getLessonDetailsLink(classroom: Classroom, role: UserRole): string[] {\r\n    let link: string[] = ['/dashboard/classrooms/lessons', classroom.id, 'details'];\r\n    if (classroom.type.toLowerCase() === ClassroomType.TRIAL.toLowerCase() && role === UserRole.STUDENT) {\r\n      link = ['/dashboard/classrooms/lessons', classroom.id, 'info', 'teacher'];\r\n    }\r\n    return link;\r\n  }\r\n\r\n  /**\r\n  Opens a dialog box with the specified component, width, and height.\r\n  @param {any} dialogService - The dialog service object.\r\n  @param {any} component - The component to be displayed in the dialog box.\r\n  @param {any} width - The width of the dialog box.\r\n  @param {any} [dialogData] - Optional data to be passed to the dialog box component.\r\n  @param {any} [appendDialogActionsTo] - Optional ID of an HTML element where the dialog box actions should be appended.\r\n  @param {function} [resultHandler] - Optional function to handle the result of the dialog box.\r\n  @param {string} [height] - Optional height of the dialog box.\r\n  */\r\n  public openDialogWithComponent(dialogService: any, component: any, width: any, dialogData?: any, appendDialogActionsTo?: any,\r\n    resultHandler?: (result: any) => void, height?: string) {\r\n    this.refs.forEach(ref => ref.close());\r\n    this.refs = [];\r\n    let centerX = '50%';\r\n    let centerY = '50%';\r\n    if (appendDialogActionsTo) {\r\n      if (Object.keys(appendDialogActionsTo).length > 0) {\r\n        const element = document.querySelector('#' + appendDialogActionsTo);\r\n        const rect = element!.getBoundingClientRect();\r\n        centerX = (rect.left + 10 + rect.width / 2 - width / 2) + 'px';\r\n        centerY = (rect.top + window.scrollY) + 'px'; // Add window.scrollY to account for scrolling\r\n      }\r\n    }\r\n\r\n    if (!width) {\r\n      width = 290;\r\n    }\r\n\r\n    const dialogRef = (dialogService.open(component, {\r\n      header: '',\r\n      width: this.containsOnlyNumbers(width) ? width + 'px' : width,\r\n      height: height ? height : 'auto',\r\n      showHeader: false,\r\n      dismissableMask: true,\r\n      modal: true,\r\n      contentStyle: {\r\n        \"max-width\": \"100%\", \"max-height\": height ? height : \"400px\", \"overflow\": \"auto\", \"border-radius\": \"10px\", \"padding\": \"0px\",\r\n      },\r\n      style: appendDialogActionsTo ? { 'left': centerX, 'top': centerY, 'position': `fixed` } : {},\r\n      baseZIndex: 10000,\r\n      maskStyleClass: 'transparent-mask',\r\n      data: { dialogData: dialogData },\r\n    }));\r\n    this.refs.push(dialogRef);\r\n    dialogRef.onClose.subscribe((data: any) => {\r\n      console.log('Dialog closed with data:', data);\r\n      if (resultHandler) {\r\n        resultHandler(data);\r\n      }\r\n    });\r\n  }\r\n\r\n  destroyComponent(componentRef: any) {\r\n    if (componentRef) {\r\n      componentRef.destroy();\r\n    }\r\n  }\r\n\r\n  navigateToBookingSystem() {\r\n    this.router.navigateByUrl('/dashboard/calendar/booking-system');\r\n  }\r\n\r\n  navigateToAiChat() {\r\n    this.router.navigateByUrl('/dashboard/ai-chat');\r\n  }\r\n\r\n  isObjectEmpty(obj: any): boolean {\r\n    return Object.keys(obj).length === 0;\r\n  }\r\n\r\n  isDayInThePast(dayNumber: number, currentDate: Date): boolean {\r\n    const dateString = currentDate.getFullYear() + '-' + (currentDate.getMonth() + 1) + '-' + dayNumber;\r\n    const today = new Date();\r\n    const currentDateFormatted = new Date(dateString);\r\n\r\n    // Set hours, minutes, seconds, and milliseconds to 0\r\n    today.setHours(0, 0, 0, 0);\r\n    currentDate.setHours(0, 0, 0, 0);\r\n\r\n    return currentDateFormatted < today;\r\n  }\r\n\r\n  getPercentScreenWidthInPixels(percentageVal = 0.8): number {\r\n    const screenWidthInPixels = window.innerWidth;\r\n    const percentage = percentageVal;\r\n    const result = screenWidthInPixels * percentage;\r\n    return result;\r\n  }\r\n\r\n  containsOnlyNumbers(input: string): boolean {\r\n    // Regular expression pattern to match only numbers\r\n    const pattern = /^[0-9]+$/;\r\n\r\n    return pattern.test(input);\r\n  }\r\n\r\n  // Method to navigate to the lesson details page\r\n  navigateToBuyPackage(classroom?: Classroom) {\r\n    const navigationExtras: NavigationExtras = {};\r\n    if (classroom) {\r\n      navigationExtras.queryParams = { 'classroom': JSON.stringify(classroom) };\r\n    }\r\n    this.router.navigate(['dashboard/buy-package'], navigationExtras);\r\n  }\r\n\r\n  openExternalLink(url: string): void {\r\n    window.open(url, \"_blank\");\r\n  }\r\n\r\n  replaceImportedWithDash(val: string): string {\r\n    if (!val) {\r\n      return '';\r\n    }\r\n    if (val === \"Imported\" || val.includes('Not Given')) {\r\n      return \"-\";\r\n    }\r\n\r\n    return val;\r\n  }\r\n\r\n  replaceNotGivenWith(val: string, replaceStr?: string): string {\r\n    if (!val) {\r\n      return '';\r\n    }\r\n    if (val === \"Imported\" || val.includes('Not Given')) {\r\n      return replaceStr ? replaceStr : \"-\";\r\n    }\r\n\r\n    return val;\r\n  }\r\n\r\n  getSafeSkypeUrl(url: string): SafeUrl {\r\n    const skypeUrl = `skype:` + url;\r\n    return this.sanitizer.bypassSecurityTrustUrl(skypeUrl);\r\n  }\r\n\r\n  sortByStartingDate(a: Lesson, b: Lesson): number {\r\n    const startDateA = moment(a.startingDate);\r\n    const startDateB = moment(b.startingDate);\r\n    if (startDateA.isBefore(startDateB, 'minute')) {\r\n      return -1;\r\n    } else if (startDateA.isAfter(startDateB, 'minute')) {\r\n      return 1;\r\n    } else {\r\n      return 0;\r\n    }\r\n  }\r\n\r\n  sortLessonsByDate(a: Lesson, b: Lesson): number {\r\n    const dateA = moment(a.startingDate);\r\n    const dateB = moment(b.startingDate);\r\n    return dateB.diff(dateA);\r\n  }\r\n\r\n  extractCountryPrefix(phoneNumber: string | undefined): string {\r\n    if (!phoneNumber) {\r\n      return '';\r\n    }\r\n\r\n    const parts = phoneNumber.split(' ');\r\n    if (parts.length > 0) {\r\n      return parts[0]; // remove the \"+\" prefix\r\n    }\r\n\r\n    return '';\r\n  }\r\n\r\n  getDefaultPhoneCode(): void {\r\n    const ukPhoneCode = this.getPhoneCodes().find(el => el.iso === 'GB');\r\n    return ukPhoneCode || this.getPhoneCodes()[0] || '';\r\n  }\r\n\r\n  extractPhoneNumber(phoneNumber: string | undefined): string {\r\n    if (!phoneNumber) {\r\n      return '';\r\n    }\r\n\r\n    const parts = phoneNumber.split(' ');\r\n    if (parts.length > 1) {\r\n      return parts[1]; // remove the \"+\" prefix\r\n    }\r\n\r\n    return '';\r\n  }\r\n\r\n  getLessonTime(lessonDate: string | Date, timeFormat: string = 'HH:mm') {\r\n    let lessonTime;\r\n    let lessonDateString = typeof lessonDate === 'string' ? lessonDate : moment(lessonDate).toISOString();\r\n\r\n    if (lessonDateString.endsWith('Z')) {\r\n      lessonTime = moment.utc(lessonDateString).format(timeFormat);\r\n    } else {\r\n      lessonTime = moment(lessonDateString).format(timeFormat);\r\n    }\r\n    return lessonTime;\r\n  }\r\n\r\n  sortLessonsByRecentFirst(a: Lesson, b: Lesson): number {\r\n    const dateB = moment(b.startingDate);\r\n    const dateA = moment(a.startingDate);\r\n    return dateB.diff(dateA);\r\n  }\r\n\r\n  isWithinTimeRange(dateString: string, timeRange: number): boolean {\r\n    const now = moment();\r\n    const date = moment(dateString);\r\n    const minutesDifference = Math.abs(now.diff(date, 'minutes'));\r\n    const hoursDifference = minutesDifference / 60;\r\n    console.log(hoursDifference);\r\n\r\n    // check if the date is in the past\r\n    const isPast = now.isAfter(date);\r\n    console.log(isPast);\r\n\r\n    // return true only if the date is NOT in the past and the difference in hours is within the time range\r\n    return !isPast && (hoursDifference >= timeRange);\r\n  }\r\n\r\n  goToGoogleMeetLink(lesson: Lesson): void {\r\n    if (lesson.googleMeetURL) {\r\n      const urlPattern = /^(http|https):\\/\\/[^ \"]+$/;\r\n      if (urlPattern.test(lesson.googleMeetURL)) {\r\n        const sanitizedURL = encodeURI(lesson.googleMeetURL);\r\n        window.open(sanitizedURL, \"_blank\");\r\n      } else {\r\n        console.error(\"Invalid Google Meet URL for the lesson.\");\r\n      }\r\n    } else {\r\n      console.error(\"Google Meet URL is missing for the lesson.\");\r\n    }\r\n  }\r\n\r\n  goToBellbirdLink(lesson: Lesson): void {\r\n    if (lesson.bellBirdMeetingURL) {\r\n      const urlPattern = /^(http|https):\\/\\/[^ \"]+$/;\r\n      if (urlPattern.test(lesson.bellBirdMeetingURL!)) {\r\n        const sanitizedURL = encodeURI(lesson.bellBirdMeetingURL!);\r\n        window.open(sanitizedURL, \"_blank\");\r\n      } else {\r\n        console.error(\"Invalid Google Meet URL for the lesson.\");\r\n      }\r\n    } else {\r\n      console.error(\"Google Meet URL is missing for the lesson.\");\r\n    }\r\n  }\r\n\r\n  goToJitsiMeetLink(lesson: Lesson): void {\r\n    if (lesson) {\r\n      this.router.navigate(['/meet'], { queryParams: { id: lesson.id, classroomId: lesson.classroomId } });\r\n    } else {\r\n      console.error(\"Google Meet URL is missing for the lesson.\");\r\n    }\r\n  }\r\n\r\n  convertTimezoneValueToText(timezone: string) {\r\n    return (data.timezones.find((el: Timezone) => el.utc.includes(timezone)));\r\n  }\r\n\r\n  isWithinMinutes(minutes: number = 15, time: string, dateTime: string): boolean {\r\n    const currentTime = moment();\r\n    const targetDateTime = moment(dateTime);\r\n\r\n    // Parse the target time\r\n    const [hours, minutesOfDay] = time.split(':').map(Number);\r\n    const targetTime = moment().hours(hours).minutes(minutesOfDay);\r\n\r\n    // Check if the target date is today\r\n    if (!targetDateTime.isSame(currentTime, 'day')) {\r\n      return false;\r\n    }\r\n\r\n    // Calculate the time difference in minutes\r\n    const timeDifference = targetTime.diff(currentTime, 'minutes');\r\n\r\n    // Check if the time difference is within the specified minutes\r\n    return Math.abs(timeDifference) <= minutes;\r\n  }\r\n\r\n  getCurrentTime(timezone: string) {\r\n    // Get the offset value from the JSON data\r\n    const timezoneData = data.timezones.find((tz: Timezone) => tz.utc.includes(timezone));\r\n    if (!timezoneData) {\r\n      throw new Error('Timezone not found in data.');\r\n    }\r\n    const offset = timezoneData.offset;\r\n\r\n    // Create a moment object with the current timestamp\r\n    const currentMoment = moment();\r\n\r\n    // Apply the offset to the moment object\r\n    const convertedMoment = currentMoment.utcOffset(offset);\r\n\r\n    // Format the converted moment object to display the current time\r\n    const currentTime = convertedMoment.format('HH:mm');\r\n\r\n    return currentTime;\r\n  }\r\n\r\n  getCurrentTimeInTimezone(timezone: string): string {\r\n    const currentTime = moment(); // Use the current time\r\n    const date = moment.tz(currentTime, timezone);\r\n    return date.format('YYYY-MM-DDTHH:mm:ss');\r\n  }\r\n\r\n  convertTimeToHourOfDayFormat(time: string, timezone: string): string {\r\n    const date = moment.tz(moment(), timezone);\r\n    return date.format('hh:mm A');\r\n  }\r\n\r\n  getTimeDifferenceInMinutes(date1: string, date2: string): number {\r\n    const userTimezone = this.authService.getLoggedInUser().timeZone;\r\n    const momentDate1 = moment.tz(date1, userTimezone); // Assuming date1 is in UTC timezone\r\n    const momentDate2 = moment.tz(date2, userTimezone); // Assuming date2 is in UTC timezone\r\n    const differenceInMinutes = momentDate1.diff(momentDate2, 'minutes');\r\n    return differenceInMinutes;\r\n  }\r\n\r\n  isDifferenceWithin30Minutes(date1: string, date2: string, lesson: Lesson): boolean {\r\n    const endTime = moment(lesson.startingDate).add(lesson.duration, 'hours');\r\n    const differenceInMinutes = this.getTimeDifferenceInMinutes(date1, date2);\r\n    const lessonDurationInMinutes = lesson.duration * 60;\r\n    // If date2 is before date1 or endTime, consider the difference as 30 minutes\r\n    if (moment(date2).isBefore(date1) || moment(date2).isBefore(endTime)) {\r\n      return Math.abs(differenceInMinutes) <= lessonDurationInMinutes;\r\n    }\r\n\r\n    // Calculate the maximum allowable time difference including the lesson duration and 30 minutes\r\n    const maxAllowedDifference = lesson.duration * 60 + 30; // Convert lesson duration to minutes\r\n\r\n    // Check if the absolute difference between date1 and date2 is within the allowable range\r\n    return Math.abs(differenceInMinutes) <= maxAllowedDifference;\r\n  }\r\n\r\n\r\n\r\n  isWithin30Mins(lesson: Lesson, user: User) {\r\n    const userTimeZone = this.convertTimezoneValueToText(user.timeZone!).text;\r\n    const userLocalTime = this.getUserLocalTime(this.convertTimezoneValueToText(user.timeZone!).text, false)!;\r\n\r\n    const currentTimeInUserTimezone = this.getCurrentTime(user.timeZone!);\r\n    const lessonStartingDateInUserTimezone = lesson.startingDate.toString();\r\n    // console.log(this.showTimeDifference(userTimeZone, lessonStartingDateInUserTimezone));\r\n    return this.isWithinMinutes(30, currentTimeInUserTimezone, lessonStartingDateInUserTimezone);\r\n  }\r\n\r\n  showTimeDifference(startTime: string, endTime: string): string {\r\n    const startMoment = moment(startTime, 'HH:mm');\r\n    const endMoment = moment(endTime, 'HH:mm');\r\n\r\n    const duration = moment.duration(endMoment.diff(startMoment));\r\n\r\n    const hours = Math.floor(duration.asHours());\r\n    const minutes = Math.floor(duration.asMinutes()) % 60;\r\n\r\n    return `${hours} hours and ${minutes} minutes`;\r\n  }\r\n\r\n  getCountryCode(): Observable<string> {\r\n    return this.http.get<any>(IP_COUNTRY_API).pipe(\r\n      map(response => {\r\n        if (response) {\r\n          return response;\r\n        } else {\r\n          throw new Error('Country code not found in response');\r\n        }\r\n      })\r\n    );\r\n  }\r\n\r\n}\r\n"], "mappings": "AACA,SAAiCA,MAAM,QAAQ,eAAe;AAE9D,SAASC,eAAe,EAAQC,GAAG,QAAwB,MAAM;AACjE,SAAoBC,aAAa,EAAEC,KAAK,QAAQ,2BAA2B;AAC3E,SAA8BC,UAAU,QAAwE,yBAAyB;AACzI,SAASC,oBAAoB,QAAuD,yBAAyB;AAC7G,SAASC,MAAM,QAAQ,YAAY;AACnC,OAAO,KAAKC,IAAI,MAAM,gBAAgB;AACtC,OAAO,KAAKC,MAAM,MAAM,iBAAiB;AAEzC,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAAeC,QAAQ,QAAQ,sBAAsB;AAGrD,SAASC,WAAW,QAAQ,gBAAgB;;;;;AAC5C,MAAMC,OAAO,GAAGH,WAAW,CAACI,MAAM,GAAG,GAAG;AACxC,MAAMC,cAAc,GAAG,wBAAwB;AAK/C,OAAM,MAAOC,cAAc;EA2KzBC,YACUC,MAAc,EACdC,IAAgB,EAChBC,SAAuB;IAFvB,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,SAAS,GAATA,SAAS;IA7KnB,KAAAC,WAAW,GAAGrB,MAAM,CAACY,WAAW,CAAC;IACzB,KAAAU,WAAW,GAAgC,IAAIrB,eAAe,CAAa;MAAEsB,QAAQ,EAAE,KAAK;MAAEC,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,MAAM,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAI,CAAE,CAAC;IACpL,KAAAC,gBAAgB,GAA4B,IAAI7B,eAAe,CAAS,MAAM,CAAC;IAEvE,KAAA8B,UAAU,GAA2B,IAAI,CAACT,WAAW,CAACU,YAAY,EAAE;IACpE,KAAAC,eAAe,GAAuB,IAAI,CAACH,gBAAgB,CAACE,YAAY,EAAE;IAE1F,KAAAE,WAAW,GACT;MACEC,QAAQ,EAAE,6CAA6C;MACvDC,OAAO,EAAE,iCAAiC;MAC1CC,QAAQ,EAAE,sCAAsC;MAChDC,OAAO,EAAE,uCAAuC;MAChDC,SAAS,EAAE,wCAAwC;MACnDC,MAAM,EAAE;KACT;IAEa,KAAAC,YAAY,GAAe,CACzC;MAAEC,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAK,CAAE,EAChC;MAAED,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC9B;MAAED,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAK,CAAE,EAC/B;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAED,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAK,CAAE,EAC9B;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAI,CAAE,EAC/B;MAAED,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE;IAAK,CAAE,EAC9B;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAK,CAAE,EAChC;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAK,CAAE,EACjC;MAAED,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE;IAAK,CAAE,EAC/B;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAK,CAAE,CACjC;IAEe,KAAAC,SAAS,GAAG;IAC1B;IACAxC,KAAK,CAACyC,EAAE,EACRzC,KAAK,CAAC0C,EAAE,EACR1C,KAAK,CAAC2C,EAAE,EACR3C,KAAK,CAAC4C,EAAE,EACR5C,KAAK,CAAC6C,EAAE,EACR7C,KAAK,CAAC8C,EAAE,EACR9C,KAAK,CAAC+C,IAAI,EACV/C,KAAK,CAACgD,IAAI,EACVhD,KAAK,CAACiD,GAAG,EACTjD,KAAK,CAACkD,EAAE,CACT;IAEe,KAAAC,gBAAgB,GAAsB,CACpD;MACEC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;KACP,EACD;MACED,KAAK,EAAE,QAAQ;MACfC,IAAI,EAAE;KACP,EACD;MACED,KAAK,EAAE,QAAQ;MACfC,IAAI,EAAE;KACP,EACD;MACED,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;KACP,EACD;MACED,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;KACP,EACD;MACED,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE;KACP,EACD;MACED,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;KACP,EACD;MACED,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE;KACP,EACD;MACED,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;KACP,EACD;MACED,KAAK,EAAE,UAAU;MACjBC,IAAI,EAAE;KACP,EACD;MACED,KAAK,EAAE,QAAQ;MACfC,IAAI,EAAE;KACP,EACD;MACED,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;KACP,CACF;IAEe,KAAAC,WAAW,GAAkB,CAC3C;MACEC,IAAI,EAAE,cAAc;MACpBF,IAAI,EAAE;KACP,EACD;MACEE,IAAI,EAAE,cAAc;MACpBF,IAAI,EAAE;KACP,EACD;MACEE,IAAI,EAAE,aAAa;MACnBF,IAAI,EAAE;KACP,EACD;MACEE,IAAI,EAAE,eAAe;MACrBF,IAAI,EAAE;KACP,EACD;MACEE,IAAI,EAAE,aAAa;MACnBF,IAAI,EAAE;KACP,CACF;IAEe,KAAAG,cAAc,GAAU,CACtC;MACEC,SAAS,EAAE,IAAI;MACfC,cAAc,EAAE,EAAE;MAClBC,OAAO,EAAE;KACV,EACD;MACEF,SAAS,EAAE,IAAI;MACfC,cAAc,EAAE,EAAE;MAClBC,OAAO,EAAE;KACV,EACD;MACEF,SAAS,EAAE,IAAI;MACfC,cAAc,EAAE,EAAE;MAClBC,OAAO,EAAE;KACV,EACD;MACEF,SAAS,EAAE,IAAI;MACfC,cAAc,EAAE,EAAE;MAClBC,OAAO,EAAE;KACV,EACD;MACEF,SAAS,EAAE,IAAI;MACfC,cAAc,EAAE,EAAE;MAClBC,OAAO,EAAE;KACV,EACD;MACEF,SAAS,EAAE,IAAI;MACfC,cAAc,EAAE,EAAE;MAClBC,OAAO,EAAE;KACV,EACD;MACEF,SAAS,EAAE,OAAO;MAClBC,cAAc,EAAE,CAAC,IAAI,CAAC;MACtBC,OAAO,EAAE;KACV,EACD;MACEF,SAAS,EAAE,OAAO;MAClBC,cAAc,EAAE,CAAC,IAAI,CAAC;MACtBC,OAAO,EAAE;KACV,CAAC;IAEY,KAAAC,oBAAoB,GAAG1D,oBAAoB;IAE3C,KAAA2D,SAAS,GAAGzD,IAAI,CAACyD,SAAS;IAC1B,KAAAC,SAAS,GAAG1D,IAAI,CAAC0D,SAAS;IAC1B,KAAAC,SAAS,GAAG3D,IAAI,CAAC2D,SAAS;IAElC,KAAAC,IAAI,GAAU,EAAE;EAMpB;EAEJC,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAACd,gBAAgB;EAC9B;EAEAe,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACZ,WAAW;EACzB;EAEAa,YAAYA,CAAA;IACV,OAAO/D,IAAI,CAACyD,SAAS;EACvB;EAEAO,YAAYA,CAAA;IACV,OAAOhE,IAAI,CAAC0D,SAAS;EACvB;EAEAO,aAAaA,CAAA;IACX,OAAOjE,IAAI,CAACkE,UAAU;EACxB;EAEAC,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACf,cAAc;EAC5B;EAEOgB,SAASA,CAAA;IACd,IAAIrD,QAAQ,GAAGsD,MAAM,CAACC,UAAU,GAAGzE,UAAU,CAAC0E,IAAI;IAClD,IAAIC,MAAM,GAAGH,MAAM,CAACC,UAAU,IAAIzE,UAAU,CAAC0E,IAAI;IACjD,IAAIE,MAAM,GAAGJ,MAAM,CAACC,UAAU,IAAIzE,UAAU,CAAC6E,IAAI;IACjD,IAAIC,MAAM,GAAGN,MAAM,CAACC,UAAU,IAAIzE,UAAU,CAAC+E,IAAI;IACjD,IAAIC,OAAO,GAAGR,MAAM,CAACC,UAAU,IAAIzE,UAAU,CAACiF,KAAK;IACnD,IAAIC,OAAO,GAAGV,MAAM,CAACC,UAAU,IAAIzE,UAAU,CAACmF,KAAK;IACnD,IAAIC,OAAO,GAAGZ,MAAM,CAACC,UAAU,IAAIzE,UAAU,CAACqF,KAAK;IAEnD,IAAIX,IAAI,GAAGF,MAAM,CAACC,UAAU,IAAIzE,UAAU,CAAC0E,IAAI;IAC/C,IAAIG,IAAI,GAAGL,MAAM,CAACC,UAAU,IAAIzE,UAAU,CAAC6E,IAAI;IAC/C,IAAIE,IAAI,GAAGP,MAAM,CAACC,UAAU,IAAIzE,UAAU,CAAC+E,IAAI;IAC/C,IAAIE,KAAK,GAAGT,MAAM,CAACC,UAAU,IAAIzE,UAAU,CAACiF,KAAK;IACjD,IAAIE,KAAK,GAAGX,MAAM,CAACC,UAAU,IAAIzE,UAAU,CAACmF,KAAK;IACjD,IAAIE,KAAK,GAAGb,MAAM,CAACC,UAAU,IAAIzE,UAAU,CAACqF,KAAK;IACjD,IAAI7D,KAAK,GAAGgD,MAAM,CAACC,UAAU,GAAGzE,UAAU,CAACmF,KAAK;IAChD,IAAI,CAAClE,WAAW,CAACqE,IAAI,CAAC;MACpBpE,QAAQ,EAAEA,QAAQ;MAClBC,KAAK,EAAEuD,IAAI;MACXtD,KAAK,EAAEyD,IAAI;MACXxD,KAAK,EAAE0D,IAAI;MACXzD,MAAM,EAAE2D,KAAK;MACb1D,MAAM,EAAE4D,KAAK;MACbR,MAAM,EAAEA,MAAM;MACdC,MAAM,EAAEA,MAAM;MACdE,MAAM,EAAEA,MAAM;MACdE,OAAO,EAAEA,OAAO;MAChBE,OAAO,EAAEA,OAAO;MAChBE,OAAO,EAAEA,OAAO;MAChB5D,KAAK,EAAEA;KACR,CAAC;EACJ;EAEO+D,cAAcA,CAACC,EAAU;IAC9BC,QAAQ,CAACC,cAAc,CAACF,EAAE,CAAC,EAAEG,SAAS,CAACC,MAAM,CAAC,eAAe,CAAC;IAC9DH,QAAQ,CAACC,cAAc,CAACF,EAAE,CAAC,EAAEG,SAAS,CAACE,GAAG,CAAC,gBAAgB,CAAC;IAC5DJ,QAAQ,CAACK,IAAI,CAACC,KAAK,CAACC,SAAS,GAAG,QAAQ;EAC1C;EAEOC,eAAeA,CAACT,EAAU;IAC/BC,QAAQ,CAACC,cAAc,CAACF,EAAE,CAAC,EAAEG,SAAS,CAACC,MAAM,CAAC,gBAAgB,CAAC;IAC/DH,QAAQ,CAACC,cAAc,CAACF,EAAE,CAAC,EAAEG,SAAS,CAACE,GAAG,CAAC,eAAe,CAAC;IAC3DJ,QAAQ,CAACK,IAAI,CAACC,KAAK,CAACC,SAAS,GAAG,QAAQ;EAC1C;EAEOE,oBAAoBA,CAACC,EAAO;IACjCA,EAAE,CAACR,SAAS,CAACC,MAAM,CAAC,eAAe,CAAC;IACpCO,EAAE,CAACR,SAAS,CAACE,GAAG,CAAC,gBAAgB,CAAC;IAClCJ,QAAQ,CAACK,IAAI,CAACC,KAAK,CAACC,SAAS,GAAG,QAAQ;EAC1C;EAEOI,qBAAqBA,CAACD,EAAO;IAClCA,EAAE,CAACR,SAAS,CAACC,MAAM,CAAC,gBAAgB,CAAC;IACrCO,EAAE,CAACR,SAAS,CAACE,GAAG,CAAC,eAAe,CAAC;IACjCJ,QAAQ,CAACK,IAAI,CAACC,KAAK,CAACC,SAAS,GAAG,QAAQ;EAC1C;EAEAK,YAAYA,CAACC,KAAW,EAAEC,KAAW;IACnC,IAAIC,IAAI,GAAG,CAACF,KAAK,CAACG,OAAO,EAAE,GAAGF,KAAK,CAACE,OAAO,EAAE,IAAI,IAAI;IACrD,IAAIC,MAAM,GAAGF,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACtC,IAAIG,KAAK,GAAGH,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACrC,IAAII,IAAI,GAAGJ,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IAChC,IAAIK,KAAK,GAAGL,IAAI,IAAI,EAAE,GAAG,EAAE,CAAC;IAC5B,IAAIM,OAAO,GAAGN,IAAI,GAAI,EAAG;IACzB,OAAO;MACLG,KAAK,EAAEI,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,KAAK,CAACN,KAAK,CAAC,CAAC;MAClCC,IAAI,EAAEG,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,KAAK,CAACL,IAAI,CAAC,CAAC;MAChCC,KAAK,EAAEE,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,KAAK,CAACJ,KAAK,CAAC,CAAC;MAClCC,OAAO,EAAEC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,KAAK,CAACH,OAAO,CAAC;KACtC;EACH;EAEOI,iBAAiBA,CAACC,aAAwC;IAC/D,EAAE,CAACC,OAAO,CAACC,IAAI,CAACF,aAAa,EAAGhB,EAAO,IAAI;MACzCA,EAAE,CAACJ,KAAK,CAACuB,OAAO,GAAG,MAAM;IAC3B,CAAC,CAAC;EACJ;EAEOC,iBAAiBA,CAACJ,aAAwC,EAAEG,OAAe;IAChF,EAAE,CAACF,OAAO,CAACC,IAAI,CAACF,aAAa,EAAGhB,EAAO,IAAI;MACzCA,EAAE,CAACJ,KAAK,CAACuB,OAAO,GAAGA,OAAO;IAC5B,CAAC,CAAC;EACJ;EAEOE,eAAeA,CAACC,OAAY;IACjC;IACA,IAAIC,aAAa,GAAGD,OAAO,CAACE,YAAY;IAExC;IACA,IAAIC,iBAAiB,GAAGH,OAAO,CAAC1B,KAAK,CAAC8B,UAAU;IAChDJ,OAAO,CAAC1B,KAAK,CAAC8B,UAAU,GAAG,EAAE;IAE7B;IACA;IACA;IACAC,qBAAqB,CAAC;MACpBL,OAAO,CAAC1B,KAAK,CAACgC,MAAM,GAAGL,aAAa,GAAG,IAAI;MAC3CD,OAAO,CAAC1B,KAAK,CAAC8B,UAAU,GAAGD,iBAAiB;MAE5C;MACA;MACAE,qBAAqB,CAAC;QACpBL,OAAO,CAAC1B,KAAK,CAACgC,MAAM,GAAG,CAAC,GAAG,IAAI;MACjC,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF;IACAN,OAAO,CAACO,YAAY,CAAC,gBAAgB,EAAE,MAAM,CAAC;EAChD;EAEOC,aAAaA,CAACR,OAAY;IAC/B;IACA,IAAIC,aAAa,GAAGD,OAAO,CAACE,YAAY;IAExC;IACAF,OAAO,CAAC1B,KAAK,CAACgC,MAAM,GAAGL,aAAa,GAAG,IAAI;IAE3C;IACAD,OAAO,CAACO,YAAY,CAAC,gBAAgB,EAAE,OAAO,CAAC;EACjD;EAGAE,aAAaA,CAACC,cAAmB,EAAEC,kBAAuB,EAAEC,OAAgB,EAAEC,IAAmC;IAC/G,IAAI9C,EAAE,GAAG2C,cAAc,CAACI,YAAY,CAAC,IAAI,CAAC;IAC1C,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAI,CAACF,IAAI,EAAE9C,EAAE,EAAE;MACbgD,OAAO,GAAGL,cAAc,CAACI,YAAY,CAAC,MAAM,CAAC,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;IAC9E,CAAC,MAAM;MACLC,OAAO,GAAGF,IAAI,EAAEG,IAAI;IACtB;IACA,IAAIC,OAAO,GAAGjD,QAAQ,CAACC,cAAc,CAACF,EAAE,CAAC;IACzC2C,cAAc,CAACH,YAAY,CAAC,MAAM,EAAEQ,OAAO,CAAC;IAC5C,IAAIF,IAAI,EAAE9C,EAAE,EAAE;MACZmD,aAAa,CAAC,IAAI,CAACC,EAAE,CAAC;IACxB;IACAC,YAAY,CAAC,IAAI,CAACC,OAAO,CAAC;IAE1B,IAAIN,OAAO,KAAK,MAAM,EAAE;MACtB,IAAI,CAACM,OAAO,GAAGC,UAAU,CAAC,MAAK;QAC7B,IAAIV,OAAO,EAAE;UACXF,cAAc,CAACpC,KAAK,CAACiD,QAAQ,GAAG,SAAS;QAC3C;MACF,CAAC,EAAE,GAAG,CAAC;MACPZ,kBAAkB,CAACrC,KAAK,CAACkD,SAAS,GAAG,gBAAgB;MACrD,IAAI,CAAChB,aAAa,CAACS,OAAO,CAAC;MAC3B,IAAIJ,IAAI,EAAE9C,EAAE,EAAE;QACZ,IAAI,CAACoD,EAAE,GAAGpE,MAAM,CAAC0E,WAAW,CAAC,MAAK;UAChC,IAAIzD,QAAQ,CAACC,cAAc,CAAC,gBAAgB,GAAG4C,IAAI,EAAE9C,EAAE,CAAC,IAAIC,QAAQ,CAACC,cAAc,CAAC,eAAe,GAAG4C,IAAI,EAAE9C,EAAE,CAAC,EAC7GC,QAAQ,CAACC,cAAc,CAAC,gBAAgB,GAAG4C,IAAI,EAAE9C,EAAE,CAAE,CAACO,KAAK,CAACgC,MAAM,GAAGtC,QAAQ,CAACC,cAAc,CAAC,eAAe,GAAG4C,IAAI,EAAE9C,EAAE,CAAE,CAAC2D,YAAY,GAAG,EAAE,GAAG,IAAI;QACtJ,CAAC,EAAE,EAAE,CAAC;MACR;IACF,CAAC,MAAM;MACLhB,cAAc,CAACpC,KAAK,CAACiD,QAAQ,GAAG,QAAQ;MACxCZ,kBAAkB,CAACrC,KAAK,CAACkD,SAAS,GAAG,cAAc;MACnD,IAAI,CAACzB,eAAe,CAACkB,OAAO,CAAC;MAC7B,IAAIJ,IAAI,EAAE9C,EAAE,EAAE;QACZmD,aAAa,CAAC,IAAI,CAACC,EAAE,CAAC;MACxB;IAEF;EACF;EAEAQ,YAAYA,CAACC,GAAW;IACtB,OAAOtC,IAAI,CAACuC,KAAK,CAACvC,IAAI,CAACwC,MAAM,EAAE,GAAGF,GAAG,CAAC;EACxC;EAEAG,gBAAgBA,CAACC,GAAW;IAC1B,IAAI,CAAC5I,MAAM,CAAC6I,aAAa,CAAC,GAAG,EAAE;MAAEC,kBAAkB,EAAE;IAAI,CAAE,CAAC,CAACC,IAAI,CAAC,MAAK;MACrE,IAAI,CAAC/I,MAAM,CAACgJ,QAAQ,CAAC,CAACJ,GAAG,CAAC,CAAC;IAC7B,CAAC,CAAC;EACJ;EAEAK,aAAaA,CAACC,QAAgB,EAAEC,QAAgB,EAAEvC,OAAe;IAC/D,IAAIhC,QAAQ,CAACC,cAAc,CAAC+B,OAAO,CAAC,EAAE;MACpC,IAAIwC,MAAM;MACV,IAAIC,KAAK,GAAG,CAAC;MACb,IAAIC,MAAM,GAAG,CAAC;MACd,IAAI1C,OAAO,KAAK,oBAAoB,EAAE;QACpCyC,KAAK,GAAG,GAAG;QACXC,MAAM,GAAG,GAAG;MACd;MACA,IAAI1C,OAAO,KAAK,mBAAmB,EAAE;QACnCyC,KAAK,GAAG,IAAI;QACZC,MAAM,GAAG,GAAG;MACd;MACA,IAAI1C,OAAO,KAAK,gBAAgB,EAAE;QAChCyC,KAAK,GAAG,IAAI;QACZC,MAAM,GAAG,GAAG;MACd;MACA,IAAI1C,OAAO,KAAK,eAAe,EAAE;QAC/ByC,KAAK,GAAG,IAAI;QACZC,MAAM,GAAG,GAAG;MACd;MACA,IAAI1C,OAAO,KAAK,mBAAmB,EAAE;QACnCyC,KAAK,GAAG,CAAC;QACTC,MAAM,GAAG,GAAG;MACd;MACA,IAAIH,QAAQ,GAAGD,QAAQ,EAAE;QACvBE,MAAM,GAAGxE,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAE,CAAC0E,WAAW,GAAG3E,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAE,CAAC0E,WAAW,GAAGD,MAAM,GAAG3F,MAAM,CAAC6F,OAAO,GAAGH,KAAK;MACtJ,CAAC,MAAM,IAAIF,QAAQ,GAAGD,QAAQ,EAAE;QAC9BE,MAAM,GAAGxE,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAE,CAAC0E,WAAW,GAAG3E,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAE,CAAC0E,WAAW,GAAGD,MAAM,GAAG3F,MAAM,CAAC6F,OAAO,GAAGH,KAAK;MACtJ;MACA;MACAzE,QAAQ,CAACC,cAAc,CAAC+B,OAAO,CAAE,CAAC1B,KAAK,CAACuE,KAAK,GAAGL,MAAM,GAAG,IAAI;MAC7DxE,QAAQ,CAACC,cAAc,CAAC+B,OAAO,CAAE,CAAC1B,KAAK,CAACgC,MAAM,GAAGkC,MAAM,GAAG,IAAI;MAC9D;IACF;EACF;EAEAM,aAAaA,CAACC,SAAkB,EAAEhF,EAAU;IAC1C,IAAIgF,SAAS,EAAE;MACb,IAAI,CAACjF,cAAc,CAAC,SAAS,CAAC;MAC9B,IAAI,CAACA,cAAc,CAACC,EAAE,CAAC;IACzB,CAAC,MAAM;MACL,IAAI,CAACS,eAAe,CAAC,SAAS,CAAC;MAC/B,IAAI,CAACA,eAAe,CAACT,EAAE,CAAC;IAC1B;EACF;EAEAiF,mBAAmBA,CAACD,SAAkB,EAAErE,EAAU;IAChD,IAAIqE,SAAS,EAAE;MACb,IAAI,CAACjF,cAAc,CAAC,SAAS,CAAC;MAC9B,IAAI,CAACW,oBAAoB,CAACC,EAAE,CAAC;IAC/B,CAAC,MAAM;MACL,IAAI,CAACF,eAAe,CAAC,SAAS,CAAC;MAC/B,IAAI,CAACG,qBAAqB,CAACD,EAAE,CAAC;IAChC;EACF;EAEAuE,WAAWA,CAACC,IAAU;IACpB,OAAO,IAAIC,IAAI,CAACD,IAAI,CAACE,QAAQ,EAAE,CAACC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAACC,WAAW,EAAE;EACzE;EAEAC,WAAWA,CAACC,GAAW;IACrB,IAAIC,CAAC,GAAGD,GAAG,CAACE,WAAW,CAAC,GAAG,CAAC;IAC5B,IAAIC,EAAE,GAAGH,GAAG,CAACE,WAAW,CAAC,GAAG,CAAC;IAC7B,IAAIE,MAAM,GAAGJ,GAAG,CAACK,SAAS,CAACJ,CAAC,GAAG,CAAC,EAAEE,EAAE,CAAC;IACrC,OAAOC,MAAM;EACf;EAEAE,kBAAkBA,CAACC,WAAmB;IACpC,IAAIA,WAAW,IAAIA,WAAW,CAACL,WAAW,CAAC,IAAI,CAAC,EAAE;MAChD,IAAID,CAAC,GAAGM,WAAW,CAACL,WAAW,CAAC,IAAI,CAAC;MACrC,IAAIE,MAAM,GAAGG,WAAW,CAACF,SAAS,CAACJ,CAAC,GAAG,CAAC,CAAC;MACzC,OAAOG,MAAM;IACf;IACA,OAAOG,WAAW;EACpB;EAEAC,YAAYA,CAACR,GAAW;IACtB,IAAIC,CAAC,GAAGD,GAAG,CAACE,WAAW,CAAC,IAAI,CAAC;IAC7B,IAAIC,EAAE,GAAGH,GAAG,CAACE,WAAW,CAAC,GAAG,CAAC;IAC7B,IAAIE,MAAM,GAAGJ,GAAG,CAACK,SAAS,CAACJ,CAAC,GAAG,CAAC,EAAEE,EAAE,CAAC;IACrC,OAAOC,MAAM;EACf;EAEAK,qBAAqBA,CAACT,GAAW;IAC/B,IAAIC,CAAC,GAAGD,GAAG,CAACE,WAAW,CAAC,IAAI,CAAC;IAC7B,IAAIC,EAAE,GAAGH,GAAG,CAACE,WAAW,CAAC,GAAG,CAAC;IAC7B,IAAIE,MAAM,GAAGJ,GAAG,CAACK,SAAS,CAACJ,CAAC,GAAG,CAAC,EAAEE,EAAE,CAAC;IACrC,MAAMO,eAAe,GAAGV,GAAG,CAACW,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IAC/C,OAAOpL,OAAO,GAAGmL,eAAe;EAClC;EAEAE,uBAAuBA,CAACC,QAAgB;IACtC,MAAMC,QAAQ,GAAGD,QAAQ,CAAChB,KAAK,CAAC,IAAI,CAAC,CAACkB,GAAG,EAAE;IAC3C,OAAOD,QAAQ;EACjB;EAEAE,gBAAgBA,CAACC,GAAW;IAC1B,IAAIA,GAAG,IAAI,CAAC,EAAE,OAAO,QAAQ;IAC7B,IAAIA,GAAG,IAAI,CAAC,EAAE,OAAO,QAAQ;IAC7B,IAAIA,GAAG,IAAI,CAAC,EAAE,OAAO,SAAS;IAC9B,IAAIA,GAAG,IAAI,CAAC,EAAE,OAAO,WAAW;IAChC,IAAIA,GAAG,IAAI,CAAC,EAAE,OAAO,UAAU;IAC/B,IAAIA,GAAG,IAAI,CAAC,EAAE,OAAO,QAAQ;IAC7B,IAAIA,GAAG,IAAI,CAAC,EAAE,OAAO,UAAU;IAC/B,OAAO,EAAE;EACX;EAEAC,kBAAkBA,CAACD,GAAW;IAC5B,IAAIA,GAAG,IAAI,CAAC,EAAE,OAAO,SAAS;IAC9B,IAAIA,GAAG,IAAI,CAAC,EAAE,OAAO,UAAU;IAC/B,IAAIA,GAAG,IAAI,CAAC,EAAE,OAAO,QAAQ;IAC7B,IAAIA,GAAG,IAAI,CAAC,EAAE,OAAO,OAAO;IAC5B,IAAIA,GAAG,IAAI,CAAC,EAAE,OAAO,KAAK;IAC1B,IAAIA,GAAG,IAAI,CAAC,EAAE,OAAO,MAAM;IAC3B,IAAIA,GAAG,IAAI,CAAC,EAAE,OAAO,MAAM;IAC3B,IAAIA,GAAG,IAAI,CAAC,EAAE,OAAO,QAAQ;IAC7B,IAAIA,GAAG,IAAI,CAAC,EAAE,OAAO,WAAW;IAChC,IAAIA,GAAG,IAAI,CAAC,EAAE,OAAO,SAAS;IAC9B,IAAIA,GAAG,IAAI,EAAE,EAAE,OAAO,UAAU;IAChC,IAAIA,GAAG,IAAI,EAAE,EAAE,OAAO,UAAU;IAChC,OAAO,EAAE;EACX;EACAE,YAAYA,CAAC3C,GAAW;IACtB,IAAI,CAAC3I,IAAI,CAACuL,GAAG,CAAC5C,GAAG,EAAE;MAAE6C,YAAY,EAAE;IAAM,CAAE,CAAC,CACzCC,SAAS,CAACC,IAAI,IAAG;MAChBtM,MAAM,CAACsM,IAAI,EAAE,IAAI,CAACjB,kBAAkB,CAAC9B,GAAG,CAAC,CAAC;IAC5C,CAAC,CAAC;EACN;EAEAgD,eAAeA,CAACC,KAAa;IAC3B,IAAIA,KAAK,IAAI,CAAC,EAAE;MACd,OAAO,SAAS;IAClB;IACA,IAAIA,KAAK,IAAI,CAAC,EAAE;MACd,OAAO,SAAS;IAClB;IACA,IAAIA,KAAK,IAAI,CAAC,EAAE;MACd,OAAO,SAAS;IAClB;IACA,IAAIA,KAAK,IAAI,CAAC,EAAE;MACd,OAAO,SAAS;IAClB;IACA,IAAIA,KAAK,IAAI,CAAC,EAAE;MACd,OAAO,SAAS;IAClB;IACA,IAAIA,KAAK,IAAI,CAAC,EAAE;MACd,OAAO,SAAS;IAClB;IACA,IAAIA,KAAK,IAAI,CAAC,EAAE;MACd,OAAO,SAAS;IAClB;IACA,IAAIA,KAAK,IAAI,CAAC,EAAE;MACd,OAAO,SAAS;IAClB;IACA,OAAO,OAAO;EAChB;EAEAC,aAAaA,CAACC,SAAiB;IAC7B,IAAI,CAAC9L,IAAI,CAACuL,GAAG,CAACO,SAAS,CAAC,CACrBL,SAAS,CAAEM,GAAQ,IAAI;MACtB,IAAIC,CAAC,GAAGrH,QAAQ,CAACsH,aAAa,CAAC,GAAG,CAAC;MACnCD,CAAC,CAACE,IAAI,GAAGC,GAAG,CAACC,eAAe,CAACL,GAAG,CAACL,IAAI,EAAE,CAAC;MACxCM,CAAC,CAACK,QAAQ,GAAGP,SAAS;MACtB;MACAE,CAAC,CAACM,KAAK,EAAE;IACX,CAAC,CAAC;EACN;EAEAC,qBAAqBA,CAACT,SAAiB;IACrC,IAAI,CAACnL,gBAAgB,CAAC6D,IAAI,CAACsH,SAAS,CAAC;EACvC;EAEOU,eAAeA,CAACC,GAAO;IAC5B,IAAIA,GAAG,KAAK,IAAI,EAAE;MAChB,OAAO,IAAI;IACb;IACA,MAAMC,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACH,GAAG,CAAC,CAACI,KAAK,CAACC,KAAK,IAAG;MACjD,IAAIA,KAAK,KAAK,IAAI,EAAE;QAClB,OAAO,IAAI;MACb;MAEA,OAAO,KAAK;IACd,CAAC,CAAC;IAEF,OAAOJ,SAAS;EAClB;EAEAK,UAAUA,CAACC,CAAO,EAAEC,SAAiB;IACnC,MAAMC,IAAI,GAAGF,CAAC,CAACG,WAAW,EAAE,CAACpD,QAAQ,EAAE;IACvC,MAAMqD,EAAE,GAAG,CAACJ,CAAC,CAACK,QAAQ,EAAE,GAAG,CAAC,EAAEtD,QAAQ,EAAE;IACxC,MAAMuD,EAAE,GAAGN,CAAC,CAACO,OAAO,EAAE,CAACxD,QAAQ,EAAE;IAEjC,OAAQmD,IAAI,GAAGD,SAAS,IAAIG,EAAE,CAAC,CAAC,CAAC,GAAGA,EAAE,GAAG,GAAG,GAAGA,EAAE,CAAC,CAAC,CAAC,CAAC,GAAGH,SAAS,IAAIK,EAAE,CAAC,CAAC,CAAC,GAAGA,EAAE,GAAG,GAAG,GAAGA,EAAE,CAAC,CAAC,CAAC,CAAC;EAChG;EAEAE,eAAeA,CAACC,cAAsB;IACpC,MAAM5D,IAAI,GAAG,IAAIC,IAAI,CAAC2D,cAAc,CAAC;IACrC,MAAMC,aAAa,GAAG7D,IAAI,CAAC8D,kBAAkB,CAAC,OAAO,EAAE;MACrDvC,GAAG,EAAE,SAAS;MACdwC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;KACP,CAAC;IAEF,OAAOH,aAAa;EACtB;EAEA;;;;;EAKAI,iBAAiBA,CAACC,UAAkB;IAClC,IAAIlE,IAAI,GAAG,IAAIC,IAAI,CAACiE,UAAU,CAAC;IAC/B,IAAI3C,GAAG,GAAGvB,IAAI,CAAC0D,OAAO,EAAE,CAACxD,QAAQ,EAAE,CAACiE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IACpD,IAAIJ,KAAK,GAAG,CAAC/D,IAAI,CAACwD,QAAQ,EAAE,GAAG,CAAC,EAAEtD,QAAQ,EAAE,CAACiE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC7D,IAAIH,IAAI,GAAGhE,IAAI,CAACsD,WAAW,EAAE;IAE7B,OAAO,GAAG/B,GAAG,IAAIwC,KAAK,IAAIC,IAAI,EAAE;EAClC;EAEA;;;;;EAKAI,wCAAwCA,CAACF,UAAkB;IACzD,IAAI,CAAC3C,GAAG,EAAEwC,KAAK,EAAEC,IAAI,CAAC,GAAGE,UAAU,CAAC/D,KAAK,CAAC,GAAG,CAAC;IAC9C,IAAIkE,aAAa,GAAG,GAAGL,IAAI,IAAID,KAAK,CAACI,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI5C,GAAG,CAAC4C,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,eAAe;IAC5F,OAAOE,aAAa;EACtB;EAEA;;;;;EAKAC,gBAAgBA,CAACV,cAAsB;IACrC,MAAM5D,IAAI,GAAG,IAAIC,IAAI,CAAC2D,cAAc,CAAC;IACrC,MAAMC,aAAa,GAAG7D,IAAI,CAACI,WAAW,EAAE;IAExC,OAAO,IAAI,CAACmE,sBAAsB,CAACV,aAAa,CAAC;EACnD;EAEA;;;;;EAKAU,sBAAsBA,CAACL,UAAkB;IACvC,MAAMlE,IAAI,GAAG,IAAIC,IAAI,CAACiE,UAAU,CAAC;IAEjClE,IAAI,CAACwE,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BxE,IAAI,CAACyE,OAAO,CAACzE,IAAI,CAAC0D,OAAO,EAAE,CAAC;IAE5B,MAAMW,aAAa,GAAGrE,IAAI,CAACI,WAAW,EAAE,CAACa,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;IAEzD,OAAOoD,aAAa;EACtB;EAEA;;;;;;EAMAK,aAAaA,CAACR,UAAkB;IAC9B,MAAMlE,IAAI,GAAG,IAAIC,IAAI,CAACiE,UAAU,CAAC;IACjC,MAAMS,OAAO,GAAG;MAAEC,MAAM,EAAE,KAAK;MAAEC,IAAI,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAS,CAAE;IACrE,OAAO9E,IAAI,CAAC+E,kBAAkB,CAAC,OAAO,EAAEJ,OAAc,CAAC;EACzD;EAEA;;;;;;EAMAK,gBAAgBA,CAACC,eAAuB;IACtC,IAAIC,YAAY,GAAG,EAAE;IAErB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,eAAe,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MAC/C,MAAME,YAAY,GAAGJ,eAAe,CAACE,CAAC,CAAC;MACvC,MAAMG,aAAa,GAAGL,eAAe,CAACE,CAAC,CAAC,CAACI,WAAW,EAAE;MAEtD,IAAIF,YAAY,KAAKC,aAAa,EAAE;QAClCJ,YAAY,IAAI,GAAG,GAAGG,YAAY;MACpC,CAAC,MAAM;QACLH,YAAY,IAAIG,YAAY;MAC9B;IACF;IAEA,OAAOH,YAAY;EACrB;EAEA;;;;;;;EAOAM,YAAYA,CAACC,IAAsB,EAAEC,KAAa,EAAEC,SAAkB;IACpE,OAASF,IAAI,CAAC/D,GAAG,CAACgE,KAAK,CAAC,EAAEE,OAAO,IAAIH,IAAI,CAAC/D,GAAG,CAACgE,KAAK,CAAC,EAAEG,OAAO,IAAMF,SAAS,IAAIF,IAAI,CAAC/D,GAAG,CAACgE,KAAK,CAAC,EAAEE,OAAQ;EAC3G;EAEAE,gBAAgBA,CAACC,WAAmB,EAAEC,QAAQ,GAAG,IAAI;IACnD,IAAI,CAACD,WAAW,EAAE;MAChB;IACF;IAEA,MAAME,iBAAiB,GAAGzQ,IAAI,CAAC0D,SAAS,CAACgN,MAAM,CAACC,QAAQ,IAAIA,QAAQ,CAACC,IAAI,KAAKL,WAAW,CAAC;IAC1F,IAAIE,iBAAiB,CAACb,MAAM,KAAK,CAAC,EAAE;MAClC;IACF;IAEA,MAAMpF,IAAI,GAAG,IAAIC,IAAI,EAAE;IACvB,MAAM0E,OAAO,GAAG;MACd0B,QAAQ,EAAEJ,iBAAiB,CAAC,CAAC,CAAC,CAACK,GAAG,CAAC,CAAC,CAAC;MACrCzB,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE,SAAS;MACjBF,MAAM,EAAEoB,QAAQ,CAAC;KAClB;IAED;IACA,MAAMO,WAAW,GAAGA,CAAA,KAAK;MACvB,MAAMC,GAAG,GAAG,IAAIvG,IAAI,EAAE;MACtB,MAAMwG,QAAQ,GAAG,IAAIxG,IAAI,CAACuG,GAAG,CAAClD,WAAW,EAAE,EAAE,CAAC,EAAE,EAAE,GAAI,CAAC,CAAC,GAAGkD,GAAG,CAAClD,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC,IAAI,CAAE,EAAE,CAAC,CAAC;MAC9F,MAAMoD,MAAM,GAAG,IAAIzG,IAAI,CAACuG,GAAG,CAAClD,WAAW,EAAE,EAAE,CAAC,EAAE,EAAE,GAAI,CAAC,CAAC,GAAGkD,GAAG,CAAClD,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC,IAAI,CAAE,EAAE,CAAC,CAAC;MAC5F,OAAOkD,GAAG,CAAC1K,OAAO,EAAE,IAAI2K,QAAQ,CAAC3K,OAAO,EAAE,IAAI0K,GAAG,CAAC1K,OAAO,EAAE,GAAG4K,MAAM,CAAC5K,OAAO,EAAE;IAChF,CAAC;IAED;IACA,IAAIyK,WAAW,EAAE,EAAE;MACjBvG,IAAI,CAAC2G,QAAQ,CAAC3G,IAAI,CAAC4G,QAAQ,EAAE,GAAG,CAAC,CAAC;IACpC;IAEA,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAACC,cAAc,CAAC,OAAO,EAAEpC,OAAc,CAAC,CAACqC,MAAM,CAAChH,IAAI,CAAC;IAC/E,OAAO6G,SAAS;EAClB;EAEAI,gBAAgBA,CAACnK,OAAmB,EAAEoK,eAAuB,EAAEC,SAAiB,EAAEC,UAAwB;IACxG,IAAIC,IAAI,GAAGF,SAAS;IACpB,IAAIxH,KAAK,GAAG,GAAG;IACf0H,IAAI,GAAGA,IAAI,GAAG,GAAG;IACjB1H,KAAK,GAAG,GAAG,GAAG0H,IAAI;IAClB,IAAID,UAAU,EAAE;MAEdA,UAAU,CAAChM,KAAK,CAAC8L,eAAe,GAAGA,eAAe;MAClDE,UAAU,CAAChM,KAAK,CAACkD,SAAS,GAAG,QAAQ,GAAG+I,IAAI,GAAG,GAAG;MAClDD,UAAU,CAAChM,KAAK,CAACuE,KAAK,GAAGA,KAAK,GAAG,GAAG;MACpC;IACF,CAAC,MAAM;MAEL7C,OAAO,CAACwK,aAAa,CAAClM,KAAK,CAAC8L,eAAe,GAAGA,eAAe;MAC7DpK,OAAO,CAACwK,aAAa,CAAClM,KAAK,CAACkD,SAAS,GAAG,QAAQ,GAAG+I,IAAI,GAAG,GAAG;MAC7DvK,OAAO,CAACwK,aAAa,CAAClM,KAAK,CAACuE,KAAK,GAAGA,KAAK,GAAG,GAAG;MAC/C7C,OAAO,CAACwK,aAAa,CAAClM,KAAK,CAACgC,MAAM,GAAGuC,KAAK,GAAG,IAAI;IACnD;EAEF;EACA4H,iBAAiBA,CAACzK,OAAmB,EAAEsK,UAAwB;IAC7D,IAAIC,IAAI,GAAG,CAAC;IACZ,IAAI1H,KAAK,GAAG,GAAG;IACf0H,IAAI,GAAGA,IAAI;IACX1H,KAAK,GAAG,GAAG,GAAG0H,IAAI;IAClB,IAAID,UAAU,EAAE;MAEdA,UAAU,CAAChM,KAAK,CAAC8L,eAAe,GAAG,OAAO;MAC1CE,UAAU,CAAChM,KAAK,CAACkD,SAAS,GAAG,MAAM;MACnC8I,UAAU,CAAChM,KAAK,CAACuE,KAAK,GAAGA,KAAK,GAAG,GAAG;IACtC,CAAC,MAAM;MACL7C,OAAO,CAACwK,aAAa,CAAClM,KAAK,CAAC8L,eAAe,GAAG,OAAO;MACrDpK,OAAO,CAACwK,aAAa,CAAClM,KAAK,CAACkD,SAAS,GAAG,MAAM;MAC9CxB,OAAO,CAACwK,aAAa,CAAClM,KAAK,CAACuE,KAAK,GAAGA,KAAK,GAAG,GAAG;IACjD;EAEF;EAEA;;;;;;EAMA6H,gBAAgBA,CAACC,SAAiB;IAChC,MAAMxO,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAMS,UAAU,GAAG,IAAI,CAACD,aAAa,EAAE;IAEvC,MAAMiO,iBAAiB,GAAGzO,SAAS,CAACiN,MAAM,CAAEyB,OAAO,IAAI;MACrD,MAAMC,WAAW,GAAGlO,UAAU,CAACmO,IAAI,CAAEC,EAAE,IAAKA,EAAE,CAACnQ,IAAI,KAAK8P,SAAS,CAAC,EAAEM,GAAG;MACvE,OAAOJ,OAAO,CAAChQ,IAAI,KAAKiQ,WAAW;IACrC,CAAC,CAAC;IAEF,MAAMI,YAAY,GAAGN,iBAAiB,CAAC,CAAC,CAAC,EAAEO,KAAK;IAEhD,OAAOD,YAAY;EACrB;EAEAE,sBAAsBA,CAACC,WAAmB;IACxC,MAAMR,OAAO,GAAG,IAAI,CAACpO,YAAY,EAAE,CAACsO,IAAI,CAACO,CAAC,IAAIA,CAAC,CAAC1Q,IAAI,KAAKyQ,WAAW,CAAC;IACrE,OAAOR,OAAO,GAAGA,OAAO,CAACM,KAAK,GAAG,EAAE;EACrC;EAEAI,aAAaA,CAACC,MAAW;IACvB,OAAQA,MAAM,CAACC,SAAS,KAAK,UAAU,IAAMD,MAAM,CAACC,SAAS,KAAK,WAAY,GAAG,EAAE,GAAID,MAAM,CAACC,SAAS,GAAG,GAAG,GAAGD,MAAM,CAACE,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAI;EAClJ;EAEAC,gBAAgBA,CAACJ,MAAW;IAC1B,IAAIA,MAAM,CAACE,QAAQ,KAAK,EAAE,EAAE;MAC1B,OAAOF,MAAM,CAACC,SAAS;IACzB,CAAC,MAAM;MACL,OAAOD,MAAM,CAACC,SAAS,GAAG,GAAG,GAAGD,MAAM,CAACE,QAAQ,CAACC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG;IACjE;EACF;EAEA;;;;;;;;EAQAE,iBAAiBA,CAACL,MAAW;IAC3B,IAAIA,MAAM,CAACC,SAAS,KAAK,EAAE,IAAID,MAAM,CAACE,QAAQ,KAAK,EAAE,EAAE;MACrD,OAAO,EAAE;IACX,CAAC,MAAM,IAAIF,MAAM,CAACC,SAAS,KAAK,EAAE,EAAE;MAClC,OAAOD,MAAM,CAACE,QAAQ;IACxB,CAAC,MAAM,IAAIF,MAAM,CAACE,QAAQ,KAAK,EAAE,EAAE;MACjC,OAAOF,MAAM,CAACC,SAAS;IACzB,CAAC,MAAM;MACL,OAAOD,MAAM,CAACC,SAAS,GAAG,GAAG,GAAGD,MAAM,CAACE,QAAQ;IACjD;EACF;EAEAI,UAAUA,CAACtI,GAAW;IACpB,IAAI,CAACA,GAAG,EAAE,OAAOA,GAAG;IACpB,OAAOA,GAAG,CAACmI,MAAM,CAAC,CAAC,CAAC,CAAClD,WAAW,EAAE,GAAGjF,GAAG,CAACuI,KAAK,CAAC,CAAC,CAAC;EACnD;EAEA;;;;;EAKAC,YAAYA,CAACC,GAAW;IACtB,MAAMC,WAAW,GAAG,IAAI/I,IAAI,CAAC8I,GAAG,CAAC;IACjC,MAAME,KAAK,GAAGhJ,IAAI,CAACuG,GAAG,EAAE,GAAGwC,WAAW,CAAClN,OAAO,EAAE;IAChD,MAAMoN,OAAO,GAAG,IAAIjJ,IAAI,CAACgJ,KAAK,CAAC;IAC/B,MAAME,GAAG,GAAG/M,IAAI,CAACC,GAAG,CAAC6M,OAAO,CAACE,cAAc,EAAE,GAAG,IAAI,CAAC;IAErD,OAAOD,GAAG;EACZ;EAEA;;;;EAIAE,oBAAoBA,CAACC,KAAY;IAC/B,MAAMC,UAAU,GAAGD,KAAK,CAACE,MAA0B;IACnDD,UAAU,CAACE,GAAG,GAAG,uCAAuC;EAC1D;EAEAC,qBAAqBA,CAACvN,OAAe;IACnC,OAAOA,OAAO,GAAG,EAAE;EACrB;EAEAwN,+BAA+BA,CAACzN,KAAa,EAAE0N,UAAU,GAAG,KAAK;IAC/D,MAAMC,WAAW,GAAG3N,KAAK,GAAG,CAAC;IAC7B,MAAM4N,UAAU,GAAG1N,IAAI,CAACuC,KAAK,CAACzC,KAAK,CAAC;IACpC,IAAIC,OAAO,GAAG2N,UAAU,GAAG,EAAE;IAE7B,IAAID,WAAW,GAAG,CAAC,EAAE;MACnB1N,OAAO,IAAI0N,WAAW,GAAG,EAAE;IAC7B;IAEA;IACA,IAAIA,WAAW,GAAG,IAAI,KAAK,CAAC,EAAE;MAC5B1N,OAAO,IAAI,CAAC0N,WAAW,GAAG,EAAE,GAAGzN,IAAI,CAACuC,KAAK,CAACkL,WAAW,GAAG,EAAE,CAAC,IAAI,EAAE;IACnE;IAEA;IACA,IAAI1N,OAAO,KAAK,CAAC,EAAE;MACjB,OAAO,SAAS;IAClB,CAAC,MAAM,IAAIA,OAAO,GAAG,EAAE,EAAE;MACvB;MACA,OAAO,GAAGA,OAAO,GAAG,CAAC,GAAGA,OAAO,GAAG,EAAE,GAAGyN,UAAU,IAAIzN,OAAO,GAAG,CAAC,GAAG,UAAU,GAAG,GAAG,EAAE;IACvF,CAAC,MAAM;MACL,MAAMD,KAAK,GAAGE,IAAI,CAACuC,KAAK,CAACxC,OAAO,GAAG,EAAE,CAAC;MACtC,MAAM4N,YAAY,GAAGH,UAAU,GAAIzN,OAAO,KAAK,EAAE,GAAG,SAAS,GAAG,OAAO,GAAI,GAAG;MAC9E,MAAM6N,UAAU,GAAGJ,UAAU,GAAI1N,KAAK,KAAK,CAAC,GAAG,OAAO,GAAG,QAAQ,GAAI,GAAG;MAExE;MACA,IAAIA,KAAK,KAAK,CAAC,EAAE;QACf,OAAO,GAAGC,OAAO,GAAG,CAAC,GAAGA,OAAO,GAAG,EAAE,GAAGyN,UAAU,IAAIzN,OAAO,GAAG,CAAC,GAAG4N,YAAY,GAAG,EAAE,EAAE;MACxF,CAAC,MAAM;QACL,OAAO,GAAG7N,KAAK,GAAG8N,UAAU,GAAG7N,OAAO,GAAG,EAAE,GAAG,CAAC,GAAG,IAAIA,OAAO,GAAG,EAAE,GAAGyN,UAAU,GAAGG,YAAY,GAAG,GAAG,EAAE,GAAG,EAAE,EAAE;MAC/G;IACF;EACF;EAEAE,+BAA+BA,CAACC,WAAiB,EAAEC,gBAAwB;IACzE,MAAMC,sBAAsB,GAAG,GAAGF,WAAW,CAACG,YAAY,EAAE,IAAIF,gBAAgB,EAAE;IAClF,MAAMtG,aAAa,GAAGpO,MAAM,CAAC6Q,GAAG,CAAC8D,sBAAsB,EAAE,uBAAuB,CAAC,CAAChK,WAAW,EAAE;IAC/F,OAAOyD,aAAa;EACtB;EAEAyG,oBAAoBA,CAACC,KAAa,EAAEC,GAAW;IAC7C,MAAMxD,MAAM,GAAG,SAAS,CAAC,CAAC;IAC1B,MAAMyD,SAAS,GAAGhV,MAAM,CAAC8U,KAAK,EAAEvD,MAAM,CAAC;IACvC,MAAM0D,OAAO,GAAGjV,MAAM,CAAC+U,GAAG,EAAExD,MAAM,CAAC;IAEnC,MAAM2D,kBAAkB,GAAGD,OAAO,CAAC7O,IAAI,CAAC4O,SAAS,CAAC;IAClD,MAAMG,aAAa,GAAGxO,IAAI,CAACE,KAAK,CAACqO,kBAAkB,GAAG,IAAI,GAAG,EAAE,CAAC;IAEhE,OAAOC,aAAa;EACtB;EAEAC,uBAAuBA,CAACjH,cAAsB;IAC5C,OAAOnO,MAAM,CAAC6Q,GAAG,CAAC1C,cAAc,CAAC,CAACoD,MAAM,CAAC,4BAA4B,CAAC;EACxE;EAEA8D,wBAAwBA,CAAClH,cAAsB;IAC7C,OAAOnO,MAAM,CAACsV,SAAS,CAACnH,cAAc,CAAC,CAACoD,MAAM,CAAC,4BAA4B,CAAC;EAC9E;EAEA;;;;;;;;EAQAgE,uBAAuBA,CAACC,SAAoB,EAAEC,IAAc;IAC1D,MAAMC,gBAAgB,GAAqB;MACzCC,KAAK,EAAE;QAAEC,SAAS,EAAEJ;MAAS;KAC9B;IACD,IAAIA,SAAS,CAACK,IAAI,KAAK,OAAO,EAAE;MAC9B,IAAIJ,IAAI,KAAKvV,QAAQ,CAAC4V,OAAO,EAAE;QAC7B,IAAI,CAACrV,MAAM,CAACgJ,QAAQ,CAAC,CAAC,+BAA+B,EAAE+L,SAAS,CAACpQ,EAAE,EAAE,SAAS,CAAC,EAAEsQ,gBAAgB,CAAC;MACpG,CAAC,MAAM;QACL;QACA,IAAI,CAACjV,MAAM,CAACgJ,QAAQ,CAAC,CAAC,8BAA8B,EAAE+L,SAAS,CAACpQ,EAAE,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE;UACtFuQ,KAAK,EAAE;YAAEC,SAAS,EAAEJ;UAAS;SAC9B,CAAC;MACJ;IAEF,CAAC,MAAM;MACL,IAAI,CAAC/U,MAAM,CAACgJ,QAAQ,CAAC,CAAC,+BAA+B,EAAE+L,SAAS,CAACpQ,EAAE,EAAE,SAAS,CAAC,EAAEsQ,gBAAgB,CAAC;IACpG;EACF;EAEA;;;;;;EAMAK,oBAAoBA,CAACP,SAAoB,EAAEC,IAAc;IACvD,IAAIzS,IAAI,GAAa,CAAC,+BAA+B,EAAEwS,SAAS,CAACpQ,EAAE,EAAE,SAAS,CAAC;IAC/E,IAAIoQ,SAAS,CAACK,IAAI,CAACG,WAAW,EAAE,KAAKtW,aAAa,CAACuW,KAAK,CAACD,WAAW,EAAE,IAAIP,IAAI,KAAKvV,QAAQ,CAACgW,OAAO,EAAE;MACnGlT,IAAI,GAAG,CAAC,+BAA+B,EAAEwS,SAAS,CAACpQ,EAAE,EAAE,MAAM,EAAE,SAAS,CAAC;IAC3E;IACA,OAAOpC,IAAI;EACb;EAEA;;;;;;;;;;EAUOmT,uBAAuBA,CAACC,aAAkB,EAAEC,SAAc,EAAEnM,KAAU,EAAEoM,UAAgB,EAAEC,qBAA2B,EAC1HC,aAAqC,EAAE7O,MAAe;IACtD,IAAI,CAAChE,IAAI,CAACqD,OAAO,CAACyP,GAAG,IAAIA,GAAG,CAACC,KAAK,EAAE,CAAC;IACrC,IAAI,CAAC/S,IAAI,GAAG,EAAE;IACd,IAAIgT,OAAO,GAAG,KAAK;IACnB,IAAIC,OAAO,GAAG,KAAK;IACnB,IAAIL,qBAAqB,EAAE;MACzB,IAAIlJ,MAAM,CAACwJ,IAAI,CAACN,qBAAqB,CAAC,CAAC5G,MAAM,GAAG,CAAC,EAAE;QACjD,MAAMtI,OAAO,GAAGhC,QAAQ,CAACyR,aAAa,CAAC,GAAG,GAAGP,qBAAqB,CAAC;QACnE,MAAMQ,IAAI,GAAG1P,OAAQ,CAAC2P,qBAAqB,EAAE;QAC7CL,OAAO,GAAII,IAAI,CAACE,IAAI,GAAG,EAAE,GAAGF,IAAI,CAAC7M,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAI,IAAI;QAC9D0M,OAAO,GAAIG,IAAI,CAACG,GAAG,GAAG9S,MAAM,CAAC6F,OAAO,GAAI,IAAI,CAAC,CAAC;MAChD;IACF;IAEA,IAAI,CAACC,KAAK,EAAE;MACVA,KAAK,GAAG,GAAG;IACb;IAEA,MAAMiN,SAAS,GAAIf,aAAa,CAAC/N,IAAI,CAACgO,SAAS,EAAE;MAC/Ce,MAAM,EAAE,EAAE;MACVlN,KAAK,EAAE,IAAI,CAACmN,mBAAmB,CAACnN,KAAK,CAAC,GAAGA,KAAK,GAAG,IAAI,GAAGA,KAAK;MAC7DvC,MAAM,EAAEA,MAAM,GAAGA,MAAM,GAAG,MAAM;MAChC2P,UAAU,EAAE,KAAK;MACjBC,eAAe,EAAE,IAAI;MACrBC,KAAK,EAAE,IAAI;MACXC,YAAY,EAAE;QACZ,WAAW,EAAE,MAAM;QAAE,YAAY,EAAE9P,MAAM,GAAGA,MAAM,GAAG,OAAO;QAAE,UAAU,EAAE,MAAM;QAAE,eAAe,EAAE,MAAM;QAAE,SAAS,EAAE;OACvH;MACDhC,KAAK,EAAE4Q,qBAAqB,GAAG;QAAE,MAAM,EAAEI,OAAO;QAAE,KAAK,EAAEC,OAAO;QAAE,UAAU,EAAE;MAAO,CAAE,GAAG,EAAE;MAC5Fc,UAAU,EAAE,KAAK;MACjBC,cAAc,EAAE,kBAAkB;MAClC5X,IAAI,EAAE;QAAEuW,UAAU,EAAEA;MAAU;KAC/B,CAAE;IACH,IAAI,CAAC3S,IAAI,CAACiU,IAAI,CAACT,SAAS,CAAC;IACzBA,SAAS,CAACU,OAAO,CAAC1L,SAAS,CAAEpM,IAAS,IAAI;MACxC+X,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEhY,IAAI,CAAC;MAC7C,IAAIyW,aAAa,EAAE;QACjBA,aAAa,CAACzW,IAAI,CAAC;MACrB;IACF,CAAC,CAAC;EACJ;EAEAiY,gBAAgBA,CAACC,YAAiB;IAChC,IAAIA,YAAY,EAAE;MAChBA,YAAY,CAACC,OAAO,EAAE;IACxB;EACF;EAEAC,uBAAuBA,CAAA;IACrB,IAAI,CAAC1X,MAAM,CAAC6I,aAAa,CAAC,oCAAoC,CAAC;EACjE;EAEA8O,gBAAgBA,CAAA;IACd,IAAI,CAAC3X,MAAM,CAAC6I,aAAa,CAAC,oBAAoB,CAAC;EACjD;EAEA+O,aAAaA,CAAClL,GAAQ;IACpB,OAAOE,MAAM,CAACwJ,IAAI,CAAC1J,GAAG,CAAC,CAACwC,MAAM,KAAK,CAAC;EACtC;EAEA2I,cAAcA,CAACC,SAAiB,EAAEC,WAAiB;IACjD,MAAM/J,UAAU,GAAG+J,WAAW,CAAC3K,WAAW,EAAE,GAAG,GAAG,IAAI2K,WAAW,CAACzK,QAAQ,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,GAAGwK,SAAS;IACnG,MAAME,KAAK,GAAG,IAAIjO,IAAI,EAAE;IACxB,MAAMkO,oBAAoB,GAAG,IAAIlO,IAAI,CAACiE,UAAU,CAAC;IAEjD;IACAgK,KAAK,CAACvH,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC1BsH,WAAW,CAACtH,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAEhC,OAAOwH,oBAAoB,GAAGD,KAAK;EACrC;EAEAE,6BAA6BA,CAACC,aAAa,GAAG,GAAG;IAC/C,MAAMC,mBAAmB,GAAGzU,MAAM,CAACC,UAAU;IAC7C,MAAMyU,UAAU,GAAGF,aAAa;IAChC,MAAM3N,MAAM,GAAG4N,mBAAmB,GAAGC,UAAU;IAC/C,OAAO7N,MAAM;EACf;EAEAoM,mBAAmBA,CAAC0B,KAAa;IAC/B;IACA,MAAMC,OAAO,GAAG,UAAU;IAE1B,OAAOA,OAAO,CAACC,IAAI,CAACF,KAAK,CAAC;EAC5B;EAEA;EACAG,oBAAoBA,CAAC1D,SAAqB;IACxC,MAAME,gBAAgB,GAAqB,EAAE;IAC7C,IAAIF,SAAS,EAAE;MACbE,gBAAgB,CAACyD,WAAW,GAAG;QAAE,WAAW,EAAEC,IAAI,CAACC,SAAS,CAAC7D,SAAS;MAAC,CAAE;IAC3E;IACA,IAAI,CAAC/U,MAAM,CAACgJ,QAAQ,CAAC,CAAC,uBAAuB,CAAC,EAAEiM,gBAAgB,CAAC;EACnE;EAEA4D,gBAAgBA,CAACjQ,GAAW;IAC1BjF,MAAM,CAACiE,IAAI,CAACgB,GAAG,EAAE,QAAQ,CAAC;EAC5B;EAEAkQ,uBAAuBA,CAACC,GAAW;IACjC,IAAI,CAACA,GAAG,EAAE;MACR,OAAO,EAAE;IACX;IACA,IAAIA,GAAG,KAAK,UAAU,IAAIA,GAAG,CAACC,QAAQ,CAAC,WAAW,CAAC,EAAE;MACnD,OAAO,GAAG;IACZ;IAEA,OAAOD,GAAG;EACZ;EAEAE,mBAAmBA,CAACF,GAAW,EAAEG,UAAmB;IAClD,IAAI,CAACH,GAAG,EAAE;MACR,OAAO,EAAE;IACX;IACA,IAAIA,GAAG,KAAK,UAAU,IAAIA,GAAG,CAACC,QAAQ,CAAC,WAAW,CAAC,EAAE;MACnD,OAAOE,UAAU,GAAGA,UAAU,GAAG,GAAG;IACtC;IAEA,OAAOH,GAAG;EACZ;EAEAI,eAAeA,CAACvQ,GAAW;IACzB,MAAMwQ,QAAQ,GAAG,QAAQ,GAAGxQ,GAAG;IAC/B,OAAO,IAAI,CAAC1I,SAAS,CAACmZ,sBAAsB,CAACD,QAAQ,CAAC;EACxD;EAEAE,kBAAkBA,CAACrN,CAAS,EAAEsN,CAAS;IACrC,MAAMC,UAAU,GAAGja,MAAM,CAAC0M,CAAC,CAACwN,YAAY,CAAC;IACzC,MAAMC,UAAU,GAAGna,MAAM,CAACga,CAAC,CAACE,YAAY,CAAC;IACzC,IAAID,UAAU,CAACG,QAAQ,CAACD,UAAU,EAAE,QAAQ,CAAC,EAAE;MAC7C,OAAO,CAAC,CAAC;IACX,CAAC,MAAM,IAAIF,UAAU,CAACI,OAAO,CAACF,UAAU,EAAE,QAAQ,CAAC,EAAE;MACnD,OAAO,CAAC;IACV,CAAC,MAAM;MACL,OAAO,CAAC;IACV;EACF;EAEAG,iBAAiBA,CAAC5N,CAAS,EAAEsN,CAAS;IACpC,MAAMO,KAAK,GAAGva,MAAM,CAAC0M,CAAC,CAACwN,YAAY,CAAC;IACpC,MAAMM,KAAK,GAAGxa,MAAM,CAACga,CAAC,CAACE,YAAY,CAAC;IACpC,OAAOM,KAAK,CAACpU,IAAI,CAACmU,KAAK,CAAC;EAC1B;EAEAE,oBAAoBA,CAACC,WAA+B;IAClD,IAAI,CAACA,WAAW,EAAE;MAChB,OAAO,EAAE;IACX;IAEA,MAAMC,KAAK,GAAGD,WAAW,CAAChQ,KAAK,CAAC,GAAG,CAAC;IACpC,IAAIiQ,KAAK,CAAChL,MAAM,GAAG,CAAC,EAAE;MACpB,OAAOgL,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB;IAEA,OAAO,EAAE;EACX;EAEAC,mBAAmBA,CAAA;IACjB,MAAMC,WAAW,GAAG,IAAI,CAAC7W,aAAa,EAAE,CAACoO,IAAI,CAACrM,EAAE,IAAIA,EAAE,CAACuM,GAAG,KAAK,IAAI,CAAC;IACpE,OAAOuI,WAAW,IAAI,IAAI,CAAC7W,aAAa,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE;EACrD;EAEA8W,kBAAkBA,CAACJ,WAA+B;IAChD,IAAI,CAACA,WAAW,EAAE;MAChB,OAAO,EAAE;IACX;IAEA,MAAMC,KAAK,GAAGD,WAAW,CAAChQ,KAAK,CAAC,GAAG,CAAC;IACpC,IAAIiQ,KAAK,CAAChL,MAAM,GAAG,CAAC,EAAE;MACpB,OAAOgL,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IACnB;IAEA,OAAO,EAAE;EACX;EAEAI,aAAaA,CAACC,UAAyB,EAAEC,UAAA,GAAqB,OAAO;IACnE,IAAIC,UAAU;IACd,IAAIC,gBAAgB,GAAG,OAAOH,UAAU,KAAK,QAAQ,GAAGA,UAAU,GAAGhb,MAAM,CAACgb,UAAU,CAAC,CAACrQ,WAAW,EAAE;IAErG,IAAIwQ,gBAAgB,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;MAClCF,UAAU,GAAGlb,MAAM,CAAC6Q,GAAG,CAACsK,gBAAgB,CAAC,CAAC5J,MAAM,CAAC0J,UAAU,CAAC;IAC9D,CAAC,MAAM;MACLC,UAAU,GAAGlb,MAAM,CAACmb,gBAAgB,CAAC,CAAC5J,MAAM,CAAC0J,UAAU,CAAC;IAC1D;IACA,OAAOC,UAAU;EACnB;EAEAG,wBAAwBA,CAAC3O,CAAS,EAAEsN,CAAS;IAC3C,MAAMQ,KAAK,GAAGxa,MAAM,CAACga,CAAC,CAACE,YAAY,CAAC;IACpC,MAAMK,KAAK,GAAGva,MAAM,CAAC0M,CAAC,CAACwN,YAAY,CAAC;IACpC,OAAOM,KAAK,CAACpU,IAAI,CAACmU,KAAK,CAAC;EAC1B;EAEAe,iBAAiBA,CAAC7M,UAAkB,EAAE8M,SAAiB;IACrD,MAAMxK,GAAG,GAAG/Q,MAAM,EAAE;IACpB,MAAMuK,IAAI,GAAGvK,MAAM,CAACyO,UAAU,CAAC;IAC/B,MAAM+M,iBAAiB,GAAG7U,IAAI,CAACC,GAAG,CAACmK,GAAG,CAAC3K,IAAI,CAACmE,IAAI,EAAE,SAAS,CAAC,CAAC;IAC7D,MAAMkR,eAAe,GAAGD,iBAAiB,GAAG,EAAE;IAC9C1D,OAAO,CAACC,GAAG,CAAC0D,eAAe,CAAC;IAE5B;IACA,MAAMC,MAAM,GAAG3K,GAAG,CAACsJ,OAAO,CAAC9P,IAAI,CAAC;IAChCuN,OAAO,CAACC,GAAG,CAAC2D,MAAM,CAAC;IAEnB;IACA,OAAO,CAACA,MAAM,IAAKD,eAAe,IAAIF,SAAU;EAClD;EAEAI,kBAAkBA,CAACC,MAAc;IAC/B,IAAIA,MAAM,CAACC,aAAa,EAAE;MACxB,MAAMC,UAAU,GAAG,2BAA2B;MAC9C,IAAIA,UAAU,CAAC7C,IAAI,CAAC2C,MAAM,CAACC,aAAa,CAAC,EAAE;QACzC,MAAME,YAAY,GAAGC,SAAS,CAACJ,MAAM,CAACC,aAAa,CAAC;QACpDzX,MAAM,CAACiE,IAAI,CAAC0T,YAAY,EAAE,QAAQ,CAAC;MACrC,CAAC,MAAM;QACLjE,OAAO,CAACmE,KAAK,CAAC,yCAAyC,CAAC;MAC1D;IACF,CAAC,MAAM;MACLnE,OAAO,CAACmE,KAAK,CAAC,4CAA4C,CAAC;IAC7D;EACF;EAEAC,gBAAgBA,CAACN,MAAc;IAC7B,IAAIA,MAAM,CAACO,kBAAkB,EAAE;MAC7B,MAAML,UAAU,GAAG,2BAA2B;MAC9C,IAAIA,UAAU,CAAC7C,IAAI,CAAC2C,MAAM,CAACO,kBAAmB,CAAC,EAAE;QAC/C,MAAMJ,YAAY,GAAGC,SAAS,CAACJ,MAAM,CAACO,kBAAmB,CAAC;QAC1D/X,MAAM,CAACiE,IAAI,CAAC0T,YAAY,EAAE,QAAQ,CAAC;MACrC,CAAC,MAAM;QACLjE,OAAO,CAACmE,KAAK,CAAC,yCAAyC,CAAC;MAC1D;IACF,CAAC,MAAM;MACLnE,OAAO,CAACmE,KAAK,CAAC,4CAA4C,CAAC;IAC7D;EACF;EAEAG,iBAAiBA,CAACR,MAAc;IAC9B,IAAIA,MAAM,EAAE;MACV,IAAI,CAACnb,MAAM,CAACgJ,QAAQ,CAAC,CAAC,OAAO,CAAC,EAAE;QAAE0P,WAAW,EAAE;UAAE/T,EAAE,EAAEwW,MAAM,CAACxW,EAAE;UAAEiX,WAAW,EAAET,MAAM,CAACS;QAAW;MAAE,CAAE,CAAC;IACtG,CAAC,MAAM;MACLvE,OAAO,CAACmE,KAAK,CAAC,4CAA4C,CAAC;IAC7D;EACF;EAEAK,0BAA0BA,CAAC5L,QAAgB;IACzC,OAAQ3Q,IAAI,CAAC0D,SAAS,CAAC2O,IAAI,CAAErM,EAAY,IAAKA,EAAE,CAAC8K,GAAG,CAAC4I,QAAQ,CAAC/I,QAAQ,CAAC,CAAC;EAC1E;EAEA6L,eAAeA,CAAC7V,OAAA,GAAkB,EAAE,EAAE8V,IAAY,EAAEC,QAAgB;IAClE,MAAMC,WAAW,GAAG1c,MAAM,EAAE;IAC5B,MAAM2c,cAAc,GAAG3c,MAAM,CAACyc,QAAQ,CAAC;IAEvC;IACA,MAAM,CAAChW,KAAK,EAAEmW,YAAY,CAAC,GAAGJ,IAAI,CAAC9R,KAAK,CAAC,GAAG,CAAC,CAACjL,GAAG,CAACod,MAAM,CAAC;IACzD,MAAMC,UAAU,GAAG9c,MAAM,EAAE,CAACyG,KAAK,CAACA,KAAK,CAAC,CAACC,OAAO,CAACkW,YAAY,CAAC;IAE9D;IACA,IAAI,CAACD,cAAc,CAACI,MAAM,CAACL,WAAW,EAAE,KAAK,CAAC,EAAE;MAC9C,OAAO,KAAK;IACd;IAEA;IACA,MAAMM,cAAc,GAAGF,UAAU,CAAC1W,IAAI,CAACsW,WAAW,EAAE,SAAS,CAAC;IAE9D;IACA,OAAO/V,IAAI,CAACC,GAAG,CAACoW,cAAc,CAAC,IAAItW,OAAO;EAC5C;EAEAuW,cAAcA,CAACvM,QAAgB;IAC7B;IACA,MAAMwM,YAAY,GAAGnd,IAAI,CAAC0D,SAAS,CAAC2O,IAAI,CAAE+K,EAAY,IAAKA,EAAE,CAACtM,GAAG,CAAC4I,QAAQ,CAAC/I,QAAQ,CAAC,CAAC;IACrF,IAAI,CAACwM,YAAY,EAAE;MACjB,MAAM,IAAIE,KAAK,CAAC,6BAA6B,CAAC;IAChD;IACA,MAAMC,MAAM,GAAGH,YAAY,CAACG,MAAM;IAElC;IACA,MAAMC,aAAa,GAAGtd,MAAM,EAAE;IAE9B;IACA,MAAMud,eAAe,GAAGD,aAAa,CAACE,SAAS,CAACH,MAAM,CAAC;IAEvD;IACA,MAAMX,WAAW,GAAGa,eAAe,CAAChM,MAAM,CAAC,OAAO,CAAC;IAEnD,OAAOmL,WAAW;EACpB;EAEAe,wBAAwBA,CAAC/M,QAAgB;IACvC,MAAMgM,WAAW,GAAG1c,MAAM,EAAE,CAAC,CAAC;IAC9B,MAAMuK,IAAI,GAAGvK,MAAM,CAACmd,EAAE,CAACT,WAAW,EAAEhM,QAAQ,CAAC;IAC7C,OAAOnG,IAAI,CAACgH,MAAM,CAAC,qBAAqB,CAAC;EAC3C;EAEAmM,4BAA4BA,CAAClB,IAAY,EAAE9L,QAAgB;IACzD,MAAMnG,IAAI,GAAGvK,MAAM,CAACmd,EAAE,CAACnd,MAAM,EAAE,EAAE0Q,QAAQ,CAAC;IAC1C,OAAOnG,IAAI,CAACgH,MAAM,CAAC,SAAS,CAAC;EAC/B;EAEAoM,0BAA0BA,CAACzX,KAAa,EAAEC,KAAa;IACrD,MAAMyX,YAAY,GAAG,IAAI,CAAChd,WAAW,CAACid,eAAe,EAAE,CAACjN,QAAQ;IAChE,MAAMkN,WAAW,GAAG9d,MAAM,CAACmd,EAAE,CAACjX,KAAK,EAAE0X,YAAY,CAAC,CAAC,CAAC;IACpD,MAAMG,WAAW,GAAG/d,MAAM,CAACmd,EAAE,CAAChX,KAAK,EAAEyX,YAAY,CAAC,CAAC,CAAC;IACpD,MAAMI,mBAAmB,GAAGF,WAAW,CAAC1X,IAAI,CAAC2X,WAAW,EAAE,SAAS,CAAC;IACpE,OAAOC,mBAAmB;EAC5B;EAEAC,2BAA2BA,CAAC/X,KAAa,EAAEC,KAAa,EAAEyV,MAAc;IACtE,MAAM3G,OAAO,GAAGjV,MAAM,CAAC4b,MAAM,CAAC1B,YAAY,CAAC,CAACzU,GAAG,CAACmW,MAAM,CAACsC,QAAQ,EAAE,OAAO,CAAC;IACzE,MAAMF,mBAAmB,GAAG,IAAI,CAACL,0BAA0B,CAACzX,KAAK,EAAEC,KAAK,CAAC;IACzE,MAAMgY,uBAAuB,GAAGvC,MAAM,CAACsC,QAAQ,GAAG,EAAE;IACpD;IACA,IAAIle,MAAM,CAACmG,KAAK,CAAC,CAACiU,QAAQ,CAAClU,KAAK,CAAC,IAAIlG,MAAM,CAACmG,KAAK,CAAC,CAACiU,QAAQ,CAACnF,OAAO,CAAC,EAAE;MACpE,OAAOtO,IAAI,CAACC,GAAG,CAACoX,mBAAmB,CAAC,IAAIG,uBAAuB;IACjE;IAEA;IACA,MAAMC,oBAAoB,GAAGxC,MAAM,CAACsC,QAAQ,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAExD;IACA,OAAOvX,IAAI,CAACC,GAAG,CAACoX,mBAAmB,CAAC,IAAII,oBAAoB;EAC9D;EAIAC,cAAcA,CAACzC,MAAc,EAAE0C,IAAU;IACvC,MAAMC,YAAY,GAAG,IAAI,CAACjC,0BAA0B,CAACgC,IAAI,CAAC1N,QAAS,CAAC,CAACD,IAAI;IACzE,MAAM6N,aAAa,GAAG,IAAI,CAACnO,gBAAgB,CAAC,IAAI,CAACiM,0BAA0B,CAACgC,IAAI,CAAC1N,QAAS,CAAC,CAACD,IAAI,EAAE,KAAK,CAAE;IAEzG,MAAM8N,yBAAyB,GAAG,IAAI,CAACxB,cAAc,CAACqB,IAAI,CAAC1N,QAAS,CAAC;IACrE,MAAM8N,gCAAgC,GAAG9C,MAAM,CAAC1B,YAAY,CAACzP,QAAQ,EAAE;IACvE;IACA,OAAO,IAAI,CAAC8R,eAAe,CAAC,EAAE,EAAEkC,yBAAyB,EAAEC,gCAAgC,CAAC;EAC9F;EAEAC,kBAAkBA,CAAC3J,SAAiB,EAAEC,OAAe;IACnD,MAAM2J,WAAW,GAAG5e,MAAM,CAACgV,SAAS,EAAE,OAAO,CAAC;IAC9C,MAAM6J,SAAS,GAAG7e,MAAM,CAACiV,OAAO,EAAE,OAAO,CAAC;IAE1C,MAAMiJ,QAAQ,GAAGle,MAAM,CAACke,QAAQ,CAACW,SAAS,CAACzY,IAAI,CAACwY,WAAW,CAAC,CAAC;IAE7D,MAAMnY,KAAK,GAAGE,IAAI,CAACuC,KAAK,CAACgV,QAAQ,CAACY,OAAO,EAAE,CAAC;IAC5C,MAAMpY,OAAO,GAAGC,IAAI,CAACuC,KAAK,CAACgV,QAAQ,CAACa,SAAS,EAAE,CAAC,GAAG,EAAE;IAErD,OAAO,GAAGtY,KAAK,cAAcC,OAAO,UAAU;EAChD;EAEAsY,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACte,IAAI,CAACuL,GAAG,CAAM3L,cAAc,CAAC,CAAC2e,IAAI,CAC5Cxf,GAAG,CAACyf,QAAQ,IAAG;MACb,IAAIA,QAAQ,EAAE;QACZ,OAAOA,QAAQ;MACjB,CAAC,MAAM;QACL,MAAM,IAAI9B,KAAK,CAAC,oCAAoC,CAAC;MACvD;IACF,CAAC,CAAC,CACH;EACH;EAAC,QAAA+B,CAAA,G;qBA7xCU5e,cAAc,EAAA6e,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAdrf,cAAc;IAAAsf,OAAA,EAAdtf,cAAc,CAAAuf,IAAA;IAAAC,UAAA,EAFb;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}