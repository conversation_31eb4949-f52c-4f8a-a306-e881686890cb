{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { NgModule, CSP_NONCE, Injectable, Optional, Inject } from '@angular/core';\nimport { coerceArray } from '@angular/cdk/coercion';\nimport { Subject, combineLatest, concat, Observable } from 'rxjs';\nimport { take, skip, debounceTime, map, startWith, takeUntil } from 'rxjs/operators';\nimport * as i1 from '@angular/cdk/platform';\nlet LayoutModule = /*#__PURE__*/(() => {\n  class LayoutModule {\n    static #_ = this.ɵfac = function LayoutModule_Factory(t) {\n      return new (t || LayoutModule)();\n    };\n    static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: LayoutModule\n    });\n    static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n  return LayoutModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Global registry for all dynamically-created, injected media queries. */\nconst mediaQueriesForWebkitCompatibility = /*#__PURE__*/new Set();\n/** Style tag that holds all of the dynamically-created media queries. */\nlet mediaQueryStyleNode;\n/** A utility for calling matchMedia queries. */\nlet MediaMatcher = /*#__PURE__*/(() => {\n  class MediaMatcher {\n    constructor(_platform, _nonce) {\n      this._platform = _platform;\n      this._nonce = _nonce;\n      this._matchMedia = this._platform.isBrowser && window.matchMedia ?\n      // matchMedia is bound to the window scope intentionally as it is an illegal invocation to\n      // call it from a different scope.\n      window.matchMedia.bind(window) : noopMatchMedia;\n    }\n    /**\n     * Evaluates the given media query and returns the native MediaQueryList from which results\n     * can be retrieved.\n     * Confirms the layout engine will trigger for the selector query provided and returns the\n     * MediaQueryList for the query provided.\n     */\n    matchMedia(query) {\n      if (this._platform.WEBKIT || this._platform.BLINK) {\n        createEmptyStyleRule(query, this._nonce);\n      }\n      return this._matchMedia(query);\n    }\n    static #_ = this.ɵfac = function MediaMatcher_Factory(t) {\n      return new (t || MediaMatcher)(i0.ɵɵinject(i1.Platform), i0.ɵɵinject(CSP_NONCE, 8));\n    };\n    static #_2 = this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MediaMatcher,\n      factory: MediaMatcher.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return MediaMatcher;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Creates an empty stylesheet that is used to work around browser inconsistencies related to\n * `matchMedia`. At the time of writing, it handles the following cases:\n * 1. On WebKit browsers, a media query has to have at least one rule in order for `matchMedia`\n * to fire. We work around it by declaring a dummy stylesheet with a `@media` declaration.\n * 2. In some cases Blink browsers will stop firing the `matchMedia` listener if none of the rules\n * inside the `@media` match existing elements on the page. We work around it by having one rule\n * targeting the `body`. See https://github.com/angular/components/issues/23546.\n */\nfunction createEmptyStyleRule(query, nonce) {\n  if (mediaQueriesForWebkitCompatibility.has(query)) {\n    return;\n  }\n  try {\n    if (!mediaQueryStyleNode) {\n      mediaQueryStyleNode = document.createElement('style');\n      if (nonce) {\n        mediaQueryStyleNode.setAttribute('nonce', nonce);\n      }\n      mediaQueryStyleNode.setAttribute('type', 'text/css');\n      document.head.appendChild(mediaQueryStyleNode);\n    }\n    if (mediaQueryStyleNode.sheet) {\n      mediaQueryStyleNode.sheet.insertRule(`@media ${query} {body{ }}`, 0);\n      mediaQueriesForWebkitCompatibility.add(query);\n    }\n  } catch (e) {\n    console.error(e);\n  }\n}\n/** No-op matchMedia replacement for non-browser platforms. */\nfunction noopMatchMedia(query) {\n  // Use `as any` here to avoid adding additional necessary properties for\n  // the noop matcher.\n  return {\n    matches: query === 'all' || query === '',\n    media: query,\n    addListener: () => {},\n    removeListener: () => {}\n  };\n}\n\n/** Utility for checking the matching state of @media queries. */\nlet BreakpointObserver = /*#__PURE__*/(() => {\n  class BreakpointObserver {\n    constructor(_mediaMatcher, _zone) {\n      this._mediaMatcher = _mediaMatcher;\n      this._zone = _zone;\n      /**  A map of all media queries currently being listened for. */\n      this._queries = new Map();\n      /** A subject for all other observables to takeUntil based on. */\n      this._destroySubject = new Subject();\n    }\n    /** Completes the active subject, signalling to all other observables to complete. */\n    ngOnDestroy() {\n      this._destroySubject.next();\n      this._destroySubject.complete();\n    }\n    /**\n     * Whether one or more media queries match the current viewport size.\n     * @param value One or more media queries to check.\n     * @returns Whether any of the media queries match.\n     */\n    isMatched(value) {\n      const queries = splitQueries(coerceArray(value));\n      return queries.some(mediaQuery => this._registerQuery(mediaQuery).mql.matches);\n    }\n    /**\n     * Gets an observable of results for the given queries that will emit new results for any changes\n     * in matching of the given queries.\n     * @param value One or more media queries to check.\n     * @returns A stream of matches for the given queries.\n     */\n    observe(value) {\n      const queries = splitQueries(coerceArray(value));\n      const observables = queries.map(query => this._registerQuery(query).observable);\n      let stateObservable = combineLatest(observables);\n      // Emit the first state immediately, and then debounce the subsequent emissions.\n      stateObservable = concat(stateObservable.pipe(take(1)), stateObservable.pipe(skip(1), debounceTime(0)));\n      return stateObservable.pipe(map(breakpointStates => {\n        const response = {\n          matches: false,\n          breakpoints: {}\n        };\n        breakpointStates.forEach(({\n          matches,\n          query\n        }) => {\n          response.matches = response.matches || matches;\n          response.breakpoints[query] = matches;\n        });\n        return response;\n      }));\n    }\n    /** Registers a specific query to be listened for. */\n    _registerQuery(query) {\n      // Only set up a new MediaQueryList if it is not already being listened for.\n      if (this._queries.has(query)) {\n        return this._queries.get(query);\n      }\n      const mql = this._mediaMatcher.matchMedia(query);\n      // Create callback for match changes and add it is as a listener.\n      const queryObservable = new Observable(observer => {\n        // Listener callback methods are wrapped to be placed back in ngZone. Callbacks must be placed\n        // back into the zone because matchMedia is only included in Zone.js by loading the\n        // webapis-media-query.js file alongside the zone.js file.  Additionally, some browsers do not\n        // have MediaQueryList inherit from EventTarget, which causes inconsistencies in how Zone.js\n        // patches it.\n        const handler = e => this._zone.run(() => observer.next(e));\n        mql.addListener(handler);\n        return () => {\n          mql.removeListener(handler);\n        };\n      }).pipe(startWith(mql), map(({\n        matches\n      }) => ({\n        query,\n        matches\n      })), takeUntil(this._destroySubject));\n      // Add the MediaQueryList to the set of queries.\n      const output = {\n        observable: queryObservable,\n        mql\n      };\n      this._queries.set(query, output);\n      return output;\n    }\n    static #_ = this.ɵfac = function BreakpointObserver_Factory(t) {\n      return new (t || BreakpointObserver)(i0.ɵɵinject(MediaMatcher), i0.ɵɵinject(i0.NgZone));\n    };\n    static #_2 = this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: BreakpointObserver,\n      factory: BreakpointObserver.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return BreakpointObserver;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * Split each query string into separate query strings if two queries are provided as comma\n * separated.\n */\nfunction splitQueries(queries) {\n  return queries.map(query => query.split(',')).reduce((a1, a2) => a1.concat(a2)).map(query => query.trim());\n}\n\n// PascalCase is being used as Breakpoints is used like an enum.\n// tslint:disable-next-line:variable-name\nconst Breakpoints = {\n  XSmall: '(max-width: 599.98px)',\n  Small: '(min-width: 600px) and (max-width: 959.98px)',\n  Medium: '(min-width: 960px) and (max-width: 1279.98px)',\n  Large: '(min-width: 1280px) and (max-width: 1919.98px)',\n  XLarge: '(min-width: 1920px)',\n  Handset: '(max-width: 599.98px) and (orientation: portrait), ' + '(max-width: 959.98px) and (orientation: landscape)',\n  Tablet: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait), ' + '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n  Web: '(min-width: 840px) and (orientation: portrait), ' + '(min-width: 1280px) and (orientation: landscape)',\n  HandsetPortrait: '(max-width: 599.98px) and (orientation: portrait)',\n  TabletPortrait: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait)',\n  WebPortrait: '(min-width: 840px) and (orientation: portrait)',\n  HandsetLandscape: '(max-width: 959.98px) and (orientation: landscape)',\n  TabletLandscape: '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n  WebLandscape: '(min-width: 1280px) and (orientation: landscape)'\n};\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BreakpointObserver, Breakpoints, LayoutModule, MediaMatcher };", "map": {"version": 3, "names": ["i0", "NgModule", "CSP_NONCE", "Injectable", "Optional", "Inject", "coerce<PERSON><PERSON><PERSON>", "Subject", "combineLatest", "concat", "Observable", "take", "skip", "debounceTime", "map", "startWith", "takeUntil", "i1", "LayoutModule", "_", "ɵfac", "LayoutModule_Factory", "t", "_2", "ɵmod", "ɵɵdefineNgModule", "type", "_3", "ɵinj", "ɵɵdefineInjector", "ngDevMode", "mediaQueriesForWebkitCompatibility", "Set", "mediaQueryStyleNode", "MediaMatcher", "constructor", "_platform", "_nonce", "_matchMedia", "<PERSON><PERSON><PERSON><PERSON>", "window", "matchMedia", "bind", "noopMatchMedia", "query", "WEBKIT", "BLINK", "createEmptyStyleRule", "MediaMatcher_Factory", "ɵɵinject", "Platform", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "nonce", "has", "document", "createElement", "setAttribute", "head", "append<PERSON><PERSON><PERSON>", "sheet", "insertRule", "add", "e", "console", "error", "matches", "media", "addListener", "removeListener", "BreakpointObserver", "_mediaMatcher", "_zone", "_queries", "Map", "_destroySubject", "ngOnDestroy", "next", "complete", "isMatched", "value", "queries", "splitQueries", "some", "mediaQuery", "_registerQuery", "mql", "observe", "observables", "observable", "stateObservable", "pipe", "breakpointStates", "response", "breakpoints", "for<PERSON>ach", "get", "queryObservable", "observer", "handler", "run", "output", "set", "BreakpointObserver_Factory", "NgZone", "split", "reduce", "a1", "a2", "trim", "Breakpoints", "XSmall", "Small", "Medium", "Large", "<PERSON>L<PERSON>ge", "Handset", "Tablet", "Web", "HandsetPortrait", "TabletPortrait", "WebPortrait", "HandsetLandscape", "TabletLandscape", "WebLandscape"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@angular/cdk/fesm2022/layout.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { NgModule, CSP_NONCE, Injectable, Optional, Inject } from '@angular/core';\nimport { coerceArray } from '@angular/cdk/coercion';\nimport { Subject, combineLatest, concat, Observable } from 'rxjs';\nimport { take, skip, debounceTime, map, startWith, takeUntil } from 'rxjs/operators';\nimport * as i1 from '@angular/cdk/platform';\n\nclass LayoutModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: LayoutModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: LayoutModule }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: LayoutModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: LayoutModule, decorators: [{\n            type: NgModule,\n            args: [{}]\n        }] });\n\n/** Global registry for all dynamically-created, injected media queries. */\nconst mediaQueriesForWebkitCompatibility = new Set();\n/** Style tag that holds all of the dynamically-created media queries. */\nlet mediaQueryStyleNode;\n/** A utility for calling matchMedia queries. */\nclass MediaMatcher {\n    constructor(_platform, _nonce) {\n        this._platform = _platform;\n        this._nonce = _nonce;\n        this._matchMedia =\n            this._platform.isBrowser && window.matchMedia\n                ? // matchMedia is bound to the window scope intentionally as it is an illegal invocation to\n                    // call it from a different scope.\n                    window.matchMedia.bind(window)\n                : noopMatchMedia;\n    }\n    /**\n     * Evaluates the given media query and returns the native MediaQueryList from which results\n     * can be retrieved.\n     * Confirms the layout engine will trigger for the selector query provided and returns the\n     * MediaQueryList for the query provided.\n     */\n    matchMedia(query) {\n        if (this._platform.WEBKIT || this._platform.BLINK) {\n            createEmptyStyleRule(query, this._nonce);\n        }\n        return this._matchMedia(query);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MediaMatcher, deps: [{ token: i1.Platform }, { token: CSP_NONCE, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MediaMatcher, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MediaMatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: i1.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [CSP_NONCE]\n                }] }] });\n/**\n * Creates an empty stylesheet that is used to work around browser inconsistencies related to\n * `matchMedia`. At the time of writing, it handles the following cases:\n * 1. On WebKit browsers, a media query has to have at least one rule in order for `matchMedia`\n * to fire. We work around it by declaring a dummy stylesheet with a `@media` declaration.\n * 2. In some cases Blink browsers will stop firing the `matchMedia` listener if none of the rules\n * inside the `@media` match existing elements on the page. We work around it by having one rule\n * targeting the `body`. See https://github.com/angular/components/issues/23546.\n */\nfunction createEmptyStyleRule(query, nonce) {\n    if (mediaQueriesForWebkitCompatibility.has(query)) {\n        return;\n    }\n    try {\n        if (!mediaQueryStyleNode) {\n            mediaQueryStyleNode = document.createElement('style');\n            if (nonce) {\n                mediaQueryStyleNode.setAttribute('nonce', nonce);\n            }\n            mediaQueryStyleNode.setAttribute('type', 'text/css');\n            document.head.appendChild(mediaQueryStyleNode);\n        }\n        if (mediaQueryStyleNode.sheet) {\n            mediaQueryStyleNode.sheet.insertRule(`@media ${query} {body{ }}`, 0);\n            mediaQueriesForWebkitCompatibility.add(query);\n        }\n    }\n    catch (e) {\n        console.error(e);\n    }\n}\n/** No-op matchMedia replacement for non-browser platforms. */\nfunction noopMatchMedia(query) {\n    // Use `as any` here to avoid adding additional necessary properties for\n    // the noop matcher.\n    return {\n        matches: query === 'all' || query === '',\n        media: query,\n        addListener: () => { },\n        removeListener: () => { },\n    };\n}\n\n/** Utility for checking the matching state of @media queries. */\nclass BreakpointObserver {\n    constructor(_mediaMatcher, _zone) {\n        this._mediaMatcher = _mediaMatcher;\n        this._zone = _zone;\n        /**  A map of all media queries currently being listened for. */\n        this._queries = new Map();\n        /** A subject for all other observables to takeUntil based on. */\n        this._destroySubject = new Subject();\n    }\n    /** Completes the active subject, signalling to all other observables to complete. */\n    ngOnDestroy() {\n        this._destroySubject.next();\n        this._destroySubject.complete();\n    }\n    /**\n     * Whether one or more media queries match the current viewport size.\n     * @param value One or more media queries to check.\n     * @returns Whether any of the media queries match.\n     */\n    isMatched(value) {\n        const queries = splitQueries(coerceArray(value));\n        return queries.some(mediaQuery => this._registerQuery(mediaQuery).mql.matches);\n    }\n    /**\n     * Gets an observable of results for the given queries that will emit new results for any changes\n     * in matching of the given queries.\n     * @param value One or more media queries to check.\n     * @returns A stream of matches for the given queries.\n     */\n    observe(value) {\n        const queries = splitQueries(coerceArray(value));\n        const observables = queries.map(query => this._registerQuery(query).observable);\n        let stateObservable = combineLatest(observables);\n        // Emit the first state immediately, and then debounce the subsequent emissions.\n        stateObservable = concat(stateObservable.pipe(take(1)), stateObservable.pipe(skip(1), debounceTime(0)));\n        return stateObservable.pipe(map(breakpointStates => {\n            const response = {\n                matches: false,\n                breakpoints: {},\n            };\n            breakpointStates.forEach(({ matches, query }) => {\n                response.matches = response.matches || matches;\n                response.breakpoints[query] = matches;\n            });\n            return response;\n        }));\n    }\n    /** Registers a specific query to be listened for. */\n    _registerQuery(query) {\n        // Only set up a new MediaQueryList if it is not already being listened for.\n        if (this._queries.has(query)) {\n            return this._queries.get(query);\n        }\n        const mql = this._mediaMatcher.matchMedia(query);\n        // Create callback for match changes and add it is as a listener.\n        const queryObservable = new Observable((observer) => {\n            // Listener callback methods are wrapped to be placed back in ngZone. Callbacks must be placed\n            // back into the zone because matchMedia is only included in Zone.js by loading the\n            // webapis-media-query.js file alongside the zone.js file.  Additionally, some browsers do not\n            // have MediaQueryList inherit from EventTarget, which causes inconsistencies in how Zone.js\n            // patches it.\n            const handler = (e) => this._zone.run(() => observer.next(e));\n            mql.addListener(handler);\n            return () => {\n                mql.removeListener(handler);\n            };\n        }).pipe(startWith(mql), map(({ matches }) => ({ query, matches })), takeUntil(this._destroySubject));\n        // Add the MediaQueryList to the set of queries.\n        const output = { observable: queryObservable, mql };\n        this._queries.set(query, output);\n        return output;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: BreakpointObserver, deps: [{ token: MediaMatcher }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: BreakpointObserver, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: BreakpointObserver, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: MediaMatcher }, { type: i0.NgZone }] });\n/**\n * Split each query string into separate query strings if two queries are provided as comma\n * separated.\n */\nfunction splitQueries(queries) {\n    return queries\n        .map(query => query.split(','))\n        .reduce((a1, a2) => a1.concat(a2))\n        .map(query => query.trim());\n}\n\n// PascalCase is being used as Breakpoints is used like an enum.\n// tslint:disable-next-line:variable-name\nconst Breakpoints = {\n    XSmall: '(max-width: 599.98px)',\n    Small: '(min-width: 600px) and (max-width: 959.98px)',\n    Medium: '(min-width: 960px) and (max-width: 1279.98px)',\n    Large: '(min-width: 1280px) and (max-width: 1919.98px)',\n    XLarge: '(min-width: 1920px)',\n    Handset: '(max-width: 599.98px) and (orientation: portrait), ' +\n        '(max-width: 959.98px) and (orientation: landscape)',\n    Tablet: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait), ' +\n        '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n    Web: '(min-width: 840px) and (orientation: portrait), ' +\n        '(min-width: 1280px) and (orientation: landscape)',\n    HandsetPortrait: '(max-width: 599.98px) and (orientation: portrait)',\n    TabletPortrait: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait)',\n    WebPortrait: '(min-width: 840px) and (orientation: portrait)',\n    HandsetLandscape: '(max-width: 959.98px) and (orientation: landscape)',\n    TabletLandscape: '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n    WebLandscape: '(min-width: 1280px) and (orientation: landscape)',\n};\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BreakpointObserver, Breakpoints, LayoutModule, MediaMatcher };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,eAAe;AACjF,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,OAAO,EAAEC,aAAa,EAAEC,MAAM,EAAEC,UAAU,QAAQ,MAAM;AACjE,SAASC,IAAI,EAAEC,IAAI,EAAEC,YAAY,EAAEC,GAAG,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AACpF,OAAO,KAAKC,EAAE,MAAM,uBAAuB;AAAC,IAEtCC,YAAY;EAAlB,MAAMA,YAAY,CAAC;IAAA,QAAAC,CAAA,GACN,IAAI,CAACC,IAAI,YAAAC,qBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFJ,YAAY;IAAA,CAAkD;IAAA,QAAAK,EAAA,GAC/J,IAAI,CAACC,IAAI,kBAD8ExB,EAAE,CAAAyB,gBAAA;MAAAC,IAAA,EACSR;IAAY,EAAG;IAAA,QAAAS,EAAA,GACjH,IAAI,CAACC,IAAI,kBAF8E5B,EAAE,CAAA6B,gBAAA,IAEwB;EAC9H;EAAC,OAJKX,YAAY;AAAA;AAKlB;EAAA,QAAAY,SAAA,oBAAAA,SAAA;AAAA;;AAKA;AACA,MAAMC,kCAAkC,gBAAG,IAAIC,GAAG,CAAC,CAAC;AACpD;AACA,IAAIC,mBAAmB;AACvB;AAAA,IACMC,YAAY;EAAlB,MAAMA,YAAY,CAAC;IACfC,WAAWA,CAACC,SAAS,EAAEC,MAAM,EAAE;MAC3B,IAAI,CAACD,SAAS,GAAGA,SAAS;MAC1B,IAAI,CAACC,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACC,WAAW,GACZ,IAAI,CAACF,SAAS,CAACG,SAAS,IAAIC,MAAM,CAACC,UAAU;MACvC;MACE;MACAD,MAAM,CAACC,UAAU,CAACC,IAAI,CAACF,MAAM,CAAC,GAChCG,cAAc;IAC5B;IACA;AACJ;AACA;AACA;AACA;AACA;IACIF,UAAUA,CAACG,KAAK,EAAE;MACd,IAAI,IAAI,CAACR,SAAS,CAACS,MAAM,IAAI,IAAI,CAACT,SAAS,CAACU,KAAK,EAAE;QAC/CC,oBAAoB,CAACH,KAAK,EAAE,IAAI,CAACP,MAAM,CAAC;MAC5C;MACA,OAAO,IAAI,CAACC,WAAW,CAACM,KAAK,CAAC;IAClC;IAAC,QAAAzB,CAAA,GACQ,IAAI,CAACC,IAAI,YAAA4B,qBAAA1B,CAAA;MAAA,YAAAA,CAAA,IAAwFY,YAAY,EArCtBlC,EAAE,CAAAiD,QAAA,CAqCsChC,EAAE,CAACiC,QAAQ,GArCnDlD,EAAE,CAAAiD,QAAA,CAqC8D/C,SAAS;IAAA,CAA6D;IAAA,QAAAqB,EAAA,GAC7N,IAAI,CAAC4B,KAAK,kBAtC6EnD,EAAE,CAAAoD,kBAAA;MAAAC,KAAA,EAsCYnB,YAAY;MAAAoB,OAAA,EAAZpB,YAAY,CAAAd,IAAA;MAAAmC,UAAA,EAAc;IAAM,EAAG;EACrJ;EAAC,OAzBKrB,YAAY;AAAA;AA0BlB;EAAA,QAAAJ,SAAA,oBAAAA,SAAA;AAAA;AASA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiB,oBAAoBA,CAACH,KAAK,EAAEY,KAAK,EAAE;EACxC,IAAIzB,kCAAkC,CAAC0B,GAAG,CAACb,KAAK,CAAC,EAAE;IAC/C;EACJ;EACA,IAAI;IACA,IAAI,CAACX,mBAAmB,EAAE;MACtBA,mBAAmB,GAAGyB,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;MACrD,IAAIH,KAAK,EAAE;QACPvB,mBAAmB,CAAC2B,YAAY,CAAC,OAAO,EAAEJ,KAAK,CAAC;MACpD;MACAvB,mBAAmB,CAAC2B,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC;MACpDF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAAC7B,mBAAmB,CAAC;IAClD;IACA,IAAIA,mBAAmB,CAAC8B,KAAK,EAAE;MAC3B9B,mBAAmB,CAAC8B,KAAK,CAACC,UAAU,CAAE,UAASpB,KAAM,YAAW,EAAE,CAAC,CAAC;MACpEb,kCAAkC,CAACkC,GAAG,CAACrB,KAAK,CAAC;IACjD;EACJ,CAAC,CACD,OAAOsB,CAAC,EAAE;IACNC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;EACpB;AACJ;AACA;AACA,SAASvB,cAAcA,CAACC,KAAK,EAAE;EAC3B;EACA;EACA,OAAO;IACHyB,OAAO,EAAEzB,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,EAAE;IACxC0B,KAAK,EAAE1B,KAAK;IACZ2B,WAAW,EAAEA,CAAA,KAAM,CAAE,CAAC;IACtBC,cAAc,EAAEA,CAAA,KAAM,CAAE;EAC5B,CAAC;AACL;;AAEA;AAAA,IACMC,kBAAkB;EAAxB,MAAMA,kBAAkB,CAAC;IACrBtC,WAAWA,CAACuC,aAAa,EAAEC,KAAK,EAAE;MAC9B,IAAI,CAACD,aAAa,GAAGA,aAAa;MAClC,IAAI,CAACC,KAAK,GAAGA,KAAK;MAClB;MACA,IAAI,CAACC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;MACzB;MACA,IAAI,CAACC,eAAe,GAAG,IAAIvE,OAAO,CAAC,CAAC;IACxC;IACA;IACAwE,WAAWA,CAAA,EAAG;MACV,IAAI,CAACD,eAAe,CAACE,IAAI,CAAC,CAAC;MAC3B,IAAI,CAACF,eAAe,CAACG,QAAQ,CAAC,CAAC;IACnC;IACA;AACJ;AACA;AACA;AACA;IACIC,SAASA,CAACC,KAAK,EAAE;MACb,MAAMC,OAAO,GAAGC,YAAY,CAAC/E,WAAW,CAAC6E,KAAK,CAAC,CAAC;MAChD,OAAOC,OAAO,CAACE,IAAI,CAACC,UAAU,IAAI,IAAI,CAACC,cAAc,CAACD,UAAU,CAAC,CAACE,GAAG,CAACpB,OAAO,CAAC;IAClF;IACA;AACJ;AACA;AACA;AACA;AACA;IACIqB,OAAOA,CAACP,KAAK,EAAE;MACX,MAAMC,OAAO,GAAGC,YAAY,CAAC/E,WAAW,CAAC6E,KAAK,CAAC,CAAC;MAChD,MAAMQ,WAAW,GAAGP,OAAO,CAACtE,GAAG,CAAC8B,KAAK,IAAI,IAAI,CAAC4C,cAAc,CAAC5C,KAAK,CAAC,CAACgD,UAAU,CAAC;MAC/E,IAAIC,eAAe,GAAGrF,aAAa,CAACmF,WAAW,CAAC;MAChD;MACAE,eAAe,GAAGpF,MAAM,CAACoF,eAAe,CAACC,IAAI,CAACnF,IAAI,CAAC,CAAC,CAAC,CAAC,EAAEkF,eAAe,CAACC,IAAI,CAAClF,IAAI,CAAC,CAAC,CAAC,EAAEC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;MACvG,OAAOgF,eAAe,CAACC,IAAI,CAAChF,GAAG,CAACiF,gBAAgB,IAAI;QAChD,MAAMC,QAAQ,GAAG;UACb3B,OAAO,EAAE,KAAK;UACd4B,WAAW,EAAE,CAAC;QAClB,CAAC;QACDF,gBAAgB,CAACG,OAAO,CAAC,CAAC;UAAE7B,OAAO;UAAEzB;QAAM,CAAC,KAAK;UAC7CoD,QAAQ,CAAC3B,OAAO,GAAG2B,QAAQ,CAAC3B,OAAO,IAAIA,OAAO;UAC9C2B,QAAQ,CAACC,WAAW,CAACrD,KAAK,CAAC,GAAGyB,OAAO;QACzC,CAAC,CAAC;QACF,OAAO2B,QAAQ;MACnB,CAAC,CAAC,CAAC;IACP;IACA;IACAR,cAAcA,CAAC5C,KAAK,EAAE;MAClB;MACA,IAAI,IAAI,CAACgC,QAAQ,CAACnB,GAAG,CAACb,KAAK,CAAC,EAAE;QAC1B,OAAO,IAAI,CAACgC,QAAQ,CAACuB,GAAG,CAACvD,KAAK,CAAC;MACnC;MACA,MAAM6C,GAAG,GAAG,IAAI,CAACf,aAAa,CAACjC,UAAU,CAACG,KAAK,CAAC;MAChD;MACA,MAAMwD,eAAe,GAAG,IAAI1F,UAAU,CAAE2F,QAAQ,IAAK;QACjD;QACA;QACA;QACA;QACA;QACA,MAAMC,OAAO,GAAIpC,CAAC,IAAK,IAAI,CAACS,KAAK,CAAC4B,GAAG,CAAC,MAAMF,QAAQ,CAACrB,IAAI,CAACd,CAAC,CAAC,CAAC;QAC7DuB,GAAG,CAAClB,WAAW,CAAC+B,OAAO,CAAC;QACxB,OAAO,MAAM;UACTb,GAAG,CAACjB,cAAc,CAAC8B,OAAO,CAAC;QAC/B,CAAC;MACL,CAAC,CAAC,CAACR,IAAI,CAAC/E,SAAS,CAAC0E,GAAG,CAAC,EAAE3E,GAAG,CAAC,CAAC;QAAEuD;MAAQ,CAAC,MAAM;QAAEzB,KAAK;QAAEyB;MAAQ,CAAC,CAAC,CAAC,EAAErD,SAAS,CAAC,IAAI,CAAC8D,eAAe,CAAC,CAAC;MACpG;MACA,MAAM0B,MAAM,GAAG;QAAEZ,UAAU,EAAEQ,eAAe;QAAEX;MAAI,CAAC;MACnD,IAAI,CAACb,QAAQ,CAAC6B,GAAG,CAAC7D,KAAK,EAAE4D,MAAM,CAAC;MAChC,OAAOA,MAAM;IACjB;IAAC,QAAArF,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAsF,2BAAApF,CAAA;MAAA,YAAAA,CAAA,IAAwFmD,kBAAkB,EArK5BzE,EAAE,CAAAiD,QAAA,CAqK4Cf,YAAY,GArK1DlC,EAAE,CAAAiD,QAAA,CAqKqEjD,EAAE,CAAC2G,MAAM;IAAA,CAA6C;IAAA,QAAApF,EAAA,GACpN,IAAI,CAAC4B,KAAK,kBAtK6EnD,EAAE,CAAAoD,kBAAA;MAAAC,KAAA,EAsKYoB,kBAAkB;MAAAnB,OAAA,EAAlBmB,kBAAkB,CAAArD,IAAA;MAAAmC,UAAA,EAAc;IAAM,EAAG;EAC3J;EAAC,OA1EKkB,kBAAkB;AAAA;AA2ExB;EAAA,QAAA3C,SAAA,oBAAAA,SAAA;AAAA;AAIA;AACA;AACA;AACA;AACA,SAASuD,YAAYA,CAACD,OAAO,EAAE;EAC3B,OAAOA,OAAO,CACTtE,GAAG,CAAC8B,KAAK,IAAIA,KAAK,CAACgE,KAAK,CAAC,GAAG,CAAC,CAAC,CAC9BC,MAAM,CAAC,CAACC,EAAE,EAAEC,EAAE,KAAKD,EAAE,CAACrG,MAAM,CAACsG,EAAE,CAAC,CAAC,CACjCjG,GAAG,CAAC8B,KAAK,IAAIA,KAAK,CAACoE,IAAI,CAAC,CAAC,CAAC;AACnC;;AAEA;AACA;AACA,MAAMC,WAAW,GAAG;EAChBC,MAAM,EAAE,uBAAuB;EAC/BC,KAAK,EAAE,8CAA8C;EACrDC,MAAM,EAAE,+CAA+C;EACvDC,KAAK,EAAE,gDAAgD;EACvDC,MAAM,EAAE,qBAAqB;EAC7BC,OAAO,EAAE,qDAAqD,GAC1D,oDAAoD;EACxDC,MAAM,EAAE,4EAA4E,GAChF,4EAA4E;EAChFC,GAAG,EAAE,kDAAkD,GACnD,kDAAkD;EACtDC,eAAe,EAAE,mDAAmD;EACpEC,cAAc,EAAE,0EAA0E;EAC1FC,WAAW,EAAE,gDAAgD;EAC7DC,gBAAgB,EAAE,oDAAoD;EACtEC,eAAe,EAAE,4EAA4E;EAC7FC,YAAY,EAAE;AAClB,CAAC;;AAED;AACA;AACA;;AAEA,SAAStD,kBAAkB,EAAEwC,WAAW,EAAE/F,YAAY,EAAEgB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}