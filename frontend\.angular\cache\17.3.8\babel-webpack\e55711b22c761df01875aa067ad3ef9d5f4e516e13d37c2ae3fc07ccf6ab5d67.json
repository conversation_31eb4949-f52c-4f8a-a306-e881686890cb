{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"./homework-details/homework-details.component\";\nfunction HomeworksComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-homework-details\", 1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const homework_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"homework\", homework_r1);\n  }\n}\nexport class HomeworksComponent {\n  constructor() {\n    this.homeworks = [];\n  }\n  ngOnInit() {\n    console.log(this.homeworks);\n  }\n  static #_ = this.ɵfac = function HomeworksComponent_Factory(t) {\n    return new (t || HomeworksComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: HomeworksComponent,\n    selectors: [[\"app-homeworks\"]],\n    inputs: {\n      homeworks: \"homeworks\",\n      filter: \"filter\"\n    },\n    decls: 1,\n    vars: 1,\n    consts: [[4, \"ngFor\", \"ngForOf\"], [3, \"homework\"]],\n    template: function HomeworksComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, HomeworksComponent_div_0_Template, 2, 1, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngForOf\", ctx.homeworks);\n      }\n    },\n    dependencies: [i1.NgForOf, i2.HomeworkDetailsComponent],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "homework_r1", "HomeworksComponent", "constructor", "homeworks", "ngOnInit", "console", "log", "_", "_2", "selectors", "inputs", "filter", "decls", "vars", "consts", "template", "HomeworksComponent_Template", "rf", "ctx", "ɵɵtemplate", "HomeworksComponent_div_0_Template"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\homework\\homeworks\\homeworks.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\homework\\homeworks\\homeworks.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\r\nimport { Homework } from 'src/app/core/models/homework.model';\r\n\r\n@Component({\r\n  selector: 'app-homeworks',\r\n  templateUrl: './homeworks.component.html',\r\n  styleUrls: ['./homeworks.component.scss']\r\n})\r\nexport class HomeworksComponent implements OnInit {\r\n  @Input() homeworks: Homework[] = [];\r\n  @Input() filter: any;\r\n  \r\n  constructor() { }\r\n\r\n  ngOnInit(): void {\r\n    console.log(this.homeworks)\r\n  }\r\n\r\n}\r\n", "<div *ngFor=\"let homework of homeworks\">\r\n    <app-homework-details [homework]=\"homework\"></app-homework-details>\r\n</div>\r\n\r\n"], "mappings": ";;;;;ICAAA,EAAA,CAAAC,cAAA,UAAwC;IACpCD,EAAA,CAAAE,SAAA,8BAAmE;IACvEF,EAAA,CAAAG,YAAA,EAAM;;;;IADoBH,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAK,UAAA,aAAAC,WAAA,CAAqB;;;ADO/C,OAAM,MAAOC,kBAAkB;EAI7BC,YAAA;IAHS,KAAAC,SAAS,GAAe,EAAE;EAGnB;EAEhBC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACH,SAAS,CAAC;EAC7B;EAAC,QAAAI,CAAA,G;qBARUN,kBAAkB;EAAA;EAAA,QAAAO,EAAA,G;UAAlBP,kBAAkB;IAAAQ,SAAA;IAAAC,MAAA;MAAAP,SAAA;MAAAQ,MAAA;IAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCR/BvB,EAAA,CAAAyB,UAAA,IAAAC,iCAAA,iBAAwC;;;QAAd1B,EAAA,CAAAK,UAAA,YAAAmB,GAAA,CAAAf,SAAA,CAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}