{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { take } from 'rxjs/operators';\nimport { slideInOut } from 'src/app/helpers/my-animations';\nimport * as data from '../../../../core/models/data';\nimport { UserRole } from 'src/app/core/models/user.model';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"src/app/core/services/classroom.service\";\nimport * as i4 from \"src/app/core/services/lesson.service\";\nimport * as i5 from \"src/app/core/services/package.service\";\nimport * as i6 from \"src/app/core/services/note.service\";\nimport * as i7 from \"src/app/core/services/auth.service\";\nimport * as i8 from \"src/app/core/services/calendar.service\";\nimport * as i9 from \"../../../../shared/loader/loader.component\";\nimport * as i10 from \"../../../../shared/layout/g-level-circle/g-level-circle.component\";\nimport * as i11 from \"../../../../shared/layout/g-header-back-button/g-header-back-button.component\";\nconst _c0 = () => [\"lessons-menu-item-active\"];\nfunction ClassComponent_div_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13)(1, \"div\", 14);\n    i0.ɵɵelement(2, \"g-level-circle\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scale\", 1)(\"name\", ctx_r1.classroom == null ? null : ctx_r1.classroom.activeLevel);\n  }\n}\nfunction ClassComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5)(2, \"div\", 6)(3, \"div\", 7)(4, \"g-header-back-button\", 8);\n    i0.ɵɵlistener(\"click\", function ClassComponent_div_2_Template_g_header_back_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBack());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"span\", 9)(6, \"span\", 10);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 11);\n    i0.ɵɵtemplate(9, ClassComponent_div_2_div_9_Template, 3, 2, \"div\", 12);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate3(\"\", ctx_r1.classroom.language, \" \", ctx_r1.classroom.type === \"Trial\" ? \"Trial\" : \"\", \" Classroom: \", ctx_r1.classroomService.getFormattedStudentNames(ctx_r1.classroom.classroomStudents), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isTrial);\n  }\n}\nfunction ClassComponent_ng_container_3_ng_container_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 23);\n  }\n}\nfunction ClassComponent_ng_container_3_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"a\", 20);\n    i0.ɵɵelement(2, \"img\", 21);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ClassComponent_ng_container_3_ng_container_4_div_4_Template, 1, 0, \"div\", 22);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const menuItem_r3 = ctx.$implicit;\n    const last_r4 = ctx.last;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", menuItem_r3.url)(\"routerLinkActive\", i0.ɵɵpureFunction0(5, _c0));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", menuItem_r3.icon, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", menuItem_r3.label, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !last_r4);\n  }\n}\nfunction ClassComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 16)(2, \"div\", 17)(3, \"div\", 18);\n    i0.ɵɵtemplate(4, ClassComponent_ng_container_3_ng_container_4_Template, 5, 6, \"ng-container\", 19);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.classMenuItems);\n  }\n}\nfunction ClassComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 24)(2, \"div\", 25);\n    i0.ɵɵelement(3, \"app-loader\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"scale\", 1.6);\n  }\n}\nfunction ClassComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"router-outlet\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nexport let ClassComponent = /*#__PURE__*/(() => {\n  class ClassComponent {\n    constructor(location, router, activatedRoute, classroomService, lessonService, packageService, noteService, authService, calendarService) {\n      this.location = location;\n      this.router = router;\n      this.activatedRoute = activatedRoute;\n      this.classroomService = classroomService;\n      this.lessonService = lessonService;\n      this.packageService = packageService;\n      this.noteService = noteService;\n      this.authService = authService;\n      this.calendarService = calendarService;\n      this.subs = new SubSink();\n      this.classroomLessons = [];\n      this.classroomTitle = \"\";\n      this.showDetails = false;\n      // showDetails: boolean = false;\n      this.showInfo = true;\n      // showInfo: boolean = true;\n      this.showReports = false;\n      this.showRatings = false;\n      // showRatings: boolean = true;\n      this.isTrial = false;\n      this.classroomPackages = [];\n      this.showTabLinks = true;\n      this.isLoadingClassroom = true;\n      this.classMenuItems = [];\n      this.dummyLesson = data.dummyLesson.classroom;\n    }\n    ngOnInit() {\n      // let routeExtras = this.router.getCurrentNavigation()!.extras.state;\n      this.classroomId = this.activatedRoute.snapshot.paramMap.get(\"classroom_id\");\n      // console.log(routeExtras);\n      this.getClassroomPackages();\n      this.activatedRoute.params.pipe(take(1)).subscribe(params => this.isTrial = params['isTrial']);\n      // if (this.classroomService.sessionUserClassrooms.length > 0) {\n      //   this.classroom = this.classroomService.sessionUserClassrooms.filter(el => el.id == this.classroomId)[0]\n      //   this.classroomTitle = this.classroomService.getClassroomTitle(this.classroom);\n      // }\n      this.fetchClassroom();\n      this.initUpdateListener();\n      // this.router.events.subscribe(event => {\n      //   if (event instanceof NavigationStart) {\n      //     console.log(event.url.includes('teacher'));\n      //     if (event.url.includes('teacher')) {\n      //       this.showTabLinks = false;\n      //     } else {\n      //       this.showTabLinks = true;\n      //     }\n      //   }\n      // });\n      // this.activatedRoute.url.subscribe(url => {\n      //   console.log(url);\n      //   // Check if the current route is '/dashboard/classrooms/lessons/2/info'\n      //   if (url[0].path === 'dashboard' && url[1].path === 'classrooms' && url[2].path === 'lessons' && url[4].path !== 'teacher') {\n      //     this.showTabLinks = true;\n      //   } else {\n      //     this.showTabLinks = false;\n      //   }\n      // });\n    }\n    updateTabLinks() {\n      this.activatedRoute.firstChild.data.subscribe(data => {\n        console.log(data);\n        this.showTabLinks = data.showTabs;\n        if (this.activatedRoute.firstChild.snapshot.children.length > 0) {\n          this.activatedRoute.firstChild.firstChild.data.subscribe(childData => {\n            this.showTabLinks = childData.showTabs;\n            // Use childData to control visibility of specific tabs\n          });\n        }\n      });\n    }\n    ngAfterContentInit() {}\n    ngOnDestroy() {\n      // TODO: check if we need to clear the selected classroom from service \n      // this.classroomService.setSelectedClassroom({} as Classroom);\n      this.subs.unsubscribe();\n      this.classroomService.setSelectedClassroomUpdate(false);\n    }\n    goBack() {\n      this.location.back();\n    }\n    goToDetails() {\n      // this.showDetails = true;\n      // this.showInfo = false;\n      // this.showReports = false;\n      // this.showRatings = false;\n      this.router.navigate(['/dashboard/classrooms/lessons', this.classroom.id, 'details']);\n      // this.router.navigateByUrl('/dashboard/classrooms/lessons/2/details', {replaceUrl: true});\n    }\n    info() {\n      // this.showDetails = false;\n      // this.showInfo = true;\n      // this.showReports = false;\n      // this.showRatings = false;\n      this.router.navigate(['/dashboard/classrooms/lessons', 2, 'info']);\n      // this.router.navigateByUrl('/dashboard/classrooms/lessons/2/info', {replaceUrl: true});\n    }\n    reports() {\n      this.showDetails = false;\n      this.showInfo = false;\n      this.showReports = true;\n      this.showRatings = false;\n      this.router.navigateByUrl('/dashboard/classrooms/lessons/2/details', {\n        replaceUrl: true\n      });\n    }\n    ratings() {\n      this.showDetails = false;\n      this.showInfo = false;\n      this.showReports = false;\n      this.showRatings = true;\n    }\n    getClassroomPackages() {\n      this.packageService.getClassroomPackages(this.classroomId).subscribe(res => {\n        this.classroomPackages = res;\n      });\n    }\n    isActiveUrl() {\n      return this.router.url.startsWith(this.router.url);\n    }\n    getClassMenuItems(classroomId, userRole) {\n      const menuItems = [{\n        label: 'Class',\n        icon: '/assets/icons/classroom/classroom-group-icon.svg',\n        url: `/dashboard/classrooms/lessons/${classroomId}/details`,\n        role: [UserRole.STUDENT, UserRole.TEACHER]\n      }, {\n        label: 'Info',\n        icon: '/assets/icons/classroom/info.svg',\n        url: `/dashboard/classrooms/lessons/${classroomId}/info`,\n        role: [UserRole.STUDENT, UserRole.TEACHER]\n      }\n      // {\n      //   label: 'Progress',\n      //   icon: '/assets/icons/classroom/progress.svg',\n      //   url: `/dashboard/classrooms/lessons/${classroomId}/progress`,\n      //   role: [UserRole.STUDENT],\n      // },\n      ];\n      return menuItems.filter(item => item.role.includes(userRole));\n    }\n    fetchClassroom() {\n      this.subs.add(this.classroomService.getClassroom(this.classroomId).pipe(take(1)).subscribe(res => {\n        this.isLoadingClassroom = false;\n        this.classroom = res;\n        this.classroomService.setSelectedClassroom(this.classroom);\n        this.classroomService.setSelectedClassroomUpdate(true);\n        console.log(res);\n        // this.noteService.setCurrentSelectedClassroomId(parseInt(this.classroom!.id))\n        this.classroomTitle = this.classroomService.getClassroomTitle(this.classroom);\n        this.isTrial = this.classroom.type === 'Trial';\n        // this.lessonService.getClassroomLessons(this.classroom.id).pipe(take(1)).subscribe(res => {\n        //   this.classroomLessons = res;\n        // });\n        this.updateTabLinks();\n        this.classMenuItems = this.getClassMenuItems(this.classroom.id, this.authService.getUserRole());\n        this.router.events.subscribe(event => {\n          if (event instanceof NavigationEnd) {\n            this.updateTabLinks();\n          }\n        });\n      }));\n    }\n    initUpdateListener() {\n      this.subs.add(this.calendarService.updateListener.subscribe(res => {\n        if (res) {\n          this.fetchClassroom();\n          this.getClassroomPackages();\n        }\n      }));\n    }\n    static #_ = this.ɵfac = function ClassComponent_Factory(t) {\n      return new (t || ClassComponent)(i0.ɵɵdirectiveInject(i1.Location), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.ClassroomService), i0.ɵɵdirectiveInject(i4.LessonService), i0.ɵɵdirectiveInject(i5.PackageService), i0.ɵɵdirectiveInject(i6.NoteService), i0.ɵɵdirectiveInject(i7.AuthService), i0.ɵɵdirectiveInject(i8.CalendarService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ClassComponent,\n      selectors: [[\"app-class\"]],\n      decls: 6,\n      vars: 4,\n      consts: [[1, \"card\", \"mb-4\"], [1, \"border-round-xl\"], [\"class\", \"\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"\"], [1, \"border-round-xl\", \"overflow-hidden\"], [1, \"block-header\", \"gradient-header\", \"relative\", \"justify-content-center\", \"lg:flex\"], [1, \"absolute\", \"pl-1\", \"md:pl-2\", \"left-0\"], [3, \"click\"], [1, \"block-title\", \"pl-2\", \"sm:pl-0\"], [1, \"text-0\", \"capitalize\", \"max-w-30rem\"], [1, \"flex\", \"justify-content-end\"], [\"class\", \"lesson-level\", 4, \"ngIf\"], [1, \"lesson-level\"], [1, \"absolute\", \"right-1\", \"v-align\", 2, \"right\", \"10px\"], [3, \"scale\", \"name\"], [1, \"lessons\", \"mt-4\"], [\"id\", \"class\"], [1, \"lessons-menu\", \"flex\", \"align-items-center\"], [4, \"ngFor\", \"ngForOf\"], [1, \"lessons-menu-item\", \"flex\", \"align-items-center\", \"gap-2\", \"text-primary\", 3, \"routerLink\", \"routerLinkActive\"], [3, \"src\"], [\"class\", \"seperator\", 4, \"ngIf\"], [1, \"seperator\"], [1, \"relative\", \"h-20rem\"], [1, \"abs-centered\"], [3, \"scale\"]],\n      template: function ClassComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵtemplate(2, ClassComponent_div_2_Template, 10, 4, \"div\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(3, ClassComponent_ng_container_3_Template, 5, 1, \"ng-container\", 3)(4, ClassComponent_ng_container_4_Template, 4, 1, \"ng-container\", 3)(5, ClassComponent_ng_container_5_Template, 2, 0, \"ng-container\", 3);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.classroom);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showTabLinks && ctx.classMenuItems.length > 0 && !ctx.isTrial);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoadingClassroom);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoadingClassroom);\n        }\n      },\n      dependencies: [i1.NgForOf, i1.NgIf, i2.RouterOutlet, i2.RouterLink, i2.RouterLinkActive, i9.LoaderComponent, i10.GLevelCircleComponent, i11.GHeaderBackButtonComponent],\n      styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}.gradient-header[_ngcontent-%COMP%]{background-image:url(/assets/images/classroom-gradient-bg.png);background-repeat:no-repeat;background-position:center center;min-height:60px;border-radius:8px;box-shadow:0 3px 6px #00000029}.lessons[_ngcontent-%COMP%]{width:100%;font-size:17px}.lessons-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;width:100%;font-family:Raleway-Bold;padding:15px 0}.lessons-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]{display:flex;align-items:center;cursor:pointer}.lessons-header[_ngcontent-%COMP%]   .classroom-title[_ngcontent-%COMP%]{text-align:center}.lessons-header[_ngcontent-%COMP%]   .lesson-level[_ngcontent-%COMP%]{background-color:var(--main-color);color:var(--white);text-align:center;transition:all .3s linear;width:33px;height:33px;line-height:33px;border-radius:50%}.lessons-menu[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center}.lessons-menu[_ngcontent-%COMP%]   .lessons-menu-item[_ngcontent-%COMP%]{color:var(--main-color);cursor:pointer;text-align:center;position:relative;padding:0 12px}.lessons-menu[_ngcontent-%COMP%]   .lessons-menu-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{height:16px}.lessons-menu[_ngcontent-%COMP%]   .lessons-menu-item-active[_ngcontent-%COMP%], .lessons-menu[_ngcontent-%COMP%]   .lessons-menu-item[_ngcontent-%COMP%]:hover{font-weight:700}.lessons-menu[_ngcontent-%COMP%]   .lessons-menu-item-active[_ngcontent-%COMP%]:after, .lessons-menu[_ngcontent-%COMP%]   .lessons-menu-item[_ngcontent-%COMP%]:hover:after{content:\\\"\\\";position:absolute;bottom:-5px;border-bottom:1px solid var(--primary-color);height:2px;width:100%;left:0}.lessons-menu[_ngcontent-%COMP%]   .seperator[_ngcontent-%COMP%]{height:20px;width:1px;background-color:var(--primary-color);margin:0 14px}\"],\n      data: {\n        animation: [slideInOut]\n      }\n    });\n  }\n  return ClassComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}