{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport class AuthGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate(route, state) {\n    const isAuth = this.authService.getIsAuth();\n    this.authService.saveLeadDynoAffiliateIdFromRoute(route);\n    if (!isAuth) {\n      this.router.navigate(['/auth/login'], {\n        queryParams: {\n          returnUrl: state.url\n        }\n      });\n    }\n    return isAuth; //allow to go to router if true\n  }\n  static #_ = this.ɵfac = function AuthGuard_Factory(t) {\n    return new (t || AuthGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthGuard,\n    factory: AuthGuard.ɵfac\n  });\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "route", "state", "isAuth", "getIsAuth", "saveLeadDynoAffiliateIdFromRoute", "navigate", "queryParams", "returnUrl", "url", "_", "i0", "ɵɵinject", "i1", "AuthService", "i2", "Router", "_2", "factory", "ɵfac"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\guards\\auth.guard.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\";\r\nimport { ActivatedRouteSnapshot, Router, RouterStateSnapshot } from \"@angular/router\";\r\nimport { Observable } from \"rxjs\";\r\nimport { AuthService } from \"../services/auth.service\";\r\n@Injectable()\r\nexport class AuthGuard  {\r\n    constructor(private authService: AuthService, private router: Router) {}\r\n    canActivate(\r\n        route: ActivatedRouteSnapshot,\r\n        state: RouterStateSnapshot\r\n    ): boolean | Observable<boolean> | Promise<boolean> {\r\n        const isAuth = this.authService.getIsAuth();\r\n        this.authService.saveLeadDynoAffiliateIdFromRoute(route);\r\n        if(!isAuth){\r\n            this.router.navigate(['/auth/login'], { queryParams: { returnUrl: state.url }});\r\n        }\r\n        return isAuth//allow to go to router if true\r\n\r\n    }\r\n\r\n}"], "mappings": ";;;AAKA,OAAM,MAAOA,SAAS;EAClBC,YAAoBC,WAAwB,EAAUC,MAAc;IAAhD,KAAAD,WAAW,GAAXA,WAAW;IAAuB,KAAAC,MAAM,GAANA,MAAM;EAAW;EACvEC,WAAWA,CACPC,KAA6B,EAC7BC,KAA0B;IAE1B,MAAMC,MAAM,GAAG,IAAI,CAACL,WAAW,CAACM,SAAS,EAAE;IAC3C,IAAI,CAACN,WAAW,CAACO,gCAAgC,CAACJ,KAAK,CAAC;IACxD,IAAG,CAACE,MAAM,EAAC;MACP,IAAI,CAACJ,MAAM,CAACO,QAAQ,CAAC,CAAC,aAAa,CAAC,EAAE;QAAEC,WAAW,EAAE;UAAEC,SAAS,EAAEN,KAAK,CAACO;QAAG;MAAE,CAAC,CAAC;IACnF;IACA,OAAON,MAAM;EAEjB;EAAC,QAAAO,CAAA,G;qBAbQd,SAAS,EAAAe,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAATrB,SAAS;IAAAsB,OAAA,EAATtB,SAAS,CAAAuB;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}