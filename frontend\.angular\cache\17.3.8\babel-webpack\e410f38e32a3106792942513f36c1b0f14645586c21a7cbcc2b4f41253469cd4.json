{"ast": null, "code": "import { inject, signal } from '@angular/core';\nimport { serverTimestamp, Timestamp } from 'firebase/firestore';\nimport * as moment from 'moment';\nimport { Observable, combineLatest, firstValueFrom, from } from 'rxjs';\nimport { map, switchMap } from 'rxjs/operators';\nimport { UserService } from './user.service';\nimport { GeneralService } from './general.service';\nimport { environment } from 'src/environments/environment';\nimport { getAuth, signInAnonymously } from \"firebase/auth\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/fire/compat/firestore\";\nimport * as i2 from \"@angular/fire/compat/auth\";\nimport * as i3 from \"@angular/common/http\";\nconst BACKEND_URL = environment.apiUrl;\nconst BACKEND_LMS_URL = environment.apiUrl + \"/LMS/\";\nexport class ChatService {\n  constructor(db, afAuth, http) {\n    this.db = db;\n    this.afAuth = afAuth;\n    this.http = http;\n    this.userService = inject(UserService);\n    this.generalService = inject(GeneralService);\n    this.isChatEnabled = signal(environment.isChatEnabled);\n    this.showUploadFile = signal(false);\n    this.unreadCount = signal(0);\n    this.currentRoom = signal({});\n    this.showBubbleChat = signal(false);\n    this.dbPath = '/chats';\n    this.usersRef = this.db.collection('users');\n    this.chatsRef = db.collection(this.dbPath);\n    this.usersRef = db.collection('/users');\n  }\n  deleteChatFilesFromLibraryFolder(fileUrl) {\n    console.log(fileUrl);\n    return this.http.post(BACKEND_LMS_URL + \"DeleteChatFilesFromLibraryFolder?fileUrl=\" + fileUrl, {});\n  }\n  // Sign in anonymously\n  signInAnonymously() {\n    return new Observable(observer => {\n      this.afAuth.signInAnonymously().then(userCredential => {\n        console.log('Signed in anonymously:', userCredential.user);\n        observer.next(userCredential);\n        observer.complete();\n      }).catch(error => {\n        console.error('Error signing in anonymously:', error);\n        observer.error(error);\n      });\n    });\n  }\n  signInAnonym() {\n    const auth = getAuth();\n    signInAnonymously(auth).then(() => {\n      // Signed in..\n    }).catch(error => {\n      const errorCode = error.code;\n      const errorMessage = error.message;\n      // ...\n    });\n  }\n  getLastMessage(classroomId) {\n    const chatRef = this.chatsRef.doc(`${classroomId}/`);\n    return chatRef.snapshotChanges().pipe(map(action => {\n      const data = action.payload.data();\n      const id = action.payload.id;\n      return {\n        id,\n        ...data\n      };\n    }));\n  }\n  // Fetch all chats for a classroom\n  getClassroomChats(classroomId) {\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    return this.chatsRef.doc(classroomId).collection('chats').snapshotChanges().pipe(map(actions => actions.map(a => {\n      const data = a.payload.doc.data();\n      const id = a.payload.doc.id;\n      return {\n        id,\n        ...data\n      };\n    })));\n  }\n  // Fetch messages for a specific chat room\n  getChatMessages(classroomId) {\n    return this.db.collection(`${this.dbPath}/${classroomId}/messages`, ref => ref.orderBy('timestamp', 'asc')).snapshotChanges().pipe(map(actions => actions.map(a => {\n      const data = a.payload.doc.data();\n      const id = a.payload.doc.id;\n      return {\n        id,\n        ...data\n      };\n    })));\n  }\n  // Create a new chat room\n  createChatRoom(classroomId, participants) {\n    const chat = {\n      classroomId: classroomId,\n      createdAt: moment().format('YYYY-MM-DD HH:mm:ss'),\n      lastMessage: '',\n      lastMessageAt: serverTimestamp(),\n      lastMessageFrom: '',\n      lastMessageFromName: '',\n      lastMessageFromImage: '',\n      lastMessageIsFile: false,\n      lastMessageDeleted: false,\n      lastMessageId: '',\n      lastProcessedMessageId: ''\n    };\n    return this.chatsRef.add(chat);\n  }\n  // Add a new message to a chat room\n  addMessage(classroomId, message, user) {\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    const messageRef = chatRef.collection('messages').doc();\n    const messageId = messageRef.ref.id;\n    return firstValueFrom(chatRef.get()).then(snapshot => {\n      const updateData = {\n        createdAt: moment().format('YYYY-MM-DD HH:mm:ss'),\n        lastMessage: message.content,\n        lastMessageAt: serverTimestamp(),\n        classroomId: classroomId,\n        lastMessageFrom: message.senderId,\n        lastMessageFromName: this.generalService.getPersonFullName(user),\n        lastMessageFromImage: this.userService.getUserPhoto(user?.avatarUrl),\n        lastMessageIsFile: message.isFileFromLibrary || message.isUploadFile,\n        lastMessageDeleted: false,\n        lastMessageId: messageId,\n        // Accessing id through ref\n        lastProcessedMessageId: ''\n      };\n      if (!snapshot.exists) {\n        // Retrieve users in the chat, including teacher and students\n        const usersInChat = [this.currentRoom().teacher, ...this.currentRoom().classroomStudents];\n        const updatedUnreadCounts = {};\n        usersInChat.forEach(chatUser => {\n          if (chatUser.aspUserId !== message.senderId) {\n            updatedUnreadCounts[chatUser.aspUserId] = 1; // Initialize unread count to 1 for new chat\n          }\n        });\n        updateData.unreadCounts = updatedUnreadCounts;\n        return chatRef.set(updateData).then(() => {\n          return messageRef.set(message).then(() => {\n            return messageRef.update({\n              id: messageRef.ref.id\n            }).then(() => {\n              return this.updateChatLastMessage(chatRef, classroomId);\n            });\n          });\n        });\n      } else {\n        // Existing chat room, update last message and unread counts\n        return this.updateLastMessage(chatRef, message, user, false, messageId).then(() => {\n          return messageRef.set(message).then(() => {\n            return messageRef.update({\n              id: messageRef.ref.id\n            });\n          });\n        });\n      }\n    });\n  }\n  updateLastMessage(chatRef, updatedMessage, user, isLastMessageDeleted = false, messageId = '') {\n    const messageRef = chatRef.collection('messages').doc();\n    const updateData = {\n      lastMessage: updatedMessage.content,\n      lastMessageAt: serverTimestamp(),\n      lastMessageFrom: updatedMessage.senderId,\n      lastMessageFromImage: this.userService.getUserPhoto(user?.avatarUrl),\n      lastMessageFromName: this.generalService.getPersonFullName(user),\n      lastMessageIsFile: updatedMessage.isFileFromLibrary || updatedMessage.isUploadFile,\n      lastMessageDeleted: isLastMessageDeleted,\n      lastMessageId: messageId\n    };\n    return firstValueFrom(chatRef.get()).then(snapshot => {\n      console.log('current room', this.currentRoom());\n      const updatedUnreadCounts = {};\n      // Retrieve users in the chat, including teacher and students\n      const usersInChat = [this.currentRoom().teacher, ...this.currentRoom().classroomStudents];\n      usersInChat.forEach(chatUser => {\n        if (chatUser.aspUserId !== updatedMessage.senderId) {\n          updatedUnreadCounts[chatUser.aspUserId] = (snapshot.exists ? snapshot.data().unreadCounts?.[chatUser.aspUserId] || 0 : 0) + 1;\n        }\n      });\n      updateData.unreadCounts = updatedUnreadCounts;\n      return chatRef.update(updateData);\n    });\n  }\n  editMessage(classroomId, messageId, updatedMessage, user, updateLastMessage = false) {\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    const messageRef = chatRef.collection('messages').doc(`${messageId}`);\n    const messagesRef = chatRef.collection('messages', ref => ref.orderBy('timestamp', 'desc').limit(1));\n    const lastMessageDoc = messagesRef.ref.doc();\n    const lastMessageId = lastMessageDoc.id;\n    console.log(lastMessageId);\n    return messageRef.get().toPromise().then(snapshot => {\n      if (snapshot.exists) {\n        return messageRef.update({\n          ...updatedMessage\n        }).then(() => {\n          if (updateLastMessage) {\n            return this.updateLastMessage(chatRef, updatedMessage, user, false, updatedMessage.id);\n          }\n          return Promise.resolve();\n        });\n      } else {\n        throw new Error('Message does not exist');\n      }\n    });\n  }\n  getLastUnreadMessagesCountForUser(classroomId, userId) {\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    return chatRef.valueChanges().pipe(map(data => {\n      const unreadCounts = data?.unreadCounts;\n      return unreadCounts ? unreadCounts[userId] || 0 : 0;\n    }));\n  }\n  markLastMessagesAsRead(classroomId, userId) {\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    return this.markLastMessagesAsReadInLastMessage(chatRef, userId);\n  }\n  markLastMessagesAsReadInLastMessage(chatRef, userId) {\n    return chatRef.get().toPromise().then(snapshot => {\n      if (snapshot.exists) {\n        const data = snapshot.data();\n        const updatedUnreadCounts = {\n          ...data.unreadCounts,\n          [userId]: 0\n        };\n        return chatRef.update({\n          unreadCounts: updatedUnreadCounts\n        });\n      }\n      return Promise.resolve(); // If the document doesn't exist, there's nothing to update.\n    });\n  }\n  // Delete a specific message from a chat room\n  deleteMessage(classroomId, message, user, updateLastMessage = false) {\n    console.log(classroomId);\n    console.log(message);\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    const messageRef = chatRef.collection('messages').doc(message.id);\n    return messageRef.delete().then(() => {\n      if (updateLastMessage) {\n        return this.getLatestMessage(classroomId).then(latestMessage => {\n          return this.updateLastMessage(chatRef, {\n            ...message,\n            content: 'Message Deleted'\n          }, user, true, latestMessage?.id).then(() => {\n            // return this.updateChatLastMessage(chatRef);\n          });\n        });\n      } else {\n        return this.updateChatLastMessage(chatRef, classroomId);\n      }\n    });\n  }\n  updateChatLastMessage(chatRef, classroomId) {\n    return this.getLatestMessage(classroomId).then(snapshot => {\n      console.log(snapshot);\n      if (snapshot) {\n        const latestMessage = snapshot;\n        return chatRef.update({\n          lastMessage: latestMessage.content,\n          lastMessageAt: latestMessage.createdAt,\n          lastMessageFrom: latestMessage.senderId,\n          lastMessageFromName: latestMessage.senderName,\n          lastMessageFromImage: latestMessage.senderImage,\n          lastMessageIsFile: latestMessage.isFileFromLibrary || latestMessage.isUploadFile,\n          lastMessageDeleted: false,\n          lastMessageId: latestMessage.id\n        });\n      } else {\n        // Handle case where there are no messages left in the collection\n        return chatRef.update({\n          lastMessage: '',\n          lastMessageAt: null,\n          lastMessageFrom: '',\n          lastMessageFromName: '',\n          lastMessageFromImage: '',\n          lastMessageIsFile: false,\n          lastMessageDeleted: false,\n          lastMessageId: ''\n        });\n      }\n    });\n  }\n  getLatestMessage(classroomId) {\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    return firstValueFrom(chatRef.collection('messages', ref => ref.orderBy('timestamp', 'desc').limit(1)).get()).then(snapshot => {\n      if (!snapshot.empty) {\n        const latestMessage = snapshot.docs[0].data();\n        return latestMessage;\n      } else {\n        return null; // No messages found\n      }\n    });\n  }\n  // Update a chat room document\n  updateChatRoom(classroomId, data) {\n    return this.chatsRef.doc(classroomId).update(data);\n  }\n  // Delete a chat room document\n  deleteChatRoom(classroomId) {\n    return this.chatsRef.doc(classroomId).delete();\n  }\n  markMessagesAsRead(classroomId, userId) {\n    return this.db.collection(`${this.dbPath}/${classroomId}/messages`).get().toPromise().then(querySnapshot => {\n      const batch = this.db.firestore.batch();\n      querySnapshot.forEach(doc => {\n        const data = doc.data();\n        // Ensure readStatus is a valid map or initialize it\n        const readStatusUpdate = data.readStatus || {};\n        // Add or update the userId in the readStatus map\n        readStatusUpdate[userId] = true;\n        // Update the document with the new or modified readStatus map\n        batch.update(doc.ref, {\n          readStatus: readStatusUpdate\n        });\n      });\n      return batch.commit();\n    });\n  }\n  // Mark messages as read\n  markAllMessagesAsRead(classroomId) {\n    return this.db.collection(`${this.dbPath}/${classroomId}/messages`).get().toPromise().then(querySnapshot => {\n      const batch = this.db.firestore.batch();\n      querySnapshot.forEach(doc => {\n        const messageData = doc.data();\n        const updatedReadStatus = {};\n        // Set readStatus for all users in the message's readStatus map to true\n        for (const userId in messageData.readStatus) {\n          if (messageData.readStatus.hasOwnProperty(userId)) {\n            const a = `readStatus.${userId}`;\n            updatedReadStatus[a] = true;\n          }\n        }\n        batch.update(doc.ref, updatedReadStatus);\n      });\n      return batch.commit();\n    });\n  }\n  // Get unread messages count for a user in a classroom\n  getUnreadMessagesCount(classroomId, userId) {\n    return this.db.collection(`${this.dbPath}/${classroomId}/messages`).snapshotChanges().pipe(map(actions => actions.filter(action => {\n      const data = action.payload.doc.data();\n      return !data.readStatus.hasOwnProperty(userId);\n    }).length));\n  }\n  // Get total unread messages count for a user across all classrooms\n  getUnreadMessagesCountForUser(userId) {\n    return this.chatsRef.snapshotChanges().pipe(switchMap(chatSnapshots => {\n      const chatObservables = chatSnapshots.map(snapshot => {\n        const chatId = snapshot.payload.doc.id;\n        return this.getUnreadMessagesCount(chatId, userId);\n      });\n      return combineLatest(chatObservables);\n    }), map(unreadCounts => unreadCounts.reduce((acc, count) => acc + count, 0)));\n  }\n  // Create Firebase Account and return as Observable\n  createFirebaseUser(email, password) {\n    return from(this.afAuth.createUserWithEmailAndPassword(email, password));\n  }\n  // Firebase Sign in as Observable\n  signInFirebaseUser(email, password) {\n    return from(this.afAuth.signInWithEmailAndPassword(email, password));\n  }\n  checkFirebaseUserExistsByEmail(email) {\n    return this.afAuth.fetchSignInMethodsForEmail(email).then(signInMethods => {\n      console.log('Sign-in methods:', signInMethods);\n      return signInMethods.length > 0;\n    }).catch(error => {\n      console.error('Error checking email:', error);\n      return false;\n    });\n  }\n  signOutFromFirebase() {\n    this.afAuth.signOut().then(() => {\n      console.log('User signed out');\n    }).catch(error => {\n      console.error('Error signing out:', error);\n    });\n  }\n  // Method to check if user is logged in\n  isLoggedInInFireBase() {\n    return this.afAuth.authState.pipe(map(user => !!user) // map user object to boolean (true if user exists, false otherwise)\n    );\n  }\n  // Or, to get the user object directly\n  getFireBaseUser() {\n    return this.afAuth.authState;\n  }\n  setUserStatus(userId, status) {\n    const userRef = this.usersRef.doc(userId);\n    return userRef.set({\n      status,\n      lastOnlineTime: Timestamp.now()\n    }, {\n      merge: true\n    });\n  }\n  setUserLastOnlineTime(userId, time) {\n    const userRef = this.usersRef.doc(userId);\n    return userRef.set({\n      status: 'online',\n      lastOnlineTime: time\n    }, {\n      merge: true\n    });\n  }\n  getUserStatus(userId) {\n    return this.usersRef.doc(userId).valueChanges().pipe(map(user => user?.status || 'offline'));\n  }\n  getOnlineUsers() {\n    return this.db.collection('/users', ref => ref.where('status', '==', 'online')).snapshotChanges().pipe(map(actions => actions.map(a => {\n      const data = a.payload.doc.data();\n      const id = a.payload.doc.id;\n      return {\n        ...data,\n        id\n      }; // Corrected merge of id and data\n    })));\n  }\n  getOnlineUsersLastOnlineTime() {\n    return this.db.collection('/users').snapshotChanges().pipe(map(actions => {\n      const currentTime = Timestamp.now();\n      return actions.map(a => {\n        const data = a.payload.doc.data();\n        const id = a.payload.doc.id;\n        const lastOnlineTime = data.lastOnlineTime;\n        if (!currentTime.seconds || !lastOnlineTime.seconds) {\n          return {\n            ...data,\n            id,\n            status: 'offline'\n          };\n        }\n        const timeDifference = currentTime.seconds - lastOnlineTime.seconds;\n        // Check if the user was online within the last 10 seconds\n        if (timeDifference <= 10) {\n          return {\n            ...data,\n            id,\n            status: 'online'\n          };\n        } else {\n          return {\n            ...data,\n            id,\n            status: 'offline'\n          };\n        }\n      });\n    }));\n  }\n  static #_ = this.ɵfac = function ChatService_Factory(t) {\n    return new (t || ChatService)(i0.ɵɵinject(i1.AngularFirestore), i0.ɵɵinject(i2.AngularFireAuth), i0.ɵɵinject(i3.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ChatService,\n    factory: ChatService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["inject", "signal", "serverTimestamp", "Timestamp", "moment", "Observable", "combineLatest", "firstValueFrom", "from", "map", "switchMap", "UserService", "GeneralService", "environment", "getAuth", "signInAnonymously", "BACKEND_URL", "apiUrl", "BACKEND_LMS_URL", "ChatService", "constructor", "db", "afAuth", "http", "userService", "generalService", "isChatEnabled", "showUploadFile", "unreadCount", "currentRoom", "showBubbleChat", "db<PERSON><PERSON>", "usersRef", "collection", "chatsRef", "deleteChatFilesFromLibraryFolder", "fileUrl", "console", "log", "post", "observer", "then", "userCredential", "user", "next", "complete", "catch", "error", "signInAnonym", "auth", "errorCode", "code", "errorMessage", "message", "getLastMessage", "classroomId", "chatRef", "doc", "snapshotChanges", "pipe", "action", "data", "payload", "id", "getClassroomChats", "actions", "a", "getChatMessages", "ref", "orderBy", "createChatRoom", "participants", "chat", "createdAt", "format", "lastMessage", "lastMessageAt", "lastMessageFrom", "lastMessageFromName", "lastMessageFromImage", "lastMessageIsFile", "lastMessageDeleted", "lastMessageId", "lastProcessedMessageId", "add", "addMessage", "messageRef", "messageId", "get", "snapshot", "updateData", "content", "senderId", "getPersonFullName", "getUserPhoto", "avatarUrl", "isFileFromLibrary", "isUploadFile", "exists", "usersInChat", "teacher", "classroomStudents", "updatedUnreadCounts", "for<PERSON>ach", "chatUser", "aspUserId", "unreadCounts", "set", "update", "updateChatLastMessage", "updateLastMessage", "updatedMessage", "isLastMessageDeleted", "editMessage", "messagesRef", "limit", "lastMessageDoc", "to<PERSON>romise", "Promise", "resolve", "Error", "getLastUnreadMessagesCountForUser", "userId", "valueChanges", "markLastMessagesAsRead", "markLastMessagesAsReadInLastMessage", "deleteMessage", "delete", "getLatestMessage", "latestMessage", "sender<PERSON>ame", "senderImage", "empty", "docs", "updateChatRoom", "deleteChatRoom", "markMessagesAsRead", "querySnapshot", "batch", "firestore", "readStatusUpdate", "readStatus", "commit", "markAllMessagesAsRead", "messageData", "updatedReadStatus", "hasOwnProperty", "getUnreadMessagesCount", "filter", "length", "getUnreadMessagesCountForUser", "chatSnapshots", "chatObservables", "chatId", "reduce", "acc", "count", "createFirebaseUser", "email", "password", "createUserWithEmailAndPassword", "signInFirebaseUser", "signInWithEmailAndPassword", "checkFirebaseUserExistsByEmail", "fetchSignInMethodsForEmail", "signInMethods", "signOutFromFirebase", "signOut", "isLoggedInInFireBase", "authState", "getFireBaseUser", "setUserStatus", "status", "userRef", "lastOnlineTime", "now", "merge", "setUserLastOnlineTime", "time", "getUserStatus", "getOnlineUsers", "where", "getOnlineUsersLastOnlineTime", "currentTime", "seconds", "timeDifference", "_", "i0", "ɵɵinject", "i1", "AngularFirestore", "i2", "AngularFireAuth", "i3", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\services\\chat.service.ts"], "sourcesContent": ["import { Injectable, inject, signal } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { AngularFirestore, AngularFirestoreCollection, AngularFirestoreDocument } from '@angular/fire/compat/firestore';\r\nimport { FieldValue, serverTimestamp, Timestamp } from 'firebase/firestore';\r\nimport * as moment from 'moment';\r\nimport { Observable, combineLatest, defer, firstValueFrom, forkJoin, from } from 'rxjs';\r\nimport { map, switchMap } from 'rxjs/operators';\r\nimport { UserService } from './user.service';\r\nimport { GeneralService } from './general.service';\r\nimport { environment } from 'src/environments/environment';\r\nconst BACKEND_URL = environment.apiUrl;\r\nconst BACKEND_LMS_URL = environment.apiUrl + \"/LMS/\";\r\nimport { getAuth, signInAnonymously } from \"firebase/auth\";\r\nimport { AngularFireAuth } from '@angular/fire/compat/auth';\r\n\r\ninterface Chat {\r\n  createdAt: string;\r\n  lastMessage: string;\r\n  lastMessageAt: FieldValue;\r\n  lastMessageFrom: string;\r\n  lastMessageFromName: string;\r\n  lastMessageFromImage: string;\r\n  lastMessageIsFile: boolean;\r\n  lastMessageDeleted: boolean;\r\n  lastMessageId: string;\r\n  lastProcessedMessageId: string;\r\n  classroomId: string;\r\n}\r\n\r\nexport interface ChatUserStatus {\r\n  id?: string;\r\n  status: 'online' | 'offline';\r\n  lastOnlineTime: FieldValue;\r\n}\r\n\r\n\r\nexport interface Message {\r\n  id?: string;\r\n  senderId: string;\r\n  content: string;\r\n  timestamp: FieldValue;\r\n  readBy: string[];\r\n  isFileFromLibrary: boolean;\r\n  isUploadFile: boolean;\r\n  readStatus: {}\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ChatService {\r\n  userService = inject(UserService);\r\n  generalService = inject(GeneralService);\r\n  isChatEnabled = signal(environment.isChatEnabled);\r\n  showUploadFile = signal(false);\r\n  unreadCount = signal(0);\r\n  currentRoom = signal({} as any);\r\n  showBubbleChat = signal(false);\r\n  private dbPath = '/chats';\r\n  chatsRef: AngularFirestoreCollection<Chat>;\r\n  private usersRef: AngularFirestoreCollection<ChatUserStatus> = this.db.collection('users');\r\n\r\n\r\n  constructor(private db: AngularFirestore, private afAuth: AngularFireAuth, private http: HttpClient) {\r\n    this.chatsRef = db.collection(this.dbPath);\r\n    this.usersRef = db.collection('/users');\r\n  }\r\n\r\n\r\n  deleteChatFilesFromLibraryFolder(fileUrl: any): Observable<any> {\r\n    console.log(fileUrl)\r\n    return this.http.post<any>(BACKEND_LMS_URL + \"DeleteChatFilesFromLibraryFolder?fileUrl=\" + fileUrl, {})\r\n  }\r\n\r\n  // Sign in anonymously\r\n  signInAnonymously(): Observable<any> {\r\n    return new Observable(observer => {\r\n      this.afAuth.signInAnonymously()\r\n        .then(userCredential => {\r\n          console.log('Signed in anonymously:', userCredential.user);\r\n          observer.next(userCredential);\r\n          observer.complete();\r\n        })\r\n        .catch(error => {\r\n          console.error('Error signing in anonymously:', error);\r\n          observer.error(error);\r\n        });\r\n    });\r\n  }\r\n\r\n  signInAnonym() {\r\n    const auth = getAuth();\r\n    signInAnonymously(auth)\r\n      .then(() => {\r\n        // Signed in..\r\n      })\r\n      .catch((error) => {\r\n        const errorCode = error.code;\r\n        const errorMessage = error.message;\r\n        // ...\r\n      });\r\n  }\r\n\r\n  getLastMessage(classroomId: string): Observable<Message> {\r\n    const chatRef = this.chatsRef.doc<Message>(`${classroomId}/`);\r\n\r\n    return chatRef.snapshotChanges().pipe(\r\n      map(action => {\r\n        const data = action.payload.data() as Message;\r\n        const id = action.payload.id;\r\n        return { id, ...data };\r\n      })\r\n    );\r\n  }\r\n\r\n  // Fetch all chats for a classroom\r\n  getClassroomChats(classroomId: string): Observable<Chat[]> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    return this.chatsRef.doc(classroomId).collection<Chat>('chats')\r\n      .snapshotChanges().pipe(\r\n        map(actions => actions.map(a => {\r\n          const data = a.payload.doc.data() as Chat;\r\n          const id = a.payload.doc.id;\r\n          return { id, ...data };\r\n        }))\r\n      );\r\n  }\r\n\r\n  // Fetch messages for a specific chat room\r\n  getChatMessages(classroomId: string): Observable<Message[]> {\r\n    return this.db.collection<Message>(`${this.dbPath}/${classroomId}/messages`, ref =>\r\n      ref.orderBy('timestamp', 'asc')\r\n    ).snapshotChanges().pipe(\r\n      map(actions => actions.map(a => {\r\n        const data = a.payload.doc.data() as Message;\r\n        const id = a.payload.doc.id;\r\n        return { id, ...data };\r\n      }))\r\n    );\r\n  }\r\n\r\n  // Create a new chat room\r\n  createChatRoom(classroomId: string, participants: string[]) {\r\n    const chat: Chat = {\r\n      classroomId: classroomId,\r\n      createdAt: moment().format('YYYY-MM-DD HH:mm:ss'),\r\n      lastMessage: '',\r\n      lastMessageAt: serverTimestamp(),\r\n      lastMessageFrom: '',\r\n      lastMessageFromName: '',\r\n      lastMessageFromImage: '',\r\n      lastMessageIsFile: false,\r\n      lastMessageDeleted: false,\r\n      lastMessageId: '',\r\n      lastProcessedMessageId: '',\r\n    };\r\n    return this.chatsRef.add(chat);\r\n  }\r\n\r\n  // Add a new message to a chat room\r\n  addMessage(classroomId: string, message: Message, user: any): Promise<void> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    const messageRef = chatRef.collection('messages').doc();\r\n\r\n    const messageId = (messageRef.ref.id);\r\n    return firstValueFrom(chatRef.get()).then(snapshot => {\r\n      const updateData: any = {\r\n        createdAt: moment().format('YYYY-MM-DD HH:mm:ss'),\r\n        lastMessage: message.content,\r\n        lastMessageAt: serverTimestamp(),\r\n        classroomId: classroomId,\r\n        lastMessageFrom: message.senderId,\r\n        lastMessageFromName: this.generalService.getPersonFullName(user),\r\n        lastMessageFromImage: this.userService.getUserPhoto(user?.avatarUrl),\r\n        lastMessageIsFile: message.isFileFromLibrary || message.isUploadFile,\r\n        lastMessageDeleted: false,\r\n        lastMessageId: messageId, // Accessing id through ref\r\n        lastProcessedMessageId: '',\r\n      };\r\n\r\n      if (!snapshot.exists) {\r\n        // Retrieve users in the chat, including teacher and students\r\n        const usersInChat = [this.currentRoom().teacher, ...this.currentRoom().classroomStudents];\r\n        const updatedUnreadCounts: any = {};\r\n        usersInChat.forEach((chatUser: any) => {\r\n          if (chatUser.aspUserId !== message.senderId) {\r\n            updatedUnreadCounts[chatUser.aspUserId] = 1; // Initialize unread count to 1 for new chat\r\n          }\r\n        });\r\n        updateData.unreadCounts = updatedUnreadCounts;\r\n\r\n        return chatRef.set(updateData).then(() => {\r\n          return messageRef.set(message).then(() => {\r\n            return messageRef.update({ id: messageRef.ref.id }).then(() => {\r\n              return this.updateChatLastMessage(chatRef, classroomId);\r\n            })\r\n          });\r\n        });\r\n      } else {\r\n        // Existing chat room, update last message and unread counts\r\n        return this.updateLastMessage(chatRef, message, user, false, messageId).then(() => {\r\n          return messageRef.set(message).then(() => {\r\n            return messageRef.update({ id: messageRef.ref.id });\r\n          })\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  private updateLastMessage(chatRef: AngularFirestoreDocument<any>,\r\n    updatedMessage: Message, user: any, isLastMessageDeleted = false, messageId = ''): Promise<void> {\r\n\r\n    const messageRef = chatRef.collection('messages').doc();\r\n    const updateData: any = {\r\n      lastMessage: updatedMessage.content,\r\n      lastMessageAt: serverTimestamp(),\r\n      lastMessageFrom: updatedMessage.senderId,\r\n      lastMessageFromImage: this.userService.getUserPhoto(user?.avatarUrl),\r\n      lastMessageFromName: this.generalService.getPersonFullName(user),\r\n      lastMessageIsFile: updatedMessage.isFileFromLibrary || updatedMessage.isUploadFile,\r\n      lastMessageDeleted: isLastMessageDeleted,\r\n      lastMessageId: messageId\r\n    };\r\n\r\n    return firstValueFrom(chatRef.get()).then(snapshot => {\r\n      console.log('current room', this.currentRoom());\r\n      const updatedUnreadCounts: any = {};\r\n      // Retrieve users in the chat, including teacher and students\r\n      const usersInChat = [this.currentRoom().teacher, ...this.currentRoom().classroomStudents];\r\n\r\n      usersInChat.forEach((chatUser: any) => {\r\n        if (chatUser.aspUserId !== updatedMessage.senderId) {\r\n          updatedUnreadCounts[chatUser.aspUserId] = (snapshot!.exists ? (snapshot!.data() as any).unreadCounts?.[chatUser.aspUserId] || 0 : 0) + 1;\r\n        }\r\n      });\r\n\r\n      updateData.unreadCounts = updatedUnreadCounts;\r\n      return chatRef.update(updateData);\r\n    });\r\n  }\r\n\r\n  editMessage(classroomId: string, messageId: string, updatedMessage: Message, user: any, updateLastMessage = false): Promise<void> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    const messageRef = chatRef.collection('messages').doc(`${messageId}`);\r\n    const messagesRef = chatRef.collection('messages', ref => ref.orderBy('timestamp', 'desc').limit(1));\r\n\r\n    const lastMessageDoc = messagesRef.ref.doc();\r\n    const lastMessageId = lastMessageDoc.id;\r\n    console.log(lastMessageId);\r\n    return messageRef.get().toPromise().then(snapshot => {\r\n      if (snapshot!.exists) {\r\n\r\n\r\n        return messageRef.update({\r\n          ...updatedMessage,\r\n        }).then(() => {\r\n          if (updateLastMessage) {\r\n            return this.updateLastMessage(chatRef, updatedMessage, user, false, updatedMessage.id!);\r\n          }\r\n          return Promise.resolve();\r\n        });\r\n      } else {\r\n        throw new Error('Message does not exist');\r\n      }\r\n    });\r\n  }\r\n\r\n  getLastUnreadMessagesCountForUser(classroomId: string, userId: string): Observable<number> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n\r\n    return chatRef.valueChanges().pipe(\r\n      map(data => {\r\n        const unreadCounts = (data as any)?.unreadCounts;\r\n        return unreadCounts ? unreadCounts[userId] || 0 : 0;\r\n      })\r\n    );\r\n  }\r\n\r\n  markLastMessagesAsRead(classroomId: string, userId: string): Promise<void> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    return this.markLastMessagesAsReadInLastMessage(chatRef, userId);\r\n  }\r\n\r\n  markLastMessagesAsReadInLastMessage(chatRef: AngularFirestoreDocument<any>, userId: string): Promise<void> {\r\n    return chatRef.get().toPromise().then(snapshot => {\r\n      if (snapshot!.exists) {\r\n        const data = snapshot!.data() as any;\r\n        const updatedUnreadCounts = { ...data.unreadCounts, [userId]: 0 };\r\n\r\n        return chatRef.update({ unreadCounts: updatedUnreadCounts });\r\n      }\r\n      return Promise.resolve(); // If the document doesn't exist, there's nothing to update.\r\n    });\r\n  }\r\n\r\n  // Delete a specific message from a chat room\r\n  deleteMessage(classroomId: string, message: any, user: any, updateLastMessage = false): Promise<void> {\r\n    console.log(classroomId);\r\n    console.log(message);\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    const messageRef = chatRef.collection('messages').doc(message.id);\r\n\r\n    return messageRef.delete().then(() => {\r\n      if (updateLastMessage) {\r\n        return this.getLatestMessage(classroomId).then(latestMessage => {\r\n          return this.updateLastMessage(chatRef, { ...message, content: 'Message Deleted' }, user, true, latestMessage?.id).then(() => {\r\n            // return this.updateChatLastMessage(chatRef);\r\n          });\r\n        })\r\n\r\n      } else {\r\n        return this.updateChatLastMessage(chatRef, classroomId);\r\n      }\r\n    });\r\n  }\r\n\r\n  private updateChatLastMessage(chatRef: AngularFirestoreDocument<any>, classroomId: string): Promise<void> {\r\n    return this.getLatestMessage(classroomId).then((snapshot: any | null) => {\r\n\r\n      console.log(snapshot);\r\n      if (snapshot) {\r\n        const latestMessage = snapshot;\r\n        return chatRef.update({\r\n          lastMessage: latestMessage.content,\r\n          lastMessageAt: latestMessage.createdAt,\r\n          lastMessageFrom: latestMessage.senderId,\r\n          lastMessageFromName: latestMessage.senderName,\r\n          lastMessageFromImage: latestMessage.senderImage,\r\n          lastMessageIsFile: latestMessage.isFileFromLibrary || latestMessage.isUploadFile,\r\n          lastMessageDeleted: false,\r\n          lastMessageId: latestMessage.id,\r\n        });\r\n      } else {\r\n        // Handle case where there are no messages left in the collection\r\n        return chatRef.update({\r\n          lastMessage: '',\r\n          lastMessageAt: null,\r\n          lastMessageFrom: '',\r\n          lastMessageFromName: '',\r\n          lastMessageFromImage: '',\r\n          lastMessageIsFile: false,\r\n          lastMessageDeleted: false,\r\n          lastMessageId: '',\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  getLatestMessage(classroomId: string): Promise<Message | null> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    return firstValueFrom(chatRef.collection('messages', ref => ref.orderBy('timestamp', 'desc').limit(1)).get()).then(snapshot => {\r\n      if (!snapshot.empty) {\r\n        const latestMessage = snapshot.docs[0].data() as Message;\r\n        return latestMessage;\r\n      } else {\r\n        return null; // No messages found\r\n      }\r\n    });\r\n  }\r\n\r\n  // Update a chat room document\r\n  updateChatRoom(classroomId: string, data: Partial<Chat>): Promise<void> {\r\n    return this.chatsRef.doc(classroomId).update(data);\r\n  }\r\n\r\n  // Delete a chat room document\r\n  deleteChatRoom(classroomId: string): Promise<void> {\r\n    return this.chatsRef.doc(classroomId).delete();\r\n  }\r\n\r\n  markMessagesAsRead(classroomId: string, userId: string): Promise<void> {\r\n    return this.db.collection(`${this.dbPath}/${classroomId}/messages`).get().toPromise().then(querySnapshot => {\r\n      const batch = this.db.firestore.batch();\r\n\r\n      querySnapshot!.forEach(doc => {\r\n        const data = doc.data();\r\n\r\n        // Ensure readStatus is a valid map or initialize it\r\n        const readStatusUpdate = (data as any).readStatus || {};\r\n\r\n        // Add or update the userId in the readStatus map\r\n        readStatusUpdate[userId] = true;\r\n\r\n        // Update the document with the new or modified readStatus map\r\n        batch.update(doc.ref, { readStatus: readStatusUpdate });\r\n      });\r\n\r\n      return batch.commit();\r\n    });\r\n  }\r\n\r\n  // Mark messages as read\r\n  markAllMessagesAsRead(classroomId: string): Promise<void> {\r\n    return this.db.collection(`${this.dbPath}/${classroomId}/messages`).get().toPromise().then(querySnapshot => {\r\n      const batch = this.db.firestore.batch();\r\n      querySnapshot!.forEach(doc => {\r\n        const messageData = doc.data() as Message;\r\n        const updatedReadStatus = {};\r\n\r\n        // Set readStatus for all users in the message's readStatus map to true\r\n        for (const userId in messageData.readStatus) {\r\n          if (messageData.readStatus.hasOwnProperty(userId)) {\r\n            const a = `readStatus.${userId}`;\r\n            (updatedReadStatus as any)[a] = true;\r\n          }\r\n        }\r\n\r\n        batch.update(doc.ref, updatedReadStatus);\r\n      });\r\n      return batch.commit();\r\n    });\r\n  }\r\n\r\n  // Get unread messages count for a user in a classroom\r\n  getUnreadMessagesCount(classroomId: string, userId: string): Observable<number> {\r\n    return this.db.collection<Message>(`${this.dbPath}/${classroomId}/messages`).snapshotChanges().pipe(\r\n      map(actions => actions.filter(action => {\r\n        const data = action.payload.doc.data() as Message;\r\n        return !data.readStatus.hasOwnProperty(userId);\r\n      }).length)\r\n    );\r\n  }\r\n\r\n\r\n  // Get total unread messages count for a user across all classrooms\r\n  getUnreadMessagesCountForUser(userId: string): Observable<number> {\r\n    return this.chatsRef.snapshotChanges().pipe(\r\n      switchMap(chatSnapshots => {\r\n        const chatObservables = chatSnapshots.map(snapshot => {\r\n          const chatId = snapshot.payload.doc.id;\r\n          return this.getUnreadMessagesCount(chatId, userId);\r\n        });\r\n        return combineLatest(chatObservables);\r\n      }),\r\n      map(unreadCounts => unreadCounts.reduce((acc, count) => acc + count, 0))\r\n    );\r\n  }\r\n\r\n  // Create Firebase Account and return as Observable\r\n  createFirebaseUser(email: string, password: string): Observable<any> {\r\n    return from(this.afAuth.createUserWithEmailAndPassword(email, password));\r\n  }\r\n\r\n  // Firebase Sign in as Observable\r\n  signInFirebaseUser(email: string, password: string): Observable<any> {\r\n    return from(this.afAuth.signInWithEmailAndPassword(email, password));\r\n  }\r\n\r\n  checkFirebaseUserExistsByEmail(email: string) {\r\n    return this.afAuth.fetchSignInMethodsForEmail(email)\r\n      .then((signInMethods) => {\r\n        console.log('Sign-in methods:', signInMethods);\r\n        return signInMethods.length > 0;\r\n      })\r\n      .catch((error) => {\r\n        console.error('Error checking email:', error);\r\n        return false;\r\n      });\r\n  }\r\n\r\n  signOutFromFirebase() {\r\n    this.afAuth.signOut().then(() => {\r\n      console.log('User signed out');\r\n    }).catch((error) => {\r\n      console.error('Error signing out:', error);\r\n    });\r\n  }\r\n\r\n  // Method to check if user is logged in\r\n  isLoggedInInFireBase(): Observable<boolean> {\r\n    return this.afAuth.authState.pipe(\r\n      map(user => !!user) // map user object to boolean (true if user exists, false otherwise)\r\n    );\r\n  }\r\n\r\n  // Or, to get the user object directly\r\n  getFireBaseUser(): Observable<any> {\r\n    return this.afAuth.authState;\r\n  }\r\n\r\n  setUserStatus(userId: string, status: 'online' | 'offline'): Promise<void> {\r\n    const userRef = this.usersRef.doc(userId);\r\n    return userRef.set({ status, lastOnlineTime: Timestamp.now() }, { merge: true });\r\n  }\r\n\r\n\r\n  setUserLastOnlineTime(userId: string, time: FieldValue): Promise<void> {\r\n    const userRef = this.usersRef.doc(userId);\r\n    return userRef.set({ status: 'online', lastOnlineTime: time }, { merge: true });\r\n  }\r\n\r\n  getUserStatus(userId: string): Observable<'online' | 'offline'> {\r\n    return this.usersRef.doc<ChatUserStatus>(userId).valueChanges().pipe(\r\n      map(user => user?.status || 'offline')\r\n    );\r\n  }\r\n\r\n  getOnlineUsers(): Observable<ChatUserStatus[]> {\r\n    return this.db.collection<ChatUserStatus>('/users', ref => ref.where('status', '==', 'online'))\r\n      .snapshotChanges().pipe(\r\n        map(actions => actions.map(a => {\r\n          const data = a.payload.doc.data() as ChatUserStatus;\r\n          const id = a.payload.doc.id;\r\n          return { ...data, id }; // Corrected merge of id and data\r\n        }))\r\n      );\r\n  }\r\n\r\n  getOnlineUsersLastOnlineTime(): Observable<ChatUserStatus[]> {\r\n    return this.db.collection<ChatUserStatus>('/users')\r\n      .snapshotChanges().pipe(\r\n        map(actions => {\r\n          const currentTime = Timestamp.now();\r\n          return actions.map(a => {\r\n            const data = a.payload.doc.data() as ChatUserStatus;\r\n            const id = a.payload.doc.id;\r\n            const lastOnlineTime = data.lastOnlineTime as Timestamp;\r\n            if (!currentTime.seconds || !lastOnlineTime.seconds) {\r\n              return { ...data, id, status: 'offline' };\r\n            }\r\n            const timeDifference = currentTime.seconds - lastOnlineTime.seconds;\r\n  \r\n            // Check if the user was online within the last 10 seconds\r\n            if (timeDifference <= 10) {\r\n              return { ...data, id, status: 'online' };\r\n            } else {\r\n              return { ...data, id, status: 'offline' };\r\n            }\r\n          });\r\n        })\r\n      );\r\n  }\r\n\r\n}\r\n"], "mappings": "AAAA,SAAqBA,MAAM,EAAEC,MAAM,QAAQ,eAAe;AAG1D,SAAqBC,eAAe,EAAEC,SAAS,QAAQ,oBAAoB;AAC3E,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,UAAU,EAAEC,aAAa,EAASC,cAAc,EAAYC,IAAI,QAAQ,MAAM;AACvF,SAASC,GAAG,EAAEC,SAAS,QAAQ,gBAAgB;AAC/C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,WAAW,QAAQ,8BAA8B;AAG1D,SAASC,OAAO,EAAEC,iBAAiB,QAAQ,eAAe;;;;;AAF1D,MAAMC,WAAW,GAAGH,WAAW,CAACI,MAAM;AACtC,MAAMC,eAAe,GAAGL,WAAW,CAACI,MAAM,GAAG,OAAO;AAuCpD,OAAM,MAAOE,WAAW;EAatBC,YAAoBC,EAAoB,EAAUC,MAAuB,EAAUC,IAAgB;IAA/E,KAAAF,EAAE,GAAFA,EAAE;IAA4B,KAAAC,MAAM,GAANA,MAAM;IAA2B,KAAAC,IAAI,GAAJA,IAAI;IAZvF,KAAAC,WAAW,GAAGxB,MAAM,CAACW,WAAW,CAAC;IACjC,KAAAc,cAAc,GAAGzB,MAAM,CAACY,cAAc,CAAC;IACvC,KAAAc,aAAa,GAAGzB,MAAM,CAACY,WAAW,CAACa,aAAa,CAAC;IACjD,KAAAC,cAAc,GAAG1B,MAAM,CAAC,KAAK,CAAC;IAC9B,KAAA2B,WAAW,GAAG3B,MAAM,CAAC,CAAC,CAAC;IACvB,KAAA4B,WAAW,GAAG5B,MAAM,CAAC,EAAS,CAAC;IAC/B,KAAA6B,cAAc,GAAG7B,MAAM,CAAC,KAAK,CAAC;IACtB,KAAA8B,MAAM,GAAG,QAAQ;IAEjB,KAAAC,QAAQ,GAA+C,IAAI,CAACX,EAAE,CAACY,UAAU,CAAC,OAAO,CAAC;IAIxF,IAAI,CAACC,QAAQ,GAAGb,EAAE,CAACY,UAAU,CAAC,IAAI,CAACF,MAAM,CAAC;IAC1C,IAAI,CAACC,QAAQ,GAAGX,EAAE,CAACY,UAAU,CAAC,QAAQ,CAAC;EACzC;EAGAE,gCAAgCA,CAACC,OAAY;IAC3CC,OAAO,CAACC,GAAG,CAACF,OAAO,CAAC;IACpB,OAAO,IAAI,CAACb,IAAI,CAACgB,IAAI,CAAMrB,eAAe,GAAG,2CAA2C,GAAGkB,OAAO,EAAE,EAAE,CAAC;EACzG;EAEA;EACArB,iBAAiBA,CAAA;IACf,OAAO,IAAIV,UAAU,CAACmC,QAAQ,IAAG;MAC/B,IAAI,CAAClB,MAAM,CAACP,iBAAiB,EAAE,CAC5B0B,IAAI,CAACC,cAAc,IAAG;QACrBL,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEI,cAAc,CAACC,IAAI,CAAC;QAC1DH,QAAQ,CAACI,IAAI,CAACF,cAAc,CAAC;QAC7BF,QAAQ,CAACK,QAAQ,EAAE;MACrB,CAAC,CAAC,CACDC,KAAK,CAACC,KAAK,IAAG;QACbV,OAAO,CAACU,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrDP,QAAQ,CAACO,KAAK,CAACA,KAAK,CAAC;MACvB,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;EAEAC,YAAYA,CAAA;IACV,MAAMC,IAAI,GAAGnC,OAAO,EAAE;IACtBC,iBAAiB,CAACkC,IAAI,CAAC,CACpBR,IAAI,CAAC,MAAK;MACT;IAAA,CACD,CAAC,CACDK,KAAK,CAAEC,KAAK,IAAI;MACf,MAAMG,SAAS,GAAGH,KAAK,CAACI,IAAI;MAC5B,MAAMC,YAAY,GAAGL,KAAK,CAACM,OAAO;MAClC;IACF,CAAC,CAAC;EACN;EAEAC,cAAcA,CAACC,WAAmB;IAChC,MAAMC,OAAO,GAAG,IAAI,CAACtB,QAAQ,CAACuB,GAAG,CAAU,GAAGF,WAAW,GAAG,CAAC;IAE7D,OAAOC,OAAO,CAACE,eAAe,EAAE,CAACC,IAAI,CACnClD,GAAG,CAACmD,MAAM,IAAG;MACX,MAAMC,IAAI,GAAGD,MAAM,CAACE,OAAO,CAACD,IAAI,EAAa;MAC7C,MAAME,EAAE,GAAGH,MAAM,CAACE,OAAO,CAACC,EAAE;MAC5B,OAAO;QAAEA,EAAE;QAAE,GAAGF;MAAI,CAAE;IACxB,CAAC,CAAC,CACH;EACH;EAEA;EACAG,iBAAiBA,CAACT,WAAmB;IACnC,MAAMC,OAAO,GAAG,IAAI,CAACtB,QAAQ,CAACuB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IACnD,OAAO,IAAI,CAACrB,QAAQ,CAACuB,GAAG,CAACF,WAAW,CAAC,CAACtB,UAAU,CAAO,OAAO,CAAC,CAC5DyB,eAAe,EAAE,CAACC,IAAI,CACrBlD,GAAG,CAACwD,OAAO,IAAIA,OAAO,CAACxD,GAAG,CAACyD,CAAC,IAAG;MAC7B,MAAML,IAAI,GAAGK,CAAC,CAACJ,OAAO,CAACL,GAAG,CAACI,IAAI,EAAU;MACzC,MAAME,EAAE,GAAGG,CAAC,CAACJ,OAAO,CAACL,GAAG,CAACM,EAAE;MAC3B,OAAO;QAAEA,EAAE;QAAE,GAAGF;MAAI,CAAE;IACxB,CAAC,CAAC,CAAC,CACJ;EACL;EAEA;EACAM,eAAeA,CAACZ,WAAmB;IACjC,OAAO,IAAI,CAAClC,EAAE,CAACY,UAAU,CAAU,GAAG,IAAI,CAACF,MAAM,IAAIwB,WAAW,WAAW,EAAEa,GAAG,IAC9EA,GAAG,CAACC,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,CAChC,CAACX,eAAe,EAAE,CAACC,IAAI,CACtBlD,GAAG,CAACwD,OAAO,IAAIA,OAAO,CAACxD,GAAG,CAACyD,CAAC,IAAG;MAC7B,MAAML,IAAI,GAAGK,CAAC,CAACJ,OAAO,CAACL,GAAG,CAACI,IAAI,EAAa;MAC5C,MAAME,EAAE,GAAGG,CAAC,CAACJ,OAAO,CAACL,GAAG,CAACM,EAAE;MAC3B,OAAO;QAAEA,EAAE;QAAE,GAAGF;MAAI,CAAE;IACxB,CAAC,CAAC,CAAC,CACJ;EACH;EAEA;EACAS,cAAcA,CAACf,WAAmB,EAAEgB,YAAsB;IACxD,MAAMC,IAAI,GAAS;MACjBjB,WAAW,EAAEA,WAAW;MACxBkB,SAAS,EAAErE,MAAM,EAAE,CAACsE,MAAM,CAAC,qBAAqB,CAAC;MACjDC,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE1E,eAAe,EAAE;MAChC2E,eAAe,EAAE,EAAE;MACnBC,mBAAmB,EAAE,EAAE;MACvBC,oBAAoB,EAAE,EAAE;MACxBC,iBAAiB,EAAE,KAAK;MACxBC,kBAAkB,EAAE,KAAK;MACzBC,aAAa,EAAE,EAAE;MACjBC,sBAAsB,EAAE;KACzB;IACD,OAAO,IAAI,CAACjD,QAAQ,CAACkD,GAAG,CAACZ,IAAI,CAAC;EAChC;EAEA;EACAa,UAAUA,CAAC9B,WAAmB,EAAEF,OAAgB,EAAEV,IAAS;IACzD,MAAMa,OAAO,GAAG,IAAI,CAACtB,QAAQ,CAACuB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IACnD,MAAM+B,UAAU,GAAG9B,OAAO,CAACvB,UAAU,CAAC,UAAU,CAAC,CAACwB,GAAG,EAAE;IAEvD,MAAM8B,SAAS,GAAID,UAAU,CAAClB,GAAG,CAACL,EAAG;IACrC,OAAOxD,cAAc,CAACiD,OAAO,CAACgC,GAAG,EAAE,CAAC,CAAC/C,IAAI,CAACgD,QAAQ,IAAG;MACnD,MAAMC,UAAU,GAAQ;QACtBjB,SAAS,EAAErE,MAAM,EAAE,CAACsE,MAAM,CAAC,qBAAqB,CAAC;QACjDC,WAAW,EAAEtB,OAAO,CAACsC,OAAO;QAC5Bf,aAAa,EAAE1E,eAAe,EAAE;QAChCqD,WAAW,EAAEA,WAAW;QACxBsB,eAAe,EAAExB,OAAO,CAACuC,QAAQ;QACjCd,mBAAmB,EAAE,IAAI,CAACrD,cAAc,CAACoE,iBAAiB,CAAClD,IAAI,CAAC;QAChEoC,oBAAoB,EAAE,IAAI,CAACvD,WAAW,CAACsE,YAAY,CAACnD,IAAI,EAAEoD,SAAS,CAAC;QACpEf,iBAAiB,EAAE3B,OAAO,CAAC2C,iBAAiB,IAAI3C,OAAO,CAAC4C,YAAY;QACpEhB,kBAAkB,EAAE,KAAK;QACzBC,aAAa,EAAEK,SAAS;QAAE;QAC1BJ,sBAAsB,EAAE;OACzB;MAED,IAAI,CAACM,QAAQ,CAACS,MAAM,EAAE;QACpB;QACA,MAAMC,WAAW,GAAG,CAAC,IAAI,CAACtE,WAAW,EAAE,CAACuE,OAAO,EAAE,GAAG,IAAI,CAACvE,WAAW,EAAE,CAACwE,iBAAiB,CAAC;QACzF,MAAMC,mBAAmB,GAAQ,EAAE;QACnCH,WAAW,CAACI,OAAO,CAAEC,QAAa,IAAI;UACpC,IAAIA,QAAQ,CAACC,SAAS,KAAKpD,OAAO,CAACuC,QAAQ,EAAE;YAC3CU,mBAAmB,CAACE,QAAQ,CAACC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;UAC/C;QACF,CAAC,CAAC;QACFf,UAAU,CAACgB,YAAY,GAAGJ,mBAAmB;QAE7C,OAAO9C,OAAO,CAACmD,GAAG,CAACjB,UAAU,CAAC,CAACjD,IAAI,CAAC,MAAK;UACvC,OAAO6C,UAAU,CAACqB,GAAG,CAACtD,OAAO,CAAC,CAACZ,IAAI,CAAC,MAAK;YACvC,OAAO6C,UAAU,CAACsB,MAAM,CAAC;cAAE7C,EAAE,EAAEuB,UAAU,CAAClB,GAAG,CAACL;YAAE,CAAE,CAAC,CAACtB,IAAI,CAAC,MAAK;cAC5D,OAAO,IAAI,CAACoE,qBAAqB,CAACrD,OAAO,EAAED,WAAW,CAAC;YACzD,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,OAAO,IAAI,CAACuD,iBAAiB,CAACtD,OAAO,EAAEH,OAAO,EAAEV,IAAI,EAAE,KAAK,EAAE4C,SAAS,CAAC,CAAC9C,IAAI,CAAC,MAAK;UAChF,OAAO6C,UAAU,CAACqB,GAAG,CAACtD,OAAO,CAAC,CAACZ,IAAI,CAAC,MAAK;YACvC,OAAO6C,UAAU,CAACsB,MAAM,CAAC;cAAE7C,EAAE,EAAEuB,UAAU,CAAClB,GAAG,CAACL;YAAE,CAAE,CAAC;UACrD,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EAEQ+C,iBAAiBA,CAACtD,OAAsC,EAC9DuD,cAAuB,EAAEpE,IAAS,EAAEqE,oBAAoB,GAAG,KAAK,EAAEzB,SAAS,GAAG,EAAE;IAEhF,MAAMD,UAAU,GAAG9B,OAAO,CAACvB,UAAU,CAAC,UAAU,CAAC,CAACwB,GAAG,EAAE;IACvD,MAAMiC,UAAU,GAAQ;MACtBf,WAAW,EAAEoC,cAAc,CAACpB,OAAO;MACnCf,aAAa,EAAE1E,eAAe,EAAE;MAChC2E,eAAe,EAAEkC,cAAc,CAACnB,QAAQ;MACxCb,oBAAoB,EAAE,IAAI,CAACvD,WAAW,CAACsE,YAAY,CAACnD,IAAI,EAAEoD,SAAS,CAAC;MACpEjB,mBAAmB,EAAE,IAAI,CAACrD,cAAc,CAACoE,iBAAiB,CAAClD,IAAI,CAAC;MAChEqC,iBAAiB,EAAE+B,cAAc,CAACf,iBAAiB,IAAIe,cAAc,CAACd,YAAY;MAClFhB,kBAAkB,EAAE+B,oBAAoB;MACxC9B,aAAa,EAAEK;KAChB;IAED,OAAOhF,cAAc,CAACiD,OAAO,CAACgC,GAAG,EAAE,CAAC,CAAC/C,IAAI,CAACgD,QAAQ,IAAG;MACnDpD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAACT,WAAW,EAAE,CAAC;MAC/C,MAAMyE,mBAAmB,GAAQ,EAAE;MACnC;MACA,MAAMH,WAAW,GAAG,CAAC,IAAI,CAACtE,WAAW,EAAE,CAACuE,OAAO,EAAE,GAAG,IAAI,CAACvE,WAAW,EAAE,CAACwE,iBAAiB,CAAC;MAEzFF,WAAW,CAACI,OAAO,CAAEC,QAAa,IAAI;QACpC,IAAIA,QAAQ,CAACC,SAAS,KAAKM,cAAc,CAACnB,QAAQ,EAAE;UAClDU,mBAAmB,CAACE,QAAQ,CAACC,SAAS,CAAC,GAAG,CAAChB,QAAS,CAACS,MAAM,GAAIT,QAAS,CAAC5B,IAAI,EAAU,CAAC6C,YAAY,GAAGF,QAAQ,CAACC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;QAC1I;MACF,CAAC,CAAC;MAEFf,UAAU,CAACgB,YAAY,GAAGJ,mBAAmB;MAC7C,OAAO9C,OAAO,CAACoD,MAAM,CAAClB,UAAU,CAAC;IACnC,CAAC,CAAC;EACJ;EAEAuB,WAAWA,CAAC1D,WAAmB,EAAEgC,SAAiB,EAAEwB,cAAuB,EAAEpE,IAAS,EAAEmE,iBAAiB,GAAG,KAAK;IAC/G,MAAMtD,OAAO,GAAG,IAAI,CAACtB,QAAQ,CAACuB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IACnD,MAAM+B,UAAU,GAAG9B,OAAO,CAACvB,UAAU,CAAC,UAAU,CAAC,CAACwB,GAAG,CAAC,GAAG8B,SAAS,EAAE,CAAC;IACrE,MAAM2B,WAAW,GAAG1D,OAAO,CAACvB,UAAU,CAAC,UAAU,EAAEmC,GAAG,IAAIA,GAAG,CAACC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC8C,KAAK,CAAC,CAAC,CAAC,CAAC;IAEpG,MAAMC,cAAc,GAAGF,WAAW,CAAC9C,GAAG,CAACX,GAAG,EAAE;IAC5C,MAAMyB,aAAa,GAAGkC,cAAc,CAACrD,EAAE;IACvC1B,OAAO,CAACC,GAAG,CAAC4C,aAAa,CAAC;IAC1B,OAAOI,UAAU,CAACE,GAAG,EAAE,CAAC6B,SAAS,EAAE,CAAC5E,IAAI,CAACgD,QAAQ,IAAG;MAClD,IAAIA,QAAS,CAACS,MAAM,EAAE;QAGpB,OAAOZ,UAAU,CAACsB,MAAM,CAAC;UACvB,GAAGG;SACJ,CAAC,CAACtE,IAAI,CAAC,MAAK;UACX,IAAIqE,iBAAiB,EAAE;YACrB,OAAO,IAAI,CAACA,iBAAiB,CAACtD,OAAO,EAAEuD,cAAc,EAAEpE,IAAI,EAAE,KAAK,EAAEoE,cAAc,CAAChD,EAAG,CAAC;UACzF;UACA,OAAOuD,OAAO,CAACC,OAAO,EAAE;QAC1B,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;MAC3C;IACF,CAAC,CAAC;EACJ;EAEAC,iCAAiCA,CAAClE,WAAmB,EAAEmE,MAAc;IACnE,MAAMlE,OAAO,GAAG,IAAI,CAACtB,QAAQ,CAACuB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IAEnD,OAAOC,OAAO,CAACmE,YAAY,EAAE,CAAChE,IAAI,CAChClD,GAAG,CAACoD,IAAI,IAAG;MACT,MAAM6C,YAAY,GAAI7C,IAAY,EAAE6C,YAAY;MAChD,OAAOA,YAAY,GAAGA,YAAY,CAACgB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;IACrD,CAAC,CAAC,CACH;EACH;EAEAE,sBAAsBA,CAACrE,WAAmB,EAAEmE,MAAc;IACxD,MAAMlE,OAAO,GAAG,IAAI,CAACtB,QAAQ,CAACuB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IACnD,OAAO,IAAI,CAACsE,mCAAmC,CAACrE,OAAO,EAAEkE,MAAM,CAAC;EAClE;EAEAG,mCAAmCA,CAACrE,OAAsC,EAAEkE,MAAc;IACxF,OAAOlE,OAAO,CAACgC,GAAG,EAAE,CAAC6B,SAAS,EAAE,CAAC5E,IAAI,CAACgD,QAAQ,IAAG;MAC/C,IAAIA,QAAS,CAACS,MAAM,EAAE;QACpB,MAAMrC,IAAI,GAAG4B,QAAS,CAAC5B,IAAI,EAAS;QACpC,MAAMyC,mBAAmB,GAAG;UAAE,GAAGzC,IAAI,CAAC6C,YAAY;UAAE,CAACgB,MAAM,GAAG;QAAC,CAAE;QAEjE,OAAOlE,OAAO,CAACoD,MAAM,CAAC;UAAEF,YAAY,EAAEJ;QAAmB,CAAE,CAAC;MAC9D;MACA,OAAOgB,OAAO,CAACC,OAAO,EAAE,CAAC,CAAC;IAC5B,CAAC,CAAC;EACJ;EAEA;EACAO,aAAaA,CAACvE,WAAmB,EAAEF,OAAY,EAAEV,IAAS,EAAEmE,iBAAiB,GAAG,KAAK;IACnFzE,OAAO,CAACC,GAAG,CAACiB,WAAW,CAAC;IACxBlB,OAAO,CAACC,GAAG,CAACe,OAAO,CAAC;IACpB,MAAMG,OAAO,GAAG,IAAI,CAACtB,QAAQ,CAACuB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IACnD,MAAM+B,UAAU,GAAG9B,OAAO,CAACvB,UAAU,CAAC,UAAU,CAAC,CAACwB,GAAG,CAACJ,OAAO,CAACU,EAAE,CAAC;IAEjE,OAAOuB,UAAU,CAACyC,MAAM,EAAE,CAACtF,IAAI,CAAC,MAAK;MACnC,IAAIqE,iBAAiB,EAAE;QACrB,OAAO,IAAI,CAACkB,gBAAgB,CAACzE,WAAW,CAAC,CAACd,IAAI,CAACwF,aAAa,IAAG;UAC7D,OAAO,IAAI,CAACnB,iBAAiB,CAACtD,OAAO,EAAE;YAAE,GAAGH,OAAO;YAAEsC,OAAO,EAAE;UAAiB,CAAE,EAAEhD,IAAI,EAAE,IAAI,EAAEsF,aAAa,EAAElE,EAAE,CAAC,CAACtB,IAAI,CAAC,MAAK;YAC1H;UAAA,CACD,CAAC;QACJ,CAAC,CAAC;MAEJ,CAAC,MAAM;QACL,OAAO,IAAI,CAACoE,qBAAqB,CAACrD,OAAO,EAAED,WAAW,CAAC;MACzD;IACF,CAAC,CAAC;EACJ;EAEQsD,qBAAqBA,CAACrD,OAAsC,EAAED,WAAmB;IACvF,OAAO,IAAI,CAACyE,gBAAgB,CAACzE,WAAW,CAAC,CAACd,IAAI,CAAEgD,QAAoB,IAAI;MAEtEpD,OAAO,CAACC,GAAG,CAACmD,QAAQ,CAAC;MACrB,IAAIA,QAAQ,EAAE;QACZ,MAAMwC,aAAa,GAAGxC,QAAQ;QAC9B,OAAOjC,OAAO,CAACoD,MAAM,CAAC;UACpBjC,WAAW,EAAEsD,aAAa,CAACtC,OAAO;UAClCf,aAAa,EAAEqD,aAAa,CAACxD,SAAS;UACtCI,eAAe,EAAEoD,aAAa,CAACrC,QAAQ;UACvCd,mBAAmB,EAAEmD,aAAa,CAACC,UAAU;UAC7CnD,oBAAoB,EAAEkD,aAAa,CAACE,WAAW;UAC/CnD,iBAAiB,EAAEiD,aAAa,CAACjC,iBAAiB,IAAIiC,aAAa,CAAChC,YAAY;UAChFhB,kBAAkB,EAAE,KAAK;UACzBC,aAAa,EAAE+C,aAAa,CAAClE;SAC9B,CAAC;MACJ,CAAC,MAAM;QACL;QACA,OAAOP,OAAO,CAACoD,MAAM,CAAC;UACpBjC,WAAW,EAAE,EAAE;UACfC,aAAa,EAAE,IAAI;UACnBC,eAAe,EAAE,EAAE;UACnBC,mBAAmB,EAAE,EAAE;UACvBC,oBAAoB,EAAE,EAAE;UACxBC,iBAAiB,EAAE,KAAK;UACxBC,kBAAkB,EAAE,KAAK;UACzBC,aAAa,EAAE;SAChB,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EAEA8C,gBAAgBA,CAACzE,WAAmB;IAClC,MAAMC,OAAO,GAAG,IAAI,CAACtB,QAAQ,CAACuB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IACnD,OAAOhD,cAAc,CAACiD,OAAO,CAACvB,UAAU,CAAC,UAAU,EAAEmC,GAAG,IAAIA,GAAG,CAACC,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC8C,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC3B,GAAG,EAAE,CAAC,CAAC/C,IAAI,CAACgD,QAAQ,IAAG;MAC5H,IAAI,CAACA,QAAQ,CAAC2C,KAAK,EAAE;QACnB,MAAMH,aAAa,GAAGxC,QAAQ,CAAC4C,IAAI,CAAC,CAAC,CAAC,CAACxE,IAAI,EAAa;QACxD,OAAOoE,aAAa;MACtB,CAAC,MAAM;QACL,OAAO,IAAI,CAAC,CAAC;MACf;IACF,CAAC,CAAC;EACJ;EAEA;EACAK,cAAcA,CAAC/E,WAAmB,EAAEM,IAAmB;IACrD,OAAO,IAAI,CAAC3B,QAAQ,CAACuB,GAAG,CAACF,WAAW,CAAC,CAACqD,MAAM,CAAC/C,IAAI,CAAC;EACpD;EAEA;EACA0E,cAAcA,CAAChF,WAAmB;IAChC,OAAO,IAAI,CAACrB,QAAQ,CAACuB,GAAG,CAACF,WAAW,CAAC,CAACwE,MAAM,EAAE;EAChD;EAEAS,kBAAkBA,CAACjF,WAAmB,EAAEmE,MAAc;IACpD,OAAO,IAAI,CAACrG,EAAE,CAACY,UAAU,CAAC,GAAG,IAAI,CAACF,MAAM,IAAIwB,WAAW,WAAW,CAAC,CAACiC,GAAG,EAAE,CAAC6B,SAAS,EAAE,CAAC5E,IAAI,CAACgG,aAAa,IAAG;MACzG,MAAMC,KAAK,GAAG,IAAI,CAACrH,EAAE,CAACsH,SAAS,CAACD,KAAK,EAAE;MAEvCD,aAAc,CAAClC,OAAO,CAAC9C,GAAG,IAAG;QAC3B,MAAMI,IAAI,GAAGJ,GAAG,CAACI,IAAI,EAAE;QAEvB;QACA,MAAM+E,gBAAgB,GAAI/E,IAAY,CAACgF,UAAU,IAAI,EAAE;QAEvD;QACAD,gBAAgB,CAAClB,MAAM,CAAC,GAAG,IAAI;QAE/B;QACAgB,KAAK,CAAC9B,MAAM,CAACnD,GAAG,CAACW,GAAG,EAAE;UAAEyE,UAAU,EAAED;QAAgB,CAAE,CAAC;MACzD,CAAC,CAAC;MAEF,OAAOF,KAAK,CAACI,MAAM,EAAE;IACvB,CAAC,CAAC;EACJ;EAEA;EACAC,qBAAqBA,CAACxF,WAAmB;IACvC,OAAO,IAAI,CAAClC,EAAE,CAACY,UAAU,CAAC,GAAG,IAAI,CAACF,MAAM,IAAIwB,WAAW,WAAW,CAAC,CAACiC,GAAG,EAAE,CAAC6B,SAAS,EAAE,CAAC5E,IAAI,CAACgG,aAAa,IAAG;MACzG,MAAMC,KAAK,GAAG,IAAI,CAACrH,EAAE,CAACsH,SAAS,CAACD,KAAK,EAAE;MACvCD,aAAc,CAAClC,OAAO,CAAC9C,GAAG,IAAG;QAC3B,MAAMuF,WAAW,GAAGvF,GAAG,CAACI,IAAI,EAAa;QACzC,MAAMoF,iBAAiB,GAAG,EAAE;QAE5B;QACA,KAAK,MAAMvB,MAAM,IAAIsB,WAAW,CAACH,UAAU,EAAE;UAC3C,IAAIG,WAAW,CAACH,UAAU,CAACK,cAAc,CAACxB,MAAM,CAAC,EAAE;YACjD,MAAMxD,CAAC,GAAG,cAAcwD,MAAM,EAAE;YAC/BuB,iBAAyB,CAAC/E,CAAC,CAAC,GAAG,IAAI;UACtC;QACF;QAEAwE,KAAK,CAAC9B,MAAM,CAACnD,GAAG,CAACW,GAAG,EAAE6E,iBAAiB,CAAC;MAC1C,CAAC,CAAC;MACF,OAAOP,KAAK,CAACI,MAAM,EAAE;IACvB,CAAC,CAAC;EACJ;EAEA;EACAK,sBAAsBA,CAAC5F,WAAmB,EAAEmE,MAAc;IACxD,OAAO,IAAI,CAACrG,EAAE,CAACY,UAAU,CAAU,GAAG,IAAI,CAACF,MAAM,IAAIwB,WAAW,WAAW,CAAC,CAACG,eAAe,EAAE,CAACC,IAAI,CACjGlD,GAAG,CAACwD,OAAO,IAAIA,OAAO,CAACmF,MAAM,CAACxF,MAAM,IAAG;MACrC,MAAMC,IAAI,GAAGD,MAAM,CAACE,OAAO,CAACL,GAAG,CAACI,IAAI,EAAa;MACjD,OAAO,CAACA,IAAI,CAACgF,UAAU,CAACK,cAAc,CAACxB,MAAM,CAAC;IAChD,CAAC,CAAC,CAAC2B,MAAM,CAAC,CACX;EACH;EAGA;EACAC,6BAA6BA,CAAC5B,MAAc;IAC1C,OAAO,IAAI,CAACxF,QAAQ,CAACwB,eAAe,EAAE,CAACC,IAAI,CACzCjD,SAAS,CAAC6I,aAAa,IAAG;MACxB,MAAMC,eAAe,GAAGD,aAAa,CAAC9I,GAAG,CAACgF,QAAQ,IAAG;QACnD,MAAMgE,MAAM,GAAGhE,QAAQ,CAAC3B,OAAO,CAACL,GAAG,CAACM,EAAE;QACtC,OAAO,IAAI,CAACoF,sBAAsB,CAACM,MAAM,EAAE/B,MAAM,CAAC;MACpD,CAAC,CAAC;MACF,OAAOpH,aAAa,CAACkJ,eAAe,CAAC;IACvC,CAAC,CAAC,EACF/I,GAAG,CAACiG,YAAY,IAAIA,YAAY,CAACgD,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC,CAAC,CACzE;EACH;EAEA;EACAC,kBAAkBA,CAACC,KAAa,EAAEC,QAAgB;IAChD,OAAOvJ,IAAI,CAAC,IAAI,CAACc,MAAM,CAAC0I,8BAA8B,CAACF,KAAK,EAAEC,QAAQ,CAAC,CAAC;EAC1E;EAEA;EACAE,kBAAkBA,CAACH,KAAa,EAAEC,QAAgB;IAChD,OAAOvJ,IAAI,CAAC,IAAI,CAACc,MAAM,CAAC4I,0BAA0B,CAACJ,KAAK,EAAEC,QAAQ,CAAC,CAAC;EACtE;EAEAI,8BAA8BA,CAACL,KAAa;IAC1C,OAAO,IAAI,CAACxI,MAAM,CAAC8I,0BAA0B,CAACN,KAAK,CAAC,CACjDrH,IAAI,CAAE4H,aAAa,IAAI;MACtBhI,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE+H,aAAa,CAAC;MAC9C,OAAOA,aAAa,CAAChB,MAAM,GAAG,CAAC;IACjC,CAAC,CAAC,CACDvG,KAAK,CAAEC,KAAK,IAAI;MACfV,OAAO,CAACU,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,OAAO,KAAK;IACd,CAAC,CAAC;EACN;EAEAuH,mBAAmBA,CAAA;IACjB,IAAI,CAAChJ,MAAM,CAACiJ,OAAO,EAAE,CAAC9H,IAAI,CAAC,MAAK;MAC9BJ,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAChC,CAAC,CAAC,CAACQ,KAAK,CAAEC,KAAK,IAAI;MACjBV,OAAO,CAACU,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;IAC5C,CAAC,CAAC;EACJ;EAEA;EACAyH,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAClJ,MAAM,CAACmJ,SAAS,CAAC9G,IAAI,CAC/BlD,GAAG,CAACkC,IAAI,IAAI,CAAC,CAACA,IAAI,CAAC,CAAC;KACrB;EACH;EAEA;EACA+H,eAAeA,CAAA;IACb,OAAO,IAAI,CAACpJ,MAAM,CAACmJ,SAAS;EAC9B;EAEAE,aAAaA,CAACjD,MAAc,EAAEkD,MAA4B;IACxD,MAAMC,OAAO,GAAG,IAAI,CAAC7I,QAAQ,CAACyB,GAAG,CAACiE,MAAM,CAAC;IACzC,OAAOmD,OAAO,CAAClE,GAAG,CAAC;MAAEiE,MAAM;MAAEE,cAAc,EAAE3K,SAAS,CAAC4K,GAAG;IAAE,CAAE,EAAE;MAAEC,KAAK,EAAE;IAAI,CAAE,CAAC;EAClF;EAGAC,qBAAqBA,CAACvD,MAAc,EAAEwD,IAAgB;IACpD,MAAML,OAAO,GAAG,IAAI,CAAC7I,QAAQ,CAACyB,GAAG,CAACiE,MAAM,CAAC;IACzC,OAAOmD,OAAO,CAAClE,GAAG,CAAC;MAAEiE,MAAM,EAAE,QAAQ;MAAEE,cAAc,EAAEI;IAAI,CAAE,EAAE;MAAEF,KAAK,EAAE;IAAI,CAAE,CAAC;EACjF;EAEAG,aAAaA,CAACzD,MAAc;IAC1B,OAAO,IAAI,CAAC1F,QAAQ,CAACyB,GAAG,CAAiBiE,MAAM,CAAC,CAACC,YAAY,EAAE,CAAChE,IAAI,CAClElD,GAAG,CAACkC,IAAI,IAAIA,IAAI,EAAEiI,MAAM,IAAI,SAAS,CAAC,CACvC;EACH;EAEAQ,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAC/J,EAAE,CAACY,UAAU,CAAiB,QAAQ,EAAEmC,GAAG,IAAIA,GAAG,CAACiH,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAC5F3H,eAAe,EAAE,CAACC,IAAI,CACrBlD,GAAG,CAACwD,OAAO,IAAIA,OAAO,CAACxD,GAAG,CAACyD,CAAC,IAAG;MAC7B,MAAML,IAAI,GAAGK,CAAC,CAACJ,OAAO,CAACL,GAAG,CAACI,IAAI,EAAoB;MACnD,MAAME,EAAE,GAAGG,CAAC,CAACJ,OAAO,CAACL,GAAG,CAACM,EAAE;MAC3B,OAAO;QAAE,GAAGF,IAAI;QAAEE;MAAE,CAAE,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CACJ;EACL;EAEAuH,4BAA4BA,CAAA;IAC1B,OAAO,IAAI,CAACjK,EAAE,CAACY,UAAU,CAAiB,QAAQ,CAAC,CAChDyB,eAAe,EAAE,CAACC,IAAI,CACrBlD,GAAG,CAACwD,OAAO,IAAG;MACZ,MAAMsH,WAAW,GAAGpL,SAAS,CAAC4K,GAAG,EAAE;MACnC,OAAO9G,OAAO,CAACxD,GAAG,CAACyD,CAAC,IAAG;QACrB,MAAML,IAAI,GAAGK,CAAC,CAACJ,OAAO,CAACL,GAAG,CAACI,IAAI,EAAoB;QACnD,MAAME,EAAE,GAAGG,CAAC,CAACJ,OAAO,CAACL,GAAG,CAACM,EAAE;QAC3B,MAAM+G,cAAc,GAAGjH,IAAI,CAACiH,cAA2B;QACvD,IAAI,CAACS,WAAW,CAACC,OAAO,IAAI,CAACV,cAAc,CAACU,OAAO,EAAE;UACnD,OAAO;YAAE,GAAG3H,IAAI;YAAEE,EAAE;YAAE6G,MAAM,EAAE;UAAS,CAAE;QAC3C;QACA,MAAMa,cAAc,GAAGF,WAAW,CAACC,OAAO,GAAGV,cAAc,CAACU,OAAO;QAEnE;QACA,IAAIC,cAAc,IAAI,EAAE,EAAE;UACxB,OAAO;YAAE,GAAG5H,IAAI;YAAEE,EAAE;YAAE6G,MAAM,EAAE;UAAQ,CAAE;QAC1C,CAAC,MAAM;UACL,OAAO;YAAE,GAAG/G,IAAI;YAAEE,EAAE;YAAE6G,MAAM,EAAE;UAAS,CAAE;QAC3C;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EAAC,QAAAc,CAAA,G;qBAjeUvK,WAAW,EAAAwK,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAXhL,WAAW;IAAAiL,OAAA,EAAXjL,WAAW,CAAAkL,IAAA;IAAAC,UAAA,EAFV;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}