{"ast": null, "code": "//! moment.js locale configuration\n//! locale : <PERSON><PERSON> [mi]\n//! author : <PERSON> <<EMAIL>> : https://github.com/johnideal\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var mi = moment.defineLocale('mi', {\n    months: 'Kohi-tāte_Hui-tanguru_Poutū-te-rangi_Paenga-whāwhā_Haratua_Pipiri_Hōngoingoi_Here-turi-kōkā_Mahuru_Whiringa-ā-nuku_Whiringa-ā-rangi_Hakihea'.split('_'),\n    monthsShort: '<PERSON><PERSON>_Hui_Pou_Pae_Hara_Pipi_Hōngoi_Here_Mahu_Whi-nu_Whi-ra_Haki'.split('_'),\n    monthsRegex: /(?:['a-z\\u0101\\u014D\\u016B]+\\-?){1,3}/i,\n    monthsStrictRegex: /(?:['a-z\\u0101\\u014D\\u016B]+\\-?){1,3}/i,\n    monthsShortRegex: /(?:['a-z\\u0101\\u014D\\u016B]+\\-?){1,3}/i,\n    monthsShortStrictRegex: /(?:['a-z\\u0101\\u014D\\u016B]+\\-?){1,2}/i,\n    weekdays: 'Rātapu_Mane_Tūrei_Wenerei_Tāite_Paraire_Hātarei'.split('_'),\n    weekdaysShort: 'Ta_Ma_Tū_We_Tāi_Pa_Hā'.split('_'),\n    weekdaysMin: 'Ta_Ma_Tū_We_Tāi_Pa_Hā'.split('_'),\n    longDateFormat: {\n      LT: 'HH:mm',\n      LTS: 'HH:mm:ss',\n      L: 'DD/MM/YYYY',\n      LL: 'D MMMM YYYY',\n      LLL: 'D MMMM YYYY [i] HH:mm',\n      LLLL: 'dddd, D MMMM YYYY [i] HH:mm'\n    },\n    calendar: {\n      sameDay: '[i teie mahana, i] LT',\n      nextDay: '[apopo i] LT',\n      nextWeek: 'dddd [i] LT',\n      lastDay: '[inanahi i] LT',\n      lastWeek: 'dddd [whakamutunga i] LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: 'i roto i %s',\n      past: '%s i mua',\n      s: 'te hēkona ruarua',\n      ss: '%d hēkona',\n      m: 'he meneti',\n      mm: '%d meneti',\n      h: 'te haora',\n      hh: '%d haora',\n      d: 'he ra',\n      dd: '%d ra',\n      M: 'he marama',\n      MM: '%d marama',\n      y: 'he tau',\n      yy: '%d tau'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}º/,\n    ordinal: '%dº',\n    week: {\n      dow: 1,\n      // Monday is the first day of the week.\n      doy: 4 // The week that contains Jan 4th is the first week of the year.\n    }\n  });\n  return mi;\n});", "map": null, "metadata": {}, "sourceType": "script", "externalDependencies": []}