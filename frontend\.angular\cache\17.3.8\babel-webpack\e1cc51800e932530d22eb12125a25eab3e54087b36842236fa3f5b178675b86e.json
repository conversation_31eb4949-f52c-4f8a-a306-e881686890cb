{"ast": null, "code": "import { signal } from '@angular/core';\nimport { DatePipe } from '@angular/common';\nimport { UserRole } from 'src/app/core/models/user.model';\nimport { SpecificAvailabilityStatusType } from 'src/app/core/models/calendar.model';\nimport { SubSink } from 'subsink';\nimport { LessonStatus } from 'src/app/core/models/lesson.model';\nimport * as moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/dynamicdialog\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"src/app/core/services/auth.service\";\nimport * as i5 from \"src/app/core/services/calendar.service\";\nimport * as i6 from \"src/app/core/services/general.service\";\nimport * as i7 from \"src/app/core/services/classroom.service\";\nimport * as i8 from \"src/app/core/services/lesson.service\";\nimport * as i9 from \"src/app/core/services/toast.service\";\nimport * as i10 from \"@angular/forms\";\nimport * as i11 from \"../../../../shared/block-viewer/block-viewer.component\";\nimport * as i12 from \"primeng/button\";\nimport * as i13 from \"primeng/api\";\nimport * as i14 from \"primeng/dropdown\";\nfunction RequestLessonCalendarDialogComponent_ng_container_11_ng_template_2_div_0_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 17);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"src\", ctx_r1.classroomService.getClassroomTypeSmallIconPath(ctx_r1.selectedClassroom), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction RequestLessonCalendarDialogComponent_ng_container_11_ng_template_2_div_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"div\", 18);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.classroomService.getStudentNamesWithHTML(ctx_r1.selectedClassroom), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction RequestLessonCalendarDialogComponent_ng_container_11_ng_template_2_div_0_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", ctx_r1.selectedClassroom.language, \" - \", ctx_r1.selectedClassroom.teacher.lastName, \" \");\n  }\n}\nfunction RequestLessonCalendarDialogComponent_ng_container_11_ng_template_2_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtemplate(1, RequestLessonCalendarDialogComponent_ng_container_11_ng_template_2_div_0_img_1_Template, 1, 1, \"img\", 15)(2, RequestLessonCalendarDialogComponent_ng_container_11_ng_template_2_div_0_ng_container_2_Template, 2, 1, \"ng-container\", 16)(3, RequestLessonCalendarDialogComponent_ng_container_11_ng_template_2_div_0_ng_template_3_Template, 2, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const elseBlock_r3 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isTeacher);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isTeacher)(\"ngIfElse\", elseBlock_r3);\n  }\n}\nfunction RequestLessonCalendarDialogComponent_ng_container_11_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, RequestLessonCalendarDialogComponent_ng_container_11_ng_template_2_div_0_Template, 5, 3, \"div\", 13);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedClassroom);\n  }\n}\nfunction RequestLessonCalendarDialogComponent_ng_container_11_ng_template_3_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 17);\n  }\n  if (rf & 2) {\n    const classroom_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.classroomService.getClassroomTypeSmallIconPath(classroom_r4), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction RequestLessonCalendarDialogComponent_ng_container_11_ng_template_3_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"div\", 18)(2, \"br\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const classroom_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r1.classroomService.getStudentNamesWithHTML(classroom_r4), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction RequestLessonCalendarDialogComponent_ng_container_11_ng_template_3_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const classroom_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", classroom_r4.language, \" - \", classroom_r4.teacher.lastName, \" \");\n  }\n}\nfunction RequestLessonCalendarDialogComponent_ng_container_11_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtemplate(1, RequestLessonCalendarDialogComponent_ng_container_11_ng_template_3_img_1_Template, 1, 1, \"img\", 15)(2, RequestLessonCalendarDialogComponent_ng_container_11_ng_template_3_ng_container_2_Template, 3, 1, \"ng-container\", 16)(3, RequestLessonCalendarDialogComponent_ng_container_11_ng_template_3_ng_template_3_Template, 2, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 19);\n    i0.ɵɵelement(6, \"img\", 20);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const classroom_r4 = ctx.$implicit;\n    const elseBlock_r5 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isTeacher);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isTeacher)(\"ngIfElse\", elseBlock_r5);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.generalService.convertHoursToMinutesWithSuffix(ctx_r1.classroomService.getAccumulatedHoursLeft(classroom_r4.packages), true), \" available \");\n  }\n}\nfunction RequestLessonCalendarDialogComponent_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p-dropdown\", 8);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RequestLessonCalendarDialogComponent_ng_container_11_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedClassroom, $event) || (ctx_r1.selectedClassroom = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function RequestLessonCalendarDialogComponent_ng_container_11_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClassroomChange($event));\n    });\n    i0.ɵɵtemplate(2, RequestLessonCalendarDialogComponent_ng_container_11_ng_template_2_Template, 1, 1, \"ng-template\", 9)(3, RequestLessonCalendarDialogComponent_ng_container_11_ng_template_3_Template, 8, 4, \"ng-template\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 11);\n    i0.ɵɵlistener(\"click\", function RequestLessonCalendarDialogComponent_ng_container_11_Template_div_click_4_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onLessonArrange());\n    });\n    i0.ɵɵelement(5, \"button\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r1.availableClassrooms);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedClassroom);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isRequestingLesson());\n  }\n}\nfunction RequestLessonCalendarDialogComponent_ng_container_12_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 23)(2, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function RequestLessonCalendarDialogComponent_ng_container_12_ng_container_3_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleClassroomSelection());\n    });\n    i0.ɵɵelementStart(3, \"div\", 25)(4, \"span\", 26);\n    i0.ɵɵelement(5, \"img\", 27);\n    i0.ɵɵtext(6, \" Request Lesson\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction RequestLessonCalendarDialogComponent_ng_container_12_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 23)(2, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function RequestLessonCalendarDialogComponent_ng_container_12_ng_container_4_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleClassroomSelection());\n    });\n    i0.ɵɵelementStart(3, \"div\", 25)(4, \"span\", 26);\n    i0.ɵɵelement(5, \"img\", 29);\n    i0.ɵɵtext(6, \" Arrange Lesson\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(7, \"div\", 30)(8, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function RequestLessonCalendarDialogComponent_ng_container_12_ng_container_4_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAddAvailabilitySelected());\n    });\n    i0.ɵɵelementStart(9, \"div\", 25)(10, \"span\", 26);\n    i0.ɵɵelement(11, \"img\", 31);\n    i0.ɵɵtext(12, \" Add Availability\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 30)(14, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function RequestLessonCalendarDialogComponent_ng_container_12_ng_container_4_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onBlockAvailabilitySelected());\n    });\n    i0.ɵɵelementStart(15, \"div\", 25)(16, \"span\", 26);\n    i0.ɵɵelement(17, \"img\", 32);\n    i0.ɵɵtext(18, \" Remove Availability\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction RequestLessonCalendarDialogComponent_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 21)(2, \"div\", 22);\n    i0.ɵɵtemplate(3, RequestLessonCalendarDialogComponent_ng_container_12_ng_container_3_Template, 7, 0, \"ng-container\", 7)(4, RequestLessonCalendarDialogComponent_ng_container_12_ng_container_4_Template, 19, 0, \"ng-container\", 7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.role === ctx_r1.UserRoles.STUDENT);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.role === ctx_r1.UserRoles.TEACHER);\n  }\n}\nexport let RequestLessonCalendarDialogComponent = /*#__PURE__*/(() => {\n  class RequestLessonCalendarDialogComponent {\n    constructor(ref, config, datePipe, router, authService, calendarService, generalService, classroomService, lessonService, toastService) {\n      this.ref = ref;\n      this.config = config;\n      this.datePipe = datePipe;\n      this.router = router;\n      this.authService = authService;\n      this.calendarService = calendarService;\n      this.generalService = generalService;\n      this.classroomService = classroomService;\n      this.lessonService = lessonService;\n      this.toastService = toastService;\n      this.dialogData = {};\n      this.date = '';\n      this.startTime = '';\n      this.endTime = '';\n      this.UserRoles = UserRole;\n      this.SpecificAvailabilityStatuses = SpecificAvailabilityStatusType;\n      this.user = {};\n      this.role = UserRole.NONE;\n      this.subs = new SubSink();\n      this.availableClassrooms = [];\n      this.selectedClassroom = {};\n      this.showClassroomSelection = false;\n      this.selectedTeacher = {};\n      this.isRequestingLesson = signal(false);\n    }\n    ngOnInit() {\n      this.dialogData = this.config.data.dialogData;\n      this.role = this.authService.getLoggedInUser().role;\n      this.user = this.authService.getLoggedInUser();\n      const start = new Date(this.dialogData.startStr);\n      const end = new Date(this.dialogData.endStr);\n      this.date = this.datePipe.transform(start, 'EEEE MMMM d, y');\n      this.startTime = start.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n      this.endTime = end.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n      const timeString = start.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false\n      });\n      this.selectedTeacher = this.dialogData.teacher;\n      this.loadClassrooms();\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n    }\n    onAddAvailabilitySelected() {\n      let availabilityTypeData = this.formatEventData(this.SpecificAvailabilityStatuses.AVAILABLE, 'add');\n      delete availabilityTypeData.action;\n      console.log(availabilityTypeData);\n      this.subs.sink = this.calendarService.createSpecificTeacherAvailability(availabilityTypeData).subscribe(res => {\n        if (res) {\n          this.showToastAndCloseDialogWithData('Availability Added.', availabilityTypeData);\n          this.calendarService.setUpdateListener(true);\n        }\n      });\n    }\n    onBlockAvailabilitySelected() {\n      let availabilityTypeData = this.formatEventData(this.SpecificAvailabilityStatuses.BLOCK, 'block');\n      console.log(availabilityTypeData);\n      delete availabilityTypeData.action;\n      this.subs.sink = this.calendarService.createSpecificTeacherAvailability(availabilityTypeData).subscribe(res => {\n        if (res) {\n          this.showToastAndCloseDialogWithData('Availability blocked.', availabilityTypeData);\n          this.calendarService.setUpdateListener(true);\n        }\n      });\n    }\n    onArrangeLessonSelected() {\n      this.router.navigate(['/dashboard/calendar/booking-system']);\n      this.ref.close();\n    }\n    onDialogClose(data) {\n      this.ref.close(data);\n    }\n    onClassroomChange(event) {\n      console.log(event);\n      this.selectedClassroom = event;\n    }\n    onLessonArrange() {\n      if (this.isRequestingLesson()) return;\n      this.isRequestingLesson.set(true);\n      const status = this.authService.isTeacher ? LessonStatus.ARRANGED : LessonStatus.REQUESTED;\n      // const answered = this.authService.isTeacher ? LessonStatus.ARRANGED : LessonStatus.REQUESTED;\n      const classroomId = parseInt(this.selectedClassroom.id);\n      const lesson = {\n        classroomId,\n        status,\n        startingDate: this.generalService.formatLessonStartingDate(this.dialogData.startStr),\n        duration: this.generalService.convertMinutesToHours(this.generalService.getMinutesDifference(this.startTime, this.endTime)),\n        isRecccuring: false\n      };\n      console.log(lesson);\n      this.subs.sink = this.lessonService.create(lesson).subscribe({\n        next: res => {\n          console.log(res);\n          if (res) {\n            this.showToastAndCloseDialogWithData(this.authService.isTeacher ? 'Lesson arranged.' : 'Lesson Requested on ' + moment.parseZone(this.dialogData.startStr).format('DD-MM-YYYY HH:mm'), null);\n            this.calendarService.setUpdateListener(true);\n          }\n        },\n        complete: () => {\n          this.isRequestingLesson.set(false);\n        },\n        error: () => {\n          this.isRequestingLesson.set(false);\n        }\n      });\n    }\n    toggleClassroomSelection() {\n      this.showClassroomSelection = !this.showClassroomSelection;\n    }\n    formatEventData(type, action) {\n      const data = {\n        day: this.convertToDateString(this.dialogData.startStr),\n        fromTime: this.getFormattedTime(this.dialogData.startStr),\n        toTime: this.getFormattedTime(this.dialogData.endStr),\n        type: type,\n        action: action\n      };\n      return data;\n    }\n    loadClassrooms() {\n      this.subs.sink = this.classroomService.getLMSUserClassrooms(this.user.id).subscribe(classRooms => {\n        this.availableClassrooms = this.classroomService.filterAvailableClassrooms(classRooms);\n        if (this.authService.isStudent) {\n          this.availableClassrooms = this.availableClassrooms.filter(el => el.teacher.aspUserId === this.selectedTeacher.aspUserId);\n          const findFirstAvailableClassroomWithTeacher = this.availableClassrooms.find(item => item.teacher.id === this.selectedTeacher.id);\n          if (findFirstAvailableClassroomWithTeacher) {\n            this.selectedClassroom = this.availableClassrooms.find(item => item.teacher.id === this.selectedTeacher.id);\n          }\n          console.log(this.selectedTeacher);\n          console.log(this.selectedClassroom);\n        }\n      });\n    }\n    showToastAndCloseDialogWithData(text, data) {\n      this.toastService.setShowToastmessage({\n        severity: 'success',\n        summary: '',\n        detail: text\n      });\n      this.onDialogClose(data);\n      this.ref.close(data);\n    }\n    getFormattedTime(dateString) {\n      // Parse the date string using Moment.js\n      const date = moment(dateString);\n      // Format the date to display only the time in 24-hour format\n      const formattedDate = date.format(\"HH:mm\");\n      // Return the formatted date string\n      return formattedDate;\n    }\n    convertToDateString(dateString) {\n      const momentDate = moment(dateString);\n      const convertedDate = momentDate.format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');\n      return convertedDate;\n    }\n    static #_ = this.ɵfac = function RequestLessonCalendarDialogComponent_Factory(t) {\n      return new (t || RequestLessonCalendarDialogComponent)(i0.ɵɵdirectiveInject(i1.DynamicDialogRef), i0.ɵɵdirectiveInject(i1.DynamicDialogConfig), i0.ɵɵdirectiveInject(i2.DatePipe), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i5.CalendarService), i0.ɵɵdirectiveInject(i6.GeneralService), i0.ɵɵdirectiveInject(i7.ClassroomService), i0.ɵɵdirectiveInject(i8.LessonService), i0.ɵɵdirectiveInject(i9.ToastService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RequestLessonCalendarDialogComponent,\n      selectors: [[\"app-request-lesson-calendar-dialog\"]],\n      features: [i0.ɵɵProvidersFeature([DatePipe])],\n      decls: 13,\n      vars: 8,\n      consts: [[\"elseBlock\", \"\"], [\"header\", \" \", \"headerBackgroundImage\", \"/assets/images/dashboard/calendar/arrange-dialog-bg.png\", \"blockClass\", \"border-radius-bottom-10\", \"headerBlockClass\", \"py-1 border-round-lg bg-cover\", \"containerClass\", \"bg-white px-3 py-2\", 3, \"closeDialogEvent\", \"headerClass\", \"headerTextClass\", \"showCloseDialogIcon\"], [1, \"flex\", \"flex-column\", \"justify-content-center\", \"align-items-center\"], [1, \"mb-0\"], [1, \"mb-0\", \"font-semibold\", \"flex\", \"align-items-center\", \"gap-1\"], [\"src\", \"/assets/images/dashboard/calendar/request-lesson-calendar-icon-blue.svg\", \"height\", \"14\", 1, \"icon-btn\"], [1, \"font-semibold\"], [4, \"ngIf\"], [\"appendTo\", \"body\", \"placeholder\", \"Select Classroom\", \"styleClass\", \"dropdown-blue small-dropdown-items\", 3, \"ngModelChange\", \"options\", \"ngModel\"], [\"pTemplate\", \"selectedItem\"], [\"pTemplate\", \"item\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", 3, \"click\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"Ok\", \"icon\", \"pi pi-check\", \"iconPos\", \"right\", 1, \"p-button-sm\", \"p-button-raised\", \"p-button-rounded\", \"blue-teal\", \"mt-3\", 3, \"disabled\"], [\"class\", \"flex align-items-center gap-2\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"height\", \"12\", 3, \"src\", 4, \"ngIf\"], [4, \"ngIf\", \"ngIfElse\"], [\"height\", \"12\", 3, \"src\"], [3, \"innerHTML\"], [1, \"flex\", \"gap-1\"], [\"src\", \"/assets/images/dashboard/calendar/check-circled.svg\", 2, \"width\", \"12px\"], [1, \"grid\", \"relative\"], [1, \"col-9\", \"mx-auto\"], [1, \"flex\", \"justify-content-evenly\", \"my-2\"], [\"pbutton\", \"\", \"type\", \"button\", 1, \"p-element\", \"p-button-rounded\", \"p-button-sm\", \"p-button\", \"p-component\", \"w-full\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"jus\", \"ju\"], [1, \"font-sm\", \"flex\", \"justify-content-center\", \"align-items-center\", \"gap-1\"], [\"src\", \"/assets/images/dashboard/calendar/request-lesson-icon-white.svg\", \"height\", \"14\", 1, \"icon-btn\"], [\"pbutton\", \"\", \"type\", \"button\", 1, \"p-element\", \"justify-content-center\", \"p-button-rounded\", \"p-button-outlined\", \"p-button-sm\", \"p-button\", \"p-component\", \"w-full\", 3, \"click\"], [\"src\", \"/assets/images/dashboard/calendar/rearrange.svg\", \"height\", \"14\", 1, \"icon-btn\"], [1, \"flex\", \"justify-content-evenly\", \"mb-2\"], [\"src\", \"/assets/images/dashboard/calendar/calendar-add.svg\", \"height\", \"14\", 1, \"icon-btn\"], [\"src\", \"/assets/images/dashboard/calendar/calendar-block.svg\", \"height\", \"14\", 1, \"icon-btn\"]],\n      template: function RequestLessonCalendarDialogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"app-block-viewer\", 1);\n          i0.ɵɵlistener(\"closeDialogEvent\", function RequestLessonCalendarDialogComponent_Template_app_block_viewer_closeDialogEvent_0_listener() {\n            return ctx.onDialogClose();\n          });\n          i0.ɵɵelementStart(1, \"div\", 2)(2, \"p\", 3);\n          i0.ɵɵtext(3, \"You've selected\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\", 4);\n          i0.ɵɵelement(5, \"img\", 5);\n          i0.ɵɵtext(6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"p\");\n          i0.ɵɵtext(8, \" from \");\n          i0.ɵɵelementStart(9, \"span\", 6);\n          i0.ɵɵtext(10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(11, RequestLessonCalendarDialogComponent_ng_container_11_Template, 6, 3, \"ng-container\", 7)(12, RequestLessonCalendarDialogComponent_ng_container_12_Template, 5, 2, \"ng-container\", 7);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"headerClass\", \"justify-content-center my-0\")(\"headerTextClass\", \"font-base font-semibold justify-content-center\")(\"showCloseDialogIcon\", true);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate1(\" \", ctx.date, \" \");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate2(\"\", ctx.startTime, \" - \", ctx.endTime, \"\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showClassroomSelection);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.showClassroomSelection);\n        }\n      },\n      dependencies: [i10.NgControlStatus, i10.NgModel, i2.NgIf, i11.BlockViewerComponent, i12.ButtonDirective, i13.PrimeTemplate, i14.Dropdown],\n      styles: [\".icon-btn[_ngcontent-%COMP%]{width:.85rem}[_nghost-%COMP%]     .small-dropdown-items .p-dropdown-panel .p-dropdown-items .p-dropdown-item{padding:0}\"]\n    });\n  }\n  return RequestLessonCalendarDialogComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}