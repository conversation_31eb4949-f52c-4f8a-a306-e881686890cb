{"ast": null, "code": "import { transition, trigger, style, animate } from '@angular/animations';\nexport const slideUpDownAnimation = trigger('slideUpDown', [transition(':enter', [style({\n  transform: 'translateY(-100%)'\n}), animate('200ms ease-in', style({\n  transform: 'translateY(0%)'\n}))]), transition(':leave', [animate('200ms ease-in', style({\n  transform: 'translateY(-100%)'\n}))])]);\nexport const slideInOut = trigger('slideInOut', [transition(':enter', [style({\n  opacity: 0\n}), animate('300ms', style({\n  opacity: 1\n}))]), transition(':leave', [animate('300ms', style({\n  opacity: 0\n}))])]);\nexport const slideInOut2 = trigger('slideInOut2', [transition(':enter', [style({\n  transform: 'translateY(-100%)'\n}), animate('200ms ease-in', style({\n  transform: 'translateY(0%)'\n}))]), transition(':leave', [animate('200ms ease-in', style({\n  transform: 'translateY(-100%)'\n}))])]);", "map": {"version": 3, "names": ["transition", "trigger", "style", "animate", "slideUpDownAnimation", "transform", "slideInOut", "opacity", "slideInOut2"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\helpers\\my-animations.ts"], "sourcesContent": ["import {\r\n    transition,\r\n    trigger,\r\n    style,\r\n    animate,\r\n} from '@angular/animations';\r\nexport const slideUpDownAnimation = trigger('slideUpDown', [\r\n    transition(':enter', [\r\n        style({ transform: 'translateY(-100%)' }),\r\n        animate('200ms ease-in', style({ transform: 'translateY(0%)' }))\r\n    ]),\r\n    transition(':leave', [\r\n        animate('200ms ease-in', style({ transform: 'translateY(-100%)' }))\r\n    ])\r\n]);\r\nexport const slideInOut = trigger('slideInOut', [\r\n    transition(':enter', [\r\n        style({ opacity: 0 }),\r\n        animate('300ms', style({ opacity: 1 }))\r\n    ]),\r\n    transition(':leave', [\r\n        animate('300ms', style({ opacity: 0 }))\r\n    ])\r\n]);\r\n\r\nexport const slideInOut2 =\r\n    trigger('slideInOut2', [\r\n        transition(':enter', [\r\n            style({ transform: 'translateY(-100%)' }),\r\n            animate('200ms ease-in', style({ transform: 'translateY(0%)' }))\r\n        ]),\r\n        transition(':leave', [\r\n            animate('200ms ease-in', style({ transform: 'translateY(-100%)' }))\r\n        ])\r\n    ])"], "mappings": "AAAA,SACIA,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,OAAO,QACJ,qBAAqB;AAC5B,OAAO,MAAMC,oBAAoB,GAAGH,OAAO,CAAC,aAAa,EAAE,CACvDD,UAAU,CAAC,QAAQ,EAAE,CACjBE,KAAK,CAAC;EAAEG,SAAS,EAAE;AAAmB,CAAE,CAAC,EACzCF,OAAO,CAAC,eAAe,EAAED,KAAK,CAAC;EAAEG,SAAS,EAAE;AAAgB,CAAE,CAAC,CAAC,CACnE,CAAC,EACFL,UAAU,CAAC,QAAQ,EAAE,CACjBG,OAAO,CAAC,eAAe,EAAED,KAAK,CAAC;EAAEG,SAAS,EAAE;AAAmB,CAAE,CAAC,CAAC,CACtE,CAAC,CACL,CAAC;AACF,OAAO,MAAMC,UAAU,GAAGL,OAAO,CAAC,YAAY,EAAE,CAC5CD,UAAU,CAAC,QAAQ,EAAE,CACjBE,KAAK,CAAC;EAAEK,OAAO,EAAE;AAAC,CAAE,CAAC,EACrBJ,OAAO,CAAC,OAAO,EAAED,KAAK,CAAC;EAAEK,OAAO,EAAE;AAAC,CAAE,CAAC,CAAC,CAC1C,CAAC,EACFP,UAAU,CAAC,QAAQ,EAAE,CACjBG,OAAO,CAAC,OAAO,EAAED,KAAK,CAAC;EAAEK,OAAO,EAAE;AAAC,CAAE,CAAC,CAAC,CAC1C,CAAC,CACL,CAAC;AAEF,OAAO,MAAMC,WAAW,GACpBP,OAAO,CAAC,aAAa,EAAE,CACnBD,UAAU,CAAC,QAAQ,EAAE,CACjBE,KAAK,CAAC;EAAEG,SAAS,EAAE;AAAmB,CAAE,CAAC,EACzCF,OAAO,CAAC,eAAe,EAAED,KAAK,CAAC;EAAEG,SAAS,EAAE;AAAgB,CAAE,CAAC,CAAC,CACnE,CAAC,EACFL,UAAU,CAAC,QAAQ,EAAE,CACjBG,OAAO,CAAC,eAAe,EAAED,KAAK,CAAC;EAAEG,SAAS,EAAE;AAAmB,CAAE,CAAC,CAAC,CACtE,CAAC,CACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}