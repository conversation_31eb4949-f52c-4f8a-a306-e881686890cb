{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { forkJoin } from 'rxjs';\nimport { map, switchMap, tap } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport { Level } from '../models/classroom.model';\nimport { PackageState, PackageType } from '../models/package.model';\nimport { UserRole } from '../models/user.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\nconst BACKEND_URL = environment.apiUrl;\nconst BACKEND_LMS_URL = environment.apiUrl + \"/LMS/\";\nexport class PackageService {\n  constructor(http, authService) {\n    this.http = http;\n    this.authService = authService;\n    this.packageFilterHelper = {\n      totalHours: ['Any', 5, 10, 20, 50],\n      hoursLeft: ['Any', 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25],\n      level: ['Any', Level.A1, Level.A2, Level.B1, Level.B2, Level.C1, Level.C2, Level.TBD, Level.BS],\n      type: ['Any', PackageType.PAID, PackageType.REGULAR, PackageType.FLEX, PackageType.BUSINESS, PackageType.GIFT, PackageType.TRIAL],\n      state: ['Any', PackageState.ACTIVE, PackageState.COMPLETED, PackageState.PAUSED, PackageState.EXPIRED, PackageState.CANCELED, PackageState.TRANSFERED],\n      hasExtention: ['Any', 'Yes', 'No']\n    };\n    this.packageFilter = {\n      totalHours: 'Any',\n      hoursLeft: 'Any',\n      level: 'Any',\n      type: 'Any',\n      state: 'Any',\n      hasExtention: 'Any',\n      expiresOn_from: {},\n      expiresOn_to: {},\n      stratedAt_to: {},\n      stratedAt_from: {},\n      purchasedAt_from: {},\n      purchasedAt_to: {}\n    };\n  }\n  getPackageFilter() {\n    return this.packageFilterHelper;\n  }\n  getCurrentPackageFilter() {\n    return this.packageFilter;\n  }\n  setCurrentPackageFilter(packageFilter) {\n    this.packageFilter = packageFilter;\n  }\n  getPackageFilterWithoutTheCurrent() {\n    let current = this.getCurrentPackageFilter();\n    let remainingTotalHours = this.packageFilterHelper.totalHours.filter(el => {\n      return el !== current.totalHours;\n    });\n    let hoursLeft = this.packageFilterHelper.hoursLeft.filter(el => {\n      return el !== current.hoursLeft;\n    });\n    let level = this.packageFilterHelper.level.filter(el => {\n      return el !== current.level;\n    });\n    let type = this.packageFilterHelper.type.filter(el => {\n      return el !== current.type;\n    });\n    let state = this.packageFilterHelper.state.filter(el => {\n      return el !== current.state;\n    });\n    let hasExtention = this.packageFilterHelper.hasExtention.filter(el => {\n      return el !== current.hasExtention;\n    });\n    return {\n      totalHours: remainingTotalHours,\n      hoursLeft: hoursLeft,\n      level: level,\n      type: type,\n      state: state,\n      hasExtention: hasExtention\n    };\n  }\n  getClassroomPackages(classroomId) {\n    return this.http.get(BACKEND_URL + \"/Package/GetClassroomPackages?ClassroomId=\" + classroomId).pipe(switchMap(packages => {\n      return this.gerPackageExtraDetails(packages);\n    }), tap(res => {}));\n  }\n  getUserPackages(userId) {\n    let endpoint = this.authService.getUserRole() === UserRole.TEACHER ? \"/Package/GetTeacherPackages?TeacherId=\" + userId : \"/Package/GetStudentPackages?StudentId=\" + userId;\n    return this.http.get(BACKEND_URL + endpoint).pipe(switchMap(packages => {\n      return this.gerPackageExtraDetails(packages);\n    }));\n  }\n  // new endpoint\n  getAllUserPackages(userId) {\n    let endpoint = this.authService.getUserRole() === UserRole.TEACHER ? \"GetAllTeacherPackages?userId=\" + userId : \"GetStudentPackages?StudentId=\" + userId;\n    return this.http.get(BACKEND_LMS_URL + endpoint);\n    // .pipe(\n    // switchMap((packages: Package[]) => {\n    // return this.gerPackageExtraDetails(packages);\n    // })\n    // )\n  }\n  getPackageExtentions(packageId) {\n    return this.http.get(BACKEND_URL + `/Package/GetPackageExtentions?packageId=${packageId}`);\n  }\n  getPackageLessonLevels(packageId) {\n    return this.http.get(BACKEND_URL + `/Package/GetPackageLessonsLevels?packageId=${packageId}`);\n  }\n  getClassroomActivePackage(classroomId) {\n    return this.http.get(BACKEND_URL + \"/Package/GetClassroomActivePackage?ClassroomId=\" + classroomId);\n  }\n  getPackagesToBuy() {\n    return this.http.get(BACKEND_URL + \"/Package/PackagesToBuy\");\n  }\n  // new endpoint\n  getUpdatedPackagesToBuy(req) {\n    return this.http.post(BACKEND_LMS_URL + \"PackagesToBuy\", req);\n  }\n  getSplitPackagesToBuy(req) {\n    return this.http.post(BACKEND_LMS_URL + \"SplitPackagesToBuy\", req);\n  }\n  convertPackageDates(pack) {\n    return {\n      stratedAt: new Date(pack.stratedAt),\n      purchasedAt: new Date(pack.purchasedAt),\n      expiresOn: new Date(pack.expiresOn)\n    };\n  }\n  findLessonPackageById(packages, lessonPackageId) {\n    return packages.find(pckage => pckage.id === lessonPackageId);\n  }\n  getPackageExtras(packageId) {\n    return forkJoin(this.getPackageExtentions(packageId), this.getPackageLessonLevels(packageId));\n  }\n  gerPackageExtraDetails(packages) {\n    if (packages.length == 0) {\n      return [];\n    }\n    return forkJoin(packages.map(pack => {\n      let convertedDates = this.convertPackageDates(pack);\n      pack.stratedAt = convertedDates.stratedAt;\n      pack.purchasedAt = convertedDates.purchasedAt;\n      pack.expiresOn = convertedDates.expiresOn;\n      return this.getPackageExtras(pack.id).pipe(map(x => {\n        for (let extension of x[0]) {\n          extension.fromDate = new Date(extension.fromDate);\n          extension.toDate = new Date(extension.toDate);\n        }\n        for (let lessonLevel of x[1]) {\n          lessonLevel.lessonsStarted = new Date(lessonLevel.lessonsStarted);\n          lessonLevel.lessonsEnded = new Date(lessonLevel.lessonsEnded);\n        }\n        let packageWithExtras = {\n          ...pack,\n          ...{\n            extensions: x[0],\n            lessons_level: x[1]\n          }\n        };\n        return packageWithExtras;\n      }));\n    }));\n  }\n  requestTrial(req) {\n    return this.http.post(BACKEND_URL + \"/Package/RequestTrial\", req);\n  }\n  requestbuyPackage(req) {\n    return this.http.post(BACKEND_URL + \"/Package/BuyPackage\", req);\n  }\n  upgradePackage(packageId, newLevel) {\n    return this.http.post(BACKEND_URL + \"/Package/UpgradePackage?packageId=\" + packageId + \"&newLevel=\" + newLevel, {});\n  }\n  getExtentionOptions() {\n    return this.http.get(BACKEND_URL + \"/Package/GetExtentionOptions\");\n  }\n  buyExtension(req) {\n    return this.http.post(BACKEND_URL + \"/Package/BuyExtension\", req);\n  }\n  createRevolutOrder(req) {\n    return this.http.post(BACKEND_LMS_URL + \"CreateRevolutOrder\", req);\n  }\n  createSplitRevolutOrder(req) {\n    return this.http.post(BACKEND_LMS_URL + \"CreateSplitRevolutOrder\", req);\n  }\n  completeOrder(req) {\n    return this.http.post(BACKEND_LMS_URL + \"CompleteOrder\", req);\n  }\n  completeSplitOrder(req) {\n    return this.http.post(BACKEND_LMS_URL + \"CompleteSplitOrder\", req);\n  }\n  addPurchaseEvent(purchaseData) {\n    const url = 'https://api.leaddyno.com/v1/purchases';\n    const headers = {\n      'Content-Type': 'application/json',\n      'X-Bypass-Error-Interceptor': 'true'\n    };\n    return this.http.post(url, purchaseData, {\n      headers\n    });\n  }\n  leadDynoGetAffiliateEvent(data) {\n    const url = 'https://api.leaddyno.com/v1/affiliates';\n    const headers = {\n      'X-Bypass-Error-Interceptor': 'true',\n      \"Content-Type\": \"application/json\",\n      key: \"165d71696287208d956a45cffb6d8112b66f0e81\",\n      affiliate_code: \"CMSSLN\"\n    };\n    return this.http.get(url, {\n      headers\n    });\n  }\n  getLeadDynoAffiliateByAffiliateCode(affiliateCode, privateKey) {\n    const url = 'https://api.leaddyno.com/v1/affiliates/by_affiliate_code';\n    // Constructing request headers\n    const headers = new HttpHeaders({\n      'X-Bypass-Error-Interceptor': 'true',\n      'Content-Type': 'application/json',\n      'key': privateKey\n    });\n    // Constructing request parameters\n    const params = {\n      'affiliate_code': affiliateCode\n    };\n    console.log(url, headers, params);\n    // Making the HTTP GET request\n    return this.http.get(url, {\n      headers,\n      params\n    });\n  }\n  /**\n   * checks if a package can be frozen based on its state and type.\n   * The function iterates through an array of packages and checks if any package is in the ongoing or purchased state and has a type of premium or flex.\n   * If such a package is found, the enableFreeze variable is set to true.\n   * The function returns the value of enableFreeze, indicating whether any package can be frozen.\n   */\n  checkIfPackageCanBeFrozen(packages) {\n    let enableFreeze = false;\n    for (const pack of packages) {\n      if ((pack.state.toLowerCase() === PackageState.ONGOING.toLowerCase() || pack.state.toLowerCase() === PackageState.PURCHASED.toLowerCase()) && (pack.type === PackageType.PREMIUM || pack.type === PackageType.FLEX)) {\n        enableFreeze = true;\n        ;\n      }\n    }\n    return enableFreeze;\n  }\n  userIsOnFlexOrPremiumPackage(packages) {\n    // Check if any package is of type \"Flex\" or \"Premium\"\n    return packages.some(iterPackage => (iterPackage.type === PackageType.FLEX || iterPackage.type === PackageType.PREMIUM) && (iterPackage.state.toLowerCase() === PackageState.ONGOING.toLowerCase() || iterPackage.state.toLowerCase() === PackageState.PURCHASED.toLowerCase()));\n  }\n  static #_ = this.ɵfac = function PackageService_Factory(t) {\n    return new (t || PackageService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: PackageService,\n    factory: PackageService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "fork<PERSON><PERSON>n", "map", "switchMap", "tap", "environment", "Level", "PackageState", "PackageType", "UserRole", "BACKEND_URL", "apiUrl", "BACKEND_LMS_URL", "PackageService", "constructor", "http", "authService", "packageFilterHelper", "totalHours", "hoursLeft", "level", "A1", "A2", "B1", "B2", "C1", "C2", "TBD", "BS", "type", "PAID", "REGULAR", "FLEX", "BUSINESS", "GIFT", "TRIAL", "state", "ACTIVE", "COMPLETED", "PAUSED", "EXPIRED", "CANCELED", "TRANSFERED", "hasExtention", "packageFilter", "expiresOn_from", "expiresOn_to", "stratedAt_to", "stratedAt_from", "purchasedAt_from", "purchasedAt_to", "getPackageFilter", "getCurrentPackageFilter", "setCurrentPackageFilter", "getPackageFilterWithoutTheCurrent", "current", "remainingTotalHours", "filter", "el", "getClassroomPackages", "classroomId", "get", "pipe", "packages", "gerPackageExtraDetails", "res", "getUserPackages", "userId", "endpoint", "getUserRole", "TEACHER", "getAllUserPackages", "getPackageExtentions", "packageId", "getPackageLessonLevels", "getClassroomActivePackage", "getPackagesToBuy", "getUpdatedPackagesToBuy", "req", "post", "getSplitPackagesToBuy", "convertPackageDates", "pack", "stratedAt", "Date", "purchasedAt", "expiresOn", "findLessonPackageById", "lessonPackageId", "find", "pckage", "id", "getPackageExtras", "length", "convertedDates", "x", "extension", "fromDate", "toDate", "lessonLevel", "lessonsStarted", "lessonsEnded", "packageWithExtras", "extensions", "lessons_level", "requestTrial", "requestbuyPackage", "upgradePackage", "newLevel", "getExtentionOptions", "buyExtension", "createRevolutOrder", "createSplitRevolutOrder", "completeOrder", "completeSplitOrder", "addPurchaseEvent", "purchaseData", "url", "headers", "leadDynoGetAffiliateEvent", "data", "key", "affiliate_code", "getLeadDynoAffiliateByAffiliateCode", "affiliateCode", "privateKey", "params", "console", "log", "checkIfPackageCanBeFrozen", "enableFreeze", "toLowerCase", "ONGOING", "PURCHASED", "PREMIUM", "userIsOnFlexOrPremiumPackage", "some", "iterPackage", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "AuthService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\services\\package.service.ts"], "sourcesContent": ["import { HttpClient, HttpHeaders } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { forkJoin, Observable, of } from 'rxjs';\r\nimport { map, switchMap, tap } from 'rxjs/operators';\r\nimport { environment } from 'src/environments/environment';\r\nimport { Level } from '../models/classroom.model';\r\nimport { PackageFilter, Package, PackageExtention, PackageFilterHelper, PackageLessonsLevel, PackageState, PackageType, PackageToBuy, PackageToExtend, PackageToBuyNew, SplitPackagesToBuyResponse } from '../models/package.model';\r\nimport { UserRole } from '../models/user.model';\r\nimport { AuthService } from './auth.service';\r\nimport { GeneralService } from './general.service';\r\nimport { UserService } from './user.service';\r\nconst BACKEND_URL = environment.apiUrl;\r\nconst BACKEND_LMS_URL = environment.apiUrl + \"/LMS/\";\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class PackageService {\r\n\r\n  public packageFilterHelper: PackageFilterHelper = {\r\n    totalHours: ['Any', 5, 10, 20, 50],\r\n    hoursLeft: ['Any', 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25],\r\n    level: ['Any', Level.A1, Level.A2, Level.B1, Level.B2, Level.C1, Level.C2, Level.TBD, Level.BS],\r\n    type: ['Any', PackageType.PAID, PackageType.REGULAR, PackageType.FLEX, PackageType.BUSINESS, PackageType.GIFT, PackageType.TRIAL],\r\n    state: ['Any', PackageState.ACTIVE, PackageState.COMPLETED, PackageState.PAUSED, PackageState.EXPIRED, PackageState.CANCELED, PackageState.TRANSFERED],\r\n    hasExtention: ['Any', 'Yes', 'No']\r\n  }\r\n\r\n  public packageFilter: PackageFilter = {\r\n    totalHours: 'Any',\r\n    hoursLeft: 'Any',\r\n    level: 'Any',\r\n    type: 'Any',\r\n    state: 'Any',\r\n    hasExtention: 'Any',\r\n    expiresOn_from: {} as Date,\r\n    expiresOn_to: {} as Date,\r\n    stratedAt_to: {} as Date,\r\n    stratedAt_from: {} as Date,\r\n    purchasedAt_from: {} as Date,\r\n    purchasedAt_to: {} as Date,\r\n  }\r\n\r\n  constructor(\r\n    private http: HttpClient,\r\n    private authService: AuthService\r\n  ) { }\r\n\r\n  public getPackageFilter(): PackageFilterHelper {\r\n    return this.packageFilterHelper;\r\n  }\r\n\r\n  public getCurrentPackageFilter(): PackageFilter {\r\n    return this.packageFilter;\r\n  }\r\n\r\n  public setCurrentPackageFilter(packageFilter: PackageFilter) {\r\n    this.packageFilter = packageFilter;\r\n  }\r\n\r\n  public getPackageFilterWithoutTheCurrent(): PackageFilterHelper {\r\n    let current = this.getCurrentPackageFilter();\r\n    let remainingTotalHours = this.packageFilterHelper.totalHours.filter(el => { return el !== current.totalHours });\r\n    let hoursLeft = this.packageFilterHelper.hoursLeft.filter(el => { return el !== current.hoursLeft });\r\n    let level = this.packageFilterHelper.level.filter(el => { return el !== current.level });\r\n    let type = this.packageFilterHelper.type.filter(el => { return el !== current.type });\r\n    let state = this.packageFilterHelper.state.filter(el => { return el !== current.state });\r\n    let hasExtention = this.packageFilterHelper.hasExtention.filter(el => { return el !== current.hasExtention });\r\n    return {\r\n      totalHours: remainingTotalHours,\r\n      hoursLeft: hoursLeft,\r\n      level: level,\r\n      type: type,\r\n      state: state,\r\n      hasExtention: hasExtention\r\n    }\r\n  }\r\n\r\n  getClassroomPackages(classroomId: string) {\r\n    return this.http.get<Package[] | any[]>(BACKEND_URL + \"/Package/GetClassroomPackages?ClassroomId=\" + classroomId)\r\n      .pipe(\r\n        switchMap((packages: Package[]) => {\r\n          return this.gerPackageExtraDetails(packages);\r\n        }),\r\n        tap((res) => {\r\n        })\r\n      )\r\n  }\r\n\r\n  getUserPackages(userId: string) {\r\n    let endpoint = this.authService.getUserRole() === UserRole.TEACHER ? \"/Package/GetTeacherPackages?TeacherId=\" + userId : \"/Package/GetStudentPackages?StudentId=\" + userId;\r\n    return this.http.get<Package[] | any[]>(BACKEND_URL + endpoint)\r\n      .pipe(\r\n        switchMap((packages: Package[]) => {\r\n          return this.gerPackageExtraDetails(packages);\r\n        })\r\n      )\r\n  }\r\n  // new endpoint\r\n  getAllUserPackages(userId: string) {\r\n    let endpoint = this.authService.getUserRole() === UserRole.TEACHER ? \"GetAllTeacherPackages?userId=\" + userId : \"GetStudentPackages?StudentId=\" + userId;\r\n    return this.http.get<Package[] | any[]>(BACKEND_LMS_URL + endpoint)\r\n    // .pipe(\r\n    // switchMap((packages: Package[]) => {\r\n    // return this.gerPackageExtraDetails(packages);\r\n    // })\r\n    // )\r\n  }\r\n\r\n  getPackageExtentions(packageId: string) {\r\n    return this.http.get<PackageExtention[]>(BACKEND_URL + `/Package/GetPackageExtentions?packageId=${packageId}`)\r\n  }\r\n\r\n  getPackageLessonLevels(packageId: string) {\r\n    return this.http.get<PackageLessonsLevel[]>(BACKEND_URL + `/Package/GetPackageLessonsLevels?packageId=${packageId}`)\r\n  }\r\n\r\n  getClassroomActivePackage(classroomId: string) {\r\n    return this.http.get<Package>(BACKEND_URL + \"/Package/GetClassroomActivePackage?ClassroomId=\" + classroomId)\r\n  }\r\n\r\n  getPackagesToBuy() {\r\n    return this.http.get<PackageToBuy[]>(BACKEND_URL + \"/Package/PackagesToBuy\")\r\n  }\r\n  // new endpoint\r\n  getUpdatedPackagesToBuy(req: any) {\r\n    return this.http.post<PackageToBuyNew[]>(BACKEND_LMS_URL + \"PackagesToBuy\", req)\r\n  }\r\n\r\n  getSplitPackagesToBuy(req: any) {\r\n    return this.http.post<SplitPackagesToBuyResponse[]>(BACKEND_LMS_URL + \"SplitPackagesToBuy\", req)\r\n  }\r\n\r\n  convertPackageDates(pack: Package) {\r\n    return {\r\n      stratedAt: new Date(pack.stratedAt),\r\n      purchasedAt: new Date(pack.purchasedAt),\r\n      expiresOn: new Date(pack.expiresOn)\r\n    }\r\n  }\r\n\r\n  findLessonPackageById(packages: Package[], lessonPackageId: string): Package | undefined {\r\n    return packages.find((pckage: Package) => pckage.id === lessonPackageId);\r\n  }\r\n\r\n  public getPackageExtras(packageId: string) {\r\n    return forkJoin(\r\n      this.getPackageExtentions(packageId),\r\n      this.getPackageLessonLevels(packageId)\r\n    )\r\n  }\r\n\r\n  gerPackageExtraDetails(packages: Package[]) {\r\n    if (packages.length == 0) {\r\n      return []\r\n    }\r\n    return forkJoin(packages.map((pack: Package) => {\r\n\r\n      let convertedDates = this.convertPackageDates(pack)\r\n      pack.stratedAt = convertedDates.stratedAt\r\n      pack.purchasedAt = convertedDates.purchasedAt\r\n      pack.expiresOn = convertedDates.expiresOn\r\n      return this.getPackageExtras(pack.id).pipe(\r\n        map(x => {\r\n          for (let extension of x[0]) {\r\n            extension.fromDate = new Date(extension.fromDate);\r\n            extension.toDate = new Date(extension.toDate);\r\n          }\r\n          for (let lessonLevel of x[1]) {\r\n            lessonLevel.lessonsStarted = new Date(lessonLevel.lessonsStarted);\r\n            lessonLevel.lessonsEnded = new Date(lessonLevel.lessonsEnded);\r\n          }\r\n          let packageWithExtras: Package = ({ ...pack, ...{ extensions: x[0], lessons_level: x[1] } });\r\n          return packageWithExtras;\r\n        }))\r\n    }))\r\n  }\r\n\r\n  requestTrial(req: any) {\r\n    return this.http.post(BACKEND_URL + \"/Package/RequestTrial\", req)\r\n  }\r\n\r\n  requestbuyPackage(req: any) {\r\n    return this.http.post(BACKEND_URL + \"/Package/BuyPackage\", req)\r\n  }\r\n\r\n  upgradePackage(packageId: any, newLevel: string) {\r\n    return this.http.post(BACKEND_URL + \"/Package/UpgradePackage?packageId=\" + packageId + \"&newLevel=\" + newLevel, {})\r\n  }\r\n\r\n  getExtentionOptions(): Observable<PackageToExtend[]> {\r\n    return this.http.get<PackageToExtend[]>(BACKEND_URL + \"/Package/GetExtentionOptions\");\r\n  }\r\n\r\n  buyExtension(req: any) {\r\n    return this.http.post(BACKEND_URL + \"/Package/BuyExtension\", req);\r\n  }\r\n\r\n  createRevolutOrder(req: any) {\r\n    return this.http.post(BACKEND_LMS_URL + \"CreateRevolutOrder\", req);\r\n  }\r\n\r\n  createSplitRevolutOrder(req: any) {\r\n    return this.http.post(BACKEND_LMS_URL + \"CreateSplitRevolutOrder\", req);\r\n  }\r\n\r\n  completeOrder(req: any) {\r\n    return this.http.post(BACKEND_LMS_URL + \"CompleteOrder\", req);\r\n  }\r\n\r\n  completeSplitOrder(req: any) {\r\n    return this.http.post(BACKEND_LMS_URL + \"CompleteSplitOrder\", req);\r\n  }\r\n\r\n  addPurchaseEvent(purchaseData: any) {\r\n    const url = 'https://api.leaddyno.com/v1/purchases';\r\n    const headers = { 'Content-Type': 'application/json', 'X-Bypass-Error-Interceptor': 'true' };\r\n\r\n    return this.http.post(url, purchaseData, { headers });\r\n  }\r\n\r\n  leadDynoGetAffiliateEvent(data?: any) {\r\n    const url = 'https://api.leaddyno.com/v1/affiliates';\r\n    const headers = {\r\n      'X-Bypass-Error-Interceptor': 'true',\r\n      \"Content-Type\": \"application/json\",\r\n      key: \"165d71696287208d956a45cffb6d8112b66f0e81\",\r\n      affiliate_code: \"CMSSLN\",\r\n    };\r\n\r\n    return this.http.get(url, { headers });\r\n  }\r\n\r\n  getLeadDynoAffiliateByAffiliateCode(affiliateCode: string, privateKey: string): Observable<any> {\r\n    const url = 'https://api.leaddyno.com/v1/affiliates/by_affiliate_code';\r\n\r\n    // Constructing request headers\r\n    const headers = new HttpHeaders({\r\n      'X-Bypass-Error-Interceptor': 'true',\r\n      'Content-Type': 'application/json',\r\n      'key': privateKey\r\n    });\r\n\r\n    // Constructing request parameters\r\n    const params = {\r\n      'affiliate_code': affiliateCode\r\n    };\r\n\r\n    console.log(url, headers, params);\r\n\r\n    // Making the HTTP GET request\r\n    return this.http.get(url, { headers, params });\r\n  }\r\n\r\n  /**\r\n   * checks if a package can be frozen based on its state and type.\r\n   * The function iterates through an array of packages and checks if any package is in the ongoing or purchased state and has a type of premium or flex.\r\n   * If such a package is found, the enableFreeze variable is set to true.\r\n   * The function returns the value of enableFreeze, indicating whether any package can be frozen.\r\n   */\r\n  checkIfPackageCanBeFrozen(packages: Package[]): boolean {\r\n    let enableFreeze = false;\r\n\r\n    for (const pack of packages) {\r\n      if ((pack.state.toLowerCase() === PackageState.ONGOING.toLowerCase() || pack.state.toLowerCase() === PackageState.PURCHASED.toLowerCase())\r\n        && (pack.type === PackageType.PREMIUM || pack.type === PackageType.FLEX)) {\r\n        enableFreeze = true;;\r\n      }\r\n    }\r\n\r\n    return enableFreeze;\r\n  }\r\n\r\n\r\n  userIsOnFlexOrPremiumPackage(packages: Package[]): boolean {\r\n    // Check if any package is of type \"Flex\" or \"Premium\"\r\n    return packages.some((iterPackage: Package) => (iterPackage.type === PackageType.FLEX || iterPackage.type === PackageType.PREMIUM) &&\r\n      (iterPackage.state.toLowerCase() === PackageState.ONGOING.toLowerCase() || iterPackage.state.toLowerCase() === PackageState.PURCHASED.toLowerCase()));\r\n  }\r\n\r\n}\r\n"], "mappings": "AAAA,SAAqBA,WAAW,QAAQ,sBAAsB;AAE9D,SAASC,QAAQ,QAAwB,MAAM;AAC/C,SAASC,GAAG,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AACpD,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,KAAK,QAAQ,2BAA2B;AACjD,SAA6FC,YAAY,EAAEC,WAAW,QAAoF,yBAAyB;AACnO,SAASC,QAAQ,QAAQ,sBAAsB;;;;AAI/C,MAAMC,WAAW,GAAGL,WAAW,CAACM,MAAM;AACtC,MAAMC,eAAe,GAAGP,WAAW,CAACM,MAAM,GAAG,OAAO;AAKpD,OAAM,MAAOE,cAAc;EA0BzBC,YACUC,IAAgB,EAChBC,WAAwB;IADxB,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IA1Bd,KAAAC,mBAAmB,GAAwB;MAChDC,UAAU,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAClCC,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;MAC7GC,KAAK,EAAE,CAAC,KAAK,EAAEd,KAAK,CAACe,EAAE,EAAEf,KAAK,CAACgB,EAAE,EAAEhB,KAAK,CAACiB,EAAE,EAAEjB,KAAK,CAACkB,EAAE,EAAElB,KAAK,CAACmB,EAAE,EAAEnB,KAAK,CAACoB,EAAE,EAAEpB,KAAK,CAACqB,GAAG,EAAErB,KAAK,CAACsB,EAAE,CAAC;MAC/FC,IAAI,EAAE,CAAC,KAAK,EAAErB,WAAW,CAACsB,IAAI,EAAEtB,WAAW,CAACuB,OAAO,EAAEvB,WAAW,CAACwB,IAAI,EAAExB,WAAW,CAACyB,QAAQ,EAAEzB,WAAW,CAAC0B,IAAI,EAAE1B,WAAW,CAAC2B,KAAK,CAAC;MACjIC,KAAK,EAAE,CAAC,KAAK,EAAE7B,YAAY,CAAC8B,MAAM,EAAE9B,YAAY,CAAC+B,SAAS,EAAE/B,YAAY,CAACgC,MAAM,EAAEhC,YAAY,CAACiC,OAAO,EAAEjC,YAAY,CAACkC,QAAQ,EAAElC,YAAY,CAACmC,UAAU,CAAC;MACtJC,YAAY,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI;KAClC;IAEM,KAAAC,aAAa,GAAkB;MACpC1B,UAAU,EAAE,KAAK;MACjBC,SAAS,EAAE,KAAK;MAChBC,KAAK,EAAE,KAAK;MACZS,IAAI,EAAE,KAAK;MACXO,KAAK,EAAE,KAAK;MACZO,YAAY,EAAE,KAAK;MACnBE,cAAc,EAAE,EAAU;MAC1BC,YAAY,EAAE,EAAU;MACxBC,YAAY,EAAE,EAAU;MACxBC,cAAc,EAAE,EAAU;MAC1BC,gBAAgB,EAAE,EAAU;MAC5BC,cAAc,EAAE;KACjB;EAKG;EAEGC,gBAAgBA,CAAA;IACrB,OAAO,IAAI,CAAClC,mBAAmB;EACjC;EAEOmC,uBAAuBA,CAAA;IAC5B,OAAO,IAAI,CAACR,aAAa;EAC3B;EAEOS,uBAAuBA,CAACT,aAA4B;IACzD,IAAI,CAACA,aAAa,GAAGA,aAAa;EACpC;EAEOU,iCAAiCA,CAAA;IACtC,IAAIC,OAAO,GAAG,IAAI,CAACH,uBAAuB,EAAE;IAC5C,IAAII,mBAAmB,GAAG,IAAI,CAACvC,mBAAmB,CAACC,UAAU,CAACuC,MAAM,CAACC,EAAE,IAAG;MAAG,OAAOA,EAAE,KAAKH,OAAO,CAACrC,UAAU;IAAC,CAAC,CAAC;IAChH,IAAIC,SAAS,GAAG,IAAI,CAACF,mBAAmB,CAACE,SAAS,CAACsC,MAAM,CAACC,EAAE,IAAG;MAAG,OAAOA,EAAE,KAAKH,OAAO,CAACpC,SAAS;IAAC,CAAC,CAAC;IACpG,IAAIC,KAAK,GAAG,IAAI,CAACH,mBAAmB,CAACG,KAAK,CAACqC,MAAM,CAACC,EAAE,IAAG;MAAG,OAAOA,EAAE,KAAKH,OAAO,CAACnC,KAAK;IAAC,CAAC,CAAC;IACxF,IAAIS,IAAI,GAAG,IAAI,CAACZ,mBAAmB,CAACY,IAAI,CAAC4B,MAAM,CAACC,EAAE,IAAG;MAAG,OAAOA,EAAE,KAAKH,OAAO,CAAC1B,IAAI;IAAC,CAAC,CAAC;IACrF,IAAIO,KAAK,GAAG,IAAI,CAACnB,mBAAmB,CAACmB,KAAK,CAACqB,MAAM,CAACC,EAAE,IAAG;MAAG,OAAOA,EAAE,KAAKH,OAAO,CAACnB,KAAK;IAAC,CAAC,CAAC;IACxF,IAAIO,YAAY,GAAG,IAAI,CAAC1B,mBAAmB,CAAC0B,YAAY,CAACc,MAAM,CAACC,EAAE,IAAG;MAAG,OAAOA,EAAE,KAAKH,OAAO,CAACZ,YAAY;IAAC,CAAC,CAAC;IAC7G,OAAO;MACLzB,UAAU,EAAEsC,mBAAmB;MAC/BrC,SAAS,EAAEA,SAAS;MACpBC,KAAK,EAAEA,KAAK;MACZS,IAAI,EAAEA,IAAI;MACVO,KAAK,EAAEA,KAAK;MACZO,YAAY,EAAEA;KACf;EACH;EAEAgB,oBAAoBA,CAACC,WAAmB;IACtC,OAAO,IAAI,CAAC7C,IAAI,CAAC8C,GAAG,CAAoBnD,WAAW,GAAG,4CAA4C,GAAGkD,WAAW,CAAC,CAC9GE,IAAI,CACH3D,SAAS,CAAE4D,QAAmB,IAAI;MAChC,OAAO,IAAI,CAACC,sBAAsB,CAACD,QAAQ,CAAC;IAC9C,CAAC,CAAC,EACF3D,GAAG,CAAE6D,GAAG,IAAI,CACZ,CAAC,CAAC,CACH;EACL;EAEAC,eAAeA,CAACC,MAAc;IAC5B,IAAIC,QAAQ,GAAG,IAAI,CAACpD,WAAW,CAACqD,WAAW,EAAE,KAAK5D,QAAQ,CAAC6D,OAAO,GAAG,wCAAwC,GAAGH,MAAM,GAAG,wCAAwC,GAAGA,MAAM;IAC1K,OAAO,IAAI,CAACpD,IAAI,CAAC8C,GAAG,CAAoBnD,WAAW,GAAG0D,QAAQ,CAAC,CAC5DN,IAAI,CACH3D,SAAS,CAAE4D,QAAmB,IAAI;MAChC,OAAO,IAAI,CAACC,sBAAsB,CAACD,QAAQ,CAAC;IAC9C,CAAC,CAAC,CACH;EACL;EACA;EACAQ,kBAAkBA,CAACJ,MAAc;IAC/B,IAAIC,QAAQ,GAAG,IAAI,CAACpD,WAAW,CAACqD,WAAW,EAAE,KAAK5D,QAAQ,CAAC6D,OAAO,GAAG,+BAA+B,GAAGH,MAAM,GAAG,+BAA+B,GAAGA,MAAM;IACxJ,OAAO,IAAI,CAACpD,IAAI,CAAC8C,GAAG,CAAoBjD,eAAe,GAAGwD,QAAQ,CAAC;IACnE;IACA;IACA;IACA;IACA;EACF;EAEAI,oBAAoBA,CAACC,SAAiB;IACpC,OAAO,IAAI,CAAC1D,IAAI,CAAC8C,GAAG,CAAqBnD,WAAW,GAAG,2CAA2C+D,SAAS,EAAE,CAAC;EAChH;EAEAC,sBAAsBA,CAACD,SAAiB;IACtC,OAAO,IAAI,CAAC1D,IAAI,CAAC8C,GAAG,CAAwBnD,WAAW,GAAG,8CAA8C+D,SAAS,EAAE,CAAC;EACtH;EAEAE,yBAAyBA,CAACf,WAAmB;IAC3C,OAAO,IAAI,CAAC7C,IAAI,CAAC8C,GAAG,CAAUnD,WAAW,GAAG,iDAAiD,GAAGkD,WAAW,CAAC;EAC9G;EAEAgB,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC7D,IAAI,CAAC8C,GAAG,CAAiBnD,WAAW,GAAG,wBAAwB,CAAC;EAC9E;EACA;EACAmE,uBAAuBA,CAACC,GAAQ;IAC9B,OAAO,IAAI,CAAC/D,IAAI,CAACgE,IAAI,CAAoBnE,eAAe,GAAG,eAAe,EAAEkE,GAAG,CAAC;EAClF;EAEAE,qBAAqBA,CAACF,GAAQ;IAC5B,OAAO,IAAI,CAAC/D,IAAI,CAACgE,IAAI,CAA+BnE,eAAe,GAAG,oBAAoB,EAAEkE,GAAG,CAAC;EAClG;EAEAG,mBAAmBA,CAACC,IAAa;IAC/B,OAAO;MACLC,SAAS,EAAE,IAAIC,IAAI,CAACF,IAAI,CAACC,SAAS,CAAC;MACnCE,WAAW,EAAE,IAAID,IAAI,CAACF,IAAI,CAACG,WAAW,CAAC;MACvCC,SAAS,EAAE,IAAIF,IAAI,CAACF,IAAI,CAACI,SAAS;KACnC;EACH;EAEAC,qBAAqBA,CAACxB,QAAmB,EAAEyB,eAAuB;IAChE,OAAOzB,QAAQ,CAAC0B,IAAI,CAAEC,MAAe,IAAKA,MAAM,CAACC,EAAE,KAAKH,eAAe,CAAC;EAC1E;EAEOI,gBAAgBA,CAACnB,SAAiB;IACvC,OAAOxE,QAAQ,CACb,IAAI,CAACuE,oBAAoB,CAACC,SAAS,CAAC,EACpC,IAAI,CAACC,sBAAsB,CAACD,SAAS,CAAC,CACvC;EACH;EAEAT,sBAAsBA,CAACD,QAAmB;IACxC,IAAIA,QAAQ,CAAC8B,MAAM,IAAI,CAAC,EAAE;MACxB,OAAO,EAAE;IACX;IACA,OAAO5F,QAAQ,CAAC8D,QAAQ,CAAC7D,GAAG,CAAEgF,IAAa,IAAI;MAE7C,IAAIY,cAAc,GAAG,IAAI,CAACb,mBAAmB,CAACC,IAAI,CAAC;MACnDA,IAAI,CAACC,SAAS,GAAGW,cAAc,CAACX,SAAS;MACzCD,IAAI,CAACG,WAAW,GAAGS,cAAc,CAACT,WAAW;MAC7CH,IAAI,CAACI,SAAS,GAAGQ,cAAc,CAACR,SAAS;MACzC,OAAO,IAAI,CAACM,gBAAgB,CAACV,IAAI,CAACS,EAAE,CAAC,CAAC7B,IAAI,CACxC5D,GAAG,CAAC6F,CAAC,IAAG;QACN,KAAK,IAAIC,SAAS,IAAID,CAAC,CAAC,CAAC,CAAC,EAAE;UAC1BC,SAAS,CAACC,QAAQ,GAAG,IAAIb,IAAI,CAACY,SAAS,CAACC,QAAQ,CAAC;UACjDD,SAAS,CAACE,MAAM,GAAG,IAAId,IAAI,CAACY,SAAS,CAACE,MAAM,CAAC;QAC/C;QACA,KAAK,IAAIC,WAAW,IAAIJ,CAAC,CAAC,CAAC,CAAC,EAAE;UAC5BI,WAAW,CAACC,cAAc,GAAG,IAAIhB,IAAI,CAACe,WAAW,CAACC,cAAc,CAAC;UACjED,WAAW,CAACE,YAAY,GAAG,IAAIjB,IAAI,CAACe,WAAW,CAACE,YAAY,CAAC;QAC/D;QACA,IAAIC,iBAAiB,GAAa;UAAE,GAAGpB,IAAI;UAAE,GAAG;YAAEqB,UAAU,EAAER,CAAC,CAAC,CAAC,CAAC;YAAES,aAAa,EAAET,CAAC,CAAC,CAAC;UAAC;QAAE,CAAG;QAC5F,OAAOO,iBAAiB;MAC1B,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC;EACL;EAEAG,YAAYA,CAAC3B,GAAQ;IACnB,OAAO,IAAI,CAAC/D,IAAI,CAACgE,IAAI,CAACrE,WAAW,GAAG,uBAAuB,EAAEoE,GAAG,CAAC;EACnE;EAEA4B,iBAAiBA,CAAC5B,GAAQ;IACxB,OAAO,IAAI,CAAC/D,IAAI,CAACgE,IAAI,CAACrE,WAAW,GAAG,qBAAqB,EAAEoE,GAAG,CAAC;EACjE;EAEA6B,cAAcA,CAAClC,SAAc,EAAEmC,QAAgB;IAC7C,OAAO,IAAI,CAAC7F,IAAI,CAACgE,IAAI,CAACrE,WAAW,GAAG,oCAAoC,GAAG+D,SAAS,GAAG,YAAY,GAAGmC,QAAQ,EAAE,EAAE,CAAC;EACrH;EAEAC,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAAC9F,IAAI,CAAC8C,GAAG,CAAoBnD,WAAW,GAAG,8BAA8B,CAAC;EACvF;EAEAoG,YAAYA,CAAChC,GAAQ;IACnB,OAAO,IAAI,CAAC/D,IAAI,CAACgE,IAAI,CAACrE,WAAW,GAAG,uBAAuB,EAAEoE,GAAG,CAAC;EACnE;EAEAiC,kBAAkBA,CAACjC,GAAQ;IACzB,OAAO,IAAI,CAAC/D,IAAI,CAACgE,IAAI,CAACnE,eAAe,GAAG,oBAAoB,EAAEkE,GAAG,CAAC;EACpE;EAEAkC,uBAAuBA,CAAClC,GAAQ;IAC9B,OAAO,IAAI,CAAC/D,IAAI,CAACgE,IAAI,CAACnE,eAAe,GAAG,yBAAyB,EAAEkE,GAAG,CAAC;EACzE;EAEAmC,aAAaA,CAACnC,GAAQ;IACpB,OAAO,IAAI,CAAC/D,IAAI,CAACgE,IAAI,CAACnE,eAAe,GAAG,eAAe,EAAEkE,GAAG,CAAC;EAC/D;EAEAoC,kBAAkBA,CAACpC,GAAQ;IACzB,OAAO,IAAI,CAAC/D,IAAI,CAACgE,IAAI,CAACnE,eAAe,GAAG,oBAAoB,EAAEkE,GAAG,CAAC;EACpE;EAEAqC,gBAAgBA,CAACC,YAAiB;IAChC,MAAMC,GAAG,GAAG,uCAAuC;IACnD,MAAMC,OAAO,GAAG;MAAE,cAAc,EAAE,kBAAkB;MAAE,4BAA4B,EAAE;IAAM,CAAE;IAE5F,OAAO,IAAI,CAACvG,IAAI,CAACgE,IAAI,CAACsC,GAAG,EAAED,YAAY,EAAE;MAAEE;IAAO,CAAE,CAAC;EACvD;EAEAC,yBAAyBA,CAACC,IAAU;IAClC,MAAMH,GAAG,GAAG,wCAAwC;IACpD,MAAMC,OAAO,GAAG;MACd,4BAA4B,EAAE,MAAM;MACpC,cAAc,EAAE,kBAAkB;MAClCG,GAAG,EAAE,0CAA0C;MAC/CC,cAAc,EAAE;KACjB;IAED,OAAO,IAAI,CAAC3G,IAAI,CAAC8C,GAAG,CAACwD,GAAG,EAAE;MAAEC;IAAO,CAAE,CAAC;EACxC;EAEAK,mCAAmCA,CAACC,aAAqB,EAAEC,UAAkB;IAC3E,MAAMR,GAAG,GAAG,0DAA0D;IAEtE;IACA,MAAMC,OAAO,GAAG,IAAItH,WAAW,CAAC;MAC9B,4BAA4B,EAAE,MAAM;MACpC,cAAc,EAAE,kBAAkB;MAClC,KAAK,EAAE6H;KACR,CAAC;IAEF;IACA,MAAMC,MAAM,GAAG;MACb,gBAAgB,EAAEF;KACnB;IAEDG,OAAO,CAACC,GAAG,CAACX,GAAG,EAAEC,OAAO,EAAEQ,MAAM,CAAC;IAEjC;IACA,OAAO,IAAI,CAAC/G,IAAI,CAAC8C,GAAG,CAACwD,GAAG,EAAE;MAAEC,OAAO;MAAEQ;IAAM,CAAE,CAAC;EAChD;EAEA;;;;;;EAMAG,yBAAyBA,CAAClE,QAAmB;IAC3C,IAAImE,YAAY,GAAG,KAAK;IAExB,KAAK,MAAMhD,IAAI,IAAInB,QAAQ,EAAE;MAC3B,IAAI,CAACmB,IAAI,CAAC9C,KAAK,CAAC+F,WAAW,EAAE,KAAK5H,YAAY,CAAC6H,OAAO,CAACD,WAAW,EAAE,IAAIjD,IAAI,CAAC9C,KAAK,CAAC+F,WAAW,EAAE,KAAK5H,YAAY,CAAC8H,SAAS,CAACF,WAAW,EAAE,MACnIjD,IAAI,CAACrD,IAAI,KAAKrB,WAAW,CAAC8H,OAAO,IAAIpD,IAAI,CAACrD,IAAI,KAAKrB,WAAW,CAACwB,IAAI,CAAC,EAAE;QAC1EkG,YAAY,GAAG,IAAI;QAAC;MACtB;IACF;IAEA,OAAOA,YAAY;EACrB;EAGAK,4BAA4BA,CAACxE,QAAmB;IAC9C;IACA,OAAOA,QAAQ,CAACyE,IAAI,CAAEC,WAAoB,IAAK,CAACA,WAAW,CAAC5G,IAAI,KAAKrB,WAAW,CAACwB,IAAI,IAAIyG,WAAW,CAAC5G,IAAI,KAAKrB,WAAW,CAAC8H,OAAO,MAC9HG,WAAW,CAACrG,KAAK,CAAC+F,WAAW,EAAE,KAAK5H,YAAY,CAAC6H,OAAO,CAACD,WAAW,EAAE,IAAIM,WAAW,CAACrG,KAAK,CAAC+F,WAAW,EAAE,KAAK5H,YAAY,CAAC8H,SAAS,CAACF,WAAW,EAAE,CAAC,CAAC;EACzJ;EAAC,QAAAO,CAAA,G;qBArQU7H,cAAc,EAAA8H,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAdpI,cAAc;IAAAqI,OAAA,EAAdrI,cAAc,CAAAsI,IAAA;IAAAC,UAAA,EAFb;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}