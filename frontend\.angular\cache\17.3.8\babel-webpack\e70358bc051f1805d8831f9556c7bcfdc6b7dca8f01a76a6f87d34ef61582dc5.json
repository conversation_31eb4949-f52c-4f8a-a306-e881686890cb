{"ast": null, "code": "export let languages = [{\n  \"code\": \"ab\",\n  \"name\": \"Abkhaz\",\n  \"nativeName\": \"аҧсуа\"\n}, {\n  \"code\": \"aa\",\n  \"name\": \"Afar\",\n  \"nativeName\": \"Afaraf\"\n}, {\n  \"code\": \"af\",\n  \"name\": \"Afrikaans\",\n  \"nativeName\": \"Afrikaans\"\n}, {\n  \"code\": \"ak\",\n  \"name\": \"Akan\",\n  \"nativeName\": \"Akan\"\n}, {\n  \"code\": \"sq\",\n  \"name\": \"Albanian\",\n  \"nativeName\": \"Shqip\"\n}, {\n  \"code\": \"am\",\n  \"name\": \"Amharic\",\n  \"nativeName\": \"አማርኛ\"\n}, {\n  \"code\": \"ar\",\n  \"name\": \"Arabic\",\n  \"nativeName\": \"العربية\"\n}, {\n  \"code\": \"an\",\n  \"name\": \"Aragonese\",\n  \"nativeName\": \"Aragonés\"\n}, {\n  \"code\": \"hy\",\n  \"name\": \"Armenian\",\n  \"nativeName\": \"Հայերեն\"\n}, {\n  \"code\": \"as\",\n  \"name\": \"Assamese\",\n  \"nativeName\": \"অসমীয়া\"\n}, {\n  \"code\": \"av\",\n  \"name\": \"Avaric\",\n  \"nativeName\": \"авар мацӀ, магӀарул мацӀ\"\n}, {\n  \"code\": \"ae\",\n  \"name\": \"Avestan\",\n  \"nativeName\": \"avesta\"\n}, {\n  \"code\": \"ay\",\n  \"name\": \"Aymara\",\n  \"nativeName\": \"aymar aru\"\n}, {\n  \"code\": \"az\",\n  \"name\": \"Azerbaijani\",\n  \"nativeName\": \"azərbaycan dili\"\n}, {\n  \"code\": \"bm\",\n  \"name\": \"Bambara\",\n  \"nativeName\": \"bamanankan\"\n}, {\n  \"code\": \"ba\",\n  \"name\": \"Bashkir\",\n  \"nativeName\": \"башҡорт теле\"\n}, {\n  \"code\": \"eu\",\n  \"name\": \"Basque\",\n  \"nativeName\": \"euskara, euskera\"\n}, {\n  \"code\": \"be\",\n  \"name\": \"Belarusian\",\n  \"nativeName\": \"Беларуская\"\n}, {\n  \"code\": \"bn\",\n  \"name\": \"Bengali\",\n  \"nativeName\": \"বাংলা\"\n}, {\n  \"code\": \"bh\",\n  \"name\": \"Bihari\",\n  \"nativeName\": \"भोजपुरी\"\n}, {\n  \"code\": \"bi\",\n  \"name\": \"Bislama\",\n  \"nativeName\": \"Bislama\"\n}, {\n  \"code\": \"bs\",\n  \"name\": \"Bosnian\",\n  \"nativeName\": \"bosanski jezik\"\n}, {\n  \"code\": \"br\",\n  \"name\": \"Breton\",\n  \"nativeName\": \"brezhoneg\"\n}, {\n  \"code\": \"bg\",\n  \"name\": \"Bulgarian\",\n  \"nativeName\": \"български език\"\n}, {\n  \"code\": \"my\",\n  \"name\": \"Burmese\",\n  \"nativeName\": \"ဗမာစာ\"\n}, {\n  \"code\": \"ca\",\n  \"name\": \"Catalan; Valencian\",\n  \"nativeName\": \"Català\"\n}, {\n  \"code\": \"ch\",\n  \"name\": \"Chamorro\",\n  \"nativeName\": \"Chamoru\"\n}, {\n  \"code\": \"ce\",\n  \"name\": \"Chechen\",\n  \"nativeName\": \"нохчийн мотт\"\n}, {\n  \"code\": \"ny\",\n  \"name\": \"Chichewa; Chewa; Nyanja\",\n  \"nativeName\": \"chiCheŵa, chinyanja\"\n}, {\n  \"code\": \"zh\",\n  \"name\": \"Chinese\",\n  \"nativeName\": \"中文 (Zhōngwén), 汉语, 漢語\"\n}, {\n  \"code\": \"cv\",\n  \"name\": \"Chuvash\",\n  \"nativeName\": \"чӑваш чӗлхи\"\n}, {\n  \"code\": \"kw\",\n  \"name\": \"Cornish\",\n  \"nativeName\": \"Kernewek\"\n}, {\n  \"code\": \"co\",\n  \"name\": \"Corsican\",\n  \"nativeName\": \"corsu, lingua corsa\"\n}, {\n  \"code\": \"cr\",\n  \"name\": \"Cree\",\n  \"nativeName\": \"ᓀᐦᐃᔭᐍᐏᐣ\"\n}, {\n  \"code\": \"hr\",\n  \"name\": \"Croatian\",\n  \"nativeName\": \"hrvatski\"\n}, {\n  \"code\": \"cs\",\n  \"name\": \"Czech\",\n  \"nativeName\": \"česky, čeština\"\n}, {\n  \"code\": \"da\",\n  \"name\": \"Danish\",\n  \"nativeName\": \"dansk\"\n}, {\n  \"code\": \"dv\",\n  \"name\": \"Divehi; Dhivehi; Maldivian;\",\n  \"nativeName\": \"ދިވެހި\"\n}, {\n  \"code\": \"nl\",\n  \"name\": \"Dutch\",\n  \"nativeName\": \"Nederlands, Vlaams\"\n}, {\n  \"code\": \"en\",\n  \"name\": \"English\",\n  \"nativeName\": \"English\"\n}, {\n  \"code\": \"eo\",\n  \"name\": \"Esperanto\",\n  \"nativeName\": \"Esperanto\"\n}, {\n  \"code\": \"et\",\n  \"name\": \"Estonian\",\n  \"nativeName\": \"eesti, eesti keel\"\n}, {\n  \"code\": \"ee\",\n  \"name\": \"Ewe\",\n  \"nativeName\": \"Eʋegbe\"\n}, {\n  \"code\": \"fo\",\n  \"name\": \"Faroese\",\n  \"nativeName\": \"føroyskt\"\n}, {\n  \"code\": \"fj\",\n  \"name\": \"Fijian\",\n  \"nativeName\": \"vosa Vakaviti\"\n}, {\n  \"code\": \"fi\",\n  \"name\": \"Finnish\",\n  \"nativeName\": \"suomi, suomen kieli\"\n}, {\n  \"code\": \"fr\",\n  \"name\": \"French\",\n  \"nativeName\": \"français, langue française\"\n}, {\n  \"code\": \"ff\",\n  \"name\": \"Fula; Fulah; Pulaar; Pular\",\n  \"nativeName\": \"Fulfulde, Pulaar, Pular\"\n}, {\n  \"code\": \"gl\",\n  \"name\": \"Galician\",\n  \"nativeName\": \"Galego\"\n}, {\n  \"code\": \"ka\",\n  \"name\": \"Georgian\",\n  \"nativeName\": \"ქართული\"\n}, {\n  \"code\": \"de\",\n  \"name\": \"German\",\n  \"nativeName\": \"Deutsch\"\n}, {\n  \"code\": \"el\",\n  \"name\": \"Greek, Modern\",\n  \"nativeName\": \"Ελληνικά\"\n}, {\n  \"code\": \"gn\",\n  \"name\": \"Guaraní\",\n  \"nativeName\": \"Avañeẽ\"\n}, {\n  \"code\": \"gu\",\n  \"name\": \"Gujarati\",\n  \"nativeName\": \"ગુજરાતી\"\n}, {\n  \"code\": \"ht\",\n  \"name\": \"Haitian; Haitian Creole\",\n  \"nativeName\": \"Kreyòl ayisyen\"\n}, {\n  \"code\": \"ha\",\n  \"name\": \"Hausa\",\n  \"nativeName\": \"Hausa, هَوُسَ\"\n}, {\n  \"code\": \"he\",\n  \"name\": \"Hebrew (modern)\",\n  \"nativeName\": \"עברית\"\n}, {\n  \"code\": \"hz\",\n  \"name\": \"Herero\",\n  \"nativeName\": \"Otjiherero\"\n}, {\n  \"code\": \"hi\",\n  \"name\": \"Hindi\",\n  \"nativeName\": \"हिन्दी, हिंदी\"\n}, {\n  \"code\": \"ho\",\n  \"name\": \"Hiri Motu\",\n  \"nativeName\": \"Hiri Motu\"\n}, {\n  \"code\": \"hu\",\n  \"name\": \"Hungarian\",\n  \"nativeName\": \"Magyar\"\n}, {\n  \"code\": \"ia\",\n  \"name\": \"Interlingua\",\n  \"nativeName\": \"Interlingua\"\n}, {\n  \"code\": \"id\",\n  \"name\": \"Indonesian\",\n  \"nativeName\": \"Bahasa Indonesia\"\n}, {\n  \"code\": \"ie\",\n  \"name\": \"Interlingue\",\n  \"nativeName\": \"Originally called Occidental; then Interlingue after WWII\"\n}, {\n  \"code\": \"ga\",\n  \"name\": \"Irish\",\n  \"nativeName\": \"Gaeilge\"\n}, {\n  \"code\": \"ig\",\n  \"name\": \"Igbo\",\n  \"nativeName\": \"Asụsụ Igbo\"\n}, {\n  \"code\": \"ik\",\n  \"name\": \"Inupiaq\",\n  \"nativeName\": \"Iñupiaq, Iñupiatun\"\n}, {\n  \"code\": \"io\",\n  \"name\": \"Ido\",\n  \"nativeName\": \"Ido\"\n}, {\n  \"code\": \"is\",\n  \"name\": \"Icelandic\",\n  \"nativeName\": \"Íslenska\"\n}, {\n  \"code\": \"it\",\n  \"name\": \"Italian\",\n  \"nativeName\": \"Italiano\"\n}, {\n  \"code\": \"iu\",\n  \"name\": \"Inuktitut\",\n  \"nativeName\": \"ᐃᓄᒃᑎᑐᑦ\"\n}, {\n  \"code\": \"ja\",\n  \"name\": \"Japanese\",\n  \"nativeName\": \"日本語 (にほんご／にっぽんご)\"\n}, {\n  \"code\": \"jv\",\n  \"name\": \"Javanese\",\n  \"nativeName\": \"basa Jawa\"\n}, {\n  \"code\": \"kl\",\n  \"name\": \"Kalaallisut, Greenlandic\",\n  \"nativeName\": \"kalaallisut, kalaallit oqaasii\"\n}, {\n  \"code\": \"kn\",\n  \"name\": \"Kannada\",\n  \"nativeName\": \"ಕನ್ನಡ\"\n}, {\n  \"code\": \"kr\",\n  \"name\": \"Kanuri\",\n  \"nativeName\": \"Kanuri\"\n}, {\n  \"code\": \"ks\",\n  \"name\": \"Kashmiri\",\n  \"nativeName\": \"कश्मीरी, كشميري‎\"\n}, {\n  \"code\": \"kk\",\n  \"name\": \"Kazakh\",\n  \"nativeName\": \"Қазақ тілі\"\n}, {\n  \"code\": \"km\",\n  \"name\": \"Khmer\",\n  \"nativeName\": \"ភាសាខ្មែរ\"\n}, {\n  \"code\": \"ki\",\n  \"name\": \"Kikuyu, Gikuyu\",\n  \"nativeName\": \"Gĩkũyũ\"\n}, {\n  \"code\": \"rw\",\n  \"name\": \"Kinyarwanda\",\n  \"nativeName\": \"Ikinyarwanda\"\n}, {\n  \"code\": \"ky\",\n  \"name\": \"Kirghiz, Kyrgyz\",\n  \"nativeName\": \"кыргыз тили\"\n}, {\n  \"code\": \"kv\",\n  \"name\": \"Komi\",\n  \"nativeName\": \"коми кыв\"\n}, {\n  \"code\": \"kg\",\n  \"name\": \"Kongo\",\n  \"nativeName\": \"KiKongo\"\n}, {\n  \"code\": \"ko\",\n  \"name\": \"Korean\",\n  \"nativeName\": \"한국어 (韓國語), 조선말 (朝鮮語)\"\n}, {\n  \"code\": \"ku\",\n  \"name\": \"Kurdish\",\n  \"nativeName\": \"Kurdî, كوردی‎\"\n}, {\n  \"code\": \"kj\",\n  \"name\": \"Kwanyama, Kuanyama\",\n  \"nativeName\": \"Kuanyama\"\n}, {\n  \"code\": \"la\",\n  \"name\": \"Latin\",\n  \"nativeName\": \"latine, lingua latina\"\n}, {\n  \"code\": \"lb\",\n  \"name\": \"Luxembourgish, Letzeburgesch\",\n  \"nativeName\": \"Lëtzebuergesch\"\n}, {\n  \"code\": \"lg\",\n  \"name\": \"Luganda\",\n  \"nativeName\": \"Luganda\"\n}, {\n  \"code\": \"li\",\n  \"name\": \"Limburgish, Limburgan, Limburger\",\n  \"nativeName\": \"Limburgs\"\n}, {\n  \"code\": \"ln\",\n  \"name\": \"Lingala\",\n  \"nativeName\": \"Lingála\"\n}, {\n  \"code\": \"lo\",\n  \"name\": \"Lao\",\n  \"nativeName\": \"ພາສາລາວ\"\n}, {\n  \"code\": \"lt\",\n  \"name\": \"Lithuanian\",\n  \"nativeName\": \"lietuvių kalba\"\n}, {\n  \"code\": \"lu\",\n  \"name\": \"Luba-Katanga\",\n  \"nativeName\": \"\"\n}, {\n  \"code\": \"lv\",\n  \"name\": \"Latvian\",\n  \"nativeName\": \"latviešu valoda\"\n}, {\n  \"code\": \"gv\",\n  \"name\": \"Manx\",\n  \"nativeName\": \"Gaelg, Gailck\"\n}, {\n  \"code\": \"mk\",\n  \"name\": \"Macedonian\",\n  \"nativeName\": \"македонски јазик\"\n}, {\n  \"code\": \"mg\",\n  \"name\": \"Malagasy\",\n  \"nativeName\": \"Malagasy fiteny\"\n}, {\n  \"code\": \"ms\",\n  \"name\": \"Malay\",\n  \"nativeName\": \"bahasa Melayu, بهاس ملايو‎\"\n}, {\n  \"code\": \"ml\",\n  \"name\": \"Malayalam\",\n  \"nativeName\": \"മലയാളം\"\n}, {\n  \"code\": \"mt\",\n  \"name\": \"Maltese\",\n  \"nativeName\": \"Malti\"\n}, {\n  \"code\": \"mi\",\n  \"name\": \"Māori\",\n  \"nativeName\": \"te reo Māori\"\n}, {\n  \"code\": \"mr\",\n  \"name\": \"Marathi (Marāṭhī)\",\n  \"nativeName\": \"मराठी\"\n}, {\n  \"code\": \"mh\",\n  \"name\": \"Marshallese\",\n  \"nativeName\": \"Kajin M̧ajeļ\"\n}, {\n  \"code\": \"mn\",\n  \"name\": \"Mongolian\",\n  \"nativeName\": \"монгол\"\n}, {\n  \"code\": \"na\",\n  \"name\": \"Nauru\",\n  \"nativeName\": \"Ekakairũ Naoero\"\n}, {\n  \"code\": \"nv\",\n  \"name\": \"Navajo, Navaho\",\n  \"nativeName\": \"Diné bizaad, Dinékʼehǰí\"\n}, {\n  \"code\": \"nb\",\n  \"name\": \"Norwegian Bokmål\",\n  \"nativeName\": \"Norsk bokmål\"\n}, {\n  \"code\": \"nd\",\n  \"name\": \"North Ndebele\",\n  \"nativeName\": \"isiNdebele\"\n}, {\n  \"code\": \"ne\",\n  \"name\": \"Nepali\",\n  \"nativeName\": \"नेपाली\"\n}, {\n  \"code\": \"ng\",\n  \"name\": \"Ndonga\",\n  \"nativeName\": \"Owambo\"\n}, {\n  \"code\": \"nn\",\n  \"name\": \"Norwegian Nynorsk\",\n  \"nativeName\": \"Norsk nynorsk\"\n}, {\n  \"code\": \"no\",\n  \"name\": \"Norwegian\",\n  \"nativeName\": \"Norsk\"\n}, {\n  \"code\": \"ii\",\n  \"name\": \"Nuosu\",\n  \"nativeName\": \"ꆈꌠ꒿ Nuosuhxop\"\n}, {\n  \"code\": \"nr\",\n  \"name\": \"South Ndebele\",\n  \"nativeName\": \"isiNdebele\"\n}, {\n  \"code\": \"oc\",\n  \"name\": \"Occitan\",\n  \"nativeName\": \"Occitan\"\n}, {\n  \"code\": \"oj\",\n  \"name\": \"Ojibwe, Ojibwa\",\n  \"nativeName\": \"ᐊᓂᔑᓈᐯᒧᐎᓐ\"\n},\n// { \"code\": \"cu\", \"name\": \"Old Church Slavonic, Church Slavic, Church Slavonic, Old Bulgarian, Old Slavonic\", \"nativeName\": \"ѩзыкъ словѣньскъ\" },\n{\n  \"code\": \"om\",\n  \"name\": \"Oromo\",\n  \"nativeName\": \"Afaan Oromoo\"\n}, {\n  \"code\": \"or\",\n  \"name\": \"Oriya\",\n  \"nativeName\": \"ଓଡ଼ିଆ\"\n}, {\n  \"code\": \"os\",\n  \"name\": \"Ossetian, Ossetic\",\n  \"nativeName\": \"ирон æвзаг\"\n}, {\n  \"code\": \"pa\",\n  \"name\": \"Panjabi, Punjabi\",\n  \"nativeName\": \"ਪੰਜਾਬੀ, پنجابی‎\"\n}, {\n  \"code\": \"pi\",\n  \"name\": \"Pāli\",\n  \"nativeName\": \"पाऴि\"\n}, {\n  \"code\": \"fa\",\n  \"name\": \"Persian\",\n  \"nativeName\": \"فارسی\"\n}, {\n  \"code\": \"pl\",\n  \"name\": \"Polish\",\n  \"nativeName\": \"polski\"\n}, {\n  \"code\": \"ps\",\n  \"name\": \"Pashto, Pushto\",\n  \"nativeName\": \"پښتو\"\n}, {\n  \"code\": \"pt\",\n  \"name\": \"Portuguese\",\n  \"nativeName\": \"Português\"\n}, {\n  \"code\": \"qu\",\n  \"name\": \"Quechua\",\n  \"nativeName\": \"Runa Simi, Kichwa\"\n}, {\n  \"code\": \"rm\",\n  \"name\": \"Romansh\",\n  \"nativeName\": \"rumantsch grischun\"\n}, {\n  \"code\": \"rn\",\n  \"name\": \"Kirundi\",\n  \"nativeName\": \"kiRundi\"\n}, {\n  \"code\": \"ro\",\n  \"name\": \"Romanian, Moldavian, Moldovan\",\n  \"nativeName\": \"română\"\n}, {\n  \"code\": \"ru\",\n  \"name\": \"Russian\",\n  \"nativeName\": \"русский язык\"\n}, {\n  \"code\": \"sa\",\n  \"name\": \"Sanskrit (Saṁskṛta)\",\n  \"nativeName\": \"संस्कृतम्\"\n}, {\n  \"code\": \"sc\",\n  \"name\": \"Sardinian\",\n  \"nativeName\": \"sardu\"\n}, {\n  \"code\": \"sd\",\n  \"name\": \"Sindhi\",\n  \"nativeName\": \"सिन्धी, سنڌي، سندھی‎\"\n}, {\n  \"code\": \"se\",\n  \"name\": \"Northern Sami\",\n  \"nativeName\": \"Davvisámegiella\"\n}, {\n  \"code\": \"sm\",\n  \"name\": \"Samoan\",\n  \"nativeName\": \"gagana faa Samoa\"\n}, {\n  \"code\": \"sg\",\n  \"name\": \"Sango\",\n  \"nativeName\": \"yângâ tî sängö\"\n}, {\n  \"code\": \"sr\",\n  \"name\": \"Serbian\",\n  \"nativeName\": \"српски језик\"\n}, {\n  \"code\": \"gd\",\n  \"name\": \"Scottish Gaelic; Gaelic\",\n  \"nativeName\": \"Gàidhlig\"\n}, {\n  \"code\": \"sn\",\n  \"name\": \"Shona\",\n  \"nativeName\": \"chiShona\"\n}, {\n  \"code\": \"si\",\n  \"name\": \"Sinhala, Sinhalese\",\n  \"nativeName\": \"සිංහල\"\n}, {\n  \"code\": \"sk\",\n  \"name\": \"Slovak\",\n  \"nativeName\": \"slovenčina\"\n}, {\n  \"code\": \"sl\",\n  \"name\": \"Slovene\",\n  \"nativeName\": \"slovenščina\"\n}, {\n  \"code\": \"so\",\n  \"name\": \"Somali\",\n  \"nativeName\": \"Soomaaliga, af Soomaali\"\n}, {\n  \"code\": \"st\",\n  \"name\": \"Southern Sotho\",\n  \"nativeName\": \"Sesotho\"\n}, {\n  \"code\": \"es\",\n  \"name\": \"Spanish; Castilian\",\n  \"nativeName\": \"español, castellano\"\n}, {\n  \"code\": \"su\",\n  \"name\": \"Sundanese\",\n  \"nativeName\": \"Basa Sunda\"\n}, {\n  \"code\": \"sw\",\n  \"name\": \"Swahili\",\n  \"nativeName\": \"Kiswahili\"\n}, {\n  \"code\": \"ss\",\n  \"name\": \"Swati\",\n  \"nativeName\": \"SiSwati\"\n}, {\n  \"code\": \"sv\",\n  \"name\": \"Swedish\",\n  \"nativeName\": \"svenska\"\n}, {\n  \"code\": \"ta\",\n  \"name\": \"Tamil\",\n  \"nativeName\": \"தமிழ்\"\n}, {\n  \"code\": \"te\",\n  \"name\": \"Telugu\",\n  \"nativeName\": \"తెలుగు\"\n}, {\n  \"code\": \"tg\",\n  \"name\": \"Tajik\",\n  \"nativeName\": \"тоҷикӣ, toğikī, تاجیکی‎\"\n}, {\n  \"code\": \"th\",\n  \"name\": \"Thai\",\n  \"nativeName\": \"ไทย\"\n}, {\n  \"code\": \"ti\",\n  \"name\": \"Tigrinya\",\n  \"nativeName\": \"ትግርኛ\"\n}, {\n  \"code\": \"bo\",\n  \"name\": \"Tibetan Standard, Tibetan, Central\",\n  \"nativeName\": \"བོད་ཡིག\"\n}, {\n  \"code\": \"tk\",\n  \"name\": \"Turkmen\",\n  \"nativeName\": \"Türkmen, Түркмен\"\n}, {\n  \"code\": \"tl\",\n  \"name\": \"Tagalog\",\n  \"nativeName\": \"Wikang Tagalog, ᜏᜒᜃᜅ᜔ ᜆᜄᜎᜓᜄ᜔\"\n}, {\n  \"code\": \"tn\",\n  \"name\": \"Tswana\",\n  \"nativeName\": \"Setswana\"\n}, {\n  \"code\": \"to\",\n  \"name\": \"Tonga (Tonga Islands)\",\n  \"nativeName\": \"faka Tonga\"\n}, {\n  \"code\": \"tr\",\n  \"name\": \"Turkish\",\n  \"nativeName\": \"Türkçe\"\n}, {\n  \"code\": \"ts\",\n  \"name\": \"Tsonga\",\n  \"nativeName\": \"Xitsonga\"\n}, {\n  \"code\": \"tt\",\n  \"name\": \"Tatar\",\n  \"nativeName\": \"татарча, tatarça, تاتارچا‎\"\n}, {\n  \"code\": \"tw\",\n  \"name\": \"Twi\",\n  \"nativeName\": \"Twi\"\n}, {\n  \"code\": \"ty\",\n  \"name\": \"Tahitian\",\n  \"nativeName\": \"Reo Tahiti\"\n}, {\n  \"code\": \"ug\",\n  \"name\": \"Uighur, Uyghur\",\n  \"nativeName\": \"Uyƣurqə, ئۇيغۇرچە‎\"\n}, {\n  \"code\": \"uk\",\n  \"name\": \"Ukrainian\",\n  \"nativeName\": \"українська\"\n}, {\n  \"code\": \"ur\",\n  \"name\": \"Urdu\",\n  \"nativeName\": \"اردو\"\n}, {\n  \"code\": \"uz\",\n  \"name\": \"Uzbek\",\n  \"nativeName\": \"zbek, Ўзбек, أۇزبېك‎\"\n}, {\n  \"code\": \"ve\",\n  \"name\": \"Venda\",\n  \"nativeName\": \"Tshivenḓa\"\n}, {\n  \"code\": \"vi\",\n  \"name\": \"Vietnamese\",\n  \"nativeName\": \"Tiếng Việt\"\n}, {\n  \"code\": \"vo\",\n  \"name\": \"Volapük\",\n  \"nativeName\": \"Volapük\"\n}, {\n  \"code\": \"wa\",\n  \"name\": \"Walloon\",\n  \"nativeName\": \"Walon\"\n}, {\n  \"code\": \"cy\",\n  \"name\": \"Welsh\",\n  \"nativeName\": \"Cymraeg\"\n}, {\n  \"code\": \"wo\",\n  \"name\": \"Wolof\",\n  \"nativeName\": \"Wollof\"\n}, {\n  \"code\": \"fy\",\n  \"name\": \"Western Frisian\",\n  \"nativeName\": \"Frysk\"\n}, {\n  \"code\": \"xh\",\n  \"name\": \"Xhosa\",\n  \"nativeName\": \"isiXhosa\"\n}, {\n  \"code\": \"yi\",\n  \"name\": \"Yiddish\",\n  \"nativeName\": \"ייִדיש\"\n}, {\n  \"code\": \"yo\",\n  \"name\": \"Yoruba\",\n  \"nativeName\": \"Yorùbá\"\n}, {\n  \"code\": \"za\",\n  \"name\": \"Zhuang, Chuang\",\n  \"nativeName\": \"Saɯ cueŋƅ, Saw cuengh\"\n}];\nexport let countries = [{\n  \"name\": \"Ascension Island\",\n  \"code\": \"AC\",\n  \"emoji\": \"🇦🇨\",\n  \"unicode\": \"U+1F1E6 U+1F1E8\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/AC.svg\"\n}, {\n  \"name\": \"Andorra\",\n  \"code\": \"AD\",\n  \"emoji\": \"🇦🇩\",\n  \"unicode\": \"U+1F1E6 U+1F1E9\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/AD.svg\"\n}, {\n  \"name\": \"United Arab Emirates\",\n  \"code\": \"AE\",\n  \"emoji\": \"🇦🇪\",\n  \"unicode\": \"U+1F1E6 U+1F1EA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/AE.svg\"\n}, {\n  \"name\": \"Afghanistan\",\n  \"code\": \"AF\",\n  \"emoji\": \"🇦🇫\",\n  \"unicode\": \"U+1F1E6 U+1F1EB\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/AF.svg\"\n}, {\n  \"name\": \"Antigua & Barbuda\",\n  \"code\": \"AG\",\n  \"emoji\": \"🇦🇬\",\n  \"unicode\": \"U+1F1E6 U+1F1EC\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/AG.svg\"\n}, {\n  \"name\": \"Anguilla\",\n  \"code\": \"AI\",\n  \"emoji\": \"🇦🇮\",\n  \"unicode\": \"U+1F1E6 U+1F1EE\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/AI.svg\"\n}, {\n  \"name\": \"Albania\",\n  \"code\": \"AL\",\n  \"emoji\": \"🇦🇱\",\n  \"unicode\": \"U+1F1E6 U+1F1F1\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/AL.svg\"\n}, {\n  \"name\": \"Armenia\",\n  \"code\": \"AM\",\n  \"emoji\": \"🇦🇲\",\n  \"unicode\": \"U+1F1E6 U+1F1F2\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/AM.svg\"\n}, {\n  \"name\": \"Angola\",\n  \"code\": \"AO\",\n  \"emoji\": \"🇦🇴\",\n  \"unicode\": \"U+1F1E6 U+1F1F4\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/AO.svg\"\n}, {\n  \"name\": \"Antarctica\",\n  \"code\": \"AQ\",\n  \"emoji\": \"🇦🇶\",\n  \"unicode\": \"U+1F1E6 U+1F1F6\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/AQ.svg\"\n}, {\n  \"name\": \"Argentina\",\n  \"code\": \"AR\",\n  \"emoji\": \"🇦🇷\",\n  \"unicode\": \"U+1F1E6 U+1F1F7\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/AR.svg\"\n}, {\n  \"name\": \"American Samoa\",\n  \"code\": \"AS\",\n  \"emoji\": \"🇦🇸\",\n  \"unicode\": \"U+1F1E6 U+1F1F8\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/AS.svg\"\n}, {\n  \"name\": \"Austria\",\n  \"code\": \"AT\",\n  \"emoji\": \"🇦🇹\",\n  \"unicode\": \"U+1F1E6 U+1F1F9\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/AT.svg\"\n}, {\n  \"name\": \"Australia\",\n  \"code\": \"AU\",\n  \"emoji\": \"🇦🇺\",\n  \"unicode\": \"U+1F1E6 U+1F1FA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/AU.svg\"\n}, {\n  \"name\": \"Aruba\",\n  \"code\": \"AW\",\n  \"emoji\": \"🇦🇼\",\n  \"unicode\": \"U+1F1E6 U+1F1FC\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/AW.svg\"\n}, {\n  \"name\": \"Åland Islands\",\n  \"code\": \"AX\",\n  \"emoji\": \"🇦🇽\",\n  \"unicode\": \"U+1F1E6 U+1F1FD\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/AX.svg\"\n}, {\n  \"name\": \"Azerbaijan\",\n  \"code\": \"AZ\",\n  \"emoji\": \"🇦🇿\",\n  \"unicode\": \"U+1F1E6 U+1F1FF\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/AZ.svg\"\n}, {\n  \"name\": \"Bosnia & Herzegovina\",\n  \"code\": \"BA\",\n  \"emoji\": \"🇧🇦\",\n  \"unicode\": \"U+1F1E7 U+1F1E6\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/BA.svg\"\n}, {\n  \"name\": \"Barbados\",\n  \"code\": \"BB\",\n  \"emoji\": \"🇧🇧\",\n  \"unicode\": \"U+1F1E7 U+1F1E7\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/BB.svg\"\n}, {\n  \"name\": \"Bangladesh\",\n  \"code\": \"BD\",\n  \"emoji\": \"🇧🇩\",\n  \"unicode\": \"U+1F1E7 U+1F1E9\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/BD.svg\"\n}, {\n  \"name\": \"Belgium\",\n  \"code\": \"BE\",\n  \"emoji\": \"🇧🇪\",\n  \"unicode\": \"U+1F1E7 U+1F1EA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/BE.svg\"\n}, {\n  \"name\": \"Burkina Faso\",\n  \"code\": \"BF\",\n  \"emoji\": \"🇧🇫\",\n  \"unicode\": \"U+1F1E7 U+1F1EB\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/BF.svg\"\n}, {\n  \"name\": \"Bulgaria\",\n  \"code\": \"BG\",\n  \"emoji\": \"🇧🇬\",\n  \"unicode\": \"U+1F1E7 U+1F1EC\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/BG.svg\"\n}, {\n  \"name\": \"Bahrain\",\n  \"code\": \"BH\",\n  \"emoji\": \"🇧🇭\",\n  \"unicode\": \"U+1F1E7 U+1F1ED\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/BH.svg\"\n}, {\n  \"name\": \"Burundi\",\n  \"code\": \"BI\",\n  \"emoji\": \"🇧🇮\",\n  \"unicode\": \"U+1F1E7 U+1F1EE\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/BI.svg\"\n}, {\n  \"name\": \"Benin\",\n  \"code\": \"BJ\",\n  \"emoji\": \"🇧🇯\",\n  \"unicode\": \"U+1F1E7 U+1F1EF\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/BJ.svg\"\n}, {\n  \"name\": \"St. Barthélemy\",\n  \"code\": \"BL\",\n  \"emoji\": \"🇧🇱\",\n  \"unicode\": \"U+1F1E7 U+1F1F1\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/BL.svg\"\n}, {\n  \"name\": \"Bermuda\",\n  \"code\": \"BM\",\n  \"emoji\": \"🇧🇲\",\n  \"unicode\": \"U+1F1E7 U+1F1F2\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/BM.svg\"\n}, {\n  \"name\": \"Brunei\",\n  \"code\": \"BN\",\n  \"emoji\": \"🇧🇳\",\n  \"unicode\": \"U+1F1E7 U+1F1F3\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/BN.svg\"\n}, {\n  \"name\": \"Bolivia\",\n  \"code\": \"BO\",\n  \"emoji\": \"🇧🇴\",\n  \"unicode\": \"U+1F1E7 U+1F1F4\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/BO.svg\"\n}, {\n  \"name\": \"Caribbean Netherlands\",\n  \"code\": \"BQ\",\n  \"emoji\": \"🇧🇶\",\n  \"unicode\": \"U+1F1E7 U+1F1F6\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/BQ.svg\"\n}, {\n  \"name\": \"Brazil\",\n  \"code\": \"BR\",\n  \"emoji\": \"🇧🇷\",\n  \"unicode\": \"U+1F1E7 U+1F1F7\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/BR.svg\"\n}, {\n  \"name\": \"Bahamas\",\n  \"code\": \"BS\",\n  \"emoji\": \"🇧🇸\",\n  \"unicode\": \"U+1F1E7 U+1F1F8\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/BS.svg\"\n}, {\n  \"name\": \"Bhutan\",\n  \"code\": \"BT\",\n  \"emoji\": \"🇧🇹\",\n  \"unicode\": \"U+1F1E7 U+1F1F9\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/BT.svg\"\n}, {\n  \"name\": \"Bouvet Island\",\n  \"code\": \"BV\",\n  \"emoji\": \"🇧🇻\",\n  \"unicode\": \"U+1F1E7 U+1F1FB\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/BV.svg\"\n}, {\n  \"name\": \"Botswana\",\n  \"code\": \"BW\",\n  \"emoji\": \"🇧🇼\",\n  \"unicode\": \"U+1F1E7 U+1F1FC\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/BW.svg\"\n}, {\n  \"name\": \"Belarus\",\n  \"code\": \"BY\",\n  \"emoji\": \"🇧🇾\",\n  \"unicode\": \"U+1F1E7 U+1F1FE\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/BY.svg\"\n}, {\n  \"name\": \"Belize\",\n  \"code\": \"BZ\",\n  \"emoji\": \"🇧🇿\",\n  \"unicode\": \"U+1F1E7 U+1F1FF\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/BZ.svg\"\n}, {\n  \"name\": \"Canada\",\n  \"code\": \"CA\",\n  \"emoji\": \"🇨🇦\",\n  \"unicode\": \"U+1F1E8 U+1F1E6\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/CA.svg\"\n}, {\n  \"name\": \"Cocos (Keeling) Islands\",\n  \"code\": \"CC\",\n  \"emoji\": \"🇨🇨\",\n  \"unicode\": \"U+1F1E8 U+1F1E8\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/CC.svg\"\n}, {\n  \"name\": \"Congo - Kinshasa\",\n  \"code\": \"CD\",\n  \"emoji\": \"🇨🇩\",\n  \"unicode\": \"U+1F1E8 U+1F1E9\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/CD.svg\"\n}, {\n  \"name\": \"Central African Republic\",\n  \"code\": \"CF\",\n  \"emoji\": \"🇨🇫\",\n  \"unicode\": \"U+1F1E8 U+1F1EB\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/CF.svg\"\n}, {\n  \"name\": \"Congo - Brazzaville\",\n  \"code\": \"CG\",\n  \"emoji\": \"🇨🇬\",\n  \"unicode\": \"U+1F1E8 U+1F1EC\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/CG.svg\"\n}, {\n  \"name\": \"Switzerland\",\n  \"code\": \"CH\",\n  \"emoji\": \"🇨🇭\",\n  \"unicode\": \"U+1F1E8 U+1F1ED\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/CH.svg\"\n}, {\n  \"name\": \"Côte d’Ivoire\",\n  \"code\": \"CI\",\n  \"emoji\": \"🇨🇮\",\n  \"unicode\": \"U+1F1E8 U+1F1EE\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/CI.svg\"\n}, {\n  \"name\": \"Cook Islands\",\n  \"code\": \"CK\",\n  \"emoji\": \"🇨🇰\",\n  \"unicode\": \"U+1F1E8 U+1F1F0\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/CK.svg\"\n}, {\n  \"name\": \"Chile\",\n  \"code\": \"CL\",\n  \"emoji\": \"🇨🇱\",\n  \"unicode\": \"U+1F1E8 U+1F1F1\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/CL.svg\"\n}, {\n  \"name\": \"Cameroon\",\n  \"code\": \"CM\",\n  \"emoji\": \"🇨🇲\",\n  \"unicode\": \"U+1F1E8 U+1F1F2\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/CM.svg\"\n}, {\n  \"name\": \"China\",\n  \"code\": \"CN\",\n  \"emoji\": \"🇨🇳\",\n  \"unicode\": \"U+1F1E8 U+1F1F3\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/CN.svg\"\n}, {\n  \"name\": \"Colombia\",\n  \"code\": \"CO\",\n  \"emoji\": \"🇨🇴\",\n  \"unicode\": \"U+1F1E8 U+1F1F4\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/CO.svg\"\n}, {\n  \"name\": \"Clipperton Island\",\n  \"code\": \"CP\",\n  \"emoji\": \"🇨🇵\",\n  \"unicode\": \"U+1F1E8 U+1F1F5\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/CP.svg\"\n}, {\n  \"name\": \"Costa Rica\",\n  \"code\": \"CR\",\n  \"emoji\": \"🇨🇷\",\n  \"unicode\": \"U+1F1E8 U+1F1F7\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/CR.svg\"\n}, {\n  \"name\": \"Cuba\",\n  \"code\": \"CU\",\n  \"emoji\": \"🇨🇺\",\n  \"unicode\": \"U+1F1E8 U+1F1FA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/CU.svg\"\n}, {\n  \"name\": \"Cape Verde\",\n  \"code\": \"CV\",\n  \"emoji\": \"🇨🇻\",\n  \"unicode\": \"U+1F1E8 U+1F1FB\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/CV.svg\"\n}, {\n  \"name\": \"Curaçao\",\n  \"code\": \"CW\",\n  \"emoji\": \"🇨🇼\",\n  \"unicode\": \"U+1F1E8 U+1F1FC\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/CW.svg\"\n}, {\n  \"name\": \"Christmas Island\",\n  \"code\": \"CX\",\n  \"emoji\": \"🇨🇽\",\n  \"unicode\": \"U+1F1E8 U+1F1FD\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/CX.svg\"\n}, {\n  \"name\": \"Cyprus\",\n  \"code\": \"CY\",\n  \"emoji\": \"🇨🇾\",\n  \"unicode\": \"U+1F1E8 U+1F1FE\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/CY.svg\"\n}, {\n  \"name\": \"Czechia\",\n  \"code\": \"CZ\",\n  \"emoji\": \"🇨🇿\",\n  \"unicode\": \"U+1F1E8 U+1F1FF\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/CZ.svg\"\n}, {\n  \"name\": \"Germany\",\n  \"code\": \"DE\",\n  \"emoji\": \"🇩🇪\",\n  \"unicode\": \"U+1F1E9 U+1F1EA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/DE.svg\"\n}, {\n  \"name\": \"Diego Garcia\",\n  \"code\": \"DG\",\n  \"emoji\": \"🇩🇬\",\n  \"unicode\": \"U+1F1E9 U+1F1EC\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/DG.svg\"\n}, {\n  \"name\": \"Djibouti\",\n  \"code\": \"DJ\",\n  \"emoji\": \"🇩🇯\",\n  \"unicode\": \"U+1F1E9 U+1F1EF\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/DJ.svg\"\n}, {\n  \"name\": \"Denmark\",\n  \"code\": \"DK\",\n  \"emoji\": \"🇩🇰\",\n  \"unicode\": \"U+1F1E9 U+1F1F0\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/DK.svg\"\n}, {\n  \"name\": \"Dominica\",\n  \"code\": \"DM\",\n  \"emoji\": \"🇩🇲\",\n  \"unicode\": \"U+1F1E9 U+1F1F2\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/DM.svg\"\n}, {\n  \"name\": \"Dominican Republic\",\n  \"code\": \"DO\",\n  \"emoji\": \"🇩🇴\",\n  \"unicode\": \"U+1F1E9 U+1F1F4\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/DO.svg\"\n}, {\n  \"name\": \"Algeria\",\n  \"code\": \"DZ\",\n  \"emoji\": \"🇩🇿\",\n  \"unicode\": \"U+1F1E9 U+1F1FF\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/DZ.svg\"\n}, {\n  \"name\": \"Ceuta & Melilla\",\n  \"code\": \"EA\",\n  \"emoji\": \"🇪🇦\",\n  \"unicode\": \"U+1F1EA U+1F1E6\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/EA.svg\"\n}, {\n  \"name\": \"Ecuador\",\n  \"code\": \"EC\",\n  \"emoji\": \"🇪🇨\",\n  \"unicode\": \"U+1F1EA U+1F1E8\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/EC.svg\"\n}, {\n  \"name\": \"Estonia\",\n  \"code\": \"EE\",\n  \"emoji\": \"🇪🇪\",\n  \"unicode\": \"U+1F1EA U+1F1EA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/EE.svg\"\n}, {\n  \"name\": \"Egypt\",\n  \"code\": \"EG\",\n  \"emoji\": \"🇪🇬\",\n  \"unicode\": \"U+1F1EA U+1F1EC\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/EG.svg\"\n}, {\n  \"name\": \"Western Sahara\",\n  \"code\": \"EH\",\n  \"emoji\": \"🇪🇭\",\n  \"unicode\": \"U+1F1EA U+1F1ED\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/EH.svg\"\n}, {\n  \"name\": \"Eritrea\",\n  \"code\": \"ER\",\n  \"emoji\": \"🇪🇷\",\n  \"unicode\": \"U+1F1EA U+1F1F7\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/ER.svg\"\n}, {\n  \"name\": \"Spain\",\n  \"code\": \"ES\",\n  \"emoji\": \"🇪🇸\",\n  \"unicode\": \"U+1F1EA U+1F1F8\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/ES.svg\"\n}, {\n  \"name\": \"Ethiopia\",\n  \"code\": \"ET\",\n  \"emoji\": \"🇪🇹\",\n  \"unicode\": \"U+1F1EA U+1F1F9\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/ET.svg\"\n}, {\n  \"name\": \"European Union\",\n  \"code\": \"EU\",\n  \"emoji\": \"🇪🇺\",\n  \"unicode\": \"U+1F1EA U+1F1FA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/EU.svg\"\n}, {\n  \"name\": \"Finland\",\n  \"code\": \"FI\",\n  \"emoji\": \"🇫🇮\",\n  \"unicode\": \"U+1F1EB U+1F1EE\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/FI.svg\"\n}, {\n  \"name\": \"Fiji\",\n  \"code\": \"FJ\",\n  \"emoji\": \"🇫🇯\",\n  \"unicode\": \"U+1F1EB U+1F1EF\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/FJ.svg\"\n}, {\n  \"name\": \"Falkland Islands\",\n  \"code\": \"FK\",\n  \"emoji\": \"🇫🇰\",\n  \"unicode\": \"U+1F1EB U+1F1F0\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/FK.svg\"\n}, {\n  \"name\": \"Micronesia\",\n  \"code\": \"FM\",\n  \"emoji\": \"🇫🇲\",\n  \"unicode\": \"U+1F1EB U+1F1F2\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/FM.svg\"\n}, {\n  \"name\": \"Faroe Islands\",\n  \"code\": \"FO\",\n  \"emoji\": \"🇫🇴\",\n  \"unicode\": \"U+1F1EB U+1F1F4\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/FO.svg\"\n}, {\n  \"name\": \"France\",\n  \"code\": \"FR\",\n  \"emoji\": \"🇫🇷\",\n  \"unicode\": \"U+1F1EB U+1F1F7\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/FR.svg\"\n}, {\n  \"name\": \"Gabon\",\n  \"code\": \"GA\",\n  \"emoji\": \"🇬🇦\",\n  \"unicode\": \"U+1F1EC U+1F1E6\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/GA.svg\"\n}, {\n  \"name\": \"United Kingdom\",\n  \"code\": \"GB\",\n  \"emoji\": \"🇬🇧\",\n  \"unicode\": \"U+1F1EC U+1F1E7\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/GB.svg\"\n}, {\n  \"name\": \"Grenada\",\n  \"code\": \"GD\",\n  \"emoji\": \"🇬🇩\",\n  \"unicode\": \"U+1F1EC U+1F1E9\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/GD.svg\"\n}, {\n  \"name\": \"Georgia\",\n  \"code\": \"GE\",\n  \"emoji\": \"🇬🇪\",\n  \"unicode\": \"U+1F1EC U+1F1EA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/GE.svg\"\n}, {\n  \"name\": \"French Guiana\",\n  \"code\": \"GF\",\n  \"emoji\": \"🇬🇫\",\n  \"unicode\": \"U+1F1EC U+1F1EB\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/GF.svg\"\n}, {\n  \"name\": \"Guernsey\",\n  \"code\": \"GG\",\n  \"emoji\": \"🇬🇬\",\n  \"unicode\": \"U+1F1EC U+1F1EC\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/GG.svg\"\n}, {\n  \"name\": \"Ghana\",\n  \"code\": \"GH\",\n  \"emoji\": \"🇬🇭\",\n  \"unicode\": \"U+1F1EC U+1F1ED\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/GH.svg\"\n}, {\n  \"name\": \"Gibraltar\",\n  \"code\": \"GI\",\n  \"emoji\": \"🇬🇮\",\n  \"unicode\": \"U+1F1EC U+1F1EE\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/GI.svg\"\n}, {\n  \"name\": \"Greenland\",\n  \"code\": \"GL\",\n  \"emoji\": \"🇬🇱\",\n  \"unicode\": \"U+1F1EC U+1F1F1\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/GL.svg\"\n}, {\n  \"name\": \"Gambia\",\n  \"code\": \"GM\",\n  \"emoji\": \"🇬🇲\",\n  \"unicode\": \"U+1F1EC U+1F1F2\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/GM.svg\"\n}, {\n  \"name\": \"Guinea\",\n  \"code\": \"GN\",\n  \"emoji\": \"🇬🇳\",\n  \"unicode\": \"U+1F1EC U+1F1F3\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/GN.svg\"\n}, {\n  \"name\": \"Guadeloupe\",\n  \"code\": \"GP\",\n  \"emoji\": \"🇬🇵\",\n  \"unicode\": \"U+1F1EC U+1F1F5\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/GP.svg\"\n}, {\n  \"name\": \"Equatorial Guinea\",\n  \"code\": \"GQ\",\n  \"emoji\": \"🇬🇶\",\n  \"unicode\": \"U+1F1EC U+1F1F6\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/GQ.svg\"\n}, {\n  \"name\": \"Greece\",\n  \"code\": \"GR\",\n  \"emoji\": \"🇬🇷\",\n  \"unicode\": \"U+1F1EC U+1F1F7\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/GR.svg\"\n}, {\n  \"name\": \"South Georgia & South Sandwich Islands\",\n  \"code\": \"GS\",\n  \"emoji\": \"🇬🇸\",\n  \"unicode\": \"U+1F1EC U+1F1F8\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/GS.svg\"\n}, {\n  \"name\": \"Guatemala\",\n  \"code\": \"GT\",\n  \"emoji\": \"🇬🇹\",\n  \"unicode\": \"U+1F1EC U+1F1F9\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/GT.svg\"\n}, {\n  \"name\": \"Guam\",\n  \"code\": \"GU\",\n  \"emoji\": \"🇬🇺\",\n  \"unicode\": \"U+1F1EC U+1F1FA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/GU.svg\"\n}, {\n  \"name\": \"Guinea-Bissau\",\n  \"code\": \"GW\",\n  \"emoji\": \"🇬🇼\",\n  \"unicode\": \"U+1F1EC U+1F1FC\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/GW.svg\"\n}, {\n  \"name\": \"Guyana\",\n  \"code\": \"GY\",\n  \"emoji\": \"🇬🇾\",\n  \"unicode\": \"U+1F1EC U+1F1FE\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/GY.svg\"\n}, {\n  \"name\": \"Hong Kong SAR China\",\n  \"code\": \"HK\",\n  \"emoji\": \"🇭🇰\",\n  \"unicode\": \"U+1F1ED U+1F1F0\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/HK.svg\"\n}, {\n  \"name\": \"Heard & McDonald Islands\",\n  \"code\": \"HM\",\n  \"emoji\": \"🇭🇲\",\n  \"unicode\": \"U+1F1ED U+1F1F2\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/HM.svg\"\n}, {\n  \"name\": \"Honduras\",\n  \"code\": \"HN\",\n  \"emoji\": \"🇭🇳\",\n  \"unicode\": \"U+1F1ED U+1F1F3\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/HN.svg\"\n}, {\n  \"name\": \"Croatia\",\n  \"code\": \"HR\",\n  \"emoji\": \"🇭🇷\",\n  \"unicode\": \"U+1F1ED U+1F1F7\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/HR.svg\"\n}, {\n  \"name\": \"Haiti\",\n  \"code\": \"HT\",\n  \"emoji\": \"🇭🇹\",\n  \"unicode\": \"U+1F1ED U+1F1F9\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/HT.svg\"\n}, {\n  \"name\": \"Hungary\",\n  \"code\": \"HU\",\n  \"emoji\": \"🇭🇺\",\n  \"unicode\": \"U+1F1ED U+1F1FA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/HU.svg\"\n}, {\n  \"name\": \"Canary Islands\",\n  \"code\": \"IC\",\n  \"emoji\": \"🇮🇨\",\n  \"unicode\": \"U+1F1EE U+1F1E8\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/IC.svg\"\n}, {\n  \"name\": \"Indonesia\",\n  \"code\": \"ID\",\n  \"emoji\": \"🇮🇩\",\n  \"unicode\": \"U+1F1EE U+1F1E9\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/ID.svg\"\n}, {\n  \"name\": \"Ireland\",\n  \"code\": \"IE\",\n  \"emoji\": \"🇮🇪\",\n  \"unicode\": \"U+1F1EE U+1F1EA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/IE.svg\"\n}, {\n  \"name\": \"Israel\",\n  \"code\": \"IL\",\n  \"emoji\": \"🇮🇱\",\n  \"unicode\": \"U+1F1EE U+1F1F1\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/IL.svg\"\n}, {\n  \"name\": \"Isle of Man\",\n  \"code\": \"IM\",\n  \"emoji\": \"🇮🇲\",\n  \"unicode\": \"U+1F1EE U+1F1F2\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/IM.svg\"\n}, {\n  \"name\": \"India\",\n  \"code\": \"IN\",\n  \"emoji\": \"🇮🇳\",\n  \"unicode\": \"U+1F1EE U+1F1F3\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/IN.svg\"\n}, {\n  \"name\": \"British Indian Ocean Territory\",\n  \"code\": \"IO\",\n  \"emoji\": \"🇮🇴\",\n  \"unicode\": \"U+1F1EE U+1F1F4\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/IO.svg\"\n}, {\n  \"name\": \"Iraq\",\n  \"code\": \"IQ\",\n  \"emoji\": \"🇮🇶\",\n  \"unicode\": \"U+1F1EE U+1F1F6\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/IQ.svg\"\n}, {\n  \"name\": \"Iran\",\n  \"code\": \"IR\",\n  \"emoji\": \"🇮🇷\",\n  \"unicode\": \"U+1F1EE U+1F1F7\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/IR.svg\"\n}, {\n  \"name\": \"Iceland\",\n  \"code\": \"IS\",\n  \"emoji\": \"🇮🇸\",\n  \"unicode\": \"U+1F1EE U+1F1F8\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/IS.svg\"\n}, {\n  \"name\": \"Italy\",\n  \"code\": \"IT\",\n  \"emoji\": \"🇮🇹\",\n  \"unicode\": \"U+1F1EE U+1F1F9\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/IT.svg\"\n}, {\n  \"name\": \"Jersey\",\n  \"code\": \"JE\",\n  \"emoji\": \"🇯🇪\",\n  \"unicode\": \"U+1F1EF U+1F1EA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/JE.svg\"\n}, {\n  \"name\": \"Jamaica\",\n  \"code\": \"JM\",\n  \"emoji\": \"🇯🇲\",\n  \"unicode\": \"U+1F1EF U+1F1F2\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/JM.svg\"\n}, {\n  \"name\": \"Jordan\",\n  \"code\": \"JO\",\n  \"emoji\": \"🇯🇴\",\n  \"unicode\": \"U+1F1EF U+1F1F4\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/JO.svg\"\n}, {\n  \"name\": \"Japan\",\n  \"code\": \"JP\",\n  \"emoji\": \"🇯🇵\",\n  \"unicode\": \"U+1F1EF U+1F1F5\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/JP.svg\"\n}, {\n  \"name\": \"Kenya\",\n  \"code\": \"KE\",\n  \"emoji\": \"🇰🇪\",\n  \"unicode\": \"U+1F1F0 U+1F1EA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/KE.svg\"\n}, {\n  \"name\": \"Kyrgyzstan\",\n  \"code\": \"KG\",\n  \"emoji\": \"🇰🇬\",\n  \"unicode\": \"U+1F1F0 U+1F1EC\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/KG.svg\"\n}, {\n  \"name\": \"Cambodia\",\n  \"code\": \"KH\",\n  \"emoji\": \"🇰🇭\",\n  \"unicode\": \"U+1F1F0 U+1F1ED\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/KH.svg\"\n}, {\n  \"name\": \"Kiribati\",\n  \"code\": \"KI\",\n  \"emoji\": \"🇰🇮\",\n  \"unicode\": \"U+1F1F0 U+1F1EE\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/KI.svg\"\n}, {\n  \"name\": \"Comoros\",\n  \"code\": \"KM\",\n  \"emoji\": \"🇰🇲\",\n  \"unicode\": \"U+1F1F0 U+1F1F2\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/KM.svg\"\n}, {\n  \"name\": \"St. Kitts & Nevis\",\n  \"code\": \"KN\",\n  \"emoji\": \"🇰🇳\",\n  \"unicode\": \"U+1F1F0 U+1F1F3\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/KN.svg\"\n}, {\n  \"name\": \"North Korea\",\n  \"code\": \"KP\",\n  \"emoji\": \"🇰🇵\",\n  \"unicode\": \"U+1F1F0 U+1F1F5\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/KP.svg\"\n}, {\n  \"name\": \"South Korea\",\n  \"code\": \"KR\",\n  \"emoji\": \"🇰🇷\",\n  \"unicode\": \"U+1F1F0 U+1F1F7\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/KR.svg\"\n}, {\n  \"name\": \"Kuwait\",\n  \"code\": \"KW\",\n  \"emoji\": \"🇰🇼\",\n  \"unicode\": \"U+1F1F0 U+1F1FC\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/KW.svg\"\n}, {\n  \"name\": \"Cayman Islands\",\n  \"code\": \"KY\",\n  \"emoji\": \"🇰🇾\",\n  \"unicode\": \"U+1F1F0 U+1F1FE\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/KY.svg\"\n}, {\n  \"name\": \"Kazakhstan\",\n  \"code\": \"KZ\",\n  \"emoji\": \"🇰🇿\",\n  \"unicode\": \"U+1F1F0 U+1F1FF\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/KZ.svg\"\n}, {\n  \"name\": \"Laos\",\n  \"code\": \"LA\",\n  \"emoji\": \"🇱🇦\",\n  \"unicode\": \"U+1F1F1 U+1F1E6\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/LA.svg\"\n}, {\n  \"name\": \"Lebanon\",\n  \"code\": \"LB\",\n  \"emoji\": \"🇱🇧\",\n  \"unicode\": \"U+1F1F1 U+1F1E7\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/LB.svg\"\n}, {\n  \"name\": \"St. Lucia\",\n  \"code\": \"LC\",\n  \"emoji\": \"🇱🇨\",\n  \"unicode\": \"U+1F1F1 U+1F1E8\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/LC.svg\"\n}, {\n  \"name\": \"Liechtenstein\",\n  \"code\": \"LI\",\n  \"emoji\": \"🇱🇮\",\n  \"unicode\": \"U+1F1F1 U+1F1EE\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/LI.svg\"\n}, {\n  \"name\": \"Sri Lanka\",\n  \"code\": \"LK\",\n  \"emoji\": \"🇱🇰\",\n  \"unicode\": \"U+1F1F1 U+1F1F0\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/LK.svg\"\n}, {\n  \"name\": \"Liberia\",\n  \"code\": \"LR\",\n  \"emoji\": \"🇱🇷\",\n  \"unicode\": \"U+1F1F1 U+1F1F7\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/LR.svg\"\n}, {\n  \"name\": \"Lesotho\",\n  \"code\": \"LS\",\n  \"emoji\": \"🇱🇸\",\n  \"unicode\": \"U+1F1F1 U+1F1F8\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/LS.svg\"\n}, {\n  \"name\": \"Lithuania\",\n  \"code\": \"LT\",\n  \"emoji\": \"🇱🇹\",\n  \"unicode\": \"U+1F1F1 U+1F1F9\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/LT.svg\"\n}, {\n  \"name\": \"Luxembourg\",\n  \"code\": \"LU\",\n  \"emoji\": \"🇱🇺\",\n  \"unicode\": \"U+1F1F1 U+1F1FA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/LU.svg\"\n}, {\n  \"name\": \"Latvia\",\n  \"code\": \"LV\",\n  \"emoji\": \"🇱🇻\",\n  \"unicode\": \"U+1F1F1 U+1F1FB\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/LV.svg\"\n}, {\n  \"name\": \"Libya\",\n  \"code\": \"LY\",\n  \"emoji\": \"🇱🇾\",\n  \"unicode\": \"U+1F1F1 U+1F1FE\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/LY.svg\"\n}, {\n  \"name\": \"Morocco\",\n  \"code\": \"MA\",\n  \"emoji\": \"🇲🇦\",\n  \"unicode\": \"U+1F1F2 U+1F1E6\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/MA.svg\"\n}, {\n  \"name\": \"Monaco\",\n  \"code\": \"MC\",\n  \"emoji\": \"🇲🇨\",\n  \"unicode\": \"U+1F1F2 U+1F1E8\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/MC.svg\"\n}, {\n  \"name\": \"Moldova\",\n  \"code\": \"MD\",\n  \"emoji\": \"🇲🇩\",\n  \"unicode\": \"U+1F1F2 U+1F1E9\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/MD.svg\"\n}, {\n  \"name\": \"Montenegro\",\n  \"code\": \"ME\",\n  \"emoji\": \"🇲🇪\",\n  \"unicode\": \"U+1F1F2 U+1F1EA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/ME.svg\"\n}, {\n  \"name\": \"St. Martin\",\n  \"code\": \"MF\",\n  \"emoji\": \"🇲🇫\",\n  \"unicode\": \"U+1F1F2 U+1F1EB\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/MF.svg\"\n}, {\n  \"name\": \"Madagascar\",\n  \"code\": \"MG\",\n  \"emoji\": \"🇲🇬\",\n  \"unicode\": \"U+1F1F2 U+1F1EC\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/MG.svg\"\n}, {\n  \"name\": \"Marshall Islands\",\n  \"code\": \"MH\",\n  \"emoji\": \"🇲🇭\",\n  \"unicode\": \"U+1F1F2 U+1F1ED\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/MH.svg\"\n}, {\n  \"name\": \"North Macedonia\",\n  \"code\": \"MK\",\n  \"emoji\": \"🇲🇰\",\n  \"unicode\": \"U+1F1F2 U+1F1F0\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/MK.svg\"\n}, {\n  \"name\": \"Mali\",\n  \"code\": \"ML\",\n  \"emoji\": \"🇲🇱\",\n  \"unicode\": \"U+1F1F2 U+1F1F1\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/ML.svg\"\n}, {\n  \"name\": \"Myanmar (Burma)\",\n  \"code\": \"MM\",\n  \"emoji\": \"🇲🇲\",\n  \"unicode\": \"U+1F1F2 U+1F1F2\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/MM.svg\"\n}, {\n  \"name\": \"Mongolia\",\n  \"code\": \"MN\",\n  \"emoji\": \"🇲🇳\",\n  \"unicode\": \"U+1F1F2 U+1F1F3\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/MN.svg\"\n}, {\n  \"name\": \"Macao SAR China\",\n  \"code\": \"MO\",\n  \"emoji\": \"🇲🇴\",\n  \"unicode\": \"U+1F1F2 U+1F1F4\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/MO.svg\"\n}, {\n  \"name\": \"Northern Mariana Islands\",\n  \"code\": \"MP\",\n  \"emoji\": \"🇲🇵\",\n  \"unicode\": \"U+1F1F2 U+1F1F5\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/MP.svg\"\n}, {\n  \"name\": \"Martinique\",\n  \"code\": \"MQ\",\n  \"emoji\": \"🇲🇶\",\n  \"unicode\": \"U+1F1F2 U+1F1F6\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/MQ.svg\"\n}, {\n  \"name\": \"Mauritania\",\n  \"code\": \"MR\",\n  \"emoji\": \"🇲🇷\",\n  \"unicode\": \"U+1F1F2 U+1F1F7\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/MR.svg\"\n}, {\n  \"name\": \"Montserrat\",\n  \"code\": \"MS\",\n  \"emoji\": \"🇲🇸\",\n  \"unicode\": \"U+1F1F2 U+1F1F8\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/MS.svg\"\n}, {\n  \"name\": \"Malta\",\n  \"code\": \"MT\",\n  \"emoji\": \"🇲🇹\",\n  \"unicode\": \"U+1F1F2 U+1F1F9\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/MT.svg\"\n}, {\n  \"name\": \"Mauritius\",\n  \"code\": \"MU\",\n  \"emoji\": \"🇲🇺\",\n  \"unicode\": \"U+1F1F2 U+1F1FA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/MU.svg\"\n}, {\n  \"name\": \"Maldives\",\n  \"code\": \"MV\",\n  \"emoji\": \"🇲🇻\",\n  \"unicode\": \"U+1F1F2 U+1F1FB\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/MV.svg\"\n}, {\n  \"name\": \"Malawi\",\n  \"code\": \"MW\",\n  \"emoji\": \"🇲🇼\",\n  \"unicode\": \"U+1F1F2 U+1F1FC\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/MW.svg\"\n}, {\n  \"name\": \"Mexico\",\n  \"code\": \"MX\",\n  \"emoji\": \"🇲🇽\",\n  \"unicode\": \"U+1F1F2 U+1F1FD\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/MX.svg\"\n}, {\n  \"name\": \"Malaysia\",\n  \"code\": \"MY\",\n  \"emoji\": \"🇲🇾\",\n  \"unicode\": \"U+1F1F2 U+1F1FE\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/MY.svg\"\n}, {\n  \"name\": \"Mozambique\",\n  \"code\": \"MZ\",\n  \"emoji\": \"🇲🇿\",\n  \"unicode\": \"U+1F1F2 U+1F1FF\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/MZ.svg\"\n}, {\n  \"name\": \"Namibia\",\n  \"code\": \"NA\",\n  \"emoji\": \"🇳🇦\",\n  \"unicode\": \"U+1F1F3 U+1F1E6\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/NA.svg\"\n}, {\n  \"name\": \"New Caledonia\",\n  \"code\": \"NC\",\n  \"emoji\": \"🇳🇨\",\n  \"unicode\": \"U+1F1F3 U+1F1E8\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/NC.svg\"\n}, {\n  \"name\": \"Niger\",\n  \"code\": \"NE\",\n  \"emoji\": \"🇳🇪\",\n  \"unicode\": \"U+1F1F3 U+1F1EA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/NE.svg\"\n}, {\n  \"name\": \"Norfolk Island\",\n  \"code\": \"NF\",\n  \"emoji\": \"🇳🇫\",\n  \"unicode\": \"U+1F1F3 U+1F1EB\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/NF.svg\"\n}, {\n  \"name\": \"Nigeria\",\n  \"code\": \"NG\",\n  \"emoji\": \"🇳🇬\",\n  \"unicode\": \"U+1F1F3 U+1F1EC\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/NG.svg\"\n}, {\n  \"name\": \"Nicaragua\",\n  \"code\": \"NI\",\n  \"emoji\": \"🇳🇮\",\n  \"unicode\": \"U+1F1F3 U+1F1EE\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/NI.svg\"\n}, {\n  \"name\": \"Netherlands\",\n  \"code\": \"NL\",\n  \"emoji\": \"🇳🇱\",\n  \"unicode\": \"U+1F1F3 U+1F1F1\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/NL.svg\"\n}, {\n  \"name\": \"Norway\",\n  \"code\": \"NO\",\n  \"emoji\": \"🇳🇴\",\n  \"unicode\": \"U+1F1F3 U+1F1F4\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/NO.svg\"\n}, {\n  \"name\": \"Nepal\",\n  \"code\": \"NP\",\n  \"emoji\": \"🇳🇵\",\n  \"unicode\": \"U+1F1F3 U+1F1F5\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/NP.svg\"\n}, {\n  \"name\": \"Nauru\",\n  \"code\": \"NR\",\n  \"emoji\": \"🇳🇷\",\n  \"unicode\": \"U+1F1F3 U+1F1F7\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/NR.svg\"\n}, {\n  \"name\": \"Niue\",\n  \"code\": \"NU\",\n  \"emoji\": \"🇳🇺\",\n  \"unicode\": \"U+1F1F3 U+1F1FA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/NU.svg\"\n}, {\n  \"name\": \"New Zealand\",\n  \"code\": \"NZ\",\n  \"emoji\": \"🇳🇿\",\n  \"unicode\": \"U+1F1F3 U+1F1FF\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/NZ.svg\"\n}, {\n  \"name\": \"Oman\",\n  \"code\": \"OM\",\n  \"emoji\": \"🇴🇲\",\n  \"unicode\": \"U+1F1F4 U+1F1F2\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/OM.svg\"\n}, {\n  \"name\": \"Panama\",\n  \"code\": \"PA\",\n  \"emoji\": \"🇵🇦\",\n  \"unicode\": \"U+1F1F5 U+1F1E6\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/PA.svg\"\n}, {\n  \"name\": \"Peru\",\n  \"code\": \"PE\",\n  \"emoji\": \"🇵🇪\",\n  \"unicode\": \"U+1F1F5 U+1F1EA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/PE.svg\"\n}, {\n  \"name\": \"French Polynesia\",\n  \"code\": \"PF\",\n  \"emoji\": \"🇵🇫\",\n  \"unicode\": \"U+1F1F5 U+1F1EB\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/PF.svg\"\n}, {\n  \"name\": \"Papua New Guinea\",\n  \"code\": \"PG\",\n  \"emoji\": \"🇵🇬\",\n  \"unicode\": \"U+1F1F5 U+1F1EC\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/PG.svg\"\n}, {\n  \"name\": \"Philippines\",\n  \"code\": \"PH\",\n  \"emoji\": \"🇵🇭\",\n  \"unicode\": \"U+1F1F5 U+1F1ED\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/PH.svg\"\n}, {\n  \"name\": \"Pakistan\",\n  \"code\": \"PK\",\n  \"emoji\": \"🇵🇰\",\n  \"unicode\": \"U+1F1F5 U+1F1F0\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/PK.svg\"\n}, {\n  \"name\": \"Poland\",\n  \"code\": \"PL\",\n  \"emoji\": \"🇵🇱\",\n  \"unicode\": \"U+1F1F5 U+1F1F1\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/PL.svg\"\n}, {\n  \"name\": \"St. Pierre & Miquelon\",\n  \"code\": \"PM\",\n  \"emoji\": \"🇵🇲\",\n  \"unicode\": \"U+1F1F5 U+1F1F2\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/PM.svg\"\n}, {\n  \"name\": \"Pitcairn Islands\",\n  \"code\": \"PN\",\n  \"emoji\": \"🇵🇳\",\n  \"unicode\": \"U+1F1F5 U+1F1F3\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/PN.svg\"\n}, {\n  \"name\": \"Puerto Rico\",\n  \"code\": \"PR\",\n  \"emoji\": \"🇵🇷\",\n  \"unicode\": \"U+1F1F5 U+1F1F7\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/PR.svg\"\n}, {\n  \"name\": \"Palestinian Territories\",\n  \"code\": \"PS\",\n  \"emoji\": \"🇵🇸\",\n  \"unicode\": \"U+1F1F5 U+1F1F8\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/PS.svg\"\n}, {\n  \"name\": \"Portugal\",\n  \"code\": \"PT\",\n  \"emoji\": \"🇵🇹\",\n  \"unicode\": \"U+1F1F5 U+1F1F9\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/PT.svg\"\n}, {\n  \"name\": \"Palau\",\n  \"code\": \"PW\",\n  \"emoji\": \"🇵🇼\",\n  \"unicode\": \"U+1F1F5 U+1F1FC\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/PW.svg\"\n}, {\n  \"name\": \"Paraguay\",\n  \"code\": \"PY\",\n  \"emoji\": \"🇵🇾\",\n  \"unicode\": \"U+1F1F5 U+1F1FE\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/PY.svg\"\n}, {\n  \"name\": \"Qatar\",\n  \"code\": \"QA\",\n  \"emoji\": \"🇶🇦\",\n  \"unicode\": \"U+1F1F6 U+1F1E6\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/QA.svg\"\n}, {\n  \"name\": \"Réunion\",\n  \"code\": \"RE\",\n  \"emoji\": \"🇷🇪\",\n  \"unicode\": \"U+1F1F7 U+1F1EA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/RE.svg\"\n}, {\n  \"name\": \"Romania\",\n  \"code\": \"RO\",\n  \"emoji\": \"🇷🇴\",\n  \"unicode\": \"U+1F1F7 U+1F1F4\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/RO.svg\"\n}, {\n  \"name\": \"Serbia\",\n  \"code\": \"RS\",\n  \"emoji\": \"🇷🇸\",\n  \"unicode\": \"U+1F1F7 U+1F1F8\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/RS.svg\"\n}, {\n  \"name\": \"Russia\",\n  \"code\": \"RU\",\n  \"emoji\": \"🇷🇺\",\n  \"unicode\": \"U+1F1F7 U+1F1FA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/RU.svg\"\n}, {\n  \"name\": \"Rwanda\",\n  \"code\": \"RW\",\n  \"emoji\": \"🇷🇼\",\n  \"unicode\": \"U+1F1F7 U+1F1FC\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/RW.svg\"\n}, {\n  \"name\": \"Saudi Arabia\",\n  \"code\": \"SA\",\n  \"emoji\": \"🇸🇦\",\n  \"unicode\": \"U+1F1F8 U+1F1E6\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/SA.svg\"\n}, {\n  \"name\": \"Solomon Islands\",\n  \"code\": \"SB\",\n  \"emoji\": \"🇸🇧\",\n  \"unicode\": \"U+1F1F8 U+1F1E7\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/SB.svg\"\n}, {\n  \"name\": \"Seychelles\",\n  \"code\": \"SC\",\n  \"emoji\": \"🇸🇨\",\n  \"unicode\": \"U+1F1F8 U+1F1E8\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/SC.svg\"\n}, {\n  \"name\": \"Sudan\",\n  \"code\": \"SD\",\n  \"emoji\": \"🇸🇩\",\n  \"unicode\": \"U+1F1F8 U+1F1E9\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/SD.svg\"\n}, {\n  \"name\": \"Sweden\",\n  \"code\": \"SE\",\n  \"emoji\": \"🇸🇪\",\n  \"unicode\": \"U+1F1F8 U+1F1EA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/SE.svg\"\n}, {\n  \"name\": \"Singapore\",\n  \"code\": \"SG\",\n  \"emoji\": \"🇸🇬\",\n  \"unicode\": \"U+1F1F8 U+1F1EC\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/SG.svg\"\n}, {\n  \"name\": \"St. Helena\",\n  \"code\": \"SH\",\n  \"emoji\": \"🇸🇭\",\n  \"unicode\": \"U+1F1F8 U+1F1ED\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/SH.svg\"\n}, {\n  \"name\": \"Slovenia\",\n  \"code\": \"SI\",\n  \"emoji\": \"🇸🇮\",\n  \"unicode\": \"U+1F1F8 U+1F1EE\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/SI.svg\"\n}, {\n  \"name\": \"Svalbard & Jan Mayen\",\n  \"code\": \"SJ\",\n  \"emoji\": \"🇸🇯\",\n  \"unicode\": \"U+1F1F8 U+1F1EF\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/SJ.svg\"\n}, {\n  \"name\": \"Slovakia\",\n  \"code\": \"SK\",\n  \"emoji\": \"🇸🇰\",\n  \"unicode\": \"U+1F1F8 U+1F1F0\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/SK.svg\"\n}, {\n  \"name\": \"Sierra Leone\",\n  \"code\": \"SL\",\n  \"emoji\": \"🇸🇱\",\n  \"unicode\": \"U+1F1F8 U+1F1F1\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/SL.svg\"\n}, {\n  \"name\": \"San Marino\",\n  \"code\": \"SM\",\n  \"emoji\": \"🇸🇲\",\n  \"unicode\": \"U+1F1F8 U+1F1F2\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/SM.svg\"\n}, {\n  \"name\": \"Senegal\",\n  \"code\": \"SN\",\n  \"emoji\": \"🇸🇳\",\n  \"unicode\": \"U+1F1F8 U+1F1F3\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/SN.svg\"\n}, {\n  \"name\": \"Somalia\",\n  \"code\": \"SO\",\n  \"emoji\": \"🇸🇴\",\n  \"unicode\": \"U+1F1F8 U+1F1F4\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/SO.svg\"\n}, {\n  \"name\": \"Suriname\",\n  \"code\": \"SR\",\n  \"emoji\": \"🇸🇷\",\n  \"unicode\": \"U+1F1F8 U+1F1F7\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/SR.svg\"\n}, {\n  \"name\": \"South Sudan\",\n  \"code\": \"SS\",\n  \"emoji\": \"🇸🇸\",\n  \"unicode\": \"U+1F1F8 U+1F1F8\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/SS.svg\"\n}, {\n  \"name\": \"São Tomé & Príncipe\",\n  \"code\": \"ST\",\n  \"emoji\": \"🇸🇹\",\n  \"unicode\": \"U+1F1F8 U+1F1F9\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/ST.svg\"\n}, {\n  \"name\": \"El Salvador\",\n  \"code\": \"SV\",\n  \"emoji\": \"🇸🇻\",\n  \"unicode\": \"U+1F1F8 U+1F1FB\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/SV.svg\"\n}, {\n  \"name\": \"Sint Maarten\",\n  \"code\": \"SX\",\n  \"emoji\": \"🇸🇽\",\n  \"unicode\": \"U+1F1F8 U+1F1FD\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/SX.svg\"\n}, {\n  \"name\": \"Syria\",\n  \"code\": \"SY\",\n  \"emoji\": \"🇸🇾\",\n  \"unicode\": \"U+1F1F8 U+1F1FE\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/SY.svg\"\n}, {\n  \"name\": \"Eswatini\",\n  \"code\": \"SZ\",\n  \"emoji\": \"🇸🇿\",\n  \"unicode\": \"U+1F1F8 U+1F1FF\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/SZ.svg\"\n}, {\n  \"name\": \"Tristan da Cunha\",\n  \"code\": \"TA\",\n  \"emoji\": \"🇹🇦\",\n  \"unicode\": \"U+1F1F9 U+1F1E6\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/TA.svg\"\n}, {\n  \"name\": \"Turks & Caicos Islands\",\n  \"code\": \"TC\",\n  \"emoji\": \"🇹🇨\",\n  \"unicode\": \"U+1F1F9 U+1F1E8\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/TC.svg\"\n}, {\n  \"name\": \"Chad\",\n  \"code\": \"TD\",\n  \"emoji\": \"🇹🇩\",\n  \"unicode\": \"U+1F1F9 U+1F1E9\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/TD.svg\"\n}, {\n  \"name\": \"French Southern Territories\",\n  \"code\": \"TF\",\n  \"emoji\": \"🇹🇫\",\n  \"unicode\": \"U+1F1F9 U+1F1EB\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/TF.svg\"\n}, {\n  \"name\": \"Togo\",\n  \"code\": \"TG\",\n  \"emoji\": \"🇹🇬\",\n  \"unicode\": \"U+1F1F9 U+1F1EC\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/TG.svg\"\n}, {\n  \"name\": \"Thailand\",\n  \"code\": \"TH\",\n  \"emoji\": \"🇹🇭\",\n  \"unicode\": \"U+1F1F9 U+1F1ED\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/TH.svg\"\n}, {\n  \"name\": \"Tajikistan\",\n  \"code\": \"TJ\",\n  \"emoji\": \"🇹🇯\",\n  \"unicode\": \"U+1F1F9 U+1F1EF\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/TJ.svg\"\n}, {\n  \"name\": \"Tokelau\",\n  \"code\": \"TK\",\n  \"emoji\": \"🇹🇰\",\n  \"unicode\": \"U+1F1F9 U+1F1F0\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/TK.svg\"\n}, {\n  \"name\": \"Timor-Leste\",\n  \"code\": \"TL\",\n  \"emoji\": \"🇹🇱\",\n  \"unicode\": \"U+1F1F9 U+1F1F1\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/TL.svg\"\n}, {\n  \"name\": \"Turkmenistan\",\n  \"code\": \"TM\",\n  \"emoji\": \"🇹🇲\",\n  \"unicode\": \"U+1F1F9 U+1F1F2\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/TM.svg\"\n}, {\n  \"name\": \"Tunisia\",\n  \"code\": \"TN\",\n  \"emoji\": \"🇹🇳\",\n  \"unicode\": \"U+1F1F9 U+1F1F3\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/TN.svg\"\n}, {\n  \"name\": \"Tonga\",\n  \"code\": \"TO\",\n  \"emoji\": \"🇹🇴\",\n  \"unicode\": \"U+1F1F9 U+1F1F4\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/TO.svg\"\n}, {\n  \"name\": \"Turkey\",\n  \"code\": \"TR\",\n  \"emoji\": \"🇹🇷\",\n  \"unicode\": \"U+1F1F9 U+1F1F7\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/TR.svg\"\n}, {\n  \"name\": \"Trinidad & Tobago\",\n  \"code\": \"TT\",\n  \"emoji\": \"🇹🇹\",\n  \"unicode\": \"U+1F1F9 U+1F1F9\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/TT.svg\"\n}, {\n  \"name\": \"Tuvalu\",\n  \"code\": \"TV\",\n  \"emoji\": \"🇹🇻\",\n  \"unicode\": \"U+1F1F9 U+1F1FB\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/TV.svg\"\n}, {\n  \"name\": \"Taiwan\",\n  \"code\": \"TW\",\n  \"emoji\": \"🇹🇼\",\n  \"unicode\": \"U+1F1F9 U+1F1FC\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/TW.svg\"\n}, {\n  \"name\": \"Tanzania\",\n  \"code\": \"TZ\",\n  \"emoji\": \"🇹🇿\",\n  \"unicode\": \"U+1F1F9 U+1F1FF\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/TZ.svg\"\n}, {\n  \"name\": \"Ukraine\",\n  \"code\": \"UA\",\n  \"emoji\": \"🇺🇦\",\n  \"unicode\": \"U+1F1FA U+1F1E6\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/UA.svg\"\n}, {\n  \"name\": \"Uganda\",\n  \"code\": \"UG\",\n  \"emoji\": \"🇺🇬\",\n  \"unicode\": \"U+1F1FA U+1F1EC\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/UG.svg\"\n}, {\n  \"name\": \"U.S. Outlying Islands\",\n  \"code\": \"UM\",\n  \"emoji\": \"🇺🇲\",\n  \"unicode\": \"U+1F1FA U+1F1F2\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/UM.svg\"\n}, {\n  \"name\": \"United Nations\",\n  \"code\": \"UN\",\n  \"emoji\": \"🇺🇳\",\n  \"unicode\": \"U+1F1FA U+1F1F3\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/UN.svg\"\n}, {\n  \"name\": \"United States\",\n  \"code\": \"US\",\n  \"emoji\": \"🇺🇸\",\n  \"unicode\": \"U+1F1FA U+1F1F8\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/US.svg\"\n}, {\n  \"name\": \"Uruguay\",\n  \"code\": \"UY\",\n  \"emoji\": \"🇺🇾\",\n  \"unicode\": \"U+1F1FA U+1F1FE\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/UY.svg\"\n}, {\n  \"name\": \"Uzbekistan\",\n  \"code\": \"UZ\",\n  \"emoji\": \"🇺🇿\",\n  \"unicode\": \"U+1F1FA U+1F1FF\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/UZ.svg\"\n}, {\n  \"name\": \"Vatican City\",\n  \"code\": \"VA\",\n  \"emoji\": \"🇻🇦\",\n  \"unicode\": \"U+1F1FB U+1F1E6\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/VA.svg\"\n}, {\n  \"name\": \"St. Vincent & Grenadines\",\n  \"code\": \"VC\",\n  \"emoji\": \"🇻🇨\",\n  \"unicode\": \"U+1F1FB U+1F1E8\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/VC.svg\"\n}, {\n  \"name\": \"Venezuela\",\n  \"code\": \"VE\",\n  \"emoji\": \"🇻🇪\",\n  \"unicode\": \"U+1F1FB U+1F1EA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/VE.svg\"\n}, {\n  \"name\": \"British Virgin Islands\",\n  \"code\": \"VG\",\n  \"emoji\": \"🇻🇬\",\n  \"unicode\": \"U+1F1FB U+1F1EC\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/VG.svg\"\n}, {\n  \"name\": \"U.S. Virgin Islands\",\n  \"code\": \"VI\",\n  \"emoji\": \"🇻🇮\",\n  \"unicode\": \"U+1F1FB U+1F1EE\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/VI.svg\"\n}, {\n  \"name\": \"Vietnam\",\n  \"code\": \"VN\",\n  \"emoji\": \"🇻🇳\",\n  \"unicode\": \"U+1F1FB U+1F1F3\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/VN.svg\"\n}, {\n  \"name\": \"Vanuatu\",\n  \"code\": \"VU\",\n  \"emoji\": \"🇻🇺\",\n  \"unicode\": \"U+1F1FB U+1F1FA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/VU.svg\"\n}, {\n  \"name\": \"Wallis & Futuna\",\n  \"code\": \"WF\",\n  \"emoji\": \"🇼🇫\",\n  \"unicode\": \"U+1F1FC U+1F1EB\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/WF.svg\"\n}, {\n  \"name\": \"Samoa\",\n  \"code\": \"WS\",\n  \"emoji\": \"🇼🇸\",\n  \"unicode\": \"U+1F1FC U+1F1F8\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/WS.svg\"\n}, {\n  \"name\": \"Kosovo\",\n  \"code\": \"XK\",\n  \"emoji\": \"🇽🇰\",\n  \"unicode\": \"U+1F1FD U+1F1F0\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/XK.svg\"\n}, {\n  \"name\": \"Yemen\",\n  \"code\": \"YE\",\n  \"emoji\": \"🇾🇪\",\n  \"unicode\": \"U+1F1FE U+1F1EA\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/YE.svg\"\n}, {\n  \"name\": \"Mayotte\",\n  \"code\": \"YT\",\n  \"emoji\": \"🇾🇹\",\n  \"unicode\": \"U+1F1FE U+1F1F9\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/YT.svg\"\n}, {\n  \"name\": \"South Africa\",\n  \"code\": \"ZA\",\n  \"emoji\": \"🇿🇦\",\n  \"unicode\": \"U+1F1FF U+1F1E6\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/ZA.svg\"\n}, {\n  \"name\": \"Zambia\",\n  \"code\": \"ZM\",\n  \"emoji\": \"🇿🇲\",\n  \"unicode\": \"U+1F1FF U+1F1F2\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/ZM.svg\"\n}, {\n  \"name\": \"Zimbabwe\",\n  \"code\": \"ZW\",\n  \"emoji\": \"🇿🇼\",\n  \"unicode\": \"U+1F1FF U+1F1FC\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/ZW.svg\"\n}, {\n  \"name\": \"England\",\n  \"code\": \"ENGLAND\",\n  \"emoji\": \"🏴󠁧󠁢󠁥󠁮󠁧󠁿\",\n  \"unicode\": \"U+1F3F4 U+E0067 U+E0062 U+E0065 U+E006E U+E0067 U+E007F\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/ENGLAND.svg\"\n}, {\n  \"name\": \"Scotland\",\n  \"code\": \"SCOTLAND\",\n  \"emoji\": \"🏴󠁧󠁢󠁳󠁣󠁴󠁿\",\n  \"unicode\": \"U+1F3F4 U+E0067 U+E0062 U+E0073 U+E0063 U+E0074 U+E007F\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/SCOTLAND.svg\"\n}, {\n  \"name\": \"Wales\",\n  \"code\": \"WALES\",\n  \"emoji\": \"🏴󠁧󠁢󠁷󠁬󠁳󠁿\",\n  \"unicode\": \"U+1F3F4 U+E0067 U+E0062 U+E0077 U+E006C U+E0073 U+E007F\",\n  \"image\": \"https://cdn.jsdelivr.net/npm/country-flag-emoji-json@2.0.0/dist/images/WALES.svg\"\n}];\nexport let timezones = [{\n  \"value\": \"Dateline Standard Time\",\n  \"abbr\": \"DST\",\n  \"offset\": -12,\n  \"isdst\": false,\n  \"text\": \"(UTC-12:00) International Date Line West\",\n  \"utc\": [\"Etc/GMT+12\"],\n  \"countries\": [\"UM\"]\n}, {\n  \"value\": \"UTC-11\",\n  \"abbr\": \"U\",\n  \"offset\": -11,\n  \"isdst\": false,\n  \"text\": \"(UTC-11:00) Coordinated Universal Time-11\",\n  \"utc\": [\"Etc/GMT+11\", \"Pacific/Midway\", \"Pacific/Niue\", \"Pacific/Pago_Pago\"],\n  \"countries\": [\"AS\", \"NU\", \"UM\"]\n}, {\n  \"value\": \"Hawaiian Standard Time\",\n  \"abbr\": \"HST\",\n  \"offset\": -10,\n  \"isdst\": false,\n  \"text\": \"(UTC-10:00) Hawaii\",\n  \"utc\": [\"Etc/GMT+10\", \"Pacific/Honolulu\", \"Pacific/Johnston\", \"Pacific/Rarotonga\", \"Pacific/Tahiti\"],\n  \"countries\": [\"UM\", \"US\"]\n}, {\n  \"value\": \"Alaskan Standard Time\",\n  \"abbr\": \"AKDT\",\n  \"offset\": -8,\n  \"isdst\": true,\n  \"text\": \"(UTC-09:00) Alaska\",\n  \"utc\": [\"America/Anchorage\", \"America/Juneau\", \"America/Nome\", \"America/Sitka\", \"America/Yakutat\"],\n  \"countries\": [\"US\"]\n}, {\n  \"value\": \"Pacific Standard Time (Mexico)\",\n  \"abbr\": \"PDT\",\n  \"offset\": -7,\n  \"isdst\": true,\n  \"text\": \"(UTC-08:00) Baja California\",\n  \"utc\": [\"America/Santa_Isabel\"],\n  \"countries\": [\"MX\"]\n}, {\n  \"value\": \"Pacific Daylight Time\",\n  \"abbr\": \"PDT\",\n  \"offset\": -7,\n  \"isdst\": true,\n  \"text\": \"(UTC-07:00) Pacific Time (US & Canada)\",\n  \"utc\": [\"America/Los_Angeles\", \"America/Tijuana\", \"America/Vancouver\"],\n  \"countries\": [\"CA\", \"MX\", \"US\"]\n}, {\n  \"value\": \"Pacific Standard Time\",\n  \"abbr\": \"PST\",\n  \"offset\": -8,\n  \"isdst\": false,\n  \"text\": \"(UTC-08:00) Pacific Time (US & Canada)\",\n  \"utc\": [\"America/Los_Angeles\", \"America/Tijuana\", \"America/Vancouver\", \"PST8PDT\"],\n  \"countries\": [\"CA\", \"MX\", \"US\"]\n}, {\n  \"value\": \"US Mountain Standard Time\",\n  \"abbr\": \"UMST\",\n  \"offset\": -7,\n  \"isdst\": false,\n  \"text\": \"(UTC-07:00) Arizona\",\n  \"utc\": [\"America/Creston\", \"America/Dawson\", \"America/Dawson_Creek\", \"America/Hermosillo\", \"America/Phoenix\", \"America/Whitehorse\", \"Etc/GMT+7\"],\n  \"countries\": [\"US\"]\n}, {\n  \"value\": \"Mountain Standard Time (Mexico)\",\n  \"abbr\": \"MDT\",\n  \"offset\": -6,\n  \"isdst\": true,\n  \"text\": \"(UTC-07:00) Chihuahua, La Paz, Mazatlan\",\n  \"utc\": [\"America/Chihuahua\", \"America/Mazatlan\"],\n  \"countries\": [\"MX\"]\n}, {\n  \"value\": \"Mountain Standard Time\",\n  \"abbr\": \"MDT\",\n  \"offset\": -6,\n  \"isdst\": true,\n  \"text\": \"(UTC-07:00) Mountain Time (US & Canada)\",\n  \"utc\": [\"America/Boise\", \"America/Cambridge_Bay\", \"America/Denver\", \"America/Edmonton\", \"America/Inuvik\", \"America/Ojinaga\", \"America/Yellowknife\", \"MST7MDT\"],\n  \"countries\": [\"CA\", \"MX\", \"US\"]\n}, {\n  \"value\": \"Central America Standard Time\",\n  \"abbr\": \"CAST\",\n  \"offset\": -6,\n  \"isdst\": false,\n  \"text\": \"(UTC-06:00) Central America\",\n  \"utc\": [\"America/Belize\", \"America/Costa_Rica\", \"America/El_Salvador\", \"America/Guatemala\", \"America/Managua\", \"America/Tegucigalpa\", \"Etc/GMT+6\", \"Pacific/Galapagos\"],\n  \"countries\": [\"BZ\", \"CR\", \"GT\", \"HN\", \"NI\", \"SV\"]\n}, {\n  \"value\": \"Central Standard Time\",\n  \"abbr\": \"CDT\",\n  \"offset\": -5,\n  \"isdst\": true,\n  \"text\": \"(UTC-06:00) Central Time (US & Canada)\",\n  \"utc\": [\"America/Chicago\", \"America/Indiana/Knox\", \"America/Indiana/Tell_City\", \"America/Matamoros\", \"America/Menominee\", \"America/North_Dakota/Beulah\", \"America/North_Dakota/Center\", \"America/North_Dakota/New_Salem\", \"America/Rainy_River\", \"America/Rankin_Inlet\", \"America/Resolute\", \"America/Winnipeg\", \"CST6CDT\"],\n  \"countries\": [\"MX\", \"US\"]\n}, {\n  \"value\": \"Central Standard Time (Mexico)\",\n  \"abbr\": \"CDT\",\n  \"offset\": -5,\n  \"isdst\": true,\n  \"text\": \"(UTC-06:00) Guadalajara, Mexico City, Monterrey\",\n  \"utc\": [\"America/Bahia_Banderas\", \"America/Cancun\", \"America/Merida\", \"America/Mexico_City\", \"America/Monterrey\"],\n  \"countries\": [\"MX\"]\n}, {\n  \"value\": \"Canada Central Standard Time\",\n  \"abbr\": \"CCST\",\n  \"offset\": -6,\n  \"isdst\": false,\n  \"text\": \"(UTC-06:00) Saskatchewan\",\n  \"utc\": [\"America/Regina\", \"America/Swift_Current\"],\n  \"countries\": [\"CA\"]\n}, {\n  \"value\": \"SA Pacific Standard Time\",\n  \"abbr\": \"SPST\",\n  \"offset\": -5,\n  \"isdst\": false,\n  \"text\": \"(UTC-05:00) Bogota, Lima, Quito\",\n  \"utc\": [\"America/Bogota\", \"America/Cayman\", \"America/Coral_Harbour\", \"America/Eirunepe\", \"America/Guayaquil\", \"America/Jamaica\", \"America/Lima\", \"America/Panama\", \"America/Rio_Branco\", \"Etc/GMT+5\"],\n  \"countries\": [\"CO\", \"EC\", \"JM\", \"KY\", \"PA\", \"PE\"]\n}, {\n  \"value\": \"Eastern Standard Time\",\n  \"abbr\": \"EST\",\n  \"offset\": -5,\n  \"isdst\": false,\n  \"text\": \"(UTC-05:00) Eastern Time (US & Canada)\",\n  \"utc\": [\"America/Detroit\", \"America/Havana\", \"America/Indiana/Petersburg\", \"America/Indiana/Vincennes\", \"America/Indiana/Winamac\", \"America/Iqaluit\", \"America/Kentucky/Monticello\", \"America/Louisville\", \"America/Montreal\", \"America/Nassau\", \"America/New_York\", \"America/Nipigon\", \"America/Pangnirtung\", \"America/Port-au-Prince\", \"America/Thunder_Bay\", \"America/Toronto\"],\n  \"countries\": [\"BS\", \"CA\", \"CU\", \"HT\", \"JM\", \"KY\", \"US\"]\n}, {\n  \"value\": \"Eastern Daylight Time\",\n  \"abbr\": \"EDT\",\n  \"offset\": -4,\n  \"isdst\": true,\n  \"text\": \"(UTC-04:00) Eastern Daylight Time (US & Canada)\",\n  \"utc\": [\"America/Detroit\", \"America/Havana\", \"America/Indiana/Petersburg\", \"America/Indiana/Vincennes\", \"America/Indiana/Winamac\", \"America/Iqaluit\", \"America/Kentucky/Monticello\", \"America/Louisville\", \"America/Montreal\", \"America/Nassau\", \"America/New_York\", \"America/Nipigon\", \"America/Pangnirtung\", \"America/Port-au-Prince\", \"America/Thunder_Bay\", \"America/Toronto\"],\n  \"countries\": [\"BS\", \"CA\", \"CU\", \"HT\", \"JM\", \"KY\", \"US\"]\n}, {\n  \"value\": \"US Eastern Standard Time\",\n  \"abbr\": \"UEDT\",\n  \"offset\": -5,\n  \"isdst\": false,\n  \"text\": \"(UTC-05:00) Indiana (East)\",\n  \"utc\": [\"America/Indiana/Marengo\", \"America/Indiana/Vevay\", \"America/Indianapolis\"],\n  \"countries\": [\"US\"]\n}, {\n  \"value\": \"Venezuela Standard Time\",\n  \"abbr\": \"VST\",\n  \"offset\": -4.5,\n  \"isdst\": false,\n  \"text\": \"(UTC-04:30) Caracas\",\n  \"utc\": [\"America/Caracas\"],\n  \"countries\": [\"VE\"]\n}, {\n  \"value\": \"Paraguay Standard Time\",\n  \"abbr\": \"PYT\",\n  \"offset\": -4,\n  \"isdst\": false,\n  \"text\": \"(UTC-04:00) Asuncion\",\n  \"utc\": [\"America/Asuncion\"],\n  \"countries\": [\"PY\"]\n}, {\n  \"value\": \"Atlantic Standard Time\",\n  \"abbr\": \"ADT\",\n  \"offset\": -3,\n  \"isdst\": true,\n  \"text\": \"(UTC-04:00) Atlantic Time (Canada)\",\n  \"utc\": [\"America/Glace_Bay\", \"America/Goose_Bay\", \"America/Halifax\", \"America/Moncton\", \"America/Thule\", \"Atlantic/Bermuda\"],\n  \"countries\": [\"CA\"]\n}, {\n  \"value\": \"Central Brazilian Standard Time\",\n  \"abbr\": \"CBST\",\n  \"offset\": -4,\n  \"isdst\": false,\n  \"text\": \"(UTC-04:00) Cuiaba\",\n  \"utc\": [\"America/Campo_Grande\", \"America/Cuiaba\"],\n  \"countries\": [\"BR\"]\n}, {\n  \"value\": \"SA Western Standard Time\",\n  \"abbr\": \"SWST\",\n  \"offset\": -4,\n  \"isdst\": false,\n  \"text\": \"(UTC-04:00) Georgetown, La Paz, Manaus, San Juan\",\n  \"utc\": [\"America/Anguilla\", \"America/Antigua\", \"America/Aruba\", \"America/Barbados\", \"America/Blanc-Sablon\", \"America/Boa_Vista\", \"America/Curacao\", \"America/Dominica\", \"America/Grand_Turk\", \"America/Grenada\", \"America/Guadeloupe\", \"America/Guyana\", \"America/Kralendijk\", \"America/La_Paz\", \"America/Lower_Princes\", \"America/Manaus\", \"America/Marigot\", \"America/Martinique\", \"America/Montserrat\", \"America/Port_of_Spain\", \"America/Porto_Velho\", \"America/Puerto_Rico\", \"America/Santo_Domingo\", \"America/St_Barthelemy\", \"America/St_Kitts\", \"America/St_Lucia\", \"America/St_Thomas\", \"America/St_Vincent\", \"America/Tortola\", \"Etc/GMT+4\"],\n  \"countries\": [\"AI\", \"AN\", \"AW\", \"BB\", \"BQ\", \"CW\", \"DM\", \"DO\", \"GD\", \"GP\", \"GY\", \"KN\", \"LC\", \"MF\", \"MQ\", \"MS\", \"PR\", \"SX\", \"TC\", \"TT\", \"VC\", \"VG\", \"VI\"]\n}, {\n  \"value\": \"Pacific SA Standard Time\",\n  \"abbr\": \"PSST\",\n  \"offset\": -4,\n  \"isdst\": false,\n  \"text\": \"(UTC-04:00) Santiago\",\n  \"utc\": [\"America/Santiago\", \"Antarctica/Palmer\"]\n}, {\n  \"value\": \"Newfoundland Standard Time\",\n  \"abbr\": \"NDT\",\n  \"offset\": -2.5,\n  \"isdst\": true,\n  \"text\": \"(UTC-03:30) Newfoundland\",\n  \"utc\": [\"America/St_Johns\"]\n}, {\n  \"value\": \"E. South America Standard Time\",\n  \"abbr\": \"ESAST\",\n  \"offset\": -3,\n  \"isdst\": false,\n  \"text\": \"(UTC-03:00) Brasilia\",\n  \"utc\": [\"America/Sao_Paulo\"]\n}, {\n  \"value\": \"Argentina Standard Time\",\n  \"abbr\": \"AST\",\n  \"offset\": -3,\n  \"isdst\": false,\n  \"text\": \"(UTC-03:00) Buenos Aires\",\n  \"utc\": [\"America/Argentina/La_Rioja\", \"America/Argentina/Rio_Gallegos\", \"America/Argentina/Salta\", \"America/Argentina/San_Juan\", \"America/Argentina/San_Luis\", \"America/Argentina/Tucuman\", \"America/Argentina/Ushuaia\", \"America/Buenos_Aires\", \"America/Catamarca\", \"America/Cordoba\", \"America/Jujuy\", \"America/Mendoza\"],\n  \"countries\": [\"AR\"]\n}, {\n  \"value\": \"SA Eastern Standard Time\",\n  \"abbr\": \"SEST\",\n  \"offset\": -3,\n  \"isdst\": false,\n  \"text\": \"(UTC-03:00) Cayenne, Fortaleza\",\n  \"utc\": [\"America/Araguaina\", \"America/Belem\", \"America/Cayenne\", \"America/Fortaleza\", \"America/Maceio\", \"America/Paramaribo\", \"America/Recife\", \"America/Santarem\", \"Antarctica/Rothera\", \"Atlantic/Stanley\", \"Etc/GMT+3\"],\n  \"countries\": [\"BR\", \"GF\", \"SR\"]\n}, {\n  \"value\": \"Greenland Standard Time\",\n  \"abbr\": \"GDT\",\n  \"offset\": -3,\n  \"isdst\": true,\n  \"text\": \"(UTC-03:00) Greenland\",\n  \"utc\": [\"America/Godthab\"],\n  \"countries\": [\"GL\"]\n}, {\n  \"value\": \"Montevideo Standard Time\",\n  \"abbr\": \"MST\",\n  \"offset\": -3,\n  \"isdst\": false,\n  \"text\": \"(UTC-03:00) Montevideo\",\n  \"utc\": [\"America/Montevideo\"],\n  \"countries\": [\"UY\"]\n}, {\n  \"value\": \"Bahia Standard Time\",\n  \"abbr\": \"BST\",\n  \"offset\": -3,\n  \"isdst\": false,\n  \"text\": \"(UTC-03:00) Salvador\",\n  \"utc\": [\"America/Bahia\"],\n  \"countries\": [\"BR\"]\n}, {\n  \"value\": \"UTC-02\",\n  \"abbr\": \"U\",\n  \"offset\": -2,\n  \"isdst\": false,\n  \"text\": \"(UTC-02:00) Coordinated Universal Time-02\",\n  \"utc\": [\"America/Noronha\", \"Atlantic/South_Georgia\", \"Etc/GMT+2\"],\n  \"countries\": [\"GS\"]\n}, {\n  \"value\": \"Mid-Atlantic Standard Time\",\n  \"abbr\": \"MDT\",\n  \"offset\": -1,\n  \"isdst\": true,\n  \"text\": \"(UTC-02:00) Mid-Atlantic - Old\",\n  \"utc\": [],\n  \"countries\": []\n}, {\n  \"value\": \"Azores Standard Time\",\n  \"abbr\": \"ADT\",\n  \"offset\": 0,\n  \"isdst\": true,\n  \"text\": \"(UTC-01:00) Azores\",\n  \"utc\": [\"America/Scoresbysund\", \"Atlantic/Azores\"],\n  \"countries\": [\"PT\"]\n}, {\n  \"value\": \"Cape Verde Standard Time\",\n  \"abbr\": \"CVST\",\n  \"offset\": -1,\n  \"isdst\": false,\n  \"text\": \"(UTC-01:00) Cape Verde Is.\",\n  \"utc\": [\"Atlantic/Cape_Verde\", \"Etc/GMT+1\"],\n  \"countries\": [\"CV\"]\n}, {\n  \"value\": \"Morocco Standard Time\",\n  \"abbr\": \"MDT\",\n  \"offset\": 1,\n  \"isdst\": true,\n  \"text\": \"(UTC) Casablanca\",\n  \"utc\": [\"Africa/Casablanca\", \"Africa/El_Aaiun\"],\n  \"countries\": [\"MA\"]\n}, {\n  \"value\": \"UTC\",\n  \"abbr\": \"UTC\",\n  \"offset\": 0,\n  \"isdst\": false,\n  \"text\": \"(UTC) Coordinated Universal Time\",\n  \"utc\": [\"America/Danmarkshavn\", \"Etc/GMT\"],\n  \"countries\": []\n}, {\n  \"value\": \"GMT Standard Time\",\n  \"abbr\": \"GMT\",\n  \"offset\": 0,\n  \"isdst\": false,\n  \"text\": \"(UTC) Edinburgh, London\",\n  \"utc\": [\"Etc/UTC\",\n  // default on backend\n  \"Europe/Isle_of_Man\", \"Europe/Guernsey\", \"Europe/Jersey\", \"Europe/London\"],\n  \"countries\": [\"GB\"]\n}, {\n  \"value\": \"GMT Standard Time\",\n  \"abbr\": \"GDT\",\n  \"offset\": 1,\n  \"isdst\": true,\n  \"text\": \"(UTC) Dublin, Lisbon\",\n  \"utc\": [\"Atlantic/Canary\", \"Atlantic/Faeroe\", \"Atlantic/Madeira\", \"Europe/Dublin\", \"Europe/Lisbon\"],\n  \"countries\": [\"IE\", \"PT\"]\n}, {\n  \"value\": \"Greenwich Standard Time\",\n  \"abbr\": \"GST\",\n  \"offset\": 0,\n  \"isdst\": false,\n  \"text\": \"(UTC) Monrovia, Reykjavik\",\n  \"utc\": [\"Africa/Abidjan\", \"Africa/Accra\", \"Africa/Bamako\", \"Africa/Banjul\", \"Africa/Bissau\", \"Africa/Conakry\", \"Africa/Dakar\", \"Africa/Freetown\", \"Africa/Lome\", \"Africa/Monrovia\", \"Africa/Nouakchott\", \"Africa/Ouagadougou\", \"Africa/Sao_Tome\", \"Atlantic/Reykjavik\", \"Atlantic/St_Helena\"],\n  \"countries\": [\"SH\"]\n}, {\n  \"value\": \"W. Europe Standard Time\",\n  \"abbr\": \"WEDT\",\n  \"offset\": 2,\n  \"isdst\": true,\n  \"text\": \"(UTC+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna\",\n  \"utc\": [\"Arctic/Longyearbyen\", \"Europe/Amsterdam\", \"Europe/Andorra\", \"Europe/Berlin\", \"Europe/Busingen\", \"Europe/Gibraltar\", \"Europe/Luxembourg\", \"Europe/Malta\", \"Europe/Monaco\", \"Europe/Oslo\", \"Europe/Rome\", \"Europe/San_Marino\", \"Europe/Stockholm\", \"Europe/Vaduz\", \"Europe/Vatican\", \"Europe/Vienna\", \"Europe/Zurich\"],\n  \"countries\": [\"AT\", \"BE\", \"CH\", \"DE\", \"IT\", \"LI\", \"LU\", \"NL\", \"NO\", \"SE\"]\n}, {\n  \"value\": \"Central Europe Standard Time\",\n  \"abbr\": \"CEDT\",\n  \"offset\": 2,\n  \"isdst\": true,\n  \"text\": \"(UTC+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague\",\n  \"utc\": [\"Europe/Belgrade\", \"Europe/Bratislava\", \"Europe/Budapest\", \"Europe/Ljubljana\", \"Europe/Podgorica\", \"Europe/Prague\", \"Europe/Tirane\"],\n  \"countries\": [\"AL\", \"BA\", \"CZ\", \"HU\", \"ME\", \"RS\", \"SK\", \"SI\"]\n}, {\n  \"value\": \"Romance Standard Time\",\n  \"abbr\": \"RDT\",\n  \"offset\": 2,\n  \"isdst\": true,\n  \"text\": \"(UTC+01:00) Brussels, Copenhagen, Madrid, Paris\",\n  \"utc\": [\"Africa/Ceuta\", \"Europe/Brussels\", \"Europe/Copenhagen\", \"Europe/Madrid\", \"Europe/Paris\"],\n  \"countries\": [\"BE\", \"DK\", \"FR\", \"ES\"]\n}, {\n  \"value\": \"Central European Standard Time\",\n  \"abbr\": \"CEDT\",\n  \"offset\": 2,\n  \"isdst\": true,\n  \"text\": \"(UTC+01:00) Sarajevo, Skopje, Warsaw, Zagreb\",\n  \"utc\": [\"Europe/Sarajevo\", \"Europe/Skopje\", \"Europe/Warsaw\", \"Europe/Zagreb\"],\n  \"countries\": [\"BA\", \"HR\", \"MK\", \"PL\"]\n}, {\n  \"value\": \"W. Central Africa Standard Time\",\n  \"abbr\": \"WCAST\",\n  \"offset\": 1,\n  \"isdst\": false,\n  \"text\": \"(UTC+01:00) West Central Africa\",\n  \"utc\": [\"Africa/Algiers\", \"Africa/Bangui\", \"Africa/Brazzaville\", \"Africa/Douala\", \"Africa/Kinshasa\", \"Africa/Lagos\", \"Africa/Libreville\", \"Africa/Luanda\", \"Africa/Malabo\", \"Africa/Ndjamena\", \"Africa/Niamey\", \"Africa/Porto-Novo\", \"Africa/Tunis\", \"Etc/GMT-1\"],\n  \"countries\": [\"AO\", \"BJ\", \"CD\", \"CF\", \"CG\", \"CM\", \"DZ\", \"GA\", \"GQ\", \"NE\", \"NG\", \"TN\"]\n}, {\n  \"value\": \"Namibia Standard Time\",\n  \"abbr\": \"NST\",\n  \"offset\": 1,\n  \"isdst\": false,\n  \"text\": \"(UTC+01:00) Windhoek\",\n  \"utc\": [\"Africa/Windhoek\"],\n  \"countries\": [\"NA\"]\n}, {\n  \"value\": \"GTB Standard Time\",\n  \"abbr\": \"GDT\",\n  \"offset\": 3,\n  \"isdst\": true,\n  \"text\": \"(UTC+02:00) Athens, Bucharest\",\n  \"utc\": [\"Asia/Nicosia\", \"Europe/Athens\", \"Europe/Bucharest\", \"Europe/Chisinau\"],\n  \"countries\": [\"CY\", \"GR\", \"RO\"]\n}, {\n  \"value\": \"Middle East Standard Time\",\n  \"abbr\": \"MEDT\",\n  \"offset\": 3,\n  \"isdst\": true,\n  \"text\": \"(UTC+02:00) Beirut\",\n  \"utc\": [\"Asia/Beirut\"],\n  \"countries\": [\"LB\"]\n}, {\n  \"value\": \"Egypt Standard Time\",\n  \"abbr\": \"EST\",\n  \"offset\": 2,\n  \"isdst\": false,\n  \"text\": \"(UTC+02:00) Cairo\",\n  \"utc\": [\"Africa/Cairo\"],\n  \"countries\": [\"EG\"]\n}, {\n  \"value\": \"Syria Standard Time\",\n  \"abbr\": \"SDT\",\n  \"offset\": 3,\n  \"isdst\": true,\n  \"text\": \"(UTC+02:00) Damascus\",\n  \"utc\": [\"Asia/Damascus\"],\n  \"countries\": [\"SY\"]\n}, {\n  \"value\": \"E. Europe Standard Time\",\n  \"abbr\": \"EEDT\",\n  \"offset\": 3,\n  \"isdst\": true,\n  \"text\": \"(UTC+02:00) E. Europe\",\n  \"utc\": [\"Asia/Nicosia\", \"Europe/Athens\", \"Europe/Bucharest\", \"Europe/Chisinau\", \"Europe/Helsinki\", \"Europe/Kiev\", \"Europe/Mariehamn\", \"Europe/Nicosia\", \"Europe/Riga\", \"Europe/Sofia\", \"Europe/Tallinn\", \"Europe/Uzhgorod\", \"Europe/Vilnius\", \"Europe/Zaporozhye\"],\n  \"countries\": [\"BG\", \"EE\", \"FI\", \"LV\", \"LT\", \"MD\", \"RO\", \"UA\"]\n}, {\n  \"value\": \"South Africa Standard Time\",\n  \"abbr\": \"SAST\",\n  \"offset\": 2,\n  \"isdst\": false,\n  \"text\": \"(UTC+02:00) Harare, Pretoria\",\n  \"utc\": [\"Africa/Blantyre\", \"Africa/Bujumbura\", \"Africa/Gaborone\", \"Africa/Harare\", \"Africa/Johannesburg\", \"Africa/Kigali\", \"Africa/Lubumbashi\", \"Africa/Lusaka\", \"Africa/Maputo\", \"Africa/Maseru\", \"Africa/Mbabane\", \"Etc/GMT-2\"],\n  \"countries\": [\"BW\", \"LS\", \"MW\", \"MZ\", \"RW\", \"ZA\", \"SZ\", \"ZM\", \"ZW\"]\n}, {\n  \"value\": \"FLE Standard Time\",\n  \"abbr\": \"FDT\",\n  \"offset\": 3,\n  \"isdst\": true,\n  \"text\": \"(UTC+02:00) Helsinki, Kyiv, Riga, Sofia, Tallinn, Vilnius\",\n  \"utc\": [\"Europe/Helsinki\", \"Europe/Kiev\", \"Europe/Mariehamn\", \"Europe/Riga\", \"Europe/Sofia\", \"Europe/Tallinn\", \"Europe/Uzhgorod\", \"Europe/Vilnius\", \"Europe/Zaporozhye\"],\n  \"countries\": [\"EE\", \"FI\", \"LV\", \"LT\", \"UA\"]\n}, {\n  \"value\": \"Turkey Standard Time\",\n  \"abbr\": \"TDT\",\n  \"offset\": 3,\n  \"isdst\": false,\n  \"text\": \"(UTC+03:00) Istanbul\",\n  \"utc\": [\"Europe/Istanbul\"],\n  \"countries\": [\"TR\"]\n}, {\n  \"value\": \"Israel Standard Time\",\n  \"abbr\": \"JDT\",\n  \"offset\": 3,\n  \"isdst\": true,\n  \"text\": \"(UTC+02:00) Jerusalem\",\n  \"utc\": [\"Asia/Jerusalem\"]\n}, {\n  \"value\": \"Libya Standard Time\",\n  \"abbr\": \"LST\",\n  \"offset\": 2,\n  \"isdst\": false,\n  \"text\": \"(UTC+02:00) Tripoli\",\n  \"utc\": [\"Africa/Tripoli\"]\n}, {\n  \"value\": \"Jordan Standard Time\",\n  \"abbr\": \"JST\",\n  \"offset\": 3,\n  \"isdst\": false,\n  \"text\": \"(UTC+03:00) Amman\",\n  \"utc\": [\"Asia/Amman\"]\n}, {\n  \"value\": \"Arabic Standard Time\",\n  \"abbr\": \"AST\",\n  \"offset\": 3,\n  \"isdst\": false,\n  \"text\": \"(UTC+03:00) Baghdad\",\n  \"utc\": [\"Asia/Baghdad\"]\n}, {\n  \"value\": \"Kaliningrad Standard Time\",\n  \"abbr\": \"KST\",\n  \"offset\": 3,\n  \"isdst\": false,\n  \"text\": \"(UTC+02:00) Kaliningrad\",\n  \"utc\": [\"Europe/Kaliningrad\"]\n}, {\n  \"value\": \"Arab Standard Time\",\n  \"abbr\": \"AST\",\n  \"offset\": 3,\n  \"isdst\": false,\n  \"text\": \"(UTC+03:00) Kuwait, Riyadh\",\n  \"utc\": [\"Asia/Aden\", \"Asia/Bahrain\", \"Asia/Kuwait\", \"Asia/Qatar\", \"Asia/Riyadh\"]\n}, {\n  \"value\": \"E. Africa Standard Time\",\n  \"abbr\": \"EAST\",\n  \"offset\": 3,\n  \"isdst\": false,\n  \"text\": \"(UTC+03:00) Nairobi\",\n  \"utc\": [\"Africa/Addis_Ababa\", \"Africa/Asmera\", \"Africa/Dar_es_Salaam\", \"Africa/Djibouti\", \"Africa/Juba\", \"Africa/Kampala\", \"Africa/Khartoum\", \"Africa/Mogadishu\", \"Africa/Nairobi\", \"Antarctica/Syowa\", \"Etc/GMT-3\", \"Indian/Antananarivo\", \"Indian/Comoro\", \"Indian/Mayotte\"],\n  \"countries\": [\"DJ\", \"ER\", \"ET\", \"KE\", \"KM\", \"MG\", \"SO\", \"TZ\", \"UG\", \"YT\"]\n}, {\n  \"value\": \"Moscow Standard Time\",\n  \"abbr\": \"MSK\",\n  \"offset\": 3,\n  \"isdst\": false,\n  \"text\": \"(UTC+03:00) Moscow, St. Petersburg, Volgograd, Minsk\",\n  \"utc\": [\"Europe/Kirov\", \"Europe/Moscow\", \"Europe/Simferopol\", \"Europe/Volgograd\", \"Europe/Minsk\"],\n  \"countries\": [\"BY\", \"RU\"]\n}, {\n  \"value\": \"Samara Time\",\n  \"abbr\": \"SAMT\",\n  \"offset\": 4,\n  \"isdst\": false,\n  \"text\": \"(UTC+04:00) Samara, Ulyanovsk, Saratov\",\n  \"utc\": [\"Europe/Astrakhan\", \"Europe/Samara\", \"Europe/Ulyanovsk\"],\n  \"countries\": [\"RU\"]\n}, {\n  \"value\": \"Iran Standard Time\",\n  \"abbr\": \"IDT\",\n  \"offset\": 4.5,\n  \"isdst\": true,\n  \"text\": \"(UTC+03:30) Tehran\",\n  \"utc\": [\"Asia/Tehran\"],\n  \"countries\": [\"IR\"]\n}, {\n  \"value\": \"Arabian Standard Time\",\n  \"abbr\": \"AST\",\n  \"offset\": 4,\n  \"isdst\": false,\n  \"text\": \"(UTC+04:00) Abu Dhabi, Muscat\",\n  \"utc\": [\"Asia/Dubai\", \"Asia/Muscat\", \"Etc/GMT-4\"],\n  \"countries\": [\"AE\", \"OM\"]\n}, {\n  \"value\": \"Azerbaijan Standard Time\",\n  \"abbr\": \"ADT\",\n  \"offset\": 5,\n  \"isdst\": true,\n  \"text\": \"(UTC+04:00) Baku\",\n  \"utc\": [\"Asia/Baku\"],\n  \"countries\": [\"AZ\"]\n}, {\n  \"value\": \"Mauritius Standard Time\",\n  \"abbr\": \"MST\",\n  \"offset\": 4,\n  \"isdst\": false,\n  \"text\": \"(UTC+04:00) Port Louis\",\n  \"utc\": [\"Indian/Mahe\", \"Indian/Mauritius\", \"Indian/Reunion\"],\n  \"countries\": [\"MU\"]\n}, {\n  \"value\": \"Georgian Standard Time\",\n  \"abbr\": \"GET\",\n  \"offset\": 4,\n  \"isdst\": false,\n  \"text\": \"(UTC+04:00) Tbilisi\",\n  \"utc\": [\"Asia/Tbilisi\"],\n  \"countries\": [\"GE\"]\n}, {\n  \"value\": \"Caucasus Standard Time\",\n  \"abbr\": \"CST\",\n  \"offset\": 4,\n  \"isdst\": false,\n  \"text\": \"(UTC+04:00) Yerevan\",\n  \"utc\": [\"Asia/Yerevan\"],\n  \"countries\": [\"AM\"]\n}, {\n  \"value\": \"Afghanistan Standard Time\",\n  \"abbr\": \"AST\",\n  \"offset\": 4.5,\n  \"isdst\": false,\n  \"text\": \"(UTC+04:30) Kabul\",\n  \"utc\": [\"Asia/Kabul\"],\n  \"countries\": [\"AF\"]\n}, {\n  \"value\": \"West Asia Standard Time\",\n  \"abbr\": \"WAST\",\n  \"offset\": 5,\n  \"isdst\": false,\n  \"text\": \"(UTC+05:00) Ashgabat, Tashkent\",\n  \"utc\": [\"Antarctica/Mawson\", \"Asia/Aqtau\", \"Asia/Aqtobe\", \"Asia/Ashgabat\", \"Asia/Dushanbe\", \"Asia/Oral\", \"Asia/Samarkand\", \"Asia/Tashkent\", \"Etc/GMT-5\", \"Indian/Kerguelen\", \"Indian/Maldives\"]\n}, {\n  \"value\": \"Yekaterinburg Time\",\n  \"abbr\": \"YEKT\",\n  \"offset\": 5,\n  \"isdst\": false,\n  \"text\": \"(UTC+05:00) Yekaterinburg\",\n  \"utc\": [\"Asia/Yekaterinburg\"]\n}, {\n  \"value\": \"Pakistan Standard Time\",\n  \"abbr\": \"PKT\",\n  \"offset\": 5,\n  \"isdst\": false,\n  \"text\": \"(UTC+05:00) Islamabad, Karachi\",\n  \"utc\": [\"Asia/Karachi\"]\n}, {\n  \"value\": \"India Standard Time\",\n  \"abbr\": \"IST\",\n  \"offset\": 5.5,\n  \"isdst\": false,\n  \"text\": \"(UTC+05:30) Chennai, Kolkata, Mumbai, New Delhi\",\n  \"utc\": [\"Asia/Kolkata\", \"Asia/Calcutta\"]\n}, {\n  \"value\": \"Sri Lanka Standard Time\",\n  \"abbr\": \"SLST\",\n  \"offset\": 5.5,\n  \"isdst\": false,\n  \"text\": \"(UTC+05:30) Sri Jayawardenepura\",\n  \"utc\": [\"Asia/Colombo\"]\n}, {\n  \"value\": \"Nepal Standard Time\",\n  \"abbr\": \"NST\",\n  \"offset\": 5.75,\n  \"isdst\": false,\n  \"text\": \"(UTC+05:45) Kathmandu\",\n  \"utc\": [\"Asia/Kathmandu\"]\n}, {\n  \"value\": \"Central Asia Standard Time\",\n  \"abbr\": \"CAST\",\n  \"offset\": 6,\n  \"isdst\": false,\n  \"text\": \"(UTC+06:00) Nur-Sultan (Astana)\",\n  \"utc\": [\"Antarctica/Vostok\", \"Asia/Almaty\", \"Asia/Bishkek\", \"Asia/Qyzylorda\", \"Asia/Urumqi\", \"Etc/GMT-6\", \"Indian/Chagos\"]\n}, {\n  \"value\": \"Bangladesh Standard Time\",\n  \"abbr\": \"BST\",\n  \"offset\": 6,\n  \"isdst\": false,\n  \"text\": \"(UTC+06:00) Dhaka\",\n  \"utc\": [\"Asia/Dhaka\", \"Asia/Thimphu\"]\n}, {\n  \"value\": \"Myanmar Standard Time\",\n  \"abbr\": \"MST\",\n  \"offset\": 6.5,\n  \"isdst\": false,\n  \"text\": \"(UTC+06:30) Yangon (Rangoon)\",\n  \"utc\": [\"Asia/Rangoon\", \"Indian/Cocos\"]\n}, {\n  \"value\": \"SE Asia Standard Time\",\n  \"abbr\": \"SAST\",\n  \"offset\": 7,\n  \"isdst\": false,\n  \"text\": \"(UTC+07:00) Bangkok, Hanoi, Jakarta\",\n  \"utc\": [\"Antarctica/Davis\", \"Asia/Bangkok\", \"Asia/Hovd\", \"Asia/Jakarta\", \"Asia/Phnom_Penh\", \"Asia/Pontianak\", \"Asia/Saigon\", \"Asia/Vientiane\", \"Etc/GMT-7\", \"Indian/Christmas\"],\n  \"countries\": [\"ID\", \"KH\", \"LA\", \"MY\", \"TH\", \"VN\"]\n}, {\n  \"value\": \"N. Central Asia Standard Time\",\n  \"abbr\": \"NCAST\",\n  \"offset\": 7,\n  \"isdst\": false,\n  \"text\": \"(UTC+07:00) Novosibirsk\",\n  \"utc\": [\"Asia/Novokuznetsk\", \"Asia/Novosibirsk\", \"Asia/Omsk\"],\n  \"countries\": [\"RU\"]\n}, {\n  \"value\": \"China Standard Time\",\n  \"abbr\": \"CST\",\n  \"offset\": 8,\n  \"isdst\": false,\n  \"text\": \"(UTC+08:00) Beijing, Chongqing, Hong Kong, Urumqi\",\n  \"utc\": [\"Asia/Hong_Kong\", \"Asia/Macau\", \"Asia/Shanghai\"],\n  \"countries\": [\"CN\"]\n}, {\n  \"value\": \"North Asia Standard Time\",\n  \"abbr\": \"NAST\",\n  \"offset\": 8,\n  \"isdst\": false,\n  \"text\": \"(UTC+08:00) Krasnoyarsk\",\n  \"utc\": [\"Asia/Krasnoyarsk\"],\n  \"countries\": [\"RU\"]\n}, {\n  \"value\": \"Singapore Standard Time\",\n  \"abbr\": \"MPST\",\n  \"offset\": 8,\n  \"isdst\": false,\n  \"text\": \"(UTC+08:00) Kuala Lumpur, Singapore\",\n  \"utc\": [\"Asia/Brunei\", \"Asia/Kuala_Lumpur\", \"Asia/Kuching\", \"Asia/Makassar\", \"Asia/Manila\", \"Asia/Singapore\", \"Etc/GMT-8\"],\n  \"countries\": [\"MY\", \"SG\"]\n}, {\n  \"value\": \"W. Australia Standard Time\",\n  \"abbr\": \"WAST\",\n  \"offset\": 8,\n  \"isdst\": false,\n  \"text\": \"(UTC+08:00) Perth\",\n  \"utc\": [\"Antarctica/Casey\", \"Australia/Perth\"],\n  \"countries\": [\"AU\"]\n}, {\n  \"value\": \"Taipei Standard Time\",\n  \"abbr\": \"TST\",\n  \"offset\": 8,\n  \"isdst\": false,\n  \"text\": \"(UTC+08:00) Taipei\",\n  \"utc\": [\"Asia/Taipei\"],\n  \"countries\": [\"TW\"]\n}, {\n  \"value\": \"Ulaanbaatar Standard Time\",\n  \"abbr\": \"UST\",\n  \"offset\": 8,\n  \"isdst\": false,\n  \"text\": \"(UTC+08:00) Ulaanbaatar\",\n  \"utc\": [\"Asia/Choibalsan\", \"Asia/Ulaanbaatar\"],\n  \"countries\": [\"MN\"]\n}, {\n  \"value\": \"North Asia East Standard Time\",\n  \"abbr\": \"NAEST\",\n  \"offset\": 8,\n  \"isdst\": false,\n  \"text\": \"(UTC+08:00) Irkutsk\",\n  \"utc\": [\"Asia/Irkutsk\"],\n  \"countries\": [\"RU\"]\n}, {\n  \"value\": \"Japan Standard Time\",\n  \"abbr\": \"JST\",\n  \"offset\": 9,\n  \"isdst\": false,\n  \"text\": \"(UTC+09:00) Osaka, Sapporo, Tokyo\",\n  \"utc\": [\"Asia/Dili\", \"Asia/Jayapura\", \"Asia/Tokyo\", \"Etc/GMT-9\", \"Pacific/Palau\"],\n  \"countries\": [\"JP\"]\n}, {\n  \"value\": \"Korea Standard Time\",\n  \"abbr\": \"KST\",\n  \"offset\": 9,\n  \"isdst\": false,\n  \"text\": \"(UTC+09:00) Seoul\",\n  \"utc\": [\"Asia/Pyongyang\", \"Asia/Seoul\"],\n  \"countries\": [\"KR\"]\n}, {\n  \"value\": \"Cen. Australia Standard Time\",\n  \"abbr\": \"CAST\",\n  \"offset\": 9.5,\n  \"isdst\": false,\n  \"text\": \"(UTC+09:30) Adelaide\",\n  \"utc\": [\"Australia/Adelaide\", \"Australia/Broken_Hill\"],\n  \"countries\": [\"AU\"]\n}, {\n  \"value\": \"AUS Central Standard Time\",\n  \"abbr\": \"ACST\",\n  \"offset\": 9.5,\n  \"isdst\": false,\n  \"text\": \"(UTC+09:30) Darwin\",\n  \"utc\": [\"Australia/Darwin\"],\n  \"countries\": [\"AU\"]\n}, {\n  \"value\": \"E. Australia Standard Time\",\n  \"abbr\": \"EAST\",\n  \"offset\": 10,\n  \"isdst\": false,\n  \"text\": \"(UTC+10:00) Brisbane\",\n  \"utc\": [\"Australia/Brisbane\", \"Australia/Lindeman\"],\n  \"countries\": [\"AU\"]\n}, {\n  \"value\": \"AUS Eastern Standard Time\",\n  \"abbr\": \"AEST\",\n  \"offset\": 10,\n  \"isdst\": false,\n  \"text\": \"(UTC+10:00) Canberra, Melbourne, Sydney\",\n  \"utc\": [\"Australia/Melbourne\", \"Australia/Sydney\"],\n  \"countries\": [\"AU\"]\n}, {\n  \"value\": \"West Pacific Standard Time\",\n  \"abbr\": \"WPST\",\n  \"offset\": 10,\n  \"isdst\": false,\n  \"text\": \"(UTC+10:00) Guam, Port Moresby\",\n  \"utc\": [\"Antarctica/DumontDUrville\", \"Etc/GMT-10\", \"Pacific/Guam\", \"Pacific/Port_Moresby\", \"Pacific/Saipan\", \"Pacific/Truk\"],\n  \"countries\": [\"PG\"]\n}, {\n  \"value\": \"Tasmania Standard Time\",\n  \"abbr\": \"TST\",\n  \"offset\": 10,\n  \"isdst\": false,\n  \"text\": \"(UTC+10:00) Hobart\",\n  \"utc\": [\"Australia/Currie\", \"Australia/Hobart\"],\n  \"countries\": [\"AU\"]\n}, {\n  \"value\": \"Yakutsk Standard Time\",\n  \"abbr\": \"YST\",\n  \"offset\": 9,\n  \"isdst\": false,\n  \"text\": \"(UTC+09:00) Yakutsk\",\n  \"utc\": [\"Asia/Chita\", \"Asia/Khandyga\", \"Asia/Yakutsk\"],\n  \"countries\": [\"RU\"]\n}, {\n  \"value\": \"Central Pacific Standard Time\",\n  \"abbr\": \"CPST\",\n  \"offset\": 11,\n  \"isdst\": false,\n  \"text\": \"(UTC+11:00) Solomon Is., New Caledonia\",\n  \"utc\": [\"Antarctica/Macquarie\", \"Etc/GMT-11\", \"Pacific/Efate\", \"Pacific/Guadalcanal\", \"Pacific/Kosrae\", \"Pacific/Noumea\", \"Pacific/Ponape\"],\n  \"countries\": [\"NC\", \"SB\"]\n}, {\n  \"value\": \"Vladivostok Standard Time\",\n  \"abbr\": \"VST\",\n  \"offset\": 11,\n  \"isdst\": false,\n  \"text\": \"(UTC+11:00) Vladivostok\",\n  \"utc\": [\"Asia/Sakhalin\", \"Asia/Ust-Nera\", \"Asia/Vladivostok\"],\n  \"countries\": [\"RU\"]\n}, {\n  \"value\": \"New Zealand Standard Time\",\n  \"abbr\": \"NZST\",\n  \"offset\": 12,\n  \"isdst\": false,\n  \"text\": \"(UTC+12:00) Auckland, Wellington\",\n  \"utc\": [\"Antarctica/McMurdo\", \"Pacific/Auckland\"],\n  \"countries\": [\"NZ\"]\n}, {\n  \"value\": \"UTC+12\",\n  \"abbr\": \"U\",\n  \"offset\": 12,\n  \"isdst\": false,\n  \"text\": \"(UTC+12:00) Coordinated Universal Time+12\",\n  \"utc\": [\"Etc/GMT-12\", \"Pacific/Funafuti\", \"Pacific/Kwajalein\", \"Pacific/Majuro\", \"Pacific/Nauru\", \"Pacific/Tarawa\", \"Pacific/Wake\", \"Pacific/Wallis\"]\n}, {\n  \"value\": \"Fiji Standard Time\",\n  \"abbr\": \"FST\",\n  \"offset\": 12,\n  \"isdst\": false,\n  \"text\": \"(UTC+12:00) Fiji\",\n  \"utc\": [\"Pacific/Fiji\"],\n  \"countries\": [\"FJ\"]\n}, {\n  \"value\": \"Magadan Standard Time\",\n  \"abbr\": \"MST\",\n  \"offset\": 12,\n  \"isdst\": false,\n  \"text\": \"(UTC+12:00) Magadan\",\n  \"utc\": [\"Asia/Anadyr\", \"Asia/Kamchatka\", \"Asia/Magadan\", \"Asia/Srednekolymsk\"],\n  \"countries\": [\"RU\"]\n}, {\n  \"value\": \"Kamchatka Standard Time\",\n  \"abbr\": \"KDT\",\n  \"offset\": 13,\n  \"isdst\": true,\n  \"text\": \"(UTC+12:00) Petropavlovsk-Kamchatsky - Old\",\n  \"utc\": [\"Asia/Kamchatka\"],\n  \"countries\": [\"RU\"]\n}, {\n  \"value\": \"Tonga Standard Time\",\n  \"abbr\": \"TST\",\n  \"offset\": 13,\n  \"isdst\": false,\n  \"text\": \"(UTC+13:00) Nuku'alofa\",\n  \"utc\": [\"Etc/GMT-13\", \"Pacific/Enderbury\", \"Pacific/Fakaofo\", \"Pacific/Tongatapu\"],\n  \"countries\": [\"TO\"]\n}, {\n  \"value\": \"Samoa Standard Time\",\n  \"abbr\": \"SST\",\n  \"offset\": 13,\n  \"isdst\": false,\n  \"text\": \"(UTC+13:00) Samoa\",\n  \"utc\": [\"Pacific/Apia\"],\n  \"countries\": [\"WS\"]\n}];\nexport let phoneCodes = [{\n  \"country\": \"Afghanistan\",\n  \"code\": \"+93\",\n  \"iso\": \"AF\"\n}, {\n  \"country\": \"Albania\",\n  \"code\": \"+355\",\n  \"iso\": \"AL\"\n}, {\n  \"country\": \"Algeria\",\n  \"code\": \"+213\",\n  \"iso\": \"DZ\"\n}, {\n  \"country\": \"American Samoa\",\n  \"code\": \"******\",\n  \"iso\": \"AS\"\n}, {\n  \"country\": \"Andorra\",\n  \"code\": \"+376\",\n  \"iso\": \"AD\"\n}, {\n  \"country\": \"Angola\",\n  \"code\": \"+244\",\n  \"iso\": \"AO\"\n}, {\n  \"country\": \"Anguilla\",\n  \"code\": \"******\",\n  \"iso\": \"AI\"\n}, {\n  \"country\": \"Antarctica\",\n  \"code\": \"+672\",\n  \"iso\": \"AQ\"\n}, {\n  \"country\": \"Antigua and Barbuda\",\n  \"code\": \"******\",\n  \"iso\": \"AG\"\n}, {\n  \"country\": \"Argentina\",\n  \"code\": \"+54\",\n  \"iso\": \"AR\"\n}, {\n  \"country\": \"Armenia\",\n  \"code\": \"+374\",\n  \"iso\": \"AM\"\n}, {\n  \"country\": \"Aruba\",\n  \"code\": \"+297\",\n  \"iso\": \"AW\"\n}, {\n  \"country\": \"Australia\",\n  \"code\": \"+61\",\n  \"iso\": \"AU\"\n}, {\n  \"country\": \"Austria\",\n  \"code\": \"+43\",\n  \"iso\": \"AT\"\n}, {\n  \"country\": \"Azerbaijan\",\n  \"code\": \"+994\",\n  \"iso\": \"AZ\"\n}, {\n  \"country\": \"Bahamas\",\n  \"code\": \"+1-242\",\n  \"iso\": \"BS\"\n}, {\n  \"country\": \"Bahrain\",\n  \"code\": \"+973\",\n  \"iso\": \"BH\"\n}, {\n  \"country\": \"Bangladesh\",\n  \"code\": \"+880\",\n  \"iso\": \"BD\"\n}, {\n  \"country\": \"Barbados\",\n  \"code\": \"+1-246\",\n  \"iso\": \"BB\"\n}, {\n  \"country\": \"Belarus\",\n  \"code\": \"+375\",\n  \"iso\": \"BY\"\n}, {\n  \"country\": \"Belgium\",\n  \"code\": \"+32\",\n  \"iso\": \"BE\"\n}, {\n  \"country\": \"Belize\",\n  \"code\": \"+501\",\n  \"iso\": \"BZ\"\n}, {\n  \"country\": \"Benin\",\n  \"code\": \"+229\",\n  \"iso\": \"BJ\"\n}, {\n  \"country\": \"Bermuda\",\n  \"code\": \"+1-441\",\n  \"iso\": \"BM\"\n}, {\n  \"country\": \"Bhutan\",\n  \"code\": \"+975\",\n  \"iso\": \"BT\"\n}, {\n  \"country\": \"Bolivia\",\n  \"code\": \"+591\",\n  \"iso\": \"BO\"\n}, {\n  \"country\": \"Bosnia and Herzegovina\",\n  \"code\": \"+387\",\n  \"iso\": \"BA\"\n}, {\n  \"country\": \"Botswana\",\n  \"code\": \"+267\",\n  \"iso\": \"BW\"\n}, {\n  \"country\": \"Brazil\",\n  \"code\": \"+55\",\n  \"iso\": \"BR\"\n}, {\n  \"country\": \"British Indian Ocean Territory\",\n  \"code\": \"+246\",\n  \"iso\": \"IO\"\n}, {\n  \"country\": \"British Virgin Islands\",\n  \"code\": \"+1-284\",\n  \"iso\": \"VG\"\n}, {\n  \"country\": \"Brunei\",\n  \"code\": \"+673\",\n  \"iso\": \"BN\"\n}, {\n  \"country\": \"Bulgaria\",\n  \"code\": \"+359\",\n  \"iso\": \"BG\"\n}, {\n  \"country\": \"Burkina Faso\",\n  \"code\": \"+226\",\n  \"iso\": \"BF\"\n}, {\n  \"country\": \"Burundi\",\n  \"code\": \"+257\",\n  \"iso\": \"BI\"\n}, {\n  \"country\": \"Cambodia\",\n  \"code\": \"+855\",\n  \"iso\": \"KH\"\n}, {\n  \"country\": \"Cameroon\",\n  \"code\": \"+237\",\n  \"iso\": \"CM\"\n}, {\n  \"country\": \"Canada\",\n  \"code\": \"+1\",\n  \"iso\": \"CA\"\n}, {\n  \"country\": \"Cape Verde\",\n  \"code\": \"+238\",\n  \"iso\": \"CV\"\n}, {\n  \"country\": \"Cayman Islands\",\n  \"code\": \"+1-345\",\n  \"iso\": \"KY\"\n}, {\n  \"country\": \"Central African Republic\",\n  \"code\": \"+236\",\n  \"iso\": \"CF\"\n}, {\n  \"country\": \"Chad\",\n  \"code\": \"+235\",\n  \"iso\": \"TD\"\n}, {\n  \"country\": \"Chile\",\n  \"code\": \"+56\",\n  \"iso\": \"CL\"\n}, {\n  \"country\": \"China\",\n  \"code\": \"+86\",\n  \"iso\": \"CN\"\n}, {\n  \"country\": \"Christmas Island\",\n  \"code\": \"+61\",\n  \"iso\": \"CX\"\n}, {\n  \"country\": \"Cocos Islands\",\n  \"code\": \"+61\",\n  \"iso\": \"CC\"\n}, {\n  \"country\": \"Colombia\",\n  \"code\": \"+57\",\n  \"iso\": \"CO\"\n}, {\n  \"country\": \"Comoros\",\n  \"code\": \"+269\",\n  \"iso\": \"KM\"\n}, {\n  \"country\": \"Cook Islands\",\n  \"code\": \"+682\",\n  \"iso\": \"CK\"\n}, {\n  \"country\": \"Costa Rica\",\n  \"code\": \"+506\",\n  \"iso\": \"CR\"\n}, {\n  \"country\": \"Croatia\",\n  \"code\": \"+385\",\n  \"iso\": \"HR\"\n}, {\n  \"country\": \"Cuba\",\n  \"code\": \"+53\",\n  \"iso\": \"CU\"\n}, {\n  \"country\": \"Curacao\",\n  \"code\": \"+599\",\n  \"iso\": \"CW\"\n}, {\n  \"country\": \"Cyprus\",\n  \"code\": \"+357\",\n  \"iso\": \"CY\"\n}, {\n  \"country\": \"Czech Republic\",\n  \"code\": \"+420\",\n  \"iso\": \"CZ\"\n}, {\n  \"country\": \"Democratic Republic of the Congo\",\n  \"code\": \"+243\",\n  \"iso\": \"CD\"\n}, {\n  \"country\": \"Denmark\",\n  \"code\": \"+45\",\n  \"iso\": \"DK\"\n}, {\n  \"country\": \"Djibouti\",\n  \"code\": \"+253\",\n  \"iso\": \"DJ\"\n}, {\n  \"country\": \"Dominica\",\n  \"code\": \"+1-767\",\n  \"iso\": \"DM\"\n}, {\n  \"country\": \"Dominican Republic\",\n  \"code\": \"+1\",\n  \"iso\": \"DO\"\n}, {\n  \"country\": \"East Timor\",\n  \"code\": \"+670\",\n  \"iso\": \"TL\"\n}, {\n  \"country\": \"Ecuador\",\n  \"code\": \"+593\",\n  \"iso\": \"EC\"\n}, {\n  \"country\": \"Egypt\",\n  \"code\": \"+20\",\n  \"iso\": \"EG\"\n}, {\n  \"country\": \"El Salvador\",\n  \"code\": \"+503\",\n  \"iso\": \"SV\"\n}, {\n  \"country\": \"Equatorial Guinea\",\n  \"code\": \"+240\",\n  \"iso\": \"GQ\"\n}, {\n  \"country\": \"Eritrea\",\n  \"code\": \"+291\",\n  \"iso\": \"ER\"\n}, {\n  \"country\": \"Estonia\",\n  \"code\": \"+372\",\n  \"iso\": \"EE\"\n}, {\n  \"country\": \"Ethiopia\",\n  \"code\": \"+251\",\n  \"iso\": \"ET\"\n}, {\n  \"country\": \"Falkland Islands\",\n  \"code\": \"+500\",\n  \"iso\": \"FK\"\n}, {\n  \"country\": \"Faroe Islands\",\n  \"code\": \"+298\",\n  \"iso\": \"FO\"\n}, {\n  \"country\": \"Fiji\",\n  \"code\": \"+679\",\n  \"iso\": \"FJ\"\n}, {\n  \"country\": \"Finland\",\n  \"code\": \"+358\",\n  \"iso\": \"FI\"\n}, {\n  \"country\": \"France\",\n  \"code\": \"+33\",\n  \"iso\": \"FR\"\n}, {\n  \"country\": \"French Polynesia\",\n  \"code\": \"+689\",\n  \"iso\": \"PF\"\n}, {\n  \"country\": \"Gabon\",\n  \"code\": \"+241\",\n  \"iso\": \"GA\"\n}, {\n  \"country\": \"Gambia\",\n  \"code\": \"+220\",\n  \"iso\": \"GM\"\n}, {\n  \"country\": \"Georgia\",\n  \"code\": \"+995\",\n  \"iso\": \"GE\"\n}, {\n  \"country\": \"Germany\",\n  \"code\": \"+49\",\n  \"iso\": \"DE\"\n}, {\n  \"country\": \"Ghana\",\n  \"code\": \"+233\",\n  \"iso\": \"GH\"\n}, {\n  \"country\": \"Gibraltar\",\n  \"code\": \"+350\",\n  \"iso\": \"GI\"\n}, {\n  \"country\": \"Greece\",\n  \"code\": \"+30\",\n  \"iso\": \"GR\"\n}, {\n  \"country\": \"Greenland\",\n  \"code\": \"+299\",\n  \"iso\": \"GL\"\n}, {\n  \"country\": \"Grenada\",\n  \"code\": \"+1-473\",\n  \"iso\": \"GD\"\n}, {\n  \"country\": \"Guam\",\n  \"code\": \"+1-671\",\n  \"iso\": \"GU\"\n}, {\n  \"country\": \"Guatemala\",\n  \"code\": \"+502\",\n  \"iso\": \"GT\"\n}, {\n  \"country\": \"Guernsey\",\n  \"code\": \"+44-1481\",\n  \"iso\": \"GG\"\n}, {\n  \"country\": \"Guinea\",\n  \"code\": \"+224\",\n  \"iso\": \"GN\"\n}, {\n  \"country\": \"Guinea-Bissau\",\n  \"code\": \"+245\",\n  \"iso\": \"GW\"\n}, {\n  \"country\": \"Guyana\",\n  \"code\": \"+592\",\n  \"iso\": \"GY\"\n}, {\n  \"country\": \"Haiti\",\n  \"code\": \"+509\",\n  \"iso\": \"HT\"\n}, {\n  \"country\": \"Honduras\",\n  \"code\": \"+504\",\n  \"iso\": \"HN\"\n}, {\n  \"country\": \"Hong Kong\",\n  \"code\": \"+852\",\n  \"iso\": \"HK\"\n}, {\n  \"country\": \"Hungary\",\n  \"code\": \"+36\",\n  \"iso\": \"HU\"\n}, {\n  \"country\": \"Iceland\",\n  \"code\": \"+354\",\n  \"iso\": \"IS\"\n}, {\n  \"country\": \"India\",\n  \"code\": \"+91\",\n  \"iso\": \"IN\"\n}, {\n  \"country\": \"Indonesia\",\n  \"code\": \"+62\",\n  \"iso\": \"ID\"\n}, {\n  \"country\": \"Iran\",\n  \"code\": \"+98\",\n  \"iso\": \"IR\"\n}, {\n  \"country\": \"Iraq\",\n  \"code\": \"+964\",\n  \"iso\": \"IQ\"\n}, {\n  \"country\": \"Ireland\",\n  \"code\": \"+353\",\n  \"iso\": \"IE\"\n}, {\n  \"country\": \"Isle of Man\",\n  \"code\": \"+44-1624\",\n  \"iso\": \"IM\"\n}, {\n  \"country\": \"Israel\",\n  \"code\": \"+972\",\n  \"iso\": \"IL\"\n}, {\n  \"country\": \"Italy\",\n  \"code\": \"+39\",\n  \"iso\": \"IT\"\n}, {\n  \"country\": \"Ivory Coast\",\n  \"code\": \"+225\",\n  \"iso\": \"CI\"\n}, {\n  \"country\": \"Jamaica\",\n  \"code\": \"+1-876\",\n  \"iso\": \"JM\"\n}, {\n  \"country\": \"Japan\",\n  \"code\": \"+81\",\n  \"iso\": \"JP\"\n}, {\n  \"country\": \"Jersey\",\n  \"code\": \"+44-1534\",\n  \"iso\": \"JE\"\n}, {\n  \"country\": \"Jordan\",\n  \"code\": \"+962\",\n  \"iso\": \"JO\"\n}, {\n  \"country\": \"Kazakhstan\",\n  \"code\": \"+7\",\n  \"iso\": \"KZ\"\n}, {\n  \"country\": \"Kenya\",\n  \"code\": \"+254\",\n  \"iso\": \"KE\"\n}, {\n  \"country\": \"Kiribati\",\n  \"code\": \"+686\",\n  \"iso\": \"KI\"\n}, {\n  \"country\": \"Kosovo\",\n  \"code\": \"+383\",\n  \"iso\": \"XK\"\n}, {\n  \"country\": \"Kuwait\",\n  \"code\": \"+965\",\n  \"iso\": \"KW\"\n}, {\n  \"country\": \"Kyrgyzstan\",\n  \"code\": \"+996\",\n  \"iso\": \"KG\"\n}, {\n  \"country\": \"Laos\",\n  \"code\": \"+856\",\n  \"iso\": \"LA\"\n}, {\n  \"country\": \"Latvia\",\n  \"code\": \"+371\",\n  \"iso\": \"LV\"\n}, {\n  \"country\": \"Lebanon\",\n  \"code\": \"+961\",\n  \"iso\": \"LB\"\n}, {\n  \"country\": \"Lesotho\",\n  \"code\": \"+266\",\n  \"iso\": \"LS\"\n}, {\n  \"country\": \"Liberia\",\n  \"code\": \"+231\",\n  \"iso\": \"LR\"\n}, {\n  \"country\": \"Libya\",\n  \"code\": \"+218\",\n  \"iso\": \"LY\"\n}, {\n  \"country\": \"Liechtenstein\",\n  \"code\": \"+423\",\n  \"iso\": \"LI\"\n}, {\n  \"country\": \"Lithuania\",\n  \"code\": \"+370\",\n  \"iso\": \"LT\"\n}, {\n  \"country\": \"Luxembourg\",\n  \"code\": \"+352\",\n  \"iso\": \"LU\"\n}, {\n  \"country\": \"Macao\",\n  \"code\": \"+853\",\n  \"iso\": \"MO\"\n}, {\n  \"country\": \"Macedonia\",\n  \"code\": \"+389\",\n  \"iso\": \"MK\"\n}, {\n  \"country\": \"Madagascar\",\n  \"code\": \"+261\",\n  \"iso\": \"MG\"\n}, {\n  \"country\": \"Malawi\",\n  \"code\": \"+265\",\n  \"iso\": \"MW\"\n}, {\n  \"country\": \"Malaysia\",\n  \"code\": \"+60\",\n  \"iso\": \"MY\"\n}, {\n  \"country\": \"Maldives\",\n  \"code\": \"+960\",\n  \"iso\": \"MV\"\n}, {\n  \"country\": \"Mali\",\n  \"code\": \"+223\",\n  \"iso\": \"ML\"\n}, {\n  \"country\": \"Malta\",\n  \"code\": \"+356\",\n  \"iso\": \"MT\"\n}, {\n  \"country\": \"Marshall Islands\",\n  \"code\": \"+692\",\n  \"iso\": \"MH\"\n}, {\n  \"country\": \"Mauritania\",\n  \"code\": \"+222\",\n  \"iso\": \"MR\"\n}, {\n  \"country\": \"Mauritius\",\n  \"code\": \"+230\",\n  \"iso\": \"MU\"\n}, {\n  \"country\": \"Mayotte\",\n  \"code\": \"+262\",\n  \"iso\": \"YT\"\n}, {\n  \"country\": \"Mexico\",\n  \"code\": \"+52\",\n  \"iso\": \"MX\"\n}, {\n  \"country\": \"Micronesia\",\n  \"code\": \"+691\",\n  \"iso\": \"FM\"\n}, {\n  \"country\": \"Moldova\",\n  \"code\": \"+373\",\n  \"iso\": \"MD\"\n}, {\n  \"country\": \"Monaco\",\n  \"code\": \"+377\",\n  \"iso\": \"MC\"\n}, {\n  \"country\": \"Mongolia\",\n  \"code\": \"+976\",\n  \"iso\": \"MN\"\n}, {\n  \"country\": \"Montenegro\",\n  \"code\": \"+382\",\n  \"iso\": \"ME\"\n}, {\n  \"country\": \"Montserrat\",\n  \"code\": \"+1-664\",\n  \"iso\": \"MS\"\n}, {\n  \"country\": \"Morocco\",\n  \"code\": \"+212\",\n  \"iso\": \"MA\"\n}, {\n  \"country\": \"Mozambique\",\n  \"code\": \"+258\",\n  \"iso\": \"MZ\"\n}, {\n  \"country\": \"Myanmar\",\n  \"code\": \"+95\",\n  \"iso\": \"MM\"\n}, {\n  \"country\": \"Namibia\",\n  \"code\": \"+264\",\n  \"iso\": \"NA\"\n}, {\n  \"country\": \"Nauru\",\n  \"code\": \"+674\",\n  \"iso\": \"NR\"\n}, {\n  \"country\": \"Nepal\",\n  \"code\": \"+977\",\n  \"iso\": \"NP\"\n}, {\n  \"country\": \"Netherlands\",\n  \"code\": \"+31\",\n  \"iso\": \"NL\"\n}, {\n  \"country\": \"Netherlands Antilles\",\n  \"code\": \"+599\",\n  \"iso\": \"AN\"\n}, {\n  \"country\": \"New Caledonia\",\n  \"code\": \"+687\",\n  \"iso\": \"NC\"\n}, {\n  \"country\": \"New Zealand\",\n  \"code\": \"+64\",\n  \"iso\": \"NZ\"\n}, {\n  \"country\": \"Nicaragua\",\n  \"code\": \"+505\",\n  \"iso\": \"NI\"\n}, {\n  \"country\": \"Niger\",\n  \"code\": \"+227\",\n  \"iso\": \"NE\"\n}, {\n  \"country\": \"Nigeria\",\n  \"code\": \"+234\",\n  \"iso\": \"NG\"\n}, {\n  \"country\": \"Niue\",\n  \"code\": \"+683\",\n  \"iso\": \"NU\"\n}, {\n  \"country\": \"North Korea\",\n  \"code\": \"+850\",\n  \"iso\": \"KP\"\n}, {\n  \"country\": \"Northern Mariana Islands\",\n  \"code\": \"+1-670\",\n  \"iso\": \"MP\"\n}, {\n  \"country\": \"Norway\",\n  \"code\": \"+47\",\n  \"iso\": \"NO\"\n}, {\n  \"country\": \"Oman\",\n  \"code\": \"+968\",\n  \"iso\": \"OM\"\n}, {\n  \"country\": \"Pakistan\",\n  \"code\": \"+92\",\n  \"iso\": \"PK\"\n}, {\n  \"country\": \"Palau\",\n  \"code\": \"+680\",\n  \"iso\": \"PW\"\n}, {\n  \"country\": \"Palestine\",\n  \"code\": \"+970\",\n  \"iso\": \"PS\"\n}, {\n  \"country\": \"Panama\",\n  \"code\": \"+507\",\n  \"iso\": \"PA\"\n}, {\n  \"country\": \"Papua New Guinea\",\n  \"code\": \"+675\",\n  \"iso\": \"PG\"\n}, {\n  \"country\": \"Paraguay\",\n  \"code\": \"+595\",\n  \"iso\": \"PY\"\n}, {\n  \"country\": \"Peru\",\n  \"code\": \"+51\",\n  \"iso\": \"PE\"\n}, {\n  \"country\": \"Philippines\",\n  \"code\": \"+63\",\n  \"iso\": \"PH\"\n}, {\n  \"country\": \"Pitcairn\",\n  \"code\": \"+64\",\n  \"iso\": \"PN\"\n}, {\n  \"country\": \"Poland\",\n  \"code\": \"+48\",\n  \"iso\": \"PL\"\n}, {\n  \"country\": \"Portugal\",\n  \"code\": \"+351\",\n  \"iso\": \"PT\"\n}, {\n  \"country\": \"Puerto Rico\",\n  \"code\": \"+1-787, 1-939\",\n  \"iso\": \"PR\"\n}, {\n  \"country\": \"Qatar\",\n  \"code\": \"+974\",\n  \"iso\": \"QA\"\n}, {\n  \"country\": \"Republic of the Congo\",\n  \"code\": \"+242\",\n  \"iso\": \"CG\"\n}, {\n  \"country\": \"Reunion\",\n  \"code\": \"+262\",\n  \"iso\": \"RE\"\n}, {\n  \"country\": \"Romania\",\n  \"code\": \"+40\",\n  \"iso\": \"RO\"\n}, {\n  \"country\": \"Russia\",\n  \"code\": \"+7\",\n  \"iso\": \"RU\"\n}, {\n  \"country\": \"Rwanda\",\n  \"code\": \"+250\",\n  \"iso\": \"RW\"\n}, {\n  \"country\": \"Saint Barthelemy\",\n  \"code\": \"+590\",\n  \"iso\": \"BL\"\n}, {\n  \"country\": \"Saint Helena\",\n  \"code\": \"+290\",\n  \"iso\": \"SH\"\n}, {\n  \"country\": \"Saint Kitts and Nevis\",\n  \"code\": \"+1-869\",\n  \"iso\": \"KN\"\n}, {\n  \"country\": \"Saint Lucia\",\n  \"code\": \"+1-758\",\n  \"iso\": \"LC\"\n}, {\n  \"country\": \"Saint Martin\",\n  \"code\": \"+590\",\n  \"iso\": \"MF\"\n}, {\n  \"country\": \"Saint Pierre and Miquelon\",\n  \"code\": \"+508\",\n  \"iso\": \"PM\"\n}, {\n  \"country\": \"Saint Vincent and the Grenadines\",\n  \"code\": \"+1-784\",\n  \"iso\": \"VC\"\n}, {\n  \"country\": \"Samoa\",\n  \"code\": \"+685\",\n  \"iso\": \"WS\"\n}, {\n  \"country\": \"San Marino\",\n  \"code\": \"+378\",\n  \"iso\": \"SM\"\n}, {\n  \"country\": \"Sao Tome and Principe\",\n  \"code\": \"+239\",\n  \"iso\": \"ST\"\n}, {\n  \"country\": \"Saudi Arabia\",\n  \"code\": \"+966\",\n  \"iso\": \"SA\"\n}, {\n  \"country\": \"Senegal\",\n  \"code\": \"+221\",\n  \"iso\": \"SN\"\n}, {\n  \"country\": \"Serbia\",\n  \"code\": \"+381\",\n  \"iso\": \"RS\"\n}, {\n  \"country\": \"Seychelles\",\n  \"code\": \"+248\",\n  \"iso\": \"SC\"\n}, {\n  \"country\": \"Sierra Leone\",\n  \"code\": \"+232\",\n  \"iso\": \"SL\"\n}, {\n  \"country\": \"Singapore\",\n  \"code\": \"+65\",\n  \"iso\": \"SG\"\n}, {\n  \"country\": \"Sint Maarten\",\n  \"code\": \"+1-721\",\n  \"iso\": \"SX\"\n}, {\n  \"country\": \"Slovakia\",\n  \"code\": \"+421\",\n  \"iso\": \"SK\"\n}, {\n  \"country\": \"Slovenia\",\n  \"code\": \"+386\",\n  \"iso\": \"SI\"\n}, {\n  \"country\": \"Solomon Islands\",\n  \"code\": \"+677\",\n  \"iso\": \"SB\"\n}, {\n  \"country\": \"Somalia\",\n  \"code\": \"+252\",\n  \"iso\": \"SO\"\n}, {\n  \"country\": \"South Africa\",\n  \"code\": \"+27\",\n  \"iso\": \"ZA\"\n}, {\n  \"country\": \"South Korea\",\n  \"code\": \"+82\",\n  \"iso\": \"KR\"\n}, {\n  \"country\": \"South Sudan\",\n  \"code\": \"+211\",\n  \"iso\": \"SS\"\n}, {\n  \"country\": \"Spain\",\n  \"code\": \"+34\",\n  \"iso\": \"ES\"\n}, {\n  \"country\": \"Sri Lanka\",\n  \"code\": \"+94\",\n  \"iso\": \"LK\"\n}, {\n  \"country\": \"Sudan\",\n  \"code\": \"+249\",\n  \"iso\": \"SD\"\n}, {\n  \"country\": \"Suriname\",\n  \"code\": \"+597\",\n  \"iso\": \"SR\"\n}, {\n  \"country\": \"Svalbard and Jan Mayen\",\n  \"code\": \"+47\",\n  \"iso\": \"SJ\"\n}, {\n  \"country\": \"Swaziland\",\n  \"code\": \"+268\",\n  \"iso\": \"SZ\"\n}, {\n  \"country\": \"Sweden\",\n  \"code\": \"+46\",\n  \"iso\": \"SE\"\n}, {\n  \"country\": \"Switzerland\",\n  \"code\": \"+41\",\n  \"iso\": \"CH\"\n}, {\n  \"country\": \"Syria\",\n  \"code\": \"+963\",\n  \"iso\": \"SY\"\n}, {\n  \"country\": \"Taiwan\",\n  \"code\": \"+886\",\n  \"iso\": \"TW\"\n}, {\n  \"country\": \"Tajikistan\",\n  \"code\": \"+992\",\n  \"iso\": \"TJ\"\n}, {\n  \"country\": \"Tanzania\",\n  \"code\": \"+255\",\n  \"iso\": \"TZ\"\n}, {\n  \"country\": \"Thailand\",\n  \"code\": \"+66\",\n  \"iso\": \"TH\"\n}, {\n  \"country\": \"Togo\",\n  \"code\": \"+228\",\n  \"iso\": \"TG\"\n}, {\n  \"country\": \"Tokelau\",\n  \"code\": \"+690\",\n  \"iso\": \"TK\"\n}, {\n  \"country\": \"Tonga\",\n  \"code\": \"+676\",\n  \"iso\": \"TO\"\n}, {\n  \"country\": \"Trinidad and Tobago\",\n  \"code\": \"+1-868\",\n  \"iso\": \"TT\"\n}, {\n  \"country\": \"Tunisia\",\n  \"code\": \"+216\",\n  \"iso\": \"TN\"\n}, {\n  \"country\": \"Turkey\",\n  \"code\": \"+90\",\n  \"iso\": \"TR\"\n}, {\n  \"country\": \"Turkmenistan\",\n  \"code\": \"+993\",\n  \"iso\": \"TM\"\n}, {\n  \"country\": \"Turks and Caicos Islands\",\n  \"code\": \"+1-649\",\n  \"iso\": \"TC\"\n}, {\n  \"country\": \"Tuvalu\",\n  \"code\": \"+688\",\n  \"iso\": \"TV\"\n}, {\n  \"country\": \"U.S. Virgin Islands\",\n  \"code\": \"+1-340\",\n  \"iso\": \"VI\"\n}, {\n  \"country\": \"Uganda\",\n  \"code\": \"+256\",\n  \"iso\": \"UG\"\n}, {\n  \"country\": \"Ukraine\",\n  \"code\": \"+380\",\n  \"iso\": \"UA\"\n}, {\n  \"country\": \"United Arab Emirates\",\n  \"code\": \"+971\",\n  \"iso\": \"AE\"\n}, {\n  \"country\": \"United Kingdom\",\n  \"code\": \"+44\",\n  \"iso\": \"GB\"\n}, {\n  \"country\": \"United States\",\n  \"code\": \"+1\",\n  \"iso\": \"US\"\n}, {\n  \"country\": \"Uruguay\",\n  \"code\": \"+598\",\n  \"iso\": \"UY\"\n}, {\n  \"country\": \"Uzbekistan\",\n  \"code\": \"+998\",\n  \"iso\": \"UZ\"\n}, {\n  \"country\": \"Vanuatu\",\n  \"code\": \"+678\",\n  \"iso\": \"VU\"\n}, {\n  \"country\": \"Vatican\",\n  \"code\": \"+379\",\n  \"iso\": \"VA\"\n}, {\n  \"country\": \"Venezuela\",\n  \"code\": \"+58\",\n  \"iso\": \"VE\"\n}, {\n  \"country\": \"Vietnam\",\n  \"code\": \"+84\",\n  \"iso\": \"VN\"\n}, {\n  \"country\": \"Wallis and Futuna\",\n  \"code\": \"+681\",\n  \"iso\": \"WF\"\n}, {\n  \"country\": \"Western Sahara\",\n  \"code\": \"+212\",\n  \"iso\": \"EH\"\n}, {\n  \"country\": \"Yemen\",\n  \"code\": \"+967\",\n  \"iso\": \"YE\"\n}, {\n  \"country\": \"Zambia\",\n  \"code\": \"+260\",\n  \"iso\": \"ZM\"\n}, {\n  \"country\": \"Zimbabwe\",\n  \"code\": \"+263\",\n  \"iso\": \"ZW\"\n}];\nexport let dummyLessons = [{\n  \"id\": 1234,\n  \"classroomId\": 5678,\n  \"status\": \"Completed\",\n  \"startingDate\": \"2023-03-15T09:00:00Z\",\n  \"duration\": 60,\n  \"isRecccuring\": false,\n  \"answered\": \"no\",\n  \"requestedByName\": \"John Doe\",\n  \"bbbLink\": \"https://example.com/bbb-room-1234\",\n  \"title\": \"Introduction to Spanish\",\n  \"level\": \"A1\",\n  \"classroom\": {\n    \"id\": \"5678\",\n    \"title\": \"Spanish 101\",\n    \"status\": \"ON_GOING\",\n    \"totalHours\": 20,\n    \"hoursLeft\": 16,\n    \"activeLevel\": \"A1\",\n    \"users\": [{\n      \"id\": 9876,\n      \"fname\": \"Jane\",\n      \"lname\": \"Doe\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"+123456789\",\n      \"timezone\": \"Europe/Paris\"\n    }, {\n      \"id\": 8765,\n      \"fname\": \"Alice\",\n      \"lname\": \"Smith\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"+987654321\",\n      \"timezone\": \"America/Los_Angeles\"\n    }],\n    \"classroomStudents\": [{\n      \"id\": 9876,\n      \"fname\": \"Jane\",\n      \"lname\": \"Doe\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"+123456789\",\n      \"timezone\": \"Europe/Paris\"\n    }],\n    \"type\": \"Paid\",\n    \"language\": \"Spanish\",\n    \"teacher\": {\n      \"id\": 3456,\n      \"fname\": \"Juan\",\n      \"lname\": \"Garcia\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"+345678901\",\n      \"timezone\": \"Europe/Madrid\"\n    },\n    \"activePackage\": {\n      \"id\": \"package-1234\",\n      \"lessons_level\": [{\n        \"level\": \"A1\",\n        \"hoursCompleted\": 4,\n        \"lessonsStarted\": \"2023-03-01T09:00:00Z\",\n        \"lessonsEnded\": \"present\"\n      }],\n      \"totalHours\": 20,\n      \"currentPackageLevel\": \"A1\",\n      \"hoursLeft\": 16,\n      \"type\": \"REGULAR\",\n      \"expiresOn\": \"2023-09-15T23:59:59Z\",\n      \"stratedAt\": \"2023-02-15T09:00:00Z\",\n      \"purchasedAt\": \"2023-02-15T12:00:00Z\",\n      \"state\": \"ACTIVE\",\n      \"extensions\": []\n    }\n  }\n}, {\n  \"id\": 513,\n  \"classroomId\": 729,\n  \"status\": \"CANCELED\",\n  \"startingDate\": \"2022-09-14T16:00:00.000Z\",\n  \"duration\": 60,\n  \"isRecccuring\": false,\n  \"answered\": \"false\",\n  \"requestedByName\": \"Alice\",\n  \"title\": \"Intermediate French\",\n  \"level\": \"B1\",\n  \"classroom\": {\n    \"id\": \"729\",\n    \"title\": \"French 202\",\n    \"status\": \"ON_GOING\",\n    \"totalHours\": 20,\n    \"hoursLeft\": 10,\n    \"activeLevel\": \"B1\",\n    \"users\": [{\n      \"id\": 123,\n      \"firstName\": \"Bob\",\n      \"lastName\": \"Smith\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"************\",\n      \"role\": \"student\",\n      \"status\": \"ACTIVE\"\n    }, {\n      \"id\": 456,\n      \"firstName\": \"Charlie\",\n      \"lastName\": \"Brown\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"************\",\n      \"role\": \"student\",\n      \"status\": \"ACTIVE\"\n    }, {\n      \"id\": 789,\n      \"firstName\": \"David\",\n      \"lastName\": \"Jones\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"************\",\n      \"role\": \"teacher\",\n      \"status\": \"ACTIVE\"\n    }],\n    \"classroomStudents\": [{\n      \"id\": 123,\n      \"firstName\": \"Bob\",\n      \"lastName\": \"Smith\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"************\",\n      \"role\": \"student\",\n      \"status\": \"ACTIVE\"\n    }, {\n      \"id\": 456,\n      \"firstName\": \"Charlie\",\n      \"lastName\": \"Brown\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"************\",\n      \"role\": \"student\",\n      \"status\": \"ACTIVE\"\n    }],\n    \"type\": \"PAID\",\n    \"teacher\": {\n      \"id\": 789,\n      \"firstName\": \"David\",\n      \"lastName\": \"Jones\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"************\",\n      \"role\": \"teacher\",\n      \"status\": \"ACTIVE\"\n    }\n  }\n}, {\n  \"id\": 2,\n  \"classroomId\": 456,\n  \"status\": \"REARRANGE\",\n  \"startingDate\": new Date('2023-03-01T18:00:00.000Z'),\n  \"duration\": 60,\n  \"isRecccuring\": false,\n  \"answered\": \"accepted\",\n  \"requestedByName\": \"Jane Smith\",\n  \"title\": \"Advanced Calculus\",\n  \"level\": \"Expert\",\n  \"classroom\": undefined,\n  \"bbbLink\": \"https://example.com/bbb/2\"\n}, {\n  \"id\": 3,\n  \"classroomId\": 789,\n  \"status\": \"REQUESTED\",\n  \"startingDate\": new Date('2023-03-01T18:00:00.000Z'),\n  \"duration\": 45,\n  \"isRecccuring\": false,\n  \"answered\": \"rejected\",\n  \"requestedByName\": \"Bob Johnson\",\n  \"title\": 'sasa',\n  \"level\": 'A1',\n  \"classroom\": undefined,\n  \"bbbLink\": \"https://example.com/bbb/3\"\n}, {\n  \"id\": 4,\n  \"classroomId\": 123,\n  \"status\": \"COMPLETED\",\n  \"startingDate\": new Date('2023-03-01T18:00:00.000Z'),\n  \"duration\": 30,\n  \"isRecccuring\": true,\n  \"answered\": \"canceled\",\n  \"requestedByName\": \"Alice Lee\",\n  \"title\": \"English Conversation\",\n  \"level\": 'A2',\n  \"classroom\": undefined,\n  \"bbbLink\": \"https://example.com/bbb/4\"\n}, {\n  \"id\": 5,\n  \"classroomId\": 456,\n  \"status\": \"NO_SHOW\",\n  \"startingDate\": new Date('2023-03-01T18:00:00.000Z'),\n  \"duration\": 60,\n  \"isRecccuring\": false,\n  \"answered\": \"attended\",\n  \"requestedByName\": \"David Kim\",\n  \"title\": \"Business Writing\",\n  \"level\": \"Advanced\",\n  \"classroom\": undefined,\n  \"bbbLink\": \"https://example.com/bbb/5\"\n}];\nexport let dummyClassrooms = [{\n  \"id\": \"987654\",\n  \"title\": \"Spanish Course for Beginners\",\n  \"status\": \"ON_GOING\",\n  \"totalHours\": 20,\n  \"hoursLeft\": 12,\n  \"activeLevel\": \"A1\",\n  \"users\": [{\n    \"id\": \"123456\",\n    \"name\": \"John Doe\",\n    \"email\": \"<EMAIL>\"\n  }, {\n    \"id\": \"789012\",\n    \"name\": \"Jane Doe\",\n    \"email\": \"<EMAIL>\"\n  }],\n  \"classroomStudents\": [{\n    \"id\": \"123456\",\n    \"name\": \"John Doe\",\n    \"email\": \"<EMAIL>\"\n  }, {\n    \"id\": \"789012\",\n    \"name\": \"Jane Doe\",\n    \"email\": \"<EMAIL>\"\n  }],\n  \"type\": \"PAID\",\n  \"language\": \"Spanish\",\n  \"teacher\": {\n    \"id\": \"345678\",\n    \"name\": \"Maria Garcia\",\n    \"email\": \"<EMAIL>\"\n  },\n  \"activePackage\": {\n    \"id\": \"234567\",\n    \"title\": \"Spanish Beginner Package\",\n    \"price\": 99.99,\n    \"hours\": 20\n  },\n  \"lessons\": [{\n    \"id\": \"345678\",\n    \"title\": \"Greetings and Introductions\",\n    \"description\": \"Learn basic greetings and how to introduce yourself\",\n    \"duration\": 2\n  }, {\n    \"id\": \"456789\",\n    \"title\": \"Numbers and Time\",\n    \"description\": \"Learn to tell the time and count to 100\",\n    \"duration\": 2\n  }],\n  \"packages\": [{\n    \"id\": \"234567\",\n    \"title\": \"Spanish Beginner Package\",\n    \"price\": 99.99,\n    \"hours\": 20\n  }]\n}, {\n  \"id\": \"classroom001\",\n  \"title\": \"English Speaking Practice for Beginners\",\n  \"status\": \"ON_GOING\",\n  \"totalHours\": 24,\n  \"hoursLeft\": 6,\n  \"activeLevel\": \"A1\",\n  \"users\": [{\n    \"id\": \"user001\",\n    \"name\": \"John Doe\",\n    \"email\": \"<EMAIL>\",\n    \"role\": \"STUDENT\"\n  }, {\n    \"id\": \"user002\",\n    \"name\": \"Jane Doe\",\n    \"email\": \"<EMAIL>\",\n    \"role\": \"STUDENT\"\n  }],\n  \"classroomStudents\": [{\n    \"id\": \"user001\",\n    \"name\": \"John Doe\",\n    \"email\": \"<EMAIL>\",\n    \"role\": \"STUDENT\"\n  }, {\n    \"id\": \"user002\",\n    \"name\": \"Jane Doe\",\n    \"email\": \"<EMAIL>\",\n    \"role\": \"STUDENT\"\n  }],\n  \"type\": \"PAID\",\n  \"language\": \"English\",\n  \"teacher\": {\n    \"id\": \"user003\",\n    \"name\": \"Mary Smith\",\n    \"email\": \"<EMAIL>\",\n    \"role\": \"TEACHER\"\n  },\n  \"activePackage\": {\n    \"id\": \"package001\",\n    \"name\": \"Basic Package\",\n    \"hours\": 24,\n    \"price\": 200\n  },\n  \"lessons\": [{\n    \"id\": \"lesson001\",\n    \"title\": \"Introduction to English\",\n    \"description\": \"Basic English phrases and greetings\",\n    \"duration\": 2\n  }, {\n    \"id\": \"lesson002\",\n    \"title\": \"Talking about yourself\",\n    \"description\": \"Describing yourself and your interests\",\n    \"duration\": 2\n  }],\n  \"packages\": [{\n    \"id\": \"package001\",\n    \"name\": \"Basic Package\",\n    \"hours\": 24,\n    \"price\": 200\n  }, {\n    \"id\": \"package002\",\n    \"name\": \"Intermediate Package\",\n    \"hours\": 48,\n    \"price\": 350\n  }]\n}];\nexport let dummyLesson = {\n  \"id\": 1234,\n  \"classroomId\": 5678,\n  \"status\": \"ARRANGED_TRIAL\",\n  \"startingDate\": \"2023-05-07T09:00:00Z\",\n  \"duration\": 60,\n  \"isRecccuring\": false,\n  \"answered\": \"no\",\n  \"requestedByName\": \"John Doe\",\n  \"bbbLink\": \"https://example.com/bbb-room-1234\",\n  \"title\": \"Introduction to Spanish\",\n  \"level\": \"A1\",\n  \"classroom\": {\n    \"id\": \"5678\",\n    \"title\": \"Spanish 101\",\n    \"status\": \"ON_GOING\",\n    \"totalHours\": 20,\n    \"hoursLeft\": 16,\n    \"activeLevel\": \"A1\",\n    \"users\": [{\n      \"id\": 9876,\n      \"fname\": \"Jane\",\n      \"lname\": \"Doe\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"+123456789\",\n      \"timezone\": \"Europe/Paris\"\n    }, {\n      \"id\": 8765,\n      \"fname\": \"Alice\",\n      \"lname\": \"Smith\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"+987654321\",\n      \"timezone\": \"America/Los_Angeles\"\n    }],\n    \"classroomStudents\": [{\n      \"id\": 9876,\n      \"fname\": \"Jane\",\n      \"lname\": \"Doe\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"+123456789\",\n      \"timezone\": \"Europe/Paris\"\n    }],\n    \"type\": \"Paid\",\n    \"language\": \"Spanish\",\n    \"teacher\": {\n      \"id\": 3456,\n      \"fname\": \"Juan\",\n      \"lname\": \"Garcia\",\n      \"email\": \"<EMAIL>\",\n      \"phone\": \"+345678901\",\n      \"timezone\": \"Europe/Madrid\"\n    },\n    \"activePackage\": {\n      \"id\": \"package-1234\",\n      \"lessons_level\": [{\n        \"level\": \"A1\",\n        \"hoursCompleted\": 4,\n        \"lessonsStarted\": \"2023-03-01T09:00:00Z\",\n        \"lessonsEnded\": \"present\"\n      }],\n      \"totalHours\": 20,\n      \"currentPackageLevel\": \"A1\",\n      \"hoursLeft\": 16,\n      \"type\": \"REGULAR\",\n      \"expiresOn\": \"2023-09-15T23:59:59Z\",\n      \"stratedAt\": \"2023-02-15T09:00:00Z\",\n      \"purchasedAt\": \"2023-02-15T12:00:00Z\",\n      \"state\": \"ACTIVE\",\n      \"extensions\": []\n    }\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}