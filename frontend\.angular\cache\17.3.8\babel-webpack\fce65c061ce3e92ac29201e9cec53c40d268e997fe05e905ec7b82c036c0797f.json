{"ast": null, "code": "import { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/layout.service\";\nimport * as i2 from \"../../../shared/layout/g-button/g-button.component\";\nconst _c0 = [\"galaxyWrapper\"];\nexport let RedirectToGalaxyComponent = /*#__PURE__*/(() => {\n  class RedirectToGalaxyComponent {\n    constructor(layoutService) {\n      this.layoutService = layoutService;\n      this.subs = new SubSink();\n      this.galaxyWrapper = {};\n    }\n    ngOnInit() {\n      this.layoutService.sideMenuHeight.subscribe(res => {\n        this.galaxyWrapper.nativeElement.style.height = res + \"px\";\n      });\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n    }\n    openLingoGalaxyInNewTab() {\n      window.open(\"https://www.lingogalaxy.com/\", \"_blank\");\n    }\n    static #_ = this.ɵfac = function RedirectToGalaxyComponent_Factory(t) {\n      return new (t || RedirectToGalaxyComponent)(i0.ɵɵdirectiveInject(i1.LayoutService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RedirectToGalaxyComponent,\n      selectors: [[\"app-redirect-to-galaxy\"]],\n      viewQuery: function RedirectToGalaxyComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.galaxyWrapper = _t.first);\n        }\n      },\n      decls: 25,\n      vars: 0,\n      consts: [[\"galaxyWrapper\", \"\"], [1, \"galaxy-wrapper\", \"px-2\", \"mb-3\"], [1, \"main-wrapper\", \"flex\", \"align-items-center\", \"justify-content-between\", \"flex-column\", \"h-full\", \"box\"], [1, \"main\"], [\"src\", \"/assets/images/dashboard/galaxy/planets-galaxy.png\", \"alt\", \"galaxy\", 1, \"w-full\"], [1, \"md:mx-6\"], [1, \"card\", \"galaxy-card\", \"p-3\", \"my-5\", \"block\", \"text-sm\"], [\"text\", \"Continue at LingoGalaxy\", 1, \"mt-3\", \"font-btn-sm\", \"trial-gradient-lesson-bg\", 3, \"click\"], [1, \"pb-3\"], [1, \"mb-2\"]],\n      template: function RedirectToGalaxyComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1, 0)(2, \"div\", 2)(3, \"div\", 3);\n          i0.ɵɵelement(4, \"img\", 4);\n          i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\");\n          i0.ɵɵtext(7, \" Your trial has been transfered to \");\n          i0.ɵɵelementStart(8, \"b\");\n          i0.ɵɵtext(9, \"LingoGalaxy!\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 6)(11, \"div\");\n          i0.ɵɵtext(12, \"LingoGalaxy is our partner brand, \");\n          i0.ɵɵelement(13, \"br\");\n          i0.ɵɵtext(14, \" the \");\n          i0.ɵɵelementStart(15, \"b\");\n          i0.ɵɵtext(16, \"Online Language School \");\n          i0.ɵɵelement(17, \"br\");\n          i0.ɵɵtext(18, \" for Kids and Teens (3-17 years old).\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"g-button\", 7);\n          i0.ɵɵlistener(\"click\", function RedirectToGalaxyComponent_Template_g_button_click_19_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.openLingoGalaxyInNewTab());\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 8)(21, \"div\", 9);\n          i0.ɵɵtext(22, \"Happy Exploring,\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"div\");\n          i0.ɵɵtext(24, \"The MyLingoTrip Family\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n      },\n      dependencies: [i2.GButtonComponent],\n      styles: [\"@charset \\\"UTF-8\\\";.responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}[_nghost-%COMP%]{display:flex;position:relative}.galaxy-wrapper[_ngcontent-%COMP%]{background-image:url(/assets/images/dashboard/galaxy/mountains-bg.png);background-repeat:no-repeat;background-position:center 70%;background-blend-mode:overlay;border-radius:11px;min-width:100%;color:#fff}@media only screen and (max-width: 768px){.galaxy-wrapper[_ngcontent-%COMP%]{height:700px!important;border-radius:0}}@media screen and (min-width: 1800px){.galaxy-wrapper[_ngcontent-%COMP%]{background-size:cover;background-position:center}}.galaxy-wrapper[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]{margin:auto;display:inline-block;position:relative}@media only screen and (max-width: 768px){.galaxy-wrapper[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]{justify-content:center!important}}@media only screen and (min-width: 768px){.galaxy-wrapper[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]{max-width:50%!important}}@media only screen and (min-width: 992px){.galaxy-wrapper[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]{max-width:50%}}@media only screen and (min-width: 1200px){.galaxy-wrapper[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]{max-width:45%!important}}@media only screen and (min-width: 1700px){.galaxy-wrapper[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]{max-width:47%!important}}@media (max-height: 800px) and (min-width: 800px){.galaxy-wrapper[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]{max-width:50%!important}.galaxy-wrapper[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .galaxy-card[_ngcontent-%COMP%]{margin-top:.5rem!important;margin-bottom:.5rem!important}}@media (max-height: 480px) and (max-width: 800px){.galaxy-wrapper[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .rounded-blue-button[_ngcontent-%COMP%]{margin-top:0!important}.galaxy-wrapper[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:75%!important;margin:auto}.galaxy-wrapper[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]   .md\\\\:mx-6[_ngcontent-%COMP%]{margin:0!important}}@media only screen and (max-width: 768px){.galaxy-wrapper[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]{overflow:auto}}.galaxy-wrapper[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]:after{padding-top:56.25%;display:block;content:\\\"\\\"}.galaxy-wrapper[_ngcontent-%COMP%]   .main-wrapper[_ngcontent-%COMP%]   .main[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:space-evenly;height:inherit;position:absolute;top:0;left:0;right:0;margin-left:auto;margin-right:auto;color:#fff;text-align:center}.galaxy-wrapper[_ngcontent-%COMP%]   .galaxy-card[_ngcontent-%COMP%]{background:#2b2b6091;border:none;color:#fff!important;border-radius:20px}.galaxy-wrapper[_ngcontent-%COMP%]   .galaxy-card[_ngcontent-%COMP%]   .card-text[_ngcontent-%COMP%]{font-size:10px}@media (min-width: 300px) and (max-width: 1599px){.galaxy-wrapper[_ngcontent-%COMP%]   .galaxy-card[_ngcontent-%COMP%]   .card-text[_ngcontent-%COMP%]{font-size:13px;font-size:calc(10px + (16 - 10) * (100vw - 300px) / (1600 - 300))!important}}@media (min-width: 1600px){.galaxy-wrapper[_ngcontent-%COMP%]   .galaxy-card[_ngcontent-%COMP%]   .card-text[_ngcontent-%COMP%]{font-size:16px}}.box[_ngcontent-%COMP%]{position:absolute;bottom:0;left:0}\"]\n    });\n  }\n  return RedirectToGalaxyComponent;\n})();", "map": {"version": 3, "names": ["SubSink", "RedirectToGalaxyComponent", "constructor", "layoutService", "subs", "galaxyWrapper", "ngOnInit", "sideMenuHeight", "subscribe", "res", "nativeElement", "style", "height", "ngOnDestroy", "unsubscribe", "openLingoGalaxyInNewTab", "window", "open", "_", "i0", "ɵɵdirectiveInject", "i1", "LayoutService", "_2", "selectors", "viewQuery", "RedirectToGalaxyComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "RedirectToGalaxyComponent_Template_g_button_click_19_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\dashboard\\redirect-to-galaxy\\redirect-to-galaxy.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\dashboard\\redirect-to-galaxy\\redirect-to-galaxy.component.html"], "sourcesContent": ["import { Compo<PERSON>, ElementR<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';\r\nimport { LayoutService } from 'src/app/core/services/layout.service';\r\nimport { SubSink } from 'subsink';\r\n\r\n@Component({\r\n  selector: \"app-redirect-to-galaxy\",\r\n  templateUrl: \"./redirect-to-galaxy.component.html\",\r\n  styleUrls: [\"./redirect-to-galaxy.component.scss\"],\r\n})\r\nexport class RedirectToGalaxyComponent implements OnInit, OnDestroy {\r\n  private subs = new SubSink();\r\n  @ViewChild(\"galaxyWrapper\", { static: true }) galaxyWrapper: ElementRef =\r\n    {} as ElementRef;\r\n  constructor(private layoutService: LayoutService) {}\r\n\r\n  ngOnInit(): void {\r\n    this.layoutService.sideMenuHeight.subscribe((res) => {\r\n      this.galaxyWrapper.nativeElement.style.height = res + \"px\";\r\n    });\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  openLingoGalaxyInNewTab() {\r\n    window.open(\"https://www.lingogalaxy.com/\", \"_blank\");\r\n  }\r\n\r\n}\r\n", "<div class=\"galaxy-wrapper px-2 mb-3\" #galaxyWrapper>\r\n    <div class=\"main-wrapper flex align-items-center justify-content-between flex-column h-full box\">\r\n        <div class=\"main\">\r\n            <img src=\"/assets/images/dashboard/galaxy/planets-galaxy.png\" class=\"w-full \" alt=\"galaxy\"/>\r\n            <div class=\"md:mx-6\">\r\n                <div> Your trial has been transfered to  <b>LingoGalaxy!</b></div>\r\n                <div class=\"card galaxy-card p-3 my-5 block text-sm\">\r\n                    <div>LingoGalaxy is our partner brand, <br>\r\n                        the <b>Online Language School <br>\r\n                        for Kids and Teens (3-17 years old).</b>\r\n                    </div>\r\n                    <!-- <button pRipple class=\"rounded-blue-button mt-3\">Continue at LingoGalaxy</button> -->\r\n                    <g-button class=\"mt-3 font-btn-sm trial-gradient-lesson-bg\" text=\"Continue at LingoGalaxy\"\r\n                    (click)=\"openLingoGalaxyInNewTab()\"></g-button>\r\n                </div> \r\n                <div class=\"pb-3\">\r\n                    <div class=\"mb-2\">Happy Exploring,</div>\r\n                    <div>The MyLingoTrip Family</div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n"], "mappings": "AAEA,SAASA,OAAO,QAAQ,SAAS;;;;;AAOjC,WAAaC,yBAAyB;EAAhC,MAAOA,yBAAyB;IAIpCC,YAAoBC,aAA4B;MAA5B,KAAAA,aAAa,GAAbA,aAAa;MAHzB,KAAAC,IAAI,GAAG,IAAIJ,OAAO,EAAE;MACkB,KAAAK,aAAa,GACzD,EAAgB;IACiC;IAEnDC,QAAQA,CAAA;MACN,IAAI,CAACH,aAAa,CAACI,cAAc,CAACC,SAAS,CAAEC,GAAG,IAAI;QAClD,IAAI,CAACJ,aAAa,CAACK,aAAa,CAACC,KAAK,CAACC,MAAM,GAAGH,GAAG,GAAG,IAAI;MAC5D,CAAC,CAAC;IACJ;IAEAI,WAAWA,CAAA;MACT,IAAI,CAACT,IAAI,CAACU,WAAW,EAAE;IACzB;IAEAC,uBAAuBA,CAAA;MACrBC,MAAM,CAACC,IAAI,CAAC,8BAA8B,EAAE,QAAQ,CAAC;IACvD;IAAC,QAAAC,CAAA,G;uBAlBUjB,yBAAyB,EAAAkB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA;IAAA;IAAA,QAAAC,EAAA,G;YAAzBtB,yBAAyB;MAAAuB,SAAA;MAAAC,SAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;UCP9BR,EAFR,CAAAU,cAAA,gBAAqD,aACgD,aAC3E;UACdV,EAAA,CAAAW,SAAA,aAA4F;UAExFX,EADJ,CAAAU,cAAA,aAAqB,UACZ;UAACV,EAAA,CAAAY,MAAA,0CAAmC;UAAAZ,EAAA,CAAAU,cAAA,QAAG;UAAAV,EAAA,CAAAY,MAAA,mBAAY;UAAIZ,EAAJ,CAAAa,YAAA,EAAI,EAAM;UAE9Db,EADJ,CAAAU,cAAA,cAAqD,WAC5C;UAAAV,EAAA,CAAAY,MAAA,0CAAkC;UAAAZ,EAAA,CAAAW,SAAA,UAAI;UACvCX,EAAA,CAAAY,MAAA,aAAI;UAAAZ,EAAA,CAAAU,cAAA,SAAG;UAAAV,EAAA,CAAAY,MAAA,+BAAuB;UAAAZ,EAAA,CAAAW,SAAA,UAAI;UAClCX,EAAA,CAAAY,MAAA,6CAAoC;UACxCZ,EADwC,CAAAa,YAAA,EAAI,EACtC;UAENb,EAAA,CAAAU,cAAA,mBACoC;UAApCV,EAAA,CAAAc,UAAA,mBAAAC,8DAAA;YAAAf,EAAA,CAAAgB,aAAA,CAAAC,GAAA;YAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAST,GAAA,CAAAb,uBAAA,EAAyB;UAAA,EAAC;UACvCI,EADwC,CAAAa,YAAA,EAAW,EAC7C;UAEFb,EADJ,CAAAU,cAAA,cAAkB,cACI;UAAAV,EAAA,CAAAY,MAAA,wBAAgB;UAAAZ,EAAA,CAAAa,YAAA,EAAM;UACxCb,EAAA,CAAAU,cAAA,WAAK;UAAAV,EAAA,CAAAY,MAAA,8BAAsB;UAK/CZ,EAL+C,CAAAa,YAAA,EAAM,EAC/B,EACJ,EACJ,EACJ,EACJ;;;;;;;SDbO/B,yBAAyB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}