{"ast": null, "code": "import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { SubSink } from 'subsink';\nimport * as moment from 'moment';\nimport { NoteListenersService, NoteViewAccess } from 'src/app/core/services/note-listeners.service';\nimport { LibraryFileActionsDialogComponent } from 'src/app/modules/library/library/components/library-file-actions-dialog/library-file-actions-dialog.component';\nimport { DialogService } from 'primeng/dynamicdialog';\nimport { tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nimport * as i2 from \"src/app/core/services/note.service\";\nimport * as i3 from \"src/app/core/services/toast.service\";\nimport * as i4 from \"src/app/core/services/classroom.service\";\nimport * as i5 from \"src/app/core/services/auth.service\";\nimport * as i6 from \"src/app/core/services/note-listeners.service\";\nimport * as i7 from \"src/app/core/services/note-actions.service\";\nimport * as i8 from \"primeng/dynamicdialog\";\nimport * as i9 from \"src/app/core/services/confirm-dialog.service\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"primeng/tabview\";\nimport * as i12 from \"@angular/forms\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nimport * as i14 from \"./note-details/note-details.component\";\nimport * as i15 from \"src/app/core/pipes/filter.pipe\";\nconst _c0 = a0 => ({\n  \"filter-blue\": a0\n});\nconst _c1 = (a0, a1) => [a0, a1];\nconst _c2 = (a0, a1) => ({\n  classroom: a0,\n  filteredNotes: a1\n});\nconst _c3 = (a0, a1, a2) => ({\n  classroom: a0,\n  filteredNotes: a1,\n  student: a2\n});\nconst _c4 = a0 => ({\n  term: a0\n});\nfunction NotesListComponent_img_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 14);\n    i0.ɵɵlistener(\"click\", function NotesListComponent_img_9_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showAddNote = !ctx_r1.showAddNote);\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotesListComponent_div_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function NotesListComponent_div_11_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onShareMultiple());\n    });\n    i0.ɵɵelementStart(1, \"div\", 18);\n    i0.ɵɵelement(2, \"img\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 20);\n    i0.ɵɵtext(4, \" Share \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotesListComponent_div_11_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function NotesListComponent_div_11_div_2_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onUnShareMultiple());\n    });\n    i0.ɵɵelementStart(1, \"div\", 18);\n    i0.ɵɵelement(2, \"img\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 20);\n    i0.ɵɵtext(4, \" Unshare \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotesListComponent_div_11_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function NotesListComponent_div_11_div_8_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onRemoveNoteMultiple());\n    });\n    i0.ɵɵelementStart(1, \"div\", 18);\n    i0.ɵɵelement(2, \"img\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 20);\n    i0.ɵɵtext(4, \" Delete \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotesListComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtemplate(1, NotesListComponent_div_11_div_1_Template, 5, 0, \"div\", 16)(2, NotesListComponent_div_11_div_2_Template, 5, 0, \"div\", 16);\n    i0.ɵɵelementStart(3, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function NotesListComponent_div_11_Template_div_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.downloadMultiple());\n    });\n    i0.ɵɵelementStart(4, \"div\", 18);\n    i0.ɵɵelement(5, \"img\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 20);\n    i0.ɵɵtext(7, \" Download \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, NotesListComponent_div_11_div_8_Template, 5, 0, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpureFunction2(3, _c1, ctx_r1.noteViewAccess.TEACHER_VIEW_STUDENTS_CLASSROOM_NOTES, ctx_r1.noteViewAccess.STUDENT_VIEW_TEACHERS_CLASSROOM_NOTES).includes(ctx_r1.currentNotesView));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentNotesView !== ctx_r1.noteViewAccess.TEACHER_VIEW_NOTES && ctx_r1.currentNotesView !== ctx_r1.noteViewAccess.STUDENT_VIEW_TEACHERS_CLASSROOM_NOTES);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasAdd);\n  }\n}\nfunction NotesListComponent_ng_container_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-note-details\", 23);\n    i0.ɵɵlistener(\"closedNewNote\", function NotesListComponent_ng_container_13_div_1_Template_app_note_details_closedNewNote_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onClosedNewNote());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"type\", \"create\")(\"classroom\", ctx_r1.classroom)(\"showGroupActions\", ctx_r1.showGroupActions)(\"view\", ctx_r1.noteListenersService.getNotesViewAccess(ctx_r1.hasAdd, ctx_r1.withClassroom));\n  }\n}\nfunction NotesListComponent_ng_container_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NotesListComponent_ng_container_13_div_1_Template, 2, 4, \"div\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.classroomStudents && ctx_r1.classroomStudents.length === 1 || !ctx_r1.withClassroom) && ctx_r1.hasAdd);\n  }\n}\nfunction NotesListComponent_div_14_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NotesListComponent_div_14_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NotesListComponent_div_14_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const notesGeneralTemplate_r8 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", notesGeneralTemplate_r8)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c2, ctx_r1.classroom, ctx_r1.filteredNotes));\n  }\n}\nfunction NotesListComponent_div_14_ng_container_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NotesListComponent_div_14_ng_container_2_ng_container_5_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NotesListComponent_div_14_ng_container_2_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p-tabPanel\", 31);\n    i0.ɵɵtemplate(2, NotesListComponent_div_14_ng_container_2_ng_container_5_ng_container_2_Template, 1, 0, \"ng-container\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const selectedStudent_r9 = ctx.$implicit;\n    i0.ɵɵnextContext(2);\n    const notesGeneralTemplate_r8 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"header\", selectedStudent_r9.firstName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", notesGeneralTemplate_r8)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction3(3, _c3, ctx_r1.classroom, ctx_r1.filteredNotes, selectedStudent_r9));\n  }\n}\nfunction NotesListComponent_div_14_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p-tabView\", 27)(2, \"p-tabPanel\", 28)(3, \"div\", 29);\n    i0.ɵɵtemplate(4, NotesListComponent_div_14_ng_container_2_ng_container_4_Template, 1, 0, \"ng-container\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, NotesListComponent_div_14_ng_container_2_ng_container_5_Template, 3, 7, \"ng-container\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const notesGeneralTemplate_r8 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"scrollable\", ctx_r1.classroomStudents.length > 4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", notesGeneralTemplate_r8)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(4, _c2, ctx_r1.classroom, ctx_r1.filteredNotes));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.classroomStudents);\n  }\n}\nfunction NotesListComponent_div_14_ng_template_3_ng_container_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-note-details\", 23);\n    i0.ɵɵlistener(\"closedNewNote\", function NotesListComponent_div_14_ng_template_3_ng_container_0_div_1_Template_app_note_details_closedNewNote_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onClosedNewNote());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const classroom_r11 = i0.ɵɵnextContext(2).classroom;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"type\", \"create\")(\"classroom\", classroom_r11)(\"showGroupActions\", ctx_r1.showGroupActions)(\"view\", ctx_r1.noteListenersService.getNotesViewAccess(ctx_r1.hasAdd, ctx_r1.withClassroom));\n  }\n}\nfunction NotesListComponent_div_14_ng_template_3_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NotesListComponent_div_14_ng_template_3_ng_container_0_div_1_Template, 2, 4, \"div\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.classroomStudents && ctx_r1.classroomStudents.length > 1);\n  }\n}\nfunction NotesListComponent_div_14_ng_template_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-note-details\", 33);\n    i0.ɵɵlistener(\"noteChecked\", function NotesListComponent_div_14_ng_template_3_div_1_Template_app_note_details_noteChecked_1_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onNoteChecked($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const note_r13 = ctx.$implicit;\n    const classroom_r11 = i0.ɵɵnextContext().classroom;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"isInMine\", ctx_r1.hasAdd)(\"note\", note_r13)(\"classroom\", classroom_r11)(\"withClassroom\", ctx_r1.withClassroom)(\"showGroupActions\", ctx_r1.showGroupActions)(\"view\", ctx_r1.noteListenersService.getNotesViewAccess(ctx_r1.hasAdd, ctx_r1.withClassroom));\n  }\n}\nfunction NotesListComponent_div_14_ng_template_3_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"img\", 37);\n    i0.ɵɵlistener(\"click\", function NotesListComponent_div_14_ng_template_3_div_3_div_1_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.showAddNote = !ctx_r1.showAddNote);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"p\", 38);\n    i0.ɵɵtext(3, \" Add your first note! \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotesListComponent_div_14_ng_template_3_div_3_ng_container_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" No notes found. \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction NotesListComponent_div_14_ng_template_3_div_3_ng_container_2_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" No notes found. \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction NotesListComponent_div_14_ng_template_3_div_3_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 36);\n    i0.ɵɵelement(2, \"img\", 39);\n    i0.ɵɵelementStart(3, \"p\", 38);\n    i0.ɵɵtemplate(4, NotesListComponent_div_14_ng_template_3_div_3_ng_container_2_ng_container_4_Template, 2, 0, \"ng-container\", 12)(5, NotesListComponent_div_14_ng_template_3_div_3_ng_container_2_ng_container_5_Template, 2, 0, \"ng-container\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isStudent);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isTeacher);\n  }\n}\nfunction NotesListComponent_div_14_ng_template_3_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtemplate(1, NotesListComponent_div_14_ng_template_3_div_3_div_1_Template, 4, 0, \"div\", 35)(2, NotesListComponent_div_14_ng_template_3_div_3_ng_container_2_Template, 6, 2, \"ng-container\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const student_r15 = i0.ɵɵnextContext().student;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"fromRoot\", true)(\"scrollWindow\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.hasAdd || ctx_r1.getFilteredNotes(student_r15).length == 0) && ctx_r1.getFilteredNotes(student_r15).length == 0 && !student_r15 && ctx_r1.hasAdd);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hasAdd || ctx_r1.getFilteredNotes(student_r15).length === 0 && student_r15);\n  }\n}\nfunction NotesListComponent_div_14_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NotesListComponent_div_14_ng_template_3_ng_container_0_Template, 2, 1, \"ng-container\", 12)(1, NotesListComponent_div_14_ng_template_3_div_1_Template, 2, 6, \"div\", 30);\n    i0.ɵɵpipe(2, \"filter\");\n    i0.ɵɵtemplate(3, NotesListComponent_div_14_ng_template_3_div_3_Template, 3, 4, \"div\", 32);\n  }\n  if (rf & 2) {\n    const student_r15 = ctx.student;\n    const filteredNotes_r16 = ctx.filteredNotes;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentNotesView === ctx_r1.noteViewAccess.TEACHER_VIEW_CLASSROOM_NOTES && ctx_r1.showAddNote);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind2(2, 3, ctx_r1.getFilteredNotes(student_r15), i0.ɵɵpureFunction1(6, _c4, ctx_r1.term)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", filteredNotes_r16 && ctx_r1.getFilteredNotes(student_r15).length == 0 && !ctx_r1.showAddNote);\n  }\n}\nfunction NotesListComponent_div_14_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"mat-spinner\", 41);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotesListComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtemplate(1, NotesListComponent_div_14_ng_container_1_Template, 2, 5, \"ng-container\", 12)(2, NotesListComponent_div_14_ng_container_2_Template, 6, 7, \"ng-container\", 12)(3, NotesListComponent_div_14_ng_template_3_Template, 4, 8, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(5, NotesListComponent_div_14_div_5_Template, 2, 0, \"div\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.classroomStudents && ctx_r1.classroomStudents.length === 1 || !ctx_r1.withClassroom || ctx_r1.authService.isStudent);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.withClassroom && ctx_r1.authService.isTeacher && ctx_r1.classroomStudents && ctx_r1.classroomStudents.length > 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n  }\n}\nexport class NotesListComponent {\n  constructor(generalService, noteService, toastService, classroomService, cd, authService, noteListenersService, noteActionsService, dialogService, confirmDialogService) {\n    this.generalService = generalService;\n    this.noteService = noteService;\n    this.toastService = toastService;\n    this.classroomService = classroomService;\n    this.cd = cd;\n    this.authService = authService;\n    this.noteListenersService = noteListenersService;\n    this.noteActionsService = noteActionsService;\n    this.dialogService = dialogService;\n    this.confirmDialogService = confirmDialogService;\n    this.notes = [];\n    this.viewTitle = 'My Notes';\n    this.withClassroom = false;\n    this.hasAdd = true;\n    this.classroom = {};\n    this.subs = new SubSink();\n    this.showFilters = false;\n    this.showGroupActions = true;\n    this.showAddNote = false;\n    this.tryToSave = false;\n    this.showTextError = false;\n    this.isSharedFilter = false;\n    this.isNotSharedFilter = false;\n    this.isPinnedFilter = false;\n    this.isFavoritedFilter = false;\n    this.fromFilterValue = {};\n    this.toFilterValue = {};\n    this.datePickerOpen = false;\n    this.filteredNotes = [];\n    this.classrooms = [];\n    this.showSend = false;\n    this.showSendAndShare = false;\n    this.usersToSend = [];\n    this.currentSelectedClassroomId = 0;\n    this.showingMine = true;\n    this.inClassroom = false;\n    this.isLoading = false;\n    this.textEditor = '';\n    this.checkedNotes = [];\n    this.classroomStudents = [];\n    this.user = {};\n    this.role = \"\";\n    this.maxSize = 6;\n    this.directionLinks = true;\n    this.autoHide = false;\n    this.to = {};\n    this.from = {};\n    this.form = new UntypedFormGroup({});\n    this.isInMine = false;\n    this.noteViewAccess = NoteViewAccess;\n  }\n  ngOnInit() {\n    this.user = this.authService.getLoggedInUser();\n    this.role = this.user.role;\n    this.initClassroomListener();\n    // this.notSharedFilter(true);\n    this.form = new UntypedFormGroup({\n      title: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      }),\n      text: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      })\n    });\n  }\n  ngOnChanges() {\n    if (this.classroom) {\n      this.classroomStudents = this.classroom.classroomStudents;\n    }\n    this.filteredNotes = this.notes;\n  }\n  ngAfterViewInit() {\n    this.showGroupActions = false;\n    this.cd.detectChanges();\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n    this.noteListenersService.setGroupActions(false);\n    this.noteService.checkedNotes = [];\n  }\n  ngAfterContentInit() {}\n  get currentNotesView() {\n    return this.noteListenersService.getNotesViewAccess(this.hasAdd, this.withClassroom);\n  }\n  //listener to change ui in nite details\n  setGroupActions() {\n    this.showGroupActions = !this.showGroupActions;\n    if (!this.showGroupActions) {\n      this.noteService.checkedNotes = [];\n    }\n    setTimeout(() => {\n      this.noteListenersService.setGroupActions(this.showGroupActions);\n    }, 300);\n  }\n  /**\n   * Filters the notes based on whether they are shared with the given student.\n   *\n   * @param targetStudent - The student user to filter by.\n   * @returns An array of notes that are shared with the given student.\n   */\n  getFilteredNotes(targetStudent) {\n    if (this.hasAdd) {\n      return this.filteredNotes.filter(note => {\n        return targetStudent ? note.sharedWithUsers.some(user => user.aspUserId === targetStudent.aspUserId) : true;\n      });\n    } else {\n      return this.filteredNotes.filter(note => {\n        return targetStudent ? note.createdBy.aspUserId === targetStudent.aspUserId : true;\n      });\n    }\n  }\n  areNotFavorited() {\n    for (let note of this.noteService.checkedNotes) {\n      if (note.favouritedBy.filter(user => user.id == this.authService.getUserId()).length == 0) return true;\n    }\n    return false;\n  }\n  areNotPinned() {\n    for (let note of this.noteService.checkedNotes) {\n      if (!note.pinned) return true;\n    }\n    return false;\n  }\n  /**\n   * Filters the notes based on the selected filters and returns the filtered notes.\n   *\n   * @return {Note[]} An array of Note objects that pass the applied filters.\n   */\n  getNotesFiltered() {\n    let filteredNotes = [];\n    for (let note of this.notes) {\n      if (this.isPinnedFilter) {\n        if (!note.pinned) {\n          continue;\n        }\n      }\n      if (this.isFavoritedFilter) {\n        if (note.favouritedBy.filter(user => user.aspUserId == this.authService.getUserId()).length == 0) {\n          continue;\n        }\n      }\n      //get shared \n      if (this.from.value) {\n        if (!(note.created.getTime() > this.from.value.getTime())) {\n          continue;\n        }\n      }\n      if (this.to.value) {\n        if (!(note.created.getTime() < moment(this.to.value).add('1', 'd').toDate().getTime())) {\n          continue;\n        }\n      }\n      if (this.isSharedFilter) {\n        if (note.sharedWithUsers.length == 0) {\n          continue;\n        }\n      }\n      if (this.isNotSharedFilter) {\n        if (note.sharedWithUsers.length > 0) {\n          continue;\n        }\n      }\n      filteredNotes.push(note);\n    }\n    return filteredNotes;\n  }\n  ifFieldValid(field) {\n    return this.form.get(field)?.invalid && this.form.get(field)?.touched || this.tryToSave && this.form.get(field)?.invalid;\n  }\n  isTextValid() {\n    this.showTextError = !this.textEditor && this.textEditor === '' ? true : false;\n  }\n  close(event) {\n    this.showSend = false;\n    this.showSendAndShare = false;\n  }\n  onClosedNewNote() {\n    console.log('he');\n    this.showAddNote = false;\n  }\n  onNoteChecked(event) {\n    console.log(event);\n    if (event.checked) {\n      this.noteService.checkedNotes.push(event.note);\n    } else {\n      this.noteService.checkedNotes = this.noteService.checkedNotes.filter(el => el.id !== event.note.id);\n    }\n  }\n  onShareMultiple() {\n    const checkedNotes = this.noteService.checkedNotes;\n    if (checkedNotes.length === 0) {\n      this.validateCheckedNotes('Please select at least one note.');\n      return;\n    }\n    const isTeacher = this.authService.isTeacher;\n    if (isTeacher) {\n      this.handleTeacherShareMultiple(checkedNotes);\n    } else {\n      this.handleStudentShareMultiple(checkedNotes);\n    }\n  }\n  onUnShareMultiple() {\n    if (this.noteService.checkedNotes.length === 0) {\n      this.validateCheckedNotes('Please select at least one note.');\n      return;\n    }\n    this.noteService.checkedNotes.forEach(note => {\n      const unshareWith = note.sharedWithUsers.map(user => user.aspUserId);\n      const sendForUnshare = {\n        noteId: note.id,\n        classroomId: Number(this.classroom.id),\n        studentIds: this.getUserIdsForUnshare(unshareWith, note)\n      };\n      this.noteActionsService.unShareNote(sendForUnshare, 'Notes unshared successfully');\n    });\n    this.setGroupActions();\n  }\n  downloadMultiple() {\n    if (this.noteService.checkedNotes.length === 0) {\n      this.validateCheckedNotes('Please select at least one note.');\n      return;\n    }\n    for (let note of this.noteService.checkedNotes) {\n      this.noteActionsService.download(note);\n    }\n  }\n  onRemoveNoteMultiple() {\n    this.initConfirmDeleteNoteDialogListener();\n    if (this.noteService.checkedNotes.length === 0) {\n      this.validateCheckedNotes('Please select at least one note.');\n      return;\n    }\n    this.confirmDialogService.setProperties({\n      confirmMessage: `\n      Are you sure you want to delete the selected notes?`,\n      acceptBtnLabel: 'Yes',\n      rejectBtnLabel: 'No',\n      action: 'delete-note',\n      confirmIcon: '/assets/icons/clock-dialog-icon.svg',\n      showHeader: true,\n      dialogType: 'custom',\n      headerClass: 'my-custom-header-class'\n    });\n    this.confirmDialogService.show();\n  }\n  getUserIdsForUnshare(unshareWith, note) {\n    if (this.authService.isTeacher) {\n      return unshareWith;\n    } else if (this.noteService.isMyNote(note)) {\n      return [this.classroom.teacher.aspUserId];\n    }\n    return [this.user.aspUserId];\n  }\n  handleTeacherShareMultiple(checkedNotes) {\n    if (![this.noteViewAccess.TEACHER_VIEW_STUDENTS_CLASSROOM_NOTES, this.noteViewAccess.TEACHER_VIEW_NOTES].includes(this.currentNotesView)) {\n      if (this.classroom.classroomStudents.length === 1) {\n        this.shareNotesMultipleWithClassroom(checkedNotes);\n      } else {\n        this.openShareMultipleWithNoteDialog(checkedNotes);\n      }\n    } else {\n      this.openShareMultipleWithNoteDialog(checkedNotes);\n    }\n  }\n  handleStudentShareMultiple(checkedNotes) {\n    const unshareWith = [];\n    for (let note of checkedNotes) {\n      for (let user of note.sharedWithUsers) {\n        unshareWith.push(user.aspUserId);\n      }\n      const sendForShare = {\n        noteId: note.id,\n        classroomId: +this.classroom.id,\n        studentIds: [this.classroom.teacher.aspUserId]\n      };\n      this.noteActionsService.shareNote(sendForShare, 'Notes unshared successfully');\n    }\n    this.setGroupActions();\n  }\n  shareNotesMultipleWithClassroom(checkedNotes) {\n    for (let note of checkedNotes) {\n      const sendForShare = {\n        noteId: note.id,\n        classroomId: +this.classroom.id,\n        studentIds: [this.classroom.classroomStudents[0].aspUserId]\n      };\n      this.noteActionsService.shareNote(sendForShare, 'Notes shared successfully');\n    }\n  }\n  openShareMultipleWithNoteDialog(checkedNotes) {\n    this.generalService.openDialogWithComponent(this.dialogService, LibraryFileActionsDialogComponent, 440, {\n      action: 'shareWith',\n      dialogsubTitle: '',\n      dialogTitle: 'Share Note with...',\n      preselectedClassroom: this.classroom\n    }, null, result => {\n      console.log(result);\n      if (result && result.action === 'shareWith') {\n        if (!this.generalService.isNullishObject(result)) {\n          for (let note of checkedNotes) {\n            this.noteActionsService.sendOrShare(note, result, this.authService.isStudent);\n          }\n          this.setGroupActions();\n          console.log(result);\n        }\n      }\n    });\n  }\n  initConfirmDeleteNoteDialogListener() {\n    this.confirmDeleteNoteSubscription = this.confirmDialogService.acceptSelected$.pipe(tap(res => {\n      this.confirmDialogService.hide();\n      console.log(res);\n      switch (res.action) {\n        case 'delete-note':\n          console.log('deltenetote');\n          this.onRemoveNoteMultipleAccepted();\n      }\n    })).subscribe();\n    this.subs.add(this.confirmDeleteNoteSubscription);\n  }\n  onRemoveNoteMultipleAccepted() {\n    console.log(this.noteService.checkedNotes);\n    for (let note of this.noteService.checkedNotes) {\n      console.log(note);\n      this.noteActionsService.afterAcceptRemoveNote(note);\n    }\n    this.confirmDeleteNoteSubscription.unsubscribe();\n    this.setGroupActions();\n  }\n  validateCheckedNotes(msg) {\n    if (this.noteService.checkedNotes.length === 0) {\n      this.toastService.setShowToastmessage({\n        severity: 'warn',\n        summary: '',\n        detail: msg\n      });\n      return;\n    }\n  }\n  initClassroomListener() {\n    this.subs.add(this.noteListenersService.currentSelectedClassroom.subscribe(res => {\n      if (this.generalService.isNullishObject(res)) {\n        return;\n      }\n      console.log(this.filteredNotes);\n      this.classroom = res;\n      this.classroomStudents = this.classroom.classroomStudents;\n    }));\n  }\n  static #_ = this.ɵfac = function NotesListComponent_Factory(t) {\n    return new (t || NotesListComponent)(i0.ɵɵdirectiveInject(i1.GeneralService), i0.ɵɵdirectiveInject(i2.NoteService), i0.ɵɵdirectiveInject(i3.ToastService), i0.ɵɵdirectiveInject(i4.ClassroomService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.AuthService), i0.ɵɵdirectiveInject(i6.NoteListenersService), i0.ɵɵdirectiveInject(i7.NoteActionsService), i0.ɵɵdirectiveInject(i8.DialogService), i0.ɵɵdirectiveInject(i9.ConfirmDialogService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NotesListComponent,\n    selectors: [[\"app-notes-list\"]],\n    inputs: {\n      notes: \"notes\",\n      viewTitle: \"viewTitle\",\n      withClassroom: \"withClassroom\",\n      hasAdd: \"hasAdd\",\n      classroom: \"classroom\"\n    },\n    features: [i0.ɵɵProvidersFeature([DialogService, NoteListenersService]), i0.ɵɵNgOnChangesFeature],\n    decls: 15,\n    vars: 10,\n    consts: [[\"notesGeneralTemplate\", \"\"], [1, \"notes-header\"], [1, \"notes-header-title\"], [1, \"notes-header-actions\"], [1, \"notes-header-actions\", \"gap-2\"], [1, \"p-input-icon-right\", \"w-full\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Search\", 1, \"h-2rem\", \"w-full\", \"input-blue\", \"gradient-blue\", \"rounded\", 3, \"ngModelChange\", \"ngModel\"], [1, \"pi\", \"pi-search\", \"text-primary\"], [\"src\", \"/assets/icons/library/group-actions.svg\", \"title\", \"Group Actions\", 1, \"hvr-grow\", 3, \"click\", \"ngClass\"], [\"title\", \"Create Note\", \"src\", \"/assets/icons/library/add-lib.svg\", \"class\", \"add hvr-grow add-note-btn-icon\", 3, \"click\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\"], [\"class\", \"note-menu-row block-gradient border-round-xl font-sm my-2 p-1 px-3 gap-2\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"notes-height\", 4, \"ngIf\"], [\"title\", \"Create Note\", \"src\", \"/assets/icons/library/add-lib.svg\", 1, \"add\", \"hvr-grow\", \"add-note-btn-icon\", 3, \"click\"], [1, \"note-menu-row\", \"block-gradient\", \"border-round-xl\", \"font-sm\", \"my-2\", \"p-1\", \"px-3\", \"gap-2\"], [\"class\", \"note-menu-col\", 3, \"click\", 4, \"ngIf\"], [1, \"note-menu-col\", 3, \"click\"], [1, \"note-menu-icon\"], [\"src\", \"/assets/icons/cloud-download.svg\", \"height\", \"14\"], [1, \"note-menu-text\", \"link-main-color\"], [\"src\", \"/assets/icons/library/share-sm-icon.svg\", \"height\", \"14\"], [\"src\", \"/assets/icons/library/delete.svg\", \"height\", \"14\"], [3, \"closedNewNote\", \"note\", \"type\", \"classroom\", \"showGroupActions\", \"view\"], [1, \"notes-height\"], [\"style\", \"width:100%;height:800px; display: flex; justify-content: center;\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"w-full\", \"notes-height\", 3, \"scrollable\"], [\"header\", \"All\"], [\"id\", \"classroom-info\", 1, \"h-full\"], [4, \"ngFor\", \"ngForOf\"], [1, \"\", 3, \"header\"], [\"class\", \"asked-for-help-main scrollable h-full notes-height\", 3, \"fromRoot\", \"scrollWindow\", 4, \"ngIf\"], [3, \"noteChecked\", \"isInMine\", \"note\", \"classroom\", \"withClassroom\", \"showGroupActions\", \"view\"], [1, \"asked-for-help-main\", \"scrollable\", \"h-full\", \"notes-height\", 3, \"fromRoot\", \"scrollWindow\"], [\"class\", \"flex flex-column gap-1 align-items-center justify-content-center h-full\", 4, \"ngIf\"], [1, \"flex\", \"flex-column\", \"gap-1\", \"align-items-center\", \"justify-content-center\", \"h-full\"], [\"src\", \"/assets/icons/library/add-lib.svg\", 1, \"add\", \"lg\", \"hvr-grow\", 3, \"click\"], [1, \"font-base\", \"max-w-14rem\", \"text-center\"], [\"src\", \"/assets/icons/library/file_manager_emp.png\", \"alt\", \"file manager empty icon\", 1, \"add\", \"empty\", \"hvr-grow\"], [2, \"width\", \"100%\", \"height\", \"800px\", \"display\", \"flex\", \"justify-content\", \"center\"], [1, \"spinner\", 2, \"margin-top\", \"200px\"]],\n    template: function NotesListComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n        i0.ɵɵtext(2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"span\", 5)(6, \"input\", 6);\n        i0.ɵɵtwoWayListener(\"ngModelChange\", function NotesListComponent_Template_input_ngModelChange_6_listener($event) {\n          i0.ɵɵtwoWayBindingSet(ctx.term, $event) || (ctx.term = $event);\n          return $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(7, \"i\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"img\", 8);\n        i0.ɵɵlistener(\"click\", function NotesListComponent_Template_img_click_8_listener() {\n          return ctx.setGroupActions();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(9, NotesListComponent_img_9_Template, 1, 0, \"img\", 9);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(10, \"div\", 10);\n        i0.ɵɵtemplate(11, NotesListComponent_div_11_Template, 9, 6, \"div\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(12);\n        i0.ɵɵtemplate(13, NotesListComponent_ng_container_13_Template, 2, 1, \"ng-container\", 12)(14, NotesListComponent_div_14_Template, 6, 3, \"div\", 13);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\" \", ctx.viewTitle, \" \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtwoWayProperty(\"ngModel\", ctx.term);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c0, ctx.showGroupActions));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.hasAdd);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.showGroupActions);\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" \", (ctx.classroomStudents && ctx.classroomStudents.length === 1 || !ctx.withClassroom) && ctx.hasAdd, \"\\n\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", (ctx.currentNotesView === ctx.noteViewAccess.TEACHER_VIEW_CLASSROOM_NOTES || ctx.currentNotesView === ctx.noteViewAccess.STUDENT_VIEW_CLASSROOM_NOTES || ctx.currentNotesView === ctx.noteViewAccess.TEACHER_VIEW_NOTES) && ctx.showAddNote);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n      }\n    },\n    dependencies: [i10.NgClass, i10.NgForOf, i10.NgIf, i10.NgTemplateOutlet, i11.TabView, i11.TabPanel, i12.DefaultValueAccessor, i12.NgControlStatus, i12.NgModel, i13.MatProgressSpinner, i14.NoteDetailsComponent, i15.FilterPipe],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.modal[_ngcontent-%COMP%] {\\n  min-width: 50vw;\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown:not(.p-disabled):hover {\\n  border: 2px solid rgb(228, 185, 84);\\n}\\n\\n  .no-label > .p-button-label {\\n  display: none;\\n}\\n\\ninput[type=file][_ngcontent-%COMP%] {\\n  visibility: hidden;\\n  width: 10px;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%] {\\n  position: relative;\\n  padding: 10px;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%]::-webkit-calendar-picker-indicator {\\n  color: transparent;\\n  background: none;\\n  z-index: 1;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%]:before {\\n  color: transparent;\\n  background: none;\\n  display: block;\\n  font-family: \\\"FontAwesome\\\";\\n  content: \\\"\\\\f073\\\";\\n  \\n\\n  width: 20px;\\n  height: 25px;\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  color: #999;\\n}\\n\\n.image-preview[_ngcontent-%COMP%] {\\n  height: 10rem;\\n  margin: 1rem 0;\\n  border-radius: 50%;\\n  border: 3px solid var(--main-color);\\n}\\n\\n.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 100%;\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-header {\\n  padding: clamp(0.38rem, 0.47vw + 0.28rem, 0.75rem);\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-items .p-dropdown-item {\\n  margin-bottom: clamp(0.38rem, 0.47vw + 0.28rem, 0.75rem);\\n}\\n\\n.country-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: clamp(0.75rem, 0.31vw + 0.69rem, 1rem);\\n  font-size: clamp(0.88rem, 0.16vw + 0.84rem, 1rem);\\n}\\n.country-item[_ngcontent-%COMP%]   .country-name[_ngcontent-%COMP%] {\\n  white-space: pre-line; \\n\\n}\\n.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  \\n\\n\\n\\n\\n\\n  \\n\\n  display: inline-block;\\n  vertical-align: middle;\\n}\\n.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  clip-path: circle();\\n}\\n.country-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 32px;\\n  margin-right: 0.5rem;\\n}\\n.country-item[_ngcontent-%COMP%]   img.smaller[_ngcontent-%COMP%] {\\n  width: 16px;\\n}\\n\\n.info-element[_ngcontent-%COMP%] {\\n  padding: 10px 0;\\n}\\n\\n.responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.note[_ngcontent-%COMP%] {\\n  border-radius: 40px;\\n  border: 1px solid #eaf0f5;\\n  margin-top: 2px;\\n  position: relative;\\n}\\n.note[_ngcontent-%COMP%]::before {\\n  pointer-events: none;\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  border-radius: 30px;\\n  padding: 2px;\\n}\\n.note.isFavNote[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(90deg, hsl(221, 90%, 59%) 0%, hsl(241, 76%, 67%) 29%, hsl(261, 66%, 64%) 53%, hsl(279, 56%, 60%) 68%, hsl(296, 48%, 56%) 77%, hsl(311, 53%, 56%) 84%, hsl(321, 62%, 57%) 89%, hsl(330, 67%, 59%) 93%, hsl(337, 71%, 60%) 97%, hsl(344, 72%, 61%) 100%);\\n}\\n.note.isFavNote[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.note.isFavNote[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .note.isFavNote[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%] {\\n  border: 1px solid white;\\n}\\n.note.isFavNote[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .note.isFavNote[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  color: var(--main-color);\\n}\\n.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .section-arrow[_ngcontent-%COMP%] {\\n  width: 40px;\\n}\\n.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  width: 100%;\\n}\\n@media screen and (max-width: 768px) {\\n  .note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    margin-left: 20px;\\n  }\\n}\\n.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%] {\\n  margin-left: 20px;\\n}\\n@media screen and (max-width: 768px) {\\n  .note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%] {\\n    margin-left: 0px;\\n  }\\n}\\n.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%]   .note-dates[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-top: 10px;\\n}\\n@media screen and (max-width: 768px) {\\n  .note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%]   .note-dates[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n\\n.note-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n@media only screen and (max-width: 768px) {\\n  .note-info[_ngcontent-%COMP%] {\\n    margin-top: 0px;\\n  }\\n}\\n.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n@media only screen and (max-width: 768px) {\\n  .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%] {\\n    justify-content: end;\\n  }\\n}\\n.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-right: 10px;\\n  margin-left: auto;\\n}\\n@media only screen and (max-width: 768px) {\\n  .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n  }\\n}\\n.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-icon[_ngcontent-%COMP%], .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-text[_ngcontent-%COMP%] {\\n  margin-left: 4px;\\n  cursor: pointer;\\n}\\n@media screen and (max-width: 768px) {\\n  .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-icon[_ngcontent-%COMP%], .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-text[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n  }\\n}\\n.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  display: flex;\\n  justify-content: center;\\n}\\n.note-info[_ngcontent-%COMP%]   .note-info-bottom[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: end;\\n  text-align: right;\\n  margin-right: 10px;\\n  font-size: 0.725rem;\\n  color: #2d3b8e;\\n}\\n\\n.note-content[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  transition: height 0.3s ease-out;\\n}\\n.rte[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  border-radius: 20px;\\n  z-index: 0 !important;\\n}\\n\\n.note-text-content[_ngcontent-%COMP%] {\\n  padding: 20px 30px;\\n}\\n\\n.update-button[_ngcontent-%COMP%] {\\n  padding: 5px 30px;\\n  background-color: var(--light-purple);\\n  text-align: center;\\n  border-radius: 10px;\\n  color: white;\\n  margin-top: 15px;\\n  cursor: pointer;\\n}\\n\\n.update-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--main-color);\\n}\\n\\n.section-arrow[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  transition-duration: 0.2s;\\n  transition-property: transform;\\n  cursor: pointer;\\n}\\n\\n.note-burger[_ngcontent-%COMP%], .note-burger-group[_ngcontent-%COMP%] {\\n  position: relative;\\n  border-radius: 50%;\\n  min-width: 36px;\\n  min-height: 36px;\\n  border: 2px solid #6563ec;\\n  display: flex;\\n  align-items: center;\\n  box-sizing: border-box;\\n  justify-content: center;\\n  cursor: pointer;\\n}\\n.note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%] {\\n  width: 4px;\\n  height: 4px;\\n  border-radius: 50%;\\n  background-color: #6563ec;\\n  margin: 2px;\\n}\\n.note-burger[_ngcontent-%COMP%]   .inner-circle-group[_ngcontent-%COMP%], .note-burger-group[_ngcontent-%COMP%]   .inner-circle-group[_ngcontent-%COMP%] {\\n  width: 34px;\\n  height: 34px;\\n  border-radius: 50%;\\n  background-color: transparent;\\n}\\n\\n.note-menu[_ngcontent-%COMP%] {\\n  width: 300px;\\n  position: absolute;\\n  right: 30px;\\n  top: 69px;\\n  background-color: white;\\n  border-radius: 12px;\\n  border: 1px solid var(--main-color);\\n  box-sizing: border-box;\\n  transition-duration: 0.2s;\\n  transition-property: transform;\\n  background-color: white;\\n  z-index: 1;\\n  display: none;\\n  flex-direction: column;\\n  padding: 10px;\\n}\\n@media screen and (max-width: 1124px) {\\n  .note-menu[_ngcontent-%COMP%] {\\n    right: 30px;\\n  }\\n}\\n.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%] {\\n  margin-top: 5px;\\n  display: flex;\\n  align-items: center;\\n}\\n.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-text[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n  cursor: pointer;\\n}\\n.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  display: flex;\\n  justify-content: center;\\n}\\n.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n\\n[_nghost-%COMP%]     .e-toolbar-items {\\n  border-radius: 20px !important;\\n}\\n[_nghost-%COMP%]     .e-rte-toolbar {\\n  border-radius: 20px 20px 0px 0px !important;\\n}\\n[_nghost-%COMP%]     .e-rte-content {\\n  border-radius: 0 0 20px 20px !important;\\n  border-top: 0px !important;\\n}\\n\\n.plain[_ngcontent-%COMP%] {\\n  border-radius: 0px;\\n  margin: -13px 2px 0 2px;\\n  border-top: 0;\\n  border-bottom-right-radius: 12px;\\n  border-bottom-left-radius: 12px;\\n  position: relative;\\n}\\n\\n.filter-white[_ngcontent-%COMP%] {\\n  filter: invert(100%) sepia(0%) saturate(1%) hue-rotate(328deg) brightness(200%) contrast(101%);\\n}\\n\\n.note-info-width[_ngcontent-%COMP%] {\\n  min-width: 10rem;\\n}\\n\\n.note-title-text[_ngcontent-%COMP%] {\\n  font-weight: normal;\\n  font-size: 1rem;\\n  letter-spacing: 0.01em;\\n  text-align: left;\\n  max-width: 17rem;\\n}\\n@media only screen and (max-width: 768px) {\\n  .note-title-text[_ngcontent-%COMP%] {\\n    max-width: 9rem;\\n    font-size: 0.825rem;\\n  }\\n}\\n\\n.accordion[_ngcontent-%COMP%] {\\n  border-radius: 4px;\\n  margin-top: 10px;\\n}\\n\\n.header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 8px;\\n  cursor: pointer;\\n  border-radius: 31px;\\n  box-shadow: 3px 3px 6px 2px rgba(0, 0, 0, 0.16);\\n  z-index: 4;\\n  position: relative;\\n  border: 1px solid transparent;\\n}\\n.header.isPinned[_ngcontent-%COMP%] {\\n  color: white;\\n  background-image: linear-gradient(90deg, hsl(232, 49%, 54%) 0%, hsl(232, 49%, 54%) 13%, hsl(232, 49%, 54%) 25%, hsl(232, 49%, 54%) 37%, hsl(232, 49%, 54%) 50%, hsl(232, 49%, 54%) 63%, hsl(232, 49%, 54%) 75%, hsl(232, 49%, 54%) 87%, hsl(232, 49%, 54%) 100%);\\n}\\n.header.isPinned[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.isPinned[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%] {\\n  border: 2px solid white;\\n}\\n.header.isPinned[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.isPinned[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n.header.isPinned[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%] {\\n  background: transparent;\\n  border: 2px solid white;\\n}\\n.header.isPinnedAndFavourite[_ngcontent-%COMP%] {\\n  color: white;\\n  background-image: linear-gradient(90deg, hsl(231, 46%, 48%) 0%, hsl(231, 46%, 51%) 11%, hsl(232, 49%, 54%) 22%, hsl(232, 53%, 57%) 33%, hsl(232, 57%, 60%) 44%, hsl(233, 62%, 63%) 56%, hsl(233, 67%, 65%) 67%, hsl(233, 74%, 68%) 78%, hsl(233, 82%, 71%) 89%, hsl(233, 92%, 74%) 100%);\\n}\\n.header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%] {\\n  border: 2px solid white;\\n}\\n.header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n.header.isPinnedAndFavourite[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%] {\\n  background: transparent;\\n  border: 2px solid white;\\n}\\n.header.opened[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%] {\\n  border: 1px solid transparent;\\n  color: white;\\n  background-color: #7f8dfa;\\n}\\n.header.opened.activated[_ngcontent-%COMP%], .header.opened2.activated[_ngcontent-%COMP%] {\\n  background-color: #7f8dfa;\\n}\\n.header.opened[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.opened[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%] {\\n  border: 2px solid white;\\n}\\n.header.opened[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.opened[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n.header.opened[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%] {\\n  border: 1px solid white;\\n  background: transparent;\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.content[_ngcontent-%COMP%] {\\n  padding: 10px;\\n}\\n\\n.down-arrow[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease-in-out;\\n}\\n\\n.down-arrow.pi-chevron-up[_ngcontent-%COMP%] {\\n  transform: rotate(360deg);\\n}\\n\\n.circle[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(180deg, hsl(249, 92%, 74%) 0%, hsl(248, 90%, 73%) 11%, hsl(247, 88%, 72%) 22%, hsl(246, 86%, 71%) 33%, hsl(245, 84%, 70%) 44%, hsl(244, 82%, 68%) 56%, hsl(242, 80%, 67%) 67%, hsl(241, 79%, 66%) 78%, hsl(240, 77%, 65%) 89%, hsl(238, 76%, 63%) 100%);\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.circle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 20px;\\n}\\n\\n.rotate[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n  transition: transform 0.3s ease-in-out;\\n}\\n\\n.accordion-content[_ngcontent-%COMP%] {\\n  max-height: 0;\\n  overflow: hidden;\\n}\\n\\n.open[_ngcontent-%COMP%] {\\n  background-color: white;\\n  margin-top: -20px;\\n  border-bottom-left-radius: 30px;\\n  border-bottom-right-radius: 30px;\\n  height: 370px;\\n  max-height: 370px;\\n  padding: 20px 3px 3px 3px;\\n}\\n@media only screen and (max-width: 768px) {\\n  .open[_ngcontent-%COMP%] {\\n    height: 410px;\\n    max-height: 410px;\\n  }\\n}\\n\\n.btn-action[_ngcontent-%COMP%] {\\n  width: 200px;\\n}\\n@media only screen and (max-width: 768px) {\\n  .btn-action[_ngcontent-%COMP%] {\\n    width: auto;\\n  }\\n}\\n\\n[_nghost-%COMP%]     .p-editor-container .p-editor-toolbar.ql-snow {\\n  border: none;\\n  padding: 0 !important;\\n}\\n[_nghost-%COMP%]     .p-editor-container .p-editor-content .ql-editor {\\n  background: transparent;\\n  padding: 4px;\\n}\\n[_nghost-%COMP%]     .p-editor-container .p-editor-content .ql-editor p {\\n  font-size: 1rem;\\n}\\n[_nghost-%COMP%]     .p-editor-container .p-editor-content .ql-editor[contenteditable=true] {\\n  border: 1px solid var(--my-gray);\\n  border-radius: 10px;\\n}\\n[_nghost-%COMP%]     .circle.ql-snow {\\n  border: none;\\n}\\n[_nghost-%COMP%]     .p-editor-container .p-editor-content.ql-snow {\\n  border: none;\\n}\\n[_nghost-%COMP%]     .ql-snow .ql-tooltip {\\n  transform: translate(80%, -50%);\\n}\\n@media only screen and (max-width: 768px) {\\n  [_nghost-%COMP%]     .ql-snow .ql-tooltip {\\n    transform: translate(45%, -50%);\\n  }\\n}\\n\\n  .p-menu .p-menuitem-link {\\n  padding: 0.5rem 1rem !important;\\n}\\n\\n#add-note[_ngcontent-%COMP%] {\\n  background-color: var(--gray);\\n  padding: 30px;\\n  border-radius: 12px;\\n  margin-top: 30px;\\n}\\n\\n.add-note-btn-icon[_ngcontent-%COMP%] {\\n  width: 25px;\\n  height: 25px;\\n}\\n\\n.notes-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.notes-header[_ngcontent-%COMP%]   .notes-header-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  color: var(--main-color);\\n  font-weight: bold;\\n}\\n.notes-header[_ngcontent-%COMP%]   .notes-header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.notes-header[_ngcontent-%COMP%]   .notes-header-actions[_ngcontent-%COMP%]   div[_ngcontent-%COMP%] {\\n  margin-right: 0.7em;\\n}\\n.notes-header[_ngcontent-%COMP%]   .notes-header-actions[_ngcontent-%COMP%]   .light-purple-circle-button[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n}\\n\\n.note-menu-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-evenly;\\n  border-radius: 40px;\\n}\\n.note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  position: relative;\\n}\\n.note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-text[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n.note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  display: flex;\\n  justify-content: center;\\n}\\n\\n.rte[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  border-radius: 20px;\\n  z-index: 0 !important;\\n}\\n\\n[_nghost-%COMP%]     .e-toolbar-items {\\n  border-radius: 20px !important;\\n}\\n[_nghost-%COMP%]     .e-rte-toolbar {\\n  border-radius: 20px 20px 0px 0px !important;\\n}\\n[_nghost-%COMP%]     .e-rte-content {\\n  border-radius: 0 0 20px 20px !important;\\n  border-top: 0px !important;\\n}\\n[_nghost-%COMP%]     .e-date-icon {\\n  position: absolute !important;\\n  left: 0 !important;\\n}\\n[_nghost-%COMP%]     .e-input {\\n  margin-left: 30px !important;\\n}\\n\\n.notes-popup[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 40px;\\n  width: 200px;\\n  border-radius: 12px;\\n  background-color: white;\\n  color: var(--main-color);\\n  right: 0;\\n  box-shadow: 0px -4px 4px rgba(0, 0, 0, 0.25);\\n  max-height: 400px;\\n  z-index: 10000;\\n}\\n\\n.notes-filters-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: bold;\\n}\\n\\n.notes-filters-filters[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n}\\n.notes-filters-filters[_ngcontent-%COMP%]   .filter-row[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n  display: flex;\\n  align-items: center;\\n}\\n.notes-filters-filters[_ngcontent-%COMP%]   .filter-row[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%] {\\n  margin-left: 15px;\\n}\\n.notes-filters-filters[_ngcontent-%COMP%]   .filter-col[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n}\\n\\n.send-classroom-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: bold;\\n}\\n\\n.send-classroom-user[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  margin-left: 10px;\\n  display: flex;\\n}\\n\\n.send-button[_ngcontent-%COMP%] {\\n  background-color: var(--main-color);\\n  width: 100%;\\n  position: sticky;\\n  bottom: 0;\\n  left: 0;\\n  padding: 8px;\\n  box-sizing: border-box;\\n  color: white;\\n  text-align: center;\\n  border-radius: 12px;\\n  cursor: pointer;\\n}\\n\\n.notes-height[_ngcontent-%COMP%] {\\n  height: calc(100% - 50px);\\n}\\n\\n.btns[_ngcontent-%COMP%]   div[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n[_nghost-%COMP%]     .ngx-pagination {\\n  padding: 0 !important;\\n}\\n\\n.filter-blue[_ngcontent-%COMP%] {\\n  opacity: 0.7;\\n}\\n\\n.add[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n}\\n.add.lg[_ngcontent-%COMP%] {\\n  width: 2.1rem;\\n  height: 2.1rem;\\n}\\n.add.empty[_ngcontent-%COMP%] {\\n  width: 7.1rem;\\n  height: auto;\\n}\\n\\n  .p-tabview .p-tabview-panels {\\n  background-color: transparent;\\n  padding: 0;\\n  justify-content: center;\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n  .p-tabview .p-tabview-nav {\\n  background: transparent;\\n  border: none;\\n}\\n\\n  .p-tabview .p-tabview-nav li .p-tabview-nav-link,   .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {\\n  background: transparent;\\n  border: none;\\n}\\n\\n  .p-tabview .p-tabview-nav .p-tabview-ink-bar {\\n  background-color: #8a7af7;\\n  bottom: 5px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["UntypedFormControl", "UntypedFormGroup", "Validators", "SubSink", "moment", "NoteListenersService", "NoteViewAccess", "LibraryFileActionsDialogComponent", "DialogService", "tap", "i0", "ɵɵelementStart", "ɵɵlistener", "NotesListComponent_img_9_Template_img_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "showAddNote", "ɵɵelementEnd", "NotesListComponent_div_11_div_1_Template_div_click_0_listener", "_r4", "onShareMultiple", "ɵɵelement", "ɵɵtext", "NotesListComponent_div_11_div_2_Template_div_click_0_listener", "_r5", "onUnShareMultiple", "NotesListComponent_div_11_div_8_Template_div_click_0_listener", "_r6", "onRemoveNoteMultiple", "ɵɵtemplate", "NotesListComponent_div_11_div_1_Template", "NotesListComponent_div_11_div_2_Template", "NotesListComponent_div_11_Template_div_click_3_listener", "_r3", "downloadMultiple", "NotesListComponent_div_11_div_8_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction2", "_c1", "noteViewAccess", "TEACHER_VIEW_STUDENTS_CLASSROOM_NOTES", "STUDENT_VIEW_TEACHERS_CLASSROOM_NOTES", "includes", "currentNotesView", "TEACHER_VIEW_NOTES", "hasAdd", "NotesListComponent_ng_container_13_div_1_Template_app_note_details_closedNewNote_1_listener", "_r7", "onClosedNewNote", "classroom", "showGroupActions", "noteListenersService", "getNotesViewAccess", "withClassroom", "ɵɵelementContainerStart", "NotesListComponent_ng_container_13_div_1_Template", "classroomStudents", "length", "ɵɵelementContainer", "NotesListComponent_div_14_ng_container_1_ng_container_1_Template", "notesGeneralTemplate_r8", "_c2", "filteredNotes", "NotesListComponent_div_14_ng_container_2_ng_container_5_ng_container_2_Template", "selectedStudent_r9", "firstName", "ɵɵpureFunction3", "_c3", "NotesListComponent_div_14_ng_container_2_ng_container_4_Template", "NotesListComponent_div_14_ng_container_2_ng_container_5_Template", "NotesListComponent_div_14_ng_template_3_ng_container_0_div_1_Template_app_note_details_closedNewNote_1_listener", "_r10", "classroom_r11", "NotesListComponent_div_14_ng_template_3_ng_container_0_div_1_Template", "NotesListComponent_div_14_ng_template_3_div_1_Template_app_note_details_noteChecked_1_listener", "$event", "_r12", "onNoteChecked", "note_r13", "NotesListComponent_div_14_ng_template_3_div_3_div_1_Template_img_click_1_listener", "_r14", "NotesListComponent_div_14_ng_template_3_div_3_ng_container_2_ng_container_4_Template", "NotesListComponent_div_14_ng_template_3_div_3_ng_container_2_ng_container_5_Template", "authService", "isStudent", "<PERSON><PERSON><PERSON>er", "NotesListComponent_div_14_ng_template_3_div_3_div_1_Template", "NotesListComponent_div_14_ng_template_3_div_3_ng_container_2_Template", "getFilteredNotes", "student_r15", "NotesListComponent_div_14_ng_template_3_ng_container_0_Template", "NotesListComponent_div_14_ng_template_3_div_1_Template", "NotesListComponent_div_14_ng_template_3_div_3_Template", "TEACHER_VIEW_CLASSROOM_NOTES", "ɵɵpipeBind2", "ɵɵpureFunction1", "_c4", "term", "filteredNotes_r16", "NotesListComponent_div_14_ng_container_1_Template", "NotesListComponent_div_14_ng_container_2_Template", "NotesListComponent_div_14_ng_template_3_Template", "ɵɵtemplateRefExtractor", "NotesListComponent_div_14_div_5_Template", "isLoading", "NotesListComponent", "constructor", "generalService", "noteService", "toastService", "classroomService", "cd", "noteActionsService", "dialogService", "confirmDialogService", "notes", "viewTitle", "subs", "showFilters", "tryToSave", "showTextError", "isSharedFilter", "isNotSharedFilter", "isPinnedFilter", "isFavoritedFilter", "fromFilterValue", "toFilterValue", "datePickerOpen", "classrooms", "showSend", "showSendAndShare", "usersToSend", "currentSelectedClassroomId", "showingMine", "inClassroom", "textEditor", "checkedNotes", "user", "role", "maxSize", "directionLinks", "autoHide", "to", "from", "form", "isInMine", "ngOnInit", "getLoggedInUser", "initClassroomListener", "title", "validators", "required", "text", "ngOnChanges", "ngAfterViewInit", "detectChanges", "ngOnDestroy", "unsubscribe", "setGroupActions", "ngAfterContentInit", "setTimeout", "targetStudent", "filter", "note", "sharedWithUsers", "some", "aspUserId", "created<PERSON>y", "areNotFavorited", "favourited<PERSON>y", "id", "getUserId", "areNotPinned", "pinned", "getNotesFiltered", "value", "created", "getTime", "add", "toDate", "push", "ifFieldValid", "field", "get", "invalid", "touched", "isTextValid", "close", "event", "console", "log", "checked", "el", "validateCheckedNotes", "handleTeacherShareMultiple", "handleStudentShareMultiple", "for<PERSON>ach", "unshareWith", "map", "sendForUnshare", "noteId", "classroomId", "Number", "studentIds", "getUserIdsForUnshare", "unShareNote", "download", "initConfirmDeleteNoteDialogListener", "setProperties", "confirmMessage", "acceptBtnLabel", "rejectBtnLabel", "action", "confirmIcon", "showHeader", "dialogType", "headerClass", "show", "isMyNote", "teacher", "shareNotesMultipleWithClassroom", "openShareMultipleWithNoteDialog", "sendForShare", "shareNote", "openDialogWithComponent", "dialogsubTitle", "dialogTitle", "preselectedClassroom", "result", "isNullishObject", "sendOrShare", "confirmDeleteNoteSubscription", "acceptSelected$", "pipe", "res", "hide", "onRemoveNoteMultipleAccepted", "subscribe", "afterAcceptRemoveNote", "msg", "setShowToastmessage", "severity", "summary", "detail", "currentSelectedClassroom", "_", "ɵɵdirectiveInject", "i1", "GeneralService", "i2", "NoteService", "i3", "ToastService", "i4", "ClassroomService", "ChangeDetectorRef", "i5", "AuthService", "i6", "i7", "NoteActionsService", "i8", "i9", "ConfirmDialogService", "_2", "selectors", "inputs", "features", "ɵɵProvidersFeature", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "NotesListComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "NotesListComponent_Template_input_ngModelChange_6_listener", "ɵɵtwoWayBindingSet", "NotesListComponent_Template_img_click_8_listener", "NotesListComponent_img_9_Template", "NotesListComponent_div_11_Template", "NotesListComponent_ng_container_13_Template", "NotesListComponent_div_14_Template", "ɵɵtextInterpolate1", "ɵɵtwoWayProperty", "_c0", "STUDENT_VIEW_CLASSROOM_NOTES"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\note\\notes\\notes-list\\notes-list.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\note\\notes\\notes-list\\notes-list.component.html"], "sourcesContent": ["/* The `NotesListComponent` is a component in an Angular application that displays a list of notes and\r\nprovides functionality for filtering, sorting, and performing actions on the notes. */\r\nimport { ChangeDetectorRef, Component, Input, OnInit, ViewChild } from '@angular/core';\r\nimport { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\r\nimport { Note, SendShareNoteRequest } from 'src/app/core/models/note.model';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { NoteService } from 'src/app/core/services/note.service';\r\nimport { Classroom, ClassroomType } from 'src/app/core/models/classroom.model';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { User } from 'src/app/core/models/user.model';\r\nimport { SubSink } from 'subsink';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport * as moment from 'moment';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { NoteActionsService } from 'src/app/core/services/note-actions.service';\r\nimport { NoteListenersService, NoteViewAccess } from 'src/app/core/services/note-listeners.service';\r\nimport { LibraryFileActionsDialogComponent } from 'src/app/modules/library/library/components/library-file-actions-dialog/library-file-actions-dialog.component';\r\nimport { DialogService } from 'primeng/dynamicdialog';\r\nimport { ConfirmDialogService } from 'src/app/core/services/confirm-dialog.service';\r\nimport { take, tap } from 'rxjs/operators';\r\nimport { Subscription } from 'rxjs';\r\n\r\ninterface SendShareEvent {\r\n  usersSelected: User[];\r\n  kind: string;\r\n  userClassroomMap: Map<User[], number>;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-notes-list',\r\n  templateUrl: './notes-list.component.html',\r\n  styleUrls: ['./notes-list.component.scss'],\r\n  providers: [DialogService, NoteListenersService]\r\n\r\n})\r\nexport class NotesListComponent implements OnInit {\r\n  @Input() notes: Note[] = [];\r\n  @Input() viewTitle = 'My Notes';\r\n  @Input() withClassroom = false;\r\n  @Input() hasAdd = true;\r\n  @Input() classroom: Classroom = {} as Classroom;\r\n  private subs = new SubSink();\r\n  showFilters: boolean = false;\r\n  showGroupActions: boolean = true;\r\n  term: any;\r\n  sendTerm: any;\r\n  showAddNote: boolean = false;\r\n  tryToSave: boolean = false;\r\n  showTextError: boolean = false;\r\n  isSharedFilter: boolean = false;\r\n  isNotSharedFilter: boolean = false;\r\n  isPinnedFilter: boolean = false;\r\n  isFavoritedFilter: boolean = false;\r\n  fromFilterValue: Date = {} as Date;\r\n  toFilterValue: Date = {} as Date;\r\n  datePickerOpen: boolean = false;\r\n  filteredNotes: Note[] = [];\r\n  classrooms: Classroom[] = [];\r\n  showSend: boolean = false;\r\n  showSendAndShare: boolean = false;\r\n  usersToSend: User[] = [];\r\n  currentSelectedClassroomId: number = 0;\r\n  showingMine: boolean = true;\r\n  inClassroom: boolean = false;\r\n  isLoading: boolean = false;\r\n  textEditor = '';\r\n  checkedNotes: Note[] = [];\r\n  classroomStudents: User[] = [];\r\n  public user: User = {} as User;\r\n  public role: string = \"\";\r\n  public maxSize: number = 6;\r\n  public directionLinks: boolean = true;\r\n  public autoHide: boolean = false;\r\n  public to: any = {} as any;\r\n  public from: any = {} as any;\r\n  public form: UntypedFormGroup = new UntypedFormGroup({});\r\n  isInMine: boolean = false;\r\n  public noteViewAccess = NoteViewAccess;\r\n  confirmDeleteNoteSubscription!: Subscription;\r\n\r\n  constructor(\r\n    private generalService: GeneralService,\r\n    private noteService: NoteService,\r\n    private toastService: ToastService,\r\n    private classroomService: ClassroomService,\r\n    private cd: ChangeDetectorRef,\r\n    private authService: AuthService,\r\n    public noteListenersService: NoteListenersService,\r\n    public noteActionsService: NoteActionsService,\r\n    public dialogService: DialogService,\r\n    public confirmDialogService: ConfirmDialogService,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.user = this.authService.getLoggedInUser();\r\n    this.role = this.user.role!\r\n    this.initClassroomListener();\r\n    // this.notSharedFilter(true);\r\n    this.form = new UntypedFormGroup({\r\n      title: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      text: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n    });\r\n  }\r\n\r\n  ngOnChanges() {\r\n    if (this.classroom) {\r\n      this.classroomStudents = this.classroom.classroomStudents;\r\n    }\r\n    this.filteredNotes = this.notes\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    this.showGroupActions = false;\r\n    this.cd.detectChanges()\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n    this.noteListenersService.setGroupActions(false);\r\n    this.noteService.checkedNotes = [];\r\n  }\r\n\r\n  ngAfterContentInit() {\r\n  }\r\n\r\n  get currentNotesView() {\r\n    return this.noteListenersService.getNotesViewAccess(this.hasAdd, this.withClassroom);\r\n  }\r\n\r\n  //listener to change ui in nite details\r\n  setGroupActions() {\r\n    this.showGroupActions = !this.showGroupActions;\r\n    if (!this.showGroupActions) {\r\n      this.noteService.checkedNotes = [];\r\n    }\r\n    setTimeout(() => {\r\n      this.noteListenersService.setGroupActions(this.showGroupActions);\r\n    }, 300);\r\n  }\r\n\r\n  /**\r\n   * Filters the notes based on whether they are shared with the given student.\r\n   *\r\n   * @param targetStudent - The student user to filter by.\r\n   * @returns An array of notes that are shared with the given student.\r\n   */\r\n  getFilteredNotes(targetStudent: User): Note[] {\r\n    if (this.hasAdd) {\r\n    return this.filteredNotes.filter((note: Note) => {\r\n      return targetStudent\r\n        ? note.sharedWithUsers.some(\r\n          (user: User) => user.aspUserId === targetStudent.aspUserId\r\n        )\r\n        : true;\r\n    });\r\n    } else {\r\n      return this.filteredNotes.filter((note: Note) => {\r\n        return targetStudent\r\n          ? note.createdBy.aspUserId === targetStudent.aspUserId : true;\r\n      });\r\n    }\r\n\r\n  }\r\n\r\n  areNotFavorited() {\r\n    for (let note of this.noteService.checkedNotes) {\r\n      if (note.favouritedBy.filter((user: User) => user.id == this.authService.getUserId()).length == 0) return true;\r\n    }\r\n    return false\r\n  }\r\n\r\n  areNotPinned() {\r\n    for (let note of this.noteService.checkedNotes) {\r\n      if (!note.pinned) return true;\r\n    }\r\n    return false\r\n  }\r\n\r\n\r\n  /**\r\n   * Filters the notes based on the selected filters and returns the filtered notes.\r\n   *\r\n   * @return {Note[]} An array of Note objects that pass the applied filters.\r\n   */\r\n  getNotesFiltered() {\r\n    let filteredNotes: Note[] = [];\r\n    for (let note of this.notes) {\r\n      if (this.isPinnedFilter) {\r\n        if (!note.pinned) {\r\n          continue;\r\n        }\r\n      }\r\n      if (this.isFavoritedFilter) {\r\n        if (note.favouritedBy.filter((user: User) => user.aspUserId == this.authService.getUserId()).length == 0) {\r\n          continue;\r\n        }\r\n      }\r\n      //get shared \r\n      if (this.from.value) {\r\n        if (!(note.created.getTime() > this.from.value.getTime())) {\r\n          continue;\r\n        }\r\n      }\r\n      if (this.to.value) {\r\n        if (!(note.created.getTime() < moment(this.to.value).add('1', 'd').toDate().getTime())) {\r\n          continue;\r\n        }\r\n      }\r\n      if (this.isSharedFilter) {\r\n        if (note.sharedWithUsers.length == 0) {\r\n          continue\r\n        }\r\n      }\r\n      if (this.isNotSharedFilter) {\r\n        if (note.sharedWithUsers.length > 0) {\r\n          continue\r\n        }\r\n      }\r\n      filteredNotes.push(note)\r\n    }\r\n    return filteredNotes\r\n  }\r\n\r\n  ifFieldValid(field: string) {\r\n    return ((this.form.get(field)?.invalid && this.form.get(field)?.touched) || (this.tryToSave && this.form.get(field)?.invalid))\r\n  }\r\n\r\n  isTextValid() {\r\n    this.showTextError = !this.textEditor && this.textEditor === '' ? true : false\r\n  }\r\n\r\n  close(event: any) {\r\n    this.showSend = false\r\n    this.showSendAndShare = false\r\n  }\r\n\r\n  onClosedNewNote() {\r\n    console.log('he');\r\n    this.showAddNote = false;\r\n  }\r\n\r\n  onNoteChecked(event: any) {\r\n    console.log(event);\r\n    if (event.checked) {\r\n      this.noteService.checkedNotes.push(event.note);\r\n    } else {\r\n      this.noteService.checkedNotes = this.noteService.checkedNotes.filter(el => el.id !== event.note.id);\r\n    }\r\n  }\r\n\r\n  onShareMultiple() {\r\n    const checkedNotes = this.noteService.checkedNotes;\r\n      \r\n    if (checkedNotes.length === 0) {\r\n      this.validateCheckedNotes('Please select at least one note.');\r\n      return;\r\n    }\r\n      \r\n    const isTeacher = this.authService.isTeacher;\r\n    \r\n    if (isTeacher) {\r\n      this.handleTeacherShareMultiple(checkedNotes);\r\n    } else {\r\n      this.handleStudentShareMultiple(checkedNotes);\r\n    }\r\n  }\r\n\r\n  onUnShareMultiple() {\r\n    if (this.noteService.checkedNotes.length === 0) {\r\n        this.validateCheckedNotes('Please select at least one note.');\r\n        return;\r\n    }\r\n\r\n    this.noteService.checkedNotes.forEach(note => {\r\n        const unshareWith: any[] = note.sharedWithUsers.map(user => user.aspUserId);\r\n        \r\n        const sendForUnshare: SendShareNoteRequest = {\r\n            noteId: note.id,\r\n            classroomId: Number(this.classroom.id),\r\n            studentIds: this.getUserIdsForUnshare(unshareWith, note)\r\n        };\r\n        this.noteActionsService.unShareNote(sendForUnshare, 'Notes unshared successfully');\r\n    });\r\n  \r\n    this.setGroupActions();\r\n}\r\n  \r\n\r\n  downloadMultiple() {\r\n    if (this.noteService.checkedNotes.length === 0) {\r\n      this.validateCheckedNotes('Please select at least one note.');\r\n      return;\r\n    }\r\n    for (let note of this.noteService.checkedNotes) {\r\n      this.noteActionsService.download(note);\r\n    }\r\n  }\r\n\r\n  onRemoveNoteMultiple() {\r\n    this.initConfirmDeleteNoteDialogListener();\r\n    if (this.noteService.checkedNotes.length === 0) {\r\n      this.validateCheckedNotes('Please select at least one note.');\r\n      return;\r\n    }\r\n    this.confirmDialogService.setProperties({\r\n      confirmMessage: `\r\n      Are you sure you want to delete the selected notes?`,\r\n      acceptBtnLabel: 'Yes',\r\n      rejectBtnLabel: 'No',\r\n      action: 'delete-note',\r\n      confirmIcon: '/assets/icons/clock-dialog-icon.svg',\r\n      showHeader: true,\r\n      dialogType: 'custom',\r\n      headerClass: 'my-custom-header-class'\r\n    });\r\n    this.confirmDialogService.show();\r\n\r\n  }\r\n\r\n  private getUserIdsForUnshare(unshareWith: any[], note: any): any[] {\r\n    if (this.authService.isTeacher) {\r\n      return unshareWith;\r\n    } else if (this.noteService.isMyNote(note)) {\r\n      return [this.classroom.teacher!.aspUserId!];\r\n    }\r\n    return [this.user.aspUserId!];\r\n  }\r\n\r\n  private handleTeacherShareMultiple(checkedNotes: Note[]) {\r\n    if (![this.noteViewAccess.TEACHER_VIEW_STUDENTS_CLASSROOM_NOTES, this.noteViewAccess.TEACHER_VIEW_NOTES].includes(this.currentNotesView)) {\r\n  \r\n      if (this.classroom.classroomStudents.length === 1) {\r\n        this.shareNotesMultipleWithClassroom(checkedNotes);\r\n      } else {\r\n        this.openShareMultipleWithNoteDialog(checkedNotes);\r\n      }\r\n  \r\n    } else {\r\n      this.openShareMultipleWithNoteDialog(checkedNotes);\r\n    }\r\n  }\r\n  \r\n  private handleStudentShareMultiple(checkedNotes: Note[]) {\r\n    const unshareWith: any[] = [];\r\n      \r\n    for (let note of checkedNotes) {\r\n      for (let user of note.sharedWithUsers) {\r\n        unshareWith.push(user.aspUserId);\r\n      }\r\n      \r\n      const sendForShare: SendShareNoteRequest = {\r\n        noteId: note.id,\r\n        classroomId: +this.classroom.id,\r\n        studentIds: [this.classroom.teacher!.aspUserId!]\r\n      };\r\n      this.noteActionsService.shareNote(sendForShare, 'Notes unshared successfully');\r\n    }\r\n      \r\n    this.setGroupActions();\r\n  }\r\n  \r\n  private shareNotesMultipleWithClassroom(checkedNotes: Note[]) {\r\n    for (let note of checkedNotes) {\r\n      const sendForShare: SendShareNoteRequest = {\r\n          noteId: note.id,\r\n          classroomId: +this.classroom.id,\r\n          studentIds: [this.classroom.classroomStudents[0].aspUserId!]\r\n       };\r\n      this.noteActionsService.shareNote(sendForShare, 'Notes shared successfully');\r\n    }\r\n  }\r\n  \r\n  private openShareMultipleWithNoteDialog(checkedNotes: Note[]) {\r\n    this.generalService.openDialogWithComponent(this.dialogService, LibraryFileActionsDialogComponent, 440,\r\n      {\r\n        action: 'shareWith',\r\n        dialogsubTitle: '',\r\n        dialogTitle: 'Share Note with...',\r\n        preselectedClassroom: this.classroom,\r\n      }, null, (result: any) => {\r\n      console.log(result);\r\n      if (result && result.action === 'shareWith') {\r\n        if (!this.generalService.isNullishObject(result)) {\r\n  \r\n          for (let note of checkedNotes) {\r\n            this.noteActionsService.sendOrShare(note, result, this.authService.isStudent);\r\n          }\r\n          this.setGroupActions();\r\n          console.log(result);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  private initConfirmDeleteNoteDialogListener() {\r\n    this.confirmDeleteNoteSubscription = (this.confirmDialogService.acceptSelected$\r\n      .pipe(tap((res) => {\r\n\r\n        this.confirmDialogService.hide();\r\n        console.log(res);\r\n\r\n        switch (res.action) {\r\n          case 'delete-note':\r\n            console.log('deltenetote')\r\n            this.onRemoveNoteMultipleAccepted();\r\n        }\r\n      })).subscribe());\r\n\r\n    this.subs.add(this.confirmDeleteNoteSubscription);\r\n  }\r\n\r\n  private onRemoveNoteMultipleAccepted() {\r\n    console.log(this.noteService.checkedNotes);\r\n    for (let note of this.noteService.checkedNotes) {\r\n      console.log(note);\r\n      this.noteActionsService.afterAcceptRemoveNote(note);\r\n    }\r\n    this.confirmDeleteNoteSubscription.unsubscribe();\r\n    this.setGroupActions();\r\n  }\r\n\r\n  private validateCheckedNotes(msg: string) {\r\n    if (this.noteService.checkedNotes.length === 0) {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'warn',\r\n        summary: '',\r\n        detail: msg \r\n      });\r\n      return;\r\n    }\r\n  }\r\n\r\n  private initClassroomListener() {\r\n    this.subs.add(this.noteListenersService.currentSelectedClassroom.subscribe(res => {\r\n      if (this.generalService.isNullishObject(res)) {\r\n        return;\r\n      }\r\n      console.log(this.filteredNotes);\r\n      this.classroom = res;\r\n      this.classroomStudents = this.classroom.classroomStudents;\r\n    }));\r\n  }\r\n}\r\n\r\n\r\n", "<div class=\"notes-header\">\r\n  <div class=\"notes-header-title\">\r\n    {{viewTitle}}\r\n  </div>\r\n  <!-- /** * Header section for notes list view. * Contains UI elements for\r\n  searching, filtering, and adding notes. */ -->\r\n  <div class=\"notes-header-actions\">\r\n    <!-- TODO: Add notes filtering -->\r\n    <!-- <div id=\"filters-content\">\r\n      <div (click)=\"showFilters = !showFilters\"></div>\r\n      <button pButton (click)=\"showFilters = !showFilters\" type=\"button\" label=\"\" class=\"p-button-text\"\r\n        styleClass=\"min-w-6rem\" style=\"min-width: 6rem\">\r\n        <span class=\"p-button-icon p-button-icon-right\" aria-hidden=\"true\"></span>\r\n        <span class=\"p-button-label mr-2\">Filters</span>\r\n        <img class=\"contact-icon-img\" src=\"/assets/icons/notifications/notif-filter.svg\" />\r\n      </button>\r\n      <div *ngIf=\"showFilters\" id=\"notes-filters\" class=\"notes-popup\">\r\n        <div class=\"popup-title p-10\">\r\n          <div>Choose Filters</div>\r\n          <img (click)=\"showFilters = false\" src=\"/assets/icons/close.png\" class=\"close-img close-img-abs hvr-glow\" />\r\n        </div>\r\n        <div class=\"notes-filters-filters p-0-10\">\r\n          <div class=\"filter-row\">\r\n            <ejs-checkbox label=\"\" (change)=\"sharedFilter($event)\" [checked]=\"isSharedFilter\">\r\n            </ejs-checkbox>\r\n            <div class=\"filter\">Shared</div>\r\n          </div>\r\n          <div class=\"filter-row\">\r\n            <ejs-checkbox label=\"\" (change)=\"notSharedFilter($event)\" [checked]=\"isNotSharedFilter\">\r\n            </ejs-checkbox>\r\n            <div class=\"filter\">Not Shared</div>\r\n          </div>\r\n          <div class=\"filter-row\">\r\n            <ejs-checkbox label=\"\" (change)=\"pinnedFilter($event)\" [checked]=\"isPinnedFilter\">\r\n            </ejs-checkbox>\r\n            <div class=\"filter\">Pinned</div>\r\n          </div>\r\n          <div class=\"filter-row\">\r\n            <ejs-checkbox label=\"\" (change)=\"favoritedFilter($event)\" [checked]=\"isFavoritedFilter\">\r\n            </ejs-checkbox>\r\n            <div class=\"filter\">Favorited</div>\r\n          </div>\r\n          <div class=\"filter-col\">\r\n            <div class=\"filter\">From</div>\r\n            <ejs-datepicker (click)=\"datePickerOpen = true\" format=\"dd-MM-yyyy\" #from (change)=\"fromFilter()\"\r\n              [value]=\"fromFilterValue\"></ejs-datepicker>\r\n          </div>\r\n          <div class=\"filter-col\">\r\n            <div class=\"filter\">To</div>\r\n            <ejs-datepicker (click)=\"datePickerOpen = true\" format=\"dd-MM-yyyy\" #to (change)=\"toFilter()\"\r\n              [value]=\"toFilterValue\"></ejs-datepicker>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div> -->\r\n    <div class=\"notes-header-actions gap-2\">\r\n      <span class=\"p-input-icon-right w-full\">\r\n        <input type=\"text\" [(ngModel)]=\"term\" pInputText placeholder=\"Search\"\r\n          class=\"h-2rem w-full input-blue gradient-blue rounded\" />\r\n        <i class=\"pi pi-search text-primary\"></i>\r\n      </span>\r\n      <img src=\"/assets/icons/library/group-actions.svg\" [ngClass]=\"{'filter-blue': showGroupActions}\" \r\n      title=\"Group Actions\"\r\n      (click)=\"setGroupActions()\" class=\"hvr-grow\" />\r\n      <img *ngIf=\"hasAdd\" title=\"Create Note\" src=\"/assets/icons/library/add-lib.svg\" class=\"add hvr-grow add-note-btn-icon\"\r\n        (click)=\"showAddNote = !showAddNote\" />\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n\r\n<div class=\"flex align-items-center justify-content-center\">\r\n  <div *ngIf=\"showGroupActions\" class=\"note-menu-row block-gradient border-round-xl font-sm my-2 p-1 px-3 gap-2\">\r\n    <!--  (click)=\"showShare = !showShare\" -->\r\n    <div *ngIf=\"![noteViewAccess.TEACHER_VIEW_STUDENTS_CLASSROOM_NOTES, noteViewAccess.STUDENT_VIEW_TEACHERS_CLASSROOM_NOTES].includes(currentNotesView)\"\r\n    class=\"note-menu-col\" (click)=\"onShareMultiple()\">\r\n      <div class=\"note-menu-icon\">\r\n        <img src=\"/assets/icons/library/share-sm-icon.svg\" height=\"14\">\r\n      </div>\r\n      <div class=\"note-menu-text link-main-color\">\r\n        Share\r\n      </div>\r\n      <!-- <div class=\"note-menu-icon\" style=\"margin-left:auto\">\r\n              <img src=\"/assets/icons/arrow-note.svg\">\r\n          </div> -->\r\n    </div>\r\n    <div\r\n      *ngIf=\"\r\n      (currentNotesView !== noteViewAccess.TEACHER_VIEW_NOTES) && (currentNotesView !== noteViewAccess.STUDENT_VIEW_TEACHERS_CLASSROOM_NOTES)\"\r\n      class=\"note-menu-col\" (click)=\"onUnShareMultiple()\">\r\n      <div class=\"note-menu-icon\">\r\n        <img src=\"/assets/icons/library/share-sm-icon.svg\" height=\"14\">\r\n      </div>\r\n      <div class=\"note-menu-text link-main-color\">\r\n        Unshare\r\n      </div>\r\n    </div>\r\n    <div class=\"note-menu-col\" (click)=\"downloadMultiple()\">\r\n      <div class=\"note-menu-icon\">\r\n        <img src=\"/assets/icons/cloud-download.svg\" height=\"14\">\r\n      </div>\r\n      <div class=\"note-menu-text link-main-color\">\r\n        Download\r\n      </div>\r\n    </div>\r\n    <div *ngIf=\"hasAdd\" class=\"note-menu-col\" (click)=\"onRemoveNoteMultiple()\">\r\n      <div class=\"note-menu-icon\">\r\n        <img src=\"/assets/icons/library/delete.svg\" height=\"14\">\r\n      </div>\r\n      <div class=\"note-menu-text link-main-color\">\r\n        Delete\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<!-- /**\r\n* logic to display the note details component based on the currentNotesView and showAddNote variables.\r\n* If the currentNotesView is either TEACHER_VIEW_CLASSROOM_NOTES, STUDENT_VIEW_CLASSROOM_NOTES, or TEACHER_VIEW_NOTES and showAddNote is true,\r\n* then it checks if there is only one classroom student or if the user is a teacher. \r\n* then can render the create new note\r\n*/ -->\r\n\r\n{{((classroomStudents && classroomStudents.length === 1) ||\r\n  !withClassroom) && hasAdd}}\r\n<ng-container *ngIf=\"\r\n    (currentNotesView === noteViewAccess.TEACHER_VIEW_CLASSROOM_NOTES ||\r\n      currentNotesView === noteViewAccess.STUDENT_VIEW_CLASSROOM_NOTES ||\r\n      currentNotesView === noteViewAccess.TEACHER_VIEW_NOTES) &&\r\n    showAddNote\">\r\n  <div *ngIf=\"\r\n      ((classroomStudents && classroomStudents.length === 1) ||\r\n      !withClassroom) && hasAdd\">\r\n    <app-note-details [note]=\"\" [type]=\"'create'\" [classroom]=\"classroom\"\r\n    [showGroupActions]=\"showGroupActions\"\r\n    [view]=\"noteListenersService.getNotesViewAccess(hasAdd, withClassroom)\"\r\n      (closedNewNote)=\"onClosedNewNote()\"></app-note-details>\r\n  </div>\r\n</ng-container>\r\n\r\n<div *ngIf=\"!isLoading\" class=\"notes-height\">\r\n\r\n  <ng-container  *ngIf=\"(classroomStudents && classroomStudents.length === 1) || !withClassroom || authService.isStudent\">\r\n    <ng-container\r\n      *ngTemplateOutlet=\"notesGeneralTemplate; context: { classroom: classroom, filteredNotes: filteredNotes }\">\r\n    </ng-container>\r\n  </ng-container>\r\n\r\n  <ng-container *ngIf=\"withClassroom && authService.isTeacher && (classroomStudents && classroomStudents.length > 1)\">\r\n    <p-tabView [scrollable]=\"classroomStudents.length > 4\" class=\"w-full notes-height\">\r\n      <p-tabPanel header=\"All\">\r\n        <div id=\"classroom-info\" class=\"h-full \">\r\n          <ng-container\r\n            *ngTemplateOutlet=\"notesGeneralTemplate; context: { classroom: classroom, filteredNotes: filteredNotes }\">\r\n          </ng-container>\r\n\r\n        </div>\r\n      </p-tabPanel>\r\n      <ng-container *ngFor=\"let selectedStudent of classroomStudents; index as i\">\r\n        <p-tabPanel [header]=\"selectedStudent.firstName\" class=\"\">\r\n          <ng-container\r\n            *ngTemplateOutlet=\"notesGeneralTemplate; context: { classroom: classroom, filteredNotes: filteredNotes, student: selectedStudent }\">\r\n          </ng-container>\r\n        </p-tabPanel>\r\n      </ng-container>\r\n    </p-tabView>\r\n  </ng-container>\r\n\r\n  <ng-template #notesGeneralTemplate let-classroom=\"classroom\" let-student=\"student\" let-filteredNotes=\"filteredNotes\">\r\n\r\n<ng-container *ngIf=\"currentNotesView === noteViewAccess.TEACHER_VIEW_CLASSROOM_NOTES && showAddNote\">\r\n<div *ngIf=\"classroomStudents && classroomStudents.length > 1\">\r\n  <app-note-details [note]=\"\" [type]=\"'create'\" [classroom]=\"classroom\"\r\n  [showGroupActions]=\"showGroupActions\"\r\n  [view]=\"noteListenersService.getNotesViewAccess(hasAdd, withClassroom)\"\r\n    (closedNewNote)=\"onClosedNewNote()\"></app-note-details>\r\n</div>\r\n</ng-container>\r\n<!--  | filter: term -->\r\n    <div *ngFor=\"let note of getFilteredNotes(student) | filter: { term: term }\">\r\n      <app-note-details [isInMine]=\"hasAdd\" [note]=\"note\" [classroom]=\"classroom\" \r\n      [withClassroom]=\"withClassroom\"\r\n      [showGroupActions]=\"showGroupActions\"\r\n      [view]=\"noteListenersService.getNotesViewAccess(hasAdd, withClassroom)\"\r\n        (noteChecked)=\"onNoteChecked($event)\"></app-note-details>\r\n    </div>\r\n    <div class=\"asked-for-help-main scrollable h-full notes-height\" *ngIf=\"filteredNotes && getFilteredNotes(student).length == 0 && !showAddNote\"\r\n      [fromRoot]=\"true\" [scrollWindow]=\"true\">\r\n\r\n  <div class=\"flex flex-column gap-1 align-items-center justify-content-center h-full\" *ngIf=\"(hasAdd || getFilteredNotes(student).length == 0) && (getFilteredNotes(student).length == 0 && !student) && hasAdd\">\r\n        <img src=\"/assets/icons/library/add-lib.svg\" (click)=\"showAddNote = !showAddNote\" class=\"add lg hvr-grow\">\r\n        <p class=\"font-base max-w-14rem text-center\">\r\n            <!-- There are currently no files {{withClassroom ? 'in this classroom' : ''}}. -->\r\n            Add your first note!\r\n        </p>\r\n    </div>\r\n\r\n  <ng-container *ngIf=\"!hasAdd || (getFilteredNotes(student).length === 0 && student)\">\r\n      <div class=\"flex flex-column gap-1 align-items-center justify-content-center h-full\">\r\n          <img src=\"/assets/icons/library/file_manager_emp.png\" alt=\"file manager empty icon\" class=\"add empty hvr-grow\">\r\n          <p class=\"font-base max-w-14rem text-center\">\r\n              <ng-container *ngIf=\"authService.isStudent\">\r\n                  No notes found.\r\n              </ng-container>\r\n              <ng-container *ngIf=\"authService.isTeacher\">\r\n                  No notes found.\r\n              </ng-container>\r\n          </p>\r\n      </div>\r\n  </ng-container>\r\n\r\n      <!-- <div class=\"no-data h-full\">\r\n        <img src=\"/assets/icons/notes-empty.svg\" class=\"max-w-5rem\">\r\n        <div class=\"title text-center\">\r\n          There are no notes for this section. <span *ngIf=\"role=='Teacher'\"> <br> You can press the + button to add\r\n            one.</span>\r\n        </div>\r\n      </div> -->\r\n    </div>\r\n\r\n  </ng-template>\r\n\r\n\r\n  <div *ngIf=\"isLoading\" style=\"width:100%;height:800px; display: flex; justify-content: center;\">\r\n    <mat-spinner class=\"spinner\" style=\"margin-top:200px;\"></mat-spinner>\r\n  </div>\r\n</div>"], "mappings": "AAGA,SAASA,kBAAkB,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,gBAAgB;AAOjF,SAASC,OAAO,QAAQ,SAAS;AAEjC,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAGhC,SAASC,oBAAoB,EAAEC,cAAc,QAAQ,8CAA8C;AACnG,SAASC,iCAAiC,QAAQ,8GAA8G;AAChK,SAASC,aAAa,QAAQ,uBAAuB;AAErD,SAAeC,GAAG,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IC6CpCC,EAAA,CAAAC,cAAA,cACyC;IAAvCD,EAAA,CAAAE,UAAA,mBAAAC,uDAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,WAAA,IAAAH,MAAA,CAAAG,WAAA;IAAA,EAAoC;IADtCT,EAAA,CAAAU,YAAA,EACyC;;;;;;IAS3CV,EAAA,CAAAC,cAAA,cACkD;IAA5BD,EAAA,CAAAE,UAAA,mBAAAS,8DAAA;MAAAX,EAAA,CAAAI,aAAA,CAAAQ,GAAA;MAAA,MAAAN,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAO,eAAA,EAAiB;IAAA,EAAC;IAC/Cb,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAc,SAAA,cAA+D;IACjEd,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,cAA4C;IAC1CD,EAAA,CAAAe,MAAA,cACF;IAIFf,EAJE,CAAAU,YAAA,EAAM,EAIF;;;;;;IACNV,EAAA,CAAAC,cAAA,cAGsD;IAA9BD,EAAA,CAAAE,UAAA,mBAAAc,8DAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAY,iBAAA,EAAmB;IAAA,EAAC;IACnDlB,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAc,SAAA,cAA+D;IACjEd,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,cAA4C;IAC1CD,EAAA,CAAAe,MAAA,gBACF;IACFf,EADE,CAAAU,YAAA,EAAM,EACF;;;;;;IASNV,EAAA,CAAAC,cAAA,cAA2E;IAAjCD,EAAA,CAAAE,UAAA,mBAAAiB,8DAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAe,oBAAA,EAAsB;IAAA,EAAC;IACxErB,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAc,SAAA,cAAwD;IAC1Dd,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,cAA4C;IAC1CD,EAAA,CAAAe,MAAA,eACF;IACFf,EADE,CAAAU,YAAA,EAAM,EACF;;;;;;IAxCRV,EAAA,CAAAC,cAAA,cAA+G;IAc7GD,EAZA,CAAAsB,UAAA,IAAAC,wCAAA,kBACkD,IAAAC,wCAAA,kBAcI;IAQtDxB,EAAA,CAAAC,cAAA,cAAwD;IAA7BD,EAAA,CAAAE,UAAA,mBAAAuB,wDAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAsB,GAAA;MAAA,MAAApB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqB,gBAAA,EAAkB;IAAA,EAAC;IACrD3B,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAc,SAAA,cAAwD;IAC1Dd,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,cAA4C;IAC1CD,EAAA,CAAAe,MAAA,iBACF;IACFf,EADE,CAAAU,YAAA,EAAM,EACF;IACNV,EAAA,CAAAsB,UAAA,IAAAM,wCAAA,kBAA2E;IAQ7E5B,EAAA,CAAAU,YAAA,EAAM;;;;IAvCEV,EAAA,CAAA6B,SAAA,EAA8I;IAA9I7B,EAAA,CAAA8B,UAAA,UAAA9B,EAAA,CAAA+B,eAAA,IAAAC,GAAA,EAAA1B,MAAA,CAAA2B,cAAA,CAAAC,qCAAA,EAAA5B,MAAA,CAAA2B,cAAA,CAAAE,qCAAA,EAAAC,QAAA,CAAA9B,MAAA,CAAA+B,gBAAA,EAA8I;IAajJrC,EAAA,CAAA6B,SAAA,EAEH;IAFG7B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAA+B,gBAAA,KAAA/B,MAAA,CAAA2B,cAAA,CAAAK,kBAAA,IAAAhC,MAAA,CAAA+B,gBAAA,KAAA/B,MAAA,CAAA2B,cAAA,CAAAE,qCAAA,CAEH;IAgBMnC,EAAA,CAAA6B,SAAA,GAAY;IAAZ7B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAiC,MAAA,CAAY;;;;;;IA4BlBvC,EAHF,CAAAC,cAAA,UAE+B,2BAIS;IAApCD,EAAA,CAAAE,UAAA,2BAAAsC,4FAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAAqC,GAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAiBF,MAAA,CAAAoC,eAAA,EAAiB;IAAA,EAAC;IACvC1C,EADwC,CAAAU,YAAA,EAAmB,EACrD;;;;IAJwBV,EAAA,CAAA6B,SAAA,EAAiB;IAE7C7B,EAF4B,CAAA8B,UAAA,kBAAiB,cAAAxB,MAAA,CAAAqC,SAAA,CAAwB,qBAAArC,MAAA,CAAAsC,gBAAA,CAChC,SAAAtC,MAAA,CAAAuC,oBAAA,CAAAC,kBAAA,CAAAxC,MAAA,CAAAiC,MAAA,EAAAjC,MAAA,CAAAyC,aAAA,EACkC;;;;;IAV3E/C,EAAA,CAAAgD,uBAAA,GAIiB;IACfhD,EAAA,CAAAsB,UAAA,IAAA2B,iDAAA,kBAE+B;;;;;IAFzBjD,EAAA,CAAA6B,SAAA,EAGN;IAHM7B,EAAA,CAAA8B,UAAA,UAAAxB,MAAA,CAAA4C,iBAAA,IAAA5C,MAAA,CAAA4C,iBAAA,CAAAC,MAAA,WAAA7C,MAAA,CAAAyC,aAAA,KAAAzC,MAAA,CAAAiC,MAAA,CAGN;;;;;IAUEvC,EAAA,CAAAoD,kBAAA,GAEe;;;;;IAHjBpD,EAAA,CAAAgD,uBAAA,GAAwH;IACtHhD,EAAA,CAAAsB,UAAA,IAAA+B,gEAAA,2BAC4G;;;;;;;IAAzGrD,EAAA,CAAA6B,SAAA,EAAwC;IAAA7B,EAAxC,CAAA8B,UAAA,qBAAAwB,uBAAA,CAAwC,4BAAAtD,EAAA,CAAA+B,eAAA,IAAAwB,GAAA,EAAAjD,MAAA,CAAAqC,SAAA,EAAArC,MAAA,CAAAkD,aAAA,EAA+D;;;;;IAQpGxD,EAAA,CAAAoD,kBAAA,GAEe;;;;;IAMfpD,EAAA,CAAAoD,kBAAA,GAEe;;;;;IAJnBpD,EAAA,CAAAgD,uBAAA,GAA4E;IAC1EhD,EAAA,CAAAC,cAAA,qBAA0D;IACxDD,EAAA,CAAAsB,UAAA,IAAAmC,+EAAA,2BACsI;IAExIzD,EAAA,CAAAU,YAAA,EAAa;;;;;;;;IAJDV,EAAA,CAAA6B,SAAA,EAAoC;IAApC7B,EAAA,CAAA8B,UAAA,WAAA4B,kBAAA,CAAAC,SAAA,CAAoC;IAE3C3D,EAAA,CAAA6B,SAAA,EAAwC;IAAA7B,EAAxC,CAAA8B,UAAA,qBAAAwB,uBAAA,CAAwC,4BAAAtD,EAAA,CAAA4D,eAAA,IAAAC,GAAA,EAAAvD,MAAA,CAAAqC,SAAA,EAAArC,MAAA,CAAAkD,aAAA,EAAAE,kBAAA,EAAyF;;;;;IAb5I1D,EAAA,CAAAgD,uBAAA,GAAoH;IAG9GhD,EAFJ,CAAAC,cAAA,oBAAmF,qBACxD,cACkB;IACvCD,EAAA,CAAAsB,UAAA,IAAAwC,gEAAA,2BAC4G;IAIhH9D,EADE,CAAAU,YAAA,EAAM,EACK;IACbV,EAAA,CAAAsB,UAAA,IAAAyC,gEAAA,2BAA4E;IAO9E/D,EAAA,CAAAU,YAAA,EAAY;;;;;;;IAhBDV,EAAA,CAAA6B,SAAA,EAA2C;IAA3C7B,EAAA,CAAA8B,UAAA,eAAAxB,MAAA,CAAA4C,iBAAA,CAAAC,MAAA,KAA2C;IAI7CnD,EAAA,CAAA6B,SAAA,GAAwC;IAAA7B,EAAxC,CAAA8B,UAAA,qBAAAwB,uBAAA,CAAwC,4BAAAtD,EAAA,CAAA+B,eAAA,IAAAwB,GAAA,EAAAjD,MAAA,CAAAqC,SAAA,EAAArC,MAAA,CAAAkD,aAAA,EAA+D;IAKpExD,EAAA,CAAA6B,SAAA,EAAsB;IAAtB7B,EAAA,CAAA8B,UAAA,YAAAxB,MAAA,CAAA4C,iBAAA,CAAsB;;;;;;IAcpElD,EADF,CAAAC,cAAA,UAA+D,2BAIvB;IAApCD,EAAA,CAAAE,UAAA,2BAAA8D,gHAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAA6D,IAAA;MAAA,MAAA3D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAiBF,MAAA,CAAAoC,eAAA,EAAiB;IAAA,EAAC;IACvC1C,EADwC,CAAAU,YAAA,EAAmB,EACrD;;;;;IAJwBV,EAAA,CAAA6B,SAAA,EAAiB;IAE7C7B,EAF4B,CAAA8B,UAAA,kBAAiB,cAAAoC,aAAA,CAAwB,qBAAA5D,MAAA,CAAAsC,gBAAA,CAChC,SAAAtC,MAAA,CAAAuC,oBAAA,CAAAC,kBAAA,CAAAxC,MAAA,CAAAiC,MAAA,EAAAjC,MAAA,CAAAyC,aAAA,EACkC;;;;;IAJzE/C,EAAA,CAAAgD,uBAAA,GAAsG;IACtGhD,EAAA,CAAAsB,UAAA,IAAA6C,qEAAA,kBAA+D;;;;;IAAzDnE,EAAA,CAAA6B,SAAA,EAAuD;IAAvD7B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAA4C,iBAAA,IAAA5C,MAAA,CAAA4C,iBAAA,CAAAC,MAAA,KAAuD;;;;;;IASvDnD,EADF,CAAAC,cAAA,UAA6E,2BAKnC;IAAtCD,EAAA,CAAAE,UAAA,yBAAAkE,+FAAAC,MAAA;MAAArE,EAAA,CAAAI,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAAiE,aAAA,CAAAF,MAAA,CAAqB;IAAA,EAAC;IACzCrE,EAD0C,CAAAU,YAAA,EAAmB,EACvD;;;;;;IALcV,EAAA,CAAA6B,SAAA,EAAmB;IAGrC7B,EAHkB,CAAA8B,UAAA,aAAAxB,MAAA,CAAAiC,MAAA,CAAmB,SAAAiC,QAAA,CAAc,cAAAN,aAAA,CAAwB,kBAAA5D,MAAA,CAAAyC,aAAA,CAC5C,qBAAAzC,MAAA,CAAAsC,gBAAA,CACM,SAAAtC,MAAA,CAAAuC,oBAAA,CAAAC,kBAAA,CAAAxC,MAAA,CAAAiC,MAAA,EAAAjC,MAAA,CAAAyC,aAAA,EACkC;;;;;;IAOrE/C,EADN,CAAAC,cAAA,cAAgN,cAChG;IAA7DD,EAAA,CAAAE,UAAA,mBAAAuE,kFAAA;MAAAzE,EAAA,CAAAI,aAAA,CAAAsE,IAAA;MAAA,MAAApE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,WAAA,IAAAH,MAAA,CAAAG,WAAA;IAAA,EAAoC;IAAjFT,EAAA,CAAAU,YAAA,EAA0G;IAC1GV,EAAA,CAAAC,cAAA,YAA6C;IAEzCD,EAAA,CAAAe,MAAA,6BACJ;IACJf,EADI,CAAAU,YAAA,EAAI,EACF;;;;;IAMIV,EAAA,CAAAgD,uBAAA,GAA4C;IACxChD,EAAA,CAAAe,MAAA,wBACJ;;;;;;IACAf,EAAA,CAAAgD,uBAAA,GAA4C;IACxChD,EAAA,CAAAe,MAAA,wBACJ;;;;;;IATZf,EAAA,CAAAgD,uBAAA,GAAqF;IACjFhD,EAAA,CAAAC,cAAA,cAAqF;IACjFD,EAAA,CAAAc,SAAA,cAA+G;IAC/Gd,EAAA,CAAAC,cAAA,YAA6C;IAIzCD,EAHA,CAAAsB,UAAA,IAAAqD,oFAAA,2BAA4C,IAAAC,oFAAA,2BAGA;IAIpD5E,EADI,CAAAU,YAAA,EAAI,EACF;;;;;IAPiBV,EAAA,CAAA6B,SAAA,GAA2B;IAA3B7B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAuE,WAAA,CAAAC,SAAA,CAA2B;IAG3B9E,EAAA,CAAA6B,SAAA,EAA2B;IAA3B7B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAuE,WAAA,CAAAE,SAAA,CAA2B;;;;;IAlBpD/E,EAAA,CAAAC,cAAA,cAC0C;IAU5CD,EARA,CAAAsB,UAAA,IAAA0D,4DAAA,kBAAgN,IAAAC,qEAAA,2BAQ3H;IAqBnFjF,EAAA,CAAAU,YAAA,EAAM;;;;;IA/BcV,EAAlB,CAAA8B,UAAA,kBAAiB,sBAAsB;IAE2C9B,EAAA,CAAA6B,SAAA,EAAwH;IAAxH7B,EAAA,CAAA8B,UAAA,UAAAxB,MAAA,CAAAiC,MAAA,IAAAjC,MAAA,CAAA4E,gBAAA,CAAAC,WAAA,EAAAhC,MAAA,UAAA7C,MAAA,CAAA4E,gBAAA,CAAAC,WAAA,EAAAhC,MAAA,UAAAgC,WAAA,IAAA7E,MAAA,CAAAiC,MAAA,CAAwH;IAQ/LvC,EAAA,CAAA6B,SAAA,EAAoE;IAApE7B,EAAA,CAAA8B,UAAA,UAAAxB,MAAA,CAAAiC,MAAA,IAAAjC,MAAA,CAAA4E,gBAAA,CAAAC,WAAA,EAAAhC,MAAA,UAAAgC,WAAA,CAAoE;;;;;IAlBjFnF,EATJ,CAAAsB,UAAA,IAAA8D,+DAAA,2BAAsG,IAAAC,sDAAA,kBASrB;;IAO7ErF,EAAA,CAAAsB,UAAA,IAAAgE,sDAAA,kBAC0C;;;;;;IAjB/BtF,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAA+B,gBAAA,KAAA/B,MAAA,CAAA2B,cAAA,CAAAsD,4BAAA,IAAAjF,MAAA,CAAAG,WAAA,CAAqF;IAS1ET,EAAA,CAAA6B,SAAA,EAAqD;IAArD7B,EAAA,CAAA8B,UAAA,YAAA9B,EAAA,CAAAwF,WAAA,OAAAlF,MAAA,CAAA4E,gBAAA,CAAAC,WAAA,GAAAnF,EAAA,CAAAyF,eAAA,IAAAC,GAAA,EAAApF,MAAA,CAAAqF,IAAA,GAAqD;IAOV3F,EAAA,CAAA6B,SAAA,GAA4E;IAA5E7B,EAAA,CAAA8B,UAAA,SAAA8D,iBAAA,IAAAtF,MAAA,CAAA4E,gBAAA,CAAAC,WAAA,EAAAhC,MAAA,UAAA7C,MAAA,CAAAG,WAAA,CAA4E;;;;;IAqC/IT,EAAA,CAAAC,cAAA,cAAgG;IAC9FD,EAAA,CAAAc,SAAA,sBAAqE;IACvEd,EAAA,CAAAU,YAAA,EAAM;;;;;IArFRV,EAAA,CAAAC,cAAA,cAA6C;IAmF3CD,EAjFA,CAAAsB,UAAA,IAAAuE,iDAAA,2BAAwH,IAAAC,iDAAA,2BAMJ,IAAAC,gDAAA,gCAAA/F,EAAA,CAAAgG,sBAAA,CAoBC,IAAAC,wCAAA,kBAuDrB;IAGlGjG,EAAA,CAAAU,YAAA,EAAM;;;;IApFYV,EAAA,CAAA6B,SAAA,EAAsG;IAAtG7B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAA4C,iBAAA,IAAA5C,MAAA,CAAA4C,iBAAA,CAAAC,MAAA,WAAA7C,MAAA,CAAAyC,aAAA,IAAAzC,MAAA,CAAAuE,WAAA,CAAAC,SAAA,CAAsG;IAMvG9E,EAAA,CAAA6B,SAAA,EAAmG;IAAnG7B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAyC,aAAA,IAAAzC,MAAA,CAAAuE,WAAA,CAAAE,SAAA,IAAAzE,MAAA,CAAA4C,iBAAA,IAAA5C,MAAA,CAAA4C,iBAAA,CAAAC,MAAA,KAAmG;IA2E5GnD,EAAA,CAAA6B,SAAA,GAAe;IAAf7B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAA4F,SAAA,CAAe;;;AD5LvB,OAAM,MAAOC,kBAAkB;EA6C7BC,YACUC,cAA8B,EAC9BC,WAAwB,EACxBC,YAA0B,EAC1BC,gBAAkC,EAClCC,EAAqB,EACrB5B,WAAwB,EACzBhC,oBAA0C,EAC1C6D,kBAAsC,EACtCC,aAA4B,EAC5BC,oBAA0C;IATzC,KAAAP,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAA5B,WAAW,GAAXA,WAAW;IACZ,KAAAhC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAA6D,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,oBAAoB,GAApBA,oBAAoB;IAtDpB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,SAAS,GAAG,UAAU;IACtB,KAAA/D,aAAa,GAAG,KAAK;IACrB,KAAAR,MAAM,GAAG,IAAI;IACb,KAAAI,SAAS,GAAc,EAAe;IACvC,KAAAoE,IAAI,GAAG,IAAItH,OAAO,EAAE;IAC5B,KAAAuH,WAAW,GAAY,KAAK;IAC5B,KAAApE,gBAAgB,GAAY,IAAI;IAGhC,KAAAnC,WAAW,GAAY,KAAK;IAC5B,KAAAwG,SAAS,GAAY,KAAK;IAC1B,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,eAAe,GAAS,EAAU;IAClC,KAAAC,aAAa,GAAS,EAAU;IAChC,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAjE,aAAa,GAAW,EAAE;IAC1B,KAAAkE,UAAU,GAAgB,EAAE;IAC5B,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,0BAA0B,GAAW,CAAC;IACtC,KAAAC,WAAW,GAAY,IAAI;IAC3B,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAA9B,SAAS,GAAY,KAAK;IAC1B,KAAA+B,UAAU,GAAG,EAAE;IACf,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAhF,iBAAiB,GAAW,EAAE;IACvB,KAAAiF,IAAI,GAAS,EAAU;IACvB,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,OAAO,GAAW,CAAC;IACnB,KAAAC,cAAc,GAAY,IAAI;IAC9B,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,EAAE,GAAQ,EAAS;IACnB,KAAAC,IAAI,GAAQ,EAAS;IACrB,KAAAC,IAAI,GAAqB,IAAInJ,gBAAgB,CAAC,EAAE,CAAC;IACxD,KAAAoJ,QAAQ,GAAY,KAAK;IAClB,KAAA1G,cAAc,GAAGrC,cAAc;EAclC;EAEJgJ,QAAQA,CAAA;IACN,IAAI,CAACT,IAAI,GAAG,IAAI,CAACtD,WAAW,CAACgE,eAAe,EAAE;IAC9C,IAAI,CAACT,IAAI,GAAG,IAAI,CAACD,IAAI,CAACC,IAAK;IAC3B,IAAI,CAACU,qBAAqB,EAAE;IAC5B;IACA,IAAI,CAACJ,IAAI,GAAG,IAAInJ,gBAAgB,CAAC;MAC/BwJ,KAAK,EAAE,IAAIzJ,kBAAkB,CAAC,IAAI,EAAE;QAClC0J,UAAU,EAAE,CAACxJ,UAAU,CAACyJ,QAAQ;OACjC,CAAC;MACFC,IAAI,EAAE,IAAI5J,kBAAkB,CAAC,IAAI,EAAE;QACjC0J,UAAU,EAAE,CAACxJ,UAAU,CAACyJ,QAAQ;OACjC;KACF,CAAC;EACJ;EAEAE,WAAWA,CAAA;IACT,IAAI,IAAI,CAACxG,SAAS,EAAE;MAClB,IAAI,CAACO,iBAAiB,GAAG,IAAI,CAACP,SAAS,CAACO,iBAAiB;IAC3D;IACA,IAAI,CAACM,aAAa,GAAG,IAAI,CAACqD,KAAK;EACjC;EAEAuC,eAAeA,CAAA;IACb,IAAI,CAACxG,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC6D,EAAE,CAAC4C,aAAa,EAAE;EACzB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACvC,IAAI,CAACwC,WAAW,EAAE;IACvB,IAAI,CAAC1G,oBAAoB,CAAC2G,eAAe,CAAC,KAAK,CAAC;IAChD,IAAI,CAAClD,WAAW,CAAC4B,YAAY,GAAG,EAAE;EACpC;EAEAuB,kBAAkBA,CAAA,GAClB;EAEA,IAAIpH,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACQ,oBAAoB,CAACC,kBAAkB,CAAC,IAAI,CAACP,MAAM,EAAE,IAAI,CAACQ,aAAa,CAAC;EACtF;EAEA;EACAyG,eAAeA,CAAA;IACb,IAAI,CAAC5G,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;IAC9C,IAAI,CAAC,IAAI,CAACA,gBAAgB,EAAE;MAC1B,IAAI,CAAC0D,WAAW,CAAC4B,YAAY,GAAG,EAAE;IACpC;IACAwB,UAAU,CAAC,MAAK;MACd,IAAI,CAAC7G,oBAAoB,CAAC2G,eAAe,CAAC,IAAI,CAAC5G,gBAAgB,CAAC;IAClE,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;;;;EAMAsC,gBAAgBA,CAACyE,aAAmB;IAClC,IAAI,IAAI,CAACpH,MAAM,EAAE;MACjB,OAAO,IAAI,CAACiB,aAAa,CAACoG,MAAM,CAAEC,IAAU,IAAI;QAC9C,OAAOF,aAAa,GAChBE,IAAI,CAACC,eAAe,CAACC,IAAI,CACxB5B,IAAU,IAAKA,IAAI,CAAC6B,SAAS,KAAKL,aAAa,CAACK,SAAS,CAC3D,GACC,IAAI;MACV,CAAC,CAAC;IACF,CAAC,MAAM;MACL,OAAO,IAAI,CAACxG,aAAa,CAACoG,MAAM,CAAEC,IAAU,IAAI;QAC9C,OAAOF,aAAa,GAChBE,IAAI,CAACI,SAAS,CAACD,SAAS,KAAKL,aAAa,CAACK,SAAS,GAAG,IAAI;MACjE,CAAC,CAAC;IACJ;EAEF;EAEAE,eAAeA,CAAA;IACb,KAAK,IAAIL,IAAI,IAAI,IAAI,CAACvD,WAAW,CAAC4B,YAAY,EAAE;MAC9C,IAAI2B,IAAI,CAACM,YAAY,CAACP,MAAM,CAAEzB,IAAU,IAAKA,IAAI,CAACiC,EAAE,IAAI,IAAI,CAACvF,WAAW,CAACwF,SAAS,EAAE,CAAC,CAAClH,MAAM,IAAI,CAAC,EAAE,OAAO,IAAI;IAChH;IACA,OAAO,KAAK;EACd;EAEAmH,YAAYA,CAAA;IACV,KAAK,IAAIT,IAAI,IAAI,IAAI,CAACvD,WAAW,CAAC4B,YAAY,EAAE;MAC9C,IAAI,CAAC2B,IAAI,CAACU,MAAM,EAAE,OAAO,IAAI;IAC/B;IACA,OAAO,KAAK;EACd;EAGA;;;;;EAKAC,gBAAgBA,CAAA;IACd,IAAIhH,aAAa,GAAW,EAAE;IAC9B,KAAK,IAAIqG,IAAI,IAAI,IAAI,CAAChD,KAAK,EAAE;MAC3B,IAAI,IAAI,CAACQ,cAAc,EAAE;QACvB,IAAI,CAACwC,IAAI,CAACU,MAAM,EAAE;UAChB;QACF;MACF;MACA,IAAI,IAAI,CAACjD,iBAAiB,EAAE;QAC1B,IAAIuC,IAAI,CAACM,YAAY,CAACP,MAAM,CAAEzB,IAAU,IAAKA,IAAI,CAAC6B,SAAS,IAAI,IAAI,CAACnF,WAAW,CAACwF,SAAS,EAAE,CAAC,CAAClH,MAAM,IAAI,CAAC,EAAE;UACxG;QACF;MACF;MACA;MACA,IAAI,IAAI,CAACsF,IAAI,CAACgC,KAAK,EAAE;QACnB,IAAI,EAAEZ,IAAI,CAACa,OAAO,CAACC,OAAO,EAAE,GAAG,IAAI,CAAClC,IAAI,CAACgC,KAAK,CAACE,OAAO,EAAE,CAAC,EAAE;UACzD;QACF;MACF;MACA,IAAI,IAAI,CAACnC,EAAE,CAACiC,KAAK,EAAE;QACjB,IAAI,EAAEZ,IAAI,CAACa,OAAO,CAACC,OAAO,EAAE,GAAGjL,MAAM,CAAC,IAAI,CAAC8I,EAAE,CAACiC,KAAK,CAAC,CAACG,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,MAAM,EAAE,CAACF,OAAO,EAAE,CAAC,EAAE;UACtF;QACF;MACF;MACA,IAAI,IAAI,CAACxD,cAAc,EAAE;QACvB,IAAI0C,IAAI,CAACC,eAAe,CAAC3G,MAAM,IAAI,CAAC,EAAE;UACpC;QACF;MACF;MACA,IAAI,IAAI,CAACiE,iBAAiB,EAAE;QAC1B,IAAIyC,IAAI,CAACC,eAAe,CAAC3G,MAAM,GAAG,CAAC,EAAE;UACnC;QACF;MACF;MACAK,aAAa,CAACsH,IAAI,CAACjB,IAAI,CAAC;IAC1B;IACA,OAAOrG,aAAa;EACtB;EAEAuH,YAAYA,CAACC,KAAa;IACxB,OAAS,IAAI,CAACtC,IAAI,CAACuC,GAAG,CAACD,KAAK,CAAC,EAAEE,OAAO,IAAI,IAAI,CAACxC,IAAI,CAACuC,GAAG,CAACD,KAAK,CAAC,EAAEG,OAAO,IAAM,IAAI,CAAClE,SAAS,IAAI,IAAI,CAACyB,IAAI,CAACuC,GAAG,CAACD,KAAK,CAAC,EAAEE,OAAQ;EAC/H;EAEAE,WAAWA,CAAA;IACT,IAAI,CAAClE,aAAa,GAAG,CAAC,IAAI,CAACe,UAAU,IAAI,IAAI,CAACA,UAAU,KAAK,EAAE,GAAG,IAAI,GAAG,KAAK;EAChF;EAEAoD,KAAKA,CAACC,KAAU;IACd,IAAI,CAAC3D,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,gBAAgB,GAAG,KAAK;EAC/B;EAEAlF,eAAeA,CAAA;IACb6I,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC;IACjB,IAAI,CAAC/K,WAAW,GAAG,KAAK;EAC1B;EAEA8D,aAAaA,CAAC+G,KAAU;IACtBC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,IAAIA,KAAK,CAACG,OAAO,EAAE;MACjB,IAAI,CAACnF,WAAW,CAAC4B,YAAY,CAAC4C,IAAI,CAACQ,KAAK,CAACzB,IAAI,CAAC;IAChD,CAAC,MAAM;MACL,IAAI,CAACvD,WAAW,CAAC4B,YAAY,GAAG,IAAI,CAAC5B,WAAW,CAAC4B,YAAY,CAAC0B,MAAM,CAAC8B,EAAE,IAAIA,EAAE,CAACtB,EAAE,KAAKkB,KAAK,CAACzB,IAAI,CAACO,EAAE,CAAC;IACrG;EACF;EAEAvJ,eAAeA,CAAA;IACb,MAAMqH,YAAY,GAAG,IAAI,CAAC5B,WAAW,CAAC4B,YAAY;IAElD,IAAIA,YAAY,CAAC/E,MAAM,KAAK,CAAC,EAAE;MAC7B,IAAI,CAACwI,oBAAoB,CAAC,kCAAkC,CAAC;MAC7D;IACF;IAEA,MAAM5G,SAAS,GAAG,IAAI,CAACF,WAAW,CAACE,SAAS;IAE5C,IAAIA,SAAS,EAAE;MACb,IAAI,CAAC6G,0BAA0B,CAAC1D,YAAY,CAAC;IAC/C,CAAC,MAAM;MACL,IAAI,CAAC2D,0BAA0B,CAAC3D,YAAY,CAAC;IAC/C;EACF;EAEAhH,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACoF,WAAW,CAAC4B,YAAY,CAAC/E,MAAM,KAAK,CAAC,EAAE;MAC5C,IAAI,CAACwI,oBAAoB,CAAC,kCAAkC,CAAC;MAC7D;IACJ;IAEA,IAAI,CAACrF,WAAW,CAAC4B,YAAY,CAAC4D,OAAO,CAACjC,IAAI,IAAG;MACzC,MAAMkC,WAAW,GAAUlC,IAAI,CAACC,eAAe,CAACkC,GAAG,CAAC7D,IAAI,IAAIA,IAAI,CAAC6B,SAAS,CAAC;MAE3E,MAAMiC,cAAc,GAAyB;QACzCC,MAAM,EAAErC,IAAI,CAACO,EAAE;QACf+B,WAAW,EAAEC,MAAM,CAAC,IAAI,CAACzJ,SAAS,CAACyH,EAAE,CAAC;QACtCiC,UAAU,EAAE,IAAI,CAACC,oBAAoB,CAACP,WAAW,EAAElC,IAAI;OAC1D;MACD,IAAI,CAACnD,kBAAkB,CAAC6F,WAAW,CAACN,cAAc,EAAE,6BAA6B,CAAC;IACtF,CAAC,CAAC;IAEF,IAAI,CAACzC,eAAe,EAAE;EAC1B;EAGE7H,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC2E,WAAW,CAAC4B,YAAY,CAAC/E,MAAM,KAAK,CAAC,EAAE;MAC9C,IAAI,CAACwI,oBAAoB,CAAC,kCAAkC,CAAC;MAC7D;IACF;IACA,KAAK,IAAI9B,IAAI,IAAI,IAAI,CAACvD,WAAW,CAAC4B,YAAY,EAAE;MAC9C,IAAI,CAACxB,kBAAkB,CAAC8F,QAAQ,CAAC3C,IAAI,CAAC;IACxC;EACF;EAEAxI,oBAAoBA,CAAA;IAClB,IAAI,CAACoL,mCAAmC,EAAE;IAC1C,IAAI,IAAI,CAACnG,WAAW,CAAC4B,YAAY,CAAC/E,MAAM,KAAK,CAAC,EAAE;MAC9C,IAAI,CAACwI,oBAAoB,CAAC,kCAAkC,CAAC;MAC7D;IACF;IACA,IAAI,CAAC/E,oBAAoB,CAAC8F,aAAa,CAAC;MACtCC,cAAc,EAAE;0DACoC;MACpDC,cAAc,EAAE,KAAK;MACrBC,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE,aAAa;MACrBC,WAAW,EAAE,qCAAqC;MAClDC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,QAAQ;MACpBC,WAAW,EAAE;KACd,CAAC;IACF,IAAI,CAACtG,oBAAoB,CAACuG,IAAI,EAAE;EAElC;EAEQb,oBAAoBA,CAACP,WAAkB,EAAElC,IAAS;IACxD,IAAI,IAAI,CAAChF,WAAW,CAACE,SAAS,EAAE;MAC9B,OAAOgH,WAAW;IACpB,CAAC,MAAM,IAAI,IAAI,CAACzF,WAAW,CAAC8G,QAAQ,CAACvD,IAAI,CAAC,EAAE;MAC1C,OAAO,CAAC,IAAI,CAAClH,SAAS,CAAC0K,OAAQ,CAACrD,SAAU,CAAC;IAC7C;IACA,OAAO,CAAC,IAAI,CAAC7B,IAAI,CAAC6B,SAAU,CAAC;EAC/B;EAEQ4B,0BAA0BA,CAAC1D,YAAoB;IACrD,IAAI,CAAC,CAAC,IAAI,CAACjG,cAAc,CAACC,qCAAqC,EAAE,IAAI,CAACD,cAAc,CAACK,kBAAkB,CAAC,CAACF,QAAQ,CAAC,IAAI,CAACC,gBAAgB,CAAC,EAAE;MAExI,IAAI,IAAI,CAACM,SAAS,CAACO,iBAAiB,CAACC,MAAM,KAAK,CAAC,EAAE;QACjD,IAAI,CAACmK,+BAA+B,CAACpF,YAAY,CAAC;MACpD,CAAC,MAAM;QACL,IAAI,CAACqF,+BAA+B,CAACrF,YAAY,CAAC;MACpD;IAEF,CAAC,MAAM;MACL,IAAI,CAACqF,+BAA+B,CAACrF,YAAY,CAAC;IACpD;EACF;EAEQ2D,0BAA0BA,CAAC3D,YAAoB;IACrD,MAAM6D,WAAW,GAAU,EAAE;IAE7B,KAAK,IAAIlC,IAAI,IAAI3B,YAAY,EAAE;MAC7B,KAAK,IAAIC,IAAI,IAAI0B,IAAI,CAACC,eAAe,EAAE;QACrCiC,WAAW,CAACjB,IAAI,CAAC3C,IAAI,CAAC6B,SAAS,CAAC;MAClC;MAEA,MAAMwD,YAAY,GAAyB;QACzCtB,MAAM,EAAErC,IAAI,CAACO,EAAE;QACf+B,WAAW,EAAE,CAAC,IAAI,CAACxJ,SAAS,CAACyH,EAAE;QAC/BiC,UAAU,EAAE,CAAC,IAAI,CAAC1J,SAAS,CAAC0K,OAAQ,CAACrD,SAAU;OAChD;MACD,IAAI,CAACtD,kBAAkB,CAAC+G,SAAS,CAACD,YAAY,EAAE,6BAA6B,CAAC;IAChF;IAEA,IAAI,CAAChE,eAAe,EAAE;EACxB;EAEQ8D,+BAA+BA,CAACpF,YAAoB;IAC1D,KAAK,IAAI2B,IAAI,IAAI3B,YAAY,EAAE;MAC7B,MAAMsF,YAAY,GAAyB;QACvCtB,MAAM,EAAErC,IAAI,CAACO,EAAE;QACf+B,WAAW,EAAE,CAAC,IAAI,CAACxJ,SAAS,CAACyH,EAAE;QAC/BiC,UAAU,EAAE,CAAC,IAAI,CAAC1J,SAAS,CAACO,iBAAiB,CAAC,CAAC,CAAC,CAAC8G,SAAU;OAC7D;MACF,IAAI,CAACtD,kBAAkB,CAAC+G,SAAS,CAACD,YAAY,EAAE,2BAA2B,CAAC;IAC9E;EACF;EAEQD,+BAA+BA,CAACrF,YAAoB;IAC1D,IAAI,CAAC7B,cAAc,CAACqH,uBAAuB,CAAC,IAAI,CAAC/G,aAAa,EAAE9G,iCAAiC,EAAE,GAAG,EACpG;MACEiN,MAAM,EAAE,WAAW;MACnBa,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE,oBAAoB;MACjCC,oBAAoB,EAAE,IAAI,CAAClL;KAC5B,EAAE,IAAI,EAAGmL,MAAW,IAAI;MACzBvC,OAAO,CAACC,GAAG,CAACsC,MAAM,CAAC;MACnB,IAAIA,MAAM,IAAIA,MAAM,CAAChB,MAAM,KAAK,WAAW,EAAE;QAC3C,IAAI,CAAC,IAAI,CAACzG,cAAc,CAAC0H,eAAe,CAACD,MAAM,CAAC,EAAE;UAEhD,KAAK,IAAIjE,IAAI,IAAI3B,YAAY,EAAE;YAC7B,IAAI,CAACxB,kBAAkB,CAACsH,WAAW,CAACnE,IAAI,EAAEiE,MAAM,EAAE,IAAI,CAACjJ,WAAW,CAACC,SAAS,CAAC;UAC/E;UACA,IAAI,CAAC0E,eAAe,EAAE;UACtB+B,OAAO,CAACC,GAAG,CAACsC,MAAM,CAAC;QACrB;MACF;IACF,CAAC,CAAC;EACJ;EAEQrB,mCAAmCA,CAAA;IACzC,IAAI,CAACwB,6BAA6B,GAAI,IAAI,CAACrH,oBAAoB,CAACsH,eAAe,CAC5EC,IAAI,CAACpO,GAAG,CAAEqO,GAAG,IAAI;MAEhB,IAAI,CAACxH,oBAAoB,CAACyH,IAAI,EAAE;MAChC9C,OAAO,CAACC,GAAG,CAAC4C,GAAG,CAAC;MAEhB,QAAQA,GAAG,CAACtB,MAAM;QAChB,KAAK,aAAa;UAChBvB,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;UAC1B,IAAI,CAAC8C,4BAA4B,EAAE;MACvC;IACF,CAAC,CAAC,CAAC,CAACC,SAAS,EAAG;IAElB,IAAI,CAACxH,IAAI,CAAC6D,GAAG,CAAC,IAAI,CAACqD,6BAA6B,CAAC;EACnD;EAEQK,4BAA4BA,CAAA;IAClC/C,OAAO,CAACC,GAAG,CAAC,IAAI,CAAClF,WAAW,CAAC4B,YAAY,CAAC;IAC1C,KAAK,IAAI2B,IAAI,IAAI,IAAI,CAACvD,WAAW,CAAC4B,YAAY,EAAE;MAC9CqD,OAAO,CAACC,GAAG,CAAC3B,IAAI,CAAC;MACjB,IAAI,CAACnD,kBAAkB,CAAC8H,qBAAqB,CAAC3E,IAAI,CAAC;IACrD;IACA,IAAI,CAACoE,6BAA6B,CAAC1E,WAAW,EAAE;IAChD,IAAI,CAACC,eAAe,EAAE;EACxB;EAEQmC,oBAAoBA,CAAC8C,GAAW;IACtC,IAAI,IAAI,CAACnI,WAAW,CAAC4B,YAAY,CAAC/E,MAAM,KAAK,CAAC,EAAE;MAC9C,IAAI,CAACoD,YAAY,CAACmI,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAEJ;OACT,CAAC;MACF;IACF;EACF;EAEQ3F,qBAAqBA,CAAA;IAC3B,IAAI,CAAC/B,IAAI,CAAC6D,GAAG,CAAC,IAAI,CAAC/H,oBAAoB,CAACiM,wBAAwB,CAACP,SAAS,CAACH,GAAG,IAAG;MAC/E,IAAI,IAAI,CAAC/H,cAAc,CAAC0H,eAAe,CAACK,GAAG,CAAC,EAAE;QAC5C;MACF;MACA7C,OAAO,CAACC,GAAG,CAAC,IAAI,CAAChI,aAAa,CAAC;MAC/B,IAAI,CAACb,SAAS,GAAGyL,GAAG;MACpB,IAAI,CAAClL,iBAAiB,GAAG,IAAI,CAACP,SAAS,CAACO,iBAAiB;IAC3D,CAAC,CAAC,CAAC;EACL;EAAC,QAAA6L,CAAA,G;qBA1ZU5I,kBAAkB,EAAAnG,EAAA,CAAAgP,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAlP,EAAA,CAAAgP,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAApP,EAAA,CAAAgP,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAAtP,EAAA,CAAAgP,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAxP,EAAA,CAAAgP,iBAAA,CAAAhP,EAAA,CAAAyP,iBAAA,GAAAzP,EAAA,CAAAgP,iBAAA,CAAAU,EAAA,CAAAC,WAAA,GAAA3P,EAAA,CAAAgP,iBAAA,CAAAY,EAAA,CAAAjQ,oBAAA,GAAAK,EAAA,CAAAgP,iBAAA,CAAAa,EAAA,CAAAC,kBAAA,GAAA9P,EAAA,CAAAgP,iBAAA,CAAAe,EAAA,CAAAjQ,aAAA,GAAAE,EAAA,CAAAgP,iBAAA,CAAAgB,EAAA,CAAAC,oBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlB/J,kBAAkB;IAAAgK,SAAA;IAAAC,MAAA;MAAAvJ,KAAA;MAAAC,SAAA;MAAA/D,aAAA;MAAAR,MAAA;MAAAI,SAAA;IAAA;IAAA0N,QAAA,GAAArQ,EAAA,CAAAsQ,kBAAA,CAHlB,CAACxQ,aAAa,EAAEH,oBAAoB,CAAC,GAAAK,EAAA,CAAAuQ,oBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC/BhD7Q,EADF,CAAAC,cAAA,aAA0B,aACQ;QAC9BD,EAAA,CAAAe,MAAA,GACF;QAAAf,EAAA,CAAAU,YAAA,EAAM;QAsDAV,EAnDN,CAAAC,cAAA,aAAkC,aAiDQ,cACE,eAEqB;QADxCD,EAAA,CAAA+Q,gBAAA,2BAAAC,2DAAA3M,MAAA;UAAArE,EAAA,CAAAiR,kBAAA,CAAAH,GAAA,CAAAnL,IAAA,EAAAtB,MAAA,MAAAyM,GAAA,CAAAnL,IAAA,GAAAtB,MAAA;UAAA,OAAAA,MAAA;QAAA,EAAkB;QAArCrE,EAAA,CAAAU,YAAA,EAC2D;QAC3DV,EAAA,CAAAc,SAAA,WAAyC;QAC3Cd,EAAA,CAAAU,YAAA,EAAO;QACPV,EAAA,CAAAC,cAAA,aAE+C;QAA/CD,EAAA,CAAAE,UAAA,mBAAAgR,iDAAA;UAAA,OAASJ,GAAA,CAAAtH,eAAA,EAAiB;QAAA,EAAC;QAF3BxJ,EAAA,CAAAU,YAAA,EAE+C;QAC/CV,EAAA,CAAAsB,UAAA,IAAA6P,iCAAA,iBACyC;QAG/CnR,EAFI,CAAAU,YAAA,EAAM,EACF,EACF;QAGNV,EAAA,CAAAC,cAAA,eAA4D;QAC1DD,EAAA,CAAAsB,UAAA,KAAA8P,kCAAA,kBAA+G;QA0CjHpR,EAAA,CAAAU,YAAA,EAAM;QASNV,EAAA,CAAAe,MAAA,IAEA;QAeAf,EAfA,CAAAsB,UAAA,KAAA+P,2CAAA,2BAIiB,KAAAC,kCAAA,kBAW4B;;;QA1IzCtR,EAAA,CAAA6B,SAAA,GACF;QADE7B,EAAA,CAAAuR,kBAAA,MAAAT,GAAA,CAAAhK,SAAA,MACF;QAsDyB9G,EAAA,CAAA6B,SAAA,GAAkB;QAAlB7B,EAAA,CAAAwR,gBAAA,YAAAV,GAAA,CAAAnL,IAAA,CAAkB;QAIY3F,EAAA,CAAA6B,SAAA,GAA6C;QAA7C7B,EAAA,CAAA8B,UAAA,YAAA9B,EAAA,CAAAyF,eAAA,IAAAgM,GAAA,EAAAX,GAAA,CAAAlO,gBAAA,EAA6C;QAG1F5C,EAAA,CAAA6B,SAAA,EAAY;QAAZ7B,EAAA,CAAA8B,UAAA,SAAAgP,GAAA,CAAAvO,MAAA,CAAY;QAQhBvC,EAAA,CAAA6B,SAAA,GAAsB;QAAtB7B,EAAA,CAAA8B,UAAA,SAAAgP,GAAA,CAAAlO,gBAAA,CAAsB;QAmD9B5C,EAAA,CAAA6B,SAAA,EAEA;QAFA7B,EAAA,CAAAuR,kBAAA,OAAAT,GAAA,CAAA5N,iBAAA,IAAA4N,GAAA,CAAA5N,iBAAA,CAAAC,MAAA,WAAA2N,GAAA,CAAA/N,aAAA,KAAA+N,GAAA,CAAAvO,MAAA,OAEA;QAAevC,EAAA,CAAA6B,SAAA,EAIE;QAJF7B,EAAA,CAAA8B,UAAA,UAAAgP,GAAA,CAAAzO,gBAAA,KAAAyO,GAAA,CAAA7O,cAAA,CAAAsD,4BAAA,IAAAuL,GAAA,CAAAzO,gBAAA,KAAAyO,GAAA,CAAA7O,cAAA,CAAAyP,4BAAA,IAAAZ,GAAA,CAAAzO,gBAAA,KAAAyO,GAAA,CAAA7O,cAAA,CAAAK,kBAAA,KAAAwO,GAAA,CAAArQ,WAAA,CAIE;QAWXT,EAAA,CAAA6B,SAAA,EAAgB;QAAhB7B,EAAA,CAAA8B,UAAA,UAAAgP,GAAA,CAAA5K,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}