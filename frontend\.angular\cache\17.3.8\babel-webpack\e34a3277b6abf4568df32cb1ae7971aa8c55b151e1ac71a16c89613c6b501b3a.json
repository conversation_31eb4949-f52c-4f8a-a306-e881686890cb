{"ast": null, "code": "import { DOCUMENT } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nconst _c0 = (a0, a1) => ({\n  \"width\": a0,\n  \"background-color\": a1\n});\nconst _c1 = a0 => ({\n  \"left\": a0\n});\nfunction MultiHandleSliderComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"div\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const variant_r1 = ctx.$implicit;\n    const i_r2 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction2(1, _c0, variant_r1[ctx_r2.prop] + \"%\", ctx_r2.colorList[i_r2]));\n  }\n}\nfunction MultiHandleSliderComponent_ng_container_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵlistener(\"mousedown\", function MultiHandleSliderComponent_ng_container_3_div_1_Template_div_mousedown_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const i_r5 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.mousedown($event, i_r5));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r5 = i0.ɵɵnextContext().index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(1, _c1, ctx_r2.getLeftOffset(i_r5) + \"%\"));\n  }\n}\nfunction MultiHandleSliderComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MultiHandleSliderComponent_ng_container_3_div_1_Template, 1, 3, \"div\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const i_r5 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r5 < ctx_r2.variants.length - 1);\n  }\n}\nexport class MultiHandleSliderComponent {\n  constructor(elementRef, renderer, document) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.document = document;\n    this.colorList = [];\n    this.curX = 0;\n    this.curL = 0;\n    this.curR = 0;\n    this.percTotoal = 100;\n    this.startLeftPosition = 0;\n    this.startRightPosition = 0;\n    this.mousePositionX = 0;\n    this.mouseup = this.unboundMouseup.bind(this);\n    this.dragging = this.unboundDragging.bind(this);\n  }\n  ngOnInit() {}\n  mousedown(e, index) {\n    if (e.button === 0) {\n      this.selectedIndex = index;\n      this.startLeftPosition = this.getPerc(this.selectedIndex);\n      this.startRightPosition = this.getPerc(this.selectedIndex + 1);\n      this.mousePositionX = e.pageX;\n      // if listeners exist, first Remove listeners\n      if (this.mousemoveEvent) {\n        this.mousemoveEvent();\n      }\n      if (this.mouseupEvent) {\n        this.mouseupEvent();\n      }\n      this.mousemoveEvent = this.renderer.listen('document', 'mousemove', this.dragging);\n      this.mouseupEvent = this.renderer.listen('document', 'mouseup', this.mouseup);\n    }\n  }\n  unboundMouseup(event) {\n    // Remove listeners\n    this.mousemoveEvent();\n    this.mouseupEvent();\n  }\n  unboundDragging(e) {\n    const sliderElWidth = document.getElementsByClassName('slider-control')[0].clientWidth;\n    const draggedPosition = (e.pageX - this.mousePositionX) / sliderElWidth * this.percTotoal;\n    // console.log('unboundDragging ::', sliderElWidth, '---', draggedPosition);\n    if (draggedPosition < -this.startLeftPosition || draggedPosition > this.startRightPosition) {\n      return;\n    }\n    this.setPerc(Math.round(this.startLeftPosition + draggedPosition));\n    this.setPerc(Math.round(this.startRightPosition - draggedPosition), this.selectedIndex + 1);\n  }\n  getPerc(i) {\n    const _i = i === 0 || i ? i : this.selectedIndex;\n    if (this.prop) {\n      return Number(this.variants[_i][this.prop]);\n    } else {\n      return Number(this.variants[_i]);\n    }\n  }\n  setPerc(perc, i) {\n    const _i = i === 0 || i ? i : this.selectedIndex;\n    if (this.prop) {\n      return this.variants[_i][this.prop] = perc;\n    } else {\n      return this.variants[_i] = perc;\n    }\n  }\n  // Need to re-factor\n  getLeftOffset(index) {\n    const _this = this;\n    this.percTotoal = this.variants.reduce(function (sum, item, i) {\n      return sum + _this.getPerc(i);\n    }, 0);\n    let x = 0,\n      pRunningTotal = 0;\n    // console.log('updatePercWidth total ::', this.percTotoal, this.variants, this.variants.length, this.variants.length - 1);\n    for (let i = 0, l = this.variants.length - 1; i < l; i++) {\n      // console.log('updatePercWidth loop ::', i);\n      pRunningTotal += this.getPerc(i);\n      x = pRunningTotal / this.percTotoal * 100;\n      // console.log(document.getElementsByClassName('slider-handle')[i], i);\n      if (document.getElementsByClassName('slider-handle')[i]) {\n        const handleElement = document.getElementsByClassName('slider-handle')[i];\n        handleElement.style.left = x + '%';\n      }\n    }\n  }\n  static #_ = this.ɵfac = function MultiHandleSliderComponent_Factory(t) {\n    return new (t || MultiHandleSliderComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(DOCUMENT));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MultiHandleSliderComponent,\n    selectors: [[\"app-multi-handle-slider\"]],\n    inputs: {\n      variants: \"variants\",\n      prop: \"prop\",\n      colorList: \"colorList\"\n    },\n    decls: 4,\n    vars: 2,\n    consts: [[1, \"slider-control\"], [4, \"ngFor\", \"ngForOf\"], [1, \"slider-handle-cont\"], [1, \"slider\", 3, \"ngStyle\"], [\"class\", \"slider-handle\", 3, \"ngStyle\", \"mousedown\", 4, \"ngIf\"], [1, \"slider-handle\", 3, \"mousedown\", \"ngStyle\"]],\n    template: function MultiHandleSliderComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, MultiHandleSliderComponent_ng_container_1_Template, 2, 4, \"ng-container\", 1);\n        i0.ɵɵelementStart(2, \"div\", 2);\n        i0.ɵɵtemplate(3, MultiHandleSliderComponent_ng_container_3_Template, 2, 1, \"ng-container\", 1);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngForOf\", ctx.variants);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.variants);\n      }\n    },\n    dependencies: [i1.NgForOf, i1.NgIf, i1.NgStyle],\n    styles: [\".slider-control[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 3em;\\n  margin-top: 3em;\\n  position: relative;\\n}\\n\\n.slider[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: inline-block;\\n  height: 2.5px;\\n  box-sizing: border-box;\\n  vertical-align: top;\\n  background: #ccc;\\n}\\n\\n.slider-handle-cont[_ngcontent-%COMP%] {\\n  margin-top: -18px;\\n  position: relative;\\n}\\n\\n.slider-handle[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: -8px;\\n  height: 15px;\\n  width: 15px;\\n  background: #fff;\\n  cursor: pointer;\\n  border-radius: 50%;\\n  background-color: #014666 !important;\\n  z-index: 10 !important;\\n}\\n\\n.slider-handle[_ngcontent-%COMP%]:hover {\\n  background: #ddd;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL211bHRpLWhhbmRsZS1zbGlkZXIvbXVsdGktaGFuZGxlLXNsaWRlci5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLFdBQUE7RUFDQSxrQkFBQTtFQUNBLGVBQUE7RUFDQSxrQkFBQTtBQUNKOztBQUVFO0VBQ0Usa0JBQUE7RUFDQSxxQkFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7QUFDSjs7QUFFRTtFQUNFLGlCQUFBO0VBQ0Esa0JBQUE7QUFDSjs7QUFFRTtFQUNJLGtCQUFBO0VBQ0EsU0FBQTtFQUNBLFlBQUE7RUFDQSxXQUFBO0VBQ0EsZ0JBQUE7RUFDQSxlQUFBO0VBQ0Esa0JBQUE7RUFDQSxvQ0FBQTtFQUNBLHNCQUFBO0FBQ047O0FBRUU7RUFDRSxnQkFBQTtBQUNKIiwic291cmNlc0NvbnRlbnQiOlsiLnNsaWRlci1jb250cm9sIHtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgbWFyZ2luLWJvdHRvbTogM2VtO1xyXG4gICAgbWFyZ2luLXRvcDogM2VtO1xyXG4gICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gIH1cclxuICBcclxuICAuc2xpZGVyIHtcclxuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsgXHJcbiAgICBkaXNwbGF5OmlubGluZS1ibG9jaztcclxuICAgIGhlaWdodDogMi41cHg7XHJcbiAgICBib3gtc2l6aW5nOiBib3JkZXItYm94O1xyXG4gICAgdmVydGljYWwtYWxpZ246IHRvcDtcclxuICAgIGJhY2tncm91bmQ6ICNjY2M7XHJcbiAgfVxyXG4gIFxyXG4gIC5zbGlkZXItaGFuZGxlLWNvbnR7XHJcbiAgICBtYXJnaW4tdG9wOiAtMThweDtcclxuICAgIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxuICB9XHJcbiAgXHJcbiAgLnNsaWRlci1oYW5kbGUge1xyXG4gICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgIHRvcDotOHB4O1xyXG4gICAgICBoZWlnaHQ6IDE1cHg7XHJcbiAgICAgIHdpZHRoOiAxNXB4O1xyXG4gICAgICBiYWNrZ3JvdW5kOiAjZmZmO1xyXG4gICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICAgIGJvcmRlci1yYWRpdXM6IDUwJTtcclxuICAgICAgYmFja2dyb3VuZC1jb2xvcjogIzAxNDY2NiAhaW1wb3J0YW50O1xyXG4gICAgICB6LWluZGV4OiAxMCFpbXBvcnRhbnQ7XHJcbiAgfVxyXG4gIFxyXG4gIC5zbGlkZXItaGFuZGxlOmhvdmVyIHtcclxuICAgIGJhY2tncm91bmQ6ICNkZGQ7XHJcbiAgfSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "names": ["DOCUMENT", "i0", "ɵɵelementContainerStart", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "variant_r1", "ctx_r2", "prop", "colorList", "i_r2", "ɵɵelementStart", "ɵɵlistener", "MultiHandleSliderComponent_ng_container_3_div_1_Template_div_mousedown_0_listener", "$event", "ɵɵrestoreView", "_r4", "i_r5", "ɵɵnextContext", "index", "ɵɵresetView", "mousedown", "ɵɵelementEnd", "ɵɵpureFunction1", "_c1", "getLeftOffset", "ɵɵtemplate", "MultiHandleSliderComponent_ng_container_3_div_1_Template", "variants", "length", "MultiHandleSliderComponent", "constructor", "elementRef", "renderer", "document", "curX", "curL", "curR", "percTotoal", "startLeftPosition", "startRightPosition", "mousePositionX", "mouseup", "unboundMouseup", "bind", "dragging", "unboundDragging", "ngOnInit", "e", "button", "selectedIndex", "getPerc", "pageX", "mousemoveEvent", "mouseupEvent", "listen", "event", "slider<PERSON><PERSON><PERSON><PERSON><PERSON>", "getElementsByClassName", "clientWidth", "draggedPosition", "setPerc", "Math", "round", "i", "_i", "Number", "perc", "_this", "reduce", "sum", "item", "x", "pRunningTotal", "l", "handleElement", "style", "left", "_", "ɵɵdirectiveInject", "ElementRef", "Renderer2", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "MultiHandleSliderComponent_Template", "rf", "ctx", "MultiHandleSliderComponent_ng_container_1_Template", "MultiHandleSliderComponent_ng_container_3_Template"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\multi-handle-slider\\multi-handle-slider.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\multi-handle-slider\\multi-handle-slider.component.html"], "sourcesContent": ["import {\r\n  Component, OnInit, Injectable, Inject, HostListener, Input,\r\n  Output, EventEmitter, ContentChildren, QueryList, ElementRef, Renderer2\r\n} from '@angular/core';\r\n\r\nimport { DOCUMENT } from '@angular/common';\r\n\r\n@Component({\r\n  selector: 'app-multi-handle-slider',\r\n  templateUrl: './multi-handle-slider.component.html',\r\n  styleUrls: ['./multi-handle-slider.component.scss'],\r\n})\r\nexport class MultiHandleSliderComponent {\r\n\r\n  constructor(private elementRef: ElementRef,\r\n    private renderer: Renderer2,\r\n    @Inject(DOCUMENT) private document: HTMLDocument) {\r\n    this.mouseup = this.unboundMouseup.bind(this);\r\n    this.dragging = this.unboundDragging.bind(this);\r\n  }\r\n  selectedIndex: any;\r\n  @Input() variants: any;\r\n  @Input() prop: any;\r\n  @Input() colorList: any = [];\r\n\r\n  curX = 0;\r\n  curL = 0;\r\n  curR = 0;\r\n\r\n  percTotoal = 100;\r\n  startLeftPosition = 0;\r\n  startRightPosition = 0;\r\n  mousePositionX = 0;\r\n  mousemoveEvent: any;\r\n  mouseupEvent: any;\r\n\r\n  mouseup: (e: any) => void;\r\n\r\n  dragging: (event: any) => void;\r\n\r\n  ngOnInit() {\r\n  }\r\n\r\n  mousedown(e: any, index: any) {\r\n    if (e.button === 0) {\r\n      this.selectedIndex = index;\r\n      this.startLeftPosition = this.getPerc(this.selectedIndex);\r\n      this.startRightPosition = this.getPerc(this.selectedIndex + 1);\r\n      this.mousePositionX = e.pageX;\r\n\r\n      // if listeners exist, first Remove listeners\r\n      if (this.mousemoveEvent) {\r\n        this.mousemoveEvent();\r\n      }\r\n\r\n      if (this.mouseupEvent) {\r\n        this.mouseupEvent();\r\n      }\r\n\r\n      this.mousemoveEvent = this.renderer.listen('document', 'mousemove', this.dragging);\r\n      this.mouseupEvent = this.renderer.listen('document', 'mouseup', this.mouseup);\r\n    }\r\n  }\r\n\r\n  unboundMouseup(event: any) {\r\n    // Remove listeners\r\n    this.mousemoveEvent();\r\n    this.mouseupEvent();\r\n  }\r\n\r\n  unboundDragging(e: any) {\r\n    const sliderElWidth = document.getElementsByClassName('slider-control')[0].clientWidth;\r\n    const draggedPosition = (e.pageX - this.mousePositionX) / sliderElWidth * this.percTotoal;\r\n    // console.log('unboundDragging ::', sliderElWidth, '---', draggedPosition);\r\n    if (draggedPosition < -this.startLeftPosition || draggedPosition > this.startRightPosition) {\r\n      return;\r\n    }\r\n\r\n    this.setPerc(Math.round(this.startLeftPosition + draggedPosition));\r\n    this.setPerc(Math.round(this.startRightPosition - draggedPosition), this.selectedIndex + 1);\r\n  }\r\n\r\n  getPerc(i?: any) {\r\n    const _i = (i === 0 || i) ? i : this.selectedIndex;\r\n    if (this.prop) {\r\n      return Number(this.variants[_i][this.prop]);\r\n    } else {\r\n      return Number(this.variants[_i]);\r\n    }\r\n  }\r\n\r\n  setPerc(perc: any, i?: any) {\r\n    const _i = (i === 0 || i) ? i : this.selectedIndex;\r\n    if (this.prop) {\r\n      return this.variants[_i][this.prop] = perc;\r\n    } else {\r\n      return this.variants[_i] = perc;\r\n    }\r\n  }\r\n\r\n  // Need to re-factor\r\n  getLeftOffset(index?: any) {\r\n    const _this = this;\r\n    this.percTotoal = this.variants.reduce(function (sum: any, item: any, i: any) {\r\n      return sum + _this.getPerc(i);\r\n    }, 0);\r\n    let x = 0, pRunningTotal = 0;\r\n\r\n    // console.log('updatePercWidth total ::', this.percTotoal, this.variants, this.variants.length, this.variants.length - 1);\r\n\r\n    for (let i = 0, l = this.variants.length - 1; i < l; i++) {\r\n      // console.log('updatePercWidth loop ::', i);\r\n      pRunningTotal += this.getPerc(i);\r\n      x = pRunningTotal / this.percTotoal * 100;\r\n      // console.log(document.getElementsByClassName('slider-handle')[i], i);\r\n      if (document.getElementsByClassName('slider-handle')[i]) {\r\n        const handleElement = document.getElementsByClassName('slider-handle')[i] as HTMLElement;\r\n        handleElement.style.left = x + '%';\r\n      }\r\n    }\r\n  }\r\n}\r\n", "<div class=\"slider-control\">\r\n    <ng-container *ngFor=\"let variant of variants; let i=index;\" >\r\n      <div class=\"slider\" [ngStyle]=\"{'width' : variant[prop]+'%', 'background-color' : colorList[i] }\">\r\n      </div>\r\n    </ng-container>\r\n  \r\n    <div class=\"slider-handle-cont\">\r\n      <ng-container *ngFor=\"let variant of variants; let i=index;\">\r\n          <div class=\"slider-handle\" \r\n              *ngIf=\"i < variants.length -1\"\r\n              [ngStyle]=\"{'left' : getLeftOffset(i)+'%' }\"\r\n            (mousedown)=\"mousedown($event, i)\">\r\n          </div>\r\n      </ng-container>\r\n    </div>\r\n  </div>"], "mappings": "AAKA,SAASA,QAAQ,QAAQ,iBAAiB;;;;;;;;;;;;ICJtCC,EAAA,CAAAC,uBAAA,GAA8D;IAC5DD,EAAA,CAAAE,SAAA,aACM;;;;;;;IADcF,EAAA,CAAAG,SAAA,EAA6E;IAA7EH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAK,eAAA,IAAAC,GAAA,EAAAC,UAAA,CAAAC,MAAA,CAAAC,IAAA,SAAAD,MAAA,CAAAE,SAAA,CAAAC,IAAA,GAA6E;;;;;;IAM7FX,EAAA,CAAAY,cAAA,aAGqC;IAAnCZ,EAAA,CAAAa,UAAA,uBAAAC,kFAAAC,MAAA;MAAAf,EAAA,CAAAgB,aAAA,CAAAC,GAAA;MAAA,MAAAC,IAAA,GAAAlB,EAAA,CAAAmB,aAAA,GAAAC,KAAA;MAAA,MAAAZ,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAqB,WAAA,CAAab,MAAA,CAAAc,SAAA,CAAAP,MAAA,EAAAG,IAAA,CAAoB;IAAA,EAAC;IACpClB,EAAA,CAAAuB,YAAA,EAAM;;;;;IAFFvB,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAAwB,eAAA,IAAAC,GAAA,EAAAjB,MAAA,CAAAkB,aAAA,CAAAR,IAAA,SAA4C;;;;;IAHpDlB,EAAA,CAAAC,uBAAA,GAA6D;IACzDD,EAAA,CAAA2B,UAAA,IAAAC,wDAAA,iBAGqC;;;;;;IAFhC5B,EAAA,CAAAG,SAAA,EAA4B;IAA5BH,EAAA,CAAAI,UAAA,SAAAc,IAAA,GAAAV,MAAA,CAAAqB,QAAA,CAAAC,MAAA,KAA4B;;;ADG3C,OAAM,MAAOC,0BAA0B;EAErCC,YAAoBC,UAAsB,EAChCC,QAAmB,EACDC,QAAsB;IAF9B,KAAAF,UAAU,GAAVA,UAAU;IACpB,KAAAC,QAAQ,GAARA,QAAQ;IACU,KAAAC,QAAQ,GAARA,QAAQ;IAO3B,KAAAzB,SAAS,GAAQ,EAAE;IAE5B,KAAA0B,IAAI,GAAG,CAAC;IACR,KAAAC,IAAI,GAAG,CAAC;IACR,KAAAC,IAAI,GAAG,CAAC;IAER,KAAAC,UAAU,GAAG,GAAG;IAChB,KAAAC,iBAAiB,GAAG,CAAC;IACrB,KAAAC,kBAAkB,GAAG,CAAC;IACtB,KAAAC,cAAc,GAAG,CAAC;IAfhB,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAC7C,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,eAAe,CAACF,IAAI,CAAC,IAAI,CAAC;EACjD;EAqBAG,QAAQA,CAAA,GACR;EAEA1B,SAASA,CAAC2B,CAAM,EAAE7B,KAAU;IAC1B,IAAI6B,CAAC,CAACC,MAAM,KAAK,CAAC,EAAE;MAClB,IAAI,CAACC,aAAa,GAAG/B,KAAK;MAC1B,IAAI,CAACoB,iBAAiB,GAAG,IAAI,CAACY,OAAO,CAAC,IAAI,CAACD,aAAa,CAAC;MACzD,IAAI,CAACV,kBAAkB,GAAG,IAAI,CAACW,OAAO,CAAC,IAAI,CAACD,aAAa,GAAG,CAAC,CAAC;MAC9D,IAAI,CAACT,cAAc,GAAGO,CAAC,CAACI,KAAK;MAE7B;MACA,IAAI,IAAI,CAACC,cAAc,EAAE;QACvB,IAAI,CAACA,cAAc,EAAE;MACvB;MAEA,IAAI,IAAI,CAACC,YAAY,EAAE;QACrB,IAAI,CAACA,YAAY,EAAE;MACrB;MAEA,IAAI,CAACD,cAAc,GAAG,IAAI,CAACpB,QAAQ,CAACsB,MAAM,CAAC,UAAU,EAAE,WAAW,EAAE,IAAI,CAACV,QAAQ,CAAC;MAClF,IAAI,CAACS,YAAY,GAAG,IAAI,CAACrB,QAAQ,CAACsB,MAAM,CAAC,UAAU,EAAE,SAAS,EAAE,IAAI,CAACb,OAAO,CAAC;IAC/E;EACF;EAEAC,cAAcA,CAACa,KAAU;IACvB;IACA,IAAI,CAACH,cAAc,EAAE;IACrB,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAR,eAAeA,CAACE,CAAM;IACpB,MAAMS,aAAa,GAAGvB,QAAQ,CAACwB,sBAAsB,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAACC,WAAW;IACtF,MAAMC,eAAe,GAAG,CAACZ,CAAC,CAACI,KAAK,GAAG,IAAI,CAACX,cAAc,IAAIgB,aAAa,GAAG,IAAI,CAACnB,UAAU;IACzF;IACA,IAAIsB,eAAe,GAAG,CAAC,IAAI,CAACrB,iBAAiB,IAAIqB,eAAe,GAAG,IAAI,CAACpB,kBAAkB,EAAE;MAC1F;IACF;IAEA,IAAI,CAACqB,OAAO,CAACC,IAAI,CAACC,KAAK,CAAC,IAAI,CAACxB,iBAAiB,GAAGqB,eAAe,CAAC,CAAC;IAClE,IAAI,CAACC,OAAO,CAACC,IAAI,CAACC,KAAK,CAAC,IAAI,CAACvB,kBAAkB,GAAGoB,eAAe,CAAC,EAAE,IAAI,CAACV,aAAa,GAAG,CAAC,CAAC;EAC7F;EAEAC,OAAOA,CAACa,CAAO;IACb,MAAMC,EAAE,GAAID,CAAC,KAAK,CAAC,IAAIA,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACd,aAAa;IAClD,IAAI,IAAI,CAAC1C,IAAI,EAAE;MACb,OAAO0D,MAAM,CAAC,IAAI,CAACtC,QAAQ,CAACqC,EAAE,CAAC,CAAC,IAAI,CAACzD,IAAI,CAAC,CAAC;IAC7C,CAAC,MAAM;MACL,OAAO0D,MAAM,CAAC,IAAI,CAACtC,QAAQ,CAACqC,EAAE,CAAC,CAAC;IAClC;EACF;EAEAJ,OAAOA,CAACM,IAAS,EAAEH,CAAO;IACxB,MAAMC,EAAE,GAAID,CAAC,KAAK,CAAC,IAAIA,CAAC,GAAIA,CAAC,GAAG,IAAI,CAACd,aAAa;IAClD,IAAI,IAAI,CAAC1C,IAAI,EAAE;MACb,OAAO,IAAI,CAACoB,QAAQ,CAACqC,EAAE,CAAC,CAAC,IAAI,CAACzD,IAAI,CAAC,GAAG2D,IAAI;IAC5C,CAAC,MAAM;MACL,OAAO,IAAI,CAACvC,QAAQ,CAACqC,EAAE,CAAC,GAAGE,IAAI;IACjC;EACF;EAEA;EACA1C,aAAaA,CAACN,KAAW;IACvB,MAAMiD,KAAK,GAAG,IAAI;IAClB,IAAI,CAAC9B,UAAU,GAAG,IAAI,CAACV,QAAQ,CAACyC,MAAM,CAAC,UAAUC,GAAQ,EAAEC,IAAS,EAAEP,CAAM;MAC1E,OAAOM,GAAG,GAAGF,KAAK,CAACjB,OAAO,CAACa,CAAC,CAAC;IAC/B,CAAC,EAAE,CAAC,CAAC;IACL,IAAIQ,CAAC,GAAG,CAAC;MAAEC,aAAa,GAAG,CAAC;IAE5B;IAEA,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEU,CAAC,GAAG,IAAI,CAAC9C,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAEmC,CAAC,GAAGU,CAAC,EAAEV,CAAC,EAAE,EAAE;MACxD;MACAS,aAAa,IAAI,IAAI,CAACtB,OAAO,CAACa,CAAC,CAAC;MAChCQ,CAAC,GAAGC,aAAa,GAAG,IAAI,CAACnC,UAAU,GAAG,GAAG;MACzC;MACA,IAAIJ,QAAQ,CAACwB,sBAAsB,CAAC,eAAe,CAAC,CAACM,CAAC,CAAC,EAAE;QACvD,MAAMW,aAAa,GAAGzC,QAAQ,CAACwB,sBAAsB,CAAC,eAAe,CAAC,CAACM,CAAC,CAAgB;QACxFW,aAAa,CAACC,KAAK,CAACC,IAAI,GAAGL,CAAC,GAAG,GAAG;MACpC;IACF;EACF;EAAC,QAAAM,CAAA,G;qBA5GUhD,0BAA0B,EAAA/B,EAAA,CAAAgF,iBAAA,CAAAhF,EAAA,CAAAiF,UAAA,GAAAjF,EAAA,CAAAgF,iBAAA,CAAAhF,EAAA,CAAAkF,SAAA,GAAAlF,EAAA,CAAAgF,iBAAA,CAI3BjF,QAAQ;EAAA;EAAA,QAAAoF,EAAA,G;UAJPpD,0BAA0B;IAAAqD,SAAA;IAAAC,MAAA;MAAAxD,QAAA;MAAApB,IAAA;MAAAC,SAAA;IAAA;IAAA4E,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZvC3F,EAAA,CAAAY,cAAA,aAA4B;QACxBZ,EAAA,CAAA2B,UAAA,IAAAkE,kDAAA,0BAA8D;QAK9D7F,EAAA,CAAAY,cAAA,aAAgC;QAC9BZ,EAAA,CAAA2B,UAAA,IAAAmE,kDAAA,0BAA6D;QAQjE9F,EADE,CAAAuB,YAAA,EAAM,EACF;;;QAd8BvB,EAAA,CAAAG,SAAA,EAAa;QAAbH,EAAA,CAAAI,UAAA,YAAAwF,GAAA,CAAA/D,QAAA,CAAa;QAMX7B,EAAA,CAAAG,SAAA,GAAa;QAAbH,EAAA,CAAAI,UAAA,YAAAwF,GAAA,CAAA/D,QAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}