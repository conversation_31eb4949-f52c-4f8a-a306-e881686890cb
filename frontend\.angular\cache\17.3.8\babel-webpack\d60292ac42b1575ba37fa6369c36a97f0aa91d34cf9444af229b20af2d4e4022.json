{"ast": null, "code": "import { inject, signal } from '@angular/core';\nimport { SubSink } from 'subsink';\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/certificate.service\";\nimport * as i2 from \"src/app/core/services/auth.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"primeng/tabview\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"primeng/table\";\nimport * as i7 from \"primeng/carousel\";\nimport * as i8 from \"primeng/dialog\";\nimport * as i9 from \"../../shared/share-button/share-button.component\";\nconst _c0 = () => ({\n  width: \"50vw\"\n});\nconst _c1 = () => ({\n  \"960px\": \"75vw\"\n});\nfunction CertificateComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 25)(1, \"div\", 11);\n    i0.ɵɵlistener(\"click\", function CertificateComponent_ng_template_12_Template_div_click_1_listener() {\n      const language_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.selectLanguage(language_r3));\n    });\n    i0.ɵɵelement(2, \"img\", 26);\n    i0.ɵɵelementStart(3, \"span\", 27);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const language_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"src\", \"/assets/icons/languages/\", language_r3, \".svg\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(language_r3);\n  }\n}\nfunction CertificateComponent_p_carousel_16_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 30);\n    i0.ɵɵelement(2, \"img\", 31)(3, \"img\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 33);\n    i0.ɵɵtext(5);\n    i0.ɵɵelement(6, \"br\");\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"titlecase\");\n    i0.ɵɵelement(9, \"br\");\n    i0.ɵɵelementStart(10, \"div\", 34)(11, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function CertificateComponent_p_carousel_16_ng_template_1_Template_button_click_11_listener() {\n      const certificate_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.viewPdf(certificate_r6.level, certificate_r6.language, certificate_r6.type));\n    });\n    i0.ɵɵelement(12, \"img\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(13, \"app-share-button\", 37);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const certificate_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", certificate_r6.type == \"completion\" ? \"/assets/images/dashboard/completion.png\" : \"/assets/images/dashboard/attendance.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", certificate_r6.language, \" \", certificate_r6.level, \" Certificate \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" of \", i0.ɵɵpipeBind1(8, 6, certificate_r6.type), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"topic\", \"MyLingoTrip Certificate\")(\"componentId\", \"Certificate\" + certificate_r6.id);\n  }\n}\nfunction CertificateComponent_p_carousel_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-carousel\", 28);\n    i0.ɵɵtemplate(1, CertificateComponent_p_carousel_16_ng_template_1_Template, 14, 8, \"ng-template\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r3.certificates.slice(-3))(\"numVisible\", 1)(\"numScroll\", 1)(\"circular\", false);\n  }\n}\nfunction CertificateComponent_ng_template_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵelement(1, \"img\", 39);\n    i0.ɵɵelementStart(2, \"div\", 40);\n    i0.ɵɵtext(3, \"You haven\\u2019t received\");\n    i0.ɵɵelement(4, \"br\");\n    i0.ɵɵtext(5, \" any certifications yet\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CertificateComponent_p_table_22_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 44)(1, \"th\");\n    i0.ɵɵtext(2, \"Level\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Hours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"Language\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CertificateComponent_p_table_22_ng_template_2_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"img\", 52);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction CertificateComponent_p_table_22_ng_template_2_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 36);\n  }\n}\nfunction CertificateComponent_p_table_22_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 45)(1, \"td\", 46)(2, \"div\");\n    i0.ɵɵelement(3, \"img\", 47);\n    i0.ɵɵelementStart(4, \"span\", 48);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"td\", 49);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 49);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 49);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"td\", 49)(14, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function CertificateComponent_p_table_22_ng_template_2_Template_button_click_14_listener() {\n      const certificate_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.viewPdf(certificate_r8.level, certificate_r8.language, certificate_r8.type, ctx_r3.i, certificate_r8.hours));\n    });\n    i0.ɵɵtemplate(15, CertificateComponent_p_table_22_ng_template_2_ng_container_15_Template, 2, 0, \"ng-container\", 50)(16, CertificateComponent_p_table_22_ng_template_2_ng_template_16_Template, 1, 0, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"app-share-button\", 51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const certificate_r8 = ctx.$implicit;\n    const downloadButton_r9 = i0.ɵɵreference(17);\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(certificate_r8.level);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", certificate_r8.hours, \" hours\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(certificate_r8.language);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(12, 9, certificate_r8.type));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.isDownloadLoading[ctx_r3.i])(\"ngIfElse\", downloadButton_r9);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"topic\", \"MyLingoTrip Certificate\")(\"link\", ctx_r3.shareUrl + certificate_r8.urlParam)(\"componentId\", \"certificate\" + certificate_r8.id);\n  }\n}\nfunction CertificateComponent_p_table_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-table\", 41);\n    i0.ɵɵtemplate(1, CertificateComponent_p_table_22_ng_template_1_Template, 11, 0, \"ng-template\", 42)(2, CertificateComponent_p_table_22_ng_template_2_Template, 19, 11, \"ng-template\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r3.allCertificates());\n  }\n}\nfunction CertificateComponent_ng_template_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵelement(1, \"img\", 39);\n    i0.ɵɵelementStart(2, \"div\", 53);\n    i0.ɵɵtext(3, \"No Certifications to show\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CertificateComponent_p_table_26_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 44)(1, \"th\");\n    i0.ɵɵtext(2, \"Level\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Hours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"Language\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CertificateComponent_p_table_26_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 45)(1, \"td\", 46)(2, \"div\");\n    i0.ɵɵelement(3, \"img\", 47);\n    i0.ɵɵelementStart(4, \"span\", 48);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"td\", 49);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 49);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 49);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 49)(13, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function CertificateComponent_p_table_26_ng_template_2_Template_button_click_13_listener() {\n      const certificate_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.view(certificate_r11.level, certificate_r11.language));\n    });\n    i0.ɵɵelement(14, \"img\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 54)(16, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function CertificateComponent_p_table_26_ng_template_2_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleShareButtons());\n    });\n    i0.ɵɵelement(17, \"img\", 56);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(18, \"svg\", 57);\n    i0.ɵɵelement(19, \"path\", 58);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(20, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function CertificateComponent_p_table_26_ng_template_2_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleShareButtons());\n    });\n    i0.ɵɵelement(21, \"img\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function CertificateComponent_p_table_26_ng_template_2_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleShareButtons());\n    });\n    i0.ɵɵelement(23, \"img\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function CertificateComponent_p_table_26_ng_template_2_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleShareButtons());\n    });\n    i0.ɵɵelement(25, \"img\", 64);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const certificate_r11 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(certificate_r11.level);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", certificate_r11.hours, \" hours\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(certificate_r11.language);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(certificate_r11.type);\n  }\n}\nfunction CertificateComponent_p_table_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-table\", 41);\n    i0.ɵɵtemplate(1, CertificateComponent_p_table_26_ng_template_1_Template, 11, 0, \"ng-template\", 42)(2, CertificateComponent_p_table_26_ng_template_2_Template, 26, 4, \"ng-template\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r3.completionCertificates);\n  }\n}\nfunction CertificateComponent_ng_template_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵelement(1, \"img\", 39);\n    i0.ɵɵelementStart(2, \"div\", 53);\n    i0.ɵɵtext(3, \"No Certifications to show\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CertificateComponent_p_table_30_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 44)(1, \"th\");\n    i0.ɵɵtext(2, \"Level\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"th\");\n    i0.ɵɵtext(4, \"Hours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"th\");\n    i0.ɵɵtext(6, \"Language\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"th\");\n    i0.ɵɵtext(8, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"th\");\n    i0.ɵɵtext(10, \"Actions\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CertificateComponent_p_table_30_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 45)(1, \"td\", 46)(2, \"div\");\n    i0.ɵɵelement(3, \"img\", 47);\n    i0.ɵɵelementStart(4, \"span\", 48);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"td\", 49);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"td\", 49);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"td\", 49);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\", 49)(13, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function CertificateComponent_p_table_30_ng_template_2_Template_button_click_13_listener() {\n      const certificate_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.view(certificate_r13.level, certificate_r13.language));\n    });\n    i0.ɵɵelement(14, \"img\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 54)(16, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function CertificateComponent_p_table_30_ng_template_2_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleShareButtons());\n    });\n    i0.ɵɵelement(17, \"img\", 56);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(18, \"svg\", 57);\n    i0.ɵɵelement(19, \"path\", 58);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(20, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function CertificateComponent_p_table_30_ng_template_2_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleShareButtons());\n    });\n    i0.ɵɵelement(21, \"img\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"button\", 61);\n    i0.ɵɵlistener(\"click\", function CertificateComponent_p_table_30_ng_template_2_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleShareButtons());\n    });\n    i0.ɵɵelement(23, \"img\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"button\", 63);\n    i0.ɵɵlistener(\"click\", function CertificateComponent_p_table_30_ng_template_2_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r3 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r3.toggleShareButtons());\n    });\n    i0.ɵɵelement(25, \"img\", 64);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const certificate_r13 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(certificate_r13.level);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", certificate_r13.hours, \" hours\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(certificate_r13.language);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(certificate_r13.type);\n  }\n}\nfunction CertificateComponent_p_table_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-table\", 41);\n    i0.ɵɵtemplate(1, CertificateComponent_p_table_30_ng_template_1_Template, 11, 0, \"ng-template\", 42)(2, CertificateComponent_p_table_30_ng_template_2_Template, 26, 4, \"ng-template\", 43);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", ctx_r3.attendanceCertificates);\n  }\n}\nfunction CertificateComponent_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵelement(1, \"img\", 39);\n    i0.ɵɵelementStart(2, \"div\", 53);\n    i0.ɵɵtext(3, \"No Certifications to show\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class CertificateComponent {\n  constructor(certificaterService, authService, datePipe) {\n    this.certificaterService = certificaterService;\n    this.authService = authService;\n    this.datePipe = datePipe;\n    this.classroomService = inject(ClassroomService);\n    this.userCertificates = [];\n    this.certificates = [];\n    this.completionCertificates = [];\n    this.attendanceCertificates = [];\n    this.languages = [];\n    this.shareButtons = [];\n    this.visible = false;\n    this.base64data = \"\";\n    this.pdfSrc = \"\";\n    this.shareUrl = \"https://www.mylingotrip.com/certificates?id=\";\n    this.isDownloadLoading = [];\n    this.user = {};\n    this.allCertificates = signal([]);\n    this.subs = new SubSink();\n  }\n  ngOnInit() {\n    this.responsiveOptions = [{\n      breakpoint: '1220px',\n      numVisible: 4,\n      numScroll: 2\n    }, {\n      breakpoint: '1100px',\n      numVisible: 1,\n      numScroll: 1\n    }];\n    this.getCertifications();\n    this.user = this.authService.getLoggedInUser();\n    this.findClassrooms();\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  findClassrooms() {\n    this.classroomService.getLMSUserClassrooms(this.user.id).subscribe(res => {\n      if (!res) return;\n      console.log(this.generateCertificatesForClassrooms(res));\n      this.allCertificates.set(this.generateCertificatesForClassrooms(res));\n    });\n  }\n  generateCertificatesForClassrooms(classrooms) {\n    const certificates = [];\n    classrooms.forEach(classroom => {\n      const completedPackages = classroom.packages.filter(pkg => pkg.state === 'Completed');\n      const ongoingPackages = classroom.packages.filter(pkg => pkg.state === 'Ongoing');\n      const totalHours = this.calculateTotalHours([...completedPackages, ...ongoingPackages]);\n      // Generate Completion Certificate\n      if (completedPackages.length === 1) {\n        // Only one completed package, show one completion certificate\n        certificates.push({\n          level: classroom.activeLevel,\n          language: classroom.language,\n          hours: completedPackages[0].totalHours,\n          // Use the hours from the completed package\n          type: 'Completion'\n        });\n      } else if (completedPackages.length > 1) {\n        // More than one completed package, show one completion certificate\n        certificates.push({\n          level: classroom.activeLevel,\n          language: classroom.language,\n          hours: totalHours,\n          // Use total hours from all completed packages\n          type: 'Completion'\n        });\n      }\n      // Generate Attendance Certificate\n      if (completedPackages.length === 0 || ongoingPackages.length > 0) {\n        certificates.push({\n          level: classroom.activeLevel,\n          language: classroom.language,\n          hours: totalHours,\n          type: 'Attendance'\n        });\n      }\n    });\n    return certificates;\n  }\n  calculateTotalHours(packages) {\n    let totalHours = 0;\n    packages.forEach(pkg => {\n      if (pkg.state === 'Completed') {\n        totalHours += pkg.totalHours; // Add completed package hours\n      } else if (pkg.state === 'Ongoing') {\n        totalHours += pkg.hoursLeft; // Add ongoing package hours\n      }\n    });\n    return totalHours;\n  }\n  viewPdf(level, language, type, index, hours) {\n    this.isDownloadLoading[index] = true;\n    let hoursInput = hours?.toString(); // Example input as a string\n    let hoursF = parseFloat(hoursInput); // Parse the string to a number\n    let formattedHours = hoursF.toFixed(0); // Format to 2 decimal places\n    this.subs.sink = this.certificaterService.generateCertificationPdf(level, language, type, formattedHours).subscribe(pdfResponse => {\n      this.isDownloadLoading[index] = false;\n      const byteCharacters = atob(pdfResponse);\n      const byteNumbers = new Array(byteCharacters.length);\n      for (let i = 0; i < byteCharacters.length; i++) {\n        byteNumbers[i] = byteCharacters.charCodeAt(i);\n      }\n      const byteArray = new Uint8Array(byteNumbers);\n      const blob = new Blob([byteArray], {\n        type: 'application/pdf'\n      });\n      var url = window.URL.createObjectURL(blob);\n      let anchor = document.createElement(\"a\");\n      anchor.href = url;\n      anchor.target = \"_blank\";\n      anchor.click();\n    }, error => {\n      console.error('Error generating PDF:', error);\n    });\n  }\n  ngAfterViewInit() {\n    this.setupShareButton();\n  }\n  setupShareButton() {\n    this.shareButtons = Array.from(document.querySelectorAll(\"button.shareButton\"));\n  }\n  toggleShareButtons() {\n    for (let i = 0; i < this.shareButtons.length; i++) {\n      this.shareButtons[i].classList.toggle(\"open\");\n    }\n  }\n  showDialog() {\n    this.visible = true;\n  }\n  selectLanguage(lang) {\n    this.certificates = this.userCertificates;\n    this.attendanceCertificates = this.certificates.filter(certificate => certificate.type === \"attendance\");\n    this.completionCertificates = this.certificates.filter(certificate => certificate.type === \"completion\");\n    if (lang !== \"all\") {\n      this.certificates = this.certificates.filter(certificate => certificate.language === lang);\n      this.attendanceCertificates = this.attendanceCertificates.filter(certificate => certificate.language === lang);\n      this.completionCertificates = this.completionCertificates.filter(certificate => certificate.language === lang);\n    }\n  }\n  getCertifications() {\n    this.subs.sink = this.certificaterService.getListOfCertifications().subscribe(response => {\n      this.userCertificates = response;\n      this.userCertificates.forEach(certificate => {\n        certificate.urlParam = btoa(`type=${certificate.type}&name=${this.user.firstName}_${this.user.lastName}&language=${certificate.language}&level=${certificate.level}&hours=${certificate.hours}&date=${this.convertDate(certificate.datetime)}`);\n      });\n      this.languages = this.userCertificates.map(certificate => certificate.language) // Extract the language property from each certificate\n      .filter((language, index, array) => array.indexOf(language) === index); // Filter out duplicate languages\n      this.certificates = this.userCertificates;\n      this.completionCertificates = this.userCertificates.filter(certificate => certificate.type === \"completion\");\n      this.attendanceCertificates = this.userCertificates.filter(certificate => certificate.type === \"attendance\");\n    });\n  }\n  b64toBlob(b64Data, contentType) {\n    contentType = contentType || '';\n    let sliceSize = 512;\n    var byteCharacters = atob(b64Data);\n    var byteArrays = [];\n    for (var offset = 0; offset < byteCharacters.length; offset += sliceSize) {\n      var slice = byteCharacters.slice(offset, offset + sliceSize);\n      var byteNumbers = new Array(slice.length);\n      for (var i = 0; i < slice.length; i++) {\n        byteNumbers[i] = slice.charCodeAt(i);\n      }\n      var byteArray = new Uint8Array(byteNumbers);\n      byteArrays.push(byteArray);\n    }\n    var blob = new Blob(byteArrays, {\n      type: contentType\n    });\n    return blob;\n  }\n  getImage() {\n    var blob = this.b64toBlob(this.base64data, \"application/pdf\");\n    let a = document.createElement(\"a\");\n    document.body.appendChild(a);\n    var url = window.URL.createObjectURL(blob);\n    a.href = url;\n    a.download = String(\"MyLingoTrip_Certificate.pdf\");\n    a.click();\n    window.URL.revokeObjectURL(url);\n    a.remove();\n  }\n  convertDate(dateString) {\n    const date = new Date(dateString);\n    let formattedDate = this.datePipe.transform(date, 'dd MMMM yyyy');\n    return formattedDate;\n  }\n  static #_ = this.ɵfac = function CertificateComponent_Factory(t) {\n    return new (t || CertificateComponent)(i0.ɵɵdirectiveInject(i1.CertificateService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.DatePipe));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: CertificateComponent,\n    selectors: [[\"app-certificate\"]],\n    decls: 36,\n    vars: 21,\n    consts: [[\"noLatest\", \"\"], [\"noCertificates\", \"\"], [\"noCompletionCertificates\", \"\"], [\"noAttendanceCertificates\", \"\"], [\"downloadButton\", \"\"], [1, \"certificates\"], [1, \"text-0\", \"font-2xl\", \"font-bold\"], [1, \"my-5\"], [1, \"flex\"], [1, \"col-fixed\"], [\"pbutton\", \"\", \"type\", \"button\", 1, \"p-element\", \"p-button-outlined\", \"w-7rem\", \"p-button-rounded\", \"mb-2\", \"button-outlined\", \"p-button-sm\", \"p-button\", \"p-component\", \"py-1\", \"pl-2\"], [1, \"flex\", \"align-items-center\", \"gap-1\", 3, \"click\"], [\"height\", \"18\", \"src\", \"/assets/icons/flag.svg\", 1, \"ng-star-inserted\"], [1, \"font-sm\", \"mx-4\"], [1, \"w-100\", 3, \"value\", \"numVisible\", \"numScroll\", \"circular\", \"responsiveOptions\"], [\"pTemplate\", \"item\"], [1, \"card\", \"block-gradient\", \"mt-3\"], [1, \"text-primary\", \"font-lg\", \"font-semibold\", \"pl-4\"], [3, \"value\", \"numVisible\", \"numScroll\", \"circular\", 4, \"ngIf\", \"ngIfElse\"], [1, \"card\", \"block-gradient\", \"relative\"], [\"header\", \"All\"], [3, \"value\", 4, \"ngIf\", \"ngIfElse\"], [\"header\", \"of Completion\"], [\"header\", \"of Attendance\"], [\"header\", \"Header\", 3, \"visibleChange\", \"visible\", \"breakpoints\", \"draggable\", \"resizable\"], [\"pbutton\", \"\", \"type\", \"button\", 1, \"p-element\", \"p-button-outlined\", \"w-7rem\", \"p-button-rounded\", \"mt-2\", \"button-outlined\", \"p-button-sm\", \"p-button\", \"p-component\", \"py-1\", \"pl-1\"], [\"height\", \"20\", 1, \"ng-star-inserted\", 3, \"src\"], [1, \"font-sm\", \"mx-3\"], [3, \"value\", \"numVisible\", \"numScroll\", \"circular\"], [1, \"m-2\", \"px-3\", \"flex\", \"align-items-center\"], [1, \"relative\", \"w-6\", \"mb-3\"], [1, \"w-full\", 3, \"src\"], [\"src\", \"/assets/images/dashboard/new.svg\", \"alt\", \"\", 1, \"new-tag\"], [1, \"text-primary\", \"font-bold\", \"font-2xl\"], [1, \"mt-4\", \"pill-bg\", \"pt-1\", 2, \"opacity\", \"0\"], [1, \"mr-2\", \"shareButton\", 3, \"click\"], [\"src\", \"/assets/icons/view-file.svg\"], [3, \"topic\", \"componentId\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"py-5\"], [\"src\", \"/assets/icons/certificate-gradient.svg\", 1, \"\"], [1, \"text-primary\", \"font-bold\", \"font-3xl\", \"ml-3\"], [3, \"value\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [1, \"font-sm\", \"text-blue\"], [1, \"font-sm\"], [1, \"flex\", \"py-2\"], [\"src\", \"/assets/icons/certificate-gradient.svg\", 1, \"icon-cer\"], [2, \"vertical-align\", \"super\"], [1, \"py-2\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"hidden\", 3, \"topic\", \"link\", \"componentId\"], [\"src\", \"/assets/images/load.gif\", \"width\", \"30\"], [1, \"text-primary\", \"font-bold\", \"font-base\", \"ml-3\"], [1, \"content\"], [1, \"shareButton\", \"main\", 3, \"click\"], [\"src\", \"/assets/icons/share-certificate.svg\"], [\"viewBox\", \"0 0 24 24\", 1, \"close\", 2, \"width\", \"24px\", \"height\", \"24px\"], [\"d\", \"M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z\"], [1, \"shareButton\", \"fb\", 3, \"click\"], [\"src\", \"/assets/icons/facebook-share.svg\", 1, \"w-100\"], [1, \"shareButton\", \"tw\", \"ml-1\", 3, \"click\"], [\"src\", \"/assets/icons/twitter.svg\", 1, \"w-100\"], [1, \"shareButton\", \"ig\", \"ml-1\", 3, \"click\"], [\"src\", \"/assets/icons/linkedin-share.svg\", 1, \"w-100\"]],\n    template: function CertificateComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6);\n        i0.ɵɵtext(2, \"Certificates\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(3, \"div\", 7);\n        i0.ɵɵelementStart(4, \"div\", 8)(5, \"div\", 9)(6, \"button\", 10)(7, \"div\", 11);\n        i0.ɵɵlistener(\"click\", function CertificateComponent_Template_div_click_7_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.selectLanguage(\"all\"));\n        });\n        i0.ɵɵelement(8, \"img\", 12);\n        i0.ɵɵelementStart(9, \"span\", 13);\n        i0.ɵɵtext(10, \"All\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(11, \"p-carousel\", 14);\n        i0.ɵɵtemplate(12, CertificateComponent_ng_template_12_Template, 5, 3, \"ng-template\", 15);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(13, \"div\", 16)(14, \"div\", 17);\n        i0.ɵɵtext(15, \"Latest\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(16, CertificateComponent_p_carousel_16_Template, 2, 4, \"p-carousel\", 18)(17, CertificateComponent_ng_template_17_Template, 6, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"div\", 19)(20, \"p-tabView\")(21, \"p-tabPanel\", 20);\n        i0.ɵɵtemplate(22, CertificateComponent_p_table_22_Template, 3, 1, \"p-table\", 21)(23, CertificateComponent_ng_template_23_Template, 4, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"p-tabPanel\", 22);\n        i0.ɵɵtemplate(26, CertificateComponent_p_table_26_Template, 3, 1, \"p-table\", 21)(27, CertificateComponent_ng_template_27_Template, 4, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(29, \"p-tabPanel\", 23);\n        i0.ɵɵtemplate(30, CertificateComponent_p_table_30_Template, 3, 1, \"p-table\", 21)(31, CertificateComponent_ng_template_31_Template, 4, 0, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(33, \"p-dialog\", 24);\n        i0.ɵɵtwoWayListener(\"visibleChange\", function CertificateComponent_Template_p_dialog_visibleChange_33_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          i0.ɵɵtwoWayBindingSet(ctx.visible, $event) || (ctx.visible = $event);\n          return i0.ɵɵresetView($event);\n        });\n        i0.ɵɵelementStart(34, \"p\");\n        i0.ɵɵtext(35, \" Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. \");\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        const noLatest_r14 = i0.ɵɵreference(18);\n        const noCertificates_r15 = i0.ɵɵreference(24);\n        const noCompletionCertificates_r16 = i0.ɵɵreference(28);\n        const noAttendanceCertificates_r17 = i0.ɵɵreference(32);\n        i0.ɵɵadvance(11);\n        i0.ɵɵproperty(\"value\", ctx.languages)(\"numVisible\", 5)(\"numScroll\", 5)(\"circular\", false)(\"responsiveOptions\", ctx.responsiveOptions);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.certificates && ctx.certificates.length > 0)(\"ngIfElse\", noLatest_r14);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", ctx.allCertificates().length > 0)(\"ngIfElse\", noCertificates_r15);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.completionCertificates.length > 0)(\"ngIfElse\", noCompletionCertificates_r16);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.attendanceCertificates.length > 0)(\"ngIfElse\", noAttendanceCertificates_r17);\n        i0.ɵɵadvance(3);\n        i0.ɵɵstyleMap(i0.ɵɵpureFunction0(19, _c0));\n        i0.ɵɵtwoWayProperty(\"visible\", ctx.visible);\n        i0.ɵɵproperty(\"breakpoints\", i0.ɵɵpureFunction0(20, _c1))(\"draggable\", false)(\"resizable\", false);\n      }\n    },\n    dependencies: [i3.NgIf, i4.TabView, i4.TabPanel, i5.PrimeTemplate, i6.Table, i7.Carousel, i8.Dialog, i9.ShareButtonComponent, i3.TitleCasePipe],\n    styles: [\".certificates[_ngcontent-%COMP%] {\\n  background-color: white;\\n  background-image: url('certificate-bg.svg');\\n  background-repeat: no-repeat;\\n  border-radius: 12px;\\n  padding: 40px;\\n}\\n.certificates[_ngcontent-%COMP%]   .certificate[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  padding: 20px 0;\\n  margin-top: 40px;\\n  font-size: 18px;\\n}\\n.certificates[_ngcontent-%COMP%]   .certificate[_ngcontent-%COMP%]   .language[_ngcontent-%COMP%] {\\n  font-size: 22px;\\n}\\n.certificates[_ngcontent-%COMP%]   .certificate[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 30px 0;\\n  font-weight: bold;\\n}\\n.certificates[_ngcontent-%COMP%]   .certificate[_ngcontent-%COMP%]   .levels[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  justify-content: space-between;\\n}\\n.certificates[_ngcontent-%COMP%]   .certificate[_ngcontent-%COMP%]   .levels[_ngcontent-%COMP%]   .level[_ngcontent-%COMP%] {\\n  box-sizing: border-box;\\n  width: 47%;\\n  display: flex;\\n  justify-content: space-evenly;\\n  padding-bottom: 20px;\\n}\\n.certificates[_ngcontent-%COMP%]   .certificate[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n\\n.style-three[_ngcontent-%COMP%] {\\n  border: 0;\\n  border-bottom: 3px dashed #ccc;\\n  margin: 20px 0;\\n}\\n\\n.icon-cer[_ngcontent-%COMP%] {\\n  width: 1.3rem;\\n  margin-right: 0.5rem;\\n}\\n\\n.text-blue[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  color: #3345A7;\\n}\\n\\n[_nghost-%COMP%]     .btn-modify {\\n  padding: 0.25rem 1rem;\\n  border-radius: 5px;\\n  background: linear-gradient(#8497ff 0%, #122171 100%);\\n  color: #fff;\\n}\\n[_nghost-%COMP%]     .card {\\n  border: 0;\\n}\\n[_nghost-%COMP%]     .p-tabview .p-tabview-nav {\\n  background: transparent;\\n}\\n[_nghost-%COMP%]     .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {\\n  background: transparent;\\n  border-color: rgba(0, 0, 0, 0.12);\\n  color: #3F51B5;\\n}\\n[_nghost-%COMP%]     .p-tabview-panels {\\n  background: transparent;\\n}\\n[_nghost-%COMP%]     .p-datatable .p-datatable-tbody > tr {\\n  background-color: transparent;\\n}\\n[_nghost-%COMP%]     .p-datatable .p-datatable-tbody > tr > td {\\n  line-height: 26px;\\n}\\n[_nghost-%COMP%]     .p-datatable .p-datatable-thead > tr > th {\\n  background-color: transparent;\\n}\\n[_nghost-%COMP%]     .p-tabview .p-tabview-nav li .p-tabview-nav-link {\\n  background: transparent;\\n}\\n[_nghost-%COMP%]     .p-tabview .p-tabview-nav li .p-tabview-nav-link {\\n  color: #3F51B5;\\n}\\n[_nghost-%COMP%]     .p-tabview .p-tabview-nav {\\n  border: 0;\\n}\\n[_nghost-%COMP%]     .p-tabview .p-tabview-nav .p-tabview-ink-bar {\\n  background: transparent;\\n}\\n[_nghost-%COMP%]     .p-tabview .p-tabview-nav li .p-tabview-nav-link {\\n  border: 0;\\n}\\n[_nghost-%COMP%]     .p-carousel-indicators {\\n  display: none !important;\\n}\\n[_nghost-%COMP%]     .pi-chevron-right:before, [_nghost-%COMP%]     .pi-chevron-left:before {\\n  color: #84ABFF !important;\\n}\\n[_nghost-%COMP%]     .p-carousel .p-carousel-content .p-carousel-prev, [_nghost-%COMP%]     .p-carousel .p-carousel-content .p-carousel-next {\\n  margin-top: 0.2rem;\\n}\\n[_nghost-%COMP%]     .p-highlight {\\n  font-family: \\\"Proxima Nova Bold\\\" !important;\\n}\\n[_nghost-%COMP%]     ul.p-tabview-nav li:nth-of-type(2) .p-tabview-nav-link {\\n  border-right: 1px solid #3F51B5;\\n  border-left: 1px solid #3F51B5;\\n}\\n[_nghost-%COMP%]     .p-tabview .p-tabview-nav li .p-tabview-nav-link {\\n  padding: 0.5rem 1.5rem;\\n}\\n[_nghost-%COMP%]     .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {\\n  border-color: #3F51B5;\\n}\\n\\n.content[_ngcontent-%COMP%] {\\n  display: contents;\\n}\\n\\n.challenge[_ngcontent-%COMP%] {\\n  width: 541px;\\n  height: 22px;\\n  position: absolute;\\n  transform: rotate(-90deg);\\n  top: 569.5px;\\n  left: 755px;\\n  transform-origin: 0 0;\\n}\\n\\n.challenge[_ngcontent-%COMP%]   .number[_ngcontent-%COMP%] {\\n  float: right;\\n}\\n\\n.challenge[_ngcontent-%COMP%]   .daily[_ngcontent-%COMP%] {\\n  float: left;\\n}\\n\\n.content[_ngcontent-%COMP%]   .shareButton.main[_ngcontent-%COMP%]   .share[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   .shareButton.main[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   .shareButton.main[_ngcontent-%COMP%]   .check[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  transition: all 150ms;\\n}\\n\\n.content[_ngcontent-%COMP%]   .shareButton.main[_ngcontent-%COMP%]   .share[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   .shareButton.main.open[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   .shareButton.main.sent[_ngcontent-%COMP%]   .check[_ngcontent-%COMP%] {\\n  transform: rotate(0) scale(1);\\n  opacity: 1;\\n}\\n\\n.content[_ngcontent-%COMP%]   .shareButton.main[_ngcontent-%COMP%]   .close[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   .shareButton.main.open[_ngcontent-%COMP%]   .share[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   .shareButton.main[_ngcontent-%COMP%]   .check[_ngcontent-%COMP%], .content[_ngcontent-%COMP%]   .shareButton.main.sent[_ngcontent-%COMP%]   .share[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  transform: rotate(90deg) scale(0);\\n}\\n\\n.shareButton[_ngcontent-%COMP%], .shareButton.open[_ngcontent-%COMP%] {\\n  border: none;\\n  border-radius: 50%;\\n  background: transparent;\\n  overflow: hidden;\\n  outline: none;\\n  width: 24px;\\n  height: 24px;\\n  box-sizing: content-box;\\n  transition: all 200ms;\\n  position: relative;\\n  opacity: 1;\\n  transform: scale(1);\\n  box-shadow: 0 0 0 rgba(0, 0, 0, 0);\\n  cursor: pointer;\\n}\\n\\n.shareButton[_ngcontent-%COMP%]:hover, .shareButton.open[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1) translateY(-3px);\\n}\\n\\n.shareButton[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%], .shareButton.open[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  display: block;\\n  fill: #3F51B5;\\n  width: 24px;\\n  height: 24px;\\n  opacity: 1;\\n  transition: all 150ms;\\n  transform: scale(1);\\n}\\n\\n.fb[_ngcontent-%COMP%], .shareButton.open.ig[_ngcontent-%COMP%] {\\n  transition-delay: 100ms;\\n}\\n\\n.tw[_ngcontent-%COMP%], .shareButton.open.tw[_ngcontent-%COMP%] {\\n  transition-delay: 50ms;\\n}\\n\\n.ig[_ngcontent-%COMP%], .shareButton.open.fb[_ngcontent-%COMP%] {\\n  transition-delay: 0ms;\\n}\\n\\n.fb[_ngcontent-%COMP%], .tw[_ngcontent-%COMP%], .ig[_ngcontent-%COMP%] {\\n  width: 0;\\n  height: 0;\\n  overflow: hidden;\\n  padding: 0;\\n  margin: 0;\\n  opacity: 0;\\n  transform: scale(0);\\n}\\n\\n.fb[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%], .tw[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%], .ig[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 0;\\n  height: 0;\\n  opacity: 0;\\n  transform: scale(0);\\n}\\n\\n.shareButton.main.open[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  transform: rotate(90deg) scale(0);\\n}\\n\\n.btn-modify[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  right: 1rem;\\n}\\n\\n.text-primary[_ngcontent-%COMP%] {\\n  color: #3F51B5;\\n}\\n\\n.pill-bg[_ngcontent-%COMP%] {\\n  border-radius: 20px;\\n  background: #f4f7ff;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n\\n.new-tag[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 1.5rem;\\n  top: 1rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["inject", "signal", "SubSink", "ClassroomService", "i0", "ɵɵelementStart", "ɵɵlistener", "CertificateComponent_ng_template_12_Template_div_click_1_listener", "language_r3", "ɵɵrestoreView", "_r2", "$implicit", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "selectLanguage", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵpropertyInterpolate1", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "CertificateComponent_p_carousel_16_ng_template_1_Template_button_click_11_listener", "certificate_r6", "_r5", "viewPdf", "level", "language", "type", "ɵɵproperty", "ɵɵtextInterpolate2", "ɵɵtextInterpolate1", "ɵɵpipeBind1", "id", "ɵɵtemplate", "CertificateComponent_p_carousel_16_ng_template_1_Template", "certificates", "slice", "ɵɵelementContainerStart", "CertificateComponent_p_table_22_ng_template_2_Template_button_click_14_listener", "certificate_r8", "_r7", "i", "hours", "CertificateComponent_p_table_22_ng_template_2_ng_container_15_Template", "CertificateComponent_p_table_22_ng_template_2_ng_template_16_Template", "ɵɵtemplateRefExtractor", "isDownloadLoading", "downloadButton_r9", "shareUrl", "urlParam", "CertificateComponent_p_table_22_ng_template_1_Template", "CertificateComponent_p_table_22_ng_template_2_Template", "allCertificates", "CertificateComponent_p_table_26_ng_template_2_Template_button_click_13_listener", "certificate_r11", "_r10", "view", "CertificateComponent_p_table_26_ng_template_2_Template_button_click_16_listener", "toggleShareButtons", "CertificateComponent_p_table_26_ng_template_2_Template_button_click_20_listener", "CertificateComponent_p_table_26_ng_template_2_Template_button_click_22_listener", "CertificateComponent_p_table_26_ng_template_2_Template_button_click_24_listener", "CertificateComponent_p_table_26_ng_template_1_Template", "CertificateComponent_p_table_26_ng_template_2_Template", "completionCertificates", "CertificateComponent_p_table_30_ng_template_2_Template_button_click_13_listener", "certificate_r13", "_r12", "CertificateComponent_p_table_30_ng_template_2_Template_button_click_16_listener", "CertificateComponent_p_table_30_ng_template_2_Template_button_click_20_listener", "CertificateComponent_p_table_30_ng_template_2_Template_button_click_22_listener", "CertificateComponent_p_table_30_ng_template_2_Template_button_click_24_listener", "CertificateComponent_p_table_30_ng_template_1_Template", "CertificateComponent_p_table_30_ng_template_2_Template", "attendanceCertificates", "CertificateComponent", "constructor", "certificaterService", "authService", "datePipe", "classroomService", "userCertificates", "languages", "shareButtons", "visible", "base64data", "pdfSrc", "user", "subs", "ngOnInit", "responsiveOptions", "breakpoint", "numVisible", "numScroll", "getCertifications", "getLoggedInUser", "findClassrooms", "ngOnDestroy", "unsubscribe", "getLMSUserClassrooms", "subscribe", "res", "console", "log", "generateCertificatesForClassrooms", "set", "classrooms", "for<PERSON>ach", "classroom", "completedPackages", "packages", "filter", "pkg", "state", "ongoingPackages", "totalHours", "calculateTotalHours", "length", "push", "activeLevel", "hoursLeft", "index", "hoursInput", "toString", "hoursF", "parseFloat", "formattedHours", "toFixed", "sink", "generateCertificationPdf", "pdfResponse", "byteCharacters", "atob", "byteNumbers", "Array", "charCodeAt", "byteArray", "Uint8Array", "blob", "Blob", "url", "window", "URL", "createObjectURL", "anchor", "document", "createElement", "href", "target", "click", "error", "ngAfterViewInit", "setupShareButton", "from", "querySelectorAll", "classList", "toggle", "showDialog", "lang", "certificate", "getListOfCertifications", "response", "btoa", "firstName", "lastName", "convertDate", "datetime", "map", "array", "indexOf", "b64toBlob", "b64Data", "contentType", "sliceSize", "byteArrays", "offset", "getImage", "a", "body", "append<PERSON><PERSON><PERSON>", "download", "String", "revokeObjectURL", "remove", "dateString", "date", "Date", "formattedDate", "transform", "_", "ɵɵdirectiveInject", "i1", "CertificateService", "i2", "AuthService", "i3", "DatePipe", "_2", "selectors", "decls", "vars", "consts", "template", "CertificateComponent_Template", "rf", "ctx", "CertificateComponent_Template_div_click_7_listener", "_r1", "CertificateComponent_ng_template_12_Template", "CertificateComponent_p_carousel_16_Template", "CertificateComponent_ng_template_17_Template", "CertificateComponent_p_table_22_Template", "CertificateComponent_ng_template_23_Template", "CertificateComponent_p_table_26_Template", "CertificateComponent_ng_template_27_Template", "CertificateComponent_p_table_30_Template", "CertificateComponent_ng_template_31_Template", "ɵɵtwoWayListener", "CertificateComponent_Template_p_dialog_visibleChange_33_listener", "$event", "ɵɵtwoWayBindingSet", "noLatest_r14", "noCertificates_r15", "noCompletionCertificates_r16", "noAttendanceCertificates_r17", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "ɵɵtwoWayProperty", "_c1"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\certificate\\certificate.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\certificate\\certificate.component.html"], "sourcesContent": ["import { Component, inject, OnInit, signal } from '@angular/core';\r\nimport { co } from '@fullcalendar/core/internal-common';\r\nimport { Certificate } from 'src/app/core/models/certificate.model';\r\nimport { User } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { CertificateService } from 'src/app/core/services/certificate.service';\r\nimport { SubSink } from 'subsink';\r\nimport { DatePipe } from '@angular/common';\r\nimport { tap, combineLatest } from 'rxjs';\r\nimport { Classroom } from 'src/app/core/models/classroom.model';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\n\r\n@Component({\r\n  selector: 'app-certificate',\r\n  templateUrl: './certificate.component.html',\r\n  styleUrls: ['./certificate.component.scss']\r\n})\r\nexport class CertificateComponent implements OnInit {\r\n  classroomService = inject(ClassroomService);\r\n  userCertificates: Certificate[] = [];\r\n  certificates: Certificate[] = [];\r\n  completionCertificates: Certificate[] = [];\r\n  attendanceCertificates: Certificate[] = [];\r\n  languages: string[] = [];\r\n  shareButtons: HTMLButtonElement[] = [];\r\n  visible: boolean = false;\r\n  responsiveOptions?: any[];\r\n  base64data: string = \"\";\r\n  pdfSrc = \"\";\r\n  shareUrl = \"https://www.mylingotrip.com/certificates?id=\";\r\n  isDownloadLoading: boolean[] = [];\r\n  user: any = {} as User;\r\n  allCertificates = signal([] as any[]);\r\n\r\n  private subs = new SubSink();\r\n  constructor(\r\n    private certificaterService: CertificateService,\r\n    private authService: AuthService,\r\n    private datePipe: DatePipe\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.responsiveOptions = [\r\n      {\r\n        breakpoint: '1220px',\r\n        numVisible: 4,\r\n        numScroll: 2\r\n      },\r\n      {\r\n        breakpoint: '1100px',\r\n        numVisible: 1,\r\n        numScroll: 1\r\n      }\r\n    ];\r\n    this.getCertifications();\r\n    this.user = this.authService.getLoggedInUser();\r\n    this.findClassrooms();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  findClassrooms() {\r\n    this.classroomService.getLMSUserClassrooms(this.user.id).subscribe(res => {\r\n      if (!res)\r\n        return;\r\n      console.log(this.generateCertificatesForClassrooms(res));\r\n      this.allCertificates.set(this.generateCertificatesForClassrooms(res));\r\n    });\r\n  }\r\n\r\n  generateCertificatesForClassrooms(classrooms: any[]): any[] {\r\n    const certificates: any[] = [];\r\n\r\n    classrooms.forEach(classroom => {\r\n      const completedPackages = classroom.packages.filter((pkg: any) => pkg.state === 'Completed');\r\n      const ongoingPackages = classroom.packages.filter((pkg: any) => pkg.state === 'Ongoing');\r\n\r\n      const totalHours = this.calculateTotalHours([...completedPackages, ...ongoingPackages]);\r\n\r\n      // Generate Completion Certificate\r\n      if (completedPackages.length === 1) {\r\n        // Only one completed package, show one completion certificate\r\n        certificates.push({\r\n          level: classroom.activeLevel,\r\n          language: classroom.language,\r\n          hours: completedPackages[0].totalHours, // Use the hours from the completed package\r\n          type: 'Completion'\r\n        });\r\n      } else if (completedPackages.length > 1) {\r\n        // More than one completed package, show one completion certificate\r\n        certificates.push({\r\n          level: classroom.activeLevel,\r\n          language: classroom.language,\r\n          hours: totalHours, // Use total hours from all completed packages\r\n          type: 'Completion'\r\n        });\r\n      }\r\n\r\n      // Generate Attendance Certificate\r\n      if (completedPackages.length === 0 || ongoingPackages.length > 0) {\r\n        certificates.push({\r\n          level: classroom.activeLevel,\r\n          language: classroom.language,\r\n          hours: totalHours,\r\n          type: 'Attendance'\r\n        });\r\n      }\r\n    });\r\n\r\n    return certificates;\r\n  }\r\n\r\n\r\n\r\n  calculateTotalHours(packages: any[]): number {\r\n    let totalHours = 0;\r\n\r\n    packages.forEach(pkg => {\r\n      if (pkg.state === 'Completed') {\r\n        totalHours += pkg.totalHours; // Add completed package hours\r\n      } else if (pkg.state === 'Ongoing') {\r\n        totalHours += pkg.hoursLeft; // Add ongoing package hours\r\n      }\r\n    });\r\n\r\n    return totalHours;\r\n  }\r\n\r\n  viewPdf(level: any, language: any, type: any, index: number, hours?: number) {\r\n    this.isDownloadLoading[index] = true;\r\n    let hoursInput = hours?.toString() as string; // Example input as a string\r\n    let hoursF: number = parseFloat(hoursInput); // Parse the string to a number\r\n    let formattedHours: string = hoursF.toFixed(0); // Format to 2 decimal places\r\n    this.subs.sink = this.certificaterService.generateCertificationPdf(level, language, type, formattedHours).subscribe((pdfResponse: any) => {\r\n      this.isDownloadLoading[index] = false;\r\n      const byteCharacters = atob(pdfResponse);\r\n      const byteNumbers = new Array(byteCharacters.length);\r\n      for (let i = 0; i < byteCharacters.length; i++) {\r\n        byteNumbers[i] = byteCharacters.charCodeAt(i);\r\n      }\r\n      const byteArray = new Uint8Array(byteNumbers);\r\n      const blob = new Blob([byteArray], { type: 'application/pdf' });\r\n      var url = window.URL.createObjectURL(blob);\r\n      let anchor = document.createElement(\"a\");\r\n      anchor.href = url;\r\n      anchor.target = \"_blank\"\r\n      anchor.click();\r\n    },\r\n      error => {\r\n        console.error('Error generating PDF:', error);\r\n      });\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.setupShareButton();\r\n  }\r\n\r\n  setupShareButton(): void {\r\n    this.shareButtons = Array.from(document.querySelectorAll(\"button.shareButton\")) as HTMLButtonElement[];\r\n  }\r\n\r\n  toggleShareButtons(): void {\r\n    for (let i = 0; i < this.shareButtons.length; i++) {\r\n      this.shareButtons[i].classList.toggle(\"open\");\r\n    }\r\n  }\r\n  showDialog() {\r\n    this.visible = true;\r\n  }\r\n  selectLanguage(lang: string) {\r\n    this.certificates = this.userCertificates;\r\n    this.attendanceCertificates = this.certificates.filter((certificate: { type: string; }) => certificate.type === \"attendance\");\r\n    this.completionCertificates = this.certificates.filter((certificate: { type: string; }) => certificate.type === \"completion\");\r\n    if (lang !== \"all\") {\r\n      this.certificates = this.certificates.filter((certificate: { language: string; }) => certificate.language === lang);\r\n      this.attendanceCertificates = this.attendanceCertificates.filter((certificate: { language: string; }) => certificate.language === lang);\r\n      this.completionCertificates = this.completionCertificates.filter((certificate: { language: string; }) => certificate.language === lang);\r\n    }\r\n  }\r\n\r\n  getCertifications() {\r\n    this.subs.sink = (this.certificaterService.getListOfCertifications().subscribe((response) => {\r\n      this.userCertificates = response;\r\n      this.userCertificates.forEach((certificate) => {\r\n        certificate.urlParam = btoa(`type=${certificate.type}&name=${this.user.firstName}_${this.user.lastName}&language=${certificate.language}&level=${certificate.level}&hours=${certificate.hours}&date=${this.convertDate(certificate.datetime)}`);\r\n      });\r\n      this.languages = this.userCertificates.map((certificate) => certificate.language) // Extract the language property from each certificate\r\n        .filter((language, index, array) => array.indexOf(language) === index); // Filter out duplicate languages\r\n      this.certificates = this.userCertificates;\r\n      this.completionCertificates = this.userCertificates.filter((certificate) => certificate.type === \"completion\");\r\n      this.attendanceCertificates = this.userCertificates.filter((certificate) => certificate.type === \"attendance\");\r\n    }));\r\n  }\r\n\r\n  public b64toBlob(b64Data: string, contentType: string) {\r\n    contentType = contentType || '';\r\n    let sliceSize = 512;\r\n\r\n    var byteCharacters = atob(b64Data);\r\n    var byteArrays = [];\r\n\r\n    for (var offset = 0; offset < byteCharacters.length; offset += sliceSize) {\r\n      var slice = byteCharacters.slice(offset, offset + sliceSize);\r\n\r\n      var byteNumbers = new Array(slice.length);\r\n      for (var i = 0; i < slice.length; i++) {\r\n        byteNumbers[i] = slice.charCodeAt(i);\r\n      }\r\n\r\n      var byteArray = new Uint8Array(byteNumbers);\r\n\r\n      byteArrays.push(byteArray);\r\n    }\r\n\r\n    var blob = new Blob(byteArrays, { type: contentType });\r\n    return blob;\r\n  }\r\n\r\n  getImage() {\r\n    var blob = this.b64toBlob(this.base64data, \"application/pdf\");\r\n    let a = document.createElement(\"a\");\r\n    document.body.appendChild(a);\r\n    var url = window.URL.createObjectURL(blob);\r\n    a.href = url;\r\n    a.download = String(\"MyLingoTrip_Certificate.pdf\");\r\n    a.click();\r\n    window.URL.revokeObjectURL(url);\r\n    a.remove();\r\n  }\r\n\r\n  convertDate(dateString: string) {\r\n    const date = new Date(dateString);\r\n    let formattedDate = this.datePipe.transform(date, 'dd MMMM yyyy');\r\n    return formattedDate;\r\n  }\r\n}", "<div class=\"certificates\">\r\n    <div class=\"text-0 font-2xl font-bold\">Certificates</div>\r\n    <div class=\"my-5\"></div>\r\n    <div class=\"flex\">\r\n        <div class=\"col-fixed\">\r\n            <button pbutton=\"\" type=\"button\" class=\"p-element p-button-outlined w-7rem p-button-rounded mb-2 button-outlined p-button-sm p-button p-component py-1 pl-2\"><div class=\"flex align-items-center gap-1\" (click)=\"selectLanguage('all')\"><img height=\"18\" src=\"/assets/icons/flag.svg\" class=\"ng-star-inserted\"><span class=\"font-sm mx-4\">All</span></div></button>\r\n        </div>\r\n        <p-carousel [value]=\"languages\" [numVisible]=\"5\" [numScroll]=\"5\" [circular]=\"false\" [responsiveOptions]=\"responsiveOptions\" class=\"w-100\">\r\n            <ng-template let-language pTemplate=\"item\">\r\n                <button pbutton=\"\" type=\"button\" class=\"p-element p-button-outlined w-7rem p-button-rounded mt-2 button-outlined p-button-sm p-button p-component py-1 pl-1\"><div class=\"flex align-items-center gap-1\" (click)=\"selectLanguage(language)\"><img height=\"20\" src=\"/assets/icons/languages/{{ language }}.svg\" class=\"ng-star-inserted\"><span class=\"font-sm mx-3\">{{ language }}</span></div></button>\r\n            </ng-template>\r\n        </p-carousel>\r\n    </div>\r\n\r\n    <div class=\"card block-gradient mt-3\">\r\n        <div class=\"text-primary font-lg font-semibold pl-4\">Latest</div>\r\n        <p-carousel *ngIf=\"certificates && certificates.length > 0; else noLatest\" [value]=\"certificates.slice(-3)\" [numVisible]=\"1\" [numScroll]=\"1\" [circular]=\"false\">\r\n            <ng-template let-certificate pTemplate=\"item\">\r\n                <div class=\"m-2 px-3 flex align-items-center\">\r\n                    <div class=\"relative w-6 mb-3\">\r\n                        <img [src]=\"certificate.type == 'completion'? '/assets/images/dashboard/completion.png': '/assets/images/dashboard/attendance.png'\" class=\"w-full\"/>\r\n                        <img src=\"/assets/images/dashboard/new.svg\" alt=\"\" class=\"new-tag\">\r\n                    </div>\r\n                    <div class=\"text-primary font-bold font-2xl\">\r\n                        {{ certificate.language }} {{ certificate.level }} Certificate \r\n                        <br> of {{ certificate.type | titlecase }}\r\n                        <br>\r\n                        <div class=\"mt-4 pill-bg pt-1\" style=\"opacity: 0;\">\r\n                            <button class=\"mr-2 shareButton\" (click)=\"viewPdf(certificate.level, certificate.language, certificate.type)\">\r\n                                <img src=\"/assets/icons/view-file.svg\">\r\n                            </button>\r\n                            <app-share-button [topic]=\"'MyLingoTrip Certificate'\"  [componentId]=\"'Certificate'+certificate.id\"></app-share-button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </ng-template>\r\n        </p-carousel>\r\n        <ng-template #noLatest>\r\n            <div class=\"flex justify-content-center align-items-center py-5\">\r\n                <img src=\"/assets/icons/certificate-gradient.svg\" class=\"\">\r\n                <div class=\"text-primary font-bold font-3xl ml-3\">You haven’t received<br>\r\n                    any certifications yet</div>\r\n            </div>\r\n        </ng-template>\r\n    </div>\r\n    <div class=\"card block-gradient relative\">\r\n        <p-tabView>\r\n            <p-tabPanel header=\"All\">\r\n                <p-table [value]=\"allCertificates()\" *ngIf=\"allCertificates().length > 0; else noCertificates\">\r\n                    <ng-template pTemplate=\"header\">\r\n                        <tr class=\"font-sm text-blue\">\r\n                            <th>Level</th>\r\n                            <th>Hours</th>\r\n                            <th>Language</th>\r\n                            <th>Type</th>\r\n                            <th>Actions</th>\r\n                        </tr>\r\n                    </ng-template>\r\n                    <ng-template pTemplate=\"body\" let-certificate>\r\n                        <tr class=\"font-sm\">\r\n                            <td class=\"flex py-2\">\r\n                                <div>\r\n                                    <img src=\"/assets/icons/certificate-gradient.svg\" class=\"icon-cer\">\r\n                                    <span style=\"vertical-align: super;\">{{certificate.level}}</span>\r\n                                </div>\r\n                            </td>\r\n                            <td class=\"py-2\">{{certificate.hours}} hours</td>\r\n                            <td class=\"py-2\">{{certificate.language}}</td>\r\n                            <td class=\"py-2\">{{ certificate.type | titlecase }}</td>\r\n                            <td class=\"py-2\">\r\n                                <button class=\"mr-2 shareButton\" (click)=\"viewPdf(certificate.level, certificate.language, certificate.type, i, certificate.hours)\">\r\n                                    <!-- show spinner when isDownloadLoading = true -->\r\n                                    <ng-container *ngIf=\"isDownloadLoading[i]; else downloadButton\">\r\n                                        <img src=\"/assets/images/load.gif\" width=\"30\">\r\n                                    </ng-container>\r\n                                    <ng-template #downloadButton>\r\n                                        <img src=\"/assets/icons/view-file.svg\">\r\n                                    </ng-template>\r\n                                </button>\r\n                                <app-share-button class=\"hidden\" [topic]=\"'MyLingoTrip Certificate'\" [link]=\"shareUrl+certificate.urlParam\" [componentId]=\"'certificate'+certificate.id\"></app-share-button>\r\n                            </td>\r\n                        </tr>\r\n                    </ng-template>\r\n                </p-table>\r\n                <ng-template #noCertificates>\r\n                    <div class=\"flex justify-content-center align-items-center py-5\">\r\n                        <img src=\"/assets/icons/certificate-gradient.svg\" class=\"\">\r\n                        <div class=\"text-primary font-bold font-base ml-3\">No Certifications to show</div>\r\n                    </div>\r\n                </ng-template>\r\n            </p-tabPanel>\r\n            <p-tabPanel header=\"of Completion\">\r\n                <p-table [value]=\"completionCertificates\" *ngIf=\"completionCertificates.length > 0; else noCompletionCertificates\">\r\n                    <ng-template pTemplate=\"header\">\r\n                        <tr class=\"font-sm text-blue\">\r\n                            <th>Level</th>\r\n                            <th>Hours</th>\r\n                            <th>Language</th>\r\n                            <th>Type</th>\r\n                            <th>Actions</th>\r\n                        </tr>\r\n                    </ng-template>\r\n                    <ng-template pTemplate=\"body\" let-certificate>\r\n                        <tr class=\"font-sm\">\r\n                            <td class=\"flex py-2\">\r\n                                <div>\r\n                                    <img src=\"/assets/icons/certificate-gradient.svg\" class=\"icon-cer\">\r\n                                    <span style=\"vertical-align: super;\">{{certificate.level}}</span>\r\n                                </div>\r\n                            </td>\r\n                            <td class=\"py-2\">{{certificate.hours}} hours</td>\r\n                            <td class=\"py-2\">{{certificate.language}}</td>\r\n                            <td class=\"py-2\">{{certificate.type}}</td>\r\n                            <td class=\"py-2\">\r\n                                <button class=\"mr-2 shareButton\" (click)=\"view(certificate.level, certificate.language)\">\r\n                                    <img src=\"/assets/icons/view-file.svg\">\r\n                                </button>\r\n                                <div class=\"content\">\r\n                                    <button class=\"shareButton main\" (click)=\"toggleShareButtons()\">\r\n                                        <img src=\"/assets/icons/share-certificate.svg\">\r\n                                        <svg class=\"close\" style=\"width:24px;height:24px\" viewBox=\"0 0 24 24\">\r\n                                        <path d=\"M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z\" /></svg>\r\n                                    </button>\r\n                                    <button class=\"shareButton fb\" (click)=\"toggleShareButtons()\">\r\n                                        <img src=\"/assets/icons/facebook-share.svg\" class=\"w-100\">\r\n                                    </button>\r\n                                    <button class=\"shareButton tw ml-1\" (click)=\"toggleShareButtons()\">\r\n                                        <img src=\"/assets/icons/twitter.svg\" class=\"w-100\">\r\n                                    </button>\r\n                                    <button class=\"shareButton ig ml-1\" (click)=\"toggleShareButtons()\">\r\n                                        <img src=\"/assets/icons/linkedin-share.svg\" class=\"w-100\">\r\n                                    </button>\r\n                                </div>\r\n                            </td>\r\n                        </tr>\r\n                    </ng-template>\r\n                </p-table>\r\n                <ng-template #noCompletionCertificates>\r\n                    <div class=\"flex justify-content-center align-items-center py-5\">\r\n                        <img src=\"/assets/icons/certificate-gradient.svg\" class=\"\">\r\n                        <div class=\"text-primary font-bold font-base ml-3\">No Certifications to show</div>\r\n                    </div>\r\n                </ng-template>\r\n            </p-tabPanel>\r\n            <p-tabPanel header=\"of Attendance\">\r\n                <p-table [value]=\"attendanceCertificates\" *ngIf=\"attendanceCertificates.length > 0; else noAttendanceCertificates\">\r\n                    <ng-template pTemplate=\"header\">\r\n                        <tr class=\"font-sm text-blue\">\r\n                            <th>Level</th>\r\n                            <th>Hours</th>\r\n                            <th>Language</th>\r\n                            <th>Type</th>\r\n                            <th>Actions</th>\r\n                        </tr>\r\n                    </ng-template>\r\n                    <ng-template pTemplate=\"body\" let-certificate>\r\n                        <tr class=\"font-sm\">\r\n                            <td class=\"flex py-2\">\r\n                                <div>\r\n                                    <img src=\"/assets/icons/certificate-gradient.svg\" class=\"icon-cer\">\r\n                                    <span style=\"vertical-align: super;\">{{certificate.level}}</span>\r\n                                </div>\r\n                            </td>\r\n                            <td class=\"py-2\">{{certificate.hours}} hours</td>\r\n                            <td class=\"py-2\">{{certificate.language}}</td>\r\n                            <td class=\"py-2\">{{certificate.type}}</td>\r\n                            <td class=\"py-2\">\r\n                                <button class=\"mr-2 shareButton\" (click)=\"view(certificate.level, certificate.language)\">\r\n                                    <img src=\"/assets/icons/view-file.svg\">\r\n                                </button>\r\n                                <div class=\"content\">\r\n                                    <button class=\"shareButton main\" (click)=\"toggleShareButtons()\">\r\n                                        <img src=\"/assets/icons/share-certificate.svg\">\r\n                                        <svg class=\"close\" style=\"width:24px;height:24px\" viewBox=\"0 0 24 24\">\r\n                                        <path d=\"M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z\" /></svg>\r\n                                    </button>\r\n                                    <button class=\"shareButton fb\" (click)=\"toggleShareButtons()\">\r\n                                        <img src=\"/assets/icons/facebook-share.svg\" class=\"w-100\">\r\n                                    </button>\r\n                                    <button class=\"shareButton tw ml-1\" (click)=\"toggleShareButtons()\">\r\n                                        <img src=\"/assets/icons/twitter.svg\" class=\"w-100\">\r\n                                    </button>\r\n                                    <button class=\"shareButton ig ml-1\" (click)=\"toggleShareButtons()\">\r\n                                        <img src=\"/assets/icons/linkedin-share.svg\" class=\"w-100\">\r\n                                    </button>\r\n                                </div>\r\n                            </td>\r\n                        </tr>\r\n                    </ng-template>\r\n                </p-table>\r\n                <ng-template #noAttendanceCertificates>\r\n                    <div class=\"flex justify-content-center align-items-center py-5\">\r\n                        <img src=\"/assets/icons/certificate-gradient.svg\" class=\"\">\r\n                        <div class=\"text-primary font-bold font-base ml-3\">No Certifications to show</div>\r\n                    </div>\r\n                </ng-template>\r\n            </p-tabPanel>\r\n        </p-tabView>\r\n        <!-- <button pbutton=\"\" type=\"button\" class=\"p-element p-button-outlined p-button-rounded button-outlined p-button-sm p-button p-component py-1 btn-modify\"><div class=\"flex align-items-center gap-1\" (click)=\"showDialog()\">\r\n            <span class=\"font-sm\">Request Modification</span></div></button> -->\r\n    </div>\r\n</div>\r\n<p-dialog header=\"Header\" [(visible)]=\"visible\" [breakpoints]=\"{ '960px': '75vw' }\" [style]=\"{ width: '50vw' }\" [draggable]=\"false\" [resizable]=\"false\">\r\n    <p>\r\n        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo\r\n        consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.\r\n    </p>\r\n</p-dialog>"], "mappings": "AAAA,SAAoBA,MAAM,EAAUC,MAAM,QAAQ,eAAe;AAMjE,SAASC,OAAO,QAAQ,SAAS;AAIjC,SAASC,gBAAgB,QAAQ,yCAAyC;;;;;;;;;;;;;;;;;;;;ICDmGC,EAA7J,CAAAC,cAAA,iBAA6J,cAA8E;IAAnCD,EAAA,CAAAE,UAAA,mBAAAC,kEAAA;MAAA,MAAAC,WAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,cAAA,CAAAP,WAAA,CAAwB;IAAA,EAAC;IAACJ,EAAA,CAAAY,SAAA,cAA2F;IAAAZ,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAa,MAAA,GAAc;IAAab,EAAb,CAAAc,YAAA,EAAO,EAAM,EAAS;;;;IAAzId,EAAA,CAAAe,SAAA,GAAgD;IAAhDf,EAAA,CAAAgB,sBAAA,oCAAAZ,WAAA,UAAAJ,EAAA,CAAAiB,aAAA,CAAgD;IAAqDjB,EAAA,CAAAe,SAAA,GAAc;IAAdf,EAAA,CAAAkB,iBAAA,CAAAd,WAAA,CAAc;;;;;;IAU3WJ,EADJ,CAAAC,cAAA,cAA8C,cACX;IAE3BD,EADA,CAAAY,SAAA,cAAoJ,cACjF;IACvEZ,EAAA,CAAAc,YAAA,EAAM;IACNd,EAAA,CAAAC,cAAA,cAA6C;IACzCD,EAAA,CAAAa,MAAA,GACA;IAAAb,EAAA,CAAAY,SAAA,SAAI;IAACZ,EAAA,CAAAa,MAAA,GACL;;IAAAb,EAAA,CAAAY,SAAA,SAAI;IAEAZ,EADJ,CAAAC,cAAA,eAAmD,kBAC+D;IAA7ED,EAAA,CAAAE,UAAA,mBAAAiB,mFAAA;MAAA,MAAAC,cAAA,GAAApB,EAAA,CAAAK,aAAA,CAAAgB,GAAA,EAAAd,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAc,OAAA,CAAAF,cAAA,CAAAG,KAAA,EAAAH,cAAA,CAAAI,QAAA,EAAAJ,cAAA,CAAAK,IAAA,CAAkE;IAAA,EAAC;IACzGzB,EAAA,CAAAY,SAAA,eAAuC;IAC3CZ,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAY,SAAA,4BAAuH;IAGnIZ,EAFQ,CAAAc,YAAA,EAAM,EACJ,EACJ;;;;IAdOd,EAAA,CAAAe,SAAA,GAA8H;IAA9Hf,EAAA,CAAA0B,UAAA,QAAAN,cAAA,CAAAK,IAAA,0GAAAzB,EAAA,CAAAiB,aAAA,CAA8H;IAInIjB,EAAA,CAAAe,SAAA,GACA;IADAf,EAAA,CAAA2B,kBAAA,MAAAP,cAAA,CAAAI,QAAA,OAAAJ,cAAA,CAAAG,KAAA,kBACA;IAAKvB,EAAA,CAAAe,SAAA,GACL;IADKf,EAAA,CAAA4B,kBAAA,SAAA5B,EAAA,CAAA6B,WAAA,OAAAT,cAAA,CAAAK,IAAA,OACL;IAKsBzB,EAAA,CAAAe,SAAA,GAAmC;IAAEf,EAArC,CAAA0B,UAAA,oCAAmC,gCAAAN,cAAA,CAAAU,EAAA,CAA8C;;;;;IAfvH9B,EAAA,CAAAC,cAAA,qBAAgK;IAC5JD,EAAA,CAAA+B,UAAA,IAAAC,yDAAA,2BAA8C;IAmBlDhC,EAAA,CAAAc,YAAA,EAAa;;;;IApBgId,EAAlE,CAAA0B,UAAA,UAAAlB,MAAA,CAAAyB,YAAA,CAAAC,KAAA,KAAgC,iBAAiB,gBAAgB,mBAAmB;;;;;IAsB3JlC,EAAA,CAAAC,cAAA,cAAiE;IAC7DD,EAAA,CAAAY,SAAA,cAA2D;IAC3DZ,EAAA,CAAAC,cAAA,cAAkD;IAAAD,EAAA,CAAAa,MAAA,gCAAoB;IAAAb,EAAA,CAAAY,SAAA,SAAI;IACtEZ,EAAA,CAAAa,MAAA,8BAAsB;IAC9Bb,EAD8B,CAAAc,YAAA,EAAM,EAC9B;;;;;IASUd,EADJ,CAAAC,cAAA,aAA8B,SACtB;IAAAD,EAAA,CAAAa,MAAA,YAAK;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACdd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,YAAK;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACdd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,eAAQ;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACjBd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,WAAI;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACbd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,eAAO;IACfb,EADe,CAAAc,YAAA,EAAK,EACf;;;;;IAgBOd,EAAA,CAAAmC,uBAAA,GAAgE;IAC5DnC,EAAA,CAAAY,SAAA,cAA8C;;;;;;IAG9CZ,EAAA,CAAAY,SAAA,cAAuC;;;;;;IAf/CZ,EAFR,CAAAC,cAAA,aAAoB,aACM,UACb;IACDD,EAAA,CAAAY,SAAA,cAAmE;IACnEZ,EAAA,CAAAC,cAAA,eAAqC;IAAAD,EAAA,CAAAa,MAAA,GAAqB;IAElEb,EAFkE,CAAAc,YAAA,EAAO,EAC/D,EACL;IACLd,EAAA,CAAAC,cAAA,aAAiB;IAAAD,EAAA,CAAAa,MAAA,GAA2B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACjDd,EAAA,CAAAC,cAAA,aAAiB;IAAAD,EAAA,CAAAa,MAAA,GAAwB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC9Cd,EAAA,CAAAC,cAAA,cAAiB;IAAAD,EAAA,CAAAa,MAAA,IAAkC;;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAEpDd,EADJ,CAAAC,cAAA,cAAiB,kBACuH;IAAnGD,EAAA,CAAAE,UAAA,mBAAAkC,gFAAA;MAAA,MAAAC,cAAA,GAAArC,EAAA,CAAAK,aAAA,CAAAiC,GAAA,EAAA/B,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAc,OAAA,CAAAe,cAAA,CAAAd,KAAA,EAAAc,cAAA,CAAAb,QAAA,EAAAa,cAAA,CAAAZ,IAAA,EAAAjB,MAAA,CAAA+B,CAAA,EAAAF,cAAA,CAAAG,KAAA,CAAwF;IAAA,EAAC;IAK/HxC,EAHA,CAAA+B,UAAA,KAAAU,sEAAA,2BAAgE,KAAAC,qEAAA,gCAAA1C,EAAA,CAAA2C,sBAAA,CAGnC;IAGjC3C,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAY,SAAA,4BAA4K;IAEpLZ,EADI,CAAAc,YAAA,EAAK,EACJ;;;;;;IAlB4Cd,EAAA,CAAAe,SAAA,GAAqB;IAArBf,EAAA,CAAAkB,iBAAA,CAAAmB,cAAA,CAAAd,KAAA,CAAqB;IAGjDvB,EAAA,CAAAe,SAAA,GAA2B;IAA3Bf,EAAA,CAAA4B,kBAAA,KAAAS,cAAA,CAAAG,KAAA,WAA2B;IAC3BxC,EAAA,CAAAe,SAAA,GAAwB;IAAxBf,EAAA,CAAAkB,iBAAA,CAAAmB,cAAA,CAAAb,QAAA,CAAwB;IACxBxB,EAAA,CAAAe,SAAA,GAAkC;IAAlCf,EAAA,CAAAkB,iBAAA,CAAAlB,EAAA,CAAA6B,WAAA,QAAAQ,cAAA,CAAAZ,IAAA,EAAkC;IAI5BzB,EAAA,CAAAe,SAAA,GAA4B;IAAAf,EAA5B,CAAA0B,UAAA,SAAAlB,MAAA,CAAAoC,iBAAA,CAAApC,MAAA,CAAA+B,CAAA,EAA4B,aAAAM,iBAAA,CAAmB;IAOjC7C,EAAA,CAAAe,SAAA,GAAmC;IAAwCf,EAA3E,CAAA0B,UAAA,oCAAmC,SAAAlB,MAAA,CAAAsC,QAAA,GAAAT,cAAA,CAAAU,QAAA,CAAuC,gCAAAV,cAAA,CAAAP,EAAA,CAA6C;;;;;IA/BxK9B,EAAA,CAAAC,cAAA,kBAA+F;IAU3FD,EATA,CAAA+B,UAAA,IAAAiB,sDAAA,2BAAgC,IAAAC,sDAAA,4BASc;IAyBlDjD,EAAA,CAAAc,YAAA,EAAU;;;;IAnCDd,EAAA,CAAA0B,UAAA,UAAAlB,MAAA,CAAA0C,eAAA,GAA2B;;;;;IAqChClD,EAAA,CAAAC,cAAA,cAAiE;IAC7DD,EAAA,CAAAY,SAAA,cAA2D;IAC3DZ,EAAA,CAAAC,cAAA,cAAmD;IAAAD,EAAA,CAAAa,MAAA,gCAAyB;IAChFb,EADgF,CAAAc,YAAA,EAAM,EAChF;;;;;IAOEd,EADJ,CAAAC,cAAA,aAA8B,SACtB;IAAAD,EAAA,CAAAa,MAAA,YAAK;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACdd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,YAAK;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACdd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,eAAQ;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACjBd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,WAAI;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACbd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,eAAO;IACfb,EADe,CAAAc,YAAA,EAAK,EACf;;;;;;IAKGd,EAFR,CAAAC,cAAA,aAAoB,aACM,UACb;IACDD,EAAA,CAAAY,SAAA,cAAmE;IACnEZ,EAAA,CAAAC,cAAA,eAAqC;IAAAD,EAAA,CAAAa,MAAA,GAAqB;IAElEb,EAFkE,CAAAc,YAAA,EAAO,EAC/D,EACL;IACLd,EAAA,CAAAC,cAAA,aAAiB;IAAAD,EAAA,CAAAa,MAAA,GAA2B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACjDd,EAAA,CAAAC,cAAA,aAAiB;IAAAD,EAAA,CAAAa,MAAA,GAAwB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC9Cd,EAAA,CAAAC,cAAA,cAAiB;IAAAD,EAAA,CAAAa,MAAA,IAAoB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAEtCd,EADJ,CAAAC,cAAA,cAAiB,kBAC4E;IAAxDD,EAAA,CAAAE,UAAA,mBAAAiD,gFAAA;MAAA,MAAAC,eAAA,GAAApD,EAAA,CAAAK,aAAA,CAAAgD,IAAA,EAAA9C,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA8C,IAAA,CAAAF,eAAA,CAAA7B,KAAA,EAAA6B,eAAA,CAAA5B,QAAA,CAA6C;IAAA,EAAC;IACpFxB,EAAA,CAAAY,SAAA,eAAuC;IAC3CZ,EAAA,CAAAc,YAAA,EAAS;IAELd,EADJ,CAAAC,cAAA,eAAqB,kBAC+C;IAA/BD,EAAA,CAAAE,UAAA,mBAAAqD,gFAAA;MAAAvD,EAAA,CAAAK,aAAA,CAAAgD,IAAA;MAAA,MAAA7C,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAgD,kBAAA,EAAoB;IAAA,EAAC;IAC3DxD,EAAA,CAAAY,SAAA,eAA+C;;IAC/CZ,EAAA,CAAAC,cAAA,eAAsE;IACtED,EAAA,CAAAY,SAAA,gBAA0H;IAC9HZ,EAD8H,CAAAc,YAAA,EAAM,EAC3H;;IACTd,EAAA,CAAAC,cAAA,kBAA8D;IAA/BD,EAAA,CAAAE,UAAA,mBAAAuD,gFAAA;MAAAzD,EAAA,CAAAK,aAAA,CAAAgD,IAAA;MAAA,MAAA7C,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAgD,kBAAA,EAAoB;IAAA,EAAC;IACzDxD,EAAA,CAAAY,SAAA,eAA0D;IAC9DZ,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAAmE;IAA/BD,EAAA,CAAAE,UAAA,mBAAAwD,gFAAA;MAAA1D,EAAA,CAAAK,aAAA,CAAAgD,IAAA;MAAA,MAAA7C,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAgD,kBAAA,EAAoB;IAAA,EAAC;IAC9DxD,EAAA,CAAAY,SAAA,eAAmD;IACvDZ,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAAmE;IAA/BD,EAAA,CAAAE,UAAA,mBAAAyD,gFAAA;MAAA3D,EAAA,CAAAK,aAAA,CAAAgD,IAAA;MAAA,MAAA7C,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAgD,kBAAA,EAAoB;IAAA,EAAC;IAC9DxD,EAAA,CAAAY,SAAA,eAA0D;IAI1EZ,EAHY,CAAAc,YAAA,EAAS,EACP,EACL,EACJ;;;;IA3B4Cd,EAAA,CAAAe,SAAA,GAAqB;IAArBf,EAAA,CAAAkB,iBAAA,CAAAkC,eAAA,CAAA7B,KAAA,CAAqB;IAGjDvB,EAAA,CAAAe,SAAA,GAA2B;IAA3Bf,EAAA,CAAA4B,kBAAA,KAAAwB,eAAA,CAAAZ,KAAA,WAA2B;IAC3BxC,EAAA,CAAAe,SAAA,GAAwB;IAAxBf,EAAA,CAAAkB,iBAAA,CAAAkC,eAAA,CAAA5B,QAAA,CAAwB;IACxBxB,EAAA,CAAAe,SAAA,GAAoB;IAApBf,EAAA,CAAAkB,iBAAA,CAAAkC,eAAA,CAAA3B,IAAA,CAAoB;;;;;IApBjDzB,EAAA,CAAAC,cAAA,kBAAmH;IAU/GD,EATA,CAAA+B,UAAA,IAAA6B,sDAAA,2BAAgC,IAAAC,sDAAA,2BASc;IAkClD7D,EAAA,CAAAc,YAAA,EAAU;;;;IA5CDd,EAAA,CAAA0B,UAAA,UAAAlB,MAAA,CAAAsD,sBAAA,CAAgC;;;;;IA8CrC9D,EAAA,CAAAC,cAAA,cAAiE;IAC7DD,EAAA,CAAAY,SAAA,cAA2D;IAC3DZ,EAAA,CAAAC,cAAA,cAAmD;IAAAD,EAAA,CAAAa,MAAA,gCAAyB;IAChFb,EADgF,CAAAc,YAAA,EAAM,EAChF;;;;;IAOEd,EADJ,CAAAC,cAAA,aAA8B,SACtB;IAAAD,EAAA,CAAAa,MAAA,YAAK;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACdd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,YAAK;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACdd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,eAAQ;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACjBd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,WAAI;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACbd,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAa,MAAA,eAAO;IACfb,EADe,CAAAc,YAAA,EAAK,EACf;;;;;;IAKGd,EAFR,CAAAC,cAAA,aAAoB,aACM,UACb;IACDD,EAAA,CAAAY,SAAA,cAAmE;IACnEZ,EAAA,CAAAC,cAAA,eAAqC;IAAAD,EAAA,CAAAa,MAAA,GAAqB;IAElEb,EAFkE,CAAAc,YAAA,EAAO,EAC/D,EACL;IACLd,EAAA,CAAAC,cAAA,aAAiB;IAAAD,EAAA,CAAAa,MAAA,GAA2B;IAAAb,EAAA,CAAAc,YAAA,EAAK;IACjDd,EAAA,CAAAC,cAAA,aAAiB;IAAAD,EAAA,CAAAa,MAAA,GAAwB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAC9Cd,EAAA,CAAAC,cAAA,cAAiB;IAAAD,EAAA,CAAAa,MAAA,IAAoB;IAAAb,EAAA,CAAAc,YAAA,EAAK;IAEtCd,EADJ,CAAAC,cAAA,cAAiB,kBAC4E;IAAxDD,EAAA,CAAAE,UAAA,mBAAA6D,gFAAA;MAAA,MAAAC,eAAA,GAAAhE,EAAA,CAAAK,aAAA,CAAA4D,IAAA,EAAA1D,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA8C,IAAA,CAAAU,eAAA,CAAAzC,KAAA,EAAAyC,eAAA,CAAAxC,QAAA,CAA6C;IAAA,EAAC;IACpFxB,EAAA,CAAAY,SAAA,eAAuC;IAC3CZ,EAAA,CAAAc,YAAA,EAAS;IAELd,EADJ,CAAAC,cAAA,eAAqB,kBAC+C;IAA/BD,EAAA,CAAAE,UAAA,mBAAAgE,gFAAA;MAAAlE,EAAA,CAAAK,aAAA,CAAA4D,IAAA;MAAA,MAAAzD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAgD,kBAAA,EAAoB;IAAA,EAAC;IAC3DxD,EAAA,CAAAY,SAAA,eAA+C;;IAC/CZ,EAAA,CAAAC,cAAA,eAAsE;IACtED,EAAA,CAAAY,SAAA,gBAA0H;IAC9HZ,EAD8H,CAAAc,YAAA,EAAM,EAC3H;;IACTd,EAAA,CAAAC,cAAA,kBAA8D;IAA/BD,EAAA,CAAAE,UAAA,mBAAAiE,gFAAA;MAAAnE,EAAA,CAAAK,aAAA,CAAA4D,IAAA;MAAA,MAAAzD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAgD,kBAAA,EAAoB;IAAA,EAAC;IACzDxD,EAAA,CAAAY,SAAA,eAA0D;IAC9DZ,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAAmE;IAA/BD,EAAA,CAAAE,UAAA,mBAAAkE,gFAAA;MAAApE,EAAA,CAAAK,aAAA,CAAA4D,IAAA;MAAA,MAAAzD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAgD,kBAAA,EAAoB;IAAA,EAAC;IAC9DxD,EAAA,CAAAY,SAAA,eAAmD;IACvDZ,EAAA,CAAAc,YAAA,EAAS;IACTd,EAAA,CAAAC,cAAA,kBAAmE;IAA/BD,EAAA,CAAAE,UAAA,mBAAAmE,gFAAA;MAAArE,EAAA,CAAAK,aAAA,CAAA4D,IAAA;MAAA,MAAAzD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAgD,kBAAA,EAAoB;IAAA,EAAC;IAC9DxD,EAAA,CAAAY,SAAA,eAA0D;IAI1EZ,EAHY,CAAAc,YAAA,EAAS,EACP,EACL,EACJ;;;;IA3B4Cd,EAAA,CAAAe,SAAA,GAAqB;IAArBf,EAAA,CAAAkB,iBAAA,CAAA8C,eAAA,CAAAzC,KAAA,CAAqB;IAGjDvB,EAAA,CAAAe,SAAA,GAA2B;IAA3Bf,EAAA,CAAA4B,kBAAA,KAAAoC,eAAA,CAAAxB,KAAA,WAA2B;IAC3BxC,EAAA,CAAAe,SAAA,GAAwB;IAAxBf,EAAA,CAAAkB,iBAAA,CAAA8C,eAAA,CAAAxC,QAAA,CAAwB;IACxBxB,EAAA,CAAAe,SAAA,GAAoB;IAApBf,EAAA,CAAAkB,iBAAA,CAAA8C,eAAA,CAAAvC,IAAA,CAAoB;;;;;IApBjDzB,EAAA,CAAAC,cAAA,kBAAmH;IAU/GD,EATA,CAAA+B,UAAA,IAAAuC,sDAAA,2BAAgC,IAAAC,sDAAA,2BASc;IAkClDvE,EAAA,CAAAc,YAAA,EAAU;;;;IA5CDd,EAAA,CAAA0B,UAAA,UAAAlB,MAAA,CAAAgE,sBAAA,CAAgC;;;;;IA8CrCxE,EAAA,CAAAC,cAAA,cAAiE;IAC7DD,EAAA,CAAAY,SAAA,cAA2D;IAC3DZ,EAAA,CAAAC,cAAA,cAAmD;IAAAD,EAAA,CAAAa,MAAA,gCAAyB;IAChFb,EADgF,CAAAc,YAAA,EAAM,EAChF;;;ADjL1B,OAAM,MAAO2D,oBAAoB;EAkB/BC,YACUC,mBAAuC,EACvCC,WAAwB,EACxBC,QAAkB;IAFlB,KAAAF,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IApBlB,KAAAC,gBAAgB,GAAGlF,MAAM,CAACG,gBAAgB,CAAC;IAC3C,KAAAgF,gBAAgB,GAAkB,EAAE;IACpC,KAAA9C,YAAY,GAAkB,EAAE;IAChC,KAAA6B,sBAAsB,GAAkB,EAAE;IAC1C,KAAAU,sBAAsB,GAAkB,EAAE;IAC1C,KAAAQ,SAAS,GAAa,EAAE;IACxB,KAAAC,YAAY,GAAwB,EAAE;IACtC,KAAAC,OAAO,GAAY,KAAK;IAExB,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,MAAM,GAAG,EAAE;IACX,KAAAtC,QAAQ,GAAG,8CAA8C;IACzD,KAAAF,iBAAiB,GAAc,EAAE;IACjC,KAAAyC,IAAI,GAAQ,EAAU;IACtB,KAAAnC,eAAe,GAAGrD,MAAM,CAAC,EAAW,CAAC;IAE7B,KAAAyF,IAAI,GAAG,IAAIxF,OAAO,EAAE;EAKxB;EAEJyF,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,GAAG,CACvB;MACEC,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAE;KACZ,EACD;MACEF,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAE;KACZ,CACF;IACD,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACP,IAAI,GAAG,IAAI,CAACT,WAAW,CAACiB,eAAe,EAAE;IAC9C,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACT,IAAI,CAACU,WAAW,EAAE;EACzB;EAEAF,cAAcA,CAAA;IACZ,IAAI,CAAChB,gBAAgB,CAACmB,oBAAoB,CAAC,IAAI,CAACZ,IAAI,CAACvD,EAAE,CAAC,CAACoE,SAAS,CAACC,GAAG,IAAG;MACvE,IAAI,CAACA,GAAG,EACN;MACFC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACC,iCAAiC,CAACH,GAAG,CAAC,CAAC;MACxD,IAAI,CAACjD,eAAe,CAACqD,GAAG,CAAC,IAAI,CAACD,iCAAiC,CAACH,GAAG,CAAC,CAAC;IACvE,CAAC,CAAC;EACJ;EAEAG,iCAAiCA,CAACE,UAAiB;IACjD,MAAMvE,YAAY,GAAU,EAAE;IAE9BuE,UAAU,CAACC,OAAO,CAACC,SAAS,IAAG;MAC7B,MAAMC,iBAAiB,GAAGD,SAAS,CAACE,QAAQ,CAACC,MAAM,CAAEC,GAAQ,IAAKA,GAAG,CAACC,KAAK,KAAK,WAAW,CAAC;MAC5F,MAAMC,eAAe,GAAGN,SAAS,CAACE,QAAQ,CAACC,MAAM,CAAEC,GAAQ,IAAKA,GAAG,CAACC,KAAK,KAAK,SAAS,CAAC;MAExF,MAAME,UAAU,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC,GAAGP,iBAAiB,EAAE,GAAGK,eAAe,CAAC,CAAC;MAEvF;MACA,IAAIL,iBAAiB,CAACQ,MAAM,KAAK,CAAC,EAAE;QAClC;QACAlF,YAAY,CAACmF,IAAI,CAAC;UAChB7F,KAAK,EAAEmF,SAAS,CAACW,WAAW;UAC5B7F,QAAQ,EAAEkF,SAAS,CAAClF,QAAQ;UAC5BgB,KAAK,EAAEmE,iBAAiB,CAAC,CAAC,CAAC,CAACM,UAAU;UAAE;UACxCxF,IAAI,EAAE;SACP,CAAC;MACJ,CAAC,MAAM,IAAIkF,iBAAiB,CAACQ,MAAM,GAAG,CAAC,EAAE;QACvC;QACAlF,YAAY,CAACmF,IAAI,CAAC;UAChB7F,KAAK,EAAEmF,SAAS,CAACW,WAAW;UAC5B7F,QAAQ,EAAEkF,SAAS,CAAClF,QAAQ;UAC5BgB,KAAK,EAAEyE,UAAU;UAAE;UACnBxF,IAAI,EAAE;SACP,CAAC;MACJ;MAEA;MACA,IAAIkF,iBAAiB,CAACQ,MAAM,KAAK,CAAC,IAAIH,eAAe,CAACG,MAAM,GAAG,CAAC,EAAE;QAChElF,YAAY,CAACmF,IAAI,CAAC;UAChB7F,KAAK,EAAEmF,SAAS,CAACW,WAAW;UAC5B7F,QAAQ,EAAEkF,SAAS,CAAClF,QAAQ;UAC5BgB,KAAK,EAAEyE,UAAU;UACjBxF,IAAI,EAAE;SACP,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAOQ,YAAY;EACrB;EAIAiF,mBAAmBA,CAACN,QAAe;IACjC,IAAIK,UAAU,GAAG,CAAC;IAElBL,QAAQ,CAACH,OAAO,CAACK,GAAG,IAAG;MACrB,IAAIA,GAAG,CAACC,KAAK,KAAK,WAAW,EAAE;QAC7BE,UAAU,IAAIH,GAAG,CAACG,UAAU,CAAC,CAAC;MAChC,CAAC,MAAM,IAAIH,GAAG,CAACC,KAAK,KAAK,SAAS,EAAE;QAClCE,UAAU,IAAIH,GAAG,CAACQ,SAAS,CAAC,CAAC;MAC/B;IACF,CAAC,CAAC;IAEF,OAAOL,UAAU;EACnB;EAEA3F,OAAOA,CAACC,KAAU,EAAEC,QAAa,EAAEC,IAAS,EAAE8F,KAAa,EAAE/E,KAAc;IACzE,IAAI,CAACI,iBAAiB,CAAC2E,KAAK,CAAC,GAAG,IAAI;IACpC,IAAIC,UAAU,GAAGhF,KAAK,EAAEiF,QAAQ,EAAY,CAAC,CAAC;IAC9C,IAAIC,MAAM,GAAWC,UAAU,CAACH,UAAU,CAAC,CAAC,CAAC;IAC7C,IAAII,cAAc,GAAWF,MAAM,CAACG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD,IAAI,CAACvC,IAAI,CAACwC,IAAI,GAAG,IAAI,CAACnD,mBAAmB,CAACoD,wBAAwB,CAACxG,KAAK,EAAEC,QAAQ,EAAEC,IAAI,EAAEmG,cAAc,CAAC,CAAC1B,SAAS,CAAE8B,WAAgB,IAAI;MACvI,IAAI,CAACpF,iBAAiB,CAAC2E,KAAK,CAAC,GAAG,KAAK;MACrC,MAAMU,cAAc,GAAGC,IAAI,CAACF,WAAW,CAAC;MACxC,MAAMG,WAAW,GAAG,IAAIC,KAAK,CAACH,cAAc,CAACd,MAAM,CAAC;MACpD,KAAK,IAAI5E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0F,cAAc,CAACd,MAAM,EAAE5E,CAAC,EAAE,EAAE;QAC9C4F,WAAW,CAAC5F,CAAC,CAAC,GAAG0F,cAAc,CAACI,UAAU,CAAC9F,CAAC,CAAC;MAC/C;MACA,MAAM+F,SAAS,GAAG,IAAIC,UAAU,CAACJ,WAAW,CAAC;MAC7C,MAAMK,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,SAAS,CAAC,EAAE;QAAE7G,IAAI,EAAE;MAAiB,CAAE,CAAC;MAC/D,IAAIiH,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;MAC1C,IAAIM,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,MAAM,CAACG,IAAI,GAAGP,GAAG;MACjBI,MAAM,CAACI,MAAM,GAAG,QAAQ;MACxBJ,MAAM,CAACK,KAAK,EAAE;IAChB,CAAC,EACCC,KAAK,IAAG;MACNhD,OAAO,CAACgD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC/C,CAAC,CAAC;EACN;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEAA,gBAAgBA,CAAA;IACd,IAAI,CAACrE,YAAY,GAAGmD,KAAK,CAACmB,IAAI,CAACR,QAAQ,CAACS,gBAAgB,CAAC,oBAAoB,CAAC,CAAwB;EACxG;EAEAhG,kBAAkBA,CAAA;IAChB,KAAK,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC0C,YAAY,CAACkC,MAAM,EAAE5E,CAAC,EAAE,EAAE;MACjD,IAAI,CAAC0C,YAAY,CAAC1C,CAAC,CAAC,CAACkH,SAAS,CAACC,MAAM,CAAC,MAAM,CAAC;IAC/C;EACF;EACAC,UAAUA,CAAA;IACR,IAAI,CAACzE,OAAO,GAAG,IAAI;EACrB;EACAvE,cAAcA,CAACiJ,IAAY;IACzB,IAAI,CAAC3H,YAAY,GAAG,IAAI,CAAC8C,gBAAgB;IACzC,IAAI,CAACP,sBAAsB,GAAG,IAAI,CAACvC,YAAY,CAAC4E,MAAM,CAAEgD,WAA8B,IAAKA,WAAW,CAACpI,IAAI,KAAK,YAAY,CAAC;IAC7H,IAAI,CAACqC,sBAAsB,GAAG,IAAI,CAAC7B,YAAY,CAAC4E,MAAM,CAAEgD,WAA8B,IAAKA,WAAW,CAACpI,IAAI,KAAK,YAAY,CAAC;IAC7H,IAAImI,IAAI,KAAK,KAAK,EAAE;MAClB,IAAI,CAAC3H,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC4E,MAAM,CAAEgD,WAAkC,IAAKA,WAAW,CAACrI,QAAQ,KAAKoI,IAAI,CAAC;MACnH,IAAI,CAACpF,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAACqC,MAAM,CAAEgD,WAAkC,IAAKA,WAAW,CAACrI,QAAQ,KAAKoI,IAAI,CAAC;MACvI,IAAI,CAAC9F,sBAAsB,GAAG,IAAI,CAACA,sBAAsB,CAAC+C,MAAM,CAAEgD,WAAkC,IAAKA,WAAW,CAACrI,QAAQ,KAAKoI,IAAI,CAAC;IACzI;EACF;EAEAhE,iBAAiBA,CAAA;IACf,IAAI,CAACN,IAAI,CAACwC,IAAI,GAAI,IAAI,CAACnD,mBAAmB,CAACmF,uBAAuB,EAAE,CAAC5D,SAAS,CAAE6D,QAAQ,IAAI;MAC1F,IAAI,CAAChF,gBAAgB,GAAGgF,QAAQ;MAChC,IAAI,CAAChF,gBAAgB,CAAC0B,OAAO,CAAEoD,WAAW,IAAI;QAC5CA,WAAW,CAAC9G,QAAQ,GAAGiH,IAAI,CAAC,QAAQH,WAAW,CAACpI,IAAI,SAAS,IAAI,CAAC4D,IAAI,CAAC4E,SAAS,IAAI,IAAI,CAAC5E,IAAI,CAAC6E,QAAQ,aAAaL,WAAW,CAACrI,QAAQ,UAAUqI,WAAW,CAACtI,KAAK,UAAUsI,WAAW,CAACrH,KAAK,SAAS,IAAI,CAAC2H,WAAW,CAACN,WAAW,CAACO,QAAQ,CAAC,EAAE,CAAC;MACjP,CAAC,CAAC;MACF,IAAI,CAACpF,SAAS,GAAG,IAAI,CAACD,gBAAgB,CAACsF,GAAG,CAAER,WAAW,IAAKA,WAAW,CAACrI,QAAQ,CAAC,CAAC;MAAA,CAC/EqF,MAAM,CAAC,CAACrF,QAAQ,EAAE+F,KAAK,EAAE+C,KAAK,KAAKA,KAAK,CAACC,OAAO,CAAC/I,QAAQ,CAAC,KAAK+F,KAAK,CAAC,CAAC,CAAC;MAC1E,IAAI,CAACtF,YAAY,GAAG,IAAI,CAAC8C,gBAAgB;MACzC,IAAI,CAACjB,sBAAsB,GAAG,IAAI,CAACiB,gBAAgB,CAAC8B,MAAM,CAAEgD,WAAW,IAAKA,WAAW,CAACpI,IAAI,KAAK,YAAY,CAAC;MAC9G,IAAI,CAAC+C,sBAAsB,GAAG,IAAI,CAACO,gBAAgB,CAAC8B,MAAM,CAAEgD,WAAW,IAAKA,WAAW,CAACpI,IAAI,KAAK,YAAY,CAAC;IAChH,CAAC,CAAE;EACL;EAEO+I,SAASA,CAACC,OAAe,EAAEC,WAAmB;IACnDA,WAAW,GAAGA,WAAW,IAAI,EAAE;IAC/B,IAAIC,SAAS,GAAG,GAAG;IAEnB,IAAI1C,cAAc,GAAGC,IAAI,CAACuC,OAAO,CAAC;IAClC,IAAIG,UAAU,GAAG,EAAE;IAEnB,KAAK,IAAIC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAG5C,cAAc,CAACd,MAAM,EAAE0D,MAAM,IAAIF,SAAS,EAAE;MACxE,IAAIzI,KAAK,GAAG+F,cAAc,CAAC/F,KAAK,CAAC2I,MAAM,EAAEA,MAAM,GAAGF,SAAS,CAAC;MAE5D,IAAIxC,WAAW,GAAG,IAAIC,KAAK,CAAClG,KAAK,CAACiF,MAAM,CAAC;MACzC,KAAK,IAAI5E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAACiF,MAAM,EAAE5E,CAAC,EAAE,EAAE;QACrC4F,WAAW,CAAC5F,CAAC,CAAC,GAAGL,KAAK,CAACmG,UAAU,CAAC9F,CAAC,CAAC;MACtC;MAEA,IAAI+F,SAAS,GAAG,IAAIC,UAAU,CAACJ,WAAW,CAAC;MAE3CyC,UAAU,CAACxD,IAAI,CAACkB,SAAS,CAAC;IAC5B;IAEA,IAAIE,IAAI,GAAG,IAAIC,IAAI,CAACmC,UAAU,EAAE;MAAEnJ,IAAI,EAAEiJ;IAAW,CAAE,CAAC;IACtD,OAAOlC,IAAI;EACb;EAEAsC,QAAQA,CAAA;IACN,IAAItC,IAAI,GAAG,IAAI,CAACgC,SAAS,CAAC,IAAI,CAACrF,UAAU,EAAE,iBAAiB,CAAC;IAC7D,IAAI4F,CAAC,GAAGhC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IACnCD,QAAQ,CAACiC,IAAI,CAACC,WAAW,CAACF,CAAC,CAAC;IAC5B,IAAIrC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;IAC1CuC,CAAC,CAAC9B,IAAI,GAAGP,GAAG;IACZqC,CAAC,CAACG,QAAQ,GAAGC,MAAM,CAAC,6BAA6B,CAAC;IAClDJ,CAAC,CAAC5B,KAAK,EAAE;IACTR,MAAM,CAACC,GAAG,CAACwC,eAAe,CAAC1C,GAAG,CAAC;IAC/BqC,CAAC,CAACM,MAAM,EAAE;EACZ;EAEAlB,WAAWA,CAACmB,UAAkB;IAC5B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,IAAIG,aAAa,GAAG,IAAI,CAAC5G,QAAQ,CAAC6G,SAAS,CAACH,IAAI,EAAE,cAAc,CAAC;IACjE,OAAOE,aAAa;EACtB;EAAC,QAAAE,CAAA,G;qBA3NUlH,oBAAoB,EAAAzE,EAAA,CAAA4L,iBAAA,CAAAC,EAAA,CAAAC,kBAAA,GAAA9L,EAAA,CAAA4L,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAhM,EAAA,CAAA4L,iBAAA,CAAAK,EAAA,CAAAC,QAAA;EAAA;EAAA,QAAAC,EAAA,G;UAApB1H,oBAAoB;IAAA2H,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QChB7B1M,EADJ,CAAAC,cAAA,aAA0B,aACiB;QAAAD,EAAA,CAAAa,MAAA,mBAAY;QAAAb,EAAA,CAAAc,YAAA,EAAM;QACzDd,EAAA,CAAAY,SAAA,aAAwB;QAG6IZ,EAFrK,CAAAC,cAAA,aAAkB,aACS,iBAC0I,cAA2E;QAAhCD,EAAA,CAAAE,UAAA,mBAAA0M,mDAAA;UAAA5M,EAAA,CAAAK,aAAA,CAAAwM,GAAA;UAAA,OAAA7M,EAAA,CAAAU,WAAA,CAASiM,GAAA,CAAAhM,cAAA,CAAe,KAAK,CAAC;QAAA,EAAC;QAACX,EAAA,CAAAY,SAAA,cAAuE;QAAAZ,EAAA,CAAAC,cAAA,eAA2B;QAAAD,EAAA,CAAAa,MAAA,WAAG;QACjVb,EADiV,CAAAc,YAAA,EAAO,EAAM,EAAS,EACjW;QACNd,EAAA,CAAAC,cAAA,sBAA0I;QACtID,EAAA,CAAA+B,UAAA,KAAA+K,4CAAA,0BAA2C;QAInD9M,EADI,CAAAc,YAAA,EAAa,EACX;QAGFd,EADJ,CAAAC,cAAA,eAAsC,eACmB;QAAAD,EAAA,CAAAa,MAAA,cAAM;QAAAb,EAAA,CAAAc,YAAA,EAAM;QAsBjEd,EArBA,CAAA+B,UAAA,KAAAgL,2CAAA,yBAAgK,KAAAC,4CAAA,gCAAAhN,EAAA,CAAA2C,sBAAA,CAqBzI;QAO3B3C,EAAA,CAAAc,YAAA,EAAM;QAGEd,EAFR,CAAAC,cAAA,eAA0C,iBAC3B,sBACkB;QAqCrBD,EApCA,CAAA+B,UAAA,KAAAkL,wCAAA,sBAA+F,KAAAC,4CAAA,gCAAAlN,EAAA,CAAA2C,sBAAA,CAoClE;QAMjC3C,EAAA,CAAAc,YAAA,EAAa;QACbd,EAAA,CAAAC,cAAA,sBAAmC;QA8C/BD,EA7CA,CAAA+B,UAAA,KAAAoL,wCAAA,sBAAmH,KAAAC,4CAAA,gCAAApN,EAAA,CAAA2C,sBAAA,CA6C5E;QAM3C3C,EAAA,CAAAc,YAAA,EAAa;QACbd,EAAA,CAAAC,cAAA,sBAAmC;QA8C/BD,EA7CA,CAAA+B,UAAA,KAAAsL,wCAAA,sBAAmH,KAAAC,4CAAA,gCAAAtN,EAAA,CAAA2C,sBAAA,CA6C5E;QAWvD3C,EALY,CAAAc,YAAA,EAAa,EACL,EAGV,EACJ;QACNd,EAAA,CAAAC,cAAA,oBAAwJ;QAA9HD,EAAA,CAAAuN,gBAAA,2BAAAC,iEAAAC,MAAA;UAAAzN,EAAA,CAAAK,aAAA,CAAAwM,GAAA;UAAA7M,EAAA,CAAA0N,kBAAA,CAAAf,GAAA,CAAAzH,OAAA,EAAAuI,MAAA,MAAAd,GAAA,CAAAzH,OAAA,GAAAuI,MAAA;UAAA,OAAAzN,EAAA,CAAAU,WAAA,CAAA+M,MAAA;QAAA,EAAqB;QAC3CzN,EAAA,CAAAC,cAAA,SAAG;QACCD,EAAA,CAAAa,MAAA,ucAEJ;QACJb,EADI,CAAAc,YAAA,EAAI,EACG;;;;;;;QAxMSd,EAAA,CAAAe,SAAA,IAAmB;QAAqDf,EAAxE,CAAA0B,UAAA,UAAAiL,GAAA,CAAA3H,SAAA,CAAmB,iBAAiB,gBAAgB,mBAAmB,sBAAA2H,GAAA,CAAAnH,iBAAA,CAAwC;QAS9GxF,EAAA,CAAAe,SAAA,GAA+C;QAAAf,EAA/C,CAAA0B,UAAA,SAAAiL,GAAA,CAAA1K,YAAA,IAAA0K,GAAA,CAAA1K,YAAA,CAAAkF,MAAA,KAA+C,aAAAwG,YAAA,CAAa;QAgC3B3N,EAAA,CAAAe,SAAA,GAAoC;QAAAf,EAApC,CAAA0B,UAAA,SAAAiL,GAAA,CAAAzJ,eAAA,GAAAiE,MAAA,KAAoC,aAAAyG,kBAAA,CAAmB;QA4ClD5N,EAAA,CAAAe,SAAA,GAAyC;QAAAf,EAAzC,CAAA0B,UAAA,SAAAiL,GAAA,CAAA7I,sBAAA,CAAAqD,MAAA,KAAyC,aAAA0G,4BAAA,CAA6B;QAqDtE7N,EAAA,CAAAe,SAAA,GAAyC;QAAAf,EAAzC,CAAA0B,UAAA,SAAAiL,GAAA,CAAAnI,sBAAA,CAAA2C,MAAA,KAAyC,aAAA2G,4BAAA,CAA6B;QAyD7C9N,EAAA,CAAAe,SAAA,GAA2B;QAA3Bf,EAAA,CAAA+N,UAAA,CAAA/N,EAAA,CAAAgO,eAAA,KAAAC,GAAA,EAA2B;QAArFjO,EAAA,CAAAkO,gBAAA,YAAAvB,GAAA,CAAAzH,OAAA,CAAqB;QAAqFlF,EAApF,CAAA0B,UAAA,gBAAA1B,EAAA,CAAAgO,eAAA,KAAAG,GAAA,EAAmC,oBAAgD,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}