<div class="buy-package">
    <div id="class"
        style="background-image: linear-gradient(to left, #7588f4, #a0a4f7, #c3c1fa, #e3dffc, #ffffff); border-radius: 14px;">
        <div *ngIf="!inTrial" class="buy-package-header">
            <button (click)="goBack()" type="button"
                class="p-element py-1 p-button-outlined p-button-rounded button-outlined p-button-sm p-button p-component ng-star-inserted">
                <div class="flex align-items-center gap-1"><span
                        class="p-datepicker-prev-icon pi pi-chevron-left"></span><span
                        class="text-base s12-16">Back</span></div>
            </button>
            <div class="buy-package-title font-lg font-semibold">
                Buy Package
            </div>
            <div *ngIf="currentStep!=4" class=steps>
                <div class="package-step">
                    {{currentStep}}/3
                </div>
            </div>
        </div>
        <div *ngIf="inTrial">
            <div class="white-button" (click)="trialSection()">
                Request new Trial
            </div>
        </div>


        <div *ngIf="!hideSection" [@slideInOut] class="section">

            <div *ngIf="currentStep==1" class="left pt-0" [style.width]="inTrial ? '100%': ''">
                <div *ngIf="!inTrial">
                    <div class="title my-2 font-sm">
                        Select Classroom
                    </div>
                    <div class="top-btns my-0">
                        <div class="btn btn-new btn-classroom new mb-2 mt-0 hvr-glow col-6 lg:col-3 hvr-glow"
                            id="package-new" (click)="buyNew()">
                            <span class="flex-column">NEW <span class="btn-teacher-name font-2xs m-0"
                                    style="min-height: auto;color: #3345a7;">language</span></span>
                        </div>
                        <div *ngFor="let classroom of userClassrooms"
                            class="btn btn-classroom hvr-glow col-6 lg:col-3 mb-2 mt-0 hvr-glow"
                            [ngStyle]="getBtnStyle(true)" id="package-btn-{{classroom.id}}"
                            (click)="selectClassroom(classroom)">
                            <span class="btn-language font-sm lang capitalize">
                                {{classroom.language}}
                            </span>
                            <br>
                            <div class="btn-teacher-name font-2xs" id="package-btn-teacher-{{classroom.id}}">
                                with {{classroom.teacher.firstName}} {{classroom.teacher.lastName | slice:0:1}}.
                            </div>
                        </div>
                    </div>
                </div>
                <div [@slideInOut] *ngIf="!hideSection" [style.marginTop]="inTrial ? '30px' : '0'">
                    <div class="title font-sm mt-1">
                        Select Language
                    </div>
                    <div class="top-btns my-0">
                        <div *ngFor="let language of mltLanguages" class="btn btn-new btn-language p-2 w-4rem"
                            [ngStyle]="getBtnStyle(false)" id="language-btn-{{language.code}}"
                            (click)="selectLanguage(language)">
                            {{language.code}}
                        </div>
                    </div>
                </div>
                <div [@slideInOut] *ngIf="!hideSection">
                    <div class="title font-sm">
                        Select Level
                    </div>
                    <div class="top-btns my-0">
                        <div *ngFor="let level of mltLevels" class="btn btn-new btn-level level"
                            id="level-btn-{{level}}" (click)="selectLevel(level)" [ngClass]="{'level-default': compareLevels(level, selectedLevel) >= 0, 'level-disabled': compareLevels(level, selectedDefaultLevel) < 0 && !isNew, 'level-selected': level == selectedLevel,
                            'hidden': level == 'NT2.1' || level == 'NT2.2' || level == 'TBD' || level == 'Any'}">
                            <div [matTooltip]="(level == 'BS') ? 'Business':''">
                                <div (click)="selectLevel(level)">{{level}}</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div *ngIf="selectedLanguage.code == 'DUT'">
                    <div class="top-btns my-0">
                        <div *ngFor="let level of mltLevels" class="btn btn-new btn-level level"
                            id="level-btn-{{level}}" (click)="selectLevel(level)" [ngClass]="{'level-default': compareLevels(level, selectedLevel) >= 0, 'level-disabled': compareLevels(level, selectedLevel) < 0, 'level-selected': level == selectedLevel,
                        'hidden': level !== 'NT2.1' && level !== 'NT2.2'}">
                            <div>
                                {{level}}
                            </div>
                        </div>
                    </div>
                </div>
                <div [@slideInOut] *ngIf="!hideSection">
                    <div class="title font-sm mt-1">
                        Select Number of Students
                    </div>
                    <div class="top-btns my-0 justify-content-center gap-2">
                        <div *ngFor="let number of numberOfStudents" class="btn py-2 col-2 btn-new btn-language"
                            [ngStyle]="getBtnStyle(false)" id="number-btn-{{number}}" (click)="selectNumber(number)">
                            {{number}}
                        </div>
                    </div>
                </div>
                <div *ngIf="!inTrial">
                    <div class="title font-sm mt-1">
                        Select Hours
                    </div>
                    <div class="top-btns top-btns-hours m-0 notranslate">
                        <div *ngFor="let hour of mltPricesHourly; let i = index" class="btn btn-hours hvr-glow"
                            [ngStyle]="getBtnStyle(true)" id="hour-btn-{{hour.price}}" (click)="selectHours(hour, i)">
                            <div class="hours-info font-xs" id="hour-btn-hours-{{hour.price}}">
                                {{hour.hours}} hours
                            </div>
                            <div class="price font-semibold">
                                {{hour.price}}&euro;
                            </div>
                            <div class="hours-info font-xs" id="hour-btn-per-{{hour.price}}">
                                {{hour.perHour}}&euro; / hour
                            </div>
                        </div>
                    </div>
                </div>
                <div [@slideInOut] *ngIf="!hideSection && inTrial" class="btns">
                    <div class="main-color-button hvr-glow"
                        style="background: linear-gradient(#a4a2e6 0%, #4895ef 100%);" (click)="sendRequestTrial()">
                        Send Request for the Trial
                    </div>
                </div>
                <div *ngIf="!inTrial">
                    <div class="title font-sm">
                        Select Package Type
                    </div>
                    <div class="grid justify-content-around packages">
                        <div *ngFor="let package of defaultPackages; let i = index" class="package-type lg:w-11rem p-0"
                            [style.borderColor]="getCasualColor(package.type)">
                            <div class="package-type-section">
                                <div class="type-name relative"
                                    [style.background]="i === 0 ? 'linear-gradient(to right, #122171, #353792, #544eb3, #7366d6, #927ffa)' : i === defaultPackages.length - 1 ? 'linear-gradient(to right, #122171, #004b99, #0074bb, #009ed7, #4dc8f0)' : 'linear-gradient(to left, #8497ff, #6977da, #4e59b6, #323c93, #122171)'">
                                    {{package.type}}
                                    <div class="my-radio absolute"
                                        [ngClass]="{'selected': package.type == selectedPackageToBuy.type}"
                                        (click)="selectPackage(package)">
                                    </div>
                                </div>
                                <div class="package-type-section py-2 border-noround">
                                    <div>
                                        <strong>
                                            {{package.costPlus !== 0 ? '+' + package.costPlus : '- '}}&euro;
                                        </strong>
                                    </div>
                                </div>
                                <div class="package-info p-3 font-sm">
                                    <div class="flex align-items-start">
                                        <img src="/assets/images/dashboard/check.svg" alt="" class="w-1rem mr-1">
                                        <span *ngIf="package.type=='Regular'">Expires in {{expiresIn}} months</span>
                                        <span *ngIf="package.type!='Regular'">Expires in +{{package.expiresPlus}}</span>
                                    </div>
                                    <div class="flex align-items-start my-1">
                                        <img src="/assets/images/dashboard/check.svg" alt="" class="w-1rem mr-1">
                                        <span>{{package.cancelation}} hours cancellation policy</span>
                                    </div>
                                    <div class="flex align-items-start my-1">
                                        <ng-container *ngIf="package.type!='Regular' else elseAiChatIconBlock">
                                            <img src="/assets/images/dashboard/check.svg" alt="" class="w-1rem mr-1">
                                        </ng-container>
                                        <ng-template #elseAiChatIconBlock>
                                            <img src="/assets/images/dashboard/package-tick-error.png" alt=""
                                                class="w-1rem mr-1">
                                        </ng-template>
                                        <span>AI Chat</span>
                                    </div>
                                    <div *ngIf="package.type!='Regular' && package.pause"
                                        class="flex align-items-start">
                                        <img src="/assets/images/dashboard/check.svg" alt="" class="w-1rem mr-1">
                                        <span>{{package.pause}} pause available</span>
                                    </div>
                                </div>
                                <div *ngIf="package.type == selectedPackageToBuy.type"
                                    class="package-type-section py-1 flex flex-row justify-content-center custom-border">
                                    <img src="/assets/icons/check-blue.svg" alt="" class="w-1rem mr-1">
                                    <span class="font-sm font-semibold">Package selected</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div *ngIf="currentStep==2" class="left">

                <!-- <li class="flex flex-column md:flex-row md:align-items-center md:justify-content-between p-3 border-1 mb-3 bg-green-50 border-green-500" style="border-radius: 10px;"><div><div><span class="inline-flex justify-content-center align-items-center w-2rem h-2rem border-circle border-1 border-green-200"><i class="pi pi-file text-green-600"></i></span><span class="text-green-700 font-bold ml-2">Dianne Russell</span></div><p class="text-green-700 mt-2 mb-0">Due <span class="font-medium">21/10/2021</span></p></div><div class="flex align-items-center justify-content-between md:justify-content-end mt-3 md:mt-0"><span class="bg-green-400 text-green-900 font-bold text-sm py-1 px-2" style="border-radius: 10px;">PAID</span><div class="text-right ml-3"><span class="text-green-700 font-bold">82.50</span><p class="mt-1 mb-0 text-green-700">EUR</p></div></div></li> -->

                @if (hasSwitchedPackage()) {
                <div
                    class="border-1 bg-blue-50 border-blue-500 border-round p-1 sm:p-3 mb-2 flex flex-row align-items-center z-1">
                    <i class="pi pi-check-circle price-blue text-2xl md:text-4xl mb-2 md:mb-0 mr-2 md:mr-3"></i>
                    <div>
                        <div class="text-900 font-medium mb-1">Great Choice!</div><span class="text-600 text-sm">
                            @if (selectedPackageToBuy.type==='Flexible') {
                            The Flexible Package ensures you have the time and flexibility you need.
                            } @else {
                            You’re all set with the extra benefits of the Premium Package.
                            }
                        </span>
                    </div>
                </div>
                }


                @if (selectedPackageToBuy?.type !== 'Premium' && !hasSwitchedPackage()) {
                <ng-container>
                    <app-buy-package-suggestion-box [preselectedPackage]="selectedPackageToBuy"
                        (switchBackToPackageTypeSelected)="switchBackToPackageType($event)"></app-buy-package-suggestion-box>
                </ng-container>
                }


                <form [formGroup]="form">
                    <div class="input-fields">
                        <div class="input-field font-semibold mb-3">
                            Select Billing Type
                        </div>
                        <div class="input-field">
                            <div class="flex flex-wrap gap-3">
                                <div class="flex align-items-center">
                                    <p-radioButton name="billing" value="receipt" [(ngModel)]="selectedBillingType"
                                        inputId="receipt" (click)="chooseReceipt()"
                                        [ngModelOptions]="{standalone: true}"></p-radioButton>
                                    <label for="receipt" class="ml-2 font-sm">Receipt</label>
                                </div>

                                <div class="flex align-items-center">
                                    <p-radioButton name="billing" value="invoice" [(ngModel)]="selectedBillingType"
                                        inputId="invoice" (click)="chooseInvoice()"
                                        [ngModelOptions]="{standalone: true}"></p-radioButton>
                                    <label for="invoice" class="ml-2 font-sm">Invoice</label>
                                </div>
                            </div>
                        </div>
                        <div class="input-field font-sm">
                            <div class="input-element-title">* First Name</div>
                            <input class="input-element" formControlName="fname" type="text">
                            <div *ngIf="(isSubmitted || errorControl.fname.touched) && errorControl.fname.errors?.required"
                                class="input-error">* First Name is required</div>
                        </div>
                        <div class="input-field font-sm">
                            <div class="input-element-title">* Last Name</div>
                            <input class="input-element" formControlName="lname" type="text">
                            <div *ngIf="(isSubmitted || errorControl.lname.touched) && errorControl.lname.errors?.required"
                                class="input-error">* Last Name is required</div>
                        </div>
                        <div class="input-field font-sm">
                            <div class="input-element-title">Company (optional)</div>
                            <input class="input-element" formControlName="company" type="text">
                        </div>
                        <div class="input-field font-sm">
                            <div class="input-element-title">Profession (optional)</div>
                            <input class="input-element" formControlName="profession" type="text">
                        </div>
                        <div *ngIf="isInvoice" class="input-field font-sm">
                            <div class="input-element-title">* T.I.N. / V.A.T.</div>
                            <input class="input-element" formControlName="tin" type="text">
                            <div *ngIf="(isSubmitted || errorControl.tin.touched) && errorControl.tin.errors?.required"
                                class="input-error">* T.I.N. / V.A.T. is required</div>
                        </div>
                        <div *ngIf="isInvoice" class="input-field font-sm">
                            <div class="input-element-title">Tax Office</div>
                            <input class="input-element" formControlName="tax" type="text">
                            <div *ngIf="(isSubmitted || errorControl.tax.touched) && errorControl.tax.errors?.required"
                                class="input-error">* Tax Office is required</div>
                        </div>
                        <div class="input-field font-sm fwidth">
                            <div class="input-element-title ">* Country</div>
                            <app-prime-input-dropdown [parentForm]="form" [countries]="countries" optionLabel="name"
                                filterBy="name" [inputName]="'country'" [withFlags]="true" flagFilter="name"
                                [placeholder]="'Country of Residence'" [selectedItemValue]="selectedResidenceCountry"
                                templateValue="name">
                            </app-prime-input-dropdown>
                            <div *ngIf="(isSubmitted || errorControl.country.touched) && errorControl.country.errors?.required"
                                class="input-error">* Country is required</div>
                        </div>
                        <div *ngIf="isReceipt" style="width:100%"></div>
                        <div class="input-field font-sm">
                            <div class="input-element-title">* Email</div>
                            <input class="input-element" formControlName="email" type="text">
                            <div *ngIf="(isSubmitted || errorControl.email.touched) && errorControl.email.errors?.required"
                                class="input-error">* Email is required</div>
                        </div>
                        <div class="input-field font-sm">
                            <div class="input-element-title">* Phone</div>
                            <span class=" flex sm:flex">
                                <app-prime-input-dropdown optionLabel="code" [parentForm]="form"
                                    [countries]="phoneCodes" filterBy="code" [filter]="true" [inputName]="'phoneCode'"
                                    [placeholder]="'select'" [withFlags]="true" flagFilter="code" templateValue="code"
                                    styleClass="dropdown-blue white-bg phone-code-input tiny rounded-less small-dropdown-items"
                                    (valueSelected)="onPhoneCodeChange($event)" [selectedItemValue]="selectedPhoneCode">
                                </app-prime-input-dropdown>
                                <span class="p-float-label w-full">
                                    <input type="text"
                                        class="input-blue input-element rounded-less white-bg no-radius-left w-full"
                                        formControlName="phone">
                                </span>
                            </span>
                            <div *ngIf="(isSubmitted || errorControl.phone.touched) && errorControl.phone.errors?.required"
                                class="input-error">* Phone is required</div>
                        </div>
                        <div class="input-field font-sm">
                            <div class="input-element-title">* Street Address</div>
                            <input class="input-element" formControlName="street" type="text">
                            <div *ngIf="(isSubmitted || errorControl.street.touched) && errorControl.street.errors?.required"
                                class="input-error">* Street Adrdess is required</div>
                        </div>
                        <div class="input-field font-sm">
                            <div class="input-element-title">* Street No</div>
                            <input class="input-element" formControlName="number" type="text">
                            <div *ngIf="(isSubmitted || errorControl.number.touched) && errorControl.number.errors?.required"
                                class="input-error">* Street Number is required</div>
                        </div>
                        <div class="input-field font-sm">
                            <div class="input-element-title">* City</div>
                            <input class="input-element" formControlName="city" type="text">
                            <div *ngIf="(isSubmitted || errorControl.city.touched) && errorControl.city.errors?.required"
                                class="input-error">* City is required</div>
                        </div>
                        <div class="input-field font-sm">
                            <div class="input-element-title">* Postcode</div>
                            <input class="input-element" formControlName="postcode" type="text">
                            <div *ngIf="(isSubmitted || errorControl.postcode.touched) && errorControl.postcode.errors?.required"
                                class="input-error">* Postcode is required</div>
                        </div>

                    </div>
                </form>
            </div>
            <div *ngIf="currentStep==3" class="left w-full">
                <div class="section-step-3">
                    <div class="section-step-3-title">
                        <span *ngIf="isInvoice">Invoice</span><span *ngIf="isReceipt">Receipt</span> Details
                        <div class="toggle-section"
                            (click)="toggleSection(receiptDetailsSection, receiptDetailsArrowSrc)">
                            <img #receiptDetailsArrowSrc src="/assets/icons/toogle-section.svg" class="section-arrow">
                        </div>
                    </div>
                    <div #receiptDetailsSection [attr.id]="'receipt-section'" [attr.open]="true" class="section-content"
                        style="overflow: inherit">
                        <div class="review">
                            <div class="review-section">
                                <div class="review-section-title font-sm font-semibold">
                                    Personal Info
                                </div>
                                <div class="review-section-content mt-1 font-sm">
                                    <div class="col-50">
                                        <div>
                                            First Name
                                        </div>
                                        <div class="info">
                                            {{buyerUserDetails.fname}}
                                        </div>
                                    </div>
                                    <div class="col-50">
                                        <div>
                                            Last Name
                                        </div>
                                        <div class="info">
                                            {{buyerUserDetails.lname}}
                                        </div>
                                    </div>
                                    <div class="col-50">
                                        <div>
                                            Company Title
                                        </div>
                                        <div class="info">
                                            {{buyerUserDetails.company}}
                                        </div>
                                    </div>
                                    <div class="col-50">
                                        <div>
                                            Proffesion
                                        </div>
                                        <div class="info">
                                            {{buyerUserDetails.profession}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="review-section">
                                <div class="review-section-title font-sm font-semibold">
                                    Location
                                </div>
                                <div class="review-section-content mt-1 font-sm">
                                    <div class="col-25">
                                        <div>
                                            Street address
                                        </div>
                                        <div class="info">
                                            {{buyerUserDetails.streetName}}
                                        </div>
                                    </div>
                                    <div class="col-25">
                                        <div>
                                            Street No
                                        </div>
                                        <div class="info">
                                            {{buyerUserDetails.streetNumber}}
                                        </div>
                                    </div>
                                    <div class="col-25">
                                        <div>
                                            Country
                                        </div>
                                        <div class="info">
                                            {{buyerUserDetails.country}}
                                        </div>
                                    </div>
                                    <div class="col-25">
                                        <div>
                                            City
                                        </div>
                                        <div class="info">
                                            {{buyerUserDetails.city}}
                                        </div>
                                    </div>
                                    <div class="col-25">
                                        <div>
                                            Postcode
                                        </div>
                                        <div class="info">
                                            {{buyerUserDetails.postcode}}
                                        </div>
                                    </div>
                                    <div class="col-25" *ngIf="isInvoice">
                                        <div>
                                            Tax Office
                                        </div>
                                        <div class="info">
                                            {{buyerUserDetails.tax}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="review-section">
                                <div class="review-section-title font-sm font-semibold">
                                    Contact Info
                                </div>
                                <div class="review-section-content mt-1 font-sm">
                                    <div class="col-50">
                                        <div>
                                            E-mail
                                        </div>
                                        <div class="info">
                                            {{buyerUserDetails.email}}
                                        </div>
                                    </div>
                                    <div class="col-25">
                                        <div>
                                            Telephone
                                        </div>
                                        <div class="info">
                                            {{buyerUserDetails.phone}}
                                        </div>
                                    </div>
                                    <div *ngIf="isInvoice" class="col-50 sm:mt-1">
                                        <div>
                                            *T.I.N. / V.A.T
                                        </div>
                                        <div class="info">
                                            {{buyerUserDetails.tin}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="font-base py-3">Terms & Conditions</div>
                <p class="font-sm">Your personal data will be used to process your order, support your experience
                    throughout this website, and for other purposes described in our privacy policy.</p>
                <div class="section-step-3">
                    <div class="section-step-3-title">
                        Read Full Terms & Conditions
                        <div class="toggle-section" (click)="toggleSection(termsSection, termsArrowSrc)">
                            <img #termsArrowSrc src="/assets/icons/toogle-section.svg" class="section-arrow">
                        </div>
                    </div>
                    <div class="section-content fixed-height" #termsSection [attr.id]="'receipt-section-details'"
                        [attr.open]="true" style="overflow-y: scroll; height: 200px!important;">
                        <div class="review">
                            <div class="review-section">
                                <div class="review-section-content">
                                    <div class="col-100">
                                        <div class="info font-xs">
                                            <div class="font-semibold">Terms and Conditions</div>
                                            <br>
                                            Welcome to MyLingoTrip, the Online Language School to learn languages
                                            (Dutch, English, Italian, Greek, German, French, Swedish, Spanish, Korean,
                                            Chinese, Japanese, Russian). This agreement (the “Agreement”) represents a
                                            part of the contract between you and MyLingoTrip and, if applicable, the
                                            fee-based subscription services therein.
                                            <br>
                                            By registering, purchasing a gift subscription from, or using MyLingoTrips’
                                            services, you agree to accept the terms and conditions which govern access
                                            and your use of the mylingotrip.com website. The terms and conditions
                                            constitute a legal arrangement between you, as a user of our services and
                                            MyLingoTrip for the services that are offered through the website. Users of
                                            the MyLingoTrip website are expected to conform, without qualification to
                                            the listed terms and conditions of use set out below in all circumstances.
                                            <br>
                                            Acceptance of the Terms
                                            Changes to these Terms
                                            General User Responsibilities (Use of Service)
                                            Access and Interference
                                            Student Registration
                                            Students – Booking and Payment Policy
                                            Students – Fees and Cancellation Policy
                                            Indemnification
                                            Language Teachers – Compliance with Booking Procedure
                                            Language Teachers – Delivery of Language Training Services and Registration
                                            Your rights
                                            Privacy and Security Policy
                                            Impart Among Users and Termination of Account
                                            Applied Laws, Regulations, and Jurisdiction
                                            Miscellaneous
                                            1. Acceptance of the Terms
                                            By using the information, features, functionality, and tools located at
                                            MyLingoTrip website (with or without the MyLingoTrip “service” or
                                            “services”), you agree to conform to this Arrangement and the Terms,
                                            regardless of being either a “visitor” (meaning you merely browse the
                                            MyLingoTrip’ website) or a “customer” (meaning you have registered with
                                            MyLingoTrip). The term “you” or “user” refers to a visitor or a customer.
                                            <br>
                                            The term “we” refers to MyLingoTrip. The term “material” relates to all
                                            class material used in our classes. If you wish to become a customer, you
                                            could communicate your wish with other customers and/or make use of our
                                            service/services. However, you have to read MyLingoTrip’s terms and
                                            conditions and indicate your acceptance during the registration process.
                                            <br>
                                            If you do not agree with all the terms and conditions, do not use the
                                            MyLingoTrip website and/or services.
                                            <br>
                                            Please review all the terms and conditions thoroughly before using the
                                            MyLingoTrip website and or/services.
                                            <br>
                                            2. Changes to these Terms
                                            MyLingoTrip has the right to modify this Agreement and any policies
                                            affecting or relating to the Site. Your continued use of the Site following
                                            notice of the Agreement modification will be considered an acceptance of the
                                            modified terms and conditions of use, although, if major implications are
                                            applied, you will be asked for providing your electronic signature. Your
                                            alternative upon dissatisfaction with the modified terms of use is to cancel
                                            your subscription. Moreover, Mylingotrip reserves the right to modify,
                                            suspend or discontinue the Site at any time.
                                            <br>
                                            3. General User Responsibilities
                                            All registered users agree not to post, distribute, publish or by any other
                                            means convey offensive material, including discourteous language,
                                            photographic material, or any other kind of material of an offensive or
                                            sexual nature. Violation of this agreement conditions by any user will
                                            result in termination of that users’ registration and will result in
                                            notifying the authorities and possibly taking legal action.
                                            <br>
                                            All registered users agree not to post, distribute, publish, or by any other
                                            means place any computer code on the MyLingoTrip website, which directly or
                                            indirectly links to another Site without our express consent.
                                            <br>
                                            All registered users agree not to distribute or share their username and /or
                                            password details with any other individual or company to provide to other
                                            individuals the permission to utilize the services provided through the
                                            MyLingoTrip website.
                                            <br>
                                            All registered users self-certify that they can form legally binding
                                            contracts under their applicable regime of their country of residence.
                                            <br>
                                            4. Access and Interference
                                            With your registration at MyLingoTrip website you agree that you will not:
                                            <br>
                                            *Use any robot, spider, scraper, deep link or other similar automated data
                                            gathering or extraction tools, programs, algorithms or methods to get access
                                            to, acquire, copy or monitor mylingotrip.com or any part of mylingotrip.com,
                                            without MyLingoTrips’ express written consent, which will be withheld in
                                            MyLingoTrips’ sole discretion;
                                            <br>
                                            *Use or attempt to use any engine, software, tool, agent, or other device or
                                            mechanism (including without limitation browsers, spiders, robots, avatars
                                            or intelligent agents) to navigate or search the MyLingoTrip website other
                                            than the search engines and search agents available through the service and
                                            other than generally available third-party web browsers (such as Microsoft
                                            Explorer);
                                            <br>
                                            *Post or transmit any file containing viruses, worms, Trojan horses or any
                                            other contaminating or malicious software/features, or whatever might
                                            interfere with the proper functioning of mylingotrip.com and/or its
                                            services;
                                            <br>
                                            *Attempt to decipher, decompile, disassemble, or reverse-engineer any of the
                                            software incorporating or in any way constituting a part of mylingotrip.com
                                            and/or its services.
                                            <br>
                                            5. Student Registration
                                            When registering as a student at MyLingoTrip, you will be required to
                                            provide personal information not limited to your preferred username and
                                            password; your email address; the language of your choice; your country of
                                            residence; your local currency and local time zone. By registering an
                                            account at mylingotrip.com, you automatically confirm that you accept the
                                            terms and conditions of use. All language students agree to the use of their
                                            personal information following the web site privacy policy.
                                            <br>
                                            6. Students – Booking and Payment Policy
                                            After providing to the website of MyLingoTrip the requested amount of fees,
                                            the student should provide the remaining amount (the price of the total
                                            study-package minus the websites’ fees) to his/her assigned teacher.
                                            MyLingoTrip is obliged to issue the student’s receipt only for the fees that
                                            are provided to the platform (mylingotrip.com). The remaining amount which
                                            is provided by the student to his/her teacher is the teacher’s fee, and
                                            there is no involvement of the MyLingoTrip platform in providing the receipt
                                            that corresponds to this amount to the student.
                                            <br>
                                            Any action or attempt to avoid providing these fees according to the
                                            MyLingoTrip booking policy or to arrange or attempt to arrange lessons
                                            outside the MyLingoTrip platform is strictly prohibited and will result in
                                            closing the student’s account, and legal action might be taken for the
                                            recovery of the amount of money deemed owing. We reserve the right to amend
                                            or alter these fees at any time. All prices displayed publicly on
                                            mylingotrip.com are inclusive of all fees.
                                            <br>
                                            7. Students – Fees and Cancellation Policy
                                            You will be required to register and create an account with MyLingoTrip (the
                                            “Account”) to have access to the website’s services. Information gathered
                                            through the registration process and information related to your account
                                            will be subject to these terms as well as to our “Privacy and Security
                                            Policy.” You warrant that the information provided for the creation of a
                                            student’s account is truthful, accurate, and complete and that this
                                            information will stay truthful, accurate, and complete as long as you are
                                            registered at the MyLingoTrip website.
                                            <br>
                                            Certain material and functionalities of the website are available only after
                                            the purchase of an online language package (the 3 “Package” options).
                                            According to this Agreement, you are responsible for any charges associated
                                            with registering to the website. MyLingoTrip reserves the right to change
                                            the price of the actual package fees or institute new fees at any time, upon
                                            reasonable notice posted on the website and emailed to current customers.
                                            <br>
                                            By purchasing a Study Package, you agree to be bound to the following terms:
                                            <br>
                                            MyLingoTrip ensures that every credit card/PayPal transaction you make is
                                            100% secure. If you wish to check on the status of your subscription, please
                                            log in and visit the “My Account” page in your Login area.
                                            <br>
                                            All language courses fees are recurring and non-refundable after your
                                            purchase. If you do not wish to continue studying at MyLingoTrip, you have
                                            to deactivate your account at the “My Account” page at your login area or
                                            provide us with a written (email) notice.
                                            <br>
                                            An exception to the prior paragraph is our “7-day money-back guarantee”.
                                            Within seven days after the purchase of a study package, you have the right
                                            to cancel your language course and receive a full refund by providing us
                                            with a written (email) notice.
                                            <br>
                                            MyLingoTrip is obliged to issue the student’s receipt only for the amount
                                            provided to the platform (mylingotrip.com). The remaining amount, provided
                                            by the student/parent to the teacher, is the Language Tutor’s fee, and there
                                            is no involvement.
                                            <br>
                                            Lesson packages booked and not used within three months will be canceled.
                                            Thus, if those remaining hours have not been used within three months after
                                            the date a student/parent purchased a class package, those unused sessions
                                            are lost and will not be refunded. Moreover, when a student chooses to
                                            conduct only one (1) session weekly, then the activation period expands to
                                            five (5) months. As a result, MyLingoTrip will not refund these particular
                                            students, only after their expiration date is due.
                                            <br>
                                            In case of a cancellation: A scheduled lesson with a teacher has to be
                                            canceled at least 48 hours before the actual appointment for the lesson.
                                            Otherwise, the arranged hour is going to be deducted from the purchased
                                            lesson-package, without any further announcement or refund.
                                            <br>
                                            For groups of 2 or 3: In case one of the students cancels the lesson but the
                                            other one attends it, the hour will be charged as usual and no extra private
                                            lesson will be provided for the student who missed the lesson.
                                            <br>
                                            Free Trial Lesson:
                                            The free trial is available only to first-time users of the MyLingoTrip
                                            website.
                                            <br>
                                            8. Indemnification
                                            You agree to indemnify, protect and do not cause any misconduct to the
                                            MyLingoTrip platform and it’s affiliates, partners, and employees due to any
                                            accountability, alleges, costs, or damages arising from your use of the
                                            MyLingoTrip website and/or your breach of this Agreement, under the terms
                                            and conditions set out herein.
                                            <br>
                                            9. Language Teachers – Compliance with Booking Procedure
                                            All registered language tutors agree not to engage in or promote any
                                            activity to arrange lessons with a language student outside the MyLingoTrip
                                            website. The activity of this nature is specifically prohibited under our
                                            terms and conditions of service and undermines the trust, security, and
                                            legitimacy of MyLingoTrip.
                                            <br>
                                            Activity specifically not permitted includes:
                                            <br>
                                            Communication via email, Skype, or other means to schedule and arrange a
                                            lesson with a language student in a way that is intended to avoid the
                                            payment of the booking fee to the MyLingoTrip website.
                                            <br>
                                            Failure of a registered language tutor to adhere to these terms and
                                            conditions and in particular any action whatsoever taken by a registered
                                            language teacher to privately arrange tuition time with a registered
                                            MyLingoTrip language student, avoiding to provide the booking fee to the
                                            MyLingoTrip website, may result in the termination of the cooperation
                                            between the MyLingoTrip platform and the language teacher that committed
                                            this action, as well as excluding the particular language student from
                                            his/her rights to use the MyLingoTrip website again and/or legal action
                                            against that language tutor and/or the language student and/or his/her
                                            parent might be taken.
                                            <br>
                                            10. Language Teachers – Delivery of Language Training Services and
                                            Registration
                                            All registered MyLingoTrip language teachers agree to provide high-quality
                                            language training services to any registered MyLingoTrip language student in
                                            a timely fashion. We reserve the right to change the required standard at
                                            any time. When registering as a language teacher you will be required to
                                            provide personal information. You will also be requested to inform the
                                            MyLingoTrip administrative team about your Skype username and password (All
                                            users acknowledge that mylingotrip.com is not responsible for the quality of
                                            the Skype interface or the quality of communications between users using
                                            Skype).
                                            <br>
                                            11. Your Rights
                                            MyLingoTrip offers you a non-transferable, non-exclusive, definite right to
                                            access, use and array the content of this platform, provided that you comply
                                            with this Agreement and the Terms as set out in full. The materials of this
                                            Site are provided privately to you for your non-commercial use. Specific
                                            services of this Site are available only to registered customers of
                                            MyLingoTrip.
                                            <br>
                                            By purchasing a subscription or register for a Free Trial Lesson of
                                            MyLingoTrip, you agree to be bound by the terms and conditions as set out in
                                            full.
                                            <br>
                                            12. Privacy and Security Policy
                                            Your use of our Site and any personal information or other information about
                                            you collected by mylingotrip.com through, or in congruence with, the Site is
                                            subject to our Privacy and Security Policy. For questions about our online
                                            privacy and security policy please refer to our administration team.
                                            <br>
                                            13. Impart Among Users and Termination of Account
                                            MyLingoTrip reserves the right to monitor communications between users. All
                                            users consent to the supervision of communication, via any of the functions
                                            we make available (i.e., Skype, Notes, G-mail) to protect the integrity and
                                            security of mylingotrip.com, protect users from abuse, detecting fraud and
                                            attempts to avoid our fee structure.
                                            <br>
                                            Language tutors may close their account if they notify MyLingoTrip’s
                                            administration team at least three (3) months prior to the breaking of
                                            engagement. They are subject to the completion of any overdue or unresolved
                                            lessons, and only in case MyLingoTrip’s administration team and their
                                            students are notified they can close their accounts. MyLingoTrip will take
                                            further actions for transferring the students to another Language Tutor
                                            matching their needs and availability slots.
                                            <br>
                                            Language students may close their account at any time but shall remain
                                            accountable to MyLingoTrip and any language tutor for any unpaid lesson
                                            fees.
                                            <br>
                                            14. Applied Laws, Regulations, and Jurisdiction
                                            These terms and conditions shall be regulated in all respects by the laws of
                                            Greece without affecting any principle that may require the application of
                                            the law of another jurisdiction. The arrangements of this agreement are
                                            severable and you agree that any dispute or claim you may have against
                                            MyLingoTrip must be resolved by a court located in Greece. Hence, you
                                            herewith submit to the personal jurisdiction of the courts located in Greece
                                            for prosecuting or litigating such disputes or claims. Any cause of action
                                            you may have in regards to MyLingoTrip must be commenced within one month
                                            after it arises, or the cause of action is repudiated.
                                            <br>
                                            15. Miscellaneous
                                            If any portion of this Agreement is deemed unlawful, void or unenforceable
                                            by any arbitrator or court of competent jurisdiction, this Agreement as a
                                            whole shall not be deemed unlawful, void or unenforceable, but only that
                                            portion of this Agreement that is unlawful, void or unenforceable shall be
                                            stricken from this Agreement. You agree that if MyLingoTrip does not
                                            exercise or enforce any legal right or remedy which is contained in the
                                            Agreement (or which MyLingoTrip has the benefit, according to any applicable
                                            law), this will not be taken to be a formal waiver of MyLingoTrips’ rights
                                            and that those rights or remedies will still be available to MyLingoTrip.
                                            <br>
                                            All covenants, agreements, representations, and warranties made in this
                                            Agreement shall survive your acceptance of this Agreement and the
                                            termination of this Agreement.
                                            <br>
                                            This Agreement represents the entire understanding and agreement between the
                                            recipient and MyLingoTrip and supersedes and replaces all oral or written
                                            agreements concerning the subject matter hereof.
                                            <br>
                                            In case you may have any questions about this policy statement, or about any
                                            of MyLingoTrips’ other policies and practices, please contact us on the
                                            contact details laid out at mylingotrip.com.
                                            <br>
                                            MyLingoTrip and related services are, if not otherwise explicitly mentioned,
                                            supplied by:
                                            <br>
                                            MyLingoTrip (MLT)
                                            <br>
                                            Telephone Number: 231 5551054
                                            <br>
                                            Contact us: contact&#64;mylingotrip.com
                                            <br>
                                            IT Support: support&#64;mylingotrip.com
                                            <br>
                                            Address: 5 Papandreou Georgiou St.,
                                            54645 Thessaloniki, Greece
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="review-section" style="font-size:15px; margin: 20px 0; font-weight: bold;">
                    <p-checkbox label="I have read and agree to the website terms and conditions *"
                        class="terms-checkbox" name="groupname" value="val1" [(ngModel)]="selectedValues"
                        (onChange)="agree()"></p-checkbox>
                </div>
            </div>
            <div *ngIf="currentStep==4" class="left">
                <div class="end">
                    <img src="/assets/icons/package-check.svg">
                    <div id="pay-form"></div>
                    <div class="end-msg text-xl text-primary">
                        Thank you for your order!
                    </div>
                    <p class="text-center text-primary text-lg">
                        We have received your payment.
                        Your lessons will be available on your profile shortly.
                    </p>

                    <div class="text-center">
                        <div class=" text-primary lg:max-w-11rem">

                            <button pButton pRipple label="Back to dashboard" [routerLink]="['/dashboard']"
                                class=" white-space-nowrap p-button p-button-rounded p-button-outlined text-primary">
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div *ngIf="!inTrial" class="right notranslate mb-6" id="package-overview">
                <div class="title">
                    Overview
                </div>
                <div class="row row-first">
                    <div>
                        Language
                    </div>
                    <div class="row-right">
                        {{selectedLanguage.name}}
                    </div>
                </div>
                <div class="row">
                    <div>
                        Level
                    </div>
                    <div class="row-right">
                        {{selectedLevel}}
                    </div>
                </div>
                <div class="row">
                    <div>
                        No. of Students
                    </div>
                    <div class="row-right">
                        {{selectedNumberOfStudents}}
                    </div>
                </div>
                <div class="row">
                    <div>
                        Hours
                    </div>
                    <div *ngIf="selectedPrice" class="row-right">
                        {{mltPricesHourly[selectedHoursIndex].hours}}
                    </div>
                </div>
                <div class="row">
                    <div>
                        Package Type
                    </div>
                    <div class="row-right">
                        {{selectedPackageToBuy.type}}
                    </div>
                </div>
                <div class="seperator"></div>
                <div class="row">
                    <div class="small-title">
                        General Info
                    </div>
                </div>
                <div class="row">
                    <div>
                        Expiration date is set {{expiresIn}} <span
                            *ngIf="selectedPackageToBuy.type=='Regular'">months</span> <span
                            *ngIf="selectedPackageToBuy.type!=='Regular'">(+{{selectedPackageToBuy.expiresPlus}})</span>
                        starting from the first package lesson
                    </div>
                </div>
                <div class="seperator"></div>
                <div class="row">
                    <div class="small-title">
                        {{selectedPackageToBuy.type}} Info
                    </div>
                </div>
                <div class="row" *ngIf="selectedPackageToBuy.type!=='Regular'">
                    <div>
                        Expiration date extends by {{selectedPackageToBuy.expiresPlus}}
                    </div>
                </div>
                <div class="row">
                    <div>
                        {{selectedPackageToBuy.cancelation}} hours lesson cancellation policy
                    </div>
                </div>
                <div class="row" *ngIf="selectedPackageToBuy.type!='Regular'">
                    <div>
                        AI Chat
                    </div>
                </div>
                <div class="row" *ngIf="selectedPackageToBuy.type!=='Regular' && selectedPackageToBuy.pause">
                    <div>
                        {{selectedPackageToBuy.pause}} pause availability
                    </div>
                </div>
                <div class="seperator"></div>
                <div class="row" *ngIf="selectedPackageToBuy.type!=='Regular'">
                    <div>
                        Regular Price
                    </div>
                    <div *ngIf="selectedPrice" class="row-right">
                        {{selectedPrice}}&euro;
                    </div>
                </div>
                <div class="row" *ngIf="selectedPackageToBuy.type!=='Regular'">
                    <div>
                        Additional Price
                    </div>
                    <div class="row-right">
                        {{selectedPackageToBuy.costPlus}}&euro;
                    </div>
                </div>
                <div class="row">
                    <div>
                        Total Price
                    </div>
                    <div class="row-right">
                        {{selectedPrice + selectedPackageToBuy.costPlus}}&euro;
                    </div>
                </div>
                <ng-container *ngIf="showSplitPayment">
                    <div class="seperator"></div>
                    <div class="row align-items-center">
                        <div class="flex flex-column">
                            Booking Fee
                            <span class="flex align-items-center gap-1 split-text">Pay now
                                <img class="pointer" (click)="onSplitPaymentPayNowTooltipSelected()"
                                    src="/assets/icons/tooltip-blue.svg" alt="tooltip" title="learn more" /></span>
                        </div>
                        <div class="row-right">
                            {{getTotalSplitPaymentAmount()}}&euro;
                        </div>
                    </div>
                    <div class="seperator"></div>
                    <div class="row align-items-center">
                        <div class="flex flex-column">
                            Remaining Balance
                            <span class="flex align-items-center gap-1 split-text">Pay later
                                <img class="pointer" (click)="onSplitPaymentPayLaterTooltipSelected()"
                                    src="/assets/icons/tooltip-blue.svg" alt="tooltip" title="learn more" /></span>
                        </div>
                        <div class="row-right">
                            {{getRemainingAmount()}}&euro;
                        </div>
                    </div>
                </ng-container>
                <div class="row mt-5 bottom-buttons" [ngClass]="{'hidden': currentStep === 4}">
                    <button type="button"
                        class=" align-self-center prev-button-outlined p-element py-1 p-button-outlined p-button-rounded button-outlined p-button-sm p-button p-component py-2 px-3"
                        [class.inactive]="currentStep==1" (click)="previous()"><span
                            class="p-datepicker-prev-icon pi pi-chevron-left"></span><span class="p-ink"></span>
                        BACK</button>

                    <ng-container *ngIf="currentStep!==3">
                        <button type="button"
                            class="next-button-outlined p-element py-1 p-button-outlined p-button-rounded button-outlined p-button-sm p-button p-component py-2 px-4 font-base"
                            (click)="next()">NEXT <span
                                class="p-datepicker-next-icon pi pi-chevron-right"></span></button>
                    </ng-container>

                    <div class="" *ngIf="currentStep==3">
                        <ng-container *ngIf="currentStep==3 && showSplitPayment; else noSplitPayment">
                            <!-- Revolut Pay Button Container - Shows when terms are agreed -->
                            <div *ngIf="agreed" class="revolut-pay-section mb-3">
                                <div class="payment-method-label mb-2">
                                    <span class="font-semibold text-primary">Pay with Revolut</span>
                                    <span class="payment-badge">Recommended</span>
                                </div>
                                <div id="revolut-pay-button" style="min-height: 44px;"></div>
                            </div>

                            <!-- Payment Instructions - Shows when terms are not agreed -->
                            <div *ngIf="!agreed" class="payment-instructions mb-3">
                                <div class="p-message p-message-info">
                                    <div class="p-message-wrapper">
                                        <span class="p-message-icon pi pi-info-circle"></span>
                                        <div class="p-message-text">
                                            <span class="p-message-summary">Payment Ready</span>
                                            <div class="p-message-detail">Please agree to the terms and conditions below to proceed with payment.</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Alternative Payment Button -->
                            <div class="alternative-payment-section" *ngIf="agreed">
                                <button type="button"
                                    class="split-pay-button-outlined p-element py-1 p-button-outlined p-button-rounded button-outlined p-button-sm p-button p-component py-2 px-3 font-base w-full"
                                    (click)="purchase()"
                                    [disabled]="!agreed">
                                    <div class="flex align-items-center justify-content-center gap-1">
                                        <div class="flex justify-content-start align-items-start flex-column"> <span
                                                class="font-bold">Pay with Card</span> <span
                                                class="font-sm">{{getTotalSplitPaymentAmount()}}&euro;</span> </div> <span
                                            class="p-datepicker-next-icon pi pi-chevron-right"></span>
                                    </div>
                                </button>
                            </div>

                        </ng-container>
                        <ng-template #noSplitPayment>
                            <div *ngIf="currentStep==3">
                                <!-- Revolut Pay Button Container - Shows when terms are agreed -->
                                <div *ngIf="agreed" class="revolut-pay-section mb-3">
                                    <div class="payment-method-label mb-2">
                                        <span class="font-semibold text-primary">Pay with Revolut</span>
                                        <span class="payment-badge">Recommended</span>
                                    </div>
                                    <div id="revolut-pay-button" style="min-height: 44px;"></div>
                                </div>

                                <!-- Payment Instructions - Shows when terms are not agreed -->
                                <div *ngIf="!agreed" class="payment-instructions mb-3">
                                    <div class="p-message p-message-info">
                                        <div class="p-message-wrapper">
                                            <span class="p-message-icon pi pi-info-circle"></span>
                                            <div class="p-message-text">
                                                <span class="p-message-summary">Payment Ready</span>
                                                <div class="p-message-detail">Please agree to the terms and conditions below to proceed with payment.</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Alternative Payment Button -->
                                <div class="alternative-payment-section" *ngIf="agreed">
                                    <button type="button"
                                        class="buy-button p-element py-1 p-button-outlined p-button-rounded button-outlined p-button-sm p-button p-component py-2 px-4 font-base w-full"
                                        (click)="purchase()"
                                        [disabled]="!agreed">Pay with Card <img _ngcontent-rrb-c97="" width="16"
                                            class="pi text-white font-sm ml-2"
                                            src="assets/icons/lessons/arranged-white.svg"></button>
                                </div>
                            </div>
                        </ng-template>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- <app-confirm-dialog 
closable="true"
dialogKey="key2" showHeader="true" headerClass="text-center flex align-items-center flex-column bg-white" [rejectBtnIcon]=""
acceptBtnLabel="Yes" acceptBtnImage="/assets/icons/tooltip-blue-header.svg" dialogType="''" headerText="<img src='your-image-source' alt='image-description'> Cool" rejectBtnLabel=""
confirmMessage="Once your booking fee payment is completed, a customer service representative will contact you shortly to provide further assistance. 
They will also inform you of the remaining balance and guide you on how to complete the payment process."></app-confirm-dialog> -->