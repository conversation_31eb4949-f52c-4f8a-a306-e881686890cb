{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { DialogService } from 'primeng/dynamicdialog';\nimport { take } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport { LibraryFileActionsDialogComponent } from '../library-file-actions-dialog/library-file-actions-dialog.component';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/library.service\";\nimport * as i2 from \"src/app/core/services/general.service\";\nimport * as i3 from \"primeng/dynamicdialog\";\nimport * as i4 from \"src/app/core/services/toast.service\";\nimport * as i5 from \"src/app/core/services/auth.service\";\nimport * as i6 from \"@angular/common/http\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"primeng/chip\";\nimport * as i9 from \"../../../../../shared/layout/g-level-circle/g-level-circle.component\";\nimport * as i10 from \"primeng/menu\";\nimport * as i11 from \"primeng/tooltip\";\nconst _c0 = (a0, a1) => ({\n  \"checked\": a0,\n  \"purple-hover\": a1\n});\nconst _c1 = (a0, a1) => ({\n  \"bg-grad-purple\": a0,\n  \"bg-grad-blue\": a1\n});\nfunction LibraryFileRowItemComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"g-level-circle\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const level_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", level_r2);\n  }\n}\nfunction LibraryFileRowItemComponent_img_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 21);\n  }\n}\nfunction LibraryFileRowItemComponent_p_chip_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-chip\")(1, \"span\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r3 = ctx.$implicit;\n    i0.ɵɵclassMapInterpolate1(\"class-rating \", category_r3.toLowerCase(), \" border-round-xl px-0\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r3);\n  }\n}\nfunction LibraryFileRowItemComponent_div_17_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 24);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pTooltip\", i0.ɵɵpipeBind2(2, 2, ctx_r3.classroomFile.categories, 1).join(\", \"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" +\", ctx_r3.classroomFile.categories.length - 1, \" \");\n  }\n}\nfunction LibraryFileRowItemComponent_div_17_ng_template_2_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵpipe(1, \"slice\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"pTooltip\", i0.ɵɵpipeBind2(1, 2, ctx_r3.classroomFile.categories, 2).join(\", \"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" +\", ctx_r3.classroomFile.categories.length - 2, \" \");\n  }\n}\nfunction LibraryFileRowItemComponent_div_17_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, LibraryFileRowItemComponent_div_17_ng_template_2_span_0_Template, 3, 5, \"span\", 25);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.classroomFile.categories.length > 2);\n  }\n}\nfunction LibraryFileRowItemComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, LibraryFileRowItemComponent_div_17_ng_container_1_Template, 4, 5, \"ng-container\", 23)(2, LibraryFileRowItemComponent_div_17_ng_template_2_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notMobileBlock_r5 = i0.ɵɵreference(3);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.isLarge)(\"ngIfElse\", notMobileBlock_r5);\n  }\n}\nfunction LibraryFileRowItemComponent_div_19_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 29);\n  }\n}\nfunction LibraryFileRowItemComponent_div_19_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 30);\n  }\n}\nfunction LibraryFileRowItemComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function LibraryFileRowItemComponent_div_19_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.checkLibraryItem(ctx_r3.checkLibMenu));\n    });\n    i0.ɵɵtemplate(1, LibraryFileRowItemComponent_div_19_img_1_Template, 1, 0, \"img\", 27)(2, LibraryFileRowItemComponent_div_19_img_2_Template, 1, 0, \"img\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.checked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.checked);\n  }\n}\nfunction LibraryFileRowItemComponent_img_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 31);\n  }\n}\nexport let LibraryFileRowItemComponent = /*#__PURE__*/(() => {\n  class LibraryFileRowItemComponent {\n    constructor(libraryService, generalService, dialogService, toastService, authService, http) {\n      this.libraryService = libraryService;\n      this.generalService = generalService;\n      this.dialogService = dialogService;\n      this.toastService = toastService;\n      this.authService = authService;\n      this.http = http;\n      this.classroom = {};\n      this.classroomFile = {};\n      this.appendDialogActionsTo = 'allFiles';\n      this.showGroupActions = false;\n      this.hasLimitedOptions = false;\n      this.availableActions = [];\n      this.isSmallScreen = false;\n      this.showSharedWithUsers = true;\n      this.isInTab = false;\n      this.fileDeleted = new EventEmitter();\n      this.libraryChecked = new EventEmitter();\n      this.fileUnshared = new EventEmitter();\n      this.StudentIdsToSend = [];\n      this.isDisabled = false;\n      this.isDisabledWholeClass = false;\n      this.moveFileId = 0;\n      this.moveFolderId = 0;\n      this.isMoveRadioCheked = false;\n      this.showRename = false;\n      this.refs = [];\n      this.subs = new SubSink();\n      this.checked = false;\n      this.sharedWithUsers = [];\n      this.menuItems = [];\n      this.isLarge = false;\n      this.hasMultipleCategoriesSlice = 1;\n    }\n    ngOnInit() {\n      if (this.showSharedWithUsers) {\n        this.getUsersLibraryHasBeenSharedWith();\n      }\n      this.subs.sink = this.libraryService.showGroupActions.subscribe(res => {\n        if (!res) {\n          this.checked = false;\n        }\n        // if (this.libraryService.isPreviewingFromChat()) {\n        //   this.checked = true;\n        // }\n      });\n      this.subs.add(this.generalService.deviceKind.pipe().subscribe(res => {\n        if (res) {\n          if (res.w768up) {\n            this.isLarge = true;\n            this.hasMultipleCategoriesSlice = 2;\n          }\n        }\n      }));\n      this.prepareMenuItemsForItem(this.classroomFile);\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n    }\n    onSelect(studentId) {\n      this.isDisabled = false;\n      if (this.StudentIdsToSend.indexOf(studentId) !== -1) this.StudentIdsToSend.splice(this.StudentIdsToSend.indexOf(studentId), 1);else this.StudentIdsToSend.push(studentId);\n      if (this.isDisabledWholeClass && this.StudentIdsToSend.length === 0) this.isDisabledWholeClass = false;else this.isDisabledWholeClass = true;\n    }\n    showMoveFilePopup(fileId) {\n      document.getElementById('lib-file-share-menu-' + fileId).style.display = 'none';\n      document.getElementById('lib-file-move-menu-' + fileId).style.display = 'block';\n      document.getElementById('lib-file-move-menu-' + fileId).style.top = '55%';\n    }\n    // openSharedByFileMenu(fileId: any) {\n    //   alert(fileId);\n    //   [].forEach.call(document.getElementsByClassName('lib-file-sharedby-menu'), (el: any) => {\n    //     el.style.display = 'none';\n    //   })\n    //   setTimeout(() => {\n    //     document.getElementById('lib-file-sharedby-menu-' + fileId)!.style.display = 'flex';\n    //     document.getElementById('lib-file-sharedby-menu-' + fileId)!.style.top = (880 + document.getElementById('lib-file-sharedby-menu-' + fileId)!.offsetTop) + \"px\";\n    //   }, 100);\n    //   // this.showShare = true;\n    // }\n    // showSharedByMoveFilePopup(fileId: any) {\n    //   document.getElementById('lib-file-sharedby-move-menu-' + fileId)!.style.display = 'block';\n    //   document.getElementById('lib-file-sharedby-move-menu-' + fileId)!.style.top = '55%';\n    // }\n    getFileName(path) {\n      if (path) {\n        let n = path.lastIndexOf(\"/\");\n        if (n === -1) n = path.lastIndexOf(\"\\\\\");\n        let result = path.substring(n + 1);\n        return result;\n      }\n      return path;\n    }\n    prepareMenuItemsForItem(item) {\n      let context = item;\n      const menuItems = [{\n        label: 'View',\n        icon: 'pi pi-fw pi-external-link',\n        command: event => {\n          this.openFileInNewTab(context.path);\n        }\n      }, {\n        label: 'Copy URL',\n        icon: 'pi pi-fw pi-copy',\n        command: event => {\n          this.copyURL(context.path);\n        }\n      }, {\n        label: 'Download',\n        icon: 'pi pi-fw pi-cloud-download',\n        command: event => {\n          this.downloadFile(context.path);\n        }\n      }, {\n        label: 'Share',\n        icon: 'pi pi-fw pi-share-alt',\n        command: event => {\n          if (this.authService.isStudent) {\n            this.shareWithTeacher(context);\n          } else {\n            const currentSelectedClassroom = this.libraryService.getCurrentSelectedClassroom();\n            if (Object.keys(currentSelectedClassroom).length > 0) {\n              const classroomStudents = this.libraryService.getCurrentSelectedClassroom().classroomStudents;\n              // if only one student, share immediately\n              if (classroomStudents.length === 1 && this.authService.isTeacher) {\n                let send = {\n                  fileIds: [context.fileId],\n                  classroomId: this.libraryService.getCurrentSelectedClassroom().id,\n                  studentIds: [classroomStudents[0].aspUserId]\n                };\n                this.sendShareToApi(send);\n              }\n            } else {\n              this.openDialogWithComponent(LibraryFileActionsDialogComponent, {\n                action: 'shareWith',\n                dialogsubTitle: 'Share \"' + this.getFileName(context.name) + '\" with...',\n                dialogTitle: 'Share file with...',\n                libraryFile: context\n              });\n            }\n            console.log(this.libraryService.getCurrentSelectedClassroom().classroomStudents);\n            // this.openDialogWithComponent(LibraryFileActionsDialogComponent,\n            //   {\n            //     action: 'shareWith',\n            //     dialogsubTitle: 'Share \"' + this.getFileName(context.name) + '\" with...',\n            //     dialogTitle: 'Share file with...',\n            //     libraryFile: context\n            //   });\n          }\n        }\n      }, {\n        label: 'Unshare',\n        icon: 'pi pi-fw pi-share-alt',\n        command: event => {\n          let send = {\n            fileId: this.classroomFile.fileId,\n            classroomId: this.libraryService.getCurrentSelectedClassroom().id,\n            studentIds: [this.libraryService.getCurrentSelectedClassroom().teacher?.aspUserId]\n          };\n          if (this.authService.isStudent) {\n            this.sendUnShareToApi(send);\n          } else {\n            this.sendUnShareToApi(send);\n            // this.openDialogWithComponent(LibraryFileActionsDialogComponent,\n            //   {\n            //     action: 'unShareWith',\n            //     dialogsubTitle: 'Share \"' + this.getFileName(context.name) + '\" with...',\n            //     dialogTitle: 'Share file with...',\n            //     libraryFile: context\n            //   });\n          }\n        }\n      }, {\n        label: 'Move',\n        icon: 'pi pi-fw pi-folder-open',\n        command: event => {\n          this.openDialogWithComponent(LibraryFileActionsDialogComponent, {\n            action: 'move',\n            dialogsubTitle: 'Move \"' + this.getFileName(context.name) + '\" to folder...',\n            dialogTitle: 'Move file to folder',\n            libraryFile: context,\n            folders: this.folders\n          });\n        }\n      }, {\n        label: 'Edit',\n        icon: 'pi pi-fw pi-file-edit',\n        command: event => {\n          this.generalService.openDialogWithComponent(this.dialogService, LibraryFileActionsDialogComponent, 440, {\n            action: 'editFileLevelsCategories',\n            dialogsubTitle: '',\n            dialogTitle: 'Edit Library File',\n            libraryFile: context\n          }, null, result => {\n            console.log(result);\n            if (result && result.action === 'editFileLevelsCategories') {\n              if (!this.generalService.isNullishObject(result)) {\n                console.log(this.classroomFile);\n                console.log(result);\n                this.classroomFile = {\n                  ...result.result\n                };\n                context = {\n                  ...result.result\n                };\n              }\n            }\n          });\n        }\n      }, {\n        label: 'Delete',\n        icon: 'pi pi-fw pi-trash',\n        command: event => {\n          this.deleteFile(item.fileId);\n        }\n      }];\n      this.menuItems = menuItems.filter(menuItem => {\n        const menuLabel = menuItem.label;\n        return this.availableActions.includes(menuLabel);\n      });\n    }\n    showSharePopup(fileId) {\n      document.getElementById('lib-file-move-menu-' + fileId).style.display = 'none';\n      document.getElementById('lib-file-share-menu-' + fileId).style.display = 'block';\n      document.getElementById('lib-file-share-menu-' + fileId).style.top = '25%';\n    }\n    rename(fileId, newName) {\n      // const input = document.getElementById('fileName') as HTMLInputElement | null;\n      // console.log(input?.value)\n      // this.classroomFile.name = input!.value\n      this.subs.add(this.libraryService.updateLibraryFileName(fileId, newName).pipe(take(1)).subscribe(res => {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'File name name has been updated.'\n        });\n        console.log(res);\n        // this.showRename = !this.showRename\n      }));\n    }\n    checkLibraryItem(el) {\n      this.checked = !this.checked;\n      // let bg = this.checked ? '#2d2a4b' : 'white';\n      // el!.style.backgroundColor = bg;\n      if (this.checked) {\n        this.libraryService.checkedLibraries.push(this.classroomFile);\n        this.libraryChecked.emit({\n          checked: true,\n          file: this.classroomFile\n        });\n      } else {\n        this.libraryService.checkedLibraries = this.libraryService.checkedLibraries.filter(el => {\n          return !(el.path == this.classroomFile.path);\n        });\n        this.libraryChecked.emit({\n          checked: false,\n          file: this.classroomFile\n        });\n      }\n    }\n    getUniqueSharedUsers() {\n      // exclude from sharedWithUsers if not in classroomStudents\n      const filteredUsers = this.sharedWithUsers.filter(user => {\n        return this.classroomStudents.find(student => {\n          return student.aspUserId === user.aspUserId;\n        });\n      });\n      const uniqueNames = Object.values(filteredUsers.reduce((acc, user) => {\n        if (!acc[user.aspUserId]) {\n          acc[user.aspUserId] = user.firstName;\n        }\n        return acc;\n      }, {}));\n      return uniqueNames.join(', ');\n    }\n    onWholeClassSelect() {\n      if (this.isDisabled) this.isDisabled = false;else this.isDisabled = true;\n      if (this.StudentIdsToSend.length === 0) {\n        this.classroom.classroomStudents.forEach(student => {\n          this.StudentIdsToSend.push(student.id);\n        });\n      } else this.StudentIdsToSend = [];\n    }\n    moveFile(libraryFile, moveFolder) {\n      this.subs.add(this.libraryService.moveFileToFolder(libraryFile.fileId, moveFolder.folderId).subscribe(response => {\n        if (!this.generalService.isNullishObject(response)) {\n          this.toastService.setShowToastmessage({\n            severity: 'success',\n            summary: '',\n            detail: 'File moved successfully.'\n          });\n          this.libraryService.setUpdateFolderListener(libraryFile, moveFolder);\n          this.libraryService.setUpdateListener(true);\n        }\n      }));\n      this.ngOnInit();\n    }\n    copyURL(filePath) {\n      this.fileURL = environment.apiUrl + \"/\" + filePath;\n      const selBox = document.createElement('textarea');\n      selBox.style.position = 'fixed';\n      selBox.style.left = '0';\n      selBox.style.top = '0';\n      selBox.style.opacity = '0';\n      selBox.value = this.fileURL;\n      document.body.appendChild(selBox);\n      selBox.focus();\n      selBox.select();\n      document.execCommand('copy');\n      document.body.removeChild(selBox);\n      this.toastService.setShowToastmessage({\n        severity: 'success',\n        summary: '',\n        detail: 'File path copied successfully.'\n      });\n    }\n    deleteFile(fileId) {\n      this.fileDeleted.emit(this.classroomFile);\n      this.ngOnDestroy();\n    }\n    getUsersLibraryHasBeenSharedWith() {\n      if (this.classroomFile.fileId) {\n        return this.subs.add(this.libraryService.getUsersLibraryHasBeenSharedWith(this.classroomFile.fileId).pipe(take(1)).subscribe(res => {\n          this.sharedWithUsers = res;\n        }));\n      }\n      return [];\n    }\n    shareWithTeacher(context) {\n      let send = {\n        fileIds: [context.fileId],\n        classroomId: this.libraryService.getCurrentSelectedClassroom().id,\n        studentIds: [this.libraryService.getCurrentSelectedClassroom().teacher?.aspUserId]\n      };\n      this.sendShareToApi(send);\n    }\n    shareLibraryFile(map) {\n      console.log(this.classroomFile);\n      map.forEach((users, classroomId) => {\n        let ids = [];\n        let fileIds = [];\n        for (let user of users) {\n          ids.push(user.aspUserId);\n        }\n        fileIds.push(this.classroomFile.fileId);\n        let send = {\n          fileIds: fileIds,\n          classroomId: classroomId,\n          studentIds: ids\n        };\n        this.sendShareToApi(send);\n      });\n    }\n    unShareLibraryFile(map) {\n      console.log(this.classroomFile);\n      map.forEach((users, classroomId) => {\n        let ids = [];\n        let fileIds = [];\n        for (let user of users) {\n          ids.push(user.aspUserId);\n        }\n        fileIds.push(this.classroomFile.fileId);\n        let send = {\n          fileId: this.classroomFile.fileId,\n          classroomId: classroomId\n        };\n        this.sendUnShareToApi(send);\n      });\n    }\n    openFileInNewTab(url) {\n      const fileURL = environment.apiUrl + \"/\" + url;\n      window.open(fileURL, '_blank');\n    }\n    sendShareToApi(sendParams) {\n      this.subs.add(this.libraryService.shareFileWithClassUsers(sendParams).pipe(take(1)).subscribe(res => {\n        console.log(this.classroom);\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Library file has been shared.'\n        });\n        if (this.authService.isTeacher) {\n          this.libraryService.setCurrentSelectedClassroomId(parseInt(sendParams.classroomId));\n        }\n      }));\n    }\n    sendUnShareToApi(sendParams) {\n      console.log(this.classroom);\n      this.subs.add(this.libraryService.unShareLibrary(sendParams).pipe(take(1)).subscribe(res => {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Library file has been unshared.'\n        });\n        this.fileUnshared.emit(res);\n        this.libraryService.setCurrentSelectedClassroomId(parseInt(sendParams.classroomId));\n      }));\n    }\n    openDialogWithComponent(component, dialogData, width = 360) {\n      this.generalService.openDialogWithComponent(this.dialogService, component, 400, dialogData, null, result => {\n        if (result && result.action === 'shareWith') {\n          this.shareLibraryFile(result.map.map);\n        } else if (result && result.action === 'unShareWith') {\n          this.unShareLibraryFile(result.map.map);\n        } else if (result && result.action === 'move') {\n          this.moveFile(result.libraryFile, result.folder);\n        }\n      });\n      // this.refs.forEach(ref => ref.close());\n      // this.refs = [];\n      // const element = document.querySelector('#' + this.appendDialogActionsTo);\n      // const rect = element!.getBoundingClientRect();\n      // const centerX = rect.left + rect.width / 2 - width / 2;\n      // const centerY = rect.top;\n      // const dialogRef = (this.dialogService.open(component, {\n      //   header: '',\n      //   width: width + 'px',\n      //   showHeader: false,\n      //   dismissableMask: true,\n      //   modal: true,\n      //   contentStyle: {\n      //     \"max-width\": \"100%\", \"max-height\": \"400px\", \"overflow\": \"auto\", \"border-radius\": \"10px\", \"padding\": \"0px\",\n      //   },\n      //   style: { 'left': `${centerX}px`, 'top': `${centerY}px`, 'position': `fixed` },\n      //   baseZIndex: 10000,\n      //   maskStyleClass: 'transparent-mask',\n      //   data: { dialogData: dialogData },\n      // }));\n      // this.refs.push(dialogRef);\n      // dialogRef.onClose.subscribe((data: any) => {\n      //   console.log('Dialog closed with data:', data);\n      //   if (data) {\n      //     switch (data.action) {\n      //       case 'rename':\n      //         this.rename(data.libraryFile.fileId, data.libraryFile.name);\n      //         break;\n      //       case 'shareWith':\n      //         this.shareLibraryFiles(data.map.map);\n      //         break;\n      //       case 'move':\n      //         this.moveFile(data.libraryFile.fileId, data.folder.folderId);\n      //         break;\n      //       default:\n      //         break;\n      //     }\n      //   }\n      // });\n    }\n    downloadFile(url) {\n      const fileURL = environment.apiUrl + \"/\" + url;\n      this.subs.add(this.http.get(fileURL, {\n        responseType: 'blob'\n      }).subscribe(blob => {\n        const link = document.createElement('a');\n        link.href = window.URL.createObjectURL(blob);\n        link.download = this.getFileName(fileURL);\n        link.click();\n      }));\n    }\n    static #_ = this.ɵfac = function LibraryFileRowItemComponent_Factory(t) {\n      return new (t || LibraryFileRowItemComponent)(i0.ɵɵdirectiveInject(i1.LibraryService), i0.ɵɵdirectiveInject(i2.GeneralService), i0.ɵɵdirectiveInject(i3.DialogService), i0.ɵɵdirectiveInject(i4.ToastService), i0.ɵɵdirectiveInject(i5.AuthService), i0.ɵɵdirectiveInject(i6.HttpClient));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LibraryFileRowItemComponent,\n      selectors: [[\"app-library-file-row-item\"]],\n      inputs: {\n        classroom: \"classroom\",\n        classroomFile: \"classroomFile\",\n        folders: \"folders\",\n        appendDialogActionsTo: \"appendDialogActionsTo\",\n        showGroupActions: \"showGroupActions\",\n        hasLimitedOptions: \"hasLimitedOptions\",\n        availableActions: \"availableActions\",\n        isSmallScreen: \"isSmallScreen\",\n        showSharedWithUsers: \"showSharedWithUsers\",\n        isInTab: \"isInTab\",\n        classroomStudents: \"classroomStudents\"\n      },\n      outputs: {\n        fileDeleted: \"fileDeleted\",\n        libraryChecked: \"libraryChecked\",\n        fileUnshared: \"fileUnshared\"\n      },\n      features: [i0.ɵɵProvidersFeature([DialogService])],\n      decls: 23,\n      vars: 24,\n      consts: [[\"menu3\", \"\"], [\"notMobileBlock\", \"\"], [1, \"library-file-row\", 3, \"ngClass\"], [1, \"w-3rem\", \"h-3rem\", \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-grad-blue\", \"border-circle\", \"mr-1\", \"flex-shrink-0\", 3, \"ngClass\"], [\"src\", \"/assets/icons/file-white.svg\", \"height\", \"24\"], [1, \"w-full\", \"flex\", \"sm:flex-row\", \"align-items-center\", \"justify-content-between\"], [1, \"library-file-details\", \"py-1\", \"font-sm\", \"flex\", \"flex-column\"], [1, \"file-name\", \"file-path-text\", \"max-w-10rem\", \"sm:max-w-15rem\", \"pointer\", 3, \"click\"], [1, \"flex\", \"gap-1\"], [4, \"ngFor\", \"ngForOf\"], [\"src\", \"/assets/icons/library/share-sm-icon.svg\", \"width\", \"10\", 4, \"ngIf\"], [1, \"font-xs\", \"file-path-text\", \"max-w-10rem\"], [1, \"library-file-meta\"], [1, \"text-900\", \"w-full\", \"md:flex-order-0\", \"flex-order-1\", \"flex\", \"align-items-end\", \"justify-content-end\", \"gap-1\"], [3, \"class\", 4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [1, \"library-actions-button\", \"mx-2\", 3, \"click\", \"id\"], [\"style\", \"position: relative;\", 3, \"click\", 4, \"ngIf\"], [\"src\", \"/assets/icons/library/library-more-actions.svg\", \"class\", \"pointer hvr-grow\", \"style\", \"width:1.75rem; height:1.75rem\", 4, \"ngIf\"], [\"popup\", \"popup\", \"appendTo\", \"body\", 3, \"model\"], [\"size\", \"2xs\", 3, \"name\"], [\"src\", \"/assets/icons/library/share-sm-icon.svg\", \"width\", \"10\"], [1, \"font-xs\", \"text-white\", \"py-1\", \"px-0\", \"line-height-1\"], [4, \"ngIf\", \"ngIfElse\"], [\"tooltipPosition\", \"top\", 1, \"font-xs\", \"text-muted\", \"mt-2\", \"more-circle\", 3, \"pTooltip\"], [\"class\", \"font-xs text-muted mt-2 more-circle\", \"tooltipPosition\", \"top\", 3, \"pTooltip\", 4, \"ngIf\"], [2, \"position\", \"relative\", 3, \"click\"], [\"src\", \"/assets/icons/library/group-item-empty.svg\", \"class\", \"pointer\", \"style\", \"width:1.75rem; height:1.75rem\", 4, \"ngIf\"], [\"src\", \"/assets/icons/library/group-item-full.svg\", \"class\", \"pointer\", \"style\", \"width:1.75rem; height:1.75rem\", 4, \"ngIf\"], [\"src\", \"/assets/icons/library/group-item-empty.svg\", 1, \"pointer\", 2, \"width\", \"1.75rem\", \"height\", \"1.75rem\"], [\"src\", \"/assets/icons/library/group-item-full.svg\", 1, \"pointer\", 2, \"width\", \"1.75rem\", \"height\", \"1.75rem\"], [\"src\", \"/assets/icons/library/library-more-actions.svg\", 1, \"pointer\", \"hvr-grow\", 2, \"width\", \"1.75rem\", \"height\", \"1.75rem\"]],\n      template: function LibraryFileRowItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"li\", 2);\n          i0.ɵɵtext(1);\n          i0.ɵɵelementStart(2, \"div\", 3);\n          i0.ɵɵelement(3, \"img\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 5)(5, \"div\", 6)(6, \"span\", 7);\n          i0.ɵɵlistener(\"click\", function LibraryFileRowItemComponent_Template_span_click_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.openFileInNewTab(ctx.classroomFile.path));\n          });\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 8);\n          i0.ɵɵtemplate(9, LibraryFileRowItemComponent_div_9_Template, 2, 1, \"div\", 9)(10, LibraryFileRowItemComponent_img_10_Template, 1, 0, \"img\", 10);\n          i0.ɵɵelementStart(11, \"span\", 11);\n          i0.ɵɵtext(12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 12)(14, \"div\", 13);\n          i0.ɵɵtemplate(15, LibraryFileRowItemComponent_p_chip_15_Template, 3, 4, \"p-chip\", 14);\n          i0.ɵɵpipe(16, \"slice\");\n          i0.ɵɵtemplate(17, LibraryFileRowItemComponent_div_17_Template, 4, 2, \"div\", 15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 16);\n          i0.ɵɵlistener(\"click\", function LibraryFileRowItemComponent_Template_div_click_18_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            const menu3_r6 = i0.ɵɵreference(22);\n            return i0.ɵɵresetView(!ctx.showGroupActions ? menu3_r6.toggle($event) : null);\n          });\n          i0.ɵɵtemplate(19, LibraryFileRowItemComponent_div_19_Template, 3, 2, \"div\", 17)(20, LibraryFileRowItemComponent_img_20_Template, 1, 0, \"img\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(21, \"p-menu\", 19, 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(18, _c0, ctx.checked && ctx.showGroupActions, ctx.isInTab));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.classroom == null ? null : ctx.classroom.id, \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(21, _c1, ctx.isInTab, !ctx.isInTab));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.getFileName(ctx.classroomFile.name));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.classroomFile.levels);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.sharedWithUsers.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getUniqueSharedUsers(), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(16, 14, ctx.classroomFile.categories, 0, ctx.hasMultipleCategoriesSlice));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.classroomFile.categories.length > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵpropertyInterpolate1(\"id\", \"lib-file-burger-\", ctx.classroomFile.fileId, \"\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showGroupActions || ctx.libraryService.isPreviewingFromChat());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.showGroupActions && !ctx.libraryService.isPreviewingFromChat());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"model\", ctx.menuItems);\n        }\n      },\n      dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i8.Chip, i9.GLevelCircleComponent, i10.Menu, i11.Tooltip, i7.SlicePipe],\n      styles: [\".bg-grad-blue[_ngcontent-%COMP%]{background:linear-gradient(#657aef,#1a266c)}.bg-grad-purple[_ngcontent-%COMP%]{background:linear-gradient(#7f8dfa,#4f29a2);border:1px solid #7d89f6}.library-actions-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{line-height:1}.library-file-row[_ngcontent-%COMP%]{border-radius:40px;display:flex;background-color:#fff;align-items:center;margin-bottom:10px;transition:all .3s ease-in-out}.library-file-row[_ngcontent-%COMP%]:hover, .library-file-row.checked[_ngcontent-%COMP%]{color:#fff;background:#002ccf;background:linear-gradient(90deg,#002ccf,#c9d2ff18)}.library-file-row.purple-hover[_ngcontent-%COMP%]:hover, .library-file-row.purple-hover.checked[_ngcontent-%COMP%]{background:linear-gradient(90deg,#6659cd,#c9d2ff18)}.library-file-row[_ngcontent-%COMP%]   svg.library-file-icon[_ngcontent-%COMP%]{padding:8px;margin-top:5px}.library-file-row[_ngcontent-%COMP%]   .library-file-details[_ngcontent-%COMP%]{width:100%;display:flex}.library-file-row[_ngcontent-%COMP%]   .library-file-details[_ngcontent-%COMP%]   .library-file-meta[_ngcontent-%COMP%]{margin-top:5px;display:flex}.library-file-row[_ngcontent-%COMP%]   .library-file-details[_ngcontent-%COMP%]   .library-file-meta[_ngcontent-%COMP%]   span.file-level[_ngcontent-%COMP%]{color:#fff;background:#444070;padding:6px;border-radius:50px}.library-file-row[_ngcontent-%COMP%]   .library-file-details[_ngcontent-%COMP%]   .library-file-meta[_ngcontent-%COMP%]   span.file-category[_ngcontent-%COMP%]{color:#fff;background-color:#a44fd0;padding:6px;border-radius:50px;margin-left:6px}.library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]{width:180px;position:absolute;left:92.5%;top:69px;border-radius:12px;border:1px solid var(--main-color);box-sizing:border-box;transition-duration:.2s;transition-property:transform;background-color:#fff;z-index:1;flex-direction:column;padding:10px}@media screen and (max-width: 1124px){.library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]{right:30px}}.library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]{display:flex;flex-direction:column}.library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]   .lib-file-menu-col[_ngcontent-%COMP%]{margin-top:5px;display:flex;align-items:center}.library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]   .lib-file-menu-col[_ngcontent-%COMP%]   .lib-file-menu-text[_ngcontent-%COMP%]{margin-left:10px;cursor:pointer}.library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]   .lib-file-menu-col[_ngcontent-%COMP%]   .lib-file-menu-icon[_ngcontent-%COMP%]{width:20px;display:flex;justify-content:center}.library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]:first-child{margin-top:0}  .p-menu{border:1px solid #1a266c!important}  .p-menu .p-menuitem-link{padding:.5rem 1rem!important}.more-circle[_ngcontent-%COMP%]{background:linear-gradient(#657aef,#1a266c);color:#fff;padding:5px 7px;border-radius:100px;line-height:1}\"]\n    });\n  }\n  return LibraryFileRowItemComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}