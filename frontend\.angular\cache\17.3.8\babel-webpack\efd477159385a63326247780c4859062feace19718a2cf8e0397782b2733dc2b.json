{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/core/services/layout.service\";\nconst _c0 = a0 => ({\n  \"no-visibility\": a0\n});\nexport class MobileLeftMenuComponent {\n  constructor(router, layoutService) {\n    this.router = router;\n    this.layoutService = layoutService;\n    this.subs = new SubSink();\n    this.menuItems = [];\n    this.hideRightSideButton = false;\n    this.onMenuUrlChange = new EventEmitter();\n    this.currentIndex = 0;\n    this.currentTitle = '';\n  }\n  ngOnInit() {\n    this.subs.add(this.layoutService.currentMenuIndex.subscribe(res => {\n      console.log(res);\n    }));\n  }\n  prevMenuItem() {\n    this.currentIndex = Math.max(0, this.currentIndex - 1);\n    this.layoutService.setCurrentMenuIndex(this.currentIndex);\n    // this.router.navigateByUrl(this.menuItems[this.currentIndex].url);\n    this.onMenuUrlChange.emit(this.menuItems[this.currentIndex].url);\n  }\n  nextMenuItem() {\n    const nextIndex = Math.min(this.menuItems.length - 1, this.currentIndex + 1);\n    if (this.menuItems[nextIndex].disabled) {\n      return false;\n    } else {\n      this.currentIndex = nextIndex;\n      this.layoutService.setCurrentMenuIndex(this.currentIndex);\n      this.currentTitle = this.menuItems[this.currentIndex].title;\n      this.onMenuUrlChange.emit(this.menuItems[this.currentIndex].url);\n      return false;\n    }\n    // this.router.navigateByUrl(this.menuItems[this.currentIndex].url);\n  }\n  hasPreviousItem() {\n    return this.currentIndex !== 0;\n  }\n  static #_ = this.ɵfac = function MobileLeftMenuComponent_Factory(t) {\n    return new (t || MobileLeftMenuComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.LayoutService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MobileLeftMenuComponent,\n    selectors: [[\"app-mobile-left-menu\"]],\n    inputs: {\n      menuItems: \"menuItems\",\n      hideRightSideButton: \"hideRightSideButton\"\n    },\n    outputs: {\n      onMenuUrlChange: \"onMenuUrlChange\"\n    },\n    decls: 6,\n    vars: 7,\n    consts: [[\"blockClass\", \"dash__menu-mobile\", \"header\", \"\", \"headerBackgroundImage\", \"/assets/images/dashboard/gradient-sm-1.png\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"py-2\", \"px-4\"], [1, \"pi\", \"pi-chevron-left\", \"text-3xl\", \"text-white\", 3, \"click\", \"ngClass\"], [1, \"font-bold\", \"fl-typo\", \"s18-24\", \"m-0\", \"text-white\"], [1, \"pi\", \"pi-chevron-right\", \"text-3xl\", \"text-white\", 3, \"click\", \"ngClass\"]],\n    template: function MobileLeftMenuComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"app-block-viewer\", 0)(1, \"div\", 1)(2, \"i\", 2);\n        i0.ɵɵlistener(\"click\", function MobileLeftMenuComponent_Template_i_click_2_listener() {\n          return ctx.prevMenuItem();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"h5\", 3);\n        i0.ɵɵtext(4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"i\", 4);\n        i0.ɵɵlistener(\"click\", function MobileLeftMenuComponent_Template_i_click_5_listener() {\n          return ctx.nextMenuItem();\n        });\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(3, _c0, !ctx.hasPreviousItem()));\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.menuItems[ctx.currentIndex].title);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(5, _c0, ctx.hideRightSideButton));\n      }\n    },\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "SubSink", "MobileLeftMenuComponent", "constructor", "router", "layoutService", "subs", "menuItems", "hideRightSideButton", "onMenuUrlChange", "currentIndex", "currentTitle", "ngOnInit", "add", "currentMenuIndex", "subscribe", "res", "console", "log", "prevMenuItem", "Math", "max", "setCurrentMenuIndex", "emit", "url", "nextMenuItem", "nextIndex", "min", "length", "disabled", "title", "hasPreviousItem", "_", "i0", "ɵɵdirectiveInject", "i1", "Router", "i2", "LayoutService", "_2", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "MobileLeftMenuComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "MobileLeftMenuComponent_Template_i_click_2_listener", "ɵɵelementEnd", "ɵɵtext", "MobileLeftMenuComponent_Template_i_click_5_listener", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "ɵɵtextInterpolate"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\layout\\mobile-left-menu\\mobile-left-menu.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\layout\\mobile-left-menu\\mobile-left-menu.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { LeftMenuItem } from 'src/app/core/models/general.model';\r\nimport { LayoutService } from 'src/app/core/services/layout.service';\r\nimport { SubSink } from 'subsink';\r\n\r\n@Component({\r\n  selector: 'app-mobile-left-menu',\r\n  templateUrl: './mobile-left-menu.component.html',\r\n  styleUrls: ['./mobile-left-menu.component.scss']\r\n})\r\nexport class MobileLeftMenuComponent implements OnInit {\r\n\r\n  private subs = new SubSink();\r\n  @Input() menuItems: LeftMenuItem[] = [];\r\n  @Input() hideRightSideButton = false;\r\n  @Output() onMenuUrlChange = new EventEmitter();\r\n  currentIndex = 0;\r\n  currentTitle = '';\r\n  constructor(\r\n    private router: Router,\r\n    private layoutService: LayoutService,\r\n    ) { }\r\n\r\n  ngOnInit(): void {\r\n    \r\n    this.subs.add(this.layoutService.currentMenuIndex.subscribe(res => {\r\n\r\n      console.log(res);\r\n    }));\r\n  }\r\n\r\n  prevMenuItem() {\r\n    this.currentIndex = Math.max(0, this.currentIndex - 1);\r\n    this.layoutService.setCurrentMenuIndex(this.currentIndex);\r\n    // this.router.navigateByUrl(this.menuItems[this.currentIndex].url);\r\n    this.onMenuUrlChange.emit(this.menuItems[this.currentIndex].url);\r\n  }\r\n  \r\n  nextMenuItem() {\r\n    const nextIndex = Math.min(this.menuItems.length - 1, this.currentIndex + 1)\r\n    if (this.menuItems[nextIndex].disabled) {\r\n      return false;\r\n    } else {\r\n\r\n      this.currentIndex = nextIndex;\r\n      this.layoutService.setCurrentMenuIndex(this.currentIndex);\r\n      this.currentTitle = (this.menuItems[this.currentIndex].title);\r\n      this.onMenuUrlChange.emit(this.menuItems[this.currentIndex].url);\r\n      return false;\r\n    }\r\n    // this.router.navigateByUrl(this.menuItems[this.currentIndex].url);\r\n  }\r\n\r\n  hasPreviousItem() {\r\n    return this.currentIndex !== 0;\r\n  }\r\n}\r\n", "<app-block-viewer blockClass=\"dash__menu-mobile\" header=\"\"\r\n    headerBackgroundImage=\"/assets/images/dashboard/gradient-sm-1.png\">\r\n    <div class=\"flex align-items-center justify-content-between py-2 px-4\">\r\n        <i (click)=\"prevMenuItem()\" [ngClass]=\"{'no-visibility': !hasPreviousItem()}\" class=\"pi pi-chevron-left text-3xl text-white\"></i>\r\n        <h5 class=\"font-bold fl-typo s18-24 m-0 text-white\">{{ menuItems[currentIndex].title }}</h5>\r\n        <i [ngClass]=\"{'no-visibility': hideRightSideButton}\" (click)=\"nextMenuItem()\" class=\"pi pi-chevron-right text-3xl text-white\"></i>\r\n    </div>\r\n</app-block-viewer>"], "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAI9E,SAASC,OAAO,QAAQ,SAAS;;;;;;;AAOjC,OAAM,MAAOC,uBAAuB;EAQlCC,YACUC,MAAc,EACdC,aAA4B;IAD5B,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IARf,KAAAC,IAAI,GAAG,IAAIL,OAAO,EAAE;IACnB,KAAAM,SAAS,GAAmB,EAAE;IAC9B,KAAAC,mBAAmB,GAAG,KAAK;IAC1B,KAAAC,eAAe,GAAG,IAAIT,YAAY,EAAE;IAC9C,KAAAU,YAAY,GAAG,CAAC;IAChB,KAAAC,YAAY,GAAG,EAAE;EAIX;EAENC,QAAQA,CAAA;IAEN,IAAI,CAACN,IAAI,CAACO,GAAG,CAAC,IAAI,CAACR,aAAa,CAACS,gBAAgB,CAACC,SAAS,CAACC,GAAG,IAAG;MAEhEC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;IAClB,CAAC,CAAC,CAAC;EACL;EAEAG,YAAYA,CAAA;IACV,IAAI,CAACT,YAAY,GAAGU,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACX,YAAY,GAAG,CAAC,CAAC;IACtD,IAAI,CAACL,aAAa,CAACiB,mBAAmB,CAAC,IAAI,CAACZ,YAAY,CAAC;IACzD;IACA,IAAI,CAACD,eAAe,CAACc,IAAI,CAAC,IAAI,CAAChB,SAAS,CAAC,IAAI,CAACG,YAAY,CAAC,CAACc,GAAG,CAAC;EAClE;EAEAC,YAAYA,CAAA;IACV,MAAMC,SAAS,GAAGN,IAAI,CAACO,GAAG,CAAC,IAAI,CAACpB,SAAS,CAACqB,MAAM,GAAG,CAAC,EAAE,IAAI,CAAClB,YAAY,GAAG,CAAC,CAAC;IAC5E,IAAI,IAAI,CAACH,SAAS,CAACmB,SAAS,CAAC,CAACG,QAAQ,EAAE;MACtC,OAAO,KAAK;IACd,CAAC,MAAM;MAEL,IAAI,CAACnB,YAAY,GAAGgB,SAAS;MAC7B,IAAI,CAACrB,aAAa,CAACiB,mBAAmB,CAAC,IAAI,CAACZ,YAAY,CAAC;MACzD,IAAI,CAACC,YAAY,GAAI,IAAI,CAACJ,SAAS,CAAC,IAAI,CAACG,YAAY,CAAC,CAACoB,KAAM;MAC7D,IAAI,CAACrB,eAAe,CAACc,IAAI,CAAC,IAAI,CAAChB,SAAS,CAAC,IAAI,CAACG,YAAY,CAAC,CAACc,GAAG,CAAC;MAChE,OAAO,KAAK;IACd;IACA;EACF;EAEAO,eAAeA,CAAA;IACb,OAAO,IAAI,CAACrB,YAAY,KAAK,CAAC;EAChC;EAAC,QAAAsB,CAAA,G;qBA7CU9B,uBAAuB,EAAA+B,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAvBrC,uBAAuB;IAAAsC,SAAA;IAAAC,MAAA;MAAAlC,SAAA;MAAAC,mBAAA;IAAA;IAAAkC,OAAA;MAAAjC,eAAA;IAAA;IAAAkC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCR5Bf,EAHR,CAAAiB,cAAA,0BACuE,aACI,WAC0D;QAA1HjB,EAAA,CAAAkB,UAAA,mBAAAC,oDAAA;UAAA,OAASH,GAAA,CAAA9B,YAAA,EAAc;QAAA,EAAC;QAAkGc,EAAA,CAAAoB,YAAA,EAAI;QACjIpB,EAAA,CAAAiB,cAAA,YAAoD;QAAAjB,EAAA,CAAAqB,MAAA,GAAmC;QAAArB,EAAA,CAAAoB,YAAA,EAAK;QAC5FpB,EAAA,CAAAiB,cAAA,WAA+H;QAAzEjB,EAAA,CAAAkB,UAAA,mBAAAI,oDAAA;UAAA,OAASN,GAAA,CAAAxB,YAAA,EAAc;QAAA,EAAC;QAEtFQ,EAFuI,CAAAoB,YAAA,EAAI,EACjI,EACS;;;QAJiBpB,EAAA,CAAAuB,SAAA,GAAiD;QAAjDvB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAyB,eAAA,IAAAC,GAAA,GAAAV,GAAA,CAAAlB,eAAA,IAAiD;QACzBE,EAAA,CAAAuB,SAAA,GAAmC;QAAnCvB,EAAA,CAAA2B,iBAAA,CAAAX,GAAA,CAAA1C,SAAA,CAAA0C,GAAA,CAAAvC,YAAA,EAAAoB,KAAA,CAAmC;QACpFG,EAAA,CAAAuB,SAAA,EAAkD;QAAlDvB,EAAA,CAAAwB,UAAA,YAAAxB,EAAA,CAAAyB,eAAA,IAAAC,GAAA,EAAAV,GAAA,CAAAzC,mBAAA,EAAkD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}