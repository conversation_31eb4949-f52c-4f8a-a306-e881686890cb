{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Version, Injectable } from '@angular/core';\nimport { getApps } from 'firebase/app';\nimport { queueScheduler, asyncScheduler, Observable } from 'rxjs';\nimport { tap, observeOn, subscribeOn } from 'rxjs/operators';\nconst VERSION = /*#__PURE__*/new Version('ANGULARFIRE2_VERSION');\nconst ɵisSupportedError = module => `The APP_INITIALIZER that is \"making\" isSupported() sync for the sake of convenient DI has not resolved in this\ncontext. Rather than injecting ${module} in the constructor, first ensure that ${module} is supported by calling\n\\`await isSupported()\\`, then retrieve the instance from the injector manually \\`injector.get(${module})\\`.`;\nfunction ɵgetDefaultInstanceOf(identifier, provided, defaultApp) {\n  if (provided) {\n    // Was provide* only called once? If so grab that\n    if (provided.length === 1) {\n      return provided[0];\n    }\n    const providedUsingDefaultApp = provided.filter(it => it.app === defaultApp);\n    // Was provide* only called once, using the default app? If so use that\n    if (providedUsingDefaultApp.length === 1) {\n      return providedUsingDefaultApp[0];\n    }\n  }\n  // Grab the default instance from the defaultApp\n  const defaultAppWithContainer = defaultApp;\n  const provider = defaultAppWithContainer.container.getProvider(identifier);\n  return provider.getImmediate({\n    optional: true\n  });\n}\nconst ɵgetAllInstancesOf = (identifier, app) => {\n  const apps = app ? [app] : getApps();\n  const instances = [];\n  apps.forEach(app => {\n    const provider = app.container.getProvider(identifier);\n    provider.instances.forEach(instance => {\n      if (!instances.includes(instance)) {\n        instances.push(instance);\n      }\n    });\n  });\n  return instances;\n};\nclass ɵAppCheckInstances {\n  constructor() {\n    return ɵgetAllInstancesOf(ɵAPP_CHECK_PROVIDER_NAME);\n  }\n}\nconst ɵAPP_CHECK_PROVIDER_NAME = 'app-check';\n\n/* eslint-disable @typescript-eslint/ban-ts-comment */\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nfunction noop() {}\n/**\n * Schedules tasks so that they are invoked inside the Zone that is passed in the constructor.\n */\nclass ɵZoneScheduler {\n  zone;\n  delegate;\n  constructor(zone, delegate = queueScheduler) {\n    this.zone = zone;\n    this.delegate = delegate;\n  }\n  now() {\n    return this.delegate.now();\n  }\n  schedule(work, delay, state) {\n    const targetZone = this.zone;\n    // Wrap the specified work function to make sure that if nested scheduling takes place the\n    // work is executed in the correct zone\n    const workInZone = function (state) {\n      targetZone.runGuarded(() => {\n        work.apply(this, [state]);\n      });\n    };\n    // Scheduling itself needs to be run in zone to ensure setInterval calls for async scheduling are done\n    // inside the correct zone. This scheduler needs to schedule asynchronously always to ensure that\n    // firebase emissions are never synchronous. Specifying a delay causes issues with the queueScheduler delegate.\n    return this.delegate.schedule(workInZone, delay, state);\n  }\n}\nclass BlockUntilFirstOperator {\n  zone;\n  // @ts-ignore\n  task = null;\n  constructor(zone) {\n    this.zone = zone;\n  }\n  call(subscriber, source) {\n    const unscheduleTask = this.unscheduleTask.bind(this);\n    // @ts-ignore\n    this.task = this.zone.run(() => Zone.current.scheduleMacroTask('firebaseZoneBlock', noop, {}, noop, noop));\n    return source.pipe(tap({\n      next: unscheduleTask,\n      complete: unscheduleTask,\n      error: unscheduleTask\n    })).subscribe(subscriber).add(unscheduleTask);\n  }\n  unscheduleTask() {\n    // maybe this is a race condition, invoke in a timeout\n    // hold for 10ms while I try to figure out what is going on\n    setTimeout(() => {\n      if (this.task != null && this.task.state === 'scheduled') {\n        this.task.invoke();\n        this.task = null;\n      }\n    }, 10);\n  }\n}\nlet ɵAngularFireSchedulers = /*#__PURE__*/(() => {\n  class ɵAngularFireSchedulers {\n    ngZone;\n    outsideAngular;\n    insideAngular;\n    constructor(ngZone) {\n      this.ngZone = ngZone;\n      // @ts-ignore\n      this.outsideAngular = ngZone.runOutsideAngular(() => new ɵZoneScheduler(Zone.current));\n      // @ts-ignore\n      this.insideAngular = ngZone.run(() => new ɵZoneScheduler(Zone.current, asyncScheduler));\n      globalThis.ɵAngularFireScheduler ||= this;\n    }\n    static ɵfac = function ɵAngularFireSchedulers_Factory(t) {\n      return new (t || ɵAngularFireSchedulers)(i0.ɵɵinject(i0.NgZone));\n    };\n    static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ɵAngularFireSchedulers,\n      factory: ɵAngularFireSchedulers.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return ɵAngularFireSchedulers;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nfunction getSchedulers() {\n  const schedulers = globalThis.ɵAngularFireScheduler;\n  if (!schedulers) {\n    throw new Error(`Either AngularFireModule has not been provided in your AppModule (this can be done manually or implictly using\nprovideFirebaseApp) or you're calling an AngularFire method outside of an NgModule (which is not supported).`);\n  }\n  return schedulers;\n}\nfunction runOutsideAngular(fn) {\n  return getSchedulers().ngZone.runOutsideAngular(() => fn());\n}\nfunction run(fn) {\n  return getSchedulers().ngZone.run(() => fn());\n}\nfunction observeOutsideAngular(obs$) {\n  return obs$.pipe(observeOn(getSchedulers().outsideAngular));\n}\nfunction observeInsideAngular(obs$) {\n  return obs$.pipe(observeOn(getSchedulers().insideAngular));\n}\nfunction keepUnstableUntilFirst(obs$) {\n  return ɵkeepUnstableUntilFirstFactory(getSchedulers())(obs$);\n}\n/**\n * Operator to block the zone until the first value has been emitted or the observable\n * has completed/errored. This is used to make sure that universal waits until the first\n * value from firebase but doesn't block the zone forever since the firebase subscription\n * is still alive.\n */\nfunction ɵkeepUnstableUntilFirstFactory(schedulers) {\n  return function keepUnstableUntilFirst(obs$) {\n    obs$ = obs$.lift(new BlockUntilFirstOperator(schedulers.ngZone));\n    return obs$.pipe(\n    // Run the subscribe body outside of Angular (e.g. calling Firebase SDK to add a listener to a change event)\n    subscribeOn(schedulers.outsideAngular),\n    // Run operators inside the angular zone (e.g. side effects via tap())\n    observeOn(schedulers.insideAngular)\n    // INVESTIGATE https://github.com/angular/angularfire/pull/2315\n    // share()\n    );\n  };\n}\n// @ts-ignore\nconst zoneWrapFn = (it, macrotask) => {\n  // eslint-disable-next-line @typescript-eslint/no-this-alias\n  const _this = this;\n  // function() is needed for the arguments object\n  return function () {\n    const _arguments = arguments;\n    if (macrotask) {\n      setTimeout(() => {\n        if (macrotask.state === 'scheduled') {\n          macrotask.invoke();\n        }\n      }, 10);\n    }\n    return run(() => it.apply(_this, _arguments));\n  };\n};\nconst ɵzoneWrap = (it, blockUntilFirst) => {\n  // function() is needed for the arguments object\n  return function () {\n    // @ts-ignore\n    let macrotask;\n    const _arguments = arguments;\n    // if this is a callback function, e.g, onSnapshot, we should create a microtask and invoke it\n    // only once one of the callback functions is tripped.\n    for (let i = 0; i < arguments.length; i++) {\n      if (typeof _arguments[i] === 'function') {\n        if (blockUntilFirst) {\n          // @ts-ignore\n          macrotask ||= run(() => Zone.current.scheduleMacroTask('firebaseZoneBlock', noop, {}, noop, noop));\n        }\n        // TODO create a microtask to track callback functions\n        _arguments[i] = zoneWrapFn(_arguments[i], macrotask);\n      }\n    }\n    const ret = runOutsideAngular(() => it.apply(this, _arguments));\n    if (!blockUntilFirst) {\n      if (ret instanceof Observable) {\n        const schedulers = getSchedulers();\n        return ret.pipe(subscribeOn(schedulers.outsideAngular), observeOn(schedulers.insideAngular));\n      } else {\n        return run(() => ret);\n      }\n    }\n    if (ret instanceof Observable) {\n      return ret.pipe(keepUnstableUntilFirst);\n    } else if (ret instanceof Promise) {\n      // eslint-disable-next-line @typescript-eslint/no-misused-promises\n      return run(() => new Promise((resolve, reject) => ret.then(it => run(() => resolve(it)), reason => run(() => reject(reason)))));\n    } else if (typeof ret === 'function' && macrotask) {\n      // Handle unsubscribe\n      // function() is needed for the arguments object\n      return function () {\n        setTimeout(() => {\n          if (macrotask && macrotask.state === 'scheduled') {\n            macrotask.invoke();\n          }\n        }, 10);\n        return ret.apply(this, arguments);\n      };\n    } else {\n      // TODO how do we handle storage uploads in Zone? and other stuff with cancel() etc?\n      return run(() => ret);\n    }\n  };\n};\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { VERSION, keepUnstableUntilFirst, observeInsideAngular, observeOutsideAngular, ɵAPP_CHECK_PROVIDER_NAME, ɵAngularFireSchedulers, ɵAppCheckInstances, ɵZoneScheduler, ɵgetAllInstancesOf, ɵgetDefaultInstanceOf, ɵisSupportedError, ɵkeepUnstableUntilFirstFactory, ɵzoneWrap };", "map": {"version": 3, "names": ["i0", "Version", "Injectable", "getApps", "queueScheduler", "asyncScheduler", "Observable", "tap", "observeOn", "subscribeOn", "VERSION", "ɵisSupportedError", "module", "ɵgetDefaultInstanceOf", "identifier", "provided", "defaultApp", "length", "providedUsingDefaultApp", "filter", "it", "app", "defaultAppWithContainer", "provider", "container", "get<PERSON><PERSON><PERSON>", "getImmediate", "optional", "ɵgetAllInstancesOf", "apps", "instances", "for<PERSON>ach", "instance", "includes", "push", "ɵAppCheckInstances", "constructor", "ɵAPP_CHECK_PROVIDER_NAME", "noop", "ɵZoneScheduler", "zone", "delegate", "now", "schedule", "work", "delay", "state", "targetZone", "workInZone", "runGuarded", "apply", "BlockUntilFirstOperator", "task", "call", "subscriber", "source", "unscheduleTask", "bind", "run", "Zone", "current", "scheduleMacroTask", "pipe", "next", "complete", "error", "subscribe", "add", "setTimeout", "invoke", "ɵAngularFireSchedulers", "ngZone", "outsideAngular", "insideAngular", "runOutsideAngular", "globalThis", "ɵAngularFireScheduler", "ɵfac", "ɵAngularFireSchedulers_Factory", "t", "ɵɵinject", "NgZone", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "getSchedulers", "schedulers", "Error", "fn", "observeOutsideAngular", "obs$", "observeInsideAngular", "keepUnstableUntilFirst", "ɵkeepUnstableUntilFirstFactory", "lift", "zoneWrapFn", "macrotask", "_this", "_arguments", "arguments", "ɵzoneWrap", "blockUntilFirst", "i", "ret", "Promise", "resolve", "reject", "then", "reason"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@angular/fire/fesm2022/angular-fire.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Version, Injectable } from '@angular/core';\nimport { getApps } from 'firebase/app';\nimport { queueScheduler, asyncScheduler, Observable } from 'rxjs';\nimport { tap, observeOn, subscribeOn } from 'rxjs/operators';\n\nconst VERSION = new Version('ANGULARFIRE2_VERSION');\nconst ɵisSupportedError = (module) => `The APP_INITIALIZER that is \"making\" isSupported() sync for the sake of convenient DI has not resolved in this\ncontext. Rather than injecting ${module} in the constructor, first ensure that ${module} is supported by calling\n\\`await isSupported()\\`, then retrieve the instance from the injector manually \\`injector.get(${module})\\`.`;\nfunction ɵgetDefaultInstanceOf(identifier, provided, defaultApp) {\n    if (provided) {\n        // Was provide* only called once? If so grab that\n        if (provided.length === 1) {\n            return provided[0];\n        }\n        const providedUsingDefaultApp = provided.filter((it) => it.app === defaultApp);\n        // Was provide* only called once, using the default app? If so use that\n        if (providedUsingDefaultApp.length === 1) {\n            return providedUsingDefaultApp[0];\n        }\n    }\n    // Grab the default instance from the defaultApp\n    const defaultAppWithContainer = defaultApp;\n    const provider = defaultAppWithContainer.container.getProvider(identifier);\n    return provider.getImmediate({ optional: true });\n}\nconst ɵgetAllInstancesOf = (identifier, app) => {\n    const apps = app ? [app] : getApps();\n    const instances = [];\n    apps.forEach((app) => {\n        const provider = app.container.getProvider(identifier);\n        provider.instances.forEach((instance) => {\n            if (!instances.includes(instance)) {\n                instances.push(instance);\n            }\n        });\n    });\n    return instances;\n};\nclass ɵAppCheckInstances {\n    constructor() {\n        return ɵgetAllInstancesOf(ɵAPP_CHECK_PROVIDER_NAME);\n    }\n}\nconst ɵAPP_CHECK_PROVIDER_NAME = 'app-check';\n\n/* eslint-disable @typescript-eslint/ban-ts-comment */\n// eslint-disable-next-line @typescript-eslint/no-empty-function\nfunction noop() {\n}\n/**\n * Schedules tasks so that they are invoked inside the Zone that is passed in the constructor.\n */\nclass ɵZoneScheduler {\n    zone;\n    delegate;\n    constructor(zone, delegate = queueScheduler) {\n        this.zone = zone;\n        this.delegate = delegate;\n    }\n    now() {\n        return this.delegate.now();\n    }\n    schedule(work, delay, state) {\n        const targetZone = this.zone;\n        // Wrap the specified work function to make sure that if nested scheduling takes place the\n        // work is executed in the correct zone\n        const workInZone = function (state) {\n            targetZone.runGuarded(() => {\n                work.apply(this, [state]);\n            });\n        };\n        // Scheduling itself needs to be run in zone to ensure setInterval calls for async scheduling are done\n        // inside the correct zone. This scheduler needs to schedule asynchronously always to ensure that\n        // firebase emissions are never synchronous. Specifying a delay causes issues with the queueScheduler delegate.\n        return this.delegate.schedule(workInZone, delay, state);\n    }\n}\nclass BlockUntilFirstOperator {\n    zone;\n    // @ts-ignore\n    task = null;\n    constructor(zone) {\n        this.zone = zone;\n    }\n    call(subscriber, source) {\n        const unscheduleTask = this.unscheduleTask.bind(this);\n        // @ts-ignore\n        this.task = this.zone.run(() => Zone.current.scheduleMacroTask('firebaseZoneBlock', noop, {}, noop, noop));\n        return source.pipe(tap({ next: unscheduleTask, complete: unscheduleTask, error: unscheduleTask })).subscribe(subscriber).add(unscheduleTask);\n    }\n    unscheduleTask() {\n        // maybe this is a race condition, invoke in a timeout\n        // hold for 10ms while I try to figure out what is going on\n        setTimeout(() => {\n            if (this.task != null && this.task.state === 'scheduled') {\n                this.task.invoke();\n                this.task = null;\n            }\n        }, 10);\n    }\n}\nclass ɵAngularFireSchedulers {\n    ngZone;\n    outsideAngular;\n    insideAngular;\n    constructor(ngZone) {\n        this.ngZone = ngZone;\n        // @ts-ignore\n        this.outsideAngular = ngZone.runOutsideAngular(() => new ɵZoneScheduler(Zone.current));\n        // @ts-ignore\n        this.insideAngular = ngZone.run(() => new ɵZoneScheduler(Zone.current, asyncScheduler));\n        globalThis.ɵAngularFireScheduler ||= this;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.0.6\", ngImport: i0, type: ɵAngularFireSchedulers, deps: [{ token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable });\n    static ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.0.6\", ngImport: i0, type: ɵAngularFireSchedulers, providedIn: 'root' });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.0.6\", ngImport: i0, type: ɵAngularFireSchedulers, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: () => [{ type: i0.NgZone }] });\nfunction getSchedulers() {\n    const schedulers = globalThis.ɵAngularFireScheduler;\n    if (!schedulers) {\n        throw new Error(`Either AngularFireModule has not been provided in your AppModule (this can be done manually or implictly using\nprovideFirebaseApp) or you're calling an AngularFire method outside of an NgModule (which is not supported).`);\n    }\n    return schedulers;\n}\nfunction runOutsideAngular(fn) {\n    return getSchedulers().ngZone.runOutsideAngular(() => fn());\n}\nfunction run(fn) {\n    return getSchedulers().ngZone.run(() => fn());\n}\nfunction observeOutsideAngular(obs$) {\n    return obs$.pipe(observeOn(getSchedulers().outsideAngular));\n}\nfunction observeInsideAngular(obs$) {\n    return obs$.pipe(observeOn(getSchedulers().insideAngular));\n}\nfunction keepUnstableUntilFirst(obs$) {\n    return ɵkeepUnstableUntilFirstFactory(getSchedulers())(obs$);\n}\n/**\n * Operator to block the zone until the first value has been emitted or the observable\n * has completed/errored. This is used to make sure that universal waits until the first\n * value from firebase but doesn't block the zone forever since the firebase subscription\n * is still alive.\n */\nfunction ɵkeepUnstableUntilFirstFactory(schedulers) {\n    return function keepUnstableUntilFirst(obs$) {\n        obs$ = obs$.lift(new BlockUntilFirstOperator(schedulers.ngZone));\n        return obs$.pipe(\n        // Run the subscribe body outside of Angular (e.g. calling Firebase SDK to add a listener to a change event)\n        subscribeOn(schedulers.outsideAngular), \n        // Run operators inside the angular zone (e.g. side effects via tap())\n        observeOn(schedulers.insideAngular)\n        // INVESTIGATE https://github.com/angular/angularfire/pull/2315\n        // share()\n        );\n    };\n}\n// @ts-ignore\nconst zoneWrapFn = (it, macrotask) => {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const _this = this;\n    // function() is needed for the arguments object\n    return function () {\n        const _arguments = arguments;\n        if (macrotask) {\n            setTimeout(() => {\n                if (macrotask.state === 'scheduled') {\n                    macrotask.invoke();\n                }\n            }, 10);\n        }\n        return run(() => it.apply(_this, _arguments));\n    };\n};\nconst ɵzoneWrap = (it, blockUntilFirst) => {\n    // function() is needed for the arguments object\n    return function () {\n        // @ts-ignore\n        let macrotask;\n        const _arguments = arguments;\n        // if this is a callback function, e.g, onSnapshot, we should create a microtask and invoke it\n        // only once one of the callback functions is tripped.\n        for (let i = 0; i < arguments.length; i++) {\n            if (typeof _arguments[i] === 'function') {\n                if (blockUntilFirst) {\n                    // @ts-ignore\n                    macrotask ||= run(() => Zone.current.scheduleMacroTask('firebaseZoneBlock', noop, {}, noop, noop));\n                }\n                // TODO create a microtask to track callback functions\n                _arguments[i] = zoneWrapFn(_arguments[i], macrotask);\n            }\n        }\n        const ret = runOutsideAngular(() => it.apply(this, _arguments));\n        if (!blockUntilFirst) {\n            if (ret instanceof Observable) {\n                const schedulers = getSchedulers();\n                return ret.pipe(subscribeOn(schedulers.outsideAngular), observeOn(schedulers.insideAngular));\n            }\n            else {\n                return run(() => ret);\n            }\n        }\n        if (ret instanceof Observable) {\n            return ret.pipe(keepUnstableUntilFirst);\n        }\n        else if (ret instanceof Promise) {\n            // eslint-disable-next-line @typescript-eslint/no-misused-promises\n            return run(() => new Promise((resolve, reject) => ret.then(it => run(() => resolve(it)), reason => run(() => reject(reason)))));\n        }\n        else if (typeof ret === 'function' && macrotask) {\n            // Handle unsubscribe\n            // function() is needed for the arguments object\n            return function () {\n                setTimeout(() => {\n                    if (macrotask && macrotask.state === 'scheduled') {\n                        macrotask.invoke();\n                    }\n                }, 10);\n                return ret.apply(this, arguments);\n            };\n        }\n        else {\n            // TODO how do we handle storage uploads in Zone? and other stuff with cancel() etc?\n            return run(() => ret);\n        }\n    };\n};\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { VERSION, keepUnstableUntilFirst, observeInsideAngular, observeOutsideAngular, ɵAPP_CHECK_PROVIDER_NAME, ɵAngularFireSchedulers, ɵAppCheckInstances, ɵZoneScheduler, ɵgetAllInstancesOf, ɵgetDefaultInstanceOf, ɵisSupportedError, ɵkeepUnstableUntilFirstFactory, ɵzoneWrap };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,OAAO,EAAEC,UAAU,QAAQ,eAAe;AACnD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,cAAc,EAAEC,cAAc,EAAEC,UAAU,QAAQ,MAAM;AACjE,SAASC,GAAG,EAAEC,SAAS,EAAEC,WAAW,QAAQ,gBAAgB;AAE5D,MAAMC,OAAO,gBAAG,IAAIT,OAAO,CAAC,sBAAsB,CAAC;AACnD,MAAMU,iBAAiB,GAAIC,MAAM,IAAM;AACvC,iCAAiCA,MAAO,0CAAyCA,MAAO;AACxF,gGAAgGA,MAAO,MAAK;AAC5G,SAASC,qBAAqBA,CAACC,UAAU,EAAEC,QAAQ,EAAEC,UAAU,EAAE;EAC7D,IAAID,QAAQ,EAAE;IACV;IACA,IAAIA,QAAQ,CAACE,MAAM,KAAK,CAAC,EAAE;MACvB,OAAOF,QAAQ,CAAC,CAAC,CAAC;IACtB;IACA,MAAMG,uBAAuB,GAAGH,QAAQ,CAACI,MAAM,CAAEC,EAAE,IAAKA,EAAE,CAACC,GAAG,KAAKL,UAAU,CAAC;IAC9E;IACA,IAAIE,uBAAuB,CAACD,MAAM,KAAK,CAAC,EAAE;MACtC,OAAOC,uBAAuB,CAAC,CAAC,CAAC;IACrC;EACJ;EACA;EACA,MAAMI,uBAAuB,GAAGN,UAAU;EAC1C,MAAMO,QAAQ,GAAGD,uBAAuB,CAACE,SAAS,CAACC,WAAW,CAACX,UAAU,CAAC;EAC1E,OAAOS,QAAQ,CAACG,YAAY,CAAC;IAAEC,QAAQ,EAAE;EAAK,CAAC,CAAC;AACpD;AACA,MAAMC,kBAAkB,GAAGA,CAACd,UAAU,EAAEO,GAAG,KAAK;EAC5C,MAAMQ,IAAI,GAAGR,GAAG,GAAG,CAACA,GAAG,CAAC,GAAGlB,OAAO,CAAC,CAAC;EACpC,MAAM2B,SAAS,GAAG,EAAE;EACpBD,IAAI,CAACE,OAAO,CAAEV,GAAG,IAAK;IAClB,MAAME,QAAQ,GAAGF,GAAG,CAACG,SAAS,CAACC,WAAW,CAACX,UAAU,CAAC;IACtDS,QAAQ,CAACO,SAAS,CAACC,OAAO,CAAEC,QAAQ,IAAK;MACrC,IAAI,CAACF,SAAS,CAACG,QAAQ,CAACD,QAAQ,CAAC,EAAE;QAC/BF,SAAS,CAACI,IAAI,CAACF,QAAQ,CAAC;MAC5B;IACJ,CAAC,CAAC;EACN,CAAC,CAAC;EACF,OAAOF,SAAS;AACpB,CAAC;AACD,MAAMK,kBAAkB,CAAC;EACrBC,WAAWA,CAAA,EAAG;IACV,OAAOR,kBAAkB,CAACS,wBAAwB,CAAC;EACvD;AACJ;AACA,MAAMA,wBAAwB,GAAG,WAAW;;AAE5C;AACA;AACA,SAASC,IAAIA,CAAA,EAAG,CAChB;AACA;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACjBC,IAAI;EACJC,QAAQ;EACRL,WAAWA,CAACI,IAAI,EAAEC,QAAQ,GAAGrC,cAAc,EAAE;IACzC,IAAI,CAACoC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC5B;EACAC,GAAGA,CAAA,EAAG;IACF,OAAO,IAAI,CAACD,QAAQ,CAACC,GAAG,CAAC,CAAC;EAC9B;EACAC,QAAQA,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAE;IACzB,MAAMC,UAAU,GAAG,IAAI,CAACP,IAAI;IAC5B;IACA;IACA,MAAMQ,UAAU,GAAG,SAAAA,CAAUF,KAAK,EAAE;MAChCC,UAAU,CAACE,UAAU,CAAC,MAAM;QACxBL,IAAI,CAACM,KAAK,CAAC,IAAI,EAAE,CAACJ,KAAK,CAAC,CAAC;MAC7B,CAAC,CAAC;IACN,CAAC;IACD;IACA;IACA;IACA,OAAO,IAAI,CAACL,QAAQ,CAACE,QAAQ,CAACK,UAAU,EAAEH,KAAK,EAAEC,KAAK,CAAC;EAC3D;AACJ;AACA,MAAMK,uBAAuB,CAAC;EAC1BX,IAAI;EACJ;EACAY,IAAI,GAAG,IAAI;EACXhB,WAAWA,CAACI,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACAa,IAAIA,CAACC,UAAU,EAAEC,MAAM,EAAE;IACrB,MAAMC,cAAc,GAAG,IAAI,CAACA,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IACrD;IACA,IAAI,CAACL,IAAI,GAAG,IAAI,CAACZ,IAAI,CAACkB,GAAG,CAAC,MAAMC,IAAI,CAACC,OAAO,CAACC,iBAAiB,CAAC,mBAAmB,EAAEvB,IAAI,EAAE,CAAC,CAAC,EAAEA,IAAI,EAAEA,IAAI,CAAC,CAAC;IAC1G,OAAOiB,MAAM,CAACO,IAAI,CAACvD,GAAG,CAAC;MAAEwD,IAAI,EAAEP,cAAc;MAAEQ,QAAQ,EAAER,cAAc;MAAES,KAAK,EAAET;IAAe,CAAC,CAAC,CAAC,CAACU,SAAS,CAACZ,UAAU,CAAC,CAACa,GAAG,CAACX,cAAc,CAAC;EAChJ;EACAA,cAAcA,CAAA,EAAG;IACb;IACA;IACAY,UAAU,CAAC,MAAM;MACb,IAAI,IAAI,CAAChB,IAAI,IAAI,IAAI,IAAI,IAAI,CAACA,IAAI,CAACN,KAAK,KAAK,WAAW,EAAE;QACtD,IAAI,CAACM,IAAI,CAACiB,MAAM,CAAC,CAAC;QAClB,IAAI,CAACjB,IAAI,GAAG,IAAI;MACpB;IACJ,CAAC,EAAE,EAAE,CAAC;EACV;AACJ;AAAC,IACKkB,sBAAsB;EAA5B,MAAMA,sBAAsB,CAAC;IACzBC,MAAM;IACNC,cAAc;IACdC,aAAa;IACbrC,WAAWA,CAACmC,MAAM,EAAE;MAChB,IAAI,CAACA,MAAM,GAAGA,MAAM;MACpB;MACA,IAAI,CAACC,cAAc,GAAGD,MAAM,CAACG,iBAAiB,CAAC,MAAM,IAAInC,cAAc,CAACoB,IAAI,CAACC,OAAO,CAAC,CAAC;MACtF;MACA,IAAI,CAACa,aAAa,GAAGF,MAAM,CAACb,GAAG,CAAC,MAAM,IAAInB,cAAc,CAACoB,IAAI,CAACC,OAAO,EAAEvD,cAAc,CAAC,CAAC;MACvFsE,UAAU,CAACC,qBAAqB,KAAK,IAAI;IAC7C;IACA,OAAOC,IAAI,YAAAC,+BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFT,sBAAsB,EAAhCtE,EAAE,CAAAgF,QAAA,CAAgDhF,EAAE,CAACiF,MAAM;IAAA;IACpJ,OAAOC,KAAK,kBAD6ElF,EAAE,CAAAmF,kBAAA;MAAAC,KAAA,EACYd,sBAAsB;MAAAe,OAAA,EAAtBf,sBAAsB,CAAAO,IAAA;MAAAS,UAAA,EAAc;IAAM;EACrJ;EAAC,OAdKhB,sBAAsB;AAAA;AAe5B;EAAA,QAAAiB,SAAA,oBAAAA,SAAA;AAAA;AAMA,SAASC,aAAaA,CAAA,EAAG;EACrB,MAAMC,UAAU,GAAGd,UAAU,CAACC,qBAAqB;EACnD,IAAI,CAACa,UAAU,EAAE;IACb,MAAM,IAAIC,KAAK,CAAE;AACzB,6GAA6G,CAAC;EAC1G;EACA,OAAOD,UAAU;AACrB;AACA,SAASf,iBAAiBA,CAACiB,EAAE,EAAE;EAC3B,OAAOH,aAAa,CAAC,CAAC,CAACjB,MAAM,CAACG,iBAAiB,CAAC,MAAMiB,EAAE,CAAC,CAAC,CAAC;AAC/D;AACA,SAASjC,GAAGA,CAACiC,EAAE,EAAE;EACb,OAAOH,aAAa,CAAC,CAAC,CAACjB,MAAM,CAACb,GAAG,CAAC,MAAMiC,EAAE,CAAC,CAAC,CAAC;AACjD;AACA,SAASC,qBAAqBA,CAACC,IAAI,EAAE;EACjC,OAAOA,IAAI,CAAC/B,IAAI,CAACtD,SAAS,CAACgF,aAAa,CAAC,CAAC,CAAChB,cAAc,CAAC,CAAC;AAC/D;AACA,SAASsB,oBAAoBA,CAACD,IAAI,EAAE;EAChC,OAAOA,IAAI,CAAC/B,IAAI,CAACtD,SAAS,CAACgF,aAAa,CAAC,CAAC,CAACf,aAAa,CAAC,CAAC;AAC9D;AACA,SAASsB,sBAAsBA,CAACF,IAAI,EAAE;EAClC,OAAOG,8BAA8B,CAACR,aAAa,CAAC,CAAC,CAAC,CAACK,IAAI,CAAC;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,8BAA8BA,CAACP,UAAU,EAAE;EAChD,OAAO,SAASM,sBAAsBA,CAACF,IAAI,EAAE;IACzCA,IAAI,GAAGA,IAAI,CAACI,IAAI,CAAC,IAAI9C,uBAAuB,CAACsC,UAAU,CAAClB,MAAM,CAAC,CAAC;IAChE,OAAOsB,IAAI,CAAC/B,IAAI;IAChB;IACArD,WAAW,CAACgF,UAAU,CAACjB,cAAc,CAAC;IACtC;IACAhE,SAAS,CAACiF,UAAU,CAAChB,aAAa;IAClC;IACA;IACA,CAAC;EACL,CAAC;AACL;AACA;AACA,MAAMyB,UAAU,GAAGA,CAAC9E,EAAE,EAAE+E,SAAS,KAAK;EAClC;EACA,MAAMC,KAAK,GAAG,IAAI;EAClB;EACA,OAAO,YAAY;IACf,MAAMC,UAAU,GAAGC,SAAS;IAC5B,IAAIH,SAAS,EAAE;MACX/B,UAAU,CAAC,MAAM;QACb,IAAI+B,SAAS,CAACrD,KAAK,KAAK,WAAW,EAAE;UACjCqD,SAAS,CAAC9B,MAAM,CAAC,CAAC;QACtB;MACJ,CAAC,EAAE,EAAE,CAAC;IACV;IACA,OAAOX,GAAG,CAAC,MAAMtC,EAAE,CAAC8B,KAAK,CAACkD,KAAK,EAAEC,UAAU,CAAC,CAAC;EACjD,CAAC;AACL,CAAC;AACD,MAAME,SAAS,GAAGA,CAACnF,EAAE,EAAEoF,eAAe,KAAK;EACvC;EACA,OAAO,YAAY;IACf;IACA,IAAIL,SAAS;IACb,MAAME,UAAU,GAAGC,SAAS;IAC5B;IACA;IACA,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,CAACrF,MAAM,EAAEwF,CAAC,EAAE,EAAE;MACvC,IAAI,OAAOJ,UAAU,CAACI,CAAC,CAAC,KAAK,UAAU,EAAE;QACrC,IAAID,eAAe,EAAE;UACjB;UACAL,SAAS,KAAKzC,GAAG,CAAC,MAAMC,IAAI,CAACC,OAAO,CAACC,iBAAiB,CAAC,mBAAmB,EAAEvB,IAAI,EAAE,CAAC,CAAC,EAAEA,IAAI,EAAEA,IAAI,CAAC,CAAC;QACtG;QACA;QACA+D,UAAU,CAACI,CAAC,CAAC,GAAGP,UAAU,CAACG,UAAU,CAACI,CAAC,CAAC,EAAEN,SAAS,CAAC;MACxD;IACJ;IACA,MAAMO,GAAG,GAAGhC,iBAAiB,CAAC,MAAMtD,EAAE,CAAC8B,KAAK,CAAC,IAAI,EAAEmD,UAAU,CAAC,CAAC;IAC/D,IAAI,CAACG,eAAe,EAAE;MAClB,IAAIE,GAAG,YAAYpG,UAAU,EAAE;QAC3B,MAAMmF,UAAU,GAAGD,aAAa,CAAC,CAAC;QAClC,OAAOkB,GAAG,CAAC5C,IAAI,CAACrD,WAAW,CAACgF,UAAU,CAACjB,cAAc,CAAC,EAAEhE,SAAS,CAACiF,UAAU,CAAChB,aAAa,CAAC,CAAC;MAChG,CAAC,MACI;QACD,OAAOf,GAAG,CAAC,MAAMgD,GAAG,CAAC;MACzB;IACJ;IACA,IAAIA,GAAG,YAAYpG,UAAU,EAAE;MAC3B,OAAOoG,GAAG,CAAC5C,IAAI,CAACiC,sBAAsB,CAAC;IAC3C,CAAC,MACI,IAAIW,GAAG,YAAYC,OAAO,EAAE;MAC7B;MACA,OAAOjD,GAAG,CAAC,MAAM,IAAIiD,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAKH,GAAG,CAACI,IAAI,CAAC1F,EAAE,IAAIsC,GAAG,CAAC,MAAMkD,OAAO,CAACxF,EAAE,CAAC,CAAC,EAAE2F,MAAM,IAAIrD,GAAG,CAAC,MAAMmD,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IACnI,CAAC,MACI,IAAI,OAAOL,GAAG,KAAK,UAAU,IAAIP,SAAS,EAAE;MAC7C;MACA;MACA,OAAO,YAAY;QACf/B,UAAU,CAAC,MAAM;UACb,IAAI+B,SAAS,IAAIA,SAAS,CAACrD,KAAK,KAAK,WAAW,EAAE;YAC9CqD,SAAS,CAAC9B,MAAM,CAAC,CAAC;UACtB;QACJ,CAAC,EAAE,EAAE,CAAC;QACN,OAAOqC,GAAG,CAACxD,KAAK,CAAC,IAAI,EAAEoD,SAAS,CAAC;MACrC,CAAC;IACL,CAAC,MACI;MACD;MACA,OAAO5C,GAAG,CAAC,MAAMgD,GAAG,CAAC;IACzB;EACJ,CAAC;AACL,CAAC;;AAED;AACA;AACA;;AAEA,SAAShG,OAAO,EAAEqF,sBAAsB,EAAED,oBAAoB,EAAEF,qBAAqB,EAAEvD,wBAAwB,EAAEiC,sBAAsB,EAAEnC,kBAAkB,EAAEI,cAAc,EAAEX,kBAAkB,EAAEf,qBAAqB,EAAEF,iBAAiB,EAAEqF,8BAA8B,EAAEO,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}