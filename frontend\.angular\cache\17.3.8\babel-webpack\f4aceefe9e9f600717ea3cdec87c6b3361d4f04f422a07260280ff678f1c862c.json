{"ast": null, "code": "export var UserStatus = /*#__PURE__*/function (UserStatus) {\n  UserStatus[\"ACTIVE\"] = \"Active\";\n  return UserStatus;\n}(UserStatus || {});\nexport var UserRole = /*#__PURE__*/function (UserRole) {\n  UserRole[\"TEACHER\"] = \"Teacher\";\n  UserRole[\"STUDENT\"] = \"Student\";\n  UserRole[\"NONE\"] = \"\"; // for initialization\n  return UserRole;\n}(UserRole || {});\nexport var OffStatus = /*#__PURE__*/function (OffStatus) {\n  OffStatus[\"PENDING\"] = \"Pending\";\n  OffStatus[\"REJECTED\"] = \"Rejected\";\n  OffStatus[\"ACCEPTED\"] = \"Accepted\";\n  return OffStatus;\n}(OffStatus || {});\nexport var UserAvailabilityType = /*#__PURE__*/function (UserAvailabilityType) {\n  UserAvailabilityType[\"OFF\"] = \"off\";\n  UserAvailabilityType[\"AVAILABLE\"] = \"Available\";\n  return UserAvailabilityType;\n}(UserAvailabilityType || {});", "map": {"version": 3, "names": ["UserStatus", "UserRole", "OffStatus", "UserAvailabilityType"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\models\\user.model.ts"], "sourcesContent": ["export interface AuthData {\r\n    username: string;\r\n    password: string;\r\n}\r\n\r\nexport interface User {\r\n    id: string,\r\n    aspUserId?: string,\r\n    email: string,\r\n    personalEmail?: string,\r\n    skype: string,\r\n    firstName: string,\r\n    lastName: string,\r\n    phone?: string, // TODO MAKE STRING\r\n    bdate?: string,\r\n    dateOfBirth?: string,\r\n    photo?: string,\r\n    avatarUrl?: string,\r\n    token?: string,\r\n    role?: string,\r\n    availability?: UserAvailability, // TODO API CALL\r\n    off?: UserAvailabilityOff[], // TODO API CALL\r\n    origin?: string,\r\n    residence?: string,\r\n    timeZone?: string,\r\n    introduce?: string,\r\n    hours_per_week: string, // TODO GET FROM AVAILABILITY (ADD TO UserAvailability)\r\n    status: UserStatus.ACTIVE,\r\n    teachingLanguage?: string,\r\n    userName?: string,\r\n    accSlitPayments?: boolean,\r\n    lingoletteId: string\r\n}\r\n\r\nexport enum UserStatus {\r\n    ACTIVE = \"Active\"\r\n}\r\n\r\nexport enum UserRole {\r\n    TEACHER = \"Teacher\",\r\n    STUDENT = \"Student\",\r\n    NONE = \"\" // for initialization\r\n}\r\n\r\nexport interface UserAvailability {\r\n    id: string,\r\n    type: UserAvailabilityType.OFF | UserAvailabilityType.AVAILABLE,\r\n    mon: UserAvailabilityHours[],\r\n    tue: UserAvailabilityHours[],\r\n    wed: UserAvailabilityHours[],\r\n    thu: UserAvailabilityHours[],\r\n    fri: UserAvailabilityHours[],\r\n    sat: UserAvailabilityHours[],\r\n    sun: UserAvailabilityHours[],\r\n}\r\n\r\nexport interface UserAvailabilityOff {\r\n    id?: number,\r\n    reason: string,\r\n    period: UserAvailabilityOffDate,\r\n    status: OffStatus\r\n}\r\n\r\nexport enum OffStatus {\r\n    PENDING = 'Pending',\r\n    REJECTED = 'Rejected',\r\n    ACCEPTED = 'Accepted'\r\n}\r\n\r\nexport enum UserAvailabilityType {\r\n    OFF = \"off\",\r\n    AVAILABLE = \"Available\"\r\n}\r\n\r\nexport interface UserAvailabilityHours {\r\n    from: string,\r\n    to : string\r\n}\r\n\r\nexport interface UserAvailabilityOffDate {\r\n    from: Date,\r\n    to : Date\r\n}\r\n\r\nexport interface RequestTrialRequest {\r\n    firstName: string;\r\n    lastName: string;\r\n    contactNumber: string;\r\n    email: string;\r\n    nativeLanguage: string;\r\n    wantToLearnLanguage: string;\r\n    country: string;\r\n    timeZone: string;\r\n    numberOfStudents: number;\r\n    findUs: string;\r\n    availability: string;\r\n    message: string;\r\n}\r\n"], "mappings": "AAkCA,WAAYA,UAEX,gBAFD,UAAYA,UAAU;EAClBA,UAAA,qBAAiB;EAAA,OADTA,UAAU;AAEtB,CAAC,CAFWA,UAAU,OAErB;AAED,WAAYC,QAIX,gBAJD,UAAYA,QAAQ;EAChBA,QAAA,uBAAmB;EACnBA,QAAA,uBAAmB;EACnBA,QAAA,aAAS,EAAC;EAAA,OAHFA,QAAQ;AAIpB,CAAC,CAJWA,QAAQ,OAInB;AAqBD,WAAYC,SAIX,gBAJD,UAAYA,SAAS;EACjBA,SAAA,uBAAmB;EACnBA,SAAA,yBAAqB;EACrBA,SAAA,yBAAqB;EAAA,OAHbA,SAAS;AAIrB,CAAC,CAJWA,SAAS,OAIpB;AAED,WAAYC,oBAGX,gBAHD,UAAYA,oBAAoB;EAC5BA,oBAAA,eAAW;EACXA,oBAAA,2BAAuB;EAAA,OAFfA,oBAAoB;AAGhC,CAAC,CAHWA,oBAAoB,OAG/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}