{"ast": null, "code": "import { PackagesModel, PackageType } from \"src/app/core/models/package.model\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"primeng/radiobutton\";\nconst _c0 = (a0, a1) => ({\n  \"surface-border\": a0,\n  \"border-primary\": a1\n});\nconst _c1 = (a0, a1) => ({\n  \"surface-50 text-600 surface-border\": a0,\n  \"bg-primary-reverse\": a1\n});\nconst _c2 = () => [];\nfunction BuyPackageSuggestionBoxComponent_div_9_i_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"i\", 23);\n  }\n}\nfunction BuyPackageSuggestionBoxComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵlistener(\"click\", function BuyPackageSuggestionBoxComponent_div_9_Template_div_click_0_listener() {\n      const pkg_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.selectPackage(pkg_r2.type));\n    });\n    i0.ɵɵelementStart(1, \"p-radioButton\", 17);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuyPackageSuggestionBoxComponent_div_9_Template_p_radioButton_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedPackage, $event) || (ctx_r2.selectedPackage = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 18)(3, \"div\", 19);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 20);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 21);\n    i0.ɵɵtemplate(8, BuyPackageSuggestionBoxComponent_div_9_i_8_Template, 1, 0, \"i\", 22);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const pkg_r2 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(7, _c0, ctx_r2.selectedPackage !== pkg_r2.type, ctx_r2.selectedPackage === pkg_r2.type));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", ctx_r2.packageTypeEnum.FLEX);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedPackage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(pkg_r2.type);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Cost: \", pkg_r2.costPlus, \" USD\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(10, _c1, ctx_r2.selectedPackage !== pkg_r2.type, ctx_r2.selectedPackage === pkg_r2.type));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpureFunction0(13, _c2).constructor(i_r4 + 1));\n  }\n}\nexport class BuyPackageSuggestionBoxComponent {\n  constructor() {\n    this.defaultPackages = PackagesModel.getDefaultPackages();\n    this.selectedPackage = this.defaultPackages[0].type;\n    this.packageTypeEnum = PackageType;\n  }\n  selectPackage(type) {\n    this.selectedPackage = type;\n  }\n  getPackages() {\n    return this.defaultPackages.filter(p => p.type !== 'Regular');\n  }\n  static #_ = this.ɵfac = function BuyPackageSuggestionBoxComponent_Factory(t) {\n    return new (t || BuyPackageSuggestionBoxComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: BuyPackageSuggestionBoxComponent,\n    selectors: [[\"app-buy-package-suggestion-box\"]],\n    decls: 20,\n    vars: 1,\n    consts: [[1, \"col-12\", \"p-2\"], [1, \"shadow-2\", \"border-round\", \"surface-card\", \"mb-3\", \"h-full\", \"flex-column\", \"justify-content-between\", \"flex\"], [1, \"p-3\"], [1, \"flex\", \"align-items-center\"], [1, \"inline-flex\", \"border-circle\", \"align-items-center\", \"justify-content-center\", \"bg-green-100\", \"mr-3\", \"mb-2\", 2, \"width\", \"38px\", \"height\", \"38px\"], [1, \"pi\", \"pi-globe\", \"text-xl\", \"text-green-600\"], [1, \"text-900\", \"font-medium\", \"text-2xl\"], [1, \"flex\", \"flex-row\", \"align-items-center\", \"justify-content-center\"], [\"class\", \"surface-card border-2 p-3 flex align-items-center cursor-pointer\", 3, \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"text-900\", \"my-3\", \"text-xl\", \"font-medium\"], [1, \"mt-0\", \"mb-3\", \"text-700\", \"line-height-3\"], [1, \"px-4\", \"py-3\", \"surface-100\", \"text-right\"], [\"pbutton\", \"\", \"pripple\", \"\", \"icon\", \"pi pi-arrow-right\", \"iconpos\", \"right\", \"label\", \"More\", 1, \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button-success\", \"p-button\", \"p-component\"], [\"aria-hidden\", \"true\", 1, \"p-button-icon\", \"p-button-icon-right\", \"pi\", \"pi-arrow-right\"], [1, \"p-button-label\"], [1, \"p-ink\"], [1, \"surface-card\", \"border-2\", \"p-3\", \"flex\", \"align-items-center\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [\"name\", \"packageType\", \"styleClass\", \"mr-3\", 3, \"ngModelChange\", \"value\", \"ngModel\"], [1, \"mr-4\", \"md:mr-8\"], [1, \"font-medium\", \"mb-1\"], [1, \"text-sm\"], [1, \"border-round\", \"border-1\", \"p-1\", \"ml-auto\", \"flex\", \"flex-nowrap\", 3, \"ngClass\"], [\"class\", \"pi pi-star-fill\", 4, \"ngFor\", \"ngForOf\"], [1, \"pi\", \"pi-star-fill\"]],\n    template: function BuyPackageSuggestionBoxComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"span\", 4);\n        i0.ɵɵelement(5, \"i\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"span\", 6);\n        i0.ɵɵtext(7, \"Card Title\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(8, \"div\", 7);\n        i0.ɵɵtemplate(9, BuyPackageSuggestionBoxComponent_div_9_Template, 9, 14, \"div\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"div\", 9);\n        i0.ɵɵtext(11, \"Quam adipiscing vitae proin sagittis.\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"p\", 10);\n        i0.ɵɵtext(13, \" Eget sit amet tellus cras adipiscing enim. At quis risus sed vulputate odio. Proin libero nunc consequat interdum varius sit amet. \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"div\", 11)(15, \"button\", 12);\n        i0.ɵɵelement(16, \"span\", 13);\n        i0.ɵɵelementStart(17, \"span\", 14);\n        i0.ɵɵtext(18, \"Upgrade to Premium\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(19, \"span\", 15);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngForOf\", ctx.getPackages());\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgForOf, i2.NgControlStatus, i2.NgModel, i3.RadioButton],\n    styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9jbGFzc3Jvb20vcGFja2FnZXMvYnV5LXBhY2thZ2UvYnV5LXBhY2thZ2Utc3VnZ2VzdGlvbi1ib3gvYnV5LXBhY2thZ2Utc3VnZ2VzdGlvbi1ib3guY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGNBQWM7QUFDaEIiLCJzb3VyY2VzQ29udGVudCI6WyI6aG9zdCB7XG4gIGRpc3BsYXk6IGJsb2NrO1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"],\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["PackagesModel", "PackageType", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵlistener", "BuyPackageSuggestionBoxComponent_div_9_Template_div_click_0_listener", "pkg_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "selectPackage", "type", "ɵɵtwoWayListener", "BuyPackageSuggestionBoxComponent_div_9_Template_p_radioButton_ngModelChange_1_listener", "$event", "ɵɵtwoWayBindingSet", "selected<PERSON><PERSON><PERSON>", "ɵɵelementEnd", "ɵɵtext", "ɵɵtemplate", "BuyPackageSuggestionBoxComponent_div_9_i_8_Template", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "ɵɵadvance", "packageTypeEnum", "FLEX", "ɵɵtwoWayProperty", "ɵɵtextInterpolate", "ɵɵtextInterpolate1", "costPlus", "_c1", "ɵɵpureFunction0", "_c2", "constructor", "i_r4", "BuyPackageSuggestionBoxComponent", "defaultPackages", "getDefaultPackages", "getPackages", "filter", "p", "_", "_2", "selectors", "decls", "vars", "consts", "template", "BuyPackageSuggestionBoxComponent_Template", "rf", "ctx", "BuyPackageSuggestionBoxComponent_div_9_Template"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\buy-package\\buy-package-suggestion-box\\buy-package-suggestion-box.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\buy-package\\buy-package-suggestion-box\\buy-package-suggestion-box.component.html"], "sourcesContent": ["import { CommonModule } from \"@angular/common\";\nimport { ChangeDetectionStrategy, Component } from '@angular/core';\nimport { PackagesModel, PackageType } from \"src/app/core/models/package.model\";\n\n@Component({\n    selector: 'app-buy-package-suggestion-box',\n    templateUrl: './buy-package-suggestion-box.component.html',\n    styleUrl: './buy-package-suggestion-box.component.css',\n    changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class BuyPackageSuggestionBoxComponent {\n\n    public defaultPackages = PackagesModel.getDefaultPackages();\n\n    selectedPackage = this.defaultPackages[0].type;\n    packageTypeEnum = PackageType;\n\n    public selectPackage(type: any) {\n        this.selectedPackage = type;\n    }\n\n    getPackages() {\n        return this.defaultPackages.filter(p => p.type !== 'Regular');\n    }\n}\n", "<div class=\"col-12 p-2\">\r\n    <div class=\"shadow-2 border-round surface-card mb-3 h-full flex-column justify-content-between flex\">\r\n        <div class=\"p-3\">\r\n            <div class=\"flex align-items-center\">\r\n                <span class=\"inline-flex border-circle align-items-center justify-content-center bg-green-100 mr-3 mb-2\"\r\n                    style=\"width: 38px; height: 38px;\">\r\n                    <i class=\"pi pi-globe text-xl text-green-600\"></i>\r\n                </span>\r\n                <span class=\"text-900 font-medium text-2xl\">Card Title</span>\r\n            </div>\r\n\r\n            <div class=\"flex flex-row align-items-center justify-content-center\">\r\n                <div *ngFor=\"let pkg of getPackages(); let i = index\"\r\n                    class=\"surface-card border-2 p-3 flex align-items-center cursor-pointer\"\r\n                    [ngClass]=\"{'surface-border': selectedPackage !== pkg.type, 'border-primary': selectedPackage === pkg.type}\"\r\n                    (click)=\"selectPackage(pkg.type)\">\r\n                    <p-radioButton name=\"packageType\" [value]=\"packageTypeEnum.FLEX\" [(ngModel)]=\"selectedPackage\"\r\n                        styleClass=\"mr-3\"></p-radioButton>\r\n                    <div class=\"mr-4 md:mr-8\">\r\n                        <div class=\"font-medium mb-1\">{{ pkg.type }}</div>\r\n                        <span class=\"text-sm\">Cost: {{ pkg.costPlus }} USD</span>\r\n                    </div>\r\n                    <div class=\"border-round border-1 p-1 ml-auto flex flex-nowrap\"\r\n                        [ngClass]=\"{'surface-50 text-600 surface-border': selectedPackage !== pkg.type, 'bg-primary-reverse': selectedPackage === pkg.type}\">\r\n                        <i class=\"pi pi-star-fill\" *ngFor=\"let star of [].constructor(i + 1); let j = index\"></i>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"text-900 my-3 text-xl font-medium\">Quam adipiscing vitae proin sagittis.</div>\r\n            <p class=\"mt-0 mb-3 text-700 line-height-3\">\r\n                Eget sit amet tellus cras adipiscing enim. At quis risus sed vulputate odio. Proin libero nunc consequat\r\n                interdum varius sit amet.\r\n            </p>\r\n        </div>\r\n        <div class=\"px-4 py-3 surface-100 text-right\">\r\n            <button pbutton=\"\" pripple=\"\" icon=\"pi pi-arrow-right\" iconpos=\"right\" label=\"More\"\r\n                class=\"p-element p-ripple p-button-rounded p-button-success p-button p-component\">\r\n                <span class=\"p-button-icon p-button-icon-right pi pi-arrow-right\" aria-hidden=\"true\"></span>\r\n                <span class=\"p-button-label\">Upgrade to Premium</span>\r\n                <span class=\"p-ink\"></span>\r\n            </button>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": "AAEA,SAASA,aAAa,EAAEC,WAAW,QAAQ,mCAAmC;;;;;;;;;;;;;;;;ICsBtDC,EAAA,CAAAC,SAAA,YAAyF;;;;;;IAZjGD,EAAA,CAAAE,cAAA,cAGsC;IAAlCF,EAAA,CAAAG,UAAA,mBAAAC,qEAAA;MAAA,MAAAC,MAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,aAAA,CAAAP,MAAA,CAAAQ,IAAA,CAAuB;IAAA,EAAC;IACjCb,EAAA,CAAAE,cAAA,wBACsB;IAD2CF,EAAA,CAAAc,gBAAA,2BAAAC,uFAAAC,MAAA;MAAAhB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAiB,kBAAA,CAAAR,MAAA,CAAAS,eAAA,EAAAF,MAAA,MAAAP,MAAA,CAAAS,eAAA,GAAAF,MAAA;MAAA,OAAAhB,EAAA,CAAAW,WAAA,CAAAK,MAAA;IAAA,EAA6B;IACxEhB,EAAA,CAAAmB,YAAA,EAAgB;IAElCnB,EADJ,CAAAE,cAAA,cAA0B,cACQ;IAAAF,EAAA,CAAAoB,MAAA,GAAc;IAAApB,EAAA,CAAAmB,YAAA,EAAM;IAClDnB,EAAA,CAAAE,cAAA,eAAsB;IAAAF,EAAA,CAAAoB,MAAA,GAA4B;IACtDpB,EADsD,CAAAmB,YAAA,EAAO,EACvD;IACNnB,EAAA,CAAAE,cAAA,cACyI;IACrIF,EAAA,CAAAqB,UAAA,IAAAC,mDAAA,gBAAqF;IAE7FtB,EADI,CAAAmB,YAAA,EAAM,EACJ;;;;;;IAZFnB,EAAA,CAAAuB,UAAA,YAAAvB,EAAA,CAAAwB,eAAA,IAAAC,GAAA,EAAAhB,MAAA,CAAAS,eAAA,KAAAb,MAAA,CAAAQ,IAAA,EAAAJ,MAAA,CAAAS,eAAA,KAAAb,MAAA,CAAAQ,IAAA,EAA4G;IAE1Eb,EAAA,CAAA0B,SAAA,EAA8B;IAA9B1B,EAAA,CAAAuB,UAAA,UAAAd,MAAA,CAAAkB,eAAA,CAAAC,IAAA,CAA8B;IAAC5B,EAAA,CAAA6B,gBAAA,YAAApB,MAAA,CAAAS,eAAA,CAA6B;IAG5DlB,EAAA,CAAA0B,SAAA,GAAc;IAAd1B,EAAA,CAAA8B,iBAAA,CAAAzB,MAAA,CAAAQ,IAAA,CAAc;IACtBb,EAAA,CAAA0B,SAAA,GAA4B;IAA5B1B,EAAA,CAAA+B,kBAAA,WAAA1B,MAAA,CAAA2B,QAAA,SAA4B;IAGlDhC,EAAA,CAAA0B,SAAA,EAAoI;IAApI1B,EAAA,CAAAuB,UAAA,YAAAvB,EAAA,CAAAwB,eAAA,KAAAS,GAAA,EAAAxB,MAAA,CAAAS,eAAA,KAAAb,MAAA,CAAAQ,IAAA,EAAAJ,MAAA,CAAAS,eAAA,KAAAb,MAAA,CAAAQ,IAAA,EAAoI;IACxFb,EAAA,CAAA0B,SAAA,EAA0B;IAA1B1B,EAAA,CAAAuB,UAAA,YAAAvB,EAAA,CAAAkC,eAAA,KAAAC,GAAA,EAAAC,WAAA,CAAAC,IAAA,MAA0B;;;ADd9F,OAAM,MAAOC,gCAAgC;EAN7CF,YAAA;IAQW,KAAAG,eAAe,GAAGzC,aAAa,CAAC0C,kBAAkB,EAAE;IAE3D,KAAAtB,eAAe,GAAG,IAAI,CAACqB,eAAe,CAAC,CAAC,CAAC,CAAC1B,IAAI;IAC9C,KAAAc,eAAe,GAAG5B,WAAW;;EAEtBa,aAAaA,CAACC,IAAS;IAC1B,IAAI,CAACK,eAAe,GAAGL,IAAI;EAC/B;EAEA4B,WAAWA,CAAA;IACP,OAAO,IAAI,CAACF,eAAe,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC9B,IAAI,KAAK,SAAS,CAAC;EACjE;EAAC,QAAA+B,CAAA,G;qBAbQN,gCAAgC;EAAA;EAAA,QAAAO,EAAA,G;UAAhCP,gCAAgC;IAAAQ,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0CAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCN7BpD,EAJhB,CAAAE,cAAA,aAAwB,aACiF,aAChF,aACwB,cAEM;QACnCF,EAAA,CAAAC,SAAA,WAAkD;QACtDD,EAAA,CAAAmB,YAAA,EAAO;QACPnB,EAAA,CAAAE,cAAA,cAA4C;QAAAF,EAAA,CAAAoB,MAAA,iBAAU;QAC1DpB,EAD0D,CAAAmB,YAAA,EAAO,EAC3D;QAENnB,EAAA,CAAAE,cAAA,aAAqE;QACjEF,EAAA,CAAAqB,UAAA,IAAAiC,+CAAA,kBAGsC;QAY1CtD,EAAA,CAAAmB,YAAA,EAAM;QAENnB,EAAA,CAAAE,cAAA,cAA+C;QAAAF,EAAA,CAAAoB,MAAA,6CAAqC;QAAApB,EAAA,CAAAmB,YAAA,EAAM;QAC1FnB,EAAA,CAAAE,cAAA,aAA4C;QACxCF,EAAA,CAAAoB,MAAA,4IAEJ;QACJpB,EADI,CAAAmB,YAAA,EAAI,EACF;QAEFnB,EADJ,CAAAE,cAAA,eAA8C,kBAE4C;QAClFF,EAAA,CAAAC,SAAA,gBAA4F;QAC5FD,EAAA,CAAAE,cAAA,gBAA6B;QAAAF,EAAA,CAAAoB,MAAA,0BAAkB;QAAApB,EAAA,CAAAmB,YAAA,EAAO;QACtDnB,EAAA,CAAAC,SAAA,gBAA2B;QAI3CD,EAHY,CAAAmB,YAAA,EAAS,EACP,EACJ,EACJ;;;QAhC+BnB,EAAA,CAAA0B,SAAA,GAAkB;QAAlB1B,EAAA,CAAAuB,UAAA,YAAA8B,GAAA,CAAAZ,WAAA,GAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}