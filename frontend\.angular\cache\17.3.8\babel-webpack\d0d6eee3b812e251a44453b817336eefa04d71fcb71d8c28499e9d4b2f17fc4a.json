{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, booleanAttribute, Directive, Optional, Input, Output, HostListener, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as i1 from '@angular/forms';\nimport * as i2 from 'primeng/api';\n\n/**\n * InputTextarea adds styling and autoResize functionality to standard textarea element.\n * @group Components\n */\nlet InputTextarea = /*#__PURE__*/(() => {\n  class InputTextarea {\n    el;\n    ngModel;\n    control;\n    cd;\n    config;\n    /**\n     * When present, textarea size changes as being typed.\n     * @group Props\n     */\n    autoResize;\n    /**\n     * Specifies the input variant of the component.\n     * @group Props\n     */\n    variant = 'outlined';\n    /**\n     * Callback to invoke on textarea resize.\n     * @param {(Event | {})} event - Custom resize event.\n     * @group Emits\n     */\n    onResize = new EventEmitter();\n    filled;\n    cachedScrollHeight;\n    ngModelSubscription;\n    ngControlSubscription;\n    constructor(el, ngModel, control, cd, config) {\n      this.el = el;\n      this.ngModel = ngModel;\n      this.control = control;\n      this.cd = cd;\n      this.config = config;\n    }\n    ngOnInit() {\n      if (this.ngModel) {\n        this.ngModelSubscription = this.ngModel.valueChanges.subscribe(() => {\n          this.updateState();\n        });\n      }\n      if (this.control) {\n        this.ngControlSubscription = this.control.valueChanges.subscribe(() => {\n          this.updateState();\n        });\n      }\n    }\n    ngAfterViewInit() {\n      if (this.autoResize) this.resize();\n      this.updateFilledState();\n      this.cd.detectChanges();\n    }\n    onInput(e) {\n      this.updateState();\n    }\n    updateFilledState() {\n      this.filled = this.el.nativeElement.value && this.el.nativeElement.value.length;\n    }\n    resize(event) {\n      this.el.nativeElement.style.height = 'auto';\n      this.el.nativeElement.style.height = this.el.nativeElement.scrollHeight + 'px';\n      if (parseFloat(this.el.nativeElement.style.height) >= parseFloat(this.el.nativeElement.style.maxHeight)) {\n        this.el.nativeElement.style.overflowY = 'scroll';\n        this.el.nativeElement.style.height = this.el.nativeElement.style.maxHeight;\n      } else {\n        this.el.nativeElement.style.overflow = 'hidden';\n      }\n      this.onResize.emit(event || {});\n    }\n    updateState() {\n      this.updateFilledState();\n      if (this.autoResize) {\n        this.resize();\n      }\n    }\n    ngOnDestroy() {\n      if (this.ngModelSubscription) {\n        this.ngModelSubscription.unsubscribe();\n      }\n      if (this.ngControlSubscription) {\n        this.ngControlSubscription.unsubscribe();\n      }\n    }\n    static ɵfac = function InputTextarea_Factory(t) {\n      return new (t || InputTextarea)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.NgModel, 8), i0.ɵɵdirectiveInject(i1.NgControl, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.PrimeNGConfig));\n    };\n    static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: InputTextarea,\n      selectors: [[\"\", \"pInputTextarea\", \"\"]],\n      hostAttrs: [1, \"p-inputtextarea\", \"p-inputtext\", \"p-component\", \"p-element\"],\n      hostVars: 6,\n      hostBindings: function InputTextarea_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"input\", function InputTextarea_input_HostBindingHandler($event) {\n            return ctx.onInput($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"p-filled\", ctx.filled)(\"p-inputtextarea-resizable\", ctx.autoResize)(\"p-variant-filled\", ctx.variant === \"filled\" || ctx.config.inputStyle() === \"filled\");\n        }\n      },\n      inputs: {\n        autoResize: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autoResize\", \"autoResize\", booleanAttribute],\n        variant: \"variant\"\n      },\n      outputs: {\n        onResize: \"onResize\"\n      },\n      features: [i0.ɵɵInputTransformsFeature]\n    });\n  }\n  return InputTextarea;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet InputTextareaModule = /*#__PURE__*/(() => {\n  class InputTextareaModule {\n    static ɵfac = function InputTextareaModule_Factory(t) {\n      return new (t || InputTextareaModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: InputTextareaModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule]\n    });\n  }\n  return InputTextareaModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { InputTextarea, InputTextareaModule };", "map": {"version": 3, "names": ["i0", "EventEmitter", "booleanAttribute", "Directive", "Optional", "Input", "Output", "HostListener", "NgModule", "CommonModule", "i1", "i2", "InputTextarea", "el", "ngModel", "control", "cd", "config", "autoResize", "variant", "onResize", "filled", "cachedScrollHeight", "ngModelSubscription", "ngControlSubscription", "constructor", "ngOnInit", "valueChanges", "subscribe", "updateState", "ngAfterViewInit", "resize", "updateFilledState", "detectChanges", "onInput", "e", "nativeElement", "value", "length", "event", "style", "height", "scrollHeight", "parseFloat", "maxHeight", "overflowY", "overflow", "emit", "ngOnDestroy", "unsubscribe", "ɵfac", "InputTextarea_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "NgModel", "NgControl", "ChangeDetectorRef", "PrimeNGConfig", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "InputTextarea_HostBindings", "rf", "ctx", "ɵɵlistener", "InputTextarea_input_HostBindingHandler", "$event", "ɵɵclassProp", "inputStyle", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "outputs", "features", "ɵɵInputTransformsFeature", "ngDevMode", "InputTextareaModule", "InputTextareaModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/primeng/fesm2022/primeng-inputtextarea.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, booleanAttribute, Directive, Optional, Input, Output, HostListener, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport * as i1 from '@angular/forms';\nimport * as i2 from 'primeng/api';\n\n/**\n * InputTextarea adds styling and autoResize functionality to standard textarea element.\n * @group Components\n */\nclass InputTextarea {\n    el;\n    ngModel;\n    control;\n    cd;\n    config;\n    /**\n     * When present, textarea size changes as being typed.\n     * @group Props\n     */\n    autoResize;\n    /**\n     * Specifies the input variant of the component.\n     * @group Props\n     */\n    variant = 'outlined';\n    /**\n     * Callback to invoke on textarea resize.\n     * @param {(Event | {})} event - Custom resize event.\n     * @group Emits\n     */\n    onResize = new EventEmitter();\n    filled;\n    cachedScrollHeight;\n    ngModelSubscription;\n    ngControlSubscription;\n    constructor(el, ngModel, control, cd, config) {\n        this.el = el;\n        this.ngModel = ngModel;\n        this.control = control;\n        this.cd = cd;\n        this.config = config;\n    }\n    ngOnInit() {\n        if (this.ngModel) {\n            this.ngModelSubscription = this.ngModel.valueChanges.subscribe(() => {\n                this.updateState();\n            });\n        }\n        if (this.control) {\n            this.ngControlSubscription = this.control.valueChanges.subscribe(() => {\n                this.updateState();\n            });\n        }\n    }\n    ngAfterViewInit() {\n        if (this.autoResize)\n            this.resize();\n        this.updateFilledState();\n        this.cd.detectChanges();\n    }\n    onInput(e) {\n        this.updateState();\n    }\n    updateFilledState() {\n        this.filled = this.el.nativeElement.value && this.el.nativeElement.value.length;\n    }\n    resize(event) {\n        this.el.nativeElement.style.height = 'auto';\n        this.el.nativeElement.style.height = this.el.nativeElement.scrollHeight + 'px';\n        if (parseFloat(this.el.nativeElement.style.height) >= parseFloat(this.el.nativeElement.style.maxHeight)) {\n            this.el.nativeElement.style.overflowY = 'scroll';\n            this.el.nativeElement.style.height = this.el.nativeElement.style.maxHeight;\n        }\n        else {\n            this.el.nativeElement.style.overflow = 'hidden';\n        }\n        this.onResize.emit(event || {});\n    }\n    updateState() {\n        this.updateFilledState();\n        if (this.autoResize) {\n            this.resize();\n        }\n    }\n    ngOnDestroy() {\n        if (this.ngModelSubscription) {\n            this.ngModelSubscription.unsubscribe();\n        }\n        if (this.ngControlSubscription) {\n            this.ngControlSubscription.unsubscribe();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: InputTextarea, deps: [{ token: i0.ElementRef }, { token: i1.NgModel, optional: true }, { token: i1.NgControl, optional: true }, { token: i0.ChangeDetectorRef }, { token: i2.PrimeNGConfig }], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.3.7\", type: InputTextarea, selector: \"[pInputTextarea]\", inputs: { autoResize: [\"autoResize\", \"autoResize\", booleanAttribute], variant: \"variant\" }, outputs: { onResize: \"onResize\" }, host: { listeners: { \"input\": \"onInput($event)\" }, properties: { \"class.p-filled\": \"filled\", \"class.p-inputtextarea-resizable\": \"autoResize\", \"class.p-variant-filled\": \"variant === \\\"filled\\\" || config.inputStyle() === \\\"filled\\\"\" }, classAttribute: \"p-inputtextarea p-inputtext p-component p-element\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: InputTextarea, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[pInputTextarea]',\n                    host: {\n                        class: 'p-inputtextarea p-inputtext p-component p-element',\n                        '[class.p-filled]': 'filled',\n                        '[class.p-inputtextarea-resizable]': 'autoResize',\n                        '[class.p-variant-filled]': 'variant === \"filled\" || config.inputStyle() === \"filled\"'\n                    }\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i1.NgModel, decorators: [{\n                    type: Optional\n                }] }, { type: i1.NgControl, decorators: [{\n                    type: Optional\n                }] }, { type: i0.ChangeDetectorRef }, { type: i2.PrimeNGConfig }], propDecorators: { autoResize: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], variant: [{\n                type: Input\n            }], onResize: [{\n                type: Output\n            }], onInput: [{\n                type: HostListener,\n                args: ['input', ['$event']]\n            }] } });\nclass InputTextareaModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: InputTextareaModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.7\", ngImport: i0, type: InputTextareaModule, declarations: [InputTextarea], imports: [CommonModule], exports: [InputTextarea] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: InputTextareaModule, imports: [CommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: InputTextareaModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [InputTextarea],\n                    declarations: [InputTextarea]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { InputTextarea, InputTextareaModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AAC1H,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,OAAO,KAAKC,EAAE,MAAM,aAAa;;AAEjC;AACA;AACA;AACA;AAHA,IAIMC,aAAa;EAAnB,MAAMA,aAAa,CAAC;IAChBC,EAAE;IACFC,OAAO;IACPC,OAAO;IACPC,EAAE;IACFC,MAAM;IACN;AACJ;AACA;AACA;IACIC,UAAU;IACV;AACJ;AACA;AACA;IACIC,OAAO,GAAG,UAAU;IACpB;AACJ;AACA;AACA;AACA;IACIC,QAAQ,GAAG,IAAInB,YAAY,CAAC,CAAC;IAC7BoB,MAAM;IACNC,kBAAkB;IAClBC,mBAAmB;IACnBC,qBAAqB;IACrBC,WAAWA,CAACZ,EAAE,EAAEC,OAAO,EAAEC,OAAO,EAAEC,EAAE,EAAEC,MAAM,EAAE;MAC1C,IAAI,CAACJ,EAAE,GAAGA,EAAE;MACZ,IAAI,CAACC,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACC,EAAE,GAAGA,EAAE;MACZ,IAAI,CAACC,MAAM,GAAGA,MAAM;IACxB;IACAS,QAAQA,CAAA,EAAG;MACP,IAAI,IAAI,CAACZ,OAAO,EAAE;QACd,IAAI,CAACS,mBAAmB,GAAG,IAAI,CAACT,OAAO,CAACa,YAAY,CAACC,SAAS,CAAC,MAAM;UACjE,IAAI,CAACC,WAAW,CAAC,CAAC;QACtB,CAAC,CAAC;MACN;MACA,IAAI,IAAI,CAACd,OAAO,EAAE;QACd,IAAI,CAACS,qBAAqB,GAAG,IAAI,CAACT,OAAO,CAACY,YAAY,CAACC,SAAS,CAAC,MAAM;UACnE,IAAI,CAACC,WAAW,CAAC,CAAC;QACtB,CAAC,CAAC;MACN;IACJ;IACAC,eAAeA,CAAA,EAAG;MACd,IAAI,IAAI,CAACZ,UAAU,EACf,IAAI,CAACa,MAAM,CAAC,CAAC;MACjB,IAAI,CAACC,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAAChB,EAAE,CAACiB,aAAa,CAAC,CAAC;IAC3B;IACAC,OAAOA,CAACC,CAAC,EAAE;MACP,IAAI,CAACN,WAAW,CAAC,CAAC;IACtB;IACAG,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAACX,MAAM,GAAG,IAAI,CAACR,EAAE,CAACuB,aAAa,CAACC,KAAK,IAAI,IAAI,CAACxB,EAAE,CAACuB,aAAa,CAACC,KAAK,CAACC,MAAM;IACnF;IACAP,MAAMA,CAACQ,KAAK,EAAE;MACV,IAAI,CAAC1B,EAAE,CAACuB,aAAa,CAACI,KAAK,CAACC,MAAM,GAAG,MAAM;MAC3C,IAAI,CAAC5B,EAAE,CAACuB,aAAa,CAACI,KAAK,CAACC,MAAM,GAAG,IAAI,CAAC5B,EAAE,CAACuB,aAAa,CAACM,YAAY,GAAG,IAAI;MAC9E,IAAIC,UAAU,CAAC,IAAI,CAAC9B,EAAE,CAACuB,aAAa,CAACI,KAAK,CAACC,MAAM,CAAC,IAAIE,UAAU,CAAC,IAAI,CAAC9B,EAAE,CAACuB,aAAa,CAACI,KAAK,CAACI,SAAS,CAAC,EAAE;QACrG,IAAI,CAAC/B,EAAE,CAACuB,aAAa,CAACI,KAAK,CAACK,SAAS,GAAG,QAAQ;QAChD,IAAI,CAAChC,EAAE,CAACuB,aAAa,CAACI,KAAK,CAACC,MAAM,GAAG,IAAI,CAAC5B,EAAE,CAACuB,aAAa,CAACI,KAAK,CAACI,SAAS;MAC9E,CAAC,MACI;QACD,IAAI,CAAC/B,EAAE,CAACuB,aAAa,CAACI,KAAK,CAACM,QAAQ,GAAG,QAAQ;MACnD;MACA,IAAI,CAAC1B,QAAQ,CAAC2B,IAAI,CAACR,KAAK,IAAI,CAAC,CAAC,CAAC;IACnC;IACAV,WAAWA,CAAA,EAAG;MACV,IAAI,CAACG,iBAAiB,CAAC,CAAC;MACxB,IAAI,IAAI,CAACd,UAAU,EAAE;QACjB,IAAI,CAACa,MAAM,CAAC,CAAC;MACjB;IACJ;IACAiB,WAAWA,CAAA,EAAG;MACV,IAAI,IAAI,CAACzB,mBAAmB,EAAE;QAC1B,IAAI,CAACA,mBAAmB,CAAC0B,WAAW,CAAC,CAAC;MAC1C;MACA,IAAI,IAAI,CAACzB,qBAAqB,EAAE;QAC5B,IAAI,CAACA,qBAAqB,CAACyB,WAAW,CAAC,CAAC;MAC5C;IACJ;IACA,OAAOC,IAAI,YAAAC,sBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFxC,aAAa,EAAvBZ,EAAE,CAAAqD,iBAAA,CAAuCrD,EAAE,CAACsD,UAAU,GAAtDtD,EAAE,CAAAqD,iBAAA,CAAiE3C,EAAE,CAAC6C,OAAO,MAA7EvD,EAAE,CAAAqD,iBAAA,CAAwG3C,EAAE,CAAC8C,SAAS,MAAtHxD,EAAE,CAAAqD,iBAAA,CAAiJrD,EAAE,CAACyD,iBAAiB,GAAvKzD,EAAE,CAAAqD,iBAAA,CAAkL1C,EAAE,CAAC+C,aAAa;IAAA;IAC7R,OAAOC,IAAI,kBAD8E3D,EAAE,CAAA4D,iBAAA;MAAAC,IAAA,EACJjD,aAAa;MAAAkD,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADXnE,EAAE,CAAAqE,UAAA,mBAAAC,uCAAAC,MAAA;YAAA,OACJH,GAAA,CAAAlC,OAAA,CAAAqC,MAAc,CAAC;UAAA,CAAH,CAAC;QAAA;QAAA,IAAAJ,EAAA;UADXnE,EAAE,CAAAwE,WAAA,aAAAJ,GAAA,CAAA/C,MACQ,CAAC,8BAAA+C,GAAA,CAAAlD,UAAD,CAAC,qBAAAkD,GAAA,CAAAjD,OAAA,KAAD,QAAQ,IAAIiD,GAAA,CAAAnD,MAAA,CAAAwD,UAAA,CAAkB,CAAC,KAAK,QAApC,CAAC;QAAA;MAAA;MAAAC,MAAA;QAAAxD,UAAA,GADXlB,EAAE,CAAA2E,YAAA,CAAAC,0BAAA,8BAC4F1E,gBAAgB;QAAAiB,OAAA;MAAA;MAAA0D,OAAA;QAAAzD,QAAA;MAAA;MAAA0D,QAAA,GAD9G9E,EAAE,CAAA+E,wBAAA;IAAA;EAE/F;EAAC,OArFKnE,aAAa;AAAA;AAsFnB;EAAA,QAAAoE,SAAA,oBAAAA,SAAA;AAAA;AAyBoB,IACdC,mBAAmB;EAAzB,MAAMA,mBAAmB,CAAC;IACtB,OAAO/B,IAAI,YAAAgC,4BAAA9B,CAAA;MAAA,YAAAA,CAAA,IAAwF6B,mBAAmB;IAAA;IACtH,OAAOE,IAAI,kBA/B8EnF,EAAE,CAAAoF,gBAAA;MAAAvB,IAAA,EA+BSoB;IAAmB;IACvH,OAAOI,IAAI,kBAhC8ErF,EAAE,CAAAsF,gBAAA;MAAAC,OAAA,GAgCwC9E,YAAY;IAAA;EACnJ;EAAC,OAJKwE,mBAAmB;AAAA;AAKzB;EAAA,QAAAD,SAAA,oBAAAA,SAAA;AAAA;;AASA;AACA;AACA;;AAEA,SAASpE,aAAa,EAAEqE,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}