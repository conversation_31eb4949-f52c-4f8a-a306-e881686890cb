{"ast": null, "code": "import { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/core/services/auth.service\";\nimport * as i3 from \"src/app/core/services/user.service\";\nimport * as i4 from \"src/app/core/services/general.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/tooltip\";\nconst _c0 = () => ({\n  exact: true\n});\nfunction MenuComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"p\", 10);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 11);\n    i0.ɵɵelement(4, \"i\", 12);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.authService.isStudent ? \"Learning\" : \"Teaching\", \" Language:\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.authService.isStudent ? \"Learning\" : ctx_r0.authService.teachingLanguage, \" \");\n  }\n}\nfunction MenuComponent_ng_container_8_div_1_img_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 22);\n  }\n}\nfunction MenuComponent_ng_container_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"a\", 15)(2, \"div\", 16)(3, \"span\", 17);\n    i0.ɵɵelement(4, \"lottie-player\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 19);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 20);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, MenuComponent_ng_container_8_div_1_img_9_Template, 1, 0, \"img\", 21);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"pTooltip\", item_r2.disabled ? \"Access is unavailable at the moment.\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"disabled\", item_r2.disabled);\n    i0.ɵɵproperty(\"tabindex\", item_r2.disabled ? -1 : null)(\"routerLink\", item_r2.url)(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(12, _c0));\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"transform\", \"scale(\" + item_r2.scale + \")\");\n    i0.ɵɵproperty(\"src\", item_r2.lottieIcon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r2.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r2.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.title === \"AI Chat\");\n  }\n}\nfunction MenuComponent_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MenuComponent_ng_container_8_div_1_Template, 10, 13, \"div\", 13);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r2.role.includes(ctx_r0.role));\n  }\n}\nvar LottieInteractivity;\nexport class MenuComponent {\n  constructor(router, authService, userService, generalService, elementRef) {\n    this.router = router;\n    this.authService = authService;\n    this.userService = userService;\n    this.generalService = generalService;\n    this.elementRef = elementRef;\n    this.menuItems = \"\";\n    this.userPhoto = \"\";\n    this.isTrial = false;\n    this.isClassroom = false;\n    this.isTrialRoute = false;\n    this.role = {};\n    this.currentIndex = 0;\n    this.isBelow768 = false;\n    this.subs = new SubSink();\n  }\n  ngOnInit() {\n    this.role = this.authService.getUserRole();\n    this.userPhoto = this.userService.getUserPhoto(this.authService.getLoggedInUser());\n    this.subs.add(this.generalService.deviceKind.subscribe(res => {\n      this.isBelow768 = res.is768;\n    }));\n    this.subs.add(this.userService.update.subscribe(res => {\n      // this.userPhoto = this.authService.getUserPhoto();\n    }));\n  }\n  ngAfterViewInit() {\n    const playerContainers = this.elementRef.nativeElement.querySelectorAll('.hoverEffects');\n    playerContainers.forEach(container => {\n      container.addEventListener('mouseover', () => {\n        if (container) {\n          const player = container.querySelector('lottie-player');\n          if (player) {\n            player.setDirection(1);\n            player.play();\n            player.setLooping(false);\n          }\n        }\n      });\n      container.addEventListener('mouseleave', () => {\n        const player = container.querySelector('lottie-player');\n        player.setDirection(-1);\n        player.play();\n        player.setLooping(false);\n      });\n    });\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  static #_ = this.ɵfac = function MenuComponent_Factory(t) {\n    return new (t || MenuComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.UserService), i0.ɵɵdirectiveInject(i4.GeneralService), i0.ɵɵdirectiveInject(i0.ElementRef));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: MenuComponent,\n    selectors: [[\"app-menu\"]],\n    inputs: {\n      menuItems: \"menuItems\"\n    },\n    decls: 9,\n    vars: 3,\n    consts: [[\"id\", \"left-menu\", 1, \"menu\"], [1, \"first-section\"], [1, \"first-section-content\", \"avatar-circle\"], [1, \"menu-img-profile\", \"min-h-4rem\", 3, \"error\", \"src\"], [\"class\", \"flex-column  hidden lg:block align-items-center justify-content-center below-profile__lang-text\", 4, \"ngIf\"], [1, \"my-hr\"], [1, \"my-hr-content\"], [1, \"second-section\"], [4, \"ngFor\", \"ngForOf\"], [1, \"flex-column\", \"hidden\", \"lg:block\", \"align-items-center\", \"justify-content-center\", \"below-profile__lang-text\"], [1, \"mb-1\", \"text-center\"], [1, \"flex\", \"font-bold\", \"justify-content-center\", \"align-items-center\", \"gap-1\"], [1, \"pi\", \"pi-flag\"], [3, \"pTooltip\", 4, \"ngIf\"], [3, \"pTooltip\"], [\"routerLinkActive\", \"is-active\", 1, \"second-section-item\", \"hoverEffects\", \"relative\", 3, \"tabindex\", \"routerLink\", \"routerLinkActiveOptions\"], [1, \"icon-container\"], [1, \"lottie-wrapper\"], [\"background\", \"transparent\", \"speed\", \"1\", \"loop\", \"\", 3, \"src\"], [1, \"text-container-tablet\"], [1, \"text-container\"], [\"src\", \"/assets/images/dashboard/new.svg\", \"class\", \"new-label\", 4, \"ngIf\"], [\"src\", \"/assets/images/dashboard/new.svg\", 1, \"new-label\"]],\n    template: function MenuComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"img\", 3);\n        i0.ɵɵlistener(\"error\", function MenuComponent_Template_img_error_3_listener($event) {\n          return ctx.generalService.setDefaultUserAvatar($event);\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(4, MenuComponent_div_4_Template, 6, 2, \"div\", 4);\n        i0.ɵɵelementStart(5, \"div\", 5);\n        i0.ɵɵelement(6, \"div\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"div\", 7);\n        i0.ɵɵtemplate(8, MenuComponent_ng_container_8_Template, 2, 1, \"ng-container\", 8);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"src\", ctx.userService.getUserPhoto(ctx.authService.getLoggedInUser()), i0.ɵɵsanitizeUrl);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.authService.isTeacher);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngForOf\", ctx.menuItems);\n      }\n    },\n    dependencies: [i5.NgForOf, i5.NgIf, i6.Tooltip, i1.RouterLink, i1.RouterLinkActive],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.menu[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  box-shadow: 0.125rem 0.3125rem 0.375rem rgba(0, 0, 0, 0.16);\\n  color: var(--white);\\n  padding-top: clamp(1.88rem, 0.78vw + 1.72rem, 2.5rem);\\n  padding-bottom: clamp(1.88rem, 0.78vw + 1.72rem, 2.5rem);\\n  box-shadow: var(--box-shadow-menu);\\n  z-index: 1;\\n  overflow: auto;\\n  border-radius: clamp(1.25rem, 0.16vw + 1.22rem, 1.38rem);\\n  background-image: linear-gradient(180deg, hsl(231, 45%, 50%) 0%, hsl(231, 46%, 48%) 6%, hsl(232, 46%, 45%) 13%, hsl(232, 48%, 43%) 20%, hsl(232, 49%, 40%) 28%, hsl(232, 50%, 37%) 37%, hsl(232, 52%, 35%) 49%, hsl(232, 54%, 32%) 64%, hsl(232, 57%, 30%) 81%, hsl(231, 60%, 27%) 100%);\\n  max-height: 820px;\\n  overflow: hidden;\\n}\\n.menu[_ngcontent-%COMP%]   lottie-player[_ngcontent-%COMP%] {\\n  width: 52px;\\n  height: 53px;\\n}\\n@media screen and (min-height: 400px) and (max-width: 950px) {\\n  .menu[_ngcontent-%COMP%] {\\n    padding-top: 0;\\n    padding-bottom: 0;\\n  }\\n  .menu[_ngcontent-%COMP%]   .menu-img-profile[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .menu[_ngcontent-%COMP%]   lottie-player[_ngcontent-%COMP%] {\\n    display: flex;\\n    width: 50px;\\n    margin: auto;\\n  }\\n  .menu[_ngcontent-%COMP%]   .second-section-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    height: 10.2vh;\\n  }\\n  .menu[_ngcontent-%COMP%]   .si[_ngcontent-%COMP%] {\\n    padding: 0.5rem !important;\\n  }\\n}\\n\\n.disabled[_ngcontent-%COMP%] {\\n  pointer-events: none;\\n  color: #aaa;\\n  opacity: 0.5;\\n}\\n\\n.below-profile__lang-text[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-top: clamp(0.63rem, 0.78vw + 0.47rem, 1.25rem);\\n  font-size: clamp(0.75rem, 0.23vw + 0.7rem, 0.94rem);\\n}\\n\\n.is-active[_ngcontent-%COMP%]   .text-container[_ngcontent-%COMP%] {\\n  color: var(--main-color);\\n}\\n.is-active[_ngcontent-%COMP%]   .lottie-wrapper[_ngcontent-%COMP%] {\\n  mix-blend-mode: luminosity;\\n}\\n.is-active[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%] {\\n  background: linear-gradient(#657aef 0%, #1a266c 100%);\\n  box-shadow: 0rem 0.1875rem 0.375rem rgba(0, 0, 0, 0.16);\\n  border-radius: 1.25rem;\\n}\\n@media only screen and (min-width: 992px) {\\n  .is-active[_ngcontent-%COMP%] {\\n    background-color: var(--white);\\n  }\\n}\\n\\n.first-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.first-section-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  flex-direction: column;\\n  width: auto;\\n}\\n\\n.menu-img-profile[_ngcontent-%COMP%] {\\n  align-self: center;\\n  border-radius: 50%;\\n  width: clamp(3.13rem, 1.64vw + 2.11rem, 3.75rem);\\n}\\n@media only screen and (min-width: 992px) {\\n  .menu-img-profile[_ngcontent-%COMP%] {\\n    width: clamp(6.25rem, 2.47vw + 4.72rem, 7.19rem);\\n  }\\n}\\n\\n.language-text[_ngcontent-%COMP%] {\\n  margin-top: 1.875rem;\\n}\\n\\n.language-teach[_ngcontent-%COMP%] {\\n  margin-top: 0.625rem;\\n  border-radius: 0.3125rem;\\n  border: 0.125rem solid var(--white);\\n  text-align: center;\\n  padding: 0.375rem;\\n}\\n\\n.my-hr[_ngcontent-%COMP%] {\\n  display: none;\\n  justify-content: center;\\n  width: 100%;\\n}\\n\\n.my-hr-content[_ngcontent-%COMP%] {\\n  height: 1px;\\n  width: 70%;\\n  background-color: #fff;\\n  margin: clamp(0.75rem, 0.63vw + 0.63rem, 1.25rem);\\n  opacity: 0.3;\\n}\\n\\n.second-section[_ngcontent-%COMP%] {\\n  margin-top: 0.625rem;\\n}\\n\\n.second-section-item[_ngcontent-%COMP%] {\\n  transition: color 0.2s linear;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  border-radius: 17px;\\n  justify-content: center;\\n  height: 47px;\\n  mix-blend-mode: luminosity;\\n}\\n.second-section-item[_ngcontent-%COMP%]   .text-container[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n.second-section-item[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%] {\\n  width: 90%;\\n  text-align: center;\\n  flex-direction: column;\\n  align-items: center;\\n  display: flex;\\n}\\n.second-section-item[_ngcontent-%COMP%]   .text-container-tablet[_ngcontent-%COMP%] {\\n  display: none;\\n  font-size: 10px;\\n}\\n.second-section-item[_ngcontent-%COMP%]   .lottie-wrapper[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n}\\n.second-section-item[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n  height: auto;\\n  width: clamp(2.63rem, 1.72vw + 2.28rem, 4rem);\\n  padding: clamp(0.75rem, 0.31vw + 0.69rem, 1rem);\\n}\\n\\n@media only screen and (max-width: 992px) and (max-height: 992px) {\\n  .second-section-item[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n    padding: clamp(0.5rem, 0.16vw + 0.47rem, 0.63rem);\\n    height: clamp(2.25rem, 0.94vw + 2.06rem, 3rem);\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .menu[_ngcontent-%COMP%] {\\n    padding-top: clamp(0.63rem, 1.64vw + -0.39rem, 1.25rem);\\n    padding-bottom: clamp(0.63rem, 1.64vw + -0.39rem, 1.25rem);\\n  }\\n  .menu[_ngcontent-%COMP%]   lottie-player[_ngcontent-%COMP%] {\\n    width: 55px;\\n    height: 49px;\\n  }\\n  .second-section[_ngcontent-%COMP%]   .second-section-item[_ngcontent-%COMP%] {\\n    margin-bottom: clamp(0rem, 0.63vw + -0.13rem, 0.5rem);\\n  }\\n  .second-section[_ngcontent-%COMP%]   .text-container[_ngcontent-%COMP%] {\\n    display: block;\\n    font-size: 0.87rem;\\n    padding-left: 0.35rem !important;\\n    padding: 1rem 1rem 0.9375rem 1rem;\\n    width: 100%;\\n    font-family: \\\"Lato Light\\\";\\n  }\\n  .second-section[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%] {\\n    width: auto;\\n    display: flex;\\n    justify-content: center;\\n    border-radius: 0.8rem;\\n    z-index: 1;\\n    height: 51px;\\n  }\\n  .second-section[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]   .text-container-tablet[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n  .second-section[_ngcontent-%COMP%]   .icon-container[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\\n    width: clamp(2rem, 3.41vw + 1.32rem, 3.88rem);\\n    padding: clamp(0.75rem, 0.31vw + 0.69rem, 1rem);\\n  }\\n  .my-hr[_ngcontent-%COMP%] {\\n    display: flex;\\n    justify-content: center;\\n    width: 100%;\\n  }\\n}\\n@media (min-width: 85.375rem) {\\n  .second-section-item[_ngcontent-%COMP%]:hover {\\n    color: var(--light-purple);\\n  }\\n}\\n\\n@media (max-height: 800px) {\\n  .second-section[_ngcontent-%COMP%] {\\n    margin-top: 0;\\n  }\\n  .menu[_ngcontent-%COMP%]   .menu-img-profile[_ngcontent-%COMP%] {\\n    max-height: 11vh;\\n    width: auto;\\n  }\\n  .menu[_ngcontent-%COMP%]   .my-hr-content[_ngcontent-%COMP%] {\\n    margin: 2vh;\\n  }\\n  .icon-container[_ngcontent-%COMP%] {\\n    height: 48px !important;\\n  }\\n}\\n@media (max-height: 660px) {\\n  .below-profile__lang-text[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n@media only screen and (min-width: 1200px) and (min-height: 850px) {\\n  .below-profile__lang-text[_ngcontent-%COMP%] {\\n    display: flex;\\n  }\\n  .menu[_ngcontent-%COMP%] {\\n    min-height: unset;\\n  }\\n}\\n@media screen and (min-width: 1440px) {\\n  .text-container[_ngcontent-%COMP%] {\\n    font-size: 15px !important;\\n  }\\n}\\n.new-label[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 1.7rem;\\n  right: 10px;\\n  top: -0.4rem;\\n  z-index: 2;\\n  opacity: 0;\\n}\\n@media only screen and (min-width: 992px) {\\n  .new-label[_ngcontent-%COMP%] {\\n    opacity: 1;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["SubSink", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "authService", "isStudent", "teachingLanguage", "ɵɵtemplate", "MenuComponent_ng_container_8_div_1_img_9_Template", "ɵɵproperty", "item_r2", "disabled", "ɵɵclassProp", "url", "ɵɵpureFunction0", "_c0", "ɵɵstyleProp", "scale", "lottieIcon", "ɵɵtextInterpolate", "title", "ɵɵelementContainerStart", "MenuComponent_ng_container_8_div_1_Template", "role", "includes", "LottieInteractivity", "MenuComponent", "constructor", "router", "userService", "generalService", "elementRef", "menuItems", "userPhoto", "isTrial", "isClassroom", "isTrialRoute", "currentIndex", "isBelow768", "subs", "ngOnInit", "getUserRole", "getUserPhoto", "getLoggedInUser", "add", "deviceKind", "subscribe", "res", "is768", "update", "ngAfterViewInit", "playerContainers", "nativeElement", "querySelectorAll", "for<PERSON>ach", "container", "addEventListener", "player", "querySelector", "setDirection", "play", "setLooping", "ngOnDestroy", "unsubscribe", "_", "ɵɵdirectiveInject", "i1", "Router", "i2", "AuthService", "i3", "UserService", "i4", "GeneralService", "ElementRef", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "MenuComponent_Template", "rf", "ctx", "ɵɵlistener", "MenuComponent_Template_img_error_3_listener", "$event", "setDefaultUserAvatar", "MenuComponent_div_4_Template", "MenuComponent_ng_container_8_Template", "ɵɵsanitizeUrl", "<PERSON><PERSON><PERSON>er"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\menu\\menu.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\menu\\menu.component.html"], "sourcesContent": ["import { AfterViewInit, Component, ElementRef, Input, OnDestroy, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { UserRole } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { UserService } from 'src/app/core/services/user.service';\r\nimport { SubSink } from 'subsink';\r\n\r\nvar LottieInteractivity: any;\r\n@Component({\r\n  selector: 'app-menu',\r\n  templateUrl: './menu.component.html',\r\n  styleUrls: ['./menu.component.scss']\r\n})\r\nexport class MenuComponent implements OnInit, AfterViewInit, OnDestroy {\r\n  @Input() menuItems: string = \"\";\r\n  public userPhoto: string = \"\";\r\n  public isTrial: boolean = false;\r\n  public isClassroom: boolean = false;\r\n  public isTrialRoute: boolean = false;\r\n  public role: UserRole = {} as UserRole;\r\n  currentIndex = 0;\r\n  isBelow768 = false;\r\n  private subs = new SubSink();\r\n\r\n  constructor(\r\n    private router: Router,\r\n    public authService: AuthService,\r\n    public userService: UserService,\r\n    public generalService: GeneralService,\r\n    private elementRef: ElementRef,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.role = this.authService.getUserRole();\r\n    this.userPhoto = this.userService.getUserPhoto(this.authService.getLoggedInUser());\r\n    this.subs.add(this.generalService.deviceKind.subscribe(res => {\r\n      this.isBelow768 = res.is768;\r\n    }));\r\n    this.subs.add(this.userService.update.subscribe(res => {\r\n      // this.userPhoto = this.authService.getUserPhoto();\r\n    }));\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    const playerContainers = this.elementRef.nativeElement.querySelectorAll('.hoverEffects');\r\n    playerContainers.forEach((container: any) => {\r\n      container.addEventListener('mouseover', () => {\r\n        if (container) {\r\n\r\n          const player = container.querySelector('lottie-player') as any;\r\n          if (player) {\r\n            player.setDirection(1);\r\n            player.play();\r\n            player.setLooping(false);\r\n          }\r\n        }\r\n      });\r\n      container.addEventListener('mouseleave', () => {\r\n        const player = container.querySelector('lottie-player') as any;\r\n        player.setDirection(-1);\r\n        player.play();\r\n        player.setLooping(false);\r\n      });\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n}\r\n", "\r\n<div id=\"left-menu\" class=\"menu\">\r\n    <div class=\"first-section\">\r\n        <div class=\"first-section-content avatar-circle\">\r\n\r\n            \r\n            <img  [src]=\"this.userService.getUserPhoto(this.authService.getLoggedInUser())\"\r\n            (error)=\"generalService.setDefaultUserAvatar($event)\" class=\"menu-img-profile min-h-4rem\">\r\n            <!-- <div class=\"language-text\">Teaching Language:</div> -->\r\n            <!-- <div class=\"language-teach\">Spanish</div> -->\r\n        </div>\r\n    </div>\r\n    <div *ngIf=\"authService.isTeacher\" class=\"flex-column  hidden lg:block align-items-center justify-content-center below-profile__lang-text\">\r\n        <p class=\"mb-1 text-center\">{{authService.isStudent ? 'Learning' : 'Teaching'}} Language:</p>\r\n        <p class=\"flex font-bold justify-content-center align-items-center gap-1\"><i class=\"pi pi-flag\"></i> \r\n            {{authService.isStudent ? 'Learning' : authService.teachingLanguage}}\r\n        </p>\r\n    </div>\r\n    <div class=\"my-hr\">\r\n        <div class=\"my-hr-content\"></div>\r\n    </div>\r\n    <div class=\"second-section\">\r\n        <ng-container *ngFor=\"let item of menuItems; let i = index\">\r\n           \r\n        <div   *ngIf=\"item.role.includes(role)\" [pTooltip]=\"item.disabled ? 'Access is unavailable at the moment.' : ''\">\r\n        <a class=\"second-section-item  hoverEffects relative\"\r\n         [class.disabled]=\"item.disabled\" [tabindex]=\"item.disabled ? -1 : null\"  [routerLink]=\"item.url\" [routerLinkActiveOptions]=\"{exact: true}\" routerLinkActive=\"is-active\">\r\n         <div class=\"icon-container\">\r\n                <span  class=\"lottie-wrapper\">\r\n                <lottie-player [src]=\"item.lottieIcon\"  background=\"transparent\"  speed=\"1\"  [style.transform]=\"'scale(' + item.scale + ')'\" loop ></lottie-player>\r\n                </span>\r\n                <!-- <img [src]=\"item.icon\" class=\"icon\"> -->\r\n            </div>\r\n            <div class=\"text-container-tablet\">{{item.title}}</div>\r\n            <div class=\"text-container\">{{item.title}}</div>\r\n            <img *ngIf=\"item.title === 'AI Chat'\" src=\"/assets/images/dashboard/new.svg\" class=\"new-label\">\r\n        </a>\r\n    </div>\r\n    </ng-container>\r\n        <!-- <a class=\"second-section-item\" routerLinkActive=\"is-active\" routerLink=\"/classrooms\" [ngClass]=\"!isTrial && isClassroom? 'is-active': ''\">\r\n            <div class=\"icon-container\">\r\n                <img src=\"/assets/icons/classroom.svg\" class=\"icon\">\r\n                <div class=\"text-container-tablet\">Classrooms</div>\r\n            </div>\r\n            <div class=\"text-container\">\r\n                Classrooms\r\n            </div>\r\n        </a>\r\n        <a *ngIf=\"role=='Teacher'\" class=\"second-section-item\" routerLink=\"/trials\" [ngClass]=\"isTrial || isTrialRoute? 'is-active': ''\">\r\n            <div class=\"icon-container\">\r\n                <img src=\"/assets/icons/trial.svg\" class=\"icon\">\r\n                <div class=\"text-container-tablet\">Trials</div>\r\n            </div>\r\n            <div class=\"text-container\">\r\n                Trials\r\n            </div>\r\n        </a>\r\n        <a class=\"second-section-item\" routerLink=\"/calendar\" routerLinkActive=\"is-active\">\r\n            <div class=\"icon-container\">\r\n                <img src=\"/assets/icons/calendar.svg\" class=\"icon\">\r\n                <div class=\"text-container-tablet\">Calendar</div>\r\n            </div>\r\n            <div class=\"text-container\">\r\n                Calendar\r\n            </div>\r\n        </a>\r\n        <a class=\"second-section-item\" routerLink=\"/notes\" routerLinkActive=\"is-active\">\r\n            <div class=\"icon-container\">\r\n                <img src=\"/assets/icons/notes.svg\" class=\"icon\">\r\n                <div class=\"text-container-tablet\">Notes</div>\r\n            </div>\r\n            <div class=\"text-container\">\r\n                Notes\r\n            </div>\r\n        </a>\r\n        <a class=\"second-section-item\" routerLink=\"/library\" routerLinkActive=\"is-active\">\r\n            <div class=\"icon-container\">\r\n                <img src=\"/assets/icons/library.svg\" class=\"icon\">\r\n                <div class=\"text-container-tablet\">Library</div>\r\n            </div>\r\n            <div class=\"text-container\">\r\n                Library\r\n            </div>\r\n        </a>\r\n        <div class=\"second-section-item\" routerLink=\"/homework\" routerLinkActive=\"is-active\">\r\n            <div class=\"icon-container\">\r\n                <img src=\"/assets/icons/homework.svg\" class=\"icon\">\r\n                <div class=\"text-container-tablet\">Homework</div>\r\n            </div>\r\n            <div class=\"text-container\">\r\n                Homework\r\n            </div>\r\n        </div>\r\n        <div *ngIf=\"role=='Teacher'\" class=\"second-section-item\" routerLink=\"/certificates\" routerLinkActive=\"is-active\">\r\n            <div class=\"icon-container\">\r\n                <img src=\"/assets/icons/certificate.svg\" class=\"icon\">\r\n                <div class=\"text-container-tablet\">Training</div>\r\n            </div>\r\n            <div class=\"text-container\">\r\n                Training\r\n            </div>\r\n        </div>\r\n        <div *ngIf=\"role=='Student'\" class=\"second-section-item\" routerLink=\"/certificates\" routerLinkActive=\"is-active\">\r\n            <div class=\"icon-container\">\r\n                <img src=\"/assets/icons/certificate.svg\" class=\"icon\">\r\n                <div class=\"text-container-tablet\">Homework</div>\r\n            </div>\r\n            <div class=\"text-container\">\r\n                Certificates\r\n            </div>\r\n        </div>\r\n        <div *ngIf=\"role=='Student'\" class=\"second-section-item\" routerLink=\"/certificates\" routerLinkActive=\"is-active\">\r\n            <div class=\"icon-container\">\r\n                <img src=\"/assets/icons/certificate.svg\" class=\"icon\">\r\n                <div class=\"text-container-tablet\">Homework</div>\r\n            </div>\r\n            <div class=\"text-container\">\r\n                Guides\r\n            </div>\r\n        </div> -->\r\n        <!-- <div class=\"second-section-item\">\r\n            <div class=\"icon-container\">\r\n                <img src=\"/assets/icons/dropin.svg\" class=\"icon\">\r\n                <div class=\"text-container-tablet\">Drop-In</div>\r\n            </div>\r\n            <div class=\"text-container\" style=\"color: var(--light-purple)\">\r\n                Drop-In\r\n            </div>\r\n        </div> -->\r\n    </div>\r\n</div>\r\n"], "mappings": "AAMA,SAASA,OAAO,QAAQ,SAAS;;;;;;;;;;;;;ICOzBC,EADJ,CAAAC,cAAA,aAA2I,YAC3G;IAAAD,EAAA,CAAAE,MAAA,GAA6D;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC7FH,EAAA,CAAAC,cAAA,YAA0E;IAAAD,EAAA,CAAAI,SAAA,YAA0B;IAChGJ,EAAA,CAAAE,MAAA,GACJ;IACJF,EADI,CAAAG,YAAA,EAAI,EACF;;;;IAJ0BH,EAAA,CAAAK,SAAA,GAA6D;IAA7DL,EAAA,CAAAM,kBAAA,KAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,yCAA6D;IAErFT,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,WAAA,CAAAC,SAAA,gBAAAF,MAAA,CAAAC,WAAA,CAAAE,gBAAA,MACJ;;;;;IAmBIV,EAAA,CAAAI,SAAA,cAA+F;;;;;IAP3FJ,EAJR,CAAAC,cAAA,cAAiH,YAEwD,cAC5I,eACS;IAC9BD,EAAA,CAAAI,SAAA,wBAAmJ;IAGvJJ,EAFI,CAAAG,YAAA,EAAO,EAEL;IACNH,EAAA,CAAAC,cAAA,cAAmC;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvDH,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAChDH,EAAA,CAAAW,UAAA,IAAAC,iDAAA,kBAA+F;IAEvGZ,EADI,CAAAG,YAAA,EAAI,EACF;;;;IAbsCH,EAAA,CAAAa,UAAA,aAAAC,OAAA,CAAAC,QAAA,+CAAwE;IAE/Gf,EAAA,CAAAK,SAAA,EAAgC;IAAhCL,EAAA,CAAAgB,WAAA,aAAAF,OAAA,CAAAC,QAAA,CAAgC;IAAiEf,EAAhE,CAAAa,UAAA,aAAAC,OAAA,CAAAC,QAAA,aAAsC,eAAAD,OAAA,CAAAG,GAAA,CAAyB,4BAAAjB,EAAA,CAAAkB,eAAA,KAAAC,GAAA,EAA0C;IAGtDnB,EAAA,CAAAK,SAAA,GAA+C;IAA/CL,EAAA,CAAAoB,WAAA,yBAAAN,OAAA,CAAAO,KAAA,OAA+C;IAA7GrB,EAAA,CAAAa,UAAA,QAAAC,OAAA,CAAAQ,UAAA,CAAuB;IAIPtB,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAAuB,iBAAA,CAAAT,OAAA,CAAAU,KAAA,CAAc;IACrBxB,EAAA,CAAAK,SAAA,GAAc;IAAdL,EAAA,CAAAuB,iBAAA,CAAAT,OAAA,CAAAU,KAAA,CAAc;IACpCxB,EAAA,CAAAK,SAAA,EAA8B;IAA9BL,EAAA,CAAAa,UAAA,SAAAC,OAAA,CAAAU,KAAA,eAA8B;;;;;IAbxCxB,EAAA,CAAAyB,uBAAA,GAA4D;IAE5DzB,EAAA,CAAAW,UAAA,IAAAe,2CAAA,oBAAiH;;;;;;IAAzG1B,EAAA,CAAAK,SAAA,EAA8B;IAA9BL,EAAA,CAAAa,UAAA,SAAAC,OAAA,CAAAa,IAAA,CAAAC,QAAA,CAAArB,MAAA,CAAAoB,IAAA,EAA8B;;;ADhB9C,IAAIE,mBAAwB;AAM5B,OAAM,MAAOC,aAAa;EAWxBC,YACUC,MAAc,EACfxB,WAAwB,EACxByB,WAAwB,EACxBC,cAA8B,EAC7BC,UAAsB;IAJtB,KAAAH,MAAM,GAANA,MAAM;IACP,KAAAxB,WAAW,GAAXA,WAAW;IACX,KAAAyB,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACb,KAAAC,UAAU,GAAVA,UAAU;IAfX,KAAAC,SAAS,GAAW,EAAE;IACxB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAb,IAAI,GAAa,EAAc;IACtC,KAAAc,YAAY,GAAG,CAAC;IAChB,KAAAC,UAAU,GAAG,KAAK;IACV,KAAAC,IAAI,GAAG,IAAI5C,OAAO,EAAE;EAQxB;EAEJ6C,QAAQA,CAAA;IACN,IAAI,CAACjB,IAAI,GAAG,IAAI,CAACnB,WAAW,CAACqC,WAAW,EAAE;IAC1C,IAAI,CAACR,SAAS,GAAG,IAAI,CAACJ,WAAW,CAACa,YAAY,CAAC,IAAI,CAACtC,WAAW,CAACuC,eAAe,EAAE,CAAC;IAClF,IAAI,CAACJ,IAAI,CAACK,GAAG,CAAC,IAAI,CAACd,cAAc,CAACe,UAAU,CAACC,SAAS,CAACC,GAAG,IAAG;MAC3D,IAAI,CAACT,UAAU,GAAGS,GAAG,CAACC,KAAK;IAC7B,CAAC,CAAC,CAAC;IACH,IAAI,CAACT,IAAI,CAACK,GAAG,CAAC,IAAI,CAACf,WAAW,CAACoB,MAAM,CAACH,SAAS,CAACC,GAAG,IAAG;MACpD;IAAA,CACD,CAAC,CAAC;EACL;EAEAG,eAAeA,CAAA;IACb,MAAMC,gBAAgB,GAAG,IAAI,CAACpB,UAAU,CAACqB,aAAa,CAACC,gBAAgB,CAAC,eAAe,CAAC;IACxFF,gBAAgB,CAACG,OAAO,CAAEC,SAAc,IAAI;MAC1CA,SAAS,CAACC,gBAAgB,CAAC,WAAW,EAAE,MAAK;QAC3C,IAAID,SAAS,EAAE;UAEb,MAAME,MAAM,GAAGF,SAAS,CAACG,aAAa,CAAC,eAAe,CAAQ;UAC9D,IAAID,MAAM,EAAE;YACVA,MAAM,CAACE,YAAY,CAAC,CAAC,CAAC;YACtBF,MAAM,CAACG,IAAI,EAAE;YACbH,MAAM,CAACI,UAAU,CAAC,KAAK,CAAC;UAC1B;QACF;MACF,CAAC,CAAC;MACFN,SAAS,CAACC,gBAAgB,CAAC,YAAY,EAAE,MAAK;QAC5C,MAAMC,MAAM,GAAGF,SAAS,CAACG,aAAa,CAAC,eAAe,CAAQ;QAC9DD,MAAM,CAACE,YAAY,CAAC,CAAC,CAAC,CAAC;QACvBF,MAAM,CAACG,IAAI,EAAE;QACbH,MAAM,CAACI,UAAU,CAAC,KAAK,CAAC;MAC1B,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACvB,IAAI,CAACwB,WAAW,EAAE;EACzB;EAAC,QAAAC,CAAA,G;qBAvDUtC,aAAa,EAAA9B,EAAA,CAAAqE,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAvE,EAAA,CAAAqE,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAzE,EAAA,CAAAqE,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA3E,EAAA,CAAAqE,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA7E,EAAA,CAAAqE,iBAAA,CAAArE,EAAA,CAAA8E,UAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAbjD,aAAa;IAAAkD,SAAA;IAAAC,MAAA;MAAA7C,SAAA;IAAA;IAAA8C,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCRdvF,EALZ,CAAAC,cAAA,aAAiC,aACF,aAC0B,aAI6C;QAA1FD,EAAA,CAAAyF,UAAA,mBAAAC,4CAAAC,MAAA;UAAA,OAASH,GAAA,CAAAtD,cAAA,CAAA0D,oBAAA,CAAAD,MAAA,CAA2C;QAAA,EAAC;QAI7D3F,EALQ,CAAAG,YAAA,EAC0F,EAGxF,EACJ;QACNH,EAAA,CAAAW,UAAA,IAAAkF,4BAAA,iBAA2I;QAM3I7F,EAAA,CAAAC,cAAA,aAAmB;QACfD,EAAA,CAAAI,SAAA,aAAiC;QACrCJ,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,aAA4B;QACxBD,EAAA,CAAAW,UAAA,IAAAmF,qCAAA,0BAA4D;QA4GpE9F,EADI,CAAAG,YAAA,EAAM,EACJ;;;QA5HYH,EAAA,CAAAK,SAAA,GAAyE;QAAzEL,EAAA,CAAAa,UAAA,QAAA2E,GAAA,CAAAvD,WAAA,CAAAa,YAAA,CAAA0C,GAAA,CAAAhF,WAAA,CAAAuC,eAAA,KAAA/C,EAAA,CAAA+F,aAAA,CAAyE;QAMjF/F,EAAA,CAAAK,SAAA,EAA2B;QAA3BL,EAAA,CAAAa,UAAA,SAAA2E,GAAA,CAAAhF,WAAA,CAAAwF,SAAA,CAA2B;QAUEhG,EAAA,CAAAK,SAAA,GAAc;QAAdL,EAAA,CAAAa,UAAA,YAAA2E,GAAA,CAAApD,SAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}