{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { DOCUMENT } from '@angular/common';\nimport { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./auth.service\";\nexport let JitsiMeetService = /*#__PURE__*/(() => {\n  class JitsiMeetService {\n    constructor(route, document, authService) {\n      var _this = this;\n      this.route = route;\n      this.document = document;\n      this.authService = authService;\n      this.teacherJoined$ = new BehaviorSubject(false);\n      this.teacherJoined = this.teacherJoined$.asObservable();\n      this.roomClosed$ = new BehaviorSubject(false);\n      this.roomClosed = this.roomClosed$.asObservable();\n      this.videoConferenceLeft$ = new BehaviorSubject(false);\n      this.videoConferenceLeft = this.videoConferenceLeft$.asObservable();\n      this.namePrincipalRoom = '';\n      this.domain = 'meet.cerint.net';\n      // For Custom Controls\n      this.isAudioMuted = true;\n      this.isVideoMuted = true;\n      this.isHangupExecuted = false;\n      this.role = '';\n      this.log = event => {\n        console.log('LOGGGGGGGGGGGGGG', event);\n      };\n      this.handleClose = ev => {\n        console.log('handleClose', ev);\n        this.roomClosed$.next(true);\n        // this.api.executeCommand('getParticipantsInfo', 'The Password');\n      };\n      this.getParticipantsInfo = /*#__PURE__*/function () {\n        var _ref = _asyncToGenerator(function* (event) {\n          console.log('getParticipantsInfo', event);\n        });\n        return function (_x) {\n          return _ref.apply(this, arguments);\n        };\n      }();\n      this.notificationTriggered = /*#__PURE__*/function () {\n        var _ref2 = _asyncToGenerator(function* (event) {\n          console.log('notificationTriggered', event);\n          if (event && event.title === 'dialog.thankYou') {\n            // this.videoConferenceLeft$.next(true);\n          }\n        });\n        return function (_x2) {\n          return _ref2.apply(this, arguments);\n        };\n      }();\n      this.endpointTextMessageReceived = /*#__PURE__*/function () {\n        var _ref3 = _asyncToGenerator(function* (event) {\n          if (event.data.eventData.text = 'mover a principal') {\n            _this.moveRoom('grupo 1', true);\n          }\n        });\n        return function (_x3) {\n          return _ref3.apply(this, arguments);\n        };\n      }();\n      this.passwordRequired = /*#__PURE__*/_asyncToGenerator(function* () {\n        console.log('passwordRequired'); // { id: \"2baa184e\" }\n        // this.api.executeCommand('password', 'The Password');\n      });\n      this.handleParticipantLeft = /*#__PURE__*/function () {\n        var _ref5 = _asyncToGenerator(function* (participant) {\n          const data = yield _this.getParticipants();\n          // this.isHangupExecuted = false;\n          console.log('handleParticipantLeft', participant); // { id: \"2baa184e\" }\n          console.log('user', _this.user); // { id: \"2baa184e\" }\n          if (participant.id !== _this.user.participantId) {\n            _this.isHangupExecuted = false;\n          }\n        });\n        return function (_x4) {\n          return _ref5.apply(this, arguments);\n        };\n      }();\n      this.participantRoleChanged = /*#__PURE__*/function () {\n        var _ref6 = _asyncToGenerator(function* (participant) {\n          console.log('participantRoleChanged', participant);\n          _this.role = participant.role;\n          if (participant.role === \"moderator\") {\n            console.log('participantRoleChanged:', participant.role);\n            // this.api.executeCommand('hangup');\n            // this.api.executeCommand('password', 'The Password');\n          }\n        });\n        return function (_x5) {\n          return _ref6.apply(this, arguments);\n        };\n      }();\n      this.subjectChange = /*#__PURE__*/function () {\n        var _ref7 = _asyncToGenerator(function* (participant) {\n          console.log('subjectChange', participant);\n        });\n        return function (_x6) {\n          return _ref7.apply(this, arguments);\n        };\n      }();\n      this.handleParticipantJoined = /*#__PURE__*/function () {\n        var _ref8 = _asyncToGenerator(function* (participant) {\n          console.log('OJOJOJOJ  handleParticipantJoined', participant);\n          console.log('current User', _this.user); // { id: \"2baa184e\", displayName: \"Shanu Verma\", formattedDisplayName: \"Shanu Verma\" }\n          //this.api.executeCommand('displayName', 'New Nickname');\n          const data = yield _this.getParticipants();\n          console.log(data);\n          if (!_this.user.id) {\n            _this.isHangupExecuted = true;\n            return;\n          }\n          if (participant.id === _this.user.id) {\n            _this.isHangupExecuted = true;\n            return;\n          }\n          console.log('isHangupExecuted', _this.isHangupExecuted);\n          console.log('role', _this.role);\n          console.log('isStudent', _this.authService.isStudent);\n          if (data.length > 1) {\n            if (_this.authService.isStudent && !_this.isHangupExecuted && _this.role !== 'none') {\n              _this.api.executeCommand('hangup');\n              console.log('HEREEEEEEEEEEEE');\n              _this.teacherJoined$.next(true);\n              _this.isHangupExecuted = true; // Set the flag to true\n            }\n            for (const participant of data) {\n              if (participant.participantId === _this.user.id) {}\n            }\n          }\n        });\n        return function (_x7) {\n          return _ref8.apply(this, arguments);\n        };\n      }();\n      this.handleVideoConferenceJoined = /*#__PURE__*/function () {\n        var _ref9 = _asyncToGenerator(function* (participant) {\n          console.log('handleVideoConferenceJoined', participant);\n          // Once the conference is joined, make the participant a moderator\n          // this.api.executeCommand('setLocalParticipantRole', 'moderator');\n          _this.user = participant;\n          const data = yield _this.getParticipants();\n          const participants = JSON.stringify(data);\n          const parsedParticipants = JSON.parse(participants); // Convert back to object if needed\n          let count = 0;\n          for (const participant of parsedParticipants) {\n            // Do something with each participant\n            if (participant.participantId !== 'local') {\n              count++;\n            }\n          }\n          if (count === 1) {}\n          _this.teacherJoined$.next(false);\n          _this.roomClosed$.next(false);\n        });\n        return function (_x8) {\n          return _ref9.apply(this, arguments);\n        };\n      }();\n      this.handleVideoConferenceLeft = e => {\n        console.log('handleVideoConferenceLeft', e);\n        // this.route.navigate(['/thank-you']);\n        // this.videoConferenceLeft$.next(true);\n      };\n      this.handleMuteStatus = audio => {\n        console.log('handleMuteStatus', audio); // { muted: true }\n      };\n      this.handleVideoStatus = video => {\n        console.log('handleVideoStatus', video); // { muted: true }\n      };\n    }\n    ngOnDestroy() {\n      this.api.dispose();\n      this.teacherJoined$.next(false);\n      this.roomClosed$.next(false);\n    }\n    /**\n     * Append the JS tag to the Document Body.\n     * @param renderer The Angular Renderer\n     * @param src The path to the script\n     * @returns the script element\n     */\n    loadJsScript(renderer, src) {\n      const script = renderer.createElement('script');\n      script.type = 'text/javascript';\n      script.src = src;\n      renderer.appendChild(this.document.body, script);\n      return script;\n    }\n    moveRoom(nameRoom, isAdmin) {\n      const myNode = document.getElementById('jitsi-iframe');\n      if (myNode) {\n        myNode.innerHTML = '';\n      }\n      this.options = {\n        roomName: nameRoom,\n        width: '100%',\n        height: 500,\n        configOverwrite: {\n          prejoinPageEnabled: false,\n          disableInviteFunctions: true\n        },\n        interfaceConfigOverwrite: {\n          // customToolbarButtons: [\n          //     {\n          //         icon: ``,\n          //         id: 'custom-toolbar-button',\n          //         text: 'Custom Toolbar Button'\n          //     }\n          // ],\n          disableInviteFunctions: true,\n          MOBILE_APP_PROMO: false,\n          // don't works \n          //DEFAULT_LOCAL_DISPLAY_NAME: 'Eu', //ok\n          //DEFAULT_LOGO_URL: 'images/sample.png',   // dont work\n          SHOW_CHROME_EXTENSION_BANNER: false,\n          //ok\n          //TOOLBAR_ALWAYS_VISIBLE: true, // ok\n          //SETTINGS_SECTIONS: ['devices', 'language'], //ok,\n          TOOLBAR_BUTTONS: ['microphone', 'camera', 'closedcaptions', 'desktop', 'fullscreen', 'fodeviceselection', 'profile', '', 'chat', 'recording', 'livestreaming', 'etherpad', 'sharedvideo', 'settings', 'raisehand', 'videoquality', 'filmstrip', 'invite', 'feedback', 'stats', 'shortcuts', 'tileview', 'videobackgroundblur', 'download', 'help', 'mute-everyone', 'e2ee'],\n          // ok\n          filmStripOnly: true,\n          FILM_STRIP_MAX_HEIGHT: 80,\n          DEFAULT_BACKGROUND: \"transparent\",\n          //DISABLE_FOCUS_INDICATOR: true,\n          //DISABLE_DOMINANT_SPEAKER_INDICATOR: true,\n          //DISPLAY_WELCOME_PAGE_CONTENT: false,\n          //TILE_VIEW_MAX_COLUMNS: 1,\n          DISABLE_VIDEO_BACKGROUND: false,\n          VIDEO_QUALITY_LABEL_DISABLED: true,\n          CONNECTION_INDICATOR_DISABLED: true,\n          HIDE_KICK_BUTTON_FOR_GUESTS: true\n        },\n        parentNode: document.querySelector('#jitsi-iframe'),\n        userInfo: {\n          displayName: this.loggedUser.firstName,\n          email: this.loggedUser.email,\n          testVar: 'makaa',\n          role: 'moderator'\n        }\n        // TODO: Whiteboard feature\n        // whiteboard: {\n        //     enabled: true,\n        //     collabServerBaseUrl: 'https://meet.example.com'\n        // },\n      };\n      this.api = new JitsiMeetExternalAPI(this.domain, this.options);\n      this.api.addEventListeners({\n        readyToClose: this.handleClose,\n        participantLeft: this.handleParticipantLeft,\n        participantJoined: this.handleParticipantJoined,\n        videoConferenceJoined: this.handleVideoConferenceJoined,\n        videoConferenceLeft: this.handleVideoConferenceLeft,\n        audioMuteStatusChanged: this.handleMuteStatus,\n        videoMuteStatusChanged: this.handleVideoStatus,\n        participantRoleChanged: this.participantRoleChanged,\n        getParticipantsInfo: this.getParticipantsInfo,\n        endpointTextMessageReceived: this.endpointTextMessageReceived,\n        moderationStatusChanged: this.moderationStatusChanged,\n        notificationTriggered: this.notificationTriggered,\n        subjectChange: this.subjectChange,\n        log: this.log\n      });\n      this.api.executeCommand('setNoiseSuppressionEnabled', {\n        enabled: true\n      });\n      // this.api.executeCommand('toggleWhiteboard');\n      this.api.executeCommand('toggleLobby', false); // Ensure lobby mode is disabled\n      this.api.executeCommand('overwriteConfig', {\n        config: this.options\n      });\n    }\n    moderationStatusChanged() {\n      alert('moderationStatusChanged');\n    }\n    myUserId() {\n      alert(this.api.myUserId());\n      return this.api.myUserId();\n    }\n    changeRouterLink(value) {\n      this.namePrincipalRoom = value;\n      const myNode = document.getElementById('jitsi-iframe');\n      myNode.innerHTML = '';\n      this.options = {\n        roomName: this.namePrincipalRoom,\n        width: 900,\n        height: 500,\n        configOverwrite: {\n          prejoinPageEnabled: false,\n          openBridgeChannel: 'datachannel',\n          JITSI_WATERMARK_LINK: 'https://aaa.org',\n          DISABLE_DOMINANT_SPEAKER_INDICATOR: true,\n          DISPLAY_WELCOME_FOOTER: false,\n          DISPLAY_WELCOME_PAGE_ADDITIONAL_CARD: true\n        },\n        \"welcomepage\": {\n          \"appDescription\": \"description\",\n          \"headerTitle\": \"Test\",\n          \"headerSubtitle\": \"description\",\n          \"title\": \"Test\"\n        },\n        interfaceConfigOverwrite: {\n          JITSI_WATERMARK_LINK: 'https://meet.jit.si',\n          DISABLE_DOMINANT_SPEAKER_INDICATOR: true,\n          DISPLAY_WELCOME_FOOTER: false,\n          DISPLAY_WELCOME_PAGE_ADDITIONAL_CARD: true,\n          DISPLAY_WELCOME_PAGE_CONTENT: true,\n          DISPLAY_WELCOME_PAGE_TOOLBAR_ADDITIONAL_CONTENT: true,\n          disableGrantModerator: false\n          // overwrite interface properties\n        },\n        parentNode: document.querySelector('#jitsi-iframe'),\n        displayName: 'cool name',\n        role: 'moderator',\n        userInfo: {\n          displayName: 'cool name',\n          role: 'moderator'\n        }\n      };\n      this.api = new JitsiMeetExternalAPI(this.domain, this.options);\n    }\n    getParticipantsNumber() {\n      // Get participants info\n      const participants = this.api.getParticipantsInfo();\n      alert(participants.length);\n      // Return the number of participants\n      return participants.length;\n    }\n    getParticipants() {\n      return new Promise((resolve, reject) => {\n        setTimeout(() => {\n          resolve(this.api.getParticipantsInfo()); // get all participants\n        }, 100);\n      });\n    }\n    // custom events\n    executeCommand(command) {\n      this.api.executeCommand(command);\n      if (command == 'hangup') {\n        this.route.navigate(['/thank-you']);\n        return;\n      }\n      if (command == 'toggleAudio') {\n        this.isAudioMuted = !this.isAudioMuted;\n      }\n      if (command == 'toggleVideo') {\n        this.isVideoMuted = !this.isVideoMuted;\n      }\n    }\n    openMicrophoneSettings() {\n      // this.api.executeCommand('toggleAudio');\n      // Check if the JitsiMeetExternalAPI instance is available\n      if (this.api && this.api.executeCommand) {\n        // Execute the command to open microphone settings\n        // this.api.executeCommand('toggleAudio');\n      } else {\n        console.error('JitsiMeetExternalAPI instance not available or does not support executeCommand');\n      }\n    }\n    setLoggedUser(user) {\n      this.loggedUser = user;\n    }\n    setRoomName(name) {\n      this.namePrincipalRoom = name;\n    }\n    setRoomSubject(subject) {\n      // this.api.executeCommand('subject', 'MyLingoTrip Lesson');\n      // TODO: check if needed to add real subject\n      this.api.executeCommand('subject', subject);\n    }\n    setVideoConferenceLeft(val) {\n      this.videoConferenceLeft$.next(val);\n    }\n    setTeacherJoined(val) {\n      this.teacherJoined$.next(val);\n    }\n    static #_ = this.ɵfac = function JitsiMeetService_Factory(t) {\n      return new (t || JitsiMeetService)(i0.ɵɵinject(i1.Router), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i2.AuthService));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: JitsiMeetService,\n      factory: JitsiMeetService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return JitsiMeetService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}