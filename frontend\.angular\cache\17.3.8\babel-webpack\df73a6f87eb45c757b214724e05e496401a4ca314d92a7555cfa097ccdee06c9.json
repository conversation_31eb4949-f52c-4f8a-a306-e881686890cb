{"ast": null, "code": "export var PackageType = /*#__PURE__*/function (PackageType) {\n  PackageType[\"REGULAR\"] = \"Regular\";\n  PackageType[\"FLEX\"] = \"Flexible\";\n  PackageType[\"PREMIUM\"] = \"Premium\";\n  PackageType[\"BUSINESS\"] = \"Business\";\n  PackageType[\"GIFT\"] = \"Gift\";\n  PackageType[\"TRIAL\"] = \"Trial\";\n  PackageType[\"PAID\"] = \"Paid\";\n  return PackageType;\n}(PackageType || {});\nexport const PackageDurationHours = {\n  [PackageType.REGULAR]: 24,\n  [PackageType.FLEX]: 12,\n  [PackageType.PREMIUM]: 4\n};\nexport var PackageTypeNew = /*#__PURE__*/function (PackageTypeNew) {\n  PackageTypeNew[\"REGULAR\"] = \"Regular\";\n  PackageTypeNew[\"FLEX\"] = \"Flexible\";\n  PackageTypeNew[\"PREMIUM\"] = \"Premium\";\n  return PackageTypeNew;\n}(PackageTypeNew || {});\nexport var PackageState = /*#__PURE__*/function (PackageState) {\n  PackageState[\"ACTIVE\"] = \"Active\";\n  PackageState[\"ONGOING\"] = \"ongoing\";\n  PackageState[\"COMPLETED\"] = \"Completed\";\n  PackageState[\"PAUSED\"] = \"Paused\";\n  PackageState[\"EXPIRED\"] = \"Expired\";\n  PackageState[\"CANCELED\"] = \"Canceled\";\n  PackageState[\"CANCELLED\"] = \"Cancelled\";\n  PackageState[\"PURCHASED\"] = \"Purchased\";\n  PackageState[\"REFUNDED\"] = \"Refunded\";\n  PackageState[\"TRANSFERED\"] = \"Transferred\";\n  return PackageState;\n}(PackageState || {});\nexport class PackagesModel {\n  static getDefaultPackages() {\n    const defaultPackages = [{\n      type: PackageType.REGULAR,\n      costPlus: 0,\n      expiresIn: [{\n        hours: 5,\n        price: 0,\n        perHour: 0,\n        expirationMonths: 1\n      }, {\n        hours: 10,\n        price: 0,\n        perHour: 0,\n        expirationMonths: 2\n      }, {\n        hours: 15,\n        price: 0,\n        perHour: 0,\n        expirationMonths: 3\n      }, {\n        hours: 20,\n        price: 0,\n        perHour: 0,\n        expirationMonths: 4\n      }, {\n        hours: 50,\n        price: 0,\n        perHour: 0,\n        expirationMonths: 10\n      }],\n      expiresPlus: \"\",\n      cancelation: \"24\",\n      pause: \"\"\n    }, {\n      type: PackageType.FLEX,\n      costPlus: 30,\n      expiresIn: [{\n        hours: 5,\n        price: 0,\n        perHour: 0,\n        expirationMonths: 3\n      }, {\n        hours: 10,\n        price: 0,\n        perHour: 0,\n        expirationMonths: 4\n      }, {\n        hours: 15,\n        price: 0,\n        perHour: 0,\n        expirationMonths: 5\n      }, {\n        hours: 20,\n        price: 0,\n        perHour: 0,\n        expirationMonths: 6\n      }, {\n        hours: 50,\n        price: 0,\n        perHour: 0,\n        expirationMonths: 12\n      }],\n      expiresPlus: \"2 months\",\n      cancelation: \"12\",\n      pause: \"\" // TODO: when freeze package is added\n    }, {\n      type: PackageType.PREMIUM,\n      costPlus: 80,\n      expiresIn: [{\n        hours: 5,\n        price: 0,\n        perHour: 0,\n        expirationMonths: 5\n      }, {\n        hours: 10,\n        price: 0,\n        perHour: 0,\n        expirationMonths: 6\n      }, {\n        hours: 15,\n        price: 0,\n        perHour: 0,\n        expirationMonths: 7\n      }, {\n        hours: 20,\n        price: 0,\n        perHour: 0,\n        expirationMonths: 8\n      }, {\n        hours: 50,\n        price: 0,\n        perHour: 0,\n        expirationMonths: 14\n      }],\n      expiresPlus: \"4 months\",\n      cancelation: \"4\",\n      pause: \"\" // TODO: when freeze package is added\n    }];\n    return defaultPackages;\n  }\n}", "map": {"version": 3, "names": ["PackageType", "PackageDurationHours", "REGULAR", "FLEX", "PREMIUM", "PackageTypeNew", "PackageState", "PackagesModel", "getDefaultPackages", "defaultPackages", "type", "costPlus", "expiresIn", "hours", "price", "perHour", "expirationMonths", "expiresPlus", "cancelation", "pause"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\models\\package.model.ts"], "sourcesContent": ["import { Classroom, Level } from \"./classroom.model\";\r\nimport { Language } from \"./general.model\";\r\nimport { User } from \"./user.model\";\r\n\r\nexport interface Package {\r\n    id: string,\r\n    lessons_level: PackageLessonsLevel[],\r\n    totalHours: number,\r\n    classroomId: string,\r\n    classroomTeacher?: string,\r\n    currentPackageLevel: string,\r\n    packageLevel: string,\r\n    hoursLeft: number,\r\n    type: PackageType.REGULAR | PackageType.FLEX | PackageType.BUSINESS | PackageType.GIFT | PackageType.TRIAL | PackageType.PREMIUM,\r\n    expiresOn: Date,\r\n    stratedAt: Date,\r\n    purchasedAt: Date,\r\n    state: PackageState,\r\n    extensions: PackageExtention[]\r\n}\r\n\r\nexport interface PackageExtention {\r\n    fromDate: Date,\r\n    toDate: Date,\r\n    reason: string,\r\n    requested_by: User\r\n}\r\n\r\nexport interface PackageLessonsLevel {\r\n    level: string,\r\n    hoursCompleted: number,\r\n    lessonsStarted: Date,\r\n    lessonsEnded: Date | 'present'\r\n}\r\n\r\nexport interface PackageFilterHelper {\r\n    totalHours: (string | number)[],\r\n    hoursLeft: (string | number)[],\r\n    level: (string | Level)[],\r\n    type: (string | PackageType)[],\r\n    state: (string | PackageState)[],\r\n    hasExtention: string[]\r\n}\r\n\r\nexport interface PackageFilter {\r\n    totalHours: (string | number),\r\n    hoursLeft: (string | number),\r\n    level: (string | Level),\r\n    type: (string | PackageType),\r\n    state: (string | PackageState),\r\n    hasExtention: string,\r\n    expiresOn_from: Date,\r\n    expiresOn_to: Date,\r\n    stratedAt_to: Date,\r\n    stratedAt_from: Date,\r\n    purchasedAt_from: Date,\r\n    purchasedAt_to: Date,\r\n}\r\n\r\nexport interface PackageToExtend {\r\n    extendTime: string,\r\n    price: number\r\n}\r\n\r\nexport interface PackageToBuy {\r\n    Id: number,\r\n    type: PackageType,\r\n    costPlus: number,\r\n    expiresIn: string,\r\n    expiresPlus: string,\r\n    cancelation: string,\r\n    pause: string,\r\n    priceHourly: PackagePriceHourly[]\r\n}\r\n\r\nexport interface PackageToBuyNew {\r\n    id: number,\r\n    hours: number,\r\n    price: number,\r\n    perHour: number,\r\n    language: string,\r\n    level: string,\r\n    numberOfStudents: string\r\n}\r\n\r\nexport interface mltPackages {\r\n    type: PackageType,\r\n    costPlus: number,\r\n    expiresIn: PackagePriceHourly[],\r\n    expiresPlus: string,\r\n    cancelation: string,\r\n    pause: string,\r\n}\r\n\r\nexport interface PackagePriceHourly {\r\n    hours: number,\r\n    price: number,\r\n    perHour: number,\r\n    expirationMonths: number\r\n}\r\n\r\nexport interface BuyerUserDetails {\r\n    fname: string,\r\n    lname: string,\r\n    company: string,\r\n    profession: string,\r\n    country: string,\r\n    streetName: string,\r\n    streetNumber: string,\r\n    city: string,\r\n    postcode: string,\r\n    tax: string,\r\n    email: string,\r\n    phone: string,\r\n    tin: string,\r\n}\r\n\r\nexport interface PackageToBuyOrderDeails {\r\n    isNew: boolean,\r\n    selectedClassroom: Classroom | null,\r\n    selectedLevel: string,\r\n    selectedLanguage: Language,\r\n    selectedNumberOfStudents: string,\r\n    selectedPackageToBuy: PackageToBuyNew,\r\n    buyerUserDetails: BuyerUserDetails\r\n}\r\n\r\nexport interface SplitPackagesToBuyResponse {\r\n    splitPaymentsId: number;\r\n    hours: number;\r\n    companyFee: number;\r\n    language: string;\r\n    level: string;\r\n    numberOfStudents: string;\r\n  }\r\n\r\nexport enum PackageType {\r\n    REGULAR = \"Regular\",\r\n    FLEX = \"Flexible\",\r\n    PREMIUM = \"Premium\",\r\n    BUSINESS = \"Business\",\r\n    GIFT = \"Gift\",\r\n    TRIAL = \"Trial\",\r\n    PAID = \"Paid\"\r\n}\r\n\r\nexport const PackageDurationHours: Record<string, number> = {\r\n    [PackageType.REGULAR]: 24,\r\n    [PackageType.FLEX]: 12,\r\n    [PackageType.PREMIUM]: 4,\r\n}\r\n\r\nexport enum PackageTypeNew {\r\n    REGULAR = \"Regular\",\r\n    FLEX = \"Flexible\",\r\n    PREMIUM = \"Premium\",\r\n}\r\n\r\nexport enum PackageState {\r\n    ACTIVE = \"Active\",\r\n    ONGOING = \"ongoing\",\r\n    COMPLETED = \"Completed\",\r\n    PAUSED = \"Paused\",\r\n    EXPIRED = \"Expired\",\r\n    CANCELED = \"Canceled\",\r\n    CANCELLED = \"Cancelled\",\r\n    PURCHASED = \"Purchased\",\r\n    REFUNDED = \"Refunded\",\r\n    TRANSFERED = \"Transferred\"\r\n}\r\n\r\nexport class PackagesModel {\r\n    static getDefaultPackages(): mltPackages[] {\r\n      const defaultPackages: mltPackages[] = [\r\n        {\r\n          type: PackageType.REGULAR,\r\n          costPlus: 0,\r\n          expiresIn: [{ hours: 5, price: 0, perHour: 0, expirationMonths: 1 },\r\n          { hours: 10, price: 0, perHour: 0, expirationMonths: 2 },\r\n          { hours: 15, price: 0, perHour: 0, expirationMonths: 3 },\r\n          { hours: 20, price: 0, perHour: 0, expirationMonths: 4 },\r\n          { hours: 50, price: 0, perHour: 0, expirationMonths: 10 },],\r\n          expiresPlus: \"\",\r\n          cancelation: \"24\",\r\n          pause: \"\",\r\n        },\r\n        {\r\n          type: PackageType.FLEX,\r\n          costPlus: 30,\r\n          expiresIn: [{ hours: 5, price: 0, perHour: 0, expirationMonths: 3 },\r\n          { hours: 10, price: 0, perHour: 0, expirationMonths: 4 },\r\n          { hours: 15, price: 0, perHour: 0, expirationMonths: 5 },\r\n          { hours: 20, price: 0, perHour: 0, expirationMonths: 6 },\r\n          { hours: 50, price: 0, perHour: 0, expirationMonths: 12 },],\r\n          expiresPlus: \"2 months\",\r\n          cancelation: \"12\",\r\n          pause: \"\", // TODO: when freeze package is added\r\n        },\r\n        {\r\n          type: PackageType.PREMIUM,\r\n          costPlus: 80,\r\n          expiresIn: [{ hours: 5, price: 0, perHour: 0, expirationMonths: 5 },\r\n            { hours: 10, price: 0, perHour: 0, expirationMonths: 6 },\r\n            { hours: 15, price: 0, perHour: 0, expirationMonths: 7 },\r\n            { hours: 20, price: 0, perHour: 0, expirationMonths: 8 },\r\n            { hours: 50, price: 0, perHour: 0, expirationMonths: 14 },],\r\n          expiresPlus: \"4 months\",  \r\n          cancelation: \"4\",\r\n          pause: \"\", // TODO: when freeze package is added\r\n        },\r\n      ];\r\n  \r\n      return defaultPackages;\r\n    }\r\n  }\r\n"], "mappings": "AAwIA,WAAYA,WAQX,gBARD,UAAYA,WAAW;EACnBA,WAAA,uBAAmB;EACnBA,WAAA,qBAAiB;EACjBA,WAAA,uBAAmB;EACnBA,WAAA,yBAAqB;EACrBA,WAAA,iBAAa;EACbA,WAAA,mBAAe;EACfA,WAAA,iBAAa;EAAA,OAPLA,WAAW;AAQvB,CAAC,CARWA,WAAW,OAQtB;AAED,OAAO,MAAMC,oBAAoB,GAA2B;EACxD,CAACD,WAAW,CAACE,OAAO,GAAG,EAAE;EACzB,CAACF,WAAW,CAACG,IAAI,GAAG,EAAE;EACtB,CAACH,WAAW,CAACI,OAAO,GAAG;CAC1B;AAED,WAAYC,cAIX,gBAJD,UAAYA,cAAc;EACtBA,cAAA,uBAAmB;EACnBA,cAAA,qBAAiB;EACjBA,cAAA,uBAAmB;EAAA,OAHXA,cAAc;AAI1B,CAAC,CAJWA,cAAc,OAIzB;AAED,WAAYC,YAWX,gBAXD,UAAYA,YAAY;EACpBA,YAAA,qBAAiB;EACjBA,YAAA,uBAAmB;EACnBA,YAAA,2BAAuB;EACvBA,YAAA,qBAAiB;EACjBA,YAAA,uBAAmB;EACnBA,YAAA,yBAAqB;EACrBA,YAAA,2BAAuB;EACvBA,YAAA,2BAAuB;EACvBA,YAAA,yBAAqB;EACrBA,YAAA,8BAA0B;EAAA,OAVlBA,YAAY;AAWxB,CAAC,CAXWA,YAAY,OAWvB;AAED,OAAM,MAAOC,aAAa;EACtB,OAAOC,kBAAkBA,CAAA;IACvB,MAAMC,eAAe,GAAkB,CACrC;MACEC,IAAI,EAAEV,WAAW,CAACE,OAAO;MACzBS,QAAQ,EAAE,CAAC;MACXC,SAAS,EAAE,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,gBAAgB,EAAE;MAAC,CAAE,EACnE;QAAEH,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,gBAAgB,EAAE;MAAC,CAAE,EACxD;QAAEH,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,gBAAgB,EAAE;MAAC,CAAE,EACxD;QAAEH,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,gBAAgB,EAAE;MAAC,CAAE,EACxD;QAAEH,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,gBAAgB,EAAE;MAAE,CAAE,CAAE;MAC3DC,WAAW,EAAE,EAAE;MACfC,WAAW,EAAE,IAAI;MACjBC,KAAK,EAAE;KACR,EACD;MACET,IAAI,EAAEV,WAAW,CAACG,IAAI;MACtBQ,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,gBAAgB,EAAE;MAAC,CAAE,EACnE;QAAEH,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,gBAAgB,EAAE;MAAC,CAAE,EACxD;QAAEH,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,gBAAgB,EAAE;MAAC,CAAE,EACxD;QAAEH,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,gBAAgB,EAAE;MAAC,CAAE,EACxD;QAAEH,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,gBAAgB,EAAE;MAAE,CAAE,CAAE;MAC3DC,WAAW,EAAE,UAAU;MACvBC,WAAW,EAAE,IAAI;MACjBC,KAAK,EAAE,EAAE,CAAE;KACZ,EACD;MACET,IAAI,EAAEV,WAAW,CAACI,OAAO;MACzBO,QAAQ,EAAE,EAAE;MACZC,SAAS,EAAE,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,KAAK,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,gBAAgB,EAAE;MAAC,CAAE,EACjE;QAAEH,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,gBAAgB,EAAE;MAAC,CAAE,EACxD;QAAEH,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,gBAAgB,EAAE;MAAC,CAAE,EACxD;QAAEH,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,gBAAgB,EAAE;MAAC,CAAE,EACxD;QAAEH,KAAK,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,OAAO,EAAE,CAAC;QAAEC,gBAAgB,EAAE;MAAE,CAAE,CAAE;MAC7DC,WAAW,EAAE,UAAU;MACvBC,WAAW,EAAE,GAAG;MAChBC,KAAK,EAAE,EAAE,CAAE;KACZ,CACF;IAED,OAAOV,eAAe;EACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}