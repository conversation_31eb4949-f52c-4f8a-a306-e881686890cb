{"ast": null, "code": "function e(e) {\n  this.message = e;\n}\ne.prototype = new Error(), e.prototype.name = \"InvalidCharacterError\";\nvar r = \"undefined\" != typeof window && window.atob && window.atob.bind(window) || function (r) {\n  var t = String(r).replace(/=+$/, \"\");\n  if (t.length % 4 == 1) throw new e(\"'atob' failed: The string to be decoded is not correctly encoded.\");\n  for (var n, o, a = 0, i = 0, c = \"\"; o = t.charAt(i++); ~o && (n = a % 4 ? 64 * n + o : o, a++ % 4) ? c += String.fromCharCode(255 & n >> (-2 * a & 6)) : 0) o = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\".indexOf(o);\n  return c;\n};\nfunction t(e) {\n  var t = e.replace(/-/g, \"+\").replace(/_/g, \"/\");\n  switch (t.length % 4) {\n    case 0:\n      break;\n    case 2:\n      t += \"==\";\n      break;\n    case 3:\n      t += \"=\";\n      break;\n    default:\n      throw \"Illegal base64url string!\";\n  }\n  try {\n    return function (e) {\n      return decodeURIComponent(r(e).replace(/(.)/g, function (e, r) {\n        var t = r.charCodeAt(0).toString(16).toUpperCase();\n        return t.length < 2 && (t = \"0\" + t), \"%\" + t;\n      }));\n    }(t);\n  } catch (e) {\n    return r(t);\n  }\n}\nfunction n(e) {\n  this.message = e;\n}\nfunction o(e, r) {\n  if (\"string\" != typeof e) throw new n(\"Invalid token specified\");\n  var o = !0 === (r = r || {}).header ? 0 : 1;\n  try {\n    return JSON.parse(t(e.split(\".\")[o]));\n  } catch (e) {\n    throw new n(\"Invalid token specified: \" + e.message);\n  }\n}\nn.prototype = new Error(), n.prototype.name = \"InvalidTokenError\";\nexport default o;\nexport { n as InvalidTokenError };\n//# sourceMappingURL=jwt-decode.esm.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}