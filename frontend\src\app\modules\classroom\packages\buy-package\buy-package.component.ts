import { DOCUMENT, Location } from '@angular/common';
import { Component, HostListener, Inject, Input, OnInit, Renderer2, signal, ViewChild } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { switchMap, take } from 'rxjs/operators';
import { Classroom } from 'src/app/core/models/classroom.model';
import { Country, Language, LeadDynoAffiliate } from 'src/app/core/models/general.model';
import { BuyerUserDetails, PackagePriceHourly, PackageToBuy, PackageToBuyNew, PackageToBuyOrderDeails, PackageType, PackagesModel, SplitPackagesToBuyResponse, mltPackages } from 'src/app/core/models/package.model';
import { User } from 'src/app/core/models/user.model';
import { AuthService } from 'src/app/core/services/auth.service';
import { ClassroomService } from 'src/app/core/services/classroom.service';
import { GeneralService } from 'src/app/core/services/general.service';
import { PackageService } from 'src/app/core/services/package.service';
import { slideInOut2, slideInOut } from 'src/app/helpers/my-animations';
import { SubSink } from 'subsink';
import RevolutCheckout from "@revolut/checkout"
import { MODE } from '@revolut/checkout/types/constants';
import { of } from 'rxjs';
import { ToastService } from 'src/app/core/services/toast.service';
import { ConfirmDialogService } from 'src/app/core/services/confirm-dialog.service';
import { environment } from 'src/environments/environment';
import { HttpHeaders } from '@angular/common/http';
interface PaidResponses {
  [key: string]: any;
}
const isProductionMode = environment.production;
declare var LeadDyno: any;

@Component({
  selector: 'app-buy-package',
  templateUrl: './buy-package.component.html',
  styleUrls: ['./buy-package.component.scss'],
  animations: [slideInOut2, slideInOut],
})
export class BuyPackageComponent implements OnInit {
  @Input() inTrial: boolean = false;
  @Input() hideSection: boolean = false;
  user = {} as User;
  private subs = new SubSink();
  guid: string = "guid-guid"
  public packageToBuyOrderDeails: PackageToBuyOrderDeails = {} as PackageToBuyOrderDeails
  public phoneCodes: any[] = this.generalService.getPhoneCodes();
  public currentStep: number = 1;

  public userClassrooms: Classroom[] = []
  public classroomId: string = ""

  public isNew: boolean = false;

  public numberOfStudents = ["1", "2", "3"]

  payload = {
    pk: 'your-public-key',
    amount: 1000,
    locale: 'en',
    txnType: 'tds',
  };

  public selectedClassroom: Classroom = {} as Classroom;
  public selectedPackageToBuy: mltPackages = {} as mltPackages;
  public selectedPrice: any;
  public selectedLevel: string = "A1";
  public selectedDefaultLevel: string = "A1";
  public selectedNumberOfStudents: string = "1"
  public selectedLanguage: Language = {} as Language;
  public selectedHoursIndex: number = 2;
  public selectedBillingType: string = "receipt";
  public selectedPhoneCode: any | undefined = {} as any | undefined;
  public selectedResidenceCountry: Country | undefined = {} as Country | undefined;
  public buyerUserDetails: BuyerUserDetails = {} as BuyerUserDetails
  public mltLanguages = this.generalService.mltLanguages
  public mltLevels = this.generalService.mltLevels
  public countries = this.generalService.countries
  public mltPricesHourly: PackagePriceHourly[] = []
  public selectedHours: PackagePriceHourly = {} as PackagePriceHourly;
  public mltPackages = this.generalService.mltPackages
  public defaultPackages = PackagesModel.getDefaultPackages();
  public newPackages: PackageToBuyNew[] = [];
  public reqBody: any = {}
  public form: UntypedFormGroup = new UntypedFormGroup({})
  public classroomFromQueryParams: Classroom = {} as Classroom;
  public showSplitPayment = false;
  public splitPaymentPackages: SplitPackagesToBuyResponse[] = [];
  public leadDynoAffiliateId = localStorage.getItem('leadDynoAffiliateId');
  rightSide: any;
  e?: SafeResourceUrl
  responsiveOptions!: any[];
  leadDynoPrivateKey = environment.leadDynoPrivateKey;
  leadDynoScriptLoaded = false;

  hasSwitchedPackage = signal(false);
  constructor(
    private authService: AuthService,
    private activatedRoute: ActivatedRoute,
    private location: Location,
    private classroomService: ClassroomService,
    private generalService: GeneralService,
    private packageService: PackageService,
    private toastService: ToastService,
    public confirmDialogService: ConfirmDialogService,
    private sanitizer: DomSanitizer,
    private router: Router,
    private renderer: Renderer2,
    @Inject(DOCUMENT) private document: Document,
  ) { }

  ngOnInit(): void {
    if (!this.inTrial) {
      const classroomString = this.activatedRoute.snapshot.queryParams['classroom'];
      if (classroomString) {
        this.classroomFromQueryParams = JSON.parse(classroomString);
      }
    }

    this.responsiveOptions = [
      {
        breakpoint: '1199px',
        numVisible: 1,
        numScroll: 1
      },
      {
        breakpoint: '991px',
        numVisible: 2,
        numScroll: 1
      },
      {
        breakpoint: '767px',
        numVisible: 1,
        numScroll: 1
      }
    ];

    this.user = this.authService.getLoggedInUser();
    this.initializeData();
    this.loadToltScript('https://cdn.tolt.io/tolt.js', '344bed5a-704b-49eb-9ec5-f0215c879772');

    // TODO: Add LeadDyno script
    this.loadScript('https://static.leaddyno.com/js').then(() => {
      // LeadDyno script loaded successfully
      // Now, you can add your events
      console.log('LeadDyno script loaded, executing LeadDyno dependent code...');

      this.leadDynoScriptLoaded = true;
      this.addLeadDynoEvents();


      if (this.leadDynoScriptLoaded) {


        this.packageService.leadDynoGetAffiliateEvent({
          key: this.leadDynoPrivateKey,
        }).subscribe({
          next: (res: any) => {
            console.log(res);
          },
          error: (err: any) => {
            console.log(err);
          }
        });


        // this.packageService.addPurchaseEvent(purchaseData).subscribe((res: any) => {
        //   console.log(res);
        // });
      }

    }).catch(error => console.log('Error loading LeadDyno script:', error));

  }

  addLeadDynoEvents(): void {
    // Assuming LeadDyno is available globally
    // Add your LeadDyno events here
    if (typeof LeadDyno !== 'undefined') {
      LeadDyno.key = "6f488593e746d71bf3743119f96e36b92acfd049";
      LeadDyno.recordVisit();
      LeadDyno.autoWatch();
    } else {
      console.error('LeadDyno is not available');
    }

  }

  loadScript(url: string): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      const script = this.renderer.createElement('script');
      script.src = url;
      script.onload = () => {
        resolve();
      };
      script.onerror = (error: any) => {
        reject(error);
      };
      this.renderer.appendChild(document.body, script);
    });
  }

  loadJsScript(renderer: Renderer2, src: string): HTMLScriptElement {
    const script = renderer.createElement('script');
    script.type = 'text/javascript';
    script.src = src;
    renderer.appendChild(this.document.body, script);
    return script;
  }

  removeLeadDynoScript(): void {
    const scriptElement = document.querySelector('script[src="https://static.leaddyno.com/js"]');
    if (scriptElement) {
      scriptElement.remove();
      console.log('LeadDyno script removed');
    }
  }

  loadToltScript(url: string, dataTolt: string): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      const script = this.renderer.createElement('script');
      script.src = url;
      script.setAttribute('id', 'tolt-player');
      script.setAttribute('data-tolt', dataTolt);
      script.onload = () => {
        resolve();
      };
      script.onerror = (error: any) => {
        reject(error);
      };
      this.renderer.appendChild(document.head, script);
    });
  }

  public loadLeadDynoStatic() {
    console.log('preparing to load LeadDyno...Static');
    let node = document.createElement('script');
    node.setAttribute("id", "leaddyno-static");
    node.src = "https://static.leaddyno.com/js";
    node.type = 'text/javascript';
    node.async = true;
    node.charset = 'utf-8';
    document.getElementsByTagName('head')[0].appendChild(node);
  }

  ngAfterViewInit() {
    this.rightSide = document.getElementById('package-overview');
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
    this.removeLeadDynoScript();
    const scriptElement = document.getElementById('tolt-player');
    if (scriptElement) {
      scriptElement.remove();
    }
  }

  get hasSplitPayment(): boolean {
    return this.showSplitPayment;
  }

  get errorControl() {
    return this.form.controls
  }

  trialSection() {
    this.hideSection = !this.hideSection;
    setTimeout(() => {
      [].forEach.call(document.getElementsByClassName('btn'), (el: any) => {
        el.classList.add('hvr-glow')
      });
    }, 1000);
  }

  initializeForm() {
    this.selectedResidenceCountry = this.countries.find(obj => obj.name === this.user.residence);
    this.selectedPhoneCode = this.phoneCodes.filter(el => el.code === this.generalService.extractCountryPrefix(this.user.phone))[0] || this.generalService.getDefaultPhoneCode();
    console.log(this.selectedResidenceCountry);
    this.form = new UntypedFormGroup({
      fname: new UntypedFormControl(this.user.firstName, {
        validators: [Validators.required]
      }),
      lname: new UntypedFormControl(this.user.lastName, {
        validators: [Validators.required]
      }),
      company: new UntypedFormControl("", {}),
      profession: new UntypedFormControl("", {}),
      country: new UntypedFormControl(this.selectedResidenceCountry, {
        validators: [Validators.required]
      }),
      street: new UntypedFormControl("", {
        validators: [Validators.required]
      }),
      number: new UntypedFormControl("", {
        validators: [Validators.required]
      }),
      city: new UntypedFormControl("", {
        validators: [Validators.required]
      }),
      postcode: new UntypedFormControl("", {
        validators: [Validators.required]
      }),
      tax: new UntypedFormControl("", {
      }),
      email: new UntypedFormControl(this.user.email, {
        validators: [Validators.required]
      }),
      phone: new UntypedFormControl(this.generalService.extractPhoneNumber(this.user.phone), {
        validators: [Validators.required]
      }),
      tin: new UntypedFormControl("", {
      }),
    })

  }

  updateFormValidationRules() {
    if (this.isInvoice) {
      this.form.controls['tin'].setValidators([Validators.required]);
    } else {
      this.form.controls['tin'].setValidators([]);
    }

    this.form.controls['tin'].updateValueAndValidity();
  }

  styleAll() {
    setTimeout(() => {
      if (!this.isNew) {
        this.styleClassrooms();
      } else {
        this.selectedBtnStyle(`package-new`)
      }
      this.styleLanguages()
      this.styleHours();
      this.styleNumberOfStudents();
    }, 1000);
  }

  deStyleAll() {
    this.styleClassrooms();
    this.styleLanguages()
    this.styleHours();
    this.styleNumberOfStudents();
  }

  styleClassrooms() {
    this.selectedBtnStyle(`package-btn-${this.selectedClassroom.id}`, `package-btn-teacher-${this.selectedClassroom?.id}`)
  }

  styleLanguages() {
    this.selectedBtnStyle(`language-btn-${this.selectedLanguage.code}`)
  }

  styleNumberOfStudents() {
    if (this.selectedNumberOfStudents > "3") {
      this.selectedNumberOfStudents = "3+"
    }
    this.selectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`)
  }

  styleHours() {
    this.selectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-hours-${this.mltPricesHourly[this.selectedHoursIndex].price}`)
    this.selectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-per-${this.mltPricesHourly[this.selectedHoursIndex].price}`)
  }

  selectedBtnStyle(id: string, extraId?: string) {
    let color = this.inTrial ? '#4895ef' : this.getGradientColor(this.selectedPackageToBuy.type)
    let btn = document.getElementById(id)

    if (btn && this.selectedPackageToBuy) {
      btn!.style.backgroundImage = color;
    }
    if (btn && !id.includes('package-new')) {
      btn!.style.color = "white";
      btn!.style.opacity = "1";
    }

    if (extraId) {
      let extraElement = document.getElementById(extraId);
      if (extraElement) {
        extraElement.style.color = "white";
      }
    }
  }

  deSelectedBtnStyle(id: string, extraId?: string) {
    let color = this.inTrial ? '#4895ef' : this.getCasualColor();
    let btn = document.getElementById(id);

    if (btn) {
      if (!this.isNew && !id.includes('package-btn') && !id.includes('number-btn') && !id.includes('hour-btn') && !id.includes('package-new') && !id.includes('level')) {
        btn!.style.color = "#CCD6FF";
      } else {
        btn!.style.color = color;
      }
      if (!id.includes('package-new')) {
        btn!.style.backgroundColor = "white";
        btn!.style.background = "white";
      }
    }

    if (extraId) {
      let extraElement = document.getElementById(extraId);
      if (extraElement) {
        extraElement.style.color = "#2d2a4b";
      }
    }

  }

  getBtnStyle(notToBeDisabled: boolean) {
    let color = this.inTrial ? '#4895ef' : this.getCasualColor()
    if (this.isNew || notToBeDisabled) {
      return {
        'border': `1px solid ${color}`,
        'color': `${color}`,
      }
    }
    return {
      'border': `1px solid ${color}`,
      'color': `${color}`,
      'opacity': '0.5',
      'cursor': 'initial'
    }
  }

  disableLanguage(id: string) {
    let btn = document.getElementById(id);
    btn!.style.pointerEvents = 'none';
    btn!.style.opacity = '0.5';
  }

  enableLanguage(id: string) {
    let btn = document.getElementById(id);
    btn!.style.pointerEvents = 'initial';
    btn!.style.opacity = '1';
  }

  getLevelIsGreaterThanSelected(level: string) {
    if (this.selectedClassroom.activeLevel == "TBD") {
      return true;
    }
    return level >= this.selectedClassroom.activeLevel
  }

  compareLevels(level1: string, level2: string): number {
    const levelsOrder = ['TBD', 'A1', 'A2', 'B1', 'B2', 'C1', 'C2', 'BS', 'NT21', 'NT22'];
    return levelsOrder.indexOf(level1) - levelsOrder.indexOf(level2);
  }

  getGradientColor(type: string) {
    if (type == PackageType.REGULAR) {
      return "linear-gradient(to top, #122171, #353792, #544eb3, #7366d6, #927ffa)"
    }
    if (type == PackageType.FLEX) {
      return "linear-gradient(to bottom, #7588f4, #5f6ed1, #4955af, #323d8e, #1b276e)"
    }
    if (type == PackageType.PREMIUM) {
      return "linear-gradient(to top, #122171, #004b99, #0074bb, #009ed7, #4dc8f0)"
    }
    return ""
  }

  getCasualColor() {
    return "#657AEF";
  }

  selectClassroom(classroom: Classroom) {
    this.isNew = true;
    [].forEach.call(document.getElementsByClassName('btn'), (el: any) => {
      if (!el.id.includes('package-btn') && !el.id.includes('hour-btn') && !el.id.includes('package-new')) {
        el.classList.remove('hvr-glow');
      }
      setTimeout(() => {
        // el.classList.add('disabled')
      }, 300);
    });
    this.deSelectedBtnStyle(`package-new`)
    this.deSelectedBtnStyle(`package-btn-${this.selectedClassroom.id}`, `package-btn-teacher-${this.selectedClassroom?.id}`)
    // this.deSelectedBtnStyle(`level-btn-${this.selectedLevel}`)
    this.deSelectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`)
    this.deSelectedBtnStyle(`language-btn-${this.selectedLanguage.code}`)

    for (let language of this.mltLanguages) {
      if (this.selectedClassroom.language == language.name) {
        this.deSelectedBtnStyle(`language-btn-${language.code}`)
      }
    }

    this.selectedClassroom = classroom;
    // /Updates the selected classroom's student list by removing any duplicate students.
    this.selectedClassroom.classroomStudents = this.classroomService.getuniqueClassroomStudents(this.selectedClassroom.classroomStudents);
    for (let language of this.mltLanguages) {
      if (this.selectedClassroom.language !== language.name) {
        this.disableLanguage(`language-btn-${language.code}`);
      }
    }
    this.selectedLevel = this.selectedClassroom.activeLevel
    if (this.selectedClassroom.activeLevel == "TBD") {
      this.selectedLevel = "A1"
    }
    this.selectedDefaultLevel = this.selectedClassroom.activeLevel;
    this.selectedNumberOfStudents = this.selectedClassroom.classroomStudents.length.toString();
    this.selectedLanguage = this.mltLanguages.filter(el => el.name.toLowerCase() == this.selectedClassroom.language!.toLowerCase())[0]
    this.reqBody.language = this.selectedLanguage.name;
    this.reqBody.level = this.selectedLevel;
    this.toggleSplitPaymentDisplay();
    console.log(this.showSplitPayment);
    this.getNewPackagePrices();
    this.getNewSplitPackagePrices();
    this.styleAll();
    this.isNew = false;
  }

  selectLanguage(language: Language) {
    if (this.isNew) {
      this.deSelectedBtnStyle(`language-btn-${this.selectedLanguage.code}`)
      this.selectedLanguage = language
      this.reqBody.language = this.selectedLanguage.name;
      this.getNewPackagePrices();
      for (let language of this.mltLanguages) {
        this.deSelectedBtnStyle(`language-btn-${language.code}`);
      }
      this.styleLanguages();
    }
  }

  selectLevel(level: string) {
    if (this.isNew || this.getLevelIsGreaterThanSelected(level)) {
      this.selectedLevel = level
      this.reqBody.level = this.selectedLevel;
      this.getNewPackagePrices();
      this.getNewSplitPackagePrices();
    }
  }

  selectNumber(number: string) {
    if (this.isNew) {
      this.deSelectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`)
      this.selectedNumberOfStudents = number
      this.selectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`)
      this.reqBody.numberOfStudents = this.selectedNumberOfStudents;
      this.getNewPackagePrices();
      this.getNewSplitPackagePrices();
    }
  }

  expiresIn: number = 1;

  selectHours(hour: any, i: number) {
    this.deSelectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-hours-${this.mltPricesHourly[this.selectedHoursIndex].price}`)
    this.deSelectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-per-${this.mltPricesHourly[this.selectedHoursIndex].price}`)
    this.selectedHoursIndex = i;
    this.selectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-hours-${this.mltPricesHourly[this.selectedHoursIndex].price}`)
    this.selectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-per-${this.mltPricesHourly[this.selectedHoursIndex].price}`)
    this.selectedPrice = this.mltPricesHourly[this.selectedHoursIndex].price;
    this.expiresIn = this.mltPricesHourly[this.selectedHoursIndex].expirationMonths;
    this.selectedHours = this.mltPricesHourly[this.selectedHoursIndex];
  }

  selectPackage(packageType: any) {
    this.selectedPackageToBuy = packageType
    this.rightSide.style.background = this.getGradientColor(this.selectedPackageToBuy.type);
    this.styleAll();
    
    this.hasSwitchedPackage.set(false);
  }

  buyNew() {
    [].forEach.call(document.getElementsByClassName('btn'), (el: any) => {
      el.classList.add('hvr-glow')
      el.classList.remove('disabled')
    });
    for (let language of this.mltLanguages) {
      if (this.selectedClassroom.language !== language.name) {
        this.enableLanguage(`language-btn-${language.code}`);
      }
    }
    this.deSelectedBtnStyle(`package-btn-${this.selectedClassroom.id}`, `package-btn-teacher-${this.selectedClassroom?.id}`)
    this.selectedBtnStyle(`package-new`)
    this.isNew = true;
    this.selectedLanguage = this.mltLanguages[0];
    this.selectLanguage(this.selectedLanguage);
    this.deSelectedBtnStyle(`level-btn-${this.selectedLevel}`);
    this.selectedLevel = "A1";
    this.selectedDefaultLevel = "A1";
    this.selectLevel(this.selectedLevel);
    this.deSelectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`);
    this.selectedNumberOfStudents = "1";
    this.styleAll();
    this.getNewPackagePrices();
    // this.getNewSplitPackagePrices();
    this.showSplitPayment = false;
  }

  isReceipt: boolean = true;
  isInvoice: boolean = false;
  isSubmitted: boolean = false;

  chooseReceipt() {
    this.isReceipt = true;
    this.isInvoice = false;
    this.updateFormValidationRules();
  }

  chooseInvoice() {
    this.isReceipt = false;
    this.isInvoice = true;
    this.updateFormValidationRules();
  }

  next() {
    window.scrollTo(0, 0)
    this.currentStep++;
    if (this.currentStep == 2) {
      return;
    }
    if (this.currentStep == 3) {
      this.isSubmitted = true;

      if (this.form.valid) {
        let formValue = this.form.value
        this.buyerUserDetails.fname = formValue.fname
        this.buyerUserDetails.lname = formValue.lname
        this.buyerUserDetails.email = formValue.email
        this.buyerUserDetails.city = formValue.city
        this.buyerUserDetails.company = formValue.company
        this.buyerUserDetails.streetName = formValue.street
        this.buyerUserDetails.streetNumber = formValue.number
        this.buyerUserDetails.tax = formValue.tax
        this.buyerUserDetails.profession = formValue.profession
        this.buyerUserDetails.postcode = formValue.postcode
        this.buyerUserDetails.tin = formValue.tin
        this.buyerUserDetails.phone = formValue.phone
        this.buyerUserDetails.country = formValue.country.name
        return;
      } else {

        this.toastService.setShowToastmessage({
          severity: 'info',
          summary: '',
          detail: 'Please fill all the required fields with *'
        });
        this.currentStep = 2;
        return;
      }
    }
    if (this.currentStep == 4) {
      this.payload.amount = (this.selectedPrice + this.selectedPackageToBuy.costPlus) * 100;
      return;
    }
    if (this.currentStep < 5) {
      this.currentStep++;
    }
    // let classroom = this.isNew ? null : this.selectedClassroom;
    // this.packageToBuyOrderDeails = {
    //   isNew: this.isNew,
    //   selectedClassroom: classroom,
    //   selectedLevel: this.selectedLevel,
    //   selectedLanguage: this.selectedLanguage,
    //   selectedNumberOfStudents: this.selectedNumberOfStudents,
    //   // selectedPackageToBuy: this.selectedPackageToBuy,
    //   buyerUserDetails: this.buyerUserDetails
    // }
  }

  previous() {
    window.scrollTo(0, 0)
    this.styleAll()
    if (this.currentStep > 1) {
      this.currentStep--;
    } else {
      this.toastService.setShowToastmessage({
        severity: 'info',
        summary: '',
        detail: 'There is no previous step'
      });
    }
  }

  goBack() {
    this.location.back();
  }

  toggleSection(sectionElement: any, sectionArrowImgSrc: any) {
    this.generalService.toggleSection(sectionElement, sectionArrowImgSrc, true)
  }
  agreed: boolean = false;
  agree() {
    this.agreed = !this.agreed;
  }

  termsError: boolean = false;
  purchase() {

    if (this.agreed) {
      let classroom = this.isNew ? 0 : this.selectedClassroom.id;
      let req = {
        token: "",
        language: this.selectedLanguage.name,
        level: this.selectedLevel,
        studentsNumber: this.selectedNumberOfStudents,
        hours: this.selectedHours.hours.toString(),
        packageType: this.selectedPackageToBuy.type,
        merchant_order_ext_ref: "",
        classroomId: classroom,
        details: {
          billingType: this.selectedBillingType,
          firstname: this.buyerUserDetails.fname,
          lastname: this.buyerUserDetails.lname,
          email: this.buyerUserDetails.email,
          phone: this.selectedPhoneCode.code + ' ' + this.buyerUserDetails.phone,
          address: this.buyerUserDetails.streetName + " " + this.buyerUserDetails.streetNumber,
          city: this.buyerUserDetails.city,
          country: this.buyerUserDetails.country,
          postcode: this.buyerUserDetails.postcode,
          company: this.buyerUserDetails.company,
          profession: this.buyerUserDetails.profession,
          taxOffice: this.buyerUserDetails.tax,
          vat: this.buyerUserDetails.tin,
        }
      }
      this.initializePaymentForm(req);
    }
    else {
      this.termsError = true;
      this.toastService.setShowToastmessage({
        severity: 'info',
        summary: '',
        detail: 'Please agree to the terms and conditions to make the order'
      });
    }
    //   // this.currentStep = 4;

    //   this.packageService.requestbuyPackage(req).subscribe((res: any) => {
    //     this.everypayLink = this.transform(res.result);
    //     this.generalService.slideNativeElements(true, this.everypay.nativeElement)
    //   })
    // } else {
    //   this.termsError = true;

    // }
  }

  sendRequestTrial() {
    if (this.selectedNumberOfStudents == "3+") {
      this.selectedNumberOfStudents = "4"
    }
    let req = {
      trialLanguage: this.selectedLanguage.name,
      trialLevel: this.selectedLevel,
      trialStudentNumber: this.selectedNumberOfStudents
    }
    this.packageService.requestTrial(req).pipe(take(1)).subscribe(res => {
      this.toastService.setShowToastmessage({
        severity: 'success',
        summary: '',
        detail: 'Your trial has been requested.'
      });
    })
  }

  closePayment() {
    this.router.navigate(['dashboard'])
  }

  async initializeData() {
    try {
      const res = await this.classroomService.getLMSUserClassrooms(this.user.id)
        .pipe(take(1))
        .toPromise(); // Convert to Promise and await its resolution

      // Proceed after receiving the response
      this.userClassrooms = res.filter((item: Classroom) => item && item.language && item.language.trim() !== '');
      this.userClassrooms = this.filterOutTrialClassroomsWithSameTeacher(this.userClassrooms);
      // Create an object to track classrooms by teacher ID
      const classroomsByTeacher: Record<string, Classroom> = {};

      // Update this.userClassrooms and classroomFromQueryParams
      this.userClassrooms = this.userClassrooms.filter((response) => {
        const teacherId = response.teacher?.id ?? null;

        if (teacherId !== null) {
          // Check if the response is of type "Paid" or if there is no existing response for this teacher ID
          if (response.type === "Paid" || !(teacherId in classroomsByTeacher)) {
            classroomsByTeacher[teacherId] = response; // Store the response
          }

          if (
            this.classroomFromQueryParams &&
            this.classroomFromQueryParams.type === "Trial" &&
            this.classroomFromQueryParams.teacher &&
            this.classroomFromQueryParams.teacher.id === teacherId
          ) {
            // Replace classroomFromQueryParams with the paid response
            this.classroomFromQueryParams = response;
          }

          return response; // else Include all classrooms
        }

        return true; // Include classrooms with no teacher ID
      });


      if (this.userClassrooms.length == 0) {
        this.isNew = true;
        this.selectedLanguage = this.mltLanguages[0];
        this.selectedLevel = "A1";
        this.selectedNumberOfStudents = "1";
        this.selectedDefaultLevel = "A1";
      }
      else {
        if (this.classroomFromQueryParams.id) {
          this.selectedClassroom = this.classroomFromQueryParams;
        } else {
          this.selectedClassroom = this.userClassrooms[0];
        }
        for (let language of this.mltLanguages) {
          if (this.selectedClassroom.language !== language.name) {
            this.disableLanguage(`language-btn-${language.code}`);
          }
        }
        this.selectedClassroom.classroomStudents = this.classroomService.getuniqueClassroomStudents(this.selectedClassroom.classroomStudents);
        this.selectedNumberOfStudents = this.selectedClassroom.classroomStudents.length.toString();
        this.selectedLevel = this.selectedClassroom.activeLevel;
        if (this.selectedLevel == "TBD") {
          this.selectedLevel = "A1";
        }
        this.selectedDefaultLevel = this.selectedClassroom.activeLevel;
        this.selectedLanguage = this.mltLanguages.filter(el => el.name == this.selectedClassroom.language!)[0];
        this.toggleSplitPaymentDisplay();
      }
      this.initializeForm();
      this.updateFormValidationRules();
      if (this.selectedNumberOfStudents <= "3") {
        this.reqBody = {
          "language": this.selectedLanguage.name,
          "level": this.selectedLevel,
          "numberOfStudents": this.selectedNumberOfStudents
        }
      }
      else {
        this.reqBody = {
          "language": this.selectedLanguage.name,
          "level": this.selectedLevel,
          "numberOfStudents": "2"
        }
      }
      this.selectedPackageToBuy = this.defaultPackages[1];
      this.mltPricesHourly = this.defaultPackages[0].expiresIn;
      this.getNewPackagePrices();
      this.getNewSplitPackagePrices();
      this.selectedHours = this.mltPricesHourly[2];
      this.expiresIn = this.mltPricesHourly[2].expirationMonths;
      this.styleAll();
    } catch (error) {
      console.error('An error occurred:', error);
    }
  }

  async updatePrices(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      try {
        this.mltPricesHourly.forEach((item, index) => {
          item.price = this.newPackages[index].price;
          item.perHour = this.newPackages[index].perHour;
        });
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  async getNewPackagePrices(): Promise<void> {
    try {
      const res = await this.packageService.getUpdatedPackagesToBuy(this.reqBody).pipe(take(1)).toPromise();
      this.newPackages = res as any[];
      await this.updatePrices();
      this.selectedPrice = this.selectedHours.price;
    } catch (error) {
      console.log('An error occurred:', error);
    }
  }

  async getNewSplitPackagePrices(): Promise<void> {
    try {
      const res = await this.packageService.getSplitPackagesToBuy(this.reqBody).pipe(take(1)).toPromise();
      this.splitPaymentPackages = res as any[];
    } catch (error) {
      console.log('An error occurred:', error);
    }
  }

  onPhoneCodeChange(event: any) {
    setTimeout(() => {
      this.selectedPhoneCode = event.value;
    }, 100);
  }

  initializePaymentForm(data: any) {
    console.log(data);
    this.subs.add((this.hasSplitPayment ? this.packageService.createSplitRevolutOrder(data) : this.packageService.createRevolutOrder(data)).pipe(
      switchMap((order: any) => {
        console.log('order', order);
        var that = this;
        RevolutCheckout(order.public_id, isProductionMode ? 'prod' : 'sandbox').then(function (instance: any) {
          instance.payWithPopup({
            onSuccess() {
              data.token = order.public_id;
              data.merchant_order_ext_ref = order.merchant_order_ext_ref;
              // let event = new CustomEvent('dosomething', {detail: 'hello'});
              that.completeOrder(data, order);
            },
            onError(message: any) {
              console.log(message);
            }
          });
        });
        return of(data);
      }),
      take(1)
    ).subscribe((res: any) => { }))
  }

  completeOrder(paymentResponse: any, order: any) {
    this.subs.add((this.hasSplitPayment ? this.packageService.completeSplitOrder(paymentResponse) : this.packageService.completeOrder(paymentResponse)).subscribe((res: any) => {

      this.currentStep = 4;
      if (this.leadDynoScriptLoaded) {
        const purchaseData = {
          key: this.leadDynoPrivateKey, // Replace with your Leaddyno API key
          email: order.email,
          purchase_amount: order.order_amount.value / 100, // Convert to the correct currency
          purchase_code: order.merchant_order_ext_ref,
          affiliate: localStorage.getItem('leadDynoAffiliateId') || '',
          line_items: [
            {
              sku: order.id,
              quantity: 1,
              description: paymentResponse.language + ' - ' + paymentResponse.level + ' level - ' + paymentResponse.hours + ' hours',
              amount: order.order_amount.value / 100
            },
          ],
        };

        // affiliate exists
        if (this.leadDynoAffiliateId) {
          this.packageService.getLeadDynoAffiliateByAffiliateCode(this.leadDynoAffiliateId,
            this.leadDynoPrivateKey).subscribe({
              next: (res: LeadDynoAffiliate) => {
                console.log(res);
                const randId = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
                const purchaseData =
                {
                  key: this.leadDynoPrivateKey,
                  email: this.user.email,
                  purchase_amount: order.order_amount.value / 100, // Convert to the correct currency
                  purchase_code: order.merchant_order_ext_ref,
                  code: res.affiliate_code || this.leadDynoAffiliateId,
                  affiliate: res.email,
                  line_items: [
                    {
                      sku: order.merchant_order_ext_ref,
                      quantity: 1,
                      description: order.merchant_order_ext_ref,
                      amount: order.order_amount.value / 100,
                    },
                  ],

                };
                this.packageService.addPurchaseEvent(purchaseData).subscribe((res: any) => {
                  console.log(res);
                });
              },
              error: (err: any) => {
                console.log(err);
              }
            });
        } else {
          // without affiliate
          this.packageService.addPurchaseEvent(purchaseData).subscribe((res: any) => {
            console.log(res);
          });
        }
      }

    }))
  }

  getTotalSplitPaymentAmount(): number {
    if (this.splitPaymentPackages.length === 0) {
      return 0;
    }
    const selectedPackage = this.splitPaymentPackages[this.selectedHoursIndex];
    const additionalCost = this.selectedPackageToBuy.costPlus * 0.5;

    return (selectedPackage?.companyFee || 0) + additionalCost;
  }

  getRemainingAmount(): number {
    const totalPrice = this.selectedPrice + this.selectedPackageToBuy.costPlus;
    return totalPrice - this.getTotalSplitPaymentAmount();
  }

  switchBackToPackageType(event: mltPackages) {
    console.log(event);
    this.selectedPackageToBuy = event;
    this.hasSwitchedPackage.set(true);
  }

  onSplitPaymentPayNowTooltipSelected() {
    this.confirmDialogService.setProperties({
      confirmMessage: `The booking fee is an upfront payment, ensuring the immediate availability of your preferred teacher.`,
      acceptBtnLabel: 'OK',
      action: 'cancel-lesson',
      confirmIcon: '',
      acceptBtnImage: '',
      acceptBtnIcon: 'pi pi-check',
      showHeader: true,
      dialogType: 'custom',
      showConfirmIcon: false,
      headerClass: 'my-custom-header-class',
      headerText: 'Booking Fee'
    });
    this.confirmDialogService.show();
  }

  onSplitPaymentPayLaterTooltipSelected() {
    this.confirmDialogService.setProperties({
      confirmMessage: `
      Once your booking fee payment is completed, a customer service representative will contact you shortly to provide further assistance. 
      They will guide you on how to complete the payment process.`,
      acceptBtnLabel: 'OK',
      action: 'cancel-lesson',
      confirmIcon: '',
      acceptBtnImage: '',
      acceptBtnIcon: 'pi pi-check',
      showHeader: true,
      dialogType: 'custom',
      showConfirmIcon: false,
      headerClass: 'my-custom-header-class',
      headerText: 'What is a Remaining Balance?'
    });
    this.confirmDialogService.show();
  }

  private toggleSplitPaymentDisplay() {
    this.showSplitPayment = this.selectedClassroom!.teacher!.accSlitPayments!;
    console.log(this.selectedPackageToBuy);
  }

  /**
  Filters out trial classrooms with the same teacher.
  @param {Classroom[]} userClassrooms - An array of classrooms for the user.
  @returns {Classroom[]} - An array of classrooms with the same teacher as the user.
  */
  private filterOutTrialClassroomsWithSameTeacher(userClassrooms: Classroom[]): Classroom[] {
    return userClassrooms.filter((classroom: Classroom) => {
      // Check if classroom is a trial
      if (classroom.type === 'Trial') {
        // Find classrooms with the same teacher asp user id
        const sameTeacherClassrooms = userClassrooms.filter((c) => c.teacher!.aspUserId === classroom.teacher!.aspUserId);

        // Filter out the trial classroom if there are other classrooms with the same teacher asp user id
        if (sameTeacherClassrooms.length > 1) {
          return false; // Exclude the trial classroom
        }
      }

      return true; // Include other classrooms
    });
  }
}
