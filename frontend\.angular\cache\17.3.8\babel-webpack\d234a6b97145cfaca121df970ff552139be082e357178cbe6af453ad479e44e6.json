{"ast": null, "code": "import { Status } from 'src/app/core/models/classroom.model';\nimport { SubSink } from 'subsink';\nimport * as moment from 'moment';\nimport { PackageState } from 'src/app/core/models/package.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/core/services/general.service\";\nimport * as i3 from \"src/app/core/services/classroom.service\";\nimport * as i4 from \"src/app/core/services/rating-and-report.service\";\nconst _c0 = a0 => ({\n  \"disabled-link\": a0\n});\nfunction ClassroomCardComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40)(2, \"div\", 41);\n    i0.ɵɵtext(3, \"Unfreeze Package \");\n    i0.ɵɵelement(4, \"img\", 42);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ClassroomCardComponent_ng_container_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 43)(2, \"div\")(3, \"span\", 44);\n    i0.ɵɵelement(4, \"span\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 46);\n    i0.ɵɵelement(6, \"span\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 47)(8, \"div\", 48);\n    i0.ɵɵtext(9);\n    i0.ɵɵelement(10, \"br\");\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"completed\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMapInterpolate1(\"progress \", ctx_r0.getStatusColor, \" \");\n    i0.ɵɵattribute(\"data-percentage\", ctx_r0.classroomPercentage);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.classroomPercentage, \"%\");\n  }\n}\nfunction ClassroomCardComponent_ng_container_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 43)(2, \"div\", 49)(3, \"img\", 50);\n    i0.ɵɵlistener(\"error\", function ClassroomCardComponent_ng_container_24_Template_img_error_3_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.generalService.setDefaultUserAvatar($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r0.generalService.getDomainFileNamePath(ctx_r0.classRoom.teacher.avatarUrl), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction ClassroomCardComponent_li_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 21)(1, \"div\", 22);\n    i0.ɵɵtext(2, \"Total Hours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 23)(4, \"p-chip\", 24)(5, \"div\", 51);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.classroomService.getAccumulatedTotalHours(ctx_r0.classRoom.packages), \" hours\");\n  }\n}\nfunction ClassroomCardComponent_li_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 21)(1, \"div\", 22);\n    i0.ɵɵtext(2, \"Hours Left\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 23)(4, \"p-chip\", 24)(5, \"div\", 51);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r0.generalService.convertHoursToMinutesWithSuffix(ctx_r0.classroomService.getAccumulatedHoursLeft(ctx_r0.classRoom.packages), true));\n  }\n}\nfunction ClassroomCardComponent_li_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 21)(1, \"div\", 22);\n    i0.ɵɵtext(2, \"Teacher\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 23)(4, \"p-chip\", 24)(5, \"div\", 51);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r0.generalService.getShortFullName(ctx_r0.classRoom.teacher));\n  }\n}\nfunction ClassroomCardComponent_li_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 21)(1, \"div\", 22);\n    i0.ɵɵtext(2, \"Duration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 23)(4, \"p-chip\", 24)(5, \"div\", 52);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.classroomService.getAccumulatedHoursLeft(ctx_r0.classRoom.packages), \"h\");\n  }\n}\nfunction ClassroomCardComponent_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 13);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.ongoingOrLastPackageWithHoursLeft.type === ctx_r0.classStatus.TRIAL ? \"Trial\" : \"Package\", \" Summary\");\n  }\n}\nfunction ClassroomCardComponent_div_51_ng_container_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 63)(2, \"div\", 64)(3, \"span\", 65);\n    i0.ɵɵtext(4, \"Rate your experience\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"img\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.classroomService.isTrialClassroomWithCompletedLesson(ctx_r0.classRoom))(\"ngClass\", i0.ɵɵpureFunction1(4, _c0, ctx_r0.hasRated))(\"pTooltip\", ctx_r0.hasRated ? \"Already rated by student\" : \"\")(\"routerLink\", ctx_r0.classroomService.getClassroomRateUrl(ctx_r0.classRoom));\n  }\n}\nfunction ClassroomCardComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"ul\", 53)(2, \"li\", 54)(3, \"div\", 55);\n    i0.ɵɵtext(4, \"Package Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 56);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"li\", 54)(8, \"div\", 55);\n    i0.ɵɵtext(9, \"Purchase Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 57);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"li\", 58)(14, \"div\", 59);\n    i0.ɵɵtext(15, \"Expiration Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 57);\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 60);\n    i0.ɵɵtemplate(20, ClassroomCardComponent_div_51_ng_container_20_Template, 6, 6, \"ng-container\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 61)(22, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function ClassroomCardComponent_div_51_Template_button_click_22_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.generalService.navigateToBuyPackage(ctx_r0.classRoom));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.ongoingOrLastPackageWithHoursLeft.type === ctx_r0.classStatus.TRIAL ? \"Free Trial\" : ctx_r0.ongoingOrLastPackageWithHoursLeft.type, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(12, 4, ctx_r0.ongoingOrLastPackageWithHoursLeft.purchasedAt, \"MMM d, y\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(18, 7, ctx_r0.ongoingOrLastPackageWithHoursLeft.expiresOn, \"MMM d, y\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showRateButton);\n  }\n}\nfunction ClassroomCardComponent_ng_container_58_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 68);\n    i0.ɵɵelement(2, \"app-mini-lesson-info-card\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const lesson_r4 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"lesson\", lesson_r4)(\"classroom\", ctx_r0.classRoom);\n  }\n}\nfunction ClassroomCardComponent_ng_container_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ClassroomCardComponent_ng_container_58_ng_container_1_Template, 3, 2, \"ng-container\", 67);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.getFirstThreeRecentLessons());\n  }\n}\nexport class ClassroomCardComponent {\n  constructor(router, cdr, generalService, classroomService, ratingAndReportService) {\n    this.router = router;\n    this.cdr = cdr;\n    this.generalService = generalService;\n    this.classroomService = classroomService;\n    this.ratingAndReportService = ratingAndReportService;\n    this.subs = new SubSink();\n    this.percentage = null;\n    this.color = 'trial-completed';\n    this.showRateButton = false;\n    this.canRate = false;\n    this.canBuy = false;\n    this.canExtend = false;\n    this.extraClass = '';\n    // TODO add real lesson\n    this.classRoom = {};\n    this.classStatus = Status;\n    this.isTablet = false;\n    this.hasRated = false;\n  }\n  ngOnInit() {\n    if (this.isTrial) {\n      this.showRateButton = true;\n      this.subs.add(this.ratingAndReportService.getTeacherTrialRating(+this.classRoom.id).subscribe(res => {\n        console.log(res);\n        if (res) {\n          this.hasRated = !this.generalService.isObjectEmpty(res);\n          console.log(this.hasRated);\n          this.cdr.detectChanges();\n        }\n      }));\n    }\n  }\n  ngAfterViewInit() {\n    this.subs.sink = this.generalService.deviceKind.pipe().subscribe(res => {\n      this.isTablet = !res.w992up;\n    });\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  // Method to navigate to the lesson details page\n  goToLessonDetails() {\n    const navigationExtras = {\n      state: {\n        classRoom: this.classRoom\n      }\n    };\n    if (this.isTrial) {\n      // TODO add real lesson\n      this.router.navigate(['dashboard/classrooms/lessons', this.classRoom.id, 'info', 'teacher'], {\n        state: {\n          classRoom: this.classRoom\n        }\n      });\n    } else {\n      this.router.navigate(['/dashboard/classrooms/lessons', this.classRoom.id, 'details'], navigationExtras);\n    }\n  }\n  get ongoingOrLastPackageWithHoursLeft() {\n    let ongoingPackage = null;\n    let lastPackage = null;\n    for (const pkg of this.classRoom.packages) {\n      if (pkg.state.toLowerCase() === PackageState.ONGOING.toLowerCase() && pkg.hoursLeft > 0) {\n        ongoingPackage = pkg;\n        break;\n      } else {\n        lastPackage = pkg;\n      }\n    }\n    return ongoingPackage || lastPackage;\n  }\n  get isTrial() {\n    return this.classRoom.type === 'Trial';\n  }\n  get getStatusColor() {\n    if (this.classroomService.isTrialClassroomWithCompletedLesson(this.classRoom)) {\n      return 'trial-completed';\n    }\n    // if (this.isTrial) {\n    //   return 'trial';\n    // }\n    switch (this.classRoom.status.toLowerCase()) {\n      case Status.EXPIRED.toLowerCase():\n        return 'expired';\n      case Status.INACTIVE.toLowerCase():\n        return 'inactive-icon';\n      case Status.ON_GOING.toLowerCase():\n        return 'pending';\n      case Status.ON_HOLD.toLowerCase():\n        return 'on-hold-icon';\n      case Status.PENDING.toLowerCase():\n        return 'pending-icon';\n      case Status.TRIAL.toLowerCase():\n        return 'trial-icon';\n      case Status.COMPLETED.toLowerCase():\n        return 'completed';\n      case Status.COMPLETED_TRIAL.toLowerCase():\n        return 'trial-completed';\n      case Status.ARRANGED_TRIAL.toLowerCase():\n        return 'trial';\n      case Status.ONGOING_TRIAL.toLowerCase():\n        return 'trial';\n      case Status.PENDING_TRIAL.toLowerCase():\n        return 'trial';\n      case Status.DISMISSED.toLowerCase():\n        return 'frozen';\n      case Status.DISMISSED_TRIAL.toLowerCase():\n        return 'dismissed';\n      default:\n        return 'primary';\n    }\n  }\n  getStatusIcon() {\n    if (this.classroomService.isTrialClassroomWithCompletedFirstLesson(this.classRoom)) {\n      return 'completed-icon';\n    }\n    switch (this.classRoom.status.toLowerCase()) {\n      case Status.EXPIRED.toLowerCase():\n        return 'expired';\n      case Status.INACTIVE.toLowerCase():\n        return 'inactive-icon';\n      case Status.ON_GOING.toLowerCase():\n        return 'ongoing';\n      case Status.ON_HOLD.toLowerCase():\n        return 'on-hold-icon';\n      case Status.PENDING.toLowerCase():\n        return 'pending-icon';\n      case Status.TRIAL.toLowerCase():\n        return 'trial-icon';\n      case Status.COMPLETED.toLowerCase():\n        return 'completed';\n      case Status.DISMISSED.toLowerCase():\n        return 'frozen';\n      default:\n        return 'default-icon';\n    }\n  }\n  getFirstThreeRecentLessons() {\n    const now = moment();\n    return this.classRoom.lessons.sort(this.sortByDate).slice(0, 3);\n  }\n  get classroomPercentage() {\n    let percentage = (this.classroomService.getAccumulatedTotalHours(this.classRoom.packages) - this.classroomService.getAccumulatedHoursLeft(this.classRoom.packages)) / this.classroomService.getAccumulatedTotalHours(this.classRoom.packages) * 100;\n    if (percentage > 100) {\n      percentage = 100;\n    } else if (percentage < 0) {\n      percentage = 0;\n    }\n    // Limit the number of decimal places to 2\n    percentage = +percentage.toFixed(2);\n    // Round up the percentage\n    percentage = Math.ceil(percentage);\n    // Remove the decimal and ensure the result ends in 0\n    percentage = Math.floor(percentage / 10) * 10;\n    return percentage;\n  }\n  hasAvailableHours() {\n    return this.classroomService.getAccumulatedHoursLeft(this.classRoom.packages) > 0;\n  }\n  sortByDate(a, b) {\n    const dateB = moment(b.startingDate);\n    const dateA = moment(a.startingDate);\n    return dateB.diff(dateA);\n  }\n  static #_ = this.ɵfac = function ClassroomCardComponent_Factory(t) {\n    return new (t || ClassroomCardComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.GeneralService), i0.ɵɵdirectiveInject(i3.ClassroomService), i0.ɵɵdirectiveInject(i4.RatingAndReportService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ClassroomCardComponent,\n    selectors: [[\"app-classroom-card\"]],\n    inputs: {\n      percentage: \"percentage\",\n      color: \"color\",\n      showRateButton: \"showRateButton\",\n      canRate: \"canRate\",\n      canBuy: \"canBuy\",\n      canExtend: \"canExtend\",\n      extraClass: \"extraClass\",\n      classRoom: \"classRoom\"\n    },\n    decls: 61,\n    vars: 32,\n    consts: [[1, \"classroom-card\", \"relative\", \"w-100\", 3, \"ngClass\"], [1, \"top-title\", \"relative\", \"border-round-2xl\", 3, \"ngClass\"], [1, \"circle-icon\", 3, \"ngClass\"], [\"alt\", \"Classroom Icon\", 3, \"src\"], [1, \"center-title\"], [1, \"font-semiBold\", \"m-0\"], [1, \"left-text\", \"left-text-mb\"], [\"tooltipPosition\", \"top\", 1, \"left-text-inner\", 3, \"pTooltip\"], [\"class\", \"right-text\", 4, \"ngIf\"], [1, \"mx-3\", \"mb-3\", \"pb-2\"], [1, \"grid\", \"pt-2\", \"white-wrapper\"], [1, \"col-12\", \"md:col-4\", \"px-0\", \"lg:ml-1\", \"lg:mr-2\", \"px-2\", \"lg:px-0\"], [1, \"card\", \"column-bg-gradient\", \"text-center\"], [1, \"card-title\", \"font-base\", \"py-2\"], [1, \"mob-flex\", \"flex-column\", \"md:flex-row\"], [1, \"card-header\"], [4, \"ngIf\"], [1, \"card-body\"], [1, \"grid\"], [1, \"col-12\", \"flex\", \"flex-wrap\", \"flex-column\", \"justify-content-center\", \"align-items-center\", \"lg:px-4\"], [1, \"list-none\", \"p-0\", \"m-0\", \"mt-2\", \"w-100\"], [1, \"flex\", \"align-items-center\", \"mb-1\", \"surface-border\", \"flex-wrap\"], [1, \"font-sm\", \"w-2\", \"md:w-4\", \"text-right\", \"pr-1\"], [1, \"flex\", \"md:w-8\", \"md:flex-order-0\", \"flex-order-1\"], [1, \"info\", \"ml-2\", \"mb-chip\"], [1, \"content\", \"font-sm\", \"capitalize\"], [\"class\", \"flex align-items-center mb-1 surface-border flex-wrap\", 4, \"ngIf\"], [1, \"col-12\", \"md:col\", \"lg:px-0\"], [1, \"card\", \"column-bg-gradient\"], [\"class\", \"card-title font-base py-2\", 4, \"ngIf\"], [\"class\", \"card-body\", 4, \"ngIf\"], [1, \"col-12\", \"md:col-4\", \"lg:mr-1\", \"lg:ml-2\", \"lg:px-0\", \"mb-height\"], [1, \"card\", \"column-bg-gradient\", \"h-full\", \"pr-0\", \"sm:pr-0\", \"lessons-height\", \"mb-4\", \"sm:mb-0\"], [1, \"card-body\", \"flex\", \"justify-content-center\", \"grid\", \"grid-nogutter\", \"w-full\"], [1, \"flex\", \"flex-wrap\", \"pt-1\"], [\"tooltipPosition\", \"top\", 1, \"col-6\", \"sm:col-4\", \"md:col-12\", \"lg:col-6\", \"p-2\", \"flex\", \"justify-content-center\", \"lg:justify-content-start\", 3, \"pTooltip\"], [\"cardType\", \"request\", 3, \"ngClass\", \"classroom\"], [1, \"bottom-button\"], [\"text\", \"Go to Classroom\", 3, \"click\"], [1, \"right-text\"], [1, \"left-text-inner\"], [1, \"gray-border-button\", \"px-2\"], [\"src\", \"/assets/icons/lessons/frozen-blue.svg\", 1, \"blue-filter\", \"ml-2\", \"max-w-1rem\"], [1, \"flex\", \"justify-content-center\"], [1, \"progress-left\"], [1, \"progress-bar\"], [1, \"progress-right\"], [1, \"progress-value\"], [1, \"text-primary\"], [1, \"avatar-circle\"], [\"width\", \"130\", \"alt\", \"Classroom Image\", 3, \"error\", \"src\"], [1, \"content\", \"font-sm\"], [1, \"content\", \"font-xs\"], [1, \"list-none\", \"px-0\", \"m-0\", \"info-list-middle\", \"mx-1\", \"lg:mx-3\", \"py-2\"], [1, \"flex\", \"align-items-center\", \"py-1\", \"surface-border\", \"flex-wrap\", \"font-xs\"], [1, \"w-6\", \"md:w-6\", \"text-right\", \"lg:text-center\", \"font-xs\"], [1, \"pl-2\", \"md:w-6\", \"md:flex-order-0\", \"flex-order-1\", \"text-primary\", \"font-semiBold\", \"capitalize\"], [1, \"pl-2\", \"md:w-6\", \"md:flex-order-0\", \"flex-order-1\", \"text-primary\", \"font-semiBold\"], [1, \"flex\", \"align-items-center\", \"py-1\", \"surface-border\", \"font-xs\", \"flex-wrap\"], [1, \"w-6\", \"md:w-6\", \"text-right\", \"lg:text-center\"], [1, \"py-3\", \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"flex\", \"w-100\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Buy Package\", 1, \"gradient-blue-package\", \"w-100\", \"border-noround\", \"font-xs\", \"mt-2\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"tooltipPosition\", \"top\", 1, \"p-button-outlined\", \"p-button-rounded\", \"button-outlined\", \"p-button-sm\", 3, \"disabled\", \"ngClass\", \"pTooltip\", \"routerLink\"], [1, \"flex\", \"align-items-center\", \"gap-1\"], [1, \"text-base\", \"s12-16\"], [\"src\", \"/assets/icons/lessons/rating-stars.svg\", \"height\", \"16\"], [4, \"ngFor\", \"ngForOf\"], [1, \"col-6\", \"sm:col-4\", \"md:col-12\", \"lg:col-6\", \"p-2\", \"flex\", \"justify-content-center\", \"lg:justify-content-start\"], [3, \"lesson\", \"classroom\"]],\n    template: function ClassroomCardComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelement(3, \"img\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\")(5, \"div\", 4)(6, \"h4\", 5);\n        i0.ɵɵtext(7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(8, \"div\", 6)(9, \"div\", 7)(10, \"p\");\n        i0.ɵɵtext(11, \"Teacher \");\n        i0.ɵɵelementStart(12, \"b\");\n        i0.ɵɵtext(13);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵtemplate(14, ClassroomCardComponent_div_14_Template, 5, 0, \"div\", 8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"div\", 9)(16, \"div\", 10)(17, \"div\", 11)(18, \"div\", 12)(19, \"div\", 13);\n        i0.ɵɵtext(20, \"Class Overview\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"div\", 14)(22, \"div\", 15);\n        i0.ɵɵtemplate(23, ClassroomCardComponent_ng_container_23_Template, 13, 5, \"ng-container\", 16)(24, ClassroomCardComponent_ng_container_24_Template, 4, 1, \"ng-container\", 16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"div\", 17)(26, \"div\", 18)(27, \"div\", 19)(28, \"ul\", 20)(29, \"li\", 21)(30, \"div\", 22);\n        i0.ɵɵtext(31, \"Status\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(32, \"div\", 23)(33, \"p-chip\", 24)(34, \"div\", 25);\n        i0.ɵɵtext(35);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(36, \"li\", 21)(37, \"div\", 22);\n        i0.ɵɵtext(38, \"Level\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(39, \"div\", 23)(40, \"p-chip\", 24)(41, \"div\", 25);\n        i0.ɵɵtext(42);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵtemplate(43, ClassroomCardComponent_li_43_Template, 7, 1, \"li\", 26)(44, ClassroomCardComponent_li_44_Template, 7, 1, \"li\", 26)(45, ClassroomCardComponent_li_45_Template, 7, 1, \"li\", 26)(46, ClassroomCardComponent_li_46_Template, 7, 1, \"li\", 26);\n        i0.ɵɵelementEnd()()()()()()();\n        i0.ɵɵelementStart(47, \"div\", 27)(48, \"div\", 28)(49, \"div\", 15);\n        i0.ɵɵtemplate(50, ClassroomCardComponent_div_50_Template, 2, 1, \"div\", 29);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(51, ClassroomCardComponent_div_51_Template, 23, 10, \"div\", 30);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(52, \"div\", 31)(53, \"div\", 32)(54, \"div\", 33)(55, \"div\", 34)(56, \"div\", 35);\n        i0.ɵɵelement(57, \"app-mini-lesson-info-card\", 36);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(58, ClassroomCardComponent_ng_container_58_Template, 2, 1, \"ng-container\", 16);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(59, \"div\", 37)(60, \"g-button\", 38);\n        i0.ɵɵlistener(\"click\", function ClassroomCardComponent_Template_g_button_click_60_listener() {\n          return ctx.goToLessonDetails();\n        });\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngClass\", ctx.getStatusColor + \"-gradient-lesson-bg\" + \" \" + ctx.extraClass);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngClass\", ctx.getStatusColor + \"-gradient-lesson-bg\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngClass\", ctx.getStatusColor + \"-gradient-lesson-bg\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"src\", \"/assets/icons/lessons/\" + ctx.getStatusIcon() + \".svg\", i0.ɵɵsanitizeUrl);\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate4(\"\", ctx.isTrial ? \"\" : \"Online\", \" \", ctx.generalService.capitalize(ctx.classRoom.language), \" \", ctx.isTrial ? \" Trial\" : \"\", \" \", ctx.isTrial ? \"Lesson\" : \"Course\", \"\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"pTooltip\", ctx.generalService.getPersonFullName(ctx.classRoom.teacher));\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(ctx.generalService.getShortFullName(ctx.classRoom.teacher));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.classRoom.status === ctx.classStatus.DISMISSED);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isTrial);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isTrial);\n        i0.ɵɵadvance(11);\n        i0.ɵɵtextInterpolate2(\"\", ctx.isTrial ? \" \" : \"\", \" \", ctx.classRoom.status, \"\");\n        i0.ɵɵadvance(7);\n        i0.ɵɵtextInterpolate2(\" \", ctx.classRoom.language ? ctx.generalService.capitalize(ctx.classRoom.language) : \"\", \" \", ctx.classRoom.activeLevel ? ctx.classRoom.activeLevel : \"Pending\", \"\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isTrial);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isTrial);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isTrial);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isTrial);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.ongoingOrLastPackageWithHoursLeft);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.ongoingOrLastPackageWithHoursLeft);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"pTooltip\", !ctx.classroomService.hasClassroomPackageHoursLeft(ctx.classRoom) ? \"No available hours left in classroom\" : \"\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(30, _c0, ctx.classroomService.getAccumulatedHoursLeft(ctx.classRoom.packages) <= 0))(\"classroom\", ctx.classRoom);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.classRoom.lessons.length > 0);\n        i0.ɵɵadvance(2);\n        i0.ɵɵclassMapInterpolate1(\"font-base w-10 width-30 py-1 m-auto \", ctx.getStatusColor + \"-gradient-lesson-bg\", \" \");\n      }\n    },\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.filter-blue[_ngcontent-%COMP%] {\\n  filter: invert(30%) sepia(69%) saturate(993%) hue-rotate(201deg) brightness(82%) contrast(99%);\\n}\\n\\n.filter-white[_ngcontent-%COMP%] {\\n  filter: invert(100%) sepia(0%) saturate(1%) hue-rotate(328deg) brightness(107%) contrast(101%);\\n}\\n\\n.classroom-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  margin-bottom: 3rem;\\n}\\n.classroom-card[_ngcontent-%COMP%]   .top-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  padding: 0.425rem;\\n}\\n.classroom-card[_ngcontent-%COMP%]   .top-title[_ngcontent-%COMP%]   .left-text[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 65px;\\n  top: 0;\\n  bottom: 0;\\n  width: auto;\\n  display: flex;\\n  justify-content: center;\\n  color: #fff;\\n}\\n.classroom-card[_ngcontent-%COMP%]   .top-title[_ngcontent-%COMP%]   .left-text-inner[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n}\\n.classroom-card[_ngcontent-%COMP%]   .top-title[_ngcontent-%COMP%]   .right-text[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 15px;\\n  top: 0;\\n  bottom: 0;\\n  width: auto;\\n  display: flex;\\n  justify-content: center;\\n  color: #fff;\\n}\\n.classroom-card[_ngcontent-%COMP%]   .top-title[_ngcontent-%COMP%]   .center-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  height: 100%;\\n  margin: 0;\\n  color: #fff;\\n  font-size: clamp(1.13rem, 0.78vw + 0.97rem, 1.75rem);\\n}\\n.classroom-card[_ngcontent-%COMP%]   .top-title[_ngcontent-%COMP%]   .circle-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  z-index: 1;\\n  left: -4px;\\n  top: -4px;\\n  width: clamp(3rem, 0.31vw + 2.94rem, 3.25rem);\\n  height: clamp(3rem, 0.31vw + 2.94rem, 3.25rem);\\n  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n.classroom-card[_ngcontent-%COMP%]   .top-title[_ngcontent-%COMP%]   .circle-icon[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 28px;\\n  height: 28px;\\n}\\n.classroom-card[_ngcontent-%COMP%]   .white-wrapper[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 0 0 1rem 1rem;\\n}\\n.classroom-card[_ngcontent-%COMP%]   .column-bg-gradient[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(180deg, hsl(221, 100%, 94%) 0%, hsl(223, 100%, 95%) 11%, hsl(225, 100%, 95%) 22%, hsl(226, 99%, 96%) 33%, hsl(228, 98%, 97%) 44%, hsl(230, 98%, 97%) 56%, hsl(232, 97%, 98%) 67%, hsl(235, 96%, 99%) 78%, hsl(237, 96%, 99%) 89%, hsl(240, 100%, 100%) 100%);\\n}\\n.classroom-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  font-size: 27px;\\n  letter-spacing: 0.01em;\\n  text-align: left;\\n  color: #fff;\\n}\\n.classroom-card[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%] {\\n  border: none;\\n}\\n.classroom-card[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  letter-spacing: 0.01em;\\n  text-align: center;\\n  color: #150b74;\\n}\\n.classroom-card[_ngcontent-%COMP%]   .badge.bg-blue[_ngcontent-%COMP%] {\\n  background: #CCD6FF;\\n  min-height: 33px;\\n}\\n.classroom-card[_ngcontent-%COMP%]   .badge-title[_ngcontent-%COMP%] {\\n  font-size: clamp(0.88rem, 0.16vw + 0.84rem, 1rem);\\n  letter-spacing: 0.01em;\\n  line-height: 1;\\n  text-align: left;\\n  color: #2d2a4b;\\n  text-align: right;\\n  padding-right: 10px;\\n  margin-right: 12px;\\n}\\n.classroom-card[_ngcontent-%COMP%]   .second-content[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  letter-spacing: 0.01em;\\n  line-height: 28px;\\n  text-align: left;\\n  color: #2e3d90;\\n}\\n.classroom-card[_ngcontent-%COMP%]   .schedule-card[_ngcontent-%COMP%]   .icon-text[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  letter-spacing: 0.01em;\\n  line-height: 1;\\n  text-align: left;\\n  color: #2e3d90;\\n}\\n.classroom-card[_ngcontent-%COMP%]   .bottom-button[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 50%;\\n  transform: translateX(-50%) translateY(calc(50% - 65px));\\n  color: #fff;\\n  border: none;\\n  border-radius: 5px;\\n  bottom: -3.8rem;\\n  width: 100%;\\n}\\n.classroom-card[_ngcontent-%COMP%]   .bottom-button[_ngcontent-%COMP%]:first-child {\\n  display: none;\\n}\\n.classroom-card[_ngcontent-%COMP%]   .info-list-middle[_ngcontent-%COMP%] {\\n  font-size: clamp(0.75rem, 0.16vw + 0.72rem, 0.88rem);\\n}\\n.classroom-card[_ngcontent-%COMP%]   .gradient-blue-package[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(90deg, hsl(230, 63%, 29%) 0%, hsl(232, 50%, 35%) 19%, hsl(233, 43%, 41%) 37%, hsl(233, 38%, 46%) 53%, hsl(233, 35%, 51%) 65%, hsl(233, 39%, 57%) 75%, hsl(232, 45%, 62%) 83%, hsl(231, 52%, 67%) 89%, hsl(231, 61%, 73%) 95%, hsl(230, 76%, 78%) 100%);\\n}\\n.classroom-card[_ngcontent-%COMP%]   .extend-backage[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(90deg, hsl(227, 69%, 64%) 0%, hsl(228, 69%, 67%) 18%, hsl(228, 70%, 70%) 35%, hsl(228, 72%, 72%) 50%, hsl(228, 73%, 75%) 61%, hsl(228, 74%, 78%) 70%, hsl(228, 76%, 80%) 78%, hsl(228, 78%, 83%) 85%, hsl(228, 80%, 86%) 93%, hsl(228, 83%, 88%) 100%);\\n}\\n.classroom-card[_ngcontent-%COMP%]   .action-btn[_ngcontent-%COMP%] {\\n  padding: 1.3rem 2.2rem;\\n  border-radius: 100px;\\n  font-size: clamp(0.88rem, 0.47vw + 0.78rem, 1.25rem);\\n  font-weight: bold;\\n}\\n@media only screen and (max-width: 992px) and (max-height: 992px) {\\n  .classroom-card[_ngcontent-%COMP%]   .lessons-height[_ngcontent-%COMP%] {\\n    height: 274px !important;\\n    overflow: hidden;\\n    overflow-y: scroll;\\n  }\\n}\\n\\n[_nghost-%COMP%]     .info .p-chip {\\n  background-color: rgba(204, 214, 255, 0.4392156863);\\n}\\n[_nghost-%COMP%]     .info .p-chip .content {\\n  text-align: left;\\n  padding: 0.2rem;\\n  color: #2e3d90;\\n  font-family: \\\"Proxima Nova SemiBold\\\";\\n}\\n[_nghost-%COMP%]     .info .p-chip-text {\\n  color: #fff;\\n}\\n\\nbutton[disabled][_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  filter: grayscale(1);\\n  opacity: 0.5;\\n}\\n\\n.p-button[_ngcontent-%COMP%] {\\n  padding: 0.514rem 1rem;\\n}\\n.p-button[_ngcontent-%COMP%]   .gradient-blue-package[_ngcontent-%COMP%] {\\n  text-transform: uppercase;\\n}\\n\\n.progress[_ngcontent-%COMP%] {\\n  width: 120px;\\n  height: 120px;\\n  line-height: 120px;\\n  background: none;\\n  margin: 0 auto;\\n  box-shadow: none;\\n  position: relative;\\n}\\n.progress[_ngcontent-%COMP%]:after {\\n  content: \\\"\\\";\\n  width: 100%;\\n  height: 100%;\\n  border-radius: 50%;\\n  border: 5px solid #c6e1f9;\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n}\\n.progress[_ngcontent-%COMP%]    > span[_ngcontent-%COMP%] {\\n  width: 50%;\\n  height: 100%;\\n  overflow: hidden;\\n  position: absolute;\\n  top: 0;\\n  z-index: 1;\\n}\\n.progress[_ngcontent-%COMP%]   .progress-left[_ngcontent-%COMP%] {\\n  left: 0;\\n}\\n.progress[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  background: none;\\n  border-width: 5px;\\n  border-style: solid;\\n  position: absolute;\\n  top: 0;\\n  border-color: #3C98E9;\\n}\\n.progress[_ngcontent-%COMP%]   .progress-left[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  left: 100%;\\n  border-top-right-radius: 60px;\\n  border-bottom-right-radius: 60px;\\n  border-left: 0;\\n  transform-origin: center left;\\n}\\n.progress[_ngcontent-%COMP%]   .progress-right[_ngcontent-%COMP%] {\\n  right: 0;\\n}\\n.progress[_ngcontent-%COMP%]   .progress-right[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  left: -100%;\\n  border-top-left-radius: 60px;\\n  border-bottom-left-radius: 60px;\\n  border-right: 0;\\n  transform-origin: center right;\\n}\\n.progress[_ngcontent-%COMP%]   .progress-value[_ngcontent-%COMP%] {\\n  display: flex;\\n  border-radius: 50%;\\n  font-size: 30px;\\n  text-align: center;\\n  line-height: 20px;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  font-weight: 300;\\n}\\n.progress[_ngcontent-%COMP%]   .progress-value[_ngcontent-%COMP%]   div[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n}\\n.progress[_ngcontent-%COMP%]   .progress-value[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  text-transform: uppercase;\\n}\\n\\n\\n\\n\\n\\n.progress[data-percentage=\\\"10\\\"][_ngcontent-%COMP%]   .progress-right[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_loading-1 1.5s linear forwards;\\n}\\n.progress[data-percentage=\\\"10\\\"][_ngcontent-%COMP%]   .progress-left[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  animation: 0;\\n}\\n\\n.progress[data-percentage=\\\"20\\\"][_ngcontent-%COMP%]   .progress-right[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_loading-2 1.5s linear forwards;\\n}\\n.progress[data-percentage=\\\"20\\\"][_ngcontent-%COMP%]   .progress-left[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  animation: 0;\\n}\\n\\n.progress[data-percentage=\\\"30\\\"][_ngcontent-%COMP%]   .progress-right[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_loading-3 1.5s linear forwards;\\n}\\n.progress[data-percentage=\\\"30\\\"][_ngcontent-%COMP%]   .progress-left[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  animation: 0;\\n}\\n\\n.progress[data-percentage=\\\"40\\\"][_ngcontent-%COMP%]   .progress-right[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_loading-4 1.5s linear forwards;\\n}\\n.progress[data-percentage=\\\"40\\\"][_ngcontent-%COMP%]   .progress-left[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  animation: 0;\\n}\\n\\n.progress[data-percentage=\\\"50\\\"][_ngcontent-%COMP%]   .progress-right[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_loading-5 1.5s linear forwards;\\n}\\n.progress[data-percentage=\\\"50\\\"][_ngcontent-%COMP%]   .progress-left[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  animation: 0;\\n}\\n\\n.progress[data-percentage=\\\"60\\\"][_ngcontent-%COMP%]   .progress-right[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_loading-5 1.5s linear forwards;\\n}\\n.progress[data-percentage=\\\"60\\\"][_ngcontent-%COMP%]   .progress-left[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_loading-1 1.5s linear forwards 1.5s;\\n}\\n\\n.progress[data-percentage=\\\"70\\\"][_ngcontent-%COMP%]   .progress-right[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_loading-5 1.5s linear forwards;\\n}\\n.progress[data-percentage=\\\"70\\\"][_ngcontent-%COMP%]   .progress-left[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_loading-2 1.5s linear forwards 1.5s;\\n}\\n\\n.progress[data-percentage=\\\"80\\\"][_ngcontent-%COMP%]   .progress-right[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_loading-5 1.5s linear forwards;\\n}\\n.progress[data-percentage=\\\"80\\\"][_ngcontent-%COMP%]   .progress-left[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_loading-3 1.5s linear forwards 1.5s;\\n}\\n\\n.progress[data-percentage=\\\"90\\\"][_ngcontent-%COMP%]   .progress-right[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_loading-5 1.5s linear forwards;\\n}\\n.progress[data-percentage=\\\"90\\\"][_ngcontent-%COMP%]   .progress-left[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_loading-4 1.5s linear forwards 1.5s;\\n}\\n\\n.progress[data-percentage=\\\"100\\\"][_ngcontent-%COMP%]   .progress-right[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_loading-5 1.5s linear forwards;\\n}\\n.progress[data-percentage=\\\"100\\\"][_ngcontent-%COMP%]   .progress-left[_ngcontent-%COMP%]   .progress-bar[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_loading-5 1.5s linear forwards 1.5s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_loading-1 {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(36deg);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_loading-2 {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(72deg);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_loading-3 {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(108deg);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_loading-4 {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(144deg);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_loading-5 {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(180deg);\\n  }\\n}\\n.progress[_ngcontent-%COMP%] {\\n  margin-bottom: 1em;\\n}\\n\\n@media only screen and (max-width: 768px) {\\n  .font-sm[_ngcontent-%COMP%], .font-xs[_ngcontent-%COMP%] {\\n    font-size: 14px !important;\\n  }\\n  .font-base[_ngcontent-%COMP%] {\\n    font-size: 1.3rem !important;\\n  }\\n  .left-text-mb[_ngcontent-%COMP%] {\\n    position: unset !important;\\n  }\\n  .left-text-mb[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 13px;\\n  }\\n  .mob-flex[_ngcontent-%COMP%] {\\n    display: flex;\\n    margin-top: 1rem;\\n    justify-content: space-between;\\n    align-items: center;\\n  }\\n  .mob-flex[_ngcontent-%COMP%]   .avatar-circle[_ngcontent-%COMP%] {\\n    width: 90px;\\n    height: 90px;\\n    margin-left: 0.6rem;\\n  }\\n  .mob-flex[_ngcontent-%COMP%]   .mb-chip[_ngcontent-%COMP%] {\\n    margin-left: 2.5rem !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["Status", "SubSink", "moment", "PackageState", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelement", "ɵɵelementEnd", "ɵɵelementContainerStart", "ɵɵadvance", "ɵɵclassMapInterpolate1", "ctx_r0", "getStatusColor", "ɵɵtextInterpolate1", "classroomPercentage", "ɵɵlistener", "ClassroomCardComponent_ng_container_24_Template_img_error_3_listener", "$event", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "generalService", "setDefaultUserAvatar", "ɵɵproperty", "getDomainFileNamePath", "classRoom", "teacher", "avatarUrl", "ɵɵsanitizeUrl", "classroomService", "getAccumulatedTotalHours", "packages", "ɵɵtextInterpolate", "convertHoursToMinutesWithSuffix", "getAccumulatedHoursLeft", "getShortFullName", "ongoingOrLastPackageWithHoursLeft", "type", "classStatus", "TRIAL", "isTrialClassroomWithCompletedLesson", "ɵɵpureFunction1", "_c0", "hasRated", "getClassroomRateUrl", "ɵɵtemplate", "ClassroomCardComponent_div_51_ng_container_20_Template", "ClassroomCardComponent_div_51_Template_button_click_22_listener", "_r3", "navigateToBuyPackage", "ɵɵpipeBind2", "purchasedAt", "expiresOn", "showRateButton", "lesson_r4", "ClassroomCardComponent_ng_container_58_ng_container_1_Template", "getFirstThreeRecentLessons", "ClassroomCardComponent", "constructor", "router", "cdr", "ratingAndReportService", "subs", "percentage", "color", "canRate", "canBuy", "canExtend", "extraClass", "isTablet", "ngOnInit", "isTrial", "add", "getTeacherTrialRating", "id", "subscribe", "res", "console", "log", "isObjectEmpty", "detectChanges", "ngAfterViewInit", "sink", "deviceKind", "pipe", "w992up", "ngOnDestroy", "unsubscribe", "goToLessonDetails", "navigationExtras", "state", "navigate", "ongoingPackage", "lastPackage", "pkg", "toLowerCase", "ONGOING", "hoursLeft", "status", "EXPIRED", "INACTIVE", "ON_GOING", "ON_HOLD", "PENDING", "COMPLETED", "COMPLETED_TRIAL", "ARRANGED_TRIAL", "ONGOING_TRIAL", "PENDING_TRIAL", "DISMISSED", "DISMISSED_TRIAL", "getStatusIcon", "isTrialClassroomWithCompletedFirstLesson", "now", "lessons", "sort", "sortByDate", "slice", "toFixed", "Math", "ceil", "floor", "hasAvailableHours", "a", "b", "dateB", "startingDate", "dateA", "diff", "_", "ɵɵdirectiveInject", "i1", "Router", "ChangeDetectorRef", "i2", "GeneralService", "i3", "ClassroomService", "i4", "RatingAndReportService", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "ClassroomCardComponent_Template", "rf", "ctx", "ClassroomCardComponent_div_14_Template", "ClassroomCardComponent_ng_container_23_Template", "ClassroomCardComponent_ng_container_24_Template", "ClassroomCardComponent_li_43_Template", "ClassroomCardComponent_li_44_Template", "ClassroomCardComponent_li_45_Template", "ClassroomCardComponent_li_46_Template", "ClassroomCardComponent_div_50_Template", "ClassroomCardComponent_div_51_Template", "ClassroomCardComponent_ng_container_58_Template", "ClassroomCardComponent_Template_g_button_click_60_listener", "ɵɵtextInterpolate4", "capitalize", "language", "getPersonFullName", "ɵɵtextInterpolate2", "activeLevel", "hasClassroomPackageHoursLeft", "length"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\classroom\\classroom-card\\classroom-card.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\classroom\\classroom-card\\classroom-card.component.html"], "sourcesContent": ["import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';\r\nimport { NavigationExtras, Router } from '@angular/router';\r\nimport { Classroom, Status } from 'src/app/core/models/classroom.model';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { Lesson } from 'src/app/core/models/lesson.model';\r\nimport { SubSink } from 'subsink';\r\nimport { RatingAndReportService } from 'src/app/core/services/rating-and-report.service';\r\nimport * as moment from 'moment';\r\nimport { PackageState } from 'src/app/core/models/package.model';\r\ntype ColorType = 'primary' | 'trial' | 'trial-completed' | 'disabled' | 'expired' | 'frozen';\r\n@Component({\r\n  selector: 'app-classroom-card',\r\n  templateUrl: './classroom-card.component.html',\r\n  styleUrls: ['./classroom-card.component.scss'],\r\n  changeDetection: ChangeDetectionStrategy.OnPush\r\n})\r\nexport class ClassroomCardComponent implements OnInit, AfterViewInit {\r\n  private subs = new SubSink();\r\n  @Input() percentage: number | null = null;\r\n  @Input() color: ColorType = 'trial-completed';\r\n  @Input() showRateButton = false;\r\n  @Input() canRate = false;\r\n  @Input() canBuy = false;\r\n  @Input() canExtend = false;\r\n  @Input() extraClass = '';\r\n  // TODO add real lesson\r\n  @Input() classRoom = {} as Classroom;\r\n  classStatus = Status;\r\n  isTablet = false;\r\n  hasRated = false;\r\n  constructor(\r\n    private router: Router,\r\n    private cdr: ChangeDetectorRef,\r\n    public generalService: GeneralService,\r\n    public classroomService: ClassroomService,\r\n    public ratingAndReportService: RatingAndReportService,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    if (this.isTrial) {\r\n      this.showRateButton = true;\r\n\r\n      this.subs.add(this.ratingAndReportService.getTeacherTrialRating(+this.classRoom.id).subscribe((res) => {\r\n        console.log(res);\r\n        if (res) {\r\n          this.hasRated = !this.generalService.isObjectEmpty(res);\r\n          console.log(this.hasRated);\r\n          this.cdr.detectChanges();\r\n        }\r\n      }));\r\n    }\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.subs.sink = this.generalService.deviceKind.pipe(\r\n    ).subscribe((res) => {\r\n      this.isTablet = !res.w992up!;\r\n    });\r\n\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  // Method to navigate to the lesson details page\r\n  goToLessonDetails() {\r\n    const navigationExtras: NavigationExtras = {\r\n      state: { classRoom: this.classRoom }\r\n    };\r\n    if (this.isTrial) {\r\n      // TODO add real lesson\r\n      this.router.navigate(['dashboard/classrooms/lessons', this.classRoom.id, 'info', 'teacher'], {\r\n        state: { classRoom: this.classRoom }\r\n      });\r\n    } else {\r\n      this.router.navigate(['/dashboard/classrooms/lessons', this.classRoom.id, 'details'], navigationExtras);\r\n    }\r\n  }\r\n\r\n  get ongoingOrLastPackageWithHoursLeft(): any {\r\n    let ongoingPackage = null;\r\n    let lastPackage = null;\r\n  \r\n    for (const pkg of this.classRoom.packages!) {\r\n      if (pkg.state.toLowerCase() === PackageState.ONGOING.toLowerCase() && pkg.hoursLeft > 0) {\r\n        ongoingPackage = pkg;\r\n        break;\r\n      } else {\r\n        lastPackage = pkg;\r\n      }\r\n    }\r\n  \r\n    return ongoingPackage || lastPackage;\r\n  }\r\n\r\n  get isTrial(): boolean {\r\n    return this.classRoom.type === 'Trial';\r\n  }\r\n\r\n  get getStatusColor(): string {\r\n    if (this.classroomService.isTrialClassroomWithCompletedLesson(this.classRoom)) {\r\n      return 'trial-completed';\r\n    }\r\n    // if (this.isTrial) {\r\n    //   return 'trial';\r\n    // }\r\n    switch (this.classRoom.status.toLowerCase()) {\r\n      case Status.EXPIRED.toLowerCase():\r\n        return 'expired';\r\n      case Status.INACTIVE.toLowerCase():\r\n        return 'inactive-icon';\r\n      case Status.ON_GOING.toLowerCase():\r\n        return 'pending';\r\n      case Status.ON_HOLD.toLowerCase():\r\n        return 'on-hold-icon';\r\n      case Status.PENDING.toLowerCase():\r\n        return 'pending-icon';\r\n      case Status.TRIAL.toLowerCase():\r\n        return 'trial-icon';\r\n      case Status.COMPLETED.toLowerCase():\r\n        return 'completed';\r\n      case Status.COMPLETED_TRIAL.toLowerCase():\r\n        return 'trial-completed';\r\n      case Status.ARRANGED_TRIAL.toLowerCase():\r\n        return 'trial';\r\n      case Status.ONGOING_TRIAL.toLowerCase():\r\n        return 'trial';\r\n      case Status.PENDING_TRIAL.toLowerCase():\r\n        return 'trial';\r\n      case Status.DISMISSED.toLowerCase():\r\n        return 'frozen';\r\n      case Status.DISMISSED_TRIAL.toLowerCase():\r\n        return 'dismissed';\r\n      default:\r\n        return 'primary';\r\n    }\r\n  }\r\n\r\n  getStatusIcon(): string {\r\n    if (this.classroomService.isTrialClassroomWithCompletedFirstLesson(this.classRoom)) {\r\n      return 'completed-icon';\r\n    }\r\n\r\n    switch (this.classRoom.status.toLowerCase()) {\r\n      case Status.EXPIRED.toLowerCase():\r\n        return 'expired';\r\n      case Status.INACTIVE.toLowerCase():\r\n        return 'inactive-icon';\r\n      case Status.ON_GOING.toLowerCase():\r\n        return 'ongoing';\r\n      case Status.ON_HOLD.toLowerCase():\r\n        return 'on-hold-icon';\r\n      case Status.PENDING.toLowerCase():\r\n        return 'pending-icon';\r\n      case Status.TRIAL.toLowerCase():\r\n        return 'trial-icon';\r\n      case Status.COMPLETED.toLowerCase():\r\n        return 'completed';\r\n      case Status.DISMISSED.toLowerCase():\r\n        return 'frozen';\r\n      default:\r\n        return 'default-icon';\r\n    }\r\n  }\r\n\r\n  getFirstThreeRecentLessons(): Lesson[] {\r\n    const now = moment();\r\n    return this.classRoom.lessons!.sort(this.sortByDate).slice(0, 3);\r\n  }\r\n\r\n  get classroomPercentage() {\r\n    let percentage = (\r\n      (this.classroomService.getAccumulatedTotalHours(this.classRoom.packages!) - this.classroomService.getAccumulatedHoursLeft(this.classRoom.packages!)) /\r\n      this.classroomService.getAccumulatedTotalHours(this.classRoom.packages!)\r\n    ) * 100;\r\n  \r\n    if (percentage > 100) {\r\n      percentage = 100;\r\n    } else if (percentage < 0) {\r\n      percentage = 0;\r\n    }\r\n  \r\n    // Limit the number of decimal places to 2\r\n    percentage = +percentage.toFixed(2);\r\n  \r\n    // Round up the percentage\r\n    percentage = Math.ceil(percentage);\r\n  \r\n    // Remove the decimal and ensure the result ends in 0\r\n    percentage = Math.floor(percentage / 10) * 10;\r\n  \r\n    return percentage;\r\n  }\r\n\r\n  hasAvailableHours() {\r\n    return this.classroomService.getAccumulatedHoursLeft(this.classRoom.packages!) > 0;\r\n  }\r\n\r\n  private sortByDate(a: Lesson, b: Lesson) {\r\n    const dateB = moment(b.startingDate);\r\n    const dateA = moment(a.startingDate);\r\n    return dateB.diff(dateA);\r\n  }\r\n\r\n}\r\n", "<div class=\"classroom-card relative w-100 \" [ngClass]=\"getStatusColor + '-gradient-lesson-bg' + ' ' + extraClass\">\r\n  <div class=\"top-title relative border-round-2xl \" [ngClass]=\"getStatusColor + '-gradient-lesson-bg'\">\r\n    <div class=\"circle-icon\" [ngClass]=\"getStatusColor + '-gradient-lesson-bg'\">\r\n      <img [src]=\"'/assets/icons/lessons/' + getStatusIcon() + '.svg'\" alt=\"Classroom Icon\">\r\n    </div>\r\n    <div>\r\n      <div class=\"center-title\">\r\n        <h4 class=\"font-semiBold m-0\">{{ isTrial ? '' : 'Online' }} {{ generalService.capitalize(classRoom.language!) }} {{ isTrial ? ' Trial' : '' }} {{isTrial ? 'Lesson' : 'Course'}}</h4>\r\n      </div>\r\n      <div class=\"left-text left-text-mb\">\r\n        <div class=\"left-text-inner\" tooltipPosition=\"top\" [pTooltip]=\"generalService.getPersonFullName(classRoom.teacher)\">\r\n          <p>Teacher <b>{{generalService.getShortFullName(classRoom.teacher)}}</b></p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"right-text\" *ngIf=\"classRoom.status === classStatus.DISMISSED\">\r\n      <div class=\"left-text-inner\">\r\n        <div class=\"gray-border-button px-2\">Unfreeze Package  \r\n          <img class=\"blue-filter ml-2 max-w-1rem\" src=\"/assets/icons/lessons/frozen-blue.svg\" />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"mx-3 mb-3 pb-2\">\r\n    <div class=\"grid pt-2 white-wrapper\">\r\n      <div class=\"col-12 md:col-4 px-0 lg:ml-1 lg:mr-2 px-2 lg:px-0\">\r\n        <div class=\"card column-bg-gradient text-center\">\r\n          <div class=\"card-title font-base py-2\">Class Overview</div>\r\n          <div class=\"mob-flex flex-column md:flex-row\">\r\n            <div class=\"card-header\">\r\n              <ng-container *ngIf=\"!isTrial\">\r\n              <div class=\"flex justify-content-center\">\r\n                <div class=\"progress {{getStatusColor}} \" [attr.data-percentage]=\"classroomPercentage\">\r\n                  <span class=\"progress-left\">\r\n                    <span class=\"progress-bar\"></span>\r\n                  </span>\r\n                  <span class=\"progress-right\">\r\n                    <span class=\"progress-bar\"></span>\r\n                  </span>\r\n                  <div class=\"progress-value\">\r\n                    <div class=\"text-primary\">\r\n                      {{classroomPercentage}}%<br>\r\n                      <span>completed</span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n            </div>\r\n            </ng-container>\r\n\r\n          <ng-container *ngIf=\"isTrial\">\r\n              <div class=\" flex justify-content-center\">\r\n                <div class=\"avatar-circle\">\r\n                  <img [src]=\"generalService.getDomainFileNamePath(classRoom.teacher.avatarUrl)\"  (error)=\"generalService.setDefaultUserAvatar($event)\" width=\"130\" alt=\"Classroom Image\">\r\n                </div>\r\n              </div>\r\n          </ng-container>\r\n            </div>\r\n            <div class=\"card-body \">\r\n              <div class=\"grid\">\r\n                <div class=\"col-12 flex flex-wrap flex-column justify-content-center align-items-center lg:px-4\">\r\n                  <ul class=\"list-none p-0 m-0 mt-2 w-100\">\r\n                    <li class=\"flex align-items-center mb-1 surface-border flex-wrap\">\r\n                      <div class=\"font-sm w-2 md:w-4 text-right pr-1\">Status</div>\r\n                      <div class=\"flex md:w-8 md:flex-order-0 flex-order-1\">\r\n                        <p-chip class=\" info ml-2 mb-chip\">\r\n                          <div class=\"content font-sm capitalize\">{{ isTrial ? ' ' : '' }} {{classRoom.status}}</div>\r\n                        </p-chip>\r\n                      </div>\r\n                    </li>\r\n                    <li class=\"flex align-items-center mb-1 surface-border flex-wrap\">\r\n                      <div class=\"font-sm w-2 md:w-4 text-right pr-1\">Level</div>\r\n                      <div class=\"flex md:w-8 md:flex-order-0 flex-order-1\">\r\n                        <p-chip class=\" info ml-2 mb-chip\">\r\n                          <div class=\"content font-sm capitalize\">\r\n                            {{classRoom.language ? generalService.capitalize(classRoom.language!) : ''}} \r\n                            {{classRoom.activeLevel ? classRoom.activeLevel : 'Pending'}}</div>\r\n                        </p-chip>\r\n                      </div>\r\n                    </li>\r\n                    <li *ngIf=\"!isTrial\" class=\"flex align-items-center mb-1 surface-border flex-wrap\">\r\n                      <div class=\"font-sm w-2 md:w-4 text-right pr-1\">Total Hours</div>\r\n                      <div class=\"flex md:w-8 md:flex-order-0 flex-order-1\">\r\n                        <p-chip class=\" info ml-2 mb-chip\">\r\n                          <div class=\"content font-sm\">{{classroomService.getAccumulatedTotalHours(this.classRoom.packages!)}} hours</div>\r\n                        </p-chip>\r\n                      </div>\r\n                    </li>\r\n                    <li *ngIf=\"!isTrial\" class=\"flex align-items-center mb-1 surface-border flex-wrap\">\r\n                      <div class=\"font-sm w-2 md:w-4 text-right pr-1\">Hours Left</div>\r\n                      <div class=\"flex md:w-8 md:flex-order-0 flex-order-1\">\r\n                        <p-chip class=\" info ml-2 mb-chip\">\r\n                          <div class=\"content font-sm\">{{\r\n                            generalService.convertHoursToMinutesWithSuffix(classroomService.getAccumulatedHoursLeft(this.classRoom.packages!), true)}}</div>\r\n                        </p-chip>\r\n                      </div>\r\n                    </li>\r\n                    <li *ngIf=\"isTrial\" class=\"flex align-items-center mb-1 surface-border flex-wrap\">\r\n                      <div class=\"font-sm w-2 md:w-4 text-right pr-1\">Teacher</div>\r\n                      <div class=\"flex md:w-8 md:flex-order-0 flex-order-1\">\r\n                        <p-chip class=\" info ml-2 mb-chip\">\r\n                          <div class=\"content font-sm\">{{generalService.getShortFullName(classRoom.teacher)}}</div>\r\n                        </p-chip>\r\n                      </div>\r\n                    </li>\r\n                    <li *ngIf=\"isTrial\" class=\"flex align-items-center mb-1 surface-border flex-wrap\">\r\n                      <div class=\"font-sm w-2 md:w-4 text-right pr-1\">Duration</div>\r\n                      <div class=\"flex md:w-8 md:flex-order-0 flex-order-1\">\r\n                        <p-chip class=\"info ml-2 mb-chip\">\r\n                          <div class=\"content font-xs\">{{classroomService.getAccumulatedHoursLeft(classRoom.packages!)}}h</div>\r\n                        </p-chip>\r\n                        \r\n                      </div>\r\n                    </li>\r\n                  </ul>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-12 md:col lg:px-0\">\r\n        <div class=\"card column-bg-gradient\">\r\n          <div class=\"card-header\">\r\n            <div class=\"card-title font-base py-2\" *ngIf=\"ongoingOrLastPackageWithHoursLeft\">\r\n              {{ ongoingOrLastPackageWithHoursLeft.type === classStatus.TRIAL ? 'Trial' : 'Package' }} Summary</div>\r\n          </div>\r\n          <div class=\"card-body\" *ngIf=\"ongoingOrLastPackageWithHoursLeft\">\r\n            <ul class=\"list-none px-0 m-0 info-list-middle mx-1 lg:mx-3 py-2\">\r\n              <li class=\"flex align-items-center py-1 surface-border flex-wrap font-xs\">\r\n                <div class=\"w-6 md:w-6 text-right lg:text-center font-xs\">Package Type</div>\r\n                <div class=\"pl-2 md:w-6 md:flex-order-0 flex-order-1 text-primary font-semiBold capitalize\">\r\n                  {{ ongoingOrLastPackageWithHoursLeft.type === classStatus.TRIAL ? 'Free Trial' : ongoingOrLastPackageWithHoursLeft.type }}</div>\r\n              </li>\r\n              <li class=\"flex align-items-center py-1 surface-border flex-wrap font-xs\">\r\n                <div class=\"w-6 md:w-6 text-right lg:text-center font-xs\">Purchase Date</div>\r\n                <div class=\"pl-2 md:w-6 md:flex-order-0 flex-order-1 text-primary font-semiBold\">{{ ongoingOrLastPackageWithHoursLeft.purchasedAt | date: 'MMM d, y' }}</div>\r\n              </li>\r\n              <li class=\"flex align-items-center py-1 surface-border font-xs flex-wrap\">\r\n                <div class=\"w-6 md:w-6 text-right lg:text-center\">Expiration Date</div>\r\n                <div class=\"pl-2 md:w-6 md:flex-order-0 flex-order-1 text-primary font-semiBold\">{{ ongoingOrLastPackageWithHoursLeft.expiresOn | date: 'MMM d, y' }}</div>\r\n              </li>\r\n            </ul>\r\n            <div class=\"py-3 flex align-items-center justify-content-center\">\r\n              <ng-container  *ngIf=\"showRateButton\">\r\n              <button [disabled]=\"!classroomService.isTrialClassroomWithCompletedLesson(classRoom)\" pButton type=\"button\" \r\n              class=\"p-button-outlined p-button-rounded button-outlined p-button-sm\"\r\n              [ngClass]=\"{'disabled-link':  hasRated}\" \r\n              [pTooltip]=\"hasRated ? 'Already rated by student' : ''\"\r\n              tooltipPosition=\"top\"\r\n              [routerLink]=\"classroomService.getClassroomRateUrl(classRoom)\">\r\n                <div class=\"flex align-items-center gap-1\">\r\n                  <span class=\"text-base s12-16\">Rate your experience</span>  <img src=\"/assets/icons/lessons/rating-stars.svg\" height=\"16\" />\r\n                </div>\r\n               \r\n              </button>\r\n            </ng-container>\r\n            </div>\r\n            <!-- <div class=\"flex w-100\">\r\n              <button class=\"extend-backage w-100 border-noround\" pButton type=\"button\" label=\"EXTEND Package\"></button>\r\n            </div> -->\r\n            <div class=\"flex w-100\">\r\n              <button (click)=\"generalService.navigateToBuyPackage(classRoom)\" class=\"gradient-blue-package w-100 border-noround font-xs mt-2\" pButton type=\"button\" label=\"Buy Package\"></button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"col-12 md:col-4 lg:mr-1 lg:ml-2 lg:px-0 mb-height\">\r\n        <div class=\"card column-bg-gradient h-full pr-0 sm:pr-0 lessons-height mb-4 sm:mb-0\">\r\n          <div class=\"card-body flex justify-content-center grid grid-nogutter w-full\">\r\n            <!-- <p-scrollPanel [style]=\"{width: '100%', height: '300px'}\" styleClass=\"custombar1\"> -->\r\n            <div class=\" flex flex-wrap pt-1\">\r\n              <div class=\"col-6 sm:col-4 md:col-12 lg:col-6 p-2 flex justify-content-center lg:justify-content-start\"\r\n              [pTooltip]=\"!classroomService.hasClassroomPackageHoursLeft(classRoom) ? 'No available hours left in classroom': ''\" tooltipPosition=\"top\"\r\n              >\r\n              <app-mini-lesson-info-card [ngClass]=\"{'disabled-link': classroomService.getAccumulatedHoursLeft(classRoom.packages!) <= 0 }\" cardType=\"request\" [classroom]=\"classRoom\"></app-mini-lesson-info-card>\r\n              </div>\r\n              <ng-container *ngIf=\"classRoom.lessons!.length > 0\">\r\n                <ng-container *ngFor=\"let lesson of getFirstThreeRecentLessons(); let i = index;\">\r\n                  <div class=\"col-6 sm:col-4 md:col-12 lg:col-6 p-2 flex justify-content-center lg:justify-content-start\">\r\n                    <app-mini-lesson-info-card [lesson]=\"lesson\" [classroom]=\"classRoom\"></app-mini-lesson-info-card>\r\n                  </div>\r\n                </ng-container>\r\n              </ng-container>\r\n            </div>\r\n            <!-- </p-scrollPanel> -->\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"bottom-button\">\r\n      <!-- <button pButton type=\"button\" styleClass=\"\" class=\"p-button-rounded button-rounded action-btn\" [ngClass]=\"color + '-gradient-lesson-bg'\">\r\n        Go to Classroom\r\n      </button> -->\r\n      <g-button class=\"font-base w-10 width-30 py-1 m-auto {{ getStatusColor + '-gradient-lesson-bg' }} \" text=\"Go to Classroom\" (click)=\"goToLessonDetails()\"></g-button>    </div>\r\n  </div>\r\n</div>"], "mappings": "AAEA,SAAoBA,MAAM,QAAQ,qCAAqC;AAIvE,SAASC,OAAO,QAAQ,SAAS;AAEjC,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,YAAY,QAAQ,mCAAmC;;;;;;;;;;;ICQxDC,EAFJ,CAAAC,cAAA,cAA2E,cAC5C,cACU;IAAAD,EAAA,CAAAE,MAAA,wBACnC;IAAAF,EAAA,CAAAG,SAAA,cAAuF;IAG7FH,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;;IAUIJ,EAAA,CAAAK,uBAAA,GAA+B;IAG3BL,EAFJ,CAAAC,cAAA,cAAyC,UACgD,eACzD;IAC1BD,EAAA,CAAAG,SAAA,eAAkC;IACpCH,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAG,SAAA,eAAkC;IACpCH,EAAA,CAAAI,YAAA,EAAO;IAELJ,EADF,CAAAC,cAAA,cAA4B,cACA;IACxBD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,SAAA,UAAI;IAC5BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAIzBF,EAJyB,CAAAI,YAAA,EAAO,EAClB,EACF,EACF,EACJ;;;;;IAdGJ,EAAA,CAAAM,SAAA,GAAoC;IAApCN,EAAA,CAAAO,sBAAA,cAAAC,MAAA,CAAAC,cAAA,MAAoC;;IASnCT,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAU,kBAAA,MAAAF,MAAA,CAAAG,mBAAA,MAAwB;;;;;;IAQpCX,EAAA,CAAAK,uBAAA,GAA8B;IAGtBL,EAFJ,CAAAC,cAAA,cAA0C,cACb,cAC+I;IAAxFD,EAAA,CAAAY,UAAA,mBAAAC,qEAAAC,MAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAR,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASV,MAAA,CAAAW,cAAA,CAAAC,oBAAA,CAAAN,MAAA,CAA2C;IAAA,EAAC;IAEzId,EAFI,CAAAI,YAAA,EAAwK,EACpK,EACF;;;;;IAFGJ,EAAA,CAAAM,SAAA,GAAyE;IAAzEN,EAAA,CAAAqB,UAAA,QAAAb,MAAA,CAAAW,cAAA,CAAAG,qBAAA,CAAAd,MAAA,CAAAe,SAAA,CAAAC,OAAA,CAAAC,SAAA,GAAAzB,EAAA,CAAA0B,aAAA,CAAyE;;;;;IA4B1E1B,EADF,CAAAC,cAAA,aAAmF,cACjC;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAI,YAAA,EAAM;IAG7DJ,EAFJ,CAAAC,cAAA,cAAsD,iBACjB,cACJ;IAAAD,EAAA,CAAAE,MAAA,GAA6E;IAGhHF,EAHgH,CAAAI,YAAA,EAAM,EACzG,EACL,EACH;;;;IAH8BJ,EAAA,CAAAM,SAAA,GAA6E;IAA7EN,EAAA,CAAAU,kBAAA,KAAAF,MAAA,CAAAmB,gBAAA,CAAAC,wBAAA,CAAApB,MAAA,CAAAe,SAAA,CAAAM,QAAA,YAA6E;;;;;IAK9G7B,EADF,CAAAC,cAAA,aAAmF,cACjC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAI,YAAA,EAAM;IAG5DJ,EAFJ,CAAAC,cAAA,cAAsD,iBACjB,cACJ;IAAAD,EAAA,CAAAE,MAAA,GAC+F;IAGlIF,EAHkI,CAAAI,YAAA,EAAM,EAC3H,EACL,EACH;;;;IAJ8BJ,EAAA,CAAAM,SAAA,GAC+F;IAD/FN,EAAA,CAAA8B,iBAAA,CAAAtB,MAAA,CAAAW,cAAA,CAAAY,+BAAA,CAAAvB,MAAA,CAAAmB,gBAAA,CAAAK,uBAAA,CAAAxB,MAAA,CAAAe,SAAA,CAAAM,QAAA,SAC+F;;;;;IAKhI7B,EADF,CAAAC,cAAA,aAAkF,cAChC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAI,YAAA,EAAM;IAGzDJ,EAFJ,CAAAC,cAAA,cAAsD,iBACjB,cACJ;IAAAD,EAAA,CAAAE,MAAA,GAAsD;IAGzFF,EAHyF,CAAAI,YAAA,EAAM,EAClF,EACL,EACH;;;;IAH8BJ,EAAA,CAAAM,SAAA,GAAsD;IAAtDN,EAAA,CAAA8B,iBAAA,CAAAtB,MAAA,CAAAW,cAAA,CAAAc,gBAAA,CAAAzB,MAAA,CAAAe,SAAA,CAAAC,OAAA,EAAsD;;;;;IAKvFxB,EADF,CAAAC,cAAA,aAAkF,cAChC;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAI,YAAA,EAAM;IAG1DJ,EAFJ,CAAAC,cAAA,cAAsD,iBAClB,cACH;IAAAD,EAAA,CAAAE,MAAA,GAAkE;IAIrGF,EAJqG,CAAAI,YAAA,EAAM,EAC9F,EAEL,EACH;;;;IAJ8BJ,EAAA,CAAAM,SAAA,GAAkE;IAAlEN,EAAA,CAAAU,kBAAA,KAAAF,MAAA,CAAAmB,gBAAA,CAAAK,uBAAA,CAAAxB,MAAA,CAAAe,SAAA,CAAAM,QAAA,OAAkE;;;;;IAgB7G7B,EAAA,CAAAC,cAAA,cAAiF;IAC/ED,EAAA,CAAAE,MAAA,GAAgG;IAAAF,EAAA,CAAAI,YAAA,EAAM;;;;IAAtGJ,EAAA,CAAAM,SAAA,EAAgG;IAAhGN,EAAA,CAAAU,kBAAA,MAAAF,MAAA,CAAA0B,iCAAA,CAAAC,IAAA,KAAA3B,MAAA,CAAA4B,WAAA,CAAAC,KAAA,mCAAgG;;;;;IAmBhGrC,EAAA,CAAAK,uBAAA,GAAsC;IAQlCL,EAPJ,CAAAC,cAAA,iBAK+D,cAClB,eACV;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAI,YAAA,EAAO;IAAEJ,EAAA,CAAAG,SAAA,cAAgE;IAGhIH,EAFE,CAAAI,YAAA,EAAM,EAEC;;;;;IAVDJ,EAAA,CAAAM,SAAA,EAA6E;IAKrFN,EALQ,CAAAqB,UAAA,cAAAb,MAAA,CAAAmB,gBAAA,CAAAW,mCAAA,CAAA9B,MAAA,CAAAe,SAAA,EAA6E,YAAAvB,EAAA,CAAAuC,eAAA,IAAAC,GAAA,EAAAhC,MAAA,CAAAiC,QAAA,EAE7C,aAAAjC,MAAA,CAAAiC,QAAA,mCACe,eAAAjC,MAAA,CAAAmB,gBAAA,CAAAe,mBAAA,CAAAlC,MAAA,CAAAe,SAAA,EAEO;;;;;;IApB5DvB,EAHN,CAAAC,cAAA,cAAiE,aACG,aACU,cACd;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAI,YAAA,EAAM;IAC5EJ,EAAA,CAAAC,cAAA,cAA4F;IAC1FD,EAAA,CAAAE,MAAA,GAA0H;IAC9HF,EAD8H,CAAAI,YAAA,EAAM,EAC/H;IAEHJ,EADF,CAAAC,cAAA,aAA0E,cACd;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAI,YAAA,EAAM;IAC7EJ,EAAA,CAAAC,cAAA,eAAiF;IAAAD,EAAA,CAAAE,MAAA,IAAsE;;IACzJF,EADyJ,CAAAI,YAAA,EAAM,EAC1J;IAEHJ,EADF,CAAAC,cAAA,cAA0E,eACtB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAI,YAAA,EAAM;IACvEJ,EAAA,CAAAC,cAAA,eAAiF;IAAAD,EAAA,CAAAE,MAAA,IAAoE;;IAEzJF,EAFyJ,CAAAI,YAAA,EAAM,EACxJ,EACF;IACLJ,EAAA,CAAAC,cAAA,eAAiE;IAC/DD,EAAA,CAAA2C,UAAA,KAAAC,sDAAA,2BAAsC;IAaxC5C,EAAA,CAAAI,YAAA,EAAM;IAKJJ,EADF,CAAAC,cAAA,eAAwB,kBACqJ;IAAnKD,EAAA,CAAAY,UAAA,mBAAAiC,gEAAA;MAAA7C,EAAA,CAAAe,aAAA,CAAA+B,GAAA;MAAA,MAAAtC,MAAA,GAAAR,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASV,MAAA,CAAAW,cAAA,CAAA4B,oBAAA,CAAAvC,MAAA,CAAAe,SAAA,CAA8C;IAAA,EAAC;IAEpEvB,EAF+K,CAAAI,YAAA,EAAS,EAChL,EACF;;;;IAhCEJ,EAAA,CAAAM,SAAA,GAA0H;IAA1HN,EAAA,CAAAU,kBAAA,MAAAF,MAAA,CAAA0B,iCAAA,CAAAC,IAAA,KAAA3B,MAAA,CAAA4B,WAAA,CAAAC,KAAA,kBAAA7B,MAAA,CAAA0B,iCAAA,CAAAC,IAAA,KAA0H;IAI3CnC,EAAA,CAAAM,SAAA,GAAsE;IAAtEN,EAAA,CAAA8B,iBAAA,CAAA9B,EAAA,CAAAgD,WAAA,QAAAxC,MAAA,CAAA0B,iCAAA,CAAAe,WAAA,cAAsE;IAItEjD,EAAA,CAAAM,SAAA,GAAoE;IAApEN,EAAA,CAAA8B,iBAAA,CAAA9B,EAAA,CAAAgD,WAAA,QAAAxC,MAAA,CAAA0B,iCAAA,CAAAgB,SAAA,cAAoE;IAIvIlD,EAAA,CAAAM,SAAA,GAAoB;IAApBN,EAAA,CAAAqB,UAAA,SAAAb,MAAA,CAAA2C,cAAA,CAAoB;;;;;IAmClCnD,EAAA,CAAAK,uBAAA,GAAkF;IAChFL,EAAA,CAAAC,cAAA,cAAwG;IACtGD,EAAA,CAAAG,SAAA,oCAAiG;IACnGH,EAAA,CAAAI,YAAA,EAAM;;;;;;IADuBJ,EAAA,CAAAM,SAAA,GAAiB;IAACN,EAAlB,CAAAqB,UAAA,WAAA+B,SAAA,CAAiB,cAAA5C,MAAA,CAAAe,SAAA,CAAwB;;;;;IAH1EvB,EAAA,CAAAK,uBAAA,GAAoD;IAClDL,EAAA,CAAA2C,UAAA,IAAAU,8DAAA,2BAAkF;;;;;IAAjDrD,EAAA,CAAAM,SAAA,EAAiC;IAAjCN,EAAA,CAAAqB,UAAA,YAAAb,MAAA,CAAA8C,0BAAA,GAAiC;;;ADnKlF,OAAM,MAAOC,sBAAsB;EAcjCC,YACUC,MAAc,EACdC,GAAsB,EACvBvC,cAA8B,EAC9BQ,gBAAkC,EAClCgC,sBAA8C;IAJ7C,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IACJ,KAAAvC,cAAc,GAAdA,cAAc;IACd,KAAAQ,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAgC,sBAAsB,GAAtBA,sBAAsB;IAlBvB,KAAAC,IAAI,GAAG,IAAI/D,OAAO,EAAE;IACnB,KAAAgE,UAAU,GAAkB,IAAI;IAChC,KAAAC,KAAK,GAAc,iBAAiB;IACpC,KAAAX,cAAc,GAAG,KAAK;IACtB,KAAAY,OAAO,GAAG,KAAK;IACf,KAAAC,MAAM,GAAG,KAAK;IACd,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,UAAU,GAAG,EAAE;IACxB;IACS,KAAA3C,SAAS,GAAG,EAAe;IACpC,KAAAa,WAAW,GAAGxC,MAAM;IACpB,KAAAuE,QAAQ,GAAG,KAAK;IAChB,KAAA1B,QAAQ,GAAG,KAAK;EAOZ;EAEJ2B,QAAQA,CAAA;IACN,IAAI,IAAI,CAACC,OAAO,EAAE;MAChB,IAAI,CAAClB,cAAc,GAAG,IAAI;MAE1B,IAAI,CAACS,IAAI,CAACU,GAAG,CAAC,IAAI,CAACX,sBAAsB,CAACY,qBAAqB,CAAC,CAAC,IAAI,CAAChD,SAAS,CAACiD,EAAE,CAAC,CAACC,SAAS,CAAEC,GAAG,IAAI;QACpGC,OAAO,CAACC,GAAG,CAACF,GAAG,CAAC;QAChB,IAAIA,GAAG,EAAE;UACP,IAAI,CAACjC,QAAQ,GAAG,CAAC,IAAI,CAACtB,cAAc,CAAC0D,aAAa,CAACH,GAAG,CAAC;UACvDC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACnC,QAAQ,CAAC;UAC1B,IAAI,CAACiB,GAAG,CAACoB,aAAa,EAAE;QAC1B;MACF,CAAC,CAAC,CAAC;IACL;EACF;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACnB,IAAI,CAACoB,IAAI,GAAG,IAAI,CAAC7D,cAAc,CAAC8D,UAAU,CAACC,IAAI,EACnD,CAACT,SAAS,CAAEC,GAAG,IAAI;MAClB,IAAI,CAACP,QAAQ,GAAG,CAACO,GAAG,CAACS,MAAO;IAC9B,CAAC,CAAC;EAEJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACxB,IAAI,CAACyB,WAAW,EAAE;EACzB;EAEA;EACAC,iBAAiBA,CAAA;IACf,MAAMC,gBAAgB,GAAqB;MACzCC,KAAK,EAAE;QAAEjE,SAAS,EAAE,IAAI,CAACA;MAAS;KACnC;IACD,IAAI,IAAI,CAAC8C,OAAO,EAAE;MAChB;MACA,IAAI,CAACZ,MAAM,CAACgC,QAAQ,CAAC,CAAC,8BAA8B,EAAE,IAAI,CAAClE,SAAS,CAACiD,EAAE,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE;QAC3FgB,KAAK,EAAE;UAAEjE,SAAS,EAAE,IAAI,CAACA;QAAS;OACnC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACkC,MAAM,CAACgC,QAAQ,CAAC,CAAC,+BAA+B,EAAE,IAAI,CAAClE,SAAS,CAACiD,EAAE,EAAE,SAAS,CAAC,EAAEe,gBAAgB,CAAC;IACzG;EACF;EAEA,IAAIrD,iCAAiCA,CAAA;IACnC,IAAIwD,cAAc,GAAG,IAAI;IACzB,IAAIC,WAAW,GAAG,IAAI;IAEtB,KAAK,MAAMC,GAAG,IAAI,IAAI,CAACrE,SAAS,CAACM,QAAS,EAAE;MAC1C,IAAI+D,GAAG,CAACJ,KAAK,CAACK,WAAW,EAAE,KAAK9F,YAAY,CAAC+F,OAAO,CAACD,WAAW,EAAE,IAAID,GAAG,CAACG,SAAS,GAAG,CAAC,EAAE;QACvFL,cAAc,GAAGE,GAAG;QACpB;MACF,CAAC,MAAM;QACLD,WAAW,GAAGC,GAAG;MACnB;IACF;IAEA,OAAOF,cAAc,IAAIC,WAAW;EACtC;EAEA,IAAItB,OAAOA,CAAA;IACT,OAAO,IAAI,CAAC9C,SAAS,CAACY,IAAI,KAAK,OAAO;EACxC;EAEA,IAAI1B,cAAcA,CAAA;IAChB,IAAI,IAAI,CAACkB,gBAAgB,CAACW,mCAAmC,CAAC,IAAI,CAACf,SAAS,CAAC,EAAE;MAC7E,OAAO,iBAAiB;IAC1B;IACA;IACA;IACA;IACA,QAAQ,IAAI,CAACA,SAAS,CAACyE,MAAM,CAACH,WAAW,EAAE;MACzC,KAAKjG,MAAM,CAACqG,OAAO,CAACJ,WAAW,EAAE;QAC/B,OAAO,SAAS;MAClB,KAAKjG,MAAM,CAACsG,QAAQ,CAACL,WAAW,EAAE;QAChC,OAAO,eAAe;MACxB,KAAKjG,MAAM,CAACuG,QAAQ,CAACN,WAAW,EAAE;QAChC,OAAO,SAAS;MAClB,KAAKjG,MAAM,CAACwG,OAAO,CAACP,WAAW,EAAE;QAC/B,OAAO,cAAc;MACvB,KAAKjG,MAAM,CAACyG,OAAO,CAACR,WAAW,EAAE;QAC/B,OAAO,cAAc;MACvB,KAAKjG,MAAM,CAACyC,KAAK,CAACwD,WAAW,EAAE;QAC7B,OAAO,YAAY;MACrB,KAAKjG,MAAM,CAAC0G,SAAS,CAACT,WAAW,EAAE;QACjC,OAAO,WAAW;MACpB,KAAKjG,MAAM,CAAC2G,eAAe,CAACV,WAAW,EAAE;QACvC,OAAO,iBAAiB;MAC1B,KAAKjG,MAAM,CAAC4G,cAAc,CAACX,WAAW,EAAE;QACtC,OAAO,OAAO;MAChB,KAAKjG,MAAM,CAAC6G,aAAa,CAACZ,WAAW,EAAE;QACrC,OAAO,OAAO;MAChB,KAAKjG,MAAM,CAAC8G,aAAa,CAACb,WAAW,EAAE;QACrC,OAAO,OAAO;MAChB,KAAKjG,MAAM,CAAC+G,SAAS,CAACd,WAAW,EAAE;QACjC,OAAO,QAAQ;MACjB,KAAKjG,MAAM,CAACgH,eAAe,CAACf,WAAW,EAAE;QACvC,OAAO,WAAW;MACpB;QACE,OAAO,SAAS;IACpB;EACF;EAEAgB,aAAaA,CAAA;IACX,IAAI,IAAI,CAAClF,gBAAgB,CAACmF,wCAAwC,CAAC,IAAI,CAACvF,SAAS,CAAC,EAAE;MAClF,OAAO,gBAAgB;IACzB;IAEA,QAAQ,IAAI,CAACA,SAAS,CAACyE,MAAM,CAACH,WAAW,EAAE;MACzC,KAAKjG,MAAM,CAACqG,OAAO,CAACJ,WAAW,EAAE;QAC/B,OAAO,SAAS;MAClB,KAAKjG,MAAM,CAACsG,QAAQ,CAACL,WAAW,EAAE;QAChC,OAAO,eAAe;MACxB,KAAKjG,MAAM,CAACuG,QAAQ,CAACN,WAAW,EAAE;QAChC,OAAO,SAAS;MAClB,KAAKjG,MAAM,CAACwG,OAAO,CAACP,WAAW,EAAE;QAC/B,OAAO,cAAc;MACvB,KAAKjG,MAAM,CAACyG,OAAO,CAACR,WAAW,EAAE;QAC/B,OAAO,cAAc;MACvB,KAAKjG,MAAM,CAACyC,KAAK,CAACwD,WAAW,EAAE;QAC7B,OAAO,YAAY;MACrB,KAAKjG,MAAM,CAAC0G,SAAS,CAACT,WAAW,EAAE;QACjC,OAAO,WAAW;MACpB,KAAKjG,MAAM,CAAC+G,SAAS,CAACd,WAAW,EAAE;QACjC,OAAO,QAAQ;MACjB;QACE,OAAO,cAAc;IACzB;EACF;EAEAvC,0BAA0BA,CAAA;IACxB,MAAMyD,GAAG,GAAGjH,MAAM,EAAE;IACpB,OAAO,IAAI,CAACyB,SAAS,CAACyF,OAAQ,CAACC,IAAI,CAAC,IAAI,CAACC,UAAU,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAClE;EAEA,IAAIxG,mBAAmBA,CAAA;IACrB,IAAIkD,UAAU,GACZ,CAAC,IAAI,CAAClC,gBAAgB,CAACC,wBAAwB,CAAC,IAAI,CAACL,SAAS,CAACM,QAAS,CAAC,GAAG,IAAI,CAACF,gBAAgB,CAACK,uBAAuB,CAAC,IAAI,CAACT,SAAS,CAACM,QAAS,CAAC,IACnJ,IAAI,CAACF,gBAAgB,CAACC,wBAAwB,CAAC,IAAI,CAACL,SAAS,CAACM,QAAS,CAAC,GACtE,GAAG;IAEP,IAAIgC,UAAU,GAAG,GAAG,EAAE;MACpBA,UAAU,GAAG,GAAG;IAClB,CAAC,MAAM,IAAIA,UAAU,GAAG,CAAC,EAAE;MACzBA,UAAU,GAAG,CAAC;IAChB;IAEA;IACAA,UAAU,GAAG,CAACA,UAAU,CAACuD,OAAO,CAAC,CAAC,CAAC;IAEnC;IACAvD,UAAU,GAAGwD,IAAI,CAACC,IAAI,CAACzD,UAAU,CAAC;IAElC;IACAA,UAAU,GAAGwD,IAAI,CAACE,KAAK,CAAC1D,UAAU,GAAG,EAAE,CAAC,GAAG,EAAE;IAE7C,OAAOA,UAAU;EACnB;EAEA2D,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAAC7F,gBAAgB,CAACK,uBAAuB,CAAC,IAAI,CAACT,SAAS,CAACM,QAAS,CAAC,GAAG,CAAC;EACpF;EAEQqF,UAAUA,CAACO,CAAS,EAAEC,CAAS;IACrC,MAAMC,KAAK,GAAG7H,MAAM,CAAC4H,CAAC,CAACE,YAAY,CAAC;IACpC,MAAMC,KAAK,GAAG/H,MAAM,CAAC2H,CAAC,CAACG,YAAY,CAAC;IACpC,OAAOD,KAAK,CAACG,IAAI,CAACD,KAAK,CAAC;EAC1B;EAAC,QAAAE,CAAA,G;qBA3LUxE,sBAAsB,EAAAvD,EAAA,CAAAgI,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAlI,EAAA,CAAAgI,iBAAA,CAAAhI,EAAA,CAAAmI,iBAAA,GAAAnI,EAAA,CAAAgI,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAArI,EAAA,CAAAgI,iBAAA,CAAAM,EAAA,CAAAC,gBAAA,GAAAvI,EAAA,CAAAgI,iBAAA,CAAAQ,EAAA,CAAAC,sBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAtBnF,sBAAsB;IAAAoF,SAAA;IAAAC,MAAA;MAAA/E,UAAA;MAAAC,KAAA;MAAAX,cAAA;MAAAY,OAAA;MAAAC,MAAA;MAAAC,SAAA;MAAAC,UAAA;MAAA3C,SAAA;IAAA;IAAAsH,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCf/BlJ,EAFJ,CAAAC,cAAA,aAAkH,aACX,aACvB;QAC1ED,EAAA,CAAAG,SAAA,aAAsF;QACxFH,EAAA,CAAAI,YAAA,EAAM;QAGFJ,EAFJ,CAAAC,cAAA,UAAK,aACuB,YACM;QAAAD,EAAA,CAAAE,MAAA,GAAkJ;QAClLF,EADkL,CAAAI,YAAA,EAAK,EACjL;QAGFJ,EAFJ,CAAAC,cAAA,aAAoC,aACkF,SAC/G;QAAAD,EAAA,CAAAE,MAAA,gBAAQ;QAAAF,EAAA,CAAAC,cAAA,SAAG;QAAAD,EAAA,CAAAE,MAAA,IAAsD;QAG1EF,EAH0E,CAAAI,YAAA,EAAI,EAAI,EACxE,EACF,EACF;QACNJ,EAAA,CAAA2C,UAAA,KAAAyG,sCAAA,iBAA2E;QAO7EpJ,EAAA,CAAAI,YAAA,EAAM;QAMEJ,EAJR,CAAAC,cAAA,cAA4B,eACW,eAC4B,eACZ,eACR;QAAAD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAEzDJ,EADF,CAAAC,cAAA,eAA8C,eACnB;QAoB3BD,EAnBI,CAAA2C,UAAA,KAAA0G,+CAAA,4BAA+B,KAAAC,+CAAA,2BAmBL;QAO5BtJ,EAAA,CAAAI,YAAA,EAAM;QAMIJ,EALV,CAAAC,cAAA,eAAwB,eACJ,eACiF,cACtD,cAC2B,eAChB;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAGxDJ,EAFJ,CAAAC,cAAA,eAAsD,kBACjB,eACO;QAAAD,EAAA,CAAAE,MAAA,IAA6C;QAG3FF,EAH2F,CAAAI,YAAA,EAAM,EACpF,EACL,EACH;QAEHJ,EADF,CAAAC,cAAA,cAAkE,eAChB;QAAAD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAI,YAAA,EAAM;QAGvDJ,EAFJ,CAAAC,cAAA,eAAsD,kBACjB,eACO;QACtCD,EAAA,CAAAE,MAAA,IAC6D;QAGrEF,EAHqE,CAAAI,YAAA,EAAM,EAC9D,EACL,EACH;QA0BLJ,EAzBA,CAAA2C,UAAA,KAAA4G,qCAAA,iBAAmF,KAAAC,qCAAA,iBAQA,KAAAC,qCAAA,iBASD,KAAAC,qCAAA,iBAQA;QAehG1J,EANY,CAAAI,YAAA,EAAK,EACD,EACF,EACF,EACF,EACF,EACF;QAIFJ,EAFJ,CAAAC,cAAA,eAAmC,eACI,eACV;QACvBD,EAAA,CAAA2C,UAAA,KAAAgH,sCAAA,kBAAiF;QAEnF3J,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAA2C,UAAA,KAAAiH,sCAAA,oBAAiE;QAuCrE5J,EADE,CAAAI,YAAA,EAAM,EACF;QAOEJ,EALR,CAAAC,cAAA,eAA+D,eACwB,eACN,eAEzC,eAG/B;QACDD,EAAA,CAAAG,SAAA,qCAAqM;QACrMH,EAAA,CAAAI,YAAA,EAAM;QACNJ,EAAA,CAAA2C,UAAA,KAAAkH,+CAAA,2BAAoD;QAY9D7J,EALQ,CAAAI,YAAA,EAAM,EAEF,EACF,EACF,EACF;QAMJJ,EAJF,CAAAC,cAAA,eAA2B,oBAIgI;QAA9BD,EAAA,CAAAY,UAAA,mBAAAkJ,2DAAA;UAAA,OAASX,GAAA,CAAA7D,iBAAA,EAAmB;QAAA,EAAC;QAE9JtF,EAF+J,CAAAI,YAAA,EAAW,EAAU,EAC5K,EACF;;;QAvMsCJ,EAAA,CAAAqB,UAAA,YAAA8H,GAAA,CAAA1I,cAAA,iCAAA0I,GAAA,CAAAjF,UAAA,CAAqE;QAC7DlE,EAAA,CAAAM,SAAA,EAAkD;QAAlDN,EAAA,CAAAqB,UAAA,YAAA8H,GAAA,CAAA1I,cAAA,yBAAkD;QACzET,EAAA,CAAAM,SAAA,EAAkD;QAAlDN,EAAA,CAAAqB,UAAA,YAAA8H,GAAA,CAAA1I,cAAA,yBAAkD;QACpET,EAAA,CAAAM,SAAA,EAA2D;QAA3DN,EAAA,CAAAqB,UAAA,mCAAA8H,GAAA,CAAAtC,aAAA,aAAA7G,EAAA,CAAA0B,aAAA,CAA2D;QAIhC1B,EAAA,CAAAM,SAAA,GAAkJ;QAAlJN,EAAA,CAAA+J,kBAAA,KAAAZ,GAAA,CAAA9E,OAAA,uBAAA8E,GAAA,CAAAhI,cAAA,CAAA6I,UAAA,CAAAb,GAAA,CAAA5H,SAAA,CAAA0I,QAAA,QAAAd,GAAA,CAAA9E,OAAA,uBAAA8E,GAAA,CAAA9E,OAAA,2BAAkJ;QAG7HrE,EAAA,CAAAM,SAAA,GAAgE;QAAhEN,EAAA,CAAAqB,UAAA,aAAA8H,GAAA,CAAAhI,cAAA,CAAA+I,iBAAA,CAAAf,GAAA,CAAA5H,SAAA,CAAAC,OAAA,EAAgE;QACnGxB,EAAA,CAAAM,SAAA,GAAsD;QAAtDN,EAAA,CAAA8B,iBAAA,CAAAqH,GAAA,CAAAhI,cAAA,CAAAc,gBAAA,CAAAkH,GAAA,CAAA5H,SAAA,CAAAC,OAAA,EAAsD;QAIjDxB,EAAA,CAAAM,SAAA,EAAgD;QAAhDN,EAAA,CAAAqB,UAAA,SAAA8H,GAAA,CAAA5H,SAAA,CAAAyE,MAAA,KAAAmD,GAAA,CAAA/G,WAAA,CAAAuE,SAAA,CAAgD;QAgBhD3G,EAAA,CAAAM,SAAA,GAAc;QAAdN,EAAA,CAAAqB,UAAA,UAAA8H,GAAA,CAAA9E,OAAA,CAAc;QAmBlBrE,EAAA,CAAAM,SAAA,EAAa;QAAbN,EAAA,CAAAqB,UAAA,SAAA8H,GAAA,CAAA9E,OAAA,CAAa;QAgB4BrE,EAAA,CAAAM,SAAA,IAA6C;QAA7CN,EAAA,CAAAmK,kBAAA,KAAAhB,GAAA,CAAA9E,OAAA,kBAAA8E,GAAA,CAAA5H,SAAA,CAAAyE,MAAA,KAA6C;QASnFhG,EAAA,CAAAM,SAAA,GAC6D;QAD7DN,EAAA,CAAAmK,kBAAA,MAAAhB,GAAA,CAAA5H,SAAA,CAAA0I,QAAA,GAAAd,GAAA,CAAAhI,cAAA,CAAA6I,UAAA,CAAAb,GAAA,CAAA5H,SAAA,CAAA0I,QAAA,aAAAd,GAAA,CAAA5H,SAAA,CAAA6I,WAAA,GAAAjB,GAAA,CAAA5H,SAAA,CAAA6I,WAAA,iBAC6D;QAIhEpK,EAAA,CAAAM,SAAA,EAAc;QAAdN,EAAA,CAAAqB,UAAA,UAAA8H,GAAA,CAAA9E,OAAA,CAAc;QAQdrE,EAAA,CAAAM,SAAA,EAAc;QAAdN,EAAA,CAAAqB,UAAA,UAAA8H,GAAA,CAAA9E,OAAA,CAAc;QASdrE,EAAA,CAAAM,SAAA,EAAa;QAAbN,EAAA,CAAAqB,UAAA,SAAA8H,GAAA,CAAA9E,OAAA,CAAa;QAQbrE,EAAA,CAAAM,SAAA,EAAa;QAAbN,EAAA,CAAAqB,UAAA,SAAA8H,GAAA,CAAA9E,OAAA,CAAa;QAoBcrE,EAAA,CAAAM,SAAA,GAAuC;QAAvCN,EAAA,CAAAqB,UAAA,SAAA8H,GAAA,CAAAjH,iCAAA,CAAuC;QAGzDlC,EAAA,CAAAM,SAAA,EAAuC;QAAvCN,EAAA,CAAAqB,UAAA,SAAA8H,GAAA,CAAAjH,iCAAA,CAAuC;QA+C3DlC,EAAA,CAAAM,SAAA,GAAmH;QAAnHN,EAAA,CAAAqB,UAAA,cAAA8H,GAAA,CAAAxH,gBAAA,CAAA0I,4BAAA,CAAAlB,GAAA,CAAA5H,SAAA,gDAAmH;QAExFvB,EAAA,CAAAM,SAAA,EAAkG;QAAoBN,EAAtH,CAAAqB,UAAA,YAAArB,EAAA,CAAAuC,eAAA,KAAAC,GAAA,EAAA2G,GAAA,CAAAxH,gBAAA,CAAAK,uBAAA,CAAAmH,GAAA,CAAA5H,SAAA,CAAAM,QAAA,QAAkG,cAAAsH,GAAA,CAAA5H,SAAA,CAA2C;QAEzJvB,EAAA,CAAAM,SAAA,EAAmC;QAAnCN,EAAA,CAAAqB,UAAA,SAAA8H,GAAA,CAAA5H,SAAA,CAAAyF,OAAA,CAAAsD,MAAA,KAAmC;QAkBhDtK,EAAA,CAAAM,SAAA,GAAyF;QAAzFN,EAAA,CAAAO,sBAAA,yCAAA4I,GAAA,CAAA1I,cAAA,8BAAyF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}