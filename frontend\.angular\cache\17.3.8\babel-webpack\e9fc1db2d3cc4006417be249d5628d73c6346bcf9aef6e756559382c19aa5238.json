{"ast": null, "code": "export var ClassroomType;\n(function (ClassroomType) {\n  ClassroomType[\"PAID\"] = \"Paid\";\n  ClassroomType[\"TRIAL\"] = \"Trial\";\n})(ClassroomType || (ClassroomType = {}));\nexport var Level;\n(function (Level) {\n  Level[\"A1\"] = \"A1\";\n  Level[\"A2\"] = \"A2\";\n  Level[\"B1\"] = \"B1\";\n  Level[\"B2\"] = \"B2\";\n  Level[\"C1\"] = \"C1\";\n  Level[\"C2\"] = \"C2\";\n  Level[\"NT21\"] = \"NT2.1\";\n  Level[\"NT22\"] = \"NT2.2\";\n  Level[\"N5\"] = \"N5\";\n  Level[\"N4\"] = \"N4\";\n  Level[\"N3\"] = \"N3\";\n  Level[\"N2\"] = \"N2\";\n  Level[\"N1\"] = \"N1\";\n  Level[\"HSK1\"] = \"HSK1\";\n  Level[\"HSK2\"] = \"HSK2\";\n  Level[\"HSK3\"] = \"HSK3\";\n  Level[\"HSK4\"] = \"HSK4\";\n  Level[\"HSK5\"] = \"HSK5\";\n  Level[\"HSK6\"] = \"HSK6\";\n  Level[\"TOPIK1\"] = \"TOPIK1\";\n  Level[\"TOPIK2\"] = \"TOPIK2\";\n  Level[\"TOPIK3\"] = \"TOPIK3\";\n  Level[\"TOPIK4\"] = \"TOPIK4\";\n  Level[\"TOPIK5\"] = \"TOPIK5\";\n  Level[\"TOPIK6\"] = \"TOPIK6\";\n  Level[\"BS\"] = \"BS\";\n  Level[\"TBD\"] = \"TBD\";\n  Level[\"ALL\"] = \"All\";\n})(Level || (Level = {}));\nexport var Status;\n(function (Status) {\n  Status[\"EXPIRED\"] = \"Expired\";\n  Status[\"INACTIVE\"] = \"Inactive\";\n  Status[\"ON_GOING\"] = \"Ongoing\";\n  Status[\"ON_HOLD\"] = \"On Hold\";\n  Status[\"PENDING\"] = \"Pending\";\n  Status[\"TRIAL\"] = \"Trial\";\n  Status[\"COMPLETED\"] = \"Completed\";\n  Status[\"COMPLETED_TRIAL\"] = \"Trial Completed\";\n  Status[\"PENDING_TRIAL\"] = \"Trial Pending\";\n  Status[\"ARRANGED_TRIAL\"] = \"Trial Arranged\";\n  Status[\"DISMISSED_TRIAL\"] = \"Trial Dismissed\";\n  Status[\"ONGOING_TRIAL\"] = \"Trial Ongoing\";\n  Status[\"ARRANGED_NO_SHOW\"] = \"Trial No Show\";\n  Status[\"DISMISSED\"] = \"Dismissed\";\n  Status[\"ALL\"] = \"All\";\n})(Status || (Status = {}));\nexport var RateValue;\n(function (RateValue) {\n  RateValue[\"VERY_BAD\"] = \"Very Bad\";\n  RateValue[\"BAD\"] = \"Bad\";\n  RateValue[\"NEUTRAL\"] = \"Neutral\";\n  RateValue[\"GOOD\"] = \"Good\";\n  RateValue[\"VERY_GOOD\"] = \"Very Good\";\n})(RateValue || (RateValue = {}));", "map": {"version": 3, "names": ["ClassroomType", "Level", "Status", "RateValue"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\models\\classroom.model.ts"], "sourcesContent": ["import { Lesson } from \"./lesson.model\"\r\nimport { Package } from \"./package.model\"\r\nimport { User } from \"./user.model\"\r\n\r\nexport interface Classroom {\r\n    id: string,\r\n    title: string,\r\n    status: Status,\r\n    totalHours: number,\r\n    hoursLeft: number,\r\n    activeLevel: Level.A1 | Level.A2 | Level.B1 | Level.B2 | Level.C1 | Level.C2 | Level.NT21 | Level.NT22 | Level.TBD | Level.BS,\r\n    users: User[],\r\n    classroomStudents: User[],\r\n    type: ClassroomType.PAID | ClassroomType.TRIAL,\r\n    language?: string\r\n    teacher?: User,\r\n    activePackage?: Package,\r\n    lessons?: Lesson[],\r\n    packages?: Package[]\r\n}\r\n\r\nexport enum ClassroomType {\r\n    PAID = 'Paid',\r\n    TRIAL = 'Trial'\r\n}\r\n\r\nexport interface ClassroomStudentsResponse {\r\n    errorMessage: string,\r\n    usersDTOResult: User[]\r\n}\r\n\r\nexport interface StatusClassroomsStats {\r\n    total: number,\r\n    inActive: number,\r\n    onHold: number,\r\n    ongoing: number,\r\n    pending: number,\r\n    expired: number,\r\n}\r\n\r\nexport interface ClassroomSizesStats {\r\n    total: number,\r\n    group_of_one: number,\r\n    group_of_two: number,\r\n    group_of_three: number,\r\n    group_of_four: number,\r\n}\r\n\r\nexport interface LevelClassroomsStats {\r\n    studentCount: number,\r\n    hoursCount: number,\r\n    level: Level,\r\n    errorMessage: string\r\n}\r\n\r\nexport interface CertificateClassroomsStats {\r\n\r\n}\r\n\r\nexport interface PackageClassroomsStats {\r\n    packageHours: number,\r\n    packageCount: number,\r\n    errorMessage: string\r\n}\r\n\r\nexport enum Level {\r\n    A1 = \"A1\",\r\n    A2 = \"A2\",\r\n    B1 = \"B1\",\r\n    B2 = \"B2\",\r\n    C1 = \"C1\",\r\n    C2 = \"C2\",\r\n    NT21 = \"NT2.1\", // DUTCH\r\n    NT22 = \"NT2.2\", // DUTCH\r\n    N5 = \"N5\", // JAPANESE\r\n    N4 = \"N4\", // JAPANESE\r\n    N3 = \"N3\", // JAPANESE\r\n    N2 = \"N2\", // JAPANESE\r\n    N1 = \"N1\", // JAPANESE\r\n    HSK1 = \"HSK1\", // CHINESE\r\n    HSK2 = \"HSK2\", // CHINESE\r\n    HSK3 = \"HSK3\", // CHINESE\r\n    HSK4 = \"HSK4\", // CHINESE\r\n    HSK5 = \"HSK5\", // CHINESE\r\n    HSK6 = \"HSK6\", // CHINESE\r\n    TOPIK1 = \"TOPIK1\", // KOREAN\r\n    TOPIK2 = \"TOPIK2\", // KOREAN\r\n    TOPIK3 = \"TOPIK3\", // KOREAN\r\n    TOPIK4 = \"TOPIK4\", // KOREAN\r\n    TOPIK5 = \"TOPIK5\", // KOREAN\r\n    TOPIK6 = \"TOPIK6\", // KOREAN\r\n    BS = \"BS\", // Business\r\n    TBD = \"TBD\", // To Be Determined\r\n    ALL = \"All\"\r\n}\r\n\r\n\r\n\r\nexport enum Status {\r\n    EXPIRED = \"Expired\",\r\n    INACTIVE = \"Inactive\",\r\n    ON_GOING = \"Ongoing\",\r\n    ON_HOLD = \"On Hold\",\r\n    PENDING = \"Pending\",\r\n    TRIAL = \"Trial\",\r\n    COMPLETED = \"Completed\",\r\n    COMPLETED_TRIAL = \"Trial Completed\",\r\n    PENDING_TRIAL = \"Trial Pending\",\r\n    ARRANGED_TRIAL = \"Trial Arranged\",\r\n    DISMISSED_TRIAL = \"Trial Dismissed\",\r\n    ONGOING_TRIAL = \"Trial Ongoing\",\r\n    ARRANGED_NO_SHOW = \"Trial No Show\",\r\n    DISMISSED = \"Dismissed\",\r\n    ALL = \"All\"\r\n}\r\n\r\nexport interface ClassroomsLevelFilter {\r\n    level: string,\r\n    domLevelID: string\r\n}\r\n\r\nexport interface ClassroomsStatusFilter {\r\n    status: string,\r\n    domStatusID: string\r\n}\r\n\r\nexport interface StudentHoursStatsOnEachLevelStats {\r\n    a1: {\r\n        students: number,\r\n        hours: number\r\n    },\r\n    a2: {\r\n        students: number,\r\n        hours: number\r\n    },\r\n    b1: {\r\n        students: number,\r\n        hours: number\r\n    },\r\n    b2: {\r\n        students: number,\r\n        hours: number\r\n    },\r\n    c1: {\r\n        students: number,\r\n        hours: number\r\n    },\r\n    c2: {\r\n        students: number,\r\n        hours: number\r\n    }\r\n}\r\n\r\nexport interface ClassroomTeacherRating {\r\n    classroomId: string,\r\n    teacherId: string,\r\n    rateText: string,\r\n    level: Level,\r\n    createdAt: Date\r\n    overall?: RateValue, // TODO THIS IS NOT REQUIRED\r\n    organized: RateValue | null,\r\n    discipline: RateValue | null,\r\n    productive: RateValue | null,\r\n    assignments: RateValue | null,\r\n    learnSubject: RateValue | null,\r\n    explanation: RateValue | null,\r\n    performanceEvaluation: RateValue | null,\r\n    variety: RateValue | null,\r\n    area: RateValue | null,\r\n    challenges: RateValue | null,\r\n    free: RateValue | null,\r\n    encouragement: RateValue | null,\r\n    enthusiastic: RateValue | null,\r\n}\r\n\r\nexport interface TrialTeacherRating {\r\n    classroomId: string | null,\r\n    teacherId: string | null,\r\n    level: Level | RateValue,\r\n    friendly: RateValue | null,\r\n    polite: RateValue | null,\r\n    focused: RateValue | null,\r\n    explain: RateValue | null,\r\n    preferences: RateValue | null,\r\n    interest: RateValue | null,\r\n    efficiently: RateValue | null,\r\n    evaluate: RateValue | null,\r\n    schedule: RateValue | null,\r\n    [key: string]: RateValue | string | null,\r\n    \r\n}\r\n\r\nexport interface RequestNewTeacher {\r\n    studentId: string,\r\n    teacherId: string,\r\n    reason: string\r\n}\r\n\r\nexport enum RateValue {\r\n    VERY_BAD = \"Very Bad\",\r\n    BAD = \"Bad\",\r\n    NEUTRAL = \"Neutral\",\r\n    GOOD = \"Good\",\r\n    VERY_GOOD = \"Very Good\"\r\n}"], "mappings": "AAqBA,WAAYA,aAGX;AAHD,WAAYA,aAAa;EACrBA,aAAA,iBAAa;EACbA,aAAA,mBAAe;AACnB,CAAC,EAHWA,aAAa,KAAbA,aAAa;AA4CzB,WAAYC,KA6BX;AA7BD,WAAYA,KAAK;EACbA,KAAA,aAAS;EACTA,KAAA,aAAS;EACTA,KAAA,aAAS;EACTA,KAAA,aAAS;EACTA,KAAA,aAAS;EACTA,KAAA,aAAS;EACTA,KAAA,kBAAc;EACdA,KAAA,kBAAc;EACdA,KAAA,aAAS;EACTA,KAAA,aAAS;EACTA,KAAA,aAAS;EACTA,KAAA,aAAS;EACTA,KAAA,aAAS;EACTA,KAAA,iBAAa;EACbA,KAAA,iBAAa;EACbA,KAAA,iBAAa;EACbA,KAAA,iBAAa;EACbA,KAAA,iBAAa;EACbA,KAAA,iBAAa;EACbA,KAAA,qBAAiB;EACjBA,KAAA,qBAAiB;EACjBA,KAAA,qBAAiB;EACjBA,KAAA,qBAAiB;EACjBA,KAAA,qBAAiB;EACjBA,KAAA,qBAAiB;EACjBA,KAAA,aAAS;EACTA,KAAA,eAAW;EACXA,KAAA,eAAW;AACf,CAAC,EA7BWA,KAAK,KAALA,KAAK;AAiCjB,WAAYC,MAgBX;AAhBD,WAAYA,MAAM;EACdA,MAAA,uBAAmB;EACnBA,MAAA,yBAAqB;EACrBA,MAAA,wBAAoB;EACpBA,MAAA,uBAAmB;EACnBA,MAAA,uBAAmB;EACnBA,MAAA,mBAAe;EACfA,MAAA,2BAAuB;EACvBA,MAAA,uCAAmC;EACnCA,MAAA,mCAA+B;EAC/BA,MAAA,qCAAiC;EACjCA,MAAA,uCAAmC;EACnCA,MAAA,mCAA+B;EAC/BA,MAAA,sCAAkC;EAClCA,MAAA,2BAAuB;EACvBA,MAAA,eAAW;AACf,CAAC,EAhBWA,MAAM,KAANA,MAAM;AAoGlB,WAAYC,SAMX;AAND,WAAYA,SAAS;EACjBA,SAAA,yBAAqB;EACrBA,SAAA,eAAW;EACXA,SAAA,uBAAmB;EACnBA,SAAA,iBAAa;EACbA,SAAA,2BAAuB;AAC3B,CAAC,EANWA,SAAS,KAATA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}