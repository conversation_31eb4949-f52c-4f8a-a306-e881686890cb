{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as moment from 'moment';\nimport { take } from 'rxjs/operators';\nimport { Level } from 'src/app/core/models/classroom.model';\nimport { UserRole } from 'src/app/core/models/user.model';\nimport { SubSink } from 'subsink';\nimport * as data from '../../../../../core/models/data';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/core/services/user.service\";\nimport * as i3 from \"src/app/core/services/package.service\";\nimport * as i4 from \"src/app/core/services/lesson.service\";\nimport * as i5 from \"src/app/core/services/rating-and-report.service\";\nimport * as i6 from \"src/app/core/services/general.service\";\nimport * as i7 from \"src/app/core/services/auth.service\";\nimport * as i8 from \"src/app/core/services/calendar.service\";\nimport * as i9 from \"src/app/core/services/classroom.service\";\nimport * as i10 from \"src/app/core/services/toast.service\";\nimport * as i11 from \"@angular/common\";\nimport * as i12 from \"primeng/api\";\nimport * as i13 from \"primeng/carousel\";\nimport * as i14 from \"primeng/scrollpanel\";\nimport * as i15 from \"../../../teacher-billing/teacher-billing.component\";\nimport * as i16 from \"../../../packages/packages-progress/packages-progress.component\";\nimport * as i17 from \"../class-reports/class-reports.component\";\nimport * as i18 from \"../class-ratings/class-ratings.component\";\nimport * as i19 from \"./teacher-rates-list/teacher-rates-list.component\";\nconst _c0 = [\"ratingDetails\"];\nconst _c1 = [\"carousel\"];\nconst _c2 = [\"tabView\"];\nconst _c3 = [\"scrollPanel\"];\nconst _c4 = a0 => ({\n  classroom: a0\n});\nconst _c5 = (a0, a1) => ({\n  classroom: a0,\n  student: a1\n});\nconst _c6 = () => ({\n  width: \"100%\",\n  height: \"320px\"\n});\nconst _c7 = a0 => [\"/dashboard\", \"classrooms\", \"lessons\", a0, \"info\", \"teacher\"];\nconst _c8 = a0 => ({\n  \"disabled-link\": a0\n});\nconst _c9 = a0 => [a0];\nfunction ClassInfoComponent_div_0_div_1_ng_container_1_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ClassInfoComponent_div_0_div_1_ng_container_1_ng_container_6_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ClassInfoComponent_div_0_div_1_ng_container_1_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p-tabPanel\", 23);\n    i0.ɵɵtemplate(2, ClassInfoComponent_div_0_div_1_ng_container_1_ng_container_6_ng_container_2_Template, 1, 0, \"ng-container\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const selectedStudent_r4 = ctx.$implicit;\n    i0.ɵɵnextContext(3);\n    const classInfoStudentTemplate_r5 = i0.ɵɵreference(9);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"header\", selectedStudent_r4.firstName === \"Not Given\" ? \"Student\" : selectedStudent_r4.firstName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", classInfoStudentTemplate_r5)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(3, _c5, ctx_r2.classroom, selectedStudent_r4));\n  }\n}\nfunction ClassInfoComponent_div_0_div_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p-tabView\", 18, 3);\n    i0.ɵɵlistener(\"activeIndexChange\", function ClassInfoComponent_div_0_div_1_ng_container_1_Template_p_tabView_activeIndexChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onActiveIndexChange($event));\n    });\n    i0.ɵɵelementStart(3, \"p-tabPanel\", 19)(4, \"div\", 20);\n    i0.ɵɵtemplate(5, ClassInfoComponent_div_0_div_1_ng_container_1_ng_container_5_Template, 1, 0, \"ng-container\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, ClassInfoComponent_div_0_div_1_ng_container_1_ng_container_6_Template, 3, 6, \"ng-container\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const tabView_r6 = i0.ɵɵreference(2);\n    i0.ɵɵnextContext(2);\n    const classInfoGeneralTemplate_r7 = i0.ɵɵreference(7);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"scrollable\", ctx_r2.classroom.classroomStudents.length > 4)(\"activeIndex\", tabView_r6.activeIndex);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", classInfoGeneralTemplate_r7)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(5, _c4, ctx_r2.classroom));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.classroom.classroomStudents);\n  }\n}\nfunction ClassInfoComponent_div_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵtemplate(1, ClassInfoComponent_div_0_div_1_ng_container_1_Template, 7, 7, \"ng-container\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.classroom.classroomStudents);\n  }\n}\nfunction ClassInfoComponent_div_0_ng_container_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ClassInfoComponent_div_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 15);\n    i0.ɵɵtemplate(2, ClassInfoComponent_div_0_ng_container_2_ng_container_2_Template, 1, 0, \"ng-container\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const classInfoGeneralTemplate_r7 = i0.ɵɵreference(7);\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", classInfoGeneralTemplate_r7)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, ctx_r2.classroom));\n  }\n}\nfunction ClassInfoComponent_div_0_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 24)(2, \"div\", 25);\n    i0.ɵɵtext(3, \" Teacher's Rating \");\n    i0.ɵɵelementStart(4, \"div\", 26);\n    i0.ɵɵtext(5, \"Rate your teacher\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 24)(7, \"div\", 25);\n    i0.ɵɵtext(8, \" Teacher's Ratings History \");\n    i0.ɵɵelementStart(9, \"div\", 26);\n    i0.ɵɵtext(10, \"Rate your teacher\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(11, \"app-teacher-rates-list\", 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"classroom\", ctx_r2.classroom);\n  }\n}\nfunction ClassInfoComponent_div_0_div_5_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtext(1, \" Listening \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.getRatingColor(\"Listening\"));\n  }\n}\nfunction ClassInfoComponent_div_0_div_5_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtext(1, \" Writing \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.getRatingColor(\"Writing\"));\n  }\n}\nfunction ClassInfoComponent_div_0_div_5_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtext(1, \" Vocabulary \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.getRatingColor(\"Vocabulary\"));\n  }\n}\nfunction ClassInfoComponent_div_0_div_5_div_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtext(1, \" Speaking \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.getRatingColor(\"Speaking\"));\n  }\n}\nfunction ClassInfoComponent_div_0_div_5_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtext(1, \" Grammar \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.getRatingColor(\"Grammar\"));\n  }\n}\nfunction ClassInfoComponent_div_0_div_5_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtext(1, \" Reading \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.getRatingColor(\"Reading\"));\n  }\n}\nfunction ClassInfoComponent_div_0_div_5_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtext(1, \" Revision \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.getRatingColor(\"Revision\"));\n  }\n}\nfunction ClassInfoComponent_div_0_div_5_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵtext(1, \" Test \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.getRatingColor(\"Test\"));\n  }\n}\nfunction ClassInfoComponent_div_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"div\", 30)(3, \"div\");\n    i0.ɵɵtext(4, \" Ratings_ \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 31);\n    i0.ɵɵlistener(\"click\", function ClassInfoComponent_div_0_div_5_Template_div_click_5_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onShowRatings());\n    });\n    i0.ɵɵtext(6, \"SEE ALL\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 32)(8, \"div\", 33)(9, \"strong\");\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"div\");\n    i0.ɵɵelementStart(13, \"div\", 34)(14, \"div\", 35);\n    i0.ɵɵlistener(\"click\", function ClassInfoComponent_div_0_div_5_Template_div_click_14_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.viewRating(ctx_r2.rating));\n    });\n    i0.ɵɵtext(15, \" View \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 36);\n    i0.ɵɵtext(17, \" Edit \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 37);\n    i0.ɵɵtemplate(19, ClassInfoComponent_div_0_div_5_div_19_Template, 2, 2, \"div\", 38)(20, ClassInfoComponent_div_0_div_5_div_20_Template, 2, 2, \"div\", 38)(21, ClassInfoComponent_div_0_div_5_div_21_Template, 2, 2, \"div\", 38)(22, ClassInfoComponent_div_0_div_5_div_22_Template, 2, 2, \"div\", 38)(23, ClassInfoComponent_div_0_div_5_div_23_Template, 2, 2, \"div\", 38)(24, ClassInfoComponent_div_0_div_5_div_24_Template, 2, 2, \"div\", 38)(25, ClassInfoComponent_div_0_div_5_div_25_Template, 2, 2, \"div\", 38)(26, ClassInfoComponent_div_0_div_5_div_26_Template, 2, 2, \"div\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(27, \"div\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 40)(29, \"div\", 30)(30, \"div\");\n    i0.ɵɵtext(31, \" Reports_ \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 31);\n    i0.ɵɵlistener(\"click\", function ClassInfoComponent_div_0_div_5_Template_div_click_32_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onShowReports());\n    });\n    i0.ɵɵtext(33, \"SEE ALL\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 41)(35, \"div\", 42)(36, \"strong\");\n    i0.ɵɵtext(37, \"Dummy report title\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\");\n    i0.ɵɵtext(39, \" A2 \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 43)(41, \"div\", 44);\n    i0.ɵɵtext(42, \" View \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 36);\n    i0.ɵɵtext(44, \" Edit \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"div\", 36);\n    i0.ɵɵtext(46, \" Download \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(47, \"div\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(48, \"div\", 46);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(11, 9, ctx_r2.lessonRating.lessonBreakdown == null ? null : ctx_r2.lessonRating.lessonBreakdown.dateCreated, \"EEEE d/M/y H:mm\"));\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.lessonRating.lessonBreakdown == null ? null : ctx_r2.lessonRating.lessonBreakdown.listening) > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.lessonRating.lessonBreakdown == null ? null : ctx_r2.lessonRating.lessonBreakdown.writing) > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.lessonRating.lessonBreakdown == null ? null : ctx_r2.lessonRating.lessonBreakdown.vocabulary) > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.lessonRating.lessonBreakdown == null ? null : ctx_r2.lessonRating.lessonBreakdown.speaking) > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.lessonRating.lessonBreakdown == null ? null : ctx_r2.lessonRating.lessonBreakdown.grammar) > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.lessonRating.lessonBreakdown == null ? null : ctx_r2.lessonRating.lessonBreakdown.reading) > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.lessonRating.lessonBreakdown == null ? null : ctx_r2.lessonRating.lessonBreakdown.revision) > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.lessonRating.lessonBreakdown == null ? null : ctx_r2.lessonRating.lessonBreakdown.test) > 0);\n  }\n}\nfunction ClassInfoComponent_div_0_ng_template_6_div_3_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Teacher\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClassInfoComponent_div_0_ng_template_6_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"h5\", 53);\n    i0.ɵɵtemplate(2, ClassInfoComponent_div_0_ng_template_6_div_3_span_2_Template, 2, 0, \"span\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 54)(4, \"li\", 74)(5, \"div\", 75)(6, \"div\", 76)(7, \"img\", 77);\n    i0.ɵɵlistener(\"error\", function ClassInfoComponent_div_0_ng_template_6_div_3_Template_img_error_7_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.generalService.setDefaultUserAvatar($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 78)(9, \"span\", 79);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 80);\n    i0.ɵɵelement(12, \"img\", 81);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 82);\n    i0.ɵɵelement(15, \"img\", 83);\n    i0.ɵɵelementStart(16, \"strong\")(17, \"a\", 84);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(19, \"div\", 85)(20, \"div\", 86);\n    i0.ɵɵtext(21, \"View Profile\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 87);\n    i0.ɵɵtext(23, \"Rate Teacher\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const classroom_r11 = i0.ɵɵnextContext().classroom;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.showUserSection);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"src\", ctx_r2.getUserPhoto(classroom_r11.teacher), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.generalService.replaceImportedWithDash(classroom_r11.teacher.firstName), \" \", ctx_r2.generalService.replaceImportedWithDash(classroom_r11.teacher.lastName), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.generalService.replaceImportedWithDash(classroom_r11.teacher.residence), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"href\", ctx_r2.generalService.getSafeSkypeUrl(ctx_r2.generalService.replaceImportedWithDash(classroom_r11.teacher.skype)), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.generalService.replaceImportedWithDash(classroom_r11.teacher.skype), \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(10, _c7, classroom_r11.id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c8, ctx_r2.role === \"Teacher\"))(\"routerLink\", i0.ɵɵpureFunction1(14, _c9, ctx_r2.classroomService.getClassroomRateUrl(classroom_r11)));\n  }\n}\nfunction ClassInfoComponent_div_0_ng_template_6_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Students\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClassInfoComponent_div_0_ng_template_6_li_8_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵlistener(\"click\", function ClassInfoComponent_div_0_ng_template_6_li_8_div_16_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const i_r14 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.changeSelectedIndex(i_r14 + 1));\n    });\n    i0.ɵɵtext(1, \"See more\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClassInfoComponent_div_0_ng_template_6_li_8_span_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\");\n  }\n}\nfunction ClassInfoComponent_div_0_ng_template_6_li_8_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵelement(1, \"button\", 99);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClassInfoComponent_div_0_ng_template_6_li_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 88)(1, \"div\", 89)(2, \"div\", 90);\n    i0.ɵɵelement(3, \"img\", 91);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 92)(5, \"span\", 79)(6, \"div\", 59)(7, \"span\", 93);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 94)(10, \"div\", 95);\n    i0.ɵɵtext(11, \" Email\");\n    i0.ɵɵelement(12, \"br\");\n    i0.ɵɵelementStart(13, \"strong\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(15, \"div\", 85);\n    i0.ɵɵtemplate(16, ClassInfoComponent_div_0_ng_template_6_li_8_div_16_Template, 2, 0, \"div\", 96);\n    i0.ɵɵelement(17, \"button\", 97);\n    i0.ɵɵelementStart(18, \"div\", 98);\n    i0.ɵɵlistener(\"click\", function ClassInfoComponent_div_0_ng_template_6_li_8_Template_div_click_18_listener() {\n      const user_r15 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.changeUserStatus(user_r15));\n    });\n    i0.ɵɵtemplate(19, ClassInfoComponent_div_0_ng_template_6_li_8_span_19_Template, 1, 0, \"span\", 14)(20, ClassInfoComponent_div_0_ng_template_6_li_8_span_20_Template, 2, 0, \"span\", 14);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const user_r15 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"opacity\", user_r15.status == \"Inactive\" ? \"0.5\" : \"1\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r2.getUserPhoto(user_r15), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.generalService.replaceImportedWithDash(user_r15.firstName), \" \", ctx_r2.generalService.replaceImportedWithDash(user_r15.lastName), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.generalService.replaceImportedWithDash(user_r15.username));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.authService.isTeacher);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"icon\", \"pi \", user_r15.status == \"Active\" ? \"pi-check-circle\" : \"pi-times-circle\", \"\");\n    i0.ɵɵproperty(\"label\", ctx_r2.generalService.replaceNotGivenWith(user_r15.status, \"Active\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", user_r15.status == \"Active\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", user_r15.status == \"Inactive\");\n  }\n}\nfunction ClassInfoComponent_div_0_ng_template_6_ng_container_9_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Change Level\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClassInfoComponent_div_0_ng_template_6_ng_container_9_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 104);\n    i0.ɵɵelement(1, \"g-level-circle\", 105);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r17 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", item_r17);\n  }\n}\nfunction ClassInfoComponent_div_0_ng_template_6_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 58)(2, \"h5\", 53);\n    i0.ɵɵtemplate(3, ClassInfoComponent_div_0_ng_template_6_ng_container_9_span_3_Template, 2, 0, \"span\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 48)(5, \"p-carousel\", 100, 5);\n    i0.ɵɵtemplate(7, ClassInfoComponent_div_0_ng_template_6_ng_container_9_ng_template_7_Template, 2, 1, \"ng-template\", 101);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 102)(9, \"button\", 103);\n    i0.ɵɵlistener(\"click\", function ClassInfoComponent_div_0_ng_template_6_ng_container_9_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.updateClassLevel());\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.showUserSection);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r2.levelItems)(\"page\", ctx_r2.getSelectedLevelIndex())(\"showIndicators\", false)(\"numVisible\", 1)(\"numScroll\", 1);\n  }\n}\nfunction ClassInfoComponent_div_0_ng_template_6_div_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 65);\n    i0.ɵɵtext(2, \"MY PACKAGE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 106)(4, \"div\", 107);\n    i0.ɵɵtext(5, \"Hours Left\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"label\", 108);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 106)(9, \"div\", 107);\n    i0.ɵɵtext(10, \"Total Hours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"label\", 108);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 106)(14, \"div\", 107);\n    i0.ɵɵtext(15, \"Total Packages\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"label\", 108);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.hoursLeft);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getTotalHours());\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.classroomPackages.length);\n  }\n}\nfunction ClassInfoComponent_div_0_ng_template_6_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 109)(1, \"span\", 110);\n    i0.ɵɵtext(2, \"ABSENCES\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 106)(4, \"label\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.userToShow.absences, \" Lessons\");\n  }\n}\nfunction ClassInfoComponent_div_0_ng_template_6_div_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 111)(1, \"span\", 65);\n    i0.ɵɵtext(2, \"LESSONS HISTORY\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 106)(4, \"div\", 107);\n    i0.ɵɵtext(5, \"Completed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"label\", 108);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 106)(9, \"div\", 107);\n    i0.ɵɵtext(10, \"Canceled\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"label\", 108);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 106)(14, \"div\", 107);\n    i0.ɵɵtext(15, \"No Show\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"label\", 108);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.getHoursOfLevel(ctx_r2.lessonHoursForEachStatus.Completed));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getHoursOfLevel(ctx_r2.lessonHoursForEachStatus.Canceled));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getHoursOfLevel(ctx_r2.lessonHoursForEachStatus[\"No Show\"]));\n  }\n}\nfunction ClassInfoComponent_div_0_ng_template_6_ng_container_76_app_class_ratings_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-class-ratings\", 27);\n  }\n  if (rf & 2) {\n    const classroom_r11 = i0.ɵɵnextContext(2).classroom;\n    i0.ɵɵproperty(\"classroom\", classroom_r11);\n  }\n}\nfunction ClassInfoComponent_div_0_ng_template_6_ng_container_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 71)(2, \"p-scrollPanel\", 72, 4);\n    i0.ɵɵtemplate(4, ClassInfoComponent_div_0_ng_template_6_ng_container_76_app_class_ratings_4_Template, 1, 1, \"app-class-ratings\", 73);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const classroom_r11 = i0.ɵɵnextContext().classroom;\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(3, _c6));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", classroom_r11);\n  }\n}\nfunction ClassInfoComponent_div_0_ng_template_6_app_class_reports_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-class-reports\", 27);\n  }\n  if (rf & 2) {\n    const classroom_r11 = i0.ɵɵnextContext().classroom;\n    i0.ɵɵproperty(\"classroom\", classroom_r11);\n  }\n}\nfunction ClassInfoComponent_div_0_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0, 48);\n    i0.ɵɵelementStart(1, \"div\", 49)(2, \"div\", 50);\n    i0.ɵɵtemplate(3, ClassInfoComponent_div_0_ng_template_6_div_3_Template, 24, 16, \"div\", 51);\n    i0.ɵɵelementStart(4, \"div\", 52)(5, \"h5\", 53);\n    i0.ɵɵtemplate(6, ClassInfoComponent_div_0_ng_template_6_span_6_Template, 2, 0, \"span\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"ul\", 54);\n    i0.ɵɵtemplate(8, ClassInfoComponent_div_0_ng_template_6_li_8_Template, 21, 12, \"li\", 55);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(9, ClassInfoComponent_div_0_ng_template_6_ng_container_9_Template, 10, 6, \"ng-container\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 56)(11, \"div\", 57)(12, \"div\", 58)(13, \"div\", 59)(14, \"h5\", 60)(15, \"span\");\n    i0.ɵɵtext(16, \"Packages Summary\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 31);\n    i0.ɵɵlistener(\"click\", function ClassInfoComponent_div_0_ng_template_6_Template_div_click_17_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.showBilling = true);\n    });\n    i0.ɵɵtext(18, \"SEE ALL\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(19, \"app-packages-progress\", 61);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 57)(21, \"div\", 62)(22, \"div\", 63)(23, \"h5\", 60)(24, \"span\");\n    i0.ɵɵtext(25, \"Lessons Summary\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"div\", 64)(27, \"div\")(28, \"span\", 65);\n    i0.ɵɵtext(29, \"HOURS PER LEVEL\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 66)(31, \"div\");\n    i0.ɵɵtext(32, \"A1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"strong\");\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 66)(36, \"div\");\n    i0.ɵɵtext(37, \"A2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"strong\");\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 66)(41, \"div\");\n    i0.ɵɵtext(42, \"B1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"strong\");\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"div\", 66)(46, \"div\");\n    i0.ɵɵtext(47, \"B2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"strong\");\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"div\", 66)(51, \"div\");\n    i0.ɵɵtext(52, \"C1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"strong\");\n    i0.ɵɵtext(54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 66)(56, \"div\");\n    i0.ɵɵtext(57, \"C2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"strong\");\n    i0.ɵɵtext(59);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(60, \"div\", 66)(61, \"div\");\n    i0.ɵɵtext(62, \"BS\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"strong\");\n    i0.ɵɵtext(64);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(65, \"div\", 67);\n    i0.ɵɵtemplate(66, ClassInfoComponent_div_0_ng_template_6_div_66_Template, 18, 3, \"div\", 14)(67, ClassInfoComponent_div_0_ng_template_6_div_67_Template, 6, 1, \"div\", 68)(68, ClassInfoComponent_div_0_ng_template_6_div_68_Template, 18, 3, \"div\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(69, \"div\", 67);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(70, \"div\", 70)(71, \"div\", 56)(72, \"div\", 58)(73, \"h5\", 60)(74, \"span\");\n    i0.ɵɵtext(75, \"Lesson Ratings\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(76, ClassInfoComponent_div_0_ng_template_6_ng_container_76_Template, 5, 4, \"ng-container\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(77, \"div\", 56)(78, \"div\", 58)(79, \"div\", 71)(80, \"div\", 59)(81, \"h5\", 60)(82, \"span\");\n    i0.ɵɵtext(83, \"Reports\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(84, \"p-scrollPanel\", 72, 4);\n    i0.ɵɵtemplate(86, ClassInfoComponent_div_0_ng_template_6_app_class_reports_86_Template, 1, 1, \"app-class-reports\", 73);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const classroom_r11 = ctx.classroom;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.role === \"Student\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.showUserSection);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", classroom_r11.classroomStudents);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.authService.isTeacher);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"small\", true)(\"packages\", ctx_r2.classroomPackages);\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r2.getHoursOfLevel(ctx_r2.lessonHoursForEachLevel.A1));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getHoursOfLevel(ctx_r2.lessonHoursForEachLevel.A2));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getHoursOfLevel(ctx_r2.lessonHoursForEachLevel.B1));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getHoursOfLevel(ctx_r2.lessonHoursForEachLevel.B2));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getHoursOfLevel(ctx_r2.lessonHoursForEachLevel.C1));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getHoursOfLevel(ctx_r2.lessonHoursForEachLevel.C2));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getHoursOfLevel(ctx_r2.lessonHoursForEachLevel.BS));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.showUserSection);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showUserSection);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.showUserSection);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", classroom_r11);\n    i0.ɵɵadvance(8);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(20, _c6));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", classroom_r11);\n  }\n}\nfunction ClassInfoComponent_div_0_ng_template_8_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 94)(1, \"div\", 117);\n    i0.ɵɵtext(2, \" Skype\");\n    i0.ɵɵelement(3, \"br\");\n    i0.ɵɵelementStart(4, \"strong\")(5, \"a\", 121);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const student_r18 = i0.ɵɵnextContext().student;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"href\", ctx_r2.generalService.getSafeSkypeUrl(ctx_r2.generalService.replaceImportedWithDash(student_r18.skype)), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.generalService.replaceImportedWithDash(student_r18.skype));\n  }\n}\nfunction ClassInfoComponent_div_0_ng_template_8_div_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 65);\n    i0.ɵɵtext(2, \"MY PACKAGE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 106)(4, \"div\", 107);\n    i0.ɵɵtext(5, \"Hours Left\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"label\", 108);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 106)(9, \"div\", 107);\n    i0.ɵɵtext(10, \"Total Hours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"label\", 108);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 106)(14, \"div\", 107);\n    i0.ɵɵtext(15, \"Total Packages\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"label\", 108);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.hoursLeft);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getTotalHours());\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.classroomPackages.length);\n  }\n}\nfunction ClassInfoComponent_div_0_ng_template_8_div_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 109)(1, \"span\", 110);\n    i0.ɵɵtext(2, \"ABSENCES\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 106)(4, \"label\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.userToShow.absences, \" Lessons\");\n  }\n}\nfunction ClassInfoComponent_div_0_ng_template_8_div_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 111)(1, \"span\", 65);\n    i0.ɵɵtext(2, \"LESSONS HISTORY\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 106)(4, \"div\", 107);\n    i0.ɵɵtext(5, \"Completed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"label\", 108);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 106)(9, \"div\", 107);\n    i0.ɵɵtext(10, \"Canceled\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"label\", 108);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 106)(14, \"div\", 107);\n    i0.ɵɵtext(15, \"No Show\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"label\", 108);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.getHoursOfLevel(ctx_r2.lessonHoursForEachStatus.Completed));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getHoursOfLevel(ctx_r2.lessonHoursForEachStatus.Canceled));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getHoursOfLevel(ctx_r2.lessonHoursForEachStatus[\"No Show\"]));\n  }\n}\nfunction ClassInfoComponent_div_0_ng_template_8_app_class_ratings_108_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-class-ratings\", 27);\n  }\n  if (rf & 2) {\n    const classroom_r19 = i0.ɵɵnextContext().classroom;\n    i0.ɵɵproperty(\"classroom\", classroom_r19);\n  }\n}\nfunction ClassInfoComponent_div_0_ng_template_8_app_class_reports_116_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-class-reports\", 27);\n  }\n  if (rf & 2) {\n    const classroom_r19 = i0.ɵɵnextContext().classroom;\n    i0.ɵɵproperty(\"classroom\", classroom_r19);\n  }\n}\nfunction ClassInfoComponent_div_0_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 112)(1, \"div\", 113)(2, \"div\", 114)(3, \"div\", 58)(4, \"h5\", 53)(5, \"span\");\n    i0.ɵɵtext(6, \"Personal Info\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"ul\", 54)(8, \"li\", 74)(9, \"div\", 115)(10, \"div\", 90);\n    i0.ɵɵelement(11, \"img\", 91);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 92)(13, \"div\", 75)(14, \"div\", 116);\n    i0.ɵɵtext(15, \" First Name\");\n    i0.ɵɵelement(16, \"br\");\n    i0.ɵɵelementStart(17, \"strong\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 117);\n    i0.ɵɵtext(20, \" Last Name\");\n    i0.ɵɵelement(21, \"br\");\n    i0.ɵɵelementStart(22, \"strong\");\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 94)(25, \"div\", 117);\n    i0.ɵɵtext(26, \" Birthday\");\n    i0.ɵɵelement(27, \"br\");\n    i0.ɵɵelementStart(28, \"strong\");\n    i0.ɵɵtext(29);\n    i0.ɵɵpipe(30, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 117);\n    i0.ɵɵtext(32, \" Location\");\n    i0.ɵɵelement(33, \"br\");\n    i0.ɵɵelementStart(34, \"strong\");\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(36, ClassInfoComponent_div_0_ng_template_8_div_36_Template, 7, 2, \"div\", 118);\n    i0.ɵɵelementStart(37, \"div\", 94)(38, \"div\", 117);\n    i0.ɵɵtext(39, \" Email\");\n    i0.ɵɵelement(40, \"br\");\n    i0.ɵɵelementStart(41, \"strong\");\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(43, \"div\", 94)(44, \"div\", 117);\n    i0.ɵɵtext(45, \" Timezone\");\n    i0.ɵɵelement(46, \"br\");\n    i0.ɵɵelementStart(47, \"strong\");\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()()()()()()()()()();\n    i0.ɵɵelementStart(49, \"div\", 113)(50, \"div\", 119)(51, \"div\", 62)(52, \"div\", 59)(53, \"h5\", 60)(54, \"span\");\n    i0.ɵɵtext(55, \"Lessons Summary\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(56, \"div\", 64)(57, \"div\")(58, \"span\", 65);\n    i0.ɵɵtext(59, \"HOURS PER LEVEL\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"div\", 66)(61, \"div\");\n    i0.ɵɵtext(62, \"A1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"strong\");\n    i0.ɵɵtext(64);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(65, \"div\", 66)(66, \"div\");\n    i0.ɵɵtext(67, \"A2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"strong\");\n    i0.ɵɵtext(69);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(70, \"div\", 66)(71, \"div\");\n    i0.ɵɵtext(72, \"B1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"strong\");\n    i0.ɵɵtext(74);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(75, \"div\", 66)(76, \"div\");\n    i0.ɵɵtext(77, \"B2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(78, \"strong\");\n    i0.ɵɵtext(79);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(80, \"div\", 66)(81, \"div\");\n    i0.ɵɵtext(82, \"C1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(83, \"strong\");\n    i0.ɵɵtext(84);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(85, \"div\", 66)(86, \"div\");\n    i0.ɵɵtext(87, \"C2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(88, \"strong\");\n    i0.ɵɵtext(89);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(90, \"div\", 66)(91, \"div\");\n    i0.ɵɵtext(92, \"BS\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(93, \"strong\");\n    i0.ɵɵtext(94);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(95, \"div\", 67);\n    i0.ɵɵtemplate(96, ClassInfoComponent_div_0_ng_template_8_div_96_Template, 18, 3, \"div\", 14)(97, ClassInfoComponent_div_0_ng_template_8_div_97_Template, 6, 1, \"div\", 68)(98, ClassInfoComponent_div_0_ng_template_8_div_98_Template, 18, 3, \"div\", 69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(99, \"div\", 67);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(100, \"div\", 120)(101, \"div\", 113)(102, \"div\", 58)(103, \"h5\", 60)(104, \"span\");\n    i0.ɵɵtext(105, \"Lesson Ratings\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(106, \"p-scrollPanel\", 72, 4);\n    i0.ɵɵtemplate(108, ClassInfoComponent_div_0_ng_template_8_app_class_ratings_108_Template, 1, 1, \"app-class-ratings\", 73);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(109, \"div\", 113)(110, \"div\", 58)(111, \"h5\", 60)(112, \"span\");\n    i0.ɵɵtext(113, \"Reports\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(114, \"p-scrollPanel\", 72, 4);\n    i0.ɵɵtemplate(116, ClassInfoComponent_div_0_ng_template_8_app_class_reports_116_Template, 1, 1, \"app-class-reports\", 73);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const student_r18 = ctx.student;\n    const classroom_r19 = ctx.classroom;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵstyleProp(\"opacity\", student_r18.status == \"Inactive\" ? \"0.5\" : \"1\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r2.getUserPhoto(student_r18), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.generalService.replaceImportedWithDash(student_r18.firstName));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.generalService.replaceImportedWithDash(student_r18.lastName));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(30, 28, student_r18.dob, \"d/M/y\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.generalService.replaceImportedWithDash(student_r18.origin), \" \", ctx_r2.generalService.replaceImportedWithDash(student_r18.residence), \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", student_r18.skype);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.generalService.replaceImportedWithDash(student_r18.username));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.generalService.convertTimezoneValueToText(student_r18.timezone).text, \" - \", ctx_r2.generalService.getUserLocalTime(ctx_r2.generalService.convertTimezoneValueToText(student_r18.timezone).text, false), \" \");\n    i0.ɵɵadvance(16);\n    i0.ɵɵtextInterpolate(ctx_r2.getHoursOfLevel(ctx_r2.lessonHoursForEachLevel.A1));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getHoursOfLevel(ctx_r2.lessonHoursForEachLevel.A2));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getHoursOfLevel(ctx_r2.lessonHoursForEachLevel.B1));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getHoursOfLevel(ctx_r2.lessonHoursForEachLevel.B2));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getHoursOfLevel(ctx_r2.lessonHoursForEachLevel.C1));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getHoursOfLevel(ctx_r2.lessonHoursForEachLevel.C2));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getHoursOfLevel(ctx_r2.lessonHoursForEachLevel.BS));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.showUserSection);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showUserSection);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.showUserSection);\n    i0.ɵɵadvance(8);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(31, _c6));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", classroom_r19);\n    i0.ɵɵadvance(6);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(32, _c6));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", classroom_r19);\n  }\n}\nfunction ClassInfoComponent_div_0_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 122);\n    i0.ɵɵelement(2, \"app-lessons-history\", 123);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"classroom\", ctx_r2.classroom)(\"wrapperScrollHeight\", \"400px\");\n  }\n}\nfunction ClassInfoComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtemplate(1, ClassInfoComponent_div_0_div_1_Template, 2, 1, \"div\", 13)(2, ClassInfoComponent_div_0_ng_container_2_Template, 3, 4, \"ng-container\", 14);\n    i0.ɵɵelementStart(3, \"div\", 15);\n    i0.ɵɵtemplate(4, ClassInfoComponent_div_0_div_4_Template, 12, 1, \"div\", 14)(5, ClassInfoComponent_div_0_div_5_Template, 49, 12, \"div\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, ClassInfoComponent_div_0_ng_template_6_Template, 87, 21, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(8, ClassInfoComponent_div_0_ng_template_8_Template, 117, 33, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(10, ClassInfoComponent_div_0_ng_container_10_Template, 3, 2, \"ng-container\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.role == \"Teacher\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.role == \"Student\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showUserSection && ctx_r2.role == \"Student\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.role == \"Teacher\" && ctx_r2.lessonRating.id);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.classroom);\n  }\n}\nfunction ClassInfoComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 124)(1, \"div\", 125)(2, \"div\", 126)(3, \"div\", 127)(4, \"div\", 128);\n    i0.ɵɵlistener(\"click\", function ClassInfoComponent_div_1_Template_div_click_4_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.showBilling = false);\n    });\n    i0.ɵɵelement(5, \"img\", 129);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 130);\n    i0.ɵɵtext(7, \" Info \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(8, \"app-teacher-billing\", 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"classroom\", ctx_r2.classroom);\n  }\n}\nfunction ClassInfoComponent_app_lesson_rating_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-lesson-rating\", 131);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"users\", ctx_r2.classroom.users)(\"lesson\", ctx_r2.lessonRating.lesson)(\"lessonRatings\", ctx_r2.lessonRating.lessonBreakdown)(\"lessonUserRatings\", ctx_r2.lessonRating.userRatings)(\"mode\", ctx_r2.mode);\n  }\n}\nexport class ClassInfoComponent {\n  constructor(router, userService, packageService, lessonService, ratingAndReportService, generalService, authService, calendarService, classroomService, toastService) {\n    this.router = router;\n    this.userService = userService;\n    this.packageService = packageService;\n    this.lessonService = lessonService;\n    this.ratingAndReportService = ratingAndReportService;\n    this.generalService = generalService;\n    this.authService = authService;\n    this.calendarService = calendarService;\n    this.classroomService = classroomService;\n    this.toastService = toastService;\n    this.subs = new SubSink();\n    this.carousel = {};\n    this.tabView = {};\n    this.scrollPanels = {};\n    this.showReports = new EventEmitter();\n    this.showRatings = new EventEmitter();\n    this.classroom = {};\n    this.classroomPackages = [];\n    this.showBilling = false;\n    this.showClassroom = true;\n    this.showUser = [];\n    this.showUserStatusList = [];\n    this.showUserSection = false;\n    this.userToShow = {};\n    this.lessonRating = {};\n    this.lessonHoursForEachLevel = {};\n    this.lessonHoursForEachStatus = [];\n    // public classroomPackages: Package[] = []\n    this.totalHours = 0;\n    this.hoursLeft = 0;\n    this.role = \"\";\n    this.user = {};\n    this.experienceInput = \"\";\n    this.activePackageTotalHours = 0;\n    this.showLevels = false;\n    this.changeLevelChoosen = \"\";\n    this.dummyLesson = data.dummyLesson.classroom;\n    this.isLoading = true;\n    this.levelItems = this.generalService.mltLevels;\n    this.selectedLevel = Level.TBD;\n    this.apiDocs = [{\n      id: 'props',\n      label: 'Properties'\n    }, {\n      id: 'templates',\n      label: 'Templates'\n    }];\n    this.active = {};\n    this.mode = 'view';\n    this.showRating = false;\n  }\n  ngOnInit() {\n    this.classroom = this.classroomService.getSelectedClassroom();\n    console.log(this.classroom.classroomStudents);\n    this.changeLevelChoosen = this.classroom.activeLevel;\n    this.classroomPackages = this.classroomPackages.slice(0, 2).reverse();\n    this.user = this.authService.getLoggedInUser();\n    this.role = this.user.role;\n    this.selectedLevel = this.classroom.activeLevel;\n    this.levelItems = this.classroomService.filterLevelsByTeachingLanguage(this.levelItems, this.authService.teachingLanguage);\n    // this.subs.sink = this.getUserLessonRatings().subscribe(res => {\n    //   for (let i = 0; i < res.length; i++) {\n    //     if (res[i][0].lessonBreakdown) {\n    //       this.lessonRating = res[i][0];\n    //       break;\n    //     }\n    //   }\n    // })\n    for (let user of this.classroom.classroomStudents) {\n      this.showUser.push(false);\n      this.showUserStatusList.push(false);\n    }\n    if (this.role == UserRole.STUDENT) {\n      this.showUser.push(false);\n      this.showUserStatusList.push(false);\n    }\n    this.getLessonHoursForEachLevel();\n    // this.getLessonHoursForEachStatus();\n    this.getClassroomPackages();\n    // this.getClassroomActivePackage();\n  }\n  ngAfterViewInit() {\n    setTimeout(() => {\n      this.scrollPanels.forEach(scrollPanel => {\n        scrollPanel.refresh();\n      });\n    }, 110);\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  onChooseChangeLevel(event) {\n    this.changeLevelChoosen = event.value;\n    this.subs.add(this.packageService.upgradePackage(this.active.id, event.value).pipe(take(1)).subscribe(res => {}));\n  }\n  getExtendPackageDisable() {\n    if (moment(new Date(this.classroom.activePackage?.expiresOn).getTime()).add('2', 'w').toDate().getTime() > moment(new Date()).add('2', 'w').toDate().getTime()) {\n      return true;\n    }\n    return false;\n  }\n  navigateToExtendPackage() {\n    this.router.navigate(['/extend-package/' + this.classroom.id]);\n  }\n  getUserLessonRatings() {\n    return this.ratingAndReportService.getClassroomRatings(this.classroom);\n  }\n  toggleClassroom() {\n    this.showClassroom = true;\n    this.showUserSection = true;\n    for (let i = 0; i < this.classroom.users.length; i++) {\n      this.showUser[i] = false;\n    }\n    if (this.role == UserRole.STUDENT) {\n      this.showUser[this.showUser.length - 1] = false;\n    }\n  }\n  toggleUser(index) {\n    this.showClassroom = false;\n    this.showUserSection = true;\n    for (let i = 0; i < this.classroom.users.length; i++) {\n      this.showUser[i] = false;\n    }\n    this.userToShow = this.classroom.users[index];\n    this.showUser[index] = true;\n  }\n  toggleTeacher() {\n    this.showClassroom = false;\n    this.showUserSection = true;\n    this.userToShow = this.classroom.teacher;\n    this.showUser[this.showUser.length - 1] = true;\n  }\n  toggleUserStatus(index) {\n    this.showUserStatusList[index] = !this.showUserStatusList[index];\n  }\n  changeUserStatus(user) {\n    let newStatus = user.status == 'Active' ? 'Inactive' : 'Active';\n    user.status = newStatus;\n    this.subs.add(this.userService.updateUserProfile(user).subscribe(res => {}));\n  }\n  getRatingColor(rating) {\n    return this.ratingAndReportService.getRatingColor(rating);\n  }\n  getUserPhoto(user) {\n    return this.userService.getUserPhoto(user);\n  }\n  getUserAge(birthday) {\n    return this.userService.calculateAge(new Date(birthday));\n  }\n  getLessonHoursForEachLevel() {\n    this.subs.sink = this.lessonService.getLessonHoursForEachLevel(this.classroom.id).subscribe(res => {\n      if (res && !this.generalService.isNullishObject(res)) {\n        // convert response keys to uppercase\n        this.lessonHoursForEachLevel = this.convertObjectKeysToUpperCase(res);\n      }\n    });\n  }\n  getLessonHoursForEachStatus() {\n    this.subs.sink = this.lessonService.getLessonHoursForEachStatus(this.classroom.id).subscribe(res => {\n      this.lessonHoursForEachStatus = res;\n    });\n  }\n  getClassroomPackages() {\n    this.subs.sink = this.packageService.getClassroomPackages(this.classroom.id).subscribe(res => {\n      this.classroomPackages = res;\n      // console.log(this.classroomPackages)\n      this.totalHours = res.reduce((a, b) => +a + +b.totalHours, 0);\n      this.hoursLeft = res.reduce((a, b) => +a + +b.hoursLeft, 0);\n    });\n  }\n  getClassroomActivePackage() {\n    return this.subs.add(this.packageService.getClassroomActivePackage(this.classroom.id).subscribe(res => {\n      this.active = res;\n      this.hoursLeft = res.hoursLeft;\n      this.activePackageTotalHours = res.totalHours;\n    }));\n  }\n  getTotalHours() {\n    return this.totalHours + this.activePackageTotalHours - this.hoursLeft;\n  }\n  getHoursOfLevel(hours) {\n    return hours ? hours : \"-\";\n  }\n  viewRating() {\n    this.showRating = true;\n    this.mode = 'view';\n    this.generalService.slideNativeElements(true, this.ratingDetails.nativeElement);\n  }\n  closeRatingDetails() {\n    this.showRating = false;\n    this.generalService.slideNativeElements(false, this.ratingDetails.nativeElement);\n  }\n  onShowRatings() {\n    this.showRatings.emit();\n  }\n  onShowReports() {\n    this.showReports.emit();\n  }\n  navigateToBuyPackage() {\n    this.router.navigate(['/buy-package/' + this.classroom.id]);\n  }\n  updateClassLevel() {\n    // console.log(this.carousel);\n    const currentIndex = this.carousel._page - 1;\n    const nextIndex = (currentIndex + 1) % this.levelItems.length;\n    console.log([this.levelItems[nextIndex]]);\n    // console.log(currentIndex);\n    this.subs.add(this.classroomService.changeClassLevel(this.classroom.id, this.levelItems[nextIndex]).pipe(take(1)).subscribe(res => {\n      console.log(res);\n      if (res) {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Classroom level has been changed to ' + this.levelItems[nextIndex]\n        });\n        this.calendarService.setUpdateListener(true);\n      }\n    }));\n    // this.carousel.value = [this.items[nextIndex]];\n  }\n  getSelectedLevelIndex() {\n    return this.levelItems.findIndex(item => item.toLowerCase() === this.selectedLevel.toLowerCase());\n  }\n  changeSelectedIndex(index) {\n    console.log(index);\n    this.tabView.activeIndex = index;\n  }\n  onActiveIndexChange(event) {\n    console.log(event);\n    this.tabView.activeIndex = event;\n  }\n  convertObjectKeysToUpperCase(res) {\n    const result = {};\n    for (const key in res) {\n      if (res.hasOwnProperty(key)) {\n        const uppercaseKey = key.toUpperCase();\n        result[uppercaseKey] = res[key];\n      }\n    }\n    return result;\n  }\n  static #_ = this.ɵfac = function ClassInfoComponent_Factory(t) {\n    return new (t || ClassInfoComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.PackageService), i0.ɵɵdirectiveInject(i4.LessonService), i0.ɵɵdirectiveInject(i5.RatingAndReportService), i0.ɵɵdirectiveInject(i6.GeneralService), i0.ɵɵdirectiveInject(i7.AuthService), i0.ɵɵdirectiveInject(i8.CalendarService), i0.ɵɵdirectiveInject(i9.ClassroomService), i0.ɵɵdirectiveInject(i10.ToastService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ClassInfoComponent,\n    selectors: [[\"app-class-info\"]],\n    viewQuery: function ClassInfoComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(_c2, 5);\n        i0.ɵɵviewQuery(_c3, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ratingDetails = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.carousel = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tabView = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollPanels = _t);\n      }\n    },\n    inputs: {\n      classroom: \"classroom\",\n      classroomPackages: \"classroomPackages\"\n    },\n    outputs: {\n      showReports: \"showReports\",\n      showRatings: \"showRatings\"\n    },\n    decls: 9,\n    vars: 3,\n    consts: [[\"ratingDetails\", \"\"], [\"classInfoGeneralTemplate\", \"\"], [\"classInfoStudentTemplate\", \"\"], [\"tabView\", \"\"], [\"scrollPanel\", \"\"], [\"carousel\", \"\"], [\"class\", \"section main-info\", 4, \"ngIf\"], [\"class\", \"section\", 4, \"ngIf\"], [1, \"modal\", \"no-visibility\"], [1, \"popup-title\"], [\"src\", \"/assets/icons/close.png\", 1, \"close-img\", \"close-img-abs\", \"hvr-glow\", 3, \"click\"], [3, \"users\", \"lesson\", \"lessonRatings\", \"lessonUserRatings\", \"mode\", 4, \"ngIf\"], [1, \"section\", \"main-info\"], [\"class\", \"w-full p-1\", 4, \"ngIf\"], [4, \"ngIf\"], [\"id\", \"classroom-info\", 1, \"grid\"], [\"class\", \"classroom-info-row\", 4, \"ngIf\"], [1, \"w-full\", \"p-1\"], [1, \"w-full\", 3, \"activeIndexChange\", \"scrollable\", \"activeIndex\"], [\"header\", \"Classroom\"], [\"id\", \"classroom-info\", 1, \"sm:grid\", \"col-12\", \"flex\", \"flex-wrap\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [4, \"ngFor\", \"ngForOf\"], [3, \"header\"], [1, \"students-section\"], [1, \"rate-teacher-title\"], [2, \"font-size\", \"13px\", \"margin\", \"20px 0\"], [3, \"classroom\"], [1, \"classroom-info-row\"], [1, \"ratings-section\"], [1, \"classroom-info-row-title\"], [1, \"gray-border-button\", 3, \"click\"], [1, \"rating\"], [1, \"ratings-list-header\"], [1, \"ratings-list-actions\"], [1, \"link-main-color\", 3, \"click\"], [1, \"link-main-color\", 2, \"margin-left\", \"10px\"], [1, \"ratings-list-tags\"], [\"class\", \"ratings-list-tag\", 3, \"backgroundColor\", 4, \"ngIf\"], [1, \"tags-seperator\"], [1, \"reports-section\"], [1, \"report\"], [1, \"reports-list-header\"], [1, \"reports-list-actions\"], [1, \"link-main-color\"], [1, \"reports-list-info\"], [1, \"reports-seperator\"], [1, \"ratings-list-tag\"], [1, \"\"], [1, \"no-padding-mobile\", \"col-12\", \"md:col-6\", \"flex\", \"p-0\"], [1, \"p-0\", \"col-12\", \"md:col-12\", \"flex\", \"flex-column\", \"justify-content-start\"], [\"class\", \"block-gradient py-2 px-2 border-round-xl\", 4, \"ngIf\"], [1, \"block-gradient\", \"py-2\", \"px-2\", \"border-round-xl\", \"h-full\"], [1, \"pb-2\", \"text-primary\", \"font-bold\"], [1, \"list-none\", \"p-0\", \"m-0\"], [\"class\", \"flex flex-column md:flex-row md:align-items-start md:justify-content-between mb-4\", 3, \"opacity\", 4, \"ngFor\", \"ngForOf\"], [1, \"no-padding-mobile\", \"col-12\", \"md:col-6\"], [1, \"no-padding-mobile\", \"col-12\", \"md:col-12\"], [1, \"block-gradient\", \"py-2\", \"px-2\", \"border-round-xl\"], [1, \"flex\", \"justify-content-between\"], [1, \"m-0\", \"text-primary\", \"font-bold\"], [3, \"small\", \"packages\"], [1, \"block-gradient\", \"py-2\", \"px-2\", \"border-round-xl\", \"lessons-info\"], [1, \"flex\", \"md:justify-content-between\"], [1, \"lessons-info-header\", \"m-t-15\"], [1, \"text-xs\", \"font-semibold\"], [1, \"lessons-stats\", \"font-xs\"], [2, \"position\", \"relative\"], [\"style\", \"position:absolute; bottom:0\", 4, \"ngIf\"], [\"class\", \"mt-3\", 4, \"ngIf\"], [1, \"no-padding-mobile\", \"col-12\", \"flex\", \"w-full\", \"flex-column\", \"md:flex-row\"], [1, \"w-full\", \"overflow-x-hidden\"], [\"styleClass\", \"custombar1\"], [3, \"classroom\", 4, \"ngIf\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"md:align-items-center\", \"md:justify-content-between\", \"mb-4\"], [1, \"flex\"], [1, \"avatar-circle\"], [1, \"h-4rem\", \"border-circle\", 3, \"error\", \"src\"], [1, \"ml-2\"], [1, \"block\", \"text-900\", \"font-semibold\"], [1, \"text-600\", \"font-xs\", \"flex\", \"justify-content-start\", \"align-items-center\", \"gap-2\"], [\"src\", \"/assets/icons/classroom/info.svg\", \"height\", \"12\"], [1, \"text-600\", \"font-xs\", \"flex\", \"justify-content-start\", \"gap-2\"], [\"src\", \"/assets/icons/classroom/skype.svg\", \"height\", \"12\"], [1, \"text-primary\", 3, \"href\"], [1, \"md:mt-0\", \"flex\", \"flex-column\", \"gap-2\", \"justify-content-start\", \"flex-nowrap\"], [1, \"gray-border-button\", 3, \"routerLink\"], [1, \"gray-border-button\", 3, \"ngClass\", \"routerLink\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"md:align-items-start\", \"md:justify-content-between\", \"mb-4\"], [1, \"flex\", \"align-items-start\"], [1, \"avatar-circle\", \"w-5rem\"], [1, \"border-circle\", 3, \"src\"], [1, \"ml-2\", \"personal-info-details\", \"font-xs\", \"w-full\"], [1, \"font-base\"], [1, \"w-full\", \"flex\"], [1, \"item\", \"col\", \"p-0\", \"mt-1\"], [\"class\", \"gray-border-button\", 3, \"click\", 4, \"ngIf\"], [\"pButton\", \"\", \"type\", \"button\", \"iconPos\", \"right\", 1, \"p-button-text\", \"pointer-events-none\", 3, \"label\", \"icon\"], [3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Active\", \"icon\", \"pi pi-check-circle\", \"iconPos\", \"right\", 1, \"p-button-text\"], [\"styleClass\", \"m-auto\", 3, \"value\", \"page\", \"showIndicators\", \"numVisible\", \"numScroll\"], [\"pTemplate\", \"item\"], [1, \"font-2xs\", \"text-center\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Update\", 1, \"p-button-xs\", \"border-round-xl\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"my-1\"], [\"size\", \"lg\", 3, \"name\"], [1, \"lessons-stats\", \"w-100\"], [1, \"text-xs\"], [1, \"ml-2\", \"font-xs\"], [2, \"position\", \"absolute\", \"bottom\", \"0\"], [1, \"lessons-info-header-text\"], [1, \"mt-3\"], [1, \"grid\"], [1, \"col-12\", \"md:col-6\"], [1, \"col-12\", \"md:col-12\"], [1, \"flex\", \"max-w-19rem\", \"align-items-start\"], [1, \"item\", \"col\", \"w-10rem\"], [1, \"item\", \"col\"], [\"class\", \"w-full flex\", 4, \"ngIf\"], [1, \"col-12\", \"md:col-12\", \"flex\", \"h-full\"], [1, \"col-12\", \"flex\", \"w-full\"], [3, \"href\"], [1, \"overflow-auto\", \"col-12\"], [3, \"classroom\", \"wrapperScrollHeight\"], [1, \"section\"], [1, \"active-package\"], [1, \"active-package-header\"], [1, \"active-package-title\"], [1, \"back-button\", \"pointer\", \"hvr-grow\", 3, \"click\"], [\"src\", \"/assets/icons/back-main-color.svg\"], [2, \"margin-left\", \"15px\"], [3, \"users\", \"lesson\", \"lessonRatings\", \"lessonUserRatings\", \"mode\"]],\n    template: function ClassInfoComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵtemplate(0, ClassInfoComponent_div_0_Template, 11, 5, \"div\", 6)(1, ClassInfoComponent_div_1_Template, 9, 1, \"div\", 7);\n        i0.ɵɵelementStart(2, \"div\", 8, 0)(4, \"div\", 9)(5, \"div\");\n        i0.ɵɵtext(6, \"Lesson Rating\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"img\", 10);\n        i0.ɵɵlistener(\"click\", function ClassInfoComponent_Template_img_click_7_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.closeRatingDetails());\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(8, ClassInfoComponent_app_lesson_rating_8_Template, 1, 5, \"app-lesson-rating\", 11);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", !ctx.showBilling);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showBilling);\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngIf\", ctx.showRating);\n      }\n    },\n    dependencies: [i11.NgClass, i11.NgForOf, i11.NgIf, i11.NgTemplateOutlet, i1.RouterLink, i12.PrimeTemplate, i13.Carousel, i14.ScrollPanel, i15.TeacherBillingComponent, i16.PackagesProgressComponent, i17.ClassReportsComponent, i18.ClassRatingsComponent, i19.TeacherRatesListComponent, i11.DatePipe],\n    styles: [\".section[_ngcontent-%COMP%] {\\n  background-color: var(--white);\\n  border-top-left-radius: 28px;\\n  border-top-right-radius: 28px;\\n  border-bottom-left-radius: 28px;\\n  border-bottom-right-radius: 28px;\\n  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);\\n  margin-top: 30px;\\n  margin-bottom: 30px;\\n}\\n.section[_ngcontent-%COMP%]   .section-content[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  transition: height 0.3s ease-out;\\n}\\n\\n.responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.active-package[_ngcontent-%COMP%]   .active-package-header[_ngcontent-%COMP%] {\\n  padding: 20px 40px 10px 40px;\\n  display: flex;\\n  justify-content: space-between;\\n}\\n.active-package[_ngcontent-%COMP%]   .active-package-header[_ngcontent-%COMP%]   .active-package-title[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  font-size: 20px;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n@media only screen and (max-width: 768px) {\\n  .no-padding-mobile[_ngcontent-%COMP%] {\\n    padding-right: 0 !important;\\n    padding-left: 0 !important;\\n  }\\n}\\n\\n[_nghost-%COMP%]     .p-carousel .p-carousel-content .p-carousel-prev, [_nghost-%COMP%]     .p-carousel .p-carousel-content .p-carousel-next {\\n  width: 2rem;\\n  height: 2rem;\\n}\\n[_nghost-%COMP%]     .p-carousel-items-content {\\n  margin-bottom: 1rem;\\n}\\n[_nghost-%COMP%]     .p-tabview .p-tabview-panels {\\n  padding: 0;\\n}\\n[_nghost-%COMP%]     .p-tabview .p-tabview-nav {\\n  border: none;\\n}\\n[_nghost-%COMP%]     .p-tabview .p-tabview-nav li .p-tabview-nav-link {\\n  border: none;\\n  color: #3F51B5;\\n}\\n\\n  .packages-top {\\n  position: relative !important;\\n}\\n\\n.info-menu[_ngcontent-%COMP%] {\\n  padding: 15px;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  margin-top: 30px;\\n}\\n.info-menu[_ngcontent-%COMP%]   .info-menu-item[_ngcontent-%COMP%] {\\n  color: var(--main-color);\\n  width: 113px;\\n  padding-bottom: 10px;\\n  cursor: pointer;\\n  text-align: center;\\n}\\n.info-menu[_ngcontent-%COMP%]   .info-menu-item-active[_ngcontent-%COMP%], .info-menu[_ngcontent-%COMP%]   .info-menu-item[_ngcontent-%COMP%]:hover {\\n  font-weight: bold;\\n  border-bottom: 1px solid var(--main-color);\\n}\\n.info-menu[_ngcontent-%COMP%]   .seperator[_ngcontent-%COMP%] {\\n  height: 30px;\\n  width: 1px;\\n  background-color: var(--light-purple);\\n  margin: 0 30px;\\n}\\n\\n.classroom-info[_ngcontent-%COMP%] {\\n  padding: 0px 20px 20px 20px;\\n  box-sizing: border-box;\\n}\\n.classroom-info[_ngcontent-%COMP%]   .classroom-info-row[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n.classroom-info[_ngcontent-%COMP%]   .classroom-info-row[_ngcontent-%COMP%]   .students-section[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.classroom-info[_ngcontent-%COMP%]   .classroom-info-row[_ngcontent-%COMP%]   .right-section[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.classroom-info-row-title[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  font-size: 25px;\\n  display: flex;\\n  justify-content: space-between;\\n}\\n\\n.students-info[_ngcontent-%COMP%]   .introduction[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  width: 100%;\\n}\\n.students-info[_ngcontent-%COMP%]   .introduction[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%] {\\n  margin-top: 5px;\\n}\\n.students-info[_ngcontent-%COMP%]   .student-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n}\\n.students-info[_ngcontent-%COMP%]   .student-info[_ngcontent-%COMP%]   .student-info-photo[_ngcontent-%COMP%] {\\n  width: 20%;\\n}\\n.students-info[_ngcontent-%COMP%]   .student-info[_ngcontent-%COMP%]   .student-info-photo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  padding: 15px;\\n  border-radius: 50%;\\n  width: 100%;\\n  box-sizing: border-box;\\n}\\n.students-info[_ngcontent-%COMP%]   .student-info[_ngcontent-%COMP%]   .student-info-details[_ngcontent-%COMP%] {\\n  width: 50%;\\n  padding: 15px;\\n  font-size: 15px;\\n  box-sizing: border-box;\\n}\\n.students-info[_ngcontent-%COMP%]   .student-info[_ngcontent-%COMP%]   .student-info-details[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%] {\\n  margin-top: 5px;\\n}\\n.students-info[_ngcontent-%COMP%]   .student-info[_ngcontent-%COMP%]   .student-info-details[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: bold;\\n}\\n.students-info[_ngcontent-%COMP%]   .student-info[_ngcontent-%COMP%]   .student-info-details[_ngcontent-%COMP%]   .view-profile[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n.students-info[_ngcontent-%COMP%]   .student-info[_ngcontent-%COMP%]   .personal-info-details[_ngcontent-%COMP%] {\\n  width: 60%;\\n  display: flex;\\n  flex-wrap: wrap;\\n  padding: 10px;\\n}\\n.students-info[_ngcontent-%COMP%]   .student-info[_ngcontent-%COMP%]   .personal-info-details[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%] {\\n  width: 50%;\\n  margin-top: 10px;\\n}\\n.students-info[_ngcontent-%COMP%]   .student-info[_ngcontent-%COMP%]   .student-info-status[_ngcontent-%COMP%] {\\n  padding: 15px;\\n  width: 30%;\\n  box-sizing: border-box;\\n}\\n.students-info[_ngcontent-%COMP%]   .student-info[_ngcontent-%COMP%]   .student-info-status[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n}\\n\\n.lessons-info[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-radius: 10px;\\n  background: #fff;\\n  padding: 15px;\\n  box-sizing: border-box;\\n  font-size: 15px;\\n}\\n.lessons-info[_ngcontent-%COMP%]   .lessons-info-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n}\\n.lessons-info[_ngcontent-%COMP%]   .lessons-info-header[_ngcontent-%COMP%]   .lessons-info-header-text[_ngcontent-%COMP%] {\\n  text-decoration: underline;\\n}\\n\\n.lessons-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  width: 70%;\\n}\\n\\n.lessons-stats[_ngcontent-%COMP%], .hours[_ngcontent-%COMP%] {\\n  margin-top: 5px;\\n}\\n\\n.w-100[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.ratings-section[_ngcontent-%COMP%], .reports-section[_ngcontent-%COMP%] {\\n  width: 50%;\\n  padding: 15px;\\n  box-sizing: border-box;\\n  max-height: 400px;\\n  overflow: auto;\\n}\\n\\n.certificates-section[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 15px;\\n  box-sizing: border-box;\\n}\\n\\n.ratings-list-header[_ngcontent-%COMP%], .reports-list-header[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n  display: flex;\\n  justify-content: space-between;\\n}\\n\\n.ratings-list-actions[_ngcontent-%COMP%], .reports-list-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.ratings-list-tags[_ngcontent-%COMP%], .reports-list-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  font-size: 12px;\\n  flex-wrap: wrap;\\n}\\n.ratings-list-tags[_ngcontent-%COMP%]   .ratings-list-tag[_ngcontent-%COMP%], .reports-list-info[_ngcontent-%COMP%]   .ratings-list-tag[_ngcontent-%COMP%] {\\n  padding: 5px 15px;\\n  margin-right: 10px;\\n  border-radius: 12px;\\n  color: white;\\n  margin-top: 15px;\\n}\\n\\n.reports-list-info[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  margin-top: 10px;\\n}\\n\\n.tags-seperator[_ngcontent-%COMP%], .reports-seperator[_ngcontent-%COMP%] {\\n  height: 2px;\\n  width: 100%;\\n  margin: 10px 0;\\n  background-color: #f4f4f8;\\n}\\n\\n.reports-seperator[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n}\\n\\n.certificates-header[_ngcontent-%COMP%] {\\n  width: 50%;\\n  display: flex;\\n  padding: 5px;\\n  box-sizing: border-box;\\n  justify-content: space-evenly;\\n  background-color: #d8d9e8;\\n}\\n\\n.certificates[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  flex-wrap: wrap;\\n  font-size: 12px;\\n}\\n.certificates[_ngcontent-%COMP%]   .certificate[_ngcontent-%COMP%] {\\n  width: 50%;\\n  padding: 15px 0;\\n  box-sizing: border-box;\\n  display: flex;\\n  justify-content: space-evenly;\\n}\\n\\n.tt[_ngcontent-%COMP%] {\\n  width: 100%;\\n  text-align: center;\\n}\\n\\n.btns[_ngcontent-%COMP%]   .main-color-button[_ngcontent-%COMP%] {\\n  width: 34%;\\n  border-radius: 24px;\\n}\\n\\n.rate-teacher-title[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  font-size: 25px;\\n}\\n\\n.rate-teacher-section[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  border: 1px solid lightgray;\\n  padding: 20px;\\n}\\n\\n.rate-teacher-title[_ngcontent-%COMP%] {\\n  margin: 20px 0;\\n  font-weight: bold;\\n}\\n\\n.rate-teacher-second-title[_ngcontent-%COMP%] {\\n  margin: 40px 0 20px 0;\\n  font-size: 15px;\\n  font-weight: bold;\\n}\\n\\n.rate-extra-choice[_ngcontent-%COMP%] {\\n  margin: 15px 0;\\n  display: flex;\\n  justify-content: space-between;\\n  border-bottom: 1px solid #cacbd9;\\n  padding: 20px 0;\\n  align-items: center;\\n}\\n.rate-extra-choice[_ngcontent-%COMP%]   .number[_ngcontent-%COMP%] {\\n  width: 5%;\\n}\\n.rate-extra-choice[_ngcontent-%COMP%]   .choice[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.rate-extra-choice[_ngcontent-%COMP%]   .rate-icons[_ngcontent-%COMP%] {\\n  width: 30%;\\n}\\n\\n.rate-icon-choice[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin: 20px 0;\\n}\\n\\n.rate-icons[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n.rate-icons[_ngcontent-%COMP%]    > img[_ngcontent-%COMP%] {\\n  width: 25px;\\n  margin: 0 10px;\\n  cursor: pointer;\\n}\\n\\n.showLevel[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 40px;\\n  margin-top: 50px;\\n  width: 109px;\\n  background: var(--light-purple);\\n  padding: 10px;\\n  border-radius: 10px;\\n  display: flex;\\n  justify-content: center;\\n}\\n\\n.btn-modify[_ngcontent-%COMP%] {\\n  padding: 0.25rem 1rem;\\n  border-radius: 5px;\\n  background: linear-gradient(#8497ff 0%, #122171 100%);\\n  color: #fff;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "moment", "take", "Level", "UserRole", "SubSink", "data", "i0", "ɵɵelementContainer", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtemplate", "ClassInfoComponent_div_0_div_1_ng_container_1_ng_container_6_ng_container_2_Template", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "selectedStudent_r4", "firstName", "classInfoStudentTemplate_r5", "ɵɵpureFunction2", "_c5", "ctx_r2", "classroom", "ɵɵlistener", "ClassInfoComponent_div_0_div_1_ng_container_1_Template_p_tabView_activeIndexChange_1_listener", "$event", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "onActiveIndexChange", "ClassInfoComponent_div_0_div_1_ng_container_1_ng_container_5_Template", "ClassInfoComponent_div_0_div_1_ng_container_1_ng_container_6_Template", "classroomStudents", "length", "tabView_r6", "activeIndex", "classInfoGeneralTemplate_r7", "ɵɵpureFunction1", "_c4", "ClassInfoComponent_div_0_div_1_ng_container_1_Template", "ClassInfoComponent_div_0_ng_container_2_ng_container_2_Template", "ɵɵtext", "ɵɵelement", "ɵɵstyleProp", "getRatingColor", "ClassInfoComponent_div_0_div_5_Template_div_click_5_listener", "_r8", "onShowRatings", "ClassInfoComponent_div_0_div_5_Template_div_click_14_listener", "viewRating", "rating", "ClassInfoComponent_div_0_div_5_div_19_Template", "ClassInfoComponent_div_0_div_5_div_20_Template", "ClassInfoComponent_div_0_div_5_div_21_Template", "ClassInfoComponent_div_0_div_5_div_22_Template", "ClassInfoComponent_div_0_div_5_div_23_Template", "ClassInfoComponent_div_0_div_5_div_24_Template", "ClassInfoComponent_div_0_div_5_div_25_Template", "ClassInfoComponent_div_0_div_5_div_26_Template", "ClassInfoComponent_div_0_div_5_Template_div_click_32_listener", "onShowReports", "ɵɵtextInterpolate", "ɵɵpipeBind2", "lessonRating", "lessonBreakdown", "dateCreated", "listening", "writing", "vocabulary", "speaking", "grammar", "reading", "revision", "test", "ClassInfoComponent_div_0_ng_template_6_div_3_span_2_Template", "ClassInfoComponent_div_0_ng_template_6_div_3_Template_img_error_7_listener", "_r10", "generalService", "setDefaultUserAvatar", "showUserSection", "getUserPhoto", "classroom_r11", "teacher", "ɵɵsanitizeUrl", "ɵɵtextInterpolate2", "replaceImportedWithDash", "lastName", "ɵɵtextInterpolate1", "residence", "getSafeSkypeUrl", "skype", "_c7", "id", "_c8", "role", "_c9", "classroomService", "getClassroomRateUrl", "ClassInfoComponent_div_0_ng_template_6_li_8_div_16_Template_div_click_0_listener", "_r13", "i_r14", "index", "changeSelectedIndex", "ClassInfoComponent_div_0_ng_template_6_li_8_div_16_Template", "ClassInfoComponent_div_0_ng_template_6_li_8_Template_div_click_18_listener", "user_r15", "_r12", "$implicit", "changeUserStatus", "ClassInfoComponent_div_0_ng_template_6_li_8_span_19_Template", "ClassInfoComponent_div_0_ng_template_6_li_8_span_20_Template", "status", "username", "authService", "<PERSON><PERSON><PERSON>er", "ɵɵpropertyInterpolate1", "replaceNotGivenWith", "item_r17", "ClassInfoComponent_div_0_ng_template_6_ng_container_9_span_3_Template", "ClassInfoComponent_div_0_ng_template_6_ng_container_9_ng_template_7_Template", "ClassInfoComponent_div_0_ng_template_6_ng_container_9_Template_button_click_9_listener", "_r16", "updateClassLevel", "levelItems", "getSelectedLevelIndex", "hoursLeft", "getTotalHours", "classroomPackages", "userToShow", "absences", "getHoursOfLevel", "lessonHoursForEachStatus", "Completed", "Canceled", "ClassInfoComponent_div_0_ng_template_6_ng_container_76_app_class_ratings_4_Template", "ɵɵstyleMap", "ɵɵpureFunction0", "_c6", "ClassInfoComponent_div_0_ng_template_6_div_3_Template", "ClassInfoComponent_div_0_ng_template_6_span_6_Template", "ClassInfoComponent_div_0_ng_template_6_li_8_Template", "ClassInfoComponent_div_0_ng_template_6_ng_container_9_Template", "ClassInfoComponent_div_0_ng_template_6_Template_div_click_17_listener", "_r9", "showBilling", "ClassInfoComponent_div_0_ng_template_6_div_66_Template", "ClassInfoComponent_div_0_ng_template_6_div_67_Template", "ClassInfoComponent_div_0_ng_template_6_div_68_Template", "ClassInfoComponent_div_0_ng_template_6_ng_container_76_Template", "ClassInfoComponent_div_0_ng_template_6_app_class_reports_86_Template", "lessonHoursForEachLevel", "A1", "A2", "B1", "B2", "C1", "C2", "BS", "student_r18", "classroom_r19", "ClassInfoComponent_div_0_ng_template_8_div_36_Template", "ClassInfoComponent_div_0_ng_template_8_div_96_Template", "ClassInfoComponent_div_0_ng_template_8_div_97_Template", "ClassInfoComponent_div_0_ng_template_8_div_98_Template", "ClassInfoComponent_div_0_ng_template_8_app_class_ratings_108_Template", "ClassInfoComponent_div_0_ng_template_8_app_class_reports_116_Template", "dob", "origin", "convertTimezoneValueToText", "timezone", "text", "getUserLocalTime", "ClassInfoComponent_div_0_div_1_Template", "ClassInfoComponent_div_0_ng_container_2_Template", "ClassInfoComponent_div_0_div_4_Template", "ClassInfoComponent_div_0_div_5_Template", "ClassInfoComponent_div_0_ng_template_6_Template", "ɵɵtemplateRefExtractor", "ClassInfoComponent_div_0_ng_template_8_Template", "ClassInfoComponent_div_0_ng_container_10_Template", "ClassInfoComponent_div_1_Template_div_click_4_listener", "_r20", "users", "lesson", "userRatings", "mode", "ClassInfoComponent", "constructor", "router", "userService", "packageService", "lessonService", "ratingAndReportService", "calendarService", "toastService", "subs", "carousel", "tabView", "scrollPanels", "showReports", "showRatings", "showClassroom", "showUser", "showUserStatusList", "totalHours", "user", "experienceInput", "activePackageTotalHours", "showLevels", "changeLevelChoosen", "du<PERSON><PERSON><PERSON><PERSON>", "isLoading", "mltLevels", "selectedLevel", "TBD", "apiDocs", "label", "active", "showRating", "ngOnInit", "getSelectedClassroom", "console", "log", "activeLevel", "slice", "reverse", "getLoggedInUser", "filterLevelsByTeachingLanguage", "teachingLanguage", "push", "STUDENT", "getLessonHoursForEachLevel", "getClassroomPackages", "ngAfterViewInit", "setTimeout", "for<PERSON>ach", "scrollPanel", "refresh", "ngOnDestroy", "unsubscribe", "onChooseChangeLevel", "event", "value", "add", "upgradePackage", "pipe", "subscribe", "res", "getExtendPackageDisable", "Date", "activePackage", "expiresOn", "getTime", "toDate", "navigateToExtendPackage", "navigate", "getUserLessonRatings", "getClassroomRatings", "toggleClassroom", "i", "toggleUser", "toggle<PERSON><PERSON><PERSON>", "toggleUserStatus", "newStatus", "updateUserProfile", "getUserAge", "birthday", "calculateAge", "sink", "isNullishObject", "convertObjectKeysToUpperCase", "getLessonHoursForEachStatus", "reduce", "a", "b", "getClassroomActivePackage", "hours", "slideNativeElements", "ratingDetails", "nativeElement", "closeRatingDetails", "emit", "navigateToBuyPackage", "currentIndex", "_page", "nextIndex", "changeClassLevel", "setShowToastmessage", "severity", "summary", "detail", "setUpdateListener", "findIndex", "item", "toLowerCase", "result", "key", "hasOwnProperty", "uppercaseKey", "toUpperCase", "_", "ɵɵdirectiveInject", "i1", "Router", "i2", "UserService", "i3", "PackageService", "i4", "LessonService", "i5", "RatingAndReportService", "i6", "GeneralService", "i7", "AuthService", "i8", "CalendarService", "i9", "ClassroomService", "i10", "ToastService", "_2", "selectors", "viewQuery", "ClassInfoComponent_Query", "rf", "ctx", "ClassInfoComponent_div_0_Template", "ClassInfoComponent_div_1_Template", "ClassInfoComponent_Template_img_click_7_listener", "_r1", "ClassInfoComponent_app_lesson_rating_8_Template"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\classrooms\\class\\class-info\\class-info.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\classrooms\\class\\class-info\\class-info.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output, QueryList, ViewChild, ViewChildren } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport * as moment from 'moment';\r\nimport { take } from 'rxjs/operators';\r\nimport { Classroom, Level } from 'src/app/core/models/classroom.model';\r\nimport { Package } from 'src/app/core/models/package.model';\r\nimport { LessonFullRating } from 'src/app/core/models/rating.model';\r\nimport { User, UserRole, UserStatus } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { LessonService } from 'src/app/core/services/lesson.service';\r\nimport { PackageService } from 'src/app/core/services/package.service';\r\nimport { RatingAndReportService } from 'src/app/core/services/rating-and-report.service';\r\nimport { UserService } from 'src/app/core/services/user.service';\r\nimport { SubSink } from 'subsink';\r\nimport * as data from '../../../../../core/models/data';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { Carousel } from 'primeng/carousel';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { CalendarService } from 'src/app/core/services/calendar.service';\r\nimport { ScrollPanel } from 'primeng/scrollpanel';\r\nimport { TabView } from 'primeng/tabview';\r\n\r\n@Component({\r\n  selector: 'app-class-info',\r\n  templateUrl: './class-info.component.html',\r\n  styleUrls: ['./class-info.component.scss']\r\n})\r\nexport class ClassInfoComponent implements OnInit {\r\n  private subs = new SubSink()\r\n  @ViewChild('ratingDetails') public ratingDetails: any;\r\n  @ViewChild('carousel', { static: false }) carousel: Carousel = {} as Carousel;\r\n  @ViewChild('tabView') tabView: TabView = {} as TabView;\r\n  @ViewChildren('scrollPanel') scrollPanels: QueryList<ScrollPanel> = {} as QueryList<ScrollPanel>;\r\n\r\n  @Output() showReports = new EventEmitter();\r\n  @Output() showRatings = new EventEmitter();\r\n  @Input() classroom: any = {} as Classroom;\r\n  @Input() classroomPackages: Package[] = [];\r\n  public showBilling: boolean = false;\r\n  public showClassroom: boolean = true;\r\n  public showUser: boolean[] = [];\r\n  public showUserStatusList: boolean[] = [];\r\n  public showUserSection: boolean = false;\r\n  public userToShow: User = {} as User;\r\n  public lessonRating: LessonFullRating = {} as LessonFullRating;\r\n  public lessonHoursForEachLevel: { [key: string]: string } = {};\r\n  public lessonHoursForEachStatus: [] = []\r\n  // public classroomPackages: Package[] = []\r\n  public totalHours: number = 0;\r\n  public hoursLeft: number = 0;\r\n  public role: string = \"\"\r\n  public user: User = {} as User\r\n  public experienceInput: string = \"\";\r\n  activePackageTotalHours: number = 0;\r\n  showLevels: boolean = false;\r\n  changeLevelChoosen: string = \"\";\r\n  dummyLesson = data.dummyLesson.classroom;\r\n  isLoading = true;\r\n  levelItems = this.generalService.mltLevels;\r\n  selectedLevel: string = Level.TBD;\r\n\r\n  apiDocs = [\r\n    {\r\n        id: 'props',\r\n        label: 'Properties',\r\n    },\r\n    {\r\n        id: 'templates',\r\n        label: 'Templates',\r\n    }\r\n];\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private userService: UserService,\r\n    private packageService: PackageService,\r\n    private lessonService: LessonService,\r\n    private ratingAndReportService: RatingAndReportService,\r\n    public generalService: GeneralService,\r\n    public authService: AuthService,\r\n    private calendarService: CalendarService,\r\n    public classroomService: ClassroomService,\r\n    public toastService: ToastService,\r\n    \r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.classroom = this.classroomService.getSelectedClassroom();\r\n\r\n    console.log(this.classroom.classroomStudents);\r\n    this.changeLevelChoosen = this.classroom.activeLevel;\r\n    this.classroomPackages=this.classroomPackages.slice(0, 2).reverse()\r\n    this.user = this.authService.getLoggedInUser();\r\n    this.role = this.user.role!;\r\n    this.selectedLevel = this.classroom.activeLevel;\r\n    this.levelItems = this.classroomService.filterLevelsByTeachingLanguage(this.levelItems, this.authService.teachingLanguage);\r\n    // this.subs.sink = this.getUserLessonRatings().subscribe(res => {\r\n    //   for (let i = 0; i < res.length; i++) {\r\n    //     if (res[i][0].lessonBreakdown) {\r\n    //       this.lessonRating = res[i][0];\r\n    //       break;\r\n    //     }\r\n    //   }\r\n    // })\r\n    for (let user of this.classroom.classroomStudents) {\r\n      this.showUser.push(false);\r\n      this.showUserStatusList.push(false);\r\n    }\r\n    if (this.role == UserRole.STUDENT) {\r\n      this.showUser.push(false)\r\n      this.showUserStatusList.push(false);\r\n    }\r\n    this.getLessonHoursForEachLevel();\r\n    // this.getLessonHoursForEachStatus();\r\n    this.getClassroomPackages();\r\n    // this.getClassroomActivePackage();\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    setTimeout(() => {\r\n      this.scrollPanels.forEach(scrollPanel => {\r\n        scrollPanel.refresh();\r\n      });\r\n    }, 110);\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe()\r\n  }\r\n\r\n  onChooseChangeLevel(event: any){\r\n    this.changeLevelChoosen = event.value\r\n    this.subs.add(this.packageService.upgradePackage(this.active.id, event.value).pipe(take(1)).subscribe(res=>{\r\n    }));\r\n  }\r\n\r\n\r\n  getExtendPackageDisable() {\r\n    if (moment(new Date(this.classroom.activePackage?.expiresOn!).getTime()!).add('2', 'w').toDate().getTime() > moment(new Date()).add('2', 'w').toDate().getTime()) {\r\n      return true\r\n    }\r\n    return false;\r\n  }\r\n\r\n  navigateToExtendPackage() {\r\n    this.router.navigate(['/extend-package/' + this.classroom.id])\r\n  }\r\n\r\n  getUserLessonRatings() {\r\n    return this.ratingAndReportService.getClassroomRatings(this.classroom)\r\n  }\r\n\r\n  toggleClassroom() {\r\n    this.showClassroom = true;\r\n    this.showUserSection = true;\r\n    for (let i = 0; i < this.classroom.users.length; i++) {\r\n      this.showUser[i] = false;\r\n    }\r\n    if (this.role == UserRole.STUDENT) {\r\n      this.showUser[this.showUser.length - 1] = false;\r\n\r\n    }\r\n  }\r\n\r\n  toggleUser(index: number) {\r\n    this.showClassroom = false;\r\n    this.showUserSection = true;\r\n    for (let i = 0; i < this.classroom.users.length; i++) {\r\n      this.showUser[i] = false;\r\n    }\r\n    this.userToShow = this.classroom.users[index];\r\n    this.showUser[index] = true;\r\n  }\r\n\r\n  toggleTeacher() {\r\n    this.showClassroom = false;\r\n    this.showUserSection = true;\r\n    this.userToShow = this.classroom.teacher!\r\n    this.showUser[this.showUser.length - 1] = true;\r\n  }\r\n\r\n  toggleUserStatus(index: number) {\r\n    this.showUserStatusList[index] = !this.showUserStatusList[index];\r\n  }\r\n\r\n  changeUserStatus(user: User) {\r\n    let newStatus = user.status == 'Active' ? 'Inactive' : 'Active'\r\n    user.status = newStatus as UserStatus\r\n    this.subs.add(this.userService.updateUserProfile(user).subscribe(res => {\r\n    }));\r\n  }\r\n\r\n  getRatingColor(rating: string) {\r\n    return this.ratingAndReportService.getRatingColor(rating)\r\n  }\r\n\r\n  getUserPhoto(user: User) {\r\n    return this.userService.getUserPhoto(user);\r\n  }\r\n\r\n  getUserAge(birthday: Date) {\r\n    return this.userService.calculateAge(new Date(birthday))\r\n  }\r\n\r\n  getLessonHoursForEachLevel() {\r\n    this.subs.sink = this.lessonService.getLessonHoursForEachLevel(this.classroom.id).subscribe(res => {\r\n      if (res && !this.generalService.isNullishObject(res)) {\r\n        // convert response keys to uppercase\r\n        this.lessonHoursForEachLevel = this.convertObjectKeysToUpperCase(res);\r\n      }\r\n    })\r\n  }\r\n\r\n  getLessonHoursForEachStatus() {\r\n    this.subs.sink = this.lessonService.getLessonHoursForEachStatus(this.classroom.id).subscribe(res => {\r\n      this.lessonHoursForEachStatus = res;\r\n    })\r\n  }\r\n\r\n  getClassroomPackages() {\r\n    this.subs.sink = this.packageService.getClassroomPackages(this.classroom.id).subscribe(res => {\r\n      this.classroomPackages = res;\r\n      // console.log(this.classroomPackages)\r\n      this.totalHours = res.reduce((a, b) => +a + +b.totalHours, 0);\r\n      this.hoursLeft = res.reduce((a, b) => +a + +b.hoursLeft, 0);\r\n    })\r\n  }\r\n\r\n  active: Package = {} as Package\r\n  getClassroomActivePackage() {\r\n    return this.subs.add(this.packageService.getClassroomActivePackage(this.classroom.id).subscribe(res => {\r\n      this.active = res;\r\n      this.hoursLeft = res.hoursLeft\r\n      this.activePackageTotalHours = res.totalHours\r\n    }));\r\n  }\r\n\r\n  getTotalHours(){\r\n    return this.totalHours + this.activePackageTotalHours - this.hoursLeft\r\n  }\r\n\r\n  getHoursOfLevel(hours: string) {\r\n    return hours ? hours : \"-\"\r\n  }\r\n\r\n  mode: 'create' | 'view' | 'edit' = 'view';\r\n  showRating: boolean = false;\r\n  viewRating() {\r\n    this.showRating = true;\r\n    this.mode = 'view';\r\n    this.generalService.slideNativeElements(true, this.ratingDetails.nativeElement)\r\n  }\r\n\r\n  closeRatingDetails() {\r\n    this.showRating = false;\r\n    this.generalService.slideNativeElements(false, this.ratingDetails.nativeElement)\r\n  }\r\n\r\n  onShowRatings() {\r\n    this.showRatings.emit();\r\n  }\r\n\r\n  onShowReports() {\r\n    this.showReports.emit();\r\n  }\r\n\r\n  navigateToBuyPackage() {\r\n    this.router.navigate(['/buy-package/' + this.classroom.id])\r\n  }\r\n\r\n  updateClassLevel() {\r\n    // console.log(this.carousel);\r\n    const currentIndex = this.carousel._page - 1;\r\n    const nextIndex = (currentIndex + 1) % this.levelItems.length;\r\n    console.log([this.levelItems[nextIndex]])\r\n    // console.log(currentIndex);\r\n    this.subs.add(this.classroomService.changeClassLevel(this.classroom.id, this.levelItems[nextIndex]).pipe(take(1)).subscribe(res => {\r\n      console.log(res);\r\n      if (res) {\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'success',\r\n          summary: '',\r\n          detail: 'Classroom level has been changed to ' + this.levelItems[nextIndex]\r\n        });\r\n        this.calendarService.setUpdateListener(true);\r\n      }\r\n    }));\r\n    // this.carousel.value = [this.items[nextIndex]];\r\n  }\r\n\r\n  getSelectedLevelIndex(): number {\r\n    return this.levelItems.findIndex(item => item.toLowerCase() === this.selectedLevel.toLowerCase());\r\n  }\r\n\r\n  changeSelectedIndex(index: number) {\r\n    console.log(index);\r\n    this.tabView.activeIndex = index;\r\n  }\r\n\r\n  onActiveIndexChange(event: any) {\r\n    console.log(event);\r\n    this.tabView.activeIndex = event;\r\n  }\r\n\r\n  private convertObjectKeysToUpperCase(res: Record<string,string>): { [key: string]: string } {\r\n    const result = {} as { [key: string]: string };\r\n    for (const key in res) {\r\n      if (res.hasOwnProperty(key)) {\r\n        const uppercaseKey = key.toUpperCase();\r\n        result[uppercaseKey] = res[key];\r\n      }\r\n    }\r\n    return result;\r\n  }\r\n\r\n}\r\n", "\r\n<div *ngIf=\"!showBilling\" class=\"section main-info\">\r\n        <div class=\"w-full p-1\" *ngIf=\"role=='Teacher'\">\r\n            <ng-container *ngIf=\"classroom.classroomStudents\">\r\n                <p-tabView #tabView [scrollable]=\"classroom.classroomStudents.length > 4\"\r\n                    [activeIndex]=\"tabView.activeIndex\"\r\n                    (activeIndexChange)=\"onActiveIndexChange($event)\" class=\"w-full\">\r\n                  <p-tabPanel header=\"Classroom\">\r\n                    <div id=\"classroom-info\" class=\"sm:grid col-12 flex flex-wrap\">\r\n                    <ng-container *ngTemplateOutlet=\"classInfoGeneralTemplate; context: { classroom: classroom }\">\r\n                    </ng-container>\r\n\r\n                    </div>\r\n                  </p-tabPanel>\r\n                  <ng-container *ngFor=\"let selectedStudent of classroom.classroomStudents; index as i\">\r\n                    <p-tabPanel [header]=\"selectedStudent.firstName === 'Not Given' ? 'Student' : selectedStudent.firstName\">\r\n                        <ng-container *ngTemplateOutlet=\"classInfoStudentTemplate; context: { \r\n                            classroom: classroom, \r\n                            student: selectedStudent }\">\r\n                        </ng-container>\r\n                    </p-tabPanel>\r\n                  </ng-container>\r\n                </p-tabView>\r\n              </ng-container>\r\n            </div>\r\n\r\n            <ng-container *ngIf=\"role=='Student'\">\r\n                <div id=\"classroom-info\" class=\"grid\">\r\n                    <ng-container *ngTemplateOutlet=\"classInfoGeneralTemplate; context: { classroom: classroom }\">\r\n                    </ng-container>\r\n                </div>\r\n            </ng-container>\r\n    <div id=\"classroom-info\" class=\"grid\">\r\n        <div *ngIf=\"showUserSection && role=='Student'\">\r\n            <div class=\"students-section\">\r\n                <div class=\"rate-teacher-title\">\r\n                    Teacher's Rating\r\n                    <div style=\"font-size:13px; margin:20px 0;\">Rate your teacher</div>\r\n                </div>\r\n                \r\n            </div>\r\n\r\n            <div class=\"students-section\">\r\n                <div class=\"rate-teacher-title\">\r\n                    Teacher's Ratings History\r\n                    <div style=\"font-size:13px; margin:20px 0;\">Rate your teacher</div>\r\n                </div>\r\n                <app-teacher-rates-list [classroom]=\"classroom\"></app-teacher-rates-list>\r\n            </div>\r\n        </div>\r\n\r\n\r\n        <div *ngIf=\"role=='Teacher' && lessonRating.id\" class=\"classroom-info-row\">\r\n            <div class=\"ratings-section\">\r\n                <div class=\"classroom-info-row-title\">\r\n                    <div>\r\n                        Ratings_\r\n                    </div>\r\n                    <div class=\"gray-border-button\" (click)=\"onShowRatings()\">SEE ALL</div>\r\n\r\n                </div>\r\n                <div class=\"rating\">\r\n                    <div class=\"ratings-list-header\">\r\n                        <strong>{{lessonRating.lessonBreakdown?.dateCreated | date: 'EEEE d/M/y H:mm'}}</strong>\r\n                        <div>\r\n                            <!-- A2 -->\r\n                        </div>\r\n                        <div class=\"ratings-list-actions\">\r\n                            <div class=\"link-main-color\" (click)=\"viewRating(rating)\">\r\n                                View\r\n                            </div>\r\n                            <div class=\"link-main-color\" style=\"margin-left:10px;\">\r\n                                Edit\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"ratings-list-tags\">\r\n                        <div *ngIf=\"lessonRating.lessonBreakdown?.listening > 0\" class=\"ratings-list-tag\"\r\n                            [style.backgroundColor]=\"getRatingColor('Listening')\">\r\n                            Listening\r\n                        </div>\r\n                        <div *ngIf=\"lessonRating.lessonBreakdown?.writing > 0\" class=\"ratings-list-tag\"\r\n                            [style.backgroundColor]=\"getRatingColor('Writing')\">\r\n                            Writing\r\n                        </div>\r\n                        <div *ngIf=\"lessonRating.lessonBreakdown?.vocabulary > 0\" class=\"ratings-list-tag\"\r\n                            [style.backgroundColor]=\"getRatingColor('Vocabulary')\">\r\n                            Vocabulary\r\n                        </div>\r\n                        <div *ngIf=\"lessonRating.lessonBreakdown?.speaking > 0\" class=\"ratings-list-tag\"\r\n                            [style.backgroundColor]=\"getRatingColor('Speaking')\">\r\n                            Speaking\r\n                        </div>\r\n                        <div *ngIf=\"lessonRating.lessonBreakdown?.grammar > 0\" class=\"ratings-list-tag\"\r\n                            [style.backgroundColor]=\"getRatingColor('Grammar')\">\r\n                            Grammar\r\n                        </div>\r\n                        <div *ngIf=\"lessonRating.lessonBreakdown?.reading > 0\" class=\"ratings-list-tag\"\r\n                            [style.backgroundColor]=\"getRatingColor('Reading')\">\r\n                            Reading\r\n                        </div>\r\n                        <div *ngIf=\"lessonRating.lessonBreakdown?.revision > 0\" class=\"ratings-list-tag\"\r\n                            [style.backgroundColor]=\"getRatingColor('Revision')\">\r\n                            Revision\r\n                        </div>\r\n                        <div *ngIf=\"lessonRating.lessonBreakdown?.test > 0\" class=\"ratings-list-tag\"\r\n                            [style.backgroundColor]=\"getRatingColor('Test')\">\r\n                            Test\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"tags-seperator\"></div>\r\n            </div>\r\n            <div class=\"reports-section\">\r\n                <div class=\"classroom-info-row-title\">\r\n                    <div>\r\n                        Reports_\r\n                    </div>\r\n                    <div class=\"gray-border-button\" (click)=\"onShowReports()\">SEE ALL</div>\r\n                </div>\r\n                <div class=\"report\">\r\n                    <div class=\"reports-list-header\">\r\n                        <strong>Dummy report title</strong>\r\n                        <div>\r\n                            A2\r\n                        </div>\r\n                        <div class=\"reports-list-actions\">\r\n                            <div class=\"link-main-color\">\r\n                                View\r\n                            </div>\r\n                            <div class=\"link-main-color\" style=\"margin-left:10px;\">\r\n                                Edit\r\n                            </div>\r\n                            <div class=\"link-main-color\" style=\"margin-left:10px;\">\r\n                                Download\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"reports-list-info\">\r\n                        <!-- <strong>{{dummyLessonRatings.date | date: 'EEEE d/M/y H:mm'}}</strong> -->\r\n                    </div>\r\n                </div>\r\n                <div class=\"reports-seperator\"></div>\r\n            </div>\r\n        </div>\r\n        <!-- <div *ngIf=\"role=='Teacher'\" class=\"classroom-info-row\">\r\n            <div class=\"certificates-section\">\r\n                <div class=\"classroom-info-row-title\" style=\"margin-bottom:15px;\">\r\n                    <div>\r\n                        Certificates_\r\n                    </div>\r\n                </div>\r\n                <div class=\"certificates\">\r\n                    <div class=\"certificates-header\">\r\n                        <div class=\"tt\">\r\n                            Level\r\n                        </div>\r\n                        <div class=\"tt\">\r\n                            Date\r\n                        </div>\r\n                        <div class=\"tt\">\r\n                            Rceived\r\n                        </div>\r\n                        <div class=\"tt\">\r\n                            Notify\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"certificates-header\">\r\n                        <div class=\"tt\">\r\n                            Level\r\n                        </div>\r\n                        <div class=\"tt\">\r\n                            Level\r\n                        </div>\r\n                        <div class=\"tt\">\r\n                            Level\r\n                        </div>\r\n                        <div class=\"tt\">\r\n                            Level\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"certificate\">\r\n                        <div class=\"tt\">a</div>\r\n                        <div class=\"tt\">a</div>\r\n                        <div class=\"tt\">a</div>\r\n                        <div class=\"tt\">\r\n                            <div class=\"main-color-button\">\r\n                                CONGRATULATE\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div> -->\r\n    </div>\r\n\r\n\r\n    <ng-template #classInfoGeneralTemplate let-classroom=\"classroom\" let-selectedIndex=\"selectedIndex\">\r\n    <ng-container class=\"\">\r\n        <div class=\"no-padding-mobile col-12 md:col-6  flex p-0\">\r\n            <div class=\"p-0 col-12 md:col-12 flex flex-column justify-content-start\">\r\n                <div class=\"block-gradient py-2 px-2 border-round-xl\" *ngIf=\"role === 'Student'\">\r\n                    <h5 class=\"pb-2 text-primary font-bold\"><span *ngIf=\"!showUserSection\">Teacher</span></h5>\r\n                    <ul class=\"list-none p-0 m-0\">\r\n                        <li class=\"flex flex-column md:flex-row md:align-items-center md:justify-content-between mb-4\">\r\n                            <div class=\"flex\">\r\n                                <div class=\"avatar-circle\">\r\n                                <img [src]=\"getUserPhoto(classroom.teacher)\" (error)=\"generalService.setDefaultUserAvatar($event)\" \r\n                                class=\" h-4rem border-circle\">\r\n                                </div>\r\n                                <div class=\"ml-2\"><span class=\"block text-900 font-semibold\">{{generalService.replaceImportedWithDash(classroom.teacher.firstName)}}\r\n                                        {{generalService.replaceImportedWithDash(classroom.teacher.lastName)}}</span>\r\n                                    <div class=\"text-600 font-xs flex justify-content-start align-items-center gap-2\">\r\n                                        <img src=\"/assets/icons/classroom/info.svg\" height=\"12\" />\r\n                                        {{generalService.replaceImportedWithDash(classroom.teacher.residence)}}\r\n                                    </div>\r\n                                    <div class=\"text-600 font-xs flex justify-content-start gap-2\">\r\n                                        <img src=\"/assets/icons/classroom/skype.svg\" height=\"12\" />\r\n                                        <strong><a class=\"text-primary\" [href]=\"generalService.getSafeSkypeUrl(generalService.replaceImportedWithDash(classroom.teacher.skype))\">\r\n                                            {{generalService.replaceImportedWithDash(classroom.teacher.skype)}}</a></strong>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"md:mt-0 flex flex-column gap-2 justify-content-start flex-nowrap\">\r\n                                <div class=\"gray-border-button\" [routerLink]=\"['/dashboard', 'classrooms', 'lessons',  classroom.id, 'info', 'teacher']\">View Profile</div>\r\n                                <div class=\"gray-border-button\" [ngClass]=\"{'disabled-link': role === 'Teacher' }\" [routerLink]=\"[classroomService.getClassroomRateUrl(classroom)]\">Rate Teacher</div>\r\n                            </div>\r\n                        </li>\r\n                    </ul>\r\n                </div>\r\n    \r\n                <div class=\"block-gradient py-2 px-2 border-round-xl h-full\">\r\n                    <h5 class=\"pb-2 text-primary font-bold\"><span *ngIf=\"!showUserSection\">Students</span></h5>\r\n                    <ul class=\"list-none p-0 m-0\">\r\n                        <li *ngFor=\"let user of classroom.classroomStudents; let i = index\"\r\n                            [style.opacity]=\"user.status=='Inactive'? '0.5': '1'\"\r\n                            class=\"flex flex-column md:flex-row md:align-items-start md:justify-content-between mb-4\">\r\n                            <div class=\"flex align-items-start\">\r\n                                \r\n                                <div class=\"avatar-circle w-5rem \">\r\n                                <img [src]=\"getUserPhoto(user)\"\r\n                                    class=\"border-circle\">\r\n                                </div>\r\n\r\n                                    <div class=\"ml-2 personal-info-details font-xs w-full\">\r\n                                        \r\n                                        <span class=\"block text-900 font-semibold\">\r\n                                            <div class=\"flex justify-content-between\">\r\n                                            <span class=\"font-base\">\r\n                                            {{generalService.replaceImportedWithDash(user.firstName)}}\r\n                                            {{generalService.replaceImportedWithDash(user.lastName)}}\r\n                                            </span>\r\n                                        </div>\r\n                                        </span>\r\n                                        <div class=\"w-full flex\">\r\n                                        <div class=\"item col p-0 mt-1\">\r\n                                            Email<br>\r\n                                            <strong>{{generalService.replaceImportedWithDash(user.username)}}</strong>\r\n                                        </div>\r\n                                        </div>\r\n                                    </div>\r\n\r\n                                <!-- <div><span class=\"block text-900 font-semibold\">\r\n                                    <div class=\"flex justify-content-between\">\r\n                                    <span>\r\n                                    {{generalService.replaceImportedWithDash(user.firstName)}}\r\n                                    {{generalService.replaceImportedWithDash(user.lastName)}}\r\n                                    </span>\r\n                                </div>\r\n                                </span>\r\n                                    <div class=\"text-600 font-xs flex align-items-center justify-content-start gap-2\">\r\n                                        <img src=\"/assets/icons/classroom/skype.svg\" />\r\n                                        {{generalService.replaceImportedWithDash(user.skype)}}\r\n                                    </div>\r\n                                    <div class=\"text-600 font-xs flex align-items-center justify-content-start gap-2\">\r\n                                        <img src=\"/assets/icons/email.png\" height=\"12\" />\r\n                                        {{generalService.replaceImportedWithDash(user.username)}}\r\n                                    </div>\r\n                                    <div class=\"text-600 font-xs flex align-items-center justify-content-start gap-2\">\r\n                                        <img src=\"/assets/icons/classroom/info.svg\" height=\"12\" />\r\n                                        {{generalService.convertTimezoneValueToText(user.timezone).text}} - \r\n                                        {{generalService.getUserLocalTime(generalService.convertTimezoneValueToText(user.timezone).text, false)}}\r\n                                    </div>\r\n                                </div> -->\r\n                            </div>\r\n                            <div class=\"md:mt-0 flex flex-column gap-2 justify-content-start flex-nowrap\">\r\n    \r\n                                <div *ngIf=\"authService.isTeacher\" class=\"gray-border-button\" (click)=\"changeSelectedIndex(i + 1)\">See more</div>\r\n                                <!-- <img *ngIf=\"role=='Teacher'\" src=\"/assets/icons/down-arrow.svg\"\r\n                                    style=\"width:20px; cursor: pointer;\" (click)=\"toggleUserStatus(i)\"> -->\r\n                                <!-- *ngIf=\"showUserStatusList[i]\" -->\r\n                                <button pButton type=\"button\" [label]=\" generalService.replaceNotGivenWith(user.status, 'Active')\" \r\n                                icon=\"pi {{user.status=='Active' ? 'pi-check-circle' : 'pi-times-circle' }}\" iconPos=\"right\"\r\n                                    class=\"p-button-text pointer-events-none\"></button>\r\n    \r\n                                <div (click)=\"changeUserStatus(user)\">\r\n                                    <span *ngIf=\"user.status=='Active'\">\r\n    \r\n                                    </span>\r\n                                    <span *ngIf=\"user.status=='Inactive'\">\r\n                                        <button pButton type=\"button\" label=\"Active\" icon=\"pi pi-check-circle\"\r\n                                            iconPos=\"right\" class=\"p-button-text\"></button>\r\n    \r\n                                    </span>\r\n                                </div>\r\n    \r\n                            </div>\r\n                        </li>\r\n                    </ul>\r\n                </div>\r\n\r\n                <ng-container *ngIf=\"authService.isTeacher\">\r\n                <!-- Classroom change level starts -->\r\n                <div class=\"block-gradient py-2 px-2 border-round-xl\">\r\n                    <h5 class=\"pb-2 text-primary font-bold\"><span *ngIf=\"!showUserSection\">Change Level</span></h5>\r\n                    <div class=\"\">\r\n                        <p-carousel #carousel [value]=\"levelItems\" [page]=\"getSelectedLevelIndex()\" styleClass=\"m-auto\"\r\n                            [showIndicators]=\"false\" [numVisible]=\"1\" [numScroll]=\"1\">\r\n                            <ng-template let-item pTemplate=\"item\">\r\n                                <div class=\"flex align-items-center justify-content-center my-1\">\r\n                                    <g-level-circle size=\"lg\" [name]=\"item\"></g-level-circle>\r\n                                </div>\r\n                            </ng-template>\r\n                        </p-carousel>\r\n                        <p class=\"font-2xs text-center\">\r\n                            <button pButton type=\"button\" (click)=\"updateClassLevel()\" label=\"Update\" class=\"p-button-xs border-round-xl\"></button>\r\n                        </p>\r\n                    </div>\r\n                </div>\r\n                <!-- Classroom change level ends -->\r\n                </ng-container>\r\n            \r\n            </div>\r\n    \r\n        </div>\r\n    \r\n        <div class=\" no-padding-mobile col-12 md:col-6\">\r\n    \r\n            <div class=\"no-padding-mobile col-12 md:col-12\">\r\n                <div class=\"block-gradient py-2 px-2 border-round-xl\">\r\n                    <div class=\"flex justify-content-between\">\r\n                        <h5 class=\"m-0 text-primary font-bold\"><span>Packages Summary</span></h5>\r\n                        <div class=\"gray-border-button\" (click)=\"showBilling = true\">SEE ALL</div>\r\n                    </div>\r\n                    <app-packages-progress [small]=\"true\" [packages]=\"classroomPackages\"></app-packages-progress>\r\n                </div>\r\n            </div>\r\n    \r\n    \r\n            <div class=\"no-padding-mobile col-12 md:col-12\">\r\n                <div class=\"block-gradient py-2 px-2 border-round-xl lessons-info\">\r\n                    <div class=\"flex md:justify-content-between\">\r\n                        <h5 class=\"m-0 text-primary font-bold\"><span>Lessons Summary</span></h5>\r\n                        <!-- <div class=\"gray-border-button\" (click)=\"showBilling = true\">SEE ALL</div> -->\r\n                    </div>\r\n    \r\n                    <div class=\"lessons-info-header m-t-15\">\r\n                        <div>\r\n                            <span class=\"text-xs font-semibold\">HOURS PER LEVEL</span>\r\n                            <div class=\"lessons-stats font-xs\">\r\n                                <div>A1</div>\r\n                                <strong>{{getHoursOfLevel(lessonHoursForEachLevel.A1)}}</strong>\r\n                            </div>\r\n                            <div class=\"lessons-stats font-xs\">\r\n                                <div>A2</div>\r\n                                <strong>{{getHoursOfLevel(lessonHoursForEachLevel.A2)}}</strong>\r\n                            </div>\r\n                            <div class=\"lessons-stats font-xs\">\r\n                                <div>B1</div>\r\n                                <strong>{{getHoursOfLevel(lessonHoursForEachLevel.B1)}}</strong>\r\n                            </div>\r\n                            <div class=\"lessons-stats font-xs\">\r\n                                <div>B2</div>\r\n                                <strong>{{getHoursOfLevel(lessonHoursForEachLevel.B2)}}</strong>\r\n                            </div>\r\n                            <div class=\"lessons-stats font-xs\">\r\n                                <div>C1</div>\r\n                                <strong>{{getHoursOfLevel(lessonHoursForEachLevel.C1)}}</strong>\r\n                            </div>\r\n                            <div class=\"lessons-stats font-xs\">\r\n                                <div>C2</div>\r\n                                <strong>{{getHoursOfLevel(lessonHoursForEachLevel.C2)}}</strong>\r\n                            </div>\r\n                            <div class=\"lessons-stats font-xs\">\r\n                                <div>BS</div>\r\n                                <strong>{{getHoursOfLevel(lessonHoursForEachLevel.BS)}}</strong>\r\n                            </div>\r\n                        </div>\r\n    \r\n                        <div style=\"position: relative;\">\r\n    \r\n    \r\n                            <div *ngIf=\"!showUserSection\">\r\n                                <span class=\"text-xs font-semibold\">MY PACKAGE</span>\r\n                                <div class=\"lessons-stats w-100\">\r\n                                    <div class=\"text-xs\">Hours Left</div>\r\n                                    <label class=\"ml-2 font-xs\">{{hoursLeft}}</label>\r\n                                </div>\r\n                                <div class=\"lessons-stats w-100\">\r\n                                    <div class=\"text-xs\">Total Hours</div>\r\n                                    <label class=\"ml-2 font-xs\">{{getTotalHours()}}</label>\r\n                                </div>\r\n                                <div class=\"lessons-stats w-100\">\r\n                                    <div class=\"text-xs\">Total Packages</div>\r\n                                    <label class=\"ml-2 font-xs\">{{classroomPackages.length}}</label>\r\n                                </div>\r\n                            </div>\r\n    \r\n                            <div *ngIf=\"showUserSection\" style=\"position:absolute; bottom:0\">\r\n                                <span class=\"lessons-info-header-text\">ABSENCES</span>\r\n                                <div class=\"lessons-stats w-100\">\r\n                                    <label>{{userToShow.absences}} Lessons</label>\r\n                                </div>\r\n                            </div>\r\n                            \r\n                            <div class=\"mt-3\" *ngIf=\"!showUserSection\">\r\n                                <span class=\"text-xs font-semibold\">LESSONS HISTORY</span>\r\n                                <div class=\"lessons-stats w-100\">\r\n                                    <div class=\"text-xs\">Completed</div>\r\n                                    <label class=\"ml-2 font-xs\">{{getHoursOfLevel(lessonHoursForEachStatus.Completed)}}</label>\r\n                                </div>\r\n                                <div class=\"lessons-stats w-100\">\r\n                                    <div class=\"text-xs\">Canceled</div>\r\n                                    <label class=\"ml-2 font-xs\">{{getHoursOfLevel(lessonHoursForEachStatus.Canceled)}}</label>\r\n                                </div>\r\n                                <div class=\"lessons-stats w-100\">\r\n                                    <div class=\"text-xs\">No Show</div>\r\n                                    <label class=\"ml-2 font-xs\">{{getHoursOfLevel(lessonHoursForEachStatus['No Show'])}}</label>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        \r\n                        <div style=\"position: relative;\">\r\n    \r\n    \r\n    \r\n                        </div>\r\n    \r\n                        <!-- <div style=\"position: relative;\">\r\n                                <span class=\"text-xs\">MY PACKAGE</span>\r\n                                \r\n                                <div class=\"lessons-stats w-100\">\r\n                                    <div>Completed</div>\r\n                                    <strong>{{getHoursOfLevel(lessonHoursForEachStatus.Completed)}}</strong>\r\n                                </div>\r\n                                <div class=\"hours\">\r\n                                    <strong>{{getTotalHours()}}</strong>\r\n                                </div>\r\n                                <div *ngIf=\"!showUserSection\" style=\"position:absolute; bottom:0\">\r\n                                    <span class=\"lessons-info-header-text\">PACKAGES</span>\r\n                                    <div class=\"lessons-stats w-100\">\r\n                                        <strong>{{classroomPackages.length}}</strong>\r\n                                    </div>\r\n                                    <div class=\"lessons-stats w-100\">\r\n                                        <span style=\"color:white\">_</span>\r\n                                    </div>\r\n                                    <div class=\"lessons-stats w-100\">\r\n                                        <span style=\"color:white\">_</span>\r\n                                    </div>\r\n                                </div>\r\n                            </div> -->\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        \r\n        <div class=\"no-padding-mobile col-12 flex w-full flex-column md:flex-row\">\r\n            <div class=\"no-padding-mobile col-12 md:col-6\">\r\n                <div class=\"block-gradient py-2 px-2 border-round-xl\">\r\n                    <h5 class=\"m-0 text-primary font-bold\"><span>Lesson Ratings</span></h5>\r\n                    <ng-container *ngIf=\"classroom\">\r\n                        <div class=\"w-full overflow-x-hidden\">\r\n                            <p-scrollPanel #scrollPanel [style]=\"{width: '100%', height: '320px'}\" styleClass=\"custombar1\">\r\n                                <app-class-ratings *ngIf=\"classroom\" [classroom]=\"classroom\"></app-class-ratings>\r\n                            </p-scrollPanel>\r\n                        </div>\r\n                    </ng-container>\r\n                </div>\r\n            </div>\r\n        \r\n            <div class=\"no-padding-mobile col-12 md:col-6\">\r\n                <div class=\"block-gradient py-2 px-2 border-round-xl\">\r\n                    <div class=\"w-full overflow-x-hidden\">\r\n                    <div class=\"flex justify-content-between\">\r\n                    <h5 class=\"m-0 text-primary font-bold\"><span>Reports</span></h5>\r\n                    <!-- <button pbutton=\"\" type=\"button\"\r\n                        class=\"p-element p-button-rounded button-outlined p-button-sm p-button p-component py-1 btn-modify\">\r\n                        <div class=\"flex align-items-center gap-1\">\r\n                            <span class=\"font-sm flex align-items-center gap-2\">Create Report \r\n                                <img src=\"/assets/icons/classroom/chart-report-icon.svg\" width=\"12\" /></span>\r\n                        </div>\r\n                    </button> -->\r\n                    </div>\r\n                    <p-scrollPanel #scrollPanel [style]=\"{width: '100%', height: '320px'}\" styleClass=\"custombar1\">\r\n                    <app-class-reports *ngIf=\"classroom\" [classroom]=\"classroom\"></app-class-reports>\r\n                    </p-scrollPanel>\r\n                </div>\r\n            </div>\r\n            </div>\r\n        </div>\r\n        \r\n    </ng-container>\r\n</ng-template>\r\n\r\n\r\n\r\n<ng-template #classInfoStudentTemplate let-student=\"student\" let-classroom=\"classroom\">\r\n    <div class=\"grid\">\r\n        <div class=\"col-12 md:col-6\">\r\n            <div class=\"col-12 md:col-12\">\r\n                <div class=\"block-gradient py-2 px-2 border-round-xl\">\r\n                    <h5 class=\"pb-2 text-primary font-bold\"><span>Personal Info</span></h5>\r\n                    <ul class=\"list-none p-0 m-0\">\r\n                        <li [style.opacity]=\"student.status=='Inactive'? '0.5': '1'\"\r\n                            class=\"flex flex-column md:flex-row md:align-items-center md:justify-content-between mb-4\">\r\n                            <div class=\"flex max-w-19rem align-items-start\">\r\n                                \r\n                                <div class=\"avatar-circle w-5rem\">\r\n                                    <img [src]=\"getUserPhoto(student)\"\r\n                                        class=\"border-circle\">\r\n                                    </div>\r\n                                    \r\n                        <div class=\"ml-2 personal-info-details font-xs w-full\">\r\n                            <div class=\" flex\">\r\n                            <div class=\"item col w-10rem\">\r\n                                First Name<br>\r\n                                <strong>{{generalService.replaceImportedWithDash(student.firstName)}}</strong>\r\n                            </div>\r\n                            <div class=\"item col\">\r\n                                Last Name<br>\r\n                                <strong>{{generalService.replaceImportedWithDash(student.lastName)}}</strong>\r\n                            </div>\r\n                            </div>\r\n                            <div class=\"w-full flex\">\r\n                            <div  class=\"item col\">\r\n                                Birthday<br>\r\n                                <strong>{{(student.dob | date: 'd/M/y')}}</strong>\r\n                            </div>\r\n                            <div class=\"item col\">\r\n                                Location<br>\r\n                                <strong>{{generalService.replaceImportedWithDash(student.origin)}} {{generalService.replaceImportedWithDash(student.residence)}}</strong>\r\n                            </div>\r\n                            </div>\r\n                            <div class=\"w-full flex\" *ngIf=\"student.skype\">\r\n                            <div class=\"item col\">\r\n                                Skype<br>\r\n                                <strong><a\r\n                                    [href]=\"generalService.getSafeSkypeUrl(generalService.replaceImportedWithDash(student.skype))\">{{generalService.replaceImportedWithDash(student.skype)}}</a></strong>\r\n                            </div>\r\n                            </div>\r\n                            <div class=\"w-full flex\">\r\n                            <div class=\"item col\">\r\n                                Email<br>\r\n                                <strong>{{generalService.replaceImportedWithDash(student.username)}}</strong>\r\n                            </div>\r\n                            </div>\r\n                            \r\n                            <div class=\"w-full flex\">\r\n                                <div class=\"item col\">\r\n                                    Timezone<br>\r\n                                    <strong>\r\n                                        {{generalService.convertTimezoneValueToText(student.timezone).text}} - \r\n                                {{generalService.getUserLocalTime(generalService.convertTimezoneValueToText(student.timezone).text, false)}}\r\n                                    </strong>\r\n                                </div>\r\n                                </div>\r\n                        </div>\r\n                            </div>\r\n                        </li>\r\n                    </ul>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    \r\n        <div class=\"col-12 md:col-6\">\r\n    \r\n    \r\n            <div class=\"col-12 md:col-12 flex h-full\">\r\n                <div class=\"block-gradient py-2 px-2 border-round-xl lessons-info\">\r\n                    <div class=\"flex justify-content-between\">\r\n                        <h5 class=\"m-0 text-primary font-bold\"><span>Lessons Summary</span></h5>\r\n                        <!-- <div class=\"gray-border-button\" (click)=\"showBilling = true\">SEE ALL</div> -->\r\n                    </div>\r\n    \r\n                    \r\n                    <div class=\"lessons-info-header m-t-15\">\r\n                        <div>\r\n                            <span class=\"text-xs font-semibold\">HOURS PER LEVEL</span>\r\n                            <div class=\"lessons-stats font-xs\">\r\n                                <div>A1</div>\r\n                                <strong>{{getHoursOfLevel(lessonHoursForEachLevel.A1)}}</strong>\r\n                            </div>\r\n                            <div class=\"lessons-stats font-xs\">\r\n                                <div>A2</div>\r\n                                <strong>{{getHoursOfLevel(lessonHoursForEachLevel.A2)}}</strong>\r\n                            </div>\r\n                            <div class=\"lessons-stats font-xs\">\r\n                                <div>B1</div>\r\n                                <strong>{{getHoursOfLevel(lessonHoursForEachLevel.B1)}}</strong>\r\n                            </div>\r\n                            <div class=\"lessons-stats font-xs\">\r\n                                <div>B2</div>\r\n                                <strong>{{getHoursOfLevel(lessonHoursForEachLevel.B2)}}</strong>\r\n                            </div>\r\n                            <div class=\"lessons-stats font-xs\">\r\n                                <div>C1</div>\r\n                                <strong>{{getHoursOfLevel(lessonHoursForEachLevel.C1)}}</strong>\r\n                            </div>\r\n                            <div class=\"lessons-stats font-xs\">\r\n                                <div>C2</div>\r\n                                <strong>{{getHoursOfLevel(lessonHoursForEachLevel.C2)}}</strong>\r\n                            </div>\r\n                            <div class=\"lessons-stats font-xs\">\r\n                                <div>BS</div>\r\n                                <strong>{{getHoursOfLevel(lessonHoursForEachLevel.BS)}}</strong>\r\n                            </div>\r\n                        </div>\r\n    \r\n                        <div style=\"position: relative;\">\r\n    \r\n    \r\n                            <div *ngIf=\"!showUserSection\">\r\n                                <span class=\"text-xs font-semibold\">MY PACKAGE</span>\r\n                                <div class=\"lessons-stats w-100\">\r\n                                    <div class=\"text-xs\">Hours Left</div>\r\n                                    <label class=\"ml-2 font-xs\">{{hoursLeft}}</label>\r\n                                </div>\r\n                                <div class=\"lessons-stats w-100\">\r\n                                    <div class=\"text-xs\">Total Hours</div>\r\n                                    <label class=\"ml-2 font-xs\">{{getTotalHours()}}</label>\r\n                                </div>\r\n                                <div class=\"lessons-stats w-100\">\r\n                                    <div class=\"text-xs\">Total Packages</div>\r\n                                    <label class=\"ml-2 font-xs\">{{classroomPackages.length}}</label>\r\n                                </div>\r\n                            </div>\r\n    \r\n                            <div *ngIf=\"showUserSection\" style=\"position:absolute; bottom:0\">\r\n                                <span class=\"lessons-info-header-text\">ABSENCES</span>\r\n                                <div class=\"lessons-stats w-100\">\r\n                                    <label>{{userToShow.absences}} Lessons</label>\r\n                                </div>\r\n                            </div>\r\n                            \r\n                            <div class=\"mt-3\" *ngIf=\"!showUserSection\">\r\n                                <span class=\"text-xs font-semibold\">LESSONS HISTORY</span>\r\n                                <div class=\"lessons-stats w-100\">\r\n                                    <div class=\"text-xs\">Completed</div>\r\n                                    <label class=\"ml-2 font-xs\">{{getHoursOfLevel(lessonHoursForEachStatus.Completed)}}</label>\r\n                                </div>\r\n                                <div class=\"lessons-stats w-100\">\r\n                                    <div class=\"text-xs\">Canceled</div>\r\n                                    <label class=\"ml-2 font-xs\">{{getHoursOfLevel(lessonHoursForEachStatus.Canceled)}}</label>\r\n                                </div>\r\n                                <div class=\"lessons-stats w-100\">\r\n                                    <div class=\"text-xs\">No Show</div>\r\n                                    <label class=\"ml-2 font-xs\">{{getHoursOfLevel(lessonHoursForEachStatus['No Show'])}}</label>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        \r\n                        <div style=\"position: relative;\">\r\n    \r\n    \r\n    \r\n                        </div>\r\n    \r\n                        <!-- <div style=\"position: relative;\">\r\n                                <span class=\"text-xs\">MY PACKAGE</span>\r\n                                \r\n                                <div class=\"lessons-stats w-100\">\r\n                                    <div>Completed</div>\r\n                                    <strong>{{getHoursOfLevel(lessonHoursForEachStatus.Completed)}}</strong>\r\n                                </div>\r\n                                <div class=\"hours\">\r\n                                    <strong>{{getTotalHours()}}</strong>\r\n                                </div>\r\n                                <div *ngIf=\"!showUserSection\" style=\"position:absolute; bottom:0\">\r\n                                    <span class=\"lessons-info-header-text\">PACKAGES</span>\r\n                                    <div class=\"lessons-stats w-100\">\r\n                                        <strong>{{classroomPackages.length}}</strong>\r\n                                    </div>\r\n                                    <div class=\"lessons-stats w-100\">\r\n                                        <span style=\"color:white\">_</span>\r\n                                    </div>\r\n                                    <div class=\"lessons-stats w-100\">\r\n                                        <span style=\"color:white\">_</span>\r\n                                    </div>\r\n                                </div>\r\n                            </div> -->\r\n                    </div>\r\n                    \r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        \r\n        <div class=\"col-12 flex w-full\">\r\n            <div class=\"col-12 md:col-6\">\r\n                <div class=\"block-gradient py-2 px-2 border-round-xl\">\r\n                    <h5 class=\"m-0 text-primary font-bold\"><span>Lesson Ratings</span></h5>\r\n                    <p-scrollPanel #scrollPanel [style]=\"{width: '100%', height: '320px'}\" styleClass=\"custombar1\">\r\n                    <app-class-ratings *ngIf=\"classroom\" [classroom]=\"classroom\"></app-class-ratings>\r\n                    </p-scrollPanel>\r\n                </div>\r\n            </div>\r\n        \r\n            <div class=\"col-12 md:col-6\">\r\n                <div class=\"block-gradient py-2 px-2 border-round-xl\">\r\n                    <h5 class=\"m-0 text-primary font-bold\"><span>Reports</span></h5>\r\n                    <p-scrollPanel #scrollPanel [style]=\"{width: '100%', height: '320px'}\" styleClass=\"custombar1\">\r\n                    <app-class-reports *ngIf=\"classroom\" [classroom]=\"classroom\"></app-class-reports>\r\n                    </p-scrollPanel>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        \r\n    </div>\r\n</ng-template>\r\n\r\n<ng-container *ngIf=\"classroom\">\r\n    <div class=\"overflow-auto col-12\">\r\n    <app-lessons-history [classroom]=\"classroom\" [wrapperScrollHeight]=\"'400px'\"></app-lessons-history>\r\n</div>\r\n</ng-container>\r\n</div>\r\n<!-- ends first line if -->\r\n\r\n<div *ngIf=\"showBilling\" class=\"section\">\r\n    <div class=\"active-package\">\r\n        <div class=\"active-package-header\">\r\n            <div class=\"active-package-title\">\r\n                <div class=\"back-button pointer hvr-grow\" (click)=\"showBilling=false\">\r\n                    <img src=\"/assets/icons/back-main-color.svg\">\r\n                </div>\r\n                <div style=\"margin-left:15px;\">\r\n                    Info\r\n                </div>\r\n            </div>\r\n            <!-- <div class=\"main-color-button\" (click)=\"showLevel = !showLevel\">Change Level</div> -->\r\n            <!-- <div *ngIf=\"showLevel\" class=\"showLevel\">\r\n                <p-dropdown [(ngModel)]=\"changeLevelChoosen\" placeholder=\"Choose Level\" class=\"dropdown\"\r\n                    [options]=\"['A1', 'A2', 'B1', 'B2', 'C1', 'C2']\" (onChange)=\"onChooseChangeLevel($event)\">\r\n                </p-dropdown>\r\n            </div> -->\r\n        </div>\r\n        <app-teacher-billing [classroom]=\"classroom\"></app-teacher-billing>\r\n    </div>\r\n</div>\r\n\r\n<div class=\"modal no-visibility\" #ratingDetails>\r\n    <div class=\"popup-title\">\r\n        <div>Lesson Rating</div>\r\n        <img (click)=\"closeRatingDetails()\" src=\"/assets/icons/close.png\" class=\"close-img close-img-abs hvr-glow\">\r\n    </div>\r\n    <app-lesson-rating *ngIf=\"showRating\" [users]=\"classroom.users\" [lesson]=\"lessonRating.lesson\"\r\n        [lessonRatings]=\"lessonRating.lessonBreakdown\" [lessonUserRatings]=\"lessonRating.userRatings\" [mode]=\"mode\">\r\n    </app-lesson-rating>\r\n</div>"], "mappings": "AAAA,SAAoBA,YAAY,QAAmE,eAAe;AAElH,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,IAAI,QAAQ,gBAAgB;AACrC,SAAoBC,KAAK,QAAQ,qCAAqC;AAGtE,SAAeC,QAAQ,QAAoB,gCAAgC;AAO3E,SAASC,OAAO,QAAQ,SAAS;AACjC,OAAO,KAAKC,IAAI,MAAM,iCAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICNnCC,EAAA,CAAAC,kBAAA,GACe;;;;;IAMXD,EAAA,CAAAC,kBAAA,GAGe;;;;;IALrBD,EAAA,CAAAE,uBAAA,GAAsF;IACpFF,EAAA,CAAAG,cAAA,qBAAyG;IACrGH,EAAA,CAAAI,UAAA,IAAAC,oFAAA,2BAEgC;IAEpCL,EAAA,CAAAM,YAAA,EAAa;;;;;;;;IALDN,EAAA,CAAAO,SAAA,EAA4F;IAA5FP,EAAA,CAAAQ,UAAA,WAAAC,kBAAA,CAAAC,SAAA,+BAAAD,kBAAA,CAAAC,SAAA,CAA4F;IACrFV,EAAA,CAAAO,SAAA,EAA4C;IAAAP,EAA5C,CAAAQ,UAAA,qBAAAG,2BAAA,CAA4C,4BAAAX,EAAA,CAAAY,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,SAAA,EAAAN,kBAAA,EAE/B;;;;;;IAfxCT,EAAA,CAAAE,uBAAA,GAAkD;IAC9CF,EAAA,CAAAG,cAAA,uBAEqE;IAAjEH,EAAA,CAAAgB,UAAA,+BAAAC,8FAAAC,MAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAC,GAAA;MAAA,MAAAN,MAAA,GAAAd,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAqBR,MAAA,CAAAS,mBAAA,CAAAL,MAAA,CAA2B;IAAA,EAAC;IAEjDlB,EADF,CAAAG,cAAA,qBAA+B,cACkC;IAC/DH,EAAA,CAAAI,UAAA,IAAAoB,qEAAA,2BAA8F;IAIhGxB,EADE,CAAAM,YAAA,EAAM,EACK;IACbN,EAAA,CAAAI,UAAA,IAAAqB,qEAAA,2BAAsF;IAQxFzB,EAAA,CAAAM,YAAA,EAAY;;;;;;;;IAlBQN,EAAA,CAAAO,SAAA,EAAqD;IACrEP,EADgB,CAAAQ,UAAA,eAAAM,MAAA,CAAAC,SAAA,CAAAW,iBAAA,CAAAC,MAAA,KAAqD,gBAAAC,UAAA,CAAAC,WAAA,CAClC;IAIpB7B,EAAA,CAAAO,SAAA,GAA4C;IAAAP,EAA5C,CAAAQ,UAAA,qBAAAsB,2BAAA,CAA4C,4BAAA9B,EAAA,CAAA+B,eAAA,IAAAC,GAAA,EAAAlB,MAAA,CAAAC,SAAA,EAAiC;IAKpDf,EAAA,CAAAO,SAAA,EAAgC;IAAhCP,EAAA,CAAAQ,UAAA,YAAAM,MAAA,CAAAC,SAAA,CAAAW,iBAAA,CAAgC;;;;;IAZpF1B,EAAA,CAAAG,cAAA,cAAgD;IAC5CH,EAAA,CAAAI,UAAA,IAAA6B,sDAAA,2BAAkD;IAqBlDjC,EAAA,CAAAM,YAAA,EAAM;;;;IArBSN,EAAA,CAAAO,SAAA,EAAiC;IAAjCP,EAAA,CAAAQ,UAAA,SAAAM,MAAA,CAAAC,SAAA,CAAAW,iBAAA,CAAiC;;;;;IAyBxC1B,EAAA,CAAAC,kBAAA,GACe;;;;;IAHvBD,EAAA,CAAAE,uBAAA,GAAsC;IAClCF,EAAA,CAAAG,cAAA,cAAsC;IAClCH,EAAA,CAAAI,UAAA,IAAA8B,+DAAA,2BAA8F;IAElGlC,EAAA,CAAAM,YAAA,EAAM;;;;;;;IAFaN,EAAA,CAAAO,SAAA,GAA4C;IAAAP,EAA5C,CAAAQ,UAAA,qBAAAsB,2BAAA,CAA4C,4BAAA9B,EAAA,CAAA+B,eAAA,IAAAC,GAAA,EAAAlB,MAAA,CAAAC,SAAA,EAAiC;;;;;IAOhGf,EAFR,CAAAG,cAAA,UAAgD,cACd,cACM;IAC5BH,EAAA,CAAAmC,MAAA,yBACA;IAAAnC,EAAA,CAAAG,cAAA,cAA4C;IAAAH,EAAA,CAAAmC,MAAA,wBAAiB;IAGrEnC,EAHqE,CAAAM,YAAA,EAAM,EACjE,EAEJ;IAGFN,EADJ,CAAAG,cAAA,cAA8B,cACM;IAC5BH,EAAA,CAAAmC,MAAA,kCACA;IAAAnC,EAAA,CAAAG,cAAA,cAA4C;IAAAH,EAAA,CAAAmC,MAAA,yBAAiB;IACjEnC,EADiE,CAAAM,YAAA,EAAM,EACjE;IACNN,EAAA,CAAAoC,SAAA,kCAAyE;IAEjFpC,EADI,CAAAM,YAAA,EAAM,EACJ;;;;IAF0BN,EAAA,CAAAO,SAAA,IAAuB;IAAvBP,EAAA,CAAAQ,UAAA,cAAAM,MAAA,CAAAC,SAAA,CAAuB;;;;;IA8BvCf,EAAA,CAAAG,cAAA,cAC0D;IACtDH,EAAA,CAAAmC,MAAA,kBACJ;IAAAnC,EAAA,CAAAM,YAAA,EAAM;;;;IAFFN,EAAA,CAAAqC,WAAA,qBAAAvB,MAAA,CAAAwB,cAAA,cAAqD;;;;;IAGzDtC,EAAA,CAAAG,cAAA,cACwD;IACpDH,EAAA,CAAAmC,MAAA,gBACJ;IAAAnC,EAAA,CAAAM,YAAA,EAAM;;;;IAFFN,EAAA,CAAAqC,WAAA,qBAAAvB,MAAA,CAAAwB,cAAA,YAAmD;;;;;IAGvDtC,EAAA,CAAAG,cAAA,cAC2D;IACvDH,EAAA,CAAAmC,MAAA,mBACJ;IAAAnC,EAAA,CAAAM,YAAA,EAAM;;;;IAFFN,EAAA,CAAAqC,WAAA,qBAAAvB,MAAA,CAAAwB,cAAA,eAAsD;;;;;IAG1DtC,EAAA,CAAAG,cAAA,cACyD;IACrDH,EAAA,CAAAmC,MAAA,iBACJ;IAAAnC,EAAA,CAAAM,YAAA,EAAM;;;;IAFFN,EAAA,CAAAqC,WAAA,qBAAAvB,MAAA,CAAAwB,cAAA,aAAoD;;;;;IAGxDtC,EAAA,CAAAG,cAAA,cACwD;IACpDH,EAAA,CAAAmC,MAAA,gBACJ;IAAAnC,EAAA,CAAAM,YAAA,EAAM;;;;IAFFN,EAAA,CAAAqC,WAAA,qBAAAvB,MAAA,CAAAwB,cAAA,YAAmD;;;;;IAGvDtC,EAAA,CAAAG,cAAA,cACwD;IACpDH,EAAA,CAAAmC,MAAA,gBACJ;IAAAnC,EAAA,CAAAM,YAAA,EAAM;;;;IAFFN,EAAA,CAAAqC,WAAA,qBAAAvB,MAAA,CAAAwB,cAAA,YAAmD;;;;;IAGvDtC,EAAA,CAAAG,cAAA,cACyD;IACrDH,EAAA,CAAAmC,MAAA,iBACJ;IAAAnC,EAAA,CAAAM,YAAA,EAAM;;;;IAFFN,EAAA,CAAAqC,WAAA,qBAAAvB,MAAA,CAAAwB,cAAA,aAAoD;;;;;IAGxDtC,EAAA,CAAAG,cAAA,cACqD;IACjDH,EAAA,CAAAmC,MAAA,aACJ;IAAAnC,EAAA,CAAAM,YAAA,EAAM;;;;IAFFN,EAAA,CAAAqC,WAAA,qBAAAvB,MAAA,CAAAwB,cAAA,SAAgD;;;;;;IAnDxDtC,EAHZ,CAAAG,cAAA,cAA2E,cAC1C,cACa,UAC7B;IACDH,EAAA,CAAAmC,MAAA,iBACJ;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACNN,EAAA,CAAAG,cAAA,cAA0D;IAA1BH,EAAA,CAAAgB,UAAA,mBAAAuB,6DAAA;MAAAvC,EAAA,CAAAmB,aAAA,CAAAqB,GAAA;MAAA,MAAA1B,MAAA,GAAAd,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASR,MAAA,CAAA2B,aAAA,EAAe;IAAA,EAAC;IAACzC,EAAA,CAAAmC,MAAA,cAAO;IAErEnC,EAFqE,CAAAM,YAAA,EAAM,EAErE;IAGEN,EAFR,CAAAG,cAAA,cAAoB,cACiB,aACrB;IAAAH,EAAA,CAAAmC,MAAA,IAAuE;;IAAAnC,EAAA,CAAAM,YAAA,EAAS;IACxFN,EAAA,CAAAoC,SAAA,WAEM;IAEFpC,EADJ,CAAAG,cAAA,eAAkC,eAC4B;IAA7BH,EAAA,CAAAgB,UAAA,mBAAA0B,8DAAA;MAAA1C,EAAA,CAAAmB,aAAA,CAAAqB,GAAA;MAAA,MAAA1B,MAAA,GAAAd,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASR,MAAA,CAAA6B,UAAA,CAAA7B,MAAA,CAAA8B,MAAA,CAAkB;IAAA,EAAC;IACrD5C,EAAA,CAAAmC,MAAA,cACJ;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACNN,EAAA,CAAAG,cAAA,eAAuD;IACnDH,EAAA,CAAAmC,MAAA,cACJ;IAERnC,EAFQ,CAAAM,YAAA,EAAM,EACJ,EACJ;IACNN,EAAA,CAAAG,cAAA,eAA+B;IA6B3BH,EA5BA,CAAAI,UAAA,KAAAyC,8CAAA,kBAC0D,KAAAC,8CAAA,kBAIF,KAAAC,8CAAA,kBAIG,KAAAC,8CAAA,kBAIF,KAAAC,8CAAA,kBAID,KAAAC,8CAAA,kBAIA,KAAAC,8CAAA,kBAIC,KAAAC,8CAAA,kBAIJ;IAI7DpD,EADI,CAAAM,YAAA,EAAM,EACJ;IACNN,EAAA,CAAAoC,SAAA,eAAkC;IACtCpC,EAAA,CAAAM,YAAA,EAAM;IAGEN,EAFR,CAAAG,cAAA,eAA6B,eACa,WAC7B;IACDH,EAAA,CAAAmC,MAAA,kBACJ;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACNN,EAAA,CAAAG,cAAA,eAA0D;IAA1BH,EAAA,CAAAgB,UAAA,mBAAAqC,8DAAA;MAAArD,EAAA,CAAAmB,aAAA,CAAAqB,GAAA;MAAA,MAAA1B,MAAA,GAAAd,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASR,MAAA,CAAAwC,aAAA,EAAe;IAAA,EAAC;IAACtD,EAAA,CAAAmC,MAAA,eAAO;IACrEnC,EADqE,CAAAM,YAAA,EAAM,EACrE;IAGEN,EAFR,CAAAG,cAAA,eAAoB,eACiB,cACrB;IAAAH,EAAA,CAAAmC,MAAA,0BAAkB;IAAAnC,EAAA,CAAAM,YAAA,EAAS;IACnCN,EAAA,CAAAG,cAAA,WAAK;IACDH,EAAA,CAAAmC,MAAA,YACJ;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IAEFN,EADJ,CAAAG,cAAA,eAAkC,eACD;IACzBH,EAAA,CAAAmC,MAAA,cACJ;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACNN,EAAA,CAAAG,cAAA,eAAuD;IACnDH,EAAA,CAAAmC,MAAA,cACJ;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACNN,EAAA,CAAAG,cAAA,eAAuD;IACnDH,EAAA,CAAAmC,MAAA,kBACJ;IAERnC,EAFQ,CAAAM,YAAA,EAAM,EACJ,EACJ;IACNN,EAAA,CAAAoC,SAAA,eAEM;IACVpC,EAAA,CAAAM,YAAA,EAAM;IACNN,EAAA,CAAAoC,SAAA,eAAqC;IAE7CpC,EADI,CAAAM,YAAA,EAAM,EACJ;;;;IAjFkBN,EAAA,CAAAO,SAAA,IAAuE;IAAvEP,EAAA,CAAAuD,iBAAA,CAAAvD,EAAA,CAAAwD,WAAA,QAAA1C,MAAA,CAAA2C,YAAA,CAAAC,eAAA,kBAAA5C,MAAA,CAAA2C,YAAA,CAAAC,eAAA,CAAAC,WAAA,qBAAuE;IAczE3D,EAAA,CAAAO,SAAA,GAAiD;IAAjDP,EAAA,CAAAQ,UAAA,UAAAM,MAAA,CAAA2C,YAAA,CAAAC,eAAA,kBAAA5C,MAAA,CAAA2C,YAAA,CAAAC,eAAA,CAAAE,SAAA,MAAiD;IAIjD5D,EAAA,CAAAO,SAAA,EAA+C;IAA/CP,EAAA,CAAAQ,UAAA,UAAAM,MAAA,CAAA2C,YAAA,CAAAC,eAAA,kBAAA5C,MAAA,CAAA2C,YAAA,CAAAC,eAAA,CAAAG,OAAA,MAA+C;IAI/C7D,EAAA,CAAAO,SAAA,EAAkD;IAAlDP,EAAA,CAAAQ,UAAA,UAAAM,MAAA,CAAA2C,YAAA,CAAAC,eAAA,kBAAA5C,MAAA,CAAA2C,YAAA,CAAAC,eAAA,CAAAI,UAAA,MAAkD;IAIlD9D,EAAA,CAAAO,SAAA,EAAgD;IAAhDP,EAAA,CAAAQ,UAAA,UAAAM,MAAA,CAAA2C,YAAA,CAAAC,eAAA,kBAAA5C,MAAA,CAAA2C,YAAA,CAAAC,eAAA,CAAAK,QAAA,MAAgD;IAIhD/D,EAAA,CAAAO,SAAA,EAA+C;IAA/CP,EAAA,CAAAQ,UAAA,UAAAM,MAAA,CAAA2C,YAAA,CAAAC,eAAA,kBAAA5C,MAAA,CAAA2C,YAAA,CAAAC,eAAA,CAAAM,OAAA,MAA+C;IAI/ChE,EAAA,CAAAO,SAAA,EAA+C;IAA/CP,EAAA,CAAAQ,UAAA,UAAAM,MAAA,CAAA2C,YAAA,CAAAC,eAAA,kBAAA5C,MAAA,CAAA2C,YAAA,CAAAC,eAAA,CAAAO,OAAA,MAA+C;IAI/CjE,EAAA,CAAAO,SAAA,EAAgD;IAAhDP,EAAA,CAAAQ,UAAA,UAAAM,MAAA,CAAA2C,YAAA,CAAAC,eAAA,kBAAA5C,MAAA,CAAA2C,YAAA,CAAAC,eAAA,CAAAQ,QAAA,MAAgD;IAIhDlE,EAAA,CAAAO,SAAA,EAA4C;IAA5CP,EAAA,CAAAQ,UAAA,UAAAM,MAAA,CAAA2C,YAAA,CAAAC,eAAA,kBAAA5C,MAAA,CAAA2C,YAAA,CAAAC,eAAA,CAAAS,IAAA,MAA4C;;;;;IAiGdnE,EAAA,CAAAG,cAAA,WAA+B;IAAAH,EAAA,CAAAmC,MAAA,cAAO;IAAAnC,EAAA,CAAAM,YAAA,EAAO;;;;;;IAArFN,EADJ,CAAAG,cAAA,cAAiF,aACrC;IAAAH,EAAA,CAAAI,UAAA,IAAAgE,4DAAA,mBAA+B;IAAcpE,EAAA,CAAAM,YAAA,EAAK;IAK9EN,EAJZ,CAAAG,cAAA,aAA8B,aACqE,cACzE,cACa,cAEG;IADeH,EAAA,CAAAgB,UAAA,mBAAAqD,2EAAAnD,MAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAmD,IAAA;MAAA,MAAAxD,MAAA,GAAAd,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASR,MAAA,CAAAyD,cAAA,CAAAC,oBAAA,CAAAtD,MAAA,CAA2C;IAAA,EAAC;IAElGlB,EAFA,CAAAM,YAAA,EAC8B,EACxB;IACYN,EAAlB,CAAAG,cAAA,cAAkB,eAA2C;IAAAH,EAAA,CAAAmC,MAAA,IACiB;IAAAnC,EAAA,CAAAM,YAAA,EAAO;IACjFN,EAAA,CAAAG,cAAA,eAAkF;IAC9EH,EAAA,CAAAoC,SAAA,eAA0D;IAC1DpC,EAAA,CAAAmC,MAAA,IACJ;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACNN,EAAA,CAAAG,cAAA,eAA+D;IAC3DH,EAAA,CAAAoC,SAAA,eAA2D;IACnDpC,EAAR,CAAAG,cAAA,cAAQ,aAAiI;IACrIH,EAAA,CAAAmC,MAAA,IAAmE;IAGnFnC,EAHmF,CAAAM,YAAA,EAAI,EAAS,EAClF,EACJ,EACJ;IAEFN,EADJ,CAAAG,cAAA,eAA8E,eAC+C;IAAAH,EAAA,CAAAmC,MAAA,oBAAY;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IAC3IN,EAAA,CAAAG,cAAA,eAAoJ;IAAAH,EAAA,CAAAmC,MAAA,oBAAY;IAIhLnC,EAJgL,CAAAM,YAAA,EAAM,EACpK,EACL,EACJ,EACH;;;;;IA3B6CN,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAQ,UAAA,UAAAM,MAAA,CAAA2D,eAAA,CAAsB;IAKpDzE,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAAQ,UAAA,QAAAM,MAAA,CAAA4D,YAAA,CAAAC,aAAA,CAAAC,OAAA,GAAA5E,EAAA,CAAA6E,aAAA,CAAuC;IAGiB7E,EAAA,CAAAO,SAAA,GACiB;IADjBP,EAAA,CAAA8E,kBAAA,KAAAhE,MAAA,CAAAyD,cAAA,CAAAQ,uBAAA,CAAAJ,aAAA,CAAAC,OAAA,CAAAlE,SAAA,QAAAI,MAAA,CAAAyD,cAAA,CAAAQ,uBAAA,CAAAJ,aAAA,CAAAC,OAAA,CAAAI,QAAA,MACiB;IAGtEhF,EAAA,CAAAO,SAAA,GACJ;IADIP,EAAA,CAAAiF,kBAAA,MAAAnE,MAAA,CAAAyD,cAAA,CAAAQ,uBAAA,CAAAJ,aAAA,CAAAC,OAAA,CAAAM,SAAA,OACJ;IAGoClF,EAAA,CAAAO,SAAA,GAAwG;IAAxGP,EAAA,CAAAQ,UAAA,SAAAM,MAAA,CAAAyD,cAAA,CAAAY,eAAA,CAAArE,MAAA,CAAAyD,cAAA,CAAAQ,uBAAA,CAAAJ,aAAA,CAAAC,OAAA,CAAAQ,KAAA,IAAApF,EAAA,CAAA6E,aAAA,CAAwG;IACpI7E,EAAA,CAAAO,SAAA,EAAmE;IAAnEP,EAAA,CAAAiF,kBAAA,MAAAnE,MAAA,CAAAyD,cAAA,CAAAQ,uBAAA,CAAAJ,aAAA,CAAAC,OAAA,CAAAQ,KAAA,MAAmE;IAK/CpF,EAAA,CAAAO,SAAA,GAAwF;IAAxFP,EAAA,CAAAQ,UAAA,eAAAR,EAAA,CAAA+B,eAAA,KAAAsD,GAAA,EAAAV,aAAA,CAAAW,EAAA,EAAwF;IACxFtF,EAAA,CAAAO,SAAA,GAAkD;IAACP,EAAnD,CAAAQ,UAAA,YAAAR,EAAA,CAAA+B,eAAA,KAAAwD,GAAA,EAAAzE,MAAA,CAAA0E,IAAA,gBAAkD,eAAAxF,EAAA,CAAA+B,eAAA,KAAA0D,GAAA,EAAA3E,MAAA,CAAA4E,gBAAA,CAAAC,mBAAA,CAAAhB,aAAA,GAAiE;;;;;IAOvH3E,EAAA,CAAAG,cAAA,WAA+B;IAAAH,EAAA,CAAAmC,MAAA,eAAQ;IAAAnC,EAAA,CAAAM,YAAA,EAAO;;;;;;IAuD1EN,EAAA,CAAAG,cAAA,cAAmG;IAArCH,EAAA,CAAAgB,UAAA,mBAAA4E,iFAAA;MAAA5F,EAAA,CAAAmB,aAAA,CAAA0E,IAAA;MAAA,MAAAC,KAAA,GAAA9F,EAAA,CAAAqB,aAAA,GAAA0E,KAAA;MAAA,MAAAjF,MAAA,GAAAd,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASR,MAAA,CAAAkF,mBAAA,CAAAF,KAAA,GAAwB,CAAC,CAAC;IAAA,EAAC;IAAC9F,EAAA,CAAAmC,MAAA,eAAQ;IAAAnC,EAAA,CAAAM,YAAA,EAAM;;;;;IAS7GN,EAAA,CAAAoC,SAAA,WAEO;;;;;IACPpC,EAAA,CAAAG,cAAA,WAAsC;IAClCH,EAAA,CAAAoC,SAAA,iBACmD;IAEvDpC,EAAA,CAAAM,YAAA,EAAO;;;;;;IAhEXN,EALR,CAAAG,cAAA,aAE8F,cACtD,cAEG;IACnCH,EAAA,CAAAoC,SAAA,cAC0B;IAC1BpC,EAAA,CAAAM,YAAA,EAAM;IAMMN,EAJR,CAAAG,cAAA,cAAuD,eAER,cACG,eAClB;IACxBH,EAAA,CAAAmC,MAAA,GAEA;IAEJnC,EAFI,CAAAM,YAAA,EAAO,EACL,EACC;IAEPN,EADA,CAAAG,cAAA,cAAyB,eACM;IAC3BH,EAAA,CAAAmC,MAAA,cAAK;IAAAnC,EAAA,CAAAoC,SAAA,UAAI;IACTpC,EAAA,CAAAG,cAAA,cAAQ;IAAAH,EAAA,CAAAmC,MAAA,IAAyD;IA2BjFnC,EA3BiF,CAAAM,YAAA,EAAS,EACxE,EACA,EACJ,EAwBR;IACNN,EAAA,CAAAG,cAAA,eAA8E;IAE1EH,EAAA,CAAAI,UAAA,KAAA6F,2DAAA,kBAAmG;IAInGjG,EAAA,CAAAoC,SAAA,kBAEuD;IAEvDpC,EAAA,CAAAG,cAAA,eAAsC;IAAjCH,EAAA,CAAAgB,UAAA,mBAAAkF,2EAAA;MAAA,MAAAC,QAAA,GAAAnG,EAAA,CAAAmB,aAAA,CAAAiF,IAAA,EAAAC,SAAA;MAAA,MAAAvF,MAAA,GAAAd,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASR,MAAA,CAAAwF,gBAAA,CAAAH,QAAA,CAAsB;IAAA,EAAC;IAIjCnG,EAHA,CAAAI,UAAA,KAAAmG,4DAAA,mBAAoC,KAAAC,4DAAA,mBAGE;IAQlDxG,EAHQ,CAAAM,YAAA,EAAM,EAEJ,EACL;;;;;IAxEDN,EAAA,CAAAqC,WAAA,YAAA8D,QAAA,CAAAM,MAAA,6BAAqD;IAK5CzG,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAQ,UAAA,QAAAM,MAAA,CAAA4D,YAAA,CAAAyB,QAAA,GAAAnG,EAAA,CAAA6E,aAAA,CAA0B;IASnB7E,EAAA,CAAAO,SAAA,GAEA;IAFAP,EAAA,CAAA8E,kBAAA,MAAAhE,MAAA,CAAAyD,cAAA,CAAAQ,uBAAA,CAAAoB,QAAA,CAAAzF,SAAA,QAAAI,MAAA,CAAAyD,cAAA,CAAAQ,uBAAA,CAAAoB,QAAA,CAAAnB,QAAA,OAEA;IAMQhF,EAAA,CAAAO,SAAA,GAAyD;IAAzDP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAAyD,cAAA,CAAAQ,uBAAA,CAAAoB,QAAA,CAAAO,QAAA,EAAyD;IA8BvE1G,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAQ,UAAA,SAAAM,MAAA,CAAA6F,WAAA,CAAAC,SAAA,CAA2B;IAKjC5G,EAAA,CAAAO,SAAA,EAA4E;IAA5EP,EAAA,CAAA6G,sBAAA,gBAAAV,QAAA,CAAAM,MAAA,yDAA4E;IAD9CzG,EAAA,CAAAQ,UAAA,UAAAM,MAAA,CAAAyD,cAAA,CAAAuC,mBAAA,CAAAX,QAAA,CAAAM,MAAA,YAAoE;IAKvFzG,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAAQ,UAAA,SAAA2F,QAAA,CAAAM,MAAA,aAA2B;IAG3BzG,EAAA,CAAAO,SAAA,EAA6B;IAA7BP,EAAA,CAAAQ,UAAA,SAAA2F,QAAA,CAAAM,MAAA,eAA6B;;;;;IAeZzG,EAAA,CAAAG,cAAA,WAA+B;IAAAH,EAAA,CAAAmC,MAAA,mBAAY;IAAAnC,EAAA,CAAAM,YAAA,EAAO;;;;;IAK9EN,EAAA,CAAAG,cAAA,eAAiE;IAC7DH,EAAA,CAAAoC,SAAA,0BAAyD;IAC7DpC,EAAA,CAAAM,YAAA,EAAM;;;;IADwBN,EAAA,CAAAO,SAAA,EAAa;IAAbP,EAAA,CAAAQ,UAAA,SAAAuG,QAAA,CAAa;;;;;;IAT3D/G,EAAA,CAAAE,uBAAA,GAA4C;IAGxCF,EADJ,CAAAG,cAAA,cAAsD,aACV;IAAAH,EAAA,CAAAI,UAAA,IAAA4G,qEAAA,mBAA+B;IAAmBhH,EAAA,CAAAM,YAAA,EAAK;IAE3FN,EADJ,CAAAG,cAAA,cAAc,yBAEoD;IAC1DH,EAAA,CAAAI,UAAA,IAAA6G,4EAAA,2BAAuC;IAK3CjH,EAAA,CAAAM,YAAA,EAAa;IAETN,EADJ,CAAAG,cAAA,aAAgC,kBACkF;IAAhFH,EAAA,CAAAgB,UAAA,mBAAAkG,uFAAA;MAAAlH,EAAA,CAAAmB,aAAA,CAAAgG,IAAA;MAAA,MAAArG,MAAA,GAAAd,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAASR,MAAA,CAAAsG,gBAAA,EAAkB;IAAA,EAAC;IAGtEpH,EAH0H,CAAAM,YAAA,EAAS,EACvH,EACF,EACJ;;;;;IAd6CN,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAQ,UAAA,UAAAM,MAAA,CAAA2D,eAAA,CAAsB;IAE3CzE,EAAA,CAAAO,SAAA,GAAoB;IACIP,EADxB,CAAAQ,UAAA,UAAAM,MAAA,CAAAuG,UAAA,CAAoB,SAAAvG,MAAA,CAAAwG,qBAAA,GAAiC,yBAC/C,iBAAiB,gBAAgB;;;;;IA4ErDtH,EADJ,CAAAG,cAAA,UAA8B,eACU;IAAAH,EAAA,CAAAmC,MAAA,iBAAU;IAAAnC,EAAA,CAAAM,YAAA,EAAO;IAEjDN,EADJ,CAAAG,cAAA,eAAiC,eACR;IAAAH,EAAA,CAAAmC,MAAA,iBAAU;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACrCN,EAAA,CAAAG,cAAA,iBAA4B;IAAAH,EAAA,CAAAmC,MAAA,GAAa;IAC7CnC,EAD6C,CAAAM,YAAA,EAAQ,EAC/C;IAEFN,EADJ,CAAAG,cAAA,eAAiC,eACR;IAAAH,EAAA,CAAAmC,MAAA,mBAAW;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACtCN,EAAA,CAAAG,cAAA,kBAA4B;IAAAH,EAAA,CAAAmC,MAAA,IAAmB;IACnDnC,EADmD,CAAAM,YAAA,EAAQ,EACrD;IAEFN,EADJ,CAAAG,cAAA,gBAAiC,gBACR;IAAAH,EAAA,CAAAmC,MAAA,sBAAc;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACzCN,EAAA,CAAAG,cAAA,kBAA4B;IAAAH,EAAA,CAAAmC,MAAA,IAA4B;IAEhEnC,EAFgE,CAAAM,YAAA,EAAQ,EAC9D,EACJ;;;;IAV8BN,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAAyG,SAAA,CAAa;IAIbvH,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAA0G,aAAA,GAAmB;IAInBxH,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAA2G,iBAAA,CAAA9F,MAAA,CAA4B;;;;;IAK5D3B,EADJ,CAAAG,cAAA,eAAiE,gBACtB;IAAAH,EAAA,CAAAmC,MAAA,eAAQ;IAAAnC,EAAA,CAAAM,YAAA,EAAO;IAElDN,EADJ,CAAAG,cAAA,eAAiC,YACtB;IAAAH,EAAA,CAAAmC,MAAA,GAA+B;IAE9CnC,EAF8C,CAAAM,YAAA,EAAQ,EAC5C,EACJ;;;;IAFSN,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAiF,kBAAA,KAAAnE,MAAA,CAAA4G,UAAA,CAAAC,QAAA,aAA+B;;;;;IAK1C3H,EADJ,CAAAG,cAAA,eAA2C,eACH;IAAAH,EAAA,CAAAmC,MAAA,sBAAe;IAAAnC,EAAA,CAAAM,YAAA,EAAO;IAEtDN,EADJ,CAAAG,cAAA,eAAiC,eACR;IAAAH,EAAA,CAAAmC,MAAA,gBAAS;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACpCN,EAAA,CAAAG,cAAA,iBAA4B;IAAAH,EAAA,CAAAmC,MAAA,GAAuD;IACvFnC,EADuF,CAAAM,YAAA,EAAQ,EACzF;IAEFN,EADJ,CAAAG,cAAA,eAAiC,eACR;IAAAH,EAAA,CAAAmC,MAAA,gBAAQ;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACnCN,EAAA,CAAAG,cAAA,kBAA4B;IAAAH,EAAA,CAAAmC,MAAA,IAAsD;IACtFnC,EADsF,CAAAM,YAAA,EAAQ,EACxF;IAEFN,EADJ,CAAAG,cAAA,gBAAiC,gBACR;IAAAH,EAAA,CAAAmC,MAAA,eAAO;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IAClCN,EAAA,CAAAG,cAAA,kBAA4B;IAAAH,EAAA,CAAAmC,MAAA,IAAwD;IAE5FnC,EAF4F,CAAAM,YAAA,EAAQ,EAC1F,EACJ;;;;IAV8BN,EAAA,CAAAO,SAAA,GAAuD;IAAvDP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAA8G,eAAA,CAAA9G,MAAA,CAAA+G,wBAAA,CAAAC,SAAA,EAAuD;IAIvD9H,EAAA,CAAAO,SAAA,GAAsD;IAAtDP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAA8G,eAAA,CAAA9G,MAAA,CAAA+G,wBAAA,CAAAE,QAAA,EAAsD;IAItD/H,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAA8G,eAAA,CAAA9G,MAAA,CAAA+G,wBAAA,aAAwD;;;;;IA+CxF7H,EAAA,CAAAoC,SAAA,4BAAiF;;;;IAA5CpC,EAAA,CAAAQ,UAAA,cAAAmE,aAAA,CAAuB;;;;;IAHxE3E,EAAA,CAAAE,uBAAA,GAAgC;IAExBF,EADJ,CAAAG,cAAA,cAAsC,2BAC6D;IAC3FH,EAAA,CAAAI,UAAA,IAAA4H,mFAAA,gCAA6D;IAErEhI,EADI,CAAAM,YAAA,EAAgB,EACd;;;;;IAH0BN,EAAA,CAAAO,SAAA,GAA0C;IAA1CP,EAAA,CAAAiI,UAAA,CAAAjI,EAAA,CAAAkI,eAAA,IAAAC,GAAA,EAA0C;IAC9CnI,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAQ,UAAA,SAAAmE,aAAA,CAAe;;;;;IAqB/C3E,EAAA,CAAAoC,SAAA,4BAAiF;;;;IAA5CpC,EAAA,CAAAQ,UAAA,cAAAmE,aAAA,CAAuB;;;;;;IAzS5E3E,EAAA,CAAAE,uBAAA,OAAuB;IAEfF,EADJ,CAAAG,cAAA,cAAyD,cACoB;IACrEH,EAAA,CAAAI,UAAA,IAAAgI,qDAAA,oBAAiF;IA+B7EpI,EADJ,CAAAG,cAAA,cAA6D,aACjB;IAAAH,EAAA,CAAAI,UAAA,IAAAiI,sDAAA,mBAA+B;IAAerI,EAAA,CAAAM,YAAA,EAAK;IAC3FN,EAAA,CAAAG,cAAA,aAA8B;IAC1BH,EAAA,CAAAI,UAAA,IAAAkI,oDAAA,mBAE8F;IAyEtGtI,EADI,CAAAM,YAAA,EAAK,EACH;IAENN,EAAA,CAAAI,UAAA,IAAAmI,8DAAA,4BAA4C;IAuBpDvI,EAFI,CAAAM,YAAA,EAAM,EAEJ;IAOiDN,EALvD,CAAAG,cAAA,eAAgD,eAEI,eACU,eACR,cACC,YAAM;IAAAH,EAAA,CAAAmC,MAAA,wBAAgB;IAAOnC,EAAP,CAAAM,YAAA,EAAO,EAAK;IACzEN,EAAA,CAAAG,cAAA,eAA6D;IAA7BH,EAAA,CAAAgB,UAAA,mBAAAwH,sEAAA;MAAAxI,EAAA,CAAAmB,aAAA,CAAAsH,GAAA;MAAA,MAAA3H,MAAA,GAAAd,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAAR,MAAA,CAAA4H,WAAA,GAAuB,IAAI;IAAA,EAAC;IAAC1I,EAAA,CAAAmC,MAAA,eAAO;IACxEnC,EADwE,CAAAM,YAAA,EAAM,EACxE;IACNN,EAAA,CAAAoC,SAAA,iCAA6F;IAErGpC,EADI,CAAAM,YAAA,EAAM,EACJ;IAM6CN,EAHnD,CAAAG,cAAA,eAAgD,eACuB,eAClB,cACF,YAAM;IAAAH,EAAA,CAAAmC,MAAA,uBAAe;IAEhEnC,EAFgE,CAAAM,YAAA,EAAO,EAAK,EAEtE;IAIEN,EAFR,CAAAG,cAAA,eAAwC,WAC/B,gBACmC;IAAAH,EAAA,CAAAmC,MAAA,uBAAe;IAAAnC,EAAA,CAAAM,YAAA,EAAO;IAEtDN,EADJ,CAAAG,cAAA,eAAmC,WAC1B;IAAAH,EAAA,CAAAmC,MAAA,UAAE;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACbN,EAAA,CAAAG,cAAA,cAAQ;IAAAH,EAAA,CAAAmC,MAAA,IAA+C;IAC3DnC,EAD2D,CAAAM,YAAA,EAAS,EAC9D;IAEFN,EADJ,CAAAG,cAAA,eAAmC,WAC1B;IAAAH,EAAA,CAAAmC,MAAA,UAAE;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACbN,EAAA,CAAAG,cAAA,cAAQ;IAAAH,EAAA,CAAAmC,MAAA,IAA+C;IAC3DnC,EAD2D,CAAAM,YAAA,EAAS,EAC9D;IAEFN,EADJ,CAAAG,cAAA,eAAmC,WAC1B;IAAAH,EAAA,CAAAmC,MAAA,UAAE;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACbN,EAAA,CAAAG,cAAA,cAAQ;IAAAH,EAAA,CAAAmC,MAAA,IAA+C;IAC3DnC,EAD2D,CAAAM,YAAA,EAAS,EAC9D;IAEFN,EADJ,CAAAG,cAAA,eAAmC,WAC1B;IAAAH,EAAA,CAAAmC,MAAA,UAAE;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACbN,EAAA,CAAAG,cAAA,cAAQ;IAAAH,EAAA,CAAAmC,MAAA,IAA+C;IAC3DnC,EAD2D,CAAAM,YAAA,EAAS,EAC9D;IAEFN,EADJ,CAAAG,cAAA,eAAmC,WAC1B;IAAAH,EAAA,CAAAmC,MAAA,UAAE;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACbN,EAAA,CAAAG,cAAA,cAAQ;IAAAH,EAAA,CAAAmC,MAAA,IAA+C;IAC3DnC,EAD2D,CAAAM,YAAA,EAAS,EAC9D;IAEFN,EADJ,CAAAG,cAAA,eAAmC,WAC1B;IAAAH,EAAA,CAAAmC,MAAA,UAAE;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACbN,EAAA,CAAAG,cAAA,cAAQ;IAAAH,EAAA,CAAAmC,MAAA,IAA+C;IAC3DnC,EAD2D,CAAAM,YAAA,EAAS,EAC9D;IAEFN,EADJ,CAAAG,cAAA,eAAmC,WAC1B;IAAAH,EAAA,CAAAmC,MAAA,UAAE;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACbN,EAAA,CAAAG,cAAA,cAAQ;IAAAH,EAAA,CAAAmC,MAAA,IAA+C;IAE/DnC,EAF+D,CAAAM,YAAA,EAAS,EAC9D,EACJ;IAENN,EAAA,CAAAG,cAAA,eAAiC;IA0B7BH,EAvBA,CAAAI,UAAA,KAAAuI,sDAAA,mBAA8B,KAAAC,sDAAA,kBAgBmC,KAAAC,sDAAA,mBAOtB;IAe/C7I,EAAA,CAAAM,YAAA,EAAM;IAENN,EAAA,CAAAoC,SAAA,eAIM;IA4BtBpC,EAHY,CAAAM,YAAA,EAAM,EACJ,EACJ,EACJ;IAM6CN,EAHnD,CAAAG,cAAA,eAA0E,eACvB,eACW,cACX,YAAM;IAAAH,EAAA,CAAAmC,MAAA,sBAAc;IAAOnC,EAAP,CAAAM,YAAA,EAAO,EAAK;IACvEN,EAAA,CAAAI,UAAA,KAAA0I,+DAAA,2BAAgC;IAQxC9I,EADI,CAAAM,YAAA,EAAM,EACJ;IAMyCN,EAJ/C,CAAAG,cAAA,eAA+C,eACW,eACZ,eACI,cACH,YAAM;IAAAH,EAAA,CAAAmC,MAAA,eAAO;IAQpDnC,EARoD,CAAAM,YAAA,EAAO,EAAK,EAQ1D;IACNN,EAAA,CAAAG,cAAA,4BAA+F;IAC/FH,EAAA,CAAAI,UAAA,KAAA2I,oEAAA,gCAA6D;IAKzE/I,EAJY,CAAAM,YAAA,EAAgB,EACd,EACJ,EACA,EACJ;;;;;;IA3SyDN,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAQ,UAAA,SAAAM,MAAA,CAAA0E,IAAA,eAAwB;IA+B5BxF,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAQ,UAAA,UAAAM,MAAA,CAAA2D,eAAA,CAAsB;IAE5CzE,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAAQ,UAAA,YAAAmE,aAAA,CAAAjD,iBAAA,CAAgC;IA6E9C1B,EAAA,CAAAO,SAAA,EAA2B;IAA3BP,EAAA,CAAAQ,UAAA,SAAAM,MAAA,CAAA6F,WAAA,CAAAC,SAAA,CAA2B;IAiCf5G,EAAA,CAAAO,SAAA,IAAc;IAACP,EAAf,CAAAQ,UAAA,eAAc,aAAAM,MAAA,CAAA2G,iBAAA,CAA+B;IAiBhDzH,EAAA,CAAAO,SAAA,IAA+C;IAA/CP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAA8G,eAAA,CAAA9G,MAAA,CAAAkI,uBAAA,CAAAC,EAAA,EAA+C;IAI/CjJ,EAAA,CAAAO,SAAA,GAA+C;IAA/CP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAA8G,eAAA,CAAA9G,MAAA,CAAAkI,uBAAA,CAAAE,EAAA,EAA+C;IAI/ClJ,EAAA,CAAAO,SAAA,GAA+C;IAA/CP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAA8G,eAAA,CAAA9G,MAAA,CAAAkI,uBAAA,CAAAG,EAAA,EAA+C;IAI/CnJ,EAAA,CAAAO,SAAA,GAA+C;IAA/CP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAA8G,eAAA,CAAA9G,MAAA,CAAAkI,uBAAA,CAAAI,EAAA,EAA+C;IAI/CpJ,EAAA,CAAAO,SAAA,GAA+C;IAA/CP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAA8G,eAAA,CAAA9G,MAAA,CAAAkI,uBAAA,CAAAK,EAAA,EAA+C;IAI/CrJ,EAAA,CAAAO,SAAA,GAA+C;IAA/CP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAA8G,eAAA,CAAA9G,MAAA,CAAAkI,uBAAA,CAAAM,EAAA,EAA+C;IAI/CtJ,EAAA,CAAAO,SAAA,GAA+C;IAA/CP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAA8G,eAAA,CAAA9G,MAAA,CAAAkI,uBAAA,CAAAO,EAAA,EAA+C;IAOrDvJ,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAQ,UAAA,UAAAM,MAAA,CAAA2D,eAAA,CAAsB;IAgBtBzE,EAAA,CAAAO,SAAA,EAAqB;IAArBP,EAAA,CAAAQ,UAAA,SAAAM,MAAA,CAAA2D,eAAA,CAAqB;IAORzE,EAAA,CAAAO,SAAA,EAAsB;IAAtBP,EAAA,CAAAQ,UAAA,UAAAM,MAAA,CAAA2D,eAAA,CAAsB;IAwDlCzE,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAQ,UAAA,SAAAmE,aAAA,CAAe;IAuBF3E,EAAA,CAAAO,SAAA,GAA0C;IAA1CP,EAAA,CAAAiI,UAAA,CAAAjI,EAAA,CAAAkI,eAAA,KAAAC,GAAA,EAA0C;IAClDnI,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAQ,UAAA,SAAAmE,aAAA,CAAe;;;;;IAkD3B3E,EADA,CAAAG,cAAA,cAA+C,eACzB;IAClBH,EAAA,CAAAmC,MAAA,aAAK;IAAAnC,EAAA,CAAAoC,SAAA,SAAI;IACDpC,EAAR,CAAAG,cAAA,aAAQ,aAC2F;IAAAH,EAAA,CAAAmC,MAAA,GAAyD;IAEhKnC,EAFgK,CAAAM,YAAA,EAAI,EAAS,EACvK,EACA;;;;;IAFEN,EAAA,CAAAO,SAAA,GAA8F;IAA9FP,EAAA,CAAAQ,UAAA,SAAAM,MAAA,CAAAyD,cAAA,CAAAY,eAAA,CAAArE,MAAA,CAAAyD,cAAA,CAAAQ,uBAAA,CAAAyE,WAAA,CAAApE,KAAA,IAAApF,EAAA,CAAA6E,aAAA,CAA8F;IAAC7E,EAAA,CAAAO,SAAA,EAAyD;IAAzDP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAAyD,cAAA,CAAAQ,uBAAA,CAAAyE,WAAA,CAAApE,KAAA,EAAyD;;;;;IA2E5JpF,EADJ,CAAAG,cAAA,UAA8B,eACU;IAAAH,EAAA,CAAAmC,MAAA,iBAAU;IAAAnC,EAAA,CAAAM,YAAA,EAAO;IAEjDN,EADJ,CAAAG,cAAA,eAAiC,eACR;IAAAH,EAAA,CAAAmC,MAAA,iBAAU;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACrCN,EAAA,CAAAG,cAAA,iBAA4B;IAAAH,EAAA,CAAAmC,MAAA,GAAa;IAC7CnC,EAD6C,CAAAM,YAAA,EAAQ,EAC/C;IAEFN,EADJ,CAAAG,cAAA,eAAiC,eACR;IAAAH,EAAA,CAAAmC,MAAA,mBAAW;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACtCN,EAAA,CAAAG,cAAA,kBAA4B;IAAAH,EAAA,CAAAmC,MAAA,IAAmB;IACnDnC,EADmD,CAAAM,YAAA,EAAQ,EACrD;IAEFN,EADJ,CAAAG,cAAA,gBAAiC,gBACR;IAAAH,EAAA,CAAAmC,MAAA,sBAAc;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACzCN,EAAA,CAAAG,cAAA,kBAA4B;IAAAH,EAAA,CAAAmC,MAAA,IAA4B;IAEhEnC,EAFgE,CAAAM,YAAA,EAAQ,EAC9D,EACJ;;;;IAV8BN,EAAA,CAAAO,SAAA,GAAa;IAAbP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAAyG,SAAA,CAAa;IAIbvH,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAA0G,aAAA,GAAmB;IAInBxH,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAA2G,iBAAA,CAAA9F,MAAA,CAA4B;;;;;IAK5D3B,EADJ,CAAAG,cAAA,eAAiE,gBACtB;IAAAH,EAAA,CAAAmC,MAAA,eAAQ;IAAAnC,EAAA,CAAAM,YAAA,EAAO;IAElDN,EADJ,CAAAG,cAAA,eAAiC,YACtB;IAAAH,EAAA,CAAAmC,MAAA,GAA+B;IAE9CnC,EAF8C,CAAAM,YAAA,EAAQ,EAC5C,EACJ;;;;IAFSN,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAiF,kBAAA,KAAAnE,MAAA,CAAA4G,UAAA,CAAAC,QAAA,aAA+B;;;;;IAK1C3H,EADJ,CAAAG,cAAA,eAA2C,eACH;IAAAH,EAAA,CAAAmC,MAAA,sBAAe;IAAAnC,EAAA,CAAAM,YAAA,EAAO;IAEtDN,EADJ,CAAAG,cAAA,eAAiC,eACR;IAAAH,EAAA,CAAAmC,MAAA,gBAAS;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACpCN,EAAA,CAAAG,cAAA,iBAA4B;IAAAH,EAAA,CAAAmC,MAAA,GAAuD;IACvFnC,EADuF,CAAAM,YAAA,EAAQ,EACzF;IAEFN,EADJ,CAAAG,cAAA,eAAiC,eACR;IAAAH,EAAA,CAAAmC,MAAA,gBAAQ;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACnCN,EAAA,CAAAG,cAAA,kBAA4B;IAAAH,EAAA,CAAAmC,MAAA,IAAsD;IACtFnC,EADsF,CAAAM,YAAA,EAAQ,EACxF;IAEFN,EADJ,CAAAG,cAAA,gBAAiC,gBACR;IAAAH,EAAA,CAAAmC,MAAA,eAAO;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IAClCN,EAAA,CAAAG,cAAA,kBAA4B;IAAAH,EAAA,CAAAmC,MAAA,IAAwD;IAE5FnC,EAF4F,CAAAM,YAAA,EAAQ,EAC1F,EACJ;;;;IAV8BN,EAAA,CAAAO,SAAA,GAAuD;IAAvDP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAA8G,eAAA,CAAA9G,MAAA,CAAA+G,wBAAA,CAAAC,SAAA,EAAuD;IAIvD9H,EAAA,CAAAO,SAAA,GAAsD;IAAtDP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAA8G,eAAA,CAAA9G,MAAA,CAAA+G,wBAAA,CAAAE,QAAA,EAAsD;IAItD/H,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAA8G,eAAA,CAAA9G,MAAA,CAAA+G,wBAAA,aAAwD;;;;;IA8CpG7H,EAAA,CAAAoC,SAAA,4BAAiF;;;;IAA5CpC,EAAA,CAAAQ,UAAA,cAAAiJ,aAAA,CAAuB;;;;;IAS5DzJ,EAAA,CAAAoC,SAAA,4BAAiF;;;;IAA5CpC,EAAA,CAAAQ,UAAA,cAAAiJ,aAAA,CAAuB;;;;;IAxMpBzJ,EAJxD,CAAAG,cAAA,eAAkB,eACe,eACK,cAC4B,aACV,WAAM;IAAAH,EAAA,CAAAmC,MAAA,oBAAa;IAAOnC,EAAP,CAAAM,YAAA,EAAO,EAAK;IAM3DN,EALZ,CAAAG,cAAA,aAA8B,aAEqE,eAC3C,eAEV;IAC9BH,EAAA,CAAAoC,SAAA,eAC0B;IAC1BpC,EAAA,CAAAM,YAAA,EAAM;IAIdN,EAFJ,CAAAG,cAAA,eAAuD,eAChC,gBACW;IAC1BH,EAAA,CAAAmC,MAAA,mBAAU;IAAAnC,EAAA,CAAAoC,SAAA,UAAI;IACdpC,EAAA,CAAAG,cAAA,cAAQ;IAAAH,EAAA,CAAAmC,MAAA,IAA6D;IACzEnC,EADyE,CAAAM,YAAA,EAAS,EAC5E;IACNN,EAAA,CAAAG,cAAA,gBAAsB;IAClBH,EAAA,CAAAmC,MAAA,kBAAS;IAAAnC,EAAA,CAAAoC,SAAA,UAAI;IACbpC,EAAA,CAAAG,cAAA,cAAQ;IAAAH,EAAA,CAAAmC,MAAA,IAA4D;IAExEnC,EAFwE,CAAAM,YAAA,EAAS,EAC3E,EACA;IAENN,EADA,CAAAG,cAAA,eAAyB,gBACF;IACnBH,EAAA,CAAAmC,MAAA,iBAAQ;IAAAnC,EAAA,CAAAoC,SAAA,UAAI;IACZpC,EAAA,CAAAG,cAAA,cAAQ;IAAAH,EAAA,CAAAmC,MAAA,IAAiC;;IAC7CnC,EAD6C,CAAAM,YAAA,EAAS,EAChD;IACNN,EAAA,CAAAG,cAAA,gBAAsB;IAClBH,EAAA,CAAAmC,MAAA,iBAAQ;IAAAnC,EAAA,CAAAoC,SAAA,UAAI;IACZpC,EAAA,CAAAG,cAAA,cAAQ;IAAAH,EAAA,CAAAmC,MAAA,IAAwH;IAEpInC,EAFoI,CAAAM,YAAA,EAAS,EACvI,EACA;IACNN,EAAA,CAAAI,UAAA,KAAAsJ,sDAAA,mBAA+C;IAQ/C1J,EADA,CAAAG,cAAA,eAAyB,gBACH;IAClBH,EAAA,CAAAmC,MAAA,cAAK;IAAAnC,EAAA,CAAAoC,SAAA,UAAI;IACTpC,EAAA,CAAAG,cAAA,cAAQ;IAAAH,EAAA,CAAAmC,MAAA,IAA4D;IAExEnC,EAFwE,CAAAM,YAAA,EAAS,EAC3E,EACA;IAGFN,EADJ,CAAAG,cAAA,eAAyB,gBACC;IAClBH,EAAA,CAAAmC,MAAA,iBAAQ;IAAAnC,EAAA,CAAAoC,SAAA,UAAI;IACZpC,EAAA,CAAAG,cAAA,cAAQ;IACJH,EAAA,CAAAmC,MAAA,IAEJ;IAS5BnC,EAT4B,CAAAM,YAAA,EAAS,EACP,EACA,EACR,EACI,EACL,EACJ,EACH,EACJ,EACJ;IAQiDN,EANvD,CAAAG,cAAA,gBAA6B,gBAGiB,eAC6B,eACrB,cACC,YAAM;IAAAH,EAAA,CAAAmC,MAAA,uBAAe;IAEhEnC,EAFgE,CAAAM,YAAA,EAAO,EAAK,EAEtE;IAKEN,EAFR,CAAAG,cAAA,eAAwC,WAC/B,gBACmC;IAAAH,EAAA,CAAAmC,MAAA,uBAAe;IAAAnC,EAAA,CAAAM,YAAA,EAAO;IAEtDN,EADJ,CAAAG,cAAA,eAAmC,WAC1B;IAAAH,EAAA,CAAAmC,MAAA,UAAE;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACbN,EAAA,CAAAG,cAAA,cAAQ;IAAAH,EAAA,CAAAmC,MAAA,IAA+C;IAC3DnC,EAD2D,CAAAM,YAAA,EAAS,EAC9D;IAEFN,EADJ,CAAAG,cAAA,eAAmC,WAC1B;IAAAH,EAAA,CAAAmC,MAAA,UAAE;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACbN,EAAA,CAAAG,cAAA,cAAQ;IAAAH,EAAA,CAAAmC,MAAA,IAA+C;IAC3DnC,EAD2D,CAAAM,YAAA,EAAS,EAC9D;IAEFN,EADJ,CAAAG,cAAA,eAAmC,WAC1B;IAAAH,EAAA,CAAAmC,MAAA,UAAE;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACbN,EAAA,CAAAG,cAAA,cAAQ;IAAAH,EAAA,CAAAmC,MAAA,IAA+C;IAC3DnC,EAD2D,CAAAM,YAAA,EAAS,EAC9D;IAEFN,EADJ,CAAAG,cAAA,eAAmC,WAC1B;IAAAH,EAAA,CAAAmC,MAAA,UAAE;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACbN,EAAA,CAAAG,cAAA,cAAQ;IAAAH,EAAA,CAAAmC,MAAA,IAA+C;IAC3DnC,EAD2D,CAAAM,YAAA,EAAS,EAC9D;IAEFN,EADJ,CAAAG,cAAA,eAAmC,WAC1B;IAAAH,EAAA,CAAAmC,MAAA,UAAE;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACbN,EAAA,CAAAG,cAAA,cAAQ;IAAAH,EAAA,CAAAmC,MAAA,IAA+C;IAC3DnC,EAD2D,CAAAM,YAAA,EAAS,EAC9D;IAEFN,EADJ,CAAAG,cAAA,eAAmC,WAC1B;IAAAH,EAAA,CAAAmC,MAAA,UAAE;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACbN,EAAA,CAAAG,cAAA,cAAQ;IAAAH,EAAA,CAAAmC,MAAA,IAA+C;IAC3DnC,EAD2D,CAAAM,YAAA,EAAS,EAC9D;IAEFN,EADJ,CAAAG,cAAA,eAAmC,WAC1B;IAAAH,EAAA,CAAAmC,MAAA,UAAE;IAAAnC,EAAA,CAAAM,YAAA,EAAM;IACbN,EAAA,CAAAG,cAAA,cAAQ;IAAAH,EAAA,CAAAmC,MAAA,IAA+C;IAE/DnC,EAF+D,CAAAM,YAAA,EAAS,EAC9D,EACJ;IAENN,EAAA,CAAAG,cAAA,eAAiC;IA0B7BH,EAvBA,CAAAI,UAAA,KAAAuJ,sDAAA,mBAA8B,KAAAC,sDAAA,kBAgBmC,KAAAC,sDAAA,mBAOtB;IAe/C7J,EAAA,CAAAM,YAAA,EAAM;IAENN,EAAA,CAAAoC,SAAA,eAIM;IA6BtBpC,EAJY,CAAAM,YAAA,EAAM,EAEJ,EACJ,EACJ;IAM6CN,EAHnD,CAAAG,cAAA,iBAAgC,iBACC,gBAC6B,eACX,aAAM;IAAAH,EAAA,CAAAmC,MAAA,uBAAc;IAAOnC,EAAP,CAAAM,YAAA,EAAO,EAAK;IACvEN,EAAA,CAAAG,cAAA,6BAA+F;IAC/FH,EAAA,CAAAI,UAAA,MAAA0J,qEAAA,gCAA6D;IAGrE9J,EAFQ,CAAAM,YAAA,EAAgB,EACd,EACJ;IAIyCN,EAF/C,CAAAG,cAAA,iBAA6B,gBAC6B,eACX,aAAM;IAAAH,EAAA,CAAAmC,MAAA,gBAAO;IAAOnC,EAAP,CAAAM,YAAA,EAAO,EAAK;IAChEN,EAAA,CAAAG,cAAA,6BAA+F;IAC/FH,EAAA,CAAAI,UAAA,MAAA2J,qEAAA,gCAA6D;IAM7E/J,EALgB,CAAAM,YAAA,EAAgB,EACd,EACJ,EACJ,EAEJ;;;;;;IA5MkBN,EAAA,CAAAO,SAAA,GAAwD;IAAxDP,EAAA,CAAAqC,WAAA,YAAAmH,WAAA,CAAA/C,MAAA,6BAAwD;IAK3CzG,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAAQ,UAAA,QAAAM,MAAA,CAAA4D,YAAA,CAAA8E,WAAA,GAAAxJ,EAAA,CAAA6E,aAAA,CAA6B;IAQ9B7E,EAAA,CAAAO,SAAA,GAA6D;IAA7DP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAAyD,cAAA,CAAAQ,uBAAA,CAAAyE,WAAA,CAAA9I,SAAA,EAA6D;IAI7DV,EAAA,CAAAO,SAAA,GAA4D;IAA5DP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAAyD,cAAA,CAAAQ,uBAAA,CAAAyE,WAAA,CAAAxE,QAAA,EAA4D;IAM5DhF,EAAA,CAAAO,SAAA,GAAiC;IAAjCP,EAAA,CAAAuD,iBAAA,CAAAvD,EAAA,CAAAwD,WAAA,SAAAgG,WAAA,CAAAQ,GAAA,WAAiC;IAIjChK,EAAA,CAAAO,SAAA,GAAwH;IAAxHP,EAAA,CAAA8E,kBAAA,KAAAhE,MAAA,CAAAyD,cAAA,CAAAQ,uBAAA,CAAAyE,WAAA,CAAAS,MAAA,QAAAnJ,MAAA,CAAAyD,cAAA,CAAAQ,uBAAA,CAAAyE,WAAA,CAAAtE,SAAA,MAAwH;IAG1GlF,EAAA,CAAAO,SAAA,EAAmB;IAAnBP,EAAA,CAAAQ,UAAA,SAAAgJ,WAAA,CAAApE,KAAA,CAAmB;IAUjCpF,EAAA,CAAAO,SAAA,GAA4D;IAA5DP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAAyD,cAAA,CAAAQ,uBAAA,CAAAyE,WAAA,CAAA9C,QAAA,EAA4D;IAQ5D1G,EAAA,CAAAO,SAAA,GAEJ;IAFIP,EAAA,CAAA8E,kBAAA,MAAAhE,MAAA,CAAAyD,cAAA,CAAA2F,0BAAA,CAAAV,WAAA,CAAAW,QAAA,EAAAC,IAAA,SAAAtJ,MAAA,CAAAyD,cAAA,CAAA8F,gBAAA,CAAAvJ,MAAA,CAAAyD,cAAA,CAAA2F,0BAAA,CAAAV,WAAA,CAAAW,QAAA,EAAAC,IAAA,cAEJ;IA2BIpK,EAAA,CAAAO,SAAA,IAA+C;IAA/CP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAA8G,eAAA,CAAA9G,MAAA,CAAAkI,uBAAA,CAAAC,EAAA,EAA+C;IAI/CjJ,EAAA,CAAAO,SAAA,GAA+C;IAA/CP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAA8G,eAAA,CAAA9G,MAAA,CAAAkI,uBAAA,CAAAE,EAAA,EAA+C;IAI/ClJ,EAAA,CAAAO,SAAA,GAA+C;IAA/CP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAA8G,eAAA,CAAA9G,MAAA,CAAAkI,uBAAA,CAAAG,EAAA,EAA+C;IAI/CnJ,EAAA,CAAAO,SAAA,GAA+C;IAA/CP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAA8G,eAAA,CAAA9G,MAAA,CAAAkI,uBAAA,CAAAI,EAAA,EAA+C;IAI/CpJ,EAAA,CAAAO,SAAA,GAA+C;IAA/CP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAA8G,eAAA,CAAA9G,MAAA,CAAAkI,uBAAA,CAAAK,EAAA,EAA+C;IAI/CrJ,EAAA,CAAAO,SAAA,GAA+C;IAA/CP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAA8G,eAAA,CAAA9G,MAAA,CAAAkI,uBAAA,CAAAM,EAAA,EAA+C;IAI/CtJ,EAAA,CAAAO,SAAA,GAA+C;IAA/CP,EAAA,CAAAuD,iBAAA,CAAAzC,MAAA,CAAA8G,eAAA,CAAA9G,MAAA,CAAAkI,uBAAA,CAAAO,EAAA,EAA+C;IAOrDvJ,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAQ,UAAA,UAAAM,MAAA,CAAA2D,eAAA,CAAsB;IAgBtBzE,EAAA,CAAAO,SAAA,EAAqB;IAArBP,EAAA,CAAAQ,UAAA,SAAAM,MAAA,CAAA2D,eAAA,CAAqB;IAORzE,EAAA,CAAAO,SAAA,EAAsB;IAAtBP,EAAA,CAAAQ,UAAA,UAAAM,MAAA,CAAA2D,eAAA,CAAsB;IAyDrBzE,EAAA,CAAAO,SAAA,GAA0C;IAA1CP,EAAA,CAAAiI,UAAA,CAAAjI,EAAA,CAAAkI,eAAA,KAAAC,GAAA,EAA0C;IAClDnI,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAQ,UAAA,SAAAiJ,aAAA,CAAe;IAQPzJ,EAAA,CAAAO,SAAA,GAA0C;IAA1CP,EAAA,CAAAiI,UAAA,CAAAjI,EAAA,CAAAkI,eAAA,KAAAC,GAAA,EAA0C;IAClDnI,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAQ,UAAA,SAAAiJ,aAAA,CAAe;;;;;IASvDzJ,EAAA,CAAAE,uBAAA,GAAgC;IAC5BF,EAAA,CAAAG,cAAA,eAAkC;IAClCH,EAAA,CAAAoC,SAAA,+BAAmG;IACvGpC,EAAA,CAAAM,YAAA,EAAM;;;;;IADmBN,EAAA,CAAAO,SAAA,GAAuB;IAACP,EAAxB,CAAAQ,UAAA,cAAAM,MAAA,CAAAC,SAAA,CAAuB,gCAAgC;;;;;IAltBhFf,EAAA,CAAAG,cAAA,cAAoD;IAyBxCH,EAxBJ,CAAAI,UAAA,IAAAkK,uCAAA,kBAAgD,IAAAC,gDAAA,2BAwBN;IAM9CvK,EAAA,CAAAG,cAAA,cAAsC;IAoBlCH,EAnBA,CAAAI,UAAA,IAAAoK,uCAAA,mBAAgD,IAAAC,uCAAA,oBAmB2B;IA8I/EzK,EAAA,CAAAM,YAAA,EAAM;IA+gBVN,EA5gBI,CAAAI,UAAA,IAAAsK,+CAAA,kCAAA1K,EAAA,CAAA2K,sBAAA,CAAmG,IAAAC,+CAAA,mCAAA5K,EAAA,CAAA2K,sBAAA,CAsThB,KAAAE,iDAAA,2BAsNvD;IAKhC7K,EAAA,CAAAM,YAAA,EAAM;;;;IAptB2BN,EAAA,CAAAO,SAAA,EAAqB;IAArBP,EAAA,CAAAQ,UAAA,SAAAM,MAAA,CAAA0E,IAAA,cAAqB;IAwB3BxF,EAAA,CAAAO,SAAA,EAAqB;IAArBP,EAAA,CAAAQ,UAAA,SAAAM,MAAA,CAAA0E,IAAA,cAAqB;IAOlCxF,EAAA,CAAAO,SAAA,GAAwC;IAAxCP,EAAA,CAAAQ,UAAA,SAAAM,MAAA,CAAA2D,eAAA,IAAA3D,MAAA,CAAA0E,IAAA,cAAwC;IAmBxCxF,EAAA,CAAAO,SAAA,EAAwC;IAAxCP,EAAA,CAAAQ,UAAA,SAAAM,MAAA,CAAA0E,IAAA,iBAAA1E,MAAA,CAAA2C,YAAA,CAAA6B,EAAA,CAAwC;IA6pBvCtF,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAQ,UAAA,SAAAM,MAAA,CAAAC,SAAA,CAAe;;;;;;IAYdf,EAJhB,CAAAG,cAAA,eAAyC,eACT,eACW,eACG,eACwC;IAA5BH,EAAA,CAAAgB,UAAA,mBAAA8J,uDAAA;MAAA9K,EAAA,CAAAmB,aAAA,CAAA4J,IAAA;MAAA,MAAAjK,MAAA,GAAAd,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAAR,MAAA,CAAA4H,WAAA,GAAqB,KAAK;IAAA,EAAC;IACjE1I,EAAA,CAAAoC,SAAA,eAA6C;IACjDpC,EAAA,CAAAM,YAAA,EAAM;IACNN,EAAA,CAAAG,cAAA,eAA+B;IAC3BH,EAAA,CAAAmC,MAAA,aACJ;IAQRnC,EARQ,CAAAM,YAAA,EAAM,EACJ,EAOJ;IACNN,EAAA,CAAAoC,SAAA,8BAAmE;IAE3EpC,EADI,CAAAM,YAAA,EAAM,EACJ;;;;IAFuBN,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAQ,UAAA,cAAAM,MAAA,CAAAC,SAAA,CAAuB;;;;;IAShDf,EAAA,CAAAoC,SAAA,6BAEoB;;;;IAD8EpC,EAD5D,CAAAQ,UAAA,UAAAM,MAAA,CAAAC,SAAA,CAAAiK,KAAA,CAAyB,WAAAlK,MAAA,CAAA2C,YAAA,CAAAwH,MAAA,CAA+B,kBAAAnK,MAAA,CAAA2C,YAAA,CAAAC,eAAA,CAC5C,sBAAA5C,MAAA,CAAA2C,YAAA,CAAAyH,WAAA,CAA+C,SAAApK,MAAA,CAAAqK,IAAA,CAAc;;;ADztBnH,OAAM,MAAOC,kBAAkB;EA6C7BC,YACUC,MAAc,EACdC,WAAwB,EACxBC,cAA8B,EAC9BC,aAA4B,EAC5BC,sBAA8C,EAC/CnH,cAA8B,EAC9BoC,WAAwB,EACvBgF,eAAgC,EACjCjG,gBAAkC,EAClCkG,YAA0B;IATzB,KAAAN,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACvB,KAAAnH,cAAc,GAAdA,cAAc;IACd,KAAAoC,WAAW,GAAXA,WAAW;IACV,KAAAgF,eAAe,GAAfA,eAAe;IAChB,KAAAjG,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAkG,YAAY,GAAZA,YAAY;IAtDb,KAAAC,IAAI,GAAG,IAAI/L,OAAO,EAAE;IAEc,KAAAgM,QAAQ,GAAa,EAAc;IACvD,KAAAC,OAAO,GAAY,EAAa;IACzB,KAAAC,YAAY,GAA2B,EAA4B;IAEtF,KAAAC,WAAW,GAAG,IAAIxM,YAAY,EAAE;IAChC,KAAAyM,WAAW,GAAG,IAAIzM,YAAY,EAAE;IACjC,KAAAsB,SAAS,GAAQ,EAAe;IAChC,KAAA0G,iBAAiB,GAAc,EAAE;IACnC,KAAAiB,WAAW,GAAY,KAAK;IAC5B,KAAAyD,aAAa,GAAY,IAAI;IAC7B,KAAAC,QAAQ,GAAc,EAAE;IACxB,KAAAC,kBAAkB,GAAc,EAAE;IAClC,KAAA5H,eAAe,GAAY,KAAK;IAChC,KAAAiD,UAAU,GAAS,EAAU;IAC7B,KAAAjE,YAAY,GAAqB,EAAsB;IACvD,KAAAuF,uBAAuB,GAA8B,EAAE;IACvD,KAAAnB,wBAAwB,GAAO,EAAE;IACxC;IACO,KAAAyE,UAAU,GAAW,CAAC;IACtB,KAAA/E,SAAS,GAAW,CAAC;IACrB,KAAA/B,IAAI,GAAW,EAAE;IACjB,KAAA+G,IAAI,GAAS,EAAU;IACvB,KAAAC,eAAe,GAAW,EAAE;IACnC,KAAAC,uBAAuB,GAAW,CAAC;IACnC,KAAAC,UAAU,GAAY,KAAK;IAC3B,KAAAC,kBAAkB,GAAW,EAAE;IAC/B,KAAAC,WAAW,GAAG7M,IAAI,CAAC6M,WAAW,CAAC7L,SAAS;IACxC,KAAA8L,SAAS,GAAG,IAAI;IAChB,KAAAxF,UAAU,GAAG,IAAI,CAAC9C,cAAc,CAACuI,SAAS;IAC1C,KAAAC,aAAa,GAAWnN,KAAK,CAACoN,GAAG;IAEjC,KAAAC,OAAO,GAAG,CACR;MACI3H,EAAE,EAAE,OAAO;MACX4H,KAAK,EAAE;KACV,EACD;MACI5H,EAAE,EAAE,WAAW;MACf4H,KAAK,EAAE;KACV,CACJ;IA8JC,KAAAC,MAAM,GAAY,EAAa;IAiB/B,KAAAhC,IAAI,GAA+B,MAAM;IACzC,KAAAiC,UAAU,GAAY,KAAK;EAlKvB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACtM,SAAS,GAAG,IAAI,CAAC2E,gBAAgB,CAAC4H,oBAAoB,EAAE;IAE7DC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACzM,SAAS,CAACW,iBAAiB,CAAC;IAC7C,IAAI,CAACiL,kBAAkB,GAAG,IAAI,CAAC5L,SAAS,CAAC0M,WAAW;IACpD,IAAI,CAAChG,iBAAiB,GAAC,IAAI,CAACA,iBAAiB,CAACiG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,OAAO,EAAE;IACnE,IAAI,CAACpB,IAAI,GAAG,IAAI,CAAC5F,WAAW,CAACiH,eAAe,EAAE;IAC9C,IAAI,CAACpI,IAAI,GAAG,IAAI,CAAC+G,IAAI,CAAC/G,IAAK;IAC3B,IAAI,CAACuH,aAAa,GAAG,IAAI,CAAChM,SAAS,CAAC0M,WAAW;IAC/C,IAAI,CAACpG,UAAU,GAAG,IAAI,CAAC3B,gBAAgB,CAACmI,8BAA8B,CAAC,IAAI,CAACxG,UAAU,EAAE,IAAI,CAACV,WAAW,CAACmH,gBAAgB,CAAC;IAC1H;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,KAAK,IAAIvB,IAAI,IAAI,IAAI,CAACxL,SAAS,CAACW,iBAAiB,EAAE;MACjD,IAAI,CAAC0K,QAAQ,CAAC2B,IAAI,CAAC,KAAK,CAAC;MACzB,IAAI,CAAC1B,kBAAkB,CAAC0B,IAAI,CAAC,KAAK,CAAC;IACrC;IACA,IAAI,IAAI,CAACvI,IAAI,IAAI3F,QAAQ,CAACmO,OAAO,EAAE;MACjC,IAAI,CAAC5B,QAAQ,CAAC2B,IAAI,CAAC,KAAK,CAAC;MACzB,IAAI,CAAC1B,kBAAkB,CAAC0B,IAAI,CAAC,KAAK,CAAC;IACrC;IACA,IAAI,CAACE,0BAA0B,EAAE;IACjC;IACA,IAAI,CAACC,oBAAoB,EAAE;IAC3B;EACF;EAEAC,eAAeA,CAAA;IACbC,UAAU,CAAC,MAAK;MACd,IAAI,CAACpC,YAAY,CAACqC,OAAO,CAACC,WAAW,IAAG;QACtCA,WAAW,CAACC,OAAO,EAAE;MACvB,CAAC,CAAC;IACJ,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC3C,IAAI,CAAC4C,WAAW,EAAE;EACzB;EAEAC,mBAAmBA,CAACC,KAAU;IAC5B,IAAI,CAAChC,kBAAkB,GAAGgC,KAAK,CAACC,KAAK;IACrC,IAAI,CAAC/C,IAAI,CAACgD,GAAG,CAAC,IAAI,CAACrD,cAAc,CAACsD,cAAc,CAAC,IAAI,CAAC3B,MAAM,CAAC7H,EAAE,EAAEqJ,KAAK,CAACC,KAAK,CAAC,CAACG,IAAI,CAACpP,IAAI,CAAC,CAAC,CAAC,CAAC,CAACqP,SAAS,CAACC,GAAG,IAAE,CAC3G,CAAC,CAAC,CAAC;EACL;EAGAC,uBAAuBA,CAAA;IACrB,IAAIxP,MAAM,CAAC,IAAIyP,IAAI,CAAC,IAAI,CAACpO,SAAS,CAACqO,aAAa,EAAEC,SAAU,CAAC,CAACC,OAAO,EAAG,CAAC,CAACT,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAACU,MAAM,EAAE,CAACD,OAAO,EAAE,GAAG5P,MAAM,CAAC,IAAIyP,IAAI,EAAE,CAAC,CAACN,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAACU,MAAM,EAAE,CAACD,OAAO,EAAE,EAAE;MAChK,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EAEAE,uBAAuBA,CAAA;IACrB,IAAI,CAAClE,MAAM,CAACmE,QAAQ,CAAC,CAAC,kBAAkB,GAAG,IAAI,CAAC1O,SAAS,CAACuE,EAAE,CAAC,CAAC;EAChE;EAEAoK,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAChE,sBAAsB,CAACiE,mBAAmB,CAAC,IAAI,CAAC5O,SAAS,CAAC;EACxE;EAEA6O,eAAeA,CAAA;IACb,IAAI,CAACzD,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC1H,eAAe,GAAG,IAAI;IAC3B,KAAK,IAAIoL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC9O,SAAS,CAACiK,KAAK,CAACrJ,MAAM,EAAEkO,CAAC,EAAE,EAAE;MACpD,IAAI,CAACzD,QAAQ,CAACyD,CAAC,CAAC,GAAG,KAAK;IAC1B;IACA,IAAI,IAAI,CAACrK,IAAI,IAAI3F,QAAQ,CAACmO,OAAO,EAAE;MACjC,IAAI,CAAC5B,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACzK,MAAM,GAAG,CAAC,CAAC,GAAG,KAAK;IAEjD;EACF;EAEAmO,UAAUA,CAAC/J,KAAa;IACtB,IAAI,CAACoG,aAAa,GAAG,KAAK;IAC1B,IAAI,CAAC1H,eAAe,GAAG,IAAI;IAC3B,KAAK,IAAIoL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC9O,SAAS,CAACiK,KAAK,CAACrJ,MAAM,EAAEkO,CAAC,EAAE,EAAE;MACpD,IAAI,CAACzD,QAAQ,CAACyD,CAAC,CAAC,GAAG,KAAK;IAC1B;IACA,IAAI,CAACnI,UAAU,GAAG,IAAI,CAAC3G,SAAS,CAACiK,KAAK,CAACjF,KAAK,CAAC;IAC7C,IAAI,CAACqG,QAAQ,CAACrG,KAAK,CAAC,GAAG,IAAI;EAC7B;EAEAgK,aAAaA,CAAA;IACX,IAAI,CAAC5D,aAAa,GAAG,KAAK;IAC1B,IAAI,CAAC1H,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACiD,UAAU,GAAG,IAAI,CAAC3G,SAAS,CAAC6D,OAAQ;IACzC,IAAI,CAACwH,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAACzK,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;EAChD;EAEAqO,gBAAgBA,CAACjK,KAAa;IAC5B,IAAI,CAACsG,kBAAkB,CAACtG,KAAK,CAAC,GAAG,CAAC,IAAI,CAACsG,kBAAkB,CAACtG,KAAK,CAAC;EAClE;EAEAO,gBAAgBA,CAACiG,IAAU;IACzB,IAAI0D,SAAS,GAAG1D,IAAI,CAAC9F,MAAM,IAAI,QAAQ,GAAG,UAAU,GAAG,QAAQ;IAC/D8F,IAAI,CAAC9F,MAAM,GAAGwJ,SAAuB;IACrC,IAAI,CAACpE,IAAI,CAACgD,GAAG,CAAC,IAAI,CAACtD,WAAW,CAAC2E,iBAAiB,CAAC3D,IAAI,CAAC,CAACyC,SAAS,CAACC,GAAG,IAAG,CACvE,CAAC,CAAC,CAAC;EACL;EAEA3M,cAAcA,CAACM,MAAc;IAC3B,OAAO,IAAI,CAAC8I,sBAAsB,CAACpJ,cAAc,CAACM,MAAM,CAAC;EAC3D;EAEA8B,YAAYA,CAAC6H,IAAU;IACrB,OAAO,IAAI,CAAChB,WAAW,CAAC7G,YAAY,CAAC6H,IAAI,CAAC;EAC5C;EAEA4D,UAAUA,CAACC,QAAc;IACvB,OAAO,IAAI,CAAC7E,WAAW,CAAC8E,YAAY,CAAC,IAAIlB,IAAI,CAACiB,QAAQ,CAAC,CAAC;EAC1D;EAEAnC,0BAA0BA,CAAA;IACxB,IAAI,CAACpC,IAAI,CAACyE,IAAI,GAAG,IAAI,CAAC7E,aAAa,CAACwC,0BAA0B,CAAC,IAAI,CAAClN,SAAS,CAACuE,EAAE,CAAC,CAAC0J,SAAS,CAACC,GAAG,IAAG;MAChG,IAAIA,GAAG,IAAI,CAAC,IAAI,CAAC1K,cAAc,CAACgM,eAAe,CAACtB,GAAG,CAAC,EAAE;QACpD;QACA,IAAI,CAACjG,uBAAuB,GAAG,IAAI,CAACwH,4BAA4B,CAACvB,GAAG,CAAC;MACvE;IACF,CAAC,CAAC;EACJ;EAEAwB,2BAA2BA,CAAA;IACzB,IAAI,CAAC5E,IAAI,CAACyE,IAAI,GAAG,IAAI,CAAC7E,aAAa,CAACgF,2BAA2B,CAAC,IAAI,CAAC1P,SAAS,CAACuE,EAAE,CAAC,CAAC0J,SAAS,CAACC,GAAG,IAAG;MACjG,IAAI,CAACpH,wBAAwB,GAAGoH,GAAG;IACrC,CAAC,CAAC;EACJ;EAEAf,oBAAoBA,CAAA;IAClB,IAAI,CAACrC,IAAI,CAACyE,IAAI,GAAG,IAAI,CAAC9E,cAAc,CAAC0C,oBAAoB,CAAC,IAAI,CAACnN,SAAS,CAACuE,EAAE,CAAC,CAAC0J,SAAS,CAACC,GAAG,IAAG;MAC3F,IAAI,CAACxH,iBAAiB,GAAGwH,GAAG;MAC5B;MACA,IAAI,CAAC3C,UAAU,GAAG2C,GAAG,CAACyB,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACD,CAAC,GAAG,CAACC,CAAC,CAACtE,UAAU,EAAE,CAAC,CAAC;MAC7D,IAAI,CAAC/E,SAAS,GAAG0H,GAAG,CAACyB,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK,CAACD,CAAC,GAAG,CAACC,CAAC,CAACrJ,SAAS,EAAE,CAAC,CAAC;IAC7D,CAAC,CAAC;EACJ;EAGAsJ,yBAAyBA,CAAA;IACvB,OAAO,IAAI,CAAChF,IAAI,CAACgD,GAAG,CAAC,IAAI,CAACrD,cAAc,CAACqF,yBAAyB,CAAC,IAAI,CAAC9P,SAAS,CAACuE,EAAE,CAAC,CAAC0J,SAAS,CAACC,GAAG,IAAG;MACpG,IAAI,CAAC9B,MAAM,GAAG8B,GAAG;MACjB,IAAI,CAAC1H,SAAS,GAAG0H,GAAG,CAAC1H,SAAS;MAC9B,IAAI,CAACkF,uBAAuB,GAAGwC,GAAG,CAAC3C,UAAU;IAC/C,CAAC,CAAC,CAAC;EACL;EAEA9E,aAAaA,CAAA;IACX,OAAO,IAAI,CAAC8E,UAAU,GAAG,IAAI,CAACG,uBAAuB,GAAG,IAAI,CAAClF,SAAS;EACxE;EAEAK,eAAeA,CAACkJ,KAAa;IAC3B,OAAOA,KAAK,GAAGA,KAAK,GAAG,GAAG;EAC5B;EAIAnO,UAAUA,CAAA;IACR,IAAI,CAACyK,UAAU,GAAG,IAAI;IACtB,IAAI,CAACjC,IAAI,GAAG,MAAM;IAClB,IAAI,CAAC5G,cAAc,CAACwM,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAACC,aAAa,CAACC,aAAa,CAAC;EACjF;EAEAC,kBAAkBA,CAAA;IAChB,IAAI,CAAC9D,UAAU,GAAG,KAAK;IACvB,IAAI,CAAC7I,cAAc,CAACwM,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAACC,aAAa,CAACC,aAAa,CAAC;EAClF;EAEAxO,aAAaA,CAAA;IACX,IAAI,CAACyJ,WAAW,CAACiF,IAAI,EAAE;EACzB;EAEA7N,aAAaA,CAAA;IACX,IAAI,CAAC2I,WAAW,CAACkF,IAAI,EAAE;EACzB;EAEAC,oBAAoBA,CAAA;IAClB,IAAI,CAAC9F,MAAM,CAACmE,QAAQ,CAAC,CAAC,eAAe,GAAG,IAAI,CAAC1O,SAAS,CAACuE,EAAE,CAAC,CAAC;EAC7D;EAEA8B,gBAAgBA,CAAA;IACd;IACA,MAAMiK,YAAY,GAAG,IAAI,CAACvF,QAAQ,CAACwF,KAAK,GAAG,CAAC;IAC5C,MAAMC,SAAS,GAAG,CAACF,YAAY,GAAG,CAAC,IAAI,IAAI,CAAChK,UAAU,CAAC1F,MAAM;IAC7D4L,OAAO,CAACC,GAAG,CAAC,CAAC,IAAI,CAACnG,UAAU,CAACkK,SAAS,CAAC,CAAC,CAAC;IACzC;IACA,IAAI,CAAC1F,IAAI,CAACgD,GAAG,CAAC,IAAI,CAACnJ,gBAAgB,CAAC8L,gBAAgB,CAAC,IAAI,CAACzQ,SAAS,CAACuE,EAAE,EAAE,IAAI,CAAC+B,UAAU,CAACkK,SAAS,CAAC,CAAC,CAACxC,IAAI,CAACpP,IAAI,CAAC,CAAC,CAAC,CAAC,CAACqP,SAAS,CAACC,GAAG,IAAG;MAChI1B,OAAO,CAACC,GAAG,CAACyB,GAAG,CAAC;MAChB,IAAIA,GAAG,EAAE;QACP,IAAI,CAACrD,YAAY,CAAC6F,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE,sCAAsC,GAAG,IAAI,CAACvK,UAAU,CAACkK,SAAS;SAC3E,CAAC;QACF,IAAI,CAAC5F,eAAe,CAACkG,iBAAiB,CAAC,IAAI,CAAC;MAC9C;IACF,CAAC,CAAC,CAAC;IACH;EACF;EAEAvK,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAACD,UAAU,CAACyK,SAAS,CAACC,IAAI,IAAIA,IAAI,CAACC,WAAW,EAAE,KAAK,IAAI,CAACjF,aAAa,CAACiF,WAAW,EAAE,CAAC;EACnG;EAEAhM,mBAAmBA,CAACD,KAAa;IAC/BwH,OAAO,CAACC,GAAG,CAACzH,KAAK,CAAC;IAClB,IAAI,CAACgG,OAAO,CAAClK,WAAW,GAAGkE,KAAK;EAClC;EAEAxE,mBAAmBA,CAACoN,KAAU;IAC5BpB,OAAO,CAACC,GAAG,CAACmB,KAAK,CAAC;IAClB,IAAI,CAAC5C,OAAO,CAAClK,WAAW,GAAG8M,KAAK;EAClC;EAEQ6B,4BAA4BA,CAACvB,GAA0B;IAC7D,MAAMgD,MAAM,GAAG,EAA+B;IAC9C,KAAK,MAAMC,GAAG,IAAIjD,GAAG,EAAE;MACrB,IAAIA,GAAG,CAACkD,cAAc,CAACD,GAAG,CAAC,EAAE;QAC3B,MAAME,YAAY,GAAGF,GAAG,CAACG,WAAW,EAAE;QACtCJ,MAAM,CAACG,YAAY,CAAC,GAAGnD,GAAG,CAACiD,GAAG,CAAC;MACjC;IACF;IACA,OAAOD,MAAM;EACf;EAAC,QAAAK,CAAA,G;qBA9RUlH,kBAAkB,EAAApL,EAAA,CAAAuS,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAzS,EAAA,CAAAuS,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA3S,EAAA,CAAAuS,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAA7S,EAAA,CAAAuS,iBAAA,CAAAO,EAAA,CAAAC,aAAA,GAAA/S,EAAA,CAAAuS,iBAAA,CAAAS,EAAA,CAAAC,sBAAA,GAAAjT,EAAA,CAAAuS,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAAnT,EAAA,CAAAuS,iBAAA,CAAAa,EAAA,CAAAC,WAAA,GAAArT,EAAA,CAAAuS,iBAAA,CAAAe,EAAA,CAAAC,eAAA,GAAAvT,EAAA,CAAAuS,iBAAA,CAAAiB,EAAA,CAAAC,gBAAA,GAAAzT,EAAA,CAAAuS,iBAAA,CAAAmB,GAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlBxI,kBAAkB;IAAAyI,SAAA;IAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;QC6rB/BhU,EAxtBA,CAAAI,UAAA,IAAA8T,iCAAA,kBAAoD,IAAAC,iCAAA,iBAwtBX;QAwBjCnU,EAFR,CAAAG,cAAA,gBAAgD,aACnB,UAChB;QAAAH,EAAA,CAAAmC,MAAA,oBAAa;QAAAnC,EAAA,CAAAM,YAAA,EAAM;QACxBN,EAAA,CAAAG,cAAA,cAA2G;QAAtGH,EAAA,CAAAgB,UAAA,mBAAAoT,iDAAA;UAAApU,EAAA,CAAAmB,aAAA,CAAAkT,GAAA;UAAA,OAAArU,EAAA,CAAAsB,WAAA,CAAS2S,GAAA,CAAA/C,kBAAA,EAAoB;QAAA,EAAC;QACvClR,EADI,CAAAM,YAAA,EAA2G,EACzG;QACNN,EAAA,CAAAI,UAAA,IAAAkU,+CAAA,gCACgH;QAEpHtU,EAAA,CAAAM,YAAA,EAAM;;;QAtvBAN,EAAA,CAAAQ,UAAA,UAAAyT,GAAA,CAAAvL,WAAA,CAAkB;QAwtBlB1I,EAAA,CAAAO,SAAA,EAAiB;QAAjBP,EAAA,CAAAQ,UAAA,SAAAyT,GAAA,CAAAvL,WAAA,CAAiB;QA2BC1I,EAAA,CAAAO,SAAA,GAAgB;QAAhBP,EAAA,CAAAQ,UAAA,SAAAyT,GAAA,CAAA7G,UAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}