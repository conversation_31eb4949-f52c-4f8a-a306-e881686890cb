{"ast": null, "code": "//! moment.js locale configuration\n//! locale : Korean [ko]\n//! author : Kyungwook, Park : https://github.com/kyungw00k\n//! author : <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n\n;\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' && typeof require === 'function' ? factory(require('../moment')) : typeof define === 'function' && define.amd ? define(['../moment'], factory) : factory(global.moment);\n})(this, function (moment) {\n  'use strict';\n\n  //! moment.js locale configuration\n  var ko = moment.defineLocale('ko', {\n    months: '1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월'.split('_'),\n    monthsShort: '1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월'.split('_'),\n    weekdays: '일요일_월요일_화요일_수요일_목요일_금요일_토요일'.split('_'),\n    weekdaysShort: '일_월_화_수_목_금_토'.split('_'),\n    weekdaysMin: '일_월_화_수_목_금_토'.split('_'),\n    longDateFormat: {\n      LT: 'A h:mm',\n      LTS: 'A h:mm:ss',\n      L: 'YYYY.MM.DD.',\n      LL: 'YYYY년 MMMM D일',\n      LLL: 'YYYY년 MMMM D일 A h:mm',\n      LLLL: 'YYYY년 MMMM D일 dddd A h:mm',\n      l: 'YYYY.MM.DD.',\n      ll: 'YYYY년 MMMM D일',\n      lll: 'YYYY년 MMMM D일 A h:mm',\n      llll: 'YYYY년 MMMM D일 dddd A h:mm'\n    },\n    calendar: {\n      sameDay: '오늘 LT',\n      nextDay: '내일 LT',\n      nextWeek: 'dddd LT',\n      lastDay: '어제 LT',\n      lastWeek: '지난주 dddd LT',\n      sameElse: 'L'\n    },\n    relativeTime: {\n      future: '%s 후',\n      past: '%s 전',\n      s: '몇 초',\n      ss: '%d초',\n      m: '1분',\n      mm: '%d분',\n      h: '한 시간',\n      hh: '%d시간',\n      d: '하루',\n      dd: '%d일',\n      M: '한 달',\n      MM: '%d달',\n      y: '일 년',\n      yy: '%d년'\n    },\n    dayOfMonthOrdinalParse: /\\d{1,2}(일|월|주)/,\n    ordinal: function (number, period) {\n      switch (period) {\n        case 'd':\n        case 'D':\n        case 'DDD':\n          return number + '일';\n        case 'M':\n          return number + '월';\n        case 'w':\n        case 'W':\n          return number + '주';\n        default:\n          return number;\n      }\n    },\n    meridiemParse: /오전|오후/,\n    isPM: function (token) {\n      return token === '오후';\n    },\n    meridiem: function (hour, minute, isUpper) {\n      return hour < 12 ? '오전' : '오후';\n    }\n  });\n  return ko;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "moment", "ko", "defineLocale", "months", "split", "monthsShort", "weekdays", "weekdaysShort", "weekdaysMin", "longDateFormat", "LT", "LTS", "L", "LL", "LLL", "LLLL", "l", "ll", "lll", "llll", "calendar", "sameDay", "nextDay", "nextWeek", "lastDay", "lastWeek", "same<PERSON><PERSON><PERSON>", "relativeTime", "future", "past", "s", "ss", "m", "mm", "h", "hh", "d", "dd", "M", "MM", "y", "yy", "dayOfMonthOrdinalParse", "ordinal", "number", "period", "meridiemParse", "isPM", "token", "meridiem", "hour", "minute", "isUpper"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/moment/locale/ko.js"], "sourcesContent": ["//! moment.js locale configuration\n//! locale : Korean [ko]\n//! author : Kyungwook, Park : https://github.com/kyungw00k\n//! author : <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n\n;(function (global, factory) {\n   typeof exports === 'object' && typeof module !== 'undefined'\n       && typeof require === 'function' ? factory(require('../moment')) :\n   typeof define === 'function' && define.amd ? define(['../moment'], factory) :\n   factory(global.moment)\n}(this, (function (moment) { 'use strict';\n\n    //! moment.js locale configuration\n\n    var ko = moment.defineLocale('ko', {\n        months: '1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월'.split('_'),\n        monthsShort: '1월_2월_3월_4월_5월_6월_7월_8월_9월_10월_11월_12월'.split(\n            '_'\n        ),\n        weekdays: '일요일_월요일_화요일_수요일_목요일_금요일_토요일'.split('_'),\n        weekdaysShort: '일_월_화_수_목_금_토'.split('_'),\n        weekdaysMin: '일_월_화_수_목_금_토'.split('_'),\n        longDateFormat: {\n            LT: 'A h:mm',\n            LTS: 'A h:mm:ss',\n            L: 'YYYY.MM.DD.',\n            LL: 'YYYY년 MMMM D일',\n            LLL: 'YYYY년 MMMM D일 A h:mm',\n            LLLL: 'YYYY년 MMMM D일 dddd A h:mm',\n            l: 'YYYY.MM.DD.',\n            ll: 'YYYY년 MMMM D일',\n            lll: 'YYYY년 MMMM D일 A h:mm',\n            llll: 'YYYY년 MMMM D일 dddd A h:mm',\n        },\n        calendar: {\n            sameDay: '오늘 LT',\n            nextDay: '내일 LT',\n            nextWeek: 'dddd LT',\n            lastDay: '어제 LT',\n            lastWeek: '지난주 dddd LT',\n            sameElse: 'L',\n        },\n        relativeTime: {\n            future: '%s 후',\n            past: '%s 전',\n            s: '몇 초',\n            ss: '%d초',\n            m: '1분',\n            mm: '%d분',\n            h: '한 시간',\n            hh: '%d시간',\n            d: '하루',\n            dd: '%d일',\n            M: '한 달',\n            MM: '%d달',\n            y: '일 년',\n            yy: '%d년',\n        },\n        dayOfMonthOrdinalParse: /\\d{1,2}(일|월|주)/,\n        ordinal: function (number, period) {\n            switch (period) {\n                case 'd':\n                case 'D':\n                case 'DDD':\n                    return number + '일';\n                case 'M':\n                    return number + '월';\n                case 'w':\n                case 'W':\n                    return number + '주';\n                default:\n                    return number;\n            }\n        },\n        meridiemParse: /오전|오후/,\n        isPM: function (token) {\n            return token === '오후';\n        },\n        meridiem: function (hour, minute, isUpper) {\n            return hour < 12 ? '오전' : '오후';\n        },\n    });\n\n    return ko;\n\n})));\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AAAE,WAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,IACrD,OAAOC,OAAO,KAAK,UAAU,GAAGH,OAAO,CAACG,OAAO,CAAC,WAAW,CAAC,CAAC,GACpE,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,WAAW,CAAC,EAAEJ,OAAO,CAAC,GAC3EA,OAAO,CAACD,MAAM,CAACO,MAAM,CAAC;AACzB,CAAC,EAAC,IAAI,EAAG,UAAUA,MAAM,EAAE;EAAE,YAAY;;EAErC;EAEA,IAAIC,EAAE,GAAGD,MAAM,CAACE,YAAY,CAAC,IAAI,EAAE;IAC/BC,MAAM,EAAE,wCAAwC,CAACC,KAAK,CAAC,GAAG,CAAC;IAC3DC,WAAW,EAAE,wCAAwC,CAACD,KAAK,CACvD,GACJ,CAAC;IACDE,QAAQ,EAAE,6BAA6B,CAACF,KAAK,CAAC,GAAG,CAAC;IAClDG,aAAa,EAAE,eAAe,CAACH,KAAK,CAAC,GAAG,CAAC;IACzCI,WAAW,EAAE,eAAe,CAACJ,KAAK,CAAC,GAAG,CAAC;IACvCK,cAAc,EAAE;MACZC,EAAE,EAAE,QAAQ;MACZC,GAAG,EAAE,WAAW;MAChBC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE,eAAe;MACnBC,GAAG,EAAE,sBAAsB;MAC3BC,IAAI,EAAE,2BAA2B;MACjCC,CAAC,EAAE,aAAa;MAChBC,EAAE,EAAE,eAAe;MACnBC,GAAG,EAAE,sBAAsB;MAC3BC,IAAI,EAAE;IACV,CAAC;IACDC,QAAQ,EAAE;MACNC,OAAO,EAAE,OAAO;MAChBC,OAAO,EAAE,OAAO;MAChBC,QAAQ,EAAE,SAAS;MACnBC,OAAO,EAAE,OAAO;MAChBC,QAAQ,EAAE,aAAa;MACvBC,QAAQ,EAAE;IACd,CAAC;IACDC,YAAY,EAAE;MACVC,MAAM,EAAE,MAAM;MACdC,IAAI,EAAE,MAAM;MACZC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE,KAAK;MACTC,CAAC,EAAE,IAAI;MACPC,EAAE,EAAE,KAAK;MACTC,CAAC,EAAE,MAAM;MACTC,EAAE,EAAE,MAAM;MACVC,CAAC,EAAE,IAAI;MACPC,EAAE,EAAE,KAAK;MACTC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE,KAAK;MACTC,CAAC,EAAE,KAAK;MACRC,EAAE,EAAE;IACR,CAAC;IACDC,sBAAsB,EAAE,gBAAgB;IACxCC,OAAO,EAAE,SAAAA,CAAUC,MAAM,EAAEC,MAAM,EAAE;MAC/B,QAAQA,MAAM;QACV,KAAK,GAAG;QACR,KAAK,GAAG;QACR,KAAK,KAAK;UACN,OAAOD,MAAM,GAAG,GAAG;QACvB,KAAK,GAAG;UACJ,OAAOA,MAAM,GAAG,GAAG;QACvB,KAAK,GAAG;QACR,KAAK,GAAG;UACJ,OAAOA,MAAM,GAAG,GAAG;QACvB;UACI,OAAOA,MAAM;MACrB;IACJ,CAAC;IACDE,aAAa,EAAE,OAAO;IACtBC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;MACnB,OAAOA,KAAK,KAAK,IAAI;IACzB,CAAC;IACDC,QAAQ,EAAE,SAAAA,CAAUC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;MACvC,OAAOF,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI;IAClC;EACJ,CAAC,CAAC;EAEF,OAAOjD,EAAE;AAEb,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}