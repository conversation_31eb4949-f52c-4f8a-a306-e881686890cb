{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, numberAttribute, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i2 from 'primeng/autofocus';\nimport { AutoFocusModule } from 'primeng/autofocus';\nconst _c0 = [\"input\"];\nconst _c1 = (a0, a1, a2) => ({\n  \"p-inputswitch p-component\": true,\n  \"p-inputswitch-checked\": a0,\n  \"p-disabled\": a1,\n  \"p-focus\": a2\n});\nconst INPUTSWITCH_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => InputSwitch),\n  multi: true\n};\n/**\n * InputSwitch is used to select a boolean value.\n * @group Components\n */\nclass InputSwitch {\n  cd;\n  /**\n   * Inline style of the component.\n   * @group Props\n   */\n  style;\n  /**\n   * Style class of the component.\n   * @group Props\n   */\n  styleClass;\n  /**\n   * Index of the element in tabbing order.\n   * @group Props\n   */\n  tabindex;\n  /**\n   * Identifier of the input element.\n   * @group Props\n   */\n  inputId;\n  /**\n   * Name of the input element.\n   * @group Props\n   */\n  name;\n  /**\n   * When present, it specifies that the element should be disabled.\n   * @group Props\n   */\n  disabled;\n  /**\n   * When present, it specifies that the component cannot be edited.\n   * @group Props\n   */\n  readonly;\n  /**\n   * Value in checked state.\n   * @group Props\n   */\n  trueValue = true;\n  /**\n   * Value in unchecked state.\n   * @group Props\n   */\n  falseValue = false;\n  /**\n   * Used to define a string that autocomplete attribute the current element.\n   * @group Props\n   */\n  ariaLabel;\n  /**\n   * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n   * @group Props\n   */\n  ariaLabelledBy;\n  /**\n   * When present, it specifies that the component should automatically get focus on load.\n   * @group Props\n   */\n  autofocus;\n  /**\n   * Callback to invoke when the on value change.\n   * @param {InputSwitchChangeEvent} event - Custom change event.\n   * @group Emits\n   */\n  onChange = new EventEmitter();\n  input;\n  modelValue = false;\n  focused = false;\n  onModelChange = () => {};\n  onModelTouched = () => {};\n  constructor(cd) {\n    this.cd = cd;\n  }\n  onClick(event) {\n    if (!this.disabled && !this.readonly) {\n      this.modelValue = this.checked() ? this.falseValue : this.trueValue;\n      this.onModelChange(this.modelValue);\n      this.onChange.emit({\n        originalEvent: event,\n        checked: this.modelValue\n      });\n      this.input.nativeElement.focus();\n    }\n  }\n  onFocus() {\n    this.focused = true;\n  }\n  onBlur() {\n    this.focused = false;\n    this.onModelTouched();\n  }\n  writeValue(value) {\n    this.modelValue = value;\n    this.cd.markForCheck();\n  }\n  registerOnChange(fn) {\n    this.onModelChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onModelTouched = fn;\n  }\n  setDisabledState(val) {\n    this.disabled = val;\n    this.cd.markForCheck();\n  }\n  checked() {\n    return this.modelValue === this.trueValue;\n  }\n  static ɵfac = function InputSwitch_Factory(t) {\n    return new (t || InputSwitch)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: InputSwitch,\n    selectors: [[\"p-inputSwitch\"]],\n    viewQuery: function InputSwitch_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.input = _t.first);\n      }\n    },\n    hostAttrs: [1, \"p-element\"],\n    inputs: {\n      style: \"style\",\n      styleClass: \"styleClass\",\n      tabindex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabindex\", \"tabindex\", numberAttribute],\n      inputId: \"inputId\",\n      name: \"name\",\n      disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n      readonly: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"readonly\", \"readonly\", booleanAttribute],\n      trueValue: \"trueValue\",\n      falseValue: \"falseValue\",\n      ariaLabel: \"ariaLabel\",\n      ariaLabelledBy: \"ariaLabelledBy\",\n      autofocus: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autofocus\", \"autofocus\", booleanAttribute]\n    },\n    outputs: {\n      onChange: \"onChange\"\n    },\n    features: [i0.ɵɵProvidersFeature([INPUTSWITCH_VALUE_ACCESSOR]), i0.ɵɵInputTransformsFeature],\n    decls: 5,\n    vars: 23,\n    consts: [[\"input\", \"\"], [3, \"click\", \"ngClass\", \"ngStyle\"], [1, \"p-hidden-accessible\"], [\"type\", \"checkbox\", \"role\", \"switch\", \"pAutoFocus\", \"\", 3, \"focus\", \"blur\", \"checked\", \"disabled\", \"autofocus\"], [1, \"p-inputswitch-slider\"]],\n    template: function InputSwitch_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 1);\n        i0.ɵɵlistener(\"click\", function InputSwitch_Template_div_click_0_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onClick($event));\n        });\n        i0.ɵɵelementStart(1, \"div\", 2)(2, \"input\", 3, 0);\n        i0.ɵɵlistener(\"focus\", function InputSwitch_Template_input_focus_2_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onFocus());\n        })(\"blur\", function InputSwitch_Template_input_blur_2_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onBlur());\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(4, \"span\", 4);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassMap(ctx.styleClass);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(19, _c1, ctx.checked(), ctx.disabled, ctx.focused))(\"ngStyle\", ctx.style);\n        i0.ɵɵattribute(\"data-pc-name\", \"inputswitch\")(\"data-pc-section\", \"root\");\n        i0.ɵɵadvance();\n        i0.ɵɵattribute(\"data-pc-section\", \"hiddenInputWrapper\")(\"data-p-hidden-accessible\", true);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"checked\", ctx.checked())(\"disabled\", ctx.disabled)(\"autofocus\", ctx.autofocus);\n        i0.ɵɵattribute(\"id\", ctx.inputId)(\"aria-checked\", ctx.checked())(\"aria-labelledby\", ctx.ariaLabelledBy)(\"aria-label\", ctx.ariaLabel)(\"name\", ctx.name)(\"tabindex\", ctx.tabindex)(\"data-pc-section\", \"hiddenInput\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵattribute(\"data-pc-section\", \"slider\");\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgStyle, i2.AutoFocus],\n    styles: [\"@layer primeng{.p-inputswitch{position:relative;display:inline-block;-webkit-user-select:none;user-select:none}.p-inputswitch-slider{position:absolute;cursor:pointer;inset:0;border:1px solid transparent}.p-inputswitch-slider:before{position:absolute;content:\\\"\\\";top:50%}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputSwitch, [{\n    type: Component,\n    args: [{\n      selector: 'p-inputSwitch',\n      template: `\n        <div\n            [ngClass]=\"{ 'p-inputswitch p-component': true, 'p-inputswitch-checked': checked(), 'p-disabled': disabled, 'p-focus': focused }\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            (click)=\"onClick($event)\"\n            [attr.data-pc-name]=\"'inputswitch'\"\n            [attr.data-pc-section]=\"'root'\"\n        >\n            <div class=\"p-hidden-accessible\" [attr.data-pc-section]=\"'hiddenInputWrapper'\" [attr.data-p-hidden-accessible]=\"true\">\n                <input\n                    #input\n                    [attr.id]=\"inputId\"\n                    type=\"checkbox\"\n                    role=\"switch\"\n                    [checked]=\"checked()\"\n                    [disabled]=\"disabled\"\n                    [attr.aria-checked]=\"checked()\"\n                    [attr.aria-labelledby]=\"ariaLabelledBy\"\n                    [attr.aria-label]=\"ariaLabel\"\n                    [attr.name]=\"name\"\n                    [attr.tabindex]=\"tabindex\"\n                    (focus)=\"onFocus()\"\n                    (blur)=\"onBlur()\"\n                    [attr.data-pc-section]=\"'hiddenInput'\"\n                    pAutoFocus\n                    [autofocus]=\"autofocus\"\n                />\n            </div>\n            <span class=\"p-inputswitch-slider\" [attr.data-pc-section]=\"'slider'\"></span>\n        </div>\n    `,\n      providers: [INPUTSWITCH_VALUE_ACCESSOR],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      host: {\n        class: 'p-element'\n      },\n      styles: [\"@layer primeng{.p-inputswitch{position:relative;display:inline-block;-webkit-user-select:none;user-select:none}.p-inputswitch-slider{position:absolute;cursor:pointer;inset:0;border:1px solid transparent}.p-inputswitch-slider:before{position:absolute;content:\\\"\\\";top:50%}}\\n\"]\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }], {\n    style: [{\n      type: Input\n    }],\n    styleClass: [{\n      type: Input\n    }],\n    tabindex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    inputId: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    readonly: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    trueValue: [{\n      type: Input\n    }],\n    falseValue: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input\n    }],\n    ariaLabelledBy: [{\n      type: Input\n    }],\n    autofocus: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    onChange: [{\n      type: Output\n    }],\n    input: [{\n      type: ViewChild,\n      args: ['input']\n    }]\n  });\n})();\nclass InputSwitchModule {\n  static ɵfac = function InputSwitchModule_Factory(t) {\n    return new (t || InputSwitchModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: InputSwitchModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    imports: [CommonModule, AutoFocusModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(InputSwitchModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CommonModule, AutoFocusModule],\n      exports: [InputSwitch],\n      declarations: [InputSwitch]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { INPUTSWITCH_VALUE_ACCESSOR, InputSwitch, InputSwitchModule };", "map": {"version": 3, "names": ["i1", "CommonModule", "i0", "forwardRef", "EventEmitter", "numberAttribute", "booleanAttribute", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "Output", "ViewChild", "NgModule", "NG_VALUE_ACCESSOR", "i2", "AutoFocusModule", "_c0", "_c1", "a0", "a1", "a2", "INPUTSWITCH_VALUE_ACCESSOR", "provide", "useExisting", "InputSwitch", "multi", "cd", "style", "styleClass", "tabindex", "inputId", "name", "disabled", "readonly", "trueValue", "falseValue", "aria<PERSON><PERSON><PERSON>", "ariaLabelledBy", "autofocus", "onChange", "input", "modelValue", "focused", "onModelChange", "onModelTouched", "constructor", "onClick", "event", "checked", "emit", "originalEvent", "nativeElement", "focus", "onFocus", "onBlur", "writeValue", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "val", "ɵfac", "InputSwitch_Factory", "t", "ɵɵdirectiveInject", "ChangeDetectorRef", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "InputSwitch_Query", "rf", "ctx", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "outputs", "features", "ɵɵProvidersFeature", "ɵɵInputTransformsFeature", "decls", "vars", "consts", "template", "InputSwitch_Template", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "InputSwitch_Template_div_click_0_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "InputSwitch_Template_input_focus_2_listener", "InputSwitch_Template_input_blur_2_listener", "ɵɵelementEnd", "ɵɵelement", "ɵɵclassMap", "ɵɵproperty", "ɵɵpureFunction3", "ɵɵattribute", "ɵɵadvance", "dependencies", "Ng<PERSON><PERSON>", "NgStyle", "AutoFocus", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "providers", "OnPush", "None", "host", "class", "transform", "InputSwitchModule", "InputSwitchModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "declarations"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/primeng/fesm2022/primeng-inputswitch.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, numberAttribute, booleanAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i2 from 'primeng/autofocus';\nimport { AutoFocusModule } from 'primeng/autofocus';\n\nconst INPUTSWITCH_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => InputSwitch),\n    multi: true\n};\n/**\n * InputSwitch is used to select a boolean value.\n * @group Components\n */\nclass InputSwitch {\n    cd;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    tabindex;\n    /**\n     * Identifier of the input element.\n     * @group Props\n     */\n    inputId;\n    /**\n     * Name of the input element.\n     * @group Props\n     */\n    name;\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    disabled;\n    /**\n     * When present, it specifies that the component cannot be edited.\n     * @group Props\n     */\n    readonly;\n    /**\n     * Value in checked state.\n     * @group Props\n     */\n    trueValue = true;\n    /**\n     * Value in unchecked state.\n     * @group Props\n     */\n    falseValue = false;\n    /**\n     * Used to define a string that autocomplete attribute the current element.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    autofocus;\n    /**\n     * Callback to invoke when the on value change.\n     * @param {InputSwitchChangeEvent} event - Custom change event.\n     * @group Emits\n     */\n    onChange = new EventEmitter();\n    input;\n    modelValue = false;\n    focused = false;\n    onModelChange = () => { };\n    onModelTouched = () => { };\n    constructor(cd) {\n        this.cd = cd;\n    }\n    onClick(event) {\n        if (!this.disabled && !this.readonly) {\n            this.modelValue = this.checked() ? this.falseValue : this.trueValue;\n            this.onModelChange(this.modelValue);\n            this.onChange.emit({\n                originalEvent: event,\n                checked: this.modelValue\n            });\n            this.input.nativeElement.focus();\n        }\n    }\n    onFocus() {\n        this.focused = true;\n    }\n    onBlur() {\n        this.focused = false;\n        this.onModelTouched();\n    }\n    writeValue(value) {\n        this.modelValue = value;\n        this.cd.markForCheck();\n    }\n    registerOnChange(fn) {\n        this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onModelTouched = fn;\n    }\n    setDisabledState(val) {\n        this.disabled = val;\n        this.cd.markForCheck();\n    }\n    checked() {\n        return this.modelValue === this.trueValue;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: InputSwitch, deps: [{ token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"17.3.7\", type: InputSwitch, selector: \"p-inputSwitch\", inputs: { style: \"style\", styleClass: \"styleClass\", tabindex: [\"tabindex\", \"tabindex\", numberAttribute], inputId: \"inputId\", name: \"name\", disabled: [\"disabled\", \"disabled\", booleanAttribute], readonly: [\"readonly\", \"readonly\", booleanAttribute], trueValue: \"trueValue\", falseValue: \"falseValue\", ariaLabel: \"ariaLabel\", ariaLabelledBy: \"ariaLabelledBy\", autofocus: [\"autofocus\", \"autofocus\", booleanAttribute] }, outputs: { onChange: \"onChange\" }, host: { classAttribute: \"p-element\" }, providers: [INPUTSWITCH_VALUE_ACCESSOR], viewQueries: [{ propertyName: \"input\", first: true, predicate: [\"input\"], descendants: true }], ngImport: i0, template: `\n        <div\n            [ngClass]=\"{ 'p-inputswitch p-component': true, 'p-inputswitch-checked': checked(), 'p-disabled': disabled, 'p-focus': focused }\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            (click)=\"onClick($event)\"\n            [attr.data-pc-name]=\"'inputswitch'\"\n            [attr.data-pc-section]=\"'root'\"\n        >\n            <div class=\"p-hidden-accessible\" [attr.data-pc-section]=\"'hiddenInputWrapper'\" [attr.data-p-hidden-accessible]=\"true\">\n                <input\n                    #input\n                    [attr.id]=\"inputId\"\n                    type=\"checkbox\"\n                    role=\"switch\"\n                    [checked]=\"checked()\"\n                    [disabled]=\"disabled\"\n                    [attr.aria-checked]=\"checked()\"\n                    [attr.aria-labelledby]=\"ariaLabelledBy\"\n                    [attr.aria-label]=\"ariaLabel\"\n                    [attr.name]=\"name\"\n                    [attr.tabindex]=\"tabindex\"\n                    (focus)=\"onFocus()\"\n                    (blur)=\"onBlur()\"\n                    [attr.data-pc-section]=\"'hiddenInput'\"\n                    pAutoFocus\n                    [autofocus]=\"autofocus\"\n                />\n            </div>\n            <span class=\"p-inputswitch-slider\" [attr.data-pc-section]=\"'slider'\"></span>\n        </div>\n    `, isInline: true, styles: [\"@layer primeng{.p-inputswitch{position:relative;display:inline-block;-webkit-user-select:none;user-select:none}.p-inputswitch-slider{position:absolute;cursor:pointer;inset:0;border:1px solid transparent}.p-inputswitch-slider:before{position:absolute;content:\\\"\\\";top:50%}}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"directive\", type: i2.AutoFocus, selector: \"[pAutoFocus]\", inputs: [\"autofocus\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: InputSwitch, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-inputSwitch', template: `\n        <div\n            [ngClass]=\"{ 'p-inputswitch p-component': true, 'p-inputswitch-checked': checked(), 'p-disabled': disabled, 'p-focus': focused }\"\n            [ngStyle]=\"style\"\n            [class]=\"styleClass\"\n            (click)=\"onClick($event)\"\n            [attr.data-pc-name]=\"'inputswitch'\"\n            [attr.data-pc-section]=\"'root'\"\n        >\n            <div class=\"p-hidden-accessible\" [attr.data-pc-section]=\"'hiddenInputWrapper'\" [attr.data-p-hidden-accessible]=\"true\">\n                <input\n                    #input\n                    [attr.id]=\"inputId\"\n                    type=\"checkbox\"\n                    role=\"switch\"\n                    [checked]=\"checked()\"\n                    [disabled]=\"disabled\"\n                    [attr.aria-checked]=\"checked()\"\n                    [attr.aria-labelledby]=\"ariaLabelledBy\"\n                    [attr.aria-label]=\"ariaLabel\"\n                    [attr.name]=\"name\"\n                    [attr.tabindex]=\"tabindex\"\n                    (focus)=\"onFocus()\"\n                    (blur)=\"onBlur()\"\n                    [attr.data-pc-section]=\"'hiddenInput'\"\n                    pAutoFocus\n                    [autofocus]=\"autofocus\"\n                />\n            </div>\n            <span class=\"p-inputswitch-slider\" [attr.data-pc-section]=\"'slider'\"></span>\n        </div>\n    `, providers: [INPUTSWITCH_VALUE_ACCESSOR], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        class: 'p-element'\n                    }, styles: [\"@layer primeng{.p-inputswitch{position:relative;display:inline-block;-webkit-user-select:none;user-select:none}.p-inputswitch-slider{position:absolute;cursor:pointer;inset:0;border:1px solid transparent}.p-inputswitch-slider:before{position:absolute;content:\\\"\\\";top:50%}}\\n\"] }]\n        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }], propDecorators: { style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], tabindex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], inputId: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], readonly: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], trueValue: [{\n                type: Input\n            }], falseValue: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input\n            }], ariaLabelledBy: [{\n                type: Input\n            }], autofocus: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], onChange: [{\n                type: Output\n            }], input: [{\n                type: ViewChild,\n                args: ['input']\n            }] } });\nclass InputSwitchModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: InputSwitchModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.7\", ngImport: i0, type: InputSwitchModule, declarations: [InputSwitch], imports: [CommonModule, AutoFocusModule], exports: [InputSwitch] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: InputSwitchModule, imports: [CommonModule, AutoFocusModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: InputSwitchModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule, AutoFocusModule],\n                    exports: [InputSwitch],\n                    declarations: [InputSwitch]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { INPUTSWITCH_VALUE_ACCESSOR, InputSwitch, InputSwitchModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,YAAY,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACtL,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,eAAe,QAAQ,mBAAmB;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;EAAA;EAAA,yBAAAF,EAAA;EAAA,cAAAC,EAAA;EAAA,WAAAC;AAAA;AAEpD,MAAMC,0BAA0B,GAAG;EAC/BC,OAAO,EAAET,iBAAiB;EAC1BU,WAAW,EAAErB,UAAU,CAAC,MAAMsB,WAAW,CAAC;EAC1CC,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMD,WAAW,CAAC;EACdE,EAAE;EACF;AACJ;AACA;AACA;EACIC,KAAK;EACL;AACJ;AACA;AACA;EACIC,UAAU;EACV;AACJ;AACA;AACA;EACIC,QAAQ;EACR;AACJ;AACA;AACA;EACIC,OAAO;EACP;AACJ;AACA;AACA;EACIC,IAAI;EACJ;AACJ;AACA;AACA;EACIC,QAAQ;EACR;AACJ;AACA;AACA;EACIC,QAAQ;EACR;AACJ;AACA;AACA;EACIC,SAAS,GAAG,IAAI;EAChB;AACJ;AACA;AACA;EACIC,UAAU,GAAG,KAAK;EAClB;AACJ;AACA;AACA;EACIC,SAAS;EACT;AACJ;AACA;AACA;EACIC,cAAc;EACd;AACJ;AACA;AACA;EACIC,SAAS;EACT;AACJ;AACA;AACA;AACA;EACIC,QAAQ,GAAG,IAAIpC,YAAY,CAAC,CAAC;EAC7BqC,KAAK;EACLC,UAAU,GAAG,KAAK;EAClBC,OAAO,GAAG,KAAK;EACfC,aAAa,GAAGA,CAAA,KAAM,CAAE,CAAC;EACzBC,cAAc,GAAGA,CAAA,KAAM,CAAE,CAAC;EAC1BC,WAAWA,CAACnB,EAAE,EAAE;IACZ,IAAI,CAACA,EAAE,GAAGA,EAAE;EAChB;EACAoB,OAAOA,CAACC,KAAK,EAAE;IACX,IAAI,CAAC,IAAI,CAACf,QAAQ,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;MAClC,IAAI,CAACQ,UAAU,GAAG,IAAI,CAACO,OAAO,CAAC,CAAC,GAAG,IAAI,CAACb,UAAU,GAAG,IAAI,CAACD,SAAS;MACnE,IAAI,CAACS,aAAa,CAAC,IAAI,CAACF,UAAU,CAAC;MACnC,IAAI,CAACF,QAAQ,CAACU,IAAI,CAAC;QACfC,aAAa,EAAEH,KAAK;QACpBC,OAAO,EAAE,IAAI,CAACP;MAClB,CAAC,CAAC;MACF,IAAI,CAACD,KAAK,CAACW,aAAa,CAACC,KAAK,CAAC,CAAC;IACpC;EACJ;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACX,OAAO,GAAG,IAAI;EACvB;EACAY,MAAMA,CAAA,EAAG;IACL,IAAI,CAACZ,OAAO,GAAG,KAAK;IACpB,IAAI,CAACE,cAAc,CAAC,CAAC;EACzB;EACAW,UAAUA,CAACC,KAAK,EAAE;IACd,IAAI,CAACf,UAAU,GAAGe,KAAK;IACvB,IAAI,CAAC9B,EAAE,CAAC+B,YAAY,CAAC,CAAC;EAC1B;EACAC,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAAChB,aAAa,GAAGgB,EAAE;EAC3B;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAACf,cAAc,GAAGe,EAAE;EAC5B;EACAE,gBAAgBA,CAACC,GAAG,EAAE;IAClB,IAAI,CAAC9B,QAAQ,GAAG8B,GAAG;IACnB,IAAI,CAACpC,EAAE,CAAC+B,YAAY,CAAC,CAAC;EAC1B;EACAT,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACP,UAAU,KAAK,IAAI,CAACP,SAAS;EAC7C;EACA,OAAO6B,IAAI,YAAAC,oBAAAC,CAAA;IAAA,YAAAA,CAAA,IAAwFzC,WAAW,EAArBvB,EAAE,CAAAiE,iBAAA,CAAqCjE,EAAE,CAACkE,iBAAiB;EAAA;EACpJ,OAAOC,IAAI,kBAD8EnE,EAAE,CAAAoE,iBAAA;IAAAC,IAAA,EACJ9C,WAAW;IAAA+C,SAAA;IAAAC,SAAA,WAAAC,kBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADTzE,EAAE,CAAA2E,WAAA,CAAA5D,GAAA;MAAA;MAAA,IAAA0D,EAAA;QAAA,IAAAG,EAAA;QAAF5E,EAAE,CAAA6E,cAAA,CAAAD,EAAA,GAAF5E,EAAE,CAAA8E,WAAA,QAAAJ,GAAA,CAAAnC,KAAA,GAAAqC,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAC,SAAA;IAAAC,MAAA;MAAAvD,KAAA;MAAAC,UAAA;MAAAC,QAAA,GAAF5B,EAAE,CAAAkF,YAAA,CAAAC,0BAAA,0BAC2HhF,eAAe;MAAA0B,OAAA;MAAAC,IAAA;MAAAC,QAAA,GAD5I/B,EAAE,CAAAkF,YAAA,CAAAC,0BAAA,0BACkN/E,gBAAgB;MAAA4B,QAAA,GADpOhC,EAAE,CAAAkF,YAAA,CAAAC,0BAAA,0BACwQ/E,gBAAgB;MAAA6B,SAAA;MAAAC,UAAA;MAAAC,SAAA;MAAAC,cAAA;MAAAC,SAAA,GAD1RrC,EAAE,CAAAkF,YAAA,CAAAC,0BAAA,4BAC6a/E,gBAAgB;IAAA;IAAAgF,OAAA;MAAA9C,QAAA;IAAA;IAAA+C,QAAA,GAD/brF,EAAE,CAAAsF,kBAAA,CACuhB,CAAClE,0BAA0B,CAAC,GADrjBpB,EAAE,CAAAuF,wBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,qBAAAnB,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAA,MAAAoB,GAAA,GAAF7F,EAAE,CAAA8F,gBAAA;QAAF9F,EAAE,CAAA+F,cAAA,YASvF,CAAC;QAToF/F,EAAE,CAAAgG,UAAA,mBAAAC,0CAAAC,MAAA;UAAFlG,EAAE,CAAAmG,aAAA,CAAAN,GAAA;UAAA,OAAF7F,EAAE,CAAAoG,WAAA,CAM1E1B,GAAA,CAAA7B,OAAA,CAAAqD,MAAc,CAAC;QAAA,EAAC;QANwDlG,EAAE,CAAA+F,cAAA,YAUkC,CAAC,iBAkBjH,CAAC;QA5B2E/F,EAAE,CAAAgG,UAAA,mBAAAK,4CAAA;UAAFrG,EAAE,CAAAmG,aAAA,CAAAN,GAAA;UAAA,OAAF7F,EAAE,CAAAoG,WAAA,CAuBlE1B,GAAA,CAAAtB,OAAA,CAAQ,CAAC;QAAA,EAAC,kBAAAkD,2CAAA;UAvBsDtG,EAAE,CAAAmG,aAAA,CAAAN,GAAA;UAAA,OAAF7F,EAAE,CAAAoG,WAAA,CAwBnE1B,GAAA,CAAArB,MAAA,CAAO,CAAC;QAAA,EAAC;QAxBwDrD,EAAE,CAAAuG,YAAA,CA4B9E,CAAC,CACD,CAAC;QA7B2EvG,EAAE,CAAAwG,SAAA,aA8BR,CAAC;QA9BKxG,EAAE,CAAAuG,YAAA,CA+BlF,CAAC;MAAA;MAAA,IAAA9B,EAAA;QA/B+EzE,EAAE,CAAAyG,UAAA,CAAA/B,GAAA,CAAA/C,UAKhE,CAAC;QAL6D3B,EAAE,CAAA0G,UAAA,YAAF1G,EAAE,CAAA2G,eAAA,KAAA3F,GAAA,EAAA0D,GAAA,CAAA3B,OAAA,IAAA2B,GAAA,CAAA3C,QAAA,EAAA2C,GAAA,CAAAjC,OAAA,CAG6C,CAAC,YAAAiC,GAAA,CAAAhD,KACjH,CAAC;QAJgE1B,EAAE,CAAA4G,WAAA;QAAF5G,EAAE,CAAA6G,SAAA,CAUN,CAAC;QAVG7G,EAAE,CAAA4G,WAAA;QAAF5G,EAAE,CAAA6G,SAAA,CAgBvD,CAAC;QAhBoD7G,EAAE,CAAA0G,UAAA,YAAAhC,GAAA,CAAA3B,OAAA,EAgBvD,CAAC,aAAA2B,GAAA,CAAA3C,QACD,CAAC,cAAA2C,GAAA,CAAArC,SAUC,CAAC;QA3BkDrC,EAAE,CAAA4G,WAAA,OAAAlC,GAAA,CAAA7C,OAAA,kBAAA6C,GAAA,CAAA3B,OAAA,uBAAA2B,GAAA,CAAAtC,cAAA,gBAAAsC,GAAA,CAAAvC,SAAA,UAAAuC,GAAA,CAAA5C,IAAA,cAAA4C,GAAA,CAAA9C,QAAA;QAAF5B,EAAE,CAAA6G,SAAA,EA8BhB,CAAC;QA9Ba7G,EAAE,CAAA4G,WAAA;MAAA;IAAA;IAAAE,YAAA,GAgCkQhH,EAAE,CAACiH,OAAO,EAAoFjH,EAAE,CAACkH,OAAO,EAA2EnG,EAAE,CAACoG,SAAS;IAAAC,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AAChiB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAlC6FrH,EAAE,CAAAsH,iBAAA,CAkCJ/F,WAAW,EAAc,CAAC;IACzG8C,IAAI,EAAEhE,SAAS;IACfkH,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAe;MAAE7B,QAAQ,EAAG;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;MAAE8B,SAAS,EAAE,CAACrG,0BAA0B,CAAC;MAAEgG,eAAe,EAAE9G,uBAAuB,CAACoH,MAAM;MAAEP,aAAa,EAAE5G,iBAAiB,CAACoH,IAAI;MAAEC,IAAI,EAAE;QACtHC,KAAK,EAAE;MACX,CAAC;MAAEX,MAAM,EAAE,CAAC,oRAAoR;IAAE,CAAC;EAC/S,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7C,IAAI,EAAErE,EAAE,CAACkE;EAAkB,CAAC,CAAC,EAAkB;IAAExC,KAAK,EAAE,CAAC;MAC9E2C,IAAI,EAAE7D;IACV,CAAC,CAAC;IAAEmB,UAAU,EAAE,CAAC;MACb0C,IAAI,EAAE7D;IACV,CAAC,CAAC;IAAEoB,QAAQ,EAAE,CAAC;MACXyC,IAAI,EAAE7D,KAAK;MACX+G,IAAI,EAAE,CAAC;QAAEO,SAAS,EAAE3H;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAE0B,OAAO,EAAE,CAAC;MACVwC,IAAI,EAAE7D;IACV,CAAC,CAAC;IAAEsB,IAAI,EAAE,CAAC;MACPuC,IAAI,EAAE7D;IACV,CAAC,CAAC;IAAEuB,QAAQ,EAAE,CAAC;MACXsC,IAAI,EAAE7D,KAAK;MACX+G,IAAI,EAAE,CAAC;QAAEO,SAAS,EAAE1H;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE4B,QAAQ,EAAE,CAAC;MACXqC,IAAI,EAAE7D,KAAK;MACX+G,IAAI,EAAE,CAAC;QAAEO,SAAS,EAAE1H;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE6B,SAAS,EAAE,CAAC;MACZoC,IAAI,EAAE7D;IACV,CAAC,CAAC;IAAE0B,UAAU,EAAE,CAAC;MACbmC,IAAI,EAAE7D;IACV,CAAC,CAAC;IAAE2B,SAAS,EAAE,CAAC;MACZkC,IAAI,EAAE7D;IACV,CAAC,CAAC;IAAE4B,cAAc,EAAE,CAAC;MACjBiC,IAAI,EAAE7D;IACV,CAAC,CAAC;IAAE6B,SAAS,EAAE,CAAC;MACZgC,IAAI,EAAE7D,KAAK;MACX+G,IAAI,EAAE,CAAC;QAAEO,SAAS,EAAE1H;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkC,QAAQ,EAAE,CAAC;MACX+B,IAAI,EAAE5D;IACV,CAAC,CAAC;IAAE8B,KAAK,EAAE,CAAC;MACR8B,IAAI,EAAE3D,SAAS;MACf6G,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMQ,iBAAiB,CAAC;EACpB,OAAOjE,IAAI,YAAAkE,0BAAAhE,CAAA;IAAA,YAAAA,CAAA,IAAwF+D,iBAAiB;EAAA;EACpH,OAAOE,IAAI,kBA1G8EjI,EAAE,CAAAkI,gBAAA;IAAA7D,IAAA,EA0GS0D;EAAiB;EACrH,OAAOI,IAAI,kBA3G8EnI,EAAE,CAAAoI,gBAAA;IAAAC,OAAA,GA2GsCtI,YAAY,EAAEe,eAAe;EAAA;AAClK;AACA;EAAA,QAAAuG,SAAA,oBAAAA,SAAA,KA7G6FrH,EAAE,CAAAsH,iBAAA,CA6GJS,iBAAiB,EAAc,CAAC;IAC/G1D,IAAI,EAAE1D,QAAQ;IACd4G,IAAI,EAAE,CAAC;MACCc,OAAO,EAAE,CAACtI,YAAY,EAAEe,eAAe,CAAC;MACxCwH,OAAO,EAAE,CAAC/G,WAAW,CAAC;MACtBgH,YAAY,EAAE,CAAChH,WAAW;IAC9B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASH,0BAA0B,EAAEG,WAAW,EAAEwG,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}