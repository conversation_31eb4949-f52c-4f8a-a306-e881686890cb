{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { DialogService } from 'primeng/dynamicdialog';\nimport { take } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport { LibraryFileActionsDialogComponent } from '../library-file-actions-dialog/library-file-actions-dialog.component';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/library.service\";\nimport * as i2 from \"src/app/core/services/general.service\";\nimport * as i3 from \"primeng/dynamicdialog\";\nimport * as i4 from \"src/app/core/services/toast.service\";\nimport * as i5 from \"src/app/core/services/auth.service\";\nimport * as i6 from \"@angular/common/http\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"primeng/chip\";\nimport * as i9 from \"../../../../../shared/layout/g-level-circle/g-level-circle.component\";\nimport * as i10 from \"primeng/menu\";\nimport * as i11 from \"primeng/tooltip\";\nconst _c0 = (a0, a1) => ({\n  \"checked\": a0,\n  \"purple-hover\": a1\n});\nconst _c1 = (a0, a1) => ({\n  \"bg-grad-purple\": a0,\n  \"bg-grad-blue\": a1\n});\nfunction LibraryFileRowItemComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"g-level-circle\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const level_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", level_r2);\n  }\n}\nfunction LibraryFileRowItemComponent_img_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 21);\n  }\n}\nfunction LibraryFileRowItemComponent_p_chip_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-chip\")(1, \"span\", 22);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r3 = ctx.$implicit;\n    i0.ɵɵclassMapInterpolate1(\"class-rating \", category_r3.toLowerCase(), \" border-round-xl px-0\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r3);\n  }\n}\nfunction LibraryFileRowItemComponent_div_17_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 24);\n    i0.ɵɵpipe(2, \"slice\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pTooltip\", i0.ɵɵpipeBind2(2, 2, ctx_r3.classroomFile.categories, 1).join(\", \"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" +\", ctx_r3.classroomFile.categories.length - 1, \" \");\n  }\n}\nfunction LibraryFileRowItemComponent_div_17_ng_template_2_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 24);\n    i0.ɵɵpipe(1, \"slice\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"pTooltip\", i0.ɵɵpipeBind2(1, 2, ctx_r3.classroomFile.categories, 2).join(\", \"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" +\", ctx_r3.classroomFile.categories.length - 2, \" \");\n  }\n}\nfunction LibraryFileRowItemComponent_div_17_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, LibraryFileRowItemComponent_div_17_ng_template_2_span_0_Template, 3, 5, \"span\", 25);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.classroomFile.categories.length > 2);\n  }\n}\nfunction LibraryFileRowItemComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, LibraryFileRowItemComponent_div_17_ng_container_1_Template, 4, 5, \"ng-container\", 23)(2, LibraryFileRowItemComponent_div_17_ng_template_2_Template, 1, 1, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notMobileBlock_r5 = i0.ɵɵreference(3);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.isLarge)(\"ngIfElse\", notMobileBlock_r5);\n  }\n}\nfunction LibraryFileRowItemComponent_div_19_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 29);\n  }\n}\nfunction LibraryFileRowItemComponent_div_19_img_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 30);\n  }\n}\nfunction LibraryFileRowItemComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function LibraryFileRowItemComponent_div_19_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.checkLibraryItem(ctx_r3.checkLibMenu));\n    });\n    i0.ɵɵtemplate(1, LibraryFileRowItemComponent_div_19_img_1_Template, 1, 0, \"img\", 27)(2, LibraryFileRowItemComponent_div_19_img_2_Template, 1, 0, \"img\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.checked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.checked);\n  }\n}\nfunction LibraryFileRowItemComponent_img_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 31);\n  }\n}\nexport let LibraryFileRowItemComponent = /*#__PURE__*/(() => {\n  class LibraryFileRowItemComponent {\n    constructor(libraryService, generalService, dialogService, toastService, authService, http) {\n      this.libraryService = libraryService;\n      this.generalService = generalService;\n      this.dialogService = dialogService;\n      this.toastService = toastService;\n      this.authService = authService;\n      this.http = http;\n      this.classroom = {};\n      this.classroomFile = {};\n      this.appendDialogActionsTo = 'allFiles';\n      this.showGroupActions = false;\n      this.hasLimitedOptions = false;\n      this.availableActions = [];\n      this.isSmallScreen = false;\n      this.showSharedWithUsers = true;\n      this.isInTab = false;\n      this.fileDeleted = new EventEmitter();\n      this.libraryChecked = new EventEmitter();\n      this.fileUnshared = new EventEmitter();\n      this.StudentIdsToSend = [];\n      this.isDisabled = false;\n      this.isDisabledWholeClass = false;\n      this.moveFileId = 0;\n      this.moveFolderId = 0;\n      this.isMoveRadioCheked = false;\n      this.showRename = false;\n      this.refs = [];\n      this.subs = new SubSink();\n      this.checked = false;\n      this.sharedWithUsers = [];\n      this.menuItems = [];\n      this.isLarge = false;\n      this.hasMultipleCategoriesSlice = 1;\n    }\n    ngOnInit() {\n      if (this.showSharedWithUsers) {\n        this.getUsersLibraryHasBeenSharedWith();\n      }\n      this.subs.sink = this.libraryService.showGroupActions.subscribe(res => {\n        if (!res) {\n          this.checked = false;\n        }\n        // if (this.libraryService.isPreviewingFromChat()) {\n        //   this.checked = true;\n        // }\n      });\n      this.subs.add(this.generalService.deviceKind.pipe().subscribe(res => {\n        if (res) {\n          if (res.w768up) {\n            this.isLarge = true;\n            this.hasMultipleCategoriesSlice = 2;\n          }\n        }\n      }));\n      this.prepareMenuItemsForItem(this.classroomFile);\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n    }\n    onSelect(studentId) {\n      this.isDisabled = false;\n      if (this.StudentIdsToSend.indexOf(studentId) !== -1) this.StudentIdsToSend.splice(this.StudentIdsToSend.indexOf(studentId), 1);else this.StudentIdsToSend.push(studentId);\n      if (this.isDisabledWholeClass && this.StudentIdsToSend.length === 0) this.isDisabledWholeClass = false;else this.isDisabledWholeClass = true;\n    }\n    showMoveFilePopup(fileId) {\n      document.getElementById('lib-file-share-menu-' + fileId).style.display = 'none';\n      document.getElementById('lib-file-move-menu-' + fileId).style.display = 'block';\n      document.getElementById('lib-file-move-menu-' + fileId).style.top = '55%';\n    }\n    // openSharedByFileMenu(fileId: any) {\n    //   alert(fileId);\n    //   [].forEach.call(document.getElementsByClassName('lib-file-sharedby-menu'), (el: any) => {\n    //     el.style.display = 'none';\n    //   })\n    //   setTimeout(() => {\n    //     document.getElementById('lib-file-sharedby-menu-' + fileId)!.style.display = 'flex';\n    //     document.getElementById('lib-file-sharedby-menu-' + fileId)!.style.top = (880 + document.getElementById('lib-file-sharedby-menu-' + fileId)!.offsetTop) + \"px\";\n    //   }, 100);\n    //   // this.showShare = true;\n    // }\n    // showSharedByMoveFilePopup(fileId: any) {\n    //   document.getElementById('lib-file-sharedby-move-menu-' + fileId)!.style.display = 'block';\n    //   document.getElementById('lib-file-sharedby-move-menu-' + fileId)!.style.top = '55%';\n    // }\n    getFileName(path) {\n      if (path) {\n        let n = path.lastIndexOf(\"/\");\n        if (n === -1) n = path.lastIndexOf(\"\\\\\");\n        let result = path.substring(n + 1);\n        return result;\n      }\n      return path;\n    }\n    prepareMenuItemsForItem(item) {\n      let context = item;\n      const menuItems = [{\n        label: 'View',\n        icon: 'pi pi-fw pi-external-link',\n        command: event => {\n          this.openFileInNewTab(context.path);\n        }\n      }, {\n        label: 'Copy URL',\n        icon: 'pi pi-fw pi-copy',\n        command: event => {\n          this.copyURL(context.path);\n        }\n      }, {\n        label: 'Download',\n        icon: 'pi pi-fw pi-cloud-download',\n        command: event => {\n          this.downloadFile(context.path);\n        }\n      }, {\n        label: 'Share',\n        icon: 'pi pi-fw pi-share-alt',\n        command: event => {\n          if (this.authService.isStudent) {\n            this.shareWithTeacher(context);\n          } else {\n            const currentSelectedClassroom = this.libraryService.getCurrentSelectedClassroom();\n            if (Object.keys(currentSelectedClassroom).length > 0) {\n              const classroomStudents = this.libraryService.getCurrentSelectedClassroom().classroomStudents;\n              // if only one student, share immediately\n              if (classroomStudents.length === 1 && this.authService.isTeacher) {\n                let send = {\n                  fileIds: [context.fileId],\n                  classroomId: this.libraryService.getCurrentSelectedClassroom().id,\n                  studentIds: [classroomStudents[0].aspUserId]\n                };\n                this.sendShareToApi(send);\n              }\n            } else {\n              this.openDialogWithComponent(LibraryFileActionsDialogComponent, {\n                action: 'shareWith',\n                dialogsubTitle: 'Share \"' + this.getFileName(context.name) + '\" with...',\n                dialogTitle: 'Share file with...',\n                libraryFile: context\n              });\n            }\n            console.log(this.libraryService.getCurrentSelectedClassroom().classroomStudents);\n            // this.openDialogWithComponent(LibraryFileActionsDialogComponent,\n            //   {\n            //     action: 'shareWith',\n            //     dialogsubTitle: 'Share \"' + this.getFileName(context.name) + '\" with...',\n            //     dialogTitle: 'Share file with...',\n            //     libraryFile: context\n            //   });\n          }\n        }\n      }, {\n        label: 'Unshare',\n        icon: 'pi pi-fw pi-share-alt',\n        command: event => {\n          let send = {\n            fileId: this.classroomFile.fileId,\n            classroomId: this.libraryService.getCurrentSelectedClassroom().id,\n            studentIds: [this.libraryService.getCurrentSelectedClassroom().teacher?.aspUserId]\n          };\n          if (this.authService.isStudent) {\n            this.sendUnShareToApi(send);\n          } else {\n            this.sendUnShareToApi(send);\n            // this.openDialogWithComponent(LibraryFileActionsDialogComponent,\n            //   {\n            //     action: 'unShareWith',\n            //     dialogsubTitle: 'Share \"' + this.getFileName(context.name) + '\" with...',\n            //     dialogTitle: 'Share file with...',\n            //     libraryFile: context\n            //   });\n          }\n        }\n      }, {\n        label: 'Move',\n        icon: 'pi pi-fw pi-folder-open',\n        command: event => {\n          this.openDialogWithComponent(LibraryFileActionsDialogComponent, {\n            action: 'move',\n            dialogsubTitle: 'Move \"' + this.getFileName(context.name) + '\" to folder...',\n            dialogTitle: 'Move file to folder',\n            libraryFile: context,\n            folders: this.folders\n          });\n        }\n      }, {\n        label: 'Edit',\n        icon: 'pi pi-fw pi-file-edit',\n        command: event => {\n          this.generalService.openDialogWithComponent(this.dialogService, LibraryFileActionsDialogComponent, 440, {\n            action: 'editFileLevelsCategories',\n            dialogsubTitle: '',\n            dialogTitle: 'Edit Library File',\n            libraryFile: context\n          }, null, result => {\n            console.log(result);\n            if (result && result.action === 'editFileLevelsCategories') {\n              if (!this.generalService.isNullishObject(result)) {\n                console.log(this.classroomFile);\n                console.log(result);\n                this.classroomFile = {\n                  ...result.result\n                };\n                context = {\n                  ...result.result\n                };\n              }\n            }\n          });\n        }\n      }, {\n        label: 'Delete',\n        icon: 'pi pi-fw pi-trash',\n        command: event => {\n          this.deleteFile(item.fileId);\n        }\n      }];\n      this.menuItems = menuItems.filter(menuItem => {\n        const menuLabel = menuItem.label;\n        return this.availableActions.includes(menuLabel);\n      });\n    }\n    showSharePopup(fileId) {\n      document.getElementById('lib-file-move-menu-' + fileId).style.display = 'none';\n      document.getElementById('lib-file-share-menu-' + fileId).style.display = 'block';\n      document.getElementById('lib-file-share-menu-' + fileId).style.top = '25%';\n    }\n    rename(fileId, newName) {\n      // const input = document.getElementById('fileName') as HTMLInputElement | null;\n      // console.log(input?.value)\n      // this.classroomFile.name = input!.value\n      this.subs.add(this.libraryService.updateLibraryFileName(fileId, newName).pipe(take(1)).subscribe(res => {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'File name name has been updated.'\n        });\n        console.log(res);\n        // this.showRename = !this.showRename\n      }));\n    }\n    checkLibraryItem(el) {\n      this.checked = !this.checked;\n      // let bg = this.checked ? '#2d2a4b' : 'white';\n      // el!.style.backgroundColor = bg;\n      if (this.checked) {\n        this.libraryService.checkedLibraries.push(this.classroomFile);\n        this.libraryChecked.emit({\n          checked: true,\n          file: this.classroomFile\n        });\n      } else {\n        this.libraryService.checkedLibraries = this.libraryService.checkedLibraries.filter(el => {\n          return !(el.path == this.classroomFile.path);\n        });\n        this.libraryChecked.emit({\n          checked: false,\n          file: this.classroomFile\n        });\n      }\n    }\n    getUniqueSharedUsers() {\n      // exclude from sharedWithUsers if not in classroomStudents\n      const filteredUsers = this.sharedWithUsers.filter(user => {\n        return this.classroomStudents.find(student => {\n          return student.aspUserId === user.aspUserId;\n        });\n      });\n      const uniqueNames = Object.values(filteredUsers.reduce((acc, user) => {\n        if (!acc[user.aspUserId]) {\n          acc[user.aspUserId] = user.firstName;\n        }\n        return acc;\n      }, {}));\n      return uniqueNames.join(', ');\n    }\n    onWholeClassSelect() {\n      if (this.isDisabled) this.isDisabled = false;else this.isDisabled = true;\n      if (this.StudentIdsToSend.length === 0) {\n        this.classroom.classroomStudents.forEach(student => {\n          this.StudentIdsToSend.push(student.id);\n        });\n      } else this.StudentIdsToSend = [];\n    }\n    moveFile(libraryFile, moveFolder) {\n      this.subs.add(this.libraryService.moveFileToFolder(libraryFile.fileId, moveFolder.folderId).subscribe(response => {\n        if (!this.generalService.isNullishObject(response)) {\n          this.toastService.setShowToastmessage({\n            severity: 'success',\n            summary: '',\n            detail: 'File moved successfully.'\n          });\n          this.libraryService.setUpdateFolderListener(libraryFile, moveFolder);\n          this.libraryService.setUpdateListener(true);\n        }\n      }));\n      this.ngOnInit();\n    }\n    copyURL(filePath) {\n      this.fileURL = environment.apiUrl + \"/\" + filePath;\n      const selBox = document.createElement('textarea');\n      selBox.style.position = 'fixed';\n      selBox.style.left = '0';\n      selBox.style.top = '0';\n      selBox.style.opacity = '0';\n      selBox.value = this.fileURL;\n      document.body.appendChild(selBox);\n      selBox.focus();\n      selBox.select();\n      document.execCommand('copy');\n      document.body.removeChild(selBox);\n      this.toastService.setShowToastmessage({\n        severity: 'success',\n        summary: '',\n        detail: 'File path copied successfully.'\n      });\n    }\n    deleteFile(fileId) {\n      this.fileDeleted.emit(this.classroomFile);\n      this.ngOnDestroy();\n    }\n    getUsersLibraryHasBeenSharedWith() {\n      if (this.classroomFile.fileId) {\n        return this.subs.add(this.libraryService.getUsersLibraryHasBeenSharedWith(this.classroomFile.fileId).pipe(take(1)).subscribe(res => {\n          this.sharedWithUsers = res;\n        }));\n      }\n      return [];\n    }\n    shareWithTeacher(context) {\n      let send = {\n        fileIds: [context.fileId],\n        classroomId: this.libraryService.getCurrentSelectedClassroom().id,\n        studentIds: [this.libraryService.getCurrentSelectedClassroom().teacher?.aspUserId]\n      };\n      this.sendShareToApi(send);\n    }\n    shareLibraryFile(map) {\n      console.log(this.classroomFile);\n      map.forEach((users, classroomId) => {\n        let ids = [];\n        let fileIds = [];\n        for (let user of users) {\n          ids.push(user.aspUserId);\n        }\n        fileIds.push(this.classroomFile.fileId);\n        let send = {\n          fileIds: fileIds,\n          classroomId: classroomId,\n          studentIds: ids\n        };\n        this.sendShareToApi(send);\n      });\n    }\n    unShareLibraryFile(map) {\n      console.log(this.classroomFile);\n      map.forEach((users, classroomId) => {\n        let ids = [];\n        let fileIds = [];\n        for (let user of users) {\n          ids.push(user.aspUserId);\n        }\n        fileIds.push(this.classroomFile.fileId);\n        let send = {\n          fileId: this.classroomFile.fileId,\n          classroomId: classroomId\n        };\n        this.sendUnShareToApi(send);\n      });\n    }\n    openFileInNewTab(url) {\n      const fileURL = environment.apiUrl + \"/\" + url;\n      window.open(fileURL, '_blank');\n    }\n    sendShareToApi(sendParams) {\n      this.subs.add(this.libraryService.shareFileWithClassUsers(sendParams).pipe(take(1)).subscribe(res => {\n        console.log(this.classroom);\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Library file has been shared.'\n        });\n        if (this.authService.isTeacher) {\n          this.libraryService.setCurrentSelectedClassroomId(parseInt(sendParams.classroomId));\n        }\n      }));\n    }\n    sendUnShareToApi(sendParams) {\n      console.log(this.classroom);\n      this.subs.add(this.libraryService.unShareLibrary(sendParams).pipe(take(1)).subscribe(res => {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Library file has been unshared.'\n        });\n        this.fileUnshared.emit(res);\n        this.libraryService.setCurrentSelectedClassroomId(parseInt(sendParams.classroomId));\n      }));\n    }\n    openDialogWithComponent(component, dialogData, width = 360) {\n      this.generalService.openDialogWithComponent(this.dialogService, component, 400, dialogData, null, result => {\n        if (result && result.action === 'shareWith') {\n          this.shareLibraryFile(result.map.map);\n        } else if (result && result.action === 'unShareWith') {\n          this.unShareLibraryFile(result.map.map);\n        } else if (result && result.action === 'move') {\n          this.moveFile(result.libraryFile, result.folder);\n        }\n      });\n      // this.refs.forEach(ref => ref.close());\n      // this.refs = [];\n      // const element = document.querySelector('#' + this.appendDialogActionsTo);\n      // const rect = element!.getBoundingClientRect();\n      // const centerX = rect.left + rect.width / 2 - width / 2;\n      // const centerY = rect.top;\n      // const dialogRef = (this.dialogService.open(component, {\n      //   header: '',\n      //   width: width + 'px',\n      //   showHeader: false,\n      //   dismissableMask: true,\n      //   modal: true,\n      //   contentStyle: {\n      //     \"max-width\": \"100%\", \"max-height\": \"400px\", \"overflow\": \"auto\", \"border-radius\": \"10px\", \"padding\": \"0px\",\n      //   },\n      //   style: { 'left': `${centerX}px`, 'top': `${centerY}px`, 'position': `fixed` },\n      //   baseZIndex: 10000,\n      //   maskStyleClass: 'transparent-mask',\n      //   data: { dialogData: dialogData },\n      // }));\n      // this.refs.push(dialogRef);\n      // dialogRef.onClose.subscribe((data: any) => {\n      //   console.log('Dialog closed with data:', data);\n      //   if (data) {\n      //     switch (data.action) {\n      //       case 'rename':\n      //         this.rename(data.libraryFile.fileId, data.libraryFile.name);\n      //         break;\n      //       case 'shareWith':\n      //         this.shareLibraryFiles(data.map.map);\n      //         break;\n      //       case 'move':\n      //         this.moveFile(data.libraryFile.fileId, data.folder.folderId);\n      //         break;\n      //       default:\n      //         break;\n      //     }\n      //   }\n      // });\n    }\n    downloadFile(url) {\n      const fileURL = environment.apiUrl + \"/\" + url;\n      this.subs.add(this.http.get(fileURL, {\n        responseType: 'blob'\n      }).subscribe(blob => {\n        const link = document.createElement('a');\n        link.href = window.URL.createObjectURL(blob);\n        link.download = this.getFileName(fileURL);\n        link.click();\n      }));\n    }\n    static #_ = this.ɵfac = function LibraryFileRowItemComponent_Factory(t) {\n      return new (t || LibraryFileRowItemComponent)(i0.ɵɵdirectiveInject(i1.LibraryService), i0.ɵɵdirectiveInject(i2.GeneralService), i0.ɵɵdirectiveInject(i3.DialogService), i0.ɵɵdirectiveInject(i4.ToastService), i0.ɵɵdirectiveInject(i5.AuthService), i0.ɵɵdirectiveInject(i6.HttpClient));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LibraryFileRowItemComponent,\n      selectors: [[\"app-library-file-row-item\"]],\n      inputs: {\n        classroom: \"classroom\",\n        classroomFile: \"classroomFile\",\n        folders: \"folders\",\n        appendDialogActionsTo: \"appendDialogActionsTo\",\n        showGroupActions: \"showGroupActions\",\n        hasLimitedOptions: \"hasLimitedOptions\",\n        availableActions: \"availableActions\",\n        isSmallScreen: \"isSmallScreen\",\n        showSharedWithUsers: \"showSharedWithUsers\",\n        isInTab: \"isInTab\",\n        classroomStudents: \"classroomStudents\"\n      },\n      outputs: {\n        fileDeleted: \"fileDeleted\",\n        libraryChecked: \"libraryChecked\",\n        fileUnshared: \"fileUnshared\"\n      },\n      features: [i0.ɵɵProvidersFeature([DialogService])],\n      decls: 23,\n      vars: 24,\n      consts: [[\"menu3\", \"\"], [\"notMobileBlock\", \"\"], [1, \"library-file-row\", 3, \"ngClass\"], [1, \"w-3rem\", \"h-3rem\", \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-grad-blue\", \"border-circle\", \"mr-1\", \"flex-shrink-0\", 3, \"ngClass\"], [\"src\", \"/assets/icons/file-white.svg\", \"height\", \"24\"], [1, \"w-full\", \"flex\", \"sm:flex-row\", \"align-items-center\", \"justify-content-between\"], [1, \"library-file-details\", \"py-1\", \"font-sm\", \"flex\", \"flex-column\"], [1, \"file-name\", \"file-path-text\", \"max-w-10rem\", \"sm:max-w-15rem\", \"pointer\", 3, \"click\"], [1, \"flex\", \"gap-1\"], [4, \"ngFor\", \"ngForOf\"], [\"src\", \"/assets/icons/library/share-sm-icon.svg\", \"width\", \"10\", 4, \"ngIf\"], [1, \"font-xs\", \"file-path-text\", \"max-w-10rem\"], [1, \"library-file-meta\"], [1, \"text-900\", \"w-full\", \"md:flex-order-0\", \"flex-order-1\", \"flex\", \"align-items-end\", \"justify-content-end\", \"gap-1\"], [3, \"class\", 4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [1, \"library-actions-button\", \"mx-2\", 3, \"click\", \"id\"], [\"style\", \"position: relative;\", 3, \"click\", 4, \"ngIf\"], [\"src\", \"/assets/icons/library/library-more-actions.svg\", \"class\", \"pointer hvr-grow\", \"style\", \"width:1.75rem; height:1.75rem\", 4, \"ngIf\"], [\"popup\", \"popup\", \"appendTo\", \"body\", 3, \"model\"], [\"size\", \"2xs\", 3, \"name\"], [\"src\", \"/assets/icons/library/share-sm-icon.svg\", \"width\", \"10\"], [1, \"font-xs\", \"text-white\", \"py-1\", \"px-0\", \"line-height-1\"], [4, \"ngIf\", \"ngIfElse\"], [\"tooltipPosition\", \"top\", 1, \"font-xs\", \"text-muted\", \"mt-2\", \"more-circle\", 3, \"pTooltip\"], [\"class\", \"font-xs text-muted mt-2 more-circle\", \"tooltipPosition\", \"top\", 3, \"pTooltip\", 4, \"ngIf\"], [2, \"position\", \"relative\", 3, \"click\"], [\"src\", \"/assets/icons/library/group-item-empty.svg\", \"class\", \"pointer\", \"style\", \"width:1.75rem; height:1.75rem\", 4, \"ngIf\"], [\"src\", \"/assets/icons/library/group-item-full.svg\", \"class\", \"pointer\", \"style\", \"width:1.75rem; height:1.75rem\", 4, \"ngIf\"], [\"src\", \"/assets/icons/library/group-item-empty.svg\", 1, \"pointer\", 2, \"width\", \"1.75rem\", \"height\", \"1.75rem\"], [\"src\", \"/assets/icons/library/group-item-full.svg\", 1, \"pointer\", 2, \"width\", \"1.75rem\", \"height\", \"1.75rem\"], [\"src\", \"/assets/icons/library/library-more-actions.svg\", 1, \"pointer\", \"hvr-grow\", 2, \"width\", \"1.75rem\", \"height\", \"1.75rem\"]],\n      template: function LibraryFileRowItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"li\", 2);\n          i0.ɵɵtext(1);\n          i0.ɵɵelementStart(2, \"div\", 3);\n          i0.ɵɵelement(3, \"img\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 5)(5, \"div\", 6)(6, \"span\", 7);\n          i0.ɵɵlistener(\"click\", function LibraryFileRowItemComponent_Template_span_click_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.openFileInNewTab(ctx.classroomFile.path));\n          });\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 8);\n          i0.ɵɵtemplate(9, LibraryFileRowItemComponent_div_9_Template, 2, 1, \"div\", 9)(10, LibraryFileRowItemComponent_img_10_Template, 1, 0, \"img\", 10);\n          i0.ɵɵelementStart(11, \"span\", 11);\n          i0.ɵɵtext(12);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 12)(14, \"div\", 13);\n          i0.ɵɵtemplate(15, LibraryFileRowItemComponent_p_chip_15_Template, 3, 4, \"p-chip\", 14);\n          i0.ɵɵpipe(16, \"slice\");\n          i0.ɵɵtemplate(17, LibraryFileRowItemComponent_div_17_Template, 4, 2, \"div\", 15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 16);\n          i0.ɵɵlistener(\"click\", function LibraryFileRowItemComponent_Template_div_click_18_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            const menu3_r6 = i0.ɵɵreference(22);\n            return i0.ɵɵresetView(!ctx.showGroupActions ? menu3_r6.toggle($event) : null);\n          });\n          i0.ɵɵtemplate(19, LibraryFileRowItemComponent_div_19_Template, 3, 2, \"div\", 17)(20, LibraryFileRowItemComponent_img_20_Template, 1, 0, \"img\", 18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(21, \"p-menu\", 19, 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(18, _c0, ctx.checked && ctx.showGroupActions, ctx.isInTab));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.classroom == null ? null : ctx.classroom.id, \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(21, _c1, ctx.isInTab, !ctx.isInTab));\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.getFileName(ctx.classroomFile.name));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.classroomFile.levels);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.sharedWithUsers.length > 0);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\" \", ctx.getUniqueSharedUsers(), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(16, 14, ctx.classroomFile.categories, 0, ctx.hasMultipleCategoriesSlice));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.classroomFile.categories.length > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵpropertyInterpolate1(\"id\", \"lib-file-burger-\", ctx.classroomFile.fileId, \"\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showGroupActions || ctx.libraryService.isPreviewingFromChat());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.showGroupActions && !ctx.libraryService.isPreviewingFromChat());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"model\", ctx.menuItems);\n        }\n      },\n      dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i8.Chip, i9.GLevelCircleComponent, i10.Menu, i11.Tooltip, i7.SlicePipe],\n      styles: [\".bg-grad-blue[_ngcontent-%COMP%]{background:linear-gradient(#657aef,#1a266c)}.bg-grad-purple[_ngcontent-%COMP%]{background:linear-gradient(#7f8dfa,#4f29a2);border:1px solid #7d89f6}.library-actions-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{line-height:1}.library-file-row[_ngcontent-%COMP%]{border-radius:40px;display:flex;background-color:#fff;align-items:center;margin-bottom:10px;transition:all .3s ease-in-out}.library-file-row[_ngcontent-%COMP%]:hover, .library-file-row.checked[_ngcontent-%COMP%]{color:#fff;background:#002ccf;background:linear-gradient(90deg,#002ccf,#c9d2ff18)}.library-file-row.purple-hover[_ngcontent-%COMP%]:hover, .library-file-row.purple-hover.checked[_ngcontent-%COMP%]{background:linear-gradient(90deg,#6659cd,#c9d2ff18)}.library-file-row[_ngcontent-%COMP%]   svg.library-file-icon[_ngcontent-%COMP%]{padding:8px;margin-top:5px}.library-file-row[_ngcontent-%COMP%]   .library-file-details[_ngcontent-%COMP%]{width:100%;display:flex}.library-file-row[_ngcontent-%COMP%]   .library-file-details[_ngcontent-%COMP%]   .library-file-meta[_ngcontent-%COMP%]{margin-top:5px;display:flex}.library-file-row[_ngcontent-%COMP%]   .library-file-details[_ngcontent-%COMP%]   .library-file-meta[_ngcontent-%COMP%]   span.file-level[_ngcontent-%COMP%]{color:#fff;background:#444070;padding:6px;border-radius:50px}.library-file-row[_ngcontent-%COMP%]   .library-file-details[_ngcontent-%COMP%]   .library-file-meta[_ngcontent-%COMP%]   span.file-category[_ngcontent-%COMP%]{color:#fff;background-color:#a44fd0;padding:6px;border-radius:50px;margin-left:6px}.library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]{width:180px;position:absolute;left:92.5%;top:69px;border-radius:12px;border:1px solid var(--main-color);box-sizing:border-box;transition-duration:.2s;transition-property:transform;background-color:#fff;z-index:1;flex-direction:column;padding:10px}@media screen and (max-width: 1124px){.library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]{right:30px}}.library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]{display:flex;flex-direction:column}.library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]   .lib-file-menu-col[_ngcontent-%COMP%]{margin-top:5px;display:flex;align-items:center}.library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]   .lib-file-menu-col[_ngcontent-%COMP%]   .lib-file-menu-text[_ngcontent-%COMP%]{margin-left:10px;cursor:pointer}.library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]   .lib-file-menu-col[_ngcontent-%COMP%]   .lib-file-menu-icon[_ngcontent-%COMP%]{width:20px;display:flex;justify-content:center}.library-file-row[_ngcontent-%COMP%]   .lib-file-menu[_ngcontent-%COMP%]   .lib-file-menu-row[_ngcontent-%COMP%]:first-child{margin-top:0}  .p-menu{border:1px solid #1a266c!important}  .p-menu .p-menuitem-link{padding:.5rem 1rem!important}.more-circle[_ngcontent-%COMP%]{background:linear-gradient(#657aef,#1a266c);color:#fff;padding:5px 7px;border-radius:100px;line-height:1}\"]\n    });\n  }\n  return LibraryFileRowItemComponent;\n})();", "map": {"version": 3, "names": ["EventEmitter", "DialogService", "take", "environment", "LibraryFileActionsDialogComponent", "SubSink", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "level_r2", "ɵɵtext", "ɵɵclassMapInterpolate1", "category_r3", "toLowerCase", "ɵɵtextInterpolate", "ɵɵelementContainerStart", "ɵɵpipeBind2", "ctx_r3", "classroomFile", "categories", "join", "ɵɵtextInterpolate1", "length", "ɵɵtemplate", "LibraryFileRowItemComponent_div_17_ng_template_2_span_0_Template", "LibraryFileRowItemComponent_div_17_ng_container_1_Template", "LibraryFileRowItemComponent_div_17_ng_template_2_Template", "ɵɵtemplateRefExtractor", "is<PERSON>arge", "notMobileBlock_r5", "ɵɵlistener", "LibraryFileRowItemComponent_div_19_Template_div_click_0_listener", "ɵɵrestoreView", "_r7", "ɵɵnextContext", "ɵɵresetView", "checkLibraryItem", "checkLibMenu", "LibraryFileRowItemComponent_div_19_img_1_Template", "LibraryFileRowItemComponent_div_19_img_2_Template", "checked", "LibraryFileRowItemComponent", "constructor", "libraryService", "generalService", "dialogService", "toastService", "authService", "http", "classroom", "appendDialogActionsTo", "showGroupActions", "hasLimitedOptions", "availableActions", "isSmallScreen", "showSharedWithUsers", "isInTab", "fileDeleted", "libraryChecked", "fileUnshared", "StudentIdsToSend", "isDisabled", "isDisabledWholeClass", "moveFileId", "moveFolderId", "isMoveRadioCheked", "showRename", "refs", "subs", "sharedWithUsers", "menuItems", "hasMultipleCategoriesSlice", "ngOnInit", "getUsersLibraryHasBeenSharedWith", "sink", "subscribe", "res", "add", "deviceKind", "pipe", "w768up", "prepareMenuItemsForItem", "ngOnDestroy", "unsubscribe", "onSelect", "studentId", "indexOf", "splice", "push", "showMoveFilePopup", "fileId", "document", "getElementById", "style", "display", "top", "getFileName", "path", "n", "lastIndexOf", "result", "substring", "item", "context", "label", "icon", "command", "event", "openFileInNewTab", "copyURL", "downloadFile", "isStudent", "shareWithTeacher", "currentSelectedClassroom", "getCurrentSelectedClassroom", "Object", "keys", "classroomStudents", "<PERSON><PERSON><PERSON>er", "send", "fileIds", "classroomId", "id", "studentIds", "aspUserId", "sendShareToApi", "openDialogWithComponent", "action", "dialogsubTitle", "name", "dialogTitle", "libraryFile", "console", "log", "teacher", "sendUnShareToApi", "folders", "isNullishObject", "deleteFile", "filter", "menuItem", "menuLabel", "includes", "showSharePopup", "rename", "newName", "updateLibraryFileName", "setShowToastmessage", "severity", "summary", "detail", "el", "checkedLibraries", "emit", "file", "getUniqueSharedUsers", "filteredUsers", "user", "find", "student", "uniqueNames", "values", "reduce", "acc", "firstName", "onWholeClassSelect", "for<PERSON>ach", "moveFile", "moveFolder", "moveFileToFolder", "folderId", "response", "setUpdateFolderListener", "setUpdateListener", "filePath", "fileURL", "apiUrl", "selBox", "createElement", "position", "left", "opacity", "value", "body", "append<PERSON><PERSON><PERSON>", "focus", "select", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "shareLibraryFile", "map", "users", "ids", "unShareLibraryFile", "url", "window", "open", "sendParams", "shareFileWithClassUsers", "setCurrentSelectedClassroomId", "parseInt", "unShareLibrary", "component", "dialogData", "width", "folder", "get", "responseType", "blob", "link", "href", "URL", "createObjectURL", "download", "click", "_", "ɵɵdirectiveInject", "i1", "LibraryService", "i2", "GeneralService", "i3", "i4", "ToastService", "i5", "AuthService", "i6", "HttpClient", "_2", "selectors", "inputs", "outputs", "features", "ɵɵProvidersFeature", "decls", "vars", "consts", "template", "LibraryFileRowItemComponent_Template", "rf", "ctx", "LibraryFileRowItemComponent_Template_span_click_6_listener", "_r1", "LibraryFileRowItemComponent_div_9_Template", "LibraryFileRowItemComponent_img_10_Template", "LibraryFileRowItemComponent_p_chip_15_Template", "LibraryFileRowItemComponent_div_17_Template", "LibraryFileRowItemComponent_Template_div_click_18_listener", "$event", "menu3_r6", "ɵɵreference", "toggle", "LibraryFileRowItemComponent_div_19_Template", "LibraryFileRowItemComponent_img_20_Template", "ɵɵpureFunction2", "_c0", "_c1", "levels", "ɵɵpipeBind3", "ɵɵpropertyInterpolate1", "isPreviewingFromChat"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\library\\library\\components\\library-file-row-item\\library-file-row-item.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\library\\library\\components\\library-file-row-item\\library-file-row-item.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';\r\nimport { MenuItem } from 'primeng/api';\r\nimport { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';\r\nimport { take } from 'rxjs/operators';\r\nimport { Classroom } from 'src/app/core/models/classroom.model';\r\nimport { LibraryService } from 'src/app/core/services/library.service';\r\nimport { environment } from 'src/environments/environment';\r\nimport { LibraryFileActionsDialogComponent } from '../library-file-actions-dialog/library-file-actions-dialog.component';\r\nimport { Folder, LibraryFile } from 'src/app/core/models/library.model';\r\nimport { User } from 'src/app/core/models/user.model';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { SubSink } from 'subsink';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\n\r\n@Component({\r\n  selector: 'app-library-file-row-item',\r\n  templateUrl: './library-file-row-item.component.html',\r\n  styleUrls: ['./library-file-row-item.component.scss'],\r\n  providers: [DialogService]\r\n})\r\nexport class LibraryFileRowItemComponent implements OnInit, OnDestroy {\r\n\r\n  @Input() classroom = {} as Classroom;\r\n  @Input() classroomFile: any = {} as any;\r\n  @Input() folders: any;\r\n  @Input() appendDialogActionsTo = 'allFiles';\r\n  @Input() showGroupActions = false;\r\n  @Input() hasLimitedOptions = false;\r\n  @Input() availableActions: string[] = [] as string[];\r\n  @Input() isSmallScreen = false;\r\n  @Input() showSharedWithUsers = true;\r\n  @Input() isInTab = false;\r\n  @Input() classroomStudents: any;\r\n\r\n  @Output() fileDeleted = new EventEmitter();\r\n  @Output() libraryChecked = new EventEmitter();\r\n  @Output() fileUnshared = new EventEmitter();\r\n  libraryFiles: any;\r\n  StudentIdsToSend: string[] = [];\r\n  isDisabled = false;\r\n  isDisabledWholeClass = false;\r\n  fileURL: string | undefined;\r\n  moveFileId = 0;\r\n  moveFolderId = 0;\r\n  isMoveRadioCheked = false;\r\n  showRename: boolean = false;\r\n  refs: DynamicDialogRef[] = [];\r\n  private subs = new SubSink();\r\n  checked: boolean = false;\r\n  sharedWithUsers = [];\r\n  menuItems: MenuItem[] = [];\r\n  isLarge = false;\r\n  hasMultipleCategoriesSlice = 1;\r\n  constructor(\r\n    public libraryService: LibraryService,\r\n    private generalService: GeneralService,\r\n    private dialogService: DialogService,\r\n    private toastService: ToastService,\r\n    private authService: AuthService,\r\n    private http: HttpClient,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    if (this.showSharedWithUsers) {\r\n      this.getUsersLibraryHasBeenSharedWith();\r\n    }\r\n    this.subs.sink = this.libraryService.showGroupActions.subscribe(res => {\r\n      if (!res) {\r\n        this.checked = false;\r\n      }\r\n      // if (this.libraryService.isPreviewingFromChat()) {\r\n      //   this.checked = true;\r\n      // }\r\n    });\r\n    \r\n    this.subs.add(this.generalService.deviceKind.pipe().subscribe(res => {\r\n      if (res) {\r\n        if (res.w768up) {\r\n          this.isLarge = true;\r\n          this.hasMultipleCategoriesSlice = 2;\r\n        }\r\n      }\r\n    }));\r\n    this.prepareMenuItemsForItem(this.classroomFile);\r\n\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  onSelect(studentId: string) {\r\n    this.isDisabled = false;\r\n    if (this.StudentIdsToSend.indexOf(studentId) !== -1)\r\n      this.StudentIdsToSend.splice(this.StudentIdsToSend.indexOf(studentId), 1);\r\n    else\r\n      this.StudentIdsToSend.push(studentId);\r\n\r\n    if (this.isDisabledWholeClass && this.StudentIdsToSend.length === 0)\r\n      this.isDisabledWholeClass = false;\r\n    else\r\n      this.isDisabledWholeClass = true;\r\n  }\r\n\r\n  showMoveFilePopup(fileId: any) {\r\n    document.getElementById('lib-file-share-menu-' + fileId)!.style.display = 'none';\r\n    document.getElementById('lib-file-move-menu-' + fileId)!.style.display = 'block';\r\n    document.getElementById('lib-file-move-menu-' + fileId)!.style.top = '55%';\r\n  }\r\n\r\n  // openSharedByFileMenu(fileId: any) {\r\n  //   alert(fileId);\r\n  //   [].forEach.call(document.getElementsByClassName('lib-file-sharedby-menu'), (el: any) => {\r\n  //     el.style.display = 'none';\r\n  //   })\r\n  //   setTimeout(() => {\r\n  //     document.getElementById('lib-file-sharedby-menu-' + fileId)!.style.display = 'flex';\r\n  //     document.getElementById('lib-file-sharedby-menu-' + fileId)!.style.top = (880 + document.getElementById('lib-file-sharedby-menu-' + fileId)!.offsetTop) + \"px\";\r\n  //   }, 100);\r\n  //   // this.showShare = true;\r\n  // }\r\n\r\n  // showSharedByMoveFilePopup(fileId: any) {\r\n  //   document.getElementById('lib-file-sharedby-move-menu-' + fileId)!.style.display = 'block';\r\n  //   document.getElementById('lib-file-sharedby-move-menu-' + fileId)!.style.top = '55%';\r\n  // }\r\n\r\n  getFileName(path: string) {\r\n    if (path) {\r\n      let n = path.lastIndexOf(\"/\");\r\n      if (n === -1)\r\n        n = path.lastIndexOf(\"\\\\\");\r\n      let result = path.substring(n + 1);\r\n      return result;\r\n    }\r\n    return path\r\n  }\r\n\r\n  prepareMenuItemsForItem(item: any) {\r\n    let context: LibraryFile = item;\r\n\r\n    const menuItems: MenuItem[] = [\r\n      {\r\n        label: 'View', icon: 'pi pi-fw pi-external-link',\r\n        command: (event) => {\r\n          this.openFileInNewTab(context.path);\r\n        }\r\n      },\r\n      {\r\n        label: 'Copy URL', icon: 'pi pi-fw pi-copy',\r\n        command: (event) => {\r\n          this.copyURL(context.path);\r\n        }\r\n      },\r\n      {\r\n        label: 'Download', icon: 'pi pi-fw pi-cloud-download',\r\n        command: (event) => {\r\n          this.downloadFile(context.path);\r\n        }\r\n      },\r\n      {\r\n        label: 'Share', icon: 'pi pi-fw pi-share-alt',\r\n        command: (event) => {\r\n          if (this.authService.isStudent) {\r\n            this.shareWithTeacher(context);\r\n          } else {\r\n            const currentSelectedClassroom = this.libraryService.getCurrentSelectedClassroom()\r\n            if (Object.keys(currentSelectedClassroom).length > 0) {\r\n              const classroomStudents = this.libraryService.getCurrentSelectedClassroom().classroomStudents;\r\n              // if only one student, share immediately\r\n              if (classroomStudents.length === 1 && this.authService.isTeacher) {\r\n                let send: any = {\r\n                  fileIds: [context.fileId],\r\n                  classroomId: this.libraryService.getCurrentSelectedClassroom().id,\r\n                  studentIds: [classroomStudents[0].aspUserId]\r\n                }\r\n                this.sendShareToApi(send);\r\n              }\r\n            } else {\r\n              this.openDialogWithComponent(LibraryFileActionsDialogComponent,\r\n                {\r\n                  action: 'shareWith',\r\n                  dialogsubTitle: 'Share \"' + this.getFileName(context.name) + '\" with...',\r\n                  dialogTitle: 'Share file with...',\r\n                  libraryFile: context\r\n                });\r\n            }\r\n\r\n            console.log(this.libraryService.getCurrentSelectedClassroom().classroomStudents);\r\n            // this.openDialogWithComponent(LibraryFileActionsDialogComponent,\r\n            //   {\r\n            //     action: 'shareWith',\r\n            //     dialogsubTitle: 'Share \"' + this.getFileName(context.name) + '\" with...',\r\n            //     dialogTitle: 'Share file with...',\r\n            //     libraryFile: context\r\n            //   });\r\n          }\r\n        }\r\n      },\r\n      {\r\n        label: 'Unshare', icon: 'pi pi-fw pi-share-alt',\r\n        command: (event) => {\r\n          let send: any = {\r\n            fileId: this.classroomFile.fileId!,\r\n            classroomId: this.libraryService.getCurrentSelectedClassroom().id,\r\n            studentIds: [this.libraryService.getCurrentSelectedClassroom().teacher?.aspUserId]\r\n          }\r\n          if (this.authService.isStudent) {\r\n            this.sendUnShareToApi(send);\r\n          } else {\r\n            \r\n            this.sendUnShareToApi(send);\r\n            // this.openDialogWithComponent(LibraryFileActionsDialogComponent,\r\n            //   {\r\n            //     action: 'unShareWith',\r\n            //     dialogsubTitle: 'Share \"' + this.getFileName(context.name) + '\" with...',\r\n            //     dialogTitle: 'Share file with...',\r\n            //     libraryFile: context\r\n            //   });\r\n          }\r\n        }\r\n      },\r\n      {\r\n        label: 'Move', icon: 'pi pi-fw pi-folder-open',\r\n        command: (event) => {\r\n          this.openDialogWithComponent(LibraryFileActionsDialogComponent,\r\n            {\r\n              action: 'move',\r\n              dialogsubTitle: 'Move \"' + this.getFileName(context.name) + '\" to folder...',\r\n              dialogTitle: 'Move file to folder',\r\n              libraryFile: context,\r\n              folders: this.folders\r\n            });\r\n        }\r\n      },\r\n      {\r\n        label: 'Edit', icon: 'pi pi-fw pi-file-edit',\r\n        command: (event) => {\r\n          this.generalService.openDialogWithComponent(this.dialogService, LibraryFileActionsDialogComponent, 440,\r\n            {\r\n              action: 'editFileLevelsCategories',\r\n              dialogsubTitle: '',\r\n              dialogTitle: 'Edit Library File',\r\n              libraryFile: context,\r\n            }, null, (result: any) => {\r\n              console.log(result);\r\n              if (result && result.action === 'editFileLevelsCategories') {\r\n                if (!this.generalService.isNullishObject(result)) {\r\n\r\n                  console.log(this.classroomFile);\r\n                  console.log(result);\r\n                  this.classroomFile = { ...result.result };\r\n                  context = { ...result.result };\r\n                }\r\n              }\r\n            });\r\n        }\r\n      },\r\n      {\r\n        label: 'Delete', icon: 'pi pi-fw pi-trash',\r\n        command: (event) => {\r\n          this.deleteFile(item.fileId);\r\n        }\r\n      }\r\n    ];\r\n\r\n    this.menuItems = menuItems.filter((menuItem: MenuItem) => {\r\n        const menuLabel = menuItem.label as string;\r\n        return this.availableActions.includes(menuLabel);\r\n    });\r\n\r\n  }\r\n\r\n  showSharePopup(fileId: number) {\r\n    document.getElementById('lib-file-move-menu-' + fileId)!.style.display = 'none';\r\n    document.getElementById('lib-file-share-menu-' + fileId)!.style.display = 'block';\r\n    document.getElementById('lib-file-share-menu-' + fileId)!.style.top = '25%';\r\n  }\r\n\r\n  rename(fileId: number, newName: string) {\r\n    // const input = document.getElementById('fileName') as HTMLInputElement | null;\r\n    // console.log(input?.value)\r\n    // this.classroomFile.name = input!.value\r\n    this.subs.add(this.libraryService.updateLibraryFileName(fileId, newName).pipe(take(1)).subscribe(res => {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'success',\r\n        summary: '',\r\n        detail: 'File name name has been updated.'\r\n      });\r\n      console.log(res)\r\n      // this.showRename = !this.showRename\r\n    }));\r\n  }\r\n\r\n  checkLibraryItem(el: any) {\r\n    this.checked = !this.checked;\r\n    // let bg = this.checked ? '#2d2a4b' : 'white';\r\n    // el!.style.backgroundColor = bg;\r\n    if (this.checked) {\r\n      this.libraryService.checkedLibraries.push(this.classroomFile);\r\n      this.libraryChecked.emit({ checked: true, file: this.classroomFile })\r\n    } else {\r\n      this.libraryService.checkedLibraries = this.libraryService.checkedLibraries.filter((el: LibraryFile) => {\r\n        return !(el.path == this.classroomFile.path)\r\n      });\r\n      this.libraryChecked.emit({ checked: false, file: this.classroomFile })\r\n    }\r\n  }\r\n\r\n  getUniqueSharedUsers(): string {\r\n    // exclude from sharedWithUsers if not in classroomStudents\r\n    const filteredUsers = this.sharedWithUsers.filter((user: User) => {\r\n      return this.classroomStudents.find((student: User) => {\r\n        return student.aspUserId === user.aspUserId;\r\n      });\r\n    });\r\n\r\n    const uniqueNames = Object.values(filteredUsers.reduce((acc: any, user: any) => {\r\n      if (!acc[user.aspUserId]) {\r\n        acc[user.aspUserId] = user.firstName;\r\n      }\r\n      return acc;\r\n    }, {}));\r\n    return uniqueNames.join(', ');\r\n  }\r\n\r\n  private onWholeClassSelect() {\r\n    if (this.isDisabled)\r\n      this.isDisabled = false;\r\n    else\r\n      this.isDisabled = true;\r\n\r\n    if (this.StudentIdsToSend.length === 0) {\r\n      this.classroom.classroomStudents.forEach((student: { id: string; }) => {\r\n        this.StudentIdsToSend.push(student.id);\r\n      });\r\n    }\r\n    else\r\n      this.StudentIdsToSend = [];\r\n  }\r\n\r\n  private moveFile(libraryFile: LibraryFile, moveFolder: Folder) {\r\n    this.subs.add(this.libraryService.moveFileToFolder(libraryFile.fileId!, moveFolder.folderId!).subscribe((response) => {\r\n      if (!this.generalService.isNullishObject(response)) {\r\n\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'success',\r\n          summary: '',\r\n          detail: 'File moved successfully.'\r\n        });\r\n        this.libraryService.setUpdateFolderListener(libraryFile, moveFolder);\r\n        this.libraryService.setUpdateListener(true);\r\n      }\r\n    }));\r\n    this.ngOnInit();\r\n  }\r\n\r\n  private copyURL(filePath: any) {\r\n    this.fileURL = environment.apiUrl + \"/\" + filePath;\r\n    const selBox = document.createElement('textarea');\r\n    selBox.style.position = 'fixed';\r\n    selBox.style.left = '0';\r\n    selBox.style.top = '0';\r\n    selBox.style.opacity = '0';\r\n    selBox.value = this.fileURL;\r\n    document.body.appendChild(selBox);\r\n    selBox.focus();\r\n    selBox.select();\r\n    document.execCommand('copy');\r\n    document.body.removeChild(selBox);\r\n    this.toastService.setShowToastmessage({\r\n      severity: 'success',\r\n      summary: '',\r\n      detail: 'File path copied successfully.'\r\n    });\r\n  }\r\n\r\n  private deleteFile(fileId: any) {\r\n    this.fileDeleted.emit(this.classroomFile);\r\n    this.ngOnDestroy();\r\n  }\r\n\r\n  private getUsersLibraryHasBeenSharedWith() {\r\n    if (this.classroomFile.fileId) {\r\n      return this.subs.add(this.libraryService.getUsersLibraryHasBeenSharedWith(this.classroomFile.fileId!).pipe(take(1)).subscribe(res => {\r\n        this.sharedWithUsers = res\r\n      }))\r\n    }\r\n    return []\r\n  }\r\n\r\n  private shareWithTeacher(context: any) {\r\n    let send: any = {\r\n      fileIds: [context.fileId],\r\n      classroomId: this.libraryService.getCurrentSelectedClassroom().id,\r\n      studentIds: [this.libraryService.getCurrentSelectedClassroom().teacher?.aspUserId]\r\n    }\r\n    this.sendShareToApi(send);\r\n  }\r\n\r\n  private shareLibraryFile(map: any) {\r\n    console.log(this.classroomFile);\r\n    map.forEach((users: User[], classroomId: number) => {\r\n      let ids: string[] = []\r\n      let fileIds: number[] = []\r\n      for (let user of users) {\r\n        ids.push(user.aspUserId!)\r\n      }\r\n      fileIds.push(this.classroomFile.fileId!)\r\n      let send: any = {\r\n        fileIds: fileIds,\r\n        classroomId: classroomId,\r\n        studentIds: ids\r\n      }\r\n      this.sendShareToApi(send);\r\n    });\r\n  }\r\n\r\n  private unShareLibraryFile(map: any) {\r\n    console.log(this.classroomFile);\r\n    map.forEach((users: User[], classroomId: number) => {\r\n      let ids: string[] = []\r\n      let fileIds: number[] = []\r\n      for (let user of users) {\r\n        ids.push(user.aspUserId!)\r\n      }\r\n      fileIds.push(this.classroomFile.fileId!)\r\n      let send: any = {\r\n        fileId: this.classroomFile.fileId!,\r\n        classroomId: classroomId\r\n      }\r\n      this.sendUnShareToApi(send);\r\n    });\r\n  }\r\n\r\n  openFileInNewTab(url: string): void {\r\n    const fileURL = environment.apiUrl + \"/\" + url;\r\n    window.open(fileURL, '_blank');\r\n  }\r\n\r\n  private sendShareToApi(sendParams: any) {\r\n    this.subs.add(this.libraryService.shareFileWithClassUsers(sendParams).pipe(take(1)).subscribe(res => {\r\n\r\n      console.log(this.classroom);\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'success',\r\n        summary: '',\r\n        detail: 'Library file has been shared.'\r\n      });\r\n      if (this.authService.isTeacher) {\r\n        this.libraryService.setCurrentSelectedClassroomId(parseInt(sendParams.classroomId));\r\n      }\r\n    }));\r\n  }\r\n\r\n  private sendUnShareToApi(sendParams: any) {\r\n    console.log(this.classroom);\r\n    this.subs.add(this.libraryService.unShareLibrary(sendParams).pipe(take(1)).subscribe(res => {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'success',\r\n        summary: '',\r\n        detail: 'Library file has been unshared.'\r\n      });\r\n      this.fileUnshared.emit(res);\r\n      this.libraryService.setCurrentSelectedClassroomId(parseInt(sendParams.classroomId));\r\n    }));\r\n  }\r\n\r\n  private openDialogWithComponent(component: any, dialogData?: any, width = 360) {\r\n\r\n    this.generalService.openDialogWithComponent(this.dialogService, component, 400, dialogData, null, (result: any) => {\r\n      if (result && result.action === 'shareWith') {\r\n        this.shareLibraryFile(result.map.map);\r\n      } else if (result && result.action === 'unShareWith') {\r\n        this.unShareLibraryFile(result.map.map);\r\n      } else if (result && result.action === 'move') {\r\n        this.moveFile(result.libraryFile, result.folder);\r\n      }\r\n    });\r\n\r\n    // this.refs.forEach(ref => ref.close());\r\n    // this.refs = [];\r\n    // const element = document.querySelector('#' + this.appendDialogActionsTo);\r\n    // const rect = element!.getBoundingClientRect();\r\n    // const centerX = rect.left + rect.width / 2 - width / 2;\r\n    // const centerY = rect.top;\r\n    // const dialogRef = (this.dialogService.open(component, {\r\n    //   header: '',\r\n    //   width: width + 'px',\r\n    //   showHeader: false,\r\n    //   dismissableMask: true,\r\n    //   modal: true,\r\n    //   contentStyle: {\r\n    //     \"max-width\": \"100%\", \"max-height\": \"400px\", \"overflow\": \"auto\", \"border-radius\": \"10px\", \"padding\": \"0px\",\r\n    //   },\r\n    //   style: { 'left': `${centerX}px`, 'top': `${centerY}px`, 'position': `fixed` },\r\n    //   baseZIndex: 10000,\r\n    //   maskStyleClass: 'transparent-mask',\r\n    //   data: { dialogData: dialogData },\r\n    // }));\r\n    // this.refs.push(dialogRef);\r\n    // dialogRef.onClose.subscribe((data: any) => {\r\n    //   console.log('Dialog closed with data:', data);\r\n    //   if (data) {\r\n    //     switch (data.action) {\r\n    //       case 'rename':\r\n    //         this.rename(data.libraryFile.fileId, data.libraryFile.name);\r\n    //         break;\r\n    //       case 'shareWith':\r\n    //         this.shareLibraryFiles(data.map.map);\r\n    //         break;\r\n    //       case 'move':\r\n    //         this.moveFile(data.libraryFile.fileId, data.folder.folderId);\r\n    //         break;\r\n\r\n    //       default:\r\n    //         break;\r\n    //     }\r\n    //   }\r\n    // });\r\n  }\r\n  \r\n  private downloadFile(url: string): void {\r\n    const fileURL = environment.apiUrl + \"/\" + url;\r\n    this.subs.add(this.http.get(fileURL, { responseType: 'blob' })\r\n      .subscribe((blob: Blob) => { // Specify the type of blob as Blob\r\n        const link = document.createElement('a');\r\n        link.href = window.URL.createObjectURL(blob);\r\n        link.download = this.getFileName(fileURL);\r\n        link.click();\r\n      }));\r\n  }\r\n\r\n}\r\n", "<li class=\"library-file-row\" [ngClass]=\"{'checked': checked && showGroupActions, 'purple-hover': isInTab}\">\r\n    {{classroom?.id}}\r\n    <div\r\n        class=\"w-3rem h-3rem flex align-items-center justify-content-center bg-grad-blue border-circle mr-1 flex-shrink-0\"\r\n        [ngClass]=\"{'bg-grad-purple': isInTab, 'bg-grad-blue': !isInTab}\">\r\n        <img src=\"/assets/icons/file-white.svg\" height=\"24\">\r\n    </div>\r\n    <div class=\"w-full flex sm:flex-row align-items-center justify-content-between\">\r\n        <div class=\"library-file-details py-1 font-sm flex flex-column\">\r\n            <span (click)=\"openFileInNewTab(classroomFile.path)\" class=\"file-name file-path-text max-w-10rem sm:max-w-15rem pointer\">{{getFileName(classroomFile.name)}}</span>\r\n            <div class=\"flex gap-1\">\r\n                <div *ngFor=\"let level of classroomFile.levels\">\r\n                    <g-level-circle size=\"2xs\" [name]=\"level\"></g-level-circle>\r\n                </div>\r\n                <img src=\"/assets/icons/library/share-sm-icon.svg\" *ngIf=\"this.sharedWithUsers.length > 0\" width=\"10\">\r\n                <span class=\"font-xs file-path-text max-w-10rem\">\r\n                    {{ getUniqueSharedUsers() }}\r\n                </span>\r\n            </div>\r\n        </div>\r\n        <div class=\"library-file-meta\">\r\n            <div class=\"text-900 w-full md:flex-order-0 flex-order-1 flex align-items-end justify-content-end gap-1\">\r\n                <p-chip *ngFor=\"let category of classroomFile.categories | slice:0:hasMultipleCategoriesSlice\"\r\n                    class=\"class-rating {{category.toLowerCase()}} border-round-xl px-0\"><span\r\n                        class=\"font-xs text-white  py-1 px-0 line-height-1\">{{category}}</span></p-chip>\r\n                        <!-- <span *ngIf=\"classroomFile.categories.length > 1\"  class=\"font-xs text-muted mt-2 more-circle\"\r\n                            [pTooltip]=\"(classroomFile.categories | slice:hasMultipleCategoriesSlice).join(', ')\"\r\n                            tooltipPosition=\"top\">\r\n                            +{{classroomFile.categories.length - hasMultipleCategoriesSlice}}\r\n                          </span> -->\r\n                          <div *ngIf=\"classroomFile.categories.length > 1\">\r\n                            <ng-container *ngIf=\"!isLarge; else notMobileBlock\" >\r\n                                <span class=\"font-xs text-muted mt-2 more-circle\"\r\n                                    [pTooltip]=\"(classroomFile.categories | slice:1).join(', ')\"\r\n                                    tooltipPosition=\"top\">\r\n                                    +{{classroomFile.categories.length - 1}}\r\n                                  </span>\r\n                            </ng-container>\r\n                            <ng-template #notMobileBlock>\r\n                                <span *ngIf=\"classroomFile.categories.length > 2\"  class=\"font-xs text-muted mt-2 more-circle\"\r\n                                    [pTooltip]=\"(classroomFile.categories | slice:2).join(', ')\"\r\n                                    tooltipPosition=\"top\">\r\n                                    +{{classroomFile.categories.length - 2}}\r\n                                </span>\r\n                            </ng-template>\r\n                        </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <div class=\"library-actions-button mx-2\" id=\"lib-file-burger-{{classroomFile.fileId}}\" (click)=\"!showGroupActions ? menu3.toggle($event) : null\">\r\n\r\n        <div *ngIf=\"showGroupActions || this.libraryService.isPreviewingFromChat()\" (click)=\"checkLibraryItem(checkLibMenu)\"\r\n            style=\"position: relative;\">\r\n            <img *ngIf=\"!checked\" src=\"/assets/icons/library/group-item-empty.svg\" class=\"pointer\" style=\"width:1.75rem; height:1.75rem\">\r\n            <img *ngIf=\"checked\" src=\"/assets/icons/library/group-item-full.svg\" class=\"pointer\" style=\"width:1.75rem; height:1.75rem\">\r\n            <!-- <div id=\"check-lib-{{libraryFile.filePath}}-1\" #checkLibMenu class=\"inner-circle-group\"></div> -->\r\n        </div>\r\n        <img *ngIf=\"!showGroupActions && !this.libraryService.isPreviewingFromChat()\" src=\"/assets/icons/library/library-more-actions.svg\" class=\"pointer hvr-grow\" style=\"width:1.75rem; height:1.75rem\">\r\n        <!-- <i class=\"ml-auto pi pi-ellipsis-h lession-more-icon center text-primary\"></i> -->\r\n    </div>\r\n\r\n</li>\r\n<p-menu #menu3 popup=\"popup\" [model]=\"menuItems\" appendTo=\"body\"></p-menu>\r\n"], "mappings": "AAAA,SAAoBA,YAAY,QAA0C,eAAe;AAEzF,SAASC,aAAa,QAA0B,uBAAuB;AACvE,SAASC,IAAI,QAAQ,gBAAgB;AAGrC,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,iCAAiC,QAAQ,sEAAsE;AAIxH,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;;;;;ICAjBC,EAAA,CAAAC,cAAA,UAAgD;IAC5CD,EAAA,CAAAE,SAAA,yBAA2D;IAC/DF,EAAA,CAAAG,YAAA,EAAM;;;;IADyBH,EAAA,CAAAI,SAAA,EAAc;IAAdJ,EAAA,CAAAK,UAAA,SAAAC,QAAA,CAAc;;;;;IAE7CN,EAAA,CAAAE,SAAA,cAAsG;;;;;IAS7BF,EADzE,CAAAC,cAAA,aACyE,eACb;IAAAD,EAAA,CAAAO,MAAA,GAAY;IAAOP,EAAP,CAAAG,YAAA,EAAO,EAAS;;;;IADpFH,EAAA,CAAAQ,sBAAA,kBAAAC,WAAA,CAAAC,WAAA,4BAAoE;IACZV,EAAA,CAAAI,SAAA,GAAY;IAAZJ,EAAA,CAAAW,iBAAA,CAAAF,WAAA,CAAY;;;;;IAO5DT,EAAA,CAAAY,uBAAA,GAAqD;IACjDZ,EAAA,CAAAC,cAAA,eAE0B;;IACtBD,EAAA,CAAAO,MAAA,GACF;IAAAP,EAAA,CAAAG,YAAA,EAAO;;;;;IAHLH,EAAA,CAAAI,SAAA,EAA4D;IAA5DJ,EAAA,CAAAK,UAAA,aAAAL,EAAA,CAAAa,WAAA,OAAAC,MAAA,CAAAC,aAAA,CAAAC,UAAA,KAAAC,IAAA,OAA4D;IAE5DjB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAkB,kBAAA,OAAAJ,MAAA,CAAAC,aAAA,CAAAC,UAAA,CAAAG,MAAA,UACF;;;;;IAGFnB,EAAA,CAAAC,cAAA,eAE0B;;IACtBD,EAAA,CAAAO,MAAA,GACJ;IAAAP,EAAA,CAAAG,YAAA,EAAO;;;;IAHHH,EAAA,CAAAK,UAAA,aAAAL,EAAA,CAAAa,WAAA,OAAAC,MAAA,CAAAC,aAAA,CAAAC,UAAA,KAAAC,IAAA,OAA4D;IAE5DjB,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAkB,kBAAA,OAAAJ,MAAA,CAAAC,aAAA,CAAAC,UAAA,CAAAG,MAAA,UACJ;;;;;IAJAnB,EAAA,CAAAoB,UAAA,IAAAC,gEAAA,mBAE0B;;;;IAFnBrB,EAAA,CAAAK,UAAA,SAAAS,MAAA,CAAAC,aAAA,CAAAC,UAAA,CAAAG,MAAA,KAAyC;;;;;IATtDnB,EAAA,CAAAC,cAAA,UAAiD;IAQ/CD,EAPA,CAAAoB,UAAA,IAAAE,0DAAA,2BAAqD,IAAAC,yDAAA,gCAAAvB,EAAA,CAAAwB,sBAAA,CAOxB;IAOjCxB,EAAA,CAAAG,YAAA,EAAM;;;;;IAdaH,EAAA,CAAAI,SAAA,EAAgB;IAAAJ,EAAhB,CAAAK,UAAA,UAAAS,MAAA,CAAAW,OAAA,CAAgB,aAAAC,iBAAA,CAAmB;;;;;IAsBlE1B,EAAA,CAAAE,SAAA,cAA6H;;;;;IAC7HF,EAAA,CAAAE,SAAA,cAA2H;;;;;;IAH/HF,EAAA,CAAAC,cAAA,cACgC;IAD4CD,EAAA,CAAA2B,UAAA,mBAAAC,iEAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAC,GAAA;MAAA,MAAAhB,MAAA,GAAAd,EAAA,CAAA+B,aAAA;MAAA,OAAA/B,EAAA,CAAAgC,WAAA,CAASlB,MAAA,CAAAmB,gBAAA,CAAAnB,MAAA,CAAAoB,YAAA,CAA8B;IAAA,EAAC;IAGhHlC,EADA,CAAAoB,UAAA,IAAAe,iDAAA,kBAA6H,IAAAC,iDAAA,kBACF;IAE/HpC,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAI,SAAA,EAAc;IAAdJ,EAAA,CAAAK,UAAA,UAAAS,MAAA,CAAAuB,OAAA,CAAc;IACdrC,EAAA,CAAAI,SAAA,EAAa;IAAbJ,EAAA,CAAAK,UAAA,SAAAS,MAAA,CAAAuB,OAAA,CAAa;;;;;IAGvBrC,EAAA,CAAAE,SAAA,cAAkM;;;ADnC1M,WAAaoC,2BAA2B;EAAlC,MAAOA,2BAA2B;IAiCtCC,YACSC,cAA8B,EAC7BC,cAA8B,EAC9BC,aAA4B,EAC5BC,YAA0B,EAC1BC,WAAwB,EACxBC,IAAgB;MALjB,KAAAL,cAAc,GAAdA,cAAc;MACb,KAAAC,cAAc,GAAdA,cAAc;MACd,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,YAAY,GAAZA,YAAY;MACZ,KAAAC,WAAW,GAAXA,WAAW;MACX,KAAAC,IAAI,GAAJA,IAAI;MArCL,KAAAC,SAAS,GAAG,EAAe;MAC3B,KAAA/B,aAAa,GAAQ,EAAS;MAE9B,KAAAgC,qBAAqB,GAAG,UAAU;MAClC,KAAAC,gBAAgB,GAAG,KAAK;MACxB,KAAAC,iBAAiB,GAAG,KAAK;MACzB,KAAAC,gBAAgB,GAAa,EAAc;MAC3C,KAAAC,aAAa,GAAG,KAAK;MACrB,KAAAC,mBAAmB,GAAG,IAAI;MAC1B,KAAAC,OAAO,GAAG,KAAK;MAGd,KAAAC,WAAW,GAAG,IAAI5D,YAAY,EAAE;MAChC,KAAA6D,cAAc,GAAG,IAAI7D,YAAY,EAAE;MACnC,KAAA8D,YAAY,GAAG,IAAI9D,YAAY,EAAE;MAE3C,KAAA+D,gBAAgB,GAAa,EAAE;MAC/B,KAAAC,UAAU,GAAG,KAAK;MAClB,KAAAC,oBAAoB,GAAG,KAAK;MAE5B,KAAAC,UAAU,GAAG,CAAC;MACd,KAAAC,YAAY,GAAG,CAAC;MAChB,KAAAC,iBAAiB,GAAG,KAAK;MACzB,KAAAC,UAAU,GAAY,KAAK;MAC3B,KAAAC,IAAI,GAAuB,EAAE;MACrB,KAAAC,IAAI,GAAG,IAAIlE,OAAO,EAAE;MAC5B,KAAAsC,OAAO,GAAY,KAAK;MACxB,KAAA6B,eAAe,GAAG,EAAE;MACpB,KAAAC,SAAS,GAAe,EAAE;MAC1B,KAAA1C,OAAO,GAAG,KAAK;MACf,KAAA2C,0BAA0B,GAAG,CAAC;IAQ1B;IAEJC,QAAQA,CAAA;MACN,IAAI,IAAI,CAACjB,mBAAmB,EAAE;QAC5B,IAAI,CAACkB,gCAAgC,EAAE;MACzC;MACA,IAAI,CAACL,IAAI,CAACM,IAAI,GAAG,IAAI,CAAC/B,cAAc,CAACQ,gBAAgB,CAACwB,SAAS,CAACC,GAAG,IAAG;QACpE,IAAI,CAACA,GAAG,EAAE;UACR,IAAI,CAACpC,OAAO,GAAG,KAAK;QACtB;QACA;QACA;QACA;MACF,CAAC,CAAC;MAEF,IAAI,CAAC4B,IAAI,CAACS,GAAG,CAAC,IAAI,CAACjC,cAAc,CAACkC,UAAU,CAACC,IAAI,EAAE,CAACJ,SAAS,CAACC,GAAG,IAAG;QAClE,IAAIA,GAAG,EAAE;UACP,IAAIA,GAAG,CAACI,MAAM,EAAE;YACd,IAAI,CAACpD,OAAO,GAAG,IAAI;YACnB,IAAI,CAAC2C,0BAA0B,GAAG,CAAC;UACrC;QACF;MACF,CAAC,CAAC,CAAC;MACH,IAAI,CAACU,uBAAuB,CAAC,IAAI,CAAC/D,aAAa,CAAC;IAElD;IAEAgE,WAAWA,CAAA;MACT,IAAI,CAACd,IAAI,CAACe,WAAW,EAAE;IACzB;IAEAC,QAAQA,CAACC,SAAiB;MACxB,IAAI,CAACxB,UAAU,GAAG,KAAK;MACvB,IAAI,IAAI,CAACD,gBAAgB,CAAC0B,OAAO,CAACD,SAAS,CAAC,KAAK,CAAC,CAAC,EACjD,IAAI,CAACzB,gBAAgB,CAAC2B,MAAM,CAAC,IAAI,CAAC3B,gBAAgB,CAAC0B,OAAO,CAACD,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,KAE1E,IAAI,CAACzB,gBAAgB,CAAC4B,IAAI,CAACH,SAAS,CAAC;MAEvC,IAAI,IAAI,CAACvB,oBAAoB,IAAI,IAAI,CAACF,gBAAgB,CAACtC,MAAM,KAAK,CAAC,EACjE,IAAI,CAACwC,oBAAoB,GAAG,KAAK,CAAC,KAElC,IAAI,CAACA,oBAAoB,GAAG,IAAI;IACpC;IAEA2B,iBAAiBA,CAACC,MAAW;MAC3BC,QAAQ,CAACC,cAAc,CAAC,sBAAsB,GAAGF,MAAM,CAAE,CAACG,KAAK,CAACC,OAAO,GAAG,MAAM;MAChFH,QAAQ,CAACC,cAAc,CAAC,qBAAqB,GAAGF,MAAM,CAAE,CAACG,KAAK,CAACC,OAAO,GAAG,OAAO;MAChFH,QAAQ,CAACC,cAAc,CAAC,qBAAqB,GAAGF,MAAM,CAAE,CAACG,KAAK,CAACE,GAAG,GAAG,KAAK;IAC5E;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IAEAC,WAAWA,CAACC,IAAY;MACtB,IAAIA,IAAI,EAAE;QACR,IAAIC,CAAC,GAAGD,IAAI,CAACE,WAAW,CAAC,GAAG,CAAC;QAC7B,IAAID,CAAC,KAAK,CAAC,CAAC,EACVA,CAAC,GAAGD,IAAI,CAACE,WAAW,CAAC,IAAI,CAAC;QAC5B,IAAIC,MAAM,GAAGH,IAAI,CAACI,SAAS,CAACH,CAAC,GAAG,CAAC,CAAC;QAClC,OAAOE,MAAM;MACf;MACA,OAAOH,IAAI;IACb;IAEAhB,uBAAuBA,CAACqB,IAAS;MAC/B,IAAIC,OAAO,GAAgBD,IAAI;MAE/B,MAAMhC,SAAS,GAAe,CAC5B;QACEkC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,2BAA2B;QAChDC,OAAO,EAAGC,KAAK,IAAI;UACjB,IAAI,CAACC,gBAAgB,CAACL,OAAO,CAACN,IAAI,CAAC;QACrC;OACD,EACD;QACEO,KAAK,EAAE,UAAU;QAAEC,IAAI,EAAE,kBAAkB;QAC3CC,OAAO,EAAGC,KAAK,IAAI;UACjB,IAAI,CAACE,OAAO,CAACN,OAAO,CAACN,IAAI,CAAC;QAC5B;OACD,EACD;QACEO,KAAK,EAAE,UAAU;QAAEC,IAAI,EAAE,4BAA4B;QACrDC,OAAO,EAAGC,KAAK,IAAI;UACjB,IAAI,CAACG,YAAY,CAACP,OAAO,CAACN,IAAI,CAAC;QACjC;OACD,EACD;QACEO,KAAK,EAAE,OAAO;QAAEC,IAAI,EAAE,uBAAuB;QAC7CC,OAAO,EAAGC,KAAK,IAAI;UACjB,IAAI,IAAI,CAAC5D,WAAW,CAACgE,SAAS,EAAE;YAC9B,IAAI,CAACC,gBAAgB,CAACT,OAAO,CAAC;UAChC,CAAC,MAAM;YACL,MAAMU,wBAAwB,GAAG,IAAI,CAACtE,cAAc,CAACuE,2BAA2B,EAAE;YAClF,IAAIC,MAAM,CAACC,IAAI,CAACH,wBAAwB,CAAC,CAAC3F,MAAM,GAAG,CAAC,EAAE;cACpD,MAAM+F,iBAAiB,GAAG,IAAI,CAAC1E,cAAc,CAACuE,2BAA2B,EAAE,CAACG,iBAAiB;cAC7F;cACA,IAAIA,iBAAiB,CAAC/F,MAAM,KAAK,CAAC,IAAI,IAAI,CAACyB,WAAW,CAACuE,SAAS,EAAE;gBAChE,IAAIC,IAAI,GAAQ;kBACdC,OAAO,EAAE,CAACjB,OAAO,CAACb,MAAM,CAAC;kBACzB+B,WAAW,EAAE,IAAI,CAAC9E,cAAc,CAACuE,2BAA2B,EAAE,CAACQ,EAAE;kBACjEC,UAAU,EAAE,CAACN,iBAAiB,CAAC,CAAC,CAAC,CAACO,SAAS;iBAC5C;gBACD,IAAI,CAACC,cAAc,CAACN,IAAI,CAAC;cAC3B;YACF,CAAC,MAAM;cACL,IAAI,CAACO,uBAAuB,CAAC7H,iCAAiC,EAC5D;gBACE8H,MAAM,EAAE,WAAW;gBACnBC,cAAc,EAAE,SAAS,GAAG,IAAI,CAAChC,WAAW,CAACO,OAAO,CAAC0B,IAAI,CAAC,GAAG,WAAW;gBACxEC,WAAW,EAAE,oBAAoB;gBACjCC,WAAW,EAAE5B;eACd,CAAC;YACN;YAEA6B,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC1F,cAAc,CAACuE,2BAA2B,EAAE,CAACG,iBAAiB,CAAC;YAChF;YACA;YACA;YACA;YACA;YACA;YACA;UACF;QACF;OACD,EACD;QACEb,KAAK,EAAE,SAAS;QAAEC,IAAI,EAAE,uBAAuB;QAC/CC,OAAO,EAAGC,KAAK,IAAI;UACjB,IAAIY,IAAI,GAAQ;YACd7B,MAAM,EAAE,IAAI,CAACxE,aAAa,CAACwE,MAAO;YAClC+B,WAAW,EAAE,IAAI,CAAC9E,cAAc,CAACuE,2BAA2B,EAAE,CAACQ,EAAE;YACjEC,UAAU,EAAE,CAAC,IAAI,CAAChF,cAAc,CAACuE,2BAA2B,EAAE,CAACoB,OAAO,EAAEV,SAAS;WAClF;UACD,IAAI,IAAI,CAAC7E,WAAW,CAACgE,SAAS,EAAE;YAC9B,IAAI,CAACwB,gBAAgB,CAAChB,IAAI,CAAC;UAC7B,CAAC,MAAM;YAEL,IAAI,CAACgB,gBAAgB,CAAChB,IAAI,CAAC;YAC3B;YACA;YACA;YACA;YACA;YACA;YACA;UACF;QACF;OACD,EACD;QACEf,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,yBAAyB;QAC9CC,OAAO,EAAGC,KAAK,IAAI;UACjB,IAAI,CAACmB,uBAAuB,CAAC7H,iCAAiC,EAC5D;YACE8H,MAAM,EAAE,MAAM;YACdC,cAAc,EAAE,QAAQ,GAAG,IAAI,CAAChC,WAAW,CAACO,OAAO,CAAC0B,IAAI,CAAC,GAAG,gBAAgB;YAC5EC,WAAW,EAAE,qBAAqB;YAClCC,WAAW,EAAE5B,OAAO;YACpBiC,OAAO,EAAE,IAAI,CAACA;WACf,CAAC;QACN;OACD,EACD;QACEhC,KAAK,EAAE,MAAM;QAAEC,IAAI,EAAE,uBAAuB;QAC5CC,OAAO,EAAGC,KAAK,IAAI;UACjB,IAAI,CAAC/D,cAAc,CAACkF,uBAAuB,CAAC,IAAI,CAACjF,aAAa,EAAE5C,iCAAiC,EAAE,GAAG,EACpG;YACE8H,MAAM,EAAE,0BAA0B;YAClCC,cAAc,EAAE,EAAE;YAClBE,WAAW,EAAE,mBAAmB;YAChCC,WAAW,EAAE5B;WACd,EAAE,IAAI,EAAGH,MAAW,IAAI;YACvBgC,OAAO,CAACC,GAAG,CAACjC,MAAM,CAAC;YACnB,IAAIA,MAAM,IAAIA,MAAM,CAAC2B,MAAM,KAAK,0BAA0B,EAAE;cAC1D,IAAI,CAAC,IAAI,CAACnF,cAAc,CAAC6F,eAAe,CAACrC,MAAM,CAAC,EAAE;gBAEhDgC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACnH,aAAa,CAAC;gBAC/BkH,OAAO,CAACC,GAAG,CAACjC,MAAM,CAAC;gBACnB,IAAI,CAAClF,aAAa,GAAG;kBAAE,GAAGkF,MAAM,CAACA;gBAAM,CAAE;gBACzCG,OAAO,GAAG;kBAAE,GAAGH,MAAM,CAACA;gBAAM,CAAE;cAChC;YACF;UACF,CAAC,CAAC;QACN;OACD,EACD;QACEI,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE,mBAAmB;QAC1CC,OAAO,EAAGC,KAAK,IAAI;UACjB,IAAI,CAAC+B,UAAU,CAACpC,IAAI,CAACZ,MAAM,CAAC;QAC9B;OACD,CACF;MAED,IAAI,CAACpB,SAAS,GAAGA,SAAS,CAACqE,MAAM,CAAEC,QAAkB,IAAI;QACrD,MAAMC,SAAS,GAAGD,QAAQ,CAACpC,KAAe;QAC1C,OAAO,IAAI,CAACnD,gBAAgB,CAACyF,QAAQ,CAACD,SAAS,CAAC;MACpD,CAAC,CAAC;IAEJ;IAEAE,cAAcA,CAACrD,MAAc;MAC3BC,QAAQ,CAACC,cAAc,CAAC,qBAAqB,GAAGF,MAAM,CAAE,CAACG,KAAK,CAACC,OAAO,GAAG,MAAM;MAC/EH,QAAQ,CAACC,cAAc,CAAC,sBAAsB,GAAGF,MAAM,CAAE,CAACG,KAAK,CAACC,OAAO,GAAG,OAAO;MACjFH,QAAQ,CAACC,cAAc,CAAC,sBAAsB,GAAGF,MAAM,CAAE,CAACG,KAAK,CAACE,GAAG,GAAG,KAAK;IAC7E;IAEAiD,MAAMA,CAACtD,MAAc,EAAEuD,OAAe;MACpC;MACA;MACA;MACA,IAAI,CAAC7E,IAAI,CAACS,GAAG,CAAC,IAAI,CAAClC,cAAc,CAACuG,qBAAqB,CAACxD,MAAM,EAAEuD,OAAO,CAAC,CAAClE,IAAI,CAAChF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC4E,SAAS,CAACC,GAAG,IAAG;QACrG,IAAI,CAAC9B,YAAY,CAACqG,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACFlB,OAAO,CAACC,GAAG,CAACzD,GAAG,CAAC;QAChB;MACF,CAAC,CAAC,CAAC;IACL;IAEAxC,gBAAgBA,CAACmH,EAAO;MACtB,IAAI,CAAC/G,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;MAC5B;MACA;MACA,IAAI,IAAI,CAACA,OAAO,EAAE;QAChB,IAAI,CAACG,cAAc,CAAC6G,gBAAgB,CAAChE,IAAI,CAAC,IAAI,CAACtE,aAAa,CAAC;QAC7D,IAAI,CAACwC,cAAc,CAAC+F,IAAI,CAAC;UAAEjH,OAAO,EAAE,IAAI;UAAEkH,IAAI,EAAE,IAAI,CAACxI;QAAa,CAAE,CAAC;MACvE,CAAC,MAAM;QACL,IAAI,CAACyB,cAAc,CAAC6G,gBAAgB,GAAG,IAAI,CAAC7G,cAAc,CAAC6G,gBAAgB,CAACb,MAAM,CAAEY,EAAe,IAAI;UACrG,OAAO,EAAEA,EAAE,CAACtD,IAAI,IAAI,IAAI,CAAC/E,aAAa,CAAC+E,IAAI,CAAC;QAC9C,CAAC,CAAC;QACF,IAAI,CAACvC,cAAc,CAAC+F,IAAI,CAAC;UAAEjH,OAAO,EAAE,KAAK;UAAEkH,IAAI,EAAE,IAAI,CAACxI;QAAa,CAAE,CAAC;MACxE;IACF;IAEAyI,oBAAoBA,CAAA;MAClB;MACA,MAAMC,aAAa,GAAG,IAAI,CAACvF,eAAe,CAACsE,MAAM,CAAEkB,IAAU,IAAI;QAC/D,OAAO,IAAI,CAACxC,iBAAiB,CAACyC,IAAI,CAAEC,OAAa,IAAI;UACnD,OAAOA,OAAO,CAACnC,SAAS,KAAKiC,IAAI,CAACjC,SAAS;QAC7C,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF,MAAMoC,WAAW,GAAG7C,MAAM,CAAC8C,MAAM,CAACL,aAAa,CAACM,MAAM,CAAC,CAACC,GAAQ,EAAEN,IAAS,KAAI;QAC7E,IAAI,CAACM,GAAG,CAACN,IAAI,CAACjC,SAAS,CAAC,EAAE;UACxBuC,GAAG,CAACN,IAAI,CAACjC,SAAS,CAAC,GAAGiC,IAAI,CAACO,SAAS;QACtC;QACA,OAAOD,GAAG;MACZ,CAAC,EAAE,EAAE,CAAC,CAAC;MACP,OAAOH,WAAW,CAAC5I,IAAI,CAAC,IAAI,CAAC;IAC/B;IAEQiJ,kBAAkBA,CAAA;MACxB,IAAI,IAAI,CAACxG,UAAU,EACjB,IAAI,CAACA,UAAU,GAAG,KAAK,CAAC,KAExB,IAAI,CAACA,UAAU,GAAG,IAAI;MAExB,IAAI,IAAI,CAACD,gBAAgB,CAACtC,MAAM,KAAK,CAAC,EAAE;QACtC,IAAI,CAAC2B,SAAS,CAACoE,iBAAiB,CAACiD,OAAO,CAAEP,OAAwB,IAAI;UACpE,IAAI,CAACnG,gBAAgB,CAAC4B,IAAI,CAACuE,OAAO,CAACrC,EAAE,CAAC;QACxC,CAAC,CAAC;MACJ,CAAC,MAEC,IAAI,CAAC9D,gBAAgB,GAAG,EAAE;IAC9B;IAEQ2G,QAAQA,CAACpC,WAAwB,EAAEqC,UAAkB;MAC3D,IAAI,CAACpG,IAAI,CAACS,GAAG,CAAC,IAAI,CAAClC,cAAc,CAAC8H,gBAAgB,CAACtC,WAAW,CAACzC,MAAO,EAAE8E,UAAU,CAACE,QAAS,CAAC,CAAC/F,SAAS,CAAEgG,QAAQ,IAAI;QACnH,IAAI,CAAC,IAAI,CAAC/H,cAAc,CAAC6F,eAAe,CAACkC,QAAQ,CAAC,EAAE;UAElD,IAAI,CAAC7H,YAAY,CAACqG,mBAAmB,CAAC;YACpCC,QAAQ,EAAE,SAAS;YACnBC,OAAO,EAAE,EAAE;YACXC,MAAM,EAAE;WACT,CAAC;UACF,IAAI,CAAC3G,cAAc,CAACiI,uBAAuB,CAACzC,WAAW,EAAEqC,UAAU,CAAC;UACpE,IAAI,CAAC7H,cAAc,CAACkI,iBAAiB,CAAC,IAAI,CAAC;QAC7C;MACF,CAAC,CAAC,CAAC;MACH,IAAI,CAACrG,QAAQ,EAAE;IACjB;IAEQqC,OAAOA,CAACiE,QAAa;MAC3B,IAAI,CAACC,OAAO,GAAG/K,WAAW,CAACgL,MAAM,GAAG,GAAG,GAAGF,QAAQ;MAClD,MAAMG,MAAM,GAAGtF,QAAQ,CAACuF,aAAa,CAAC,UAAU,CAAC;MACjDD,MAAM,CAACpF,KAAK,CAACsF,QAAQ,GAAG,OAAO;MAC/BF,MAAM,CAACpF,KAAK,CAACuF,IAAI,GAAG,GAAG;MACvBH,MAAM,CAACpF,KAAK,CAACE,GAAG,GAAG,GAAG;MACtBkF,MAAM,CAACpF,KAAK,CAACwF,OAAO,GAAG,GAAG;MAC1BJ,MAAM,CAACK,KAAK,GAAG,IAAI,CAACP,OAAO;MAC3BpF,QAAQ,CAAC4F,IAAI,CAACC,WAAW,CAACP,MAAM,CAAC;MACjCA,MAAM,CAACQ,KAAK,EAAE;MACdR,MAAM,CAACS,MAAM,EAAE;MACf/F,QAAQ,CAACgG,WAAW,CAAC,MAAM,CAAC;MAC5BhG,QAAQ,CAAC4F,IAAI,CAACK,WAAW,CAACX,MAAM,CAAC;MACjC,IAAI,CAACnI,YAAY,CAACqG,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;IACJ;IAEQZ,UAAUA,CAAChD,MAAW;MAC5B,IAAI,CAACjC,WAAW,CAACgG,IAAI,CAAC,IAAI,CAACvI,aAAa,CAAC;MACzC,IAAI,CAACgE,WAAW,EAAE;IACpB;IAEQT,gCAAgCA,CAAA;MACtC,IAAI,IAAI,CAACvD,aAAa,CAACwE,MAAM,EAAE;QAC7B,OAAO,IAAI,CAACtB,IAAI,CAACS,GAAG,CAAC,IAAI,CAAClC,cAAc,CAAC8B,gCAAgC,CAAC,IAAI,CAACvD,aAAa,CAACwE,MAAO,CAAC,CAACX,IAAI,CAAChF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC4E,SAAS,CAACC,GAAG,IAAG;UAClI,IAAI,CAACP,eAAe,GAAGO,GAAG;QAC5B,CAAC,CAAC,CAAC;MACL;MACA,OAAO,EAAE;IACX;IAEQoC,gBAAgBA,CAACT,OAAY;MACnC,IAAIgB,IAAI,GAAQ;QACdC,OAAO,EAAE,CAACjB,OAAO,CAACb,MAAM,CAAC;QACzB+B,WAAW,EAAE,IAAI,CAAC9E,cAAc,CAACuE,2BAA2B,EAAE,CAACQ,EAAE;QACjEC,UAAU,EAAE,CAAC,IAAI,CAAChF,cAAc,CAACuE,2BAA2B,EAAE,CAACoB,OAAO,EAAEV,SAAS;OAClF;MACD,IAAI,CAACC,cAAc,CAACN,IAAI,CAAC;IAC3B;IAEQsE,gBAAgBA,CAACC,GAAQ;MAC/B1D,OAAO,CAACC,GAAG,CAAC,IAAI,CAACnH,aAAa,CAAC;MAC/B4K,GAAG,CAACxB,OAAO,CAAC,CAACyB,KAAa,EAAEtE,WAAmB,KAAI;QACjD,IAAIuE,GAAG,GAAa,EAAE;QACtB,IAAIxE,OAAO,GAAa,EAAE;QAC1B,KAAK,IAAIqC,IAAI,IAAIkC,KAAK,EAAE;UACtBC,GAAG,CAACxG,IAAI,CAACqE,IAAI,CAACjC,SAAU,CAAC;QAC3B;QACAJ,OAAO,CAAChC,IAAI,CAAC,IAAI,CAACtE,aAAa,CAACwE,MAAO,CAAC;QACxC,IAAI6B,IAAI,GAAQ;UACdC,OAAO,EAAEA,OAAO;UAChBC,WAAW,EAAEA,WAAW;UACxBE,UAAU,EAAEqE;SACb;QACD,IAAI,CAACnE,cAAc,CAACN,IAAI,CAAC;MAC3B,CAAC,CAAC;IACJ;IAEQ0E,kBAAkBA,CAACH,GAAQ;MACjC1D,OAAO,CAACC,GAAG,CAAC,IAAI,CAACnH,aAAa,CAAC;MAC/B4K,GAAG,CAACxB,OAAO,CAAC,CAACyB,KAAa,EAAEtE,WAAmB,KAAI;QACjD,IAAIuE,GAAG,GAAa,EAAE;QACtB,IAAIxE,OAAO,GAAa,EAAE;QAC1B,KAAK,IAAIqC,IAAI,IAAIkC,KAAK,EAAE;UACtBC,GAAG,CAACxG,IAAI,CAACqE,IAAI,CAACjC,SAAU,CAAC;QAC3B;QACAJ,OAAO,CAAChC,IAAI,CAAC,IAAI,CAACtE,aAAa,CAACwE,MAAO,CAAC;QACxC,IAAI6B,IAAI,GAAQ;UACd7B,MAAM,EAAE,IAAI,CAACxE,aAAa,CAACwE,MAAO;UAClC+B,WAAW,EAAEA;SACd;QACD,IAAI,CAACc,gBAAgB,CAAChB,IAAI,CAAC;MAC7B,CAAC,CAAC;IACJ;IAEAX,gBAAgBA,CAACsF,GAAW;MAC1B,MAAMnB,OAAO,GAAG/K,WAAW,CAACgL,MAAM,GAAG,GAAG,GAAGkB,GAAG;MAC9CC,MAAM,CAACC,IAAI,CAACrB,OAAO,EAAE,QAAQ,CAAC;IAChC;IAEQlD,cAAcA,CAACwE,UAAe;MACpC,IAAI,CAACjI,IAAI,CAACS,GAAG,CAAC,IAAI,CAAClC,cAAc,CAAC2J,uBAAuB,CAACD,UAAU,CAAC,CAACtH,IAAI,CAAChF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC4E,SAAS,CAACC,GAAG,IAAG;QAElGwD,OAAO,CAACC,GAAG,CAAC,IAAI,CAACpF,SAAS,CAAC;QAC3B,IAAI,CAACH,YAAY,CAACqG,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,IAAI,CAACvG,WAAW,CAACuE,SAAS,EAAE;UAC9B,IAAI,CAAC3E,cAAc,CAAC4J,6BAA6B,CAACC,QAAQ,CAACH,UAAU,CAAC5E,WAAW,CAAC,CAAC;QACrF;MACF,CAAC,CAAC,CAAC;IACL;IAEQc,gBAAgBA,CAAC8D,UAAe;MACtCjE,OAAO,CAACC,GAAG,CAAC,IAAI,CAACpF,SAAS,CAAC;MAC3B,IAAI,CAACmB,IAAI,CAACS,GAAG,CAAC,IAAI,CAAClC,cAAc,CAAC8J,cAAc,CAACJ,UAAU,CAAC,CAACtH,IAAI,CAAChF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC4E,SAAS,CAACC,GAAG,IAAG;QACzF,IAAI,CAAC9B,YAAY,CAACqG,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAAC3F,YAAY,CAAC8F,IAAI,CAAC7E,GAAG,CAAC;QAC3B,IAAI,CAACjC,cAAc,CAAC4J,6BAA6B,CAACC,QAAQ,CAACH,UAAU,CAAC5E,WAAW,CAAC,CAAC;MACrF,CAAC,CAAC,CAAC;IACL;IAEQK,uBAAuBA,CAAC4E,SAAc,EAAEC,UAAgB,EAAEC,KAAK,GAAG,GAAG;MAE3E,IAAI,CAAChK,cAAc,CAACkF,uBAAuB,CAAC,IAAI,CAACjF,aAAa,EAAE6J,SAAS,EAAE,GAAG,EAAEC,UAAU,EAAE,IAAI,EAAGvG,MAAW,IAAI;QAChH,IAAIA,MAAM,IAAIA,MAAM,CAAC2B,MAAM,KAAK,WAAW,EAAE;UAC3C,IAAI,CAAC8D,gBAAgB,CAACzF,MAAM,CAAC0F,GAAG,CAACA,GAAG,CAAC;QACvC,CAAC,MAAM,IAAI1F,MAAM,IAAIA,MAAM,CAAC2B,MAAM,KAAK,aAAa,EAAE;UACpD,IAAI,CAACkE,kBAAkB,CAAC7F,MAAM,CAAC0F,GAAG,CAACA,GAAG,CAAC;QACzC,CAAC,MAAM,IAAI1F,MAAM,IAAIA,MAAM,CAAC2B,MAAM,KAAK,MAAM,EAAE;UAC7C,IAAI,CAACwC,QAAQ,CAACnE,MAAM,CAAC+B,WAAW,EAAE/B,MAAM,CAACyG,MAAM,CAAC;QAClD;MACF,CAAC,CAAC;MAEF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA;MACA;MACA;MACA;MACA;IACF;IAEQ/F,YAAYA,CAACoF,GAAW;MAC9B,MAAMnB,OAAO,GAAG/K,WAAW,CAACgL,MAAM,GAAG,GAAG,GAAGkB,GAAG;MAC9C,IAAI,CAAC9H,IAAI,CAACS,GAAG,CAAC,IAAI,CAAC7B,IAAI,CAAC8J,GAAG,CAAC/B,OAAO,EAAE;QAAEgC,YAAY,EAAE;MAAM,CAAE,CAAC,CAC3DpI,SAAS,CAAEqI,IAAU,IAAI;QACxB,MAAMC,IAAI,GAAGtH,QAAQ,CAACuF,aAAa,CAAC,GAAG,CAAC;QACxC+B,IAAI,CAACC,IAAI,GAAGf,MAAM,CAACgB,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;QAC5CC,IAAI,CAACI,QAAQ,GAAG,IAAI,CAACrH,WAAW,CAAC+E,OAAO,CAAC;QACzCkC,IAAI,CAACK,KAAK,EAAE;MACd,CAAC,CAAC,CAAC;IACP;IAAC,QAAAC,CAAA,G;uBA/fU9K,2BAA2B,EAAAtC,EAAA,CAAAqN,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAvN,EAAA,CAAAqN,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAzN,EAAA,CAAAqN,iBAAA,CAAAK,EAAA,CAAA/N,aAAA,GAAAK,EAAA,CAAAqN,iBAAA,CAAAM,EAAA,CAAAC,YAAA,GAAA5N,EAAA,CAAAqN,iBAAA,CAAAQ,EAAA,CAAAC,WAAA,GAAA9N,EAAA,CAAAqN,iBAAA,CAAAU,EAAA,CAAAC,UAAA;IAAA;IAAA,QAAAC,EAAA,G;YAA3B3L,2BAA2B;MAAA4L,SAAA;MAAAC,MAAA;QAAArL,SAAA;QAAA/B,aAAA;QAAAsH,OAAA;QAAAtF,qBAAA;QAAAC,gBAAA;QAAAC,iBAAA;QAAAC,gBAAA;QAAAC,aAAA;QAAAC,mBAAA;QAAAC,OAAA;QAAA6D,iBAAA;MAAA;MAAAkH,OAAA;QAAA9K,WAAA;QAAAC,cAAA;QAAAC,YAAA;MAAA;MAAA6K,QAAA,GAAArO,EAAA,CAAAsO,kBAAA,CAF3B,CAAC3O,aAAa,CAAC;MAAA4O,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCpB5B5O,EAAA,CAAAC,cAAA,YAA2G;UACvGD,EAAA,CAAAO,MAAA,GACA;UAAAP,EAAA,CAAAC,cAAA,aAEsE;UAClED,EAAA,CAAAE,SAAA,aAAoD;UACxDF,EAAA,CAAAG,YAAA,EAAM;UAGEH,EAFR,CAAAC,cAAA,aAAgF,aACZ,cAC6D;UAAnHD,EAAA,CAAA2B,UAAA,mBAAAmN,2DAAA;YAAA9O,EAAA,CAAA6B,aAAA,CAAAkN,GAAA;YAAA,OAAA/O,EAAA,CAAAgC,WAAA,CAAS6M,GAAA,CAAApI,gBAAA,CAAAoI,GAAA,CAAA9N,aAAA,CAAA+E,IAAA,CAAoC;UAAA,EAAC;UAAqE9F,EAAA,CAAAO,MAAA,GAAmC;UAAAP,EAAA,CAAAG,YAAA,EAAO;UACnKH,EAAA,CAAAC,cAAA,aAAwB;UAIpBD,EAHA,CAAAoB,UAAA,IAAA4N,0CAAA,iBAAgD,KAAAC,2CAAA,kBAGsD;UACtGjP,EAAA,CAAAC,cAAA,gBAAiD;UAC7CD,EAAA,CAAAO,MAAA,IACJ;UAERP,EAFQ,CAAAG,YAAA,EAAO,EACL,EACJ;UAEFH,EADJ,CAAAC,cAAA,eAA+B,eAC8E;UACrGD,EAAA,CAAAoB,UAAA,KAAA8N,8CAAA,qBACyE;;UAO/DlP,EAAA,CAAAoB,UAAA,KAAA+N,2CAAA,kBAAiD;UAkBvEnP,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;UACNH,EAAA,CAAAC,cAAA,eAAiJ;UAA1DD,EAAA,CAAA2B,UAAA,mBAAAyN,2DAAAC,MAAA;YAAArP,EAAA,CAAA6B,aAAA,CAAAkN,GAAA;YAAA,MAAAO,QAAA,GAAAtP,EAAA,CAAAuP,WAAA;YAAA,OAAAvP,EAAA,CAAAgC,WAAA,EAAA6M,GAAA,CAAA7L,gBAAA,GAA6BsM,QAAA,CAAAE,MAAA,CAAAH,MAAA,CAAoB,GAAG,IAAI;UAAA,EAAC;UAQ5IrP,EANA,CAAAoB,UAAA,KAAAqO,2CAAA,kBACgC,KAAAC,2CAAA,kBAKkK;UAI1M1P,EAFI,CAAAG,YAAA,EAAM,EAEL;UACLH,EAAA,CAAAE,SAAA,qBAA0E;;;UA9D7CF,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAA2P,eAAA,KAAAC,GAAA,EAAAf,GAAA,CAAAxM,OAAA,IAAAwM,GAAA,CAAA7L,gBAAA,EAAA6L,GAAA,CAAAxL,OAAA,EAA6E;UACtGrD,EAAA,CAAAI,SAAA,EACA;UADAJ,EAAA,CAAAkB,kBAAA,MAAA2N,GAAA,CAAA/L,SAAA,kBAAA+L,GAAA,CAAA/L,SAAA,CAAAyE,EAAA,MACA;UAEIvH,EAAA,CAAAI,SAAA,EAAiE;UAAjEJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAA2P,eAAA,KAAAE,GAAA,EAAAhB,GAAA,CAAAxL,OAAA,GAAAwL,GAAA,CAAAxL,OAAA,EAAiE;UAK4DrD,EAAA,CAAAI,SAAA,GAAmC;UAAnCJ,EAAA,CAAAW,iBAAA,CAAAkO,GAAA,CAAAhJ,WAAA,CAAAgJ,GAAA,CAAA9N,aAAA,CAAA+G,IAAA,EAAmC;UAEjI9H,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAK,UAAA,YAAAwO,GAAA,CAAA9N,aAAA,CAAA+O,MAAA,CAAuB;UAGM9P,EAAA,CAAAI,SAAA,EAAqC;UAArCJ,EAAA,CAAAK,UAAA,SAAAwO,GAAA,CAAA3K,eAAA,CAAA/C,MAAA,KAAqC;UAErFnB,EAAA,CAAAI,SAAA,GACJ;UADIJ,EAAA,CAAAkB,kBAAA,MAAA2N,GAAA,CAAArF,oBAAA,QACJ;UAK6BxJ,EAAA,CAAAI,SAAA,GAAgE;UAAhEJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAA+P,WAAA,SAAAlB,GAAA,CAAA9N,aAAA,CAAAC,UAAA,KAAA6N,GAAA,CAAAzK,0BAAA,EAAgE;UAQ7EpE,EAAA,CAAAI,SAAA,GAAyC;UAAzCJ,EAAA,CAAAK,UAAA,SAAAwO,GAAA,CAAA9N,aAAA,CAAAC,UAAA,CAAAG,MAAA,KAAyC;UAmB5BnB,EAAA,CAAAI,SAAA,EAA6C;UAA7CJ,EAAA,CAAAgQ,sBAAA,2BAAAnB,GAAA,CAAA9N,aAAA,CAAAwE,MAAA,KAA6C;UAE5EvF,EAAA,CAAAI,SAAA,EAAoE;UAApEJ,EAAA,CAAAK,UAAA,SAAAwO,GAAA,CAAA7L,gBAAA,IAAA6L,GAAA,CAAArM,cAAA,CAAAyN,oBAAA,GAAoE;UAMpEjQ,EAAA,CAAAI,SAAA,EAAsE;UAAtEJ,EAAA,CAAAK,UAAA,UAAAwO,GAAA,CAAA7L,gBAAA,KAAA6L,GAAA,CAAArM,cAAA,CAAAyN,oBAAA,GAAsE;UAKvDjQ,EAAA,CAAAI,SAAA,EAAmB;UAAnBJ,EAAA,CAAAK,UAAA,UAAAwO,GAAA,CAAA1K,SAAA,CAAmB;;;;;;;SDxCnC7B,2BAA2B;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}