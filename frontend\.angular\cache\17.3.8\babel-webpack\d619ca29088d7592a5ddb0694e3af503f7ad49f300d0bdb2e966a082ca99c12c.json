{"ast": null, "code": "import { EventEmitter } from \"@angular/core\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nimport * as i2 from \"src/app/core/services/classroom.service\";\nimport * as i3 from \"src/app/core/services/lesson.service\";\nimport * as i4 from \"src/app/core/services/auth.service\";\nimport * as i5 from \"@angular/common\";\nconst _c0 = (a0, a1) => ({\n  \"text-white\": a0,\n  \"p-button-outlined\": a1\n});\nexport class NextLessonItemComponent {\n  constructor(generalService, classroomService, lessonService, authService) {\n    this.generalService = generalService;\n    this.classroomService = classroomService;\n    this.lessonService = lessonService;\n    this.authService = authService;\n    this.onLessonCardClicked = new EventEmitter();\n    this.user = {};\n  }\n  ngOnInit() {\n    this.user = this.authService.getLoggedInUser();\n  }\n  lessonCardClicked(todayLesson) {\n    const isWithin30Mins = this.generalService.isDifferenceWithin30Minutes(todayLesson.startingDate.toString(), this.generalService.getCurrentTimeInTimezone(this.user.timeZone), todayLesson);\n    if (isWithin30Mins) {\n      this.generalService.goToBellbirdLink(todayLesson);\n      return;\n    }\n    this.onLessonCardClicked.emit(todayLesson);\n  }\n  static #_ = this.ɵfac = function NextLessonItemComponent_Factory(t) {\n    return new (t || NextLessonItemComponent)(i0.ɵɵdirectiveInject(i1.GeneralService), i0.ɵɵdirectiveInject(i2.ClassroomService), i0.ɵɵdirectiveInject(i3.LessonService), i0.ɵɵdirectiveInject(i4.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NextLessonItemComponent,\n    selectors: [[\"app-next-lesson-item\"]],\n    inputs: {\n      todayLesson: \"todayLesson\"\n    },\n    outputs: {\n      onLessonCardClicked: \"onLessonCardClicked\"\n    },\n    decls: 15,\n    vars: 14,\n    consts: [[1, \"flex\", \"w-full\", \"justify-content-between\", \"align-items-center\", \"p-2\"], [1, \"font-sm\", \"user-color\", \"font-bold\", \"flex\", \"justify-content-center\", \"align-items-center\", \"gap-2\"], [\"src\", \"/assets/icons/wall-clock.svg\", \"height\", \"16\"], [\"tooltipPosition\", \"top\", 1, \"sm:w-5rem\", \"file-path-text\", 3, \"pTooltip\"], [1, \"font-sm\"], [\"pButton\", \"\", 1, \"p-button-xs\", \"p-button\", \"p-button-rounded\", \"w-full\", \"gray-border-button\", \"line-height-1\", 3, \"click\", \"ngClass\"]],\n    template: function NextLessonItemComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"span\", 1);\n        i0.ɵɵelement(2, \"img\", 2);\n        i0.ɵɵelementStart(3, \"span\", 3);\n        i0.ɵɵtext(4);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(5, \"span\", 4);\n        i0.ɵɵtext(6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"span\", 4);\n        i0.ɵɵtext(8);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"span\", 4);\n        i0.ɵɵtext(10);\n        i0.ɵɵpipe(11, \"date\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"span\")(13, \"button\", 5);\n        i0.ɵɵlistener(\"click\", function NextLessonItemComponent_Template_button_click_13_listener() {\n          return ctx.lessonCardClicked(ctx.todayLesson);\n        });\n        i0.ɵɵtext(14);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"pTooltip\", ctx.lessonService.getLessonRowTooltipName(ctx.todayLesson.classroom));\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate(ctx.classroomService.getLessonRowName(ctx.todayLesson.classroom));\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.todayLesson.classroom == null ? null : ctx.todayLesson.classroom.activeLevel);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.generalService.convertHoursToMinutesWithSuffix(ctx.todayLesson.duration));\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind2(11, 8, ctx.todayLesson.startingDate, \"dd/MM\"), \" at \", ctx.generalService.getLessonTime(ctx.todayLesson.startingDate), \"\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(11, _c0, ctx.generalService.isDifferenceWithin30Minutes(ctx.todayLesson.startingDate.toString(), ctx.generalService.getCurrentTimeInTimezone(ctx.user.timeZone), ctx.todayLesson), !ctx.generalService.isDifferenceWithin30Minutes(ctx.todayLesson.startingDate.toString(), ctx.generalService.getCurrentTimeInTimezone(ctx.user.timeZone), ctx.todayLesson)));\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate1(\" \", ctx.generalService.isDifferenceWithin30Minutes(ctx.todayLesson.startingDate.toString(), ctx.generalService.getCurrentTimeInTimezone(ctx.user.timeZone), ctx.todayLesson) ? \"JOIN LESSON\" : \"VIEW LESSON\", \"\");\n      }\n    },\n    dependencies: [i5.NgClass, i5.DatePipe],\n    styles: [\".next-lesson[_ngcontent-%COMP%] {\\n  border-radius: 10px;\\n  color: var(--white);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 10px 0;\\n  height: 100%;\\n}\\n.next-lesson[_ngcontent-%COMP%]   .join-lesson-button[_ngcontent-%COMP%] {\\n  border-radius: 30px;\\n  color: var(--main-color);\\n  background-color: var(--white);\\n  text-align: center;\\n  padding: 5px 30px;\\n  margin-left: 15px;\\n  cursor: pointer;\\n  transition: all 0.3 ease;\\n}\\n.next-lesson[_ngcontent-%COMP%]   .join-lesson-button[_ngcontent-%COMP%]:hover {\\n  transition: all 0.3 ease;\\n  background-color: var(--light-purple);\\n  color: white;\\n}\\n\\n.user-color[_ngcontent-%COMP%] {\\n  color: #2D2A4B;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9jbGFzc3Jvb20vbmV4dC1sZXNzb24vbmV4dC1sZXNzb24taXRlbS9uZXh0LWxlc3Nvbi1pdGVtLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0ksbUJBQUE7RUFFQSxtQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsZUFBQTtFQUNBLFlBQUE7QUFBSjtBQUNJO0VBQ0ksbUJBQUE7RUFDQSx3QkFBQTtFQUNBLDhCQUFBO0VBQ0Esa0JBQUE7RUFDQSxpQkFBQTtFQUNBLGlCQUFBO0VBQ0EsZUFBQTtFQUNBLHdCQUFBO0FBQ1I7QUFDSTtFQUNJLHdCQUFBO0VBQ0EscUNBQUE7RUFDQSxZQUFBO0FBQ1I7O0FBR0E7RUFDSSxjQUFBO0FBQUoiLCJzb3VyY2VzQ29udGVudCI6WyIubmV4dC1sZXNzb24ge1xyXG4gICAgYm9yZGVyLXJhZGl1czogMTBweDtcclxuICAgIC8vIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgjYTRhMmU2IDAlLCAjNDg5NWVmIDEwMCUpO1xyXG4gICAgY29sb3I6IHZhcigtLXdoaXRlKTtcclxuICAgIGRpc3BsYXk6IGZsZXg7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XHJcbiAgICBwYWRkaW5nOiAxMHB4IDA7XHJcbiAgICBoZWlnaHQ6IDEwMCU7XHJcbiAgICAuam9pbi1sZXNzb24tYnV0dG9uIHtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiAzMHB4O1xyXG4gICAgICAgIGNvbG9yOiB2YXIoLS1tYWluLWNvbG9yKTtcclxuICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS13aGl0ZSk7XHJcbiAgICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gICAgICAgIHBhZGRpbmc6IDVweCAzMHB4O1xyXG4gICAgICAgIG1hcmdpbi1sZWZ0OiAxNXB4O1xyXG4gICAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zIGVhc2U7XHJcbiAgICB9XHJcbiAgICAuam9pbi1sZXNzb24tYnV0dG9uOmhvdmVyIHtcclxuICAgICAgICB0cmFuc2l0aW9uOiBhbGwgMC4zIGVhc2U7XHJcbiAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tbGlnaHQtcHVycGxlKTtcclxuICAgICAgICBjb2xvcjogd2hpdGU7XHJcbiAgICB9XHJcbn1cclxuXHJcbi51c2VyLWNvbG9yIHtcclxuICAgIGNvbG9yOiAjMkQyQTRCO1xyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "NextLessonItemComponent", "constructor", "generalService", "classroomService", "lessonService", "authService", "onLessonCardClicked", "user", "ngOnInit", "getLoggedInUser", "lessonCardClicked", "<PERSON><PERSON><PERSON><PERSON>", "isWithin30Mins", "isDifferenceWithin30Minutes", "startingDate", "toString", "getCurrentTimeInTimezone", "timeZone", "goToBellbirdLink", "emit", "_", "i0", "ɵɵdirectiveInject", "i1", "GeneralService", "i2", "ClassroomService", "i3", "LessonService", "i4", "AuthService", "_2", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "NextLessonItemComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "NextLessonItemComponent_Template_button_click_13_listener", "ɵɵadvance", "ɵɵproperty", "getLessonRowTooltipName", "classroom", "ɵɵtextInterpolate", "getLessonRowName", "activeLevel", "convertHoursToMinutesWithSuffix", "duration", "ɵɵtextInterpolate2", "ɵɵpipeBind2", "getLessonTime", "ɵɵpureFunction2", "_c0", "ɵɵtextInterpolate1"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\next-lesson\\next-lesson-item\\next-lesson-item.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\next-lesson\\next-lesson-item\\next-lesson-item.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, Output } from \"@angular/core\";\r\nimport { Lesson } from \"src/app/core/models/lesson.model\";\r\nimport { User } from \"src/app/core/models/user.model\";\r\nimport { AuthService } from \"src/app/core/services/auth.service\";\r\nimport { ClassroomService } from \"src/app/core/services/classroom.service\";\r\nimport { GeneralService } from \"src/app/core/services/general.service\";\r\nimport { LessonService } from \"src/app/core/services/lesson.service\";\r\n\r\n@Component({\r\n  selector: \"app-next-lesson-item\",\r\n  templateUrl: \"./next-lesson-item.component.html\",\r\n  styleUrls: [\"./next-lesson-item.component.scss\"],\r\n})\r\nexport class NextLessonItemComponent {\r\n  @Input() todayLesson!: Lesson;\r\n  @Output() onLessonCardClicked = new EventEmitter<Lesson>();\r\n  public user: User = {} as User;\r\n  constructor(\r\n    public generalService: GeneralService,\r\n    public classroomService: ClassroomService,\r\n    public lessonService: LessonService,\r\n    public authService: AuthService\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.user = this.authService.getLoggedInUser();\r\n  }\r\n\r\n  lessonCardClicked(todayLesson: Lesson) {\r\n    const isWithin30Mins = this.generalService.isDifferenceWithin30Minutes(todayLesson.startingDate.toString(), \r\n    this.generalService.getCurrentTimeInTimezone(this.user.timeZone!), todayLesson);\r\n\r\n    if (isWithin30Mins) {\r\n      this.generalService.goToBellbirdLink(todayLesson)\r\n      return;\r\n    }\r\n    this.onLessonCardClicked.emit(todayLesson);\r\n  }\r\n}\r\n", "<div class=\"flex w-full justify-content-between align-items-center p-2\">\r\n    <span class=\"font-sm user-color font-bold flex justify-content-center align-items-center gap-2\">\r\n        <img src=\"/assets/icons/wall-clock.svg\" height=\"16\" />\r\n        <span class=\"sm:w-5rem file-path-text\" tooltipPosition=\"top\"\r\n            [pTooltip]=\"lessonService.getLessonRowTooltipName(todayLesson.classroom!)\">{{classroomService.getLessonRowName(todayLesson.classroom!)}}</span></span>\r\n    <span class=\"font-sm\">{{todayLesson.classroom?.activeLevel}}</span>\r\n    <span class=\"font-sm\">{{generalService.convertHoursToMinutesWithSuffix(todayLesson.duration)}}</span>\r\n    <span class=\"font-sm\">{{todayLesson.startingDate | date:'dd/MM'}} at {{\r\n        generalService.getLessonTime(todayLesson.startingDate) }}</span>\r\n    <span><button\r\n            (click)=\"lessonCardClicked(todayLesson)\"\r\n            pButton class=\" p-button-xs p-button p-button-rounded w-full gray-border-button line-height-1\"\r\n            [ngClass]=\"{'text-white': generalService.isDifferenceWithin30Minutes(todayLesson.startingDate.toString(), \r\n            this.generalService.getCurrentTimeInTimezone(this.user.timeZone!), todayLesson), \r\n            'p-button-outlined': !generalService.isDifferenceWithin30Minutes(todayLesson.startingDate.toString(), \r\n            this.generalService.getCurrentTimeInTimezone(this.user.timeZone!), todayLesson)}\">\r\n\r\n\r\n            {{generalService.isDifferenceWithin30Minutes(todayLesson.startingDate.toString(), \r\n                this.generalService.getCurrentTimeInTimezone(this.user.timeZone!), todayLesson) ? 'JOIN LESSON' : 'VIEW LESSON'}}</button>\r\n    </span>\r\n</div>"], "mappings": "AAAA,SAAoBA,YAAY,QAAuB,eAAe;;;;;;;;;;;AAatE,OAAM,MAAOC,uBAAuB;EAIlCC,YACSC,cAA8B,EAC9BC,gBAAkC,EAClCC,aAA4B,EAC5BC,WAAwB;IAHxB,KAAAH,cAAc,GAAdA,cAAc;IACd,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IANV,KAAAC,mBAAmB,GAAG,IAAIP,YAAY,EAAU;IACnD,KAAAQ,IAAI,GAAS,EAAU;EAM3B;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACD,IAAI,GAAG,IAAI,CAACF,WAAW,CAACI,eAAe,EAAE;EAChD;EAEAC,iBAAiBA,CAACC,WAAmB;IACnC,MAAMC,cAAc,GAAG,IAAI,CAACV,cAAc,CAACW,2BAA2B,CAACF,WAAW,CAACG,YAAY,CAACC,QAAQ,EAAE,EAC1G,IAAI,CAACb,cAAc,CAACc,wBAAwB,CAAC,IAAI,CAACT,IAAI,CAACU,QAAS,CAAC,EAAEN,WAAW,CAAC;IAE/E,IAAIC,cAAc,EAAE;MAClB,IAAI,CAACV,cAAc,CAACgB,gBAAgB,CAACP,WAAW,CAAC;MACjD;IACF;IACA,IAAI,CAACL,mBAAmB,CAACa,IAAI,CAACR,WAAW,CAAC;EAC5C;EAAC,QAAAS,CAAA,G;qBAxBUpB,uBAAuB,EAAAqB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,aAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAvB/B,uBAAuB;IAAAgC,SAAA;IAAAC,MAAA;MAAAtB,WAAA;IAAA;IAAAuB,OAAA;MAAA5B,mBAAA;IAAA;IAAA6B,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCZhCnB,EADJ,CAAAqB,cAAA,aAAwE,cAC4B;QAC5FrB,EAAA,CAAAsB,SAAA,aAAsD;QACtDtB,EAAA,CAAAqB,cAAA,cAC+E;QAAArB,EAAA,CAAAuB,MAAA,GAA6D;QAAOvB,EAAP,CAAAwB,YAAA,EAAO,EAAO;QAC9JxB,EAAA,CAAAqB,cAAA,cAAsB;QAAArB,EAAA,CAAAuB,MAAA,GAAsC;QAAAvB,EAAA,CAAAwB,YAAA,EAAO;QACnExB,EAAA,CAAAqB,cAAA,cAAsB;QAAArB,EAAA,CAAAuB,MAAA,GAAwE;QAAAvB,EAAA,CAAAwB,YAAA,EAAO;QACrGxB,EAAA,CAAAqB,cAAA,cAAsB;QAAArB,EAAA,CAAAuB,MAAA,IACuC;;QAAAvB,EAAA,CAAAwB,YAAA,EAAO;QAC9DxB,EAAN,CAAAqB,cAAA,YAAM,iBAMoF;QALlFrB,EAAA,CAAAyB,UAAA,mBAAAC,0DAAA;UAAA,OAASN,GAAA,CAAA/B,iBAAA,CAAA+B,GAAA,CAAA9B,WAAA,CAA8B;QAAA,EAAC;QAQxCU,EAAA,CAAAuB,MAAA,IACqH;QAEjIvB,EAFiI,CAAAwB,YAAA,EAAS,EAC/H,EACL;;;QAjBMxB,EAAA,CAAA2B,SAAA,GAA0E;QAA1E3B,EAAA,CAAA4B,UAAA,aAAAR,GAAA,CAAArC,aAAA,CAAA8C,uBAAA,CAAAT,GAAA,CAAA9B,WAAA,CAAAwC,SAAA,EAA0E;QAAC9B,EAAA,CAAA2B,SAAA,EAA6D;QAA7D3B,EAAA,CAAA+B,iBAAA,CAAAX,GAAA,CAAAtC,gBAAA,CAAAkD,gBAAA,CAAAZ,GAAA,CAAA9B,WAAA,CAAAwC,SAAA,EAA6D;QAC1H9B,EAAA,CAAA2B,SAAA,GAAsC;QAAtC3B,EAAA,CAAA+B,iBAAA,CAAAX,GAAA,CAAA9B,WAAA,CAAAwC,SAAA,kBAAAV,GAAA,CAAA9B,WAAA,CAAAwC,SAAA,CAAAG,WAAA,CAAsC;QACtCjC,EAAA,CAAA2B,SAAA,GAAwE;QAAxE3B,EAAA,CAAA+B,iBAAA,CAAAX,GAAA,CAAAvC,cAAA,CAAAqD,+BAAA,CAAAd,GAAA,CAAA9B,WAAA,CAAA6C,QAAA,EAAwE;QACxEnC,EAAA,CAAA2B,SAAA,GACuC;QADvC3B,EAAA,CAAAoC,kBAAA,KAAApC,EAAA,CAAAqC,WAAA,QAAAjB,GAAA,CAAA9B,WAAA,CAAAG,YAAA,oBAAA2B,GAAA,CAAAvC,cAAA,CAAAyD,aAAA,CAAAlB,GAAA,CAAA9B,WAAA,CAAAG,YAAA,MACuC;QAIrDO,EAAA,CAAA2B,SAAA,GAGiF;QAHjF3B,EAAA,CAAA4B,UAAA,YAAA5B,EAAA,CAAAuC,eAAA,KAAAC,GAAA,EAAApB,GAAA,CAAAvC,cAAA,CAAAW,2BAAA,CAAA4B,GAAA,CAAA9B,WAAA,CAAAG,YAAA,CAAAC,QAAA,IAAA0B,GAAA,CAAAvC,cAAA,CAAAc,wBAAA,CAAAyB,GAAA,CAAAlC,IAAA,CAAAU,QAAA,GAAAwB,GAAA,CAAA9B,WAAA,IAAA8B,GAAA,CAAAvC,cAAA,CAAAW,2BAAA,CAAA4B,GAAA,CAAA9B,WAAA,CAAAG,YAAA,CAAAC,QAAA,IAAA0B,GAAA,CAAAvC,cAAA,CAAAc,wBAAA,CAAAyB,GAAA,CAAAlC,IAAA,CAAAU,QAAA,GAAAwB,GAAA,CAAA9B,WAAA,GAGiF;QAGjFU,EAAA,CAAA2B,SAAA,EACqH;QADrH3B,EAAA,CAAAyC,kBAAA,MAAArB,GAAA,CAAAvC,cAAA,CAAAW,2BAAA,CAAA4B,GAAA,CAAA9B,WAAA,CAAAG,YAAA,CAAAC,QAAA,IAAA0B,GAAA,CAAAvC,cAAA,CAAAc,wBAAA,CAAAyB,GAAA,CAAAlC,IAAA,CAAAU,QAAA,GAAAwB,GAAA,CAAA9B,WAAA,sCACqH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}