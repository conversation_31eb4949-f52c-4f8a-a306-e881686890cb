{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { inject, signal } from '@angular/core';\nimport { serverTimestamp, Timestamp } from 'firebase/firestore';\nimport * as moment from 'moment';\nimport { Observable, combineLatest, firstValueFrom } from 'rxjs';\nimport { map, switchMap, tap } from 'rxjs/operators';\nimport { UserService } from './user.service';\nimport { GeneralService } from './general.service';\nimport { environment } from 'src/environments/environment';\nimport { getAuth, signInAnonymously } from \"firebase/auth\";\nimport { AuthService } from './auth.service';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/fire/compat/firestore\";\nimport * as i2 from \"@angular/fire/compat/auth\";\nimport * as i3 from \"@angular/common/http\";\nconst BACKEND_URL = environment.apiUrl;\nconst BACKEND_LMS_URL = environment.apiUrl + \"/LMS/\";\nexport class ChatService {\n  constructor(db, afAuth, http) {\n    this.db = db;\n    this.afAuth = afAuth;\n    this.http = http;\n    this.userService = inject(UserService);\n    this.generalService = inject(GeneralService);\n    this.authService = inject(AuthService);\n    this.isChatEnabled = signal(environment.isChatEnabled);\n    this.showUploadFile = signal(false);\n    this.unreadCount = signal(0);\n    this.currentRoom = signal({});\n    this.showBubbleChat = signal(false);\n    this.dbPath = '/chats';\n    this.dbUnreadPath = '/chats';\n    this.usersRef = this.db.collection('users');\n    this.readCount = 0;\n    this.secondsToCheckOnlineStatus = 10;\n    this.user = this.authService.getLoggedInUser();\n    this.chatsRef = db.collection(this.dbPath);\n    this.usersRef = db.collection('/users');\n  }\n  deleteChatFilesFromLibraryFolder(fileUrl) {\n    console.log(fileUrl);\n    return this.http.post(BACKEND_LMS_URL + \"DeleteChatFilesFromLibraryFolder?fileUrl=\" + fileUrl, {});\n  }\n  // Sign in anonymously\n  signInAnonymously() {\n    return new Observable(observer => {\n      this.afAuth.signInAnonymously().then(userCredential => {\n        console.log('Signed in anonymously:', userCredential.user);\n        observer.next(userCredential);\n        observer.complete();\n      }).catch(error => {\n        console.error('Error signing in anonymously:', error);\n        observer.error(error);\n      });\n    });\n  }\n  signInAnonym() {\n    const auth = getAuth();\n    signInAnonymously(auth).then(() => {\n      // Signed in..\n    }).catch(error => {\n      const errorCode = error.code;\n      const errorMessage = error.message;\n      // ...\n    });\n  }\n  getLastMessage(classroomId) {\n    const chatRef = this.chatsRef.doc(`${classroomId}/`);\n    return chatRef.snapshotChanges().pipe(map(action => {\n      const data = action.payload.data();\n      const id = action.payload.id;\n      return {\n        id,\n        ...data\n      };\n    }));\n  }\n  // Fetch all chats for a classroom\n  getClassroomChats(classroomId) {\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    return this.chatsRef.doc(classroomId).collection('chats').snapshotChanges().pipe(tap(actions => this.logReadCount(actions.length)),\n    // Log read count\n    map(actions => actions.map(a => {\n      const data = a.payload.doc.data();\n      const id = a.payload.doc.id;\n      return {\n        id,\n        ...data\n      };\n    })));\n  }\n  // Fetch messages for a specific chat room\n  getChatMessagesAndMarkAsRead(chatId, userId, limit, lastMessage) {\n    // Create an observable to fetch messages\n    const messagesQuery = this.db.collection(`${this.dbPath}/${chatId}/messages`, ref => {\n      let query = ref.orderBy('timestamp', 'desc').limit(limit);\n      if (lastMessage) {\n        query = query.startAfter(lastMessage.timestamp);\n      }\n      return query;\n    });\n    // Fetch the messages and mark them as read\n    return messagesQuery.snapshotChanges().pipe(tap(actions => this.logReadCount(actions.length)),\n    // Log read count\n    map(actions => {\n      const messages = actions.map(a => {\n        const data = a.payload.doc.data();\n        const id = a.payload.doc.id;\n        return {\n          id,\n          ...data\n        };\n      });\n      // Mark messages as read\n      if (messages.length > 0) {\n        this.markMessagesAsRead(chatId, userId).catch(error => {\n          console.error('Failed to mark messages as read:', error);\n        });\n      }\n      return messages;\n    }));\n  }\n  // Create a new chat room\n  createChatRoom(classroomId, participants) {\n    const chat = {\n      classroomId: classroomId,\n      createdAt: moment().format('YYYY-MM-DD HH:mm:ss'),\n      lastMessage: '',\n      lastMessageAt: serverTimestamp(),\n      lastMessageFrom: '',\n      lastMessageFromName: '',\n      lastMessageFromImage: '',\n      lastMessageIsFile: false,\n      lastMessageDeleted: false,\n      lastMessageId: '',\n      lastProcessedMessageId: ''\n    };\n    return this.chatsRef.add(chat);\n  }\n  // Add a new message to a chat room\n  addMessage(classroomId, message, user) {\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    const messageRef = chatRef.collection('messages').doc();\n    const messageId = messageRef.ref.id;\n    return firstValueFrom(chatRef.get()).then(snapshot => {\n      const updateData = {\n        createdAt: moment().format('YYYY-MM-DD HH:mm:ss'),\n        lastMessage: message.content,\n        lastMessageAt: serverTimestamp(),\n        classroomId: classroomId,\n        lastMessageFrom: message.senderId,\n        lastMessageFromName: this.generalService.getPersonFullName(user),\n        lastMessageFromImage: this.userService.getUserPhoto(user?.avatarUrl),\n        lastMessageIsFile: message.isFileFromLibrary || message.isUploadFile,\n        lastMessageDeleted: false,\n        lastMessageId: messageId,\n        // Accessing id through ref\n        lastProcessedMessageId: ''\n      };\n      if (!snapshot.exists) {\n        // Retrieve users in the chat, including teacher and students\n        const usersInChat = [this.currentRoom().teacher, ...this.currentRoom().classroomStudents];\n        const updatedUnreadCounts = {};\n        usersInChat.forEach(chatUser => {\n          if (chatUser.aspUserId !== message.senderId) {\n            updatedUnreadCounts[chatUser.aspUserId] = 1; // Initialize unread count to 1 for new chat\n          }\n        });\n        updateData.unreadCounts = updatedUnreadCounts;\n        return chatRef.set(updateData).then(() => {\n          return messageRef.set(message).then(() => {\n            return messageRef.update({\n              id: messageRef.ref.id\n            }).then(() => {\n              return this.updateChatLastMessage(chatRef, classroomId);\n            });\n          });\n        });\n      } else {\n        // Existing chat room, update last message and unread counts\n        return this.updateLastMessage(chatRef, message, user, false, messageId).then(() => {\n          return messageRef.set(message).then(() => {\n            return messageRef.update({\n              id: messageRef.ref.id\n            });\n          });\n        });\n      }\n    });\n  }\n  sendMessageAndMarkRead(classroomId, message, user) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const chatRef = _this.chatsRef.doc(`${classroomId}`);\n      const messageRef = chatRef.collection('messages').doc();\n      const unreadMessagesRef = _this.db.collection(`${_this.dbUnreadPath}/${classroomId}/messages`);\n      const messageId = messageRef.ref.id;\n      const batch = _this.db.firestore.batch();\n      // Prepare message data\n      const messageData = {\n        ...message,\n        id: messageId,\n        createdAt: moment().format('YYYY-MM-DD HH:mm:ss')\n      };\n      // Add new message to the batch\n      batch.set(messageRef.ref, messageData);\n      const chatSnapshot = yield firstValueFrom(chatRef.get());\n      const updateData = {\n        lastMessage: message.content,\n        lastMessageAt: serverTimestamp(),\n        lastMessageFrom: message.senderId,\n        lastMessageFromName: _this.generalService.getPersonFullName(user),\n        lastMessageFromImage: _this.userService.getUserPhoto(user?.avatarUrl),\n        lastMessageIsFile: message.isFileFromLibrary || message.isUploadFile,\n        lastMessageDeleted: false,\n        lastMessageId: messageId\n      };\n      if (!chatSnapshot.exists) {\n        // New chat room: initialize unread counts for all users except the sender\n        const usersInChat = [_this.currentRoom().teacher, ..._this.currentRoom().classroomStudents];\n        const unreadCounts = {};\n        usersInChat.forEach(chatUser => {\n          if (chatUser.aspUserId !== message.senderId) {\n            unreadCounts[chatUser.aspUserId] = 1;\n          }\n        });\n        updateData.unreadCounts = unreadCounts;\n        batch.set(chatRef.ref, updateData);\n      } else {\n        // Update last message and adjust unread counts for existing room\n        const existingData = chatSnapshot.data();\n        const updatedUnreadCounts = {\n          ...existingData?.unreadCounts\n        };\n        Object.keys(updatedUnreadCounts).forEach(userId => {\n          if (userId !== message.senderId) {\n            updatedUnreadCounts[userId] = (updatedUnreadCounts[userId] || 0) + 1;\n          }\n        });\n        updateData.unreadCounts = updatedUnreadCounts;\n        batch.update(chatRef.ref, updateData);\n      }\n      // Update read status for the current user in unread messages\n      const unreadMessages = yield firstValueFrom(unreadMessagesRef.get());\n      unreadMessages.forEach(doc => {\n        const docData = doc.data(); // Retrieve data from each document\n        const readStatus = docData.readStatus || {}; // Access or initialize readStatus\n        if (!readStatus[user.aspUserId]) {\n          readStatus[user.aspUserId] = true; // Mark as read for the current user\n          batch.update(doc.ref, {\n            readStatus\n          }); // Add update to batch\n        }\n      });\n      // Commit the entire batch\n      yield batch.commit();\n    })();\n  }\n  updateLastMessage(chatRef, updatedMessage, user, isLastMessageDeleted = false, messageId = '') {\n    const messageRef = chatRef.collection('messages').doc();\n    const updateData = {\n      lastMessage: updatedMessage.content,\n      lastMessageAt: serverTimestamp(),\n      lastMessageFrom: updatedMessage.senderId,\n      lastMessageFromImage: this.userService.getUserPhoto(user?.avatarUrl),\n      lastMessageFromName: this.generalService.getPersonFullName(user),\n      lastMessageIsFile: updatedMessage.isFileFromLibrary || updatedMessage.isUploadFile,\n      lastMessageDeleted: isLastMessageDeleted,\n      lastMessageId: messageId\n    };\n    return firstValueFrom(chatRef.get()).then(snapshot => {\n      console.log('current room', this.currentRoom());\n      const updatedUnreadCounts = {};\n      // Retrieve users in the chat, including teacher and students\n      const usersInChat = [this.currentRoom().teacher, ...this.currentRoom().classroomStudents];\n      usersInChat.forEach(chatUser => {\n        if (chatUser.aspUserId !== updatedMessage.senderId) {\n          updatedUnreadCounts[chatUser.aspUserId] = (snapshot.exists ? snapshot.data().unreadCounts?.[chatUser.aspUserId] || 0 : 0) + 1;\n        }\n      });\n      updateData.unreadCounts = updatedUnreadCounts;\n      return chatRef.update(updateData);\n    });\n  }\n  editMessage(classroomId, messageId, updatedMessage, user, updateLastMessage = false) {\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    const messageRef = chatRef.collection('messages').doc(`${messageId}`);\n    const messagesRef = chatRef.collection('messages', ref => ref.orderBy('timestamp', 'desc').limit(1));\n    const lastMessageDoc = messagesRef.ref.doc();\n    const lastMessageId = lastMessageDoc.id;\n    console.log(lastMessageId);\n    return messageRef.get().toPromise().then(snapshot => {\n      if (snapshot.exists) {\n        return messageRef.update({\n          ...updatedMessage\n        }).then(() => {\n          if (updateLastMessage) {\n            return this.updateLastMessage(chatRef, updatedMessage, user, false, updatedMessage.id);\n          }\n          return Promise.resolve();\n        });\n      } else {\n        throw new Error('Message does not exist');\n      }\n    });\n  }\n  getLastUnreadMessagesCountForUser(classroomId, userId) {\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    return chatRef.valueChanges().pipe(tap(actions => this.logReadCount(actions?.unreadCounts?.[userId] || 0)),\n    // Log read count\n    map(data => {\n      const unreadCounts = data?.unreadCounts;\n      return unreadCounts ? unreadCounts[userId] || 0 : 0;\n    }));\n  }\n  markLastMessagesAsRead(classroomId, userId) {\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    return this.markLastMessagesAsReadInLastMessage(chatRef, userId);\n  }\n  markLastMessagesAsReadInLastMessage(chatUnreadCountsRef, userId) {\n    return chatUnreadCountsRef.get().toPromise().then(snapshot => {\n      if (snapshot.exists) {\n        const data = snapshot.data();\n        const currentCount = data.unreadCounts?.[userId] || 0;\n        // Only proceed with the update if the unread count for the user is greater than zero\n        if (currentCount > 0) {\n          const updatedUnreadCounts = {\n            ...data.unreadCounts,\n            [userId]: 0\n          };\n          return chatUnreadCountsRef.update({\n            unreadCounts: updatedUnreadCounts\n          });\n        }\n      }\n      // Resolve immediately if the document doesn't exist or no update is needed\n      return Promise.resolve();\n    }).catch(error => {\n      console.error('Failed to mark messages as read:', error);\n      return Promise.reject(error);\n    });\n  }\n  // Delete a specific message from a chat room\n  deleteMessage(classroomId, message, user, updateLastMessage = false) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      console.log(classroomId);\n      console.log(message);\n      const chatRef = _this2.chatsRef.doc(`${classroomId}`);\n      const messageRef = chatRef.collection('messages').doc(message.id);\n      // Start a batch to combine operations\n      const batch = _this2.db.firestore.batch();\n      // Queue the deletion of the message\n      batch.delete(messageRef.ref);\n      if (updateLastMessage) {\n        const latestMessage = yield _this2.getLatestMessage(classroomId);\n        const updateData = {\n          ...message,\n          content: 'Message Deleted'\n        };\n        yield _this2.updateLastMessageInBatch(chatRef, updateData, user, true, latestMessage?.id, batch);\n      }\n      // Commit the batch\n      yield batch.commit();\n      // Update the chat's last message if not updating the last message\n      if (!updateLastMessage) {\n        yield _this2.updateChatLastMessage(chatRef, classroomId);\n      }\n    })();\n  }\n  updateLastMessageInBatch(chatRef, updatedMessage, user, isLastMessageDeleted, messageId, batch) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      const updateData = {\n        lastMessage: updatedMessage.content,\n        lastMessageAt: serverTimestamp(),\n        lastMessageFrom: updatedMessage.senderId,\n        lastMessageFromImage: _this3.userService.getUserPhoto(user?.avatarUrl),\n        lastMessageFromName: _this3.generalService.getPersonFullName(user),\n        lastMessageIsFile: updatedMessage.isFileFromLibrary || updatedMessage.isUploadFile,\n        lastMessageDeleted: isLastMessageDeleted,\n        lastMessageId: messageId\n      };\n      // Get snapshot of the chat document\n      const snapshot = yield firstValueFrom(chatRef.get());\n      // Prepare updated unread counts\n      const updatedUnreadCounts = {};\n      const usersInChat = [_this3.currentRoom().teacher, ..._this3.currentRoom().classroomStudents];\n      const currentUnreadCounts = snapshot.exists ? snapshot.data()?.unreadCounts || {} : {};\n      usersInChat.forEach(chatUser => {\n        if (chatUser.aspUserId !== updatedMessage.senderId) {\n          updatedUnreadCounts[chatUser.aspUserId] = (currentUnreadCounts[chatUser.aspUserId] || 0) + 1; // Increment unread count\n        }\n      });\n      // Only update if there are changes to the unread counts\n      if (Object.keys(updatedUnreadCounts).length > 0) {\n        updateData.unreadCounts = updatedUnreadCounts;\n      }\n      // Perform a single update to the chat document in the batch\n      batch.update(chatRef.ref, updateData);\n    })();\n  }\n  updateChatLastMessage(chatRef, classroomId) {\n    return this.getLatestMessage(classroomId).then(snapshot => {\n      console.log(snapshot);\n      if (snapshot) {\n        const latestMessage = snapshot;\n        return chatRef.update({\n          lastMessage: latestMessage.content,\n          lastMessageAt: latestMessage.createdAt,\n          lastMessageFrom: latestMessage.senderId,\n          lastMessageFromName: this.generalService.getPersonFullName(this.user),\n          lastMessageFromImage: latestMessage.senderImage,\n          lastMessageIsFile: latestMessage.isFileFromLibrary || latestMessage.isUploadFile,\n          lastMessageDeleted: false,\n          lastMessageId: latestMessage.id\n        });\n      } else {\n        // Handle case where there are no messages left in the collection\n        return chatRef.update({\n          lastMessage: '',\n          lastMessageAt: null,\n          lastMessageFrom: '',\n          lastMessageFromName: '',\n          lastMessageFromImage: '',\n          lastMessageIsFile: false,\n          lastMessageDeleted: false,\n          lastMessageId: ''\n        });\n      }\n    });\n  }\n  getLatestMessage(classroomId) {\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\n    return firstValueFrom(chatRef.collection('messages', ref => ref.orderBy('timestamp', 'desc').limit(1)).get()).then(snapshot => {\n      if (!snapshot.empty) {\n        const latestMessage = snapshot.docs[0].data();\n        const messageCount = snapshot.size; // Get the count of messages retrieved\n        this.logReadCount(messageCount);\n        return latestMessage;\n      } else {\n        return null; // No messages found\n      }\n    });\n  }\n  // Update a chat room document\n  updateChatRoom(classroomId, data) {\n    return this.chatsRef.doc(classroomId).update(data);\n  }\n  // Delete a chat room document\n  deleteChatRoom(classroomId) {\n    return this.chatsRef.doc(classroomId).delete();\n  }\n  addUnreadMessage(classroomId, message) {\n    const messageRef = this.db.collection(`${this.dbUnreadPath}/${classroomId}/messages`).doc(message.id);\n    // Set the initial readStatus for the new message\n    const initialReadStatus = {\n      // Initialize the readStatus for all users, defaulting to false (unread)\n      [message.senderId]: false // Mark the sender as false (unread)\n      // You can add more user IDs here if needed, depending on your application logic\n    };\n    return messageRef.set({\n      readStatus: initialReadStatus,\n      // Add the readStatus field\n      timestamp: serverTimestamp() // Ensure there's a timestamp for ordering\n    });\n  }\n  markMessagesAsRead(classroomId, userId) {\n    return this.db.collection(`${this.dbUnreadPath}/${classroomId}/messages`).get().toPromise().then(querySnapshot => {\n      const batch = this.db.firestore.batch();\n      let hasUpdates = false; // Flag to track if there are any updates\n      querySnapshot.forEach(doc => {\n        const data = doc.data();\n        // Ensure readStatus is a valid map or initialize it\n        const readStatusUpdate = data.readStatus || {};\n        // Only update if the userId is not already marked as read\n        if (!readStatusUpdate[userId]) {\n          readStatusUpdate[userId] = true;\n          // Update the document with the new or modified readStatus map\n          batch.update(doc.ref, {\n            readStatus: readStatusUpdate\n          });\n          hasUpdates = true; // Set the flag to true if an update is made\n        }\n      });\n      // Only commit the batch if there are updates\n      if (hasUpdates) {\n        return batch.commit();\n      }\n      // If no updates were made, resolve immediately\n      return Promise.resolve();\n    });\n  }\n  // Mark messages as read\n  markAllMessagesAsRead(classroomId) {\n    return this.db.collection(`${this.dbUnreadPath}/${classroomId}/messages`).get().toPromise().then(querySnapshot => {\n      const batch = this.db.firestore.batch();\n      querySnapshot.forEach(doc => {\n        const messageData = doc.data();\n        const updatedReadStatus = {};\n        // Set readStatus for all users in the message's readStatus map to true\n        for (const userId in messageData.readStatus) {\n          if (messageData.readStatus.hasOwnProperty(userId)) {\n            const a = `readStatus.${userId}`;\n            updatedReadStatus[a] = true;\n          }\n        }\n        batch.update(doc.ref, updatedReadStatus);\n      });\n      return batch.commit();\n    });\n  }\n  // Get unread messages count for a user in a classroom\n  getUnreadMessagesCount(classroomId, userId) {\n    return this.db.collection(`${this.dbUnreadPath}/${classroomId}/messages`).snapshotChanges().pipe(tap(actions => this.logReadCount(actions.length)),\n    // Log read count\n    map(actions => actions.filter(action => {\n      const data = action.payload.doc.data();\n      return !data.readStatus.hasOwnProperty(userId);\n    }).length));\n  }\n  // Get total unread messages count for a user across all classrooms\n  getUnreadMessagesCountForUser(userId) {\n    return this.chatsRef.snapshotChanges().pipe(tap(actions => this.logReadCount(actions.length)),\n    // Log read count\n    switchMap(chatSnapshots => {\n      const chatObservables = chatSnapshots.map(snapshot => {\n        const chatId = snapshot.payload.doc.id;\n        return this.getUnreadMessagesCount(chatId, userId);\n      });\n      return combineLatest(chatObservables);\n    }), map(unreadCounts => unreadCounts.reduce((acc, count) => acc + count, 0)));\n  }\n  getTotalUnreadMessagesCountForUser(userId) {\n    return this.chatsRef.snapshotChanges().pipe(tap(actions => this.logReadCount(actions.length)),\n    // Log read count\n    switchMap(chatSnapshots => {\n      const chatIds = chatSnapshots.map(snapshot => snapshot.payload.doc.id);\n      // Create an array of observables for each classroom's unread messages\n      const unreadMessageObservables = chatIds.map(classroomId => this.chatsRef.doc(classroomId).collection('messages').snapshotChanges().pipe(map(actions => actions.filter(action => {\n        const data = action.payload.doc.data();\n        return !data.readStatus.hasOwnProperty(userId);\n      }).length)));\n      // Combine all observables into a single observable\n      return combineLatest(unreadMessageObservables);\n    }), map(unreadCounts => unreadCounts.reduce((total, count) => total + count, 0)));\n  }\n  // Or, to get the user object directly\n  getFireBaseUser() {\n    return this.afAuth.authState;\n  }\n  setUserStatus(userId, status) {\n    const userRef = this.usersRef.doc(userId);\n    return userRef.set({\n      status,\n      lastOnlineTime: Timestamp.now()\n    }, {\n      merge: true\n    });\n  }\n  setUserLastOnlineTime(userId, time) {\n    const userRef = this.usersRef.doc(userId);\n    return userRef.set({\n      status: 'online',\n      lastOnlineTime: time\n    }, {\n      merge: true\n    });\n  }\n  getUserStatus(userId) {\n    return this.usersRef.doc(userId).valueChanges().pipe(map(user => user?.status || 'offline'));\n  }\n  /**\n   * Retrieves all users in the Firestore 'users' collection, including their status compared to the last online time.\n   * The status is determined by comparing the current time with the user's last online time. If the user was online\n   * within the last `secondsToCheckOnlineStatus` seconds, the status is set to 'online', otherwise it is set to 'offline'.\n   * If the user does not have a last online time, the status is set to 'offline'.\n   * @returns An Observable of an array of ChatUserStatus objects, where each object contains the user's id, status, and last online time.\n   */\n  getOnlineUsersComparedToLastOnlineTime() {\n    return this.db.collection('/users').snapshotChanges().pipe(tap(actions => this.logReadCount(actions.length)),\n    // Log read count\n    map(actions => {\n      const currentTime = Timestamp.now();\n      return actions.map(a => {\n        const data = a.payload.doc.data();\n        const id = a.payload.doc.id;\n        const lastOnlineTime = data.lastOnlineTime;\n        if (lastOnlineTime) {\n          if (!currentTime.seconds || !lastOnlineTime.seconds) {\n            return {\n              ...data,\n              id,\n              status: 'offline'\n            };\n          }\n          const timeDifference = currentTime?.seconds - lastOnlineTime?.seconds;\n          // Check if the user was online within the last 10 seconds\n          if (timeDifference <= this.secondsToCheckOnlineStatus) {\n            return {\n              ...data,\n              id,\n              status: 'online'\n            };\n          } else {\n            return {\n              ...data,\n              id,\n              status: 'offline'\n            };\n          }\n        }\n        return {\n          ...data,\n          id,\n          status: 'offline'\n        };\n      });\n    }));\n  }\n  // Fetch all chats for a classroom along with the last unread messages count\n  getClassroomChatsWithUnreadCount(classroomId) {\n    const chatRef = this.chatsRef.doc(classroomId).collection('chats');\n    const unreadCount$ = this.getUnreadMessagesCount(classroomId, this.user?.uid); // Assuming 'this.user' is defined\n    return combineLatest([chatRef.snapshotChanges().pipe(map(actions => actions.map(a => {\n      const data = a.payload.doc.data();\n      const id = a.payload.doc.id;\n      return {\n        id,\n        ...data\n      };\n    }))), unreadCount$]).pipe(map(([chats, unreadCount]) => ({\n      chats,\n      unreadCount\n    })));\n  }\n  // Use this method for fetching messages and marking them as read\n  getChatMessagesAndUnreadCounts(chatId, userId, limit) {\n    const messagesQuery = this.db.collection(`${this.dbPath}/${chatId}/messages`, ref => ref.orderBy('timestamp', 'desc').limit(limit));\n    const unreadCount$ = this.getUnreadMessagesCount(chatId, userId);\n    return combineLatest([messagesQuery.snapshotChanges().pipe(map(actions => actions.map(a => {\n      const data = a.payload.doc.data();\n      const id = a.payload.doc.id;\n      return {\n        id,\n        ...data\n      };\n    }))), unreadCount$]).pipe(map(([messages, unreadCount]) => ({\n      messages,\n      unreadCount\n    })));\n  }\n  // Method to count messages sent by a user today per chatroom\n  countMessagesSentTodayByUser(userId, chatroomId) {\n    const chatRef = this.chatsRef.doc(`${chatroomId}`).collection('messages');\n    // Get the start and end timestamps for today\n    const startOfDay = new Date();\n    startOfDay.setHours(0, 0, 0, 0); // Start of today\n    const endOfDay = new Date();\n    endOfDay.setHours(23, 59, 59, 999); // End of today\n    // Create an observable from the Firestore query\n    return new Observable(observer => {\n      chatRef.ref.where('senderId', '==', userId).where('timestamp', '>=', startOfDay).where('timestamp', '<=', endOfDay).get().then(snapshot => {\n        observer.next(snapshot.size); // Emit the count\n        observer.complete(); // Complete the observable\n      }).catch(error => {\n        observer.error(error); // Emit an error if something goes wrong\n      });\n    });\n  }\n  logReadCount(count) {\n    const currentCount = parseInt(localStorage.getItem('readCount') || '0', 10);\n    localStorage.setItem('readCount', (currentCount + count).toString());\n  }\n  static #_ = this.ɵfac = function ChatService_Factory(t) {\n    return new (t || ChatService)(i0.ɵɵinject(i1.AngularFirestore), i0.ɵɵinject(i2.AngularFireAuth), i0.ɵɵinject(i3.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ChatService,\n    factory: ChatService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["inject", "signal", "serverTimestamp", "Timestamp", "moment", "Observable", "combineLatest", "firstValueFrom", "map", "switchMap", "tap", "UserService", "GeneralService", "environment", "getAuth", "signInAnonymously", "AuthService", "BACKEND_URL", "apiUrl", "BACKEND_LMS_URL", "ChatService", "constructor", "db", "afAuth", "http", "userService", "generalService", "authService", "isChatEnabled", "showUploadFile", "unreadCount", "currentRoom", "showBubbleChat", "db<PERSON><PERSON>", "dbUnreadPath", "usersRef", "collection", "readCount", "secondsToCheckOnlineStatus", "user", "getLoggedInUser", "chatsRef", "deleteChatFilesFromLibraryFolder", "fileUrl", "console", "log", "post", "observer", "then", "userCredential", "next", "complete", "catch", "error", "signInAnonym", "auth", "errorCode", "code", "errorMessage", "message", "getLastMessage", "classroomId", "chatRef", "doc", "snapshotChanges", "pipe", "action", "data", "payload", "id", "getClassroomChats", "actions", "logReadCount", "length", "a", "getChatMessagesAndMarkAsRead", "chatId", "userId", "limit", "lastMessage", "messagesQuery", "ref", "query", "orderBy", "startAfter", "timestamp", "messages", "markMessagesAsRead", "createChatRoom", "participants", "chat", "createdAt", "format", "lastMessageAt", "lastMessageFrom", "lastMessageFromName", "lastMessageFromImage", "lastMessageIsFile", "lastMessageDeleted", "lastMessageId", "lastProcessedMessageId", "add", "addMessage", "messageRef", "messageId", "get", "snapshot", "updateData", "content", "senderId", "getPersonFullName", "getUserPhoto", "avatarUrl", "isFileFromLibrary", "isUploadFile", "exists", "usersInChat", "teacher", "classroomStudents", "updatedUnreadCounts", "for<PERSON>ach", "chatUser", "aspUserId", "unreadCounts", "set", "update", "updateChatLastMessage", "updateLastMessage", "sendMessageAndMarkRead", "_this", "_asyncToGenerator", "unreadMessagesRef", "batch", "firestore", "messageData", "chatSnapshot", "existingData", "Object", "keys", "unreadMessages", "docData", "readStatus", "commit", "updatedMessage", "isLastMessageDeleted", "editMessage", "messagesRef", "lastMessageDoc", "to<PERSON>romise", "Promise", "resolve", "Error", "getLastUnreadMessagesCountForUser", "valueChanges", "markLastMessagesAsRead", "markLastMessagesAsReadInLastMessage", "chatUnreadCountsRef", "currentCount", "reject", "deleteMessage", "_this2", "delete", "latestMessage", "getLatestMessage", "updateLastMessageInBatch", "_this3", "currentUnreadCounts", "senderImage", "empty", "docs", "messageCount", "size", "updateChatRoom", "deleteChatRoom", "addUnreadMessage", "initialReadStatus", "querySnapshot", "hasUpdates", "readStatusUpdate", "markAllMessagesAsRead", "updatedReadStatus", "hasOwnProperty", "getUnreadMessagesCount", "filter", "getUnreadMessagesCountForUser", "chatSnapshots", "chatObservables", "reduce", "acc", "count", "getTotalUnreadMessagesCountForUser", "chatIds", "unreadMessageObservables", "total", "getFireBaseUser", "authState", "setUserStatus", "status", "userRef", "lastOnlineTime", "now", "merge", "setUserLastOnlineTime", "time", "getUserStatus", "getOnlineUsersComparedToLastOnlineTime", "currentTime", "seconds", "timeDifference", "getClassroomChatsWithUnreadCount", "unreadCount$", "uid", "chats", "getChatMessagesAndUnreadCounts", "countMessagesSentTodayByUser", "chatroomId", "startOfDay", "Date", "setHours", "endOfDay", "where", "parseInt", "localStorage", "getItem", "setItem", "toString", "_", "i0", "ɵɵinject", "i1", "AngularFirestore", "i2", "AngularFireAuth", "i3", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\services\\chat.service.ts"], "sourcesContent": ["import { Injectable, inject, signal } from '@angular/core';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { AngularFirestore, AngularFirestoreCollection, AngularFirestoreDocument } from '@angular/fire/compat/firestore';\r\nimport { FieldValue, serverTimestamp, Timestamp } from 'firebase/firestore';\r\nimport * as moment from 'moment';\r\nimport { Observable, combineLatest, defer, firstValueFrom, forkJoin, from } from 'rxjs';\r\nimport { map, switchMap, tap } from 'rxjs/operators';\r\nimport { UserService } from './user.service';\r\nimport { GeneralService } from './general.service';\r\nimport { environment } from 'src/environments/environment';\r\nconst BACKEND_URL = environment.apiUrl;\r\nconst BACKEND_LMS_URL = environment.apiUrl + \"/LMS/\";\r\nimport { getAuth, signInAnonymously } from \"firebase/auth\";\r\nimport { AngularFireAuth } from '@angular/fire/compat/auth';\r\nimport { AuthService } from './auth.service';\r\n\r\ninterface Chat {\r\n  createdAt: string;\r\n  lastMessage: string;\r\n  lastMessageAt: FieldValue;\r\n  lastMessageFrom: string;\r\n  lastMessageFromName: string;\r\n  lastMessageFromImage: string;\r\n  lastMessageIsFile: boolean;\r\n  lastMessageDeleted: boolean;\r\n  lastMessageId: string;\r\n  lastProcessedMessageId: string;\r\n  classroomId: string;\r\n  unreadCounts?: any;\r\n}\r\n\r\nexport interface ChatUserStatus {\r\n  id?: string;\r\n  status: 'online' | 'offline';\r\n  lastOnlineTime: FieldValue;\r\n}\r\n\r\n\r\nexport interface Message {\r\n  id?: string;\r\n  senderId: string;\r\n  content: string;\r\n  timestamp: FieldValue;\r\n  isFileFromLibrary: boolean;\r\n  isUploadFile: boolean;\r\n  readStatus: {}\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ChatService {\r\n  userService = inject(UserService);\r\n  generalService = inject(GeneralService);\r\n  authService = inject(AuthService);\r\n  isChatEnabled = signal(environment.isChatEnabled);\r\n  showUploadFile = signal(false);\r\n  unreadCount = signal(0);\r\n  currentRoom = signal({} as any);\r\n  showBubbleChat = signal(false);\r\n  private dbPath = '/chats';\r\n  private dbUnreadPath = '/chats';\r\n  chatsRef: AngularFirestoreCollection<Chat>;\r\n  private usersRef: AngularFirestoreCollection<ChatUserStatus> = this.db.collection('users');\r\n  readCount = 0;\r\n  readonly secondsToCheckOnlineStatus = 10;\r\n\r\n  user = this.authService.getLoggedInUser();\r\n\r\n  constructor(private db: AngularFirestore, private afAuth: AngularFireAuth, private http: HttpClient) {\r\n    this.chatsRef = db.collection(this.dbPath);\r\n    this.usersRef = db.collection('/users');\r\n  }\r\n\r\n\r\n  deleteChatFilesFromLibraryFolder(fileUrl: any): Observable<any> {\r\n    console.log(fileUrl)\r\n    return this.http.post<any>(BACKEND_LMS_URL + \"DeleteChatFilesFromLibraryFolder?fileUrl=\" + fileUrl, {})\r\n  }\r\n\r\n  // Sign in anonymously\r\n  signInAnonymously(): Observable<any> {\r\n    return new Observable(observer => {\r\n      this.afAuth.signInAnonymously()\r\n        .then(userCredential => {\r\n          console.log('Signed in anonymously:', userCredential.user);\r\n          observer.next(userCredential);\r\n          observer.complete();\r\n        })\r\n        .catch(error => {\r\n          console.error('Error signing in anonymously:', error);\r\n          observer.error(error);\r\n        });\r\n    });\r\n  }\r\n\r\n  signInAnonym() {\r\n    const auth = getAuth();\r\n    signInAnonymously(auth)\r\n      .then(() => {\r\n        // Signed in..\r\n      })\r\n      .catch((error) => {\r\n        const errorCode = error.code;\r\n        const errorMessage = error.message;\r\n        // ...\r\n      });\r\n  }\r\n\r\n  getLastMessage(classroomId: string): Observable<Message> {\r\n    const chatRef = this.chatsRef.doc<Message>(`${classroomId}/`);\r\n\r\n    return chatRef.snapshotChanges().pipe(\r\n      map(action => {\r\n        const data = action.payload.data() as Message;\r\n        const id = action.payload.id;\r\n        return { id, ...data };\r\n      })\r\n    );\r\n  }\r\n\r\n  // Fetch all chats for a classroom\r\n  getClassroomChats(classroomId: string): Observable<Chat[]> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    return this.chatsRef.doc(classroomId).collection<Chat>('chats')\r\n      .snapshotChanges().pipe(\r\n        tap(actions => this.logReadCount(actions.length)), // Log read count\r\n        map(actions => actions.map(a => {\r\n          const data = a.payload.doc.data() as Chat;\r\n          const id = a.payload.doc.id;\r\n          return { id, ...data };\r\n        }))\r\n      );\r\n  }\r\n\r\n  // Fetch messages for a specific chat room\r\n  getChatMessagesAndMarkAsRead(chatId: string, userId: string, limit: number, lastMessage?: Message): Observable<Message[]> {\r\n    // Create an observable to fetch messages\r\n    const messagesQuery = this.db.collection<Message>(`${this.dbPath}/${chatId}/messages`, ref => {\r\n      let query = ref.orderBy('timestamp', 'desc').limit(limit);\r\n      if (lastMessage) {\r\n        query = query.startAfter(lastMessage.timestamp);\r\n      }\r\n      return query;\r\n    });\r\n\r\n    // Fetch the messages and mark them as read\r\n    return messagesQuery.snapshotChanges().pipe(\r\n      tap(actions => this.logReadCount(actions.length)), // Log read count\r\n      map(actions => {\r\n        const messages: Message[] = actions.map(a => {\r\n          const data = a.payload.doc.data() as Message;\r\n          const id = a.payload.doc.id;\r\n          return { id, ...data };\r\n        });\r\n\r\n        // Mark messages as read\r\n        if (messages.length > 0) {\r\n          this.markMessagesAsRead(chatId, userId).catch(error => {\r\n            console.error('Failed to mark messages as read:', error);\r\n          });\r\n        }\r\n\r\n        return messages;\r\n      })\r\n    );\r\n  }\r\n\r\n  // Create a new chat room\r\n  createChatRoom(classroomId: string, participants: string[]) {\r\n    const chat: Chat = {\r\n      classroomId: classroomId,\r\n      createdAt: moment().format('YYYY-MM-DD HH:mm:ss'),\r\n      lastMessage: '',\r\n      lastMessageAt: serverTimestamp(),\r\n      lastMessageFrom: '',\r\n      lastMessageFromName: '',\r\n      lastMessageFromImage: '',\r\n      lastMessageIsFile: false,\r\n      lastMessageDeleted: false,\r\n      lastMessageId: '',\r\n      lastProcessedMessageId: '',\r\n    };\r\n    return this.chatsRef.add(chat);\r\n  }\r\n\r\n  // Add a new message to a chat room\r\n  addMessage(classroomId: string, message: Message, user: any): Promise<void> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    const messageRef = chatRef.collection('messages').doc();\r\n\r\n    const messageId = (messageRef.ref.id);\r\n    return firstValueFrom(chatRef.get()).then(snapshot => {\r\n      const updateData: any = {\r\n        createdAt: moment().format('YYYY-MM-DD HH:mm:ss'),\r\n        lastMessage: message.content,\r\n        lastMessageAt: serverTimestamp(),\r\n        classroomId: classroomId,\r\n        lastMessageFrom: message.senderId,\r\n        lastMessageFromName: this.generalService.getPersonFullName(user),\r\n        lastMessageFromImage: this.userService.getUserPhoto(user?.avatarUrl),\r\n        lastMessageIsFile: message.isFileFromLibrary || message.isUploadFile,\r\n        lastMessageDeleted: false,\r\n        lastMessageId: messageId, // Accessing id through ref\r\n        lastProcessedMessageId: '',\r\n      };\r\n\r\n      if (!snapshot.exists) {\r\n        // Retrieve users in the chat, including teacher and students\r\n        const usersInChat = [this.currentRoom().teacher, ...this.currentRoom().classroomStudents];\r\n        const updatedUnreadCounts: any = {};\r\n        usersInChat.forEach((chatUser: any) => {\r\n          if (chatUser.aspUserId !== message.senderId) {\r\n            updatedUnreadCounts[chatUser.aspUserId] = 1; // Initialize unread count to 1 for new chat\r\n          }\r\n        });\r\n        updateData.unreadCounts = updatedUnreadCounts;\r\n\r\n        return chatRef.set(updateData).then(() => {\r\n          return messageRef.set(message).then(() => {\r\n            return messageRef.update({ id: messageRef.ref.id }).then(() => {\r\n              return this.updateChatLastMessage(chatRef, classroomId);\r\n            })\r\n          });\r\n        });\r\n      } else {\r\n        // Existing chat room, update last message and unread counts\r\n        return this.updateLastMessage(chatRef, message, user, false, messageId).then(() => {\r\n          return messageRef.set(message).then(() => {\r\n            return messageRef.update({ id: messageRef.ref.id });\r\n          })\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  async sendMessageAndMarkRead(\r\n    classroomId: string,\r\n    message: Message,\r\n    user: any\r\n  ): Promise<void> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    const messageRef = chatRef.collection('messages').doc();\r\n    const unreadMessagesRef = this.db.collection(`${this.dbUnreadPath}/${classroomId}/messages`);\r\n\r\n    const messageId = messageRef.ref.id;\r\n    const batch = this.db.firestore.batch();\r\n\r\n    // Prepare message data\r\n    const messageData = {\r\n      ...message,\r\n      id: messageId,\r\n      createdAt: moment().format('YYYY-MM-DD HH:mm:ss')\r\n    };\r\n\r\n    // Add new message to the batch\r\n    batch.set(messageRef.ref, messageData);\r\n\r\n    const chatSnapshot = await firstValueFrom(chatRef.get());\r\n    const updateData: any = {\r\n      lastMessage: message.content,\r\n      lastMessageAt: serverTimestamp(),\r\n      lastMessageFrom: message.senderId,\r\n      lastMessageFromName: this.generalService.getPersonFullName(user),\r\n      lastMessageFromImage: this.userService.getUserPhoto(user?.avatarUrl),\r\n      lastMessageIsFile: message.isFileFromLibrary || message.isUploadFile,\r\n      lastMessageDeleted: false,\r\n      lastMessageId: messageId,\r\n    };\r\n\r\n    if (!chatSnapshot.exists) {\r\n      // New chat room: initialize unread counts for all users except the sender\r\n      const usersInChat = [this.currentRoom().teacher, ...this.currentRoom().classroomStudents];\r\n      const unreadCounts: { [key: string]: number } = {};\r\n\r\n      usersInChat.forEach(chatUser => {\r\n        if (chatUser.aspUserId !== message.senderId) {\r\n          unreadCounts[chatUser.aspUserId] = 1;\r\n        }\r\n      });\r\n\r\n      updateData.unreadCounts = unreadCounts;\r\n      batch.set(chatRef.ref, updateData);\r\n    } else {\r\n      // Update last message and adjust unread counts for existing room\r\n      const existingData = chatSnapshot.data();\r\n      const updatedUnreadCounts = { ...existingData?.unreadCounts };\r\n\r\n      Object.keys(updatedUnreadCounts).forEach(userId => {\r\n        if (userId !== message.senderId) {\r\n          updatedUnreadCounts[userId] = (updatedUnreadCounts[userId] || 0) + 1;\r\n        }\r\n      });\r\n\r\n      updateData.unreadCounts = updatedUnreadCounts;\r\n      batch.update(chatRef.ref, updateData);\r\n    }\r\n\r\n    // Update read status for the current user in unread messages\r\n    const unreadMessages = await firstValueFrom(unreadMessagesRef.get());\r\n    unreadMessages.forEach(doc => {  // Use .docs to access individual documents\r\n      const docData = doc.data() as any;   // Retrieve data from each document\r\n      const readStatus = docData.readStatus || {}; // Access or initialize readStatus\r\n\r\n      if (!readStatus[user.aspUserId]) {\r\n        readStatus[user.aspUserId] = true; // Mark as read for the current user\r\n        batch.update(doc.ref, { readStatus }); // Add update to batch\r\n      }\r\n    });\r\n\r\n\r\n\r\n    // Commit the entire batch\r\n    await batch.commit();\r\n  }\r\n\r\n\r\n  private updateLastMessage(chatRef: AngularFirestoreDocument<any>,\r\n    updatedMessage: Message, user: any, isLastMessageDeleted = false, messageId = ''): Promise<void> {\r\n\r\n    const messageRef = chatRef.collection('messages').doc();\r\n    const updateData: any = {\r\n      lastMessage: updatedMessage.content,\r\n      lastMessageAt: serverTimestamp(),\r\n      lastMessageFrom: updatedMessage.senderId,\r\n      lastMessageFromImage: this.userService.getUserPhoto(user?.avatarUrl),\r\n      lastMessageFromName: this.generalService.getPersonFullName(user),\r\n      lastMessageIsFile: updatedMessage.isFileFromLibrary || updatedMessage.isUploadFile,\r\n      lastMessageDeleted: isLastMessageDeleted,\r\n      lastMessageId: messageId\r\n    };\r\n\r\n    return firstValueFrom(chatRef.get()).then(snapshot => {\r\n      console.log('current room', this.currentRoom());\r\n      const updatedUnreadCounts: any = {};\r\n      // Retrieve users in the chat, including teacher and students\r\n      const usersInChat = [this.currentRoom().teacher, ...this.currentRoom().classroomStudents];\r\n\r\n      usersInChat.forEach((chatUser: any) => {\r\n        if (chatUser.aspUserId !== updatedMessage.senderId) {\r\n          updatedUnreadCounts[chatUser.aspUserId] = (snapshot!.exists ? (snapshot!.data() as any).unreadCounts?.[chatUser.aspUserId] || 0 : 0) + 1;\r\n        }\r\n      });\r\n\r\n      updateData.unreadCounts = updatedUnreadCounts;\r\n      return chatRef.update(updateData);\r\n    });\r\n  }\r\n\r\n  editMessage(classroomId: string, messageId: string, updatedMessage: Message, user: any, updateLastMessage = false): Promise<void> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    const messageRef = chatRef.collection('messages').doc(`${messageId}`);\r\n    const messagesRef = chatRef.collection('messages', ref => ref.orderBy('timestamp', 'desc').limit(1));\r\n\r\n    const lastMessageDoc = messagesRef.ref.doc();\r\n    const lastMessageId = lastMessageDoc.id;\r\n    console.log(lastMessageId);\r\n    return messageRef.get().toPromise().then(snapshot => {\r\n      if (snapshot!.exists) {\r\n\r\n\r\n        return messageRef.update({\r\n          ...updatedMessage,\r\n        }).then(() => {\r\n          if (updateLastMessage) {\r\n            return this.updateLastMessage(chatRef, updatedMessage, user, false, updatedMessage.id!);\r\n          }\r\n          return Promise.resolve();\r\n        });\r\n      } else {\r\n        throw new Error('Message does not exist');\r\n      }\r\n    });\r\n  }\r\n\r\n  getLastUnreadMessagesCountForUser(classroomId: string, userId: string): Observable<number> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n\r\n    return chatRef.valueChanges().pipe(\r\n      tap(actions => this.logReadCount(actions?.unreadCounts?.[userId] || 0)), // Log read count\r\n      map(data => {\r\n        const unreadCounts = (data as any)?.unreadCounts;\r\n        return unreadCounts ? unreadCounts[userId] || 0 : 0;\r\n      })\r\n    );\r\n  }\r\n\r\n  markLastMessagesAsRead(classroomId: string, userId: string): Promise<void> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    return this.markLastMessagesAsReadInLastMessage(chatRef, userId);\r\n  }\r\n\r\n  markLastMessagesAsReadInLastMessage(chatUnreadCountsRef: AngularFirestoreDocument<any>, userId: string): Promise<void> {\r\n    return chatUnreadCountsRef.get().toPromise().then(snapshot => {\r\n      if (snapshot!.exists) {\r\n        const data = snapshot!.data() as any;\r\n        const currentCount = data.unreadCounts?.[userId] || 0;\r\n\r\n        // Only proceed with the update if the unread count for the user is greater than zero\r\n        if (currentCount > 0) {\r\n          const updatedUnreadCounts = { ...data.unreadCounts, [userId]: 0 };\r\n          return chatUnreadCountsRef.update({ unreadCounts: updatedUnreadCounts });\r\n        }\r\n      }\r\n      // Resolve immediately if the document doesn't exist or no update is needed\r\n      return Promise.resolve();\r\n    }).catch(error => {\r\n      console.error('Failed to mark messages as read:', error);\r\n      return Promise.reject(error);\r\n    });\r\n  }\r\n\r\n  // Delete a specific message from a chat room\r\n  async deleteMessage(classroomId: string, message: any, user: any, updateLastMessage = false): Promise<void> {\r\n    console.log(classroomId);\r\n    console.log(message);\r\n\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    const messageRef = chatRef.collection('messages').doc(message.id);\r\n\r\n    // Start a batch to combine operations\r\n    const batch = this.db.firestore.batch();\r\n\r\n    // Queue the deletion of the message\r\n    batch.delete(messageRef.ref);\r\n\r\n    if (updateLastMessage) {\r\n      const latestMessage = await this.getLatestMessage(classroomId);\r\n      const updateData = {\r\n        ...message,\r\n        content: 'Message Deleted'\r\n      };\r\n\r\n      await this.updateLastMessageInBatch(chatRef, updateData, user, true, latestMessage?.id, batch);\r\n    }\r\n\r\n    // Commit the batch\r\n    await batch.commit();\r\n\r\n    // Update the chat's last message if not updating the last message\r\n    if (!updateLastMessage) {\r\n      await this.updateChatLastMessage(chatRef, classroomId);\r\n    }\r\n  }\r\n\r\n  private async updateLastMessageInBatch(\r\n    chatRef: AngularFirestoreDocument<any>,\r\n    updatedMessage: any,\r\n    user: any,\r\n    isLastMessageDeleted: boolean,\r\n    messageId: string | undefined,\r\n    batch: any\r\n  ): Promise<void> {\r\n    const updateData: any = {\r\n      lastMessage: updatedMessage.content,\r\n      lastMessageAt: serverTimestamp(),\r\n      lastMessageFrom: updatedMessage.senderId,\r\n      lastMessageFromImage: this.userService.getUserPhoto(user?.avatarUrl),\r\n      lastMessageFromName: this.generalService.getPersonFullName(user),\r\n      lastMessageIsFile: updatedMessage.isFileFromLibrary || updatedMessage.isUploadFile,\r\n      lastMessageDeleted: isLastMessageDeleted,\r\n      lastMessageId: messageId\r\n    };\r\n\r\n    // Get snapshot of the chat document\r\n    const snapshot = await firstValueFrom(chatRef.get());\r\n\r\n    // Prepare updated unread counts\r\n    const updatedUnreadCounts: any = {};\r\n    const usersInChat = [this.currentRoom().teacher, ...this.currentRoom().classroomStudents];\r\n    const currentUnreadCounts = snapshot.exists ? snapshot.data()?.unreadCounts || {} : {};\r\n\r\n    usersInChat.forEach((chatUser: any) => {\r\n      if (chatUser.aspUserId !== updatedMessage.senderId) {\r\n        updatedUnreadCounts[chatUser.aspUserId] = (currentUnreadCounts[chatUser.aspUserId] || 0) + 1; // Increment unread count\r\n      }\r\n    });\r\n\r\n    // Only update if there are changes to the unread counts\r\n    if (Object.keys(updatedUnreadCounts).length > 0) {\r\n      updateData.unreadCounts = updatedUnreadCounts;\r\n    }\r\n\r\n    // Perform a single update to the chat document in the batch\r\n    batch.update(chatRef.ref, updateData);\r\n  }\r\n\r\n  private updateChatLastMessage(chatRef: AngularFirestoreDocument<any>, classroomId: string): Promise<void> {\r\n    return this.getLatestMessage(classroomId).then((snapshot: any | null) => {\r\n\r\n      console.log(snapshot);\r\n      if (snapshot) {\r\n        const latestMessage = snapshot;\r\n        return chatRef.update({\r\n          lastMessage: latestMessage.content,\r\n          lastMessageAt: latestMessage.createdAt,\r\n          lastMessageFrom: latestMessage.senderId,\r\n          lastMessageFromName: this.generalService.getPersonFullName(this.user),\r\n          lastMessageFromImage: latestMessage.senderImage,\r\n          lastMessageIsFile: latestMessage.isFileFromLibrary || latestMessage.isUploadFile,\r\n          lastMessageDeleted: false,\r\n          lastMessageId: latestMessage.id,\r\n        });\r\n      } else {\r\n        // Handle case where there are no messages left in the collection\r\n        return chatRef.update({\r\n          lastMessage: '',\r\n          lastMessageAt: null,\r\n          lastMessageFrom: '',\r\n          lastMessageFromName: '',\r\n          lastMessageFromImage: '',\r\n          lastMessageIsFile: false,\r\n          lastMessageDeleted: false,\r\n          lastMessageId: '',\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  getLatestMessage(classroomId: string): Promise<Message | null> {\r\n    const chatRef = this.chatsRef.doc(`${classroomId}`);\r\n    return firstValueFrom(chatRef.collection('messages', ref => ref.orderBy('timestamp', 'desc').limit(1)).get()).then(snapshot => {\r\n      if (!snapshot.empty) {\r\n        const latestMessage = snapshot.docs[0].data() as Message;\r\n        const messageCount = snapshot.size; // Get the count of messages retrieved\r\n        this.logReadCount(messageCount);\r\n\r\n        return latestMessage;\r\n      } else {\r\n        return null; // No messages found\r\n      }\r\n    });\r\n  }\r\n\r\n  // Update a chat room document\r\n  updateChatRoom(classroomId: string, data: Partial<Chat>): Promise<void> {\r\n    return this.chatsRef.doc(classroomId).update(data);\r\n  }\r\n\r\n  // Delete a chat room document\r\n  deleteChatRoom(classroomId: string): Promise<void> {\r\n    return this.chatsRef.doc(classroomId).delete();\r\n  }\r\n\r\n  addUnreadMessage(classroomId: string, message: Message): Promise<void> {\r\n    const messageRef = this.db.collection(`${this.dbUnreadPath}/${classroomId}/messages`).doc(message.id);\r\n\r\n    // Set the initial readStatus for the new message\r\n    const initialReadStatus = {\r\n      // Initialize the readStatus for all users, defaulting to false (unread)\r\n      [message.senderId]: false, // Mark the sender as false (unread)\r\n      // You can add more user IDs here if needed, depending on your application logic\r\n    };\r\n\r\n    return messageRef.set({\r\n      readStatus: initialReadStatus, // Add the readStatus field\r\n      timestamp: serverTimestamp(), // Ensure there's a timestamp for ordering\r\n    });\r\n  }\r\n\r\n  markMessagesAsRead(classroomId: string, userId: string): Promise<void> {\r\n    return this.db.collection(`${this.dbUnreadPath}/${classroomId}/messages`).get().toPromise().then(querySnapshot => {\r\n      const batch = this.db.firestore.batch();\r\n      let hasUpdates = false; // Flag to track if there are any updates\r\n\r\n      querySnapshot!.forEach(doc => {\r\n        const data = doc.data();\r\n\r\n        // Ensure readStatus is a valid map or initialize it\r\n        const readStatusUpdate = (data as any).readStatus || {};\r\n\r\n        // Only update if the userId is not already marked as read\r\n        if (!readStatusUpdate[userId]) {\r\n          readStatusUpdate[userId] = true;\r\n          // Update the document with the new or modified readStatus map\r\n          batch.update(doc.ref, { readStatus: readStatusUpdate });\r\n          hasUpdates = true; // Set the flag to true if an update is made\r\n        }\r\n      });\r\n\r\n      // Only commit the batch if there are updates\r\n      if (hasUpdates) {\r\n        return batch.commit();\r\n      }\r\n\r\n      // If no updates were made, resolve immediately\r\n      return Promise.resolve();\r\n    });\r\n  }\r\n\r\n  // Mark messages as read\r\n  markAllMessagesAsRead(classroomId: string): Promise<void> {\r\n    return this.db.collection(`${this.dbUnreadPath}/${classroomId}/messages`).get().toPromise().then(querySnapshot => {\r\n      const batch = this.db.firestore.batch();\r\n      querySnapshot!.forEach(doc => {\r\n        const messageData = doc.data() as Message;\r\n        const updatedReadStatus = {};\r\n\r\n        // Set readStatus for all users in the message's readStatus map to true\r\n        for (const userId in messageData.readStatus) {\r\n          if (messageData.readStatus.hasOwnProperty(userId)) {\r\n            const a = `readStatus.${userId}`;\r\n            (updatedReadStatus as any)[a] = true;\r\n          }\r\n        }\r\n\r\n        batch.update(doc.ref, updatedReadStatus);\r\n      });\r\n      return batch.commit();\r\n    });\r\n  }\r\n\r\n  // Get unread messages count for a user in a classroom\r\n  getUnreadMessagesCount(classroomId: string, userId: string): Observable<number> {\r\n    return this.db.collection<Message>(`${this.dbUnreadPath}/${classroomId}/messages`).snapshotChanges().pipe(\r\n      tap(actions => this.logReadCount(actions.length)), // Log read count\r\n      map(actions => actions.filter(action => {\r\n        const data = action.payload.doc.data() as Message;\r\n        return !data.readStatus.hasOwnProperty(userId);\r\n      }).length)\r\n    );\r\n  }\r\n\r\n\r\n  // Get total unread messages count for a user across all classrooms\r\n  getUnreadMessagesCountForUser(userId: string): Observable<number> {\r\n    return this.chatsRef.snapshotChanges().pipe(\r\n      tap(actions => this.logReadCount(actions.length)), // Log read count\r\n      switchMap(chatSnapshots => {\r\n        const chatObservables = chatSnapshots.map(snapshot => {\r\n          const chatId = snapshot.payload.doc.id;\r\n          return this.getUnreadMessagesCount(chatId, userId);\r\n        });\r\n        return combineLatest(chatObservables);\r\n      }),\r\n      map(unreadCounts => unreadCounts.reduce((acc, count) => acc + count, 0))\r\n    );\r\n  }\r\n\r\n  getTotalUnreadMessagesCountForUser(userId: string): Observable<number> {\r\n    return this.chatsRef.snapshotChanges().pipe(\r\n      tap(actions => this.logReadCount(actions.length)), // Log read count\r\n      switchMap(chatSnapshots => {\r\n        const chatIds = chatSnapshots.map(snapshot => snapshot.payload.doc.id);\r\n        // Create an array of observables for each classroom's unread messages\r\n        const unreadMessageObservables = chatIds.map(classroomId =>\r\n          this.chatsRef.doc(classroomId).collection<Message>('messages').snapshotChanges()\r\n            .pipe(\r\n              map(actions => actions.filter(action => {\r\n                const data = action.payload.doc.data() as Message;\r\n                return !data.readStatus.hasOwnProperty(userId);\r\n              }).length)\r\n            )\r\n        );\r\n\r\n        // Combine all observables into a single observable\r\n        return combineLatest(unreadMessageObservables);\r\n      }),\r\n      map(unreadCounts => unreadCounts.reduce((total, count) => total + count, 0))\r\n    );\r\n  }\r\n\r\n  // Or, to get the user object directly\r\n  getFireBaseUser(): Observable<any> {\r\n    return this.afAuth.authState;\r\n  }\r\n\r\n  setUserStatus(userId: string, status: 'online' | 'offline'): Promise<void> {\r\n    const userRef = this.usersRef.doc(userId);\r\n    return userRef.set({ status, lastOnlineTime: Timestamp.now() }, { merge: true });\r\n  }\r\n\r\n  setUserLastOnlineTime(userId: string, time: FieldValue): Promise<void> {\r\n    const userRef = this.usersRef.doc(userId);\r\n    return userRef.set({ status: 'online', lastOnlineTime: time }, { merge: true });\r\n  }\r\n\r\n  getUserStatus(userId: string): Observable<'online' | 'offline'> {\r\n    return this.usersRef.doc<ChatUserStatus>(userId).valueChanges().pipe(\r\n      map(user => user?.status || 'offline')\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Retrieves all users in the Firestore 'users' collection, including their status compared to the last online time.\r\n   * The status is determined by comparing the current time with the user's last online time. If the user was online\r\n   * within the last `secondsToCheckOnlineStatus` seconds, the status is set to 'online', otherwise it is set to 'offline'.\r\n   * If the user does not have a last online time, the status is set to 'offline'.\r\n   * @returns An Observable of an array of ChatUserStatus objects, where each object contains the user's id, status, and last online time.\r\n   */\r\n  getOnlineUsersComparedToLastOnlineTime(): Observable<ChatUserStatus[]> {\r\n    return this.db.collection<ChatUserStatus>('/users')\r\n      .snapshotChanges().pipe(\r\n        tap(actions => this.logReadCount(actions.length)), // Log read count\r\n        map(actions => {\r\n          const currentTime = Timestamp.now();\r\n          return actions.map(a => {\r\n            const data = a.payload.doc.data() as ChatUserStatus;\r\n            const id = a.payload.doc.id;\r\n            const lastOnlineTime = data.lastOnlineTime as Timestamp;\r\n            if (lastOnlineTime) {\r\n              if (!currentTime.seconds || !lastOnlineTime.seconds) {\r\n                return { ...data, id, status: 'offline' };\r\n              }\r\n              const timeDifference = currentTime?.seconds - lastOnlineTime?.seconds;\r\n\r\n              // Check if the user was online within the last 10 seconds\r\n              if (timeDifference <= this.secondsToCheckOnlineStatus) {\r\n                return { ...data, id, status: 'online' };\r\n              } else {\r\n                return { ...data, id, status: 'offline' };\r\n              }\r\n            }\r\n            return { ...data, id, status: 'offline' };\r\n\r\n          });\r\n        })\r\n      );\r\n  }\r\n\r\n  // Fetch all chats for a classroom along with the last unread messages count\r\n  getClassroomChatsWithUnreadCount(classroomId: string): Observable<{ chats: Chat[], unreadCount: number }> {\r\n    const chatRef = this.chatsRef.doc(classroomId).collection<Chat>('chats');\r\n    const unreadCount$ = this.getUnreadMessagesCount(classroomId, this.user?.uid); // Assuming 'this.user' is defined\r\n\r\n    return combineLatest([\r\n      chatRef.snapshotChanges().pipe(\r\n        map(actions => actions.map(a => {\r\n          const data = a.payload.doc.data() as Chat;\r\n          const id = a.payload.doc.id;\r\n          return { id, ...data };\r\n        }))\r\n      ),\r\n      unreadCount$\r\n    ]).pipe(\r\n      map(([chats, unreadCount]) => ({ chats, unreadCount }))\r\n    );\r\n  }\r\n\r\n  // Use this method for fetching messages and marking them as read\r\n  getChatMessagesAndUnreadCounts(chatId: string, userId: string, limit: number): Observable<{ messages: Message[], unreadCount: number }> {\r\n    const messagesQuery = this.db.collection<Message>(`${this.dbPath}/${chatId}/messages`, ref =>\r\n      ref.orderBy('timestamp', 'desc').limit(limit)\r\n    );\r\n\r\n    const unreadCount$ = this.getUnreadMessagesCount(chatId, userId);\r\n\r\n    return combineLatest([\r\n      messagesQuery.snapshotChanges().pipe(\r\n        map(actions => actions.map(a => {\r\n          const data = a.payload.doc.data() as Message;\r\n          const id = a.payload.doc.id;\r\n          return { id, ...data };\r\n        }))\r\n      ),\r\n      unreadCount$\r\n    ]).pipe(\r\n      map(([messages, unreadCount]) => ({ messages, unreadCount }))\r\n    );\r\n  }\r\n\r\n  // Method to count messages sent by a user today per chatroom\r\n  countMessagesSentTodayByUser(userId: string, chatroomId: string): Observable<number> {\r\n    const chatRef = this.chatsRef.doc(`${chatroomId}`).collection<Message>('messages');\r\n\r\n    // Get the start and end timestamps for today\r\n    const startOfDay = new Date();\r\n    startOfDay.setHours(0, 0, 0, 0); // Start of today\r\n    const endOfDay = new Date();\r\n    endOfDay.setHours(23, 59, 59, 999); // End of today\r\n\r\n    // Create an observable from the Firestore query\r\n    return new Observable<number>(observer => {\r\n      chatRef.ref.where('senderId', '==', userId)\r\n        .where('timestamp', '>=', startOfDay)\r\n        .where('timestamp', '<=', endOfDay)\r\n        .get()\r\n        .then(snapshot => {\r\n          observer.next(snapshot.size); // Emit the count\r\n          observer.complete();            // Complete the observable\r\n        })\r\n        .catch(error => {\r\n          observer.error(error); // Emit an error if something goes wrong\r\n        });\r\n    });\r\n  }\r\n\r\n  logReadCount(count: number) {\r\n    const currentCount = parseInt(localStorage.getItem('readCount') || '0', 10);\r\n    localStorage.setItem('readCount', (currentCount + count).toString());\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAAqBA,MAAM,EAAEC,MAAM,QAAQ,eAAe;AAG1D,SAAqBC,eAAe,EAAEC,SAAS,QAAQ,oBAAoB;AAC3E,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAChC,SAASC,UAAU,EAAEC,aAAa,EAASC,cAAc,QAAwB,MAAM;AACvF,SAASC,GAAG,EAAEC,SAAS,EAAEC,GAAG,QAAQ,gBAAgB;AACpD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,WAAW,QAAQ,8BAA8B;AAG1D,SAASC,OAAO,EAAEC,iBAAiB,QAAQ,eAAe;AAE1D,SAASC,WAAW,QAAQ,gBAAgB;;;;;AAJ5C,MAAMC,WAAW,GAAGJ,WAAW,CAACK,MAAM;AACtC,MAAMC,eAAe,GAAGN,WAAW,CAACK,MAAM,GAAG,OAAO;AAwCpD,OAAM,MAAOE,WAAW;EAkBtBC,YAAoBC,EAAoB,EAAUC,MAAuB,EAAUC,IAAgB;IAA/E,KAAAF,EAAE,GAAFA,EAAE;IAA4B,KAAAC,MAAM,GAANA,MAAM;IAA2B,KAAAC,IAAI,GAAJA,IAAI;IAjBvF,KAAAC,WAAW,GAAGzB,MAAM,CAACW,WAAW,CAAC;IACjC,KAAAe,cAAc,GAAG1B,MAAM,CAACY,cAAc,CAAC;IACvC,KAAAe,WAAW,GAAG3B,MAAM,CAACgB,WAAW,CAAC;IACjC,KAAAY,aAAa,GAAG3B,MAAM,CAACY,WAAW,CAACe,aAAa,CAAC;IACjD,KAAAC,cAAc,GAAG5B,MAAM,CAAC,KAAK,CAAC;IAC9B,KAAA6B,WAAW,GAAG7B,MAAM,CAAC,CAAC,CAAC;IACvB,KAAA8B,WAAW,GAAG9B,MAAM,CAAC,EAAS,CAAC;IAC/B,KAAA+B,cAAc,GAAG/B,MAAM,CAAC,KAAK,CAAC;IACtB,KAAAgC,MAAM,GAAG,QAAQ;IACjB,KAAAC,YAAY,GAAG,QAAQ;IAEvB,KAAAC,QAAQ,GAA+C,IAAI,CAACb,EAAE,CAACc,UAAU,CAAC,OAAO,CAAC;IAC1F,KAAAC,SAAS,GAAG,CAAC;IACJ,KAAAC,0BAA0B,GAAG,EAAE;IAExC,KAAAC,IAAI,GAAG,IAAI,CAACZ,WAAW,CAACa,eAAe,EAAE;IAGvC,IAAI,CAACC,QAAQ,GAAGnB,EAAE,CAACc,UAAU,CAAC,IAAI,CAACH,MAAM,CAAC;IAC1C,IAAI,CAACE,QAAQ,GAAGb,EAAE,CAACc,UAAU,CAAC,QAAQ,CAAC;EACzC;EAGAM,gCAAgCA,CAACC,OAAY;IAC3CC,OAAO,CAACC,GAAG,CAACF,OAAO,CAAC;IACpB,OAAO,IAAI,CAACnB,IAAI,CAACsB,IAAI,CAAM3B,eAAe,GAAG,2CAA2C,GAAGwB,OAAO,EAAE,EAAE,CAAC;EACzG;EAEA;EACA5B,iBAAiBA,CAAA;IACf,OAAO,IAAIV,UAAU,CAAC0C,QAAQ,IAAG;MAC/B,IAAI,CAACxB,MAAM,CAACR,iBAAiB,EAAE,CAC5BiC,IAAI,CAACC,cAAc,IAAG;QACrBL,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEI,cAAc,CAACV,IAAI,CAAC;QAC1DQ,QAAQ,CAACG,IAAI,CAACD,cAAc,CAAC;QAC7BF,QAAQ,CAACI,QAAQ,EAAE;MACrB,CAAC,CAAC,CACDC,KAAK,CAACC,KAAK,IAAG;QACbT,OAAO,CAACS,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrDN,QAAQ,CAACM,KAAK,CAACA,KAAK,CAAC;MACvB,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;EAEAC,YAAYA,CAAA;IACV,MAAMC,IAAI,GAAGzC,OAAO,EAAE;IACtBC,iBAAiB,CAACwC,IAAI,CAAC,CACpBP,IAAI,CAAC,MAAK;MACT;IAAA,CACD,CAAC,CACDI,KAAK,CAAEC,KAAK,IAAI;MACf,MAAMG,SAAS,GAAGH,KAAK,CAACI,IAAI;MAC5B,MAAMC,YAAY,GAAGL,KAAK,CAACM,OAAO;MAClC;IACF,CAAC,CAAC;EACN;EAEAC,cAAcA,CAACC,WAAmB;IAChC,MAAMC,OAAO,GAAG,IAAI,CAACrB,QAAQ,CAACsB,GAAG,CAAU,GAAGF,WAAW,GAAG,CAAC;IAE7D,OAAOC,OAAO,CAACE,eAAe,EAAE,CAACC,IAAI,CACnCzD,GAAG,CAAC0D,MAAM,IAAG;MACX,MAAMC,IAAI,GAAGD,MAAM,CAACE,OAAO,CAACD,IAAI,EAAa;MAC7C,MAAME,EAAE,GAAGH,MAAM,CAACE,OAAO,CAACC,EAAE;MAC5B,OAAO;QAAEA,EAAE;QAAE,GAAGF;MAAI,CAAE;IACxB,CAAC,CAAC,CACH;EACH;EAEA;EACAG,iBAAiBA,CAACT,WAAmB;IACnC,MAAMC,OAAO,GAAG,IAAI,CAACrB,QAAQ,CAACsB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IACnD,OAAO,IAAI,CAACpB,QAAQ,CAACsB,GAAG,CAACF,WAAW,CAAC,CAACzB,UAAU,CAAO,OAAO,CAAC,CAC5D4B,eAAe,EAAE,CAACC,IAAI,CACrBvD,GAAG,CAAC6D,OAAO,IAAI,IAAI,CAACC,YAAY,CAACD,OAAO,CAACE,MAAM,CAAC,CAAC;IAAE;IACnDjE,GAAG,CAAC+D,OAAO,IAAIA,OAAO,CAAC/D,GAAG,CAACkE,CAAC,IAAG;MAC7B,MAAMP,IAAI,GAAGO,CAAC,CAACN,OAAO,CAACL,GAAG,CAACI,IAAI,EAAU;MACzC,MAAME,EAAE,GAAGK,CAAC,CAACN,OAAO,CAACL,GAAG,CAACM,EAAE;MAC3B,OAAO;QAAEA,EAAE;QAAE,GAAGF;MAAI,CAAE;IACxB,CAAC,CAAC,CAAC,CACJ;EACL;EAEA;EACAQ,4BAA4BA,CAACC,MAAc,EAAEC,MAAc,EAAEC,KAAa,EAAEC,WAAqB;IAC/F;IACA,MAAMC,aAAa,GAAG,IAAI,CAAC1D,EAAE,CAACc,UAAU,CAAU,GAAG,IAAI,CAACH,MAAM,IAAI2C,MAAM,WAAW,EAAEK,GAAG,IAAG;MAC3F,IAAIC,KAAK,GAAGD,GAAG,CAACE,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAACL,KAAK,CAACA,KAAK,CAAC;MACzD,IAAIC,WAAW,EAAE;QACfG,KAAK,GAAGA,KAAK,CAACE,UAAU,CAACL,WAAW,CAACM,SAAS,CAAC;MACjD;MACA,OAAOH,KAAK;IACd,CAAC,CAAC;IAEF;IACA,OAAOF,aAAa,CAAChB,eAAe,EAAE,CAACC,IAAI,CACzCvD,GAAG,CAAC6D,OAAO,IAAI,IAAI,CAACC,YAAY,CAACD,OAAO,CAACE,MAAM,CAAC,CAAC;IAAE;IACnDjE,GAAG,CAAC+D,OAAO,IAAG;MACZ,MAAMe,QAAQ,GAAcf,OAAO,CAAC/D,GAAG,CAACkE,CAAC,IAAG;QAC1C,MAAMP,IAAI,GAAGO,CAAC,CAACN,OAAO,CAACL,GAAG,CAACI,IAAI,EAAa;QAC5C,MAAME,EAAE,GAAGK,CAAC,CAACN,OAAO,CAACL,GAAG,CAACM,EAAE;QAC3B,OAAO;UAAEA,EAAE;UAAE,GAAGF;QAAI,CAAE;MACxB,CAAC,CAAC;MAEF;MACA,IAAImB,QAAQ,CAACb,MAAM,GAAG,CAAC,EAAE;QACvB,IAAI,CAACc,kBAAkB,CAACX,MAAM,EAAEC,MAAM,CAAC,CAACzB,KAAK,CAACC,KAAK,IAAG;UACpDT,OAAO,CAACS,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QAC1D,CAAC,CAAC;MACJ;MAEA,OAAOiC,QAAQ;IACjB,CAAC,CAAC,CACH;EACH;EAEA;EACAE,cAAcA,CAAC3B,WAAmB,EAAE4B,YAAsB;IACxD,MAAMC,IAAI,GAAS;MACjB7B,WAAW,EAAEA,WAAW;MACxB8B,SAAS,EAAEvF,MAAM,EAAE,CAACwF,MAAM,CAAC,qBAAqB,CAAC;MACjDb,WAAW,EAAE,EAAE;MACfc,aAAa,EAAE3F,eAAe,EAAE;MAChC4F,eAAe,EAAE,EAAE;MACnBC,mBAAmB,EAAE,EAAE;MACvBC,oBAAoB,EAAE,EAAE;MACxBC,iBAAiB,EAAE,KAAK;MACxBC,kBAAkB,EAAE,KAAK;MACzBC,aAAa,EAAE,EAAE;MACjBC,sBAAsB,EAAE;KACzB;IACD,OAAO,IAAI,CAAC3D,QAAQ,CAAC4D,GAAG,CAACX,IAAI,CAAC;EAChC;EAEA;EACAY,UAAUA,CAACzC,WAAmB,EAAEF,OAAgB,EAAEpB,IAAS;IACzD,MAAMuB,OAAO,GAAG,IAAI,CAACrB,QAAQ,CAACsB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IACnD,MAAM0C,UAAU,GAAGzC,OAAO,CAAC1B,UAAU,CAAC,UAAU,CAAC,CAAC2B,GAAG,EAAE;IAEvD,MAAMyC,SAAS,GAAID,UAAU,CAACtB,GAAG,CAACZ,EAAG;IACrC,OAAO9D,cAAc,CAACuD,OAAO,CAAC2C,GAAG,EAAE,CAAC,CAACzD,IAAI,CAAC0D,QAAQ,IAAG;MACnD,MAAMC,UAAU,GAAQ;QACtBhB,SAAS,EAAEvF,MAAM,EAAE,CAACwF,MAAM,CAAC,qBAAqB,CAAC;QACjDb,WAAW,EAAEpB,OAAO,CAACiD,OAAO;QAC5Bf,aAAa,EAAE3F,eAAe,EAAE;QAChC2D,WAAW,EAAEA,WAAW;QACxBiC,eAAe,EAAEnC,OAAO,CAACkD,QAAQ;QACjCd,mBAAmB,EAAE,IAAI,CAACrE,cAAc,CAACoF,iBAAiB,CAACvE,IAAI,CAAC;QAChEyD,oBAAoB,EAAE,IAAI,CAACvE,WAAW,CAACsF,YAAY,CAACxE,IAAI,EAAEyE,SAAS,CAAC;QACpEf,iBAAiB,EAAEtC,OAAO,CAACsD,iBAAiB,IAAItD,OAAO,CAACuD,YAAY;QACpEhB,kBAAkB,EAAE,KAAK;QACzBC,aAAa,EAAEK,SAAS;QAAE;QAC1BJ,sBAAsB,EAAE;OACzB;MAED,IAAI,CAACM,QAAQ,CAACS,MAAM,EAAE;QACpB;QACA,MAAMC,WAAW,GAAG,CAAC,IAAI,CAACrF,WAAW,EAAE,CAACsF,OAAO,EAAE,GAAG,IAAI,CAACtF,WAAW,EAAE,CAACuF,iBAAiB,CAAC;QACzF,MAAMC,mBAAmB,GAAQ,EAAE;QACnCH,WAAW,CAACI,OAAO,CAAEC,QAAa,IAAI;UACpC,IAAIA,QAAQ,CAACC,SAAS,KAAK/D,OAAO,CAACkD,QAAQ,EAAE;YAC3CU,mBAAmB,CAACE,QAAQ,CAACC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;UAC/C;QACF,CAAC,CAAC;QACFf,UAAU,CAACgB,YAAY,GAAGJ,mBAAmB;QAE7C,OAAOzD,OAAO,CAAC8D,GAAG,CAACjB,UAAU,CAAC,CAAC3D,IAAI,CAAC,MAAK;UACvC,OAAOuD,UAAU,CAACqB,GAAG,CAACjE,OAAO,CAAC,CAACX,IAAI,CAAC,MAAK;YACvC,OAAOuD,UAAU,CAACsB,MAAM,CAAC;cAAExD,EAAE,EAAEkC,UAAU,CAACtB,GAAG,CAACZ;YAAE,CAAE,CAAC,CAACrB,IAAI,CAAC,MAAK;cAC5D,OAAO,IAAI,CAAC8E,qBAAqB,CAAChE,OAAO,EAAED,WAAW,CAAC;YACzD,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA,OAAO,IAAI,CAACkE,iBAAiB,CAACjE,OAAO,EAAEH,OAAO,EAAEpB,IAAI,EAAE,KAAK,EAAEiE,SAAS,CAAC,CAACxD,IAAI,CAAC,MAAK;UAChF,OAAOuD,UAAU,CAACqB,GAAG,CAACjE,OAAO,CAAC,CAACX,IAAI,CAAC,MAAK;YACvC,OAAOuD,UAAU,CAACsB,MAAM,CAAC;cAAExD,EAAE,EAAEkC,UAAU,CAACtB,GAAG,CAACZ;YAAE,CAAE,CAAC;UACrD,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EAEM2D,sBAAsBA,CAC1BnE,WAAmB,EACnBF,OAAgB,EAChBpB,IAAS;IAAA,IAAA0F,KAAA;IAAA,OAAAC,iBAAA;MAET,MAAMpE,OAAO,GAAGmE,KAAI,CAACxF,QAAQ,CAACsB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;MACnD,MAAM0C,UAAU,GAAGzC,OAAO,CAAC1B,UAAU,CAAC,UAAU,CAAC,CAAC2B,GAAG,EAAE;MACvD,MAAMoE,iBAAiB,GAAGF,KAAI,CAAC3G,EAAE,CAACc,UAAU,CAAC,GAAG6F,KAAI,CAAC/F,YAAY,IAAI2B,WAAW,WAAW,CAAC;MAE5F,MAAM2C,SAAS,GAAGD,UAAU,CAACtB,GAAG,CAACZ,EAAE;MACnC,MAAM+D,KAAK,GAAGH,KAAI,CAAC3G,EAAE,CAAC+G,SAAS,CAACD,KAAK,EAAE;MAEvC;MACA,MAAME,WAAW,GAAG;QAClB,GAAG3E,OAAO;QACVU,EAAE,EAAEmC,SAAS;QACbb,SAAS,EAAEvF,MAAM,EAAE,CAACwF,MAAM,CAAC,qBAAqB;OACjD;MAED;MACAwC,KAAK,CAACR,GAAG,CAACrB,UAAU,CAACtB,GAAG,EAAEqD,WAAW,CAAC;MAEtC,MAAMC,YAAY,SAAShI,cAAc,CAACuD,OAAO,CAAC2C,GAAG,EAAE,CAAC;MACxD,MAAME,UAAU,GAAQ;QACtB5B,WAAW,EAAEpB,OAAO,CAACiD,OAAO;QAC5Bf,aAAa,EAAE3F,eAAe,EAAE;QAChC4F,eAAe,EAAEnC,OAAO,CAACkD,QAAQ;QACjCd,mBAAmB,EAAEkC,KAAI,CAACvG,cAAc,CAACoF,iBAAiB,CAACvE,IAAI,CAAC;QAChEyD,oBAAoB,EAAEiC,KAAI,CAACxG,WAAW,CAACsF,YAAY,CAACxE,IAAI,EAAEyE,SAAS,CAAC;QACpEf,iBAAiB,EAAEtC,OAAO,CAACsD,iBAAiB,IAAItD,OAAO,CAACuD,YAAY;QACpEhB,kBAAkB,EAAE,KAAK;QACzBC,aAAa,EAAEK;OAChB;MAED,IAAI,CAAC+B,YAAY,CAACpB,MAAM,EAAE;QACxB;QACA,MAAMC,WAAW,GAAG,CAACa,KAAI,CAAClG,WAAW,EAAE,CAACsF,OAAO,EAAE,GAAGY,KAAI,CAAClG,WAAW,EAAE,CAACuF,iBAAiB,CAAC;QACzF,MAAMK,YAAY,GAA8B,EAAE;QAElDP,WAAW,CAACI,OAAO,CAACC,QAAQ,IAAG;UAC7B,IAAIA,QAAQ,CAACC,SAAS,KAAK/D,OAAO,CAACkD,QAAQ,EAAE;YAC3Cc,YAAY,CAACF,QAAQ,CAACC,SAAS,CAAC,GAAG,CAAC;UACtC;QACF,CAAC,CAAC;QAEFf,UAAU,CAACgB,YAAY,GAAGA,YAAY;QACtCS,KAAK,CAACR,GAAG,CAAC9D,OAAO,CAACmB,GAAG,EAAE0B,UAAU,CAAC;MACpC,CAAC,MAAM;QACL;QACA,MAAM6B,YAAY,GAAGD,YAAY,CAACpE,IAAI,EAAE;QACxC,MAAMoD,mBAAmB,GAAG;UAAE,GAAGiB,YAAY,EAAEb;QAAY,CAAE;QAE7Dc,MAAM,CAACC,IAAI,CAACnB,mBAAmB,CAAC,CAACC,OAAO,CAAC3C,MAAM,IAAG;UAChD,IAAIA,MAAM,KAAKlB,OAAO,CAACkD,QAAQ,EAAE;YAC/BU,mBAAmB,CAAC1C,MAAM,CAAC,GAAG,CAAC0C,mBAAmB,CAAC1C,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;UACtE;QACF,CAAC,CAAC;QAEF8B,UAAU,CAACgB,YAAY,GAAGJ,mBAAmB;QAC7Ca,KAAK,CAACP,MAAM,CAAC/D,OAAO,CAACmB,GAAG,EAAE0B,UAAU,CAAC;MACvC;MAEA;MACA,MAAMgC,cAAc,SAASpI,cAAc,CAAC4H,iBAAiB,CAAC1B,GAAG,EAAE,CAAC;MACpEkC,cAAc,CAACnB,OAAO,CAACzD,GAAG,IAAG;QAC3B,MAAM6E,OAAO,GAAG7E,GAAG,CAACI,IAAI,EAAS,CAAC,CAAG;QACrC,MAAM0E,UAAU,GAAGD,OAAO,CAACC,UAAU,IAAI,EAAE,CAAC,CAAC;QAE7C,IAAI,CAACA,UAAU,CAACtG,IAAI,CAACmF,SAAS,CAAC,EAAE;UAC/BmB,UAAU,CAACtG,IAAI,CAACmF,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC;UACnCU,KAAK,CAACP,MAAM,CAAC9D,GAAG,CAACkB,GAAG,EAAE;YAAE4D;UAAU,CAAE,CAAC,CAAC,CAAC;QACzC;MACF,CAAC,CAAC;MAIF;MACA,MAAMT,KAAK,CAACU,MAAM,EAAE;IAAC;EACvB;EAGQf,iBAAiBA,CAACjE,OAAsC,EAC9DiF,cAAuB,EAAExG,IAAS,EAAEyG,oBAAoB,GAAG,KAAK,EAAExC,SAAS,GAAG,EAAE;IAEhF,MAAMD,UAAU,GAAGzC,OAAO,CAAC1B,UAAU,CAAC,UAAU,CAAC,CAAC2B,GAAG,EAAE;IACvD,MAAM4C,UAAU,GAAQ;MACtB5B,WAAW,EAAEgE,cAAc,CAACnC,OAAO;MACnCf,aAAa,EAAE3F,eAAe,EAAE;MAChC4F,eAAe,EAAEiD,cAAc,CAAClC,QAAQ;MACxCb,oBAAoB,EAAE,IAAI,CAACvE,WAAW,CAACsF,YAAY,CAACxE,IAAI,EAAEyE,SAAS,CAAC;MACpEjB,mBAAmB,EAAE,IAAI,CAACrE,cAAc,CAACoF,iBAAiB,CAACvE,IAAI,CAAC;MAChE0D,iBAAiB,EAAE8C,cAAc,CAAC9B,iBAAiB,IAAI8B,cAAc,CAAC7B,YAAY;MAClFhB,kBAAkB,EAAE8C,oBAAoB;MACxC7C,aAAa,EAAEK;KAChB;IAED,OAAOjG,cAAc,CAACuD,OAAO,CAAC2C,GAAG,EAAE,CAAC,CAACzD,IAAI,CAAC0D,QAAQ,IAAG;MACnD9D,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAACd,WAAW,EAAE,CAAC;MAC/C,MAAMwF,mBAAmB,GAAQ,EAAE;MACnC;MACA,MAAMH,WAAW,GAAG,CAAC,IAAI,CAACrF,WAAW,EAAE,CAACsF,OAAO,EAAE,GAAG,IAAI,CAACtF,WAAW,EAAE,CAACuF,iBAAiB,CAAC;MAEzFF,WAAW,CAACI,OAAO,CAAEC,QAAa,IAAI;QACpC,IAAIA,QAAQ,CAACC,SAAS,KAAKqB,cAAc,CAAClC,QAAQ,EAAE;UAClDU,mBAAmB,CAACE,QAAQ,CAACC,SAAS,CAAC,GAAG,CAAChB,QAAS,CAACS,MAAM,GAAIT,QAAS,CAACvC,IAAI,EAAU,CAACwD,YAAY,GAAGF,QAAQ,CAACC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC;QAC1I;MACF,CAAC,CAAC;MAEFf,UAAU,CAACgB,YAAY,GAAGJ,mBAAmB;MAC7C,OAAOzD,OAAO,CAAC+D,MAAM,CAAClB,UAAU,CAAC;IACnC,CAAC,CAAC;EACJ;EAEAsC,WAAWA,CAACpF,WAAmB,EAAE2C,SAAiB,EAAEuC,cAAuB,EAAExG,IAAS,EAAEwF,iBAAiB,GAAG,KAAK;IAC/G,MAAMjE,OAAO,GAAG,IAAI,CAACrB,QAAQ,CAACsB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IACnD,MAAM0C,UAAU,GAAGzC,OAAO,CAAC1B,UAAU,CAAC,UAAU,CAAC,CAAC2B,GAAG,CAAC,GAAGyC,SAAS,EAAE,CAAC;IACrE,MAAM0C,WAAW,GAAGpF,OAAO,CAAC1B,UAAU,CAAC,UAAU,EAAE6C,GAAG,IAAIA,GAAG,CAACE,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAACL,KAAK,CAAC,CAAC,CAAC,CAAC;IAEpG,MAAMqE,cAAc,GAAGD,WAAW,CAACjE,GAAG,CAAClB,GAAG,EAAE;IAC5C,MAAMoC,aAAa,GAAGgD,cAAc,CAAC9E,EAAE;IACvCzB,OAAO,CAACC,GAAG,CAACsD,aAAa,CAAC;IAC1B,OAAOI,UAAU,CAACE,GAAG,EAAE,CAAC2C,SAAS,EAAE,CAACpG,IAAI,CAAC0D,QAAQ,IAAG;MAClD,IAAIA,QAAS,CAACS,MAAM,EAAE;QAGpB,OAAOZ,UAAU,CAACsB,MAAM,CAAC;UACvB,GAAGkB;SACJ,CAAC,CAAC/F,IAAI,CAAC,MAAK;UACX,IAAI+E,iBAAiB,EAAE;YACrB,OAAO,IAAI,CAACA,iBAAiB,CAACjE,OAAO,EAAEiF,cAAc,EAAExG,IAAI,EAAE,KAAK,EAAEwG,cAAc,CAAC1E,EAAG,CAAC;UACzF;UACA,OAAOgF,OAAO,CAACC,OAAO,EAAE;QAC1B,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;MAC3C;IACF,CAAC,CAAC;EACJ;EAEAC,iCAAiCA,CAAC3F,WAAmB,EAAEgB,MAAc;IACnE,MAAMf,OAAO,GAAG,IAAI,CAACrB,QAAQ,CAACsB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IAEnD,OAAOC,OAAO,CAAC2F,YAAY,EAAE,CAACxF,IAAI,CAChCvD,GAAG,CAAC6D,OAAO,IAAI,IAAI,CAACC,YAAY,CAACD,OAAO,EAAEoD,YAAY,GAAG9C,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IAAE;IACzErE,GAAG,CAAC2D,IAAI,IAAG;MACT,MAAMwD,YAAY,GAAIxD,IAAY,EAAEwD,YAAY;MAChD,OAAOA,YAAY,GAAGA,YAAY,CAAC9C,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;IACrD,CAAC,CAAC,CACH;EACH;EAEA6E,sBAAsBA,CAAC7F,WAAmB,EAAEgB,MAAc;IACxD,MAAMf,OAAO,GAAG,IAAI,CAACrB,QAAQ,CAACsB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IACnD,OAAO,IAAI,CAAC8F,mCAAmC,CAAC7F,OAAO,EAAEe,MAAM,CAAC;EAClE;EAEA8E,mCAAmCA,CAACC,mBAAkD,EAAE/E,MAAc;IACpG,OAAO+E,mBAAmB,CAACnD,GAAG,EAAE,CAAC2C,SAAS,EAAE,CAACpG,IAAI,CAAC0D,QAAQ,IAAG;MAC3D,IAAIA,QAAS,CAACS,MAAM,EAAE;QACpB,MAAMhD,IAAI,GAAGuC,QAAS,CAACvC,IAAI,EAAS;QACpC,MAAM0F,YAAY,GAAG1F,IAAI,CAACwD,YAAY,GAAG9C,MAAM,CAAC,IAAI,CAAC;QAErD;QACA,IAAIgF,YAAY,GAAG,CAAC,EAAE;UACpB,MAAMtC,mBAAmB,GAAG;YAAE,GAAGpD,IAAI,CAACwD,YAAY;YAAE,CAAC9C,MAAM,GAAG;UAAC,CAAE;UACjE,OAAO+E,mBAAmB,CAAC/B,MAAM,CAAC;YAAEF,YAAY,EAAEJ;UAAmB,CAAE,CAAC;QAC1E;MACF;MACA;MACA,OAAO8B,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC,CAAC,CAAClG,KAAK,CAACC,KAAK,IAAG;MACfT,OAAO,CAACS,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,OAAOgG,OAAO,CAACS,MAAM,CAACzG,KAAK,CAAC;IAC9B,CAAC,CAAC;EACJ;EAEA;EACM0G,aAAaA,CAAClG,WAAmB,EAAEF,OAAY,EAAEpB,IAAS,EAAEwF,iBAAiB,GAAG,KAAK;IAAA,IAAAiC,MAAA;IAAA,OAAA9B,iBAAA;MACzFtF,OAAO,CAACC,GAAG,CAACgB,WAAW,CAAC;MACxBjB,OAAO,CAACC,GAAG,CAACc,OAAO,CAAC;MAEpB,MAAMG,OAAO,GAAGkG,MAAI,CAACvH,QAAQ,CAACsB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;MACnD,MAAM0C,UAAU,GAAGzC,OAAO,CAAC1B,UAAU,CAAC,UAAU,CAAC,CAAC2B,GAAG,CAACJ,OAAO,CAACU,EAAE,CAAC;MAEjE;MACA,MAAM+D,KAAK,GAAG4B,MAAI,CAAC1I,EAAE,CAAC+G,SAAS,CAACD,KAAK,EAAE;MAEvC;MACAA,KAAK,CAAC6B,MAAM,CAAC1D,UAAU,CAACtB,GAAG,CAAC;MAE5B,IAAI8C,iBAAiB,EAAE;QACrB,MAAMmC,aAAa,SAASF,MAAI,CAACG,gBAAgB,CAACtG,WAAW,CAAC;QAC9D,MAAM8C,UAAU,GAAG;UACjB,GAAGhD,OAAO;UACViD,OAAO,EAAE;SACV;QAED,MAAMoD,MAAI,CAACI,wBAAwB,CAACtG,OAAO,EAAE6C,UAAU,EAAEpE,IAAI,EAAE,IAAI,EAAE2H,aAAa,EAAE7F,EAAE,EAAE+D,KAAK,CAAC;MAChG;MAEA;MACA,MAAMA,KAAK,CAACU,MAAM,EAAE;MAEpB;MACA,IAAI,CAACf,iBAAiB,EAAE;QACtB,MAAMiC,MAAI,CAAClC,qBAAqB,CAAChE,OAAO,EAAED,WAAW,CAAC;MACxD;IAAC;EACH;EAEcuG,wBAAwBA,CACpCtG,OAAsC,EACtCiF,cAAmB,EACnBxG,IAAS,EACTyG,oBAA6B,EAC7BxC,SAA6B,EAC7B4B,KAAU;IAAA,IAAAiC,MAAA;IAAA,OAAAnC,iBAAA;MAEV,MAAMvB,UAAU,GAAQ;QACtB5B,WAAW,EAAEgE,cAAc,CAACnC,OAAO;QACnCf,aAAa,EAAE3F,eAAe,EAAE;QAChC4F,eAAe,EAAEiD,cAAc,CAAClC,QAAQ;QACxCb,oBAAoB,EAAEqE,MAAI,CAAC5I,WAAW,CAACsF,YAAY,CAACxE,IAAI,EAAEyE,SAAS,CAAC;QACpEjB,mBAAmB,EAAEsE,MAAI,CAAC3I,cAAc,CAACoF,iBAAiB,CAACvE,IAAI,CAAC;QAChE0D,iBAAiB,EAAE8C,cAAc,CAAC9B,iBAAiB,IAAI8B,cAAc,CAAC7B,YAAY;QAClFhB,kBAAkB,EAAE8C,oBAAoB;QACxC7C,aAAa,EAAEK;OAChB;MAED;MACA,MAAME,QAAQ,SAASnG,cAAc,CAACuD,OAAO,CAAC2C,GAAG,EAAE,CAAC;MAEpD;MACA,MAAMc,mBAAmB,GAAQ,EAAE;MACnC,MAAMH,WAAW,GAAG,CAACiD,MAAI,CAACtI,WAAW,EAAE,CAACsF,OAAO,EAAE,GAAGgD,MAAI,CAACtI,WAAW,EAAE,CAACuF,iBAAiB,CAAC;MACzF,MAAMgD,mBAAmB,GAAG5D,QAAQ,CAACS,MAAM,GAAGT,QAAQ,CAACvC,IAAI,EAAE,EAAEwD,YAAY,IAAI,EAAE,GAAG,EAAE;MAEtFP,WAAW,CAACI,OAAO,CAAEC,QAAa,IAAI;QACpC,IAAIA,QAAQ,CAACC,SAAS,KAAKqB,cAAc,CAAClC,QAAQ,EAAE;UAClDU,mBAAmB,CAACE,QAAQ,CAACC,SAAS,CAAC,GAAG,CAAC4C,mBAAmB,CAAC7C,QAAQ,CAACC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAChG;MACF,CAAC,CAAC;MAEF;MACA,IAAIe,MAAM,CAACC,IAAI,CAACnB,mBAAmB,CAAC,CAAC9C,MAAM,GAAG,CAAC,EAAE;QAC/CkC,UAAU,CAACgB,YAAY,GAAGJ,mBAAmB;MAC/C;MAEA;MACAa,KAAK,CAACP,MAAM,CAAC/D,OAAO,CAACmB,GAAG,EAAE0B,UAAU,CAAC;IAAC;EACxC;EAEQmB,qBAAqBA,CAAChE,OAAsC,EAAED,WAAmB;IACvF,OAAO,IAAI,CAACsG,gBAAgB,CAACtG,WAAW,CAAC,CAACb,IAAI,CAAE0D,QAAoB,IAAI;MAEtE9D,OAAO,CAACC,GAAG,CAAC6D,QAAQ,CAAC;MACrB,IAAIA,QAAQ,EAAE;QACZ,MAAMwD,aAAa,GAAGxD,QAAQ;QAC9B,OAAO5C,OAAO,CAAC+D,MAAM,CAAC;UACpB9C,WAAW,EAAEmF,aAAa,CAACtD,OAAO;UAClCf,aAAa,EAAEqE,aAAa,CAACvE,SAAS;UACtCG,eAAe,EAAEoE,aAAa,CAACrD,QAAQ;UACvCd,mBAAmB,EAAE,IAAI,CAACrE,cAAc,CAACoF,iBAAiB,CAAC,IAAI,CAACvE,IAAI,CAAC;UACrEyD,oBAAoB,EAAEkE,aAAa,CAACK,WAAW;UAC/CtE,iBAAiB,EAAEiE,aAAa,CAACjD,iBAAiB,IAAIiD,aAAa,CAAChD,YAAY;UAChFhB,kBAAkB,EAAE,KAAK;UACzBC,aAAa,EAAE+D,aAAa,CAAC7F;SAC9B,CAAC;MACJ,CAAC,MAAM;QACL;QACA,OAAOP,OAAO,CAAC+D,MAAM,CAAC;UACpB9C,WAAW,EAAE,EAAE;UACfc,aAAa,EAAE,IAAI;UACnBC,eAAe,EAAE,EAAE;UACnBC,mBAAmB,EAAE,EAAE;UACvBC,oBAAoB,EAAE,EAAE;UACxBC,iBAAiB,EAAE,KAAK;UACxBC,kBAAkB,EAAE,KAAK;UACzBC,aAAa,EAAE;SAChB,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EAEAgE,gBAAgBA,CAACtG,WAAmB;IAClC,MAAMC,OAAO,GAAG,IAAI,CAACrB,QAAQ,CAACsB,GAAG,CAAC,GAAGF,WAAW,EAAE,CAAC;IACnD,OAAOtD,cAAc,CAACuD,OAAO,CAAC1B,UAAU,CAAC,UAAU,EAAE6C,GAAG,IAAIA,GAAG,CAACE,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAACL,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC2B,GAAG,EAAE,CAAC,CAACzD,IAAI,CAAC0D,QAAQ,IAAG;MAC5H,IAAI,CAACA,QAAQ,CAAC8D,KAAK,EAAE;QACnB,MAAMN,aAAa,GAAGxD,QAAQ,CAAC+D,IAAI,CAAC,CAAC,CAAC,CAACtG,IAAI,EAAa;QACxD,MAAMuG,YAAY,GAAGhE,QAAQ,CAACiE,IAAI,CAAC,CAAC;QACpC,IAAI,CAACnG,YAAY,CAACkG,YAAY,CAAC;QAE/B,OAAOR,aAAa;MACtB,CAAC,MAAM;QACL,OAAO,IAAI,CAAC,CAAC;MACf;IACF,CAAC,CAAC;EACJ;EAEA;EACAU,cAAcA,CAAC/G,WAAmB,EAAEM,IAAmB;IACrD,OAAO,IAAI,CAAC1B,QAAQ,CAACsB,GAAG,CAACF,WAAW,CAAC,CAACgE,MAAM,CAAC1D,IAAI,CAAC;EACpD;EAEA;EACA0G,cAAcA,CAAChH,WAAmB;IAChC,OAAO,IAAI,CAACpB,QAAQ,CAACsB,GAAG,CAACF,WAAW,CAAC,CAACoG,MAAM,EAAE;EAChD;EAEAa,gBAAgBA,CAACjH,WAAmB,EAAEF,OAAgB;IACpD,MAAM4C,UAAU,GAAG,IAAI,CAACjF,EAAE,CAACc,UAAU,CAAC,GAAG,IAAI,CAACF,YAAY,IAAI2B,WAAW,WAAW,CAAC,CAACE,GAAG,CAACJ,OAAO,CAACU,EAAE,CAAC;IAErG;IACA,MAAM0G,iBAAiB,GAAG;MACxB;MACA,CAACpH,OAAO,CAACkD,QAAQ,GAAG,KAAK,CAAE;MAC3B;KACD;IAED,OAAON,UAAU,CAACqB,GAAG,CAAC;MACpBiB,UAAU,EAAEkC,iBAAiB;MAAE;MAC/B1F,SAAS,EAAEnF,eAAe,EAAE,CAAE;KAC/B,CAAC;EACJ;EAEAqF,kBAAkBA,CAAC1B,WAAmB,EAAEgB,MAAc;IACpD,OAAO,IAAI,CAACvD,EAAE,CAACc,UAAU,CAAC,GAAG,IAAI,CAACF,YAAY,IAAI2B,WAAW,WAAW,CAAC,CAAC4C,GAAG,EAAE,CAAC2C,SAAS,EAAE,CAACpG,IAAI,CAACgI,aAAa,IAAG;MAC/G,MAAM5C,KAAK,GAAG,IAAI,CAAC9G,EAAE,CAAC+G,SAAS,CAACD,KAAK,EAAE;MACvC,IAAI6C,UAAU,GAAG,KAAK,CAAC,CAAC;MAExBD,aAAc,CAACxD,OAAO,CAACzD,GAAG,IAAG;QAC3B,MAAMI,IAAI,GAAGJ,GAAG,CAACI,IAAI,EAAE;QAEvB;QACA,MAAM+G,gBAAgB,GAAI/G,IAAY,CAAC0E,UAAU,IAAI,EAAE;QAEvD;QACA,IAAI,CAACqC,gBAAgB,CAACrG,MAAM,CAAC,EAAE;UAC7BqG,gBAAgB,CAACrG,MAAM,CAAC,GAAG,IAAI;UAC/B;UACAuD,KAAK,CAACP,MAAM,CAAC9D,GAAG,CAACkB,GAAG,EAAE;YAAE4D,UAAU,EAAEqC;UAAgB,CAAE,CAAC;UACvDD,UAAU,GAAG,IAAI,CAAC,CAAC;QACrB;MACF,CAAC,CAAC;MAEF;MACA,IAAIA,UAAU,EAAE;QACd,OAAO7C,KAAK,CAACU,MAAM,EAAE;MACvB;MAEA;MACA,OAAOO,OAAO,CAACC,OAAO,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA;EACA6B,qBAAqBA,CAACtH,WAAmB;IACvC,OAAO,IAAI,CAACvC,EAAE,CAACc,UAAU,CAAC,GAAG,IAAI,CAACF,YAAY,IAAI2B,WAAW,WAAW,CAAC,CAAC4C,GAAG,EAAE,CAAC2C,SAAS,EAAE,CAACpG,IAAI,CAACgI,aAAa,IAAG;MAC/G,MAAM5C,KAAK,GAAG,IAAI,CAAC9G,EAAE,CAAC+G,SAAS,CAACD,KAAK,EAAE;MACvC4C,aAAc,CAACxD,OAAO,CAACzD,GAAG,IAAG;QAC3B,MAAMuE,WAAW,GAAGvE,GAAG,CAACI,IAAI,EAAa;QACzC,MAAMiH,iBAAiB,GAAG,EAAE;QAE5B;QACA,KAAK,MAAMvG,MAAM,IAAIyD,WAAW,CAACO,UAAU,EAAE;UAC3C,IAAIP,WAAW,CAACO,UAAU,CAACwC,cAAc,CAACxG,MAAM,CAAC,EAAE;YACjD,MAAMH,CAAC,GAAG,cAAcG,MAAM,EAAE;YAC/BuG,iBAAyB,CAAC1G,CAAC,CAAC,GAAG,IAAI;UACtC;QACF;QAEA0D,KAAK,CAACP,MAAM,CAAC9D,GAAG,CAACkB,GAAG,EAAEmG,iBAAiB,CAAC;MAC1C,CAAC,CAAC;MACF,OAAOhD,KAAK,CAACU,MAAM,EAAE;IACvB,CAAC,CAAC;EACJ;EAEA;EACAwC,sBAAsBA,CAACzH,WAAmB,EAAEgB,MAAc;IACxD,OAAO,IAAI,CAACvD,EAAE,CAACc,UAAU,CAAU,GAAG,IAAI,CAACF,YAAY,IAAI2B,WAAW,WAAW,CAAC,CAACG,eAAe,EAAE,CAACC,IAAI,CACvGvD,GAAG,CAAC6D,OAAO,IAAI,IAAI,CAACC,YAAY,CAACD,OAAO,CAACE,MAAM,CAAC,CAAC;IAAE;IACnDjE,GAAG,CAAC+D,OAAO,IAAIA,OAAO,CAACgH,MAAM,CAACrH,MAAM,IAAG;MACrC,MAAMC,IAAI,GAAGD,MAAM,CAACE,OAAO,CAACL,GAAG,CAACI,IAAI,EAAa;MACjD,OAAO,CAACA,IAAI,CAAC0E,UAAU,CAACwC,cAAc,CAACxG,MAAM,CAAC;IAChD,CAAC,CAAC,CAACJ,MAAM,CAAC,CACX;EACH;EAGA;EACA+G,6BAA6BA,CAAC3G,MAAc;IAC1C,OAAO,IAAI,CAACpC,QAAQ,CAACuB,eAAe,EAAE,CAACC,IAAI,CACzCvD,GAAG,CAAC6D,OAAO,IAAI,IAAI,CAACC,YAAY,CAACD,OAAO,CAACE,MAAM,CAAC,CAAC;IAAE;IACnDhE,SAAS,CAACgL,aAAa,IAAG;MACxB,MAAMC,eAAe,GAAGD,aAAa,CAACjL,GAAG,CAACkG,QAAQ,IAAG;QACnD,MAAM9B,MAAM,GAAG8B,QAAQ,CAACtC,OAAO,CAACL,GAAG,CAACM,EAAE;QACtC,OAAO,IAAI,CAACiH,sBAAsB,CAAC1G,MAAM,EAAEC,MAAM,CAAC;MACpD,CAAC,CAAC;MACF,OAAOvE,aAAa,CAACoL,eAAe,CAAC;IACvC,CAAC,CAAC,EACFlL,GAAG,CAACmH,YAAY,IAAIA,YAAY,CAACgE,MAAM,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAKD,GAAG,GAAGC,KAAK,EAAE,CAAC,CAAC,CAAC,CACzE;EACH;EAEAC,kCAAkCA,CAACjH,MAAc;IAC/C,OAAO,IAAI,CAACpC,QAAQ,CAACuB,eAAe,EAAE,CAACC,IAAI,CACzCvD,GAAG,CAAC6D,OAAO,IAAI,IAAI,CAACC,YAAY,CAACD,OAAO,CAACE,MAAM,CAAC,CAAC;IAAE;IACnDhE,SAAS,CAACgL,aAAa,IAAG;MACxB,MAAMM,OAAO,GAAGN,aAAa,CAACjL,GAAG,CAACkG,QAAQ,IAAIA,QAAQ,CAACtC,OAAO,CAACL,GAAG,CAACM,EAAE,CAAC;MACtE;MACA,MAAM2H,wBAAwB,GAAGD,OAAO,CAACvL,GAAG,CAACqD,WAAW,IACtD,IAAI,CAACpB,QAAQ,CAACsB,GAAG,CAACF,WAAW,CAAC,CAACzB,UAAU,CAAU,UAAU,CAAC,CAAC4B,eAAe,EAAE,CAC7EC,IAAI,CACHzD,GAAG,CAAC+D,OAAO,IAAIA,OAAO,CAACgH,MAAM,CAACrH,MAAM,IAAG;QACrC,MAAMC,IAAI,GAAGD,MAAM,CAACE,OAAO,CAACL,GAAG,CAACI,IAAI,EAAa;QACjD,OAAO,CAACA,IAAI,CAAC0E,UAAU,CAACwC,cAAc,CAACxG,MAAM,CAAC;MAChD,CAAC,CAAC,CAACJ,MAAM,CAAC,CACX,CACJ;MAED;MACA,OAAOnE,aAAa,CAAC0L,wBAAwB,CAAC;IAChD,CAAC,CAAC,EACFxL,GAAG,CAACmH,YAAY,IAAIA,YAAY,CAACgE,MAAM,CAAC,CAACM,KAAK,EAAEJ,KAAK,KAAKI,KAAK,GAAGJ,KAAK,EAAE,CAAC,CAAC,CAAC,CAC7E;EACH;EAEA;EACAK,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC3K,MAAM,CAAC4K,SAAS;EAC9B;EAEAC,aAAaA,CAACvH,MAAc,EAAEwH,MAA4B;IACxD,MAAMC,OAAO,GAAG,IAAI,CAACnK,QAAQ,CAAC4B,GAAG,CAACc,MAAM,CAAC;IACzC,OAAOyH,OAAO,CAAC1E,GAAG,CAAC;MAAEyE,MAAM;MAAEE,cAAc,EAAEpM,SAAS,CAACqM,GAAG;IAAE,CAAE,EAAE;MAAEC,KAAK,EAAE;IAAI,CAAE,CAAC;EAClF;EAEAC,qBAAqBA,CAAC7H,MAAc,EAAE8H,IAAgB;IACpD,MAAML,OAAO,GAAG,IAAI,CAACnK,QAAQ,CAAC4B,GAAG,CAACc,MAAM,CAAC;IACzC,OAAOyH,OAAO,CAAC1E,GAAG,CAAC;MAAEyE,MAAM,EAAE,QAAQ;MAAEE,cAAc,EAAEI;IAAI,CAAE,EAAE;MAAEF,KAAK,EAAE;IAAI,CAAE,CAAC;EACjF;EAEAG,aAAaA,CAAC/H,MAAc;IAC1B,OAAO,IAAI,CAAC1C,QAAQ,CAAC4B,GAAG,CAAiBc,MAAM,CAAC,CAAC4E,YAAY,EAAE,CAACxF,IAAI,CAClEzD,GAAG,CAAC+B,IAAI,IAAIA,IAAI,EAAE8J,MAAM,IAAI,SAAS,CAAC,CACvC;EACH;EAEA;;;;;;;EAOAQ,sCAAsCA,CAAA;IACpC,OAAO,IAAI,CAACvL,EAAE,CAACc,UAAU,CAAiB,QAAQ,CAAC,CAChD4B,eAAe,EAAE,CAACC,IAAI,CACrBvD,GAAG,CAAC6D,OAAO,IAAI,IAAI,CAACC,YAAY,CAACD,OAAO,CAACE,MAAM,CAAC,CAAC;IAAE;IACnDjE,GAAG,CAAC+D,OAAO,IAAG;MACZ,MAAMuI,WAAW,GAAG3M,SAAS,CAACqM,GAAG,EAAE;MACnC,OAAOjI,OAAO,CAAC/D,GAAG,CAACkE,CAAC,IAAG;QACrB,MAAMP,IAAI,GAAGO,CAAC,CAACN,OAAO,CAACL,GAAG,CAACI,IAAI,EAAoB;QACnD,MAAME,EAAE,GAAGK,CAAC,CAACN,OAAO,CAACL,GAAG,CAACM,EAAE;QAC3B,MAAMkI,cAAc,GAAGpI,IAAI,CAACoI,cAA2B;QACvD,IAAIA,cAAc,EAAE;UAClB,IAAI,CAACO,WAAW,CAACC,OAAO,IAAI,CAACR,cAAc,CAACQ,OAAO,EAAE;YACnD,OAAO;cAAE,GAAG5I,IAAI;cAAEE,EAAE;cAAEgI,MAAM,EAAE;YAAS,CAAE;UAC3C;UACA,MAAMW,cAAc,GAAGF,WAAW,EAAEC,OAAO,GAAGR,cAAc,EAAEQ,OAAO;UAErE;UACA,IAAIC,cAAc,IAAI,IAAI,CAAC1K,0BAA0B,EAAE;YACrD,OAAO;cAAE,GAAG6B,IAAI;cAAEE,EAAE;cAAEgI,MAAM,EAAE;YAAQ,CAAE;UAC1C,CAAC,MAAM;YACL,OAAO;cAAE,GAAGlI,IAAI;cAAEE,EAAE;cAAEgI,MAAM,EAAE;YAAS,CAAE;UAC3C;QACF;QACA,OAAO;UAAE,GAAGlI,IAAI;UAAEE,EAAE;UAAEgI,MAAM,EAAE;QAAS,CAAE;MAE3C,CAAC,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EAEA;EACAY,gCAAgCA,CAACpJ,WAAmB;IAClD,MAAMC,OAAO,GAAG,IAAI,CAACrB,QAAQ,CAACsB,GAAG,CAACF,WAAW,CAAC,CAACzB,UAAU,CAAO,OAAO,CAAC;IACxE,MAAM8K,YAAY,GAAG,IAAI,CAAC5B,sBAAsB,CAACzH,WAAW,EAAE,IAAI,CAACtB,IAAI,EAAE4K,GAAG,CAAC,CAAC,CAAC;IAE/E,OAAO7M,aAAa,CAAC,CACnBwD,OAAO,CAACE,eAAe,EAAE,CAACC,IAAI,CAC5BzD,GAAG,CAAC+D,OAAO,IAAIA,OAAO,CAAC/D,GAAG,CAACkE,CAAC,IAAG;MAC7B,MAAMP,IAAI,GAAGO,CAAC,CAACN,OAAO,CAACL,GAAG,CAACI,IAAI,EAAU;MACzC,MAAME,EAAE,GAAGK,CAAC,CAACN,OAAO,CAACL,GAAG,CAACM,EAAE;MAC3B,OAAO;QAAEA,EAAE;QAAE,GAAGF;MAAI,CAAE;IACxB,CAAC,CAAC,CAAC,CACJ,EACD+I,YAAY,CACb,CAAC,CAACjJ,IAAI,CACLzD,GAAG,CAAC,CAAC,CAAC4M,KAAK,EAAEtL,WAAW,CAAC,MAAM;MAAEsL,KAAK;MAAEtL;IAAW,CAAE,CAAC,CAAC,CACxD;EACH;EAEA;EACAuL,8BAA8BA,CAACzI,MAAc,EAAEC,MAAc,EAAEC,KAAa;IAC1E,MAAME,aAAa,GAAG,IAAI,CAAC1D,EAAE,CAACc,UAAU,CAAU,GAAG,IAAI,CAACH,MAAM,IAAI2C,MAAM,WAAW,EAAEK,GAAG,IACxFA,GAAG,CAACE,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,CAACL,KAAK,CAACA,KAAK,CAAC,CAC9C;IAED,MAAMoI,YAAY,GAAG,IAAI,CAAC5B,sBAAsB,CAAC1G,MAAM,EAAEC,MAAM,CAAC;IAEhE,OAAOvE,aAAa,CAAC,CACnB0E,aAAa,CAAChB,eAAe,EAAE,CAACC,IAAI,CAClCzD,GAAG,CAAC+D,OAAO,IAAIA,OAAO,CAAC/D,GAAG,CAACkE,CAAC,IAAG;MAC7B,MAAMP,IAAI,GAAGO,CAAC,CAACN,OAAO,CAACL,GAAG,CAACI,IAAI,EAAa;MAC5C,MAAME,EAAE,GAAGK,CAAC,CAACN,OAAO,CAACL,GAAG,CAACM,EAAE;MAC3B,OAAO;QAAEA,EAAE;QAAE,GAAGF;MAAI,CAAE;IACxB,CAAC,CAAC,CAAC,CACJ,EACD+I,YAAY,CACb,CAAC,CAACjJ,IAAI,CACLzD,GAAG,CAAC,CAAC,CAAC8E,QAAQ,EAAExD,WAAW,CAAC,MAAM;MAAEwD,QAAQ;MAAExD;IAAW,CAAE,CAAC,CAAC,CAC9D;EACH;EAEA;EACAwL,4BAA4BA,CAACzI,MAAc,EAAE0I,UAAkB;IAC7D,MAAMzJ,OAAO,GAAG,IAAI,CAACrB,QAAQ,CAACsB,GAAG,CAAC,GAAGwJ,UAAU,EAAE,CAAC,CAACnL,UAAU,CAAU,UAAU,CAAC;IAElF;IACA,MAAMoL,UAAU,GAAG,IAAIC,IAAI,EAAE;IAC7BD,UAAU,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACjC,MAAMC,QAAQ,GAAG,IAAIF,IAAI,EAAE;IAC3BE,QAAQ,CAACD,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;IAEpC;IACA,OAAO,IAAIrN,UAAU,CAAS0C,QAAQ,IAAG;MACvCe,OAAO,CAACmB,GAAG,CAAC2I,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE/I,MAAM,CAAC,CACxC+I,KAAK,CAAC,WAAW,EAAE,IAAI,EAAEJ,UAAU,CAAC,CACpCI,KAAK,CAAC,WAAW,EAAE,IAAI,EAAED,QAAQ,CAAC,CAClClH,GAAG,EAAE,CACLzD,IAAI,CAAC0D,QAAQ,IAAG;QACf3D,QAAQ,CAACG,IAAI,CAACwD,QAAQ,CAACiE,IAAI,CAAC,CAAC,CAAC;QAC9B5H,QAAQ,CAACI,QAAQ,EAAE,CAAC,CAAY;MAClC,CAAC,CAAC,CACDC,KAAK,CAACC,KAAK,IAAG;QACbN,QAAQ,CAACM,KAAK,CAACA,KAAK,CAAC,CAAC,CAAC;MACzB,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;EAEAmB,YAAYA,CAACqH,KAAa;IACxB,MAAMhC,YAAY,GAAGgE,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,IAAI,GAAG,EAAE,EAAE,CAAC;IAC3ED,YAAY,CAACE,OAAO,CAAC,WAAW,EAAE,CAACnE,YAAY,GAAGgC,KAAK,EAAEoC,QAAQ,EAAE,CAAC;EACtE;EAAC,QAAAC,CAAA,G;qBAnuBU9M,WAAW,EAAA+M,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAXvN,WAAW;IAAAwN,OAAA,EAAXxN,WAAW,CAAAyN,IAAA;IAAAC,UAAA,EAFV;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}