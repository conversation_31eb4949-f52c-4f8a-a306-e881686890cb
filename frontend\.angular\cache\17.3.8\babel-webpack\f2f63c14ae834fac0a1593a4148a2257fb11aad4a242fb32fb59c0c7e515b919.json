{"ast": null, "code": "import { inject, Injector, signal } from '@angular/core';\nimport { NavigationEnd, NavigationStart, RoutesRecognized } from '@angular/router';\nimport { combineLatest } from 'rxjs';\nimport { filter, map, mergeMap, switchMap, take, tap } from 'rxjs/operators';\nimport { AppLayoutType } from './core/models/general.model';\nimport { slideInAnimation } from './helpers/route-animation';\nimport { ChatService } from './core/services/chat.service';\nimport { toObservable } from '@angular/core/rxjs-interop';\nimport { TooltipModule } from 'primeng/tooltip';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"./core/services/general.service\";\nimport * as i4 from \"./core/services/auth.service\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"./core/services/classroom.service\";\nimport * as i7 from \"./core/services/user.service\";\nimport * as i8 from \"./core/services/layout.service\";\nimport * as i9 from \"./core/services/confirm-dialog.service\";\nimport * as i10 from \"./shared/prime/toast/toast.component\";\nimport * as i11 from \"./shared/prime/confirm-dialog/confirm-dialog.component\";\nimport * as i12 from \"./modules/chat/chat.component\";\nfunction AppComponent_app_confirm_dialog_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-confirm-dialog\", 2);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"rejectBtnIcon\", ctx_r0.confirmDialogService.rejectBtnIcon)(\"acceptBtnIcon\", ctx_r0.confirmDialogService.acceptBtnIcon)(\"confirmMessage\", ctx_r0.confirmDialogService.confirmMessage)(\"rejectBtnLabel\", ctx_r0.confirmDialogService.rejectBtnLabel)(\"acceptBtnLabel\", ctx_r0.confirmDialogService.acceptBtnLabel)(\"headerText\", ctx_r0.confirmDialogService.headerText)(\"acceptBtnImage\", ctx_r0.confirmDialogService.acceptBtnImage)(\"acceptBtnClass\", ctx_r0.confirmDialogService.acceptBtnClass)(\"rejectBtnClass\", ctx_r0.confirmDialogService.rejectBtnClass)(\"confirmIcon\", ctx_r0.confirmDialogService.confirmIcon)(\"showConfirmIcon\", ctx_r0.confirmDialogService.showConfirmIcon)(\"extraData\", ctx_r0.confirmDialogService.extraData);\n  }\n}\nfunction AppComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-chat\", 1);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"bubbleMode\", true);\n  }\n}\nexport class AppComponent {\n  constructor(router, location, generalService, authService, primengConfig, activatedRoute, classroomService, userService, layoutService, confirmDialogService) {\n    this.router = router;\n    this.location = location;\n    this.generalService = generalService;\n    this.authService = authService;\n    this.primengConfig = primengConfig;\n    this.activatedRoute = activatedRoute;\n    this.classroomService = classroomService;\n    this.userService = userService;\n    this.layoutService = layoutService;\n    this.confirmDialogService = confirmDialogService;\n    this.injector = inject(Injector);\n    this.title = 'frontend';\n    this.mt = 0;\n    this.routeIsDashboard = false;\n    this.routeIsProfile = false;\n    this.routeIsTeacher = false;\n    this.routeIsAuth = false;\n    this.hideLeftMenu = true;\n    this.oldValue = 0;\n    this.newValue = 0;\n    this.url = \"\";\n    this.showLeftMenu = false;\n    this.gradient = \"\";\n    this.routeData = {};\n    this.layout$ = this.getLayoutType$();\n    this.AppLayoutType = AppLayoutType;\n    this.previousUrl = '';\n    this.loggedIn = signal(false);\n    this.chatService = inject(ChatService);\n    this.user = this.authService.getLoggedInUser();\n    this.userClassrooms = [];\n  }\n  ngOnInit() {\n    this.primengConfig.ripple = true;\n    this.router.events.subscribe(evt => {\n      if (evt instanceof NavigationStart) {\n        window.scrollTo(0, 0);\n      }\n      if (evt instanceof NavigationEnd) {\n        this.previousUrl = this.router.url;\n        console.log(' url = this .router.url ', evt.url);\n        if (evt.url.includes('chat')) {\n          // this.chatService.showBubbleChat.set(false);\n        } else {\n          // this.chatService.showBubbleChat.set(true);\n        }\n        if (this.authService.getIsAuth()) {\n          if (this.authService.isStudent) {\n            this.handleStudentClassroomDataAndMenu();\n          } else {\n            this.layoutService.updateAllMenuItemsDisabledStatus(false);\n          }\n        }\n        if (this.previousUrl !== evt.url) {\n          console.log(evt.url);\n          window.scrollTo(0, 0);\n        }\n      }\n    });\n    setTimeout(() => {\n      [].forEach.call(document.getElementsByClassName('tawk-card-primary'), el => {\n        el.style.setProperty(\"backgroundColor\", \"black\", \"important\");\n        console.log(el);\n      });\n    }, 10000);\n    this.authService.autoAuthUser();\n    this.generalService.setDevice();\n    this.getLayoutType$();\n    this.layout$.subscribe(res => {\n      // console.log(res);\n    });\n    // this.getLayoutType$().subscribe((res)=>{\n    //   console.log(res);\n    // });\n    this.router.events.subscribe(data => {\n      if (data instanceof RoutesRecognized) {\n        // console.log(data.state.root.firstChild!.data);\n        this.routeData = data.state.root.firstChild.data;\n      }\n    });\n    this.authService.authStatusListener.subscribe(res => {\n      console.log(res);\n      this.loggedIn.set(res);\n      if (res) {}\n    });\n    this.listenLoggedInUser();\n  }\n  getLayoutType$() {\n    return this.router.events.pipe(filter(event => event instanceof RoutesRecognized), map(ev => {\n      // console.log(this.activatedRoute);\n      return this.activatedRoute;\n    }), map(route => {\n      while (route.firstChild) route = route.firstChild;\n      // console.log(route);\n      return route;\n    }), mergeMap(route => route.data), map(({\n      layout\n    }) => layout));\n  }\n  getTopLeftGradientClass() {\n    if (this.router.url.includes('dashboard')) {\n      this.routeIsDashboard = true;\n      return \"top-left-gradient\";\n    }\n    return \"top-left-gradient-big\";\n  }\n  checkOffset() {\n    // var leftMenu = document.getElementById('left-menu');\n    // var a = window.scrollY + leftMenu!?.clientHeight + document.getElementById('footer')!?.clientHeight + 100;\n    // var body = document.body;\n    // var html = document.documentElement;\n    // var height = Math.max(body.scrollHeight, body.offsetHeight, html.clientHeight, html.scrollHeight, html.offsetHeight);\n    // if (leftMenu !== null) {\n    //   if (a < height) {\n    //     leftMenu.style.position = \"fixed\";\n    //     leftMenu.style.top = html.clientHeight > 850 ? \"86px\" : \"70px\"; // see marginTop in .app-content class and check @media (max-height: 850px)\n    //     this.mt = window.scrollY;\n    //   } else {\n    //     leftMenu.style.position = \"absolute\";\n    //     leftMenu.style.top = this.mt + \"px\";\n    //   }\n    // }\n  }\n  onActivate(event) {\n    window.scrollTo(0, 0);\n  }\n  onWindowScroll($event) {\n    this.checkOffset();\n    this.newValue = window.pageYOffset;\n    this.generalService.gradientScale(this.newValue, this.oldValue, 'top-left-gradient');\n    this.oldValue = this.newValue;\n  }\n  onResize(event) {\n    this.generalService.setDevice();\n  }\n  backClicked() {\n    this.location.back();\n  }\n  addActiveClassToProfileInfoInSillyWay(data, url, id) {\n    // Sorry :( but routerLinkActive is not working in profile-menu.component.ts\n    if (data.url === url) {\n      setTimeout(() => {\n        document.getElementById(id)?.classList.add('item-active');\n      }, 100);\n    } else {\n      document.getElementById(id)?.classList.remove('item-active');\n    }\n  }\n  addActiveClassToTeacherInfoInSillyWay(data, url, id) {\n    // Sorry :( but routerLinkActive is not working in profile-menu.component.ts\n    if (data.url === url) {\n      setTimeout(() => {\n        document.getElementById(id)?.classList.add('item-active-teacher');\n        document.getElementById(id)?.classList.remove('inactive-item');\n      }, 100);\n    } else {\n      document.getElementById(id)?.classList.remove('item-active-teacher');\n      document.getElementById(id)?.classList.add('inactive-item');\n    }\n  }\n  handleStudentClassroomDataAndMenu() {\n    this.userService.getUserHasPayment().pipe(take(1), switchMap(res => {\n      return this.classroomService.getLMSUserClassrooms(this.authService.getLoggedInUser().id).pipe(tap(classrooms => {\n        this.classroomService.setUserClassrooms(classrooms);\n        const isStudentActive = this.classroomService.isStudentActive(classrooms, res);\n        this.layoutService.updateMenuItemDisabledStatus(\"Classrooms\", classrooms.length === 0);\n        this.layoutService.updateMenuItemDisabledStatus(\"Guides\", true);\n        if (isStudentActive) {\n          this.layoutService.updateAllMenuItemsDisabledStatus(false);\n        } else {\n          this.layoutService.updateMenuItemClassroomToTrialsForStudent(classrooms);\n        }\n        console.log(classrooms);\n      }), take(1));\n    })).subscribe();\n  }\n  listenChatFromClassrooms() {\n    this.classroomService.getLMSUserClassrooms(this.user.id).subscribe(res => {\n      let totalUnreadCount = 0; // Initialize a variable to accumulate the total unread count\n      console.log(res);\n      this.classroomService.setUserClassrooms(res);\n      const classroomObservables = res.map(element => {\n        return this.chatService.getUnreadMessagesCount(element.id, this.user.aspUserId).pipe(tap(count => {\n          console.log(`Unread messages count for classroom ${element.id} for user ${this.user.aspUserId}: ${count}`);\n        }));\n      });\n      combineLatest(classroomObservables).subscribe(counts => {\n        totalUnreadCount = counts.reduce((acc, count) => acc + count, 0);\n        this.chatService.unreadCount.set(totalUnreadCount); // Update the total count\n      });\n    });\n  }\n  listenLoggedInUser() {\n    toObservable(this.loggedIn, {\n      injector: this.injector\n    }).subscribe({\n      next: data => {\n        if (data) {\n          console.log(data);\n          console.log(\"🚀 ~ AppComponent ~ listenLoggedInUser ~ console:\", console);\n          // this.listenChatFromClassrooms();\n        }\n      }\n    });\n  }\n  static #_ = this.ɵfac = function AppComponent_Factory(t) {\n    return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.Location), i0.ɵɵdirectiveInject(i3.GeneralService), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i5.PrimeNGConfig), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i6.ClassroomService), i0.ɵɵdirectiveInject(i7.UserService), i0.ɵɵdirectiveInject(i8.LayoutService), i0.ɵɵdirectiveInject(i9.ConfirmDialogService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppComponent,\n    selectors: [[\"app-root\"]],\n    hostBindings: function AppComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"scroll\", function AppComponent_scroll_HostBindingHandler($event) {\n          return ctx.onWindowScroll($event);\n        }, false, i0.ɵɵresolveWindow)(\"resize\", function AppComponent_resize_HostBindingHandler($event) {\n          return ctx.onResize($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    features: [i0.ɵɵProvidersFeature([TooltipModule])],\n    decls: 5,\n    vars: 2,\n    consts: [[3, \"rejectBtnIcon\", \"acceptBtnIcon\", \"confirmMessage\", \"rejectBtnLabel\", \"acceptBtnLabel\", \"headerText\", \"acceptBtnImage\", \"acceptBtnClass\", \"rejectBtnClass\", \"confirmIcon\", \"showConfirmIcon\", \"extraData\", 4, \"ngIf\"], [3, \"bubbleMode\"], [3, \"rejectBtnIcon\", \"acceptBtnIcon\", \"confirmMessage\", \"rejectBtnLabel\", \"acceptBtnLabel\", \"headerText\", \"acceptBtnImage\", \"acceptBtnClass\", \"rejectBtnClass\", \"confirmIcon\", \"showConfirmIcon\", \"extraData\"]],\n    template: function AppComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"router-outlet\")(1, \"app-prime-toast\");\n        i0.ɵɵelementContainerStart(2);\n        i0.ɵɵtemplate(3, AppComponent_app_confirm_dialog_3_Template, 1, 12, \"app-confirm-dialog\", 0);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵtemplate(4, AppComponent_Conditional_4_Template, 1, 1, \"app-chat\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.confirmDialogService.isVisible);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(4, ctx.loggedIn() && ctx.chatService.isChatEnabled() ? 4 : -1);\n      }\n    },\n    dependencies: [i2.NgIf, i1.RouterOutlet, i10.ToastComponent, i11.ConfirmDialogComponent, i12.ChatComponent],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.app[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  justify-content: center;\\n  font-family: \\\"Proxima Nova Regular\\\";\\n  font-size: 20px;\\n  padding-top: 70px;\\n}\\n\\n.app-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  width: var(--app-content-width);\\n  margin: var(--content-margin);\\n  margin-top: 1rem;\\n}\\n\\n@media only screen and (max-width: 768px) {\\n  .app-content[_ngcontent-%COMP%] {\\n    margin-top: 0px;\\n  }\\n}\\n.app-content-menu[_ngcontent-%COMP%], .dummy-for-width[_ngcontent-%COMP%] {\\n  width: var(--menu-width);\\n}\\n\\n.app-content-menu[_ngcontent-%COMP%] {\\n  position: fixed;\\n  z-index: 1;\\n}\\n\\n.app-content-page[_ngcontent-%COMP%] {\\n  margin-left: calc(var(--menu-width) + var(--menu-to-page-padding));\\n  width: var(--page-width);\\n}\\n\\n@media only screen and (min-width: 768px) and (max-width: 1024px) and (max-height: 850px) {\\n  .app-content-page[_ngcontent-%COMP%] {\\n    margin-left: var(--menu-to-page-padding);\\n    width: var(--page-width);\\n    margin-top: 86px;\\n  }\\n  .app-content-menu[_ngcontent-%COMP%] {\\n    top: 71px;\\n    margin-left: -1%;\\n    z-index: 1;\\n  }\\n}\\n.app-content-page-no-profile[_ngcontent-%COMP%] {\\n  margin-left: 0;\\n  width: var(--app-content-width);\\n}\\n\\n@media screen and (max-width: 992px) {\\n  .app-content-page-no-profile[_ngcontent-%COMP%] {\\n    overflow: auto;\\n  }\\n}\\n@media screen and (max-width: 1024px) {\\n  .app-content-page-no-profile[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n    width: 100%;\\n  }\\n}\\n.top-left-gradient[_ngcontent-%COMP%], .top-left-gradient-big[_ngcontent-%COMP%] {\\n  border-radius: 50%;\\n  position: absolute;\\n  display: flex;\\n  justify-content: center;\\n  z-index: 0;\\n}\\n\\n.top-left-gradient[_ngcontent-%COMP%] {\\n  width: calc(var(--app-content-width) + var(--app-content-width) / 1.5);\\n  top: calc(-1 * var(--app-content-width) / 2.2);\\n  left: calc(-1 * var(--app-content-width) / 2);\\n  position: fixed;\\n}\\n\\n.profile[_ngcontent-%COMP%] {\\n  border-radius: 10px;\\n  background-color: var(--white);\\n  display: flex;\\n  flex-wrap: wrap;\\n}\\n\\n.auth-route[_ngcontent-%COMP%] {\\n  background-color: #fff;\\n  border-radius: 10px;\\n  box-shadow: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);\\n  position: relative;\\n  overflow: hidden;\\n  width: 85vw;\\n  max-width: 100%;\\n  min-height: 600px;\\n  margin: 1.25rem auto;\\n  transform: scale(1);\\n}\\n@media only screen and (min-width: 768px) {\\n  .auth-route[_ngcontent-%COMP%] {\\n    width: 960px;\\n    max-width: 100%;\\n    min-height: 620px;\\n  }\\n}\\n@media only screen and (min-width: 768px) and (max-height: 768px) {\\n  .auth-route[_ngcontent-%COMP%] {\\n    transform: scale(0.85);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n    data: {\n      animation: [slideInAnimation]\n    }\n  });\n}", "map": {"version": 3, "names": ["inject", "Injector", "signal", "NavigationEnd", "NavigationStart", "RoutesRecognized", "combineLatest", "filter", "map", "mergeMap", "switchMap", "take", "tap", "AppLayoutType", "slideInAnimation", "ChatService", "toObservable", "TooltipModule", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r0", "confirmDialogService", "rejectBtnIcon", "acceptBtnIcon", "confirmMessage", "rejectBtnLabel", "acceptBtnLabel", "headerText", "acceptBtnImage", "acceptBtnClass", "rejectBtnClass", "confirmIcon", "showConfirmIcon", "extraData", "AppComponent", "constructor", "router", "location", "generalService", "authService", "primengConfig", "activatedRoute", "classroomService", "userService", "layoutService", "injector", "title", "mt", "routeIsDashboard", "routeIsProfile", "routeIsTeacher", "routeIsAuth", "hideLeftMenu", "oldValue", "newValue", "url", "showLeftMenu", "gradient", "routeData", "layout$", "getLayoutType$", "previousUrl", "loggedIn", "chatService", "user", "getLoggedInUser", "userClassrooms", "ngOnInit", "ripple", "events", "subscribe", "evt", "window", "scrollTo", "console", "log", "includes", "getIsAuth", "isStudent", "handleStudentClassroomDataAndMenu", "updateAllMenuItemsDisabledStatus", "setTimeout", "for<PERSON>ach", "call", "document", "getElementsByClassName", "el", "style", "setProperty", "autoAuthUser", "setDevice", "res", "data", "state", "root", "<PERSON><PERSON><PERSON><PERSON>", "authStatusListener", "set", "listenLoggedInUser", "pipe", "event", "ev", "route", "layout", "getTopLeftGradientClass", "checkOffset", "onActivate", "onWindowScroll", "$event", "pageYOffset", "gradientScale", "onResize", "backClicked", "back", "addActiveClassToProfileInfoInSillyWay", "id", "getElementById", "classList", "add", "remove", "addActiveClassToTeacherInfoInSillyWay", "getUserHasPayment", "getLMSUserClassrooms", "classrooms", "setUserClassrooms", "isStudentActive", "updateMenuItemDisabledStatus", "length", "updateMenuItemClassroomToTrialsForStudent", "listenChatFromClassrooms", "totalUnreadCount", "classroomObservables", "element", "getUnreadMessagesCount", "aspUserId", "count", "counts", "reduce", "acc", "unreadCount", "next", "_", "ɵɵdirectiveInject", "i1", "Router", "i2", "Location", "i3", "GeneralService", "i4", "AuthService", "i5", "PrimeNGConfig", "ActivatedRoute", "i6", "ClassroomService", "i7", "UserService", "i8", "LayoutService", "i9", "ConfirmDialogService", "_2", "selectors", "hostBindings", "AppComponent_HostBindings", "rf", "ctx", "ɵɵlistener", "AppComponent_scroll_HostBindingHandler", "ɵɵresolveWindow", "AppComponent_resize_HostBindingHandler", "decls", "vars", "consts", "template", "AppComponent_Template", "ɵɵelementContainerStart", "ɵɵtemplate", "AppComponent_app_confirm_dialog_3_Template", "AppComponent_Conditional_4_Template", "ɵɵadvance", "isVisible", "ɵɵconditional", "isChatEnabled"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\app.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\app.component.html"], "sourcesContent": ["import { Location } from '@angular/common';\r\nimport { Component, HostListener, inject, Injector, OnInit, signal } from '@angular/core';\r\nimport { ActivatedRoute, Data, NavigationEnd, NavigationStart, Router, RoutesRecognized } from '@angular/router';\r\nimport { PrimeNGConfig } from 'primeng/api';\r\nimport { combineLatest, Observable } from 'rxjs';\r\nimport { filter, map, mergeMap, switchMap, take, tap } from 'rxjs/operators';\r\nimport { AppLayoutType } from './core/models/general.model';\r\nimport { AuthService } from './core/services/auth.service';\r\nimport { GeneralService } from './core/services/general.service';\r\nimport { slideInAnimation } from './helpers/route-animation';\r\nimport { ClassroomService } from './core/services/classroom.service';\r\nimport { UserService } from './core/services/user.service';\r\nimport { LayoutService } from './core/services/layout.service';\r\nimport { ConfirmDialogService } from './core/services/confirm-dialog.service';\r\nimport { ChatService } from './core/services/chat.service';\r\nimport { Classroom } from './core/models/classroom.model';\r\nimport { toObservable } from '@angular/core/rxjs-interop';\r\nimport { TooltipModule } from 'primeng/tooltip';\r\nimport { environment } from 'src/environments/environment';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: './app.component.html',\r\n  styleUrls: ['./app.component.scss'],\r\n  animations: [slideInAnimation],\r\n  providers: [\r\n    TooltipModule\r\n  ]\r\n})\r\nexport class AppComponent implements OnInit {\r\n\r\n  private injector = inject(Injector);\r\n  public title = 'frontend';\r\n  public mt: number = 0;\r\n  public routeIsDashboard: boolean = false;\r\n  public routeIsProfile: boolean = false;\r\n  public routeIsTeacher: boolean = false;\r\n  public routeIsAuth: boolean = false;\r\n  public hideLeftMenu: boolean = true;\r\n  public oldValue: number = 0;\r\n  public newValue: number = 0;\r\n  public url: string = \"\";\r\n  public showLeftMenu: boolean = false;\r\n  public gradient: string = \"\";\r\n  public routeData: Data = {} as Data;\r\n  layout$: Observable<AppLayoutType> = this.getLayoutType$();\r\n  readonly AppLayoutType = AppLayoutType;\r\n  private previousUrl: string = '';\r\n  loggedIn = signal(false);\r\n  chatService = inject(ChatService);\r\n  user = this.authService.getLoggedInUser();\r\n  userClassrooms = [] as Classroom[];\r\n  constructor(\r\n    private router: Router,\r\n    private location: Location,\r\n    private generalService: GeneralService,\r\n    public authService: AuthService,\r\n    private primengConfig: PrimeNGConfig,\r\n    private activatedRoute: ActivatedRoute,\r\n    public classroomService: ClassroomService,\r\n    private userService: UserService,\r\n    private layoutService: LayoutService,\r\n    public confirmDialogService: ConfirmDialogService,\r\n  ) {\r\n\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.primengConfig.ripple = true;\r\n\r\n    this.router.events.subscribe((evt) => {\r\n\r\n      if (evt instanceof NavigationStart) {\r\n        window.scrollTo(0, 0);\r\n      }\r\n\r\n      if (evt instanceof NavigationEnd) {\r\n        this.previousUrl = this.router.url;\r\n        console.log(' url = this .router.url ', evt.url);\r\n        if (evt.url.includes('chat')) {\r\n          // this.chatService.showBubbleChat.set(false);\r\n        } else {\r\n          // this.chatService.showBubbleChat.set(true);\r\n        }\r\n        if (this.authService.getIsAuth()) {\r\n          if (this.authService.isStudent) {\r\n            this.handleStudentClassroomDataAndMenu();\r\n          } else {\r\n            this.layoutService.updateAllMenuItemsDisabledStatus(false);\r\n          }\r\n        }\r\n        if (this.previousUrl !== evt.url) {\r\n          console.log(evt.url);\r\n          window.scrollTo(0, 0);\r\n        }\r\n      }\r\n\r\n\r\n\r\n    });\r\n\r\n    setTimeout(() => {\r\n      [].forEach.call(document.getElementsByClassName('tawk-card-primary'), (el: any) => {\r\n        el.style.setProperty(\"backgroundColor\", \"black\", \"important\");\r\n        console.log(el)\r\n      })\r\n    }, 10000);\r\n    this.authService.autoAuthUser();\r\n    this.generalService.setDevice();\r\n    this.getLayoutType$();\r\n\r\n    this.layout$.subscribe((res) => {\r\n      // console.log(res);\r\n    });\r\n\r\n    // this.getLayoutType$().subscribe((res)=>{\r\n    //   console.log(res);\r\n    // });\r\n\r\n    this.router.events.subscribe((data) => {\r\n      if (data instanceof RoutesRecognized) {\r\n        // console.log(data.state.root.firstChild!.data);\r\n\r\n        this.routeData = data.state.root.firstChild!.data;\r\n      }\r\n    });\r\n\r\n    this.authService.authStatusListener.subscribe(res => {\r\n      console.log(res);\r\n      this.loggedIn.set(res);\r\n      if (res) {\r\n      }\r\n    });\r\n    this.listenLoggedInUser();\r\n\r\n  }\r\n\r\n  getLayoutType$(): Observable<AppLayoutType> {\r\n    return this.router.events.pipe(\r\n      filter((event) => event instanceof RoutesRecognized),\r\n      map((ev: Data) => {\r\n        // console.log(this.activatedRoute);\r\n        return this.activatedRoute\r\n      }),\r\n      map((route) => {\r\n        while (route.firstChild) route = route.firstChild\r\n\r\n        // console.log(route);\r\n        return route\r\n      }),\r\n      mergeMap((route) => route.data),\r\n      map(({ layout }) => layout),\r\n    )\r\n  }\r\n\r\n  getTopLeftGradientClass() {\r\n    if (this.router.url.includes('dashboard')) {\r\n      this.routeIsDashboard = true;\r\n      return \"top-left-gradient\";\r\n    }\r\n    return \"top-left-gradient-big\"\r\n  }\r\n\r\n  checkOffset() {\r\n    // var leftMenu = document.getElementById('left-menu');\r\n    // var a = window.scrollY + leftMenu!?.clientHeight + document.getElementById('footer')!?.clientHeight + 100;\r\n    // var body = document.body;\r\n    // var html = document.documentElement;\r\n    // var height = Math.max(body.scrollHeight, body.offsetHeight, html.clientHeight, html.scrollHeight, html.offsetHeight);\r\n    // if (leftMenu !== null) {\r\n    //   if (a < height) {\r\n    //     leftMenu.style.position = \"fixed\";\r\n    //     leftMenu.style.top = html.clientHeight > 850 ? \"86px\" : \"70px\"; // see marginTop in .app-content class and check @media (max-height: 850px)\r\n    //     this.mt = window.scrollY;\r\n    //   } else {\r\n    //     leftMenu.style.position = \"absolute\";\r\n    //     leftMenu.style.top = this.mt + \"px\";\r\n    //   }\r\n    // }\r\n  }\r\n\r\n  onActivate(event: Event) {\r\n    window.scrollTo(0, 0);\r\n  }\r\n\r\n  @HostListener('window:scroll', ['$event'])\r\n  onWindowScroll($event: any) {\r\n    this.checkOffset();\r\n    this.newValue = window.pageYOffset;\r\n    this.generalService.gradientScale(this.newValue, this.oldValue, 'top-left-gradient')\r\n    this.oldValue = this.newValue;\r\n  }\r\n\r\n  @HostListener('window:resize', ['$event'])\r\n  onResize(event: any) {\r\n    this.generalService.setDevice();\r\n  }\r\n\r\n  backClicked() {\r\n    this.location.back();\r\n  }\r\n\r\n  addActiveClassToProfileInfoInSillyWay(data: RoutesRecognized, url: string, id: string) {\r\n    // Sorry :( but routerLinkActive is not working in profile-menu.component.ts\r\n    if (data.url === url) {\r\n      setTimeout(() => {\r\n        document.getElementById(id)?.classList.add('item-active')\r\n      }, 100);\r\n    } else {\r\n      document.getElementById(id)?.classList.remove('item-active')\r\n    }\r\n  }\r\n\r\n  addActiveClassToTeacherInfoInSillyWay(data: RoutesRecognized, url: string, id: string) {\r\n    // Sorry :( but routerLinkActive is not working in profile-menu.component.ts\r\n    if (data.url === url) {\r\n      setTimeout(() => {\r\n        document.getElementById(id)?.classList.add('item-active-teacher')\r\n        document.getElementById(id)?.classList.remove('inactive-item')\r\n      }, 100);\r\n    } else {\r\n      document.getElementById(id)?.classList.remove('item-active-teacher')\r\n      document.getElementById(id)?.classList.add('inactive-item')\r\n    }\r\n  }\r\n\r\n  private handleStudentClassroomDataAndMenu() {\r\n    this.userService.getUserHasPayment()\r\n      .pipe(\r\n        take(1),\r\n        switchMap((res: any) => {\r\n          return this.classroomService.getLMSUserClassrooms(this.authService.getLoggedInUser().id).pipe(\r\n            tap((classrooms) => {\r\n              this.classroomService.setUserClassrooms(classrooms);\r\n              const isStudentActive = this.classroomService.isStudentActive(classrooms, res);\r\n              this.layoutService.updateMenuItemDisabledStatus(\"Classrooms\", classrooms.length === 0);\r\n              this.layoutService.updateMenuItemDisabledStatus(\"Guides\", true);\r\n              if (isStudentActive) {\r\n                this.layoutService.updateAllMenuItemsDisabledStatus(false);\r\n              } else {\r\n                this.layoutService.updateMenuItemClassroomToTrialsForStudent(classrooms);\r\n              }\r\n              console.log(classrooms);\r\n            }),\r\n            take(1)\r\n          );\r\n        })\r\n      ).subscribe();\r\n  }\r\n\r\n  private listenChatFromClassrooms() {\r\n    this.classroomService.getLMSUserClassrooms(this.user.id).subscribe(res => {\r\n      let totalUnreadCount = 0; // Initialize a variable to accumulate the total unread count\r\n      console.log(res);\r\n      this.classroomService.setUserClassrooms(res);\r\n      const classroomObservables = res.map((element: Classroom) => {\r\n        return this.chatService.getUnreadMessagesCount(element.id, this.user.aspUserId).pipe(\r\n          tap(count => {\r\n            console.log(`Unread messages count for classroom ${element.id} for user ${this.user.aspUserId}: ${count}`);\r\n          })\r\n        );\r\n      });\r\n\r\n      combineLatest(classroomObservables).subscribe(counts => {\r\n        totalUnreadCount = (counts as number[]).reduce((acc, count) => acc + count, 0);\r\n        this.chatService.unreadCount.set(totalUnreadCount); // Update the total count\r\n      });\r\n    });\r\n  }\r\n\r\n  private listenLoggedInUser() {\r\n    toObservable(this.loggedIn, {\r\n      injector: this.injector\r\n    }).subscribe({\r\n      next: (data) => {\r\n        if (data) {\r\n          console.log(data);\r\n          \r\n          console.log(\"🚀 ~ AppComponent ~ listenLoggedInUser ~ console:\", console)\r\n          // this.listenChatFromClassrooms();\r\n        }\r\n      }\r\n    });\r\n\r\n  }\r\n\r\n}", "<!-- <ng-container *ngIf=\"routeIsAuth\">\r\n    <div class=\"content content--canvas\"></div>\r\n    <div class=\"animated-background w-embed\">\r\n        <div class=\"animatedwrapper\">\r\n            <div class=\"dot dot--one\"></div>\r\n            <div class=\"dot dot--two\"></div>\r\n        </div>\r\n    </div>\r\n    <div class=\"auth-container\">\r\n        <div\r\n            class=\"surface-ground flex align-items-center justify-content-center min-h-screen min-w-screen overflow-hidden\">\r\n            <div class=\"auth-route\">\r\n                <router-outlet></router-outlet>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</ng-container> -->\r\n\r\n<!-- <router-outlet></router-outlet> -->\r\n\r\n<!-- {{layout$ | json}} -->\r\n\r\n    <!-- <router-outlet></router-outlet> -->\r\n\r\n    <!-- {{this.routeData.layout}}\r\n    <ng-container *ngIf=\"this.routeData.layout\">\r\n    <ng-container [ngSwitch]=\"this.routeData.layout\">\r\n        <app-top-menu-container-layout *ngSwitchCase=\"AppLayoutType.Default\"></app-top-menu-container-layout>\r\n        <app-login-container-layout *ngSwitchCase=\"AppLayoutType.Center\"></app-login-container-layout>\r\n        <app-layout-sidebar *ngSwitchCase=\"AppLayoutType.Sidebar\"></app-layout-sidebar>\r\n        <router-outlet *ngSwitchCase=\"AppLayoutType.Blank\"></router-outlet>\r\n        <app-top-menu-container-layout *ngSwitchDefault></app-top-menu-container-layout>\r\n    </ng-container>\r\n</ng-container> -->\r\n\r\n<router-outlet></router-outlet>\r\n\r\n<!-- <ng-container *ngIf=\"!routeIsAuth\">\r\n    <app-header></app-header>\r\n    <app-mobile-menu></app-mobile-menu>\r\n    <div class=\"p-container-lg pt-8\" [ngClass]=\"{'pt-0' : routeIsAuth}\">\r\n     \r\n\r\n        <div class=\"\">\r\n            <div *ngIf=\"!hideLeftMenu\" id=\"left-menu\" class=\"app-content-menu\">\r\n                <app-menu></app-menu>\r\n            </div>\r\n            <router-outlet></router-outlet>\r\n        </div>\r\n    </div>\r\n</ng-container> -->\r\n<!-- \r\n<div *ngIf=\"!routeIsTeacher\" id=\"top-left-gradient\" class=\"top-left-gradient\">\r\n</div>\r\n<app-mobile-menu *ngIf=\"!routeIsAuth\"></app-mobile-menu>\r\n<app-header *ngIf=\"!routeIsAuth\"></app-header>\r\n<div class=\"app\" [ngClass]=\"{'pt-0' : routeIsAuth}\">\r\n\r\n    <ng-container *ngIf=\"routeIsAuth\">\r\n        <div class=\"content content--canvas\"></div>\r\n        <div class=\"animated-background w-embed\">\r\n            <div class=\"animatedwrapper\">\r\n                <div class=\"dot dot--one\"></div>\r\n                <div class=\"dot dot--two\"></div>\r\n            </div>\r\n        </div>\r\n    </ng-container>\r\n    <div id=\"app-content\" class=\"app-content\">\r\n        <div *ngIf=\"!hideLeftMenu\" id=\"left-menu\" class=\"app-content-menu\">\r\n            <app-menu></app-menu>\r\n        </div>\r\n        <div id=\"app-content-page\" class=\"app-content-page\" [ngClass]=\"{'auth-route' : routeIsAuth}\">\r\n            <div *ngIf=\"!routeIsProfile && !routeIsTeacher\">\r\n                <router-outlet #o=\"outlet\" (activate)=\"onActivate($event)\"></router-outlet>\r\n            </div>\r\n            <div *ngIf=\"routeIsProfile\">\r\n                <app-profile></app-profile>\r\n            </div>\r\n            <div *ngIf=\"routeIsTeacher\">\r\n                <router-outlet></router-outlet>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n<app-footer *ngIf=\"!routeIsAuth\" class=\"bottom-footer\"></app-footer>\r\n<div class=\"blur-bg no-visibility\" id=\"blur_bg\"></div>\r\n -->\r\n<!-- primeng toast GLOBAL in app -->\r\n<app-prime-toast></app-prime-toast>\r\n<!-- primeng confirm dialog GLOBAL in app -->\r\n<ng-container>\r\n    <app-confirm-dialog *ngIf=\"confirmDialogService.isVisible\" [rejectBtnIcon]=\"confirmDialogService.rejectBtnIcon\"\r\n        [acceptBtnIcon]=\"confirmDialogService.acceptBtnIcon\" [confirmMessage]=\"confirmDialogService.confirmMessage\"\r\n        [rejectBtnLabel]=\"confirmDialogService.rejectBtnLabel\"\r\n        [acceptBtnLabel]=\"confirmDialogService.acceptBtnLabel\"\r\n        [headerText]=\"confirmDialogService.headerText\"\r\n        [acceptBtnImage]=\"confirmDialogService.acceptBtnImage\"\r\n        [acceptBtnClass]=\"confirmDialogService.acceptBtnClass\"\r\n        [rejectBtnClass]=\"confirmDialogService.rejectBtnClass\"\r\n        [confirmIcon]=\"confirmDialogService.confirmIcon\"\r\n        [showConfirmIcon]=\"confirmDialogService.showConfirmIcon\"\r\n        [extraData]=\"confirmDialogService.extraData\"></app-confirm-dialog>\r\n</ng-container>\r\n\r\n\r\n<!-- TODO: enable for logged in users -->\r\n @if (loggedIn() && chatService.isChatEnabled()) {\r\n    <app-chat [bubbleMode]=\"true\"></app-chat>\r\n }"], "mappings": "AACA,SAAkCA,MAAM,EAAEC,QAAQ,EAAUC,MAAM,QAAQ,eAAe;AACzF,SAA+BC,aAAa,EAAEC,eAAe,EAAUC,gBAAgB,QAAQ,iBAAiB;AAEhH,SAASC,aAAa,QAAoB,MAAM;AAChD,SAASC,MAAM,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,IAAI,EAAEC,GAAG,QAAQ,gBAAgB;AAC5E,SAASC,aAAa,QAAQ,6BAA6B;AAG3D,SAASC,gBAAgB,QAAQ,2BAA2B;AAK5D,SAASC,WAAW,QAAQ,8BAA8B;AAE1D,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,aAAa,QAAQ,iBAAiB;;;;;;;;;;;;;;;;IC0E3CC,EAAA,CAAAC,SAAA,4BAUsE;;;;IAAlED,EAVuD,CAAAE,UAAA,kBAAAC,MAAA,CAAAC,oBAAA,CAAAC,aAAA,CAAoD,kBAAAF,MAAA,CAAAC,oBAAA,CAAAE,aAAA,CACvD,mBAAAH,MAAA,CAAAC,oBAAA,CAAAG,cAAA,CAAuD,mBAAAJ,MAAA,CAAAC,oBAAA,CAAAI,cAAA,CACrD,mBAAAL,MAAA,CAAAC,oBAAA,CAAAK,cAAA,CACA,eAAAN,MAAA,CAAAC,oBAAA,CAAAM,UAAA,CACR,mBAAAP,MAAA,CAAAC,oBAAA,CAAAO,cAAA,CACQ,mBAAAR,MAAA,CAAAC,oBAAA,CAAAQ,cAAA,CACA,mBAAAT,MAAA,CAAAC,oBAAA,CAAAS,cAAA,CACA,gBAAAV,MAAA,CAAAC,oBAAA,CAAAU,WAAA,CACN,oBAAAX,MAAA,CAAAC,oBAAA,CAAAW,eAAA,CACQ,cAAAZ,MAAA,CAAAC,oBAAA,CAAAY,SAAA,CACZ;;;;;IAMhDhB,EAAA,CAAAC,SAAA,kBAAyC;;;IAA/BD,EAAA,CAAAE,UAAA,oBAAmB;;;AD9EjC,OAAM,MAAOe,YAAY;EAuBvBC,YACUC,MAAc,EACdC,QAAkB,EAClBC,cAA8B,EAC/BC,WAAwB,EACvBC,aAA4B,EAC5BC,cAA8B,EAC/BC,gBAAkC,EACjCC,WAAwB,EACxBC,aAA4B,EAC7BvB,oBAA0C;IATzC,KAAAe,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,cAAc,GAAdA,cAAc;IACf,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACd,KAAAvB,oBAAoB,GAApBA,oBAAoB;IA/BrB,KAAAwB,QAAQ,GAAG9C,MAAM,CAACC,QAAQ,CAAC;IAC5B,KAAA8C,KAAK,GAAG,UAAU;IAClB,KAAAC,EAAE,GAAW,CAAC;IACd,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAAC,YAAY,GAAY,IAAI;IAC5B,KAAAC,QAAQ,GAAW,CAAC;IACpB,KAAAC,QAAQ,GAAW,CAAC;IACpB,KAAAC,GAAG,GAAW,EAAE;IAChB,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,SAAS,GAAS,EAAU;IACnC,KAAAC,OAAO,GAA8B,IAAI,CAACC,cAAc,EAAE;IACjD,KAAAhD,aAAa,GAAGA,aAAa;IAC9B,KAAAiD,WAAW,GAAW,EAAE;IAChC,KAAAC,QAAQ,GAAG7D,MAAM,CAAC,KAAK,CAAC;IACxB,KAAA8D,WAAW,GAAGhE,MAAM,CAACe,WAAW,CAAC;IACjC,KAAAkD,IAAI,GAAG,IAAI,CAACzB,WAAW,CAAC0B,eAAe,EAAE;IACzC,KAAAC,cAAc,GAAG,EAAiB;EAclC;EAEAC,QAAQA,CAAA;IACN,IAAI,CAAC3B,aAAa,CAAC4B,MAAM,GAAG,IAAI;IAEhC,IAAI,CAAChC,MAAM,CAACiC,MAAM,CAACC,SAAS,CAAEC,GAAG,IAAI;MAEnC,IAAIA,GAAG,YAAYpE,eAAe,EAAE;QAClCqE,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;MACvB;MAEA,IAAIF,GAAG,YAAYrE,aAAa,EAAE;QAChC,IAAI,CAAC2D,WAAW,GAAG,IAAI,CAACzB,MAAM,CAACmB,GAAG;QAClCmB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEJ,GAAG,CAAChB,GAAG,CAAC;QAChD,IAAIgB,GAAG,CAAChB,GAAG,CAACqB,QAAQ,CAAC,MAAM,CAAC,EAAE;UAC5B;QAAA,CACD,MAAM;UACL;QAAA;QAEF,IAAI,IAAI,CAACrC,WAAW,CAACsC,SAAS,EAAE,EAAE;UAChC,IAAI,IAAI,CAACtC,WAAW,CAACuC,SAAS,EAAE;YAC9B,IAAI,CAACC,iCAAiC,EAAE;UAC1C,CAAC,MAAM;YACL,IAAI,CAACnC,aAAa,CAACoC,gCAAgC,CAAC,KAAK,CAAC;UAC5D;QACF;QACA,IAAI,IAAI,CAACnB,WAAW,KAAKU,GAAG,CAAChB,GAAG,EAAE;UAChCmB,OAAO,CAACC,GAAG,CAACJ,GAAG,CAAChB,GAAG,CAAC;UACpBiB,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;QACvB;MACF;IAIF,CAAC,CAAC;IAEFQ,UAAU,CAAC,MAAK;MACd,EAAE,CAACC,OAAO,CAACC,IAAI,CAACC,QAAQ,CAACC,sBAAsB,CAAC,mBAAmB,CAAC,EAAGC,EAAO,IAAI;QAChFA,EAAE,CAACC,KAAK,CAACC,WAAW,CAAC,iBAAiB,EAAE,OAAO,EAAE,WAAW,CAAC;QAC7Dd,OAAO,CAACC,GAAG,CAACW,EAAE,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,EAAE,KAAK,CAAC;IACT,IAAI,CAAC/C,WAAW,CAACkD,YAAY,EAAE;IAC/B,IAAI,CAACnD,cAAc,CAACoD,SAAS,EAAE;IAC/B,IAAI,CAAC9B,cAAc,EAAE;IAErB,IAAI,CAACD,OAAO,CAACW,SAAS,CAAEqB,GAAG,IAAI;MAC7B;IAAA,CACD,CAAC;IAEF;IACA;IACA;IAEA,IAAI,CAACvD,MAAM,CAACiC,MAAM,CAACC,SAAS,CAAEsB,IAAI,IAAI;MACpC,IAAIA,IAAI,YAAYxF,gBAAgB,EAAE;QACpC;QAEA,IAAI,CAACsD,SAAS,GAAGkC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,UAAW,CAACH,IAAI;MACnD;IACF,CAAC,CAAC;IAEF,IAAI,CAACrD,WAAW,CAACyD,kBAAkB,CAAC1B,SAAS,CAACqB,GAAG,IAAG;MAClDjB,OAAO,CAACC,GAAG,CAACgB,GAAG,CAAC;MAChB,IAAI,CAAC7B,QAAQ,CAACmC,GAAG,CAACN,GAAG,CAAC;MACtB,IAAIA,GAAG,EAAE,CACT;IACF,CAAC,CAAC;IACF,IAAI,CAACO,kBAAkB,EAAE;EAE3B;EAEAtC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACxB,MAAM,CAACiC,MAAM,CAAC8B,IAAI,CAC5B7F,MAAM,CAAE8F,KAAK,IAAKA,KAAK,YAAYhG,gBAAgB,CAAC,EACpDG,GAAG,CAAE8F,EAAQ,IAAI;MACf;MACA,OAAO,IAAI,CAAC5D,cAAc;IAC5B,CAAC,CAAC,EACFlC,GAAG,CAAE+F,KAAK,IAAI;MACZ,OAAOA,KAAK,CAACP,UAAU,EAAEO,KAAK,GAAGA,KAAK,CAACP,UAAU;MAEjD;MACA,OAAOO,KAAK;IACd,CAAC,CAAC,EACF9F,QAAQ,CAAE8F,KAAK,IAAKA,KAAK,CAACV,IAAI,CAAC,EAC/BrF,GAAG,CAAC,CAAC;MAAEgG;IAAM,CAAE,KAAKA,MAAM,CAAC,CAC5B;EACH;EAEAC,uBAAuBA,CAAA;IACrB,IAAI,IAAI,CAACpE,MAAM,CAACmB,GAAG,CAACqB,QAAQ,CAAC,WAAW,CAAC,EAAE;MACzC,IAAI,CAAC5B,gBAAgB,GAAG,IAAI;MAC5B,OAAO,mBAAmB;IAC5B;IACA,OAAO,uBAAuB;EAChC;EAEAyD,WAAWA,CAAA;IACT;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;EAGFC,UAAUA,CAACN,KAAY;IACrB5B,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;EACvB;EAGAkC,cAAcA,CAACC,MAAW;IACxB,IAAI,CAACH,WAAW,EAAE;IAClB,IAAI,CAACnD,QAAQ,GAAGkB,MAAM,CAACqC,WAAW;IAClC,IAAI,CAACvE,cAAc,CAACwE,aAAa,CAAC,IAAI,CAACxD,QAAQ,EAAE,IAAI,CAACD,QAAQ,EAAE,mBAAmB,CAAC;IACpF,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACC,QAAQ;EAC/B;EAGAyD,QAAQA,CAACX,KAAU;IACjB,IAAI,CAAC9D,cAAc,CAACoD,SAAS,EAAE;EACjC;EAEAsB,WAAWA,CAAA;IACT,IAAI,CAAC3E,QAAQ,CAAC4E,IAAI,EAAE;EACtB;EAEAC,qCAAqCA,CAACtB,IAAsB,EAAErC,GAAW,EAAE4D,EAAU;IACnF;IACA,IAAIvB,IAAI,CAACrC,GAAG,KAAKA,GAAG,EAAE;MACpB0B,UAAU,CAAC,MAAK;QACdG,QAAQ,CAACgC,cAAc,CAACD,EAAE,CAAC,EAAEE,SAAS,CAACC,GAAG,CAAC,aAAa,CAAC;MAC3D,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,MAAM;MACLlC,QAAQ,CAACgC,cAAc,CAACD,EAAE,CAAC,EAAEE,SAAS,CAACE,MAAM,CAAC,aAAa,CAAC;IAC9D;EACF;EAEAC,qCAAqCA,CAAC5B,IAAsB,EAAErC,GAAW,EAAE4D,EAAU;IACnF;IACA,IAAIvB,IAAI,CAACrC,GAAG,KAAKA,GAAG,EAAE;MACpB0B,UAAU,CAAC,MAAK;QACdG,QAAQ,CAACgC,cAAc,CAACD,EAAE,CAAC,EAAEE,SAAS,CAACC,GAAG,CAAC,qBAAqB,CAAC;QACjElC,QAAQ,CAACgC,cAAc,CAACD,EAAE,CAAC,EAAEE,SAAS,CAACE,MAAM,CAAC,eAAe,CAAC;MAChE,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,MAAM;MACLnC,QAAQ,CAACgC,cAAc,CAACD,EAAE,CAAC,EAAEE,SAAS,CAACE,MAAM,CAAC,qBAAqB,CAAC;MACpEnC,QAAQ,CAACgC,cAAc,CAACD,EAAE,CAAC,EAAEE,SAAS,CAACC,GAAG,CAAC,eAAe,CAAC;IAC7D;EACF;EAEQvC,iCAAiCA,CAAA;IACvC,IAAI,CAACpC,WAAW,CAAC8E,iBAAiB,EAAE,CACjCtB,IAAI,CACHzF,IAAI,CAAC,CAAC,CAAC,EACPD,SAAS,CAAEkF,GAAQ,IAAI;MACrB,OAAO,IAAI,CAACjD,gBAAgB,CAACgF,oBAAoB,CAAC,IAAI,CAACnF,WAAW,CAAC0B,eAAe,EAAE,CAACkD,EAAE,CAAC,CAAChB,IAAI,CAC3FxF,GAAG,CAAEgH,UAAU,IAAI;QACjB,IAAI,CAACjF,gBAAgB,CAACkF,iBAAiB,CAACD,UAAU,CAAC;QACnD,MAAME,eAAe,GAAG,IAAI,CAACnF,gBAAgB,CAACmF,eAAe,CAACF,UAAU,EAAEhC,GAAG,CAAC;QAC9E,IAAI,CAAC/C,aAAa,CAACkF,4BAA4B,CAAC,YAAY,EAAEH,UAAU,CAACI,MAAM,KAAK,CAAC,CAAC;QACtF,IAAI,CAACnF,aAAa,CAACkF,4BAA4B,CAAC,QAAQ,EAAE,IAAI,CAAC;QAC/D,IAAID,eAAe,EAAE;UACnB,IAAI,CAACjF,aAAa,CAACoC,gCAAgC,CAAC,KAAK,CAAC;QAC5D,CAAC,MAAM;UACL,IAAI,CAACpC,aAAa,CAACoF,yCAAyC,CAACL,UAAU,CAAC;QAC1E;QACAjD,OAAO,CAACC,GAAG,CAACgD,UAAU,CAAC;MACzB,CAAC,CAAC,EACFjH,IAAI,CAAC,CAAC,CAAC,CACR;IACH,CAAC,CAAC,CACH,CAAC4D,SAAS,EAAE;EACjB;EAEQ2D,wBAAwBA,CAAA;IAC9B,IAAI,CAACvF,gBAAgB,CAACgF,oBAAoB,CAAC,IAAI,CAAC1D,IAAI,CAACmD,EAAE,CAAC,CAAC7C,SAAS,CAACqB,GAAG,IAAG;MACvE,IAAIuC,gBAAgB,GAAG,CAAC,CAAC,CAAC;MAC1BxD,OAAO,CAACC,GAAG,CAACgB,GAAG,CAAC;MAChB,IAAI,CAACjD,gBAAgB,CAACkF,iBAAiB,CAACjC,GAAG,CAAC;MAC5C,MAAMwC,oBAAoB,GAAGxC,GAAG,CAACpF,GAAG,CAAE6H,OAAkB,IAAI;QAC1D,OAAO,IAAI,CAACrE,WAAW,CAACsE,sBAAsB,CAACD,OAAO,CAACjB,EAAE,EAAE,IAAI,CAACnD,IAAI,CAACsE,SAAS,CAAC,CAACnC,IAAI,CAClFxF,GAAG,CAAC4H,KAAK,IAAG;UACV7D,OAAO,CAACC,GAAG,CAAC,uCAAuCyD,OAAO,CAACjB,EAAE,aAAa,IAAI,CAACnD,IAAI,CAACsE,SAAS,KAAKC,KAAK,EAAE,CAAC;QAC5G,CAAC,CAAC,CACH;MACH,CAAC,CAAC;MAEFlI,aAAa,CAAC8H,oBAAoB,CAAC,CAAC7D,SAAS,CAACkE,MAAM,IAAG;QACrDN,gBAAgB,GAAIM,MAAmB,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEH,KAAK,KAAKG,GAAG,GAAGH,KAAK,EAAE,CAAC,CAAC;QAC9E,IAAI,CAACxE,WAAW,CAAC4E,WAAW,CAAC1C,GAAG,CAACiC,gBAAgB,CAAC,CAAC,CAAC;MACtD,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEQhC,kBAAkBA,CAAA;IACxBnF,YAAY,CAAC,IAAI,CAAC+C,QAAQ,EAAE;MAC1BjB,QAAQ,EAAE,IAAI,CAACA;KAChB,CAAC,CAACyB,SAAS,CAAC;MACXsE,IAAI,EAAGhD,IAAI,IAAI;QACb,IAAIA,IAAI,EAAE;UACRlB,OAAO,CAACC,GAAG,CAACiB,IAAI,CAAC;UAEjBlB,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAED,OAAO,CAAC;UACzE;QACF;MACF;KACD,CAAC;EAEJ;EAAC,QAAAmE,CAAA,G;qBA/PU3G,YAAY,EAAAjB,EAAA,CAAA6H,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA/H,EAAA,CAAA6H,iBAAA,CAAAG,EAAA,CAAAC,QAAA,GAAAjI,EAAA,CAAA6H,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAnI,EAAA,CAAA6H,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAArI,EAAA,CAAA6H,iBAAA,CAAAS,EAAA,CAAAC,aAAA,GAAAvI,EAAA,CAAA6H,iBAAA,CAAAC,EAAA,CAAAU,cAAA,GAAAxI,EAAA,CAAA6H,iBAAA,CAAAY,EAAA,CAAAC,gBAAA,GAAA1I,EAAA,CAAA6H,iBAAA,CAAAc,EAAA,CAAAC,WAAA,GAAA5I,EAAA,CAAA6H,iBAAA,CAAAgB,EAAA,CAAAC,aAAA,GAAA9I,EAAA,CAAA6H,iBAAA,CAAAkB,EAAA,CAAAC,oBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAZhI,YAAY;IAAAiI,SAAA;IAAAC,YAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAZrJ,EAAA,CAAAuJ,UAAA,oBAAAC,uCAAA7D,MAAA;UAAA,OAAA2D,GAAA,CAAA5D,cAAA,CAAAC,MAAA,CAAsB;QAAA,UAAA3F,EAAA,CAAAyJ,eAAA,CAAV,oBAAAC,uCAAA/D,MAAA;UAAA,OAAZ2D,GAAA,CAAAxD,QAAA,CAAAH,MAAA,CAAgB;QAAA,UAAA3F,EAAA,CAAAyJ,eAAA,CAAJ;;;qCAJZ,CACT1J,aAAa,CACd;IAAA4J,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,sBAAAV,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC6DHrJ,EArDA,CAAAC,SAAA,oBAA+B,sBAqDI;QAEnCD,EAAA,CAAAgK,uBAAA,GAAc;QACVhK,EAAA,CAAAiK,UAAA,IAAAC,0CAAA,iCAUiD;;QAKpDlK,EAAA,CAAAiK,UAAA,IAAAE,mCAAA,sBAAiD;;;QAfzBnK,EAAA,CAAAoK,SAAA,GAAoC;QAApCpK,EAAA,CAAAE,UAAA,SAAAoJ,GAAA,CAAAlJ,oBAAA,CAAAiK,SAAA,CAAoC;QAe5DrK,EAAA,CAAAoK,SAAA,EAEC;QAFDpK,EAAA,CAAAsK,aAAA,IAAAhB,GAAA,CAAAzG,QAAA,MAAAyG,GAAA,CAAAxG,WAAA,CAAAyH,aAAA,YAEC;;;;;;iBDpFY,CAAC3K,gBAAgB;IAAC;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}