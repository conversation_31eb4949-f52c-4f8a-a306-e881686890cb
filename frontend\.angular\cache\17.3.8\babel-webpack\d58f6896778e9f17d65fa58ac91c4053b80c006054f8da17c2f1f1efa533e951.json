{"ast": null, "code": "import { NavigationEnd } from '@angular/router';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"primeng/api\";\nimport * as i3 from \"primeng/button\";\nimport * as i4 from \"primeng/confirmdialog\";\nimport * as i5 from \"./components/profile-menu/profile-menu.component\";\nconst _c0 = () => ({\n  width: \"480px\"\n});\nconst _c1 = () => ({\n  \"960px\": \"75vw\",\n  \"640px\": \"100vw\"\n});\nfunction UserProfileComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 10);\n    i0.ɵɵlistener(\"click\", function UserProfileComponent_ng_template_12_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      i0.ɵɵnextContext();\n      const cda_r2 = i0.ɵɵreference(11);\n      return i0.ɵɵresetView(cda_r2.reject());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(1, \"p-button\", 11);\n    i0.ɵɵlistener(\"click\", function UserProfileComponent_ng_template_12_Template_p_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      i0.ɵɵnextContext();\n      const cda_r2 = i0.ɵɵreference(11);\n      return i0.ɵɵresetView(cda_r2.accept());\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nexport class UserProfileComponent {\n  constructor(router, route) {\n    this.router = router;\n    this.route = route;\n    this.headingTitle = '';\n    this.subs = new SubSink();\n  }\n  ngOnInit() {\n    this.prepareDataTitleFromRoute();\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  prepareDataTitleFromRoute() {\n    this.subs.add(this.router.events.subscribe(event => {\n      if (event instanceof NavigationEnd) {\n        const title = this.route.snapshot.firstChild.data;\n        this.headingTitle = title.title;\n      }\n    }));\n    if (this.route.snapshot.firstChild) {\n      if (this.route.snapshot.firstChild.data.title) {\n        this.headingTitle = this.route.snapshot.firstChild.data.title;\n      }\n    }\n  }\n  static #_ = this.ɵfac = function UserProfileComponent_Factory(t) {\n    return new (t || UserProfileComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: UserProfileComponent,\n    selectors: [[\"app-user-profile\"]],\n    decls: 13,\n    vars: 8,\n    consts: [[\"cda\", \"\"], [\"id\", \"profile\", 1, \"profile\", \"flex-column\"], [1, \"block-header\", \"justify-content-center\", \"hidden\", \"lg:flex\"], [1, \"block-title\"], [1, \"text-0\"], [1, \"flex\", \"flex-column\", \"lg:flex-row\"], [1, \"left-side\"], [1, \"right-side\"], [\"key\", \"stepLeaveConfirmation\", \"defaultFocus\", \"none\", \"rejectIcon\", \"pi pi-times\", 1, \"cd-confirm-dialog\", 3, \"breakpoints\", \"baseZIndex\", \"closable\"], [\"pTemplate\", \"footer\"], [\"styleClass\", \"cd-dialog-btn-reject\", \"icon\", \"pi pi-times\", \"label\", \"No\", 3, \"click\"], [\"styleClass\", \"cd-dialog-btn-accept\", \"icon\", \"pi pi-check\", \"label\", \"Yes\", 3, \"click\"]],\n    template: function UserProfileComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"span\", 3)(3, \"span\", 4);\n        i0.ɵɵtext(4);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(5, \"div\", 5)(6, \"div\", 6);\n        i0.ɵɵelement(7, \"app-profile-menu\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"div\", 7);\n        i0.ɵɵelement(9, \"router-outlet\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(10, \"p-confirmDialog\", 8, 0);\n        i0.ɵɵtemplate(12, UserProfileComponent_ng_template_12_Template, 2, 0, \"ng-template\", 9);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵtextInterpolate(ctx.headingTitle);\n        i0.ɵɵadvance(6);\n        i0.ɵɵstyleMap(i0.ɵɵpureFunction0(6, _c0));\n        i0.ɵɵproperty(\"breakpoints\", i0.ɵɵpureFunction0(7, _c1))(\"baseZIndex\", 10000)(\"closable\", false);\n      }\n    },\n    dependencies: [i1.RouterOutlet, i2.PrimeTemplate, i3.Button, i4.ConfirmDialog, i5.ProfileMenuComponent],\n    styles: [\".profile[_ngcontent-%COMP%] {\\n  border-radius: 10px;\\n  background-color: var(--white);\\n  display: flex;\\n}\\n.profile[_ngcontent-%COMP%]   .block-header[_ngcontent-%COMP%] {\\n  background: url(\\\"/assets/images/profile-header-bg.png\\\") no-repeat center;\\n  border-radius: 10px;\\n  background-size: 100%;\\n}\\n\\n\\n\\n.profile[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: var(--scrollbar-width);\\n}\\n\\n.profile[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background-color: currentColor;\\n  border-radius: 9999px;\\n  \\n\\n}\\n\\n.profile[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background-color: transparent;\\n}\\n\\n.profile-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  cursor: pointer;\\n  margin: 0 0 15px 0;\\n  color: white;\\n  padding: 10px;\\n  width: var(--app-content-width);\\n  z-index: 100;\\n  box-sizing: border-box;\\n}\\n\\n.back-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.back-button[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n}\\n\\n.right-side[_ngcontent-%COMP%] {\\n  padding: 15px;\\n}\\n\\n@media screen and (min-width: 992px) {\\n  .right-side[_ngcontent-%COMP%] {\\n    width: 100%;\\n    position: relative;\\n  }\\n  .left-side[_ngcontent-%COMP%] {\\n    --profile-menu-width: 30%;\\n    flex-basis: var(--profile-menu-width);\\n    width: var(--profile-menu-width);\\n    border-top-left-radius: 10px;\\n    border-top-right-radius: 0px;\\n    border-bottom-left-radius: 10px;\\n    border-right: 1px solid var(--gray-border);\\n    box-sizing: border-box;\\n    background-color: var(--white);\\n    position: relative;\\n  }\\n}\\n@media screen and (max-width: 991px) {\\n  .profile[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n  }\\n  .right-side[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n  }\\n  .left-side[_ngcontent-%COMP%] {\\n    flex-basis: 100%;\\n    width: 100%;\\n    border-top-left-radius: 10px;\\n    border-top-right-radius: 10px;\\n    border-bottom-left-radius: 0px;\\n    box-sizing: border-box;\\n    background-color: var(--white);\\n    background-color: var(--white);\\n  }\\n}\\n@media screen and (max-width: 576px) {\\n  .profile[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n.profile-info[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  position: relative;\\n}\\n.profile-info[_ngcontent-%COMP%]   .save-button[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 20px;\\n  top: 20px;\\n}\\n.profile-info[_ngcontent-%COMP%]   .profile-info-section[_ngcontent-%COMP%] {\\n  padding: 30px;\\n  box-sizing: border-box;\\n}\\n.profile-info[_ngcontent-%COMP%]   .profile-info-section[_ngcontent-%COMP%]   .profile-info-section-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 600;\\n  font-stretch: normal;\\n  font-style: normal;\\n  line-height: normal;\\n}\\n.profile-info[_ngcontent-%COMP%]   .profile-info-section[_ngcontent-%COMP%]   .profile-info-section-file[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n@media screen and (min-width: 1366px) {\\n  .profile-info[_ngcontent-%COMP%]   .profile-info-section[_ngcontent-%COMP%]   .profile-info-section-file[_ngcontent-%COMP%] {\\n    width: 50%;\\n  }\\n}\\n\\n[_nghost-%COMP%]     .p-dialog.p-confirm-dialog .p-confirm-dialog-message {\\n  margin-left: 0;\\n}\\n[_nghost-%COMP%]     .cd-confirm-dialog .p-dialog .p-dialog-footer {\\n  justify-content: center;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["NavigationEnd", "SubSink", "i0", "ɵɵelementStart", "ɵɵlistener", "UserProfileComponent_ng_template_12_Template_p_button_click_0_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "cda_r2", "ɵɵreference", "ɵɵresetView", "reject", "ɵɵelementEnd", "UserProfileComponent_ng_template_12_Template_p_button_click_1_listener", "accept", "UserProfileComponent", "constructor", "router", "route", "headingTitle", "subs", "ngOnInit", "prepareDataTitleFromRoute", "ngOnDestroy", "unsubscribe", "add", "events", "subscribe", "event", "title", "snapshot", "<PERSON><PERSON><PERSON><PERSON>", "data", "_", "ɵɵdirectiveInject", "i1", "Router", "ActivatedRoute", "_2", "selectors", "decls", "vars", "consts", "template", "UserProfileComponent_Template", "rf", "ctx", "ɵɵtext", "ɵɵelement", "ɵɵtemplate", "UserProfileComponent_ng_template_12_Template", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵstyleMap", "ɵɵpureFunction0", "_c0", "ɵɵproperty", "_c1"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\user-profile\\user-profile.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\user-profile\\user-profile.component.html"], "sourcesContent": ["import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, NavigationEnd, Router, RouterEvent } from '@angular/router';\r\nimport { filter } from 'rxjs/operators';\r\nimport { SubSink } from 'subsink';\r\n\r\n@Component({\r\n  selector: 'app-user-profile',\r\n  templateUrl: './user-profile.component.html',\r\n  styleUrls: ['./user-profile.component.scss']\r\n})\r\nexport class UserProfileComponent implements OnInit, OnDestroy {\r\n\r\n  headingTitle = '';\r\n  subs = new SubSink();\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private route: ActivatedRoute\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.prepareDataTitleFromRoute();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  prepareDataTitleFromRoute() {\r\n    this.subs.add(this.router.events.subscribe(event => {\r\n      if (event instanceof NavigationEnd) {\r\n        const title = this.route.snapshot.firstChild!.data;\r\n        this.headingTitle = title.title;\r\n\r\n      }\r\n    }));\r\n    if (this.route.snapshot.firstChild) {\r\n      if (this.route.snapshot.firstChild.data.title) {\r\n\r\n        this.headingTitle = this.route.snapshot.firstChild.data.title;\r\n      }\r\n    }\r\n  }\r\n}\r\n", "\r\n<!-- <div class=\"profile-header\" (click)=\"backClicked()\">\r\n    <div class=\"back-button\">\r\n        <img src=\"/assets/icons/back.svg\" />\r\n        <div class=\"text\">Back</div>\r\n    </div>\r\n</div> -->\r\n\r\n\r\n<div id=\"profile\" class=\"profile flex-column\">\r\n    <div class=\"block-header justify-content-center hidden lg:flex\">\r\n        <span class=\"block-title\">\r\n            <span class=\"text-0\">{{headingTitle}}</span>\r\n        </span>\r\n    </div>\r\n    <div class=\"flex flex-column lg:flex-row\">\r\n        \r\n        <div class=\"left-side\">\r\n            <app-profile-menu></app-profile-menu>\r\n        </div>\r\n        <div class=\"right-side\">\r\n            <router-outlet></router-outlet>\r\n        </div>\r\n    </div>\r\n</div>\r\n\r\n\r\n<p-confirmDialog #cda key=\"stepLeaveConfirmation\" class=\"cd-confirm-dialog\" \r\ndefaultFocus=\"none\"\r\n[breakpoints]=\"{'960px': '75vw', '640px': '100vw'}\" [style]=\"{width: '480px'}\" \r\n[baseZIndex]=\"10000\"\r\nrejectIcon=\"pi pi-times\"\r\n[closable]=\"false\">\r\n<ng-template pTemplate=\"footer\">\r\n    <p-button styleClass=\"cd-dialog-btn-reject\" icon=\"pi pi-times\" label=\"No\" (click)=\"cda.reject()\"></p-button>\r\n    <p-button styleClass=\"cd-dialog-btn-accept\" icon=\"pi pi-check\" label=\"Yes\" (click)=\"cda.accept()\"></p-button>\r\n</ng-template>\r\n</p-confirmDialog>\r\n"], "mappings": "AACA,SAAyBA,aAAa,QAA6B,iBAAiB;AAEpF,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;;;;;;IC+B7BC,EAAA,CAAAC,cAAA,mBAAiG;IAAvBD,EAAA,CAAAE,UAAA,mBAAAC,uEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAAL,EAAA,CAAAM,aAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,WAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAG,MAAA,EAAY;IAAA,EAAC;IAACV,EAAA,CAAAW,YAAA,EAAW;IAC5GX,EAAA,CAAAC,cAAA,mBAAkG;IAAvBD,EAAA,CAAAE,UAAA,mBAAAU,uEAAA;MAAAZ,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAAL,EAAA,CAAAM,aAAA;MAAA,MAAAC,MAAA,GAAAP,EAAA,CAAAQ,WAAA;MAAA,OAAAR,EAAA,CAAAS,WAAA,CAASF,MAAA,CAAAM,MAAA,EAAY;IAAA,EAAC;IAACb,EAAA,CAAAW,YAAA,EAAW;;;ADzBjH,OAAM,MAAOG,oBAAoB;EAK/BC,YACUC,MAAc,EACdC,KAAqB;IADrB,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IALf,KAAAC,YAAY,GAAG,EAAE;IACjB,KAAAC,IAAI,GAAG,IAAIpB,OAAO,EAAE;EAKhB;EAEJqB,QAAQA,CAAA;IACN,IAAI,CAACC,yBAAyB,EAAE;EAClC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACH,IAAI,CAACI,WAAW,EAAE;EACzB;EAEAF,yBAAyBA,CAAA;IACvB,IAAI,CAACF,IAAI,CAACK,GAAG,CAAC,IAAI,CAACR,MAAM,CAACS,MAAM,CAACC,SAAS,CAACC,KAAK,IAAG;MACjD,IAAIA,KAAK,YAAY7B,aAAa,EAAE;QAClC,MAAM8B,KAAK,GAAG,IAAI,CAACX,KAAK,CAACY,QAAQ,CAACC,UAAW,CAACC,IAAI;QAClD,IAAI,CAACb,YAAY,GAAGU,KAAK,CAACA,KAAK;MAEjC;IACF,CAAC,CAAC,CAAC;IACH,IAAI,IAAI,CAACX,KAAK,CAACY,QAAQ,CAACC,UAAU,EAAE;MAClC,IAAI,IAAI,CAACb,KAAK,CAACY,QAAQ,CAACC,UAAU,CAACC,IAAI,CAACH,KAAK,EAAE;QAE7C,IAAI,CAACV,YAAY,GAAG,IAAI,CAACD,KAAK,CAACY,QAAQ,CAACC,UAAU,CAACC,IAAI,CAACH,KAAK;MAC/D;IACF;EACF;EAAC,QAAAI,CAAA,G;qBAhCUlB,oBAAoB,EAAAd,EAAA,CAAAiC,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAnC,EAAA,CAAAiC,iBAAA,CAAAC,EAAA,CAAAE,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAApBvB,oBAAoB;IAAAwB,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCErB5C,EAHZ,CAAAC,cAAA,aAA8C,aACsB,cAClC,cACD;QAAAD,EAAA,CAAA8C,MAAA,GAAgB;QAE7C9C,EAF6C,CAAAW,YAAA,EAAO,EACzC,EACL;QAGFX,EAFJ,CAAAC,cAAA,aAA0C,aAEf;QACnBD,EAAA,CAAA+C,SAAA,uBAAqC;QACzC/C,EAAA,CAAAW,YAAA,EAAM;QACNX,EAAA,CAAAC,cAAA,aAAwB;QACpBD,EAAA,CAAA+C,SAAA,oBAA+B;QAG3C/C,EAFQ,CAAAW,YAAA,EAAM,EACJ,EACJ;QAGNX,EAAA,CAAAC,cAAA,6BAKmB;QACnBD,EAAA,CAAAgD,UAAA,KAAAC,4CAAA,yBAAgC;QAIhCjD,EAAA,CAAAW,YAAA,EAAkB;;;QAzBeX,EAAA,CAAAkD,SAAA,GAAgB;QAAhBlD,EAAA,CAAAmD,iBAAA,CAAAN,GAAA,CAAA3B,YAAA,CAAgB;QAiBGlB,EAAA,CAAAkD,SAAA,GAA0B;QAA1BlD,EAAA,CAAAoD,UAAA,CAAApD,EAAA,CAAAqD,eAAA,IAAAC,GAAA,EAA0B;QAG9EtD,EAHA,CAAAuD,UAAA,gBAAAvD,EAAA,CAAAqD,eAAA,IAAAG,GAAA,EAAmD,qBAC/B,mBAEF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}