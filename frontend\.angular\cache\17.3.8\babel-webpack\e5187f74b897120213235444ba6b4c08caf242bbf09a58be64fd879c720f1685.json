{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/note.service\";\nimport * as i2 from \"@angular/common\";\nfunction SendOrShareComponent_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Send \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SendOrShareComponent_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Send & Share \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class SendOrShareComponent {\n  constructor(noteService) {\n    this.noteService = noteService;\n    this.kind = '';\n    this.classrooms = [];\n    this.show = false;\n    this.usersSendAndShared = [];\n    this.classroomsSendAndShared = [];\n    this.note = {};\n    this.onUsersSelect = new EventEmitter();\n    this.onClose = new EventEmitter();\n    this.usersSelected = [];\n    this.fromClassroom = {};\n    this.map = new Map();\n  }\n  ngOnInit() {\n    console.log(this.usersSendAndShared);\n    if (this.note.id) {\n      this.noteService.getClassroomNoteHasBeenSharedWith(this.note.id.toString()).subscribe(res => {\n        this.classroomsSendAndShared = res;\n      });\n    } else {\n      this.classroomsSendAndShared = [];\n    }\n    this.usersSelected = this.usersSendAndShared;\n  }\n  onCheckedList(event) {\n    this.usersSelected = event.usersChecked;\n    this.fromClassroom = event.classroom;\n    this.map = event.map;\n  }\n  send() {\n    this.onUsersSelect.emit({\n      usersSelected: this.usersSelected,\n      kind: this.kind,\n      fromClassroom: this.fromClassroom,\n      map: this.map\n    });\n  }\n  close() {\n    this.onClose.emit({\n      kind: this.kind\n    });\n  }\n  static #_ = this.ɵfac = function SendOrShareComponent_Factory(t) {\n    return new (t || SendOrShareComponent)(i0.ɵɵdirectiveInject(i1.NoteService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SendOrShareComponent,\n    selectors: [[\"app-send-or-share\"]],\n    inputs: {\n      kind: \"kind\",\n      classrooms: \"classrooms\",\n      show: \"show\",\n      usersSendAndShared: \"usersSendAndShared\",\n      classroomsSendAndShared: \"classroomsSendAndShared\",\n      note: \"note\"\n    },\n    outputs: {\n      onUsersSelect: \"onUsersSelect\",\n      onClose: \"onClose\"\n    },\n    decls: 5,\n    vars: 6,\n    consts: [[1, \"notes-popup\", 2, \"overflow-y\", \"scroll\"], [3, \"close\", \"checkedList\", \"already\", \"showBlur\", \"usersAssignedTo\", \"classroomsAssignedTo\"], [1, \"send-button\", \"hvr-glow\", 3, \"click\"], [4, \"ngIf\"]],\n    template: function SendOrShareComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"app-classrooms-check-list\", 1);\n        i0.ɵɵlistener(\"close\", function SendOrShareComponent_Template_app_classrooms_check_list_close_1_listener() {\n          return ctx.close();\n        })(\"checkedList\", function SendOrShareComponent_Template_app_classrooms_check_list_checkedList_1_listener($event) {\n          return ctx.onCheckedList($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(2, \"div\", 2);\n        i0.ɵɵlistener(\"click\", function SendOrShareComponent_Template_div_click_2_listener() {\n          return ctx.send();\n        });\n        i0.ɵɵtemplate(3, SendOrShareComponent_span_3_Template, 2, 0, \"span\", 3)(4, SendOrShareComponent_span_4_Template, 2, 0, \"span\", 3);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"already\", ctx.usersSendAndShared)(\"showBlur\", true)(\"usersAssignedTo\", ctx.usersSelected)(\"classroomsAssignedTo\", ctx.classroomsSendAndShared);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.kind === \"send\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.kind === \"send-and-share\");\n      }\n    },\n    dependencies: [i2.NgIf],\n    styles: [\".send-classroom-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: bold;\\n}\\n\\n.send-classroom-user[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  margin-left: 10px;\\n  display: flex;\\n}\\n\\n.send-button[_ngcontent-%COMP%] {\\n  background-color: var(--main-color);\\n  width: 100%;\\n  position: sticky;\\n  bottom: 0;\\n  left: 0;\\n  padding: 8px;\\n  box-sizing: border-box;\\n  color: white;\\n  text-align: center;\\n  border-radius: 12px;\\n  cursor: pointer;\\n}\\n\\n.notes-popup[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0px;\\n  border-radius: 12px;\\n  background-color: white;\\n  color: var(--main-color);\\n  right: 0;\\n  box-shadow: 0px -4px 4px rgba(0, 0, 0, 0.25);\\n  max-height: 400px;\\n  z-index: 10000;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.search[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  position: sticky;\\n  top: 0;\\n  background-color: white;\\n  z-index: 1000;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "SendOrShareComponent", "constructor", "noteService", "kind", "classrooms", "show", "usersSendAndShared", "classroomsSendAndShared", "note", "onUsersSelect", "onClose", "usersSelected", "fromClassroom", "map", "Map", "ngOnInit", "console", "log", "id", "getClassroomNoteHasBeenSharedWith", "toString", "subscribe", "res", "onCheckedList", "event", "usersChecked", "classroom", "send", "emit", "close", "_", "ɵɵdirectiveInject", "i1", "NoteService", "_2", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "SendOrShareComponent_Template", "rf", "ctx", "ɵɵlistener", "SendOrShareComponent_Template_app_classrooms_check_list_close_1_listener", "SendOrShareComponent_Template_app_classrooms_check_list_checkedList_1_listener", "$event", "SendOrShareComponent_Template_div_click_2_listener", "ɵɵtemplate", "SendOrShareComponent_span_3_Template", "SendOrShareComponent_span_4_Template", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\note\\notes\\send-or-share\\send-or-share.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\note\\notes\\send-or-share\\send-or-share.component.html"], "sourcesContent": ["import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';\r\nimport { Classroom } from 'src/app/core/models/classroom.model';\r\nimport { Note } from 'src/app/core/models/note.model';\r\nimport { User } from 'src/app/core/models/user.model';\r\nimport { NoteService } from 'src/app/core/services/note.service';\r\n\r\n@Component({\r\n  selector: 'app-send-or-share',\r\n  templateUrl: './send-or-share.component.html',\r\n  styleUrls: ['./send-or-share.component.scss']\r\n})\r\nexport class SendOrShareComponent implements OnInit {\r\n  @Input() kind: string = '';\r\n  @Input() classrooms: Classroom[] = [];\r\n  @Input() show: boolean = false;\r\n  @Input() usersSendAndShared: User[] = [];\r\n  @Input() classroomsSendAndShared: Classroom[] = [];\r\n  @Input() note: Note = {} as Note;\r\n  @Output() public onUsersSelect = new EventEmitter();\r\n  @Output() public onClose = new EventEmitter();\r\n  usersSelected: User[] = [];\r\n  fromClassroom: Classroom = {} as Classroom\r\n  sendTerm: any;\r\n  map: Map<string, User[]> = new Map()\r\n\r\n  constructor(\r\n    private noteService: NoteService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    console.log(this.usersSendAndShared)\r\n    if(this.note.id){\r\n      this.noteService.getClassroomNoteHasBeenSharedWith(this.note.id.toString()).subscribe(res=>{\r\n        this.classroomsSendAndShared = res;\r\n      })\r\n    }else{\r\n      this.classroomsSendAndShared = [];\r\n    }\r\n    this.usersSelected = this.usersSendAndShared\r\n  }\r\n\r\n  onCheckedList(event: any) {\r\n    this.usersSelected = event.usersChecked;\r\n    this.fromClassroom = event.classroom\r\n    this.map = event.map\r\n  }\r\n\r\n  send() {\r\n    this.onUsersSelect.emit({ usersSelected: this.usersSelected, kind: this.kind, fromClassroom: this.fromClassroom, map: this.map });\r\n  }\r\n\r\n  close() {\r\n    this.onClose.emit({ kind: this.kind });\r\n  }\r\n}\r\n", "<div class=\"notes-popup\" style=\"overflow-y:scroll\">\r\n    <app-classrooms-check-list [already]=\"usersSendAndShared\" [showBlur]=true [usersAssignedTo]=\"usersSelected\" [classroomsAssignedTo]=\"classroomsSendAndShared\"  (close)=\"close()\" (checkedList)=\"onCheckedList($event)\"></app-classrooms-check-list>\r\n    <div class=\"send-button hvr-glow\" (click)=\"send()\">\r\n        <span *ngIf=\"kind === 'send'\">\r\n            Send\r\n        </span>\r\n        <span *ngIf=\"kind === 'send-and-share'\">\r\n            Send & Share\r\n        </span>\r\n    </div>\r\n</div>"], "mappings": "AAAA,SAA2CA,YAAY,QAAQ,eAAe;;;;;;ICGtEC,EAAA,CAAAC,cAAA,WAA8B;IAC1BD,EAAA,CAAAE,MAAA,aACJ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IACPH,EAAA,CAAAC,cAAA,WAAwC;IACpCD,EAAA,CAAAE,MAAA,qBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;ADGf,OAAM,MAAOC,oBAAoB;EAc/BC,YACUC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAdZ,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,UAAU,GAAgB,EAAE;IAC5B,KAAAC,IAAI,GAAY,KAAK;IACrB,KAAAC,kBAAkB,GAAW,EAAE;IAC/B,KAAAC,uBAAuB,GAAgB,EAAE;IACzC,KAAAC,IAAI,GAAS,EAAU;IACf,KAAAC,aAAa,GAAG,IAAId,YAAY,EAAE;IAClC,KAAAe,OAAO,GAAG,IAAIf,YAAY,EAAE;IAC7C,KAAAgB,aAAa,GAAW,EAAE;IAC1B,KAAAC,aAAa,GAAc,EAAe;IAE1C,KAAAC,GAAG,GAAwB,IAAIC,GAAG,EAAE;EAIhC;EAEJC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACX,kBAAkB,CAAC;IACpC,IAAG,IAAI,CAACE,IAAI,CAACU,EAAE,EAAC;MACd,IAAI,CAAChB,WAAW,CAACiB,iCAAiC,CAAC,IAAI,CAACX,IAAI,CAACU,EAAE,CAACE,QAAQ,EAAE,CAAC,CAACC,SAAS,CAACC,GAAG,IAAE;QACzF,IAAI,CAACf,uBAAuB,GAAGe,GAAG;MACpC,CAAC,CAAC;IACJ,CAAC,MAAI;MACH,IAAI,CAACf,uBAAuB,GAAG,EAAE;IACnC;IACA,IAAI,CAACI,aAAa,GAAG,IAAI,CAACL,kBAAkB;EAC9C;EAEAiB,aAAaA,CAACC,KAAU;IACtB,IAAI,CAACb,aAAa,GAAGa,KAAK,CAACC,YAAY;IACvC,IAAI,CAACb,aAAa,GAAGY,KAAK,CAACE,SAAS;IACpC,IAAI,CAACb,GAAG,GAAGW,KAAK,CAACX,GAAG;EACtB;EAEAc,IAAIA,CAAA;IACF,IAAI,CAAClB,aAAa,CAACmB,IAAI,CAAC;MAAEjB,aAAa,EAAE,IAAI,CAACA,aAAa;MAAER,IAAI,EAAE,IAAI,CAACA,IAAI;MAAES,aAAa,EAAE,IAAI,CAACA,aAAa;MAAEC,GAAG,EAAE,IAAI,CAACA;IAAG,CAAE,CAAC;EACnI;EAEAgB,KAAKA,CAAA;IACH,IAAI,CAACnB,OAAO,CAACkB,IAAI,CAAC;MAAEzB,IAAI,EAAE,IAAI,CAACA;IAAI,CAAE,CAAC;EACxC;EAAC,QAAA2B,CAAA,G;qBA1CU9B,oBAAoB,EAAAJ,EAAA,CAAAmC,iBAAA,CAAAC,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAApBlC,oBAAoB;IAAAmC,SAAA;IAAAC,MAAA;MAAAjC,IAAA;MAAAC,UAAA;MAAAC,IAAA;MAAAC,kBAAA;MAAAC,uBAAA;MAAAC,IAAA;IAAA;IAAA6B,OAAA;MAAA5B,aAAA;MAAAC,OAAA;IAAA;IAAA4B,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCV7B/C,EADJ,CAAAC,cAAA,aAAmD,mCACuK;QAAtCD,EAAlB,CAAAiD,UAAA,mBAAAC,yEAAA;UAAA,OAASF,GAAA,CAAAf,KAAA,EAAO;QAAA,EAAC,yBAAAkB,+EAAAC,MAAA;UAAA,OAAgBJ,GAAA,CAAArB,aAAA,CAAAyB,MAAA,CAAqB;QAAA,EAAC;QAACpD,EAAA,CAAAG,YAAA,EAA4B;QAClPH,EAAA,CAAAC,cAAA,aAAmD;QAAjBD,EAAA,CAAAiD,UAAA,mBAAAI,mDAAA;UAAA,OAASL,GAAA,CAAAjB,IAAA,EAAM;QAAA,EAAC;QAI9C/B,EAHA,CAAAsD,UAAA,IAAAC,oCAAA,kBAA8B,IAAAC,oCAAA,kBAGU;QAIhDxD,EADI,CAAAG,YAAA,EAAM,EACJ;;;QATyBH,EAAA,CAAAyD,SAAA,EAA8B;QAAmDzD,EAAjF,CAAA0D,UAAA,YAAAV,GAAA,CAAAtC,kBAAA,CAA8B,kBAAgB,oBAAAsC,GAAA,CAAAjC,aAAA,CAAkC,yBAAAiC,GAAA,CAAArC,uBAAA,CAAiD;QAEjJX,EAAA,CAAAyD,SAAA,GAAqB;QAArBzD,EAAA,CAAA0D,UAAA,SAAAV,GAAA,CAAAzC,IAAA,YAAqB;QAGrBP,EAAA,CAAAyD,SAAA,EAA+B;QAA/BzD,EAAA,CAAA0D,UAAA,SAAAV,GAAA,CAAAzC,IAAA,sBAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}