{"ast": null, "code": "import { createPlugin } from '@fullcalendar/core/index.js';\nimport { DayTimeColsView } from './internal.js';\nimport '@fullcalendar/core/internal.js';\nimport '@fullcalendar/core/preact.js';\nimport '@fullcalendar/daygrid/internal.js';\nconst OPTION_REFINERS = {\n  allDaySlot: Boolean\n};\nvar index = createPlugin({\n  name: '@fullcalendar/timegrid',\n  initialView: 'timeGridWeek',\n  optionRefiners: OPTION_REFINERS,\n  views: {\n    timeGrid: {\n      component: DayTimeColsView,\n      usesMinMaxTime: true,\n      allDaySlot: true,\n      slotDuration: '00:30:00',\n      slotEventOverlap: true // a bad name. confused with overlap/constraint system\n    },\n    timeGridDay: {\n      type: 'timeGrid',\n      duration: {\n        days: 1\n      }\n    },\n    timeGridWeek: {\n      type: 'timeGrid',\n      duration: {\n        weeks: 1\n      }\n    }\n  }\n});\nexport { index as default };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}