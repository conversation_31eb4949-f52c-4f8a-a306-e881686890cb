{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"primeng/inputswitch\";\nfunction ClassroomStatusFiltersComponent_ng_container_0_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"label\", 4);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-inputSwitch\", 5);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ClassroomStatusFiltersComponent_ng_container_0_div_2_Template_p_inputSwitch_ngModelChange_3_listener($event) {\n      const switch_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      i0.ɵɵtwoWayBindingSet(switch_r2.value, $event) || (switch_r2.value = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_0_div_2_Template_p_inputSwitch_onChange_3_listener() {\n      const switch_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(switch_r2.label));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const switch_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(switch_r2.label);\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", switch_r2.value);\n  }\n}\nfunction ClassroomStatusFiltersComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 1);\n    i0.ɵɵtemplate(2, ClassroomStatusFiltersComponent_ng_container_0_div_2_Template, 4, 2, \"div\", 2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.switchesWithLabels);\n  }\n}\nfunction ClassroomStatusFiltersComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 6)(2, \"div\", 3)(3, \"label\", 4);\n    i0.ɵɵtext(4, \"Ongoing\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_1_Template_p_inputSwitch_onChange_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"Ongoing\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 3)(7, \"label\", 4);\n    i0.ɵɵtext(8, \"Completed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_1_Template_p_inputSwitch_onChange_9_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"Completed\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 3)(11, \"label\", 4);\n    i0.ɵɵtext(12, \"Expired\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_1_Template_p_inputSwitch_onChange_13_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"Expired\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 3)(15, \"label\", 4);\n    i0.ɵɵtext(16, \"All\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_1_Template_p_inputSwitch_onChange_17_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"All\"));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.Ongoing);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.Completed);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.Expired);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.All);\n  }\n}\nfunction ClassroomStatusFiltersComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 8)(3, \"label\", 4);\n    i0.ɵɵtext(4, \"Ongoing\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_2_Template_p_inputSwitch_onChange_5_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"Active\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 8)(7, \"label\", 4);\n    i0.ɵɵtext(8, \"Completed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_2_Template_p_inputSwitch_onChange_9_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"Completed\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 8)(11, \"label\", 4);\n    i0.ɵɵtext(12, \"Dismissed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_2_Template_p_inputSwitch_onChange_13_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"Dismissed\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 8)(15, \"label\", 4);\n    i0.ɵɵtext(16, \"All\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_2_Template_p_inputSwitch_onChange_17_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"All\"));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.Active);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.Completed);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.Dismissed);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.All);\n  }\n}\nfunction ClassroomStatusFiltersComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 1)(2, \"div\", 3)(3, \"label\", 9);\n    i0.ɵɵtext(4, \"Arranged\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_3_Template_p_inputSwitch_onChange_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"Arranged\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 3)(7, \"label\", 9);\n    i0.ɵɵtext(8, \"Completed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_3_Template_p_inputSwitch_onChange_9_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"Completed\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 3)(11, \"label\", 9);\n    i0.ɵɵtext(12, \"Canceled\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_3_Template_p_inputSwitch_onChange_13_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"Canceled\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 3)(15, \"label\", 9);\n    i0.ɵɵtext(16, \"No Show\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_3_Template_p_inputSwitch_onChange_17_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"NoShow\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 3)(19, \"label\", 9);\n    i0.ɵɵtext(20, \"All\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"p-inputSwitch\", 7);\n    i0.ɵɵlistener(\"onChange\", function ClassroomStatusFiltersComponent_ng_container_3_Template_p_inputSwitch_onChange_21_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleSwitch(\"All\"));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.Arranged);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.Completed);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.Canceled);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.NoShow);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngModel\", ctx_r2.switches.All);\n  }\n}\nexport let ClassroomStatusFiltersComponent = /*#__PURE__*/(() => {\n  class ClassroomStatusFiltersComponent {\n    constructor() {\n      this.isTrial = false;\n      this.isLessonsFilter = false;\n      this.switchToggled = new EventEmitter();\n    }\n    ngOnInit() {\n      console.log(this.switchesWithLabels);\n    }\n    ngOnChanges() {\n      console.log(this.switches);\n    }\n    toggleSwitch(switchName) {\n      this.switchToggled.emit(switchName);\n    }\n    static #_ = this.ɵfac = function ClassroomStatusFiltersComponent_Factory(t) {\n      return new (t || ClassroomStatusFiltersComponent)();\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ClassroomStatusFiltersComponent,\n      selectors: [[\"app-classroom-status-filters\"]],\n      inputs: {\n        switches: \"switches\",\n        switchesWithLabels: \"switchesWithLabels\",\n        isTrial: \"isTrial\",\n        isLessonsFilter: \"isLessonsFilter\"\n      },\n      outputs: {\n        switchToggled: \"switchToggled\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 4,\n      vars: 4,\n      consts: [[4, \"ngIf\"], [1, \"flex\", \"flex-wrap\"], [\"class\", \"field-checkbox my-0\", 4, \"ngFor\", \"ngForOf\"], [1, \"field-checkbox\", \"my-0\"], [1, \"lesson-filter-label\", \"font-xs\", \"mr-2\"], [1, \"flex\", \"small-input-switch\", \"p-mr-2\", 3, \"ngModelChange\", \"onChange\", \"ngModel\"], [1, \"flex\", \"flex-wrap\", \"gap-2\", \"sm:gap-3\"], [1, \"flex\", \"small-input-switch\", \"p-mr-2\", 3, \"onChange\", \"ngModel\"], [1, \"field-checkbox\", \"my-2\", \"sm:my-0\"], [1, \"lesson-filter-label\", \"font-xs\", \"mr-1\"]],\n      template: function ClassroomStatusFiltersComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ClassroomStatusFiltersComponent_ng_container_0_Template, 3, 1, \"ng-container\", 0)(1, ClassroomStatusFiltersComponent_ng_container_1_Template, 18, 4, \"ng-container\", 0)(2, ClassroomStatusFiltersComponent_ng_container_2_Template, 18, 4, \"ng-container\", 0)(3, ClassroomStatusFiltersComponent_ng_container_3_Template, 22, 5, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.switchesWithLabels);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isTrial && !ctx.isLessonsFilter && !ctx.switchesWithLabels);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isTrial);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLessonsFilter);\n        }\n      },\n      dependencies: [i1.NgForOf, i1.NgIf, i2.NgControlStatus, i2.NgModel, i3.InputSwitch]\n    });\n  }\n  return ClassroomStatusFiltersComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}