{"ast": null, "code": "import { EventEmitter, inject } from '@angular/core';\nimport { HttpEventType, HttpHeaders } from '@angular/common/http';\nimport { environment } from 'src/environments/environment';\nimport { SubSink } from 'subsink';\nimport { DomSanitizer } from '@angular/platform-browser';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"src/app/core/services/user.service\";\nimport * as i3 from \"src/app/core/services/library.service\";\nimport * as i4 from \"src/app/core/services/toast.service\";\nimport * as i5 from \"src/app/core/services/auth.service\";\nimport * as i6 from \"src/app/core/services/general.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"ngx-image-cropper\";\nimport * as i9 from \"primeng/button\";\nimport * as i10 from \"primeng/ripple\";\nimport * as i11 from \"../../core/directives/dnd.directive\";\nimport * as i12 from \"./choose-tags/choose-tags.component\";\nfunction UploadFilesComponent_div_0_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"img\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r1.fileDropImageWidth ? ctx_r1.fileDropImageWidth : 100, \"%\");\n    i0.ɵɵproperty(\"src\", ctx_r1.fileDropImage, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction UploadFilesComponent_div_0_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 13);\n  }\n}\nfunction UploadFilesComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 7);\n    i0.ɵɵlistener(\"fileDropped\", function UploadFilesComponent_div_0_Template_div_fileDropped_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFileDropped($event));\n    });\n    i0.ɵɵelementStart(2, \"div\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 8);\n    i0.ɵɵlistener(\"change\", function UploadFilesComponent_div_0_Template_input_change_4_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.fileBrowseHandler($event.target.files));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, UploadFilesComponent_div_0_ng_container_5_Template, 2, 3, \"ng-container\", 9)(6, UploadFilesComponent_div_0_ng_template_6_Template, 1, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(8, \"div\", 10);\n    i0.ɵɵtext(9, \" Drag and drop files here or \");\n    i0.ɵɵelementStart(10, \"label\", 11);\n    i0.ɵɵtext(11, \"Browse\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const defaultImage_r3 = i0.ɵɵreference(7);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.title);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"id\", ctx_r1.title);\n    i0.ɵɵproperty(\"multiple\", ctx_r1.multiple);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.fileDropImage)(\"ngIfElse\", defaultImage_r3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵpropertyInterpolate(\"for\", ctx_r1.title);\n  }\n}\nfunction UploadFilesComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\")(2, \"input\", 8);\n    i0.ɵɵlistener(\"change\", function UploadFilesComponent_div_1_Template_input_change_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.fileBrowseHandler($event.target.files));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"label\", 14);\n    i0.ɵɵtext(4, \"Browse\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate(\"id\", ctx_r1.title);\n    i0.ɵɵproperty(\"multiple\", ctx_r1.multiple);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"for\", ctx_r1.title);\n  }\n}\nfunction UploadFilesComponent_div_2_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"img\", 22);\n    i0.ɵɵtext(2, \"No file chosen\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UploadFilesComponent_div_2_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 23);\n    i0.ɵɵelement(2, \"div\")(3, \"img\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 19);\n    i0.ɵɵlistener(\"change\", function UploadFilesComponent_div_2_ng_container_10_Template_input_change_4_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.fileChangeEvent($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"image-cropper\", 25);\n    i0.ɵɵlistener(\"imageCropped\", function UploadFilesComponent_div_2_ng_container_10_Template_image_cropper_imageCropped_5_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.imageCropped($event));\n    })(\"imageLoaded\", function UploadFilesComponent_div_2_ng_container_10_Template_image_cropper_imageLoaded_5_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.imageLoaded($event));\n    })(\"cropperReady\", function UploadFilesComponent_div_2_ng_container_10_Template_image_cropper_cropperReady_5_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.cropperReady());\n    })(\"loadImageFailed\", function UploadFilesComponent_div_2_ng_container_10_Template_image_cropper_loadImageFailed_5_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.loadImageFailed());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r1.croppedImage, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"imageChangedEvent\", ctx_r1.imageChangedEvent)(\"maintainAspectRatio\", true)(\"aspectRatio\", 4 / 3)(\"aspectRatio\", 1 / 1)(\"resizeToWidth\", 256)(\"cropperMinWidth\", 128);\n  }\n}\nfunction UploadFilesComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 15)(2, \"div\", 16)(3, \"button\", 17);\n    i0.ɵɵlistener(\"click\", function UploadFilesComponent_div_2_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const filePicker_r6 = i0.ɵɵreference(8);\n      return i0.ɵɵresetView(filePicker_r6.click());\n    });\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Choose file\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"img\", 18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"input\", 19, 1);\n    i0.ɵɵlistener(\"change\", function UploadFilesComponent_div_2_Template_input_change_7_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.fileBrowseHandlerProfile($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, UploadFilesComponent_div_2_div_9_Template, 3, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, UploadFilesComponent_div_2_ng_container_10_Template, 6, 7, \"ng-container\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.croppedImage === \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showCropper);\n  }\n}\nfunction UploadFilesComponent_div_3_div_1_div_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"img\", 37);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", ctx_r1.fileAfterImageWidth ? ctx_r1.fileAfterImageWidth : null, \"%\");\n    i0.ɵɵproperty(\"src\", ctx_r1.fileAfterImage, i0.ɵɵsanitizeUrl);\n  }\n}\nfunction UploadFilesComponent_div_3_div_1_div_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵelement(1, \"img\", 39);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UploadFilesComponent_div_3_div_1_div_1_p_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 40);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r9 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.formatBytes(file_r9 == null ? null : file_r9.file.size), \" \");\n  }\n}\nfunction UploadFilesComponent_div_3_div_1_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41);\n    i0.ɵɵelement(1, \"div\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r9 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", file_r9.progress + \"%\");\n  }\n}\nfunction UploadFilesComponent_div_3_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtemplate(1, UploadFilesComponent_div_3_div_1_div_1_ng_container_1_Template, 2, 3, \"ng-container\", 9)(2, UploadFilesComponent_div_3_div_1_div_1_ng_template_2_Template, 2, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(4, \"div\", 30)(5, \"p\", 31)(6, \"a\", 32);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, UploadFilesComponent_div_3_div_1_div_1_p_8_Template, 2, 1, \"p\", 33)(9, UploadFilesComponent_div_3_div_1_div_1_div_9_Template, 2, 2, \"div\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 35);\n    i0.ɵɵlistener(\"click\", function UploadFilesComponent_div_3_div_1_div_1_Template_div_click_10_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const i_r10 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.deleteFile(i_r10));\n    });\n    i0.ɵɵelement(11, \"i\", 36);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const defaultAfterImage_r11 = i0.ɵɵreference(3);\n    const file_r9 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.fileAfterImage)(\"ngIfElse\", defaultAfterImage_r11);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"href\", ctx_r1.generalService.getDomainFileNamePath(file_r9 == null ? null : file_r9.file.name), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.generalService.getFileNameAndExtension(file_r9 == null ? null : file_r9.file.name), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", file_r9.file.size);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", file_r9.progress);\n  }\n}\nfunction UploadFilesComponent_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, UploadFilesComponent_div_3_div_1_div_1_Template, 12, 6, \"div\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", file_r9.file.name);\n  }\n}\nfunction UploadFilesComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtemplate(1, UploadFilesComponent_div_3_div_1_Template, 2, 1, \"div\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.files);\n  }\n}\nfunction UploadFilesComponent_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"div\", 45)(3, \"div\", 46)(4, \"strong\");\n    i0.ɵɵtext(5, \"Tags\\u00A0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" apply to all \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"app-choose-tags\", 47);\n    i0.ɵɵlistener(\"chooseTag\", function UploadFilesComponent_div_4_div_1_Template_app_choose_tags_chooseTag_7_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onChooseTag($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" You have selected \", ctx_r1.files.length, \" files \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"applyToAll\", true);\n  }\n}\nfunction UploadFilesComponent_div_4_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"div\", 49);\n    i0.ɵɵelement(2, \"img\", 39);\n    i0.ɵɵelementStart(3, \"label\", 50);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(5, \"hr\");\n    i0.ɵɵelementStart(6, \"app-choose-tags\", 51);\n    i0.ɵɵlistener(\"chooseTag\", function UploadFilesComponent_div_4_div_2_Template_app_choose_tags_chooseTag_6_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onChooseTag($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r14 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", file_r14.file.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"applyToAll\", false)(\"file\", file_r14);\n  }\n}\nfunction UploadFilesComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵtemplate(1, UploadFilesComponent_div_4_div_1_Template, 8, 2, \"div\", 3)(2, UploadFilesComponent_div_4_div_2_Template, 7, 3, \"div\", 44);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.files.length > 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.files);\n  }\n}\nfunction UploadFilesComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function UploadFilesComponent_div_5_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.submit());\n    });\n    i0.ɵɵelementStart(2, \"div\", 54)(3, \"span\", 55);\n    i0.ɵɵtext(4, \"Upload\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"img\", 56);\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport let UploadFilesComponent = /*#__PURE__*/(() => {\n  class UploadFilesComponent {\n    constructor(http, userService, libraryService, toast, authService, generalService) {\n      this.http = http;\n      this.userService = userService;\n      this.libraryService = libraryService;\n      this.toast = toast;\n      this.authService = authService;\n      this.generalService = generalService;\n      this.isProfile = false;\n      this.isTeacherApplication = false;\n      this.isSick = false;\n      this.files = [];\n      this.teacherFilesLength = 0;\n      this.postUrl = \"\";\n      this.title = \"\";\n      this.buttonUI = false;\n      this.dndUI = false;\n      this.checkboxUI = false;\n      this.multiple = true;\n      this.showSubmitButton = false;\n      this.inHomeworkTask = false;\n      this.homeworkTaskStudentId = \"\";\n      this.clearFiles = false;\n      this.onUploadFinished = new EventEmitter();\n      this.onDeleteFileClicked = new EventEmitter();\n      this.tagChoosen = new EventEmitter();\n      this.onSubmit = new EventEmitter();\n      this.subs = new SubSink();\n      this.progress = [];\n      this.message = \"\";\n      this.showCropper = false;\n      this.croppedImage = \"\";\n      this.profileFile = {};\n      this.profileFileName = \"\";\n      this.uploadedFile = \"\";\n      this.sanitizer = inject(DomSanitizer);\n      this.uploadFile = files => {\n        console.log(files);\n        if (this.isProfile && !files.name) {\n          this.onUploadFinished.emit({\n            filePath: \"\",\n            levels: [],\n            categories: []\n          });\n          return;\n        }\n        if (!this.isProfile && files.length === 0) {\n          return;\n        }\n        if (!this.isProfile && !this.isTeacherApplication) {\n          for (const file of files) {\n            let myFile = {\n              file: file,\n              progress: 0,\n              index: this.teacherFilesLength\n            };\n            this.teacherFilesLength++;\n            // this.files.push(myFile);\n            this.userService.setNewFileUpload(myFile);\n          }\n        }\n        if (!this.isProfile && this.isTeacherApplication) {\n          for (const file of files) {\n            let myFile = {\n              file: file,\n              progress: 0,\n              index: this.teacherFilesLength\n            };\n            // this.teacherFilesLength++;\n            // this.files.push(myFile);\n            // this.userService.setNewFileUpload(myFile);\n            this.upload(myFile);\n          }\n        } else {\n          let myFile = {\n            file: files,\n            progress: 0,\n            index: this.teacherFilesLength\n          };\n          this.upload(myFile);\n        }\n      };\n    }\n    ngOnInit() {\n      // this.uploadFile([{lastModified: 1680183216555,\n      //   lastModifiedDate\n      //   : \n      //   'Thu Mar 30 2023 16:33:36 GMT+0300 (Eastern European Summer Time)',\n      //   name  :  \"calendar (-41.png\",\n      //   size: 323,\n      //   type : \"image/png\",\n      //   webkitRelativePath: \"\"}])\n      // this is happening because of cropped image. we cannot imidiatly upload it until the user presses save at profile info component\n      this.subs.sink = this.userService.updateProfile.subscribe(res => {\n        if (res) {\n          console.log(this.profileFile);\n          this.uploadFile(this.profileFile);\n        }\n      });\n      this.subs.sink = this.userService.uploadFiles.subscribe(res => {\n        if (res) {\n          this.files = [];\n        }\n      });\n      this.subs.sink = this.userService.newFileUpload.subscribe(res => {\n        if (res && !this.generalService.isObjectEmpty(res)) {\n          console.log(res);\n          this.files.push(res);\n          this.upload(res);\n        }\n      });\n    }\n    ngOnDestroy() {\n      //when we land to this cocmponent the value must be false so it wont get called in ngOnInit\n      this.userService.setUpdateProfile(false);\n      this.subs.unsubscribe();\n    }\n    /**\n     * on file drop handler\n     */\n    onFileDropped($event) {\n      this.uploadFile($event);\n    }\n    /**\n     * Delete file from files list\n     * @param index (File index)\n     */\n    deleteFile(index) {\n      this.onDeleteFileClicked.emit({\n        file: this.files[index]\n      });\n      this.files.splice(index, 1);\n    }\n    /**\n     * handle file from browsing\n     */\n    fileBrowseHandlerProfile(event) {\n      this.profileFileName = event.target.files[0].name;\n      this.showCropper = true;\n      this.imageChangedEvent = event;\n      // document.getElementById('cropped-img')!.style.borderColor = \"var(--main-color)\";\n    }\n    /**\n     * handle file from browsing\n     */\n    fileBrowseHandler(event) {\n      console.log(event);\n      this.uploadFile(event);\n    }\n    upload(myFile) {\n      let headers;\n      const formData = new FormData();\n      formData.append('file', myFile.file, this.replaceSpecialCharactersForFilename(myFile.file.name));\n      headers = new HttpHeaders({\n        'documentType': myFile.file.name.split('.').pop(),\n        \"Authorization\": \"Bearer \" + this.authService.getToken()\n      });\n      console.log(headers);\n      if (this.assignToClassroom) {\n        for (let user of this.assignToClassroom.users) {\n          let endpointExtras = this.inHomeworkTask ? \"?studentId=\" + user.id : \"\";\n          this.uploadRequest(headers, endpointExtras, formData, myFile);\n        }\n      } else if (this.assignToUser) {\n        let endpointExtras = \"?studentId=\" + this.assignToUser.id;\n        console.log(endpointExtras);\n        this.uploadRequest(headers, endpointExtras, formData, myFile);\n      } else {\n        let endpointExtras = this.inHomeworkTask ? \"?studentId=\" + this.homeworkTaskStudentId : \"\";\n        this.uploadRequest(headers, endpointExtras, formData, myFile);\n      }\n    }\n    uploadRequest(headers, endpointExtras, formData, myFile) {\n      this.subs.add(this.http.post(environment.apiUrl + this.postUrl + endpointExtras, formData, {\n        headers: headers,\n        reportProgress: true,\n        observe: 'events'\n      }).subscribe(event => {\n        console.log(event);\n        if (event.type === HttpEventType.UploadProgress) {\n          myFile.progress = Math.round(100 * event.loaded / event.total);\n        } else if (event.type === HttpEventType.Response) {\n          this.toast.setShowToastmessage({\n            severity: 'success',\n            summary: '',\n            detail: 'Uploaded successfully!'\n          });\n          this.uploadedFile = event.body;\n          if (this.isProfile) {\n            console.log('GEGEG');\n            this.showCropper = false;\n          }\n          this.onUploadFinished.emit({\n            filePath: event.body,\n            task: this.task\n          });\n        } else {}\n      }, err => {\n        console.log(err);\n      }));\n    }\n    formatBytes(bytes, decimals) {\n      if (bytes === 0) {\n        return '0 Bytes';\n      }\n      const k = 1024;\n      const dm = decimals <= 0 ? 0 : decimals || 2;\n      const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];\n      const i = Math.floor(Math.log(bytes) / Math.log(k));\n      return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];\n    }\n    onChooseTag(event) {\n      this.tagChoosen.emit(event);\n    }\n    imageCropped(event) {\n      console.log(event);\n      this.croppedImage = this.sanitizer.bypassSecurityTrustUrl(event.objectUrl || event.base64 || '');\n      const file = new File([event.blob], this.profileFileName, {\n        type: event.blob.type\n      });\n      this.profileFile = new File([file], this.profileFileName);\n      const reader = new FileReader();\n      reader.onload = () => {\n        this.croppedImage = reader.result;\n      };\n      reader.readAsDataURL(file);\n    }\n    imageLoaded(event) {\n      console.log(event);\n      // show cropper\n    }\n    cropperReady(event) {\n      // cropper ready\n    }\n    loadImageFailed() {\n      // show message\n    }\n    submit() {\n      this.onSubmit.emit();\n    }\n    /**\n     * Replaces special characters in a given filename with a specified replacement character.\n     * @param {string} fileName - The original filename.\n     * @returns {string} - The modified filename with special characters replaced.\n     */\n    replaceSpecialCharactersForFilename(fileName) {\n      const specialCharacters = /[^\\w\\s.-]/g;\n      const replacementCharacter = \"_\";\n      return fileName.replace(specialCharacters, replacementCharacter);\n    }\n    static #_ = this.ɵfac = function UploadFilesComponent_Factory(t) {\n      return new (t || UploadFilesComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.LibraryService), i0.ɵɵdirectiveInject(i4.ToastService), i0.ɵɵdirectiveInject(i5.AuthService), i0.ɵɵdirectiveInject(i6.GeneralService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UploadFilesComponent,\n      selectors: [[\"app-upload-files\"]],\n      inputs: {\n        isProfile: \"isProfile\",\n        isTeacherApplication: \"isTeacherApplication\",\n        isSick: \"isSick\",\n        files: \"files\",\n        teacherFilesLength: \"teacherFilesLength\",\n        postUrl: \"postUrl\",\n        title: \"title\",\n        buttonUI: \"buttonUI\",\n        dndUI: \"dndUI\",\n        checkboxUI: \"checkboxUI\",\n        multiple: \"multiple\",\n        showSubmitButton: \"showSubmitButton\",\n        inHomeworkTask: \"inHomeworkTask\",\n        homeworkTaskStudentId: \"homeworkTaskStudentId\",\n        assignToClassroom: \"assignToClassroom\",\n        assignToUser: \"assignToUser\",\n        task: \"task\",\n        filePath: \"filePath\",\n        fileDropImage: \"fileDropImage\",\n        fileDropImageWidth: \"fileDropImageWidth\",\n        fileAfterImage: \"fileAfterImage\",\n        fileAfterImageWidth: \"fileAfterImageWidth\",\n        clearFiles: \"clearFiles\"\n      },\n      outputs: {\n        onUploadFinished: \"onUploadFinished\",\n        onDeleteFileClicked: \"onDeleteFileClicked\",\n        tagChoosen: \"tagChoosen\",\n        onSubmit: \"onSubmit\"\n      },\n      decls: 6,\n      vars: 6,\n      consts: [[\"defaultImage\", \"\"], [\"filePicker\", \"\"], [\"defaultAfterImage\", \"\"], [4, \"ngIf\"], [\"class\", \"files-list lg:mt-2\", 4, \"ngIf\"], [\"class\", \"library-uploads\", 4, \"ngIf\"], [\"class\", \"flex justify-content-center align-items-center mt-2\", 4, \"ngIf\"], [\"appDnd\", \"\", 1, \"files-container\", 3, \"fileDropped\"], [\"type\", \"file\", 3, \"change\", \"id\", \"multiple\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"drop-text-subtitle\"], [3, \"for\"], [3, \"src\"], [\"src\", \"/assets/icons/drop.svg\", 2, \"width\", \"60%\"], [1, \"light-purple-button\", 2, \"color\", \"white !important\", 3, \"for\"], [1, \"flex\", \"align-items-center\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-outlined\", \"p-button-rounded\", \"gradient-input-like-btn\", \"no-label\", \"text-center\", \"flex\", \"align-items-center\", \"justify-content-center\", \"px-6\", 3, \"click\"], [\"src\", \"/assets/icons/profile-upload-icon.svg\", \"height\", \"20\", 1, \"pl-3\"], [\"type\", \"file\", 3, \"change\"], [\"class\", \"flex align-items-center z-1\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"z-1\"], [\"src\", \"/assets/icons/warning-sign.svg\", \"height\", \"24\", 1, \"pr-3\"], [1, \"flex\", \"flex-row\", \"align-items-center\"], [\"id\", \"cropped-img\", 1, \"cropped-img\", 2, \"border-color\", \"var(--primary-color)\", 3, \"src\"], [\"format\", \"png\", 3, \"imageCropped\", \"imageLoaded\", \"cropperReady\", \"loadImageFailed\", \"imageChangedEvent\", \"maintainAspectRatio\", \"aspectRatio\", \"resizeToWidth\", \"cropperMinWidth\"], [1, \"files-list\", \"lg:mt-2\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"single-file section-bg-gradient flex\", 4, \"ngIf\"], [1, \"single-file\", \"section-bg-gradient\", \"flex\"], [1, \"info\"], [1, \"name\"], [\"target\", \"_blank\", 1, \"text-primary\", 3, \"href\"], [\"class\", \"size\", 4, \"ngIf\"], [\"class\", \"progress-cont\", 4, \"ngIf\"], [1, \"delete\", 3, \"click\"], [1, \"pi\", \"pi-times-circle\", \"text-xl\", \"text-primary\"], [1, \"mr-2\", 3, \"src\"], [1, \"file-icon\", 2, \"width\", \"50px\"], [\"src\", \"/assets/icons/library/surface1.svg\"], [1, \"size\"], [1, \"progress-cont\"], [1, \"progress\"], [1, \"library-uploads\"], [\"class\", \"library-upload p-3\", 4, \"ngFor\", \"ngForOf\"], [1, \"library-upload\"], [1, \"font-sm\", \"p-3\"], [3, \"chooseTag\", \"applyToAll\"], [1, \"library-upload\", \"p-3\"], [1, \"library-upload-title\"], [1, \"font-sm\", \"text-800\", \"in-title\", \"p-2\"], [3, \"chooseTag\", \"applyToAll\", \"file\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"mt-2\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", 1, \"p-button\", \"p-button-sm\", \"p-button-rounded\", \"link-main-color-active\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"px-3\"], [1, \"text-white\", \"py-1\"], [\"src\", \"/assets/icons/library/upload-file-lib.svg\", \"width\", \"26\", 1, \"pl-2\"]],\n      template: function UploadFilesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, UploadFilesComponent_div_0_Template, 12, 6, \"div\", 3)(1, UploadFilesComponent_div_1_Template, 5, 3, \"div\", 3)(2, UploadFilesComponent_div_2_Template, 11, 2, \"div\", 3)(3, UploadFilesComponent_div_3_Template, 2, 1, \"div\", 4)(4, UploadFilesComponent_div_4_Template, 3, 2, \"div\", 5)(5, UploadFilesComponent_div_5_Template, 6, 0, \"div\", 6);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.dndUI);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isSick);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isProfile);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.dndUI);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.checkboxUI);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showSubmitButton);\n        }\n      },\n      dependencies: [i7.NgForOf, i7.NgIf, i8.ImageCropperComponent, i9.ButtonDirective, i10.Ripple, i11.DndDirective, i12.ChooseTagsComponent],\n      styles: [\".modal[_ngcontent-%COMP%]{min-width:50vw}[_nghost-%COMP%]     .p-dropdown:not(.p-disabled):hover{border:2px solid rgb(228,185,84)}  .no-label>.p-button-label{display:none}input[type=file][_ngcontent-%COMP%]{visibility:hidden;width:10px}input[type=date][_ngcontent-%COMP%]{position:relative;padding:10px}input[type=date][_ngcontent-%COMP%]::-webkit-calendar-picker-indicator{color:transparent;background:none;z-index:1}input[type=date][_ngcontent-%COMP%]:before{color:transparent;background:none;display:block;font-family:FontAwesome;content:\\\"\\\\f073\\\";width:20px;height:25px;position:absolute;top:12px;right:12px;color:#999}.image-preview[_ngcontent-%COMP%]{height:10rem;margin:1rem 0;border-radius:50%;border:3px solid var(--main-color)}.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{height:100%}[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-header{padding:clamp(.38rem,.47vw + .28rem,.75rem)}[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-items .p-dropdown-item{margin-bottom:clamp(.38rem,.47vw + .28rem,.75rem)}.country-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:clamp(.75rem,.31vw + .69rem,1rem);font-size:clamp(.88rem,.16vw + .84rem,1rem)}.country-item[_ngcontent-%COMP%]   .country-name[_ngcontent-%COMP%]{white-space:pre-line}.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;display:inline-block;vertical-align:middle}.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;clip-path:circle()}.country-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:32px;margin-right:.5rem}.country-item[_ngcontent-%COMP%]   img.smaller[_ngcontent-%COMP%]{width:16px}.info-element[_ngcontent-%COMP%]{padding:10px 0}.responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}.files-container[_ngcontent-%COMP%]{padding:2rem;border-radius:12px;text-align:center;position:relative;background:#f2f6ffad;background-image:repeating-linear-gradient(to right,rgba(52,131,226,.61) 0%,rgba(52,131,226,.61) 50%,transparent 50%,transparent 100%),repeating-linear-gradient(to right,rgba(52,131,226,.61) 0%,rgba(52,131,226,.61) 50%,transparent 50%,transparent 100%),repeating-linear-gradient(to bottom,rgba(52,131,226,.61) 0%,rgba(52,131,226,.61) 50%,transparent 50%,transparent 100%),repeating-linear-gradient(to bottom,rgba(52,131,226,.61) 0%,rgba(52,131,226,.61) 50%,transparent 50%,transparent 100%);background-position:left top,left bottom,left top,right top;background-repeat:repeat-x,repeat-x,repeat-y,repeat-y;background-size:14px 2px,14px 2px,2px 12px,2px 14px}.files-container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]{opacity:0;position:absolute;z-index:2;width:100%;height:100%;top:0;left:0}.files-container[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:20px;font-weight:600;color:#38424c}.files-container[_ngcontent-%COMP%]   .drop-text-subtitle[_ngcontent-%COMP%]{margin-top:12px;font-size:16px;font-weight:500;font-stretch:normal;font-style:normal;line-height:normal;letter-spacing:1.5px;text-align:left;color:#000;text-align:center}label[_ngcontent-%COMP%]{color:var(--primary-color);font-weight:700;cursor:pointer}.fileover[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_shake 1s;animation-iteration-count:infinite}.single-file[_ngcontent-%COMP%]{display:flex;padding:.5rem;justify-content:space-between;align-items:center;margin-bottom:1rem;flex-grow:1;word-break:break-all}.single-file[_ngcontent-%COMP%]   .delete[_ngcontent-%COMP%]{display:flex;cursor:pointer;align-self:flex-start}.single-file[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%]{font-size:14px;font-weight:500;color:#353f4a;margin:0}.single-file[_ngcontent-%COMP%]   .size[_ngcontent-%COMP%]{font-size:12px;font-weight:500;color:#a4a4a4;margin:0;margin-bottom:.25rem}.single-file[_ngcontent-%COMP%]   .info[_ngcontent-%COMP%]{width:100%}.progress-cont[_ngcontent-%COMP%]{height:7px;width:100%;border-radius:4px;background-color:#d0d0d0;position:relative}.progress-cont[_ngcontent-%COMP%]   .progress[_ngcontent-%COMP%]{width:0;height:100%;position:absolute;z-index:1;top:0;left:0;border-radius:4px;background-color:#4c97cb;transition:.5s all}@keyframes _ngcontent-%COMP%_shake{0%{transform:translate(1px,1px) rotate(0)}10%{transform:translate(-1px,-2px) rotate(-1deg)}20%{transform:translate(-3px) rotate(1deg)}30%{transform:translate(3px,2px) rotate(0)}40%{transform:translate(1px,-1px) rotate(1deg)}50%{transform:translate(-1px,2px) rotate(-1deg)}60%{transform:translate(-3px,1px) rotate(0)}70%{transform:translate(3px,1px) rotate(-1deg)}80%{transform:translate(-1px,-1px) rotate(1deg)}90%{transform:translate(1px,2px) rotate(0)}to{transform:translate(1px,-2px) rotate(-1deg)}}.upload[_ngcontent-%COMP%]{font-weight:700;color:var(--light-purple);line-height:36px;display:flex;align-items:center;justify-content:center;margin-top:15px}.item-img[_ngcontent-%COMP%]{margin-left:15px}.spinner[_ngcontent-%COMP%]{position:absolute;margin-left:10px}[_nghost-%COMP%]     .mat-progress-spinner circle, [_nghost-%COMP%]     .mat-spinner circle{stroke:var(--light-purple)}.cropped-img[_ngcontent-%COMP%]{border:3px solid white;width:200px;border-radius:50%}@media screen and (max-width: 1366px){.cropped-img[_ngcontent-%COMP%]{border:3px solid white;width:200px;border-radius:50%;position:absolute;right:0}.test[_ngcontent-%COMP%]{width:50%!important}}@media screen and (max-width: 768px){.cropped-img[_ngcontent-%COMP%]{width:100px}}.white-button[_ngcontent-%COMP%]{border:1px solid var(--my-gray);background-image:linear-gradient(#fff,#e3ebfd);border-radius:50px;text-align:center;padding:10px;width:220px}.gradient-input-like-btn[_ngcontent-%COMP%]{border:none;background-image:linear-gradient(#fff,#e3ebfd);box-shadow:none}.library-uploads[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;flex-direction:column}.library-uploads[_ngcontent-%COMP%]   .library-upload[_ngcontent-%COMP%]{border-radius:32px;border:1px solid #2E3D90;margin-top:15px;width:100%}.library-uploads[_ngcontent-%COMP%]   .library-upload[_ngcontent-%COMP%]   .library-upload-title[_ngcontent-%COMP%]{display:flex;align-items:center;font-size:18px}.library-uploads[_ngcontent-%COMP%]   .library-upload[_ngcontent-%COMP%]   .library-upload-title[_ngcontent-%COMP%]   .in-title[_ngcontent-%COMP%]{border-radius:20px;background:#fff}.library-uploads[_ngcontent-%COMP%]   .library-upload[_ngcontent-%COMP%]   .library-upload-title[_ngcontent-%COMP%] > img[_ngcontent-%COMP%]{width:30px;margin-right:15px}\"]\n    });\n  }\n  return UploadFilesComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}