{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { UntypedFormArray, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { UserAvailabilityType } from 'src/app/core/models/user.model';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"src/app/core/services/teacher-application.service\";\nimport * as i5 from \"src/app/core/services/user.service\";\nimport * as i6 from \"src/app/core/services/toast.service\";\nimport * as i7 from \"src/app/core/services/auth.service\";\nimport * as i8 from \"src/app/core/services/calendar.service\";\nimport * as i9 from \"src/app/core/services/general.service\";\nimport * as i10 from \"primeng/dropdown\";\nimport * as i11 from \"primeng/api\";\nimport * as i12 from \"primeng/calendar\";\nimport * as i13 from \"primeng/button\";\nimport * as i14 from \"primeng/tooltip\";\nimport * as i15 from \"primeng/ripple\";\nimport * as i16 from \"primeng/checkbox\";\nconst _c0 = (a0, a1) => ({\n  \"md:col-9 lg:col-9\": a0,\n  \"narrow\": a1\n});\nconst _c1 = (a0, a1) => ({\n  \"font-xs\": a0,\n  \"font-base\": a1\n});\nconst _c2 = a0 => ({\n  \"disabled-div\": a0\n});\nconst _c3 = a0 => ({\n  \"flex-nowrap gap-1\": a0\n});\nconst _c4 = a0 => ({\n  \"m-0\": a0\n});\nconst _c5 = a0 => ({\n  \"sm bottom-0\": a0\n});\nfunction AvailabilityComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41);\n    i0.ɵɵtext(2, \" Choose date & time available \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AvailabilityComponent_div_26_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 51);\n  }\n}\nfunction AvailabilityComponent_div_26_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_26_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.addMon());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_26_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_26_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const i_r2 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeMon(i_r2));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelementContainerStart(1, 43);\n    i0.ɵɵelementStart(2, \"div\", 44)(3, \"div\", 45);\n    i0.ɵɵtemplate(4, AvailabilityComponent_div_26_img_4_Template, 1, 0, \"img\", 46);\n    i0.ɵɵelementStart(5, \"p-calendar\", 47);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_26_Template_p_calendar_onSelect_5_listener($event) {\n      const i_r2 = i0.ɵɵrestoreView(_r1).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.mon, i_r2, \"from\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 45)(7, \"p-calendar\", 48);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_26_Template_p_calendar_onSelect_7_listener($event) {\n      const i_r2 = i0.ɵɵrestoreView(_r1).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.mon, i_r2, \"to\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, AvailabilityComponent_div_26_button_8_Template, 1, 3, \"button\", 49)(9, AvailabilityComponent_div_26_button_9_Template, 1, 3, \"button\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r2 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroupName\", i_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c3, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r2 > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r2 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r2 > 0);\n  }\n}\nfunction AvailabilityComponent_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1, \"* Invalid Format \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AvailabilityComponent_div_39_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 51);\n  }\n}\nfunction AvailabilityComponent_div_39_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_39_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.addTue());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_39_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_39_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const i_r7 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeTue(i_r7));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelementContainerStart(1, 43);\n    i0.ɵɵelementStart(2, \"div\", 44)(3, \"div\", 45);\n    i0.ɵɵtemplate(4, AvailabilityComponent_div_39_img_4_Template, 1, 0, \"img\", 46);\n    i0.ɵɵelementStart(5, \"p-calendar\", 47);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_39_Template_p_calendar_onSelect_5_listener($event) {\n      const i_r7 = i0.ɵɵrestoreView(_r6).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.tue, i_r7, \"from\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 45)(7, \"p-calendar\", 48);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_39_Template_p_calendar_onSelect_7_listener($event) {\n      const i_r7 = i0.ɵɵrestoreView(_r6).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.tue, i_r7, \"to\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, AvailabilityComponent_div_39_button_8_Template, 1, 3, \"button\", 49)(9, AvailabilityComponent_div_39_button_9_Template, 1, 3, \"button\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r7 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroupName\", i_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c3, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r7 > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r7 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r7 > 0);\n  }\n}\nfunction AvailabilityComponent_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1, \"* Invalid Format \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AvailabilityComponent_div_52_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 51);\n  }\n}\nfunction AvailabilityComponent_div_52_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_52_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.addWed());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_52_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_52_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const i_r11 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeWed(i_r11));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelementContainerStart(1, 43);\n    i0.ɵɵelementStart(2, \"div\", 44)(3, \"div\", 45);\n    i0.ɵɵtemplate(4, AvailabilityComponent_div_52_img_4_Template, 1, 0, \"img\", 46);\n    i0.ɵɵelementStart(5, \"p-calendar\", 47);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_52_Template_p_calendar_onSelect_5_listener($event) {\n      const i_r11 = i0.ɵɵrestoreView(_r10).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.wed, i_r11, \"from\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 45)(7, \"p-calendar\", 48);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_52_Template_p_calendar_onSelect_7_listener($event) {\n      const i_r11 = i0.ɵɵrestoreView(_r10).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.wed, i_r11, \"to\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, AvailabilityComponent_div_52_button_8_Template, 1, 3, \"button\", 49)(9, AvailabilityComponent_div_52_button_9_Template, 1, 3, \"button\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r11 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroupName\", i_r11);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c3, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r11 > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r11 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r11 > 0);\n  }\n}\nfunction AvailabilityComponent_div_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1, \"* Invalid Format \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AvailabilityComponent_div_65_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 51);\n  }\n}\nfunction AvailabilityComponent_div_65_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_65_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.addThu());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_65_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_65_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const i_r15 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeThu(i_r15));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelementContainerStart(1, 43);\n    i0.ɵɵelementStart(2, \"div\", 44)(3, \"div\", 45);\n    i0.ɵɵtemplate(4, AvailabilityComponent_div_65_img_4_Template, 1, 0, \"img\", 46);\n    i0.ɵɵelementStart(5, \"p-calendar\", 47);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_65_Template_p_calendar_onSelect_5_listener($event) {\n      const i_r15 = i0.ɵɵrestoreView(_r14).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.thu, i_r15, \"from\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 45)(7, \"p-calendar\", 48);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_65_Template_p_calendar_onSelect_7_listener($event) {\n      const i_r15 = i0.ɵɵrestoreView(_r14).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.thu, i_r15, \"to\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, AvailabilityComponent_div_65_button_8_Template, 1, 3, \"button\", 49)(9, AvailabilityComponent_div_65_button_9_Template, 1, 3, \"button\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r15 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroupName\", i_r15);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c3, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r15 > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r15 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r15 > 0);\n  }\n}\nfunction AvailabilityComponent_div_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1, \"* Invalid Format \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AvailabilityComponent_div_78_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 51);\n  }\n}\nfunction AvailabilityComponent_div_78_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_78_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.addFri());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_78_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_78_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const i_r19 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeFri(i_r19));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_78_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelementContainerStart(1, 43);\n    i0.ɵɵelementStart(2, \"div\", 44)(3, \"div\", 45);\n    i0.ɵɵtemplate(4, AvailabilityComponent_div_78_img_4_Template, 1, 0, \"img\", 46);\n    i0.ɵɵelementStart(5, \"p-calendar\", 47);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_78_Template_p_calendar_onSelect_5_listener($event) {\n      const i_r19 = i0.ɵɵrestoreView(_r18).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.fri, i_r19, \"from\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 45)(7, \"p-calendar\", 48);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_78_Template_p_calendar_onSelect_7_listener($event) {\n      const i_r19 = i0.ɵɵrestoreView(_r18).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.fri, i_r19, \"to\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, AvailabilityComponent_div_78_button_8_Template, 1, 3, \"button\", 49)(9, AvailabilityComponent_div_78_button_9_Template, 1, 3, \"button\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r19 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroupName\", i_r19);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c3, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r19 > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r19 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r19 > 0);\n  }\n}\nfunction AvailabilityComponent_div_79_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1, \"* Invalid Format \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AvailabilityComponent_div_91_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 51);\n  }\n}\nfunction AvailabilityComponent_div_91_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_91_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.addSat());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_91_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_91_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const i_r23 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeSat(i_r23));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_91_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelementContainerStart(1, 43);\n    i0.ɵɵelementStart(2, \"div\", 44)(3, \"div\", 45);\n    i0.ɵɵtemplate(4, AvailabilityComponent_div_91_img_4_Template, 1, 0, \"img\", 46);\n    i0.ɵɵelementStart(5, \"p-calendar\", 47);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_91_Template_p_calendar_onSelect_5_listener($event) {\n      const i_r23 = i0.ɵɵrestoreView(_r22).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.sat, i_r23, \"from\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 45)(7, \"p-calendar\", 48);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_91_Template_p_calendar_onSelect_7_listener($event) {\n      const i_r23 = i0.ɵɵrestoreView(_r22).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.sat, i_r23, \"to\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, AvailabilityComponent_div_91_button_8_Template, 1, 3, \"button\", 49)(9, AvailabilityComponent_div_91_button_9_Template, 1, 3, \"button\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r23 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroupName\", i_r23);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c3, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r23 > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r23 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r23 > 0);\n  }\n}\nfunction AvailabilityComponent_div_92_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1, \"* Invalid Format \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AvailabilityComponent_div_104_img_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 51);\n  }\n}\nfunction AvailabilityComponent_div_104_button_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_104_button_8_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.addSun());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_104_button_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 53);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_div_104_button_9_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r29);\n      const i_r27 = i0.ɵɵnextContext().index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.removeSun(i_r27));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c5, ctx_r2.isDialog));\n  }\n}\nfunction AvailabilityComponent_div_104_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 42);\n    i0.ɵɵelementContainerStart(1, 43);\n    i0.ɵɵelementStart(2, \"div\", 44)(3, \"div\", 45);\n    i0.ɵɵtemplate(4, AvailabilityComponent_div_104_img_4_Template, 1, 0, \"img\", 46);\n    i0.ɵɵelementStart(5, \"p-calendar\", 47);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_104_Template_p_calendar_onSelect_5_listener($event) {\n      const i_r27 = i0.ɵɵrestoreView(_r26).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.sun, i_r27, \"from\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 45)(7, \"p-calendar\", 48);\n    i0.ɵɵlistener(\"onSelect\", function AvailabilityComponent_div_104_Template_p_calendar_onSelect_7_listener($event) {\n      const i_r27 = i0.ɵɵrestoreView(_r26).index;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.timePickChanged($event, ctx_r2.form.controls.sun, i_r27, \"to\"));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, AvailabilityComponent_div_104_button_8_Template, 1, 3, \"button\", 49)(9, AvailabilityComponent_div_104_button_9_Template, 1, 3, \"button\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r27 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroupName\", i_r27);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(15, _c3, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r27 > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c4, ctx_r2.isDialog));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inputStyleClass\", !ctx_r2.isDialog ? \"input-element\" : \"m-0 dialog-time-input\")(\"stepMinute\", 15)(\"timeOnly\", true)(\"defaultDate\", ctx_r2.defaultDate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r27 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r27 > 0);\n  }\n}\nfunction AvailabilityComponent_div_105_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1, \"* Invalid Format \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AvailabilityComponent_div_106_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61)(1, \"div\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const time_r31 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(time_r31);\n  }\n}\nfunction AvailabilityComponent_div_106_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtext(1, \"* Hours per week is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AvailabilityComponent_div_106_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 40)(2, \"div\", 41);\n    i0.ɵɵtext(3, \" Choose hours per week * \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 56)(5, \"div\", 57)(6, \"div\", 58)(7, \"p-dropdown\", 59);\n    i0.ɵɵlistener(\"onChange\", function AvailabilityComponent_div_106_Template_p_dropdown_onChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onHoursChange($event));\n    });\n    i0.ɵɵtemplate(8, AvailabilityComponent_div_106_ng_template_8_Template, 3, 1, \"ng-template\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, AvailabilityComponent_div_106_div_9_Template, 2, 0, \"div\", 21);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"options\", ctx_r2.hourOptions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.ifFieldValid(\"hours\"));\n  }\n}\nfunction AvailabilityComponent_ng_container_107_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 63)(2, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_ng_container_107_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AvailabilityComponent_ng_container_108_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Next \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AvailabilityComponent_ng_container_108_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \" Save \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AvailabilityComponent_ng_container_108_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 65)(2, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_ng_container_108_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.goBack());\n    });\n    i0.ɵɵelement(3, \"img\", 67);\n    i0.ɵɵtext(4, \" Back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 68);\n    i0.ɵɵlistener(\"click\", function AvailabilityComponent_ng_container_108_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmit());\n    });\n    i0.ɵɵtemplate(6, AvailabilityComponent_ng_container_108_span_6_Template, 2, 0, \"span\", 39)(7, AvailabilityComponent_ng_container_108_span_7_Template, 2, 0, \"span\", 39);\n    i0.ɵɵelement(8, \"img\", 69);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.user);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.user);\n  }\n}\nexport let AvailabilityComponent = /*#__PURE__*/(() => {\n  class AvailabilityComponent {\n    constructor(fb, router, location, teacherService, userService, toast, authService, calendarService, generalService) {\n      this.fb = fb;\n      this.router = router;\n      this.location = location;\n      this.teacherService = teacherService;\n      this.userService = userService;\n      this.toast = toast;\n      this.authService = authService;\n      this.calendarService = calendarService;\n      this.generalService = generalService;\n      this.user = {};\n      this.showHours = true;\n      this.isDialog = false;\n      this.subs = new SubSink();\n      this.form = new UntypedFormGroup({});\n      this.weekDays = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];\n      this.teacherAvailabilityMon = [];\n      this.teacherAvailabilityTue = [];\n      this.teacherAvailabilityWed = [];\n      this.teacherAvailabilityThu = [];\n      this.teacherAvailabilityFri = [];\n      this.teacherAvailabilitySat = [];\n      this.teacherAvailabilitySun = [];\n      this.hourOptions = ['5-10', '10-15', '15-20', '20-25', '25-30', '30-35', '35-40'];\n      this.selectedHours = \"5-10\";\n      this.tryToSave = false;\n      this.defaultDate = new Date(\"January 31 2024 00:00\");\n    }\n    ngOnInit() {\n      this.teacherService.setCurrentStepIndex(4);\n      this.subs.add(this.teacherService.updateAvailabilityListener.subscribe(res => {\n        this.teacherAvailabilityMon = [];\n        this.teacherAvailabilityTue = [];\n        this.teacherAvailabilityWed = [];\n        this.teacherAvailabilityThu = [];\n        this.teacherAvailabilityFri = [];\n        this.teacherAvailabilitySat = [];\n        this.teacherAvailabilitySun = [];\n        this.teacher = this.user ? this.authService.getLoggedInUser() : this.teacherService.dummyTeacher;\n        this.teacherAvailability = this.teacher?.availability;\n        this.form = new UntypedFormGroup({\n          mon: new UntypedFormArray([]),\n          tue: new UntypedFormArray([]),\n          wed: new UntypedFormArray([]),\n          thu: new UntypedFormArray([]),\n          fri: new UntypedFormArray([]),\n          sat: new UntypedFormArray([]),\n          sun: new UntypedFormArray([]),\n          mondayActive: new UntypedFormControl(false),\n          tuesdayActive: new UntypedFormControl(false),\n          wednesdayActive: new UntypedFormControl(false),\n          thursdayActive: new UntypedFormControl(false),\n          fridayActive: new UntypedFormControl(false),\n          saturdayActive: new UntypedFormControl(false),\n          sundayActive: new UntypedFormControl(false),\n          hours: new UntypedFormControl(this.teacher?.hours_per_week ? this.teacher?.hours_per_week : this.hourOptions[0], {\n            validators: [Validators.required]\n          })\n        });\n      }));\n      this.subs.add(this.teacherService.submitOnMenuClickListener.subscribe(res => {\n        if (res) {\n          this.onSubmit();\n        }\n      }));\n      if (this.isDialog) {\n        this.userService.getUserAvailability(this.user.aspUserId).subscribe(res => {\n          if (res) {\n            this.updateDaysFields(res);\n          }\n        });\n      } else {\n        this.subs.add(this.teacherService.getTeacherApplicationStep5().subscribe(res => {\n          if (res) {\n            this.updateDaysFields(res);\n          }\n        }));\n      }\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n      this.teacherService.setSubmitOnMenuClickListener(false);\n    }\n    /**\n     * Handle the change event of the time picker control\n     * @param {any} $event - The change event data\n     * @param {FormArray} formArray - The form array that contains the time picker control\n     * @param {any} i - The index of the control in the form array\n     * @param {string} property - The property name to be updated with the selected time\n     */\n    timePickChanged($event, formArray, i, property) {\n      const dd = new Date($event);\n      const timeConverted = {};\n      timeConverted[property] = dd.toLocaleString('en-US', {\n        hour12: false,\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n      const timeArray = timeConverted[property].split(\":\");\n      console.log(timeArray);\n      if (timeArray[0] <= '23') {\n        console.log(dd);\n        console.log(timeConverted);\n        let controls = this.form.controls;\n        formArray.at(i).patchValue(timeConverted);\n        return false;\n      } else {\n        formArray.at(i).patchValue({\n          from: '00:00',\n          to: '00:' + timeArray[1]\n        });\n        return false;\n      }\n    }\n    updateDaysFields(availability) {\n      this.form.get('mondayActive')?.patchValue(availability.mondayActive || !this.hasEmptyFromOrTo(availability.mon));\n      this.form.get('tuesdayActive')?.patchValue(availability.tuesdayActive || !this.hasEmptyFromOrTo(availability.tue));\n      this.form.get('wednesdayActive')?.patchValue(availability.wednesdayActive || !this.hasEmptyFromOrTo(availability.wed));\n      this.form.get('thursdayActive')?.patchValue(availability.thursdayActive || !this.hasEmptyFromOrTo(availability.thu));\n      this.form.get('fridayActive')?.patchValue(availability.fridayActive || !this.hasEmptyFromOrTo(availability.fri));\n      this.form.get('saturdayActive')?.patchValue(availability.saturdayActive || !this.hasEmptyFromOrTo(availability.sat));\n      this.form.get('sundayActive')?.patchValue(availability.sundayActive || !this.hasEmptyFromOrTo(availability.sun));\n      this.form.get('hours')?.patchValue(availability.hours);\n      const mondayActive = this.form.get('mondayActive')?.value;\n      const tuesdayActive = this.form.get('tuesdayActive')?.value;\n      const wednesdayActive = this.form.get('wednesdayActive')?.value;\n      const thursdayActive = this.form.get('thursdayActive')?.value;\n      const fridayActive = this.form.get('fridayActive')?.value;\n      const saturdayActive = this.form.get('saturdayActive')?.value;\n      const sundayActive = this.form.get('sundayActive')?.value;\n      if (availability?.mon && mondayActive) {\n        for (let m of availability.mon) {\n          this.teacherAvailabilityMon.push({\n            from: m.from,\n            to: m.to\n          });\n        }\n      }\n      if (availability?.tue && tuesdayActive) {\n        for (let m of availability.tue) {\n          this.teacherAvailabilityTue.push({\n            from: m.from,\n            to: m.to\n          });\n        }\n      }\n      if (availability?.wed && wednesdayActive) {\n        for (let m of availability.wed) {\n          this.teacherAvailabilityWed.push({\n            from: m.from,\n            to: m.to\n          });\n        }\n      }\n      if (availability?.thu && thursdayActive) {\n        for (let m of availability.thu) {\n          this.teacherAvailabilityThu.push({\n            from: m.from,\n            to: m.to\n          });\n        }\n      }\n      if (availability?.fri && fridayActive) {\n        for (let m of availability.fri) {\n          this.teacherAvailabilityFri.push({\n            from: m.from,\n            to: m.to\n          });\n        }\n      }\n      if (availability?.sat && saturdayActive) {\n        for (let m of availability.sat) {\n          this.teacherAvailabilitySat.push({\n            from: m.from,\n            to: m.to\n          });\n        }\n      }\n      if (availability?.sun && sundayActive) {\n        for (let m of availability.sun) {\n          this.teacherAvailabilitySun.push({\n            from: m.from,\n            to: m.to\n          });\n        }\n      }\n      this.teacherAvailabilityMon?.forEach(element => {\n        this.mon.push(this.fb.group(element));\n      });\n      if (this.teacherAvailabilityMon.length === 0) {\n        this.addMon();\n      }\n      this.teacherAvailabilityTue?.forEach(element => {\n        this.tue.push(this.fb.group(element));\n      });\n      if (this.teacherAvailabilityTue?.length === 0) {\n        this.addTue();\n      }\n      this.teacherAvailabilityWed?.forEach(element => {\n        this.wed.push(this.fb.group(element));\n      });\n      if (this.teacherAvailabilityWed?.length === 0) {\n        this.addWed();\n      }\n      this.teacherAvailabilityThu?.forEach(element => {\n        this.thu.push(this.fb.group(element));\n      });\n      if (this.teacherAvailabilityThu?.length === 0) {\n        this.addThu();\n      }\n      this.teacherAvailabilityFri?.forEach(element => {\n        this.fri.push(this.fb.group(element));\n      });\n      if (this.teacherAvailabilityFri?.length === 0) {\n        this.addFri();\n      }\n      this.teacherAvailabilitySat?.forEach(element => {\n        this.sat.push(this.fb.group(element));\n      });\n      if (this.teacherAvailabilitySat?.length === 0) {\n        this.addSat();\n      }\n      this.teacherAvailabilitySun?.forEach(element => {\n        this.sun.push(this.fb.group(element));\n      });\n      if (this.teacherAvailabilitySun.length === 0) {\n        this.addSun();\n      }\n    }\n    initCheckboxChangedListener(event, dayParam, dayActive) {\n      if (!event.checked) {\n        dayParam.controls?.forEach((element, index) => {\n          console.log(element);\n          const group = new UntypedFormGroup({\n            from: new UntypedFormControl(''),\n            to: new UntypedFormControl('')\n          });\n          this.form.setControl(dayActive, this.fb.array([group]));\n        });\n      }\n    }\n    removeDay(name, index) {\n      switch (name) {\n        case 'mon':\n          this.removeMon(index);\n          break;\n        case 'tue':\n          this.removeTue(index);\n          break;\n        case 'wed':\n          this.removeWed(index);\n          break;\n        case 'thu':\n          this.removeThu(index);\n          break;\n        case 'fri':\n          this.removeFri(index);\n          break;\n        case 'sat':\n          this.removeSat(index);\n          break;\n        case 'sun':\n          this.removeSun(index);\n          break;\n      }\n    }\n    // TODO: unused, see if can be used, else delete\n    processDay(day) {\n      day = day.toLowerCase();\n      const availability = [`teacherAvailability${day}`];\n      let list = [];\n      availability?.forEach(element => {\n        const group = new UntypedFormGroup({\n          from: new UntypedFormControl(''),\n          to: new UntypedFormControl('')\n        });\n        list.push(this.fb.group(group));\n      });\n    }\n    // TODO: unused, see if can be used, else delete\n    add(day) {\n      const dayString = day.toString();\n      const control = this.form.get(dayString.toLowerCase());\n      const group = new UntypedFormGroup({\n        from: new UntypedFormControl(''),\n        to: new UntypedFormControl('')\n      });\n      control.push(group);\n    }\n    addMon() {\n      const group = new UntypedFormGroup({\n        from: new UntypedFormControl(''),\n        to: new UntypedFormControl('')\n      });\n      this.mon.push(group);\n    }\n    removeMon(index) {\n      console.log('remove', index);\n      this.mon.removeAt(index);\n    }\n    addTue() {\n      const group = new UntypedFormGroup({\n        from: new UntypedFormControl(''),\n        to: new UntypedFormControl('')\n      });\n      this.tue.push(group);\n    }\n    removeTue(index) {\n      this.tue.removeAt(index);\n    }\n    addWed() {\n      const group = new UntypedFormGroup({\n        from: new UntypedFormControl(''),\n        to: new UntypedFormControl('')\n      });\n      this.wed.push(group);\n    }\n    removeWed(index) {\n      this.wed.removeAt(index);\n    }\n    addThu() {\n      const group = new UntypedFormGroup({\n        from: new UntypedFormControl(''),\n        to: new UntypedFormControl('')\n      });\n      this.thu.push(group);\n    }\n    removeThu(index) {\n      this.thu.removeAt(index);\n    }\n    addFri() {\n      const group = new UntypedFormGroup({\n        from: new UntypedFormControl(''),\n        to: new UntypedFormControl('')\n      });\n      this.fri.push(group);\n    }\n    removeFri(index) {\n      this.fri.removeAt(index);\n    }\n    addSat() {\n      const group = new UntypedFormGroup({\n        from: new UntypedFormControl(''),\n        to: new UntypedFormControl('')\n      });\n      this.sat.push(group);\n    }\n    removeSat(index) {\n      this.sat.removeAt(index);\n    }\n    addSun() {\n      const group = new UntypedFormGroup({\n        from: new UntypedFormControl(''),\n        to: new UntypedFormControl('')\n      });\n      this.sun.push(group);\n    }\n    removeSun(index) {\n      this.sun.removeAt(index);\n    }\n    get mon() {\n      return this.form.get('mon');\n    }\n    get tue() {\n      return this.form.get('tue');\n    }\n    get wed() {\n      return this.form.get('wed');\n    }\n    get thu() {\n      return this.form.get('thu');\n    }\n    get fri() {\n      return this.form.get('fri');\n    }\n    get sat() {\n      return this.form.get('sat');\n    }\n    get sun() {\n      return this.form.get('sun');\n    }\n    onSubmit() {\n      this.tryToSave = true;\n      // if (!this.form.valid) {\n      //   return;\n      // }\n      let availability = {\n        id: \"string\",\n        type: UserAvailabilityType.AVAILABLE,\n        mon: [],\n        tue: [],\n        wed: [],\n        thu: [],\n        fri: [],\n        sat: [],\n        sun: []\n      };\n      const days = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];\n      const activeDay = ['mondayActive', 'tuesdayActive', 'wednesdayActive', 'thursdayActive', 'fridayActive', 'saturdayActive', 'sundayActive'];\n      const controls = this.form.controls;\n      for (let day of days) {\n        const castedDay = day;\n        if (Array.isArray(availability[castedDay])) {\n          for (let availabilitySlot of controls[day].value) {\n            for (let activeDayFormValue of activeDay) {\n              if (this.form.get(activeDayFormValue).value) {\n                availability[castedDay].push({\n                  from: availabilitySlot.from,\n                  to: availabilitySlot.to\n                });\n              }\n            }\n          }\n        }\n      }\n      // console.log(this.form.value);\n      if (this.isDialog) {\n        this.subs.add(this.teacherService.updateTeacherAvailability(this.form.value).subscribe(res => {\n          if (res) {\n            this.toast.setShowToastmessage({\n              severity: 'success',\n              summary: '',\n              detail: 'Your Availability info were updated.'\n            });\n            this.calendarService.setToggleOverlayListener(true);\n            this.calendarService.setUpdateListener(true);\n          }\n        }));\n      } else {\n        this.subs.add(this.teacherService.updateAPITeacherApplicationStep5(this.form.value).subscribe(res => {\n          if (res) {\n            this.toast.setShowToastmessage({\n              severity: 'success',\n              summary: '',\n              detail: 'Your Availability info were updated.'\n            });\n            this.router.navigateByUrl('/teacher/review', {\n              replaceUrl: true\n            });\n          }\n          if (!res) {\n            this.toast.setShowToastmessage({\n              severity: 'warn',\n              summary: '',\n              detail: 'Previous steps must be completed first.'\n            });\n          }\n        }));\n        if (this.user) {\n          this.user.availability = availability;\n          this.user.hours_per_week = this.selectedHours;\n          console.log(this.user);\n          this.userService.updateUserListener();\n          this.subs.add(this.teacherService.updateAvailability(this.user.availability).subscribe(res => {\n            localStorage.setItem(\"user\", JSON.stringify(this.user));\n            this.teacherService.setUpdateAvailabilityForSchedulerClickListener(true);\n          }));\n          this.toast.setShowToastmessage({\n            severity: 'success',\n            summary: '',\n            detail: 'Availability has been updated.'\n          });\n        } else {\n          this.teacher.availability = availability;\n          this.teacher.hours_per_week = this.selectedHours;\n          // this.router.navigateByUrl('/teacher/(teacher:review)', { replaceUrl: true });\n        }\n      }\n    }\n    checkFormat(from, to) {\n      const isFromValid = from.length === 5 && from.indexOf(\":\") === 2;\n      const isToValid = to.length === 5 && to.indexOf(\":\") === 2;\n      return isFromValid && isToValid;\n    }\n    reload(url) {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        yield _this.router.navigateByUrl('.', {\n          skipLocationChange: true\n        });\n        return _this.router.navigateByUrl(url);\n      })();\n    }\n    onHoursChange(event) {\n      setTimeout(() => {\n        this.selectedHours = event.value;\n      }, 100);\n    }\n    checkValidity(time) {\n      if (time.length === 0) return 1;\n      let split = time.split(':');\n      let hours = parseInt(split[0]);\n      let minutes = parseInt(split[1]);\n      return hours && minutes;\n    }\n    /**\n    * Check if the given field is valid or not.\n    * @param {string} field - The field to check its validity.\n    * @returns {boolean} - Returns `true` if the field is invalid, and `false` otherwise.\n    */\n    ifFieldValid(field) {\n      let control = this.form.get(field);\n      let isNotValid = false;\n      if (control.controls) {\n        for (let c of control.controls) {\n          let from = c.controls.from.value.toString();\n          let to = c.controls.to.value.toString();\n          isNotValid = isNotValid || this.tryToSave && control.touched && (isNaN(this.checkValidity(from)) || isNaN(this.checkValidity(to)));\n        }\n      } else {\n        isNotValid = this.form.get(field).invalid && (this.tryToSave || this.form.get(field).touched);\n      }\n      if (control.controls?.length === 0) {\n        isNotValid = false;\n      }\n      // if (this.router.url.includes('teacher')) {\n      //   this.teacherService.setStepValidWithBoolean(4, isNotValid, 'teacher-availability-route');\n      // }\n      return isNotValid;\n    }\n    goBack() {\n      this.router.navigateByUrl('/teacher/resume', {\n        replaceUrl: true\n      });\n    }\n    hasEmptyFromOrTo(data) {\n      if (this.generalService.isNullishObject(data)) {\n        return true;\n      }\n      if (data.length === 0) {\n        return true; // Empty array\n      }\n      for (const item of data) {\n        if (item.from.trim() === '' || item.to.trim() === '') {\n          return true; // Empty from or to\n        }\n      }\n      return false; // No empty from or to\n    }\n    static #_ = this.ɵfac = function AvailabilityComponent_Factory(t) {\n      return new (t || AvailabilityComponent)(i0.ɵɵdirectiveInject(i1.UntypedFormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.Location), i0.ɵɵdirectiveInject(i4.TeacherApplicationService), i0.ɵɵdirectiveInject(i5.UserService), i0.ɵɵdirectiveInject(i6.ToastService), i0.ɵɵdirectiveInject(i7.AuthService), i0.ɵɵdirectiveInject(i8.CalendarService), i0.ɵɵdirectiveInject(i9.GeneralService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AvailabilityComponent,\n      selectors: [[\"app-availability\"]],\n      inputs: {\n        user: \"user\",\n        showHours: \"showHours\",\n        isDialog: \"isDialog\"\n      },\n      decls: 109,\n      vars: 73,\n      consts: [[1, \"profile-info-section\"], [3, \"formGroup\"], [1, \"availability\", \"grid\", \"grid-nogutter\"], [\"class\", \"col-12 md:col-3 lg:col-3\", 4, \"ngIf\"], [1, \"col-12\", \"availability-wrapper\", 3, \"ngClass\"], [1, \"availability-row\", \"hidden\", \"md:flex\", 2, \"font-weight\", \"bold\", \"text-align\", \"left\"], [1, \"availability-column-1\", \"title-color\"], [1, \"availability-column-1\", \"title-color\", \"text-center\"], [1, \"availability-column-2\", 2, \"display\", \"flex\", \"flex-wrap\", \"wrap\"], [1, \"column-title\", \"title-color\", \"text-center\"], [1, \"availability-row\"], [1, \"availability-column-1\", \"day\"], [1, \"day-title\", 3, \"ngClass\"], [1, \"availability-column-1\", \"checkbox-wrap\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\"], [\"name\", \"mondayActive\", \"formControlName\", \"mondayActive\", \"styleClass\", \"primary-blue outlined\", \"binary\", \"true\", \"label\", \"\", 3, \"onChange\"], [1, \"availability-column-2\"], [1, \"profile-info\", 3, \"ngClass\"], [1, \"profile-info-section\", 2, \"padding-top\", \"0\"], [\"formArrayName\", \"mon\"], [\"style\", \"position: relative;\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"input-error\", 4, \"ngIf\"], [\"name\", \"tuesdayActive\", \"formControlName\", \"tuesdayActive\", \"styleClass\", \"primary-blue outlined\", \"binary\", \"true\", \"label\", \"\", 3, \"onChange\"], [1, \"availability-column-2\", 3, \"pTooltip\"], [1, \"profile-info\"], [1, \"profile-info-section\", 2, \"padding-top\", \"0\", 3, \"ngClass\"], [\"formArrayName\", \"tue\"], [\"name\", \"wednesdayActive\", \"formControlName\", \"wednesdayActive\", \"styleClass\", \"primary-blue outlined\", \"binary\", \"true\", \"label\", \"\", 3, \"onChange\"], [1, \"availability-column-2\", 3, \"ngClass\"], [\"formArrayName\", \"wed\"], [\"name\", \"thursdayActive\", \"formControlName\", \"thursdayActive\", \"styleClass\", \"primary-blue outlined\", \"binary\", \"true\", \"label\", \"\", 3, \"onChange\"], [\"formArrayName\", \"thu\"], [\"name\", \"fridayActive\", \"formControlName\", \"fridayActive\", \"styleClass\", \"primary-blue outlined\", \"binary\", \"true\", \"label\", \"\", 3, \"onChange\"], [\"formArrayName\", \"fri\"], [\"name\", \"saturdayActive\", \"formControlName\", \"saturdayActive\", \"styleClass\", \"primary-blue outlined\", \"binary\", \"true\", \"label\", \"\", 3, \"onChange\"], [\"formArrayName\", \"sat\"], [\"name\", \"sundayActive\", \"formControlName\", \"sundayActive\", \"styleClass\", \"primary-blue outlined\", \"binary\", \"true\", \"label\", \"\", 3, \"onChange\"], [\"formArrayName\", \"sun\"], [\"class\", \"availability grid\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"col-12\", \"md:col-3\", \"lg:col-3\"], [1, \"availability-title\"], [2, \"position\", \"relative\"], [3, \"formGroupName\"], [1, \"input-fields\", 3, \"ngClass\"], [1, \"input-field\", 3, \"ngClass\"], [\"class\", \"more-arrow\", \"src\", \"/assets/icons/availability-day-more-arrow.svg\", \"alt\", \"availability-day-more\", 4, \"ngIf\"], [\"readonlyInput\", \"true\", \"formControlName\", \"from\", \"placeholder\", \"e.g. 11:00\", 3, \"onSelect\", \"inputStyleClass\", \"stepMinute\", \"timeOnly\", \"defaultDate\"], [\"readonlyInput\", \"true\", \"formControlName\", \"to\", \"placeholder\", \"e.g. 14:00\", 3, \"onSelect\", \"inputStyleClass\", \"stepMinute\", \"timeOnly\", \"defaultDate\"], [\"pButton\", \"\", \"type\", \"button\", \"class\", \"p-button-raised p-button-rounded plus-btn-circle addDayIcon\", \"icon\", \"pi pi-plus\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"pButton\", \"\", \"type\", \"button\", \"class\", \"p-button-raised p-button-rounded minus-btn-circle addDayIcon\", \"icon\", \"pi pi-minus\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"src\", \"/assets/icons/availability-day-more-arrow.svg\", \"alt\", \"availability-day-more\", 1, \"more-arrow\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-plus\", 1, \"p-button-raised\", \"p-button-rounded\", \"plus-btn-circle\", \"addDayIcon\", 3, \"click\", \"ngClass\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-minus\", 1, \"p-button-raised\", \"p-button-rounded\", \"minus-btn-circle\", \"addDayIcon\", 3, \"click\", \"ngClass\"], [1, \"input-error\"], [1, \"availability\", \"grid\"], [1, \"col-12\", \"md:col-9\", \"lg:col-9\"], [1, \"input-fields\", \"my-dropdown\"], [1, \"input-field\"], [\"autocomplete\", \"off\", \"placeholder\", \"-\", \"formControlName\", \"hours\", \"styleClass\", \"dropdown-blue hours-dropdown m-t-0\", 3, \"onChange\", \"options\"], [\"pTemplate\", \"item\"], [1, \"country-item\"], [1, \"country-name\"], [1, \"flex\", \"justify-content-center\", \"my-3\"], [\"icon\", \"pi pi-calendar\", \"iconPos\", \"left\", \"pButton\", \"\", \"type\", \"button\", \"label\", \"Schedule\", 1, \"mt-2\", \"p-button-sm\", 3, \"click\"], [1, \"btns\", \"md:ml-5\", \"md:mt-4\"], [\"pRipple\", \"\", 1, \"rounded-blue-button\", \"transparent\", 3, \"click\"], [\"src\", \"/assets/icons/arrow-left-blue.svg\"], [\"pRipple\", \"\", 1, \"rounded-blue-button\", 3, \"click\"], [\"src\", \"/assets/icons/arrow-right.svg\"]],\n      template: function AvailabilityComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"form\", 1)(2, \"div\", 2);\n          i0.ɵɵtemplate(3, AvailabilityComponent_div_3_Template, 3, 0, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵtext(7, \" Day \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 7);\n          i0.ɵɵtext(9, \" Active \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9);\n          i0.ɵɵtext(12, \" Starts \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 9);\n          i0.ɵɵtext(14, \" Ends \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 10)(16, \"div\", 11)(17, \"div\", 12);\n          i0.ɵɵtext(18, \" Monday \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"div\", 13)(20, \"div\", 14)(21, \"p-checkbox\", 15);\n          i0.ɵɵlistener(\"onChange\", function AvailabilityComponent_Template_p_checkbox_onChange_21_listener($event) {\n            return ctx.initCheckboxChangedListener($event, ctx.mon, \"mon\");\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(22, \"div\", 16)(23, \"div\", 17)(24, \"div\", 18);\n          i0.ɵɵelementContainerStart(25, 19);\n          i0.ɵɵtemplate(26, AvailabilityComponent_div_26_Template, 10, 21, \"div\", 20)(27, AvailabilityComponent_div_27_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(28, \"div\", 10)(29, \"div\", 11)(30, \"div\", 12);\n          i0.ɵɵtext(31, \" Tuesday \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 13)(33, \"div\", 14)(34, \"p-checkbox\", 22);\n          i0.ɵɵlistener(\"onChange\", function AvailabilityComponent_Template_p_checkbox_onChange_34_listener($event) {\n            return ctx.initCheckboxChangedListener($event, ctx.tue, \"tue\");\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(35, \"div\", 23)(36, \"div\", 24)(37, \"div\", 25);\n          i0.ɵɵelementContainerStart(38, 26);\n          i0.ɵɵtemplate(39, AvailabilityComponent_div_39_Template, 10, 21, \"div\", 20)(40, AvailabilityComponent_div_40_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(41, \"div\", 10)(42, \"div\", 11)(43, \"div\", 12);\n          i0.ɵɵtext(44, \" Wednesday \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"div\", 13)(46, \"div\", 14)(47, \"p-checkbox\", 27);\n          i0.ɵɵlistener(\"onChange\", function AvailabilityComponent_Template_p_checkbox_onChange_47_listener($event) {\n            return ctx.initCheckboxChangedListener($event, ctx.wed, \"wed\");\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(48, \"div\", 28)(49, \"div\", 24)(50, \"div\", 18);\n          i0.ɵɵelementContainerStart(51, 29);\n          i0.ɵɵtemplate(52, AvailabilityComponent_div_52_Template, 10, 21, \"div\", 20)(53, AvailabilityComponent_div_53_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(54, \"div\", 10)(55, \"div\", 11)(56, \"div\", 12);\n          i0.ɵɵtext(57, \" Thursday \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(58, \"div\", 13)(59, \"div\", 14)(60, \"p-checkbox\", 30);\n          i0.ɵɵlistener(\"onChange\", function AvailabilityComponent_Template_p_checkbox_onChange_60_listener($event) {\n            return ctx.initCheckboxChangedListener($event, ctx.thu, \"thu\");\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(61, \"div\", 28)(62, \"div\", 24)(63, \"div\", 18);\n          i0.ɵɵelementContainerStart(64, 31);\n          i0.ɵɵtemplate(65, AvailabilityComponent_div_65_Template, 10, 21, \"div\", 20)(66, AvailabilityComponent_div_66_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(67, \"div\", 10)(68, \"div\", 11)(69, \"div\", 12);\n          i0.ɵɵtext(70, \" Friday \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(71, \"div\", 13)(72, \"div\", 14)(73, \"p-checkbox\", 32);\n          i0.ɵɵlistener(\"onChange\", function AvailabilityComponent_Template_p_checkbox_onChange_73_listener($event) {\n            return ctx.initCheckboxChangedListener($event, ctx.fri, \"fri\");\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(74, \"div\", 28)(75, \"div\", 24)(76, \"div\", 18);\n          i0.ɵɵelementContainerStart(77, 33);\n          i0.ɵɵtemplate(78, AvailabilityComponent_div_78_Template, 10, 21, \"div\", 20)(79, AvailabilityComponent_div_79_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(80, \"div\", 10)(81, \"div\", 11)(82, \"div\", 12);\n          i0.ɵɵtext(83, \" Saturday \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(84, \"div\", 13)(85, \"div\", 14)(86, \"p-checkbox\", 34);\n          i0.ɵɵlistener(\"onChange\", function AvailabilityComponent_Template_p_checkbox_onChange_86_listener($event) {\n            return ctx.initCheckboxChangedListener($event, ctx.sat, \"sat\");\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(87, \"div\", 28)(88, \"div\", 24)(89, \"div\", 18);\n          i0.ɵɵelementContainerStart(90, 35);\n          i0.ɵɵtemplate(91, AvailabilityComponent_div_91_Template, 10, 21, \"div\", 20)(92, AvailabilityComponent_div_92_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(93, \"div\", 10)(94, \"div\", 11)(95, \"div\", 12);\n          i0.ɵɵtext(96, \" Sunday \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(97, \"div\", 13)(98, \"div\", 14)(99, \"p-checkbox\", 36);\n          i0.ɵɵlistener(\"onChange\", function AvailabilityComponent_Template_p_checkbox_onChange_99_listener($event) {\n            return ctx.initCheckboxChangedListener($event, ctx.sun, \"sun\");\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(100, \"div\", 28)(101, \"div\", 24)(102, \"div\", 18);\n          i0.ɵɵelementContainerStart(103, 37);\n          i0.ɵɵtemplate(104, AvailabilityComponent_div_104_Template, 10, 21, \"div\", 20)(105, AvailabilityComponent_div_105_Template, 2, 0, \"div\", 21);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵtemplate(106, AvailabilityComponent_div_106_Template, 10, 2, \"div\", 38);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(107, AvailabilityComponent_ng_container_107_Template, 3, 0, \"ng-container\", 39)(108, AvailabilityComponent_ng_container_108_Template, 9, 2, \"ng-container\", 39);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          let tmp_4_0;\n          let tmp_8_0;\n          let tmp_9_0;\n          let tmp_13_0;\n          let tmp_17_0;\n          let tmp_21_0;\n          let tmp_25_0;\n          let tmp_29_0;\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.form);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isDialog);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(35, _c0, !ctx.isDialog, ctx.isDialog));\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(38, _c1, ctx.isDialog, !ctx.isDialog));\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(41, _c2, !((tmp_4_0 = ctx.form.get(\"mondayActive\")) == null ? null : tmp_4_0.value)));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.mon.controls);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.ifFieldValid(\"mon\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(43, _c1, ctx.isDialog, !ctx.isDialog));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"pTooltip\", !((tmp_8_0 = ctx.form.get(\"tuesdayActive\")) == null ? null : tmp_8_0.value) ? \"Tuesday must be active to edit time.\" : \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(46, _c2, !((tmp_9_0 = ctx.form.get(\"tuesdayActive\")) == null ? null : tmp_9_0.value)));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.tue.controls);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.ifFieldValid(\"tue\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(48, _c1, ctx.isDialog, !ctx.isDialog));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(51, _c2, !((tmp_13_0 = ctx.form.get(\"wednesdayActive\")) == null ? null : tmp_13_0.value)));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.wed.controls);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.ifFieldValid(\"wed\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(53, _c1, ctx.isDialog, !ctx.isDialog));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(56, _c2, !((tmp_17_0 = ctx.form.get(\"thursdayActive\")) == null ? null : tmp_17_0.value)));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.thu.controls);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.ifFieldValid(\"thu\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(58, _c1, ctx.isDialog, !ctx.isDialog));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(61, _c2, !((tmp_21_0 = ctx.form.get(\"fridayActive\")) == null ? null : tmp_21_0.value)));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.fri.controls);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.ifFieldValid(\"fri\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(63, _c1, ctx.isDialog, !ctx.isDialog));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(66, _c2, !((tmp_25_0 = ctx.form.get(\"saturdayActive\")) == null ? null : tmp_25_0.value)));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.sat.controls);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.ifFieldValid(\"sat\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(68, _c1, ctx.isDialog, !ctx.isDialog));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(71, _c2, !((tmp_29_0 = ctx.form.get(\"sundayActive\")) == null ? null : tmp_29_0.value)));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngForOf\", ctx.sun.controls);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.ifFieldValid(\"sun\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showHours);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isDialog);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isDialog);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i10.Dropdown, i11.PrimeTemplate, i1.ɵNgNoValidate, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i1.FormGroupName, i1.FormArrayName, i12.Calendar, i13.ButtonDirective, i14.Tooltip, i15.Ripple, i16.Checkbox],\n      styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}.modal[_ngcontent-%COMP%]{min-width:50vw}[_nghost-%COMP%]     .p-dropdown:not(.p-disabled):hover{border:2px solid rgb(228,185,84)}  .no-label>.p-button-label{display:none}input[type=file][_ngcontent-%COMP%]{visibility:hidden;width:10px}input[type=date][_ngcontent-%COMP%]{position:relative;padding:10px}input[type=date][_ngcontent-%COMP%]::-webkit-calendar-picker-indicator{color:transparent;background:none;z-index:1}input[type=date][_ngcontent-%COMP%]:before{color:transparent;background:none;display:block;font-family:FontAwesome;content:\\\"\\\\f073\\\";width:20px;height:25px;position:absolute;top:12px;right:12px;color:#999}.image-preview[_ngcontent-%COMP%]{height:10rem;margin:1rem 0;border-radius:50%;border:3px solid var(--main-color)}.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{height:100%}[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-header{padding:clamp(.38rem,.47vw + .28rem,.75rem)}[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-items .p-dropdown-item{margin-bottom:clamp(.38rem,.47vw + .28rem,.75rem)}.country-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:clamp(.75rem,.31vw + .69rem,1rem);font-size:clamp(.88rem,.16vw + .84rem,1rem)}.country-item[_ngcontent-%COMP%]   .country-name[_ngcontent-%COMP%]{white-space:pre-line}.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;display:inline-block;vertical-align:middle}.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;clip-path:circle()}.country-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:32px;margin-right:.5rem}.country-item[_ngcontent-%COMP%]   img.smaller[_ngcontent-%COMP%]{width:16px}.info-element[_ngcontent-%COMP%]{padding:10px 0}.btns[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{margin:15px}.white-button[_ngcontent-%COMP%]{padding:10px}[_nghost-%COMP%]     .p-element .input-element{height:35px;margin-top:0;margin-right:10px}[_nghost-%COMP%]     .p-element .input-element:before{content:\\\"\\\";position:absolute;left:10px;top:0;bottom:0;width:20px;background:url(/assets/icons/wall-clock.svg) center/contain no-repeat}.availability[_ngcontent-%COMP%]{padding:14px 0 0;box-sizing:border-box}.availability[_ngcontent-%COMP%]   .availability-wrapper[_ngcontent-%COMP%]{padding:14px;border-radius:10px;box-shadow:3px 3px 6px #00000029;background-color:#fff}.availability[_ngcontent-%COMP%]   .availability-wrapper.narrow[_ngcontent-%COMP%]{padding:0;box-shadow:none}.availability[_ngcontent-%COMP%]   .availability-wrapper.narrow[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]{margin-top:0}.availability[_ngcontent-%COMP%]   .availability-wrapper.narrow[_ngcontent-%COMP%]   .more-arrow[_ngcontent-%COMP%]{top:8px}.availability[_ngcontent-%COMP%]   .availability-title[_ngcontent-%COMP%]{font-size:15px;font-weight:700;color:var(--main-color);text-align:left}.availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]{display:flex;flex-direction:row;width:100%;margin-top:10px;font-size:15px;align-items:baseline}@media screen and (max-width: 576px){.availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]{width:100%}}.availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]   .availability-column-1[_ngcontent-%COMP%]{width:20%;padding:5px;box-sizing:border-box}@media screen and (max-width: 576px){.availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]   .availability-column-1[_ngcontent-%COMP%]{width:100%}}.availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]   .day[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between}@media screen and (max-width: 576px){.availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]   .day[_ngcontent-%COMP%]{justify-content:center}}.availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]   .availability-column-2[_ngcontent-%COMP%]{width:50%;padding:5px;box-sizing:border-box}@media only screen and (max-width: 576px){.availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]   .availability-column-2[_ngcontent-%COMP%]{width:100%}}.availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]   .column-title[_ngcontent-%COMP%]{width:50%;padding-right:20px;box-sizing:border-box;font-stretch:normal;font-style:normal;line-height:normal;letter-spacing:normal;text-align:left}.availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]   .title-color[_ngcontent-%COMP%]{color:#93949e}.availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]   .more-arrow[_ngcontent-%COMP%]{position:absolute;left:-25px;top:15px}@media screen and (max-width: 576px){.availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]   .more-arrow[_ngcontent-%COMP%]{left:-15px}}@media screen and (max-width: 576px){.availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]{flex-direction:column}.availability[_ngcontent-%COMP%]   .availability-row[_ngcontent-%COMP%]   .input-fields[_ngcontent-%COMP%]{justify-content:center;display:flex}}.e-custom.e-checkbox-wrapper[_ngcontent-%COMP%]   .e-frame[_ngcontent-%COMP%]{height:16px;line-height:8px;width:16px;border-radius:50px}.e-custom.e-checkbox-wrapper[_ngcontent-%COMP%]   .e-check[_ngcontent-%COMP%]{font-size:10px;background-color:#2fb9d3;outline:2px solid #2fb9d3;outline-offset:3px}.e-custom.e-checkbox-wrapper[_ngcontent-%COMP%]   .e-label[_ngcontent-%COMP%]{font-size:18px}.add-btn[_ngcontent-%COMP%]{position:absolute;right:-34px;top:0;width:20px}.add-btn-2[_ngcontent-%COMP%]{margin-left:15px;width:15px}.light-purple-circle-button[_ngcontent-%COMP%]{width:10px;height:10px}.form-array-title[_ngcontent-%COMP%]{font-weight:700;color:var(--main-color);font-size:20px;margin-top:30px;width:100%;border-bottom:1px solid var(--main-color);padding-bottom:5px}.my-dropdown[_ngcontent-%COMP%]{box-sizing:border-box}@media screen and (max-width: 1024px){.my-dropdown[_ngcontent-%COMP%]{width:90%!important}}.input-field[_ngcontent-%COMP%]{margin-top:0!important;font-size:18px!important;padding:0!important;box-sizing:border-box!important;margin-bottom:6px!important}@media screen and (max-width: 1024px){.input-field[_ngcontent-%COMP%]{width:50%!important;flex-basis:50%!important;padding:0 0 0 10px!important}}.input-element-title[_ngcontent-%COMP%]{text-align:center!important;font-weight:400!important}.input-element[_ngcontent-%COMP%]{margin-top:10px!important;padding:5px!important;box-sizing:border-box;font-size:15px!important}.profile-info-section[_ngcontent-%COMP%]{padding:0!important}.input-error[_ngcontent-%COMP%]{text-align:center!important}.trash[_ngcontent-%COMP%]{position:absolute;right:-34px;bottom:0;cursor:pointer}[_nghost-%COMP%]     .p-dropdown.hours-dropdown{margin-top:0}.addDayIcon[_ngcontent-%COMP%]{position:absolute;right:-34px;bottom:10px}.day-title[_ngcontent-%COMP%]{font-size:19px;font-weight:400;font-stretch:normal;font-style:normal;line-height:normal;letter-spacing:normal;text-align:left;color:#2d2a4b}@media screen and (max-width: 576px){.addDayIcon[_ngcontent-%COMP%]{position:relative;right:auto;width:auto;bottom:0;margin-top:10px}}\"]\n    });\n  }\n  return AvailabilityComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}