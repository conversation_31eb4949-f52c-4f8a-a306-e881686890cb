{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { SharedLinkRoutingModule } from './shared-link-routing.module';\nimport * as i0 from \"@angular/core\";\nexport let SharedLinkModule = /*#__PURE__*/(() => {\n  class SharedLinkModule {\n    static #_ = this.ɵfac = function SharedLinkModule_Factory(t) {\n      return new (t || SharedLinkModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SharedLinkModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedLinkRoutingModule]\n    });\n  }\n  return SharedLinkModule;\n})();", "map": {"version": 3, "names": ["CommonModule", "SharedLinkRoutingModule", "SharedLinkModule", "_", "_2", "_3"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\shared-link\\shared-link.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { SharedLinkRoutingModule } from './shared-link-routing.module';\r\nimport { SharedCertificateComponent } from './shared-certificate.component';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    SharedCertificateComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    SharedLinkRoutingModule\r\n  ]\r\n})\r\nexport class SharedLinkModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,uBAAuB,QAAQ,8BAA8B;;AAatE,WAAaC,gBAAgB;EAAvB,MAAOA,gBAAgB;IAAA,QAAAC,CAAA,G;uBAAhBD,gBAAgB;IAAA;IAAA,QAAAE,EAAA,G;YAAhBF;IAAgB;IAAA,QAAAG,EAAA,G;gBAJzBL,YAAY,EACZC,uBAAuB;IAAA;;SAGdC,gBAAgB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}