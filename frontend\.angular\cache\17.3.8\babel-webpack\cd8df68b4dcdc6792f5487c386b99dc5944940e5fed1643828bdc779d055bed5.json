{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/core/services/auth.service\";\nconst _c0 = [\"confirmCircleLottie\"];\nexport let ConfirmComponent = /*#__PURE__*/(() => {\n  class ConfirmComponent {\n    constructor(activatedRoute, authService, router) {\n      this.activatedRoute = activatedRoute;\n      this.authService = authService;\n      this.router = router;\n      this.firstStep = {};\n    }\n    ngOnInit() {}\n    ngAfterViewInit() {\n      this.addLottiePlayerToStep(this.firstStep);\n      const params = this.activatedRoute.snapshot.queryParams;\n      this.authService.confirmUser(params.userId, params.code);\n      // this.activatedRoute.queryParams.pipe(take(1)).subscribe((params: Params) => {\n      // });\n    }\n    addLottiePlayerToStep(step) {\n      const lottiePlayer = `<div  class=\"animation-wrapper\"><lottie-player src=\"https://assets-v2.lottiefiles.com/a/a5aa5c06-117e-11ee-bad5-4b080a01fe62/kiQH7jlOEB.json\" background=\"transparent\" \n    speed=\"1\" style=\"max-width: 310px; max-height: 310px;\" autoplay loop></lottie-player>\n    <img src=\"assets/icons/user-wait.png\" class=\"max-w-22rem img-centered\" style=\"\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n    \" />\n    </div>`;\n      step.nativeElement.innerHTML = lottiePlayer;\n    }\n    static #_ = this.ɵfac = function ConfirmComponent_Factory(t) {\n      return new (t || ConfirmComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i1.Router));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ConfirmComponent,\n      selectors: [[\"app-confirm\"]],\n      viewQuery: function ConfirmComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.firstStep = _t.first);\n        }\n      },\n      decls: 6,\n      vars: 0,\n      consts: [[\"confirmCircleLottie\", \"\"], [1, \"confirm\"], [1, \"animation-wrapper\", \"relative\"], [1, \"title\", \"text-primary\", \"font-semibold\", \"font-2xl\", \"text-center\", \"max-w-17rem\", \"line-height-2\"]],\n      template: function ConfirmComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n          i0.ɵɵelement(2, \"div\", null, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3);\n          i0.ɵɵtext(5, \" Please wait a moment while we confirm your account... \");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      styles: [\".confirm[_ngcontent-%COMP%]{width:100%;display:flex;height:700px;align-items:center;justify-content:center;flex-direction:column}.confirm[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:1rem;margin:20px}.confirm[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:60%}.animation-wrapper[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center}.img-centered[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%)}\"]\n    });\n  }\n  return ConfirmComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}