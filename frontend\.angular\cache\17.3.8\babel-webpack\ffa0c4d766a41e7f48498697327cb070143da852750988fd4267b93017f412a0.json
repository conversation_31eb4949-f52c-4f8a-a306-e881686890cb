{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { LibraryRoutingModule } from './library-routing.module';\nimport { LibraryComponent } from './library/library.component';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport { FormsModule } from '@angular/forms';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { LibraryFileRowItemComponent } from './library/components/library-file-row-item/library-file-row-item.component';\nimport { LibraryLeftSidebarComponent } from './library/components/library-left-sidebar/library-left-sidebar.component';\nimport { DynamicDialogRef } from 'primeng/dynamicdialog';\nimport { LibraryFileActionsDialogComponent } from './library/components/library-file-actions-dialog/library-file-actions-dialog.component';\nimport { SingleLibraryComponent } from './library/components/single-library/single-library.component';\nimport { SingleLibraryFolderItemComponent } from './library/components/single-library-folder-item/single-library-folder-item.component';\nimport { FilterPipe } from 'src/app/core/pipes/filter.pipe';\nimport * as i0 from \"@angular/core\";\nexport class LibraryModule {\n  static #_ = this.ɵfac = function LibraryModule_Factory(t) {\n    return new (t || LibraryModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: LibraryModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    providers: [DynamicDialogRef],\n    imports: [CommonModule, LibraryRoutingModule, SharedModule, FormsModule, ConfirmDialogModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(LibraryModule, {\n    declarations: [LibraryComponent, LibraryFileRowItemComponent, LibraryLeftSidebarComponent, LibraryFileActionsDialogComponent, SingleLibraryComponent, SingleLibraryFolderItemComponent, FilterPipe],\n    imports: [CommonModule, LibraryRoutingModule, SharedModule, FormsModule, ConfirmDialogModule],\n    exports: [LibraryComponent, LibraryLeftSidebarComponent, SingleLibraryComponent, LibraryFileActionsDialogComponent, FilterPipe]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "LibraryRoutingModule", "LibraryComponent", "SharedModule", "FormsModule", "ConfirmDialogModule", "LibraryFileRowItemComponent", "LibraryLeftSidebarComponent", "DynamicDialogRef", "LibraryFileActionsDialogComponent", "SingleLibraryComponent", "SingleLibraryFolderItemComponent", "FilterPipe", "LibraryModule", "_", "_2", "_3", "imports", "declarations", "exports"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\library\\library.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { LibraryRoutingModule } from './library-routing.module';\r\nimport { LibraryComponent } from './library/library.component';\r\nimport { BrowserModule } from '@angular/platform-browser';\r\nimport { SharedModule } from 'src/app/shared/shared.module';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\r\nimport { LibraryFileRowItemComponent } from './library/components/library-file-row-item/library-file-row-item.component';\r\nimport { LibraryLeftSidebarComponent } from './library/components/library-left-sidebar/library-left-sidebar.component';\r\nimport { DynamicDialogRef } from 'primeng/dynamicdialog';\r\nimport { LibraryFileActionsDialogComponent } from './library/components/library-file-actions-dialog/library-file-actions-dialog.component';\r\nimport { SingleLibraryComponent } from './library/components/single-library/single-library.component';\r\nimport { SingleLibraryFolderItemComponent } from './library/components/single-library-folder-item/single-library-folder-item.component';\r\nimport { FilterPipe } from 'src/app/core/pipes/filter.pipe';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    LibraryComponent,\r\n    LibraryFileRowItemComponent,\r\n    LibraryLeftSidebarComponent,\r\n    LibraryFileActionsDialogComponent,\r\n    SingleLibraryComponent,\r\n    SingleLibraryFolderItemComponent,\r\n    FilterPipe\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    LibraryRoutingModule,\r\n    SharedModule,\r\n    FormsModule,\r\n    ConfirmDialogModule,\r\n  ],\r\n  exports: [\r\n    LibraryComponent,\r\n    LibraryLeftSidebarComponent,\r\n    SingleLibraryComponent,\r\n    LibraryFileActionsDialogComponent,\r\n    FilterPipe\r\n  ],\r\n  providers: [DynamicDialogRef]\r\n})\r\nexport class LibraryModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,gBAAgB,QAAQ,6BAA6B;AAE9D,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,2BAA2B,QAAQ,4EAA4E;AACxH,SAASC,2BAA2B,QAAQ,0EAA0E;AACtH,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,iCAAiC,QAAQ,wFAAwF;AAC1I,SAASC,sBAAsB,QAAQ,8DAA8D;AACrG,SAASC,gCAAgC,QAAQ,sFAAsF;AACvI,SAASC,UAAU,QAAQ,gCAAgC;;AA6B3D,OAAM,MAAOC,aAAa;EAAA,QAAAC,CAAA,G;qBAAbD,aAAa;EAAA;EAAA,QAAAE,EAAA,G;UAAbF;EAAa;EAAA,QAAAG,EAAA,G;eAFb,CAACR,gBAAgB,CAAC;IAAAS,OAAA,GAb3BjB,YAAY,EACZC,oBAAoB,EACpBE,YAAY,EACZC,WAAW,EACXC,mBAAmB;EAAA;;;2EAWVQ,aAAa;IAAAK,YAAA,GAxBtBhB,gBAAgB,EAChBI,2BAA2B,EAC3BC,2BAA2B,EAC3BE,iCAAiC,EACjCC,sBAAsB,EACtBC,gCAAgC,EAChCC,UAAU;IAAAK,OAAA,GAGVjB,YAAY,EACZC,oBAAoB,EACpBE,YAAY,EACZC,WAAW,EACXC,mBAAmB;IAAAc,OAAA,GAGnBjB,gBAAgB,EAChBK,2BAA2B,EAC3BG,sBAAsB,EACtBD,iCAAiC,EACjCG,UAAU;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}