{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { NoteComponent } from './note.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: NoteComponent\n}];\nexport let NoteRoutingModule = /*#__PURE__*/(() => {\n  class NoteRoutingModule {\n    static #_ = this.ɵfac = function NoteRoutingModule_Factory(t) {\n      return new (t || NoteRoutingModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: NoteRoutingModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forChild(routes), RouterModule]\n    });\n  }\n  return NoteRoutingModule;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}