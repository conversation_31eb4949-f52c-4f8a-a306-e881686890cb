{"ast": null, "code": "import { DOCUMENT } from '@angular/common';\nimport { EventEmitter, inject } from '@angular/core';\nimport { ChatService } from 'src/app/core/services/chat.service';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/core/services/general.service\";\nimport * as i3 from \"src/app/core/services/auth.service\";\nimport * as i4 from \"src/app/core/services/layout.service\";\nimport * as i5 from \"src/app/core/services/notification.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"primeng/badge\";\nconst _c0 = () => [\"/notifications\"];\nconst _c1 = () => [\"/chat\"];\nconst _c2 = a0 => ({\n  \"disabled-link\": a0\n});\nfunction MobileMenuComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"div\", 34);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction MobileMenuComponent_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 8)(1, \"a\", 9)(2, \"div\", 10);\n    i0.ɵɵelement(3, \"i\", 35);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(2, _c1));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", 0);\n  }\n}\nfunction MobileMenuComponent_ng_container_21_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 37);\n    i0.ɵɵlistener(\"click\", function MobileMenuComponent_ng_container_21_li_1_Template_li_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const item_r3 = i0.ɵɵnextContext().$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.navigateTo(item_r3.url));\n    });\n    i0.ɵɵelementStart(1, \"div\", 38)(2, \"span\", 39);\n    i0.ɵɵelement(3, \"lottie-player\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c2, item_r3.disabled))(\"tabindex\", item_r3.disabled ? -1 : null);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"width\", \"50\", \"px\")(\"height\", \"50\", \"px\")(\"transform\", \"scale(\" + item_r3.scale + \")\");\n    i0.ɵɵproperty(\"src\", item_r3.lottieIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r3.title, \" \");\n  }\n}\nfunction MobileMenuComponent_ng_container_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, MobileMenuComponent_ng_container_21_li_1_Template, 5, 12, \"li\", 36);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r3.role.includes(ctx_r3.role));\n  }\n}\nfunction MobileMenuComponent_ng_container_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 41);\n    i0.ɵɵlistener(\"click\", function MobileMenuComponent_ng_container_22_Template_li_click_1_listener() {\n      const item_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(item_r6.url ? ctx_r3.navigateTo(item_r6.url) : item_r6.method());\n    });\n    i0.ɵɵelementStart(2, \"div\", 38)(3, \"span\", 39);\n    i0.ɵɵelement(4, \"lottie-player\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r6 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵstyleProp(\"width\", \"50\", \"px\")(\"height\", \"50\", \"px\")(\"transform\", \"scale(\" + item_r6.scale + \")\");\n    i0.ɵɵproperty(\"src\", item_r6.lottieIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", item_r6.title, \" \");\n  }\n}\nexport let MobileMenuComponent = /*#__PURE__*/(() => {\n  class MobileMenuComponent {\n    constructor(router, generalService, authService, changeDetector, layoutService, notificationService, renderer, document) {\n      this.router = router;\n      this.generalService = generalService;\n      this.authService = authService;\n      this.changeDetector = changeDetector;\n      this.layoutService = layoutService;\n      this.notificationService = notificationService;\n      this.renderer = renderer;\n      this.document = document;\n      this.init = false;\n      this.opened = new EventEmitter();\n      this.chatService = inject(ChatService);\n      this.role = {};\n      this.active = false;\n      this.subs = new SubSink();\n      this.mobileMenuTitle = '';\n      this.burgerClasses = {\n        burger: '',\n        nava: '',\n        overlay: '',\n        body: '',\n        menu: 'hidden'\n      };\n      this.extraMobileMenuItems = [];\n      this.unreadNotifications = [];\n      this.socialLinks = {\n        linkedin: 'https://gr.linkedin.com/company/mylingotrip',\n        twitter: 'https://twitter.com/MyLingoTrip',\n        facebook: 'https://www.facebook.com/mylingotrip',\n        youtube: 'https://www.youtube.com/c/MyLingoTrip',\n        instagram: 'https://www.instagram.com/mylingotrip/'\n      };\n    }\n    ngOnInit() {\n      // this.burgerClick();\n      this.role = this.authService.getUserRole();\n      this.menuItems = this.layoutService.getMenuItems();\n      this.extraMobileMenuItems = this.layoutService.getExtraMobileMenuItems();\n      this.subs.add(this.notificationService.get().subscribe(res => {\n        // Filter notifications to get only the unread ones\n        this.unreadNotifications = res.filter(notification => !notification.isRead);\n      }));\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n    }\n    ngAfterContentChecked() {\n      this.subs.sink = this.generalService.mobileMenuTitle.subscribe(res => {\n        this.mobileMenuTitle = res;\n        this.changeDetector.detectChanges();\n      });\n    }\n    onBurgerClicked() {\n      this.active = !this.active;\n      this.opened.emit();\n    }\n    toggleMobileMenuItems() {\n      var mobileMenuItems = document.getElementById('mobile-menu-items');\n      if (mobileMenuItems.clientHeight) {\n        mobileMenuItems.style.height = \"0\";\n      } else {\n        mobileMenuItems.style.height = window.innerHeight + \"px\";\n      }\n    }\n    logout() {\n      this.authService.logout();\n    }\n    navigateTo(route) {\n      this.onBurgerClicked();\n      this.burgerClick();\n      this.router.navigateByUrl(route, {\n        replaceUrl: true\n      });\n      // this.router.navigate([route]);\n    }\n    burgerClick() {\n      this.burgerClasses.burger = this.burgerClasses.burger === 'clicked' ? '' : 'clicked';\n      this.burgerClasses.overlay = this.burgerClasses.overlay === 'show' ? '' : 'show';\n      this.burgerClasses.nava = this.burgerClasses.nava === 'show' ? '' : 'show';\n      this.burgerClasses.menu = this.burgerClasses.menu === 'hidden' ? '' : 'hidden';\n      this.burgerClasses.body = 'overflow';\n      if (this.burgerClasses.burger === 'clicked') {\n        this.renderer.addClass(this.document.body, 'overflow-hidden');\n      } else {\n        this.renderer.removeClass(this.document.body, 'overflow-hidden');\n      }\n    }\n    static #_ = this.ɵfac = function MobileMenuComponent_Factory(t) {\n      return new (t || MobileMenuComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.GeneralService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i4.LayoutService), i0.ɵɵdirectiveInject(i5.NotificationService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(DOCUMENT));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MobileMenuComponent,\n      selectors: [[\"app-mobile-menu\"]],\n      inputs: {\n        init: \"init\",\n        menuItems: \"menuItems\"\n      },\n      outputs: {\n        opened: \"opened\"\n      },\n      decls: 48,\n      vars: 16,\n      consts: [[\"myDiv\", \"\"], [1, \"mobile-menu\", \"px-2\"], [1, \"ml-2\"], [\"href\", \"https://www.mylingotrip.com\", \"target\", \"_blank\"], [\"src\", \"/assets/icons/logo/mlt-logo-white.svg\", \"alt\", \"mlt-logo-white\", 1, \"logo-img\"], [4, \"ngIf\"], [1, \"flex\", \"ml-auto\"], [1, \"list-none\", \"select-none\", \"flex\", \"flex-row\", \"md:border-top-none\"], [\"id\", \"chats-dropdown\", 1, \"relative\", \"lg:pl-3\", \"lg:ml-2\", 2, \"z-index\", \"55\"], [\"title\", \"Chat\", 1, \"flex\", \"align-items-center\", \"text-indigo-100\", \"note-badge\", \"hover:text-indigo-50\", \"hover:bg-indigo-700\", \"font-medium\", \"border-round\", \"cursor-pointer\", 3, \"routerLink\"], [1, \"notaa\", \"flex\", \"align-items-center\", \"justify-content-center\", \"mr-3\", 2, \"z-index\", \"1\", \"pointer-events\", \"none\"], [\"pBadge\", \"\", 1, \"pi\", \"pi-bell\", \"pb-0\", \"text-2xl\", \"note-badge\", 3, \"value\"], [1, \"nava\", 3, \"ngClass\"], [1, \"wpo-site-footer\", 3, \"ngClass\"], [1, \"wpo-upper-footer\"], [1, \"container\"], [1, \"grid\", \"align-items-start\", \"justify-content-center\"], [1, \"col-12\", \"sm:col-12\", \"lg:col-2\", \"md:col-4\"], [1, \"widget\", \"link-widget\", \"flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\"], [1, \"pl-0\"], [4, \"ngFor\", \"ngForOf\"], [1, \"wpo-lower-footer\"], [1, \"row\"], [1, \"col-12\", \"text-center\"], [1, \"about-widget\", \"text-center\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", \"gap-1\", \"p-0\"], [\"target\", \"_blank\", 3, \"href\"], [\"src\", \"/assets/icons/navbar/facebook.svg\", \"alt\", \"facebook Image\"], [\"src\", \"/assets/icons/navbar/instagram.svg\", \"alt\", \"instagram Image\"], [\"src\", \"/assets/icons/navbar/linkedin.svg\", \"alt\", \"linkedin Image\"], [\"src\", \"/assets/icons/navbar/twitter.svg\", \"alt\", \"twitter Image\"], [\"src\", \"/assets/icons/navbar/youtube.svg\", \"alt\", \"youtube Image\"], [1, \"overlay\", 3, \"ngClass\"], [1, \"burger\", 3, \"click\", \"ngClass\"], [1, \"mobile-title-header\"], [\"pBadge\", \"\", 1, \"pi\", \"pi-comments\", \"pb-0\", \"text-2xl\", \"note-badge\", 3, \"value\"], [\"class\", \"hover:bg-indigo-600 pointer flex align-items-center font-3xl gap-2\", 3, \"ngClass\", \"tabindex\", \"click\", 4, \"ngIf\"], [1, \"hover:bg-indigo-600\", \"pointer\", \"flex\", \"align-items-center\", \"font-3xl\", \"gap-2\", 3, \"click\", \"ngClass\", \"tabindex\"], [1, \"icon-container\"], [1, \"lottie-wrapper\"], [\"background\", \"transparent\", \"speed\", \"1\", \"loop\", \"\", 3, \"src\"], [1, \"hover:bg-indigo-600\", \"pointer\", \"flex\", \"align-items-center\", \"font-3xl\", \"gap-2\", 3, \"click\"]],\n      template: function MobileMenuComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"a\", 3);\n          i0.ɵɵelement(3, \"img\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(4, MobileMenuComponent_ng_container_4_Template, 2, 0, \"ng-container\", 5);\n          i0.ɵɵelementStart(5, \"div\", 6)(6, \"ul\", 7, 0);\n          i0.ɵɵtemplate(8, MobileMenuComponent_Conditional_8_Template, 4, 3, \"li\", 8);\n          i0.ɵɵelementStart(9, \"li\", 8)(10, \"a\", 9)(11, \"div\", 10);\n          i0.ɵɵelement(12, \"i\", 11);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(13, \"div\", 12)(14, \"div\", 13)(15, \"div\", 14)(16, \"div\", 15)(17, \"div\", 16)(18, \"div\", 17)(19, \"div\", 18)(20, \"ul\", 19);\n          i0.ɵɵtemplate(21, MobileMenuComponent_ng_container_21_Template, 2, 1, \"ng-container\", 20)(22, MobileMenuComponent_ng_container_22_Template, 6, 8, \"ng-container\", 20);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(23, \"div\", 21)(24, \"div\", 15)(25, \"div\", 22)(26, \"div\", 23)(27, \"p\", 24);\n          i0.ɵɵtext(28, \"Let\\u2019s get social!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"ul\", 25)(30, \"li\")(31, \"a\", 26);\n          i0.ɵɵelement(32, \"img\", 27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(33, \"li\")(34, \"a\", 26);\n          i0.ɵɵelement(35, \"img\", 28);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"li\")(37, \"a\", 26);\n          i0.ɵɵelement(38, \"img\", 29);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"li\")(40, \"a\", 26);\n          i0.ɵɵelement(41, \"img\", 30);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"li\")(43, \"a\", 26);\n          i0.ɵɵelement(44, \"img\", 31);\n          i0.ɵɵelementEnd()()()()()()()()()();\n          i0.ɵɵelement(45, \"div\", 32);\n          i0.ɵɵelementStart(46, \"div\", 33);\n          i0.ɵɵlistener(\"click\", function MobileMenuComponent_Template_div_click_46_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.burgerClick());\n          });\n          i0.ɵɵelement(47, \"span\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.mobileMenuTitle !== \"\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵconditional(8, ctx.chatService.isChatEnabled() ? 8 : -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(15, _c0));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"value\", ctx.unreadNotifications.length > 0 ? ctx.unreadNotifications.length : 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", ctx.burgerClasses.nava);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", ctx.burgerClasses.menu);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngForOf\", ctx.menuItems);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.extraMobileMenuItems);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"href\", ctx.socialLinks.facebook, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"href\", ctx.socialLinks.instagram, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"href\", ctx.socialLinks.linkedin, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"href\", ctx.socialLinks.twitter, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"href\", ctx.socialLinks.youtube, i0.ɵɵsanitizeUrl);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", ctx.burgerClasses.overlay);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", ctx.burgerClasses.burger);\n        }\n      },\n      dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i1.RouterLink, i7.BadgeDirective],\n      styles: [\".burger-menu[_ngcontent-%COMP%]{width:24px;height:24px;cursor:pointer}.burger-menu[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]{position:relative}.burger-menu[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]   .burger-inner[_ngcontent-%COMP%]{position:absolute;width:22px;left:0;top:11px;border:1px solid #ffffff;transition:border-color .2s ease-in .2s}.burger-menu[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]   .burger-inner[_ngcontent-%COMP%]:before{position:absolute;content:\\\"\\\";top:-6px;left:-1px;width:22px;border:1px solid #ffffff;transition:top .2s ease-in .3s,transform ease-in .2s}.burger-menu[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]   .burger-inner[_ngcontent-%COMP%]:after{position:absolute;content:\\\"\\\";top:4px;left:-1px;width:22px;border:1px solid #ffffff;transition:top .2s ease-in .3s,transform ease-in .2s}.burger-menu.active[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]   .burger-inner[_ngcontent-%COMP%]{border-color:transparent;transition:border-color ease-in .1s}.burger-menu.active[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]   .burger-inner[_ngcontent-%COMP%]:before{top:-1px;transform:rotate(-45deg);transition:top ease-out .2s,transform .2s ease-out .3s}.burger-menu.active[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]   .burger-inner[_ngcontent-%COMP%]:after{top:-1px;transform:rotate(45deg);transition:top ease-out .2s,transform .2s ease-out .3s}.lottie-wrapper[_ngcontent-%COMP%]{mix-blend-mode:luminosity}.icon-container[_ngcontent-%COMP%]{border-radius:1.25rem;mix-blend-mode:luminosity}.mobile-menu[_ngcontent-%COMP%]{width:100%;position:fixed;background:#657aef;background-image:linear-gradient(90deg,#23307b,#273482 26%,#2c398c 48%,#303d91 65%,#35439c 76%,#3b4aa5 84%,#3f50ab 90%,#4556b5 94%,#4b5cbe,#4d5fc7);display:none;z-index:100;color:var(--white);height:64px;top:0}@media screen and (max-width: 768px){.mobile-menu[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:space-between}}.mobile-menu[_ngcontent-%COMP%]   .burger[_ngcontent-%COMP%]{width:50px;height:50px;position:relative;z-index:10;border-radius:100px}.mobile-menu[_ngcontent-%COMP%]   .burger[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{position:relative;margin-bottom:9px;-webkit-user-select:none;user-select:none;position:absolute;top:50%;left:50%;margin-left:-15px;margin-top:-1.5px}.mobile-menu[_ngcontent-%COMP%]   .burger[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], .mobile-menu[_ngcontent-%COMP%]   .burger[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:before, .mobile-menu[_ngcontent-%COMP%]   .burger[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:after{display:block;width:30px;height:3px;background-color:#fff;outline:1px solid transparent;transition-property:background-color,transform;transition-duration:.3s}.mobile-menu[_ngcontent-%COMP%]   .burger[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:before, .mobile-menu[_ngcontent-%COMP%]   .burger[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:after{position:absolute;content:\\\"\\\"}.mobile-menu[_ngcontent-%COMP%]   .burger[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:before{top:-9px}.mobile-menu[_ngcontent-%COMP%]   .burger[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:after{top:9px}.mobile-menu[_ngcontent-%COMP%]   .burger.clicked[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{background-color:transparent}.mobile-menu[_ngcontent-%COMP%]   .burger.clicked[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:before{transform:translateY(9px) rotate(45deg)}.mobile-menu[_ngcontent-%COMP%]   .burger.clicked[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:after{transform:translateY(-9px) rotate(-45deg)}.mobile-menu[_ngcontent-%COMP%]   .burger.clicked[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:before, .mobile-menu[_ngcontent-%COMP%]   .burger.clicked[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:after{background-color:#fff}.mobile-menu[_ngcontent-%COMP%]   .burger[_ngcontent-%COMP%]:hover{cursor:pointer}.mobile-menu[_ngcontent-%COMP%]   .nava[_ngcontent-%COMP%]{background-image:linear-gradient(140deg,#3445a8,#3c4daf 35%,#4556ba 57%,#4c5cc2 70%,#5363ca 78%,#5e70d4 85%,#6577dc,#7082e6,#7688ef 97%,#7c8ff8);position:fixed;z-index:9;top:0;right:0;height:100%;max-width:100%;width:100%;padding:45px 0;overflow-y:auto;visibility:hidden;transform:translate(100%)}.mobile-menu[_ngcontent-%COMP%]   .nava.show[_ngcontent-%COMP%]{visibility:visible;transform:translate(0)}@media (min-width: 667px){.mobile-menu[_ngcontent-%COMP%]   .nava[_ngcontent-%COMP%]{padding:120px 40px 70px}}.mobile-menu[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;z-index:1;width:100%;height:100%;transition:all .3s ease-in-out;background-color:#8497ff;opacity:0;visibility:hidden}.mobile-menu[_ngcontent-%COMP%]   .overlay.show[_ngcontent-%COMP%]{opacity:.8;visibility:visible}.logo-img[_ngcontent-%COMP%]{width:50px}.icon[_ngcontent-%COMP%]{display:block;position:absolute;right:0;top:0;border:2px solid #ffffff;border-radius:50px}.mobile-tab[_ngcontent-%COMP%]{padding:10px;text-align:center}#mobile-menu-items[_ngcontent-%COMP%]{transition:height .5s;height:0;overflow:hidden;width:calc(100% - 100px)}.measuringWrapper[_ngcontent-%COMP%]{margin-top:80px}.mobile-title-header[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%)}.wpo-site-footer[_ngcontent-%COMP%]{position:relative;font-size:15px;overflow:hidden;width:100%}.wpo-site-footer[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{list-style:none}.wpo-site-footer[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#fff}.wpo-site-footer[_ngcontent-%COMP%]   li[_ngcontent-%COMP%], .wpo-site-footer[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{letter-spacing:.01em;text-align:left;color:#fff;text-decoration:none;line-height:2;mix-blend-mode:luminosity}.wpo-site-footer[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%]{position:relative}.wpo-site-footer[_ngcontent-%COMP%]   .wpo-upper-footer[_ngcontent-%COMP%]{padding:10px 0}@media (max-width: 991px){.wpo-site-footer[_ngcontent-%COMP%]   .wpo-upper-footer[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]{min-height:235px;margin-bottom:70px}}@media (max-width: 767px){.wpo-site-footer[_ngcontent-%COMP%]   .wpo-upper-footer[_ngcontent-%COMP%]   .col[_ngcontent-%COMP%]{min-height:auto;margin-bottom:60px}}.wpo-site-footer[_ngcontent-%COMP%]   .widget-title[_ngcontent-%COMP%]{align-self:center}.wpo-site-footer[_ngcontent-%COMP%]   .widget-title[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:20px;margin:0;position:relative;font-weight:400;font-size:28px;line-height:24px;text-align:left;color:#fff;opacity:.89}@media (max-width: 991px){.wpo-site-footer[_ngcontent-%COMP%]   .widget-title[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:20px;font-size:1.25rem}}.wpo-site-footer[_ngcontent-%COMP%]   .about-widget[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]{max-width:180px}.wpo-site-footer[_ngcontent-%COMP%]   .about-widget[_ngcontent-%COMP%]{font-size:38px;letter-spacing:.05em;line-height:70px;text-align:left;color:#fff;opacity:.89}.circle-singleline[_ngcontent-%COMP%]{width:24px;height:24px;border-radius:50%;font-size:1rem;line-height:24px;text-align:center;color:#cbd3ff;opacity:.89;border:1px solid;left:-11px;position:relative;display:inline-block}\"]\n    });\n  }\n  return MobileMenuComponent;\n})();", "map": {"version": 3, "names": ["DOCUMENT", "EventEmitter", "inject", "ChatService", "SubSink", "i0", "ɵɵelementContainerStart", "ɵɵelement", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction0", "_c1", "ɵɵlistener", "MobileMenuComponent_ng_container_21_li_1_Template_li_click_0_listener", "ɵɵrestoreView", "_r2", "item_r3", "ɵɵnextContext", "$implicit", "ctx_r3", "ɵɵresetView", "navigateTo", "url", "ɵɵtext", "ɵɵpureFunction1", "_c2", "disabled", "ɵɵstyleProp", "scale", "lottieIcon", "ɵɵtextInterpolate1", "title", "ɵɵtemplate", "MobileMenuComponent_ng_container_21_li_1_Template", "role", "includes", "MobileMenuComponent_ng_container_22_Template_li_click_1_listener", "item_r6", "_r5", "method", "MobileMenuComponent", "constructor", "router", "generalService", "authService", "changeDetector", "layoutService", "notificationService", "renderer", "document", "init", "opened", "chatService", "active", "subs", "mobileMenuTitle", "burgerClasses", "burger", "nava", "overlay", "body", "menu", "extraMobileMenuItems", "unreadNotifications", "socialLinks", "linkedin", "twitter", "facebook", "youtube", "instagram", "ngOnInit", "getUserRole", "menuItems", "getMenuItems", "getExtraMobileMenuItems", "add", "get", "subscribe", "res", "filter", "notification", "isRead", "ngOnDestroy", "unsubscribe", "ngAfterContentChecked", "sink", "detectChanges", "onBurgerClicked", "emit", "toggleMobileMenuItems", "mobileMenuItems", "getElementById", "clientHeight", "style", "height", "window", "innerHeight", "logout", "route", "burgerClick", "navigateByUrl", "replaceUrl", "addClass", "removeClass", "_", "ɵɵdirectiveInject", "i1", "Router", "i2", "GeneralService", "i3", "AuthService", "ChangeDetectorRef", "i4", "LayoutService", "i5", "NotificationService", "Renderer2", "_2", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "MobileMenuComponent_Template", "rf", "ctx", "MobileMenuComponent_ng_container_4_Template", "MobileMenuComponent_Conditional_8_Template", "MobileMenuComponent_ng_container_21_Template", "MobileMenuComponent_ng_container_22_Template", "MobileMenuComponent_Template_div_click_46_listener", "_r1", "ɵɵconditional", "isChatEnabled", "_c0", "length", "ɵɵsanitizeUrl"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\mobile-menu\\mobile-menu.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\mobile-menu\\mobile-menu.component.html"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\r\nimport { AfterContentChecked, ChangeDetectorRef, Component, EventEmitter, inject, Inject, Input, OnInit, Output, Renderer2 } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { LeftMenuItem } from 'src/app/core/models/general.model';\r\nimport { Notification } from 'src/app/core/models/notification.model';\r\nimport { UserRole } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { ChatService } from 'src/app/core/services/chat.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { LayoutService } from 'src/app/core/services/layout.service';\r\nimport { NotificationService } from 'src/app/core/services/notification.service';\r\nimport { SubSink } from 'subsink';\r\n\r\n@Component({\r\n  selector: 'app-mobile-menu',\r\n  templateUrl: './mobile-menu.component.html',\r\n  styleUrls: ['./mobile-menu.component.scss']\r\n})\r\nexport class MobileMenuComponent implements OnInit, AfterContentChecked {\r\n  @Input() init: boolean = false;\r\n  @Input() menuItems!: any;\r\n  @Output() opened = new EventEmitter<any>();\r\n  chatService = inject(ChatService);\r\n  public role: UserRole = {} as UserRole;\r\n  active = false;\r\n  subs = new SubSink();\r\n  mobileMenuTitle: string = '';\r\n  burgerClasses = { burger: '', nava: '', overlay: '', body: '', menu: 'hidden' };\r\n  extraMobileMenuItems = [] as LeftMenuItem[];\r\n  unreadNotifications: Notification[] = [];\r\n  socialLinks =\r\n    {\r\n      linkedin: 'https://gr.linkedin.com/company/mylingotrip',\r\n      twitter: 'https://twitter.com/MyLingoTrip',\r\n      facebook: 'https://www.facebook.com/mylingotrip',\r\n      youtube: 'https://www.youtube.com/c/MyLingoTrip',\r\n      instagram: 'https://www.instagram.com/mylingotrip/',\r\n    };\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private generalService: GeneralService,\r\n    private authService: AuthService,\r\n    private changeDetector: ChangeDetectorRef,\r\n    private layoutService: LayoutService,\r\n    private notificationService: NotificationService,\r\n    private renderer: Renderer2,\r\n    @Inject(DOCUMENT) private document: Document,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    // this.burgerClick();\r\n    this.role = this.authService.getUserRole();\r\n    this.menuItems = this.layoutService.getMenuItems();\r\n    this.extraMobileMenuItems = this.layoutService.getExtraMobileMenuItems();\r\n\r\n    this.subs.add(this.notificationService.get().subscribe((res: any) => {\r\n      // Filter notifications to get only the unread ones\r\n      this.unreadNotifications = res.filter(\r\n        (notification: Notification) => !notification.isRead\r\n      );\r\n    }));\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  ngAfterContentChecked(): void {\r\n    this.subs.sink = this.generalService.mobileMenuTitle.subscribe((res: string) => {\r\n      this.mobileMenuTitle = res;\r\n      this.changeDetector.detectChanges();\r\n    })\r\n  }\r\n  onBurgerClicked() {\r\n    this.active = !this.active;\r\n    this.opened.emit();\r\n  }\r\n  toggleMobileMenuItems() {\r\n    var mobileMenuItems = document.getElementById('mobile-menu-items');\r\n    if (mobileMenuItems!.clientHeight) {\r\n      mobileMenuItems!.style.height = \"0\";\r\n    } else {\r\n      mobileMenuItems!.style.height = window!.innerHeight + \"px\";\r\n    }\r\n  }\r\n\r\n  logout() {\r\n    this.authService.logout();\r\n  }\r\n\r\n  navigateTo(route: string) {\r\n    this.onBurgerClicked();\r\n    this.burgerClick();\r\n    this.router.navigateByUrl(route, { replaceUrl: true });\r\n    // this.router.navigate([route]);\r\n  }\r\n\r\n  burgerClick() {\r\n    this.burgerClasses.burger = this.burgerClasses.burger === 'clicked' ? '' : 'clicked';\r\n    this.burgerClasses.overlay = this.burgerClasses.overlay === 'show' ? '' : 'show';\r\n    this.burgerClasses.nava = this.burgerClasses.nava === 'show' ? '' : 'show';\r\n    this.burgerClasses.menu = this.burgerClasses.menu === 'hidden' ? '' : 'hidden';\r\n    this.burgerClasses.body = 'overflow';\r\n\r\n    if (this.burgerClasses.burger === 'clicked') {\r\n\r\n      this.renderer.addClass(this.document.body, 'overflow-hidden');\r\n    } else {\r\n\r\n      this.renderer.removeClass(this.document.body, 'overflow-hidden');\r\n    }\r\n  }\r\n}\r\n", "<div class=\"mobile-menu px-2\">\r\n  <div class=\" ml-2\">\r\n    <a href=\"https://www.mylingotrip.com\" target=\"_blank\"> <img src=\"/assets/icons/logo/mlt-logo-white.svg\"\r\n        alt=\"mlt-logo-white\" class=\"logo-img\">\r\n    </a>\r\n  </div>\r\n  <ng-container *ngIf=\"mobileMenuTitle !== ''\">\r\n    <div class=\"mobile-title-header\">\r\n      <!-- Application Progress -->\r\n      <!-- {{mobileMenuTitle}} -->\r\n    </div>\r\n  </ng-container>\r\n\r\n  <div class=\"flex ml-auto\">\r\n    <ul #myDiv class=\"list-none select-none flex flex-row \r\n    md:border-top-none\">\r\n      <!-- TODO: enable for chat -->\r\n      @if (chatService.isChatEnabled()) {\r\n      <li class=\"relative  lg:pl-3 lg:ml-2\" id=\"chats-dropdown\" style=\"z-index: 55\">\r\n        <a title=\"Chat\" [routerLink]=\"['/chat']\" class=\" flex align-items-center text-indigo-100 note-badge \r\nhover:text-indigo-50 hover:bg-indigo-700 font-medium border-round cursor-pointer\">\r\n\r\n          <div class=\"notaa flex align-items-center justify-content-center mr-3\"\r\n            style=\"z-index: 1; pointer-events: none;\">\r\n            <i class=\"pi pi-comments pb-0 text-2xl note-badge \" pBadge [value]=\"0\"></i>\r\n          </div>\r\n        </a>\r\n      </li>\r\n      }\r\n      <li class=\"relative  lg:pl-3 lg:ml-2\" id=\"chats-dropdown\" style=\"z-index: 55\">\r\n        <a title=\"Chat\" [routerLink]=\"['/notifications']\" class=\" flex align-items-center text-indigo-100 note-badge \r\nhover:text-indigo-50 hover:bg-indigo-700 font-medium border-round cursor-pointer\">\r\n\r\n          <div class=\"notaa flex align-items-center justify-content-center mr-3\"\r\n            style=\"z-index: 1; pointer-events: none;\">\r\n            <i class=\"pi pi-bell pb-0 text-2xl note-badge \" pBadge\r\n              [value]=\"this.unreadNotifications.length > 0 ? this.unreadNotifications.length : 0\"></i>\r\n          </div>\r\n        </a>\r\n      </li>\r\n\r\n    </ul>\r\n\r\n    <div class=\"nava\" [ngClass]=\"burgerClasses.nava\">\r\n      <div class=\"wpo-site-footer\" [ngClass]=\"burgerClasses.menu\">\r\n        <div class=\"wpo-upper-footer\">\r\n          <div class=\"container\">\r\n            <div class=\"grid align-items-start justify-content-center\">\r\n\r\n              <!-- <div href=\"javascript:void(0);\" class=\"icon\" (click)=\"toggleMobileMenuItems()\">\r\n                        <i class=\"fa fa-bars\" style=\"font-size:30px; padding: 14px; color:var(--main-color)\"></i>\r\n                    </div> -->\r\n              <!--                     \r\n                    <div class=\"col-12 sm:col-12 lg:col-2 md:col-4\">\r\n                      <div class=\"widget link-widget flex flex-column align-items-center justify-content-center\">\r\n                          <div class=\"widget-title\">\r\n                              <h3 class=\" d-flex align-items-sm-center mb-2 mb-md-4\"><span class=\"circle-singleline\">1</span>Home</h3>\r\n                          </div>\r\n                          <ul>\r\n                            <li><a pButton class=\"btn p-button-outlined p-button-raised p-button-rounded rounded-pill text-decoration-none text-white w-100 text-center mb-2\" href=\"\">\r\n                                LingoGalaxy</a></li>\r\n                            <li><a class=\"btn p-button-outlined p-button-raised p-button-rounded  rounded-pill text-decoration-none text-white w-100 text-center mb-2\" href=\"\">\r\n                                MyLingoBusiness</a></li>\r\n                          </ul>\r\n                      </div>\r\n                  </div> -->\r\n\r\n\r\n              <div class=\"col-12 sm:col-12 lg:col-2 md:col-4\">\r\n                <div class=\"widget link-widget flex flex-column align-items-center justify-content-center\">\r\n                  <!-- <div class=\"widget-title\">\r\n                            <h3 class=\" d-flex align-items-sm-center mb-2 mb-md-4\"><span class=\"circle-singleline\">1</span>Home</h3>\r\n                        </div> -->\r\n                  <ul class=\"pl-0\">\r\n                    <ng-container *ngFor=\"let item of menuItems; let i = index;\">\r\n                      <li *ngIf=\"item.role.includes(role)\"\r\n                        class=\"hover:bg-indigo-600 pointer flex align-items-center font-3xl gap-2\"\r\n                        [ngClass]=\"{'disabled-link': item.disabled}\" (click)=\"navigateTo(item.url)\"\r\n                        [tabindex]=\"item.disabled ? -1 : null\">\r\n                        <div class=\"icon-container\">\r\n                          <span class=\"lottie-wrapper\">\r\n                            <lottie-player [src]=\"item.lottieIcon\" background=\"transparent\" speed=\"1\"\r\n                              [style.width.px]=\"'50'\" [style.height.px]=\"'50'\"\r\n                              [style.transform]=\"'scale(' + item.scale + ')'\" loop></lottie-player>\r\n                          </span>\r\n                          <!-- <img [src]=\"item.icon\" class=\"icon\"> -->\r\n                        </div>\r\n\r\n                        {{item.title}}\r\n                      </li>\r\n                    </ng-container>\r\n                    <ng-container *ngFor=\"let item of extraMobileMenuItems; let i = index;\">\r\n                      <li class=\"hover:bg-indigo-600 pointer flex align-items-center font-3xl gap-2\"\r\n                        (click)=\"item.url ? navigateTo(item.url): item.method()\">\r\n\r\n                        <div class=\"icon-container\">\r\n                          <span class=\"lottie-wrapper\">\r\n                            <lottie-player [src]=\"item.lottieIcon\" background=\"transparent\" speed=\"1\"\r\n                              [style.width.px]=\"'50'\" [style.height.px]=\"'50'\"\r\n                              [style.transform]=\"'scale(' + item.scale + ')'\" loop></lottie-player>\r\n                          </span>\r\n                          <!-- <img [src]=\"item.icon\" class=\"icon\"> -->\r\n                        </div>\r\n                        {{item.title}}\r\n                      </li>\r\n                    </ng-container>\r\n                  </ul>\r\n                </div>\r\n              </div>\r\n\r\n            </div>\r\n          </div> <!-- end container -->\r\n        </div>\r\n        <div class=\"wpo-lower-footer\">\r\n          <div class=\"container\">\r\n            <div class=\"row\">\r\n              <div class=\"col-12 text-center\">\r\n                <p class=\"about-widget text-center\">Let’s get social!</p>\r\n\r\n                <ul class=\"flex justify-content-center align-items-center gap-1 p-0\">\r\n                  <li>\r\n                    <a [href]=\"this.socialLinks.facebook\" target=\"_blank\">\r\n                      <img src=\"/assets/icons/navbar/facebook.svg\" alt=\"facebook Image\">\r\n                    </a>\r\n                  </li>\r\n                  <li>\r\n                    <a [href]=\"this.socialLinks.instagram\" target=\"_blank\">\r\n                      <img src=\"/assets/icons/navbar/instagram.svg\" alt=\"instagram Image\">\r\n                    </a>\r\n                  </li>\r\n                  <li>\r\n                    <a [href]=\"this.socialLinks.linkedin\" target=\"_blank\">\r\n                      <img src=\"/assets/icons/navbar/linkedin.svg\" alt=\"linkedin Image\">\r\n                    </a>\r\n                  </li>\r\n                  <li>\r\n                    <a [href]=\"this.socialLinks.twitter\" target=\"_blank\">\r\n                      <img src=\"/assets/icons/navbar/twitter.svg\" alt=\"twitter Image\">\r\n                    </a>\r\n                  </li>\r\n                  <li>\r\n                    <a [href]=\"this.socialLinks.youtube\" target=\"_blank\">\r\n                      <img src=\"/assets/icons/navbar/youtube.svg\" alt=\"youtube Image\">\r\n                    </a>\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"overlay\" [ngClass]=\"burgerClasses.overlay\"></div>\r\n\r\n\r\n  <div class=\"burger\" [ngClass]=\"burgerClasses.burger\" (click)=\"burgerClick()\">\r\n    <span></span>\r\n  </div>\r\n</div>"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,SAA4DC,YAAY,EAAEC,MAAM,QAAkD,eAAe;AAMjJ,SAASC,WAAW,QAAQ,oCAAoC;AAIhE,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;;;;;ICL/BC,EAAA,CAAAC,uBAAA,GAA6C;IAC3CD,EAAA,CAAAE,SAAA,cAGM;;;;;;IAYAF,EAJJ,CAAAG,cAAA,YAA8E,WAEF,cAG5B;IAC1CH,EAAA,CAAAE,SAAA,YAA2E;IAGjFF,EAFI,CAAAI,YAAA,EAAM,EACJ,EACD;;;IARaJ,EAAA,CAAAK,SAAA,EAAwB;IAAxBL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAAO,eAAA,IAAAC,GAAA,EAAwB;IAKuBR,EAAA,CAAAK,SAAA,GAAW;IAAXL,EAAA,CAAAM,UAAA,YAAW;;;;;;IAmD5DN,EAAA,CAAAG,cAAA,aAGyC;IADMH,EAAA,CAAAS,UAAA,mBAAAC,sEAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAC,OAAA,GAAAb,EAAA,CAAAc,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAASD,MAAA,CAAAE,UAAA,CAAAL,OAAA,CAAAM,GAAA,CAAoB;IAAA,EAAC;IAGzEnB,EADF,CAAAG,cAAA,cAA4B,eACG;IAC3BH,EAAA,CAAAE,SAAA,wBAEuE;IAG3EF,EAFE,CAAAI,YAAA,EAAO,EAEH;IAENJ,EAAA,CAAAoB,MAAA,GACF;IAAApB,EAAA,CAAAI,YAAA,EAAK;;;;IAXHJ,EADA,CAAAM,UAAA,YAAAN,EAAA,CAAAqB,eAAA,KAAAC,GAAA,EAAAT,OAAA,CAAAU,QAAA,EAA4C,aAAAV,OAAA,CAAAU,QAAA,aACN;IAIhCvB,EAAA,CAAAK,SAAA,GAAuB;IACvBL,EADA,CAAAwB,WAAA,qBAAuB,sBAAyB,yBAAAX,OAAA,CAAAY,KAAA,OACD;IAFlCzB,EAAA,CAAAM,UAAA,QAAAO,OAAA,CAAAa,UAAA,CAAuB;IAO1C1B,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAA2B,kBAAA,MAAAd,OAAA,CAAAe,KAAA,MACF;;;;;IAfF5B,EAAA,CAAAC,uBAAA,GAA6D;IAC3DD,EAAA,CAAA6B,UAAA,IAAAC,iDAAA,kBAGyC;;;;;;IAHpC9B,EAAA,CAAAK,SAAA,EAA8B;IAA9BL,EAAA,CAAAM,UAAA,SAAAO,OAAA,CAAAkB,IAAA,CAAAC,QAAA,CAAAhB,MAAA,CAAAe,IAAA,EAA8B;;;;;;IAgBrC/B,EAAA,CAAAC,uBAAA,GAAwE;IACtED,EAAA,CAAAG,cAAA,aAC2D;IAAzDH,EAAA,CAAAS,UAAA,mBAAAwB,iEAAA;MAAA,MAAAC,OAAA,GAAAlC,EAAA,CAAAW,aAAA,CAAAwB,GAAA,EAAApB,SAAA;MAAA,MAAAC,MAAA,GAAAhB,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAiB,WAAA,CAAAiB,OAAA,CAAAf,GAAA,GAAoBH,MAAA,CAAAE,UAAA,CAAAgB,OAAA,CAAAf,GAAA,CAAoB,GAAEe,OAAA,CAAAE,MAAA,EAAa;IAAA,EAAC;IAGtDpC,EADF,CAAAG,cAAA,cAA4B,eACG;IAC3BH,EAAA,CAAAE,SAAA,wBAEuE;IAG3EF,EAFE,CAAAI,YAAA,EAAO,EAEH;IACNJ,EAAA,CAAAoB,MAAA,GACF;IAAApB,EAAA,CAAAI,YAAA,EAAK;;;;;IANGJ,EAAA,CAAAK,SAAA,GAAuB;IACvBL,EADA,CAAAwB,WAAA,qBAAuB,sBAAyB,yBAAAU,OAAA,CAAAT,KAAA,OACD;IAFlCzB,EAAA,CAAAM,UAAA,QAAA4B,OAAA,CAAAR,UAAA,CAAuB;IAM1C1B,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAA2B,kBAAA,MAAAO,OAAA,CAAAN,KAAA,MACF;;;ADtFtB,WAAaS,mBAAmB;EAA1B,MAAOA,mBAAmB;IAqB9BC,YACUC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB,EACxBC,cAAiC,EACjCC,aAA4B,EAC5BC,mBAAwC,EACxCC,QAAmB,EACDC,QAAkB;MAPpC,KAAAP,MAAM,GAANA,MAAM;MACN,KAAAC,cAAc,GAAdA,cAAc;MACd,KAAAC,WAAW,GAAXA,WAAW;MACX,KAAAC,cAAc,GAAdA,cAAc;MACd,KAAAC,aAAa,GAAbA,aAAa;MACb,KAAAC,mBAAmB,GAAnBA,mBAAmB;MACnB,KAAAC,QAAQ,GAARA,QAAQ;MACU,KAAAC,QAAQ,GAARA,QAAQ;MA5B3B,KAAAC,IAAI,GAAY,KAAK;MAEpB,KAAAC,MAAM,GAAG,IAAIpD,YAAY,EAAO;MAC1C,KAAAqD,WAAW,GAAGpD,MAAM,CAACC,WAAW,CAAC;MAC1B,KAAAiC,IAAI,GAAa,EAAc;MACtC,KAAAmB,MAAM,GAAG,KAAK;MACd,KAAAC,IAAI,GAAG,IAAIpD,OAAO,EAAE;MACpB,KAAAqD,eAAe,GAAW,EAAE;MAC5B,KAAAC,aAAa,GAAG;QAAEC,MAAM,EAAE,EAAE;QAAEC,IAAI,EAAE,EAAE;QAAEC,OAAO,EAAE,EAAE;QAAEC,IAAI,EAAE,EAAE;QAAEC,IAAI,EAAE;MAAQ,CAAE;MAC/E,KAAAC,oBAAoB,GAAG,EAAoB;MAC3C,KAAAC,mBAAmB,GAAmB,EAAE;MACxC,KAAAC,WAAW,GACT;QACEC,QAAQ,EAAE,6CAA6C;QACvDC,OAAO,EAAE,iCAAiC;QAC1CC,QAAQ,EAAE,sCAAsC;QAChDC,OAAO,EAAE,uCAAuC;QAChDC,SAAS,EAAE;OACZ;IAWC;IAEJC,QAAQA,CAAA;MACN;MACA,IAAI,CAACpC,IAAI,GAAG,IAAI,CAACU,WAAW,CAAC2B,WAAW,EAAE;MAC1C,IAAI,CAACC,SAAS,GAAG,IAAI,CAAC1B,aAAa,CAAC2B,YAAY,EAAE;MAClD,IAAI,CAACX,oBAAoB,GAAG,IAAI,CAAChB,aAAa,CAAC4B,uBAAuB,EAAE;MAExE,IAAI,CAACpB,IAAI,CAACqB,GAAG,CAAC,IAAI,CAAC5B,mBAAmB,CAAC6B,GAAG,EAAE,CAACC,SAAS,CAAEC,GAAQ,IAAI;QAClE;QACA,IAAI,CAACf,mBAAmB,GAAGe,GAAG,CAACC,MAAM,CAClCC,YAA0B,IAAK,CAACA,YAAY,CAACC,MAAM,CACrD;MACH,CAAC,CAAC,CAAC;IACL;IAEAC,WAAWA,CAAA;MACT,IAAI,CAAC5B,IAAI,CAAC6B,WAAW,EAAE;IACzB;IAEAC,qBAAqBA,CAAA;MACnB,IAAI,CAAC9B,IAAI,CAAC+B,IAAI,GAAG,IAAI,CAAC1C,cAAc,CAACY,eAAe,CAACsB,SAAS,CAAEC,GAAW,IAAI;QAC7E,IAAI,CAACvB,eAAe,GAAGuB,GAAG;QAC1B,IAAI,CAACjC,cAAc,CAACyC,aAAa,EAAE;MACrC,CAAC,CAAC;IACJ;IACAC,eAAeA,CAAA;MACb,IAAI,CAAClC,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;MAC1B,IAAI,CAACF,MAAM,CAACqC,IAAI,EAAE;IACpB;IACAC,qBAAqBA,CAAA;MACnB,IAAIC,eAAe,GAAGzC,QAAQ,CAAC0C,cAAc,CAAC,mBAAmB,CAAC;MAClE,IAAID,eAAgB,CAACE,YAAY,EAAE;QACjCF,eAAgB,CAACG,KAAK,CAACC,MAAM,GAAG,GAAG;MACrC,CAAC,MAAM;QACLJ,eAAgB,CAACG,KAAK,CAACC,MAAM,GAAGC,MAAO,CAACC,WAAW,GAAG,IAAI;MAC5D;IACF;IAEAC,MAAMA,CAAA;MACJ,IAAI,CAACrD,WAAW,CAACqD,MAAM,EAAE;IAC3B;IAEA5E,UAAUA,CAAC6E,KAAa;MACtB,IAAI,CAACX,eAAe,EAAE;MACtB,IAAI,CAACY,WAAW,EAAE;MAClB,IAAI,CAACzD,MAAM,CAAC0D,aAAa,CAACF,KAAK,EAAE;QAAEG,UAAU,EAAE;MAAI,CAAE,CAAC;MACtD;IACF;IAEAF,WAAWA,CAAA;MACT,IAAI,CAAC3C,aAAa,CAACC,MAAM,GAAG,IAAI,CAACD,aAAa,CAACC,MAAM,KAAK,SAAS,GAAG,EAAE,GAAG,SAAS;MACpF,IAAI,CAACD,aAAa,CAACG,OAAO,GAAG,IAAI,CAACH,aAAa,CAACG,OAAO,KAAK,MAAM,GAAG,EAAE,GAAG,MAAM;MAChF,IAAI,CAACH,aAAa,CAACE,IAAI,GAAG,IAAI,CAACF,aAAa,CAACE,IAAI,KAAK,MAAM,GAAG,EAAE,GAAG,MAAM;MAC1E,IAAI,CAACF,aAAa,CAACK,IAAI,GAAG,IAAI,CAACL,aAAa,CAACK,IAAI,KAAK,QAAQ,GAAG,EAAE,GAAG,QAAQ;MAC9E,IAAI,CAACL,aAAa,CAACI,IAAI,GAAG,UAAU;MAEpC,IAAI,IAAI,CAACJ,aAAa,CAACC,MAAM,KAAK,SAAS,EAAE;QAE3C,IAAI,CAACT,QAAQ,CAACsD,QAAQ,CAAC,IAAI,CAACrD,QAAQ,CAACW,IAAI,EAAE,iBAAiB,CAAC;MAC/D,CAAC,MAAM;QAEL,IAAI,CAACZ,QAAQ,CAACuD,WAAW,CAAC,IAAI,CAACtD,QAAQ,CAACW,IAAI,EAAE,iBAAiB,CAAC;MAClE;IACF;IAAC,QAAA4C,CAAA,G;uBA9FUhE,mBAAmB,EAAArC,EAAA,CAAAsG,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAxG,EAAA,CAAAsG,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA1G,EAAA,CAAAsG,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA5G,EAAA,CAAAsG,iBAAA,CAAAtG,EAAA,CAAA6G,iBAAA,GAAA7G,EAAA,CAAAsG,iBAAA,CAAAQ,EAAA,CAAAC,aAAA,GAAA/G,EAAA,CAAAsG,iBAAA,CAAAU,EAAA,CAAAC,mBAAA,GAAAjH,EAAA,CAAAsG,iBAAA,CAAAtG,EAAA,CAAAkH,SAAA,GAAAlH,EAAA,CAAAsG,iBAAA,CA6BpB3G,QAAQ;IAAA;IAAA,QAAAwH,EAAA,G;YA7BP9E,mBAAmB;MAAA+E,SAAA;MAAAC,MAAA;QAAAtE,IAAA;QAAAsB,SAAA;MAAA;MAAAiD,OAAA;QAAAtE,MAAA;MAAA;MAAAuE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UChB5B5H,EAFJ,CAAAG,cAAA,aAA8B,aACT,WACqC;UAACH,EAAA,CAAAE,SAAA,aACb;UAE5CF,EADE,CAAAI,YAAA,EAAI,EACA;UACNJ,EAAA,CAAA6B,UAAA,IAAAiG,2CAAA,0BAA6C;UAQ3C9H,EADF,CAAAG,cAAA,aAA0B,eAEJ;UAElBH,EAAA,CAAA6B,UAAA,IAAAkG,0CAAA,gBAAmC;UAgB/B/H,EAJJ,CAAAG,cAAA,YAA8E,YAEF,eAG5B;UAC1CH,EAAA,CAAAE,SAAA,aAC0F;UAKlGF,EAJM,CAAAI,YAAA,EAAM,EACJ,EACD,EAEF;UAgCSJ,EA9Bd,CAAAG,cAAA,eAAiD,eACa,eAC5B,eACL,eACsC,eAqBT,eAC6C,cAIxE;UAkBfH,EAjBA,CAAA6B,UAAA,KAAAmG,4CAAA,2BAA6D,KAAAC,4CAAA,2BAiBW;UAqBpFjI,EANU,CAAAI,YAAA,EAAK,EACD,EACF,EAEF,EACF,EACF;UAKEJ,EAJR,CAAAG,cAAA,eAA8B,eACL,eACJ,eACiB,aACM;UAAAH,EAAA,CAAAoB,MAAA,8BAAiB;UAAApB,EAAA,CAAAI,YAAA,EAAI;UAIrDJ,EAFJ,CAAAG,cAAA,cAAqE,UAC/D,aACoD;UACpDH,EAAA,CAAAE,SAAA,eAAkE;UAEtEF,EADE,CAAAI,YAAA,EAAI,EACD;UAEHJ,EADF,CAAAG,cAAA,UAAI,aACqD;UACrDH,EAAA,CAAAE,SAAA,eAAoE;UAExEF,EADE,CAAAI,YAAA,EAAI,EACD;UAEHJ,EADF,CAAAG,cAAA,UAAI,aACoD;UACpDH,EAAA,CAAAE,SAAA,eAAkE;UAEtEF,EADE,CAAAI,YAAA,EAAI,EACD;UAEHJ,EADF,CAAAG,cAAA,UAAI,aACmD;UACnDH,EAAA,CAAAE,SAAA,eAAgE;UAEpEF,EADE,CAAAI,YAAA,EAAI,EACD;UAEHJ,EADF,CAAAG,cAAA,UAAI,aACmD;UACnDH,EAAA,CAAAE,SAAA,eAAgE;UAUpFF,EATkB,CAAAI,YAAA,EAAI,EACD,EACF,EACD,EACF,EACF,EACF,EACF,EACF,EACF;UAENJ,EAAA,CAAAE,SAAA,eAA6D;UAG7DF,EAAA,CAAAG,cAAA,eAA6E;UAAxBH,EAAA,CAAAS,UAAA,mBAAAyH,mDAAA;YAAAlI,EAAA,CAAAW,aAAA,CAAAwH,GAAA;YAAA,OAAAnI,EAAA,CAAAiB,WAAA,CAAS4G,GAAA,CAAA7B,WAAA,EAAa;UAAA,EAAC;UAC1EhG,EAAA,CAAAE,SAAA,YAAa;UAEjBF,EADE,CAAAI,YAAA,EAAM,EACF;;;UA1JWJ,EAAA,CAAAK,SAAA,GAA4B;UAA5BL,EAAA,CAAAM,UAAA,SAAAuH,GAAA,CAAAzE,eAAA,QAA4B;UAWvCpD,EAAA,CAAAK,SAAA,GAWC;UAXDL,EAAA,CAAAoI,aAAA,IAAAP,GAAA,CAAA5E,WAAA,CAAAoF,aAAA,YAWC;UAEiBrI,EAAA,CAAAK,SAAA,GAAiC;UAAjCL,EAAA,CAAAM,UAAA,eAAAN,EAAA,CAAAO,eAAA,KAAA+H,GAAA,EAAiC;UAM3CtI,EAAA,CAAAK,SAAA,GAAmF;UAAnFL,EAAA,CAAAM,UAAA,UAAAuH,GAAA,CAAAjE,mBAAA,CAAA2E,MAAA,OAAAV,GAAA,CAAAjE,mBAAA,CAAA2E,MAAA,KAAmF;UAO3EvI,EAAA,CAAAK,SAAA,EAA8B;UAA9BL,EAAA,CAAAM,UAAA,YAAAuH,GAAA,CAAAxE,aAAA,CAAAE,IAAA,CAA8B;UACjBvD,EAAA,CAAAK,SAAA,EAA8B;UAA9BL,EAAA,CAAAM,UAAA,YAAAuH,GAAA,CAAAxE,aAAA,CAAAK,IAAA,CAA8B;UA8Bd1D,EAAA,CAAAK,SAAA,GAAc;UAAdL,EAAA,CAAAM,UAAA,YAAAuH,GAAA,CAAAxD,SAAA,CAAc;UAiBdrE,EAAA,CAAAK,SAAA,EAAyB;UAAzBL,EAAA,CAAAM,UAAA,YAAAuH,GAAA,CAAAlE,oBAAA,CAAyB;UA8BrD3D,EAAA,CAAAK,SAAA,GAAkC;UAAlCL,EAAA,CAAAM,UAAA,SAAAuH,GAAA,CAAAhE,WAAA,CAAAG,QAAA,EAAAhE,EAAA,CAAAwI,aAAA,CAAkC;UAKlCxI,EAAA,CAAAK,SAAA,GAAmC;UAAnCL,EAAA,CAAAM,UAAA,SAAAuH,GAAA,CAAAhE,WAAA,CAAAK,SAAA,EAAAlE,EAAA,CAAAwI,aAAA,CAAmC;UAKnCxI,EAAA,CAAAK,SAAA,GAAkC;UAAlCL,EAAA,CAAAM,UAAA,SAAAuH,GAAA,CAAAhE,WAAA,CAAAC,QAAA,EAAA9D,EAAA,CAAAwI,aAAA,CAAkC;UAKlCxI,EAAA,CAAAK,SAAA,GAAiC;UAAjCL,EAAA,CAAAM,UAAA,SAAAuH,GAAA,CAAAhE,WAAA,CAAAE,OAAA,EAAA/D,EAAA,CAAAwI,aAAA,CAAiC;UAKjCxI,EAAA,CAAAK,SAAA,GAAiC;UAAjCL,EAAA,CAAAM,UAAA,SAAAuH,GAAA,CAAAhE,WAAA,CAAAI,OAAA,EAAAjE,EAAA,CAAAwI,aAAA,CAAiC;UAajCxI,EAAA,CAAAK,SAAA,GAAiC;UAAjCL,EAAA,CAAAM,UAAA,YAAAuH,GAAA,CAAAxE,aAAA,CAAAG,OAAA,CAAiC;UAGlCxD,EAAA,CAAAK,SAAA,EAAgC;UAAhCL,EAAA,CAAAM,UAAA,YAAAuH,GAAA,CAAAxE,aAAA,CAAAC,MAAA,CAAgC;;;;;;;SD3IzCjB,mBAAmB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}