{"ast": null, "code": "/**\n * @dynamic is for runtime initializing DomHandler.browser\n *\n * If delete below comment, we can see this error message:\n *  Metadata collected contains an error that will be reported at runtime:\n *  Only initialized variables and constants can be referenced\n *  because the value of this variable is needed by the template compiler.\n */\n// @dynamic\nlet DomHandler = /*#__PURE__*/(() => {\n  class DomHandler {\n    static zindex = 1000;\n    static calculatedScrollbarWidth = null;\n    static calculatedScrollbarHeight = null;\n    static browser;\n    static addClass(element, className) {\n      if (element && className) {\n        if (element.classList) element.classList.add(className);else element.className += ' ' + className;\n      }\n    }\n    static addMultipleClasses(element, className) {\n      if (element && className) {\n        if (element.classList) {\n          let styles = className.trim().split(' ');\n          for (let i = 0; i < styles.length; i++) {\n            element.classList.add(styles[i]);\n          }\n        } else {\n          let styles = className.split(' ');\n          for (let i = 0; i < styles.length; i++) {\n            element.className += ' ' + styles[i];\n          }\n        }\n      }\n    }\n    static removeClass(element, className) {\n      if (element && className) {\n        if (element.classList) element.classList.remove(className);else element.className = element.className.replace(new RegExp('(^|\\\\b)' + className.split(' ').join('|') + '(\\\\b|$)', 'gi'), ' ');\n      }\n    }\n    static removeMultipleClasses(element, classNames) {\n      if (element && classNames) {\n        [classNames].flat().filter(Boolean).forEach(cNames => cNames.split(' ').forEach(className => this.removeClass(element, className)));\n      }\n    }\n    static hasClass(element, className) {\n      if (element && className) {\n        if (element.classList) return element.classList.contains(className);else return new RegExp('(^| )' + className + '( |$)', 'gi').test(element.className);\n      }\n      return false;\n    }\n    static siblings(element) {\n      return Array.prototype.filter.call(element.parentNode.children, function (child) {\n        return child !== element;\n      });\n    }\n    static find(element, selector) {\n      return Array.from(element.querySelectorAll(selector));\n    }\n    static findSingle(element, selector) {\n      return this.isElement(element) ? element.querySelector(selector) : null;\n    }\n    static index(element) {\n      let children = element.parentNode.childNodes;\n      let num = 0;\n      for (var i = 0; i < children.length; i++) {\n        if (children[i] == element) return num;\n        if (children[i].nodeType == 1) num++;\n      }\n      return -1;\n    }\n    static indexWithinGroup(element, attributeName) {\n      let children = element.parentNode ? element.parentNode.childNodes : [];\n      let num = 0;\n      for (var i = 0; i < children.length; i++) {\n        if (children[i] == element) return num;\n        if (children[i].attributes && children[i].attributes[attributeName] && children[i].nodeType == 1) num++;\n      }\n      return -1;\n    }\n    static appendOverlay(overlay, target, appendTo = 'self') {\n      if (appendTo !== 'self' && overlay && target) {\n        this.appendChild(overlay, target);\n      }\n    }\n    static alignOverlay(overlay, target, appendTo = 'self', calculateMinWidth = true) {\n      if (overlay && target) {\n        if (calculateMinWidth) {\n          overlay.style.minWidth = `${DomHandler.getOuterWidth(target)}px`;\n        }\n        if (appendTo === 'self') {\n          this.relativePosition(overlay, target);\n        } else {\n          this.absolutePosition(overlay, target);\n        }\n      }\n    }\n    static relativePosition(element, target, gutter = true) {\n      const getClosestRelativeElement = el => {\n        if (!el) return;\n        return getComputedStyle(el).getPropertyValue('position') === 'relative' ? el : getClosestRelativeElement(el.parentElement);\n      };\n      const elementDimensions = element.offsetParent ? {\n        width: element.offsetWidth,\n        height: element.offsetHeight\n      } : this.getHiddenElementDimensions(element);\n      const targetHeight = target.offsetHeight ?? target.getBoundingClientRect().height;\n      const targetOffset = target.getBoundingClientRect();\n      const windowScrollTop = this.getWindowScrollTop();\n      const windowScrollLeft = this.getWindowScrollLeft();\n      const viewport = this.getViewport();\n      const relativeElement = getClosestRelativeElement(element);\n      const relativeElementOffset = relativeElement?.getBoundingClientRect() || {\n        top: -1 * windowScrollTop,\n        left: -1 * windowScrollLeft\n      };\n      let top, left;\n      if (targetOffset.top + targetHeight + elementDimensions.height > viewport.height) {\n        top = targetOffset.top - relativeElementOffset.top - elementDimensions.height;\n        element.style.transformOrigin = 'bottom';\n        if (targetOffset.top + top < 0) {\n          top = -1 * targetOffset.top;\n        }\n      } else {\n        top = targetHeight + targetOffset.top - relativeElementOffset.top;\n        element.style.transformOrigin = 'top';\n      }\n      const horizontalOverflow = targetOffset.left + elementDimensions.width - viewport.width;\n      const targetLeftOffsetInSpaceOfRelativeElement = targetOffset.left - relativeElementOffset.left;\n      if (elementDimensions.width > viewport.width) {\n        // element wider then viewport and cannot fit on screen (align at left side of viewport)\n        left = (targetOffset.left - relativeElementOffset.left) * -1;\n      } else if (horizontalOverflow > 0) {\n        // element wider then viewport but can be fit on screen (align at right side of viewport)\n        left = targetLeftOffsetInSpaceOfRelativeElement - horizontalOverflow;\n      } else {\n        // element fits on screen (align with target)\n        left = targetOffset.left - relativeElementOffset.left;\n      }\n      element.style.top = top + 'px';\n      element.style.left = left + 'px';\n      gutter && (element.style.marginTop = origin === 'bottom' ? 'calc(var(--p-anchor-gutter) * -1)' : 'calc(var(--p-anchor-gutter))');\n    }\n    static absolutePosition(element, target, gutter = true) {\n      const elementDimensions = element.offsetParent ? {\n        width: element.offsetWidth,\n        height: element.offsetHeight\n      } : this.getHiddenElementDimensions(element);\n      const elementOuterHeight = elementDimensions.height;\n      const elementOuterWidth = elementDimensions.width;\n      const targetOuterHeight = target.offsetHeight ?? target.getBoundingClientRect().height;\n      const targetOuterWidth = target.offsetWidth ?? target.getBoundingClientRect().width;\n      const targetOffset = target.getBoundingClientRect();\n      const windowScrollTop = this.getWindowScrollTop();\n      const windowScrollLeft = this.getWindowScrollLeft();\n      const viewport = this.getViewport();\n      let top, left;\n      if (targetOffset.top + targetOuterHeight + elementOuterHeight > viewport.height) {\n        top = targetOffset.top + windowScrollTop - elementOuterHeight;\n        element.style.transformOrigin = 'bottom';\n        if (top < 0) {\n          top = windowScrollTop;\n        }\n      } else {\n        top = targetOuterHeight + targetOffset.top + windowScrollTop;\n        element.style.transformOrigin = 'top';\n      }\n      if (targetOffset.left + elementOuterWidth > viewport.width) left = Math.max(0, targetOffset.left + windowScrollLeft + targetOuterWidth - elementOuterWidth);else left = targetOffset.left + windowScrollLeft;\n      element.style.top = top + 'px';\n      element.style.left = left + 'px';\n      gutter && (element.style.marginTop = origin === 'bottom' ? 'calc(var(--p-anchor-gutter) * -1)' : 'calc(var(--p-anchor-gutter))');\n    }\n    static getParents(element, parents = []) {\n      return element['parentNode'] === null ? parents : this.getParents(element.parentNode, parents.concat([element.parentNode]));\n    }\n    static getScrollableParents(element) {\n      let scrollableParents = [];\n      if (element) {\n        let parents = this.getParents(element);\n        const overflowRegex = /(auto|scroll)/;\n        const overflowCheck = node => {\n          let styleDeclaration = window['getComputedStyle'](node, null);\n          return overflowRegex.test(styleDeclaration.getPropertyValue('overflow')) || overflowRegex.test(styleDeclaration.getPropertyValue('overflowX')) || overflowRegex.test(styleDeclaration.getPropertyValue('overflowY'));\n        };\n        for (let parent of parents) {\n          let scrollSelectors = parent.nodeType === 1 && parent.dataset['scrollselectors'];\n          if (scrollSelectors) {\n            let selectors = scrollSelectors.split(',');\n            for (let selector of selectors) {\n              let el = this.findSingle(parent, selector);\n              if (el && overflowCheck(el)) {\n                scrollableParents.push(el);\n              }\n            }\n          }\n          if (parent.nodeType !== 9 && overflowCheck(parent)) {\n            scrollableParents.push(parent);\n          }\n        }\n      }\n      return scrollableParents;\n    }\n    static getHiddenElementOuterHeight(element) {\n      element.style.visibility = 'hidden';\n      element.style.display = 'block';\n      let elementHeight = element.offsetHeight;\n      element.style.display = 'none';\n      element.style.visibility = 'visible';\n      return elementHeight;\n    }\n    static getHiddenElementOuterWidth(element) {\n      element.style.visibility = 'hidden';\n      element.style.display = 'block';\n      let elementWidth = element.offsetWidth;\n      element.style.display = 'none';\n      element.style.visibility = 'visible';\n      return elementWidth;\n    }\n    static getHiddenElementDimensions(element) {\n      let dimensions = {};\n      element.style.visibility = 'hidden';\n      element.style.display = 'block';\n      dimensions.width = element.offsetWidth;\n      dimensions.height = element.offsetHeight;\n      element.style.display = 'none';\n      element.style.visibility = 'visible';\n      return dimensions;\n    }\n    static scrollInView(container, item) {\n      let borderTopValue = getComputedStyle(container).getPropertyValue('borderTopWidth');\n      let borderTop = borderTopValue ? parseFloat(borderTopValue) : 0;\n      let paddingTopValue = getComputedStyle(container).getPropertyValue('paddingTop');\n      let paddingTop = paddingTopValue ? parseFloat(paddingTopValue) : 0;\n      let containerRect = container.getBoundingClientRect();\n      let itemRect = item.getBoundingClientRect();\n      let offset = itemRect.top + document.body.scrollTop - (containerRect.top + document.body.scrollTop) - borderTop - paddingTop;\n      let scroll = container.scrollTop;\n      let elementHeight = container.clientHeight;\n      let itemHeight = this.getOuterHeight(item);\n      if (offset < 0) {\n        container.scrollTop = scroll + offset;\n      } else if (offset + itemHeight > elementHeight) {\n        container.scrollTop = scroll + offset - elementHeight + itemHeight;\n      }\n    }\n    static fadeIn(element, duration) {\n      element.style.opacity = 0;\n      let last = +new Date();\n      let opacity = 0;\n      let tick = function () {\n        opacity = +element.style.opacity.replace(',', '.') + (new Date().getTime() - last) / duration;\n        element.style.opacity = opacity;\n        last = +new Date();\n        if (+opacity < 1) {\n          window.requestAnimationFrame && requestAnimationFrame(tick) || setTimeout(tick, 16);\n        }\n      };\n      tick();\n    }\n    static fadeOut(element, ms) {\n      var opacity = 1,\n        interval = 50,\n        duration = ms,\n        gap = interval / duration;\n      let fading = setInterval(() => {\n        opacity = opacity - gap;\n        if (opacity <= 0) {\n          opacity = 0;\n          clearInterval(fading);\n        }\n        element.style.opacity = opacity;\n      }, interval);\n    }\n    static getWindowScrollTop() {\n      let doc = document.documentElement;\n      return (window.pageYOffset || doc.scrollTop) - (doc.clientTop || 0);\n    }\n    static getWindowScrollLeft() {\n      let doc = document.documentElement;\n      return (window.pageXOffset || doc.scrollLeft) - (doc.clientLeft || 0);\n    }\n    static matches(element, selector) {\n      var p = Element.prototype;\n      var f = p['matches'] || p.webkitMatchesSelector || p['mozMatchesSelector'] || p['msMatchesSelector'] || function (s) {\n        return [].indexOf.call(document.querySelectorAll(s), this) !== -1;\n      };\n      return f.call(element, selector);\n    }\n    static getOuterWidth(el, margin) {\n      let width = el.offsetWidth;\n      if (margin) {\n        let style = getComputedStyle(el);\n        width += parseFloat(style.marginLeft) + parseFloat(style.marginRight);\n      }\n      return width;\n    }\n    static getHorizontalPadding(el) {\n      let style = getComputedStyle(el);\n      return parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n    }\n    static getHorizontalMargin(el) {\n      let style = getComputedStyle(el);\n      return parseFloat(style.marginLeft) + parseFloat(style.marginRight);\n    }\n    static innerWidth(el) {\n      let width = el.offsetWidth;\n      let style = getComputedStyle(el);\n      width += parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n      return width;\n    }\n    static width(el) {\n      let width = el.offsetWidth;\n      let style = getComputedStyle(el);\n      width -= parseFloat(style.paddingLeft) + parseFloat(style.paddingRight);\n      return width;\n    }\n    static getInnerHeight(el) {\n      let height = el.offsetHeight;\n      let style = getComputedStyle(el);\n      height += parseFloat(style.paddingTop) + parseFloat(style.paddingBottom);\n      return height;\n    }\n    static getOuterHeight(el, margin) {\n      let height = el.offsetHeight;\n      if (margin) {\n        let style = getComputedStyle(el);\n        height += parseFloat(style.marginTop) + parseFloat(style.marginBottom);\n      }\n      return height;\n    }\n    static getHeight(el) {\n      let height = el.offsetHeight;\n      let style = getComputedStyle(el);\n      height -= parseFloat(style.paddingTop) + parseFloat(style.paddingBottom) + parseFloat(style.borderTopWidth) + parseFloat(style.borderBottomWidth);\n      return height;\n    }\n    static getWidth(el) {\n      let width = el.offsetWidth;\n      let style = getComputedStyle(el);\n      width -= parseFloat(style.paddingLeft) + parseFloat(style.paddingRight) + parseFloat(style.borderLeftWidth) + parseFloat(style.borderRightWidth);\n      return width;\n    }\n    static getViewport() {\n      let win = window,\n        d = document,\n        e = d.documentElement,\n        g = d.getElementsByTagName('body')[0],\n        w = win.innerWidth || e.clientWidth || g.clientWidth,\n        h = win.innerHeight || e.clientHeight || g.clientHeight;\n      return {\n        width: w,\n        height: h\n      };\n    }\n    static getOffset(el) {\n      var rect = el.getBoundingClientRect();\n      return {\n        top: rect.top + (window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0),\n        left: rect.left + (window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0)\n      };\n    }\n    static replaceElementWith(element, replacementElement) {\n      let parentNode = element.parentNode;\n      if (!parentNode) throw `Can't replace element`;\n      return parentNode.replaceChild(replacementElement, element);\n    }\n    static getUserAgent() {\n      if (navigator && this.isClient()) {\n        return navigator.userAgent;\n      }\n    }\n    static isIE() {\n      var ua = window.navigator.userAgent;\n      var msie = ua.indexOf('MSIE ');\n      if (msie > 0) {\n        // IE 10 or older => return version number\n        return true;\n      }\n      var trident = ua.indexOf('Trident/');\n      if (trident > 0) {\n        // IE 11 => return version number\n        var rv = ua.indexOf('rv:');\n        return true;\n      }\n      var edge = ua.indexOf('Edge/');\n      if (edge > 0) {\n        // Edge (IE 12+) => return version number\n        return true;\n      }\n      // other browser\n      return false;\n    }\n    static isIOS() {\n      return /iPad|iPhone|iPod/.test(navigator.userAgent) && !window['MSStream'];\n    }\n    static isAndroid() {\n      return /(android)/i.test(navigator.userAgent);\n    }\n    static isTouchDevice() {\n      return 'ontouchstart' in window || navigator.maxTouchPoints > 0;\n    }\n    static appendChild(element, target) {\n      if (this.isElement(target)) target.appendChild(element);else if (target && target.el && target.el.nativeElement) target.el.nativeElement.appendChild(element);else throw 'Cannot append ' + target + ' to ' + element;\n    }\n    static removeChild(element, target) {\n      if (this.isElement(target)) target.removeChild(element);else if (target.el && target.el.nativeElement) target.el.nativeElement.removeChild(element);else throw 'Cannot remove ' + element + ' from ' + target;\n    }\n    static removeElement(element) {\n      if (!('remove' in Element.prototype)) element.parentNode.removeChild(element);else element.remove();\n    }\n    static isElement(obj) {\n      return typeof HTMLElement === 'object' ? obj instanceof HTMLElement : obj && typeof obj === 'object' && obj !== null && obj.nodeType === 1 && typeof obj.nodeName === 'string';\n    }\n    static calculateScrollbarWidth(el) {\n      if (el) {\n        let style = getComputedStyle(el);\n        return el.offsetWidth - el.clientWidth - parseFloat(style.borderLeftWidth) - parseFloat(style.borderRightWidth);\n      } else {\n        if (this.calculatedScrollbarWidth !== null) return this.calculatedScrollbarWidth;\n        let scrollDiv = document.createElement('div');\n        scrollDiv.className = 'p-scrollbar-measure';\n        document.body.appendChild(scrollDiv);\n        let scrollbarWidth = scrollDiv.offsetWidth - scrollDiv.clientWidth;\n        document.body.removeChild(scrollDiv);\n        this.calculatedScrollbarWidth = scrollbarWidth;\n        return scrollbarWidth;\n      }\n    }\n    static calculateScrollbarHeight() {\n      if (this.calculatedScrollbarHeight !== null) return this.calculatedScrollbarHeight;\n      let scrollDiv = document.createElement('div');\n      scrollDiv.className = 'p-scrollbar-measure';\n      document.body.appendChild(scrollDiv);\n      let scrollbarHeight = scrollDiv.offsetHeight - scrollDiv.clientHeight;\n      document.body.removeChild(scrollDiv);\n      this.calculatedScrollbarWidth = scrollbarHeight;\n      return scrollbarHeight;\n    }\n    static invokeElementMethod(element, methodName, args) {\n      element[methodName].apply(element, args);\n    }\n    static clearSelection() {\n      if (window.getSelection) {\n        if (window.getSelection().empty) {\n          window.getSelection().empty();\n        } else if (window.getSelection().removeAllRanges && window.getSelection().rangeCount > 0 && window.getSelection().getRangeAt(0).getClientRects().length > 0) {\n          window.getSelection().removeAllRanges();\n        }\n      } else if (document['selection'] && document['selection'].empty) {\n        try {\n          document['selection'].empty();\n        } catch (error) {\n          //ignore IE bug\n        }\n      }\n    }\n    static getBrowser() {\n      if (!this.browser) {\n        let matched = this.resolveUserAgent();\n        this.browser = {};\n        if (matched.browser) {\n          this.browser[matched.browser] = true;\n          this.browser['version'] = matched.version;\n        }\n        if (this.browser['chrome']) {\n          this.browser['webkit'] = true;\n        } else if (this.browser['webkit']) {\n          this.browser['safari'] = true;\n        }\n      }\n      return this.browser;\n    }\n    static resolveUserAgent() {\n      let ua = navigator.userAgent.toLowerCase();\n      let match = /(chrome)[ \\/]([\\w.]+)/.exec(ua) || /(webkit)[ \\/]([\\w.]+)/.exec(ua) || /(opera)(?:.*version|)[ \\/]([\\w.]+)/.exec(ua) || /(msie) ([\\w.]+)/.exec(ua) || ua.indexOf('compatible') < 0 && /(mozilla)(?:.*? rv:([\\w.]+)|)/.exec(ua) || [];\n      return {\n        browser: match[1] || '',\n        version: match[2] || '0'\n      };\n    }\n    static isInteger(value) {\n      if (Number.isInteger) {\n        return Number.isInteger(value);\n      } else {\n        return typeof value === 'number' && isFinite(value) && Math.floor(value) === value;\n      }\n    }\n    static isHidden(element) {\n      return !element || element.offsetParent === null;\n    }\n    static isVisible(element) {\n      return element && element.offsetParent != null;\n    }\n    static isExist(element) {\n      return element !== null && typeof element !== 'undefined' && element.nodeName && element.parentNode;\n    }\n    static focus(element, options) {\n      element && document.activeElement !== element && element.focus(options);\n    }\n    static getFocusableSelectorString(selector = '') {\n      return `button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        [href][clientHeight][clientWidth]:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        input:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        select:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        textarea:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        [tabIndex]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        [contenteditable]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        .p-inputtext:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n        .p-button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector}`;\n    }\n    static getFocusableElements(element, selector = '') {\n      let focusableElements = this.find(element, this.getFocusableSelectorString(selector));\n      let visibleFocusableElements = [];\n      for (let focusableElement of focusableElements) {\n        const computedStyle = getComputedStyle(focusableElement);\n        if (this.isVisible(focusableElement) && computedStyle.display != 'none' && computedStyle.visibility != 'hidden') visibleFocusableElements.push(focusableElement);\n      }\n      return visibleFocusableElements;\n    }\n    static getFocusableElement(element, selector = '') {\n      let focusableElement = this.findSingle(element, this.getFocusableSelectorString(selector));\n      if (focusableElement) {\n        const computedStyle = getComputedStyle(focusableElement);\n        if (this.isVisible(focusableElement) && computedStyle.display != 'none' && computedStyle.visibility != 'hidden') return focusableElement;\n      }\n      return null;\n    }\n    static getFirstFocusableElement(element, selector = '') {\n      const focusableElements = this.getFocusableElements(element, selector);\n      return focusableElements.length > 0 ? focusableElements[0] : null;\n    }\n    static getLastFocusableElement(element, selector) {\n      const focusableElements = this.getFocusableElements(element, selector);\n      return focusableElements.length > 0 ? focusableElements[focusableElements.length - 1] : null;\n    }\n    static getNextFocusableElement(element, reverse = false) {\n      const focusableElements = DomHandler.getFocusableElements(element);\n      let index = 0;\n      if (focusableElements && focusableElements.length > 0) {\n        const focusedIndex = focusableElements.indexOf(focusableElements[0].ownerDocument.activeElement);\n        if (reverse) {\n          if (focusedIndex == -1 || focusedIndex === 0) {\n            index = focusableElements.length - 1;\n          } else {\n            index = focusedIndex - 1;\n          }\n        } else if (focusedIndex != -1 && focusedIndex !== focusableElements.length - 1) {\n          index = focusedIndex + 1;\n        }\n      }\n      return focusableElements[index];\n    }\n    static generateZIndex() {\n      this.zindex = this.zindex || 999;\n      return ++this.zindex;\n    }\n    static getSelection() {\n      if (window.getSelection) return window.getSelection().toString();else if (document.getSelection) return document.getSelection().toString();else if (document['selection']) return document['selection'].createRange().text;\n      return null;\n    }\n    static getTargetElement(target, el) {\n      if (!target) return null;\n      switch (target) {\n        case 'document':\n          return document;\n        case 'window':\n          return window;\n        case '@next':\n          return el?.nextElementSibling;\n        case '@prev':\n          return el?.previousElementSibling;\n        case '@parent':\n          return el?.parentElement;\n        case '@grandparent':\n          return el?.parentElement.parentElement;\n        default:\n          const type = typeof target;\n          if (type === 'string') {\n            return document.querySelector(target);\n          } else if (type === 'object' && target.hasOwnProperty('nativeElement')) {\n            return this.isExist(target.nativeElement) ? target.nativeElement : undefined;\n          }\n          const isFunction = obj => !!(obj && obj.constructor && obj.call && obj.apply);\n          const element = isFunction(target) ? target() : target;\n          return element && element.nodeType === 9 || this.isExist(element) ? element : null;\n      }\n    }\n    static isClient() {\n      return !!(typeof window !== 'undefined' && window.document && window.document.createElement);\n    }\n    static getAttribute(element, name) {\n      if (element) {\n        const value = element.getAttribute(name);\n        if (!isNaN(value)) {\n          return +value;\n        }\n        if (value === 'true' || value === 'false') {\n          return value === 'true';\n        }\n        return value;\n      }\n      return undefined;\n    }\n    static calculateBodyScrollbarWidth() {\n      return window.innerWidth - document.documentElement.offsetWidth;\n    }\n    static blockBodyScroll(className = 'p-overflow-hidden') {\n      document.body.style.setProperty('--scrollbar-width', this.calculateBodyScrollbarWidth() + 'px');\n      this.addClass(document.body, className);\n    }\n    static unblockBodyScroll(className = 'p-overflow-hidden') {\n      document.body.style.removeProperty('--scrollbar-width');\n      this.removeClass(document.body, className);\n    }\n    static createElement(type, attributes = {}, ...children) {\n      if (type) {\n        const element = document.createElement(type);\n        this.setAttributes(element, attributes);\n        element.append(...children);\n        return element;\n      }\n      return undefined;\n    }\n    static setAttribute(element, attribute = '', value) {\n      if (this.isElement(element) && value !== null && value !== undefined) {\n        element.setAttribute(attribute, value);\n      }\n    }\n    static setAttributes(element, attributes = {}) {\n      if (this.isElement(element)) {\n        const computedStyles = (rule, value) => {\n          const styles = element?.$attrs?.[rule] ? [element?.$attrs?.[rule]] : [];\n          return [value].flat().reduce((cv, v) => {\n            if (v !== null && v !== undefined) {\n              const type = typeof v;\n              if (type === 'string' || type === 'number') {\n                cv.push(v);\n              } else if (type === 'object') {\n                const _cv = Array.isArray(v) ? computedStyles(rule, v) : Object.entries(v).map(([_k, _v]) => rule === 'style' && (!!_v || _v === 0) ? `${_k.replace(/([a-z])([A-Z])/g, '$1-$2').toLowerCase()}:${_v}` : !!_v ? _k : undefined);\n                cv = _cv.length ? cv.concat(_cv.filter(c => !!c)) : cv;\n              }\n            }\n            return cv;\n          }, styles);\n        };\n        Object.entries(attributes).forEach(([key, value]) => {\n          if (value !== undefined && value !== null) {\n            const matchedEvent = key.match(/^on(.+)/);\n            if (matchedEvent) {\n              element.addEventListener(matchedEvent[1].toLowerCase(), value);\n            } else if (key === 'pBind') {\n              this.setAttributes(element, value);\n            } else {\n              value = key === 'class' ? [...new Set(computedStyles('class', value))].join(' ').trim() : key === 'style' ? computedStyles('style', value).join(';').trim() : value;\n              (element.$attrs = element.$attrs || {}) && (element.$attrs[key] = value);\n              element.setAttribute(key, value);\n            }\n          }\n        });\n      }\n    }\n    static isFocusableElement(element, selector = '') {\n      return this.isElement(element) ? element.matches(`button:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                [href][clientHeight][clientWidth]:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                input:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                select:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                textarea:not([tabindex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                [tabIndex]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector},\n                [contenteditable]:not([tabIndex = \"-1\"]):not([disabled]):not([style*=\"display:none\"]):not([hidden])${selector}`) : false;\n    }\n  }\n  return DomHandler;\n})();\nclass ConnectedOverlayScrollHandler {\n  element;\n  listener;\n  scrollableParents;\n  constructor(element, listener = () => {}) {\n    this.element = element;\n    this.listener = listener;\n  }\n  bindScrollListener() {\n    this.scrollableParents = DomHandler.getScrollableParents(this.element);\n    for (let i = 0; i < this.scrollableParents.length; i++) {\n      this.scrollableParents[i].addEventListener('scroll', this.listener);\n    }\n  }\n  unbindScrollListener() {\n    if (this.scrollableParents) {\n      for (let i = 0; i < this.scrollableParents.length; i++) {\n        this.scrollableParents[i].removeEventListener('scroll', this.listener);\n      }\n    }\n  }\n  destroy() {\n    this.unbindScrollListener();\n    this.element = null;\n    this.listener = null;\n    this.scrollableParents = null;\n  }\n}\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ConnectedOverlayScrollHandler, DomHandler };\n//# sourceMappingURL=primeng-dom.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}