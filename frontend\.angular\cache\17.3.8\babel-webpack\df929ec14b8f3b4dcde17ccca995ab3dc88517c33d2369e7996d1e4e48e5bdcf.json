{"ast": null, "code": "import { CommonModule, DOCUMENT } from \"@angular/common\";\nimport { ChangeDetectorRef, inject } from '@angular/core';\nimport { FormsModule } from \"@angular/forms\";\nimport { DomSanitizer } from \"@angular/platform-browser\";\nimport { Router } from \"@angular/router\";\nimport { DropdownModule } from \"primeng/dropdown\";\nimport { EMPTY, forkJoin, of } from \"rxjs\";\nimport { catchError, startWith, switchMap, take, tap } from \"rxjs/operators\";\nimport { AuthService } from \"src/app/core/services/auth.service\";\nimport { GeneralService } from \"src/app/core/services/general.service\";\nimport { LingoletteService } from \"src/app/core/services/lingolette.service\";\nimport { PackageService } from \"src/app/core/services/package.service\";\nimport { TeacherApplicationService } from \"src/app/core/services/teacher-application.service\";\nimport { UserService } from \"src/app/core/services/user.service\";\nimport { LoaderComponent } from \"src/app/shared/loader/loader.component\";\nimport { SubSink } from \"subsink\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/layout.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"primeng/dropdown\";\nconst _c0 = [\"lingoletteWrapper\"];\nconst _c1 = [\"lottieEl\"];\nconst _c2 = a0 => ({\n  \"surface-section px-4 py-8 md:px-6 lg:px-4 block-gradient-reverse border-round-3xl\\nflex align-items-center justify-content-center\": a0\n});\nfunction LingoletteRoomComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6);\n    i0.ɵɵelement(2, \"app-loader\", 7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scale\", 1.7)(\"size\", 200);\n  }\n}\nfunction LingoletteRoomComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 8)(2, \"div\", 9);\n    i0.ɵɵelement(3, \"div\", 10, 1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h5\", 11);\n    i0.ɵɵtext(6, \" Please purchase a Flexible or Premium Package to use our AI-Chat.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function LingoletteRoomComponent_ng_container_3_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onGoToBuyPackage());\n    });\n    i0.ɵɵelement(8, \"span\", 13);\n    i0.ɵɵelementStart(9, \"span\", 14);\n    i0.ɵɵtext(10, \"Buy a Package\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(11, \"i\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction LingoletteRoomComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 8)(2, \"p\");\n    i0.ɵɵtext(3, \" There was an error adding your Lingolette account.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function LingoletteRoomComponent_ng_container_4_Template_button_click_4_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onClickRemoveLingoletteUser());\n    });\n    i0.ɵɵelement(5, \"span\", 13);\n    i0.ɵɵelementStart(6, \"span\", 14);\n    i0.ɵɵtext(7, \"Reset your Lingolette account\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"i\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction LingoletteRoomComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 16)(2, \"div\", 17)(3, \"div\", 18);\n    i0.ɵɵelement(4, \"div\", 10, 1);\n    i0.ɵɵelementStart(6, \"div\", 19);\n    i0.ɵɵtext(7, \"Start Learning\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"p\", 20);\n    i0.ɵɵelementStart(9, \"div\", 8)(10, \"div\", 21)(11, \"div\", 22)(12, \"label\", 23);\n    i0.ɵɵtext(13, \"Choose a language to learn\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p-dropdown\", 24);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LingoletteRoomComponent_ng_container_5_Template_p_dropdown_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedLanguage, $event) || (ctx_r1.selectedLanguage = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function LingoletteRoomComponent_ng_container_5_Template_p_dropdown_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTeacherChange($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(15, \"div\", 21)(16, \"div\", 22)(17, \"label\", 23);\n    i0.ɵɵtext(18, \"Choose a language level\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"p-dropdown\", 24);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LingoletteRoomComponent_ng_container_5_Template_p_dropdown_ngModelChange_19_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedLevel, $event) || (ctx_r1.selectedLevel = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function LingoletteRoomComponent_ng_container_5_Template_p_dropdown_ngModelChange_19_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTeacherChange($event));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(20, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function LingoletteRoomComponent_ng_container_5_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.createLingoletteUser());\n    });\n    i0.ɵɵelement(21, \"span\", 13);\n    i0.ɵɵelementStart(22, \"span\", 14);\n    i0.ɵɵtext(23, \"Continue to Chat\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(24, \"i\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(25, \"div\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"options\", ctx_r1.languageOptions);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedLanguage);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"options\", ctx_r1.languageLevels);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedLevel);\n  }\n}\nfunction LingoletteRoomComponent_ng_container_6_iframe_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"iframe\", 26);\n    i0.ɵɵlistener(\"load\", function LingoletteRoomComponent_ng_container_6_iframe_1_Template_iframe_load_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.handleIframeLoaded());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r1.url, i0.ɵɵsanitizeResourceUrl);\n  }\n}\nfunction LingoletteRoomComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, LingoletteRoomComponent_ng_container_6_iframe_1_Template, 1, 1, \"iframe\", 25);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showIframe);\n  }\n}\nexport let LingoletteRoomComponent = /*#__PURE__*/(() => {\n  class LingoletteRoomComponent {\n    constructor(layoutService, renderer, document) {\n      this.layoutService = layoutService;\n      this.renderer = renderer;\n      this.document = document;\n      this.subs = new SubSink();\n      this.lingoletteService = inject(LingoletteService);\n      this.authService = inject(AuthService);\n      this.packageService = inject(PackageService);\n      this.generalService = inject(GeneralService);\n      this.userService = inject(UserService);\n      this.sanitizer = inject(DomSanitizer);\n      this.router = inject(Router);\n      this.teacherApplicationService = inject(TeacherApplicationService);\n      this.loading = true;\n      this.showIframe = false;\n      this.userAddError = false;\n      this.user = {};\n      this.token = null;\n      this.cdr = inject(ChangeDetectorRef);\n      this.galaxyWrapper = {};\n      this.lottieEl = {};\n      this.languageCodeMap = {\n        \"English\": \"en\",\n        \"French\": \"fr\",\n        \"German\": \"de\",\n        \"Italian\": \"it\",\n        \"Spanish\": \"es\",\n        \"Swedish\": \"sv\",\n        \"Dutch\": \"nl\",\n        \"Russian\": \"ru\",\n        \"Japanese\": \"ja\",\n        \"Korean\": \"ko\",\n        \"Chinese\": \"zh\"\n      };\n      this.languageLevels = [{\n        label: 'I don\\'t know',\n        value: 0\n      }, {\n        label: 'Absolute Beginner',\n        value: 7\n      }, {\n        label: 'A1',\n        value: 1\n      }, {\n        label: 'A2',\n        value: 2\n      }, {\n        label: 'B1',\n        value: 3\n      }, {\n        label: 'B2',\n        value: 4\n      }, {\n        label: 'C1',\n        value: 5\n      }, {\n        label: 'C2',\n        value: 6\n      }];\n      this.step = 0;\n      this.languageOptions = [];\n      this.selectedLanguage = {\n        label: 'English',\n        value: 'en'\n      };\n      this.selectedLevel = this.languageLevels[1];\n      this.url = {};\n      this.isUserPremiumOrFlex = false;\n    }\n    ngOnInit() {\n      this.languageOptions = Object.keys(this.languageCodeMap).map(key => ({\n        label: key,\n        value: this.languageCodeMap[key]\n      }));\n      this.selectedLanguage = this.languageOptions[0];\n      this.user = this.authService.getLoggedInUser();\n      this.getLingoLanguages();\n      this.setIframeHeight();\n      forkJoin([this.packageService.getAllUserPackages(this.authService.getUserId()).pipe(tap(res => {\n        // handle packages\n        this.handlePackages(res);\n      })), this.lingoletteService.getLingoletteUserList().pipe(tap(res => {\n        // handle lingolette user list\n        console.log(res);\n      }), catchError(err => {\n        console.error(err);\n        return EMPTY;\n      }))]).subscribe();\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n    }\n    handlePackages(packages) {\n      const matchingPackage = packages.find(pkg => pkg.packLanguage && this.languageOptions.some(opt => opt.label === pkg.packLanguage));\n      const matchingLevel = packages.find(pkg => pkg.packageLevel && this.languageLevels.some(opt => opt.label === pkg.packageLevel));\n      console.log(matchingPackage);\n      if (matchingLevel) {\n        this.selectedLevel = {\n          label: matchingLevel.packageLevel,\n          value: this.languageLevels.find(opt => opt.label === matchingLevel.packageLevel)?.value\n        };\n      }\n      if (matchingPackage) {\n        this.selectedLanguage = {\n          label: matchingPackage.packLanguage,\n          value: this.languageCodeMap[matchingPackage.packLanguage]\n        };\n      }\n      this.handleLingoletteInit();\n      console.log(this.packageService.userIsOnFlexOrPremiumPackage(packages));\n      this.isUserPremiumOrFlex = this.packageService.userIsOnFlexOrPremiumPackage(packages);\n    }\n    handleLingoletteInit() {\n      setTimeout(() => {\n        this.addLottiePlayer(this.lottieEl);\n      }, 600);\n      // if (this.step !== 1) {\n      //     return;\n      // }\n      // this.createLingoletteUser();\n      this.subs.add(this.userService.getUserById(this.authService.getUserId()).pipe(take(1), tap(res => {\n        console.log(res);\n        if (res.lingoletteId !== \"\" && res.lingoletteId !== null) {\n          this.getLingoletteToken(res.lingoletteId);\n        } else {\n          this.loading = false;\n          this.step = 1;\n        }\n      })).subscribe());\n    }\n    getLingoletteToken(lingoletteId) {\n      this.subs.add(this.lingoletteService.createLingoletteUserSession(lingoletteId).pipe(take(1), tap(res => {\n        console.log(res);\n        this.token = res.data.token;\n        setTimeout(() => {\n          this.step = 2;\n          this.showIframe = true;\n          const url = `https://lingolette.com/talk/?token=${this.token}`;\n          this.url = this.sanitizer.bypassSecurityTrustResourceUrl(url);\n        }, 500);\n        this.loading = false;\n      }), catchError(err => {\n        console.log(err);\n        this.handleError(err);\n        return EMPTY;\n      })).subscribe());\n    }\n    createLingoletteUser() {\n      const userData = {\n        \"name\": this.generalService.getPersonFullName(this.user),\n        \"targetLng\": this.selectedLanguage.value,\n        \"nativeLng\": \"en\",\n        \"languageLevel\": this.selectedLevel.value\n      };\n      this.subs.add(this.lingoletteService.createLingoletteUser(userData).subscribe({\n        next: res => {\n          console.log(res);\n          this.getLingoletteToken(res.data.id);\n        },\n        error: err => {\n          console.log(err);\n          this.handleError(err);\n        }\n      }));\n    }\n    onClickRemoveLingoletteUser() {\n      this.userService.getUserById(this.authService.getUserId()).subscribe(res => {\n        console.log(res);\n        if (res.lingoletteId !== \"\" && res.lingoletteId !== null) {\n          this.removeLingoletteUser(res.lingoletteId);\n        } else {\n          this.step = 1;\n        }\n      });\n    }\n    removeLingoletteUser(userId) {\n      this.subs.add(this.lingoletteService.removeLingoletteUser(userId).pipe(tap(res => {\n        console.log(res);\n        this.step = 1;\n      }), catchError(err => {\n        console.log(err);\n        this.handleError(err);\n        return EMPTY;\n      })).subscribe());\n    }\n    setIframeHeight() {\n      this.subs.sink = this.generalService.deviceKind.pipe(take(2), switchMap(res => {\n        console.log(res);\n        if (res.is576 || res.is992) {\n          return of(0);\n        } else {\n          return this.layoutService.sideMenuHeight;\n        }\n      }), startWith(0)).subscribe(height => {\n        console.log(window.innerHeight);\n        if (height !== 0) {\n          this.galaxyWrapper.nativeElement.style.height = height + \"px\";\n          this.cdr.detectChanges();\n        } else {\n          this.galaxyWrapper.nativeElement.style.height = window.innerHeight - 70 + \"px\";\n          this.cdr.detectChanges();\n        }\n      });\n    }\n    handleIframeLoaded() {\n      console.log('Iframe loaded');\n      // Perform any actions you want to take once the iframe is loaded\n      // For example, you can hide the loading spinner or show a success message\n    }\n    getLingoLanguages() {\n      this.subs.sink = this.teacherApplicationService.getLingoTeachingLanguages().pipe(switchMap(res => {\n        this.mltLanguages = res;\n        return of(null);\n      })).subscribe(res => {\n        if (!res) {\n          return;\n        }\n      }, error => {\n        this.handleError(error);\n      });\n    }\n    onTeacherChange(event) {\n      console.log(event);\n    }\n    onGoToBuyPackage() {\n      this.router.navigate(['/dashboard/buy-package/']);\n    }\n    addLottiePlayer(el) {\n      console.log(el);\n      if (!el) {\n        return;\n      }\n      const lottiePlayer = `<lottie-player src=\"/assets/icons/lottie/ai-chat-2.json\"  \n    background=\"transparent\"  speed=\"1\"  style=\"width: 120px; height: 120px; margin:-8px auto; transform: scale(1.2);\" autoplay loop></lottie-player>`;\n      el.nativeElement.innerHTML = lottiePlayer;\n    }\n    handleError(error) {\n      console.error(error);\n      this.loading = false;\n      this.userService.getUserById(this.authService.getUserId()).subscribe(res => {\n        console.log(res);\n        if (res.lingoletteId !== \"\" && res.lingoletteId !== null) {\n          this.removeLingoletteUser(res.lingoletteId);\n        } else {\n          this.step = 1;\n        }\n      });\n    }\n    static #_ = this.ɵfac = function LingoletteRoomComponent_Factory(t) {\n      return new (t || LingoletteRoomComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(DOCUMENT));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LingoletteRoomComponent,\n      selectors: [[\"app-lingolette-room\"]],\n      viewQuery: function LingoletteRoomComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.galaxyWrapper = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.lottieEl = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 7,\n      vars: 8,\n      consts: [[\"lingoletteWrapper\", \"\"], [\"lottieEl\", \"\"], [\"class\", \"h-17rem md:h-auto w-full flex align-items-center justify-content-center\", 4, \"ngIf\"], [3, \"ngClass\"], [4, \"ngIf\"], [1, \"h-17rem\", \"md:h-auto\", \"w-full\", \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"\"], [3, \"scale\", \"size\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"h-full\", \"flex-column\", \"text-center\"], [1, \"text-center\", \"w-full\"], [2, \"mix-blend-mode\", \"luminosity\"], [1, \"m-0\", \"text-lg\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Enroll Now\", 1, \"p-element\", \"p-ripple\", \"font-bold\", \"mt-2\", \"px-3\", \"py-2\", \"p-button-outlined\", \"p-button-rounded\", \"white-space-nowrap\", \"p-button\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"p-button-icon\", \"p-button-icon-right\"], [1, \"p-button-label\", \"mr-3\"], [1, \"pi\", \"pi-arrow-right\"], [1, \"w-full\"], [1, \"w-full\", \"flex\", \"m-auto\", \"flex-column\", \"lg:flex-row\", \"justify-content-center\", \"align-items-center\", \"gap-7\"], [1, \"text-center\", \"lg:text-center\"], [1, \"mt-0\", \"mb-3\", \"font-bold\", \"text-2xl\", \"text-primary\", \"text-center\"], [1, \"text-700\", \"text-md\", \"mt-0\", \"mb-2\"], [1, \"p-fluid\", \"w-full\"], [1, \"field\", \"mb-0\"], [\"htmlfor\", \"name1\"], [\"optionLabel\", \"label\", \"placeholder\", \"Select language \", \"appendTo\", \"body\", \"styleClass\", \"w-full dropdown-blue rounded\", 1, \"col-12\", \"dropdown-blue\", 3, \"ngModelChange\", \"options\", \"ngModel\"], [\"frameborder\", \"0\", \"width\", \"100%\", \"height\", \"100%\", \"allow\", \"camera;microphone\", \"class\", \"border-round-xl\", 3, \"src\", \"load\", 4, \"ngIf\"], [\"frameborder\", \"0\", \"width\", \"100%\", \"height\", \"100%\", \"allow\", \"camera;microphone\", 1, \"border-round-xl\", 3, \"load\", \"src\"]],\n      template: function LingoletteRoomComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, LingoletteRoomComponent_div_0_Template, 3, 2, \"div\", 2);\n          i0.ɵɵelementStart(1, \"div\", 3, 0);\n          i0.ɵɵtemplate(3, LingoletteRoomComponent_ng_container_3_Template, 12, 0, \"ng-container\", 4)(4, LingoletteRoomComponent_ng_container_4_Template, 9, 0, \"ng-container\", 4)(5, LingoletteRoomComponent_ng_container_5_Template, 26, 4, \"ng-container\", 4)(6, LingoletteRoomComponent_ng_container_6_Template, 2, 1, \"ng-container\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c2, ctx.step === 1));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isUserPremiumOrFlex && !ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.userAddError && ctx.step === 0 && ctx.isUserPremiumOrFlex);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.step === 1 && ctx.isUserPremiumOrFlex);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.step === 2 && ctx.isUserPremiumOrFlex);\n        }\n      },\n      dependencies: [CommonModule, i2.NgClass, i2.NgIf, FormsModule, i3.NgControlStatus, i3.NgModel, LoaderComponent, DropdownModule, i4.Dropdown],\n      styles: [\"[_nghost-%COMP%]{display:block}\"]\n    });\n  }\n  return LingoletteRoomComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}