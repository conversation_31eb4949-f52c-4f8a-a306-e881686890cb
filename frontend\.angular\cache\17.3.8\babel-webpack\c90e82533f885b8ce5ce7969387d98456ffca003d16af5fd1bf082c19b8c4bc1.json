{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AuthGuard } from 'src/app/core/guards/auth.guard';\nimport { TeacherGuard } from 'src/app/core/guards/teacher.guard';\nimport { SideMenuContainerLayoutComponent } from 'src/app/shared/layout/side-menu-container-layout/side-menu-container-layout.component';\nimport { DashboardComponent } from './dashboard.component';\nimport { BuyPackageComponent } from '../classroom/packages/buy-package/buy-package.component';\nimport { CertificateComponent } from '../certificate/certificate.component';\nimport { PackagesBlockPageComponent } from '../classroom/packages/packages-block-page/packages-block-page.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: SideMenuContainerLayoutComponent,\n  children: [{\n    path: '',\n    component: DashboardComponent\n  }, {\n    path: \"calendar\",\n    loadChildren: () => import(\"../calendar/calendar.module\").then(m => m.CalendarModule),\n    canActivate: [AuthGuard, TeacherGuard]\n  }, {\n    path: \"notes\",\n    loadChildren: () => import(\"../note/note.module\").then(m => m.NoteModule),\n    canActivate: [AuthGuard, TeacherGuard]\n  }, {\n    path: \"library\",\n    loadChildren: () => import(\"../library/library.module\").then(m => m.LibraryModule),\n    canActivate: [AuthGuard, TeacherGuard]\n  }, {\n    path: \"homework\",\n    loadChildren: () => import(\"../homework/homework.module\").then(m => m.HomeworkModule),\n    canActivate: [AuthGuard, TeacherGuard]\n  }, {\n    path: \"classrooms\",\n    loadChildren: () => import(\"../classroom/classroom.module\").then(m => m.ClassroomModule),\n    canActivate: [AuthGuard, TeacherGuard]\n  }, {\n    path: \"certificates\",\n    // loadChildren: () =>\n    //   import(\"../certificate/certificate.module\").then(\n    //     (m) => m.CertificateModule\n    //   ),\n    component: CertificateComponent,\n    canActivate: [AuthGuard, TeacherGuard]\n  }, {\n    path: \"guides\",\n    loadChildren: () => import(\"../guides/guides.module\").then(m => m.GuidesModule)\n  }, {\n    path: \"my-packages\",\n    component: PackagesBlockPageComponent,\n    canActivate: [AuthGuard]\n  }, {\n    path: \"buy-package\",\n    component: BuyPackageComponent,\n    canActivate: [AuthGuard]\n  }, {\n    path: \"meet\",\n    loadComponent: () => import(\"../jitsi-meet/meet-room/meet-room.component\").then(m => m.MeetRoomComponent),\n    canActivate: [AuthGuard],\n    data: {\n      animation: \"Basic\",\n      hideLeftMenu: true\n    }\n  }, {\n    path: \"ai-chat\",\n    loadComponent: () => import(\"./lingolette-room/lingolette-room.component\").then(m => m.LingoletteRoomComponent),\n    canActivate: [AuthGuard]\n  }]\n}];\nexport class DashboardRoutingModule {\n  static #_ = this.ɵfac = function DashboardRoutingModule_Factory(t) {\n    return new (t || DashboardRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: DashboardRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(DashboardRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "SideMenuContainerLayoutComponent", "DashboardComponent", "BuyPackageComponent", "CertificateComponent", "PackagesBlockPageComponent", "routes", "path", "component", "children", "loadChildren", "then", "m", "CalendarModule", "canActivate", "NoteModule", "LibraryModule", "HomeworkModule", "ClassroomModule", "GuidesModule", "loadComponent", "MeetRoomComponent", "data", "animation", "hideLeftMenu", "LingoletteRoomComponent", "DashboardRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\dashboard\\dashboard-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { AuthGuard } from 'src/app/core/guards/auth.guard';\r\nimport { TeacherGuard } from 'src/app/core/guards/teacher.guard';\r\nimport { SideMenuContainerLayoutComponent } from 'src/app/shared/layout/side-menu-container-layout/side-menu-container-layout.component';\r\nimport { DashboardComponent } from './dashboard.component';\r\nimport { BuyPackageComponent } from '../classroom/packages/buy-package/buy-package.component';\r\nimport { CertificateComponent } from '../certificate/certificate.component';\r\nimport { PackagesBlockPageComponent } from '../classroom/packages/packages-block-page/packages-block-page.component';\r\n\r\n\r\nconst routes: Routes = [\r\n  {\r\n    path: '', component: SideMenuContainerLayoutComponent,\r\n    children: [\r\n      {\r\n        path: '',\r\n        component: DashboardComponent,\r\n      },\r\n      {\r\n        path: \"calendar\",\r\n        loadChildren: () =>\r\n          import(\"../calendar/calendar.module\").then(\r\n            (m) => m.CalendarModule\r\n          ),\r\n        canActivate: [AuthGuard, TeacherGuard],\r\n      },\r\n      {\r\n        path: \"notes\",\r\n        loadChildren: () =>\r\n          import(\"../note/note.module\").then(\r\n            (m) => m.NoteModule\r\n          ),\r\n        canActivate: [AuthGuard, TeacherGuard],\r\n      },\r\n      {\r\n        path: \"library\",\r\n        loadChildren: () =>\r\n          import(\"../library/library.module\").then(\r\n            (m) => m.LibraryModule\r\n          ),\r\n        canActivate: [AuthGuard, TeacherGuard],\r\n      },\r\n      {\r\n        path: \"homework\",\r\n        loadChildren: () =>\r\n          import(\"../homework/homework.module\").then(\r\n            (m) => m.HomeworkModule\r\n          ),\r\n        canActivate: [AuthGuard, TeacherGuard],\r\n      },\r\n      {\r\n        path: \"classrooms\",\r\n        loadChildren: () =>\r\n          import(\"../classroom/classroom.module\").then(\r\n            (m) => m.ClassroomModule\r\n          ),\r\n        canActivate: [AuthGuard, TeacherGuard],\r\n      },\r\n      {\r\n        path: \"certificates\",\r\n        // loadChildren: () =>\r\n        //   import(\"../certificate/certificate.module\").then(\r\n        //     (m) => m.CertificateModule\r\n        //   ),\r\n        component: CertificateComponent,\r\n        canActivate: [AuthGuard, TeacherGuard],\r\n      },\r\n      {\r\n        path: \"guides\",\r\n        loadChildren: () =>\r\n          import(\"../guides/guides.module\").then(\r\n            (m) => m.GuidesModule\r\n          ),\r\n      },\r\n      {\r\n        path: \"my-packages\",\r\n        component: PackagesBlockPageComponent,\r\n        canActivate: [AuthGuard],\r\n      },\r\n      {\r\n        path: \"buy-package\",\r\n        component: BuyPackageComponent,\r\n        canActivate: [AuthGuard],\r\n      },\r\n      {\r\n        path: \"meet\",\r\n        loadComponent: () =>\r\n          import(\"../jitsi-meet/meet-room/meet-room.component\").then(\r\n            (m) => m.MeetRoomComponent\r\n          ),\r\n        canActivate: [AuthGuard],\r\n        data: { animation: \"Basic\", hideLeftMenu: true },\r\n      },\r\n      {\r\n        path: \"ai-chat\",\r\n        loadComponent: () =>\r\n          import(\"./lingolette-room/lingolette-room.component\").then(\r\n            (m) => m.LingoletteRoomComponent\r\n          ),\r\n        canActivate: [AuthGuard],\r\n      },\r\n    ]\r\n  },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class DashboardRoutingModule { }"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,SAAS,QAAQ,gCAAgC;AAC1D,SAASC,YAAY,QAAQ,mCAAmC;AAChE,SAASC,gCAAgC,QAAQ,uFAAuF;AACxI,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,mBAAmB,QAAQ,yDAAyD;AAC7F,SAASC,oBAAoB,QAAQ,sCAAsC;AAC3E,SAASC,0BAA0B,QAAQ,yEAAyE;;;AAGpH,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEP,gCAAgC;EACrDQ,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,EAAE;IACRC,SAAS,EAAEN;GACZ,EACD;IACEK,IAAI,EAAE,UAAU;IAChBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CACvCC,CAAC,IAAKA,CAAC,CAACC,cAAc,CACxB;IACHC,WAAW,EAAE,CAACf,SAAS,EAAEC,YAAY;GACtC,EACD;IACEO,IAAI,EAAE,OAAO;IACbG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,qBAAqB,CAAC,CAACC,IAAI,CAC/BC,CAAC,IAAKA,CAAC,CAACG,UAAU,CACpB;IACHD,WAAW,EAAE,CAACf,SAAS,EAAEC,YAAY;GACtC,EACD;IACEO,IAAI,EAAE,SAAS;IACfG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,2BAA2B,CAAC,CAACC,IAAI,CACrCC,CAAC,IAAKA,CAAC,CAACI,aAAa,CACvB;IACHF,WAAW,EAAE,CAACf,SAAS,EAAEC,YAAY;GACtC,EACD;IACEO,IAAI,EAAE,UAAU;IAChBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CACvCC,CAAC,IAAKA,CAAC,CAACK,cAAc,CACxB;IACHH,WAAW,EAAE,CAACf,SAAS,EAAEC,YAAY;GACtC,EACD;IACEO,IAAI,EAAE,YAAY;IAClBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,+BAA+B,CAAC,CAACC,IAAI,CACzCC,CAAC,IAAKA,CAAC,CAACM,eAAe,CACzB;IACHJ,WAAW,EAAE,CAACf,SAAS,EAAEC,YAAY;GACtC,EACD;IACEO,IAAI,EAAE,cAAc;IACpB;IACA;IACA;IACA;IACAC,SAAS,EAAEJ,oBAAoB;IAC/BU,WAAW,EAAE,CAACf,SAAS,EAAEC,YAAY;GACtC,EACD;IACEO,IAAI,EAAE,QAAQ;IACdG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,yBAAyB,CAAC,CAACC,IAAI,CACnCC,CAAC,IAAKA,CAAC,CAACO,YAAY;GAE1B,EACD;IACEZ,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEH,0BAA0B;IACrCS,WAAW,EAAE,CAACf,SAAS;GACxB,EACD;IACEQ,IAAI,EAAE,aAAa;IACnBC,SAAS,EAAEL,mBAAmB;IAC9BW,WAAW,EAAE,CAACf,SAAS;GACxB,EACD;IACEQ,IAAI,EAAE,MAAM;IACZa,aAAa,EAAEA,CAAA,KACb,MAAM,CAAC,6CAA6C,CAAC,CAACT,IAAI,CACvDC,CAAC,IAAKA,CAAC,CAACS,iBAAiB,CAC3B;IACHP,WAAW,EAAE,CAACf,SAAS,CAAC;IACxBuB,IAAI,EAAE;MAAEC,SAAS,EAAE,OAAO;MAAEC,YAAY,EAAE;IAAI;GAC/C,EACD;IACEjB,IAAI,EAAE,SAAS;IACfa,aAAa,EAAEA,CAAA,KACb,MAAM,CAAC,6CAA6C,CAAC,CAACT,IAAI,CACvDC,CAAC,IAAKA,CAAC,CAACa,uBAAuB,CACjC;IACHX,WAAW,EAAE,CAACf,SAAS;GACxB;CAEJ,CACF;AAMD,OAAM,MAAO2B,sBAAsB;EAAA,QAAAC,CAAA,G;qBAAtBD,sBAAsB;EAAA;EAAA,QAAAE,EAAA,G;UAAtBF;EAAsB;EAAA,QAAAG,EAAA,G;cAHvB/B,YAAY,CAACgC,QAAQ,CAACxB,MAAM,CAAC,EAC7BR,YAAY;EAAA;;;2EAEX4B,sBAAsB;IAAAK,OAAA,GAAAC,EAAA,CAAAlC,YAAA;IAAAmC,OAAA,GAFvBnC,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}