{"ast": null, "code": "import { skip } from 'rxjs/operators';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/layout.service\";\nimport * as i2 from \"../packages.component\";\nconst _c0 = [\"packagesWrapper\"];\nexport class PackagesBlockPageComponent {\n  constructor(layoutService) {\n    this.layoutService = layoutService;\n    this.subs = new SubSink();\n    this.packagesWrapper = {};\n  }\n  ngOnInit() {}\n  ngAfterViewInit() {\n    this.subs.sink = this.layoutService.sideMenuHeight.pipe(skip(1)).subscribe(res => {\n      if (res !== 0) {\n        this.packagesWrapper.nativeElement.style.minHeight = res + 'px';\n      }\n    });\n  }\n  static #_ = this.ɵfac = function PackagesBlockPageComponent_Factory(t) {\n    return new (t || PackagesBlockPageComponent)(i0.ɵɵdirectiveInject(i1.LayoutService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PackagesBlockPageComponent,\n    selectors: [[\"app-packages-block-page\"]],\n    viewQuery: function PackagesBlockPageComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.packagesWrapper = _t.first);\n      }\n    },\n    decls: 8,\n    vars: 0,\n    consts: [[\"packagesWrapper\", \"\"], [\"id\", \"profile\", 1, \"profile\", \"flex-column\", \"border-round-xl\"], [1, \"block-header\", \"justify-content-center\", \"hidden\", \"lg:flex\"], [1, \"block-title\"], [1, \"text-0\"], [1, \"mt-2\", \"h-full\"]],\n    template: function PackagesBlockPageComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 1, 0)(2, \"div\", 2)(3, \"span\", 3)(4, \"span\", 4);\n        i0.ɵɵtext(5, \"My Packages\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(6, \"div\", 5);\n        i0.ɵɵelement(7, \"app-packages\");\n        i0.ɵɵelementEnd()();\n      }\n    },\n    dependencies: [i2.PackagesComponent],\n    styles: [\".profile[_ngcontent-%COMP%] {\\n  border-radius: 10px;\\n  background-color: var(--white);\\n  display: flex;\\n}\\n.profile[_ngcontent-%COMP%]   .block-header[_ngcontent-%COMP%] {\\n  background: url(\\\"/assets/images/profile-header-bg.png\\\") no-repeat center;\\n  border-radius: 10px;\\n  background-size: 100%;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9jbGFzc3Jvb20vcGFja2FnZXMvcGFja2FnZXMtYmxvY2stcGFnZS9wYWNrYWdlcy1ibG9jay1wYWdlLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBRUksbUJBQUE7RUFDQSw4QkFBQTtFQUNBLGFBQUE7QUFBSjtBQUVJO0VBQ0ksd0VBQUE7RUFDQSxtQkFBQTtFQUNBLHFCQUFBO0FBQVIiLCJzb3VyY2VzQ29udGVudCI6WyIucHJvZmlsZSB7XHJcbiAgICAvLyBtYXgtaGVpZ2h0OiA3NzBweDtcclxuICAgIGJvcmRlci1yYWRpdXM6IDEwcHg7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS13aGl0ZSk7XHJcbiAgICBkaXNwbGF5OiBmbGV4O1xyXG5cclxuICAgIC5ibG9jay1oZWFkZXIge1xyXG4gICAgICAgIGJhY2tncm91bmQ6IHVybCgnL2Fzc2V0cy9pbWFnZXMvcHJvZmlsZS1oZWFkZXItYmcucG5nJykgbm8tcmVwZWF0IGNlbnRlcjtcclxuICAgICAgICBib3JkZXItcmFkaXVzOiAxMHB4O1xyXG4gICAgICAgIGJhY2tncm91bmQtc2l6ZTogMTAwJTtcclxuICAgIH1cclxufSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "names": ["skip", "SubSink", "PackagesBlockPageComponent", "constructor", "layoutService", "subs", "packagesWrapper", "ngOnInit", "ngAfterViewInit", "sink", "sideMenuHeight", "pipe", "subscribe", "res", "nativeElement", "style", "minHeight", "_", "i0", "ɵɵdirectiveInject", "i1", "LayoutService", "_2", "selectors", "viewQuery", "PackagesBlockPageComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\packages-block-page\\packages-block-page.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\packages-block-page\\packages-block-page.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { skip } from 'rxjs/operators';\r\nimport { LayoutService } from 'src/app/core/services/layout.service';\r\nimport { SubSink } from 'subsink';\r\n\r\n@Component({\r\n  selector: 'app-packages-block-page',\r\n  templateUrl: './packages-block-page.component.html',\r\n  styleUrls: ['./packages-block-page.component.scss']\r\n})\r\nexport class PackagesBlockPageComponent implements OnInit {\r\n\r\n  private subs = new SubSink();\r\n  @ViewChild('packagesWrapper', { static: true }) packagesWrapper: ElementRef = {} as ElementRef;\r\n  constructor(private layoutService: LayoutService) { }\r\n\r\n  ngOnInit(): void {\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    this.subs.sink = this.layoutService.sideMenuHeight.pipe(skip(1)).subscribe((res) => {\r\n      if (res !== 0) {\r\n        this.packagesWrapper.nativeElement.style.minHeight = (res + 'px');\r\n      }\r\n    });\r\n  }\r\n\r\n}\r\n", "<div id=\"profile\" class=\"profile flex-column border-round-xl\" #packagesWrapper>\r\n    <div class=\"block-header justify-content-center hidden lg:flex\">\r\n        <span class=\"block-title\">\r\n            <span class=\"text-0\">My Packages</span>\r\n        </span>\r\n    </div>\r\n    <div class=\"mt-2 h-full\">\r\n        <app-packages></app-packages>\r\n    </div>\r\n</div>"], "mappings": "AACA,SAASA,IAAI,QAAQ,gBAAgB;AAErC,SAASC,OAAO,QAAQ,SAAS;;;;;AAOjC,OAAM,MAAOC,0BAA0B;EAIrCC,YAAoBC,aAA4B;IAA5B,KAAAA,aAAa,GAAbA,aAAa;IAFzB,KAAAC,IAAI,GAAG,IAAIJ,OAAO,EAAE;IACoB,KAAAK,eAAe,GAAe,EAAgB;EAC1C;EAEpDC,QAAQA,CAAA,GACR;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACH,IAAI,CAACI,IAAI,GAAG,IAAI,CAACL,aAAa,CAACM,cAAc,CAACC,IAAI,CAACX,IAAI,CAAC,CAAC,CAAC,CAAC,CAACY,SAAS,CAAEC,GAAG,IAAI;MACjF,IAAIA,GAAG,KAAK,CAAC,EAAE;QACb,IAAI,CAACP,eAAe,CAACQ,aAAa,CAACC,KAAK,CAACC,SAAS,GAAIH,GAAG,GAAG,IAAK;MACnE;IACF,CAAC,CAAC;EACJ;EAAC,QAAAI,CAAA,G;qBAfUf,0BAA0B,EAAAgB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;UAA1BpB,0BAA0B;IAAAqB,SAAA;IAAAC,SAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;QCP3BR,EAHZ,CAAAU,cAAA,gBAA+E,aACX,cAClC,cACD;QAAAV,EAAA,CAAAW,MAAA,kBAAW;QAExCX,EAFwC,CAAAY,YAAA,EAAO,EACpC,EACL;QACNZ,EAAA,CAAAU,cAAA,aAAyB;QACrBV,EAAA,CAAAa,SAAA,mBAA6B;QAErCb,EADI,CAAAY,YAAA,EAAM,EACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}