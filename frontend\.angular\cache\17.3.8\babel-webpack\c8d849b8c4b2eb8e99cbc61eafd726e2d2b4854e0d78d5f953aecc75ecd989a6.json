{"ast": null, "code": "import { switchMap } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./general.service\";\nconst BACKEND_URL = environment.apiUrl;\nexport let GuideService = /*#__PURE__*/(() => {\n  class GuideService {\n    constructor(http, generalService) {\n      this.http = http;\n      this.generalService = generalService;\n    }\n    getGuides() {\n      let myGuides = [];\n      return this.http.get(BACKEND_URL + \"/Upload/GetManuals\").pipe(switchMap(tutorials => {\n        tutorials.map(tutorial => {\n          myGuides.push({\n            title: this.generalService.getFileName(tutorial),\n            link: BACKEND_URL + tutorial\n          });\n        });\n        return myGuides;\n      }));\n    }\n    static #_ = this.ɵfac = function GuideService_Factory(t) {\n      return new (t || GuideService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.GeneralService));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: GuideService,\n      factory: GuideService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return GuideService;\n})();", "map": {"version": 3, "names": ["switchMap", "environment", "BACKEND_URL", "apiUrl", "GuideService", "constructor", "http", "generalService", "getGuides", "myGuides", "get", "pipe", "tutorials", "map", "tutorial", "push", "title", "getFileName", "link", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "GeneralService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\services\\guide.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { switchMap } from 'rxjs/operators';\r\nimport { environment } from 'src/environments/environment';\r\nimport { Guide } from '../models/guide.modal';\r\nimport { GeneralService } from './general.service';\r\nconst BACKEND_URL = environment.apiUrl\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class GuideService {\r\n  \r\n  constructor(\r\n    private http: HttpClient,\r\n    private generalService: GeneralService\r\n  ) { }\r\n\r\n  public getGuides(): Observable<Guide> {\r\n    let myGuides: any[] = [];\r\n    return this.http.get<Guide[]>(BACKEND_URL + \"/Upload/GetManuals\")\r\n      .pipe(\r\n        switchMap((tutorials: Guide[]) => {\r\n          tutorials.map((tutorial: any) => {\r\n            myGuides.push(\r\n              {\r\n                title: this.generalService.getFileName(tutorial),\r\n                link: BACKEND_URL + tutorial\r\n              }\r\n            )\r\n          })\r\n          return myGuides;\r\n        })\r\n      )\r\n  }\r\n}\r\n"], "mappings": "AAGA,SAASA,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,WAAW,QAAQ,8BAA8B;;;;AAG1D,MAAMC,WAAW,GAAGD,WAAW,CAACE,MAAM;AAItC,WAAaC,YAAY;EAAnB,MAAOA,YAAY;IAEvBC,YACUC,IAAgB,EAChBC,cAA8B;MAD9B,KAAAD,IAAI,GAAJA,IAAI;MACJ,KAAAC,cAAc,GAAdA,cAAc;IACpB;IAEGC,SAASA,CAAA;MACd,IAAIC,QAAQ,GAAU,EAAE;MACxB,OAAO,IAAI,CAACH,IAAI,CAACI,GAAG,CAAUR,WAAW,GAAG,oBAAoB,CAAC,CAC9DS,IAAI,CACHX,SAAS,CAAEY,SAAkB,IAAI;QAC/BA,SAAS,CAACC,GAAG,CAAEC,QAAa,IAAI;UAC9BL,QAAQ,CAACM,IAAI,CACX;YACEC,KAAK,EAAE,IAAI,CAACT,cAAc,CAACU,WAAW,CAACH,QAAQ,CAAC;YAChDI,IAAI,EAAEhB,WAAW,GAAGY;WACrB,CACF;QACH,CAAC,CAAC;QACF,OAAOL,QAAQ;MACjB,CAAC,CAAC,CACH;IACL;IAAC,QAAAU,CAAA,G;uBAvBUf,YAAY,EAAAgB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,cAAA;IAAA;IAAA,QAAAC,EAAA,G;aAAZtB,YAAY;MAAAuB,OAAA,EAAZvB,YAAY,CAAAwB,IAAA;MAAAC,UAAA,EAFX;IAAM;;SAEPzB,YAAY;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}