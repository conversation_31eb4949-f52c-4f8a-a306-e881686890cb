{"ast": null, "code": "import { formatDate } from '@angular/common';\nimport { PackageType } from 'src/app/core/models/package.model';\nimport { SubSink } from 'subsink';\nimport * as moment from 'moment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nimport * as i2 from \"src/app/core/services/package.service\";\nimport * as i3 from \"src/app/core/services/auth.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"primeng/button\";\nimport * as i6 from \"../../../shared/layout/g-level-circle/g-level-circle.component\";\nimport * as i7 from \"../../../shared/classroom/classroom-status-filters/classroom-status-filters.component\";\nimport * as i8 from \"./package-accordion-item/package-accordion-item.component\";\nimport * as i9 from \"./package-content-info/package-content-info.component\";\nconst _c0 = a0 => ({\n  \"trial-gradient\": a0\n});\nconst _c1 = (a0, a1) => ({\n  \"sm:col-1\": a0,\n  \"sm:col-2\": a1\n});\nfunction PackagesComponent_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function PackagesComponent_ng_container_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.generalService.navigateToBuyPackage(!ctx_r1.generalService.isNullishObject(ctx_r1.classroom) ? ctx_r1.classroom : null));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction PackagesComponent_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 9)(2, \"div\", 10)(3, \"strong\");\n    i0.ɵɵtext(4, \"There are no packages available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"img\", 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 12)(7, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function PackagesComponent_ng_container_8_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.generalService.navigateToBuyPackage());\n    });\n    i0.ɵɵelementStart(8, \"span\", 14);\n    i0.ɵɵtext(9, \"Buy Package\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(10, \"img\", 15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction PackagesComponent_ng_container_10_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 20);\n    i0.ɵɵtext(2, \"Language\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 29);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const package_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(package_r4.packLanguage.trim() !== \"\" ? package_r4.packLanguage : \"-\");\n  }\n}\nfunction PackagesComponent_ng_container_10_ng_container_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-package-content-info\", 38);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const package_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"type\", \"Package Expiration\")(\"date\", ctx_r1.formatDate(package_r4.expiresOn));\n  }\n}\nfunction PackagesComponent_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 16)(2, \"app-package-accordion-item\")(3, \"div\", 17)(4, \"div\", 18)(5, \"div\", 19)(6, \"div\", 20);\n    i0.ɵɵtext(7, \"Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 21);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(10, PackagesComponent_ng_container_10_div_10_Template, 5, 1, \"div\", 22);\n    i0.ɵɵelementStart(11, \"div\", 23)(12, \"div\", 20);\n    i0.ɵɵtext(13, \"Total hours\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 24);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 25)(17, \"div\", 20);\n    i0.ɵɵtext(18, \"Hours Left\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 24);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 25)(22, \"div\", 20);\n    i0.ɵɵtext(23, \"Level\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 26);\n    i0.ɵɵelement(25, \"g-level-circle\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 25)(27, \"div\", 20);\n    i0.ɵɵtext(28, \"Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 21);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 25)(32, \"div\", 20);\n    i0.ɵɵtext(33, \"Expires on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 21);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 28)(37, \"div\", 20);\n    i0.ɵɵtext(38, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 29);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(41, \"div\", 30)(42, \"div\", 31)(43, \"div\", 32);\n    i0.ɵɵtext(44, \" Events \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"div\", 33)(46, \"div\", 34);\n    i0.ɵɵtext(47, \" Type \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"div\", 35);\n    i0.ɵɵtext(49, \" Duration \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"div\", 35);\n    i0.ɵɵtext(51, \" From \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 36);\n    i0.ɵɵtext(53, \" To \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"div\", 37);\n    i0.ɵɵtext(55, \" Date \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(56, \"app-package-content-info\", 38);\n    i0.ɵɵtemplate(57, PackagesComponent_ng_container_10_ng_container_57_Template, 2, 2, \"ng-container\", 6);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const package_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(13, _c0, package_r4.type === \"Trial\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.authService.isStudent ? package_r4.classroomTeacher : ctx_r1.generalService.getPersonFullName(package_r4.paidByStudent));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isStudent);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(15, _c1, ctx_r1.authService.isStudent, ctx_r1.authService.isTeacher));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(package_r4.totalHours);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(package_r4.hoursLeft);\n    i0.ɵɵadvance(5);\n    i0.ɵɵpropertyInterpolate(\"name\", package_r4.packageLevel === \"\" || package_r4.packageLevel === \"No Lesson\" || package_r4.packageLevel === \"No Classroom\" ? \"TBD\" : package_r4.packageLevel);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(package_r4.type);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(package_r4.type === \"Trial\" ? \"-\" : ctx_r1.formatDate(package_r4.expiresOn));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(package_r4.state.toLowerCase());\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"type\", package_r4.type === \"Trial\" ? \"Trial Created\" : \"Package Purchase\")(\"date\", ctx_r1.formatDate(package_r4.purchasedAt));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", package_r4.type !== \"Trial\");\n  }\n}\nexport let PackagesComponent = /*#__PURE__*/(() => {\n  class PackagesComponent {\n    constructor(generalService, packageService, authService, ngZone) {\n      this.generalService = generalService;\n      this.packageService = packageService;\n      this.authService = authService;\n      this.ngZone = ngZone;\n      this.classroom = {};\n      this.subs = new SubSink();\n      this.packages = [];\n      this.filteredPackages = [];\n      this.viewImgSrc = \"/assets/icons/down.svg\";\n      this.filtersOpen = false;\n      this.packageFilterHelper = this.packageService.packageFilterHelper;\n      this.packageFilter = this.packageService.getCurrentPackageFilter();\n      this.packageFilterWithoutTheCurrent = this.packageService.getPackageFilterWithoutTheCurrent();\n      this.packageFilterSwitches = [{\n        label: 'Classrooms',\n        value: false\n      }, {\n        label: PackageType.TRIAL,\n        value: false\n      }, {\n        label: PackageType.GIFT,\n        value: false\n      }, {\n        label: 'All',\n        value: true\n      }];\n    }\n    ngOnInit() {\n      this.getPackages();\n    }\n    ngAfterViewInit() {}\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n    }\n    getPackages() {\n      this.subs.sink = this.packageService.getAllUserPackages(this.authService.getUserId()).subscribe(res => {\n        console.log(res);\n        this.packages = res;\n        this.filteredPackages = res;\n        this.toggleSwitch('All');\n      });\n    }\n    getDurationInDays(date1, date2) {\n      return this.generalService.getDatesDiff(date1, date2).days;\n    }\n    togglePackage(event, viewImgElement, packageElement, viewImgElementSrc) {\n      let id = packageElement.getAttribute('id');\n      let setOpen = viewImgElement.getAttribute('open') === 'false' ? 'true' : 'false';\n      var section = document.getElementById(id);\n      viewImgElement.setAttribute('open', setOpen);\n      if (setOpen === 'true') {\n        console.log(\"open\");\n        viewImgElementSrc.style.transform = \"rotate(180deg)\";\n        this.generalService.expandSection(section);\n      } else {\n        viewImgElementSrc.style.transform = \"rotate(0)\";\n        this.generalService.collapseSection(section);\n      }\n    }\n    toggleFilters() {\n      if (this.filtersOpen) {\n        this.generalService.slideOutElement('blur_bg');\n        this.generalService.slideOutElement('package-filters');\n      } else {\n        this.generalService.slideInElement('blur_bg');\n        this.generalService.slideInElement('package-filters');\n      }\n      this.filtersOpen = !this.filtersOpen;\n    }\n    getDateLessonLevelEnds(lesson_level_ended) {\n      if (lesson_level_ended.toString().includes('1970')) {\n        return 'present';\n      } else {\n        return formatDate(lesson_level_ended, 'dd-MM-yyyy', 'en_US');\n      }\n    }\n    formatDate(dateString) {\n      const date = moment(dateString);\n      return date.format('D/MM/YYYY');\n    }\n    toggleSwitch(event) {\n      this.resetSwitches();\n      switch (event) {\n        case PackageType.GIFT:\n        case PackageType.TRIAL:\n        case 'Classrooms':\n        case 'All':\n          this.toggleFoundSwitch(event);\n          break;\n      }\n      this.filteredPackages = this.filterPackages(event);\n    }\n    resetSwitches() {\n      for (const key in this.packageFilterSwitches) {\n        this.packageFilterSwitches[key].value = false;\n      }\n    }\n    /**\n    * Filters the packages based on the specified type and classroom ID.\n    *\n    * @param {string} type - The type of package to filter by.\n    * @returns {Package[]} - The filtered packages.\n    */\n    filterPackages(type) {\n      const filteredPackages = this.packages.filter(pkg => {\n        if (type === 'All') {\n          return true;\n        }\n        if (type === 'Classrooms') {\n          return [PackageType.FLEX, PackageType.PREMIUM, PackageType.REGULAR].includes(pkg.type);\n        } else {\n          return pkg.type === type;\n        }\n      });\n      console.log(filteredPackages);\n      return this.generalService.isNullishObject(this.classroom) ? filteredPackages : filteredPackages.filter(pkg => pkg.classroomId === this.classroom.id);\n    }\n    /**\n    Toggles the value of a switch object with the given label.\n    @param {string} label - The label of the switch object to toggle.\n    */\n    toggleFoundSwitch(label) {\n      const switchObj = this.packageFilterSwitches.find(s => s.label === label);\n      if (switchObj) {\n        switchObj.value = !switchObj.value;\n      }\n    }\n    static #_ = this.ɵfac = function PackagesComponent_Factory(t) {\n      return new (t || PackagesComponent)(i0.ɵɵdirectiveInject(i1.GeneralService), i0.ɵɵdirectiveInject(i2.PackageService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PackagesComponent,\n      selectors: [[\"app-packages\"]],\n      inputs: {\n        classroom: \"classroom\"\n      },\n      decls: 11,\n      vars: 6,\n      consts: [[1, \"packages\", \"overflow-y-auto\", \"mb-4\"], [\"id\", \"packages-top\", 1, \"packages-top\", \"pt-0\", \"pb-4\"], [1, \"packages-header\", \"flex-column\", \"sm:flex-row\", \"gap-2\", \"sm:gap-0\", \"p-0\", \"my-4\"], [1, \"packages-header-title\", \"text-primary\", \"m-0\", \"font-bold\", \"font-xl\"], [1, \"ml-auto\", \"sm:mr-2\"], [3, \"switchesWithLabelsChange\", \"switchToggled\", \"switchesWithLabels\"], [4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"Buy Package\", \"icon\", \"\", \"iconPos\", \"left\", 1, \"p-button-sm\", \"p-button-raised\", \"p-button-rounded\", \"blue-gradient-btn\", \"font-2xs\", \"col-12\", \"sm:col-2\", 2, \"padding\", \"0.5rem 0.875rem\", 3, \"click\"], [1, \"flex\", \"w-full\", \"h-full\", \"py-3\", \"flex-column\", \"align-items-center\", \"justify-content-center\"], [1, \"flex\", \"h-full\", \"align-items-center\", \"justify-content-center\", \"flex-column\"], [\"src\", \"/assets/icons/empty-classroom.png\", \"height\", \"280\"], [1, \"flex\", \"gap-2\", \"align-items-center\", \"justify-content-around\"], [\"pButton\", \"\", \"pRipple\", \"\", \"type\", \"button\", \"label\", \"\", \"icon\", \"\", \"iconPos\", \"left\", 1, \"p-button-sm\", \"p-button-raised\", \"p-button-rounded\", \"blue-gradient-btn\", \"flex\", \"gap-3\", 3, \"click\"], [1, \"font-lg\"], [\"src\", \"/assets/icons/arrow-right.svg\"], [1, \"w-100\", \"block-gradient\", \"border-round-xl\", 3, \"ngClass\"], [\"title\", \"\", 1, \"grid\"], [1, \"grid\", \"grid-nogutter\", \"surface-border\", \"px-2\", \"pt-2\", \"justify-content-between\", \"w-full\"], [1, \"col-4\", \"mt-3\", \"sm:col-2\", \"font-2xs\", \"text-center\", \"sm:text-left\"], [1, \"text-500\", \"font-medium\", \"mb-1\"], [1, \"text-900\"], [\"class\", \"col-4 mt-3 sm:col-1 font-2xs text-center sm:text-left\", 4, \"ngIf\"], [1, \"col-4\", \"mt-3\", \"sm:col-1\", \"font-2xs\", \"text-center\", 3, \"ngClass\"], [1, \"text-900\", \"text-center\"], [1, \"col-4\", \"mt-3\", \"sm:col-1\", \"font-2xs\", \"text-center\"], [1, \"text-900\", \"flex\", \"justify-content-center\"], [\"size\", \"xs\", \"textSize\", \"2xs\", 3, \"name\"], [1, \"col-4\", \"mt-3\", \"sm:col-1\", \"font-2xs\", \"text-center\", \"sm:mr-1\"], [1, \"text-900\", \"capitalize\"], [\"content\", \"\", 1, \"p-2\"], [1, \"w-full\", \"font-sm\", \"justify-content-center\"], [1, \"package-content-title\", \"font-bold\"], [1, \"package-content-header\"], [1, \"col-20-per\", \"col-20-per-left\", \"color-gray-1\"], [1, \"col-20-per\", \"color-gray-1\"], [1, \"col-20-per\", \"col-20-per-right\", \"color-gray-1\"], [1, \"col-20-per\", \"col-25-per-right\", \"color-gray-1\"], [3, \"type\", \"date\"], [1, \"col-4\", \"mt-3\", \"sm:col-1\", \"font-2xs\", \"text-center\", \"sm:text-left\"]],\n      template: function PackagesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h3\", 3);\n          i0.ɵɵtext(4, \"All Packages\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 4)(6, \"app-classroom-status-filters\", 5);\n          i0.ɵɵtwoWayListener(\"switchesWithLabelsChange\", function PackagesComponent_Template_app_classroom_status_filters_switchesWithLabelsChange_6_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.packageFilterSwitches, $event) || (ctx.packageFilterSwitches = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"switchToggled\", function PackagesComponent_Template_app_classroom_status_filters_switchToggled_6_listener($event) {\n            return ctx.toggleSwitch($event);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(7, PackagesComponent_ng_container_7_Template, 2, 0, \"ng-container\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(8, PackagesComponent_ng_container_8_Template, 11, 0, \"ng-container\", 6);\n          i0.ɵɵelementStart(9, \"div\");\n          i0.ɵɵtemplate(10, PackagesComponent_ng_container_10_Template, 58, 18, \"ng-container\", 7);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"switchesWithLabels\", ctx.packageFilterSwitches);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.authService.isStudent);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.filteredPackages.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵclassMap(ctx.filteredPackages.length === 0 ? \"flex align-items-center justify-content-center w-full h-full\" : \"\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredPackages);\n        }\n      },\n      dependencies: [i4.NgClass, i4.NgForOf, i4.NgIf, i5.ButtonDirective, i6.GLevelCircleComponent, i7.ClassroomStatusFiltersComponent, i8.PackageAccordionItemComponent, i9.PackageContentInfoComponent],\n      styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}.packages[_ngcontent-%COMP%]{position:relative;background-color:var(--white);border-radius:40px;width:100%}.packages-top[_ngcontent-%COMP%]{top:0;width:100%;padding:20px 30px 0;box-sizing:border-box}@media screen and (max-width: 1366px){.packages-top[_ngcontent-%COMP%]{padding:20px;position:relative}}@media only screen and (max-width: 768px){.packages-top[_ngcontent-%COMP%]{padding:0}}.packages-top[_ngcontent-%COMP%]   .package-header[_ngcontent-%COMP%], .packages-top[_ngcontent-%COMP%]   .package-header-filters[_ngcontent-%COMP%]{width:100%;display:flex;flex-wrap:wrap;position:relative}.packages-top[_ngcontent-%COMP%]   .package-header-filters[_ngcontent-%COMP%]{position:absolute;background-color:var(--white);color:#fff!important;z-index:200;top:100px;border-radius:10px;width:90%;padding:10px 0}.packages-header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:30px 0}.package-content-title[_ngcontent-%COMP%]{width:100%;text-align:center}.package-content-header[_ngcontent-%COMP%]{width:100%;display:flex;flex-wrap:wrap;font-size:12px;margin-top:15px}.package-content-info[_ngcontent-%COMP%]{width:100%;display:flex;justify-content:space-evenly;border-bottom:.5px solid var(--gray-border-2);padding:10px 0}.package-content-info[_ngcontent-%COMP%]:last-child{border-bottom:0px solid var(--gray-border-2)}.filters-button[_ngcontent-%COMP%]{z-index:200}.color-gray-1[_ngcontent-%COMP%]{color:var(--gray-1)}.package[_ngcontent-%COMP%]{overflow:hidden;transition:height .3s ease-out;height:auto;width:100%;height:0;margin-top:20px}.arrow[_ngcontent-%COMP%]{overflow:hidden;transition-duration:.2s;transition-property:transform;cursor:pointer}.package-filter[_ngcontent-%COMP%]{flex-direction:column;align-items:center;justify-content:start}.white-button[_ngcontent-%COMP%]{margin:10px}.btns[_ngcontent-%COMP%]{margin:auto}@media screen and (max-width: 1024px){.cut-on-tablet[_ngcontent-%COMP%]{display:none}.col-18-per-tablet[_ngcontent-%COMP%]{flex-basis:18%;width:18%}.col-20-per-tablet[_ngcontent-%COMP%]{flex-basis:20%;width:20%}.col-8-per-tablet[_ngcontent-%COMP%]{flex-basis:8%;width:8%}.col-14-per-tablet[_ngcontent-%COMP%]{flex-basis:14%;width:14%}}@media screen and (max-width: 768px){.cut-on-mobile[_ngcontent-%COMP%]{display:none}.col-24-per-mobile[_ngcontent-%COMP%]{flex-basis:24%;width:24%}}[_nghost-%COMP%]     .e-date-icon{position:absolute!important;left:0!important}[_nghost-%COMP%]     .e-input{margin-left:30px!important}[_nghost-%COMP%]     .e-date-modal{width:400px}[_nghost-%COMP%]     .small-input-switch .p-inputswitch{height:1rem;width:1.8rem}[_nghost-%COMP%]     .small-input-switch .p-inputswitch.p-inputswitch-checked .p-inputswitch-slider:before{transform:translate(1rem);background-image:linear-gradient(90deg,#6f89fb,#647cf7,#5e77f3,#526cef,#4861ea 44%,#415ae6 56%,#3751e1,#2c4add,#1f3dd6,#0b36d0)}[_nghost-%COMP%]     .small-input-switch .p-inputswitch .p-inputswitch-slider{background-image:linear-gradient(90deg,#e4e8f6,#dfe4f6,#dbe1f5,#d6dcf5,#cdd5f3 44%,#c9d1f3 56%,#c4cdf3,#c0caf2,#bbc6f2,#b7c2f1)}[_nghost-%COMP%]     .small-input-switch .p-inputswitch .p-inputswitch-slider:before{width:.8rem;height:.8rem;margin-top:-.4rem}.trial-gradient[_ngcontent-%COMP%]{background:linear-gradient(#fff,#3fd1ff33)!important}\"]\n    });\n  }\n  return PackagesComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}