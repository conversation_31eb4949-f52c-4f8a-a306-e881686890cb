{"ast": null, "code": "import { take } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/user.service\";\nimport * as i2 from \"src/app/core/services/general.service\";\nimport * as i3 from \"src/app/core/services/classroom.service\";\nimport * as i4 from \"src/app/core/services/toast.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"../../../../../../../shared/lesson/mini-lesson-info-card/mini-lesson-info-card.component\";\nimport * as i7 from \"primeng/button\";\nimport * as i8 from \"primeng/api\";\nimport * as i9 from \"primeng/chip\";\nimport * as i10 from \"../../../../../../../shared/layout/g-level-circle/g-level-circle.component\";\nimport * as i11 from \"primeng/carousel\";\nconst _c0 = [\"carousel\"];\nconst _c1 = [[[\"\", \"title\", \"\"]]];\nconst _c2 = [\"[title]\"];\nconst _c3 = (a0, a1) => ({\n  \"pi-chevron-down\": a0,\n  \"pi-chevron-up\": a1\n});\nconst _c4 = a0 => ({\n  \"open\": a0\n});\nfunction ClassInfoStudentAccordionItemComponent_ng_template_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57);\n    i0.ɵɵelement(1, \"g-level-circle\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"name\", item_r2);\n  }\n}\nfunction ClassInfoStudentAccordionItemComponent_ng_container_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 25);\n    i0.ɵɵelement(2, \"app-mini-lesson-info-card\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const lesson_r3 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"lesson\", lesson_r3)(\"classroom\", ctx_r3.classroom);\n  }\n}\nexport class ClassInfoStudentAccordionItemComponent {\n  constructor(userService, generalService, classroomService, toastService) {\n    this.userService = userService;\n    this.generalService = generalService;\n    this.classroomService = classroomService;\n    this.toastService = toastService;\n    this.carousel = {};\n    this.items = this.generalService.mltLevels;\n    this.title = '';\n    this.isActive = false;\n    this.student = {};\n    this.classroom = {};\n    this.selectedLevel = 'A1';\n  }\n  toggle() {\n    this.isActive = !this.isActive;\n  }\n  ngOnInit() {\n    console.log(this.classroom);\n    this.selectedLevel = this.classroom.activeLevel;\n  }\n  trackById(index, item) {\n    return +item.id;\n  }\n  updateCarousel() {\n    // console.log(this.carousel);\n    const currentIndex = this.carousel._page - 1;\n    const nextIndex = (currentIndex + 1) % this.items.length;\n    console.log([this.items[nextIndex]]);\n    // console.log(currentIndex);\n    this.classroomService.changeTrialLevel(this.classroom.id, this.items[nextIndex]).pipe(take(1)).subscribe(res => {\n      console.log(res);\n      if (res) {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Classroom level has been changed to ' + this.items[nextIndex]\n        });\n      }\n    });\n    // this.carousel.value = [this.items[nextIndex]];\n  }\n  getSelectedIndex() {\n    return this.items.findIndex(item => item === this.selectedLevel);\n  }\n  static #_ = this.ɵfac = function ClassInfoStudentAccordionItemComponent_Factory(t) {\n    return new (t || ClassInfoStudentAccordionItemComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i2.GeneralService), i0.ɵɵdirectiveInject(i3.ClassroomService), i0.ɵɵdirectiveInject(i4.ToastService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ClassInfoStudentAccordionItemComponent,\n    selectors: [[\"app-class-info-student-accordion-item\"]],\n    viewQuery: function ClassInfoStudentAccordionItemComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.carousel = _t.first);\n      }\n    },\n    inputs: {\n      title: \"title\",\n      isActive: \"isActive\",\n      student: \"student\",\n      classroom: \"classroom\"\n    },\n    ngContentSelectors: _c2,\n    decls: 139,\n    vars: 32,\n    consts: [[\"carousel\", \"\"], [1, \"accordion\", \"border-round-xl\"], [1, \"header\", 3, \"click\"], [1, \"package-header\", \"w-100\", \"justify-content-between\", \"align-items-center\", \"font-xs\", \"font-bold\"], [1, \"flex\"], [1, \"circle\", \"ml-1\"], [1, \"down-arrow\", \"pi\", 3, \"ngClass\"], [1, \"accordion-content\", \"accordion-content-transition\", 3, \"ngClass\"], [1, \"grid\", \"grid-nogutter\", \"py-3\"], [1, \"col-12\", \"md:col-4\"], [1, \"flex\", \"flex-row\", \"justify-content-center\", \"md:justify-content-start\"], [1, \"flex\", \"flex-column\", \"md:ml-3\"], [1, \"flex\", \"gap-2\", \"mb-2\"], [1, \"flex\", \"flex-column\", \"w-8rem\"], [1, \"person__title\"], [1, \"person__detail\"], [1, \"flex\", \"flex-column\"], [1, \"col-12\", \"md:col-4\", \"align-items-center\", \"justify-content-center\"], [1, \"m-0\", \"person__title\", \"text-center\"], [1, \"max-w-11rem\", \"my-1\", \"m-auto\"], [3, \"value\", \"page\", \"showIndicators\", \"numVisible\", \"numScroll\"], [\"pTemplate\", \"item\"], [1, \"font-2xs\", \"text-center\", \"mt-2\"], [\"pButton\", \"\", \"type\", \"button\", \"label\", \"Update\", 1, \"p-button-xs\", 3, \"click\"], [1, \"w-full\", \"grid\", \"mr-2\"], [1, \"col-6\", \"sm:col-6\"], [\"cardType\", \"request\", 3, \"classroom\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"grid\", \"mt-2\", \"hidden\"], [1, \"col-12\", \"md:col-3\", \"md:col-offset-1\"], [1, \"pb-0\", \"mb-1\", \"text-primary\", \"font-bold\"], [1, \"block-gradient\", \"border-round-xl\", \"p-2\"], [1, \"pb-0\", \"mb-1\", \"text-primary\", \"font-bold\", \"font-sm\"], [1, \"flex\", \"flex-column\", \"align-items-center\", \"justify-content-center\"], [1, \"flex\", \"align-items-center\"], [\"size\", \"lg\", 3, \"name\"], [1, \"pb-0\", \"mt-1\", \"mb-1\", \"text-primary\", \"font-bold\", \"font-sm\"], [1, \"m-0\"], [1, \"md:col-8\", \"flex-column\", \"gap-2\", \"hidden\"], [1, \"flex\", \"block-gradient\", \"py-3\", \"mx-2\", \"gap-3\", \"flex-column\", \"sm:flex-row\", \"justify-content-center\", \"align-items-center\", \"border-round-xl\"], [1, \"circle-stat\", \"text-center\", \"text-primary\"], [1, \"flex\", \"flex-column\", \"align-items-center\", \"gap-1\"], [1, \"flex\", \"justify-content-center\"], [\"src\", \"/assets/icons/goal-sm-icon.svg\", 1, \"rating-icon\"], [1, \"text-2xl\"], [1, \"font-xs\", \"line-height-1\", \"max-w-4rem\"], [1, \"flex\", \"block-gradient\", \"px-2\", \"py-3\", \"mx-2\", \"gap-3\", \"flex-row\", \"sm:flex-row\", \"justify-content-center\", \"align-items-center\", \"border-round-xl\"], [1, \"\"], [1, \"text-primary\", \"font-sm\", \"line-height-1\"], [1, \"text-900\", \"w-full\", \"md:flex-order-0\", \"flex-order-1\"], [1, \"mr-2\", \"mb-1\", \"class-rating\", \"writing\", \"border-round-xl\"], [1, \"font-xs\", \"mb-1\", \"text-white\"], [1, \"mr-2\", \"mb-1\", \"class-rating\", \"speaking\", \"border-round-xl\"], [1, \"mr-2\", \"mb-1\", \"class-rating\", \"reading\", \"border-round-xl\"], [1, \"mr-2\", \"mb-1\", \"class-rating\", \"listening\", \"border-round-xl\"], [1, \"mr-2\", \"mb-1\", \"class-rating\", \"revision\", \"border-round-xl\"], [1, \"mr-2\", \"mb-1\", \"class-rating\", \"grammar\", \"border-round-xl\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\"], [\"cardType\", \"lesson\", 3, \"lesson\", \"classroom\"]],\n    template: function ClassInfoStudentAccordionItemComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef(_c1);\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n        i0.ɵɵlistener(\"click\", function ClassInfoStudentAccordionItemComponent_Template_div_click_1_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.toggle());\n        });\n        i0.ɵɵelementStart(2, \"div\", 3);\n        i0.ɵɵprojection(3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5);\n        i0.ɵɵelement(6, \"i\", 6);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"div\", 7)(8, \"div\", 8)(9, \"div\", 9)(10, \"div\", 10)(11, \"div\", 11)(12, \"div\", 12)(13, \"div\", 13)(14, \"p\", 14);\n        i0.ɵɵtext(15, \"First Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"p\", 15);\n        i0.ɵɵtext(17);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"div\", 16)(19, \"p\", 14);\n        i0.ɵɵtext(20, \"Last Name\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"p\", 15);\n        i0.ɵɵtext(22);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(23, \"div\", 12)(24, \"div\", 16)(25, \"p\", 14);\n        i0.ɵɵtext(26, \"Skype\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(27, \"p\", 15);\n        i0.ɵɵtext(28);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(29, \"div\", 12)(30, \"div\", 16)(31, \"p\", 14);\n        i0.ɵɵtext(32, \"E-mail\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(33, \"p\", 15);\n        i0.ɵɵtext(34);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(35, \"div\", 12)(36, \"div\", 13)(37, \"p\", 14);\n        i0.ɵɵtext(38, \"Birthday\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(39, \"p\", 15);\n        i0.ɵɵtext(40);\n        i0.ɵɵpipe(41, \"date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(42, \"div\", 16)(43, \"p\", 14);\n        i0.ɵɵtext(44, \"Country\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(45, \"p\", 15);\n        i0.ɵɵtext(46);\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(47, \"div\", 17)(48, \"p\", 18);\n        i0.ɵɵtext(49, \" Choose Level \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(50, \"div\", 19)(51, \"p-carousel\", 20, 0);\n        i0.ɵɵtemplate(53, ClassInfoStudentAccordionItemComponent_ng_template_53_Template, 2, 1, \"ng-template\", 21);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(54, \"p\", 22)(55, \"button\", 23);\n        i0.ɵɵlistener(\"click\", function ClassInfoStudentAccordionItemComponent_Template_button_click_55_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.updateCarousel());\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(56, \"div\", 9)(57, \"div\", 24)(58, \"div\", 25);\n        i0.ɵɵelement(59, \"app-mini-lesson-info-card\", 26);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(60, ClassInfoStudentAccordionItemComponent_ng_container_60_Template, 3, 2, \"ng-container\", 27);\n        i0.ɵɵpipe(61, \"slice\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(62, \"div\", 28)(63, \"div\", 29)(64, \"div\", 16)(65, \"h5\", 30)(66, \"span\");\n        i0.ɵɵtext(67);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(68, \"div\", 31)(69, \"h6\", 32)(70, \"span\");\n        i0.ɵɵtext(71, \"Desired level\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(72, \"div\", 33)(73, \"div\", 34);\n        i0.ɵɵelement(74, \"g-level-circle\", 35);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(75, \"h6\", 36)(76, \"span\");\n        i0.ɵɵtext(77, \"Why\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(78, \"p\", 37);\n        i0.ɵɵtext(79, \"For educational / academic reasons\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(80, \"p\", 37);\n        i0.ɵɵtext(81, \"For work, to boost my career To obtain a certificate / pass an exam \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(82, \"div\", 38)(83, \"div\", 39)(84, \"div\", 40)(85, \"div\", 41)(86, \"div\", 42);\n        i0.ɵɵelement(87, \"img\", 43);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(88, \"span\", 44);\n        i0.ɵɵtext(89, \"37\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(90, \"span\", 45);\n        i0.ɵɵtext(91, \"Reasons for achievemtn\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(92, \"div\", 40)(93, \"div\", 41)(94, \"div\", 42);\n        i0.ɵɵelement(95, \"img\", 43);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(96, \"span\", 44);\n        i0.ɵɵtext(97, \"37\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(98, \"span\", 45);\n        i0.ɵɵtext(99, \"Reasons for achievemtn\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(100, \"div\", 40)(101, \"div\", 41)(102, \"div\", 42);\n        i0.ɵɵelement(103, \"img\", 43);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(104, \"span\", 44);\n        i0.ɵɵtext(105, \"37\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(106, \"span\", 45);\n        i0.ɵɵtext(107, \"Reasons for achievemtn\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(108, \"div\", 40)(109, \"div\", 41)(110, \"div\", 42);\n        i0.ɵɵelement(111, \"img\", 43);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(112, \"span\", 44);\n        i0.ɵɵtext(113, \"37\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(114, \"span\", 45);\n        i0.ɵɵtext(115, \"Reasons for achievemtn\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(116, \"div\", 46)(117, \"div\", 47)(118, \"span\", 48);\n        i0.ɵɵtext(119, \"Topic for improvement\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(120, \"div\", 49)(121, \"p-chip\", 50)(122, \"span\", 51);\n        i0.ɵɵtext(123, \"Writing\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(124, \"p-chip\", 52)(125, \"span\", 51);\n        i0.ɵɵtext(126, \"Speaking\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(127, \"p-chip\", 53)(128, \"span\", 51);\n        i0.ɵɵtext(129, \"Reading\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(130, \"p-chip\", 54)(131, \"span\", 51);\n        i0.ɵɵtext(132, \"listening\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(133, \"p-chip\", 55)(134, \"span\", 51);\n        i0.ɵɵtext(135, \"revision\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(136, \"p-chip\", 56)(137, \"span\", 51);\n        i0.ɵɵtext(138, \"grammar\");\n        i0.ɵɵelementEnd()()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵclassProp(\"active\", ctx.isActive);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(27, _c3, !ctx.isActive, ctx.isActive));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(30, _c4, ctx.isActive));\n        i0.ɵɵadvance(10);\n        i0.ɵɵtextInterpolate(ctx.generalService.replaceImportedWithDash(ctx.student.firstName));\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.generalService.replaceImportedWithDash(ctx.student.lastName));\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate(ctx.generalService.replaceImportedWithDash(ctx.student.skype));\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate(ctx.student.username);\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(41, 20, ctx.student.dob, \"d/M/y\"));\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate(ctx.generalService.replaceImportedWithDash(ctx.student.origin));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"value\", ctx.items)(\"page\", ctx.getSelectedIndex())(\"showIndicators\", false)(\"numVisible\", 1)(\"numScroll\", 1);\n        i0.ɵɵadvance(8);\n        i0.ɵɵproperty(\"classroom\", ctx.classroom);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind3(61, 23, ctx.classroom.lessons, 0, 4))(\"ngForTrackBy\", ctx.trackById);\n        i0.ɵɵadvance(7);\n        i0.ɵɵtextInterpolate1(\"\", ctx.student.firstName, \"\\u2019s Goals\");\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"name\", \"A1\");\n      }\n    },\n    dependencies: [i5.NgClass, i5.NgForOf, i6.MiniLessonInfoCardComponent, i7.ButtonDirective, i8.PrimeTemplate, i9.Chip, i10.GLevelCircleComponent, i11.Carousel, i5.SlicePipe, i5.DatePipe],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.accordion[_ngcontent-%COMP%] {\\n  border: 1px solid transparent;\\n  border-radius: 4px;\\n  margin-bottom: 10px;\\n}\\n\\n.header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 10px;\\n  cursor: pointer;\\n}\\n\\n.content[_ngcontent-%COMP%] {\\n  padding: 10px;\\n}\\n\\n.down-arrow[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease-in-out;\\n}\\n\\n.down-arrow.pi-chevron-up[_ngcontent-%COMP%] {\\n  transform: rotate(360deg);\\n}\\n\\n.circle[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border-radius: 50%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.circle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #3F51B5;\\n  font-size: 1.25rem;\\n}\\n\\n.rotate[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n  transition: transform 0.3s ease-in-out;\\n}\\n\\n.accordion-content[_ngcontent-%COMP%] {\\n  max-height: 0;\\n  overflow: hidden;\\n  transition: max-height 0.7s ease-in-out;\\n}\\n\\n.open[_ngcontent-%COMP%] {\\n  max-height: 80rem;\\n}\\n@media only screen and (min-width: 768px) {\\n  .open[_ngcontent-%COMP%] {\\n    max-height: 40rem;\\n  }\\n}\\n\\n.circle-stat[_ngcontent-%COMP%] {\\n  min-width: 100px;\\n  min-height: 100px;\\n  border-radius: 200px;\\n  background-color: #CCD6FF;\\n  color: white;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.person__title[_ngcontent-%COMP%] {\\n  font-size: clamp(0.75rem, 0.07vw + 0.74rem, 0.88rem);\\n  margin: 0;\\n  color: #2D2A4B;\\n}\\n\\n.person__detail[_ngcontent-%COMP%] {\\n  margin-bottom: 0.5rem;\\n  font-weight: bold;\\n  font-size: clamp(0.88rem, 0.07vw + 0.86rem, 1rem);\\n  color: #2D2A4B;\\n  word-break: break-all;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["take", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "item_r2", "ɵɵelementContainerStart", "lesson_r3", "ctx_r3", "classroom", "ClassInfoStudentAccordionItemComponent", "constructor", "userService", "generalService", "classroomService", "toastService", "carousel", "items", "mltLevels", "title", "isActive", "student", "selectedLevel", "toggle", "ngOnInit", "console", "log", "activeLevel", "trackById", "index", "item", "id", "updateCarousel", "currentIndex", "_page", "nextIndex", "length", "changeTrialLevel", "pipe", "subscribe", "res", "setShowToastmessage", "severity", "summary", "detail", "getSelectedIndex", "findIndex", "_", "ɵɵdirectiveInject", "i1", "UserService", "i2", "GeneralService", "i3", "ClassroomService", "i4", "ToastService", "_2", "selectors", "viewQuery", "ClassInfoStudentAccordionItemComponent_Query", "rf", "ctx", "ɵɵlistener", "ClassInfoStudentAccordionItemComponent_Template_div_click_1_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "ɵɵprojection", "ɵɵtext", "ɵɵtemplate", "ClassInfoStudentAccordionItemComponent_ng_template_53_Template", "ClassInfoStudentAccordionItemComponent_Template_button_click_55_listener", "ClassInfoStudentAccordionItemComponent_ng_container_60_Template", "ɵɵclassProp", "ɵɵpureFunction2", "_c3", "ɵɵpureFunction1", "_c4", "ɵɵtextInterpolate", "replaceImportedWithDash", "firstName", "lastName", "skype", "username", "ɵɵpipeBind2", "dob", "origin", "ɵɵpipeBind3", "lessons", "ɵɵtextInterpolate1"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\classrooms\\class\\class-info\\components\\class-info-student-accordion-item\\class-info-student-accordion-item.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\classrooms\\class\\class-info\\components\\class-info-student-accordion-item\\class-info-student-accordion-item.component.html"], "sourcesContent": ["import { Component, Input, OnInit, ViewChild } from '@angular/core';\r\nimport { Carousel } from 'primeng/carousel';\r\nimport { take } from 'rxjs/operators';\r\nimport { Classroom } from 'src/app/core/models/classroom.model';\r\nimport { Lesson } from 'src/app/core/models/lesson.model';\r\nimport { User } from 'src/app/core/models/user.model';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { UserService } from 'src/app/core/services/user.service';\r\n\r\n@Component({\r\n  selector: 'app-class-info-student-accordion-item',\r\n  templateUrl: './class-info-student-accordion-item.component.html',\r\n  styleUrls: ['./class-info-student-accordion-item.component.scss']\r\n})\r\nexport class ClassInfoStudentAccordionItemComponent implements OnInit {\r\n  @ViewChild('carousel', { static: false }) carousel: Carousel = {} as Carousel;\r\n  items = this.generalService.mltLevels;\r\n  @Input() title: string = '';\r\n  constructor(\r\n    public userService: UserService,\r\n    public generalService: GeneralService,\r\n    public classroomService: ClassroomService,\r\n    public toastService: ToastService,\r\n  ) { }\r\n  @Input() isActive: boolean = false;\r\n  @Input() student: any = {};\r\n  @Input() classroom: Classroom = {} as Classroom;\r\n  selectedLevel: string = 'A1';\r\n  toggle() {\r\n    this.isActive = !this.isActive;\r\n  }\r\n  ngOnInit(): void {\r\n    console.log(this.classroom);\r\n    this.selectedLevel = this.classroom.activeLevel;\r\n  }  \r\n\r\n  trackById(index: number, item: Lesson): number {\r\n    return +item.id;\r\n  }\r\n\r\n  updateCarousel() {\r\n    // console.log(this.carousel);\r\n    const currentIndex = this.carousel._page - 1;\r\n    const nextIndex = (currentIndex + 1) % this.items.length;\r\n    console.log([this.items[nextIndex]])\r\n    // console.log(currentIndex);\r\n    this.classroomService.changeTrialLevel(this.classroom.id, this.items[nextIndex]).pipe(take(1)).subscribe(res => {\r\n      console.log(res);\r\n      if (res) {\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'success',\r\n          summary: '',\r\n          detail: 'Classroom level has been changed to ' + this.items[nextIndex]\r\n        });\r\n      }\r\n    })\r\n    // this.carousel.value = [this.items[nextIndex]];\r\n  }\r\n\r\n  getSelectedIndex(): number {\r\n    return this.items.findIndex(item => item === this.selectedLevel);\r\n  }\r\n\r\n}\r\n", "<div class=\"accordion border-round-xl\" [class.active]=\"isActive\">\r\n    <div class=\"header\" (click)=\"toggle()\">\r\n        <div class=\"package-header w-100 justify-content-between align-items-center font-xs font-bold\">\r\n            <ng-content select=\"[title]\"></ng-content>\r\n        </div>\r\n        <div class=\"flex\">\r\n            <div class=\"circle ml-1\">\r\n                <i [ngClass]=\"{'pi-chevron-down': !isActive, 'pi-chevron-up': isActive}\" class=\"down-arrow pi\"></i>\r\n            </div>\r\n        </div>\r\n        <!-- <i class=\"pi\" [ngClass]=\"{'pi pi-chevron-down': !isActive, 'pi pi-chevron-up': isActive}\"></i> -->\r\n    </div>\r\n    <div class=\"accordion-content accordion-content-transition\" [ngClass]=\"{'open': isActive}\">\r\n        <div class=\"grid grid-nogutter py-3\">\r\n            <div class=\"col-12 md:col-4\">\r\n                <div class=\"flex flex-row justify-content-center md:justify-content-start\">\r\n                    <!-- <div>\r\n                        <img [src]=\"this.userService.getUserPhoto(student)\" (error)=\"generalService.setDefaultUserAvatar($event)\" class=\"ml-1 mr-2 h-4rem border-circle\">\r\n                    </div> -->\r\n                    <div class=\"flex flex-column md:ml-3\">\r\n                        <!-- <h5 class=\"pb-0 mb-1 text-primary font-bold\"><span>{{student.firstName}}’s Information</span></h5> -->\r\n                        <div class=\"flex gap-2 mb-2\">\r\n                            <div class=\"flex flex-column w-8rem\">\r\n                                <p class=\"person__title\">First Name</p>\r\n                                <p class=\"person__detail\">{{generalService.replaceImportedWithDash(student.firstName)}}</p>\r\n                            </div>\r\n                            <div class=\"flex flex-column\">\r\n                                <p class=\"person__title\">Last Name</p>\r\n                                <p class=\"person__detail\">{{generalService.replaceImportedWithDash(student.lastName)}}</p>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"flex gap-2 mb-2\">\r\n                            <div class=\"flex flex-column\">\r\n                                <p class=\"person__title\">Skype</p>\r\n                                <p class=\"person__detail\">{{generalService.replaceImportedWithDash(student.skype)}}</p>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"flex gap-2 mb-2\">\r\n                            <div class=\"flex flex-column\">\r\n                                <p class=\"person__title\">E-mail</p>\r\n                                <p class=\"person__detail\">{{(student.username)}}</p>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"flex gap-2 mb-2\">\r\n                            <div class=\"flex flex-column w-8rem\">\r\n                                <p class=\"person__title\">Birthday</p>\r\n                                <p class=\"person__detail\">{{(student.dob | date: 'd/M/y')}}</p>\r\n                            </div>\r\n                            <div class=\"flex flex-column\">\r\n                                <p class=\"person__title\">Country</p>\r\n                                <p class=\"person__detail\">{{generalService.replaceImportedWithDash(student.origin)}}</p>\r\n                            </div>\r\n                        </div>\r\n                        \r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"col-12 md:col-4 align-items-center justify-content-center\">\r\n                <p class=\"m-0 person__title text-center\">\r\n                    Choose Level\r\n                </p>\r\n                <div class=\"max-w-11rem my-1 m-auto\">\r\n                    <p-carousel #carousel [value]=\"items\" [page]=\"getSelectedIndex()\" [showIndicators]=\"false\" [numVisible]=\"1\" [numScroll]=\"1\">\r\n                        <ng-template let-item pTemplate=\"item\">\r\n                            <div class=\"flex align-items-center justify-content-center\">\r\n                                <g-level-circle size=\"lg\" [name]=\"item\"></g-level-circle>\r\n                            </div>\r\n                        </ng-template>\r\n                    </p-carousel>\r\n                </div>\r\n                <p class=\"font-2xs text-center mt-2\">\r\n                    <button pButton type=\"button\" label=\"Update\" class=\"p-button-xs\" (click)=\"updateCarousel()\"></button>\r\n                </p>\r\n            </div>\r\n\r\n            <div class=\"col-12 md:col-4\">\r\n                <div class=\"w-full grid mr-2\">\r\n                    <div class=\"col-6 sm:col-6\">\r\n                        <app-mini-lesson-info-card cardType=\"request\" [classroom]=\"classroom\"></app-mini-lesson-info-card>\r\n                    </div>\r\n                    <ng-container *ngFor=\"let lesson of classroom.lessons | slice:0:4; trackBy: trackById\">\r\n                        <div class=\"col-6 sm:col-6\">\r\n                        <app-mini-lesson-info-card cardType=\"lesson\" [lesson]=\"lesson\" [classroom]=\"classroom\"></app-mini-lesson-info-card>\r\n                        </div>\r\n                      </ng-container>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- starts goals -->\r\n        <div class=\"grid mt-2 hidden\">\r\n            <div class=\"col-12 md:col-3 md:col-offset-1\">\r\n                <div class=\"flex flex-column\">\r\n                    <h5 class=\"pb-0 mb-1 text-primary font-bold\"><span>{{student.firstName}}’s Goals</span></h5>\r\n                </div>\r\n                <div class=\"block-gradient border-round-xl p-2\">\r\n                    <h6 class=\"pb-0 mb-1 text-primary font-bold font-sm\"><span>Desired\r\n                            level</span></h6>\r\n                    <div class=\"flex flex-column align-items-center justify-content-center\">\r\n                        <div class=\"flex align-items-center\">\r\n                            <g-level-circle size=\"lg\" [name]=\"'A1'\"></g-level-circle>\r\n                        </div>\r\n                    </div>\r\n                    <h6 class=\"pb-0 mt-1 mb-1 text-primary font-bold font-sm\"><span>Why</span></h6>\r\n                    <p class=\"m-0\">For educational / academic reasons</p>\r\n                    <p class=\"m-0\">For work, to boost my career\r\n                        To obtain a certificate / pass an exam\r\n                    </p>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"md:col-8  flex-column gap-2 hidden\">\r\n\r\n                <div\r\n                    class=\"flex block-gradient py-3 mx-2 gap-3 flex-column sm:flex-row justify-content-center align-items-center border-round-xl\">\r\n\r\n                    <div class=\"circle-stat text-center   text-primary\">\r\n                        <div class=\"flex flex-column align-items-center gap-1\">\r\n                            <div class=\"flex justify-content-center\">\r\n                                <img class=\"rating-icon\" src=\"/assets/icons/goal-sm-icon.svg\" />\r\n                            </div>\r\n                            <span class=\"text-2xl\">37</span>\r\n                            <span class=\"font-xs line-height-1 max-w-4rem\">Reasons for achievemtn</span>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"circle-stat text-center   text-primary\">\r\n                        <div class=\"flex flex-column align-items-center gap-1\">\r\n                            <div class=\"flex justify-content-center\">\r\n                                <img class=\"rating-icon\" src=\"/assets/icons/goal-sm-icon.svg\" />\r\n                            </div>\r\n                            <span class=\"text-2xl\">37</span>\r\n                            <span class=\"font-xs line-height-1 max-w-4rem\">Reasons for achievemtn</span>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"circle-stat text-center   text-primary\">\r\n                        <div class=\"flex flex-column align-items-center gap-1\">\r\n                            <div class=\"flex justify-content-center\">\r\n                                <img class=\"rating-icon\" src=\"/assets/icons/goal-sm-icon.svg\" />\r\n                            </div>\r\n                            <span class=\"text-2xl\">37</span>\r\n                            <span class=\"font-xs line-height-1 max-w-4rem\">Reasons for achievemtn</span>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"circle-stat text-center   text-primary\">\r\n                        <div class=\"flex flex-column align-items-center gap-1\">\r\n                            <div class=\"flex justify-content-center\">\r\n                                <img class=\"rating-icon\" src=\"/assets/icons/goal-sm-icon.svg\" />\r\n                            </div>\r\n                            <span class=\"text-2xl\">37</span>\r\n                            <span class=\"font-xs line-height-1 max-w-4rem\">Reasons for achievemtn</span>\r\n                        </div>\r\n                    </div>\r\n\r\n                </div>\r\n\r\n                <div\r\n                    class=\"flex block-gradient px-2 py-3 mx-2 gap-3 flex-row sm:flex-row justify-content-center align-items-center border-round-xl\">\r\n\r\n                    <div class=\"\">\r\n                        <span class=\"text-primary font-sm line-height-1\">Topic for improvement</span>\r\n                    </div>\r\n                    <div class=\"text-900 w-full md:flex-order-0 flex-order-1\">\r\n                        <p-chip class=\"mr-2 mb-1 class-rating writing border-round-xl\"><span\r\n                                class=\"font-xs  mb-1 text-white\">Writing</span></p-chip>\r\n                        <p-chip class=\"mr-2 mb-1 class-rating speaking border-round-xl\"><span\r\n                                class=\"font-xs  mb-1 text-white\">Speaking</span></p-chip>\r\n                        <p-chip class=\"mr-2 mb-1 class-rating reading border-round-xl\"><span\r\n                                class=\"font-xs  mb-1 text-white\">Reading</span></p-chip>\r\n                        <p-chip class=\"mr-2 mb-1 class-rating listening border-round-xl\"><span\r\n                                class=\"font-xs  mb-1 text-white\">listening</span></p-chip>\r\n                        <p-chip class=\"mr-2 mb-1 class-rating revision border-round-xl\"><span\r\n                                class=\"font-xs  mb-1 text-white\">revision</span></p-chip>\r\n                        <p-chip class=\"mr-2 mb-1 class-rating grammar border-round-xl\"><span\r\n                                class=\"font-xs  mb-1 text-white\">grammar</span></p-chip>\r\n                    </div>\r\n\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <!-- <ng-content select=\"[content]\"></ng-content> -->\r\n    </div>\r\n</div>"], "mappings": "AAEA,SAASA,IAAI,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;IC+DTC,EAAA,CAAAC,cAAA,cAA4D;IACxDD,EAAA,CAAAE,SAAA,yBAAyD;IAC7DF,EAAA,CAAAG,YAAA,EAAM;;;;IADwBH,EAAA,CAAAI,SAAA,EAAa;IAAbJ,EAAA,CAAAK,UAAA,SAAAC,OAAA,CAAa;;;;;IAenDN,EAAA,CAAAO,uBAAA,GAAuF;IACnFP,EAAA,CAAAC,cAAA,cAA4B;IAC5BD,EAAA,CAAAE,SAAA,oCAAmH;IACnHF,EAAA,CAAAG,YAAA,EAAM;;;;;;IADuCH,EAAA,CAAAI,SAAA,GAAiB;IAACJ,EAAlB,CAAAK,UAAA,WAAAG,SAAA,CAAiB,cAAAC,MAAA,CAAAC,SAAA,CAAwB;;;ADnE9G,OAAM,MAAOC,sCAAsC;EAIjDC,YACSC,WAAwB,EACxBC,cAA8B,EAC9BC,gBAAkC,EAClCC,YAA0B;IAH1B,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,YAAY,GAAZA,YAAY;IAPqB,KAAAC,QAAQ,GAAa,EAAc;IAC7E,KAAAC,KAAK,GAAG,IAAI,CAACJ,cAAc,CAACK,SAAS;IAC5B,KAAAC,KAAK,GAAW,EAAE;IAOlB,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,OAAO,GAAQ,EAAE;IACjB,KAAAZ,SAAS,GAAc,EAAe;IAC/C,KAAAa,aAAa,GAAW,IAAI;EAJxB;EAKJC,MAAMA,CAAA;IACJ,IAAI,CAACH,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ;EAChC;EACAI,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACjB,SAAS,CAAC;IAC3B,IAAI,CAACa,aAAa,GAAG,IAAI,CAACb,SAAS,CAACkB,WAAW;EACjD;EAEAC,SAASA,CAACC,KAAa,EAAEC,IAAY;IACnC,OAAO,CAACA,IAAI,CAACC,EAAE;EACjB;EAEAC,cAAcA,CAAA;IACZ;IACA,MAAMC,YAAY,GAAG,IAAI,CAACjB,QAAQ,CAACkB,KAAK,GAAG,CAAC;IAC5C,MAAMC,SAAS,GAAG,CAACF,YAAY,GAAG,CAAC,IAAI,IAAI,CAAChB,KAAK,CAACmB,MAAM;IACxDX,OAAO,CAACC,GAAG,CAAC,CAAC,IAAI,CAACT,KAAK,CAACkB,SAAS,CAAC,CAAC,CAAC;IACpC;IACA,IAAI,CAACrB,gBAAgB,CAACuB,gBAAgB,CAAC,IAAI,CAAC5B,SAAS,CAACsB,EAAE,EAAE,IAAI,CAACd,KAAK,CAACkB,SAAS,CAAC,CAAC,CAACG,IAAI,CAACxC,IAAI,CAAC,CAAC,CAAC,CAAC,CAACyC,SAAS,CAACC,GAAG,IAAG;MAC7Gf,OAAO,CAACC,GAAG,CAACc,GAAG,CAAC;MAChB,IAAIA,GAAG,EAAE;QACP,IAAI,CAACzB,YAAY,CAAC0B,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE,sCAAsC,GAAG,IAAI,CAAC3B,KAAK,CAACkB,SAAS;SACtE,CAAC;MACJ;IACF,CAAC,CAAC;IACF;EACF;EAEAU,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC5B,KAAK,CAAC6B,SAAS,CAAChB,IAAI,IAAIA,IAAI,KAAK,IAAI,CAACR,aAAa,CAAC;EAClE;EAAC,QAAAyB,CAAA,G;qBA/CUrC,sCAAsC,EAAAX,EAAA,CAAAiD,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAnD,EAAA,CAAAiD,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAArD,EAAA,CAAAiD,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAvD,EAAA,CAAAiD,iBAAA,CAAAO,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAtC/C,sCAAsC;IAAAgD,SAAA;IAAAC,SAAA,WAAAC,6CAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;QCf/C9D,EADJ,CAAAC,cAAA,aAAiE,aACtB;QAAnBD,EAAA,CAAAgE,UAAA,mBAAAC,qEAAA;UAAAjE,EAAA,CAAAkE,aAAA,CAAAC,GAAA;UAAA,OAAAnE,EAAA,CAAAoE,WAAA,CAASL,GAAA,CAAAvC,MAAA,EAAQ;QAAA,EAAC;QAClCxB,EAAA,CAAAC,cAAA,aAA+F;QAC3FD,EAAA,CAAAqE,YAAA,GAA0C;QAC9CrE,EAAA,CAAAG,YAAA,EAAM;QAEFH,EADJ,CAAAC,cAAA,aAAkB,aACW;QACrBD,EAAA,CAAAE,SAAA,WAAmG;QAI/GF,EAHQ,CAAAG,YAAA,EAAM,EACJ,EAEJ;QAYsBH,EAX5B,CAAAC,cAAA,aAA2F,aAClD,aACJ,eACkD,eAIjC,eAEL,eACY,aACR;QAAAD,EAAA,CAAAsE,MAAA,kBAAU;QAAAtE,EAAA,CAAAG,YAAA,EAAI;QACvCH,EAAA,CAAAC,cAAA,aAA0B;QAAAD,EAAA,CAAAsE,MAAA,IAA6D;QAC3FtE,EAD2F,CAAAG,YAAA,EAAI,EACzF;QAEFH,EADJ,CAAAC,cAAA,eAA8B,aACD;QAAAD,EAAA,CAAAsE,MAAA,iBAAS;QAAAtE,EAAA,CAAAG,YAAA,EAAI;QACtCH,EAAA,CAAAC,cAAA,aAA0B;QAAAD,EAAA,CAAAsE,MAAA,IAA4D;QAE9FtE,EAF8F,CAAAG,YAAA,EAAI,EACxF,EACJ;QAGEH,EAFR,CAAAC,cAAA,eAA6B,eACK,aACD;QAAAD,EAAA,CAAAsE,MAAA,aAAK;QAAAtE,EAAA,CAAAG,YAAA,EAAI;QAClCH,EAAA,CAAAC,cAAA,aAA0B;QAAAD,EAAA,CAAAsE,MAAA,IAAyD;QAE3FtE,EAF2F,CAAAG,YAAA,EAAI,EACrF,EACJ;QAGEH,EAFR,CAAAC,cAAA,eAA6B,eACK,aACD;QAAAD,EAAA,CAAAsE,MAAA,cAAM;QAAAtE,EAAA,CAAAG,YAAA,EAAI;QACnCH,EAAA,CAAAC,cAAA,aAA0B;QAAAD,EAAA,CAAAsE,MAAA,IAAsB;QAExDtE,EAFwD,CAAAG,YAAA,EAAI,EAClD,EACJ;QAGEH,EAFR,CAAAC,cAAA,eAA6B,eACY,aACR;QAAAD,EAAA,CAAAsE,MAAA,gBAAQ;QAAAtE,EAAA,CAAAG,YAAA,EAAI;QACrCH,EAAA,CAAAC,cAAA,aAA0B;QAAAD,EAAA,CAAAsE,MAAA,IAAiC;;QAC/DtE,EAD+D,CAAAG,YAAA,EAAI,EAC7D;QAEFH,EADJ,CAAAC,cAAA,eAA8B,aACD;QAAAD,EAAA,CAAAsE,MAAA,eAAO;QAAAtE,EAAA,CAAAG,YAAA,EAAI;QACpCH,EAAA,CAAAC,cAAA,aAA0B;QAAAD,EAAA,CAAAsE,MAAA,IAA0D;QAMxGtE,EANwG,CAAAG,YAAA,EAAI,EACtF,EACJ,EAEJ,EACJ,EACJ;QAGFH,EADJ,CAAAC,cAAA,eAAuE,aAC1B;QACrCD,EAAA,CAAAsE,MAAA,sBACJ;QAAAtE,EAAA,CAAAG,YAAA,EAAI;QAEAH,EADJ,CAAAC,cAAA,eAAqC,yBAC2F;QACxHD,EAAA,CAAAuE,UAAA,KAAAC,8DAAA,0BAAuC;QAM/CxE,EADI,CAAAG,YAAA,EAAa,EACX;QAEFH,EADJ,CAAAC,cAAA,aAAqC,kBAC2D;QAA3BD,EAAA,CAAAgE,UAAA,mBAAAS,yEAAA;UAAAzE,EAAA,CAAAkE,aAAA,CAAAC,GAAA;UAAA,OAAAnE,EAAA,CAAAoE,WAAA,CAASL,GAAA,CAAA9B,cAAA,EAAgB;QAAA,EAAC;QAEnGjC,EAFoG,CAAAG,YAAA,EAAS,EACrG,EACF;QAIEH,EAFR,CAAAC,cAAA,cAA6B,eACK,eACE;QACxBD,EAAA,CAAAE,SAAA,qCAAkG;QACtGF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAuE,UAAA,KAAAG,+DAAA,2BAAuF;;QAOnG1E,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;QAMmDH,EAHzD,CAAAC,cAAA,eAA8B,eACmB,eACX,cACmB,YAAM;QAAAD,EAAA,CAAAsE,MAAA,IAA6B;QACpFtE,EADoF,CAAAG,YAAA,EAAO,EAAK,EAC1F;QAEmDH,EADzD,CAAAC,cAAA,eAAgD,cACS,YAAM;QAAAD,EAAA,CAAAsE,MAAA,qBAC9C;QAAOtE,EAAP,CAAAG,YAAA,EAAO,EAAK;QAErBH,EADJ,CAAAC,cAAA,eAAwE,eAC/B;QACjCD,EAAA,CAAAE,SAAA,0BAAyD;QAEjEF,EADI,CAAAG,YAAA,EAAM,EACJ;QACoDH,EAA1D,CAAAC,cAAA,cAA0D,YAAM;QAAAD,EAAA,CAAAsE,MAAA,WAAG;QAAOtE,EAAP,CAAAG,YAAA,EAAO,EAAK;QAC/EH,EAAA,CAAAC,cAAA,aAAe;QAAAD,EAAA,CAAAsE,MAAA,0CAAkC;QAAAtE,EAAA,CAAAG,YAAA,EAAI;QACrDH,EAAA,CAAAC,cAAA,aAAe;QAAAD,EAAA,CAAAsE,MAAA,4EAEf;QAERtE,EAFQ,CAAAG,YAAA,EAAI,EACF,EACJ;QASUH,EAPhB,CAAAC,cAAA,eAAgD,eAGsF,eAE1E,eACO,eACV;QACrCD,EAAA,CAAAE,SAAA,eAAgE;QACpEF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,gBAAuB;QAAAD,EAAA,CAAAsE,MAAA,UAAE;QAAAtE,EAAA,CAAAG,YAAA,EAAO;QAChCH,EAAA,CAAAC,cAAA,gBAA+C;QAAAD,EAAA,CAAAsE,MAAA,8BAAsB;QAE7EtE,EAF6E,CAAAG,YAAA,EAAO,EAC1E,EACJ;QAIEH,EAFR,CAAAC,cAAA,eAAoD,eACO,eACV;QACrCD,EAAA,CAAAE,SAAA,eAAgE;QACpEF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,gBAAuB;QAAAD,EAAA,CAAAsE,MAAA,UAAE;QAAAtE,EAAA,CAAAG,YAAA,EAAO;QAChCH,EAAA,CAAAC,cAAA,gBAA+C;QAAAD,EAAA,CAAAsE,MAAA,8BAAsB;QAE7EtE,EAF6E,CAAAG,YAAA,EAAO,EAC1E,EACJ;QAIEH,EAFR,CAAAC,cAAA,gBAAoD,gBACO,gBACV;QACrCD,EAAA,CAAAE,SAAA,gBAAgE;QACpEF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,iBAAuB;QAAAD,EAAA,CAAAsE,MAAA,WAAE;QAAAtE,EAAA,CAAAG,YAAA,EAAO;QAChCH,EAAA,CAAAC,cAAA,iBAA+C;QAAAD,EAAA,CAAAsE,MAAA,+BAAsB;QAE7EtE,EAF6E,CAAAG,YAAA,EAAO,EAC1E,EACJ;QAIEH,EAFR,CAAAC,cAAA,gBAAoD,gBACO,gBACV;QACrCD,EAAA,CAAAE,SAAA,gBAAgE;QACpEF,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAC,cAAA,iBAAuB;QAAAD,EAAA,CAAAsE,MAAA,WAAE;QAAAtE,EAAA,CAAAG,YAAA,EAAO;QAChCH,EAAA,CAAAC,cAAA,iBAA+C;QAAAD,EAAA,CAAAsE,MAAA,+BAAsB;QAIjFtE,EAJiF,CAAAG,YAAA,EAAO,EAC1E,EACJ,EAEJ;QAMEH,EAJR,CAAAC,cAAA,gBACoI,gBAElH,iBACuC;QAAAD,EAAA,CAAAsE,MAAA,8BAAqB;QAC1EtE,EAD0E,CAAAG,YAAA,EAAO,EAC3E;QAE6DH,EADnE,CAAAC,cAAA,gBAA0D,mBACS,iBACtB;QAAAD,EAAA,CAAAsE,MAAA,gBAAO;QAAOtE,EAAP,CAAAG,YAAA,EAAO,EAAS;QACAH,EAAhE,CAAAC,cAAA,mBAAgE,iBACvB;QAAAD,EAAA,CAAAsE,MAAA,iBAAQ;QAAOtE,EAAP,CAAAG,YAAA,EAAO,EAAS;QACFH,EAA/D,CAAAC,cAAA,mBAA+D,iBACtB;QAAAD,EAAA,CAAAsE,MAAA,gBAAO;QAAOtE,EAAP,CAAAG,YAAA,EAAO,EAAS;QACCH,EAAjE,CAAAC,cAAA,mBAAiE,iBACxB;QAAAD,EAAA,CAAAsE,MAAA,kBAAS;QAAOtE,EAAP,CAAAG,YAAA,EAAO,EAAS;QACFH,EAAhE,CAAAC,cAAA,mBAAgE,iBACvB;QAAAD,EAAA,CAAAsE,MAAA,iBAAQ;QAAOtE,EAAP,CAAAG,YAAA,EAAO,EAAS;QACFH,EAA/D,CAAAC,cAAA,mBAA+D,iBACtB;QAAAD,EAAA,CAAAsE,MAAA,gBAAO;QAQxEtE,EARwE,CAAAG,YAAA,EAAO,EAAS,EAC9D,EAEJ,EACJ,EACJ,EAEJ,EACJ;;;QAzLiCH,EAAA,CAAA2E,WAAA,WAAAZ,GAAA,CAAA1C,QAAA,CAAyB;QAO7CrB,EAAA,CAAAI,SAAA,GAAqE;QAArEJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAA4E,eAAA,KAAAC,GAAA,GAAAd,GAAA,CAAA1C,QAAA,EAAA0C,GAAA,CAAA1C,QAAA,EAAqE;QAKxBrB,EAAA,CAAAI,SAAA,EAA8B;QAA9BJ,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAA8E,eAAA,KAAAC,GAAA,EAAAhB,GAAA,CAAA1C,QAAA,EAA8B;QAYpCrB,EAAA,CAAAI,SAAA,IAA6D;QAA7DJ,EAAA,CAAAgF,iBAAA,CAAAjB,GAAA,CAAAjD,cAAA,CAAAmE,uBAAA,CAAAlB,GAAA,CAAAzC,OAAA,CAAA4D,SAAA,EAA6D;QAI7DlF,EAAA,CAAAI,SAAA,GAA4D;QAA5DJ,EAAA,CAAAgF,iBAAA,CAAAjB,GAAA,CAAAjD,cAAA,CAAAmE,uBAAA,CAAAlB,GAAA,CAAAzC,OAAA,CAAA6D,QAAA,EAA4D;QAM5DnF,EAAA,CAAAI,SAAA,GAAyD;QAAzDJ,EAAA,CAAAgF,iBAAA,CAAAjB,GAAA,CAAAjD,cAAA,CAAAmE,uBAAA,CAAAlB,GAAA,CAAAzC,OAAA,CAAA8D,KAAA,EAAyD;QAMzDpF,EAAA,CAAAI,SAAA,GAAsB;QAAtBJ,EAAA,CAAAgF,iBAAA,CAAAjB,GAAA,CAAAzC,OAAA,CAAA+D,QAAA,CAAsB;QAMtBrF,EAAA,CAAAI,SAAA,GAAiC;QAAjCJ,EAAA,CAAAgF,iBAAA,CAAAhF,EAAA,CAAAsF,WAAA,SAAAvB,GAAA,CAAAzC,OAAA,CAAAiE,GAAA,WAAiC;QAIjCvF,EAAA,CAAAI,SAAA,GAA0D;QAA1DJ,EAAA,CAAAgF,iBAAA,CAAAjB,GAAA,CAAAjD,cAAA,CAAAmE,uBAAA,CAAAlB,GAAA,CAAAzC,OAAA,CAAAkE,MAAA,EAA0D;QAa1ExF,EAAA,CAAAI,SAAA,GAAe;QAAuEJ,EAAtF,CAAAK,UAAA,UAAA0D,GAAA,CAAA7C,KAAA,CAAe,SAAA6C,GAAA,CAAAjB,gBAAA,GAA4B,yBAAyB,iBAAiB,gBAAgB;QAgBzE9C,EAAA,CAAAI,SAAA,GAAuB;QAAvBJ,EAAA,CAAAK,UAAA,cAAA0D,GAAA,CAAArD,SAAA,CAAuB;QAExCV,EAAA,CAAAI,SAAA,EAAkC;QAAAJ,EAAlC,CAAAK,UAAA,YAAAL,EAAA,CAAAyF,WAAA,SAAA1B,GAAA,CAAArD,SAAA,CAAAgF,OAAA,QAAkC,iBAAA3B,GAAA,CAAAlC,SAAA,CAAkB;QAalC7B,EAAA,CAAAI,SAAA,GAA6B;QAA7BJ,EAAA,CAAA2F,kBAAA,KAAA5B,GAAA,CAAAzC,OAAA,CAAA4D,SAAA,kBAA6B;QAO9ClF,EAAA,CAAAI,SAAA,GAAa;QAAbJ,EAAA,CAAAK,UAAA,cAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}