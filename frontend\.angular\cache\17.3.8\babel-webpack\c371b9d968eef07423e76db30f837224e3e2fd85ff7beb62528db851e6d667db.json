{"ast": null, "code": "import { TeacherStatus } from \"../models/teacher.model\";\nimport { map } from \"rxjs/operators\";\nimport { UserStatus } from \"../models/user.model\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport class TeacherGuard {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  canActivate(route, state) {\n    const user = this.authService.getLoggedInUser();\n    const userRole = this.authService.getUserRole();\n    let accept = true;\n    if (userRole && userRole.toLowerCase() === 'teacher') {\n      console.log(state.url);\n      // if (state.url.includes(\"dashboard\")) {\n      //     this.router.navigateByUrl('/teacher/status', { replaceUrl: true });\n      //     accept = false;\n      //     return true;\n      // }\n      // teacher status is active\n      return this.authService.getTeacherStatus().pipe(map(teacherStatus => {\n        if (teacherStatus) {\n          this.authService.setTeacherStatus(teacherStatus);\n          let statusText = teacherStatus.status.toLowerCase();\n          console.log(state.url);\n          // all other urls except teacher urls\n          // TODO remove profile checks and possible move them to another guard service\n          if (!state.url.includes(\"teacher/\")) {\n            if (statusText.toLowerCase() !== TeacherStatus.APPROVED.toLowerCase()) {\n              this.router.navigateByUrl('/teacher/status', {\n                replaceUrl: true\n              });\n              accept = false;\n              return true;\n            }\n            if (user.status.toLowerCase(), UserStatus.ACTIVE.toLowerCase()) {\n              return true;\n            }\n            if (statusText === TeacherStatus.WITHDRAWN.toLowerCase() && !state.url.includes(\"profile\") && !state.url.includes(\":status\")) {\n              this.router.navigateByUrl('/teacher/status', {\n                replaceUrl: true\n              });\n              accept = false;\n              return true;\n            }\n            if (statusText === TeacherStatus.INCOMPLETE.toLowerCase() && !state.url.includes(\"profile\") && state.url.includes(\":status\")) {\n              accept = false;\n              this.router.navigateByUrl('/teacher/status', {\n                replaceUrl: true\n              });\n              return false;\n            }\n            if (statusText === TeacherStatus.INCOMPLETE.toLowerCase() && !state.url.includes(\"profile\") && !state.url.includes(\":status\")) {\n              // return true;\n              this.router.navigateByUrl('/teacher/status', {\n                replaceUrl: true\n              });\n              accept = true;\n              return false;\n            }\n            if (statusText === TeacherStatus.CONSIDERATION.toLowerCase() && !state.url.includes(\"profile\") && !state.url.includes(\":status\")) {\n              this.router.navigateByUrl('/teacher/status', {\n                replaceUrl: true\n              });\n              accept = false;\n              return true;\n            }\n            if (statusText === TeacherStatus.CONSIDERATION.toLowerCase() && !state.url.includes(\":status\")) {\n              accept = true;\n            }\n          } else {\n            return true;\n          }\n        } else {\n          return true;\n        }\n        return accept; //allow to go to router if true\n      }));\n      // let teacherStatus = (await this.authService.getTeacherStatus().toPromise() as TeacherStatusResponse)\n    } else {\n      if (state.url.includes(\"teacher/\")) {\n        accept = true;\n      }\n    }\n    return accept; //allow to go to router if true\n  }\n  static #_ = this.ɵfac = function TeacherGuard_Factory(t) {\n    return new (t || TeacherGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: TeacherGuard,\n    factory: TeacherGuard.ɵfac\n  });\n}", "map": {"version": 3, "names": ["TeacherStatus", "map", "UserStatus", "<PERSON><PERSON><PERSON>", "constructor", "authService", "router", "canActivate", "route", "state", "user", "getLoggedInUser", "userRole", "getUserRole", "accept", "toLowerCase", "console", "log", "url", "getTeacherStatus", "pipe", "teacher<PERSON><PERSON><PERSON>", "setTeacherStatus", "statusText", "status", "includes", "APPROVED", "navigateByUrl", "replaceUrl", "ACTIVE", "WITHDRAWN", "INCOMPLETE", "CONSIDERATION", "_", "i0", "ɵɵinject", "i1", "AuthService", "i2", "Router", "_2", "factory", "ɵfac"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\guards\\teacher.guard.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\";\r\nimport { ActivatedRouteSnapshot, Router, RouterStateSnapshot } from \"@angular/router\";\r\nimport { TeacherStatus, TeacherStatusResponse } from \"../models/teacher.model\";\r\nimport { Observable } from \"rxjs\";\r\nimport { AuthService } from \"../services/auth.service\";\r\nimport { map } from \"rxjs/operators\";\r\nimport { UserStatus } from \"../models/user.model\";\r\n@Injectable()\r\n\r\nexport class TeacherGuard  {\r\n    constructor(\r\n        private authService: AuthService,\r\n        private router: Router,\r\n    ) { }\r\n    canActivate(\r\n        route: ActivatedRouteSnapshot,\r\n        state: RouterStateSnapshot\r\n    ) {\r\n        const user = this.authService.getLoggedInUser();\r\n        const userRole = this.authService.getUserRole();\r\n        let accept: boolean = true;\r\n\r\n        if (userRole && userRole.toLowerCase() === 'teacher') {\r\n\r\n            \r\n            console.log(state.url);\r\n            // if (state.url.includes(\"dashboard\")) {\r\n            //     this.router.navigateByUrl('/teacher/status', { replaceUrl: true });\r\n            //     accept = false;\r\n            //     return true;\r\n            // }\r\n            // teacher status is active\r\n\r\n            return this.authService.getTeacherStatus().pipe(\r\n                map(teacherStatus => {\r\n                    if (teacherStatus) {\r\n                        this.authService.setTeacherStatus(teacherStatus);\r\n                        let statusText = teacherStatus.status.toLowerCase();\r\n\r\n                        console.log(state.url);\r\n                        // all other urls except teacher urls\r\n                        // TODO remove profile checks and possible move them to another guard service\r\n                        if (!state.url.includes(\"teacher/\")) {\r\n                            if (statusText.toLowerCase() !== TeacherStatus.APPROVED.toLowerCase()) {\r\n                                this.router.navigateByUrl('/teacher/status', { replaceUrl: true });\r\n                                accept = false;\r\n                                return true;\r\n                            }\r\n                            if (user.status.toLowerCase(), UserStatus.ACTIVE.toLowerCase()) {\r\n                                return true;\r\n                            }\r\n                            if (statusText === TeacherStatus.WITHDRAWN.toLowerCase() && !state.url.includes(\"profile\") && !state.url.includes(\":status\")) {\r\n                                this.router.navigateByUrl('/teacher/status', { replaceUrl: true });\r\n                                accept = false;\r\n                                return true;\r\n                            }\r\n                            if (statusText === TeacherStatus.INCOMPLETE.toLowerCase() && !state.url.includes(\"profile\") && state.url.includes(\":status\")) {\r\n                                accept = false;\r\n\r\n                                this.router.navigateByUrl('/teacher/status', { replaceUrl: true });\r\n                                return false;\r\n                            }\r\n                            if (statusText === TeacherStatus.INCOMPLETE.toLowerCase() && !state.url.includes(\"profile\") && !state.url.includes(\":status\")) {\r\n                                // return true;\r\n                                this.router.navigateByUrl('/teacher/status', { replaceUrl: true });\r\n                                accept = true;\r\n                                return false;\r\n                            }\r\n                            if (statusText === TeacherStatus.CONSIDERATION.toLowerCase() && !state.url.includes(\"profile\") && !state.url.includes(\":status\")) {\r\n                                this.router.navigateByUrl('/teacher/status', { replaceUrl: true });\r\n                                accept = false;\r\n                                return true;\r\n                            }\r\n                            if (statusText === TeacherStatus.CONSIDERATION.toLowerCase() && !state.url.includes(\":status\")) {\r\n                                accept = true;\r\n                            }\r\n                        } else {\r\n                            return true;\r\n                        }\r\n                    } else {\r\n                        return true;\r\n                    }\r\n                    return accept//allow to go to router if true\r\n                })\r\n            );\r\n\r\n            // let teacherStatus = (await this.authService.getTeacherStatus().toPromise() as TeacherStatusResponse)\r\n\r\n        } else {\r\n            if (state.url.includes(\"teacher/\")) {\r\n                accept = true;\r\n            }\r\n        }\r\n\r\n        return accept//allow to go to router if true\r\n\r\n    }\r\n}    "], "mappings": "AAEA,SAASA,aAAa,QAA+B,yBAAyB;AAG9E,SAASC,GAAG,QAAQ,gBAAgB;AACpC,SAASC,UAAU,QAAQ,sBAAsB;;;;AAGjD,OAAM,MAAOC,YAAY;EACrBC,YACYC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;EACd;EACJC,WAAWA,CACPC,KAA6B,EAC7BC,KAA0B;IAE1B,MAAMC,IAAI,GAAG,IAAI,CAACL,WAAW,CAACM,eAAe,EAAE;IAC/C,MAAMC,QAAQ,GAAG,IAAI,CAACP,WAAW,CAACQ,WAAW,EAAE;IAC/C,IAAIC,MAAM,GAAY,IAAI;IAE1B,IAAIF,QAAQ,IAAIA,QAAQ,CAACG,WAAW,EAAE,KAAK,SAAS,EAAE;MAGlDC,OAAO,CAACC,GAAG,CAACR,KAAK,CAACS,GAAG,CAAC;MACtB;MACA;MACA;MACA;MACA;MACA;MAEA,OAAO,IAAI,CAACb,WAAW,CAACc,gBAAgB,EAAE,CAACC,IAAI,CAC3CnB,GAAG,CAACoB,aAAa,IAAG;QAChB,IAAIA,aAAa,EAAE;UACf,IAAI,CAAChB,WAAW,CAACiB,gBAAgB,CAACD,aAAa,CAAC;UAChD,IAAIE,UAAU,GAAGF,aAAa,CAACG,MAAM,CAACT,WAAW,EAAE;UAEnDC,OAAO,CAACC,GAAG,CAACR,KAAK,CAACS,GAAG,CAAC;UACtB;UACA;UACA,IAAI,CAACT,KAAK,CAACS,GAAG,CAACO,QAAQ,CAAC,UAAU,CAAC,EAAE;YACjC,IAAIF,UAAU,CAACR,WAAW,EAAE,KAAKf,aAAa,CAAC0B,QAAQ,CAACX,WAAW,EAAE,EAAE;cACnE,IAAI,CAACT,MAAM,CAACqB,aAAa,CAAC,iBAAiB,EAAE;gBAAEC,UAAU,EAAE;cAAI,CAAE,CAAC;cAClEd,MAAM,GAAG,KAAK;cACd,OAAO,IAAI;YACf;YACA,IAAIJ,IAAI,CAACc,MAAM,CAACT,WAAW,EAAE,EAAEb,UAAU,CAAC2B,MAAM,CAACd,WAAW,EAAE,EAAE;cAC5D,OAAO,IAAI;YACf;YACA,IAAIQ,UAAU,KAAKvB,aAAa,CAAC8B,SAAS,CAACf,WAAW,EAAE,IAAI,CAACN,KAAK,CAACS,GAAG,CAACO,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAChB,KAAK,CAACS,GAAG,CAACO,QAAQ,CAAC,SAAS,CAAC,EAAE;cAC1H,IAAI,CAACnB,MAAM,CAACqB,aAAa,CAAC,iBAAiB,EAAE;gBAAEC,UAAU,EAAE;cAAI,CAAE,CAAC;cAClEd,MAAM,GAAG,KAAK;cACd,OAAO,IAAI;YACf;YACA,IAAIS,UAAU,KAAKvB,aAAa,CAAC+B,UAAU,CAAChB,WAAW,EAAE,IAAI,CAACN,KAAK,CAACS,GAAG,CAACO,QAAQ,CAAC,SAAS,CAAC,IAAIhB,KAAK,CAACS,GAAG,CAACO,QAAQ,CAAC,SAAS,CAAC,EAAE;cAC1HX,MAAM,GAAG,KAAK;cAEd,IAAI,CAACR,MAAM,CAACqB,aAAa,CAAC,iBAAiB,EAAE;gBAAEC,UAAU,EAAE;cAAI,CAAE,CAAC;cAClE,OAAO,KAAK;YAChB;YACA,IAAIL,UAAU,KAAKvB,aAAa,CAAC+B,UAAU,CAAChB,WAAW,EAAE,IAAI,CAACN,KAAK,CAACS,GAAG,CAACO,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAChB,KAAK,CAACS,GAAG,CAACO,QAAQ,CAAC,SAAS,CAAC,EAAE;cAC3H;cACA,IAAI,CAACnB,MAAM,CAACqB,aAAa,CAAC,iBAAiB,EAAE;gBAAEC,UAAU,EAAE;cAAI,CAAE,CAAC;cAClEd,MAAM,GAAG,IAAI;cACb,OAAO,KAAK;YAChB;YACA,IAAIS,UAAU,KAAKvB,aAAa,CAACgC,aAAa,CAACjB,WAAW,EAAE,IAAI,CAACN,KAAK,CAACS,GAAG,CAACO,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAChB,KAAK,CAACS,GAAG,CAACO,QAAQ,CAAC,SAAS,CAAC,EAAE;cAC9H,IAAI,CAACnB,MAAM,CAACqB,aAAa,CAAC,iBAAiB,EAAE;gBAAEC,UAAU,EAAE;cAAI,CAAE,CAAC;cAClEd,MAAM,GAAG,KAAK;cACd,OAAO,IAAI;YACf;YACA,IAAIS,UAAU,KAAKvB,aAAa,CAACgC,aAAa,CAACjB,WAAW,EAAE,IAAI,CAACN,KAAK,CAACS,GAAG,CAACO,QAAQ,CAAC,SAAS,CAAC,EAAE;cAC5FX,MAAM,GAAG,IAAI;YACjB;UACJ,CAAC,MAAM;YACH,OAAO,IAAI;UACf;QACJ,CAAC,MAAM;UACH,OAAO,IAAI;QACf;QACA,OAAOA,MAAM;MACjB,CAAC,CAAC,CACL;MAED;IAEJ,CAAC,MAAM;MACH,IAAIL,KAAK,CAACS,GAAG,CAACO,QAAQ,CAAC,UAAU,CAAC,EAAE;QAChCX,MAAM,GAAG,IAAI;MACjB;IACJ;IAEA,OAAOA,MAAM;EAEjB;EAAC,QAAAmB,CAAA,G;qBAvFQ9B,YAAY,EAAA+B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAZrC,YAAY;IAAAsC,OAAA,EAAZtC,YAAY,CAAAuC;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}