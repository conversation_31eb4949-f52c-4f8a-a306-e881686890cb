{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nconst _c0 = [[[\"\", \"title\", \"\"]], [[\"\", \"content\", \"\"]]];\nconst _c1 = [\"[title]\", \"[content]\"];\nconst _c2 = (a0, a1) => ({\n  \"pi-chevron-down\": a0,\n  \"pi-chevron-up\": a1\n});\nconst _c3 = a0 => ({\n  \"open\": a0\n});\nexport let PackageAccordionItemComponent = /*#__PURE__*/(() => {\n  class PackageAccordionItemComponent {\n    toggle() {\n      this.isActive = !this.isActive;\n    }\n    constructor() {\n      this.title = '';\n      this.isActive = false;\n    }\n    ngOnInit() {}\n    static #_ = this.ɵfac = function PackageAccordionItemComponent_Factory(t) {\n      return new (t || PackageAccordionItemComponent)();\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PackageAccordionItemComponent,\n      selectors: [[\"app-package-accordion-item\"]],\n      inputs: {\n        title: \"title\"\n      },\n      ngContentSelectors: _c1,\n      decls: 9,\n      vars: 9,\n      consts: [[1, \"accordion\", \"border-round-xl\"], [1, \"header\", 3, \"click\"], [1, \"package-header\", \"w-100\", \"justify-content-between\", \"align-items-center\", \"font-xs\", \"font-bold\"], [1, \"flex\"], [1, \"circle\", \"ml-1\"], [1, \"down-arrow\", \"pi\", 3, \"ngClass\"], [1, \"accordion-content\", \"accordion-content-transition\", 3, \"ngClass\"]],\n      template: function PackageAccordionItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n          i0.ɵɵlistener(\"click\", function PackageAccordionItemComponent_Template_div_click_1_listener() {\n            return ctx.toggle();\n          });\n          i0.ɵɵelementStart(2, \"div\", 2);\n          i0.ɵɵprojection(3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"div\", 4);\n          i0.ɵɵelement(6, \"i\", 5);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"div\", 6);\n          i0.ɵɵprojection(8, 1);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"active\", ctx.isActive);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c2, !ctx.isActive, ctx.isActive));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c3, ctx.isActive));\n        }\n      },\n      dependencies: [i1.NgClass],\n      styles: [\".accordion[_ngcontent-%COMP%]{border:1px solid transparent;border-radius:4px;margin-bottom:10px}.header[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;padding:10px;cursor:pointer}.content[_ngcontent-%COMP%]{padding:10px}.down-arrow[_ngcontent-%COMP%]{transition:transform .2s ease-in-out}.down-arrow.pi-chevron-up[_ngcontent-%COMP%]{transform:rotate(360deg)}.circle[_ngcontent-%COMP%]{background:linear-gradient(to bottom right,#9baaff,#152caf);width:24px;height:24px;border-radius:50%;display:flex;justify-content:center;align-items:center}.circle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{color:#fff;font-size:16px}.rotate[_ngcontent-%COMP%]{transform:rotate(180deg);transition:transform .3s ease-in-out}.accordion-content[_ngcontent-%COMP%]{max-height:0;overflow:hidden;transition:max-height .7s ease-in-out}.open[_ngcontent-%COMP%]{max-height:360px}\"]\n    });\n  }\n  return PackageAccordionItemComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}