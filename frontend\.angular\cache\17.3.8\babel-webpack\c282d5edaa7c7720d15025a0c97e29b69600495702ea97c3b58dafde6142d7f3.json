{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { GuidesRoutingModule } from './guides-routing.module';\nimport { GuidesComponent } from './components/guides/guides.component';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport * as i0 from \"@angular/core\";\nexport class GuidesModule {\n  static #_ = this.ɵfac = function GuidesModule_Factory(t) {\n    return new (t || GuidesModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: GuidesModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, GuidesRoutingModule, SharedModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(GuidesModule, {\n    declarations: [GuidesComponent],\n    imports: [CommonModule, GuidesRoutingModule, SharedModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "GuidesRoutingModule", "GuidesComponent", "SharedModule", "GuidesModule", "_", "_2", "_3", "declarations", "imports"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\guides\\guides.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { GuidesRoutingModule } from './guides-routing.module';\r\nimport { GuidesComponent } from './components/guides/guides.component';\r\nimport { SharedModule } from 'src/app/shared/shared.module';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    GuidesComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    GuidesRoutingModule,\r\n    SharedModule,\r\n  ]\r\n})\r\nexport class GuidesModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,mBAAmB,QAAQ,yBAAyB;AAC7D,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,YAAY,QAAQ,8BAA8B;;AAa3D,OAAM,MAAOC,YAAY;EAAA,QAAAC,CAAA,G;qBAAZD,YAAY;EAAA;EAAA,QAAAE,EAAA,G;UAAZF;EAAY;EAAA,QAAAG,EAAA,G;cALrBP,YAAY,EACZC,mBAAmB,EACnBE,YAAY;EAAA;;;2EAGHC,YAAY;IAAAI,YAAA,GARrBN,eAAe;IAAAO,OAAA,GAGfT,YAAY,EACZC,mBAAmB,EACnBE,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}