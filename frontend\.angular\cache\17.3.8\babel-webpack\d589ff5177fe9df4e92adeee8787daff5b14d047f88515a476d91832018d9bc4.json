{"ast": null, "code": "import { EventEmitter, signal } from '@angular/core';\nimport { PackagesModel, PackageType } from \"src/app/core/models/package.model\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"primeng/radiobutton\";\nconst _c0 = (a0, a1) => ({\n  \"surface-border\": a0,\n  \"border-primary\": a1\n});\nfunction BuyPackageSuggestionBoxComponent_div_0_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function BuyPackageSuggestionBoxComponent_div_0_ng_container_15_Template_div_click_1_listener() {\n      const pkg_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.selectPackageType(pkg_r4.type));\n    });\n    i0.ɵɵelementStart(2, \"p-radioButton\", 34);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuyPackageSuggestionBoxComponent_div_0_ng_container_15_Template_p_radioButton_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedPackageType, $event) || (ctx_r1.selectedPackageType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35)(4, \"div\", 36);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const pkg_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(4, _c0, ctx_r1.selectedPackageType !== pkg_r4.type, ctx_r1.selectedPackage === pkg_r4.type));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", pkg_r4.type);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedPackageType);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(pkg_r4.type);\n  }\n}\nfunction BuyPackageSuggestionBoxComponent_div_0_ng_container_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" Switch to \", (tmp_3_0 = ctx_r1.getSelectedPackage()) == null ? null : tmp_3_0.type, \" Package \");\n  }\n}\nfunction BuyPackageSuggestionBoxComponent_div_0_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" Go Premium for the Ultimate Learning Experience \");\n  }\n}\nfunction BuyPackageSuggestionBoxComponent_div_0_span_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Expires in +\", (tmp_3_0 = ctx_r1.getSelectedPackage()) == null ? null : tmp_3_0.expiresPlus, \"\");\n  }\n}\nfunction BuyPackageSuggestionBoxComponent_div_0_span_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Exclusive Access to our \");\n    i0.ɵɵelementStart(2, \"span\", 37);\n    i0.ɵɵtext(3, \"AI Chat\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuyPackageSuggestionBoxComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"div\", 5)(4, \"div\", 6)(5, \"span\", 7);\n    i0.ɵɵelement(6, \"img\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 9);\n    i0.ɵɵtext(8, \"Consider upgrading?\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 10);\n    i0.ɵɵtext(10, \"And Unlock Exclusive Access to our AI Chat, your personal learning assistant! \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function BuyPackageSuggestionBoxComponent_div_0_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.show.set(false));\n    });\n    i0.ɵɵelement(12, \"span\", 12)(13, \"span\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 14);\n    i0.ɵɵtemplate(15, BuyPackageSuggestionBoxComponent_div_0_ng_container_15_Template, 6, 7, \"ng-container\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 16)(17, \"div\", 17)(18, \"div\", 18);\n    i0.ɵɵtemplate(19, BuyPackageSuggestionBoxComponent_div_0_ng_container_19_Template, 2, 1, \"ng-container\", 19)(20, BuyPackageSuggestionBoxComponent_div_0_ng_template_20_Template, 1, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 20)(23, \"span\", 21);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 22)(26, \"div\", 23)(27, \"ul\", 24)(28, \"li\", 25);\n    i0.ɵɵelement(29, \"img\", 26);\n    i0.ɵɵtemplate(30, BuyPackageSuggestionBoxComponent_div_0_span_30_Template, 2, 1, \"span\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"li\", 25);\n    i0.ɵɵelement(32, \"img\", 26);\n    i0.ɵɵelementStart(33, \"span\");\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"li\", 25);\n    i0.ɵɵelement(36, \"img\", 26);\n    i0.ɵɵtemplate(37, BuyPackageSuggestionBoxComponent_div_0_span_37_Template, 4, 0, \"span\", 27);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(38, \"div\", 28)(39, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function BuyPackageSuggestionBoxComponent_div_0_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.switchBackToPackageType());\n    });\n    i0.ɵɵelement(40, \"span\", 30);\n    i0.ɵɵelementStart(41, \"span\", 31);\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(43, \"span\", 32);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    let tmp_8_0;\n    let tmp_9_0;\n    const notFlexFirstTitle_r5 = i0.ɵɵreference(21);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getPackages());\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r1.getSelectedPackage()) == null ? null : tmp_3_0.type) === ctx_r1.packageTypeEnum.FLEX)(\"ngIfElse\", notFlexFirstTitle_r5);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"+\", (tmp_5_0 = ctx_r1.getSelectedPackage()) == null ? null : tmp_5_0.costPlus, \"\\u20AC\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx_r1.getSelectedPackage()) == null ? null : tmp_6_0.type) != \"Regular\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_7_0 = ctx_r1.getSelectedPackage()) == null ? null : tmp_7_0.cancelation, \" hours cancellation policy \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx_r1.getSelectedPackage()) == null ? null : tmp_8_0.type) != \"Regular\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"Switch to \", (tmp_9_0 = ctx_r1.getSelectedPackage()) == null ? null : tmp_9_0.type, \"\");\n  }\n}\nexport class BuyPackageSuggestionBoxComponent {\n  constructor() {\n    this.show = signal(true);\n    this.defaultPackages = PackagesModel.getDefaultPackages();\n    this.switchBackToPackageTypeSelected = new EventEmitter();\n    this.selectedPackageType = this.defaultPackages[1].type;\n    this.packageTypeEnum = PackageType;\n  }\n  /**\n   * Updates the selected package type by setting `selectedPackageType` to the given type.\n   * @param type The package type to select.\n   */\n  selectPackageType(type) {\n    this.selectedPackageType = type;\n  }\n  getPackages() {\n    return this.defaultPackages.filter(p => p.type !== 'Regular');\n  }\n  getSelectedPackage() {\n    return this.defaultPackages.find(pkg => pkg.type === this.selectedPackageType);\n  }\n  switchBackToPackageType() {\n    this.switchBackToPackageTypeSelected.emit(this.getSelectedPackage());\n  }\n  static #_ = this.ɵfac = function BuyPackageSuggestionBoxComponent_Factory(t) {\n    return new (t || BuyPackageSuggestionBoxComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: BuyPackageSuggestionBoxComponent,\n    selectors: [[\"app-buy-package-suggestion-box\"]],\n    outputs: {\n      switchBackToPackageTypeSelected: \"switchBackToPackageTypeSelected\"\n    },\n    decls: 1,\n    vars: 1,\n    consts: [[\"notFlexFirstTitle\", \"\"], [\"class\", \"\", 4, \"ngIf\"], [1, \"\"], [1, \"shadow-2\", \"border-round\", \"mb-3\", \"h-full\", \"flex-column\", \"justify-content-between\", \"flex\", \"border-round-lg\", \"border-2\", \"bg-blue-50\", \"border-blue-500\"], [1, \"p-3\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"line-height-1\", \"mb-3\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"inline-flex\", \"border-circle\", \"align-items-center\", \"justify-content-center\", \"bg-green-100\", \"mr-3\"], [\"src\", \"/assets/icons/switch-premium.png\", 1, \"w-2rem\"], [1, \"text-900\", \"font-medium\", \"text-2xl\"], [1, \"text-sm\"], [\"pbutton\", \"\", \"pripple\", \"\", \"type\", \"button\", \"icon\", \"pi pi-times text-sm\", 1, \"p-element\", \"p-ripple\", \"w-2rem\", \"h-2rem\", \"p-button-rounded\", \"p-button-secondary\", \"p-button-text\", \"text-600\", \"p-button\", \"p-component\", \"p-button-icon-only\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"p-button-icon\", \"pi\", \"pi-times\", \"text-sm\"], [1, \"p-ink\", 2, \"height\", \"32px\", \"width\", \"32px\", \"top\", \"-5px\", \"left\", \"9.8125px\"], [1, \"flex\", \"flex-column\", \"sm:flex-row\", \"align-items-center\", \"justify-content-between\", \"gap-2\"], [4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"flex-column\", \"justify-content-between\", \"md:flex-row\", \"mt-3\"], [1, \"flex-grow-1\", \"lg:w-min\"], [1, \"text-900\", \"font-bold\", \"text-xl\", \"mb-2\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"flex\", \"align-items-center\", \"w-auto\", \"lg:w-10rem\", \"justify-content-center\", \"lg:justify-content-end\", \"my-3\", \"lg:mt-0\"], [1, \"font-bold\", \"text-2xl\", \"price-blue\"], [1, \"flex\", \"flex-column\", \"align-items-center\", \"justify-space-between\", \"md:flex-row\"], [1, \"flex\", \"flex-column\", \"justify-space-between\", \"md:flex-row\"], [1, \"list-none\", \"p-0\", \"m-0\", \"flex-grow-1\", \"font-sm\", \"font-medium\"], [1, \"flex\", \"align-items-center\", \"my-1\"], [\"src\", \"/assets/images/dashboard/check.svg\", \"alt\", \"\", 1, \"w-1rem\", \"mr-2\"], [4, \"ngIf\"], [1, \"mb-1\", \"mt-2\", \"sm:mt-0\", \"sm:ml-auto\", \"text-right\", \"border-round-lg\", \"align-self-center\", \"sm:align-self-end\"], [\"pbutton\", \"\", \"pripple\", \"\", \"type\", \"button\", \"icon\", \"pi pi-arrow-right\", \"iconpos\", \"right\", \"label\", \"More\", 1, \"p-element\", \"p-ripple\", \"p-button-rounded\", \"blue-gradient-btn\", \"p-button\", \"p-component\", 3, \"click\"], [\"aria-hidden\", \"true\", 1, \"p-button-icon\", \"p-button-icon-right\", \"pi\", \"pi-arrow-right\"], [1, \"p-button-label\"], [1, \"p-ink\"], [1, \"surface-card\", \"border-round-lg\", \"w-full\", \"mx-1\", \"border-2\", \"p-1\", \"line-height-1\", \"flex\", \"align-items-center\", \"cursor-pointer\", \"border-blue-500\", 3, \"click\", \"ngClass\"], [\"name\", \"packageType\", \"styleClass\", \"mr-3\", 3, \"ngModelChange\", \"value\", \"ngModel\"], [1, \"mr-4\", \"md:mr-8\", \"line-height-1\"], [1, \"font-medium\", \"text-base\"], [1, \"font-semibold\"]],\n    template: function BuyPackageSuggestionBoxComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, BuyPackageSuggestionBoxComponent_div_0_Template, 44, 8, \"div\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.show());\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf, i2.NgControlStatus, i2.NgModel, i3.RadioButton],\n    styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n}\\n\\n.price-blue[_ngcontent-%COMP%] {\\n  color: #3345A7;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9jbGFzc3Jvb20vcGFja2FnZXMvYnV5LXBhY2thZ2UvYnV5LXBhY2thZ2Utc3VnZ2VzdGlvbi1ib3gvYnV5LXBhY2thZ2Utc3VnZ2VzdGlvbi1ib3guY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxjQUFjO0FBQ2hCIiwic291cmNlc0NvbnRlbnQiOlsiOmhvc3Qge1xuICBkaXNwbGF5OiBibG9jaztcbn1cblxuLnByaWNlLWJsdWUge1xuICBjb2xvcjogIzMzNDVBNztcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"],\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "signal", "PackagesModel", "PackageType", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵlistener", "BuyPackageSuggestionBoxComponent_div_0_ng_container_15_Template_div_click_1_listener", "pkg_r4", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "selectPackageType", "type", "ɵɵtwoWayListener", "BuyPackageSuggestionBoxComponent_div_0_ng_container_15_Template_p_radioButton_ngModelChange_2_listener", "$event", "ɵɵtwoWayBindingSet", "selectedPackageType", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "selected<PERSON><PERSON><PERSON>", "ɵɵtwoWayProperty", "ɵɵtextInterpolate", "ɵɵtextInterpolate1", "tmp_3_0", "getSelectedPackage", "expiresPlus", "ɵɵelement", "BuyPackageSuggestionBoxComponent_div_0_Template_button_click_11_listener", "_r1", "show", "set", "ɵɵtemplate", "BuyPackageSuggestionBoxComponent_div_0_ng_container_15_Template", "BuyPackageSuggestionBoxComponent_div_0_ng_container_19_Template", "BuyPackageSuggestionBoxComponent_div_0_ng_template_20_Template", "ɵɵtemplateRefExtractor", "BuyPackageSuggestionBoxComponent_div_0_span_30_Template", "BuyPackageSuggestionBoxComponent_div_0_span_37_Template", "BuyPackageSuggestionBoxComponent_div_0_Template_button_click_39_listener", "switchBackToPackageType", "getPackages", "packageTypeEnum", "FLEX", "notFlexFirstTitle_r5", "tmp_5_0", "costPlus", "tmp_6_0", "tmp_7_0", "cancelation", "tmp_8_0", "tmp_9_0", "BuyPackageSuggestionBoxComponent", "constructor", "defaultPackages", "getDefaultPackages", "switchBackToPackageTypeSelected", "filter", "p", "find", "pkg", "emit", "_", "_2", "selectors", "outputs", "decls", "vars", "consts", "template", "BuyPackageSuggestionBoxComponent_Template", "rf", "ctx", "BuyPackageSuggestionBoxComponent_div_0_Template"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\buy-package\\buy-package-suggestion-box\\buy-package-suggestion-box.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\buy-package\\buy-package-suggestion-box\\buy-package-suggestion-box.component.html"], "sourcesContent": ["import { CommonModule } from \"@angular/common\";\nimport { ChangeDetectionStrategy, Component, EventEmitter, Output, signal } from '@angular/core';\nimport { PackagesModel, PackageType } from \"src/app/core/models/package.model\";\n\n@Component({\n    selector: 'app-buy-package-suggestion-box',\n    templateUrl: './buy-package-suggestion-box.component.html',\n    styleUrl: './buy-package-suggestion-box.component.css',\n    changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class BuyPackageSuggestionBoxComponent {\n\n    show = signal(true);\n    public defaultPackages = PackagesModel.getDefaultPackages();\n\n    @Output() switchBackToPackageTypeSelected = new EventEmitter<any>();\n\n    selectedPackageType = this.defaultPackages[1].type;\n    packageTypeEnum = PackageType;\n\n    \n    /**\n     * Updates the selected package type by setting `selectedPackageType` to the given type.\n     * @param type The package type to select.\n     */\n\n    public selectPackageType(type: any) {\n        this.selectedPackageType = type;\n    }\n\n    getPackages() {\n        return this.defaultPackages.filter(p => p.type !== 'Regular');\n    }\n\n    public getSelectedPackage() {\n        return this.defaultPackages.find(pkg => pkg.type === this.selectedPackageType);\n    }\n\n    switchBackToPackageType() {\n        this.switchBackToPackageTypeSelected.emit(this.getSelectedPackage());\n    }\n}\n", "\r\n<div class=\"\" *ngIf=\"show()\">\r\n    <div class=\"shadow-2 border-round  mb-3 h-full flex-column justify-content-between flex border-round-lg border-2 \r\n    bg-blue-50 border-blue-500\">\r\n        <div class=\"p-3\">\r\n            <div class=\"flex align-items-center justify-content-between line-height-1 mb-3\">\r\n                <div class=\"flex align-items-center justify-content-center \">\r\n                    <span class=\"inline-flex border-circle align-items-center justify-content-center bg-green-100 mr-3\">\r\n                        <img src=\"/assets/icons/switch-premium.png\" class=\"w-2rem\">\r\n                    </span>\r\n                    <span class=\"text-900 font-medium text-2xl\">Consider upgrading?</span>\r\n                    <span class=\"text-sm\">And Unlock Exclusive Access to our AI Chat, your personal learning assistant! </span>\r\n                </div>\r\n\r\n                <button pbutton=\"\" (click)=\"show.set(false)\" pripple=\"\" type=\"button\" icon=\"pi pi-times text-sm\"\r\n                    class=\"p-element p-ripple w-2rem h-2rem p-button-rounded p-button-secondary p-button-text text-600 p-button p-component p-button-icon-only\"><span\r\n                        class=\"p-button-icon pi pi-times text-sm\" aria-hidden=\"true\"></span><span class=\"p-ink\"\r\n                        style=\"height: 32px; width: 32px; top: -5px; left: 9.8125px;\"></span></button>\r\n            </div>\r\n\r\n\r\n\r\n            <div class=\"flex flex-column sm:flex-row align-items-center justify-content-between gap-2\">\r\n                <ng-container *ngFor=\"let pkg of getPackages(); let i = index\">\r\n                    <div class=\"surface-card border-round-lg w-full mx-1 border-2 p-1 line-height-1 flex \r\n                    align-items-center cursor-pointer border-blue-500\"\r\n                        [ngClass]=\"{'surface-border': selectedPackageType !== pkg.type, 'border-primary': selectedPackage === pkg.type}\"\r\n                        (click)=\"selectPackageType(pkg.type)\">\r\n                        <p-radioButton name=\"packageType\" [value]=\"pkg.type\" [(ngModel)]=\"selectedPackageType\"\r\n                            styleClass=\"mr-3\"></p-radioButton>\r\n                        <div class=\"mr-4 md:mr-8 line-height-1\">\r\n                            <div class=\"font-medium text-base\">{{ pkg.type }}</div>\r\n                            <!-- <span class=\"text-sm\">Cost: {{ pkg.costPlus }} USD</span> -->\r\n                        </div>\r\n                        <!-- <div class=\"border-round border-1 p-1 ml-auto flex w-min\"\r\n                            [ngClass]=\"{'bg-green-400 text-green-900': selectedPackageType !== pkg.type, 'bg-primary-reverse': selectedPackageType === pkg.type}\">\r\n                            \r\n                            <span class=\" font-bold font-2xs py-1 px-2\" style=\"border-radius: 10px;\">Best Value</span>\r\n                        </div> -->\r\n                    </div>\r\n                </ng-container>\r\n            </div>\r\n\r\n            <div class=\"flex flex-column justify-content-between md:flex-row mt-3\">\r\n                <div class=\"flex-grow-1 lg:w-min\">\r\n                    <div class=\"text-900 font-bold text-xl mb-2\">\r\n\r\n                        <ng-container\r\n                            *ngIf=\"getSelectedPackage()?.type === packageTypeEnum.FLEX; else notFlexFirstTitle\">\r\n\r\n                            Switch to {{ getSelectedPackage()?.type }} Package\r\n\r\n                        </ng-container>\r\n\r\n                        <ng-template #notFlexFirstTitle>\r\n                            Go Premium for the Ultimate Learning Experience\r\n                        </ng-template>\r\n\r\n                    </div>\r\n                    <!-- <div class=\"text-500 text-xl\">Id diam vel quam elementum.</div> -->\r\n                </div>\r\n                <div class=\"flex align-items-center w-auto lg:w-10rem justify-content-center lg:justify-content-end my-3 lg:mt-0\">\r\n                    <span class=\"font-bold text-2xl price-blue\">+{{ getSelectedPackage()?.costPlus }}€</span>\r\n                    <!-- <span class=\"text-700 text-xl ml-2 line-height-3\">per month</span> -->\r\n                </div>\r\n            </div>\r\n\r\n            <!-- <div class=\"text-900 my-3 text-xl font-medium\">{{getSelectedPackage()?.costPlus}} €.</div>\r\n            <p class=\"mt-0 mb-3 text-700 line-height-3\">\r\n                Eget sit amet tellus cras adipiscing enim. At quis risus sed vulputate odio. Proin libero nunc consequat\r\n                interdum varius sit amet.\r\n            </p> -->\r\n\r\n            <!-- <div class=\"text-900 font-medium text-xl pb-1\">Features</div> -->\r\n\r\n\r\n            <div class=\"flex flex-column align-items-center justify-space-between md:flex-row\">\r\n\r\n                <div class=\"flex flex-column justify-space-between md:flex-row\">\r\n                    <ul class=\"list-none p-0 m-0 flex-grow-1 font-sm font-medium\">\r\n                        <li class=\"flex align-items-center my-1\"><img src=\"/assets/images/dashboard/check.svg\" alt=\"\"\r\n                                class=\"w-1rem mr-2\">\r\n                            <span *ngIf=\"getSelectedPackage()?.type!='Regular'\">Expires in\r\n                                +{{getSelectedPackage()?.expiresPlus}}</span>\r\n                        </li>\r\n                        <li class=\"flex align-items-center my-1\"><img src=\"/assets/images/dashboard/check.svg\" alt=\"\"\r\n                                class=\"w-1rem mr-2\">\r\n                            <span>\r\n                                {{getSelectedPackage()?.cancelation}} hours cancellation policy\r\n                            </span>\r\n                        </li>\r\n                        <li class=\"flex align-items-center my-1\"><img src=\"/assets/images/dashboard/check.svg\" alt=\"\"\r\n                                class=\"w-1rem mr-2\">\r\n                            <span *ngIf=\"getSelectedPackage()?.type!='Regular'\">Exclusive Access to our <span class=\"font-semibold\">AI Chat</span></span>\r\n                        </li>\r\n                    </ul>\r\n                </div>\r\n\r\n                <div class=\"mb-1 mt-2 sm:mt-0 sm:ml-auto text-right border-round-lg align-self-center sm:align-self-end\">\r\n                    <button pbutton=\"\" pripple=\"\" (click)=\"switchBackToPackageType()\" type=\"button\"\r\n                        icon=\"pi pi-arrow-right\" iconpos=\"right\" label=\"More\"\r\n                        class=\"p-element p-ripple p-button-rounded blue-gradient-btn p-button p-component\">\r\n                        <span class=\"p-button-icon p-button-icon-right pi pi-arrow-right\" aria-hidden=\"true\"></span>\r\n                        <span class=\"p-button-label\">Switch to {{ getSelectedPackage()?.type }}</span>\r\n                        <span class=\"p-ink\"></span>\r\n                    </button>\r\n                </div>\r\n\r\n            </div>\r\n\r\n        </div>\r\n\r\n    </div>\r\n</div>"], "mappings": "AACA,SAA6CA,YAAY,EAAUC,MAAM,QAAQ,eAAe;AAChG,SAASC,aAAa,EAAEC,WAAW,QAAQ,mCAAmC;;;;;;;;;;;;ICqB9DC,EAAA,CAAAC,uBAAA,GAA+D;IAC3DD,EAAA,CAAAE,cAAA,cAG0C;IAAtCF,EAAA,CAAAG,UAAA,mBAAAC,qFAAA;MAAA,MAAAC,MAAA,GAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,iBAAA,CAAAP,MAAA,CAAAQ,IAAA,CAA2B;IAAA,EAAC;IACrCb,EAAA,CAAAE,cAAA,wBACsB;IAD+BF,EAAA,CAAAc,gBAAA,2BAAAC,uGAAAC,MAAA;MAAAhB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAiB,kBAAA,CAAAR,MAAA,CAAAS,mBAAA,EAAAF,MAAA,MAAAP,MAAA,CAAAS,mBAAA,GAAAF,MAAA;MAAA,OAAAhB,EAAA,CAAAW,WAAA,CAAAK,MAAA;IAAA,EAAiC;IAChEhB,EAAA,CAAAmB,YAAA,EAAgB;IAElCnB,EADJ,CAAAE,cAAA,cAAwC,cACD;IAAAF,EAAA,CAAAoB,MAAA,GAAc;IAQzDpB,EARyD,CAAAmB,YAAA,EAAM,EAErD,EAMJ;;;;;;IAbFnB,EAAA,CAAAqB,SAAA,EAAgH;IAAhHrB,EAAA,CAAAsB,UAAA,YAAAtB,EAAA,CAAAuB,eAAA,IAAAC,GAAA,EAAAf,MAAA,CAAAS,mBAAA,KAAAb,MAAA,CAAAQ,IAAA,EAAAJ,MAAA,CAAAgB,eAAA,KAAApB,MAAA,CAAAQ,IAAA,EAAgH;IAE9Eb,EAAA,CAAAqB,SAAA,EAAkB;IAAlBrB,EAAA,CAAAsB,UAAA,UAAAjB,MAAA,CAAAQ,IAAA,CAAkB;IAACb,EAAA,CAAA0B,gBAAA,YAAAjB,MAAA,CAAAS,mBAAA,CAAiC;IAG/ClB,EAAA,CAAAqB,SAAA,GAAc;IAAdrB,EAAA,CAAA2B,iBAAA,CAAAtB,MAAA,CAAAQ,IAAA,CAAc;;;;;IAgBrDb,EAAA,CAAAC,uBAAA,GACwF;IAEpFD,EAAA,CAAAoB,MAAA,GAEJ;;;;;;IAFIpB,EAAA,CAAAqB,SAAA,EAEJ;IAFIrB,EAAA,CAAA4B,kBAAA,iBAAAC,OAAA,GAAApB,MAAA,CAAAqB,kBAAA,qBAAAD,OAAA,CAAAhB,IAAA,cAEJ;;;;;IAGIb,EAAA,CAAAoB,MAAA,wDACJ;;;;;IA0BIpB,EAAA,CAAAE,cAAA,WAAoD;IAAAF,EAAA,CAAAoB,MAAA,GACV;IAAApB,EAAA,CAAAmB,YAAA,EAAO;;;;;IADGnB,EAAA,CAAAqB,SAAA,EACV;IADUrB,EAAA,CAAA4B,kBAAA,kBAAAC,OAAA,GAAApB,MAAA,CAAAqB,kBAAA,qBAAAD,OAAA,CAAAE,WAAA,KACV;;;;;IAU1C/B,EAAA,CAAAE,cAAA,WAAoD;IAAAF,EAAA,CAAAoB,MAAA,+BAAwB;IAAApB,EAAA,CAAAE,cAAA,eAA4B;IAAAF,EAAA,CAAAoB,MAAA,cAAO;IAAOpB,EAAP,CAAAmB,YAAA,EAAO,EAAO;;;;;;IAtFrInB,EANpB,CAAAE,cAAA,aAA6B,aAEG,aACP,aACmE,aACf,cAC2C;IAChGF,EAAA,CAAAgC,SAAA,aAA2D;IAC/DhC,EAAA,CAAAmB,YAAA,EAAO;IACPnB,EAAA,CAAAE,cAAA,cAA4C;IAAAF,EAAA,CAAAoB,MAAA,0BAAmB;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IACtEnB,EAAA,CAAAE,cAAA,eAAsB;IAAAF,EAAA,CAAAoB,MAAA,sFAA8E;IACxGpB,EADwG,CAAAmB,YAAA,EAAO,EACzG;IAENnB,EAAA,CAAAE,cAAA,kBACgJ;IAD7HF,EAAA,CAAAG,UAAA,mBAAA8B,yEAAA;MAAAjC,EAAA,CAAAM,aAAA,CAAA4B,GAAA;MAAA,MAAAzB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA0B,IAAA,CAAAC,GAAA,CAAS,KAAK,CAAC;IAAA,EAAC;IAEgCpC,EADoE,CAAAgC,SAAA,gBACpE,gBACC;IACjFhC,EADiF,CAAAmB,YAAA,EAAS,EACpF;IAINnB,EAAA,CAAAE,cAAA,eAA2F;IACvFF,EAAA,CAAAqC,UAAA,KAAAC,+DAAA,2BAA+D;IAkBnEtC,EAAA,CAAAmB,YAAA,EAAM;IAIEnB,EAFR,CAAAE,cAAA,eAAuE,eACjC,eACe;IASzCF,EAPA,CAAAqC,UAAA,KAAAE,+DAAA,2BACwF,KAAAC,8DAAA,gCAAAxC,EAAA,CAAAyC,sBAAA,CAMxD;IAMxCzC,EAFI,CAAAmB,YAAA,EAAM,EAEJ;IAEFnB,EADJ,CAAAE,cAAA,eAAkH,gBAClE;IAAAF,EAAA,CAAAoB,MAAA,IAAsC;IAG1FpB,EAH0F,CAAAmB,YAAA,EAAO,EAEvF,EACJ;IAeMnB,EAJZ,CAAAE,cAAA,eAAmF,eAEf,cACE,cACjB;IAAAF,EAAA,CAAAgC,SAAA,eACb;IACxBhC,EAAA,CAAAqC,UAAA,KAAAK,uDAAA,mBAAoD;IAExD1C,EAAA,CAAAmB,YAAA,EAAK;IACLnB,EAAA,CAAAE,cAAA,cAAyC;IAAAF,EAAA,CAAAgC,SAAA,eACb;IACxBhC,EAAA,CAAAE,cAAA,YAAM;IACFF,EAAA,CAAAoB,MAAA,IACJ;IACJpB,EADI,CAAAmB,YAAA,EAAO,EACN;IACLnB,EAAA,CAAAE,cAAA,cAAyC;IAAAF,EAAA,CAAAgC,SAAA,eACb;IACxBhC,EAAA,CAAAqC,UAAA,KAAAM,uDAAA,mBAAoD;IAGhE3C,EAFQ,CAAAmB,YAAA,EAAK,EACJ,EACH;IAGFnB,EADJ,CAAAE,cAAA,eAAyG,kBAGd;IAFzDF,EAAA,CAAAG,UAAA,mBAAAyC,yEAAA;MAAA5C,EAAA,CAAAM,aAAA,CAAA4B,GAAA;MAAA,MAAAzB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAoC,uBAAA,EAAyB;IAAA,EAAC;IAG7D7C,EAAA,CAAAgC,SAAA,gBAA4F;IAC5FhC,EAAA,CAAAE,cAAA,gBAA6B;IAAAF,EAAA,CAAAoB,MAAA,IAA0C;IAAApB,EAAA,CAAAmB,YAAA,EAAO;IAC9EnB,EAAA,CAAAgC,SAAA,gBAA2B;IASnDhC,EARoB,CAAAmB,YAAA,EAAS,EACP,EAEJ,EAEJ,EAEJ,EACJ;;;;;;;;;;;IA1FwCnB,EAAA,CAAAqB,SAAA,IAAkB;IAAlBrB,EAAA,CAAAsB,UAAA,YAAAb,MAAA,CAAAqC,WAAA,GAAkB;IAyBnC9C,EAAA,CAAAqB,SAAA,GAA2D;IAAArB,EAA3D,CAAAsB,UAAA,WAAAO,OAAA,GAAApB,MAAA,CAAAqB,kBAAA,qBAAAD,OAAA,CAAAhB,IAAA,MAAAJ,MAAA,CAAAsC,eAAA,CAAAC,IAAA,CAA2D,aAAAC,oBAAA,CAAsB;IAc9CjD,EAAA,CAAAqB,SAAA,GAAsC;IAAtCrB,EAAA,CAAA4B,kBAAA,OAAAsB,OAAA,GAAAzC,MAAA,CAAAqB,kBAAA,qBAAAoB,OAAA,CAAAC,QAAA,WAAsC;IAoBnEnD,EAAA,CAAAqB,SAAA,GAA2C;IAA3CrB,EAAA,CAAAsB,UAAA,WAAA8B,OAAA,GAAA3C,MAAA,CAAAqB,kBAAA,qBAAAsB,OAAA,CAAAvC,IAAA,eAA2C;IAM9Cb,EAAA,CAAAqB,SAAA,GACJ;IADIrB,EAAA,CAAA4B,kBAAA,OAAAyB,OAAA,GAAA5C,MAAA,CAAAqB,kBAAA,qBAAAuB,OAAA,CAAAC,WAAA,gCACJ;IAIOtD,EAAA,CAAAqB,SAAA,GAA2C;IAA3CrB,EAAA,CAAAsB,UAAA,WAAAiC,OAAA,GAAA9C,MAAA,CAAAqB,kBAAA,qBAAAyB,OAAA,CAAA1C,IAAA,eAA2C;IAUzBb,EAAA,CAAAqB,SAAA,GAA0C;IAA1CrB,EAAA,CAAA4B,kBAAA,gBAAA4B,OAAA,GAAA/C,MAAA,CAAAqB,kBAAA,qBAAA0B,OAAA,CAAA3C,IAAA,KAA0C;;;AD7F/F,OAAM,MAAO4C,gCAAgC;EAN7CC,YAAA;IAQI,KAAAvB,IAAI,GAAGtC,MAAM,CAAC,IAAI,CAAC;IACZ,KAAA8D,eAAe,GAAG7D,aAAa,CAAC8D,kBAAkB,EAAE;IAEjD,KAAAC,+BAA+B,GAAG,IAAIjE,YAAY,EAAO;IAEnE,KAAAsB,mBAAmB,GAAG,IAAI,CAACyC,eAAe,CAAC,CAAC,CAAC,CAAC9C,IAAI;IAClD,KAAAkC,eAAe,GAAGhD,WAAW;;EAG7B;;;;EAKOa,iBAAiBA,CAACC,IAAS;IAC9B,IAAI,CAACK,mBAAmB,GAAGL,IAAI;EACnC;EAEAiC,WAAWA,CAAA;IACP,OAAO,IAAI,CAACa,eAAe,CAACG,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClD,IAAI,KAAK,SAAS,CAAC;EACjE;EAEOiB,kBAAkBA,CAAA;IACrB,OAAO,IAAI,CAAC6B,eAAe,CAACK,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACpD,IAAI,KAAK,IAAI,CAACK,mBAAmB,CAAC;EAClF;EAEA2B,uBAAuBA,CAAA;IACnB,IAAI,CAACgB,+BAA+B,CAACK,IAAI,CAAC,IAAI,CAACpC,kBAAkB,EAAE,CAAC;EACxE;EAAC,QAAAqC,CAAA,G;qBA9BQV,gCAAgC;EAAA;EAAA,QAAAW,EAAA,G;UAAhCX,gCAAgC;IAAAY,SAAA;IAAAC,OAAA;MAAAT,+BAAA;IAAA;IAAAU,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0CAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCT7C5E,EAAA,CAAAqC,UAAA,IAAAyC,+CAAA,kBAA6B;;;QAAd9E,EAAA,CAAAsB,UAAA,SAAAuD,GAAA,CAAA1C,IAAA,GAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}