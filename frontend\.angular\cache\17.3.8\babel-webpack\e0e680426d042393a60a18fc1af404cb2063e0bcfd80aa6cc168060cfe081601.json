{"ast": null, "code": "import { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/core/services/toast.service\";\nimport * as i3 from \"src/app/core/services/teacher-application.service\";\nimport * as i4 from \"src/app/core/services/general.service\";\nimport * as i5 from \"src/app/core/services/auth.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"primeng/ripple\";\nfunction ReviewComponent_div_4_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.teacherApplication.step1.countryOrigin.name, \" \");\n  }\n}\nfunction ReviewComponent_div_4_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.teacherApplication.step1.countryResidency.name, \" \");\n  }\n}\nfunction ReviewComponent_div_4_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const nativeLanguage_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", nativeLanguage_r2[\"native\"].name, \" \");\n  }\n}\nfunction ReviewComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"div\", 11);\n    i0.ɵɵtext(3, \"First Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 12);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 10)(7, \"div\", 11);\n    i0.ɵɵtext(8, \"Last Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 12);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 13)(12, \"div\", 11);\n    i0.ɵɵtext(13, \"Preffered Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 12);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 10)(17, \"div\", 11);\n    i0.ɵɵtext(18, \"Country of Origin\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, ReviewComponent_div_4_div_19_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 10)(21, \"div\", 11);\n    i0.ɵɵtext(22, \"Country of Residency\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, ReviewComponent_div_4_div_23_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 10)(25, \"div\", 11);\n    i0.ɵɵtext(26, \"Date of Birth\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 12);\n    i0.ɵɵtext(28);\n    i0.ɵɵpipe(29, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 10)(31, \"div\", 11);\n    i0.ɵɵtext(32, \"Timezone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 12);\n    i0.ɵɵtext(34);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(35, \"div\", 15);\n    i0.ɵɵelementStart(36, \"div\", 16)(37, \"div\", 17);\n    i0.ɵɵtext(38, \"Native Languages\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 18);\n    i0.ɵɵtemplate(40, ReviewComponent_div_4_div_40_Template, 2, 1, \"div\", 19);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.teacherApplication.step1.firstName, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.teacherApplication.step1.lastName, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.teacherApplication.step1.prefferedName, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.teacherApplication.step1.countryOrigin);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.teacherApplication.step1.countryResidency);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(29, 8, ctx_r0.teacherApplication.step1.birth, \"dd-MM-yyy\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.teacherApplication.step1.timeZone, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.teacherApplication.step1.nativeLanguages);\n  }\n}\nfunction ReviewComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 21)(2, \"div\", 22)(3, \"div\", 23);\n    i0.ɵɵtext(4, \"E-mail\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 12);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 24)(8, \"div\", 23);\n    i0.ɵɵtext(9, \"Phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 12);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 25)(13, \"div\", 23);\n    i0.ɵɵtext(14, \"Skype\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 12);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.teacherApplication.step1.email, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r0.teacherApplication.step1.phoneCode, \" \", ctx_r0.teacherApplication.step1.phone, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.teacherApplication.step1.skype, \" \");\n  }\n}\nfunction ReviewComponent_div_12_div_1_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵelement(1, \"img\", 31);\n    i0.ɵɵelementStart(2, \"span\", 32);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r3 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.generalService.getFileName2(file_r3.filePath));\n  }\n}\nfunction ReviewComponent_div_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 24)(2, \"div\", 23);\n    i0.ɵɵtext(3, \"Degree\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 12);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 24)(7, \"div\", 23);\n    i0.ɵɵtext(8, \"Institute\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 12);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 24)(12, \"div\", 23);\n    i0.ɵɵtext(13, \"Degree / Diploma\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 12);\n    i0.ɵɵtemplate(15, ReviewComponent_div_12_div_1_div_15_Template, 4, 1, \"div\", 29);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const education_r4 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", education_r4.name, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", education_r4.institute, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", education_r4.fileUploads);\n  }\n}\nfunction ReviewComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtemplate(1, ReviewComponent_div_12_div_1_Template, 16, 3, \"div\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.teacherApplication.step2.education);\n  }\n}\nfunction ReviewComponent_div_16_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 13);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const experience_r5 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", experience_r5.language.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", experience_r5.level.levelName, \" \");\n  }\n}\nfunction ReviewComponent_div_16_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"div\", 13);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 13);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const experience_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", experience_r6.language.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", experience_r6.level.levelName, \" \");\n  }\n}\nfunction ReviewComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 34)(2, \"div\", 23);\n    i0.ɵɵtext(3, \"Teaching Language\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ReviewComponent_div_16_div_4_Template, 5, 2, \"div\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 34)(6, \"div\", 23);\n    i0.ɵɵtext(7, \"Speaking Language\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, ReviewComponent_div_16_div_8_Template, 5, 2, \"div\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 15)(10, \"div\", 36)(11, \"div\", 23);\n    i0.ɵɵtext(12, \"Years of teaching experience\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 12);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 36)(16, \"div\", 23);\n    i0.ɵɵtext(17, \"Teaching Method\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 12);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.teacherApplication.step3.languageTeach);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.teacherApplication.step3.languageSpeak);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.teacherApplication.step3.yearsExperience, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.teacherApplication.step3.teachingMethods, \" \");\n  }\n}\nfunction ReviewComponent_div_17_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"a\", 43);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"href\", ctx_r0.generalService.getDomainFileNamePath(ctx_r0.teacherApplication.step4.cv), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.generalService.getFileNameAndExtension(ctx_r0.teacherApplication.step4.cv), \" \");\n  }\n}\nfunction ReviewComponent_div_17_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"a\", 43);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"href\", ctx_r0.generalService.getDomainFileNamePath(ctx_r0.teacherApplication.step4.profilePicture), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.generalService.getFileNameAndExtension(ctx_r0.teacherApplication.step4.profilePicture), \"\");\n  }\n}\nfunction ReviewComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n    i0.ɵɵtext(2, \"Resume\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 38)(4, \"div\", 39)(5, \"div\", 23);\n    i0.ɵɵtext(6, \"CV\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 40)(8, \"div\");\n    i0.ɵɵelement(9, \"img\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, ReviewComponent_div_17_div_10_Template, 3, 2, \"div\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 39)(12, \"div\", 23);\n    i0.ɵɵtext(13, \"Photo\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 40)(15, \"div\");\n    i0.ɵɵelement(16, \"img\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, ReviewComponent_div_17_div_17_Template, 3, 2, \"div\", 8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.teacherApplication.step4.cv);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.teacherApplication.step4.profilePicture);\n  }\n}\nfunction ReviewComponent_div_21_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 49);\n    i0.ɵɵtext(2, \"Monday\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const available_r7 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", available_r7.from, \" - \", available_r7.to, \" \");\n  }\n}\nfunction ReviewComponent_div_21_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 49);\n    i0.ɵɵtext(2, \"Tuesday\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const available_r8 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", available_r8.from, \" - \", available_r8.to, \" \");\n  }\n}\nfunction ReviewComponent_div_21_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 49);\n    i0.ɵɵtext(2, \"Wednesday\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const available_r9 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", available_r9.from, \" - \", available_r9.to, \" \");\n  }\n}\nfunction ReviewComponent_div_21_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 49);\n    i0.ɵɵtext(2, \"Thursday\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const available_r10 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", available_r10.from, \" - \", available_r10.to, \" \");\n  }\n}\nfunction ReviewComponent_div_21_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 49);\n    i0.ɵɵtext(2, \"Friday\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const available_r11 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", available_r11.from, \" - \", available_r11.to, \" \");\n  }\n}\nfunction ReviewComponent_div_21_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 49);\n    i0.ɵɵtext(2, \"Saturday\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const available_r12 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", available_r12.from, \" - \", available_r12.to, \" \");\n  }\n}\nfunction ReviewComponent_div_21_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"div\", 49);\n    i0.ɵɵtext(2, \"Sunday\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 49);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const available_r13 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", available_r13.from, \" - \", available_r13.to, \" \");\n  }\n}\nfunction ReviewComponent_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 44)(2, \"div\", 45)(3, \"div\", 46)(4, \"div\", 13);\n    i0.ɵɵtext(5, \"Days\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 13);\n    i0.ɵɵtext(7, \"Time\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, ReviewComponent_div_21_div_8_Template, 5, 2, \"div\", 47)(9, ReviewComponent_div_21_div_9_Template, 5, 2, \"div\", 47)(10, ReviewComponent_div_21_div_10_Template, 5, 2, \"div\", 47)(11, ReviewComponent_div_21_div_11_Template, 5, 2, \"div\", 47)(12, ReviewComponent_div_21_div_12_Template, 5, 2, \"div\", 47)(13, ReviewComponent_div_21_div_13_Template, 5, 2, \"div\", 47)(14, ReviewComponent_div_21_div_14_Template, 5, 2, \"div\", 47);\n    i0.ɵɵelementStart(15, \"div\", 48)(16, \"div\", 13);\n    i0.ɵɵtext(17, \"Hours/Week\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 46)(19, \"div\", 49)(20, \"strong\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.teacherApplication.step5 == null ? null : ctx_r0.teacherApplication.step5.mon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.teacherApplication.step5 == null ? null : ctx_r0.teacherApplication.step5.tue);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.teacherApplication.step5 == null ? null : ctx_r0.teacherApplication.step5.wed);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.teacherApplication.step5 == null ? null : ctx_r0.teacherApplication.step5.thu);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.teacherApplication.step5 == null ? null : ctx_r0.teacherApplication.step5.fri);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.teacherApplication.step5 == null ? null : ctx_r0.teacherApplication.step5.sat);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.teacherApplication.step5 == null ? null : ctx_r0.teacherApplication.step5.sun);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r0.teacherApplication.step5.hours);\n  }\n}\nfunction ReviewComponent_ng_container_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 51)(2, \"div\")(3, \"div\", 52)(4, \"p-checkbox\", 53);\n    i0.ɵɵlistener(\"onChange\", function ReviewComponent_ng_container_22_Template_p_checkbox_onChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.updateAgreement1($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"span\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 55)(7, \"p-checkbox\", 56);\n    i0.ɵɵlistener(\"onChange\", function ReviewComponent_ng_container_22_Template_p_checkbox_onChange_7_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.updateAgreement2($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"span\", 54);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 57)(10, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function ReviewComponent_ng_container_22_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.goBack());\n    });\n    i0.ɵɵelement(11, \"img\", 59);\n    i0.ɵɵtext(12, \" Back \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function ReviewComponent_ng_container_22_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onSubmit());\n    });\n    i0.ɵɵtext(14, \" Submit \");\n    i0.ɵɵelement(15, \"img\", 61);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nexport class ReviewComponent {\n  constructor(router, route, toastService, teacherService, generalService, authService) {\n    this.router = router;\n    this.route = route;\n    this.toastService = toastService;\n    this.teacherService = teacherService;\n    this.generalService = generalService;\n    this.authService = authService;\n    this.teacherApplication = {};\n    this.isAgreement1Checked = false;\n    this.isAgreement2Checked = false;\n    this.subs = new SubSink();\n    this.submitTeacher = {};\n    this.hideBottomLayout = false;\n  }\n  ngOnInit() {\n    this.teacherService.setCurrentStepIndex(5);\n    this.checkQueryParamsForBottomLayout();\n    this.subs.add(this.teacherService.getTeacherApplicationStep6().subscribe(teacherApplicationReview => {\n      this.teacherApplication = teacherApplicationReview;\n      console.log(this.teacherService.stepsStatuses);\n    }));\n    // this.subs.add(this.teacherService.getTeacherApplicationStepsStatus().subscribe((stepsStatus: TeacherApplicationStepsStatus) => {\n    //   console.log(stepsStatus);\n    // }));\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  updateAgreement1(event) {\n    this.isAgreement1Checked = event.checked;\n  }\n  updateAgreement2(event) {\n    this.isAgreement2Checked = event.checked;\n  }\n  onSubmit() {\n    if (!this.isAgreement1Checked || !this.isAgreement2Checked) {\n      this.toastService.setShowToastmessage({\n        severity: 'warn',\n        summary: '',\n        detail: 'Please check the terms and conditions and personal information agreement.'\n      });\n      return;\n    }\n    this.teacherService.registerTeacher({\n      teacherId: this.authService.getUserId()\n    });\n  }\n  goBack() {\n    this.router.navigateByUrl('/teacher/availability', {\n      replaceUrl: true\n    });\n  }\n  /**\n   * Subscribe to query parameters in the current route to determine if bottom layout should be hidden.\n   *\n   * @param {Params} queryparams - Query parameters in the current route.\n   */\n  checkQueryParamsForBottomLayout() {\n    this.subs.add(this.route.queryParams.subscribe(queryparams => {\n      if (queryparams && queryparams.buttons) {\n        this.hideBottomLayout = queryparams.buttons === '1';\n      }\n    }));\n  }\n  static #_ = this.ɵfac = function ReviewComponent_Factory(t) {\n    return new (t || ReviewComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.ToastService), i0.ɵɵdirectiveInject(i3.TeacherApplicationService), i0.ɵɵdirectiveInject(i4.GeneralService), i0.ɵɵdirectiveInject(i5.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ReviewComponent,\n    selectors: [[\"app-review\"]],\n    decls: 23,\n    vars: 7,\n    consts: [[1, \"review\", \"md:mt-3\"], [1, \"review-section\", \"section-bg-gradient\", \"p-3\"], [1, \"review-section-title\"], [\"class\", \"review-section-content flex flex-column md:flex-row\", 4, \"ngIf\"], [1, \"review-section\"], [\"class\", \"review-section-content\", 4, \"ngIf\"], [\"class\", \"review-section-content grid\", 4, \"ngIf\"], [\"class\", \"review-section section-bg-gradient p-3\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"review-section-content\", \"flex\", \"flex-column\", \"md:flex-row\"], [1, \"col-25\"], [1, \"info-title\", \"subtitle\"], [1, \"info\"], [1, \"col-50\"], [\"class\", \"info\", 4, \"ngIf\"], [1, \"grid\"], [1, \"grid\", \"grid-nogutter\", \"flex-column\"], [1, \"col-12\", \"info-title\", \"subtitle\"], [1, \"info\", \"col-12\", \"grid-nogutter\"], [\"class\", \"col\", 4, \"ngFor\", \"ngForOf\"], [1, \"col\"], [1, \"grid\", \"w-100\"], [1, \"col\", \"md:col-5\"], [1, \"subtitle\"], [1, \"col\", \"md:col-4\"], [1, \"col\", \"md:col-3\"], [1, \"review-section-content\"], [\"class\", \"flex flex-column md:flex-row flex-auto grid w-100\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"flex-auto\", \"grid\", \"w-100\"], [\"class\", \"file-path\", 4, \"ngFor\", \"ngForOf\"], [1, \"file-path\"], [\"src\", \"/assets/icons/education-degree-white.svg\", 1, \"close-img\", \"close-img-abs\", \"hvr-glow\"], [1, \"file-path-text\", \"font-base\"], [1, \"review-section-content\", \"grid\"], [1, \"col-12\", \"sm:col-6\"], [\"class\", \"info flex\", 4, \"ngFor\", \"ngForOf\"], [1, \"col\", \"md:col-12\"], [1, \"info\", \"flex\"], [1, \"grid\", \"flex-column\", \"sm:flex-row\"], [1, \"col-12\", \"sm:col-6\", \"review-section-content\", \"flex-column\"], [1, \"section-bg-gradient\", \"p-2\", \"flex\", \"gap-2\"], [\"src\", \"/assets/icons/teacher-application/cv-file-uploaded-icon.svg\"], [\"src\", \"/assets/icons/teacher-application/cv-image-uploaded.svg\"], [\"target\", \"_blank\", 1, \"text-primary\", 3, \"href\"], [1, \"grid\", \"w-full\", 2, \"margin-top\", \"5px\"], [1, \"col-12\", \"sm:col-8\", 2, \"margin-top\", \"5px\"], [1, \"availability\", \"flex\", \"sm:gap-2\"], [\"class\", \"availability flex sm:gap-2\", \"style\", \"font-weight: bold\", 4, \"ngFor\", \"ngForOf\"], [1, \"availability\", \"mt-4\"], [1, \"col-50\", 2, \"margin-top\", \"5px\"], [1, \"availability\", \"flex\", \"sm:gap-2\", 2, \"font-weight\", \"bold\"], [1, \"checkboxes-wrap\"], [1, \"review-section\", \"flex\", \"align-items-center\"], [\"styleClass\", \"primary-blue outlined bigger\", \"binary\", \"true\", \"label\", \"* Agree with the terms and conditions\", 3, \"onChange\"], [1, \"sm:pl-2\"], [1, \"review-section\", \"flex\", \"align-items-start\"], [\"styleClass\", \"primary-blue outlined bigger\", \"binary\", \"true\", \"label\", \"* All personal information collected by MyLingoTrip is done so exclusively with your consent, by means of a form posted\\n          on our website, an email received from you or by telephone. No information is collected automatically.\", 3, \"onChange\"], [1, \"btns\", \"md:ml-5\", \"mt-4\"], [\"pRipple\", \"\", 1, \"rounded-blue-button\", \"transparent\", 3, \"click\"], [\"src\", \"/assets/icons/arrow-left-blue.svg\"], [\"pRipple\", \"\", 1, \"rounded-blue-button\", \"submit-btn\", 3, \"click\"], [\"src\", \"/assets/icons/teacher-application/checked.svg\"]],\n    template: function ReviewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵtext(3, \"General Info\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(4, ReviewComponent_div_4_Template, 41, 11, \"div\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 1)(6, \"div\", 2);\n        i0.ɵɵtext(7, \"Contact Info\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(8, ReviewComponent_div_8_Template, 17, 4, \"div\", 3);\n        i0.ɵɵelementStart(9, \"div\", 4)(10, \"div\", 2);\n        i0.ɵɵtext(11, \"Education\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(12, ReviewComponent_div_12_Template, 2, 1, \"div\", 5);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(13, \"div\", 1)(14, \"div\", 2);\n        i0.ɵɵtext(15, \"Experience & Skills\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(16, ReviewComponent_div_16_Template, 20, 4, \"div\", 6);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(17, ReviewComponent_div_17_Template, 18, 2, \"div\", 7);\n        i0.ɵɵelementStart(18, \"div\", 1)(19, \"div\", 2);\n        i0.ɵɵtext(20, \"Availability\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(21, ReviewComponent_div_21_Template, 22, 8, \"div\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(22, ReviewComponent_ng_container_22_Template, 16, 0, \"ng-container\", 8);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.teacherApplication.step1);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.teacherApplication.step1);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.teacherApplication.step2);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.teacherApplication.step3);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.teacherApplication.step4);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.teacherApplication.step5);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.hideBottomLayout);\n      }\n    },\n    dependencies: [i6.NgForOf, i6.NgIf, i7.Ripple, i6.DatePipe],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.review[_ngcontent-%COMP%] {\\n  box-sizing: border-box;\\n}\\n.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%] {\\n  margin-top: 1.875rem;\\n}\\n.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-title[_ngcontent-%COMP%] {\\n  color: #3a4997;\\n  font-size: 1.25rem;\\n  font-weight: bold;\\n  opacity: 0.45;\\n  margin: clamp(0.75rem, 0.45vw + 0.66rem, 1.5rem) 0;\\n}\\n.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%] {\\n  font-size: clamp(0.88rem, 0.07vw + 0.86rem, 1rem);\\n  margin: clamp(0.5rem, 0.3vw + 0.44rem, 1rem) 0;\\n}\\n.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%] {\\n  color: var(--main-color);\\n  font-size: 1.125rem;\\n  display: flex;\\n  flex-wrap: wrap;\\n}\\n.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-25[_ngcontent-%COMP%], .review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-50[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-25[_ngcontent-%COMP%]   .availability[_ngcontent-%COMP%], .review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-50[_ngcontent-%COMP%]   .availability[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-25[_ngcontent-%COMP%]   .availability[_ngcontent-%COMP%]   .col-50[_ngcontent-%COMP%], .review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-50[_ngcontent-%COMP%]   .availability[_ngcontent-%COMP%]   .col-50[_ngcontent-%COMP%] {\\n  width: 50%;\\n}\\n.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .info[_ngcontent-%COMP%] {\\n  margin-bottom: 25px;\\n  font-weight: bold;\\n  font-size: clamp(0.88rem, 0.07vw + 0.86rem, 1rem);\\n}\\n.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-25[_ngcontent-%COMP%] {\\n  width: 25%;\\n  flex-basis: 25%;\\n}\\n.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-50[_ngcontent-%COMP%] {\\n  width: 50%;\\n  flex-basis: 50%;\\n}\\n.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .info-title[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n}\\n.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n\\n.checkboxes-wrap[_ngcontent-%COMP%] {\\n  margin: 36px 0;\\n  font-size: 15px;\\n  font-weight: normal;\\n  font-stretch: normal;\\n  font-style: normal;\\n  font-size: 15px;\\n  letter-spacing: 0.01em;\\n  line-height: 23px;\\n  text-align: left;\\n  color: #3a4997;\\n}\\n\\n.submit-btn[_ngcontent-%COMP%] {\\n  background-color: #2fb9d3;\\n}\\n.submit-btn[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 18px;\\n}\\n\\n[_nghost-%COMP%]     .primary-blue.p-checkbox {\\n  width: 24px;\\n  height: 24px;\\n}\\n[_nghost-%COMP%]     .primary-blue.p-checkbox-label {\\n  font-size: 0.875rem;\\n  color: var(--primary-color);\\n  cursor: pointer;\\n}\\n@media screen and (max-width: 768px) {\\n  [_nghost-%COMP%]     .primary-blue.p-checkbox-label {\\n    font-size: 14px;\\n  }\\n}\\n[_nghost-%COMP%]     .primary-blue.p-checkbox .p-checkbox-box.p-highlight .p-checkbox-icon.pi-check:before {\\n  top: 1px;\\n  left: -5px;\\n}\\n[_nghost-%COMP%]     .primary-blue.p-checkbox .p-checkbox-box {\\n  border-radius: 50px;\\n  border-color: #5BB7D0;\\n}\\n[_nghost-%COMP%]     .primary-blue.p-checkbox .p-checkbox-box.p-highlight {\\n  border-radius: 50px;\\n  background-color: #5BB7D0;\\n  border-color: #5BB7D0;\\n}\\n[_nghost-%COMP%]     .primary-blue.outlined.p-checkbox {\\n  width: 18px;\\n  height: 18px;\\n}\\n[_nghost-%COMP%]     .primary-blue.outlined.p-checkbox .p-checkbox-box .p-checkbox-icon {\\n  transform: scale(1);\\n}\\n[_nghost-%COMP%]     .primary-blue.outlined.p-checkbox .p-checkbox-box.p-highlight {\\n  border-radius: 50px;\\n  background-color: #5BB7D0;\\n  border-color: #5BB7D0;\\n  outline: 2px solid #2fb9d3;\\n  outline-offset: 3px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["SubSink", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "teacherApplication", "step1", "country<PERSON><PERSON><PERSON>", "name", "countryResidency", "nativeLanguage_r2", "ɵɵtemplate", "ReviewComponent_div_4_div_19_Template", "ReviewComponent_div_4_div_23_Template", "ɵɵelement", "ReviewComponent_div_4_div_40_Template", "firstName", "lastName", "prefferedName", "ɵɵproperty", "ɵɵpipeBind2", "birth", "timeZone", "nativeLanguages", "email", "ɵɵtextInterpolate2", "phoneCode", "phone", "skype", "ɵɵtextInterpolate", "generalService", "getFileName2", "file_r3", "filePath", "ReviewComponent_div_12_div_1_div_15_Template", "education_r4", "institute", "fileUploads", "ReviewComponent_div_12_div_1_Template", "step2", "education", "experience_r5", "language", "level", "levelName", "experience_r6", "ReviewComponent_div_16_div_4_Template", "ReviewComponent_div_16_div_8_Template", "step3", "languageTeach", "languageSpeak", "yearsExperience", "teachingMethods", "getDomainFileNamePath", "step4", "cv", "ɵɵsanitizeUrl", "getFileNameAndExtension", "profilePicture", "ReviewComponent_div_17_div_10_Template", "ReviewComponent_div_17_div_17_Template", "available_r7", "from", "to", "available_r8", "available_r9", "available_r10", "available_r11", "available_r12", "available_r13", "ReviewComponent_div_21_div_8_Template", "ReviewComponent_div_21_div_9_Template", "ReviewComponent_div_21_div_10_Template", "ReviewComponent_div_21_div_11_Template", "ReviewComponent_div_21_div_12_Template", "ReviewComponent_div_21_div_13_Template", "ReviewComponent_div_21_div_14_Template", "step5", "mon", "tue", "wed", "thu", "fri", "sat", "sun", "hours", "ɵɵelementContainerStart", "ɵɵlistener", "ReviewComponent_ng_container_22_Template_p_checkbox_onChange_4_listener", "$event", "ɵɵrestoreView", "_r14", "ɵɵnextContext", "ɵɵresetView", "updateAgreement1", "ReviewComponent_ng_container_22_Template_p_checkbox_onChange_7_listener", "updateAgreement2", "ReviewComponent_ng_container_22_Template_button_click_10_listener", "goBack", "ReviewComponent_ng_container_22_Template_button_click_13_listener", "onSubmit", "ReviewComponent", "constructor", "router", "route", "toastService", "teacherService", "authService", "isAgreement1Checked", "isAgreement2Checked", "subs", "submitTeacher", "hideBottomLayout", "ngOnInit", "setCurrentStepIndex", "checkQueryParamsForBottomLayout", "add", "getTeacherApplicationStep6", "subscribe", "teacherApplicationReview", "console", "log", "stepsStatuses", "ngOnDestroy", "unsubscribe", "event", "checked", "setShowToastmessage", "severity", "summary", "detail", "registerTeacher", "teacherId", "getUserId", "navigateByUrl", "replaceUrl", "queryParams", "queryparams", "buttons", "_", "ɵɵdirectiveInject", "i1", "Router", "ActivatedRoute", "i2", "ToastService", "i3", "TeacherApplicationService", "i4", "GeneralService", "i5", "AuthService", "_2", "selectors", "decls", "vars", "consts", "template", "ReviewComponent_Template", "rf", "ctx", "ReviewComponent_div_4_Template", "ReviewComponent_div_8_Template", "ReviewComponent_div_12_Template", "ReviewComponent_div_16_Template", "ReviewComponent_div_17_Template", "ReviewComponent_div_21_Template", "ReviewComponent_ng_container_22_Template"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\teacher-application\\review\\review.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\teacher-application\\review\\review.component.html"], "sourcesContent": ["import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Params, Router } from '@angular/router';\r\nimport { Teacher, TeacherApplicationReview, TeacherApplicationStepsStatus, UserAvailabilityStep5 } from 'src/app/core/models/teacher.model';\r\nimport { UserAvailability, UserAvailabilityType } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { TeacherApplicationService } from 'src/app/core/services/teacher-application.service';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { SubSink } from 'subsink';\r\n\r\n@Component({\r\n  selector: 'app-review',\r\n  templateUrl: './review.component.html',\r\n  styleUrls: ['./review.component.scss']\r\n})\r\nexport class ReviewComponent implements OnInit, OnDestroy {\r\n  public teacherApplication: TeacherApplicationReview = {} as TeacherApplicationReview;\r\n  public isAgreement1Checked: boolean = false;\r\n  public isAgreement2Checked: boolean = false;\r\n  private subs = new SubSink();\r\n  private submitTeacher: Teacher = {} as Teacher;\r\n  hideBottomLayout = false;\r\n  constructor(\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private toastService: ToastService,\r\n    private teacherService: TeacherApplicationService,\r\n    public generalService: GeneralService,\r\n    public authService: AuthService,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.teacherService.setCurrentStepIndex(5);\r\n    this.checkQueryParamsForBottomLayout();\r\n    this.subs.add(this.teacherService.getTeacherApplicationStep6().subscribe((teacherApplicationReview: TeacherApplicationReview) => {\r\n      this.teacherApplication = teacherApplicationReview;\r\n      console.log(this.teacherService.stepsStatuses);\r\n    }));\r\n\r\n    // this.subs.add(this.teacherService.getTeacherApplicationStepsStatus().subscribe((stepsStatus: TeacherApplicationStepsStatus) => {\r\n    //   console.log(stepsStatus);\r\n    // }));\r\n\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  updateAgreement1(event: any) {\r\n    this.isAgreement1Checked = event.checked;\r\n  }\r\n\r\n  updateAgreement2(event: any) {\r\n    this.isAgreement2Checked = event.checked;\r\n  }\r\n\r\n  onSubmit() {\r\n    if (!this.isAgreement1Checked || !this.isAgreement2Checked) {\r\n      \r\n      this.toastService.setShowToastmessage({\r\n        severity: 'warn',\r\n        summary: '',\r\n        detail: 'Please check the terms and conditions and personal information agreement.'\r\n      });\r\n      return;\r\n    }\r\n\r\n    this.teacherService.registerTeacher({ teacherId: this.authService.getUserId() });\r\n  }\r\n\r\n  goBack() {\r\n    this.router.navigateByUrl('/teacher/availability', { replaceUrl: true });\r\n  }\r\n\r\n  /**\r\n   * Subscribe to query parameters in the current route to determine if bottom layout should be hidden.\r\n   * \r\n   * @param {Params} queryparams - Query parameters in the current route.\r\n   */\r\n  private checkQueryParamsForBottomLayout(): void {\r\n    this.subs.add(this.route.queryParams.subscribe((queryparams: Params) => {\r\n      if (queryparams && queryparams.buttons) {\r\n        this.hideBottomLayout = queryparams.buttons === '1';\r\n      }\r\n    }));\r\n  }\r\n\r\n}\r\n", "<div class=\"review md:mt-3\">\r\n    <div class=\"review-section section-bg-gradient p-3\">\r\n      <div class=\"review-section-title\">General Info</div>\r\n      <div class=\"review-section-content flex flex-column md:flex-row\" *ngIf=\"teacherApplication.step1\">\r\n        <div class=\"col-25\">\r\n          <div class=\"info-title subtitle\">First Name</div>\r\n          <div class=\"info\">\r\n            {{teacherApplication.step1.firstName}}\r\n          </div>\r\n        </div>\r\n        <div class=\"col-25\">\r\n          <div class=\"info-title subtitle\">Last Name</div>\r\n          <div class=\"info\">\r\n            {{teacherApplication.step1.lastName}}\r\n          </div>\r\n        </div>\r\n        <div class=\"col-50\">\r\n          <div class=\"info-title subtitle\">Preffered Name</div>\r\n          <div class=\"info\">\r\n            {{teacherApplication.step1.prefferedName}}\r\n          </div>\r\n        </div>\r\n        <div class=\"col-25\">\r\n          <div class=\"info-title subtitle\">Country of Origin</div>\r\n          <div class=\"info\" *ngIf=\"teacherApplication.step1.countryOrigin\">\r\n            {{teacherApplication.step1.countryOrigin.name}}\r\n          </div>\r\n        </div>\r\n        <div class=\"col-25\">\r\n          <div class=\"info-title subtitle\">Country of Residency</div>\r\n          <div class=\"info\" *ngIf=\"teacherApplication.step1.countryResidency\">\r\n            {{teacherApplication.step1.countryResidency.name}}\r\n          </div>\r\n        </div>\r\n        <div class=\"col-25\">\r\n          <div class=\"info-title subtitle\">Date of Birth</div>\r\n          <div class=\"info\">\r\n            {{teacherApplication.step1.birth | date: 'dd-MM-yyy'}}\r\n          </div>\r\n        </div>\r\n        <div class=\"col-25\">\r\n          <div class=\"info-title subtitle\">Timezone</div>\r\n          <div class=\"info\">\r\n            {{teacherApplication.step1.timeZone}}\r\n          </div>\r\n        </div>\r\n        <div class=\"grid\"></div>\r\n        <div class=\"grid grid-nogutter flex-column\">\r\n          <div class=\"col-12 info-title subtitle\">Native Languages</div>\r\n          <div class=\"info col-12 grid-nogutter\">\r\n            <div\r\n              class=\"col\"\r\n              *ngFor=\"let nativeLanguage of teacherApplication.step1.nativeLanguages\"\r\n            >\r\n              {{nativeLanguage['native'].name}}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  \r\n    <div class=\"review-section section-bg-gradient p-3\">\r\n      <div class=\"review-section-title\">Contact Info</div>\r\n      <div class=\"review-section-content flex flex-column md:flex-row\" *ngIf=\"teacherApplication.step1\">\r\n        <div class=\"grid w-100\">\r\n        <div class=\"col md:col-5\">\r\n          <div class=\"subtitle\">E-mail</div>\r\n          <div class=\"info\">\r\n            {{teacherApplication.step1.email}}\r\n          </div>\r\n        </div>\r\n        <div class=\"col md:col-4\">\r\n          <div class=\"subtitle\">Phone</div>\r\n          <div class=\"info\">\r\n            {{teacherApplication.step1.phoneCode}}\r\n            {{teacherApplication.step1.phone}}\r\n          </div>\r\n        </div>\r\n        <div class=\"col md:col-3\">\r\n          <div class=\"subtitle\">Skype</div>\r\n          <div class=\"info\">\r\n            {{teacherApplication.step1.skype}}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      </div>\r\n      <div class=\"review-section\">\r\n        <div class=\"review-section-title\">Education</div>\r\n        <div class=\"review-section-content\" *ngIf=\"teacherApplication.step2\">\r\n          <div\r\n            *ngFor=\"let education of teacherApplication.step2.education; let ind = index;\"\r\n            class=\"flex flex-column md:flex-row flex-auto grid w-100\"\r\n          >\r\n            <div class=\"col md:col-4\">\r\n              <div class=\"subtitle\">Degree</div>\r\n              <div class=\"info\">\r\n                {{education.name}}\r\n              </div>\r\n            </div>\r\n            <div class=\"col md:col-4\">\r\n              <div class=\"subtitle\">Institute</div>\r\n              <div class=\"info\">\r\n                {{education.institute}}\r\n              </div>\r\n            </div>\r\n  \r\n            <div class=\"col md:col-4\">\r\n              <div class=\"subtitle\">Degree / Diploma</div>\r\n              <div class=\"info\">\r\n                <div\r\n                  class=\"file-path\"\r\n                  *ngFor=\"let file of education.fileUploads;\"\r\n                >\r\n                  <img\r\n                    src=\"/assets/icons/education-degree-white.svg\"\r\n                    class=\"close-img close-img-abs hvr-glow\"\r\n                  />\r\n                  <span\r\n                    class=\"file-path-text font-base\"\r\n                    >{{(this.generalService.getFileName2(file.filePath))}}</span\r\n                  >\r\n                  <!-- <img src=\"/assets/icons/trash-white.svg\" alt=\"trash-white.svg\" class=\"delete-file-icon-img hvr-glow\"> -->\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  \r\n    <div class=\"review-section section-bg-gradient p-3\">\r\n      <div class=\"review-section-title\">Experience & Skills</div>\r\n      <div class=\"review-section-content grid\" *ngIf=\"teacherApplication.step3\">\r\n        <div class=\"col-12 sm:col-6\">\r\n          <div class=\"subtitle\">Teaching Language</div>\r\n          <div\r\n            class=\"info flex\"\r\n            *ngFor=\"let experience of teacherApplication.step3.languageTeach\"\r\n          >\r\n            <div class=\"col-50\">\r\n              {{experience.language.name}}\r\n            </div>\r\n            <div class=\"col-50\">\r\n              {{experience.level.levelName}}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-12 sm:col-6\">\r\n          <div class=\"subtitle\">Speaking Language</div>\r\n          <div\r\n            class=\"info flex\"\r\n            *ngFor=\"let experience of teacherApplication.step3.languageSpeak\"\r\n          >\r\n            <div class=\"col-50\">\r\n              {{experience.language.name}}\r\n            </div>\r\n            <div class=\"col-50\">\r\n              {{experience.level.levelName}}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"grid\">\r\n          <div class=\"col md:col-12\">\r\n            <div class=\"subtitle\">Years of teaching experience</div>\r\n            <div class=\"info\">\r\n              {{teacherApplication.step3.yearsExperience}}\r\n            </div>\r\n          </div>\r\n          <div class=\"col md:col-12\">\r\n            <div class=\"subtitle\">Teaching Method</div>\r\n            <div class=\"info\">\r\n              {{teacherApplication.step3.teachingMethods}}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  \r\n    <div\r\n      class=\"review-section section-bg-gradient p-3\"\r\n      *ngIf=\"teacherApplication.step4\"\r\n    >\r\n      <div class=\"review-section-title\">Resume</div>\r\n      <div class=\"grid flex-column sm:flex-row\">\r\n        <div class=\"col-12 sm:col-6 review-section-content flex-column\">\r\n          <div class=\"subtitle\">CV</div>\r\n          <div class=\"section-bg-gradient p-2 flex gap-2\">\r\n            <div>\r\n              <img\r\n                src=\"/assets/icons/teacher-application/cv-file-uploaded-icon.svg\"\r\n              />\r\n            </div>\r\n            <div *ngIf=\"teacherApplication.step4.cv\">\r\n              <a class=\"text-primary\" [href]=\"this.generalService.getDomainFileNamePath(teacherApplication.step4.cv)\" target=\"_blank\">\r\n                \r\n                {{this.generalService.getFileNameAndExtension(teacherApplication.step4.cv)}}\r\n              </a>\r\n          \r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-12 sm:col-6 review-section-content flex-column\">\r\n          <div class=\"subtitle\">Photo</div>\r\n          <div class=\"section-bg-gradient p-2 flex gap-2\">\r\n            <div>\r\n              <img\r\n                src=\"/assets/icons/teacher-application/cv-image-uploaded.svg\"\r\n              />\r\n            </div>\r\n            <div *ngIf=\"teacherApplication.step4.profilePicture\">\r\n              <a class=\"text-primary\" [href]=\"this.generalService.getDomainFileNamePath(teacherApplication.step4.profilePicture)\" target=\"_blank\">\r\n                {{this.generalService.getFileNameAndExtension(teacherApplication.step4.profilePicture)}}</a>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  \r\n    <div class=\"review-section section-bg-gradient p-3\">\r\n      <div class=\"review-section-title\">Availability</div>\r\n      <div class=\"review-section-content\" *ngIf=\"teacherApplication.step5\">\r\n        <div class=\"grid w-full\" style=\"margin-top:5px;\">\r\n          <div class=\"col-12 sm:col-8\" style=\"margin-top:5px;\">\r\n          <div class=\"availability flex sm:gap-2\">\r\n            <div class=\"col-50\">Days</div>\r\n            <div class=\"col-50\">Time</div>\r\n          </div>\r\n          <div\r\n            *ngFor=\"let available of teacherApplication.step5?.mon\"\r\n            class=\"availability flex sm:gap-2\"\r\n            style=\"font-weight: bold\"\r\n          >\r\n            <div class=\"col-50\" style=\"margin-top:5px\">Monday</div>\r\n            <div class=\"col-50\" style=\"margin-top:5px\">\r\n              {{available.from}} - {{available.to}}\r\n            </div>\r\n          </div>\r\n          <div\r\n            *ngFor=\"let available of teacherApplication.step5?.tue\"\r\n            class=\"availability flex sm:gap-2\"\r\n            style=\"font-weight: bold\"\r\n          >\r\n            <div class=\"col-50\" style=\"margin-top:5px\">Tuesday</div>\r\n            <div class=\"col-50\" style=\"margin-top:5px\">\r\n              {{available.from}} - {{available.to}}\r\n            </div>\r\n          </div>\r\n          <div\r\n            *ngFor=\"let available of teacherApplication.step5?.wed\"\r\n            class=\"availability flex sm:gap-2\"\r\n            style=\"font-weight: bold\"\r\n          >\r\n            <div class=\"col-50\" style=\"margin-top:5px\">Wednesday</div>\r\n            <div class=\"col-50\" style=\"margin-top:5px\">\r\n              {{available.from}} - {{available.to}}\r\n            </div>\r\n          </div>\r\n          <div\r\n            *ngFor=\"let available of teacherApplication.step5?.thu\"\r\n            class=\"availability flex sm:gap-2\"\r\n            style=\"font-weight: bold\"\r\n          >\r\n            <div class=\"col-50\" style=\"margin-top:5px\">Thursday</div>\r\n            <div class=\"col-50\" style=\"margin-top:5px\">\r\n              {{available.from}} - {{available.to}}\r\n            </div>\r\n          </div>\r\n          <div\r\n            *ngFor=\"let available of teacherApplication.step5?.fri\"\r\n            class=\"availability flex sm:gap-2\"\r\n            style=\"font-weight: bold\"\r\n          >\r\n            <div class=\"col-50\" style=\"margin-top:5px\">Friday</div>\r\n            <div class=\"col-50\" style=\"margin-top:5px\">\r\n              {{available.from}} - {{available.to}}\r\n            </div>\r\n          </div>\r\n          <div\r\n            *ngFor=\"let available of teacherApplication.step5?.sat\"\r\n            class=\"availability flex sm:gap-2\"\r\n            style=\"font-weight: bold\"\r\n          >\r\n            <div class=\"col-50\" style=\"margin-top:5px\">Saturday</div>\r\n            <div class=\"col-50\" style=\"margin-top:5px\">\r\n              {{available.from}} - {{available.to}}\r\n            </div>\r\n          </div>\r\n          <div\r\n            *ngFor=\"let available of teacherApplication.step5?.sun\"\r\n            class=\"availability flex sm:gap-2\"\r\n            style=\"font-weight: bold\"\r\n          >\r\n            <div class=\"col-50\" style=\"margin-top:5px\">Sunday</div>\r\n            <div class=\"col-50\" style=\"margin-top:5px\">\r\n              {{available.from}} - {{available.to}}\r\n            </div>\r\n          </div>\r\n          <div class=\"availability mt-4\">\r\n            <div class=\"col-50\">Hours/Week</div>\r\n          </div>\r\n          <div class=\"availability flex sm:gap-2\">\r\n            <div class=\"col-50\" style=\"margin-top:5px;\">\r\n              <strong>{{teacherApplication.step5.hours}}</strong>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      </div>\r\n    </div>\r\n    <ng-container *ngIf=\"!hideBottomLayout\">\r\n    <div class=\"checkboxes-wrap\">\r\n      <div>\r\n        <div class=\"review-section flex align-items-center\">\r\n          \r\n          <p-checkbox styleClass=\"primary-blue outlined bigger\" binary=\"true\" label=\"* Agree with the terms and conditions\" (onChange)=\"updateAgreement1($event)\"></p-checkbox>\r\n\r\n          <span class=\"sm:pl-2\">\r\n            \r\n          </span>\r\n        </div>\r\n        <div class=\"review-section flex align-items-start\">\r\n          <p-checkbox styleClass=\"primary-blue outlined bigger\" binary=\"true\" label=\"* All personal information collected by MyLingoTrip is done so exclusively with your consent, by means of a form posted\r\n          on our website, an email received from you or by telephone. No information is collected automatically.\" (onChange)=\"updateAgreement2($event)\"></p-checkbox>\r\n          <span class=\"sm:pl-2\">\r\n        </span>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  \r\n    <div class=\"btns md:ml-5 mt-4\">\r\n      <button pRipple (click)=\"goBack()\" class=\"rounded-blue-button transparent\">\r\n        <img src=\"/assets/icons/arrow-left-blue.svg\" /> Back\r\n      </button>\r\n      <button pRipple (click)=\"onSubmit()\" class=\"rounded-blue-button submit-btn\">\r\n        Submit <img src=\"/assets/icons/teacher-application/checked.svg\" />\r\n      </button>\r\n    </div>\r\n  </ng-container>\r\n  </div>\r\n  "], "mappings": "AAQA,SAASA,OAAO,QAAQ,SAAS;;;;;;;;;;;ICgBvBC,EAAA,CAAAC,cAAA,cAAiE;IAC/DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,kBAAA,CAAAC,KAAA,CAAAC,aAAA,CAAAC,IAAA,MACF;;;;;IAIAV,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,kBAAA,CAAAC,KAAA,CAAAG,gBAAA,CAAAD,IAAA,MACF;;;;;IAkBEV,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAO,iBAAA,WAAAF,IAAA,MACF;;;;;IAlDFV,EAFJ,CAAAC,cAAA,aAAkG,cAC5E,cACe;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjDH,EAAA,CAAAC,cAAA,cAAkB;IAChBD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAEJH,EADF,CAAAC,cAAA,cAAoB,cACe;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAChDH,EAAA,CAAAC,cAAA,cAAkB;IAChBD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAEJH,EADF,CAAAC,cAAA,eAAoB,eACe;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACrDH,EAAA,CAAAC,cAAA,eAAkB;IAChBD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAEJH,EADF,CAAAC,cAAA,eAAoB,eACe;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACxDH,EAAA,CAAAa,UAAA,KAAAC,qCAAA,kBAAiE;IAGnEd,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAoB,eACe;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC3DH,EAAA,CAAAa,UAAA,KAAAE,qCAAA,kBAAoE;IAGtEf,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAoB,eACe;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpDH,EAAA,CAAAC,cAAA,eAAkB;IAChBD,EAAA,CAAAE,MAAA,IACF;;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAEJH,EADF,CAAAC,cAAA,eAAoB,eACe;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC/CH,EAAA,CAAAC,cAAA,eAAkB;IAChBD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IACNH,EAAA,CAAAgB,SAAA,eAAwB;IAEtBhB,EADF,CAAAC,cAAA,eAA4C,eACF;IAAAD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC9DH,EAAA,CAAAC,cAAA,eAAuC;IACrCD,EAAA,CAAAa,UAAA,KAAAI,qCAAA,kBAGC;IAKPjB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAnDAH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,kBAAA,CAAAC,KAAA,CAAAU,SAAA,MACF;IAKElB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,kBAAA,CAAAC,KAAA,CAAAW,QAAA,MACF;IAKEnB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,kBAAA,CAAAC,KAAA,CAAAY,aAAA,MACF;IAImBpB,EAAA,CAAAI,SAAA,GAA4C;IAA5CJ,EAAA,CAAAqB,UAAA,SAAAf,MAAA,CAAAC,kBAAA,CAAAC,KAAA,CAAAC,aAAA,CAA4C;IAM5CT,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAqB,UAAA,SAAAf,MAAA,CAAAC,kBAAA,CAAAC,KAAA,CAAAG,gBAAA,CAA+C;IAOhEX,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAAsB,WAAA,QAAAhB,MAAA,CAAAC,kBAAA,CAAAC,KAAA,CAAAe,KAAA,oBACF;IAKEvB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,kBAAA,CAAAC,KAAA,CAAAgB,QAAA,MACF;IAQ+BxB,EAAA,CAAAI,SAAA,GAA2C;IAA3CJ,EAAA,CAAAqB,UAAA,YAAAf,MAAA,CAAAC,kBAAA,CAAAC,KAAA,CAAAiB,eAAA,CAA2C;;;;;IAc1EzB,EAHJ,CAAAC,cAAA,aAAkG,cACxE,cACE,cACF;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAClCH,EAAA,CAAAC,cAAA,cAAkB;IAChBD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAEJH,EADF,CAAAC,cAAA,cAA0B,cACF;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjCH,EAAA,CAAAC,cAAA,eAAkB;IAChBD,EAAA,CAAAE,MAAA,IAEF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAEJH,EADF,CAAAC,cAAA,eAA0B,eACF;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjCH,EAAA,CAAAC,cAAA,eAAkB;IAChBD,EAAA,CAAAE,MAAA,IACF;IAGJF,EAHI,CAAAG,YAAA,EAAM,EACF,EACF,EACA;;;;IAjBAH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,kBAAA,CAAAC,KAAA,CAAAkB,KAAA,MACF;IAKE1B,EAAA,CAAAI,SAAA,GAEF;IAFEJ,EAAA,CAAA2B,kBAAA,MAAArB,MAAA,CAAAC,kBAAA,CAAAC,KAAA,CAAAoB,SAAA,OAAAtB,MAAA,CAAAC,kBAAA,CAAAC,KAAA,CAAAqB,KAAA,MAEF;IAKE7B,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,kBAAA,CAAAC,KAAA,CAAAsB,KAAA,MACF;;;;;IA2BM9B,EAAA,CAAAC,cAAA,cAGC;IACCD,EAAA,CAAAgB,SAAA,cAGE;IACFhB,EAAA,CAAAC,cAAA,eAEG;IAAAD,EAAA,CAAAE,MAAA,GAAqD;IAG1DF,EAH0D,CAAAG,YAAA,EACvD,EAEG;;;;;IAHDH,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAA+B,iBAAA,CAAAzB,MAAA,CAAA0B,cAAA,CAAAC,YAAA,CAAAC,OAAA,CAAAC,QAAA,EAAqD;;;;;IAzB5DnC,EALJ,CAAAC,cAAA,cAGC,cAC2B,cACF;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAClCH,EAAA,CAAAC,cAAA,cAAkB;IAChBD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAEJH,EADF,CAAAC,cAAA,cAA0B,cACF;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACrCH,EAAA,CAAAC,cAAA,cAAkB;IAChBD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,eAA0B,eACF;IAAAD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5CH,EAAA,CAAAC,cAAA,eAAkB;IAChBD,EAAA,CAAAa,UAAA,KAAAuB,4CAAA,kBAGC;IAaPpC,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IA7BAH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAgC,YAAA,CAAA3B,IAAA,MACF;IAKEV,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAgC,YAAA,CAAAC,SAAA,MACF;IAQqBtC,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAqB,UAAA,YAAAgB,YAAA,CAAAE,WAAA,CAAyB;;;;;IAvBpDvC,EAAA,CAAAC,cAAA,cAAqE;IACnED,EAAA,CAAAa,UAAA,IAAA2B,qCAAA,mBAGC;IAkCHxC,EAAA,CAAAG,YAAA,EAAM;;;;IApCoBH,EAAA,CAAAI,SAAA,EAAuC;IAAvCJ,EAAA,CAAAqB,UAAA,YAAAf,MAAA,CAAAC,kBAAA,CAAAkC,KAAA,CAAAC,SAAA,CAAuC;;;;;IAiD7D1C,EAJF,CAAAC,cAAA,cAGC,cACqB;IAClBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAoB;IAClBD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IALFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAsC,aAAA,CAAAC,QAAA,CAAAlC,IAAA,MACF;IAEEV,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAsC,aAAA,CAAAE,KAAA,CAAAC,SAAA,MACF;;;;;IASA9C,EAJF,CAAAC,cAAA,cAGC,cACqB;IAClBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAoB;IAClBD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IALFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA0C,aAAA,CAAAH,QAAA,CAAAlC,IAAA,MACF;IAEEV,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAA0C,aAAA,CAAAF,KAAA,CAAAC,SAAA,MACF;;;;;IAxBF9C,EAFJ,CAAAC,cAAA,cAA0E,cAC3C,cACL;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC7CH,EAAA,CAAAa,UAAA,IAAAmC,qCAAA,kBAGC;IAQHhD,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAA6B,cACL;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC7CH,EAAA,CAAAa,UAAA,IAAAoC,qCAAA,kBAGC;IAQHjD,EAAA,CAAAG,YAAA,EAAM;IAGFH,EAFJ,CAAAC,cAAA,cAAkB,eACW,eACH;IAAAD,EAAA,CAAAE,MAAA,oCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACxDH,EAAA,CAAAC,cAAA,eAAkB;IAChBD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAEJH,EADF,CAAAC,cAAA,eAA2B,eACH;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC3CH,EAAA,CAAAC,cAAA,eAAkB;IAChBD,EAAA,CAAAE,MAAA,IACF;IAGNF,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;;IAtCuBH,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAqB,UAAA,YAAAf,MAAA,CAAAC,kBAAA,CAAA2C,KAAA,CAAAC,aAAA,CAAyC;IAczCnD,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAqB,UAAA,YAAAf,MAAA,CAAAC,kBAAA,CAAA2C,KAAA,CAAAE,aAAA,CAAyC;IAc9DpD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,kBAAA,CAAA2C,KAAA,CAAAG,eAAA,MACF;IAKErD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,kBAAA,CAAA2C,KAAA,CAAAI,eAAA,MACF;;;;;IAqBEtD,EADF,CAAAC,cAAA,UAAyC,YACiF;IAEtHD,EAAA,CAAAE,MAAA,GACF;IAEFF,EAFE,CAAAG,YAAA,EAAI,EAEA;;;;IALoBH,EAAA,CAAAI,SAAA,EAA+E;IAA/EJ,EAAA,CAAAqB,UAAA,SAAAf,MAAA,CAAA0B,cAAA,CAAAuB,qBAAA,CAAAjD,MAAA,CAAAC,kBAAA,CAAAiD,KAAA,CAAAC,EAAA,GAAAzD,EAAA,CAAA0D,aAAA,CAA+E;IAErG1D,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA0B,cAAA,CAAA2B,uBAAA,CAAArD,MAAA,CAAAC,kBAAA,CAAAiD,KAAA,CAAAC,EAAA,OACF;;;;;IAcAzD,EADF,CAAAC,cAAA,UAAqD,YACiF;IAClID,EAAA,CAAAE,MAAA,GAAwF;IAC5FF,EAD4F,CAAAG,YAAA,EAAI,EAC1F;;;;IAFoBH,EAAA,CAAAI,SAAA,EAA2F;IAA3FJ,EAAA,CAAAqB,UAAA,SAAAf,MAAA,CAAA0B,cAAA,CAAAuB,qBAAA,CAAAjD,MAAA,CAAAC,kBAAA,CAAAiD,KAAA,CAAAI,cAAA,GAAA5D,EAAA,CAAA0D,aAAA,CAA2F;IACjH1D,EAAA,CAAAI,SAAA,EAAwF;IAAxFJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA0B,cAAA,CAAA2B,uBAAA,CAAArD,MAAA,CAAAC,kBAAA,CAAAiD,KAAA,CAAAI,cAAA,MAAwF;;;;;IA7BlG5D,EAJF,CAAAC,cAAA,aAGC,aACmC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAG1CH,EAFJ,CAAAC,cAAA,cAA0C,cACwB,cACxC;IAAAD,EAAA,CAAAE,MAAA,SAAE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAE5BH,EADF,CAAAC,cAAA,cAAgD,UACzC;IACHD,EAAA,CAAAgB,SAAA,cAEE;IACJhB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAa,UAAA,KAAAgD,sCAAA,iBAAyC;IAQ7C7D,EADE,CAAAG,YAAA,EAAM,EACF;IAEJH,EADF,CAAAC,cAAA,eAAgE,eACxC;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAE/BH,EADF,CAAAC,cAAA,eAAgD,WACzC;IACHD,EAAA,CAAAgB,SAAA,eAEE;IACJhB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAa,UAAA,KAAAiD,sCAAA,iBAAqD;IAO7D9D,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;;;;IAxBQH,EAAA,CAAAI,SAAA,IAAiC;IAAjCJ,EAAA,CAAAqB,UAAA,SAAAf,MAAA,CAAAC,kBAAA,CAAAiD,KAAA,CAAAC,EAAA,CAAiC;IAiBjCzD,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAqB,UAAA,SAAAf,MAAA,CAAAC,kBAAA,CAAAiD,KAAA,CAAAI,cAAA,CAA6C;;;;;IAuBnD5D,EALF,CAAAC,cAAA,cAIC,cAC4C;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvDH,EAAA,CAAAC,cAAA,cAA2C;IACzCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAFFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA2B,kBAAA,MAAAoC,YAAA,CAAAC,IAAA,SAAAD,YAAA,CAAAE,EAAA,MACF;;;;;IAOAjE,EALF,CAAAC,cAAA,cAIC,cAC4C;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACxDH,EAAA,CAAAC,cAAA,cAA2C;IACzCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAFFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA2B,kBAAA,MAAAuC,YAAA,CAAAF,IAAA,SAAAE,YAAA,CAAAD,EAAA,MACF;;;;;IAOAjE,EALF,CAAAC,cAAA,cAIC,cAC4C;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1DH,EAAA,CAAAC,cAAA,cAA2C;IACzCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAFFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA2B,kBAAA,MAAAwC,YAAA,CAAAH,IAAA,SAAAG,YAAA,CAAAF,EAAA,MACF;;;;;IAOAjE,EALF,CAAAC,cAAA,cAIC,cAC4C;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzDH,EAAA,CAAAC,cAAA,cAA2C;IACzCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAFFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA2B,kBAAA,MAAAyC,aAAA,CAAAJ,IAAA,SAAAI,aAAA,CAAAH,EAAA,MACF;;;;;IAOAjE,EALF,CAAAC,cAAA,cAIC,cAC4C;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvDH,EAAA,CAAAC,cAAA,cAA2C;IACzCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAFFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA2B,kBAAA,MAAA0C,aAAA,CAAAL,IAAA,SAAAK,aAAA,CAAAJ,EAAA,MACF;;;;;IAOAjE,EALF,CAAAC,cAAA,cAIC,cAC4C;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzDH,EAAA,CAAAC,cAAA,cAA2C;IACzCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAFFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA2B,kBAAA,MAAA2C,aAAA,CAAAN,IAAA,SAAAM,aAAA,CAAAL,EAAA,MACF;;;;;IAOAjE,EALF,CAAAC,cAAA,cAIC,cAC4C;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvDH,EAAA,CAAAC,cAAA,cAA2C;IACzCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAFFH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAA2B,kBAAA,MAAA4C,aAAA,CAAAP,IAAA,SAAAO,aAAA,CAAAN,EAAA,MACF;;;;;IAvEAjE,EAJN,CAAAC,cAAA,cAAqE,cAClB,cACM,cACb,cAClB;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC9BH,EAAA,CAAAC,cAAA,cAAoB;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAC1BF,EAD0B,CAAAG,YAAA,EAAM,EAC1B;IA6DNH,EA5DA,CAAAa,UAAA,IAAA2D,qCAAA,kBAIC,IAAAC,qCAAA,kBAUA,KAAAC,sCAAA,kBAUA,KAAAC,sCAAA,kBAUA,KAAAC,sCAAA,kBAUA,KAAAC,sCAAA,kBAUA,KAAAC,sCAAA,kBAUA;IAOC9E,EADF,CAAAC,cAAA,eAA+B,eACT;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAChCF,EADgC,CAAAG,YAAA,EAAM,EAChC;IAGFH,EAFJ,CAAAC,cAAA,eAAwC,eACM,cAClC;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAKlDF,EALkD,CAAAG,YAAA,EAAS,EAC/C,EACF,EACF,EACF,EACA;;;;IA/EsBH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAqB,UAAA,YAAAf,MAAA,CAAAC,kBAAA,CAAAwE,KAAA,kBAAAzE,MAAA,CAAAC,kBAAA,CAAAwE,KAAA,CAAAC,GAAA,CAAgC;IAUhChF,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAqB,UAAA,YAAAf,MAAA,CAAAC,kBAAA,CAAAwE,KAAA,kBAAAzE,MAAA,CAAAC,kBAAA,CAAAwE,KAAA,CAAAE,GAAA,CAAgC;IAUhCjF,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAqB,UAAA,YAAAf,MAAA,CAAAC,kBAAA,CAAAwE,KAAA,kBAAAzE,MAAA,CAAAC,kBAAA,CAAAwE,KAAA,CAAAG,GAAA,CAAgC;IAUhClF,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAqB,UAAA,YAAAf,MAAA,CAAAC,kBAAA,CAAAwE,KAAA,kBAAAzE,MAAA,CAAAC,kBAAA,CAAAwE,KAAA,CAAAI,GAAA,CAAgC;IAUhCnF,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAqB,UAAA,YAAAf,MAAA,CAAAC,kBAAA,CAAAwE,KAAA,kBAAAzE,MAAA,CAAAC,kBAAA,CAAAwE,KAAA,CAAAK,GAAA,CAAgC;IAUhCpF,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAqB,UAAA,YAAAf,MAAA,CAAAC,kBAAA,CAAAwE,KAAA,kBAAAzE,MAAA,CAAAC,kBAAA,CAAAwE,KAAA,CAAAM,GAAA,CAAgC;IAUhCrF,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAqB,UAAA,YAAAf,MAAA,CAAAC,kBAAA,CAAAwE,KAAA,kBAAAzE,MAAA,CAAAC,kBAAA,CAAAwE,KAAA,CAAAO,GAAA,CAAgC;IAc5CtF,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAA+B,iBAAA,CAAAzB,MAAA,CAAAC,kBAAA,CAAAwE,KAAA,CAAAQ,KAAA,CAAkC;;;;;;IAOpDvF,EAAA,CAAAwF,uBAAA,GAAwC;IAKlCxF,EAJN,CAAAC,cAAA,cAA6B,UACtB,cACiD,qBAEsG;IAAtCD,EAAA,CAAAyF,UAAA,sBAAAC,wEAAAC,MAAA;MAAA3F,EAAA,CAAA4F,aAAA,CAAAC,IAAA;MAAA,MAAAvF,MAAA,GAAAN,EAAA,CAAA8F,aAAA;MAAA,OAAA9F,EAAA,CAAA+F,WAAA,CAAYzF,MAAA,CAAA0F,gBAAA,CAAAL,MAAA,CAAwB;IAAA,EAAC;IAAC3F,EAAA,CAAAG,YAAA,EAAa;IAErKH,EAAA,CAAAgB,SAAA,eAEO;IACThB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAmD,qBAE6F;IAAtCD,EAAA,CAAAyF,UAAA,sBAAAQ,wEAAAN,MAAA;MAAA3F,EAAA,CAAA4F,aAAA,CAAAC,IAAA;MAAA,MAAAvF,MAAA,GAAAN,EAAA,CAAA8F,aAAA;MAAA,OAAA9F,EAAA,CAAA+F,WAAA,CAAYzF,MAAA,CAAA4F,gBAAA,CAAAP,MAAA,CAAwB;IAAA,EAAC;IAAC3F,EAAA,CAAAG,YAAA,EAAa;IAC3JH,EAAA,CAAAgB,SAAA,eACK;IAGXhB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAGJH,EADF,CAAAC,cAAA,cAA+B,kBAC8C;IAA3DD,EAAA,CAAAyF,UAAA,mBAAAU,kEAAA;MAAAnG,EAAA,CAAA4F,aAAA,CAAAC,IAAA;MAAA,MAAAvF,MAAA,GAAAN,EAAA,CAAA8F,aAAA;MAAA,OAAA9F,EAAA,CAAA+F,WAAA,CAASzF,MAAA,CAAA8F,MAAA,EAAQ;IAAA,EAAC;IAChCpG,EAAA,CAAAgB,SAAA,eAA+C;IAAChB,EAAA,CAAAE,MAAA,cAClD;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA4E;IAA5DD,EAAA,CAAAyF,UAAA,mBAAAY,kEAAA;MAAArG,EAAA,CAAA4F,aAAA,CAAAC,IAAA;MAAA,MAAAvF,MAAA,GAAAN,EAAA,CAAA8F,aAAA;MAAA,OAAA9F,EAAA,CAAA+F,WAAA,CAASzF,MAAA,CAAAgG,QAAA,EAAU;IAAA,EAAC;IAClCtG,EAAA,CAAAE,MAAA,gBAAO;IAAAF,EAAA,CAAAgB,SAAA,eAA2D;IAEtEhB,EADE,CAAAG,YAAA,EAAS,EACL;;;;ADjUV,OAAM,MAAOoG,eAAe;EAO1BC,YACUC,MAAc,EACdC,KAAqB,EACrBC,YAA0B,EAC1BC,cAAyC,EAC1C5E,cAA8B,EAC9B6E,WAAwB;IALvB,KAAAJ,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACf,KAAA5E,cAAc,GAAdA,cAAc;IACd,KAAA6E,WAAW,GAAXA,WAAW;IAZb,KAAAtG,kBAAkB,GAA6B,EAA8B;IAC7E,KAAAuG,mBAAmB,GAAY,KAAK;IACpC,KAAAC,mBAAmB,GAAY,KAAK;IACnC,KAAAC,IAAI,GAAG,IAAIjH,OAAO,EAAE;IACpB,KAAAkH,aAAa,GAAY,EAAa;IAC9C,KAAAC,gBAAgB,GAAG,KAAK;EAQpB;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACP,cAAc,CAACQ,mBAAmB,CAAC,CAAC,CAAC;IAC1C,IAAI,CAACC,+BAA+B,EAAE;IACtC,IAAI,CAACL,IAAI,CAACM,GAAG,CAAC,IAAI,CAACV,cAAc,CAACW,0BAA0B,EAAE,CAACC,SAAS,CAAEC,wBAAkD,IAAI;MAC9H,IAAI,CAAClH,kBAAkB,GAAGkH,wBAAwB;MAClDC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACf,cAAc,CAACgB,aAAa,CAAC;IAChD,CAAC,CAAC,CAAC;IAEH;IACA;IACA;EAEF;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACb,IAAI,CAACc,WAAW,EAAE;EACzB;EAEA9B,gBAAgBA,CAAC+B,KAAU;IACzB,IAAI,CAACjB,mBAAmB,GAAGiB,KAAK,CAACC,OAAO;EAC1C;EAEA9B,gBAAgBA,CAAC6B,KAAU;IACzB,IAAI,CAAChB,mBAAmB,GAAGgB,KAAK,CAACC,OAAO;EAC1C;EAEA1B,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACQ,mBAAmB,IAAI,CAAC,IAAI,CAACC,mBAAmB,EAAE;MAE1D,IAAI,CAACJ,YAAY,CAACsB,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;MACF;IACF;IAEA,IAAI,CAACxB,cAAc,CAACyB,eAAe,CAAC;MAAEC,SAAS,EAAE,IAAI,CAACzB,WAAW,CAAC0B,SAAS;IAAE,CAAE,CAAC;EAClF;EAEAnC,MAAMA,CAAA;IACJ,IAAI,CAACK,MAAM,CAAC+B,aAAa,CAAC,uBAAuB,EAAE;MAAEC,UAAU,EAAE;IAAI,CAAE,CAAC;EAC1E;EAEA;;;;;EAKQpB,+BAA+BA,CAAA;IACrC,IAAI,CAACL,IAAI,CAACM,GAAG,CAAC,IAAI,CAACZ,KAAK,CAACgC,WAAW,CAAClB,SAAS,CAAEmB,WAAmB,IAAI;MACrE,IAAIA,WAAW,IAAIA,WAAW,CAACC,OAAO,EAAE;QACtC,IAAI,CAAC1B,gBAAgB,GAAGyB,WAAW,CAACC,OAAO,KAAK,GAAG;MACrD;IACF,CAAC,CAAC,CAAC;EACL;EAAC,QAAAC,CAAA,G;qBAvEUtC,eAAe,EAAAvG,EAAA,CAAA8I,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAhJ,EAAA,CAAA8I,iBAAA,CAAAC,EAAA,CAAAE,cAAA,GAAAjJ,EAAA,CAAA8I,iBAAA,CAAAI,EAAA,CAAAC,YAAA,GAAAnJ,EAAA,CAAA8I,iBAAA,CAAAM,EAAA,CAAAC,yBAAA,GAAArJ,EAAA,CAAA8I,iBAAA,CAAAQ,EAAA,CAAAC,cAAA,GAAAvJ,EAAA,CAAA8I,iBAAA,CAAAU,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAfnD,eAAe;IAAAoD,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCbtBjK,EAFN,CAAAC,cAAA,aAA4B,aAC4B,aAChB;QAAAD,EAAA,CAAAE,MAAA,mBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACpDH,EAAA,CAAAa,UAAA,IAAAsJ,8BAAA,mBAAkG;QAwDpGnK,EAAA,CAAAG,YAAA,EAAM;QAGJH,EADF,CAAAC,cAAA,aAAoD,aAChB;QAAAD,EAAA,CAAAE,MAAA,mBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACpDH,EAAA,CAAAa,UAAA,IAAAuJ,8BAAA,kBAAkG;QAwBhGpK,EADF,CAAAC,cAAA,aAA4B,cACQ;QAAAD,EAAA,CAAAE,MAAA,iBAAS;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACjDH,EAAA,CAAAa,UAAA,KAAAwJ,+BAAA,iBAAqE;QAwCzErK,EADE,CAAAG,YAAA,EAAM,EACF;QAGJH,EADF,CAAAC,cAAA,cAAoD,cAChB;QAAAD,EAAA,CAAAE,MAAA,2BAAmB;QAAAF,EAAA,CAAAG,YAAA,EAAM;QAC3DH,EAAA,CAAAa,UAAA,KAAAyJ,+BAAA,kBAA0E;QA4C5EtK,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAa,UAAA,KAAA0J,+BAAA,kBAGC;QAsCCvK,EADF,CAAAC,cAAA,cAAoD,cAChB;QAAAD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAM;QACpDH,EAAA,CAAAa,UAAA,KAAA2J,+BAAA,kBAAqE;QAwFvExK,EAAA,CAAAG,YAAA,EAAM;QACNH,EAAA,CAAAa,UAAA,KAAA4J,wCAAA,2BAAwC;QA6B1CzK,EAAA,CAAAG,YAAA,EAAM;;;QA/UgEH,EAAA,CAAAI,SAAA,GAA8B;QAA9BJ,EAAA,CAAAqB,UAAA,SAAA6I,GAAA,CAAA3J,kBAAA,CAAAC,KAAA,CAA8B;QA4D9BR,EAAA,CAAAI,SAAA,GAA8B;QAA9BJ,EAAA,CAAAqB,UAAA,SAAA6I,GAAA,CAAA3J,kBAAA,CAAAC,KAAA,CAA8B;QAyBzDR,EAAA,CAAAI,SAAA,GAA8B;QAA9BJ,EAAA,CAAAqB,UAAA,SAAA6I,GAAA,CAAA3J,kBAAA,CAAAkC,KAAA,CAA8B;QA4C3BzC,EAAA,CAAAI,SAAA,GAA8B;QAA9BJ,EAAA,CAAAqB,UAAA,SAAA6I,GAAA,CAAA3J,kBAAA,CAAA2C,KAAA,CAA8B;QAgDvElD,EAAA,CAAAI,SAAA,EAA8B;QAA9BJ,EAAA,CAAAqB,UAAA,SAAA6I,GAAA,CAAA3J,kBAAA,CAAAiD,KAAA,CAA8B;QAwCMxD,EAAA,CAAAI,SAAA,GAA8B;QAA9BJ,EAAA,CAAAqB,UAAA,SAAA6I,GAAA,CAAA3J,kBAAA,CAAAwE,KAAA,CAA8B;QAyFtD/E,EAAA,CAAAI,SAAA,EAAuB;QAAvBJ,EAAA,CAAAqB,UAAA,UAAA6I,GAAA,CAAAhD,gBAAA,CAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}