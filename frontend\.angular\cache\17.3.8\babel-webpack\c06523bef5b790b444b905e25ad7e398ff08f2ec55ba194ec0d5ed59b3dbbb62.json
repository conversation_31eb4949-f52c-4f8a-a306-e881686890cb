{"ast": null, "code": "import { catchError, map, switchMap } from 'rxjs/operators';\nimport { SubSink } from 'subsink';\nimport { UserRole } from 'src/app/core/models/user.model';\nimport { of, throwError } from 'rxjs';\nimport * as moment from 'moment';\nimport { LessonStatus } from 'src/app/core/models/lesson.model';\nimport { HttpHeaders } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/layout.service\";\nimport * as i2 from \"src/app/core/services/auth.service\";\nimport * as i3 from \"src/app/core/services/general.service\";\nimport * as i4 from \"src/app/core/services/classroom.service\";\nimport * as i5 from \"src/app/core/services/calendar.service\";\nimport * as i6 from \"src/app/core/services/user.service\";\nimport * as i7 from \"src/app/core/services/toast.service\";\nimport * as i8 from \"../../shared/lesson/lessons-history/lessons-history.component\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"../../shared/availability/availability.component\";\nimport * as i12 from \"../../shared/block-viewer/block-viewer.component\";\nimport * as i13 from \"@angular/router\";\nimport * as i14 from \"primeng/scrollpanel\";\nimport * as i15 from \"primeng/dropdown\";\nimport * as i16 from \"./weekly-availability-calendar/weekly-availability-calendar.component\";\nimport * as i17 from \"./overlay-panel-block/overlay-panel-block.component\";\nimport * as i18 from \"./overlay-panel-block/awaiting-approval-notification/awaiting-approval-notification.component\";\nimport * as i19 from \"./overlay-panel-block/recurring-lessons-block/recurring-lessons-block.component\";\nimport * as i20 from \"./overlay-panel-block/teacher-days-off-panel/teacher-days-off-panel.component\";\nconst _c0 = [\"calendarWrapper\"];\nconst _c1 = [\"calendarContainer\"];\nconst CalendarComponent_Defer_4_DepsFn = () => [i9.NgControlStatus, i9.NgModel, i10.NgClass, i10.NgForOf, i10.NgIf, i11.AvailabilityComponent, i12.BlockViewerComponent, i13.RouterLink, i14.ScrollPanel, i15.Dropdown, i16.WeeklyAvailabilityCalendarComponent, i17.OverlayPanelBlockComponent, i18.AwaitingApprovalNotificationComponent, i19.RecurringLessonsBlockComponent, i20.TeacherDaysOffPanelComponent];\nconst _c2 = a0 => ({\n  \"pointer-events-none opacity-40\": a0\n});\nconst _c3 = () => ({\n  width: \"100%\"\n});\nfunction CalendarComponent_Defer_2_div_1_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-awaiting-approval-notification\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const request_r3 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"index\", i_r4 + 1)(\"lesson\", request_r3.lesson)(\"lessonId\", request_r3.id)(\"avatar\", request_r3.avatar)(\"requestedName\", request_r3.requestedName)(\"date\", request_r3.date)(\"time\", request_r3.time)(\"duration\", ctx_r1.generalService.convertHoursToMinutesWithSuffix(request_r3.duration));\n  }\n}\nfunction CalendarComponent_Defer_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12)(2, \"button\", 13)(3, \"div\", 14);\n    i0.ɵɵelement(4, \"img\", 15);\n    i0.ɵɵelementStart(5, \"span\", 16);\n    i0.ɵɵtext(6, \"Connect with \");\n    i0.ɵɵelementStart(7, \"b\");\n    i0.ɵɵtext(8, \"Google Calendar\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(9, \"div\", 17);\n    i0.ɵɵtext(10, \"Availability of \");\n    i0.ɵɵelementStart(11, \"p-dropdown\", 18);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function CalendarComponent_Defer_2_div_1_Template_p_dropdown_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedTeacher, $event) || (ctx_r1.selectedTeacher = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function CalendarComponent_Defer_2_div_1_Template_p_dropdown_ngModelChange_11_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTeacherChange($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 19)(13, \"div\", 20)(14, \"div\", 21)(15, \"app-overlay-panel-block\", 22)(16, \"div\", 23);\n    i0.ɵɵtemplate(17, CalendarComponent_Defer_2_div_1_div_17_Template, 2, 8, \"div\", 24);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 25);\n    i0.ɵɵelement(19, \"img\", 26);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"hidden\", true);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"options\", ctx_r1.availableTeachers);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedTeacher);\n    i0.ɵɵproperty(\"optionLabel\", ctx_r1.getTeacherLabel);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"role\", ctx_r1.role)(\"notificationsNumber\", ctx_r1.notificationsNumber);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.pendingRequests);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c2, !ctx_r1.showBookingSystemLink));\n  }\n}\nfunction CalendarComponent_Defer_2_div_2_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelement(1, \"app-awaiting-approval-notification\", 40);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const request_r5 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"role\", ctx_r1.role)(\"lesson\", request_r5.lesson)(\"lessonId\", request_r5.id)(\"index\", i_r6 + 1)(\"date\", request_r5.date)(\"time\", request_r5.time)(\"avatar\", request_r5.avatar)(\"requestedName\", request_r5.requestedName)(\"duration\", ctx_r1.generalService.convertHoursToMinutesWithSuffix(request_r5.duration));\n  }\n}\nfunction CalendarComponent_Defer_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"div\", 30)(3, \"button\", 13)(4, \"div\", 14);\n    i0.ɵɵelement(5, \"img\", 31);\n    i0.ɵɵelementStart(6, \"span\", 16);\n    i0.ɵɵtext(7, \"Connect with \");\n    i0.ɵɵelementStart(8, \"b\");\n    i0.ɵɵtext(9, \"Google Calendar\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(10, \"div\", 32)(11, \"div\", 30)(12, \"app-overlay-panel-block\", 33);\n    i0.ɵɵelement(13, \"app-teacher-days-off-panel\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 29)(15, \"div\", 30)(16, \"app-overlay-panel-block\", 34);\n    i0.ɵɵelement(17, \"app-recurring-lessons-block\", 35);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 21)(19, \"div\", 30)(20, \"app-overlay-panel-block\", 36)(21, \"p-scrollPanel\", 37, 1);\n    i0.ɵɵelement(23, \"app-availability\", 38);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(24, \"div\", 21)(25, \"div\", 30)(26, \"app-overlay-panel-block\", 39)(27, \"div\", 23);\n    i0.ɵɵtemplate(28, CalendarComponent_Defer_2_div_2_div_28_Template, 2, 9, \"div\", 24);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(29, \"div\", 25);\n    i0.ɵɵelement(30, \"img\", 26);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"hidden\", true);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"hidden\", true)(\"role\", ctx_r1.role);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"hidden\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"role\", ctx_r1.role);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"classrooms\", ctx_r1.classrooms);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"role\", ctx_r1.role)(\"panelWidth\", ctx_r1.isTabletOrMobile ? \"100%\" : \"26rem\");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(18, _c3));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"showHours\", false)(\"isDialog\", true)(\"user\", ctx_r1.user);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"role\", ctx_r1.role)(\"notificationsNumber\", ctx_r1.notificationsNumber)(\"panelWidth\", ctx_r1.isTabletOrMobile ? \"90%\" : \"22rem\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.pendingRequests);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(19, _c2, !ctx_r1.showBookingSystemLink));\n  }\n}\nfunction CalendarComponent_Defer_2_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 41, 2)(3, \"app-weekly-availability-calendar\", 42);\n    i0.ɵɵlistener(\"availableTeachersChanged\", function CalendarComponent_Defer_2_ng_container_3_Template_app_weekly_availability_calendar_availableTeachersChanged_3_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onAvailableTeacherChange($event));\n    })(\"calendarRangeDatesChanged\", function CalendarComponent_Defer_2_ng_container_3_Template_app_weekly_availability_calendar_calendarRangeDatesChanged_3_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onCalendarRangeDatesChanged($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 43)(5, \"div\", 44)(6, \"div\", 14);\n    i0.ɵɵelement(7, \"div\", 45);\n    i0.ɵɵtext(8, \" Availability \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 14)(10, \"div\", 46);\n    i0.ɵɵelement(11, \"img\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" Arranged \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 14)(14, \"div\", 48);\n    i0.ɵɵelement(15, \"img\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16, \" Completed \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 14)(18, \"div\", 50);\n    i0.ɵɵelement(19, \"img\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20, \" Trial Arranged \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 14)(22, \"div\", 51);\n    i0.ɵɵelement(23, \"img\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(24, \" Trial Completed \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 14)(26, \"div\", 52);\n    i0.ɵɵelement(27, \"img\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(28, \" Requested \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"calendarHeight\", ctx_r1.containerHeight)(\"businessHours\", ctx_r1.businessHours)(\"dayOffEvents\", ctx_r1.dayOffEvents)(\"lessonEvents\", ctx_r1.lessonEvents)(\"selectedTeacher\", ctx_r1.selectedTeacher)(\"isTabletOrMobile\", ctx_r1.isTabletOrMobile);\n  }\n}\nfunction CalendarComponent_Defer_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"app-block-viewer\", 5);\n    i0.ɵɵtemplate(1, CalendarComponent_Defer_2_div_1_Template, 20, 10, \"div\", 6)(2, CalendarComponent_Defer_2_div_2_Template, 31, 21, \"div\", 7)(3, CalendarComponent_Defer_2_ng_container_3_Template, 29, 6, \"ng-container\", 8);\n    i0.ɵɵelementStart(4, \"div\", 9);\n    i0.ɵɵelement(5, \"div\", 10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"headerClass\", \"justify-content-center my-2\")(\"headerTextClass\", \"font-xl font-semibold justify-content-center\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.role == \"Student\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.role == \"Teacher\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showCalendar);\n  }\n}\nfunction CalendarComponent_DeferPlaceholder_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport let CalendarComponent = /*#__PURE__*/(() => {\n  class CalendarComponent {\n    constructor(layoutService, authService, generalService, classroomService, calendarService, userService, toastService, cdr) {\n      this.layoutService = layoutService;\n      this.authService = authService;\n      this.generalService = generalService;\n      this.classroomService = classroomService;\n      this.calendarService = calendarService;\n      this.userService = userService;\n      this.toastService = toastService;\n      this.cdr = cdr;\n      this.subs = new SubSink();\n      this.calendarWrapper = {};\n      this.role = \"\";\n      this.teacher = [];\n      this.user = {};\n      this.selectedTeacher = null;\n      this.availableTeachers = [];\n      this.containerHeight = '';\n      this.businessHours = [];\n      this.dayOffEvents = [];\n      this.lessonEvents = [];\n      this.filteredLessonEvents = [];\n      this.showBookingSystemLink = false;\n      this.classrooms = [];\n      this.showCalendar = false;\n      this.calendarStartDate = new Date();\n      this.calendarEndDate = new Date();\n      this.UserRoles = UserRole;\n      this.pendingRequests = [];\n      this.lessonStatus = LessonStatus;\n      this.isTabletOrMobile = false;\n    }\n    ngOnInit() {\n      this.teacher = [];\n      this.role = this.authService.getLoggedInUser().role;\n      this.user = this.authService.getLoggedInUser();\n      this.notificationsNumber = 0;\n      this.subs.sink = this.generalService.deviceKind.pipe(switchMap(res => {\n        this.isTabletOrMobile = !res.w992up;\n        if (!res.w1024up) {\n          return of(0);\n        } else {\n          return this.layoutService.sideMenuHeight;\n        }\n      })).subscribe(res => {\n        if (res === 0) {\n          this.containerHeight = '100vh';\n        } else if (res !== 0) {\n          const menuHeight = res;\n          this.calendarWrapper.nativeElement.style.height = res + 'px';\n          this.containerHeight = menuHeight - 110 + 'px';\n          this.cdr.detectChanges();\n        }\n      });\n      this.fetchClassrooms();\n      this.initUpdateListener();\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n    }\n    getTeacherAvailability(teacher) {\n      if (!this.createAvailabilityRequestObject()) {\n        return of(null);\n      }\n      return this.calendarService.retrieveCalendarAvailability(this.createAvailabilityRequestObject()).pipe(map(res => {\n        const daysOff = res.daysOff;\n        // Set business hours and select constraint\n        this.businessHours = this.calendarService.getBusinessHours(res.calendarAvailability);\n        // Create events for each day off\n        this.dayOffEvents = this.calendarService.createDayOffEvents(daysOff);\n        return null;\n      }), catchError(error => {\n        if (error.status === 400) {\n          // Handle the 400 error here\n          console.log('Bad Request - Invalid input');\n        }\n        return throwError(error);\n      }));\n    }\n    filterLessonsByTeacherId(lessons, teacherId) {\n      return lessons.filter(lesson => lesson.teacher.id === teacherId);\n    }\n    onTeacherChange(event) {\n      if (!this.selectedTeacher) {\n        return;\n      }\n      console.log('Selected teacher:', this.selectedTeacher);\n      // Do something else here\n      this.businessHours = [];\n      this.subs.add(this.getTeacherAvailability(this.selectedTeacher).pipe(switchMap(teacherAvailability => {\n        // Use teacherAvailability to make another request\n        return this.classroomService.getAvailableHoursWithTeacher(this.selectedTeacher.aspUserId);\n      })).subscribe(availableHours => {\n        this.filteredLessonEvents = this.lessonEvents;\n        this.calendarService.setTeacherChangeListener(true);\n      }));\n    }\n    onAvailableTeacherChange(event) {\n      console.log(event);\n      this.teacher = event;\n    }\n    onCalendarRangeDatesChanged(event) {\n      this.businessHours = [];\n      console.log(event);\n      this.calendarStartDate = event.fromDate;\n      this.calendarEndDate = event.toDate;\n      let userId = null;\n      if (this.role === UserRole.STUDENT) {\n        userId = this.selectedTeacher.aspUserId;\n      } else {\n        userId = this.user.aspUserId;\n      }\n      event.userId = userId;\n      if (!userId) {\n        return;\n      }\n      const headers = new HttpHeaders({\n        'X-Bypass-Error-Interceptor': 'true'\n      });\n      this.subs.sink = this.calendarService.retrieveCalendarAvailability(event, headers).pipe(catchError(error => {\n        // Handle the error here\n        console.error('An error occurred:', error);\n        if (error.status === 400) {\n          this.toastService.setShowToastmessage({\n            severity: 'info',\n            summary: '',\n            detail: 'Check if you have set your availability, if the problem persists contact us.'\n          });\n        }\n        // You can also throw a new error if needed\n        return throwError('Something went wrong');\n      })).subscribe(res => {\n        this.businessHours = this.calendarService.getBusinessHours(res.calendarAvailability);\n        this.dayOffEvents = this.calendarService.createDayOffEvents(res.daysOff);\n      });\n    }\n    getTeacherLabel(teacher) {\n      return `${teacher.firstName} ${teacher.lastName}`;\n    }\n    /**\n     * Creates an availability request object.\n     * @returns The availability request object.\n     */\n    createAvailabilityRequestObject() {\n      // Determine the ASP user ID based on the role\n      const aspUserId = this.role === UserRole.STUDENT ? this.selectedTeacher?.aspUserId ?? null : this.user?.aspUserId ?? null;\n      // If the ASP user ID is not available, return null\n      if (!aspUserId) {\n        return null;\n      }\n      // Create and return the availability request object\n      return {\n        fromDate: this.calendarStartDate,\n        toDate: this.calendarEndDate,\n        userId: aspUserId\n      };\n    }\n    getPendingLessons(classroom) {\n      return this.classroomService.gePendingLessons(classroom.id).pipe(switchMap(res => {\n        return of(res);\n      }), catchError(error => {\n        console.error('Error getting pending lessons:', error);\n        // Handle the error or rethrow it if necessary\n        return throwError(error);\n      }));\n    }\n    fetchClassrooms() {\n      this.subs.add(this.classroomService.getLMSUserClassrooms(this.user.id).pipe(switchMap(classrooms => {\n        this.pendingRequests = [];\n        this.lessonEvents = [];\n        const availableTeachers = [];\n        this.notificationsNumber = 0;\n        classrooms.forEach(classroom => {\n          this.fetchPendingLessons(classroom, this.pendingRequests);\n          availableTeachers.push(classroom.teacher);\n          classroom.lessons.forEach(lesson => {\n            if (lesson.status.toLowerCase().includes(LessonStatus.CANCELED.toLowerCase()) || lesson.status.toLowerCase() === LessonStatus.NO_SHOW.toLowerCase()) {\n              return;\n            }\n            const start = new Date(lesson.startingDate);\n            const end = new Date(start.getTime() + lesson.duration * 60 * 60 * 1000);\n            const teacherName = this.generalService.getShortFullName(classroom.teacher);\n            this.lessonEvents.push({\n              id: lesson.id,\n              title: teacherName,\n              start: lesson.startingDate,\n              end: end,\n              allDay: false,\n              backgroundColor: '#378006',\n              borderColor: '#378006',\n              textColor: '#ffffff',\n              extendedProps: {\n                type: 'lesson',\n                lesson: lesson,\n                classroom: classroom\n              },\n              editable: false\n            });\n          });\n        });\n        const uniqueTeachers = availableTeachers.filter((value, index, self) => {\n          return self.map(x => x.id).indexOf(value.id) === index;\n        });\n        this.classrooms = classrooms;\n        this.availableTeachers = uniqueTeachers;\n        this.selectedTeacher = this.availableTeachers[0];\n        this.showCalendar = true;\n        return of(classrooms);\n      }), switchMap(classrooms => {\n        if (this.role === UserRole.STUDENT) {\n          return this.classroomService.getAvailableHoursWithTeacher(this.selectedTeacher.aspUserId);\n        } else {\n          return of(classrooms);\n        }\n      })).subscribe(res => {\n        if (this.role === UserRole.STUDENT) {\n          this.showBookingSystemLink = res;\n        } else {\n          this.showBookingSystemLink = this.classroomService.filterAvailableClassrooms(res).length > 0;\n        }\n      }));\n    }\n    fetchPendingLessons(classroom, pendingRequests) {\n      this.subs.add(this.getPendingLessons(classroom).subscribe(response => {\n        if (response.length === 0) return;\n        response.forEach(pendingLesson => {\n          let avatar = '';\n          console.log(pendingLesson);\n          if (pendingLesson.result === 'Pending') {\n            let studentRequestedLesson = classroom.classroomStudents.find(student => student.aspUserId === pendingLesson.requestedByAspUserId);\n            console.log(pendingLesson.duration);\n            if (studentRequestedLesson) {\n              avatar = this.role === UserRole.STUDENT ? classroom.teacher.avatarUrl : studentRequestedLesson.avatarUrl;\n            }\n            let name = this.role === UserRole.STUDENT ? `${classroom.teacher.firstName} ${classroom.teacher.lastName}` : pendingLesson.requestedByName;\n            pendingRequests.push({\n              id: pendingLesson.id,\n              lesson: pendingLesson,\n              requestedName: name,\n              avatar: avatar,\n              date: moment(pendingLesson.startingDate).format('DD/MM/YYYY'),\n              time: moment(pendingLesson.startingDate).format('HH:mm'),\n              duration: pendingLesson.duration\n            });\n            this.notificationsNumber++;\n          }\n        });\n      }, error => {\n        console.log(error);\n      }));\n    }\n    initUpdateListener() {\n      this.subs.add(this.calendarService.updateListener.subscribe(res => {\n        console.log(res);\n        if (res) {\n          this.onTeacherChange('');\n          this.fetchClassrooms();\n        }\n      }));\n    }\n    static #_ = this.ɵfac = function CalendarComponent_Factory(t) {\n      return new (t || CalendarComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.GeneralService), i0.ɵɵdirectiveInject(i4.ClassroomService), i0.ɵɵdirectiveInject(i5.CalendarService), i0.ɵɵdirectiveInject(i6.UserService), i0.ɵɵdirectiveInject(i7.ToastService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CalendarComponent,\n      selectors: [[\"app-calendar\"]],\n      viewQuery: function CalendarComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n          i0.ɵɵviewQuery(_c1, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.calendarWrapper = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.calendarContainer = _t.first);\n        }\n      },\n      decls: 8,\n      vars: 1,\n      consts: [[\"calendarWrapper\", \"\"], [\"scrollPanel\", \"\"], [\"calendarContainer\", \"\"], [1, \"classroom-cards-space\", \"py-8\"], [3, \"wrapperScrollHeight\"], [\"header\", \"Calendar\", \"headerBackgroundImage\", \"/assets/images/dashboard/calendar-banner.svg\", \"blockClass\", \"border-radius-bottom-10\", \"containerClass\", \"bg-white px-3 py-2 border-round-2xl \", 3, \"headerClass\", \"headerTextClass\"], [\"class\", \"grid justify-content-center lg:justify-content-end font-sm lg:h-3rem\", 4, \"ngIf\"], [\"class\", \"grid justify-content-end align-items-center gap-2 font-sm h-2rem\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"grid\"], [1, \"col\"], [1, \"grid\", \"justify-content-center\", \"lg:justify-content-end\", \"font-sm\", \"lg:h-3rem\"], [1, \"col\", \"pb-1\", \"col-fixed\", 3, \"hidden\"], [\"pbutton\", \"\", \"type\", \"button\", 1, \"p-element\", \"p-button-rounded\", \"p-button-sm\", \"p-button\", \"p-component\", \"py-1\", \"btn-gradient\"], [1, \"flex\", \"align-items-center\", \"gap-1\"], [\"src\", \"/assets/icons/google-calendar.svg\", \"height\", \"22\"], [1, \"font-sm\"], [1, \"col-fixed\", \"flex\", \"text-center\", \"pb-1\", \"font-sm\", \"align-items-center\", \"justify-content-center\"], [\"placeholder\", \"Select Teacher \", 1, \"inline-block\", \"ml-1\", 3, \"ngModelChange\", \"options\", \"ngModel\", \"optionLabel\"], [1, \"col-fixed\", \"text-right\", \"pb-1\", \"align-items-center\"], [1, \"grid\", \"grid-nogutter\", \"gap-3\", \"left-grid\", \"awaitingApproval\", \"align-items-center\"], [1, \"col-fixed\", \"text-right\", \"pb-1\"], [\"selector\", \"awaitingApproval\", \"buttonIcon\", \"awaiting-approval\", \"buttonText\", \"Requested Lessons\", \"headIcon\", \"awaiting-approval-white\", \"headText\", \"Requested Lessons\", \"headBackgroundImage\", \"/assets/images/dashboard/awaiting-aproval-bg.png\", 3, \"role\", \"notificationsNumber\"], [1, \"overflow-y-scroll\", \"px-2\", 2, \"max-height\", \"300px\"], [4, \"ngFor\", \"ngForOf\"], [\"routerLink\", \"/dashboard/calendar/booking-system\", 1, \"col-fixed\", \"add-icon\", \"cursor-pointer\", 3, \"ngClass\"], [\"src\", \"/assets/icons/plus-icon-svg-cropped.svg\", \"alt\", \"\"], [3, \"index\", \"lesson\", \"lessonId\", \"avatar\", \"requestedName\", \"date\", \"time\", \"duration\"], [1, \"grid\", \"justify-content-end\", \"align-items-center\", \"gap-2\", \"font-sm\", \"h-2rem\"], [1, \"col-fixed\", \"text-right\", \"pb-1\", 3, \"hidden\"], [1, \"grid\", \"left-grid\", \"awaitingApproval\"], [\"src\", \"/assets/icons/google-calendar.svg\", \"height\", \"16\"], [1, \"col-fixed\", \"text-right\", \"pb-1\", \"p-0\"], [\"selector\", \"awaitingApproval\", \"buttonText\", \"Days off\", \"headText\", \"Add Days-Off for...\", \"headBackgroundImage\", \"/assets/images/dashboard/awaiting-aproval-bg.png\", 3, \"hidden\", \"role\"], [\"selector\", \"awaitingApproval\", \"buttonText\", \"Reccurring Lessons\", \"headText\", \"Reccurring Lessons\", \"headBackgroundImage\", \"/assets/images/dashboard/calendar/recurring-bg.png\", 3, \"role\"], [3, \"classrooms\"], [\"selector\", \"awaitingApproval\", \"buttonText\", \"Availability\", \"headText\", \"Availability\", \"headBackgroundImage\", \"/assets/images/dashboard/calendar/availability-dialog-bg.png\", 3, \"role\", \"panelWidth\"], [\"styleClass\", \"custombar1 h-20rem lg:max-h-30rem lg:min-h-22rem\"], [3, \"showHours\", \"isDialog\", \"user\"], [\"selector\", \"awaitingApproval\", \"buttonText\", \"Lesson Requests\", \"headIcon\", \"awaiting-approval-white\", \"headText\", \"Lesson Requests\", \"headBackgroundImage\", \"/assets/images/dashboard/awaiting-aproval-bg.png\", 3, \"role\", \"notificationsNumber\", \"panelWidth\"], [3, \"role\", \"lesson\", \"lessonId\", \"index\", \"date\", \"time\", \"avatar\", \"requestedName\", \"duration\"], [1, \"relative\", \"mt-2\"], [3, \"availableTeachersChanged\", \"calendarRangeDatesChanged\", \"calendarHeight\", \"businessHours\", \"dayOffEvents\", \"lessonEvents\", \"selectedTeacher\", \"isTabletOrMobile\"], [1, \"border-round-xl\", \"p-1\", \"font-xs\"], [1, \"p-2\", \"flex\", \"flex-wrap\", \"gap-2\", \"flex-row\", \"align-items-start\", \"sm:align-items-center\", \"justify-content-start\", \"sm:justify-content-between\"], [1, \"circle-legend\", \"availability-bg-color\"], [1, \"circle-legend\", \"arranged-gradient-lesson-bg\"], [\"src\", \"/assets/icons/lessons/arranged.svg\", \"width\", \"12\"], [1, \"circle-legend\", \"completed-gradient-lesson-bg\"], [\"src\", \"/assets/icons/lessons/completed.svg\", \"width\", \"12\"], [1, \"circle-legend\", \"trial-arranged-gradient-lesson-bg\"], [1, \"circle-legend\", \"trial-completed-gradient-lesson-bg\"], [1, \"circle-legend\", \"requested-gradient-lesson-bg\"], [\"src\", \"/assets/icons/lessons/requested-white.svg\", \"width\", \"12\"]],\n      template: function CalendarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", null, 0);\n          i0.ɵɵtemplate(2, CalendarComponent_Defer_2_Template, 6, 5)(3, CalendarComponent_DeferPlaceholder_3_Template, 2, 0);\n          i0.ɵɵdefer(4, 2, CalendarComponent_Defer_4_DepsFn, null, 3);\n          i0.ɵɵdeferOnViewport(0, -1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 3);\n          i0.ɵɵelement(7, \"app-lessons-history\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"wrapperScrollHeight\", ctx.containerHeight);\n        }\n      },\n      dependencies: [i8.LessonsHistoryComponent],\n      styles: [\"p-dropdown .p-dropdown{width:100%;background-image:linear-gradient(to bottom,#fff,#b4b4fa33);border:none}p-dropdown .p-dropdown .p-inputtext{padding:.3rem 1rem;font-size:.85rem;font-family:Proxima Nova Regular}p-dropdown .p-dropdown-panel{box-shadow:none}p-dropdown .p-dropdown-panel .p-dropdown-items{background-image:linear-gradient(to bottom,#b4b4fa33,#8492f733,#3873f433);color:#2e3d90}p-dropdown .p-dropdown:not(.p-disabled).p-focus{border:0;box-shadow:none}.btn-gradient{background:transparent;background-image:linear-gradient(to bottom,#fff,#b4b4fa33);color:#2d2a4b}.left-grid .add-icon{padding-top:.4rem}app-overlay-panel-block{padding-bottom:.4rem}.circle-legend{height:1.3rem;width:1.3rem;border-radius:200px;display:flex;align-items:center;justify-content:center}.circle-legend.availability-bg-color{background:#eaedfb}.circle-legend.days-off-bg-color{background:linear-gradient(#8497ff,#2e3d90)}\\n\"],\n      encapsulation: 2\n    });\n  }\n  return CalendarComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}