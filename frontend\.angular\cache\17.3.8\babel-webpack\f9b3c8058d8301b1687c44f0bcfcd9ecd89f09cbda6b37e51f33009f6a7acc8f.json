{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { TopMenuContainerLayoutComponent } from 'src/app/shared/layout/top-menu-container-layout/top-menu-container-layout.component';\nimport { NotificationOverviewComponent } from './components/notification-overview/notification-overview.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: TopMenuContainerLayoutComponent,\n  children: [{\n    path: '',\n    component: NotificationOverviewComponent\n  }]\n}];\nexport class NotificationsRoutingModule {\n  static #_ = this.ɵfac = function NotificationsRoutingModule_Factory(t) {\n    return new (t || NotificationsRoutingModule)();\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: NotificationsRoutingModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [RouterModule.forChild(routes), RouterModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(NotificationsRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "TopMenuContainerLayoutComponent", "NotificationOverviewComponent", "routes", "path", "component", "children", "NotificationsRoutingModule", "_", "_2", "_3", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1", "exports"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\notifications\\notifications-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { TopMenuContainerLayoutComponent } from 'src/app/shared/layout/top-menu-container-layout/top-menu-container-layout.component';\r\nimport { NotificationOverviewComponent } from './components/notification-overview/notification-overview.component';\r\n\r\n\r\nconst routes: Routes = [\r\n  { path: '', component: TopMenuContainerLayoutComponent,\r\n  children: [\r\n    {\r\n      path: '',\r\n      component: NotificationOverviewComponent,\r\n    },\r\n  ]\r\n},\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forChild(routes)],\r\n  exports: [RouterModule]\r\n})\r\nexport class NotificationsRoutingModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,+BAA+B,QAAQ,qFAAqF;AACrI,SAASC,6BAA6B,QAAQ,oEAAoE;;;AAGlH,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEJ,+BAA+B;EACtDK,QAAQ,EAAE,CACR;IACEF,IAAI,EAAE,EAAE;IACRC,SAAS,EAAEH;GACZ;CAEJ,CACA;AAMD,OAAM,MAAOK,0BAA0B;EAAA,QAAAC,CAAA,G;qBAA1BD,0BAA0B;EAAA;EAAA,QAAAE,EAAA,G;UAA1BF;EAA0B;EAAA,QAAAG,EAAA,G;cAH3BV,YAAY,CAACW,QAAQ,CAACR,MAAM,CAAC,EAC7BH,YAAY;EAAA;;;2EAEXO,0BAA0B;IAAAK,OAAA,GAAAC,EAAA,CAAAb,YAAA;IAAAc,OAAA,GAF3Bd,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}