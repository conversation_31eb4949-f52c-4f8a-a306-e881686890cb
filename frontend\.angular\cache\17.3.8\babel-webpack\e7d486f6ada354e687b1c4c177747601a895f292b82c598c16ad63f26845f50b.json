{"ast": null, "code": "import { Observable } from 'rxjs';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/core/services/teacher-application.service\";\nimport * as i3 from \"src/app/core/services/toast.service\";\nimport * as i4 from \"src/app/core/services/general.service\";\nimport * as i5 from \"primeng/api\";\nimport * as i6 from \"../../../shared/upload-files/upload-files.component\";\nimport * as i7 from \"primeng/ripple\";\nconst _c0 = a0 => ({\n  size: null,\n  name: a0\n});\nconst _c1 = a0 => ({\n  file: a0\n});\nconst _c2 = a0 => [a0];\nexport class ResumeComponent {\n  constructor(router, teacherService, toastService, generalService, confirmationService) {\n    this.router = router;\n    this.teacherService = teacherService;\n    this.toastService = toastService;\n    this.generalService = generalService;\n    this.confirmationService = confirmationService;\n    this.UPLOAD_TEACHER_CV_URL = '/LMS/UploadTeacherCVAsync';\n    this.UPLOAD_TEACHER_PICTURE_URL = '/LMS/UploadTeacherPictureAsync';\n    this.filesUploaded = false;\n    this.submitted = false;\n    this.msgs = [];\n    this.submittedFiles = {};\n    this.fileUploadChanged = false;\n    this.subs = new SubSink();\n  }\n  ngOnInit() {\n    this.teacherService.setCurrentStepIndex(3);\n    this.subs.add(this.teacherService.getTeacherApplicationStep4().subscribe(res => {\n      this.submittedFiles = res;\n      this.teacherService.setStepValidWithBoolean(3, this.areFilesSubmitted(), 'teacher-resume-route');\n    }));\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n    this.teacherService.setSubmitOnMenuClickListener(false);\n  }\n  /**\n  Submits the resume information.\n  @returns {void}\n  */\n  submitResume() {\n    if (!this.areFilesSubmitted()) {\n      this.toastService.setShowToastmessage({\n        severity: 'warn',\n        summary: '',\n        detail: 'Please upload both files for your Resume.'\n      });\n    } else {\n      this.toastService.setShowToastmessage({\n        severity: 'success',\n        summary: '',\n        detail: 'Your Resume is updated.'\n      });\n      this.fileUploadChanged = false;\n      this.router.navigateByUrl('/teacher/availability', {\n        replaceUrl: true\n      });\n    }\n  }\n  goBack() {\n    this.router.navigateByUrl('/teacher/experience', {\n      replaceUrl: true\n    });\n  }\n  getFileName(fileName) {\n    return this.generalService.getFileName2(fileName);\n  }\n  /**\n  @function\n  @desc Handles the event when a file upload has finished.\n  @param {any} event - The event object that is returned after the file upload has finished.\n  @param {string} fileKind - A string indicating the type of file that was uploaded, either 'cv' or 'profilePicture'.\n  @returns {void}\n  */\n  uploadFinished(event, fileKind) {\n    console.log('', fileKind);\n    switch (fileKind) {\n      case 'cv':\n        this.submittedFiles.cv = event.filePath.dbPath;\n        break;\n      case 'photo':\n        console.log('', event.filePath.dbPath);\n        this.submittedFiles.profilePicture = event.filePath.dbPath;\n        break;\n      default:\n        break;\n    }\n    this.fileUploadChanged = true;\n    this.teacherService.setStepValidWithBoolean(3, this.areFilesSubmitted(), 'teacher-resume-route');\n  }\n  /**\n  Deletes a file from the list of submitted files.\n  @param {Event} event - The event that triggers this function.\n  @param {string} fileKind - The type of file to be deleted, either 'cv' or 'profilePicture'.\n  @returns {void}\n  */\n  deleteFileClicked(event, fileKind) {\n    this.submittedFiles[fileKind === 'cv' ? 'cv' : 'profilePicture'] = '';\n  }\n  /**\n  Check if files (CV and profile picture) are submitted\n  @returns {boolean} Returns true if both CV and profile picture files are submitted, false otherwise.\n  */\n  areFilesSubmitted() {\n    return this.submittedFiles.cv !== '' && this.submittedFiles.profilePicture !== '';\n  }\n  canDeactivate() {\n    if (this.fileUploadChanged) {\n      return new Observable(observer => {\n        this.confirmationService.confirm({\n          header: '',\n          key: 'stepLeaveConfirmation',\n          message: 'Are you sure you want to leave this page? Your changes will be lost.',\n          accept: () => {\n            observer.next(true);\n            observer.complete();\n          },\n          reject: () => {\n            observer.next(false);\n            observer.complete();\n          }\n        });\n      });\n    } else {\n      return true;\n    }\n  }\n  static #_ = this.ɵfac = function ResumeComponent_Factory(t) {\n    return new (t || ResumeComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.TeacherApplicationService), i0.ɵɵdirectiveInject(i3.ToastService), i0.ɵɵdirectiveInject(i4.GeneralService), i0.ɵɵdirectiveInject(i5.ConfirmationService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ResumeComponent,\n    selectors: [[\"app-resume\"]],\n    decls: 10,\n    vars: 28,\n    consts: [[1, \"grid\"], [\"title\", \"CV *\", 1, \"col-12\", \"sm:col-6\", 3, \"onUploadFinished\", \"onDeleteFileClicked\", \"multiple\", \"postUrl\", \"dndUI\", \"fileDropImage\", \"fileDropImageWidth\", \"isTeacherApplication\", \"fileAfterImage\", \"files\"], [\"title\", \"Profile Picture *\", 1, \"col-12\", \"sm:col-6\", 3, \"onDeleteFileClicked\", \"onUploadFinished\", \"multiple\", \"postUrl\", \"fileDropImage\", \"fileAfterImage\", \"fileDropImageWidth\", \"isTeacherApplication\", \"dndUI\", \"files\"], [1, \"btns\", \"md:ml-5\", \"mt-4\"], [\"pRipple\", \"\", 1, \"rounded-blue-button\", \"transparent\", 3, \"click\"], [\"src\", \"/assets/icons/arrow-left-blue.svg\"], [\"pRipple\", \"\", 1, \"rounded-blue-button\", 3, \"click\"], [\"src\", \"/assets/icons/arrow-right.svg\"]],\n    template: function ResumeComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"app-upload-files\", 1);\n        i0.ɵɵlistener(\"onUploadFinished\", function ResumeComponent_Template_app_upload_files_onUploadFinished_1_listener($event) {\n          return ctx.uploadFinished($event, \"cv\");\n        })(\"onDeleteFileClicked\", function ResumeComponent_Template_app_upload_files_onDeleteFileClicked_1_listener($event) {\n          return ctx.deleteFileClicked($event, \"cv\");\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(2, \"app-upload-files\", 2);\n        i0.ɵɵlistener(\"onDeleteFileClicked\", function ResumeComponent_Template_app_upload_files_onDeleteFileClicked_2_listener($event) {\n          return ctx.deleteFileClicked($event, \"photo\");\n        })(\"onUploadFinished\", function ResumeComponent_Template_app_upload_files_onUploadFinished_2_listener($event) {\n          return ctx.uploadFinished($event, \"photo\");\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"button\", 4);\n        i0.ɵɵlistener(\"click\", function ResumeComponent_Template_button_click_4_listener() {\n          return ctx.goBack();\n        });\n        i0.ɵɵelement(5, \"img\", 5);\n        i0.ɵɵtext(6, \" Back\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"button\", 6);\n        i0.ɵɵlistener(\"click\", function ResumeComponent_Template_button_click_7_listener() {\n          return ctx.submitResume();\n        });\n        i0.ɵɵtext(8, \"Next \");\n        i0.ɵɵelement(9, \"img\", 7);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"multiple\", false)(\"postUrl\", ctx.UPLOAD_TEACHER_CV_URL)(\"dndUI\", true)(\"fileDropImage\", \"/assets/icons/teacher-application/resume-cv-file.svg\")(\"fileDropImageWidth\", 60)(\"isTeacherApplication\", true)(\"fileAfterImage\", \"/assets/icons/teacher-application/cv-file-uploaded-icon.svg\")(\"files\", i0.ɵɵpureFunction1(20, _c2, i0.ɵɵpureFunction1(18, _c1, i0.ɵɵpureFunction1(16, _c0, ctx.submittedFiles.cv))));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"multiple\", false)(\"postUrl\", ctx.UPLOAD_TEACHER_PICTURE_URL)(\"fileDropImage\", \"/assets/icons/teacher-application/resume-photo-file.svg\")(\"fileAfterImage\", \"/assets/icons/teacher-application/cv-image-uploaded.svg\")(\"fileDropImageWidth\", 86)(\"isTeacherApplication\", true)(\"dndUI\", true)(\"files\", i0.ɵɵpureFunction1(26, _c2, i0.ɵɵpureFunction1(24, _c1, i0.ɵɵpureFunction1(22, _c0, ctx.submittedFiles.profilePicture))));\n      }\n    },\n    dependencies: [i6.UploadFilesComponent, i7.Ripple],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.modal[_ngcontent-%COMP%] {\\n  min-width: 50vw;\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown:not(.p-disabled):hover {\\n  border: 2px solid rgb(228, 185, 84);\\n}\\n\\n  .no-label > .p-button-label {\\n  display: none;\\n}\\n\\ninput[type=file][_ngcontent-%COMP%] {\\n  visibility: hidden;\\n  width: 10px;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%] {\\n  position: relative;\\n  padding: 10px;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%]::-webkit-calendar-picker-indicator {\\n  color: transparent;\\n  background: none;\\n  z-index: 1;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%]:before {\\n  color: transparent;\\n  background: none;\\n  display: block;\\n  font-family: \\\"FontAwesome\\\";\\n  content: \\\"\\\\f073\\\";\\n  \\n\\n  width: 20px;\\n  height: 25px;\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  color: #999;\\n}\\n\\n.image-preview[_ngcontent-%COMP%] {\\n  height: 10rem;\\n  margin: 1rem 0;\\n  border-radius: 50%;\\n  border: 3px solid var(--main-color);\\n}\\n\\n.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 100%;\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-header {\\n  padding: clamp(0.38rem, 0.47vw + 0.28rem, 0.75rem);\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-items .p-dropdown-item {\\n  margin-bottom: clamp(0.38rem, 0.47vw + 0.28rem, 0.75rem);\\n}\\n\\n.country-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: clamp(0.75rem, 0.31vw + 0.69rem, 1rem);\\n  font-size: clamp(0.88rem, 0.16vw + 0.84rem, 1rem);\\n}\\n.country-item[_ngcontent-%COMP%]   .country-name[_ngcontent-%COMP%] {\\n  white-space: pre-line; \\n\\n}\\n.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  \\n\\n\\n\\n\\n\\n  \\n\\n  display: inline-block;\\n  vertical-align: middle;\\n}\\n.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  clip-path: circle();\\n}\\n.country-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 32px;\\n  margin-right: 0.5rem;\\n}\\n.country-item[_ngcontent-%COMP%]   img.smaller[_ngcontent-%COMP%] {\\n  width: 16px;\\n}\\n\\n.info-element[_ngcontent-%COMP%] {\\n  padding: 10px 0;\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  width: 18.75rem;\\n  height: 18.75rem;\\n  padding: 2rem;\\n  border-radius: 1.25rem;\\n  text-align: center;\\n  position: relative;\\n  margin: 0 auto;\\n  background: rgba(242, 246, 255, 0.68);\\n  border: 0.0625rem dashed rgba(52, 131, 226, 0.61);\\n}\\n.container[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  position: absolute;\\n  z-index: 2;\\n  width: 100%;\\n  height: 100%;\\n  top: 0;\\n  left: 0;\\n}\\n.container[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  color: var(--light-purple);\\n  padding: 0.5rem 1rem;\\n  font-weight: bold;\\n  cursor: pointer;\\n}\\n.container[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]:hover {\\n  color: var(--main-color);\\n}\\n.container[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #38424c;\\n}\\n\\n.fileover[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_shake 1s;\\n  animation-iteration-count: infinite;\\n}\\n\\n.files-list[_ngcontent-%COMP%] {\\n  margin-top: 1.5rem;\\n}\\n.files-list[_ngcontent-%COMP%]   .single-file[_ngcontent-%COMP%] {\\n  display: flex;\\n  padding: 0.5rem;\\n  justify-content: space-between;\\n  align-items: center;\\n  border: dashed 0.0625rem #979797;\\n  margin-bottom: 1rem;\\n  display: flex;\\n  flex-grow: 1;\\n}\\n.files-list[_ngcontent-%COMP%]   .single-file[_ngcontent-%COMP%]   .delete[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-left: 0.5rem;\\n  cursor: pointer;\\n  align-self: flex-end;\\n}\\n.files-list[_ngcontent-%COMP%]   .single-file[_ngcontent-%COMP%]   .name[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  color: #353f4a;\\n  margin: 0;\\n}\\n.files-list[_ngcontent-%COMP%]   .single-file[_ngcontent-%COMP%]   .size[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n  color: #a4a4a4;\\n  margin: 0;\\n  margin-bottom: 0.25rem;\\n}\\n.files-list[_ngcontent-%COMP%]   .single-file[_ngcontent-%COMP%]   .info[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.progress-cont[_ngcontent-%COMP%] {\\n  height: 0.4375rem;\\n  width: 100%;\\n  border-radius: 0.25rem;\\n  background-color: #d0d0d0;\\n  position: relative;\\n}\\n.progress-cont[_ngcontent-%COMP%]   .progress[_ngcontent-%COMP%] {\\n  width: 0;\\n  height: 100%;\\n  position: absolute;\\n  z-index: 1;\\n  top: 0;\\n  left: 0;\\n  border-radius: 0.25rem;\\n  background-color: #4c97cb;\\n  transition: 0.5s all;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_shake {\\n  0% {\\n    transform: translate(0.0625rem, 0.0625rem) rotate(0deg);\\n  }\\n  10% {\\n    transform: translate(-0.0625rem, -0.125rem) rotate(-1deg);\\n  }\\n  20% {\\n    transform: translate(-0.1875rem, 0rem) rotate(1deg);\\n  }\\n  30% {\\n    transform: translate(0.1875rem, 0.125rem) rotate(0deg);\\n  }\\n  40% {\\n    transform: translate(0.0625rem, -0.0625rem) rotate(1deg);\\n  }\\n  50% {\\n    transform: translate(-0.0625rem, 0.125rem) rotate(-1deg);\\n  }\\n  60% {\\n    transform: translate(-0.1875rem, 0.0625rem) rotate(0deg);\\n  }\\n  70% {\\n    transform: translate(0.1875rem, 0.0625rem) rotate(-1deg);\\n  }\\n  80% {\\n    transform: translate(-0.0625rem, -0.0625rem) rotate(1deg);\\n  }\\n  90% {\\n    transform: translate(0.0625rem, 0.125rem) rotate(0deg);\\n  }\\n  100% {\\n    transform: translate(0.0625rem, -0.125rem) rotate(-1deg);\\n  }\\n}\\n.btns[_ngcontent-%COMP%]   div[_ngcontent-%COMP%] {\\n  margin: 0.9375rem;\\n}\\n\\n.white-button[_ngcontent-%COMP%] {\\n  padding: 0.625rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Observable", "SubSink", "ResumeComponent", "constructor", "router", "teacherService", "toastService", "generalService", "confirmationService", "UPLOAD_TEACHER_CV_URL", "UPLOAD_TEACHER_PICTURE_URL", "filesUploaded", "submitted", "msgs", "submittedFiles", "fileUploadChanged", "subs", "ngOnInit", "setCurrentStepIndex", "add", "getTeacherApplicationStep4", "subscribe", "res", "setStepValidWithBoolean", "areFilesSubmitted", "ngOnDestroy", "unsubscribe", "setSubmitOnMenuClickListener", "submitResume", "setShowToastmessage", "severity", "summary", "detail", "navigateByUrl", "replaceUrl", "goBack", "getFileName", "fileName", "getFileName2", "uploadFinished", "event", "fileKind", "console", "log", "cv", "filePath", "db<PERSON><PERSON>", "profilePicture", "deleteFileClicked", "canDeactivate", "observer", "confirm", "header", "key", "message", "accept", "next", "complete", "reject", "_", "i0", "ɵɵdirectiveInject", "i1", "Router", "i2", "TeacherApplicationService", "i3", "ToastService", "i4", "GeneralService", "i5", "ConfirmationService", "_2", "selectors", "decls", "vars", "consts", "template", "ResumeComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵlistener", "ResumeComponent_Template_app_upload_files_onUploadFinished_1_listener", "$event", "ResumeComponent_Template_app_upload_files_onDeleteFileClicked_1_listener", "ɵɵelementEnd", "ResumeComponent_Template_app_upload_files_onDeleteFileClicked_2_listener", "ResumeComponent_Template_app_upload_files_onUploadFinished_2_listener", "ResumeComponent_Template_button_click_4_listener", "ɵɵelement", "ɵɵtext", "ResumeComponent_Template_button_click_7_listener", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction1", "_c2", "_c1", "_c0"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\teacher-application\\resume\\resume.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\teacher-application\\resume\\resume.component.html"], "sourcesContent": ["import { Component, OnDestroy, OnInit } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { ConfirmationService, Message } from 'primeng/api';\r\nimport { Observable } from 'rxjs';\r\nimport { TeacherApplicationStep4Response } from 'src/app/core/models/teacher.model';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { TeacherApplicationService } from 'src/app/core/services/teacher-application.service';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { SubSink } from 'subsink';\r\n\r\n@Component({\r\n  selector: 'app-resume',\r\n  templateUrl: './resume.component.html',\r\n  styleUrls: ['./resume.component.scss']\r\n})\r\nexport class ResumeComponent implements OnInit, OnDestroy {\r\n  public UPLOAD_TEACHER_CV_URL = '/LMS/UploadTeacherCVAsync';\r\n  public UPLOAD_TEACHER_PICTURE_URL = '/LMS/UploadTeacherPictureAsync';\r\n  filesUploaded: boolean = false;\r\n  submitted: boolean = false;\r\n  msgs: Message[] = [];\r\n  submittedFiles: { cv: string; profilePicture: string } = {} as { cv: string; profilePicture: string };\r\n  fileUploadChanged = false;\r\n  private subs = new SubSink();\r\n  constructor(\r\n    private router: Router,\r\n    private teacherService: TeacherApplicationService,\r\n    private toastService: ToastService,\r\n    private generalService: GeneralService,\r\n    private confirmationService: ConfirmationService,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.teacherService.setCurrentStepIndex(3);\r\n    this.subs.add(this.teacherService.getTeacherApplicationStep4().subscribe((res: TeacherApplicationStep4Response) => {\r\n      this.submittedFiles = res;\r\n      this.teacherService.setStepValidWithBoolean(3, this.areFilesSubmitted(), 'teacher-resume-route');\r\n\r\n    }));\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n    this.teacherService.setSubmitOnMenuClickListener(false);\r\n  }\r\n\r\n  /**\r\n  Submits the resume information.\r\n  @returns {void}\r\n  */\r\n  submitResume() {\r\n    if (!this.areFilesSubmitted()) {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'warn',\r\n        summary: '',\r\n        detail: 'Please upload both files for your Resume.'\r\n      });\r\n    } else {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'success',\r\n        summary: '',\r\n        detail: 'Your Resume is updated.'\r\n      });\r\n      this.fileUploadChanged = false;\r\n      this.router.navigateByUrl('/teacher/availability', {\r\n        replaceUrl: true\r\n      });\r\n    }\r\n  }\r\n\r\n  goBack() {\r\n    this.router.navigateByUrl('/teacher/experience', { replaceUrl: true });\r\n  }\r\n\r\n  getFileName(fileName: string): string {\r\n    return this.generalService.getFileName2(fileName);\r\n  }\r\n\r\n  /**\r\n  @function\r\n  @desc Handles the event when a file upload has finished.\r\n  @param {any} event - The event object that is returned after the file upload has finished.\r\n  @param {string} fileKind - A string indicating the type of file that was uploaded, either 'cv' or 'profilePicture'.\r\n  @returns {void}\r\n  */\r\n  uploadFinished(event: any, fileKind: string) {\r\n    console.log('',fileKind);\r\n    switch (fileKind) {\r\n      case 'cv':\r\n        this.submittedFiles.cv = event.filePath.dbPath;\r\n        break;\r\n      case 'photo':\r\n        console.log('',event.filePath.dbPath);\r\n        this.submittedFiles.profilePicture = event.filePath.dbPath;\r\n        break;\r\n      default:\r\n        break;\r\n    }\r\n    this.fileUploadChanged = true;\r\n    this.teacherService.setStepValidWithBoolean(3, this.areFilesSubmitted(), 'teacher-resume-route');\r\n  }\r\n\r\n  /**\r\n  Deletes a file from the list of submitted files.\r\n  @param {Event} event - The event that triggers this function.\r\n  @param {string} fileKind - The type of file to be deleted, either 'cv' or 'profilePicture'.\r\n  @returns {void}\r\n  */\r\n  deleteFileClicked(event: Event, fileKind: string) {\r\n    this.submittedFiles[fileKind === 'cv' ? 'cv' : 'profilePicture'] = '';\r\n  }\r\n\r\n  /**\r\n  Check if files (CV and profile picture) are submitted\r\n  @returns {boolean} Returns true if both CV and profile picture files are submitted, false otherwise.\r\n  */\r\n  areFilesSubmitted(): boolean {\r\n    return (this.submittedFiles.cv !== '' && this.submittedFiles.profilePicture !== '');\r\n  }\r\n  \r\n  canDeactivate(): Observable<boolean> | boolean {\r\n    if (this.fileUploadChanged) {\r\n      return new Observable((observer: any) => {\r\n        this.confirmationService.confirm({\r\n          header: '',\r\n          key: 'stepLeaveConfirmation',\r\n          message: 'Are you sure you want to leave this page? Your changes will be lost.',\r\n          accept: () => {\r\n            observer.next(true);\r\n            observer.complete();\r\n          },\r\n          reject: () => {\r\n            observer.next(false);\r\n            observer.complete();\r\n          }\r\n        });\r\n      });\r\n    } else {\r\n      return (true);\r\n    }\r\n  }\r\n}\r\n", "<div class=\"grid \">\r\n    <!--  style=\"display: flex; justify-content: center; gap: 20px; flex-wrap: wrap;\" -->\r\n    <app-upload-files class=\"col-12 sm:col-6\" [multiple]=\"false\" title=\"CV *\" \r\n    [postUrl]=\"UPLOAD_TEACHER_CV_URL\" [dndUI]=\"true\" \r\n    [fileDropImage]=\"'/assets/icons/teacher-application/resume-cv-file.svg'\" \r\n    [fileDropImageWidth]=\"60\" \r\n    [isTeacherApplication]=\"true\"\r\n    [fileAfterImage]=\"'/assets/icons/teacher-application/cv-file-uploaded-icon.svg'\" \r\n    (onUploadFinished)=\"uploadFinished($event, 'cv')\"\r\n    [files]=\"[{file: {size: null, name: submittedFiles.cv }}]\"\r\n    (onDeleteFileClicked)=\"deleteFileClicked($event, 'cv')\"></app-upload-files>\r\n    <app-upload-files class=\"col-12 sm:col-6\" [multiple]=\"false\" title=\"Profile Picture *\" \r\n    [postUrl]=\"UPLOAD_TEACHER_PICTURE_URL\" \r\n    [fileDropImage]=\"'/assets/icons/teacher-application/resume-photo-file.svg'\" \r\n    [fileAfterImage]=\"'/assets/icons/teacher-application/cv-image-uploaded.svg'\" \r\n    [fileDropImageWidth]=\"86\" \r\n    [isTeacherApplication]=\"true\"\r\n    [dndUI]=\"true\"\r\n    [files]=\"[{file: {size: null, name: (submittedFiles.profilePicture) }}]\"\r\n    (onDeleteFileClicked)=\"deleteFileClicked($event, 'photo')\"\r\n    (onUploadFinished)=\"uploadFinished($event, 'photo')\"></app-upload-files>\r\n</div>\r\n\r\n\r\n<div class=\"btns md:ml-5 mt-4\">\r\n    <button pRipple class=\"rounded-blue-button transparent\" (click)=\"goBack()\"> <img src=\"/assets/icons/arrow-left-blue.svg\" />\r\n        Back</button>\r\n    <button pRipple (click)=\"submitResume()\" class=\"rounded-blue-button\">Next <img src=\"/assets/icons/arrow-right.svg\" /></button>\r\n</div>"], "mappings": "AAGA,SAASA,UAAU,QAAQ,MAAM;AAKjC,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;;;;;;AAOjC,OAAM,MAAOC,eAAe;EAS1BC,YACUC,MAAc,EACdC,cAAyC,EACzCC,YAA0B,EAC1BC,cAA8B,EAC9BC,mBAAwC;IAJxC,KAAAJ,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAbtB,KAAAC,qBAAqB,GAAG,2BAA2B;IACnD,KAAAC,0BAA0B,GAAG,gCAAgC;IACpE,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,IAAI,GAAc,EAAE;IACpB,KAAAC,cAAc,GAA2C,EAA4C;IACrG,KAAAC,iBAAiB,GAAG,KAAK;IACjB,KAAAC,IAAI,GAAG,IAAIf,OAAO,EAAE;EAOxB;EAEJgB,QAAQA,CAAA;IACN,IAAI,CAACZ,cAAc,CAACa,mBAAmB,CAAC,CAAC,CAAC;IAC1C,IAAI,CAACF,IAAI,CAACG,GAAG,CAAC,IAAI,CAACd,cAAc,CAACe,0BAA0B,EAAE,CAACC,SAAS,CAAEC,GAAoC,IAAI;MAChH,IAAI,CAACR,cAAc,GAAGQ,GAAG;MACzB,IAAI,CAACjB,cAAc,CAACkB,uBAAuB,CAAC,CAAC,EAAE,IAAI,CAACC,iBAAiB,EAAE,EAAE,sBAAsB,CAAC;IAElG,CAAC,CAAC,CAAC;EACL;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACT,IAAI,CAACU,WAAW,EAAE;IACvB,IAAI,CAACrB,cAAc,CAACsB,4BAA4B,CAAC,KAAK,CAAC;EACzD;EAEA;;;;EAIAC,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACJ,iBAAiB,EAAE,EAAE;MAC7B,IAAI,CAAClB,YAAY,CAACuB,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAAC1B,YAAY,CAACuB,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;MACF,IAAI,CAACjB,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAACX,MAAM,CAAC6B,aAAa,CAAC,uBAAuB,EAAE;QACjDC,UAAU,EAAE;OACb,CAAC;IACJ;EACF;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAAC/B,MAAM,CAAC6B,aAAa,CAAC,qBAAqB,EAAE;MAAEC,UAAU,EAAE;IAAI,CAAE,CAAC;EACxE;EAEAE,WAAWA,CAACC,QAAgB;IAC1B,OAAO,IAAI,CAAC9B,cAAc,CAAC+B,YAAY,CAACD,QAAQ,CAAC;EACnD;EAEA;;;;;;;EAOAE,cAAcA,CAACC,KAAU,EAAEC,QAAgB;IACzCC,OAAO,CAACC,GAAG,CAAC,EAAE,EAACF,QAAQ,CAAC;IACxB,QAAQA,QAAQ;MACd,KAAK,IAAI;QACP,IAAI,CAAC3B,cAAc,CAAC8B,EAAE,GAAGJ,KAAK,CAACK,QAAQ,CAACC,MAAM;QAC9C;MACF,KAAK,OAAO;QACVJ,OAAO,CAACC,GAAG,CAAC,EAAE,EAACH,KAAK,CAACK,QAAQ,CAACC,MAAM,CAAC;QACrC,IAAI,CAAChC,cAAc,CAACiC,cAAc,GAAGP,KAAK,CAACK,QAAQ,CAACC,MAAM;QAC1D;MACF;QACE;IACJ;IACA,IAAI,CAAC/B,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACV,cAAc,CAACkB,uBAAuB,CAAC,CAAC,EAAE,IAAI,CAACC,iBAAiB,EAAE,EAAE,sBAAsB,CAAC;EAClG;EAEA;;;;;;EAMAwB,iBAAiBA,CAACR,KAAY,EAAEC,QAAgB;IAC9C,IAAI,CAAC3B,cAAc,CAAC2B,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,gBAAgB,CAAC,GAAG,EAAE;EACvE;EAEA;;;;EAIAjB,iBAAiBA,CAAA;IACf,OAAQ,IAAI,CAACV,cAAc,CAAC8B,EAAE,KAAK,EAAE,IAAI,IAAI,CAAC9B,cAAc,CAACiC,cAAc,KAAK,EAAE;EACpF;EAEAE,aAAaA,CAAA;IACX,IAAI,IAAI,CAAClC,iBAAiB,EAAE;MAC1B,OAAO,IAAIf,UAAU,CAAEkD,QAAa,IAAI;QACtC,IAAI,CAAC1C,mBAAmB,CAAC2C,OAAO,CAAC;UAC/BC,MAAM,EAAE,EAAE;UACVC,GAAG,EAAE,uBAAuB;UAC5BC,OAAO,EAAE,sEAAsE;UAC/EC,MAAM,EAAEA,CAAA,KAAK;YACXL,QAAQ,CAACM,IAAI,CAAC,IAAI,CAAC;YACnBN,QAAQ,CAACO,QAAQ,EAAE;UACrB,CAAC;UACDC,MAAM,EAAEA,CAAA,KAAK;YACXR,QAAQ,CAACM,IAAI,CAAC,KAAK,CAAC;YACpBN,QAAQ,CAACO,QAAQ,EAAE;UACrB;SACD,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAQ,IAAI;IACd;EACF;EAAC,QAAAE,CAAA,G;qBA7HUzD,eAAe,EAAA0D,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,yBAAA,GAAAL,EAAA,CAAAC,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAAP,EAAA,CAAAC,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAT,EAAA,CAAAC,iBAAA,CAAAS,EAAA,CAAAC,mBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAftE,eAAe;IAAAuE,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCbxBnB,EAFJ,CAAAqB,cAAA,aAAmB,0BAUyC;QAAxDrB,EAFA,CAAAsB,UAAA,8BAAAC,sEAAAC,MAAA;UAAA,OAAoBJ,GAAA,CAAAzC,cAAA,CAAA6C,MAAA,EAAuB,IAAI,CAAC;QAAA,EAAC,iCAAAC,yEAAAD,MAAA;UAAA,OAE1BJ,GAAA,CAAAhC,iBAAA,CAAAoC,MAAA,EAA0B,IAAI,CAAC;QAAA,EAAC;QAACxB,EAAA,CAAA0B,YAAA,EAAmB;QAC3E1B,EAAA,CAAAqB,cAAA,0BASqD;QAArDrB,EADA,CAAAsB,UAAA,iCAAAK,yEAAAH,MAAA;UAAA,OAAuBJ,GAAA,CAAAhC,iBAAA,CAAAoC,MAAA,EAA0B,OAAO,CAAC;QAAA,EAAC,8BAAAI,sEAAAJ,MAAA;UAAA,OACtCJ,GAAA,CAAAzC,cAAA,CAAA6C,MAAA,EAAuB,OAAO,CAAC;QAAA,EAAC;QACxDxB,EADyD,CAAA0B,YAAA,EAAmB,EACtE;QAIF1B,EADJ,CAAAqB,cAAA,aAA+B,gBACgD;QAAnBrB,EAAA,CAAAsB,UAAA,mBAAAO,iDAAA;UAAA,OAAST,GAAA,CAAA7C,MAAA,EAAQ;QAAA,EAAC;QAAEyB,EAAA,CAAA8B,SAAA,aAA+C;QACvH9B,EAAA,CAAA+B,MAAA,YAAI;QAAA/B,EAAA,CAAA0B,YAAA,EAAS;QACjB1B,EAAA,CAAAqB,cAAA,gBAAqE;QAArDrB,EAAA,CAAAsB,UAAA,mBAAAU,iDAAA;UAAA,OAASZ,GAAA,CAAApD,YAAA,EAAc;QAAA,EAAC;QAA6BgC,EAAA,CAAA+B,MAAA,YAAK;QAAA/B,EAAA,CAAA8B,SAAA,aAA2C;QACzH9B,EADyH,CAAA0B,YAAA,EAAS,EAC5H;;;QA1BwC1B,EAAA,CAAAiC,SAAA,EAAkB;QAO5DjC,EAP0C,CAAAkC,UAAA,mBAAkB,YAAAd,GAAA,CAAAvE,qBAAA,CAC3B,eAAe,yEACwB,0BAC/C,8BACI,iFACmD,UAAAmD,EAAA,CAAAmC,eAAA,KAAAC,GAAA,EAAApC,EAAA,CAAAmC,eAAA,KAAAE,GAAA,EAAArC,EAAA,CAAAmC,eAAA,KAAAG,GAAA,EAAAlB,GAAA,CAAAlE,cAAA,CAAA8B,EAAA,IAEtB;QAEhBgB,EAAA,CAAAiC,SAAA,EAAkB;QAO5DjC,EAP0C,CAAAkC,UAAA,mBAAkB,YAAAd,GAAA,CAAAtE,0BAAA,CACtB,4EACqC,6EACC,0BACnD,8BACI,eACf,UAAAkD,EAAA,CAAAmC,eAAA,KAAAC,GAAA,EAAApC,EAAA,CAAAmC,eAAA,KAAAE,GAAA,EAAArC,EAAA,CAAAmC,eAAA,KAAAG,GAAA,EAAAlB,GAAA,CAAAlE,cAAA,CAAAiC,cAAA,IAC0D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}