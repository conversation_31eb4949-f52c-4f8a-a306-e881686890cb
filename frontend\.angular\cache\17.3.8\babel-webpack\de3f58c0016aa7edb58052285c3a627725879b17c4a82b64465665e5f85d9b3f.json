{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { ChangeDetectionStrategy, Component } from '@angular/core';\nimport { PackagesModel, PackageType } from \"src/app/core/models/package.model\";\nlet BuyPackageSuggestionBoxComponent = class BuyPackageSuggestionBoxComponent {\n  constructor() {\n    this.defaultPackages = PackagesModel.getDefaultPackages();\n    this.selectedPackageType = this.defaultPackages[1].type;\n    this.packageTypeEnum = PackageType;\n  }\n  selectPackageType(type) {\n    this.selectedPackageType = type;\n  }\n  getPackages() {\n    return this.defaultPackages.filter(p => p.type !== 'Regular');\n  }\n  getSelectedPackage() {\n    return this.defaultPackages.find(pkg => pkg.type === this.selectedPackageType);\n  }\n};\nBuyPackageSuggestionBoxComponent = __decorate([Component({\n  selector: 'app-buy-package-suggestion-box',\n  templateUrl: './buy-package-suggestion-box.component.html',\n  styleUrl: './buy-package-suggestion-box.component.css',\n  changeDetection: ChangeDetectionStrategy.OnPush\n})], BuyPackageSuggestionBoxComponent);\nexport { BuyPackageSuggestionBoxComponent };", "map": {"version": 3, "names": ["ChangeDetectionStrategy", "Component", "PackagesModel", "PackageType", "BuyPackageSuggestionBoxComponent", "constructor", "defaultPackages", "getDefaultPackages", "selectedPackageType", "type", "packageTypeEnum", "selectPackageType", "getPackages", "filter", "p", "getSelectedPackage", "find", "pkg", "__decorate", "selector", "templateUrl", "styleUrl", "changeDetection", "OnPush"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\buy-package\\buy-package-suggestion-box\\buy-package-suggestion-box.component.ts"], "sourcesContent": ["import { CommonModule } from \"@angular/common\";\nimport { ChangeDetectionStrategy, Component } from '@angular/core';\nimport { PackagesModel, PackageType } from \"src/app/core/models/package.model\";\n\n@Component({\n    selector: 'app-buy-package-suggestion-box',\n    templateUrl: './buy-package-suggestion-box.component.html',\n    styleUrl: './buy-package-suggestion-box.component.css',\n    changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class BuyPackageSuggestionBoxComponent {\n\n    public defaultPackages = PackagesModel.getDefaultPackages();\n\n    selectedPackageType = this.defaultPackages[1].type;\n    packageTypeEnum = PackageType;\n\n    public selectPackageType(type: any) {\n        this.selectedPackageType = type;\n    }\n\n    getPackages() {\n        return this.defaultPackages.filter(p => p.type !== 'Regular');\n    }\n\n    public getSelectedPackage() {\n        return this.defaultPackages.find(pkg => pkg.type === this.selectedPackageType);\n    }\n}\n"], "mappings": ";AACA,SAASA,uBAAuB,EAAEC,SAAS,QAAQ,eAAe;AAClE,SAASC,aAAa,EAAEC,WAAW,QAAQ,mCAAmC;AAQvE,IAAMC,gCAAgC,GAAtC,MAAMA,gCAAgC;EAAtCC,YAAA;IAEI,KAAAC,eAAe,GAAGJ,aAAa,CAACK,kBAAkB,EAAE;IAE3D,KAAAC,mBAAmB,GAAG,IAAI,CAACF,eAAe,CAAC,CAAC,CAAC,CAACG,IAAI;IAClD,KAAAC,eAAe,GAAGP,WAAW;EAajC;EAXWQ,iBAAiBA,CAACF,IAAS;IAC9B,IAAI,CAACD,mBAAmB,GAAGC,IAAI;EACnC;EAEAG,WAAWA,CAAA;IACP,OAAO,IAAI,CAACN,eAAe,CAACO,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACL,IAAI,KAAK,SAAS,CAAC;EACjE;EAEOM,kBAAkBA,CAAA;IACrB,OAAO,IAAI,CAACT,eAAe,CAACU,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACR,IAAI,KAAK,IAAI,CAACD,mBAAmB,CAAC;EAClF;CACH;AAlBYJ,gCAAgC,GAAAc,UAAA,EAN5CjB,SAAS,CAAC;EACPkB,QAAQ,EAAE,gCAAgC;EAC1CC,WAAW,EAAE,6CAA6C;EAC1DC,QAAQ,EAAE,4CAA4C;EACtDC,eAAe,EAAEtB,uBAAuB,CAACuB;CAC5C,CAAC,C,EACWnB,gCAAgC,CAkB5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}