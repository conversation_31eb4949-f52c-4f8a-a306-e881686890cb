{"ast": null, "code": "import * as i1 from '@angular/common';\nimport { isPlatformServer, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, afterNextRender, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Header, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\nconst _c0 = [[[\"p-header\"]]];\nconst _c1 = [\"p-header\"];\nfunction Editor_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Editor_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵprojection(1);\n    i0.ɵɵtemplate(2, Editor_div_1_ng_container_2_Template, 1, 0, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.headerTemplate);\n  }\n}\nfunction Editor_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"span\", 5)(2, \"select\", 6)(3, \"option\", 7);\n    i0.ɵɵtext(4, \"Heading\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"option\", 8);\n    i0.ɵɵtext(6, \"Subheading\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"option\", 9);\n    i0.ɵɵtext(8, \"Normal\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"select\", 10)(10, \"option\", 9);\n    i0.ɵɵtext(11, \"Sans Serif\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"option\", 11);\n    i0.ɵɵtext(13, \"Serif\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"option\", 12);\n    i0.ɵɵtext(15, \"Monospace\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"span\", 5);\n    i0.ɵɵelement(17, \"button\", 13)(18, \"button\", 14)(19, \"button\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\", 5);\n    i0.ɵɵelement(21, \"select\", 16)(22, \"select\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 5);\n    i0.ɵɵelement(24, \"button\", 18)(25, \"button\", 19);\n    i0.ɵɵelementStart(26, \"select\", 20);\n    i0.ɵɵelement(27, \"option\", 9);\n    i0.ɵɵelementStart(28, \"option\", 21);\n    i0.ɵɵtext(29, \"center\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"option\", 22);\n    i0.ɵɵtext(31, \"right\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"option\", 23);\n    i0.ɵɵtext(33, \"justify\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"span\", 5);\n    i0.ɵɵelement(35, \"button\", 24)(36, \"button\", 25)(37, \"button\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"span\", 5);\n    i0.ɵɵelement(39, \"button\", 27);\n    i0.ɵɵelementEnd()();\n  }\n}\nconst EDITOR_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => Editor),\n  multi: true\n};\n/**\n * Editor groups a collection of contents in tabs.\n * @group Components\n */\nlet Editor = /*#__PURE__*/(() => {\n  class Editor {\n    el;\n    platformId;\n    /**\n     * Inline style of the container.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the container.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Placeholder text to show when editor is empty.\n     * @group Props\n     */\n    placeholder;\n    /**\n     * Whitelist of formats to display, see here for available options.\n     * @group Props\n     */\n    formats;\n    /**\n     * Modules configuration of Editor, see here for available options.\n     * @group Props\n     */\n    modules;\n    /**\n     * DOM Element or a CSS selector for a DOM Element, within which the editor’s p elements (i.e. tooltips, etc.) should be confined. Currently, it only considers left and right boundaries.\n     * @group Props\n     */\n    bounds;\n    /**\n     * DOM Element or a CSS selector for a DOM Element, specifying which container has the scrollbars (i.e. overflow-y: auto), if is has been changed from the default ql-editor with custom CSS. Necessary to fix scroll jumping bugs when Quill is set to auto grow its height, and another ancestor container is responsible from the scrolling..\n     * @group Props\n     */\n    scrollingContainer;\n    /**\n     * Shortcut for debug. Note debug is a static method and will affect other instances of Quill editors on the page. Only warning and error messages are enabled by default.\n     * @group Props\n     */\n    debug;\n    /**\n     * Whether to instantiate the editor to read-only mode.\n     * @group Props\n     */\n    get readonly() {\n      return this._readonly;\n    }\n    set readonly(val) {\n      this._readonly = val;\n      if (this.quill) {\n        if (this._readonly) this.quill.disable();else this.quill.enable();\n      }\n    }\n    /**\n     * Callback to invoke when the quill modules are loaded.\n     * @param {EditorInitEvent} event - custom event.\n     * @group Emits\n     */\n    onInit = new EventEmitter();\n    /**\n     * Callback to invoke when text of editor changes.\n     * @param {EditorTextChangeEvent} event - custom event.\n     * @group Emits\n     */\n    onTextChange = new EventEmitter();\n    /**\n     * Callback to invoke when selection of the text changes.\n     * @param {EditorSelectionChangeEvent} event - custom event.\n     * @group Emits\n     */\n    onSelectionChange = new EventEmitter();\n    templates;\n    toolbar;\n    value;\n    delayedCommand = null;\n    _readonly = false;\n    onModelChange = () => {};\n    onModelTouched = () => {};\n    quill;\n    dynamicQuill;\n    headerTemplate;\n    get isAttachedQuillEditorToDOM() {\n      return this.quillElements?.editorElement?.isConnected;\n    }\n    quillElements;\n    constructor(el, platformId) {\n      this.el = el;\n      this.platformId = platformId;\n      /**\n       * Read or write the DOM once, when initializing non-Angular (Quill) library.\n       */\n      afterNextRender(() => {\n        this.initQuillElements();\n        this.initQuillEditor();\n      });\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'header':\n            this.headerTemplate = item.template;\n            break;\n        }\n      });\n    }\n    writeValue(value) {\n      this.value = value;\n      if (this.quill) {\n        if (value) {\n          const command = () => {\n            this.quill.setContents(this.quill.clipboard.convert(this.dynamicQuill.version.startsWith('2') ? {\n              html: this.value\n            } : this.value));\n          };\n          if (this.isAttachedQuillEditorToDOM) {\n            command();\n          } else {\n            this.delayedCommand = command;\n          }\n        } else {\n          const command = () => {\n            this.quill.setText('');\n          };\n          if (this.isAttachedQuillEditorToDOM) {\n            command();\n          } else {\n            this.delayedCommand = command;\n          }\n        }\n      }\n    }\n    registerOnChange(fn) {\n      this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n      this.onModelTouched = fn;\n    }\n    getQuill() {\n      return this.quill;\n    }\n    initQuillEditor() {\n      if (isPlatformServer(this.platformId)) {\n        return;\n      }\n      /**\n       * Importing Quill at top level, throws `document is undefined` error during when\n       * building for SSR, so this dynamically loads quill when it's in browser module.\n       */\n      if (!this.dynamicQuill) {\n        import('quill').then(quillModule => {\n          this.dynamicQuill = quillModule.default;\n          this.createQuillEditor();\n        }).catch(e => console.error(e.message));\n      } else {\n        this.createQuillEditor();\n      }\n    }\n    createQuillEditor() {\n      this.initQuillElements();\n      const {\n        toolbarElement,\n        editorElement\n      } = this.quillElements;\n      let defaultModule = {\n        toolbar: toolbarElement\n      };\n      let modules = this.modules ? {\n        ...defaultModule,\n        ...this.modules\n      } : defaultModule;\n      this.quill = new this.dynamicQuill(editorElement, {\n        modules: modules,\n        placeholder: this.placeholder,\n        readOnly: this.readonly,\n        theme: 'snow',\n        formats: this.formats,\n        bounds: this.bounds,\n        debug: this.debug,\n        scrollingContainer: this.scrollingContainer\n      });\n      const isQuill2 = this.dynamicQuill.version.startsWith('2');\n      if (this.value) {\n        this.quill.setContents(this.quill.clipboard.convert(isQuill2 ? {\n          html: this.value\n        } : this.value));\n      }\n      this.quill.on('text-change', (delta, oldContents, source) => {\n        if (source === 'user') {\n          let html = isQuill2 ? this.quill.getSemanticHTML() : DomHandler.findSingle(editorElement, '.ql-editor').innerHTML;\n          let text = this.quill.getText().trim();\n          if (html === '<p><br></p>') {\n            html = null;\n          }\n          this.onTextChange.emit({\n            htmlValue: html,\n            textValue: text,\n            delta: delta,\n            source: source\n          });\n          this.onModelChange(html);\n          this.onModelTouched();\n        }\n      });\n      this.quill.on('selection-change', (range, oldRange, source) => {\n        this.onSelectionChange.emit({\n          range: range,\n          oldRange: oldRange,\n          source: source\n        });\n      });\n      this.onInit.emit({\n        editor: this.quill\n      });\n    }\n    initQuillElements() {\n      if (!this.quillElements) {\n        this.quillElements = {\n          editorElement: DomHandler.findSingle(this.el.nativeElement, 'div.p-editor-content'),\n          toolbarElement: DomHandler.findSingle(this.el.nativeElement, 'div.p-editor-toolbar')\n        };\n      }\n    }\n    static ɵfac = function Editor_Factory(t) {\n      return new (t || Editor)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(PLATFORM_ID));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Editor,\n      selectors: [[\"p-editor\"]],\n      contentQueries: function Editor_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, Header, 5);\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.toolbar = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        style: \"style\",\n        styleClass: \"styleClass\",\n        placeholder: \"placeholder\",\n        formats: \"formats\",\n        modules: \"modules\",\n        bounds: \"bounds\",\n        scrollingContainer: \"scrollingContainer\",\n        debug: \"debug\",\n        readonly: \"readonly\"\n      },\n      outputs: {\n        onInit: \"onInit\",\n        onTextChange: \"onTextChange\",\n        onSelectionChange: \"onSelectionChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([EDITOR_VALUE_ACCESSOR])],\n      ngContentSelectors: _c1,\n      decls: 4,\n      vars: 6,\n      consts: [[3, \"ngClass\"], [\"class\", \"p-editor-toolbar\", 4, \"ngIf\"], [1, \"p-editor-content\", 3, \"ngStyle\"], [1, \"p-editor-toolbar\"], [4, \"ngTemplateOutlet\"], [1, \"ql-formats\"], [1, \"ql-header\"], [\"value\", \"1\"], [\"value\", \"2\"], [\"selected\", \"\"], [1, \"ql-font\"], [\"value\", \"serif\"], [\"value\", \"monospace\"], [\"aria-label\", \"Bold\", \"type\", \"button\", 1, \"ql-bold\"], [\"aria-label\", \"Italic\", \"type\", \"button\", 1, \"ql-italic\"], [\"aria-label\", \"Underline\", \"type\", \"button\", 1, \"ql-underline\"], [1, \"ql-color\"], [1, \"ql-background\"], [\"value\", \"ordered\", \"aria-label\", \"Ordered List\", \"type\", \"button\", 1, \"ql-list\"], [\"value\", \"bullet\", \"aria-label\", \"Unordered List\", \"type\", \"button\", 1, \"ql-list\"], [1, \"ql-align\"], [\"value\", \"center\"], [\"value\", \"right\"], [\"value\", \"justify\"], [\"aria-label\", \"Insert Link\", \"type\", \"button\", 1, \"ql-link\"], [\"aria-label\", \"Insert Image\", \"type\", \"button\", 1, \"ql-image\"], [\"aria-label\", \"Insert Code Block\", \"type\", \"button\", 1, \"ql-code-block\"], [\"aria-label\", \"Remove Styles\", \"type\", \"button\", 1, \"ql-clean\"]],\n      template: function Editor_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c0);\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, Editor_div_1_Template, 3, 1, \"div\", 1)(2, Editor_div_2_Template, 40, 0, \"div\", 1);\n          i0.ɵɵelement(3, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngClass\", \"p-editor-container\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.toolbar || ctx.headerTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.toolbar && !ctx.headerTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngStyle\", ctx.style);\n        }\n      },\n      dependencies: [i1.NgClass, i1.NgIf, i1.NgTemplateOutlet, i1.NgStyle],\n      styles: [\".p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options .ql-picker-item{width:auto;height:auto}\\n\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return Editor;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet EditorModule = /*#__PURE__*/(() => {\n  class EditorModule {\n    static ɵfac = function EditorModule_Factory(t) {\n      return new (t || EditorModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: EditorModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedModule]\n    });\n  }\n  return EditorModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { EDITOR_VALUE_ACCESSOR, Editor, EditorModule };", "map": {"version": 3, "names": ["i1", "isPlatformServer", "CommonModule", "i0", "forwardRef", "EventEmitter", "afterNextRender", "PLATFORM_ID", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Input", "Output", "ContentChildren", "ContentChild", "NgModule", "NG_VALUE_ACCESSOR", "Header", "PrimeTemplate", "SharedModule", "<PERSON><PERSON><PERSON><PERSON>", "_c0", "_c1", "Editor_div_1_ng_container_2_Template", "rf", "ctx", "ɵɵelementContainer", "Editor_div_1_Template", "ɵɵelementStart", "ɵɵprojection", "ɵɵtemplate", "ɵɵelementEnd", "ctx_r0", "ɵɵnextContext", "ɵɵadvance", "ɵɵproperty", "headerTemplate", "Editor_div_2_Template", "ɵɵtext", "ɵɵelement", "EDITOR_VALUE_ACCESSOR", "provide", "useExisting", "Editor", "multi", "el", "platformId", "style", "styleClass", "placeholder", "formats", "modules", "bounds", "scrollingContainer", "debug", "readonly", "_readonly", "val", "quill", "disable", "enable", "onInit", "onTextChange", "onSelectionChange", "templates", "toolbar", "value", "delayedCommand", "onModelChange", "onModelTouched", "dynamicQuill", "isAttachedQuillEditorToDOM", "quillElements", "editor<PERSON><PERSON>", "isConnected", "constructor", "initQuillElements", "initQuillEditor", "ngAfterContentInit", "for<PERSON>ach", "item", "getType", "template", "writeValue", "command", "setContents", "clipboard", "convert", "version", "startsWith", "html", "setText", "registerOnChange", "fn", "registerOnTouched", "getQuill", "then", "quillModule", "default", "createQuillEditor", "catch", "e", "console", "error", "message", "toolbarElement", "defaultModule", "readOnly", "theme", "isQuill2", "on", "delta", "oldContents", "source", "getSemanticHTML", "findSingle", "innerHTML", "text", "getText", "trim", "emit", "htmlValue", "textValue", "range", "oldRange", "editor", "nativeElement", "ɵfac", "Editor_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "contentQueries", "Editor_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "inputs", "outputs", "features", "ɵɵProvidersFeature", "ngContentSelectors", "decls", "vars", "consts", "Editor_Template", "ɵɵprojectionDef", "ɵɵclassMap", "dependencies", "Ng<PERSON><PERSON>", "NgIf", "NgTemplateOutlet", "NgStyle", "styles", "encapsulation", "changeDetection", "ngDevMode", "EditorModule", "EditorModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/primeng/fesm2022/primeng-editor.mjs"], "sourcesContent": ["import * as i1 from '@angular/common';\nimport { isPlatformServer, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, afterNextRender, PLATFORM_ID, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, ContentChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Header, PrimeTemplate, SharedModule } from 'primeng/api';\nimport { DomHandler } from 'primeng/dom';\n\nconst EDITOR_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => Editor),\n    multi: true\n};\n/**\n * Editor groups a collection of contents in tabs.\n * @group Components\n */\nclass Editor {\n    el;\n    platformId;\n    /**\n     * Inline style of the container.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the container.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Placeholder text to show when editor is empty.\n     * @group Props\n     */\n    placeholder;\n    /**\n     * Whitelist of formats to display, see here for available options.\n     * @group Props\n     */\n    formats;\n    /**\n     * Modules configuration of Editor, see here for available options.\n     * @group Props\n     */\n    modules;\n    /**\n     * DOM Element or a CSS selector for a DOM Element, within which the editor’s p elements (i.e. tooltips, etc.) should be confined. Currently, it only considers left and right boundaries.\n     * @group Props\n     */\n    bounds;\n    /**\n     * DOM Element or a CSS selector for a DOM Element, specifying which container has the scrollbars (i.e. overflow-y: auto), if is has been changed from the default ql-editor with custom CSS. Necessary to fix scroll jumping bugs when Quill is set to auto grow its height, and another ancestor container is responsible from the scrolling..\n     * @group Props\n     */\n    scrollingContainer;\n    /**\n     * Shortcut for debug. Note debug is a static method and will affect other instances of Quill editors on the page. Only warning and error messages are enabled by default.\n     * @group Props\n     */\n    debug;\n    /**\n     * Whether to instantiate the editor to read-only mode.\n     * @group Props\n     */\n    get readonly() {\n        return this._readonly;\n    }\n    set readonly(val) {\n        this._readonly = val;\n        if (this.quill) {\n            if (this._readonly)\n                this.quill.disable();\n            else\n                this.quill.enable();\n        }\n    }\n    /**\n     * Callback to invoke when the quill modules are loaded.\n     * @param {EditorInitEvent} event - custom event.\n     * @group Emits\n     */\n    onInit = new EventEmitter();\n    /**\n     * Callback to invoke when text of editor changes.\n     * @param {EditorTextChangeEvent} event - custom event.\n     * @group Emits\n     */\n    onTextChange = new EventEmitter();\n    /**\n     * Callback to invoke when selection of the text changes.\n     * @param {EditorSelectionChangeEvent} event - custom event.\n     * @group Emits\n     */\n    onSelectionChange = new EventEmitter();\n    templates;\n    toolbar;\n    value;\n    delayedCommand = null;\n    _readonly = false;\n    onModelChange = () => { };\n    onModelTouched = () => { };\n    quill;\n    dynamicQuill;\n    headerTemplate;\n    get isAttachedQuillEditorToDOM() {\n        return this.quillElements?.editorElement?.isConnected;\n    }\n    quillElements;\n    constructor(el, platformId) {\n        this.el = el;\n        this.platformId = platformId;\n        /**\n         * Read or write the DOM once, when initializing non-Angular (Quill) library.\n         */\n        afterNextRender(() => {\n            this.initQuillElements();\n            this.initQuillEditor();\n        });\n    }\n    ngAfterContentInit() {\n        this.templates.forEach((item) => {\n            switch (item.getType()) {\n                case 'header':\n                    this.headerTemplate = item.template;\n                    break;\n            }\n        });\n    }\n    writeValue(value) {\n        this.value = value;\n        if (this.quill) {\n            if (value) {\n                const command = () => {\n                    this.quill.setContents(this.quill.clipboard.convert(this.dynamicQuill.version.startsWith('2') ? { html: this.value } : this.value));\n                };\n                if (this.isAttachedQuillEditorToDOM) {\n                    command();\n                }\n                else {\n                    this.delayedCommand = command;\n                }\n            }\n            else {\n                const command = () => {\n                    this.quill.setText('');\n                };\n                if (this.isAttachedQuillEditorToDOM) {\n                    command();\n                }\n                else {\n                    this.delayedCommand = command;\n                }\n            }\n        }\n    }\n    registerOnChange(fn) {\n        this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onModelTouched = fn;\n    }\n    getQuill() {\n        return this.quill;\n    }\n    initQuillEditor() {\n        if (isPlatformServer(this.platformId)) {\n            return;\n        }\n        /**\n         * Importing Quill at top level, throws `document is undefined` error during when\n         * building for SSR, so this dynamically loads quill when it's in browser module.\n         */\n        if (!this.dynamicQuill) {\n            import('quill')\n                .then((quillModule) => {\n                this.dynamicQuill = quillModule.default;\n                this.createQuillEditor();\n            })\n                .catch((e) => console.error(e.message));\n        }\n        else {\n            this.createQuillEditor();\n        }\n    }\n    createQuillEditor() {\n        this.initQuillElements();\n        const { toolbarElement, editorElement } = this.quillElements;\n        let defaultModule = { toolbar: toolbarElement };\n        let modules = this.modules ? { ...defaultModule, ...this.modules } : defaultModule;\n        this.quill = new this.dynamicQuill(editorElement, {\n            modules: modules,\n            placeholder: this.placeholder,\n            readOnly: this.readonly,\n            theme: 'snow',\n            formats: this.formats,\n            bounds: this.bounds,\n            debug: this.debug,\n            scrollingContainer: this.scrollingContainer\n        });\n        const isQuill2 = this.dynamicQuill.version.startsWith('2');\n        if (this.value) {\n            this.quill.setContents(this.quill.clipboard.convert(isQuill2 ? { html: this.value } : this.value));\n        }\n        this.quill.on('text-change', (delta, oldContents, source) => {\n            if (source === 'user') {\n                let html = isQuill2 ? this.quill.getSemanticHTML() : DomHandler.findSingle(editorElement, '.ql-editor').innerHTML;\n                let text = this.quill.getText().trim();\n                if (html === '<p><br></p>') {\n                    html = null;\n                }\n                this.onTextChange.emit({\n                    htmlValue: html,\n                    textValue: text,\n                    delta: delta,\n                    source: source\n                });\n                this.onModelChange(html);\n                this.onModelTouched();\n            }\n        });\n        this.quill.on('selection-change', (range, oldRange, source) => {\n            this.onSelectionChange.emit({\n                range: range,\n                oldRange: oldRange,\n                source: source\n            });\n        });\n        this.onInit.emit({\n            editor: this.quill\n        });\n    }\n    initQuillElements() {\n        if (!this.quillElements) {\n            this.quillElements = {\n                editorElement: DomHandler.findSingle(this.el.nativeElement, 'div.p-editor-content'),\n                toolbarElement: DomHandler.findSingle(this.el.nativeElement, 'div.p-editor-toolbar')\n            };\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: Editor, deps: [{ token: i0.ElementRef }, { token: PLATFORM_ID }], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.7\", type: Editor, selector: \"p-editor\", inputs: { style: \"style\", styleClass: \"styleClass\", placeholder: \"placeholder\", formats: \"formats\", modules: \"modules\", bounds: \"bounds\", scrollingContainer: \"scrollingContainer\", debug: \"debug\", readonly: \"readonly\" }, outputs: { onInit: \"onInit\", onTextChange: \"onTextChange\", onSelectionChange: \"onSelectionChange\" }, host: { classAttribute: \"p-element\" }, providers: [EDITOR_VALUE_ACCESSOR], queries: [{ propertyName: \"toolbar\", first: true, predicate: Header, descendants: true }, { propertyName: \"templates\", predicate: PrimeTemplate }], ngImport: i0, template: `\n        <div [ngClass]=\"'p-editor-container'\" [class]=\"styleClass\">\n            <div class=\"p-editor-toolbar\" *ngIf=\"toolbar || headerTemplate\">\n                <ng-content select=\"p-header\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n            </div>\n            <div class=\"p-editor-toolbar\" *ngIf=\"!toolbar && !headerTemplate\">\n                <span class=\"ql-formats\">\n                    <select class=\"ql-header\">\n                        <option value=\"1\">Heading</option>\n                        <option value=\"2\">Subheading</option>\n                        <option selected>Normal</option>\n                    </select>\n                    <select class=\"ql-font\">\n                        <option selected>Sans Serif</option>\n                        <option value=\"serif\">Serif</option>\n                        <option value=\"monospace\">Monospace</option>\n                    </select>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-bold\" aria-label=\"Bold\" type=\"button\"></button>\n                    <button class=\"ql-italic\" aria-label=\"Italic\" type=\"button\"></button>\n                    <button class=\"ql-underline\" aria-label=\"Underline\" type=\"button\"></button>\n                </span>\n                <span class=\"ql-formats\">\n                    <select class=\"ql-color\"></select>\n                    <select class=\"ql-background\"></select>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-list\" value=\"ordered\" aria-label=\"Ordered List\" type=\"button\"></button>\n                    <button class=\"ql-list\" value=\"bullet\" aria-label=\"Unordered List\" type=\"button\"></button>\n                    <select class=\"ql-align\">\n                        <option selected></option>\n                        <option value=\"center\">center</option>\n                        <option value=\"right\">right</option>\n                        <option value=\"justify\">justify</option>\n                    </select>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-link\" aria-label=\"Insert Link\" type=\"button\"></button>\n                    <button class=\"ql-image\" aria-label=\"Insert Image\" type=\"button\"></button>\n                    <button class=\"ql-code-block\" aria-label=\"Insert Code Block\" type=\"button\"></button>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-clean\" aria-label=\"Remove Styles\" type=\"button\"></button>\n                </span>\n            </div>\n            <div class=\"p-editor-content\" [ngStyle]=\"style\"></div>\n        </div>\n    `, isInline: true, styles: [\".p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options .ql-picker-item{width:auto;height:auto}\\n\"], dependencies: [{ kind: \"directive\", type: i1.NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: i1.NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: i1.NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: i1.NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: Editor, decorators: [{\n            type: Component,\n            args: [{ selector: 'p-editor', template: `\n        <div [ngClass]=\"'p-editor-container'\" [class]=\"styleClass\">\n            <div class=\"p-editor-toolbar\" *ngIf=\"toolbar || headerTemplate\">\n                <ng-content select=\"p-header\"></ng-content>\n                <ng-container *ngTemplateOutlet=\"headerTemplate\"></ng-container>\n            </div>\n            <div class=\"p-editor-toolbar\" *ngIf=\"!toolbar && !headerTemplate\">\n                <span class=\"ql-formats\">\n                    <select class=\"ql-header\">\n                        <option value=\"1\">Heading</option>\n                        <option value=\"2\">Subheading</option>\n                        <option selected>Normal</option>\n                    </select>\n                    <select class=\"ql-font\">\n                        <option selected>Sans Serif</option>\n                        <option value=\"serif\">Serif</option>\n                        <option value=\"monospace\">Monospace</option>\n                    </select>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-bold\" aria-label=\"Bold\" type=\"button\"></button>\n                    <button class=\"ql-italic\" aria-label=\"Italic\" type=\"button\"></button>\n                    <button class=\"ql-underline\" aria-label=\"Underline\" type=\"button\"></button>\n                </span>\n                <span class=\"ql-formats\">\n                    <select class=\"ql-color\"></select>\n                    <select class=\"ql-background\"></select>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-list\" value=\"ordered\" aria-label=\"Ordered List\" type=\"button\"></button>\n                    <button class=\"ql-list\" value=\"bullet\" aria-label=\"Unordered List\" type=\"button\"></button>\n                    <select class=\"ql-align\">\n                        <option selected></option>\n                        <option value=\"center\">center</option>\n                        <option value=\"right\">right</option>\n                        <option value=\"justify\">justify</option>\n                    </select>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-link\" aria-label=\"Insert Link\" type=\"button\"></button>\n                    <button class=\"ql-image\" aria-label=\"Insert Image\" type=\"button\"></button>\n                    <button class=\"ql-code-block\" aria-label=\"Insert Code Block\" type=\"button\"></button>\n                </span>\n                <span class=\"ql-formats\">\n                    <button class=\"ql-clean\" aria-label=\"Remove Styles\" type=\"button\"></button>\n                </span>\n            </div>\n            <div class=\"p-editor-content\" [ngStyle]=\"style\"></div>\n        </div>\n    `, providers: [EDITOR_VALUE_ACCESSOR], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, host: {\n                        class: 'p-element'\n                    }, styles: [\".p-editor-container .p-editor-toolbar.ql-snow .ql-picker.ql-expanded .ql-picker-options .ql-picker-item{width:auto;height:auto}\\n\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }], propDecorators: { style: [{\n                type: Input\n            }], styleClass: [{\n                type: Input\n            }], placeholder: [{\n                type: Input\n            }], formats: [{\n                type: Input\n            }], modules: [{\n                type: Input\n            }], bounds: [{\n                type: Input\n            }], scrollingContainer: [{\n                type: Input\n            }], debug: [{\n                type: Input\n            }], readonly: [{\n                type: Input\n            }], onInit: [{\n                type: Output\n            }], onTextChange: [{\n                type: Output\n            }], onSelectionChange: [{\n                type: Output\n            }], templates: [{\n                type: ContentChildren,\n                args: [PrimeTemplate]\n            }], toolbar: [{\n                type: ContentChild,\n                args: [Header]\n            }] } });\nclass EditorModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: EditorModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.7\", ngImport: i0, type: EditorModule, declarations: [Editor], imports: [CommonModule], exports: [Editor, SharedModule] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: EditorModule, imports: [CommonModule, SharedModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.7\", ngImport: i0, type: EditorModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CommonModule],\n                    exports: [Editor, SharedModule],\n                    declarations: [Editor]\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { EDITOR_VALUE_ACCESSOR, Editor, EditorModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,iBAAiB;AACrC,SAASC,gBAAgB,EAAEC,YAAY,QAAQ,iBAAiB;AAChE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,YAAY,EAAEC,eAAe,EAAEC,WAAW,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,eAAe,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,eAAe;AAC7M,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,MAAM,EAAEC,aAAa,EAAEC,YAAY,QAAQ,aAAa;AACjE,SAASC,UAAU,QAAQ,aAAa;AAAC,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,qCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAyOoDtB,EAAE,CAAAwB,kBAAA,EAKhB,CAAC;EAAA;AAAA;AAAA,SAAAC,sBAAAH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IALatB,EAAE,CAAA0B,cAAA,YAGpB,CAAC;IAHiB1B,EAAE,CAAA2B,YAAA,EAIrC,CAAC;IAJkC3B,EAAE,CAAA4B,UAAA,IAAAP,oCAAA,yBAK/B,CAAC;IAL4BrB,EAAE,CAAA6B,YAAA,CAM9E,CAAC;EAAA;EAAA,IAAAP,EAAA;IAAA,MAAAQ,MAAA,GAN2E9B,EAAE,CAAA+B,aAAA;IAAF/B,EAAE,CAAAgC,SAAA,EAKjC,CAAC;IAL8BhC,EAAE,CAAAiC,UAAA,qBAAAH,MAAA,CAAAI,cAKjC,CAAC;EAAA;AAAA;AAAA,SAAAC,sBAAAb,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAL8BtB,EAAE,CAAA0B,cAAA,YAOlB,CAAC,aACtC,CAAC,eACI,CAAC,eACL,CAAC;IAVmD1B,EAAE,CAAAoC,MAAA,aAU/C,CAAC;IAV4CpC,EAAE,CAAA6B,YAAA,CAUtC,CAAC;IAVmC7B,EAAE,CAAA0B,cAAA,eAWtD,CAAC;IAXmD1B,EAAE,CAAAoC,MAAA,gBAW5C,CAAC;IAXyCpC,EAAE,CAAA6B,YAAA,CAWnC,CAAC;IAXgC7B,EAAE,CAAA0B,cAAA,eAYvD,CAAC;IAZoD1B,EAAE,CAAAoC,MAAA,YAYjD,CAAC;IAZ8CpC,EAAE,CAAA6B,YAAA,CAYxC,CAAC,CAC5B,CAAC;IAbgE7B,EAAE,CAAA0B,cAAA,gBAcpD,CAAC,gBACJ,CAAC;IAfoD1B,EAAE,CAAAoC,MAAA,iBAe7C,CAAC;IAf0CpC,EAAE,CAAA6B,YAAA,CAepC,CAAC;IAfiC7B,EAAE,CAAA0B,cAAA,iBAgBlD,CAAC;IAhB+C1B,EAAE,CAAAoC,MAAA,YAgB7C,CAAC;IAhB0CpC,EAAE,CAAA6B,YAAA,CAgBpC,CAAC;IAhBiC7B,EAAE,CAAA0B,cAAA,iBAiB9C,CAAC;IAjB2C1B,EAAE,CAAAoC,MAAA,gBAiBrC,CAAC;IAjBkCpC,EAAE,CAAA6B,YAAA,CAiB5B,CAAC,CACxC,CAAC,CACP,CAAC;IAnBsE7B,EAAE,CAAA0B,cAAA,cAoBvD,CAAC;IApBoD1B,EAAE,CAAAqC,SAAA,iBAqBX,CAAC,iBACG,CAAC,iBACK,CAAC;IAvBFrC,EAAE,CAAA6B,YAAA,CAwBzE,CAAC;IAxBsE7B,EAAE,CAAA0B,cAAA,cAyBvD,CAAC;IAzBoD1B,EAAE,CAAAqC,SAAA,iBA0B1C,CAAC,iBACI,CAAC;IA3BkCrC,EAAE,CAAA6B,YAAA,CA4BzE,CAAC;IA5BsE7B,EAAE,CAAA0B,cAAA,cA6BvD,CAAC;IA7BoD1B,EAAE,CAAAqC,SAAA,iBA8Ba,CAAC,iBACA,CAAC;IA/BjBrC,EAAE,CAAA0B,cAAA,iBAgCnD,CAAC;IAhCgD1B,EAAE,CAAAqC,SAAA,gBAiC9C,CAAC;IAjC2CrC,EAAE,CAAA0B,cAAA,iBAkCjD,CAAC;IAlC8C1B,EAAE,CAAAoC,MAAA,aAkC3C,CAAC;IAlCwCpC,EAAE,CAAA6B,YAAA,CAkClC,CAAC;IAlC+B7B,EAAE,CAAA0B,cAAA,iBAmClD,CAAC;IAnC+C1B,EAAE,CAAAoC,MAAA,YAmC7C,CAAC;IAnC0CpC,EAAE,CAAA6B,YAAA,CAmCpC,CAAC;IAnCiC7B,EAAE,CAAA0B,cAAA,iBAoChD,CAAC;IApC6C1B,EAAE,CAAAoC,MAAA,cAoCzC,CAAC;IApCsCpC,EAAE,CAAA6B,YAAA,CAoChC,CAAC,CACpC,CAAC,CACP,CAAC;IAtCsE7B,EAAE,CAAA0B,cAAA,cAuCvD,CAAC;IAvCoD1B,EAAE,CAAAqC,SAAA,iBAwCJ,CAAC,iBACC,CAAC,iBACS,CAAC;IA1CXrC,EAAE,CAAA6B,YAAA,CA2CzE,CAAC;IA3CsE7B,EAAE,CAAA0B,cAAA,cA4CvD,CAAC;IA5CoD1B,EAAE,CAAAqC,SAAA,iBA6CD,CAAC;IA7CFrC,EAAE,CAAA6B,YAAA,CA8CzE,CAAC,CACN,CAAC;EAAA;AAAA;AAtRlB,MAAMS,qBAAqB,GAAG;EAC1BC,OAAO,EAAEzB,iBAAiB;EAC1B0B,WAAW,EAAEvC,UAAU,CAAC,MAAMwC,MAAM,CAAC;EACrCC,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AAHA,IAIMD,MAAM;EAAZ,MAAMA,MAAM,CAAC;IACTE,EAAE;IACFC,UAAU;IACV;AACJ;AACA;AACA;IACIC,KAAK;IACL;AACJ;AACA;AACA;IACIC,UAAU;IACV;AACJ;AACA;AACA;IACIC,WAAW;IACX;AACJ;AACA;AACA;IACIC,OAAO;IACP;AACJ;AACA;AACA;IACIC,OAAO;IACP;AACJ;AACA;AACA;IACIC,MAAM;IACN;AACJ;AACA;AACA;IACIC,kBAAkB;IAClB;AACJ;AACA;AACA;IACIC,KAAK;IACL;AACJ;AACA;AACA;IACI,IAAIC,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACC,SAAS;IACzB;IACA,IAAID,QAAQA,CAACE,GAAG,EAAE;MACd,IAAI,CAACD,SAAS,GAAGC,GAAG;MACpB,IAAI,IAAI,CAACC,KAAK,EAAE;QACZ,IAAI,IAAI,CAACF,SAAS,EACd,IAAI,CAACE,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,KAErB,IAAI,CAACD,KAAK,CAACE,MAAM,CAAC,CAAC;MAC3B;IACJ;IACA;AACJ;AACA;AACA;AACA;IACIC,MAAM,GAAG,IAAIzD,YAAY,CAAC,CAAC;IAC3B;AACJ;AACA;AACA;AACA;IACI0D,YAAY,GAAG,IAAI1D,YAAY,CAAC,CAAC;IACjC;AACJ;AACA;AACA;AACA;IACI2D,iBAAiB,GAAG,IAAI3D,YAAY,CAAC,CAAC;IACtC4D,SAAS;IACTC,OAAO;IACPC,KAAK;IACLC,cAAc,GAAG,IAAI;IACrBX,SAAS,GAAG,KAAK;IACjBY,aAAa,GAAGA,CAAA,KAAM,CAAE,CAAC;IACzBC,cAAc,GAAGA,CAAA,KAAM,CAAE,CAAC;IAC1BX,KAAK;IACLY,YAAY;IACZlC,cAAc;IACd,IAAImC,0BAA0BA,CAAA,EAAG;MAC7B,OAAO,IAAI,CAACC,aAAa,EAAEC,aAAa,EAAEC,WAAW;IACzD;IACAF,aAAa;IACbG,WAAWA,CAAC9B,EAAE,EAAEC,UAAU,EAAE;MACxB,IAAI,CAACD,EAAE,GAAGA,EAAE;MACZ,IAAI,CAACC,UAAU,GAAGA,UAAU;MAC5B;AACR;AACA;MACQzC,eAAe,CAAC,MAAM;QAClB,IAAI,CAACuE,iBAAiB,CAAC,CAAC;QACxB,IAAI,CAACC,eAAe,CAAC,CAAC;MAC1B,CAAC,CAAC;IACN;IACAC,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAACd,SAAS,CAACe,OAAO,CAAEC,IAAI,IAAK;QAC7B,QAAQA,IAAI,CAACC,OAAO,CAAC,CAAC;UAClB,KAAK,QAAQ;YACT,IAAI,CAAC7C,cAAc,GAAG4C,IAAI,CAACE,QAAQ;YACnC;QACR;MACJ,CAAC,CAAC;IACN;IACAC,UAAUA,CAACjB,KAAK,EAAE;MACd,IAAI,CAACA,KAAK,GAAGA,KAAK;MAClB,IAAI,IAAI,CAACR,KAAK,EAAE;QACZ,IAAIQ,KAAK,EAAE;UACP,MAAMkB,OAAO,GAAGA,CAAA,KAAM;YAClB,IAAI,CAAC1B,KAAK,CAAC2B,WAAW,CAAC,IAAI,CAAC3B,KAAK,CAAC4B,SAAS,CAACC,OAAO,CAAC,IAAI,CAACjB,YAAY,CAACkB,OAAO,CAACC,UAAU,CAAC,GAAG,CAAC,GAAG;cAAEC,IAAI,EAAE,IAAI,CAACxB;YAAM,CAAC,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC;UACvI,CAAC;UACD,IAAI,IAAI,CAACK,0BAA0B,EAAE;YACjCa,OAAO,CAAC,CAAC;UACb,CAAC,MACI;YACD,IAAI,CAACjB,cAAc,GAAGiB,OAAO;UACjC;QACJ,CAAC,MACI;UACD,MAAMA,OAAO,GAAGA,CAAA,KAAM;YAClB,IAAI,CAAC1B,KAAK,CAACiC,OAAO,CAAC,EAAE,CAAC;UAC1B,CAAC;UACD,IAAI,IAAI,CAACpB,0BAA0B,EAAE;YACjCa,OAAO,CAAC,CAAC;UACb,CAAC,MACI;YACD,IAAI,CAACjB,cAAc,GAAGiB,OAAO;UACjC;QACJ;MACJ;IACJ;IACAQ,gBAAgBA,CAACC,EAAE,EAAE;MACjB,IAAI,CAACzB,aAAa,GAAGyB,EAAE;IAC3B;IACAC,iBAAiBA,CAACD,EAAE,EAAE;MAClB,IAAI,CAACxB,cAAc,GAAGwB,EAAE;IAC5B;IACAE,QAAQA,CAAA,EAAG;MACP,OAAO,IAAI,CAACrC,KAAK;IACrB;IACAmB,eAAeA,CAAA,EAAG;MACd,IAAI7E,gBAAgB,CAAC,IAAI,CAAC8C,UAAU,CAAC,EAAE;QACnC;MACJ;MACA;AACR;AACA;AACA;MACQ,IAAI,CAAC,IAAI,CAACwB,YAAY,EAAE;QACpB,MAAM,CAAC,OAAO,CAAC,CACV0B,IAAI,CAAEC,WAAW,IAAK;UACvB,IAAI,CAAC3B,YAAY,GAAG2B,WAAW,CAACC,OAAO;UACvC,IAAI,CAACC,iBAAiB,CAAC,CAAC;QAC5B,CAAC,CAAC,CACGC,KAAK,CAAEC,CAAC,IAAKC,OAAO,CAACC,KAAK,CAACF,CAAC,CAACG,OAAO,CAAC,CAAC;MAC/C,CAAC,MACI;QACD,IAAI,CAACL,iBAAiB,CAAC,CAAC;MAC5B;IACJ;IACAA,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAACvB,iBAAiB,CAAC,CAAC;MACxB,MAAM;QAAE6B,cAAc;QAAEhC;MAAc,CAAC,GAAG,IAAI,CAACD,aAAa;MAC5D,IAAIkC,aAAa,GAAG;QAAEzC,OAAO,EAAEwC;MAAe,CAAC;MAC/C,IAAItD,OAAO,GAAG,IAAI,CAACA,OAAO,GAAG;QAAE,GAAGuD,aAAa;QAAE,GAAG,IAAI,CAACvD;MAAQ,CAAC,GAAGuD,aAAa;MAClF,IAAI,CAAChD,KAAK,GAAG,IAAI,IAAI,CAACY,YAAY,CAACG,aAAa,EAAE;QAC9CtB,OAAO,EAAEA,OAAO;QAChBF,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7B0D,QAAQ,EAAE,IAAI,CAACpD,QAAQ;QACvBqD,KAAK,EAAE,MAAM;QACb1D,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBE,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBE,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBD,kBAAkB,EAAE,IAAI,CAACA;MAC7B,CAAC,CAAC;MACF,MAAMwD,QAAQ,GAAG,IAAI,CAACvC,YAAY,CAACkB,OAAO,CAACC,UAAU,CAAC,GAAG,CAAC;MAC1D,IAAI,IAAI,CAACvB,KAAK,EAAE;QACZ,IAAI,CAACR,KAAK,CAAC2B,WAAW,CAAC,IAAI,CAAC3B,KAAK,CAAC4B,SAAS,CAACC,OAAO,CAACsB,QAAQ,GAAG;UAAEnB,IAAI,EAAE,IAAI,CAACxB;QAAM,CAAC,GAAG,IAAI,CAACA,KAAK,CAAC,CAAC;MACtG;MACA,IAAI,CAACR,KAAK,CAACoD,EAAE,CAAC,aAAa,EAAE,CAACC,KAAK,EAAEC,WAAW,EAAEC,MAAM,KAAK;QACzD,IAAIA,MAAM,KAAK,MAAM,EAAE;UACnB,IAAIvB,IAAI,GAAGmB,QAAQ,GAAG,IAAI,CAACnD,KAAK,CAACwD,eAAe,CAAC,CAAC,GAAG9F,UAAU,CAAC+F,UAAU,CAAC1C,aAAa,EAAE,YAAY,CAAC,CAAC2C,SAAS;UACjH,IAAIC,IAAI,GAAG,IAAI,CAAC3D,KAAK,CAAC4D,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;UACtC,IAAI7B,IAAI,KAAK,aAAa,EAAE;YACxBA,IAAI,GAAG,IAAI;UACf;UACA,IAAI,CAAC5B,YAAY,CAAC0D,IAAI,CAAC;YACnBC,SAAS,EAAE/B,IAAI;YACfgC,SAAS,EAAEL,IAAI;YACfN,KAAK,EAAEA,KAAK;YACZE,MAAM,EAAEA;UACZ,CAAC,CAAC;UACF,IAAI,CAAC7C,aAAa,CAACsB,IAAI,CAAC;UACxB,IAAI,CAACrB,cAAc,CAAC,CAAC;QACzB;MACJ,CAAC,CAAC;MACF,IAAI,CAACX,KAAK,CAACoD,EAAE,CAAC,kBAAkB,EAAE,CAACa,KAAK,EAAEC,QAAQ,EAAEX,MAAM,KAAK;QAC3D,IAAI,CAAClD,iBAAiB,CAACyD,IAAI,CAAC;UACxBG,KAAK,EAAEA,KAAK;UACZC,QAAQ,EAAEA,QAAQ;UAClBX,MAAM,EAAEA;QACZ,CAAC,CAAC;MACN,CAAC,CAAC;MACF,IAAI,CAACpD,MAAM,CAAC2D,IAAI,CAAC;QACbK,MAAM,EAAE,IAAI,CAACnE;MACjB,CAAC,CAAC;IACN;IACAkB,iBAAiBA,CAAA,EAAG;MAChB,IAAI,CAAC,IAAI,CAACJ,aAAa,EAAE;QACrB,IAAI,CAACA,aAAa,GAAG;UACjBC,aAAa,EAAErD,UAAU,CAAC+F,UAAU,CAAC,IAAI,CAACtE,EAAE,CAACiF,aAAa,EAAE,sBAAsB,CAAC;UACnFrB,cAAc,EAAErF,UAAU,CAAC+F,UAAU,CAAC,IAAI,CAACtE,EAAE,CAACiF,aAAa,EAAE,sBAAsB;QACvF,CAAC;MACL;IACJ;IACA,OAAOC,IAAI,YAAAC,eAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFtF,MAAM,EAAhBzC,EAAE,CAAAgI,iBAAA,CAAgChI,EAAE,CAACiI,UAAU,GAA/CjI,EAAE,CAAAgI,iBAAA,CAA0D5H,WAAW;IAAA;IAChK,OAAO8H,IAAI,kBAD8ElI,EAAE,CAAAmI,iBAAA;MAAAC,IAAA,EACJ3F,MAAM;MAAA4F,SAAA;MAAAC,cAAA,WAAAC,sBAAAjH,EAAA,EAAAC,GAAA,EAAAiH,QAAA;QAAA,IAAAlH,EAAA;UADJtB,EAAE,CAAAyI,cAAA,CAAAD,QAAA,EACmezH,MAAM;UAD3ef,EAAE,CAAAyI,cAAA,CAAAD,QAAA,EACwiBxH,aAAa;QAAA;QAAA,IAAAM,EAAA;UAAA,IAAAoH,EAAA;UADvjB1I,EAAE,CAAA2I,cAAA,CAAAD,EAAA,GAAF1I,EAAE,CAAA4I,WAAA,QAAArH,GAAA,CAAAwC,OAAA,GAAA2E,EAAA,CAAAG,KAAA;UAAF7I,EAAE,CAAA2I,cAAA,CAAAD,EAAA,GAAF1I,EAAE,CAAA4I,WAAA,QAAArH,GAAA,CAAAuC,SAAA,GAAA4E,EAAA;QAAA;MAAA;MAAAI,SAAA;MAAAC,MAAA;QAAAlG,KAAA;QAAAC,UAAA;QAAAC,WAAA;QAAAC,OAAA;QAAAC,OAAA;QAAAC,MAAA;QAAAC,kBAAA;QAAAC,KAAA;QAAAC,QAAA;MAAA;MAAA2F,OAAA;QAAArF,MAAA;QAAAC,YAAA;QAAAC,iBAAA;MAAA;MAAAoF,QAAA,GAAFjJ,EAAE,CAAAkJ,kBAAA,CAC6Y,CAAC5G,qBAAqB,CAAC;MAAA6G,kBAAA,EAAA/H,GAAA;MAAAgI,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAtE,QAAA,WAAAuE,gBAAAjI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADtatB,EAAE,CAAAwJ,eAAA,CAAArI,GAAA;UAAFnB,EAAE,CAAA0B,cAAA,YAE7B,CAAC;UAF0B1B,EAAE,CAAA4B,UAAA,IAAAH,qBAAA,gBAGpB,CAAC,IAAAU,qBAAA,iBAIC,CAAC;UAPenC,EAAE,CAAAqC,SAAA,YAgD9B,CAAC;UAhD2BrC,EAAE,CAAA6B,YAAA,CAiDlF,CAAC;QAAA;QAAA,IAAAP,EAAA;UAjD+EtB,EAAE,CAAAyJ,UAAA,CAAAlI,GAAA,CAAAuB,UAE9B,CAAC;UAF2B9C,EAAE,CAAAiC,UAAA,gCAEnD,CAAC;UAFgDjC,EAAE,CAAAgC,SAAA,CAGtB,CAAC;UAHmBhC,EAAE,CAAAiC,UAAA,SAAAV,GAAA,CAAAwC,OAAA,IAAAxC,GAAA,CAAAW,cAGtB,CAAC;UAHmBlC,EAAE,CAAAgC,SAAA,CAOpB,CAAC;UAPiBhC,EAAE,CAAAiC,UAAA,UAAAV,GAAA,CAAAwC,OAAA,KAAAxC,GAAA,CAAAW,cAOpB,CAAC;UAPiBlC,EAAE,CAAAgC,SAAA,CAgDrC,CAAC;UAhDkChC,EAAE,CAAAiC,UAAA,YAAAV,GAAA,CAAAsB,KAgDrC,CAAC;QAAA;MAAA;MAAA6G,YAAA,GAEqJ7J,EAAE,CAAC8J,OAAO,EAAoF9J,EAAE,CAAC+J,IAAI,EAA6F/J,EAAE,CAACgK,gBAAgB,EAAoJhK,EAAE,CAACiK,OAAO;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA;EACnkB;EAAC,OAjRKxH,MAAM;AAAA;AAkRZ;EAAA,QAAAyH,SAAA,oBAAAA,SAAA;AAAA;AAuFoB,IACdC,YAAY;EAAlB,MAAMA,YAAY,CAAC;IACf,OAAOtC,IAAI,YAAAuC,qBAAArC,CAAA;MAAA,YAAAA,CAAA,IAAwFoC,YAAY;IAAA;IAC/G,OAAOE,IAAI,kBA9I8ErK,EAAE,CAAAsK,gBAAA;MAAAlC,IAAA,EA8IS+B;IAAY;IAChH,OAAOI,IAAI,kBA/I8EvK,EAAE,CAAAwK,gBAAA;MAAAC,OAAA,GA+IiC1K,YAAY,EAAEkB,YAAY;IAAA;EAC1J;EAAC,OAJKkJ,YAAY;AAAA;AAKlB;EAAA,QAAAD,SAAA,oBAAAA,SAAA;AAAA;;AASA;AACA;AACA;;AAEA,SAAS5H,qBAAqB,EAAEG,MAAM,EAAE0H,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}