{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nimport * as i2 from \"src/app/core/services/library.service\";\nimport * as i3 from \"src/app/core/services/homework.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/forms\";\nconst _c0 = [\"taskFiles\"];\nfunction HomeworkTaskComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14);\n    i0.ɵɵtext(1, \" Title is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeworkTaskComponent_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3);\n    i0.ɵɵelement(2, \"img\", 15);\n    i0.ɵɵelementStart(3, \"div\", 16);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const teacherFile_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getTeacherFileName(teacherFile_r1.filePath), \" \");\n  }\n}\nfunction HomeworkTaskComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3);\n    i0.ɵɵelement(2, \"img\", 15);\n    i0.ɵɵelementStart(3, \"div\", 16);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const libFile_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getTeacherFileName(libFile_r3.path), \" \");\n  }\n}\nfunction HomeworkTaskComponent_div_14_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26);\n    i0.ɵɵlistener(\"click\", function HomeworkTaskComponent_div_14_div_13_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.closeAddFiles());\n    });\n    i0.ɵɵtext(2, \" SUBMIT \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HomeworkTaskComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17, 0)(2, \"div\", 18)(3, \"div\");\n    i0.ɵɵtext(4, \"Add files\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"img\", 19);\n    i0.ɵɵlistener(\"click\", function HomeworkTaskComponent_div_14_Template_img_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closeAddFiles());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 20)(7, \"div\", 21);\n    i0.ɵɵtext(8, \" Add from your local files \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"app-upload-files\", 22);\n    i0.ɵɵlistener(\"onUploadFinished\", function HomeworkTaskComponent_div_14_Template_app_upload_files_onUploadFinished_9_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.uploadFinished($event));\n    })(\"tagChoosen\", function HomeworkTaskComponent_div_14_Template_app_upload_files_tagChoosen_9_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTagChoosen($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 21);\n    i0.ɵɵtext(11, \" Add from your libraries \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"app-library\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, HomeworkTaskComponent_div_14_div_13_Template, 3, 0, \"div\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"assignToClassroom\", ctx_r1.assignToClassroom)(\"inHomeworkTask\", true)(\"homeworkTaskStudentId\", ctx_r1.assignToUser == null ? null : ctx_r1.assignToUser.id)(\"dndUI\", true)(\"checkboxUI\", true)(\"teacherFilesLength\", ctx_r1.task.teacherFiles.length);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"task\", ctx_r1.task)(\"inHomework\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showSubmitButton);\n  }\n}\nexport class HomeworkTaskComponent {\n  constructor(generalService, renderer, libraryService, homeworkService) {\n    this.generalService = generalService;\n    this.renderer = renderer;\n    this.libraryService = libraryService;\n    this.homeworkService = homeworkService;\n    this.task = {};\n    this.showSubmitButton = false;\n    this.homework = {};\n    this.edit = false;\n    this.deleteTask = new EventEmitter();\n    this.onOpenAddFiles = new EventEmitter();\n    this.onCloseAddFiles = new EventEmitter();\n    this.subs = new SubSink();\n    this.taskForm = new UntypedFormGroup({});\n    this.s = false;\n    this.isSubmitted = false;\n    this.uniqueTeacherFiles = [];\n  }\n  ngOnInit() {\n    this.homeworkService.submitListener.subscribe(res => {\n      this.homeworkService.setIsTaskFormValid(this.taskForm.valid);\n      this.isSubmitted = res;\n    });\n    this.taskForm = new UntypedFormGroup({\n      title: new UntypedFormControl(this.task.title, {\n        validators: [Validators.required]\n      }),\n      link: new UntypedFormControl(this.task.link, {\n        // validators: [Validators.required]\n      })\n    });\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  get errorControl() {\n    return this.taskForm.controls;\n  }\n  onDeleteTask() {\n    this.deleteTask.emit({\n      task: this.task\n    });\n  }\n  showAddFiles() {\n    this.generalService.slideInElement('blur_bg2');\n    this.s = true;\n    if (this.edit) {\n      this.onOpenAddFiles.emit();\n    }\n  }\n  closeAddFiles() {\n    this.s = false;\n    this.generalService.slideOutElement('blur_bg2');\n    if (this.edit) {\n      this.onCloseAddFiles.emit();\n    }\n  }\n  modalStyle() {\n    let position = '';\n    if (this.edit) {\n      return {\n        position: 'relative',\n        marginLeft: '0',\n        transform: 'translate(0px, 0px)',\n        left: '0',\n        top: '0',\n        width: '950px',\n        height: 'auto'\n      };\n    } else {\n      return {\n        position: 'fixed'\n      };\n    }\n  }\n  getTeacherFileName(teacherFile) {\n    if (teacherFile && teacherFile.lastIndexOf('\\\\')) {\n      let n = teacherFile.lastIndexOf('\\\\');\n      let result = teacherFile.substring(n + 1);\n      return result;\n    }\n    return teacherFile;\n  }\n  uploadFinished(event) {\n    if (!this.task.teacherFiles?.some(el => this.getTeacherFileName(el.filePath) == this.getTeacherFileName(event.filePath.dbPath))) this.task.teacherFiles?.push({\n      filePath: event.filePath.dbPath,\n      levels: [],\n      categories: []\n    });\n    console.log(this.task.teacherFiles);\n  }\n  onTagChoosen(event) {\n    let isLevel = event.isLevel;\n    let arr = isLevel ? this.homework.levels : this.homework.categories;\n    if (event.checked) {\n      arr.push(event.tag);\n    } else {\n      let index = arr.findIndex(el => el == event.tag);\n      arr.splice(index, 1);\n    }\n  }\n  onTitleChange(event) {\n    this.task.title = this.taskForm.value.title;\n  }\n  onLinkChange(event) {\n    this.task.link = this.taskForm.value.link;\n  }\n  static #_ = this.ɵfac = function HomeworkTaskComponent_Factory(t) {\n    return new (t || HomeworkTaskComponent)(i0.ɵɵdirectiveInject(i1.GeneralService), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i2.LibraryService), i0.ɵɵdirectiveInject(i3.HomeworkService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: HomeworkTaskComponent,\n    selectors: [[\"app-homework-task\"]],\n    viewQuery: function HomeworkTaskComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.taskFiles = _t.first);\n      }\n    },\n    inputs: {\n      task: \"task\",\n      assignToUser: \"assignToUser\",\n      showSubmitButton: \"showSubmitButton\",\n      assignToClassroom: \"assignToClassroom\",\n      homework: \"homework\",\n      edit: \"edit\"\n    },\n    outputs: {\n      deleteTask: \"deleteTask\",\n      onOpenAddFiles: \"onOpenAddFiles\",\n      onCloseAddFiles: \"onCloseAddFiles\"\n    },\n    decls: 17,\n    vars: 5,\n    consts: [[\"taskFiles\", \"\"], [1, \"homework-task\", 3, \"formGroup\"], [1, \"input-fields\"], [1, \"input-field-100\"], [\"formControlName\", \"title\", \"type\", \"text\", \"placeholder\", \"Task title\", 1, \"input-element\", 3, \"input\"], [1, \"minus\", \"hvr-grow\", \"m-l-10\", 3, \"click\"], [1, \"minus-inside\"], [\"class\", \"input-error\", \"style\", \"margin-left:15px\", 4, \"ngIf\"], [\"formControlName\", \"link\", \"type\", \"text\", \"placeholder\", \"Homework Link\", 1, \"input-element\", 3, \"input\"], [\"class\", \"input-fields\", 4, \"ngFor\", \"ngForOf\"], [1, \"light-purple-button\", 2, \"margin\", \"10px\", \"width\", \"100px\", 3, \"click\"], [\"class\", \"modal p-0\", 4, \"ngIf\"], [\"id\", \"blur_bg2\", 1, \"blur-bg\", \"no-visibility\"], [2, \"margin\", \"0\", \"padding\", \"20px 0\", \"border-bottom\", \"1px dashed gray\", \"width\", \"100%\"], [1, \"input-error\", 2, \"margin-left\", \"15px\"], [\"src\", \"/assets/icons/file-icon.svg\"], [1, \"file-name\"], [1, \"modal\", \"p-0\"], [1, \"popup-title\", \"p-20\"], [\"src\", \"/assets/icons/close.png\", 1, \"close-img\", \"close-img-abs\", \"hvr-glow\", 3, \"click\"], [1, \"p-20\"], [1, \"libs-title\"], [\"title\", \"Task Files\", \"postUrl\", \"/Upload/UploadHomeworkTaskFile\", 3, \"onUploadFinished\", \"tagChoosen\", \"assignToClassroom\", \"inHomeworkTask\", \"homeworkTaskStudentId\", \"dndUI\", \"checkboxUI\", \"teacherFilesLength\"], [3, \"task\", \"inHomework\"], [\"class\", \"btns\", \"style\", \"position: sticky; width:100%; background-color: white; border-top:1px solid lightgray; bottom:0;\", 4, \"ngIf\"], [1, \"btns\", 2, \"position\", \"sticky\", \"width\", \"100%\", \"background-color\", \"white\", \"border-top\", \"1px solid lightgray\", \"bottom\", \"0\"], [1, \"light-purple-button\", 3, \"click\"]],\n    template: function HomeworkTaskComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"input\", 4);\n        i0.ɵɵlistener(\"input\", function HomeworkTaskComponent_Template_input_input_3_listener($event) {\n          return ctx.onTitleChange($event);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(4, \"div\", 5);\n        i0.ɵɵlistener(\"click\", function HomeworkTaskComponent_Template_div_click_4_listener() {\n          return ctx.onDeleteTask();\n        });\n        i0.ɵɵelement(5, \"div\", 6);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(6, HomeworkTaskComponent_div_6_Template, 2, 0, \"div\", 7);\n        i0.ɵɵelementStart(7, \"div\", 2)(8, \"div\", 3)(9, \"input\", 8);\n        i0.ɵɵlistener(\"input\", function HomeworkTaskComponent_Template_input_input_9_listener($event) {\n          return ctx.onLinkChange($event);\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(10, HomeworkTaskComponent_div_10_Template, 5, 1, \"div\", 9)(11, HomeworkTaskComponent_div_11_Template, 5, 1, \"div\", 9);\n        i0.ɵɵelementStart(12, \"div\", 10);\n        i0.ɵɵlistener(\"click\", function HomeworkTaskComponent_Template_div_click_12_listener() {\n          return ctx.showAddFiles();\n        });\n        i0.ɵɵtext(13, \" Add Files \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(14, HomeworkTaskComponent_div_14_Template, 14, 9, \"div\", 11);\n        i0.ɵɵelement(15, \"div\", 12)(16, \"div\", 13);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"formGroup\", ctx.taskForm);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngIf\", (ctx.isSubmitted || ctx.errorControl.title.touched) && (ctx.errorControl.title.errors == null ? null : ctx.errorControl.title.errors.required));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngForOf\", ctx.task.teacherFiles);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngForOf\", ctx.task.libraries);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.s);\n      }\n    },\n    dependencies: [i4.NgForOf, i4.NgIf, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.FormGroupDirective, i5.FormControlName],\n    styles: [\".input-fields[_ngcontent-%COMP%] {\\n  flex-wrap: nowrap;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n.input-fields[_ngcontent-%COMP%]   .input-field-100[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n  display: flex;\\n  align-items: center;\\n}\\n.input-fields[_ngcontent-%COMP%]   .input-field-100[_ngcontent-%COMP%]   .file-name[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 10px;\\n  border-radius: 32px;\\n  background-color: #d8d9e8;\\n}\\n.input-fields[_ngcontent-%COMP%]   .input-field-100[_ngcontent-%COMP%]    > img[_ngcontent-%COMP%] {\\n  margin-right: 20px;\\n  width: 30px;\\n}\\n.input-fields[_ngcontent-%COMP%]   .input-field-100[_ngcontent-%COMP%]   .input-element[_ngcontent-%COMP%] {\\n  margin-top: 0;\\n}\\n\\n.libs-title[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  font-size: 18px;\\n  padding: 20px 0;\\n  text-align: center;\\n  background: lightgray;\\n  border-radius: 12px;\\n  margin: 20px 0;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9ob21ld29yay9ob21ld29yay1mb3JtL2hvbWV3b3JrLXRhc2svaG9tZXdvcmstdGFzay5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSw4QkFBQTtBQUNKO0FBQUk7RUFDSSxhQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0FBRVI7QUFEUTtFQUNJLFdBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx5QkFBQTtBQUdaO0FBRFE7RUFDSSxrQkFBQTtFQUNBLFdBQUE7QUFHWjtBQURRO0VBQ0ksYUFBQTtBQUdaOztBQUVBO0VBQ0ksaUJBQUE7RUFDQSxlQUFBO0VBQ0EsZUFBQTtFQUNBLGtCQUFBO0VBQ0EscUJBQUE7RUFDQSxtQkFBQTtFQUNBLGNBQUE7QUFDSiIsInNvdXJjZXNDb250ZW50IjpbIi5pbnB1dC1maWVsZHMge1xyXG4gICAgZmxleC13cmFwOiBub3dyYXA7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gICAgLmlucHV0LWZpZWxkLTEwMCB7XHJcbiAgICAgICAgbWFyZ2luLXRvcDogMDtcclxuICAgICAgICBkaXNwbGF5OiBmbGV4O1xyXG4gICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICAgICAgLmZpbGUtbmFtZSB7XHJcbiAgICAgICAgICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgICAgICAgICBwYWRkaW5nOiAxMHB4O1xyXG4gICAgICAgICAgICBib3JkZXItcmFkaXVzOiAzMnB4O1xyXG4gICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZDhkOWU4O1xyXG4gICAgICAgIH1cclxuICAgICAgICA+IGltZyB7XHJcbiAgICAgICAgICAgIG1hcmdpbi1yaWdodDogMjBweDtcclxuICAgICAgICAgICAgd2lkdGg6IDMwcHg7XHJcbiAgICAgICAgfVxyXG4gICAgICAgIC5pbnB1dC1lbGVtZW50IHtcclxuICAgICAgICAgICAgbWFyZ2luLXRvcDogMDtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbn1cclxuXHJcbi5saWJzLXRpdGxlIHtcclxuICAgIGZvbnQtd2VpZ2h0OiBib2xkO1xyXG4gICAgZm9udC1zaXplOiAxOHB4O1xyXG4gICAgcGFkZGluZzogMjBweCAwO1xyXG4gICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gICAgYmFja2dyb3VuZDogbGlnaHRncmF5O1xyXG4gICAgYm9yZGVyLXJhZGl1czogMTJweDtcclxuICAgIG1hcmdpbjogMjBweCAwO1xyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "UntypedFormControl", "UntypedFormGroup", "Validators", "SubSink", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "getTeacherFileName", "teacherFile_r1", "filePath", "libFile_r3", "path", "ɵɵlistener", "HomeworkTaskComponent_div_14_div_13_Template_div_click_1_listener", "ɵɵrestoreView", "_r5", "ɵɵnextContext", "ɵɵresetView", "closeAddFiles", "HomeworkTaskComponent_div_14_Template_img_click_5_listener", "_r4", "HomeworkTaskComponent_div_14_Template_app_upload_files_onUploadFinished_9_listener", "$event", "uploadFinished", "HomeworkTaskComponent_div_14_Template_app_upload_files_tagChoosen_9_listener", "onTagChoosen", "ɵɵtemplate", "HomeworkTaskComponent_div_14_div_13_Template", "ɵɵproperty", "assignToClassroom", "assignToUser", "id", "task", "teacherFiles", "length", "showSubmitButton", "HomeworkTaskComponent", "constructor", "generalService", "renderer", "libraryService", "homeworkService", "homework", "edit", "deleteTask", "onOpenAddFiles", "onCloseAddFiles", "subs", "taskForm", "s", "isSubmitted", "uniqueTeacherFiles", "ngOnInit", "submitListener", "subscribe", "res", "setIsTaskFormValid", "valid", "title", "validators", "required", "link", "ngOnDestroy", "unsubscribe", "errorControl", "controls", "onDeleteTask", "emit", "showAddFiles", "slideInElement", "slideOutElement", "modalStyle", "position", "marginLeft", "transform", "left", "top", "width", "height", "teacherFile", "lastIndexOf", "n", "result", "substring", "event", "some", "el", "db<PERSON><PERSON>", "push", "levels", "categories", "console", "log", "isLevel", "arr", "checked", "tag", "index", "findIndex", "splice", "onTitleChange", "value", "onLinkChange", "_", "ɵɵdirectiveInject", "i1", "GeneralService", "Renderer2", "i2", "LibraryService", "i3", "HomeworkService", "_2", "selectors", "viewQuery", "HomeworkTaskComponent_Query", "rf", "ctx", "HomeworkTaskComponent_Template_input_input_3_listener", "HomeworkTaskComponent_Template_div_click_4_listener", "HomeworkTaskComponent_div_6_Template", "HomeworkTaskComponent_Template_input_input_9_listener", "HomeworkTaskComponent_div_10_Template", "HomeworkTaskComponent_div_11_Template", "HomeworkTaskComponent_Template_div_click_12_listener", "HomeworkTaskComponent_div_14_Template", "touched", "errors", "libraries"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\homework\\homework-form\\homework-task\\homework-task.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\homework\\homework-form\\homework-task\\homework-task.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output, Renderer2, ViewChild } from '@angular/core';\r\nimport { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\r\nimport { Classroom } from 'src/app/core/models/classroom.model';\r\nimport { Homework, HomeworkTask, HomeworkTaskFile } from 'src/app/core/models/homework.model';\r\nimport { Library } from 'src/app/core/models/library.model';\r\nimport { User } from 'src/app/core/models/user.model';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { HomeworkService } from 'src/app/core/services/homework.service';\r\nimport { LibraryService } from 'src/app/core/services/library.service';\r\nimport { SubSink } from 'subsink';\r\n\r\n@Component({\r\n  selector: 'app-homework-task',\r\n  templateUrl: './homework-task.component.html',\r\n  styleUrls: ['./homework-task.component.scss']\r\n})\r\nexport class HomeworkTaskComponent implements OnInit {\r\n  @ViewChild('taskFiles') public taskFiles: any\r\n  @Input() task: HomeworkTask = {} as HomeworkTask;\r\n  @Input() assignToUser?: User;\r\n  @Input() showSubmitButton: boolean = false;\r\n  @Input() assignToClassroom?: Classroom;\r\n  @Input() homework: Homework = {} as Homework;\r\n  @Input() edit: boolean = false;\r\n  @Output() deleteTask = new EventEmitter<{ task: HomeworkTask }>();\r\n  @Output() onOpenAddFiles = new EventEmitter();\r\n  @Output() onCloseAddFiles = new EventEmitter();\r\n  private subs = new SubSink();\r\n  public taskForm: UntypedFormGroup = new UntypedFormGroup({});\r\n  s: boolean = false\r\n  isSubmitted: boolean = false;\r\n  uniqueTeacherFiles: HomeworkTaskFile[] = []\r\n  constructor(\r\n    private generalService: GeneralService,\r\n    private renderer: Renderer2,\r\n    private libraryService: LibraryService,\r\n    private homeworkService: HomeworkService,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n\r\n    this.homeworkService.submitListener.subscribe(res => {\r\n      this.homeworkService.setIsTaskFormValid(this.taskForm.valid)\r\n      this.isSubmitted = res;\r\n    })\r\n    this.taskForm = new UntypedFormGroup({\r\n      title: new UntypedFormControl(this.task.title, {\r\n        validators: [Validators.required]\r\n      }),\r\n      link: new UntypedFormControl(this.task.link, {\r\n        // validators: [Validators.required]\r\n      })\r\n    })\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  get errorControl() {\r\n    return this.taskForm.controls;\r\n  }\r\n\r\n  onDeleteTask() {\r\n    this.deleteTask.emit({ task: this.task });\r\n  }\r\n\r\n  showAddFiles() {\r\n    this.generalService.slideInElement('blur_bg2')\r\n    this.s = true\r\n    if (this.edit) {\r\n      this.onOpenAddFiles.emit()\r\n    }\r\n  }\r\n\r\n  closeAddFiles() {\r\n    this.s = false\r\n    this.generalService.slideOutElement('blur_bg2')\r\n\r\n    if (this.edit) {\r\n      this.onCloseAddFiles.emit()\r\n    }\r\n  }\r\n\r\n  modalStyle() {\r\n    let position = ''\r\n    if (this.edit) {\r\n      return {\r\n        position: 'relative',\r\n        marginLeft: '0',\r\n        transform: 'translate(0px, 0px)',\r\n        left: '0',\r\n        top: '0',\r\n        width: '950px',\r\n        height: 'auto',\r\n      }\r\n    } else {\r\n      return {\r\n        position: 'fixed'\r\n      }\r\n    }\r\n\r\n  }\r\n\r\n  getTeacherFileName(teacherFile: string) {\r\n    if (teacherFile && teacherFile.lastIndexOf('\\\\')) {\r\n      let n = teacherFile.lastIndexOf('\\\\');\r\n      let result = teacherFile.substring(n + 1);\r\n      return result;\r\n    }\r\n    return teacherFile\r\n  }\r\n\r\n  uploadFinished(event: any) {\r\n    if (!this.task.teacherFiles?.some(el => this.getTeacherFileName(el.filePath) == this.getTeacherFileName(event.filePath.dbPath)))\r\n      this.task.teacherFiles?.push({ filePath: event.filePath.dbPath, levels: [], categories: [] })\r\n    console.log(this.task.teacherFiles)\r\n  }\r\n\r\n  onTagChoosen(event: any) {\r\n    let isLevel = event.isLevel;\r\n    let arr = isLevel ? this.homework.levels : this.homework.categories\r\n    if (event.checked) {\r\n      arr.push(event.tag)\r\n\r\n    } else {\r\n      let index = arr.findIndex(((el: string) => el == event.tag));\r\n      arr.splice(index, 1);\r\n\r\n    }\r\n  }\r\n\r\n  onTitleChange(event: any) {\r\n    this.task.title = this.taskForm.value.title;\r\n  }\r\n\r\n  onLinkChange(event: any) {\r\n    this.task.link = this.taskForm.value.link;\r\n  }\r\n}\r\n", "<!-- <ejs-checkbox label=\"\" (change)=\"finishTask($event, task)\" [checked]=\"task.finished\">\r\n</ejs-checkbox> {{task.title}} -->\r\n<div [formGroup]=\"taskForm\" class=\"homework-task\">\r\n    <div class=\"input-fields\">\r\n        <div class=\"input-field-100\">\r\n            <input class=\"input-element\" (input)=\"onTitleChange($event)\" formControlName=\"title\" type=\"text\"\r\n                placeholder=\"Task title\">\r\n        </div>\r\n        <div class=\"minus hvr-grow m-l-10\" (click)=\"onDeleteTask()\">\r\n            <div class=\"minus-inside\"></div>\r\n        </div>\r\n    </div>\r\n    <div *ngIf=\"(isSubmitted || errorControl.title.touched) && errorControl.title.errors?.required\" class=\"input-error\" style=\"margin-left:15px\">\r\n        Title is\r\n        required</div>\r\n    <div class=\"input-fields\">\r\n        <div class=\"input-field-100\">\r\n            <input class=\"input-element\" (input)=\"onLinkChange($event)\" formControlName=\"link\" type=\"text\"\r\n                placeholder=\"Homework Link\">\r\n        </div>\r\n    </div>\r\n    <div *ngFor=\"let teacherFile of task.teacherFiles\" class=\"input-fields\">\r\n        <div class=\"input-field-100\">\r\n            <img src=\"/assets/icons/file-icon.svg\">\r\n            <div class=\"file-name\">\r\n                {{getTeacherFileName(teacherFile.filePath)}}\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <div *ngFor=\"let libFile of task.libraries\" class=\"input-fields\">\r\n        <div class=\"input-field-100\">\r\n            <img src=\"/assets/icons/file-icon.svg\">\r\n            <div class=\"file-name\">\r\n                {{getTeacherFileName(libFile.path)}}\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <div class=\"light-purple-button\" style=\"margin:10px; width:100px;\" (click)=\"showAddFiles()\">\r\n        Add Files\r\n    </div>\r\n</div>\r\n\r\n<div *ngIf=\"s\" class=\"modal p-0\" #taskFiles>\r\n    <div class=\"popup-title p-20\">\r\n        <div>Add files</div>\r\n        <img (click)=\"closeAddFiles()\" src=\"/assets/icons/close.png\" class=\"close-img close-img-abs hvr-glow\">\r\n    </div>\r\n    <div class=\"p-20\">\r\n        <div class=\"libs-title\">\r\n            Add from your local files\r\n        </div>\r\n        <app-upload-files [assignToClassroom]=\"assignToClassroom\" [inHomeworkTask]=\"true\"\r\n            [homeworkTaskStudentId]=\"assignToUser?.id\" [dndUI]=\"true\" [checkboxUI]=\"true\" title=\"Task Files\"\r\n            postUrl=\"/Upload/UploadHomeworkTaskFile\" [teacherFilesLength]=\"task.teacherFiles.length\"\r\n            (onUploadFinished)=\"uploadFinished($event)\" (tagChoosen)=\"onTagChoosen($event)\">\r\n        </app-upload-files>\r\n        <div class=\"libs-title\">\r\n            Add from your libraries\r\n        </div>\r\n        <app-library [task]=\"task\" [inHomework]=\"true\">\r\n        </app-library>\r\n    </div>\r\n    <div *ngIf=\"showSubmitButton\" class=\"btns\"\r\n        style=\"position: sticky; width:100%; background-color: white; border-top:1px solid lightgray; bottom:0;\">\r\n        <div class=\"light-purple-button\" (click)=\"closeAddFiles()\">\r\n            SUBMIT\r\n        </div>\r\n    </div>\r\n</div>\r\n\r\n<div class=\"blur-bg no-visibility\" id=\"blur_bg2\"></div>\r\n\r\n<div style=\"margin:0; padding:20px 0; border-bottom: 1px dashed gray; width:100%\"></div>"], "mappings": "AAAA,SAAoBA,YAAY,QAAqD,eAAe;AACpG,SAASC,kBAAkB,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,gBAAgB;AAQjF,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;ICG7BC,EAAA,CAAAC,cAAA,cAA6I;IACzID,EAAA,CAAAE,MAAA,yBACQ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAQdH,EADJ,CAAAC,cAAA,aAAwE,aACvC;IACzBD,EAAA,CAAAI,SAAA,cAAuC;IACvCJ,EAAA,CAAAC,cAAA,cAAuB;IACnBD,EAAA,CAAAE,MAAA,GACJ;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;;;;;IAHMH,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,kBAAA,CAAAC,cAAA,CAAAC,QAAA,OACJ;;;;;IAIJV,EADJ,CAAAC,cAAA,aAAiE,aAChC;IACzBD,EAAA,CAAAI,SAAA,cAAuC;IACvCJ,EAAA,CAAAC,cAAA,cAAuB;IACnBD,EAAA,CAAAE,MAAA,GACJ;IAERF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;;;;;IAHMH,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAAM,kBAAA,MAAAC,MAAA,CAAAC,kBAAA,CAAAG,UAAA,CAAAC,IAAA,OACJ;;;;;;IA8BJZ,EAFJ,CAAAC,cAAA,cAC6G,cAC9C;IAA1BD,EAAA,CAAAa,UAAA,mBAAAC,kEAAA;MAAAd,EAAA,CAAAe,aAAA,CAAAC,GAAA;MAAA,MAAAT,MAAA,GAAAP,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASX,MAAA,CAAAY,aAAA,EAAe;IAAA,EAAC;IACtDnB,EAAA,CAAAE,MAAA,eACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;;;;;;IAvBFH,EAFR,CAAAC,cAAA,iBAA4C,cACV,UACrB;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpBH,EAAA,CAAAC,cAAA,cAAsG;IAAjGD,EAAA,CAAAa,UAAA,mBAAAO,2DAAA;MAAApB,EAAA,CAAAe,aAAA,CAAAM,GAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAASX,MAAA,CAAAY,aAAA,EAAe;IAAA,EAAC;IAClCnB,EADI,CAAAG,YAAA,EAAsG,EACpG;IAEFH,EADJ,CAAAC,cAAA,cAAkB,cACU;IACpBD,EAAA,CAAAE,MAAA,kCACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,2BAGoF;IAApCD,EAA5C,CAAAa,UAAA,8BAAAS,mFAAAC,MAAA;MAAAvB,EAAA,CAAAe,aAAA,CAAAM,GAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAoBX,MAAA,CAAAiB,cAAA,CAAAD,MAAA,CAAsB;IAAA,EAAC,wBAAAE,6EAAAF,MAAA;MAAAvB,EAAA,CAAAe,aAAA,CAAAM,GAAA;MAAA,MAAAd,MAAA,GAAAP,EAAA,CAAAiB,aAAA;MAAA,OAAAjB,EAAA,CAAAkB,WAAA,CAAeX,MAAA,CAAAmB,YAAA,CAAAH,MAAA,CAAoB;IAAA,EAAC;IACnFvB,EAAA,CAAAG,YAAA,EAAmB;IACnBH,EAAA,CAAAC,cAAA,eAAwB;IACpBD,EAAA,CAAAE,MAAA,iCACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAI,SAAA,uBACc;IAClBJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAA2B,UAAA,KAAAC,4CAAA,kBAC6G;IAKjH5B,EAAA,CAAAG,YAAA,EAAM;;;;IAjBoBH,EAAA,CAAAK,SAAA,GAAuC;IAEZL,EAF3B,CAAA6B,UAAA,sBAAAtB,MAAA,CAAAuB,iBAAA,CAAuC,wBAAwB,0BAAAvB,MAAA,CAAAwB,YAAA,kBAAAxB,MAAA,CAAAwB,YAAA,CAAAC,EAAA,CACnC,eAAe,oBAAoB,uBAAAzB,MAAA,CAAA0B,IAAA,CAAAC,YAAA,CAAAC,MAAA,CACW;IAM/EnC,EAAA,CAAAK,SAAA,GAAa;IAACL,EAAd,CAAA6B,UAAA,SAAAtB,MAAA,CAAA0B,IAAA,CAAa,oBAAoB;IAG5CjC,EAAA,CAAAK,SAAA,EAAsB;IAAtBL,EAAA,CAAA6B,UAAA,SAAAtB,MAAA,CAAA6B,gBAAA,CAAsB;;;AD9ChC,OAAM,MAAOC,qBAAqB;EAgBhCC,YACUC,cAA8B,EAC9BC,QAAmB,EACnBC,cAA8B,EAC9BC,eAAgC;IAHhC,KAAAH,cAAc,GAAdA,cAAc;IACd,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IAlBhB,KAAAT,IAAI,GAAiB,EAAkB;IAEvC,KAAAG,gBAAgB,GAAY,KAAK;IAEjC,KAAAO,QAAQ,GAAa,EAAc;IACnC,KAAAC,IAAI,GAAY,KAAK;IACpB,KAAAC,UAAU,GAAG,IAAIlD,YAAY,EAA0B;IACvD,KAAAmD,cAAc,GAAG,IAAInD,YAAY,EAAE;IACnC,KAAAoD,eAAe,GAAG,IAAIpD,YAAY,EAAE;IACtC,KAAAqD,IAAI,GAAG,IAAIjD,OAAO,EAAE;IACrB,KAAAkD,QAAQ,GAAqB,IAAIpD,gBAAgB,CAAC,EAAE,CAAC;IAC5D,KAAAqD,CAAC,GAAY,KAAK;IAClB,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAAC,kBAAkB,GAAuB,EAAE;EAMvC;EAEJC,QAAQA,CAAA;IAEN,IAAI,CAACX,eAAe,CAACY,cAAc,CAACC,SAAS,CAACC,GAAG,IAAG;MAClD,IAAI,CAACd,eAAe,CAACe,kBAAkB,CAAC,IAAI,CAACR,QAAQ,CAACS,KAAK,CAAC;MAC5D,IAAI,CAACP,WAAW,GAAGK,GAAG;IACxB,CAAC,CAAC;IACF,IAAI,CAACP,QAAQ,GAAG,IAAIpD,gBAAgB,CAAC;MACnC8D,KAAK,EAAE,IAAI/D,kBAAkB,CAAC,IAAI,CAACqC,IAAI,CAAC0B,KAAK,EAAE;QAC7CC,UAAU,EAAE,CAAC9D,UAAU,CAAC+D,QAAQ;OACjC,CAAC;MACFC,IAAI,EAAE,IAAIlE,kBAAkB,CAAC,IAAI,CAACqC,IAAI,CAAC6B,IAAI,EAAE;QAC3C;MAAA,CACD;KACF,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACf,IAAI,CAACgB,WAAW,EAAE;EACzB;EAEA,IAAIC,YAAYA,CAAA;IACd,OAAO,IAAI,CAAChB,QAAQ,CAACiB,QAAQ;EAC/B;EAEAC,YAAYA,CAAA;IACV,IAAI,CAACtB,UAAU,CAACuB,IAAI,CAAC;MAAEnC,IAAI,EAAE,IAAI,CAACA;IAAI,CAAE,CAAC;EAC3C;EAEAoC,YAAYA,CAAA;IACV,IAAI,CAAC9B,cAAc,CAAC+B,cAAc,CAAC,UAAU,CAAC;IAC9C,IAAI,CAACpB,CAAC,GAAG,IAAI;IACb,IAAI,IAAI,CAACN,IAAI,EAAE;MACb,IAAI,CAACE,cAAc,CAACsB,IAAI,EAAE;IAC5B;EACF;EAEAjD,aAAaA,CAAA;IACX,IAAI,CAAC+B,CAAC,GAAG,KAAK;IACd,IAAI,CAACX,cAAc,CAACgC,eAAe,CAAC,UAAU,CAAC;IAE/C,IAAI,IAAI,CAAC3B,IAAI,EAAE;MACb,IAAI,CAACG,eAAe,CAACqB,IAAI,EAAE;IAC7B;EACF;EAEAI,UAAUA,CAAA;IACR,IAAIC,QAAQ,GAAG,EAAE;IACjB,IAAI,IAAI,CAAC7B,IAAI,EAAE;MACb,OAAO;QACL6B,QAAQ,EAAE,UAAU;QACpBC,UAAU,EAAE,GAAG;QACfC,SAAS,EAAE,qBAAqB;QAChCC,IAAI,EAAE,GAAG;QACTC,GAAG,EAAE,GAAG;QACRC,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE;OACT;IACH,CAAC,MAAM;MACL,OAAO;QACLN,QAAQ,EAAE;OACX;IACH;EAEF;EAEAjE,kBAAkBA,CAACwE,WAAmB;IACpC,IAAIA,WAAW,IAAIA,WAAW,CAACC,WAAW,CAAC,IAAI,CAAC,EAAE;MAChD,IAAIC,CAAC,GAAGF,WAAW,CAACC,WAAW,CAAC,IAAI,CAAC;MACrC,IAAIE,MAAM,GAAGH,WAAW,CAACI,SAAS,CAACF,CAAC,GAAG,CAAC,CAAC;MACzC,OAAOC,MAAM;IACf;IACA,OAAOH,WAAW;EACpB;EAEAxD,cAAcA,CAAC6D,KAAU;IACvB,IAAI,CAAC,IAAI,CAACpD,IAAI,CAACC,YAAY,EAAEoD,IAAI,CAACC,EAAE,IAAI,IAAI,CAAC/E,kBAAkB,CAAC+E,EAAE,CAAC7E,QAAQ,CAAC,IAAI,IAAI,CAACF,kBAAkB,CAAC6E,KAAK,CAAC3E,QAAQ,CAAC8E,MAAM,CAAC,CAAC,EAC7H,IAAI,CAACvD,IAAI,CAACC,YAAY,EAAEuD,IAAI,CAAC;MAAE/E,QAAQ,EAAE2E,KAAK,CAAC3E,QAAQ,CAAC8E,MAAM;MAAEE,MAAM,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAE,CAAE,CAAC;IAC/FC,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC5D,IAAI,CAACC,YAAY,CAAC;EACrC;EAEAR,YAAYA,CAAC2D,KAAU;IACrB,IAAIS,OAAO,GAAGT,KAAK,CAACS,OAAO;IAC3B,IAAIC,GAAG,GAAGD,OAAO,GAAG,IAAI,CAACnD,QAAQ,CAAC+C,MAAM,GAAG,IAAI,CAAC/C,QAAQ,CAACgD,UAAU;IACnE,IAAIN,KAAK,CAACW,OAAO,EAAE;MACjBD,GAAG,CAACN,IAAI,CAACJ,KAAK,CAACY,GAAG,CAAC;IAErB,CAAC,MAAM;MACL,IAAIC,KAAK,GAAGH,GAAG,CAACI,SAAS,CAAGZ,EAAU,IAAKA,EAAE,IAAIF,KAAK,CAACY,GAAI,CAAC;MAC5DF,GAAG,CAACK,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IAEtB;EACF;EAEAG,aAAaA,CAAChB,KAAU;IACtB,IAAI,CAACpD,IAAI,CAAC0B,KAAK,GAAG,IAAI,CAACV,QAAQ,CAACqD,KAAK,CAAC3C,KAAK;EAC7C;EAEA4C,YAAYA,CAAClB,KAAU;IACrB,IAAI,CAACpD,IAAI,CAAC6B,IAAI,GAAG,IAAI,CAACb,QAAQ,CAACqD,KAAK,CAACxC,IAAI;EAC3C;EAAC,QAAA0C,CAAA,G;qBA1HUnE,qBAAqB,EAAArC,EAAA,CAAAyG,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA3G,EAAA,CAAAyG,iBAAA,CAAAzG,EAAA,CAAA4G,SAAA,GAAA5G,EAAA,CAAAyG,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAA9G,EAAA,CAAAyG,iBAAA,CAAAM,EAAA,CAAAC,eAAA;EAAA;EAAA,QAAAC,EAAA,G;UAArB5E,qBAAqB;IAAA6E,SAAA;IAAAC,SAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;QCXtBrH,EAHZ,CAAAC,cAAA,aAAkD,aACpB,aACO,eAEI;QADAD,EAAA,CAAAa,UAAA,mBAAA0G,sDAAAhG,MAAA;UAAA,OAAS+F,GAAA,CAAAjB,aAAA,CAAA9E,MAAA,CAAqB;QAAA,EAAC;QAEhEvB,EAFI,CAAAG,YAAA,EAC6B,EAC3B;QACNH,EAAA,CAAAC,cAAA,aAA4D;QAAzBD,EAAA,CAAAa,UAAA,mBAAA2G,oDAAA;UAAA,OAASF,GAAA,CAAAnD,YAAA,EAAc;QAAA,EAAC;QACvDnE,EAAA,CAAAI,SAAA,aAAgC;QAExCJ,EADI,CAAAG,YAAA,EAAM,EACJ;QACNH,EAAA,CAAA2B,UAAA,IAAA8F,oCAAA,iBAA6I;QAKrIzH,EAFR,CAAAC,cAAA,aAA0B,aACO,eAEO;QADHD,EAAA,CAAAa,UAAA,mBAAA6G,sDAAAnG,MAAA;UAAA,OAAS+F,GAAA,CAAAf,YAAA,CAAAhF,MAAA,CAAoB;QAAA,EAAC;QAGnEvB,EAHQ,CAAAG,YAAA,EACgC,EAC9B,EACJ;QASNH,EARA,CAAA2B,UAAA,KAAAgG,qCAAA,iBAAwE,KAAAC,qCAAA,iBAQP;QAQjE5H,EAAA,CAAAC,cAAA,eAA4F;QAAzBD,EAAA,CAAAa,UAAA,mBAAAgH,qDAAA;UAAA,OAASP,GAAA,CAAAjD,YAAA,EAAc;QAAA,EAAC;QACvFrE,EAAA,CAAAE,MAAA,mBACJ;QACJF,EADI,CAAAG,YAAA,EAAM,EACJ;QAENH,EAAA,CAAA2B,UAAA,KAAAmG,qCAAA,mBAA4C;QA8B5C9H,EAFA,CAAAI,SAAA,eAAuD,eAEiC;;;QAtEnFJ,EAAA,CAAA6B,UAAA,cAAAyF,GAAA,CAAArE,QAAA,CAAsB;QAUjBjD,EAAA,CAAAK,SAAA,GAAwF;QAAxFL,EAAA,CAAA6B,UAAA,UAAAyF,GAAA,CAAAnE,WAAA,IAAAmE,GAAA,CAAArD,YAAA,CAAAN,KAAA,CAAAoE,OAAA,MAAAT,GAAA,CAAArD,YAAA,CAAAN,KAAA,CAAAqE,MAAA,kBAAAV,GAAA,CAAArD,YAAA,CAAAN,KAAA,CAAAqE,MAAA,CAAAnE,QAAA,EAAwF;QASjE7D,EAAA,CAAAK,SAAA,GAAoB;QAApBL,EAAA,CAAA6B,UAAA,YAAAyF,GAAA,CAAArF,IAAA,CAAAC,YAAA,CAAoB;QAQxBlC,EAAA,CAAAK,SAAA,EAAiB;QAAjBL,EAAA,CAAA6B,UAAA,YAAAyF,GAAA,CAAArF,IAAA,CAAAgG,SAAA,CAAiB;QAaxCjI,EAAA,CAAAK,SAAA,GAAO;QAAPL,EAAA,CAAA6B,UAAA,SAAAyF,GAAA,CAAApE,CAAA,CAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}