{"ast": null, "code": "import { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i2 from '@angular/common';\nimport { DOCUMENT, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, booleanAttribute, numberAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, Output, ContentChildren, ViewChild, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i1 from 'primeng/api';\nimport { TranslationKeys, PrimeTemplate, SharedModule } from 'primeng/api';\nimport * as i3 from 'primeng/button';\nimport { ButtonModule } from 'primeng/button';\nimport { DomHandler, ConnectedOverlayScrollHandler } from 'primeng/dom';\nimport * as i4 from 'primeng/ripple';\nimport { RippleModule } from 'primeng/ripple';\nimport { UniqueComponentId, ObjectUtils, ZIndexUtils } from 'primeng/utils';\nimport { ChevronLeftIcon } from 'primeng/icons/chevronleft';\nimport { ChevronRightIcon } from 'primeng/icons/chevronright';\nimport { ChevronUpIcon } from 'primeng/icons/chevronup';\nimport { ChevronDownIcon } from 'primeng/icons/chevrondown';\nimport { TimesIcon } from 'primeng/icons/times';\nimport { CalendarIcon } from 'primeng/icons/calendar';\nimport * as i5 from 'primeng/autofocus';\nimport { AutoFocusModule } from 'primeng/autofocus';\nconst _c0 = [\"container\"];\nconst _c1 = [\"inputfield\"];\nconst _c2 = [\"contentWrapper\"];\nconst _c3 = [[[\"p-header\"]], [[\"p-footer\"]]];\nconst _c4 = [\"p-header\", \"p-footer\"];\nconst _c5 = (a0, a1, a2, a3, a4) => ({\n  \"p-calendar\": true,\n  \"p-input-icon-right\": a0,\n  \"p-calendar-w-btn\": a1,\n  \"p-calendar-timeonly\": a2,\n  \"p-calendar-disabled\": a3,\n  \"p-focus\": a4\n});\nconst _c6 = a0 => ({\n  clickCallBack: a0\n});\nconst _c7 = a0 => ({\n  \"p-datepicker-icon\": a0\n});\nconst _c8 = (a0, a1, a2, a3, a4, a5) => ({\n  \"p-datepicker p-component\": true,\n  \"p-datepicker-inline\": a0,\n  \"p-disabled\": a1,\n  \"p-datepicker-timeonly\": a2,\n  \"p-datepicker-multiple-month\": a3,\n  \"p-datepicker-monthpicker\": a4,\n  \"p-datepicker-touch-ui\": a5\n});\nconst _c9 = (a0, a1) => ({\n  showTransitionParams: a0,\n  hideTransitionParams: a1\n});\nconst _c10 = a0 => ({\n  value: \"visibleTouchUI\",\n  params: a0\n});\nconst _c11 = a0 => ({\n  value: \"visible\",\n  params: a0\n});\nconst _c12 = a0 => ({\n  $implicit: a0\n});\nconst _c13 = (a0, a1) => ({\n  \"p-datepicker-other-month\": a0,\n  \"p-datepicker-today\": a1\n});\nconst _c14 = (a0, a1) => ({\n  \"p-highlight\": a0,\n  \"p-disabled\": a1\n});\nconst _c15 = a0 => [a0];\nfunction Calendar_ng_template_2_ng_container_2_TimesIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"TimesIcon\", 11);\n    i0.ɵɵlistener(\"click\", function Calendar_ng_template_2_ng_container_2_TimesIcon_1_Template_TimesIcon_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.clear());\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-calendar-clear-icon\");\n  }\n}\nfunction Calendar_ng_template_2_ng_container_2_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Calendar_ng_template_2_ng_container_2_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Calendar_ng_template_2_ng_container_2_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Calendar_ng_template_2_ng_container_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵlistener(\"click\", function Calendar_ng_template_2_ng_container_2_span_2_Template_span_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.clear());\n    });\n    i0.ɵɵtemplate(1, Calendar_ng_template_2_ng_container_2_span_2_1_Template, 1, 0, null, 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.clearIconTemplate);\n  }\n}\nfunction Calendar_ng_template_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Calendar_ng_template_2_ng_container_2_TimesIcon_1_Template, 1, 1, \"TimesIcon\", 9)(2, Calendar_ng_template_2_ng_container_2_span_2_Template, 2, 1, \"span\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.clearIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.clearIconTemplate);\n  }\n}\nfunction Calendar_ng_template_2_button_3_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 16);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.icon);\n  }\n}\nfunction Calendar_ng_template_2_button_3_ng_container_2_CalendarIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"CalendarIcon\");\n  }\n}\nfunction Calendar_ng_template_2_button_3_ng_container_2_2_ng_template_0_Template(rf, ctx) {}\nfunction Calendar_ng_template_2_button_3_ng_container_2_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Calendar_ng_template_2_button_3_ng_container_2_2_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Calendar_ng_template_2_button_3_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Calendar_ng_template_2_button_3_ng_container_2_CalendarIcon_1_Template, 1, 0, \"CalendarIcon\", 7)(2, Calendar_ng_template_2_button_3_ng_container_2_2_Template, 1, 0, null, 13);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.triggerIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.triggerIconTemplate);\n  }\n}\nfunction Calendar_ng_template_2_button_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 14);\n    i0.ɵɵlistener(\"click\", function Calendar_ng_template_2_button_3_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      i0.ɵɵnextContext();\n      const inputfield_r6 = i0.ɵɵreference(1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onButtonClick($event, inputfield_r6));\n    });\n    i0.ɵɵtemplate(1, Calendar_ng_template_2_button_3_span_1_Template, 1, 1, \"span\", 15)(2, Calendar_ng_template_2_button_3_ng_container_2_Template, 3, 2, \"ng-container\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_6_0;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.iconButtonAriaLabel)(\"aria-expanded\", (tmp_6_0 = ctx_r1.overlayVisible) !== null && tmp_6_0 !== undefined ? tmp_6_0 : false)(\"aria-controls\", ctx_r1.overlayVisible ? ctx_r1.panelId : null);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.icon);\n  }\n}\nfunction Calendar_ng_template_2_ng_container_4_CalendarIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"CalendarIcon\", 19);\n    i0.ɵɵlistener(\"click\", function Calendar_ng_template_2_ng_container_4_CalendarIcon_1_Template_CalendarIcon_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onButtonClick($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c7, ctx_r1.showOnFocus));\n  }\n}\nfunction Calendar_ng_template_2_ng_container_4_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Calendar_ng_template_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Calendar_ng_template_2_ng_container_4_CalendarIcon_1_Template, 1, 3, \"CalendarIcon\", 17)(2, Calendar_ng_template_2_ng_container_4_ng_container_2_Template, 1, 0, \"ng-container\", 18);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.inputIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.inputIconTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(3, _c6, ctx_r1.onButtonClick.bind(ctx_r1)));\n  }\n}\nfunction Calendar_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"input\", 6, 1);\n    i0.ɵɵlistener(\"focus\", function Calendar_ng_template_2_Template_input_focus_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onInputFocus($event));\n    })(\"keydown\", function Calendar_ng_template_2_Template_input_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onInputKeydown($event));\n    })(\"click\", function Calendar_ng_template_2_Template_input_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onInputClick());\n    })(\"blur\", function Calendar_ng_template_2_Template_input_blur_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onInputBlur($event));\n    })(\"input\", function Calendar_ng_template_2_Template_input_input_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onUserInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, Calendar_ng_template_2_ng_container_2_Template, 3, 2, \"ng-container\", 7)(3, Calendar_ng_template_2_button_3_Template, 3, 6, \"button\", 8)(4, Calendar_ng_template_2_ng_container_4_Template, 3, 5, \"ng-container\", 7);\n  }\n  if (rf & 2) {\n    let tmp_15_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.inputStyleClass);\n    i0.ɵɵproperty(\"value\", ctx_r1.inputFieldValue)(\"readonly\", ctx_r1.readonlyInput)(\"ngStyle\", ctx_r1.inputStyle)(\"placeholder\", ctx_r1.placeholder || \"\")(\"disabled\", ctx_r1.disabled)(\"ngClass\", ctx_r1.inputClass)(\"autofocus\", ctx_r1.autofocus);\n    i0.ɵɵattribute(\"id\", ctx_r1.inputId)(\"name\", ctx_r1.name)(\"required\", ctx_r1.required)(\"aria-required\", ctx_r1.required)(\"aria-expanded\", (tmp_15_0 = ctx_r1.overlayVisible) !== null && tmp_15_0 !== undefined ? tmp_15_0 : false)(\"aria-controls\", ctx_r1.overlayVisible ? ctx_r1.panelId : null)(\"aria-labelledby\", ctx_r1.ariaLabelledBy)(\"aria-label\", ctx_r1.ariaLabel)(\"tabindex\", ctx_r1.tabindex)(\"inputmode\", ctx_r1.touchUI ? \"off\" : null);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showClear && !ctx_r1.disabled && ctx_r1.value != null);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showIcon && ctx_r1.iconDisplay === \"button\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.iconDisplay === \"input\" && ctx_r1.showIcon);\n  }\n}\nfunction Calendar_div_3_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_button_2_ChevronLeftIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronLeftIcon\", 40);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-datepicker-prev-icon\");\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_button_2_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction Calendar_div_3_ng_container_4_div_2_button_2_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Calendar_div_3_ng_container_4_div_2_button_2_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_button_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 41);\n    i0.ɵɵtemplate(1, Calendar_div_3_ng_container_4_div_2_button_2_span_2_1_Template, 1, 0, null, 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.previousIconTemplate);\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_button_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 38);\n    i0.ɵɵlistener(\"keydown\", function Calendar_div_3_ng_container_4_div_2_button_2_Template_button_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onContainerButtonKeydown($event));\n    })(\"click\", function Calendar_div_3_ng_container_4_div_2_button_2_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onPrevButtonClick($event));\n    });\n    i0.ɵɵtemplate(1, Calendar_div_3_ng_container_4_div_2_button_2_ChevronLeftIcon_1_Template, 1, 1, \"ChevronLeftIcon\", 35)(2, Calendar_div_3_ng_container_4_div_2_button_2_span_2_Template, 2, 1, \"span\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.prevIconAriaLabel);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.previousIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.previousIconTemplate);\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function Calendar_div_3_ng_container_4_div_2_button_4_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.switchToMonthView($event));\n    })(\"keydown\", function Calendar_div_3_ng_container_4_div_2_button_4_Template_button_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onContainerButtonKeydown($event));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const month_r12 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.switchViewButtonDisabled());\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.getTranslation(\"chooseMonth\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getMonthName(month_r12.month), \" \");\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_button_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function Calendar_div_3_ng_container_4_div_2_button_5_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.switchToYearView($event));\n    })(\"keydown\", function Calendar_div_3_ng_container_4_div_2_button_5_Template_button_keydown_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onContainerButtonKeydown($event));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const month_r12 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.switchViewButtonDisabled());\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.getTranslation(\"chooseYear\"));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getYear(month_r12), \" \");\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_span_6_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.yearPickerValues()[0], \" - \", ctx_r1.yearPickerValues()[ctx_r1.yearPickerValues().length - 1], \"\");\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_span_6_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 44);\n    i0.ɵɵtemplate(1, Calendar_div_3_ng_container_4_div_2_span_6_ng_container_1_Template, 2, 2, \"ng-container\", 7)(2, Calendar_div_3_ng_container_4_div_2_span_6_ng_container_2_Template, 1, 0, \"ng-container\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.decadeTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.decadeTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(3, _c12, ctx_r1.yearPickerValues));\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_ChevronRightIcon_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronRightIcon\", 40);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-datepicker-next-icon\");\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_span_9_1_ng_template_0_Template(rf, ctx) {}\nfunction Calendar_div_3_ng_container_4_div_2_span_9_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Calendar_div_3_ng_container_4_div_2_span_9_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 45);\n    i0.ɵɵtemplate(1, Calendar_div_3_ng_container_4_div_2_span_9_1_Template, 1, 0, null, 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.nextIconTemplate);\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_div_10_th_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 51)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getTranslation(\"weekHeader\"));\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_div_10_th_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 52)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const weekDay_r14 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(weekDay_r14);\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_div_10_tr_7_td_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 55)(1, \"span\", 56);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const j_r15 = i0.ɵɵnextContext().index;\n    const month_r12 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", month_r12.weekNumbers[j_r15], \" \");\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_div_10_tr_7_td_2_ng_container_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const date_r17 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(date_r17.day);\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_div_10_tr_7_td_2_ng_container_1_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_div_10_tr_7_td_2_ng_container_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Calendar_div_3_ng_container_4_div_2_div_10_tr_7_td_2_ng_container_1_ng_container_3_ng_container_1_Template, 1, 0, \"ng-container\", 18);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const date_r17 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.dateTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c12, date_r17));\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_div_10_tr_7_td_2_ng_container_1_ng_container_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_div_10_tr_7_td_2_ng_container_1_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, Calendar_div_3_ng_container_4_div_2_div_10_tr_7_td_2_ng_container_1_ng_container_4_ng_container_1_Template, 1, 0, \"ng-container\", 18);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const date_r17 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.disabledDateTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c12, date_r17));\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_div_10_tr_7_td_2_ng_container_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const date_r17 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", date_r17.day, \" \");\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_div_10_tr_7_td_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 57);\n    i0.ɵɵlistener(\"click\", function Calendar_div_3_ng_container_4_div_2_div_10_tr_7_td_2_ng_container_1_Template_span_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const date_r17 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(6);\n      return i0.ɵɵresetView(ctx_r1.onDateSelect($event, date_r17));\n    })(\"keydown\", function Calendar_div_3_ng_container_4_div_2_div_10_tr_7_td_2_ng_container_1_Template_span_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const date_r17 = i0.ɵɵnextContext().$implicit;\n      const i_r18 = i0.ɵɵnextContext(3).index;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onDateCellKeydown($event, date_r17, i_r18));\n    });\n    i0.ɵɵtemplate(2, Calendar_div_3_ng_container_4_div_2_div_10_tr_7_td_2_ng_container_1_ng_container_2_Template, 2, 1, \"ng-container\", 7)(3, Calendar_div_3_ng_container_4_div_2_div_10_tr_7_td_2_ng_container_1_ng_container_3_Template, 2, 4, \"ng-container\", 7)(4, Calendar_div_3_ng_container_4_div_2_div_10_tr_7_td_2_ng_container_1_ng_container_4_Template, 2, 4, \"ng-container\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, Calendar_div_3_ng_container_4_div_2_div_10_tr_7_td_2_ng_container_1_div_5_Template, 2, 1, \"div\", 58);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const date_r17 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(6, _c14, ctx_r1.isSelected(date_r17) && date_r17.selectable, !date_r17.selectable));\n    i0.ɵɵattribute(\"data-date\", ctx_r1.formatDateKey(ctx_r1.formatDateMetaToDate(date_r17)));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.dateTemplate && (date_r17.selectable || !ctx_r1.disabledDateTemplate));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", date_r17.selectable || !ctx_r1.disabledDateTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !date_r17.selectable);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isSelected(date_r17));\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_div_10_tr_7_td_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 16);\n    i0.ɵɵtemplate(1, Calendar_div_3_ng_container_4_div_2_div_10_tr_7_td_2_ng_container_1_Template, 6, 9, \"ng-container\", 7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const date_r17 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(3, _c13, date_r17.otherMonth, date_r17.today));\n    i0.ɵɵattribute(\"aria-label\", date_r17.day);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", date_r17.otherMonth ? ctx_r1.showOtherMonths : true);\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_div_10_tr_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵtemplate(1, Calendar_div_3_ng_container_4_div_2_div_10_tr_7_td_1_Template, 3, 1, \"td\", 53)(2, Calendar_div_3_ng_container_4_div_2_div_10_tr_7_td_2_Template, 2, 6, \"td\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const week_r19 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showWeek);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", week_r19);\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"table\", 47)(2, \"thead\")(3, \"tr\");\n    i0.ɵɵtemplate(4, Calendar_div_3_ng_container_4_div_2_div_10_th_4_Template, 3, 1, \"th\", 48)(5, Calendar_div_3_ng_container_4_div_2_div_10_th_5_Template, 3, 1, \"th\", 49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"tbody\");\n    i0.ɵɵtemplate(7, Calendar_div_3_ng_container_4_div_2_div_10_tr_7_Template, 3, 2, \"tr\", 50);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const month_r12 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showWeek);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.weekDays);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", month_r12.dates);\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 28);\n    i0.ɵɵtemplate(2, Calendar_div_3_ng_container_4_div_2_button_2_Template, 3, 3, \"button\", 29);\n    i0.ɵɵelementStart(3, \"div\", 30);\n    i0.ɵɵtemplate(4, Calendar_div_3_ng_container_4_div_2_button_4_Template, 2, 3, \"button\", 31)(5, Calendar_div_3_ng_container_4_div_2_button_5_Template, 2, 3, \"button\", 32)(6, Calendar_div_3_ng_container_4_div_2_span_6_Template, 3, 5, \"span\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 34);\n    i0.ɵɵlistener(\"keydown\", function Calendar_div_3_ng_container_4_div_2_Template_button_keydown_7_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onContainerButtonKeydown($event));\n    })(\"click\", function Calendar_div_3_ng_container_4_div_2_Template_button_click_7_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onNextButtonClick($event));\n    });\n    i0.ɵɵtemplate(8, Calendar_div_3_ng_container_4_div_2_ChevronRightIcon_8_Template, 1, 1, \"ChevronRightIcon\", 35)(9, Calendar_div_3_ng_container_4_div_2_span_9_Template, 2, 1, \"span\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(10, Calendar_div_3_ng_container_4_div_2_div_10_Template, 8, 3, \"div\", 37);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r18 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i_r18 === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentView === \"date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentView !== \"year\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentView === \"year\");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"display\", ctx_r1.numberOfMonths === 1 ? \"inline-flex\" : i_r18 === ctx_r1.numberOfMonths - 1 ? \"inline-flex\" : \"none\");\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.nextIconAriaLabel);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.nextIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.nextIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentView === \"date\");\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_3_span_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const m_r22 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", m_r22, \" \");\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_3_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 62);\n    i0.ɵɵlistener(\"click\", function Calendar_div_3_ng_container_4_div_3_span_1_Template_span_click_0_listener($event) {\n      const i_r21 = i0.ɵɵrestoreView(_r20).index;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onMonthSelect($event, i_r21));\n    })(\"keydown\", function Calendar_div_3_ng_container_4_div_3_span_1_Template_span_keydown_0_listener($event) {\n      const i_r21 = i0.ɵɵrestoreView(_r20).index;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onMonthCellKeydown($event, i_r21));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, Calendar_div_3_ng_container_4_div_3_span_1_div_2_Template, 2, 1, \"div\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const m_r22 = ctx.$implicit;\n    const i_r21 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(3, _c14, ctx_r1.isMonthSelected(i_r21), ctx_r1.isMonthDisabled(i_r21)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", m_r22, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isMonthSelected(i_r21));\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵtemplate(1, Calendar_div_3_ng_container_4_div_3_span_1_Template, 3, 6, \"span\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.monthPickerValues());\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_4_span_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const y_r24 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", y_r24, \" \");\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_4_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 65);\n    i0.ɵɵlistener(\"click\", function Calendar_div_3_ng_container_4_div_4_span_1_Template_span_click_0_listener($event) {\n      const y_r24 = i0.ɵɵrestoreView(_r23).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onYearSelect($event, y_r24));\n    })(\"keydown\", function Calendar_div_3_ng_container_4_div_4_span_1_Template_span_keydown_0_listener($event) {\n      const y_r24 = i0.ɵɵrestoreView(_r23).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onYearCellKeydown($event, y_r24));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, Calendar_div_3_ng_container_4_div_4_span_1_div_2_Template, 2, 1, \"div\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const y_r24 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(3, _c14, ctx_r1.isYearSelected(y_r24), ctx_r1.isYearDisabled(y_r24)));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", y_r24, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isYearSelected(y_r24));\n  }\n}\nfunction Calendar_div_3_ng_container_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63);\n    i0.ɵɵtemplate(1, Calendar_div_3_ng_container_4_div_4_span_1_Template, 3, 6, \"span\", 64);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.yearPickerValues());\n  }\n}\nfunction Calendar_div_3_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 23);\n    i0.ɵɵtemplate(2, Calendar_div_3_ng_container_4_div_2_Template, 11, 10, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, Calendar_div_3_ng_container_4_div_3_Template, 2, 1, \"div\", 25)(4, Calendar_div_3_ng_container_4_div_4_Template, 2, 1, \"div\", 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.months);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentView === \"month\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentView === \"year\");\n  }\n}\nfunction Calendar_div_3_div_5_ChevronUpIcon_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronUpIcon\");\n  }\n}\nfunction Calendar_div_3_div_5_4_ng_template_0_Template(rf, ctx) {}\nfunction Calendar_div_3_div_5_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Calendar_div_3_div_5_4_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Calendar_div_3_div_5_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \"0\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction Calendar_div_3_div_5_ChevronDownIcon_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronDownIcon\");\n  }\n}\nfunction Calendar_div_3_div_5_10_ng_template_0_Template(rf, ctx) {}\nfunction Calendar_div_3_div_5_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Calendar_div_3_div_5_10_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Calendar_div_3_div_5_ChevronUpIcon_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronUpIcon\");\n  }\n}\nfunction Calendar_div_3_div_5_17_ng_template_0_Template(rf, ctx) {}\nfunction Calendar_div_3_div_5_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Calendar_div_3_div_5_17_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Calendar_div_3_div_5_ng_container_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \"0\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction Calendar_div_3_div_5_ChevronDownIcon_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronDownIcon\");\n  }\n}\nfunction Calendar_div_3_div_5_23_ng_template_0_Template(rf, ctx) {}\nfunction Calendar_div_3_div_5_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Calendar_div_3_div_5_23_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Calendar_div_3_div_5_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 69)(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.timeSeparator);\n  }\n}\nfunction Calendar_div_3_div_5_div_25_ChevronUpIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronUpIcon\");\n  }\n}\nfunction Calendar_div_3_div_5_div_25_3_ng_template_0_Template(rf, ctx) {}\nfunction Calendar_div_3_div_5_div_25_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Calendar_div_3_div_5_div_25_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Calendar_div_3_div_5_div_25_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \"0\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction Calendar_div_3_div_5_div_25_ChevronDownIcon_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronDownIcon\");\n  }\n}\nfunction Calendar_div_3_div_5_div_25_9_ng_template_0_Template(rf, ctx) {}\nfunction Calendar_div_3_div_5_div_25_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Calendar_div_3_div_5_div_25_9_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Calendar_div_3_div_5_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"button\", 68);\n    i0.ɵɵlistener(\"keydown\", function Calendar_div_3_div_5_div_25_Template_button_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onContainerButtonKeydown($event));\n    })(\"keydown.enter\", function Calendar_div_3_div_5_div_25_Template_button_keydown_enter_1_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.incrementSecond($event));\n    })(\"keydown.space\", function Calendar_div_3_div_5_div_25_Template_button_keydown_space_1_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.incrementSecond($event));\n    })(\"mousedown\", function Calendar_div_3_div_5_div_25_Template_button_mousedown_1_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseDown($event, 2, 1));\n    })(\"mouseup\", function Calendar_div_3_div_5_div_25_Template_button_mouseup_1_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseUp($event));\n    })(\"keyup.enter\", function Calendar_div_3_div_5_div_25_Template_button_keyup_enter_1_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseUp($event));\n    })(\"keyup.space\", function Calendar_div_3_div_5_div_25_Template_button_keyup_space_1_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseUp($event));\n    })(\"mouseleave\", function Calendar_div_3_div_5_div_25_Template_button_mouseleave_1_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseLeave());\n    });\n    i0.ɵɵtemplate(2, Calendar_div_3_div_5_div_25_ChevronUpIcon_2_Template, 1, 0, \"ChevronUpIcon\", 7)(3, Calendar_div_3_div_5_div_25_3_Template, 1, 0, null, 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtemplate(5, Calendar_div_3_div_5_div_25_ng_container_5_Template, 2, 0, \"ng-container\", 7);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 68);\n    i0.ɵɵlistener(\"keydown\", function Calendar_div_3_div_5_div_25_Template_button_keydown_7_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onContainerButtonKeydown($event));\n    })(\"keydown.enter\", function Calendar_div_3_div_5_div_25_Template_button_keydown_enter_7_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.decrementSecond($event));\n    })(\"keydown.space\", function Calendar_div_3_div_5_div_25_Template_button_keydown_space_7_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.decrementSecond($event));\n    })(\"mousedown\", function Calendar_div_3_div_5_div_25_Template_button_mousedown_7_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseDown($event, 2, -1));\n    })(\"mouseup\", function Calendar_div_3_div_5_div_25_Template_button_mouseup_7_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseUp($event));\n    })(\"keyup.enter\", function Calendar_div_3_div_5_div_25_Template_button_keyup_enter_7_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseUp($event));\n    })(\"keyup.space\", function Calendar_div_3_div_5_div_25_Template_button_keyup_space_7_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseUp($event));\n    })(\"mouseleave\", function Calendar_div_3_div_5_div_25_Template_button_mouseleave_7_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseLeave());\n    });\n    i0.ɵɵtemplate(8, Calendar_div_3_div_5_div_25_ChevronDownIcon_8_Template, 1, 0, \"ChevronDownIcon\", 7)(9, Calendar_div_3_div_5_div_25_9_Template, 1, 0, null, 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.getTranslation(\"nextSecond\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.incrementIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.incrementIconTemplate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentSecond < 10);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.currentSecond);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.getTranslation(\"prevSecond\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.decrementIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.decrementIconTemplate);\n  }\n}\nfunction Calendar_div_3_div_5_div_26_ChevronUpIcon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronUpIcon\");\n  }\n}\nfunction Calendar_div_3_div_5_div_26_3_ng_template_0_Template(rf, ctx) {}\nfunction Calendar_div_3_div_5_div_26_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Calendar_div_3_div_5_div_26_3_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Calendar_div_3_div_5_div_26_ChevronDownIcon_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"ChevronDownIcon\");\n  }\n}\nfunction Calendar_div_3_div_5_div_26_8_ng_template_0_Template(rf, ctx) {}\nfunction Calendar_div_3_div_5_div_26_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, Calendar_div_3_div_5_div_26_8_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction Calendar_div_3_div_5_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 75)(1, \"button\", 76);\n    i0.ɵɵlistener(\"keydown\", function Calendar_div_3_div_5_div_26_Template_button_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onContainerButtonKeydown($event));\n    })(\"click\", function Calendar_div_3_div_5_div_26_Template_button_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.toggleAMPM($event));\n    })(\"keydown.enter\", function Calendar_div_3_div_5_div_26_Template_button_keydown_enter_1_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.toggleAMPM($event));\n    });\n    i0.ɵɵtemplate(2, Calendar_div_3_div_5_div_26_ChevronUpIcon_2_Template, 1, 0, \"ChevronUpIcon\", 7)(3, Calendar_div_3_div_5_div_26_3_Template, 1, 0, null, 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 76);\n    i0.ɵɵlistener(\"keydown\", function Calendar_div_3_div_5_div_26_Template_button_keydown_6_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onContainerButtonKeydown($event));\n    })(\"click\", function Calendar_div_3_div_5_div_26_Template_button_click_6_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.toggleAMPM($event));\n    })(\"keydown.enter\", function Calendar_div_3_div_5_div_26_Template_button_keydown_enter_6_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.toggleAMPM($event));\n    });\n    i0.ɵɵtemplate(7, Calendar_div_3_div_5_div_26_ChevronDownIcon_7_Template, 1, 0, \"ChevronDownIcon\", 7)(8, Calendar_div_3_div_5_div_26_8_Template, 1, 0, null, 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.getTranslation(\"am\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.incrementIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.incrementIconTemplate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.pm ? \"PM\" : \"AM\");\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.getTranslation(\"pm\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.decrementIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.decrementIconTemplate);\n  }\n}\nfunction Calendar_div_3_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"div\", 67)(2, \"button\", 68);\n    i0.ɵɵlistener(\"keydown\", function Calendar_div_3_div_5_Template_button_keydown_2_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onContainerButtonKeydown($event));\n    })(\"keydown.enter\", function Calendar_div_3_div_5_Template_button_keydown_enter_2_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.incrementHour($event));\n    })(\"keydown.space\", function Calendar_div_3_div_5_Template_button_keydown_space_2_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.incrementHour($event));\n    })(\"mousedown\", function Calendar_div_3_div_5_Template_button_mousedown_2_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseDown($event, 0, 1));\n    })(\"mouseup\", function Calendar_div_3_div_5_Template_button_mouseup_2_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseUp($event));\n    })(\"keyup.enter\", function Calendar_div_3_div_5_Template_button_keyup_enter_2_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseUp($event));\n    })(\"keyup.space\", function Calendar_div_3_div_5_Template_button_keyup_space_2_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseUp($event));\n    })(\"mouseleave\", function Calendar_div_3_div_5_Template_button_mouseleave_2_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseLeave());\n    });\n    i0.ɵɵtemplate(3, Calendar_div_3_div_5_ChevronUpIcon_3_Template, 1, 0, \"ChevronUpIcon\", 7)(4, Calendar_div_3_div_5_4_Template, 1, 0, null, 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtemplate(6, Calendar_div_3_div_5_ng_container_6_Template, 2, 0, \"ng-container\", 7);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 68);\n    i0.ɵɵlistener(\"keydown\", function Calendar_div_3_div_5_Template_button_keydown_8_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onContainerButtonKeydown($event));\n    })(\"keydown.enter\", function Calendar_div_3_div_5_Template_button_keydown_enter_8_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.decrementHour($event));\n    })(\"keydown.space\", function Calendar_div_3_div_5_Template_button_keydown_space_8_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.decrementHour($event));\n    })(\"mousedown\", function Calendar_div_3_div_5_Template_button_mousedown_8_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseDown($event, 0, -1));\n    })(\"mouseup\", function Calendar_div_3_div_5_Template_button_mouseup_8_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseUp($event));\n    })(\"keyup.enter\", function Calendar_div_3_div_5_Template_button_keyup_enter_8_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseUp($event));\n    })(\"keyup.space\", function Calendar_div_3_div_5_Template_button_keyup_space_8_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseUp($event));\n    })(\"mouseleave\", function Calendar_div_3_div_5_Template_button_mouseleave_8_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseLeave());\n    });\n    i0.ɵɵtemplate(9, Calendar_div_3_div_5_ChevronDownIcon_9_Template, 1, 0, \"ChevronDownIcon\", 7)(10, Calendar_div_3_div_5_10_Template, 1, 0, null, 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 69)(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 70)(15, \"button\", 68);\n    i0.ɵɵlistener(\"keydown\", function Calendar_div_3_div_5_Template_button_keydown_15_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onContainerButtonKeydown($event));\n    })(\"keydown.enter\", function Calendar_div_3_div_5_Template_button_keydown_enter_15_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.incrementMinute($event));\n    })(\"keydown.space\", function Calendar_div_3_div_5_Template_button_keydown_space_15_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.incrementMinute($event));\n    })(\"mousedown\", function Calendar_div_3_div_5_Template_button_mousedown_15_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseDown($event, 1, 1));\n    })(\"mouseup\", function Calendar_div_3_div_5_Template_button_mouseup_15_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseUp($event));\n    })(\"keyup.enter\", function Calendar_div_3_div_5_Template_button_keyup_enter_15_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseUp($event));\n    })(\"keyup.space\", function Calendar_div_3_div_5_Template_button_keyup_space_15_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseUp($event));\n    })(\"mouseleave\", function Calendar_div_3_div_5_Template_button_mouseleave_15_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseLeave());\n    });\n    i0.ɵɵtemplate(16, Calendar_div_3_div_5_ChevronUpIcon_16_Template, 1, 0, \"ChevronUpIcon\", 7)(17, Calendar_div_3_div_5_17_Template, 1, 0, null, 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\");\n    i0.ɵɵtemplate(19, Calendar_div_3_div_5_ng_container_19_Template, 2, 0, \"ng-container\", 7);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 68);\n    i0.ɵɵlistener(\"keydown\", function Calendar_div_3_div_5_Template_button_keydown_21_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onContainerButtonKeydown($event));\n    })(\"keydown.enter\", function Calendar_div_3_div_5_Template_button_keydown_enter_21_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.decrementMinute($event));\n    })(\"keydown.space\", function Calendar_div_3_div_5_Template_button_keydown_space_21_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.decrementMinute($event));\n    })(\"mousedown\", function Calendar_div_3_div_5_Template_button_mousedown_21_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseDown($event, 1, -1));\n    })(\"mouseup\", function Calendar_div_3_div_5_Template_button_mouseup_21_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseUp($event));\n    })(\"keyup.enter\", function Calendar_div_3_div_5_Template_button_keyup_enter_21_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseUp($event));\n    })(\"keyup.space\", function Calendar_div_3_div_5_Template_button_keyup_space_21_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseUp($event));\n    })(\"mouseleave\", function Calendar_div_3_div_5_Template_button_mouseleave_21_listener() {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTimePickerElementMouseLeave());\n    });\n    i0.ɵɵtemplate(22, Calendar_div_3_div_5_ChevronDownIcon_22_Template, 1, 0, \"ChevronDownIcon\", 7)(23, Calendar_div_3_div_5_23_Template, 1, 0, null, 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(24, Calendar_div_3_div_5_div_24_Template, 3, 1, \"div\", 71)(25, Calendar_div_3_div_5_div_25_Template, 10, 8, \"div\", 72)(26, Calendar_div_3_div_5_div_26_Template, 9, 7, \"div\", 73);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.getTranslation(\"nextHour\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.incrementIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.incrementIconTemplate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentHour < 10);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.currentHour);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.getTranslation(\"prevHour\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.decrementIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.decrementIconTemplate);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.timeSeparator);\n    i0.ɵɵadvance(2);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.getTranslation(\"nextMinute\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.incrementIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.incrementIconTemplate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentMinute < 10);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.currentMinute);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.getTranslation(\"prevMinute\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.decrementIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.decrementIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showSeconds);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showSeconds);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hourFormat == \"12\");\n  }\n}\nfunction Calendar_div_3_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"button\", 78);\n    i0.ɵɵlistener(\"keydown\", function Calendar_div_3_div_6_Template_button_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onContainerButtonKeydown($event));\n    })(\"click\", function Calendar_div_3_div_6_Template_button_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onTodayButtonClick($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"button\", 78);\n    i0.ɵɵlistener(\"keydown\", function Calendar_div_3_div_6_Template_button_keydown_2_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onContainerButtonKeydown($event));\n    })(\"click\", function Calendar_div_3_div_6_Template_button_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onClearButtonClick($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", ctx_r1.getTranslation(\"today\"))(\"ngClass\", i0.ɵɵpureFunction1(4, _c15, ctx_r1.todayButtonStyleClass));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"label\", ctx_r1.getTranslation(\"clear\"))(\"ngClass\", i0.ɵɵpureFunction1(6, _c15, ctx_r1.clearButtonStyleClass));\n  }\n}\nfunction Calendar_div_3_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction Calendar_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20, 2);\n    i0.ɵɵlistener(\"@overlayAnimation.start\", function Calendar_div_3_Template_div_animation_overlayAnimation_start_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onOverlayAnimationStart($event));\n    })(\"@overlayAnimation.done\", function Calendar_div_3_Template_div_animation_overlayAnimation_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onOverlayAnimationDone($event));\n    })(\"click\", function Calendar_div_3_Template_div_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onOverlayClick($event));\n    });\n    i0.ɵɵprojection(2);\n    i0.ɵɵtemplate(3, Calendar_div_3_ng_container_3_Template, 1, 0, \"ng-container\", 13)(4, Calendar_div_3_ng_container_4_Template, 5, 3, \"ng-container\", 7)(5, Calendar_div_3_div_5_Template, 27, 20, \"div\", 21)(6, Calendar_div_3_div_6_Template, 3, 8, \"div\", 22);\n    i0.ɵɵprojection(7, 1);\n    i0.ɵɵtemplate(8, Calendar_div_3_ng_container_8_Template, 1, 0, \"ng-container\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassMap(ctx_r1.panelStyleClass);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.panelStyle)(\"ngClass\", i0.ɵɵpureFunction6(15, _c8, ctx_r1.inline, ctx_r1.disabled, ctx_r1.timeOnly, ctx_r1.numberOfMonths > 1, ctx_r1.view === \"month\", ctx_r1.touchUI))(\"@overlayAnimation\", ctx_r1.touchUI ? i0.ɵɵpureFunction1(25, _c10, i0.ɵɵpureFunction2(22, _c9, ctx_r1.showTransitionOptions, ctx_r1.hideTransitionOptions)) : i0.ɵɵpureFunction1(30, _c11, i0.ɵɵpureFunction2(27, _c9, ctx_r1.showTransitionOptions, ctx_r1.hideTransitionOptions)))(\"@.disabled\", ctx_r1.inline === true);\n    i0.ɵɵattribute(\"id\", ctx_r1.panelId)(\"aria-label\", ctx_r1.getTranslation(\"chooseDate\"))(\"role\", ctx_r1.inline ? null : \"dialog\")(\"aria-modal\", ctx_r1.inline ? null : \"true\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.headerTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.timeOnly);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.showTime || ctx_r1.timeOnly) && ctx_r1.currentView === \"date\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showButtonBar);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.footerTemplate);\n  }\n}\nconst CALENDAR_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => Calendar),\n  multi: true\n};\n/**\n * Calendar also known as DatePicker, is a form component to work with dates.\n * @group Components\n */\nlet Calendar = /*#__PURE__*/(() => {\n  class Calendar {\n    document;\n    el;\n    renderer;\n    cd;\n    zone;\n    config;\n    overlayService;\n    iconDisplay = 'button';\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Inline style of the input field.\n     * @group Props\n     */\n    inputStyle;\n    /**\n     * Identifier of the focus input to match a label defined for the component.\n     * @group Props\n     */\n    inputId;\n    /**\n     * Name of the input element.\n     * @group Props\n     */\n    name;\n    /**\n     * Style class of the input field.\n     * @group Props\n     */\n    inputStyleClass;\n    /**\n     * Placeholder text for the input.\n     * @group Props\n     */\n    placeholder;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * Defines a string that labels the input for accessibility.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Defines a string that labels the icon button for accessibility.\n     * @group Props\n     */\n    iconAriaLabel;\n    /**\n     * When specified, disables the component.\n     * @group Props\n     */\n    disabled;\n    /**\n     * Format of the date which can also be defined at locale settings.\n     * @group Props\n     */\n    dateFormat;\n    /**\n     * Separator for multiple selection mode.\n     * @group Props\n     */\n    multipleSeparator = ',';\n    /**\n     * Separator for joining start and end dates on range selection mode.\n     * @group Props\n     */\n    rangeSeparator = '-';\n    /**\n     * When enabled, displays the calendar as inline. Default is false for popup mode.\n     * @group Props\n     */\n    inline = false;\n    /**\n     * Whether to display dates in other months (non-selectable) at the start or end of the current month. To make these days selectable use the selectOtherMonths option.\n     * @group Props\n     */\n    showOtherMonths = true;\n    /**\n     * Whether days in other months shown before or after the current month are selectable. This only applies if the showOtherMonths option is set to true.\n     * @group Props\n     */\n    selectOtherMonths;\n    /**\n     * When enabled, displays a button with icon next to input.\n     * @group Props\n     */\n    showIcon;\n    /**\n     * Icon of the calendar button.\n     * @group Props\n     */\n    icon;\n    /**\n     * Target element to attach the overlay, valid values are \"body\" or a local ng-template variable of another element (note: use binding with brackets for template variables, e.g. [appendTo]=\"mydiv\" for a div element having#mydiv as variable name).\n     * @group Props\n     */\n    appendTo;\n    /**\n     * When specified, prevents entering the date manually with keyboard.\n     * @group Props\n     */\n    readonlyInput;\n    /**\n     * The cutoff year for determining the century for a date.\n     * @group Props\n     */\n    shortYearCutoff = '+10';\n    /**\n     * Whether the month should be rendered as a dropdown instead of text.\n     * @group Props\n     * @deprecated Navigator is always on.\n     */\n    monthNavigator;\n    /**\n     * Whether the year should be rendered as a dropdown instead of text.\n     * @group Props\n     * @deprecated  Navigator is always on.\n     */\n    yearNavigator;\n    /**\n     * Specifies 12 or 24 hour format.\n     * @group Props\n     */\n    hourFormat = '24';\n    /**\n     * Whether to display timepicker only.\n     * @group Props\n     */\n    timeOnly;\n    /**\n     * Years to change per step in yearpicker.\n     * @group Props\n     */\n    stepYearPicker = 10;\n    /**\n     * Hours to change per step.\n     * @group Props\n     */\n    stepHour = 1;\n    /**\n     * Minutes to change per step.\n     * @group Props\n     */\n    stepMinute = 1;\n    /**\n     * Seconds to change per step.\n     * @group Props\n     */\n    stepSecond = 1;\n    /**\n     * Whether to show the seconds in time picker.\n     * @group Props\n     */\n    showSeconds = false;\n    /**\n     * When present, it specifies that an input field must be filled out before submitting the form.\n     * @group Props\n     */\n    required;\n    /**\n     * When disabled, datepicker will not be visible with input focus.\n     * @group Props\n     */\n    showOnFocus = true;\n    /**\n     * When enabled, calendar will show week numbers.\n     * @group Props\n     */\n    showWeek = false;\n    /**\n     * When enabled, calendar will start week numbers from first day of the year.\n     * @group Props\n     */\n    startWeekFromFirstDayOfYear = false;\n    /**\n     * When enabled, a clear icon is displayed to clear the value.\n     * @group Props\n     */\n    showClear = false;\n    /**\n     * Type of the value to write back to ngModel, default is date and alternative is string.\n     * @group Props\n     */\n    dataType = 'date';\n    /**\n     * Defines the quantity of the selection, valid values are \"single\", \"multiple\" and \"range\".\n     * @group Props\n     */\n    selectionMode = 'single';\n    /**\n     * Maximum number of selectable dates in multiple mode.\n     * @group Props\n     */\n    maxDateCount;\n    /**\n     * Whether to display today and clear buttons at the footer\n     * @group Props\n     */\n    showButtonBar;\n    /**\n     * Style class of the today button.\n     * @group Props\n     */\n    todayButtonStyleClass = 'p-button-text';\n    /**\n     * Style class of the clear button.\n     * @group Props\n     */\n    clearButtonStyleClass = 'p-button-text';\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    autofocus;\n    /**\n     * Whether to automatically manage layering.\n     * @group Props\n     */\n    autoZIndex = true;\n    /**\n     * Base zIndex value to use in layering.\n     * @group Props\n     */\n    baseZIndex = 0;\n    /**\n     * Style class of the datetimepicker container element.\n     * @group Props\n     */\n    panelStyleClass;\n    /**\n     * Inline style of the datetimepicker container element.\n     * @group Props\n     */\n    panelStyle;\n    /**\n     * Keep invalid value when input blur.\n     * @group Props\n     */\n    keepInvalid = false;\n    /**\n     * Whether to hide the overlay on date selection.\n     * @group Props\n     */\n    hideOnDateTimeSelect = true;\n    /**\n     * When enabled, calendar overlay is displayed as optimized for touch devices.\n     * @group Props\n     */\n    touchUI;\n    /**\n     * Separator of time selector.\n     * @group Props\n     */\n    timeSeparator = ':';\n    /**\n     * When enabled, can only focus on elements inside the calendar.\n     * @group Props\n     */\n    focusTrap = true;\n    /**\n     * Transition options of the show animation.\n     * @group Props\n     */\n    showTransitionOptions = '.12s cubic-bezier(0, 0, 0.2, 1)';\n    /**\n     * Transition options of the hide animation.\n     * @group Props\n     */\n    hideTransitionOptions = '.1s linear';\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    tabindex;\n    /**\n     * Specifies the input variant of the component.\n     * @group Props\n     */\n    variant = 'outlined';\n    /**\n     * The minimum selectable date.\n     * @group Props\n     */\n    get minDate() {\n      return this._minDate;\n    }\n    set minDate(date) {\n      this._minDate = date;\n      if (this.currentMonth != undefined && this.currentMonth != null && this.currentYear) {\n        this.createMonths(this.currentMonth, this.currentYear);\n      }\n    }\n    /**\n     * The maximum selectable date.\n     * @group Props\n     */\n    get maxDate() {\n      return this._maxDate;\n    }\n    set maxDate(date) {\n      this._maxDate = date;\n      if (this.currentMonth != undefined && this.currentMonth != null && this.currentYear) {\n        this.createMonths(this.currentMonth, this.currentYear);\n      }\n    }\n    /**\n     * Array with dates that should be disabled (not selectable).\n     * @group Props\n     */\n    get disabledDates() {\n      return this._disabledDates;\n    }\n    set disabledDates(disabledDates) {\n      this._disabledDates = disabledDates;\n      if (this.currentMonth != undefined && this.currentMonth != null && this.currentYear) {\n        this.createMonths(this.currentMonth, this.currentYear);\n      }\n    }\n    /**\n     * Array with weekday numbers that should be disabled (not selectable).\n     * @group Props\n     */\n    get disabledDays() {\n      return this._disabledDays;\n    }\n    set disabledDays(disabledDays) {\n      this._disabledDays = disabledDays;\n      if (this.currentMonth != undefined && this.currentMonth != null && this.currentYear) {\n        this.createMonths(this.currentMonth, this.currentYear);\n      }\n    }\n    /**\n     * The range of years displayed in the year drop-down in (nnnn:nnnn) format such as (2000:2020).\n     * @group Props\n     * @deprecated Years are based on decades by default.\n     */\n    get yearRange() {\n      return this._yearRange;\n    }\n    set yearRange(yearRange) {\n      this._yearRange = yearRange;\n      if (yearRange) {\n        const years = yearRange.split(':');\n        const yearStart = parseInt(years[0]);\n        const yearEnd = parseInt(years[1]);\n        this.populateYearOptions(yearStart, yearEnd);\n      }\n    }\n    /**\n     * Whether to display timepicker.\n     * @group Props\n     */\n    get showTime() {\n      return this._showTime;\n    }\n    set showTime(showTime) {\n      this._showTime = showTime;\n      if (this.currentHour === undefined) {\n        this.initTime(this.value || new Date());\n      }\n      this.updateInputfield();\n    }\n    /**\n     * An array of options for responsive design.\n     * @group Props\n     */\n    get responsiveOptions() {\n      return this._responsiveOptions;\n    }\n    set responsiveOptions(responsiveOptions) {\n      this._responsiveOptions = responsiveOptions;\n      this.destroyResponsiveStyleElement();\n      this.createResponsiveStyle();\n    }\n    /**\n     * Number of months to display.\n     * @group Props\n     */\n    get numberOfMonths() {\n      return this._numberOfMonths;\n    }\n    set numberOfMonths(numberOfMonths) {\n      this._numberOfMonths = numberOfMonths;\n      this.destroyResponsiveStyleElement();\n      this.createResponsiveStyle();\n    }\n    /**\n     * Defines the first of the week for various date calculations.\n     * @group Props\n     */\n    get firstDayOfWeek() {\n      return this._firstDayOfWeek;\n    }\n    set firstDayOfWeek(firstDayOfWeek) {\n      this._firstDayOfWeek = firstDayOfWeek;\n      this.createWeekDays();\n    }\n    /**\n     * Option to set calendar locale.\n     * @group Props\n     * @deprecated Locale property has no effect, use new i18n API instead.\n     */\n    set locale(newLocale) {\n      console.warn('Locale property has no effect, use new i18n API instead.');\n    }\n    /**\n     * Type of view to display, valid values are \"date\" for datepicker and \"month\" for month picker.\n     * @group Props\n     */\n    get view() {\n      return this._view;\n    }\n    set view(view) {\n      this._view = view;\n      this.currentView = this._view;\n    }\n    /**\n     * Set the date to highlight on first opening if the field is blank.\n     * @group Props\n     */\n    get defaultDate() {\n      return this._defaultDate;\n    }\n    set defaultDate(defaultDate) {\n      this._defaultDate = defaultDate;\n      if (this.initialized) {\n        const date = defaultDate || new Date();\n        this.currentMonth = date.getMonth();\n        this.currentYear = date.getFullYear();\n        this.initTime(date);\n        this.createMonths(this.currentMonth, this.currentYear);\n      }\n    }\n    get inputClass() {\n      return {\n        'p-inputtext p-component': true,\n        'p-variant-filled': this.variant === 'filled' || this.config.inputStyle() === 'filled'\n      };\n    }\n    /**\n     * Callback to invoke on focus of input field.\n     * @param {Event} event - browser event.\n     * @group Emits\n     */\n    onFocus = new EventEmitter();\n    /**\n     * Callback to invoke on blur of input field.\n     * @param {Event} event - browser event.\n     * @group Emits\n     */\n    onBlur = new EventEmitter();\n    /**\n     * Callback to invoke when date panel closed.\n     * @param {Event} event - Mouse event\n     * @group Emits\n     */\n    onClose = new EventEmitter();\n    /**\n     * Callback to invoke on date select.\n     * @param {Date} date - date value.\n     * @group Emits\n     */\n    onSelect = new EventEmitter();\n    /**\n     * Callback to invoke when input field cleared.\n     * @group Emits\n     */\n    onClear = new EventEmitter();\n    /**\n     * Callback to invoke when input field is being typed.\n     * @param {Event} event - browser event\n     * @group Emits\n     */\n    onInput = new EventEmitter();\n    /**\n     * Callback to invoke when today button is clicked.\n     * @param {Date} date - today as a date instance.\n     * @group Emits\n     */\n    onTodayClick = new EventEmitter();\n    /**\n     * Callback to invoke when clear button is clicked.\n     * @param {Event} event - browser event.\n     * @group Emits\n     */\n    onClearClick = new EventEmitter();\n    /**\n     * Callback to invoke when a month is changed using the navigators.\n     * @param {CalendarMonthChangeEvent} event - custom month change event.\n     * @group Emits\n     */\n    onMonthChange = new EventEmitter();\n    /**\n     * Callback to invoke when a year is changed using the navigators.\n     * @param {CalendarYearChangeEvent} event - custom year change event.\n     * @group Emits\n     */\n    onYearChange = new EventEmitter();\n    /**\n     * Callback to invoke when clicked outside of the date panel.\n     * @group Emits\n     */\n    onClickOutside = new EventEmitter();\n    /**\n     * Callback to invoke when datepicker panel is shown.\n     * @group Emits\n     */\n    onShow = new EventEmitter();\n    templates;\n    containerViewChild;\n    inputfieldViewChild;\n    set content(content) {\n      this.contentViewChild = content;\n      if (this.contentViewChild) {\n        if (this.isMonthNavigate) {\n          Promise.resolve(null).then(() => this.updateFocus());\n          this.isMonthNavigate = false;\n        } else {\n          if (!this.focus && !this.inline) {\n            this.initFocusableCell();\n          }\n        }\n      }\n    }\n    contentViewChild;\n    value;\n    dates;\n    months;\n    weekDays;\n    currentMonth;\n    currentYear;\n    currentHour;\n    currentMinute;\n    currentSecond;\n    pm;\n    mask;\n    maskClickListener;\n    overlay;\n    responsiveStyleElement;\n    overlayVisible;\n    onModelChange = () => {};\n    onModelTouched = () => {};\n    calendarElement;\n    timePickerTimer;\n    documentClickListener;\n    animationEndListener;\n    ticksTo1970;\n    yearOptions;\n    focus;\n    isKeydown;\n    filled;\n    inputFieldValue = null;\n    _minDate;\n    _maxDate;\n    _showTime;\n    _yearRange;\n    preventDocumentListener;\n    dateTemplate;\n    headerTemplate;\n    footerTemplate;\n    disabledDateTemplate;\n    decadeTemplate;\n    previousIconTemplate;\n    nextIconTemplate;\n    triggerIconTemplate;\n    clearIconTemplate;\n    decrementIconTemplate;\n    incrementIconTemplate;\n    inputIconTemplate;\n    _disabledDates;\n    _disabledDays;\n    selectElement;\n    todayElement;\n    focusElement;\n    scrollHandler;\n    documentResizeListener;\n    navigationState = null;\n    isMonthNavigate;\n    initialized;\n    translationSubscription;\n    _locale;\n    _responsiveOptions;\n    currentView;\n    attributeSelector;\n    panelId;\n    _numberOfMonths = 1;\n    _firstDayOfWeek;\n    _view = 'date';\n    preventFocus;\n    _defaultDate;\n    _focusKey = null;\n    window;\n    get locale() {\n      return this._locale;\n    }\n    get iconButtonAriaLabel() {\n      return this.iconAriaLabel ? this.iconAriaLabel : this.getTranslation('chooseDate');\n    }\n    get prevIconAriaLabel() {\n      return this.currentView === 'year' ? this.getTranslation('prevDecade') : this.currentView === 'month' ? this.getTranslation('prevYear') : this.getTranslation('prevMonth');\n    }\n    get nextIconAriaLabel() {\n      return this.currentView === 'year' ? this.getTranslation('nextDecade') : this.currentView === 'month' ? this.getTranslation('nextYear') : this.getTranslation('nextMonth');\n    }\n    constructor(document, el, renderer, cd, zone, config, overlayService) {\n      this.document = document;\n      this.el = el;\n      this.renderer = renderer;\n      this.cd = cd;\n      this.zone = zone;\n      this.config = config;\n      this.overlayService = overlayService;\n      this.window = this.document.defaultView;\n    }\n    ngOnInit() {\n      this.attributeSelector = UniqueComponentId();\n      this.panelId = this.attributeSelector + '_panel';\n      const date = this.defaultDate || new Date();\n      this.createResponsiveStyle();\n      this.currentMonth = date.getMonth();\n      this.currentYear = date.getFullYear();\n      this.yearOptions = [];\n      this.currentView = this.view;\n      if (this.view === 'date') {\n        this.createWeekDays();\n        this.initTime(date);\n        this.createMonths(this.currentMonth, this.currentYear);\n        this.ticksTo1970 = ((1970 - 1) * 365 + Math.floor(1970 / 4) - Math.floor(1970 / 100) + Math.floor(1970 / 400)) * 24 * 60 * 60 * 10000000;\n      }\n      this.translationSubscription = this.config.translationObserver.subscribe(() => {\n        this.createWeekDays();\n        this.cd.markForCheck();\n      });\n      this.initialized = true;\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'date':\n            this.dateTemplate = item.template;\n            break;\n          case 'decade':\n            this.decadeTemplate = item.template;\n            break;\n          case 'disabledDate':\n            this.disabledDateTemplate = item.template;\n            break;\n          case 'header':\n            this.headerTemplate = item.template;\n            break;\n          case 'inputicon':\n            this.inputIconTemplate = item.template;\n            break;\n          case 'previousicon':\n            this.previousIconTemplate = item.template;\n            break;\n          case 'nexticon':\n            this.nextIconTemplate = item.template;\n            break;\n          case 'triggericon':\n            this.triggerIconTemplate = item.template;\n            break;\n          case 'clearicon':\n            this.clearIconTemplate = item.template;\n            break;\n          case 'decrementicon':\n            this.decrementIconTemplate = item.template;\n            break;\n          case 'incrementicon':\n            this.incrementIconTemplate = item.template;\n            break;\n          case 'footer':\n            this.footerTemplate = item.template;\n            break;\n          default:\n            this.dateTemplate = item.template;\n            break;\n        }\n      });\n    }\n    ngAfterViewInit() {\n      if (this.inline) {\n        this.contentViewChild && this.contentViewChild.nativeElement.setAttribute(this.attributeSelector, '');\n        if (!this.disabled && !this.inline) {\n          this.initFocusableCell();\n          if (this.numberOfMonths === 1) {\n            if (this.contentViewChild && this.contentViewChild.nativeElement) {\n              this.contentViewChild.nativeElement.style.width = DomHandler.getOuterWidth(this.containerViewChild?.nativeElement) + 'px';\n            }\n          }\n        }\n      }\n    }\n    getTranslation(option) {\n      return this.config.getTranslation(option);\n    }\n    populateYearOptions(start, end) {\n      this.yearOptions = [];\n      for (let i = start; i <= end; i++) {\n        this.yearOptions.push(i);\n      }\n    }\n    createWeekDays() {\n      this.weekDays = [];\n      let dayIndex = this.getFirstDateOfWeek();\n      let dayLabels = this.getTranslation(TranslationKeys.DAY_NAMES_MIN);\n      for (let i = 0; i < 7; i++) {\n        this.weekDays.push(dayLabels[dayIndex]);\n        dayIndex = dayIndex == 6 ? 0 : ++dayIndex;\n      }\n    }\n    monthPickerValues() {\n      let monthPickerValues = [];\n      for (let i = 0; i <= 11; i++) {\n        monthPickerValues.push(this.config.getTranslation('monthNamesShort')[i]);\n      }\n      return monthPickerValues;\n    }\n    yearPickerValues() {\n      let yearPickerValues = [];\n      let base = this.currentYear - this.currentYear % this.stepYearPicker;\n      for (let i = 0; i < this.stepYearPicker; i++) {\n        yearPickerValues.push(base + i);\n      }\n      return yearPickerValues;\n    }\n    createMonths(month, year) {\n      this.months = this.months = [];\n      for (let i = 0; i < this.numberOfMonths; i++) {\n        let m = month + i;\n        let y = year;\n        if (m > 11) {\n          m = m % 11 - 1;\n          y = year + 1;\n        }\n        this.months.push(this.createMonth(m, y));\n      }\n    }\n    getWeekNumber(date) {\n      let checkDate = new Date(date.getTime());\n      if (this.startWeekFromFirstDayOfYear) {\n        let firstDayOfWeek = +this.getFirstDateOfWeek();\n        checkDate.setDate(checkDate.getDate() + 6 + firstDayOfWeek - checkDate.getDay());\n      } else {\n        checkDate.setDate(checkDate.getDate() + 4 - (checkDate.getDay() || 7));\n      }\n      let time = checkDate.getTime();\n      checkDate.setMonth(0);\n      checkDate.setDate(1);\n      return Math.floor(Math.round((time - checkDate.getTime()) / 86400000) / 7) + 1;\n    }\n    createMonth(month, year) {\n      let dates = [];\n      let firstDay = this.getFirstDayOfMonthIndex(month, year);\n      let daysLength = this.getDaysCountInMonth(month, year);\n      let prevMonthDaysLength = this.getDaysCountInPrevMonth(month, year);\n      let dayNo = 1;\n      let today = new Date();\n      let weekNumbers = [];\n      let monthRows = Math.ceil((daysLength + firstDay) / 7);\n      for (let i = 0; i < monthRows; i++) {\n        let week = [];\n        if (i == 0) {\n          for (let j = prevMonthDaysLength - firstDay + 1; j <= prevMonthDaysLength; j++) {\n            let prev = this.getPreviousMonthAndYear(month, year);\n            week.push({\n              day: j,\n              month: prev.month,\n              year: prev.year,\n              otherMonth: true,\n              today: this.isToday(today, j, prev.month, prev.year),\n              selectable: this.isSelectable(j, prev.month, prev.year, true)\n            });\n          }\n          let remainingDaysLength = 7 - week.length;\n          for (let j = 0; j < remainingDaysLength; j++) {\n            week.push({\n              day: dayNo,\n              month: month,\n              year: year,\n              today: this.isToday(today, dayNo, month, year),\n              selectable: this.isSelectable(dayNo, month, year, false)\n            });\n            dayNo++;\n          }\n        } else {\n          for (let j = 0; j < 7; j++) {\n            if (dayNo > daysLength) {\n              let next = this.getNextMonthAndYear(month, year);\n              week.push({\n                day: dayNo - daysLength,\n                month: next.month,\n                year: next.year,\n                otherMonth: true,\n                today: this.isToday(today, dayNo - daysLength, next.month, next.year),\n                selectable: this.isSelectable(dayNo - daysLength, next.month, next.year, true)\n              });\n            } else {\n              week.push({\n                day: dayNo,\n                month: month,\n                year: year,\n                today: this.isToday(today, dayNo, month, year),\n                selectable: this.isSelectable(dayNo, month, year, false)\n              });\n            }\n            dayNo++;\n          }\n        }\n        if (this.showWeek) {\n          weekNumbers.push(this.getWeekNumber(new Date(week[0].year, week[0].month, week[0].day)));\n        }\n        dates.push(week);\n      }\n      return {\n        month: month,\n        year: year,\n        dates: dates,\n        weekNumbers: weekNumbers\n      };\n    }\n    initTime(date) {\n      this.pm = date.getHours() > 11;\n      if (this.showTime) {\n        this.currentMinute = date.getMinutes();\n        this.currentSecond = date.getSeconds();\n        this.setCurrentHourPM(date.getHours());\n      } else if (this.timeOnly) {\n        this.currentMinute = 0;\n        this.currentHour = 0;\n        this.currentSecond = 0;\n      }\n    }\n    navBackward(event) {\n      if (this.disabled) {\n        event.preventDefault();\n        return;\n      }\n      this.isMonthNavigate = true;\n      if (this.currentView === 'month') {\n        this.decrementYear();\n        setTimeout(() => {\n          this.updateFocus();\n        }, 1);\n      } else if (this.currentView === 'year') {\n        this.decrementYearPickerStep();\n        setTimeout(() => {\n          this.updateFocus();\n        }, 1);\n      } else {\n        if (this.currentMonth === 0) {\n          this.currentMonth = 11;\n          this.decrementYear();\n        } else {\n          this.currentMonth--;\n        }\n        this.onMonthChange.emit({\n          month: this.currentMonth + 1,\n          year: this.currentYear\n        });\n        this.createMonths(this.currentMonth, this.currentYear);\n      }\n    }\n    navForward(event) {\n      if (this.disabled) {\n        event.preventDefault();\n        return;\n      }\n      this.isMonthNavigate = true;\n      if (this.currentView === 'month') {\n        this.incrementYear();\n        setTimeout(() => {\n          this.updateFocus();\n        }, 1);\n      } else if (this.currentView === 'year') {\n        this.incrementYearPickerStep();\n        setTimeout(() => {\n          this.updateFocus();\n        }, 1);\n      } else {\n        if (this.currentMonth === 11) {\n          this.currentMonth = 0;\n          this.incrementYear();\n        } else {\n          this.currentMonth++;\n        }\n        this.onMonthChange.emit({\n          month: this.currentMonth + 1,\n          year: this.currentYear\n        });\n        this.createMonths(this.currentMonth, this.currentYear);\n      }\n    }\n    decrementYear() {\n      this.currentYear--;\n      let _yearOptions = this.yearOptions;\n      if (this.yearNavigator && this.currentYear < _yearOptions[0]) {\n        let difference = _yearOptions[_yearOptions.length - 1] - _yearOptions[0];\n        this.populateYearOptions(_yearOptions[0] - difference, _yearOptions[_yearOptions.length - 1] - difference);\n      }\n    }\n    decrementYearPickerStep() {\n      this.currentYear = this.currentYear - this.stepYearPicker;\n    }\n    incrementYearPickerStep() {\n      this.currentYear = this.currentYear + this.stepYearPicker;\n    }\n    incrementYear() {\n      this.currentYear++;\n      let _yearOptions = this.yearOptions;\n      if (this.yearNavigator && this.currentYear > _yearOptions[_yearOptions.length - 1]) {\n        let difference = _yearOptions[_yearOptions.length - 1] - _yearOptions[0];\n        this.populateYearOptions(_yearOptions[0] + difference, _yearOptions[_yearOptions.length - 1] + difference);\n      }\n    }\n    switchToMonthView(event) {\n      this.setCurrentView('month');\n      event.preventDefault();\n    }\n    switchToYearView(event) {\n      this.setCurrentView('year');\n      event.preventDefault();\n    }\n    onDateSelect(event, dateMeta) {\n      if (this.disabled || !dateMeta.selectable) {\n        event.preventDefault();\n        return;\n      }\n      if (this.isMultipleSelection() && this.isSelected(dateMeta)) {\n        this.value = this.value.filter((date, i) => {\n          return !this.isDateEquals(date, dateMeta);\n        });\n        if (this.value.length === 0) {\n          this.value = null;\n        }\n        this.updateModel(this.value);\n      } else {\n        if (this.shouldSelectDate(dateMeta)) {\n          this.selectDate(dateMeta);\n        }\n      }\n      if (this.hideOnDateTimeSelect && (this.isSingleSelection() || this.isRangeSelection() && this.value[1])) {\n        setTimeout(() => {\n          event.preventDefault();\n          this.hideOverlay();\n          if (this.mask) {\n            this.disableModality();\n          }\n          this.cd.markForCheck();\n        }, 150);\n      }\n      this.updateInputfield();\n      event.preventDefault();\n    }\n    shouldSelectDate(dateMeta) {\n      if (this.isMultipleSelection()) return this.maxDateCount != null ? this.maxDateCount > (this.value ? this.value.length : 0) : true;else return true;\n    }\n    onMonthSelect(event, index) {\n      if (this.view === 'month') {\n        this.onDateSelect(event, {\n          year: this.currentYear,\n          month: index,\n          day: 1,\n          selectable: true\n        });\n      } else {\n        this.currentMonth = index;\n        this.createMonths(this.currentMonth, this.currentYear);\n        this.setCurrentView('date');\n        this.onMonthChange.emit({\n          month: this.currentMonth + 1,\n          year: this.currentYear\n        });\n      }\n    }\n    onYearSelect(event, year) {\n      if (this.view === 'year') {\n        this.onDateSelect(event, {\n          year: year,\n          month: 0,\n          day: 1,\n          selectable: true\n        });\n      } else {\n        this.currentYear = year;\n        this.setCurrentView('month');\n        this.onYearChange.emit({\n          month: this.currentMonth + 1,\n          year: this.currentYear\n        });\n      }\n    }\n    updateInputfield() {\n      let formattedValue = '';\n      if (this.value) {\n        if (this.isSingleSelection()) {\n          formattedValue = this.formatDateTime(this.value);\n        } else if (this.isMultipleSelection()) {\n          for (let i = 0; i < this.value.length; i++) {\n            let dateAsString = this.formatDateTime(this.value[i]);\n            formattedValue += dateAsString;\n            if (i !== this.value.length - 1) {\n              formattedValue += this.multipleSeparator + ' ';\n            }\n          }\n        } else if (this.isRangeSelection()) {\n          if (this.value && this.value.length) {\n            let startDate = this.value[0];\n            let endDate = this.value[1];\n            formattedValue = this.formatDateTime(startDate);\n            if (endDate) {\n              formattedValue += ' ' + this.rangeSeparator + ' ' + this.formatDateTime(endDate);\n            }\n          }\n        }\n      }\n      this.inputFieldValue = formattedValue;\n      this.updateFilledState();\n      if (this.inputfieldViewChild && this.inputfieldViewChild.nativeElement) {\n        this.inputfieldViewChild.nativeElement.value = this.inputFieldValue;\n      }\n    }\n    formatDateTime(date) {\n      let formattedValue = this.keepInvalid ? date : null;\n      const isDateValid = this.isValidDateForTimeConstraints(date);\n      if (this.isValidDate(date)) {\n        if (this.timeOnly) {\n          formattedValue = this.formatTime(date);\n        } else {\n          formattedValue = this.formatDate(date, this.getDateFormat());\n          if (this.showTime) {\n            formattedValue += ' ' + this.formatTime(date);\n          }\n        }\n      } else if (this.dataType === 'string') {\n        formattedValue = date;\n      }\n      formattedValue = isDateValid ? formattedValue : '';\n      return formattedValue;\n    }\n    formatDateMetaToDate(dateMeta) {\n      return new Date(dateMeta.year, dateMeta.month, dateMeta.day);\n    }\n    formatDateKey(date) {\n      return `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`;\n    }\n    setCurrentHourPM(hours) {\n      if (this.hourFormat == '12') {\n        this.pm = hours > 11;\n        if (hours >= 12) {\n          this.currentHour = hours == 12 ? 12 : hours - 12;\n        } else {\n          this.currentHour = hours == 0 ? 12 : hours;\n        }\n      } else {\n        this.currentHour = hours;\n      }\n    }\n    setCurrentView(currentView) {\n      this.currentView = currentView;\n      this.cd.detectChanges();\n      this.alignOverlay();\n    }\n    selectDate(dateMeta) {\n      let date = this.formatDateMetaToDate(dateMeta);\n      if (this.showTime) {\n        if (this.hourFormat == '12') {\n          if (this.currentHour === 12) date.setHours(this.pm ? 12 : 0);else date.setHours(this.pm ? this.currentHour + 12 : this.currentHour);\n        } else {\n          date.setHours(this.currentHour);\n        }\n        date.setMinutes(this.currentMinute);\n        date.setSeconds(this.currentSecond);\n      }\n      if (this.minDate && this.minDate > date) {\n        date = this.minDate;\n        this.setCurrentHourPM(date.getHours());\n        this.currentMinute = date.getMinutes();\n        this.currentSecond = date.getSeconds();\n      }\n      if (this.maxDate && this.maxDate < date) {\n        date = this.maxDate;\n        this.setCurrentHourPM(date.getHours());\n        this.currentMinute = date.getMinutes();\n        this.currentSecond = date.getSeconds();\n      }\n      if (this.isSingleSelection()) {\n        this.updateModel(date);\n      } else if (this.isMultipleSelection()) {\n        this.updateModel(this.value ? [...this.value, date] : [date]);\n      } else if (this.isRangeSelection()) {\n        if (this.value && this.value.length) {\n          let startDate = this.value[0];\n          let endDate = this.value[1];\n          if (!endDate && date.getTime() >= startDate.getTime()) {\n            endDate = date;\n          } else {\n            startDate = date;\n            endDate = null;\n          }\n          this.updateModel([startDate, endDate]);\n        } else {\n          this.updateModel([date, null]);\n        }\n      }\n      this.onSelect.emit(date);\n    }\n    updateModel(value) {\n      this.value = value;\n      if (this.dataType == 'date') {\n        this.onModelChange(this.value);\n      } else if (this.dataType == 'string') {\n        if (this.isSingleSelection()) {\n          this.onModelChange(this.formatDateTime(this.value));\n        } else {\n          let stringArrValue = null;\n          if (Array.isArray(this.value)) {\n            stringArrValue = this.value.map(date => this.formatDateTime(date));\n          }\n          this.onModelChange(stringArrValue);\n        }\n      }\n    }\n    getFirstDayOfMonthIndex(month, year) {\n      let day = new Date();\n      day.setDate(1);\n      day.setMonth(month);\n      day.setFullYear(year);\n      let dayIndex = day.getDay() + this.getSundayIndex();\n      return dayIndex >= 7 ? dayIndex - 7 : dayIndex;\n    }\n    getDaysCountInMonth(month, year) {\n      return 32 - this.daylightSavingAdjust(new Date(year, month, 32)).getDate();\n    }\n    getDaysCountInPrevMonth(month, year) {\n      let prev = this.getPreviousMonthAndYear(month, year);\n      return this.getDaysCountInMonth(prev.month, prev.year);\n    }\n    getPreviousMonthAndYear(month, year) {\n      let m, y;\n      if (month === 0) {\n        m = 11;\n        y = year - 1;\n      } else {\n        m = month - 1;\n        y = year;\n      }\n      return {\n        month: m,\n        year: y\n      };\n    }\n    getNextMonthAndYear(month, year) {\n      let m, y;\n      if (month === 11) {\n        m = 0;\n        y = year + 1;\n      } else {\n        m = month + 1;\n        y = year;\n      }\n      return {\n        month: m,\n        year: y\n      };\n    }\n    getSundayIndex() {\n      let firstDayOfWeek = this.getFirstDateOfWeek();\n      return firstDayOfWeek > 0 ? 7 - firstDayOfWeek : 0;\n    }\n    isSelected(dateMeta) {\n      if (this.value) {\n        if (this.isSingleSelection()) {\n          return this.isDateEquals(this.value, dateMeta);\n        } else if (this.isMultipleSelection()) {\n          let selected = false;\n          for (let date of this.value) {\n            selected = this.isDateEquals(date, dateMeta);\n            if (selected) {\n              break;\n            }\n          }\n          return selected;\n        } else if (this.isRangeSelection()) {\n          if (this.value[1]) return this.isDateEquals(this.value[0], dateMeta) || this.isDateEquals(this.value[1], dateMeta) || this.isDateBetween(this.value[0], this.value[1], dateMeta);else return this.isDateEquals(this.value[0], dateMeta);\n        }\n      } else {\n        return false;\n      }\n    }\n    isComparable() {\n      return this.value != null && typeof this.value !== 'string';\n    }\n    isMonthSelected(month) {\n      if (this.isComparable() && !this.isMultipleSelection()) {\n        const [start, end] = this.isRangeSelection() ? this.value : [this.value, this.value];\n        const selected = new Date(this.currentYear, month, 1);\n        return selected >= start && selected <= (end ?? start);\n      }\n      return false;\n    }\n    isMonthDisabled(month, year) {\n      const yearToCheck = year ?? this.currentYear;\n      for (let day = 1; day < this.getDaysCountInMonth(month, yearToCheck) + 1; day++) {\n        if (this.isSelectable(day, month, yearToCheck, false)) {\n          return false;\n        }\n      }\n      return true;\n    }\n    isYearDisabled(year) {\n      return Array(12).fill(0).every((v, month) => this.isMonthDisabled(month, year));\n    }\n    isYearSelected(year) {\n      if (this.isComparable()) {\n        let value = this.isRangeSelection() ? this.value[0] : this.value;\n        return !this.isMultipleSelection() ? value.getFullYear() === year : false;\n      }\n      return false;\n    }\n    isDateEquals(value, dateMeta) {\n      if (value && ObjectUtils.isDate(value)) return value.getDate() === dateMeta.day && value.getMonth() === dateMeta.month && value.getFullYear() === dateMeta.year;else return false;\n    }\n    isDateBetween(start, end, dateMeta) {\n      let between = false;\n      if (ObjectUtils.isDate(start) && ObjectUtils.isDate(end)) {\n        let date = this.formatDateMetaToDate(dateMeta);\n        return start.getTime() <= date.getTime() && end.getTime() >= date.getTime();\n      }\n      return between;\n    }\n    isSingleSelection() {\n      return this.selectionMode === 'single';\n    }\n    isRangeSelection() {\n      return this.selectionMode === 'range';\n    }\n    isMultipleSelection() {\n      return this.selectionMode === 'multiple';\n    }\n    isToday(today, day, month, year) {\n      return today.getDate() === day && today.getMonth() === month && today.getFullYear() === year;\n    }\n    isSelectable(day, month, year, otherMonth) {\n      let validMin = true;\n      let validMax = true;\n      let validDate = true;\n      let validDay = true;\n      if (otherMonth && !this.selectOtherMonths) {\n        return false;\n      }\n      if (this.minDate) {\n        if (this.minDate.getFullYear() > year) {\n          validMin = false;\n        } else if (this.minDate.getFullYear() === year && this.currentView != 'year') {\n          if (this.minDate.getMonth() > month) {\n            validMin = false;\n          } else if (this.minDate.getMonth() === month) {\n            if (this.minDate.getDate() > day) {\n              validMin = false;\n            }\n          }\n        }\n      }\n      if (this.maxDate) {\n        if (this.maxDate.getFullYear() < year) {\n          validMax = false;\n        } else if (this.maxDate.getFullYear() === year) {\n          if (this.maxDate.getMonth() < month) {\n            validMax = false;\n          } else if (this.maxDate.getMonth() === month) {\n            if (this.maxDate.getDate() < day) {\n              validMax = false;\n            }\n          }\n        }\n      }\n      if (this.disabledDates) {\n        validDate = !this.isDateDisabled(day, month, year);\n      }\n      if (this.disabledDays) {\n        validDay = !this.isDayDisabled(day, month, year);\n      }\n      return validMin && validMax && validDate && validDay;\n    }\n    isDateDisabled(day, month, year) {\n      if (this.disabledDates) {\n        for (let disabledDate of this.disabledDates) {\n          if (disabledDate.getFullYear() === year && disabledDate.getMonth() === month && disabledDate.getDate() === day) {\n            return true;\n          }\n        }\n      }\n      return false;\n    }\n    isDayDisabled(day, month, year) {\n      if (this.disabledDays) {\n        let weekday = new Date(year, month, day);\n        let weekdayNumber = weekday.getDay();\n        return this.disabledDays.indexOf(weekdayNumber) !== -1;\n      }\n      return false;\n    }\n    onInputFocus(event) {\n      this.focus = true;\n      if (this.showOnFocus) {\n        this.showOverlay();\n      }\n      this.onFocus.emit(event);\n    }\n    onInputClick() {\n      if (this.showOnFocus && !this.overlayVisible) {\n        this.showOverlay();\n      }\n    }\n    onInputBlur(event) {\n      this.focus = false;\n      this.onBlur.emit(event);\n      if (!this.keepInvalid) {\n        this.updateInputfield();\n      }\n      this.onModelTouched();\n    }\n    onButtonClick(event, inputfield = this.inputfieldViewChild?.nativeElement) {\n      if (!this.overlayVisible) {\n        inputfield.focus();\n        this.showOverlay();\n      } else {\n        this.hideOverlay();\n      }\n    }\n    clear() {\n      this.inputFieldValue = null;\n      this.value = null;\n      this.onModelChange(this.value);\n      this.onClear.emit();\n    }\n    onOverlayClick(event) {\n      this.overlayService.add({\n        originalEvent: event,\n        target: this.el.nativeElement\n      });\n    }\n    getMonthName(index) {\n      return this.config.getTranslation('monthNames')[index];\n    }\n    getYear(month) {\n      return this.currentView === 'month' ? this.currentYear : month.year;\n    }\n    switchViewButtonDisabled() {\n      return this.numberOfMonths > 1 || this.disabled;\n    }\n    onPrevButtonClick(event) {\n      this.navigationState = {\n        backward: true,\n        button: true\n      };\n      this.navBackward(event);\n    }\n    onNextButtonClick(event) {\n      this.navigationState = {\n        backward: false,\n        button: true\n      };\n      this.navForward(event);\n    }\n    onContainerButtonKeydown(event) {\n      switch (event.which) {\n        //tab\n        case 9:\n          if (!this.inline) {\n            this.trapFocus(event);\n          }\n          if (this.inline) {\n            const headerElements = DomHandler.findSingle(this.containerViewChild?.nativeElement, '.p-datepicker-header');\n            const element = event.target;\n            if (this.timeOnly) {\n              return;\n            } else {\n              if (element == headerElements.children[headerElements?.children?.length - 1]) {\n                this.initFocusableCell();\n              }\n            }\n          }\n          break;\n        //escape\n        case 27:\n          this.inputfieldViewChild?.nativeElement.focus();\n          this.overlayVisible = false;\n          event.preventDefault();\n          break;\n        default:\n          //Noop\n          break;\n      }\n    }\n    onInputKeydown(event) {\n      this.isKeydown = true;\n      if (event.keyCode === 40 && this.contentViewChild) {\n        this.trapFocus(event);\n      } else if (event.keyCode === 27) {\n        if (this.overlayVisible) {\n          this.inputfieldViewChild?.nativeElement.focus();\n          this.overlayVisible = false;\n          event.preventDefault();\n        }\n      } else if (event.keyCode === 13) {\n        if (this.overlayVisible) {\n          this.overlayVisible = false;\n          event.preventDefault();\n        }\n      } else if (event.keyCode === 9 && this.contentViewChild) {\n        DomHandler.getFocusableElements(this.contentViewChild.nativeElement).forEach(el => el.tabIndex = '-1');\n        if (this.overlayVisible) {\n          this.overlayVisible = false;\n        }\n      }\n    }\n    onDateCellKeydown(event, dateMeta, groupIndex) {\n      const cellContent = event.currentTarget;\n      const cell = cellContent.parentElement;\n      const currentDate = this.formatDateMetaToDate(dateMeta);\n      switch (event.which) {\n        //down arrow\n        case 40:\n          {\n            cellContent.tabIndex = '-1';\n            let cellIndex = DomHandler.index(cell);\n            let nextRow = cell.parentElement.nextElementSibling;\n            if (nextRow) {\n              let focusCell = nextRow.children[cellIndex].children[0];\n              if (DomHandler.hasClass(focusCell, 'p-disabled')) {\n                this.navigationState = {\n                  backward: false\n                };\n                this.navForward(event);\n              } else {\n                nextRow.children[cellIndex].children[0].tabIndex = '0';\n                nextRow.children[cellIndex].children[0].focus();\n              }\n            } else {\n              this.navigationState = {\n                backward: false\n              };\n              this.navForward(event);\n            }\n            event.preventDefault();\n            break;\n          }\n        //up arrow\n        case 38:\n          {\n            cellContent.tabIndex = '-1';\n            let cellIndex = DomHandler.index(cell);\n            let prevRow = cell.parentElement.previousElementSibling;\n            if (prevRow) {\n              let focusCell = prevRow.children[cellIndex].children[0];\n              if (DomHandler.hasClass(focusCell, 'p-disabled')) {\n                this.navigationState = {\n                  backward: true\n                };\n                this.navBackward(event);\n              } else {\n                focusCell.tabIndex = '0';\n                focusCell.focus();\n              }\n            } else {\n              this.navigationState = {\n                backward: true\n              };\n              this.navBackward(event);\n            }\n            event.preventDefault();\n            break;\n          }\n        //left arrow\n        case 37:\n          {\n            cellContent.tabIndex = '-1';\n            let prevCell = cell.previousElementSibling;\n            if (prevCell) {\n              let focusCell = prevCell.children[0];\n              if (DomHandler.hasClass(focusCell, 'p-disabled') || DomHandler.hasClass(focusCell.parentElement, 'p-datepicker-weeknumber')) {\n                this.navigateToMonth(true, groupIndex);\n              } else {\n                focusCell.tabIndex = '0';\n                focusCell.focus();\n              }\n            } else {\n              this.navigateToMonth(true, groupIndex);\n            }\n            event.preventDefault();\n            break;\n          }\n        //right arrow\n        case 39:\n          {\n            cellContent.tabIndex = '-1';\n            let nextCell = cell.nextElementSibling;\n            if (nextCell) {\n              let focusCell = nextCell.children[0];\n              if (DomHandler.hasClass(focusCell, 'p-disabled')) {\n                this.navigateToMonth(false, groupIndex);\n              } else {\n                focusCell.tabIndex = '0';\n                focusCell.focus();\n              }\n            } else {\n              this.navigateToMonth(false, groupIndex);\n            }\n            event.preventDefault();\n            break;\n          }\n        //enter\n        //space\n        case 13:\n        case 32:\n          {\n            this.onDateSelect(event, dateMeta);\n            event.preventDefault();\n            break;\n          }\n        //escape\n        case 27:\n          {\n            this.inputfieldViewChild?.nativeElement.focus();\n            this.overlayVisible = false;\n            event.preventDefault();\n            break;\n          }\n        //tab\n        case 9:\n          {\n            if (!this.inline) {\n              this.trapFocus(event);\n            }\n            break;\n          }\n        // page up\n        case 33:\n          {\n            cellContent.tabIndex = '-1';\n            const dateToFocus = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, currentDate.getDate());\n            const focusKey = this.formatDateKey(dateToFocus);\n            this.navigateToMonth(true, groupIndex, `span[data-date='${focusKey}']:not(.p-disabled):not(.p-ink)`);\n            event.preventDefault();\n            break;\n          }\n        // page down\n        case 34:\n          {\n            cellContent.tabIndex = '-1';\n            const dateToFocus = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, currentDate.getDate());\n            const focusKey = this.formatDateKey(dateToFocus);\n            this.navigateToMonth(false, groupIndex, `span[data-date='${focusKey}']:not(.p-disabled):not(.p-ink)`);\n            event.preventDefault();\n            break;\n          }\n        //home\n        case 36:\n          cellContent.tabIndex = '-1';\n          const firstDayDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);\n          const firstDayDateKey = this.formatDateKey(firstDayDate);\n          const firstDayCell = DomHandler.findSingle(cellContent.offsetParent, `span[data-date='${firstDayDateKey}']:not(.p-disabled):not(.p-ink)`);\n          if (firstDayCell) {\n            firstDayCell.tabIndex = '0';\n            firstDayCell.focus();\n          }\n          event.preventDefault();\n          break;\n        //end\n        case 35:\n          cellContent.tabIndex = '-1';\n          const lastDayDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);\n          const lastDayDateKey = this.formatDateKey(lastDayDate);\n          const lastDayCell = DomHandler.findSingle(cellContent.offsetParent, `span[data-date='${lastDayDateKey}']:not(.p-disabled):not(.p-ink)`);\n          if (lastDayDate) {\n            lastDayCell.tabIndex = '0';\n            lastDayCell.focus();\n          }\n          event.preventDefault();\n          break;\n        default:\n          //no op\n          break;\n      }\n    }\n    onMonthCellKeydown(event, index) {\n      const cell = event.currentTarget;\n      switch (event.which) {\n        //arrows\n        case 38:\n        case 40:\n          {\n            cell.tabIndex = '-1';\n            var cells = cell.parentElement.children;\n            var cellIndex = DomHandler.index(cell);\n            let nextCell = cells[event.which === 40 ? cellIndex + 3 : cellIndex - 3];\n            if (nextCell) {\n              nextCell.tabIndex = '0';\n              nextCell.focus();\n            }\n            event.preventDefault();\n            break;\n          }\n        //left arrow\n        case 37:\n          {\n            cell.tabIndex = '-1';\n            let prevCell = cell.previousElementSibling;\n            if (prevCell) {\n              prevCell.tabIndex = '0';\n              prevCell.focus();\n            } else {\n              this.navigationState = {\n                backward: true\n              };\n              this.navBackward(event);\n            }\n            event.preventDefault();\n            break;\n          }\n        //right arrow\n        case 39:\n          {\n            cell.tabIndex = '-1';\n            let nextCell = cell.nextElementSibling;\n            if (nextCell) {\n              nextCell.tabIndex = '0';\n              nextCell.focus();\n            } else {\n              this.navigationState = {\n                backward: false\n              };\n              this.navForward(event);\n            }\n            event.preventDefault();\n            break;\n          }\n        //enter\n        //space\n        case 13:\n        case 32:\n          {\n            this.onMonthSelect(event, index);\n            event.preventDefault();\n            break;\n          }\n        //escape\n        case 27:\n          {\n            this.inputfieldViewChild?.nativeElement.focus();\n            this.overlayVisible = false;\n            event.preventDefault();\n            break;\n          }\n        //tab\n        case 9:\n          {\n            if (!this.inline) {\n              this.trapFocus(event);\n            }\n            break;\n          }\n        default:\n          //no op\n          break;\n      }\n    }\n    onYearCellKeydown(event, index) {\n      const cell = event.currentTarget;\n      switch (event.which) {\n        //arrows\n        case 38:\n        case 40:\n          {\n            cell.tabIndex = '-1';\n            var cells = cell.parentElement.children;\n            var cellIndex = DomHandler.index(cell);\n            let nextCell = cells[event.which === 40 ? cellIndex + 2 : cellIndex - 2];\n            if (nextCell) {\n              nextCell.tabIndex = '0';\n              nextCell.focus();\n            }\n            event.preventDefault();\n            break;\n          }\n        //left arrow\n        case 37:\n          {\n            cell.tabIndex = '-1';\n            let prevCell = cell.previousElementSibling;\n            if (prevCell) {\n              prevCell.tabIndex = '0';\n              prevCell.focus();\n            } else {\n              this.navigationState = {\n                backward: true\n              };\n              this.navBackward(event);\n            }\n            event.preventDefault();\n            break;\n          }\n        //right arrow\n        case 39:\n          {\n            cell.tabIndex = '-1';\n            let nextCell = cell.nextElementSibling;\n            if (nextCell) {\n              nextCell.tabIndex = '0';\n              nextCell.focus();\n            } else {\n              this.navigationState = {\n                backward: false\n              };\n              this.navForward(event);\n            }\n            event.preventDefault();\n            break;\n          }\n        //enter\n        //space\n        case 13:\n        case 32:\n          {\n            this.onYearSelect(event, index);\n            event.preventDefault();\n            break;\n          }\n        //escape\n        case 27:\n          {\n            this.inputfieldViewChild?.nativeElement.focus();\n            this.overlayVisible = false;\n            event.preventDefault();\n            break;\n          }\n        //tab\n        case 9:\n          {\n            this.trapFocus(event);\n            break;\n          }\n        default:\n          //no op\n          break;\n      }\n    }\n    navigateToMonth(prev, groupIndex, focusKey) {\n      if (prev) {\n        if (this.numberOfMonths === 1 || groupIndex === 0) {\n          this.navigationState = {\n            backward: true\n          };\n          this._focusKey = focusKey;\n          this.navBackward(event);\n        } else {\n          let prevMonthContainer = this.contentViewChild.nativeElement.children[groupIndex - 1];\n          if (focusKey) {\n            const firstDayCell = DomHandler.findSingle(prevMonthContainer, focusKey);\n            firstDayCell.tabIndex = '0';\n            firstDayCell.focus();\n          } else {\n            let cells = DomHandler.find(prevMonthContainer, '.p-datepicker-calendar td span:not(.p-disabled):not(.p-ink)');\n            let focusCell = cells[cells.length - 1];\n            focusCell.tabIndex = '0';\n            focusCell.focus();\n          }\n        }\n      } else {\n        if (this.numberOfMonths === 1 || groupIndex === this.numberOfMonths - 1) {\n          this.navigationState = {\n            backward: false\n          };\n          this._focusKey = focusKey;\n          this.navForward(event);\n        } else {\n          let nextMonthContainer = this.contentViewChild.nativeElement.children[groupIndex + 1];\n          if (focusKey) {\n            const firstDayCell = DomHandler.findSingle(nextMonthContainer, focusKey);\n            firstDayCell.tabIndex = '0';\n            firstDayCell.focus();\n          } else {\n            let focusCell = DomHandler.findSingle(nextMonthContainer, '.p-datepicker-calendar td span:not(.p-disabled):not(.p-ink)');\n            focusCell.tabIndex = '0';\n            focusCell.focus();\n          }\n        }\n      }\n    }\n    updateFocus() {\n      let cell;\n      if (this.navigationState) {\n        if (this.navigationState.button) {\n          this.initFocusableCell();\n          if (this.navigationState.backward) DomHandler.findSingle(this.contentViewChild.nativeElement, '.p-datepicker-prev').focus();else DomHandler.findSingle(this.contentViewChild.nativeElement, '.p-datepicker-next').focus();\n        } else {\n          if (this.navigationState.backward) {\n            let cells;\n            if (this.currentView === 'month') {\n              cells = DomHandler.find(this.contentViewChild.nativeElement, '.p-monthpicker .p-monthpicker-month:not(.p-disabled)');\n            } else if (this.currentView === 'year') {\n              cells = DomHandler.find(this.contentViewChild.nativeElement, '.p-yearpicker .p-yearpicker-year:not(.p-disabled)');\n            } else {\n              cells = DomHandler.find(this.contentViewChild.nativeElement, this._focusKey || '.p-datepicker-calendar td span:not(.p-disabled):not(.p-ink)');\n            }\n            if (cells && cells.length > 0) {\n              cell = cells[cells.length - 1];\n            }\n          } else {\n            if (this.currentView === 'month') {\n              cell = DomHandler.findSingle(this.contentViewChild.nativeElement, '.p-monthpicker .p-monthpicker-month:not(.p-disabled)');\n            } else if (this.currentView === 'year') {\n              cell = DomHandler.findSingle(this.contentViewChild.nativeElement, '.p-yearpicker .p-yearpicker-year:not(.p-disabled)');\n            } else {\n              cell = DomHandler.findSingle(this.contentViewChild.nativeElement, this._focusKey || '.p-datepicker-calendar td span:not(.p-disabled):not(.p-ink)');\n            }\n          }\n          if (cell) {\n            cell.tabIndex = '0';\n            cell.focus();\n          }\n        }\n        this.navigationState = null;\n        this._focusKey = null;\n      } else {\n        this.initFocusableCell();\n      }\n      this.alignOverlay();\n    }\n    initFocusableCell() {\n      const contentEl = this.contentViewChild?.nativeElement;\n      let cell;\n      if (this.currentView === 'month') {\n        let cells = DomHandler.find(contentEl, '.p-monthpicker .p-monthpicker-month:not(.p-disabled)');\n        let selectedCell = DomHandler.findSingle(contentEl, '.p-monthpicker .p-monthpicker-month.p-highlight');\n        cells.forEach(cell => cell.tabIndex = -1);\n        cell = selectedCell || cells[0];\n        if (cells.length === 0) {\n          let disabledCells = DomHandler.find(contentEl, '.p-monthpicker .p-monthpicker-month.p-disabled[tabindex = \"0\"]');\n          disabledCells.forEach(cell => cell.tabIndex = -1);\n        }\n      } else if (this.currentView === 'year') {\n        let cells = DomHandler.find(contentEl, '.p-yearpicker .p-yearpicker-year:not(.p-disabled)');\n        let selectedCell = DomHandler.findSingle(contentEl, '.p-yearpicker .p-yearpicker-year.p-highlight');\n        cells.forEach(cell => cell.tabIndex = -1);\n        cell = selectedCell || cells[0];\n        if (cells.length === 0) {\n          let disabledCells = DomHandler.find(contentEl, '.p-yearpicker .p-yearpicker-year.p-disabled[tabindex = \"0\"]');\n          disabledCells.forEach(cell => cell.tabIndex = -1);\n        }\n      } else {\n        cell = DomHandler.findSingle(contentEl, 'span.p-highlight');\n        if (!cell) {\n          let todayCell = DomHandler.findSingle(contentEl, 'td.p-datepicker-today span:not(.p-disabled):not(.p-ink)');\n          if (todayCell) cell = todayCell;else cell = DomHandler.findSingle(contentEl, '.p-datepicker-calendar td span:not(.p-disabled):not(.p-ink)');\n        }\n      }\n      if (cell) {\n        cell.tabIndex = '0';\n        if (!this.preventFocus && (!this.navigationState || !this.navigationState.button)) {\n          setTimeout(() => {\n            if (!this.disabled) {\n              cell.focus();\n            }\n          }, 1);\n        }\n        this.preventFocus = false;\n      }\n    }\n    trapFocus(event) {\n      let focusableElements = DomHandler.getFocusableElements(this.contentViewChild.nativeElement);\n      if (focusableElements && focusableElements.length > 0) {\n        if (!focusableElements[0].ownerDocument.activeElement) {\n          focusableElements[0].focus();\n        } else {\n          let focusedIndex = focusableElements.indexOf(focusableElements[0].ownerDocument.activeElement);\n          if (event.shiftKey) {\n            if (focusedIndex == -1 || focusedIndex === 0) {\n              if (this.focusTrap) {\n                focusableElements[focusableElements.length - 1].focus();\n              } else {\n                if (focusedIndex === -1) return this.hideOverlay();else if (focusedIndex === 0) return;\n              }\n            } else {\n              focusableElements[focusedIndex - 1].focus();\n            }\n          } else {\n            if (focusedIndex == -1) {\n              if (this.timeOnly) {\n                focusableElements[0].focus();\n              } else {\n                let spanIndex = 0;\n                for (let i = 0; i < focusableElements.length; i++) {\n                  if (focusableElements[i].tagName === 'SPAN') spanIndex = i;\n                }\n                focusableElements[spanIndex].focus();\n              }\n            } else if (focusedIndex === focusableElements.length - 1) {\n              if (!this.focusTrap && focusedIndex != -1) return this.hideOverlay();\n              focusableElements[0].focus();\n            } else {\n              focusableElements[focusedIndex + 1].focus();\n            }\n          }\n        }\n      }\n      event.preventDefault();\n    }\n    onMonthDropdownChange(m) {\n      this.currentMonth = parseInt(m);\n      this.onMonthChange.emit({\n        month: this.currentMonth + 1,\n        year: this.currentYear\n      });\n      this.createMonths(this.currentMonth, this.currentYear);\n    }\n    onYearDropdownChange(y) {\n      this.currentYear = parseInt(y);\n      this.onYearChange.emit({\n        month: this.currentMonth + 1,\n        year: this.currentYear\n      });\n      this.createMonths(this.currentMonth, this.currentYear);\n    }\n    convertTo24Hour(hours, pm) {\n      //@ts-ignore\n      if (this.hourFormat == '12') {\n        if (hours === 12) {\n          return pm ? 12 : 0;\n        } else {\n          return pm ? hours + 12 : hours;\n        }\n      }\n      return hours;\n    }\n    constrainTime(hour, minute, second, pm) {\n      let returnTimeTriple = [hour, minute, second];\n      let minHoursExceeds12;\n      let value = this.value;\n      const convertedHour = this.convertTo24Hour(hour, pm);\n      const isRange = this.isRangeSelection(),\n        isMultiple = this.isMultipleSelection(),\n        isMultiValue = isRange || isMultiple;\n      if (isMultiValue) {\n        if (!this.value) {\n          this.value = [new Date(), new Date()];\n        }\n        if (isRange) {\n          value = this.value[1] || this.value[0];\n        }\n        if (isMultiple) {\n          value = this.value[this.value.length - 1];\n        }\n      }\n      const valueDateString = value ? value.toDateString() : null;\n      let isMinDate = this.minDate && valueDateString && this.minDate.toDateString() === valueDateString;\n      let isMaxDate = this.maxDate && valueDateString && this.maxDate.toDateString() === valueDateString;\n      if (isMinDate) {\n        minHoursExceeds12 = this.minDate.getHours() >= 12;\n      }\n      switch (true // intentional fall through\n      ) {\n        case isMinDate && minHoursExceeds12 && this.minDate.getHours() === 12 && this.minDate.getHours() > convertedHour:\n          returnTimeTriple[0] = 11;\n        case isMinDate && this.minDate.getHours() === convertedHour && this.minDate.getMinutes() > minute:\n          returnTimeTriple[1] = this.minDate.getMinutes();\n        case isMinDate && this.minDate.getHours() === convertedHour && this.minDate.getMinutes() === minute && this.minDate.getSeconds() > second:\n          returnTimeTriple[2] = this.minDate.getSeconds();\n          break;\n        case isMinDate && !minHoursExceeds12 && this.minDate.getHours() - 1 === convertedHour && this.minDate.getHours() > convertedHour:\n          returnTimeTriple[0] = 11;\n          this.pm = true;\n        case isMinDate && this.minDate.getHours() === convertedHour && this.minDate.getMinutes() > minute:\n          returnTimeTriple[1] = this.minDate.getMinutes();\n        case isMinDate && this.minDate.getHours() === convertedHour && this.minDate.getMinutes() === minute && this.minDate.getSeconds() > second:\n          returnTimeTriple[2] = this.minDate.getSeconds();\n          break;\n        case isMinDate && minHoursExceeds12 && this.minDate.getHours() > convertedHour && convertedHour !== 12:\n          this.setCurrentHourPM(this.minDate.getHours());\n          returnTimeTriple[0] = this.currentHour;\n        case isMinDate && this.minDate.getHours() === convertedHour && this.minDate.getMinutes() > minute:\n          returnTimeTriple[1] = this.minDate.getMinutes();\n        case isMinDate && this.minDate.getHours() === convertedHour && this.minDate.getMinutes() === minute && this.minDate.getSeconds() > second:\n          returnTimeTriple[2] = this.minDate.getSeconds();\n          break;\n        case isMinDate && this.minDate.getHours() > convertedHour:\n          returnTimeTriple[0] = this.minDate.getHours();\n        case isMinDate && this.minDate.getHours() === convertedHour && this.minDate.getMinutes() > minute:\n          returnTimeTriple[1] = this.minDate.getMinutes();\n        case isMinDate && this.minDate.getHours() === convertedHour && this.minDate.getMinutes() === minute && this.minDate.getSeconds() > second:\n          returnTimeTriple[2] = this.minDate.getSeconds();\n          break;\n        case isMaxDate && this.maxDate.getHours() < convertedHour:\n          returnTimeTriple[0] = this.maxDate.getHours();\n        case isMaxDate && this.maxDate.getHours() === convertedHour && this.maxDate.getMinutes() < minute:\n          returnTimeTriple[1] = this.maxDate.getMinutes();\n        case isMaxDate && this.maxDate.getHours() === convertedHour && this.maxDate.getMinutes() === minute && this.maxDate.getSeconds() < second:\n          returnTimeTriple[2] = this.maxDate.getSeconds();\n          break;\n      }\n      return returnTimeTriple;\n    }\n    incrementHour(event) {\n      const prevHour = this.currentHour ?? 0;\n      let newHour = (this.currentHour ?? 0) + this.stepHour;\n      let newPM = this.pm;\n      if (this.hourFormat == '24') newHour = newHour >= 24 ? newHour - 24 : newHour;else if (this.hourFormat == '12') {\n        // Before the AM/PM break, now after\n        if (prevHour < 12 && newHour > 11) {\n          newPM = !this.pm;\n        }\n        newHour = newHour >= 13 ? newHour - 12 : newHour;\n      }\n      this.toggleAMPMIfNotMinDate(newPM);\n      [this.currentHour, this.currentMinute, this.currentSecond] = this.constrainTime(newHour, this.currentMinute, this.currentSecond, newPM);\n      event.preventDefault();\n    }\n    toggleAMPMIfNotMinDate(newPM) {\n      let value = this.value;\n      if ((this.selectionMode == 'range' || this.selectionMode == 'multiple') && Array.isArray(value) && value.length > 0) {\n        value = value[value.length - 1];\n      }\n      const valueDateString = value ? value.toDateString() : null;\n      let isMinDate = this.minDate && valueDateString && this.minDate.toDateString() === valueDateString;\n      if (isMinDate && this.minDate.getHours() >= 12) {\n        this.pm = true;\n      } else {\n        this.pm = newPM;\n      }\n    }\n    onTimePickerElementMouseDown(event, type, direction) {\n      if (!this.disabled) {\n        this.repeat(event, null, type, direction);\n        event.preventDefault();\n      }\n    }\n    onTimePickerElementMouseUp(event) {\n      if (!this.disabled) {\n        this.clearTimePickerTimer();\n        this.updateTime();\n      }\n    }\n    onTimePickerElementMouseLeave() {\n      if (!this.disabled && this.timePickerTimer) {\n        this.clearTimePickerTimer();\n        this.updateTime();\n      }\n    }\n    repeat(event, interval, type, direction) {\n      let i = interval || 500;\n      this.clearTimePickerTimer();\n      this.timePickerTimer = setTimeout(() => {\n        this.repeat(event, 100, type, direction);\n        this.cd.markForCheck();\n      }, i);\n      switch (type) {\n        case 0:\n          if (direction === 1) this.incrementHour(event);else this.decrementHour(event);\n          break;\n        case 1:\n          if (direction === 1) this.incrementMinute(event);else this.decrementMinute(event);\n          break;\n        case 2:\n          if (direction === 1) this.incrementSecond(event);else this.decrementSecond(event);\n          break;\n      }\n      this.updateInputfield();\n    }\n    clearTimePickerTimer() {\n      if (this.timePickerTimer) {\n        clearTimeout(this.timePickerTimer);\n        this.timePickerTimer = null;\n      }\n    }\n    decrementHour(event) {\n      let newHour = (this.currentHour ?? 0) - this.stepHour;\n      let newPM = this.pm;\n      if (this.hourFormat == '24') newHour = newHour < 0 ? 24 + newHour : newHour;else if (this.hourFormat == '12') {\n        // If we were at noon/midnight, then switch\n        if (this.currentHour === 12) {\n          newPM = !this.pm;\n        }\n        newHour = newHour <= 0 ? 12 + newHour : newHour;\n      }\n      this.toggleAMPMIfNotMinDate(newPM);\n      [this.currentHour, this.currentMinute, this.currentSecond] = this.constrainTime(newHour, this.currentMinute, this.currentSecond, newPM);\n      event.preventDefault();\n    }\n    incrementMinute(event) {\n      let newMinute = (this.currentMinute ?? 0) + this.stepMinute;\n      newMinute = newMinute > 59 ? newMinute - 60 : newMinute;\n      [this.currentHour, this.currentMinute, this.currentSecond] = this.constrainTime(this.currentHour, newMinute, this.currentSecond, this.pm);\n      event.preventDefault();\n    }\n    decrementMinute(event) {\n      let newMinute = (this.currentMinute ?? 0) - this.stepMinute;\n      newMinute = newMinute < 0 ? 60 + newMinute : newMinute;\n      [this.currentHour, this.currentMinute, this.currentSecond] = this.constrainTime(this.currentHour, newMinute, this.currentSecond, this.pm);\n      event.preventDefault();\n    }\n    incrementSecond(event) {\n      let newSecond = this.currentSecond + this.stepSecond;\n      newSecond = newSecond > 59 ? newSecond - 60 : newSecond;\n      [this.currentHour, this.currentMinute, this.currentSecond] = this.constrainTime(this.currentHour, this.currentMinute, newSecond, this.pm);\n      event.preventDefault();\n    }\n    decrementSecond(event) {\n      let newSecond = this.currentSecond - this.stepSecond;\n      newSecond = newSecond < 0 ? 60 + newSecond : newSecond;\n      [this.currentHour, this.currentMinute, this.currentSecond] = this.constrainTime(this.currentHour, this.currentMinute, newSecond, this.pm);\n      event.preventDefault();\n    }\n    updateTime() {\n      let value = this.value;\n      if (this.isRangeSelection()) {\n        value = this.value[1] || this.value[0];\n      }\n      if (this.isMultipleSelection()) {\n        value = this.value[this.value.length - 1];\n      }\n      value = value ? new Date(value.getTime()) : new Date();\n      if (this.hourFormat == '12') {\n        if (this.currentHour === 12) value.setHours(this.pm ? 12 : 0);else value.setHours(this.pm ? this.currentHour + 12 : this.currentHour);\n      } else {\n        value.setHours(this.currentHour);\n      }\n      value.setMinutes(this.currentMinute);\n      value.setSeconds(this.currentSecond);\n      if (this.isRangeSelection()) {\n        if (this.value[1]) value = [this.value[0], value];else value = [value, null];\n      }\n      if (this.isMultipleSelection()) {\n        value = [...this.value.slice(0, -1), value];\n      }\n      this.updateModel(value);\n      this.onSelect.emit(value);\n      this.updateInputfield();\n    }\n    toggleAMPM(event) {\n      const newPM = !this.pm;\n      this.pm = newPM;\n      [this.currentHour, this.currentMinute, this.currentSecond] = this.constrainTime(this.currentHour, this.currentMinute, this.currentSecond, newPM);\n      this.updateTime();\n      event.preventDefault();\n    }\n    onUserInput(event) {\n      // IE 11 Workaround for input placeholder : https://github.com/primefaces/primeng/issues/2026\n      if (!this.isKeydown) {\n        return;\n      }\n      this.isKeydown = false;\n      let val = event.target.value;\n      try {\n        let value = this.parseValueFromString(val);\n        if (this.isValidSelection(value)) {\n          this.updateModel(value);\n          this.updateUI();\n        } else if (this.keepInvalid) {\n          this.updateModel(value);\n        }\n      } catch (err) {\n        //invalid date\n        let value = this.keepInvalid ? val : null;\n        this.updateModel(value);\n      }\n      this.filled = val != null && val.length;\n      this.onInput.emit(event);\n    }\n    isValidSelection(value) {\n      if (this.isSingleSelection()) {\n        return this.isSelectable(value.getDate(), value.getMonth(), value.getFullYear(), false);\n      }\n      let isValid = value.every(v => this.isSelectable(v.getDate(), v.getMonth(), v.getFullYear(), false));\n      if (isValid && this.isRangeSelection()) {\n        isValid = value.length === 1 || value.length > 1 && value[1] >= value[0];\n      }\n      return isValid;\n    }\n    parseValueFromString(text) {\n      if (!text || text.trim().length === 0) {\n        return null;\n      }\n      let value;\n      if (this.isSingleSelection()) {\n        value = this.parseDateTime(text);\n      } else if (this.isMultipleSelection()) {\n        let tokens = text.split(this.multipleSeparator);\n        value = [];\n        for (let token of tokens) {\n          value.push(this.parseDateTime(token.trim()));\n        }\n      } else if (this.isRangeSelection()) {\n        let tokens = text.split(' ' + this.rangeSeparator + ' ');\n        value = [];\n        for (let i = 0; i < tokens.length; i++) {\n          value[i] = this.parseDateTime(tokens[i].trim());\n        }\n      }\n      return value;\n    }\n    parseDateTime(text) {\n      let date;\n      let parts = text.split(' ');\n      if (this.timeOnly) {\n        date = new Date();\n        this.populateTime(date, parts[0], parts[1]);\n      } else {\n        const dateFormat = this.getDateFormat();\n        if (this.showTime) {\n          let ampm = this.hourFormat == '12' ? parts.pop() : null;\n          let timeString = parts.pop();\n          date = this.parseDate(parts.join(' '), dateFormat);\n          this.populateTime(date, timeString, ampm);\n        } else {\n          date = this.parseDate(text, dateFormat);\n        }\n      }\n      return date;\n    }\n    populateTime(value, timeString, ampm) {\n      if (this.hourFormat == '12' && !ampm) {\n        throw 'Invalid Time';\n      }\n      this.pm = ampm === 'PM' || ampm === 'pm';\n      let time = this.parseTime(timeString);\n      value.setHours(time.hour);\n      value.setMinutes(time.minute);\n      value.setSeconds(time.second);\n    }\n    isValidDate(date) {\n      return ObjectUtils.isDate(date) && ObjectUtils.isNotEmpty(date);\n    }\n    updateUI() {\n      let propValue = this.value;\n      if (Array.isArray(propValue)) {\n        propValue = propValue[1] || propValue[0];\n      }\n      let val = this.defaultDate && this.isValidDate(this.defaultDate) && !this.value ? this.defaultDate : propValue && this.isValidDate(propValue) ? propValue : new Date();\n      this.currentMonth = val.getMonth();\n      this.currentYear = val.getFullYear();\n      this.createMonths(this.currentMonth, this.currentYear);\n      if (this.showTime || this.timeOnly) {\n        this.setCurrentHourPM(val.getHours());\n        this.currentMinute = val.getMinutes();\n        this.currentSecond = val.getSeconds();\n      }\n    }\n    showOverlay() {\n      if (!this.overlayVisible) {\n        this.updateUI();\n        if (!this.touchUI) {\n          this.preventFocus = true;\n        }\n        this.overlayVisible = true;\n      }\n    }\n    hideOverlay() {\n      this.inputfieldViewChild?.nativeElement.focus();\n      this.overlayVisible = false;\n      this.clearTimePickerTimer();\n      if (this.touchUI) {\n        this.disableModality();\n      }\n      this.cd.markForCheck();\n    }\n    toggle() {\n      if (!this.inline) {\n        if (!this.overlayVisible) {\n          this.showOverlay();\n          this.inputfieldViewChild?.nativeElement.focus();\n        } else {\n          this.hideOverlay();\n        }\n      }\n    }\n    onOverlayAnimationStart(event) {\n      switch (event.toState) {\n        case 'visible':\n        case 'visibleTouchUI':\n          if (!this.inline) {\n            this.overlay = event.element;\n            this.overlay?.setAttribute(this.attributeSelector, '');\n            this.appendOverlay();\n            this.updateFocus();\n            if (this.autoZIndex) {\n              if (this.touchUI) ZIndexUtils.set('modal', this.overlay, this.baseZIndex || this.config.zIndex.modal);else ZIndexUtils.set('overlay', this.overlay, this.baseZIndex || this.config.zIndex.overlay);\n            }\n            this.alignOverlay();\n            this.onShow.emit(event);\n          }\n          break;\n        case 'void':\n          this.onOverlayHide();\n          this.onClose.emit(event);\n          break;\n      }\n    }\n    onOverlayAnimationDone(event) {\n      switch (event.toState) {\n        case 'visible':\n        case 'visibleTouchUI':\n          if (!this.inline) {\n            this.bindDocumentClickListener();\n            this.bindDocumentResizeListener();\n            this.bindScrollListener();\n          }\n          break;\n        case 'void':\n          if (this.autoZIndex) {\n            ZIndexUtils.clear(event.element);\n          }\n          break;\n      }\n    }\n    appendOverlay() {\n      if (this.appendTo) {\n        if (this.appendTo === 'body') this.document.body.appendChild(this.overlay);else DomHandler.appendChild(this.overlay, this.appendTo);\n      }\n    }\n    restoreOverlayAppend() {\n      if (this.overlay && this.appendTo) {\n        this.el.nativeElement.appendChild(this.overlay);\n      }\n    }\n    alignOverlay() {\n      if (this.touchUI) {\n        this.enableModality(this.overlay);\n      } else if (this.overlay) {\n        if (this.appendTo) {\n          if (this.view === 'date') {\n            this.overlay.style.width = DomHandler.getOuterWidth(this.overlay) + 'px';\n            this.overlay.style.minWidth = DomHandler.getOuterWidth(this.inputfieldViewChild?.nativeElement) + 'px';\n          } else {\n            this.overlay.style.width = DomHandler.getOuterWidth(this.inputfieldViewChild?.nativeElement) + 'px';\n          }\n          DomHandler.absolutePosition(this.overlay, this.inputfieldViewChild?.nativeElement);\n        } else {\n          DomHandler.relativePosition(this.overlay, this.inputfieldViewChild?.nativeElement);\n        }\n      }\n    }\n    enableModality(element) {\n      if (!this.mask && this.touchUI) {\n        this.mask = this.renderer.createElement('div');\n        this.renderer.setStyle(this.mask, 'zIndex', String(parseInt(element.style.zIndex) - 1));\n        let maskStyleClass = 'p-component-overlay p-datepicker-mask p-datepicker-mask-scrollblocker p-component-overlay p-component-overlay-enter';\n        DomHandler.addMultipleClasses(this.mask, maskStyleClass);\n        this.maskClickListener = this.renderer.listen(this.mask, 'click', event => {\n          this.disableModality();\n          this.overlayVisible = false;\n        });\n        this.renderer.appendChild(this.document.body, this.mask);\n        DomHandler.blockBodyScroll();\n      }\n    }\n    disableModality() {\n      if (this.mask) {\n        DomHandler.addClass(this.mask, 'p-component-overlay-leave');\n        if (!this.animationEndListener) {\n          this.animationEndListener = this.renderer.listen(this.mask, 'animationend', this.destroyMask.bind(this));\n        }\n      }\n    }\n    destroyMask() {\n      if (!this.mask) {\n        return;\n      }\n      this.renderer.removeChild(this.document.body, this.mask);\n      let bodyChildren = this.document.body.children;\n      let hasBlockerMasks;\n      for (let i = 0; i < bodyChildren.length; i++) {\n        let bodyChild = bodyChildren[i];\n        if (DomHandler.hasClass(bodyChild, 'p-datepicker-mask-scrollblocker')) {\n          hasBlockerMasks = true;\n          break;\n        }\n      }\n      if (!hasBlockerMasks) {\n        DomHandler.unblockBodyScroll();\n      }\n      this.unbindAnimationEndListener();\n      this.unbindMaskClickListener();\n      this.mask = null;\n    }\n    unbindMaskClickListener() {\n      if (this.maskClickListener) {\n        this.maskClickListener();\n        this.maskClickListener = null;\n      }\n    }\n    unbindAnimationEndListener() {\n      if (this.animationEndListener && this.mask) {\n        this.animationEndListener();\n        this.animationEndListener = null;\n      }\n    }\n    writeValue(value) {\n      this.value = value;\n      if (this.value && typeof this.value === 'string') {\n        try {\n          this.value = this.parseValueFromString(this.value);\n        } catch {\n          if (this.keepInvalid) {\n            this.value = value;\n          }\n        }\n      }\n      this.updateInputfield();\n      this.updateUI();\n      this.cd.markForCheck();\n    }\n    registerOnChange(fn) {\n      this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n      this.onModelTouched = fn;\n    }\n    setDisabledState(val) {\n      this.disabled = val;\n      this.cd.markForCheck();\n    }\n    getDateFormat() {\n      return this.dateFormat || this.getTranslation('dateFormat');\n    }\n    getFirstDateOfWeek() {\n      return this._firstDayOfWeek || this.getTranslation(TranslationKeys.FIRST_DAY_OF_WEEK);\n    }\n    // Ported from jquery-ui datepicker formatDate\n    formatDate(date, format) {\n      if (!date) {\n        return '';\n      }\n      let iFormat;\n      const lookAhead = match => {\n          const matches = iFormat + 1 < format.length && format.charAt(iFormat + 1) === match;\n          if (matches) {\n            iFormat++;\n          }\n          return matches;\n        },\n        formatNumber = (match, value, len) => {\n          let num = '' + value;\n          if (lookAhead(match)) {\n            while (num.length < len) {\n              num = '0' + num;\n            }\n          }\n          return num;\n        },\n        formatName = (match, value, shortNames, longNames) => {\n          return lookAhead(match) ? longNames[value] : shortNames[value];\n        };\n      let output = '';\n      let literal = false;\n      if (date) {\n        for (iFormat = 0; iFormat < format.length; iFormat++) {\n          if (literal) {\n            if (format.charAt(iFormat) === \"'\" && !lookAhead(\"'\")) {\n              literal = false;\n            } else {\n              output += format.charAt(iFormat);\n            }\n          } else {\n            switch (format.charAt(iFormat)) {\n              case 'd':\n                output += formatNumber('d', date.getDate(), 2);\n                break;\n              case 'D':\n                output += formatName('D', date.getDay(), this.getTranslation(TranslationKeys.DAY_NAMES_SHORT), this.getTranslation(TranslationKeys.DAY_NAMES));\n                break;\n              case 'o':\n                output += formatNumber('o', Math.round((new Date(date.getFullYear(), date.getMonth(), date.getDate()).getTime() - new Date(date.getFullYear(), 0, 0).getTime()) / 86400000), 3);\n                break;\n              case 'm':\n                output += formatNumber('m', date.getMonth() + 1, 2);\n                break;\n              case 'M':\n                output += formatName('M', date.getMonth(), this.getTranslation(TranslationKeys.MONTH_NAMES_SHORT), this.getTranslation(TranslationKeys.MONTH_NAMES));\n                break;\n              case 'y':\n                output += lookAhead('y') ? date.getFullYear() : (date.getFullYear() % 100 < 10 ? '0' : '') + date.getFullYear() % 100;\n                break;\n              case '@':\n                output += date.getTime();\n                break;\n              case '!':\n                output += date.getTime() * 10000 + this.ticksTo1970;\n                break;\n              case \"'\":\n                if (lookAhead(\"'\")) {\n                  output += \"'\";\n                } else {\n                  literal = true;\n                }\n                break;\n              default:\n                output += format.charAt(iFormat);\n            }\n          }\n        }\n      }\n      return output;\n    }\n    formatTime(date) {\n      if (!date) {\n        return '';\n      }\n      let output = '';\n      let hours = date.getHours();\n      let minutes = date.getMinutes();\n      let seconds = date.getSeconds();\n      if (this.hourFormat == '12' && hours > 11 && hours != 12) {\n        hours -= 12;\n      }\n      if (this.hourFormat == '12') {\n        output += hours === 0 ? 12 : hours < 10 ? '0' + hours : hours;\n      } else {\n        output += hours < 10 ? '0' + hours : hours;\n      }\n      output += ':';\n      output += minutes < 10 ? '0' + minutes : minutes;\n      if (this.showSeconds) {\n        output += ':';\n        output += seconds < 10 ? '0' + seconds : seconds;\n      }\n      if (this.hourFormat == '12') {\n        output += date.getHours() > 11 ? ' PM' : ' AM';\n      }\n      return output;\n    }\n    parseTime(value) {\n      let tokens = value.split(':');\n      let validTokenLength = this.showSeconds ? 3 : 2;\n      if (tokens.length !== validTokenLength) {\n        throw 'Invalid time';\n      }\n      let h = parseInt(tokens[0]);\n      let m = parseInt(tokens[1]);\n      let s = this.showSeconds ? parseInt(tokens[2]) : null;\n      if (isNaN(h) || isNaN(m) || h > 23 || m > 59 || this.hourFormat == '12' && h > 12 || this.showSeconds && (isNaN(s) || s > 59)) {\n        throw 'Invalid time';\n      } else {\n        if (this.hourFormat == '12') {\n          if (h !== 12 && this.pm) {\n            h += 12;\n          } else if (!this.pm && h === 12) {\n            h -= 12;\n          }\n        }\n        return {\n          hour: h,\n          minute: m,\n          second: s\n        };\n      }\n    }\n    // Ported from jquery-ui datepicker parseDate\n    parseDate(value, format) {\n      if (format == null || value == null) {\n        throw 'Invalid arguments';\n      }\n      value = typeof value === 'object' ? value.toString() : value + '';\n      if (value === '') {\n        return null;\n      }\n      let iFormat,\n        dim,\n        extra,\n        iValue = 0,\n        shortYearCutoff = typeof this.shortYearCutoff !== 'string' ? this.shortYearCutoff : new Date().getFullYear() % 100 + parseInt(this.shortYearCutoff, 10),\n        year = -1,\n        month = -1,\n        day = -1,\n        doy = -1,\n        literal = false,\n        date,\n        lookAhead = match => {\n          let matches = iFormat + 1 < format.length && format.charAt(iFormat + 1) === match;\n          if (matches) {\n            iFormat++;\n          }\n          return matches;\n        },\n        getNumber = match => {\n          let isDoubled = lookAhead(match),\n            size = match === '@' ? 14 : match === '!' ? 20 : match === 'y' && isDoubled ? 4 : match === 'o' ? 3 : 2,\n            minSize = match === 'y' ? size : 1,\n            digits = new RegExp('^\\\\d{' + minSize + ',' + size + '}'),\n            num = value.substring(iValue).match(digits);\n          if (!num) {\n            throw 'Missing number at position ' + iValue;\n          }\n          iValue += num[0].length;\n          return parseInt(num[0], 10);\n        },\n        getName = (match, shortNames, longNames) => {\n          let index = -1;\n          let arr = lookAhead(match) ? longNames : shortNames;\n          let names = [];\n          for (let i = 0; i < arr.length; i++) {\n            names.push([i, arr[i]]);\n          }\n          names.sort((a, b) => {\n            return -(a[1].length - b[1].length);\n          });\n          for (let i = 0; i < names.length; i++) {\n            let name = names[i][1];\n            if (value.substr(iValue, name.length).toLowerCase() === name.toLowerCase()) {\n              index = names[i][0];\n              iValue += name.length;\n              break;\n            }\n          }\n          if (index !== -1) {\n            return index + 1;\n          } else {\n            throw 'Unknown name at position ' + iValue;\n          }\n        },\n        checkLiteral = () => {\n          if (value.charAt(iValue) !== format.charAt(iFormat)) {\n            throw 'Unexpected literal at position ' + iValue;\n          }\n          iValue++;\n        };\n      if (this.view === 'month') {\n        day = 1;\n      }\n      for (iFormat = 0; iFormat < format.length; iFormat++) {\n        if (literal) {\n          if (format.charAt(iFormat) === \"'\" && !lookAhead(\"'\")) {\n            literal = false;\n          } else {\n            checkLiteral();\n          }\n        } else {\n          switch (format.charAt(iFormat)) {\n            case 'd':\n              day = getNumber('d');\n              break;\n            case 'D':\n              getName('D', this.getTranslation(TranslationKeys.DAY_NAMES_SHORT), this.getTranslation(TranslationKeys.DAY_NAMES));\n              break;\n            case 'o':\n              doy = getNumber('o');\n              break;\n            case 'm':\n              month = getNumber('m');\n              break;\n            case 'M':\n              month = getName('M', this.getTranslation(TranslationKeys.MONTH_NAMES_SHORT), this.getTranslation(TranslationKeys.MONTH_NAMES));\n              break;\n            case 'y':\n              year = getNumber('y');\n              break;\n            case '@':\n              date = new Date(getNumber('@'));\n              year = date.getFullYear();\n              month = date.getMonth() + 1;\n              day = date.getDate();\n              break;\n            case '!':\n              date = new Date((getNumber('!') - this.ticksTo1970) / 10000);\n              year = date.getFullYear();\n              month = date.getMonth() + 1;\n              day = date.getDate();\n              break;\n            case \"'\":\n              if (lookAhead(\"'\")) {\n                checkLiteral();\n              } else {\n                literal = true;\n              }\n              break;\n            default:\n              checkLiteral();\n          }\n        }\n      }\n      if (iValue < value.length) {\n        extra = value.substr(iValue);\n        if (!/^\\s+/.test(extra)) {\n          throw 'Extra/unparsed characters found in date: ' + extra;\n        }\n      }\n      if (year === -1) {\n        year = new Date().getFullYear();\n      } else if (year < 100) {\n        year += new Date().getFullYear() - new Date().getFullYear() % 100 + (year <= shortYearCutoff ? 0 : -100);\n      }\n      if (doy > -1) {\n        month = 1;\n        day = doy;\n        do {\n          dim = this.getDaysCountInMonth(year, month - 1);\n          if (day <= dim) {\n            break;\n          }\n          month++;\n          day -= dim;\n        } while (true);\n      }\n      if (this.view === 'year') {\n        month = month === -1 ? 1 : month;\n        day = day === -1 ? 1 : day;\n      }\n      date = this.daylightSavingAdjust(new Date(year, month - 1, day));\n      if (date.getFullYear() !== year || date.getMonth() + 1 !== month || date.getDate() !== day) {\n        throw 'Invalid date'; // E.g. 31/02/00\n      }\n      return date;\n    }\n    daylightSavingAdjust(date) {\n      if (!date) {\n        return null;\n      }\n      date.setHours(date.getHours() > 12 ? date.getHours() + 2 : 0);\n      return date;\n    }\n    updateFilledState() {\n      this.filled = this.inputFieldValue && this.inputFieldValue != '';\n    }\n    isValidDateForTimeConstraints(selectedDate) {\n      if (this.keepInvalid) {\n        return true; // If we are keeping invalid dates, we don't need to check for time constraints\n      }\n      return (!this.minDate || selectedDate >= this.minDate) && (!this.maxDate || selectedDate <= this.maxDate);\n    }\n    onTodayButtonClick(event) {\n      const date = new Date();\n      const dateMeta = {\n        day: date.getDate(),\n        month: date.getMonth(),\n        year: date.getFullYear(),\n        otherMonth: date.getMonth() !== this.currentMonth || date.getFullYear() !== this.currentYear,\n        today: true,\n        selectable: true\n      };\n      this.createMonths(date.getMonth(), date.getFullYear());\n      this.onDateSelect(event, dateMeta);\n      this.onTodayClick.emit(date);\n    }\n    onClearButtonClick(event) {\n      this.updateModel(null);\n      this.updateInputfield();\n      this.hideOverlay();\n      this.onClearClick.emit(event);\n    }\n    createResponsiveStyle() {\n      if (this.numberOfMonths > 1 && this.responsiveOptions) {\n        if (!this.responsiveStyleElement) {\n          this.responsiveStyleElement = this.renderer.createElement('style');\n          this.responsiveStyleElement.type = 'text/css';\n          DomHandler.setAttribute(this.responsiveStyleElement, 'nonce', this.config?.csp()?.nonce);\n          this.renderer.appendChild(this.document.body, this.responsiveStyleElement);\n        }\n        let innerHTML = '';\n        if (this.responsiveOptions) {\n          let responsiveOptions = [...this.responsiveOptions].filter(o => !!(o.breakpoint && o.numMonths)).sort((o1, o2) => -1 * o1.breakpoint.localeCompare(o2.breakpoint, undefined, {\n            numeric: true\n          }));\n          for (let i = 0; i < responsiveOptions.length; i++) {\n            let {\n              breakpoint,\n              numMonths\n            } = responsiveOptions[i];\n            let styles = `\n                        .p-datepicker[${this.attributeSelector}] .p-datepicker-group:nth-child(${numMonths}) .p-datepicker-next {\n                            display: inline-flex !important;\n                        }\n                    `;\n            for (let j = numMonths; j < this.numberOfMonths; j++) {\n              styles += `\n                            .p-datepicker[${this.attributeSelector}] .p-datepicker-group:nth-child(${j + 1}) {\n                                display: none !important;\n                            }\n                        `;\n            }\n            innerHTML += `\n                        @media screen and (max-width: ${breakpoint}) {\n                            ${styles}\n                        }\n                    `;\n          }\n        }\n        this.responsiveStyleElement.innerHTML = innerHTML;\n      }\n    }\n    destroyResponsiveStyleElement() {\n      if (this.responsiveStyleElement) {\n        this.responsiveStyleElement.remove();\n        this.responsiveStyleElement = null;\n      }\n    }\n    bindDocumentClickListener() {\n      if (!this.documentClickListener) {\n        this.zone.runOutsideAngular(() => {\n          const documentTarget = this.el ? this.el.nativeElement.ownerDocument : this.document;\n          this.documentClickListener = this.renderer.listen(documentTarget, 'mousedown', event => {\n            if (this.isOutsideClicked(event) && this.overlayVisible) {\n              this.zone.run(() => {\n                this.hideOverlay();\n                this.onClickOutside.emit(event);\n                this.cd.markForCheck();\n              });\n            }\n          });\n        });\n      }\n    }\n    unbindDocumentClickListener() {\n      if (this.documentClickListener) {\n        this.documentClickListener();\n        this.documentClickListener = null;\n      }\n    }\n    bindDocumentResizeListener() {\n      if (!this.documentResizeListener && !this.touchUI) {\n        this.documentResizeListener = this.renderer.listen(this.window, 'resize', this.onWindowResize.bind(this));\n      }\n    }\n    unbindDocumentResizeListener() {\n      if (this.documentResizeListener) {\n        this.documentResizeListener();\n        this.documentResizeListener = null;\n      }\n    }\n    bindScrollListener() {\n      if (!this.scrollHandler) {\n        this.scrollHandler = new ConnectedOverlayScrollHandler(this.containerViewChild?.nativeElement, () => {\n          if (this.overlayVisible) {\n            this.hideOverlay();\n          }\n        });\n      }\n      this.scrollHandler.bindScrollListener();\n    }\n    unbindScrollListener() {\n      if (this.scrollHandler) {\n        this.scrollHandler.unbindScrollListener();\n      }\n    }\n    isOutsideClicked(event) {\n      return !(this.el.nativeElement.isSameNode(event.target) || this.isNavIconClicked(event) || this.el.nativeElement.contains(event.target) || this.overlay && this.overlay.contains(event.target));\n    }\n    isNavIconClicked(event) {\n      return DomHandler.hasClass(event.target, 'p-datepicker-prev') || DomHandler.hasClass(event.target, 'p-datepicker-prev-icon') || DomHandler.hasClass(event.target, 'p-datepicker-next') || DomHandler.hasClass(event.target, 'p-datepicker-next-icon');\n    }\n    onWindowResize() {\n      if (this.overlayVisible && !DomHandler.isTouchDevice()) {\n        this.hideOverlay();\n      }\n    }\n    onOverlayHide() {\n      this.currentView = this.view;\n      if (this.mask) {\n        this.destroyMask();\n      }\n      this.unbindDocumentClickListener();\n      this.unbindDocumentResizeListener();\n      this.unbindScrollListener();\n      this.overlay = null;\n    }\n    ngOnDestroy() {\n      if (this.scrollHandler) {\n        this.scrollHandler.destroy();\n        this.scrollHandler = null;\n      }\n      if (this.translationSubscription) {\n        this.translationSubscription.unsubscribe();\n      }\n      if (this.overlay && this.autoZIndex) {\n        ZIndexUtils.clear(this.overlay);\n      }\n      this.destroyResponsiveStyleElement();\n      this.clearTimePickerTimer();\n      this.restoreOverlayAppend();\n      this.onOverlayHide();\n    }\n    static ɵfac = function Calendar_Factory(t) {\n      return new (t || Calendar)(i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.PrimeNGConfig), i0.ɵɵdirectiveInject(i1.OverlayService));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: Calendar,\n      selectors: [[\"p-calendar\"]],\n      contentQueries: function Calendar_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      viewQuery: function Calendar_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(_c2, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.containerViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputfieldViewChild = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.content = _t.first);\n        }\n      },\n      hostAttrs: [1, \"p-element\", \"p-inputwrapper\"],\n      hostVars: 6,\n      hostBindings: function Calendar_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"p-inputwrapper-filled\", ctx.filled)(\"p-inputwrapper-focus\", ctx.focus)(\"p-calendar-clearable\", ctx.showClear && !ctx.disabled);\n        }\n      },\n      inputs: {\n        iconDisplay: \"iconDisplay\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        inputStyle: \"inputStyle\",\n        inputId: \"inputId\",\n        name: \"name\",\n        inputStyleClass: \"inputStyleClass\",\n        placeholder: \"placeholder\",\n        ariaLabelledBy: \"ariaLabelledBy\",\n        ariaLabel: \"ariaLabel\",\n        iconAriaLabel: \"iconAriaLabel\",\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n        dateFormat: \"dateFormat\",\n        multipleSeparator: \"multipleSeparator\",\n        rangeSeparator: \"rangeSeparator\",\n        inline: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"inline\", \"inline\", booleanAttribute],\n        showOtherMonths: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"showOtherMonths\", \"showOtherMonths\", booleanAttribute],\n        selectOtherMonths: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"selectOtherMonths\", \"selectOtherMonths\", booleanAttribute],\n        showIcon: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"showIcon\", \"showIcon\", booleanAttribute],\n        icon: \"icon\",\n        appendTo: \"appendTo\",\n        readonlyInput: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"readonlyInput\", \"readonlyInput\", booleanAttribute],\n        shortYearCutoff: \"shortYearCutoff\",\n        monthNavigator: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"monthNavigator\", \"monthNavigator\", booleanAttribute],\n        yearNavigator: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"yearNavigator\", \"yearNavigator\", booleanAttribute],\n        hourFormat: \"hourFormat\",\n        timeOnly: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"timeOnly\", \"timeOnly\", booleanAttribute],\n        stepYearPicker: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"stepYearPicker\", \"stepYearPicker\", numberAttribute],\n        stepHour: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"stepHour\", \"stepHour\", numberAttribute],\n        stepMinute: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"stepMinute\", \"stepMinute\", numberAttribute],\n        stepSecond: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"stepSecond\", \"stepSecond\", numberAttribute],\n        showSeconds: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"showSeconds\", \"showSeconds\", booleanAttribute],\n        required: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"required\", \"required\", booleanAttribute],\n        showOnFocus: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"showOnFocus\", \"showOnFocus\", booleanAttribute],\n        showWeek: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"showWeek\", \"showWeek\", booleanAttribute],\n        startWeekFromFirstDayOfYear: \"startWeekFromFirstDayOfYear\",\n        showClear: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"showClear\", \"showClear\", booleanAttribute],\n        dataType: \"dataType\",\n        selectionMode: \"selectionMode\",\n        maxDateCount: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"maxDateCount\", \"maxDateCount\", numberAttribute],\n        showButtonBar: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"showButtonBar\", \"showButtonBar\", booleanAttribute],\n        todayButtonStyleClass: \"todayButtonStyleClass\",\n        clearButtonStyleClass: \"clearButtonStyleClass\",\n        autofocus: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autofocus\", \"autofocus\", booleanAttribute],\n        autoZIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autoZIndex\", \"autoZIndex\", booleanAttribute],\n        baseZIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"baseZIndex\", \"baseZIndex\", numberAttribute],\n        panelStyleClass: \"panelStyleClass\",\n        panelStyle: \"panelStyle\",\n        keepInvalid: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"keepInvalid\", \"keepInvalid\", booleanAttribute],\n        hideOnDateTimeSelect: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"hideOnDateTimeSelect\", \"hideOnDateTimeSelect\", booleanAttribute],\n        touchUI: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"touchUI\", \"touchUI\", booleanAttribute],\n        timeSeparator: \"timeSeparator\",\n        focusTrap: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"focusTrap\", \"focusTrap\", booleanAttribute],\n        showTransitionOptions: \"showTransitionOptions\",\n        hideTransitionOptions: \"hideTransitionOptions\",\n        tabindex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabindex\", \"tabindex\", numberAttribute],\n        variant: \"variant\",\n        minDate: \"minDate\",\n        maxDate: \"maxDate\",\n        disabledDates: \"disabledDates\",\n        disabledDays: \"disabledDays\",\n        yearRange: \"yearRange\",\n        showTime: \"showTime\",\n        responsiveOptions: \"responsiveOptions\",\n        numberOfMonths: \"numberOfMonths\",\n        firstDayOfWeek: \"firstDayOfWeek\",\n        locale: \"locale\",\n        view: \"view\",\n        defaultDate: \"defaultDate\"\n      },\n      outputs: {\n        onFocus: \"onFocus\",\n        onBlur: \"onBlur\",\n        onClose: \"onClose\",\n        onSelect: \"onSelect\",\n        onClear: \"onClear\",\n        onInput: \"onInput\",\n        onTodayClick: \"onTodayClick\",\n        onClearClick: \"onClearClick\",\n        onMonthChange: \"onMonthChange\",\n        onYearChange: \"onYearChange\",\n        onClickOutside: \"onClickOutside\",\n        onShow: \"onShow\"\n      },\n      features: [i0.ɵɵProvidersFeature([CALENDAR_VALUE_ACCESSOR]), i0.ɵɵInputTransformsFeature],\n      ngContentSelectors: _c4,\n      decls: 4,\n      vars: 12,\n      consts: [[\"container\", \"\"], [\"inputfield\", \"\"], [\"contentWrapper\", \"\"], [3, \"ngClass\", \"ngStyle\"], [3, \"ngIf\"], [3, \"class\", \"ngStyle\", \"ngClass\", \"click\", 4, \"ngIf\"], [\"type\", \"text\", \"role\", \"combobox\", \"aria-autocomplete\", \"none\", \"aria-haspopup\", \"dialog\", \"autocomplete\", \"off\", \"pAutoFocus\", \"\", 3, \"focus\", \"keydown\", \"click\", \"blur\", \"input\", \"value\", \"readonly\", \"ngStyle\", \"placeholder\", \"disabled\", \"ngClass\", \"autofocus\"], [4, \"ngIf\"], [\"type\", \"button\", \"aria-haspopup\", \"dialog\", \"pButton\", \"\", \"pRipple\", \"\", \"class\", \"p-datepicker-trigger p-button-icon-only\", \"tabindex\", \"0\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [3, \"styleClass\", \"click\", 4, \"ngIf\"], [\"class\", \"p-calendar-clear-icon\", 3, \"click\", 4, \"ngIf\"], [3, \"click\", \"styleClass\"], [1, \"p-calendar-clear-icon\", 3, \"click\"], [4, \"ngTemplateOutlet\"], [\"type\", \"button\", \"aria-haspopup\", \"dialog\", \"pButton\", \"\", \"pRipple\", \"\", \"tabindex\", \"0\", 1, \"p-datepicker-trigger\", \"p-button-icon-only\", 3, \"click\", \"disabled\"], [3, \"ngClass\", 4, \"ngIf\"], [3, \"ngClass\"], [3, \"ngClass\", \"click\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"click\", \"ngClass\"], [3, \"click\", \"ngStyle\", \"ngClass\"], [\"class\", \"p-timepicker\", 4, \"ngIf\"], [\"class\", \"p-datepicker-buttonbar\", 4, \"ngIf\"], [1, \"p-datepicker-group-container\"], [\"class\", \"p-datepicker-group\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"p-monthpicker\", 4, \"ngIf\"], [\"class\", \"p-yearpicker\", 4, \"ngIf\"], [1, \"p-datepicker-group\"], [1, \"p-datepicker-header\"], [\"class\", \"p-datepicker-prev p-link\", \"type\", \"button\", \"pRipple\", \"\", 3, \"keydown\", \"click\", 4, \"ngIf\"], [1, \"p-datepicker-title\"], [\"type\", \"button\", \"class\", \"p-datepicker-month p-link\", 3, \"disabled\", \"click\", \"keydown\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"p-datepicker-year p-link\", 3, \"disabled\", \"click\", \"keydown\", 4, \"ngIf\"], [\"class\", \"p-datepicker-decade\", 4, \"ngIf\"], [\"type\", \"button\", \"pRipple\", \"\", 1, \"p-datepicker-next\", \"p-link\", 3, \"keydown\", \"click\"], [3, \"styleClass\", 4, \"ngIf\"], [\"class\", \"p-datepicker-next-icon\", 4, \"ngIf\"], [\"class\", \"p-datepicker-calendar-container\", 4, \"ngIf\"], [\"type\", \"button\", \"pRipple\", \"\", 1, \"p-datepicker-prev\", \"p-link\", 3, \"keydown\", \"click\"], [\"class\", \"p-datepicker-prev-icon\", 4, \"ngIf\"], [3, \"styleClass\"], [1, \"p-datepicker-prev-icon\"], [\"type\", \"button\", 1, \"p-datepicker-month\", \"p-link\", 3, \"click\", \"keydown\", \"disabled\"], [\"type\", \"button\", 1, \"p-datepicker-year\", \"p-link\", 3, \"click\", \"keydown\", \"disabled\"], [1, \"p-datepicker-decade\"], [1, \"p-datepicker-next-icon\"], [1, \"p-datepicker-calendar-container\"], [\"role\", \"grid\", 1, \"p-datepicker-calendar\"], [\"class\", \"p-datepicker-weekheader p-disabled\", 4, \"ngIf\"], [\"scope\", \"col\", 4, \"ngFor\", \"ngForOf\"], [4, \"ngFor\", \"ngForOf\"], [1, \"p-datepicker-weekheader\", \"p-disabled\"], [\"scope\", \"col\"], [\"class\", \"p-datepicker-weeknumber\", 4, \"ngIf\"], [3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [1, \"p-datepicker-weeknumber\"], [1, \"p-disabled\"], [\"draggable\", \"false\", \"pRipple\", \"\", 3, \"click\", \"keydown\", \"ngClass\"], [\"class\", \"p-hidden-accessible\", \"aria-live\", \"polite\", 4, \"ngIf\"], [\"aria-live\", \"polite\", 1, \"p-hidden-accessible\"], [1, \"p-monthpicker\"], [\"class\", \"p-monthpicker-month\", \"pRipple\", \"\", 3, \"ngClass\", \"click\", \"keydown\", 4, \"ngFor\", \"ngForOf\"], [\"pRipple\", \"\", 1, \"p-monthpicker-month\", 3, \"click\", \"keydown\", \"ngClass\"], [1, \"p-yearpicker\"], [\"class\", \"p-yearpicker-year\", \"pRipple\", \"\", 3, \"ngClass\", \"click\", \"keydown\", 4, \"ngFor\", \"ngForOf\"], [\"pRipple\", \"\", 1, \"p-yearpicker-year\", 3, \"click\", \"keydown\", \"ngClass\"], [1, \"p-timepicker\"], [1, \"p-hour-picker\"], [\"type\", \"button\", \"pRipple\", \"\", 1, \"p-link\", 3, \"keydown\", \"keydown.enter\", \"keydown.space\", \"mousedown\", \"mouseup\", \"keyup.enter\", \"keyup.space\", \"mouseleave\"], [1, \"p-separator\"], [1, \"p-minute-picker\"], [\"class\", \"p-separator\", 4, \"ngIf\"], [\"class\", \"p-second-picker\", 4, \"ngIf\"], [\"class\", \"p-ampm-picker\", 4, \"ngIf\"], [1, \"p-second-picker\"], [1, \"p-ampm-picker\"], [\"type\", \"button\", \"pRipple\", \"\", 1, \"p-link\", 3, \"keydown\", \"click\", \"keydown.enter\"], [1, \"p-datepicker-buttonbar\"], [\"type\", \"button\", \"pButton\", \"\", \"pRipple\", \"\", 3, \"keydown\", \"click\", \"label\", \"ngClass\"]],\n      template: function Calendar_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c3);\n          i0.ɵɵelementStart(0, \"span\", 3, 0);\n          i0.ɵɵtemplate(2, Calendar_ng_template_2_Template, 5, 22, \"ng-template\", 4)(3, Calendar_div_3_Template, 9, 32, \"div\", 5);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction5(6, _c5, ctx.showIcon && ctx.iconDisplay === \"input\", ctx.showIcon && ctx.iconDisplay === \"button\", ctx.timeOnly, ctx.disabled, ctx.focus || ctx.overlayVisible))(\"ngStyle\", ctx.style);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.inline);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.inline || ctx.overlayVisible);\n        }\n      },\n      dependencies: () => [i2.NgClass, i2.NgForOf, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.ButtonDirective, i4.Ripple, ChevronLeftIcon, ChevronRightIcon, ChevronUpIcon, ChevronDownIcon, TimesIcon, CalendarIcon, i5.AutoFocus],\n      styles: [\"@layer primeng{.p-calendar{position:relative;display:inline-flex;max-width:100%}.p-calendar .p-inputtext{flex:1 1 auto;width:1%;text-overflow:ellipsis}.p-calendar-w-btn .p-inputtext{border-top-right-radius:0;border-bottom-right-radius:0}.p-calendar-w-btn .p-datepicker-trigger{border-top-left-radius:0;border-bottom-left-radius:0}.p-fluid .p-calendar{display:flex}.p-fluid .p-calendar .p-inputtext{width:1%}.p-calendar .p-datepicker{min-width:100%}.p-datepicker{width:auto;position:absolute;top:0;left:0}.p-datepicker-inline{display:inline-block;position:static;overflow-x:auto}.p-datepicker-header{display:flex;align-items:center;justify-content:space-between}.p-datepicker-header .p-datepicker-title{margin:0 auto}.p-datepicker-prev,.p-datepicker-next{cursor:pointer;display:inline-flex;justify-content:center;align-items:center;overflow:hidden;position:relative}.p-datepicker-multiple-month .p-datepicker-group-container .p-datepicker-group{flex:1 1 auto}.p-datepicker-multiple-month .p-datepicker-group-container{display:flex}.p-datepicker table{width:100%;border-collapse:collapse}.p-datepicker td>span{display:flex;justify-content:center;align-items:center;cursor:pointer;margin:0 auto;overflow:hidden;position:relative}.p-monthpicker-month{width:33.3%;display:inline-flex;align-items:center;justify-content:center;cursor:pointer;overflow:hidden;position:relative}.p-datepicker-buttonbar{display:flex;justify-content:space-between;align-items:center}.p-timepicker{display:flex;justify-content:center;align-items:center}.p-timepicker button{display:flex;align-items:center;justify-content:center;cursor:pointer;overflow:hidden;position:relative}.p-timepicker>div{display:flex;align-items:center;flex-direction:column}.p-datepicker-touch-ui,.p-calendar .p-datepicker-touch-ui{position:fixed;top:50%;left:50%;min-width:80vw;transform:translate(-50%,-50%)}.p-yearpicker-year{width:50%;display:inline-flex;align-items:center;justify-content:center;cursor:pointer;overflow:hidden;position:relative}.p-calendar-clear-icon{position:absolute;top:50%;margin-top:-.5rem;cursor:pointer}.p-datepicker-icon{pointer-events:none}.p-calendar-clearable{position:relative}}\\n\"],\n      encapsulation: 2,\n      data: {\n        animation: [trigger('overlayAnimation', [state('visibleTouchUI', style({\n          transform: 'translate(-50%,-50%)',\n          opacity: 1\n        })), transition('void => visible', [style({\n          opacity: 0,\n          transform: 'scaleY(0.8)'\n        }), animate('{{showTransitionParams}}', style({\n          opacity: 1,\n          transform: '*'\n        }))]), transition('visible => void', [animate('{{hideTransitionParams}}', style({\n          opacity: 0\n        }))]), transition('void => visibleTouchUI', [style({\n          opacity: 0,\n          transform: 'translate3d(-50%, -40%, 0) scale(0.9)'\n        }), animate('{{showTransitionParams}}')]), transition('visibleTouchUI => void', [animate('{{hideTransitionParams}}', style({\n          opacity: 0,\n          transform: 'translate3d(-50%, -40%, 0) scale(0.9)'\n        }))])])]\n      },\n      changeDetection: 0\n    });\n  }\n  return Calendar;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CalendarModule = /*#__PURE__*/(() => {\n  class CalendarModule {\n    static ɵfac = function CalendarModule_Factory(t) {\n      return new (t || CalendarModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CalendarModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, ButtonModule, SharedModule, RippleModule, ChevronLeftIcon, ChevronRightIcon, ChevronUpIcon, ChevronDownIcon, TimesIcon, CalendarIcon, AutoFocusModule, ButtonModule, SharedModule]\n    });\n  }\n  return CalendarModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CALENDAR_VALUE_ACCESSOR, Calendar, CalendarModule };\n//# sourceMappingURL=primeng-calendar.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}