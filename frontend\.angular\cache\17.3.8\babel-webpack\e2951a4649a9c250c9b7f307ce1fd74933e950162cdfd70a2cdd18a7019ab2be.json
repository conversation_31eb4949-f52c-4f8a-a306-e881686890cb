{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport let FilterPipe = /*#__PURE__*/(() => {\n  class FilterPipe {\n    transform(value, args) {\n      if (!value || !args || !args['term']) {\n        return value;\n      }\n      const term = args['term'].toLocaleLowerCase();\n      console.log('Search term:', term);\n      const filteredValues = value.filter(item => {\n        const contains = this.isContain(term, item);\n        if (contains) {\n          console.log('Match found in item:', item);\n        }\n        return contains;\n      });\n      return filteredValues;\n    }\n    isContain(term, item) {\n      for (const key in item) {\n        if (item.hasOwnProperty(key)) {\n          const propertyValue = item[key];\n          if (typeof propertyValue === 'string') {\n            if (propertyValue.toLocaleLowerCase().indexOf(term) !== -1) {\n              return true;\n            }\n          } else if (typeof propertyValue === 'object' && propertyValue !== null) {\n            if (this.isContain(term, propertyValue)) {\n              return true;\n            }\n          }\n        }\n      }\n      return false;\n    }\n    static #_ = this.ɵfac = function FilterPipe_Factory(t) {\n      return new (t || FilterPipe)();\n    };\n    static #_2 = this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"filter\",\n      type: FilterPipe,\n      pure: true\n    });\n  }\n  return FilterPipe;\n})();", "map": {"version": 3, "names": ["FilterPipe", "transform", "value", "args", "term", "toLocaleLowerCase", "console", "log", "filteredValues", "filter", "item", "contains", "isContain", "key", "hasOwnProperty", "propertyValue", "indexOf", "_", "_2", "pure"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\pipes\\filter.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\n\r\n@Pipe({\r\n  name: 'filter',\r\n})\r\nexport class FilterPipe implements PipeTransform {\r\n\r\n\r\n  transform(value: any[], args: any): any[] {\r\n    if (!value || !args || !args['term']) {\r\n      return value;\r\n    }\r\n\r\n    const term: string = args['term'].toLocaleLowerCase();\r\n\r\n    console.log('Search term:', term);\r\n\r\n    const filteredValues = value.filter(item => {\r\n      const contains = this.isContain(term, item);\r\n      if (contains) {\r\n        console.log('Match found in item:', item);\r\n      }\r\n      return contains;\r\n    });\r\n\r\n    return filteredValues;\r\n  }\r\n\r\n  isContain(term: string, item: any): boolean {\r\n    for (const key in item) {\r\n      if (item.hasOwnProperty(key)) {\r\n        const propertyValue = item[key];\r\n        if (typeof propertyValue === 'string') {\r\n          if (propertyValue.toLocaleLowerCase().indexOf(term) !== -1) {\r\n            return true;\r\n          }\r\n        } else if (typeof propertyValue === 'object' && propertyValue !== null) {\r\n          if (this.isContain(term, propertyValue)) {\r\n            return true;\r\n          }\r\n        }\r\n      }\r\n    }\r\n    return false;\r\n  }\r\n}"], "mappings": ";AAKA,WAAaA,UAAU;EAAjB,MAAOA,UAAU;IAGrBC,SAASA,CAACC,KAAY,EAAEC,IAAS;MAC/B,IAAI,CAACD,KAAK,IAAI,CAACC,IAAI,IAAI,CAACA,IAAI,CAAC,MAAM,CAAC,EAAE;QACpC,OAAOD,KAAK;MACd;MAEA,MAAME,IAAI,GAAWD,IAAI,CAAC,MAAM,CAAC,CAACE,iBAAiB,EAAE;MAErDC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEH,IAAI,CAAC;MAEjC,MAAMI,cAAc,GAAGN,KAAK,CAACO,MAAM,CAACC,IAAI,IAAG;QACzC,MAAMC,QAAQ,GAAG,IAAI,CAACC,SAAS,CAACR,IAAI,EAAEM,IAAI,CAAC;QAC3C,IAAIC,QAAQ,EAAE;UACZL,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEG,IAAI,CAAC;QAC3C;QACA,OAAOC,QAAQ;MACjB,CAAC,CAAC;MAEF,OAAOH,cAAc;IACvB;IAEAI,SAASA,CAACR,IAAY,EAAEM,IAAS;MAC/B,KAAK,MAAMG,GAAG,IAAIH,IAAI,EAAE;QACtB,IAAIA,IAAI,CAACI,cAAc,CAACD,GAAG,CAAC,EAAE;UAC5B,MAAME,aAAa,GAAGL,IAAI,CAACG,GAAG,CAAC;UAC/B,IAAI,OAAOE,aAAa,KAAK,QAAQ,EAAE;YACrC,IAAIA,aAAa,CAACV,iBAAiB,EAAE,CAACW,OAAO,CAACZ,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;cAC1D,OAAO,IAAI;YACb;UACF,CAAC,MAAM,IAAI,OAAOW,aAAa,KAAK,QAAQ,IAAIA,aAAa,KAAK,IAAI,EAAE;YACtE,IAAI,IAAI,CAACH,SAAS,CAACR,IAAI,EAAEW,aAAa,CAAC,EAAE;cACvC,OAAO,IAAI;YACb;UACF;QACF;MACF;MACA,OAAO,KAAK;IACd;IAAC,QAAAE,CAAA,G;uBAvCUjB,UAAU;IAAA;IAAA,QAAAkB,EAAA,G;;YAAVlB,UAAU;MAAAmB,IAAA;IAAA;;SAAVnB,UAAU;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}