{"ast": null, "code": "import { LOCALES } from './constants';\nexport function isValidationError(error) {\n  return error != null && error.name === 'Validation';\n}\nexport function isRevolutCheckoutError(error) {\n  return error != null && error.name === 'RevolutCheckout';\n}\nexport function isValidLocale(locale) {\n  return locale && LOCALES.some(function (value) {\n    return value === locale;\n  });\n}", "map": {"version": 3, "names": ["LOCALES", "isValidationError", "error", "name", "isRevolutCheckoutError", "isValidLocale", "locale", "some", "value"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@revolut/checkout/esm/checks.js"], "sourcesContent": ["import { LOCALES } from './constants';\nexport function isValidationError(error) {\n    return error != null && error.name === 'Validation';\n}\nexport function isRevolutCheckoutError(error) {\n    return error != null && error.name === 'RevolutCheckout';\n}\nexport function isValidLocale(locale) {\n    return locale && LOCALES.some(function (value) { return value === locale; });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,aAAa;AACrC,OAAO,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EACrC,OAAOA,KAAK,IAAI,IAAI,IAAIA,KAAK,CAACC,IAAI,KAAK,YAAY;AACvD;AACA,OAAO,SAASC,sBAAsBA,CAACF,KAAK,EAAE;EAC1C,OAAOA,KAAK,IAAI,IAAI,IAAIA,KAAK,CAACC,IAAI,KAAK,iBAAiB;AAC5D;AACA,OAAO,SAASE,aAAaA,CAACC,MAAM,EAAE;EAClC,OAAOA,MAAM,IAAIN,OAAO,CAACO,IAAI,CAAC,UAAUC,KAAK,EAAE;IAAE,OAAOA,KAAK,KAAKF,MAAM;EAAE,CAAC,CAAC;AAChF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}