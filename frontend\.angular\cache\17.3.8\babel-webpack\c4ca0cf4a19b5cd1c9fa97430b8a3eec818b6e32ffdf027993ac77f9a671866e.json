{"ast": null, "code": "import { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { SubSink } from 'subsink';\nimport * as moment from 'moment';\nimport { NoteListenersService, NoteViewAccess } from 'src/app/core/services/note-listeners.service';\nimport { LibraryFileActionsDialogComponent } from 'src/app/modules/library/library/components/library-file-actions-dialog/library-file-actions-dialog.component';\nimport { DialogService } from 'primeng/dynamicdialog';\nimport { tap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nimport * as i2 from \"src/app/core/services/note.service\";\nimport * as i3 from \"src/app/core/services/toast.service\";\nimport * as i4 from \"src/app/core/services/classroom.service\";\nimport * as i5 from \"src/app/core/services/auth.service\";\nimport * as i6 from \"src/app/core/services/note-listeners.service\";\nimport * as i7 from \"src/app/core/services/note-actions.service\";\nimport * as i8 from \"primeng/dynamicdialog\";\nimport * as i9 from \"src/app/core/services/confirm-dialog.service\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"primeng/tabview\";\nimport * as i12 from \"@angular/forms\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nimport * as i14 from \"./note-details/note-details.component\";\nimport * as i15 from \"src/app/core/pipes/filter.pipe\";\nconst _c0 = a0 => ({\n  \"filter-blue\": a0\n});\nconst _c1 = (a0, a1) => [a0, a1];\nconst _c2 = (a0, a1) => ({\n  classroom: a0,\n  filteredNotes: a1\n});\nconst _c3 = (a0, a1, a2) => ({\n  classroom: a0,\n  filteredNotes: a1,\n  student: a2\n});\nconst _c4 = a0 => ({\n  term: a0\n});\nfunction NotesListComponent_img_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"img\", 14);\n    i0.ɵɵlistener(\"click\", function NotesListComponent_img_9_Template_img_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showAddNote = !ctx_r1.showAddNote);\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotesListComponent_div_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function NotesListComponent_div_11_div_1_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onShareMultiple());\n    });\n    i0.ɵɵelementStart(1, \"div\", 18);\n    i0.ɵɵelement(2, \"img\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 20);\n    i0.ɵɵtext(4, \" Share \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotesListComponent_div_11_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function NotesListComponent_div_11_div_2_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onUnShareMultiple());\n    });\n    i0.ɵɵelementStart(1, \"div\", 18);\n    i0.ɵɵelement(2, \"img\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 20);\n    i0.ɵɵtext(4, \" Unshare \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotesListComponent_div_11_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function NotesListComponent_div_11_div_8_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onRemoveNoteMultiple());\n    });\n    i0.ɵɵelementStart(1, \"div\", 18);\n    i0.ɵɵelement(2, \"img\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 20);\n    i0.ɵɵtext(4, \" Delete \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotesListComponent_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15);\n    i0.ɵɵtemplate(1, NotesListComponent_div_11_div_1_Template, 5, 0, \"div\", 16)(2, NotesListComponent_div_11_div_2_Template, 5, 0, \"div\", 16);\n    i0.ɵɵelementStart(3, \"div\", 17);\n    i0.ɵɵlistener(\"click\", function NotesListComponent_div_11_Template_div_click_3_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.downloadMultiple());\n    });\n    i0.ɵɵelementStart(4, \"div\", 18);\n    i0.ɵɵelement(5, \"img\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 20);\n    i0.ɵɵtext(7, \" Download \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(8, NotesListComponent_div_11_div_8_Template, 5, 0, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpureFunction2(3, _c1, ctx_r1.noteViewAccess.TEACHER_VIEW_STUDENTS_CLASSROOM_NOTES, ctx_r1.noteViewAccess.STUDENT_VIEW_TEACHERS_CLASSROOM_NOTES).includes(ctx_r1.currentNotesView));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentNotesView !== ctx_r1.noteViewAccess.TEACHER_VIEW_NOTES && ctx_r1.currentNotesView !== ctx_r1.noteViewAccess.STUDENT_VIEW_TEACHERS_CLASSROOM_NOTES);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.hasAdd);\n  }\n}\nfunction NotesListComponent_ng_container_12_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-note-details\", 23);\n    i0.ɵɵlistener(\"closedNewNote\", function NotesListComponent_ng_container_12_div_1_Template_app_note_details_closedNewNote_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onClosedNewNote());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"type\", \"create\")(\"classroom\", ctx_r1.classroom)(\"showGroupActions\", ctx_r1.showGroupActions)(\"view\", ctx_r1.noteListenersService.getNotesViewAccess(ctx_r1.hasAdd, ctx_r1.withClassroom));\n  }\n}\nfunction NotesListComponent_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NotesListComponent_ng_container_12_div_1_Template, 2, 4, \"div\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.classroomStudents && ctx_r1.classroomStudents.length === 1 || !ctx_r1.withClassroom) && ctx_r1.hasAdd);\n  }\n}\nfunction NotesListComponent_div_13_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NotesListComponent_div_13_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NotesListComponent_div_13_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const notesGeneralTemplate_r8 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", notesGeneralTemplate_r8)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c2, ctx_r1.classroom, ctx_r1.filteredNotes));\n  }\n}\nfunction NotesListComponent_div_13_ng_container_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NotesListComponent_div_13_ng_container_2_ng_container_5_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NotesListComponent_div_13_ng_container_2_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p-tabPanel\", 31);\n    i0.ɵɵtemplate(2, NotesListComponent_div_13_ng_container_2_ng_container_5_ng_container_2_Template, 1, 0, \"ng-container\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const selectedStudent_r9 = ctx.$implicit;\n    i0.ɵɵnextContext(2);\n    const notesGeneralTemplate_r8 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"header\", selectedStudent_r9.firstName);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", notesGeneralTemplate_r8)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction3(3, _c3, ctx_r1.classroom, ctx_r1.filteredNotes, selectedStudent_r9));\n  }\n}\nfunction NotesListComponent_div_13_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p-tabView\", 27)(2, \"p-tabPanel\", 28)(3, \"div\", 29);\n    i0.ɵɵtemplate(4, NotesListComponent_div_13_ng_container_2_ng_container_4_Template, 1, 0, \"ng-container\", 26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, NotesListComponent_div_13_ng_container_2_ng_container_5_Template, 3, 7, \"ng-container\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const notesGeneralTemplate_r8 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"scrollable\", ctx_r1.classroomStudents.length > 4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", notesGeneralTemplate_r8)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(4, _c2, ctx_r1.classroom, ctx_r1.filteredNotes));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.classroomStudents);\n  }\n}\nfunction NotesListComponent_div_13_ng_template_3_ng_container_0_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-note-details\", 23);\n    i0.ɵɵlistener(\"closedNewNote\", function NotesListComponent_div_13_ng_template_3_ng_container_0_div_1_Template_app_note_details_closedNewNote_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onClosedNewNote());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const classroom_r11 = i0.ɵɵnextContext(2).classroom;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"type\", \"create\")(\"classroom\", classroom_r11)(\"showGroupActions\", ctx_r1.showGroupActions)(\"view\", ctx_r1.noteListenersService.getNotesViewAccess(ctx_r1.hasAdd, ctx_r1.withClassroom));\n  }\n}\nfunction NotesListComponent_div_13_ng_template_3_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NotesListComponent_div_13_ng_template_3_ng_container_0_div_1_Template, 2, 4, \"div\", 12);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.classroomStudents && ctx_r1.classroomStudents.length > 1);\n  }\n}\nfunction NotesListComponent_div_13_ng_template_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"app-note-details\", 33);\n    i0.ɵɵlistener(\"noteChecked\", function NotesListComponent_div_13_ng_template_3_div_1_Template_app_note_details_noteChecked_1_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onNoteChecked($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const note_r13 = ctx.$implicit;\n    const classroom_r11 = i0.ɵɵnextContext().classroom;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"isInMine\", ctx_r1.hasAdd)(\"note\", note_r13)(\"classroom\", classroom_r11)(\"withClassroom\", ctx_r1.withClassroom)(\"showGroupActions\", ctx_r1.showGroupActions)(\"view\", ctx_r1.noteListenersService.getNotesViewAccess(ctx_r1.hasAdd, ctx_r1.withClassroom));\n  }\n}\nfunction NotesListComponent_div_13_ng_template_3_div_3_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"img\", 37);\n    i0.ɵɵlistener(\"click\", function NotesListComponent_div_13_ng_template_3_div_3_div_1_Template_img_click_1_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.showAddNote = !ctx_r1.showAddNote);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"p\", 38);\n    i0.ɵɵtext(3, \" Add your first note! \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NotesListComponent_div_13_ng_template_3_div_3_ng_container_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" No notes found. \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction NotesListComponent_div_13_ng_template_3_div_3_ng_container_2_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" No notes found. \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction NotesListComponent_div_13_ng_template_3_div_3_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 36);\n    i0.ɵɵelement(2, \"img\", 39);\n    i0.ɵɵelementStart(3, \"p\", 38);\n    i0.ɵɵtemplate(4, NotesListComponent_div_13_ng_template_3_div_3_ng_container_2_ng_container_4_Template, 2, 0, \"ng-container\", 12)(5, NotesListComponent_div_13_ng_template_3_div_3_ng_container_2_ng_container_5_Template, 2, 0, \"ng-container\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isStudent);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isTeacher);\n  }\n}\nfunction NotesListComponent_div_13_ng_template_3_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtemplate(1, NotesListComponent_div_13_ng_template_3_div_3_div_1_Template, 4, 0, \"div\", 35)(2, NotesListComponent_div_13_ng_template_3_div_3_ng_container_2_Template, 6, 2, \"ng-container\", 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const student_r15 = i0.ɵɵnextContext().student;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"fromRoot\", true)(\"scrollWindow\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.hasAdd || ctx_r1.getFilteredNotes(student_r15).length == 0) && ctx_r1.getFilteredNotes(student_r15).length == 0 && !student_r15 && ctx_r1.hasAdd);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hasAdd || ctx_r1.getFilteredNotes(student_r15).length === 0 && student_r15);\n  }\n}\nfunction NotesListComponent_div_13_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NotesListComponent_div_13_ng_template_3_ng_container_0_Template, 2, 1, \"ng-container\", 12)(1, NotesListComponent_div_13_ng_template_3_div_1_Template, 2, 6, \"div\", 30);\n    i0.ɵɵpipe(2, \"filter\");\n    i0.ɵɵtemplate(3, NotesListComponent_div_13_ng_template_3_div_3_Template, 3, 4, \"div\", 32);\n  }\n  if (rf & 2) {\n    const student_r15 = ctx.student;\n    const filteredNotes_r16 = ctx.filteredNotes;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentNotesView === ctx_r1.noteViewAccess.TEACHER_VIEW_CLASSROOM_NOTES && ctx_r1.showAddNote);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", i0.ɵɵpipeBind2(2, 3, ctx_r1.getFilteredNotes(student_r15), i0.ɵɵpureFunction1(6, _c4, ctx_r1.term)));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", filteredNotes_r16 && ctx_r1.getFilteredNotes(student_r15).length == 0 && !ctx_r1.showAddNote);\n  }\n}\nfunction NotesListComponent_div_13_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵelement(1, \"mat-spinner\", 41);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NotesListComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵtemplate(1, NotesListComponent_div_13_ng_container_1_Template, 2, 5, \"ng-container\", 12)(2, NotesListComponent_div_13_ng_container_2_Template, 6, 7, \"ng-container\", 12)(3, NotesListComponent_div_13_ng_template_3_Template, 4, 8, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(5, NotesListComponent_div_13_div_5_Template, 2, 0, \"div\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.classroomStudents && ctx_r1.classroomStudents.length === 1 || !ctx_r1.withClassroom || ctx_r1.authService.isStudent);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.withClassroom && ctx_r1.authService.isTeacher && ctx_r1.classroomStudents && ctx_r1.classroomStudents.length > 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLoading);\n  }\n}\nexport class NotesListComponent {\n  constructor(generalService, noteService, toastService, classroomService, cd, authService, noteListenersService, noteActionsService, dialogService, confirmDialogService) {\n    this.generalService = generalService;\n    this.noteService = noteService;\n    this.toastService = toastService;\n    this.classroomService = classroomService;\n    this.cd = cd;\n    this.authService = authService;\n    this.noteListenersService = noteListenersService;\n    this.noteActionsService = noteActionsService;\n    this.dialogService = dialogService;\n    this.confirmDialogService = confirmDialogService;\n    this.notes = [];\n    this.viewTitle = 'My Notes';\n    this.withClassroom = false;\n    this.hasAdd = true;\n    this.classroom = {};\n    this.subs = new SubSink();\n    this.showFilters = false;\n    this.showGroupActions = true;\n    this.showAddNote = false;\n    this.tryToSave = false;\n    this.showTextError = false;\n    this.isSharedFilter = false;\n    this.isNotSharedFilter = false;\n    this.isPinnedFilter = false;\n    this.isFavoritedFilter = false;\n    this.fromFilterValue = {};\n    this.toFilterValue = {};\n    this.datePickerOpen = false;\n    this.filteredNotes = [];\n    this.classrooms = [];\n    this.showSend = false;\n    this.showSendAndShare = false;\n    this.usersToSend = [];\n    this.currentSelectedClassroomId = 0;\n    this.showingMine = true;\n    this.inClassroom = false;\n    this.isLoading = false;\n    this.textEditor = '';\n    this.checkedNotes = [];\n    this.classroomStudents = [];\n    this.user = {};\n    this.role = \"\";\n    this.maxSize = 6;\n    this.directionLinks = true;\n    this.autoHide = false;\n    this.to = {};\n    this.from = {};\n    this.form = new UntypedFormGroup({});\n    this.isInMine = false;\n    this.noteViewAccess = NoteViewAccess;\n  }\n  ngOnInit() {\n    this.user = this.authService.getLoggedInUser();\n    this.role = this.user.role;\n    this.initClassroomListener();\n    // this.notSharedFilter(true);\n    this.form = new UntypedFormGroup({\n      title: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      }),\n      text: new UntypedFormControl(null, {\n        validators: [Validators.required]\n      })\n    });\n  }\n  ngOnChanges() {\n    if (this.classroom) {\n      this.classroomStudents = this.classroom.classroomStudents;\n    }\n    this.filteredNotes = this.notes;\n  }\n  ngAfterViewInit() {\n    this.showGroupActions = false;\n    this.cd.detectChanges();\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n    this.noteListenersService.setGroupActions(false);\n    this.noteService.checkedNotes = [];\n  }\n  ngAfterContentInit() {}\n  get currentNotesView() {\n    return this.noteListenersService.getNotesViewAccess(this.hasAdd, this.withClassroom);\n  }\n  //listener to change ui in nite details\n  setGroupActions() {\n    this.showGroupActions = !this.showGroupActions;\n    if (!this.showGroupActions) {\n      this.noteService.checkedNotes = [];\n    }\n    setTimeout(() => {\n      this.noteListenersService.setGroupActions(this.showGroupActions);\n    }, 300);\n  }\n  /**\n   * Filters the notes based on whether they are shared with the given student.\n   *\n   * @param targetStudent - The student user to filter by.\n   * @returns An array of notes that are shared with the given student.\n   */\n  getFilteredNotes(targetStudent) {\n    if (this.hasAdd) {\n      return this.filteredNotes.filter(note => {\n        return targetStudent ? note.sharedWithUsers.some(user => user.aspUserId === targetStudent.aspUserId) : true;\n      });\n    } else {\n      return this.filteredNotes.filter(note => {\n        return targetStudent ? note.createdBy.aspUserId === targetStudent.aspUserId : true;\n      });\n    }\n  }\n  areNotFavorited() {\n    for (let note of this.noteService.checkedNotes) {\n      if (note.favouritedBy.filter(user => user.id == this.authService.getUserId()).length == 0) return true;\n    }\n    return false;\n  }\n  areNotPinned() {\n    for (let note of this.noteService.checkedNotes) {\n      if (!note.pinned) return true;\n    }\n    return false;\n  }\n  /**\n   * Filters the notes based on the selected filters and returns the filtered notes.\n   *\n   * @return {Note[]} An array of Note objects that pass the applied filters.\n   */\n  getNotesFiltered() {\n    let filteredNotes = [];\n    for (let note of this.notes) {\n      if (this.isPinnedFilter) {\n        if (!note.pinned) {\n          continue;\n        }\n      }\n      if (this.isFavoritedFilter) {\n        if (note.favouritedBy.filter(user => user.aspUserId == this.authService.getUserId()).length == 0) {\n          continue;\n        }\n      }\n      //get shared \n      if (this.from.value) {\n        if (!(note.created.getTime() > this.from.value.getTime())) {\n          continue;\n        }\n      }\n      if (this.to.value) {\n        if (!(note.created.getTime() < moment(this.to.value).add('1', 'd').toDate().getTime())) {\n          continue;\n        }\n      }\n      if (this.isSharedFilter) {\n        if (note.sharedWithUsers.length == 0) {\n          continue;\n        }\n      }\n      if (this.isNotSharedFilter) {\n        if (note.sharedWithUsers.length > 0) {\n          continue;\n        }\n      }\n      filteredNotes.push(note);\n    }\n    return filteredNotes;\n  }\n  ifFieldValid(field) {\n    return this.form.get(field)?.invalid && this.form.get(field)?.touched || this.tryToSave && this.form.get(field)?.invalid;\n  }\n  isTextValid() {\n    this.showTextError = !this.textEditor && this.textEditor === '' ? true : false;\n  }\n  close(event) {\n    this.showSend = false;\n    this.showSendAndShare = false;\n  }\n  onClosedNewNote() {\n    console.log('he');\n    this.showAddNote = false;\n  }\n  onNoteChecked(event) {\n    console.log(event);\n    if (event.checked) {\n      this.noteService.checkedNotes.push(event.note);\n    } else {\n      this.noteService.checkedNotes = this.noteService.checkedNotes.filter(el => el.id !== event.note.id);\n    }\n  }\n  onShareMultiple() {\n    const checkedNotes = this.noteService.checkedNotes;\n    if (checkedNotes.length === 0) {\n      this.validateCheckedNotes('Please select at least one note.');\n      return;\n    }\n    const isTeacher = this.authService.isTeacher;\n    if (isTeacher) {\n      this.handleTeacherShareMultiple(checkedNotes);\n    } else {\n      this.handleStudentShareMultiple(checkedNotes);\n    }\n  }\n  onUnShareMultiple() {\n    if (this.noteService.checkedNotes.length === 0) {\n      this.validateCheckedNotes('Please select at least one note.');\n      return;\n    }\n    this.noteService.checkedNotes.forEach(note => {\n      const unshareWith = note.sharedWithUsers.map(user => user.aspUserId);\n      const sendForUnshare = {\n        noteId: note.id,\n        classroomId: Number(this.classroom.id),\n        studentIds: this.getUserIdsForUnshare(unshareWith, note)\n      };\n      this.noteActionsService.unShareNote(sendForUnshare, 'Notes unshared successfully');\n    });\n    this.setGroupActions();\n  }\n  downloadMultiple() {\n    if (this.noteService.checkedNotes.length === 0) {\n      this.validateCheckedNotes('Please select at least one note.');\n      return;\n    }\n    for (let note of this.noteService.checkedNotes) {\n      this.noteActionsService.download(note);\n    }\n  }\n  onRemoveNoteMultiple() {\n    this.initConfirmDeleteNoteDialogListener();\n    if (this.noteService.checkedNotes.length === 0) {\n      this.validateCheckedNotes('Please select at least one note.');\n      return;\n    }\n    this.confirmDialogService.setProperties({\n      confirmMessage: `\n      Are you sure you want to delete the selected notes?`,\n      acceptBtnLabel: 'Yes',\n      rejectBtnLabel: 'No',\n      action: 'delete-note',\n      confirmIcon: '/assets/icons/clock-dialog-icon.svg',\n      showHeader: true,\n      dialogType: 'custom',\n      headerClass: 'my-custom-header-class'\n    });\n    this.confirmDialogService.show();\n  }\n  getUserIdsForUnshare(unshareWith, note) {\n    if (this.authService.isTeacher) {\n      return unshareWith;\n    } else if (this.noteService.isMyNote(note)) {\n      return [this.classroom.teacher.aspUserId];\n    }\n    return [this.user.aspUserId];\n  }\n  handleTeacherShareMultiple(checkedNotes) {\n    if (![this.noteViewAccess.TEACHER_VIEW_STUDENTS_CLASSROOM_NOTES, this.noteViewAccess.TEACHER_VIEW_NOTES].includes(this.currentNotesView)) {\n      if (this.classroom.classroomStudents.length === 1) {\n        this.shareNotesMultipleWithClassroom(checkedNotes);\n      } else {\n        this.openShareMultipleWithNoteDialog(checkedNotes);\n      }\n    } else {\n      this.openShareMultipleWithNoteDialog(checkedNotes);\n    }\n  }\n  handleStudentShareMultiple(checkedNotes) {\n    const unshareWith = [];\n    for (let note of checkedNotes) {\n      for (let user of note.sharedWithUsers) {\n        unshareWith.push(user.aspUserId);\n      }\n      const sendForShare = {\n        noteId: note.id,\n        classroomId: +this.classroom.id,\n        studentIds: [this.classroom.teacher.aspUserId]\n      };\n      this.noteActionsService.shareNote(sendForShare, 'Notes unshared successfully');\n    }\n    this.setGroupActions();\n  }\n  shareNotesMultipleWithClassroom(checkedNotes) {\n    for (let note of checkedNotes) {\n      const sendForShare = {\n        noteId: note.id,\n        classroomId: +this.classroom.id,\n        studentIds: [this.classroom.classroomStudents[0].aspUserId]\n      };\n      this.noteActionsService.shareNote(sendForShare, 'Notes shared successfully');\n    }\n  }\n  openShareMultipleWithNoteDialog(checkedNotes) {\n    this.generalService.openDialogWithComponent(this.dialogService, LibraryFileActionsDialogComponent, 440, {\n      action: 'shareWith',\n      dialogsubTitle: '',\n      dialogTitle: 'Share Note with...',\n      preselectedClassroom: this.classroom\n    }, null, result => {\n      console.log(result);\n      if (result && result.action === 'shareWith') {\n        if (!this.generalService.isNullishObject(result)) {\n          for (let note of checkedNotes) {\n            this.noteActionsService.sendOrShare(note, result, this.authService.isStudent);\n          }\n          this.setGroupActions();\n          console.log(result);\n        }\n      }\n    });\n  }\n  initConfirmDeleteNoteDialogListener() {\n    this.confirmDeleteNoteSubscription = this.confirmDialogService.acceptSelected$.pipe(tap(res => {\n      this.confirmDialogService.hide();\n      console.log(res);\n      switch (res.action) {\n        case 'delete-note':\n          console.log('deltenetote');\n          this.onRemoveNoteMultipleAccepted();\n      }\n    })).subscribe();\n    this.subs.add(this.confirmDeleteNoteSubscription);\n  }\n  onRemoveNoteMultipleAccepted() {\n    console.log(this.noteService.checkedNotes);\n    for (let note of this.noteService.checkedNotes) {\n      console.log(note);\n      this.noteActionsService.afterAcceptRemoveNote(note);\n    }\n    this.confirmDeleteNoteSubscription.unsubscribe();\n    this.setGroupActions();\n  }\n  validateCheckedNotes(msg) {\n    if (this.noteService.checkedNotes.length === 0) {\n      this.toastService.setShowToastmessage({\n        severity: 'warn',\n        summary: '',\n        detail: msg\n      });\n      return;\n    }\n  }\n  initClassroomListener() {\n    this.subs.add(this.noteListenersService.currentSelectedClassroom.subscribe(res => {\n      if (this.generalService.isNullishObject(res)) {\n        return;\n      }\n      console.log(this.filteredNotes);\n      this.classroom = res;\n      this.classroomStudents = this.classroom.classroomStudents;\n    }));\n  }\n  static #_ = this.ɵfac = function NotesListComponent_Factory(t) {\n    return new (t || NotesListComponent)(i0.ɵɵdirectiveInject(i1.GeneralService), i0.ɵɵdirectiveInject(i2.NoteService), i0.ɵɵdirectiveInject(i3.ToastService), i0.ɵɵdirectiveInject(i4.ClassroomService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i5.AuthService), i0.ɵɵdirectiveInject(i6.NoteListenersService), i0.ɵɵdirectiveInject(i7.NoteActionsService), i0.ɵɵdirectiveInject(i8.DialogService), i0.ɵɵdirectiveInject(i9.ConfirmDialogService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NotesListComponent,\n    selectors: [[\"app-notes-list\"]],\n    inputs: {\n      notes: \"notes\",\n      viewTitle: \"viewTitle\",\n      withClassroom: \"withClassroom\",\n      hasAdd: \"hasAdd\",\n      classroom: \"classroom\"\n    },\n    features: [i0.ɵɵProvidersFeature([DialogService, NoteListenersService]), i0.ɵɵNgOnChangesFeature],\n    decls: 14,\n    vars: 9,\n    consts: [[\"notesGeneralTemplate\", \"\"], [1, \"notes-header\"], [1, \"notes-header-title\"], [1, \"notes-header-actions\"], [1, \"notes-header-actions\", \"gap-2\"], [1, \"p-input-icon-right\", \"w-full\"], [\"type\", \"text\", \"pInputText\", \"\", \"placeholder\", \"Search\", 1, \"h-2rem\", \"w-full\", \"input-blue\", \"gradient-blue\", \"rounded\", 3, \"ngModelChange\", \"ngModel\"], [1, \"pi\", \"pi-search\", \"text-primary\"], [\"src\", \"/assets/icons/library/group-actions.svg\", \"title\", \"Group Actions\", 1, \"hvr-grow\", 3, \"click\", \"ngClass\"], [\"title\", \"Create Note\", \"src\", \"/assets/icons/library/add-lib.svg\", \"class\", \"add hvr-grow add-note-btn-icon\", 3, \"click\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\"], [\"class\", \"note-menu-row block-gradient border-round-xl font-sm my-2 p-1 px-3 gap-2\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"notes-height\", 4, \"ngIf\"], [\"title\", \"Create Note\", \"src\", \"/assets/icons/library/add-lib.svg\", 1, \"add\", \"hvr-grow\", \"add-note-btn-icon\", 3, \"click\"], [1, \"note-menu-row\", \"block-gradient\", \"border-round-xl\", \"font-sm\", \"my-2\", \"p-1\", \"px-3\", \"gap-2\"], [\"class\", \"note-menu-col\", 3, \"click\", 4, \"ngIf\"], [1, \"note-menu-col\", 3, \"click\"], [1, \"note-menu-icon\"], [\"src\", \"/assets/icons/cloud-download.svg\", \"height\", \"14\"], [1, \"note-menu-text\", \"link-main-color\"], [\"src\", \"/assets/icons/library/share-sm-icon.svg\", \"height\", \"14\"], [\"src\", \"/assets/icons/library/delete.svg\", \"height\", \"14\"], [3, \"closedNewNote\", \"note\", \"type\", \"classroom\", \"showGroupActions\", \"view\"], [1, \"notes-height\"], [\"style\", \"width:100%;height:800px; display: flex; justify-content: center;\", 4, \"ngIf\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"w-full\", \"notes-height\", 3, \"scrollable\"], [\"header\", \"All\"], [\"id\", \"classroom-info\", 1, \"h-full\"], [4, \"ngFor\", \"ngForOf\"], [1, \"\", 3, \"header\"], [\"class\", \"asked-for-help-main scrollable h-full notes-height\", 3, \"fromRoot\", \"scrollWindow\", 4, \"ngIf\"], [3, \"noteChecked\", \"isInMine\", \"note\", \"classroom\", \"withClassroom\", \"showGroupActions\", \"view\"], [1, \"asked-for-help-main\", \"scrollable\", \"h-full\", \"notes-height\", 3, \"fromRoot\", \"scrollWindow\"], [\"class\", \"flex flex-column gap-1 align-items-center justify-content-center h-full\", 4, \"ngIf\"], [1, \"flex\", \"flex-column\", \"gap-1\", \"align-items-center\", \"justify-content-center\", \"h-full\"], [\"src\", \"/assets/icons/library/add-lib.svg\", 1, \"add\", \"lg\", \"hvr-grow\", 3, \"click\"], [1, \"font-base\", \"max-w-14rem\", \"text-center\"], [\"src\", \"/assets/icons/library/file_manager_emp.png\", \"alt\", \"file manager empty icon\", 1, \"add\", \"empty\", \"hvr-grow\"], [2, \"width\", \"100%\", \"height\", \"800px\", \"display\", \"flex\", \"justify-content\", \"center\"], [1, \"spinner\", 2, \"margin-top\", \"200px\"]],\n    template: function NotesListComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n        i0.ɵɵtext(2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"div\", 4)(5, \"span\", 5)(6, \"input\", 6);\n        i0.ɵɵtwoWayListener(\"ngModelChange\", function NotesListComponent_Template_input_ngModelChange_6_listener($event) {\n          i0.ɵɵtwoWayBindingSet(ctx.term, $event) || (ctx.term = $event);\n          return $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(7, \"i\", 7);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"img\", 8);\n        i0.ɵɵlistener(\"click\", function NotesListComponent_Template_img_click_8_listener() {\n          return ctx.setGroupActions();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(9, NotesListComponent_img_9_Template, 1, 0, \"img\", 9);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(10, \"div\", 10);\n        i0.ɵɵtemplate(11, NotesListComponent_div_11_Template, 9, 6, \"div\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(12, NotesListComponent_ng_container_12_Template, 2, 1, \"ng-container\", 12)(13, NotesListComponent_div_13_Template, 6, 3, \"div\", 13);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\" \", ctx.viewTitle, \" \");\n        i0.ɵɵadvance(4);\n        i0.ɵɵtwoWayProperty(\"ngModel\", ctx.term);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(7, _c0, ctx.showGroupActions));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.hasAdd);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.showGroupActions);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", (ctx.currentNotesView === ctx.noteViewAccess.TEACHER_VIEW_CLASSROOM_NOTES || ctx.currentNotesView === ctx.noteViewAccess.STUDENT_VIEW_CLASSROOM_NOTES || ctx.currentNotesView === ctx.noteViewAccess.TEACHER_VIEW_NOTES) && ctx.showAddNote);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n      }\n    },\n    dependencies: [i10.NgClass, i10.NgForOf, i10.NgIf, i10.NgTemplateOutlet, i11.TabView, i11.TabPanel, i12.DefaultValueAccessor, i12.NgControlStatus, i12.NgModel, i13.MatProgressSpinner, i14.NoteDetailsComponent, i15.FilterPipe],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.modal[_ngcontent-%COMP%] {\\n  min-width: 50vw;\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown:not(.p-disabled):hover {\\n  border: 2px solid rgb(228, 185, 84);\\n}\\n\\n  .no-label > .p-button-label {\\n  display: none;\\n}\\n\\ninput[type=file][_ngcontent-%COMP%] {\\n  visibility: hidden;\\n  width: 10px;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%] {\\n  position: relative;\\n  padding: 10px;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%]::-webkit-calendar-picker-indicator {\\n  color: transparent;\\n  background: none;\\n  z-index: 1;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%]:before {\\n  color: transparent;\\n  background: none;\\n  display: block;\\n  font-family: \\\"FontAwesome\\\";\\n  content: \\\"\\\\f073\\\";\\n  \\n\\n  width: 20px;\\n  height: 25px;\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  color: #999;\\n}\\n\\n.image-preview[_ngcontent-%COMP%] {\\n  height: 10rem;\\n  margin: 1rem 0;\\n  border-radius: 50%;\\n  border: 3px solid var(--main-color);\\n}\\n\\n.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 100%;\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-header {\\n  padding: clamp(0.38rem, 0.47vw + 0.28rem, 0.75rem);\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-items .p-dropdown-item {\\n  margin-bottom: clamp(0.38rem, 0.47vw + 0.28rem, 0.75rem);\\n}\\n\\n.country-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: clamp(0.75rem, 0.31vw + 0.69rem, 1rem);\\n  font-size: clamp(0.88rem, 0.16vw + 0.84rem, 1rem);\\n}\\n.country-item[_ngcontent-%COMP%]   .country-name[_ngcontent-%COMP%] {\\n  white-space: pre-line; \\n\\n}\\n.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  \\n\\n\\n\\n\\n\\n  \\n\\n  display: inline-block;\\n  vertical-align: middle;\\n}\\n.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  clip-path: circle();\\n}\\n.country-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 32px;\\n  margin-right: 0.5rem;\\n}\\n.country-item[_ngcontent-%COMP%]   img.smaller[_ngcontent-%COMP%] {\\n  width: 16px;\\n}\\n\\n.info-element[_ngcontent-%COMP%] {\\n  padding: 10px 0;\\n}\\n\\n.responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.note[_ngcontent-%COMP%] {\\n  border-radius: 40px;\\n  border: 1px solid #eaf0f5;\\n  margin-top: 2px;\\n  position: relative;\\n}\\n.note[_ngcontent-%COMP%]::before {\\n  pointer-events: none;\\n  content: \\\"\\\";\\n  position: absolute;\\n  inset: 0;\\n  border-radius: 30px;\\n  padding: 2px;\\n}\\n.note.isFavNote[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(90deg, hsl(221, 90%, 59%) 0%, hsl(241, 76%, 67%) 29%, hsl(261, 66%, 64%) 53%, hsl(279, 56%, 60%) 68%, hsl(296, 48%, 56%) 77%, hsl(311, 53%, 56%) 84%, hsl(321, 62%, 57%) 89%, hsl(330, 67%, 59%) 93%, hsl(337, 71%, 60%) 97%, hsl(344, 72%, 61%) 100%);\\n}\\n.note.isFavNote[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.note.isFavNote[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .note.isFavNote[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%] {\\n  border: 1px solid white;\\n}\\n.note.isFavNote[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .note.isFavNote[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  color: var(--main-color);\\n}\\n.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .section-arrow[_ngcontent-%COMP%] {\\n  width: 40px;\\n}\\n.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  width: 100%;\\n}\\n@media screen and (max-width: 768px) {\\n  .note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    margin-left: 20px;\\n  }\\n}\\n.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%] {\\n  margin-left: 20px;\\n}\\n@media screen and (max-width: 768px) {\\n  .note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%] {\\n    margin-left: 0px;\\n  }\\n}\\n.note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%]   .note-dates[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-top: 10px;\\n}\\n@media screen and (max-width: 768px) {\\n  .note[_ngcontent-%COMP%]   .note-header[_ngcontent-%COMP%]   .note-bla[_ngcontent-%COMP%]   .note-title[_ngcontent-%COMP%]   .note-dates[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n\\n.note-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n@media only screen and (max-width: 768px) {\\n  .note-info[_ngcontent-%COMP%] {\\n    margin-top: 0px;\\n  }\\n}\\n.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n@media only screen and (max-width: 768px) {\\n  .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%] {\\n    justify-content: end;\\n  }\\n}\\n.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-right: 10px;\\n  margin-left: auto;\\n}\\n@media only screen and (max-width: 768px) {\\n  .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n  }\\n}\\n.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-icon[_ngcontent-%COMP%], .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-text[_ngcontent-%COMP%] {\\n  margin-left: 4px;\\n  cursor: pointer;\\n}\\n@media screen and (max-width: 768px) {\\n  .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-icon[_ngcontent-%COMP%], .note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-text[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n  }\\n}\\n.note-info[_ngcontent-%COMP%]   .note-info-top[_ngcontent-%COMP%]   .note-info-element[_ngcontent-%COMP%]   .note-info-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  display: flex;\\n  justify-content: center;\\n}\\n.note-info[_ngcontent-%COMP%]   .note-info-bottom[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: end;\\n  text-align: right;\\n  margin-right: 10px;\\n  font-size: 0.725rem;\\n  color: #2d3b8e;\\n}\\n\\n.note-content[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  transition: height 0.3s ease-out;\\n}\\n.rte[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  border-radius: 20px;\\n  z-index: 0 !important;\\n}\\n\\n.note-text-content[_ngcontent-%COMP%] {\\n  padding: 20px 30px;\\n}\\n\\n.update-button[_ngcontent-%COMP%] {\\n  padding: 5px 30px;\\n  background-color: var(--light-purple);\\n  text-align: center;\\n  border-radius: 10px;\\n  color: white;\\n  margin-top: 15px;\\n  cursor: pointer;\\n}\\n\\n.update-button[_ngcontent-%COMP%]:hover {\\n  background-color: var(--main-color);\\n}\\n\\n.section-arrow[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  transition-duration: 0.2s;\\n  transition-property: transform;\\n  cursor: pointer;\\n}\\n\\n.note-burger[_ngcontent-%COMP%], .note-burger-group[_ngcontent-%COMP%] {\\n  position: relative;\\n  border-radius: 50%;\\n  min-width: 36px;\\n  min-height: 36px;\\n  border: 2px solid #6563ec;\\n  display: flex;\\n  align-items: center;\\n  box-sizing: border-box;\\n  justify-content: center;\\n  cursor: pointer;\\n}\\n.note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%] {\\n  width: 4px;\\n  height: 4px;\\n  border-radius: 50%;\\n  background-color: #6563ec;\\n  margin: 2px;\\n}\\n.note-burger[_ngcontent-%COMP%]   .inner-circle-group[_ngcontent-%COMP%], .note-burger-group[_ngcontent-%COMP%]   .inner-circle-group[_ngcontent-%COMP%] {\\n  width: 34px;\\n  height: 34px;\\n  border-radius: 50%;\\n  background-color: transparent;\\n}\\n\\n.note-menu[_ngcontent-%COMP%] {\\n  width: 300px;\\n  position: absolute;\\n  right: 30px;\\n  top: 69px;\\n  background-color: white;\\n  border-radius: 12px;\\n  border: 1px solid var(--main-color);\\n  box-sizing: border-box;\\n  transition-duration: 0.2s;\\n  transition-property: transform;\\n  background-color: white;\\n  z-index: 1;\\n  display: none;\\n  flex-direction: column;\\n  padding: 10px;\\n}\\n@media screen and (max-width: 1124px) {\\n  .note-menu[_ngcontent-%COMP%] {\\n    right: 30px;\\n  }\\n}\\n.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%] {\\n  margin-top: 5px;\\n  display: flex;\\n  align-items: center;\\n}\\n.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-text[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n  cursor: pointer;\\n}\\n.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  display: flex;\\n  justify-content: center;\\n}\\n.note-menu[_ngcontent-%COMP%]   .note-menu-row[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n\\n[_nghost-%COMP%]     .e-toolbar-items {\\n  border-radius: 20px !important;\\n}\\n[_nghost-%COMP%]     .e-rte-toolbar {\\n  border-radius: 20px 20px 0px 0px !important;\\n}\\n[_nghost-%COMP%]     .e-rte-content {\\n  border-radius: 0 0 20px 20px !important;\\n  border-top: 0px !important;\\n}\\n\\n.plain[_ngcontent-%COMP%] {\\n  border-radius: 0px;\\n  margin: -13px 2px 0 2px;\\n  border-top: 0;\\n  border-bottom-right-radius: 12px;\\n  border-bottom-left-radius: 12px;\\n  position: relative;\\n}\\n\\n.filter-white[_ngcontent-%COMP%] {\\n  filter: invert(100%) sepia(0%) saturate(1%) hue-rotate(328deg) brightness(200%) contrast(101%);\\n}\\n\\n.note-info-width[_ngcontent-%COMP%] {\\n  min-width: 10rem;\\n}\\n\\n.note-title-text[_ngcontent-%COMP%] {\\n  font-weight: normal;\\n  font-size: 1rem;\\n  letter-spacing: 0.01em;\\n  text-align: left;\\n  max-width: 17rem;\\n}\\n@media only screen and (max-width: 768px) {\\n  .note-title-text[_ngcontent-%COMP%] {\\n    max-width: 9rem;\\n    font-size: 0.825rem;\\n  }\\n}\\n\\n.accordion[_ngcontent-%COMP%] {\\n  border-radius: 4px;\\n  margin-top: 10px;\\n}\\n\\n.header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 8px;\\n  cursor: pointer;\\n  border-radius: 31px;\\n  box-shadow: 3px 3px 6px 2px rgba(0, 0, 0, 0.16);\\n  z-index: 4;\\n  position: relative;\\n  border: 1px solid transparent;\\n}\\n.header.isPinned[_ngcontent-%COMP%] {\\n  color: white;\\n  background-image: linear-gradient(90deg, hsl(232, 49%, 54%) 0%, hsl(232, 49%, 54%) 13%, hsl(232, 49%, 54%) 25%, hsl(232, 49%, 54%) 37%, hsl(232, 49%, 54%) 50%, hsl(232, 49%, 54%) 63%, hsl(232, 49%, 54%) 75%, hsl(232, 49%, 54%) 87%, hsl(232, 49%, 54%) 100%);\\n}\\n.header.isPinned[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.isPinned[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%] {\\n  border: 2px solid white;\\n}\\n.header.isPinned[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.isPinned[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n.header.isPinned[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%] {\\n  background: transparent;\\n  border: 2px solid white;\\n}\\n.header.isPinnedAndFavourite[_ngcontent-%COMP%] {\\n  color: white;\\n  background-image: linear-gradient(90deg, hsl(231, 46%, 48%) 0%, hsl(231, 46%, 51%) 11%, hsl(232, 49%, 54%) 22%, hsl(232, 53%, 57%) 33%, hsl(232, 57%, 60%) 44%, hsl(233, 62%, 63%) 56%, hsl(233, 67%, 65%) 67%, hsl(233, 74%, 68%) 78%, hsl(233, 82%, 71%) 89%, hsl(233, 92%, 74%) 100%);\\n}\\n.header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%] {\\n  border: 2px solid white;\\n}\\n.header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.isPinnedAndFavourite[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n.header.isPinnedAndFavourite[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%] {\\n  background: transparent;\\n  border: 2px solid white;\\n}\\n.header.opened[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%] {\\n  border: 1px solid transparent;\\n  color: white;\\n  background-color: #7f8dfa;\\n}\\n.header.opened.activated[_ngcontent-%COMP%], .header.opened2.activated[_ngcontent-%COMP%] {\\n  background-color: #7f8dfa;\\n}\\n.header.opened[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.opened[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%] {\\n  border: 2px solid white;\\n}\\n.header.opened[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.opened[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .note-burger-group[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n.header.opened[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%], .header.opened2[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%] {\\n  border: 1px solid white;\\n  background: transparent;\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.content[_ngcontent-%COMP%] {\\n  padding: 10px;\\n}\\n\\n.down-arrow[_ngcontent-%COMP%] {\\n  transition: transform 0.3s ease-in-out;\\n}\\n\\n.down-arrow.pi-chevron-up[_ngcontent-%COMP%] {\\n  transform: rotate(360deg);\\n}\\n\\n.circle[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(180deg, hsl(249, 92%, 74%) 0%, hsl(248, 90%, 73%) 11%, hsl(247, 88%, 72%) 22%, hsl(246, 86%, 71%) 33%, hsl(245, 84%, 70%) 44%, hsl(244, 82%, 68%) 56%, hsl(242, 80%, 67%) 67%, hsl(241, 79%, 66%) 78%, hsl(240, 77%, 65%) 89%, hsl(238, 76%, 63%) 100%);\\n  width: 36px;\\n  height: 36px;\\n  border-radius: 50%;\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n}\\n\\n.circle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: white;\\n  font-size: 20px;\\n}\\n\\n.rotate[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n  transition: transform 0.3s ease-in-out;\\n}\\n\\n.accordion-content[_ngcontent-%COMP%] {\\n  max-height: 0;\\n  overflow: hidden;\\n}\\n\\n.open[_ngcontent-%COMP%] {\\n  background-color: white;\\n  margin-top: -20px;\\n  border-bottom-left-radius: 30px;\\n  border-bottom-right-radius: 30px;\\n  height: 370px;\\n  max-height: 370px;\\n  padding: 20px 3px 3px 3px;\\n}\\n@media only screen and (max-width: 768px) {\\n  .open[_ngcontent-%COMP%] {\\n    height: 410px;\\n    max-height: 410px;\\n  }\\n}\\n\\n.btn-action[_ngcontent-%COMP%] {\\n  width: 200px;\\n}\\n@media only screen and (max-width: 768px) {\\n  .btn-action[_ngcontent-%COMP%] {\\n    width: auto;\\n  }\\n}\\n\\n[_nghost-%COMP%]     .p-editor-container .p-editor-toolbar.ql-snow {\\n  border: none;\\n  padding: 0 !important;\\n}\\n[_nghost-%COMP%]     .p-editor-container .p-editor-content .ql-editor {\\n  background: transparent;\\n  padding: 4px;\\n}\\n[_nghost-%COMP%]     .p-editor-container .p-editor-content .ql-editor p {\\n  font-size: 1rem;\\n}\\n[_nghost-%COMP%]     .p-editor-container .p-editor-content .ql-editor[contenteditable=true] {\\n  border: 1px solid var(--my-gray);\\n  border-radius: 10px;\\n}\\n[_nghost-%COMP%]     .circle.ql-snow {\\n  border: none;\\n}\\n[_nghost-%COMP%]     .p-editor-container .p-editor-content.ql-snow {\\n  border: none;\\n}\\n[_nghost-%COMP%]     .ql-snow .ql-tooltip {\\n  transform: translate(80%, -50%);\\n}\\n@media only screen and (max-width: 768px) {\\n  [_nghost-%COMP%]     .ql-snow .ql-tooltip {\\n    transform: translate(45%, -50%);\\n  }\\n}\\n\\n  .p-menu .p-menuitem-link {\\n  padding: 0.5rem 1rem !important;\\n}\\n\\n#add-note[_ngcontent-%COMP%] {\\n  background-color: var(--gray);\\n  padding: 30px;\\n  border-radius: 12px;\\n  margin-top: 30px;\\n}\\n\\n.add-note-btn-icon[_ngcontent-%COMP%] {\\n  width: 25px;\\n  height: 25px;\\n}\\n\\n.notes-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.notes-header[_ngcontent-%COMP%]   .notes-header-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  color: var(--main-color);\\n  margin-left: 10px;\\n  font-weight: bold;\\n}\\n.notes-header[_ngcontent-%COMP%]   .notes-header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.notes-header[_ngcontent-%COMP%]   .notes-header-actions[_ngcontent-%COMP%]   div[_ngcontent-%COMP%] {\\n  margin-right: 0.7em;\\n}\\n.notes-header[_ngcontent-%COMP%]   .notes-header-actions[_ngcontent-%COMP%]   .light-purple-circle-button[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n}\\n\\n.note-menu-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-evenly;\\n  border-radius: 40px;\\n}\\n.note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  position: relative;\\n}\\n.note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-text[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n.note-menu-row[_ngcontent-%COMP%]   .note-menu-col[_ngcontent-%COMP%]   .note-menu-icon[_ngcontent-%COMP%] {\\n  width: 20px;\\n  display: flex;\\n  justify-content: center;\\n}\\n\\n.rte[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n  border-radius: 20px;\\n  z-index: 0 !important;\\n}\\n\\n[_nghost-%COMP%]     .e-toolbar-items {\\n  border-radius: 20px !important;\\n}\\n[_nghost-%COMP%]     .e-rte-toolbar {\\n  border-radius: 20px 20px 0px 0px !important;\\n}\\n[_nghost-%COMP%]     .e-rte-content {\\n  border-radius: 0 0 20px 20px !important;\\n  border-top: 0px !important;\\n}\\n[_nghost-%COMP%]     .e-date-icon {\\n  position: absolute !important;\\n  left: 0 !important;\\n}\\n[_nghost-%COMP%]     .e-input {\\n  margin-left: 30px !important;\\n}\\n\\n.notes-popup[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 40px;\\n  width: 200px;\\n  border-radius: 12px;\\n  background-color: white;\\n  color: var(--main-color);\\n  right: 0;\\n  box-shadow: 0px -4px 4px rgba(0, 0, 0, 0.25);\\n  max-height: 400px;\\n  z-index: 10000;\\n}\\n\\n.notes-filters-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: bold;\\n}\\n\\n.notes-filters-filters[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n}\\n.notes-filters-filters[_ngcontent-%COMP%]   .filter-row[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n  display: flex;\\n  align-items: center;\\n}\\n.notes-filters-filters[_ngcontent-%COMP%]   .filter-row[_ngcontent-%COMP%]   .filter[_ngcontent-%COMP%] {\\n  margin-left: 15px;\\n}\\n.notes-filters-filters[_ngcontent-%COMP%]   .filter-col[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n}\\n\\n.send-classroom-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: bold;\\n}\\n\\n.send-classroom-user[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  margin-left: 10px;\\n  display: flex;\\n}\\n\\n.send-button[_ngcontent-%COMP%] {\\n  background-color: var(--main-color);\\n  width: 100%;\\n  position: sticky;\\n  bottom: 0;\\n  left: 0;\\n  padding: 8px;\\n  box-sizing: border-box;\\n  color: white;\\n  text-align: center;\\n  border-radius: 12px;\\n  cursor: pointer;\\n}\\n\\n.notes-height[_ngcontent-%COMP%] {\\n  height: calc(100% - 50px);\\n}\\n\\n.btns[_ngcontent-%COMP%]   div[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n[_nghost-%COMP%]     .ngx-pagination {\\n  padding: 0 !important;\\n}\\n\\n.filter-blue[_ngcontent-%COMP%] {\\n  opacity: 0.7;\\n}\\n\\n.add[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n}\\n.add.lg[_ngcontent-%COMP%] {\\n  width: 2.1rem;\\n  height: 2.1rem;\\n}\\n.add.empty[_ngcontent-%COMP%] {\\n  width: 7.1rem;\\n  height: auto;\\n}\\n\\n  .p-tabview .p-tabview-panels {\\n  background-color: transparent;\\n  padding: 0;\\n  justify-content: center;\\n  height: 100%;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n  .p-tabview .p-tabview-nav {\\n  background: transparent;\\n  border: none;\\n}\\n\\n  .p-tabview .p-tabview-nav li .p-tabview-nav-link,   .p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {\\n  background: transparent;\\n  border: none;\\n}\\n\\n  .p-tabview .p-tabview-nav .p-tabview-ink-bar {\\n  background-color: #8a7af7;\\n  bottom: 5px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["UntypedFormControl", "UntypedFormGroup", "Validators", "SubSink", "moment", "NoteListenersService", "NoteViewAccess", "LibraryFileActionsDialogComponent", "DialogService", "tap", "i0", "ɵɵelementStart", "ɵɵlistener", "NotesListComponent_img_9_Template_img_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "showAddNote", "ɵɵelementEnd", "NotesListComponent_div_11_div_1_Template_div_click_0_listener", "_r4", "onShareMultiple", "ɵɵelement", "ɵɵtext", "NotesListComponent_div_11_div_2_Template_div_click_0_listener", "_r5", "onUnShareMultiple", "NotesListComponent_div_11_div_8_Template_div_click_0_listener", "_r6", "onRemoveNoteMultiple", "ɵɵtemplate", "NotesListComponent_div_11_div_1_Template", "NotesListComponent_div_11_div_2_Template", "NotesListComponent_div_11_Template_div_click_3_listener", "_r3", "downloadMultiple", "NotesListComponent_div_11_div_8_Template", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction2", "_c1", "noteViewAccess", "TEACHER_VIEW_STUDENTS_CLASSROOM_NOTES", "STUDENT_VIEW_TEACHERS_CLASSROOM_NOTES", "includes", "currentNotesView", "TEACHER_VIEW_NOTES", "hasAdd", "NotesListComponent_ng_container_12_div_1_Template_app_note_details_closedNewNote_1_listener", "_r7", "onClosedNewNote", "classroom", "showGroupActions", "noteListenersService", "getNotesViewAccess", "withClassroom", "ɵɵelementContainerStart", "NotesListComponent_ng_container_12_div_1_Template", "classroomStudents", "length", "ɵɵelementContainer", "NotesListComponent_div_13_ng_container_1_ng_container_1_Template", "notesGeneralTemplate_r8", "_c2", "filteredNotes", "NotesListComponent_div_13_ng_container_2_ng_container_5_ng_container_2_Template", "selectedStudent_r9", "firstName", "ɵɵpureFunction3", "_c3", "NotesListComponent_div_13_ng_container_2_ng_container_4_Template", "NotesListComponent_div_13_ng_container_2_ng_container_5_Template", "NotesListComponent_div_13_ng_template_3_ng_container_0_div_1_Template_app_note_details_closedNewNote_1_listener", "_r10", "classroom_r11", "NotesListComponent_div_13_ng_template_3_ng_container_0_div_1_Template", "NotesListComponent_div_13_ng_template_3_div_1_Template_app_note_details_noteChecked_1_listener", "$event", "_r12", "onNoteChecked", "note_r13", "NotesListComponent_div_13_ng_template_3_div_3_div_1_Template_img_click_1_listener", "_r14", "NotesListComponent_div_13_ng_template_3_div_3_ng_container_2_ng_container_4_Template", "NotesListComponent_div_13_ng_template_3_div_3_ng_container_2_ng_container_5_Template", "authService", "isStudent", "<PERSON><PERSON><PERSON>er", "NotesListComponent_div_13_ng_template_3_div_3_div_1_Template", "NotesListComponent_div_13_ng_template_3_div_3_ng_container_2_Template", "getFilteredNotes", "student_r15", "NotesListComponent_div_13_ng_template_3_ng_container_0_Template", "NotesListComponent_div_13_ng_template_3_div_1_Template", "NotesListComponent_div_13_ng_template_3_div_3_Template", "TEACHER_VIEW_CLASSROOM_NOTES", "ɵɵpipeBind2", "ɵɵpureFunction1", "_c4", "term", "filteredNotes_r16", "NotesListComponent_div_13_ng_container_1_Template", "NotesListComponent_div_13_ng_container_2_Template", "NotesListComponent_div_13_ng_template_3_Template", "ɵɵtemplateRefExtractor", "NotesListComponent_div_13_div_5_Template", "isLoading", "NotesListComponent", "constructor", "generalService", "noteService", "toastService", "classroomService", "cd", "noteActionsService", "dialogService", "confirmDialogService", "notes", "viewTitle", "subs", "showFilters", "tryToSave", "showTextError", "isSharedFilter", "isNotSharedFilter", "isPinnedFilter", "isFavoritedFilter", "fromFilterValue", "toFilterValue", "datePickerOpen", "classrooms", "showSend", "showSendAndShare", "usersToSend", "currentSelectedClassroomId", "showingMine", "inClassroom", "textEditor", "checkedNotes", "user", "role", "maxSize", "directionLinks", "autoHide", "to", "from", "form", "isInMine", "ngOnInit", "getLoggedInUser", "initClassroomListener", "title", "validators", "required", "text", "ngOnChanges", "ngAfterViewInit", "detectChanges", "ngOnDestroy", "unsubscribe", "setGroupActions", "ngAfterContentInit", "setTimeout", "targetStudent", "filter", "note", "sharedWithUsers", "some", "aspUserId", "created<PERSON>y", "areNotFavorited", "favourited<PERSON>y", "id", "getUserId", "areNotPinned", "pinned", "getNotesFiltered", "value", "created", "getTime", "add", "toDate", "push", "ifFieldValid", "field", "get", "invalid", "touched", "isTextValid", "close", "event", "console", "log", "checked", "el", "validateCheckedNotes", "handleTeacherShareMultiple", "handleStudentShareMultiple", "for<PERSON>ach", "unshareWith", "map", "sendForUnshare", "noteId", "classroomId", "Number", "studentIds", "getUserIdsForUnshare", "unShareNote", "download", "initConfirmDeleteNoteDialogListener", "setProperties", "confirmMessage", "acceptBtnLabel", "rejectBtnLabel", "action", "confirmIcon", "showHeader", "dialogType", "headerClass", "show", "isMyNote", "teacher", "shareNotesMultipleWithClassroom", "openShareMultipleWithNoteDialog", "sendForShare", "shareNote", "openDialogWithComponent", "dialogsubTitle", "dialogTitle", "preselectedClassroom", "result", "isNullishObject", "sendOrShare", "confirmDeleteNoteSubscription", "acceptSelected$", "pipe", "res", "hide", "onRemoveNoteMultipleAccepted", "subscribe", "afterAcceptRemoveNote", "msg", "setShowToastmessage", "severity", "summary", "detail", "currentSelectedClassroom", "_", "ɵɵdirectiveInject", "i1", "GeneralService", "i2", "NoteService", "i3", "ToastService", "i4", "ClassroomService", "ChangeDetectorRef", "i5", "AuthService", "i6", "i7", "NoteActionsService", "i8", "i9", "ConfirmDialogService", "_2", "selectors", "inputs", "features", "ɵɵProvidersFeature", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "NotesListComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "NotesListComponent_Template_input_ngModelChange_6_listener", "ɵɵtwoWayBindingSet", "NotesListComponent_Template_img_click_8_listener", "NotesListComponent_img_9_Template", "NotesListComponent_div_11_Template", "NotesListComponent_ng_container_12_Template", "NotesListComponent_div_13_Template", "ɵɵtextInterpolate1", "ɵɵtwoWayProperty", "_c0", "STUDENT_VIEW_CLASSROOM_NOTES"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\note\\notes\\notes-list\\notes-list.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\note\\notes\\notes-list\\notes-list.component.html"], "sourcesContent": ["/* The `NotesListComponent` is a component in an Angular application that displays a list of notes and\r\nprovides functionality for filtering, sorting, and performing actions on the notes. */\r\nimport { ChangeDetectorRef, Component, Input, OnInit, ViewChild } from '@angular/core';\r\nimport { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\r\nimport { Note, SendShareNoteRequest } from 'src/app/core/models/note.model';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { NoteService } from 'src/app/core/services/note.service';\r\nimport { Classroom, ClassroomType } from 'src/app/core/models/classroom.model';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { User } from 'src/app/core/models/user.model';\r\nimport { SubSink } from 'subsink';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport * as moment from 'moment';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { NoteActionsService } from 'src/app/core/services/note-actions.service';\r\nimport { NoteListenersService, NoteViewAccess } from 'src/app/core/services/note-listeners.service';\r\nimport { LibraryFileActionsDialogComponent } from 'src/app/modules/library/library/components/library-file-actions-dialog/library-file-actions-dialog.component';\r\nimport { DialogService } from 'primeng/dynamicdialog';\r\nimport { ConfirmDialogService } from 'src/app/core/services/confirm-dialog.service';\r\nimport { take, tap } from 'rxjs/operators';\r\nimport { Subscription } from 'rxjs';\r\n\r\ninterface SendShareEvent {\r\n  usersSelected: User[];\r\n  kind: string;\r\n  userClassroomMap: Map<User[], number>;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-notes-list',\r\n  templateUrl: './notes-list.component.html',\r\n  styleUrls: ['./notes-list.component.scss'],\r\n  providers: [DialogService, NoteListenersService]\r\n\r\n})\r\nexport class NotesListComponent implements OnInit {\r\n  @Input() notes: Note[] = [];\r\n  @Input() viewTitle = 'My Notes';\r\n  @Input() withClassroom = false;\r\n  @Input() hasAdd = true;\r\n  @Input() classroom: Classroom = {} as Classroom;\r\n  private subs = new SubSink();\r\n  showFilters: boolean = false;\r\n  showGroupActions: boolean = true;\r\n  term: any;\r\n  sendTerm: any;\r\n  showAddNote: boolean = false;\r\n  tryToSave: boolean = false;\r\n  showTextError: boolean = false;\r\n  isSharedFilter: boolean = false;\r\n  isNotSharedFilter: boolean = false;\r\n  isPinnedFilter: boolean = false;\r\n  isFavoritedFilter: boolean = false;\r\n  fromFilterValue: Date = {} as Date;\r\n  toFilterValue: Date = {} as Date;\r\n  datePickerOpen: boolean = false;\r\n  filteredNotes: Note[] = [];\r\n  classrooms: Classroom[] = [];\r\n  showSend: boolean = false;\r\n  showSendAndShare: boolean = false;\r\n  usersToSend: User[] = [];\r\n  currentSelectedClassroomId: number = 0;\r\n  showingMine: boolean = true;\r\n  inClassroom: boolean = false;\r\n  isLoading: boolean = false;\r\n  textEditor = '';\r\n  checkedNotes: Note[] = [];\r\n  classroomStudents: User[] = [];\r\n  public user: User = {} as User;\r\n  public role: string = \"\";\r\n  public maxSize: number = 6;\r\n  public directionLinks: boolean = true;\r\n  public autoHide: boolean = false;\r\n  public to: any = {} as any;\r\n  public from: any = {} as any;\r\n  public form: UntypedFormGroup = new UntypedFormGroup({});\r\n  isInMine: boolean = false;\r\n  public noteViewAccess = NoteViewAccess;\r\n  confirmDeleteNoteSubscription!: Subscription;\r\n\r\n  constructor(\r\n    private generalService: GeneralService,\r\n    private noteService: NoteService,\r\n    private toastService: ToastService,\r\n    private classroomService: ClassroomService,\r\n    private cd: ChangeDetectorRef,\r\n    private authService: AuthService,\r\n    public noteListenersService: NoteListenersService,\r\n    public noteActionsService: NoteActionsService,\r\n    public dialogService: DialogService,\r\n    public confirmDialogService: ConfirmDialogService,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.user = this.authService.getLoggedInUser();\r\n    this.role = this.user.role!\r\n    this.initClassroomListener();\r\n    // this.notSharedFilter(true);\r\n    this.form = new UntypedFormGroup({\r\n      title: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      text: new UntypedFormControl(null, {\r\n        validators: [Validators.required]\r\n      }),\r\n    });\r\n  }\r\n\r\n  ngOnChanges() {\r\n    if (this.classroom) {\r\n      this.classroomStudents = this.classroom.classroomStudents;\r\n    }\r\n    this.filteredNotes = this.notes\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    this.showGroupActions = false;\r\n    this.cd.detectChanges()\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n    this.noteListenersService.setGroupActions(false);\r\n    this.noteService.checkedNotes = [];\r\n  }\r\n\r\n  ngAfterContentInit() {\r\n  }\r\n\r\n  get currentNotesView() {\r\n    return this.noteListenersService.getNotesViewAccess(this.hasAdd, this.withClassroom);\r\n  }\r\n\r\n  //listener to change ui in nite details\r\n  setGroupActions() {\r\n    this.showGroupActions = !this.showGroupActions;\r\n    if (!this.showGroupActions) {\r\n      this.noteService.checkedNotes = [];\r\n    }\r\n    setTimeout(() => {\r\n      this.noteListenersService.setGroupActions(this.showGroupActions);\r\n    }, 300);\r\n  }\r\n\r\n  /**\r\n   * Filters the notes based on whether they are shared with the given student.\r\n   *\r\n   * @param targetStudent - The student user to filter by.\r\n   * @returns An array of notes that are shared with the given student.\r\n   */\r\n  getFilteredNotes(targetStudent: User): Note[] {\r\n    if (this.hasAdd) {\r\n    return this.filteredNotes.filter((note: Note) => {\r\n      return targetStudent\r\n        ? note.sharedWithUsers.some(\r\n          (user: User) => user.aspUserId === targetStudent.aspUserId\r\n        )\r\n        : true;\r\n    });\r\n    } else {\r\n      return this.filteredNotes.filter((note: Note) => {\r\n        return targetStudent\r\n          ? note.createdBy.aspUserId === targetStudent.aspUserId : true;\r\n      });\r\n    }\r\n\r\n  }\r\n\r\n  areNotFavorited() {\r\n    for (let note of this.noteService.checkedNotes) {\r\n      if (note.favouritedBy.filter((user: User) => user.id == this.authService.getUserId()).length == 0) return true;\r\n    }\r\n    return false\r\n  }\r\n\r\n  areNotPinned() {\r\n    for (let note of this.noteService.checkedNotes) {\r\n      if (!note.pinned) return true;\r\n    }\r\n    return false\r\n  }\r\n\r\n\r\n  /**\r\n   * Filters the notes based on the selected filters and returns the filtered notes.\r\n   *\r\n   * @return {Note[]} An array of Note objects that pass the applied filters.\r\n   */\r\n  getNotesFiltered() {\r\n    let filteredNotes: Note[] = [];\r\n    for (let note of this.notes) {\r\n      if (this.isPinnedFilter) {\r\n        if (!note.pinned) {\r\n          continue;\r\n        }\r\n      }\r\n      if (this.isFavoritedFilter) {\r\n        if (note.favouritedBy.filter((user: User) => user.aspUserId == this.authService.getUserId()).length == 0) {\r\n          continue;\r\n        }\r\n      }\r\n      //get shared \r\n      if (this.from.value) {\r\n        if (!(note.created.getTime() > this.from.value.getTime())) {\r\n          continue;\r\n        }\r\n      }\r\n      if (this.to.value) {\r\n        if (!(note.created.getTime() < moment(this.to.value).add('1', 'd').toDate().getTime())) {\r\n          continue;\r\n        }\r\n      }\r\n      if (this.isSharedFilter) {\r\n        if (note.sharedWithUsers.length == 0) {\r\n          continue\r\n        }\r\n      }\r\n      if (this.isNotSharedFilter) {\r\n        if (note.sharedWithUsers.length > 0) {\r\n          continue\r\n        }\r\n      }\r\n      filteredNotes.push(note)\r\n    }\r\n    return filteredNotes\r\n  }\r\n\r\n  ifFieldValid(field: string) {\r\n    return ((this.form.get(field)?.invalid && this.form.get(field)?.touched) || (this.tryToSave && this.form.get(field)?.invalid))\r\n  }\r\n\r\n  isTextValid() {\r\n    this.showTextError = !this.textEditor && this.textEditor === '' ? true : false\r\n  }\r\n\r\n  close(event: any) {\r\n    this.showSend = false\r\n    this.showSendAndShare = false\r\n  }\r\n\r\n  onClosedNewNote() {\r\n    console.log('he');\r\n    this.showAddNote = false;\r\n  }\r\n\r\n  onNoteChecked(event: any) {\r\n    console.log(event);\r\n    if (event.checked) {\r\n      this.noteService.checkedNotes.push(event.note);\r\n    } else {\r\n      this.noteService.checkedNotes = this.noteService.checkedNotes.filter(el => el.id !== event.note.id);\r\n    }\r\n  }\r\n\r\n  onShareMultiple() {\r\n    const checkedNotes = this.noteService.checkedNotes;\r\n      \r\n    if (checkedNotes.length === 0) {\r\n      this.validateCheckedNotes('Please select at least one note.');\r\n      return;\r\n    }\r\n      \r\n    const isTeacher = this.authService.isTeacher;\r\n    \r\n    if (isTeacher) {\r\n      this.handleTeacherShareMultiple(checkedNotes);\r\n    } else {\r\n      this.handleStudentShareMultiple(checkedNotes);\r\n    }\r\n  }\r\n\r\n  onUnShareMultiple() {\r\n    if (this.noteService.checkedNotes.length === 0) {\r\n        this.validateCheckedNotes('Please select at least one note.');\r\n        return;\r\n    }\r\n\r\n    this.noteService.checkedNotes.forEach(note => {\r\n        const unshareWith: any[] = note.sharedWithUsers.map(user => user.aspUserId);\r\n        \r\n        const sendForUnshare: SendShareNoteRequest = {\r\n            noteId: note.id,\r\n            classroomId: Number(this.classroom.id),\r\n            studentIds: this.getUserIdsForUnshare(unshareWith, note)\r\n        };\r\n        this.noteActionsService.unShareNote(sendForUnshare, 'Notes unshared successfully');\r\n    });\r\n  \r\n    this.setGroupActions();\r\n}\r\n  \r\n\r\n  downloadMultiple() {\r\n    if (this.noteService.checkedNotes.length === 0) {\r\n      this.validateCheckedNotes('Please select at least one note.');\r\n      return;\r\n    }\r\n    for (let note of this.noteService.checkedNotes) {\r\n      this.noteActionsService.download(note);\r\n    }\r\n  }\r\n\r\n  onRemoveNoteMultiple() {\r\n    this.initConfirmDeleteNoteDialogListener();\r\n    if (this.noteService.checkedNotes.length === 0) {\r\n      this.validateCheckedNotes('Please select at least one note.');\r\n      return;\r\n    }\r\n    this.confirmDialogService.setProperties({\r\n      confirmMessage: `\r\n      Are you sure you want to delete the selected notes?`,\r\n      acceptBtnLabel: 'Yes',\r\n      rejectBtnLabel: 'No',\r\n      action: 'delete-note',\r\n      confirmIcon: '/assets/icons/clock-dialog-icon.svg',\r\n      showHeader: true,\r\n      dialogType: 'custom',\r\n      headerClass: 'my-custom-header-class'\r\n    });\r\n    this.confirmDialogService.show();\r\n\r\n  }\r\n\r\n  private getUserIdsForUnshare(unshareWith: any[], note: any): any[] {\r\n    if (this.authService.isTeacher) {\r\n      return unshareWith;\r\n    } else if (this.noteService.isMyNote(note)) {\r\n      return [this.classroom.teacher!.aspUserId!];\r\n    }\r\n    return [this.user.aspUserId!];\r\n  }\r\n\r\n  private handleTeacherShareMultiple(checkedNotes: Note[]) {\r\n    if (![this.noteViewAccess.TEACHER_VIEW_STUDENTS_CLASSROOM_NOTES, this.noteViewAccess.TEACHER_VIEW_NOTES].includes(this.currentNotesView)) {\r\n  \r\n      if (this.classroom.classroomStudents.length === 1) {\r\n        this.shareNotesMultipleWithClassroom(checkedNotes);\r\n      } else {\r\n        this.openShareMultipleWithNoteDialog(checkedNotes);\r\n      }\r\n  \r\n    } else {\r\n      this.openShareMultipleWithNoteDialog(checkedNotes);\r\n    }\r\n  }\r\n  \r\n  private handleStudentShareMultiple(checkedNotes: Note[]) {\r\n    const unshareWith: any[] = [];\r\n      \r\n    for (let note of checkedNotes) {\r\n      for (let user of note.sharedWithUsers) {\r\n        unshareWith.push(user.aspUserId);\r\n      }\r\n      \r\n      const sendForShare: SendShareNoteRequest = {\r\n        noteId: note.id,\r\n        classroomId: +this.classroom.id,\r\n        studentIds: [this.classroom.teacher!.aspUserId!]\r\n      };\r\n      this.noteActionsService.shareNote(sendForShare, 'Notes unshared successfully');\r\n    }\r\n      \r\n    this.setGroupActions();\r\n  }\r\n  \r\n  private shareNotesMultipleWithClassroom(checkedNotes: Note[]) {\r\n    for (let note of checkedNotes) {\r\n      const sendForShare: SendShareNoteRequest = {\r\n          noteId: note.id,\r\n          classroomId: +this.classroom.id,\r\n          studentIds: [this.classroom.classroomStudents[0].aspUserId!]\r\n       };\r\n      this.noteActionsService.shareNote(sendForShare, 'Notes shared successfully');\r\n    }\r\n  }\r\n  \r\n  private openShareMultipleWithNoteDialog(checkedNotes: Note[]) {\r\n    this.generalService.openDialogWithComponent(this.dialogService, LibraryFileActionsDialogComponent, 440,\r\n      {\r\n        action: 'shareWith',\r\n        dialogsubTitle: '',\r\n        dialogTitle: 'Share Note with...',\r\n        preselectedClassroom: this.classroom,\r\n      }, null, (result: any) => {\r\n      console.log(result);\r\n      if (result && result.action === 'shareWith') {\r\n        if (!this.generalService.isNullishObject(result)) {\r\n  \r\n          for (let note of checkedNotes) {\r\n            this.noteActionsService.sendOrShare(note, result, this.authService.isStudent);\r\n          }\r\n          this.setGroupActions();\r\n          console.log(result);\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  private initConfirmDeleteNoteDialogListener() {\r\n    this.confirmDeleteNoteSubscription = (this.confirmDialogService.acceptSelected$\r\n      .pipe(tap((res) => {\r\n\r\n        this.confirmDialogService.hide();\r\n        console.log(res);\r\n\r\n        switch (res.action) {\r\n          case 'delete-note':\r\n            console.log('deltenetote')\r\n            this.onRemoveNoteMultipleAccepted();\r\n        }\r\n      })).subscribe());\r\n\r\n    this.subs.add(this.confirmDeleteNoteSubscription);\r\n  }\r\n\r\n  private onRemoveNoteMultipleAccepted() {\r\n    console.log(this.noteService.checkedNotes);\r\n    for (let note of this.noteService.checkedNotes) {\r\n      console.log(note);\r\n      this.noteActionsService.afterAcceptRemoveNote(note);\r\n    }\r\n    this.confirmDeleteNoteSubscription.unsubscribe();\r\n    this.setGroupActions();\r\n  }\r\n\r\n  private validateCheckedNotes(msg: string) {\r\n    if (this.noteService.checkedNotes.length === 0) {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'warn',\r\n        summary: '',\r\n        detail: msg \r\n      });\r\n      return;\r\n    }\r\n  }\r\n\r\n  private initClassroomListener() {\r\n    this.subs.add(this.noteListenersService.currentSelectedClassroom.subscribe(res => {\r\n      if (this.generalService.isNullishObject(res)) {\r\n        return;\r\n      }\r\n      console.log(this.filteredNotes);\r\n      this.classroom = res;\r\n      this.classroomStudents = this.classroom.classroomStudents;\r\n    }));\r\n  }\r\n}\r\n\r\n\r\n", "<div class=\"notes-header\">\r\n  <div class=\"notes-header-title\">\r\n    {{viewTitle}}\r\n  </div>\r\n  <!-- /** * Header section for notes list view. * Contains UI elements for\r\n  searching, filtering, and adding notes. */ -->\r\n  <div class=\"notes-header-actions\">\r\n    <!-- TODO: Add notes filtering -->\r\n    <!-- <div id=\"filters-content\">\r\n      <div (click)=\"showFilters = !showFilters\"></div>\r\n      <button pButton (click)=\"showFilters = !showFilters\" type=\"button\" label=\"\" class=\"p-button-text\"\r\n        styleClass=\"min-w-6rem\" style=\"min-width: 6rem\">\r\n        <span class=\"p-button-icon p-button-icon-right\" aria-hidden=\"true\"></span>\r\n        <span class=\"p-button-label mr-2\">Filters</span>\r\n        <img class=\"contact-icon-img\" src=\"/assets/icons/notifications/notif-filter.svg\" />\r\n      </button>\r\n      <div *ngIf=\"showFilters\" id=\"notes-filters\" class=\"notes-popup\">\r\n        <div class=\"popup-title p-10\">\r\n          <div>Choose Filters</div>\r\n          <img (click)=\"showFilters = false\" src=\"/assets/icons/close.png\" class=\"close-img close-img-abs hvr-glow\" />\r\n        </div>\r\n        <div class=\"notes-filters-filters p-0-10\">\r\n          <div class=\"filter-row\">\r\n            <ejs-checkbox label=\"\" (change)=\"sharedFilter($event)\" [checked]=\"isSharedFilter\">\r\n            </ejs-checkbox>\r\n            <div class=\"filter\">Shared</div>\r\n          </div>\r\n          <div class=\"filter-row\">\r\n            <ejs-checkbox label=\"\" (change)=\"notSharedFilter($event)\" [checked]=\"isNotSharedFilter\">\r\n            </ejs-checkbox>\r\n            <div class=\"filter\">Not Shared</div>\r\n          </div>\r\n          <div class=\"filter-row\">\r\n            <ejs-checkbox label=\"\" (change)=\"pinnedFilter($event)\" [checked]=\"isPinnedFilter\">\r\n            </ejs-checkbox>\r\n            <div class=\"filter\">Pinned</div>\r\n          </div>\r\n          <div class=\"filter-row\">\r\n            <ejs-checkbox label=\"\" (change)=\"favoritedFilter($event)\" [checked]=\"isFavoritedFilter\">\r\n            </ejs-checkbox>\r\n            <div class=\"filter\">Favorited</div>\r\n          </div>\r\n          <div class=\"filter-col\">\r\n            <div class=\"filter\">From</div>\r\n            <ejs-datepicker (click)=\"datePickerOpen = true\" format=\"dd-MM-yyyy\" #from (change)=\"fromFilter()\"\r\n              [value]=\"fromFilterValue\"></ejs-datepicker>\r\n          </div>\r\n          <div class=\"filter-col\">\r\n            <div class=\"filter\">To</div>\r\n            <ejs-datepicker (click)=\"datePickerOpen = true\" format=\"dd-MM-yyyy\" #to (change)=\"toFilter()\"\r\n              [value]=\"toFilterValue\"></ejs-datepicker>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div> -->\r\n    <div class=\"notes-header-actions gap-2\">\r\n      <span class=\"p-input-icon-right w-full\">\r\n        <input type=\"text\" [(ngModel)]=\"term\" pInputText placeholder=\"Search\"\r\n          class=\"h-2rem w-full input-blue gradient-blue rounded\" />\r\n        <i class=\"pi pi-search text-primary\"></i>\r\n      </span>\r\n      <img src=\"/assets/icons/library/group-actions.svg\" [ngClass]=\"{'filter-blue': showGroupActions}\" \r\n      title=\"Group Actions\"\r\n      (click)=\"setGroupActions()\" class=\"hvr-grow\" />\r\n      <img *ngIf=\"hasAdd\" title=\"Create Note\" src=\"/assets/icons/library/add-lib.svg\" class=\"add hvr-grow add-note-btn-icon\"\r\n        (click)=\"showAddNote = !showAddNote\" />\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n\r\n<div class=\"flex align-items-center justify-content-center\">\r\n  <div *ngIf=\"showGroupActions\" class=\"note-menu-row block-gradient border-round-xl font-sm my-2 p-1 px-3 gap-2\">\r\n    <!--  (click)=\"showShare = !showShare\" -->\r\n    <div *ngIf=\"![noteViewAccess.TEACHER_VIEW_STUDENTS_CLASSROOM_NOTES, noteViewAccess.STUDENT_VIEW_TEACHERS_CLASSROOM_NOTES].includes(currentNotesView)\"\r\n    class=\"note-menu-col\" (click)=\"onShareMultiple()\">\r\n      <div class=\"note-menu-icon\">\r\n        <img src=\"/assets/icons/library/share-sm-icon.svg\" height=\"14\">\r\n      </div>\r\n      <div class=\"note-menu-text link-main-color\">\r\n        Share\r\n      </div>\r\n      <!-- <div class=\"note-menu-icon\" style=\"margin-left:auto\">\r\n              <img src=\"/assets/icons/arrow-note.svg\">\r\n          </div> -->\r\n    </div>\r\n    <div\r\n      *ngIf=\"\r\n      (currentNotesView !== noteViewAccess.TEACHER_VIEW_NOTES) && (currentNotesView !== noteViewAccess.STUDENT_VIEW_TEACHERS_CLASSROOM_NOTES)\"\r\n      class=\"note-menu-col\" (click)=\"onUnShareMultiple()\">\r\n      <div class=\"note-menu-icon\">\r\n        <img src=\"/assets/icons/library/share-sm-icon.svg\" height=\"14\">\r\n      </div>\r\n      <div class=\"note-menu-text link-main-color\">\r\n        Unshare\r\n      </div>\r\n    </div>\r\n    <div class=\"note-menu-col\" (click)=\"downloadMultiple()\">\r\n      <div class=\"note-menu-icon\">\r\n        <img src=\"/assets/icons/cloud-download.svg\" height=\"14\">\r\n      </div>\r\n      <div class=\"note-menu-text link-main-color\">\r\n        Download\r\n      </div>\r\n    </div>\r\n    <div *ngIf=\"hasAdd\" class=\"note-menu-col\" (click)=\"onRemoveNoteMultiple()\">\r\n      <div class=\"note-menu-icon\">\r\n        <img src=\"/assets/icons/library/delete.svg\" height=\"14\">\r\n      </div>\r\n      <div class=\"note-menu-text link-main-color\">\r\n        Delete\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n<!-- /**\r\n* logic to display the note details component based on the currentNotesView and showAddNote variables.\r\n* If the currentNotesView is either TEACHER_VIEW_CLASSROOM_NOTES, STUDENT_VIEW_CLASSROOM_NOTES, or TEACHER_VIEW_NOTES and showAddNote is true,\r\n* then it checks if there is only one classroom student or if the user is a teacher. \r\n* then can render the create new note\r\n*/ -->\r\n\r\n<ng-container *ngIf=\"\r\n    (currentNotesView === noteViewAccess.TEACHER_VIEW_CLASSROOM_NOTES ||\r\n      currentNotesView === noteViewAccess.STUDENT_VIEW_CLASSROOM_NOTES ||\r\n      currentNotesView === noteViewAccess.TEACHER_VIEW_NOTES) &&\r\n    showAddNote\">\r\n  <div *ngIf=\"\r\n      ((classroomStudents && classroomStudents.length === 1) ||\r\n      !withClassroom) && hasAdd\">\r\n    <app-note-details [note]=\"\" [type]=\"'create'\" [classroom]=\"classroom\"\r\n    [showGroupActions]=\"showGroupActions\"\r\n    [view]=\"noteListenersService.getNotesViewAccess(hasAdd, withClassroom)\"\r\n      (closedNewNote)=\"onClosedNewNote()\"></app-note-details>\r\n  </div>\r\n</ng-container>\r\n\r\n<div *ngIf=\"!isLoading\" class=\"notes-height\">\r\n\r\n  <ng-container  *ngIf=\"(classroomStudents && classroomStudents.length === 1) || !withClassroom || authService.isStudent\">\r\n    <ng-container\r\n      *ngTemplateOutlet=\"notesGeneralTemplate; context: { classroom: classroom, filteredNotes: filteredNotes }\">\r\n    </ng-container>\r\n  </ng-container>\r\n\r\n  <ng-container *ngIf=\"withClassroom && authService.isTeacher && (classroomStudents && classroomStudents.length > 1)\">\r\n    <p-tabView [scrollable]=\"classroomStudents.length > 4\" class=\"w-full notes-height\">\r\n      <p-tabPanel header=\"All\">\r\n        <div id=\"classroom-info\" class=\"h-full \">\r\n          <ng-container\r\n            *ngTemplateOutlet=\"notesGeneralTemplate; context: { classroom: classroom, filteredNotes: filteredNotes }\">\r\n          </ng-container>\r\n\r\n        </div>\r\n      </p-tabPanel>\r\n      <ng-container *ngFor=\"let selectedStudent of classroomStudents; index as i\">\r\n        <p-tabPanel [header]=\"selectedStudent.firstName\" class=\"\">\r\n          <ng-container\r\n            *ngTemplateOutlet=\"notesGeneralTemplate; context: { classroom: classroom, filteredNotes: filteredNotes, student: selectedStudent }\">\r\n          </ng-container>\r\n        </p-tabPanel>\r\n      </ng-container>\r\n    </p-tabView>\r\n  </ng-container>\r\n\r\n  <ng-template #notesGeneralTemplate let-classroom=\"classroom\" let-student=\"student\" let-filteredNotes=\"filteredNotes\">\r\n\r\n<ng-container *ngIf=\"currentNotesView === noteViewAccess.TEACHER_VIEW_CLASSROOM_NOTES && showAddNote\">\r\n<div *ngIf=\"classroomStudents && classroomStudents.length > 1\">\r\n  <app-note-details [note]=\"\" [type]=\"'create'\" [classroom]=\"classroom\"\r\n  [showGroupActions]=\"showGroupActions\"\r\n  [view]=\"noteListenersService.getNotesViewAccess(hasAdd, withClassroom)\"\r\n    (closedNewNote)=\"onClosedNewNote()\"></app-note-details>\r\n</div>\r\n</ng-container>\r\n<!--  | filter: term -->\r\n    <div *ngFor=\"let note of getFilteredNotes(student) | filter: { term: term }\">\r\n      <app-note-details [isInMine]=\"hasAdd\" [note]=\"note\" [classroom]=\"classroom\" \r\n      [withClassroom]=\"withClassroom\"\r\n      [showGroupActions]=\"showGroupActions\"\r\n      [view]=\"noteListenersService.getNotesViewAccess(hasAdd, withClassroom)\"\r\n        (noteChecked)=\"onNoteChecked($event)\"></app-note-details>\r\n    </div>\r\n    <div class=\"asked-for-help-main scrollable h-full notes-height\" *ngIf=\"filteredNotes && getFilteredNotes(student).length == 0 && !showAddNote\"\r\n      [fromRoot]=\"true\" [scrollWindow]=\"true\">\r\n\r\n  <div class=\"flex flex-column gap-1 align-items-center justify-content-center h-full\" *ngIf=\"(hasAdd || getFilteredNotes(student).length == 0) && (getFilteredNotes(student).length == 0 && !student) && hasAdd\">\r\n        <img src=\"/assets/icons/library/add-lib.svg\" (click)=\"showAddNote = !showAddNote\" class=\"add lg hvr-grow\">\r\n        <p class=\"font-base max-w-14rem text-center\">\r\n            <!-- There are currently no files {{withClassroom ? 'in this classroom' : ''}}. -->\r\n            Add your first note!\r\n        </p>\r\n    </div>\r\n\r\n  <ng-container *ngIf=\"!hasAdd || (getFilteredNotes(student).length === 0 && student)\">\r\n      <div class=\"flex flex-column gap-1 align-items-center justify-content-center h-full\">\r\n          <img src=\"/assets/icons/library/file_manager_emp.png\" alt=\"file manager empty icon\" class=\"add empty hvr-grow\">\r\n          <p class=\"font-base max-w-14rem text-center\">\r\n              <ng-container *ngIf=\"authService.isStudent\">\r\n                  No notes found.\r\n              </ng-container>\r\n              <ng-container *ngIf=\"authService.isTeacher\">\r\n                  No notes found.\r\n              </ng-container>\r\n          </p>\r\n      </div>\r\n  </ng-container>\r\n\r\n      <!-- <div class=\"no-data h-full\">\r\n        <img src=\"/assets/icons/notes-empty.svg\" class=\"max-w-5rem\">\r\n        <div class=\"title text-center\">\r\n          There are no notes for this section. <span *ngIf=\"role=='Teacher'\"> <br> You can press the + button to add\r\n            one.</span>\r\n        </div>\r\n      </div> -->\r\n    </div>\r\n\r\n  </ng-template>\r\n\r\n\r\n  <div *ngIf=\"isLoading\" style=\"width:100%;height:800px; display: flex; justify-content: center;\">\r\n    <mat-spinner class=\"spinner\" style=\"margin-top:200px;\"></mat-spinner>\r\n  </div>\r\n</div>"], "mappings": "AAGA,SAASA,kBAAkB,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,gBAAgB;AAOjF,SAASC,OAAO,QAAQ,SAAS;AAEjC,OAAO,KAAKC,MAAM,MAAM,QAAQ;AAGhC,SAASC,oBAAoB,EAAEC,cAAc,QAAQ,8CAA8C;AACnG,SAASC,iCAAiC,QAAQ,8GAA8G;AAChK,SAASC,aAAa,QAAQ,uBAAuB;AAErD,SAAeC,GAAG,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IC6CpCC,EAAA,CAAAC,cAAA,cACyC;IAAvCD,EAAA,CAAAE,UAAA,mBAAAC,uDAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,WAAA,IAAAH,MAAA,CAAAG,WAAA;IAAA,EAAoC;IADtCT,EAAA,CAAAU,YAAA,EACyC;;;;;;IAS3CV,EAAA,CAAAC,cAAA,cACkD;IAA5BD,EAAA,CAAAE,UAAA,mBAAAS,8DAAA;MAAAX,EAAA,CAAAI,aAAA,CAAAQ,GAAA;MAAA,MAAAN,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAO,eAAA,EAAiB;IAAA,EAAC;IAC/Cb,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAc,SAAA,cAA+D;IACjEd,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,cAA4C;IAC1CD,EAAA,CAAAe,MAAA,cACF;IAIFf,EAJE,CAAAU,YAAA,EAAM,EAIF;;;;;;IACNV,EAAA,CAAAC,cAAA,cAGsD;IAA9BD,EAAA,CAAAE,UAAA,mBAAAc,8DAAA;MAAAhB,EAAA,CAAAI,aAAA,CAAAa,GAAA;MAAA,MAAAX,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAY,iBAAA,EAAmB;IAAA,EAAC;IACnDlB,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAc,SAAA,cAA+D;IACjEd,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,cAA4C;IAC1CD,EAAA,CAAAe,MAAA,gBACF;IACFf,EADE,CAAAU,YAAA,EAAM,EACF;;;;;;IASNV,EAAA,CAAAC,cAAA,cAA2E;IAAjCD,EAAA,CAAAE,UAAA,mBAAAiB,8DAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAgB,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAe,oBAAA,EAAsB;IAAA,EAAC;IACxErB,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAc,SAAA,cAAwD;IAC1Dd,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,cAA4C;IAC1CD,EAAA,CAAAe,MAAA,eACF;IACFf,EADE,CAAAU,YAAA,EAAM,EACF;;;;;;IAxCRV,EAAA,CAAAC,cAAA,cAA+G;IAc7GD,EAZA,CAAAsB,UAAA,IAAAC,wCAAA,kBACkD,IAAAC,wCAAA,kBAcI;IAQtDxB,EAAA,CAAAC,cAAA,cAAwD;IAA7BD,EAAA,CAAAE,UAAA,mBAAAuB,wDAAA;MAAAzB,EAAA,CAAAI,aAAA,CAAAsB,GAAA;MAAA,MAAApB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqB,gBAAA,EAAkB;IAAA,EAAC;IACrD3B,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAc,SAAA,cAAwD;IAC1Dd,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,cAA4C;IAC1CD,EAAA,CAAAe,MAAA,iBACF;IACFf,EADE,CAAAU,YAAA,EAAM,EACF;IACNV,EAAA,CAAAsB,UAAA,IAAAM,wCAAA,kBAA2E;IAQ7E5B,EAAA,CAAAU,YAAA,EAAM;;;;IAvCEV,EAAA,CAAA6B,SAAA,EAA8I;IAA9I7B,EAAA,CAAA8B,UAAA,UAAA9B,EAAA,CAAA+B,eAAA,IAAAC,GAAA,EAAA1B,MAAA,CAAA2B,cAAA,CAAAC,qCAAA,EAAA5B,MAAA,CAAA2B,cAAA,CAAAE,qCAAA,EAAAC,QAAA,CAAA9B,MAAA,CAAA+B,gBAAA,EAA8I;IAajJrC,EAAA,CAAA6B,SAAA,EAEH;IAFG7B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAA+B,gBAAA,KAAA/B,MAAA,CAAA2B,cAAA,CAAAK,kBAAA,IAAAhC,MAAA,CAAA+B,gBAAA,KAAA/B,MAAA,CAAA2B,cAAA,CAAAE,qCAAA,CAEH;IAgBMnC,EAAA,CAAA6B,SAAA,GAAY;IAAZ7B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAiC,MAAA,CAAY;;;;;;IA0BlBvC,EAHF,CAAAC,cAAA,UAE+B,2BAIS;IAApCD,EAAA,CAAAE,UAAA,2BAAAsC,4FAAA;MAAAxC,EAAA,CAAAI,aAAA,CAAAqC,GAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAiBF,MAAA,CAAAoC,eAAA,EAAiB;IAAA,EAAC;IACvC1C,EADwC,CAAAU,YAAA,EAAmB,EACrD;;;;IAJwBV,EAAA,CAAA6B,SAAA,EAAiB;IAE7C7B,EAF4B,CAAA8B,UAAA,kBAAiB,cAAAxB,MAAA,CAAAqC,SAAA,CAAwB,qBAAArC,MAAA,CAAAsC,gBAAA,CAChC,SAAAtC,MAAA,CAAAuC,oBAAA,CAAAC,kBAAA,CAAAxC,MAAA,CAAAiC,MAAA,EAAAjC,MAAA,CAAAyC,aAAA,EACkC;;;;;IAV3E/C,EAAA,CAAAgD,uBAAA,GAIiB;IACfhD,EAAA,CAAAsB,UAAA,IAAA2B,iDAAA,kBAE+B;;;;;IAFzBjD,EAAA,CAAA6B,SAAA,EAGN;IAHM7B,EAAA,CAAA8B,UAAA,UAAAxB,MAAA,CAAA4C,iBAAA,IAAA5C,MAAA,CAAA4C,iBAAA,CAAAC,MAAA,WAAA7C,MAAA,CAAAyC,aAAA,KAAAzC,MAAA,CAAAiC,MAAA,CAGN;;;;;IAUEvC,EAAA,CAAAoD,kBAAA,GAEe;;;;;IAHjBpD,EAAA,CAAAgD,uBAAA,GAAwH;IACtHhD,EAAA,CAAAsB,UAAA,IAAA+B,gEAAA,2BAC4G;;;;;;;IAAzGrD,EAAA,CAAA6B,SAAA,EAAwC;IAAA7B,EAAxC,CAAA8B,UAAA,qBAAAwB,uBAAA,CAAwC,4BAAAtD,EAAA,CAAA+B,eAAA,IAAAwB,GAAA,EAAAjD,MAAA,CAAAqC,SAAA,EAAArC,MAAA,CAAAkD,aAAA,EAA+D;;;;;IAQpGxD,EAAA,CAAAoD,kBAAA,GAEe;;;;;IAMfpD,EAAA,CAAAoD,kBAAA,GAEe;;;;;IAJnBpD,EAAA,CAAAgD,uBAAA,GAA4E;IAC1EhD,EAAA,CAAAC,cAAA,qBAA0D;IACxDD,EAAA,CAAAsB,UAAA,IAAAmC,+EAAA,2BACsI;IAExIzD,EAAA,CAAAU,YAAA,EAAa;;;;;;;;IAJDV,EAAA,CAAA6B,SAAA,EAAoC;IAApC7B,EAAA,CAAA8B,UAAA,WAAA4B,kBAAA,CAAAC,SAAA,CAAoC;IAE3C3D,EAAA,CAAA6B,SAAA,EAAwC;IAAA7B,EAAxC,CAAA8B,UAAA,qBAAAwB,uBAAA,CAAwC,4BAAAtD,EAAA,CAAA4D,eAAA,IAAAC,GAAA,EAAAvD,MAAA,CAAAqC,SAAA,EAAArC,MAAA,CAAAkD,aAAA,EAAAE,kBAAA,EAAyF;;;;;IAb5I1D,EAAA,CAAAgD,uBAAA,GAAoH;IAG9GhD,EAFJ,CAAAC,cAAA,oBAAmF,qBACxD,cACkB;IACvCD,EAAA,CAAAsB,UAAA,IAAAwC,gEAAA,2BAC4G;IAIhH9D,EADE,CAAAU,YAAA,EAAM,EACK;IACbV,EAAA,CAAAsB,UAAA,IAAAyC,gEAAA,2BAA4E;IAO9E/D,EAAA,CAAAU,YAAA,EAAY;;;;;;;IAhBDV,EAAA,CAAA6B,SAAA,EAA2C;IAA3C7B,EAAA,CAAA8B,UAAA,eAAAxB,MAAA,CAAA4C,iBAAA,CAAAC,MAAA,KAA2C;IAI7CnD,EAAA,CAAA6B,SAAA,GAAwC;IAAA7B,EAAxC,CAAA8B,UAAA,qBAAAwB,uBAAA,CAAwC,4BAAAtD,EAAA,CAAA+B,eAAA,IAAAwB,GAAA,EAAAjD,MAAA,CAAAqC,SAAA,EAAArC,MAAA,CAAAkD,aAAA,EAA+D;IAKpExD,EAAA,CAAA6B,SAAA,EAAsB;IAAtB7B,EAAA,CAAA8B,UAAA,YAAAxB,MAAA,CAAA4C,iBAAA,CAAsB;;;;;;IAcpElD,EADF,CAAAC,cAAA,UAA+D,2BAIvB;IAApCD,EAAA,CAAAE,UAAA,2BAAA8D,gHAAA;MAAAhE,EAAA,CAAAI,aAAA,CAAA6D,IAAA;MAAA,MAAA3D,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAiBF,MAAA,CAAAoC,eAAA,EAAiB;IAAA,EAAC;IACvC1C,EADwC,CAAAU,YAAA,EAAmB,EACrD;;;;;IAJwBV,EAAA,CAAA6B,SAAA,EAAiB;IAE7C7B,EAF4B,CAAA8B,UAAA,kBAAiB,cAAAoC,aAAA,CAAwB,qBAAA5D,MAAA,CAAAsC,gBAAA,CAChC,SAAAtC,MAAA,CAAAuC,oBAAA,CAAAC,kBAAA,CAAAxC,MAAA,CAAAiC,MAAA,EAAAjC,MAAA,CAAAyC,aAAA,EACkC;;;;;IAJzE/C,EAAA,CAAAgD,uBAAA,GAAsG;IACtGhD,EAAA,CAAAsB,UAAA,IAAA6C,qEAAA,kBAA+D;;;;;IAAzDnE,EAAA,CAAA6B,SAAA,EAAuD;IAAvD7B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAA4C,iBAAA,IAAA5C,MAAA,CAAA4C,iBAAA,CAAAC,MAAA,KAAuD;;;;;;IASvDnD,EADF,CAAAC,cAAA,UAA6E,2BAKnC;IAAtCD,EAAA,CAAAE,UAAA,yBAAAkE,+FAAAC,MAAA;MAAArE,EAAA,CAAAI,aAAA,CAAAkE,IAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAeF,MAAA,CAAAiE,aAAA,CAAAF,MAAA,CAAqB;IAAA,EAAC;IACzCrE,EAD0C,CAAAU,YAAA,EAAmB,EACvD;;;;;;IALcV,EAAA,CAAA6B,SAAA,EAAmB;IAGrC7B,EAHkB,CAAA8B,UAAA,aAAAxB,MAAA,CAAAiC,MAAA,CAAmB,SAAAiC,QAAA,CAAc,cAAAN,aAAA,CAAwB,kBAAA5D,MAAA,CAAAyC,aAAA,CAC5C,qBAAAzC,MAAA,CAAAsC,gBAAA,CACM,SAAAtC,MAAA,CAAAuC,oBAAA,CAAAC,kBAAA,CAAAxC,MAAA,CAAAiC,MAAA,EAAAjC,MAAA,CAAAyC,aAAA,EACkC;;;;;;IAOrE/C,EADN,CAAAC,cAAA,cAAgN,cAChG;IAA7DD,EAAA,CAAAE,UAAA,mBAAAuE,kFAAA;MAAAzE,EAAA,CAAAI,aAAA,CAAAsE,IAAA;MAAA,MAAApE,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,WAAA,IAAAH,MAAA,CAAAG,WAAA;IAAA,EAAoC;IAAjFT,EAAA,CAAAU,YAAA,EAA0G;IAC1GV,EAAA,CAAAC,cAAA,YAA6C;IAEzCD,EAAA,CAAAe,MAAA,6BACJ;IACJf,EADI,CAAAU,YAAA,EAAI,EACF;;;;;IAMIV,EAAA,CAAAgD,uBAAA,GAA4C;IACxChD,EAAA,CAAAe,MAAA,wBACJ;;;;;;IACAf,EAAA,CAAAgD,uBAAA,GAA4C;IACxChD,EAAA,CAAAe,MAAA,wBACJ;;;;;;IATZf,EAAA,CAAAgD,uBAAA,GAAqF;IACjFhD,EAAA,CAAAC,cAAA,cAAqF;IACjFD,EAAA,CAAAc,SAAA,cAA+G;IAC/Gd,EAAA,CAAAC,cAAA,YAA6C;IAIzCD,EAHA,CAAAsB,UAAA,IAAAqD,oFAAA,2BAA4C,IAAAC,oFAAA,2BAGA;IAIpD5E,EADI,CAAAU,YAAA,EAAI,EACF;;;;;IAPiBV,EAAA,CAAA6B,SAAA,GAA2B;IAA3B7B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAuE,WAAA,CAAAC,SAAA,CAA2B;IAG3B9E,EAAA,CAAA6B,SAAA,EAA2B;IAA3B7B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAuE,WAAA,CAAAE,SAAA,CAA2B;;;;;IAlBpD/E,EAAA,CAAAC,cAAA,cAC0C;IAU5CD,EARA,CAAAsB,UAAA,IAAA0D,4DAAA,kBAAgN,IAAAC,qEAAA,2BAQ3H;IAqBnFjF,EAAA,CAAAU,YAAA,EAAM;;;;;IA/BcV,EAAlB,CAAA8B,UAAA,kBAAiB,sBAAsB;IAE2C9B,EAAA,CAAA6B,SAAA,EAAwH;IAAxH7B,EAAA,CAAA8B,UAAA,UAAAxB,MAAA,CAAAiC,MAAA,IAAAjC,MAAA,CAAA4E,gBAAA,CAAAC,WAAA,EAAAhC,MAAA,UAAA7C,MAAA,CAAA4E,gBAAA,CAAAC,WAAA,EAAAhC,MAAA,UAAAgC,WAAA,IAAA7E,MAAA,CAAAiC,MAAA,CAAwH;IAQ/LvC,EAAA,CAAA6B,SAAA,EAAoE;IAApE7B,EAAA,CAAA8B,UAAA,UAAAxB,MAAA,CAAAiC,MAAA,IAAAjC,MAAA,CAAA4E,gBAAA,CAAAC,WAAA,EAAAhC,MAAA,UAAAgC,WAAA,CAAoE;;;;;IAlBjFnF,EATJ,CAAAsB,UAAA,IAAA8D,+DAAA,2BAAsG,IAAAC,sDAAA,kBASrB;;IAO7ErF,EAAA,CAAAsB,UAAA,IAAAgE,sDAAA,kBAC0C;;;;;;IAjB/BtF,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAA+B,gBAAA,KAAA/B,MAAA,CAAA2B,cAAA,CAAAsD,4BAAA,IAAAjF,MAAA,CAAAG,WAAA,CAAqF;IAS1ET,EAAA,CAAA6B,SAAA,EAAqD;IAArD7B,EAAA,CAAA8B,UAAA,YAAA9B,EAAA,CAAAwF,WAAA,OAAAlF,MAAA,CAAA4E,gBAAA,CAAAC,WAAA,GAAAnF,EAAA,CAAAyF,eAAA,IAAAC,GAAA,EAAApF,MAAA,CAAAqF,IAAA,GAAqD;IAOV3F,EAAA,CAAA6B,SAAA,GAA4E;IAA5E7B,EAAA,CAAA8B,UAAA,SAAA8D,iBAAA,IAAAtF,MAAA,CAAA4E,gBAAA,CAAAC,WAAA,EAAAhC,MAAA,UAAA7C,MAAA,CAAAG,WAAA,CAA4E;;;;;IAqC/IT,EAAA,CAAAC,cAAA,cAAgG;IAC9FD,EAAA,CAAAc,SAAA,sBAAqE;IACvEd,EAAA,CAAAU,YAAA,EAAM;;;;;IArFRV,EAAA,CAAAC,cAAA,cAA6C;IAmF3CD,EAjFA,CAAAsB,UAAA,IAAAuE,iDAAA,2BAAwH,IAAAC,iDAAA,2BAMJ,IAAAC,gDAAA,gCAAA/F,EAAA,CAAAgG,sBAAA,CAoBC,IAAAC,wCAAA,kBAuDrB;IAGlGjG,EAAA,CAAAU,YAAA,EAAM;;;;IApFYV,EAAA,CAAA6B,SAAA,EAAsG;IAAtG7B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAA4C,iBAAA,IAAA5C,MAAA,CAAA4C,iBAAA,CAAAC,MAAA,WAAA7C,MAAA,CAAAyC,aAAA,IAAAzC,MAAA,CAAAuE,WAAA,CAAAC,SAAA,CAAsG;IAMvG9E,EAAA,CAAA6B,SAAA,EAAmG;IAAnG7B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAAyC,aAAA,IAAAzC,MAAA,CAAAuE,WAAA,CAAAE,SAAA,IAAAzE,MAAA,CAAA4C,iBAAA,IAAA5C,MAAA,CAAA4C,iBAAA,CAAAC,MAAA,KAAmG;IA2E5GnD,EAAA,CAAA6B,SAAA,GAAe;IAAf7B,EAAA,CAAA8B,UAAA,SAAAxB,MAAA,CAAA4F,SAAA,CAAe;;;AD1LvB,OAAM,MAAOC,kBAAkB;EA6C7BC,YACUC,cAA8B,EAC9BC,WAAwB,EACxBC,YAA0B,EAC1BC,gBAAkC,EAClCC,EAAqB,EACrB5B,WAAwB,EACzBhC,oBAA0C,EAC1C6D,kBAAsC,EACtCC,aAA4B,EAC5BC,oBAA0C;IATzC,KAAAP,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAA5B,WAAW,GAAXA,WAAW;IACZ,KAAAhC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAA6D,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,oBAAoB,GAApBA,oBAAoB;IAtDpB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,SAAS,GAAG,UAAU;IACtB,KAAA/D,aAAa,GAAG,KAAK;IACrB,KAAAR,MAAM,GAAG,IAAI;IACb,KAAAI,SAAS,GAAc,EAAe;IACvC,KAAAoE,IAAI,GAAG,IAAItH,OAAO,EAAE;IAC5B,KAAAuH,WAAW,GAAY,KAAK;IAC5B,KAAApE,gBAAgB,GAAY,IAAI;IAGhC,KAAAnC,WAAW,GAAY,KAAK;IAC5B,KAAAwG,SAAS,GAAY,KAAK;IAC1B,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,eAAe,GAAS,EAAU;IAClC,KAAAC,aAAa,GAAS,EAAU;IAChC,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAjE,aAAa,GAAW,EAAE;IAC1B,KAAAkE,UAAU,GAAgB,EAAE;IAC5B,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,gBAAgB,GAAY,KAAK;IACjC,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,0BAA0B,GAAW,CAAC;IACtC,KAAAC,WAAW,GAAY,IAAI;IAC3B,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAA9B,SAAS,GAAY,KAAK;IAC1B,KAAA+B,UAAU,GAAG,EAAE;IACf,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAhF,iBAAiB,GAAW,EAAE;IACvB,KAAAiF,IAAI,GAAS,EAAU;IACvB,KAAAC,IAAI,GAAW,EAAE;IACjB,KAAAC,OAAO,GAAW,CAAC;IACnB,KAAAC,cAAc,GAAY,IAAI;IAC9B,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,EAAE,GAAQ,EAAS;IACnB,KAAAC,IAAI,GAAQ,EAAS;IACrB,KAAAC,IAAI,GAAqB,IAAInJ,gBAAgB,CAAC,EAAE,CAAC;IACxD,KAAAoJ,QAAQ,GAAY,KAAK;IAClB,KAAA1G,cAAc,GAAGrC,cAAc;EAclC;EAEJgJ,QAAQA,CAAA;IACN,IAAI,CAACT,IAAI,GAAG,IAAI,CAACtD,WAAW,CAACgE,eAAe,EAAE;IAC9C,IAAI,CAACT,IAAI,GAAG,IAAI,CAACD,IAAI,CAACC,IAAK;IAC3B,IAAI,CAACU,qBAAqB,EAAE;IAC5B;IACA,IAAI,CAACJ,IAAI,GAAG,IAAInJ,gBAAgB,CAAC;MAC/BwJ,KAAK,EAAE,IAAIzJ,kBAAkB,CAAC,IAAI,EAAE;QAClC0J,UAAU,EAAE,CAACxJ,UAAU,CAACyJ,QAAQ;OACjC,CAAC;MACFC,IAAI,EAAE,IAAI5J,kBAAkB,CAAC,IAAI,EAAE;QACjC0J,UAAU,EAAE,CAACxJ,UAAU,CAACyJ,QAAQ;OACjC;KACF,CAAC;EACJ;EAEAE,WAAWA,CAAA;IACT,IAAI,IAAI,CAACxG,SAAS,EAAE;MAClB,IAAI,CAACO,iBAAiB,GAAG,IAAI,CAACP,SAAS,CAACO,iBAAiB;IAC3D;IACA,IAAI,CAACM,aAAa,GAAG,IAAI,CAACqD,KAAK;EACjC;EAEAuC,eAAeA,CAAA;IACb,IAAI,CAACxG,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC6D,EAAE,CAAC4C,aAAa,EAAE;EACzB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACvC,IAAI,CAACwC,WAAW,EAAE;IACvB,IAAI,CAAC1G,oBAAoB,CAAC2G,eAAe,CAAC,KAAK,CAAC;IAChD,IAAI,CAAClD,WAAW,CAAC4B,YAAY,GAAG,EAAE;EACpC;EAEAuB,kBAAkBA,CAAA,GAClB;EAEA,IAAIpH,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACQ,oBAAoB,CAACC,kBAAkB,CAAC,IAAI,CAACP,MAAM,EAAE,IAAI,CAACQ,aAAa,CAAC;EACtF;EAEA;EACAyG,eAAeA,CAAA;IACb,IAAI,CAAC5G,gBAAgB,GAAG,CAAC,IAAI,CAACA,gBAAgB;IAC9C,IAAI,CAAC,IAAI,CAACA,gBAAgB,EAAE;MAC1B,IAAI,CAAC0D,WAAW,CAAC4B,YAAY,GAAG,EAAE;IACpC;IACAwB,UAAU,CAAC,MAAK;MACd,IAAI,CAAC7G,oBAAoB,CAAC2G,eAAe,CAAC,IAAI,CAAC5G,gBAAgB,CAAC;IAClE,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;;;;EAMAsC,gBAAgBA,CAACyE,aAAmB;IAClC,IAAI,IAAI,CAACpH,MAAM,EAAE;MACjB,OAAO,IAAI,CAACiB,aAAa,CAACoG,MAAM,CAAEC,IAAU,IAAI;QAC9C,OAAOF,aAAa,GAChBE,IAAI,CAACC,eAAe,CAACC,IAAI,CACxB5B,IAAU,IAAKA,IAAI,CAAC6B,SAAS,KAAKL,aAAa,CAACK,SAAS,CAC3D,GACC,IAAI;MACV,CAAC,CAAC;IACF,CAAC,MAAM;MACL,OAAO,IAAI,CAACxG,aAAa,CAACoG,MAAM,CAAEC,IAAU,IAAI;QAC9C,OAAOF,aAAa,GAChBE,IAAI,CAACI,SAAS,CAACD,SAAS,KAAKL,aAAa,CAACK,SAAS,GAAG,IAAI;MACjE,CAAC,CAAC;IACJ;EAEF;EAEAE,eAAeA,CAAA;IACb,KAAK,IAAIL,IAAI,IAAI,IAAI,CAACvD,WAAW,CAAC4B,YAAY,EAAE;MAC9C,IAAI2B,IAAI,CAACM,YAAY,CAACP,MAAM,CAAEzB,IAAU,IAAKA,IAAI,CAACiC,EAAE,IAAI,IAAI,CAACvF,WAAW,CAACwF,SAAS,EAAE,CAAC,CAAClH,MAAM,IAAI,CAAC,EAAE,OAAO,IAAI;IAChH;IACA,OAAO,KAAK;EACd;EAEAmH,YAAYA,CAAA;IACV,KAAK,IAAIT,IAAI,IAAI,IAAI,CAACvD,WAAW,CAAC4B,YAAY,EAAE;MAC9C,IAAI,CAAC2B,IAAI,CAACU,MAAM,EAAE,OAAO,IAAI;IAC/B;IACA,OAAO,KAAK;EACd;EAGA;;;;;EAKAC,gBAAgBA,CAAA;IACd,IAAIhH,aAAa,GAAW,EAAE;IAC9B,KAAK,IAAIqG,IAAI,IAAI,IAAI,CAAChD,KAAK,EAAE;MAC3B,IAAI,IAAI,CAACQ,cAAc,EAAE;QACvB,IAAI,CAACwC,IAAI,CAACU,MAAM,EAAE;UAChB;QACF;MACF;MACA,IAAI,IAAI,CAACjD,iBAAiB,EAAE;QAC1B,IAAIuC,IAAI,CAACM,YAAY,CAACP,MAAM,CAAEzB,IAAU,IAAKA,IAAI,CAAC6B,SAAS,IAAI,IAAI,CAACnF,WAAW,CAACwF,SAAS,EAAE,CAAC,CAAClH,MAAM,IAAI,CAAC,EAAE;UACxG;QACF;MACF;MACA;MACA,IAAI,IAAI,CAACsF,IAAI,CAACgC,KAAK,EAAE;QACnB,IAAI,EAAEZ,IAAI,CAACa,OAAO,CAACC,OAAO,EAAE,GAAG,IAAI,CAAClC,IAAI,CAACgC,KAAK,CAACE,OAAO,EAAE,CAAC,EAAE;UACzD;QACF;MACF;MACA,IAAI,IAAI,CAACnC,EAAE,CAACiC,KAAK,EAAE;QACjB,IAAI,EAAEZ,IAAI,CAACa,OAAO,CAACC,OAAO,EAAE,GAAGjL,MAAM,CAAC,IAAI,CAAC8I,EAAE,CAACiC,KAAK,CAAC,CAACG,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,MAAM,EAAE,CAACF,OAAO,EAAE,CAAC,EAAE;UACtF;QACF;MACF;MACA,IAAI,IAAI,CAACxD,cAAc,EAAE;QACvB,IAAI0C,IAAI,CAACC,eAAe,CAAC3G,MAAM,IAAI,CAAC,EAAE;UACpC;QACF;MACF;MACA,IAAI,IAAI,CAACiE,iBAAiB,EAAE;QAC1B,IAAIyC,IAAI,CAACC,eAAe,CAAC3G,MAAM,GAAG,CAAC,EAAE;UACnC;QACF;MACF;MACAK,aAAa,CAACsH,IAAI,CAACjB,IAAI,CAAC;IAC1B;IACA,OAAOrG,aAAa;EACtB;EAEAuH,YAAYA,CAACC,KAAa;IACxB,OAAS,IAAI,CAACtC,IAAI,CAACuC,GAAG,CAACD,KAAK,CAAC,EAAEE,OAAO,IAAI,IAAI,CAACxC,IAAI,CAACuC,GAAG,CAACD,KAAK,CAAC,EAAEG,OAAO,IAAM,IAAI,CAAClE,SAAS,IAAI,IAAI,CAACyB,IAAI,CAACuC,GAAG,CAACD,KAAK,CAAC,EAAEE,OAAQ;EAC/H;EAEAE,WAAWA,CAAA;IACT,IAAI,CAAClE,aAAa,GAAG,CAAC,IAAI,CAACe,UAAU,IAAI,IAAI,CAACA,UAAU,KAAK,EAAE,GAAG,IAAI,GAAG,KAAK;EAChF;EAEAoD,KAAKA,CAACC,KAAU;IACd,IAAI,CAAC3D,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,gBAAgB,GAAG,KAAK;EAC/B;EAEAlF,eAAeA,CAAA;IACb6I,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC;IACjB,IAAI,CAAC/K,WAAW,GAAG,KAAK;EAC1B;EAEA8D,aAAaA,CAAC+G,KAAU;IACtBC,OAAO,CAACC,GAAG,CAACF,KAAK,CAAC;IAClB,IAAIA,KAAK,CAACG,OAAO,EAAE;MACjB,IAAI,CAACnF,WAAW,CAAC4B,YAAY,CAAC4C,IAAI,CAACQ,KAAK,CAACzB,IAAI,CAAC;IAChD,CAAC,MAAM;MACL,IAAI,CAACvD,WAAW,CAAC4B,YAAY,GAAG,IAAI,CAAC5B,WAAW,CAAC4B,YAAY,CAAC0B,MAAM,CAAC8B,EAAE,IAAIA,EAAE,CAACtB,EAAE,KAAKkB,KAAK,CAACzB,IAAI,CAACO,EAAE,CAAC;IACrG;EACF;EAEAvJ,eAAeA,CAAA;IACb,MAAMqH,YAAY,GAAG,IAAI,CAAC5B,WAAW,CAAC4B,YAAY;IAElD,IAAIA,YAAY,CAAC/E,MAAM,KAAK,CAAC,EAAE;MAC7B,IAAI,CAACwI,oBAAoB,CAAC,kCAAkC,CAAC;MAC7D;IACF;IAEA,MAAM5G,SAAS,GAAG,IAAI,CAACF,WAAW,CAACE,SAAS;IAE5C,IAAIA,SAAS,EAAE;MACb,IAAI,CAAC6G,0BAA0B,CAAC1D,YAAY,CAAC;IAC/C,CAAC,MAAM;MACL,IAAI,CAAC2D,0BAA0B,CAAC3D,YAAY,CAAC;IAC/C;EACF;EAEAhH,iBAAiBA,CAAA;IACf,IAAI,IAAI,CAACoF,WAAW,CAAC4B,YAAY,CAAC/E,MAAM,KAAK,CAAC,EAAE;MAC5C,IAAI,CAACwI,oBAAoB,CAAC,kCAAkC,CAAC;MAC7D;IACJ;IAEA,IAAI,CAACrF,WAAW,CAAC4B,YAAY,CAAC4D,OAAO,CAACjC,IAAI,IAAG;MACzC,MAAMkC,WAAW,GAAUlC,IAAI,CAACC,eAAe,CAACkC,GAAG,CAAC7D,IAAI,IAAIA,IAAI,CAAC6B,SAAS,CAAC;MAE3E,MAAMiC,cAAc,GAAyB;QACzCC,MAAM,EAAErC,IAAI,CAACO,EAAE;QACf+B,WAAW,EAAEC,MAAM,CAAC,IAAI,CAACzJ,SAAS,CAACyH,EAAE,CAAC;QACtCiC,UAAU,EAAE,IAAI,CAACC,oBAAoB,CAACP,WAAW,EAAElC,IAAI;OAC1D;MACD,IAAI,CAACnD,kBAAkB,CAAC6F,WAAW,CAACN,cAAc,EAAE,6BAA6B,CAAC;IACtF,CAAC,CAAC;IAEF,IAAI,CAACzC,eAAe,EAAE;EAC1B;EAGE7H,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAAC2E,WAAW,CAAC4B,YAAY,CAAC/E,MAAM,KAAK,CAAC,EAAE;MAC9C,IAAI,CAACwI,oBAAoB,CAAC,kCAAkC,CAAC;MAC7D;IACF;IACA,KAAK,IAAI9B,IAAI,IAAI,IAAI,CAACvD,WAAW,CAAC4B,YAAY,EAAE;MAC9C,IAAI,CAACxB,kBAAkB,CAAC8F,QAAQ,CAAC3C,IAAI,CAAC;IACxC;EACF;EAEAxI,oBAAoBA,CAAA;IAClB,IAAI,CAACoL,mCAAmC,EAAE;IAC1C,IAAI,IAAI,CAACnG,WAAW,CAAC4B,YAAY,CAAC/E,MAAM,KAAK,CAAC,EAAE;MAC9C,IAAI,CAACwI,oBAAoB,CAAC,kCAAkC,CAAC;MAC7D;IACF;IACA,IAAI,CAAC/E,oBAAoB,CAAC8F,aAAa,CAAC;MACtCC,cAAc,EAAE;0DACoC;MACpDC,cAAc,EAAE,KAAK;MACrBC,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE,aAAa;MACrBC,WAAW,EAAE,qCAAqC;MAClDC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,QAAQ;MACpBC,WAAW,EAAE;KACd,CAAC;IACF,IAAI,CAACtG,oBAAoB,CAACuG,IAAI,EAAE;EAElC;EAEQb,oBAAoBA,CAACP,WAAkB,EAAElC,IAAS;IACxD,IAAI,IAAI,CAAChF,WAAW,CAACE,SAAS,EAAE;MAC9B,OAAOgH,WAAW;IACpB,CAAC,MAAM,IAAI,IAAI,CAACzF,WAAW,CAAC8G,QAAQ,CAACvD,IAAI,CAAC,EAAE;MAC1C,OAAO,CAAC,IAAI,CAAClH,SAAS,CAAC0K,OAAQ,CAACrD,SAAU,CAAC;IAC7C;IACA,OAAO,CAAC,IAAI,CAAC7B,IAAI,CAAC6B,SAAU,CAAC;EAC/B;EAEQ4B,0BAA0BA,CAAC1D,YAAoB;IACrD,IAAI,CAAC,CAAC,IAAI,CAACjG,cAAc,CAACC,qCAAqC,EAAE,IAAI,CAACD,cAAc,CAACK,kBAAkB,CAAC,CAACF,QAAQ,CAAC,IAAI,CAACC,gBAAgB,CAAC,EAAE;MAExI,IAAI,IAAI,CAACM,SAAS,CAACO,iBAAiB,CAACC,MAAM,KAAK,CAAC,EAAE;QACjD,IAAI,CAACmK,+BAA+B,CAACpF,YAAY,CAAC;MACpD,CAAC,MAAM;QACL,IAAI,CAACqF,+BAA+B,CAACrF,YAAY,CAAC;MACpD;IAEF,CAAC,MAAM;MACL,IAAI,CAACqF,+BAA+B,CAACrF,YAAY,CAAC;IACpD;EACF;EAEQ2D,0BAA0BA,CAAC3D,YAAoB;IACrD,MAAM6D,WAAW,GAAU,EAAE;IAE7B,KAAK,IAAIlC,IAAI,IAAI3B,YAAY,EAAE;MAC7B,KAAK,IAAIC,IAAI,IAAI0B,IAAI,CAACC,eAAe,EAAE;QACrCiC,WAAW,CAACjB,IAAI,CAAC3C,IAAI,CAAC6B,SAAS,CAAC;MAClC;MAEA,MAAMwD,YAAY,GAAyB;QACzCtB,MAAM,EAAErC,IAAI,CAACO,EAAE;QACf+B,WAAW,EAAE,CAAC,IAAI,CAACxJ,SAAS,CAACyH,EAAE;QAC/BiC,UAAU,EAAE,CAAC,IAAI,CAAC1J,SAAS,CAAC0K,OAAQ,CAACrD,SAAU;OAChD;MACD,IAAI,CAACtD,kBAAkB,CAAC+G,SAAS,CAACD,YAAY,EAAE,6BAA6B,CAAC;IAChF;IAEA,IAAI,CAAChE,eAAe,EAAE;EACxB;EAEQ8D,+BAA+BA,CAACpF,YAAoB;IAC1D,KAAK,IAAI2B,IAAI,IAAI3B,YAAY,EAAE;MAC7B,MAAMsF,YAAY,GAAyB;QACvCtB,MAAM,EAAErC,IAAI,CAACO,EAAE;QACf+B,WAAW,EAAE,CAAC,IAAI,CAACxJ,SAAS,CAACyH,EAAE;QAC/BiC,UAAU,EAAE,CAAC,IAAI,CAAC1J,SAAS,CAACO,iBAAiB,CAAC,CAAC,CAAC,CAAC8G,SAAU;OAC7D;MACF,IAAI,CAACtD,kBAAkB,CAAC+G,SAAS,CAACD,YAAY,EAAE,2BAA2B,CAAC;IAC9E;EACF;EAEQD,+BAA+BA,CAACrF,YAAoB;IAC1D,IAAI,CAAC7B,cAAc,CAACqH,uBAAuB,CAAC,IAAI,CAAC/G,aAAa,EAAE9G,iCAAiC,EAAE,GAAG,EACpG;MACEiN,MAAM,EAAE,WAAW;MACnBa,cAAc,EAAE,EAAE;MAClBC,WAAW,EAAE,oBAAoB;MACjCC,oBAAoB,EAAE,IAAI,CAAClL;KAC5B,EAAE,IAAI,EAAGmL,MAAW,IAAI;MACzBvC,OAAO,CAACC,GAAG,CAACsC,MAAM,CAAC;MACnB,IAAIA,MAAM,IAAIA,MAAM,CAAChB,MAAM,KAAK,WAAW,EAAE;QAC3C,IAAI,CAAC,IAAI,CAACzG,cAAc,CAAC0H,eAAe,CAACD,MAAM,CAAC,EAAE;UAEhD,KAAK,IAAIjE,IAAI,IAAI3B,YAAY,EAAE;YAC7B,IAAI,CAACxB,kBAAkB,CAACsH,WAAW,CAACnE,IAAI,EAAEiE,MAAM,EAAE,IAAI,CAACjJ,WAAW,CAACC,SAAS,CAAC;UAC/E;UACA,IAAI,CAAC0E,eAAe,EAAE;UACtB+B,OAAO,CAACC,GAAG,CAACsC,MAAM,CAAC;QACrB;MACF;IACF,CAAC,CAAC;EACJ;EAEQrB,mCAAmCA,CAAA;IACzC,IAAI,CAACwB,6BAA6B,GAAI,IAAI,CAACrH,oBAAoB,CAACsH,eAAe,CAC5EC,IAAI,CAACpO,GAAG,CAAEqO,GAAG,IAAI;MAEhB,IAAI,CAACxH,oBAAoB,CAACyH,IAAI,EAAE;MAChC9C,OAAO,CAACC,GAAG,CAAC4C,GAAG,CAAC;MAEhB,QAAQA,GAAG,CAACtB,MAAM;QAChB,KAAK,aAAa;UAChBvB,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;UAC1B,IAAI,CAAC8C,4BAA4B,EAAE;MACvC;IACF,CAAC,CAAC,CAAC,CAACC,SAAS,EAAG;IAElB,IAAI,CAACxH,IAAI,CAAC6D,GAAG,CAAC,IAAI,CAACqD,6BAA6B,CAAC;EACnD;EAEQK,4BAA4BA,CAAA;IAClC/C,OAAO,CAACC,GAAG,CAAC,IAAI,CAAClF,WAAW,CAAC4B,YAAY,CAAC;IAC1C,KAAK,IAAI2B,IAAI,IAAI,IAAI,CAACvD,WAAW,CAAC4B,YAAY,EAAE;MAC9CqD,OAAO,CAACC,GAAG,CAAC3B,IAAI,CAAC;MACjB,IAAI,CAACnD,kBAAkB,CAAC8H,qBAAqB,CAAC3E,IAAI,CAAC;IACrD;IACA,IAAI,CAACoE,6BAA6B,CAAC1E,WAAW,EAAE;IAChD,IAAI,CAACC,eAAe,EAAE;EACxB;EAEQmC,oBAAoBA,CAAC8C,GAAW;IACtC,IAAI,IAAI,CAACnI,WAAW,CAAC4B,YAAY,CAAC/E,MAAM,KAAK,CAAC,EAAE;MAC9C,IAAI,CAACoD,YAAY,CAACmI,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAEJ;OACT,CAAC;MACF;IACF;EACF;EAEQ3F,qBAAqBA,CAAA;IAC3B,IAAI,CAAC/B,IAAI,CAAC6D,GAAG,CAAC,IAAI,CAAC/H,oBAAoB,CAACiM,wBAAwB,CAACP,SAAS,CAACH,GAAG,IAAG;MAC/E,IAAI,IAAI,CAAC/H,cAAc,CAAC0H,eAAe,CAACK,GAAG,CAAC,EAAE;QAC5C;MACF;MACA7C,OAAO,CAACC,GAAG,CAAC,IAAI,CAAChI,aAAa,CAAC;MAC/B,IAAI,CAACb,SAAS,GAAGyL,GAAG;MACpB,IAAI,CAAClL,iBAAiB,GAAG,IAAI,CAACP,SAAS,CAACO,iBAAiB;IAC3D,CAAC,CAAC,CAAC;EACL;EAAC,QAAA6L,CAAA,G;qBA1ZU5I,kBAAkB,EAAAnG,EAAA,CAAAgP,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAlP,EAAA,CAAAgP,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAApP,EAAA,CAAAgP,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAAtP,EAAA,CAAAgP,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAxP,EAAA,CAAAgP,iBAAA,CAAAhP,EAAA,CAAAyP,iBAAA,GAAAzP,EAAA,CAAAgP,iBAAA,CAAAU,EAAA,CAAAC,WAAA,GAAA3P,EAAA,CAAAgP,iBAAA,CAAAY,EAAA,CAAAjQ,oBAAA,GAAAK,EAAA,CAAAgP,iBAAA,CAAAa,EAAA,CAAAC,kBAAA,GAAA9P,EAAA,CAAAgP,iBAAA,CAAAe,EAAA,CAAAjQ,aAAA,GAAAE,EAAA,CAAAgP,iBAAA,CAAAgB,EAAA,CAAAC,oBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlB/J,kBAAkB;IAAAgK,SAAA;IAAAC,MAAA;MAAAvJ,KAAA;MAAAC,SAAA;MAAA/D,aAAA;MAAAR,MAAA;MAAAI,SAAA;IAAA;IAAA0N,QAAA,GAAArQ,EAAA,CAAAsQ,kBAAA,CAHlB,CAACxQ,aAAa,EAAEH,oBAAoB,CAAC,GAAAK,EAAA,CAAAuQ,oBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC/BhD7Q,EADF,CAAAC,cAAA,aAA0B,aACQ;QAC9BD,EAAA,CAAAe,MAAA,GACF;QAAAf,EAAA,CAAAU,YAAA,EAAM;QAsDAV,EAnDN,CAAAC,cAAA,aAAkC,aAiDQ,cACE,eAEqB;QADxCD,EAAA,CAAA+Q,gBAAA,2BAAAC,2DAAA3M,MAAA;UAAArE,EAAA,CAAAiR,kBAAA,CAAAH,GAAA,CAAAnL,IAAA,EAAAtB,MAAA,MAAAyM,GAAA,CAAAnL,IAAA,GAAAtB,MAAA;UAAA,OAAAA,MAAA;QAAA,EAAkB;QAArCrE,EAAA,CAAAU,YAAA,EAC2D;QAC3DV,EAAA,CAAAc,SAAA,WAAyC;QAC3Cd,EAAA,CAAAU,YAAA,EAAO;QACPV,EAAA,CAAAC,cAAA,aAE+C;QAA/CD,EAAA,CAAAE,UAAA,mBAAAgR,iDAAA;UAAA,OAASJ,GAAA,CAAAtH,eAAA,EAAiB;QAAA,EAAC;QAF3BxJ,EAAA,CAAAU,YAAA,EAE+C;QAC/CV,EAAA,CAAAsB,UAAA,IAAA6P,iCAAA,iBACyC;QAG/CnR,EAFI,CAAAU,YAAA,EAAM,EACF,EACF;QAGNV,EAAA,CAAAC,cAAA,eAA4D;QAC1DD,EAAA,CAAAsB,UAAA,KAAA8P,kCAAA,kBAA+G;QA0CjHpR,EAAA,CAAAU,YAAA,EAAM;QAwBNV,EAfA,CAAAsB,UAAA,KAAA+P,2CAAA,2BAIiB,KAAAC,kCAAA,kBAW4B;;;QAxIzCtR,EAAA,CAAA6B,SAAA,GACF;QADE7B,EAAA,CAAAuR,kBAAA,MAAAT,GAAA,CAAAhK,SAAA,MACF;QAsDyB9G,EAAA,CAAA6B,SAAA,GAAkB;QAAlB7B,EAAA,CAAAwR,gBAAA,YAAAV,GAAA,CAAAnL,IAAA,CAAkB;QAIY3F,EAAA,CAAA6B,SAAA,GAA6C;QAA7C7B,EAAA,CAAA8B,UAAA,YAAA9B,EAAA,CAAAyF,eAAA,IAAAgM,GAAA,EAAAX,GAAA,CAAAlO,gBAAA,EAA6C;QAG1F5C,EAAA,CAAA6B,SAAA,EAAY;QAAZ7B,EAAA,CAAA8B,UAAA,SAAAgP,GAAA,CAAAvO,MAAA,CAAY;QAQhBvC,EAAA,CAAA6B,SAAA,GAAsB;QAAtB7B,EAAA,CAAA8B,UAAA,SAAAgP,GAAA,CAAAlO,gBAAA,CAAsB;QAmDf5C,EAAA,CAAA6B,SAAA,EAIE;QAJF7B,EAAA,CAAA8B,UAAA,UAAAgP,GAAA,CAAAzO,gBAAA,KAAAyO,GAAA,CAAA7O,cAAA,CAAAsD,4BAAA,IAAAuL,GAAA,CAAAzO,gBAAA,KAAAyO,GAAA,CAAA7O,cAAA,CAAAyP,4BAAA,IAAAZ,GAAA,CAAAzO,gBAAA,KAAAyO,GAAA,CAAA7O,cAAA,CAAAK,kBAAA,KAAAwO,GAAA,CAAArQ,WAAA,CAIE;QAWXT,EAAA,CAAA6B,SAAA,EAAgB;QAAhB7B,EAAA,CAAA8B,UAAA,UAAAgP,GAAA,CAAA5K,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}