{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport let NotFoundModule = /*#__PURE__*/(() => {\n  class NotFoundModule {\n    static #_ = this.ɵfac = function NotFoundModule_Factory(t) {\n      return new (t || NotFoundModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: NotFoundModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, RouterModule.forRoot([\n        // your routes here\n      ])]\n    });\n  }\n  return NotFoundModule;\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "NotFoundModule", "_", "_2", "_3", "forRoot"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\not-found\\not-found.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NotFoundComponent } from './not-found.component';\r\nimport { RouterModule } from '@angular/router';\r\n\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    NotFoundComponent\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule.forRoot([\r\n      // your routes here\r\n    ])\r\n  ]\r\n})\r\nexport class NotFoundModule { }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,iBAAiB;;;AAe9C,WAAaC,cAAc;EAArB,MAAOA,cAAc;IAAA,QAAAC,CAAA,G;uBAAdD,cAAc;IAAA;IAAA,QAAAE,EAAA,G;YAAdF;IAAc;IAAA,QAAAG,EAAA,G;gBANvBL,YAAY,EACZC,YAAY,CAACK,OAAO,CAAC;QACnB;MAAA,CACD,CAAC;IAAA;;SAGOJ,cAAc;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}