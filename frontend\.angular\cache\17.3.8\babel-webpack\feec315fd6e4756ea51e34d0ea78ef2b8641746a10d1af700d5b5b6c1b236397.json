{"ast": null, "code": "import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, InjectionToken, EventEmitter, Directive, Optional, Input, Output, NgModule } from '@angular/core';\n\n/**\n * A pending copy-to-clipboard operation.\n *\n * The implementation of copying text to the clipboard modifies the DOM and\n * forces a re-layout. This re-layout can take too long if the string is large,\n * causing the execCommand('copy') to happen too long after the user clicked.\n * This results in the browser refusing to copy. This object lets the\n * re-layout happen in a separate tick from copying by providing a copy function\n * that can be called later.\n *\n * Destroy must be called when no longer in use, regardless of whether `copy` is\n * called.\n */\nclass PendingCopy {\n  constructor(text, _document) {\n    this._document = _document;\n    const textarea = this._textarea = this._document.createElement('textarea');\n    const styles = textarea.style;\n    // Hide the element for display and accessibility. Set a fixed position so the page layout\n    // isn't affected. We use `fixed` with `top: 0`, because focus is moved into the textarea\n    // for a split second and if it's off-screen, some browsers will attempt to scroll it into view.\n    styles.position = 'fixed';\n    styles.top = styles.opacity = '0';\n    styles.left = '-999em';\n    textarea.setAttribute('aria-hidden', 'true');\n    textarea.value = text;\n    // Making the textarea `readonly` prevents the screen from jumping on iOS Safari (see #25169).\n    textarea.readOnly = true;\n    // The element needs to be inserted into the fullscreen container, if the page\n    // is in fullscreen mode, otherwise the browser won't execute the copy command.\n    (this._document.fullscreenElement || this._document.body).appendChild(textarea);\n  }\n  /** Finishes copying the text. */\n  copy() {\n    const textarea = this._textarea;\n    let successful = false;\n    try {\n      // Older browsers could throw if copy is not supported.\n      if (textarea) {\n        const currentFocus = this._document.activeElement;\n        textarea.select();\n        textarea.setSelectionRange(0, textarea.value.length);\n        successful = this._document.execCommand('copy');\n        if (currentFocus) {\n          currentFocus.focus();\n        }\n      }\n    } catch {\n      // Discard error.\n      // Initial setting of {@code successful} will represent failure here.\n    }\n    return successful;\n  }\n  /** Cleans up DOM changes used to perform the copy operation. */\n  destroy() {\n    const textarea = this._textarea;\n    if (textarea) {\n      textarea.remove();\n      this._textarea = undefined;\n    }\n  }\n}\n\n/**\n * A service for copying text to the clipboard.\n */\nlet Clipboard = /*#__PURE__*/(() => {\n  class Clipboard {\n    constructor(document) {\n      this._document = document;\n    }\n    /**\n     * Copies the provided text into the user's clipboard.\n     *\n     * @param text The string to copy.\n     * @returns Whether the operation was successful.\n     */\n    copy(text) {\n      const pendingCopy = this.beginCopy(text);\n      const successful = pendingCopy.copy();\n      pendingCopy.destroy();\n      return successful;\n    }\n    /**\n     * Prepares a string to be copied later. This is useful for large strings\n     * which take too long to successfully render and be copied in the same tick.\n     *\n     * The caller must call `destroy` on the returned `PendingCopy`.\n     *\n     * @param text The string to copy.\n     * @returns the pending copy operation.\n     */\n    beginCopy(text) {\n      return new PendingCopy(text, this._document);\n    }\n    static #_ = this.ɵfac = function Clipboard_Factory(t) {\n      return new (t || Clipboard)(i0.ɵɵinject(DOCUMENT));\n    };\n    static #_2 = this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: Clipboard,\n      factory: Clipboard.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return Clipboard;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Injection token that can be used to provide the default options to `CdkCopyToClipboard`. */\nconst CDK_COPY_TO_CLIPBOARD_CONFIG = /*#__PURE__*/new InjectionToken('CDK_COPY_TO_CLIPBOARD_CONFIG');\n/**\n * Provides behavior for a button that when clicked copies content into user's\n * clipboard.\n */\nlet CdkCopyToClipboard = /*#__PURE__*/(() => {\n  class CdkCopyToClipboard {\n    constructor(_clipboard, _ngZone, config) {\n      this._clipboard = _clipboard;\n      this._ngZone = _ngZone;\n      /** Content to be copied. */\n      this.text = '';\n      /**\n       * How many times to attempt to copy the text. This may be necessary for longer text, because\n       * the browser needs time to fill an intermediate textarea element and copy the content.\n       */\n      this.attempts = 1;\n      /**\n       * Emits when some text is copied to the clipboard. The\n       * emitted value indicates whether copying was successful.\n       */\n      this.copied = new EventEmitter();\n      /** Copies that are currently being attempted. */\n      this._pending = new Set();\n      if (config && config.attempts != null) {\n        this.attempts = config.attempts;\n      }\n    }\n    /** Copies the current text to the clipboard. */\n    copy(attempts = this.attempts) {\n      if (attempts > 1) {\n        let remainingAttempts = attempts;\n        const pending = this._clipboard.beginCopy(this.text);\n        this._pending.add(pending);\n        const attempt = () => {\n          const successful = pending.copy();\n          if (!successful && --remainingAttempts && !this._destroyed) {\n            // We use 1 for the timeout since it's more predictable when flushing in unit tests.\n            this._currentTimeout = this._ngZone.runOutsideAngular(() => setTimeout(attempt, 1));\n          } else {\n            this._currentTimeout = null;\n            this._pending.delete(pending);\n            pending.destroy();\n            this.copied.emit(successful);\n          }\n        };\n        attempt();\n      } else {\n        this.copied.emit(this._clipboard.copy(this.text));\n      }\n    }\n    ngOnDestroy() {\n      if (this._currentTimeout) {\n        clearTimeout(this._currentTimeout);\n      }\n      this._pending.forEach(copy => copy.destroy());\n      this._pending.clear();\n      this._destroyed = true;\n    }\n    static #_ = this.ɵfac = function CdkCopyToClipboard_Factory(t) {\n      return new (t || CdkCopyToClipboard)(i0.ɵɵdirectiveInject(Clipboard), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(CDK_COPY_TO_CLIPBOARD_CONFIG, 8));\n    };\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkCopyToClipboard,\n      selectors: [[\"\", \"cdkCopyToClipboard\", \"\"]],\n      hostBindings: function CdkCopyToClipboard_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function CdkCopyToClipboard_click_HostBindingHandler() {\n            return ctx.copy();\n          });\n        }\n      },\n      inputs: {\n        text: [i0.ɵɵInputFlags.None, \"cdkCopyToClipboard\", \"text\"],\n        attempts: [i0.ɵɵInputFlags.None, \"cdkCopyToClipboardAttempts\", \"attempts\"]\n      },\n      outputs: {\n        copied: \"cdkCopyToClipboardCopied\"\n      },\n      standalone: true\n    });\n  }\n  return CdkCopyToClipboard;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet ClipboardModule = /*#__PURE__*/(() => {\n  class ClipboardModule {\n    static #_ = this.ɵfac = function ClipboardModule_Factory(t) {\n      return new (t || ClipboardModule)();\n    };\n    static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ClipboardModule\n    });\n    static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n  return ClipboardModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CDK_COPY_TO_CLIPBOARD_CONFIG, CdkCopyToClipboard, Clipboard, ClipboardModule, PendingCopy };", "map": {"version": 3, "names": ["DOCUMENT", "i0", "Injectable", "Inject", "InjectionToken", "EventEmitter", "Directive", "Optional", "Input", "Output", "NgModule", "PendingCopy", "constructor", "text", "_document", "textarea", "_textarea", "createElement", "styles", "style", "position", "top", "opacity", "left", "setAttribute", "value", "readOnly", "fullscreenElement", "body", "append<PERSON><PERSON><PERSON>", "copy", "successful", "currentFocus", "activeElement", "select", "setSelectionRange", "length", "execCommand", "focus", "destroy", "remove", "undefined", "Clipboard", "document", "pendingCopy", "beginCopy", "_", "ɵfac", "Clipboard_Factory", "t", "ɵɵinject", "_2", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "CDK_COPY_TO_CLIPBOARD_CONFIG", "CdkCopyToClipboard", "_clipboard", "_ngZone", "config", "attempts", "copied", "_pending", "Set", "remainingAttempts", "pending", "add", "attempt", "_destroyed", "_currentTimeout", "runOutsideAngular", "setTimeout", "delete", "emit", "ngOnDestroy", "clearTimeout", "for<PERSON>ach", "clear", "CdkCopyToClipboard_Factory", "ɵɵdirectiveInject", "NgZone", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostBindings", "CdkCopyToClipboard_HostBindings", "rf", "ctx", "ɵɵlistener", "CdkCopyToClipboard_click_HostBindingHandler", "inputs", "ɵɵInputFlags", "None", "outputs", "standalone", "ClipboardModule", "ClipboardModule_Factory", "ɵmod", "ɵɵdefineNgModule", "_3", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@angular/cdk/fesm2022/clipboard.mjs"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Injectable, Inject, InjectionToken, EventEmitter, Directive, Optional, Input, Output, NgModule } from '@angular/core';\n\n/**\n * A pending copy-to-clipboard operation.\n *\n * The implementation of copying text to the clipboard modifies the DOM and\n * forces a re-layout. This re-layout can take too long if the string is large,\n * causing the execCommand('copy') to happen too long after the user clicked.\n * This results in the browser refusing to copy. This object lets the\n * re-layout happen in a separate tick from copying by providing a copy function\n * that can be called later.\n *\n * Destroy must be called when no longer in use, regardless of whether `copy` is\n * called.\n */\nclass PendingCopy {\n    constructor(text, _document) {\n        this._document = _document;\n        const textarea = (this._textarea = this._document.createElement('textarea'));\n        const styles = textarea.style;\n        // Hide the element for display and accessibility. Set a fixed position so the page layout\n        // isn't affected. We use `fixed` with `top: 0`, because focus is moved into the textarea\n        // for a split second and if it's off-screen, some browsers will attempt to scroll it into view.\n        styles.position = 'fixed';\n        styles.top = styles.opacity = '0';\n        styles.left = '-999em';\n        textarea.setAttribute('aria-hidden', 'true');\n        textarea.value = text;\n        // Making the textarea `readonly` prevents the screen from jumping on iOS Safari (see #25169).\n        textarea.readOnly = true;\n        // The element needs to be inserted into the fullscreen container, if the page\n        // is in fullscreen mode, otherwise the browser won't execute the copy command.\n        (this._document.fullscreenElement || this._document.body).appendChild(textarea);\n    }\n    /** Finishes copying the text. */\n    copy() {\n        const textarea = this._textarea;\n        let successful = false;\n        try {\n            // Older browsers could throw if copy is not supported.\n            if (textarea) {\n                const currentFocus = this._document.activeElement;\n                textarea.select();\n                textarea.setSelectionRange(0, textarea.value.length);\n                successful = this._document.execCommand('copy');\n                if (currentFocus) {\n                    currentFocus.focus();\n                }\n            }\n        }\n        catch {\n            // Discard error.\n            // Initial setting of {@code successful} will represent failure here.\n        }\n        return successful;\n    }\n    /** Cleans up DOM changes used to perform the copy operation. */\n    destroy() {\n        const textarea = this._textarea;\n        if (textarea) {\n            textarea.remove();\n            this._textarea = undefined;\n        }\n    }\n}\n\n/**\n * A service for copying text to the clipboard.\n */\nclass Clipboard {\n    constructor(document) {\n        this._document = document;\n    }\n    /**\n     * Copies the provided text into the user's clipboard.\n     *\n     * @param text The string to copy.\n     * @returns Whether the operation was successful.\n     */\n    copy(text) {\n        const pendingCopy = this.beginCopy(text);\n        const successful = pendingCopy.copy();\n        pendingCopy.destroy();\n        return successful;\n    }\n    /**\n     * Prepares a string to be copied later. This is useful for large strings\n     * which take too long to successfully render and be copied in the same tick.\n     *\n     * The caller must call `destroy` on the returned `PendingCopy`.\n     *\n     * @param text The string to copy.\n     * @returns the pending copy operation.\n     */\n    beginCopy(text) {\n        return new PendingCopy(text, this._document);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: Clipboard, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: Clipboard, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: Clipboard, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\n/** Injection token that can be used to provide the default options to `CdkCopyToClipboard`. */\nconst CDK_COPY_TO_CLIPBOARD_CONFIG = new InjectionToken('CDK_COPY_TO_CLIPBOARD_CONFIG');\n/**\n * Provides behavior for a button that when clicked copies content into user's\n * clipboard.\n */\nclass CdkCopyToClipboard {\n    constructor(_clipboard, _ngZone, config) {\n        this._clipboard = _clipboard;\n        this._ngZone = _ngZone;\n        /** Content to be copied. */\n        this.text = '';\n        /**\n         * How many times to attempt to copy the text. This may be necessary for longer text, because\n         * the browser needs time to fill an intermediate textarea element and copy the content.\n         */\n        this.attempts = 1;\n        /**\n         * Emits when some text is copied to the clipboard. The\n         * emitted value indicates whether copying was successful.\n         */\n        this.copied = new EventEmitter();\n        /** Copies that are currently being attempted. */\n        this._pending = new Set();\n        if (config && config.attempts != null) {\n            this.attempts = config.attempts;\n        }\n    }\n    /** Copies the current text to the clipboard. */\n    copy(attempts = this.attempts) {\n        if (attempts > 1) {\n            let remainingAttempts = attempts;\n            const pending = this._clipboard.beginCopy(this.text);\n            this._pending.add(pending);\n            const attempt = () => {\n                const successful = pending.copy();\n                if (!successful && --remainingAttempts && !this._destroyed) {\n                    // We use 1 for the timeout since it's more predictable when flushing in unit tests.\n                    this._currentTimeout = this._ngZone.runOutsideAngular(() => setTimeout(attempt, 1));\n                }\n                else {\n                    this._currentTimeout = null;\n                    this._pending.delete(pending);\n                    pending.destroy();\n                    this.copied.emit(successful);\n                }\n            };\n            attempt();\n        }\n        else {\n            this.copied.emit(this._clipboard.copy(this.text));\n        }\n    }\n    ngOnDestroy() {\n        if (this._currentTimeout) {\n            clearTimeout(this._currentTimeout);\n        }\n        this._pending.forEach(copy => copy.destroy());\n        this._pending.clear();\n        this._destroyed = true;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkCopyToClipboard, deps: [{ token: Clipboard }, { token: i0.NgZone }, { token: CDK_COPY_TO_CLIPBOARD_CONFIG, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkCopyToClipboard, isStandalone: true, selector: \"[cdkCopyToClipboard]\", inputs: { text: [\"cdkCopyToClipboard\", \"text\"], attempts: [\"cdkCopyToClipboardAttempts\", \"attempts\"] }, outputs: { copied: \"cdkCopyToClipboardCopied\" }, host: { listeners: { \"click\": \"copy()\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkCopyToClipboard, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkCopyToClipboard]',\n                    host: {\n                        '(click)': 'copy()',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: Clipboard }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [CDK_COPY_TO_CLIPBOARD_CONFIG]\n                }] }], propDecorators: { text: [{\n                type: Input,\n                args: ['cdkCopyToClipboard']\n            }], attempts: [{\n                type: Input,\n                args: ['cdkCopyToClipboardAttempts']\n            }], copied: [{\n                type: Output,\n                args: ['cdkCopyToClipboardCopied']\n            }] } });\n\nclass ClipboardModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: ClipboardModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: ClipboardModule, imports: [CdkCopyToClipboard], exports: [CdkCopyToClipboard] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: ClipboardModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: ClipboardModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CdkCopyToClipboard],\n                    exports: [CdkCopyToClipboard],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CDK_COPY_TO_CLIPBOARD_CONFIG, CdkCopyToClipboard, Clipboard, ClipboardModule, PendingCopy };\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,MAAM,EAAEC,cAAc,EAAEC,YAAY,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;;AAE9H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,CAAC;EACdC,WAAWA,CAACC,IAAI,EAAEC,SAAS,EAAE;IACzB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,MAAMC,QAAQ,GAAI,IAAI,CAACC,SAAS,GAAG,IAAI,CAACF,SAAS,CAACG,aAAa,CAAC,UAAU,CAAE;IAC5E,MAAMC,MAAM,GAAGH,QAAQ,CAACI,KAAK;IAC7B;IACA;IACA;IACAD,MAAM,CAACE,QAAQ,GAAG,OAAO;IACzBF,MAAM,CAACG,GAAG,GAAGH,MAAM,CAACI,OAAO,GAAG,GAAG;IACjCJ,MAAM,CAACK,IAAI,GAAG,QAAQ;IACtBR,QAAQ,CAACS,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAC5CT,QAAQ,CAACU,KAAK,GAAGZ,IAAI;IACrB;IACAE,QAAQ,CAACW,QAAQ,GAAG,IAAI;IACxB;IACA;IACA,CAAC,IAAI,CAACZ,SAAS,CAACa,iBAAiB,IAAI,IAAI,CAACb,SAAS,CAACc,IAAI,EAAEC,WAAW,CAACd,QAAQ,CAAC;EACnF;EACA;EACAe,IAAIA,CAAA,EAAG;IACH,MAAMf,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,IAAIe,UAAU,GAAG,KAAK;IACtB,IAAI;MACA;MACA,IAAIhB,QAAQ,EAAE;QACV,MAAMiB,YAAY,GAAG,IAAI,CAAClB,SAAS,CAACmB,aAAa;QACjDlB,QAAQ,CAACmB,MAAM,CAAC,CAAC;QACjBnB,QAAQ,CAACoB,iBAAiB,CAAC,CAAC,EAAEpB,QAAQ,CAACU,KAAK,CAACW,MAAM,CAAC;QACpDL,UAAU,GAAG,IAAI,CAACjB,SAAS,CAACuB,WAAW,CAAC,MAAM,CAAC;QAC/C,IAAIL,YAAY,EAAE;UACdA,YAAY,CAACM,KAAK,CAAC,CAAC;QACxB;MACJ;IACJ,CAAC,CACD,MAAM;MACF;MACA;IAAA;IAEJ,OAAOP,UAAU;EACrB;EACA;EACAQ,OAAOA,CAAA,EAAG;IACN,MAAMxB,QAAQ,GAAG,IAAI,CAACC,SAAS;IAC/B,IAAID,QAAQ,EAAE;MACVA,QAAQ,CAACyB,MAAM,CAAC,CAAC;MACjB,IAAI,CAACxB,SAAS,GAAGyB,SAAS;IAC9B;EACJ;AACJ;;AAEA;AACA;AACA;AAFA,IAGMC,SAAS;EAAf,MAAMA,SAAS,CAAC;IACZ9B,WAAWA,CAAC+B,QAAQ,EAAE;MAClB,IAAI,CAAC7B,SAAS,GAAG6B,QAAQ;IAC7B;IACA;AACJ;AACA;AACA;AACA;AACA;IACIb,IAAIA,CAACjB,IAAI,EAAE;MACP,MAAM+B,WAAW,GAAG,IAAI,CAACC,SAAS,CAAChC,IAAI,CAAC;MACxC,MAAMkB,UAAU,GAAGa,WAAW,CAACd,IAAI,CAAC,CAAC;MACrCc,WAAW,CAACL,OAAO,CAAC,CAAC;MACrB,OAAOR,UAAU;IACrB;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACIc,SAASA,CAAChC,IAAI,EAAE;MACZ,OAAO,IAAIF,WAAW,CAACE,IAAI,EAAE,IAAI,CAACC,SAAS,CAAC;IAChD;IAAC,QAAAgC,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAC,kBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFP,SAAS,EAAnBzC,EAAE,CAAAiD,QAAA,CAAmClD,QAAQ;IAAA,CAA6C;IAAA,QAAAmD,EAAA,GACjL,IAAI,CAACC,KAAK,kBAD6EnD,EAAE,CAAAoD,kBAAA;MAAAC,KAAA,EACYZ,SAAS;MAAAa,OAAA,EAATb,SAAS,CAAAK,IAAA;MAAAS,UAAA,EAAc;IAAM,EAAG;EAClJ;EAAC,OA9BKd,SAAS;AAAA;AA+Bf;EAAA,QAAAe,SAAA,oBAAAA,SAAA;AAAA;;AAQA;AACA,MAAMC,4BAA4B,gBAAG,IAAItD,cAAc,CAAC,8BAA8B,CAAC;AACvF;AACA;AACA;AACA;AAHA,IAIMuD,kBAAkB;EAAxB,MAAMA,kBAAkB,CAAC;IACrB/C,WAAWA,CAACgD,UAAU,EAAEC,OAAO,EAAEC,MAAM,EAAE;MACrC,IAAI,CAACF,UAAU,GAAGA,UAAU;MAC5B,IAAI,CAACC,OAAO,GAAGA,OAAO;MACtB;MACA,IAAI,CAAChD,IAAI,GAAG,EAAE;MACd;AACR;AACA;AACA;MACQ,IAAI,CAACkD,QAAQ,GAAG,CAAC;MACjB;AACR;AACA;AACA;MACQ,IAAI,CAACC,MAAM,GAAG,IAAI3D,YAAY,CAAC,CAAC;MAChC;MACA,IAAI,CAAC4D,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;MACzB,IAAIJ,MAAM,IAAIA,MAAM,CAACC,QAAQ,IAAI,IAAI,EAAE;QACnC,IAAI,CAACA,QAAQ,GAAGD,MAAM,CAACC,QAAQ;MACnC;IACJ;IACA;IACAjC,IAAIA,CAACiC,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;MAC3B,IAAIA,QAAQ,GAAG,CAAC,EAAE;QACd,IAAII,iBAAiB,GAAGJ,QAAQ;QAChC,MAAMK,OAAO,GAAG,IAAI,CAACR,UAAU,CAACf,SAAS,CAAC,IAAI,CAAChC,IAAI,CAAC;QACpD,IAAI,CAACoD,QAAQ,CAACI,GAAG,CAACD,OAAO,CAAC;QAC1B,MAAME,OAAO,GAAGA,CAAA,KAAM;UAClB,MAAMvC,UAAU,GAAGqC,OAAO,CAACtC,IAAI,CAAC,CAAC;UACjC,IAAI,CAACC,UAAU,IAAI,EAAEoC,iBAAiB,IAAI,CAAC,IAAI,CAACI,UAAU,EAAE;YACxD;YACA,IAAI,CAACC,eAAe,GAAG,IAAI,CAACX,OAAO,CAACY,iBAAiB,CAAC,MAAMC,UAAU,CAACJ,OAAO,EAAE,CAAC,CAAC,CAAC;UACvF,CAAC,MACI;YACD,IAAI,CAACE,eAAe,GAAG,IAAI;YAC3B,IAAI,CAACP,QAAQ,CAACU,MAAM,CAACP,OAAO,CAAC;YAC7BA,OAAO,CAAC7B,OAAO,CAAC,CAAC;YACjB,IAAI,CAACyB,MAAM,CAACY,IAAI,CAAC7C,UAAU,CAAC;UAChC;QACJ,CAAC;QACDuC,OAAO,CAAC,CAAC;MACb,CAAC,MACI;QACD,IAAI,CAACN,MAAM,CAACY,IAAI,CAAC,IAAI,CAAChB,UAAU,CAAC9B,IAAI,CAAC,IAAI,CAACjB,IAAI,CAAC,CAAC;MACrD;IACJ;IACAgE,WAAWA,CAAA,EAAG;MACV,IAAI,IAAI,CAACL,eAAe,EAAE;QACtBM,YAAY,CAAC,IAAI,CAACN,eAAe,CAAC;MACtC;MACA,IAAI,CAACP,QAAQ,CAACc,OAAO,CAACjD,IAAI,IAAIA,IAAI,CAACS,OAAO,CAAC,CAAC,CAAC;MAC7C,IAAI,CAAC0B,QAAQ,CAACe,KAAK,CAAC,CAAC;MACrB,IAAI,CAACT,UAAU,GAAG,IAAI;IAC1B;IAAC,QAAAzB,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAkC,2BAAAhC,CAAA;MAAA,YAAAA,CAAA,IAAwFU,kBAAkB,EAxE5B1D,EAAE,CAAAiF,iBAAA,CAwE4CxC,SAAS,GAxEvDzC,EAAE,CAAAiF,iBAAA,CAwEkEjF,EAAE,CAACkF,MAAM,GAxE7ElF,EAAE,CAAAiF,iBAAA,CAwEwFxB,4BAA4B;IAAA,CAA4D;IAAA,QAAAP,EAAA,GACzQ,IAAI,CAACiC,IAAI,kBAzE8EnF,EAAE,CAAAoF,iBAAA;MAAAC,IAAA,EAyEJ3B,kBAAkB;MAAA4B,SAAA;MAAAC,YAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzEhBzF,EAAE,CAAA2F,UAAA,mBAAAC,4CAAA;YAAA,OAyEJF,GAAA,CAAA7D,IAAA,CAAK,CAAC;UAAA,CAAW,CAAC;QAAA;MAAA;MAAAgE,MAAA;QAAAjF,IAAA,GAzEhBZ,EAAE,CAAA8F,YAAA,CAAAC,IAAA;QAAAjC,QAAA,GAAF9D,EAAE,CAAA8F,YAAA,CAAAC,IAAA;MAAA;MAAAC,OAAA;QAAAjC,MAAA;MAAA;MAAAkC,UAAA;IAAA,EAyE0R;EAChY;EAAC,OAzDKvC,kBAAkB;AAAA;AA0DxB;EAAA,QAAAF,SAAA,oBAAAA,SAAA;AAAA;AAuBoB,IAEd0C,eAAe;EAArB,MAAMA,eAAe,CAAC;IAAA,QAAArD,CAAA,GACT,IAAI,CAACC,IAAI,YAAAqD,wBAAAnD,CAAA;MAAA,YAAAA,CAAA,IAAwFkD,eAAe;IAAA,CAAkD;IAAA,QAAAhD,EAAA,GAClK,IAAI,CAACkD,IAAI,kBAtG8EpG,EAAE,CAAAqG,gBAAA;MAAAhB,IAAA,EAsGSa;IAAe,EAAiE;IAAA,QAAAI,EAAA,GAClL,IAAI,CAACC,IAAI,kBAvG8EvG,EAAE,CAAAwG,gBAAA,IAuG2B;EACjI;EAAC,OAJKN,eAAe;AAAA;AAKrB;EAAA,QAAA1C,SAAA,oBAAAA,SAAA;AAAA;;AAQA;AACA;AACA;;AAEA,SAASC,4BAA4B,EAAEC,kBAAkB,EAAEjB,SAAS,EAAEyD,eAAe,EAAExF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}