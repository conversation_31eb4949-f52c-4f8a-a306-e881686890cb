{"ast": null, "code": "import { of } from 'rxjs';\nimport { map, startWith, switchMap, take } from 'rxjs/operators';\nimport { ClassroomType } from 'src/app/core/models/classroom.model';\nimport { SubSink } from 'subsink';\nimport * as moment from 'moment';\nimport { LessonStatus } from 'src/app/core/models/lesson.model';\nimport { UserRole } from 'src/app/core/models/user.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/classroom.service\";\nimport * as i2 from \"src/app/core/services/general.service\";\nimport * as i3 from \"src/app/core/services/auth.service\";\nimport * as i4 from \"src/app/core/services/user.service\";\nimport * as i5 from \"src/app/core/services/calendar.service\";\nimport * as i6 from \"src/app/core/services/layout.service\";\nimport * as i7 from \"src/app/core/services/lesson.service\";\nimport * as i8 from \"src/app/core/services/toast.service\";\nimport * as i9 from \"@angular/router\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"@angular/forms\";\nimport * as i12 from \"../../../shared/block-viewer/block-viewer.component\";\nimport * as i13 from \"../../../shared/prime/prime/calendar-agenda/calendar-agenda.component\";\nimport * as i14 from \"primeng/button\";\nimport * as i15 from \"primeng/api\";\nimport * as i16 from \"primeng/scrollpanel\";\nimport * as i17 from \"primeng/dropdown\";\nconst _c0 = [\"bookingWrapper\"];\nconst _c1 = [\"scrollPanel\"];\nconst _c2 = a0 => ({\n  \"height\": a0\n});\nconst _c3 = () => ({\n  width: \"100%\"\n});\nconst _c4 = (a0, a1) => ({\n  \"availability\": a0,\n  \"no-availability\": a1\n});\nconst _c5 = a0 => ({\n  \"pointer-events-none\": a0\n});\nfunction BookingSystemComponent_ng_container_15_ng_template_2_div_0_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 47);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"src\", ctx_r2.classroomService.getClassroomTypeSmallIconPath(ctx_r2.studentSelectedClassroom), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction BookingSystemComponent_ng_container_15_ng_template_2_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, BookingSystemComponent_ng_container_15_ng_template_2_div_0_img_1_Template, 1, 1, \"img\", 45);\n    i0.ɵɵelement(2, \"div\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.authService.isTeacher);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.classroomService.getStudentNamesWithHTML(ctx_r2.studentSelectedClassroom), i0.ɵɵsanitizeHtml);\n  }\n}\nfunction BookingSystemComponent_ng_container_15_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, BookingSystemComponent_ng_container_15_ng_template_2_div_0_Template, 3, 2, \"div\", 43);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.studentSelectedClassroom);\n  }\n}\nfunction BookingSystemComponent_ng_container_15_ng_template_3_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 47);\n  }\n  if (rf & 2) {\n    const classroom_r4 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.classroomService.getClassroomTypeSmallIconPath(classroom_r4), i0.ɵɵsanitizeUrl);\n  }\n}\nfunction BookingSystemComponent_ng_container_15_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44);\n    i0.ɵɵtemplate(1, BookingSystemComponent_ng_container_15_ng_template_3_img_1_Template, 1, 1, \"img\", 45);\n    i0.ɵɵelement(2, \"div\", 46)(3, \"br\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 48);\n    i0.ɵɵelement(5, \"img\", 49);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const classroom_r4 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.authService.isTeacher);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"innerHTML\", ctx_r2.classroomService.getStudentNamesWithHTML(classroom_r4, true), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.generalService.convertHoursToMinutesWithSuffix(ctx_r2.classroomService.getAccumulatedHoursLeft(classroom_r4.packages), true), \" available\");\n  }\n}\nfunction BookingSystemComponent_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p-dropdown\", 40);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BookingSystemComponent_ng_container_15_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.studentSelectedClassroom, $event) || (ctx_r2.studentSelectedClassroom = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function BookingSystemComponent_ng_container_15_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onClassroomChange($event));\n    });\n    i0.ɵɵtemplate(2, BookingSystemComponent_ng_container_15_ng_template_2_Template, 1, 1, \"ng-template\", 41)(3, BookingSystemComponent_ng_container_15_ng_template_3_Template, 7, 3, \"ng-template\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r2.availableClassrooms);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.studentSelectedClassroom);\n  }\n}\nfunction BookingSystemComponent_ng_container_16_ng_template_2_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 51);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.selectedTeachersLabel.language, \" - \", ctx_r2.selectedTeachersLabel.teacherName, \"\");\n  }\n}\nfunction BookingSystemComponent_ng_container_16_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, BookingSystemComponent_ng_container_16_ng_template_2_div_0_Template, 3, 2, \"div\", 43);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedTeachersLabel);\n  }\n}\nfunction BookingSystemComponent_ng_container_16_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵelement(1, \"img\", 53);\n    i0.ɵɵelementStart(2, \"div\", 51);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"br\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 48);\n    i0.ɵɵelement(6, \"img\", 49);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const teacher_r6 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"src\", \"/assets/images/dashboard/calendar/\", teacher_r6.icon, \".svg\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", teacher_r6.language, \" - \", teacher_r6.teacherName, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.generalService.convertHoursToMinutesWithSuffix(teacher_r6.hoursLeft, true), \" available\");\n  }\n}\nfunction BookingSystemComponent_ng_container_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"p-dropdown\", 50);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BookingSystemComponent_ng_container_16_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedTeachersLabel, $event) || (ctx_r2.selectedTeachersLabel = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"ngModelChange\", function BookingSystemComponent_ng_container_16_Template_p_dropdown_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onTeacherChange($event));\n    });\n    i0.ɵɵtemplate(2, BookingSystemComponent_ng_container_16_ng_template_2_Template, 1, 1, \"ng-template\", 41)(3, BookingSystemComponent_ng_container_16_ng_template_3_Template, 8, 5, \"ng-template\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r2.teachersLabels);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedTeachersLabel);\n  }\n}\nfunction BookingSystemComponent_div_39_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, ctx_r2.selectedDay, \"EEEE, dd/MM\"));\n  }\n}\nfunction BookingSystemComponent_div_39_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.selectedTimeSlot);\n  }\n}\nfunction BookingSystemComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 54)(2, \"div\", 55)(3, \"div\", 56);\n    i0.ɵɵtext(4, \"You have chosen a lesson for\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 57)(6, \"div\", 58)(7, \"div\", 59);\n    i0.ɵɵelement(8, \"i\", 60);\n    i0.ɵɵtemplate(9, BookingSystemComponent_div_39_span_9_Template, 3, 4, \"span\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\");\n    i0.ɵɵelement(11, \"i\", 61);\n    i0.ɵɵtemplate(12, BookingSystemComponent_div_39_span_12_Template, 2, 1, \"span\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\");\n    i0.ɵɵelement(14, \"i\", 62);\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(17, \"div\", 63)(18, \"div\", 64)(19, \"div\", 65);\n    i0.ɵɵelement(20, \"img\", 66);\n    i0.ɵɵtext(21, \"Add to Google Calendar \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 67);\n    i0.ɵɵelement(23, \"img\", 66);\n    i0.ɵɵtext(24, \"Add to Outlook Calendar \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 68)(26, \"button\", 69);\n    i0.ɵɵlistener(\"click\", function BookingSystemComponent_div_39_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onLessonRequested());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedDay);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedTimeSlot);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedDuration.viewValue);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"hidden\", true);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"loading\", ctx_r2.buttonLoading)(\"label\", ctx_r2.isRescheduling ? \"RESCHEDULE\" : \"REQUEST\");\n  }\n}\nfunction BookingSystemComponent_li_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r8 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind2(2, 2, day_r8, \"EEE\"), \" \", day_r8.getDate(), \"\");\n  }\n}\nfunction BookingSystemComponent_ng_container_57_a_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 75);\n    i0.ɵɵlistener(\"click\", function BookingSystemComponent_ng_container_57_a_9_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.generalService.navigateToBuyPackage());\n    });\n    i0.ɵɵtext(1, \" Purchase Package \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BookingSystemComponent_ng_container_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 70)(2, \"div\", 71);\n    i0.ɵɵelement(3, \"img\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 73);\n    i0.ɵɵtext(5, \" You have \");\n    i0.ɵɵelementStart(6, \"b\");\n    i0.ɵɵtext(7, \"0\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" hours available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, BookingSystemComponent_ng_container_57_a_9_Template, 2, 0, \"a\", 74);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.authService.isStudent);\n  }\n}\nfunction BookingSystemComponent_li_59_ul_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 76)(2, \"div\", 77)(3, \"label\", 78);\n    i0.ɵɵlistener(\"click\", function BookingSystemComponent_li_59_ul_1_ng_container_1_Template_label_click_3_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const hour_r11 = i0.ɵɵnextContext().$implicit;\n      const day_r12 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onTimeSlotSelected(day_r12, hour_r11));\n    });\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 79);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const hour_r11 = i0.ɵɵnextContext().$implicit;\n    const day_r12 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(14, _c4, ctx_r2.isTimeSlotAvailableOnDay(day_r12, hour_r11), !ctx_r2.isTimeSlotAvailableOnDay(day_r12, hour_r11) && ctx_r2.authService.isTeacher));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(17, _c5, !ctx_r2.isTimeSlotAvailableOnDay(day_r12, hour_r11)));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"checked\", ctx_r2.isSelectedDayAndTime(day_r12, hour_r11) && ctx_r2.radioChecked);\n    i0.ɵɵpropertyInterpolate2(\"for\", \"radio\", day_r12, \"\", hour_r11, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(hour_r11);\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate2(\"id\", \"radio\", day_r12, \"\", hour_r11, \"\");\n    i0.ɵɵpropertyInterpolate(\"value\", hour_r11);\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.isTimeSlotAvailableOnDay(day_r12, hour_r11))(\"checked\", ctx_r2.isSelectedDayAndTime(day_r12, hour_r11) && ctx_r2.radioChecked ? true : null);\n  }\n}\nfunction BookingSystemComponent_li_59_ul_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\");\n    i0.ɵɵtemplate(1, BookingSystemComponent_li_59_ul_1_ng_container_1_Template, 6, 19, \"ng-container\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const hour_r11 = ctx.$implicit;\n    const day_r12 = i0.ɵɵnextContext().$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isTimeSlotAvailableOnDay(day_r12, hour_r11) || ctx_r2.authService.isTeacher);\n  }\n}\nfunction BookingSystemComponent_li_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtemplate(1, BookingSystemComponent_li_59_ul_1_Template, 2, 1, \"ul\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.availableHours);\n  }\n}\nexport let BookingSystemComponent = /*#__PURE__*/(() => {\n  class BookingSystemComponent {\n    constructor(classroomService, generalService, authService, userService, calendarService, layoutService, lessonService, toastService, route, router, cdRef, location) {\n      this.classroomService = classroomService;\n      this.generalService = generalService;\n      this.authService = authService;\n      this.userService = userService;\n      this.calendarService = calendarService;\n      this.layoutService = layoutService;\n      this.lessonService = lessonService;\n      this.toastService = toastService;\n      this.route = route;\n      this.router = router;\n      this.cdRef = cdRef;\n      this.location = location;\n      this.bookingWrapper = {};\n      this.scrollPanel = {};\n      this.studentSelectedClassroom = {};\n      this.startDate = new Date();\n      this.weekDays = [];\n      this.hours = [];\n      this.schedule = {};\n      this.availableHours = [];\n      this.subs = new SubSink();\n      this.classrooms = [];\n      this.classroomsWithRecurringLessons = [];\n      this.hasClassrooms = true;\n      this.availableTeachers = [];\n      this.lessonEvents = [];\n      this.selectedTeacher = null;\n      this.filteredLessonEvents = [];\n      this.user = {};\n      this.teachersLabels = [];\n      this.selectedTeachersLabel = null;\n      this.selectedHour = null;\n      this.duration = [];\n      this.availableDuration = [];\n      this.selectedDuration = null;\n      this.cdr = {};\n      this.containerHeight = '';\n      this.availableClassrooms = [];\n      this.UserRoles = UserRole;\n      this.radioChecked = false;\n      this.calendarAgendaUserId = '';\n      this.extraData = {};\n      this.extraClassroomData = {};\n      this.isRescheduling = false;\n      this.isPending = false;\n      this.mobileView = false;\n      this.daysRange = 7;\n      this.height = 0;\n      this.noClassroomsFound = false;\n      this.buttonLoading = false;\n      this.startDate = new Date();\n    }\n    ngOnInit() {\n      const queryParams = this.route.snapshot.queryParams;\n      const lessonString = queryParams['lesson'];\n      const classroomString = queryParams['classroom'];\n      const hasRescheduleString = queryParams['reschedule'];\n      const answeredString = queryParams['answered'];\n      this.mobileView = this.isMobileScreenSize();\n      if (hasRescheduleString) {\n        this.isRescheduling = true;\n      }\n      if (answeredString) {\n        this.isPending = true;\n      }\n      if (lessonString) {\n        const lesson = JSON.parse(lessonString);\n        console.log(lesson);\n        this.extraData = {\n          \"lessonId\": lesson.id,\n          \"lessonStatus\": lesson.status,\n          \"classroomId\": lesson.classroomId,\n          \"status\": lesson.status,\n          \"startingDate\": lesson.startingDate,\n          \"duration\": lesson.duration,\n          \"isRecccuring\": lesson.isRecccuring\n        };\n      }\n      if (classroomString) {\n        const classroom = JSON.parse(classroomString);\n        console.log(classroom);\n        this.extraClassroomData = {\n          \"classroomId\": classroom.id,\n          \"status\": classroom.status,\n          \"classroomType\": classroom.type\n        };\n      }\n      this.duration = [{\n        value: 15,\n        viewValue: '15m'\n      }, {\n        value: 30,\n        viewValue: '30m'\n      }, {\n        value: 45,\n        viewValue: '45m'\n      }, {\n        value: 60,\n        viewValue: '1h'\n      }, {\n        value: 75,\n        viewValue: '1h 15m'\n      }, {\n        value: 90,\n        viewValue: '1h 30m'\n      }, {\n        value: 105,\n        viewValue: '1h 45m'\n      }, {\n        value: 120,\n        viewValue: '2h'\n      }, {\n        value: 135,\n        viewValue: '2h 15m'\n      }, {\n        value: 150,\n        viewValue: '2h 30m'\n      }, {\n        value: 165,\n        viewValue: '2h 45m'\n      }, {\n        value: 180,\n        viewValue: '3h'\n      }];\n      this.availableDuration = [...this.duration];\n      this.selectedDuration = this.duration[3];\n      this.calculateWeekDays(new Date());\n      this.calculateHours();\n      this.initSchedule();\n      this.user = this.authService.getLoggedInUser();\n      this.prepareInitialData();\n    }\n    isMobileScreenSize() {\n      const screenWidth = window.innerWidth;\n      return screenWidth < 768;\n    }\n    isTabletScreenSize() {\n      const screenWidth = window.innerWidth;\n      return screenWidth < 1024; // Change the width here to the desired tablet width\n    }\n    ngOnChanges() {\n      this.mobileView = this.isMobileScreenSize();\n      console.log(this.mobileView);\n    }\n    ngAfterViewInit() {\n      this.subs.sink = this.generalService.deviceKind.pipe(take(2), switchMap(res => {\n        console.log(res);\n        if (res.is576 || res.is992) {\n          return of(0);\n        } else {\n          return this.layoutService.sideMenuHeight;\n        }\n      }), startWith(0)).subscribe(height => {\n        if (height !== 0) {\n          this.setDaysRange(7);\n          this.height = height;\n          this.bookingWrapper.nativeElement.style.height = height + 30 + 'px';\n        } else {\n          this.setDaysRange(3);\n          this.bookingWrapper.nativeElement.style.height = '100%';\n        }\n        this.cdRef.detectChanges(); // Manually trigger change detection\n      });\n      // this.subs.sink = this.layoutService.sideMenuHeight.subscribe((res) => {\n      //   this.bookingWrapper.nativeElement.style.height = (res + 'px');\n      // });\n      this.scrollToMiddle();\n    }\n    ngAfterContentInit() {}\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n    }\n    setDaysRange(days) {\n      this.daysRange = days;\n    }\n    getDaysRange() {\n      return this.daysRange;\n    }\n    scrollToMiddle() {\n      setTimeout(() => {\n        const scrollContent = this.scrollPanel.containerViewChild.nativeElement.getElementsByClassName('p-scrollpanel-content')[0];\n        const middleScrollPosition = scrollContent.scrollHeight / 3;\n        scrollContent.scrollTop = middleScrollPosition;\n      }, 1000);\n    }\n    onDurationSelected(event) {\n      this.selectedTimeSlot = '';\n      this.radioChecked = false;\n      console.log(this.selectedDay);\n      // Add your own logic here to handle the selected duration\n    }\n    onTimeSlotSelected(day, hour) {\n      this.selectedDay = moment(day, \"ddd MMM DD YYYY\").toDate();\n      this.selectedTimeSlot = hour;\n      this.displayAvailableTimeSlots();\n      const selectedDateTime = moment(day, \"ddd MMM DD YYYY HH:mm:ss [GMT]ZZ\");\n      const formattedDateTime = selectedDateTime.format(\"ddd MMM DD YYYY \" + hour);\n      // TODO : implement uncheck all radios functionality\n      // if (this.radioChecked && this.timeSlotRadio.checked) {\n      //   if (this.timeSlotRadio) {\n      //     this.timeSlotRadio.checked = false;\n      //   }\n      //   this.resetSelectedTimeSlot();\n      //   return;\n      // }\n      if (this.user.role === UserRole.TEACHER) {\n        this.radioChecked = true;\n      } else {\n        if (!this.isDateTimeInThePast(new Date(formattedDateTime))) {\n          this.radioChecked = true;\n        } else {\n          this.toastService.setShowToastmessage({\n            severity: 'info',\n            summary: '',\n            detail: 'Date is in the past. Please choose another date.'\n          });\n          this.radioChecked = false;\n        }\n      }\n    }\n    resetSelectedTimeSlot() {\n      this.selectedDay = new Date();\n      this.selectedTimeSlot = '';\n      this.displayAvailableTimeSlots();\n      this.radioChecked = false;\n    }\n    isSelectedDayAndTime(day, hour) {\n      const d = new Date(day);\n      if (this.hasExtraData()) {\n        return d.getDate() + '' + d.getMonth() === this.selectedDay.getDate() + '' + this.selectedDay.getMonth() && hour === this.selectedTimeSlot;\n      } else {\n        return day === this.selectedDay && hour === this.selectedTimeSlot;\n      }\n    }\n    /**\n     * The function `getTeacherAvailability` retrieves the calendar availability of a teacher based on\n     * their ASP user ID.\n     * @param {any} teacher - The `teacher` parameter is an object that represents a teacher. It contains\n     * information about the teacher, such as their ID (`aspUserId`).\n     * @returns an Observable of type RetrieveCalendarAvailabilityRequestData or null.\n     */\n    getTeacherAvailability(teacher) {\n      if (!teacher || this.generalService.isNullishObject(teacher)) {\n        return of(null);\n      }\n      const data = {\n        ...this.getDatesRange(),\n        userId: teacher.aspUserId\n      };\n      return this.getAvailabilityFromRange(data);\n    }\n    getAvailabilityFromRange(requestData) {\n      console.log(requestData);\n      return this.calendarService.retrieveCalendarAvailability(requestData).pipe(map(res => {\n        // const daysOff = res.daysOff;\n        // // Set business hours and select constraint\n        this.businessHours = res.calendarAvailability;\n        this.availableHours = this.displayAvailableTimeSlots();\n        // // Create events for each day off\n        // this.dayOffEvents = this.calendarService.createDayOffEvents(daysOff);\n        return null;\n      }));\n    }\n    filterLessonsByTeacherId(lessons, teacherId) {\n      return lessons.filter(lesson => lesson.teacher.id === teacherId);\n    }\n    onTeacherChange(event) {\n      // Reset selected time slot and radio checked status\n      this.selectedTimeSlot = '';\n      this.radioChecked = false;\n      // Find the selected teacher based on the teacherId\n      this.selectedTeacher = this.availableTeachers.find(item => item.id === event.teacherId);\n      // Find the classroom based on the selected teacher's classroom label\n      const classroomId = this.availableClassrooms.find(item => item.id === this.selectedTeachersLabel.classroom);\n      console.log(classroomId);\n      // Handle the available duration for the classroom type\n      this.handleClassroomTypeAvailableDuration(classroomId);\n      // Get the teacher's availability and update the filtered lesson events\n      this.subs.add(this.getTeacherAvailability(this.selectedTeacher).subscribe(res => {\n        this.filteredLessonEvents = this.lessonEvents;\n        console.log('filteredLessonEvents', this.filteredLessonEvents);\n      }));\n      // Set the calendar agenda user ID to the selected teacher's ASP user ID\n      this.calendarAgendaUserId = this.selectedTeacher.aspUserId;\n    }\n    /**\n     * Handles the change event when a classroom is selected.\n     * It sets the selected classroom and updates the calendar agenda user ID if a teacher is associated with the classroom.\n     * It also handles the available duration based on the classroom type.\n     *\n     * @param event - The selected classroom object.\n     */\n    onClassroomChange(event) {\n      console.log(event);\n      this.studentSelectedClassroom = event;\n      console.log(event);\n      if (this.studentSelectedClassroom.teacher) {\n        this.calendarAgendaUserId = this.studentSelectedClassroom.teacher?.aspUserId;\n      }\n      this.handleClassroomTypeAvailableDuration(event);\n    }\n    getTeacherLabel(teacher) {\n      return ` ${teacher.lastName} ${teacher.firstName}`;\n    }\n    /**\n     * Updates the selected classroom when a user chooses a classroom from the UI.\n     * @param event - The change event triggered by selecting a classroom.\n     */\n    /**\n     * Calculates the dates of the current week, starting from the given start date.\n     * @param startDate - The start date for the current week.\n     */\n    calculateWeekDays(startDate) {\n      const today = moment(startDate).toDate();\n      const firstDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());\n      const firstDayCopy = new Date(firstDay);\n      this.weekDays = Array.from({\n        length: this.daysRange\n      }, (_, i) => {\n        const d = new Date(firstDayCopy);\n        d.setDate(d.getDate() + i);\n        return d;\n      });\n    }\n    /**\n     * Returns the date range for the current week.\n     * @returns {Object} An object with \"from\" and \"to\" properties representing the start and end dates of the week.\n     */\n    getDatesRange() {\n      // Convert date strings to Date objects\n      const dates = this.weekDays.map(dateString => new Date(dateString));\n      // Get the range of dates from and to\n      const fromDate = dates[0];\n      const toDate = dates[dates.length - 1];\n      // console.log(fromDate);\n      // Log the range of dates\n      return {\n        fromDate: moment(fromDate, 'ddd MMM D YYYY HH:mm:ss [GMT]ZZ').format('YYYY-MM-DD') + 'T09:29:27.434Z',\n        toDate: moment(toDate, 'ddd MMM D YYYY HH:mm:ss [GMT]ZZ').format('YYYY-MM-DD') + 'T09:29:27.434Z'\n      };\n    }\n    findMonthFromDateRange() {\n      const data = this.getDatesRange();\n      const fromDate = moment(data.fromDate);\n      const toDate = moment(data.toDate);\n      const fromMonth = fromDate.format('MMMM');\n      const toMonth = toDate.format('MMMM');\n      const fromYear = fromDate.format('YYYY');\n      const toYear = toDate.format('YYYY');\n      if (fromMonth === toMonth) {\n        return `${fromMonth} ${fromYear}`;\n      } else {\n        return `${fromMonth} - ${toMonth} ${toYear}`;\n      }\n    }\n    displayAvailableTimeSlots() {\n      const weekDayAbbreviations = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];\n      const availableHours = [];\n      const startHour = 0;\n      const endHour = 23;\n      const minuteIncrements = ['00', '15', '30', '45'];\n      for (let hour = startHour; hour <= endHour; hour++) {\n        const hourString = hour.toString().padStart(2, '0');\n        for (const minute of minuteIncrements) {\n          const timeString = `${hourString}:${minute}`;\n          let isAvailable = true;\n          // Check availability for each day\n          // for (const day of this.weekDays) {\n          //   const dayAbbr = weekDayAbbreviations[day.getDay()];\n          //   if (this.isTimeSlotAvailableOnDay(day.toDateString(), timeString)) {\n          //     isAvailable = true;\n          //   }\n          // }\n          if (isAvailable) {\n            availableHours.push(timeString);\n          }\n        }\n      }\n      return availableHours;\n    }\n    /**\n     * Calculates the available hours for scheduling.\n     */\n    calculateHours() {\n      const startHour = 0;\n      const endHour = 23;\n      const quarterHours = ['00', '15', '30', '45'];\n      this.hours = [];\n      for (let hour = startHour; hour <= endHour; hour++) {\n        const hourString = hour.toString().padStart(2, '0');\n        for (const minute of quarterHours) {\n          const timeString = `${hourString}:${minute}`;\n          this.hours.push(timeString);\n        }\n      }\n      for (let hour = 0; hour < startHour; hour++) {\n        const hourString = hour.toString().padStart(2, '0');\n        for (const minute of quarterHours) {\n          const timeString = `${hourString}:${minute}`;\n          this.hours.push(timeString);\n        }\n      }\n    }\n    convertHoursToMinutes(hours) {\n      return hours * 60;\n    }\n    removeValuesGreaterThanHoursLeft(hours) {\n      const hoursLeft = hours; // Hours left in Classroom for the selected teacher\n      const minutesLeft = this.convertHoursToMinutes(hoursLeft);\n      const maxDuration = this.convertHoursToMinutes(3); // Maximum duration of 3 hours\n      console.log(hoursLeft);\n      this.availableDuration = this.duration.filter(item => item.value <= minutesLeft && item.value <= maxDuration);\n    }\n    /**\n     * The function initializes a schedule by setting time slots from the minimum hour and minute to the\n     * end hour and minute in 15-minute intervals.\n     */\n    initSchedule() {\n      const startMinute = 0;\n      const endMinute = 45;\n      let minHour = 23;\n      let minMinute = 59;\n      for (const day in this.schedule) {\n        const dayObj = this.schedule[day];\n        for (const hour in dayObj) {\n          if (dayObj[hour]) {\n            const [hourStr, minuteStr] = hour.split(':');\n            const hourNum = parseInt(hourStr);\n            const minuteNum = parseInt(minuteStr);\n            if (hourNum < minHour || hourNum === minHour && minuteNum < minMinute) {\n              minHour = hourNum;\n              minMinute = minuteNum;\n            }\n          }\n        }\n      }\n      const startHour = minHour;\n      const endHour = 23;\n      for (let i = startHour; i <= endHour; i++) {\n        const hour = i.toString().padStart(2, '0');\n        for (let j = 0; j < 4; j++) {\n          let minute = j * 15 + startMinute;\n          if (minute >= 60) {\n            minute = 45;\n          }\n          if (i === endHour && minute > endMinute || i === startHour && minute < minMinute) {\n            continue;\n          }\n          const timeString = `${hour}:${minute.toString().padStart(2, '0')}`;\n          for (const day in this.schedule) {\n            this.schedule[day][timeString] = true;\n          }\n        }\n      }\n    }\n    /**\n     * Toggles the availability of a time slot on a specific day.\n     * @param day - The date object representing the day to toggle.\n     * @param hour - The hour string representing the time slot to toggle.\n     */\n    toggleSchedule(day, hour) {\n      const dateString = day.toDateString();\n      const isAvailable = !this.schedule[dateString]?.[hour];\n      this.schedule[dateString] = this.schedule[dateString] || {};\n      this.schedule[dateString][hour] = isAvailable;\n    }\n    /**\n     * Moves the calendar view back one week and recalculates the dates of the new week.\n     */\n    goToPrevWeek() {\n      this.startDate.setDate(this.startDate.getDate() - this.daysRange);\n      this.startDate = new Date(this.startDate);\n      console.log('prev', this.startDate);\n      this.calculateWeekDays(this.startDate);\n      this.subs.sink = this.getTeacherAvailability(this.selectedTeacher).subscribe(res => {\n        console.log(res);\n      });\n    }\n    /**\n     * Moves the calendar view forward one week and recalculates the dates of the new week.\n     */\n    goToNextWeek() {\n      this.startDate.setDate(this.startDate.getDate() + this.daysRange);\n      this.startDate = new Date(this.startDate);\n      this.calculateWeekDays(this.startDate);\n      this.subs.sink = this.getTeacherAvailability(this.selectedTeacher).subscribe(res => {});\n    }\n    /**\n     * Gets the availability status of a specific time slot on a specific day.\n     * @param dayString - The string representation of the date to check.\n     * @param hour - The hour string representing the time slot to check.\n     * @returns A boolean indicating whether the time slot is available or not.\n     */\n    isTimeSlotAvailableOnDay(dayString, hour) {\n      const day = moment(dayString, \"ddd MMM DD YYYY HH:mm:ss ZZ\");\n      const dayFormatted = day.format(\"DD/MM/YYYY\");\n      const dayAbbr = day.format(\"ddd\").toLowerCase();\n      if (this.businessHours) {\n        const dayObj = this.businessHours.find(a => a.date === dayFormatted);\n        // This code checks if the role of the user is equal to the 'TEACHER' role.\n        if (this.user.role === UserRole.TEACHER) {\n          return true;\n        }\n        if (dayObj && dayObj.availability.length > 0) {\n          const availableHours = dayObj.availability;\n          let hourIndex = this.hours.indexOf(hour);\n          const durationInMinutes = this.selectedDuration.value;\n          for (let i = 0; i < availableHours.length; i++) {\n            const range = availableHours[i];\n            let start = Math.max(this.hours.indexOf(range.from), hourIndex);\n            const end = range.to === \"00:00\" ? this.hours.length - 1 : this.hours.indexOf(range.to);\n            if (start >= 0 && end >= 0 && start <= hourIndex && hourIndex <= end) {\n              const availableDuration = (end - start + (range.to === \"00:00\" ? 2 : 1)) * 14;\n              if (availableDuration >= durationInMinutes) {\n                return true;\n              }\n            }\n          }\n        }\n      }\n      return false;\n    }\n    /**\n    * Creates a new lesson based on user input and sends it to backend.\n    *\n    * @returns void\n    */\n    onLessonRequested() {\n      // Check if a timeslot is selected\n      if (this.selectedTimeSlot === '') {\n        this.toastService.setShowToastmessage({\n          severity: 'info',\n          summary: '',\n          detail: 'Please select a timeslot'\n        });\n        return;\n      }\n      // Get the output date\n      const outputDate = this.getOutputDate();\n      // Get the current time\n      const currentTime = moment().startOf('hour');\n      // Get the UTC date\n      const utcDate = moment(this.selectedDay);\n      // Format the date\n      const formattedDate = utcDate.format('ddd MMM DD YYYY HH:mm:ss ZZ');\n      if (!this.isTimeSlotAvailableOnDay(formattedDate, this.selectedTimeSlot)) {\n        this.toastService.setShowToastmessage({\n          severity: 'info',\n          summary: '',\n          detail: 'Please select a correct timeslot'\n        });\n        return;\n      }\n      // Prevent button spam\n      if (this.buttonLoading) {\n        return;\n      }\n      // Set the button loading state to true\n      this.buttonLoading = true;\n      // Set the default lesson status\n      let status = LessonStatus.REQUESTED;\n      // Check if the user is a teacher and has extra classroom data\n      if (this.user.role === UserRole.TEACHER) {\n        if (this.hasExtraClassroomData()) {\n          if (this.extraClassroomData.status.toLowerCase().includes(ClassroomType.TRIAL.toLowerCase())) {\n            status = LessonStatus.ARRANGED_TRIAL;\n          } else {\n            status = LessonStatus.ARRANGED;\n          }\n        } else {\n          const matchingClassroom = this.availableClassrooms.find(classroom => {\n            return classroom.id === this.studentSelectedClassroom?.id;\n          });\n          if (matchingClassroom.type.toLowerCase().includes(ClassroomType.TRIAL.toLowerCase())) {\n            status = LessonStatus.ARRANGED_TRIAL;\n          } else {\n            status = LessonStatus.ARRANGED;\n          }\n        }\n      }\n      let classroomId;\n      // Check if the user is a student\n      if (this.user.role === UserRole.STUDENT) {\n        const matchingClassroom = this.availableClassrooms.find(classroom => {\n          return classroom.teacher.id === this.selectedTeachersLabel.teacherId && classroom.language === this.selectedTeachersLabel.language;\n        });\n        [classroomId] = matchingClassroom ? [matchingClassroom.id] : [null];\n      } else {\n        [classroomId] = [this.studentSelectedClassroom?.id];\n      }\n      // Create the lesson object\n      const lesson = {\n        classroomId,\n        status,\n        startingDate: this.generalService.formatSelectedDateTimeForLesson(this.selectedDay, this.selectedTimeSlot),\n        duration: this.generalService.convertMinutesToHours(this.selectedDuration.value),\n        isRecccuring: false\n      };\n      // Check if it is a rescheduling\n      if (this.isRescheduling) {\n        let lessonUpdate = {\n          id: this.extraData.lessonId,\n          classroomId,\n          status: this.isPending ? LessonStatus.ARRANGED : this.extraData.status,\n          startingDate: this.generalService.formatSelectedDateTimeForLesson(this.selectedDay, this.selectedTimeSlot),\n          duration: this.generalService.convertMinutesToHours(this.selectedDuration.value),\n          isRecccuring: false,\n          answered: this.isPending ? \"requested\" : \"accepted\"\n        };\n        console.log(lessonUpdate);\n        this.subs.sink = this.lessonService.update(lessonUpdate).subscribe(res => {\n          console.log(res);\n          this.toastService.setShowToastmessage({\n            severity: 'success',\n            summary: '',\n            detail: 'Lesson Rescheduled for ' + moment.parseZone(this.generalService.formatSelectedDateTimeForLesson(this.selectedDay, this.selectedTimeSlot)).format('DD-MM-YYYY HH:mm')\n          });\n          this.location.back();\n          // this.router.navigate(['/dashboard/calendar']);\n        }, error => {\n          this.buttonLoading = false;\n        });\n      } else {\n        const extraTimeString = moment.parseZone(this.generalService.formatSelectedDateTimeForLesson(this.selectedDay, this.selectedTimeSlot)).format('DD-MM-YYYY HH:mm');\n        this.subs.sink = this.lessonService.create(lesson).subscribe(res => {\n          console.log(res);\n          this.toastService.setShowToastmessage({\n            severity: 'success',\n            summary: '',\n            detail: status === LessonStatus.ARRANGED ? 'Lesson Created for ' + extraTimeString : 'Lesson Requested for ' + extraTimeString\n          });\n          this.location.back();\n        }, error => {\n          this.buttonLoading = false;\n        });\n      }\n    }\n    prepareInitialData() {\n      this.subs.sink = this.classroomService.getLMSUserClassrooms(this.user.id).pipe(switchMap(classrooms => {\n        // sort classrooms first\n        classrooms = this.classroomService.sortClassroomsByFirstName(classrooms);\n        return this.addTeacherLabelToArrays(classrooms);\n        //TODO: remove when database is updated - START\n        // const modifiedArray = Object.values(this.teachersLabels.reduce((acc, { teacherId, hoursLeft, ...rest }) => {\n        //   if (acc[teacherId]) {\n        //     acc[teacherId].hoursLeft += hoursLeft;\n        //   } else {\n        //     acc[teacherId] = { teacherId, hoursLeft, ...rest };\n        //   }\n        //   return acc;\n        // }, {}));\n        // this.teachersLabels = modifiedArray;\n        //TODO: remove when database is updated - END\n        // const uniqueTeachers = [...new Set(this.availableTeachers.map(t => t.id))].map(id => this.availableTeachers.find(t => t.id === id));\n        // this.availableTeachers = uniqueTeachers;\n        if (this.user.role === UserRole.TEACHER) {\n          this.studentSelectedClassroom = this.availableClassrooms[0];\n          this.calendarAgendaUserId = this.studentSelectedClassroom.teacher?.aspUserId;\n        } else {\n          this.selectedTeacher = this.availableTeachers[0];\n          this.selectedTeachersLabel = this.teachersLabels[0];\n          const classroomId = this.availableClassrooms.find(item => item.id === this.selectedTeachersLabel.classroom);\n          this.removeValuesGreaterThanHoursLeft(classroomId.hoursLeft);\n          this.calendarAgendaUserId = this.selectedTeacher.aspUserId;\n        }\n        return this.getTeacherAvailability(this.availableTeachers[0]);\n      })).subscribe(res => {\n        console.log(res);\n        // this.filteredLessonEvents = this.lessonEvents;\n      });\n    }\n    /**\n     * Formats a date object to a string in the format 'dddd DD/MM'.\n     * @param {Date} date - The date object to format.\n     * @returns {string} The formatted date string.\n     */\n    formatDateWithDay(date) {\n      const dateString = date.toDateString();\n      const momentDate = moment(dateString);\n      return momentDate.format('dddd DD/MM');\n    }\n    onAgendaDayClicked(event) {\n      console.log(event);\n      this.calculateWeekDays(event);\n      this.subs.add(this.getTeacherAvailability(this.selectedTeacher).subscribe(res => {\n        this.filteredLessonEvents = this.lessonEvents;\n        console.log('filteredLessonEvents', this.filteredLessonEvents);\n      }));\n    }\n    /**\n     * The function adds teacher labels to arrays of classrooms and performs various initialization tasks.\n     * @param {Classroom[]} classrooms - An array of Classroom objects.\n     * @returns the result of the `getTeacherAvailability` function with the first element of the\n     * `availableTeachers` array as the argument.\n     */\n    addTeacherLabelToArrays(classrooms) {\n      if (this.hasExtraData()) {\n        const classroom = this.getClassroomById(classrooms);\n        if (!classroom) {\n          console.log(`No teacher label found for classroom`);\n          return undefined;\n        }\n        this.addTeacherLabel(classroom);\n        this.addAvailableClassroom(classroom);\n        console.log(classroom);\n        this.addAvailableTeacher(classroom.teacher);\n      } else {\n        const events = this.getAvailableClassroomEvents(classrooms);\n        console.log(`No teacher label found for classroom`, events);\n        this.lessonEvents.push(...events);\n      }\n      this.setInitialValuesForUserRole();\n      this.initializeStartDateAndTime();\n      this.initializeWeekDays();\n      if (this.authService.isTeacher) {\n        if (!this.studentSelectedClassroom) {\n          this.noClassroomsFound = true;\n          return of(null);\n        }\n      }\n      this.initializeCalendarAgendaUserId();\n      return this.getTeacherAvailability(this.availableTeachers[0]);\n    }\n    /**\n     * The function `getClassroomById` takes an array of `Classroom` objects and returns the `Classroom`\n     * object with a matching `id` property, or `undefined` if no match is found.\n     * @param {Classroom[]} classrooms - An array of Classroom objects.\n     * @returns a Classroom object or undefined.\n     */\n    getClassroomById(classrooms) {\n      const classroomId = this.extraData.classroomId;\n      return classrooms.find(classroom => classroom.id === classroomId);\n    }\n    /**\n     * The addTeacherLabel function adds a teacher label to the teachersLabels array for a given classroom.\n     * @param {Classroom} classroom - The classroom parameter is an object that represents a classroom.\n     */\n    addTeacherLabel(classroom) {\n      const icon = this.getTeacherLabelIcon(classroom);\n      const teachersLabel = this.createTeachersLabel(classroom, icon);\n      this.teachersLabels.push(teachersLabel);\n    }\n    /**\n     * The function adds a classroom to the list of available classrooms.\n     * @param {Classroom} classroom - The parameter \"classroom\" is of type \"Classroom\".\n     */\n    addAvailableClassroom(classroom) {\n      this.availableClassrooms.push(classroom);\n    }\n    /**\n     * The function adds a teacher to the list of available teachers.\n     * @param {User} teacher - The \"teacher\" parameter is of type \"User\".\n     */\n    addAvailableTeacher(teacher) {\n      this.availableTeachers.push(teacher);\n    }\n    /**\n     * The function sets initial values for user role based on certain conditions and handles classroom\n     * selection.\n     */\n    setInitialValuesForUserRole() {\n      if (this.isRescheduling) {\n        this.selectedDuration = this.duration.find(duration => duration.value === this.extraData.duration * 60);\n      } else {\n        this.selectedDuration = this.duration[3];\n      }\n      if (this.hasExtraClassroomData()) {\n        this.studentSelectedClassroom = this.availableClassrooms.find(item => item.id === this.extraClassroomData.classroomId);\n        if (this.studentSelectedClassroom) {\n          this.removeValuesGreaterThanHoursLeft(this.classroomService.getAccumulatedHoursLeft(this.studentSelectedClassroom.packages));\n        }\n        if (!this.studentSelectedClassroom) {\n          this.toastService.setShowToastmessage({\n            severity: 'info',\n            summary: '',\n            detail: 'Not enough hours for previously selected classroom. Please select another.'\n          });\n          this.handleSelectedClassroomForUserRole();\n        } else {\n          this.selectedTeacher = this.studentSelectedClassroom.teacher;\n          this.selectedTeachersLabel = this.teachersLabels.find(item => item.classroom === this.studentSelectedClassroom.id);\n        }\n      } else {\n        if (this.user.role === UserRole.TEACHER) {\n          this.studentSelectedClassroom = this.availableClassrooms[0];\n          this.selectedTeacher = this.user;\n        } else {\n          this.handleSelectedClassroomForUserRole();\n        }\n      }\n    }\n    /**\n     * The function handles the selection of a classroom based on the user's role and performs additional\n     * operations based on the selected classroom.\n     */\n    handleSelectedClassroomForUserRole() {\n      if (this.user.role === UserRole.TEACHER) {\n        this.studentSelectedClassroom = this.availableClassrooms[0];\n        this.selectedTeacher = this.user;\n      } else {\n        console.log(this.availableTeachers);\n        this.selectedTeacher = this.availableTeachers[0];\n        this.selectedTeachersLabel = this.teachersLabels[0];\n        const classroom = this.availableClassrooms.find(item => item.id === this.selectedTeachersLabel.classroom);\n        if (classroom) {\n          if (!this.isRescheduling) {\n            console.log(classroom);\n            this.handleClassroomTypeAvailableDuration(classroom);\n            this.availableDuration = this.duration.filter(duration => {\n              return this.generalService.convertMinutesToHours(duration.value) <= this.classroomService.getAccumulatedHoursLeft(classroom.packages);\n            });\n            // this.removeValuesGreaterThanHoursLeft(classroom.packages[0].hoursLeft);\n          }\n        }\n      }\n    }\n    /**\n     * The function `handleClassroomTypeAvailableDuration` sets the available duration options for a\n     * classroom based on its type and removes any options that exceed the remaining hours.\n     * @param {Classroom} classroom - The \"classroom\" parameter is an object of type Classroom.\n     */\n    handleClassroomTypeAvailableDuration(classroom) {\n      if (this.classroomService.isTrialClassroom(classroom)) {\n        const trialDuration = this.duration[3];\n        this.availableDuration = [trialDuration];\n        this.selectedDuration = trialDuration;\n      } else {\n        this.availableDuration = [...this.duration];\n        this.selectedDuration = this.duration[3];\n        const accumulatedHoursLeft = this.classroomService.getAccumulatedHoursLeft(classroom.packages);\n        this.removeValuesGreaterThanHoursLeft(accumulatedHoursLeft);\n      }\n    }\n    onCurrentMonthChanged(monthNumber) {\n      const outputDate = this.getOutputDate();\n      console.log(outputDate);\n      // this.selectedDay = undefined;\n      // const currentTime = moment().startOf('hour');\n      // const time = currentTime.format('HH:00');\n      // const utcDate = moment(outputDate);\n      // // Fri Jul 14 2023 00:00:00 GMT+0300 (Eastern European Summer Time)\n      // const formattedDate = utcDate.format('ddd MMM DD YYYY HH:mm:ss [GMT]ZZ (z)');\n      // if (this.isTimeSlotAvailableOnDay(formattedDate, this.selectedTimeSlot)) {\n      //   this.onTimeSlotSelected(outputDate, time);\n      // } else {\n      //   this.selectedTimeSlot = '';\n      //   this.radioChecked = false;\n      // }\n    }\n    initializeStartDateAndTime() {\n      const outputDate = this.getOutputDate();\n      this.startDate = new Date(outputDate);\n      const inputDate = this.extraData.startingDate;\n      if (!this.isRescheduling) {\n        // (TODO) init an hour to help the ui \n        // const currentTime = moment().add(2, 'hour').startOf('hour');\n        // const time = currentTime.format('HH:00');\n        // this.onTimeSlotSelected(outputDate, time);\n      } else {\n        const selectedTime = moment(inputDate).format('HH:mm');\n        this.onTimeSlotSelected(outputDate, selectedTime);\n      }\n    }\n    /**\n     * The function initializes the week days by calculating them based on the output date.\n     */\n    initializeWeekDays() {\n      const outputDate = this.getOutputDate();\n      this.calculateWeekDays(outputDate);\n    }\n    /**\n     * The function takes in an array of classrooms and returns an array of lesson events for the available\n     * classrooms.\n     * @param {Classroom[]} classrooms - An array of Classroom objects.\n     * @returns an array of Lesson objects.\n     */\n    getAvailableClassroomEvents(classrooms) {\n      return this.classroomService.filterAvailableClassrooms(classrooms).flatMap(classroom => {\n        const teachersLabel = this.createTeachersLabelForEvent(classroom);\n        this.teachersLabels.push(teachersLabel);\n        this.availableClassrooms.push(classroom);\n        this.availableTeachers.push(classroom.teacher);\n        return classroom.lessons.map(lesson => this.createLessonEvent(lesson, classroom));\n      });\n    }\n    /**\n     * The function creates a label for teachers based on the given classroom information.\n     * @param {Classroom} classroom - The \"classroom\" parameter is an object of type \"Classroom\".\n     * @returns an object with the following properties:\n     */\n    createTeachersLabelForEvent(classroom) {\n      let icon = 'student';\n      if (classroom.type && classroom.type.includes('Trial')) {\n        icon = 'blue-plane';\n      }\n      if (classroom.type && classroom.type.includes('Gift')) {\n        icon = 'gift-box';\n      }\n      return {\n        language: classroom.language,\n        teacherName: classroom.teacher && classroom.teacher.firstName,\n        icon,\n        hoursLeft: classroom.packages && classroom.packages.length > 0 ? this.classroomService.getAccumulatedHoursLeft(classroom.packages) : classroom.hoursLeft,\n        teacherId: classroom.teacher && classroom.teacher.id,\n        classroom: classroom.id,\n        classroomType: classroom.type\n      };\n    }\n    /**\n     * The function creates a lesson event object with specific properties based on the given lesson and\n     * classroom data.\n     * @param {any} lesson - The lesson parameter is an object that represents a lesson. It contains\n     * properties such as startingDate (the date and time the lesson starts), duration (the duration of the\n     * lesson in hours), and id (a unique identifier for the lesson).\n     * @param {Classroom} classroom - The `classroom` parameter is an object that represents a classroom.\n     * It likely contains information such as the classroom name, teacher, students, and other relevant\n     * details.\n     * @returns an object with the following properties:\n     * - id: the id of the lesson\n     * - title: the short full name of the teacher\n     * - start: the starting date and time of the lesson (with the time set to 12:00:00)\n     * - end: the ending date and time of the lesson (calculated based on the starting date and the\n     * duration of the lesson\n     */\n    createLessonEvent(lesson, classroom) {\n      const start = new Date(lesson.startingDate);\n      const end = new Date(start.getTime() + lesson.duration * 60 * 60 * 1000);\n      const {\n        teacher\n      } = classroom;\n      const title = this.generalService.getShortFullName(teacher);\n      return {\n        id: lesson.id,\n        title,\n        start: lesson.startingDate.slice(0, 11) + '12:00:00',\n        end,\n        allDay: false,\n        backgroundColor: '#378006',\n        borderColor: '#378006',\n        textColor: '#ffffff',\n        extendedProps: {\n          type: 'lesson',\n          lesson,\n          classRoom: classroom\n        },\n        editable: false\n      };\n    }\n    /**\n     * The function initializes the calendar agenda user ID based on the user's role and selected classroom\n     * or teacher.\n     */\n    initializeCalendarAgendaUserId() {\n      if (this.authService.isTeacher) {\n        console.log(this.studentSelectedClassroom);\n        if (!this.studentSelectedClassroom) {\n          this.showToastMessage('info', '', 'No available classrooms for booking');\n        }\n        this.calendarAgendaUserId = this.getTeacherAspUserId();\n      } else if (this.authService.isStudent && this.selectedTeacher) {\n        this.calendarAgendaUserId = this.selectedTeacher.aspUserId;\n      } else {\n        this.showToastMessage('info', '', 'No available classrooms for booking or enrolled classrooms have no available hours.');\n        this.goBack();\n      }\n    }\n    showToastMessage(severity, summary, detail) {\n      this.toastService.setShowToastmessage({\n        severity: severity,\n        summary: summary,\n        detail: detail\n      });\n    }\n    getTeacherAspUserId() {\n      return this.studentSelectedClassroom.teacher?.aspUserId;\n    }\n    goBack() {\n      this.location.back();\n    }\n    getTeacherLabelIcon(teacherLabel) {\n      let icon = 'student';\n      if (teacherLabel.status.includes('Trial')) {\n        icon = 'blue-plane';\n      } else if (teacherLabel.status.includes('Gift')) {\n        icon = 'gift-box';\n      }\n      return icon;\n    }\n    createTeachersLabel(teacherLabel, icon) {\n      const {\n        language,\n        teacher,\n        id,\n        hoursLeft\n      } = teacherLabel;\n      console.log(teacherLabel);\n      return {\n        language,\n        teacherName: teacher.firstName,\n        icon,\n        hoursLeft,\n        teacherId: teacher.id,\n        classroom: id\n      };\n    }\n    getOutputDate() {\n      const outputFormat = \"ddd MMM DD YYYY HH:mm:ss [GMT]ZZ (z)\";\n      const inputDate = this.extraData.startingDate;\n      const outputDateStr = moment.utc(inputDate).startOf('day').local().format(outputFormat);\n      const outputDate = new Date(Date.parse(outputDateStr));\n      outputDate.setHours(0, 0, 0, 0);\n      return outputDate;\n    }\n    hasExtraData() {\n      return typeof this.extraData !== 'undefined' && Object.keys(this.extraData).length > 0;\n    }\n    hasExtraClassroomData() {\n      return !!this.extraClassroomData && Object.keys(this.extraClassroomData).length > 0;\n    }\n    /**\n     * Check if the selected date and time is in the past.\n     * @param {Date} selectedDateTime - The selected date and time.\n     * @returns {boolean} True if the selected date and time is in the past, false otherwise.\n     */\n    isDateTimeInThePast(selectedDateTime) {\n      const currentDateTime = moment();\n      const selectedDateTimeMoment = moment(selectedDateTime);\n      return selectedDateTimeMoment.isBefore(currentDateTime);\n    }\n    static #_ = this.ɵfac = function BookingSystemComponent_Factory(t) {\n      return new (t || BookingSystemComponent)(i0.ɵɵdirectiveInject(i1.ClassroomService), i0.ɵɵdirectiveInject(i2.GeneralService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.UserService), i0.ɵɵdirectiveInject(i5.CalendarService), i0.ɵɵdirectiveInject(i6.LayoutService), i0.ɵɵdirectiveInject(i7.LessonService), i0.ɵɵdirectiveInject(i8.ToastService), i0.ɵɵdirectiveInject(i9.ActivatedRoute), i0.ɵɵdirectiveInject(i9.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i10.Location));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BookingSystemComponent,\n      selectors: [[\"app-booking-system\"]],\n      viewQuery: function BookingSystemComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n          i0.ɵɵviewQuery(_c1, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.bookingWrapper = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollPanel = _t.first);\n        }\n      },\n      hostBindings: function BookingSystemComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"resize\", function BookingSystemComponent_resize_HostBindingHandler() {\n            return ctx.isTabletScreenSize();\n          }, false, i0.ɵɵresolveWindow);\n        }\n      },\n      inputs: {\n        studentSelectedClassroom: \"studentSelectedClassroom\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 60,\n      vars: 23,\n      consts: [[\"bookingWrapper\", \"\"], [\"scrollPanel\", \"\"], [\"header\", \"Booking System\", \"headerBackgroundImage\", \"/assets/images/dashboard/calendar-banner.svg\", \"blockClass\", \"border-radius-bottom-10\", \"containerClass\", \"bg-white p-2 border-round-lg\", 2, \"height\", \"inherit\", 3, \"headerClass\", \"backButtonLabel\", \"headerTextClass\"], [1, \"surface-section\", \"lg:px-1\", \"lg:h-full\"], [1, \"grid\", \"h-full\", \"flex-column\", \"lg:flex-row\", \"relative\", \"overflow-y-scroll\"], [1, \"calendar-nav\", \"hidden\", \"lg:block\"], [\"type\", \"button\", 1, \"prev\", 3, \"click\"], [\"src\", \"/assets/icons/arrow-right-calendar-grad.svg\"], [\"type\", \"button\", 1, \"next\", 3, \"click\"], [1, \"col\", \"lg:col-fixed\", \"lg:pr-0\", \"lg:m-3\", \"lg:m-0\", \"left-col\"], [1, \"flex\", \"align-items-center\", \"justify-content-start\", \"flex-wrap\", \"gap-3\", \"mb-3\"], [\"for\", \"Teacher\", 1, \"col-fix\", \"flex\", \"gap-1\", \"font-sm\", 2, \"width\", \"70px\"], [\"src\", \"/assets/icons/student.svg\"], [4, \"ngIf\"], [\"for\", \"Duration\", 1, \"col-fix\", \"flex\", \"gap-1\", \"font-sm\", 2, \"width\", \"70px\"], [\"src\", \"/assets/icons/hourglass.svg\"], [\"optionLabel\", \"viewValue\", 3, \"ngModelChange\", \"onChange\", \"options\", \"ngModel\"], [1, \"flex\", \"flex-column\", \"justify-content-between\", 3, \"ngStyle\"], [1, \"mt-5\"], [\"title\", \"\", 3, \"dayClicked\", \"currentMonthChanged\", \"currentSelectedDay\", \"reverseGradient\", \"showAvailability\", \"userId\"], [1, \"grid\", \"font-2xs\", \"justify-content-between\"], [1, \"col-fixed\", \"col-circle\", \"pr-0\", \"pb-0\", \"gap-1\"], [1, \"high-bg\", \"circle\"], [1, \"low-bg\", \"circle\"], [1, \"col-fixed\", \"col-circle\", \"pb-0\", \"gap-1\"], [1, \"no-bg\", \"circle\"], [1, \"col\", \"h-full\", \"p-2\", \"md:pr-4\", \"right-col\"], [1, \"calendar-nav\", \"relative\", \"block\", \"lg:hidden\"], [\"type\", \"button\", 1, \"prev\", \"z-5\", 3, \"click\"], [\"type\", \"button\", 1, \"next\", \"z-5\", 3, \"click\"], [1, \"appointment-popup\", \"h-full\", \"pl-1\"], [\"id\", \"calendar\", 1, \"appointment-calendar\", \"h-full\"], [1, \"calendar-wrapper\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"text-primary\", \"mb-1\"], [1, \"calendar-week\", \"relative\", \"flex\", \"justify-content-center\"], [1, \"p-0\", \"mx-2\"], [4, \"ngFor\", \"ngForOf\"], [1, \"calendar-hours\"], [\"styleClass\", \"custombar1 h-30rem lg:h-full\"], [1, \"calendar-inside\", \"pb-2\"], [\"styleClass\", \"max-w-10rem\", \"panelStyleClass\", \"bordered-panel\", \"placeholder\", \"Select Classroom\", 3, \"ngModelChange\", \"options\", \"ngModel\"], [\"pTemplate\", \"selectedItem\"], [\"pTemplate\", \"item\"], [\"class\", \"flex align-items-center gap-2\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"height\", \"12\", 3, \"src\", 4, \"ngIf\"], [1, \"font-xs\", 3, \"innerHTML\"], [\"height\", \"12\", 3, \"src\"], [1, \"flex\", \"gap-1\", \"font-xs\"], [\"src\", \"/assets/images/dashboard/calendar/check-circled.svg\", 2, \"width\", \"12px\"], [\"optionLabel\", \"teacherName\", \"styleClass\", \"max-w-10rem\", \"placeholder\", \"Select Teacher\", 3, \"ngModelChange\", \"options\", \"ngModel\"], [1, \"capitalize\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"font-xs\"], [2, \"width\", \"18px\", 3, \"src\"], [1, \"border-round-xl\", \"block-gradient-reverse\", \"mt-3\", \"mb-0\"], [1, \"card-header\", \"text-center\"], [1, \"card-title\", \"font-xs\", \"py-2\"], [1, \"card-body\", \"px-2\"], [1, \"flex\", \"justify-content-center\", \"font-sm\", \"gap-1\", \"text-primary\"], [1, \"\"], [1, \"pi\", \"pi-calendar\", \"font-sm\", \"mr-1\"], [1, \"pi\", \"pi-clock\", \"font-sm\", \"mr-1\"], [1, \"pi\", \"pi-hourglass\", \"font-sm\", \"mr-1\"], [3, \"hidden\"], [1, \"grid\", \"justify-content-evenly\", \"font-2xs\", \"mt-2\"], [1, \"col-fixed\", \"flex\", \"align-items-center\"], [\"src\", \"/assets/icons/add-to-calendar.svg\", \"width\", \"15\", 1, \"mr-1\"], [1, \"col-fixed\", \"align-items-center\", \"pr-0\"], [1, \"flex\", \"justify-content-center\", \"mt-2\"], [\"styleClass\", \" px-2 \", \"icon\", \"pi pi-calendar\", \"iconPos\", \"left\", \"pButton\", \"\", \"type\", \"button\", 1, \"font-base\", \"mt-2\", \"border-round-lg\", \"request\", 3, \"click\", \"loading\", \"label\"], [1, \"flex\", \"w-full\", \"flex-column\", \"align-items-center\", \"justify-content-center\"], [1, \"text-center\", \"w-full\", \"pt-5\"], [\"src\", \"/assets/icons/fast-time-clock-ui-icon.png\", 1, \"max-w-14rem\"], [1, \"text-primary\", \"font-lg\"], [\"type\", \"button\", \"class\", \"font-lg gray-border-button border-round-3xl gap-2 px-3 py-2\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"font-lg\", \"gray-border-button\", \"border-round-3xl\", \"gap-2\", \"px-3\", \"py-2\", 3, \"click\"], [1, \"relative\", \"font-sm\", 3, \"ngClass\"], [1, \"flex\", \"justify-content-center\", \"align-items-center\", 3, \"ngClass\"], [1, \"hour-label\", 3, \"click\", \"for\"], [\"type\", \"radio\", \"name\", \"timeslot\", 3, \"disabled\", \"id\", \"value\", \"checked\"]],\n      template: function BookingSystemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", null, 0)(2, \"app-block-viewer\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5)(6, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function BookingSystemComponent_Template_button_click_6_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.goToPrevWeek());\n          });\n          i0.ɵɵelement(7, \"img\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function BookingSystemComponent_Template_button_click_8_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.goToNextWeek());\n          });\n          i0.ɵɵelement(9, \"img\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 9)(11, \"div\", 10)(12, \"label\", 11);\n          i0.ɵɵelement(13, \"img\", 12);\n          i0.ɵɵtext(14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(15, BookingSystemComponent_ng_container_15_Template, 4, 2, \"ng-container\", 13)(16, BookingSystemComponent_ng_container_16_Template, 4, 2, \"ng-container\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\", 10)(18, \"label\", 14);\n          i0.ɵɵelement(19, \"img\", 15);\n          i0.ɵɵtext(20, \" Duration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"p-dropdown\", 16);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function BookingSystemComponent_Template_p_dropdown_ngModelChange_21_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            i0.ɵɵtwoWayBindingSet(ctx.selectedDuration, $event) || (ctx.selectedDuration = $event);\n            return i0.ɵɵresetView($event);\n          });\n          i0.ɵɵlistener(\"onChange\", function BookingSystemComponent_Template_p_dropdown_onChange_21_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onDurationSelected($event));\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 17)(23, \"div\")(24, \"div\", 18)(25, \"app-calendar-agenda\", 19);\n          i0.ɵɵlistener(\"dayClicked\", function BookingSystemComponent_Template_app_calendar_agenda_dayClicked_25_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onAgendaDayClicked($event));\n          })(\"currentMonthChanged\", function BookingSystemComponent_Template_app_calendar_agenda_currentMonthChanged_25_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onCurrentMonthChanged($event));\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 20)(27, \"div\", 21);\n          i0.ɵɵelement(28, \"div\", 22);\n          i0.ɵɵelementStart(29, \"div\");\n          i0.ɵɵtext(30, \"High Availability\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(31, \"div\", 21);\n          i0.ɵɵelement(32, \"div\", 23);\n          i0.ɵɵelementStart(33, \"div\");\n          i0.ɵɵtext(34, \"Low Availability\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"div\", 24);\n          i0.ɵɵelement(36, \"div\", 25);\n          i0.ɵɵelementStart(37, \"div\");\n          i0.ɵɵtext(38, \"No Availability\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(39, BookingSystemComponent_div_39_Template, 27, 6, \"div\", 13);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"div\", 26)(41, \"div\", 27)(42, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function BookingSystemComponent_Template_button_click_42_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.goToPrevWeek());\n          });\n          i0.ɵɵelement(43, \"img\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"button\", 29);\n          i0.ɵɵlistener(\"click\", function BookingSystemComponent_Template_button_click_44_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.goToNextWeek());\n          });\n          i0.ɵɵelement(45, \"img\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"div\", 30)(47, \"div\", 31)(48, \"div\", 32)(49, \"div\", 33);\n          i0.ɵɵtext(50);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"div\", 34)(52, \"ul\", 35);\n          i0.ɵɵtemplate(53, BookingSystemComponent_li_53_Template, 3, 5, \"li\", 36);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(54, \"div\", 37)(55, \"p-scrollPanel\", 38, 1);\n          i0.ɵɵtemplate(57, BookingSystemComponent_ng_container_57_Template, 10, 1, \"ng-container\", 13);\n          i0.ɵɵelementStart(58, \"ul\", 39);\n          i0.ɵɵtemplate(59, BookingSystemComponent_li_59_Template, 2, 1, \"li\", 36);\n          i0.ɵɵelementEnd()()()()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"headerClass\", \"justify-content-center my-2\")(\"backButtonLabel\", \"Back\")(\"headerTextClass\", \"font-xl font-semibold justify-content-center\");\n          i0.ɵɵadvance(12);\n          i0.ɵɵtextInterpolate1(\" \", ctx.user.role == ctx.UserRoles.TEACHER ? \"Classroom\" : \"Teacher\", \" \");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.user.role == ctx.UserRoles.TEACHER);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.user.role == ctx.UserRoles.STUDENT);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"options\", ctx.availableDuration);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedDuration);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(20, _c2, !ctx.isTabletScreenSize() ? ctx.height - 150 + \"px\" : \"auto\"));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"currentSelectedDay\", ctx.selectedDay)(\"reverseGradient\", true)(\"showAvailability\", true)(\"userId\", ctx.calendarAgendaUserId);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngIf\", ctx.radioChecked);\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate1(\" \", ctx.findMonthFromDateRange(), \" \");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.weekDays);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction0(22, _c3));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.noClassroomsFound);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngForOf\", ctx.weekDays);\n        }\n      },\n      dependencies: [i11.NgControlStatus, i11.NgModel, i10.NgClass, i10.NgForOf, i10.NgIf, i10.NgStyle, i12.BlockViewerComponent, i13.CalendarAgendaComponent, i14.ButtonDirective, i15.PrimeTemplate, i16.ScrollPanel, i17.Dropdown, i10.DatePipe],\n      styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}.hour-label[_ngcontent-%COMP%]{font-size:clamp(.75rem,.12vw + .67rem,.88rem)}.hour-label.checked[_ngcontent-%COMP%]{color:#2fb9d3;font-weight:bolder}[_nghost-%COMP%]     .block-section{height:inherit}[_nghost-%COMP%]     .block-content{height:calc(100% - 52px)}[_nghost-%COMP%]     .block-content .bg-white{height:100%;padding-bottom:0!important}[_nghost-%COMP%]     .p-scrollpanel-content{padding:0}[_nghost-%COMP%]     .request.p-button{background:linear-gradient(#657aef,#1a266c);width:auto;justify-content:center}[_nghost-%COMP%]     .request.p-button .p-button-label{flex:unset}[_nghost-%COMP%]     p-dropdown .p-dropdown{width:100%;background-image:linear-gradient(to bottom,#fff,#b4b4fa33);border:none}[_nghost-%COMP%]     p-dropdown .p-dropdown .p-inputtext{padding:.3rem 1rem;font-size:.85rem;font-family:Proxima Nova Regular}[_nghost-%COMP%]     p-dropdown .p-dropdown-panel{box-shadow:none}[_nghost-%COMP%]     p-dropdown .p-dropdown-panel .p-dropdown-items{background-image:linear-gradient(to bottom,#b4b4fa33,#8492f733,#3873f433);color:#2e3d90}[_nghost-%COMP%]     p-dropdown .p-dropdown:not(.p-disabled).p-focus{border:0;box-shadow:none}[_nghost-%COMP%]     .reversed-gradient .cal-header{width:100%}[_nghost-%COMP%]     .table-container{overflow-x:hidden!important}[_nghost-%COMP%]     .card{border:0}[_nghost-%COMP%]     .card .p-datepicker{padding-top:0;padding-bottom:0}[_nghost-%COMP%]     .card .p-datepicker table th{padding-top:0;padding-bottom:0;border:0}[_nghost-%COMP%]     .card .p-datepicker table td{height:40px}[_nghost-%COMP%]     .card .p-datepicker table thead th{color:#cacbd9}.left-col[_ngcontent-%COMP%]{width:40%}.right-col[_ngcontent-%COMP%]{width:60%}.no-availability[_ngcontent-%COMP%]{color:#cacbd9}.availability[_ngcontent-%COMP%]{color:#2e3d90}.block-header[_ngcontent-%COMP%]{position:relative;z-index:2;min-height:60px;box-shadow:0 3px 8px #b3b3b3f5;border-radius:16px}.block-header[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;background-image:url(/assets/images/lessons/calendar-availability-gradient-bg.png);background-size:inherit;background-repeat:no-repeat;background-position:center;width:100%;height:100%;left:0;top:0;z-index:1;border-radius:16px}.appointment-popup[_ngcontent-%COMP%]{position:relative;width:100%;z-index:1;overflow:hidden}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]{width:100%;display:flex;flex-direction:column}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .current-week[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:.5rem 0;border-bottom:1px solid #f3f3f3;align-items:center}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .current-week[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]{font-size:1rem;color:#2d3152}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .current-week[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{margin-right:.5rem;opacity:.5}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:inherit}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-week[_ngcontent-%COMP%]{border-bottom:0!important}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-week[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{padding:0 3px 0 0;list-style:none;display:flex;width:100%;margin:0;background-image:linear-gradient(359deg,rgba(0,44,207,.1882352941),transparent);border-radius:14px}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-week[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{width:100%;padding:.5rem 0;text-align:center;color:#575757}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]{padding:0 0 0 1rem;overflow:auto;height:100%}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{list-style:none;padding:0;margin:0}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%]{display:flex}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%] > li[_ngcontent-%COMP%]{width:100%;text-align:center}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%] > li[_ngcontent-%COMP%] > ul[_ngcontent-%COMP%] > li[_ngcontent-%COMP%]{display:flex;justify-content:center;align-items:center;min-height:1rem;padding-top:6px;padding-bottom:6px}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%] > li[_ngcontent-%COMP%] > ul[_ngcontent-%COMP%] > li.no-availability[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{cursor:initial}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%] > li[_ngcontent-%COMP%] > ul[_ngcontent-%COMP%] > li.availability[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]:hover{color:#2fb9d3;cursor:pointer}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%] > li[_ngcontent-%COMP%] > ul[_ngcontent-%COMP%] > li[_ngcontent-%COMP%]   input[type=radio i][_ngcontent-%COMP%]{appearance:none;-webkit-appearance:none;-moz-appearance:none;-ms-appearance:none;-o-appearance:none;width:20px;height:20px;margin-right:5px}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%] > li[_ngcontent-%COMP%] > ul[_ngcontent-%COMP%] > li[_ngcontent-%COMP%]   input[type=radio i][_ngcontent-%COMP%]:before{background-color:#fff;content:\\\"\\\";display:inline-block;width:16px;height:16px;border-radius:50%;transition:background-color .2s ease;cursor:pointer}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%] > li[_ngcontent-%COMP%] > ul[_ngcontent-%COMP%] > li[_ngcontent-%COMP%]   input[type=radio i][_ngcontent-%COMP%]:checked:before{background-color:#2fb9d3;border:1px solid #2FB9D3;position:absolute}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]   .calendar-inside[_ngcontent-%COMP%] > li[_ngcontent-%COMP%] > ul[_ngcontent-%COMP%] > li[_ngcontent-%COMP%]   input[type=radio i][_ngcontent-%COMP%]:checked:after{content:\\\"\\\";display:block;position:relative;width:6px;height:10px;border-bottom:2px solid #fff;border-right:2px solid #fff;transform:rotate(45deg);top:2px;left:4px}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-buttons[_ngcontent-%COMP%]{display:flex;border-top:1px solid #f3f3f3;margin:0 -2rem}@media screen and (max-width: 768px){.appointment-popup[_ngcontent-%COMP%]{flex-direction:column;width:100%;max-width:100%;top:0;border-radius:0;overflow:auto;height:calc(100% - 6.4rem);min-height:auto;max-height:100%}.appointment-popup[_ngcontent-%COMP%]   .calendar-nav[_ngcontent-%COMP%]{flex-wrap:wrap}.appointment-popup[_ngcontent-%COMP%]   .calendar-nav[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:before{padding:0 1rem;font-size:1.2rem}.appointment-popup[_ngcontent-%COMP%]   .calendar-nav[_ngcontent-%COMP%] > span[_ngcontent-%COMP%]{font-size:1.5rem}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]{padding:0}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%]{padding:0;margin:0}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-wrapper[_ngcontent-%COMP%]   .calendar-hours[_ngcontent-%COMP%] > ul[_ngcontent-%COMP%] > li[_ngcontent-%COMP%] > ul[_ngcontent-%COMP%] > li[_ngcontent-%COMP%]{padding:1rem 0 .75rem;font-size:1.2rem}.appointment-popup[_ngcontent-%COMP%]   .appointment-calendar[_ngcontent-%COMP%]   .calendar-buttons[_ngcontent-%COMP%]{margin:0}}.round[_ngcontent-%COMP%]{position:relative}.round[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{background-color:#fff;border:1px solid #ccc;border-radius:50%;cursor:pointer;height:14px;left:0;position:absolute;top:3px;width:14px}.round[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]:after{border:2px solid #fff;border-top:none;border-right:none;content:\\\"\\\";height:6px;left:1px;opacity:0;position:absolute;top:2px;transform:rotate(-45deg);width:10px}.round[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]{visibility:hidden}.round[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked + label[_ngcontent-%COMP%]{background-color:#2fb9d3;border-color:#2fb9d3}.round[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked + label[_ngcontent-%COMP%]:after{opacity:1}.high-bg[_ngcontent-%COMP%]{background-image:linear-gradient(to bottom,#64cfdd,#48c4e0,#2cb8e2,#18abe4,#219de3,#1591e2,#1e85e0,#3177dc,#2a69da,#2b5ad6,#334ad0,#3f37c9)}.low-bg[_ngcontent-%COMP%]{background-image:linear-gradient(to bottom,#9baaff,#7c8aec,#5e6ad9,#3f4bc4,#152caf)}.no-bg[_ngcontent-%COMP%]{background:#dfe8fc}.circle[_ngcontent-%COMP%]{height:.75rem;width:.75rem;border-radius:50%}.col-circle[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;font-size:.55rem!important}[_ngcontent-%COMP%]::-webkit-scrollbar{width:10px!important;height:10px!important;display:none}[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:transparent!important}[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:transparent!important}.calendar-nav[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]{border:none;background:transparent;padding:0;font-size:0;cursor:pointer;transform:translate(-50%,1%)}.calendar-nav[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:before{font-size:2rem;padding:0 1.5rem;display:block}.calendar-nav[_ngcontent-%COMP%]   button.prev[_ngcontent-%COMP%]{transform:rotate(-180deg);position:absolute;top:2rem;left:calc(1% - 5px)}@media only screen and (min-width: 992px){.calendar-nav[_ngcontent-%COMP%]   button.prev[_ngcontent-%COMP%]{top:2.5rem;left:calc(36% - 8px)}}.calendar-nav[_ngcontent-%COMP%]   button.next[_ngcontent-%COMP%]{position:absolute;top:2rem;right:calc(1% - 15px)}@media only screen and (min-width: 992px){.calendar-nav[_ngcontent-%COMP%]   button.next[_ngcontent-%COMP%]{top:2.5rem;right:-8px}}.left-col[_ngcontent-%COMP%]{width:100%}@media only screen and (min-width: 992px){.left-col[_ngcontent-%COMP%]{width:32%}}.right-col[_ngcontent-%COMP%]{width:100%}@media only screen and (min-width: 992px){.right-col[_ngcontent-%COMP%]{width:63%}}[_nghost-%COMP%]     .bordered-panel .p-dropdown-items-wrapper{border-radius:8px}\"]\n    });\n  }\n  return BookingSystemComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}