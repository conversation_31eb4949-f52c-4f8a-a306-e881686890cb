{"ast": null, "code": "import { UntypedFormArray, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { Observable } from 'rxjs';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"src/app/core/services/general.service\";\nimport * as i4 from \"src/app/core/services/teacher-application.service\";\nimport * as i5 from \"src/app/core/services/auth.service\";\nimport * as i6 from \"src/app/core/services/toast.service\";\nimport * as i7 from \"primeng/api\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"../../../shared/prime/input/prime-input-dropdown/prime-input-dropdown.component\";\nimport * as i11 from \"primeng/button\";\nimport * as i12 from \"primeng/calendar\";\nimport * as i13 from \"primeng/ripple\";\nconst _c0 = [\"opportunityForm\"];\nfunction GeneralInfoComponent_ng_container_2_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \"* First Name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \"* Last Name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \"* Preffered name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelement(1, \"img\", 37);\n    i0.ɵɵelementStart(2, \"div\", 38);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const country_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", country_r3.image, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(country_r3.name);\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \"* Country of Origin is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_ng_template_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelement(1, \"img\", 37);\n    i0.ɵɵelementStart(2, \"div\", 38);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const country_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", country_r4.image, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(country_r4.name);\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \"* Country of Residency is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_ng_template_36_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \"* Timezone is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_ng_template_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 38);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, GeneralInfoComponent_ng_container_2_ng_template_36_div_3_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const timezone_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(timezone_r5.text);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"timezone\"));\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \"* Timezone is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \"* Date of Birth is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_47_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 38);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const language_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(language_r7.name);\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_47_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function GeneralInfoComponent_ng_container_2_div_47_div_6_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.addnativeLanguage());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_47_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function GeneralInfoComponent_ng_container_2_div_47_ng_container_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const i_r10 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeNativeLanguage(i_r10));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40);\n    i0.ɵɵelementContainerStart(2, 41);\n    i0.ɵɵelementStart(3, \"div\", 42)(4, \"p-dropdown\", 43);\n    i0.ɵɵlistener(\"onChange\", function GeneralInfoComponent_ng_container_2_div_47_Template_p_dropdown_onChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onOriginChange($event));\n    });\n    i0.ɵɵtemplate(5, GeneralInfoComponent_ng_container_2_div_47_ng_template_5_Template, 3, 1, \"ng-template\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, GeneralInfoComponent_ng_container_2_div_47_div_6_Template, 2, 0, \"div\", 2)(7, GeneralInfoComponent_ng_container_2_div_47_ng_container_7_Template, 2, 0, \"ng-container\", 2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r10 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroupName\", i_r10);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"options\", ctx_r1.languages)(\"filter\", !ctx_r1.isTablet);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i_r10 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r10 > 0);\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵtext(1, \"* Please select your Native languages \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \"* Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \"* Phone is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"form\", 8)(2, \"div\", 9)(3, \"div\", 10)(4, \"div\", 11)(5, \"div\", 12);\n    i0.ɵɵtext(6, \"First Name *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"input\", 13);\n    i0.ɵɵtemplate(8, GeneralInfoComponent_ng_container_2_div_8_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 11)(10, \"div\", 12);\n    i0.ɵɵtext(11, \"Last Name *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 15);\n    i0.ɵɵtemplate(13, GeneralInfoComponent_ng_container_2_div_13_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 16)(15, \"div\", 11)(16, \"div\", 12);\n    i0.ɵɵtext(17, \"Preffered Name *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"input\", 17);\n    i0.ɵɵtemplate(19, GeneralInfoComponent_ng_container_2_div_19_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 11)(21, \"div\", 12);\n    i0.ɵɵtext(22, \"Country of Origin *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"p-dropdown\", 18);\n    i0.ɵɵlistener(\"onChange\", function GeneralInfoComponent_ng_container_2_Template_p_dropdown_onChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCountryChange($event));\n    });\n    i0.ɵɵtemplate(24, GeneralInfoComponent_ng_container_2_ng_template_24_Template, 4, 2, \"ng-template\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, GeneralInfoComponent_ng_container_2_div_25_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 11)(27, \"div\", 12);\n    i0.ɵɵtext(28, \"Country of Residency *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"p-dropdown\", 20);\n    i0.ɵɵlistener(\"onChange\", function GeneralInfoComponent_ng_container_2_Template_p_dropdown_onChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCountryChange($event));\n    });\n    i0.ɵɵtemplate(30, GeneralInfoComponent_ng_container_2_ng_template_30_Template, 4, 2, \"ng-template\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, GeneralInfoComponent_ng_container_2_div_31_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 11)(33, \"div\", 12);\n    i0.ɵɵtext(34, \"Timezone *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"p-dropdown\", 21);\n    i0.ɵɵlistener(\"onChange\", function GeneralInfoComponent_ng_container_2_Template_p_dropdown_onChange_35_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTimeZoneChange($event));\n    });\n    i0.ɵɵtemplate(36, GeneralInfoComponent_ng_container_2_ng_template_36_Template, 4, 2, \"ng-template\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(37, GeneralInfoComponent_ng_container_2_div_37_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 11)(39, \"div\", 12);\n    i0.ɵɵtext(40, \"Date of Birth *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"p-calendar\", 22);\n    i0.ɵɵlistener(\"onSelect\", function GeneralInfoComponent_ng_container_2_Template_p_calendar_onSelect_41_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onBirthDateSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(42, GeneralInfoComponent_ng_container_2_div_42_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 11)(44, \"div\", 12);\n    i0.ɵɵtext(45, \"Native Speaker in * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerStart(46, 23);\n    i0.ɵɵtemplate(47, GeneralInfoComponent_ng_container_2_div_47_Template, 8, 5, \"div\", 24)(48, GeneralInfoComponent_ng_container_2_div_48_Template, 2, 0, \"div\", 25);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 16)(50, \"div\", 26)(51, \"strong\");\n    i0.ɵɵtext(52, \"Enter your contact info\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(53, \"div\", 11)(54, \"div\", 12);\n    i0.ɵɵtext(55, \"E-mail *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(56, \"input\", 27);\n    i0.ɵɵtemplate(57, GeneralInfoComponent_ng_container_2_div_57_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 11)(59, \"div\", 12);\n    i0.ɵɵtext(60, \"Phone *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"div\", 28)(62, \"div\", 29)(63, \"div\", 30)(64, \"app-prime-input-dropdown\", 31);\n    i0.ɵɵlistener(\"valueSelected\", function GeneralInfoComponent_ng_container_2_Template_app_prime_input_dropdown_valueSelected_64_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPhoneCodeChange($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(65, \"div\", 32);\n    i0.ɵɵelement(66, \"input\", 33);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(67, GeneralInfoComponent_ng_container_2_div_67_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"div\", 11)(69, \"div\", 12);\n    i0.ɵɵtext(70, \"Skype *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(71, \"input\", 34);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.form);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"fname\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"lname\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"preffered\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"options\", ctx_r1.countries)(\"filter\", !ctx_r1.isTablet);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"countryOrigin\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"options\", ctx_r1.countries)(\"filter\", !ctx_r1.isTablet);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"countryResidency\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"options\", ctx_r1.timezones)(\"filter\", !ctx_r1.isTablet);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"zone\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"maxDate\", ctx_r1.maxBirthdateTime)(\"defaultDate\", ctx_r1.maxBirthdateTime)(\"keepInvalid\", true)(\"monthNavigator\", true)(\"yearNavigator\", false)(\"yearRange\", \"2019:2021\")(\"firstDayOfWeek\", 1)(\"readonlyInput\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"birth\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.nativeLanguages.controls);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"nativeLanguages\"));\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"email\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"parentForm\", ctx_r1.form)(\"countries\", ctx_r1.phoneCodes)(\"filter\", true)(\"inputName\", \"phoneCode\")(\"showTextAfterImage\", false)(\"withFlags\", true)(\"selectedItemValue\", ctx_r1.selectedPhoneCode);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"phone\"));\n  }\n}\nexport let GeneralInfoComponent = /*#__PURE__*/(() => {\n  class GeneralInfoComponent {\n    constructor(router, fb, generalService, teacherService, authService, toast, confirmationService) {\n      this.router = router;\n      this.fb = fb;\n      this.generalService = generalService;\n      this.teacherService = teacherService;\n      this.authService = authService;\n      this.toast = toast;\n      this.confirmationService = confirmationService;\n      this.ngForm = {};\n      this.subs = new SubSink();\n      this.form = new UntypedFormGroup({});\n      this.isTablet = false;\n      this.timezones = this.generalService.getTimezones();\n      this.countries = this.generalService.getCountries();\n      this.phoneCodes = this.generalService.getPhoneCodes();\n      this.languages = this.generalService.languages;\n      this.tryToSave = false;\n      this.isLoading = true;\n      this.selectedCountry = \"\";\n      this.maxBirthdateTime = new Date();\n      this.formattedBirthDate = \"\";\n      this.nativeLanguagesModel = [];\n      this.formChanged = false;\n      this.selectedPhoneCode = {};\n    }\n    get nativeLanguages() {\n      return this.form.get('nativeLanguages');\n    }\n    ngOnInit() {\n      this.isLoading = true;\n      this.subs.add(this.teacherService.submitOnMenuClickListener.subscribe(res => {\n        if (res) {\n          this.onSubmit();\n        }\n      }));\n      console.log(this.maxBirthdateTime);\n      const date = new Date();\n      const newDate = this.maxBirthdateTime.setFullYear(this.maxBirthdateTime.getFullYear() - 18);\n      this.maxBirthdateTime = new Date(newDate);\n      this.teacherService.setCurrentStepIndex(0);\n      this.teacher = this.teacherService.dummyTeacher;\n      this.subs.add(this.generalService.deviceKind.subscribe(res => {\n        this.isTablet = res.is1024;\n      }));\n      this.subs.add(this.teacherService.getTeacherApplicationStepsStatus().subscribe(stepsStatuses => {\n        if (!stepsStatuses.step1) {\n          this.prepareSelectedPhoneCodeFromIP();\n        }\n      }));\n      this.subs.add(this.teacherService.getTeacherApplicationStep1(this.authService.getUserId()).subscribe(res => {\n        console.log(res);\n        this.updateStep1TeacherInfoFormValues(res);\n        this.initFormChangedListener();\n        this.isLoading = false;\n      }));\n      // updating title for mobile header text\n      // this.generalService.updateMobileMenuTitle(this.teacherService.getStepsTitle(0));\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n      this.teacherService.setSubmitOnMenuClickListener(false);\n    }\n    onSubmit() {\n      this.tryToSave = true;\n      let invalidInputs = '';\n      for (const controlName in this.form.controls) {\n        if (this.form.controls.hasOwnProperty(controlName)) {\n          const control = this.form.controls[controlName];\n          if (control.invalid) {\n            invalidInputs += `${controlName}, `;\n          }\n        }\n      }\n      let detailMessage = 'Please enter all required fields to continue.';\n      if (invalidInputs !== '') {\n        invalidInputs = invalidInputs.slice(0, -2); // Remove the trailing comma and space\n        if (invalidInputs !== '') {\n          detailMessage += `Invalid input for the following fields: ${invalidInputs}`;\n        }\n      }\n      if (!this.form.valid) {\n        this.toast.setShowToastmessage({\n          severity: 'warn',\n          summary: '',\n          detail: detailMessage\n        });\n        return;\n      }\n      this.formChanged = false;\n      const teacherApplicationStep1FormData = {\n        firstName: this.form.value.firstName,\n        lastName: this.form.value.lastName,\n        prefferedName: this.form.value.prefferedName,\n        countryOrigin: {\n          name: this.form.value.countryOrigin.name,\n          flagUrl: this.form.value.countryOrigin.image\n        },\n        countryResidency: {\n          name: this.form.value.countryResidency.name,\n          flagUrl: this.form.value.countryResidency.image\n        },\n        nativeLanguages: this.form.value.nativeLanguages,\n        timeZone: this.form.value.timeZone?.utc[0],\n        birth: this.generalService.convertDateStringToIsoFormatWithZeroTime(this.formattedBirthDate),\n        email: this.form.value.email,\n        phone: this.form.value.phone,\n        phoneCode: this.form.value.phoneCode.code,\n        skype: this.form.value.skype\n      };\n      this.subs.add(this.teacherService.updateAPITeacherApplicationStep1(teacherApplicationStep1FormData).subscribe(res => {\n        if (res) {\n          this.toast.setShowToastmessage({\n            severity: 'success',\n            summary: '',\n            detail: 'Your General details were saved.'\n          });\n          // this.toastr.success(\"Your general info were updated.\")\n          this.router.navigateByUrl('/teacher/education', {\n            replaceUrl: true\n          });\n        }\n      }));\n    }\n    updateStep1TeacherInfoFormValues(teacherStep1Object) {\n      if (this.isValidDate(teacherStep1Object.birth)) {\n        this.formattedBirthDate = this.generalService.formatDateToDMY(teacherStep1Object.birth);\n      } else {\n        this.formattedBirthDate = this.generalService.formatDateToDMY(this.maxBirthdateTime.toString());\n      }\n      if (!this.generalService.isNullishObject(teacherStep1Object.phoneCode)) {\n        this.selectedPhoneCode = this.phoneCodes.filter(el => el.code === teacherStep1Object.phoneCode)[0];\n      }\n      this.form = new UntypedFormGroup({\n        birth: new UntypedFormControl(this.formattedBirthDate, {\n          validators: [Validators.required]\n        }),\n        prefferedName: new UntypedFormControl(teacherStep1Object.prefferedName, {\n          validators: [Validators.required]\n        }),\n        firstName: new UntypedFormControl(teacherStep1Object.firstName, {\n          validators: [Validators.required]\n        }),\n        lastName: new UntypedFormControl(teacherStep1Object.lastName, {\n          validators: [Validators.required]\n        }),\n        email: new UntypedFormControl(teacherStep1Object.email, {\n          validators: [Validators.required, Validators.email]\n        }),\n        phoneCode: new UntypedFormControl(this.selectedPhoneCode, {\n          validators: [Validators.required]\n        }),\n        phone: new UntypedFormControl(teacherStep1Object.phone, {\n          validators: [Validators.required]\n        }),\n        skype: new UntypedFormControl(teacherStep1Object.skype || '-', {\n          validators: [Validators.required]\n        }),\n        nativeLanguages: new UntypedFormArray([], {\n          validators: [Validators.required]\n        }),\n        timeZone: new UntypedFormControl(this.timezones.find(el => el.utc.includes(teacherStep1Object.timeZone)), {\n          validators: [Validators.required]\n        }),\n        countryOrigin: new UntypedFormControl(!this.generalService.isNullishObject(teacherStep1Object.countryOrigin) ? this.countries.filter(el => el.name === teacherStep1Object.countryOrigin.name)[0] : null, {\n          validators: [Validators.required]\n        }),\n        countryResidency: new UntypedFormControl(!this.generalService.isNullishObject(teacherStep1Object.countryResidency) ? this.countries.filter(el => el.name === teacherStep1Object.countryResidency.name)[0] : null, {\n          validators: [Validators.required]\n        })\n      });\n      if (!this.generalService.isNullishObject(teacherStep1Object.nativeLanguages)) {\n        teacherStep1Object.nativeLanguages.forEach(element => {\n          this.addnativeLanguage(element.native);\n        });\n      } else {\n        this.nativeLanguagesModel.forEach(element => {\n          this.nativeLanguages.push(this.fb.group(element));\n        });\n        if (this.nativeLanguagesModel.length === 0) {\n          this.addnativeLanguage({});\n        }\n      }\n    }\n    onBirthDateSelected(event) {\n      console.log(event);\n      let d = new Date(Date.parse(event));\n      this.formattedBirthDate = this.generalService.formatDateToDMY(event);\n    }\n    isConfirm(field) {\n      return this.form.get(field)?.invalid && this.form.get(field)?.touched || this.tryToSave && this.form.get(field)?.invalid || this.form.value.password !== this.form.value.confirm && this.form.get(field)?.touched;\n    }\n    onOriginChange(event) {\n      setTimeout(() => {\n        this.selectedLanguage = event.value;\n      }, 100);\n    }\n    onCountryChange(event) {\n      setTimeout(() => {\n        this.selectedCountry = event.value;\n      }, 100);\n    }\n    onTimeZoneChange(event) {\n      setTimeout(() => {\n        this.selectedTimezone = event.value;\n      }, 100);\n    }\n    onPhoneCodeChange(event) {\n      setTimeout(() => {\n        this.selectedPhoneCode = event.value;\n        console.log(this.selectedPhoneCode);\n      }, 100);\n    }\n    initFormChangedListener() {\n      this.subs.add(this.form.valueChanges.subscribe(val => {\n        if (this.form.dirty) {\n          this.formChanged = true;\n        }\n      }));\n    }\n    ifFieldValid(field) {\n      this.teacherService.setStepValid(0, this.form, 'teacher-info-route');\n      return this.form.get(field)?.invalid && this.form.get(field)?.touched || this.tryToSave && this.form.get(field)?.invalid;\n    }\n    ifNativeLanguagesFieldValid(i) {\n      this.teacherService.setStepValid(0, this.form, 'teacher-info-route');\n      return this.tryToSave && this.form.get('nativeLanguages').controls[i].invalid;\n    }\n    addnativeLanguage(objectValue) {\n      if (this.nativeLanguages.length < 3) {\n        const group = new UntypedFormGroup({\n          native: new UntypedFormControl(objectValue, Validators.required)\n        });\n        this.nativeLanguages.push(group);\n      }\n    }\n    removeNativeLanguage(index) {\n      this.nativeLanguages.removeAt(index);\n    }\n    isValidDate(dateValue) {\n      const date = new Date(dateValue);\n      if (isNaN(date.getTime()) || date.getFullYear() < 1000) {\n        return false;\n      } else {\n        return true;\n      }\n    }\n    findCountryImage(phoneCode) {\n      return this.generalService.findCountryImage(phoneCode);\n    }\n    prepareSelectedPhoneCodeFromIP() {\n      this.generalService.getCountryCode().subscribe(res => {\n        if (res) {\n          console.log(res);\n          this.selectedPhoneCode = this.phoneCodes.filter(el => el.iso === res.country_code)[0];\n        }\n      });\n    }\n    canDeactivate() {\n      if (this.formChanged) {\n        return new Observable(observer => {\n          this.confirmationService.confirm({\n            header: '',\n            key: 'stepLeaveConfirmation',\n            message: 'Are you sure you want to leave this page? Your changes will be lost.',\n            accept: () => {\n              observer.next(true);\n              observer.complete();\n            },\n            reject: () => {\n              observer.next(false);\n              observer.complete();\n            }\n          });\n        });\n      } else {\n        return true;\n      }\n    }\n    static #_ = this.ɵfac = function GeneralInfoComponent_Factory(t) {\n      return new (t || GeneralInfoComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.UntypedFormBuilder), i0.ɵɵdirectiveInject(i3.GeneralService), i0.ɵɵdirectiveInject(i4.TeacherApplicationService), i0.ɵɵdirectiveInject(i5.AuthService), i0.ɵɵdirectiveInject(i6.ToastService), i0.ɵɵdirectiveInject(i7.ConfirmationService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GeneralInfoComponent,\n      selectors: [[\"app-general-info\"]],\n      viewQuery: function GeneralInfoComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ngForm = _t.first);\n        }\n      },\n      decls: 10,\n      vars: 1,\n      consts: [[1, \"md:mt-3\"], [1, \"profile-info\"], [4, \"ngIf\"], [1, \"btns\", \"md:ml-5\", \"mt-4\"], [\"pRipple\", \"\", \"disabled\", \"\", 1, \"rounded-blue-button\", \"transparent\"], [\"src\", \"/assets/icons/arrow-left-blue.svg\"], [\"pRipple\", \"\", 1, \"rounded-blue-button\", 3, \"click\"], [\"src\", \"/assets/icons/arrow-right.svg\"], [3, \"formGroup\"], [1, \"profile-info-section\", 2, \"padding-top\", \"0\"], [1, \"input-fields\"], [1, \"input-field\"], [1, \"input-element-title\"], [\"formControlName\", \"firstName\", \"type\", \"text\", 1, \"input-element\"], [\"class\", \"input-error\", 4, \"ngIf\"], [\"formControlName\", \"lastName\", \"type\", \"text\", 1, \"input-element\"], [1, \"w-100\"], [\"formControlName\", \"prefferedName\", \"type\", \"text\", 1, \"input-element\"], [\"autocomplete\", \"autocomplete_off_hack_xfr4!k\", \"optionLabel\", \"name\", \"filterBy\", \"name\", \"placeholder\", \"Select Country\", \"formControlName\", \"countryOrigin\", \"styleClass\", \"dropdown-blue small-dropdown-items\", 3, \"onChange\", \"options\", \"filter\"], [\"pTemplate\", \"item\"], [\"autocomplete\", \"autocomplete_off_hack_xfr4!k\", \"optionLabel\", \"name\", \"filterBy\", \"name\", \"placeholder\", \"Select Country\", \"formControlName\", \"countryResidency\", \"styleClass\", \"dropdown-blue small-dropdown-items\", 3, \"onChange\", \"options\", \"filter\"], [\"autocomplete\", \"autocomplete_off_hack_xfr4!k\", \"optionLabel\", \"text\", \"filterBy\", \"text\", \"placeholder\", \"Select Zone\", \"formControlName\", \"timeZone\", \"styleClass\", \"dropdown-blue\", 3, \"onChange\", \"options\", \"filter\"], [\"dateFormat\", \"dd/mm/yy\", \"styleClass\", \"date-element\", \"inputStyleClass\", \"input-element\", \"formControlName\", \"birth\", 3, \"onSelect\", \"maxDate\", \"defaultDate\", \"keepInvalid\", \"monthNavigator\", \"yearNavigator\", \"yearRange\", \"firstDayOfWeek\", \"readonlyInput\"], [\"formArrayName\", \"nativeLanguages\"], [\"style\", \"position: relative; display: flex;flex-direction: column;\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"input-error w-100\", 4, \"ngIf\"], [1, \"step-heading-right\", \"m-t-30\", 2, \"padding\", \"10px\"], [\"formControlName\", \"email\", \"type\", \"email\", 1, \"input-element\"], [1, \"ui-g\", \"ui-fluid\"], [1, \"display-flex\"], [\"fxFlex\", \"\", 1, \"col--2of3\"], [\"optionLabel\", \"code\", \"filterBy\", \"code\", \"flagFilter\", \"code\", \"templateValue\", \"code\", \"styleClass\", \"dropdown-blue phone-code-input small-dropdown-items\", 3, \"valueSelected\", \"parentForm\", \"countries\", \"filter\", \"inputName\", \"showTextAfterImage\", \"withFlags\", \"selectedItemValue\"], [\"fxFlex\", \"\", 1, \"col--1of1\"], [\"formControlName\", \"phone\", \"type\", \"text\", 1, \"input-element\", \"phone-input\"], [\"formControlName\", \"skype\", \"type\", \"text\", 1, \"input-element\"], [1, \"input-error\"], [1, \"country-item\"], [3, \"src\"], [1, \"country-name\"], [2, \"position\", \"relative\", \"display\", \"flex\", \"flex-direction\", \"column\"], [2, \"flex-direction\", \"row\", \"display\", \"flex\", \"align-items\", \"center\"], [3, \"formGroupName\"], [1, \"display-flex\", \"align-center\", \"m-t-10\", 2, \"gap\", \"10px\", \"width\", \"100%\"], [\"autocomplete\", \"off\", \"optionLabel\", \"name\", \"filterBy\", \"name\", \"placeholder\", \"Select Native\", \"formControlName\", \"native\", \"styleClass\", \"dropdown-blue m-t-0\", 3, \"onChange\", \"options\", \"filter\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-plus\", 1, \"p-button-raised\", \"p-button-rounded\", \"plus-btn-circle\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-minus\", 1, \"p-button-raised\", \"p-button-rounded\", \"minus-btn-circle\", 3, \"click\"], [1, \"input-error\", \"w-100\"]],\n      template: function GeneralInfoComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"div\", 0);\n          i0.ɵɵelementStart(1, \"div\", 1);\n          i0.ɵɵtemplate(2, GeneralInfoComponent_ng_container_2_Template, 72, 33, \"ng-container\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 3)(4, \"button\", 4);\n          i0.ɵɵelement(5, \"img\", 5);\n          i0.ɵɵtext(6, \" Back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function GeneralInfoComponent_Template_button_click_7_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtext(8, \"Next \");\n          i0.ɵɵelement(9, \"img\", 7);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i8.NgForOf, i8.NgIf, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i2.FormGroupName, i2.FormArrayName, i9.Dropdown, i7.PrimeTemplate, i10.PrimeInputDropdownComponent, i11.ButtonDirective, i12.Calendar, i13.Ripple],\n      styles: [\".modal[_ngcontent-%COMP%]{min-width:50vw}[_nghost-%COMP%]     .p-dropdown:not(.p-disabled):hover{border:2px solid rgb(228,185,84)}  .no-label>.p-button-label{display:none}input[type=file][_ngcontent-%COMP%]{visibility:hidden;width:10px}input[type=date][_ngcontent-%COMP%]{position:relative;padding:10px}input[type=date][_ngcontent-%COMP%]::-webkit-calendar-picker-indicator{color:transparent;background:none;z-index:1}input[type=date][_ngcontent-%COMP%]:before{color:transparent;background:none;display:block;font-family:FontAwesome;content:\\\"\\\\f073\\\";width:20px;height:25px;position:absolute;top:12px;right:12px;color:#999}.image-preview[_ngcontent-%COMP%]{height:10rem;margin:1rem 0;border-radius:50%;border:3px solid var(--main-color)}.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{height:100%}[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-header{padding:clamp(.38rem,.47vw + .28rem,.75rem)}[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-items .p-dropdown-item{margin-bottom:clamp(.38rem,.47vw + .28rem,.75rem)}.country-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:clamp(.75rem,.31vw + .69rem,1rem);font-size:clamp(.88rem,.16vw + .84rem,1rem)}.country-item[_ngcontent-%COMP%]   .country-name[_ngcontent-%COMP%]{white-space:pre-line}.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]{width:32px;height:32px;border-radius:50%;display:inline-block;vertical-align:middle}.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100%;clip-path:circle()}.country-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:32px;margin-right:.5rem}.country-item[_ngcontent-%COMP%]   img.smaller[_ngcontent-%COMP%]{width:16px}.info-element[_ngcontent-%COMP%]{padding:10px 0}.responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}.white-button[_ngcontent-%COMP%]{padding:10px}.dd-style[_ngcontent-%COMP%]{border:1px solid #aaa}p-dropdown[_ngcontent-%COMP%]{width:100%}[_nghost-%COMP%]     .p-calendar.p-calendar-w-btn{display:flex;align-items:center;justify-content:center}[_nghost-%COMP%]     .date-element{display:flex;align-items:center;justify-content:center}.trash[_ngcontent-%COMP%]{margin-left:auto;cursor:pointer}.trash[_ngcontent-%COMP%] > img[_ngcontent-%COMP%]{width:30px;margin-top:5px}[_nghost-%COMP%]     .small-dropdown-items .p-dropdown-panel .p-dropdown-items .p-dropdown-item{padding:0}[_nghost-%COMP%]     .small-dropdown-items .p-dropdown-items{min-width:clamp(10rem,2.34vw + 9.53rem,11.88rem)}.phonecode-item[_ngcontent-%COMP%]{align-items:center;display:flex;gap:clamp(.75rem,.31vw + .69rem,1rem);font-size:clamp(.88rem,.16vw + .84rem,1rem)}.phonecode-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:clamp(1.5rem,.63vw + 1.38rem,2rem)}\"]\n    });\n  }\n  return GeneralInfoComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}