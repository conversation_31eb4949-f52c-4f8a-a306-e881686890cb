{"ast": null, "code": "import { Splitter, hasBgRendering, createFormatter, ViewContextType, ContentContainer, BaseComponent, DateComponent, diffDays, buildNavLinkAttrs, WeekNumberContainer, getStickyHeaderDates, ViewContainer, SimpleScrollGrid, getStickyFooterScrollbar, NowTimer, NowIndicatorContainer, renderScrollShim, rangeContainsMarker, startOfDay, asRoughMs, createDuration, RefMap, PositionCache, MoreLinkContainer, SegHierarchy, groupIntersectingEntries, binarySearch, getEntrySpanEnd, buildEntryKey, StandardEvent, memoize, sortEventSegs, DayCellContainer, hasCustomDayCellContent, getSegMeta, buildIsoString, computeEarliestSegStart, buildEventRangeKey, BgEvent, renderFill, addDurations, multiplyDuration, wholeDivideDurations, Slicer, intersectRanges, formatIsoTimeString, DayHeader, DaySeriesModel, DayTableModel, injectStyles } from '@fullcalendar/core/internal.js';\nimport { createElement, createRef, Fragment } from '@fullcalendar/core/preact.js';\nimport { DayTable } from '@fullcalendar/daygrid/internal.js';\nclass AllDaySplitter extends Splitter {\n  getKeyInfo() {\n    return {\n      allDay: {},\n      timed: {}\n    };\n  }\n  getKeysForDateSpan(dateSpan) {\n    if (dateSpan.allDay) {\n      return ['allDay'];\n    }\n    return ['timed'];\n  }\n  getKeysForEventDef(eventDef) {\n    if (!eventDef.allDay) {\n      return ['timed'];\n    }\n    if (hasBgRendering(eventDef)) {\n      return ['timed', 'allDay'];\n    }\n    return ['allDay'];\n  }\n}\nconst DEFAULT_SLAT_LABEL_FORMAT = createFormatter({\n  hour: 'numeric',\n  minute: '2-digit',\n  omitZeroMinute: true,\n  meridiem: 'short'\n});\nfunction TimeColsAxisCell(props) {\n  let classNames = ['fc-timegrid-slot', 'fc-timegrid-slot-label', props.isLabeled ? 'fc-scrollgrid-shrink' : 'fc-timegrid-slot-minor'];\n  return createElement(ViewContextType.Consumer, null, context => {\n    if (!props.isLabeled) {\n      return createElement(\"td\", {\n        className: classNames.join(' '),\n        \"data-time\": props.isoTimeStr\n      });\n    }\n    let {\n      dateEnv,\n      options,\n      viewApi\n    } = context;\n    let labelFormat =\n    // TODO: fully pre-parse\n    options.slotLabelFormat == null ? DEFAULT_SLAT_LABEL_FORMAT : Array.isArray(options.slotLabelFormat) ? createFormatter(options.slotLabelFormat[0]) : createFormatter(options.slotLabelFormat);\n    let renderProps = {\n      level: 0,\n      time: props.time,\n      date: dateEnv.toDate(props.date),\n      view: viewApi,\n      text: dateEnv.format(props.date, labelFormat)\n    };\n    return createElement(ContentContainer, {\n      elTag: \"td\",\n      elClasses: classNames,\n      elAttrs: {\n        'data-time': props.isoTimeStr\n      },\n      renderProps: renderProps,\n      generatorName: \"slotLabelContent\",\n      customGenerator: options.slotLabelContent,\n      defaultGenerator: renderInnerContent,\n      classNameGenerator: options.slotLabelClassNames,\n      didMount: options.slotLabelDidMount,\n      willUnmount: options.slotLabelWillUnmount\n    }, InnerContent => createElement(\"div\", {\n      className: \"fc-timegrid-slot-label-frame fc-scrollgrid-shrink-frame\"\n    }, createElement(InnerContent, {\n      elTag: \"div\",\n      elClasses: ['fc-timegrid-slot-label-cushion', 'fc-scrollgrid-shrink-cushion']\n    })));\n  });\n}\nfunction renderInnerContent(props) {\n  return props.text;\n}\nclass TimeBodyAxis extends BaseComponent {\n  render() {\n    return this.props.slatMetas.map(slatMeta => createElement(\"tr\", {\n      key: slatMeta.key\n    }, createElement(TimeColsAxisCell, Object.assign({}, slatMeta))));\n  }\n}\nconst DEFAULT_WEEK_NUM_FORMAT = createFormatter({\n  week: 'short'\n});\nconst AUTO_ALL_DAY_MAX_EVENT_ROWS = 5;\nclass TimeColsView extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.allDaySplitter = new AllDaySplitter(); // for use by subclasses\n    this.headerElRef = createRef();\n    this.rootElRef = createRef();\n    this.scrollerElRef = createRef();\n    this.state = {\n      slatCoords: null\n    };\n    this.handleScrollTopRequest = scrollTop => {\n      let scrollerEl = this.scrollerElRef.current;\n      if (scrollerEl) {\n        // TODO: not sure how this could ever be null. weirdness with the reducer\n        scrollerEl.scrollTop = scrollTop;\n      }\n    };\n    /* Header Render Methods\n    ------------------------------------------------------------------------------------------------------------------*/\n    this.renderHeadAxis = (rowKey, frameHeight = '') => {\n      let {\n        options\n      } = this.context;\n      let {\n        dateProfile\n      } = this.props;\n      let range = dateProfile.renderRange;\n      let dayCnt = diffDays(range.start, range.end);\n      // only do in day views (to avoid doing in week views that dont need it)\n      let navLinkAttrs = dayCnt === 1 ? buildNavLinkAttrs(this.context, range.start, 'week') : {};\n      if (options.weekNumbers && rowKey === 'day') {\n        return createElement(WeekNumberContainer, {\n          elTag: \"th\",\n          elClasses: ['fc-timegrid-axis', 'fc-scrollgrid-shrink'],\n          elAttrs: {\n            'aria-hidden': true\n          },\n          date: range.start,\n          defaultFormat: DEFAULT_WEEK_NUM_FORMAT\n        }, InnerContent => createElement(\"div\", {\n          className: ['fc-timegrid-axis-frame', 'fc-scrollgrid-shrink-frame', 'fc-timegrid-axis-frame-liquid'].join(' '),\n          style: {\n            height: frameHeight\n          }\n        }, createElement(InnerContent, {\n          elTag: \"a\",\n          elClasses: ['fc-timegrid-axis-cushion', 'fc-scrollgrid-shrink-cushion', 'fc-scrollgrid-sync-inner'],\n          elAttrs: navLinkAttrs\n        })));\n      }\n      return createElement(\"th\", {\n        \"aria-hidden\": true,\n        className: \"fc-timegrid-axis\"\n      }, createElement(\"div\", {\n        className: \"fc-timegrid-axis-frame\",\n        style: {\n          height: frameHeight\n        }\n      }));\n    };\n    /* Table Component Render Methods\n    ------------------------------------------------------------------------------------------------------------------*/\n    // only a one-way height sync. we don't send the axis inner-content height to the DayGrid,\n    // but DayGrid still needs to have classNames on inner elements in order to measure.\n    this.renderTableRowAxis = rowHeight => {\n      let {\n        options,\n        viewApi\n      } = this.context;\n      let renderProps = {\n        text: options.allDayText,\n        view: viewApi\n      };\n      return (\n        // TODO: make reusable hook. used in list view too\n        createElement(ContentContainer, {\n          elTag: \"td\",\n          elClasses: ['fc-timegrid-axis', 'fc-scrollgrid-shrink'],\n          elAttrs: {\n            'aria-hidden': true\n          },\n          renderProps: renderProps,\n          generatorName: \"allDayContent\",\n          customGenerator: options.allDayContent,\n          defaultGenerator: renderAllDayInner,\n          classNameGenerator: options.allDayClassNames,\n          didMount: options.allDayDidMount,\n          willUnmount: options.allDayWillUnmount\n        }, InnerContent => createElement(\"div\", {\n          className: ['fc-timegrid-axis-frame', 'fc-scrollgrid-shrink-frame', rowHeight == null ? ' fc-timegrid-axis-frame-liquid' : ''].join(' '),\n          style: {\n            height: rowHeight\n          }\n        }, createElement(InnerContent, {\n          elTag: \"span\",\n          elClasses: ['fc-timegrid-axis-cushion', 'fc-scrollgrid-shrink-cushion', 'fc-scrollgrid-sync-inner']\n        })))\n      );\n    };\n    this.handleSlatCoords = slatCoords => {\n      this.setState({\n        slatCoords\n      });\n    };\n  }\n  // rendering\n  // ----------------------------------------------------------------------------------------------------\n  renderSimpleLayout(headerRowContent, allDayContent, timeContent) {\n    let {\n      context,\n      props\n    } = this;\n    let sections = [];\n    let stickyHeaderDates = getStickyHeaderDates(context.options);\n    if (headerRowContent) {\n      sections.push({\n        type: 'header',\n        key: 'header',\n        isSticky: stickyHeaderDates,\n        chunk: {\n          elRef: this.headerElRef,\n          tableClassName: 'fc-col-header',\n          rowContent: headerRowContent\n        }\n      });\n    }\n    if (allDayContent) {\n      sections.push({\n        type: 'body',\n        key: 'all-day',\n        chunk: {\n          content: allDayContent\n        }\n      });\n      sections.push({\n        type: 'body',\n        key: 'all-day-divider',\n        outerContent:\n        // TODO: rename to cellContent so don't need to define <tr>?\n        createElement(\"tr\", {\n          role: \"presentation\",\n          className: \"fc-scrollgrid-section\"\n        }, createElement(\"td\", {\n          className: 'fc-timegrid-divider ' + context.theme.getClass('tableCellShaded')\n        }))\n      });\n    }\n    sections.push({\n      type: 'body',\n      key: 'body',\n      liquid: true,\n      expandRows: Boolean(context.options.expandRows),\n      chunk: {\n        scrollerElRef: this.scrollerElRef,\n        content: timeContent\n      }\n    });\n    return createElement(ViewContainer, {\n      elRef: this.rootElRef,\n      elClasses: ['fc-timegrid'],\n      viewSpec: context.viewSpec\n    }, createElement(SimpleScrollGrid, {\n      liquid: !props.isHeightAuto && !props.forPrint,\n      collapsibleWidth: props.forPrint,\n      cols: [{\n        width: 'shrink'\n      }],\n      sections: sections\n    }));\n  }\n  renderHScrollLayout(headerRowContent, allDayContent, timeContent, colCnt, dayMinWidth, slatMetas, slatCoords) {\n    let ScrollGrid = this.context.pluginHooks.scrollGridImpl;\n    if (!ScrollGrid) {\n      throw new Error('No ScrollGrid implementation');\n    }\n    let {\n      context,\n      props\n    } = this;\n    let stickyHeaderDates = !props.forPrint && getStickyHeaderDates(context.options);\n    let stickyFooterScrollbar = !props.forPrint && getStickyFooterScrollbar(context.options);\n    let sections = [];\n    if (headerRowContent) {\n      sections.push({\n        type: 'header',\n        key: 'header',\n        isSticky: stickyHeaderDates,\n        syncRowHeights: true,\n        chunks: [{\n          key: 'axis',\n          rowContent: arg => createElement(\"tr\", {\n            role: \"presentation\"\n          }, this.renderHeadAxis('day', arg.rowSyncHeights[0]))\n        }, {\n          key: 'cols',\n          elRef: this.headerElRef,\n          tableClassName: 'fc-col-header',\n          rowContent: headerRowContent\n        }]\n      });\n    }\n    if (allDayContent) {\n      sections.push({\n        type: 'body',\n        key: 'all-day',\n        syncRowHeights: true,\n        chunks: [{\n          key: 'axis',\n          rowContent: contentArg => createElement(\"tr\", {\n            role: \"presentation\"\n          }, this.renderTableRowAxis(contentArg.rowSyncHeights[0]))\n        }, {\n          key: 'cols',\n          content: allDayContent\n        }]\n      });\n      sections.push({\n        key: 'all-day-divider',\n        type: 'body',\n        outerContent:\n        // TODO: rename to cellContent so don't need to define <tr>?\n        createElement(\"tr\", {\n          role: \"presentation\",\n          className: \"fc-scrollgrid-section\"\n        }, createElement(\"td\", {\n          colSpan: 2,\n          className: 'fc-timegrid-divider ' + context.theme.getClass('tableCellShaded')\n        }))\n      });\n    }\n    let isNowIndicator = context.options.nowIndicator;\n    sections.push({\n      type: 'body',\n      key: 'body',\n      liquid: true,\n      expandRows: Boolean(context.options.expandRows),\n      chunks: [{\n        key: 'axis',\n        content: arg =>\n        // TODO: make this now-indicator arrow more DRY with TimeColsContent\n        createElement(\"div\", {\n          className: \"fc-timegrid-axis-chunk\"\n        }, createElement(\"table\", {\n          \"aria-hidden\": true,\n          style: {\n            height: arg.expandRows ? arg.clientHeight : ''\n          }\n        }, arg.tableColGroupNode, createElement(\"tbody\", null, createElement(TimeBodyAxis, {\n          slatMetas: slatMetas\n        }))), createElement(\"div\", {\n          className: \"fc-timegrid-now-indicator-container\"\n        }, createElement(NowTimer, {\n          unit: isNowIndicator ? 'minute' : 'day' /* hacky */\n        }, nowDate => {\n          let nowIndicatorTop = isNowIndicator && slatCoords && slatCoords.safeComputeTop(nowDate); // might return void\n          if (typeof nowIndicatorTop === 'number') {\n            return createElement(NowIndicatorContainer, {\n              elClasses: ['fc-timegrid-now-indicator-arrow'],\n              elStyle: {\n                top: nowIndicatorTop\n              },\n              isAxis: true,\n              date: nowDate\n            });\n          }\n          return null;\n        })))\n      }, {\n        key: 'cols',\n        scrollerElRef: this.scrollerElRef,\n        content: timeContent\n      }]\n    });\n    if (stickyFooterScrollbar) {\n      sections.push({\n        key: 'footer',\n        type: 'footer',\n        isSticky: true,\n        chunks: [{\n          key: 'axis',\n          content: renderScrollShim\n        }, {\n          key: 'cols',\n          content: renderScrollShim\n        }]\n      });\n    }\n    return createElement(ViewContainer, {\n      elRef: this.rootElRef,\n      elClasses: ['fc-timegrid'],\n      viewSpec: context.viewSpec\n    }, createElement(ScrollGrid, {\n      liquid: !props.isHeightAuto && !props.forPrint,\n      forPrint: props.forPrint,\n      collapsibleWidth: false,\n      colGroups: [{\n        width: 'shrink',\n        cols: [{\n          width: 'shrink'\n        }]\n      }, {\n        cols: [{\n          span: colCnt,\n          minWidth: dayMinWidth\n        }]\n      }],\n      sections: sections\n    }));\n  }\n  /* Dimensions\n  ------------------------------------------------------------------------------------------------------------------*/\n  getAllDayMaxEventProps() {\n    let {\n      dayMaxEvents,\n      dayMaxEventRows\n    } = this.context.options;\n    if (dayMaxEvents === true || dayMaxEventRows === true) {\n      // is auto?\n      dayMaxEvents = undefined;\n      dayMaxEventRows = AUTO_ALL_DAY_MAX_EVENT_ROWS; // make sure \"auto\" goes to a real number\n    }\n    return {\n      dayMaxEvents,\n      dayMaxEventRows\n    };\n  }\n}\nfunction renderAllDayInner(renderProps) {\n  return renderProps.text;\n}\nclass TimeColsSlatsCoords {\n  constructor(positions, dateProfile, slotDuration) {\n    this.positions = positions;\n    this.dateProfile = dateProfile;\n    this.slotDuration = slotDuration;\n  }\n  safeComputeTop(date) {\n    let {\n      dateProfile\n    } = this;\n    if (rangeContainsMarker(dateProfile.currentRange, date)) {\n      let startOfDayDate = startOfDay(date);\n      let timeMs = date.valueOf() - startOfDayDate.valueOf();\n      if (timeMs >= asRoughMs(dateProfile.slotMinTime) && timeMs < asRoughMs(dateProfile.slotMaxTime)) {\n        return this.computeTimeTop(createDuration(timeMs));\n      }\n    }\n    return null;\n  }\n  // Computes the top coordinate, relative to the bounds of the grid, of the given date.\n  // A `startOfDayDate` must be given for avoiding ambiguity over how to treat midnight.\n  computeDateTop(when, startOfDayDate) {\n    if (!startOfDayDate) {\n      startOfDayDate = startOfDay(when);\n    }\n    return this.computeTimeTop(createDuration(when.valueOf() - startOfDayDate.valueOf()));\n  }\n  // Computes the top coordinate, relative to the bounds of the grid, of the given time (a Duration).\n  // This is a makeshify way to compute the time-top. Assumes all slatMetas dates are uniform.\n  // Eventually allow computation with arbirary slat dates.\n  computeTimeTop(duration) {\n    let {\n      positions,\n      dateProfile\n    } = this;\n    let len = positions.els.length;\n    // floating-point value of # of slots covered\n    let slatCoverage = (duration.milliseconds - asRoughMs(dateProfile.slotMinTime)) / asRoughMs(this.slotDuration);\n    let slatIndex;\n    let slatRemainder;\n    // compute a floating-point number for how many slats should be progressed through.\n    // from 0 to number of slats (inclusive)\n    // constrained because slotMinTime/slotMaxTime might be customized.\n    slatCoverage = Math.max(0, slatCoverage);\n    slatCoverage = Math.min(len, slatCoverage);\n    // an integer index of the furthest whole slat\n    // from 0 to number slats (*exclusive*, so len-1)\n    slatIndex = Math.floor(slatCoverage);\n    slatIndex = Math.min(slatIndex, len - 1);\n    // how much further through the slatIndex slat (from 0.0-1.0) must be covered in addition.\n    // could be 1.0 if slatCoverage is covering *all* the slots\n    slatRemainder = slatCoverage - slatIndex;\n    return positions.tops[slatIndex] + positions.getHeight(slatIndex) * slatRemainder;\n  }\n}\nclass TimeColsSlatsBody extends BaseComponent {\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    let {\n      options\n    } = context;\n    let {\n      slatElRefs\n    } = props;\n    return createElement(\"tbody\", null, props.slatMetas.map((slatMeta, i) => {\n      let renderProps = {\n        time: slatMeta.time,\n        date: context.dateEnv.toDate(slatMeta.date),\n        view: context.viewApi\n      };\n      return createElement(\"tr\", {\n        key: slatMeta.key,\n        ref: slatElRefs.createRef(slatMeta.key)\n      }, props.axis && createElement(TimeColsAxisCell, Object.assign({}, slatMeta)), createElement(ContentContainer, {\n        elTag: \"td\",\n        elClasses: ['fc-timegrid-slot', 'fc-timegrid-slot-lane', !slatMeta.isLabeled && 'fc-timegrid-slot-minor'],\n        elAttrs: {\n          'data-time': slatMeta.isoTimeStr\n        },\n        renderProps: renderProps,\n        generatorName: \"slotLaneContent\",\n        customGenerator: options.slotLaneContent,\n        classNameGenerator: options.slotLaneClassNames,\n        didMount: options.slotLaneDidMount,\n        willUnmount: options.slotLaneWillUnmount\n      }));\n    }));\n  }\n}\n\n/*\nfor the horizontal \"slats\" that run width-wise. Has a time axis on a side. Depends on RTL.\n*/\nclass TimeColsSlats extends BaseComponent {\n  constructor() {\n    super(...arguments);\n    this.rootElRef = createRef();\n    this.slatElRefs = new RefMap();\n  }\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    return createElement(\"div\", {\n      ref: this.rootElRef,\n      className: \"fc-timegrid-slots\"\n    }, createElement(\"table\", {\n      \"aria-hidden\": true,\n      className: context.theme.getClass('table'),\n      style: {\n        minWidth: props.tableMinWidth,\n        width: props.clientWidth,\n        height: props.minHeight\n      }\n    }, props.tableColGroupNode /* relies on there only being a single <col> for the axis */, createElement(TimeColsSlatsBody, {\n      slatElRefs: this.slatElRefs,\n      axis: props.axis,\n      slatMetas: props.slatMetas\n    })));\n  }\n  componentDidMount() {\n    this.updateSizing();\n  }\n  componentDidUpdate() {\n    this.updateSizing();\n  }\n  componentWillUnmount() {\n    if (this.props.onCoords) {\n      this.props.onCoords(null);\n    }\n  }\n  updateSizing() {\n    let {\n      context,\n      props\n    } = this;\n    if (props.onCoords && props.clientWidth !== null // means sizing has stabilized\n    ) {\n      let rootEl = this.rootElRef.current;\n      if (rootEl.offsetHeight) {\n        // not hidden by css\n        props.onCoords(new TimeColsSlatsCoords(new PositionCache(this.rootElRef.current, collectSlatEls(this.slatElRefs.currentMap, props.slatMetas), false, true), this.props.dateProfile, context.options.slotDuration));\n      }\n    }\n  }\n}\nfunction collectSlatEls(elMap, slatMetas) {\n  return slatMetas.map(slatMeta => elMap[slatMeta.key]);\n}\nfunction splitSegsByCol(segs, colCnt) {\n  let segsByCol = [];\n  let i;\n  for (i = 0; i < colCnt; i += 1) {\n    segsByCol.push([]);\n  }\n  if (segs) {\n    for (i = 0; i < segs.length; i += 1) {\n      segsByCol[segs[i].col].push(segs[i]);\n    }\n  }\n  return segsByCol;\n}\nfunction splitInteractionByCol(ui, colCnt) {\n  let byRow = [];\n  if (!ui) {\n    for (let i = 0; i < colCnt; i += 1) {\n      byRow[i] = null;\n    }\n  } else {\n    for (let i = 0; i < colCnt; i += 1) {\n      byRow[i] = {\n        affectedInstances: ui.affectedInstances,\n        isEvent: ui.isEvent,\n        segs: []\n      };\n    }\n    for (let seg of ui.segs) {\n      byRow[seg.col].segs.push(seg);\n    }\n  }\n  return byRow;\n}\nclass TimeColMoreLink extends BaseComponent {\n  render() {\n    let {\n      props\n    } = this;\n    return createElement(MoreLinkContainer, {\n      elClasses: ['fc-timegrid-more-link'],\n      elStyle: {\n        top: props.top,\n        bottom: props.bottom\n      },\n      allDayDate: null,\n      moreCnt: props.hiddenSegs.length,\n      allSegs: props.hiddenSegs,\n      hiddenSegs: props.hiddenSegs,\n      extraDateSpan: props.extraDateSpan,\n      dateProfile: props.dateProfile,\n      todayRange: props.todayRange,\n      popoverContent: () => renderPlainFgSegs(props.hiddenSegs, props),\n      defaultGenerator: renderMoreLinkInner,\n      forceTimed: true\n    }, InnerContent => createElement(InnerContent, {\n      elTag: \"div\",\n      elClasses: ['fc-timegrid-more-link-inner', 'fc-sticky']\n    }));\n  }\n}\nfunction renderMoreLinkInner(props) {\n  return props.shortText;\n}\n\n// segInputs assumed sorted\nfunction buildPositioning(segInputs, strictOrder, maxStackCnt) {\n  let hierarchy = new SegHierarchy();\n  if (strictOrder != null) {\n    hierarchy.strictOrder = strictOrder;\n  }\n  if (maxStackCnt != null) {\n    hierarchy.maxStackCnt = maxStackCnt;\n  }\n  let hiddenEntries = hierarchy.addSegs(segInputs);\n  let hiddenGroups = groupIntersectingEntries(hiddenEntries);\n  let web = buildWeb(hierarchy);\n  web = stretchWeb(web, 1); // all levelCoords/thickness will have 0.0-1.0\n  let segRects = webToRects(web);\n  return {\n    segRects,\n    hiddenGroups\n  };\n}\nfunction buildWeb(hierarchy) {\n  const {\n    entriesByLevel\n  } = hierarchy;\n  const buildNode = cacheable((level, lateral) => level + ':' + lateral, (level, lateral) => {\n    let siblingRange = findNextLevelSegs(hierarchy, level, lateral);\n    let nextLevelRes = buildNodes(siblingRange, buildNode);\n    let entry = entriesByLevel[level][lateral];\n    return [Object.assign(Object.assign({}, entry), {\n      nextLevelNodes: nextLevelRes[0]\n    }), entry.thickness + nextLevelRes[1] // the pressure builds\n    ];\n  });\n  return buildNodes(entriesByLevel.length ? {\n    level: 0,\n    lateralStart: 0,\n    lateralEnd: entriesByLevel[0].length\n  } : null, buildNode)[0];\n}\nfunction buildNodes(siblingRange, buildNode) {\n  if (!siblingRange) {\n    return [[], 0];\n  }\n  let {\n    level,\n    lateralStart,\n    lateralEnd\n  } = siblingRange;\n  let lateral = lateralStart;\n  let pairs = [];\n  while (lateral < lateralEnd) {\n    pairs.push(buildNode(level, lateral));\n    lateral += 1;\n  }\n  pairs.sort(cmpDescPressures);\n  return [pairs.map(extractNode), pairs[0][1] // first item's pressure\n  ];\n}\nfunction cmpDescPressures(a, b) {\n  return b[1] - a[1];\n}\nfunction extractNode(a) {\n  return a[0];\n}\nfunction findNextLevelSegs(hierarchy, subjectLevel, subjectLateral) {\n  let {\n    levelCoords,\n    entriesByLevel\n  } = hierarchy;\n  let subjectEntry = entriesByLevel[subjectLevel][subjectLateral];\n  let afterSubject = levelCoords[subjectLevel] + subjectEntry.thickness;\n  let levelCnt = levelCoords.length;\n  let level = subjectLevel;\n  // skip past levels that are too high up\n  for (; level < levelCnt && levelCoords[level] < afterSubject; level += 1); // do nothing\n  for (; level < levelCnt; level += 1) {\n    let entries = entriesByLevel[level];\n    let entry;\n    let searchIndex = binarySearch(entries, subjectEntry.span.start, getEntrySpanEnd);\n    let lateralStart = searchIndex[0] + searchIndex[1]; // if exact match (which doesn't collide), go to next one\n    let lateralEnd = lateralStart;\n    while (\n    // loop through entries that horizontally intersect\n    (entry = entries[lateralEnd]) &&\n    // but not past the whole seg list\n    entry.span.start < subjectEntry.span.end) {\n      lateralEnd += 1;\n    }\n    if (lateralStart < lateralEnd) {\n      return {\n        level,\n        lateralStart,\n        lateralEnd\n      };\n    }\n  }\n  return null;\n}\nfunction stretchWeb(topLevelNodes, totalThickness) {\n  const stretchNode = cacheable((node, startCoord, prevThickness) => buildEntryKey(node), (node, startCoord, prevThickness) => {\n    let {\n      nextLevelNodes,\n      thickness\n    } = node;\n    let allThickness = thickness + prevThickness;\n    let thicknessFraction = thickness / allThickness;\n    let endCoord;\n    let newChildren = [];\n    if (!nextLevelNodes.length) {\n      endCoord = totalThickness;\n    } else {\n      for (let childNode of nextLevelNodes) {\n        if (endCoord === undefined) {\n          let res = stretchNode(childNode, startCoord, allThickness);\n          endCoord = res[0];\n          newChildren.push(res[1]);\n        } else {\n          let res = stretchNode(childNode, endCoord, 0);\n          newChildren.push(res[1]);\n        }\n      }\n    }\n    let newThickness = (endCoord - startCoord) * thicknessFraction;\n    return [endCoord - newThickness, Object.assign(Object.assign({}, node), {\n      thickness: newThickness,\n      nextLevelNodes: newChildren\n    })];\n  });\n  return topLevelNodes.map(node => stretchNode(node, 0, 0)[1]);\n}\n// not sorted in any particular order\nfunction webToRects(topLevelNodes) {\n  let rects = [];\n  const processNode = cacheable((node, levelCoord, stackDepth) => buildEntryKey(node), (node, levelCoord, stackDepth) => {\n    let rect = Object.assign(Object.assign({}, node), {\n      levelCoord,\n      stackDepth,\n      stackForward: 0\n    });\n    rects.push(rect);\n    return rect.stackForward = processNodes(node.nextLevelNodes, levelCoord + node.thickness, stackDepth + 1) + 1;\n  });\n  function processNodes(nodes, levelCoord, stackDepth) {\n    let stackForward = 0;\n    for (let node of nodes) {\n      stackForward = Math.max(processNode(node, levelCoord, stackDepth), stackForward);\n    }\n    return stackForward;\n  }\n  processNodes(topLevelNodes, 0, 0);\n  return rects; // TODO: sort rects by levelCoord to be consistent with toRects?\n}\n// TODO: move to general util\nfunction cacheable(keyFunc, workFunc) {\n  const cache = {};\n  return (...args) => {\n    let key = keyFunc(...args);\n    return key in cache ? cache[key] : cache[key] = workFunc(...args);\n  };\n}\nfunction computeSegVCoords(segs, colDate, slatCoords = null, eventMinHeight = 0) {\n  let vcoords = [];\n  if (slatCoords) {\n    for (let i = 0; i < segs.length; i += 1) {\n      let seg = segs[i];\n      let spanStart = slatCoords.computeDateTop(seg.start, colDate);\n      let spanEnd = Math.max(spanStart + (eventMinHeight || 0),\n      // :(\n      slatCoords.computeDateTop(seg.end, colDate));\n      vcoords.push({\n        start: Math.round(spanStart),\n        end: Math.round(spanEnd) //\n      });\n    }\n  }\n  return vcoords;\n}\nfunction computeFgSegPlacements(segs, segVCoords,\n// might not have for every seg\neventOrderStrict, eventMaxStack) {\n  let segInputs = [];\n  let dumbSegs = []; // segs without coords\n  for (let i = 0; i < segs.length; i += 1) {\n    let vcoords = segVCoords[i];\n    if (vcoords) {\n      segInputs.push({\n        index: i,\n        thickness: 1,\n        span: vcoords\n      });\n    } else {\n      dumbSegs.push(segs[i]);\n    }\n  }\n  let {\n    segRects,\n    hiddenGroups\n  } = buildPositioning(segInputs, eventOrderStrict, eventMaxStack);\n  let segPlacements = [];\n  for (let segRect of segRects) {\n    segPlacements.push({\n      seg: segs[segRect.index],\n      rect: segRect\n    });\n  }\n  for (let dumbSeg of dumbSegs) {\n    segPlacements.push({\n      seg: dumbSeg,\n      rect: null\n    });\n  }\n  return {\n    segPlacements,\n    hiddenGroups\n  };\n}\nconst DEFAULT_TIME_FORMAT = createFormatter({\n  hour: 'numeric',\n  minute: '2-digit',\n  meridiem: false\n});\nclass TimeColEvent extends BaseComponent {\n  render() {\n    return createElement(StandardEvent, Object.assign({}, this.props, {\n      elClasses: ['fc-timegrid-event', 'fc-v-event', this.props.isShort && 'fc-timegrid-event-short'],\n      defaultTimeFormat: DEFAULT_TIME_FORMAT\n    }));\n  }\n}\nclass TimeCol extends BaseComponent {\n  constructor() {\n    super(...arguments);\n    this.sortEventSegs = memoize(sortEventSegs);\n  }\n  // TODO: memoize event-placement?\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    let {\n      options\n    } = context;\n    let isSelectMirror = options.selectMirror;\n    let mirrorSegs =\n    // yuck\n    props.eventDrag && props.eventDrag.segs || props.eventResize && props.eventResize.segs || isSelectMirror && props.dateSelectionSegs || [];\n    let interactionAffectedInstances =\n    // TODO: messy way to compute this\n    props.eventDrag && props.eventDrag.affectedInstances || props.eventResize && props.eventResize.affectedInstances || {};\n    let sortedFgSegs = this.sortEventSegs(props.fgEventSegs, options.eventOrder);\n    return createElement(DayCellContainer, {\n      elTag: \"td\",\n      elRef: props.elRef,\n      elClasses: ['fc-timegrid-col', ...(props.extraClassNames || [])],\n      elAttrs: Object.assign({\n        role: 'gridcell'\n      }, props.extraDataAttrs),\n      date: props.date,\n      dateProfile: props.dateProfile,\n      todayRange: props.todayRange,\n      extraRenderProps: props.extraRenderProps\n    }, InnerContent => createElement(\"div\", {\n      className: \"fc-timegrid-col-frame\"\n    }, createElement(\"div\", {\n      className: \"fc-timegrid-col-bg\"\n    }, this.renderFillSegs(props.businessHourSegs, 'non-business'), this.renderFillSegs(props.bgEventSegs, 'bg-event'), this.renderFillSegs(props.dateSelectionSegs, 'highlight')), createElement(\"div\", {\n      className: \"fc-timegrid-col-events\"\n    }, this.renderFgSegs(sortedFgSegs, interactionAffectedInstances, false, false, false)), createElement(\"div\", {\n      className: \"fc-timegrid-col-events\"\n    }, this.renderFgSegs(mirrorSegs, {}, Boolean(props.eventDrag), Boolean(props.eventResize), Boolean(isSelectMirror), 'mirror')), createElement(\"div\", {\n      className: \"fc-timegrid-now-indicator-container\"\n    }, this.renderNowIndicator(props.nowIndicatorSegs)), hasCustomDayCellContent(options) && createElement(InnerContent, {\n      elTag: \"div\",\n      elClasses: ['fc-timegrid-col-misc']\n    })));\n  }\n  renderFgSegs(sortedFgSegs, segIsInvisible, isDragging, isResizing, isDateSelecting, forcedKey) {\n    let {\n      props\n    } = this;\n    if (props.forPrint) {\n      return renderPlainFgSegs(sortedFgSegs, props);\n    }\n    return this.renderPositionedFgSegs(sortedFgSegs, segIsInvisible, isDragging, isResizing, isDateSelecting, forcedKey);\n  }\n  renderPositionedFgSegs(segs,\n  // if not mirror, needs to be sorted\n  segIsInvisible, isDragging, isResizing, isDateSelecting, forcedKey) {\n    let {\n      eventMaxStack,\n      eventShortHeight,\n      eventOrderStrict,\n      eventMinHeight\n    } = this.context.options;\n    let {\n      date,\n      slatCoords,\n      eventSelection,\n      todayRange,\n      nowDate\n    } = this.props;\n    let isMirror = isDragging || isResizing || isDateSelecting;\n    let segVCoords = computeSegVCoords(segs, date, slatCoords, eventMinHeight);\n    let {\n      segPlacements,\n      hiddenGroups\n    } = computeFgSegPlacements(segs, segVCoords, eventOrderStrict, eventMaxStack);\n    return createElement(Fragment, null, this.renderHiddenGroups(hiddenGroups, segs), segPlacements.map(segPlacement => {\n      let {\n        seg,\n        rect\n      } = segPlacement;\n      let instanceId = seg.eventRange.instance.instanceId;\n      let isVisible = isMirror || Boolean(!segIsInvisible[instanceId] && rect);\n      let vStyle = computeSegVStyle(rect && rect.span);\n      let hStyle = !isMirror && rect ? this.computeSegHStyle(rect) : {\n        left: 0,\n        right: 0\n      };\n      let isInset = Boolean(rect) && rect.stackForward > 0;\n      let isShort = Boolean(rect) && rect.span.end - rect.span.start < eventShortHeight; // look at other places for this problem\n      return createElement(\"div\", {\n        className: 'fc-timegrid-event-harness' + (isInset ? ' fc-timegrid-event-harness-inset' : ''),\n        key: forcedKey || instanceId,\n        style: Object.assign(Object.assign({\n          visibility: isVisible ? '' : 'hidden'\n        }, vStyle), hStyle)\n      }, createElement(TimeColEvent, Object.assign({\n        seg: seg,\n        isDragging: isDragging,\n        isResizing: isResizing,\n        isDateSelecting: isDateSelecting,\n        isSelected: instanceId === eventSelection,\n        isShort: isShort\n      }, getSegMeta(seg, todayRange, nowDate))));\n    }));\n  }\n  // will already have eventMinHeight applied because segInputs already had it\n  renderHiddenGroups(hiddenGroups, segs) {\n    let {\n      extraDateSpan,\n      dateProfile,\n      todayRange,\n      nowDate,\n      eventSelection,\n      eventDrag,\n      eventResize\n    } = this.props;\n    return createElement(Fragment, null, hiddenGroups.map(hiddenGroup => {\n      let positionCss = computeSegVStyle(hiddenGroup.span);\n      let hiddenSegs = compileSegsFromEntries(hiddenGroup.entries, segs);\n      return createElement(TimeColMoreLink, {\n        key: buildIsoString(computeEarliestSegStart(hiddenSegs)),\n        hiddenSegs: hiddenSegs,\n        top: positionCss.top,\n        bottom: positionCss.bottom,\n        extraDateSpan: extraDateSpan,\n        dateProfile: dateProfile,\n        todayRange: todayRange,\n        nowDate: nowDate,\n        eventSelection: eventSelection,\n        eventDrag: eventDrag,\n        eventResize: eventResize\n      });\n    }));\n  }\n  renderFillSegs(segs, fillType) {\n    let {\n      props,\n      context\n    } = this;\n    let segVCoords = computeSegVCoords(segs, props.date, props.slatCoords, context.options.eventMinHeight); // don't assume all populated\n    let children = segVCoords.map((vcoords, i) => {\n      let seg = segs[i];\n      return createElement(\"div\", {\n        key: buildEventRangeKey(seg.eventRange),\n        className: \"fc-timegrid-bg-harness\",\n        style: computeSegVStyle(vcoords)\n      }, fillType === 'bg-event' ? createElement(BgEvent, Object.assign({\n        seg: seg\n      }, getSegMeta(seg, props.todayRange, props.nowDate))) : renderFill(fillType));\n    });\n    return createElement(Fragment, null, children);\n  }\n  renderNowIndicator(segs) {\n    let {\n      slatCoords,\n      date\n    } = this.props;\n    if (!slatCoords) {\n      return null;\n    }\n    return segs.map((seg, i) => createElement(NowIndicatorContainer\n    // key doesn't matter. will only ever be one\n    , {\n      // key doesn't matter. will only ever be one\n      key: i,\n      elClasses: ['fc-timegrid-now-indicator-line'],\n      elStyle: {\n        top: slatCoords.computeDateTop(seg.start, date)\n      },\n      isAxis: false,\n      date: date\n    }));\n  }\n  computeSegHStyle(segHCoords) {\n    let {\n      isRtl,\n      options\n    } = this.context;\n    let shouldOverlap = options.slotEventOverlap;\n    let nearCoord = segHCoords.levelCoord; // the left side if LTR. the right side if RTL. floating-point\n    let farCoord = segHCoords.levelCoord + segHCoords.thickness; // the right side if LTR. the left side if RTL. floating-point\n    let left; // amount of space from left edge, a fraction of the total width\n    let right; // amount of space from right edge, a fraction of the total width\n    if (shouldOverlap) {\n      // double the width, but don't go beyond the maximum forward coordinate (1.0)\n      farCoord = Math.min(1, nearCoord + (farCoord - nearCoord) * 2);\n    }\n    if (isRtl) {\n      left = 1 - farCoord;\n      right = nearCoord;\n    } else {\n      left = nearCoord;\n      right = 1 - farCoord;\n    }\n    let props = {\n      zIndex: segHCoords.stackDepth + 1,\n      left: left * 100 + '%',\n      right: right * 100 + '%'\n    };\n    if (shouldOverlap && !segHCoords.stackForward) {\n      // add padding to the edge so that forward stacked events don't cover the resizer's icon\n      props[isRtl ? 'marginLeft' : 'marginRight'] = 10 * 2; // 10 is a guesstimate of the icon's width\n    }\n    return props;\n  }\n}\nfunction renderPlainFgSegs(sortedFgSegs, {\n  todayRange,\n  nowDate,\n  eventSelection,\n  eventDrag,\n  eventResize\n}) {\n  let hiddenInstances = (eventDrag ? eventDrag.affectedInstances : null) || (eventResize ? eventResize.affectedInstances : null) || {};\n  return createElement(Fragment, null, sortedFgSegs.map(seg => {\n    let instanceId = seg.eventRange.instance.instanceId;\n    return createElement(\"div\", {\n      key: instanceId,\n      style: {\n        visibility: hiddenInstances[instanceId] ? 'hidden' : ''\n      }\n    }, createElement(TimeColEvent, Object.assign({\n      seg: seg,\n      isDragging: false,\n      isResizing: false,\n      isDateSelecting: false,\n      isSelected: instanceId === eventSelection,\n      isShort: false\n    }, getSegMeta(seg, todayRange, nowDate))));\n  }));\n}\nfunction computeSegVStyle(segVCoords) {\n  if (!segVCoords) {\n    return {\n      top: '',\n      bottom: ''\n    };\n  }\n  return {\n    top: segVCoords.start,\n    bottom: -segVCoords.end\n  };\n}\nfunction compileSegsFromEntries(segEntries, allSegs) {\n  return segEntries.map(segEntry => allSegs[segEntry.index]);\n}\nclass TimeColsContent extends BaseComponent {\n  constructor() {\n    super(...arguments);\n    this.splitFgEventSegs = memoize(splitSegsByCol);\n    this.splitBgEventSegs = memoize(splitSegsByCol);\n    this.splitBusinessHourSegs = memoize(splitSegsByCol);\n    this.splitNowIndicatorSegs = memoize(splitSegsByCol);\n    this.splitDateSelectionSegs = memoize(splitSegsByCol);\n    this.splitEventDrag = memoize(splitInteractionByCol);\n    this.splitEventResize = memoize(splitInteractionByCol);\n    this.rootElRef = createRef();\n    this.cellElRefs = new RefMap();\n  }\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    let nowIndicatorTop = context.options.nowIndicator && props.slatCoords && props.slatCoords.safeComputeTop(props.nowDate); // might return void\n    let colCnt = props.cells.length;\n    let fgEventSegsByRow = this.splitFgEventSegs(props.fgEventSegs, colCnt);\n    let bgEventSegsByRow = this.splitBgEventSegs(props.bgEventSegs, colCnt);\n    let businessHourSegsByRow = this.splitBusinessHourSegs(props.businessHourSegs, colCnt);\n    let nowIndicatorSegsByRow = this.splitNowIndicatorSegs(props.nowIndicatorSegs, colCnt);\n    let dateSelectionSegsByRow = this.splitDateSelectionSegs(props.dateSelectionSegs, colCnt);\n    let eventDragByRow = this.splitEventDrag(props.eventDrag, colCnt);\n    let eventResizeByRow = this.splitEventResize(props.eventResize, colCnt);\n    return createElement(\"div\", {\n      className: \"fc-timegrid-cols\",\n      ref: this.rootElRef\n    }, createElement(\"table\", {\n      role: \"presentation\",\n      style: {\n        minWidth: props.tableMinWidth,\n        width: props.clientWidth\n      }\n    }, props.tableColGroupNode, createElement(\"tbody\", {\n      role: \"presentation\"\n    }, createElement(\"tr\", {\n      role: \"row\"\n    }, props.axis && createElement(\"td\", {\n      \"aria-hidden\": true,\n      className: \"fc-timegrid-col fc-timegrid-axis\"\n    }, createElement(\"div\", {\n      className: \"fc-timegrid-col-frame\"\n    }, createElement(\"div\", {\n      className: \"fc-timegrid-now-indicator-container\"\n    }, typeof nowIndicatorTop === 'number' && createElement(NowIndicatorContainer, {\n      elClasses: ['fc-timegrid-now-indicator-arrow'],\n      elStyle: {\n        top: nowIndicatorTop\n      },\n      isAxis: true,\n      date: props.nowDate\n    })))), props.cells.map((cell, i) => createElement(TimeCol, {\n      key: cell.key,\n      elRef: this.cellElRefs.createRef(cell.key),\n      dateProfile: props.dateProfile,\n      date: cell.date,\n      nowDate: props.nowDate,\n      todayRange: props.todayRange,\n      extraRenderProps: cell.extraRenderProps,\n      extraDataAttrs: cell.extraDataAttrs,\n      extraClassNames: cell.extraClassNames,\n      extraDateSpan: cell.extraDateSpan,\n      fgEventSegs: fgEventSegsByRow[i],\n      bgEventSegs: bgEventSegsByRow[i],\n      businessHourSegs: businessHourSegsByRow[i],\n      nowIndicatorSegs: nowIndicatorSegsByRow[i],\n      dateSelectionSegs: dateSelectionSegsByRow[i],\n      eventDrag: eventDragByRow[i],\n      eventResize: eventResizeByRow[i],\n      slatCoords: props.slatCoords,\n      eventSelection: props.eventSelection,\n      forPrint: props.forPrint\n    }))))));\n  }\n  componentDidMount() {\n    this.updateCoords();\n  }\n  componentDidUpdate() {\n    this.updateCoords();\n  }\n  updateCoords() {\n    let {\n      props\n    } = this;\n    if (props.onColCoords && props.clientWidth !== null // means sizing has stabilized\n    ) {\n      props.onColCoords(new PositionCache(this.rootElRef.current, collectCellEls(this.cellElRefs.currentMap, props.cells), true,\n      // horizontal\n      false));\n    }\n  }\n}\nfunction collectCellEls(elMap, cells) {\n  return cells.map(cell => elMap[cell.key]);\n}\n\n/* A component that renders one or more columns of vertical time slots\n----------------------------------------------------------------------------------------------------------------------*/\nclass TimeCols extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.processSlotOptions = memoize(processSlotOptions);\n    this.state = {\n      slatCoords: null\n    };\n    this.handleRootEl = el => {\n      if (el) {\n        this.context.registerInteractiveComponent(this, {\n          el,\n          isHitComboAllowed: this.props.isHitComboAllowed\n        });\n      } else {\n        this.context.unregisterInteractiveComponent(this);\n      }\n    };\n    this.handleScrollRequest = request => {\n      let {\n        onScrollTopRequest\n      } = this.props;\n      let {\n        slatCoords\n      } = this.state;\n      if (onScrollTopRequest && slatCoords) {\n        if (request.time) {\n          let top = slatCoords.computeTimeTop(request.time);\n          top = Math.ceil(top); // zoom can give weird floating-point values. rather scroll a little bit further\n          if (top) {\n            top += 1; // to overcome top border that slots beyond the first have. looks better\n          }\n          onScrollTopRequest(top);\n        }\n        return true;\n      }\n      return false;\n    };\n    this.handleColCoords = colCoords => {\n      this.colCoords = colCoords;\n    };\n    this.handleSlatCoords = slatCoords => {\n      this.setState({\n        slatCoords\n      });\n      if (this.props.onSlatCoords) {\n        this.props.onSlatCoords(slatCoords);\n      }\n    };\n  }\n  render() {\n    let {\n      props,\n      state\n    } = this;\n    return createElement(\"div\", {\n      className: \"fc-timegrid-body\",\n      ref: this.handleRootEl,\n      style: {\n        // these props are important to give this wrapper correct dimensions for interactions\n        // TODO: if we set it here, can we avoid giving to inner tables?\n        width: props.clientWidth,\n        minWidth: props.tableMinWidth\n      }\n    }, createElement(TimeColsSlats, {\n      axis: props.axis,\n      dateProfile: props.dateProfile,\n      slatMetas: props.slatMetas,\n      clientWidth: props.clientWidth,\n      minHeight: props.expandRows ? props.clientHeight : '',\n      tableMinWidth: props.tableMinWidth,\n      tableColGroupNode: props.axis ? props.tableColGroupNode : null /* axis depends on the colgroup's shrinking */,\n      onCoords: this.handleSlatCoords\n    }), createElement(TimeColsContent, {\n      cells: props.cells,\n      axis: props.axis,\n      dateProfile: props.dateProfile,\n      businessHourSegs: props.businessHourSegs,\n      bgEventSegs: props.bgEventSegs,\n      fgEventSegs: props.fgEventSegs,\n      dateSelectionSegs: props.dateSelectionSegs,\n      eventSelection: props.eventSelection,\n      eventDrag: props.eventDrag,\n      eventResize: props.eventResize,\n      todayRange: props.todayRange,\n      nowDate: props.nowDate,\n      nowIndicatorSegs: props.nowIndicatorSegs,\n      clientWidth: props.clientWidth,\n      tableMinWidth: props.tableMinWidth,\n      tableColGroupNode: props.tableColGroupNode,\n      slatCoords: state.slatCoords,\n      onColCoords: this.handleColCoords,\n      forPrint: props.forPrint\n    }));\n  }\n  componentDidMount() {\n    this.scrollResponder = this.context.createScrollResponder(this.handleScrollRequest);\n  }\n  componentDidUpdate(prevProps) {\n    this.scrollResponder.update(prevProps.dateProfile !== this.props.dateProfile);\n  }\n  componentWillUnmount() {\n    this.scrollResponder.detach();\n  }\n  queryHit(positionLeft, positionTop) {\n    let {\n      dateEnv,\n      options\n    } = this.context;\n    let {\n      colCoords\n    } = this;\n    let {\n      dateProfile\n    } = this.props;\n    let {\n      slatCoords\n    } = this.state;\n    let {\n      snapDuration,\n      snapsPerSlot\n    } = this.processSlotOptions(this.props.slotDuration, options.snapDuration);\n    let colIndex = colCoords.leftToIndex(positionLeft);\n    let slatIndex = slatCoords.positions.topToIndex(positionTop);\n    if (colIndex != null && slatIndex != null) {\n      let cell = this.props.cells[colIndex];\n      let slatTop = slatCoords.positions.tops[slatIndex];\n      let slatHeight = slatCoords.positions.getHeight(slatIndex);\n      let partial = (positionTop - slatTop) / slatHeight; // floating point number between 0 and 1\n      let localSnapIndex = Math.floor(partial * snapsPerSlot); // the snap # relative to start of slat\n      let snapIndex = slatIndex * snapsPerSlot + localSnapIndex;\n      let dayDate = this.props.cells[colIndex].date;\n      let time = addDurations(dateProfile.slotMinTime, multiplyDuration(snapDuration, snapIndex));\n      let start = dateEnv.add(dayDate, time);\n      let end = dateEnv.add(start, snapDuration);\n      return {\n        dateProfile,\n        dateSpan: Object.assign({\n          range: {\n            start,\n            end\n          },\n          allDay: false\n        }, cell.extraDateSpan),\n        dayEl: colCoords.els[colIndex],\n        rect: {\n          left: colCoords.lefts[colIndex],\n          right: colCoords.rights[colIndex],\n          top: slatTop,\n          bottom: slatTop + slatHeight\n        },\n        layer: 0\n      };\n    }\n    return null;\n  }\n}\nfunction processSlotOptions(slotDuration, snapDurationOverride) {\n  let snapDuration = snapDurationOverride || slotDuration;\n  let snapsPerSlot = wholeDivideDurations(slotDuration, snapDuration);\n  if (snapsPerSlot === null) {\n    snapDuration = slotDuration;\n    snapsPerSlot = 1;\n    // TODO: say warning?\n  }\n  return {\n    snapDuration,\n    snapsPerSlot\n  };\n}\nclass DayTimeColsSlicer extends Slicer {\n  sliceRange(range, dayRanges) {\n    let segs = [];\n    for (let col = 0; col < dayRanges.length; col += 1) {\n      let segRange = intersectRanges(range, dayRanges[col]);\n      if (segRange) {\n        segs.push({\n          start: segRange.start,\n          end: segRange.end,\n          isStart: segRange.start.valueOf() === range.start.valueOf(),\n          isEnd: segRange.end.valueOf() === range.end.valueOf(),\n          col\n        });\n      }\n    }\n    return segs;\n  }\n}\nclass DayTimeCols extends DateComponent {\n  constructor() {\n    super(...arguments);\n    this.buildDayRanges = memoize(buildDayRanges);\n    this.slicer = new DayTimeColsSlicer();\n    this.timeColsRef = createRef();\n  }\n  render() {\n    let {\n      props,\n      context\n    } = this;\n    let {\n      dateProfile,\n      dayTableModel\n    } = props;\n    let {\n      nowIndicator,\n      nextDayThreshold\n    } = context.options;\n    let dayRanges = this.buildDayRanges(dayTableModel, dateProfile, context.dateEnv);\n    // give it the first row of cells\n    // TODO: would move this further down hierarchy, but sliceNowDate needs it\n    return createElement(NowTimer, {\n      unit: nowIndicator ? 'minute' : 'day'\n    }, (nowDate, todayRange) => createElement(TimeCols, Object.assign({\n      ref: this.timeColsRef\n    }, this.slicer.sliceProps(props, dateProfile, null, context, dayRanges), {\n      forPrint: props.forPrint,\n      axis: props.axis,\n      dateProfile: dateProfile,\n      slatMetas: props.slatMetas,\n      slotDuration: props.slotDuration,\n      cells: dayTableModel.cells[0],\n      tableColGroupNode: props.tableColGroupNode,\n      tableMinWidth: props.tableMinWidth,\n      clientWidth: props.clientWidth,\n      clientHeight: props.clientHeight,\n      expandRows: props.expandRows,\n      nowDate: nowDate,\n      nowIndicatorSegs: nowIndicator && this.slicer.sliceNowDate(nowDate, dateProfile, nextDayThreshold, context, dayRanges),\n      todayRange: todayRange,\n      onScrollTopRequest: props.onScrollTopRequest,\n      onSlatCoords: props.onSlatCoords\n    })));\n  }\n}\nfunction buildDayRanges(dayTableModel, dateProfile, dateEnv) {\n  let ranges = [];\n  for (let date of dayTableModel.headerDates) {\n    ranges.push({\n      start: dateEnv.add(date, dateProfile.slotMinTime),\n      end: dateEnv.add(date, dateProfile.slotMaxTime)\n    });\n  }\n  return ranges;\n}\n\n// potential nice values for the slot-duration and interval-duration\n// from largest to smallest\nconst STOCK_SUB_DURATIONS = [{\n  hours: 1\n}, {\n  minutes: 30\n}, {\n  minutes: 15\n}, {\n  seconds: 30\n}, {\n  seconds: 15\n}];\nfunction buildSlatMetas(slotMinTime, slotMaxTime, explicitLabelInterval, slotDuration, dateEnv) {\n  let dayStart = new Date(0);\n  let slatTime = slotMinTime;\n  let slatIterator = createDuration(0);\n  let labelInterval = explicitLabelInterval || computeLabelInterval(slotDuration);\n  let metas = [];\n  while (asRoughMs(slatTime) < asRoughMs(slotMaxTime)) {\n    let date = dateEnv.add(dayStart, slatTime);\n    let isLabeled = wholeDivideDurations(slatIterator, labelInterval) !== null;\n    metas.push({\n      date,\n      time: slatTime,\n      key: date.toISOString(),\n      isoTimeStr: formatIsoTimeString(date),\n      isLabeled\n    });\n    slatTime = addDurations(slatTime, slotDuration);\n    slatIterator = addDurations(slatIterator, slotDuration);\n  }\n  return metas;\n}\n// Computes an automatic value for slotLabelInterval\nfunction computeLabelInterval(slotDuration) {\n  let i;\n  let labelInterval;\n  let slotsPerLabel;\n  // find the smallest stock label interval that results in more than one slots-per-label\n  for (i = STOCK_SUB_DURATIONS.length - 1; i >= 0; i -= 1) {\n    labelInterval = createDuration(STOCK_SUB_DURATIONS[i]);\n    slotsPerLabel = wholeDivideDurations(labelInterval, slotDuration);\n    if (slotsPerLabel !== null && slotsPerLabel > 1) {\n      return labelInterval;\n    }\n  }\n  return slotDuration; // fall back\n}\nclass DayTimeColsView extends TimeColsView {\n  constructor() {\n    super(...arguments);\n    this.buildTimeColsModel = memoize(buildTimeColsModel);\n    this.buildSlatMetas = memoize(buildSlatMetas);\n  }\n  render() {\n    let {\n      options,\n      dateEnv,\n      dateProfileGenerator\n    } = this.context;\n    let {\n      props\n    } = this;\n    let {\n      dateProfile\n    } = props;\n    let dayTableModel = this.buildTimeColsModel(dateProfile, dateProfileGenerator);\n    let splitProps = this.allDaySplitter.splitProps(props);\n    let slatMetas = this.buildSlatMetas(dateProfile.slotMinTime, dateProfile.slotMaxTime, options.slotLabelInterval, options.slotDuration, dateEnv);\n    let {\n      dayMinWidth\n    } = options;\n    let hasAttachedAxis = !dayMinWidth;\n    let hasDetachedAxis = dayMinWidth;\n    let headerContent = options.dayHeaders && createElement(DayHeader, {\n      dates: dayTableModel.headerDates,\n      dateProfile: dateProfile,\n      datesRepDistinctDays: true,\n      renderIntro: hasAttachedAxis ? this.renderHeadAxis : null\n    });\n    let allDayContent = options.allDaySlot !== false && (contentArg => createElement(DayTable, Object.assign({}, splitProps.allDay, {\n      dateProfile: dateProfile,\n      dayTableModel: dayTableModel,\n      nextDayThreshold: options.nextDayThreshold,\n      tableMinWidth: contentArg.tableMinWidth,\n      colGroupNode: contentArg.tableColGroupNode,\n      renderRowIntro: hasAttachedAxis ? this.renderTableRowAxis : null,\n      showWeekNumbers: false,\n      expandRows: false,\n      headerAlignElRef: this.headerElRef,\n      clientWidth: contentArg.clientWidth,\n      clientHeight: contentArg.clientHeight,\n      forPrint: props.forPrint\n    }, this.getAllDayMaxEventProps())));\n    let timeGridContent = contentArg => createElement(DayTimeCols, Object.assign({}, splitProps.timed, {\n      dayTableModel: dayTableModel,\n      dateProfile: dateProfile,\n      axis: hasAttachedAxis,\n      slotDuration: options.slotDuration,\n      slatMetas: slatMetas,\n      forPrint: props.forPrint,\n      tableColGroupNode: contentArg.tableColGroupNode,\n      tableMinWidth: contentArg.tableMinWidth,\n      clientWidth: contentArg.clientWidth,\n      clientHeight: contentArg.clientHeight,\n      onSlatCoords: this.handleSlatCoords,\n      expandRows: contentArg.expandRows,\n      onScrollTopRequest: this.handleScrollTopRequest\n    }));\n    return hasDetachedAxis ? this.renderHScrollLayout(headerContent, allDayContent, timeGridContent, dayTableModel.colCnt, dayMinWidth, slatMetas, this.state.slatCoords) : this.renderSimpleLayout(headerContent, allDayContent, timeGridContent);\n  }\n}\nfunction buildTimeColsModel(dateProfile, dateProfileGenerator) {\n  let daySeries = new DaySeriesModel(dateProfile.renderRange, dateProfileGenerator);\n  return new DayTableModel(daySeries, false);\n}\nvar css_248z = \".fc-v-event{background-color:var(--fc-event-bg-color);border:1px solid var(--fc-event-border-color);display:block}.fc-v-event .fc-event-main{color:var(--fc-event-text-color);height:100%}.fc-v-event .fc-event-main-frame{display:flex;flex-direction:column;height:100%}.fc-v-event .fc-event-time{flex-grow:0;flex-shrink:0;max-height:100%;overflow:hidden}.fc-v-event .fc-event-title-container{flex-grow:1;flex-shrink:1;min-height:0}.fc-v-event .fc-event-title{bottom:0;max-height:100%;overflow:hidden;top:0}.fc-v-event:not(.fc-event-start){border-top-left-radius:0;border-top-right-radius:0;border-top-width:0}.fc-v-event:not(.fc-event-end){border-bottom-left-radius:0;border-bottom-right-radius:0;border-bottom-width:0}.fc-v-event.fc-event-selected:before{left:-10px;right:-10px}.fc-v-event .fc-event-resizer-start{cursor:n-resize}.fc-v-event .fc-event-resizer-end{cursor:s-resize}.fc-v-event:not(.fc-event-selected) .fc-event-resizer{height:var(--fc-event-resizer-thickness);left:0;right:0}.fc-v-event:not(.fc-event-selected) .fc-event-resizer-start{top:calc(var(--fc-event-resizer-thickness)/-2)}.fc-v-event:not(.fc-event-selected) .fc-event-resizer-end{bottom:calc(var(--fc-event-resizer-thickness)/-2)}.fc-v-event.fc-event-selected .fc-event-resizer{left:50%;margin-left:calc(var(--fc-event-resizer-dot-total-width)/-2)}.fc-v-event.fc-event-selected .fc-event-resizer-start{top:calc(var(--fc-event-resizer-dot-total-width)/-2)}.fc-v-event.fc-event-selected .fc-event-resizer-end{bottom:calc(var(--fc-event-resizer-dot-total-width)/-2)}.fc .fc-timegrid .fc-daygrid-body{z-index:2}.fc .fc-timegrid-divider{padding:0 0 2px}.fc .fc-timegrid-body{min-height:100%;position:relative;z-index:1}.fc .fc-timegrid-axis-chunk{position:relative}.fc .fc-timegrid-axis-chunk>table,.fc .fc-timegrid-slots{position:relative;z-index:1}.fc .fc-timegrid-slot{border-bottom:0;height:1.5em}.fc .fc-timegrid-slot:empty:before{content:\\\"\\\\00a0\\\"}.fc .fc-timegrid-slot-minor{border-top-style:dotted}.fc .fc-timegrid-slot-label-cushion{display:inline-block;white-space:nowrap}.fc .fc-timegrid-slot-label{vertical-align:middle}.fc .fc-timegrid-axis-cushion,.fc .fc-timegrid-slot-label-cushion{padding:0 4px}.fc .fc-timegrid-axis-frame-liquid{height:100%}.fc .fc-timegrid-axis-frame{align-items:center;display:flex;justify-content:flex-end;overflow:hidden}.fc .fc-timegrid-axis-cushion{flex-shrink:0;max-width:60px}.fc-direction-ltr .fc-timegrid-slot-label-frame{text-align:right}.fc-direction-rtl .fc-timegrid-slot-label-frame{text-align:left}.fc-liquid-hack .fc-timegrid-axis-frame-liquid{bottom:0;height:auto;left:0;position:absolute;right:0;top:0}.fc .fc-timegrid-col.fc-day-today{background-color:var(--fc-today-bg-color)}.fc .fc-timegrid-col-frame{min-height:100%;position:relative}.fc-media-screen.fc-liquid-hack .fc-timegrid-col-frame{bottom:0;height:auto;left:0;position:absolute;right:0;top:0}.fc-media-screen .fc-timegrid-cols{bottom:0;left:0;position:absolute;right:0;top:0}.fc-media-screen .fc-timegrid-cols>table{height:100%}.fc-media-screen .fc-timegrid-col-bg,.fc-media-screen .fc-timegrid-col-events,.fc-media-screen .fc-timegrid-now-indicator-container{left:0;position:absolute;right:0;top:0}.fc .fc-timegrid-col-bg{z-index:2}.fc .fc-timegrid-col-bg .fc-non-business{z-index:1}.fc .fc-timegrid-col-bg .fc-bg-event{z-index:2}.fc .fc-timegrid-col-bg .fc-highlight{z-index:3}.fc .fc-timegrid-bg-harness{left:0;position:absolute;right:0}.fc .fc-timegrid-col-events{z-index:3}.fc .fc-timegrid-now-indicator-container{bottom:0;overflow:hidden}.fc-direction-ltr .fc-timegrid-col-events{margin:0 2.5% 0 2px}.fc-direction-rtl .fc-timegrid-col-events{margin:0 2px 0 2.5%}.fc-timegrid-event-harness{position:absolute}.fc-timegrid-event-harness>.fc-timegrid-event{bottom:0;left:0;position:absolute;right:0;top:0}.fc-timegrid-event-harness-inset .fc-timegrid-event,.fc-timegrid-event.fc-event-mirror,.fc-timegrid-more-link{box-shadow:0 0 0 1px var(--fc-page-bg-color)}.fc-timegrid-event,.fc-timegrid-more-link{border-radius:3px;font-size:var(--fc-small-font-size)}.fc-timegrid-event{margin-bottom:1px}.fc-timegrid-event .fc-event-main{padding:1px 1px 0}.fc-timegrid-event .fc-event-time{font-size:var(--fc-small-font-size);margin-bottom:1px;white-space:nowrap}.fc-timegrid-event-short .fc-event-main-frame{flex-direction:row;overflow:hidden}.fc-timegrid-event-short .fc-event-time:after{content:\\\"\\\\00a0-\\\\00a0\\\"}.fc-timegrid-event-short .fc-event-title{font-size:var(--fc-small-font-size)}.fc-timegrid-more-link{background:var(--fc-more-link-bg-color);color:var(--fc-more-link-text-color);cursor:pointer;margin-bottom:1px;position:absolute;z-index:9999}.fc-timegrid-more-link-inner{padding:3px 2px;top:0}.fc-direction-ltr .fc-timegrid-more-link{right:0}.fc-direction-rtl .fc-timegrid-more-link{left:0}.fc .fc-timegrid-now-indicator-line{border-color:var(--fc-now-indicator-color);border-style:solid;border-width:1px 0 0;left:0;position:absolute;right:0;z-index:4}.fc .fc-timegrid-now-indicator-arrow{border-color:var(--fc-now-indicator-color);border-style:solid;margin-top:-5px;position:absolute;z-index:4}.fc-direction-ltr .fc-timegrid-now-indicator-arrow{border-bottom-color:transparent;border-top-color:transparent;border-width:5px 0 5px 6px;left:0}.fc-direction-rtl .fc-timegrid-now-indicator-arrow{border-bottom-color:transparent;border-top-color:transparent;border-width:5px 6px 5px 0;right:0}\";\ninjectStyles(css_248z);\nexport { DayTimeCols, DayTimeColsSlicer, DayTimeColsView, TimeCols, TimeColsSlatsCoords, TimeColsView, buildDayRanges, buildSlatMetas, buildTimeColsModel };", "map": {"version": 3, "names": ["Splitter", "hasBgRendering", "createFormatter", "ViewContextType", "ContentContainer", "BaseComponent", "DateComponent", "diffDays", "buildNavLinkAttrs", "WeekNumberContainer", "getStickyHeaderDates", "ViewContainer", "SimpleScrollGrid", "getStickyFooterScrollbar", "NowTimer", "NowIndicatorContainer", "renderScrollShim", "rangeContainsMarker", "startOfDay", "asRoughMs", "createDuration", "RefMap", "PositionCache", "MoreLinkContainer", "SegHierarchy", "groupIntersectingEntries", "binarySearch", "getEntrySpanEnd", "buildEntryKey", "StandardEvent", "memoize", "sortEventSegs", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hasCustomDayCellContent", "getSegMeta", "buildIsoString", "computeEarliestSegStart", "buildEventRangeKey", "BgEvent", "renderFill", "addDurations", "multiplyDuration", "wholeDivideDurations", "<PERSON>licer", "intersectRanges", "formatIsoTimeString", "<PERSON><PERSON><PERSON><PERSON>", "DaySeriesModel", "DayTableModel", "injectStyles", "createElement", "createRef", "Fragment", "DayTable", "AllDaySplitter", "getKeyInfo", "allDay", "timed", "getKeysForDateSpan", "dateSpan", "getKeysForEventDef", "eventDef", "DEFAULT_SLAT_LABEL_FORMAT", "hour", "minute", "omitZeroMinute", "meridiem", "TimeColsAxisCell", "props", "classNames", "isLabeled", "Consumer", "context", "className", "join", "isoTimeStr", "dateEnv", "options", "viewApi", "labelFormat", "slotLabelFormat", "Array", "isArray", "renderProps", "level", "time", "date", "toDate", "view", "text", "format", "elTag", "elClasses", "elAttrs", "generatorName", "customGenerator", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaultGenerator", "renderInnerContent", "classNameGenerator", "slotLabelClassNames", "didMount", "slotLabelDidMount", "will<PERSON>n<PERSON>", "slotLabelWillUnmount", "InnerContent", "TimeBodyAxis", "render", "slatMetas", "map", "slatMeta", "key", "Object", "assign", "DEFAULT_WEEK_NUM_FORMAT", "week", "AUTO_ALL_DAY_MAX_EVENT_ROWS", "TimeColsView", "constructor", "arguments", "allDaySplitter", "headerElRef", "rootElRef", "scrollerElRef", "state", "slatCoords", "handleScrollTopRequest", "scrollTop", "scrollerEl", "current", "renderHeadAxis", "<PERSON><PERSON><PERSON>", "frameHeight", "dateProfile", "range", "renderRange", "dayCnt", "start", "end", "navLinkAttrs", "weekNumbers", "defaultFormat", "style", "height", "renderTableRowAxis", "rowHeight", "allDayText", "allDayContent", "renderAllDayInner", "allDayClassNames", "allDayDidMount", "allDayWillUnmount", "handleSlatCoords", "setState", "renderSimpleLayout", "headerRowContent", "timeContent", "sections", "stickyHeaderDates", "push", "type", "isSticky", "chunk", "elRef", "tableClassName", "row<PERSON><PERSON>nt", "content", "outerContent", "role", "theme", "getClass", "liquid", "expandRows", "Boolean", "viewSpec", "isHeightAuto", "forPrint", "collapsibleWidth", "cols", "width", "renderHScrollLayout", "colCnt", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ScrollGrid", "pluginHooks", "scrollGridImpl", "Error", "stickyFooterScrollbar", "syncRowHeights", "chunks", "arg", "rowSyncHeights", "contentArg", "colSpan", "isNowIndicator", "nowIndicator", "clientHeight", "tableColGroupNode", "unit", "nowDate", "nowIndicatorTop", "safeComputeTop", "elStyle", "top", "isAxis", "colGroups", "span", "min<PERSON><PERSON><PERSON>", "getAllDayMaxEventProps", "dayMaxEvents", "dayMaxEventRows", "undefined", "TimeColsSlatsCoords", "positions", "slotDuration", "currentRange", "startOfDayDate", "timeMs", "valueOf", "slotMinTime", "slotMaxTime", "computeTimeTop", "computeDateTop", "when", "duration", "len", "els", "length", "slatCoverage", "milliseconds", "slatIndex", "slat<PERSON><PERSON><PERSON>", "Math", "max", "min", "floor", "tops", "getHeight", "TimeColsSlatsBody", "slatElRefs", "i", "ref", "axis", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "slotLaneClassNames", "slotLaneDidMount", "slotLaneWillUnmount", "TimeColsSlats", "tableMin<PERSON>idth", "clientWidth", "minHeight", "componentDidMount", "updateSizing", "componentDidUpdate", "componentWillUnmount", "onCoords", "rootEl", "offsetHeight", "collectSlatEls", "currentMap", "elMap", "splitSegsByCol", "segs", "segsByCol", "col", "splitInteractionByCol", "ui", "byRow", "affectedInstances", "isEvent", "seg", "TimeColMoreLink", "bottom", "allDayDate", "moreCnt", "hiddenSegs", "allSegs", "extraDateSpan", "todayRange", "popoverContent", "renderPlainFgSegs", "renderMoreLinkInner", "forceTimed", "shortText", "buildPositioning", "segInputs", "strictOrder", "maxStackCnt", "hierarchy", "hiddenEntries", "addSegs", "hiddenGroups", "web", "buildWeb", "stretchWeb", "segRects", "webToRects", "entriesByLevel", "buildNode", "cacheable", "lateral", "<PERSON><PERSON><PERSON><PERSON>", "findNextLevelSegs", "nextLevelRes", "buildNodes", "entry", "nextLevelNodes", "thickness", "lateralStart", "lateralEnd", "pairs", "sort", "cmpDescPressures", "extractNode", "a", "b", "subjectLevel", "subjectLateral", "levelCoords", "subjectEntry", "afterSubject", "levelCnt", "entries", "searchIndex", "topLevelNodes", "totalThickness", "stretchNode", "node", "startCoord", "prevThickness", "allThickness", "thicknessFraction", "endCoord", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "childNode", "res", "newThickness", "rects", "processNode", "levelCoord", "stack<PERSON>epth", "rect", "stackForward", "processNodes", "nodes", "keyFunc", "workFunc", "cache", "args", "computeSegVCoords", "colDate", "eventMinHeight", "vcoords", "spanStart", "spanEnd", "round", "computeFgSegPlacements", "segVCoords", "eventOrderStrict", "eventMaxStack", "dumbSegs", "index", "segPlacements", "segRect", "dumbSeg", "DEFAULT_TIME_FORMAT", "TimeColEvent", "isShort", "defaultTimeFormat", "TimeCol", "isSelectMirror", "selectMirror", "mirrorSegs", "eventDrag", "eventResize", "dateSelectionSegs", "interactionAffectedInstances", "sortedFgSegs", "fgEventSegs", "eventOrder", "extraClassNames", "extraDataAttrs", "extraRenderProps", "renderFillSegs", "businessHourSegs", "bgEventSegs", "renderFgSegs", "renderNowIndicator", "nowIndicatorSegs", "segIsInvisible", "isDragging", "isResizing", "isDateSelecting", "<PERSON><PERSON><PERSON>", "renderPositionedFgSegs", "eventShortHeight", "eventSelection", "is<PERSON><PERSON><PERSON><PERSON>", "renderHiddenGroups", "segPlacement", "instanceId", "eventRange", "instance", "isVisible", "vStyle", "computeSegVStyle", "hStyle", "computeSegHStyle", "left", "right", "isInset", "visibility", "isSelected", "hiddenGroup", "positionCss", "compileSegsFromEntries", "fillType", "children", "segHCoords", "isRtl", "shouldOverlap", "slotEventOverlap", "nearCoord", "farCoord", "zIndex", "hiddenInstances", "segEntries", "segEntry", "TimeCols<PERSON><PERSON>nt", "splitFgEventSegs", "splitBgEventSegs", "splitBusinessHourSegs", "splitNowIndicatorSegs", "splitDateSelectionSegs", "splitEventDrag", "splitEventResize", "cellElRefs", "cells", "fgEventSegsByRow", "bgEventSegsByRow", "businessHourSegsByRow", "nowIndicatorSegsByRow", "dateSelectionSegsByRow", "eventDragByRow", "eventResizeByRow", "cell", "updateCoords", "onColCoords", "collectCellEls", "TimeCols", "processSlotOptions", "handleRootEl", "el", "registerInteractiveComponent", "isHitComboAllowed", "unregisterInteractiveComponent", "handleScrollRequest", "request", "onScrollTopRequest", "ceil", "handleColCoords", "colCoords", "onSlatCoords", "scrollResponder", "createScrollResponder", "prevProps", "update", "detach", "queryHit", "positionLeft", "positionTop", "snapDuration", "snapsPerSlot", "colIndex", "leftToIndex", "topToIndex", "slatTop", "slatHeight", "partial", "localSnapIndex", "snapIndex", "dayDate", "add", "dayEl", "lefts", "rights", "layer", "snapDurationOverride", "DayTimeColsSlicer", "sliceRange", "<PERSON><PERSON><PERSON><PERSON>", "seg<PERSON><PERSON><PERSON>", "isStart", "isEnd", "DayTimeCols", "buildDayRanges", "slicer", "timeColsRef", "dayTableModel", "nextDayThreshold", "sliceProps", "sliceNowDate", "ranges", "headerDates", "STOCK_SUB_DURATIONS", "hours", "minutes", "seconds", "buildSlatMetas", "explicitLabelInterval", "dayStart", "Date", "slatTime", "slatIterator", "labelInterval", "computeLabelInterval", "metas", "toISOString", "slotsPerLabel", "DayTimeColsView", "buildTimeColsModel", "dateProfileGenerator", "splitProps", "slotLabelInterval", "hasAttachedAxis", "hasDetachedAxis", "headerContent", "dayHeaders", "dates", "datesRepDistinctDays", "renderIntro", "allDaySlot", "colGroupNode", "renderRowIntro", "showWeekNumbers", "headerAlignElRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "daySeries", "css_248z"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@fullcalendar/timegrid/internal.js"], "sourcesContent": ["import { Splitter, hasBgRendering, createFormatter, ViewContextType, ContentContainer, BaseComponent, DateComponent, diffDays, buildNavLinkAttrs, WeekNumberContainer, getStickyHeaderDates, ViewContainer, SimpleScrollGrid, getStickyFooterScrollbar, NowTimer, NowIndicatorContainer, renderScrollShim, rangeContainsMarker, startOfDay, asRoughMs, createDuration, RefMap, PositionCache, MoreLinkContainer, SegHierarchy, groupIntersectingEntries, binarySearch, getEntrySpanEnd, buildEntryKey, StandardEvent, memoize, sortEventSegs, DayCellContainer, hasCustomDayCellContent, getSegMeta, buildIsoString, computeEarliestSegStart, buildEventRangeKey, BgEvent, renderFill, addDurations, multiplyDuration, wholeDivideDurations, Slicer, intersectRanges, formatIsoTimeString, DayHeader, DaySeriesModel, DayTableModel, injectStyles } from '@fullcalendar/core/internal.js';\nimport { createElement, createRef, Fragment } from '@fullcalendar/core/preact.js';\nimport { DayTable } from '@fullcalendar/daygrid/internal.js';\n\nclass AllDaySplitter extends Splitter {\n    getKeyInfo() {\n        return {\n            allDay: {},\n            timed: {},\n        };\n    }\n    getKeysForDateSpan(dateSpan) {\n        if (dateSpan.allDay) {\n            return ['allDay'];\n        }\n        return ['timed'];\n    }\n    getKeysForEventDef(eventDef) {\n        if (!eventDef.allDay) {\n            return ['timed'];\n        }\n        if (hasBgRendering(eventDef)) {\n            return ['timed', 'allDay'];\n        }\n        return ['allDay'];\n    }\n}\n\nconst DEFAULT_SLAT_LABEL_FORMAT = createFormatter({\n    hour: 'numeric',\n    minute: '2-digit',\n    omitZeroMinute: true,\n    meridiem: 'short',\n});\nfunction TimeColsAxisCell(props) {\n    let classNames = [\n        'fc-timegrid-slot',\n        'fc-timegrid-slot-label',\n        props.isLabeled ? 'fc-scrollgrid-shrink' : 'fc-timegrid-slot-minor',\n    ];\n    return (createElement(ViewContextType.Consumer, null, (context) => {\n        if (!props.isLabeled) {\n            return (createElement(\"td\", { className: classNames.join(' '), \"data-time\": props.isoTimeStr }));\n        }\n        let { dateEnv, options, viewApi } = context;\n        let labelFormat = // TODO: fully pre-parse\n         options.slotLabelFormat == null ? DEFAULT_SLAT_LABEL_FORMAT :\n            Array.isArray(options.slotLabelFormat) ? createFormatter(options.slotLabelFormat[0]) :\n                createFormatter(options.slotLabelFormat);\n        let renderProps = {\n            level: 0,\n            time: props.time,\n            date: dateEnv.toDate(props.date),\n            view: viewApi,\n            text: dateEnv.format(props.date, labelFormat),\n        };\n        return (createElement(ContentContainer, { elTag: \"td\", elClasses: classNames, elAttrs: {\n                'data-time': props.isoTimeStr,\n            }, renderProps: renderProps, generatorName: \"slotLabelContent\", customGenerator: options.slotLabelContent, defaultGenerator: renderInnerContent, classNameGenerator: options.slotLabelClassNames, didMount: options.slotLabelDidMount, willUnmount: options.slotLabelWillUnmount }, (InnerContent) => (createElement(\"div\", { className: \"fc-timegrid-slot-label-frame fc-scrollgrid-shrink-frame\" },\n            createElement(InnerContent, { elTag: \"div\", elClasses: [\n                    'fc-timegrid-slot-label-cushion',\n                    'fc-scrollgrid-shrink-cushion',\n                ] })))));\n    }));\n}\nfunction renderInnerContent(props) {\n    return props.text;\n}\n\nclass TimeBodyAxis extends BaseComponent {\n    render() {\n        return this.props.slatMetas.map((slatMeta) => (createElement(\"tr\", { key: slatMeta.key },\n            createElement(TimeColsAxisCell, Object.assign({}, slatMeta)))));\n    }\n}\n\nconst DEFAULT_WEEK_NUM_FORMAT = createFormatter({ week: 'short' });\nconst AUTO_ALL_DAY_MAX_EVENT_ROWS = 5;\nclass TimeColsView extends DateComponent {\n    constructor() {\n        super(...arguments);\n        this.allDaySplitter = new AllDaySplitter(); // for use by subclasses\n        this.headerElRef = createRef();\n        this.rootElRef = createRef();\n        this.scrollerElRef = createRef();\n        this.state = {\n            slatCoords: null,\n        };\n        this.handleScrollTopRequest = (scrollTop) => {\n            let scrollerEl = this.scrollerElRef.current;\n            if (scrollerEl) { // TODO: not sure how this could ever be null. weirdness with the reducer\n                scrollerEl.scrollTop = scrollTop;\n            }\n        };\n        /* Header Render Methods\n        ------------------------------------------------------------------------------------------------------------------*/\n        this.renderHeadAxis = (rowKey, frameHeight = '') => {\n            let { options } = this.context;\n            let { dateProfile } = this.props;\n            let range = dateProfile.renderRange;\n            let dayCnt = diffDays(range.start, range.end);\n            // only do in day views (to avoid doing in week views that dont need it)\n            let navLinkAttrs = (dayCnt === 1)\n                ? buildNavLinkAttrs(this.context, range.start, 'week')\n                : {};\n            if (options.weekNumbers && rowKey === 'day') {\n                return (createElement(WeekNumberContainer, { elTag: \"th\", elClasses: [\n                        'fc-timegrid-axis',\n                        'fc-scrollgrid-shrink',\n                    ], elAttrs: {\n                        'aria-hidden': true,\n                    }, date: range.start, defaultFormat: DEFAULT_WEEK_NUM_FORMAT }, (InnerContent) => (createElement(\"div\", { className: [\n                        'fc-timegrid-axis-frame',\n                        'fc-scrollgrid-shrink-frame',\n                        'fc-timegrid-axis-frame-liquid',\n                    ].join(' '), style: { height: frameHeight } },\n                    createElement(InnerContent, { elTag: \"a\", elClasses: [\n                            'fc-timegrid-axis-cushion',\n                            'fc-scrollgrid-shrink-cushion',\n                            'fc-scrollgrid-sync-inner',\n                        ], elAttrs: navLinkAttrs })))));\n            }\n            return (createElement(\"th\", { \"aria-hidden\": true, className: \"fc-timegrid-axis\" },\n                createElement(\"div\", { className: \"fc-timegrid-axis-frame\", style: { height: frameHeight } })));\n        };\n        /* Table Component Render Methods\n        ------------------------------------------------------------------------------------------------------------------*/\n        // only a one-way height sync. we don't send the axis inner-content height to the DayGrid,\n        // but DayGrid still needs to have classNames on inner elements in order to measure.\n        this.renderTableRowAxis = (rowHeight) => {\n            let { options, viewApi } = this.context;\n            let renderProps = {\n                text: options.allDayText,\n                view: viewApi,\n            };\n            return (\n            // TODO: make reusable hook. used in list view too\n            createElement(ContentContainer, { elTag: \"td\", elClasses: [\n                    'fc-timegrid-axis',\n                    'fc-scrollgrid-shrink',\n                ], elAttrs: {\n                    'aria-hidden': true,\n                }, renderProps: renderProps, generatorName: \"allDayContent\", customGenerator: options.allDayContent, defaultGenerator: renderAllDayInner, classNameGenerator: options.allDayClassNames, didMount: options.allDayDidMount, willUnmount: options.allDayWillUnmount }, (InnerContent) => (createElement(\"div\", { className: [\n                    'fc-timegrid-axis-frame',\n                    'fc-scrollgrid-shrink-frame',\n                    rowHeight == null ? ' fc-timegrid-axis-frame-liquid' : '',\n                ].join(' '), style: { height: rowHeight } },\n                createElement(InnerContent, { elTag: \"span\", elClasses: [\n                        'fc-timegrid-axis-cushion',\n                        'fc-scrollgrid-shrink-cushion',\n                        'fc-scrollgrid-sync-inner',\n                    ] })))));\n        };\n        this.handleSlatCoords = (slatCoords) => {\n            this.setState({ slatCoords });\n        };\n    }\n    // rendering\n    // ----------------------------------------------------------------------------------------------------\n    renderSimpleLayout(headerRowContent, allDayContent, timeContent) {\n        let { context, props } = this;\n        let sections = [];\n        let stickyHeaderDates = getStickyHeaderDates(context.options);\n        if (headerRowContent) {\n            sections.push({\n                type: 'header',\n                key: 'header',\n                isSticky: stickyHeaderDates,\n                chunk: {\n                    elRef: this.headerElRef,\n                    tableClassName: 'fc-col-header',\n                    rowContent: headerRowContent,\n                },\n            });\n        }\n        if (allDayContent) {\n            sections.push({\n                type: 'body',\n                key: 'all-day',\n                chunk: { content: allDayContent },\n            });\n            sections.push({\n                type: 'body',\n                key: 'all-day-divider',\n                outerContent: ( // TODO: rename to cellContent so don't need to define <tr>?\n                createElement(\"tr\", { role: \"presentation\", className: \"fc-scrollgrid-section\" },\n                    createElement(\"td\", { className: 'fc-timegrid-divider ' + context.theme.getClass('tableCellShaded') }))),\n            });\n        }\n        sections.push({\n            type: 'body',\n            key: 'body',\n            liquid: true,\n            expandRows: Boolean(context.options.expandRows),\n            chunk: {\n                scrollerElRef: this.scrollerElRef,\n                content: timeContent,\n            },\n        });\n        return (createElement(ViewContainer, { elRef: this.rootElRef, elClasses: ['fc-timegrid'], viewSpec: context.viewSpec },\n            createElement(SimpleScrollGrid, { liquid: !props.isHeightAuto && !props.forPrint, collapsibleWidth: props.forPrint, cols: [{ width: 'shrink' }], sections: sections })));\n    }\n    renderHScrollLayout(headerRowContent, allDayContent, timeContent, colCnt, dayMinWidth, slatMetas, slatCoords) {\n        let ScrollGrid = this.context.pluginHooks.scrollGridImpl;\n        if (!ScrollGrid) {\n            throw new Error('No ScrollGrid implementation');\n        }\n        let { context, props } = this;\n        let stickyHeaderDates = !props.forPrint && getStickyHeaderDates(context.options);\n        let stickyFooterScrollbar = !props.forPrint && getStickyFooterScrollbar(context.options);\n        let sections = [];\n        if (headerRowContent) {\n            sections.push({\n                type: 'header',\n                key: 'header',\n                isSticky: stickyHeaderDates,\n                syncRowHeights: true,\n                chunks: [\n                    {\n                        key: 'axis',\n                        rowContent: (arg) => (createElement(\"tr\", { role: \"presentation\" }, this.renderHeadAxis('day', arg.rowSyncHeights[0]))),\n                    },\n                    {\n                        key: 'cols',\n                        elRef: this.headerElRef,\n                        tableClassName: 'fc-col-header',\n                        rowContent: headerRowContent,\n                    },\n                ],\n            });\n        }\n        if (allDayContent) {\n            sections.push({\n                type: 'body',\n                key: 'all-day',\n                syncRowHeights: true,\n                chunks: [\n                    {\n                        key: 'axis',\n                        rowContent: (contentArg) => (createElement(\"tr\", { role: \"presentation\" }, this.renderTableRowAxis(contentArg.rowSyncHeights[0]))),\n                    },\n                    {\n                        key: 'cols',\n                        content: allDayContent,\n                    },\n                ],\n            });\n            sections.push({\n                key: 'all-day-divider',\n                type: 'body',\n                outerContent: ( // TODO: rename to cellContent so don't need to define <tr>?\n                createElement(\"tr\", { role: \"presentation\", className: \"fc-scrollgrid-section\" },\n                    createElement(\"td\", { colSpan: 2, className: 'fc-timegrid-divider ' + context.theme.getClass('tableCellShaded') }))),\n            });\n        }\n        let isNowIndicator = context.options.nowIndicator;\n        sections.push({\n            type: 'body',\n            key: 'body',\n            liquid: true,\n            expandRows: Boolean(context.options.expandRows),\n            chunks: [\n                {\n                    key: 'axis',\n                    content: (arg) => (\n                    // TODO: make this now-indicator arrow more DRY with TimeColsContent\n                    createElement(\"div\", { className: \"fc-timegrid-axis-chunk\" },\n                        createElement(\"table\", { \"aria-hidden\": true, style: { height: arg.expandRows ? arg.clientHeight : '' } },\n                            arg.tableColGroupNode,\n                            createElement(\"tbody\", null,\n                                createElement(TimeBodyAxis, { slatMetas: slatMetas }))),\n                        createElement(\"div\", { className: \"fc-timegrid-now-indicator-container\" },\n                            createElement(NowTimer, { unit: isNowIndicator ? 'minute' : 'day' /* hacky */ }, (nowDate) => {\n                                let nowIndicatorTop = isNowIndicator &&\n                                    slatCoords &&\n                                    slatCoords.safeComputeTop(nowDate); // might return void\n                                if (typeof nowIndicatorTop === 'number') {\n                                    return (createElement(NowIndicatorContainer, { elClasses: ['fc-timegrid-now-indicator-arrow'], elStyle: { top: nowIndicatorTop }, isAxis: true, date: nowDate }));\n                                }\n                                return null;\n                            })))),\n                },\n                {\n                    key: 'cols',\n                    scrollerElRef: this.scrollerElRef,\n                    content: timeContent,\n                },\n            ],\n        });\n        if (stickyFooterScrollbar) {\n            sections.push({\n                key: 'footer',\n                type: 'footer',\n                isSticky: true,\n                chunks: [\n                    {\n                        key: 'axis',\n                        content: renderScrollShim,\n                    },\n                    {\n                        key: 'cols',\n                        content: renderScrollShim,\n                    },\n                ],\n            });\n        }\n        return (createElement(ViewContainer, { elRef: this.rootElRef, elClasses: ['fc-timegrid'], viewSpec: context.viewSpec },\n            createElement(ScrollGrid, { liquid: !props.isHeightAuto && !props.forPrint, forPrint: props.forPrint, collapsibleWidth: false, colGroups: [\n                    { width: 'shrink', cols: [{ width: 'shrink' }] },\n                    { cols: [{ span: colCnt, minWidth: dayMinWidth }] },\n                ], sections: sections })));\n    }\n    /* Dimensions\n    ------------------------------------------------------------------------------------------------------------------*/\n    getAllDayMaxEventProps() {\n        let { dayMaxEvents, dayMaxEventRows } = this.context.options;\n        if (dayMaxEvents === true || dayMaxEventRows === true) { // is auto?\n            dayMaxEvents = undefined;\n            dayMaxEventRows = AUTO_ALL_DAY_MAX_EVENT_ROWS; // make sure \"auto\" goes to a real number\n        }\n        return { dayMaxEvents, dayMaxEventRows };\n    }\n}\nfunction renderAllDayInner(renderProps) {\n    return renderProps.text;\n}\n\nclass TimeColsSlatsCoords {\n    constructor(positions, dateProfile, slotDuration) {\n        this.positions = positions;\n        this.dateProfile = dateProfile;\n        this.slotDuration = slotDuration;\n    }\n    safeComputeTop(date) {\n        let { dateProfile } = this;\n        if (rangeContainsMarker(dateProfile.currentRange, date)) {\n            let startOfDayDate = startOfDay(date);\n            let timeMs = date.valueOf() - startOfDayDate.valueOf();\n            if (timeMs >= asRoughMs(dateProfile.slotMinTime) &&\n                timeMs < asRoughMs(dateProfile.slotMaxTime)) {\n                return this.computeTimeTop(createDuration(timeMs));\n            }\n        }\n        return null;\n    }\n    // Computes the top coordinate, relative to the bounds of the grid, of the given date.\n    // A `startOfDayDate` must be given for avoiding ambiguity over how to treat midnight.\n    computeDateTop(when, startOfDayDate) {\n        if (!startOfDayDate) {\n            startOfDayDate = startOfDay(when);\n        }\n        return this.computeTimeTop(createDuration(when.valueOf() - startOfDayDate.valueOf()));\n    }\n    // Computes the top coordinate, relative to the bounds of the grid, of the given time (a Duration).\n    // This is a makeshify way to compute the time-top. Assumes all slatMetas dates are uniform.\n    // Eventually allow computation with arbirary slat dates.\n    computeTimeTop(duration) {\n        let { positions, dateProfile } = this;\n        let len = positions.els.length;\n        // floating-point value of # of slots covered\n        let slatCoverage = (duration.milliseconds - asRoughMs(dateProfile.slotMinTime)) / asRoughMs(this.slotDuration);\n        let slatIndex;\n        let slatRemainder;\n        // compute a floating-point number for how many slats should be progressed through.\n        // from 0 to number of slats (inclusive)\n        // constrained because slotMinTime/slotMaxTime might be customized.\n        slatCoverage = Math.max(0, slatCoverage);\n        slatCoverage = Math.min(len, slatCoverage);\n        // an integer index of the furthest whole slat\n        // from 0 to number slats (*exclusive*, so len-1)\n        slatIndex = Math.floor(slatCoverage);\n        slatIndex = Math.min(slatIndex, len - 1);\n        // how much further through the slatIndex slat (from 0.0-1.0) must be covered in addition.\n        // could be 1.0 if slatCoverage is covering *all* the slots\n        slatRemainder = slatCoverage - slatIndex;\n        return positions.tops[slatIndex] +\n            positions.getHeight(slatIndex) * slatRemainder;\n    }\n}\n\nclass TimeColsSlatsBody extends BaseComponent {\n    render() {\n        let { props, context } = this;\n        let { options } = context;\n        let { slatElRefs } = props;\n        return (createElement(\"tbody\", null, props.slatMetas.map((slatMeta, i) => {\n            let renderProps = {\n                time: slatMeta.time,\n                date: context.dateEnv.toDate(slatMeta.date),\n                view: context.viewApi,\n            };\n            return (createElement(\"tr\", { key: slatMeta.key, ref: slatElRefs.createRef(slatMeta.key) },\n                props.axis && (createElement(TimeColsAxisCell, Object.assign({}, slatMeta))),\n                createElement(ContentContainer, { elTag: \"td\", elClasses: [\n                        'fc-timegrid-slot',\n                        'fc-timegrid-slot-lane',\n                        !slatMeta.isLabeled && 'fc-timegrid-slot-minor',\n                    ], elAttrs: {\n                        'data-time': slatMeta.isoTimeStr,\n                    }, renderProps: renderProps, generatorName: \"slotLaneContent\", customGenerator: options.slotLaneContent, classNameGenerator: options.slotLaneClassNames, didMount: options.slotLaneDidMount, willUnmount: options.slotLaneWillUnmount })));\n        })));\n    }\n}\n\n/*\nfor the horizontal \"slats\" that run width-wise. Has a time axis on a side. Depends on RTL.\n*/\nclass TimeColsSlats extends BaseComponent {\n    constructor() {\n        super(...arguments);\n        this.rootElRef = createRef();\n        this.slatElRefs = new RefMap();\n    }\n    render() {\n        let { props, context } = this;\n        return (createElement(\"div\", { ref: this.rootElRef, className: \"fc-timegrid-slots\" },\n            createElement(\"table\", { \"aria-hidden\": true, className: context.theme.getClass('table'), style: {\n                    minWidth: props.tableMinWidth,\n                    width: props.clientWidth,\n                    height: props.minHeight,\n                } },\n                props.tableColGroupNode /* relies on there only being a single <col> for the axis */,\n                createElement(TimeColsSlatsBody, { slatElRefs: this.slatElRefs, axis: props.axis, slatMetas: props.slatMetas }))));\n    }\n    componentDidMount() {\n        this.updateSizing();\n    }\n    componentDidUpdate() {\n        this.updateSizing();\n    }\n    componentWillUnmount() {\n        if (this.props.onCoords) {\n            this.props.onCoords(null);\n        }\n    }\n    updateSizing() {\n        let { context, props } = this;\n        if (props.onCoords &&\n            props.clientWidth !== null // means sizing has stabilized\n        ) {\n            let rootEl = this.rootElRef.current;\n            if (rootEl.offsetHeight) { // not hidden by css\n                props.onCoords(new TimeColsSlatsCoords(new PositionCache(this.rootElRef.current, collectSlatEls(this.slatElRefs.currentMap, props.slatMetas), false, true), this.props.dateProfile, context.options.slotDuration));\n            }\n        }\n    }\n}\nfunction collectSlatEls(elMap, slatMetas) {\n    return slatMetas.map((slatMeta) => elMap[slatMeta.key]);\n}\n\nfunction splitSegsByCol(segs, colCnt) {\n    let segsByCol = [];\n    let i;\n    for (i = 0; i < colCnt; i += 1) {\n        segsByCol.push([]);\n    }\n    if (segs) {\n        for (i = 0; i < segs.length; i += 1) {\n            segsByCol[segs[i].col].push(segs[i]);\n        }\n    }\n    return segsByCol;\n}\nfunction splitInteractionByCol(ui, colCnt) {\n    let byRow = [];\n    if (!ui) {\n        for (let i = 0; i < colCnt; i += 1) {\n            byRow[i] = null;\n        }\n    }\n    else {\n        for (let i = 0; i < colCnt; i += 1) {\n            byRow[i] = {\n                affectedInstances: ui.affectedInstances,\n                isEvent: ui.isEvent,\n                segs: [],\n            };\n        }\n        for (let seg of ui.segs) {\n            byRow[seg.col].segs.push(seg);\n        }\n    }\n    return byRow;\n}\n\nclass TimeColMoreLink extends BaseComponent {\n    render() {\n        let { props } = this;\n        return (createElement(MoreLinkContainer, { elClasses: ['fc-timegrid-more-link'], elStyle: {\n                top: props.top,\n                bottom: props.bottom,\n            }, allDayDate: null, moreCnt: props.hiddenSegs.length, allSegs: props.hiddenSegs, hiddenSegs: props.hiddenSegs, extraDateSpan: props.extraDateSpan, dateProfile: props.dateProfile, todayRange: props.todayRange, popoverContent: () => renderPlainFgSegs(props.hiddenSegs, props), defaultGenerator: renderMoreLinkInner, forceTimed: true }, (InnerContent) => (createElement(InnerContent, { elTag: \"div\", elClasses: ['fc-timegrid-more-link-inner', 'fc-sticky'] }))));\n    }\n}\nfunction renderMoreLinkInner(props) {\n    return props.shortText;\n}\n\n// segInputs assumed sorted\nfunction buildPositioning(segInputs, strictOrder, maxStackCnt) {\n    let hierarchy = new SegHierarchy();\n    if (strictOrder != null) {\n        hierarchy.strictOrder = strictOrder;\n    }\n    if (maxStackCnt != null) {\n        hierarchy.maxStackCnt = maxStackCnt;\n    }\n    let hiddenEntries = hierarchy.addSegs(segInputs);\n    let hiddenGroups = groupIntersectingEntries(hiddenEntries);\n    let web = buildWeb(hierarchy);\n    web = stretchWeb(web, 1); // all levelCoords/thickness will have 0.0-1.0\n    let segRects = webToRects(web);\n    return { segRects, hiddenGroups };\n}\nfunction buildWeb(hierarchy) {\n    const { entriesByLevel } = hierarchy;\n    const buildNode = cacheable((level, lateral) => level + ':' + lateral, (level, lateral) => {\n        let siblingRange = findNextLevelSegs(hierarchy, level, lateral);\n        let nextLevelRes = buildNodes(siblingRange, buildNode);\n        let entry = entriesByLevel[level][lateral];\n        return [\n            Object.assign(Object.assign({}, entry), { nextLevelNodes: nextLevelRes[0] }),\n            entry.thickness + nextLevelRes[1], // the pressure builds\n        ];\n    });\n    return buildNodes(entriesByLevel.length\n        ? { level: 0, lateralStart: 0, lateralEnd: entriesByLevel[0].length }\n        : null, buildNode)[0];\n}\nfunction buildNodes(siblingRange, buildNode) {\n    if (!siblingRange) {\n        return [[], 0];\n    }\n    let { level, lateralStart, lateralEnd } = siblingRange;\n    let lateral = lateralStart;\n    let pairs = [];\n    while (lateral < lateralEnd) {\n        pairs.push(buildNode(level, lateral));\n        lateral += 1;\n    }\n    pairs.sort(cmpDescPressures);\n    return [\n        pairs.map(extractNode),\n        pairs[0][1], // first item's pressure\n    ];\n}\nfunction cmpDescPressures(a, b) {\n    return b[1] - a[1];\n}\nfunction extractNode(a) {\n    return a[0];\n}\nfunction findNextLevelSegs(hierarchy, subjectLevel, subjectLateral) {\n    let { levelCoords, entriesByLevel } = hierarchy;\n    let subjectEntry = entriesByLevel[subjectLevel][subjectLateral];\n    let afterSubject = levelCoords[subjectLevel] + subjectEntry.thickness;\n    let levelCnt = levelCoords.length;\n    let level = subjectLevel;\n    // skip past levels that are too high up\n    for (; level < levelCnt && levelCoords[level] < afterSubject; level += 1)\n        ; // do nothing\n    for (; level < levelCnt; level += 1) {\n        let entries = entriesByLevel[level];\n        let entry;\n        let searchIndex = binarySearch(entries, subjectEntry.span.start, getEntrySpanEnd);\n        let lateralStart = searchIndex[0] + searchIndex[1]; // if exact match (which doesn't collide), go to next one\n        let lateralEnd = lateralStart;\n        while ( // loop through entries that horizontally intersect\n        (entry = entries[lateralEnd]) && // but not past the whole seg list\n            entry.span.start < subjectEntry.span.end) {\n            lateralEnd += 1;\n        }\n        if (lateralStart < lateralEnd) {\n            return { level, lateralStart, lateralEnd };\n        }\n    }\n    return null;\n}\nfunction stretchWeb(topLevelNodes, totalThickness) {\n    const stretchNode = cacheable((node, startCoord, prevThickness) => buildEntryKey(node), (node, startCoord, prevThickness) => {\n        let { nextLevelNodes, thickness } = node;\n        let allThickness = thickness + prevThickness;\n        let thicknessFraction = thickness / allThickness;\n        let endCoord;\n        let newChildren = [];\n        if (!nextLevelNodes.length) {\n            endCoord = totalThickness;\n        }\n        else {\n            for (let childNode of nextLevelNodes) {\n                if (endCoord === undefined) {\n                    let res = stretchNode(childNode, startCoord, allThickness);\n                    endCoord = res[0];\n                    newChildren.push(res[1]);\n                }\n                else {\n                    let res = stretchNode(childNode, endCoord, 0);\n                    newChildren.push(res[1]);\n                }\n            }\n        }\n        let newThickness = (endCoord - startCoord) * thicknessFraction;\n        return [endCoord - newThickness, Object.assign(Object.assign({}, node), { thickness: newThickness, nextLevelNodes: newChildren })];\n    });\n    return topLevelNodes.map((node) => stretchNode(node, 0, 0)[1]);\n}\n// not sorted in any particular order\nfunction webToRects(topLevelNodes) {\n    let rects = [];\n    const processNode = cacheable((node, levelCoord, stackDepth) => buildEntryKey(node), (node, levelCoord, stackDepth) => {\n        let rect = Object.assign(Object.assign({}, node), { levelCoord,\n            stackDepth, stackForward: 0 });\n        rects.push(rect);\n        return (rect.stackForward = processNodes(node.nextLevelNodes, levelCoord + node.thickness, stackDepth + 1) + 1);\n    });\n    function processNodes(nodes, levelCoord, stackDepth) {\n        let stackForward = 0;\n        for (let node of nodes) {\n            stackForward = Math.max(processNode(node, levelCoord, stackDepth), stackForward);\n        }\n        return stackForward;\n    }\n    processNodes(topLevelNodes, 0, 0);\n    return rects; // TODO: sort rects by levelCoord to be consistent with toRects?\n}\n// TODO: move to general util\nfunction cacheable(keyFunc, workFunc) {\n    const cache = {};\n    return (...args) => {\n        let key = keyFunc(...args);\n        return (key in cache)\n            ? cache[key]\n            : (cache[key] = workFunc(...args));\n    };\n}\n\nfunction computeSegVCoords(segs, colDate, slatCoords = null, eventMinHeight = 0) {\n    let vcoords = [];\n    if (slatCoords) {\n        for (let i = 0; i < segs.length; i += 1) {\n            let seg = segs[i];\n            let spanStart = slatCoords.computeDateTop(seg.start, colDate);\n            let spanEnd = Math.max(spanStart + (eventMinHeight || 0), // :(\n            slatCoords.computeDateTop(seg.end, colDate));\n            vcoords.push({\n                start: Math.round(spanStart),\n                end: Math.round(spanEnd), //\n            });\n        }\n    }\n    return vcoords;\n}\nfunction computeFgSegPlacements(segs, segVCoords, // might not have for every seg\neventOrderStrict, eventMaxStack) {\n    let segInputs = [];\n    let dumbSegs = []; // segs without coords\n    for (let i = 0; i < segs.length; i += 1) {\n        let vcoords = segVCoords[i];\n        if (vcoords) {\n            segInputs.push({\n                index: i,\n                thickness: 1,\n                span: vcoords,\n            });\n        }\n        else {\n            dumbSegs.push(segs[i]);\n        }\n    }\n    let { segRects, hiddenGroups } = buildPositioning(segInputs, eventOrderStrict, eventMaxStack);\n    let segPlacements = [];\n    for (let segRect of segRects) {\n        segPlacements.push({\n            seg: segs[segRect.index],\n            rect: segRect,\n        });\n    }\n    for (let dumbSeg of dumbSegs) {\n        segPlacements.push({ seg: dumbSeg, rect: null });\n    }\n    return { segPlacements, hiddenGroups };\n}\n\nconst DEFAULT_TIME_FORMAT = createFormatter({\n    hour: 'numeric',\n    minute: '2-digit',\n    meridiem: false,\n});\nclass TimeColEvent extends BaseComponent {\n    render() {\n        return (createElement(StandardEvent, Object.assign({}, this.props, { elClasses: [\n                'fc-timegrid-event',\n                'fc-v-event',\n                this.props.isShort && 'fc-timegrid-event-short',\n            ], defaultTimeFormat: DEFAULT_TIME_FORMAT })));\n    }\n}\n\nclass TimeCol extends BaseComponent {\n    constructor() {\n        super(...arguments);\n        this.sortEventSegs = memoize(sortEventSegs);\n    }\n    // TODO: memoize event-placement?\n    render() {\n        let { props, context } = this;\n        let { options } = context;\n        let isSelectMirror = options.selectMirror;\n        let mirrorSegs = // yuck\n         (props.eventDrag && props.eventDrag.segs) ||\n            (props.eventResize && props.eventResize.segs) ||\n            (isSelectMirror && props.dateSelectionSegs) ||\n            [];\n        let interactionAffectedInstances = // TODO: messy way to compute this\n         (props.eventDrag && props.eventDrag.affectedInstances) ||\n            (props.eventResize && props.eventResize.affectedInstances) ||\n            {};\n        let sortedFgSegs = this.sortEventSegs(props.fgEventSegs, options.eventOrder);\n        return (createElement(DayCellContainer, { elTag: \"td\", elRef: props.elRef, elClasses: [\n                'fc-timegrid-col',\n                ...(props.extraClassNames || []),\n            ], elAttrs: Object.assign({ role: 'gridcell' }, props.extraDataAttrs), date: props.date, dateProfile: props.dateProfile, todayRange: props.todayRange, extraRenderProps: props.extraRenderProps }, (InnerContent) => (createElement(\"div\", { className: \"fc-timegrid-col-frame\" },\n            createElement(\"div\", { className: \"fc-timegrid-col-bg\" },\n                this.renderFillSegs(props.businessHourSegs, 'non-business'),\n                this.renderFillSegs(props.bgEventSegs, 'bg-event'),\n                this.renderFillSegs(props.dateSelectionSegs, 'highlight')),\n            createElement(\"div\", { className: \"fc-timegrid-col-events\" }, this.renderFgSegs(sortedFgSegs, interactionAffectedInstances, false, false, false)),\n            createElement(\"div\", { className: \"fc-timegrid-col-events\" }, this.renderFgSegs(mirrorSegs, {}, Boolean(props.eventDrag), Boolean(props.eventResize), Boolean(isSelectMirror), 'mirror')),\n            createElement(\"div\", { className: \"fc-timegrid-now-indicator-container\" }, this.renderNowIndicator(props.nowIndicatorSegs)),\n            hasCustomDayCellContent(options) && (createElement(InnerContent, { elTag: \"div\", elClasses: ['fc-timegrid-col-misc'] }))))));\n    }\n    renderFgSegs(sortedFgSegs, segIsInvisible, isDragging, isResizing, isDateSelecting, forcedKey) {\n        let { props } = this;\n        if (props.forPrint) {\n            return renderPlainFgSegs(sortedFgSegs, props);\n        }\n        return this.renderPositionedFgSegs(sortedFgSegs, segIsInvisible, isDragging, isResizing, isDateSelecting, forcedKey);\n    }\n    renderPositionedFgSegs(segs, // if not mirror, needs to be sorted\n    segIsInvisible, isDragging, isResizing, isDateSelecting, forcedKey) {\n        let { eventMaxStack, eventShortHeight, eventOrderStrict, eventMinHeight } = this.context.options;\n        let { date, slatCoords, eventSelection, todayRange, nowDate } = this.props;\n        let isMirror = isDragging || isResizing || isDateSelecting;\n        let segVCoords = computeSegVCoords(segs, date, slatCoords, eventMinHeight);\n        let { segPlacements, hiddenGroups } = computeFgSegPlacements(segs, segVCoords, eventOrderStrict, eventMaxStack);\n        return (createElement(Fragment, null,\n            this.renderHiddenGroups(hiddenGroups, segs),\n            segPlacements.map((segPlacement) => {\n                let { seg, rect } = segPlacement;\n                let instanceId = seg.eventRange.instance.instanceId;\n                let isVisible = isMirror || Boolean(!segIsInvisible[instanceId] && rect);\n                let vStyle = computeSegVStyle(rect && rect.span);\n                let hStyle = (!isMirror && rect) ? this.computeSegHStyle(rect) : { left: 0, right: 0 };\n                let isInset = Boolean(rect) && rect.stackForward > 0;\n                let isShort = Boolean(rect) && (rect.span.end - rect.span.start) < eventShortHeight; // look at other places for this problem\n                return (createElement(\"div\", { className: 'fc-timegrid-event-harness' +\n                        (isInset ? ' fc-timegrid-event-harness-inset' : ''), key: forcedKey || instanceId, style: Object.assign(Object.assign({ visibility: isVisible ? '' : 'hidden' }, vStyle), hStyle) },\n                    createElement(TimeColEvent, Object.assign({ seg: seg, isDragging: isDragging, isResizing: isResizing, isDateSelecting: isDateSelecting, isSelected: instanceId === eventSelection, isShort: isShort }, getSegMeta(seg, todayRange, nowDate)))));\n            })));\n    }\n    // will already have eventMinHeight applied because segInputs already had it\n    renderHiddenGroups(hiddenGroups, segs) {\n        let { extraDateSpan, dateProfile, todayRange, nowDate, eventSelection, eventDrag, eventResize } = this.props;\n        return (createElement(Fragment, null, hiddenGroups.map((hiddenGroup) => {\n            let positionCss = computeSegVStyle(hiddenGroup.span);\n            let hiddenSegs = compileSegsFromEntries(hiddenGroup.entries, segs);\n            return (createElement(TimeColMoreLink, { key: buildIsoString(computeEarliestSegStart(hiddenSegs)), hiddenSegs: hiddenSegs, top: positionCss.top, bottom: positionCss.bottom, extraDateSpan: extraDateSpan, dateProfile: dateProfile, todayRange: todayRange, nowDate: nowDate, eventSelection: eventSelection, eventDrag: eventDrag, eventResize: eventResize }));\n        })));\n    }\n    renderFillSegs(segs, fillType) {\n        let { props, context } = this;\n        let segVCoords = computeSegVCoords(segs, props.date, props.slatCoords, context.options.eventMinHeight); // don't assume all populated\n        let children = segVCoords.map((vcoords, i) => {\n            let seg = segs[i];\n            return (createElement(\"div\", { key: buildEventRangeKey(seg.eventRange), className: \"fc-timegrid-bg-harness\", style: computeSegVStyle(vcoords) }, fillType === 'bg-event' ?\n                createElement(BgEvent, Object.assign({ seg: seg }, getSegMeta(seg, props.todayRange, props.nowDate))) :\n                renderFill(fillType)));\n        });\n        return createElement(Fragment, null, children);\n    }\n    renderNowIndicator(segs) {\n        let { slatCoords, date } = this.props;\n        if (!slatCoords) {\n            return null;\n        }\n        return segs.map((seg, i) => (createElement(NowIndicatorContainer\n        // key doesn't matter. will only ever be one\n        , { \n            // key doesn't matter. will only ever be one\n            key: i, elClasses: ['fc-timegrid-now-indicator-line'], elStyle: {\n                top: slatCoords.computeDateTop(seg.start, date),\n            }, isAxis: false, date: date })));\n    }\n    computeSegHStyle(segHCoords) {\n        let { isRtl, options } = this.context;\n        let shouldOverlap = options.slotEventOverlap;\n        let nearCoord = segHCoords.levelCoord; // the left side if LTR. the right side if RTL. floating-point\n        let farCoord = segHCoords.levelCoord + segHCoords.thickness; // the right side if LTR. the left side if RTL. floating-point\n        let left; // amount of space from left edge, a fraction of the total width\n        let right; // amount of space from right edge, a fraction of the total width\n        if (shouldOverlap) {\n            // double the width, but don't go beyond the maximum forward coordinate (1.0)\n            farCoord = Math.min(1, nearCoord + (farCoord - nearCoord) * 2);\n        }\n        if (isRtl) {\n            left = 1 - farCoord;\n            right = nearCoord;\n        }\n        else {\n            left = nearCoord;\n            right = 1 - farCoord;\n        }\n        let props = {\n            zIndex: segHCoords.stackDepth + 1,\n            left: left * 100 + '%',\n            right: right * 100 + '%',\n        };\n        if (shouldOverlap && !segHCoords.stackForward) {\n            // add padding to the edge so that forward stacked events don't cover the resizer's icon\n            props[isRtl ? 'marginLeft' : 'marginRight'] = 10 * 2; // 10 is a guesstimate of the icon's width\n        }\n        return props;\n    }\n}\nfunction renderPlainFgSegs(sortedFgSegs, { todayRange, nowDate, eventSelection, eventDrag, eventResize }) {\n    let hiddenInstances = (eventDrag ? eventDrag.affectedInstances : null) ||\n        (eventResize ? eventResize.affectedInstances : null) ||\n        {};\n    return (createElement(Fragment, null, sortedFgSegs.map((seg) => {\n        let instanceId = seg.eventRange.instance.instanceId;\n        return (createElement(\"div\", { key: instanceId, style: { visibility: hiddenInstances[instanceId] ? 'hidden' : '' } },\n            createElement(TimeColEvent, Object.assign({ seg: seg, isDragging: false, isResizing: false, isDateSelecting: false, isSelected: instanceId === eventSelection, isShort: false }, getSegMeta(seg, todayRange, nowDate)))));\n    })));\n}\nfunction computeSegVStyle(segVCoords) {\n    if (!segVCoords) {\n        return { top: '', bottom: '' };\n    }\n    return {\n        top: segVCoords.start,\n        bottom: -segVCoords.end,\n    };\n}\nfunction compileSegsFromEntries(segEntries, allSegs) {\n    return segEntries.map((segEntry) => allSegs[segEntry.index]);\n}\n\nclass TimeColsContent extends BaseComponent {\n    constructor() {\n        super(...arguments);\n        this.splitFgEventSegs = memoize(splitSegsByCol);\n        this.splitBgEventSegs = memoize(splitSegsByCol);\n        this.splitBusinessHourSegs = memoize(splitSegsByCol);\n        this.splitNowIndicatorSegs = memoize(splitSegsByCol);\n        this.splitDateSelectionSegs = memoize(splitSegsByCol);\n        this.splitEventDrag = memoize(splitInteractionByCol);\n        this.splitEventResize = memoize(splitInteractionByCol);\n        this.rootElRef = createRef();\n        this.cellElRefs = new RefMap();\n    }\n    render() {\n        let { props, context } = this;\n        let nowIndicatorTop = context.options.nowIndicator &&\n            props.slatCoords &&\n            props.slatCoords.safeComputeTop(props.nowDate); // might return void\n        let colCnt = props.cells.length;\n        let fgEventSegsByRow = this.splitFgEventSegs(props.fgEventSegs, colCnt);\n        let bgEventSegsByRow = this.splitBgEventSegs(props.bgEventSegs, colCnt);\n        let businessHourSegsByRow = this.splitBusinessHourSegs(props.businessHourSegs, colCnt);\n        let nowIndicatorSegsByRow = this.splitNowIndicatorSegs(props.nowIndicatorSegs, colCnt);\n        let dateSelectionSegsByRow = this.splitDateSelectionSegs(props.dateSelectionSegs, colCnt);\n        let eventDragByRow = this.splitEventDrag(props.eventDrag, colCnt);\n        let eventResizeByRow = this.splitEventResize(props.eventResize, colCnt);\n        return (createElement(\"div\", { className: \"fc-timegrid-cols\", ref: this.rootElRef },\n            createElement(\"table\", { role: \"presentation\", style: {\n                    minWidth: props.tableMinWidth,\n                    width: props.clientWidth,\n                } },\n                props.tableColGroupNode,\n                createElement(\"tbody\", { role: \"presentation\" },\n                    createElement(\"tr\", { role: \"row\" },\n                        props.axis && (createElement(\"td\", { \"aria-hidden\": true, className: \"fc-timegrid-col fc-timegrid-axis\" },\n                            createElement(\"div\", { className: \"fc-timegrid-col-frame\" },\n                                createElement(\"div\", { className: \"fc-timegrid-now-indicator-container\" }, typeof nowIndicatorTop === 'number' && (createElement(NowIndicatorContainer, { elClasses: ['fc-timegrid-now-indicator-arrow'], elStyle: { top: nowIndicatorTop }, isAxis: true, date: props.nowDate })))))),\n                        props.cells.map((cell, i) => (createElement(TimeCol, { key: cell.key, elRef: this.cellElRefs.createRef(cell.key), dateProfile: props.dateProfile, date: cell.date, nowDate: props.nowDate, todayRange: props.todayRange, extraRenderProps: cell.extraRenderProps, extraDataAttrs: cell.extraDataAttrs, extraClassNames: cell.extraClassNames, extraDateSpan: cell.extraDateSpan, fgEventSegs: fgEventSegsByRow[i], bgEventSegs: bgEventSegsByRow[i], businessHourSegs: businessHourSegsByRow[i], nowIndicatorSegs: nowIndicatorSegsByRow[i], dateSelectionSegs: dateSelectionSegsByRow[i], eventDrag: eventDragByRow[i], eventResize: eventResizeByRow[i], slatCoords: props.slatCoords, eventSelection: props.eventSelection, forPrint: props.forPrint }))))))));\n    }\n    componentDidMount() {\n        this.updateCoords();\n    }\n    componentDidUpdate() {\n        this.updateCoords();\n    }\n    updateCoords() {\n        let { props } = this;\n        if (props.onColCoords &&\n            props.clientWidth !== null // means sizing has stabilized\n        ) {\n            props.onColCoords(new PositionCache(this.rootElRef.current, collectCellEls(this.cellElRefs.currentMap, props.cells), true, // horizontal\n            false));\n        }\n    }\n}\nfunction collectCellEls(elMap, cells) {\n    return cells.map((cell) => elMap[cell.key]);\n}\n\n/* A component that renders one or more columns of vertical time slots\n----------------------------------------------------------------------------------------------------------------------*/\nclass TimeCols extends DateComponent {\n    constructor() {\n        super(...arguments);\n        this.processSlotOptions = memoize(processSlotOptions);\n        this.state = {\n            slatCoords: null,\n        };\n        this.handleRootEl = (el) => {\n            if (el) {\n                this.context.registerInteractiveComponent(this, {\n                    el,\n                    isHitComboAllowed: this.props.isHitComboAllowed,\n                });\n            }\n            else {\n                this.context.unregisterInteractiveComponent(this);\n            }\n        };\n        this.handleScrollRequest = (request) => {\n            let { onScrollTopRequest } = this.props;\n            let { slatCoords } = this.state;\n            if (onScrollTopRequest && slatCoords) {\n                if (request.time) {\n                    let top = slatCoords.computeTimeTop(request.time);\n                    top = Math.ceil(top); // zoom can give weird floating-point values. rather scroll a little bit further\n                    if (top) {\n                        top += 1; // to overcome top border that slots beyond the first have. looks better\n                    }\n                    onScrollTopRequest(top);\n                }\n                return true;\n            }\n            return false;\n        };\n        this.handleColCoords = (colCoords) => {\n            this.colCoords = colCoords;\n        };\n        this.handleSlatCoords = (slatCoords) => {\n            this.setState({ slatCoords });\n            if (this.props.onSlatCoords) {\n                this.props.onSlatCoords(slatCoords);\n            }\n        };\n    }\n    render() {\n        let { props, state } = this;\n        return (createElement(\"div\", { className: \"fc-timegrid-body\", ref: this.handleRootEl, style: {\n                // these props are important to give this wrapper correct dimensions for interactions\n                // TODO: if we set it here, can we avoid giving to inner tables?\n                width: props.clientWidth,\n                minWidth: props.tableMinWidth,\n            } },\n            createElement(TimeColsSlats, { axis: props.axis, dateProfile: props.dateProfile, slatMetas: props.slatMetas, clientWidth: props.clientWidth, minHeight: props.expandRows ? props.clientHeight : '', tableMinWidth: props.tableMinWidth, tableColGroupNode: props.axis ? props.tableColGroupNode : null /* axis depends on the colgroup's shrinking */, onCoords: this.handleSlatCoords }),\n            createElement(TimeColsContent, { cells: props.cells, axis: props.axis, dateProfile: props.dateProfile, businessHourSegs: props.businessHourSegs, bgEventSegs: props.bgEventSegs, fgEventSegs: props.fgEventSegs, dateSelectionSegs: props.dateSelectionSegs, eventSelection: props.eventSelection, eventDrag: props.eventDrag, eventResize: props.eventResize, todayRange: props.todayRange, nowDate: props.nowDate, nowIndicatorSegs: props.nowIndicatorSegs, clientWidth: props.clientWidth, tableMinWidth: props.tableMinWidth, tableColGroupNode: props.tableColGroupNode, slatCoords: state.slatCoords, onColCoords: this.handleColCoords, forPrint: props.forPrint })));\n    }\n    componentDidMount() {\n        this.scrollResponder = this.context.createScrollResponder(this.handleScrollRequest);\n    }\n    componentDidUpdate(prevProps) {\n        this.scrollResponder.update(prevProps.dateProfile !== this.props.dateProfile);\n    }\n    componentWillUnmount() {\n        this.scrollResponder.detach();\n    }\n    queryHit(positionLeft, positionTop) {\n        let { dateEnv, options } = this.context;\n        let { colCoords } = this;\n        let { dateProfile } = this.props;\n        let { slatCoords } = this.state;\n        let { snapDuration, snapsPerSlot } = this.processSlotOptions(this.props.slotDuration, options.snapDuration);\n        let colIndex = colCoords.leftToIndex(positionLeft);\n        let slatIndex = slatCoords.positions.topToIndex(positionTop);\n        if (colIndex != null && slatIndex != null) {\n            let cell = this.props.cells[colIndex];\n            let slatTop = slatCoords.positions.tops[slatIndex];\n            let slatHeight = slatCoords.positions.getHeight(slatIndex);\n            let partial = (positionTop - slatTop) / slatHeight; // floating point number between 0 and 1\n            let localSnapIndex = Math.floor(partial * snapsPerSlot); // the snap # relative to start of slat\n            let snapIndex = slatIndex * snapsPerSlot + localSnapIndex;\n            let dayDate = this.props.cells[colIndex].date;\n            let time = addDurations(dateProfile.slotMinTime, multiplyDuration(snapDuration, snapIndex));\n            let start = dateEnv.add(dayDate, time);\n            let end = dateEnv.add(start, snapDuration);\n            return {\n                dateProfile,\n                dateSpan: Object.assign({ range: { start, end }, allDay: false }, cell.extraDateSpan),\n                dayEl: colCoords.els[colIndex],\n                rect: {\n                    left: colCoords.lefts[colIndex],\n                    right: colCoords.rights[colIndex],\n                    top: slatTop,\n                    bottom: slatTop + slatHeight,\n                },\n                layer: 0,\n            };\n        }\n        return null;\n    }\n}\nfunction processSlotOptions(slotDuration, snapDurationOverride) {\n    let snapDuration = snapDurationOverride || slotDuration;\n    let snapsPerSlot = wholeDivideDurations(slotDuration, snapDuration);\n    if (snapsPerSlot === null) {\n        snapDuration = slotDuration;\n        snapsPerSlot = 1;\n        // TODO: say warning?\n    }\n    return { snapDuration, snapsPerSlot };\n}\n\nclass DayTimeColsSlicer extends Slicer {\n    sliceRange(range, dayRanges) {\n        let segs = [];\n        for (let col = 0; col < dayRanges.length; col += 1) {\n            let segRange = intersectRanges(range, dayRanges[col]);\n            if (segRange) {\n                segs.push({\n                    start: segRange.start,\n                    end: segRange.end,\n                    isStart: segRange.start.valueOf() === range.start.valueOf(),\n                    isEnd: segRange.end.valueOf() === range.end.valueOf(),\n                    col,\n                });\n            }\n        }\n        return segs;\n    }\n}\n\nclass DayTimeCols extends DateComponent {\n    constructor() {\n        super(...arguments);\n        this.buildDayRanges = memoize(buildDayRanges);\n        this.slicer = new DayTimeColsSlicer();\n        this.timeColsRef = createRef();\n    }\n    render() {\n        let { props, context } = this;\n        let { dateProfile, dayTableModel } = props;\n        let { nowIndicator, nextDayThreshold } = context.options;\n        let dayRanges = this.buildDayRanges(dayTableModel, dateProfile, context.dateEnv);\n        // give it the first row of cells\n        // TODO: would move this further down hierarchy, but sliceNowDate needs it\n        return (createElement(NowTimer, { unit: nowIndicator ? 'minute' : 'day' }, (nowDate, todayRange) => (createElement(TimeCols, Object.assign({ ref: this.timeColsRef }, this.slicer.sliceProps(props, dateProfile, null, context, dayRanges), { forPrint: props.forPrint, axis: props.axis, dateProfile: dateProfile, slatMetas: props.slatMetas, slotDuration: props.slotDuration, cells: dayTableModel.cells[0], tableColGroupNode: props.tableColGroupNode, tableMinWidth: props.tableMinWidth, clientWidth: props.clientWidth, clientHeight: props.clientHeight, expandRows: props.expandRows, nowDate: nowDate, nowIndicatorSegs: nowIndicator && this.slicer.sliceNowDate(nowDate, dateProfile, nextDayThreshold, context, dayRanges), todayRange: todayRange, onScrollTopRequest: props.onScrollTopRequest, onSlatCoords: props.onSlatCoords })))));\n    }\n}\nfunction buildDayRanges(dayTableModel, dateProfile, dateEnv) {\n    let ranges = [];\n    for (let date of dayTableModel.headerDates) {\n        ranges.push({\n            start: dateEnv.add(date, dateProfile.slotMinTime),\n            end: dateEnv.add(date, dateProfile.slotMaxTime),\n        });\n    }\n    return ranges;\n}\n\n// potential nice values for the slot-duration and interval-duration\n// from largest to smallest\nconst STOCK_SUB_DURATIONS = [\n    { hours: 1 },\n    { minutes: 30 },\n    { minutes: 15 },\n    { seconds: 30 },\n    { seconds: 15 },\n];\nfunction buildSlatMetas(slotMinTime, slotMaxTime, explicitLabelInterval, slotDuration, dateEnv) {\n    let dayStart = new Date(0);\n    let slatTime = slotMinTime;\n    let slatIterator = createDuration(0);\n    let labelInterval = explicitLabelInterval || computeLabelInterval(slotDuration);\n    let metas = [];\n    while (asRoughMs(slatTime) < asRoughMs(slotMaxTime)) {\n        let date = dateEnv.add(dayStart, slatTime);\n        let isLabeled = wholeDivideDurations(slatIterator, labelInterval) !== null;\n        metas.push({\n            date,\n            time: slatTime,\n            key: date.toISOString(),\n            isoTimeStr: formatIsoTimeString(date),\n            isLabeled,\n        });\n        slatTime = addDurations(slatTime, slotDuration);\n        slatIterator = addDurations(slatIterator, slotDuration);\n    }\n    return metas;\n}\n// Computes an automatic value for slotLabelInterval\nfunction computeLabelInterval(slotDuration) {\n    let i;\n    let labelInterval;\n    let slotsPerLabel;\n    // find the smallest stock label interval that results in more than one slots-per-label\n    for (i = STOCK_SUB_DURATIONS.length - 1; i >= 0; i -= 1) {\n        labelInterval = createDuration(STOCK_SUB_DURATIONS[i]);\n        slotsPerLabel = wholeDivideDurations(labelInterval, slotDuration);\n        if (slotsPerLabel !== null && slotsPerLabel > 1) {\n            return labelInterval;\n        }\n    }\n    return slotDuration; // fall back\n}\n\nclass DayTimeColsView extends TimeColsView {\n    constructor() {\n        super(...arguments);\n        this.buildTimeColsModel = memoize(buildTimeColsModel);\n        this.buildSlatMetas = memoize(buildSlatMetas);\n    }\n    render() {\n        let { options, dateEnv, dateProfileGenerator } = this.context;\n        let { props } = this;\n        let { dateProfile } = props;\n        let dayTableModel = this.buildTimeColsModel(dateProfile, dateProfileGenerator);\n        let splitProps = this.allDaySplitter.splitProps(props);\n        let slatMetas = this.buildSlatMetas(dateProfile.slotMinTime, dateProfile.slotMaxTime, options.slotLabelInterval, options.slotDuration, dateEnv);\n        let { dayMinWidth } = options;\n        let hasAttachedAxis = !dayMinWidth;\n        let hasDetachedAxis = dayMinWidth;\n        let headerContent = options.dayHeaders && (createElement(DayHeader, { dates: dayTableModel.headerDates, dateProfile: dateProfile, datesRepDistinctDays: true, renderIntro: hasAttachedAxis ? this.renderHeadAxis : null }));\n        let allDayContent = (options.allDaySlot !== false) && ((contentArg) => (createElement(DayTable, Object.assign({}, splitProps.allDay, { dateProfile: dateProfile, dayTableModel: dayTableModel, nextDayThreshold: options.nextDayThreshold, tableMinWidth: contentArg.tableMinWidth, colGroupNode: contentArg.tableColGroupNode, renderRowIntro: hasAttachedAxis ? this.renderTableRowAxis : null, showWeekNumbers: false, expandRows: false, headerAlignElRef: this.headerElRef, clientWidth: contentArg.clientWidth, clientHeight: contentArg.clientHeight, forPrint: props.forPrint }, this.getAllDayMaxEventProps()))));\n        let timeGridContent = (contentArg) => (createElement(DayTimeCols, Object.assign({}, splitProps.timed, { dayTableModel: dayTableModel, dateProfile: dateProfile, axis: hasAttachedAxis, slotDuration: options.slotDuration, slatMetas: slatMetas, forPrint: props.forPrint, tableColGroupNode: contentArg.tableColGroupNode, tableMinWidth: contentArg.tableMinWidth, clientWidth: contentArg.clientWidth, clientHeight: contentArg.clientHeight, onSlatCoords: this.handleSlatCoords, expandRows: contentArg.expandRows, onScrollTopRequest: this.handleScrollTopRequest })));\n        return hasDetachedAxis\n            ? this.renderHScrollLayout(headerContent, allDayContent, timeGridContent, dayTableModel.colCnt, dayMinWidth, slatMetas, this.state.slatCoords)\n            : this.renderSimpleLayout(headerContent, allDayContent, timeGridContent);\n    }\n}\nfunction buildTimeColsModel(dateProfile, dateProfileGenerator) {\n    let daySeries = new DaySeriesModel(dateProfile.renderRange, dateProfileGenerator);\n    return new DayTableModel(daySeries, false);\n}\n\nvar css_248z = \".fc-v-event{background-color:var(--fc-event-bg-color);border:1px solid var(--fc-event-border-color);display:block}.fc-v-event .fc-event-main{color:var(--fc-event-text-color);height:100%}.fc-v-event .fc-event-main-frame{display:flex;flex-direction:column;height:100%}.fc-v-event .fc-event-time{flex-grow:0;flex-shrink:0;max-height:100%;overflow:hidden}.fc-v-event .fc-event-title-container{flex-grow:1;flex-shrink:1;min-height:0}.fc-v-event .fc-event-title{bottom:0;max-height:100%;overflow:hidden;top:0}.fc-v-event:not(.fc-event-start){border-top-left-radius:0;border-top-right-radius:0;border-top-width:0}.fc-v-event:not(.fc-event-end){border-bottom-left-radius:0;border-bottom-right-radius:0;border-bottom-width:0}.fc-v-event.fc-event-selected:before{left:-10px;right:-10px}.fc-v-event .fc-event-resizer-start{cursor:n-resize}.fc-v-event .fc-event-resizer-end{cursor:s-resize}.fc-v-event:not(.fc-event-selected) .fc-event-resizer{height:var(--fc-event-resizer-thickness);left:0;right:0}.fc-v-event:not(.fc-event-selected) .fc-event-resizer-start{top:calc(var(--fc-event-resizer-thickness)/-2)}.fc-v-event:not(.fc-event-selected) .fc-event-resizer-end{bottom:calc(var(--fc-event-resizer-thickness)/-2)}.fc-v-event.fc-event-selected .fc-event-resizer{left:50%;margin-left:calc(var(--fc-event-resizer-dot-total-width)/-2)}.fc-v-event.fc-event-selected .fc-event-resizer-start{top:calc(var(--fc-event-resizer-dot-total-width)/-2)}.fc-v-event.fc-event-selected .fc-event-resizer-end{bottom:calc(var(--fc-event-resizer-dot-total-width)/-2)}.fc .fc-timegrid .fc-daygrid-body{z-index:2}.fc .fc-timegrid-divider{padding:0 0 2px}.fc .fc-timegrid-body{min-height:100%;position:relative;z-index:1}.fc .fc-timegrid-axis-chunk{position:relative}.fc .fc-timegrid-axis-chunk>table,.fc .fc-timegrid-slots{position:relative;z-index:1}.fc .fc-timegrid-slot{border-bottom:0;height:1.5em}.fc .fc-timegrid-slot:empty:before{content:\\\"\\\\00a0\\\"}.fc .fc-timegrid-slot-minor{border-top-style:dotted}.fc .fc-timegrid-slot-label-cushion{display:inline-block;white-space:nowrap}.fc .fc-timegrid-slot-label{vertical-align:middle}.fc .fc-timegrid-axis-cushion,.fc .fc-timegrid-slot-label-cushion{padding:0 4px}.fc .fc-timegrid-axis-frame-liquid{height:100%}.fc .fc-timegrid-axis-frame{align-items:center;display:flex;justify-content:flex-end;overflow:hidden}.fc .fc-timegrid-axis-cushion{flex-shrink:0;max-width:60px}.fc-direction-ltr .fc-timegrid-slot-label-frame{text-align:right}.fc-direction-rtl .fc-timegrid-slot-label-frame{text-align:left}.fc-liquid-hack .fc-timegrid-axis-frame-liquid{bottom:0;height:auto;left:0;position:absolute;right:0;top:0}.fc .fc-timegrid-col.fc-day-today{background-color:var(--fc-today-bg-color)}.fc .fc-timegrid-col-frame{min-height:100%;position:relative}.fc-media-screen.fc-liquid-hack .fc-timegrid-col-frame{bottom:0;height:auto;left:0;position:absolute;right:0;top:0}.fc-media-screen .fc-timegrid-cols{bottom:0;left:0;position:absolute;right:0;top:0}.fc-media-screen .fc-timegrid-cols>table{height:100%}.fc-media-screen .fc-timegrid-col-bg,.fc-media-screen .fc-timegrid-col-events,.fc-media-screen .fc-timegrid-now-indicator-container{left:0;position:absolute;right:0;top:0}.fc .fc-timegrid-col-bg{z-index:2}.fc .fc-timegrid-col-bg .fc-non-business{z-index:1}.fc .fc-timegrid-col-bg .fc-bg-event{z-index:2}.fc .fc-timegrid-col-bg .fc-highlight{z-index:3}.fc .fc-timegrid-bg-harness{left:0;position:absolute;right:0}.fc .fc-timegrid-col-events{z-index:3}.fc .fc-timegrid-now-indicator-container{bottom:0;overflow:hidden}.fc-direction-ltr .fc-timegrid-col-events{margin:0 2.5% 0 2px}.fc-direction-rtl .fc-timegrid-col-events{margin:0 2px 0 2.5%}.fc-timegrid-event-harness{position:absolute}.fc-timegrid-event-harness>.fc-timegrid-event{bottom:0;left:0;position:absolute;right:0;top:0}.fc-timegrid-event-harness-inset .fc-timegrid-event,.fc-timegrid-event.fc-event-mirror,.fc-timegrid-more-link{box-shadow:0 0 0 1px var(--fc-page-bg-color)}.fc-timegrid-event,.fc-timegrid-more-link{border-radius:3px;font-size:var(--fc-small-font-size)}.fc-timegrid-event{margin-bottom:1px}.fc-timegrid-event .fc-event-main{padding:1px 1px 0}.fc-timegrid-event .fc-event-time{font-size:var(--fc-small-font-size);margin-bottom:1px;white-space:nowrap}.fc-timegrid-event-short .fc-event-main-frame{flex-direction:row;overflow:hidden}.fc-timegrid-event-short .fc-event-time:after{content:\\\"\\\\00a0-\\\\00a0\\\"}.fc-timegrid-event-short .fc-event-title{font-size:var(--fc-small-font-size)}.fc-timegrid-more-link{background:var(--fc-more-link-bg-color);color:var(--fc-more-link-text-color);cursor:pointer;margin-bottom:1px;position:absolute;z-index:9999}.fc-timegrid-more-link-inner{padding:3px 2px;top:0}.fc-direction-ltr .fc-timegrid-more-link{right:0}.fc-direction-rtl .fc-timegrid-more-link{left:0}.fc .fc-timegrid-now-indicator-line{border-color:var(--fc-now-indicator-color);border-style:solid;border-width:1px 0 0;left:0;position:absolute;right:0;z-index:4}.fc .fc-timegrid-now-indicator-arrow{border-color:var(--fc-now-indicator-color);border-style:solid;margin-top:-5px;position:absolute;z-index:4}.fc-direction-ltr .fc-timegrid-now-indicator-arrow{border-bottom-color:transparent;border-top-color:transparent;border-width:5px 0 5px 6px;left:0}.fc-direction-rtl .fc-timegrid-now-indicator-arrow{border-bottom-color:transparent;border-top-color:transparent;border-width:5px 6px 5px 0;right:0}\";\ninjectStyles(css_248z);\n\nexport { DayTimeCols, DayTimeColsSlicer, DayTimeColsView, TimeCols, TimeColsSlatsCoords, TimeColsView, buildDayRanges, buildSlatMetas, buildTimeColsModel };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,cAAc,EAAEC,eAAe,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAEC,oBAAoB,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,wBAAwB,EAAEC,QAAQ,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,UAAU,EAAEC,SAAS,EAAEC,cAAc,EAAEC,MAAM,EAAEC,aAAa,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,wBAAwB,EAAEC,YAAY,EAAEC,eAAe,EAAEC,aAAa,EAAEC,aAAa,EAAEC,OAAO,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,uBAAuB,EAAEC,UAAU,EAAEC,cAAc,EAAEC,uBAAuB,EAAEC,kBAAkB,EAAEC,OAAO,EAAEC,UAAU,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,oBAAoB,EAAEC,MAAM,EAAEC,eAAe,EAAEC,mBAAmB,EAAEC,SAAS,EAAEC,cAAc,EAAEC,aAAa,EAAEC,YAAY,QAAQ,gCAAgC;AACz1B,SAASC,aAAa,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,8BAA8B;AACjF,SAASC,QAAQ,QAAQ,mCAAmC;AAE5D,MAAMC,cAAc,SAAStD,QAAQ,CAAC;EAClCuD,UAAUA,CAAA,EAAG;IACT,OAAO;MACHC,MAAM,EAAE,CAAC,CAAC;MACVC,KAAK,EAAE,CAAC;IACZ,CAAC;EACL;EACAC,kBAAkBA,CAACC,QAAQ,EAAE;IACzB,IAAIA,QAAQ,CAACH,MAAM,EAAE;MACjB,OAAO,CAAC,QAAQ,CAAC;IACrB;IACA,OAAO,CAAC,OAAO,CAAC;EACpB;EACAI,kBAAkBA,CAACC,QAAQ,EAAE;IACzB,IAAI,CAACA,QAAQ,CAACL,MAAM,EAAE;MAClB,OAAO,CAAC,OAAO,CAAC;IACpB;IACA,IAAIvD,cAAc,CAAC4D,QAAQ,CAAC,EAAE;MAC1B,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC;IAC9B;IACA,OAAO,CAAC,QAAQ,CAAC;EACrB;AACJ;AAEA,MAAMC,yBAAyB,GAAG5D,eAAe,CAAC;EAC9C6D,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE,SAAS;EACjBC,cAAc,EAAE,IAAI;EACpBC,QAAQ,EAAE;AACd,CAAC,CAAC;AACF,SAASC,gBAAgBA,CAACC,KAAK,EAAE;EAC7B,IAAIC,UAAU,GAAG,CACb,kBAAkB,EAClB,wBAAwB,EACxBD,KAAK,CAACE,SAAS,GAAG,sBAAsB,GAAG,wBAAwB,CACtE;EACD,OAAQpB,aAAa,CAAC/C,eAAe,CAACoE,QAAQ,EAAE,IAAI,EAAGC,OAAO,IAAK;IAC/D,IAAI,CAACJ,KAAK,CAACE,SAAS,EAAE;MAClB,OAAQpB,aAAa,CAAC,IAAI,EAAE;QAAEuB,SAAS,EAAEJ,UAAU,CAACK,IAAI,CAAC,GAAG,CAAC;QAAE,WAAW,EAAEN,KAAK,CAACO;MAAW,CAAC,CAAC;IACnG;IACA,IAAI;MAAEC,OAAO;MAAEC,OAAO;MAAEC;IAAQ,CAAC,GAAGN,OAAO;IAC3C,IAAIO,WAAW;IAAG;IACjBF,OAAO,CAACG,eAAe,IAAI,IAAI,GAAGlB,yBAAyB,GACxDmB,KAAK,CAACC,OAAO,CAACL,OAAO,CAACG,eAAe,CAAC,GAAG9E,eAAe,CAAC2E,OAAO,CAACG,eAAe,CAAC,CAAC,CAAC,CAAC,GAChF9E,eAAe,CAAC2E,OAAO,CAACG,eAAe,CAAC;IAChD,IAAIG,WAAW,GAAG;MACdC,KAAK,EAAE,CAAC;MACRC,IAAI,EAAEjB,KAAK,CAACiB,IAAI;MAChBC,IAAI,EAAEV,OAAO,CAACW,MAAM,CAACnB,KAAK,CAACkB,IAAI,CAAC;MAChCE,IAAI,EAAEV,OAAO;MACbW,IAAI,EAAEb,OAAO,CAACc,MAAM,CAACtB,KAAK,CAACkB,IAAI,EAAEP,WAAW;IAChD,CAAC;IACD,OAAQ7B,aAAa,CAAC9C,gBAAgB,EAAE;MAAEuF,KAAK,EAAE,IAAI;MAAEC,SAAS,EAAEvB,UAAU;MAAEwB,OAAO,EAAE;QAC/E,WAAW,EAAEzB,KAAK,CAACO;MACvB,CAAC;MAAEQ,WAAW,EAAEA,WAAW;MAAEW,aAAa,EAAE,kBAAkB;MAAEC,eAAe,EAAElB,OAAO,CAACmB,gBAAgB;MAAEC,gBAAgB,EAAEC,kBAAkB;MAAEC,kBAAkB,EAAEtB,OAAO,CAACuB,mBAAmB;MAAEC,QAAQ,EAAExB,OAAO,CAACyB,iBAAiB;MAAEC,WAAW,EAAE1B,OAAO,CAAC2B;IAAqB,CAAC,EAAGC,YAAY,IAAMvD,aAAa,CAAC,KAAK,EAAE;MAAEuB,SAAS,EAAE;IAA0D,CAAC,EACpYvB,aAAa,CAACuD,YAAY,EAAE;MAAEd,KAAK,EAAE,KAAK;MAAEC,SAAS,EAAE,CAC/C,gCAAgC,EAChC,8BAA8B;IAChC,CAAC,CAAC,CAAE,CAAC;EACnB,CAAC,CAAC;AACN;AACA,SAASM,kBAAkBA,CAAC9B,KAAK,EAAE;EAC/B,OAAOA,KAAK,CAACqB,IAAI;AACrB;AAEA,MAAMiB,YAAY,SAASrG,aAAa,CAAC;EACrCsG,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACvC,KAAK,CAACwC,SAAS,CAACC,GAAG,CAAEC,QAAQ,IAAM5D,aAAa,CAAC,IAAI,EAAE;MAAE6D,GAAG,EAAED,QAAQ,CAACC;IAAI,CAAC,EACpF7D,aAAa,CAACiB,gBAAgB,EAAE6C,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,QAAQ,CAAC,CAAC,CAAE,CAAC;EACvE;AACJ;AAEA,MAAMI,uBAAuB,GAAGhH,eAAe,CAAC;EAAEiH,IAAI,EAAE;AAAQ,CAAC,CAAC;AAClE,MAAMC,2BAA2B,GAAG,CAAC;AACrC,MAAMC,YAAY,SAAS/G,aAAa,CAAC;EACrCgH,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,cAAc,GAAG,IAAIlE,cAAc,CAAC,CAAC,CAAC,CAAC;IAC5C,IAAI,CAACmE,WAAW,GAAGtE,SAAS,CAAC,CAAC;IAC9B,IAAI,CAACuE,SAAS,GAAGvE,SAAS,CAAC,CAAC;IAC5B,IAAI,CAACwE,aAAa,GAAGxE,SAAS,CAAC,CAAC;IAChC,IAAI,CAACyE,KAAK,GAAG;MACTC,UAAU,EAAE;IAChB,CAAC;IACD,IAAI,CAACC,sBAAsB,GAAIC,SAAS,IAAK;MACzC,IAAIC,UAAU,GAAG,IAAI,CAACL,aAAa,CAACM,OAAO;MAC3C,IAAID,UAAU,EAAE;QAAE;QACdA,UAAU,CAACD,SAAS,GAAGA,SAAS;MACpC;IACJ,CAAC;IACD;AACR;IACQ,IAAI,CAACG,cAAc,GAAG,CAACC,MAAM,EAAEC,WAAW,GAAG,EAAE,KAAK;MAChD,IAAI;QAAEvD;MAAQ,CAAC,GAAG,IAAI,CAACL,OAAO;MAC9B,IAAI;QAAE6D;MAAY,CAAC,GAAG,IAAI,CAACjE,KAAK;MAChC,IAAIkE,KAAK,GAAGD,WAAW,CAACE,WAAW;MACnC,IAAIC,MAAM,GAAGjI,QAAQ,CAAC+H,KAAK,CAACG,KAAK,EAAEH,KAAK,CAACI,GAAG,CAAC;MAC7C;MACA,IAAIC,YAAY,GAAIH,MAAM,KAAK,CAAC,GAC1BhI,iBAAiB,CAAC,IAAI,CAACgE,OAAO,EAAE8D,KAAK,CAACG,KAAK,EAAE,MAAM,CAAC,GACpD,CAAC,CAAC;MACR,IAAI5D,OAAO,CAAC+D,WAAW,IAAIT,MAAM,KAAK,KAAK,EAAE;QACzC,OAAQjF,aAAa,CAACzC,mBAAmB,EAAE;UAAEkF,KAAK,EAAE,IAAI;UAAEC,SAAS,EAAE,CAC7D,kBAAkB,EAClB,sBAAsB,CACzB;UAAEC,OAAO,EAAE;YACR,aAAa,EAAE;UACnB,CAAC;UAAEP,IAAI,EAAEgD,KAAK,CAACG,KAAK;UAAEI,aAAa,EAAE3B;QAAwB,CAAC,EAAGT,YAAY,IAAMvD,aAAa,CAAC,KAAK,EAAE;UAAEuB,SAAS,EAAE,CACjH,wBAAwB,EACxB,4BAA4B,EAC5B,+BAA+B,CAClC,CAACC,IAAI,CAAC,GAAG,CAAC;UAAEoE,KAAK,EAAE;YAAEC,MAAM,EAAEX;UAAY;QAAE,CAAC,EAC7ClF,aAAa,CAACuD,YAAY,EAAE;UAAEd,KAAK,EAAE,GAAG;UAAEC,SAAS,EAAE,CAC7C,0BAA0B,EAC1B,8BAA8B,EAC9B,0BAA0B,CAC7B;UAAEC,OAAO,EAAE8C;QAAa,CAAC,CAAC,CAAE,CAAC;MAC1C;MACA,OAAQzF,aAAa,CAAC,IAAI,EAAE;QAAE,aAAa,EAAE,IAAI;QAAEuB,SAAS,EAAE;MAAmB,CAAC,EAC9EvB,aAAa,CAAC,KAAK,EAAE;QAAEuB,SAAS,EAAE,wBAAwB;QAAEqE,KAAK,EAAE;UAAEC,MAAM,EAAEX;QAAY;MAAE,CAAC,CAAC,CAAC;IACtG,CAAC;IACD;AACR;IACQ;IACA;IACA,IAAI,CAACY,kBAAkB,GAAIC,SAAS,IAAK;MACrC,IAAI;QAAEpE,OAAO;QAAEC;MAAQ,CAAC,GAAG,IAAI,CAACN,OAAO;MACvC,IAAIW,WAAW,GAAG;QACdM,IAAI,EAAEZ,OAAO,CAACqE,UAAU;QACxB1D,IAAI,EAAEV;MACV,CAAC;MACD;QACA;QACA5B,aAAa,CAAC9C,gBAAgB,EAAE;UAAEuF,KAAK,EAAE,IAAI;UAAEC,SAAS,EAAE,CAClD,kBAAkB,EAClB,sBAAsB,CACzB;UAAEC,OAAO,EAAE;YACR,aAAa,EAAE;UACnB,CAAC;UAAEV,WAAW,EAAEA,WAAW;UAAEW,aAAa,EAAE,eAAe;UAAEC,eAAe,EAAElB,OAAO,CAACsE,aAAa;UAAElD,gBAAgB,EAAEmD,iBAAiB;UAAEjD,kBAAkB,EAAEtB,OAAO,CAACwE,gBAAgB;UAAEhD,QAAQ,EAAExB,OAAO,CAACyE,cAAc;UAAE/C,WAAW,EAAE1B,OAAO,CAAC0E;QAAkB,CAAC,EAAG9C,YAAY,IAAMvD,aAAa,CAAC,KAAK,EAAE;UAAEuB,SAAS,EAAE,CACrT,wBAAwB,EACxB,4BAA4B,EAC5BwE,SAAS,IAAI,IAAI,GAAG,gCAAgC,GAAG,EAAE,CAC5D,CAACvE,IAAI,CAAC,GAAG,CAAC;UAAEoE,KAAK,EAAE;YAAEC,MAAM,EAAEE;UAAU;QAAE,CAAC,EAC3C/F,aAAa,CAACuD,YAAY,EAAE;UAAEd,KAAK,EAAE,MAAM;UAAEC,SAAS,EAAE,CAChD,0BAA0B,EAC1B,8BAA8B,EAC9B,0BAA0B;QAC5B,CAAC,CAAC,CAAE;MAAC;IACnB,CAAC;IACD,IAAI,CAAC4D,gBAAgB,GAAI3B,UAAU,IAAK;MACpC,IAAI,CAAC4B,QAAQ,CAAC;QAAE5B;MAAW,CAAC,CAAC;IACjC,CAAC;EACL;EACA;EACA;EACA6B,kBAAkBA,CAACC,gBAAgB,EAAER,aAAa,EAAES,WAAW,EAAE;IAC7D,IAAI;MAAEpF,OAAO;MAAEJ;IAAM,CAAC,GAAG,IAAI;IAC7B,IAAIyF,QAAQ,GAAG,EAAE;IACjB,IAAIC,iBAAiB,GAAGpJ,oBAAoB,CAAC8D,OAAO,CAACK,OAAO,CAAC;IAC7D,IAAI8E,gBAAgB,EAAE;MAClBE,QAAQ,CAACE,IAAI,CAAC;QACVC,IAAI,EAAE,QAAQ;QACdjD,GAAG,EAAE,QAAQ;QACbkD,QAAQ,EAAEH,iBAAiB;QAC3BI,KAAK,EAAE;UACHC,KAAK,EAAE,IAAI,CAAC1C,WAAW;UACvB2C,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAEV;QAChB;MACJ,CAAC,CAAC;IACN;IACA,IAAIR,aAAa,EAAE;MACfU,QAAQ,CAACE,IAAI,CAAC;QACVC,IAAI,EAAE,MAAM;QACZjD,GAAG,EAAE,SAAS;QACdmD,KAAK,EAAE;UAAEI,OAAO,EAAEnB;QAAc;MACpC,CAAC,CAAC;MACFU,QAAQ,CAACE,IAAI,CAAC;QACVC,IAAI,EAAE,MAAM;QACZjD,GAAG,EAAE,iBAAiB;QACtBwD,YAAY;QAAI;QAChBrH,aAAa,CAAC,IAAI,EAAE;UAAEsH,IAAI,EAAE,cAAc;UAAE/F,SAAS,EAAE;QAAwB,CAAC,EAC5EvB,aAAa,CAAC,IAAI,EAAE;UAAEuB,SAAS,EAAE,sBAAsB,GAAGD,OAAO,CAACiG,KAAK,CAACC,QAAQ,CAAC,iBAAiB;QAAE,CAAC,CAAC;MAC9G,CAAC,CAAC;IACN;IACAb,QAAQ,CAACE,IAAI,CAAC;MACVC,IAAI,EAAE,MAAM;MACZjD,GAAG,EAAE,MAAM;MACX4D,MAAM,EAAE,IAAI;MACZC,UAAU,EAAEC,OAAO,CAACrG,OAAO,CAACK,OAAO,CAAC+F,UAAU,CAAC;MAC/CV,KAAK,EAAE;QACHvC,aAAa,EAAE,IAAI,CAACA,aAAa;QACjC2C,OAAO,EAAEV;MACb;IACJ,CAAC,CAAC;IACF,OAAQ1G,aAAa,CAACvC,aAAa,EAAE;MAAEwJ,KAAK,EAAE,IAAI,CAACzC,SAAS;MAAE9B,SAAS,EAAE,CAAC,aAAa,CAAC;MAAEkF,QAAQ,EAAEtG,OAAO,CAACsG;IAAS,CAAC,EAClH5H,aAAa,CAACtC,gBAAgB,EAAE;MAAE+J,MAAM,EAAE,CAACvG,KAAK,CAAC2G,YAAY,IAAI,CAAC3G,KAAK,CAAC4G,QAAQ;MAAEC,gBAAgB,EAAE7G,KAAK,CAAC4G,QAAQ;MAAEE,IAAI,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAS,CAAC,CAAC;MAAEtB,QAAQ,EAAEA;IAAS,CAAC,CAAC,CAAC;EAC/K;EACAuB,mBAAmBA,CAACzB,gBAAgB,EAAER,aAAa,EAAES,WAAW,EAAEyB,MAAM,EAAEC,WAAW,EAAE1E,SAAS,EAAEiB,UAAU,EAAE;IAC1G,IAAI0D,UAAU,GAAG,IAAI,CAAC/G,OAAO,CAACgH,WAAW,CAACC,cAAc;IACxD,IAAI,CAACF,UAAU,EAAE;MACb,MAAM,IAAIG,KAAK,CAAC,8BAA8B,CAAC;IACnD;IACA,IAAI;MAAElH,OAAO;MAAEJ;IAAM,CAAC,GAAG,IAAI;IAC7B,IAAI0F,iBAAiB,GAAG,CAAC1F,KAAK,CAAC4G,QAAQ,IAAItK,oBAAoB,CAAC8D,OAAO,CAACK,OAAO,CAAC;IAChF,IAAI8G,qBAAqB,GAAG,CAACvH,KAAK,CAAC4G,QAAQ,IAAInK,wBAAwB,CAAC2D,OAAO,CAACK,OAAO,CAAC;IACxF,IAAIgF,QAAQ,GAAG,EAAE;IACjB,IAAIF,gBAAgB,EAAE;MAClBE,QAAQ,CAACE,IAAI,CAAC;QACVC,IAAI,EAAE,QAAQ;QACdjD,GAAG,EAAE,QAAQ;QACbkD,QAAQ,EAAEH,iBAAiB;QAC3B8B,cAAc,EAAE,IAAI;QACpBC,MAAM,EAAE,CACJ;UACI9E,GAAG,EAAE,MAAM;UACXsD,UAAU,EAAGyB,GAAG,IAAM5I,aAAa,CAAC,IAAI,EAAE;YAAEsH,IAAI,EAAE;UAAe,CAAC,EAAE,IAAI,CAACtC,cAAc,CAAC,KAAK,EAAE4D,GAAG,CAACC,cAAc,CAAC,CAAC,CAAC,CAAC;QACzH,CAAC,EACD;UACIhF,GAAG,EAAE,MAAM;UACXoD,KAAK,EAAE,IAAI,CAAC1C,WAAW;UACvB2C,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAEV;QAChB,CAAC;MAET,CAAC,CAAC;IACN;IACA,IAAIR,aAAa,EAAE;MACfU,QAAQ,CAACE,IAAI,CAAC;QACVC,IAAI,EAAE,MAAM;QACZjD,GAAG,EAAE,SAAS;QACd6E,cAAc,EAAE,IAAI;QACpBC,MAAM,EAAE,CACJ;UACI9E,GAAG,EAAE,MAAM;UACXsD,UAAU,EAAG2B,UAAU,IAAM9I,aAAa,CAAC,IAAI,EAAE;YAAEsH,IAAI,EAAE;UAAe,CAAC,EAAE,IAAI,CAACxB,kBAAkB,CAACgD,UAAU,CAACD,cAAc,CAAC,CAAC,CAAC,CAAC;QACpI,CAAC,EACD;UACIhF,GAAG,EAAE,MAAM;UACXuD,OAAO,EAAEnB;QACb,CAAC;MAET,CAAC,CAAC;MACFU,QAAQ,CAACE,IAAI,CAAC;QACVhD,GAAG,EAAE,iBAAiB;QACtBiD,IAAI,EAAE,MAAM;QACZO,YAAY;QAAI;QAChBrH,aAAa,CAAC,IAAI,EAAE;UAAEsH,IAAI,EAAE,cAAc;UAAE/F,SAAS,EAAE;QAAwB,CAAC,EAC5EvB,aAAa,CAAC,IAAI,EAAE;UAAE+I,OAAO,EAAE,CAAC;UAAExH,SAAS,EAAE,sBAAsB,GAAGD,OAAO,CAACiG,KAAK,CAACC,QAAQ,CAAC,iBAAiB;QAAE,CAAC,CAAC;MAC1H,CAAC,CAAC;IACN;IACA,IAAIwB,cAAc,GAAG1H,OAAO,CAACK,OAAO,CAACsH,YAAY;IACjDtC,QAAQ,CAACE,IAAI,CAAC;MACVC,IAAI,EAAE,MAAM;MACZjD,GAAG,EAAE,MAAM;MACX4D,MAAM,EAAE,IAAI;MACZC,UAAU,EAAEC,OAAO,CAACrG,OAAO,CAACK,OAAO,CAAC+F,UAAU,CAAC;MAC/CiB,MAAM,EAAE,CACJ;QACI9E,GAAG,EAAE,MAAM;QACXuD,OAAO,EAAGwB,GAAG;QACb;QACA5I,aAAa,CAAC,KAAK,EAAE;UAAEuB,SAAS,EAAE;QAAyB,CAAC,EACxDvB,aAAa,CAAC,OAAO,EAAE;UAAE,aAAa,EAAE,IAAI;UAAE4F,KAAK,EAAE;YAAEC,MAAM,EAAE+C,GAAG,CAAClB,UAAU,GAAGkB,GAAG,CAACM,YAAY,GAAG;UAAG;QAAE,CAAC,EACrGN,GAAG,CAACO,iBAAiB,EACrBnJ,aAAa,CAAC,OAAO,EAAE,IAAI,EACvBA,aAAa,CAACwD,YAAY,EAAE;UAAEE,SAAS,EAAEA;QAAU,CAAC,CAAC,CAAC,CAAC,EAC/D1D,aAAa,CAAC,KAAK,EAAE;UAAEuB,SAAS,EAAE;QAAsC,CAAC,EACrEvB,aAAa,CAACpC,QAAQ,EAAE;UAAEwL,IAAI,EAAEJ,cAAc,GAAG,QAAQ,GAAG,KAAK,CAAC;QAAY,CAAC,EAAGK,OAAO,IAAK;UAC1F,IAAIC,eAAe,GAAGN,cAAc,IAChCrE,UAAU,IACVA,UAAU,CAAC4E,cAAc,CAACF,OAAO,CAAC,CAAC,CAAC;UACxC,IAAI,OAAOC,eAAe,KAAK,QAAQ,EAAE;YACrC,OAAQtJ,aAAa,CAACnC,qBAAqB,EAAE;cAAE6E,SAAS,EAAE,CAAC,iCAAiC,CAAC;cAAE8G,OAAO,EAAE;gBAAEC,GAAG,EAAEH;cAAgB,CAAC;cAAEI,MAAM,EAAE,IAAI;cAAEtH,IAAI,EAAEiH;YAAQ,CAAC,CAAC;UACpK;UACA,OAAO,IAAI;QACf,CAAC,CAAC,CAAC;MACf,CAAC,EACD;QACIxF,GAAG,EAAE,MAAM;QACXY,aAAa,EAAE,IAAI,CAACA,aAAa;QACjC2C,OAAO,EAAEV;MACb,CAAC;IAET,CAAC,CAAC;IACF,IAAI+B,qBAAqB,EAAE;MACvB9B,QAAQ,CAACE,IAAI,CAAC;QACVhD,GAAG,EAAE,QAAQ;QACbiD,IAAI,EAAE,QAAQ;QACdC,QAAQ,EAAE,IAAI;QACd4B,MAAM,EAAE,CACJ;UACI9E,GAAG,EAAE,MAAM;UACXuD,OAAO,EAAEtJ;QACb,CAAC,EACD;UACI+F,GAAG,EAAE,MAAM;UACXuD,OAAO,EAAEtJ;QACb,CAAC;MAET,CAAC,CAAC;IACN;IACA,OAAQkC,aAAa,CAACvC,aAAa,EAAE;MAAEwJ,KAAK,EAAE,IAAI,CAACzC,SAAS;MAAE9B,SAAS,EAAE,CAAC,aAAa,CAAC;MAAEkF,QAAQ,EAAEtG,OAAO,CAACsG;IAAS,CAAC,EAClH5H,aAAa,CAACqI,UAAU,EAAE;MAAEZ,MAAM,EAAE,CAACvG,KAAK,CAAC2G,YAAY,IAAI,CAAC3G,KAAK,CAAC4G,QAAQ;MAAEA,QAAQ,EAAE5G,KAAK,CAAC4G,QAAQ;MAAEC,gBAAgB,EAAE,KAAK;MAAE4B,SAAS,EAAE,CAClI;QAAE1B,KAAK,EAAE,QAAQ;QAAED,IAAI,EAAE,CAAC;UAAEC,KAAK,EAAE;QAAS,CAAC;MAAE,CAAC,EAChD;QAAED,IAAI,EAAE,CAAC;UAAE4B,IAAI,EAAEzB,MAAM;UAAE0B,QAAQ,EAAEzB;QAAY,CAAC;MAAE,CAAC,CACtD;MAAEzB,QAAQ,EAAEA;IAAS,CAAC,CAAC,CAAC;EACrC;EACA;AACJ;EACImD,sBAAsBA,CAAA,EAAG;IACrB,IAAI;MAAEC,YAAY;MAAEC;IAAgB,CAAC,GAAG,IAAI,CAAC1I,OAAO,CAACK,OAAO;IAC5D,IAAIoI,YAAY,KAAK,IAAI,IAAIC,eAAe,KAAK,IAAI,EAAE;MAAE;MACrDD,YAAY,GAAGE,SAAS;MACxBD,eAAe,GAAG9F,2BAA2B,CAAC,CAAC;IACnD;IACA,OAAO;MAAE6F,YAAY;MAAEC;IAAgB,CAAC;EAC5C;AACJ;AACA,SAAS9D,iBAAiBA,CAACjE,WAAW,EAAE;EACpC,OAAOA,WAAW,CAACM,IAAI;AAC3B;AAEA,MAAM2H,mBAAmB,CAAC;EACtB9F,WAAWA,CAAC+F,SAAS,EAAEhF,WAAW,EAAEiF,YAAY,EAAE;IAC9C,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAChF,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACiF,YAAY,GAAGA,YAAY;EACpC;EACAb,cAAcA,CAACnH,IAAI,EAAE;IACjB,IAAI;MAAE+C;IAAY,CAAC,GAAG,IAAI;IAC1B,IAAIpH,mBAAmB,CAACoH,WAAW,CAACkF,YAAY,EAAEjI,IAAI,CAAC,EAAE;MACrD,IAAIkI,cAAc,GAAGtM,UAAU,CAACoE,IAAI,CAAC;MACrC,IAAImI,MAAM,GAAGnI,IAAI,CAACoI,OAAO,CAAC,CAAC,GAAGF,cAAc,CAACE,OAAO,CAAC,CAAC;MACtD,IAAID,MAAM,IAAItM,SAAS,CAACkH,WAAW,CAACsF,WAAW,CAAC,IAC5CF,MAAM,GAAGtM,SAAS,CAACkH,WAAW,CAACuF,WAAW,CAAC,EAAE;QAC7C,OAAO,IAAI,CAACC,cAAc,CAACzM,cAAc,CAACqM,MAAM,CAAC,CAAC;MACtD;IACJ;IACA,OAAO,IAAI;EACf;EACA;EACA;EACAK,cAAcA,CAACC,IAAI,EAAEP,cAAc,EAAE;IACjC,IAAI,CAACA,cAAc,EAAE;MACjBA,cAAc,GAAGtM,UAAU,CAAC6M,IAAI,CAAC;IACrC;IACA,OAAO,IAAI,CAACF,cAAc,CAACzM,cAAc,CAAC2M,IAAI,CAACL,OAAO,CAAC,CAAC,GAAGF,cAAc,CAACE,OAAO,CAAC,CAAC,CAAC,CAAC;EACzF;EACA;EACA;EACA;EACAG,cAAcA,CAACG,QAAQ,EAAE;IACrB,IAAI;MAAEX,SAAS;MAAEhF;IAAY,CAAC,GAAG,IAAI;IACrC,IAAI4F,GAAG,GAAGZ,SAAS,CAACa,GAAG,CAACC,MAAM;IAC9B;IACA,IAAIC,YAAY,GAAG,CAACJ,QAAQ,CAACK,YAAY,GAAGlN,SAAS,CAACkH,WAAW,CAACsF,WAAW,CAAC,IAAIxM,SAAS,CAAC,IAAI,CAACmM,YAAY,CAAC;IAC9G,IAAIgB,SAAS;IACb,IAAIC,aAAa;IACjB;IACA;IACA;IACAH,YAAY,GAAGI,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEL,YAAY,CAAC;IACxCA,YAAY,GAAGI,IAAI,CAACE,GAAG,CAACT,GAAG,EAAEG,YAAY,CAAC;IAC1C;IACA;IACAE,SAAS,GAAGE,IAAI,CAACG,KAAK,CAACP,YAAY,CAAC;IACpCE,SAAS,GAAGE,IAAI,CAACE,GAAG,CAACJ,SAAS,EAAEL,GAAG,GAAG,CAAC,CAAC;IACxC;IACA;IACAM,aAAa,GAAGH,YAAY,GAAGE,SAAS;IACxC,OAAOjB,SAAS,CAACuB,IAAI,CAACN,SAAS,CAAC,GAC5BjB,SAAS,CAACwB,SAAS,CAACP,SAAS,CAAC,GAAGC,aAAa;EACtD;AACJ;AAEA,MAAMO,iBAAiB,SAASzO,aAAa,CAAC;EAC1CsG,MAAMA,CAAA,EAAG;IACL,IAAI;MAAEvC,KAAK;MAAEI;IAAQ,CAAC,GAAG,IAAI;IAC7B,IAAI;MAAEK;IAAQ,CAAC,GAAGL,OAAO;IACzB,IAAI;MAAEuK;IAAW,CAAC,GAAG3K,KAAK;IAC1B,OAAQlB,aAAa,CAAC,OAAO,EAAE,IAAI,EAAEkB,KAAK,CAACwC,SAAS,CAACC,GAAG,CAAC,CAACC,QAAQ,EAAEkI,CAAC,KAAK;MACtE,IAAI7J,WAAW,GAAG;QACdE,IAAI,EAAEyB,QAAQ,CAACzB,IAAI;QACnBC,IAAI,EAAEd,OAAO,CAACI,OAAO,CAACW,MAAM,CAACuB,QAAQ,CAACxB,IAAI,CAAC;QAC3CE,IAAI,EAAEhB,OAAO,CAACM;MAClB,CAAC;MACD,OAAQ5B,aAAa,CAAC,IAAI,EAAE;QAAE6D,GAAG,EAAED,QAAQ,CAACC,GAAG;QAAEkI,GAAG,EAAEF,UAAU,CAAC5L,SAAS,CAAC2D,QAAQ,CAACC,GAAG;MAAE,CAAC,EACtF3C,KAAK,CAAC8K,IAAI,IAAKhM,aAAa,CAACiB,gBAAgB,EAAE6C,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,QAAQ,CAAC,CAAE,EAC5E5D,aAAa,CAAC9C,gBAAgB,EAAE;QAAEuF,KAAK,EAAE,IAAI;QAAEC,SAAS,EAAE,CAClD,kBAAkB,EAClB,uBAAuB,EACvB,CAACkB,QAAQ,CAACxC,SAAS,IAAI,wBAAwB,CAClD;QAAEuB,OAAO,EAAE;UACR,WAAW,EAAEiB,QAAQ,CAACnC;QAC1B,CAAC;QAAEQ,WAAW,EAAEA,WAAW;QAAEW,aAAa,EAAE,iBAAiB;QAAEC,eAAe,EAAElB,OAAO,CAACsK,eAAe;QAAEhJ,kBAAkB,EAAEtB,OAAO,CAACuK,kBAAkB;QAAE/I,QAAQ,EAAExB,OAAO,CAACwK,gBAAgB;QAAE9I,WAAW,EAAE1B,OAAO,CAACyK;MAAoB,CAAC,CAAC,CAAC;IACrP,CAAC,CAAC,CAAC;EACP;AACJ;;AAEA;AACA;AACA;AACA,MAAMC,aAAa,SAASlP,aAAa,CAAC;EACtCiH,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACG,SAAS,GAAGvE,SAAS,CAAC,CAAC;IAC5B,IAAI,CAAC4L,UAAU,GAAG,IAAI1N,MAAM,CAAC,CAAC;EAClC;EACAsF,MAAMA,CAAA,EAAG;IACL,IAAI;MAAEvC,KAAK;MAAEI;IAAQ,CAAC,GAAG,IAAI;IAC7B,OAAQtB,aAAa,CAAC,KAAK,EAAE;MAAE+L,GAAG,EAAE,IAAI,CAACvH,SAAS;MAAEjD,SAAS,EAAE;IAAoB,CAAC,EAChFvB,aAAa,CAAC,OAAO,EAAE;MAAE,aAAa,EAAE,IAAI;MAAEuB,SAAS,EAAED,OAAO,CAACiG,KAAK,CAACC,QAAQ,CAAC,OAAO,CAAC;MAAE5B,KAAK,EAAE;QACzFiE,QAAQ,EAAE3I,KAAK,CAACoL,aAAa;QAC7BrE,KAAK,EAAE/G,KAAK,CAACqL,WAAW;QACxB1G,MAAM,EAAE3E,KAAK,CAACsL;MAClB;IAAE,CAAC,EACHtL,KAAK,CAACiI,iBAAiB,CAAC,8DACxBnJ,aAAa,CAAC4L,iBAAiB,EAAE;MAAEC,UAAU,EAAE,IAAI,CAACA,UAAU;MAAEG,IAAI,EAAE9K,KAAK,CAAC8K,IAAI;MAAEtI,SAAS,EAAExC,KAAK,CAACwC;IAAU,CAAC,CAAC,CAAC,CAAC;EAC7H;EACA+I,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACC,YAAY,CAAC,CAAC;EACvB;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACD,YAAY,CAAC,CAAC;EACvB;EACAE,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAAC1L,KAAK,CAAC2L,QAAQ,EAAE;MACrB,IAAI,CAAC3L,KAAK,CAAC2L,QAAQ,CAAC,IAAI,CAAC;IAC7B;EACJ;EACAH,YAAYA,CAAA,EAAG;IACX,IAAI;MAAEpL,OAAO;MAAEJ;IAAM,CAAC,GAAG,IAAI;IAC7B,IAAIA,KAAK,CAAC2L,QAAQ,IACd3L,KAAK,CAACqL,WAAW,KAAK,IAAI,CAAC;IAAA,EAC7B;MACE,IAAIO,MAAM,GAAG,IAAI,CAACtI,SAAS,CAACO,OAAO;MACnC,IAAI+H,MAAM,CAACC,YAAY,EAAE;QAAE;QACvB7L,KAAK,CAAC2L,QAAQ,CAAC,IAAI3C,mBAAmB,CAAC,IAAI9L,aAAa,CAAC,IAAI,CAACoG,SAAS,CAACO,OAAO,EAAEiI,cAAc,CAAC,IAAI,CAACnB,UAAU,CAACoB,UAAU,EAAE/L,KAAK,CAACwC,SAAS,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE,IAAI,CAACxC,KAAK,CAACiE,WAAW,EAAE7D,OAAO,CAACK,OAAO,CAACyI,YAAY,CAAC,CAAC;MACtN;IACJ;EACJ;AACJ;AACA,SAAS4C,cAAcA,CAACE,KAAK,EAAExJ,SAAS,EAAE;EACtC,OAAOA,SAAS,CAACC,GAAG,CAAEC,QAAQ,IAAKsJ,KAAK,CAACtJ,QAAQ,CAACC,GAAG,CAAC,CAAC;AAC3D;AAEA,SAASsJ,cAAcA,CAACC,IAAI,EAAEjF,MAAM,EAAE;EAClC,IAAIkF,SAAS,GAAG,EAAE;EAClB,IAAIvB,CAAC;EACL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3D,MAAM,EAAE2D,CAAC,IAAI,CAAC,EAAE;IAC5BuB,SAAS,CAACxG,IAAI,CAAC,EAAE,CAAC;EACtB;EACA,IAAIuG,IAAI,EAAE;IACN,KAAKtB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,IAAI,CAACnC,MAAM,EAAEa,CAAC,IAAI,CAAC,EAAE;MACjCuB,SAAS,CAACD,IAAI,CAACtB,CAAC,CAAC,CAACwB,GAAG,CAAC,CAACzG,IAAI,CAACuG,IAAI,CAACtB,CAAC,CAAC,CAAC;IACxC;EACJ;EACA,OAAOuB,SAAS;AACpB;AACA,SAASE,qBAAqBA,CAACC,EAAE,EAAErF,MAAM,EAAE;EACvC,IAAIsF,KAAK,GAAG,EAAE;EACd,IAAI,CAACD,EAAE,EAAE;IACL,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3D,MAAM,EAAE2D,CAAC,IAAI,CAAC,EAAE;MAChC2B,KAAK,CAAC3B,CAAC,CAAC,GAAG,IAAI;IACnB;EACJ,CAAC,MACI;IACD,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3D,MAAM,EAAE2D,CAAC,IAAI,CAAC,EAAE;MAChC2B,KAAK,CAAC3B,CAAC,CAAC,GAAG;QACP4B,iBAAiB,EAAEF,EAAE,CAACE,iBAAiB;QACvCC,OAAO,EAAEH,EAAE,CAACG,OAAO;QACnBP,IAAI,EAAE;MACV,CAAC;IACL;IACA,KAAK,IAAIQ,GAAG,IAAIJ,EAAE,CAACJ,IAAI,EAAE;MACrBK,KAAK,CAACG,GAAG,CAACN,GAAG,CAAC,CAACF,IAAI,CAACvG,IAAI,CAAC+G,GAAG,CAAC;IACjC;EACJ;EACA,OAAOH,KAAK;AAChB;AAEA,MAAMI,eAAe,SAAS1Q,aAAa,CAAC;EACxCsG,MAAMA,CAAA,EAAG;IACL,IAAI;MAAEvC;IAAM,CAAC,GAAG,IAAI;IACpB,OAAQlB,aAAa,CAAC3B,iBAAiB,EAAE;MAAEqE,SAAS,EAAE,CAAC,uBAAuB,CAAC;MAAE8G,OAAO,EAAE;QAClFC,GAAG,EAAEvI,KAAK,CAACuI,GAAG;QACdqE,MAAM,EAAE5M,KAAK,CAAC4M;MAClB,CAAC;MAAEC,UAAU,EAAE,IAAI;MAAEC,OAAO,EAAE9M,KAAK,CAAC+M,UAAU,CAAChD,MAAM;MAAEiD,OAAO,EAAEhN,KAAK,CAAC+M,UAAU;MAAEA,UAAU,EAAE/M,KAAK,CAAC+M,UAAU;MAAEE,aAAa,EAAEjN,KAAK,CAACiN,aAAa;MAAEhJ,WAAW,EAAEjE,KAAK,CAACiE,WAAW;MAAEiJ,UAAU,EAAElN,KAAK,CAACkN,UAAU;MAAEC,cAAc,EAAEA,CAAA,KAAMC,iBAAiB,CAACpN,KAAK,CAAC+M,UAAU,EAAE/M,KAAK,CAAC;MAAE6B,gBAAgB,EAAEwL,mBAAmB;MAAEC,UAAU,EAAE;IAAK,CAAC,EAAGjL,YAAY,IAAMvD,aAAa,CAACuD,YAAY,EAAE;MAAEd,KAAK,EAAE,KAAK;MAAEC,SAAS,EAAE,CAAC,6BAA6B,EAAE,WAAW;IAAE,CAAC,CAAE,CAAC;EACld;AACJ;AACA,SAAS6L,mBAAmBA,CAACrN,KAAK,EAAE;EAChC,OAAOA,KAAK,CAACuN,SAAS;AAC1B;;AAEA;AACA,SAASC,gBAAgBA,CAACC,SAAS,EAAEC,WAAW,EAAEC,WAAW,EAAE;EAC3D,IAAIC,SAAS,GAAG,IAAIxQ,YAAY,CAAC,CAAC;EAClC,IAAIsQ,WAAW,IAAI,IAAI,EAAE;IACrBE,SAAS,CAACF,WAAW,GAAGA,WAAW;EACvC;EACA,IAAIC,WAAW,IAAI,IAAI,EAAE;IACrBC,SAAS,CAACD,WAAW,GAAGA,WAAW;EACvC;EACA,IAAIE,aAAa,GAAGD,SAAS,CAACE,OAAO,CAACL,SAAS,CAAC;EAChD,IAAIM,YAAY,GAAG1Q,wBAAwB,CAACwQ,aAAa,CAAC;EAC1D,IAAIG,GAAG,GAAGC,QAAQ,CAACL,SAAS,CAAC;EAC7BI,GAAG,GAAGE,UAAU,CAACF,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;EAC1B,IAAIG,QAAQ,GAAGC,UAAU,CAACJ,GAAG,CAAC;EAC9B,OAAO;IAAEG,QAAQ;IAAEJ;EAAa,CAAC;AACrC;AACA,SAASE,QAAQA,CAACL,SAAS,EAAE;EACzB,MAAM;IAAES;EAAe,CAAC,GAAGT,SAAS;EACpC,MAAMU,SAAS,GAAGC,SAAS,CAAC,CAACvN,KAAK,EAAEwN,OAAO,KAAKxN,KAAK,GAAG,GAAG,GAAGwN,OAAO,EAAE,CAACxN,KAAK,EAAEwN,OAAO,KAAK;IACvF,IAAIC,YAAY,GAAGC,iBAAiB,CAACd,SAAS,EAAE5M,KAAK,EAAEwN,OAAO,CAAC;IAC/D,IAAIG,YAAY,GAAGC,UAAU,CAACH,YAAY,EAAEH,SAAS,CAAC;IACtD,IAAIO,KAAK,GAAGR,cAAc,CAACrN,KAAK,CAAC,CAACwN,OAAO,CAAC;IAC1C,OAAO,CACH5L,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEgM,KAAK,CAAC,EAAE;MAAEC,cAAc,EAAEH,YAAY,CAAC,CAAC;IAAE,CAAC,CAAC,EAC5EE,KAAK,CAACE,SAAS,GAAGJ,YAAY,CAAC,CAAC,CAAC,CAAE;IAAA,CACtC;EACL,CAAC,CAAC;EACF,OAAOC,UAAU,CAACP,cAAc,CAACtE,MAAM,GACjC;IAAE/I,KAAK,EAAE,CAAC;IAAEgO,YAAY,EAAE,CAAC;IAAEC,UAAU,EAAEZ,cAAc,CAAC,CAAC,CAAC,CAACtE;EAAO,CAAC,GACnE,IAAI,EAAEuE,SAAS,CAAC,CAAC,CAAC,CAAC;AAC7B;AACA,SAASM,UAAUA,CAACH,YAAY,EAAEH,SAAS,EAAE;EACzC,IAAI,CAACG,YAAY,EAAE;IACf,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;EAClB;EACA,IAAI;IAAEzN,KAAK;IAAEgO,YAAY;IAAEC;EAAW,CAAC,GAAGR,YAAY;EACtD,IAAID,OAAO,GAAGQ,YAAY;EAC1B,IAAIE,KAAK,GAAG,EAAE;EACd,OAAOV,OAAO,GAAGS,UAAU,EAAE;IACzBC,KAAK,CAACvJ,IAAI,CAAC2I,SAAS,CAACtN,KAAK,EAAEwN,OAAO,CAAC,CAAC;IACrCA,OAAO,IAAI,CAAC;EAChB;EACAU,KAAK,CAACC,IAAI,CAACC,gBAAgB,CAAC;EAC5B,OAAO,CACHF,KAAK,CAACzM,GAAG,CAAC4M,WAAW,CAAC,EACtBH,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAE;EAAA,CAChB;AACL;AACA,SAASE,gBAAgBA,CAACE,CAAC,EAAEC,CAAC,EAAE;EAC5B,OAAOA,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC;AACtB;AACA,SAASD,WAAWA,CAACC,CAAC,EAAE;EACpB,OAAOA,CAAC,CAAC,CAAC,CAAC;AACf;AACA,SAASZ,iBAAiBA,CAACd,SAAS,EAAE4B,YAAY,EAAEC,cAAc,EAAE;EAChE,IAAI;IAAEC,WAAW;IAAErB;EAAe,CAAC,GAAGT,SAAS;EAC/C,IAAI+B,YAAY,GAAGtB,cAAc,CAACmB,YAAY,CAAC,CAACC,cAAc,CAAC;EAC/D,IAAIG,YAAY,GAAGF,WAAW,CAACF,YAAY,CAAC,GAAGG,YAAY,CAACZ,SAAS;EACrE,IAAIc,QAAQ,GAAGH,WAAW,CAAC3F,MAAM;EACjC,IAAI/I,KAAK,GAAGwO,YAAY;EACxB;EACA,OAAOxO,KAAK,GAAG6O,QAAQ,IAAIH,WAAW,CAAC1O,KAAK,CAAC,GAAG4O,YAAY,EAAE5O,KAAK,IAAI,CAAC,CACpE,CAAC,CAAC;EACN,OAAOA,KAAK,GAAG6O,QAAQ,EAAE7O,KAAK,IAAI,CAAC,EAAE;IACjC,IAAI8O,OAAO,GAAGzB,cAAc,CAACrN,KAAK,CAAC;IACnC,IAAI6N,KAAK;IACT,IAAIkB,WAAW,GAAGzS,YAAY,CAACwS,OAAO,EAAEH,YAAY,CAACjH,IAAI,CAACrE,KAAK,EAAE9G,eAAe,CAAC;IACjF,IAAIyR,YAAY,GAAGe,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IACpD,IAAId,UAAU,GAAGD,YAAY;IAC7B;IAAQ;IACR,CAACH,KAAK,GAAGiB,OAAO,CAACb,UAAU,CAAC;IAAK;IAC7BJ,KAAK,CAACnG,IAAI,CAACrE,KAAK,GAAGsL,YAAY,CAACjH,IAAI,CAACpE,GAAG,EAAE;MAC1C2K,UAAU,IAAI,CAAC;IACnB;IACA,IAAID,YAAY,GAAGC,UAAU,EAAE;MAC3B,OAAO;QAAEjO,KAAK;QAAEgO,YAAY;QAAEC;MAAW,CAAC;IAC9C;EACJ;EACA,OAAO,IAAI;AACf;AACA,SAASf,UAAUA,CAAC8B,aAAa,EAAEC,cAAc,EAAE;EAC/C,MAAMC,WAAW,GAAG3B,SAAS,CAAC,CAAC4B,IAAI,EAAEC,UAAU,EAAEC,aAAa,KAAK7S,aAAa,CAAC2S,IAAI,CAAC,EAAE,CAACA,IAAI,EAAEC,UAAU,EAAEC,aAAa,KAAK;IACzH,IAAI;MAAEvB,cAAc;MAAEC;IAAU,CAAC,GAAGoB,IAAI;IACxC,IAAIG,YAAY,GAAGvB,SAAS,GAAGsB,aAAa;IAC5C,IAAIE,iBAAiB,GAAGxB,SAAS,GAAGuB,YAAY;IAChD,IAAIE,QAAQ;IACZ,IAAIC,WAAW,GAAG,EAAE;IACpB,IAAI,CAAC3B,cAAc,CAAC/E,MAAM,EAAE;MACxByG,QAAQ,GAAGP,cAAc;IAC7B,CAAC,MACI;MACD,KAAK,IAAIS,SAAS,IAAI5B,cAAc,EAAE;QAClC,IAAI0B,QAAQ,KAAKzH,SAAS,EAAE;UACxB,IAAI4H,GAAG,GAAGT,WAAW,CAACQ,SAAS,EAAEN,UAAU,EAAEE,YAAY,CAAC;UAC1DE,QAAQ,GAAGG,GAAG,CAAC,CAAC,CAAC;UACjBF,WAAW,CAAC9K,IAAI,CAACgL,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,MACI;UACD,IAAIA,GAAG,GAAGT,WAAW,CAACQ,SAAS,EAAEF,QAAQ,EAAE,CAAC,CAAC;UAC7CC,WAAW,CAAC9K,IAAI,CAACgL,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5B;MACJ;IACJ;IACA,IAAIC,YAAY,GAAG,CAACJ,QAAQ,GAAGJ,UAAU,IAAIG,iBAAiB;IAC9D,OAAO,CAACC,QAAQ,GAAGI,YAAY,EAAEhO,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEsN,IAAI,CAAC,EAAE;MAAEpB,SAAS,EAAE6B,YAAY;MAAE9B,cAAc,EAAE2B;IAAY,CAAC,CAAC,CAAC;EACtI,CAAC,CAAC;EACF,OAAOT,aAAa,CAACvN,GAAG,CAAE0N,IAAI,IAAKD,WAAW,CAACC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAClE;AACA;AACA,SAAS/B,UAAUA,CAAC4B,aAAa,EAAE;EAC/B,IAAIa,KAAK,GAAG,EAAE;EACd,MAAMC,WAAW,GAAGvC,SAAS,CAAC,CAAC4B,IAAI,EAAEY,UAAU,EAAEC,UAAU,KAAKxT,aAAa,CAAC2S,IAAI,CAAC,EAAE,CAACA,IAAI,EAAEY,UAAU,EAAEC,UAAU,KAAK;IACnH,IAAIC,IAAI,GAAGrO,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEsN,IAAI,CAAC,EAAE;MAAEY,UAAU;MAC1DC,UAAU;MAAEE,YAAY,EAAE;IAAE,CAAC,CAAC;IAClCL,KAAK,CAAClL,IAAI,CAACsL,IAAI,CAAC;IAChB,OAAQA,IAAI,CAACC,YAAY,GAAGC,YAAY,CAAChB,IAAI,CAACrB,cAAc,EAAEiC,UAAU,GAAGZ,IAAI,CAACpB,SAAS,EAAEiC,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC;EAClH,CAAC,CAAC;EACF,SAASG,YAAYA,CAACC,KAAK,EAAEL,UAAU,EAAEC,UAAU,EAAE;IACjD,IAAIE,YAAY,GAAG,CAAC;IACpB,KAAK,IAAIf,IAAI,IAAIiB,KAAK,EAAE;MACpBF,YAAY,GAAG9G,IAAI,CAACC,GAAG,CAACyG,WAAW,CAACX,IAAI,EAAEY,UAAU,EAAEC,UAAU,CAAC,EAAEE,YAAY,CAAC;IACpF;IACA,OAAOA,YAAY;EACvB;EACAC,YAAY,CAACnB,aAAa,EAAE,CAAC,EAAE,CAAC,CAAC;EACjC,OAAOa,KAAK,CAAC,CAAC;AAClB;AACA;AACA,SAAStC,SAASA,CAAC8C,OAAO,EAAEC,QAAQ,EAAE;EAClC,MAAMC,KAAK,GAAG,CAAC,CAAC;EAChB,OAAO,CAAC,GAAGC,IAAI,KAAK;IAChB,IAAI7O,GAAG,GAAG0O,OAAO,CAAC,GAAGG,IAAI,CAAC;IAC1B,OAAQ7O,GAAG,IAAI4O,KAAK,GACdA,KAAK,CAAC5O,GAAG,CAAC,GACT4O,KAAK,CAAC5O,GAAG,CAAC,GAAG2O,QAAQ,CAAC,GAAGE,IAAI,CAAE;EAC1C,CAAC;AACL;AAEA,SAASC,iBAAiBA,CAACvF,IAAI,EAAEwF,OAAO,EAAEjO,UAAU,GAAG,IAAI,EAAEkO,cAAc,GAAG,CAAC,EAAE;EAC7E,IAAIC,OAAO,GAAG,EAAE;EAChB,IAAInO,UAAU,EAAE;IACZ,KAAK,IAAImH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,IAAI,CAACnC,MAAM,EAAEa,CAAC,IAAI,CAAC,EAAE;MACrC,IAAI8B,GAAG,GAAGR,IAAI,CAACtB,CAAC,CAAC;MACjB,IAAIiH,SAAS,GAAGpO,UAAU,CAACiG,cAAc,CAACgD,GAAG,CAACrI,KAAK,EAAEqN,OAAO,CAAC;MAC7D,IAAII,OAAO,GAAG1H,IAAI,CAACC,GAAG,CAACwH,SAAS,IAAIF,cAAc,IAAI,CAAC,CAAC;MAAE;MAC1DlO,UAAU,CAACiG,cAAc,CAACgD,GAAG,CAACpI,GAAG,EAAEoN,OAAO,CAAC,CAAC;MAC5CE,OAAO,CAACjM,IAAI,CAAC;QACTtB,KAAK,EAAE+F,IAAI,CAAC2H,KAAK,CAACF,SAAS,CAAC;QAC5BvN,GAAG,EAAE8F,IAAI,CAAC2H,KAAK,CAACD,OAAO,CAAC,CAAE;MAC9B,CAAC,CAAC;IACN;EACJ;EACA,OAAOF,OAAO;AAClB;AACA,SAASI,sBAAsBA,CAAC9F,IAAI,EAAE+F,UAAU;AAAE;AAClDC,gBAAgB,EAAEC,aAAa,EAAE;EAC7B,IAAI1E,SAAS,GAAG,EAAE;EAClB,IAAI2E,QAAQ,GAAG,EAAE,CAAC,CAAC;EACnB,KAAK,IAAIxH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,IAAI,CAACnC,MAAM,EAAEa,CAAC,IAAI,CAAC,EAAE;IACrC,IAAIgH,OAAO,GAAGK,UAAU,CAACrH,CAAC,CAAC;IAC3B,IAAIgH,OAAO,EAAE;MACTnE,SAAS,CAAC9H,IAAI,CAAC;QACX0M,KAAK,EAAEzH,CAAC;QACRmE,SAAS,EAAE,CAAC;QACZrG,IAAI,EAAEkJ;MACV,CAAC,CAAC;IACN,CAAC,MACI;MACDQ,QAAQ,CAACzM,IAAI,CAACuG,IAAI,CAACtB,CAAC,CAAC,CAAC;IAC1B;EACJ;EACA,IAAI;IAAEuD,QAAQ;IAAEJ;EAAa,CAAC,GAAGP,gBAAgB,CAACC,SAAS,EAAEyE,gBAAgB,EAAEC,aAAa,CAAC;EAC7F,IAAIG,aAAa,GAAG,EAAE;EACtB,KAAK,IAAIC,OAAO,IAAIpE,QAAQ,EAAE;IAC1BmE,aAAa,CAAC3M,IAAI,CAAC;MACf+G,GAAG,EAAER,IAAI,CAACqG,OAAO,CAACF,KAAK,CAAC;MACxBpB,IAAI,EAAEsB;IACV,CAAC,CAAC;EACN;EACA,KAAK,IAAIC,OAAO,IAAIJ,QAAQ,EAAE;IAC1BE,aAAa,CAAC3M,IAAI,CAAC;MAAE+G,GAAG,EAAE8F,OAAO;MAAEvB,IAAI,EAAE;IAAK,CAAC,CAAC;EACpD;EACA,OAAO;IAAEqB,aAAa;IAAEvE;EAAa,CAAC;AAC1C;AAEA,MAAM0E,mBAAmB,GAAG3W,eAAe,CAAC;EACxC6D,IAAI,EAAE,SAAS;EACfC,MAAM,EAAE,SAAS;EACjBE,QAAQ,EAAE;AACd,CAAC,CAAC;AACF,MAAM4S,YAAY,SAASzW,aAAa,CAAC;EACrCsG,MAAMA,CAAA,EAAG;IACL,OAAQzD,aAAa,CAACrB,aAAa,EAAEmF,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC7C,KAAK,EAAE;MAAEwB,SAAS,EAAE,CACxE,mBAAmB,EACnB,YAAY,EACZ,IAAI,CAACxB,KAAK,CAAC2S,OAAO,IAAI,yBAAyB,CAClD;MAAEC,iBAAiB,EAAEH;IAAoB,CAAC,CAAC,CAAC;EACrD;AACJ;AAEA,MAAMI,OAAO,SAAS5W,aAAa,CAAC;EAChCiH,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACxF,aAAa,GAAGD,OAAO,CAACC,aAAa,CAAC;EAC/C;EACA;EACA4E,MAAMA,CAAA,EAAG;IACL,IAAI;MAAEvC,KAAK;MAAEI;IAAQ,CAAC,GAAG,IAAI;IAC7B,IAAI;MAAEK;IAAQ,CAAC,GAAGL,OAAO;IACzB,IAAI0S,cAAc,GAAGrS,OAAO,CAACsS,YAAY;IACzC,IAAIC,UAAU;IAAG;IACfhT,KAAK,CAACiT,SAAS,IAAIjT,KAAK,CAACiT,SAAS,CAAC/G,IAAI,IACpClM,KAAK,CAACkT,WAAW,IAAIlT,KAAK,CAACkT,WAAW,CAAChH,IAAK,IAC5C4G,cAAc,IAAI9S,KAAK,CAACmT,iBAAkB,IAC3C,EAAE;IACN,IAAIC,4BAA4B;IAAG;IACjCpT,KAAK,CAACiT,SAAS,IAAIjT,KAAK,CAACiT,SAAS,CAACzG,iBAAiB,IACjDxM,KAAK,CAACkT,WAAW,IAAIlT,KAAK,CAACkT,WAAW,CAAC1G,iBAAkB,IAC1D,CAAC,CAAC;IACN,IAAI6G,YAAY,GAAG,IAAI,CAAC1V,aAAa,CAACqC,KAAK,CAACsT,WAAW,EAAE7S,OAAO,CAAC8S,UAAU,CAAC;IAC5E,OAAQzU,aAAa,CAAClB,gBAAgB,EAAE;MAAE2D,KAAK,EAAE,IAAI;MAAEwE,KAAK,EAAE/F,KAAK,CAAC+F,KAAK;MAAEvE,SAAS,EAAE,CAC9E,iBAAiB,EACjB,IAAIxB,KAAK,CAACwT,eAAe,IAAI,EAAE,CAAC,CACnC;MAAE/R,OAAO,EAAEmB,MAAM,CAACC,MAAM,CAAC;QAAEuD,IAAI,EAAE;MAAW,CAAC,EAAEpG,KAAK,CAACyT,cAAc,CAAC;MAAEvS,IAAI,EAAElB,KAAK,CAACkB,IAAI;MAAE+C,WAAW,EAAEjE,KAAK,CAACiE,WAAW;MAAEiJ,UAAU,EAAElN,KAAK,CAACkN,UAAU;MAAEwG,gBAAgB,EAAE1T,KAAK,CAAC0T;IAAiB,CAAC,EAAGrR,YAAY,IAAMvD,aAAa,CAAC,KAAK,EAAE;MAAEuB,SAAS,EAAE;IAAwB,CAAC,EACjRvB,aAAa,CAAC,KAAK,EAAE;MAAEuB,SAAS,EAAE;IAAqB,CAAC,EACpD,IAAI,CAACsT,cAAc,CAAC3T,KAAK,CAAC4T,gBAAgB,EAAE,cAAc,CAAC,EAC3D,IAAI,CAACD,cAAc,CAAC3T,KAAK,CAAC6T,WAAW,EAAE,UAAU,CAAC,EAClD,IAAI,CAACF,cAAc,CAAC3T,KAAK,CAACmT,iBAAiB,EAAE,WAAW,CAAC,CAAC,EAC9DrU,aAAa,CAAC,KAAK,EAAE;MAAEuB,SAAS,EAAE;IAAyB,CAAC,EAAE,IAAI,CAACyT,YAAY,CAACT,YAAY,EAAED,4BAA4B,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EACjJtU,aAAa,CAAC,KAAK,EAAE;MAAEuB,SAAS,EAAE;IAAyB,CAAC,EAAE,IAAI,CAACyT,YAAY,CAACd,UAAU,EAAE,CAAC,CAAC,EAAEvM,OAAO,CAACzG,KAAK,CAACiT,SAAS,CAAC,EAAExM,OAAO,CAACzG,KAAK,CAACkT,WAAW,CAAC,EAAEzM,OAAO,CAACqM,cAAc,CAAC,EAAE,QAAQ,CAAC,CAAC,EACzLhU,aAAa,CAAC,KAAK,EAAE;MAAEuB,SAAS,EAAE;IAAsC,CAAC,EAAE,IAAI,CAAC0T,kBAAkB,CAAC/T,KAAK,CAACgU,gBAAgB,CAAC,CAAC,EAC3HnW,uBAAuB,CAAC4C,OAAO,CAAC,IAAK3B,aAAa,CAACuD,YAAY,EAAE;MAAEd,KAAK,EAAE,KAAK;MAAEC,SAAS,EAAE,CAAC,sBAAsB;IAAE,CAAC,CAAE,CAAE,CAAC;EACnI;EACAsS,YAAYA,CAACT,YAAY,EAAEY,cAAc,EAAEC,UAAU,EAAEC,UAAU,EAAEC,eAAe,EAAEC,SAAS,EAAE;IAC3F,IAAI;MAAErU;IAAM,CAAC,GAAG,IAAI;IACpB,IAAIA,KAAK,CAAC4G,QAAQ,EAAE;MAChB,OAAOwG,iBAAiB,CAACiG,YAAY,EAAErT,KAAK,CAAC;IACjD;IACA,OAAO,IAAI,CAACsU,sBAAsB,CAACjB,YAAY,EAAEY,cAAc,EAAEC,UAAU,EAAEC,UAAU,EAAEC,eAAe,EAAEC,SAAS,CAAC;EACxH;EACAC,sBAAsBA,CAACpI,IAAI;EAAE;EAC7B+H,cAAc,EAAEC,UAAU,EAAEC,UAAU,EAAEC,eAAe,EAAEC,SAAS,EAAE;IAChE,IAAI;MAAElC,aAAa;MAAEoC,gBAAgB;MAAErC,gBAAgB;MAAEP;IAAe,CAAC,GAAG,IAAI,CAACvR,OAAO,CAACK,OAAO;IAChG,IAAI;MAAES,IAAI;MAAEuC,UAAU;MAAE+Q,cAAc;MAAEtH,UAAU;MAAE/E;IAAQ,CAAC,GAAG,IAAI,CAACnI,KAAK;IAC1E,IAAIyU,QAAQ,GAAGP,UAAU,IAAIC,UAAU,IAAIC,eAAe;IAC1D,IAAInC,UAAU,GAAGR,iBAAiB,CAACvF,IAAI,EAAEhL,IAAI,EAAEuC,UAAU,EAAEkO,cAAc,CAAC;IAC1E,IAAI;MAAEW,aAAa;MAAEvE;IAAa,CAAC,GAAGiE,sBAAsB,CAAC9F,IAAI,EAAE+F,UAAU,EAAEC,gBAAgB,EAAEC,aAAa,CAAC;IAC/G,OAAQrT,aAAa,CAACE,QAAQ,EAAE,IAAI,EAChC,IAAI,CAAC0V,kBAAkB,CAAC3G,YAAY,EAAE7B,IAAI,CAAC,EAC3CoG,aAAa,CAAC7P,GAAG,CAAEkS,YAAY,IAAK;MAChC,IAAI;QAAEjI,GAAG;QAAEuE;MAAK,CAAC,GAAG0D,YAAY;MAChC,IAAIC,UAAU,GAAGlI,GAAG,CAACmI,UAAU,CAACC,QAAQ,CAACF,UAAU;MACnD,IAAIG,SAAS,GAAGN,QAAQ,IAAIhO,OAAO,CAAC,CAACwN,cAAc,CAACW,UAAU,CAAC,IAAI3D,IAAI,CAAC;MACxE,IAAI+D,MAAM,GAAGC,gBAAgB,CAAChE,IAAI,IAAIA,IAAI,CAACvI,IAAI,CAAC;MAChD,IAAIwM,MAAM,GAAI,CAACT,QAAQ,IAAIxD,IAAI,GAAI,IAAI,CAACkE,gBAAgB,CAAClE,IAAI,CAAC,GAAG;QAAEmE,IAAI,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAE,CAAC;MACtF,IAAIC,OAAO,GAAG7O,OAAO,CAACwK,IAAI,CAAC,IAAIA,IAAI,CAACC,YAAY,GAAG,CAAC;MACpD,IAAIyB,OAAO,GAAGlM,OAAO,CAACwK,IAAI,CAAC,IAAKA,IAAI,CAACvI,IAAI,CAACpE,GAAG,GAAG2M,IAAI,CAACvI,IAAI,CAACrE,KAAK,GAAIkQ,gBAAgB,CAAC,CAAC;MACrF,OAAQzV,aAAa,CAAC,KAAK,EAAE;QAAEuB,SAAS,EAAE,2BAA2B,IAC5DiV,OAAO,GAAG,kCAAkC,GAAG,EAAE,CAAC;QAAE3S,GAAG,EAAE0R,SAAS,IAAIO,UAAU;QAAElQ,KAAK,EAAE9B,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC;UAAE0S,UAAU,EAAER,SAAS,GAAG,EAAE,GAAG;QAAS,CAAC,EAAEC,MAAM,CAAC,EAAEE,MAAM;MAAE,CAAC,EACvLpW,aAAa,CAAC4T,YAAY,EAAE9P,MAAM,CAACC,MAAM,CAAC;QAAE6J,GAAG,EAAEA,GAAG;QAAEwH,UAAU,EAAEA,UAAU;QAAEC,UAAU,EAAEA,UAAU;QAAEC,eAAe,EAAEA,eAAe;QAAEoB,UAAU,EAAEZ,UAAU,KAAKJ,cAAc;QAAE7B,OAAO,EAAEA;MAAQ,CAAC,EAAE7U,UAAU,CAAC4O,GAAG,EAAEQ,UAAU,EAAE/E,OAAO,CAAC,CAAC,CAAC,CAAC;IACtP,CAAC,CAAC,CAAC;EACX;EACA;EACAuM,kBAAkBA,CAAC3G,YAAY,EAAE7B,IAAI,EAAE;IACnC,IAAI;MAAEe,aAAa;MAAEhJ,WAAW;MAAEiJ,UAAU;MAAE/E,OAAO;MAAEqM,cAAc;MAAEvB,SAAS;MAAEC;IAAY,CAAC,GAAG,IAAI,CAAClT,KAAK;IAC5G,OAAQlB,aAAa,CAACE,QAAQ,EAAE,IAAI,EAAE+O,YAAY,CAACtL,GAAG,CAAEgT,WAAW,IAAK;MACpE,IAAIC,WAAW,GAAGT,gBAAgB,CAACQ,WAAW,CAAC/M,IAAI,CAAC;MACpD,IAAIqE,UAAU,GAAG4I,sBAAsB,CAACF,WAAW,CAAC3F,OAAO,EAAE5D,IAAI,CAAC;MAClE,OAAQpN,aAAa,CAAC6N,eAAe,EAAE;QAAEhK,GAAG,EAAE5E,cAAc,CAACC,uBAAuB,CAAC+O,UAAU,CAAC,CAAC;QAAEA,UAAU,EAAEA,UAAU;QAAExE,GAAG,EAAEmN,WAAW,CAACnN,GAAG;QAAEqE,MAAM,EAAE8I,WAAW,CAAC9I,MAAM;QAAEK,aAAa,EAAEA,aAAa;QAAEhJ,WAAW,EAAEA,WAAW;QAAEiJ,UAAU,EAAEA,UAAU;QAAE/E,OAAO,EAAEA,OAAO;QAAEqM,cAAc,EAAEA,cAAc;QAAEvB,SAAS,EAAEA,SAAS;QAAEC,WAAW,EAAEA;MAAY,CAAC,CAAC;IACpW,CAAC,CAAC,CAAC;EACP;EACAS,cAAcA,CAACzH,IAAI,EAAE0J,QAAQ,EAAE;IAC3B,IAAI;MAAE5V,KAAK;MAAEI;IAAQ,CAAC,GAAG,IAAI;IAC7B,IAAI6R,UAAU,GAAGR,iBAAiB,CAACvF,IAAI,EAAElM,KAAK,CAACkB,IAAI,EAAElB,KAAK,CAACyD,UAAU,EAAErD,OAAO,CAACK,OAAO,CAACkR,cAAc,CAAC,CAAC,CAAC;IACxG,IAAIkE,QAAQ,GAAG5D,UAAU,CAACxP,GAAG,CAAC,CAACmP,OAAO,EAAEhH,CAAC,KAAK;MAC1C,IAAI8B,GAAG,GAAGR,IAAI,CAACtB,CAAC,CAAC;MACjB,OAAQ9L,aAAa,CAAC,KAAK,EAAE;QAAE6D,GAAG,EAAE1E,kBAAkB,CAACyO,GAAG,CAACmI,UAAU,CAAC;QAAExU,SAAS,EAAE,wBAAwB;QAAEqE,KAAK,EAAEuQ,gBAAgB,CAACrD,OAAO;MAAE,CAAC,EAAEgE,QAAQ,KAAK,UAAU,GACpK9W,aAAa,CAACZ,OAAO,EAAE0E,MAAM,CAACC,MAAM,CAAC;QAAE6J,GAAG,EAAEA;MAAI,CAAC,EAAE5O,UAAU,CAAC4O,GAAG,EAAE1M,KAAK,CAACkN,UAAU,EAAElN,KAAK,CAACmI,OAAO,CAAC,CAAC,CAAC,GACrGhK,UAAU,CAACyX,QAAQ,CAAC,CAAC;IAC7B,CAAC,CAAC;IACF,OAAO9W,aAAa,CAACE,QAAQ,EAAE,IAAI,EAAE6W,QAAQ,CAAC;EAClD;EACA9B,kBAAkBA,CAAC7H,IAAI,EAAE;IACrB,IAAI;MAAEzI,UAAU;MAAEvC;IAAK,CAAC,GAAG,IAAI,CAAClB,KAAK;IACrC,IAAI,CAACyD,UAAU,EAAE;MACb,OAAO,IAAI;IACf;IACA,OAAOyI,IAAI,CAACzJ,GAAG,CAAC,CAACiK,GAAG,EAAE9B,CAAC,KAAM9L,aAAa,CAACnC;IAC3C;IAAA,EACE;MACE;MACAgG,GAAG,EAAEiI,CAAC;MAAEpJ,SAAS,EAAE,CAAC,gCAAgC,CAAC;MAAE8G,OAAO,EAAE;QAC5DC,GAAG,EAAE9E,UAAU,CAACiG,cAAc,CAACgD,GAAG,CAACrI,KAAK,EAAEnD,IAAI;MAClD,CAAC;MAAEsH,MAAM,EAAE,KAAK;MAAEtH,IAAI,EAAEA;IAAK,CAAC,CAAE,CAAC;EACzC;EACAiU,gBAAgBA,CAACW,UAAU,EAAE;IACzB,IAAI;MAAEC,KAAK;MAAEtV;IAAQ,CAAC,GAAG,IAAI,CAACL,OAAO;IACrC,IAAI4V,aAAa,GAAGvV,OAAO,CAACwV,gBAAgB;IAC5C,IAAIC,SAAS,GAAGJ,UAAU,CAAC/E,UAAU,CAAC,CAAC;IACvC,IAAIoF,QAAQ,GAAGL,UAAU,CAAC/E,UAAU,GAAG+E,UAAU,CAAC/G,SAAS,CAAC,CAAC;IAC7D,IAAIqG,IAAI,CAAC,CAAC;IACV,IAAIC,KAAK,CAAC,CAAC;IACX,IAAIW,aAAa,EAAE;MACf;MACAG,QAAQ,GAAG/L,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE4L,SAAS,GAAG,CAACC,QAAQ,GAAGD,SAAS,IAAI,CAAC,CAAC;IAClE;IACA,IAAIH,KAAK,EAAE;MACPX,IAAI,GAAG,CAAC,GAAGe,QAAQ;MACnBd,KAAK,GAAGa,SAAS;IACrB,CAAC,MACI;MACDd,IAAI,GAAGc,SAAS;MAChBb,KAAK,GAAG,CAAC,GAAGc,QAAQ;IACxB;IACA,IAAInW,KAAK,GAAG;MACRoW,MAAM,EAAEN,UAAU,CAAC9E,UAAU,GAAG,CAAC;MACjCoE,IAAI,EAAEA,IAAI,GAAG,GAAG,GAAG,GAAG;MACtBC,KAAK,EAAEA,KAAK,GAAG,GAAG,GAAG;IACzB,CAAC;IACD,IAAIW,aAAa,IAAI,CAACF,UAAU,CAAC5E,YAAY,EAAE;MAC3C;MACAlR,KAAK,CAAC+V,KAAK,GAAG,YAAY,GAAG,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAC1D;IACA,OAAO/V,KAAK;EAChB;AACJ;AACA,SAASoN,iBAAiBA,CAACiG,YAAY,EAAE;EAAEnG,UAAU;EAAE/E,OAAO;EAAEqM,cAAc;EAAEvB,SAAS;EAAEC;AAAY,CAAC,EAAE;EACtG,IAAImD,eAAe,GAAG,CAACpD,SAAS,GAAGA,SAAS,CAACzG,iBAAiB,GAAG,IAAI,MAChE0G,WAAW,GAAGA,WAAW,CAAC1G,iBAAiB,GAAG,IAAI,CAAC,IACpD,CAAC,CAAC;EACN,OAAQ1N,aAAa,CAACE,QAAQ,EAAE,IAAI,EAAEqU,YAAY,CAAC5Q,GAAG,CAAEiK,GAAG,IAAK;IAC5D,IAAIkI,UAAU,GAAGlI,GAAG,CAACmI,UAAU,CAACC,QAAQ,CAACF,UAAU;IACnD,OAAQ9V,aAAa,CAAC,KAAK,EAAE;MAAE6D,GAAG,EAAEiS,UAAU;MAAElQ,KAAK,EAAE;QAAE6Q,UAAU,EAAEc,eAAe,CAACzB,UAAU,CAAC,GAAG,QAAQ,GAAG;MAAG;IAAE,CAAC,EAChH9V,aAAa,CAAC4T,YAAY,EAAE9P,MAAM,CAACC,MAAM,CAAC;MAAE6J,GAAG,EAAEA,GAAG;MAAEwH,UAAU,EAAE,KAAK;MAAEC,UAAU,EAAE,KAAK;MAAEC,eAAe,EAAE,KAAK;MAAEoB,UAAU,EAAEZ,UAAU,KAAKJ,cAAc;MAAE7B,OAAO,EAAE;IAAM,CAAC,EAAE7U,UAAU,CAAC4O,GAAG,EAAEQ,UAAU,EAAE/E,OAAO,CAAC,CAAC,CAAC,CAAC;EAChO,CAAC,CAAC,CAAC;AACP;AACA,SAAS8M,gBAAgBA,CAAChD,UAAU,EAAE;EAClC,IAAI,CAACA,UAAU,EAAE;IACb,OAAO;MAAE1J,GAAG,EAAE,EAAE;MAAEqE,MAAM,EAAE;IAAG,CAAC;EAClC;EACA,OAAO;IACHrE,GAAG,EAAE0J,UAAU,CAAC5N,KAAK;IACrBuI,MAAM,EAAE,CAACqF,UAAU,CAAC3N;EACxB,CAAC;AACL;AACA,SAASqR,sBAAsBA,CAACW,UAAU,EAAEtJ,OAAO,EAAE;EACjD,OAAOsJ,UAAU,CAAC7T,GAAG,CAAE8T,QAAQ,IAAKvJ,OAAO,CAACuJ,QAAQ,CAAClE,KAAK,CAAC,CAAC;AAChE;AAEA,MAAMmE,eAAe,SAASva,aAAa,CAAC;EACxCiH,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACsT,gBAAgB,GAAG/Y,OAAO,CAACuO,cAAc,CAAC;IAC/C,IAAI,CAACyK,gBAAgB,GAAGhZ,OAAO,CAACuO,cAAc,CAAC;IAC/C,IAAI,CAAC0K,qBAAqB,GAAGjZ,OAAO,CAACuO,cAAc,CAAC;IACpD,IAAI,CAAC2K,qBAAqB,GAAGlZ,OAAO,CAACuO,cAAc,CAAC;IACpD,IAAI,CAAC4K,sBAAsB,GAAGnZ,OAAO,CAACuO,cAAc,CAAC;IACrD,IAAI,CAAC6K,cAAc,GAAGpZ,OAAO,CAAC2O,qBAAqB,CAAC;IACpD,IAAI,CAAC0K,gBAAgB,GAAGrZ,OAAO,CAAC2O,qBAAqB,CAAC;IACtD,IAAI,CAAC/I,SAAS,GAAGvE,SAAS,CAAC,CAAC;IAC5B,IAAI,CAACiY,UAAU,GAAG,IAAI/Z,MAAM,CAAC,CAAC;EAClC;EACAsF,MAAMA,CAAA,EAAG;IACL,IAAI;MAAEvC,KAAK;MAAEI;IAAQ,CAAC,GAAG,IAAI;IAC7B,IAAIgI,eAAe,GAAGhI,OAAO,CAACK,OAAO,CAACsH,YAAY,IAC9C/H,KAAK,CAACyD,UAAU,IAChBzD,KAAK,CAACyD,UAAU,CAAC4E,cAAc,CAACrI,KAAK,CAACmI,OAAO,CAAC,CAAC,CAAC;IACpD,IAAIlB,MAAM,GAAGjH,KAAK,CAACiX,KAAK,CAAClN,MAAM;IAC/B,IAAImN,gBAAgB,GAAG,IAAI,CAACT,gBAAgB,CAACzW,KAAK,CAACsT,WAAW,EAAErM,MAAM,CAAC;IACvE,IAAIkQ,gBAAgB,GAAG,IAAI,CAACT,gBAAgB,CAAC1W,KAAK,CAAC6T,WAAW,EAAE5M,MAAM,CAAC;IACvE,IAAImQ,qBAAqB,GAAG,IAAI,CAACT,qBAAqB,CAAC3W,KAAK,CAAC4T,gBAAgB,EAAE3M,MAAM,CAAC;IACtF,IAAIoQ,qBAAqB,GAAG,IAAI,CAACT,qBAAqB,CAAC5W,KAAK,CAACgU,gBAAgB,EAAE/M,MAAM,CAAC;IACtF,IAAIqQ,sBAAsB,GAAG,IAAI,CAACT,sBAAsB,CAAC7W,KAAK,CAACmT,iBAAiB,EAAElM,MAAM,CAAC;IACzF,IAAIsQ,cAAc,GAAG,IAAI,CAACT,cAAc,CAAC9W,KAAK,CAACiT,SAAS,EAAEhM,MAAM,CAAC;IACjE,IAAIuQ,gBAAgB,GAAG,IAAI,CAACT,gBAAgB,CAAC/W,KAAK,CAACkT,WAAW,EAAEjM,MAAM,CAAC;IACvE,OAAQnI,aAAa,CAAC,KAAK,EAAE;MAAEuB,SAAS,EAAE,kBAAkB;MAAEwK,GAAG,EAAE,IAAI,CAACvH;IAAU,CAAC,EAC/ExE,aAAa,CAAC,OAAO,EAAE;MAAEsH,IAAI,EAAE,cAAc;MAAE1B,KAAK,EAAE;QAC9CiE,QAAQ,EAAE3I,KAAK,CAACoL,aAAa;QAC7BrE,KAAK,EAAE/G,KAAK,CAACqL;MACjB;IAAE,CAAC,EACHrL,KAAK,CAACiI,iBAAiB,EACvBnJ,aAAa,CAAC,OAAO,EAAE;MAAEsH,IAAI,EAAE;IAAe,CAAC,EAC3CtH,aAAa,CAAC,IAAI,EAAE;MAAEsH,IAAI,EAAE;IAAM,CAAC,EAC/BpG,KAAK,CAAC8K,IAAI,IAAKhM,aAAa,CAAC,IAAI,EAAE;MAAE,aAAa,EAAE,IAAI;MAAEuB,SAAS,EAAE;IAAmC,CAAC,EACrGvB,aAAa,CAAC,KAAK,EAAE;MAAEuB,SAAS,EAAE;IAAwB,CAAC,EACvDvB,aAAa,CAAC,KAAK,EAAE;MAAEuB,SAAS,EAAE;IAAsC,CAAC,EAAE,OAAO+H,eAAe,KAAK,QAAQ,IAAKtJ,aAAa,CAACnC,qBAAqB,EAAE;MAAE6E,SAAS,EAAE,CAAC,iCAAiC,CAAC;MAAE8G,OAAO,EAAE;QAAEC,GAAG,EAAEH;MAAgB,CAAC;MAAEI,MAAM,EAAE,IAAI;MAAEtH,IAAI,EAAElB,KAAK,CAACmI;IAAQ,CAAC,CAAE,CAAC,CAAC,CAAE,EAC9RnI,KAAK,CAACiX,KAAK,CAACxU,GAAG,CAAC,CAACgV,IAAI,EAAE7M,CAAC,KAAM9L,aAAa,CAAC+T,OAAO,EAAE;MAAElQ,GAAG,EAAE8U,IAAI,CAAC9U,GAAG;MAAEoD,KAAK,EAAE,IAAI,CAACiR,UAAU,CAACjY,SAAS,CAAC0Y,IAAI,CAAC9U,GAAG,CAAC;MAAEsB,WAAW,EAAEjE,KAAK,CAACiE,WAAW;MAAE/C,IAAI,EAAEuW,IAAI,CAACvW,IAAI;MAAEiH,OAAO,EAAEnI,KAAK,CAACmI,OAAO;MAAE+E,UAAU,EAAElN,KAAK,CAACkN,UAAU;MAAEwG,gBAAgB,EAAE+D,IAAI,CAAC/D,gBAAgB;MAAED,cAAc,EAAEgE,IAAI,CAAChE,cAAc;MAAED,eAAe,EAAEiE,IAAI,CAACjE,eAAe;MAAEvG,aAAa,EAAEwK,IAAI,CAACxK,aAAa;MAAEqG,WAAW,EAAE4D,gBAAgB,CAACtM,CAAC,CAAC;MAAEiJ,WAAW,EAAEsD,gBAAgB,CAACvM,CAAC,CAAC;MAAEgJ,gBAAgB,EAAEwD,qBAAqB,CAACxM,CAAC,CAAC;MAAEoJ,gBAAgB,EAAEqD,qBAAqB,CAACzM,CAAC,CAAC;MAAEuI,iBAAiB,EAAEmE,sBAAsB,CAAC1M,CAAC,CAAC;MAAEqI,SAAS,EAAEsE,cAAc,CAAC3M,CAAC,CAAC;MAAEsI,WAAW,EAAEsE,gBAAgB,CAAC5M,CAAC,CAAC;MAAEnH,UAAU,EAAEzD,KAAK,CAACyD,UAAU;MAAE+Q,cAAc,EAAExU,KAAK,CAACwU,cAAc;MAAE5N,QAAQ,EAAE5G,KAAK,CAAC4G;IAAS,CAAC,CAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACpvB;EACA2E,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACmM,YAAY,CAAC,CAAC;EACvB;EACAjM,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACiM,YAAY,CAAC,CAAC;EACvB;EACAA,YAAYA,CAAA,EAAG;IACX,IAAI;MAAE1X;IAAM,CAAC,GAAG,IAAI;IACpB,IAAIA,KAAK,CAAC2X,WAAW,IACjB3X,KAAK,CAACqL,WAAW,KAAK,IAAI,CAAC;IAAA,EAC7B;MACErL,KAAK,CAAC2X,WAAW,CAAC,IAAIza,aAAa,CAAC,IAAI,CAACoG,SAAS,CAACO,OAAO,EAAE+T,cAAc,CAAC,IAAI,CAACZ,UAAU,CAACjL,UAAU,EAAE/L,KAAK,CAACiX,KAAK,CAAC,EAAE,IAAI;MAAE;MAC3H,KAAK,CAAC,CAAC;IACX;EACJ;AACJ;AACA,SAASW,cAAcA,CAAC5L,KAAK,EAAEiL,KAAK,EAAE;EAClC,OAAOA,KAAK,CAACxU,GAAG,CAAEgV,IAAI,IAAKzL,KAAK,CAACyL,IAAI,CAAC9U,GAAG,CAAC,CAAC;AAC/C;;AAEA;AACA;AACA,MAAMkV,QAAQ,SAAS3b,aAAa,CAAC;EACjCgH,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAAC2U,kBAAkB,GAAGpa,OAAO,CAACoa,kBAAkB,CAAC;IACrD,IAAI,CAACtU,KAAK,GAAG;MACTC,UAAU,EAAE;IAChB,CAAC;IACD,IAAI,CAACsU,YAAY,GAAIC,EAAE,IAAK;MACxB,IAAIA,EAAE,EAAE;QACJ,IAAI,CAAC5X,OAAO,CAAC6X,4BAA4B,CAAC,IAAI,EAAE;UAC5CD,EAAE;UACFE,iBAAiB,EAAE,IAAI,CAAClY,KAAK,CAACkY;QAClC,CAAC,CAAC;MACN,CAAC,MACI;QACD,IAAI,CAAC9X,OAAO,CAAC+X,8BAA8B,CAAC,IAAI,CAAC;MACrD;IACJ,CAAC;IACD,IAAI,CAACC,mBAAmB,GAAIC,OAAO,IAAK;MACpC,IAAI;QAAEC;MAAmB,CAAC,GAAG,IAAI,CAACtY,KAAK;MACvC,IAAI;QAAEyD;MAAW,CAAC,GAAG,IAAI,CAACD,KAAK;MAC/B,IAAI8U,kBAAkB,IAAI7U,UAAU,EAAE;QAClC,IAAI4U,OAAO,CAACpX,IAAI,EAAE;UACd,IAAIsH,GAAG,GAAG9E,UAAU,CAACgG,cAAc,CAAC4O,OAAO,CAACpX,IAAI,CAAC;UACjDsH,GAAG,GAAG6B,IAAI,CAACmO,IAAI,CAAChQ,GAAG,CAAC,CAAC,CAAC;UACtB,IAAIA,GAAG,EAAE;YACLA,GAAG,IAAI,CAAC,CAAC,CAAC;UACd;UACA+P,kBAAkB,CAAC/P,GAAG,CAAC;QAC3B;QACA,OAAO,IAAI;MACf;MACA,OAAO,KAAK;IAChB,CAAC;IACD,IAAI,CAACiQ,eAAe,GAAIC,SAAS,IAAK;MAClC,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC9B,CAAC;IACD,IAAI,CAACrT,gBAAgB,GAAI3B,UAAU,IAAK;MACpC,IAAI,CAAC4B,QAAQ,CAAC;QAAE5B;MAAW,CAAC,CAAC;MAC7B,IAAI,IAAI,CAACzD,KAAK,CAAC0Y,YAAY,EAAE;QACzB,IAAI,CAAC1Y,KAAK,CAAC0Y,YAAY,CAACjV,UAAU,CAAC;MACvC;IACJ,CAAC;EACL;EACAlB,MAAMA,CAAA,EAAG;IACL,IAAI;MAAEvC,KAAK;MAAEwD;IAAM,CAAC,GAAG,IAAI;IAC3B,OAAQ1E,aAAa,CAAC,KAAK,EAAE;MAAEuB,SAAS,EAAE,kBAAkB;MAAEwK,GAAG,EAAE,IAAI,CAACkN,YAAY;MAAErT,KAAK,EAAE;QACrF;QACA;QACAqC,KAAK,EAAE/G,KAAK,CAACqL,WAAW;QACxB1C,QAAQ,EAAE3I,KAAK,CAACoL;MACpB;IAAE,CAAC,EACHtM,aAAa,CAACqM,aAAa,EAAE;MAAEL,IAAI,EAAE9K,KAAK,CAAC8K,IAAI;MAAE7G,WAAW,EAAEjE,KAAK,CAACiE,WAAW;MAAEzB,SAAS,EAAExC,KAAK,CAACwC,SAAS;MAAE6I,WAAW,EAAErL,KAAK,CAACqL,WAAW;MAAEC,SAAS,EAAEtL,KAAK,CAACwG,UAAU,GAAGxG,KAAK,CAACgI,YAAY,GAAG,EAAE;MAAEoD,aAAa,EAAEpL,KAAK,CAACoL,aAAa;MAAEnD,iBAAiB,EAAEjI,KAAK,CAAC8K,IAAI,GAAG9K,KAAK,CAACiI,iBAAiB,GAAG,IAAI,CAAC;MAAgD0D,QAAQ,EAAE,IAAI,CAACvG;IAAiB,CAAC,CAAC,EACzXtG,aAAa,CAAC0X,eAAe,EAAE;MAAES,KAAK,EAAEjX,KAAK,CAACiX,KAAK;MAAEnM,IAAI,EAAE9K,KAAK,CAAC8K,IAAI;MAAE7G,WAAW,EAAEjE,KAAK,CAACiE,WAAW;MAAE2P,gBAAgB,EAAE5T,KAAK,CAAC4T,gBAAgB;MAAEC,WAAW,EAAE7T,KAAK,CAAC6T,WAAW;MAAEP,WAAW,EAAEtT,KAAK,CAACsT,WAAW;MAAEH,iBAAiB,EAAEnT,KAAK,CAACmT,iBAAiB;MAAEqB,cAAc,EAAExU,KAAK,CAACwU,cAAc;MAAEvB,SAAS,EAAEjT,KAAK,CAACiT,SAAS;MAAEC,WAAW,EAAElT,KAAK,CAACkT,WAAW;MAAEhG,UAAU,EAAElN,KAAK,CAACkN,UAAU;MAAE/E,OAAO,EAAEnI,KAAK,CAACmI,OAAO;MAAE6L,gBAAgB,EAAEhU,KAAK,CAACgU,gBAAgB;MAAE3I,WAAW,EAAErL,KAAK,CAACqL,WAAW;MAAED,aAAa,EAAEpL,KAAK,CAACoL,aAAa;MAAEnD,iBAAiB,EAAEjI,KAAK,CAACiI,iBAAiB;MAAExE,UAAU,EAAED,KAAK,CAACC,UAAU;MAAEkU,WAAW,EAAE,IAAI,CAACa,eAAe;MAAE5R,QAAQ,EAAE5G,KAAK,CAAC4G;IAAS,CAAC,CAAC,CAAC;EACppB;EACA2E,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACoN,eAAe,GAAG,IAAI,CAACvY,OAAO,CAACwY,qBAAqB,CAAC,IAAI,CAACR,mBAAmB,CAAC;EACvF;EACA3M,kBAAkBA,CAACoN,SAAS,EAAE;IAC1B,IAAI,CAACF,eAAe,CAACG,MAAM,CAACD,SAAS,CAAC5U,WAAW,KAAK,IAAI,CAACjE,KAAK,CAACiE,WAAW,CAAC;EACjF;EACAyH,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACiN,eAAe,CAACI,MAAM,CAAC,CAAC;EACjC;EACAC,QAAQA,CAACC,YAAY,EAAEC,WAAW,EAAE;IAChC,IAAI;MAAE1Y,OAAO;MAAEC;IAAQ,CAAC,GAAG,IAAI,CAACL,OAAO;IACvC,IAAI;MAAEqY;IAAU,CAAC,GAAG,IAAI;IACxB,IAAI;MAAExU;IAAY,CAAC,GAAG,IAAI,CAACjE,KAAK;IAChC,IAAI;MAAEyD;IAAW,CAAC,GAAG,IAAI,CAACD,KAAK;IAC/B,IAAI;MAAE2V,YAAY;MAAEC;IAAa,CAAC,GAAG,IAAI,CAACtB,kBAAkB,CAAC,IAAI,CAAC9X,KAAK,CAACkJ,YAAY,EAAEzI,OAAO,CAAC0Y,YAAY,CAAC;IAC3G,IAAIE,QAAQ,GAAGZ,SAAS,CAACa,WAAW,CAACL,YAAY,CAAC;IAClD,IAAI/O,SAAS,GAAGzG,UAAU,CAACwF,SAAS,CAACsQ,UAAU,CAACL,WAAW,CAAC;IAC5D,IAAIG,QAAQ,IAAI,IAAI,IAAInP,SAAS,IAAI,IAAI,EAAE;MACvC,IAAIuN,IAAI,GAAG,IAAI,CAACzX,KAAK,CAACiX,KAAK,CAACoC,QAAQ,CAAC;MACrC,IAAIG,OAAO,GAAG/V,UAAU,CAACwF,SAAS,CAACuB,IAAI,CAACN,SAAS,CAAC;MAClD,IAAIuP,UAAU,GAAGhW,UAAU,CAACwF,SAAS,CAACwB,SAAS,CAACP,SAAS,CAAC;MAC1D,IAAIwP,OAAO,GAAG,CAACR,WAAW,GAAGM,OAAO,IAAIC,UAAU,CAAC,CAAC;MACpD,IAAIE,cAAc,GAAGvP,IAAI,CAACG,KAAK,CAACmP,OAAO,GAAGN,YAAY,CAAC,CAAC,CAAC;MACzD,IAAIQ,SAAS,GAAG1P,SAAS,GAAGkP,YAAY,GAAGO,cAAc;MACzD,IAAIE,OAAO,GAAG,IAAI,CAAC7Z,KAAK,CAACiX,KAAK,CAACoC,QAAQ,CAAC,CAACnY,IAAI;MAC7C,IAAID,IAAI,GAAG7C,YAAY,CAAC6F,WAAW,CAACsF,WAAW,EAAElL,gBAAgB,CAAC8a,YAAY,EAAES,SAAS,CAAC,CAAC;MAC3F,IAAIvV,KAAK,GAAG7D,OAAO,CAACsZ,GAAG,CAACD,OAAO,EAAE5Y,IAAI,CAAC;MACtC,IAAIqD,GAAG,GAAG9D,OAAO,CAACsZ,GAAG,CAACzV,KAAK,EAAE8U,YAAY,CAAC;MAC1C,OAAO;QACHlV,WAAW;QACX1E,QAAQ,EAAEqD,MAAM,CAACC,MAAM,CAAC;UAAEqB,KAAK,EAAE;YAAEG,KAAK;YAAEC;UAAI,CAAC;UAAElF,MAAM,EAAE;QAAM,CAAC,EAAEqY,IAAI,CAACxK,aAAa,CAAC;QACrF8M,KAAK,EAAEtB,SAAS,CAAC3O,GAAG,CAACuP,QAAQ,CAAC;QAC9BpI,IAAI,EAAE;UACFmE,IAAI,EAAEqD,SAAS,CAACuB,KAAK,CAACX,QAAQ,CAAC;UAC/BhE,KAAK,EAAEoD,SAAS,CAACwB,MAAM,CAACZ,QAAQ,CAAC;UACjC9Q,GAAG,EAAEiR,OAAO;UACZ5M,MAAM,EAAE4M,OAAO,GAAGC;QACtB,CAAC;QACDS,KAAK,EAAE;MACX,CAAC;IACL;IACA,OAAO,IAAI;EACf;AACJ;AACA,SAASpC,kBAAkBA,CAAC5O,YAAY,EAAEiR,oBAAoB,EAAE;EAC5D,IAAIhB,YAAY,GAAGgB,oBAAoB,IAAIjR,YAAY;EACvD,IAAIkQ,YAAY,GAAG9a,oBAAoB,CAAC4K,YAAY,EAAEiQ,YAAY,CAAC;EACnE,IAAIC,YAAY,KAAK,IAAI,EAAE;IACvBD,YAAY,GAAGjQ,YAAY;IAC3BkQ,YAAY,GAAG,CAAC;IAChB;EACJ;EACA,OAAO;IAAED,YAAY;IAAEC;EAAa,CAAC;AACzC;AAEA,MAAMgB,iBAAiB,SAAS7b,MAAM,CAAC;EACnC8b,UAAUA,CAACnW,KAAK,EAAEoW,SAAS,EAAE;IACzB,IAAIpO,IAAI,GAAG,EAAE;IACb,KAAK,IAAIE,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGkO,SAAS,CAACvQ,MAAM,EAAEqC,GAAG,IAAI,CAAC,EAAE;MAChD,IAAImO,QAAQ,GAAG/b,eAAe,CAAC0F,KAAK,EAAEoW,SAAS,CAAClO,GAAG,CAAC,CAAC;MACrD,IAAImO,QAAQ,EAAE;QACVrO,IAAI,CAACvG,IAAI,CAAC;UACNtB,KAAK,EAAEkW,QAAQ,CAAClW,KAAK;UACrBC,GAAG,EAAEiW,QAAQ,CAACjW,GAAG;UACjBkW,OAAO,EAAED,QAAQ,CAAClW,KAAK,CAACiF,OAAO,CAAC,CAAC,KAAKpF,KAAK,CAACG,KAAK,CAACiF,OAAO,CAAC,CAAC;UAC3DmR,KAAK,EAAEF,QAAQ,CAACjW,GAAG,CAACgF,OAAO,CAAC,CAAC,KAAKpF,KAAK,CAACI,GAAG,CAACgF,OAAO,CAAC,CAAC;UACrD8C;QACJ,CAAC,CAAC;MACN;IACJ;IACA,OAAOF,IAAI;EACf;AACJ;AAEA,MAAMwO,WAAW,SAASxe,aAAa,CAAC;EACpCgH,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACwX,cAAc,GAAGjd,OAAO,CAACid,cAAc,CAAC;IAC7C,IAAI,CAACC,MAAM,GAAG,IAAIR,iBAAiB,CAAC,CAAC;IACrC,IAAI,CAACS,WAAW,GAAG9b,SAAS,CAAC,CAAC;EAClC;EACAwD,MAAMA,CAAA,EAAG;IACL,IAAI;MAAEvC,KAAK;MAAEI;IAAQ,CAAC,GAAG,IAAI;IAC7B,IAAI;MAAE6D,WAAW;MAAE6W;IAAc,CAAC,GAAG9a,KAAK;IAC1C,IAAI;MAAE+H,YAAY;MAAEgT;IAAiB,CAAC,GAAG3a,OAAO,CAACK,OAAO;IACxD,IAAI6Z,SAAS,GAAG,IAAI,CAACK,cAAc,CAACG,aAAa,EAAE7W,WAAW,EAAE7D,OAAO,CAACI,OAAO,CAAC;IAChF;IACA;IACA,OAAQ1B,aAAa,CAACpC,QAAQ,EAAE;MAAEwL,IAAI,EAAEH,YAAY,GAAG,QAAQ,GAAG;IAAM,CAAC,EAAE,CAACI,OAAO,EAAE+E,UAAU,KAAMpO,aAAa,CAAC+Y,QAAQ,EAAEjV,MAAM,CAACC,MAAM,CAAC;MAAEgI,GAAG,EAAE,IAAI,CAACgQ;IAAY,CAAC,EAAE,IAAI,CAACD,MAAM,CAACI,UAAU,CAAChb,KAAK,EAAEiE,WAAW,EAAE,IAAI,EAAE7D,OAAO,EAAEka,SAAS,CAAC,EAAE;MAAE1T,QAAQ,EAAE5G,KAAK,CAAC4G,QAAQ;MAAEkE,IAAI,EAAE9K,KAAK,CAAC8K,IAAI;MAAE7G,WAAW,EAAEA,WAAW;MAAEzB,SAAS,EAAExC,KAAK,CAACwC,SAAS;MAAE0G,YAAY,EAAElJ,KAAK,CAACkJ,YAAY;MAAE+N,KAAK,EAAE6D,aAAa,CAAC7D,KAAK,CAAC,CAAC,CAAC;MAAEhP,iBAAiB,EAAEjI,KAAK,CAACiI,iBAAiB;MAAEmD,aAAa,EAAEpL,KAAK,CAACoL,aAAa;MAAEC,WAAW,EAAErL,KAAK,CAACqL,WAAW;MAAErD,YAAY,EAAEhI,KAAK,CAACgI,YAAY;MAAExB,UAAU,EAAExG,KAAK,CAACwG,UAAU;MAAE2B,OAAO,EAAEA,OAAO;MAAE6L,gBAAgB,EAAEjM,YAAY,IAAI,IAAI,CAAC6S,MAAM,CAACK,YAAY,CAAC9S,OAAO,EAAElE,WAAW,EAAE8W,gBAAgB,EAAE3a,OAAO,EAAEka,SAAS,CAAC;MAAEpN,UAAU,EAAEA,UAAU;MAAEoL,kBAAkB,EAAEtY,KAAK,CAACsY,kBAAkB;MAAEI,YAAY,EAAE1Y,KAAK,CAAC0Y;IAAa,CAAC,CAAC,CAAE,CAAC;EAC3zB;AACJ;AACA,SAASiC,cAAcA,CAACG,aAAa,EAAE7W,WAAW,EAAEzD,OAAO,EAAE;EACzD,IAAI0a,MAAM,GAAG,EAAE;EACf,KAAK,IAAIha,IAAI,IAAI4Z,aAAa,CAACK,WAAW,EAAE;IACxCD,MAAM,CAACvV,IAAI,CAAC;MACRtB,KAAK,EAAE7D,OAAO,CAACsZ,GAAG,CAAC5Y,IAAI,EAAE+C,WAAW,CAACsF,WAAW,CAAC;MACjDjF,GAAG,EAAE9D,OAAO,CAACsZ,GAAG,CAAC5Y,IAAI,EAAE+C,WAAW,CAACuF,WAAW;IAClD,CAAC,CAAC;EACN;EACA,OAAO0R,MAAM;AACjB;;AAEA;AACA;AACA,MAAME,mBAAmB,GAAG,CACxB;EAAEC,KAAK,EAAE;AAAE,CAAC,EACZ;EAAEC,OAAO,EAAE;AAAG,CAAC,EACf;EAAEA,OAAO,EAAE;AAAG,CAAC,EACf;EAAEC,OAAO,EAAE;AAAG,CAAC,EACf;EAAEA,OAAO,EAAE;AAAG,CAAC,CAClB;AACD,SAASC,cAAcA,CAACjS,WAAW,EAAEC,WAAW,EAAEiS,qBAAqB,EAAEvS,YAAY,EAAE1I,OAAO,EAAE;EAC5F,IAAIkb,QAAQ,GAAG,IAAIC,IAAI,CAAC,CAAC,CAAC;EAC1B,IAAIC,QAAQ,GAAGrS,WAAW;EAC1B,IAAIsS,YAAY,GAAG7e,cAAc,CAAC,CAAC,CAAC;EACpC,IAAI8e,aAAa,GAAGL,qBAAqB,IAAIM,oBAAoB,CAAC7S,YAAY,CAAC;EAC/E,IAAI8S,KAAK,GAAG,EAAE;EACd,OAAOjf,SAAS,CAAC6e,QAAQ,CAAC,GAAG7e,SAAS,CAACyM,WAAW,CAAC,EAAE;IACjD,IAAItI,IAAI,GAAGV,OAAO,CAACsZ,GAAG,CAAC4B,QAAQ,EAAEE,QAAQ,CAAC;IAC1C,IAAI1b,SAAS,GAAG5B,oBAAoB,CAACud,YAAY,EAAEC,aAAa,CAAC,KAAK,IAAI;IAC1EE,KAAK,CAACrW,IAAI,CAAC;MACPzE,IAAI;MACJD,IAAI,EAAE2a,QAAQ;MACdjZ,GAAG,EAAEzB,IAAI,CAAC+a,WAAW,CAAC,CAAC;MACvB1b,UAAU,EAAE9B,mBAAmB,CAACyC,IAAI,CAAC;MACrChB;IACJ,CAAC,CAAC;IACF0b,QAAQ,GAAGxd,YAAY,CAACwd,QAAQ,EAAE1S,YAAY,CAAC;IAC/C2S,YAAY,GAAGzd,YAAY,CAACyd,YAAY,EAAE3S,YAAY,CAAC;EAC3D;EACA,OAAO8S,KAAK;AAChB;AACA;AACA,SAASD,oBAAoBA,CAAC7S,YAAY,EAAE;EACxC,IAAI0B,CAAC;EACL,IAAIkR,aAAa;EACjB,IAAII,aAAa;EACjB;EACA,KAAKtR,CAAC,GAAGwQ,mBAAmB,CAACrR,MAAM,GAAG,CAAC,EAAEa,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;IACrDkR,aAAa,GAAG9e,cAAc,CAACoe,mBAAmB,CAACxQ,CAAC,CAAC,CAAC;IACtDsR,aAAa,GAAG5d,oBAAoB,CAACwd,aAAa,EAAE5S,YAAY,CAAC;IACjE,IAAIgT,aAAa,KAAK,IAAI,IAAIA,aAAa,GAAG,CAAC,EAAE;MAC7C,OAAOJ,aAAa;IACxB;EACJ;EACA,OAAO5S,YAAY,CAAC,CAAC;AACzB;AAEA,MAAMiT,eAAe,SAASlZ,YAAY,CAAC;EACvCC,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACiZ,kBAAkB,GAAG1e,OAAO,CAAC0e,kBAAkB,CAAC;IACrD,IAAI,CAACZ,cAAc,GAAG9d,OAAO,CAAC8d,cAAc,CAAC;EACjD;EACAjZ,MAAMA,CAAA,EAAG;IACL,IAAI;MAAE9B,OAAO;MAAED,OAAO;MAAE6b;IAAqB,CAAC,GAAG,IAAI,CAACjc,OAAO;IAC7D,IAAI;MAAEJ;IAAM,CAAC,GAAG,IAAI;IACpB,IAAI;MAAEiE;IAAY,CAAC,GAAGjE,KAAK;IAC3B,IAAI8a,aAAa,GAAG,IAAI,CAACsB,kBAAkB,CAACnY,WAAW,EAAEoY,oBAAoB,CAAC;IAC9E,IAAIC,UAAU,GAAG,IAAI,CAAClZ,cAAc,CAACkZ,UAAU,CAACtc,KAAK,CAAC;IACtD,IAAIwC,SAAS,GAAG,IAAI,CAACgZ,cAAc,CAACvX,WAAW,CAACsF,WAAW,EAAEtF,WAAW,CAACuF,WAAW,EAAE/I,OAAO,CAAC8b,iBAAiB,EAAE9b,OAAO,CAACyI,YAAY,EAAE1I,OAAO,CAAC;IAC/I,IAAI;MAAE0G;IAAY,CAAC,GAAGzG,OAAO;IAC7B,IAAI+b,eAAe,GAAG,CAACtV,WAAW;IAClC,IAAIuV,eAAe,GAAGvV,WAAW;IACjC,IAAIwV,aAAa,GAAGjc,OAAO,CAACkc,UAAU,IAAK7d,aAAa,CAACJ,SAAS,EAAE;MAAEke,KAAK,EAAE9B,aAAa,CAACK,WAAW;MAAElX,WAAW,EAAEA,WAAW;MAAE4Y,oBAAoB,EAAE,IAAI;MAAEC,WAAW,EAAEN,eAAe,GAAG,IAAI,CAAC1Y,cAAc,GAAG;IAAK,CAAC,CAAE;IAC3N,IAAIiB,aAAa,GAAItE,OAAO,CAACsc,UAAU,KAAK,KAAK,KAAOnV,UAAU,IAAM9I,aAAa,CAACG,QAAQ,EAAE2D,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEyZ,UAAU,CAACld,MAAM,EAAE;MAAE6E,WAAW,EAAEA,WAAW;MAAE6W,aAAa,EAAEA,aAAa;MAAEC,gBAAgB,EAAEta,OAAO,CAACsa,gBAAgB;MAAE3P,aAAa,EAAExD,UAAU,CAACwD,aAAa;MAAE4R,YAAY,EAAEpV,UAAU,CAACK,iBAAiB;MAAEgV,cAAc,EAAET,eAAe,GAAG,IAAI,CAAC5X,kBAAkB,GAAG,IAAI;MAAEsY,eAAe,EAAE,KAAK;MAAE1W,UAAU,EAAE,KAAK;MAAE2W,gBAAgB,EAAE,IAAI,CAAC9Z,WAAW;MAAEgI,WAAW,EAAEzD,UAAU,CAACyD,WAAW;MAAErD,YAAY,EAAEJ,UAAU,CAACI,YAAY;MAAEpB,QAAQ,EAAE5G,KAAK,CAAC4G;IAAS,CAAC,EAAE,IAAI,CAACgC,sBAAsB,CAAC,CAAC,CAAC,CAAE,CAAC;IAC1lB,IAAIwU,eAAe,GAAIxV,UAAU,IAAM9I,aAAa,CAAC4b,WAAW,EAAE9X,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEyZ,UAAU,CAACjd,KAAK,EAAE;MAAEyb,aAAa,EAAEA,aAAa;MAAE7W,WAAW,EAAEA,WAAW;MAAE6G,IAAI,EAAE0R,eAAe;MAAEtT,YAAY,EAAEzI,OAAO,CAACyI,YAAY;MAAE1G,SAAS,EAAEA,SAAS;MAAEoE,QAAQ,EAAE5G,KAAK,CAAC4G,QAAQ;MAAEqB,iBAAiB,EAAEL,UAAU,CAACK,iBAAiB;MAAEmD,aAAa,EAAExD,UAAU,CAACwD,aAAa;MAAEC,WAAW,EAAEzD,UAAU,CAACyD,WAAW;MAAErD,YAAY,EAAEJ,UAAU,CAACI,YAAY;MAAE0Q,YAAY,EAAE,IAAI,CAACtT,gBAAgB;MAAEoB,UAAU,EAAEoB,UAAU,CAACpB,UAAU;MAAE8R,kBAAkB,EAAE,IAAI,CAAC5U;IAAuB,CAAC,CAAC,CAAE;IAC7iB,OAAO+Y,eAAe,GAChB,IAAI,CAACzV,mBAAmB,CAAC0V,aAAa,EAAE3X,aAAa,EAAEqY,eAAe,EAAEtC,aAAa,CAAC7T,MAAM,EAAEC,WAAW,EAAE1E,SAAS,EAAE,IAAI,CAACgB,KAAK,CAACC,UAAU,CAAC,GAC5I,IAAI,CAAC6B,kBAAkB,CAACoX,aAAa,EAAE3X,aAAa,EAAEqY,eAAe,CAAC;EAChF;AACJ;AACA,SAAShB,kBAAkBA,CAACnY,WAAW,EAAEoY,oBAAoB,EAAE;EAC3D,IAAIgB,SAAS,GAAG,IAAI1e,cAAc,CAACsF,WAAW,CAACE,WAAW,EAAEkY,oBAAoB,CAAC;EACjF,OAAO,IAAIzd,aAAa,CAACye,SAAS,EAAE,KAAK,CAAC;AAC9C;AAEA,IAAIC,QAAQ,GAAG,owKAAowK;AACnxKze,YAAY,CAACye,QAAQ,CAAC;AAEtB,SAAS5C,WAAW,EAAEN,iBAAiB,EAAE+B,eAAe,EAAEtE,QAAQ,EAAE7O,mBAAmB,EAAE/F,YAAY,EAAE0X,cAAc,EAAEa,cAAc,EAAEY,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}