{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nconst _c0 = [\"circlesElement\"];\nfunction CircleLineComponent_div_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 6);\n  }\n  if (rf & 2) {\n    const circle_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵstyleProp(\"width\", circle_r2.width)(\"border-top\", circle_r2.lineStyle);\n  }\n}\nfunction CircleLineComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵlistener(\"click\", function CircleLineComponent_div_2_Template_div_click_0_listener() {\n      const circle_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.chooseCircle(circle_r2));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵtemplate(2, CircleLineComponent_div_2_div_2_Template, 1, 4, \"div\", 4);\n    i0.ɵɵelementStart(3, \"div\", 5);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const circle_r2 = ctx.$implicit;\n    const isLast_r4 = ctx.last;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"color\", circle_r2.color)(\"background\", circle_r2.background);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.allowHover ? \"circle hoverit\" : ctx_r2.allowHoverMain ? \"hoverit-main circle\" : \"circle\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", circle_r2.text, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !isLast_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"margin-left\", circle_r2.extraTextML);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", circle_r2.extraText, \" \");\n  }\n}\nexport class CircleLineComponent {\n  constructor() {\n    this.circlesElement = {};\n    this.circles = [];\n    this.allowHover = false;\n    this.allowHoverMain = false;\n    this.circleChoice = new EventEmitter();\n  }\n  ngOnInit() {}\n  chooseCircle(circle) {\n    this.circleChoice.emit({\n      circle: circle\n    });\n  }\n  static #_ = this.ɵfac = function CircleLineComponent_Factory(t) {\n    return new (t || CircleLineComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: CircleLineComponent,\n    selectors: [[\"app-circle-line\"]],\n    viewQuery: function CircleLineComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.circlesElement = _t.first);\n      }\n    },\n    inputs: {\n      circles: \"circles\",\n      allowHover: \"allowHover\",\n      allowHoverMain: \"allowHoverMain\"\n    },\n    outputs: {\n      circleChoice: \"circleChoice\"\n    },\n    decls: 3,\n    vars: 1,\n    consts: [[\"circlesElement\", \"\"], [1, \"circles\"], [3, \"ngClass\", \"color\", \"background\", \"click\", 4, \"ngFor\", \"ngForOf\"], [3, \"click\", \"ngClass\"], [\"class\", \"line\", 3, \"width\", \"borderTop\", 4, \"ngIf\"], [1, \"extra-text\"], [1, \"line\"]],\n    template: function CircleLineComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 1, 0);\n        i0.ɵɵtemplate(2, CircleLineComponent_div_2_Template, 5, 10, \"div\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngForOf\", ctx.circles);\n      }\n    },\n    dependencies: [i1.NgClass, i1.NgForOf, i1.NgIf],\n    styles: [\".circles[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n}\\n.circles[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  line-height: 40px;\\n  background: var(--light-purple);\\n  border: 1px solid var(--light-purple);\\n  text-align: center;\\n  border-radius: 50%;\\n  position: relative;\\n}\\n.circles[_ngcontent-%COMP%]   .circle[_ngcontent-%COMP%]   .line[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 42px;\\n  top: 21px;\\n  height: 1px;\\n  border-top: 1px solid lightgray;\\n  width: 42px;\\n}\\n\\n.hoverit[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n\\n.hoverit[_ngcontent-%COMP%]:hover {\\n  background: var(--light-purple) !important;\\n  color: white !important;\\n}\\n\\n.hoverit-main[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n\\n.hoverit-main[_ngcontent-%COMP%]:hover {\\n  background: var(--main-color) !important;\\n  color: white !important;\\n}\\n\\n.extra-text[_ngcontent-%COMP%] {\\n  color: var(--light-purple);\\n  white-space: nowrap;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "i0", "ɵɵelement", "ɵɵstyleProp", "circle_r2", "width", "lineStyle", "ɵɵelementStart", "ɵɵlistener", "CircleLineComponent_div_2_Template_div_click_0_listener", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "chooseCircle", "ɵɵtext", "ɵɵtemplate", "CircleLineComponent_div_2_div_2_Template", "ɵɵelementEnd", "color", "background", "ɵɵproperty", "allowHover", "allowHoverMain", "ɵɵadvance", "ɵɵtextInterpolate1", "text", "isLast_r4", "extraTextML", "extraText", "CircleLineComponent", "constructor", "circlesElement", "circles", "circleChoice", "ngOnInit", "circle", "emit", "_", "_2", "selectors", "viewQuery", "CircleLineComponent_Query", "rf", "ctx", "CircleLineComponent_div_2_Template"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\circle-line\\circle-line.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\circle-line\\circle-line.component.html"], "sourcesContent": ["import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';\r\nimport { CircleLine, StudentGoal } from 'src/app/core/models/goal.model';\r\n\r\n@Component({\r\n  selector: 'app-circle-line',\r\n  templateUrl: './circle-line.component.html',\r\n  styleUrls: ['./circle-line.component.scss']\r\n})\r\nexport class CircleLineComponent implements OnInit {\r\n  @ViewChild('circlesElement') public circlesElement: ElementRef = {} as ElementRef;\r\n  @Input() circles: CircleLine[] = [];\r\n  @Input() allowHover: boolean = false;\r\n  @Input() allowHoverMain: boolean = false;\r\n  @Output() circleChoice = new EventEmitter<{ circle: CircleLine }>()\r\n\r\n  constructor() { }\r\n\r\n  ngOnInit(): void {\r\n  }\r\n\r\n  chooseCircle(circle: CircleLine) {\r\n    this.circleChoice.emit({ circle: circle });\r\n  }\r\n}\r\n", "<div class=\"circles\" #circlesElement>\r\n    <div *ngFor=\"let circle of circles; let i =index; let isLast = last\"\r\n        [ngClass]=\"allowHover? 'circle hoverit': allowHoverMain?'hoverit-main circle': 'circle'\"\r\n        [style.color]=\"circle.color\" [style.background]=\"circle.background\" (click)=\"chooseCircle(circle)\">\r\n        {{circle.text}}\r\n        <div *ngIf=\"!isLast\" class=\"line\" [style.width]=\"circle.width\" [style.borderTop]=\"circle.lineStyle\">\r\n\r\n        </div>\r\n        <div class=\"extra-text\" [style.marginLeft]=\"circle.extraTextML\">\r\n            {{circle.extraText}}\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": "AAAA,SAAgCA,YAAY,QAA0C,eAAe;;;;;;ICK7FC,EAAA,CAAAC,SAAA,aAEM;;;;IAFyDD,EAA7B,CAAAE,WAAA,UAAAC,SAAA,CAAAC,KAAA,CAA4B,eAAAD,SAAA,CAAAE,SAAA,CAAqC;;;;;;IAJvGL,EAAA,CAAAM,cAAA,aAEuG;IAA/BN,EAAA,CAAAO,UAAA,mBAAAC,wDAAA;MAAA,MAAAL,SAAA,GAAAH,EAAA,CAAAS,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAZ,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAAZ,SAAA,CAAoB;IAAA,EAAC;IAClGH,EAAA,CAAAgB,MAAA,GACA;IAAAhB,EAAA,CAAAiB,UAAA,IAAAC,wCAAA,iBAAoG;IAGpGlB,EAAA,CAAAM,cAAA,aAAgE;IAC5DN,EAAA,CAAAgB,MAAA,GACJ;IACJhB,EADI,CAAAmB,YAAA,EAAM,EACJ;;;;;;IAR2BnB,EAA7B,CAAAE,WAAA,UAAAC,SAAA,CAAAiB,KAAA,CAA4B,eAAAjB,SAAA,CAAAkB,UAAA,CAAuC;IADnErB,EAAA,CAAAsB,UAAA,YAAAV,MAAA,CAAAW,UAAA,sBAAAX,MAAA,CAAAY,cAAA,oCAAwF;IAExFxB,EAAA,CAAAyB,SAAA,EACA;IADAzB,EAAA,CAAA0B,kBAAA,MAAAvB,SAAA,CAAAwB,IAAA,MACA;IAAM3B,EAAA,CAAAyB,SAAA,EAAa;IAAbzB,EAAA,CAAAsB,UAAA,UAAAM,SAAA,CAAa;IAGK5B,EAAA,CAAAyB,SAAA,EAAuC;IAAvCzB,EAAA,CAAAE,WAAA,gBAAAC,SAAA,CAAA0B,WAAA,CAAuC;IAC3D7B,EAAA,CAAAyB,SAAA,EACJ;IADIzB,EAAA,CAAA0B,kBAAA,MAAAvB,SAAA,CAAA2B,SAAA,MACJ;;;ADFR,OAAM,MAAOC,mBAAmB;EAO9BC,YAAA;IANoC,KAAAC,cAAc,GAAe,EAAgB;IACxE,KAAAC,OAAO,GAAiB,EAAE;IAC1B,KAAAX,UAAU,GAAY,KAAK;IAC3B,KAAAC,cAAc,GAAY,KAAK;IAC9B,KAAAW,YAAY,GAAG,IAAIpC,YAAY,EAA0B;EAEnD;EAEhBqC,QAAQA,CAAA,GACR;EAEArB,YAAYA,CAACsB,MAAkB;IAC7B,IAAI,CAACF,YAAY,CAACG,IAAI,CAAC;MAAED,MAAM,EAAEA;IAAM,CAAE,CAAC;EAC5C;EAAC,QAAAE,CAAA,G;qBAdUR,mBAAmB;EAAA;EAAA,QAAAS,EAAA,G;UAAnBT,mBAAmB;IAAAU,SAAA;IAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;QCRhC5C,EAAA,CAAAM,cAAA,gBAAqC;QACjCN,EAAA,CAAAiB,UAAA,IAAA6B,kCAAA,kBAEuG;QAS3G9C,EAAA,CAAAmB,YAAA,EAAM;;;QAXsBnB,EAAA,CAAAyB,SAAA,GAAY;QAAZzB,EAAA,CAAAsB,UAAA,YAAAuB,GAAA,CAAAX,OAAA,CAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}