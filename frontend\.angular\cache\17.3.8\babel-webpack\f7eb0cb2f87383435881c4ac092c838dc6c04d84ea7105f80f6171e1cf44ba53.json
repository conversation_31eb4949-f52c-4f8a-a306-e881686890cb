{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nconst BACKEND_URL = environment.apiUrl;\nexport class TutorialService {\n  constructor(http) {\n    this.http = http;\n  }\n  getTutorials() {\n    return this.http.get(BACKEND_URL + \"/Upload/GetTutorials\");\n  }\n  static #_ = this.ɵfac = function TutorialService_Factory(t) {\n    return new (t || TutorialService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: TutorialService,\n    factory: TutorialService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["environment", "BACKEND_URL", "apiUrl", "TutorialService", "constructor", "http", "getTutorials", "get", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\services\\tutorial.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { map, mergeMap, switchMap } from 'rxjs/operators';\r\nimport { environment } from 'src/environments/environment';\r\nimport { Tutorial } from '../models/tutorial.model';\r\nconst BACKEND_URL = environment.apiUrl;\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class TutorialService {\r\n\r\n  constructor(\r\n    private http: HttpClient\r\n  ) { }\r\n\r\n  public getTutorials(): Observable<Tutorial[]> {\r\n    return this.http.get<Tutorial[]>(BACKEND_URL + \"/Upload/GetTutorials\")\r\n  }\r\n}"], "mappings": "AAIA,SAASA,WAAW,QAAQ,8BAA8B;;;AAE1D,MAAMC,WAAW,GAAGD,WAAW,CAACE,MAAM;AAItC,OAAM,MAAOC,eAAe;EAE1BC,YACUC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EACV;EAEGC,YAAYA,CAAA;IACjB,OAAO,IAAI,CAACD,IAAI,CAACE,GAAG,CAAaN,WAAW,GAAG,sBAAsB,CAAC;EACxE;EAAC,QAAAO,CAAA,G;qBARUL,eAAe,EAAAM,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAfV,eAAe;IAAAW,OAAA,EAAfX,eAAe,CAAAY,IAAA;IAAAC,UAAA,EAFd;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}