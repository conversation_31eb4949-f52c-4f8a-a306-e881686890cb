{"ast": null, "code": "import { Subject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\n;\nexport let ConfirmDialogService = /*#__PURE__*/(() => {\n  class ConfirmDialogService {\n    constructor() {\n      this.isVisible = false;\n      this.rejectBtnIcon = undefined;\n      this.acceptBtnIcon = undefined;\n      this.showHeader = true;\n      this.dismissableMask = true;\n      this.dialogType = 'header-with-image';\n      this.headerClass = 'text-center flex align-items-center flex-column ';\n      this.dialogKey = 'key1';\n      this.acceptBtnClass = ' ';\n      this.rejectBtnClass = 'cd-dialog-btn-reject';\n      this.rejectSelectionSource = new Subject();\n      this.rejectSelected$ = this.rejectSelectionSource.asObservable();\n      this.acceptSelectionSource = new Subject();\n      this.acceptSelected$ = this.acceptSelectionSource.asObservable();\n    }\n    setProperties(props) {\n      Object.keys(props).forEach(key => {\n        this[key] = props[key];\n      });\n    }\n    getProperties() {\n      const properties = {\n        isVisible: this.isVisible,\n        confirmMessage: this.confirmMessage,\n        confirmIcon: this.confirmIcon,\n        acceptBtnLabel: this.acceptBtnLabel,\n        rejectBtnLabel: this.rejectBtnLabel,\n        rejectBtnIcon: this.rejectBtnIcon,\n        acceptBtnIcon: this.acceptBtnIcon,\n        showHeader: this.showHeader,\n        dismissableMask: this.dismissableMask,\n        dialogType: this.dialogType,\n        headerClass: this.headerClass,\n        dialogKey: this.dialogKey,\n        action: this.action,\n        headerText: this.headerText,\n        acceptBtnClass: this.acceptBtnClass,\n        rejectBtnClass: this.rejectBtnClass,\n        extraData: this.extraData\n      };\n      return properties;\n    }\n    resetProperties() {\n      this.isVisible = false;\n      this.confirmMessage = undefined;\n      this.confirmIcon = undefined;\n      this.acceptBtnLabel = undefined;\n      this.rejectBtnLabel = undefined;\n      this.action = undefined;\n      this.rejectBtnIcon = undefined;\n      this.acceptBtnIcon = undefined;\n      this.showHeader = true;\n      this.dismissableMask = true;\n      this.dialogType = 'header-with-image';\n      this.headerClass = 'text-center flex align-items-center flex-column ';\n      this.dialogKey = 'key1';\n      this.headerText = undefined;\n      this.acceptBtnClass = ' ';\n      this.rejectBtnClass = 'cd-dialog-btn-reject';\n      this.extraData = null;\n    }\n    onRejectSelection(event) {\n      this.rejectSelectionSource.next(this.getProperties());\n    }\n    onAcceptSelection(event) {\n      this.acceptSelectionSource.next(this.getProperties());\n    }\n    show() {\n      this.isVisible = true;\n    }\n    hide() {\n      this.isVisible = false;\n    }\n    static #_ = this.ɵfac = function ConfirmDialogService_Factory(t) {\n      return new (t || ConfirmDialogService)();\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ConfirmDialogService,\n      factory: ConfirmDialogService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return ConfirmDialogService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}