{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { DashboardComponent } from './dashboard.component';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport { ClassroomModule } from '../classroom/classroom.module';\nimport { SafePipe } from 'src/app/core/pipes/save.pipe';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { IntroGradientComponent } from './intro-gradient/intro-gradient.component';\nimport { DashboardNotificationsComponent } from './dashboard-notifications/dashboard-notifications.component';\nimport { ScrollPanelModule } from 'primeng/scrollpanel';\nimport { CalendarModule } from 'primeng/calendar';\nimport { DashboardNextStepsComponent } from './dashboard-next-steps/dashboard-next-steps.component';\nimport { DashboardBenefitsComponent } from './dashboard-benefits/dashboard-benefits.component';\nimport { DashboardRequestTrialFormComponent } from './dashboard-request-trial-form/dashboard-request-trial-form.component';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { OverlayPanelModule } from 'primeng/overlaypanel';\nimport { RouterModule } from '@angular/router';\nimport { DashboardRoutingModule } from './dashboard-routing.module';\nimport { DeferModule } from 'primeng/defer';\nimport { CalendarPreviewWeekComponent } from './calendar-preview-week/calendar-preview-week.component';\nimport { RedirectToGalaxyComponent } from './redirect-to-galaxy/redirect-to-galaxy.component';\nimport { DashboardContactUsFormComponent } from './dashboard-contact-us-form/dashboard-contact-us-form.component';\nimport { CarouselModule } from 'primeng/carousel';\nimport { DashboardTeacherClassroomsComponent } from './dashboard-teacher-classrooms/dashboard-teacher-classrooms.component';\nimport { BuyPackageSuggestionBoxComponent } from '../classroom/packages/buy-package/buy-package-suggestion-box/buy-package-suggestion-box.component';\nlet DashboardModule = class DashboardModule {};\nDashboardModule = __decorate([NgModule({\n  declarations: [DashboardComponent, SafePipe, IntroGradientComponent, DashboardNotificationsComponent, DashboardNextStepsComponent, DashboardBenefitsComponent, DashboardRequestTrialFormComponent, CalendarPreviewWeekComponent, RedirectToGalaxyComponent, DashboardContactUsFormComponent, DashboardTeacherClassroomsComponent, BuyPackageSuggestionBoxComponent],\n  imports: [CommonModule, RouterModule, DashboardRoutingModule, ClassroomModule, SharedModule, ReactiveFormsModule, MatProgressSpinnerModule, ScrollPanelModule, CalendarModule, FormsModule, InputTextModule, DropdownModule, OverlayPanelModule, DeferModule, CarouselModule]\n})], DashboardModule);\nexport { DashboardModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "DashboardComponent", "SharedModule", "ClassroomModule", "SafePipe", "FormsModule", "ReactiveFormsModule", "MatProgressSpinnerModule", "IntroGradientComponent", "DashboardNotificationsComponent", "ScrollPanelModule", "CalendarModule", "DashboardNextStepsComponent", "DashboardBenefitsComponent", "DashboardRequestTrialFormComponent", "InputTextModule", "DropdownModule", "OverlayPanelModule", "RouterModule", "DashboardRoutingModule", "DeferModule", "CalendarPreviewWeekComponent", "RedirectToGalaxyComponent", "DashboardContactUsFormComponent", "CarouselModule", "DashboardTeacherClassroomsComponent", "BuyPackageSuggestionBoxComponent", "DashboardModule", "__decorate", "declarations", "imports"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\dashboard\\dashboard.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { DashboardComponent } from './dashboard.component';\r\nimport { SharedModule } from 'src/app/shared/shared.module';\r\nimport { ClassroomModule } from '../classroom/classroom.module';\r\nimport { SafePipe } from 'src/app/core/pipes/save.pipe';\r\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { IntroGradientComponent } from './intro-gradient/intro-gradient.component';\r\nimport { DashboardNotificationsComponent } from './dashboard-notifications/dashboard-notifications.component';\r\nimport { ScrollPanelModule } from 'primeng/scrollpanel';\r\nimport { CalendarModule } from 'primeng/calendar';\r\nimport { DashboardNextStepsComponent } from './dashboard-next-steps/dashboard-next-steps.component';\r\nimport { DashboardBenefitsComponent } from './dashboard-benefits/dashboard-benefits.component';\r\nimport { DashboardRequestTrialFormComponent } from './dashboard-request-trial-form/dashboard-request-trial-form.component';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { OverlayPanelModule } from 'primeng/overlaypanel';\r\nimport { RouterModule } from '@angular/router';\r\nimport { DashboardRoutingModule } from './dashboard-routing.module';\r\nimport { DeferModule } from 'primeng/defer';\r\nimport { CalendarPreviewWeekComponent } from './calendar-preview-week/calendar-preview-week.component';\r\nimport { RedirectToGalaxyComponent } from './redirect-to-galaxy/redirect-to-galaxy.component';\r\nimport { DashboardContactUsFormComponent } from './dashboard-contact-us-form/dashboard-contact-us-form.component';\r\nimport { CarouselModule } from 'primeng/carousel';\r\nimport { DashboardTeacherClassroomsComponent } from './dashboard-teacher-classrooms/dashboard-teacher-classrooms.component';\r\nimport { BuyPackageSuggestionBoxComponent } from '../classroom/packages/buy-package/buy-package-suggestion-box/buy-package-suggestion-box.component';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    DashboardComponent,\r\n    SafePipe,\r\n    IntroGradientComponent,\r\n    DashboardNotificationsComponent,\r\n    DashboardNextStepsComponent,\r\n    DashboardBenefitsComponent,\r\n    DashboardRequestTrialFormComponent,\r\n    CalendarPreviewWeekComponent,\r\n    RedirectToGalaxyComponent,\r\n    DashboardContactUsFormComponent,\r\n    DashboardTeacherClassroomsComponent,\r\n    BuyPackageSuggestionBoxComponent,\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    RouterModule,\r\n    DashboardRoutingModule,\r\n    ClassroomModule,\r\n    SharedModule,\r\n    ReactiveFormsModule,\r\n    MatProgressSpinnerModule,\r\n    ScrollPanelModule,\r\n    CalendarModule,\r\n    FormsModule,\r\n    InputTextModule,\r\n    DropdownModule,\r\n    OverlayPanelModule,\r\n    DeferModule,\r\n    CarouselModule,\r\n  ]\r\n})\r\nexport class DashboardModule { }\r\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,eAAe,QAAQ,+BAA+B;AAC/D,SAASC,QAAQ,QAAQ,8BAA8B;AACvD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,sBAAsB,QAAQ,2CAA2C;AAClF,SAASC,+BAA+B,QAAQ,6DAA6D;AAC7G,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,2BAA2B,QAAQ,uDAAuD;AACnG,SAASC,0BAA0B,QAAQ,mDAAmD;AAC9F,SAASC,kCAAkC,QAAQ,uEAAuE;AAC1H,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,4BAA4B,QAAQ,yDAAyD;AACtG,SAASC,yBAAyB,QAAQ,mDAAmD;AAC7F,SAASC,+BAA+B,QAAQ,iEAAiE;AACjH,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,mCAAmC,QAAQ,uEAAuE;AAC3H,SAASC,gCAAgC,QAAQ,mGAAmG;AAoC7I,IAAMC,eAAe,GAArB,MAAMA,eAAe,GAAI;AAAnBA,eAAe,GAAAC,UAAA,EAjC3B7B,QAAQ,CAAC;EACR8B,YAAY,EAAE,CACZ5B,kBAAkB,EAClBG,QAAQ,EACRI,sBAAsB,EACtBC,+BAA+B,EAC/BG,2BAA2B,EAC3BC,0BAA0B,EAC1BC,kCAAkC,EAClCO,4BAA4B,EAC5BC,yBAAyB,EACzBC,+BAA+B,EAC/BE,mCAAmC,EACnCC,gCAAgC,CACjC;EACDI,OAAO,EAAE,CACP9B,YAAY,EACZkB,YAAY,EACZC,sBAAsB,EACtBhB,eAAe,EACfD,YAAY,EACZI,mBAAmB,EACnBC,wBAAwB,EACxBG,iBAAiB,EACjBC,cAAc,EACdN,WAAW,EACXU,eAAe,EACfC,cAAc,EACdC,kBAAkB,EAClBG,WAAW,EACXI,cAAc;CAEjB,CAAC,C,EACWG,eAAe,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}