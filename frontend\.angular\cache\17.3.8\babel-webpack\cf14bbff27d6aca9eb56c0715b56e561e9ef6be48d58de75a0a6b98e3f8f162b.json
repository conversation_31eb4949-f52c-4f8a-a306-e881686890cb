{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class TeacherAvailabilityComponent {\n  constructor() {}\n  ngOnInit() {}\n  static #_ = this.ɵfac = function TeacherAvailabilityComponent_Factory(t) {\n    return new (t || TeacherAvailabilityComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TeacherAvailabilityComponent,\n    selectors: [[\"app-teacher-availability\"]],\n    inputs: {\n      user: \"user\"\n    },\n    decls: 1,\n    vars: 1,\n    consts: [[3, \"user\"]],\n    template: function TeacherAvailabilityComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"app-availability\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"user\", ctx.user);\n      }\n    },\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["TeacherAvailabilityComponent", "constructor", "ngOnInit", "_", "_2", "selectors", "inputs", "user", "decls", "vars", "consts", "template", "TeacherAvailabilityComponent_Template", "rf", "ctx", "i0", "ɵɵelement", "ɵɵproperty"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\teacher-application\\teacher-availability\\teacher-availability.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\teacher-application\\teacher-availability\\teacher-availability.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\r\nimport { User } from 'src/app/core/models/user.model';\r\n\r\n@Component({\r\n  selector: 'app-teacher-availability',\r\n  templateUrl: './teacher-availability.component.html',\r\n  styleUrls: ['./teacher-availability.component.scss']\r\n})\r\nexport class TeacherAvailabilityComponent implements OnInit {\r\n  @Input() user?: User\r\n\r\n  constructor() { }\r\n\r\n  ngOnInit(): void {\r\n    \r\n  }\r\n\r\n}\r\n", "<app-availability [user]=\"user\"></app-availability>\r\n"], "mappings": ";AAQA,OAAM,MAAOA,4BAA4B;EAGvCC,YAAA,GAAgB;EAEhBC,QAAQA,CAAA,GAER;EAAC,QAAAC,CAAA,G;qBAPUH,4BAA4B;EAAA;EAAA,QAAAI,EAAA,G;UAA5BJ,4BAA4B;IAAAK,SAAA;IAAAC,MAAA;MAAAC,IAAA;IAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCRzCE,EAAA,CAAAC,SAAA,0BAAmD;;;QAAjCD,EAAA,CAAAE,UAAA,SAAAH,GAAA,CAAAP,IAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}