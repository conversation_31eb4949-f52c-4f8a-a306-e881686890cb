{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { DOCUMENT } from '@angular/common';\nimport { signal } from '@angular/core';\nimport { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { switchMap, take } from 'rxjs/operators';\nimport { PackageType, PackagesModel } from 'src/app/core/models/package.model';\nimport { slideInOut2, slideInOut } from 'src/app/helpers/my-animations';\nimport { SubSink } from 'subsink';\nimport RevolutCheckout from \"@revolut/checkout\";\nimport { of } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"src/app/core/services/classroom.service\";\nimport * as i5 from \"src/app/core/services/general.service\";\nimport * as i6 from \"src/app/core/services/package.service\";\nimport * as i7 from \"src/app/core/services/toast.service\";\nimport * as i8 from \"src/app/core/services/confirm-dialog.service\";\nimport * as i9 from \"@angular/platform-browser\";\nimport * as i10 from \"../../../../shared/prime/input/prime-input-dropdown/prime-input-dropdown.component\";\nimport * as i11 from \"primeng/button\";\nimport * as i12 from \"primeng/checkbox\";\nimport * as i13 from \"primeng/radiobutton\";\nimport * as i14 from \"./buy-package-suggestion-box/buy-package-suggestion-box.component\";\nimport * as i15 from \"@angular/forms\";\nimport * as i16 from \"@angular/material/tooltip\";\nconst _c0 = (a0, a1, a2, a3) => ({\n  \"level-default\": a0,\n  \"level-disabled\": a1,\n  \"level-selected\": a2,\n  \"hidden\": a3\n});\nconst _c1 = a0 => ({\n  \"selected\": a0\n});\nconst _c2 = () => ({\n  standalone: true\n});\nconst _c3 = () => [\"/dashboard\"];\nconst _c4 = a0 => ({\n  \"hidden\": a0\n});\nfunction BuyPackageComponent_div_2_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.currentStep, \"/3 \");\n  }\n}\nfunction BuyPackageComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function BuyPackageComponent_div_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBack());\n    });\n    i0.ɵɵelementStart(2, \"div\", 13);\n    i0.ɵɵelement(3, \"span\", 14);\n    i0.ɵɵelementStart(4, \"span\", 15);\n    i0.ɵɵtext(5, \"Back\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 16);\n    i0.ɵɵtext(7, \" Buy Package \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, BuyPackageComponent_div_2_div_8_Template, 3, 1, \"div\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStep != 4);\n  }\n}\nfunction BuyPackageComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 20);\n    i0.ɵɵlistener(\"click\", function BuyPackageComponent_div_3_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.trialSection());\n    });\n    i0.ɵɵtext(2, \" Request new Trial \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuyPackageComponent_div_4_div_1_div_1_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵlistener(\"click\", function BuyPackageComponent_div_4_div_1_div_1_div_9_Template_div_click_0_listener() {\n      const classroom_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.selectClassroom(classroom_r6));\n    });\n    i0.ɵɵelementStart(1, \"span\", 36);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"br\");\n    i0.ɵɵelementStart(4, \"div\", 37);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"slice\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const classroom_r6 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"package-btn-\", classroom_r6.id, \"\");\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.getBtnStyle(true));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", classroom_r6.language, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"package-btn-teacher-\", classroom_r6.id, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" with \", classroom_r6.teacher.firstName, \" \", i0.ɵɵpipeBind3(6, 8, classroom_r6.teacher.lastName, 0, 1), \". \");\n  }\n}\nfunction BuyPackageComponent_div_4_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 29);\n    i0.ɵɵtext(2, \" Select Classroom \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 30)(4, \"div\", 31);\n    i0.ɵɵlistener(\"click\", function BuyPackageComponent_div_4_div_1_div_1_Template_div_click_4_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.buyNew());\n    });\n    i0.ɵɵelementStart(5, \"span\", 32);\n    i0.ɵɵtext(6, \"NEW \");\n    i0.ɵɵelementStart(7, \"span\", 33);\n    i0.ɵɵtext(8, \"language\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(9, BuyPackageComponent_div_4_div_1_div_1_div_9_Template, 7, 12, \"div\", 34);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.userClassrooms);\n  }\n}\nfunction BuyPackageComponent_div_4_div_1_div_2_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵlistener(\"click\", function BuyPackageComponent_div_4_div_1_div_2_div_4_Template_div_click_0_listener() {\n      const language_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.selectLanguage(language_r8));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const language_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"language-btn-\", language_r8.code, \"\");\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.getBtnStyle(false));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", language_r8.code, \" \");\n  }\n}\nfunction BuyPackageComponent_div_4_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 38);\n    i0.ɵɵtext(2, \" Select Language \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 30);\n    i0.ɵɵtemplate(4, BuyPackageComponent_div_4_div_1_div_2_div_4_Template, 2, 4, \"div\", 39);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"margin-top\", ctx_r1.inTrial ? \"30px\" : \"0\");\n    i0.ɵɵproperty(\"@slideInOut\", undefined);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.mltLanguages);\n  }\n}\nfunction BuyPackageComponent_div_4_div_1_div_3_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function BuyPackageComponent_div_4_div_1_div_3_div_4_Template_div_click_0_listener() {\n      const level_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.selectLevel(level_r10));\n    });\n    i0.ɵɵelementStart(1, \"div\", 44)(2, \"div\", 45);\n    i0.ɵɵlistener(\"click\", function BuyPackageComponent_div_4_div_1_div_3_div_4_Template_div_click_2_listener() {\n      const level_r10 = i0.ɵɵrestoreView(_r9).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.selectLevel(level_r10));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const level_r10 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"level-btn-\", level_r10, \"\");\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(5, _c0, ctx_r1.compareLevels(level_r10, ctx_r1.selectedLevel) >= 0, ctx_r1.compareLevels(level_r10, ctx_r1.selectedDefaultLevel) < 0 && !ctx_r1.isNew, level_r10 == ctx_r1.selectedLevel, level_r10 == \"NT2.1\" || level_r10 == \"NT2.2\" || level_r10 == \"TBD\" || level_r10 == \"Any\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matTooltip\", level_r10 == \"BS\" ? \"Business\" : \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(level_r10);\n  }\n}\nfunction BuyPackageComponent_div_4_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 41);\n    i0.ɵɵtext(2, \" Select Level \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 30);\n    i0.ɵɵtemplate(4, BuyPackageComponent_div_4_div_1_div_3_div_4_Template, 4, 10, \"div\", 42);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"@slideInOut\", undefined);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.mltLevels);\n  }\n}\nfunction BuyPackageComponent_div_4_div_1_div_4_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 43);\n    i0.ɵɵlistener(\"click\", function BuyPackageComponent_div_4_div_1_div_4_div_2_Template_div_click_0_listener() {\n      const level_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.selectLevel(level_r12));\n    });\n    i0.ɵɵelementStart(1, \"div\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const level_r12 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"level-btn-\", level_r12, \"\");\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction4(4, _c0, ctx_r1.compareLevels(level_r12, ctx_r1.selectedLevel) >= 0, ctx_r1.compareLevels(level_r12, ctx_r1.selectedLevel) < 0, level_r12 == ctx_r1.selectedLevel, level_r12 !== \"NT2.1\" && level_r12 !== \"NT2.2\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", level_r12, \" \");\n  }\n}\nfunction BuyPackageComponent_div_4_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 30);\n    i0.ɵɵtemplate(2, BuyPackageComponent_div_4_div_1_div_4_div_2_Template, 3, 9, \"div\", 42);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.mltLevels);\n  }\n}\nfunction BuyPackageComponent_div_4_div_1_div_5_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 48);\n    i0.ɵɵlistener(\"click\", function BuyPackageComponent_div_4_div_1_div_5_div_4_Template_div_click_0_listener() {\n      const number_r14 = i0.ɵɵrestoreView(_r13).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.selectNumber(number_r14));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const number_r14 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"number-btn-\", number_r14, \"\");\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.getBtnStyle(false));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", number_r14, \" \");\n  }\n}\nfunction BuyPackageComponent_div_4_div_1_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 38);\n    i0.ɵɵtext(2, \" Select Number of Students \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 46);\n    i0.ɵɵtemplate(4, BuyPackageComponent_div_4_div_1_div_5_div_4_Template, 2, 4, \"div\", 47);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"@slideInOut\", undefined);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.numberOfStudents);\n  }\n}\nfunction BuyPackageComponent_div_4_div_1_div_6_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51);\n    i0.ɵɵlistener(\"click\", function BuyPackageComponent_div_4_div_1_div_6_div_4_Template_div_click_0_listener() {\n      const ctx_r15 = i0.ɵɵrestoreView(_r15);\n      const hour_r17 = ctx_r15.$implicit;\n      const i_r18 = ctx_r15.index;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.selectHours(hour_r17, i_r18));\n    });\n    i0.ɵɵelementStart(1, \"div\", 52);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 53);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 52);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const hour_r17 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"hour-btn-\", hour_r17.price, \"\");\n    i0.ɵɵproperty(\"ngStyle\", ctx_r1.getBtnStyle(true));\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"id\", \"hour-btn-hours-\", hour_r17.price, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", hour_r17.hours, \" hours \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", hour_r17.price, \"\\u20AC \");\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"id\", \"hour-btn-per-\", hour_r17.price, \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", hour_r17.perHour, \"\\u20AC / hour \");\n  }\n}\nfunction BuyPackageComponent_div_4_div_1_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 38);\n    i0.ɵɵtext(2, \" Select Hours \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 49);\n    i0.ɵɵtemplate(4, BuyPackageComponent_div_4_div_1_div_6_div_4_Template, 7, 10, \"div\", 50);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.mltPricesHourly);\n  }\n}\nfunction BuyPackageComponent_div_4_div_1_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55);\n    i0.ɵɵlistener(\"click\", function BuyPackageComponent_div_4_div_1_div_7_Template_div_click_1_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.sendRequestTrial());\n    });\n    i0.ɵɵtext(2, \" Send Request for the Trial \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"@slideInOut\", undefined);\n  }\n}\nfunction BuyPackageComponent_div_4_div_1_div_8_div_4_span_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Expires in \", ctx_r1.expiresIn, \" months\");\n  }\n}\nfunction BuyPackageComponent_div_4_div_1_div_8_div_4_span_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const package_r21 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Expires in +\", package_r21.expiresPlus, \"\");\n  }\n}\nfunction BuyPackageComponent_div_4_div_1_div_8_div_4_ng_container_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"img\", 65);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction BuyPackageComponent_div_4_div_1_div_8_div_4_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 70);\n  }\n}\nfunction BuyPackageComponent_div_4_div_1_div_8_div_4_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 64);\n    i0.ɵɵelement(1, \"img\", 65);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const package_r21 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", package_r21.pause, \" pause available\");\n  }\n}\nfunction BuyPackageComponent_div_4_div_1_div_8_div_4_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71);\n    i0.ɵɵelement(1, \"img\", 72);\n    i0.ɵɵelementStart(2, \"span\", 73);\n    i0.ɵɵtext(3, \"Package selected\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuyPackageComponent_div_4_div_1_div_8_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"div\", 59)(2, \"div\", 60);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementStart(4, \"div\", 61);\n    i0.ɵɵlistener(\"click\", function BuyPackageComponent_div_4_div_1_div_8_div_4_Template_div_click_4_listener() {\n      const package_r21 = i0.ɵɵrestoreView(_r20).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.selectPackage(package_r21));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 62)(6, \"div\")(7, \"strong\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 63)(10, \"div\", 64);\n    i0.ɵɵelement(11, \"img\", 65);\n    i0.ɵɵtemplate(12, BuyPackageComponent_div_4_div_1_div_8_div_4_span_12_Template, 2, 1, \"span\", 9)(13, BuyPackageComponent_div_4_div_1_div_8_div_4_span_13_Template, 2, 1, \"span\", 9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 66);\n    i0.ɵɵelement(15, \"img\", 65);\n    i0.ɵɵelementStart(16, \"span\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 66);\n    i0.ɵɵtemplate(19, BuyPackageComponent_div_4_div_1_div_8_div_4_ng_container_19_Template, 2, 0, \"ng-container\", 67)(20, BuyPackageComponent_div_4_div_1_div_8_div_4_ng_template_20_Template, 1, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23, \"AI Chat\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(24, BuyPackageComponent_div_4_div_1_div_8_div_4_div_24_Template, 4, 1, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, BuyPackageComponent_div_4_div_1_div_8_div_4_div_25_Template, 4, 0, \"div\", 69);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const package_r21 = ctx.$implicit;\n    const i_r22 = ctx.index;\n    const elseAiChatIconBlock_r23 = i0.ɵɵreference(21);\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵstyleProp(\"border-color\", ctx_r1.getCasualColor(package_r21.type));\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background\", i_r22 === 0 ? \"linear-gradient(to right, #122171, #353792, #544eb3, #7366d6, #927ffa)\" : i_r22 === ctx_r1.defaultPackages.length - 1 ? \"linear-gradient(to right, #122171, #004b99, #0074bb, #009ed7, #4dc8f0)\" : \"linear-gradient(to left, #8497ff, #6977da, #4e59b6, #323c93, #122171)\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", package_r21.type, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(14, _c1, package_r21.type == ctx_r1.selectedPackageToBuy.type));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", package_r21.costPlus !== 0 ? \"+\" + package_r21.costPlus : \"- \", \"\\u20AC \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", package_r21.type == \"Regular\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", package_r21.type != \"Regular\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", package_r21.cancelation, \" hours cancellation policy\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", package_r21.type != \"Regular\")(\"ngIfElse\", elseAiChatIconBlock_r23);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", package_r21.type != \"Regular\" && package_r21.pause);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", package_r21.type == ctx_r1.selectedPackageToBuy.type);\n  }\n}\nfunction BuyPackageComponent_div_4_div_1_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 41);\n    i0.ɵɵtext(2, \" Select Package Type \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 56);\n    i0.ɵɵtemplate(4, BuyPackageComponent_div_4_div_1_div_8_div_4_Template, 26, 16, \"div\", 57);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.defaultPackages);\n  }\n}\nfunction BuyPackageComponent_div_4_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵtemplate(1, BuyPackageComponent_div_4_div_1_div_1_Template, 10, 1, \"div\", 9)(2, BuyPackageComponent_div_4_div_1_div_2_Template, 5, 4, \"div\", 27)(3, BuyPackageComponent_div_4_div_1_div_3_Template, 5, 2, \"div\", 9)(4, BuyPackageComponent_div_4_div_1_div_4_Template, 3, 1, \"div\", 9)(5, BuyPackageComponent_div_4_div_1_div_5_Template, 5, 2, \"div\", 9)(6, BuyPackageComponent_div_4_div_1_div_6_Template, 5, 1, \"div\", 9)(7, BuyPackageComponent_div_4_div_1_div_7_Template, 3, 1, \"div\", 28)(8, BuyPackageComponent_div_4_div_1_div_8_Template, 5, 1, \"div\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.inTrial ? \"100%\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.inTrial);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hideSection);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hideSection);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedLanguage.code == \"DUT\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hideSection);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.inTrial);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.hideSection && ctx_r1.inTrial);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.inTrial);\n  }\n}\nfunction BuyPackageComponent_div_4_div_2_Conditional_1_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" The Flexible Package ensures you have the time and flexibility you need. \");\n  }\n}\nfunction BuyPackageComponent_div_4_div_2_Conditional_1_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" You\\u2019re all set with the extra benefits of the Premium Package. \");\n  }\n}\nfunction BuyPackageComponent_div_4_div_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵelement(1, \"i\", 106);\n    i0.ɵɵelementStart(2, \"div\")(3, \"div\", 107);\n    i0.ɵɵtext(4, \"Great Choice!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 108);\n    i0.ɵɵtemplate(6, BuyPackageComponent_div_4_div_2_Conditional_1_Conditional_6_Template, 1, 0)(7, BuyPackageComponent_div_4_div_2_Conditional_1_Conditional_7_Template, 1, 0);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵconditional(6, ctx_r1.selectedPackageToBuy.type === \"Flexible\" ? 6 : 7);\n  }\n}\nfunction BuyPackageComponent_div_4_div_2_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r25 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-buy-package-suggestion-box\", 109);\n    i0.ɵɵlistener(\"switchBackToPackageTypeSelected\", function BuyPackageComponent_div_4_div_2_Conditional_2_Template_app_buy_package_suggestion_box_switchBackToPackageTypeSelected_1_listener($event) {\n      i0.ɵɵrestoreView(_r25);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.switchBackToPackageType($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"preselectedPackage\", ctx_r1.selectedPackageToBuy);\n  }\n}\nfunction BuyPackageComponent_div_4_div_2_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵtext(1, \"* First Name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuyPackageComponent_div_4_div_2_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵtext(1, \"* Last Name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuyPackageComponent_div_4_div_2_div_35_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵtext(1, \"* T.I.N. / V.A.T. is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuyPackageComponent_div_4_div_2_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 87);\n    i0.ɵɵtext(2, \"* T.I.N. / V.A.T.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 111);\n    i0.ɵɵtemplate(4, BuyPackageComponent_div_4_div_2_div_35_div_4_Template, 2, 0, \"div\", 89);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.isSubmitted || ctx_r1.errorControl.tin.touched) && (ctx_r1.errorControl.tin.errors == null ? null : ctx_r1.errorControl.tin.errors.required));\n  }\n}\nfunction BuyPackageComponent_div_4_div_2_div_36_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵtext(1, \"* Tax Office is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuyPackageComponent_div_4_div_2_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"div\", 87);\n    i0.ɵɵtext(2, \"Tax Office\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 112);\n    i0.ɵɵtemplate(4, BuyPackageComponent_div_4_div_2_div_36_div_4_Template, 2, 0, \"div\", 89);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.isSubmitted || ctx_r1.errorControl.tax.touched) && (ctx_r1.errorControl.tax.errors == null ? null : ctx_r1.errorControl.tax.errors.required));\n  }\n}\nfunction BuyPackageComponent_div_4_div_2_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵtext(1, \"* Country is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuyPackageComponent_div_4_div_2_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 113);\n  }\n}\nfunction BuyPackageComponent_div_4_div_2_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵtext(1, \"* Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuyPackageComponent_div_4_div_2_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵtext(1, \"* Phone is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuyPackageComponent_div_4_div_2_div_60_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵtext(1, \"* Street Adrdess is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuyPackageComponent_div_4_div_2_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵtext(1, \"* Street Number is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuyPackageComponent_div_4_div_2_div_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵtext(1, \"* City is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuyPackageComponent_div_4_div_2_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵtext(1, \"* Postcode is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuyPackageComponent_div_4_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 74);\n    i0.ɵɵtemplate(1, BuyPackageComponent_div_4_div_2_Conditional_1_Template, 8, 1, \"div\", 75)(2, BuyPackageComponent_div_4_div_2_Conditional_2_Template, 2, 1, \"ng-container\");\n    i0.ɵɵelementStart(3, \"form\", 76)(4, \"div\", 77)(5, \"div\", 78);\n    i0.ɵɵtext(6, \" Select Billing Type \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 79)(8, \"div\", 80)(9, \"div\", 81)(10, \"p-radioButton\", 82);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuyPackageComponent_div_4_div_2_Template_p_radioButton_ngModelChange_10_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedBillingType, $event) || (ctx_r1.selectedBillingType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"click\", function BuyPackageComponent_div_4_div_2_Template_p_radioButton_click_10_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.chooseReceipt());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"label\", 83);\n    i0.ɵɵtext(12, \"Receipt\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 81)(14, \"p-radioButton\", 84);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuyPackageComponent_div_4_div_2_Template_p_radioButton_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedBillingType, $event) || (ctx_r1.selectedBillingType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"click\", function BuyPackageComponent_div_4_div_2_Template_p_radioButton_click_14_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.chooseInvoice());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"label\", 85);\n    i0.ɵɵtext(16, \"Invoice\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(17, \"div\", 86)(18, \"div\", 87);\n    i0.ɵɵtext(19, \"* First Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(20, \"input\", 88);\n    i0.ɵɵtemplate(21, BuyPackageComponent_div_4_div_2_div_21_Template, 2, 0, \"div\", 89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 86)(23, \"div\", 87);\n    i0.ɵɵtext(24, \"* Last Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(25, \"input\", 90);\n    i0.ɵɵtemplate(26, BuyPackageComponent_div_4_div_2_div_26_Template, 2, 0, \"div\", 89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 86)(28, \"div\", 87);\n    i0.ɵɵtext(29, \"Company (optional)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(30, \"input\", 91);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 86)(32, \"div\", 87);\n    i0.ɵɵtext(33, \"Profession (optional)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(34, \"input\", 92);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(35, BuyPackageComponent_div_4_div_2_div_35_Template, 5, 1, \"div\", 93)(36, BuyPackageComponent_div_4_div_2_div_36_Template, 5, 1, \"div\", 93);\n    i0.ɵɵelementStart(37, \"div\", 94)(38, \"div\", 87);\n    i0.ɵɵtext(39, \"* Country\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(40, \"app-prime-input-dropdown\", 95);\n    i0.ɵɵtemplate(41, BuyPackageComponent_div_4_div_2_div_41_Template, 2, 0, \"div\", 89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(42, BuyPackageComponent_div_4_div_2_div_42_Template, 1, 0, \"div\", 96);\n    i0.ɵɵelementStart(43, \"div\", 86)(44, \"div\", 87);\n    i0.ɵɵtext(45, \"* Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(46, \"input\", 97);\n    i0.ɵɵtemplate(47, BuyPackageComponent_div_4_div_2_div_47_Template, 2, 0, \"div\", 89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"div\", 86)(49, \"div\", 87);\n    i0.ɵɵtext(50, \"* Phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"span\", 98)(52, \"app-prime-input-dropdown\", 99);\n    i0.ɵɵlistener(\"valueSelected\", function BuyPackageComponent_div_4_div_2_Template_app_prime_input_dropdown_valueSelected_52_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onPhoneCodeChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"span\", 100);\n    i0.ɵɵelement(54, \"input\", 101);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(55, BuyPackageComponent_div_4_div_2_div_55_Template, 2, 0, \"div\", 89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"div\", 86)(57, \"div\", 87);\n    i0.ɵɵtext(58, \"* Street Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(59, \"input\", 102);\n    i0.ɵɵtemplate(60, BuyPackageComponent_div_4_div_2_div_60_Template, 2, 0, \"div\", 89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"div\", 86)(62, \"div\", 87);\n    i0.ɵɵtext(63, \"* Street No\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(64, \"input\", 103);\n    i0.ɵɵtemplate(65, BuyPackageComponent_div_4_div_2_div_65_Template, 2, 0, \"div\", 89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"div\", 86)(67, \"div\", 87);\n    i0.ɵɵtext(68, \"* City\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(69, \"input\", 104);\n    i0.ɵɵtemplate(70, BuyPackageComponent_div_4_div_2_div_70_Template, 2, 0, \"div\", 89);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(71, \"div\", 86)(72, \"div\", 87);\n    i0.ɵɵtext(73, \"* Postcode\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(74, \"input\", 105);\n    i0.ɵɵtemplate(75, BuyPackageComponent_div_4_div_2_div_75_Template, 2, 0, \"div\", 89);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ctx_r1.hasSwitchedPackage() ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(2, (ctx_r1.selectedPackageToBuy == null ? null : ctx_r1.selectedPackageToBuy.type) !== \"Premium\" && !ctx_r1.hasSwitchedPackage() ? 2 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.form);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedBillingType);\n    i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(32, _c2));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedBillingType);\n    i0.ɵɵproperty(\"ngModelOptions\", i0.ɵɵpureFunction0(33, _c2));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.isSubmitted || ctx_r1.errorControl.fname.touched) && (ctx_r1.errorControl.fname.errors == null ? null : ctx_r1.errorControl.fname.errors.required));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.isSubmitted || ctx_r1.errorControl.lname.touched) && (ctx_r1.errorControl.lname.errors == null ? null : ctx_r1.errorControl.lname.errors.required));\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isInvoice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isInvoice);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"parentForm\", ctx_r1.form)(\"countries\", ctx_r1.countries)(\"inputName\", \"country\")(\"withFlags\", true)(\"placeholder\", \"Country of Residence\")(\"selectedItemValue\", ctx_r1.selectedResidenceCountry);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.isSubmitted || ctx_r1.errorControl.country.touched) && (ctx_r1.errorControl.country.errors == null ? null : ctx_r1.errorControl.country.errors.required));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isReceipt);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.isSubmitted || ctx_r1.errorControl.email.touched) && (ctx_r1.errorControl.email.errors == null ? null : ctx_r1.errorControl.email.errors.required));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"parentForm\", ctx_r1.form)(\"countries\", ctx_r1.phoneCodes)(\"filter\", true)(\"inputName\", \"phoneCode\")(\"placeholder\", \"select\")(\"withFlags\", true)(\"selectedItemValue\", ctx_r1.selectedPhoneCode);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.isSubmitted || ctx_r1.errorControl.phone.touched) && (ctx_r1.errorControl.phone.errors == null ? null : ctx_r1.errorControl.phone.errors.required));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.isSubmitted || ctx_r1.errorControl.street.touched) && (ctx_r1.errorControl.street.errors == null ? null : ctx_r1.errorControl.street.errors.required));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.isSubmitted || ctx_r1.errorControl.number.touched) && (ctx_r1.errorControl.number.errors == null ? null : ctx_r1.errorControl.number.errors.required));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.isSubmitted || ctx_r1.errorControl.city.touched) && (ctx_r1.errorControl.city.errors == null ? null : ctx_r1.errorControl.city.errors.required));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.isSubmitted || ctx_r1.errorControl.postcode.touched) && (ctx_r1.errorControl.postcode.errors == null ? null : ctx_r1.errorControl.postcode.errors.required));\n  }\n}\nfunction BuyPackageComponent_div_4_div_3_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Invoice\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuyPackageComponent_div_4_div_3_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Receipt\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuyPackageComponent_div_4_div_3_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 126)(1, \"div\");\n    i0.ɵɵtext(2, \" Tax Office \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 125);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.buyerUserDetails.tax, \" \");\n  }\n}\nfunction BuyPackageComponent_div_4_div_3_div_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 138)(1, \"div\");\n    i0.ɵɵtext(2, \" *T.I.N. / V.A.T \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 125);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.buyerUserDetails.tin, \" \");\n  }\n}\nfunction BuyPackageComponent_div_4_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r26 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 114)(1, \"div\", 115)(2, \"div\", 116);\n    i0.ɵɵtemplate(3, BuyPackageComponent_div_4_div_3_span_3_Template, 2, 0, \"span\", 9)(4, BuyPackageComponent_div_4_div_3_span_4_Template, 2, 0, \"span\", 9);\n    i0.ɵɵtext(5, \" Details \");\n    i0.ɵɵelementStart(6, \"div\", 117);\n    i0.ɵɵlistener(\"click\", function BuyPackageComponent_div_4_div_3_Template_div_click_6_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const receiptDetailsArrowSrc_r27 = i0.ɵɵreference(8);\n      const receiptDetailsSection_r28 = i0.ɵɵreference(10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleSection(receiptDetailsSection_r28, receiptDetailsArrowSrc_r27));\n    });\n    i0.ɵɵelement(7, \"img\", 118, 1);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 119, 2)(11, \"div\", 120)(12, \"div\", 121)(13, \"div\", 122);\n    i0.ɵɵtext(14, \" Personal Info \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 123)(16, \"div\", 124)(17, \"div\");\n    i0.ɵɵtext(18, \" First Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 125);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 124)(22, \"div\");\n    i0.ɵɵtext(23, \" Last Name \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 125);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 124)(27, \"div\");\n    i0.ɵɵtext(28, \" Company Title \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 125);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 124)(32, \"div\");\n    i0.ɵɵtext(33, \" Proffesion \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 125);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(36, \"div\", 121)(37, \"div\", 122);\n    i0.ɵɵtext(38, \" Location \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\", 123)(40, \"div\", 126)(41, \"div\");\n    i0.ɵɵtext(42, \" Street address \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 125);\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(45, \"div\", 126)(46, \"div\");\n    i0.ɵɵtext(47, \" Street No \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"div\", 125);\n    i0.ɵɵtext(49);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"div\", 126)(51, \"div\");\n    i0.ɵɵtext(52, \" Country \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"div\", 125);\n    i0.ɵɵtext(54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(55, \"div\", 126)(56, \"div\");\n    i0.ɵɵtext(57, \" City \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 125);\n    i0.ɵɵtext(59);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(60, \"div\", 126)(61, \"div\");\n    i0.ɵɵtext(62, \" Postcode \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"div\", 125);\n    i0.ɵɵtext(64);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(65, BuyPackageComponent_div_4_div_3_div_65_Template, 5, 1, \"div\", 127);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(66, \"div\", 121)(67, \"div\", 122);\n    i0.ɵɵtext(68, \" Contact Info \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(69, \"div\", 123)(70, \"div\", 124)(71, \"div\");\n    i0.ɵɵtext(72, \" E-mail \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"div\", 125);\n    i0.ɵɵtext(74);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(75, \"div\", 126)(76, \"div\");\n    i0.ɵɵtext(77, \" Telephone \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(78, \"div\", 125);\n    i0.ɵɵtext(79);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(80, BuyPackageComponent_div_4_div_3_div_80_Template, 5, 1, \"div\", 128);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(81, \"div\", 129);\n    i0.ɵɵtext(82, \"Terms & Conditions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(83, \"p\", 130);\n    i0.ɵɵtext(84, \"Your personal data will be used to process your order, support your experience throughout this website, and for other purposes described in our privacy policy.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(85, \"div\", 115)(86, \"div\", 116);\n    i0.ɵɵtext(87, \" Read Full Terms & Conditions \");\n    i0.ɵɵelementStart(88, \"div\", 117);\n    i0.ɵɵlistener(\"click\", function BuyPackageComponent_div_4_div_3_Template_div_click_88_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const termsArrowSrc_r29 = i0.ɵɵreference(90);\n      const termsSection_r30 = i0.ɵɵreference(92);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleSection(termsSection_r30, termsArrowSrc_r29));\n    });\n    i0.ɵɵelement(89, \"img\", 118, 3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(91, \"div\", 131, 4)(93, \"div\", 120)(94, \"div\", 121)(95, \"div\", 132)(96, \"div\", 133)(97, \"div\", 134)(98, \"div\", 135);\n    i0.ɵɵtext(99, \"Terms and Conditions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(100, \"br\");\n    i0.ɵɵtext(101, \" Welcome to MyLingoTrip, the Online Language School to learn languages (Dutch, English, Italian, Greek, German, French, Swedish, Spanish, Korean, Chinese, Japanese, Russian). This agreement (the \\u201CAgreement\\u201D) represents a part of the contract between you and MyLingoTrip and, if applicable, the fee-based subscription services therein. \");\n    i0.ɵɵelement(102, \"br\");\n    i0.ɵɵtext(103, \" By registering, purchasing a gift subscription from, or using MyLingoTrips\\u2019 services, you agree to accept the terms and conditions which govern access and your use of the mylingotrip.com website. The terms and conditions constitute a legal arrangement between you, as a user of our services and MyLingoTrip for the services that are offered through the website. Users of the MyLingoTrip website are expected to conform, without qualification to the listed terms and conditions of use set out below in all circumstances. \");\n    i0.ɵɵelement(104, \"br\");\n    i0.ɵɵtext(105, \" Acceptance of the Terms Changes to these Terms General User Responsibilities (Use of Service) Access and Interference Student Registration Students \\u2013 Booking and Payment Policy Students \\u2013 Fees and Cancellation Policy Indemnification Language Teachers \\u2013 Compliance with Booking Procedure Language Teachers \\u2013 Delivery of Language Training Services and Registration Your rights Privacy and Security Policy Impart Among Users and Termination of Account Applied Laws, Regulations, and Jurisdiction Miscellaneous 1. Acceptance of the Terms By using the information, features, functionality, and tools located at MyLingoTrip website (with or without the MyLingoTrip \\u201Cservice\\u201D or \\u201Cservices\\u201D), you agree to conform to this Arrangement and the Terms, regardless of being either a \\u201Cvisitor\\u201D (meaning you merely browse the MyLingoTrip\\u2019 website) or a \\u201Ccustomer\\u201D (meaning you have registered with MyLingoTrip). The term \\u201Cyou\\u201D or \\u201Cuser\\u201D refers to a visitor or a customer. \");\n    i0.ɵɵelement(106, \"br\");\n    i0.ɵɵtext(107, \" The term \\u201Cwe\\u201D refers to MyLingoTrip. The term \\u201Cmaterial\\u201D relates to all class material used in our classes. If you wish to become a customer, you could communicate your wish with other customers and/or make use of our service/services. However, you have to read MyLingoTrip\\u2019s terms and conditions and indicate your acceptance during the registration process. \");\n    i0.ɵɵelement(108, \"br\");\n    i0.ɵɵtext(109, \" If you do not agree with all the terms and conditions, do not use the MyLingoTrip website and/or services. \");\n    i0.ɵɵelement(110, \"br\");\n    i0.ɵɵtext(111, \" Please review all the terms and conditions thoroughly before using the MyLingoTrip website and or/services. \");\n    i0.ɵɵelement(112, \"br\");\n    i0.ɵɵtext(113, \" 2. Changes to these Terms MyLingoTrip has the right to modify this Agreement and any policies affecting or relating to the Site. Your continued use of the Site following notice of the Agreement modification will be considered an acceptance of the modified terms and conditions of use, although, if major implications are applied, you will be asked for providing your electronic signature. Your alternative upon dissatisfaction with the modified terms of use is to cancel your subscription. Moreover, Mylingotrip reserves the right to modify, suspend or discontinue the Site at any time. \");\n    i0.ɵɵelement(114, \"br\");\n    i0.ɵɵtext(115, \" 3. General User Responsibilities All registered users agree not to post, distribute, publish or by any other means convey offensive material, including discourteous language, photographic material, or any other kind of material of an offensive or sexual nature. Violation of this agreement conditions by any user will result in termination of that users\\u2019 registration and will result in notifying the authorities and possibly taking legal action. \");\n    i0.ɵɵelement(116, \"br\");\n    i0.ɵɵtext(117, \" All registered users agree not to post, distribute, publish, or by any other means place any computer code on the MyLingoTrip website, which directly or indirectly links to another Site without our express consent. \");\n    i0.ɵɵelement(118, \"br\");\n    i0.ɵɵtext(119, \" All registered users agree not to distribute or share their username and /or password details with any other individual or company to provide to other individuals the permission to utilize the services provided through the MyLingoTrip website. \");\n    i0.ɵɵelement(120, \"br\");\n    i0.ɵɵtext(121, \" All registered users self-certify that they can form legally binding contracts under their applicable regime of their country of residence. \");\n    i0.ɵɵelement(122, \"br\");\n    i0.ɵɵtext(123, \" 4. Access and Interference With your registration at MyLingoTrip website you agree that you will not: \");\n    i0.ɵɵelement(124, \"br\");\n    i0.ɵɵtext(125, \" *Use any robot, spider, scraper, deep link or other similar automated data gathering or extraction tools, programs, algorithms or methods to get access to, acquire, copy or monitor mylingotrip.com or any part of mylingotrip.com, without MyLingoTrips\\u2019 express written consent, which will be withheld in MyLingoTrips\\u2019 sole discretion; \");\n    i0.ɵɵelement(126, \"br\");\n    i0.ɵɵtext(127, \" *Use or attempt to use any engine, software, tool, agent, or other device or mechanism (including without limitation browsers, spiders, robots, avatars or intelligent agents) to navigate or search the MyLingoTrip website other than the search engines and search agents available through the service and other than generally available third-party web browsers (such as Microsoft Explorer); \");\n    i0.ɵɵelement(128, \"br\");\n    i0.ɵɵtext(129, \" *Post or transmit any file containing viruses, worms, Trojan horses or any other contaminating or malicious software/features, or whatever might interfere with the proper functioning of mylingotrip.com and/or its services; \");\n    i0.ɵɵelement(130, \"br\");\n    i0.ɵɵtext(131, \" *Attempt to decipher, decompile, disassemble, or reverse-engineer any of the software incorporating or in any way constituting a part of mylingotrip.com and/or its services. \");\n    i0.ɵɵelement(132, \"br\");\n    i0.ɵɵtext(133, \" 5. Student Registration When registering as a student at MyLingoTrip, you will be required to provide personal information not limited to your preferred username and password; your email address; the language of your choice; your country of residence; your local currency and local time zone. By registering an account at mylingotrip.com, you automatically confirm that you accept the terms and conditions of use. All language students agree to the use of their personal information following the web site privacy policy. \");\n    i0.ɵɵelement(134, \"br\");\n    i0.ɵɵtext(135, \" 6. Students \\u2013 Booking and Payment Policy After providing to the website of MyLingoTrip the requested amount of fees, the student should provide the remaining amount (the price of the total study-package minus the websites\\u2019 fees) to his/her assigned teacher. MyLingoTrip is obliged to issue the student\\u2019s receipt only for the fees that are provided to the platform (mylingotrip.com). The remaining amount which is provided by the student to his/her teacher is the teacher\\u2019s fee, and there is no involvement of the MyLingoTrip platform in providing the receipt that corresponds to this amount to the student. \");\n    i0.ɵɵelement(136, \"br\");\n    i0.ɵɵtext(137, \" Any action or attempt to avoid providing these fees according to the MyLingoTrip booking policy or to arrange or attempt to arrange lessons outside the MyLingoTrip platform is strictly prohibited and will result in closing the student\\u2019s account, and legal action might be taken for the recovery of the amount of money deemed owing. We reserve the right to amend or alter these fees at any time. All prices displayed publicly on mylingotrip.com are inclusive of all fees. \");\n    i0.ɵɵelement(138, \"br\");\n    i0.ɵɵtext(139, \" 7. Students \\u2013 Fees and Cancellation Policy You will be required to register and create an account with MyLingoTrip (the \\u201CAccount\\u201D) to have access to the website\\u2019s services. Information gathered through the registration process and information related to your account will be subject to these terms as well as to our \\u201CPrivacy and Security Policy.\\u201D You warrant that the information provided for the creation of a student\\u2019s account is truthful, accurate, and complete and that this information will stay truthful, accurate, and complete as long as you are registered at the MyLingoTrip website. \");\n    i0.ɵɵelement(140, \"br\");\n    i0.ɵɵtext(141, \" Certain material and functionalities of the website are available only after the purchase of an online language package (the 3 \\u201CPackage\\u201D options). According to this Agreement, you are responsible for any charges associated with registering to the website. MyLingoTrip reserves the right to change the price of the actual package fees or institute new fees at any time, upon reasonable notice posted on the website and emailed to current customers. \");\n    i0.ɵɵelement(142, \"br\");\n    i0.ɵɵtext(143, \" By purchasing a Study Package, you agree to be bound to the following terms: \");\n    i0.ɵɵelement(144, \"br\");\n    i0.ɵɵtext(145, \" MyLingoTrip ensures that every credit card/PayPal transaction you make is 100% secure. If you wish to check on the status of your subscription, please log in and visit the \\u201CMy Account\\u201D page in your Login area. \");\n    i0.ɵɵelement(146, \"br\");\n    i0.ɵɵtext(147, \" All language courses fees are recurring and non-refundable after your purchase. If you do not wish to continue studying at MyLingoTrip, you have to deactivate your account at the \\u201CMy Account\\u201D page at your login area or provide us with a written (email) notice. \");\n    i0.ɵɵelement(148, \"br\");\n    i0.ɵɵtext(149, \" An exception to the prior paragraph is our \\u201C7-day money-back guarantee\\u201D. Within seven days after the purchase of a study package, you have the right to cancel your language course and receive a full refund by providing us with a written (email) notice. \");\n    i0.ɵɵelement(150, \"br\");\n    i0.ɵɵtext(151, \" MyLingoTrip is obliged to issue the student\\u2019s receipt only for the amount provided to the platform (mylingotrip.com). The remaining amount, provided by the student/parent to the teacher, is the Language Tutor\\u2019s fee, and there is no involvement. \");\n    i0.ɵɵelement(152, \"br\");\n    i0.ɵɵtext(153, \" Lesson packages booked and not used within three months will be canceled. Thus, if those remaining hours have not been used within three months after the date a student/parent purchased a class package, those unused sessions are lost and will not be refunded. Moreover, when a student chooses to conduct only one (1) session weekly, then the activation period expands to five (5) months. As a result, MyLingoTrip will not refund these particular students, only after their expiration date is due. \");\n    i0.ɵɵelement(154, \"br\");\n    i0.ɵɵtext(155, \" In case of a cancellation: A scheduled lesson with a teacher has to be canceled at least 48 hours before the actual appointment for the lesson. Otherwise, the arranged hour is going to be deducted from the purchased lesson-package, without any further announcement or refund. \");\n    i0.ɵɵelement(156, \"br\");\n    i0.ɵɵtext(157, \" For groups of 2 or 3: In case one of the students cancels the lesson but the other one attends it, the hour will be charged as usual and no extra private lesson will be provided for the student who missed the lesson. \");\n    i0.ɵɵelement(158, \"br\");\n    i0.ɵɵtext(159, \" Free Trial Lesson: The free trial is available only to first-time users of the MyLingoTrip website. \");\n    i0.ɵɵelement(160, \"br\");\n    i0.ɵɵtext(161, \" 8. Indemnification You agree to indemnify, protect and do not cause any misconduct to the MyLingoTrip platform and it\\u2019s affiliates, partners, and employees due to any accountability, alleges, costs, or damages arising from your use of the MyLingoTrip website and/or your breach of this Agreement, under the terms and conditions set out herein. \");\n    i0.ɵɵelement(162, \"br\");\n    i0.ɵɵtext(163, \" 9. Language Teachers \\u2013 Compliance with Booking Procedure All registered language tutors agree not to engage in or promote any activity to arrange lessons with a language student outside the MyLingoTrip website. The activity of this nature is specifically prohibited under our terms and conditions of service and undermines the trust, security, and legitimacy of MyLingoTrip. \");\n    i0.ɵɵelement(164, \"br\");\n    i0.ɵɵtext(165, \" Activity specifically not permitted includes: \");\n    i0.ɵɵelement(166, \"br\");\n    i0.ɵɵtext(167, \" Communication via email, Skype, or other means to schedule and arrange a lesson with a language student in a way that is intended to avoid the payment of the booking fee to the MyLingoTrip website. \");\n    i0.ɵɵelement(168, \"br\");\n    i0.ɵɵtext(169, \" Failure of a registered language tutor to adhere to these terms and conditions and in particular any action whatsoever taken by a registered language teacher to privately arrange tuition time with a registered MyLingoTrip language student, avoiding to provide the booking fee to the MyLingoTrip website, may result in the termination of the cooperation between the MyLingoTrip platform and the language teacher that committed this action, as well as excluding the particular language student from his/her rights to use the MyLingoTrip website again and/or legal action against that language tutor and/or the language student and/or his/her parent might be taken. \");\n    i0.ɵɵelement(170, \"br\");\n    i0.ɵɵtext(171, \" 10. Language Teachers \\u2013 Delivery of Language Training Services and Registration All registered MyLingoTrip language teachers agree to provide high-quality language training services to any registered MyLingoTrip language student in a timely fashion. We reserve the right to change the required standard at any time. When registering as a language teacher you will be required to provide personal information. You will also be requested to inform the MyLingoTrip administrative team about your Skype username and password (All users acknowledge that mylingotrip.com is not responsible for the quality of the Skype interface or the quality of communications between users using Skype). \");\n    i0.ɵɵelement(172, \"br\");\n    i0.ɵɵtext(173, \" 11. Your Rights MyLingoTrip offers you a non-transferable, non-exclusive, definite right to access, use and array the content of this platform, provided that you comply with this Agreement and the Terms as set out in full. The materials of this Site are provided privately to you for your non-commercial use. Specific services of this Site are available only to registered customers of MyLingoTrip. \");\n    i0.ɵɵelement(174, \"br\");\n    i0.ɵɵtext(175, \" By purchasing a subscription or register for a Free Trial Lesson of MyLingoTrip, you agree to be bound by the terms and conditions as set out in full. \");\n    i0.ɵɵelement(176, \"br\");\n    i0.ɵɵtext(177, \" 12. Privacy and Security Policy Your use of our Site and any personal information or other information about you collected by mylingotrip.com through, or in congruence with, the Site is subject to our Privacy and Security Policy. For questions about our online privacy and security policy please refer to our administration team. \");\n    i0.ɵɵelement(178, \"br\");\n    i0.ɵɵtext(179, \" 13. Impart Among Users and Termination of Account MyLingoTrip reserves the right to monitor communications between users. All users consent to the supervision of communication, via any of the functions we make available (i.e., Skype, Notes, G-mail) to protect the integrity and security of mylingotrip.com, protect users from abuse, detecting fraud and attempts to avoid our fee structure. \");\n    i0.ɵɵelement(180, \"br\");\n    i0.ɵɵtext(181, \" Language tutors may close their account if they notify MyLingoTrip\\u2019s administration team at least three (3) months prior to the breaking of engagement. They are subject to the completion of any overdue or unresolved lessons, and only in case MyLingoTrip\\u2019s administration team and their students are notified they can close their accounts. MyLingoTrip will take further actions for transferring the students to another Language Tutor matching their needs and availability slots. \");\n    i0.ɵɵelement(182, \"br\");\n    i0.ɵɵtext(183, \" Language students may close their account at any time but shall remain accountable to MyLingoTrip and any language tutor for any unpaid lesson fees. \");\n    i0.ɵɵelement(184, \"br\");\n    i0.ɵɵtext(185, \" 14. Applied Laws, Regulations, and Jurisdiction These terms and conditions shall be regulated in all respects by the laws of Greece without affecting any principle that may require the application of the law of another jurisdiction. The arrangements of this agreement are severable and you agree that any dispute or claim you may have against MyLingoTrip must be resolved by a court located in Greece. Hence, you herewith submit to the personal jurisdiction of the courts located in Greece for prosecuting or litigating such disputes or claims. Any cause of action you may have in regards to MyLingoTrip must be commenced within one month after it arises, or the cause of action is repudiated. \");\n    i0.ɵɵelement(186, \"br\");\n    i0.ɵɵtext(187, \" 15. Miscellaneous If any portion of this Agreement is deemed unlawful, void or unenforceable by any arbitrator or court of competent jurisdiction, this Agreement as a whole shall not be deemed unlawful, void or unenforceable, but only that portion of this Agreement that is unlawful, void or unenforceable shall be stricken from this Agreement. You agree that if MyLingoTrip does not exercise or enforce any legal right or remedy which is contained in the Agreement (or which MyLingoTrip has the benefit, according to any applicable law), this will not be taken to be a formal waiver of MyLingoTrips\\u2019 rights and that those rights or remedies will still be available to MyLingoTrip. \");\n    i0.ɵɵelement(188, \"br\");\n    i0.ɵɵtext(189, \" All covenants, agreements, representations, and warranties made in this Agreement shall survive your acceptance of this Agreement and the termination of this Agreement. \");\n    i0.ɵɵelement(190, \"br\");\n    i0.ɵɵtext(191, \" This Agreement represents the entire understanding and agreement between the recipient and MyLingoTrip and supersedes and replaces all oral or written agreements concerning the subject matter hereof. \");\n    i0.ɵɵelement(192, \"br\");\n    i0.ɵɵtext(193, \" In case you may have any questions about this policy statement, or about any of MyLingoTrips\\u2019 other policies and practices, please contact us on the contact details laid out at mylingotrip.com. \");\n    i0.ɵɵelement(194, \"br\");\n    i0.ɵɵtext(195, \" MyLingoTrip and related services are, if not otherwise explicitly mentioned, supplied by: \");\n    i0.ɵɵelement(196, \"br\");\n    i0.ɵɵtext(197, \" MyLingoTrip (MLT) \");\n    i0.ɵɵelement(198, \"br\");\n    i0.ɵɵtext(199, \" Telephone Number: 231 5551054 \");\n    i0.ɵɵelement(200, \"br\");\n    i0.ɵɵtext(201, \" Contact us: <EMAIL> \");\n    i0.ɵɵelement(202, \"br\");\n    i0.ɵɵtext(203, \" IT Support: <EMAIL> \");\n    i0.ɵɵelement(204, \"br\");\n    i0.ɵɵtext(205, \" Address: 5 Papandreou Georgiou St., 54645 Thessaloniki, Greece \");\n    i0.ɵɵelementEnd()()()()()()();\n    i0.ɵɵelementStart(206, \"div\", 136)(207, \"p-checkbox\", 137);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function BuyPackageComponent_div_4_div_3_Template_p_checkbox_ngModelChange_207_listener($event) {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedValues, $event) || (ctx_r1.selectedValues = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"onChange\", function BuyPackageComponent_div_4_div_3_Template_p_checkbox_onChange_207_listener() {\n      i0.ɵɵrestoreView(_r26);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.agree());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isInvoice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isReceipt);\n    i0.ɵɵadvance(5);\n    i0.ɵɵattribute(\"id\", \"receipt-section\")(\"open\", true);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.buyerUserDetails.fname, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.buyerUserDetails.lname, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.buyerUserDetails.company, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.buyerUserDetails.profession, \" \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.buyerUserDetails.streetName, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.buyerUserDetails.streetNumber, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.buyerUserDetails.country, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.buyerUserDetails.city, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.buyerUserDetails.postcode, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isInvoice);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.buyerUserDetails.email, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.buyerUserDetails.phone, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isInvoice);\n    i0.ɵɵadvance(11);\n    i0.ɵɵattribute(\"id\", \"receipt-section-details\")(\"open\", true);\n    i0.ɵɵadvance(116);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r1.selectedValues);\n  }\n}\nfunction BuyPackageComponent_div_4_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 139);\n    i0.ɵɵelement(2, \"img\", 140)(3, \"div\", 141);\n    i0.ɵɵelementStart(4, \"div\", 142);\n    i0.ɵɵtext(5, \" Thank you for your order! \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 143);\n    i0.ɵɵtext(7, \" We have received your payment. Your lessons will be available on your profile shortly. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 144)(9, \"div\", 145);\n    i0.ɵɵelement(10, \"button\", 146);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction0(1, _c3));\n  }\n}\nfunction BuyPackageComponent_div_4_div_5_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 150);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.mltPricesHourly[ctx_r1.selectedHoursIndex].hours, \" \");\n  }\n}\nfunction BuyPackageComponent_div_4_div_5_span_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"months\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BuyPackageComponent_div_4_div_5_span_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"(+\", ctx_r1.selectedPackageToBuy.expiresPlus, \")\");\n  }\n}\nfunction BuyPackageComponent_div_4_div_5_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 151)(1, \"div\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" Expiration date extends by \", ctx_r1.selectedPackageToBuy.expiresPlus, \" \");\n  }\n}\nfunction BuyPackageComponent_div_4_div_5_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 151)(1, \"div\");\n    i0.ɵɵtext(2, \" AI Chat \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuyPackageComponent_div_4_div_5_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 151)(1, \"div\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedPackageToBuy.pause, \" pause availability \");\n  }\n}\nfunction BuyPackageComponent_div_4_div_5_div_48_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 150);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedPrice, \"\\u20AC \");\n  }\n}\nfunction BuyPackageComponent_div_4_div_5_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 151)(1, \"div\");\n    i0.ɵɵtext(2, \" Regular Price \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, BuyPackageComponent_div_4_div_5_div_48_div_3_Template, 2, 1, \"div\", 152);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedPrice);\n  }\n}\nfunction BuyPackageComponent_div_4_div_5_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 151)(1, \"div\");\n    i0.ɵɵtext(2, \" Additional Price \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 150);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedPackageToBuy.costPlus, \"\\u20AC \");\n  }\n}\nfunction BuyPackageComponent_div_4_div_5_ng_container_55_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"div\", 153);\n    i0.ɵɵelementStart(2, \"div\", 160)(3, \"div\", 161);\n    i0.ɵɵtext(4, \" Booking Fee \");\n    i0.ɵɵelementStart(5, \"span\", 162);\n    i0.ɵɵtext(6, \"Pay now \");\n    i0.ɵɵelementStart(7, \"img\", 163);\n    i0.ɵɵlistener(\"click\", function BuyPackageComponent_div_4_div_5_ng_container_55_Template_img_click_7_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onSplitPaymentPayNowTooltipSelected());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 150);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(10, \"div\", 153);\n    i0.ɵɵelementStart(11, \"div\", 160)(12, \"div\", 161);\n    i0.ɵɵtext(13, \" Remaining Balance \");\n    i0.ɵɵelementStart(14, \"span\", 162);\n    i0.ɵɵtext(15, \"Pay later \");\n    i0.ɵɵelementStart(16, \"img\", 163);\n    i0.ɵɵlistener(\"click\", function BuyPackageComponent_div_4_div_5_ng_container_55_Template_img_click_16_listener() {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onSplitPaymentPayLaterTooltipSelected());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(17, \"div\", 150);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getTotalSplitPaymentAmount(), \"\\u20AC \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getRemainingAmount(), \"\\u20AC \");\n  }\n}\nfunction BuyPackageComponent_div_4_div_5_ng_container_61_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r33 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 164);\n    i0.ɵɵlistener(\"click\", function BuyPackageComponent_div_4_div_5_ng_container_61_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r33);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.next());\n    });\n    i0.ɵɵtext(2, \"NEXT \");\n    i0.ɵɵelement(3, \"span\", 165);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction BuyPackageComponent_div_4_div_5_div_62_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 167);\n    i0.ɵɵlistener(\"click\", function BuyPackageComponent_div_4_div_5_div_62_ng_container_1_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.purchase());\n    });\n    i0.ɵɵelementStart(2, \"div\", 168)(3, \"div\", 169)(4, \"span\", 170);\n    i0.ɵɵtext(5, \"Pay Now\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 130);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(8, \"span\", 165);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.getTotalSplitPaymentAmount(), \"\\u20AC\");\n  }\n}\nfunction BuyPackageComponent_div_4_div_5_div_62_ng_template_2_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r35 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 171);\n    i0.ɵɵlistener(\"click\", function BuyPackageComponent_div_4_div_5_div_62_ng_template_2_div_0_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r35);\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.purchase());\n    });\n    i0.ɵɵtext(2, \"Buy \");\n    i0.ɵɵelement(3, \"img\", 172);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction BuyPackageComponent_div_4_div_5_div_62_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, BuyPackageComponent_div_4_div_5_div_62_ng_template_2_div_0_Template, 4, 0, \"div\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStep == 3);\n  }\n}\nfunction BuyPackageComponent_div_4_div_5_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 166);\n    i0.ɵɵtemplate(1, BuyPackageComponent_div_4_div_5_div_62_ng_container_1_Template, 9, 1, \"ng-container\", 67)(2, BuyPackageComponent_div_4_div_5_div_62_ng_template_2_Template, 1, 1, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const noSplitPayment_r36 = i0.ɵɵreference(3);\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStep == 3 && ctx_r1.showSplitPayment)(\"ngIfElse\", noSplitPayment_r36);\n  }\n}\nfunction BuyPackageComponent_div_4_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r31 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 147)(1, \"div\", 148);\n    i0.ɵɵtext(2, \" Overview \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 149)(4, \"div\");\n    i0.ɵɵtext(5, \" Language \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 150);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 151)(9, \"div\");\n    i0.ɵɵtext(10, \" Level \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 150);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 151)(14, \"div\");\n    i0.ɵɵtext(15, \" No. of Students \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 150);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 151)(19, \"div\");\n    i0.ɵɵtext(20, \" Hours \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, BuyPackageComponent_div_4_div_5_div_21_Template, 2, 1, \"div\", 152);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 151)(23, \"div\");\n    i0.ɵɵtext(24, \" Package Type \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 150);\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(27, \"div\", 153);\n    i0.ɵɵelementStart(28, \"div\", 151)(29, \"div\", 154);\n    i0.ɵɵtext(30, \" General Info \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 151)(32, \"div\");\n    i0.ɵɵtext(33);\n    i0.ɵɵtemplate(34, BuyPackageComponent_div_4_div_5_span_34_Template, 2, 0, \"span\", 9)(35, BuyPackageComponent_div_4_div_5_span_35_Template, 2, 1, \"span\", 9);\n    i0.ɵɵtext(36, \" starting from the first package lesson \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(37, \"div\", 153);\n    i0.ɵɵelementStart(38, \"div\", 151)(39, \"div\", 154);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(41, BuyPackageComponent_div_4_div_5_div_41_Template, 3, 1, \"div\", 155);\n    i0.ɵɵelementStart(42, \"div\", 151)(43, \"div\");\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(45, BuyPackageComponent_div_4_div_5_div_45_Template, 3, 0, \"div\", 155)(46, BuyPackageComponent_div_4_div_5_div_46_Template, 3, 1, \"div\", 155);\n    i0.ɵɵelement(47, \"div\", 153);\n    i0.ɵɵtemplate(48, BuyPackageComponent_div_4_div_5_div_48_Template, 4, 1, \"div\", 155)(49, BuyPackageComponent_div_4_div_5_div_49_Template, 5, 1, \"div\", 155);\n    i0.ɵɵelementStart(50, \"div\", 151)(51, \"div\");\n    i0.ɵɵtext(52, \" Total Price \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"div\", 150);\n    i0.ɵɵtext(54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(55, BuyPackageComponent_div_4_div_5_ng_container_55_Template, 19, 2, \"ng-container\", 9);\n    i0.ɵɵelementStart(56, \"div\", 156)(57, \"button\", 157);\n    i0.ɵɵlistener(\"click\", function BuyPackageComponent_div_4_div_5_Template_button_click_57_listener() {\n      i0.ɵɵrestoreView(_r31);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.previous());\n    });\n    i0.ɵɵelement(58, \"span\", 14)(59, \"span\", 158);\n    i0.ɵɵtext(60, \" BACK\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(61, BuyPackageComponent_div_4_div_5_ng_container_61_Template, 4, 0, \"ng-container\", 9)(62, BuyPackageComponent_div_4_div_5_div_62_Template, 4, 2, \"div\", 159);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedLanguage.name, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedLevel, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedNumberOfStudents, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedPrice);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedPackageToBuy.type, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" Expiration date is set \", ctx_r1.expiresIn, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedPackageToBuy.type == \"Regular\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedPackageToBuy.type !== \"Regular\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedPackageToBuy.type, \" Info \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedPackageToBuy.type !== \"Regular\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedPackageToBuy.cancelation, \" hours lesson cancellation policy \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedPackageToBuy.type != \"Regular\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedPackageToBuy.type !== \"Regular\" && ctx_r1.selectedPackageToBuy.pause);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedPackageToBuy.type !== \"Regular\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedPackageToBuy.type !== \"Regular\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedPrice + ctx_r1.selectedPackageToBuy.costPlus, \"\\u20AC \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showSplitPayment);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(22, _c4, ctx_r1.currentStep === 4));\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"inactive\", ctx_r1.currentStep == 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStep !== 3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStep == 3);\n  }\n}\nfunction BuyPackageComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtemplate(1, BuyPackageComponent_div_4_div_1_Template, 9, 10, \"div\", 22)(2, BuyPackageComponent_div_4_div_2_Template, 76, 34, \"div\", 23)(3, BuyPackageComponent_div_4_div_3_Template, 208, 20, \"div\", 24)(4, BuyPackageComponent_div_4_div_4_Template, 11, 2, \"div\", 23)(5, BuyPackageComponent_div_4_div_5_Template, 63, 24, \"div\", 25);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@slideInOut\", undefined);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStep == 1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStep == 2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStep == 3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentStep == 4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.inTrial);\n  }\n}\nconst isProductionMode = environment.production;\nexport let BuyPackageComponent = /*#__PURE__*/(() => {\n  class BuyPackageComponent {\n    constructor(authService, activatedRoute, location, classroomService, generalService, packageService, toastService, confirmDialogService, sanitizer, router, renderer, document) {\n      this.authService = authService;\n      this.activatedRoute = activatedRoute;\n      this.location = location;\n      this.classroomService = classroomService;\n      this.generalService = generalService;\n      this.packageService = packageService;\n      this.toastService = toastService;\n      this.confirmDialogService = confirmDialogService;\n      this.sanitizer = sanitizer;\n      this.router = router;\n      this.renderer = renderer;\n      this.document = document;\n      this.inTrial = false;\n      this.hideSection = false;\n      this.user = {};\n      this.subs = new SubSink();\n      this.guid = \"guid-guid\";\n      this.packageToBuyOrderDeails = {};\n      this.phoneCodes = this.generalService.getPhoneCodes();\n      this.currentStep = 1;\n      this.userClassrooms = [];\n      this.classroomId = \"\";\n      this.isNew = false;\n      this.numberOfStudents = [\"1\", \"2\", \"3\"];\n      this.payload = {\n        pk: 'your-public-key',\n        amount: 1000,\n        locale: 'en',\n        txnType: 'tds'\n      };\n      this.selectedClassroom = {};\n      this.selectedPackageToBuy = {};\n      this.selectedLevel = \"A1\";\n      this.selectedDefaultLevel = \"A1\";\n      this.selectedNumberOfStudents = \"1\";\n      this.selectedLanguage = {};\n      this.selectedHoursIndex = 2;\n      this.selectedBillingType = \"receipt\";\n      this.selectedPhoneCode = {};\n      this.selectedResidenceCountry = {};\n      this.buyerUserDetails = {};\n      this.mltLanguages = this.generalService.mltLanguages;\n      this.mltLevels = this.generalService.mltLevels;\n      this.countries = this.generalService.countries;\n      this.mltPricesHourly = [];\n      this.selectedHours = {};\n      this.mltPackages = this.generalService.mltPackages;\n      this.defaultPackages = PackagesModel.getDefaultPackages();\n      this.newPackages = [];\n      this.reqBody = {};\n      this.form = new UntypedFormGroup({});\n      this.classroomFromQueryParams = {};\n      this.showSplitPayment = false;\n      this.splitPaymentPackages = [];\n      this.leadDynoAffiliateId = localStorage.getItem('leadDynoAffiliateId');\n      this.leadDynoPrivateKey = environment.leadDynoPrivateKey;\n      this.leadDynoScriptLoaded = false;\n      this.hasSwitchedPackage = signal(false);\n      this.expiresIn = 1;\n      this.isReceipt = true;\n      this.isInvoice = false;\n      this.isSubmitted = false;\n      this.agreed = false;\n      this.termsError = false;\n    }\n    ngOnInit() {\n      if (!this.inTrial) {\n        const classroomString = this.activatedRoute.snapshot.queryParams['classroom'];\n        if (classroomString) {\n          this.classroomFromQueryParams = JSON.parse(classroomString);\n        }\n      }\n      this.responsiveOptions = [{\n        breakpoint: '1199px',\n        numVisible: 1,\n        numScroll: 1\n      }, {\n        breakpoint: '991px',\n        numVisible: 2,\n        numScroll: 1\n      }, {\n        breakpoint: '767px',\n        numVisible: 1,\n        numScroll: 1\n      }];\n      this.user = this.authService.getLoggedInUser();\n      this.initializeData();\n      this.loadToltScript('https://cdn.tolt.io/tolt.js', '344bed5a-704b-49eb-9ec5-f0215c879772');\n      // TODO: Add LeadDyno script\n      this.loadScript('https://static.leaddyno.com/js').then(() => {\n        // LeadDyno script loaded successfully\n        // Now, you can add your events\n        console.log('LeadDyno script loaded, executing LeadDyno dependent code...');\n        this.leadDynoScriptLoaded = true;\n        this.addLeadDynoEvents();\n        if (this.leadDynoScriptLoaded) {\n          this.packageService.leadDynoGetAffiliateEvent({\n            key: this.leadDynoPrivateKey\n          }).subscribe({\n            next: res => {\n              console.log(res);\n            },\n            error: err => {\n              console.log(err);\n            }\n          });\n          // this.packageService.addPurchaseEvent(purchaseData).subscribe((res: any) => {\n          //   console.log(res);\n          // });\n        }\n      }).catch(error => console.log('Error loading LeadDyno script:', error));\n    }\n    addLeadDynoEvents() {\n      // Assuming LeadDyno is available globally\n      // Add your LeadDyno events here\n      if (typeof LeadDyno !== 'undefined') {\n        LeadDyno.key = \"6f488593e746d71bf3743119f96e36b92acfd049\";\n        LeadDyno.recordVisit();\n        LeadDyno.autoWatch();\n      } else {\n        console.error('LeadDyno is not available');\n      }\n    }\n    loadScript(url) {\n      return new Promise((resolve, reject) => {\n        const script = this.renderer.createElement('script');\n        script.src = url;\n        script.onload = () => {\n          resolve();\n        };\n        script.onerror = error => {\n          reject(error);\n        };\n        this.renderer.appendChild(document.body, script);\n      });\n    }\n    loadJsScript(renderer, src) {\n      const script = renderer.createElement('script');\n      script.type = 'text/javascript';\n      script.src = src;\n      renderer.appendChild(this.document.body, script);\n      return script;\n    }\n    removeLeadDynoScript() {\n      const scriptElement = document.querySelector('script[src=\"https://static.leaddyno.com/js\"]');\n      if (scriptElement) {\n        scriptElement.remove();\n        console.log('LeadDyno script removed');\n      }\n    }\n    loadToltScript(url, dataTolt) {\n      return new Promise((resolve, reject) => {\n        const script = this.renderer.createElement('script');\n        script.src = url;\n        script.setAttribute('id', 'tolt-player');\n        script.setAttribute('data-tolt', dataTolt);\n        script.onload = () => {\n          resolve();\n        };\n        script.onerror = error => {\n          reject(error);\n        };\n        this.renderer.appendChild(document.head, script);\n      });\n    }\n    loadLeadDynoStatic() {\n      console.log('preparing to load LeadDyno...Static');\n      let node = document.createElement('script');\n      node.setAttribute(\"id\", \"leaddyno-static\");\n      node.src = \"https://static.leaddyno.com/js\";\n      node.type = 'text/javascript';\n      node.async = true;\n      node.charset = 'utf-8';\n      document.getElementsByTagName('head')[0].appendChild(node);\n    }\n    ngAfterViewInit() {\n      this.rightSide = document.getElementById('package-overview');\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n      this.removeLeadDynoScript();\n      const scriptElement = document.getElementById('tolt-player');\n      if (scriptElement) {\n        scriptElement.remove();\n      }\n    }\n    get hasSplitPayment() {\n      return this.showSplitPayment;\n    }\n    get errorControl() {\n      return this.form.controls;\n    }\n    trialSection() {\n      this.hideSection = !this.hideSection;\n      setTimeout(() => {\n        [].forEach.call(document.getElementsByClassName('btn'), el => {\n          el.classList.add('hvr-glow');\n        });\n      }, 1000);\n    }\n    initializeForm() {\n      this.selectedResidenceCountry = this.countries.find(obj => obj.name === this.user.residence);\n      this.selectedPhoneCode = this.phoneCodes.filter(el => el.code === this.generalService.extractCountryPrefix(this.user.phone))[0] || this.generalService.getDefaultPhoneCode();\n      console.log(this.selectedResidenceCountry);\n      this.form = new UntypedFormGroup({\n        fname: new UntypedFormControl(this.user.firstName, {\n          validators: [Validators.required]\n        }),\n        lname: new UntypedFormControl(this.user.lastName, {\n          validators: [Validators.required]\n        }),\n        company: new UntypedFormControl(\"\", {}),\n        profession: new UntypedFormControl(\"\", {}),\n        country: new UntypedFormControl(this.selectedResidenceCountry, {\n          validators: [Validators.required]\n        }),\n        street: new UntypedFormControl(\"\", {\n          validators: [Validators.required]\n        }),\n        number: new UntypedFormControl(\"\", {\n          validators: [Validators.required]\n        }),\n        city: new UntypedFormControl(\"\", {\n          validators: [Validators.required]\n        }),\n        postcode: new UntypedFormControl(\"\", {\n          validators: [Validators.required]\n        }),\n        tax: new UntypedFormControl(\"\", {}),\n        email: new UntypedFormControl(this.user.email, {\n          validators: [Validators.required]\n        }),\n        phone: new UntypedFormControl(this.generalService.extractPhoneNumber(this.user.phone), {\n          validators: [Validators.required]\n        }),\n        tin: new UntypedFormControl(\"\", {})\n      });\n    }\n    updateFormValidationRules() {\n      if (this.isInvoice) {\n        this.form.controls['tin'].setValidators([Validators.required]);\n      } else {\n        this.form.controls['tin'].setValidators([]);\n      }\n      this.form.controls['tin'].updateValueAndValidity();\n    }\n    styleAll() {\n      setTimeout(() => {\n        if (!this.isNew) {\n          this.styleClassrooms();\n        } else {\n          this.selectedBtnStyle(`package-new`);\n        }\n        this.styleLanguages();\n        this.styleHours();\n        this.styleNumberOfStudents();\n      }, 1000);\n    }\n    deStyleAll() {\n      this.styleClassrooms();\n      this.styleLanguages();\n      this.styleHours();\n      this.styleNumberOfStudents();\n    }\n    styleClassrooms() {\n      this.selectedBtnStyle(`package-btn-${this.selectedClassroom.id}`, `package-btn-teacher-${this.selectedClassroom?.id}`);\n    }\n    styleLanguages() {\n      this.selectedBtnStyle(`language-btn-${this.selectedLanguage.code}`);\n    }\n    styleNumberOfStudents() {\n      if (this.selectedNumberOfStudents > \"3\") {\n        this.selectedNumberOfStudents = \"3+\";\n      }\n      this.selectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`);\n    }\n    styleHours() {\n      this.selectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-hours-${this.mltPricesHourly[this.selectedHoursIndex].price}`);\n      this.selectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-per-${this.mltPricesHourly[this.selectedHoursIndex].price}`);\n    }\n    selectedBtnStyle(id, extraId) {\n      let color = this.inTrial ? '#4895ef' : this.getGradientColor(this.selectedPackageToBuy.type);\n      let btn = document.getElementById(id);\n      if (btn && this.selectedPackageToBuy) {\n        btn.style.backgroundImage = color;\n      }\n      if (btn && !id.includes('package-new')) {\n        btn.style.color = \"white\";\n        btn.style.opacity = \"1\";\n      }\n      if (extraId) {\n        let extraElement = document.getElementById(extraId);\n        if (extraElement) {\n          extraElement.style.color = \"white\";\n        }\n      }\n    }\n    deSelectedBtnStyle(id, extraId) {\n      let color = this.inTrial ? '#4895ef' : this.getCasualColor();\n      let btn = document.getElementById(id);\n      if (btn) {\n        if (!this.isNew && !id.includes('package-btn') && !id.includes('number-btn') && !id.includes('hour-btn') && !id.includes('package-new') && !id.includes('level')) {\n          btn.style.color = \"#CCD6FF\";\n        } else {\n          btn.style.color = color;\n        }\n        if (!id.includes('package-new')) {\n          btn.style.backgroundColor = \"white\";\n          btn.style.background = \"white\";\n        }\n      }\n      if (extraId) {\n        let extraElement = document.getElementById(extraId);\n        if (extraElement) {\n          extraElement.style.color = \"#2d2a4b\";\n        }\n      }\n    }\n    getBtnStyle(notToBeDisabled) {\n      let color = this.inTrial ? '#4895ef' : this.getCasualColor();\n      if (this.isNew || notToBeDisabled) {\n        return {\n          'border': `1px solid ${color}`,\n          'color': `${color}`\n        };\n      }\n      return {\n        'border': `1px solid ${color}`,\n        'color': `${color}`,\n        'opacity': '0.5',\n        'cursor': 'initial'\n      };\n    }\n    disableLanguage(id) {\n      let btn = document.getElementById(id);\n      btn.style.pointerEvents = 'none';\n      btn.style.opacity = '0.5';\n    }\n    enableLanguage(id) {\n      let btn = document.getElementById(id);\n      btn.style.pointerEvents = 'initial';\n      btn.style.opacity = '1';\n    }\n    getLevelIsGreaterThanSelected(level) {\n      if (this.selectedClassroom.activeLevel == \"TBD\") {\n        return true;\n      }\n      return level >= this.selectedClassroom.activeLevel;\n    }\n    compareLevels(level1, level2) {\n      const levelsOrder = ['TBD', 'A1', 'A2', 'B1', 'B2', 'C1', 'C2', 'BS', 'NT21', 'NT22'];\n      return levelsOrder.indexOf(level1) - levelsOrder.indexOf(level2);\n    }\n    getGradientColor(type) {\n      if (type == PackageType.REGULAR) {\n        return \"linear-gradient(to top, #122171, #353792, #544eb3, #7366d6, #927ffa)\";\n      }\n      if (type == PackageType.FLEX) {\n        return \"linear-gradient(to bottom, #7588f4, #5f6ed1, #4955af, #323d8e, #1b276e)\";\n      }\n      if (type == PackageType.PREMIUM) {\n        return \"linear-gradient(to top, #122171, #004b99, #0074bb, #009ed7, #4dc8f0)\";\n      }\n      return \"\";\n    }\n    getCasualColor() {\n      return \"#657AEF\";\n    }\n    selectClassroom(classroom) {\n      this.isNew = true;\n      [].forEach.call(document.getElementsByClassName('btn'), el => {\n        if (!el.id.includes('package-btn') && !el.id.includes('hour-btn') && !el.id.includes('package-new')) {\n          el.classList.remove('hvr-glow');\n        }\n        setTimeout(() => {\n          // el.classList.add('disabled')\n        }, 300);\n      });\n      this.deSelectedBtnStyle(`package-new`);\n      this.deSelectedBtnStyle(`package-btn-${this.selectedClassroom.id}`, `package-btn-teacher-${this.selectedClassroom?.id}`);\n      // this.deSelectedBtnStyle(`level-btn-${this.selectedLevel}`)\n      this.deSelectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`);\n      this.deSelectedBtnStyle(`language-btn-${this.selectedLanguage.code}`);\n      for (let language of this.mltLanguages) {\n        if (this.selectedClassroom.language == language.name) {\n          this.deSelectedBtnStyle(`language-btn-${language.code}`);\n        }\n      }\n      this.selectedClassroom = classroom;\n      // /Updates the selected classroom's student list by removing any duplicate students.\n      this.selectedClassroom.classroomStudents = this.classroomService.getuniqueClassroomStudents(this.selectedClassroom.classroomStudents);\n      for (let language of this.mltLanguages) {\n        if (this.selectedClassroom.language !== language.name) {\n          this.disableLanguage(`language-btn-${language.code}`);\n        }\n      }\n      this.selectedLevel = this.selectedClassroom.activeLevel;\n      if (this.selectedClassroom.activeLevel == \"TBD\") {\n        this.selectedLevel = \"A1\";\n      }\n      this.selectedDefaultLevel = this.selectedClassroom.activeLevel;\n      this.selectedNumberOfStudents = this.selectedClassroom.classroomStudents.length.toString();\n      this.selectedLanguage = this.mltLanguages.filter(el => el.name.toLowerCase() == this.selectedClassroom.language.toLowerCase())[0];\n      this.reqBody.language = this.selectedLanguage.name;\n      this.reqBody.level = this.selectedLevel;\n      this.toggleSplitPaymentDisplay();\n      console.log(this.showSplitPayment);\n      this.getNewPackagePrices();\n      this.getNewSplitPackagePrices();\n      this.styleAll();\n      this.isNew = false;\n    }\n    selectLanguage(language) {\n      if (this.isNew) {\n        this.deSelectedBtnStyle(`language-btn-${this.selectedLanguage.code}`);\n        this.selectedLanguage = language;\n        this.reqBody.language = this.selectedLanguage.name;\n        this.getNewPackagePrices();\n        for (let language of this.mltLanguages) {\n          this.deSelectedBtnStyle(`language-btn-${language.code}`);\n        }\n        this.styleLanguages();\n      }\n    }\n    selectLevel(level) {\n      if (this.isNew || this.getLevelIsGreaterThanSelected(level)) {\n        this.selectedLevel = level;\n        this.reqBody.level = this.selectedLevel;\n        this.getNewPackagePrices();\n        this.getNewSplitPackagePrices();\n      }\n    }\n    selectNumber(number) {\n      if (this.isNew) {\n        this.deSelectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`);\n        this.selectedNumberOfStudents = number;\n        this.selectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`);\n        this.reqBody.numberOfStudents = this.selectedNumberOfStudents;\n        this.getNewPackagePrices();\n        this.getNewSplitPackagePrices();\n      }\n    }\n    selectHours(hour, i) {\n      this.deSelectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-hours-${this.mltPricesHourly[this.selectedHoursIndex].price}`);\n      this.deSelectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-per-${this.mltPricesHourly[this.selectedHoursIndex].price}`);\n      this.selectedHoursIndex = i;\n      this.selectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-hours-${this.mltPricesHourly[this.selectedHoursIndex].price}`);\n      this.selectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-per-${this.mltPricesHourly[this.selectedHoursIndex].price}`);\n      this.selectedPrice = this.mltPricesHourly[this.selectedHoursIndex].price;\n      this.expiresIn = this.mltPricesHourly[this.selectedHoursIndex].expirationMonths;\n      this.selectedHours = this.mltPricesHourly[this.selectedHoursIndex];\n    }\n    selectPackage(packageType) {\n      this.selectedPackageToBuy = packageType;\n      this.rightSide.style.background = this.getGradientColor(this.selectedPackageToBuy.type);\n      this.styleAll();\n      this.hasSwitchedPackage.set(false);\n    }\n    buyNew() {\n      [].forEach.call(document.getElementsByClassName('btn'), el => {\n        el.classList.add('hvr-glow');\n        el.classList.remove('disabled');\n      });\n      for (let language of this.mltLanguages) {\n        if (this.selectedClassroom.language !== language.name) {\n          this.enableLanguage(`language-btn-${language.code}`);\n        }\n      }\n      this.deSelectedBtnStyle(`package-btn-${this.selectedClassroom.id}`, `package-btn-teacher-${this.selectedClassroom?.id}`);\n      this.selectedBtnStyle(`package-new`);\n      this.isNew = true;\n      this.selectedLanguage = this.mltLanguages[0];\n      this.selectLanguage(this.selectedLanguage);\n      this.deSelectedBtnStyle(`level-btn-${this.selectedLevel}`);\n      this.selectedLevel = \"A1\";\n      this.selectedDefaultLevel = \"A1\";\n      this.selectLevel(this.selectedLevel);\n      this.deSelectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`);\n      this.selectedNumberOfStudents = \"1\";\n      this.styleAll();\n      this.getNewPackagePrices();\n      // this.getNewSplitPackagePrices();\n      this.showSplitPayment = false;\n    }\n    chooseReceipt() {\n      this.isReceipt = true;\n      this.isInvoice = false;\n      this.updateFormValidationRules();\n    }\n    chooseInvoice() {\n      this.isReceipt = false;\n      this.isInvoice = true;\n      this.updateFormValidationRules();\n    }\n    next() {\n      window.scrollTo(0, 0);\n      this.currentStep++;\n      if (this.currentStep == 2) {\n        return;\n      }\n      if (this.currentStep == 3) {\n        this.isSubmitted = true;\n        if (this.form.valid) {\n          let formValue = this.form.value;\n          this.buyerUserDetails.fname = formValue.fname;\n          this.buyerUserDetails.lname = formValue.lname;\n          this.buyerUserDetails.email = formValue.email;\n          this.buyerUserDetails.city = formValue.city;\n          this.buyerUserDetails.company = formValue.company;\n          this.buyerUserDetails.streetName = formValue.street;\n          this.buyerUserDetails.streetNumber = formValue.number;\n          this.buyerUserDetails.tax = formValue.tax;\n          this.buyerUserDetails.profession = formValue.profession;\n          this.buyerUserDetails.postcode = formValue.postcode;\n          this.buyerUserDetails.tin = formValue.tin;\n          this.buyerUserDetails.phone = formValue.phone;\n          this.buyerUserDetails.country = formValue.country.name;\n          return;\n        } else {\n          this.toastService.setShowToastmessage({\n            severity: 'info',\n            summary: '',\n            detail: 'Please fill all the required fields with *'\n          });\n          this.currentStep = 2;\n          return;\n        }\n      }\n      if (this.currentStep == 4) {\n        this.payload.amount = (this.selectedPrice + this.selectedPackageToBuy.costPlus) * 100;\n        return;\n      }\n      if (this.currentStep < 5) {\n        this.currentStep++;\n      }\n      // let classroom = this.isNew ? null : this.selectedClassroom;\n      // this.packageToBuyOrderDeails = {\n      //   isNew: this.isNew,\n      //   selectedClassroom: classroom,\n      //   selectedLevel: this.selectedLevel,\n      //   selectedLanguage: this.selectedLanguage,\n      //   selectedNumberOfStudents: this.selectedNumberOfStudents,\n      //   // selectedPackageToBuy: this.selectedPackageToBuy,\n      //   buyerUserDetails: this.buyerUserDetails\n      // }\n    }\n    previous() {\n      window.scrollTo(0, 0);\n      this.styleAll();\n      if (this.currentStep > 1) {\n        this.currentStep--;\n      } else {\n        this.toastService.setShowToastmessage({\n          severity: 'info',\n          summary: '',\n          detail: 'There is no previous step'\n        });\n      }\n    }\n    goBack() {\n      this.location.back();\n    }\n    toggleSection(sectionElement, sectionArrowImgSrc) {\n      this.generalService.toggleSection(sectionElement, sectionArrowImgSrc, true);\n    }\n    agree() {\n      this.agreed = !this.agreed;\n    }\n    purchase() {\n      if (this.agreed) {\n        let classroom = this.isNew ? 0 : this.selectedClassroom.id;\n        let req = {\n          token: \"\",\n          language: this.selectedLanguage.name,\n          level: this.selectedLevel,\n          studentsNumber: this.selectedNumberOfStudents,\n          hours: this.selectedHours.hours.toString(),\n          packageType: this.selectedPackageToBuy.type,\n          merchant_order_ext_ref: \"\",\n          classroomId: classroom,\n          details: {\n            billingType: this.selectedBillingType,\n            firstname: this.buyerUserDetails.fname,\n            lastname: this.buyerUserDetails.lname,\n            email: this.buyerUserDetails.email,\n            phone: this.selectedPhoneCode.code + ' ' + this.buyerUserDetails.phone,\n            address: this.buyerUserDetails.streetName + \" \" + this.buyerUserDetails.streetNumber,\n            city: this.buyerUserDetails.city,\n            country: this.buyerUserDetails.country,\n            postcode: this.buyerUserDetails.postcode,\n            company: this.buyerUserDetails.company,\n            profession: this.buyerUserDetails.profession,\n            taxOffice: this.buyerUserDetails.tax,\n            vat: this.buyerUserDetails.tin\n          }\n        };\n        this.initializePaymentForm(req);\n      } else {\n        this.termsError = true;\n        this.toastService.setShowToastmessage({\n          severity: 'info',\n          summary: '',\n          detail: 'Please agree to the terms and conditions to make the order'\n        });\n      }\n      //   // this.currentStep = 4;\n      //   this.packageService.requestbuyPackage(req).subscribe((res: any) => {\n      //     this.everypayLink = this.transform(res.result);\n      //     this.generalService.slideNativeElements(true, this.everypay.nativeElement)\n      //   })\n      // } else {\n      //   this.termsError = true;\n      // }\n    }\n    sendRequestTrial() {\n      if (this.selectedNumberOfStudents == \"3+\") {\n        this.selectedNumberOfStudents = \"4\";\n      }\n      let req = {\n        trialLanguage: this.selectedLanguage.name,\n        trialLevel: this.selectedLevel,\n        trialStudentNumber: this.selectedNumberOfStudents\n      };\n      this.packageService.requestTrial(req).pipe(take(1)).subscribe(res => {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Your trial has been requested.'\n        });\n      });\n    }\n    closePayment() {\n      this.router.navigate(['dashboard']);\n    }\n    initializeData() {\n      var _this = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const res = yield _this.classroomService.getLMSUserClassrooms(_this.user.id).pipe(take(1)).toPromise(); // Convert to Promise and await its resolution\n          // Proceed after receiving the response\n          _this.userClassrooms = res.filter(item => item && item.language && item.language.trim() !== '');\n          _this.userClassrooms = _this.filterOutTrialClassroomsWithSameTeacher(_this.userClassrooms);\n          // Create an object to track classrooms by teacher ID\n          const classroomsByTeacher = {};\n          // Update this.userClassrooms and classroomFromQueryParams\n          _this.userClassrooms = _this.userClassrooms.filter(response => {\n            const teacherId = response.teacher?.id ?? null;\n            if (teacherId !== null) {\n              // Check if the response is of type \"Paid\" or if there is no existing response for this teacher ID\n              if (response.type === \"Paid\" || !(teacherId in classroomsByTeacher)) {\n                classroomsByTeacher[teacherId] = response; // Store the response\n              }\n              if (_this.classroomFromQueryParams && _this.classroomFromQueryParams.type === \"Trial\" && _this.classroomFromQueryParams.teacher && _this.classroomFromQueryParams.teacher.id === teacherId) {\n                // Replace classroomFromQueryParams with the paid response\n                _this.classroomFromQueryParams = response;\n              }\n              return response; // else Include all classrooms\n            }\n            return true; // Include classrooms with no teacher ID\n          });\n          if (_this.userClassrooms.length == 0) {\n            _this.isNew = true;\n            _this.selectedLanguage = _this.mltLanguages[0];\n            _this.selectedLevel = \"A1\";\n            _this.selectedNumberOfStudents = \"1\";\n            _this.selectedDefaultLevel = \"A1\";\n          } else {\n            if (_this.classroomFromQueryParams.id) {\n              _this.selectedClassroom = _this.classroomFromQueryParams;\n            } else {\n              _this.selectedClassroom = _this.userClassrooms[0];\n            }\n            for (let language of _this.mltLanguages) {\n              if (_this.selectedClassroom.language !== language.name) {\n                _this.disableLanguage(`language-btn-${language.code}`);\n              }\n            }\n            _this.selectedClassroom.classroomStudents = _this.classroomService.getuniqueClassroomStudents(_this.selectedClassroom.classroomStudents);\n            _this.selectedNumberOfStudents = _this.selectedClassroom.classroomStudents.length.toString();\n            _this.selectedLevel = _this.selectedClassroom.activeLevel;\n            if (_this.selectedLevel == \"TBD\") {\n              _this.selectedLevel = \"A1\";\n            }\n            _this.selectedDefaultLevel = _this.selectedClassroom.activeLevel;\n            _this.selectedLanguage = _this.mltLanguages.filter(el => el.name == _this.selectedClassroom.language)[0];\n            _this.toggleSplitPaymentDisplay();\n          }\n          _this.initializeForm();\n          _this.updateFormValidationRules();\n          if (_this.selectedNumberOfStudents <= \"3\") {\n            _this.reqBody = {\n              \"language\": _this.selectedLanguage.name,\n              \"level\": _this.selectedLevel,\n              \"numberOfStudents\": _this.selectedNumberOfStudents\n            };\n          } else {\n            _this.reqBody = {\n              \"language\": _this.selectedLanguage.name,\n              \"level\": _this.selectedLevel,\n              \"numberOfStudents\": \"2\"\n            };\n          }\n          _this.selectedPackageToBuy = _this.defaultPackages[1];\n          _this.mltPricesHourly = _this.defaultPackages[0].expiresIn;\n          _this.getNewPackagePrices();\n          _this.getNewSplitPackagePrices();\n          _this.selectedHours = _this.mltPricesHourly[2];\n          _this.expiresIn = _this.mltPricesHourly[2].expirationMonths;\n          _this.styleAll();\n        } catch (error) {\n          console.error('An error occurred:', error);\n        }\n      })();\n    }\n    updatePrices() {\n      var _this2 = this;\n      return _asyncToGenerator(function* () {\n        return new Promise((resolve, reject) => {\n          try {\n            _this2.mltPricesHourly.forEach((item, index) => {\n              item.price = _this2.newPackages[index].price;\n              item.perHour = _this2.newPackages[index].perHour;\n            });\n            resolve();\n          } catch (error) {\n            reject(error);\n          }\n        });\n      })();\n    }\n    getNewPackagePrices() {\n      var _this3 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const res = yield _this3.packageService.getUpdatedPackagesToBuy(_this3.reqBody).pipe(take(1)).toPromise();\n          _this3.newPackages = res;\n          yield _this3.updatePrices();\n          _this3.selectedPrice = _this3.selectedHours.price;\n        } catch (error) {\n          console.log('An error occurred:', error);\n        }\n      })();\n    }\n    getNewSplitPackagePrices() {\n      var _this4 = this;\n      return _asyncToGenerator(function* () {\n        try {\n          const res = yield _this4.packageService.getSplitPackagesToBuy(_this4.reqBody).pipe(take(1)).toPromise();\n          _this4.splitPaymentPackages = res;\n        } catch (error) {\n          console.log('An error occurred:', error);\n        }\n      })();\n    }\n    onPhoneCodeChange(event) {\n      setTimeout(() => {\n        this.selectedPhoneCode = event.value;\n      }, 100);\n    }\n    initializePaymentForm(data) {\n      console.log(data);\n      this.subs.add((this.hasSplitPayment ? this.packageService.createSplitRevolutOrder(data) : this.packageService.createRevolutOrder(data)).pipe(switchMap(order => {\n        console.log('order', order);\n        var that = this;\n        RevolutCheckout(order.public_id, isProductionMode ? 'prod' : 'sandbox').then(function (instance) {\n          instance.payWithPopup({\n            onSuccess() {\n              data.token = order.public_id;\n              data.merchant_order_ext_ref = order.merchant_order_ext_ref;\n              // let event = new CustomEvent('dosomething', {detail: 'hello'});\n              that.completeOrder(data, order);\n            },\n            onError(message) {\n              console.log(message);\n            }\n          });\n        });\n        return of(data);\n      }), take(1)).subscribe(res => {}));\n    }\n    completeOrder(paymentResponse, order) {\n      this.subs.add((this.hasSplitPayment ? this.packageService.completeSplitOrder(paymentResponse) : this.packageService.completeOrder(paymentResponse)).subscribe(res => {\n        this.currentStep = 4;\n        if (this.leadDynoScriptLoaded) {\n          const purchaseData = {\n            key: this.leadDynoPrivateKey,\n            // Replace with your Leaddyno API key\n            email: order.email,\n            purchase_amount: order.order_amount.value / 100,\n            // Convert to the correct currency\n            purchase_code: order.merchant_order_ext_ref,\n            affiliate: localStorage.getItem('leadDynoAffiliateId') || '',\n            line_items: [{\n              sku: order.id,\n              quantity: 1,\n              description: paymentResponse.language + ' - ' + paymentResponse.level + ' level - ' + paymentResponse.hours + ' hours',\n              amount: order.order_amount.value / 100\n            }]\n          };\n          // affiliate exists\n          if (this.leadDynoAffiliateId) {\n            this.packageService.getLeadDynoAffiliateByAffiliateCode(this.leadDynoAffiliateId, this.leadDynoPrivateKey).subscribe({\n              next: res => {\n                console.log(res);\n                const randId = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\n                const purchaseData = {\n                  key: this.leadDynoPrivateKey,\n                  email: this.user.email,\n                  purchase_amount: order.order_amount.value / 100,\n                  // Convert to the correct currency\n                  purchase_code: order.merchant_order_ext_ref,\n                  code: res.affiliate_code || this.leadDynoAffiliateId,\n                  affiliate: res.email,\n                  line_items: [{\n                    sku: order.merchant_order_ext_ref,\n                    quantity: 1,\n                    description: order.merchant_order_ext_ref,\n                    amount: order.order_amount.value / 100\n                  }]\n                };\n                this.packageService.addPurchaseEvent(purchaseData).subscribe(res => {\n                  console.log(res);\n                });\n              },\n              error: err => {\n                console.log(err);\n              }\n            });\n          } else {\n            // without affiliate\n            this.packageService.addPurchaseEvent(purchaseData).subscribe(res => {\n              console.log(res);\n            });\n          }\n        }\n      }));\n    }\n    getTotalSplitPaymentAmount() {\n      if (this.splitPaymentPackages.length === 0) {\n        return 0;\n      }\n      const selectedPackage = this.splitPaymentPackages[this.selectedHoursIndex];\n      const additionalCost = this.selectedPackageToBuy.costPlus * 0.5;\n      return (selectedPackage?.companyFee || 0) + additionalCost;\n    }\n    getRemainingAmount() {\n      const totalPrice = this.selectedPrice + this.selectedPackageToBuy.costPlus;\n      return totalPrice - this.getTotalSplitPaymentAmount();\n    }\n    switchBackToPackageType(event) {\n      console.log(event);\n      this.selectedPackageToBuy = event;\n      this.hasSwitchedPackage.set(true);\n    }\n    onSplitPaymentPayNowTooltipSelected() {\n      this.confirmDialogService.setProperties({\n        confirmMessage: `The booking fee is an upfront payment, ensuring the immediate availability of your preferred teacher.`,\n        acceptBtnLabel: 'OK',\n        action: 'cancel-lesson',\n        confirmIcon: '',\n        acceptBtnImage: '',\n        acceptBtnIcon: 'pi pi-check',\n        showHeader: true,\n        dialogType: 'custom',\n        showConfirmIcon: false,\n        headerClass: 'my-custom-header-class',\n        headerText: 'Booking Fee'\n      });\n      this.confirmDialogService.show();\n    }\n    onSplitPaymentPayLaterTooltipSelected() {\n      this.confirmDialogService.setProperties({\n        confirmMessage: `\n      Once your booking fee payment is completed, a customer service representative will contact you shortly to provide further assistance. \n      They will guide you on how to complete the payment process.`,\n        acceptBtnLabel: 'OK',\n        action: 'cancel-lesson',\n        confirmIcon: '',\n        acceptBtnImage: '',\n        acceptBtnIcon: 'pi pi-check',\n        showHeader: true,\n        dialogType: 'custom',\n        showConfirmIcon: false,\n        headerClass: 'my-custom-header-class',\n        headerText: 'What is a Remaining Balance?'\n      });\n      this.confirmDialogService.show();\n    }\n    toggleSplitPaymentDisplay() {\n      this.showSplitPayment = this.selectedClassroom.teacher.accSlitPayments;\n      console.log(this.selectedPackageToBuy);\n    }\n    /**\n    Filters out trial classrooms with the same teacher.\n    @param {Classroom[]} userClassrooms - An array of classrooms for the user.\n    @returns {Classroom[]} - An array of classrooms with the same teacher as the user.\n    */\n    filterOutTrialClassroomsWithSameTeacher(userClassrooms) {\n      return userClassrooms.filter(classroom => {\n        // Check if classroom is a trial\n        if (classroom.type === 'Trial') {\n          // Find classrooms with the same teacher asp user id\n          const sameTeacherClassrooms = userClassrooms.filter(c => c.teacher.aspUserId === classroom.teacher.aspUserId);\n          // Filter out the trial classroom if there are other classrooms with the same teacher asp user id\n          if (sameTeacherClassrooms.length > 1) {\n            return false; // Exclude the trial classroom\n          }\n        }\n        return true; // Include other classrooms\n      });\n    }\n    static #_ = this.ɵfac = function BuyPackageComponent_Factory(t) {\n      return new (t || BuyPackageComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.Location), i0.ɵɵdirectiveInject(i4.ClassroomService), i0.ɵɵdirectiveInject(i5.GeneralService), i0.ɵɵdirectiveInject(i6.PackageService), i0.ɵɵdirectiveInject(i7.ToastService), i0.ɵɵdirectiveInject(i8.ConfirmDialogService), i0.ɵɵdirectiveInject(i9.DomSanitizer), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(DOCUMENT));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: BuyPackageComponent,\n      selectors: [[\"app-buy-package\"]],\n      inputs: {\n        inTrial: \"inTrial\",\n        hideSection: \"hideSection\"\n      },\n      decls: 5,\n      vars: 3,\n      consts: [[\"elseAiChatIconBlock\", \"\"], [\"receiptDetailsArrowSrc\", \"\"], [\"receiptDetailsSection\", \"\"], [\"termsArrowSrc\", \"\"], [\"termsSection\", \"\"], [\"noSplitPayment\", \"\"], [1, \"buy-package\"], [\"id\", \"class\", 2, \"background-image\", \"linear-gradient(to left, #7588f4, #a0a4f7, #c3c1fa, #e3dffc, #ffffff)\", \"border-radius\", \"14px\"], [\"class\", \"buy-package-header\", 4, \"ngIf\"], [4, \"ngIf\"], [\"class\", \"section\", 4, \"ngIf\"], [1, \"buy-package-header\"], [\"type\", \"button\", 1, \"p-element\", \"py-1\", \"p-button-outlined\", \"p-button-rounded\", \"button-outlined\", \"p-button-sm\", \"p-button\", \"p-component\", \"ng-star-inserted\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"gap-1\"], [1, \"p-datepicker-prev-icon\", \"pi\", \"pi-chevron-left\"], [1, \"text-base\", \"s12-16\"], [1, \"buy-package-title\", \"font-lg\", \"font-semibold\"], [\"class\", \"steps\", 4, \"ngIf\"], [1, \"steps\"], [1, \"package-step\"], [1, \"white-button\", 3, \"click\"], [1, \"section\"], [\"class\", \"left pt-0\", 3, \"width\", 4, \"ngIf\"], [\"class\", \"left\", 4, \"ngIf\"], [\"class\", \"left w-full\", 4, \"ngIf\"], [\"class\", \"right notranslate mb-6\", \"id\", \"package-overview\", 4, \"ngIf\"], [1, \"left\", \"pt-0\"], [3, \"marginTop\", 4, \"ngIf\"], [\"class\", \"btns\", 4, \"ngIf\"], [1, \"title\", \"my-2\", \"font-sm\"], [1, \"top-btns\", \"my-0\"], [\"id\", \"package-new\", 1, \"btn\", \"btn-new\", \"btn-classroom\", \"new\", \"mb-2\", \"mt-0\", \"hvr-glow\", \"col-6\", \"lg:col-3\", \"hvr-glow\", 3, \"click\"], [1, \"flex-column\"], [1, \"btn-teacher-name\", \"font-2xs\", \"m-0\", 2, \"min-height\", \"auto\", \"color\", \"#3345a7\"], [\"class\", \"btn btn-classroom hvr-glow col-6 lg:col-3 mb-2 mt-0 hvr-glow\", 3, \"ngStyle\", \"id\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"btn\", \"btn-classroom\", \"hvr-glow\", \"col-6\", \"lg:col-3\", \"mb-2\", \"mt-0\", \"hvr-glow\", 3, \"click\", \"ngStyle\", \"id\"], [1, \"btn-language\", \"font-sm\", \"lang\", \"capitalize\"], [1, \"btn-teacher-name\", \"font-2xs\", 3, \"id\"], [1, \"title\", \"font-sm\", \"mt-1\"], [\"class\", \"btn btn-new btn-language p-2 w-4rem\", 3, \"ngStyle\", \"id\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"btn\", \"btn-new\", \"btn-language\", \"p-2\", \"w-4rem\", 3, \"click\", \"ngStyle\", \"id\"], [1, \"title\", \"font-sm\"], [\"class\", \"btn btn-new btn-level level\", 3, \"id\", \"ngClass\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"btn\", \"btn-new\", \"btn-level\", \"level\", 3, \"click\", \"id\", \"ngClass\"], [3, \"matTooltip\"], [3, \"click\"], [1, \"top-btns\", \"my-0\", \"justify-content-center\", \"gap-2\"], [\"class\", \"btn py-2 col-2 btn-new btn-language\", 3, \"ngStyle\", \"id\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"btn\", \"py-2\", \"col-2\", \"btn-new\", \"btn-language\", 3, \"click\", \"ngStyle\", \"id\"], [1, \"top-btns\", \"top-btns-hours\", \"m-0\", \"notranslate\"], [\"class\", \"btn btn-hours hvr-glow\", 3, \"ngStyle\", \"id\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"btn\", \"btn-hours\", \"hvr-glow\", 3, \"click\", \"ngStyle\", \"id\"], [1, \"hours-info\", \"font-xs\", 3, \"id\"], [1, \"price\", \"font-semibold\"], [1, \"btns\"], [1, \"main-color-button\", \"hvr-glow\", 2, \"background\", \"linear-gradient(#a4a2e6 0%, #4895ef 100%)\", 3, \"click\"], [1, \"grid\", \"justify-content-around\", \"packages\"], [\"class\", \"package-type lg:w-11rem p-0\", 3, \"borderColor\", 4, \"ngFor\", \"ngForOf\"], [1, \"package-type\", \"lg:w-11rem\", \"p-0\"], [1, \"package-type-section\"], [1, \"type-name\", \"relative\"], [1, \"my-radio\", \"absolute\", 3, \"click\", \"ngClass\"], [1, \"package-type-section\", \"py-2\", \"border-noround\"], [1, \"package-info\", \"p-3\", \"font-sm\"], [1, \"flex\", \"align-items-start\"], [\"src\", \"/assets/images/dashboard/check.svg\", \"alt\", \"\", 1, \"w-1rem\", \"mr-1\"], [1, \"flex\", \"align-items-start\", \"my-1\"], [4, \"ngIf\", \"ngIfElse\"], [\"class\", \"flex align-items-start\", 4, \"ngIf\"], [\"class\", \"package-type-section py-1 flex flex-row justify-content-center custom-border\", 4, \"ngIf\"], [\"src\", \"/assets/images/dashboard/package-tick-error.png\", \"alt\", \"\", 1, \"w-1rem\", \"mr-1\"], [1, \"package-type-section\", \"py-1\", \"flex\", \"flex-row\", \"justify-content-center\", \"custom-border\"], [\"src\", \"/assets/icons/check-blue.svg\", \"alt\", \"\", 1, \"w-1rem\", \"mr-1\"], [1, \"font-sm\", \"font-semibold\"], [1, \"left\"], [1, \"border-1\", \"bg-blue-50\", \"border-blue-500\", \"border-round\", \"p-1\", \"sm:p-3\", \"mb-2\", \"flex\", \"flex-row\", \"align-items-center\", \"z-1\"], [3, \"formGroup\"], [1, \"input-fields\"], [1, \"input-field\", \"font-semibold\", \"mb-3\"], [1, \"input-field\"], [1, \"flex\", \"flex-wrap\", \"gap-3\"], [1, \"flex\", \"align-items-center\"], [\"name\", \"billing\", \"value\", \"receipt\", \"inputId\", \"receipt\", 3, \"ngModelChange\", \"click\", \"ngModel\", \"ngModelOptions\"], [\"for\", \"receipt\", 1, \"ml-2\", \"font-sm\"], [\"name\", \"billing\", \"value\", \"invoice\", \"inputId\", \"invoice\", 3, \"ngModelChange\", \"click\", \"ngModel\", \"ngModelOptions\"], [\"for\", \"invoice\", 1, \"ml-2\", \"font-sm\"], [1, \"input-field\", \"font-sm\"], [1, \"input-element-title\"], [\"formControlName\", \"fname\", \"type\", \"text\", 1, \"input-element\"], [\"class\", \"input-error\", 4, \"ngIf\"], [\"formControlName\", \"lname\", \"type\", \"text\", 1, \"input-element\"], [\"formControlName\", \"company\", \"type\", \"text\", 1, \"input-element\"], [\"formControlName\", \"profession\", \"type\", \"text\", 1, \"input-element\"], [\"class\", \"input-field font-sm\", 4, \"ngIf\"], [1, \"input-field\", \"font-sm\", \"fwidth\"], [\"optionLabel\", \"name\", \"filterBy\", \"name\", \"flagFilter\", \"name\", \"templateValue\", \"name\", 3, \"parentForm\", \"countries\", \"inputName\", \"withFlags\", \"placeholder\", \"selectedItemValue\"], [\"style\", \"width:100%\", 4, \"ngIf\"], [\"formControlName\", \"email\", \"type\", \"text\", 1, \"input-element\"], [1, \"flex\", \"sm:flex\"], [\"optionLabel\", \"code\", \"filterBy\", \"code\", \"flagFilter\", \"code\", \"templateValue\", \"code\", \"styleClass\", \"dropdown-blue white-bg phone-code-input tiny rounded-less small-dropdown-items\", 3, \"valueSelected\", \"parentForm\", \"countries\", \"filter\", \"inputName\", \"placeholder\", \"withFlags\", \"selectedItemValue\"], [1, \"p-float-label\", \"w-full\"], [\"type\", \"text\", \"formControlName\", \"phone\", 1, \"input-blue\", \"input-element\", \"rounded-less\", \"white-bg\", \"no-radius-left\", \"w-full\"], [\"formControlName\", \"street\", \"type\", \"text\", 1, \"input-element\"], [\"formControlName\", \"number\", \"type\", \"text\", 1, \"input-element\"], [\"formControlName\", \"city\", \"type\", \"text\", 1, \"input-element\"], [\"formControlName\", \"postcode\", \"type\", \"text\", 1, \"input-element\"], [1, \"pi\", \"pi-check-circle\", \"price-blue\", \"text-2xl\", \"md:text-4xl\", \"mb-2\", \"md:mb-0\", \"mr-2\", \"md:mr-3\"], [1, \"text-900\", \"font-medium\", \"mb-1\"], [1, \"text-600\", \"text-sm\"], [3, \"switchBackToPackageTypeSelected\", \"preselectedPackage\"], [1, \"input-error\"], [\"formControlName\", \"tin\", \"type\", \"text\", 1, \"input-element\"], [\"formControlName\", \"tax\", \"type\", \"text\", 1, \"input-element\"], [2, \"width\", \"100%\"], [1, \"left\", \"w-full\"], [1, \"section-step-3\"], [1, \"section-step-3-title\"], [1, \"toggle-section\", 3, \"click\"], [\"src\", \"/assets/icons/toogle-section.svg\", 1, \"section-arrow\"], [1, \"section-content\", 2, \"overflow\", \"inherit\"], [1, \"review\"], [1, \"review-section\"], [1, \"review-section-title\", \"font-sm\", \"font-semibold\"], [1, \"review-section-content\", \"mt-1\", \"font-sm\"], [1, \"col-50\"], [1, \"info\"], [1, \"col-25\"], [\"class\", \"col-25\", 4, \"ngIf\"], [\"class\", \"col-50 sm:mt-1\", 4, \"ngIf\"], [1, \"font-base\", \"py-3\"], [1, \"font-sm\"], [1, \"section-content\", \"fixed-height\", 2, \"overflow-y\", \"scroll\", \"height\", \"200px!important\"], [1, \"review-section-content\"], [1, \"col-100\"], [1, \"info\", \"font-xs\"], [1, \"font-semibold\"], [1, \"review-section\", 2, \"font-size\", \"15px\", \"margin\", \"20px 0\", \"font-weight\", \"bold\"], [\"label\", \"I have read and agree to the website terms and conditions *\", \"name\", \"groupname\", \"value\", \"val1\", 1, \"terms-checkbox\", 3, \"ngModelChange\", \"onChange\", \"ngModel\"], [1, \"col-50\", \"sm:mt-1\"], [1, \"end\"], [\"src\", \"/assets/icons/package-check.svg\"], [\"id\", \"pay-form\"], [1, \"end-msg\", \"text-xl\", \"text-primary\"], [1, \"text-center\", \"text-primary\", \"text-lg\"], [1, \"text-center\"], [1, \"text-primary\", \"lg:max-w-11rem\"], [\"pButton\", \"\", \"pRipple\", \"\", \"label\", \"Back to dashboard\", 1, \"white-space-nowrap\", \"p-button\", \"p-button-rounded\", \"p-button-outlined\", \"text-primary\", 3, \"routerLink\"], [\"id\", \"package-overview\", 1, \"right\", \"notranslate\", \"mb-6\"], [1, \"title\"], [1, \"row\", \"row-first\"], [1, \"row-right\"], [1, \"row\"], [\"class\", \"row-right\", 4, \"ngIf\"], [1, \"seperator\"], [1, \"small-title\"], [\"class\", \"row\", 4, \"ngIf\"], [1, \"row\", \"mt-5\", \"bottom-buttons\", 3, \"ngClass\"], [\"type\", \"button\", 1, \"align-self-center\", \"prev-button-outlined\", \"p-element\", \"py-1\", \"p-button-outlined\", \"p-button-rounded\", \"button-outlined\", \"p-button-sm\", \"p-button\", \"p-component\", \"py-2\", \"px-3\", 3, \"click\"], [1, \"p-ink\"], [\"class\", \"\", 4, \"ngIf\"], [1, \"row\", \"align-items-center\"], [1, \"flex\", \"flex-column\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"split-text\"], [\"src\", \"/assets/icons/tooltip-blue.svg\", \"alt\", \"tooltip\", \"title\", \"learn more\", 1, \"pointer\", 3, \"click\"], [\"type\", \"button\", 1, \"next-button-outlined\", \"p-element\", \"py-1\", \"p-button-outlined\", \"p-button-rounded\", \"button-outlined\", \"p-button-sm\", \"p-button\", \"p-component\", \"py-2\", \"px-4\", \"font-base\", 3, \"click\"], [1, \"p-datepicker-next-icon\", \"pi\", \"pi-chevron-right\"], [1, \"\"], [\"type\", \"button\", 1, \"split-pay-button-outlined\", \"p-element\", \"py-1\", \"p-button-outlined\", \"p-button-rounded\", \"button-outlined\", \"p-button-sm\", \"p-button\", \"p-component\", \"py-2\", \"px-3\", \"font-base\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-1\"], [1, \"flex\", \"justify-content-start\", \"align-items-start\", \"flex-column\"], [1, \"font-bold\"], [\"type\", \"button\", 1, \"buy-button\", \"p-element\", \"py-1\", \"p-button-outlined\", \"p-button-rounded\", \"button-outlined\", \"p-button-sm\", \"p-button\", \"p-component\", \"py-2\", \"px-4\", \"font-base\", 3, \"click\"], [\"_ngcontent-rrb-c97\", \"\", \"width\", \"16\", \"src\", \"assets/icons/lessons/arranged-white.svg\", 1, \"pi\", \"text-white\", \"font-sm\", \"ml-2\"]],\n      template: function BuyPackageComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7);\n          i0.ɵɵtemplate(2, BuyPackageComponent_div_2_Template, 9, 1, \"div\", 8)(3, BuyPackageComponent_div_3_Template, 3, 0, \"div\", 9)(4, BuyPackageComponent_div_4_Template, 6, 6, \"div\", 10);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.inTrial);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.inTrial);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.hideSection);\n        }\n      },\n      dependencies: [i3.NgClass, i3.NgForOf, i3.NgIf, i3.NgStyle, i2.RouterLink, i10.PrimeInputDropdownComponent, i11.ButtonDirective, i12.Checkbox, i13.RadioButton, i14.BuyPackageSuggestionBoxComponent, i15.ɵNgNoValidate, i15.DefaultValueAccessor, i15.NgControlStatus, i15.NgControlStatusGroup, i15.NgModel, i15.FormGroupDirective, i15.FormControlName, i16.MatTooltip, i3.SlicePipe],\n      styles: [\"[_nghost-%COMP%]     .e-frame.e-check{background-color:var(--main-color)!important;width:20px!important;height:20px!important}[_nghost-%COMP%]     .e-checkbox-wrapper .e-frame{width:20px!important;height:20px!important;line-height:16px}[_nghost-%COMP%]     .e-checkbox-wrapper .e-checkbox:focus+.e-frame{width:20px!important;height:20px!important}[_nghost-%COMP%]     .e-icons.e-check:before{font-size:13px!important}[_nghost-%COMP%]     .e-radio:focus+label:before{border-color:var(--main-color)!important;box-shadow:none!important}[_nghost-%COMP%]     .e-radio:focus+label:before{border-color:#757575!important;box-shadow:none!important}[_nghost-%COMP%]     .e-radio:hover+label:before{border-color:#757575!important}[_nghost-%COMP%]     .e-radio:checked+label:before{background-color:#fff!important;border-color:var(--main-color)!important}[_nghost-%COMP%]     .e-radio:checked+label:after{background-color:var(--main-color)!important;color:var(--main-color)!important}[_nghost-%COMP%]     .e-radio:checked+label:active .e-ripple-element{background-color:#e3165b42!important}[_nghost-%COMP%]     .e-radio:checked+.e-focus .e-ripple-container{background-color:#e3165b42!important}[_nghost-%COMP%]     .e-radio:checked+.e-focus:before{outline:#fff 0 solid!important;outline-offset:0!important}[_nghost-%COMP%]     .e-radio:checked:focus+label:before{border-color:var(--main-color)!important}[_nghost-%COMP%]     .e-radio:checked:focus+label:after{background-color:var(--main-color)!important}[_nghost-%COMP%]     .e-radio:checked+label:hover:before{border-color:var(--main-color)!important}[_nghost-%COMP%]     .e-radio:checked+label:hover:after{background-color:var(--main-color)!important}[_nghost-%COMP%]     .e-radio+label:after{border:2px solid!important;height:12px!important;left:3px!important;top:3px!important;width:12px!important}.responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}.buy-package[_ngcontent-%COMP%]{width:100%;font-size:17px;background:#fff;border-radius:14px}.section[_ngcontent-%COMP%]{display:flex;flex-direction:column;background-color:var(--white);border-radius:14px;margin-top:0;margin-bottom:30px}@media only screen and (min-width: 576px){.section[_ngcontent-%COMP%]{flex-direction:row}}.section[_ngcontent-%COMP%]   .left[_ngcontent-%COMP%]{width:100%!important;padding:20px}@media only screen and (min-width: 576px){.section[_ngcontent-%COMP%]   .left[_ngcontent-%COMP%]{width:70%!important}}.section[_ngcontent-%COMP%]   .left[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{width:100%;text-align:center;font-family:Proxima Nova Bold}.section[_ngcontent-%COMP%]   .right[_ngcontent-%COMP%]{width:100%;margin-top:-10px;padding:20px;border-radius:0 0 14px;color:#fff;transition:all .2s ease-in;position:sticky;background-image:linear-gradient(to bottom,#7588f4,#5f6ed1,#4955af,#323d8e,#1b276e)}@media only screen and (min-width: 576px){.section[_ngcontent-%COMP%]   .right[_ngcontent-%COMP%]{width:30%}}.section[_ngcontent-%COMP%]   .right[_ngcontent-%COMP%]   .seperator[_ngcontent-%COMP%]{border-top:1px solid white;width:100%;margin:15px 0}.section[_ngcontent-%COMP%]   .right[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:18px;font-family:Proxima Nova Bold}.section[_ngcontent-%COMP%]   .right[_ngcontent-%COMP%]   .small-title[_ngcontent-%COMP%]{font-size:15px;font-family:Proxima Nova Bold}.section[_ngcontent-%COMP%]   .right[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]{display:flex;justify-content:space-between;margin-top:10px;font-size:15px;text-align:start}.section[_ngcontent-%COMP%]   .right[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .row-right[_ngcontent-%COMP%]{font-family:Proxima Nova Bold}.section[_ngcontent-%COMP%]   .right[_ngcontent-%COMP%]   .row-first[_ngcontent-%COMP%]{margin-top:20px}.package-type[_ngcontent-%COMP%]{border-radius:5px;margin-top:20px;padding:10px;display:flex;flex-direction:column;justify-content:space-between;align-items:center;border-color:transparent!important}.package-type[_ngcontent-%COMP%]   .package-type-section[_ngcontent-%COMP%]{flex-direction:column;align-items:center;display:flex;background-color:#ccd6ff8a;width:100%;color:#3345a7;border-radius:10px 10px 5px 5px}.package-type[_ngcontent-%COMP%]   .package-type-section[_ngcontent-%COMP%]   .package-info[_ngcontent-%COMP%]{color:#000;font-size:13px}.package-type[_ngcontent-%COMP%]   .package-type-section[_ngcontent-%COMP%]   .my-radio[_ngcontent-%COMP%]{border:2px solid #2f338d;border-radius:50%;width:36px;height:36px;background-color:#fff;margin-left:10px;background-clip:content-box!important;cursor:pointer;top:-3px;left:-15px;position:relative}.package-type[_ngcontent-%COMP%]   .package-type-section[_ngcontent-%COMP%]   .type-name[_ngcontent-%COMP%]{color:#fff;padding:5px 15px;font-size:.95rem;text-align:center;font-family:Proxima Nova Bold;border-radius:10px;width:100%}.top-btns[_ngcontent-%COMP%]{display:flex;width:100%;justify-content:center;margin:20px 0;flex-wrap:wrap;box-sizing:border-box}.top-btns[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]{transition:all .2s ease-in;padding:2px;border-radius:12px;margin:10px;text-align:center;cursor:pointer}.top-btns[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   .btn-language[_ngcontent-%COMP%]{font-family:Proxima Nova Bold}.top-btns[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   .btn-teacher-name[_ngcontent-%COMP%]{font-size:12px;color:#657aef;transition:all .2s ease-in}.top-btns[_ngcontent-%COMP%]   .btn-classroom[_ngcontent-%COMP%]{height:3rem;color:#3345a7}.top-btns[_ngcontent-%COMP%]   .btn-classroom.new[_ngcontent-%COMP%]{background-image:-webkit-gradient(linear,left bottom,left top,color-stop(.33,#002ccf),color-stop(.67,#a796ff))}.top-btns[_ngcontent-%COMP%]   .btn-classroom.new[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{background-color:var(--white);margin:10px;width:100%;min-height:100%;border-radius:inherit;display:flex;justify-content:center;align-items:center}@media only screen and (min-width: 992px) and (min-width: 576px){.top-btns[_ngcontent-%COMP%]   .btn-classroom.lg\\\\:col-3[_ngcontent-%COMP%]{width:20%!important}}.top-btns[_ngcontent-%COMP%]   .btn-hours[_ngcontent-%COMP%]{padding:10px;height:auto;width:33%;transition:all .2s ease-in}@media only screen and (min-width: 576px){.top-btns[_ngcontent-%COMP%]   .btn-hours[_ngcontent-%COMP%]{width:18%}}.top-btns[_ngcontent-%COMP%]   .btn-hours[_ngcontent-%COMP%]   .price[_ngcontent-%COMP%]{margin:5px 0}.top-btns[_ngcontent-%COMP%]   .btn-hours[_ngcontent-%COMP%]   .hours-info[_ngcontent-%COMP%]{transition:all .2s ease-in;color:#000}.top-btns[_ngcontent-%COMP%]   .btn-new[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;justify-content:center;font-family:Proxima Nova Bold}.top-btns[_ngcontent-%COMP%]   .btn-level[_ngcontent-%COMP%]{border-radius:50%;width:40px;height:40px}.top-btns-hours[_ngcontent-%COMP%]{flex-wrap:wrap}@media only screen and (min-width: 576px){.top-btns-hours[_ngcontent-%COMP%]{flex-wrap:nowrap}}.buy-package-header[_ngcontent-%COMP%]{background-image:url(/assets/images/payment.png);width:100%;border:0!important;padding:10px;box-sizing:border-box;position:sticky;padding:.5rem 1rem;display:flex;align-items:center;justify-content:space-between;background-size:cover;background-repeat:no-repeat;background-position:center;border-radius:12px;min-height:37px;background-size:inherit;color:var(--white);font-family:Proxima Nova Regular,sans-serif;letter-spacing:.05rem;z-index:2}.buy-package-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]{display:flex;align-items:center;cursor:pointer}.buy-package-header[_ngcontent-%COMP%]   .buy-package-title[_ngcontent-%COMP%]{text-align:center}.buy-package-header[_ngcontent-%COMP%]   .steps[_ngcontent-%COMP%]{display:flex}.buy-package-header[_ngcontent-%COMP%]   .steps[_ngcontent-%COMP%]   .package-step[_ngcontent-%COMP%]{color:var(--white);text-align:center;transition:all .3s linear;width:33px;height:33px;line-height:33px;border-radius:50%;margin:0 10px}[_nghost-%COMP%]     .e-label{font-size:15px!important}[_nghost-%COMP%]     .p-dropdown{width:100%;padding:4.5px;border-radius:10px;margin-top:10px}[_nghost-%COMP%]     .dropdown-blue{width:75px!important}[_nghost-%COMP%]     .dropdown-blue .with-icon{padding-left:0!important}[_nghost-%COMP%]     .fwidth .dropdown-blue{width:100%!important}.country-item[_ngcontent-%COMP%]{display:flex;align-items:center}.country-item[_ngcontent-%COMP%]   .country-name[_ngcontent-%COMP%]{margin-left:10px}.country-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:40px}.disabled[_ngcontent-%COMP%]{background:#fff;color:#d3d3d3;border:1px solid lightgray}  .mat-tooltip{color:#ff0;font-size:15px;background-color:#707070}.review[_ngcontent-%COMP%]{padding:30px;box-sizing:border-box}@media only screen and (max-width: 768px){.review[_ngcontent-%COMP%]{padding:1rem}}.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]{margin-top:30px}.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-title[_ngcontent-%COMP%]{color:var(--main-color);font-size:20px;font-family:Proxima Nova Bold}.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]{color:var(--main-color);font-size:15px;display:flex;flex-wrap:wrap}.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-25[_ngcontent-%COMP%], .review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-50[_ngcontent-%COMP%]{display:flex;flex-direction:column}.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-25[_ngcontent-%COMP%]   .info[_ngcontent-%COMP%], .review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-50[_ngcontent-%COMP%]   .info[_ngcontent-%COMP%]{color:#3345a7;font-family:Proxima Nova Bold}.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-25[_ngcontent-%COMP%]   .availability[_ngcontent-%COMP%], .review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-50[_ngcontent-%COMP%]   .availability[_ngcontent-%COMP%]{display:flex}.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-25[_ngcontent-%COMP%]   .availability[_ngcontent-%COMP%]   .col-50[_ngcontent-%COMP%], .review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-50[_ngcontent-%COMP%]   .availability[_ngcontent-%COMP%]   .col-50[_ngcontent-%COMP%]{width:50%}.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-25[_ngcontent-%COMP%]{width:25%;flex-basis:25%}@media only screen and (max-width: 768px){.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-25[_ngcontent-%COMP%]{width:100%;flex-basis:100%}}.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-50[_ngcontent-%COMP%]{width:50%;flex-basis:50%}@media only screen and (max-width: 768px){.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-50[_ngcontent-%COMP%]{width:100%;flex-basis:100%}}.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]   .review-section-content[_ngcontent-%COMP%]   .col-100[_ngcontent-%COMP%]{width:100%;flex-basis:100%}.review[_ngcontent-%COMP%]   .review-section[_ngcontent-%COMP%]:first-child{margin-top:0}.btns[_ngcontent-%COMP%]{position:sticky;top:0;background-color:#fff;border-bottom:1px solid lightgray}.section-step-3[_ngcontent-%COMP%]{background:linear-gradient(#fff,#3873f433);border-radius:28px 28px 8px 8px}.section-content[_ngcontent-%COMP%]{overflow:hidden;transition:height .3s ease-out}.fixed-height[_ngcontent-%COMP%]{height:200px!important;overflow-y:scroll!important}.section-step-3-title[_ngcontent-%COMP%]{width:100%;border:1px solid #2E3D90;font-size:15px;text-align:center;padding:.3rem 0;box-sizing:border-box;border-radius:28px;position:relative;color:#2e3d90}.section-step-3-title[_ngcontent-%COMP%]   .toggle-section[_ngcontent-%COMP%]   .section-arrow[_ngcontent-%COMP%]{background-image:linear-gradient(to top,#7588f4,#5f6ed1,#4955af,#323d8e,#1b276e);transform:rotate(180deg);position:absolute;top:0;right:0;overflow:hidden;transition-duration:.2s;transition-property:transform;cursor:pointer;border-radius:50%;width:1.8rem}.end[_ngcontent-%COMP%]{display:flex;justify-content:center;flex-direction:column;align-items:center;height:470px}.end[_ngcontent-%COMP%]   .end-msg[_ngcontent-%COMP%]{margin-top:30px;font-family:Proxima Nova Bold}.white-button[_ngcontent-%COMP%]{width:200px;margin-top:15px}button.p-element[_ngcontent-%COMP%]{color:var(--white)}.cursor-none[_ngcontent-%COMP%]{cursor:default!important}.p-custom[_ngcontent-%COMP%]{padding:.3rem!important}.hvr-glow[_ngcontent-%COMP%]:hover, .hvr-glow[_ngcontent-%COMP%]:focus, .hvr-glow[_ngcontent-%COMP%]:active{box-shadow:0 0 4px #00000075}.level[_ngcontent-%COMP%]{position:relative;color:#fff!important;font-weight:400!important}.level[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]{font-family:Proxima Nova Regular;z-index:1}.level[_ngcontent-%COMP%]:after{content:\\\" \\\";display:block;position:absolute;left:0;top:0;width:100%;height:100%;background-image:url(/assets/images/dashboard/goals/level-bubble.png)!important;background-repeat:no-repeat;background-position:50% 0;background-size:40px;z-index:0;opacity:.8}.level-disabled[_ngcontent-%COMP%]{border:1px solid #CCD6FF;background-image:none}.level-disabled[_ngcontent-%COMP%]:after{opacity:.2}.level-default[_ngcontent-%COMP%]{border:1px solid rgb(101,122,239);color:#657aef;background-image:none}.level-selected[_ngcontent-%COMP%]{box-shadow:0 0 4px #00000075;margin:4px;background-color:transparent;color:#fff;border:none;overflow:hidden;float:left;border-radius:50%}.level-selected[_ngcontent-%COMP%]:after{opacity:1}.custom-border[_ngcontent-%COMP%]{border-radius:0 0 10px 10px!important}.selected[_ngcontent-%COMP%]:before{content:url(/assets/icons/check-blue.svg);position:absolute;left:50%;transform:translate(-50%,-50%);top:55%;z-index:2}.input-fields[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]{margin-top:0!important}.payment-step[_ngcontent-%COMP%]{display:flex;justify-content:center}.prev-button-outlined[_ngcontent-%COMP%], .next-button-outlined[_ngcontent-%COMP%]{border-radius:50px;background-color:#7082e6!important}.prev-button-outlined[_ngcontent-%COMP%]:hover, .next-button-outlined[_ngcontent-%COMP%]:hover{background-color:#7082e6!important;box-shadow:none!important;color:#fff!important}.prev-button-outlined.inactive[_ngcontent-%COMP%], .next-button-outlined.inactive[_ngcontent-%COMP%]{opacity:.3;cursor:not-allowed}.prev-button-outlined.inactive[_ngcontent-%COMP%]:hover, .next-button-outlined.inactive[_ngcontent-%COMP%]:hover{color:#fff;border-color:#fff}.split-pay-button-outlined[_ngcontent-%COMP%]{border-radius:50px;box-shadow:none;background-color:#2fb9d3!important}.split-pay-button-outlined[_ngcontent-%COMP%]:hover{background-color:#7082e6!important;box-shadow:none!important;color:#fff!important}.split-pay-button-outlined.inactive[_ngcontent-%COMP%]{opacity:.3;cursor:not-allowed}.split-pay-button-outlined.inactive[_ngcontent-%COMP%]:hover{color:#fff;border-color:#fff}.buy-button[_ngcontent-%COMP%]{box-shadow:none!important;background:linear-gradient(#9baaff,#152caf);border-radius:50px}.buy-button[_ngcontent-%COMP%]:hover{background:linear-gradient(#152caf,#9baaff)!important;box-shadow:none!important;color:#fff!important}.input-element[_ngcontent-%COMP%]{font-size:.85rem!important;font-family:inherit;outline:none!important}.split-text[_ngcontent-%COMP%]{color:#ccd6ff}[_nghost-%COMP%]  .terms-checkbox .p-checkbox{width:18px;height:18px}[_nghost-%COMP%]  .terms-checkbox .p-checkbox-label{font-size:16px;cursor:pointer}@media screen and (max-width: 768px){[_nghost-%COMP%]  .terms-checkbox .p-checkbox-label{font-size:14px}}[_nghost-%COMP%]  .terms-checkbox .p-checkbox .p-checkbox-box.p-highlight .p-checkbox-icon.pi-check:before{top:1px;left:-5px}[_nghost-%COMP%]  .terms-checkbox .p-checkbox .p-checkbox-box{border-radius:50px}[_nghost-%COMP%]  .terms-checkbox .p-checkbox .p-checkbox-box.p-highlight{border-radius:50px}[_nghost-%COMP%]  .terms-checkbox .p-checkbox .p-checkbox-box .p-checkbox-icon{transform:scale(1.3)}[_nghost-%COMP%]  .terms-checkbox .p-component .p-checkbox-box{width:18px!important;height:18px!important}@media only screen and (max-width: 768px){.bottom-buttons[_ngcontent-%COMP%]{z-index:55555;bottom:0;left:0;width:100%;position:fixed;padding:1rem;background-color:#2e3b8f}}\"],\n      data: {\n        animation: [slideInOut2, slideInOut]\n      }\n    });\n  }\n  return BuyPackageComponent;\n})();", "map": {"version": 3, "names": ["DOCUMENT", "signal", "UntypedFormControl", "UntypedFormGroup", "Validators", "switchMap", "take", "PackageType", "PackagesModel", "slideInOut2", "slideInOut", "SubSink", "RevolutCheckout", "of", "environment", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "currentStep", "ɵɵlistener", "BuyPackageComponent_div_2_Template_button_click_1_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "goBack", "ɵɵelement", "ɵɵtemplate", "BuyPackageComponent_div_2_div_8_Template", "ɵɵproperty", "BuyPackageComponent_div_3_Template_div_click_1_listener", "_r3", "trialSection", "BuyPackageComponent_div_4_div_1_div_1_div_9_Template_div_click_0_listener", "classroom_r6", "_r5", "$implicit", "selectClassroom", "ɵɵpropertyInterpolate1", "id", "getBtnStyle", "language", "ɵɵtextInterpolate2", "teacher", "firstName", "ɵɵpipeBind3", "lastName", "BuyPackageComponent_div_4_div_1_div_1_Template_div_click_4_listener", "_r4", "buyNew", "BuyPackageComponent_div_4_div_1_div_1_div_9_Template", "userClassrooms", "BuyPackageComponent_div_4_div_1_div_2_div_4_Template_div_click_0_listener", "language_r8", "_r7", "selectLanguage", "code", "BuyPackageComponent_div_4_div_1_div_2_div_4_Template", "ɵɵstyleProp", "inTrial", "undefined", "mltLanguages", "BuyPackageComponent_div_4_div_1_div_3_div_4_Template_div_click_0_listener", "level_r10", "_r9", "selectLevel", "BuyPackageComponent_div_4_div_1_div_3_div_4_Template_div_click_2_listener", "ɵɵpureFunction4", "_c0", "compareLevels", "selectedLevel", "selectedDefaultLevel", "isNew", "ɵɵtextInterpolate", "BuyPackageComponent_div_4_div_1_div_3_div_4_Template", "mltLevels", "BuyPackageComponent_div_4_div_1_div_4_div_2_Template_div_click_0_listener", "level_r12", "_r11", "BuyPackageComponent_div_4_div_1_div_4_div_2_Template", "BuyPackageComponent_div_4_div_1_div_5_div_4_Template_div_click_0_listener", "number_r14", "_r13", "selectNumber", "BuyPackageComponent_div_4_div_1_div_5_div_4_Template", "numberOfStudents", "BuyPackageComponent_div_4_div_1_div_6_div_4_Template_div_click_0_listener", "ctx_r15", "_r15", "hour_r17", "i_r18", "index", "selectHours", "price", "hours", "perHour", "BuyPackageComponent_div_4_div_1_div_6_div_4_Template", "mltPricesHourly", "BuyPackageComponent_div_4_div_1_div_7_Template_div_click_1_listener", "_r19", "sendRequestTrial", "expiresIn", "package_r21", "expiresPlus", "ɵɵelementContainerStart", "pause", "BuyPackageComponent_div_4_div_1_div_8_div_4_Template_div_click_4_listener", "_r20", "selectPackage", "BuyPackageComponent_div_4_div_1_div_8_div_4_span_12_Template", "BuyPackageComponent_div_4_div_1_div_8_div_4_span_13_Template", "BuyPackageComponent_div_4_div_1_div_8_div_4_ng_container_19_Template", "BuyPackageComponent_div_4_div_1_div_8_div_4_ng_template_20_Template", "ɵɵtemplateRefExtractor", "BuyPackageComponent_div_4_div_1_div_8_div_4_div_24_Template", "BuyPackageComponent_div_4_div_1_div_8_div_4_div_25_Template", "getCasualColor", "type", "i_r22", "defaultPackages", "length", "ɵɵpureFunction1", "_c1", "selectedP<PERSON>ageToBuy", "costPlus", "cancelation", "elseAiChatIconBlock_r23", "BuyPackageComponent_div_4_div_1_div_8_div_4_Template", "BuyPackageComponent_div_4_div_1_div_1_Template", "BuyPackageComponent_div_4_div_1_div_2_Template", "BuyPackageComponent_div_4_div_1_div_3_Template", "BuyPackageComponent_div_4_div_1_div_4_Template", "BuyPackageComponent_div_4_div_1_div_5_Template", "BuyPackageComponent_div_4_div_1_div_6_Template", "BuyPackageComponent_div_4_div_1_div_7_Template", "BuyPackageComponent_div_4_div_1_div_8_Template", "hideSection", "selectedLanguage", "BuyPackageComponent_div_4_div_2_Conditional_1_Conditional_6_Template", "BuyPackageComponent_div_4_div_2_Conditional_1_Conditional_7_Template", "ɵɵconditional", "BuyPackageComponent_div_4_div_2_Conditional_2_Template_app_buy_package_suggestion_box_switchBackToPackageTypeSelected_1_listener", "$event", "_r25", "switchBackToPackageType", "BuyPackageComponent_div_4_div_2_div_35_div_4_Template", "isSubmitted", "errorControl", "tin", "touched", "errors", "required", "BuyPackageComponent_div_4_div_2_div_36_div_4_Template", "tax", "BuyPackageComponent_div_4_div_2_Conditional_1_Template", "BuyPackageComponent_div_4_div_2_Conditional_2_Template", "ɵɵtwoWayListener", "BuyPackageComponent_div_4_div_2_Template_p_radioButton_ngModelChange_10_listener", "_r24", "ɵɵtwoWayBindingSet", "selectedBillingType", "BuyPackageComponent_div_4_div_2_Template_p_radioButton_click_10_listener", "chooseReceipt", "BuyPackageComponent_div_4_div_2_Template_p_radioButton_ngModelChange_14_listener", "BuyPackageComponent_div_4_div_2_Template_p_radioButton_click_14_listener", "chooseInvoice", "BuyPackageComponent_div_4_div_2_div_21_Template", "BuyPackageComponent_div_4_div_2_div_26_Template", "BuyPackageComponent_div_4_div_2_div_35_Template", "BuyPackageComponent_div_4_div_2_div_36_Template", "BuyPackageComponent_div_4_div_2_div_41_Template", "BuyPackageComponent_div_4_div_2_div_42_Template", "BuyPackageComponent_div_4_div_2_div_47_Template", "BuyPackageComponent_div_4_div_2_Template_app_prime_input_dropdown_valueSelected_52_listener", "onPhoneCodeChange", "BuyPackageComponent_div_4_div_2_div_55_Template", "BuyPackageComponent_div_4_div_2_div_60_Template", "BuyPackageComponent_div_4_div_2_div_65_Template", "BuyPackageComponent_div_4_div_2_div_70_Template", "BuyPackageComponent_div_4_div_2_div_75_Template", "hasSwitchedPackage", "form", "ɵɵtwoWayProperty", "ɵɵpureFunction0", "_c2", "fname", "lname", "isInvoice", "countries", "selectedResidenceCountry", "country", "isReceipt", "email", "phoneCodes", "selectedPhoneCode", "phone", "street", "number", "city", "postcode", "buyerUserDetails", "BuyPackageComponent_div_4_div_3_span_3_Template", "BuyPackageComponent_div_4_div_3_span_4_Template", "BuyPackageComponent_div_4_div_3_Template_div_click_6_listener", "_r26", "receiptDetailsArrowSrc_r27", "ɵɵreference", "receiptDetailsSection_r28", "toggleSection", "BuyPackageComponent_div_4_div_3_div_65_Template", "BuyPackageComponent_div_4_div_3_div_80_Template", "BuyPackageComponent_div_4_div_3_Template_div_click_88_listener", "termsArrowSrc_r29", "termsSection_r30", "BuyPackageComponent_div_4_div_3_Template_p_checkbox_ngModelChange_207_listener", "<PERSON><PERSON><PERSON><PERSON>", "BuyPackageComponent_div_4_div_3_Template_p_checkbox_onChange_207_listener", "agree", "company", "profession", "streetName", "streetNumber", "_c3", "selectedHoursIndex", "selectedPrice", "BuyPackageComponent_div_4_div_5_div_48_div_3_Template", "BuyPackageComponent_div_4_div_5_ng_container_55_Template_img_click_7_listener", "_r32", "onSplitPaymentPayNowTooltipSelected", "BuyPackageComponent_div_4_div_5_ng_container_55_Template_img_click_16_listener", "onSplitPaymentPayLaterTooltipSelected", "getTotalSplitPaymentAmount", "getRemainingAmount", "BuyPackageComponent_div_4_div_5_ng_container_61_Template_button_click_1_listener", "_r33", "next", "BuyPackageComponent_div_4_div_5_div_62_ng_container_1_Template_button_click_1_listener", "_r34", "purchase", "BuyPackageComponent_div_4_div_5_div_62_ng_template_2_div_0_Template_button_click_1_listener", "_r35", "BuyPackageComponent_div_4_div_5_div_62_ng_template_2_div_0_Template", "BuyPackageComponent_div_4_div_5_div_62_ng_container_1_Template", "BuyPackageComponent_div_4_div_5_div_62_ng_template_2_Template", "showSplitPayment", "noSplitPayment_r36", "BuyPackageComponent_div_4_div_5_div_21_Template", "BuyPackageComponent_div_4_div_5_span_34_Template", "BuyPackageComponent_div_4_div_5_span_35_Template", "BuyPackageComponent_div_4_div_5_div_41_Template", "BuyPackageComponent_div_4_div_5_div_45_Template", "BuyPackageComponent_div_4_div_5_div_46_Template", "BuyPackageComponent_div_4_div_5_div_48_Template", "BuyPackageComponent_div_4_div_5_div_49_Template", "BuyPackageComponent_div_4_div_5_ng_container_55_Template", "BuyPackageComponent_div_4_div_5_Template_button_click_57_listener", "_r31", "previous", "BuyPackageComponent_div_4_div_5_ng_container_61_Template", "BuyPackageComponent_div_4_div_5_div_62_Template", "name", "selectedNumberOfStudents", "_c4", "ɵɵclassProp", "BuyPackageComponent_div_4_div_1_Template", "BuyPackageComponent_div_4_div_2_Template", "BuyPackageComponent_div_4_div_3_Template", "BuyPackageComponent_div_4_div_4_Template", "BuyPackageComponent_div_4_div_5_Template", "isProductionMode", "production", "BuyPackageComponent", "constructor", "authService", "activatedRoute", "location", "classroomService", "generalService", "packageService", "toastService", "confirmDialogService", "sanitizer", "router", "renderer", "document", "user", "subs", "guid", "packageToBuyOrderDeails", "getPhoneCodes", "classroomId", "payload", "pk", "amount", "locale", "txnType", "selectedClassroom", "selectedHours", "mltPackages", "getDefaultPackages", "newPackages", "reqBody", "classroomFromQueryParams", "splitPaymentPackages", "leadDynoAffiliateId", "localStorage", "getItem", "leadDynoPrivateKey", "leadDynoScriptLoaded", "agreed", "termsError", "ngOnInit", "classroomString", "snapshot", "queryParams", "JSON", "parse", "responsiveOptions", "breakpoint", "numVisible", "numScroll", "getLoggedInUser", "initializeData", "loadToltScript", "loadScript", "then", "console", "log", "addLeadDynoEvents", "leadDynoGetAffiliateEvent", "key", "subscribe", "res", "error", "err", "catch", "LeadDyno", "recordVisit", "autoWatch", "url", "Promise", "resolve", "reject", "script", "createElement", "src", "onload", "onerror", "append<PERSON><PERSON><PERSON>", "body", "loadJsScript", "removeLeadDynoScript", "scriptElement", "querySelector", "remove", "dataTolt", "setAttribute", "head", "loadLeadDynoStatic", "node", "async", "charset", "getElementsByTagName", "ngAfterViewInit", "rightSide", "getElementById", "ngOnDestroy", "unsubscribe", "hasSplitPayment", "controls", "setTimeout", "for<PERSON>ach", "call", "getElementsByClassName", "el", "classList", "add", "initializeForm", "find", "obj", "residence", "filter", "extractCountryPrefix", "getDefaultPhoneCode", "validators", "extractPhoneNumber", "updateFormValidationRules", "setValidators", "updateValueAndValidity", "styleAll", "styleClassrooms", "selectedBtnStyle", "styleLanguages", "styleHours", "styleNumberOfStudents", "deStyleAll", "extraId", "color", "getGradientColor", "btn", "style", "backgroundImage", "includes", "opacity", "extraElement", "deSelectedBtnStyle", "backgroundColor", "background", "notToBeDisabled", "disableLanguage", "pointerEvents", "enableLanguage", "getLevelIsGreaterThanSelected", "level", "activeLevel", "level1", "level2", "levelsOrder", "indexOf", "REGULAR", "FLEX", "PREMIUM", "classroom", "classroomStudents", "getuniqueClassroomStudents", "toString", "toLowerCase", "toggleSplitPaymentDisplay", "getNewPackagePrices", "getNewSplitPackagePrices", "hour", "i", "expirationMonths", "packageType", "set", "window", "scrollTo", "valid", "formValue", "value", "setShowToastmessage", "severity", "summary", "detail", "back", "sectionElement", "sectionArrowImgSrc", "req", "token", "studentsNumber", "merchant_order_ext_ref", "details", "billingType", "firstname", "lastname", "address", "taxOffice", "vat", "initializePaymentForm", "trialLanguage", "trialLevel", "trialStudentNumber", "requestTrial", "pipe", "closePayment", "navigate", "_this", "_asyncToGenerator", "getLMSUserClassrooms", "to<PERSON>romise", "item", "trim", "filterOutTrialClassroomsWithSameTeacher", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "response", "teacherId", "updatePrices", "_this2", "_this3", "getUpdatedPackagesToBuy", "_this4", "getSplitPackagesToBuy", "event", "data", "createSplitRevolutOrder", "createRevolutOrder", "order", "that", "public_id", "instance", "payWithPopup", "onSuccess", "completeOrder", "onError", "message", "paymentResponse", "completeSplitOrder", "purchaseData", "purchase_amount", "order_amount", "purchase_code", "affiliate", "line_items", "sku", "quantity", "description", "getLeadDynoAffiliateByAffiliateCode", "randId", "Math", "random", "substring", "affiliate_code", "addPurchaseEvent", "selected<PERSON><PERSON><PERSON>", "additionalCost", "companyFee", "totalPrice", "setProperties", "confirmMessage", "acceptBtnLabel", "action", "confirmIcon", "acceptBtnImage", "acceptBtnIcon", "showHeader", "dialogType", "showConfirmIcon", "headerClass", "headerText", "show", "accSlitPayments", "sameTeacherClassrooms", "c", "aspUserId", "_", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "ActivatedRoute", "i3", "Location", "i4", "ClassroomService", "i5", "GeneralService", "i6", "PackageService", "i7", "ToastService", "i8", "ConfirmDialogService", "i9", "Dom<PERSON><PERSON><PERSON>zer", "Router", "Renderer2", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "BuyPackageComponent_Template", "rf", "ctx", "BuyPackageComponent_div_2_Template", "BuyPackageComponent_div_3_Template", "BuyPackageComponent_div_4_Template"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\buy-package\\buy-package.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\buy-package\\buy-package.component.html"], "sourcesContent": ["import { DOCUMENT, Location } from '@angular/common';\r\nimport { Component, HostListener, Inject, Input, OnInit, Renderer2, signal, ViewChild } from '@angular/core';\r\nimport { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\r\nimport { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { switchMap, take } from 'rxjs/operators';\r\nimport { Classroom } from 'src/app/core/models/classroom.model';\r\nimport { Country, Language, LeadDynoAffiliate } from 'src/app/core/models/general.model';\r\nimport { BuyerUserDetails, PackagePriceHourly, PackageToBuy, PackageToBuyNew, PackageToBuyOrderDeails, PackageType, PackagesModel, SplitPackagesToBuyResponse, mltPackages } from 'src/app/core/models/package.model';\r\nimport { User } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { PackageService } from 'src/app/core/services/package.service';\r\nimport { slideInOut2, slideInOut } from 'src/app/helpers/my-animations';\r\nimport { SubSink } from 'subsink';\r\nimport RevolutCheckout from \"@revolut/checkout\"\r\nimport { MODE } from '@revolut/checkout/types/constants';\r\nimport { of } from 'rxjs';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { ConfirmDialogService } from 'src/app/core/services/confirm-dialog.service';\r\nimport { environment } from 'src/environments/environment';\r\nimport { HttpHeaders } from '@angular/common/http';\r\ninterface PaidResponses {\r\n  [key: string]: any;\r\n}\r\nconst isProductionMode = environment.production;\r\ndeclare var LeadDyno: any;\r\n\r\n@Component({\r\n  selector: 'app-buy-package',\r\n  templateUrl: './buy-package.component.html',\r\n  styleUrls: ['./buy-package.component.scss'],\r\n  animations: [slideInOut2, slideInOut],\r\n})\r\nexport class BuyPackageComponent implements OnInit {\r\n  @Input() inTrial: boolean = false;\r\n  @Input() hideSection: boolean = false;\r\n  user = {} as User;\r\n  private subs = new SubSink();\r\n  guid: string = \"guid-guid\"\r\n  public packageToBuyOrderDeails: PackageToBuyOrderDeails = {} as PackageToBuyOrderDeails\r\n  public phoneCodes: any[] = this.generalService.getPhoneCodes();\r\n  public currentStep: number = 1;\r\n\r\n  public userClassrooms: Classroom[] = []\r\n  public classroomId: string = \"\"\r\n\r\n  public isNew: boolean = false;\r\n\r\n  public numberOfStudents = [\"1\", \"2\", \"3\"]\r\n\r\n  payload = {\r\n    pk: 'your-public-key',\r\n    amount: 1000,\r\n    locale: 'en',\r\n    txnType: 'tds',\r\n  };\r\n\r\n  public selectedClassroom: Classroom = {} as Classroom;\r\n  public selectedPackageToBuy: mltPackages = {} as mltPackages;\r\n  public selectedPrice: any;\r\n  public selectedLevel: string = \"A1\";\r\n  public selectedDefaultLevel: string = \"A1\";\r\n  public selectedNumberOfStudents: string = \"1\"\r\n  public selectedLanguage: Language = {} as Language;\r\n  public selectedHoursIndex: number = 2;\r\n  public selectedBillingType: string = \"receipt\";\r\n  public selectedPhoneCode: any | undefined = {} as any | undefined;\r\n  public selectedResidenceCountry: Country | undefined = {} as Country | undefined;\r\n  public buyerUserDetails: BuyerUserDetails = {} as BuyerUserDetails\r\n  public mltLanguages = this.generalService.mltLanguages\r\n  public mltLevels = this.generalService.mltLevels\r\n  public countries = this.generalService.countries\r\n  public mltPricesHourly: PackagePriceHourly[] = []\r\n  public selectedHours: PackagePriceHourly = {} as PackagePriceHourly;\r\n  public mltPackages = this.generalService.mltPackages\r\n  public defaultPackages = PackagesModel.getDefaultPackages();\r\n  public newPackages: PackageToBuyNew[] = [];\r\n  public reqBody: any = {}\r\n  public form: UntypedFormGroup = new UntypedFormGroup({})\r\n  public classroomFromQueryParams: Classroom = {} as Classroom;\r\n  public showSplitPayment = false;\r\n  public splitPaymentPackages: SplitPackagesToBuyResponse[] = [];\r\n  public leadDynoAffiliateId = localStorage.getItem('leadDynoAffiliateId');\r\n  rightSide: any;\r\n  e?: SafeResourceUrl\r\n  responsiveOptions!: any[];\r\n  leadDynoPrivateKey = environment.leadDynoPrivateKey;\r\n  leadDynoScriptLoaded = false;\r\n\r\n  hasSwitchedPackage = signal(false);\r\n  constructor(\r\n    private authService: AuthService,\r\n    private activatedRoute: ActivatedRoute,\r\n    private location: Location,\r\n    private classroomService: ClassroomService,\r\n    private generalService: GeneralService,\r\n    private packageService: PackageService,\r\n    private toastService: ToastService,\r\n    public confirmDialogService: ConfirmDialogService,\r\n    private sanitizer: DomSanitizer,\r\n    private router: Router,\r\n    private renderer: Renderer2,\r\n    @Inject(DOCUMENT) private document: Document,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    if (!this.inTrial) {\r\n      const classroomString = this.activatedRoute.snapshot.queryParams['classroom'];\r\n      if (classroomString) {\r\n        this.classroomFromQueryParams = JSON.parse(classroomString);\r\n      }\r\n    }\r\n\r\n    this.responsiveOptions = [\r\n      {\r\n        breakpoint: '1199px',\r\n        numVisible: 1,\r\n        numScroll: 1\r\n      },\r\n      {\r\n        breakpoint: '991px',\r\n        numVisible: 2,\r\n        numScroll: 1\r\n      },\r\n      {\r\n        breakpoint: '767px',\r\n        numVisible: 1,\r\n        numScroll: 1\r\n      }\r\n    ];\r\n\r\n    this.user = this.authService.getLoggedInUser();\r\n    this.initializeData();\r\n    this.loadToltScript('https://cdn.tolt.io/tolt.js', '344bed5a-704b-49eb-9ec5-f0215c879772');\r\n\r\n    // TODO: Add LeadDyno script\r\n    this.loadScript('https://static.leaddyno.com/js').then(() => {\r\n      // LeadDyno script loaded successfully\r\n      // Now, you can add your events\r\n      console.log('LeadDyno script loaded, executing LeadDyno dependent code...');\r\n\r\n      this.leadDynoScriptLoaded = true;\r\n      this.addLeadDynoEvents();\r\n\r\n\r\n      if (this.leadDynoScriptLoaded) {\r\n\r\n\r\n        this.packageService.leadDynoGetAffiliateEvent({\r\n          key: this.leadDynoPrivateKey,\r\n        }).subscribe({\r\n          next: (res: any) => {\r\n            console.log(res);\r\n          },\r\n          error: (err: any) => {\r\n            console.log(err);\r\n          }\r\n        });\r\n\r\n\r\n        // this.packageService.addPurchaseEvent(purchaseData).subscribe((res: any) => {\r\n        //   console.log(res);\r\n        // });\r\n      }\r\n\r\n    }).catch(error => console.log('Error loading LeadDyno script:', error));\r\n\r\n  }\r\n\r\n  addLeadDynoEvents(): void {\r\n    // Assuming LeadDyno is available globally\r\n    // Add your LeadDyno events here\r\n    if (typeof LeadDyno !== 'undefined') {\r\n      LeadDyno.key = \"6f488593e746d71bf3743119f96e36b92acfd049\";\r\n      LeadDyno.recordVisit();\r\n      LeadDyno.autoWatch();\r\n    } else {\r\n      console.error('LeadDyno is not available');\r\n    }\r\n\r\n  }\r\n\r\n  loadScript(url: string): Promise<void> {\r\n    return new Promise<void>((resolve, reject) => {\r\n      const script = this.renderer.createElement('script');\r\n      script.src = url;\r\n      script.onload = () => {\r\n        resolve();\r\n      };\r\n      script.onerror = (error: any) => {\r\n        reject(error);\r\n      };\r\n      this.renderer.appendChild(document.body, script);\r\n    });\r\n  }\r\n\r\n  loadJsScript(renderer: Renderer2, src: string): HTMLScriptElement {\r\n    const script = renderer.createElement('script');\r\n    script.type = 'text/javascript';\r\n    script.src = src;\r\n    renderer.appendChild(this.document.body, script);\r\n    return script;\r\n  }\r\n\r\n  removeLeadDynoScript(): void {\r\n    const scriptElement = document.querySelector('script[src=\"https://static.leaddyno.com/js\"]');\r\n    if (scriptElement) {\r\n      scriptElement.remove();\r\n      console.log('LeadDyno script removed');\r\n    }\r\n  }\r\n\r\n  loadToltScript(url: string, dataTolt: string): Promise<void> {\r\n    return new Promise<void>((resolve, reject) => {\r\n      const script = this.renderer.createElement('script');\r\n      script.src = url;\r\n      script.setAttribute('id', 'tolt-player');\r\n      script.setAttribute('data-tolt', dataTolt);\r\n      script.onload = () => {\r\n        resolve();\r\n      };\r\n      script.onerror = (error: any) => {\r\n        reject(error);\r\n      };\r\n      this.renderer.appendChild(document.head, script);\r\n    });\r\n  }\r\n\r\n  public loadLeadDynoStatic() {\r\n    console.log('preparing to load LeadDyno...Static');\r\n    let node = document.createElement('script');\r\n    node.setAttribute(\"id\", \"leaddyno-static\");\r\n    node.src = \"https://static.leaddyno.com/js\";\r\n    node.type = 'text/javascript';\r\n    node.async = true;\r\n    node.charset = 'utf-8';\r\n    document.getElementsByTagName('head')[0].appendChild(node);\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n    this.rightSide = document.getElementById('package-overview');\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n    this.removeLeadDynoScript();\r\n    const scriptElement = document.getElementById('tolt-player');\r\n    if (scriptElement) {\r\n      scriptElement.remove();\r\n    }\r\n  }\r\n\r\n  get hasSplitPayment(): boolean {\r\n    return this.showSplitPayment;\r\n  }\r\n\r\n  get errorControl() {\r\n    return this.form.controls\r\n  }\r\n\r\n  trialSection() {\r\n    this.hideSection = !this.hideSection;\r\n    setTimeout(() => {\r\n      [].forEach.call(document.getElementsByClassName('btn'), (el: any) => {\r\n        el.classList.add('hvr-glow')\r\n      });\r\n    }, 1000);\r\n  }\r\n\r\n  initializeForm() {\r\n    this.selectedResidenceCountry = this.countries.find(obj => obj.name === this.user.residence);\r\n    this.selectedPhoneCode = this.phoneCodes.filter(el => el.code === this.generalService.extractCountryPrefix(this.user.phone))[0] || this.generalService.getDefaultPhoneCode();\r\n    console.log(this.selectedResidenceCountry);\r\n    this.form = new UntypedFormGroup({\r\n      fname: new UntypedFormControl(this.user.firstName, {\r\n        validators: [Validators.required]\r\n      }),\r\n      lname: new UntypedFormControl(this.user.lastName, {\r\n        validators: [Validators.required]\r\n      }),\r\n      company: new UntypedFormControl(\"\", {}),\r\n      profession: new UntypedFormControl(\"\", {}),\r\n      country: new UntypedFormControl(this.selectedResidenceCountry, {\r\n        validators: [Validators.required]\r\n      }),\r\n      street: new UntypedFormControl(\"\", {\r\n        validators: [Validators.required]\r\n      }),\r\n      number: new UntypedFormControl(\"\", {\r\n        validators: [Validators.required]\r\n      }),\r\n      city: new UntypedFormControl(\"\", {\r\n        validators: [Validators.required]\r\n      }),\r\n      postcode: new UntypedFormControl(\"\", {\r\n        validators: [Validators.required]\r\n      }),\r\n      tax: new UntypedFormControl(\"\", {\r\n      }),\r\n      email: new UntypedFormControl(this.user.email, {\r\n        validators: [Validators.required]\r\n      }),\r\n      phone: new UntypedFormControl(this.generalService.extractPhoneNumber(this.user.phone), {\r\n        validators: [Validators.required]\r\n      }),\r\n      tin: new UntypedFormControl(\"\", {\r\n      }),\r\n    })\r\n\r\n  }\r\n\r\n  updateFormValidationRules() {\r\n    if (this.isInvoice) {\r\n      this.form.controls['tin'].setValidators([Validators.required]);\r\n    } else {\r\n      this.form.controls['tin'].setValidators([]);\r\n    }\r\n\r\n    this.form.controls['tin'].updateValueAndValidity();\r\n  }\r\n\r\n  styleAll() {\r\n    setTimeout(() => {\r\n      if (!this.isNew) {\r\n        this.styleClassrooms();\r\n      } else {\r\n        this.selectedBtnStyle(`package-new`)\r\n      }\r\n      this.styleLanguages()\r\n      this.styleHours();\r\n      this.styleNumberOfStudents();\r\n    }, 1000);\r\n  }\r\n\r\n  deStyleAll() {\r\n    this.styleClassrooms();\r\n    this.styleLanguages()\r\n    this.styleHours();\r\n    this.styleNumberOfStudents();\r\n  }\r\n\r\n  styleClassrooms() {\r\n    this.selectedBtnStyle(`package-btn-${this.selectedClassroom.id}`, `package-btn-teacher-${this.selectedClassroom?.id}`)\r\n  }\r\n\r\n  styleLanguages() {\r\n    this.selectedBtnStyle(`language-btn-${this.selectedLanguage.code}`)\r\n  }\r\n\r\n  styleNumberOfStudents() {\r\n    if (this.selectedNumberOfStudents > \"3\") {\r\n      this.selectedNumberOfStudents = \"3+\"\r\n    }\r\n    this.selectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`)\r\n  }\r\n\r\n  styleHours() {\r\n    this.selectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-hours-${this.mltPricesHourly[this.selectedHoursIndex].price}`)\r\n    this.selectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-per-${this.mltPricesHourly[this.selectedHoursIndex].price}`)\r\n  }\r\n\r\n  selectedBtnStyle(id: string, extraId?: string) {\r\n    let color = this.inTrial ? '#4895ef' : this.getGradientColor(this.selectedPackageToBuy.type)\r\n    let btn = document.getElementById(id)\r\n\r\n    if (btn && this.selectedPackageToBuy) {\r\n      btn!.style.backgroundImage = color;\r\n    }\r\n    if (btn && !id.includes('package-new')) {\r\n      btn!.style.color = \"white\";\r\n      btn!.style.opacity = \"1\";\r\n    }\r\n\r\n    if (extraId) {\r\n      let extraElement = document.getElementById(extraId);\r\n      if (extraElement) {\r\n        extraElement.style.color = \"white\";\r\n      }\r\n    }\r\n  }\r\n\r\n  deSelectedBtnStyle(id: string, extraId?: string) {\r\n    let color = this.inTrial ? '#4895ef' : this.getCasualColor();\r\n    let btn = document.getElementById(id);\r\n\r\n    if (btn) {\r\n      if (!this.isNew && !id.includes('package-btn') && !id.includes('number-btn') && !id.includes('hour-btn') && !id.includes('package-new') && !id.includes('level')) {\r\n        btn!.style.color = \"#CCD6FF\";\r\n      } else {\r\n        btn!.style.color = color;\r\n      }\r\n      if (!id.includes('package-new')) {\r\n        btn!.style.backgroundColor = \"white\";\r\n        btn!.style.background = \"white\";\r\n      }\r\n    }\r\n\r\n    if (extraId) {\r\n      let extraElement = document.getElementById(extraId);\r\n      if (extraElement) {\r\n        extraElement.style.color = \"#2d2a4b\";\r\n      }\r\n    }\r\n\r\n  }\r\n\r\n  getBtnStyle(notToBeDisabled: boolean) {\r\n    let color = this.inTrial ? '#4895ef' : this.getCasualColor()\r\n    if (this.isNew || notToBeDisabled) {\r\n      return {\r\n        'border': `1px solid ${color}`,\r\n        'color': `${color}`,\r\n      }\r\n    }\r\n    return {\r\n      'border': `1px solid ${color}`,\r\n      'color': `${color}`,\r\n      'opacity': '0.5',\r\n      'cursor': 'initial'\r\n    }\r\n  }\r\n\r\n  disableLanguage(id: string) {\r\n    let btn = document.getElementById(id);\r\n    btn!.style.pointerEvents = 'none';\r\n    btn!.style.opacity = '0.5';\r\n  }\r\n\r\n  enableLanguage(id: string) {\r\n    let btn = document.getElementById(id);\r\n    btn!.style.pointerEvents = 'initial';\r\n    btn!.style.opacity = '1';\r\n  }\r\n\r\n  getLevelIsGreaterThanSelected(level: string) {\r\n    if (this.selectedClassroom.activeLevel == \"TBD\") {\r\n      return true;\r\n    }\r\n    return level >= this.selectedClassroom.activeLevel\r\n  }\r\n\r\n  compareLevels(level1: string, level2: string): number {\r\n    const levelsOrder = ['TBD', 'A1', 'A2', 'B1', 'B2', 'C1', 'C2', 'BS', 'NT21', 'NT22'];\r\n    return levelsOrder.indexOf(level1) - levelsOrder.indexOf(level2);\r\n  }\r\n\r\n  getGradientColor(type: string) {\r\n    if (type == PackageType.REGULAR) {\r\n      return \"linear-gradient(to top, #122171, #353792, #544eb3, #7366d6, #927ffa)\"\r\n    }\r\n    if (type == PackageType.FLEX) {\r\n      return \"linear-gradient(to bottom, #7588f4, #5f6ed1, #4955af, #323d8e, #1b276e)\"\r\n    }\r\n    if (type == PackageType.PREMIUM) {\r\n      return \"linear-gradient(to top, #122171, #004b99, #0074bb, #009ed7, #4dc8f0)\"\r\n    }\r\n    return \"\"\r\n  }\r\n\r\n  getCasualColor() {\r\n    return \"#657AEF\";\r\n  }\r\n\r\n  selectClassroom(classroom: Classroom) {\r\n    this.isNew = true;\r\n    [].forEach.call(document.getElementsByClassName('btn'), (el: any) => {\r\n      if (!el.id.includes('package-btn') && !el.id.includes('hour-btn') && !el.id.includes('package-new')) {\r\n        el.classList.remove('hvr-glow');\r\n      }\r\n      setTimeout(() => {\r\n        // el.classList.add('disabled')\r\n      }, 300);\r\n    });\r\n    this.deSelectedBtnStyle(`package-new`)\r\n    this.deSelectedBtnStyle(`package-btn-${this.selectedClassroom.id}`, `package-btn-teacher-${this.selectedClassroom?.id}`)\r\n    // this.deSelectedBtnStyle(`level-btn-${this.selectedLevel}`)\r\n    this.deSelectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`)\r\n    this.deSelectedBtnStyle(`language-btn-${this.selectedLanguage.code}`)\r\n\r\n    for (let language of this.mltLanguages) {\r\n      if (this.selectedClassroom.language == language.name) {\r\n        this.deSelectedBtnStyle(`language-btn-${language.code}`)\r\n      }\r\n    }\r\n\r\n    this.selectedClassroom = classroom;\r\n    // /Updates the selected classroom's student list by removing any duplicate students.\r\n    this.selectedClassroom.classroomStudents = this.classroomService.getuniqueClassroomStudents(this.selectedClassroom.classroomStudents);\r\n    for (let language of this.mltLanguages) {\r\n      if (this.selectedClassroom.language !== language.name) {\r\n        this.disableLanguage(`language-btn-${language.code}`);\r\n      }\r\n    }\r\n    this.selectedLevel = this.selectedClassroom.activeLevel\r\n    if (this.selectedClassroom.activeLevel == \"TBD\") {\r\n      this.selectedLevel = \"A1\"\r\n    }\r\n    this.selectedDefaultLevel = this.selectedClassroom.activeLevel;\r\n    this.selectedNumberOfStudents = this.selectedClassroom.classroomStudents.length.toString();\r\n    this.selectedLanguage = this.mltLanguages.filter(el => el.name.toLowerCase() == this.selectedClassroom.language!.toLowerCase())[0]\r\n    this.reqBody.language = this.selectedLanguage.name;\r\n    this.reqBody.level = this.selectedLevel;\r\n    this.toggleSplitPaymentDisplay();\r\n    console.log(this.showSplitPayment);\r\n    this.getNewPackagePrices();\r\n    this.getNewSplitPackagePrices();\r\n    this.styleAll();\r\n    this.isNew = false;\r\n  }\r\n\r\n  selectLanguage(language: Language) {\r\n    if (this.isNew) {\r\n      this.deSelectedBtnStyle(`language-btn-${this.selectedLanguage.code}`)\r\n      this.selectedLanguage = language\r\n      this.reqBody.language = this.selectedLanguage.name;\r\n      this.getNewPackagePrices();\r\n      for (let language of this.mltLanguages) {\r\n        this.deSelectedBtnStyle(`language-btn-${language.code}`);\r\n      }\r\n      this.styleLanguages();\r\n    }\r\n  }\r\n\r\n  selectLevel(level: string) {\r\n    if (this.isNew || this.getLevelIsGreaterThanSelected(level)) {\r\n      this.selectedLevel = level\r\n      this.reqBody.level = this.selectedLevel;\r\n      this.getNewPackagePrices();\r\n      this.getNewSplitPackagePrices();\r\n    }\r\n  }\r\n\r\n  selectNumber(number: string) {\r\n    if (this.isNew) {\r\n      this.deSelectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`)\r\n      this.selectedNumberOfStudents = number\r\n      this.selectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`)\r\n      this.reqBody.numberOfStudents = this.selectedNumberOfStudents;\r\n      this.getNewPackagePrices();\r\n      this.getNewSplitPackagePrices();\r\n    }\r\n  }\r\n\r\n  expiresIn: number = 1;\r\n\r\n  selectHours(hour: any, i: number) {\r\n    this.deSelectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-hours-${this.mltPricesHourly[this.selectedHoursIndex].price}`)\r\n    this.deSelectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-per-${this.mltPricesHourly[this.selectedHoursIndex].price}`)\r\n    this.selectedHoursIndex = i;\r\n    this.selectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-hours-${this.mltPricesHourly[this.selectedHoursIndex].price}`)\r\n    this.selectedBtnStyle(`hour-btn-${this.mltPricesHourly[this.selectedHoursIndex].price}`, `hour-btn-per-${this.mltPricesHourly[this.selectedHoursIndex].price}`)\r\n    this.selectedPrice = this.mltPricesHourly[this.selectedHoursIndex].price;\r\n    this.expiresIn = this.mltPricesHourly[this.selectedHoursIndex].expirationMonths;\r\n    this.selectedHours = this.mltPricesHourly[this.selectedHoursIndex];\r\n  }\r\n\r\n  selectPackage(packageType: any) {\r\n    this.selectedPackageToBuy = packageType\r\n    this.rightSide.style.background = this.getGradientColor(this.selectedPackageToBuy.type);\r\n    this.styleAll();\r\n    \r\n    this.hasSwitchedPackage.set(false);\r\n  }\r\n\r\n  buyNew() {\r\n    [].forEach.call(document.getElementsByClassName('btn'), (el: any) => {\r\n      el.classList.add('hvr-glow')\r\n      el.classList.remove('disabled')\r\n    });\r\n    for (let language of this.mltLanguages) {\r\n      if (this.selectedClassroom.language !== language.name) {\r\n        this.enableLanguage(`language-btn-${language.code}`);\r\n      }\r\n    }\r\n    this.deSelectedBtnStyle(`package-btn-${this.selectedClassroom.id}`, `package-btn-teacher-${this.selectedClassroom?.id}`)\r\n    this.selectedBtnStyle(`package-new`)\r\n    this.isNew = true;\r\n    this.selectedLanguage = this.mltLanguages[0];\r\n    this.selectLanguage(this.selectedLanguage);\r\n    this.deSelectedBtnStyle(`level-btn-${this.selectedLevel}`);\r\n    this.selectedLevel = \"A1\";\r\n    this.selectedDefaultLevel = \"A1\";\r\n    this.selectLevel(this.selectedLevel);\r\n    this.deSelectedBtnStyle(`number-btn-${this.selectedNumberOfStudents}`);\r\n    this.selectedNumberOfStudents = \"1\";\r\n    this.styleAll();\r\n    this.getNewPackagePrices();\r\n    // this.getNewSplitPackagePrices();\r\n    this.showSplitPayment = false;\r\n  }\r\n\r\n  isReceipt: boolean = true;\r\n  isInvoice: boolean = false;\r\n  isSubmitted: boolean = false;\r\n\r\n  chooseReceipt() {\r\n    this.isReceipt = true;\r\n    this.isInvoice = false;\r\n    this.updateFormValidationRules();\r\n  }\r\n\r\n  chooseInvoice() {\r\n    this.isReceipt = false;\r\n    this.isInvoice = true;\r\n    this.updateFormValidationRules();\r\n  }\r\n\r\n  next() {\r\n    window.scrollTo(0, 0)\r\n    this.currentStep++;\r\n    if (this.currentStep == 2) {\r\n      return;\r\n    }\r\n    if (this.currentStep == 3) {\r\n      this.isSubmitted = true;\r\n\r\n      if (this.form.valid) {\r\n        let formValue = this.form.value\r\n        this.buyerUserDetails.fname = formValue.fname\r\n        this.buyerUserDetails.lname = formValue.lname\r\n        this.buyerUserDetails.email = formValue.email\r\n        this.buyerUserDetails.city = formValue.city\r\n        this.buyerUserDetails.company = formValue.company\r\n        this.buyerUserDetails.streetName = formValue.street\r\n        this.buyerUserDetails.streetNumber = formValue.number\r\n        this.buyerUserDetails.tax = formValue.tax\r\n        this.buyerUserDetails.profession = formValue.profession\r\n        this.buyerUserDetails.postcode = formValue.postcode\r\n        this.buyerUserDetails.tin = formValue.tin\r\n        this.buyerUserDetails.phone = formValue.phone\r\n        this.buyerUserDetails.country = formValue.country.name\r\n        return;\r\n      } else {\r\n\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'info',\r\n          summary: '',\r\n          detail: 'Please fill all the required fields with *'\r\n        });\r\n        this.currentStep = 2;\r\n        return;\r\n      }\r\n    }\r\n    if (this.currentStep == 4) {\r\n      this.payload.amount = (this.selectedPrice + this.selectedPackageToBuy.costPlus) * 100;\r\n      return;\r\n    }\r\n    if (this.currentStep < 5) {\r\n      this.currentStep++;\r\n    }\r\n    // let classroom = this.isNew ? null : this.selectedClassroom;\r\n    // this.packageToBuyOrderDeails = {\r\n    //   isNew: this.isNew,\r\n    //   selectedClassroom: classroom,\r\n    //   selectedLevel: this.selectedLevel,\r\n    //   selectedLanguage: this.selectedLanguage,\r\n    //   selectedNumberOfStudents: this.selectedNumberOfStudents,\r\n    //   // selectedPackageToBuy: this.selectedPackageToBuy,\r\n    //   buyerUserDetails: this.buyerUserDetails\r\n    // }\r\n  }\r\n\r\n  previous() {\r\n    window.scrollTo(0, 0)\r\n    this.styleAll()\r\n    if (this.currentStep > 1) {\r\n      this.currentStep--;\r\n    } else {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'info',\r\n        summary: '',\r\n        detail: 'There is no previous step'\r\n      });\r\n    }\r\n  }\r\n\r\n  goBack() {\r\n    this.location.back();\r\n  }\r\n\r\n  toggleSection(sectionElement: any, sectionArrowImgSrc: any) {\r\n    this.generalService.toggleSection(sectionElement, sectionArrowImgSrc, true)\r\n  }\r\n  agreed: boolean = false;\r\n  agree() {\r\n    this.agreed = !this.agreed;\r\n  }\r\n\r\n  termsError: boolean = false;\r\n  purchase() {\r\n\r\n    if (this.agreed) {\r\n      let classroom = this.isNew ? 0 : this.selectedClassroom.id;\r\n      let req = {\r\n        token: \"\",\r\n        language: this.selectedLanguage.name,\r\n        level: this.selectedLevel,\r\n        studentsNumber: this.selectedNumberOfStudents,\r\n        hours: this.selectedHours.hours.toString(),\r\n        packageType: this.selectedPackageToBuy.type,\r\n        merchant_order_ext_ref: \"\",\r\n        classroomId: classroom,\r\n        details: {\r\n          billingType: this.selectedBillingType,\r\n          firstname: this.buyerUserDetails.fname,\r\n          lastname: this.buyerUserDetails.lname,\r\n          email: this.buyerUserDetails.email,\r\n          phone: this.selectedPhoneCode.code + ' ' + this.buyerUserDetails.phone,\r\n          address: this.buyerUserDetails.streetName + \" \" + this.buyerUserDetails.streetNumber,\r\n          city: this.buyerUserDetails.city,\r\n          country: this.buyerUserDetails.country,\r\n          postcode: this.buyerUserDetails.postcode,\r\n          company: this.buyerUserDetails.company,\r\n          profession: this.buyerUserDetails.profession,\r\n          taxOffice: this.buyerUserDetails.tax,\r\n          vat: this.buyerUserDetails.tin,\r\n        }\r\n      }\r\n      this.initializePaymentForm(req);\r\n    }\r\n    else {\r\n      this.termsError = true;\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'info',\r\n        summary: '',\r\n        detail: 'Please agree to the terms and conditions to make the order'\r\n      });\r\n    }\r\n    //   // this.currentStep = 4;\r\n\r\n    //   this.packageService.requestbuyPackage(req).subscribe((res: any) => {\r\n    //     this.everypayLink = this.transform(res.result);\r\n    //     this.generalService.slideNativeElements(true, this.everypay.nativeElement)\r\n    //   })\r\n    // } else {\r\n    //   this.termsError = true;\r\n\r\n    // }\r\n  }\r\n\r\n  sendRequestTrial() {\r\n    if (this.selectedNumberOfStudents == \"3+\") {\r\n      this.selectedNumberOfStudents = \"4\"\r\n    }\r\n    let req = {\r\n      trialLanguage: this.selectedLanguage.name,\r\n      trialLevel: this.selectedLevel,\r\n      trialStudentNumber: this.selectedNumberOfStudents\r\n    }\r\n    this.packageService.requestTrial(req).pipe(take(1)).subscribe(res => {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'success',\r\n        summary: '',\r\n        detail: 'Your trial has been requested.'\r\n      });\r\n    })\r\n  }\r\n\r\n  closePayment() {\r\n    this.router.navigate(['dashboard'])\r\n  }\r\n\r\n  async initializeData() {\r\n    try {\r\n      const res = await this.classroomService.getLMSUserClassrooms(this.user.id)\r\n        .pipe(take(1))\r\n        .toPromise(); // Convert to Promise and await its resolution\r\n\r\n      // Proceed after receiving the response\r\n      this.userClassrooms = res.filter((item: Classroom) => item && item.language && item.language.trim() !== '');\r\n      this.userClassrooms = this.filterOutTrialClassroomsWithSameTeacher(this.userClassrooms);\r\n      // Create an object to track classrooms by teacher ID\r\n      const classroomsByTeacher: Record<string, Classroom> = {};\r\n\r\n      // Update this.userClassrooms and classroomFromQueryParams\r\n      this.userClassrooms = this.userClassrooms.filter((response) => {\r\n        const teacherId = response.teacher?.id ?? null;\r\n\r\n        if (teacherId !== null) {\r\n          // Check if the response is of type \"Paid\" or if there is no existing response for this teacher ID\r\n          if (response.type === \"Paid\" || !(teacherId in classroomsByTeacher)) {\r\n            classroomsByTeacher[teacherId] = response; // Store the response\r\n          }\r\n\r\n          if (\r\n            this.classroomFromQueryParams &&\r\n            this.classroomFromQueryParams.type === \"Trial\" &&\r\n            this.classroomFromQueryParams.teacher &&\r\n            this.classroomFromQueryParams.teacher.id === teacherId\r\n          ) {\r\n            // Replace classroomFromQueryParams with the paid response\r\n            this.classroomFromQueryParams = response;\r\n          }\r\n\r\n          return response; // else Include all classrooms\r\n        }\r\n\r\n        return true; // Include classrooms with no teacher ID\r\n      });\r\n\r\n\r\n      if (this.userClassrooms.length == 0) {\r\n        this.isNew = true;\r\n        this.selectedLanguage = this.mltLanguages[0];\r\n        this.selectedLevel = \"A1\";\r\n        this.selectedNumberOfStudents = \"1\";\r\n        this.selectedDefaultLevel = \"A1\";\r\n      }\r\n      else {\r\n        if (this.classroomFromQueryParams.id) {\r\n          this.selectedClassroom = this.classroomFromQueryParams;\r\n        } else {\r\n          this.selectedClassroom = this.userClassrooms[0];\r\n        }\r\n        for (let language of this.mltLanguages) {\r\n          if (this.selectedClassroom.language !== language.name) {\r\n            this.disableLanguage(`language-btn-${language.code}`);\r\n          }\r\n        }\r\n        this.selectedClassroom.classroomStudents = this.classroomService.getuniqueClassroomStudents(this.selectedClassroom.classroomStudents);\r\n        this.selectedNumberOfStudents = this.selectedClassroom.classroomStudents.length.toString();\r\n        this.selectedLevel = this.selectedClassroom.activeLevel;\r\n        if (this.selectedLevel == \"TBD\") {\r\n          this.selectedLevel = \"A1\";\r\n        }\r\n        this.selectedDefaultLevel = this.selectedClassroom.activeLevel;\r\n        this.selectedLanguage = this.mltLanguages.filter(el => el.name == this.selectedClassroom.language!)[0];\r\n        this.toggleSplitPaymentDisplay();\r\n      }\r\n      this.initializeForm();\r\n      this.updateFormValidationRules();\r\n      if (this.selectedNumberOfStudents <= \"3\") {\r\n        this.reqBody = {\r\n          \"language\": this.selectedLanguage.name,\r\n          \"level\": this.selectedLevel,\r\n          \"numberOfStudents\": this.selectedNumberOfStudents\r\n        }\r\n      }\r\n      else {\r\n        this.reqBody = {\r\n          \"language\": this.selectedLanguage.name,\r\n          \"level\": this.selectedLevel,\r\n          \"numberOfStudents\": \"2\"\r\n        }\r\n      }\r\n      this.selectedPackageToBuy = this.defaultPackages[1];\r\n      this.mltPricesHourly = this.defaultPackages[0].expiresIn;\r\n      this.getNewPackagePrices();\r\n      this.getNewSplitPackagePrices();\r\n      this.selectedHours = this.mltPricesHourly[2];\r\n      this.expiresIn = this.mltPricesHourly[2].expirationMonths;\r\n      this.styleAll();\r\n    } catch (error) {\r\n      console.error('An error occurred:', error);\r\n    }\r\n  }\r\n\r\n  async updatePrices(): Promise<void> {\r\n    return new Promise<void>((resolve, reject) => {\r\n      try {\r\n        this.mltPricesHourly.forEach((item, index) => {\r\n          item.price = this.newPackages[index].price;\r\n          item.perHour = this.newPackages[index].perHour;\r\n        });\r\n        resolve();\r\n      } catch (error) {\r\n        reject(error);\r\n      }\r\n    });\r\n  }\r\n\r\n  async getNewPackagePrices(): Promise<void> {\r\n    try {\r\n      const res = await this.packageService.getUpdatedPackagesToBuy(this.reqBody).pipe(take(1)).toPromise();\r\n      this.newPackages = res as any[];\r\n      await this.updatePrices();\r\n      this.selectedPrice = this.selectedHours.price;\r\n    } catch (error) {\r\n      console.log('An error occurred:', error);\r\n    }\r\n  }\r\n\r\n  async getNewSplitPackagePrices(): Promise<void> {\r\n    try {\r\n      const res = await this.packageService.getSplitPackagesToBuy(this.reqBody).pipe(take(1)).toPromise();\r\n      this.splitPaymentPackages = res as any[];\r\n    } catch (error) {\r\n      console.log('An error occurred:', error);\r\n    }\r\n  }\r\n\r\n  onPhoneCodeChange(event: any) {\r\n    setTimeout(() => {\r\n      this.selectedPhoneCode = event.value;\r\n    }, 100);\r\n  }\r\n\r\n  initializePaymentForm(data: any) {\r\n    console.log(data);\r\n    this.subs.add((this.hasSplitPayment ? this.packageService.createSplitRevolutOrder(data) : this.packageService.createRevolutOrder(data)).pipe(\r\n      switchMap((order: any) => {\r\n        console.log('order', order);\r\n        var that = this;\r\n        RevolutCheckout(order.public_id, isProductionMode ? 'prod' : 'sandbox').then(function (instance: any) {\r\n          instance.payWithPopup({\r\n            onSuccess() {\r\n              data.token = order.public_id;\r\n              data.merchant_order_ext_ref = order.merchant_order_ext_ref;\r\n              // let event = new CustomEvent('dosomething', {detail: 'hello'});\r\n              that.completeOrder(data, order);\r\n            },\r\n            onError(message: any) {\r\n              console.log(message);\r\n            }\r\n          });\r\n        });\r\n        return of(data);\r\n      }),\r\n      take(1)\r\n    ).subscribe((res: any) => { }))\r\n  }\r\n\r\n  completeOrder(paymentResponse: any, order: any) {\r\n    this.subs.add((this.hasSplitPayment ? this.packageService.completeSplitOrder(paymentResponse) : this.packageService.completeOrder(paymentResponse)).subscribe((res: any) => {\r\n\r\n      this.currentStep = 4;\r\n      if (this.leadDynoScriptLoaded) {\r\n        const purchaseData = {\r\n          key: this.leadDynoPrivateKey, // Replace with your Leaddyno API key\r\n          email: order.email,\r\n          purchase_amount: order.order_amount.value / 100, // Convert to the correct currency\r\n          purchase_code: order.merchant_order_ext_ref,\r\n          affiliate: localStorage.getItem('leadDynoAffiliateId') || '',\r\n          line_items: [\r\n            {\r\n              sku: order.id,\r\n              quantity: 1,\r\n              description: paymentResponse.language + ' - ' + paymentResponse.level + ' level - ' + paymentResponse.hours + ' hours',\r\n              amount: order.order_amount.value / 100\r\n            },\r\n          ],\r\n        };\r\n\r\n        // affiliate exists\r\n        if (this.leadDynoAffiliateId) {\r\n          this.packageService.getLeadDynoAffiliateByAffiliateCode(this.leadDynoAffiliateId,\r\n            this.leadDynoPrivateKey).subscribe({\r\n              next: (res: LeadDynoAffiliate) => {\r\n                console.log(res);\r\n                const randId = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);\r\n                const purchaseData =\r\n                {\r\n                  key: this.leadDynoPrivateKey,\r\n                  email: this.user.email,\r\n                  purchase_amount: order.order_amount.value / 100, // Convert to the correct currency\r\n                  purchase_code: order.merchant_order_ext_ref,\r\n                  code: res.affiliate_code || this.leadDynoAffiliateId,\r\n                  affiliate: res.email,\r\n                  line_items: [\r\n                    {\r\n                      sku: order.merchant_order_ext_ref,\r\n                      quantity: 1,\r\n                      description: order.merchant_order_ext_ref,\r\n                      amount: order.order_amount.value / 100,\r\n                    },\r\n                  ],\r\n\r\n                };\r\n                this.packageService.addPurchaseEvent(purchaseData).subscribe((res: any) => {\r\n                  console.log(res);\r\n                });\r\n              },\r\n              error: (err: any) => {\r\n                console.log(err);\r\n              }\r\n            });\r\n        } else {\r\n          // without affiliate\r\n          this.packageService.addPurchaseEvent(purchaseData).subscribe((res: any) => {\r\n            console.log(res);\r\n          });\r\n        }\r\n      }\r\n\r\n    }))\r\n  }\r\n\r\n  getTotalSplitPaymentAmount(): number {\r\n    if (this.splitPaymentPackages.length === 0) {\r\n      return 0;\r\n    }\r\n    const selectedPackage = this.splitPaymentPackages[this.selectedHoursIndex];\r\n    const additionalCost = this.selectedPackageToBuy.costPlus * 0.5;\r\n\r\n    return (selectedPackage?.companyFee || 0) + additionalCost;\r\n  }\r\n\r\n  getRemainingAmount(): number {\r\n    const totalPrice = this.selectedPrice + this.selectedPackageToBuy.costPlus;\r\n    return totalPrice - this.getTotalSplitPaymentAmount();\r\n  }\r\n\r\n  switchBackToPackageType(event: mltPackages) {\r\n    console.log(event);\r\n    this.selectedPackageToBuy = event;\r\n    this.hasSwitchedPackage.set(true);\r\n  }\r\n\r\n  onSplitPaymentPayNowTooltipSelected() {\r\n    this.confirmDialogService.setProperties({\r\n      confirmMessage: `The booking fee is an upfront payment, ensuring the immediate availability of your preferred teacher.`,\r\n      acceptBtnLabel: 'OK',\r\n      action: 'cancel-lesson',\r\n      confirmIcon: '',\r\n      acceptBtnImage: '',\r\n      acceptBtnIcon: 'pi pi-check',\r\n      showHeader: true,\r\n      dialogType: 'custom',\r\n      showConfirmIcon: false,\r\n      headerClass: 'my-custom-header-class',\r\n      headerText: 'Booking Fee'\r\n    });\r\n    this.confirmDialogService.show();\r\n  }\r\n\r\n  onSplitPaymentPayLaterTooltipSelected() {\r\n    this.confirmDialogService.setProperties({\r\n      confirmMessage: `\r\n      Once your booking fee payment is completed, a customer service representative will contact you shortly to provide further assistance. \r\n      They will guide you on how to complete the payment process.`,\r\n      acceptBtnLabel: 'OK',\r\n      action: 'cancel-lesson',\r\n      confirmIcon: '',\r\n      acceptBtnImage: '',\r\n      acceptBtnIcon: 'pi pi-check',\r\n      showHeader: true,\r\n      dialogType: 'custom',\r\n      showConfirmIcon: false,\r\n      headerClass: 'my-custom-header-class',\r\n      headerText: 'What is a Remaining Balance?'\r\n    });\r\n    this.confirmDialogService.show();\r\n  }\r\n\r\n  private toggleSplitPaymentDisplay() {\r\n    this.showSplitPayment = this.selectedClassroom!.teacher!.accSlitPayments!;\r\n    console.log(this.selectedPackageToBuy);\r\n  }\r\n\r\n  /**\r\n  Filters out trial classrooms with the same teacher.\r\n  @param {Classroom[]} userClassrooms - An array of classrooms for the user.\r\n  @returns {Classroom[]} - An array of classrooms with the same teacher as the user.\r\n  */\r\n  private filterOutTrialClassroomsWithSameTeacher(userClassrooms: Classroom[]): Classroom[] {\r\n    return userClassrooms.filter((classroom: Classroom) => {\r\n      // Check if classroom is a trial\r\n      if (classroom.type === 'Trial') {\r\n        // Find classrooms with the same teacher asp user id\r\n        const sameTeacherClassrooms = userClassrooms.filter((c) => c.teacher!.aspUserId === classroom.teacher!.aspUserId);\r\n\r\n        // Filter out the trial classroom if there are other classrooms with the same teacher asp user id\r\n        if (sameTeacherClassrooms.length > 1) {\r\n          return false; // Exclude the trial classroom\r\n        }\r\n      }\r\n\r\n      return true; // Include other classrooms\r\n    });\r\n  }\r\n}\r\n", "<div class=\"buy-package\">\r\n    <div id=\"class\"\r\n        style=\"background-image: linear-gradient(to left, #7588f4, #a0a4f7, #c3c1fa, #e3dffc, #ffffff); border-radius: 14px;\">\r\n        <div *ngIf=\"!inTrial\" class=\"buy-package-header\">\r\n            <button (click)=\"goBack()\" type=\"button\"\r\n                class=\"p-element py-1 p-button-outlined p-button-rounded button-outlined p-button-sm p-button p-component ng-star-inserted\">\r\n                <div class=\"flex align-items-center gap-1\"><span\r\n                        class=\"p-datepicker-prev-icon pi pi-chevron-left\"></span><span\r\n                        class=\"text-base s12-16\">Back</span></div>\r\n            </button>\r\n            <div class=\"buy-package-title font-lg font-semibold\">\r\n                Buy Package\r\n            </div>\r\n            <div *ngIf=\"currentStep!=4\" class=steps>\r\n                <div class=\"package-step\">\r\n                    {{currentStep}}/3\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div *ngIf=\"inTrial\">\r\n            <div class=\"white-button\" (click)=\"trialSection()\">\r\n                Request new Trial\r\n            </div>\r\n        </div>\r\n\r\n\r\n        <div *ngIf=\"!hideSection\" [@slideInOut] class=\"section\">\r\n\r\n            <div *ngIf=\"currentStep==1\" class=\"left pt-0\" [style.width]=\"inTrial ? '100%': ''\">\r\n                <div *ngIf=\"!inTrial\">\r\n                    <div class=\"title my-2 font-sm\">\r\n                        Select Classroom\r\n                    </div>\r\n                    <div class=\"top-btns my-0\">\r\n                        <div class=\"btn btn-new btn-classroom new mb-2 mt-0 hvr-glow col-6 lg:col-3 hvr-glow\"\r\n                            id=\"package-new\" (click)=\"buyNew()\">\r\n                            <span class=\"flex-column\">NEW <span class=\"btn-teacher-name font-2xs m-0\"\r\n                                    style=\"min-height: auto;color: #3345a7;\">language</span></span>\r\n                        </div>\r\n                        <div *ngFor=\"let classroom of userClassrooms\"\r\n                            class=\"btn btn-classroom hvr-glow col-6 lg:col-3 mb-2 mt-0 hvr-glow\"\r\n                            [ngStyle]=\"getBtnStyle(true)\" id=\"package-btn-{{classroom.id}}\"\r\n                            (click)=\"selectClassroom(classroom)\">\r\n                            <span class=\"btn-language font-sm lang capitalize\">\r\n                                {{classroom.language}}\r\n                            </span>\r\n                            <br>\r\n                            <div class=\"btn-teacher-name font-2xs\" id=\"package-btn-teacher-{{classroom.id}}\">\r\n                                with {{classroom.teacher.firstName}} {{classroom.teacher.lastName | slice:0:1}}.\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div [@slideInOut] *ngIf=\"!hideSection\" [style.marginTop]=\"inTrial ? '30px' : '0'\">\r\n                    <div class=\"title font-sm mt-1\">\r\n                        Select Language\r\n                    </div>\r\n                    <div class=\"top-btns my-0\">\r\n                        <div *ngFor=\"let language of mltLanguages\" class=\"btn btn-new btn-language p-2 w-4rem\"\r\n                            [ngStyle]=\"getBtnStyle(false)\" id=\"language-btn-{{language.code}}\"\r\n                            (click)=\"selectLanguage(language)\">\r\n                            {{language.code}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div [@slideInOut] *ngIf=\"!hideSection\">\r\n                    <div class=\"title font-sm\">\r\n                        Select Level\r\n                    </div>\r\n                    <div class=\"top-btns my-0\">\r\n                        <div *ngFor=\"let level of mltLevels\" class=\"btn btn-new btn-level level\"\r\n                            id=\"level-btn-{{level}}\" (click)=\"selectLevel(level)\" [ngClass]=\"{'level-default': compareLevels(level, selectedLevel) >= 0, 'level-disabled': compareLevels(level, selectedDefaultLevel) < 0 && !isNew, 'level-selected': level == selectedLevel,\r\n                            'hidden': level == 'NT2.1' || level == 'NT2.2' || level == 'TBD' || level == 'Any'}\">\r\n                            <div [matTooltip]=\"(level == 'BS') ? 'Business':''\">\r\n                                <div (click)=\"selectLevel(level)\">{{level}}</div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div *ngIf=\"selectedLanguage.code == 'DUT'\">\r\n                    <div class=\"top-btns my-0\">\r\n                        <div *ngFor=\"let level of mltLevels\" class=\"btn btn-new btn-level level\"\r\n                            id=\"level-btn-{{level}}\" (click)=\"selectLevel(level)\" [ngClass]=\"{'level-default': compareLevels(level, selectedLevel) >= 0, 'level-disabled': compareLevels(level, selectedLevel) < 0, 'level-selected': level == selectedLevel,\r\n                        'hidden': level !== 'NT2.1' && level !== 'NT2.2'}\">\r\n                            <div>\r\n                                {{level}}\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div [@slideInOut] *ngIf=\"!hideSection\">\r\n                    <div class=\"title font-sm mt-1\">\r\n                        Select Number of Students\r\n                    </div>\r\n                    <div class=\"top-btns my-0 justify-content-center gap-2\">\r\n                        <div *ngFor=\"let number of numberOfStudents\" class=\"btn py-2 col-2 btn-new btn-language\"\r\n                            [ngStyle]=\"getBtnStyle(false)\" id=\"number-btn-{{number}}\" (click)=\"selectNumber(number)\">\r\n                            {{number}}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div *ngIf=\"!inTrial\">\r\n                    <div class=\"title font-sm mt-1\">\r\n                        Select Hours\r\n                    </div>\r\n                    <div class=\"top-btns top-btns-hours m-0 notranslate\">\r\n                        <div *ngFor=\"let hour of mltPricesHourly; let i = index\" class=\"btn btn-hours hvr-glow\"\r\n                            [ngStyle]=\"getBtnStyle(true)\" id=\"hour-btn-{{hour.price}}\" (click)=\"selectHours(hour, i)\">\r\n                            <div class=\"hours-info font-xs\" id=\"hour-btn-hours-{{hour.price}}\">\r\n                                {{hour.hours}} hours\r\n                            </div>\r\n                            <div class=\"price font-semibold\">\r\n                                {{hour.price}}&euro;\r\n                            </div>\r\n                            <div class=\"hours-info font-xs\" id=\"hour-btn-per-{{hour.price}}\">\r\n                                {{hour.perHour}}&euro; / hour\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div [@slideInOut] *ngIf=\"!hideSection && inTrial\" class=\"btns\">\r\n                    <div class=\"main-color-button hvr-glow\"\r\n                        style=\"background: linear-gradient(#a4a2e6 0%, #4895ef 100%);\" (click)=\"sendRequestTrial()\">\r\n                        Send Request for the Trial\r\n                    </div>\r\n                </div>\r\n                <div *ngIf=\"!inTrial\">\r\n                    <div class=\"title font-sm\">\r\n                        Select Package Type\r\n                    </div>\r\n                    <div class=\"grid justify-content-around packages\">\r\n                        <div *ngFor=\"let package of defaultPackages; let i = index\" class=\"package-type lg:w-11rem p-0\"\r\n                            [style.borderColor]=\"getCasualColor(package.type)\">\r\n                            <div class=\"package-type-section\">\r\n                                <div class=\"type-name relative\"\r\n                                    [style.background]=\"i === 0 ? 'linear-gradient(to right, #122171, #353792, #544eb3, #7366d6, #927ffa)' : i === defaultPackages.length - 1 ? 'linear-gradient(to right, #122171, #004b99, #0074bb, #009ed7, #4dc8f0)' : 'linear-gradient(to left, #8497ff, #6977da, #4e59b6, #323c93, #122171)'\">\r\n                                    {{package.type}}\r\n                                    <div class=\"my-radio absolute\"\r\n                                        [ngClass]=\"{'selected': package.type == selectedPackageToBuy.type}\"\r\n                                        (click)=\"selectPackage(package)\">\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"package-type-section py-2 border-noround\">\r\n                                    <div>\r\n                                        <strong>\r\n                                            {{package.costPlus !== 0 ? '+' + package.costPlus : '- '}}&euro;\r\n                                        </strong>\r\n                                    </div>\r\n                                </div>\r\n                                <div class=\"package-info p-3 font-sm\">\r\n                                    <div class=\"flex align-items-start\">\r\n                                        <img src=\"/assets/images/dashboard/check.svg\" alt=\"\" class=\"w-1rem mr-1\">\r\n                                        <span *ngIf=\"package.type=='Regular'\">Expires in {{expiresIn}} months</span>\r\n                                        <span *ngIf=\"package.type!='Regular'\">Expires in +{{package.expiresPlus}}</span>\r\n                                    </div>\r\n                                    <div class=\"flex align-items-start my-1\">\r\n                                        <img src=\"/assets/images/dashboard/check.svg\" alt=\"\" class=\"w-1rem mr-1\">\r\n                                        <span>{{package.cancelation}} hours cancellation policy</span>\r\n                                    </div>\r\n                                    <div class=\"flex align-items-start my-1\">\r\n                                        <ng-container *ngIf=\"package.type!='Regular' else elseAiChatIconBlock\">\r\n                                            <img src=\"/assets/images/dashboard/check.svg\" alt=\"\" class=\"w-1rem mr-1\">\r\n                                        </ng-container>\r\n                                        <ng-template #elseAiChatIconBlock>\r\n                                            <img src=\"/assets/images/dashboard/package-tick-error.png\" alt=\"\"\r\n                                                class=\"w-1rem mr-1\">\r\n                                        </ng-template>\r\n                                        <span>AI Chat</span>\r\n                                    </div>\r\n                                    <div *ngIf=\"package.type!='Regular' && package.pause\"\r\n                                        class=\"flex align-items-start\">\r\n                                        <img src=\"/assets/images/dashboard/check.svg\" alt=\"\" class=\"w-1rem mr-1\">\r\n                                        <span>{{package.pause}} pause available</span>\r\n                                    </div>\r\n                                </div>\r\n                                <div *ngIf=\"package.type == selectedPackageToBuy.type\"\r\n                                    class=\"package-type-section py-1 flex flex-row justify-content-center custom-border\">\r\n                                    <img src=\"/assets/icons/check-blue.svg\" alt=\"\" class=\"w-1rem mr-1\">\r\n                                    <span class=\"font-sm font-semibold\">Package selected</span>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div *ngIf=\"currentStep==2\" class=\"left\">\r\n\r\n                <!-- <li class=\"flex flex-column md:flex-row md:align-items-center md:justify-content-between p-3 border-1 mb-3 bg-green-50 border-green-500\" style=\"border-radius: 10px;\"><div><div><span class=\"inline-flex justify-content-center align-items-center w-2rem h-2rem border-circle border-1 border-green-200\"><i class=\"pi pi-file text-green-600\"></i></span><span class=\"text-green-700 font-bold ml-2\">Dianne Russell</span></div><p class=\"text-green-700 mt-2 mb-0\">Due <span class=\"font-medium\">21/10/2021</span></p></div><div class=\"flex align-items-center justify-content-between md:justify-content-end mt-3 md:mt-0\"><span class=\"bg-green-400 text-green-900 font-bold text-sm py-1 px-2\" style=\"border-radius: 10px;\">PAID</span><div class=\"text-right ml-3\"><span class=\"text-green-700 font-bold\">82.50</span><p class=\"mt-1 mb-0 text-green-700\">EUR</p></div></div></li> -->\r\n\r\n                @if (hasSwitchedPackage()) {\r\n                <div\r\n                    class=\"border-1 bg-blue-50 border-blue-500 border-round p-1 sm:p-3 mb-2 flex flex-row align-items-center z-1\">\r\n                    <i class=\"pi pi-check-circle price-blue text-2xl md:text-4xl mb-2 md:mb-0 mr-2 md:mr-3\"></i>\r\n                    <div>\r\n                        <div class=\"text-900 font-medium mb-1\">Great Choice!</div><span class=\"text-600 text-sm\">\r\n                            @if (selectedPackageToBuy.type==='Flexible') {\r\n                            The Flexible Package ensures you have the time and flexibility you need.\r\n                            } @else {\r\n                            You’re all set with the extra benefits of the Premium Package.\r\n                            }\r\n                        </span>\r\n                    </div>\r\n                </div>\r\n                }\r\n\r\n\r\n                @if (selectedPackageToBuy?.type !== 'Premium' && !hasSwitchedPackage()) {\r\n                <ng-container>\r\n                    <app-buy-package-suggestion-box [preselectedPackage]=\"selectedPackageToBuy\"\r\n                        (switchBackToPackageTypeSelected)=\"switchBackToPackageType($event)\"></app-buy-package-suggestion-box>\r\n                </ng-container>\r\n                }\r\n\r\n\r\n                <form [formGroup]=\"form\">\r\n                    <div class=\"input-fields\">\r\n                        <div class=\"input-field font-semibold mb-3\">\r\n                            Select Billing Type\r\n                        </div>\r\n                        <div class=\"input-field\">\r\n                            <div class=\"flex flex-wrap gap-3\">\r\n                                <div class=\"flex align-items-center\">\r\n                                    <p-radioButton name=\"billing\" value=\"receipt\" [(ngModel)]=\"selectedBillingType\"\r\n                                        inputId=\"receipt\" (click)=\"chooseReceipt()\"\r\n                                        [ngModelOptions]=\"{standalone: true}\"></p-radioButton>\r\n                                    <label for=\"receipt\" class=\"ml-2 font-sm\">Receipt</label>\r\n                                </div>\r\n\r\n                                <div class=\"flex align-items-center\">\r\n                                    <p-radioButton name=\"billing\" value=\"invoice\" [(ngModel)]=\"selectedBillingType\"\r\n                                        inputId=\"invoice\" (click)=\"chooseInvoice()\"\r\n                                        [ngModelOptions]=\"{standalone: true}\"></p-radioButton>\r\n                                    <label for=\"invoice\" class=\"ml-2 font-sm\">Invoice</label>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                        <div class=\"input-field font-sm\">\r\n                            <div class=\"input-element-title\">* First Name</div>\r\n                            <input class=\"input-element\" formControlName=\"fname\" type=\"text\">\r\n                            <div *ngIf=\"(isSubmitted || errorControl.fname.touched) && errorControl.fname.errors?.required\"\r\n                                class=\"input-error\">* First Name is required</div>\r\n                        </div>\r\n                        <div class=\"input-field font-sm\">\r\n                            <div class=\"input-element-title\">* Last Name</div>\r\n                            <input class=\"input-element\" formControlName=\"lname\" type=\"text\">\r\n                            <div *ngIf=\"(isSubmitted || errorControl.lname.touched) && errorControl.lname.errors?.required\"\r\n                                class=\"input-error\">* Last Name is required</div>\r\n                        </div>\r\n                        <div class=\"input-field font-sm\">\r\n                            <div class=\"input-element-title\">Company (optional)</div>\r\n                            <input class=\"input-element\" formControlName=\"company\" type=\"text\">\r\n                        </div>\r\n                        <div class=\"input-field font-sm\">\r\n                            <div class=\"input-element-title\">Profession (optional)</div>\r\n                            <input class=\"input-element\" formControlName=\"profession\" type=\"text\">\r\n                        </div>\r\n                        <div *ngIf=\"isInvoice\" class=\"input-field font-sm\">\r\n                            <div class=\"input-element-title\">* T.I.N. / V.A.T.</div>\r\n                            <input class=\"input-element\" formControlName=\"tin\" type=\"text\">\r\n                            <div *ngIf=\"(isSubmitted || errorControl.tin.touched) && errorControl.tin.errors?.required\"\r\n                                class=\"input-error\">* T.I.N. / V.A.T. is required</div>\r\n                        </div>\r\n                        <div *ngIf=\"isInvoice\" class=\"input-field font-sm\">\r\n                            <div class=\"input-element-title\">Tax Office</div>\r\n                            <input class=\"input-element\" formControlName=\"tax\" type=\"text\">\r\n                            <div *ngIf=\"(isSubmitted || errorControl.tax.touched) && errorControl.tax.errors?.required\"\r\n                                class=\"input-error\">* Tax Office is required</div>\r\n                        </div>\r\n                        <div class=\"input-field font-sm fwidth\">\r\n                            <div class=\"input-element-title \">* Country</div>\r\n                            <app-prime-input-dropdown [parentForm]=\"form\" [countries]=\"countries\" optionLabel=\"name\"\r\n                                filterBy=\"name\" [inputName]=\"'country'\" [withFlags]=\"true\" flagFilter=\"name\"\r\n                                [placeholder]=\"'Country of Residence'\" [selectedItemValue]=\"selectedResidenceCountry\"\r\n                                templateValue=\"name\">\r\n                            </app-prime-input-dropdown>\r\n                            <div *ngIf=\"(isSubmitted || errorControl.country.touched) && errorControl.country.errors?.required\"\r\n                                class=\"input-error\">* Country is required</div>\r\n                        </div>\r\n                        <div *ngIf=\"isReceipt\" style=\"width:100%\"></div>\r\n                        <div class=\"input-field font-sm\">\r\n                            <div class=\"input-element-title\">* Email</div>\r\n                            <input class=\"input-element\" formControlName=\"email\" type=\"text\">\r\n                            <div *ngIf=\"(isSubmitted || errorControl.email.touched) && errorControl.email.errors?.required\"\r\n                                class=\"input-error\">* Email is required</div>\r\n                        </div>\r\n                        <div class=\"input-field font-sm\">\r\n                            <div class=\"input-element-title\">* Phone</div>\r\n                            <span class=\" flex sm:flex\">\r\n                                <app-prime-input-dropdown optionLabel=\"code\" [parentForm]=\"form\"\r\n                                    [countries]=\"phoneCodes\" filterBy=\"code\" [filter]=\"true\" [inputName]=\"'phoneCode'\"\r\n                                    [placeholder]=\"'select'\" [withFlags]=\"true\" flagFilter=\"code\" templateValue=\"code\"\r\n                                    styleClass=\"dropdown-blue white-bg phone-code-input tiny rounded-less small-dropdown-items\"\r\n                                    (valueSelected)=\"onPhoneCodeChange($event)\" [selectedItemValue]=\"selectedPhoneCode\">\r\n                                </app-prime-input-dropdown>\r\n                                <span class=\"p-float-label w-full\">\r\n                                    <input type=\"text\"\r\n                                        class=\"input-blue input-element rounded-less white-bg no-radius-left w-full\"\r\n                                        formControlName=\"phone\">\r\n                                </span>\r\n                            </span>\r\n                            <div *ngIf=\"(isSubmitted || errorControl.phone.touched) && errorControl.phone.errors?.required\"\r\n                                class=\"input-error\">* Phone is required</div>\r\n                        </div>\r\n                        <div class=\"input-field font-sm\">\r\n                            <div class=\"input-element-title\">* Street Address</div>\r\n                            <input class=\"input-element\" formControlName=\"street\" type=\"text\">\r\n                            <div *ngIf=\"(isSubmitted || errorControl.street.touched) && errorControl.street.errors?.required\"\r\n                                class=\"input-error\">* Street Adrdess is required</div>\r\n                        </div>\r\n                        <div class=\"input-field font-sm\">\r\n                            <div class=\"input-element-title\">* Street No</div>\r\n                            <input class=\"input-element\" formControlName=\"number\" type=\"text\">\r\n                            <div *ngIf=\"(isSubmitted || errorControl.number.touched) && errorControl.number.errors?.required\"\r\n                                class=\"input-error\">* Street Number is required</div>\r\n                        </div>\r\n                        <div class=\"input-field font-sm\">\r\n                            <div class=\"input-element-title\">* City</div>\r\n                            <input class=\"input-element\" formControlName=\"city\" type=\"text\">\r\n                            <div *ngIf=\"(isSubmitted || errorControl.city.touched) && errorControl.city.errors?.required\"\r\n                                class=\"input-error\">* City is required</div>\r\n                        </div>\r\n                        <div class=\"input-field font-sm\">\r\n                            <div class=\"input-element-title\">* Postcode</div>\r\n                            <input class=\"input-element\" formControlName=\"postcode\" type=\"text\">\r\n                            <div *ngIf=\"(isSubmitted || errorControl.postcode.touched) && errorControl.postcode.errors?.required\"\r\n                                class=\"input-error\">* Postcode is required</div>\r\n                        </div>\r\n\r\n                    </div>\r\n                </form>\r\n            </div>\r\n            <div *ngIf=\"currentStep==3\" class=\"left w-full\">\r\n                <div class=\"section-step-3\">\r\n                    <div class=\"section-step-3-title\">\r\n                        <span *ngIf=\"isInvoice\">Invoice</span><span *ngIf=\"isReceipt\">Receipt</span> Details\r\n                        <div class=\"toggle-section\"\r\n                            (click)=\"toggleSection(receiptDetailsSection, receiptDetailsArrowSrc)\">\r\n                            <img #receiptDetailsArrowSrc src=\"/assets/icons/toogle-section.svg\" class=\"section-arrow\">\r\n                        </div>\r\n                    </div>\r\n                    <div #receiptDetailsSection [attr.id]=\"'receipt-section'\" [attr.open]=\"true\" class=\"section-content\"\r\n                        style=\"overflow: inherit\">\r\n                        <div class=\"review\">\r\n                            <div class=\"review-section\">\r\n                                <div class=\"review-section-title font-sm font-semibold\">\r\n                                    Personal Info\r\n                                </div>\r\n                                <div class=\"review-section-content mt-1 font-sm\">\r\n                                    <div class=\"col-50\">\r\n                                        <div>\r\n                                            First Name\r\n                                        </div>\r\n                                        <div class=\"info\">\r\n                                            {{buyerUserDetails.fname}}\r\n                                        </div>\r\n                                    </div>\r\n                                    <div class=\"col-50\">\r\n                                        <div>\r\n                                            Last Name\r\n                                        </div>\r\n                                        <div class=\"info\">\r\n                                            {{buyerUserDetails.lname}}\r\n                                        </div>\r\n                                    </div>\r\n                                    <div class=\"col-50\">\r\n                                        <div>\r\n                                            Company Title\r\n                                        </div>\r\n                                        <div class=\"info\">\r\n                                            {{buyerUserDetails.company}}\r\n                                        </div>\r\n                                    </div>\r\n                                    <div class=\"col-50\">\r\n                                        <div>\r\n                                            Proffesion\r\n                                        </div>\r\n                                        <div class=\"info\">\r\n                                            {{buyerUserDetails.profession}}\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"review-section\">\r\n                                <div class=\"review-section-title font-sm font-semibold\">\r\n                                    Location\r\n                                </div>\r\n                                <div class=\"review-section-content mt-1 font-sm\">\r\n                                    <div class=\"col-25\">\r\n                                        <div>\r\n                                            Street address\r\n                                        </div>\r\n                                        <div class=\"info\">\r\n                                            {{buyerUserDetails.streetName}}\r\n                                        </div>\r\n                                    </div>\r\n                                    <div class=\"col-25\">\r\n                                        <div>\r\n                                            Street No\r\n                                        </div>\r\n                                        <div class=\"info\">\r\n                                            {{buyerUserDetails.streetNumber}}\r\n                                        </div>\r\n                                    </div>\r\n                                    <div class=\"col-25\">\r\n                                        <div>\r\n                                            Country\r\n                                        </div>\r\n                                        <div class=\"info\">\r\n                                            {{buyerUserDetails.country}}\r\n                                        </div>\r\n                                    </div>\r\n                                    <div class=\"col-25\">\r\n                                        <div>\r\n                                            City\r\n                                        </div>\r\n                                        <div class=\"info\">\r\n                                            {{buyerUserDetails.city}}\r\n                                        </div>\r\n                                    </div>\r\n                                    <div class=\"col-25\">\r\n                                        <div>\r\n                                            Postcode\r\n                                        </div>\r\n                                        <div class=\"info\">\r\n                                            {{buyerUserDetails.postcode}}\r\n                                        </div>\r\n                                    </div>\r\n                                    <div class=\"col-25\" *ngIf=\"isInvoice\">\r\n                                        <div>\r\n                                            Tax Office\r\n                                        </div>\r\n                                        <div class=\"info\">\r\n                                            {{buyerUserDetails.tax}}\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                            <div class=\"review-section\">\r\n                                <div class=\"review-section-title font-sm font-semibold\">\r\n                                    Contact Info\r\n                                </div>\r\n                                <div class=\"review-section-content mt-1 font-sm\">\r\n                                    <div class=\"col-50\">\r\n                                        <div>\r\n                                            E-mail\r\n                                        </div>\r\n                                        <div class=\"info\">\r\n                                            {{buyerUserDetails.email}}\r\n                                        </div>\r\n                                    </div>\r\n                                    <div class=\"col-25\">\r\n                                        <div>\r\n                                            Telephone\r\n                                        </div>\r\n                                        <div class=\"info\">\r\n                                            {{buyerUserDetails.phone}}\r\n                                        </div>\r\n                                    </div>\r\n                                    <div *ngIf=\"isInvoice\" class=\"col-50 sm:mt-1\">\r\n                                        <div>\r\n                                            *T.I.N. / V.A.T\r\n                                        </div>\r\n                                        <div class=\"info\">\r\n                                            {{buyerUserDetails.tin}}\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"font-base py-3\">Terms & Conditions</div>\r\n                <p class=\"font-sm\">Your personal data will be used to process your order, support your experience\r\n                    throughout this website, and for other purposes described in our privacy policy.</p>\r\n                <div class=\"section-step-3\">\r\n                    <div class=\"section-step-3-title\">\r\n                        Read Full Terms & Conditions\r\n                        <div class=\"toggle-section\" (click)=\"toggleSection(termsSection, termsArrowSrc)\">\r\n                            <img #termsArrowSrc src=\"/assets/icons/toogle-section.svg\" class=\"section-arrow\">\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"section-content fixed-height\" #termsSection [attr.id]=\"'receipt-section-details'\"\r\n                        [attr.open]=\"true\" style=\"overflow-y: scroll; height: 200px!important;\">\r\n                        <div class=\"review\">\r\n                            <div class=\"review-section\">\r\n                                <div class=\"review-section-content\">\r\n                                    <div class=\"col-100\">\r\n                                        <div class=\"info font-xs\">\r\n                                            <div class=\"font-semibold\">Terms and Conditions</div>\r\n                                            <br>\r\n                                            Welcome to MyLingoTrip, the Online Language School to learn languages\r\n                                            (Dutch, English, Italian, Greek, German, French, Swedish, Spanish, Korean,\r\n                                            Chinese, Japanese, Russian). This agreement (the “Agreement”) represents a\r\n                                            part of the contract between you and MyLingoTrip and, if applicable, the\r\n                                            fee-based subscription services therein.\r\n                                            <br>\r\n                                            By registering, purchasing a gift subscription from, or using MyLingoTrips’\r\n                                            services, you agree to accept the terms and conditions which govern access\r\n                                            and your use of the mylingotrip.com website. The terms and conditions\r\n                                            constitute a legal arrangement between you, as a user of our services and\r\n                                            MyLingoTrip for the services that are offered through the website. Users of\r\n                                            the MyLingoTrip website are expected to conform, without qualification to\r\n                                            the listed terms and conditions of use set out below in all circumstances.\r\n                                            <br>\r\n                                            Acceptance of the Terms\r\n                                            Changes to these Terms\r\n                                            General User Responsibilities (Use of Service)\r\n                                            Access and Interference\r\n                                            Student Registration\r\n                                            Students – Booking and Payment Policy\r\n                                            Students – Fees and Cancellation Policy\r\n                                            Indemnification\r\n                                            Language Teachers – Compliance with Booking Procedure\r\n                                            Language Teachers – Delivery of Language Training Services and Registration\r\n                                            Your rights\r\n                                            Privacy and Security Policy\r\n                                            Impart Among Users and Termination of Account\r\n                                            Applied Laws, Regulations, and Jurisdiction\r\n                                            Miscellaneous\r\n                                            1. Acceptance of the Terms\r\n                                            By using the information, features, functionality, and tools located at\r\n                                            MyLingoTrip website (with or without the MyLingoTrip “service” or\r\n                                            “services”), you agree to conform to this Arrangement and the Terms,\r\n                                            regardless of being either a “visitor” (meaning you merely browse the\r\n                                            MyLingoTrip’ website) or a “customer” (meaning you have registered with\r\n                                            MyLingoTrip). The term “you” or “user” refers to a visitor or a customer.\r\n                                            <br>\r\n                                            The term “we” refers to MyLingoTrip. The term “material” relates to all\r\n                                            class material used in our classes. If you wish to become a customer, you\r\n                                            could communicate your wish with other customers and/or make use of our\r\n                                            service/services. However, you have to read MyLingoTrip’s terms and\r\n                                            conditions and indicate your acceptance during the registration process.\r\n                                            <br>\r\n                                            If you do not agree with all the terms and conditions, do not use the\r\n                                            MyLingoTrip website and/or services.\r\n                                            <br>\r\n                                            Please review all the terms and conditions thoroughly before using the\r\n                                            MyLingoTrip website and or/services.\r\n                                            <br>\r\n                                            2. Changes to these Terms\r\n                                            MyLingoTrip has the right to modify this Agreement and any policies\r\n                                            affecting or relating to the Site. Your continued use of the Site following\r\n                                            notice of the Agreement modification will be considered an acceptance of the\r\n                                            modified terms and conditions of use, although, if major implications are\r\n                                            applied, you will be asked for providing your electronic signature. Your\r\n                                            alternative upon dissatisfaction with the modified terms of use is to cancel\r\n                                            your subscription. Moreover, Mylingotrip reserves the right to modify,\r\n                                            suspend or discontinue the Site at any time.\r\n                                            <br>\r\n                                            3. General User Responsibilities\r\n                                            All registered users agree not to post, distribute, publish or by any other\r\n                                            means convey offensive material, including discourteous language,\r\n                                            photographic material, or any other kind of material of an offensive or\r\n                                            sexual nature. Violation of this agreement conditions by any user will\r\n                                            result in termination of that users’ registration and will result in\r\n                                            notifying the authorities and possibly taking legal action.\r\n                                            <br>\r\n                                            All registered users agree not to post, distribute, publish, or by any other\r\n                                            means place any computer code on the MyLingoTrip website, which directly or\r\n                                            indirectly links to another Site without our express consent.\r\n                                            <br>\r\n                                            All registered users agree not to distribute or share their username and /or\r\n                                            password details with any other individual or company to provide to other\r\n                                            individuals the permission to utilize the services provided through the\r\n                                            MyLingoTrip website.\r\n                                            <br>\r\n                                            All registered users self-certify that they can form legally binding\r\n                                            contracts under their applicable regime of their country of residence.\r\n                                            <br>\r\n                                            4. Access and Interference\r\n                                            With your registration at MyLingoTrip website you agree that you will not:\r\n                                            <br>\r\n                                            *Use any robot, spider, scraper, deep link or other similar automated data\r\n                                            gathering or extraction tools, programs, algorithms or methods to get access\r\n                                            to, acquire, copy or monitor mylingotrip.com or any part of mylingotrip.com,\r\n                                            without MyLingoTrips’ express written consent, which will be withheld in\r\n                                            MyLingoTrips’ sole discretion;\r\n                                            <br>\r\n                                            *Use or attempt to use any engine, software, tool, agent, or other device or\r\n                                            mechanism (including without limitation browsers, spiders, robots, avatars\r\n                                            or intelligent agents) to navigate or search the MyLingoTrip website other\r\n                                            than the search engines and search agents available through the service and\r\n                                            other than generally available third-party web browsers (such as Microsoft\r\n                                            Explorer);\r\n                                            <br>\r\n                                            *Post or transmit any file containing viruses, worms, Trojan horses or any\r\n                                            other contaminating or malicious software/features, or whatever might\r\n                                            interfere with the proper functioning of mylingotrip.com and/or its\r\n                                            services;\r\n                                            <br>\r\n                                            *Attempt to decipher, decompile, disassemble, or reverse-engineer any of the\r\n                                            software incorporating or in any way constituting a part of mylingotrip.com\r\n                                            and/or its services.\r\n                                            <br>\r\n                                            5. Student Registration\r\n                                            When registering as a student at MyLingoTrip, you will be required to\r\n                                            provide personal information not limited to your preferred username and\r\n                                            password; your email address; the language of your choice; your country of\r\n                                            residence; your local currency and local time zone. By registering an\r\n                                            account at mylingotrip.com, you automatically confirm that you accept the\r\n                                            terms and conditions of use. All language students agree to the use of their\r\n                                            personal information following the web site privacy policy.\r\n                                            <br>\r\n                                            6. Students – Booking and Payment Policy\r\n                                            After providing to the website of MyLingoTrip the requested amount of fees,\r\n                                            the student should provide the remaining amount (the price of the total\r\n                                            study-package minus the websites’ fees) to his/her assigned teacher.\r\n                                            MyLingoTrip is obliged to issue the student’s receipt only for the fees that\r\n                                            are provided to the platform (mylingotrip.com). The remaining amount which\r\n                                            is provided by the student to his/her teacher is the teacher’s fee, and\r\n                                            there is no involvement of the MyLingoTrip platform in providing the receipt\r\n                                            that corresponds to this amount to the student.\r\n                                            <br>\r\n                                            Any action or attempt to avoid providing these fees according to the\r\n                                            MyLingoTrip booking policy or to arrange or attempt to arrange lessons\r\n                                            outside the MyLingoTrip platform is strictly prohibited and will result in\r\n                                            closing the student’s account, and legal action might be taken for the\r\n                                            recovery of the amount of money deemed owing. We reserve the right to amend\r\n                                            or alter these fees at any time. All prices displayed publicly on\r\n                                            mylingotrip.com are inclusive of all fees.\r\n                                            <br>\r\n                                            7. Students – Fees and Cancellation Policy\r\n                                            You will be required to register and create an account with MyLingoTrip (the\r\n                                            “Account”) to have access to the website’s services. Information gathered\r\n                                            through the registration process and information related to your account\r\n                                            will be subject to these terms as well as to our “Privacy and Security\r\n                                            Policy.” You warrant that the information provided for the creation of a\r\n                                            student’s account is truthful, accurate, and complete and that this\r\n                                            information will stay truthful, accurate, and complete as long as you are\r\n                                            registered at the MyLingoTrip website.\r\n                                            <br>\r\n                                            Certain material and functionalities of the website are available only after\r\n                                            the purchase of an online language package (the 3 “Package” options).\r\n                                            According to this Agreement, you are responsible for any charges associated\r\n                                            with registering to the website. MyLingoTrip reserves the right to change\r\n                                            the price of the actual package fees or institute new fees at any time, upon\r\n                                            reasonable notice posted on the website and emailed to current customers.\r\n                                            <br>\r\n                                            By purchasing a Study Package, you agree to be bound to the following terms:\r\n                                            <br>\r\n                                            MyLingoTrip ensures that every credit card/PayPal transaction you make is\r\n                                            100% secure. If you wish to check on the status of your subscription, please\r\n                                            log in and visit the “My Account” page in your Login area.\r\n                                            <br>\r\n                                            All language courses fees are recurring and non-refundable after your\r\n                                            purchase. If you do not wish to continue studying at MyLingoTrip, you have\r\n                                            to deactivate your account at the “My Account” page at your login area or\r\n                                            provide us with a written (email) notice.\r\n                                            <br>\r\n                                            An exception to the prior paragraph is our “7-day money-back guarantee”.\r\n                                            Within seven days after the purchase of a study package, you have the right\r\n                                            to cancel your language course and receive a full refund by providing us\r\n                                            with a written (email) notice.\r\n                                            <br>\r\n                                            MyLingoTrip is obliged to issue the student’s receipt only for the amount\r\n                                            provided to the platform (mylingotrip.com). The remaining amount, provided\r\n                                            by the student/parent to the teacher, is the Language Tutor’s fee, and there\r\n                                            is no involvement.\r\n                                            <br>\r\n                                            Lesson packages booked and not used within three months will be canceled.\r\n                                            Thus, if those remaining hours have not been used within three months after\r\n                                            the date a student/parent purchased a class package, those unused sessions\r\n                                            are lost and will not be refunded. Moreover, when a student chooses to\r\n                                            conduct only one (1) session weekly, then the activation period expands to\r\n                                            five (5) months. As a result, MyLingoTrip will not refund these particular\r\n                                            students, only after their expiration date is due.\r\n                                            <br>\r\n                                            In case of a cancellation: A scheduled lesson with a teacher has to be\r\n                                            canceled at least 48 hours before the actual appointment for the lesson.\r\n                                            Otherwise, the arranged hour is going to be deducted from the purchased\r\n                                            lesson-package, without any further announcement or refund.\r\n                                            <br>\r\n                                            For groups of 2 or 3: In case one of the students cancels the lesson but the\r\n                                            other one attends it, the hour will be charged as usual and no extra private\r\n                                            lesson will be provided for the student who missed the lesson.\r\n                                            <br>\r\n                                            Free Trial Lesson:\r\n                                            The free trial is available only to first-time users of the MyLingoTrip\r\n                                            website.\r\n                                            <br>\r\n                                            8. Indemnification\r\n                                            You agree to indemnify, protect and do not cause any misconduct to the\r\n                                            MyLingoTrip platform and it’s affiliates, partners, and employees due to any\r\n                                            accountability, alleges, costs, or damages arising from your use of the\r\n                                            MyLingoTrip website and/or your breach of this Agreement, under the terms\r\n                                            and conditions set out herein.\r\n                                            <br>\r\n                                            9. Language Teachers – Compliance with Booking Procedure\r\n                                            All registered language tutors agree not to engage in or promote any\r\n                                            activity to arrange lessons with a language student outside the MyLingoTrip\r\n                                            website. The activity of this nature is specifically prohibited under our\r\n                                            terms and conditions of service and undermines the trust, security, and\r\n                                            legitimacy of MyLingoTrip.\r\n                                            <br>\r\n                                            Activity specifically not permitted includes:\r\n                                            <br>\r\n                                            Communication via email, Skype, or other means to schedule and arrange a\r\n                                            lesson with a language student in a way that is intended to avoid the\r\n                                            payment of the booking fee to the MyLingoTrip website.\r\n                                            <br>\r\n                                            Failure of a registered language tutor to adhere to these terms and\r\n                                            conditions and in particular any action whatsoever taken by a registered\r\n                                            language teacher to privately arrange tuition time with a registered\r\n                                            MyLingoTrip language student, avoiding to provide the booking fee to the\r\n                                            MyLingoTrip website, may result in the termination of the cooperation\r\n                                            between the MyLingoTrip platform and the language teacher that committed\r\n                                            this action, as well as excluding the particular language student from\r\n                                            his/her rights to use the MyLingoTrip website again and/or legal action\r\n                                            against that language tutor and/or the language student and/or his/her\r\n                                            parent might be taken.\r\n                                            <br>\r\n                                            10. Language Teachers – Delivery of Language Training Services and\r\n                                            Registration\r\n                                            All registered MyLingoTrip language teachers agree to provide high-quality\r\n                                            language training services to any registered MyLingoTrip language student in\r\n                                            a timely fashion. We reserve the right to change the required standard at\r\n                                            any time. When registering as a language teacher you will be required to\r\n                                            provide personal information. You will also be requested to inform the\r\n                                            MyLingoTrip administrative team about your Skype username and password (All\r\n                                            users acknowledge that mylingotrip.com is not responsible for the quality of\r\n                                            the Skype interface or the quality of communications between users using\r\n                                            Skype).\r\n                                            <br>\r\n                                            11. Your Rights\r\n                                            MyLingoTrip offers you a non-transferable, non-exclusive, definite right to\r\n                                            access, use and array the content of this platform, provided that you comply\r\n                                            with this Agreement and the Terms as set out in full. The materials of this\r\n                                            Site are provided privately to you for your non-commercial use. Specific\r\n                                            services of this Site are available only to registered customers of\r\n                                            MyLingoTrip.\r\n                                            <br>\r\n                                            By purchasing a subscription or register for a Free Trial Lesson of\r\n                                            MyLingoTrip, you agree to be bound by the terms and conditions as set out in\r\n                                            full.\r\n                                            <br>\r\n                                            12. Privacy and Security Policy\r\n                                            Your use of our Site and any personal information or other information about\r\n                                            you collected by mylingotrip.com through, or in congruence with, the Site is\r\n                                            subject to our Privacy and Security Policy. For questions about our online\r\n                                            privacy and security policy please refer to our administration team.\r\n                                            <br>\r\n                                            13. Impart Among Users and Termination of Account\r\n                                            MyLingoTrip reserves the right to monitor communications between users. All\r\n                                            users consent to the supervision of communication, via any of the functions\r\n                                            we make available (i.e., Skype, Notes, G-mail) to protect the integrity and\r\n                                            security of mylingotrip.com, protect users from abuse, detecting fraud and\r\n                                            attempts to avoid our fee structure.\r\n                                            <br>\r\n                                            Language tutors may close their account if they notify MyLingoTrip’s\r\n                                            administration team at least three (3) months prior to the breaking of\r\n                                            engagement. They are subject to the completion of any overdue or unresolved\r\n                                            lessons, and only in case MyLingoTrip’s administration team and their\r\n                                            students are notified they can close their accounts. MyLingoTrip will take\r\n                                            further actions for transferring the students to another Language Tutor\r\n                                            matching their needs and availability slots.\r\n                                            <br>\r\n                                            Language students may close their account at any time but shall remain\r\n                                            accountable to MyLingoTrip and any language tutor for any unpaid lesson\r\n                                            fees.\r\n                                            <br>\r\n                                            14. Applied Laws, Regulations, and Jurisdiction\r\n                                            These terms and conditions shall be regulated in all respects by the laws of\r\n                                            Greece without affecting any principle that may require the application of\r\n                                            the law of another jurisdiction. The arrangements of this agreement are\r\n                                            severable and you agree that any dispute or claim you may have against\r\n                                            MyLingoTrip must be resolved by a court located in Greece. Hence, you\r\n                                            herewith submit to the personal jurisdiction of the courts located in Greece\r\n                                            for prosecuting or litigating such disputes or claims. Any cause of action\r\n                                            you may have in regards to MyLingoTrip must be commenced within one month\r\n                                            after it arises, or the cause of action is repudiated.\r\n                                            <br>\r\n                                            15. Miscellaneous\r\n                                            If any portion of this Agreement is deemed unlawful, void or unenforceable\r\n                                            by any arbitrator or court of competent jurisdiction, this Agreement as a\r\n                                            whole shall not be deemed unlawful, void or unenforceable, but only that\r\n                                            portion of this Agreement that is unlawful, void or unenforceable shall be\r\n                                            stricken from this Agreement. You agree that if MyLingoTrip does not\r\n                                            exercise or enforce any legal right or remedy which is contained in the\r\n                                            Agreement (or which MyLingoTrip has the benefit, according to any applicable\r\n                                            law), this will not be taken to be a formal waiver of MyLingoTrips’ rights\r\n                                            and that those rights or remedies will still be available to MyLingoTrip.\r\n                                            <br>\r\n                                            All covenants, agreements, representations, and warranties made in this\r\n                                            Agreement shall survive your acceptance of this Agreement and the\r\n                                            termination of this Agreement.\r\n                                            <br>\r\n                                            This Agreement represents the entire understanding and agreement between the\r\n                                            recipient and MyLingoTrip and supersedes and replaces all oral or written\r\n                                            agreements concerning the subject matter hereof.\r\n                                            <br>\r\n                                            In case you may have any questions about this policy statement, or about any\r\n                                            of MyLingoTrips’ other policies and practices, please contact us on the\r\n                                            contact details laid out at mylingotrip.com.\r\n                                            <br>\r\n                                            MyLingoTrip and related services are, if not otherwise explicitly mentioned,\r\n                                            supplied by:\r\n                                            <br>\r\n                                            MyLingoTrip (MLT)\r\n                                            <br>\r\n                                            Telephone Number: 231 5551054\r\n                                            <br>\r\n                                            Contact us: contact&#64;mylingotrip.com\r\n                                            <br>\r\n                                            IT Support: support&#64;mylingotrip.com\r\n                                            <br>\r\n                                            Address: 5 Papandreou Georgiou St.,\r\n                                            54645 Thessaloniki, Greece\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div class=\"review-section\" style=\"font-size:15px; margin: 20px 0; font-weight: bold;\">\r\n                    <p-checkbox label=\"I have read and agree to the website terms and conditions *\"\r\n                        class=\"terms-checkbox\" name=\"groupname\" value=\"val1\" [(ngModel)]=\"selectedValues\"\r\n                        (onChange)=\"agree()\"></p-checkbox>\r\n                </div>\r\n            </div>\r\n            <div *ngIf=\"currentStep==4\" class=\"left\">\r\n                <div class=\"end\">\r\n                    <img src=\"/assets/icons/package-check.svg\">\r\n                    <div id=\"pay-form\"></div>\r\n                    <div class=\"end-msg text-xl text-primary\">\r\n                        Thank you for your order!\r\n                    </div>\r\n                    <p class=\"text-center text-primary text-lg\">\r\n                        We have received your payment.\r\n                        Your lessons will be available on your profile shortly.\r\n                    </p>\r\n\r\n                    <div class=\"text-center\">\r\n                        <div class=\" text-primary lg:max-w-11rem\">\r\n\r\n                            <button pButton pRipple label=\"Back to dashboard\" [routerLink]=\"['/dashboard']\"\r\n                                class=\" white-space-nowrap p-button p-button-rounded p-button-outlined text-primary\">\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n            <div *ngIf=\"!inTrial\" class=\"right notranslate mb-6\" id=\"package-overview\">\r\n                <div class=\"title\">\r\n                    Overview\r\n                </div>\r\n                <div class=\"row row-first\">\r\n                    <div>\r\n                        Language\r\n                    </div>\r\n                    <div class=\"row-right\">\r\n                        {{selectedLanguage.name}}\r\n                    </div>\r\n                </div>\r\n                <div class=\"row\">\r\n                    <div>\r\n                        Level\r\n                    </div>\r\n                    <div class=\"row-right\">\r\n                        {{selectedLevel}}\r\n                    </div>\r\n                </div>\r\n                <div class=\"row\">\r\n                    <div>\r\n                        No. of Students\r\n                    </div>\r\n                    <div class=\"row-right\">\r\n                        {{selectedNumberOfStudents}}\r\n                    </div>\r\n                </div>\r\n                <div class=\"row\">\r\n                    <div>\r\n                        Hours\r\n                    </div>\r\n                    <div *ngIf=\"selectedPrice\" class=\"row-right\">\r\n                        {{mltPricesHourly[selectedHoursIndex].hours}}\r\n                    </div>\r\n                </div>\r\n                <div class=\"row\">\r\n                    <div>\r\n                        Package Type\r\n                    </div>\r\n                    <div class=\"row-right\">\r\n                        {{selectedPackageToBuy.type}}\r\n                    </div>\r\n                </div>\r\n                <div class=\"seperator\"></div>\r\n                <div class=\"row\">\r\n                    <div class=\"small-title\">\r\n                        General Info\r\n                    </div>\r\n                </div>\r\n                <div class=\"row\">\r\n                    <div>\r\n                        Expiration date is set {{expiresIn}} <span\r\n                            *ngIf=\"selectedPackageToBuy.type=='Regular'\">months</span> <span\r\n                            *ngIf=\"selectedPackageToBuy.type!=='Regular'\">(+{{selectedPackageToBuy.expiresPlus}})</span>\r\n                        starting from the first package lesson\r\n                    </div>\r\n                </div>\r\n                <div class=\"seperator\"></div>\r\n                <div class=\"row\">\r\n                    <div class=\"small-title\">\r\n                        {{selectedPackageToBuy.type}} Info\r\n                    </div>\r\n                </div>\r\n                <div class=\"row\" *ngIf=\"selectedPackageToBuy.type!=='Regular'\">\r\n                    <div>\r\n                        Expiration date extends by {{selectedPackageToBuy.expiresPlus}}\r\n                    </div>\r\n                </div>\r\n                <div class=\"row\">\r\n                    <div>\r\n                        {{selectedPackageToBuy.cancelation}} hours lesson cancellation policy\r\n                    </div>\r\n                </div>\r\n                <div class=\"row\" *ngIf=\"selectedPackageToBuy.type!='Regular'\">\r\n                    <div>\r\n                        AI Chat\r\n                    </div>\r\n                </div>\r\n                <div class=\"row\" *ngIf=\"selectedPackageToBuy.type!=='Regular' && selectedPackageToBuy.pause\">\r\n                    <div>\r\n                        {{selectedPackageToBuy.pause}} pause availability\r\n                    </div>\r\n                </div>\r\n                <div class=\"seperator\"></div>\r\n                <div class=\"row\" *ngIf=\"selectedPackageToBuy.type!=='Regular'\">\r\n                    <div>\r\n                        Regular Price\r\n                    </div>\r\n                    <div *ngIf=\"selectedPrice\" class=\"row-right\">\r\n                        {{selectedPrice}}&euro;\r\n                    </div>\r\n                </div>\r\n                <div class=\"row\" *ngIf=\"selectedPackageToBuy.type!=='Regular'\">\r\n                    <div>\r\n                        Additional Price\r\n                    </div>\r\n                    <div class=\"row-right\">\r\n                        {{selectedPackageToBuy.costPlus}}&euro;\r\n                    </div>\r\n                </div>\r\n                <div class=\"row\">\r\n                    <div>\r\n                        Total Price\r\n                    </div>\r\n                    <div class=\"row-right\">\r\n                        {{selectedPrice + selectedPackageToBuy.costPlus}}&euro;\r\n                    </div>\r\n                </div>\r\n                <ng-container *ngIf=\"showSplitPayment\">\r\n                    <div class=\"seperator\"></div>\r\n                    <div class=\"row align-items-center\">\r\n                        <div class=\"flex flex-column\">\r\n                            Booking Fee\r\n                            <span class=\"flex align-items-center gap-1 split-text\">Pay now\r\n                                <img class=\"pointer\" (click)=\"onSplitPaymentPayNowTooltipSelected()\"\r\n                                    src=\"/assets/icons/tooltip-blue.svg\" alt=\"tooltip\" title=\"learn more\" /></span>\r\n                        </div>\r\n                        <div class=\"row-right\">\r\n                            {{getTotalSplitPaymentAmount()}}&euro;\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"seperator\"></div>\r\n                    <div class=\"row align-items-center\">\r\n                        <div class=\"flex flex-column\">\r\n                            Remaining Balance\r\n                            <span class=\"flex align-items-center gap-1 split-text\">Pay later\r\n                                <img class=\"pointer\" (click)=\"onSplitPaymentPayLaterTooltipSelected()\"\r\n                                    src=\"/assets/icons/tooltip-blue.svg\" alt=\"tooltip\" title=\"learn more\" /></span>\r\n                        </div>\r\n                        <div class=\"row-right\">\r\n                            {{getRemainingAmount()}}&euro;\r\n                        </div>\r\n                    </div>\r\n                </ng-container>\r\n                <div class=\"row mt-5 bottom-buttons\" [ngClass]=\"{'hidden': currentStep === 4}\">\r\n                    <button type=\"button\"\r\n                        class=\" align-self-center prev-button-outlined p-element py-1 p-button-outlined p-button-rounded button-outlined p-button-sm p-button p-component py-2 px-3\"\r\n                        [class.inactive]=\"currentStep==1\" (click)=\"previous()\"><span\r\n                            class=\"p-datepicker-prev-icon pi pi-chevron-left\"></span><span class=\"p-ink\"></span>\r\n                        BACK</button>\r\n\r\n                    <ng-container *ngIf=\"currentStep!==3\">\r\n                        <button type=\"button\"\r\n                            class=\"next-button-outlined p-element py-1 p-button-outlined p-button-rounded button-outlined p-button-sm p-button p-component py-2 px-4 font-base\"\r\n                            (click)=\"next()\">NEXT <span\r\n                                class=\"p-datepicker-next-icon pi pi-chevron-right\"></span></button>\r\n                    </ng-container>\r\n\r\n                    <div class=\"\" *ngIf=\"currentStep==3\">\r\n                        <ng-container *ngIf=\"currentStep==3 && showSplitPayment; else noSplitPayment\">\r\n                            <button type=\"button\"\r\n                                class=\"split-pay-button-outlined p-element py-1 p-button-outlined p-button-rounded button-outlined p-button-sm p-button p-component py-2 px-3 font-base\"\r\n                                (click)=\"purchase()\">\r\n                                <div class=\"flex align-items-center justify-content-center gap-1\">\r\n                                    <div class=\"flex justify-content-start align-items-start flex-column\"> <span\r\n                                            class=\"font-bold\">Pay Now</span> <span\r\n                                            class=\"font-sm\">{{getTotalSplitPaymentAmount()}}&euro;</span> </div> <span\r\n                                        class=\"p-datepicker-next-icon pi pi-chevron-right\"></span>\r\n                                </div>\r\n                            </button>\r\n\r\n                        </ng-container>\r\n                        <ng-template #noSplitPayment>\r\n                            <div *ngIf=\"currentStep==3\">\r\n                                <button type=\"button\"\r\n                                    class=\"buy-button p-element py-1 p-button-outlined p-button-rounded button-outlined p-button-sm p-button p-component py-2 px-4 font-base\"\r\n                                    (click)=\"purchase()\">Buy <img _ngcontent-rrb-c97=\"\" width=\"16\"\r\n                                        class=\"pi text-white font-sm ml-2\"\r\n                                        src=\"assets/icons/lessons/arranged-white.svg\"></button>\r\n                            </div>\r\n                        </ng-template>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n\r\n<!-- <app-confirm-dialog \r\nclosable=\"true\"\r\ndialogKey=\"key2\" showHeader=\"true\" headerClass=\"text-center flex align-items-center flex-column bg-white\" [rejectBtnIcon]=\"\"\r\nacceptBtnLabel=\"Yes\" acceptBtnImage=\"/assets/icons/tooltip-blue-header.svg\" dialogType=\"''\" headerText=\"<img src='your-image-source' alt='image-description'> Cool\" rejectBtnLabel=\"\"\r\nconfirmMessage=\"Once your booking fee payment is completed, a customer service representative will contact you shortly to provide further assistance. \r\nThey will also inform you of the remaining balance and guide you on how to complete the payment process.\"></app-confirm-dialog> -->"], "mappings": ";AAAA,SAASA,QAAQ,QAAkB,iBAAiB;AACpD,SAAoEC,MAAM,QAAmB,eAAe;AAC5G,SAASC,kBAAkB,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,gBAAgB;AAGjF,SAASC,SAAS,EAAEC,IAAI,QAAQ,gBAAgB;AAGhD,SAAuGC,WAAW,EAAEC,aAAa,QAAiD,mCAAmC;AAMrN,SAASC,WAAW,EAAEC,UAAU,QAAQ,+BAA+B;AACvE,SAASC,OAAO,QAAQ,SAAS;AACjC,OAAOC,eAAe,MAAM,mBAAmB;AAE/C,SAASC,EAAE,QAAQ,MAAM;AAGzB,SAASC,WAAW,QAAQ,8BAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICP1CC,EADJ,CAAAC,cAAA,cAAwC,cACV;IACtBD,EAAA,CAAAE,MAAA,GACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IAFEH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,WAAA,QACJ;;;;;;IAZJP,EADJ,CAAAC,cAAA,cAAiD,iBAEmF;IADxHD,EAAA,CAAAQ,UAAA,mBAAAC,2DAAA;MAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA;MAAA,MAAAL,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASP,MAAA,CAAAQ,MAAA,EAAQ;IAAA,EAAC;IAEtBd,EAAA,CAAAC,cAAA,cAA2C;IAAAD,EAAA,CAAAe,SAAA,eACsB;IAAAf,EAAA,CAAAC,cAAA,eAChC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IACzCF,EADyC,CAAAG,YAAA,EAAO,EAAM,EAC7C;IACTH,EAAA,CAAAC,cAAA,cAAqD;IACjDD,EAAA,CAAAE,MAAA,oBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAgB,UAAA,IAAAC,wCAAA,kBAAwC;IAK5CjB,EAAA,CAAAG,YAAA,EAAM;;;;IALIH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,MAAoB;;;;;;IAO1BP,EADJ,CAAAC,cAAA,UAAqB,cACkC;IAAzBD,EAAA,CAAAQ,UAAA,mBAAAW,wDAAA;MAAAnB,EAAA,CAAAU,aAAA,CAAAU,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASP,MAAA,CAAAe,YAAA,EAAc;IAAA,EAAC;IAC9CrB,EAAA,CAAAE,MAAA,0BACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;;;;;;IAgBUH,EAAA,CAAAC,cAAA,cAGyC;IAArCD,EAAA,CAAAQ,UAAA,mBAAAc,0EAAA;MAAA,MAAAC,YAAA,GAAAvB,EAAA,CAAAU,aAAA,CAAAc,GAAA,EAAAC,SAAA;MAAA,MAAAnB,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASP,MAAA,CAAAoB,eAAA,CAAAH,YAAA,CAA0B;IAAA,EAAC;IACpCvB,EAAA,CAAAC,cAAA,eAAmD;IAC/CD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAe,SAAA,SAAI;IACJf,EAAA,CAAAC,cAAA,cAAiF;IAC7ED,EAAA,CAAAE,MAAA,GACJ;;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;;;;;IAT4BH,EAAA,CAAA2B,sBAAA,uBAAAJ,YAAA,CAAAK,EAAA,KAAiC;IAA/D5B,EAAA,CAAAkB,UAAA,YAAAZ,MAAA,CAAAuB,WAAA,OAA6B;IAGzB7B,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAkB,YAAA,CAAAO,QAAA,MACJ;IAEuC9B,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAA2B,sBAAA,+BAAAJ,YAAA,CAAAK,EAAA,KAAyC;IAC5E5B,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAA+B,kBAAA,WAAAR,YAAA,CAAAS,OAAA,CAAAC,SAAA,OAAAjC,EAAA,CAAAkC,WAAA,OAAAX,YAAA,CAAAS,OAAA,CAAAG,QAAA,cACJ;;;;;;IAnBRnC,EADJ,CAAAC,cAAA,UAAsB,cACc;IAC5BD,EAAA,CAAAE,MAAA,yBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADJ,CAAAC,cAAA,cAA2B,cAEiB;IAAnBD,EAAA,CAAAQ,UAAA,mBAAA4B,oEAAA;MAAApC,EAAA,CAAAU,aAAA,CAAA2B,GAAA;MAAA,MAAA/B,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASP,MAAA,CAAAgC,MAAA,EAAQ;IAAA,EAAC;IACnCtC,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAC,cAAA,eACmB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAC7DF,EAD6D,CAAAG,YAAA,EAAO,EAAO,EACrE;IACNH,EAAA,CAAAgB,UAAA,IAAAuB,oDAAA,mBAGyC;IAUjDvC,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IAb6BH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAkB,UAAA,YAAAZ,MAAA,CAAAkC,cAAA,CAAiB;;;;;;IAmB5CxC,EAAA,CAAAC,cAAA,cAEuC;IAAnCD,EAAA,CAAAQ,UAAA,mBAAAiC,0EAAA;MAAA,MAAAC,WAAA,GAAA1C,EAAA,CAAAU,aAAA,CAAAiC,GAAA,EAAAlB,SAAA;MAAA,MAAAnB,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASP,MAAA,CAAAsC,cAAA,CAAAF,WAAA,CAAwB;IAAA,EAAC;IAClC1C,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAH6BH,EAAA,CAAA2B,sBAAA,wBAAAe,WAAA,CAAAG,IAAA,KAAmC;IAAlE7C,EAAA,CAAAkB,UAAA,YAAAZ,MAAA,CAAAuB,WAAA,QAA8B;IAE9B7B,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAqC,WAAA,CAAAG,IAAA,MACJ;;;;;IARJ7C,EADJ,CAAAC,cAAA,UAAmF,cAC/C;IAC5BD,EAAA,CAAAE,MAAA,wBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA2B;IACvBD,EAAA,CAAAgB,UAAA,IAAA8B,oDAAA,kBAEuC;IAI/C9C,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IAXkCH,EAAA,CAAA+C,WAAA,eAAAzC,MAAA,CAAA0C,OAAA,gBAA0C;IAA7EhD,EAAA,CAAAkB,UAAA,gBAAA+B,SAAA,CAAa;IAKgBjD,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAkB,UAAA,YAAAZ,MAAA,CAAA4C,YAAA,CAAe;;;;;;IAYzClD,EAAA,CAAAC,cAAA,cAEyF;IAD5DD,EAAA,CAAAQ,UAAA,mBAAA2C,0EAAA;MAAA,MAAAC,SAAA,GAAApD,EAAA,CAAAU,aAAA,CAAA2C,GAAA,EAAA5B,SAAA;MAAA,MAAAnB,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASP,MAAA,CAAAgD,WAAA,CAAAF,SAAA,CAAkB;IAAA,EAAC;IAGjDpD,EADJ,CAAAC,cAAA,cAAoD,cACd;IAA7BD,EAAA,CAAAQ,UAAA,mBAAA+C,0EAAA;MAAA,MAAAH,SAAA,GAAApD,EAAA,CAAAU,aAAA,CAAA2C,GAAA,EAAA5B,SAAA;MAAA,MAAAnB,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASP,MAAA,CAAAgD,WAAA,CAAAF,SAAA,CAAkB;IAAA,EAAC;IAACpD,EAAA,CAAAE,MAAA,GAAS;IAEnDF,EAFmD,CAAAG,YAAA,EAAM,EAC/C,EACJ;;;;;IALFH,EAAA,CAAA2B,sBAAA,qBAAAyB,SAAA,KAAwB;IAA8BpD,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAwD,eAAA,IAAAC,GAAA,EAAAnD,MAAA,CAAAoD,aAAA,CAAAN,SAAA,EAAA9C,MAAA,CAAAqD,aAAA,QAAArD,MAAA,CAAAoD,aAAA,CAAAN,SAAA,EAAA9C,MAAA,CAAAsD,oBAAA,UAAAtD,MAAA,CAAAuD,KAAA,EAAAT,SAAA,IAAA9C,MAAA,CAAAqD,aAAA,EAAAP,SAAA,eAAAA,SAAA,eAAAA,SAAA,aAAAA,SAAA,WAC8B;IAC/EpD,EAAA,CAAAI,SAAA,EAA8C;IAA9CJ,EAAA,CAAAkB,UAAA,eAAAkC,SAAA,2BAA8C;IACbpD,EAAA,CAAAI,SAAA,GAAS;IAATJ,EAAA,CAAA8D,iBAAA,CAAAV,SAAA,CAAS;;;;;IARvDpD,EADJ,CAAAC,cAAA,UAAwC,cACT;IACvBD,EAAA,CAAAE,MAAA,qBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA2B;IACvBD,EAAA,CAAAgB,UAAA,IAAA+C,oDAAA,mBAEyF;IAMjG/D,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IAbDH,EAAA,CAAAkB,UAAA,gBAAA+B,SAAA,CAAa;IAKajD,EAAA,CAAAI,SAAA,GAAY;IAAZJ,EAAA,CAAAkB,UAAA,YAAAZ,MAAA,CAAA0D,SAAA,CAAY;;;;;;IAWnChE,EAAA,CAAAC,cAAA,cAEmD;IADtBD,EAAA,CAAAQ,UAAA,mBAAAyD,0EAAA;MAAA,MAAAC,SAAA,GAAAlE,EAAA,CAAAU,aAAA,CAAAyD,IAAA,EAAA1C,SAAA;MAAA,MAAAnB,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASP,MAAA,CAAAgD,WAAA,CAAAY,SAAA,CAAkB;IAAA,EAAC;IAErDlE,EAAA,CAAAC,cAAA,UAAK;IACDD,EAAA,CAAAE,MAAA,GACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;;;;;IALFH,EAAA,CAAA2B,sBAAA,qBAAAuC,SAAA,KAAwB;IAA8BlE,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAwD,eAAA,IAAAC,GAAA,EAAAnD,MAAA,CAAAoD,aAAA,CAAAQ,SAAA,EAAA5D,MAAA,CAAAqD,aAAA,QAAArD,MAAA,CAAAoD,aAAA,CAAAQ,SAAA,EAAA5D,MAAA,CAAAqD,aAAA,OAAAO,SAAA,IAAA5D,MAAA,CAAAqD,aAAA,EAAAO,SAAA,gBAAAA,SAAA,cACR;IAE1ClE,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAA6D,SAAA,MACJ;;;;;IANRlE,EADJ,CAAAC,cAAA,UAA4C,cACb;IACvBD,EAAA,CAAAgB,UAAA,IAAAoD,oDAAA,kBAEmD;IAM3DpE,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IARyBH,EAAA,CAAAI,SAAA,GAAY;IAAZJ,EAAA,CAAAkB,UAAA,YAAAZ,MAAA,CAAA0D,SAAA,CAAY;;;;;;IAcnChE,EAAA,CAAAC,cAAA,cAC6F;IAA/BD,EAAA,CAAAQ,UAAA,mBAAA6D,0EAAA;MAAA,MAAAC,UAAA,GAAAtE,EAAA,CAAAU,aAAA,CAAA6D,IAAA,EAAA9C,SAAA;MAAA,MAAAnB,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASP,MAAA,CAAAkE,YAAA,CAAAF,UAAA,CAAoB;IAAA,EAAC;IACxFtE,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAF6BH,EAAA,CAAA2B,sBAAA,sBAAA2C,UAAA,KAA0B;IAAzDtE,EAAA,CAAAkB,UAAA,YAAAZ,MAAA,CAAAuB,WAAA,QAA8B;IAC9B7B,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAiE,UAAA,MACJ;;;;;IAPJtE,EADJ,CAAAC,cAAA,UAAwC,cACJ;IAC5BD,EAAA,CAAAE,MAAA,kCACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAwD;IACpDD,EAAA,CAAAgB,UAAA,IAAAyD,oDAAA,kBAC6F;IAIrGzE,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IAVDH,EAAA,CAAAkB,UAAA,gBAAA+B,SAAA,CAAa;IAKcjD,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAkB,UAAA,YAAAZ,MAAA,CAAAoE,gBAAA,CAAmB;;;;;;IAW3C1E,EAAA,CAAAC,cAAA,cAC8F;IAA/BD,EAAA,CAAAQ,UAAA,mBAAAmE,0EAAA;MAAA,MAAAC,OAAA,GAAA5E,EAAA,CAAAU,aAAA,CAAAmE,IAAA;MAAA,MAAAC,QAAA,GAAAF,OAAA,CAAAnD,SAAA;MAAA,MAAAsD,KAAA,GAAAH,OAAA,CAAAI,KAAA;MAAA,MAAA1E,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASP,MAAA,CAAA2E,WAAA,CAAAH,QAAA,EAAAC,KAAA,CAAoB;IAAA,EAAC;IACzF/E,EAAA,CAAAC,cAAA,cAAmE;IAC/DD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAiC;IAC7BD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAiE;IAC7DD,EAAA,CAAAE,MAAA,GACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;;;;;IAV4BH,EAAA,CAAA2B,sBAAA,oBAAAmD,QAAA,CAAAI,KAAA,KAA4B;IAA1DlF,EAAA,CAAAkB,UAAA,YAAAZ,MAAA,CAAAuB,WAAA,OAA6B;IACG7B,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAA2B,sBAAA,0BAAAmD,QAAA,CAAAI,KAAA,KAAkC;IAC9DlF,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAyE,QAAA,CAAAK,KAAA,YACJ;IAEInF,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAyE,QAAA,CAAAI,KAAA,YACJ;IACgClF,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAA2B,sBAAA,wBAAAmD,QAAA,CAAAI,KAAA,KAAgC;IAC5DlF,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAyE,QAAA,CAAAM,OAAA,mBACJ;;;;;IAdRpF,EADJ,CAAAC,cAAA,UAAsB,cACc;IAC5BD,EAAA,CAAAE,MAAA,qBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAqD;IACjDD,EAAA,CAAAgB,UAAA,IAAAqE,oDAAA,mBAC8F;IAYtGrF,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IAbwBH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAkB,UAAA,YAAAZ,MAAA,CAAAgF,eAAA,CAAoB;;;;;;IAe9CtF,EADJ,CAAAC,cAAA,cAAgE,cAEoC;IAA7BD,EAAA,CAAAQ,UAAA,mBAAA+E,oEAAA;MAAAvF,EAAA,CAAAU,aAAA,CAAA8E,IAAA;MAAA,MAAAlF,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASP,MAAA,CAAAmF,gBAAA,EAAkB;IAAA,EAAC;IAC3FzF,EAAA,CAAAE,MAAA,mCACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;;;IALDH,EAAA,CAAAkB,UAAA,gBAAA+B,SAAA,CAAa;;;;;IAgCMjD,EAAA,CAAAC,cAAA,WAAsC;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAtCH,EAAA,CAAAI,SAAA,EAA+B;IAA/BJ,EAAA,CAAAK,kBAAA,gBAAAC,MAAA,CAAAoF,SAAA,YAA+B;;;;;IACrE1F,EAAA,CAAAC,cAAA,WAAsC;IAAAD,EAAA,CAAAE,MAAA,GAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA1CH,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAK,kBAAA,iBAAAsF,WAAA,CAAAC,WAAA,KAAmC;;;;;IAOzE5F,EAAA,CAAA6F,uBAAA,GAAuE;IACnE7F,EAAA,CAAAe,SAAA,cAAyE;;;;;;IAGzEf,EAAA,CAAAe,SAAA,cACwB;;;;;IAIhCf,EAAA,CAAAC,cAAA,cACmC;IAC/BD,EAAA,CAAAe,SAAA,cAAyE;IACzEf,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAC3CF,EAD2C,CAAAG,YAAA,EAAO,EAC5C;;;;IADIH,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAAK,kBAAA,KAAAsF,WAAA,CAAAG,KAAA,qBAAiC;;;;;IAG/C9F,EAAA,CAAAC,cAAA,cACyF;IACrFD,EAAA,CAAAe,SAAA,cAAmE;IACnEf,EAAA,CAAAC,cAAA,eAAoC;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IACxDF,EADwD,CAAAG,YAAA,EAAO,EACzD;;;;;;IA7CNH,EAHR,CAAAC,cAAA,cACuD,cACjB,cAEsQ;IAChSD,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,cAEqC;IAAjCD,EAAA,CAAAQ,UAAA,mBAAAuF,0EAAA;MAAA,MAAAJ,WAAA,GAAA3F,EAAA,CAAAU,aAAA,CAAAsF,IAAA,EAAAvE,SAAA;MAAA,MAAAnB,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASP,MAAA,CAAA2F,aAAA,CAAAN,WAAA,CAAsB;IAAA,EAAC;IAExC3F,EADI,CAAAG,YAAA,EAAM,EACJ;IAGEH,EAFR,CAAAC,cAAA,cAAsD,UAC7C,aACO;IACJD,EAAA,CAAAE,MAAA,GACJ;IAERF,EAFQ,CAAAG,YAAA,EAAS,EACP,EACJ;IAEFH,EADJ,CAAAC,cAAA,cAAsC,eACE;IAChCD,EAAA,CAAAe,SAAA,eAAyE;IAEzEf,EADA,CAAAgB,UAAA,KAAAkF,4DAAA,kBAAsC,KAAAC,4DAAA,kBACA;IAC1CnG,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAyC;IACrCD,EAAA,CAAAe,SAAA,eAAyE;IACzEf,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAiD;IAC3DF,EAD2D,CAAAG,YAAA,EAAO,EAC5D;IACNH,EAAA,CAAAC,cAAA,eAAyC;IAIrCD,EAHA,CAAAgB,UAAA,KAAAoF,oEAAA,2BAAuE,KAAAC,mEAAA,gCAAArG,EAAA,CAAAsG,sBAAA,CAGrC;IAIlCtG,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,eAAO;IACjBF,EADiB,CAAAG,YAAA,EAAO,EAClB;IACNH,EAAA,CAAAgB,UAAA,KAAAuF,2DAAA,kBACmC;IAIvCvG,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAgB,UAAA,KAAAwF,2DAAA,kBACyF;IAKjGxG,EADI,CAAAG,YAAA,EAAM,EACJ;;;;;;;IAjDFH,EAAA,CAAA+C,WAAA,iBAAAzC,MAAA,CAAAmG,cAAA,CAAAd,WAAA,CAAAe,IAAA,EAAkD;IAG1C1G,EAAA,CAAAI,SAAA,GAA+R;IAA/RJ,EAAA,CAAA+C,WAAA,eAAA4D,KAAA,oFAAAA,KAAA,KAAArG,MAAA,CAAAsG,eAAA,CAAAC,MAAA,0JAA+R;IAC/R7G,EAAA,CAAAI,SAAA,EACA;IADAJ,EAAA,CAAAK,kBAAA,MAAAsF,WAAA,CAAAe,IAAA,MACA;IACI1G,EAAA,CAAAI,SAAA,EAAmE;IAAnEJ,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAA8G,eAAA,KAAAC,GAAA,EAAApB,WAAA,CAAAe,IAAA,IAAApG,MAAA,CAAA0G,oBAAA,CAAAN,IAAA,EAAmE;IAO/D1G,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAsF,WAAA,CAAAsB,QAAA,eAAAtB,WAAA,CAAAsB,QAAA,mBACJ;IAMOjH,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAkB,UAAA,SAAAyE,WAAA,CAAAe,IAAA,cAA6B;IAC7B1G,EAAA,CAAAI,SAAA,EAA6B;IAA7BJ,EAAA,CAAAkB,UAAA,SAAAyE,WAAA,CAAAe,IAAA,cAA6B;IAI9B1G,EAAA,CAAAI,SAAA,GAAiD;IAAjDJ,EAAA,CAAAK,kBAAA,KAAAsF,WAAA,CAAAuB,WAAA,+BAAiD;IAGxClH,EAAA,CAAAI,SAAA,GAA8B;IAAAJ,EAA9B,CAAAkB,UAAA,SAAAyE,WAAA,CAAAe,IAAA,cAA8B,aAAAS,uBAAA,CAAwB;IASnEnH,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAkB,UAAA,SAAAyE,WAAA,CAAAe,IAAA,iBAAAf,WAAA,CAAAG,KAAA,CAA8C;IAMlD9F,EAAA,CAAAI,SAAA,EAA+C;IAA/CJ,EAAA,CAAAkB,UAAA,SAAAyE,WAAA,CAAAe,IAAA,IAAApG,MAAA,CAAA0G,oBAAA,CAAAN,IAAA,CAA+C;;;;;IAhDjE1G,EADJ,CAAAC,cAAA,UAAsB,cACS;IACvBD,EAAA,CAAAE,MAAA,4BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAkD;IAC9CD,EAAA,CAAAgB,UAAA,IAAAoG,oDAAA,oBACuD;IAmD/DpH,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IApD2BH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAkB,UAAA,YAAAZ,MAAA,CAAAsG,eAAA,CAAoB;;;;;IAvGzD5G,EAAA,CAAAC,cAAA,cAAmF;IAkG/ED,EAjGA,CAAAgB,UAAA,IAAAqG,8CAAA,kBAAsB,IAAAC,8CAAA,kBAwB6D,IAAAC,8CAAA,iBAY3C,IAAAC,8CAAA,iBAcI,IAAAC,8CAAA,iBAWJ,IAAAC,8CAAA,iBAWlB,IAAAC,8CAAA,kBAmB0C,IAAAC,8CAAA,iBAM1C;IA0D1B5H,EAAA,CAAAG,YAAA,EAAM;;;;IA5JwCH,EAAA,CAAA+C,WAAA,UAAAzC,MAAA,CAAA0C,OAAA,eAAoC;IACxEhD,EAAA,CAAAI,SAAA,EAAc;IAAdJ,EAAA,CAAAkB,UAAA,UAAAZ,MAAA,CAAA0C,OAAA,CAAc;IAwBAhD,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAkB,UAAA,UAAAZ,MAAA,CAAAuH,WAAA,CAAkB;IAYlB7H,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAkB,UAAA,UAAAZ,MAAA,CAAAuH,WAAA,CAAkB;IAchC7H,EAAA,CAAAI,SAAA,EAAoC;IAApCJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAAwH,gBAAA,CAAAjF,IAAA,UAAoC;IAWtB7C,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAkB,UAAA,UAAAZ,MAAA,CAAAuH,WAAA,CAAkB;IAWhC7H,EAAA,CAAAI,SAAA,EAAc;IAAdJ,EAAA,CAAAkB,UAAA,UAAAZ,MAAA,CAAA0C,OAAA,CAAc;IAmBAhD,EAAA,CAAAI,SAAA,EAA6B;IAA7BJ,EAAA,CAAAkB,UAAA,UAAAZ,MAAA,CAAAuH,WAAA,IAAAvH,MAAA,CAAA0C,OAAA,CAA6B;IAM3ChD,EAAA,CAAAI,SAAA,EAAc;IAAdJ,EAAA,CAAAkB,UAAA,UAAAZ,MAAA,CAAA0C,OAAA,CAAc;;;;;IAsERhD,EAAA,CAAAE,MAAA,iFACA;;;;;IACAF,EAAA,CAAAE,MAAA,4EACA;;;;;IATZF,EAAA,CAAAC,cAAA,cACkH;IAC9GD,EAAA,CAAAe,SAAA,aAA4F;IAExFf,EADJ,CAAAC,cAAA,UAAK,eACsC;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAAAH,EAAA,CAAAC,cAAA,gBAA+B;IAGnFD,EAFF,CAAAgB,UAAA,IAAA+G,oEAAA,OAA8C,IAAAC,oEAAA,OAErC;IAKrBhI,EAFQ,CAAAG,YAAA,EAAO,EACL,EACJ;;;;IAPMH,EAAA,CAAAI,SAAA,GAIC;IAJDJ,EAAA,CAAAiI,aAAA,IAAA3H,MAAA,CAAA0G,oBAAA,CAAAN,IAAA,wBAIC;;;;;;IAQb1G,EAAA,CAAA6F,uBAAA,GAAc;IACV7F,EAAA,CAAAC,cAAA,0CACwE;IAApED,EAAA,CAAAQ,UAAA,6CAAA0H,iIAAAC,MAAA;MAAAnI,EAAA,CAAAU,aAAA,CAAA0H,IAAA;MAAA,MAAA9H,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAmCP,MAAA,CAAA+H,uBAAA,CAAAF,MAAA,CAA+B;IAAA,EAAC;IAACnI,EAAA,CAAAG,YAAA,EAAiC;;;;;IADzEH,EAAA,CAAAI,SAAA,EAA2C;IAA3CJ,EAAA,CAAAkB,UAAA,uBAAAZ,MAAA,CAAA0G,oBAAA,CAA2C;;;;;IA+BnEhH,EAAA,CAAAC,cAAA,eACwB;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAKtDH,EAAA,CAAAC,cAAA,eACwB;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAarDH,EAAA,CAAAC,cAAA,eACwB;IAAAD,EAAA,CAAAE,MAAA,oCAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAH3DH,EADJ,CAAAC,cAAA,cAAmD,cACd;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACxDH,EAAA,CAAAe,SAAA,iBAA+D;IAC/Df,EAAA,CAAAgB,UAAA,IAAAsH,qDAAA,kBACwB;IAC5BtI,EAAA,CAAAG,YAAA,EAAM;;;;IAFIH,EAAA,CAAAI,SAAA,GAAoF;IAApFJ,EAAA,CAAAkB,UAAA,UAAAZ,MAAA,CAAAiI,WAAA,IAAAjI,MAAA,CAAAkI,YAAA,CAAAC,GAAA,CAAAC,OAAA,MAAApI,MAAA,CAAAkI,YAAA,CAAAC,GAAA,CAAAE,MAAA,kBAAArI,MAAA,CAAAkI,YAAA,CAAAC,GAAA,CAAAE,MAAA,CAAAC,QAAA,EAAoF;;;;;IAM1F5I,EAAA,CAAAC,cAAA,eACwB;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHtDH,EADJ,CAAAC,cAAA,cAAmD,cACd;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjDH,EAAA,CAAAe,SAAA,iBAA+D;IAC/Df,EAAA,CAAAgB,UAAA,IAAA6H,qDAAA,kBACwB;IAC5B7I,EAAA,CAAAG,YAAA,EAAM;;;;IAFIH,EAAA,CAAAI,SAAA,GAAoF;IAApFJ,EAAA,CAAAkB,UAAA,UAAAZ,MAAA,CAAAiI,WAAA,IAAAjI,MAAA,CAAAkI,YAAA,CAAAM,GAAA,CAAAJ,OAAA,MAAApI,MAAA,CAAAkI,YAAA,CAAAM,GAAA,CAAAH,MAAA,kBAAArI,MAAA,CAAAkI,YAAA,CAAAM,GAAA,CAAAH,MAAA,CAAAC,QAAA,EAAoF;;;;;IAU1F5I,EAAA,CAAAC,cAAA,eACwB;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAEvDH,EAAA,CAAAe,SAAA,eAAgD;;;;;IAI5Cf,EAAA,CAAAC,cAAA,eACwB;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAiBjDH,EAAA,CAAAC,cAAA,eACwB;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAKjDH,EAAA,CAAAC,cAAA,eACwB;IAAAD,EAAA,CAAAE,MAAA,mCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAK1DH,EAAA,CAAAC,cAAA,eACwB;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAKzDH,EAAA,CAAAC,cAAA,eACwB;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAKhDH,EAAA,CAAAC,cAAA,eACwB;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IA5IpEH,EAAA,CAAAC,cAAA,cAAyC;IAqBrCD,EAjBA,CAAAgB,UAAA,IAAA+H,sDAAA,kBAA4B,IAAAC,sDAAA,uBAiB6C;IAUjEhJ,EAFR,CAAAC,cAAA,eAAyB,cACK,cACsB;IACxCD,EAAA,CAAAE,MAAA,4BACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAIMH,EAHZ,CAAAC,cAAA,cAAyB,cACa,cACO,yBAGS;IAFID,EAAA,CAAAiJ,gBAAA,2BAAAC,iFAAAf,MAAA;MAAAnI,EAAA,CAAAU,aAAA,CAAAyI,IAAA;MAAA,MAAA7I,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAAZ,EAAA,CAAAoJ,kBAAA,CAAA9I,MAAA,CAAA+I,mBAAA,EAAAlB,MAAA,MAAA7H,MAAA,CAAA+I,mBAAA,GAAAlB,MAAA;MAAA,OAAAnI,EAAA,CAAAa,WAAA,CAAAsH,MAAA;IAAA,EAAiC;IACzDnI,EAAA,CAAAQ,UAAA,mBAAA8I,yEAAA;MAAAtJ,EAAA,CAAAU,aAAA,CAAAyI,IAAA;MAAA,MAAA7I,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASP,MAAA,CAAAiJ,aAAA,EAAe;IAAA,EAAC;IACLvJ,EAAA,CAAAG,YAAA,EAAgB;IAC1DH,EAAA,CAAAC,cAAA,iBAA0C;IAAAD,EAAA,CAAAE,MAAA,eAAO;IACrDF,EADqD,CAAAG,YAAA,EAAQ,EACvD;IAGFH,EADJ,CAAAC,cAAA,eAAqC,yBAGS;IAFID,EAAA,CAAAiJ,gBAAA,2BAAAO,iFAAArB,MAAA;MAAAnI,EAAA,CAAAU,aAAA,CAAAyI,IAAA;MAAA,MAAA7I,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAAZ,EAAA,CAAAoJ,kBAAA,CAAA9I,MAAA,CAAA+I,mBAAA,EAAAlB,MAAA,MAAA7H,MAAA,CAAA+I,mBAAA,GAAAlB,MAAA;MAAA,OAAAnI,EAAA,CAAAa,WAAA,CAAAsH,MAAA;IAAA,EAAiC;IACzDnI,EAAA,CAAAQ,UAAA,mBAAAiJ,yEAAA;MAAAzJ,EAAA,CAAAU,aAAA,CAAAyI,IAAA;MAAA,MAAA7I,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASP,MAAA,CAAAoJ,aAAA,EAAe;IAAA,EAAC;IACL1J,EAAA,CAAAG,YAAA,EAAgB;IAC1DH,EAAA,CAAAC,cAAA,iBAA0C;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAG7DF,EAH6D,CAAAG,YAAA,EAAQ,EACvD,EACJ,EACJ;IAEFH,EADJ,CAAAC,cAAA,eAAiC,eACI;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACnDH,EAAA,CAAAe,SAAA,iBAAiE;IACjEf,EAAA,CAAAgB,UAAA,KAAA2I,+CAAA,kBACwB;IAC5B3J,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADJ,CAAAC,cAAA,eAAiC,eACI;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAClDH,EAAA,CAAAe,SAAA,iBAAiE;IACjEf,EAAA,CAAAgB,UAAA,KAAA4I,+CAAA,kBACwB;IAC5B5J,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADJ,CAAAC,cAAA,eAAiC,eACI;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACzDH,EAAA,CAAAe,SAAA,iBAAmE;IACvEf,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADJ,CAAAC,cAAA,eAAiC,eACI;IAAAD,EAAA,CAAAE,MAAA,6BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5DH,EAAA,CAAAe,SAAA,iBAAsE;IAC1Ef,EAAA,CAAAG,YAAA,EAAM;IAONH,EANA,CAAAgB,UAAA,KAAA6I,+CAAA,kBAAmD,KAAAC,+CAAA,kBAMA;IAO/C9J,EADJ,CAAAC,cAAA,eAAwC,eACF;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjDH,EAAA,CAAAe,SAAA,oCAI2B;IAC3Bf,EAAA,CAAAgB,UAAA,KAAA+I,+CAAA,kBACwB;IAC5B/J,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAgB,UAAA,KAAAgJ,+CAAA,kBAA0C;IAEtChK,EADJ,CAAAC,cAAA,eAAiC,eACI;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC9CH,EAAA,CAAAe,SAAA,iBAAiE;IACjEf,EAAA,CAAAgB,UAAA,KAAAiJ,+CAAA,kBACwB;IAC5BjK,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADJ,CAAAC,cAAA,eAAiC,eACI;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAE1CH,EADJ,CAAAC,cAAA,gBAA4B,oCAKgE;IAApFD,EAAA,CAAAQ,UAAA,2BAAA0J,4FAAA/B,MAAA;MAAAnI,EAAA,CAAAU,aAAA,CAAAyI,IAAA;MAAA,MAAA7I,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAiBP,MAAA,CAAA6J,iBAAA,CAAAhC,MAAA,CAAyB;IAAA,EAAC;IAC/CnI,EAAA,CAAAG,YAAA,EAA2B;IAC3BH,EAAA,CAAAC,cAAA,iBAAmC;IAC/BD,EAAA,CAAAe,SAAA,kBAE4B;IAEpCf,EADI,CAAAG,YAAA,EAAO,EACJ;IACPH,EAAA,CAAAgB,UAAA,KAAAoJ,+CAAA,kBACwB;IAC5BpK,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADJ,CAAAC,cAAA,eAAiC,eACI;IAAAD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvDH,EAAA,CAAAe,SAAA,kBAAkE;IAClEf,EAAA,CAAAgB,UAAA,KAAAqJ,+CAAA,kBACwB;IAC5BrK,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADJ,CAAAC,cAAA,eAAiC,eACI;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAClDH,EAAA,CAAAe,SAAA,kBAAkE;IAClEf,EAAA,CAAAgB,UAAA,KAAAsJ,+CAAA,kBACwB;IAC5BtK,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADJ,CAAAC,cAAA,eAAiC,eACI;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC7CH,EAAA,CAAAe,SAAA,kBAAgE;IAChEf,EAAA,CAAAgB,UAAA,KAAAuJ,+CAAA,kBACwB;IAC5BvK,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADJ,CAAAC,cAAA,eAAiC,eACI;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjDH,EAAA,CAAAe,SAAA,kBAAoE;IACpEf,EAAA,CAAAgB,UAAA,KAAAwJ,+CAAA,kBACwB;IAKxCxK,EAJY,CAAAG,YAAA,EAAM,EAEJ,EACH,EACL;;;;IA7IFH,EAAA,CAAAI,SAAA,EAcC;IAdDJ,EAAA,CAAAiI,aAAA,IAAA3H,MAAA,CAAAmK,kBAAA,YAcC;IAGDzK,EAAA,CAAAI,SAAA,EAKC;IALDJ,EAAA,CAAAiI,aAAA,KAAA3H,MAAA,CAAA0G,oBAAA,kBAAA1G,MAAA,CAAA0G,oBAAA,CAAAN,IAAA,oBAAApG,MAAA,CAAAmK,kBAAA,YAKC;IAGKzK,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAkB,UAAA,cAAAZ,MAAA,CAAAoK,IAAA,CAAkB;IAQ0C1K,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAA2K,gBAAA,YAAArK,MAAA,CAAA+I,mBAAA,CAAiC;IAE3ErJ,EAAA,CAAAkB,UAAA,mBAAAlB,EAAA,CAAA4K,eAAA,KAAAC,GAAA,EAAqC;IAKK7K,EAAA,CAAAI,SAAA,GAAiC;IAAjCJ,EAAA,CAAA2K,gBAAA,YAAArK,MAAA,CAAA+I,mBAAA,CAAiC;IAE3ErJ,EAAA,CAAAkB,UAAA,mBAAAlB,EAAA,CAAA4K,eAAA,KAAAC,GAAA,EAAqC;IAQ3C7K,EAAA,CAAAI,SAAA,GAAwF;IAAxFJ,EAAA,CAAAkB,UAAA,UAAAZ,MAAA,CAAAiI,WAAA,IAAAjI,MAAA,CAAAkI,YAAA,CAAAsC,KAAA,CAAApC,OAAA,MAAApI,MAAA,CAAAkI,YAAA,CAAAsC,KAAA,CAAAnC,MAAA,kBAAArI,MAAA,CAAAkI,YAAA,CAAAsC,KAAA,CAAAnC,MAAA,CAAAC,QAAA,EAAwF;IAMxF5I,EAAA,CAAAI,SAAA,GAAwF;IAAxFJ,EAAA,CAAAkB,UAAA,UAAAZ,MAAA,CAAAiI,WAAA,IAAAjI,MAAA,CAAAkI,YAAA,CAAAuC,KAAA,CAAArC,OAAA,MAAApI,MAAA,CAAAkI,YAAA,CAAAuC,KAAA,CAAApC,MAAA,kBAAArI,MAAA,CAAAkI,YAAA,CAAAuC,KAAA,CAAApC,MAAA,CAAAC,QAAA,EAAwF;IAW5F5I,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAA0K,SAAA,CAAe;IAMfhL,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAA0K,SAAA,CAAe;IAQShL,EAAA,CAAAI,SAAA,GAAmB;IAEFJ,EAFjB,CAAAkB,UAAA,eAAAZ,MAAA,CAAAoK,IAAA,CAAmB,cAAApK,MAAA,CAAA2K,SAAA,CAAwB,wBAC1B,mBAAmB,uCACpB,sBAAA3K,MAAA,CAAA4K,wBAAA,CAA+C;IAGnFlL,EAAA,CAAAI,SAAA,EAA4F;IAA5FJ,EAAA,CAAAkB,UAAA,UAAAZ,MAAA,CAAAiI,WAAA,IAAAjI,MAAA,CAAAkI,YAAA,CAAA2C,OAAA,CAAAzC,OAAA,MAAApI,MAAA,CAAAkI,YAAA,CAAA2C,OAAA,CAAAxC,MAAA,kBAAArI,MAAA,CAAAkI,YAAA,CAAA2C,OAAA,CAAAxC,MAAA,CAAAC,QAAA,EAA4F;IAGhG5I,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAA8K,SAAA,CAAe;IAIXpL,EAAA,CAAAI,SAAA,GAAwF;IAAxFJ,EAAA,CAAAkB,UAAA,UAAAZ,MAAA,CAAAiI,WAAA,IAAAjI,MAAA,CAAAkI,YAAA,CAAA6C,KAAA,CAAA3C,OAAA,MAAApI,MAAA,CAAAkI,YAAA,CAAA6C,KAAA,CAAA1C,MAAA,kBAAArI,MAAA,CAAAkI,YAAA,CAAA6C,KAAA,CAAA1C,MAAA,CAAAC,QAAA,EAAwF;IAM7C5I,EAAA,CAAAI,SAAA,GAAmB;IAIhBJ,EAJH,CAAAkB,UAAA,eAAAZ,MAAA,CAAAoK,IAAA,CAAmB,cAAApK,MAAA,CAAAgL,UAAA,CACpC,gBAAgC,0BAA0B,yBAC1D,mBAAmB,sBAAAhL,MAAA,CAAAiL,iBAAA,CAEwC;IAQrFvL,EAAA,CAAAI,SAAA,GAAwF;IAAxFJ,EAAA,CAAAkB,UAAA,UAAAZ,MAAA,CAAAiI,WAAA,IAAAjI,MAAA,CAAAkI,YAAA,CAAAgD,KAAA,CAAA9C,OAAA,MAAApI,MAAA,CAAAkI,YAAA,CAAAgD,KAAA,CAAA7C,MAAA,kBAAArI,MAAA,CAAAkI,YAAA,CAAAgD,KAAA,CAAA7C,MAAA,CAAAC,QAAA,EAAwF;IAMxF5I,EAAA,CAAAI,SAAA,GAA0F;IAA1FJ,EAAA,CAAAkB,UAAA,UAAAZ,MAAA,CAAAiI,WAAA,IAAAjI,MAAA,CAAAkI,YAAA,CAAAiD,MAAA,CAAA/C,OAAA,MAAApI,MAAA,CAAAkI,YAAA,CAAAiD,MAAA,CAAA9C,MAAA,kBAAArI,MAAA,CAAAkI,YAAA,CAAAiD,MAAA,CAAA9C,MAAA,CAAAC,QAAA,EAA0F;IAM1F5I,EAAA,CAAAI,SAAA,GAA0F;IAA1FJ,EAAA,CAAAkB,UAAA,UAAAZ,MAAA,CAAAiI,WAAA,IAAAjI,MAAA,CAAAkI,YAAA,CAAAkD,MAAA,CAAAhD,OAAA,MAAApI,MAAA,CAAAkI,YAAA,CAAAkD,MAAA,CAAA/C,MAAA,kBAAArI,MAAA,CAAAkI,YAAA,CAAAkD,MAAA,CAAA/C,MAAA,CAAAC,QAAA,EAA0F;IAM1F5I,EAAA,CAAAI,SAAA,GAAsF;IAAtFJ,EAAA,CAAAkB,UAAA,UAAAZ,MAAA,CAAAiI,WAAA,IAAAjI,MAAA,CAAAkI,YAAA,CAAAmD,IAAA,CAAAjD,OAAA,MAAApI,MAAA,CAAAkI,YAAA,CAAAmD,IAAA,CAAAhD,MAAA,kBAAArI,MAAA,CAAAkI,YAAA,CAAAmD,IAAA,CAAAhD,MAAA,CAAAC,QAAA,EAAsF;IAMtF5I,EAAA,CAAAI,SAAA,GAA8F;IAA9FJ,EAAA,CAAAkB,UAAA,UAAAZ,MAAA,CAAAiI,WAAA,IAAAjI,MAAA,CAAAkI,YAAA,CAAAoD,QAAA,CAAAlD,OAAA,MAAApI,MAAA,CAAAkI,YAAA,CAAAoD,QAAA,CAAAjD,MAAA,kBAAArI,MAAA,CAAAkI,YAAA,CAAAoD,QAAA,CAAAjD,MAAA,CAAAC,QAAA,EAA8F;;;;;IAUxG5I,EAAA,CAAAC,cAAA,WAAwB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAAAH,EAAA,CAAAC,cAAA,WAAwB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IA8F5DH,EADJ,CAAAC,cAAA,eAAsC,UAC7B;IACDD,EAAA,CAAAE,MAAA,mBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAkB;IACdD,EAAA,CAAAE,MAAA,GACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IAFEH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAuL,gBAAA,CAAA/C,GAAA,MACJ;;;;;IA0BA9I,EADJ,CAAAC,cAAA,eAA8C,UACrC;IACDD,EAAA,CAAAE,MAAA,wBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAkB;IACdD,EAAA,CAAAE,MAAA,GACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IAFEH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAuL,gBAAA,CAAApD,GAAA,MACJ;;;;;;IAnIpBzI,EAFR,CAAAC,cAAA,eAAgD,eAChB,eACU;IACQD,EAAtC,CAAAgB,UAAA,IAAA8K,+CAAA,kBAAwB,IAAAC,+CAAA,kBAAsC;IAAe/L,EAAA,CAAAE,MAAA,gBAC7E;IAAAF,EAAA,CAAAC,cAAA,eAC2E;IAAvED,EAAA,CAAAQ,UAAA,mBAAAwL,8DAAA;MAAAhM,EAAA,CAAAU,aAAA,CAAAuL,IAAA;MAAA,MAAAC,0BAAA,GAAAlM,EAAA,CAAAmM,WAAA;MAAA,MAAAC,yBAAA,GAAApM,EAAA,CAAAmM,WAAA;MAAA,MAAA7L,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASP,MAAA,CAAA+L,aAAA,CAAAD,yBAAA,EAAAF,0BAAA,CAA4D;IAAA,EAAC;IACtElM,EAAA,CAAAe,SAAA,kBAA0F;IAElGf,EADI,CAAAG,YAAA,EAAM,EACJ;IAKMH,EAJZ,CAAAC,cAAA,kBAC8B,gBACN,gBACY,gBACgC;IACpDD,EAAA,CAAAE,MAAA,uBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGEH,EAFR,CAAAC,cAAA,gBAAiD,gBACzB,WACX;IACDD,EAAA,CAAAE,MAAA,oBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAkB;IACdD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IAEFH,EADJ,CAAAC,cAAA,gBAAoB,WACX;IACDD,EAAA,CAAAE,MAAA,mBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAkB;IACdD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IAEFH,EADJ,CAAAC,cAAA,gBAAoB,WACX;IACDD,EAAA,CAAAE,MAAA,uBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAkB;IACdD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IAEFH,EADJ,CAAAC,cAAA,gBAAoB,WACX;IACDD,EAAA,CAAAE,MAAA,oBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAkB;IACdD,EAAA,CAAAE,MAAA,IACJ;IAGZF,EAHY,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ;IAEFH,EADJ,CAAAC,cAAA,gBAA4B,gBACgC;IACpDD,EAAA,CAAAE,MAAA,kBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGEH,EAFR,CAAAC,cAAA,gBAAiD,gBACzB,WACX;IACDD,EAAA,CAAAE,MAAA,wBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAkB;IACdD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IAEFH,EADJ,CAAAC,cAAA,gBAAoB,WACX;IACDD,EAAA,CAAAE,MAAA,mBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAkB;IACdD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IAEFH,EADJ,CAAAC,cAAA,gBAAoB,WACX;IACDD,EAAA,CAAAE,MAAA,iBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAkB;IACdD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IAEFH,EADJ,CAAAC,cAAA,gBAAoB,WACX;IACDD,EAAA,CAAAE,MAAA,cACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAkB;IACdD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IAEFH,EADJ,CAAAC,cAAA,gBAAoB,WACX;IACDD,EAAA,CAAAE,MAAA,kBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAkB;IACdD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IACNH,EAAA,CAAAgB,UAAA,KAAAsL,+CAAA,mBAAsC;IAS9CtM,EADI,CAAAG,YAAA,EAAM,EACJ;IAEFH,EADJ,CAAAC,cAAA,gBAA4B,gBACgC;IACpDD,EAAA,CAAAE,MAAA,sBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGEH,EAFR,CAAAC,cAAA,gBAAiD,gBACzB,WACX;IACDD,EAAA,CAAAE,MAAA,gBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAkB;IACdD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IAEFH,EADJ,CAAAC,cAAA,gBAAoB,WACX;IACDD,EAAA,CAAAE,MAAA,mBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAkB;IACdD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IACNH,EAAA,CAAAgB,UAAA,KAAAuL,+CAAA,mBAA8C;IAYlEvM,EAJgB,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ,EACJ;IACNH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpDH,EAAA,CAAAC,cAAA,cAAmB;IAAAD,EAAA,CAAAE,MAAA,uKACiE;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEpFH,EADJ,CAAAC,cAAA,gBAA4B,gBACU;IAC9BD,EAAA,CAAAE,MAAA,sCACA;IAAAF,EAAA,CAAAC,cAAA,gBAAiF;IAArDD,EAAA,CAAAQ,UAAA,mBAAAgM,+DAAA;MAAAxM,EAAA,CAAAU,aAAA,CAAAuL,IAAA;MAAA,MAAAQ,iBAAA,GAAAzM,EAAA,CAAAmM,WAAA;MAAA,MAAAO,gBAAA,GAAA1M,EAAA,CAAAmM,WAAA;MAAA,MAAA7L,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASP,MAAA,CAAA+L,aAAA,CAAAK,gBAAA,EAAAD,iBAAA,CAA0C;IAAA,EAAC;IAC5EzM,EAAA,CAAAe,SAAA,mBAAiF;IAEzFf,EADI,CAAAG,YAAA,EAAM,EACJ;IAQkBH,EAPxB,CAAAC,cAAA,mBAC4E,gBACpD,gBACY,gBACY,gBACX,gBACS,gBACK;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACrDH,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,kWAKA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,uhBAOA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,4hCAsBA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,0YAKA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,qHAEA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,sHAEA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,qlBASA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,8cAOA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,iOAGA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,8PAIA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,sJAEA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,gHAEA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,iWAKA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,+YAMA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,yOAIA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,wLAGA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,ohBAQA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,6nBASA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,seAOA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,6nBASA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,odAMA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,uFACA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,sOAGA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,yRAIA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,iRAIA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,yQAIA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,2fAOA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,8RAIA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,mOAGA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,8GAGA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,uWAMA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,sYAMA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,wDACA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,gNAGA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,iqBAUA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,2rBAWA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,yZAOA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,iKAGA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,oVAKA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,gZAMA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,kfAOA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,+JAGA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,gsBAUA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,yrBAUA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,mLAGA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,kNAGA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,iNAGA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,oGAEA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,4BACA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,wCACA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,8CACA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,8CACA;IAAAF,EAAA,CAAAe,SAAA,WAAI;IACJf,EAAA,CAAAE,MAAA,yEAEJ;IAMxBF,EANwB,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ,EACJ,EACJ,EACJ;IAEFH,EADJ,CAAAC,cAAA,iBAAuF,wBAG1D;IADgCD,EAAA,CAAAiJ,gBAAA,2BAAA0D,+EAAAxE,MAAA;MAAAnI,EAAA,CAAAU,aAAA,CAAAuL,IAAA;MAAA,MAAA3L,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAAZ,EAAA,CAAAoJ,kBAAA,CAAA9I,MAAA,CAAAsM,cAAA,EAAAzE,MAAA,MAAA7H,MAAA,CAAAsM,cAAA,GAAAzE,MAAA;MAAA,OAAAnI,EAAA,CAAAa,WAAA,CAAAsH,MAAA;IAAA,EAA4B;IACjFnI,EAAA,CAAAQ,UAAA,sBAAAqM,0EAAA;MAAA7M,EAAA,CAAAU,aAAA,CAAAuL,IAAA;MAAA,MAAA3L,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAYP,MAAA,CAAAwM,KAAA,EAAO;IAAA,EAAC;IAEhC9M,EAFiC,CAAAG,YAAA,EAAa,EACpC,EACJ;;;;IAreaH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAA0K,SAAA,CAAe;IAAuBhL,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAA8K,SAAA,CAAe;IAMpCpL,EAAA,CAAAI,SAAA,GAA6B;;IAajCJ,EAAA,CAAAI,SAAA,IACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAuL,gBAAA,CAAAf,KAAA,MACJ;IAOI9K,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAuL,gBAAA,CAAAd,KAAA,MACJ;IAOI/K,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAuL,gBAAA,CAAAkB,OAAA,MACJ;IAOI/M,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAuL,gBAAA,CAAAmB,UAAA,MACJ;IAcIhN,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAuL,gBAAA,CAAAoB,UAAA,MACJ;IAOIjN,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAuL,gBAAA,CAAAqB,YAAA,MACJ;IAOIlN,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAuL,gBAAA,CAAAV,OAAA,MACJ;IAOInL,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAuL,gBAAA,CAAAF,IAAA,MACJ;IAOI3L,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAuL,gBAAA,CAAAD,QAAA,MACJ;IAEiB5L,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAA0K,SAAA,CAAe;IAoB5BhL,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAuL,gBAAA,CAAAR,KAAA,MACJ;IAOIrL,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAuL,gBAAA,CAAAL,KAAA,MACJ;IAEExL,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAA0K,SAAA,CAAe;IAuBmBhL,EAAA,CAAAI,SAAA,IAAqC;;IA+UpCJ,EAAA,CAAAI,SAAA,KAA4B;IAA5BJ,EAAA,CAAA2K,gBAAA,YAAArK,MAAA,CAAAsM,cAAA,CAA4B;;;;;IAKzF5M,EADJ,CAAAC,cAAA,cAAyC,eACpB;IAEbD,EADA,CAAAe,SAAA,eAA2C,eAClB;IACzBf,EAAA,CAAAC,cAAA,eAA0C;IACtCD,EAAA,CAAAE,MAAA,kCACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,aAA4C;IACxCD,EAAA,CAAAE,MAAA,+FAEJ;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGAH,EADJ,CAAAC,cAAA,eAAyB,eACqB;IAEtCD,EAAA,CAAAe,SAAA,mBAES;IAIzBf,EAHY,CAAAG,YAAA,EAAM,EACJ,EACJ,EACJ;;;IAN4DH,EAAA,CAAAI,SAAA,IAA6B;IAA7BJ,EAAA,CAAAkB,UAAA,eAAAlB,EAAA,CAAA4K,eAAA,IAAAuC,GAAA,EAA6B;;;;;IAuCvFnN,EAAA,CAAAC,cAAA,eAA6C;IACzCD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAgF,eAAA,CAAAhF,MAAA,CAAA8M,kBAAA,EAAAjI,KAAA,MACJ;;;;;IAkByCnF,EAAA,CAAAC,cAAA,WACY;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAACH,EAAA,CAAAC,cAAA,WACb;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA9CH,EAAA,CAAAI,SAAA,EAAuC;IAAvCJ,EAAA,CAAAK,kBAAA,OAAAC,MAAA,CAAA0G,oBAAA,CAAApB,WAAA,MAAuC;;;;;IAW7F5F,EADJ,CAAAC,cAAA,eAA+D,UACtD;IACDD,EAAA,CAAAE,MAAA,GACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IAFEH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,iCAAAC,MAAA,CAAA0G,oBAAA,CAAApB,WAAA,MACJ;;;;;IAQA5F,EADJ,CAAAC,cAAA,eAA8D,UACrD;IACDD,EAAA,CAAAE,MAAA,gBACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;;;;;IAEFH,EADJ,CAAAC,cAAA,eAA6F,UACpF;IACDD,EAAA,CAAAE,MAAA,GACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IAFEH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA0G,oBAAA,CAAAlB,KAAA,yBACJ;;;;;IAOA9F,EAAA,CAAAC,cAAA,eAA6C;IACzCD,EAAA,CAAAE,MAAA,GACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADFH,EAAA,CAAAI,SAAA,EACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA+M,aAAA,YACJ;;;;;IALArN,EADJ,CAAAC,cAAA,eAA+D,UACtD;IACDD,EAAA,CAAAE,MAAA,sBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAgB,UAAA,IAAAsM,qDAAA,mBAA6C;IAGjDtN,EAAA,CAAAG,YAAA,EAAM;;;;IAHIH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAA+M,aAAA,CAAmB;;;;;IAKzBrN,EADJ,CAAAC,cAAA,eAA+D,UACtD;IACDD,EAAA,CAAAE,MAAA,yBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAuB;IACnBD,EAAA,CAAAE,MAAA,GACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IAFEH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA0G,oBAAA,CAAAC,QAAA,YACJ;;;;;;IAUJjH,EAAA,CAAA6F,uBAAA,GAAuC;IACnC7F,EAAA,CAAAe,SAAA,eAA6B;IAEzBf,EADJ,CAAAC,cAAA,eAAoC,eACF;IAC1BD,EAAA,CAAAE,MAAA,oBACA;IAAAF,EAAA,CAAAC,cAAA,gBAAuD;IAAAD,EAAA,CAAAE,MAAA,eACnD;IAAAF,EAAA,CAAAC,cAAA,eAC4E;IADvDD,EAAA,CAAAQ,UAAA,mBAAA+M,8EAAA;MAAAvN,EAAA,CAAAU,aAAA,CAAA8M,IAAA;MAAA,MAAAlN,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASP,MAAA,CAAAmN,mCAAA,EAAqC;IAAA,EAAC;IAE5EzN,EAFQ,CAAAG,YAAA,EAC4E,EAAO,EACrF;IACNH,EAAA,CAAAC,cAAA,eAAuB;IACnBD,EAAA,CAAAE,MAAA,GACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IACNH,EAAA,CAAAe,SAAA,gBAA6B;IAEzBf,EADJ,CAAAC,cAAA,gBAAoC,gBACF;IAC1BD,EAAA,CAAAE,MAAA,2BACA;IAAAF,EAAA,CAAAC,cAAA,iBAAuD;IAAAD,EAAA,CAAAE,MAAA,kBACnD;IAAAF,EAAA,CAAAC,cAAA,gBAC4E;IADvDD,EAAA,CAAAQ,UAAA,mBAAAkN,+EAAA;MAAA1N,EAAA,CAAAU,aAAA,CAAA8M,IAAA;MAAA,MAAAlN,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASP,MAAA,CAAAqN,qCAAA,EAAuC;IAAA,EAAC;IAE9E3N,EAFQ,CAAAG,YAAA,EAC4E,EAAO,EACrF;IACNH,EAAA,CAAAC,cAAA,gBAAuB;IACnBD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;;;;;IAdEH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAsN,0BAAA,cACJ;IAWI5N,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAuN,kBAAA,cACJ;;;;;;IAUJ7N,EAAA,CAAA6F,uBAAA,GAAsC;IAClC7F,EAAA,CAAAC,cAAA,kBAEqB;IAAjBD,EAAA,CAAAQ,UAAA,mBAAAsN,iFAAA;MAAA9N,EAAA,CAAAU,aAAA,CAAAqN,IAAA;MAAA,MAAAzN,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASP,MAAA,CAAA0N,IAAA,EAAM;IAAA,EAAC;IAAChO,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAe,SAAA,gBACwC;IAAAf,EAAA,CAAAG,YAAA,EAAS;;;;;;;IAI3EH,EAAA,CAAA6F,uBAAA,GAA8E;IAC1E7F,EAAA,CAAAC,cAAA,kBAEyB;IAArBD,EAAA,CAAAQ,UAAA,mBAAAyN,uFAAA;MAAAjO,EAAA,CAAAU,aAAA,CAAAwN,IAAA;MAAA,MAAA5N,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASP,MAAA,CAAA6N,QAAA,EAAU;IAAA,EAAC;IAEuDnO,EAD3E,CAAAC,cAAA,eAAkE,eACQ,gBAC5C;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAACH,EAAA,CAAAC,cAAA,gBACjB;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IAAQF,EAAR,CAAAG,YAAA,EAAO,EAAO;IAACH,EAAA,CAAAe,SAAA,gBACf;IAEtEf,EADI,CAAAG,YAAA,EAAM,EACD;;;;;IAHuBH,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAK,kBAAA,KAAAC,MAAA,CAAAsN,0BAAA,aAAsC;;;;;;IAQlE5N,EADJ,CAAAC,cAAA,UAA4B,kBAGC;IAArBD,EAAA,CAAAQ,UAAA,mBAAA4N,4FAAA;MAAApO,EAAA,CAAAU,aAAA,CAAA2N,IAAA;MAAA,MAAA/N,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASP,MAAA,CAAA6N,QAAA,EAAU;IAAA,EAAC;IAACnO,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAe,SAAA,eAEyB;IAC1Df,EAD0D,CAAAG,YAAA,EAAS,EAC7D;;;;;IANNH,EAAA,CAAAgB,UAAA,IAAAsN,mEAAA,iBAA4B;;;;IAAtBtO,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,MAAoB;;;;;IAflCP,EAAA,CAAAC,cAAA,eAAqC;IAcjCD,EAbA,CAAAgB,UAAA,IAAAuN,8DAAA,2BAA8E,IAAAC,6DAAA,gCAAAxO,EAAA,CAAAsG,sBAAA,CAajD;IASjCtG,EAAA,CAAAG,YAAA,EAAM;;;;;IAtBaH,EAAA,CAAAI,SAAA,EAA0C;IAAAJ,EAA1C,CAAAkB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,SAAAD,MAAA,CAAAmO,gBAAA,CAA0C,aAAAC,kBAAA,CAAmB;;;;;;IArJpF1O,EADJ,CAAAC,cAAA,eAA2E,eACpD;IACfD,EAAA,CAAAE,MAAA,iBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADJ,CAAAC,cAAA,eAA2B,UAClB;IACDD,EAAA,CAAAE,MAAA,iBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAuB;IACnBD,EAAA,CAAAE,MAAA,GACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IAEFH,EADJ,CAAAC,cAAA,eAAiB,UACR;IACDD,EAAA,CAAAE,MAAA,eACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAuB;IACnBD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IAEFH,EADJ,CAAAC,cAAA,gBAAiB,WACR;IACDD,EAAA,CAAAE,MAAA,yBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAuB;IACnBD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IAEFH,EADJ,CAAAC,cAAA,gBAAiB,WACR;IACDD,EAAA,CAAAE,MAAA,eACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAgB,UAAA,KAAA2N,+CAAA,mBAA6C;IAGjD3O,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADJ,CAAAC,cAAA,gBAAiB,WACR;IACDD,EAAA,CAAAE,MAAA,sBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAuB;IACnBD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IACNH,EAAA,CAAAe,SAAA,gBAA6B;IAEzBf,EADJ,CAAAC,cAAA,gBAAiB,gBACY;IACrBD,EAAA,CAAAE,MAAA,sBACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IAEFH,EADJ,CAAAC,cAAA,gBAAiB,WACR;IACDD,EAAA,CAAAE,MAAA,IAAqC;IAC0BF,EAD1B,CAAAgB,UAAA,KAAA4N,gDAAA,kBACY,KAAAC,gDAAA,kBACC;IAClD7O,EAAA,CAAAE,MAAA,gDACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IACNH,EAAA,CAAAe,SAAA,gBAA6B;IAEzBf,EADJ,CAAAC,cAAA,gBAAiB,gBACY;IACrBD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IACNH,EAAA,CAAAgB,UAAA,KAAA8N,+CAAA,mBAA+D;IAM3D9O,EADJ,CAAAC,cAAA,gBAAiB,WACR;IACDD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IAMNH,EALA,CAAAgB,UAAA,KAAA+N,+CAAA,mBAA8D,KAAAC,+CAAA,mBAK+B;IAK7FhP,EAAA,CAAAe,SAAA,gBAA6B;IAS7Bf,EARA,CAAAgB,UAAA,KAAAiO,+CAAA,mBAA+D,KAAAC,+CAAA,mBAQA;IAS3DlP,EADJ,CAAAC,cAAA,gBAAiB,WACR;IACDD,EAAA,CAAAE,MAAA,qBACJ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAuB;IACnBD,EAAA,CAAAE,MAAA,IACJ;IACJF,EADI,CAAAG,YAAA,EAAM,EACJ;IACNH,EAAA,CAAAgB,UAAA,KAAAmO,wDAAA,2BAAuC;IA2BnCnP,EADJ,CAAAC,cAAA,gBAA+E,mBAGhB;IAArBD,EAAA,CAAAQ,UAAA,mBAAA4O,kEAAA;MAAApP,EAAA,CAAAU,aAAA,CAAA2O,IAAA;MAAA,MAAA/O,MAAA,GAAAN,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASP,MAAA,CAAAgP,QAAA,EAAU;IAAA,EAAC;IACOtP,EADN,CAAAe,SAAA,gBACM,iBAA2B;IACxFf,EAAA,CAAAE,MAAA,aAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;IASjBH,EAPA,CAAAgB,UAAA,KAAAuO,wDAAA,0BAAsC,KAAAC,+CAAA,mBAOD;IAyB7CxP,EADI,CAAAG,YAAA,EAAM,EACJ;;;;IArKMH,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAwH,gBAAA,CAAA2H,IAAA,MACJ;IAOIzP,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAqD,aAAA,MACJ;IAOI3D,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAoP,wBAAA,MACJ;IAMM1P,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAA+M,aAAA,CAAmB;IASrBrN,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA0G,oBAAA,CAAAN,IAAA,MACJ;IAUI1G,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAK,kBAAA,6BAAAC,MAAA,CAAAoF,SAAA,MAAqC;IAChC1F,EAAA,CAAAI,SAAA,EAA0C;IAA1CJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAA0G,oBAAA,CAAAN,IAAA,cAA0C;IAC1C1G,EAAA,CAAAI,SAAA,EAA2C;IAA3CJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAA0G,oBAAA,CAAAN,IAAA,eAA2C;IAOhD1G,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA0G,oBAAA,CAAAN,IAAA,WACJ;IAEc1G,EAAA,CAAAI,SAAA,EAA2C;IAA3CJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAA0G,oBAAA,CAAAN,IAAA,eAA2C;IAOrD1G,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA0G,oBAAA,CAAAE,WAAA,uCACJ;IAEclH,EAAA,CAAAI,SAAA,EAA0C;IAA1CJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAA0G,oBAAA,CAAAN,IAAA,cAA0C;IAK1C1G,EAAA,CAAAI,SAAA,EAAyE;IAAzEJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAA0G,oBAAA,CAAAN,IAAA,kBAAApG,MAAA,CAAA0G,oBAAA,CAAAlB,KAAA,CAAyE;IAMzE9F,EAAA,CAAAI,SAAA,GAA2C;IAA3CJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAA0G,oBAAA,CAAAN,IAAA,eAA2C;IAQ3C1G,EAAA,CAAAI,SAAA,EAA2C;IAA3CJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAA0G,oBAAA,CAAAN,IAAA,eAA2C;IAarD1G,EAAA,CAAAI,SAAA,GACJ;IADIJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA+M,aAAA,GAAA/M,MAAA,CAAA0G,oBAAA,CAAAC,QAAA,YACJ;IAEWjH,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAAmO,gBAAA,CAAsB;IA0BAzO,EAAA,CAAAI,SAAA,EAAyC;IAAzCJ,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAA8G,eAAA,KAAA6I,GAAA,EAAArP,MAAA,CAAAC,WAAA,QAAyC;IAGtEP,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAA4P,WAAA,aAAAtP,MAAA,CAAAC,WAAA,MAAiC;IAItBP,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,OAAqB;IAOrBP,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,MAAoB;;;;;IAr8B/CP,EAAA,CAAAC,cAAA,cAAwD;IAgzBpDD,EA9yBA,CAAAgB,UAAA,IAAA6O,wCAAA,mBAAmF,IAAAC,wCAAA,oBA6J1C,IAAAC,wCAAA,qBAkJO,IAAAC,wCAAA,mBAyeP,IAAAC,wCAAA,oBAsBkC;IA+K/EjQ,EAAA,CAAAG,YAAA,EAAM;;;;IA/9BoBH,EAAA,CAAAkB,UAAA,gBAAA+B,SAAA,CAAa;IAE7BjD,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,MAAoB;IA6JpBP,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,MAAoB;IAkJpBP,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,MAAoB;IAyepBP,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAkB,UAAA,SAAAZ,MAAA,CAAAC,WAAA,MAAoB;IAsBpBP,EAAA,CAAAI,SAAA,EAAc;IAAdJ,EAAA,CAAAkB,UAAA,UAAAZ,MAAA,CAAA0C,OAAA,CAAc;;;ADhzBhC,MAAMkN,gBAAgB,GAAGnQ,WAAW,CAACoQ,UAAU;AAS/C,WAAaC,mBAAmB;EAA1B,MAAOA,mBAAmB;IAyD9BC,YACUC,WAAwB,EACxBC,cAA8B,EAC9BC,QAAkB,EAClBC,gBAAkC,EAClCC,cAA8B,EAC9BC,cAA8B,EAC9BC,YAA0B,EAC3BC,oBAA0C,EACzCC,SAAuB,EACvBC,MAAc,EACdC,QAAmB,EACDC,QAAkB;MAXpC,KAAAX,WAAW,GAAXA,WAAW;MACX,KAAAC,cAAc,GAAdA,cAAc;MACd,KAAAC,QAAQ,GAARA,QAAQ;MACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;MAChB,KAAAC,cAAc,GAAdA,cAAc;MACd,KAAAC,cAAc,GAAdA,cAAc;MACd,KAAAC,YAAY,GAAZA,YAAY;MACb,KAAAC,oBAAoB,GAApBA,oBAAoB;MACnB,KAAAC,SAAS,GAATA,SAAS;MACT,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,QAAQ,GAARA,QAAQ;MACU,KAAAC,QAAQ,GAARA,QAAQ;MApE3B,KAAAjO,OAAO,GAAY,KAAK;MACxB,KAAA6E,WAAW,GAAY,KAAK;MACrC,KAAAqJ,IAAI,GAAG,EAAU;MACT,KAAAC,IAAI,GAAG,IAAIvR,OAAO,EAAE;MAC5B,KAAAwR,IAAI,GAAW,WAAW;MACnB,KAAAC,uBAAuB,GAA4B,EAA6B;MAChF,KAAA/F,UAAU,GAAU,IAAI,CAACoF,cAAc,CAACY,aAAa,EAAE;MACvD,KAAA/Q,WAAW,GAAW,CAAC;MAEvB,KAAAiC,cAAc,GAAgB,EAAE;MAChC,KAAA+O,WAAW,GAAW,EAAE;MAExB,KAAA1N,KAAK,GAAY,KAAK;MAEtB,KAAAa,gBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAEzC,KAAA8M,OAAO,GAAG;QACRC,EAAE,EAAE,iBAAiB;QACrBC,MAAM,EAAE,IAAI;QACZC,MAAM,EAAE,IAAI;QACZC,OAAO,EAAE;OACV;MAEM,KAAAC,iBAAiB,GAAc,EAAe;MAC9C,KAAA7K,oBAAoB,GAAgB,EAAiB;MAErD,KAAArD,aAAa,GAAW,IAAI;MAC5B,KAAAC,oBAAoB,GAAW,IAAI;MACnC,KAAA8L,wBAAwB,GAAW,GAAG;MACtC,KAAA5H,gBAAgB,GAAa,EAAc;MAC3C,KAAAsF,kBAAkB,GAAW,CAAC;MAC9B,KAAA/D,mBAAmB,GAAW,SAAS;MACvC,KAAAkC,iBAAiB,GAAoB,EAAqB;MAC1D,KAAAL,wBAAwB,GAAwB,EAAyB;MACzE,KAAAW,gBAAgB,GAAqB,EAAsB;MAC3D,KAAA3I,YAAY,GAAG,IAAI,CAACwN,cAAc,CAACxN,YAAY;MAC/C,KAAAc,SAAS,GAAG,IAAI,CAAC0M,cAAc,CAAC1M,SAAS;MACzC,KAAAiH,SAAS,GAAG,IAAI,CAACyF,cAAc,CAACzF,SAAS;MACzC,KAAA3F,eAAe,GAAyB,EAAE;MAC1C,KAAAwM,aAAa,GAAuB,EAAwB;MAC5D,KAAAC,WAAW,GAAG,IAAI,CAACrB,cAAc,CAACqB,WAAW;MAC7C,KAAAnL,eAAe,GAAGnH,aAAa,CAACuS,kBAAkB,EAAE;MACpD,KAAAC,WAAW,GAAsB,EAAE;MACnC,KAAAC,OAAO,GAAQ,EAAE;MACjB,KAAAxH,IAAI,GAAqB,IAAItL,gBAAgB,CAAC,EAAE,CAAC;MACjD,KAAA+S,wBAAwB,GAAc,EAAe;MACrD,KAAA1D,gBAAgB,GAAG,KAAK;MACxB,KAAA2D,oBAAoB,GAAiC,EAAE;MACvD,KAAAC,mBAAmB,GAAGC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC;MAIxE,KAAAC,kBAAkB,GAAGzS,WAAW,CAACyS,kBAAkB;MACnD,KAAAC,oBAAoB,GAAG,KAAK;MAE5B,KAAAhI,kBAAkB,GAAGvL,MAAM,CAAC,KAAK,CAAC;MAsclC,KAAAwG,SAAS,GAAW,CAAC;MAgDrB,KAAA0F,SAAS,GAAY,IAAI;MACzB,KAAAJ,SAAS,GAAY,KAAK;MAC1B,KAAAzC,WAAW,GAAY,KAAK;MA0F5B,KAAAmK,MAAM,GAAY,KAAK;MAKvB,KAAAC,UAAU,GAAY,KAAK;IAzkBvB;IAEJC,QAAQA,CAAA;MACN,IAAI,CAAC,IAAI,CAAC5P,OAAO,EAAE;QACjB,MAAM6P,eAAe,GAAG,IAAI,CAACtC,cAAc,CAACuC,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC;QAC7E,IAAIF,eAAe,EAAE;UACnB,IAAI,CAACV,wBAAwB,GAAGa,IAAI,CAACC,KAAK,CAACJ,eAAe,CAAC;QAC7D;MACF;MAEA,IAAI,CAACK,iBAAiB,GAAG,CACvB;QACEC,UAAU,EAAE,QAAQ;QACpBC,UAAU,EAAE,CAAC;QACbC,SAAS,EAAE;OACZ,EACD;QACEF,UAAU,EAAE,OAAO;QACnBC,UAAU,EAAE,CAAC;QACbC,SAAS,EAAE;OACZ,EACD;QACEF,UAAU,EAAE,OAAO;QACnBC,UAAU,EAAE,CAAC;QACbC,SAAS,EAAE;OACZ,CACF;MAED,IAAI,CAACnC,IAAI,GAAG,IAAI,CAACZ,WAAW,CAACgD,eAAe,EAAE;MAC9C,IAAI,CAACC,cAAc,EAAE;MACrB,IAAI,CAACC,cAAc,CAAC,6BAA6B,EAAE,sCAAsC,CAAC;MAE1F;MACA,IAAI,CAACC,UAAU,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAAC,MAAK;QAC1D;QACA;QACAC,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;QAE3E,IAAI,CAACnB,oBAAoB,GAAG,IAAI;QAChC,IAAI,CAACoB,iBAAiB,EAAE;QAGxB,IAAI,IAAI,CAACpB,oBAAoB,EAAE;UAG7B,IAAI,CAAC9B,cAAc,CAACmD,yBAAyB,CAAC;YAC5CC,GAAG,EAAE,IAAI,CAACvB;WACX,CAAC,CAACwB,SAAS,CAAC;YACXhG,IAAI,EAAGiG,GAAQ,IAAI;cACjBN,OAAO,CAACC,GAAG,CAACK,GAAG,CAAC;YAClB,CAAC;YACDC,KAAK,EAAGC,GAAQ,IAAI;cAClBR,OAAO,CAACC,GAAG,CAACO,GAAG,CAAC;YAClB;WACD,CAAC;UAGF;UACA;UACA;QACF;MAEF,CAAC,CAAC,CAACC,KAAK,CAACF,KAAK,IAAIP,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEM,KAAK,CAAC,CAAC;IAEzE;IAEAL,iBAAiBA,CAAA;MACf;MACA;MACA,IAAI,OAAOQ,QAAQ,KAAK,WAAW,EAAE;QACnCA,QAAQ,CAACN,GAAG,GAAG,0CAA0C;QACzDM,QAAQ,CAACC,WAAW,EAAE;QACtBD,QAAQ,CAACE,SAAS,EAAE;MACtB,CAAC,MAAM;QACLZ,OAAO,CAACO,KAAK,CAAC,2BAA2B,CAAC;MAC5C;IAEF;IAEAT,UAAUA,CAACe,GAAW;MACpB,OAAO,IAAIC,OAAO,CAAO,CAACC,OAAO,EAAEC,MAAM,KAAI;QAC3C,MAAMC,MAAM,GAAG,IAAI,CAAC5D,QAAQ,CAAC6D,aAAa,CAAC,QAAQ,CAAC;QACpDD,MAAM,CAACE,GAAG,GAAGN,GAAG;QAChBI,MAAM,CAACG,MAAM,GAAG,MAAK;UACnBL,OAAO,EAAE;QACX,CAAC;QACDE,MAAM,CAACI,OAAO,GAAId,KAAU,IAAI;UAC9BS,MAAM,CAACT,KAAK,CAAC;QACf,CAAC;QACD,IAAI,CAAClD,QAAQ,CAACiE,WAAW,CAAChE,QAAQ,CAACiE,IAAI,EAAEN,MAAM,CAAC;MAClD,CAAC,CAAC;IACJ;IAEAO,YAAYA,CAACnE,QAAmB,EAAE8D,GAAW;MAC3C,MAAMF,MAAM,GAAG5D,QAAQ,CAAC6D,aAAa,CAAC,QAAQ,CAAC;MAC/CD,MAAM,CAAClO,IAAI,GAAG,iBAAiB;MAC/BkO,MAAM,CAACE,GAAG,GAAGA,GAAG;MAChB9D,QAAQ,CAACiE,WAAW,CAAC,IAAI,CAAChE,QAAQ,CAACiE,IAAI,EAAEN,MAAM,CAAC;MAChD,OAAOA,MAAM;IACf;IAEAQ,oBAAoBA,CAAA;MAClB,MAAMC,aAAa,GAAGpE,QAAQ,CAACqE,aAAa,CAAC,8CAA8C,CAAC;MAC5F,IAAID,aAAa,EAAE;QACjBA,aAAa,CAACE,MAAM,EAAE;QACtB5B,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;MACxC;IACF;IAEAJ,cAAcA,CAACgB,GAAW,EAAEgB,QAAgB;MAC1C,OAAO,IAAIf,OAAO,CAAO,CAACC,OAAO,EAAEC,MAAM,KAAI;QAC3C,MAAMC,MAAM,GAAG,IAAI,CAAC5D,QAAQ,CAAC6D,aAAa,CAAC,QAAQ,CAAC;QACpDD,MAAM,CAACE,GAAG,GAAGN,GAAG;QAChBI,MAAM,CAACa,YAAY,CAAC,IAAI,EAAE,aAAa,CAAC;QACxCb,MAAM,CAACa,YAAY,CAAC,WAAW,EAAED,QAAQ,CAAC;QAC1CZ,MAAM,CAACG,MAAM,GAAG,MAAK;UACnBL,OAAO,EAAE;QACX,CAAC;QACDE,MAAM,CAACI,OAAO,GAAId,KAAU,IAAI;UAC9BS,MAAM,CAACT,KAAK,CAAC;QACf,CAAC;QACD,IAAI,CAAClD,QAAQ,CAACiE,WAAW,CAAChE,QAAQ,CAACyE,IAAI,EAAEd,MAAM,CAAC;MAClD,CAAC,CAAC;IACJ;IAEOe,kBAAkBA,CAAA;MACvBhC,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClD,IAAIgC,IAAI,GAAG3E,QAAQ,CAAC4D,aAAa,CAAC,QAAQ,CAAC;MAC3Ce,IAAI,CAACH,YAAY,CAAC,IAAI,EAAE,iBAAiB,CAAC;MAC1CG,IAAI,CAACd,GAAG,GAAG,gCAAgC;MAC3Cc,IAAI,CAAClP,IAAI,GAAG,iBAAiB;MAC7BkP,IAAI,CAACC,KAAK,GAAG,IAAI;MACjBD,IAAI,CAACE,OAAO,GAAG,OAAO;MACtB7E,QAAQ,CAAC8E,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAACd,WAAW,CAACW,IAAI,CAAC;IAC5D;IAEAI,eAAeA,CAAA;MACb,IAAI,CAACC,SAAS,GAAGhF,QAAQ,CAACiF,cAAc,CAAC,kBAAkB,CAAC;IAC9D;IAEAC,WAAWA,CAAA;MACT,IAAI,CAAChF,IAAI,CAACiF,WAAW,EAAE;MACvB,IAAI,CAAChB,oBAAoB,EAAE;MAC3B,MAAMC,aAAa,GAAGpE,QAAQ,CAACiF,cAAc,CAAC,aAAa,CAAC;MAC5D,IAAIb,aAAa,EAAE;QACjBA,aAAa,CAACE,MAAM,EAAE;MACxB;IACF;IAEA,IAAIc,eAAeA,CAAA;MACjB,OAAO,IAAI,CAAC5H,gBAAgB;IAC9B;IAEA,IAAIjG,YAAYA,CAAA;MACd,OAAO,IAAI,CAACkC,IAAI,CAAC4L,QAAQ;IAC3B;IAEAjV,YAAYA,CAAA;MACV,IAAI,CAACwG,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;MACpC0O,UAAU,CAAC,MAAK;QACd,EAAE,CAACC,OAAO,CAACC,IAAI,CAACxF,QAAQ,CAACyF,sBAAsB,CAAC,KAAK,CAAC,EAAGC,EAAO,IAAI;UAClEA,EAAE,CAACC,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;QAC9B,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAAC;IACV;IAEAC,cAAcA,CAAA;MACZ,IAAI,CAAC5L,wBAAwB,GAAG,IAAI,CAACD,SAAS,CAAC8L,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACvH,IAAI,KAAK,IAAI,CAACyB,IAAI,CAAC+F,SAAS,CAAC;MAC5F,IAAI,CAAC1L,iBAAiB,GAAG,IAAI,CAACD,UAAU,CAAC4L,MAAM,CAACP,EAAE,IAAIA,EAAE,CAAC9T,IAAI,KAAK,IAAI,CAAC6N,cAAc,CAACyG,oBAAoB,CAAC,IAAI,CAACjG,IAAI,CAAC1F,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAACkF,cAAc,CAAC0G,mBAAmB,EAAE;MAC5KzD,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC1I,wBAAwB,CAAC;MAC1C,IAAI,CAACR,IAAI,GAAG,IAAItL,gBAAgB,CAAC;QAC/B0L,KAAK,EAAE,IAAI3L,kBAAkB,CAAC,IAAI,CAAC+R,IAAI,CAACjP,SAAS,EAAE;UACjDoV,UAAU,EAAE,CAAChY,UAAU,CAACuJ,QAAQ;SACjC,CAAC;QACFmC,KAAK,EAAE,IAAI5L,kBAAkB,CAAC,IAAI,CAAC+R,IAAI,CAAC/O,QAAQ,EAAE;UAChDkV,UAAU,EAAE,CAAChY,UAAU,CAACuJ,QAAQ;SACjC,CAAC;QACFmE,OAAO,EAAE,IAAI5N,kBAAkB,CAAC,EAAE,EAAE,EAAE,CAAC;QACvC6N,UAAU,EAAE,IAAI7N,kBAAkB,CAAC,EAAE,EAAE,EAAE,CAAC;QAC1CgM,OAAO,EAAE,IAAIhM,kBAAkB,CAAC,IAAI,CAAC+L,wBAAwB,EAAE;UAC7DmM,UAAU,EAAE,CAAChY,UAAU,CAACuJ,QAAQ;SACjC,CAAC;QACF6C,MAAM,EAAE,IAAItM,kBAAkB,CAAC,EAAE,EAAE;UACjCkY,UAAU,EAAE,CAAChY,UAAU,CAACuJ,QAAQ;SACjC,CAAC;QACF8C,MAAM,EAAE,IAAIvM,kBAAkB,CAAC,EAAE,EAAE;UACjCkY,UAAU,EAAE,CAAChY,UAAU,CAACuJ,QAAQ;SACjC,CAAC;QACF+C,IAAI,EAAE,IAAIxM,kBAAkB,CAAC,EAAE,EAAE;UAC/BkY,UAAU,EAAE,CAAChY,UAAU,CAACuJ,QAAQ;SACjC,CAAC;QACFgD,QAAQ,EAAE,IAAIzM,kBAAkB,CAAC,EAAE,EAAE;UACnCkY,UAAU,EAAE,CAAChY,UAAU,CAACuJ,QAAQ;SACjC,CAAC;QACFE,GAAG,EAAE,IAAI3J,kBAAkB,CAAC,EAAE,EAAE,EAC/B,CAAC;QACFkM,KAAK,EAAE,IAAIlM,kBAAkB,CAAC,IAAI,CAAC+R,IAAI,CAAC7F,KAAK,EAAE;UAC7CgM,UAAU,EAAE,CAAChY,UAAU,CAACuJ,QAAQ;SACjC,CAAC;QACF4C,KAAK,EAAE,IAAIrM,kBAAkB,CAAC,IAAI,CAACuR,cAAc,CAAC4G,kBAAkB,CAAC,IAAI,CAACpG,IAAI,CAAC1F,KAAK,CAAC,EAAE;UACrF6L,UAAU,EAAE,CAAChY,UAAU,CAACuJ,QAAQ;SACjC,CAAC;QACFH,GAAG,EAAE,IAAItJ,kBAAkB,CAAC,EAAE,EAAE,EAC/B;OACF,CAAC;IAEJ;IAEAoY,yBAAyBA,CAAA;MACvB,IAAI,IAAI,CAACvM,SAAS,EAAE;QAClB,IAAI,CAACN,IAAI,CAAC4L,QAAQ,CAAC,KAAK,CAAC,CAACkB,aAAa,CAAC,CAACnY,UAAU,CAACuJ,QAAQ,CAAC,CAAC;MAChE,CAAC,MAAM;QACL,IAAI,CAAC8B,IAAI,CAAC4L,QAAQ,CAAC,KAAK,CAAC,CAACkB,aAAa,CAAC,EAAE,CAAC;MAC7C;MAEA,IAAI,CAAC9M,IAAI,CAAC4L,QAAQ,CAAC,KAAK,CAAC,CAACmB,sBAAsB,EAAE;IACpD;IAEAC,QAAQA,CAAA;MACNnB,UAAU,CAAC,MAAK;QACd,IAAI,CAAC,IAAI,CAAC1S,KAAK,EAAE;UACf,IAAI,CAAC8T,eAAe,EAAE;QACxB,CAAC,MAAM;UACL,IAAI,CAACC,gBAAgB,CAAC,aAAa,CAAC;QACtC;QACA,IAAI,CAACC,cAAc,EAAE;QACrB,IAAI,CAACC,UAAU,EAAE;QACjB,IAAI,CAACC,qBAAqB,EAAE;MAC9B,CAAC,EAAE,IAAI,CAAC;IACV;IAEAC,UAAUA,CAAA;MACR,IAAI,CAACL,eAAe,EAAE;MACtB,IAAI,CAACE,cAAc,EAAE;MACrB,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAACC,qBAAqB,EAAE;IAC9B;IAEAJ,eAAeA,CAAA;MACb,IAAI,CAACC,gBAAgB,CAAC,eAAe,IAAI,CAAC/F,iBAAiB,CAACjQ,EAAE,EAAE,EAAE,uBAAuB,IAAI,CAACiQ,iBAAiB,EAAEjQ,EAAE,EAAE,CAAC;IACxH;IAEAiW,cAAcA,CAAA;MACZ,IAAI,CAACD,gBAAgB,CAAC,gBAAgB,IAAI,CAAC9P,gBAAgB,CAACjF,IAAI,EAAE,CAAC;IACrE;IAEAkV,qBAAqBA,CAAA;MACnB,IAAI,IAAI,CAACrI,wBAAwB,GAAG,GAAG,EAAE;QACvC,IAAI,CAACA,wBAAwB,GAAG,IAAI;MACtC;MACA,IAAI,CAACkI,gBAAgB,CAAC,cAAc,IAAI,CAAClI,wBAAwB,EAAE,CAAC;IACtE;IAEAoI,UAAUA,CAAA;MACR,IAAI,CAACF,gBAAgB,CAAC,YAAY,IAAI,CAACtS,eAAe,CAAC,IAAI,CAAC8H,kBAAkB,CAAC,CAAClI,KAAK,EAAE,EAAE,kBAAkB,IAAI,CAACI,eAAe,CAAC,IAAI,CAAC8H,kBAAkB,CAAC,CAAClI,KAAK,EAAE,CAAC;MACjK,IAAI,CAAC0S,gBAAgB,CAAC,YAAY,IAAI,CAACtS,eAAe,CAAC,IAAI,CAAC8H,kBAAkB,CAAC,CAAClI,KAAK,EAAE,EAAE,gBAAgB,IAAI,CAACI,eAAe,CAAC,IAAI,CAAC8H,kBAAkB,CAAC,CAAClI,KAAK,EAAE,CAAC;IACjK;IAEA0S,gBAAgBA,CAAChW,EAAU,EAAEqW,OAAgB;MAC3C,IAAIC,KAAK,GAAG,IAAI,CAAClV,OAAO,GAAG,SAAS,GAAG,IAAI,CAACmV,gBAAgB,CAAC,IAAI,CAACnR,oBAAoB,CAACN,IAAI,CAAC;MAC5F,IAAI0R,GAAG,GAAGnH,QAAQ,CAACiF,cAAc,CAACtU,EAAE,CAAC;MAErC,IAAIwW,GAAG,IAAI,IAAI,CAACpR,oBAAoB,EAAE;QACpCoR,GAAI,CAACC,KAAK,CAACC,eAAe,GAAGJ,KAAK;MACpC;MACA,IAAIE,GAAG,IAAI,CAACxW,EAAE,CAAC2W,QAAQ,CAAC,aAAa,CAAC,EAAE;QACtCH,GAAI,CAACC,KAAK,CAACH,KAAK,GAAG,OAAO;QAC1BE,GAAI,CAACC,KAAK,CAACG,OAAO,GAAG,GAAG;MAC1B;MAEA,IAAIP,OAAO,EAAE;QACX,IAAIQ,YAAY,GAAGxH,QAAQ,CAACiF,cAAc,CAAC+B,OAAO,CAAC;QACnD,IAAIQ,YAAY,EAAE;UAChBA,YAAY,CAACJ,KAAK,CAACH,KAAK,GAAG,OAAO;QACpC;MACF;IACF;IAEAQ,kBAAkBA,CAAC9W,EAAU,EAAEqW,OAAgB;MAC7C,IAAIC,KAAK,GAAG,IAAI,CAAClV,OAAO,GAAG,SAAS,GAAG,IAAI,CAACyD,cAAc,EAAE;MAC5D,IAAI2R,GAAG,GAAGnH,QAAQ,CAACiF,cAAc,CAACtU,EAAE,CAAC;MAErC,IAAIwW,GAAG,EAAE;QACP,IAAI,CAAC,IAAI,CAACvU,KAAK,IAAI,CAACjC,EAAE,CAAC2W,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC3W,EAAE,CAAC2W,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC3W,EAAE,CAAC2W,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC3W,EAAE,CAAC2W,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC3W,EAAE,CAAC2W,QAAQ,CAAC,OAAO,CAAC,EAAE;UAChKH,GAAI,CAACC,KAAK,CAACH,KAAK,GAAG,SAAS;QAC9B,CAAC,MAAM;UACLE,GAAI,CAACC,KAAK,CAACH,KAAK,GAAGA,KAAK;QAC1B;QACA,IAAI,CAACtW,EAAE,CAAC2W,QAAQ,CAAC,aAAa,CAAC,EAAE;UAC/BH,GAAI,CAACC,KAAK,CAACM,eAAe,GAAG,OAAO;UACpCP,GAAI,CAACC,KAAK,CAACO,UAAU,GAAG,OAAO;QACjC;MACF;MAEA,IAAIX,OAAO,EAAE;QACX,IAAIQ,YAAY,GAAGxH,QAAQ,CAACiF,cAAc,CAAC+B,OAAO,CAAC;QACnD,IAAIQ,YAAY,EAAE;UAChBA,YAAY,CAACJ,KAAK,CAACH,KAAK,GAAG,SAAS;QACtC;MACF;IAEF;IAEArW,WAAWA,CAACgX,eAAwB;MAClC,IAAIX,KAAK,GAAG,IAAI,CAAClV,OAAO,GAAG,SAAS,GAAG,IAAI,CAACyD,cAAc,EAAE;MAC5D,IAAI,IAAI,CAAC5C,KAAK,IAAIgV,eAAe,EAAE;QACjC,OAAO;UACL,QAAQ,EAAE,aAAaX,KAAK,EAAE;UAC9B,OAAO,EAAE,GAAGA,KAAK;SAClB;MACH;MACA,OAAO;QACL,QAAQ,EAAE,aAAaA,KAAK,EAAE;QAC9B,OAAO,EAAE,GAAGA,KAAK,EAAE;QACnB,SAAS,EAAE,KAAK;QAChB,QAAQ,EAAE;OACX;IACH;IAEAY,eAAeA,CAAClX,EAAU;MACxB,IAAIwW,GAAG,GAAGnH,QAAQ,CAACiF,cAAc,CAACtU,EAAE,CAAC;MACrCwW,GAAI,CAACC,KAAK,CAACU,aAAa,GAAG,MAAM;MACjCX,GAAI,CAACC,KAAK,CAACG,OAAO,GAAG,KAAK;IAC5B;IAEAQ,cAAcA,CAACpX,EAAU;MACvB,IAAIwW,GAAG,GAAGnH,QAAQ,CAACiF,cAAc,CAACtU,EAAE,CAAC;MACrCwW,GAAI,CAACC,KAAK,CAACU,aAAa,GAAG,SAAS;MACpCX,GAAI,CAACC,KAAK,CAACG,OAAO,GAAG,GAAG;IAC1B;IAEAS,6BAA6BA,CAACC,KAAa;MACzC,IAAI,IAAI,CAACrH,iBAAiB,CAACsH,WAAW,IAAI,KAAK,EAAE;QAC/C,OAAO,IAAI;MACb;MACA,OAAOD,KAAK,IAAI,IAAI,CAACrH,iBAAiB,CAACsH,WAAW;IACpD;IAEAzV,aAAaA,CAAC0V,MAAc,EAAEC,MAAc;MAC1C,MAAMC,WAAW,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,CAAC;MACrF,OAAOA,WAAW,CAACC,OAAO,CAACH,MAAM,CAAC,GAAGE,WAAW,CAACC,OAAO,CAACF,MAAM,CAAC;IAClE;IAEAlB,gBAAgBA,CAACzR,IAAY;MAC3B,IAAIA,IAAI,IAAIlH,WAAW,CAACga,OAAO,EAAE;QAC/B,OAAO,sEAAsE;MAC/E;MACA,IAAI9S,IAAI,IAAIlH,WAAW,CAACia,IAAI,EAAE;QAC5B,OAAO,yEAAyE;MAClF;MACA,IAAI/S,IAAI,IAAIlH,WAAW,CAACka,OAAO,EAAE;QAC/B,OAAO,sEAAsE;MAC/E;MACA,OAAO,EAAE;IACX;IAEAjT,cAAcA,CAAA;MACZ,OAAO,SAAS;IAClB;IAEA/E,eAAeA,CAACiY,SAAoB;MAClC,IAAI,CAAC9V,KAAK,GAAG,IAAI;MACjB,EAAE,CAAC2S,OAAO,CAACC,IAAI,CAACxF,QAAQ,CAACyF,sBAAsB,CAAC,KAAK,CAAC,EAAGC,EAAO,IAAI;QAClE,IAAI,CAACA,EAAE,CAAC/U,EAAE,CAAC2W,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC5B,EAAE,CAAC/U,EAAE,CAAC2W,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC5B,EAAE,CAAC/U,EAAE,CAAC2W,QAAQ,CAAC,aAAa,CAAC,EAAE;UACnG5B,EAAE,CAACC,SAAS,CAACrB,MAAM,CAAC,UAAU,CAAC;QACjC;QACAgB,UAAU,CAAC,MAAK;UACd;QAAA,CACD,EAAE,GAAG,CAAC;MACT,CAAC,CAAC;MACF,IAAI,CAACmC,kBAAkB,CAAC,aAAa,CAAC;MACtC,IAAI,CAACA,kBAAkB,CAAC,eAAe,IAAI,CAAC7G,iBAAiB,CAACjQ,EAAE,EAAE,EAAE,uBAAuB,IAAI,CAACiQ,iBAAiB,EAAEjQ,EAAE,EAAE,CAAC;MACxH;MACA,IAAI,CAAC8W,kBAAkB,CAAC,cAAc,IAAI,CAAChJ,wBAAwB,EAAE,CAAC;MACtE,IAAI,CAACgJ,kBAAkB,CAAC,gBAAgB,IAAI,CAAC5Q,gBAAgB,CAACjF,IAAI,EAAE,CAAC;MAErE,KAAK,IAAIf,QAAQ,IAAI,IAAI,CAACoB,YAAY,EAAE;QACtC,IAAI,IAAI,CAAC2O,iBAAiB,CAAC/P,QAAQ,IAAIA,QAAQ,CAAC2N,IAAI,EAAE;UACpD,IAAI,CAACiJ,kBAAkB,CAAC,gBAAgB5W,QAAQ,CAACe,IAAI,EAAE,CAAC;QAC1D;MACF;MAEA,IAAI,CAACgP,iBAAiB,GAAG8H,SAAS;MAClC;MACA,IAAI,CAAC9H,iBAAiB,CAAC+H,iBAAiB,GAAG,IAAI,CAACnJ,gBAAgB,CAACoJ,0BAA0B,CAAC,IAAI,CAAChI,iBAAiB,CAAC+H,iBAAiB,CAAC;MACrI,KAAK,IAAI9X,QAAQ,IAAI,IAAI,CAACoB,YAAY,EAAE;QACtC,IAAI,IAAI,CAAC2O,iBAAiB,CAAC/P,QAAQ,KAAKA,QAAQ,CAAC2N,IAAI,EAAE;UACrD,IAAI,CAACqJ,eAAe,CAAC,gBAAgBhX,QAAQ,CAACe,IAAI,EAAE,CAAC;QACvD;MACF;MACA,IAAI,CAACc,aAAa,GAAG,IAAI,CAACkO,iBAAiB,CAACsH,WAAW;MACvD,IAAI,IAAI,CAACtH,iBAAiB,CAACsH,WAAW,IAAI,KAAK,EAAE;QAC/C,IAAI,CAACxV,aAAa,GAAG,IAAI;MAC3B;MACA,IAAI,CAACC,oBAAoB,GAAG,IAAI,CAACiO,iBAAiB,CAACsH,WAAW;MAC9D,IAAI,CAACzJ,wBAAwB,GAAG,IAAI,CAACmC,iBAAiB,CAAC+H,iBAAiB,CAAC/S,MAAM,CAACiT,QAAQ,EAAE;MAC1F,IAAI,CAAChS,gBAAgB,GAAG,IAAI,CAAC5E,YAAY,CAACgU,MAAM,CAACP,EAAE,IAAIA,EAAE,CAAClH,IAAI,CAACsK,WAAW,EAAE,IAAI,IAAI,CAAClI,iBAAiB,CAAC/P,QAAS,CAACiY,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC;MAClI,IAAI,CAAC7H,OAAO,CAACpQ,QAAQ,GAAG,IAAI,CAACgG,gBAAgB,CAAC2H,IAAI;MAClD,IAAI,CAACyC,OAAO,CAACgH,KAAK,GAAG,IAAI,CAACvV,aAAa;MACvC,IAAI,CAACqW,yBAAyB,EAAE;MAChCrG,OAAO,CAACC,GAAG,CAAC,IAAI,CAACnF,gBAAgB,CAAC;MAClC,IAAI,CAACwL,mBAAmB,EAAE;MAC1B,IAAI,CAACC,wBAAwB,EAAE;MAC/B,IAAI,CAACxC,QAAQ,EAAE;MACf,IAAI,CAAC7T,KAAK,GAAG,KAAK;IACpB;IAEAjB,cAAcA,CAACd,QAAkB;MAC/B,IAAI,IAAI,CAAC+B,KAAK,EAAE;QACd,IAAI,CAAC6U,kBAAkB,CAAC,gBAAgB,IAAI,CAAC5Q,gBAAgB,CAACjF,IAAI,EAAE,CAAC;QACrE,IAAI,CAACiF,gBAAgB,GAAGhG,QAAQ;QAChC,IAAI,CAACoQ,OAAO,CAACpQ,QAAQ,GAAG,IAAI,CAACgG,gBAAgB,CAAC2H,IAAI;QAClD,IAAI,CAACwK,mBAAmB,EAAE;QAC1B,KAAK,IAAInY,QAAQ,IAAI,IAAI,CAACoB,YAAY,EAAE;UACtC,IAAI,CAACwV,kBAAkB,CAAC,gBAAgB5W,QAAQ,CAACe,IAAI,EAAE,CAAC;QAC1D;QACA,IAAI,CAACgV,cAAc,EAAE;MACvB;IACF;IAEAvU,WAAWA,CAAC4V,KAAa;MACvB,IAAI,IAAI,CAACrV,KAAK,IAAI,IAAI,CAACoV,6BAA6B,CAACC,KAAK,CAAC,EAAE;QAC3D,IAAI,CAACvV,aAAa,GAAGuV,KAAK;QAC1B,IAAI,CAAChH,OAAO,CAACgH,KAAK,GAAG,IAAI,CAACvV,aAAa;QACvC,IAAI,CAACsW,mBAAmB,EAAE;QAC1B,IAAI,CAACC,wBAAwB,EAAE;MACjC;IACF;IAEA1V,YAAYA,CAACkH,MAAc;MACzB,IAAI,IAAI,CAAC7H,KAAK,EAAE;QACd,IAAI,CAAC6U,kBAAkB,CAAC,cAAc,IAAI,CAAChJ,wBAAwB,EAAE,CAAC;QACtE,IAAI,CAACA,wBAAwB,GAAGhE,MAAM;QACtC,IAAI,CAACkM,gBAAgB,CAAC,cAAc,IAAI,CAAClI,wBAAwB,EAAE,CAAC;QACpE,IAAI,CAACwC,OAAO,CAACxN,gBAAgB,GAAG,IAAI,CAACgL,wBAAwB;QAC7D,IAAI,CAACuK,mBAAmB,EAAE;QAC1B,IAAI,CAACC,wBAAwB,EAAE;MACjC;IACF;IAIAjV,WAAWA,CAACkV,IAAS,EAAEC,CAAS;MAC9B,IAAI,CAAC1B,kBAAkB,CAAC,YAAY,IAAI,CAACpT,eAAe,CAAC,IAAI,CAAC8H,kBAAkB,CAAC,CAAClI,KAAK,EAAE,EAAE,kBAAkB,IAAI,CAACI,eAAe,CAAC,IAAI,CAAC8H,kBAAkB,CAAC,CAAClI,KAAK,EAAE,CAAC;MACnK,IAAI,CAACwT,kBAAkB,CAAC,YAAY,IAAI,CAACpT,eAAe,CAAC,IAAI,CAAC8H,kBAAkB,CAAC,CAAClI,KAAK,EAAE,EAAE,gBAAgB,IAAI,CAACI,eAAe,CAAC,IAAI,CAAC8H,kBAAkB,CAAC,CAAClI,KAAK,EAAE,CAAC;MACjK,IAAI,CAACkI,kBAAkB,GAAGgN,CAAC;MAC3B,IAAI,CAACxC,gBAAgB,CAAC,YAAY,IAAI,CAACtS,eAAe,CAAC,IAAI,CAAC8H,kBAAkB,CAAC,CAAClI,KAAK,EAAE,EAAE,kBAAkB,IAAI,CAACI,eAAe,CAAC,IAAI,CAAC8H,kBAAkB,CAAC,CAAClI,KAAK,EAAE,CAAC;MACjK,IAAI,CAAC0S,gBAAgB,CAAC,YAAY,IAAI,CAACtS,eAAe,CAAC,IAAI,CAAC8H,kBAAkB,CAAC,CAAClI,KAAK,EAAE,EAAE,gBAAgB,IAAI,CAACI,eAAe,CAAC,IAAI,CAAC8H,kBAAkB,CAAC,CAAClI,KAAK,EAAE,CAAC;MAC/J,IAAI,CAACmI,aAAa,GAAG,IAAI,CAAC/H,eAAe,CAAC,IAAI,CAAC8H,kBAAkB,CAAC,CAAClI,KAAK;MACxE,IAAI,CAACQ,SAAS,GAAG,IAAI,CAACJ,eAAe,CAAC,IAAI,CAAC8H,kBAAkB,CAAC,CAACiN,gBAAgB;MAC/E,IAAI,CAACvI,aAAa,GAAG,IAAI,CAACxM,eAAe,CAAC,IAAI,CAAC8H,kBAAkB,CAAC;IACpE;IAEAnH,aAAaA,CAACqU,WAAgB;MAC5B,IAAI,CAACtT,oBAAoB,GAAGsT,WAAW;MACvC,IAAI,CAACrE,SAAS,CAACoC,KAAK,CAACO,UAAU,GAAG,IAAI,CAACT,gBAAgB,CAAC,IAAI,CAACnR,oBAAoB,CAACN,IAAI,CAAC;MACvF,IAAI,CAACgR,QAAQ,EAAE;MAEf,IAAI,CAACjN,kBAAkB,CAAC8P,GAAG,CAAC,KAAK,CAAC;IACpC;IAEAjY,MAAMA,CAAA;MACJ,EAAE,CAACkU,OAAO,CAACC,IAAI,CAACxF,QAAQ,CAACyF,sBAAsB,CAAC,KAAK,CAAC,EAAGC,EAAO,IAAI;QAClEA,EAAE,CAACC,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;QAC5BF,EAAE,CAACC,SAAS,CAACrB,MAAM,CAAC,UAAU,CAAC;MACjC,CAAC,CAAC;MACF,KAAK,IAAIzT,QAAQ,IAAI,IAAI,CAACoB,YAAY,EAAE;QACtC,IAAI,IAAI,CAAC2O,iBAAiB,CAAC/P,QAAQ,KAAKA,QAAQ,CAAC2N,IAAI,EAAE;UACrD,IAAI,CAACuJ,cAAc,CAAC,gBAAgBlX,QAAQ,CAACe,IAAI,EAAE,CAAC;QACtD;MACF;MACA,IAAI,CAAC6V,kBAAkB,CAAC,eAAe,IAAI,CAAC7G,iBAAiB,CAACjQ,EAAE,EAAE,EAAE,uBAAuB,IAAI,CAACiQ,iBAAiB,EAAEjQ,EAAE,EAAE,CAAC;MACxH,IAAI,CAACgW,gBAAgB,CAAC,aAAa,CAAC;MACpC,IAAI,CAAC/T,KAAK,GAAG,IAAI;MACjB,IAAI,CAACiE,gBAAgB,GAAG,IAAI,CAAC5E,YAAY,CAAC,CAAC,CAAC;MAC5C,IAAI,CAACN,cAAc,CAAC,IAAI,CAACkF,gBAAgB,CAAC;MAC1C,IAAI,CAAC4Q,kBAAkB,CAAC,aAAa,IAAI,CAAC/U,aAAa,EAAE,CAAC;MAC1D,IAAI,CAACA,aAAa,GAAG,IAAI;MACzB,IAAI,CAACC,oBAAoB,GAAG,IAAI;MAChC,IAAI,CAACN,WAAW,CAAC,IAAI,CAACK,aAAa,CAAC;MACpC,IAAI,CAAC+U,kBAAkB,CAAC,cAAc,IAAI,CAAChJ,wBAAwB,EAAE,CAAC;MACtE,IAAI,CAACA,wBAAwB,GAAG,GAAG;MACnC,IAAI,CAACgI,QAAQ,EAAE;MACf,IAAI,CAACuC,mBAAmB,EAAE;MAC1B;MACA,IAAI,CAACxL,gBAAgB,GAAG,KAAK;IAC/B;IAMAlF,aAAaA,CAAA;MACX,IAAI,CAAC6B,SAAS,GAAG,IAAI;MACrB,IAAI,CAACJ,SAAS,GAAG,KAAK;MACtB,IAAI,CAACuM,yBAAyB,EAAE;IAClC;IAEA7N,aAAaA,CAAA;MACX,IAAI,CAAC0B,SAAS,GAAG,KAAK;MACtB,IAAI,CAACJ,SAAS,GAAG,IAAI;MACrB,IAAI,CAACuM,yBAAyB,EAAE;IAClC;IAEAvJ,IAAIA,CAAA;MACFwM,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;MACrB,IAAI,CAACla,WAAW,EAAE;MAClB,IAAI,IAAI,CAACA,WAAW,IAAI,CAAC,EAAE;QACzB;MACF;MACA,IAAI,IAAI,CAACA,WAAW,IAAI,CAAC,EAAE;QACzB,IAAI,CAACgI,WAAW,GAAG,IAAI;QAEvB,IAAI,IAAI,CAACmC,IAAI,CAACgQ,KAAK,EAAE;UACnB,IAAIC,SAAS,GAAG,IAAI,CAACjQ,IAAI,CAACkQ,KAAK;UAC/B,IAAI,CAAC/O,gBAAgB,CAACf,KAAK,GAAG6P,SAAS,CAAC7P,KAAK;UAC7C,IAAI,CAACe,gBAAgB,CAACd,KAAK,GAAG4P,SAAS,CAAC5P,KAAK;UAC7C,IAAI,CAACc,gBAAgB,CAACR,KAAK,GAAGsP,SAAS,CAACtP,KAAK;UAC7C,IAAI,CAACQ,gBAAgB,CAACF,IAAI,GAAGgP,SAAS,CAAChP,IAAI;UAC3C,IAAI,CAACE,gBAAgB,CAACkB,OAAO,GAAG4N,SAAS,CAAC5N,OAAO;UACjD,IAAI,CAAClB,gBAAgB,CAACoB,UAAU,GAAG0N,SAAS,CAAClP,MAAM;UACnD,IAAI,CAACI,gBAAgB,CAACqB,YAAY,GAAGyN,SAAS,CAACjP,MAAM;UACrD,IAAI,CAACG,gBAAgB,CAAC/C,GAAG,GAAG6R,SAAS,CAAC7R,GAAG;UACzC,IAAI,CAAC+C,gBAAgB,CAACmB,UAAU,GAAG2N,SAAS,CAAC3N,UAAU;UACvD,IAAI,CAACnB,gBAAgB,CAACD,QAAQ,GAAG+O,SAAS,CAAC/O,QAAQ;UACnD,IAAI,CAACC,gBAAgB,CAACpD,GAAG,GAAGkS,SAAS,CAAClS,GAAG;UACzC,IAAI,CAACoD,gBAAgB,CAACL,KAAK,GAAGmP,SAAS,CAACnP,KAAK;UAC7C,IAAI,CAACK,gBAAgB,CAACV,OAAO,GAAGwP,SAAS,CAACxP,OAAO,CAACsE,IAAI;UACtD;QACF,CAAC,MAAM;UAEL,IAAI,CAACmB,YAAY,CAACiK,mBAAmB,CAAC;YACpCC,QAAQ,EAAE,MAAM;YAChBC,OAAO,EAAE,EAAE;YACXC,MAAM,EAAE;WACT,CAAC;UACF,IAAI,CAACza,WAAW,GAAG,CAAC;UACpB;QACF;MACF;MACA,IAAI,IAAI,CAACA,WAAW,IAAI,CAAC,EAAE;QACzB,IAAI,CAACiR,OAAO,CAACE,MAAM,GAAG,CAAC,IAAI,CAACrE,aAAa,GAAG,IAAI,CAACrG,oBAAoB,CAACC,QAAQ,IAAI,GAAG;QACrF;MACF;MACA,IAAI,IAAI,CAAC1G,WAAW,GAAG,CAAC,EAAE;QACxB,IAAI,CAACA,WAAW,EAAE;MACpB;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACF;IAEA+O,QAAQA,CAAA;MACNkL,MAAM,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;MACrB,IAAI,CAAC/C,QAAQ,EAAE;MACf,IAAI,IAAI,CAACnX,WAAW,GAAG,CAAC,EAAE;QACxB,IAAI,CAACA,WAAW,EAAE;MACpB,CAAC,MAAM;QACL,IAAI,CAACqQ,YAAY,CAACiK,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;MACJ;IACF;IAEAla,MAAMA,CAAA;MACJ,IAAI,CAAC0P,QAAQ,CAACyK,IAAI,EAAE;IACtB;IAEA5O,aAAaA,CAAC6O,cAAmB,EAAEC,kBAAuB;MACxD,IAAI,CAACzK,cAAc,CAACrE,aAAa,CAAC6O,cAAc,EAAEC,kBAAkB,EAAE,IAAI,CAAC;IAC7E;IAEArO,KAAKA,CAAA;MACH,IAAI,CAAC4F,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;IAC5B;IAGAvE,QAAQA,CAAA;MAEN,IAAI,IAAI,CAACuE,MAAM,EAAE;QACf,IAAIiH,SAAS,GAAG,IAAI,CAAC9V,KAAK,GAAG,CAAC,GAAG,IAAI,CAACgO,iBAAiB,CAACjQ,EAAE;QAC1D,IAAIwZ,GAAG,GAAG;UACRC,KAAK,EAAE,EAAE;UACTvZ,QAAQ,EAAE,IAAI,CAACgG,gBAAgB,CAAC2H,IAAI;UACpCyJ,KAAK,EAAE,IAAI,CAACvV,aAAa;UACzB2X,cAAc,EAAE,IAAI,CAAC5L,wBAAwB;UAC7CvK,KAAK,EAAE,IAAI,CAAC2M,aAAa,CAAC3M,KAAK,CAAC2U,QAAQ,EAAE;UAC1CQ,WAAW,EAAE,IAAI,CAACtT,oBAAoB,CAACN,IAAI;UAC3C6U,sBAAsB,EAAE,EAAE;UAC1BhK,WAAW,EAAEoI,SAAS;UACtB6B,OAAO,EAAE;YACPC,WAAW,EAAE,IAAI,CAACpS,mBAAmB;YACrCqS,SAAS,EAAE,IAAI,CAAC7P,gBAAgB,CAACf,KAAK;YACtC6Q,QAAQ,EAAE,IAAI,CAAC9P,gBAAgB,CAACd,KAAK;YACrCM,KAAK,EAAE,IAAI,CAACQ,gBAAgB,CAACR,KAAK;YAClCG,KAAK,EAAE,IAAI,CAACD,iBAAiB,CAAC1I,IAAI,GAAG,GAAG,GAAG,IAAI,CAACgJ,gBAAgB,CAACL,KAAK;YACtEoQ,OAAO,EAAE,IAAI,CAAC/P,gBAAgB,CAACoB,UAAU,GAAG,GAAG,GAAG,IAAI,CAACpB,gBAAgB,CAACqB,YAAY;YACpFvB,IAAI,EAAE,IAAI,CAACE,gBAAgB,CAACF,IAAI;YAChCR,OAAO,EAAE,IAAI,CAACU,gBAAgB,CAACV,OAAO;YACtCS,QAAQ,EAAE,IAAI,CAACC,gBAAgB,CAACD,QAAQ;YACxCmB,OAAO,EAAE,IAAI,CAAClB,gBAAgB,CAACkB,OAAO;YACtCC,UAAU,EAAE,IAAI,CAACnB,gBAAgB,CAACmB,UAAU;YAC5C6O,SAAS,EAAE,IAAI,CAAChQ,gBAAgB,CAAC/C,GAAG;YACpCgT,GAAG,EAAE,IAAI,CAACjQ,gBAAgB,CAACpD;;SAE9B;QACD,IAAI,CAACsT,qBAAqB,CAACX,GAAG,CAAC;MACjC,CAAC,MACI;QACH,IAAI,CAACzI,UAAU,GAAG,IAAI;QACtB,IAAI,CAAC/B,YAAY,CAACiK,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;MACJ;MACA;MAEA;MACA;MACA;MACA;MACA;MACA;MAEA;IACF;IAEAvV,gBAAgBA,CAAA;MACd,IAAI,IAAI,CAACiK,wBAAwB,IAAI,IAAI,EAAE;QACzC,IAAI,CAACA,wBAAwB,GAAG,GAAG;MACrC;MACA,IAAI0L,GAAG,GAAG;QACRY,aAAa,EAAE,IAAI,CAAClU,gBAAgB,CAAC2H,IAAI;QACzCwM,UAAU,EAAE,IAAI,CAACtY,aAAa;QAC9BuY,kBAAkB,EAAE,IAAI,CAACxM;OAC1B;MACD,IAAI,CAACiB,cAAc,CAACwL,YAAY,CAACf,GAAG,CAAC,CAACgB,IAAI,CAAC7c,IAAI,CAAC,CAAC,CAAC,CAAC,CAACyU,SAAS,CAACC,GAAG,IAAG;QAClE,IAAI,CAACrD,YAAY,CAACiK,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;MACJ,CAAC,CAAC;IACJ;IAEAqB,YAAYA,CAAA;MACV,IAAI,CAACtL,MAAM,CAACuL,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;IACrC;IAEM/I,cAAcA,CAAA;MAAA,IAAAgJ,KAAA;MAAA,OAAAC,iBAAA;QAClB,IAAI;UACF,MAAMvI,GAAG,SAASsI,KAAI,CAAC9L,gBAAgB,CAACgM,oBAAoB,CAACF,KAAI,CAACrL,IAAI,CAACtP,EAAE,CAAC,CACvEwa,IAAI,CAAC7c,IAAI,CAAC,CAAC,CAAC,CAAC,CACbmd,SAAS,EAAE,CAAC,CAAC;UAEhB;UACAH,KAAI,CAAC/Z,cAAc,GAAGyR,GAAG,CAACiD,MAAM,CAAEyF,IAAe,IAAKA,IAAI,IAAIA,IAAI,CAAC7a,QAAQ,IAAI6a,IAAI,CAAC7a,QAAQ,CAAC8a,IAAI,EAAE,KAAK,EAAE,CAAC;UAC3GL,KAAI,CAAC/Z,cAAc,GAAG+Z,KAAI,CAACM,uCAAuC,CAACN,KAAI,CAAC/Z,cAAc,CAAC;UACvF;UACA,MAAMsa,mBAAmB,GAA8B,EAAE;UAEzD;UACAP,KAAI,CAAC/Z,cAAc,GAAG+Z,KAAI,CAAC/Z,cAAc,CAAC0U,MAAM,CAAE6F,QAAQ,IAAI;YAC5D,MAAMC,SAAS,GAAGD,QAAQ,CAAC/a,OAAO,EAAEJ,EAAE,IAAI,IAAI;YAE9C,IAAIob,SAAS,KAAK,IAAI,EAAE;cACtB;cACA,IAAID,QAAQ,CAACrW,IAAI,KAAK,MAAM,IAAI,EAAEsW,SAAS,IAAIF,mBAAmB,CAAC,EAAE;gBACnEA,mBAAmB,CAACE,SAAS,CAAC,GAAGD,QAAQ,CAAC,CAAC;cAC7C;cAEA,IACER,KAAI,CAACpK,wBAAwB,IAC7BoK,KAAI,CAACpK,wBAAwB,CAACzL,IAAI,KAAK,OAAO,IAC9C6V,KAAI,CAACpK,wBAAwB,CAACnQ,OAAO,IACrCua,KAAI,CAACpK,wBAAwB,CAACnQ,OAAO,CAACJ,EAAE,KAAKob,SAAS,EACtD;gBACA;gBACAT,KAAI,CAACpK,wBAAwB,GAAG4K,QAAQ;cAC1C;cAEA,OAAOA,QAAQ,CAAC,CAAC;YACnB;YAEA,OAAO,IAAI,CAAC,CAAC;UACf,CAAC,CAAC;UAGF,IAAIR,KAAI,CAAC/Z,cAAc,CAACqE,MAAM,IAAI,CAAC,EAAE;YACnC0V,KAAI,CAAC1Y,KAAK,GAAG,IAAI;YACjB0Y,KAAI,CAACzU,gBAAgB,GAAGyU,KAAI,CAACrZ,YAAY,CAAC,CAAC,CAAC;YAC5CqZ,KAAI,CAAC5Y,aAAa,GAAG,IAAI;YACzB4Y,KAAI,CAAC7M,wBAAwB,GAAG,GAAG;YACnC6M,KAAI,CAAC3Y,oBAAoB,GAAG,IAAI;UAClC,CAAC,MACI;YACH,IAAI2Y,KAAI,CAACpK,wBAAwB,CAACvQ,EAAE,EAAE;cACpC2a,KAAI,CAAC1K,iBAAiB,GAAG0K,KAAI,CAACpK,wBAAwB;YACxD,CAAC,MAAM;cACLoK,KAAI,CAAC1K,iBAAiB,GAAG0K,KAAI,CAAC/Z,cAAc,CAAC,CAAC,CAAC;YACjD;YACA,KAAK,IAAIV,QAAQ,IAAIya,KAAI,CAACrZ,YAAY,EAAE;cACtC,IAAIqZ,KAAI,CAAC1K,iBAAiB,CAAC/P,QAAQ,KAAKA,QAAQ,CAAC2N,IAAI,EAAE;gBACrD8M,KAAI,CAACzD,eAAe,CAAC,gBAAgBhX,QAAQ,CAACe,IAAI,EAAE,CAAC;cACvD;YACF;YACA0Z,KAAI,CAAC1K,iBAAiB,CAAC+H,iBAAiB,GAAG2C,KAAI,CAAC9L,gBAAgB,CAACoJ,0BAA0B,CAAC0C,KAAI,CAAC1K,iBAAiB,CAAC+H,iBAAiB,CAAC;YACrI2C,KAAI,CAAC7M,wBAAwB,GAAG6M,KAAI,CAAC1K,iBAAiB,CAAC+H,iBAAiB,CAAC/S,MAAM,CAACiT,QAAQ,EAAE;YAC1FyC,KAAI,CAAC5Y,aAAa,GAAG4Y,KAAI,CAAC1K,iBAAiB,CAACsH,WAAW;YACvD,IAAIoD,KAAI,CAAC5Y,aAAa,IAAI,KAAK,EAAE;cAC/B4Y,KAAI,CAAC5Y,aAAa,GAAG,IAAI;YAC3B;YACA4Y,KAAI,CAAC3Y,oBAAoB,GAAG2Y,KAAI,CAAC1K,iBAAiB,CAACsH,WAAW;YAC9DoD,KAAI,CAACzU,gBAAgB,GAAGyU,KAAI,CAACrZ,YAAY,CAACgU,MAAM,CAACP,EAAE,IAAIA,EAAE,CAAClH,IAAI,IAAI8M,KAAI,CAAC1K,iBAAiB,CAAC/P,QAAS,CAAC,CAAC,CAAC,CAAC;YACtGya,KAAI,CAACvC,yBAAyB,EAAE;UAClC;UACAuC,KAAI,CAACzF,cAAc,EAAE;UACrByF,KAAI,CAAChF,yBAAyB,EAAE;UAChC,IAAIgF,KAAI,CAAC7M,wBAAwB,IAAI,GAAG,EAAE;YACxC6M,KAAI,CAACrK,OAAO,GAAG;cACb,UAAU,EAAEqK,KAAI,CAACzU,gBAAgB,CAAC2H,IAAI;cACtC,OAAO,EAAE8M,KAAI,CAAC5Y,aAAa;cAC3B,kBAAkB,EAAE4Y,KAAI,CAAC7M;aAC1B;UACH,CAAC,MACI;YACH6M,KAAI,CAACrK,OAAO,GAAG;cACb,UAAU,EAAEqK,KAAI,CAACzU,gBAAgB,CAAC2H,IAAI;cACtC,OAAO,EAAE8M,KAAI,CAAC5Y,aAAa;cAC3B,kBAAkB,EAAE;aACrB;UACH;UACA4Y,KAAI,CAACvV,oBAAoB,GAAGuV,KAAI,CAAC3V,eAAe,CAAC,CAAC,CAAC;UACnD2V,KAAI,CAACjX,eAAe,GAAGiX,KAAI,CAAC3V,eAAe,CAAC,CAAC,CAAC,CAAClB,SAAS;UACxD6W,KAAI,CAACtC,mBAAmB,EAAE;UAC1BsC,KAAI,CAACrC,wBAAwB,EAAE;UAC/BqC,KAAI,CAACzK,aAAa,GAAGyK,KAAI,CAACjX,eAAe,CAAC,CAAC,CAAC;UAC5CiX,KAAI,CAAC7W,SAAS,GAAG6W,KAAI,CAACjX,eAAe,CAAC,CAAC,CAAC,CAAC+U,gBAAgB;UACzDkC,KAAI,CAAC7E,QAAQ,EAAE;QACjB,CAAC,CAAC,OAAOxD,KAAK,EAAE;UACdP,OAAO,CAACO,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;QAC5C;MAAC;IACH;IAEM+I,YAAYA,CAAA;MAAA,IAAAC,MAAA;MAAA,OAAAV,iBAAA;QAChB,OAAO,IAAI/H,OAAO,CAAO,CAACC,OAAO,EAAEC,MAAM,KAAI;UAC3C,IAAI;YACFuI,MAAI,CAAC5X,eAAe,CAACkR,OAAO,CAAC,CAACmG,IAAI,EAAE3X,KAAK,KAAI;cAC3C2X,IAAI,CAACzX,KAAK,GAAGgY,MAAI,CAACjL,WAAW,CAACjN,KAAK,CAAC,CAACE,KAAK;cAC1CyX,IAAI,CAACvX,OAAO,GAAG8X,MAAI,CAACjL,WAAW,CAACjN,KAAK,CAAC,CAACI,OAAO;YAChD,CAAC,CAAC;YACFsP,OAAO,EAAE;UACX,CAAC,CAAC,OAAOR,KAAK,EAAE;YACdS,MAAM,CAACT,KAAK,CAAC;UACf;QACF,CAAC,CAAC;MAAC;IACL;IAEM+F,mBAAmBA,CAAA;MAAA,IAAAkD,MAAA;MAAA,OAAAX,iBAAA;QACvB,IAAI;UACF,MAAMvI,GAAG,SAASkJ,MAAI,CAACxM,cAAc,CAACyM,uBAAuB,CAACD,MAAI,CAACjL,OAAO,CAAC,CAACkK,IAAI,CAAC7c,IAAI,CAAC,CAAC,CAAC,CAAC,CAACmd,SAAS,EAAE;UACrGS,MAAI,CAAClL,WAAW,GAAGgC,GAAY;UAC/B,MAAMkJ,MAAI,CAACF,YAAY,EAAE;UACzBE,MAAI,CAAC9P,aAAa,GAAG8P,MAAI,CAACrL,aAAa,CAAC5M,KAAK;QAC/C,CAAC,CAAC,OAAOgP,KAAK,EAAE;UACdP,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEM,KAAK,CAAC;QAC1C;MAAC;IACH;IAEMgG,wBAAwBA,CAAA;MAAA,IAAAmD,MAAA;MAAA,OAAAb,iBAAA;QAC5B,IAAI;UACF,MAAMvI,GAAG,SAASoJ,MAAI,CAAC1M,cAAc,CAAC2M,qBAAqB,CAACD,MAAI,CAACnL,OAAO,CAAC,CAACkK,IAAI,CAAC7c,IAAI,CAAC,CAAC,CAAC,CAAC,CAACmd,SAAS,EAAE;UACnGW,MAAI,CAACjL,oBAAoB,GAAG6B,GAAY;QAC1C,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdP,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEM,KAAK,CAAC;QAC1C;MAAC;IACH;IAEA/J,iBAAiBA,CAACoT,KAAU;MAC1BhH,UAAU,CAAC,MAAK;QACd,IAAI,CAAChL,iBAAiB,GAAGgS,KAAK,CAAC3C,KAAK;MACtC,CAAC,EAAE,GAAG,CAAC;IACT;IAEAmB,qBAAqBA,CAACyB,IAAS;MAC7B7J,OAAO,CAACC,GAAG,CAAC4J,IAAI,CAAC;MACjB,IAAI,CAACrM,IAAI,CAAC0F,GAAG,CAAC,CAAC,IAAI,CAACR,eAAe,GAAG,IAAI,CAAC1F,cAAc,CAAC8M,uBAAuB,CAACD,IAAI,CAAC,GAAG,IAAI,CAAC7M,cAAc,CAAC+M,kBAAkB,CAACF,IAAI,CAAC,EAAEpB,IAAI,CAC1I9c,SAAS,CAAEqe,KAAU,IAAI;QACvBhK,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE+J,KAAK,CAAC;QAC3B,IAAIC,IAAI,GAAG,IAAI;QACf/d,eAAe,CAAC8d,KAAK,CAACE,SAAS,EAAE3N,gBAAgB,GAAG,MAAM,GAAG,SAAS,CAAC,CAACwD,IAAI,CAAC,UAAUoK,QAAa;UAClGA,QAAQ,CAACC,YAAY,CAAC;YACpBC,SAASA,CAAA;cACPR,IAAI,CAACnC,KAAK,GAAGsC,KAAK,CAACE,SAAS;cAC5BL,IAAI,CAACjC,sBAAsB,GAAGoC,KAAK,CAACpC,sBAAsB;cAC1D;cACAqC,IAAI,CAACK,aAAa,CAACT,IAAI,EAAEG,KAAK,CAAC;YACjC,CAAC;YACDO,OAAOA,CAACC,OAAY;cAClBxK,OAAO,CAACC,GAAG,CAACuK,OAAO,CAAC;YACtB;WACD,CAAC;QACJ,CAAC,CAAC;QACF,OAAOre,EAAE,CAAC0d,IAAI,CAAC;MACjB,CAAC,CAAC,EACFje,IAAI,CAAC,CAAC,CAAC,CACR,CAACyU,SAAS,CAAEC,GAAQ,IAAI,CAAG,CAAC,CAAC,CAAC;IACjC;IAEAgK,aAAaA,CAACG,eAAoB,EAAET,KAAU;MAC5C,IAAI,CAACxM,IAAI,CAAC0F,GAAG,CAAC,CAAC,IAAI,CAACR,eAAe,GAAG,IAAI,CAAC1F,cAAc,CAAC0N,kBAAkB,CAACD,eAAe,CAAC,GAAG,IAAI,CAACzN,cAAc,CAACsN,aAAa,CAACG,eAAe,CAAC,EAAEpK,SAAS,CAAEC,GAAQ,IAAI;QAEzK,IAAI,CAAC1T,WAAW,GAAG,CAAC;QACpB,IAAI,IAAI,CAACkS,oBAAoB,EAAE;UAC7B,MAAM6L,YAAY,GAAG;YACnBvK,GAAG,EAAE,IAAI,CAACvB,kBAAkB;YAAE;YAC9BnH,KAAK,EAAEsS,KAAK,CAACtS,KAAK;YAClBkT,eAAe,EAAEZ,KAAK,CAACa,YAAY,CAAC5D,KAAK,GAAG,GAAG;YAAE;YACjD6D,aAAa,EAAEd,KAAK,CAACpC,sBAAsB;YAC3CmD,SAAS,EAAEpM,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,IAAI,EAAE;YAC5DoM,UAAU,EAAE,CACV;cACEC,GAAG,EAAEjB,KAAK,CAAC/b,EAAE;cACbid,QAAQ,EAAE,CAAC;cACXC,WAAW,EAAEV,eAAe,CAACtc,QAAQ,GAAG,KAAK,GAAGsc,eAAe,CAAClF,KAAK,GAAG,WAAW,GAAGkF,eAAe,CAACjZ,KAAK,GAAG,QAAQ;cACtHuM,MAAM,EAAEiM,KAAK,CAACa,YAAY,CAAC5D,KAAK,GAAG;aACpC;WAEJ;UAED;UACA,IAAI,IAAI,CAACvI,mBAAmB,EAAE;YAC5B,IAAI,CAAC1B,cAAc,CAACoO,mCAAmC,CAAC,IAAI,CAAC1M,mBAAmB,EAC9E,IAAI,CAACG,kBAAkB,CAAC,CAACwB,SAAS,CAAC;cACjChG,IAAI,EAAGiG,GAAsB,IAAI;gBAC/BN,OAAO,CAACC,GAAG,CAACK,GAAG,CAAC;gBAChB,MAAM+K,MAAM,GAAGC,IAAI,CAACC,MAAM,EAAE,CAACpF,QAAQ,CAAC,EAAE,CAAC,CAACqF,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGF,IAAI,CAACC,MAAM,EAAE,CAACpF,QAAQ,CAAC,EAAE,CAAC,CAACqF,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;gBACxG,MAAMb,YAAY,GAClB;kBACEvK,GAAG,EAAE,IAAI,CAACvB,kBAAkB;kBAC5BnH,KAAK,EAAE,IAAI,CAAC6F,IAAI,CAAC7F,KAAK;kBACtBkT,eAAe,EAAEZ,KAAK,CAACa,YAAY,CAAC5D,KAAK,GAAG,GAAG;kBAAE;kBACjD6D,aAAa,EAAEd,KAAK,CAACpC,sBAAsB;kBAC3C1Y,IAAI,EAAEoR,GAAG,CAACmL,cAAc,IAAI,IAAI,CAAC/M,mBAAmB;kBACpDqM,SAAS,EAAEzK,GAAG,CAAC5I,KAAK;kBACpBsT,UAAU,EAAE,CACV;oBACEC,GAAG,EAAEjB,KAAK,CAACpC,sBAAsB;oBACjCsD,QAAQ,EAAE,CAAC;oBACXC,WAAW,EAAEnB,KAAK,CAACpC,sBAAsB;oBACzC7J,MAAM,EAAEiM,KAAK,CAACa,YAAY,CAAC5D,KAAK,GAAG;mBACpC;iBAGJ;gBACD,IAAI,CAACjK,cAAc,CAAC0O,gBAAgB,CAACf,YAAY,CAAC,CAACtK,SAAS,CAAEC,GAAQ,IAAI;kBACxEN,OAAO,CAACC,GAAG,CAACK,GAAG,CAAC;gBAClB,CAAC,CAAC;cACJ,CAAC;cACDC,KAAK,EAAGC,GAAQ,IAAI;gBAClBR,OAAO,CAACC,GAAG,CAACO,GAAG,CAAC;cAClB;aACD,CAAC;UACN,CAAC,MAAM;YACL;YACA,IAAI,CAACxD,cAAc,CAAC0O,gBAAgB,CAACf,YAAY,CAAC,CAACtK,SAAS,CAAEC,GAAQ,IAAI;cACxEN,OAAO,CAACC,GAAG,CAACK,GAAG,CAAC;YAClB,CAAC,CAAC;UACJ;QACF;MAEF,CAAC,CAAC,CAAC;IACL;IAEArG,0BAA0BA,CAAA;MACxB,IAAI,IAAI,CAACwE,oBAAoB,CAACvL,MAAM,KAAK,CAAC,EAAE;QAC1C,OAAO,CAAC;MACV;MACA,MAAMyY,eAAe,GAAG,IAAI,CAAClN,oBAAoB,CAAC,IAAI,CAAChF,kBAAkB,CAAC;MAC1E,MAAMmS,cAAc,GAAG,IAAI,CAACvY,oBAAoB,CAACC,QAAQ,GAAG,GAAG;MAE/D,OAAO,CAACqY,eAAe,EAAEE,UAAU,IAAI,CAAC,IAAID,cAAc;IAC5D;IAEA1R,kBAAkBA,CAAA;MAChB,MAAM4R,UAAU,GAAG,IAAI,CAACpS,aAAa,GAAG,IAAI,CAACrG,oBAAoB,CAACC,QAAQ;MAC1E,OAAOwY,UAAU,GAAG,IAAI,CAAC7R,0BAA0B,EAAE;IACvD;IAEAvF,uBAAuBA,CAACkV,KAAkB;MACxC5J,OAAO,CAACC,GAAG,CAAC2J,KAAK,CAAC;MAClB,IAAI,CAACvW,oBAAoB,GAAGuW,KAAK;MACjC,IAAI,CAAC9S,kBAAkB,CAAC8P,GAAG,CAAC,IAAI,CAAC;IACnC;IAEA9M,mCAAmCA,CAAA;MACjC,IAAI,CAACoD,oBAAoB,CAAC6O,aAAa,CAAC;QACtCC,cAAc,EAAE,uGAAuG;QACvHC,cAAc,EAAE,IAAI;QACpBC,MAAM,EAAE,eAAe;QACvBC,WAAW,EAAE,EAAE;QACfC,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE,aAAa;QAC5BC,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,QAAQ;QACpBC,eAAe,EAAE,KAAK;QACtBC,WAAW,EAAE,wBAAwB;QACrCC,UAAU,EAAE;OACb,CAAC;MACF,IAAI,CAACxP,oBAAoB,CAACyP,IAAI,EAAE;IAClC;IAEA3S,qCAAqCA,CAAA;MACnC,IAAI,CAACkD,oBAAoB,CAAC6O,aAAa,CAAC;QACtCC,cAAc,EAAE;;kEAE4C;QAC5DC,cAAc,EAAE,IAAI;QACpBC,MAAM,EAAE,eAAe;QACvBC,WAAW,EAAE,EAAE;QACfC,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE,aAAa;QAC5BC,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,QAAQ;QACpBC,eAAe,EAAE,KAAK;QACtBC,WAAW,EAAE,wBAAwB;QACrCC,UAAU,EAAE;OACb,CAAC;MACF,IAAI,CAACxP,oBAAoB,CAACyP,IAAI,EAAE;IAClC;IAEQtG,yBAAyBA,CAAA;MAC/B,IAAI,CAACvL,gBAAgB,GAAG,IAAI,CAACoD,iBAAkB,CAAC7P,OAAQ,CAACue,eAAgB;MACzE5M,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC5M,oBAAoB,CAAC;IACxC;IAEA;;;;;IAKQ6V,uCAAuCA,CAACra,cAA2B;MACzE,OAAOA,cAAc,CAAC0U,MAAM,CAAEyC,SAAoB,IAAI;QACpD;QACA,IAAIA,SAAS,CAACjT,IAAI,KAAK,OAAO,EAAE;UAC9B;UACA,MAAM8Z,qBAAqB,GAAGhe,cAAc,CAAC0U,MAAM,CAAEuJ,CAAC,IAAKA,CAAC,CAACze,OAAQ,CAAC0e,SAAS,KAAK/G,SAAS,CAAC3X,OAAQ,CAAC0e,SAAS,CAAC;UAEjH;UACA,IAAIF,qBAAqB,CAAC3Z,MAAM,GAAG,CAAC,EAAE;YACpC,OAAO,KAAK,CAAC,CAAC;UAChB;QACF;QAEA,OAAO,IAAI,CAAC,CAAC;MACf,CAAC,CAAC;IACJ;IAAC,QAAA8Z,CAAA,G;uBA5gCUvQ,mBAAmB,EAAApQ,EAAA,CAAA4gB,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9gB,EAAA,CAAA4gB,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAhhB,EAAA,CAAA4gB,iBAAA,CAAAK,EAAA,CAAAC,QAAA,GAAAlhB,EAAA,CAAA4gB,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAphB,EAAA,CAAA4gB,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAAthB,EAAA,CAAA4gB,iBAAA,CAAAW,EAAA,CAAAC,cAAA,GAAAxhB,EAAA,CAAA4gB,iBAAA,CAAAa,EAAA,CAAAC,YAAA,GAAA1hB,EAAA,CAAA4gB,iBAAA,CAAAe,EAAA,CAAAC,oBAAA,GAAA5hB,EAAA,CAAA4gB,iBAAA,CAAAiB,EAAA,CAAAC,YAAA,GAAA9hB,EAAA,CAAA4gB,iBAAA,CAAAG,EAAA,CAAAgB,MAAA,GAAA/hB,EAAA,CAAA4gB,iBAAA,CAAA5gB,EAAA,CAAAgiB,SAAA,GAAAhiB,EAAA,CAAA4gB,iBAAA,CAqEpB3hB,QAAQ;IAAA;IAAA,QAAAgjB,EAAA,G;YArEP7R,mBAAmB;MAAA8R,SAAA;MAAAC,MAAA;QAAAnf,OAAA;QAAA6E,WAAA;MAAA;MAAAua,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UClC5BziB,EADJ,CAAAC,cAAA,aAAyB,aAEqG;UAwBtHD,EAvBA,CAAAgB,UAAA,IAAA2hB,kCAAA,iBAAiD,IAAAC,kCAAA,iBAgB5B,IAAAC,kCAAA,kBAOmC;UAi+BhE7iB,EADI,CAAAG,YAAA,EAAM,EACJ;;;UAx/BQH,EAAA,CAAAI,SAAA,GAAc;UAAdJ,EAAA,CAAAkB,UAAA,UAAAwhB,GAAA,CAAA1f,OAAA,CAAc;UAgBdhD,EAAA,CAAAI,SAAA,EAAa;UAAbJ,EAAA,CAAAkB,UAAA,SAAAwhB,GAAA,CAAA1f,OAAA,CAAa;UAObhD,EAAA,CAAAI,SAAA,EAAkB;UAAlBJ,EAAA,CAAAkB,UAAA,UAAAwhB,GAAA,CAAA7a,WAAA,CAAkB;;;;;;mBDOlB,CAACnI,WAAW,EAAEC,UAAU;MAAC;IAAA;;SAE1ByQ,mBAAmB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}