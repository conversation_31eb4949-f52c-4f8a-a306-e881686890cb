{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { AuthGuard } from './core/guards/auth.guard';\nimport { TeacherGuard } from './core/guards/teacher.guard';\nimport { UserInfoGuard } from './core/guards/user.guard';\nimport { NotFoundComponent } from './modules/not-found/not-found.component';\nimport { TrialComponent } from './modules/trial/trial.component';\nimport { ProfileGuard } from './core/guards/profile.guard';\nimport { AppLayoutType } from './core/models/general.model';\nimport { LoggedInGuard } from './core/guards/logged-in.guard';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\n// import { LibraryComponent } from './modules/library_old/library.component';\nconst routes = [{\n  path: \"\",\n  redirectTo: \"/dashboard\",\n  pathMatch: \"full\"\n}, {\n  path: \"dashboard\",\n  loadChildren: () => import(\"./modules/dashboard/dashboard.module\").then(m => m.DashboardModule),\n  canActivate: [AuthGuard, TeacherGuard],\n  data: {\n    layout: AppLayoutType.Default,\n    isDashboard: true\n  }\n}, {\n  path: \"classrooms\",\n  loadChildren: () => import(\"./modules/classroom/classroom.module\").then(m => m.ClassroomModule),\n  canActivate: [AuthGuard, TeacherGuard],\n  data: {\n    gradient: \"classrooms-gradient.png\"\n  }\n}, {\n  path: \"auth\",\n  loadChildren: () => import(\"./modules/auth/auth.module\").then(m => m.AuthModule),\n  canActivate: [LoggedInGuard],\n  data: {\n    layout: AppLayoutType.Center,\n    hideLeftMenu: true,\n    isAuth: true\n  }\n}, {\n  path: \"certificates\",\n  loadChildren: () => import(\"./modules/shared-link/shared-link.module\").then(m => m.SharedLinkModule)\n}, {\n  path: \"trials\",\n  component: TrialComponent,\n  canActivate: [AuthGuard, TeacherGuard],\n  data: {\n    gradient: \"dashboard-gradient.png\"\n  }\n},\n// {\n//   path: \"calendar\",\n//   component: CalendarComponent,\n//   canActivate: [AuthGuard, TeacherGuard],\n//   data: { gradient: \"calendar-gradient.png\" },\n// },\n{\n  path: \"calendar\",\n  loadChildren: () => import(\"./modules/calendar/calendar.module\").then(m => m.CalendarModule),\n  canActivate: [AuthGuard],\n  data: {\n    layout: AppLayoutType.Default\n  }\n}, {\n  path: \"teacher\",\n  loadChildren: () => import(\"./modules/teacher-application/teacher-application.module\").then(m => m.TeacherApplicationModule),\n  canActivate: [AuthGuard, TeacherGuard],\n  data: {\n    layout: AppLayoutType.Default,\n    isTeacher: true,\n    hideLeftMenu: true,\n    gradient: \"profile-gradient.png\"\n  }\n}, {\n  path: \"notifications\",\n  loadChildren: () => import(\"./modules/notifications/notifications.module\").then(m => m.NotificationsModule),\n  canActivate: [AuthGuard],\n  data: {\n    layout: AppLayoutType.Default,\n    animation: \"Basic\",\n    hideLeftMenu: true,\n    gradient: \"profile-gradient.png\"\n  }\n}, {\n  path: \"not-found\",\n  component: NotFoundComponent,\n  canActivate: [AuthGuard],\n  data: {\n    gradient: \"profile-gradient.png\"\n  }\n}, {\n  path: \"user-profile\",\n  loadChildren: () => import(\"./modules/user-profile/user-profile.module\").then(m => m.UserProfileModule),\n  canActivate: [AuthGuard],\n  data: {\n    animation: \"Basic\",\n    hideLeftMenu: true\n  }\n}, {\n  path: \"meet\",\n  loadChildren: () => import(\"./modules/jitsi-meet/jitsi-meet.module\").then(m => m.JitsiMeetModule),\n  canActivate: [AuthGuard]\n}, {\n  path: \"chat\",\n  loadChildren: () => import(\"./modules/chat/chat.module\").then(m => m.ChatModule),\n  canActivate: [AuthGuard],\n  data: {\n    layout: AppLayoutType.Default,\n    animation: \"Basic\",\n    hideLeftMenu: true,\n    gradient: \"profile-gradient.png\"\n  }\n}, {\n  path: \"ai-chat\",\n  loadComponent: () => import(\"./modules/dashboard/lingolette-room/lingolette-room.component\").then(m => m.LingoletteRoomComponent),\n  canActivate: [AuthGuard]\n},\n// { path: 'dashboard', component: DashboardComponent, data: { \"isDashboard\": true, \"gradient\": \"dashboard-gradient.png\" } },\n// { path: 'classrooms', loadChildren: () => import('./modules/classroom/classroom.module').then(m => m.ClassroomModule), data: { \"gradient\": \"classrooms-gradient.png\" } },\n// { path: 'auth', loadChildren: () => import('./modules/auth/auth.module').then(m => m.AuthModule), data: { \"hideLeftMenu\": true, \"isAuth\": true } },\n// { path: 'trials', component: TrialComponent, data: { \"gradient\": \"dashboard-gradient.png\" } },\n// { path: 'notes', component: NoteComponent, data: { \"gradient\": \"notes-gradient.png\" } },\n// { path: 'library', component: LibraryComponent, data: { \"gradient\": \"library-gradient.png\" } },\n// { path: 'calendar', component: CalendarComponent, data: { \"gradient\": \"calendar-gradient.png\" } },\n// { path: 'homework', component: HomeworkComponent, data: { \"gradient\": \"homework-gradient.png\" } },\n// { path: 'profile', loadChildren: () => import('./modules/profile/profile.module').then(m => m.ProfileModule), data: { animation: 'Basic', \"isProfile\": true, \"hideLeftMenu\": true, \"gradient\": \"profile-gradient.png\" } },\n// { path: 'teacher', loadChildren: () => import('./modules/teacher-application/teacher-application.module').then(m => m.TeacherApplicationModule), data: { \"isTeacher\": true, \"hideLeftMenu\": true, \"gradient\": \"profile-gradient.png\" } },\n// { path: 'not-found', component: NotFoundComponent, data: { \"gradient\": \"profile-gradient.png\" } },\n{\n  path: \"**\",\n  redirectTo: \"/not-found\",\n  pathMatch: \"full\"\n}];\nexport let AppRoutingModule = /*#__PURE__*/(() => {\n  class AppRoutingModule {\n    static #_ = this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n    static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n    static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [AuthGuard, UserInfoGuard, TeacherGuard, ProfileGuard],\n      imports: [RouterModule.forRoot(routes, {\n        scrollPositionRestoration: 'enabled'\n      }), RouterModule]\n    });\n  }\n  return AppRoutingModule;\n})();", "map": {"version": 3, "names": ["RouterModule", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "UserInfoGuard", "NotFoundComponent", "TrialComponent", "Profile<PERSON><PERSON>", "AppLayoutType", "LoggedInGuard", "routes", "path", "redirectTo", "pathMatch", "loadChildren", "then", "m", "DashboardModule", "canActivate", "data", "layout", "<PERSON><PERSON><PERSON>", "isDashboard", "ClassroomModule", "gradient", "AuthModule", "Center", "hideLeftMenu", "isAuth", "SharedLinkModule", "component", "CalendarModule", "TeacherApplicationModule", "<PERSON><PERSON><PERSON>er", "NotificationsModule", "animation", "UserProfileModule", "JitsiMeetModule", "ChatModule", "loadComponent", "LingoletteRoomComponent", "AppRoutingModule", "_", "_2", "_3", "imports", "forRoot", "scrollPositionRestoration"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { RouterModule, Routes } from '@angular/router';\r\nimport { AuthGuard } from './core/guards/auth.guard';\r\nimport { TeacherGuard } from './core/guards/teacher.guard';\r\nimport { UserInfoGuard } from './core/guards/user.guard';\r\nimport { NotFoundComponent } from './modules/not-found/not-found.component';\r\nimport { TrialComponent } from './modules/trial/trial.component';\r\nimport { ProfileGuard } from './core/guards/profile.guard';\r\nimport { AppLayoutType } from './core/models/general.model';\r\nimport { LoggedInGuard } from './core/guards/logged-in.guard';\r\n// import { LibraryComponent } from './modules/library_old/library.component';\r\n\r\nconst routes: Routes = [\r\n  { path: \"\", redirectTo: \"/dashboard\", pathMatch: \"full\" },\r\n  {\r\n    path: \"dashboard\",\r\n    loadChildren: () =>\r\n      import(\"./modules/dashboard/dashboard.module\").then(\r\n        (m) => m.DashboardModule\r\n      ),\r\n    canActivate: [AuthGuard, TeacherGuard],\r\n    data: { \r\n      layout: AppLayoutType.Default,\r\n      isDashboard: true },\r\n  },\r\n  {\r\n    path: \"classrooms\",\r\n    loadChildren: () =>\r\n      import(\"./modules/classroom/classroom.module\").then(\r\n        (m) => m.ClassroomModule\r\n      ),\r\n    canActivate: [AuthGuard, TeacherGuard],\r\n    data: { gradient: \"classrooms-gradient.png\" },\r\n  },\r\n  {\r\n    path: \"auth\",\r\n    loadChildren: () =>\r\n      import(\"./modules/auth/auth.module\").then((m) => m.AuthModule),\r\n    canActivate: [LoggedInGuard],\r\n    data: { layout: AppLayoutType.Center, hideLeftMenu: true, isAuth: true },\r\n  },\r\n  {\r\n    path: \"certificates\",\r\n    loadChildren: () =>\r\n      import(\"./modules/shared-link/shared-link.module\").then(\r\n        (m) => m.SharedLinkModule\r\n      ),\r\n  },\r\n  {\r\n    path: \"trials\",\r\n    component: TrialComponent,\r\n    canActivate: [AuthGuard, TeacherGuard],\r\n    data: { gradient: \"dashboard-gradient.png\" },\r\n  },\r\n  // {\r\n  //   path: \"calendar\",\r\n  //   component: CalendarComponent,\r\n  //   canActivate: [AuthGuard, TeacherGuard],\r\n  //   data: { gradient: \"calendar-gradient.png\" },\r\n  // },\r\n  \r\n  {\r\n    path: \"calendar\",\r\n    loadChildren: () =>\r\n      import(\"./modules/calendar/calendar.module\").then(\r\n        (m) => m.CalendarModule\r\n      ),\r\n    canActivate: [AuthGuard],\r\n    data: {\r\n      layout: AppLayoutType.Default,\r\n    },\r\n  },\r\n  {\r\n    path: \"teacher\",\r\n    loadChildren: () =>\r\n      import(\"./modules/teacher-application/teacher-application.module\").then(\r\n        (m) => m.TeacherApplicationModule\r\n      ),\r\n    canActivate: [AuthGuard, TeacherGuard],\r\n    data: {\r\n      layout: AppLayoutType.Default,\r\n      isTeacher: true,\r\n      hideLeftMenu: true,\r\n      gradient: \"profile-gradient.png\",\r\n    },\r\n  },\r\n  {\r\n    path: \"notifications\",\r\n    loadChildren: () =>\r\n      import(\"./modules/notifications/notifications.module\").then(\r\n        (m) => m.NotificationsModule\r\n      ),\r\n    canActivate: [AuthGuard],\r\n    data: {\r\n      layout: AppLayoutType.Default,\r\n      animation: \"Basic\",\r\n      hideLeftMenu: true,\r\n      gradient: \"profile-gradient.png\",\r\n    },\r\n  },\r\n  {\r\n    path: \"not-found\",\r\n    component: NotFoundComponent,\r\n    canActivate: [AuthGuard],\r\n    data: { gradient: \"profile-gradient.png\" },\r\n  },\r\n  {\r\n    path: \"user-profile\",\r\n    loadChildren: () =>\r\n      import(\"./modules/user-profile/user-profile.module\").then(\r\n        (m) => m.UserProfileModule\r\n      ),\r\n    canActivate: [AuthGuard],\r\n    data: { animation: \"Basic\", hideLeftMenu: true },\r\n  },\r\n  {\r\n    path: \"meet\",\r\n    loadChildren: () =>\r\n      import(\"./modules/jitsi-meet/jitsi-meet.module\").then(\r\n        (m) => m.JitsiMeetModule\r\n      ),\r\n    canActivate: [AuthGuard],\r\n  },\r\n\r\n  {\r\n    path: \"chat\",\r\n    loadChildren: () =>\r\n      import(\"./modules/chat/chat.module\").then(\r\n        (m) => m.ChatModule\r\n      ),\r\n    canActivate: [AuthGuard],\r\n    data: {\r\n      layout: AppLayoutType.Default,\r\n      animation: \"Basic\",\r\n      hideLeftMenu: true,\r\n      gradient: \"profile-gradient.png\",\r\n    },\r\n  },\r\n  {\r\n    path: \"ai-chat\",\r\n    loadComponent: () =>\r\n      import(\"./modules/dashboard/lingolette-room/lingolette-room.component\").then(\r\n        (m) => m.LingoletteRoomComponent\r\n      ),\r\n    canActivate: [AuthGuard],\r\n  },\r\n  // { path: 'dashboard', component: DashboardComponent, data: { \"isDashboard\": true, \"gradient\": \"dashboard-gradient.png\" } },\r\n  // { path: 'classrooms', loadChildren: () => import('./modules/classroom/classroom.module').then(m => m.ClassroomModule), data: { \"gradient\": \"classrooms-gradient.png\" } },\r\n  // { path: 'auth', loadChildren: () => import('./modules/auth/auth.module').then(m => m.AuthModule), data: { \"hideLeftMenu\": true, \"isAuth\": true } },\r\n  // { path: 'trials', component: TrialComponent, data: { \"gradient\": \"dashboard-gradient.png\" } },\r\n  // { path: 'notes', component: NoteComponent, data: { \"gradient\": \"notes-gradient.png\" } },\r\n  // { path: 'library', component: LibraryComponent, data: { \"gradient\": \"library-gradient.png\" } },\r\n  // { path: 'calendar', component: CalendarComponent, data: { \"gradient\": \"calendar-gradient.png\" } },\r\n  // { path: 'homework', component: HomeworkComponent, data: { \"gradient\": \"homework-gradient.png\" } },\r\n  // { path: 'profile', loadChildren: () => import('./modules/profile/profile.module').then(m => m.ProfileModule), data: { animation: 'Basic', \"isProfile\": true, \"hideLeftMenu\": true, \"gradient\": \"profile-gradient.png\" } },\r\n  // { path: 'teacher', loadChildren: () => import('./modules/teacher-application/teacher-application.module').then(m => m.TeacherApplicationModule), data: { \"isTeacher\": true, \"hideLeftMenu\": true, \"gradient\": \"profile-gradient.png\" } },\r\n  // { path: 'not-found', component: NotFoundComponent, data: { \"gradient\": \"profile-gradient.png\" } },\r\n  { path: \"**\", redirectTo: \"/not-found\", pathMatch: \"full\" },\r\n];\r\n\r\n@NgModule({\r\n  imports: [RouterModule.forRoot(routes, { scrollPositionRestoration: 'enabled' })],\r\n  exports: [RouterModule],\r\n  providers: [AuthGuard, UserInfoGuard, TeacherGuard, ProfileGuard]\r\n})\r\nexport class AppRoutingModule { }"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,SAAS,QAAQ,0BAA0B;AACpD,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,aAAa,QAAQ,0BAA0B;AACxD,SAASC,iBAAiB,QAAQ,yCAAyC;AAC3E,SAASC,cAAc,QAAQ,iCAAiC;AAChE,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,aAAa,QAAQ,6BAA6B;AAC3D,SAASC,aAAa,QAAQ,+BAA+B;;;AAC7D;AAEA,MAAMC,MAAM,GAAW,CACrB;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,YAAY;EAAEC,SAAS,EAAE;AAAM,CAAE,EACzD;EACEF,IAAI,EAAE,WAAW;EACjBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAChDC,CAAC,IAAKA,CAAC,CAACC,eAAe,CACzB;EACHC,WAAW,EAAE,CAAChB,SAAS,EAAEC,YAAY,CAAC;EACtCgB,IAAI,EAAE;IACJC,MAAM,EAAEZ,aAAa,CAACa,OAAO;IAC7BC,WAAW,EAAE;;CAChB,EACD;EACEX,IAAI,EAAE,YAAY;EAClBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAChDC,CAAC,IAAKA,CAAC,CAACO,eAAe,CACzB;EACHL,WAAW,EAAE,CAAChB,SAAS,EAAEC,YAAY,CAAC;EACtCgB,IAAI,EAAE;IAAEK,QAAQ,EAAE;EAAyB;CAC5C,EACD;EACEb,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACS,UAAU,CAAC;EAChEP,WAAW,EAAE,CAACT,aAAa,CAAC;EAC5BU,IAAI,EAAE;IAAEC,MAAM,EAAEZ,aAAa,CAACkB,MAAM;IAAEC,YAAY,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAI;CACvE,EACD;EACEjB,IAAI,EAAE,cAAc;EACpBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CACpDC,CAAC,IAAKA,CAAC,CAACa,gBAAgB;CAE9B,EACD;EACElB,IAAI,EAAE,QAAQ;EACdmB,SAAS,EAAExB,cAAc;EACzBY,WAAW,EAAE,CAAChB,SAAS,EAAEC,YAAY,CAAC;EACtCgB,IAAI,EAAE;IAAEK,QAAQ,EAAE;EAAwB;CAC3C;AACD;AACA;AACA;AACA;AACA;AACA;AAEA;EACEb,IAAI,EAAE,UAAU;EAChBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,oCAAoC,CAAC,CAACC,IAAI,CAC9CC,CAAC,IAAKA,CAAC,CAACe,cAAc,CACxB;EACHb,WAAW,EAAE,CAAChB,SAAS,CAAC;EACxBiB,IAAI,EAAE;IACJC,MAAM,EAAEZ,aAAa,CAACa;;CAEzB,EACD;EACEV,IAAI,EAAE,SAAS;EACfG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0DAA0D,CAAC,CAACC,IAAI,CACpEC,CAAC,IAAKA,CAAC,CAACgB,wBAAwB,CAClC;EACHd,WAAW,EAAE,CAAChB,SAAS,EAAEC,YAAY,CAAC;EACtCgB,IAAI,EAAE;IACJC,MAAM,EAAEZ,aAAa,CAACa,OAAO;IAC7BY,SAAS,EAAE,IAAI;IACfN,YAAY,EAAE,IAAI;IAClBH,QAAQ,EAAE;;CAEb,EACD;EACEb,IAAI,EAAE,eAAe;EACrBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,8CAA8C,CAAC,CAACC,IAAI,CACxDC,CAAC,IAAKA,CAAC,CAACkB,mBAAmB,CAC7B;EACHhB,WAAW,EAAE,CAAChB,SAAS,CAAC;EACxBiB,IAAI,EAAE;IACJC,MAAM,EAAEZ,aAAa,CAACa,OAAO;IAC7Bc,SAAS,EAAE,OAAO;IAClBR,YAAY,EAAE,IAAI;IAClBH,QAAQ,EAAE;;CAEb,EACD;EACEb,IAAI,EAAE,WAAW;EACjBmB,SAAS,EAAEzB,iBAAiB;EAC5Ba,WAAW,EAAE,CAAChB,SAAS,CAAC;EACxBiB,IAAI,EAAE;IAAEK,QAAQ,EAAE;EAAsB;CACzC,EACD;EACEb,IAAI,EAAE,cAAc;EACpBG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,4CAA4C,CAAC,CAACC,IAAI,CACtDC,CAAC,IAAKA,CAAC,CAACoB,iBAAiB,CAC3B;EACHlB,WAAW,EAAE,CAAChB,SAAS,CAAC;EACxBiB,IAAI,EAAE;IAAEgB,SAAS,EAAE,OAAO;IAAER,YAAY,EAAE;EAAI;CAC/C,EACD;EACEhB,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAClDC,CAAC,IAAKA,CAAC,CAACqB,eAAe,CACzB;EACHnB,WAAW,EAAE,CAAChB,SAAS;CACxB,EAED;EACES,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CACtCC,CAAC,IAAKA,CAAC,CAACsB,UAAU,CACpB;EACHpB,WAAW,EAAE,CAAChB,SAAS,CAAC;EACxBiB,IAAI,EAAE;IACJC,MAAM,EAAEZ,aAAa,CAACa,OAAO;IAC7Bc,SAAS,EAAE,OAAO;IAClBR,YAAY,EAAE,IAAI;IAClBH,QAAQ,EAAE;;CAEb,EACD;EACEb,IAAI,EAAE,SAAS;EACf4B,aAAa,EAAEA,CAAA,KACb,MAAM,CAAC,+DAA+D,CAAC,CAACxB,IAAI,CACzEC,CAAC,IAAKA,CAAC,CAACwB,uBAAuB,CACjC;EACHtB,WAAW,EAAE,CAAChB,SAAS;CACxB;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAAES,IAAI,EAAE,IAAI;EAAEC,UAAU,EAAE,YAAY;EAAEC,SAAS,EAAE;AAAM,CAAE,CAC5D;AAOD,WAAa4B,gBAAgB;EAAvB,MAAOA,gBAAgB;IAAA,QAAAC,CAAA,G;uBAAhBD,gBAAgB;IAAA;IAAA,QAAAE,EAAA,G;YAAhBF;IAAgB;IAAA,QAAAG,EAAA,G;iBAFhB,CAAC1C,SAAS,EAAEE,aAAa,EAAED,YAAY,EAAEI,YAAY,CAAC;MAAAsC,OAAA,GAFvD5C,YAAY,CAAC6C,OAAO,CAACpC,MAAM,EAAE;QAAEqC,yBAAyB,EAAE;MAAS,CAAE,CAAC,EACtE9C,YAAY;IAAA;;SAGXwC,gBAAgB;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}