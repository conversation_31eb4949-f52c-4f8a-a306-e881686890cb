{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport * as i0 from \"@angular/core\";\nexport let DndDirective = /*#__PURE__*/(() => {\n  class DndDirective {\n    constructor() {\n      this.fileOver = false;\n      this.fileDropped = new EventEmitter();\n    }\n    // Dragover listener\n    onDragOver(evt) {\n      evt.preventDefault();\n      evt.stopPropagation();\n      this.fileOver = true;\n    }\n    // Dragleave listener\n    onDragLeave(evt) {\n      evt.preventDefault();\n      evt.stopPropagation();\n      this.fileOver = false;\n    }\n    // Drop listener\n    ondrop(evt) {\n      evt.preventDefault();\n      evt.stopPropagation();\n      this.fileOver = false;\n      let files = evt.dataTransfer.files;\n      if (files.length > 0) {\n        this.fileDropped.emit(files);\n      }\n    }\n    static #_ = this.ɵfac = function DndDirective_Factory(t) {\n      return new (t || DndDirective)();\n    };\n    static #_2 = this.ɵdir = /*@__PURE__*/i0.ɵɵdefineDirective({\n      type: DndDirective,\n      selectors: [[\"\", \"appDnd\", \"\"]],\n      hostVars: 2,\n      hostBindings: function DndDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"dragover\", function DndDirective_dragover_HostBindingHandler($event) {\n            return ctx.onDragOver($event);\n          })(\"dragleave\", function DndDirective_dragleave_HostBindingHandler($event) {\n            return ctx.onDragLeave($event);\n          })(\"drop\", function DndDirective_drop_HostBindingHandler($event) {\n            return ctx.ondrop($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"fileover\", ctx.fileOver);\n        }\n      },\n      outputs: {\n        fileDropped: \"fileDropped\"\n      }\n    });\n  }\n  return DndDirective;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}