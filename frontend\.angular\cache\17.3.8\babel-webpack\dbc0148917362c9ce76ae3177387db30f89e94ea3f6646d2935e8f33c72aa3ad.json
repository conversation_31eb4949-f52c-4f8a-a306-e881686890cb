{"ast": null, "code": "import _asyncToGenerator from \"C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { signal } from '@angular/core';\nimport { UserRole } from '../models/user.model';\nimport { environment } from 'src/environments/environment';\nimport { BehaviorSubject, Subject } from 'rxjs';\nimport jwt_decode from 'jwt-decode';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"./toast.service\";\nimport * as i4 from \"./user.service\";\nconst BACKEND_URL = environment.apiUrl + \"/Auth/\";\nconst BACKEND_TEACHER_URL = environment.apiUrl + \"/Teacher/\";\nconst BACKEND_LMS_URL = environment.apiUrl + \"/LMS/\";\nexport class AuthService {\n  constructor(http, router, toastService, userService) {\n    this.http = http;\n    this.router = router;\n    this.toastService = toastService;\n    this.userService = userService;\n    this.isAuthenticated = false;\n    this.isAuthenticatedSignal = signal(false);\n    this.firebaseLogoutEvent = signal(false);\n    this.authStatusListener = new Subject();\n    this.UserRoles = UserRole;\n    this._userId = null; // private property to store userId value\n    this.showRegistrationSuccessfullMessage$ = new BehaviorSubject(false);\n    this.showRegistrationSuccessfullMessage = this.showRegistrationSuccessfullMessage$.asObservable();\n    this.disabledProfileRoutes = false;\n  }\n  get isStudent() {\n    return this.getUserRole() === UserRole.STUDENT;\n  }\n  get isTeacher() {\n    return this.getUserRole() === UserRole.TEACHER;\n  }\n  get teachingLanguage() {\n    return this.getUser().teachingLanguage;\n  }\n  getToken() {\n    return this.token;\n  }\n  getIsAuth() {\n    return this.isAuthenticated;\n  }\n  getUserId() {\n    return this.userId || this._userId; // return userId from private property if it exists, otherwise from local storage\n  }\n  setUserId(userId) {\n    this._userId = userId; // set userId value in private property\n  }\n  getUserRole() {\n    if (this.token) {\n      const tokenInfo = this.getDecodedAccessToken(this.token); // decode token\n      return tokenInfo.role;\n    }\n    return null;\n  }\n  getLoggedInUser() {\n    const authInformation = this.getAuthData();\n    if (authInformation) {\n      let user = JSON.parse(authInformation?.user);\n      // // Mutate timeZone\n      // if (user.timeZone === 'Etc/UTC') {\n      //   user.timeZone = \"Europe/London\";\n      // }\n      return user;\n    }\n    return {};\n  }\n  setAuthStatusListener(status) {\n    this.authStatusListener.next(status);\n  }\n  getAuthStatusListener() {\n    return this.authStatusListener.asObservable();\n  }\n  createUser(email, password, endpoint) {\n    return this.http.post(BACKEND_LMS_URL + endpoint + \"?email=\" + email + \"&pwd=\" + password, {\n      email: email,\n      pwd: password\n    });\n  }\n  confirmUser(userId, code) {\n    this.http.post(BACKEND_LMS_URL + 'ConfirmEmailAccount?userId = ' + userId + '&code=' + code + '', {\n      userId: userId,\n      code: code\n    }).subscribe(res => {\n      if (res) {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Your account has been confirmed.'\n        });\n        this.router.navigateByUrl('/auth', {\n          replaceUrl: true\n        });\n      }\n    });\n  }\n  forgot(email) {\n    this.http.post(BACKEND_LMS_URL + \"sendForgotPassCode?UsernameResetPass=\" + email, {}).subscribe( /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (response) {});\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n  }\n  confirmForgot(email, code, password) {\n    let c = {\n      code: code,\n      email: email,\n      pwd: password,\n      pwdRetype: password\n    };\n    return this.http.post(BACKEND_URL + \"SetPassword\", c);\n  }\n  login(email, password) {\n    const authData = {\n      username: email,\n      password: password\n    };\n    return this.http.post(BACKEND_LMS_URL + \"authenticate\", authData);\n  }\n  getDecodedAccessToken(token) {\n    try {\n      return jwt_decode(token);\n    } catch (Error) {\n      return null;\n    }\n  }\n  setToken(tokenValue) {\n    this.token = tokenValue;\n  }\n  setIsAuthenticated(value) {\n    this.isAuthenticated = value;\n    this.isAuthenticatedSignal.set(value);\n  }\n  setAuthTimer(duration) {\n    this.tokenTimer = setTimeout(() => {\n      this.logout();\n    }, duration * 1000);\n  }\n  setDisabledProfileRoutes(value) {\n    this.disabledProfileRoutes = value;\n  }\n  setShowRegistrationSuccessfullMessage(value) {\n    this.showRegistrationSuccessfullMessage$.next(value);\n  }\n  saveAuthData(token, expirationDate, userId, user, role) {\n    localStorage.setItem(\"token\", token);\n    localStorage.setItem(\"expiration\", expirationDate.toISOString());\n    localStorage.setItem(\"userId\", userId);\n    localStorage.setItem(\"user\", JSON.stringify(user));\n    localStorage.setItem(\"role\", role);\n    if (userId) {\n      this._userId = userId; // set userId value in private property\n    }\n  }\n  autoAuthUser() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const authInformation = _this.getAuthData();\n      if (!authInformation) {\n        return;\n      }\n      const now = new Date();\n      const expiresIn = authInformation.expirationDate.getTime() - now.getTime();\n      if (expiresIn > 0) {\n        _this.token = authInformation.token;\n        const tokenInfo = _this.getDecodedAccessToken(_this.token); // decode token\n        _this.isAuthenticated = true;\n        _this.userId = authInformation.userId;\n        _this.user = JSON.parse(authInformation.user);\n        if (authInformation.userId) {\n          _this._userId = authInformation.userId; // set userId value in private property\n        }\n        _this.setAuthTimer(expiresIn / 1000);\n        _this.authStatusListener.next(true);\n      }\n    })();\n  }\n  getAuthData() {\n    const token = localStorage.getItem(\"token\");\n    const expirationDate = localStorage.getItem(\"expiration\");\n    const userId = localStorage.getItem(\"userId\");\n    const user = localStorage.getItem(\"user\");\n    const teacherStatus = localStorage.getItem(\"teacherStatus\");\n    const role = localStorage.getItem(\"role\");\n    if (!token || !expirationDate) {\n      return;\n    }\n    return {\n      token: token,\n      expirationDate: new Date(expirationDate),\n      userId: userId,\n      user: user,\n      teacherStatus: teacherStatus,\n      role: role\n    };\n  }\n  logout() {\n    this.token = null;\n    this.isAuthenticated = false;\n    this.authStatusListener.next(false);\n    this.userId = null;\n    this.user = null;\n    clearTimeout(this.tokenTimer);\n    this.clearAuthData();\n    this.router.navigate(['/auth/login']);\n  }\n  clearAuthData() {\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"expiration\");\n    localStorage.removeItem(\"userId\");\n    localStorage.removeItem(\"user\");\n    localStorage.removeItem(\"role\");\n    localStorage.removeItem(\"teacherStatus\");\n    this.teacherStatus = undefined;\n    this._userId = null;\n  }\n  isUserInfoFullfileld() {\n    if (this.user?.email === \"\" || this.user?.firstName === \"\" || this.user?.lastName === \"\" || this.user?.skype === \"\" || this.user?.timeZone === \"\" || this.user?.email === \"Not Given\" || this.user?.firstName === \"Not Given\" || this.user?.lastName === \"Not Given\" || this.user?.skype === \"Not Given\" || this.user?.timeZone === \"Not Given\") {\n      return false;\n    }\n    return true;\n  }\n  getTeacherStatus() {\n    return this.http.get(BACKEND_LMS_URL + \"GetTeacherApplicationStatus\");\n  }\n  getTeacherStatusPromise() {\n    return this.http.get(BACKEND_LMS_URL + \"GetTeacherApplicationStatus\").toPromise();\n  }\n  setTeacherStatus(status) {\n    this.teacherStatus = status;\n  }\n  getUserPhoto() {\n    const authInformation = this.getAuthData();\n    if (authInformation) {\n      this.user = JSON.parse(authInformation?.user);\n      let photo = this.user?.photo && this.user?.photo !== 'Not Given' ? environment.apiUrl + \"/\" + this.user?.photo : \"/assets/images/avatar.svg\";\n      return photo;\n    }\n    return \"\";\n  }\n  getUser() {\n    const authInformation = this.getAuthData();\n    this.user = JSON.parse(authInformation?.user);\n    return this.user;\n  }\n  //TODO GOOGLE LOGIN\n  // public signInWithGoogle = ()=> {\n  //   return this._externalAuthService.signIn(GoogleLoginProvider.PROVIDER_ID);\n  // }\n  // public signOutExternal = () => {\n  //   this._externalAuthService.signOut();\n  // }\n  saveLeadDynoAffiliateIdFromRoute(next) {\n    const leadDynoAffiliateId = next.queryParams['afmc'];\n    if (leadDynoAffiliateId !== null && leadDynoAffiliateId !== undefined) {\n      console.log('leadDynoAffiliateId', leadDynoAffiliateId);\n      localStorage.setItem('leadDynoAffiliateId', leadDynoAffiliateId);\n    }\n  }\n  static #_ = this.ɵfac = function AuthService_Factory(t) {\n    return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router), i0.ɵɵinject(i3.ToastService), i0.ɵɵinject(i4.UserService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AuthService,\n    factory: AuthService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["signal", "UserRole", "environment", "BehaviorSubject", "Subject", "jwt_decode", "BACKEND_URL", "apiUrl", "BACKEND_TEACHER_URL", "BACKEND_LMS_URL", "AuthService", "constructor", "http", "router", "toastService", "userService", "isAuthenticated", "isAuthenticatedSignal", "firebaseLogoutEvent", "authStatusListener", "UserRoles", "_userId", "showRegistrationSuccessfullMessage$", "showRegistrationSuccessfullMessage", "asObservable", "disabledProfileRoutes", "isStudent", "getUserRole", "STUDENT", "<PERSON><PERSON><PERSON>er", "TEACHER", "teachingLanguage", "getUser", "getToken", "token", "getIsAuth", "getUserId", "userId", "setUserId", "tokenInfo", "getDecodedAccessToken", "role", "getLoggedInUser", "authInformation", "getAuthData", "user", "JSON", "parse", "setAuthStatusListener", "status", "next", "getAuthStatusListener", "createUser", "email", "password", "endpoint", "post", "pwd", "confirmUser", "code", "subscribe", "res", "setShowToastmessage", "severity", "summary", "detail", "navigateByUrl", "replaceUrl", "forgot", "_ref", "_asyncToGenerator", "response", "_x", "apply", "arguments", "confirmForgot", "c", "pwdRetype", "login", "authData", "username", "Error", "setToken", "tokenValue", "setIsAuthenticated", "value", "set", "setAuthTimer", "duration", "tokenTimer", "setTimeout", "logout", "setDisabledProfileRoutes", "setShowRegistrationSuccessfullMessage", "saveAuthData", "expirationDate", "localStorage", "setItem", "toISOString", "stringify", "autoAuthUser", "_this", "now", "Date", "expiresIn", "getTime", "getItem", "teacher<PERSON><PERSON><PERSON>", "clearTimeout", "clearAuthData", "navigate", "removeItem", "undefined", "isUserInfoFullfileld", "firstName", "lastName", "skype", "timeZone", "getTeacherStatus", "get", "getTeacherStatusPromise", "to<PERSON>romise", "setTeacherStatus", "getUserPhoto", "photo", "saveLeadDynoAffiliateIdFromRoute", "leadDynoAffiliateId", "queryParams", "console", "log", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "i3", "ToastService", "i4", "UserService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable, signal } from '@angular/core';\r\nimport { ActivatedRouteSnapshot, Router } from '@angular/router';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { AuthData, User, UserAvailability, UserAvailabilityOff, UserRole } from '../models/user.model';\r\nimport { environment } from 'src/environments/environment';\r\nimport { BehaviorSubject, forkJoin, Observable, Subject } from 'rxjs';\r\nimport jwt_decode from 'jwt-decode';\r\nimport { Teacher, TeacherStatus, TeacherStatusResponse } from '../models/teacher.model';\r\nimport { map, switchMap } from 'rxjs/operators';\r\nimport { UserService } from './user.service';\r\nimport { ToastService } from './toast.service';\r\nconst BACKEND_URL = environment.apiUrl + \"/Auth/\";\r\nconst BACKEND_TEACHER_URL = environment.apiUrl + \"/Teacher/\";\r\nconst BACKEND_LMS_URL = environment.apiUrl + \"/LMS/\";\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AuthService {\r\n  private isAuthenticated = false;\r\n  isAuthenticatedSignal = signal(false);\r\n  firebaseLogoutEvent = signal(false);\r\n  private token?: string | null;\r\n  private user?: User | null;\r\n  private tokenTimer: any;\r\n  private userId?: string | null;\r\n  public authStatusListener = new Subject<boolean>();\r\n  public teacherStatus?: string;\r\n  UserRoles = UserRole;\r\n  private _userId: string | null = null; // private property to store userId value\r\n\r\n  private showRegistrationSuccessfullMessage$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);\r\n  public showRegistrationSuccessfullMessage: Observable<boolean> = this.showRegistrationSuccessfullMessage$.asObservable();\r\n\r\n  public disabledProfileRoutes = false;\r\n  constructor(\r\n    private http: HttpClient,\r\n    private router: Router,\r\n    private toastService: ToastService,\r\n    private userService: UserService,\r\n    // private _externalAuthService: SocialAuthService\r\n  ) { }\r\n\r\n  get isStudent(): boolean {\r\n    return this.getUserRole() === UserRole.STUDENT;\r\n  }\r\n\r\n  get isTeacher(): boolean {\r\n    return this.getUserRole() === UserRole.TEACHER;\r\n  }\r\n\r\n  get teachingLanguage(): string {\r\n    return this.getUser()!.teachingLanguage!;\r\n  }\r\n\r\n  getToken() {\r\n    return this.token;\r\n  }\r\n\r\n  getIsAuth() {\r\n    return this.isAuthenticated;\r\n  }\r\n\r\n  getUserId() {\r\n    return this.userId || this._userId; // return userId from private property if it exists, otherwise from local storage\r\n  }\r\n\r\n  setUserId(userId: string) {\r\n    this._userId = userId; // set userId value in private property\r\n  }\r\n\r\n  getUserRole() {\r\n    if (this.token) {\r\n      const tokenInfo = this.getDecodedAccessToken(this.token!); // decode token\r\n      return tokenInfo.role\r\n    }\r\n    return null\r\n  }\r\n\r\n  getLoggedInUser() {\r\n    const authInformation = this.getAuthData();\r\n    if (authInformation) {\r\n\r\n      let user = JSON.parse(authInformation?.user!);\r\n      // // Mutate timeZone\r\n      // if (user.timeZone === 'Etc/UTC') {\r\n      //   user.timeZone = \"Europe/London\";\r\n      // }\r\n      return user;\r\n    }\r\n    return {}\r\n  }\r\n\r\n  setAuthStatusListener(status: boolean) {\r\n    this.authStatusListener.next(status);\r\n  }\r\n\r\n  getAuthStatusListener() {\r\n    return this.authStatusListener.asObservable();\r\n  }\r\n\r\n  createUser(email: string, password: string, endpoint: string) {\r\n    return this.http.post(BACKEND_LMS_URL + endpoint + \"?email=\" + email + \"&pwd=\" + password, {\r\n      email: email,\r\n      pwd: password\r\n    });\r\n  }\r\n\r\n  confirmUser(userId: string, code: string) {\r\n    this.http.post(BACKEND_LMS_URL + 'ConfirmEmailAccount?userId = ' + userId + '&code=' + code + '', { userId: userId, code: code })\r\n      .subscribe(res => {\r\n        if (res) {\r\n          this.toastService.setShowToastmessage({\r\n            severity: 'success',\r\n            summary: '',\r\n            detail: 'Your account has been confirmed.'\r\n          });\r\n          this.router.navigateByUrl('/auth', { replaceUrl: true })\r\n        }\r\n      })\r\n  }\r\n\r\n  forgot(email: string) {\r\n    this.http.post<User>(BACKEND_LMS_URL + \"sendForgotPassCode?UsernameResetPass=\" + email, {})\r\n      .subscribe(async response => {\r\n\r\n      })\r\n  }\r\n\r\n  confirmForgot(email: string, code: string, password: string) {\r\n    let c = {\r\n      code: code,\r\n      email: email,\r\n      pwd: password,\r\n      pwdRetype: password\r\n    }\r\n    return this.http.post<User>(BACKEND_URL + \"SetPassword\", c);\r\n  }\r\n\r\n  login(email: string, password: string) {\r\n    const authData: AuthData = {\r\n      username: email,\r\n      password: password\r\n    };\r\n\r\n    return this.http.post<User>(BACKEND_LMS_URL + \"authenticate\", authData)\r\n  }\r\n\r\n  getDecodedAccessToken(token: string): any {\r\n    try {\r\n      return jwt_decode(token);\r\n    } catch (Error) {\r\n      return null;\r\n    }\r\n  }\r\n\r\n  setToken(tokenValue: string | undefined) {\r\n    this.token = tokenValue;\r\n  }\r\n\r\n  setIsAuthenticated(value: boolean) {\r\n    this.isAuthenticated = value;\r\n    this.isAuthenticatedSignal.set(value);\r\n  }\r\n\r\n  setAuthTimer(duration: number) {\r\n    this.tokenTimer = setTimeout(() => {\r\n      this.logout();\r\n    }, duration * 1000)\r\n  }\r\n\r\n  setDisabledProfileRoutes(value: boolean) {\r\n    this.disabledProfileRoutes = value;\r\n  }\r\n\r\n  setShowRegistrationSuccessfullMessage(value: boolean) {\r\n    this.showRegistrationSuccessfullMessage$.next(value);\r\n  }\r\n\r\n  saveAuthData(token: string, expirationDate: Date, userId: string, user: User, role: string) {\r\n    localStorage.setItem(\"token\", token);\r\n    localStorage.setItem(\"expiration\", expirationDate.toISOString());\r\n    localStorage.setItem(\"userId\", userId);\r\n    localStorage.setItem(\"user\", JSON.stringify(user));\r\n    localStorage.setItem(\"role\", role);\r\n    if (userId) {\r\n      this._userId = userId; // set userId value in private property\r\n    }\r\n  }\r\n\r\n  async autoAuthUser() {\r\n\r\n    const authInformation = this.getAuthData();\r\n    if (!authInformation) {\r\n      return;\r\n    }\r\n    const now = new Date();\r\n    const expiresIn = authInformation.expirationDate.getTime() - now.getTime();\r\n    if (expiresIn > 0) {\r\n      this.token = authInformation.token;\r\n      const tokenInfo = this.getDecodedAccessToken(this.token!); // decode token\r\n      this.isAuthenticated = true;\r\n      this.userId = authInformation.userId;\r\n      this.user = JSON.parse(authInformation.user!)\r\n      if (authInformation.userId) {\r\n        this._userId = authInformation.userId; // set userId value in private property\r\n      }\r\n      this.setAuthTimer(expiresIn / 1000);\r\n      this.authStatusListener.next(true);\r\n    }\r\n  }\r\n\r\n  private getAuthData() {\r\n    const token = localStorage.getItem(\"token\");\r\n    const expirationDate = localStorage.getItem(\"expiration\");\r\n    const userId = localStorage.getItem(\"userId\");\r\n    const user = localStorage.getItem(\"user\");\r\n    const teacherStatus = localStorage.getItem(\"teacherStatus\");\r\n    const role = localStorage.getItem(\"role\");\r\n    if (!token || !expirationDate) {\r\n      return;\r\n    }\r\n    return {\r\n      token: token,\r\n      expirationDate: new Date(expirationDate),\r\n      userId: userId,\r\n      user: user,\r\n      teacherStatus: teacherStatus,\r\n      role: role\r\n    }\r\n  }\r\n\r\n  logout() {\r\n    this.token = null;\r\n    this.isAuthenticated = false;\r\n    this.authStatusListener.next(false);\r\n    this.userId = null;\r\n    this.user = null;\r\n    clearTimeout(this.tokenTimer);\r\n    this.clearAuthData();\r\n    this.router.navigate(['/auth/login']);\r\n  }\r\n\r\n  private clearAuthData() {\r\n    localStorage.removeItem(\"token\");\r\n    localStorage.removeItem(\"expiration\");\r\n    localStorage.removeItem(\"userId\");\r\n    localStorage.removeItem(\"user\");\r\n    localStorage.removeItem(\"role\");\r\n    localStorage.removeItem(\"teacherStatus\");\r\n    this.teacherStatus = undefined;\r\n    this._userId = null;\r\n  }\r\n\r\n  public isUserInfoFullfileld() {\r\n    if (\r\n      this.user?.email === \"\" ||\r\n      this.user?.firstName === \"\" ||\r\n      this.user?.lastName === \"\" ||\r\n      this.user?.skype === \"\" ||\r\n      this.user?.timeZone === \"\" ||\r\n      this.user?.email === \"Not Given\" ||\r\n      this.user?.firstName === \"Not Given\" ||\r\n      this.user?.lastName === \"Not Given\" ||\r\n      this.user?.skype === \"Not Given\" ||\r\n      this.user?.timeZone === \"Not Given\"\r\n    ) {\r\n      return false;\r\n    }\r\n    return true;\r\n  }\r\n\r\n  public getTeacherStatus(): Observable<TeacherStatusResponse> {\r\n\r\n    return this.http.get<TeacherStatusResponse>(BACKEND_LMS_URL + \"GetTeacherApplicationStatus\");\r\n  }\r\n\r\n  public getTeacherStatusPromise(): Promise<TeacherStatusResponse | undefined>  {\r\n\r\n    return this.http.get<TeacherStatusResponse>(BACKEND_LMS_URL + \"GetTeacherApplicationStatus\").toPromise();\r\n  }\r\n\r\n  public setTeacherStatus(status: any) {\r\n    this.teacherStatus = status;\r\n  }\r\n\r\n  public getUserPhoto() {\r\n    const authInformation = this.getAuthData();\r\n    if (authInformation) {\r\n      this.user = JSON.parse(authInformation?.user!);\r\n      let photo = (this.user?.photo && this.user?.photo !== 'Not Given') ? environment.apiUrl + \"/\" + this.user?.photo : \"/assets/images/avatar.svg\";\r\n      return photo;\r\n    }\r\n    return \"\"\r\n  }\r\n\r\n  public getUser() {\r\n    const authInformation = this.getAuthData();\r\n    this.user = JSON.parse(authInformation?.user!);\r\n    return this.user;\r\n  }\r\n  //TODO GOOGLE LOGIN\r\n\r\n  // public signInWithGoogle = ()=> {\r\n  //   return this._externalAuthService.signIn(GoogleLoginProvider.PROVIDER_ID);\r\n  // }\r\n  // public signOutExternal = () => {\r\n  //   this._externalAuthService.signOut();\r\n  // }\r\n\r\n  \r\n  saveLeadDynoAffiliateIdFromRoute(next: ActivatedRouteSnapshot) {\r\n    const leadDynoAffiliateId = next.queryParams['afmc'];\r\n\r\n    if (leadDynoAffiliateId !== null && leadDynoAffiliateId !== undefined) {\r\n      console.log('leadDynoAffiliateId', leadDynoAffiliateId);\r\n      localStorage.setItem('leadDynoAffiliateId', leadDynoAffiliateId);\r\n    }\r\n  }\r\n  \r\n}\r\n"], "mappings": ";AAAA,SAAqBA,MAAM,QAAQ,eAAe;AAGlD,SAAgEC,QAAQ,QAAQ,sBAAsB;AACtG,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,eAAe,EAAwBC,OAAO,QAAQ,MAAM;AACrE,OAAOC,UAAU,MAAM,YAAY;;;;;;AAKnC,MAAMC,WAAW,GAAGJ,WAAW,CAACK,MAAM,GAAG,QAAQ;AACjD,MAAMC,mBAAmB,GAAGN,WAAW,CAACK,MAAM,GAAG,WAAW;AAC5D,MAAME,eAAe,GAAGP,WAAW,CAACK,MAAM,GAAG,OAAO;AAKpD,OAAM,MAAOG,WAAW;EAiBtBC,YACUC,IAAgB,EAChBC,MAAc,EACdC,YAA0B,EAC1BC,WAAwB;IAHxB,KAAAH,IAAI,GAAJA,IAAI;IACJ,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IApBb,KAAAC,eAAe,GAAG,KAAK;IAC/B,KAAAC,qBAAqB,GAAGjB,MAAM,CAAC,KAAK,CAAC;IACrC,KAAAkB,mBAAmB,GAAGlB,MAAM,CAAC,KAAK,CAAC;IAK5B,KAAAmB,kBAAkB,GAAG,IAAIf,OAAO,EAAW;IAElD,KAAAgB,SAAS,GAAGnB,QAAQ;IACZ,KAAAoB,OAAO,GAAkB,IAAI,CAAC,CAAC;IAE/B,KAAAC,mCAAmC,GAA6B,IAAInB,eAAe,CAAU,KAAK,CAAC;IACpG,KAAAoB,kCAAkC,GAAwB,IAAI,CAACD,mCAAmC,CAACE,YAAY,EAAE;IAEjH,KAAAC,qBAAqB,GAAG,KAAK;EAOhC;EAEJ,IAAIC,SAASA,CAAA;IACX,OAAO,IAAI,CAACC,WAAW,EAAE,KAAK1B,QAAQ,CAAC2B,OAAO;EAChD;EAEA,IAAIC,SAASA,CAAA;IACX,OAAO,IAAI,CAACF,WAAW,EAAE,KAAK1B,QAAQ,CAAC6B,OAAO;EAChD;EAEA,IAAIC,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAACC,OAAO,EAAG,CAACD,gBAAiB;EAC1C;EAEAE,QAAQA,CAAA;IACN,OAAO,IAAI,CAACC,KAAK;EACnB;EAEAC,SAASA,CAAA;IACP,OAAO,IAAI,CAACnB,eAAe;EAC7B;EAEAoB,SAASA,CAAA;IACP,OAAO,IAAI,CAACC,MAAM,IAAI,IAAI,CAAChB,OAAO,CAAC,CAAC;EACtC;EAEAiB,SAASA,CAACD,MAAc;IACtB,IAAI,CAAChB,OAAO,GAAGgB,MAAM,CAAC,CAAC;EACzB;EAEAV,WAAWA,CAAA;IACT,IAAI,IAAI,CAACO,KAAK,EAAE;MACd,MAAMK,SAAS,GAAG,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAACN,KAAM,CAAC,CAAC,CAAC;MAC3D,OAAOK,SAAS,CAACE,IAAI;IACvB;IACA,OAAO,IAAI;EACb;EAEAC,eAAeA,CAAA;IACb,MAAMC,eAAe,GAAG,IAAI,CAACC,WAAW,EAAE;IAC1C,IAAID,eAAe,EAAE;MAEnB,IAAIE,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACJ,eAAe,EAAEE,IAAK,CAAC;MAC7C;MACA;MACA;MACA;MACA,OAAOA,IAAI;IACb;IACA,OAAO,EAAE;EACX;EAEAG,qBAAqBA,CAACC,MAAe;IACnC,IAAI,CAAC9B,kBAAkB,CAAC+B,IAAI,CAACD,MAAM,CAAC;EACtC;EAEAE,qBAAqBA,CAAA;IACnB,OAAO,IAAI,CAAChC,kBAAkB,CAACK,YAAY,EAAE;EAC/C;EAEA4B,UAAUA,CAACC,KAAa,EAAEC,QAAgB,EAAEC,QAAgB;IAC1D,OAAO,IAAI,CAAC3C,IAAI,CAAC4C,IAAI,CAAC/C,eAAe,GAAG8C,QAAQ,GAAG,SAAS,GAAGF,KAAK,GAAG,OAAO,GAAGC,QAAQ,EAAE;MACzFD,KAAK,EAAEA,KAAK;MACZI,GAAG,EAAEH;KACN,CAAC;EACJ;EAEAI,WAAWA,CAACrB,MAAc,EAAEsB,IAAY;IACtC,IAAI,CAAC/C,IAAI,CAAC4C,IAAI,CAAC/C,eAAe,GAAG,+BAA+B,GAAG4B,MAAM,GAAG,QAAQ,GAAGsB,IAAI,GAAG,EAAE,EAAE;MAAEtB,MAAM,EAAEA,MAAM;MAAEsB,IAAI,EAAEA;IAAI,CAAE,CAAC,CAC9HC,SAAS,CAACC,GAAG,IAAG;MACf,IAAIA,GAAG,EAAE;QACP,IAAI,CAAC/C,YAAY,CAACgD,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAACpD,MAAM,CAACqD,aAAa,CAAC,OAAO,EAAE;UAAEC,UAAU,EAAE;QAAI,CAAE,CAAC;MAC1D;IACF,CAAC,CAAC;EACN;EAEAC,MAAMA,CAACf,KAAa;IAClB,IAAI,CAACzC,IAAI,CAAC4C,IAAI,CAAO/C,eAAe,GAAG,uCAAuC,GAAG4C,KAAK,EAAE,EAAE,CAAC,CACxFO,SAAS;MAAA,IAAAS,IAAA,GAAAC,iBAAA,CAAC,WAAMC,QAAQ,EAAG,CAE5B,CAAC;MAAA,iBAAAC,EAAA;QAAA,OAAAH,IAAA,CAAAI,KAAA,OAAAC,SAAA;MAAA;IAAA,IAAC;EACN;EAEAC,aAAaA,CAACtB,KAAa,EAAEM,IAAY,EAAEL,QAAgB;IACzD,IAAIsB,CAAC,GAAG;MACNjB,IAAI,EAAEA,IAAI;MACVN,KAAK,EAAEA,KAAK;MACZI,GAAG,EAAEH,QAAQ;MACbuB,SAAS,EAAEvB;KACZ;IACD,OAAO,IAAI,CAAC1C,IAAI,CAAC4C,IAAI,CAAOlD,WAAW,GAAG,aAAa,EAAEsE,CAAC,CAAC;EAC7D;EAEAE,KAAKA,CAACzB,KAAa,EAAEC,QAAgB;IACnC,MAAMyB,QAAQ,GAAa;MACzBC,QAAQ,EAAE3B,KAAK;MACfC,QAAQ,EAAEA;KACX;IAED,OAAO,IAAI,CAAC1C,IAAI,CAAC4C,IAAI,CAAO/C,eAAe,GAAG,cAAc,EAAEsE,QAAQ,CAAC;EACzE;EAEAvC,qBAAqBA,CAACN,KAAa;IACjC,IAAI;MACF,OAAO7B,UAAU,CAAC6B,KAAK,CAAC;IAC1B,CAAC,CAAC,OAAO+C,KAAK,EAAE;MACd,OAAO,IAAI;IACb;EACF;EAEAC,QAAQA,CAACC,UAA8B;IACrC,IAAI,CAACjD,KAAK,GAAGiD,UAAU;EACzB;EAEAC,kBAAkBA,CAACC,KAAc;IAC/B,IAAI,CAACrE,eAAe,GAAGqE,KAAK;IAC5B,IAAI,CAACpE,qBAAqB,CAACqE,GAAG,CAACD,KAAK,CAAC;EACvC;EAEAE,YAAYA,CAACC,QAAgB;IAC3B,IAAI,CAACC,UAAU,GAAGC,UAAU,CAAC,MAAK;MAChC,IAAI,CAACC,MAAM,EAAE;IACf,CAAC,EAAEH,QAAQ,GAAG,IAAI,CAAC;EACrB;EAEAI,wBAAwBA,CAACP,KAAc;IACrC,IAAI,CAAC5D,qBAAqB,GAAG4D,KAAK;EACpC;EAEAQ,qCAAqCA,CAACR,KAAc;IAClD,IAAI,CAAC/D,mCAAmC,CAAC4B,IAAI,CAACmC,KAAK,CAAC;EACtD;EAEAS,YAAYA,CAAC5D,KAAa,EAAE6D,cAAoB,EAAE1D,MAAc,EAAEQ,IAAU,EAAEJ,IAAY;IACxFuD,YAAY,CAACC,OAAO,CAAC,OAAO,EAAE/D,KAAK,CAAC;IACpC8D,YAAY,CAACC,OAAO,CAAC,YAAY,EAAEF,cAAc,CAACG,WAAW,EAAE,CAAC;IAChEF,YAAY,CAACC,OAAO,CAAC,QAAQ,EAAE5D,MAAM,CAAC;IACtC2D,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEnD,IAAI,CAACqD,SAAS,CAACtD,IAAI,CAAC,CAAC;IAClDmD,YAAY,CAACC,OAAO,CAAC,MAAM,EAAExD,IAAI,CAAC;IAClC,IAAIJ,MAAM,EAAE;MACV,IAAI,CAAChB,OAAO,GAAGgB,MAAM,CAAC,CAAC;IACzB;EACF;EAEM+D,YAAYA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAA/B,iBAAA;MAEhB,MAAM3B,eAAe,GAAG0D,KAAI,CAACzD,WAAW,EAAE;MAC1C,IAAI,CAACD,eAAe,EAAE;QACpB;MACF;MACA,MAAM2D,GAAG,GAAG,IAAIC,IAAI,EAAE;MACtB,MAAMC,SAAS,GAAG7D,eAAe,CAACoD,cAAc,CAACU,OAAO,EAAE,GAAGH,GAAG,CAACG,OAAO,EAAE;MAC1E,IAAID,SAAS,GAAG,CAAC,EAAE;QACjBH,KAAI,CAACnE,KAAK,GAAGS,eAAe,CAACT,KAAK;QAClC,MAAMK,SAAS,GAAG8D,KAAI,CAAC7D,qBAAqB,CAAC6D,KAAI,CAACnE,KAAM,CAAC,CAAC,CAAC;QAC3DmE,KAAI,CAACrF,eAAe,GAAG,IAAI;QAC3BqF,KAAI,CAAChE,MAAM,GAAGM,eAAe,CAACN,MAAM;QACpCgE,KAAI,CAACxD,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACJ,eAAe,CAACE,IAAK,CAAC;QAC7C,IAAIF,eAAe,CAACN,MAAM,EAAE;UAC1BgE,KAAI,CAAChF,OAAO,GAAGsB,eAAe,CAACN,MAAM,CAAC,CAAC;QACzC;QACAgE,KAAI,CAACd,YAAY,CAACiB,SAAS,GAAG,IAAI,CAAC;QACnCH,KAAI,CAAClF,kBAAkB,CAAC+B,IAAI,CAAC,IAAI,CAAC;MACpC;IAAC;EACH;EAEQN,WAAWA,CAAA;IACjB,MAAMV,KAAK,GAAG8D,YAAY,CAACU,OAAO,CAAC,OAAO,CAAC;IAC3C,MAAMX,cAAc,GAAGC,YAAY,CAACU,OAAO,CAAC,YAAY,CAAC;IACzD,MAAMrE,MAAM,GAAG2D,YAAY,CAACU,OAAO,CAAC,QAAQ,CAAC;IAC7C,MAAM7D,IAAI,GAAGmD,YAAY,CAACU,OAAO,CAAC,MAAM,CAAC;IACzC,MAAMC,aAAa,GAAGX,YAAY,CAACU,OAAO,CAAC,eAAe,CAAC;IAC3D,MAAMjE,IAAI,GAAGuD,YAAY,CAACU,OAAO,CAAC,MAAM,CAAC;IACzC,IAAI,CAACxE,KAAK,IAAI,CAAC6D,cAAc,EAAE;MAC7B;IACF;IACA,OAAO;MACL7D,KAAK,EAAEA,KAAK;MACZ6D,cAAc,EAAE,IAAIQ,IAAI,CAACR,cAAc,CAAC;MACxC1D,MAAM,EAAEA,MAAM;MACdQ,IAAI,EAAEA,IAAI;MACV8D,aAAa,EAAEA,aAAa;MAC5BlE,IAAI,EAAEA;KACP;EACH;EAEAkD,MAAMA,CAAA;IACJ,IAAI,CAACzD,KAAK,GAAG,IAAI;IACjB,IAAI,CAAClB,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACG,kBAAkB,CAAC+B,IAAI,CAAC,KAAK,CAAC;IACnC,IAAI,CAACb,MAAM,GAAG,IAAI;IAClB,IAAI,CAACQ,IAAI,GAAG,IAAI;IAChB+D,YAAY,CAAC,IAAI,CAACnB,UAAU,CAAC;IAC7B,IAAI,CAACoB,aAAa,EAAE;IACpB,IAAI,CAAChG,MAAM,CAACiG,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEQD,aAAaA,CAAA;IACnBb,YAAY,CAACe,UAAU,CAAC,OAAO,CAAC;IAChCf,YAAY,CAACe,UAAU,CAAC,YAAY,CAAC;IACrCf,YAAY,CAACe,UAAU,CAAC,QAAQ,CAAC;IACjCf,YAAY,CAACe,UAAU,CAAC,MAAM,CAAC;IAC/Bf,YAAY,CAACe,UAAU,CAAC,MAAM,CAAC;IAC/Bf,YAAY,CAACe,UAAU,CAAC,eAAe,CAAC;IACxC,IAAI,CAACJ,aAAa,GAAGK,SAAS;IAC9B,IAAI,CAAC3F,OAAO,GAAG,IAAI;EACrB;EAEO4F,oBAAoBA,CAAA;IACzB,IACE,IAAI,CAACpE,IAAI,EAAEQ,KAAK,KAAK,EAAE,IACvB,IAAI,CAACR,IAAI,EAAEqE,SAAS,KAAK,EAAE,IAC3B,IAAI,CAACrE,IAAI,EAAEsE,QAAQ,KAAK,EAAE,IAC1B,IAAI,CAACtE,IAAI,EAAEuE,KAAK,KAAK,EAAE,IACvB,IAAI,CAACvE,IAAI,EAAEwE,QAAQ,KAAK,EAAE,IAC1B,IAAI,CAACxE,IAAI,EAAEQ,KAAK,KAAK,WAAW,IAChC,IAAI,CAACR,IAAI,EAAEqE,SAAS,KAAK,WAAW,IACpC,IAAI,CAACrE,IAAI,EAAEsE,QAAQ,KAAK,WAAW,IACnC,IAAI,CAACtE,IAAI,EAAEuE,KAAK,KAAK,WAAW,IAChC,IAAI,CAACvE,IAAI,EAAEwE,QAAQ,KAAK,WAAW,EACnC;MACA,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb;EAEOC,gBAAgBA,CAAA;IAErB,OAAO,IAAI,CAAC1G,IAAI,CAAC2G,GAAG,CAAwB9G,eAAe,GAAG,6BAA6B,CAAC;EAC9F;EAEO+G,uBAAuBA,CAAA;IAE5B,OAAO,IAAI,CAAC5G,IAAI,CAAC2G,GAAG,CAAwB9G,eAAe,GAAG,6BAA6B,CAAC,CAACgH,SAAS,EAAE;EAC1G;EAEOC,gBAAgBA,CAACzE,MAAW;IACjC,IAAI,CAAC0D,aAAa,GAAG1D,MAAM;EAC7B;EAEO0E,YAAYA,CAAA;IACjB,MAAMhF,eAAe,GAAG,IAAI,CAACC,WAAW,EAAE;IAC1C,IAAID,eAAe,EAAE;MACnB,IAAI,CAACE,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACJ,eAAe,EAAEE,IAAK,CAAC;MAC9C,IAAI+E,KAAK,GAAI,IAAI,CAAC/E,IAAI,EAAE+E,KAAK,IAAI,IAAI,CAAC/E,IAAI,EAAE+E,KAAK,KAAK,WAAW,GAAI1H,WAAW,CAACK,MAAM,GAAG,GAAG,GAAG,IAAI,CAACsC,IAAI,EAAE+E,KAAK,GAAG,2BAA2B;MAC9I,OAAOA,KAAK;IACd;IACA,OAAO,EAAE;EACX;EAEO5F,OAAOA,CAAA;IACZ,MAAMW,eAAe,GAAG,IAAI,CAACC,WAAW,EAAE;IAC1C,IAAI,CAACC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACJ,eAAe,EAAEE,IAAK,CAAC;IAC9C,OAAO,IAAI,CAACA,IAAI;EAClB;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EAGAgF,gCAAgCA,CAAC3E,IAA4B;IAC3D,MAAM4E,mBAAmB,GAAG5E,IAAI,CAAC6E,WAAW,CAAC,MAAM,CAAC;IAEpD,IAAID,mBAAmB,KAAK,IAAI,IAAIA,mBAAmB,KAAKd,SAAS,EAAE;MACrEgB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEH,mBAAmB,CAAC;MACvD9B,YAAY,CAACC,OAAO,CAAC,qBAAqB,EAAE6B,mBAAmB,CAAC;IAClE;EACF;EAAC,QAAAI,CAAA,G;qBA5SUxH,WAAW,EAAAyH,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,YAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAO,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAXnI,WAAW;IAAAoI,OAAA,EAAXpI,WAAW,CAAAqI,IAAA;IAAAC,UAAA,EAFV;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}