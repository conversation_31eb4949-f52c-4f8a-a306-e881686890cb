{"ast": null, "code": "import { take } from 'rxjs/operators';\nimport { LessonStatus } from 'src/app/core/models/lesson.model';\nimport { slideInOut } from 'src/app/helpers/my-animations';\nimport { SubSink } from 'subsink';\nimport { forkJoin } from 'rxjs';\nimport { DialogService } from 'primeng/dynamicdialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nimport * as i2 from \"src/app/core/services/calendar.service\";\nimport * as i3 from \"src/app/core/services/classroom.service\";\nimport * as i4 from \"src/app/core/services/lesson.service\";\nimport * as i5 from \"src/app/core/services/package.service\";\nimport * as i6 from \"src/app/core/services/note.service\";\nimport * as i7 from \"src/app/core/services/user.service\";\nimport * as i8 from \"src/app/core/services/auth.service\";\nimport * as i9 from \"src/app/core/services/rating-and-report.service\";\nimport * as i10 from \"@angular/router\";\nimport * as i11 from \"src/app/core/services/toast.service\";\nimport * as i12 from \"src/app/core/services/library.service\";\nimport * as i13 from \"primeng/api\";\nimport * as i14 from \"src/app/core/services/confirm-dialog.service\";\nimport * as i15 from \"src/app/core/services/student-trial.service\";\nimport * as i16 from \"@angular/common\";\nimport * as i17 from \"../../../../../shared/block-viewer/block-viewer.component\";\nimport * as i18 from \"../../../../../shared/prime/prime/calendar-agenda/calendar-agenda.component\";\nimport * as i19 from \"../../../../../shared/lesson/lesson-schedule-box/lesson-schedule-box.component\";\nimport * as i20 from \"../../../../../shared/lesson/mini-lesson-info-card/mini-lesson-info-card.component\";\nimport * as i21 from \"primeng/chip\";\nimport * as i22 from \"primeng/tooltip\";\nimport * as i23 from \"primeng/scrollpanel\";\nimport * as i24 from \"@angular/forms\";\nimport * as i25 from \"@angular/material/progress-spinner\";\nimport * as i26 from \"../../../packages/packages.component\";\nimport * as i27 from \"../../../packages/packages-progress/packages-progress.component\";\nimport * as i28 from \"../class-info/teacher-rate/teacher-rate.component\";\nimport * as i29 from \"../class-info/components/class-info-student-accordion-item/class-info-student-accordion-item.component\";\nimport * as i30 from \"../../../../note/notes/notes.component\";\nimport * as i31 from \"../../../../library/library/library.component\";\nconst _c0 = [\"arrangeLesson\"];\nconst _c1 = [\"allPackages\"];\nconst ClassDetailsComponent_Defer_22_DepsFn = () => [i30.NotesComponent, i31.LibraryComponent];\nconst _c2 = () => ({\n  width: \"100%\",\n  height: \"300px\"\n});\nconst _c3 = a0 => ({\n  \"disabled-link\": a0\n});\nfunction ClassDetailsComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵelement(1, \"mat-spinner\", 22);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClassDetailsComponent_ng_container_3_div_3_ng_container_2_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ClassDetailsComponent_ng_container_3_div_3_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 28)(2, \"div\", 29);\n    i0.ɵɵtext(3, \"Overview\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ClassDetailsComponent_ng_container_3_div_3_ng_container_2_ng_container_4_Template, 1, 0, \"ng-container\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(3);\n    const balanceTemplate_r2 = i0.ɵɵreference(5);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", balanceTemplate_r2);\n  }\n}\nfunction ClassDetailsComponent_ng_container_3_div_3_ng_container_3_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction ClassDetailsComponent_ng_container_3_div_3_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 28)(2, \"div\", 29);\n    i0.ɵɵtext(3, \"Overview\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ClassDetailsComponent_ng_container_3_div_3_ng_container_3_ng_container_4_Template, 1, 0, \"ng-container\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(3);\n    const balanceTemplate_r2 = i0.ɵɵreference(5);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", balanceTemplate_r2);\n  }\n}\nfunction ClassDetailsComponent_ng_container_3_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26);\n    i0.ɵɵtemplate(2, ClassDetailsComponent_ng_container_3_div_3_ng_container_2_Template, 5, 1, \"ng-container\", 4)(3, ClassDetailsComponent_ng_container_3_div_3_ng_container_3_Template, 5, 1, \"ng-container\", 4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"app-packages-progress\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.role == \"Student\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.role == \"Teacher\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"packages\", ctx_r2.classroomPackages);\n  }\n}\nfunction ClassDetailsComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 23)(2, \"div\", 2);\n    i0.ɵɵtemplate(3, ClassDetailsComponent_ng_container_3_div_3_Template, 5, 3, \"div\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (ctx_r2.classroom == null ? null : ctx_r2.classroom.type) !== \"Trial\");\n  }\n}\nfunction ClassDetailsComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32);\n    i0.ɵɵtext(2, \"Current Balance: \");\n    i0.ɵɵelementStart(3, \"span\")(4, \"p-chip\", 33)(5, \"div\", 34);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.getTotalDurationOfRequestedAndArrangedLessons());\n  }\n}\nfunction ClassDetailsComponent_ng_container_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 36)(2, \"app-class-info-student-accordion-item\", 37)(3, \"div\", 38)(4, \"div\", 39)(5, \"div\", 40)(6, \"img\", 41);\n    i0.ɵɵlistener(\"error\", function ClassDetailsComponent_ng_container_7_ng_container_1_Template_img_error_6_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.generalService.setDefaultUserAvatar($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 42);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelement(9, \"div\", 43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r5 = ctx.$implicit;\n    const i_r6 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"classroom\", ctx_r2.classroom)(\"student\", item_r5)(\"isActive\", i_r6 === 0 ? true : false);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r2.getUserPhoto(item_r5), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", item_r5.firstName, \"\\u2019s Information\");\n  }\n}\nfunction ClassDetailsComponent_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ClassDetailsComponent_ng_container_7_ng_container_1_Template, 10, 5, \"ng-container\", 35);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.classroom.classroomStudents)(\"ngForTrackBy\", ctx_r2.trackByStudentId);\n  }\n}\nfunction ClassDetailsComponent_div_8_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 52);\n    i0.ɵɵelement(2, \"app-mini-lesson-info-card\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const lesson_r7 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"lesson\", lesson_r7)(\"classroom\", ctx_r2.classroom);\n  }\n}\nfunction ClassDetailsComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 44)(1, \"div\", 45)(2, \"div\", 46);\n    i0.ɵɵtext(3, \"Lessons\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"p-scrollPanel\", 47)(5, \"div\", 48)(6, \"div\", 49);\n    i0.ɵɵelement(7, \"app-mini-lesson-info-card\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, ClassDetailsComponent_div_8_ng_container_8_Template, 3, 2, \"ng-container\", 51);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(6, _c2));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"pTooltip\", !ctx_r2.classroomService.hasClassroomPackageHoursLeft(ctx_r2.classroom) ? \"No available hours left in classroom\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"classroom\", ctx_r2.classroom)(\"ngClass\", i0.ɵɵpureFunction1(7, _c3, !ctx_r2.classroomService.hasClassroomPackageHoursLeft(ctx_r2.classroom)));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.classroom.lessons.sort(ctx_r2.lessonService.sortLessonByDate));\n  }\n}\nfunction ClassDetailsComponent_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵlistener(\"click\", function ClassDetailsComponent_div_16_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.showRate = true);\n    });\n    i0.ɵɵtext(1, \" Rate Trial \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClassDetailsComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 56)(2, \"strong\");\n    i0.ɵɵtext(3, \"Trial Rating\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 57);\n    i0.ɵɵlistener(\"click\", function ClassDetailsComponent_div_17_Template_div_click_4_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.showRate = false);\n    });\n    i0.ɵɵtext(5, \"Close\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(6, \"app-teacher-rate\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@slideInOut\", undefined);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"classroom\", ctx_r2.classroom);\n  }\n}\nfunction ClassDetailsComponent_div_19_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 65)(1, \"div\", 66);\n    i0.ɵɵtext(2, \" Let us know why you would like to change your teacher \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"textarea\", 67);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ClassDetailsComponent_div_19_div_6_Template_textarea_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.anotherTeacherText, $event) || (ctx_r2.anotherTeacherText = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 68)(5, \"div\", 69);\n    i0.ɵɵlistener(\"click\", function ClassDetailsComponent_div_19_div_6_Template_div_click_5_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.requestTeacher());\n    });\n    i0.ɵɵtext(6, \" Submit \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(7, \"hr\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.anotherTeacherText);\n  }\n}\nfunction ClassDetailsComponent_div_19_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"div\", 71);\n    i0.ɵɵelement(2, \"img\", 72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 73)(4, \"div\", 74)(5, \"div\");\n    i0.ɵɵtext(6, \" First Name \");\n    i0.ɵɵelement(7, \"br\");\n    i0.ɵɵelementStart(8, \"strong\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 75);\n    i0.ɵɵtext(11, \" Last Name \");\n    i0.ɵɵelement(12, \"br\");\n    i0.ɵɵelementStart(13, \"strong\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 75);\n    i0.ɵɵtext(16, \" Skype \");\n    i0.ɵɵelement(17, \"br\");\n    i0.ɵɵelementStart(18, \"strong\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 75);\n    i0.ɵɵtext(21, \" Email \");\n    i0.ɵɵelement(22, \"br\");\n    i0.ɵɵelementStart(23, \"strong\");\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", ctx_r2.getUserPhoto(ctx_r2.classroom.teacher == null ? null : ctx_r2.classroom.teacher.photo), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.classroom.teacher == null ? null : ctx_r2.classroom.teacher.firstName);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.classroom.teacher == null ? null : ctx_r2.classroom.teacher.lastName);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.classroom.teacher == null ? null : ctx_r2.classroom.teacher.skype);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.classroom.teacher == null ? null : ctx_r2.classroom.teacher.username);\n  }\n}\nfunction ClassDetailsComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60)(2, \"strong\");\n    i0.ɵɵtext(3, \"Teacher Info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 61);\n    i0.ɵɵlistener(\"click\", function ClassDetailsComponent_div_19_Template_div_click_4_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.showRequestAnoterTeacher = true);\n    });\n    i0.ɵɵtext(5, \" Request another teacher \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(6, ClassDetailsComponent_div_19_div_6_Template, 8, 1, \"div\", 62);\n    i0.ɵɵelementStart(7, \"div\", 63)(8, \"div\");\n    i0.ɵɵtemplate(9, ClassDetailsComponent_div_19_div_9_Template, 25, 5, \"div\", 64);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showRequestAnoterTeacher);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.classroom.classroomStudents);\n  }\n}\nfunction ClassDetailsComponent_Defer_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵelement(1, \"app-library\", 77);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"div\", 76);\n    i0.ɵɵelement(3, \"app-notes\", 77);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"selectedClassroom\", ctx_r2.classroom);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"selectedClassroom\", ctx_r2.classroom);\n  }\n}\nfunction ClassDetailsComponent_DeferPlaceholder_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class ClassDetailsComponent {\n  constructor(generalService, calendarService, classroomService, lessonService, packageService, noteService, userService, authService, reportService, router, toastService, libraryService, confirmationService, confirmDialogService, studentTrialService) {\n    this.generalService = generalService;\n    this.calendarService = calendarService;\n    this.classroomService = classroomService;\n    this.lessonService = lessonService;\n    this.packageService = packageService;\n    this.noteService = noteService;\n    this.userService = userService;\n    this.authService = authService;\n    this.reportService = reportService;\n    this.router = router;\n    this.toastService = toastService;\n    this.libraryService = libraryService;\n    this.confirmationService = confirmationService;\n    this.confirmDialogService = confirmDialogService;\n    this.studentTrialService = studentTrialService;\n    this.classroom = {};\n    this.subs = new SubSink();\n    this.classroomPackages = [];\n    this.classroomLessons = [];\n    this.classroomTitle = \"\";\n    this.classroomNotes = [];\n    this.suggestLevelIndex = 0;\n    this.suggestCurrentLevel = [\"A1\", \"A2\", \"B1\", \"B2\", \"C1\", \"C2\"];\n    this.showArrangeLesson = false;\n    this.isLoadingOverlook = false;\n    this.role = \"\";\n    this.showRateBtn = true;\n    this.showRate = false;\n    this.showRequestAnoterTeacher = false;\n    this.showAllPackages = false;\n    this.anotherTeacherText = \"\";\n    this.maxSize = 10;\n    this.directionLinks = true;\n    this.autoHide = false;\n    this.responsive = false;\n    this.config = {\n      id: 'advanced',\n      itemsPerPage: 10,\n      currentPage: 1\n    };\n    this.labels = {\n      previousLabel: 'Previous',\n      nextLabel: 'Next',\n      screenReaderPaginationLabel: 'Pagination',\n      screenReaderPageLabel: 'page',\n      screenReaderCurrentLabel: `You're on page`\n    };\n    this.libraryFiles = [];\n    this.currentAgendaDate = new Date();\n    this.user = {};\n  }\n  ngOnInit() {\n    this.classroom = this.classroomService.getSelectedClassroom();\n    console.log(this.classroom);\n    this.loadLibraryData(+this.classroom.id);\n    this.classroomTitle = this.classroomService.getClassroomTitle(this.classroom);\n    for (let i = 0; i < this.suggestCurrentLevel.length; i++) {\n      if (this.suggestCurrentLevel[i] == this.classroom.activeLevel) {\n        this.suggestLevelIndex = i;\n      }\n    }\n    this.getClassroomPackages();\n    // this.showRateBtn = !!this.classroom.lessons?.find(el => el.status == LessonStatus.COMPLETED) // TODO UNCOMMENT\n    this.role = this.authService.getLoggedInUser().role;\n    this.suggestLevelIndex = this.classroomService.getLevelIndex(this.classroom.activeLevel);\n    this.initUpdateListener();\n    this.initPackagesUpdateListener();\n    this.initConfirmDialogListener();\n    this.user = this.authService.getLoggedInUser();\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  ngOnChanges() {}\n  ngAfterContentInit() {\n    // this.getLessons()\n    // this.subs.sink = this.lessonService.updateListener.subscribe(res => {\n    //   if (res) {\n    //     this.getLessons()\n    //   }\n    // })\n    // this.subs.sink = this.lessonService.addListener.subscribe(res => {\n    //   if (res) {\n    //     this.getLessons()\n    //   }\n    // })\n  }\n  ngAfterViewInit() {}\n  trackById(index, item) {\n    return +item.id;\n  }\n  trackByStudentId(index, item) {\n    return item.aspUserId;\n  }\n  changeLevel() {\n    this.subs.add(this.classroomService.changeTrialLevel(this.classroom.id, this.suggestCurrentLevel[this.suggestLevelIndex]).pipe(take(1)).subscribe(res => {\n      this.toastService.setShowToastmessage({\n        severity: 'success',\n        summary: '',\n        detail: 'Please check your email to reset your password.'\n      });\n    }));\n  }\n  onPageChange(number) {\n    this.config.currentPage = number;\n  }\n  requestTeacher() {\n    let request = {\n      reason: this.anotherTeacherText,\n      studentId: this.authService.getLoggedInUser().id,\n      teacherId: this.classroom.teacher?.aspUserId\n    };\n    this.subs.add(this.reportService.submitRequestTeacher(request).subscribe(res => {\n      this.toastService.setShowToastmessage({\n        severity: 'success',\n        summary: '',\n        detail: 'Please check your email to reset your password.'\n      });\n    }));\n  }\n  getClassroomPackages() {\n    this.subs.add(this.packageService.getClassroomPackages(this.classroom.id).subscribe(res => {\n      // this.classroomPackages = res\n      console.log(res);\n      this.classroomPackages = res;\n    }));\n  }\n  getLessons() {\n    // this.classroomLessons = this.classroom.lessons!\n    // this.isLoadingOverlook = true;\n    // // if (this.classroomService.sessionUserClassrooms.length > 0) {\n    // //   this.classroomLessons = this.classroomService.sessionUserClassrooms.filter(el => el.id == this.classroom.id)[0].lessons!\n    // //   this.isLoadingOverlook = false;\n    // // }\n    // this.lessonService.getClassroomLessons(this.classroom.id).pipe(take(1)).subscribe(res => {\n    //   this.isLoadingOverlook = false\n    //   this.classroomLessons = res;\n    // });\n  }\n  onShowArrangeLesson() {\n    this.classroomService.classroomToArrangeLesson = this.classroom;\n    this.router.navigate(['/reschedule']);\n  }\n  toggleSection(sectionElement, sectionArrowImgSrc) {\n    this.generalService.toggleSection(sectionElement, sectionArrowImgSrc, true);\n  }\n  getLessonColor(status) {\n    return this.calendarService.getEventGradientColor(status);\n  }\n  pop(event) {}\n  // getClassroomNotes(): Note[] {\n  //   return this.classroomService.getClassroomNotes();\n  // }\n  suggestPrevious() {\n    if (this.suggestLevelIndex > 0) {\n      this.suggestLevelIndex--;\n    }\n  }\n  suggestNext() {\n    if (this.suggestLevelIndex < 5) {\n      this.suggestLevelIndex++;\n    }\n  }\n  onDeleteLesson(event) {\n    this.classroomLessons = this.classroomLessons.filter(el => el.id !== event.lesson.id);\n  }\n  getUserPhoto(user) {\n    return this.userService.getUserPhoto(user);\n  }\n  viewPackages() {\n    this.showAllPackages = !this.showAllPackages;\n    this.generalService.slideNativeElements(this.showAllPackages, this.allPackages.nativeElement);\n  }\n  loadLibraryData(classroomId) {\n    const requests = [this.libraryService.getClassroomFiles(classroomId)];\n    this.subs.add(forkJoin(requests).subscribe(res => {\n      this.libraryFiles = res;\n    }));\n  }\n  /**\n   * Calculates the total duration of requested and arranged lessons.\n   * @returns A string representing the total duration in hours.\n   */\n  getTotalDurationOfRequestedAndArrangedLessons() {\n    /**\n     * The total duration of requested and arranged lessons.\n     * @type {number}\n     */\n    const totalDuration = this.classroom.lessons.reduce((duration, lesson) => {\n      if (lesson.status.toLowerCase() === LessonStatus.ARRANGED.toLowerCase() || lesson.status.toLowerCase() === LessonStatus.REQUESTED.toLowerCase()) {\n        return duration + lesson.duration;\n      }\n      return duration;\n    }, 0);\n    /**\n     * The accumulated hours left in the classroom packages.\n     * @type {number}\n     */\n    const accumulatedHoursLeft = this.classroomService.getAccumulatedHoursLeft(this.classroom.packages);\n    /**\n     * The result of adding the accumulated hours left and the total duration.\n     * @type {number}\n     */\n    const result = accumulatedHoursLeft + totalDuration;\n    // Return the result as a formatted string\n    return this.generalService.convertHoursToMinutesWithSuffix(result, true);\n  }\n  initPackagesUpdateListener() {\n    this.subs.add(this.classroomService.classroomPackagesUpdateListener.subscribe(res => {\n      if (res) {\n        this.getClassroomPackages();\n      }\n    }));\n  }\n  initUpdateListener() {\n    this.subs.add(this.classroomService.classroomUpdateListener.subscribe(res => {\n      if (res) {\n        this.classroom = this.classroomService.getSelectedClassroom();\n      }\n    }));\n  }\n  freezePackageSelected() {\n    this.showFreezeDialog();\n  }\n  /**\n   * Function to display a confirmation dialog for canceling a lesson.\n   */\n  showFreezeDialog() {\n    // Set properties for the confirmation dialog\n    this.confirmDialogService.setProperties({\n      confirmMessage: 'Are you sure you would like to freeze your package?',\n      acceptBtnLabel: 'Yes',\n      rejectBtnLabel: 'No',\n      action: 'freeze-package',\n      confirmIcon: '/assets/icons/clock-dialog-icon.svg',\n      showHeader: true,\n      headerText: 'Freeze package request',\n      dialogType: 'custom',\n      headerClass: 'cd-dialog-btn-accept'\n    });\n    // Show the confirmation dialog\n    this.confirmDialogService.show();\n  }\n  prepareDataBeforeSubmit() {\n    const studentType = 'Old Student';\n    const formattedData = {\n      userData: {\n        firstName: this.user.firstName,\n        lastName: this.user.lastName,\n        email: this.user.email,\n        phone: `${this.user.phone}`\n      },\n      course: this.classroom.language + ' ' + this.classroom.activeLevel,\n      studentType: studentType,\n      message: 'Freeze Package'\n    };\n    return formattedData;\n  }\n  initConfirmDialogListener() {\n    this.subs.add(this.confirmDialogService.acceptSelected$.subscribe(res => {\n      this.confirmDialogService.hide();\n      console.log(res);\n      switch (res.action) {\n        case 'freeze-package':\n          this.subs.sink = this.studentTrialService.postContactUs(this.prepareDataBeforeSubmit()).subscribe(res => {\n            if (res) {\n              // this.showSuccessMessage();\n              // this.form.reset();\n            }\n          });\n          break;\n      }\n    }));\n  }\n  static #_ = this.ɵfac = function ClassDetailsComponent_Factory(t) {\n    return new (t || ClassDetailsComponent)(i0.ɵɵdirectiveInject(i1.GeneralService), i0.ɵɵdirectiveInject(i2.CalendarService), i0.ɵɵdirectiveInject(i3.ClassroomService), i0.ɵɵdirectiveInject(i4.LessonService), i0.ɵɵdirectiveInject(i5.PackageService), i0.ɵɵdirectiveInject(i6.NoteService), i0.ɵɵdirectiveInject(i7.UserService), i0.ɵɵdirectiveInject(i8.AuthService), i0.ɵɵdirectiveInject(i9.RatingAndReportService), i0.ɵɵdirectiveInject(i10.Router), i0.ɵɵdirectiveInject(i11.ToastService), i0.ɵɵdirectiveInject(i12.LibraryService), i0.ɵɵdirectiveInject(i13.ConfirmationService), i0.ɵɵdirectiveInject(i14.ConfirmDialogService), i0.ɵɵdirectiveInject(i15.StudentTrialService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ClassDetailsComponent,\n    selectors: [[\"app-class-details\"]],\n    viewQuery: function ClassDetailsComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.arrangeLesson = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.allPackages = _t.first);\n      }\n    },\n    inputs: {\n      classroom: \"classroom\",\n      classroomLessons: \"classroomLessons\"\n    },\n    features: [i0.ɵɵProvidersFeature([DialogService]), i0.ɵɵNgOnChangesFeature],\n    decls: 29,\n    vars: 11,\n    consts: [[\"balanceTemplate\", \"\"], [\"allPackages\", \"\"], [1, \"p-1\", \"sm:p-3\"], [\"class\", \"spinner\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"section-content\"], [\"class\", \"block-gradient border-round-xl  p-3\", 4, \"ngIf\"], [1, \"grid\", \"mt-4\"], [1, \"col-12\", \"md:col-6\", \"h-full\"], [1, \"gradient-block\"], [3, \"lessons\"], [1, \"col-12\", \"md:col-6\"], [\"header\", \" \", \"headerBackgroundImage\", \"/assets/images/dashboard/gradient-sm-1.png\", \"blockClass\", \"relative card\", \"containerClass\", \"\", 3, \"containerHeight\"], [3, \"classroom\", \"lessons\"], [\"class\", \"light-purple-button rate-btn\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"trial-rate\", 4, \"ngIf\"], [2, \"display\", \"flex\"], [\"class\", \" section-trial\", 4, \"ngIf\"], [1, \"modal\", \"no-visibility\", \"p-0\"], [1, \"popup-title\", \"p-20\"], [\"src\", \"/assets/icons/close.png\", 1, \"close-img\", \"close-img-abs\", \"hvr-glow\", 3, \"click\"], [1, \"spinner\"], [2, \"margin-top\", \"200px\"], [1, \"block-gradient\", \"border-round-xl\"], [\"class\", \"\", 4, \"ngIf\"], [1, \"\"], [1, \"section-overlook-title\", \"flex\", \"justify-content-between\"], [3, \"packages\"], [1, \"sm:flex\", \"justify-content-between\", \"w-full\"], [1, \"font-3xl\", \"mt-2\", \"sm:mt-0\"], [4, \"ngTemplateOutlet\"], [1, \"flex\", \"sm:mt-0\", \"gap-2\", \"justify-content-between\", \"flex-column\", \"align-items-end\"], [1, \"font-xl\", \"flex\", \"mt-3\", \"sm:mt-0\", \"gap-1\", \"justify-content-between\"], [1, \"info\", \"bg-blue-400\", \"border-round-xl\"], [1, \"content\", \"text-white\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"block-gradient\", \"border-round-xl\", \"h-full\"], [3, \"classroom\", \"student\", \"isActive\"], [\"title\", \"\", 1, \"w-full\"], [1, \"grid\", \"align-items-center\", \"justify-content-between\", \"font-lg\", \"font-bold\", \"align-items-end\"], [1, \"col-12\", \"flex\", \"align-items-center\"], [1, \"mr-2\", \"h-3rem\", \"border-circle\", 3, \"error\", \"src\"], [1, \"text-primary\"], [\"content\", \"\", 1, \"p-4\"], [1, \"block-gradient\", \"border-round-xl\", \"p-3\"], [1, \"mb-3\", \"section-overlook-title\", \"flex\", \"justify-content-between\"], [1, \"font-3xl\"], [\"styleClass\", \"custombar1\"], [1, \"grid\", \"mr-1\"], [\"tooltipPosition\", \"top\", 1, \"col-6\", \"sm:col-3\", \"md:col-3\", \"col-lesson\", 3, \"pTooltip\"], [\"cardType\", \"request\", 1, \"pointer\", \"hover:opacity-60\", 3, \"classroom\", \"ngClass\"], [4, \"ngFor\", \"ngForOf\"], [1, \"col-6\", \"sm:col-3\", \"md:col-3\", \"col-lesson\"], [\"cardType\", \"lesson\", 3, \"lesson\", \"classroom\"], [1, \"light-purple-button\", \"rate-btn\", 3, \"click\"], [1, \"trial-rate\"], [1, \"section-lessons-title\", 2, \"margin\", \"15px\"], [1, \"main-color-button\", 3, \"click\"], [\"mode\", \"inTrial\", 3, \"classroom\"], [1, \"section-trial\"], [1, \"section-lessons-title\"], [1, \"white-button\", 3, \"click\"], [\"class\", \"requestTeacher\", 4, \"ngIf\"], [1, \"students\"], [\"class\", \"student-info\", 4, \"ngFor\", \"ngForOf\"], [1, \"requestTeacher\"], [2, \"margin\", \"10px 0\"], [\"rows\", \"3\", 1, \"input-element\", 2, \"box-sizing\", \"border-box\", \"width\", \"100%\", 3, \"ngModelChange\", \"ngModel\"], [1, \"btns\"], [1, \"light-purple-button\", 3, \"click\"], [1, \"student-info\"], [1, \"teacher-info-img\"], [3, \"src\"], [1, \"student-info-details\"], [1, \"student-info-details-first\"], [1, \"student-info-details-row\"], [1, \"classroom-cards-space\"], [3, \"selectedClassroom\"]],\n    template: function ClassDetailsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 2);\n        i0.ɵɵtemplate(1, ClassDetailsComponent_div_1_Template, 2, 0, \"div\", 3);\n        i0.ɵɵelementStart(2, \"div\");\n        i0.ɵɵtemplate(3, ClassDetailsComponent_ng_container_3_Template, 4, 1, \"ng-container\", 4)(4, ClassDetailsComponent_ng_template_4_Template, 7, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementStart(6, \"div\", 5);\n        i0.ɵɵtemplate(7, ClassDetailsComponent_ng_container_7_Template, 2, 2, \"ng-container\", 4)(8, ClassDetailsComponent_div_8_Template, 9, 9, \"div\", 6);\n        i0.ɵɵelementStart(9, \"div\", 7)(10, \"div\", 8)(11, \"div\", 9);\n        i0.ɵɵelement(12, \"app-calendar-agenda\", 10);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(13, \"div\", 11)(14, \"app-block-viewer\", 12);\n        i0.ɵɵelement(15, \"app-lesson-schedule-box\", 13);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(16, ClassDetailsComponent_div_16_Template, 2, 0, \"div\", 14)(17, ClassDetailsComponent_div_17_Template, 7, 2, \"div\", 15);\n        i0.ɵɵelementStart(18, \"div\", 16);\n        i0.ɵɵtemplate(19, ClassDetailsComponent_div_19_Template, 10, 2, \"div\", 17);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(20, ClassDetailsComponent_Defer_20_Template, 4, 2)(21, ClassDetailsComponent_DeferPlaceholder_21_Template, 2, 0);\n        i0.ɵɵdefer(22, 20, ClassDetailsComponent_Defer_22_DepsFn, null, 21);\n        i0.ɵɵdeferOnViewport(0, -1);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"div\", 18, 1)(26, \"div\", 19)(27, \"img\", 20);\n        i0.ɵɵlistener(\"click\", function ClassDetailsComponent_Template_img_click_27_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.viewPackages());\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(28, \"app-packages\");\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoadingOverlook);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", (ctx.classroom == null ? null : ctx.classroom.type) !== \"Trial\" && ctx.classroom);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", (ctx.classroom == null ? null : ctx.classroom.type) === \"Trial\" && ctx.classroom && ctx.role == \"Teacher\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", (ctx.classroom == null ? null : ctx.classroom.type) !== \"Trial\" && ctx.classroom);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"lessons\", ctx.classroom.lessons);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"containerHeight\", 480);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"classroom\", ctx.classroom)(\"lessons\", ctx.classroom.lessons);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", (ctx.classroom == null ? null : ctx.classroom.type) === \"Trial\" && ctx.role == \"Student\" && ctx.showRateBtn);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showRate);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", (ctx.classroom == null ? null : ctx.classroom.type) === \"Trial\" && ctx.role == \"Student\");\n      }\n    },\n    dependencies: [i16.NgClass, i16.NgForOf, i16.NgIf, i16.NgTemplateOutlet, i17.BlockViewerComponent, i18.CalendarAgendaComponent, i19.LessonScheduleBoxComponent, i20.MiniLessonInfoCardComponent, i21.Chip, i22.Tooltip, i23.ScrollPanel, i24.DefaultValueAccessor, i24.NgControlStatus, i24.NgModel, i25.MatProgressSpinner, i26.PackagesComponent, i27.PackagesProgressComponent, i28.TeacherRateComponent, i29.ClassInfoStudentAccordionItemComponent],\n    styles: [\".section[_ngcontent-%COMP%] {\\n  background-color: var(--white);\\n  border-top-left-radius: 28px;\\n  border-top-right-radius: 28px;\\n  border-bottom-left-radius: 28px;\\n  border-bottom-right-radius: 28px;\\n  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);\\n  margin-top: 30px;\\n  margin-bottom: 30px;\\n}\\n.section[_ngcontent-%COMP%]   .section-content[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  transition: height 0.3s ease-out;\\n}\\n\\n.responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  width: 100%;\\n  font-size: 25px;\\n  text-align: center;\\n  padding: 15px 0;\\n  box-sizing: border-box;\\n  border-radius: 28px;\\n  position: relative;\\n}\\n.section-title.gradient-bg[_ngcontent-%COMP%] {\\n  background: url(\\\"/assets/images/lessons-gradient-box-bg.png\\\") no-repeat center center !important;\\n  border-radius: 20px !important;\\n}\\n.section-title[_ngcontent-%COMP%]   .toggle-section[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 9px;\\n  left: 10px;\\n}\\n.section-title[_ngcontent-%COMP%]   .toggle-section[_ngcontent-%COMP%]   .section-arrow[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n  position: absolute;\\n  overflow: hidden;\\n  transition-duration: 0.2s;\\n  transition-property: transform;\\n  cursor: pointer;\\n}\\n\\n.section-overlook[_ngcontent-%COMP%] {\\n  border-radius: 10px;\\n  border: 3px solid #f4f4f8;\\n  margin: 15px;\\n  padding: 15px;\\n}\\n.section-overlook[_ngcontent-%COMP%]   .section-overlook-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  font-size: 20px;\\n}\\n\\n.col--1of5[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 20px;\\n  position: relative;\\n}\\n\\n.section-lessons[_ngcontent-%COMP%] {\\n  border-radius: 10px;\\n  border: 3px solid #f4f4f8;\\n  margin: 15px;\\n  padding: 15px;\\n}\\n.section-lessons[_ngcontent-%COMP%]   .the-lessons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  position: relative;\\n}\\n.section-lessons[_ngcontent-%COMP%]   .the-lessons[_ngcontent-%COMP%]   .arrange-lesson[_ngcontent-%COMP%] {\\n  border: 5px solid var(--main-color);\\n}\\n\\n.lesson[_ngcontent-%COMP%] {\\n  margin: 13px;\\n  cursor: pointer;\\n  border-radius: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  position: relative;\\n  width: 140px;\\n  height: 140px;\\n  box-sizing: border-box;\\n  z-index: 2;\\n  background: white;\\n  font-weight: bold;\\n}\\n.lesson[_ngcontent-%COMP%]   .copy-lesson[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 140px;\\n  height: 140px;\\n  border-radius: 20px;\\n  z-index: -1;\\n}\\n.lesson[_ngcontent-%COMP%]   .copy-lesson-2[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 130px;\\n  height: 130px;\\n  border-radius: 16px;\\n  z-index: -1;\\n  background-color: white;\\n}\\n.lesson[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  border-radius: 18px;\\n  background-color: var(--main-color);\\n  font-size: 15px;\\n  width: 100%;\\n  text-align: center;\\n  color: white;\\n  filter: drop-shadow(0px 3px 6px rgba(0, 0, 0, 0.16));\\n  margin-top: -1px;\\n  height: 42px;\\n  line-height: 42px;\\n  box-sizing: border-box;\\n  margin-top: -5px;\\n  width: 140px;\\n  position: absolute;\\n  top: 0;\\n}\\n.lesson[_ngcontent-%COMP%]   .seperator-horizontal[_ngcontent-%COMP%] {\\n  margin: 11px;\\n  background-color: var(--main-color);\\n  width: 100px;\\n  border: 1px solid var(--main-color);\\n  border-radius: 21px;\\n}\\n.lesson[_ngcontent-%COMP%]   .hours-duration[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 100px;\\n}\\n.lesson[_ngcontent-%COMP%]   .hours-duration[_ngcontent-%COMP%]   .seperator-vertical[_ngcontent-%COMP%] {\\n  margin: 0 11px;\\n  background-color: var(--main-color);\\n  height: 25px;\\n  border: 1px solid var(--main-color);\\n  border-radius: 21px;\\n}\\n.lesson[_ngcontent-%COMP%]   .lesson-date[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  margin-top: 38px;\\n}\\n\\n.section-notes[_ngcontent-%COMP%], .section-library[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n.section-trial[_ngcontent-%COMP%] {\\n  border-radius: 10px;\\n  border: 3px solid #f4f4f8;\\n  margin: 15px;\\n  padding: 15px;\\n  width: 50%;\\n}\\n.section-trial[_ngcontent-%COMP%]   .the-lessons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  position: relative;\\n}\\n\\n.section-lessons-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  font-size: 20px;\\n  align-items: center;\\n}\\n\\n.students[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n\\n.student-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-top: 10px;\\n  font-size: 14px;\\n  width: 100%;\\n}\\n.student-info[_ngcontent-%COMP%]   .student-info-img[_ngcontent-%COMP%], .student-info[_ngcontent-%COMP%]   .teacher-info-img[_ngcontent-%COMP%] {\\n  margin: 10px;\\n}\\n.student-info[_ngcontent-%COMP%]   .student-info-img[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .student-info[_ngcontent-%COMP%]   .teacher-info-img[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  border-radius: 50%;\\n  width: 100%;\\n}\\n.student-info[_ngcontent-%COMP%]   .student-info-img[_ngcontent-%COMP%] {\\n  width: 80%;\\n}\\n.student-info[_ngcontent-%COMP%]   .teacher-info-img[_ngcontent-%COMP%] {\\n  width: 30%;\\n}\\n.student-info[_ngcontent-%COMP%]   .student-info-details[_ngcontent-%COMP%] {\\n  width: 50%;\\n  margin: 10px;\\n  color: var(--main-color);\\n}\\n.student-info[_ngcontent-%COMP%]   .student-info-details[_ngcontent-%COMP%]   .student-info-details-first[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n}\\n.student-info[_ngcontent-%COMP%]   .student-info-details[_ngcontent-%COMP%]   .student-info-details-row[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n}\\n\\n.student-info-level[_ngcontent-%COMP%] {\\n  width: 35%;\\n  margin: 10px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n.student-info-level[_ngcontent-%COMP%]   .suggest[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-around;\\n  margin: 10px 0;\\n}\\n.student-info-level[_ngcontent-%COMP%]   .suggest[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n.student-info-level[_ngcontent-%COMP%]   .suggest[_ngcontent-%COMP%]   .suggest-level[_ngcontent-%COMP%] {\\n  width: 80%;\\n  padding: 30px;\\n  font-size: 25px;\\n  font-weight: bold;\\n  border-radius: 12px;\\n  border: 5px solid var(--main-color);\\n  margin: 0 10px;\\n}\\n\\n.spinner[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 500px;\\n  display: flex;\\n  justify-content: center;\\n}\\n\\n.rate-btn[_ngcontent-%COMP%] {\\n  width: 150px;\\n  margin-top: 15px;\\n  margin-left: 15px;\\n}\\n\\n.trial-rate[_ngcontent-%COMP%] {\\n  margin-top: 30px;\\n  width: 97%;\\n  padding: 15px;\\n  border-radius: 28px;\\n  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);\\n  margin: 15px;\\n  box-sizing: border-box;\\n}\\n\\n.has-text-centered[_ngcontent-%COMP%] {\\n  clear: both;\\n  width: 100%;\\n  display: flex;\\n  justify-content: center;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .col-lesson[_ngcontent-%COMP%] {\\n    width: 20% !important;\\n  }\\n}\\n\\n.freeze-sm-btn[_ngcontent-%COMP%] {\\n  padding: 3px 0 3px 0;\\n  border-radius: 100px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n    data: {\n      animation: [slideInOut]\n    }\n  });\n}", "map": {"version": 3, "names": ["take", "LessonStatus", "slideInOut", "SubSink", "fork<PERSON><PERSON>n", "DialogService", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵelementContainer", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵtemplate", "ClassDetailsComponent_ng_container_3_div_3_ng_container_2_ng_container_4_Template", "ɵɵadvance", "ɵɵproperty", "balanceTemplate_r2", "ClassDetailsComponent_ng_container_3_div_3_ng_container_3_ng_container_4_Template", "ClassDetailsComponent_ng_container_3_div_3_ng_container_2_Template", "ClassDetailsComponent_ng_container_3_div_3_ng_container_3_Template", "ctx_r2", "role", "classroomPackages", "ClassDetailsComponent_ng_container_3_div_3_Template", "classroom", "type", "ɵɵtextInterpolate", "getTotalDurationOfRequestedAndArrangedLessons", "ɵɵlistener", "ClassDetailsComponent_ng_container_7_ng_container_1_Template_img_error_6_listener", "$event", "ɵɵrestoreView", "_r4", "ɵɵnextContext", "ɵɵresetView", "generalService", "setDefaultUserAvatar", "item_r5", "i_r6", "getUserPhoto", "ɵɵsanitizeUrl", "ɵɵtextInterpolate1", "firstName", "ClassDetailsComponent_ng_container_7_ng_container_1_Template", "classroomStudents", "trackByStudentId", "lesson_r7", "ClassDetailsComponent_div_8_ng_container_8_Template", "ɵɵstyleMap", "ɵɵpureFunction0", "_c2", "classroomService", "hasClassroomPackageHoursLeft", "ɵɵpureFunction1", "_c3", "lessons", "sort", "lessonService", "sortLessonByDate", "ClassDetailsComponent_div_16_Template_div_click_0_listener", "_r8", "showRate", "ClassDetailsComponent_div_17_Template_div_click_4_listener", "_r9", "undefined", "ɵɵtwoWayListener", "ClassDetailsComponent_div_19_div_6_Template_textarea_ngModelChange_3_listener", "_r11", "ɵɵtwoWayBindingSet", "anotherTeacherText", "ClassDetailsComponent_div_19_div_6_Template_div_click_5_listener", "requestTeacher", "ɵɵtwoWayProperty", "teacher", "photo", "lastName", "skype", "username", "ClassDetailsComponent_div_19_Template_div_click_4_listener", "_r10", "showRequestAnoter<PERSON><PERSON><PERSON>", "ClassDetailsComponent_div_19_div_6_Template", "ClassDetailsComponent_div_19_div_9_Template", "ClassDetailsComponent", "constructor", "calendarService", "packageService", "noteService", "userService", "authService", "reportService", "router", "toastService", "libraryService", "confirmationService", "confirmDialogService", "studentTrialService", "subs", "classroomLessons", "classroomTitle", "classroomNotes", "suggestLevelIndex", "suggestCurrentLevel", "show<PERSON><PERSON>nge<PERSON><PERSON><PERSON>", "isLoadingOverlook", "showRateBtn", "showAllPackages", "maxSize", "directionLinks", "autoHide", "responsive", "config", "id", "itemsPerPage", "currentPage", "labels", "previousLabel", "next<PERSON><PERSON><PERSON>", "screenReaderPaginationLabel", "screenReaderPageLabel", "screenReaderCurrentLabel", "libraryFiles", "currentAgendaDate", "Date", "user", "ngOnInit", "getSelectedClassroom", "console", "log", "loadLibraryData", "getClassroomTitle", "i", "length", "activeLevel", "getClassroomPackages", "getLoggedInUser", "getLevelIndex", "initUpdateListener", "initPackagesUpdateListener", "initConfirmDialogListener", "ngOnDestroy", "unsubscribe", "ngOnChanges", "ngAfterContentInit", "ngAfterViewInit", "trackById", "index", "item", "aspUserId", "changeLevel", "add", "changeTrialLevel", "pipe", "subscribe", "res", "setShowToastmessage", "severity", "summary", "detail", "onPageChange", "number", "request", "reason", "studentId", "teacherId", "submitRequestTeacher", "get<PERSON>essons", "onShowArrangeLesson", "classroomToArrangeLesson", "navigate", "toggleSection", "sectionElement", "sectionArrowImgSrc", "getLessonColor", "status", "getEventGradientColor", "pop", "event", "suggestPrevious", "suggestNext", "onDeleteLesson", "filter", "el", "lesson", "viewPackages", "slideNativeElements", "allPackages", "nativeElement", "classroomId", "requests", "getClassroomFiles", "totalDuration", "reduce", "duration", "toLowerCase", "ARRANGED", "REQUESTED", "accumulatedHoursLeft", "getAccumulatedHoursLeft", "packages", "result", "convertHoursToMinutesWithSuffix", "classroomPackagesUpdateListener", "classroomUpdateListener", "freezePackageSelected", "showFreezeDialog", "setProperties", "confirmMessage", "acceptBtnLabel", "rejectBtnLabel", "action", "confirmIcon", "showHeader", "headerText", "dialogType", "headerClass", "show", "prepareDataBeforeSubmit", "studentType", "formattedData", "userData", "email", "phone", "course", "language", "message", "acceptSelected$", "hide", "sink", "postContactUs", "_", "ɵɵdirectiveInject", "i1", "GeneralService", "i2", "CalendarService", "i3", "ClassroomService", "i4", "LessonService", "i5", "PackageService", "i6", "NoteService", "i7", "UserService", "i8", "AuthService", "i9", "RatingAndReportService", "i10", "Router", "i11", "ToastService", "i12", "LibraryService", "i13", "ConfirmationService", "i14", "ConfirmDialogService", "i15", "StudentTrialService", "_2", "selectors", "viewQuery", "ClassDetailsComponent_Query", "rf", "ctx", "ɵɵNgOnChangesFeature", "decls", "vars", "consts", "template", "ClassDetailsComponent_Template", "ClassDetailsComponent_div_1_Template", "ClassDetailsComponent_ng_container_3_Template", "ClassDetailsComponent_ng_template_4_Template", "ɵɵtemplateRefExtractor", "ClassDetailsComponent_ng_container_7_Template", "ClassDetailsComponent_div_8_Template", "ClassDetailsComponent_div_16_Template", "ClassDetailsComponent_div_17_Template", "ClassDetailsComponent_div_19_Template", "ClassDetailsComponent_Defer_20_Template", "ClassDetailsComponent_DeferPlaceholder_21_Template", "ɵɵdefer", "ClassDetailsComponent_Defer_22_DepsFn", "ɵɵdeferOnViewport", "ClassDetailsComponent_Template_img_click_27_listener", "_r1"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\classrooms\\class\\class-details\\class-details.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\classrooms\\class\\class-details\\class-details.component.html"], "sourcesContent": ["import { Component, Input, OnInit, ViewChild } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { take } from 'rxjs/operators';\r\nimport { Classroom, RequestNewTeacher } from 'src/app/core/models/classroom.model';\r\nimport { Lesson, LessonStatus } from 'src/app/core/models/lesson.model';\r\nimport { Note } from 'src/app/core/models/note.model';\r\nimport { Package } from 'src/app/core/models/package.model';\r\nimport { User } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { CalendarService } from 'src/app/core/services/calendar.service';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { LessonService } from 'src/app/core/services/lesson.service';\r\nimport { NoteService } from 'src/app/core/services/note.service';\r\nimport { PackageService } from 'src/app/core/services/package.service';\r\nimport { RatingAndReportService } from 'src/app/core/services/rating-and-report.service';\r\nimport { UserService } from 'src/app/core/services/user.service';\r\nimport { slideInOut } from 'src/app/helpers/my-animations';\r\nimport { SubSink } from 'subsink';\r\nimport * as data from '../../../../../core/models/data';\r\nimport { LibraryService } from 'src/app/core/services/library.service';\r\nimport { forkJoin } from 'rxjs';\r\nimport { LibraryFile } from 'src/app/core/models/library.model';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { DialogService } from 'primeng/dynamicdialog';\r\nimport { ConfirmationService } from 'primeng/api';\r\nimport { ConfirmDialogService } from 'src/app/core/services/confirm-dialog.service';\r\nimport { StudentTrialService } from 'src/app/core/services/student-trial.service';\r\n\r\n@Component({\r\n  selector: 'app-class-details',\r\n  templateUrl: './class-details.component.html',\r\n  styleUrls: ['./class-details.component.scss'],\r\n  animations: [slideInOut],\r\n  providers: [DialogService]\r\n})\r\nexport class ClassDetailsComponent implements OnInit {\r\n  @Input() classroom: Classroom = {} as Classroom;\r\n  @ViewChild('arrangeLesson') public arrangeLesson: any;\r\n  @ViewChild('allPackages') public allPackages: any;\r\n  private subs = new SubSink();\r\n  public classroomPackages: Package[] = [];\r\n  @Input() public classroomLessons: Lesson[] = [];\r\n  public classroomTitle: string = \"\";\r\n  public classroomNotes: Note[] = [];\r\n  public suggestLevelIndex: number = 0;\r\n  public suggestCurrentLevel: string[] = [\"A1\", \"A2\", \"B1\", \"B2\", \"C1\", \"C2\"];\r\n  public showArrangeLesson: boolean = false;\r\n  public isLoadingOverlook: boolean = false;\r\n  public role: string = \"\";\r\n  showRateBtn: boolean = true;\r\n  showRate: boolean = false;\r\n  showRequestAnoterTeacher: boolean = false;\r\n  showAllPackages: boolean = false;\r\n  anotherTeacherText: string = \"\"\r\n\r\n\r\n  public maxSize: number = 10;\r\n  public directionLinks: boolean = true;\r\n  public autoHide: boolean = false;\r\n  public responsive: boolean = false;\r\n  public config: any = {\r\n    id: 'advanced',\r\n    itemsPerPage: 10,\r\n    currentPage: 1\r\n  };\r\n  public labels: any = {\r\n    previousLabel: 'Previous',\r\n    nextLabel: 'Next',\r\n    screenReaderPaginationLabel: 'Pagination',\r\n    screenReaderPageLabel: 'page',\r\n    screenReaderCurrentLabel: `You're on page`\r\n  };\r\n  libraryFiles: LibraryFile[] = [];\r\n  currentAgendaDate = new Date();\r\n  public user: User = {} as User\r\n\r\n  constructor(\r\n    private generalService: GeneralService,\r\n    private calendarService: CalendarService,\r\n    private classroomService: ClassroomService,\r\n    public lessonService: LessonService,\r\n    public packageService: PackageService,\r\n    private noteService: NoteService,\r\n    private userService: UserService,\r\n    private authService: AuthService,\r\n    private reportService: RatingAndReportService,\r\n    private router: Router,\r\n    private toastService: ToastService,\r\n    private libraryService: LibraryService,\r\n    private confirmationService: ConfirmationService,\r\n    public confirmDialogService: ConfirmDialogService,\r\n    private studentTrialService: StudentTrialService,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.classroom = this.classroomService.getSelectedClassroom();\r\n    console.log(this.classroom);\r\n    this.loadLibraryData(+this.classroom.id);\r\n    this.classroomTitle = this.classroomService.getClassroomTitle(this.classroom!);\r\n    for (let i = 0; i < this.suggestCurrentLevel.length; i++) {\r\n      if (this.suggestCurrentLevel[i] == this.classroom.activeLevel) {\r\n        this.suggestLevelIndex = i;\r\n      }\r\n    }\r\n\r\n    this.getClassroomPackages();\r\n    // this.showRateBtn = !!this.classroom.lessons?.find(el => el.status == LessonStatus.COMPLETED) // TODO UNCOMMENT\r\n    this.role = this.authService.getLoggedInUser().role\r\n    this.suggestLevelIndex = this.classroomService.getLevelIndex(this.classroom.activeLevel);\r\n    this.initUpdateListener();\r\n    this.initPackagesUpdateListener();\r\n    this.initConfirmDialogListener();\r\n    this.user = this.authService.getLoggedInUser();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  ngOnChanges() {\r\n    \r\n  }\r\n\r\n  ngAfterContentInit() {\r\n    // this.getLessons()\r\n    // this.subs.sink = this.lessonService.updateListener.subscribe(res => {\r\n    //   if (res) {\r\n    //     this.getLessons()\r\n    //   }\r\n    // })\r\n    // this.subs.sink = this.lessonService.addListener.subscribe(res => {\r\n    //   if (res) {\r\n    //     this.getLessons()\r\n    //   }\r\n    // })\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n  }\r\n\r\n  trackById(index: number, item: Classroom): number {\r\n    return +item.id;\r\n  }\r\n\r\n  trackByStudentId(index: number, item: User): string {\r\n    return item.aspUserId!;\r\n  }\r\n\r\n  changeLevel() {\r\n    this.subs.add(this.classroomService.changeTrialLevel(this.classroom.id, this.suggestCurrentLevel[this.suggestLevelIndex]).pipe(take(1)).subscribe(res => {\r\n    this.toastService.setShowToastmessage({\r\n      severity: 'success',\r\n      summary: '',\r\n      detail: 'Please check your email to reset your password.'\r\n    });\r\n    }));\r\n  }\r\n\r\n  onPageChange(number: number) {\r\n    this.config.currentPage = number;\r\n  }\r\n  requestTeacher() {\r\n    let request: RequestNewTeacher = {\r\n      reason: this.anotherTeacherText,\r\n      studentId: this.authService.getLoggedInUser().id,\r\n      teacherId: this.classroom.teacher?.aspUserId!\r\n    }\r\n    this.subs.add(this.reportService.submitRequestTeacher(request).subscribe(res => {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'success',\r\n        summary: '',\r\n        detail: 'Please check your email to reset your password.'\r\n      });\r\n    }));\r\n  }\r\n\r\n  getClassroomPackages() {\r\n    this.subs.add(this.packageService.getClassroomPackages(this.classroom.id).subscribe(res => {\r\n      // this.classroomPackages = res\r\n      console.log(res)\r\n      this.classroomPackages = res;\r\n    }));\r\n  }\r\n\r\n  getLessons() {\r\n    // this.classroomLessons = this.classroom.lessons!\r\n    // this.isLoadingOverlook = true;\r\n    // // if (this.classroomService.sessionUserClassrooms.length > 0) {\r\n    // //   this.classroomLessons = this.classroomService.sessionUserClassrooms.filter(el => el.id == this.classroom.id)[0].lessons!\r\n    // //   this.isLoadingOverlook = false;\r\n    // // }\r\n    // this.lessonService.getClassroomLessons(this.classroom.id).pipe(take(1)).subscribe(res => {\r\n    //   this.isLoadingOverlook = false\r\n    //   this.classroomLessons = res;\r\n    // });\r\n  }\r\n\r\n  onShowArrangeLesson() {\r\n    this.classroomService.classroomToArrangeLesson = this.classroom!\r\n    this.router.navigate(['/reschedule'])\r\n  }\r\n\r\n  toggleSection(sectionElement: any, sectionArrowImgSrc: any) {\r\n    this.generalService.toggleSection(sectionElement, sectionArrowImgSrc, true)\r\n  }\r\n\r\n\r\n  getLessonColor(status: LessonStatus): string {\r\n    return this.calendarService.getEventGradientColor(status);\r\n  }\r\n\r\n  pop(event: any) {\r\n  }\r\n\r\n  // getClassroomNotes(): Note[] {\r\n  //   return this.classroomService.getClassroomNotes();\r\n  // }\r\n\r\n  suggestPrevious() {\r\n    if (this.suggestLevelIndex > 0) {\r\n      this.suggestLevelIndex--;\r\n    }\r\n  }\r\n\r\n  suggestNext() {\r\n    if (this.suggestLevelIndex < 5) {\r\n      this.suggestLevelIndex++;\r\n    }\r\n  }\r\n\r\n  onDeleteLesson(event: any) {\r\n    this.classroomLessons = this.classroomLessons.filter((el: Lesson) => el.id !== event.lesson.id)\r\n  }\r\n\r\n  getUserPhoto(user: User) {\r\n    return this.userService.getUserPhoto(user);\r\n  }\r\n\r\n  viewPackages() {\r\n    this.showAllPackages = !this.showAllPackages;\r\n    this.generalService.slideNativeElements(this.showAllPackages, this.allPackages.nativeElement)\r\n  }\r\n\r\n  loadLibraryData(classroomId: number) {\r\n    const requests = [\r\n      this.libraryService.getClassroomFiles(classroomId),\r\n    ];\r\n    this.subs.add(forkJoin(requests).subscribe((res: any) => {\r\n      this.libraryFiles = res;\r\n    }));\r\n  }\r\n\r\n  /**\r\n   * Calculates the total duration of requested and arranged lessons.\r\n   * @returns A string representing the total duration in hours.\r\n   */\r\n  getTotalDurationOfRequestedAndArrangedLessons(): string {\r\n    /**\r\n     * The total duration of requested and arranged lessons.\r\n     * @type {number}\r\n     */\r\n    const totalDuration = this.classroom.lessons!.reduce((duration: number, lesson: Lesson) => {\r\n      if (lesson.status.toLowerCase() === LessonStatus.ARRANGED.toLowerCase() ||\r\n        lesson.status.toLowerCase() === LessonStatus.REQUESTED.toLowerCase()) {\r\n        return duration + lesson.duration;\r\n      }\r\n      return duration;\r\n    }, 0);\r\n\r\n    /**\r\n     * The accumulated hours left in the classroom packages.\r\n     * @type {number}\r\n     */\r\n    const accumulatedHoursLeft = this.classroomService.getAccumulatedHoursLeft(this.classroom.packages!);\r\n\r\n    /**\r\n     * The result of adding the accumulated hours left and the total duration.\r\n     * @type {number}\r\n     */\r\n    const result = accumulatedHoursLeft + totalDuration;\r\n\r\n    // Return the result as a formatted string\r\n    return this.generalService.convertHoursToMinutesWithSuffix(result, true);\r\n  }\r\n\r\n  private initPackagesUpdateListener() {\r\n    this.subs.add(this.classroomService.classroomPackagesUpdateListener.subscribe((res: any) => {\r\n      if (res) {\r\n        this.getClassroomPackages();\r\n      }\r\n    }));\r\n  }\r\n\r\n  private initUpdateListener() {\r\n    this.subs.add(this.classroomService.classroomUpdateListener.subscribe((res: any) => {\r\n      if (res) {\r\n        this.classroom = this.classroomService.getSelectedClassroom();\r\n      }\r\n    }));\r\n  }\r\n\r\n  freezePackageSelected() {\r\n    this.showFreezeDialog();\r\n  }\r\n  \r\n  /**\r\n   * Function to display a confirmation dialog for canceling a lesson.\r\n   */\r\n  private showFreezeDialog() {\r\n    // Set properties for the confirmation dialog\r\n    this.confirmDialogService.setProperties({\r\n      confirmMessage: 'Are you sure you would like to freeze your package?',\r\n      acceptBtnLabel: 'Yes',\r\n      rejectBtnLabel: 'No',\r\n      action: 'freeze-package',\r\n      confirmIcon: '/assets/icons/clock-dialog-icon.svg',\r\n      showHeader: true,\r\n      headerText: 'Freeze package request',\r\n      dialogType: 'custom',\r\n      headerClass: 'cd-dialog-btn-accept',\r\n    });\r\n    // Show the confirmation dialog\r\n    this.confirmDialogService.show();\r\n  }\r\n  \r\n\r\n\r\n  private prepareDataBeforeSubmit() {\r\n    const studentType = 'Old Student';\r\n    const formattedData = {\r\n      userData: {\r\n        firstName: this.user.firstName,\r\n        lastName: this.user.lastName,\r\n        email: this.user.email,\r\n        phone: `${this.user.phone}`\r\n      },\r\n      course: this.classroom.language + ' ' + this.classroom.activeLevel,\r\n      studentType: studentType,\r\n      message: 'Freeze Package'\r\n    }\r\n    return formattedData;\r\n  }\r\n\r\n  private initConfirmDialogListener() {\r\n    this.subs.add(this.confirmDialogService.acceptSelected$.subscribe(res => {\r\n      this.confirmDialogService.hide();\r\n      console.log(res);\r\n\r\n      switch (res.action) {\r\n        case 'freeze-package':\r\n\r\n          this.subs.sink = this.studentTrialService.postContactUs(this.prepareDataBeforeSubmit()).subscribe((res) => {\r\n            if (res) {\r\n              // this.showSuccessMessage();\r\n              // this.form.reset();\r\n            }\r\n          });\r\n          break;\r\n\r\n      }\r\n    }));\r\n  }\r\n}\r\n", "\r\n\r\n<div class=\"p-1 sm:p-3\">\r\n    <div *ngIf=\"isLoadingOverlook\" class=\"spinner\">\r\n        <mat-spinner style=\"margin-top:200px\"></mat-spinner>\r\n    </div>\r\n\r\n    <div>\r\n\r\n        <ng-container *ngIf=\"classroom?.type!=='Trial' && classroom\">\r\n\r\n            <!-- <app-lesson-rating [lesson]=\"classroom.lessons[0]\" \r\n[classroom]=\"classroom\"\r\n[mode]=\"'edit'\"\r\n[users]=\"classroom.classroomStudents\"></app-lesson-rating> -->\r\n\r\n\r\n            <div class=\"block-gradient border-round-xl\">\r\n                <div class=\"p-1 sm:p-3\">\r\n                    <div *ngIf=\"classroom?.type!=='Trial'\" class=\"\">\r\n                        <div class=\"section-overlook-title flex justify-content-between\">\r\n                            <ng-container *ngIf=\"role=='Student'\">\r\n                                <div class=\"sm:flex justify-content-between w-full\">\r\n                                    <div class=\"font-3xl mt-2 sm:mt-0\">Overview</div>\r\n                                    <ng-container *ngTemplateOutlet=\"balanceTemplate\"></ng-container>\r\n                                </div>\r\n                            </ng-container>\r\n                            <ng-container *ngIf=\"role=='Teacher'\">\r\n                            <div class=\"sm:flex justify-content-between w-full\">\r\n                                <div class=\"font-3xl mt-2 sm:mt-0\">Overview</div>\r\n                                <ng-container *ngTemplateOutlet=\"balanceTemplate\"></ng-container>\r\n                            </div>\r\n                            </ng-container>\r\n                            <!-- <div class=\"gray-border-button gap-2 px-2\" (click)=\"viewPackages()\">Freeze Package\r\n                                <img (click)=\"onShowArrangeLesson()\" src=\"/assets/icons/lessons/frozen-grad.svg\" height=\"16\">\r\n                            </div> -->\r\n                        </div>\r\n                        <app-packages-progress [packages]=\"classroomPackages\"></app-packages-progress>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </ng-container>\r\n\r\n        <ng-template #balanceTemplate>\r\n            <div class=\"flex sm:mt-0 gap-2 justify-content-between flex-column align-items-end\">\r\n            <div class=\"font-xl flex mt-3 sm:mt-0 gap-1 justify-content-between\">Current Balance: <span>\r\n                    <p-chip class=\" info bg-blue-400 border-round-xl\">\r\n                        <div class=\"content text-white\">{{getTotalDurationOfRequestedAndArrangedLessons()}}</div>\r\n                    </p-chip>\r\n                </span> </div>\r\n                <!-- TODO: add freeze button when backend is implemented -->\r\n                <!-- <div *ngIf=\"packageService.checkIfPackageCanBeFrozen(classroomPackages)\" class=\"gray-border-button font-sm freeze-sm-btn gap-2 px-3 max-w-max\" (click)=\"freezePackageSelected()\">Freeze Package\r\n                </div>   -->\r\n            </div>\r\n        </ng-template>\r\n\r\n        <div class=\"section-content\">\r\n            <ng-container *ngIf=\"classroom?.type==='Trial' && classroom && role=='Teacher'\">\r\n            <ng-container *ngFor=\"let item of classroom.classroomStudents; index as i; trackBy: trackByStudentId\">\r\n                <div class=\"block-gradient border-round-xl h-full\">\r\n            <app-class-info-student-accordion-item [classroom]=\"classroom\" [student]=\"item\" [isActive]=\"i === 0 ? true : false\">\r\n                <div title class=\"w-full\">\r\n                    <div class=\"grid align-items-center justify-content-between font-lg font-bold align-items-end\">\r\n                      <div class=\"col-12 flex align-items-center\"> <img [src]=\"getUserPhoto(item)\" (error)=\"generalService.setDefaultUserAvatar($event)\" class=\"mr-2 h-3rem border-circle\">\r\n                        <span class=\"text-primary\">{{item.firstName}}’s Information</span></div>\r\n                    \r\n                    </div>\r\n                  </div>\r\n                <div content class=\"p-4\">\r\n                 \r\n                </div>\r\n            </app-class-info-student-accordion-item>\r\n        </div>\r\n    </ng-container>\r\n</ng-container>\r\n            <div *ngIf=\"classroom?.type!=='Trial' && classroom\" class=\"block-gradient border-round-xl  p-3\">\r\n                <div class=\"mb-3 section-overlook-title flex justify-content-between\">\r\n                    <div class=\"font-3xl\">Lessons</div>\r\n                    <!-- <div class=\"gray-border-button\" (click)=\"viewPackages()\">SEE ALL</div> -->\r\n                </div>\r\n\r\n                <p-scrollPanel [style]=\"{width: '100%', height: '300px'}\" styleClass=\"custombar1\">\r\n                <div class=\"grid mr-1\">\r\n                    <div class=\"col-6 sm:col-3 md:col-3 col-lesson\" [pTooltip]=\"!classroomService.hasClassroomPackageHoursLeft(classroom) ? 'No available hours left in classroom': ''\" tooltipPosition=\"top\">\r\n                    <app-mini-lesson-info-card class=\"pointer hover:opacity-60\" cardType=\"request\" [classroom]=\"classroom\" [ngClass]=\"{'disabled-link': !classroomService.hasClassroomPackageHoursLeft(classroom)}\"></app-mini-lesson-info-card>\r\n                </div>\r\n                    <ng-container *ngFor=\"let lesson of (classroom.lessons.sort(lessonService.sortLessonByDate))\">\r\n                        <div class=\"col-6 sm:col-3 md:col-3 col-lesson\">\r\n                        <app-mini-lesson-info-card cardType=\"lesson\" [lesson]=\"lesson\" [classroom]=\"classroom\"></app-mini-lesson-info-card>\r\n                        </div>\r\n                      </ng-container>\r\n                </div>\r\n            </p-scrollPanel>\r\n                <!-- <div class=\"the-lessons\">\r\n\r\n                    <div *ngFor=\"let lesson of classroomLessons | paginate: config; let i=index\" class=\"col--1of5\">\r\n                        <app-lesson-details (onDeleteLesson)=\"onDeleteLesson($event)\" [lesson]=\"lesson\">\r\n                        </app-lesson-details>\r\n                    </div>\r\n\r\n                    <div *ngIf=\"classroomLessons.length>maxSize\" class=\"has-text-centered\">\r\n                        <pagination-controls [id]=\"config.id\" [maxSize]=\"10\" [directionLinks]=\"directionLinks\"\r\n                            [autoHide]=\"autoHide\" [responsive]=\"responsive\" [previousLabel]=\"labels.previousLabel\"\r\n                            [nextLabel]=\"labels.nextLabel\"\r\n                            [screenReaderPaginationLabel]=\"labels.screenReaderPaginationLabel\"\r\n                            [screenReaderPageLabel]=\"labels.screenReaderPageLabel\"\r\n                            [screenReaderCurrentLabel]=\"labels.screenReaderCurrentLabel\"\r\n                            (pageChange)=\"onPageChange($event)\">\r\n                        </pagination-controls>\r\n                    </div>\r\n\r\n                </div> -->\r\n            </div>\r\n\r\n            \r\n<div class=\"grid mt-4\">\r\n\r\n    <div class=\"col-12 md:col-6 h-full\">\r\n        <div class=\"gradient-block\">\r\n            \r\n        <app-calendar-agenda [lessons]=\"classroom.lessons\"></app-calendar-agenda>\r\n        </div>\r\n    </div>\r\n\r\n\r\n    <div class=\"col-12 md:col-6\">\r\n       \r\n    <app-block-viewer header=\" \" headerBackgroundImage=\"/assets/images/dashboard/gradient-sm-1.png\"\r\n    blockClass=\"relative card\" [containerHeight]=\"480\" containerClass=\"\">\r\n        <app-lesson-schedule-box [classroom]=\"classroom\" [lessons]=\"classroom.lessons\"></app-lesson-schedule-box>\r\n    </app-block-viewer>\r\n    </div>\r\n\r\n</div>\r\n\r\n            <div (click)=\"showRate=true\" *ngIf=\"classroom?.type==='Trial' && role=='Student' && showRateBtn\"\r\n                class=\"light-purple-button rate-btn\">\r\n                Rate Trial\r\n            </div>\r\n            <div [@slideInOut] class=\"trial-rate\" *ngIf=\"showRate\">\r\n                <div class=\"section-lessons-title\" style=\"margin:15px;\">\r\n                    <strong>Trial Rating</strong>\r\n                    <div class=\"main-color-button\" (click)=\"showRate=false\">Close</div>\r\n                </div>\r\n                <app-teacher-rate [classroom]=\"classroom\" mode=\"inTrial\"></app-teacher-rate>\r\n            </div>\r\n            <div style=\"display: flex\">\r\n                <div *ngIf=\"classroom?.type==='Trial' && role=='Student'\" class=\" section-trial\">\r\n                    <div class=\"section-lessons-title\">\r\n                        <strong>Teacher Info</strong>\r\n                        <div (click)=\"showRequestAnoterTeacher=true\" class=\"white-button\">\r\n                            Request another teacher\r\n                        </div>\r\n                    </div>\r\n                    <div *ngIf=\"showRequestAnoterTeacher\" class=\"requestTeacher\">\r\n                        <div style=\"margin:10px 0\">\r\n                            Let us know why you would like to change your teacher\r\n                        </div>\r\n                        <textarea rows=\"3\" [(ngModel)]=\"anotherTeacherText\" class=\"input-element\"\r\n                            style=\"box-sizing: border-box; width:100%;\"></textarea>\r\n                        <div class=\"btns\">\r\n                            <div class=\"light-purple-button\" (click)=\"requestTeacher()\">\r\n                                Submit\r\n                            </div>\r\n                        </div>\r\n                        <hr>\r\n                    </div>\r\n                    <div class=\"students\">\r\n                        <div>\r\n                            <div *ngFor=\"let user of classroom.classroomStudents\" class=\"student-info\">\r\n                                <div class=\"teacher-info-img\">\r\n                                    <img [src]=\"getUserPhoto(classroom.teacher?.photo)\">\r\n                                </div>\r\n                                <div class=\"student-info-details\">\r\n                                    <div class=\"student-info-details-first\">\r\n                                        <div>\r\n                                            First Name\r\n                                            <br>\r\n                                            <strong>{{classroom.teacher?.firstName}}</strong>\r\n                                        </div>\r\n                                        <!-- <div>\r\n                                            Preffered Name\r\n                                            <br>\r\n                                            <strong>{{user.}}</strong>\r\n                                        </div> -->\r\n                                    </div>\r\n                                    <div class=\"student-info-details-row\">\r\n                                        Last Name\r\n                                        <br>\r\n                                        <strong>{{classroom.teacher?.lastName}}</strong>\r\n                                    </div>\r\n                                    <div class=\"student-info-details-row\">\r\n                                        Skype\r\n                                        <br>\r\n                                        <strong>{{classroom.teacher?.skype}}</strong>\r\n                                    </div>\r\n                                    <div class=\"student-info-details-row\">\r\n                                        Email\r\n                                        <br>\r\n                                        <strong>{{classroom.teacher?.username}}</strong>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n\r\n    \r\n<!-- <g-accordion title=\"Notes\" backgroundImage=\"/assets/images/lessons-gradient-box-bg.png\" elementTitle=\"section-notes\">\r\n    <div class=\"p-3\">\r\n        <div class=\"section-notes\">\r\n            <app-notes-list [notes]=\"classroomNotes\"></app-notes-list>\r\n        </div>\r\n    </div>\r\n</g-accordion> -->\r\n\r\n@defer(on viewport) {\r\n<div class=\"classroom-cards-space\">\r\n    <app-library [selectedClassroom]=\"classroom\"></app-library>\r\n</div>\r\n<div class=\"classroom-cards-space\">\r\n    <app-notes [selectedClassroom]=\"classroom\"></app-notes>\r\n</div>\r\n}@placeholder {\r\n    <span>loading...</span>\r\n    }\r\n\r\n</div>\r\n\r\n            \r\n\r\n\r\n\r\n\r\n\r\n<!-- <div class=\"section\">\r\n    <div class=\"section-title\">\r\n        <strong>Homework</strong>\r\n        <div class=\"toggle-section\" (click)=\"toggleSection(homeworkSection, homeworkArrowSrc)\">\r\n            <img #homeworkArrowSrc src=\"/assets/icons/toogle-section.svg\" class=\"section-arrow\">\r\n        </div>\r\n    </div>\r\n    <div #homeworkSection [attr.id]=\"'homework-section'\" [attr.open]=\"true\" class=\"section-content\">\r\n        <div class=\"section-homework\">\r\n            <app-homework [classroom]=\"classroom\"></app-homework>\r\n        </div>\r\n    </div>\r\n</div>\r\n\r\n<div class=\"modal no-visibility p-0\" #arrangeLesson>\r\n    <div class=\"popup-title p-20\">\r\n        <div>Arrange Lesson</div>\r\n        <img (click)=\"onShowArrangeLesson()\" src=\"/assets/icons/close.png\" class=\"close-img close-img-abs hvr-glow\">\r\n    </div>\r\n    <app-scheduler [classroom]=\"classroom\" [showHeader]=\"false\"></app-scheduler>\r\n</div> -->\r\n\r\n<div class=\"modal no-visibility p-0\" #allPackages>\r\n    <div class=\"popup-title p-20\">\r\n        <img (click)=\"viewPackages()\" src=\"/assets/icons/close.png\" class=\"close-img close-img-abs hvr-glow\">\r\n    </div>\r\n    <app-packages></app-packages>\r\n</div>"], "mappings": "AAEA,SAASA,IAAI,QAAQ,gBAAgB;AAErC,SAAiBC,YAAY,QAAQ,kCAAkC;AAavE,SAASC,UAAU,QAAQ,+BAA+B;AAC1D,SAASC,OAAO,QAAQ,SAAS;AAGjC,SAASC,QAAQ,QAAQ,MAAM;AAG/B,SAASC,aAAa,QAAQ,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICrBjDC,EAAA,CAAAC,cAAA,cAA+C;IAC3CD,EAAA,CAAAE,SAAA,sBAAoD;IACxDF,EAAA,CAAAG,YAAA,EAAM;;;;;IAmB0BH,EAAA,CAAAI,kBAAA,GAAiE;;;;;IAHzEJ,EAAA,CAAAK,uBAAA,GAAsC;IAE9BL,EADJ,CAAAC,cAAA,cAAoD,cACb;IAAAD,EAAA,CAAAM,MAAA,eAAQ;IAAAN,EAAA,CAAAG,YAAA,EAAM;IACjDH,EAAA,CAAAO,UAAA,IAAAC,iFAAA,2BAAkD;IACtDR,EAAA,CAAAG,YAAA,EAAM;;;;;;IADaH,EAAA,CAAAS,SAAA,GAAiC;IAAjCT,EAAA,CAAAU,UAAA,qBAAAC,kBAAA,CAAiC;;;;;IAMpDX,EAAA,CAAAI,kBAAA,GAAiE;;;;;IAHrEJ,EAAA,CAAAK,uBAAA,GAAsC;IAElCL,EADJ,CAAAC,cAAA,cAAoD,cACb;IAAAD,EAAA,CAAAM,MAAA,eAAQ;IAAAN,EAAA,CAAAG,YAAA,EAAM;IACjDH,EAAA,CAAAO,UAAA,IAAAK,iFAAA,2BAAkD;IACtDZ,EAAA,CAAAG,YAAA,EAAM;;;;;;IADaH,EAAA,CAAAS,SAAA,GAAiC;IAAjCT,EAAA,CAAAU,UAAA,qBAAAC,kBAAA,CAAiC;;;;;IAVxDX,EADJ,CAAAC,cAAA,cAAgD,cACqB;IAO7DD,EANA,CAAAO,UAAA,IAAAM,kEAAA,0BAAsC,IAAAC,kEAAA,0BAMA;IAS1Cd,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAE,SAAA,gCAA8E;IAClFF,EAAA,CAAAG,YAAA,EAAM;;;;IAjBiBH,EAAA,CAAAS,SAAA,GAAqB;IAArBT,EAAA,CAAAU,UAAA,SAAAK,MAAA,CAAAC,IAAA,cAAqB;IAMrBhB,EAAA,CAAAS,SAAA,EAAqB;IAArBT,EAAA,CAAAU,UAAA,SAAAK,MAAA,CAAAC,IAAA,cAAqB;IAUjBhB,EAAA,CAAAS,SAAA,EAA8B;IAA9BT,EAAA,CAAAU,UAAA,aAAAK,MAAA,CAAAE,iBAAA,CAA8B;;;;;IA5BrEjB,EAAA,CAAAK,uBAAA,GAA6D;IASrDL,EADJ,CAAAC,cAAA,cAA4C,aAChB;IACpBD,EAAA,CAAAO,UAAA,IAAAW,mDAAA,kBAAgD;IAqBxDlB,EADI,CAAAG,YAAA,EAAM,EACJ;;;;;IArBQH,EAAA,CAAAS,SAAA,GAA+B;IAA/BT,EAAA,CAAAU,UAAA,UAAAK,MAAA,CAAAI,SAAA,kBAAAJ,MAAA,CAAAI,SAAA,CAAAC,IAAA,cAA+B;;;;;IA0B7CpB,EADA,CAAAC,cAAA,cAAoF,cACf;IAAAD,EAAA,CAAAM,MAAA,wBAAiB;IAE1EN,EAF0E,CAAAC,cAAA,WAAM,iBAClC,cACd;IAAAD,EAAA,CAAAM,MAAA,GAAmD;IAM/FN,EAN+F,CAAAG,YAAA,EAAM,EACpF,EACN,EAAO,EAIZ;;;;IANsCH,EAAA,CAAAS,SAAA,GAAmD;IAAnDT,EAAA,CAAAqB,iBAAA,CAAAN,MAAA,CAAAO,6CAAA,GAAmD;;;;;;IAW/FtB,EAAA,CAAAK,uBAAA,GAAsG;IAK/CL,EAJnD,CAAAC,cAAA,cAAmD,gDAC6D,cACtF,cACyE,cACjD,cAAyH;IAAxFD,EAAA,CAAAuB,UAAA,mBAAAC,kFAAAC,MAAA;MAAAzB,EAAA,CAAA0B,aAAA,CAAAC,GAAA;MAAA,MAAAZ,MAAA,GAAAf,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAASd,MAAA,CAAAe,cAAA,CAAAC,oBAAA,CAAAN,MAAA,CAA2C;IAAA,EAAC;IAArFzB,EAAA,CAAAG,YAAA,EAAwH;IACnKH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAM,MAAA,GAAgC;IAGjEN,EAHiE,CAAAG,YAAA,EAAO,EAAM,EAEtE,EACF;IACRH,EAAA,CAAAE,SAAA,cAEM;IAEdF,EADI,CAAAG,YAAA,EAAwC,EACtC;;;;;;;IAZqCH,EAAA,CAAAS,SAAA,GAAuB;IAAkBT,EAAzC,CAAAU,UAAA,cAAAK,MAAA,CAAAI,SAAA,CAAuB,YAAAa,OAAA,CAAiB,aAAAC,IAAA,sBAAoC;IAGvDjC,EAAA,CAAAS,SAAA,GAA0B;IAA1BT,EAAA,CAAAU,UAAA,QAAAK,MAAA,CAAAmB,YAAA,CAAAF,OAAA,GAAAhC,EAAA,CAAAmC,aAAA,CAA0B;IAC/CnC,EAAA,CAAAS,SAAA,GAAgC;IAAhCT,EAAA,CAAAoC,kBAAA,KAAAJ,OAAA,CAAAK,SAAA,wBAAgC;;;;;IAPvErC,EAAA,CAAAK,uBAAA,GAAgF;IAChFL,EAAA,CAAAO,UAAA,IAAA+B,4DAAA,4BAAsG;;;;;IAAvEtC,EAAA,CAAAS,SAAA,EAAgC;IAAYT,EAA5C,CAAAU,UAAA,YAAAK,MAAA,CAAAI,SAAA,CAAAoB,iBAAA,CAAgC,iBAAAxB,MAAA,CAAAyB,gBAAA,CAAqC;;;;;IA4B5FxC,EAAA,CAAAK,uBAAA,GAA8F;IAC1FL,EAAA,CAAAC,cAAA,cAAgD;IAChDD,EAAA,CAAAE,SAAA,oCAAmH;IACnHF,EAAA,CAAAG,YAAA,EAAM;;;;;;IADuCH,EAAA,CAAAS,SAAA,GAAiB;IAACT,EAAlB,CAAAU,UAAA,WAAA+B,SAAA,CAAiB,cAAA1B,MAAA,CAAAI,SAAA,CAAwB;;;;;IAX1FnB,EAFR,CAAAC,cAAA,cAAgG,cACtB,cAC5C;IAAAD,EAAA,CAAAM,MAAA,cAAO;IAEjCN,EAFiC,CAAAG,YAAA,EAAM,EAEjC;IAIFH,EAFJ,CAAAC,cAAA,wBAAkF,cAC3D,cACuK;IAC1LD,EAAA,CAAAE,SAAA,oCAA4N;IAChOF,EAAA,CAAAG,YAAA,EAAM;IACFH,EAAA,CAAAO,UAAA,IAAAmC,mDAAA,2BAA8F;IA0BtG1C,EArBI,CAAAG,YAAA,EAAM,EACM,EAoBV;;;;IA/BaH,EAAA,CAAAS,SAAA,GAA0C;IAA1CT,EAAA,CAAA2C,UAAA,CAAA3C,EAAA,CAAA4C,eAAA,IAAAC,GAAA,EAA0C;IAEL7C,EAAA,CAAAS,SAAA,GAAmH;IAAnHT,EAAA,CAAAU,UAAA,cAAAK,MAAA,CAAA+B,gBAAA,CAAAC,4BAAA,CAAAhC,MAAA,CAAAI,SAAA,gDAAmH;IACpFnB,EAAA,CAAAS,SAAA,EAAuB;IAACT,EAAxB,CAAAU,UAAA,cAAAK,MAAA,CAAAI,SAAA,CAAuB,YAAAnB,EAAA,CAAAgD,eAAA,IAAAC,GAAA,GAAAlC,MAAA,CAAA+B,gBAAA,CAAAC,4BAAA,CAAAhC,MAAA,CAAAI,SAAA,GAAyF;IAE9JnB,EAAA,CAAAS,SAAA,EAA2D;IAA3DT,EAAA,CAAAU,UAAA,YAAAK,MAAA,CAAAI,SAAA,CAAA+B,OAAA,CAAAC,IAAA,CAAApC,MAAA,CAAAqC,aAAA,CAAAC,gBAAA,EAA2D;;;;;;IAiDpGrD,EAAA,CAAAC,cAAA,cACyC;IADpCD,EAAA,CAAAuB,UAAA,mBAAA+B,2DAAA;MAAAtD,EAAA,CAAA0B,aAAA,CAAA6B,GAAA;MAAA,MAAAxC,MAAA,GAAAf,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAAAd,MAAA,CAAAyC,QAAA,GAAkB,IAAI;IAAA,EAAC;IAExBxD,EAAA,CAAAM,MAAA,mBACJ;IAAAN,EAAA,CAAAG,YAAA,EAAM;;;;;;IAGEH,EAFR,CAAAC,cAAA,cAAuD,cACK,aAC5C;IAAAD,EAAA,CAAAM,MAAA,mBAAY;IAAAN,EAAA,CAAAG,YAAA,EAAS;IAC7BH,EAAA,CAAAC,cAAA,cAAwD;IAAzBD,EAAA,CAAAuB,UAAA,mBAAAkC,2DAAA;MAAAzD,EAAA,CAAA0B,aAAA,CAAAgC,GAAA;MAAA,MAAA3C,MAAA,GAAAf,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAAAd,MAAA,CAAAyC,QAAA,GAAkB,KAAK;IAAA,EAAC;IAACxD,EAAA,CAAAM,MAAA,YAAK;IACjEN,EADiE,CAAAG,YAAA,EAAM,EACjE;IACNH,EAAA,CAAAE,SAAA,2BAA4E;IAChFF,EAAA,CAAAG,YAAA,EAAM;;;;IANDH,EAAA,CAAAU,UAAA,gBAAAiD,SAAA,CAAa;IAKI3D,EAAA,CAAAS,SAAA,GAAuB;IAAvBT,EAAA,CAAAU,UAAA,cAAAK,MAAA,CAAAI,SAAA,CAAuB;;;;;;IAWjCnB,EADJ,CAAAC,cAAA,cAA6D,cAC9B;IACvBD,EAAA,CAAAM,MAAA,8DACJ;IAAAN,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,mBACgD;IAD7BD,EAAA,CAAA4D,gBAAA,2BAAAC,8EAAApC,MAAA;MAAAzB,EAAA,CAAA0B,aAAA,CAAAoC,IAAA;MAAA,MAAA/C,MAAA,GAAAf,EAAA,CAAA4B,aAAA;MAAA5B,EAAA,CAAA+D,kBAAA,CAAAhD,MAAA,CAAAiD,kBAAA,EAAAvC,MAAA,MAAAV,MAAA,CAAAiD,kBAAA,GAAAvC,MAAA;MAAA,OAAAzB,EAAA,CAAA6B,WAAA,CAAAJ,MAAA;IAAA,EAAgC;IACHzB,EAAA,CAAAG,YAAA,EAAW;IAEvDH,EADJ,CAAAC,cAAA,cAAkB,cAC8C;IAA3BD,EAAA,CAAAuB,UAAA,mBAAA0C,iEAAA;MAAAjE,EAAA,CAAA0B,aAAA,CAAAoC,IAAA;MAAA,MAAA/C,MAAA,GAAAf,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAASd,MAAA,CAAAmD,cAAA,EAAgB;IAAA,EAAC;IACvDlE,EAAA,CAAAM,MAAA,eACJ;IACJN,EADI,CAAAG,YAAA,EAAM,EACJ;IACNH,EAAA,CAAAE,SAAA,SAAI;IACRF,EAAA,CAAAG,YAAA,EAAM;;;;IARiBH,EAAA,CAAAS,SAAA,GAAgC;IAAhCT,EAAA,CAAAmE,gBAAA,YAAApD,MAAA,CAAAiD,kBAAA,CAAgC;;;;;IAY3ChE,EADJ,CAAAC,cAAA,cAA2E,cACzC;IAC1BD,EAAA,CAAAE,SAAA,cAAoD;IACxDF,EAAA,CAAAG,YAAA,EAAM;IAGEH,EAFR,CAAAC,cAAA,cAAkC,cACU,UAC/B;IACDD,EAAA,CAAAM,MAAA,mBACA;IAAAN,EAAA,CAAAE,SAAA,SAAI;IACJF,EAAA,CAAAC,cAAA,aAAQ;IAAAD,EAAA,CAAAM,MAAA,GAAgC;IAOhDN,EAPgD,CAAAG,YAAA,EAAS,EAC/C,EAMJ;IACNH,EAAA,CAAAC,cAAA,eAAsC;IAClCD,EAAA,CAAAM,MAAA,mBACA;IAAAN,EAAA,CAAAE,SAAA,UAAI;IACJF,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAM,MAAA,IAA+B;IAC3CN,EAD2C,CAAAG,YAAA,EAAS,EAC9C;IACNH,EAAA,CAAAC,cAAA,eAAsC;IAClCD,EAAA,CAAAM,MAAA,eACA;IAAAN,EAAA,CAAAE,SAAA,UAAI;IACJF,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAM,MAAA,IAA4B;IACxCN,EADwC,CAAAG,YAAA,EAAS,EAC3C;IACNH,EAAA,CAAAC,cAAA,eAAsC;IAClCD,EAAA,CAAAM,MAAA,eACA;IAAAN,EAAA,CAAAE,SAAA,UAAI;IACJF,EAAA,CAAAC,cAAA,cAAQ;IAAAD,EAAA,CAAAM,MAAA,IAA+B;IAGnDN,EAHmD,CAAAG,YAAA,EAAS,EAC9C,EACJ,EACJ;;;;IA/BOH,EAAA,CAAAS,SAAA,GAA8C;IAA9CT,EAAA,CAAAU,UAAA,QAAAK,MAAA,CAAAmB,YAAA,CAAAnB,MAAA,CAAAI,SAAA,CAAAiD,OAAA,kBAAArD,MAAA,CAAAI,SAAA,CAAAiD,OAAA,CAAAC,KAAA,GAAArE,EAAA,CAAAmC,aAAA,CAA8C;IAOnCnC,EAAA,CAAAS,SAAA,GAAgC;IAAhCT,EAAA,CAAAqB,iBAAA,CAAAN,MAAA,CAAAI,SAAA,CAAAiD,OAAA,kBAAArD,MAAA,CAAAI,SAAA,CAAAiD,OAAA,CAAA/B,SAAA,CAAgC;IAWpCrC,EAAA,CAAAS,SAAA,GAA+B;IAA/BT,EAAA,CAAAqB,iBAAA,CAAAN,MAAA,CAAAI,SAAA,CAAAiD,OAAA,kBAAArD,MAAA,CAAAI,SAAA,CAAAiD,OAAA,CAAAE,QAAA,CAA+B;IAK/BtE,EAAA,CAAAS,SAAA,GAA4B;IAA5BT,EAAA,CAAAqB,iBAAA,CAAAN,MAAA,CAAAI,SAAA,CAAAiD,OAAA,kBAAArD,MAAA,CAAAI,SAAA,CAAAiD,OAAA,CAAAG,KAAA,CAA4B;IAK5BvE,EAAA,CAAAS,SAAA,GAA+B;IAA/BT,EAAA,CAAAqB,iBAAA,CAAAN,MAAA,CAAAI,SAAA,CAAAiD,OAAA,kBAAArD,MAAA,CAAAI,SAAA,CAAAiD,OAAA,CAAAI,QAAA,CAA+B;;;;;;IAlDvDxE,EAFR,CAAAC,cAAA,cAAiF,cAC1C,aACvB;IAAAD,EAAA,CAAAM,MAAA,mBAAY;IAAAN,EAAA,CAAAG,YAAA,EAAS;IAC7BH,EAAA,CAAAC,cAAA,cAAkE;IAA7DD,EAAA,CAAAuB,UAAA,mBAAAkD,2DAAA;MAAAzE,EAAA,CAAA0B,aAAA,CAAAgD,IAAA;MAAA,MAAA3D,MAAA,GAAAf,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAAAd,MAAA,CAAA4D,wBAAA,GAAkC,IAAI;IAAA,EAAC;IACxC3E,EAAA,CAAAM,MAAA,gCACJ;IACJN,EADI,CAAAG,YAAA,EAAM,EACJ;IACNH,EAAA,CAAAO,UAAA,IAAAqE,2CAAA,kBAA6D;IAczD5E,EADJ,CAAAC,cAAA,cAAsB,UACb;IACDD,EAAA,CAAAO,UAAA,IAAAsE,2CAAA,mBAA2E;IAoCvF7E,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;;;;IAnDIH,EAAA,CAAAS,SAAA,GAA8B;IAA9BT,EAAA,CAAAU,UAAA,SAAAK,MAAA,CAAA4D,wBAAA,CAA8B;IAeN3E,EAAA,CAAAS,SAAA,GAA8B;IAA9BT,EAAA,CAAAU,UAAA,YAAAK,MAAA,CAAAI,SAAA,CAAAoB,iBAAA,CAA8B;;;;;IAmDhFvC,EAAA,CAAAC,cAAA,cAAmC;IAC/BD,EAAA,CAAAE,SAAA,sBAA2D;IAC/DF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAAmC;IAC/BD,EAAA,CAAAE,SAAA,oBAAuD;IAC3DF,EAAA,CAAAG,YAAA,EAAM;;;;IAJWH,EAAA,CAAAS,SAAA,EAA+B;IAA/BT,EAAA,CAAAU,UAAA,sBAAAK,MAAA,CAAAI,SAAA,CAA+B;IAGjCnB,EAAA,CAAAS,SAAA,GAA+B;IAA/BT,EAAA,CAAAU,UAAA,sBAAAK,MAAA,CAAAI,SAAA,CAA+B;;;;;IAG1CnB,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAM,MAAA,iBAAU;IAAAN,EAAA,CAAAG,YAAA,EAAO;;;AD/L3B,OAAM,MAAO2E,qBAAqB;EAyChCC,YACUjD,cAA8B,EAC9BkD,eAAgC,EAChClC,gBAAkC,EACnCM,aAA4B,EAC5B6B,cAA8B,EAC7BC,WAAwB,EACxBC,WAAwB,EACxBC,WAAwB,EACxBC,aAAqC,EACrCC,MAAc,EACdC,YAA0B,EAC1BC,cAA8B,EAC9BC,mBAAwC,EACzCC,oBAA0C,EACzCC,mBAAwC;IAdxC,KAAA7D,cAAc,GAAdA,cAAc;IACd,KAAAkD,eAAe,GAAfA,eAAe;IACf,KAAAlC,gBAAgB,GAAhBA,gBAAgB;IACjB,KAAAM,aAAa,GAAbA,aAAa;IACb,KAAA6B,cAAc,GAAdA,cAAc;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACpB,KAAAC,oBAAoB,GAApBA,oBAAoB;IACnB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IAvDpB,KAAAxE,SAAS,GAAc,EAAe;IAGvC,KAAAyE,IAAI,GAAG,IAAI/F,OAAO,EAAE;IACrB,KAAAoB,iBAAiB,GAAc,EAAE;IACxB,KAAA4E,gBAAgB,GAAa,EAAE;IACxC,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,cAAc,GAAW,EAAE;IAC3B,KAAAC,iBAAiB,GAAW,CAAC;IAC7B,KAAAC,mBAAmB,GAAa,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACpE,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAnF,IAAI,GAAW,EAAE;IACxB,KAAAoF,WAAW,GAAY,IAAI;IAC3B,KAAA5C,QAAQ,GAAY,KAAK;IACzB,KAAAmB,wBAAwB,GAAY,KAAK;IACzC,KAAA0B,eAAe,GAAY,KAAK;IAChC,KAAArC,kBAAkB,GAAW,EAAE;IAGxB,KAAAsC,OAAO,GAAW,EAAE;IACpB,KAAAC,cAAc,GAAY,IAAI;IAC9B,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,UAAU,GAAY,KAAK;IAC3B,KAAAC,MAAM,GAAQ;MACnBC,EAAE,EAAE,UAAU;MACdC,YAAY,EAAE,EAAE;MAChBC,WAAW,EAAE;KACd;IACM,KAAAC,MAAM,GAAQ;MACnBC,aAAa,EAAE,UAAU;MACzBC,SAAS,EAAE,MAAM;MACjBC,2BAA2B,EAAE,YAAY;MACzCC,qBAAqB,EAAE,MAAM;MAC7BC,wBAAwB,EAAE;KAC3B;IACD,KAAAC,YAAY,GAAkB,EAAE;IAChC,KAAAC,iBAAiB,GAAG,IAAIC,IAAI,EAAE;IACvB,KAAAC,IAAI,GAAS,EAAU;EAkB1B;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACrG,SAAS,GAAG,IAAI,CAAC2B,gBAAgB,CAAC2E,oBAAoB,EAAE;IAC7DC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACxG,SAAS,CAAC;IAC3B,IAAI,CAACyG,eAAe,CAAC,CAAC,IAAI,CAACzG,SAAS,CAACwF,EAAE,CAAC;IACxC,IAAI,CAACb,cAAc,GAAG,IAAI,CAAChD,gBAAgB,CAAC+E,iBAAiB,CAAC,IAAI,CAAC1G,SAAU,CAAC;IAC9E,KAAK,IAAI2G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC7B,mBAAmB,CAAC8B,MAAM,EAAED,CAAC,EAAE,EAAE;MACxD,IAAI,IAAI,CAAC7B,mBAAmB,CAAC6B,CAAC,CAAC,IAAI,IAAI,CAAC3G,SAAS,CAAC6G,WAAW,EAAE;QAC7D,IAAI,CAAChC,iBAAiB,GAAG8B,CAAC;MAC5B;IACF;IAEA,IAAI,CAACG,oBAAoB,EAAE;IAC3B;IACA,IAAI,CAACjH,IAAI,GAAG,IAAI,CAACoE,WAAW,CAAC8C,eAAe,EAAE,CAAClH,IAAI;IACnD,IAAI,CAACgF,iBAAiB,GAAG,IAAI,CAAClD,gBAAgB,CAACqF,aAAa,CAAC,IAAI,CAAChH,SAAS,CAAC6G,WAAW,CAAC;IACxF,IAAI,CAACI,kBAAkB,EAAE;IACzB,IAAI,CAACC,0BAA0B,EAAE;IACjC,IAAI,CAACC,yBAAyB,EAAE;IAChC,IAAI,CAACf,IAAI,GAAG,IAAI,CAACnC,WAAW,CAAC8C,eAAe,EAAE;EAChD;EAEAK,WAAWA,CAAA;IACT,IAAI,CAAC3C,IAAI,CAAC4C,WAAW,EAAE;EACzB;EAEAC,WAAWA,CAAA,GAEX;EAEAC,kBAAkBA,CAAA;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;EAGFC,eAAeA,CAAA,GACf;EAEAC,SAASA,CAACC,KAAa,EAAEC,IAAe;IACtC,OAAO,CAACA,IAAI,CAACnC,EAAE;EACjB;EAEAnE,gBAAgBA,CAACqG,KAAa,EAAEC,IAAU;IACxC,OAAOA,IAAI,CAACC,SAAU;EACxB;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACpD,IAAI,CAACqD,GAAG,CAAC,IAAI,CAACnG,gBAAgB,CAACoG,gBAAgB,CAAC,IAAI,CAAC/H,SAAS,CAACwF,EAAE,EAAE,IAAI,CAACV,mBAAmB,CAAC,IAAI,CAACD,iBAAiB,CAAC,CAAC,CAACmD,IAAI,CAACzJ,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC0J,SAAS,CAACC,GAAG,IAAG;MACxJ,IAAI,CAAC9D,YAAY,CAAC+D,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;IACF,CAAC,CAAC,CAAC;EACL;EAEAC,YAAYA,CAACC,MAAc;IACzB,IAAI,CAACjD,MAAM,CAACG,WAAW,GAAG8C,MAAM;EAClC;EACAzF,cAAcA,CAAA;IACZ,IAAI0F,OAAO,GAAsB;MAC/BC,MAAM,EAAE,IAAI,CAAC7F,kBAAkB;MAC/B8F,SAAS,EAAE,IAAI,CAAC1E,WAAW,CAAC8C,eAAe,EAAE,CAACvB,EAAE;MAChDoD,SAAS,EAAE,IAAI,CAAC5I,SAAS,CAACiD,OAAO,EAAE2E;KACpC;IACD,IAAI,CAACnD,IAAI,CAACqD,GAAG,CAAC,IAAI,CAAC5D,aAAa,CAAC2E,oBAAoB,CAACJ,OAAO,CAAC,CAACR,SAAS,CAACC,GAAG,IAAG;MAC7E,IAAI,CAAC9D,YAAY,CAAC+D,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;IACJ,CAAC,CAAC,CAAC;EACL;EAEAxB,oBAAoBA,CAAA;IAClB,IAAI,CAACrC,IAAI,CAACqD,GAAG,CAAC,IAAI,CAAChE,cAAc,CAACgD,oBAAoB,CAAC,IAAI,CAAC9G,SAAS,CAACwF,EAAE,CAAC,CAACyC,SAAS,CAACC,GAAG,IAAG;MACxF;MACA3B,OAAO,CAACC,GAAG,CAAC0B,GAAG,CAAC;MAChB,IAAI,CAACpI,iBAAiB,GAAGoI,GAAG;IAC9B,CAAC,CAAC,CAAC;EACL;EAEAY,UAAUA,CAAA;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;EAGFC,mBAAmBA,CAAA;IACjB,IAAI,CAACpH,gBAAgB,CAACqH,wBAAwB,GAAG,IAAI,CAAChJ,SAAU;IAChE,IAAI,CAACmE,MAAM,CAAC8E,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEAC,aAAaA,CAACC,cAAmB,EAAEC,kBAAuB;IACxD,IAAI,CAACzI,cAAc,CAACuI,aAAa,CAACC,cAAc,EAAEC,kBAAkB,EAAE,IAAI,CAAC;EAC7E;EAGAC,cAAcA,CAACC,MAAoB;IACjC,OAAO,IAAI,CAACzF,eAAe,CAAC0F,qBAAqB,CAACD,MAAM,CAAC;EAC3D;EAEAE,GAAGA,CAACC,KAAU,GACd;EAEA;EACA;EACA;EAEAC,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC7E,iBAAiB,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACA,iBAAiB,EAAE;IAC1B;EACF;EAEA8E,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC9E,iBAAiB,GAAG,CAAC,EAAE;MAC9B,IAAI,CAACA,iBAAiB,EAAE;IAC1B;EACF;EAEA+E,cAAcA,CAACH,KAAU;IACvB,IAAI,CAAC/E,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACmF,MAAM,CAAEC,EAAU,IAAKA,EAAE,CAACtE,EAAE,KAAKiE,KAAK,CAACM,MAAM,CAACvE,EAAE,CAAC;EACjG;EAEAzE,YAAYA,CAACqF,IAAU;IACrB,OAAO,IAAI,CAACpC,WAAW,CAACjD,YAAY,CAACqF,IAAI,CAAC;EAC5C;EAEA4D,YAAYA,CAAA;IACV,IAAI,CAAC9E,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;IAC5C,IAAI,CAACvE,cAAc,CAACsJ,mBAAmB,CAAC,IAAI,CAAC/E,eAAe,EAAE,IAAI,CAACgF,WAAW,CAACC,aAAa,CAAC;EAC/F;EAEA1D,eAAeA,CAAC2D,WAAmB;IACjC,MAAMC,QAAQ,GAAG,CACf,IAAI,CAAChG,cAAc,CAACiG,iBAAiB,CAACF,WAAW,CAAC,CACnD;IACD,IAAI,CAAC3F,IAAI,CAACqD,GAAG,CAACnJ,QAAQ,CAAC0L,QAAQ,CAAC,CAACpC,SAAS,CAAEC,GAAQ,IAAI;MACtD,IAAI,CAACjC,YAAY,GAAGiC,GAAG;IACzB,CAAC,CAAC,CAAC;EACL;EAEA;;;;EAIA/H,6CAA6CA,CAAA;IAC3C;;;;IAIA,MAAMoK,aAAa,GAAG,IAAI,CAACvK,SAAS,CAAC+B,OAAQ,CAACyI,MAAM,CAAC,CAACC,QAAgB,EAAEV,MAAc,KAAI;MACxF,IAAIA,MAAM,CAACT,MAAM,CAACoB,WAAW,EAAE,KAAKlM,YAAY,CAACmM,QAAQ,CAACD,WAAW,EAAE,IACrEX,MAAM,CAACT,MAAM,CAACoB,WAAW,EAAE,KAAKlM,YAAY,CAACoM,SAAS,CAACF,WAAW,EAAE,EAAE;QACtE,OAAOD,QAAQ,GAAGV,MAAM,CAACU,QAAQ;MACnC;MACA,OAAOA,QAAQ;IACjB,CAAC,EAAE,CAAC,CAAC;IAEL;;;;IAIA,MAAMI,oBAAoB,GAAG,IAAI,CAAClJ,gBAAgB,CAACmJ,uBAAuB,CAAC,IAAI,CAAC9K,SAAS,CAAC+K,QAAS,CAAC;IAEpG;;;;IAIA,MAAMC,MAAM,GAAGH,oBAAoB,GAAGN,aAAa;IAEnD;IACA,OAAO,IAAI,CAAC5J,cAAc,CAACsK,+BAA+B,CAACD,MAAM,EAAE,IAAI,CAAC;EAC1E;EAEQ9D,0BAA0BA,CAAA;IAChC,IAAI,CAACzC,IAAI,CAACqD,GAAG,CAAC,IAAI,CAACnG,gBAAgB,CAACuJ,+BAA+B,CAACjD,SAAS,CAAEC,GAAQ,IAAI;MACzF,IAAIA,GAAG,EAAE;QACP,IAAI,CAACpB,oBAAoB,EAAE;MAC7B;IACF,CAAC,CAAC,CAAC;EACL;EAEQG,kBAAkBA,CAAA;IACxB,IAAI,CAACxC,IAAI,CAACqD,GAAG,CAAC,IAAI,CAACnG,gBAAgB,CAACwJ,uBAAuB,CAAClD,SAAS,CAAEC,GAAQ,IAAI;MACjF,IAAIA,GAAG,EAAE;QACP,IAAI,CAAClI,SAAS,GAAG,IAAI,CAAC2B,gBAAgB,CAAC2E,oBAAoB,EAAE;MAC/D;IACF,CAAC,CAAC,CAAC;EACL;EAEA8E,qBAAqBA,CAAA;IACnB,IAAI,CAACC,gBAAgB,EAAE;EACzB;EAEA;;;EAGQA,gBAAgBA,CAAA;IACtB;IACA,IAAI,CAAC9G,oBAAoB,CAAC+G,aAAa,CAAC;MACtCC,cAAc,EAAE,qDAAqD;MACrEC,cAAc,EAAE,KAAK;MACrBC,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE,gBAAgB;MACxBC,WAAW,EAAE,qCAAqC;MAClDC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,wBAAwB;MACpCC,UAAU,EAAE,QAAQ;MACpBC,WAAW,EAAE;KACd,CAAC;IACF;IACA,IAAI,CAACxH,oBAAoB,CAACyH,IAAI,EAAE;EAClC;EAIQC,uBAAuBA,CAAA;IAC7B,MAAMC,WAAW,GAAG,aAAa;IACjC,MAAMC,aAAa,GAAG;MACpBC,QAAQ,EAAE;QACRlL,SAAS,EAAE,IAAI,CAACkF,IAAI,CAAClF,SAAS;QAC9BiC,QAAQ,EAAE,IAAI,CAACiD,IAAI,CAACjD,QAAQ;QAC5BkJ,KAAK,EAAE,IAAI,CAACjG,IAAI,CAACiG,KAAK;QACtBC,KAAK,EAAE,GAAG,IAAI,CAAClG,IAAI,CAACkG,KAAK;OAC1B;MACDC,MAAM,EAAE,IAAI,CAACvM,SAAS,CAACwM,QAAQ,GAAG,GAAG,GAAG,IAAI,CAACxM,SAAS,CAAC6G,WAAW;MAClEqF,WAAW,EAAEA,WAAW;MACxBO,OAAO,EAAE;KACV;IACD,OAAON,aAAa;EACtB;EAEQhF,yBAAyBA,CAAA;IAC/B,IAAI,CAAC1C,IAAI,CAACqD,GAAG,CAAC,IAAI,CAACvD,oBAAoB,CAACmI,eAAe,CAACzE,SAAS,CAACC,GAAG,IAAG;MACtE,IAAI,CAAC3D,oBAAoB,CAACoI,IAAI,EAAE;MAChCpG,OAAO,CAACC,GAAG,CAAC0B,GAAG,CAAC;MAEhB,QAAQA,GAAG,CAACwD,MAAM;QAChB,KAAK,gBAAgB;UAEnB,IAAI,CAACjH,IAAI,CAACmI,IAAI,GAAG,IAAI,CAACpI,mBAAmB,CAACqI,aAAa,CAAC,IAAI,CAACZ,uBAAuB,EAAE,CAAC,CAAChE,SAAS,CAAEC,GAAG,IAAI;YACxG,IAAIA,GAAG,EAAE;cACP;cACA;YAAA;UAEJ,CAAC,CAAC;UACF;MAEJ;IACF,CAAC,CAAC,CAAC;EACL;EAAC,QAAA4E,CAAA,G;qBAtUUnJ,qBAAqB,EAAA9E,EAAA,CAAAkO,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAApO,EAAA,CAAAkO,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAtO,EAAA,CAAAkO,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAxO,EAAA,CAAAkO,iBAAA,CAAAO,EAAA,CAAAC,aAAA,GAAA1O,EAAA,CAAAkO,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAA5O,EAAA,CAAAkO,iBAAA,CAAAW,EAAA,CAAAC,WAAA,GAAA9O,EAAA,CAAAkO,iBAAA,CAAAa,EAAA,CAAAC,WAAA,GAAAhP,EAAA,CAAAkO,iBAAA,CAAAe,EAAA,CAAAC,WAAA,GAAAlP,EAAA,CAAAkO,iBAAA,CAAAiB,EAAA,CAAAC,sBAAA,GAAApP,EAAA,CAAAkO,iBAAA,CAAAmB,GAAA,CAAAC,MAAA,GAAAtP,EAAA,CAAAkO,iBAAA,CAAAqB,GAAA,CAAAC,YAAA,GAAAxP,EAAA,CAAAkO,iBAAA,CAAAuB,GAAA,CAAAC,cAAA,GAAA1P,EAAA,CAAAkO,iBAAA,CAAAyB,GAAA,CAAAC,mBAAA,GAAA5P,EAAA,CAAAkO,iBAAA,CAAA2B,GAAA,CAAAC,oBAAA,GAAA9P,EAAA,CAAAkO,iBAAA,CAAA6B,GAAA,CAAAC,mBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAArBnL,qBAAqB;IAAAoL,SAAA;IAAAC,SAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;qCAFrB,CAACtQ,aAAa,CAAC,GAAAC,EAAA,CAAAuQ,oBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAP,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QChC5BrQ,EAAA,CAAAC,cAAA,aAAwB;QACpBD,EAAA,CAAAO,UAAA,IAAAsQ,oCAAA,iBAA+C;QAI/C7Q,EAAA,CAAAC,cAAA,UAAK;QAoCDD,EAlCA,CAAAO,UAAA,IAAAuQ,6CAAA,0BAA6D,IAAAC,4CAAA,gCAAA/Q,EAAA,CAAAgR,sBAAA,CAkC/B;QAa9BhR,EAAA,CAAAC,cAAA,aAA6B;QAmBzBD,EAlBA,CAAAO,UAAA,IAAA0Q,6CAAA,0BAAgF,IAAAC,oCAAA,iBAkBgB;QA2CpGlR,EAHR,CAAAC,cAAA,aAAuB,cAEiB,cACJ;QAE5BD,EAAA,CAAAE,SAAA,+BAAyE;QAE7EF,EADI,CAAAG,YAAA,EAAM,EACJ;QAKNH,EAFA,CAAAC,cAAA,eAA6B,4BAGwC;QACjED,EAAA,CAAAE,SAAA,mCAAyG;QAIjHF,EAHI,CAAAG,YAAA,EAAmB,EACb,EAEJ;QAMMH,EAJA,CAAAO,UAAA,KAAA4Q,qCAAA,kBACyC,KAAAC,qCAAA,kBAGc;QAOvDpR,EAAA,CAAAC,cAAA,eAA2B;QACvBD,EAAA,CAAAO,UAAA,KAAA8Q,qCAAA,mBAAiF;QA6D7FrR,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;QAkBTH,EAPD,CAAAO,UAAA,KAAA+Q,uCAAA,OASK,KAAAC,kDAAA;QATLvR,EAAA,CAAAwR,OAAA,SAAAC,qCAAA,WASK;QATEzR,EAAA,CAAA0R,iBAAA,OAAW;QAWlB1R,EAAA,CAAAG,YAAA,EAAM;QAgCEH,EAFR,CAAAC,cAAA,kBAAkD,eAChB,eAC2E;QAAhGD,EAAA,CAAAuB,UAAA,mBAAAoQ,qDAAA;UAAA3R,EAAA,CAAA0B,aAAA,CAAAkQ,GAAA;UAAA,OAAA5R,EAAA,CAAA6B,WAAA,CAASyO,GAAA,CAAAnF,YAAA,EAAc;QAAA,EAAC;QACjCnL,EADI,CAAAG,YAAA,EAAqG,EACnG;QACNH,EAAA,CAAAE,SAAA,oBAA6B;QACjCF,EAAA,CAAAG,YAAA,EAAM;;;QAtQIH,EAAA,CAAAS,SAAA,EAAuB;QAAvBT,EAAA,CAAAU,UAAA,SAAA4P,GAAA,CAAAnK,iBAAA,CAAuB;QAMVnG,EAAA,CAAAS,SAAA,GAA4C;QAA5CT,EAAA,CAAAU,UAAA,UAAA4P,GAAA,CAAAnP,SAAA,kBAAAmP,GAAA,CAAAnP,SAAA,CAAAC,IAAA,iBAAAkP,GAAA,CAAAnP,SAAA,CAA4C;QAgDxCnB,EAAA,CAAAS,SAAA,GAA+D;QAA/DT,EAAA,CAAAU,UAAA,UAAA4P,GAAA,CAAAnP,SAAA,kBAAAmP,GAAA,CAAAnP,SAAA,CAAAC,IAAA,iBAAAkP,GAAA,CAAAnP,SAAA,IAAAmP,GAAA,CAAAtP,IAAA,cAA+D;QAkBxEhB,EAAA,CAAAS,SAAA,EAA4C;QAA5CT,EAAA,CAAAU,UAAA,UAAA4P,GAAA,CAAAnP,SAAA,kBAAAmP,GAAA,CAAAnP,SAAA,CAAAC,IAAA,iBAAAkP,GAAA,CAAAnP,SAAA,CAA4C;QA6CjCnB,EAAA,CAAAS,SAAA,GAA6B;QAA7BT,EAAA,CAAAU,UAAA,YAAA4P,GAAA,CAAAnP,SAAA,CAAA+B,OAAA,CAA6B;QAQ3BlD,EAAA,CAAAS,SAAA,GAAuB;QAAvBT,EAAA,CAAAU,UAAA,wBAAuB;QACrBV,EAAA,CAAAS,SAAA,EAAuB;QAACT,EAAxB,CAAAU,UAAA,cAAA4P,GAAA,CAAAnP,SAAA,CAAuB,YAAAmP,GAAA,CAAAnP,SAAA,CAAA+B,OAAA,CAA8B;QAM5ClD,EAAA,CAAAS,SAAA,EAAiE;QAAjET,EAAA,CAAAU,UAAA,UAAA4P,GAAA,CAAAnP,SAAA,kBAAAmP,GAAA,CAAAnP,SAAA,CAAAC,IAAA,iBAAAkP,GAAA,CAAAtP,IAAA,iBAAAsP,GAAA,CAAAlK,WAAA,CAAiE;QAIxDpG,EAAA,CAAAS,SAAA,EAAc;QAAdT,EAAA,CAAAU,UAAA,SAAA4P,GAAA,CAAA9M,QAAA,CAAc;QAQ3CxD,EAAA,CAAAS,SAAA,GAAkD;QAAlDT,EAAA,CAAAU,UAAA,UAAA4P,GAAA,CAAAnP,SAAA,kBAAAmP,GAAA,CAAAnP,SAAA,CAAAC,IAAA,iBAAAkP,GAAA,CAAAtP,IAAA,cAAkD;;;;;;iBDlH1D,CAACpB,UAAU;IAAC;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}