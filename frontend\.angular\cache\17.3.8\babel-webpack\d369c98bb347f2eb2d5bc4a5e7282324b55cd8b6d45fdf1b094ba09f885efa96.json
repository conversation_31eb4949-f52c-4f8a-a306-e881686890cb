{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport { HomeworkStatus } from '../models/homework.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./user.service\";\nimport * as i2 from \"./library.service\";\nimport * as i3 from \"@angular/common/http\";\nimport * as i4 from \"./general.service\";\nconst BACKEND_URL = environment.apiUrl;\nexport let HomeworkService = /*#__PURE__*/(() => {\n  class HomeworkService {\n    setTagToAll(tagToAll) {\n      this.tagToAll$.next(tagToAll);\n    }\n    constructor(userService, libraryService, http, generalService) {\n      this.userService = userService;\n      this.libraryService = libraryService;\n      this.http = http;\n      this.generalService = generalService;\n      this.homeworkToAdd = {};\n      this.tagToAll$ = new BehaviorSubject({});\n      this.tagToAll = this.tagToAll$.asObservable();\n      this.submitListener$ = new BehaviorSubject(false);\n      this.submitListener = this.submitListener$.asObservable();\n      this.isTaskFormValid$ = new BehaviorSubject(false);\n      this.isTaskFormValid = this.isTaskFormValid$.asObservable();\n      this.updateListener$ = new BehaviorSubject(false);\n      this.updateListener = this.updateListener$.asObservable();\n      this.showFormListener$ = new BehaviorSubject({\n        show: false,\n        homework: {}\n      });\n      this.showFormListener = this.showFormListener$.asObservable();\n    }\n    setSubmitListener(listen) {\n      this.submitListener$.next(listen);\n    }\n    setShowFormListener(listen, homework) {\n      this.showFormListener$.next({\n        show: listen,\n        homework: homework\n      });\n    }\n    setIsTaskFormValid(listen) {\n      this.isTaskFormValid$.next(listen);\n    }\n    setUpdateListener(listen) {\n      this.updateListener$.next(listen);\n    }\n    createHomework(homework) {\n      let homeworkTasksToSend = [];\n      let assignedToIds = [];\n      for (let user of homework.assignedTo) {\n        assignedToIds.push(user.id);\n      }\n      for (let task of homework.homeWorkTasks) {\n        let libraryFileIds = [];\n        for (let library of task.libraries) {\n          libraryFileIds.push(library.fileId);\n        }\n        let homeworkTaskToSend = {\n          title: task.title,\n          finished: false,\n          assignedTo: task.assignedTo.id,\n          studentFiles: [],\n          teacherFiles: task.teacherFiles,\n          link: task.link,\n          rate: task.rate,\n          proportion: task.proportion,\n          dismissed: false,\n          libraryFileIds: libraryFileIds,\n          classroomId: parseInt(task.assignedToUserInClassroom)\n        };\n        homeworkTasksToSend.push(homeworkTaskToSend);\n      }\n      let deadline = homework.deadLine;\n      if (homework.deadLine == null) {\n        deadline = \"No Deadline\";\n      }\n      let homeworkToSend = {\n        title: homework.title,\n        description: homework.description,\n        deadLine: deadline,\n        status: homework.status,\n        state: homework.state,\n        levels: homework.levels,\n        categories: homework.categories,\n        homeWorkTasks: homeworkTasksToSend\n      };\n      return this.http.post(BACKEND_URL + \"/Homework/CreateHomeWork\", homeworkToSend);\n    }\n    updateHomeworkState(homework, state) {\n      return this.http.post(BACKEND_URL + `/HomeWork/UpdateHomeworkState?HomeworkId=${homework.id}`, state);\n    }\n    updateHomeworkStatus(homework, status) {\n      return this.http.post(BACKEND_URL + `/HomeWork/UpdateHomeworkStatus?HomeworkId=${homework.id}`, {\n        status: status\n      });\n    }\n    deleteHomework(homework) {\n      return this.http.post(BACKEND_URL + `/HomeWork/DeleteHomework?hwId=${homework.id}`, {});\n    }\n    deleteTaskFromHomework(taskId) {\n      return this.http.post(BACKEND_URL + `/HomeWork/DeleteTaskFromHomework?homeWorkTaskId=${taskId}`, {});\n    }\n    compareFn(a, b) {\n      if (a.state == 'Pending' && b.state != 'Pending') return -1;\n      return 1;\n    }\n    getHomeworks() {\n      return this.http.get(BACKEND_URL + `/HomeWork/GetHomeworks`).pipe(map(x => {\n        let sortByPending = [];\n        for (let hw of x) {\n          hw.deadLine = hw.deadLine != 'No Deadline' ? new Date(hw.deadLine) : \"No Deadline\";\n          for (let task of hw.homeWorkTasks) {\n            for (let lib of task.libraries) {\n              let f = {\n                filePath: lib.path,\n                levels: lib.levels,\n                categories: lib.categories\n              };\n              task.teacherFiles.push(f);\n            }\n          }\n          if (hw.status == 'Pending') {\n            sortByPending.push(hw);\n          }\n        }\n        for (let hw of x) {\n          if (hw.status != 'Pending') {\n            sortByPending.push(hw);\n          }\n        }\n        return sortByPending;\n      }));\n    }\n    updateTaskFinished(finished, taskId) {\n      return this.http.post(BACKEND_URL + `​/HomeWork/UpdateTaskFinished?taskId=${taskId}&finished=${finished}`, {});\n    }\n    updateTaskRate(rate, taskId) {\n      return this.http.post(BACKEND_URL + `​/HomeWork/UpdateTaskRate?taskId=${taskId}&rate=${rate}`, {});\n    }\n    addStudentFileToTask(filePath, taskId) {\n      return this.http.post(BACKEND_URL + `​/HomeWork/UpdateTaskRate?taskId=${taskId}&filePath=${filePath}`, {});\n    }\n    updateTaskProportion(proportion, taskId) {\n      return this.http.post(BACKEND_URL + `​/HomeWork/UpdateTaskProportion?taskId=${taskId}&proportion=${proportion}`, {});\n    }\n    updateHomework(updateReq) {\n      return this.http.post(BACKEND_URL + `​/HomeWork/UpdateHomework?homeWorkId=${updateReq.id}`, updateReq);\n    }\n    addTaskToHomework(task, hwId) {\n      return this.http.post(BACKEND_URL + `​/HomeWork/AddTaskToHomework?homeWorkId=${hwId}`, task);\n    }\n    updateTaskDetails(task, hwId) {\n      return this.http.post(BACKEND_URL + `​/HomeWork/UpdateTaskDetails?homeWorkTaskId=${task.id}`, task);\n    }\n    getHomeworkColor(homework) {\n      if (homework.status.includes(HomeworkStatus.COMPLETED_RATED)) {\n        return \"#3F37C9\";\n      }\n      if (homework.status.includes(HomeworkStatus.COMPLETED_UNRATED)) {\n        return \"#3F37C9\";\n      }\n      if (homework.status.includes(HomeworkStatus.COMPLETED_PARTIALLY_RATED)) {\n        return \"#3F37C9\";\n      }\n      if (homework.status.includes(HomeworkStatus.PARTIALLY_COMPLETED_PARTIALLY_RATED)) {\n        return \"#4895EF\";\n      }\n      if (homework.status.includes(HomeworkStatus.COMPLETED_PARTIALLY_UNRATED)) {\n        return \"#4895EF\";\n      }\n      if (homework.status.includes(HomeworkStatus.PENDING)) {\n        return \"#7209B7\";\n      }\n      return \"#4895EF\";\n    }\n    getStatusOnUpdateFinishedTaskOrRate(tasks) {\n      let status = \"\";\n      let countRatedTasks = 0;\n      let countCompleted = 0;\n      let countDismissed = 0;\n      for (let task of tasks) {\n        if (task.rate > 0) {\n          countRatedTasks++;\n        }\n        if (task.finished) {\n          countCompleted++;\n        }\n        if (task.dismissed) {\n          countCompleted++;\n        }\n      }\n      if (countCompleted == 0) {\n        status = HomeworkStatus.PENDING;\n      }\n      if (countCompleted == tasks.length && countRatedTasks == tasks.length) {\n        status = HomeworkStatus.COMPLETED_RATED;\n      }\n      if (countCompleted == tasks.length && countRatedTasks == 0) {\n        status = HomeworkStatus.COMPLETED_UNRATED;\n      }\n      if (countCompleted >= 0 && countCompleted < tasks.length && countRatedTasks == tasks.length) {\n        status = HomeworkStatus.PARTIALLY_COMPLETED_RATED;\n      }\n      if (countCompleted == 0 && countCompleted < tasks.length && countRatedTasks == tasks.length) {\n        status = HomeworkStatus.NO_COMPLETED_RATED;\n      }\n      if (countCompleted == tasks.length && countRatedTasks > 0 && countRatedTasks < tasks.length) {\n        status = HomeworkStatus.COMPLETED_PARTIALLY_RATED;\n      }\n      if (countCompleted >= 0 && countCompleted < tasks.length && countRatedTasks > 0 && countRatedTasks < tasks.length) {\n        status = HomeworkStatus.PARTIALLY_COMPLETED_PARTIALLY_RATED;\n      }\n      if (countCompleted >= 0 && countCompleted < tasks.length && countRatedTasks == 0) {\n        status = HomeworkStatus.COMPLETED_PARTIALLY_UNRATED;\n      }\n      if (countDismissed > 0) {\n        status += \" Dismissed\";\n      }\n      return status;\n    }\n    initiateHomeworkObject() {\n      this.homeworkToAdd = {\n        title: \"\",\n        description: \"\",\n        homeWorkTasks: [],\n        pinned: false,\n        deadLine: new Date(),\n        status: \"\",\n        submitted: null,\n        assignedTo: [],\n        levels: [],\n        categories: [],\n        state: \"\",\n        classroom: 0\n      };\n      return this.homeworkToAdd;\n    }\n    initiateHomeworkTaskObject() {\n      return {\n        title: \"\",\n        finished: false,\n        teacherFiles: [],\n        studentFiles: [],\n        link: \"\",\n        assignedTo: undefined,\n        assignedToClassroom: undefined,\n        // TODO THIS IS FOR ME ONLY\n        libraries: [],\n        rate: 0,\n        proportion: \"\"\n      };\n    }\n    static #_ = this.ɵfac = function HomeworkService_Factory(t) {\n      return new (t || HomeworkService)(i0.ɵɵinject(i1.UserService), i0.ɵɵinject(i2.LibraryService), i0.ɵɵinject(i3.HttpClient), i0.ɵɵinject(i4.GeneralService));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: HomeworkService,\n      factory: HomeworkService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return HomeworkService;\n})();", "map": {"version": 3, "names": ["BehaviorSubject", "map", "environment", "HomeworkStatus", "BACKEND_URL", "apiUrl", "HomeworkService", "setTagToAll", "tagToAll", "tagToAll$", "next", "constructor", "userService", "libraryService", "http", "generalService", "homeworkToAdd", "asObservable", "submitListener$", "submitListener", "isTaskFormValid$", "isTaskFormValid", "updateListener$", "updateListener", "showFormListener$", "show", "homework", "showFormListener", "setSubmitListener", "listen", "setShowFormListener", "setIsTaskFormValid", "setUpdateListener", "createHomework", "homeworkTasksToSend", "assignedToIds", "user", "assignedTo", "push", "id", "task", "homeWorkTasks", "libraryFileIds", "library", "libraries", "fileId", "homeworkTaskToSend", "title", "finished", "studentFiles", "teacherFiles", "link", "rate", "proportion", "dismissed", "classroomId", "parseInt", "assignedToUserInClassroom", "deadline", "deadLine", "homeworkToSend", "description", "status", "state", "levels", "categories", "post", "updateHomeworkState", "updateHomeworkStatus", "deleteHomework", "deleteTaskFromHomework", "taskId", "compareFn", "a", "b", "getHomeworks", "get", "pipe", "x", "sortByPending", "hw", "Date", "lib", "f", "filePath", "path", "updateTaskFinished", "updateTaskRate", "addStudentFileToTask", "updateTaskProportion", "updateHomework", "updateReq", "addTaskToHomework", "hwId", "updateTaskDetails", "getHomeworkColor", "includes", "COMPLETED_RATED", "COMPLETED_UNRATED", "COMPLETED_PARTIALLY_RATED", "PARTIALLY_COMPLETED_PARTIALLY_RATED", "COMPLETED_PARTIALLY_UNRATED", "PENDING", "getStatusOnUpdateFinishedTaskOrRate", "tasks", "countRatedTasks", "countCompleted", "countDismissed", "length", "PARTIALLY_COMPLETED_RATED", "NO_COMPLETED_RATED", "initiateHomeworkObject", "pinned", "submitted", "classroom", "initiateHomeworkTaskObject", "undefined", "assignedToClassroom", "_", "i0", "ɵɵinject", "i1", "UserService", "i2", "LibraryService", "i3", "HttpClient", "i4", "GeneralService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\services\\homework.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { environment } from 'src/environments/environment';\r\nimport { Level } from '../models/classroom.model';\r\nimport { Category, Homework, HomeworkPostRequest, HomeworkStatus, HomeworkTask, HomeworkTaskFile, HomeworkTaskToSend, Proportion, UpdateHomeworkReq } from '../models/homework.model';\r\nimport { LibraryFile } from '../models/library.model';\r\nimport { User } from '../models/user.model';\r\nimport { GeneralService } from './general.service';\r\nimport { LibraryService } from './library.service';\r\nimport { UserService } from './user.service';\r\nconst BACKEND_URL = environment.apiUrl;\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class HomeworkService {\r\n  public homeworkToAdd: Homework = {} as Homework;\r\n\r\n  private tagToAll$: BehaviorSubject<{ tag: string, add: boolean }> = new BehaviorSubject<{ tag: string, add: boolean }>({} as { tag: string, add: boolean });\r\n  public readonly tagToAll: Observable<{ tag: string, add: boolean }> = this.tagToAll$.asObservable();\r\n\r\n  private submitListener$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);\r\n  public readonly submitListener: Observable<boolean> = this.submitListener$.asObservable();\r\n\r\n  private isTaskFormValid$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);\r\n  public readonly isTaskFormValid: Observable<boolean> = this.isTaskFormValid$.asObservable();\r\n\r\n  private updateListener$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);\r\n  public readonly updateListener: Observable<boolean> = this.updateListener$.asObservable();\r\n\r\n  private showFormListener$: BehaviorSubject<{ show: boolean, homework: Homework }> = new BehaviorSubject<{ show: boolean, homework: Homework }>({ show: false, homework: {} as Homework });\r\n  public readonly showFormListener: Observable<{ show: boolean, homework: Homework }> = this.showFormListener$.asObservable();\r\n\r\n  setTagToAll(tagToAll: { tag: string, add: boolean }) {\r\n    this.tagToAll$.next(tagToAll)\r\n  }\r\n\r\n  constructor(\r\n    private userService: UserService,\r\n    private libraryService: LibraryService,\r\n    private http: HttpClient,\r\n    private generalService: GeneralService\r\n  ) { }\r\n\r\n  setSubmitListener(listen: boolean) {\r\n    this.submitListener$.next(listen);\r\n  }\r\n\r\n  setShowFormListener(listen: boolean, homework: Homework) {\r\n    this.showFormListener$.next({ show: listen, homework: homework });\r\n  }\r\n\r\n  setIsTaskFormValid(listen: boolean) {\r\n    this.isTaskFormValid$.next(listen);\r\n  }\r\n\r\n  setUpdateListener(listen: boolean) {\r\n    this.updateListener$.next(listen);\r\n  }\r\n\r\n  createHomework(homework: Homework) {\r\n    let homeworkTasksToSend: HomeworkTaskToSend[] = []\r\n    let assignedToIds: string[] = [];\r\n    for (let user of homework.assignedTo) {\r\n      assignedToIds.push(user.id)\r\n    }\r\n    for (let task of homework.homeWorkTasks) {\r\n      let libraryFileIds: number[] = []\r\n      for (let library of task.libraries!) {\r\n        libraryFileIds.push(library.fileId!)\r\n      }\r\n      let homeworkTaskToSend = {\r\n        title: task.title,\r\n        finished: false,\r\n        assignedTo: task.assignedTo!.id,\r\n        studentFiles: [],\r\n        teacherFiles: task.teacherFiles,\r\n        link: task.link,\r\n        rate: task.rate,\r\n        proportion: task.proportion,\r\n        dismissed: false,\r\n        libraryFileIds: libraryFileIds,\r\n        classroomId: parseInt(task.assignedToUserInClassroom!)\r\n      }\r\n      homeworkTasksToSend.push(homeworkTaskToSend)\r\n    }\r\n    let deadline = homework.deadLine;\r\n    if(homework.deadLine == null){\r\n      deadline = \"No Deadline\"\r\n    }\r\n    let homeworkToSend: HomeworkPostRequest = {\r\n      title: homework.title,\r\n      description: homework.description,\r\n      deadLine: deadline as string,\r\n      status: homework.status,\r\n      state: homework.state,\r\n      levels: homework.levels,\r\n      categories: homework.categories,\r\n      homeWorkTasks: homeworkTasksToSend\r\n    };\r\n\r\n    return this.http.post(BACKEND_URL + \"/Homework/CreateHomeWork\", homeworkToSend)\r\n  }\r\n\r\n  updateHomeworkState(homework: Homework, state: string) {\r\n    return this.http.post(BACKEND_URL + `/HomeWork/UpdateHomeworkState?HomeworkId=${homework.id}`, state)\r\n  }\r\n\r\n  updateHomeworkStatus(homework: Homework, status: string) {\r\n    return this.http.post(BACKEND_URL + `/HomeWork/UpdateHomeworkStatus?HomeworkId=${homework.id}`, { status: status })\r\n  }\r\n\r\n  deleteHomework(homework: Homework) {\r\n    return this.http.post(BACKEND_URL + `/HomeWork/DeleteHomework?hwId=${homework.id}`, {})\r\n  }\r\n\r\n  deleteTaskFromHomework(taskId: string) {\r\n    return this.http.post(BACKEND_URL + `/HomeWork/DeleteTaskFromHomework?homeWorkTaskId=${taskId}`, {})\r\n  }\r\n\r\n  compareFn(a: Homework, b: Homework) {\r\n    if (a.state == 'Pending' && b.state != 'Pending')\r\n      return -1;\r\n    return 1;\r\n  };\r\n\r\n  getHomeworks(): Observable<Homework[]> {\r\n    return this.http.get<any[]>(BACKEND_URL + `/HomeWork/GetHomeworks`).pipe(\r\n      map(x => {\r\n        let sortByPending: Homework[] = [];\r\n        for (let hw of x) {\r\n          hw.deadLine = hw.deadLine != 'No Deadline' ? new Date(hw.deadLine) : \"No Deadline\"\r\n          for (let task of hw.homeWorkTasks) {\r\n            for (let lib of task.libraries!) {\r\n              let f: HomeworkTaskFile = {\r\n                filePath: lib.path,\r\n                levels: lib.levels,\r\n                categories: lib.categories\r\n              }\r\n              task.teacherFiles.push(f)\r\n            }\r\n          }\r\n          if (hw.status == 'Pending') {\r\n            sortByPending.push(hw)\r\n          }\r\n        }\r\n        for (let hw of x) {\r\n          if (hw.status != 'Pending') {\r\n            sortByPending.push(hw)\r\n          }\r\n        }\r\n        return sortByPending;\r\n      }))\r\n  }\r\n\r\n  updateTaskFinished(finished: boolean, taskId: string) {\r\n    return this.http.post(BACKEND_URL + `​/HomeWork/UpdateTaskFinished?taskId=${taskId}&finished=${finished}`, {})\r\n  }\r\n\r\n  updateTaskRate(rate: number, taskId: string) {\r\n    return this.http.post(BACKEND_URL + `​/HomeWork/UpdateTaskRate?taskId=${taskId}&rate=${rate}`, {})\r\n  }\r\n\r\n  addStudentFileToTask(filePath: number, taskId: string) {\r\n    return this.http.post(BACKEND_URL + `​/HomeWork/UpdateTaskRate?taskId=${taskId}&filePath=${filePath}`, {})\r\n  }\r\n\r\n  updateTaskProportion(proportion: string, taskId: string) {\r\n    return this.http.post(BACKEND_URL + `​/HomeWork/UpdateTaskProportion?taskId=${taskId}&proportion=${proportion}`, {})\r\n  }\r\n\r\n  updateHomework(updateReq: UpdateHomeworkReq) {\r\n    return this.http.post(BACKEND_URL + `​/HomeWork/UpdateHomework?homeWorkId=${updateReq.id}`, updateReq)\r\n  }\r\n\r\n  addTaskToHomework(task: HomeworkTaskToSend, hwId: string) {\r\n    return this.http.post(BACKEND_URL + `​/HomeWork/AddTaskToHomework?homeWorkId=${hwId}`, task)\r\n  }\r\n\r\n  updateTaskDetails(task: HomeworkTaskToSend, hwId: string) {\r\n    return this.http.post(BACKEND_URL + `​/HomeWork/UpdateTaskDetails?homeWorkTaskId=${task.id}`, task)\r\n  }\r\n\r\n  getHomeworkColor(homework: Homework) {\r\n    if (homework.status.includes(HomeworkStatus.COMPLETED_RATED)) {\r\n      return \"#3F37C9\"\r\n    }\r\n    if (homework.status.includes(HomeworkStatus.COMPLETED_UNRATED)) {\r\n      return \"#3F37C9\"\r\n    }\r\n    if (homework.status.includes(HomeworkStatus.COMPLETED_PARTIALLY_RATED)) {\r\n      return \"#3F37C9\"\r\n    }\r\n    if (homework.status.includes(HomeworkStatus.PARTIALLY_COMPLETED_PARTIALLY_RATED)) {\r\n      return \"#4895EF\"\r\n    }\r\n    if (homework.status.includes(HomeworkStatus.COMPLETED_PARTIALLY_UNRATED)) {\r\n      return \"#4895EF\"\r\n    }\r\n    if (homework.status.includes(HomeworkStatus.PENDING)) {\r\n      return \"#7209B7\"\r\n    }\r\n    return \"#4895EF\"\r\n  }\r\n\r\n  getStatusOnUpdateFinishedTaskOrRate(tasks: HomeworkTask[]) {\r\n    let status = \"\";\r\n    let countRatedTasks: number = 0;\r\n    let countCompleted: number = 0;\r\n    let countDismissed: number = 0;\r\n    for (let task of tasks) {\r\n      if (task.rate! > 0) {\r\n        countRatedTasks++\r\n      }\r\n      if (task.finished) {\r\n        countCompleted++\r\n      }\r\n      if (task.dismissed) {\r\n        countCompleted++\r\n      }\r\n    }\r\n    if (countCompleted == 0) {\r\n      status = HomeworkStatus.PENDING\r\n    }\r\n    if (countCompleted == tasks.length && countRatedTasks == tasks.length) {\r\n      status = HomeworkStatus.COMPLETED_RATED\r\n    }\r\n    if (countCompleted == tasks.length && countRatedTasks == 0) {\r\n      status = HomeworkStatus.COMPLETED_UNRATED\r\n    }\r\n    if (countCompleted >= 0 && countCompleted < tasks.length && countRatedTasks == tasks.length) {\r\n      status = HomeworkStatus.PARTIALLY_COMPLETED_RATED\r\n    }\r\n    if (countCompleted == 0 && countCompleted < tasks.length && countRatedTasks == tasks.length) {\r\n      status = HomeworkStatus.NO_COMPLETED_RATED\r\n    }\r\n    if (countCompleted == tasks.length && countRatedTasks > 0 && countRatedTasks < tasks.length) {\r\n      status = HomeworkStatus.COMPLETED_PARTIALLY_RATED\r\n    }\r\n    if (countCompleted >= 0 && countCompleted < tasks.length && countRatedTasks > 0 && countRatedTasks < tasks.length) {\r\n      status = HomeworkStatus.PARTIALLY_COMPLETED_PARTIALLY_RATED\r\n    }\r\n    if (countCompleted >= 0 && countCompleted < tasks.length && countRatedTasks == 0) {\r\n      status = HomeworkStatus.COMPLETED_PARTIALLY_UNRATED\r\n    }\r\n    if (countDismissed > 0) {\r\n      status += \" Dismissed\"\r\n    }\r\n    return status;\r\n  }\r\n\r\n  initiateHomeworkObject(): Homework {\r\n    this.homeworkToAdd = {\r\n      title: \"\",\r\n      description: \"\",\r\n      homeWorkTasks: [],\r\n      pinned: false,\r\n      deadLine: new Date,\r\n      status: \"\",\r\n      submitted: null,\r\n      assignedTo: [],\r\n      levels: [],\r\n      categories: [],\r\n      state: \"\",\r\n      classroom: 0,\r\n    }\r\n    return this.homeworkToAdd\r\n  }\r\n\r\n  initiateHomeworkTaskObject(): HomeworkTask {\r\n    return {\r\n      title: \"\",\r\n      finished: false,\r\n      teacherFiles: [],\r\n      studentFiles: [],\r\n      link: \"\",\r\n      assignedTo: undefined,\r\n      assignedToClassroom: undefined, // TODO THIS IS FOR ME ONLY\r\n      libraries: [],\r\n      rate: 0,\r\n      proportion: \"\"\r\n    }\r\n  }\r\n\r\n\r\n}\r\n"], "mappings": "AAEA,SAASA,eAAe,QAAoB,MAAM;AAClD,SAASC,GAAG,QAAQ,gBAAgB;AACpC,SAASC,WAAW,QAAQ,8BAA8B;AAE1D,SAAkDC,cAAc,QAA2F,0BAA0B;;;;;;AAMrL,MAAMC,WAAW,GAAGF,WAAW,CAACG,MAAM;AAKtC,WAAaC,eAAe;EAAtB,MAAOA,eAAe;IAkB1BC,WAAWA,CAACC,QAAuC;MACjD,IAAI,CAACC,SAAS,CAACC,IAAI,CAACF,QAAQ,CAAC;IAC/B;IAEAG,YACUC,WAAwB,EACxBC,cAA8B,EAC9BC,IAAgB,EAChBC,cAA8B;MAH9B,KAAAH,WAAW,GAAXA,WAAW;MACX,KAAAC,cAAc,GAAdA,cAAc;MACd,KAAAC,IAAI,GAAJA,IAAI;MACJ,KAAAC,cAAc,GAAdA,cAAc;MAzBjB,KAAAC,aAAa,GAAa,EAAc;MAEvC,KAAAP,SAAS,GAAmD,IAAIT,eAAe,CAAgC,EAAmC,CAAC;MAC3I,KAAAQ,QAAQ,GAA8C,IAAI,CAACC,SAAS,CAACQ,YAAY,EAAE;MAE3F,KAAAC,eAAe,GAA6B,IAAIlB,eAAe,CAAU,KAAK,CAAC;MACvE,KAAAmB,cAAc,GAAwB,IAAI,CAACD,eAAe,CAACD,YAAY,EAAE;MAEjF,KAAAG,gBAAgB,GAA6B,IAAIpB,eAAe,CAAU,KAAK,CAAC;MACxE,KAAAqB,eAAe,GAAwB,IAAI,CAACD,gBAAgB,CAACH,YAAY,EAAE;MAEnF,KAAAK,eAAe,GAA6B,IAAItB,eAAe,CAAU,KAAK,CAAC;MACvE,KAAAuB,cAAc,GAAwB,IAAI,CAACD,eAAe,CAACL,YAAY,EAAE;MAEjF,KAAAO,iBAAiB,GAA2D,IAAIxB,eAAe,CAAwC;QAAEyB,IAAI,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAc,CAAE,CAAC;MACzK,KAAAC,gBAAgB,GAAsD,IAAI,CAACH,iBAAiB,CAACP,YAAY,EAAE;IAWvH;IAEJW,iBAAiBA,CAACC,MAAe;MAC/B,IAAI,CAACX,eAAe,CAACR,IAAI,CAACmB,MAAM,CAAC;IACnC;IAEAC,mBAAmBA,CAACD,MAAe,EAAEH,QAAkB;MACrD,IAAI,CAACF,iBAAiB,CAACd,IAAI,CAAC;QAAEe,IAAI,EAAEI,MAAM;QAAEH,QAAQ,EAAEA;MAAQ,CAAE,CAAC;IACnE;IAEAK,kBAAkBA,CAACF,MAAe;MAChC,IAAI,CAACT,gBAAgB,CAACV,IAAI,CAACmB,MAAM,CAAC;IACpC;IAEAG,iBAAiBA,CAACH,MAAe;MAC/B,IAAI,CAACP,eAAe,CAACZ,IAAI,CAACmB,MAAM,CAAC;IACnC;IAEAI,cAAcA,CAACP,QAAkB;MAC/B,IAAIQ,mBAAmB,GAAyB,EAAE;MAClD,IAAIC,aAAa,GAAa,EAAE;MAChC,KAAK,IAAIC,IAAI,IAAIV,QAAQ,CAACW,UAAU,EAAE;QACpCF,aAAa,CAACG,IAAI,CAACF,IAAI,CAACG,EAAE,CAAC;MAC7B;MACA,KAAK,IAAIC,IAAI,IAAId,QAAQ,CAACe,aAAa,EAAE;QACvC,IAAIC,cAAc,GAAa,EAAE;QACjC,KAAK,IAAIC,OAAO,IAAIH,IAAI,CAACI,SAAU,EAAE;UACnCF,cAAc,CAACJ,IAAI,CAACK,OAAO,CAACE,MAAO,CAAC;QACtC;QACA,IAAIC,kBAAkB,GAAG;UACvBC,KAAK,EAAEP,IAAI,CAACO,KAAK;UACjBC,QAAQ,EAAE,KAAK;UACfX,UAAU,EAAEG,IAAI,CAACH,UAAW,CAACE,EAAE;UAC/BU,YAAY,EAAE,EAAE;UAChBC,YAAY,EAAEV,IAAI,CAACU,YAAY;UAC/BC,IAAI,EAAEX,IAAI,CAACW,IAAI;UACfC,IAAI,EAAEZ,IAAI,CAACY,IAAI;UACfC,UAAU,EAAEb,IAAI,CAACa,UAAU;UAC3BC,SAAS,EAAE,KAAK;UAChBZ,cAAc,EAAEA,cAAc;UAC9Ba,WAAW,EAAEC,QAAQ,CAAChB,IAAI,CAACiB,yBAA0B;SACtD;QACDvB,mBAAmB,CAACI,IAAI,CAACQ,kBAAkB,CAAC;MAC9C;MACA,IAAIY,QAAQ,GAAGhC,QAAQ,CAACiC,QAAQ;MAChC,IAAGjC,QAAQ,CAACiC,QAAQ,IAAI,IAAI,EAAC;QAC3BD,QAAQ,GAAG,aAAa;MAC1B;MACA,IAAIE,cAAc,GAAwB;QACxCb,KAAK,EAAErB,QAAQ,CAACqB,KAAK;QACrBc,WAAW,EAAEnC,QAAQ,CAACmC,WAAW;QACjCF,QAAQ,EAAED,QAAkB;QAC5BI,MAAM,EAAEpC,QAAQ,CAACoC,MAAM;QACvBC,KAAK,EAAErC,QAAQ,CAACqC,KAAK;QACrBC,MAAM,EAAEtC,QAAQ,CAACsC,MAAM;QACvBC,UAAU,EAAEvC,QAAQ,CAACuC,UAAU;QAC/BxB,aAAa,EAAEP;OAChB;MAED,OAAO,IAAI,CAACpB,IAAI,CAACoD,IAAI,CAAC9D,WAAW,GAAG,0BAA0B,EAAEwD,cAAc,CAAC;IACjF;IAEAO,mBAAmBA,CAACzC,QAAkB,EAAEqC,KAAa;MACnD,OAAO,IAAI,CAACjD,IAAI,CAACoD,IAAI,CAAC9D,WAAW,GAAG,4CAA4CsB,QAAQ,CAACa,EAAE,EAAE,EAAEwB,KAAK,CAAC;IACvG;IAEAK,oBAAoBA,CAAC1C,QAAkB,EAAEoC,MAAc;MACrD,OAAO,IAAI,CAAChD,IAAI,CAACoD,IAAI,CAAC9D,WAAW,GAAG,6CAA6CsB,QAAQ,CAACa,EAAE,EAAE,EAAE;QAAEuB,MAAM,EAAEA;MAAM,CAAE,CAAC;IACrH;IAEAO,cAAcA,CAAC3C,QAAkB;MAC/B,OAAO,IAAI,CAACZ,IAAI,CAACoD,IAAI,CAAC9D,WAAW,GAAG,iCAAiCsB,QAAQ,CAACa,EAAE,EAAE,EAAE,EAAE,CAAC;IACzF;IAEA+B,sBAAsBA,CAACC,MAAc;MACnC,OAAO,IAAI,CAACzD,IAAI,CAACoD,IAAI,CAAC9D,WAAW,GAAG,mDAAmDmE,MAAM,EAAE,EAAE,EAAE,CAAC;IACtG;IAEAC,SAASA,CAACC,CAAW,EAAEC,CAAW;MAChC,IAAID,CAAC,CAACV,KAAK,IAAI,SAAS,IAAIW,CAAC,CAACX,KAAK,IAAI,SAAS,EAC9C,OAAO,CAAC,CAAC;MACX,OAAO,CAAC;IACV;IAEAY,YAAYA,CAAA;MACV,OAAO,IAAI,CAAC7D,IAAI,CAAC8D,GAAG,CAAQxE,WAAW,GAAG,wBAAwB,CAAC,CAACyE,IAAI,CACtE5E,GAAG,CAAC6E,CAAC,IAAG;QACN,IAAIC,aAAa,GAAe,EAAE;QAClC,KAAK,IAAIC,EAAE,IAAIF,CAAC,EAAE;UAChBE,EAAE,CAACrB,QAAQ,GAAGqB,EAAE,CAACrB,QAAQ,IAAI,aAAa,GAAG,IAAIsB,IAAI,CAACD,EAAE,CAACrB,QAAQ,CAAC,GAAG,aAAa;UAClF,KAAK,IAAInB,IAAI,IAAIwC,EAAE,CAACvC,aAAa,EAAE;YACjC,KAAK,IAAIyC,GAAG,IAAI1C,IAAI,CAACI,SAAU,EAAE;cAC/B,IAAIuC,CAAC,GAAqB;gBACxBC,QAAQ,EAAEF,GAAG,CAACG,IAAI;gBAClBrB,MAAM,EAAEkB,GAAG,CAAClB,MAAM;gBAClBC,UAAU,EAAEiB,GAAG,CAACjB;eACjB;cACDzB,IAAI,CAACU,YAAY,CAACZ,IAAI,CAAC6C,CAAC,CAAC;YAC3B;UACF;UACA,IAAIH,EAAE,CAAClB,MAAM,IAAI,SAAS,EAAE;YAC1BiB,aAAa,CAACzC,IAAI,CAAC0C,EAAE,CAAC;UACxB;QACF;QACA,KAAK,IAAIA,EAAE,IAAIF,CAAC,EAAE;UAChB,IAAIE,EAAE,CAAClB,MAAM,IAAI,SAAS,EAAE;YAC1BiB,aAAa,CAACzC,IAAI,CAAC0C,EAAE,CAAC;UACxB;QACF;QACA,OAAOD,aAAa;MACtB,CAAC,CAAC,CAAC;IACP;IAEAO,kBAAkBA,CAACtC,QAAiB,EAAEuB,MAAc;MAClD,OAAO,IAAI,CAACzD,IAAI,CAACoD,IAAI,CAAC9D,WAAW,GAAG,wCAAwCmE,MAAM,aAAavB,QAAQ,EAAE,EAAE,EAAE,CAAC;IAChH;IAEAuC,cAAcA,CAACnC,IAAY,EAAEmB,MAAc;MACzC,OAAO,IAAI,CAACzD,IAAI,CAACoD,IAAI,CAAC9D,WAAW,GAAG,oCAAoCmE,MAAM,SAASnB,IAAI,EAAE,EAAE,EAAE,CAAC;IACpG;IAEAoC,oBAAoBA,CAACJ,QAAgB,EAAEb,MAAc;MACnD,OAAO,IAAI,CAACzD,IAAI,CAACoD,IAAI,CAAC9D,WAAW,GAAG,oCAAoCmE,MAAM,aAAaa,QAAQ,EAAE,EAAE,EAAE,CAAC;IAC5G;IAEAK,oBAAoBA,CAACpC,UAAkB,EAAEkB,MAAc;MACrD,OAAO,IAAI,CAACzD,IAAI,CAACoD,IAAI,CAAC9D,WAAW,GAAG,0CAA0CmE,MAAM,eAAelB,UAAU,EAAE,EAAE,EAAE,CAAC;IACtH;IAEAqC,cAAcA,CAACC,SAA4B;MACzC,OAAO,IAAI,CAAC7E,IAAI,CAACoD,IAAI,CAAC9D,WAAW,GAAG,wCAAwCuF,SAAS,CAACpD,EAAE,EAAE,EAAEoD,SAAS,CAAC;IACxG;IAEAC,iBAAiBA,CAACpD,IAAwB,EAAEqD,IAAY;MACtD,OAAO,IAAI,CAAC/E,IAAI,CAACoD,IAAI,CAAC9D,WAAW,GAAG,2CAA2CyF,IAAI,EAAE,EAAErD,IAAI,CAAC;IAC9F;IAEAsD,iBAAiBA,CAACtD,IAAwB,EAAEqD,IAAY;MACtD,OAAO,IAAI,CAAC/E,IAAI,CAACoD,IAAI,CAAC9D,WAAW,GAAG,+CAA+CoC,IAAI,CAACD,EAAE,EAAE,EAAEC,IAAI,CAAC;IACrG;IAEAuD,gBAAgBA,CAACrE,QAAkB;MACjC,IAAIA,QAAQ,CAACoC,MAAM,CAACkC,QAAQ,CAAC7F,cAAc,CAAC8F,eAAe,CAAC,EAAE;QAC5D,OAAO,SAAS;MAClB;MACA,IAAIvE,QAAQ,CAACoC,MAAM,CAACkC,QAAQ,CAAC7F,cAAc,CAAC+F,iBAAiB,CAAC,EAAE;QAC9D,OAAO,SAAS;MAClB;MACA,IAAIxE,QAAQ,CAACoC,MAAM,CAACkC,QAAQ,CAAC7F,cAAc,CAACgG,yBAAyB,CAAC,EAAE;QACtE,OAAO,SAAS;MAClB;MACA,IAAIzE,QAAQ,CAACoC,MAAM,CAACkC,QAAQ,CAAC7F,cAAc,CAACiG,mCAAmC,CAAC,EAAE;QAChF,OAAO,SAAS;MAClB;MACA,IAAI1E,QAAQ,CAACoC,MAAM,CAACkC,QAAQ,CAAC7F,cAAc,CAACkG,2BAA2B,CAAC,EAAE;QACxE,OAAO,SAAS;MAClB;MACA,IAAI3E,QAAQ,CAACoC,MAAM,CAACkC,QAAQ,CAAC7F,cAAc,CAACmG,OAAO,CAAC,EAAE;QACpD,OAAO,SAAS;MAClB;MACA,OAAO,SAAS;IAClB;IAEAC,mCAAmCA,CAACC,KAAqB;MACvD,IAAI1C,MAAM,GAAG,EAAE;MACf,IAAI2C,eAAe,GAAW,CAAC;MAC/B,IAAIC,cAAc,GAAW,CAAC;MAC9B,IAAIC,cAAc,GAAW,CAAC;MAC9B,KAAK,IAAInE,IAAI,IAAIgE,KAAK,EAAE;QACtB,IAAIhE,IAAI,CAACY,IAAK,GAAG,CAAC,EAAE;UAClBqD,eAAe,EAAE;QACnB;QACA,IAAIjE,IAAI,CAACQ,QAAQ,EAAE;UACjB0D,cAAc,EAAE;QAClB;QACA,IAAIlE,IAAI,CAACc,SAAS,EAAE;UAClBoD,cAAc,EAAE;QAClB;MACF;MACA,IAAIA,cAAc,IAAI,CAAC,EAAE;QACvB5C,MAAM,GAAG3D,cAAc,CAACmG,OAAO;MACjC;MACA,IAAII,cAAc,IAAIF,KAAK,CAACI,MAAM,IAAIH,eAAe,IAAID,KAAK,CAACI,MAAM,EAAE;QACrE9C,MAAM,GAAG3D,cAAc,CAAC8F,eAAe;MACzC;MACA,IAAIS,cAAc,IAAIF,KAAK,CAACI,MAAM,IAAIH,eAAe,IAAI,CAAC,EAAE;QAC1D3C,MAAM,GAAG3D,cAAc,CAAC+F,iBAAiB;MAC3C;MACA,IAAIQ,cAAc,IAAI,CAAC,IAAIA,cAAc,GAAGF,KAAK,CAACI,MAAM,IAAIH,eAAe,IAAID,KAAK,CAACI,MAAM,EAAE;QAC3F9C,MAAM,GAAG3D,cAAc,CAAC0G,yBAAyB;MACnD;MACA,IAAIH,cAAc,IAAI,CAAC,IAAIA,cAAc,GAAGF,KAAK,CAACI,MAAM,IAAIH,eAAe,IAAID,KAAK,CAACI,MAAM,EAAE;QAC3F9C,MAAM,GAAG3D,cAAc,CAAC2G,kBAAkB;MAC5C;MACA,IAAIJ,cAAc,IAAIF,KAAK,CAACI,MAAM,IAAIH,eAAe,GAAG,CAAC,IAAIA,eAAe,GAAGD,KAAK,CAACI,MAAM,EAAE;QAC3F9C,MAAM,GAAG3D,cAAc,CAACgG,yBAAyB;MACnD;MACA,IAAIO,cAAc,IAAI,CAAC,IAAIA,cAAc,GAAGF,KAAK,CAACI,MAAM,IAAIH,eAAe,GAAG,CAAC,IAAIA,eAAe,GAAGD,KAAK,CAACI,MAAM,EAAE;QACjH9C,MAAM,GAAG3D,cAAc,CAACiG,mCAAmC;MAC7D;MACA,IAAIM,cAAc,IAAI,CAAC,IAAIA,cAAc,GAAGF,KAAK,CAACI,MAAM,IAAIH,eAAe,IAAI,CAAC,EAAE;QAChF3C,MAAM,GAAG3D,cAAc,CAACkG,2BAA2B;MACrD;MACA,IAAIM,cAAc,GAAG,CAAC,EAAE;QACtB7C,MAAM,IAAI,YAAY;MACxB;MACA,OAAOA,MAAM;IACf;IAEAiD,sBAAsBA,CAAA;MACpB,IAAI,CAAC/F,aAAa,GAAG;QACnB+B,KAAK,EAAE,EAAE;QACTc,WAAW,EAAE,EAAE;QACfpB,aAAa,EAAE,EAAE;QACjBuE,MAAM,EAAE,KAAK;QACbrD,QAAQ,EAAE,IAAIsB,IAAI,CAAJ,CAAI;QAClBnB,MAAM,EAAE,EAAE;QACVmD,SAAS,EAAE,IAAI;QACf5E,UAAU,EAAE,EAAE;QACd2B,MAAM,EAAE,EAAE;QACVC,UAAU,EAAE,EAAE;QACdF,KAAK,EAAE,EAAE;QACTmD,SAAS,EAAE;OACZ;MACD,OAAO,IAAI,CAAClG,aAAa;IAC3B;IAEAmG,0BAA0BA,CAAA;MACxB,OAAO;QACLpE,KAAK,EAAE,EAAE;QACTC,QAAQ,EAAE,KAAK;QACfE,YAAY,EAAE,EAAE;QAChBD,YAAY,EAAE,EAAE;QAChBE,IAAI,EAAE,EAAE;QACRd,UAAU,EAAE+E,SAAS;QACrBC,mBAAmB,EAAED,SAAS;QAAE;QAChCxE,SAAS,EAAE,EAAE;QACbQ,IAAI,EAAE,CAAC;QACPC,UAAU,EAAE;OACb;IACH;IAAC,QAAAiE,CAAA,G;uBA3QUhH,eAAe,EAAAiH,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,UAAA,GAAAP,EAAA,CAAAC,QAAA,CAAAO,EAAA,CAAAC,cAAA;IAAA;IAAA,QAAAC,EAAA,G;aAAf3H,eAAe;MAAA4H,OAAA,EAAf5H,eAAe,CAAA6H,IAAA;MAAAC,UAAA,EAFd;IAAM;;SAEP9H,eAAe;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}