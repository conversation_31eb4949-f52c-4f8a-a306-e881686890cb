{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { forkJoin, of } from 'rxjs';\nimport { catchError, finalize, map, skip, switchMap, take } from 'rxjs/operators';\nimport { Status } from 'src/app/core/models/classroom.model';\nimport { LessonStatus } from 'src/app/core/models/lesson.model';\nimport { StudentTrialStatus } from 'src/app/core/models/student.model';\nimport { UserRole } from 'src/app/core/models/user.model';\nimport { SubSink } from 'subsink';\nimport * as data from '../../core/models/data';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nimport * as i2 from \"src/app/core/services/auth.service\";\nimport * as i3 from \"src/app/core/services/lesson.service\";\nimport * as i4 from \"src/app/core/services/blog.service\";\nimport * as i5 from \"src/app/core/services/tutorial.service\";\nimport * as i6 from \"src/app/core/services/guide.service\";\nimport * as i7 from \"src/app/core/services/goal.service\";\nimport * as i8 from \"src/app/core/services/notification.service\";\nimport * as i9 from \"src/app/core/services/classroom.service\";\nimport * as i10 from \"src/app/core/services/student-trial.service\";\nimport * as i11 from \"src/app/core/services/calendar.service\";\nimport * as i12 from \"src/app/core/services/user.service\";\nimport * as i13 from \"src/app/core/services/layout.service\";\nimport * as i14 from \"src/app/core/services/toast.service\";\nimport * as i15 from \"src/app/core/services/jitsi-meet.service\";\nimport * as i16 from \"src/app/core/services/rating-and-report.service\";\nimport * as i17 from \"@angular/common\";\nimport * as i18 from \"../classroom/next-lesson/next-lesson.component\";\nimport * as i19 from \"../../shared/block-viewer/block-viewer.component\";\nimport * as i20 from \"../../shared/prime/prime/calendar-agenda/calendar-agenda.component\";\nimport * as i21 from \"../../shared/lesson/lesson-schedule-box/lesson-schedule-box.component\";\nimport * as i22 from \"../../shared/goal/goal-steps-box/goal-steps-box.component\";\nimport * as i23 from \"../../shared/prime/text-call-to-action-button/text-call-to-action-button.component\";\nimport * as i24 from \"../../shared/classroom/classroom-card/classroom-card.component\";\nimport * as i25 from \"primeng/api\";\nimport * as i26 from \"../../shared/classroom/classroom-status-filters/classroom-status-filters.component\";\nimport * as i27 from \"primeng/scrollpanel\";\nimport * as i28 from \"primeng/carousel\";\nimport * as i29 from \"./intro-gradient/intro-gradient.component\";\nimport * as i30 from \"./dashboard-notifications/dashboard-notifications.component\";\nimport * as i31 from \"./dashboard-next-steps/dashboard-next-steps.component\";\nimport * as i32 from \"./dashboard-benefits/dashboard-benefits.component\";\nimport * as i33 from \"./calendar-preview-week/calendar-preview-week.component\";\nimport * as i34 from \"./dashboard-contact-us-form/dashboard-contact-us-form.component\";\nimport * as i35 from \"./dashboard-teacher-classrooms/dashboard-teacher-classrooms.component\";\nimport * as i36 from \"../../shared/loader/loader.component\";\nimport * as i37 from \"./dashboard-request-trial-form/dashboard-request-trial-form.component\";\nimport * as i38 from \"./redirect-to-galaxy/redirect-to-galaxy.component\";\nconst _c0 = [\"ignoredOrLostTrialStatusTemplate\"];\nconst _c1 = [\"newOrForwaredTrialStatusTemplate\"];\nconst _c2 = [\"paidOrActiveTemplate\"];\nconst _c3 = [\"classroomCardsWrapper\"];\nconst DashboardComponent_Defer_2_DepsFn = () => [i17.NgIf, i17.NgTemplateOutlet, i17.NgSwitch, i17.NgSwitchCase, i17.NgSwitchDefault, i19.BlockViewerComponent, i20.CalendarAgendaComponent, i21.LessonScheduleBoxComponent, i22.GoalStepsBoxComponent, i23.TextCallToActionButtonComponent, i36.LoaderComponent, i24.ClassroomCardComponent, i25.PrimeTemplate, i27.ScrollPanel, i28.Carousel, i29.IntroGradientComponent, i30.DashboardNotificationsComponent, i31.DashboardNextStepsComponent, i32.DashboardBenefitsComponent, i37.DashboardRequestTrialFormComponent, i33.CalendarPreviewWeekComponent, i38.RedirectToGalaxyComponent, i17.AsyncPipe];\nconst _c4 = () => ({\n  width: \"100%\",\n  height: \"118px\"\n});\nconst _c5 = () => [];\nconst _c6 = a0 => ({\n  $implicit: a0\n});\nconst _c7 = () => ({});\nconst _c8 = () => ({\n  icon: \"/assets/images/dashboard/next-steps-1-icon.svg\",\n  title: \"Chose a package and begin learning!\",\n  description: \"Once you you have purchased a package, we will contact you for more information regarding your level, your availability, your goals, and any specific difficulties or requirements. That way, we can match you with the most suitable of our teachers.\"\n});\nconst _c9 = () => ({\n  icon: \"/assets/images/dashboard/next-steps-1-icon.svg\",\n  title: \"Arrange your first trial lesson\",\n  description: \"Once you you have purchased a package, we will contact you for more information regarding your level, your availability, your goals, and any specific difficulties or requirements. That way, we can match you with the most suitable of our teachers.\"\n});\nconst _c10 = () => ({\n  width: \"100%\",\n  height: \"119px\"\n});\nfunction DashboardComponent_Defer_0_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 13)(2, \"div\", 14);\n    i0.ɵɵelement(3, \"app-loader\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DashboardComponent_Defer_0_ng_container_2_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    const teacherTemplate_r3 = i0.ɵɵreference(27);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", teacherTemplate_r3)(\"ngTemplateOutletContext\", ctx_r1.templateContext);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_2_ng_container_1_Template, 1, 0, \"ng-container\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    const newOrForwaredTrialStatusTemplate_r4 = i0.ɵɵreference(15);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", newOrForwaredTrialStatusTemplate_r4)(\"ngTemplateOutletContext\", ctx_r1.templateContext);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_3_ng_container_1_Template, 1, 0, \"ng-container\", 19);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(5);\n    const ignoredOrLostTrialStatusTemplate_r5 = i0.ɵɵreference(17);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ignoredOrLostTrialStatusTemplate_r5);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_4_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_4_ng_container_1_Template, 1, 0, \"ng-container\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    const paidOrActiveTemplate_r6 = i0.ɵɵreference(19);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", paidOrActiveTemplate_r6)(\"ngTemplateOutletContext\", ctx_r1.templateContext);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_5_app_redirect_to_galaxy_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-redirect-to-galaxy\");\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_5_app_redirect_to_galaxy_1_Template, 1, 0, \"app-redirect-to-galaxy\", 11);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const status_r7 = i0.ɵɵnextContext().ngIf;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", status_r7.toLowerCase() === ctx_r1.StudentStatus.GALAXY.toLowerCase());\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_6_p_carousel_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-intro-gradient\", 35);\n  }\n  if (rf & 2) {\n    const banner_r9 = ctx.$implicit;\n    i0.ɵɵproperty(\"bannerMode\", banner_r9.bannerMode);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_6_p_carousel_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-carousel\", 33);\n    i0.ɵɵtemplate(1, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_6_p_carousel_3_ng_template_1_Template, 1, 1, \"ng-template\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(6);\n    i0.ɵɵproperty(\"value\", ctx_r1.banners)(\"autoplayInterval\", 5000)(\"verticalViewPortHeight\", \"130px\")(\"orientation\", \"vertical\")(\"numVisible\", 1)(\"page\", 1)(\"numScroll\", 1)(\"showNavigators\", false)(\"showIndicators\", ctx_r1.banners.length > 1 ? true : false)(\"circular\", ctx_r1.banners.length > 1 ? true : false);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 20)(2, \"div\", 21);\n    i0.ɵɵtemplate(3, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_6_p_carousel_3_Template, 2, 10, \"p-carousel\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 20)(5, \"div\", 23);\n    i0.ɵɵelement(6, \"app-goal-steps-box\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 23)(8, \"app-block-viewer\", 25)(9, \"p-scrollPanel\", 26);\n    i0.ɵɵelement(10, \"app-dashboard-notifications\", 27);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 20)(12, \"div\", 28)(13, \"app-calendar-agenda\", 29);\n    i0.ɵɵlistener(\"dayClicked\", function DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_6_Template_app_calendar_agenda_dayClicked_13_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.onAgendaDayClicked($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 23);\n    i0.ɵɵelement(15, \"app-lesson-schedule-box\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(16, \"app-classroom-card\", 31)(17, \"app-dashboard-next-steps\", 32)(18, \"app-dashboard-benefits\")(19, \"app-goal-steps-box\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.banners.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"isSmall\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(10, _c4));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"slimStyle\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"lessons\", ctx_r1.context.lessons);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"lessons\", i0.ɵɵpureFunction0(11, _c5))(\"disableAddNewLessonButton\", true)(\"currentDate\", ctx_r1.currentAgendaDate);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"activeStep\", 2);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_7_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_7_ng_container_1_Template, 1, 0, \"ng-container\", 19);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(5);\n    const ignoredOrLostTrialStatusTemplate_r5 = i0.ɵɵreference(17);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ignoredOrLostTrialStatusTemplate_r5);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_8_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_8_ng_container_1_Template, 1, 0, \"ng-container\", 15);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    const newOrForwaredTrialStatusTemplate_r4 = i0.ɵɵreference(15);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", newOrForwaredTrialStatusTemplate_r4)(\"ngTemplateOutletContext\", ctx_r1.templateContext);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_9_app_dashboard_request_trial_form_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-dashboard-request-trial-form\", 39);\n    i0.ɵɵlistener(\"formSubmitted\", function DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_9_app_dashboard_request_trial_form_8_Template_app_dashboard_request_trial_form_formSubmitted_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(6);\n      return i0.ɵɵresetView(ctx_r1.onTrialRequestFormSubmitted($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 20)(2, \"div\", 23);\n    i0.ɵɵelement(3, \"app-calendar-preview-week\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 23)(5, \"app-block-viewer\", 36)(6, \"p-scrollPanel\", 26);\n    i0.ɵɵelement(7, \"app-dashboard-notifications\", 27);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(8, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_9_app_dashboard_request_trial_form_8_Template, 1, 0, \"app-dashboard-request-trial-form\", 37);\n    i0.ɵɵelementStart(9, \"div\", 38);\n    i0.ɵɵelement(10, \"app-dashboard-next-steps\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 38);\n    i0.ɵɵelement(12, \"app-dashboard-benefits\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"containerHeight\", 118);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(5, _c4));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"slimStyle\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.studentHasTrialRequest);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \" Unknown status. \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵelementContainerStart(1, 16);\n    i0.ɵɵtemplate(2, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_2_Template, 2, 2, \"ng-container\", 17)(3, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_3_Template, 2, 1, \"ng-container\", 17)(4, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_4_Template, 2, 2, \"ng-container\", 17)(5, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_5_Template, 2, 1, \"ng-container\", 17)(6, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_6_Template, 20, 12, \"ng-container\", 17)(7, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_7_Template, 2, 1, \"ng-container\", 17)(8, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_8_Template, 2, 2, \"ng-container\", 17)(9, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_9_Template, 13, 6, \"ng-container\", 17)(10, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_10_Template, 2, 0, \"ng-container\", 18);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r7 = ctx.ngIf;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitch\", status_r7);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.StudentStatus.FORWARDED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.StudentStatus.IGNORED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.StudentStatus.HAS_PAYMENT);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.StudentStatus.GALAXY);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.StudentStatus.ASSIGNED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.StudentStatus.LOST);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.StudentStatus.NEW);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", ctx_r1.StudentStatus.NONE);\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_Template, 11, 9, \"div\", 11);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelement(3, \"app-text-call-to-action-button\");\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(2, 1, ctx_r1.studentStatus$));\n  }\n}\nfunction DashboardComponent_Defer_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, DashboardComponent_Defer_0_ng_container_2_ng_container_1_Template, 2, 2, \"ng-container\", 11)(2, DashboardComponent_Defer_0_ng_container_2_ng_container_2_Template, 4, 3, \"ng-container\", 11);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.role == ctx_r1.UserRoles.TEACHER);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.role == ctx_r1.UserRoles.STUDENT);\n  }\n}\nfunction DashboardComponent_Defer_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, DashboardComponent_Defer_0_ng_container_0_Template, 4, 0, \"ng-container\", 11);\n    i0.ɵɵelementStart(1, \"div\", 12);\n    i0.ɵɵtemplate(2, DashboardComponent_Defer_0_ng_container_2_Template, 3, 2, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showLoader);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.showLoader);\n  }\n}\nfunction DashboardComponent_DeferPlaceholder_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"loading...\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_ng_template_14_p_carousel_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-intro-gradient\", 44);\n  }\n  if (rf & 2) {\n    let tmp_12_0;\n    const banner_r12 = ctx.$implicit;\n    const context_r13 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"classRoom\", (tmp_12_0 = banner_r12.classRoom) !== null && tmp_12_0 !== undefined ? tmp_12_0 : i0.ɵɵpureFunction0(3, _c7))(\"teacher\", context_r13 && context_r13.trialClassRooms.length > 0 ? context_r13.trialClassRooms[0].teacher : null)(\"bannerMode\", banner_r12.bannerMode);\n  }\n}\nfunction DashboardComponent_ng_template_14_p_carousel_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-carousel\", 43);\n    i0.ɵɵtemplate(1, DashboardComponent_ng_template_14_p_carousel_2_ng_template_1_Template, 1, 4, \"ng-template\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", ctx_r1.banners)(\"autoplayInterval\", 5000)(\"responsiveOptions\", ctx_r1.responsiveOptions)(\"verticalViewPortHeight\", \"130px\")(\"numVisible\", 1)(\"page\", 1)(\"numScroll\", 1)(\"showNavigators\", false)(\"showIndicators\", ctx_r1.banners.length > 1 ? true : false)(\"circular\", ctx_r1.banners.length > 1 ? true : false);\n  }\n}\nfunction DashboardComponent_ng_template_14_ng_container_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction DashboardComponent_ng_template_14_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction DashboardComponent_ng_template_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21);\n    i0.ɵɵtemplate(2, DashboardComponent_ng_template_14_p_carousel_2_Template, 2, 10, \"p-carousel\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 20)(4, \"div\", 23);\n    i0.ɵɵelement(5, \"app-goal-steps-box\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 23)(7, \"app-block-viewer\", 41)(8, \"p-scrollPanel\", 26);\n    i0.ɵɵelement(9, \"app-dashboard-notifications\", 27);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(10, \"div\", 20)(11, \"div\", 28)(12, \"app-calendar-agenda\", 29);\n    i0.ɵɵlistener(\"dayClicked\", function DashboardComponent_ng_template_14_Template_app_calendar_agenda_dayClicked_12_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAgendaDayClicked($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 23);\n    i0.ɵɵtemplate(14, DashboardComponent_ng_template_14_ng_container_14_Template, 1, 0, \"ng-container\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(15, DashboardComponent_ng_template_14_ng_container_15_Template, 1, 0, \"ng-container\", 15);\n    i0.ɵɵelementStart(16, \"div\", 42);\n    i0.ɵɵelement(17, \"app-dashboard-next-steps\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 38);\n    i0.ɵɵelement(19, \"app-dashboard-benefits\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"div\", 38);\n    i0.ɵɵelement(21, \"app-goal-steps-box\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const context_r13 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    const classroomCardsTemplate_r14 = i0.ɵɵreference(21);\n    const lessonsBoxTemplate_r15 = i0.ɵɵreference(23);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.banners.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"isSmall\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"containerHeight\", 119);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(12, _c4));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"slimStyle\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"lessons\", context_r13.lessons);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", lessonsBoxTemplate_r15)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(13, _c6, ctx_r1.templateContext.$implicit));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", classroomCardsTemplate_r14)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(15, _c6, ctx_r1.templateContext.$implicit));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"activeStep\", ctx_r1.getNextStepNumber(context_r13.trialClassRooms));\n  }\n}\nfunction DashboardComponent_ng_template_16_p_carousel_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-intro-gradient\", 35);\n  }\n  if (rf & 2) {\n    const banner_r16 = ctx.$implicit;\n    i0.ɵɵproperty(\"bannerMode\", banner_r16.bannerMode);\n  }\n}\nfunction DashboardComponent_ng_template_16_p_carousel_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-carousel\", 33);\n    i0.ɵɵtemplate(1, DashboardComponent_ng_template_16_p_carousel_2_ng_template_1_Template, 1, 1, \"ng-template\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", ctx_r1.banners)(\"autoplayInterval\", 5000)(\"verticalViewPortHeight\", \"130px\")(\"orientation\", \"vertical\")(\"numVisible\", 1)(\"page\", 1)(\"numScroll\", 1)(\"showNavigators\", false)(\"showIndicators\", ctx_r1.banners.length > 1 ? true : false)(\"circular\", ctx_r1.banners.length > 1 ? true : false);\n  }\n}\nfunction DashboardComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21);\n    i0.ɵɵtemplate(2, DashboardComponent_ng_template_16_p_carousel_2_Template, 2, 10, \"p-carousel\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 20)(4, \"div\", 23);\n    i0.ɵɵelement(5, \"app-calendar-preview-week\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 23)(7, \"app-block-viewer\", 45)(8, \"p-scrollPanel\", 26);\n    i0.ɵɵelement(9, \"app-dashboard-notifications\", 27);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelement(10, \"app-dashboard-contact-us-form\")(11, \"app-dashboard-benefits\");\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.banners.length > 0);\n    i0.ɵɵadvance(6);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(4, _c4));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"slimStyle\", true);\n  }\n}\nfunction DashboardComponent_ng_template_18_p_carousel_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-intro-gradient\", 51);\n  }\n  if (rf & 2) {\n    const banner_r18 = ctx.$implicit;\n    i0.ɵɵproperty(\"classRoom\", banner_r18.classRoom)(\"bannerMode\", banner_r18.bannerMode);\n  }\n}\nfunction DashboardComponent_ng_template_18_p_carousel_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-carousel\", 50);\n    i0.ɵɵtemplate(1, DashboardComponent_ng_template_18_p_carousel_2_ng_template_1_Template, 1, 2, \"ng-template\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", ctx_r1.banners)(\"autoplayInterval\", 5000)(\"verticalViewPortHeight\", \"130px\")(\"showNavigators\", false)(\"showIndicators\", ctx_r1.banners.length > 1 ? true : false)(\"circular\", ctx_r1.banners.length > 1 ? true : false);\n  }\n}\nfunction DashboardComponent_ng_template_18_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction DashboardComponent_ng_template_18_ng_container_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction DashboardComponent_ng_template_18_ng_container_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 38);\n    i0.ɵɵelement(2, \"app-dashboard-next-steps\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"app-dashboard-benefits\");\n    i0.ɵɵelementStart(4, \"div\", 38);\n    i0.ɵɵelement(5, \"app-goal-steps-box\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const context_r19 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"activeStep\", context_r19.trialClassRooms.length > 0 ? 3 : 2)(\"stepOneInfo\", i0.ɵɵpureFunction0(3, _c8))(\"stepThreeInfo\", i0.ɵɵpureFunction0(4, _c9));\n  }\n}\nfunction DashboardComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21);\n    i0.ɵɵtemplate(2, DashboardComponent_ng_template_18_p_carousel_2_Template, 2, 6, \"p-carousel\", 46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 20)(4, \"div\", 23)(5, \"app-block-viewer\", 47);\n    i0.ɵɵelement(6, \"app-next-lesson\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 23)(8, \"app-block-viewer\", 49)(9, \"p-scrollPanel\", 26);\n    i0.ɵɵelement(10, \"app-dashboard-notifications\", 27);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 20)(12, \"div\", 28)(13, \"app-calendar-agenda\", 29);\n    i0.ɵɵlistener(\"dayClicked\", function DashboardComponent_ng_template_18_Template_app_calendar_agenda_dayClicked_13_listener($event) {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAgendaDayClicked($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 28);\n    i0.ɵɵtemplate(15, DashboardComponent_ng_template_18_ng_container_15_Template, 1, 0, \"ng-container\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, DashboardComponent_ng_template_18_ng_container_16_Template, 1, 0, \"ng-container\", 15)(17, DashboardComponent_ng_template_18_ng_container_17_Template, 6, 5, \"ng-container\", 11);\n  }\n  if (rf & 2) {\n    const context_r19 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    const classroomCardsTemplate_r14 = i0.ɵɵreference(21);\n    const lessonsBoxTemplate_r15 = i0.ɵɵreference(23);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.banners.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"containerHeight\", 129);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"lessons\", context_r19.lessons)(\"todayLessons\", context_r19.todaysLessons);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(13, _c4));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"slimStyle\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"lessons\", context_r19.lessons);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", lessonsBoxTemplate_r15)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(14, _c6, ctx_r1.templateContext.$implicit));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", classroomCardsTemplate_r14)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(16, _c6, ctx_r1.templateContext.$implicit));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", context_r19.trialClassRooms.length === 0);\n  }\n}\nfunction DashboardComponent_ng_template_20_ng_container_0_ng_container_3_ng_container_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 64);\n    i0.ɵɵelement(2, \"app-classroom-card\", 65);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const trialClassroom_r21 = ctx.$implicit;\n    const index_r22 = ctx.i;\n    const context_r23 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", context_r23.filteredTrialClassRooms.length === 1 ? \"mt-3\" : index_r22 > 0 ? \"classroom-cards-space\" : \"mt-3\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"classRoom\", trialClassroom_r21);\n  }\n}\nfunction DashboardComponent_ng_template_20_ng_container_0_ng_container_3_ng_container_11_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-classroom-card\", 69);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const trialClassroom_r24 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"classRoom\", trialClassroom_r24);\n  }\n}\nfunction DashboardComponent_ng_template_20_ng_container_0_ng_container_3_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 66);\n    i0.ɵɵelementContainerStart(2);\n    i0.ɵɵelementStart(3, \"div\", 67);\n    i0.ɵɵelement(4, \"img\", 68);\n    i0.ɵɵelementStart(5, \"strong\");\n    i0.ɵɵtext(6, \"No classrooms found.\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(7, DashboardComponent_ng_template_20_ng_container_0_ng_container_3_ng_container_11_ng_container_7_Template, 2, 1, \"ng-container\", 63);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.filteredClassrooms);\n  }\n}\nfunction DashboardComponent_ng_template_20_ng_container_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r20 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 55)(2, \"div\", 56)(3, \"div\", 57);\n    i0.ɵɵelement(4, \"img\", 58);\n    i0.ɵɵelementStart(5, \"div\", 59)(6, \"h2\", 60);\n    i0.ɵɵtext(7, \"Classrooms\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 61)(9, \"app-classroom-status-filters\", 62);\n    i0.ɵɵlistener(\"switchToggled\", function DashboardComponent_ng_template_20_ng_container_0_ng_container_3_Template_app_classroom_status_filters_switchToggled_9_listener($event) {\n      i0.ɵɵrestoreView(_r20);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.toggleSwitch($event));\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(10, DashboardComponent_ng_template_20_ng_container_0_ng_container_3_ng_container_10_Template, 3, 2, \"ng-container\", 63)(11, DashboardComponent_ng_template_20_ng_container_0_ng_container_3_ng_container_11_Template, 8, 1, \"ng-container\", 11);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const context_r23 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"isTrial\", false)(\"switches\", ctx_r1.switches);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", context_r23.filteredTrialClassRooms);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", context_r23.filteredTrialClassRooms.length === 0);\n  }\n}\nfunction DashboardComponent_ng_template_20_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 53)(2, \"div\", 54);\n    i0.ɵɵtemplate(3, DashboardComponent_ng_template_20_ng_container_0_ng_container_3_Template, 12, 4, \"ng-container\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const context_r23 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", context_r23 && context_r23.trialClassRooms);\n  }\n}\nfunction DashboardComponent_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, DashboardComponent_ng_template_20_ng_container_0_Template, 4, 1, \"ng-container\", 11);\n  }\n  if (rf & 2) {\n    const context_r23 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", context_r23 && context_r23.trialClassRooms && context_r23.trialClassRooms.length > 0);\n  }\n}\nfunction DashboardComponent_ng_template_22_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-block-viewer\", 70);\n    i0.ɵɵelement(2, \"app-lesson-schedule-box\", 71);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const context_r25 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"containerHeight\", 386);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"scrollHeight\", 340)(\"classRooms\", context_r25.trialClassRooms)(\"lessons\", context_r25.lessons)(\"disableAddNewLessonButton\", context_r25.classRooms.length === 0 && context_r25.trialClassRooms.length === 0);\n  }\n}\nfunction DashboardComponent_ng_template_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, DashboardComponent_ng_template_22_ng_container_0_Template, 3, 5, \"ng-container\", 11);\n  }\n  if (rf & 2) {\n    const context_r25 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", context_r25 && context_r25.trialClassRooms);\n  }\n}\nfunction DashboardComponent_ng_template_24_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-lesson-schedule-box\", 72);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const context_r26 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"classRooms\", context_r26.classRooms)(\"lessons\", context_r26.lessons)(\"disableAddNewLessonButton\", context_r26.classRooms.length === 0);\n  }\n}\nfunction DashboardComponent_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"app-block-viewer\", 70);\n    i0.ɵɵtemplate(1, DashboardComponent_ng_template_24_ng_container_1_Template, 2, 3, \"ng-container\", 11);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const context_r26 = ctx.$implicit;\n    i0.ɵɵproperty(\"containerHeight\", 500);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", context_r26.classRooms);\n  }\n}\nfunction DashboardComponent_ng_template_26_p_carousel_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-intro-gradient\", 77);\n  }\n  if (rf & 2) {\n    const banner_r28 = ctx.$implicit;\n    const context_r29 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"teacher\", context_r29 && context_r29.trialClassRooms.length > 0 ? context_r29.trialClassRooms[0].teacher : null)(\"bannerMode\", banner_r28.bannerMode);\n  }\n}\nfunction DashboardComponent_ng_template_26_p_carousel_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-carousel\", 43);\n    i0.ɵɵtemplate(1, DashboardComponent_ng_template_26_p_carousel_2_ng_template_1_Template, 1, 2, \"ng-template\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", ctx_r1.banners)(\"autoplayInterval\", 5000)(\"responsiveOptions\", ctx_r1.responsiveOptions)(\"verticalViewPortHeight\", \"130px\")(\"numVisible\", 1)(\"page\", 1)(\"numScroll\", 1)(\"showNavigators\", false)(\"showIndicators\", ctx_r1.banners.length > 1 ? true : false)(\"circular\", ctx_r1.banners.length > 1 ? true : false);\n  }\n}\nfunction DashboardComponent_ng_template_26_ng_container_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction DashboardComponent_ng_template_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21);\n    i0.ɵɵtemplate(2, DashboardComponent_ng_template_26_p_carousel_2_Template, 2, 10, \"p-carousel\", 40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(3, \"div\", 20)(4, \"div\", 23)(5, \"app-block-viewer\", 47);\n    i0.ɵɵelement(6, \"app-next-lesson\", 73);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 23)(8, \"app-block-viewer\", 74)(9, \"p-scrollPanel\", 26);\n    i0.ɵɵelement(10, \"app-dashboard-notifications\", 27);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"div\", 75)(12, \"div\", 28)(13, \"app-calendar-agenda\", 29);\n    i0.ɵɵlistener(\"dayClicked\", function DashboardComponent_ng_template_26_Template_app_calendar_agenda_dayClicked_13_listener($event) {\n      i0.ɵɵrestoreView(_r27);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onAgendaDayClicked($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 28);\n    i0.ɵɵtemplate(15, DashboardComponent_ng_template_26_ng_container_15_Template, 1, 0, \"ng-container\", 15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 38);\n    i0.ɵɵelement(17, \"app-dashboard-teacher-classrooms\", 76);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"app-text-call-to-action-button\");\n  }\n  if (rf & 2) {\n    const context_r29 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    const lessonsBoxTemplateTeacher_r30 = i0.ɵɵreference(25);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.banners.length > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"containerHeight\", 129);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"lessons\", context_r29.lessons);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"containerHeight\", 119);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(11, _c10));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"slimStyle\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"lessons\", context_r29.lessons);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", lessonsBoxTemplateTeacher_r30)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(12, _c6, ctx_r1.templateContext.$implicit));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"classrooms\", context_r29.classRooms);\n  }\n}\nexport class DashboardComponent {\n  constructor(generalService, authService, lessonService, blogService, tutorialService, guideService, goalService, notificationService, classroomService, studentTrialService, calendarService, userService, layoutService, toastService, cdr, jitsiMeetService, ratingAndReportService, renderer) {\n    this.generalService = generalService;\n    this.authService = authService;\n    this.lessonService = lessonService;\n    this.blogService = blogService;\n    this.tutorialService = tutorialService;\n    this.guideService = guideService;\n    this.goalService = goalService;\n    this.notificationService = notificationService;\n    this.classroomService = classroomService;\n    this.studentTrialService = studentTrialService;\n    this.calendarService = calendarService;\n    this.userService = userService;\n    this.layoutService = layoutService;\n    this.toastService = toastService;\n    this.cdr = cdr;\n    this.jitsiMeetService = jitsiMeetService;\n    this.ratingAndReportService = ratingAndReportService;\n    this.renderer = renderer;\n    this.ignoredOrLostTrialStatusTemplate = {};\n    this.newOrForwaredTrialStatusTemplate = {};\n    this.paidOrActiveTemplate = {};\n    this.classroomCardsWrapper = {};\n    this.subs = new SubSink();\n    this.loggedInUser = {};\n    this.allLessons = [];\n    this.todaysLessons = [];\n    this.oldValue = 0;\n    this.newValue = 0;\n    this.blog = this.blogService.dummyBlogs;\n    this.guides = [];\n    this.showModal = false;\n    this.suggestForm = {};\n    this.controlErrors = {};\n    this.suggestError = false;\n    this.suggestErrorLength = false;\n    this.recommendToUsers = [];\n    this.role = \"\";\n    this.studentGoals = [];\n    this.allLessonsLoading = true;\n    this.todaysLessonsLoading = true;\n    this.showGoalsMenu = false;\n    this.notifications = [];\n    this.tutorials = [];\n    this.recHistory = [];\n    this.userClassrooms = [];\n    this.loadingGoals = true;\n    this.loadingClassrooms = true;\n    this.today = new Date();\n    this.studentHasTrialRequest = false;\n    this.showLoader = true;\n    this.banners = [];\n    this.verticalViewPortHeight = 130;\n    this.agendaHeight = 0;\n    this.responsiveOptions = [];\n    this.LessonStatus = LessonStatus;\n    this.lessons = data.dummyLessons;\n    this.studentStatus$ = this.studentTrialService.studentStatus$;\n    this.StudentStatus = StudentTrialStatus;\n    this.currentAgendaDate = new Date();\n    this.divHeight = '140px';\n    this.UserRoles = UserRole;\n    this.user = {};\n    this.switches = {\n      All: false,\n      Ongoing: true,\n      Completed: false,\n      Expired: false\n    };\n    this.autoplayInterval = 5000;\n    this.templateContext = {\n      $implicit: {\n        hasAssignedLesson: false,\n        trialClassRooms: [],\n        filteredTrialClassRooms: [],\n        classRooms: [],\n        lessons: [],\n        todaysLessons: [],\n        leftMenuHeight: 0,\n        hasPayment: false\n      }\n    };\n  }\n  // TODO: add real dates\n  getStyle(date) {\n    const myDates = ['2023-01-31', '2023-02-15', '2023-02-21', '2018-03-05'];\n    const year = date.year.toString();\n    const month = (date.month + 1).toString().padStart(2, '0');\n    const day = date.day.toString().padStart(2, '0');\n    const dateString = `${year}-${month}-${day}`;\n    const returnedStatus = myDates.includes(dateString);\n    return returnedStatus;\n  }\n  executeCommand(data) {\n    console.log('this.jitsiService.getParticipants():', this.jitsiMeetService.getParticipants());\n    this.jitsiMeetService.api.executeCommand('sendEndpointTextMessage', this.jitsiMeetService.getParticipants(), 'mover a principal');\n  }\n  ngOnInit() {\n    this.user = this.authService.getLoggedInUser();\n    this.role = this.authService.getUserRole();\n    this.studentHasTrialRequest = this.studentTrialService.isTrialRequested;\n    this.allLessonsLoading = true;\n    this.loadingClassrooms = true;\n    this.subs.add(this.generalService.deviceKind.pipe().subscribe(res => {\n      if (res) {\n        if (res.w576up) {\n          this.verticalViewPortHeight = 130;\n          // this.renderer.addClass(this.document.getElementsByClassName('app')[0], 'h-full');\n        } else {\n          this.verticalViewPortHeight = 200;\n        }\n      }\n    }));\n    this.adjustHeightListener();\n    this.loadInitialDataBasedOnRole(this.role);\n    this.initUpdateListener();\n    this.suggestForm = new UntypedFormGroup({\n      suggestion: new UntypedFormControl(null, {\n        validators: [Validators.required, Validators.minLength(20)]\n      })\n    });\n    // this.subs.sink = this.blogService.get().pipe(take(1)).subscribe(res => {\n    //   this.blog = res;\n    // })\n    this.subs.sink = this.blogService.getRecHistory().pipe(take(1)).subscribe(res => {\n      let toret = [];\n      for (let h of res) {\n        if (!toret.some(el => el.studentName == h.studentName && el.articleTitle == h.articleTitle)) {\n          toret.push(h);\n        }\n      }\n      this.recHistory = toret;\n    });\n  }\n  ngAfterViewInit() {\n    // document.getElementById('classrooms-big-title')!.style.right = -(window.innerWidth / 2 + 160) + \"px\";\n    // document.getElementById('tutorials-big-title')!.style.right = -(window.innerWidth / 2) + \"px\";\n    // document.getElementById('blog-big-title')!.style.left = -(window.innerWidth / 3.2) + \"px\";\n    // document.getElementById('guides-big-title')!.style.left = -(window.innerWidth / 3.2) + \"px\";\n  }\n  loadScript(url) {\n    console.log('preparing to load...');\n    let node = document.createElement('script');\n    node.setAttribute(\"id\", \"lottie-player\");\n    node.src = url;\n    node.type = 'text/javascript';\n    node.async = true;\n    node.charset = 'utf-8';\n    document.getElementsByTagName('head')[0].appendChild(node);\n  }\n  ngAfterContentInit() {\n    if (this.role == UserRole.STUDENT) {\n      this.subs.add(this.goalService.updateListener.subscribe(res => {\n        this.subs.sink = this.goalService.getGoals().subscribe(res => {\n          this.loadingGoals = false;\n          this.studentGoals = res;\n        });\n      }));\n    }\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  onResize() {\n    this.adjustHeightListener();\n  }\n  getNotificationColor(type) {\n    if (type === LessonStatus.ARRANGED) {\n      return '#c056f5';\n    } else if (type === LessonStatus.COMPLETED) {\n      return '#3b9bed';\n    } else {\n      return '#F27769';\n    }\n  }\n  getNotificationBd(read) {\n    if (read) {\n      return \"white\";\n    } else {\n      return \"#efefef\";\n    }\n  }\n  getLessonColor(status) {\n    if (status === LessonStatus.ARRANGED) {\n      return '#c056f5';\n    } else if (status === LessonStatus.ARRANGED_TRIAL) {\n      return '#E5B0FC';\n    } else if (status === LessonStatus.COMPLETED) {\n      return '#3b9bed';\n    } else {\n      return '#F27769';\n    }\n  }\n  toggleSection(sectionElement, sectionArrowImgSrc) {\n    this.generalService.toggleSection(sectionElement, sectionArrowImgSrc, true);\n  }\n  onWindowScroll($event) {\n    this.newValue = window.pageYOffset;\n    if (this.newValue > 400) this.generalService.gradientScale(this.newValue, this.oldValue, 'classroom-gradient');\n    if (this.newValue > 1000) this.generalService.gradientScale(this.newValue, this.oldValue, 'blog-gradient');\n    if (this.newValue > 1500) this.generalService.gradientScale(this.newValue, this.oldValue, 'tutorial-gradient');\n    if (this.newValue > 2000) this.generalService.gradientScale(this.newValue, this.oldValue, 'guide-gradient');\n    this.oldValue = this.newValue;\n  }\n  toggleReadAbout() {\n    this.showModal = !this.showModal;\n    this.generalService.slideElements(this.showModal, 'read-about');\n  }\n  toggleSuggestIdea() {\n    this.showModal = !this.showModal;\n    this.generalService.slideElements(this.showModal, 'suggest-idea');\n  }\n  toggleHistory() {\n    this.showModal = !this.showModal;\n    this.generalService.slideElements(this.showModal, 'recommendation-history');\n  }\n  toggleViewGoals() {\n    this.showModal = !this.showModal;\n    this.generalService.slideElements(this.showModal, 'view-goals');\n  }\n  toggleSetGoals() {\n    this.showModal = !this.showModal;\n    this.generalService.slideElements(this.showModal, 'set-goals');\n  }\n  toggleEditGoals() {\n    this.showModal = !this.showModal;\n    this.generalService.slideElements(this.showModal, 'edit-goals');\n  }\n  submitSuggestion() {\n    this.suggestError = false;\n    this.suggestErrorLength = false;\n    if (!this.suggestForm.valid) {\n      this.getFormValidationErrors();\n      return;\n    }\n    this.toggleSuggestIdea();\n  }\n  getFormValidationErrors() {\n    Object.keys(this.suggestForm.controls).forEach(key => {\n      this.controlErrors = this.suggestForm.get(key).errors;\n      if (this.controlErrors != null) {\n        Object.keys(this.controlErrors).forEach(keyError => {\n          if (keyError == 'required') {\n            this.suggestError = true;\n          }\n          if (keyError == 'minlength') {\n            this.suggestErrorLength = true;\n          }\n        });\n      }\n    });\n  }\n  toggleRecommentTo(element) {\n    [].forEach.call(document.getElementsByClassName('recommend-list'), el => {\n      if (el.id !== element.id) el.style.display = 'none';\n    });\n    if (element.style.display === 'none' || element.style.display === '') {\n      element.style.display = 'flex';\n    } else {\n      element.style.display = 'none';\n    }\n  }\n  onAgendaHeight(event) {\n    this.agendaHeight = event;\n  }\n  closeRecommentListsOnClickOutside() {\n    document.addEventListener('click', event => {\n      setTimeout(() => {\n        let ignoreClickOnMeElement = document.getElementById('blog-right-side');\n        var isClickInsideElement = ignoreClickOnMeElement?.contains(event.target);\n        if (!isClickInsideElement) {\n          [].forEach.call(document.getElementsByClassName('recommend-list'), el => {\n            el.style.display = 'none';\n          });\n        }\n      }, 0);\n    });\n  }\n  showGuide(el) {\n    if (el.offsetHeight == 0) {\n      document.getElementById(el.id)?.classList.remove('no-visibility');\n      document.getElementById(el.id)?.classList.add('yes-visibility');\n      el.style.height = \"600px\";\n    } else {\n      document.getElementById(el.id)?.classList.remove('yes-visibility');\n      document.getElementById(el.id)?.classList.add('no-visibility');\n      el.style.height = \"0\";\n    }\n  }\n  onRecommentCheckedList(event) {\n    this.recommendToUsers = event.usersChecked;\n  }\n  rec(id) {\n    let ids = [];\n    for (let user of this.recommendToUsers) {\n      ids.push(user.id);\n    }\n    this.blogService.share(ids, id).pipe(take(1)).subscribe(res => {});\n  }\n  viewArticle(blogItem) {\n    window.open(blogItem.link, \"_blank\");\n  }\n  onTrialRequestFormSubmitted(isSent) {\n    if (isSent) {\n      this.toastService.setShowToastmessage({\n        severity: 'success',\n        summary: '',\n        detail: 'Trial request has been sent.'\n      });\n      window.scrollTo(0, 0);\n      this.studentTrialService.updateStudentTrialStatus(StudentTrialStatus.NEW);\n      this.updateBanners(StudentTrialStatus.NEW, []);\n    }\n  }\n  loadInitialData() {\n    this.banners = [];\n    const userClassrooms$ = this.classroomService.getLMSUserClassrooms(this.user.id);\n    const tutorials$ = this.tutorialService.getTutorials();\n    const userHasPayment$ = this.userService.getUserHasPayment();\n    const headers = new HttpHeaders({\n      'X-Bypass-Error-Interceptor': 'true'\n    });\n    const statusTrial$ = this.studentTrialService.getStudentTrialStatus(headers);\n    const studentHasTrialRequest$ = this.studentTrialService.getStudentHasTrialRequest();\n    this.showLoader = true;\n    this.subs.sink = studentHasTrialRequest$.pipe(switchMap(hasTrialRequest => {\n      if (hasTrialRequest) {\n        const trialObservables = {\n          userClassrooms: userClassrooms$.pipe(catchError(error => of(error))),\n          statusTrial: statusTrial$.pipe(catchError(error => of(false))),\n          userHasPayment: userHasPayment$.pipe(catchError(error => of(error))),\n          tutorials: tutorials$.pipe(catchError(error => of(error)))\n        };\n        return forkJoin(trialObservables);\n      } else {\n        return of(hasTrialRequest);\n      }\n    }), switchMap(results => {\n      // false is for no  trial status found (currently backend returns 400 error code if no trial is found)\n      if (results === false) {\n        return of(false);\n      }\n      const classroomIds = results.userClassrooms.map(classroom => classroom.id);\n      if (classroomIds.length === 0) {\n        return of({\n          ...results,\n          lessons: []\n        });\n      }\n      const lessonObservables = classroomIds.map(classroomId => this.classroomService.getClassroomLessons(classroomId));\n      return forkJoin(lessonObservables).pipe(map(lessons => ({\n        ...results,\n        lessons\n      })));\n    }), catchError(error => {\n      console.error(error);\n      this.showLoader = false;\n      return of({});\n    }), finalize(() => {\n      this.allLessonsLoading = false;\n      this.loadingClassrooms = false;\n      this.showLoader = false;\n    })).subscribe(res => {\n      this.showLoader = false;\n      console.log(res);\n      if (res === false) {\n        this.studentTrialService.updateStudentTrialStatus(StudentTrialStatus.NONE);\n      } else {\n        const trialClassRooms = res.userClassrooms;\n        console.log(res);\n        let newStatus = StudentTrialStatus.NONE;\n        if (!res.statusTrial && !res.userHasPayment) {\n          newStatus = trialClassRooms.length > 0 ? StudentTrialStatus.FORWARDED : StudentTrialStatus.NONE;\n        } else {\n          newStatus = res.userHasPayment ? StudentTrialStatus.HAS_PAYMENT : res.statusTrial.toLowerCase();\n          if (newStatus.toLowerCase().includes('Trial'.toLowerCase())) {\n            newStatus = StudentTrialStatus.NEW;\n          }\n        }\n        // set status for dashboard\n        this.templateContext.$implicit.hasAssignedLesson = true;\n        this.templateContext.$implicit.hasPayment = res.userHasPayment;\n        this.templateContext.$implicit.trialClassRooms = trialClassRooms;\n        this.templateContext.$implicit.filteredTrialClassRooms = trialClassRooms;\n        // If there are no ongoing classrooms, toggle the switch to the \"All\" position.\n        // If there are ongoing classrooms, toggle the switch to the \"Ongoing\" position.\n        if (this.getOngoingClassrooms().length === 0) {\n          this.toggleSwitch('All');\n        } else {\n          this.toggleSwitch('Ongoing');\n        }\n        this.templateContext.$implicit.lessons = res.lessons;\n        // this.layoutService.updateMenuItemDisabledStatus(\"Classrooms\", trialClassRooms.length === 0);\n        // if (this.isStudentActive(trialClassRooms, res.userHasPayment)) {\n        //   this.layoutService.updateAllMenuItemsDisabledStatus(false);\n        // } else {\n        //   this.layoutService.updateMenuItemClassroomToTrialsForStudent();\n        // }\n        trialClassRooms.forEach(classroom => {\n          console.log(classroom);\n          if (this.classroomService.isLastLessonWithinOneMonth(classroom)) {\n            if (this.classroomService.isTrialClassroomWithCompletedFirstLesson(classroom)) {\n              this.banners.push({\n                bannerMode: 'trial-assigned',\n                classRoom: classroom\n              });\n            } else {\n              if (this.classroomService.isFirstLessonStatusInTrialClassroom(classroom, LessonStatus.ARRANGED)) {\n                this.banners.push({\n                  bannerMode: 'trial-arranged',\n                  classRoom: classroom\n                });\n              } else if (this.classroomService.isTrialClassroomDismissed(classroom)) {\n                this.banners.push({\n                  bannerMode: 'trial-dismissed',\n                  classRoom: classroom\n                });\n              } else {\n                this.banners.push({\n                  bannerMode: 'trial-assigned',\n                  classRoom: classroom\n                });\n              }\n            }\n          }\n        });\n        console.log(newStatus);\n        this.updateBanners(newStatus, trialClassRooms);\n        // Adding banner in the second position\n        // TODO: add when referral system is ready\n        // this.banners.splice(1, 0, { bannerMode: 'primary-referral-code' });\n        this.studentTrialService.updateStudentTrialStatus(newStatus);\n      }\n    });\n  }\n  loadInitialDataForTeacher() {\n    this.banners = [];\n    const userClassrooms$ = this.classroomService.getLMSUserClassrooms(this.user.id);\n    const headers = new HttpHeaders({\n      'X-Bypass-Error-Interceptor': 'true'\n    });\n    this.subs.add(userClassrooms$.pipe(switchMap(userClassrooms => {\n      if (userClassrooms) {\n        const lessonObservables = userClassrooms.map(classroom => this.classroomService.getClassroomLessons(classroom.id));\n        const observables = {\n          userClassrooms: userClassrooms$,\n          lessons: forkJoin(lessonObservables)\n        };\n        return forkJoin(observables);\n      } else {\n        return of(userClassrooms);\n      }\n    }), catchError(error => {\n      console.error(error);\n      this.showLoader = false;\n      return of({});\n    }), finalize(() => {\n      this.updateBanners('', []);\n      this.showLoader = false;\n    })).subscribe(data => {\n      this.showLoader = false;\n      // this.updateBanners('', []);\n      this.templateContext.$implicit.classRooms = data.userClassrooms;\n      // filter out empty lessons\n      this.templateContext.$implicit.lessons = data.lessons.filter(lessons => lessons.length > 0);\n      this.templateContext.$implicit.todaysLessons = data.todaysLessons;\n    }));\n  }\n  // TODO: check if this is needed\n  // getKeyByValue(object: any, value: any): string | undefined {\n  //   for (const key in object) {\n  //     if (object[key] === value) {\n  //       return key;\n  //     }\n  //   }\n  //   return undefined;\n  // }\n  updateBanners(status, trialClassRooms) {\n    // TODO: check for more statuses\n    // this.studentTrialService.studentStatus$.subscribe((status: StudentTrialStatus) => {\n    if (this.role === this.UserRoles.TEACHER) {\n      this.banners.push({\n        bannerMode: 'primary'\n      });\n    }\n    switch (status) {\n      case StudentTrialStatus.HAS_PAYMENT:\n        this.banners.unshift({\n          bannerMode: trialClassRooms.length > 0 ? 'primary-assigned' : 'primary-received'\n        });\n        break;\n      case StudentTrialStatus.ASSIGNED:\n        this.banners.unshift({\n          bannerMode: 'trial-assigned'\n        });\n        break;\n      case StudentTrialStatus.FORWARDED:\n        trialClassRooms.length > 0 ? null : this.banners.unshift({\n          bannerMode: 'trial-received'\n        });\n        break;\n      case StudentTrialStatus.NEW:\n      case StudentTrialStatus.NONE:\n        this.banners.unshift({\n          bannerMode: 'trial-received'\n        });\n        break;\n      case StudentTrialStatus.IGNORED:\n      case StudentTrialStatus.LOST:\n        this.banners.push({\n          bannerMode: 'unreachable'\n        });\n        break;\n      case StudentTrialStatus.GALAXY:\n        break;\n    }\n  }\n  isStudentActive(classrooms, hasPayment) {\n    return classrooms.length > 0 && hasPayment;\n  }\n  onAgendaDayClicked(date) {\n    const dateF = new Date(date);\n    this.currentAgendaDate = dateF;\n    const formattedDate = dateF.toLocaleDateString('en-GB');\n    console.log(formattedDate);\n  }\n  adjustHeightListener() {\n    this.subs.sink = this.layoutService.sideMenuHeight.pipe(skip(1)).subscribe(res => {\n      if (res !== 0) {\n        this.templateContext.$implicit.leftMenuHeight = res;\n        this.cdr.detectChanges(); // Manually trigger change detection // TODO: improve\n      }\n    });\n  }\n  getNextStepNumber(classrooms) {\n    if (!classrooms || classrooms.length === 0) {\n      return 1;\n    }\n    const stepToShow = classrooms.some(classroom => this.classroomService.isTrialClassroomWithCompletedLesson(classroom)) ? 3 : 2;\n    return stepToShow;\n  }\n  toggleSwitch(key) {\n    console.log(key);\n    this.resetSwitches();\n    switch (key) {\n      case 'All':\n      case 'Ongoing':\n      case 'Active':\n      case 'Completed':\n      case 'Expired':\n      case 'Dismissed':\n      case 'Freeze':\n        this.switches[key] = !this.switches[key];\n        break;\n    }\n    this.filterLessons();\n  }\n  resetSwitches() {\n    for (const key in this.switches) {\n      this.switches[key] = false;\n    }\n  }\n  /**\n  * Filters the list of lessons based on the selected level and status filters.\n  * @function\n  * @returns {void}\n  */\n  filterLessons(filterOngoing = false) {\n    // Filter the lessons based on the selected switches\n    this.templateContext.$implicit.filteredTrialClassRooms = this.templateContext.$implicit.trialClassRooms.filter(lesson => {\n      let levelFilter;\n      // Filter by \"Ongoing\" switch\n      if (this.switches.Ongoing || filterOngoing) {\n        // Return lessons that have an \"ongoing\" status, \"arranged\" status, or \"pending\" status\n        return (lesson.status.toLowerCase().includes(Status.ON_GOING.toLowerCase()) || lesson.status.toLowerCase().includes(LessonStatus.ARRANGED.toLowerCase()) || lesson.status.toLowerCase().includes(Status.PENDING.toLowerCase())) && (!levelFilter || levelFilter(lesson));\n      }\n      // Filter by \"Completed\" switch\n      if (this.switches.Completed) {\n        // Return lessons that have a \"completed\" status\n        return lesson.status.toLowerCase().includes(Status.COMPLETED.toLowerCase()) && (!levelFilter || levelFilter(lesson));\n      }\n      // Filter by \"Expired\" switch\n      if (this.switches.Expired) {\n        // Return lessons that have an \"expired\" or \"dismissed\" status\n        return (lesson.status.toLowerCase().includes(Status.EXPIRED.toLowerCase()) || lesson.status.toLowerCase().includes(Status.DISMISSED.toLowerCase())) && (!levelFilter || levelFilter(lesson));\n      }\n      // Filter by \"Dismissed\" switch\n      if (this.switches.Dismissed) {\n        // Return lessons that have a \"dismissed\" status\n        return lesson.status.toLowerCase().includes(Status.DISMISSED.toLowerCase()) && (!levelFilter || levelFilter(lesson));\n      }\n      // Filter by \"All\" switch\n      if (this.switches.All) {\n        // Return all lessons\n        return true && (!levelFilter || levelFilter(lesson));\n      }\n      // Return false for all other cases\n      return false;\n    }).reverse();\n  }\n  getOngoingClassrooms() {\n    const classroomsForCheck = this.templateContext.$implicit.filteredTrialClassRooms;\n    const ongoingClassrooms = classroomsForCheck.filter(lesson => {\n      return lesson.status.toLowerCase().includes(Status.ON_GOING.toLowerCase()) || lesson.status.toLowerCase().includes(LessonStatus.ARRANGED.toLowerCase()) || lesson.status.toLowerCase().includes(Status.PENDING.toLowerCase());\n    });\n    return ongoingClassrooms;\n  }\n  loadInitialDataBasedOnRole(role) {\n    if (role === UserRole.STUDENT) {\n      this.loadInitialData();\n    } else {\n      this.loadInitialDataForTeacher();\n    }\n  }\n  initUpdateListener() {\n    this.subs.add(this.calendarService.updateListener.subscribe(res => {\n      if (res) {\n        this.loadInitialDataBasedOnRole(this.role);\n      }\n    }));\n  }\n  static #_ = this.ɵfac = function DashboardComponent_Factory(t) {\n    return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.GeneralService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.LessonService), i0.ɵɵdirectiveInject(i4.BlogService), i0.ɵɵdirectiveInject(i5.TutorialService), i0.ɵɵdirectiveInject(i6.GuideService), i0.ɵɵdirectiveInject(i7.GoalService), i0.ɵɵdirectiveInject(i8.NotificationService), i0.ɵɵdirectiveInject(i9.ClassroomService), i0.ɵɵdirectiveInject(i10.StudentTrialService), i0.ɵɵdirectiveInject(i11.CalendarService), i0.ɵɵdirectiveInject(i12.UserService), i0.ɵɵdirectiveInject(i13.LayoutService), i0.ɵɵdirectiveInject(i14.ToastService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i15.JitsiMeetService), i0.ɵɵdirectiveInject(i16.RatingAndReportService), i0.ɵɵdirectiveInject(i0.Renderer2));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: DashboardComponent,\n    selectors: [[\"app-dashboard\"]],\n    viewQuery: function DashboardComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n        i0.ɵɵviewQuery(_c1, 7);\n        i0.ɵɵviewQuery(_c2, 7);\n        i0.ɵɵviewQuery(_c3, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ignoredOrLostTrialStatusTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.newOrForwaredTrialStatusTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paidOrActiveTemplate = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.classroomCardsWrapper = _t.first);\n      }\n    },\n    hostBindings: function DashboardComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"resize\", function DashboardComponent_resize_HostBindingHandler() {\n          return ctx.onResize();\n        }, false, i0.ɵɵresolveWindow)(\"scroll\", function DashboardComponent_scroll_HostBindingHandler($event) {\n          return ctx.onWindowScroll($event);\n        }, false, i0.ɵɵresolveWindow);\n      }\n    },\n    decls: 28,\n    vars: 0,\n    consts: [[\"newOrForwaredTrialStatusTemplate\", \"\"], [\"ignoredOrLostTrialStatusTemplate\", \"\"], [\"paidOrActiveTemplate\", \"\"], [\"classroomCardsTemplate\", \"\"], [\"lessonsBoxTemplate\", \"\"], [\"lessonsBoxTemplateTeacher\", \"\"], [\"teacherTemplate\", \"\"], [\"id\", \"set-goals\", 1, \"modal\", \"no-visibility\"], [1, \"popup-title\"], [\"src\", \"/assets/icons/close.png\", 1, \"close-img\", \"close-img-abs\", \"hvr-glow\", 3, \"click\"], [\"id\", \"edit-goals\", 1, \"modal\", \"no-visibility\"], [4, \"ngIf\"], [1, \"dashboard-wrapper\", \"pt-3\", \"px-2\", \"sm:px-3\", \"md:pt-0\", \"md:px-0\"], [1, \"grid\"], [1, \"m-auto\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"ngSwitch\"], [4, \"ngSwitchCase\"], [4, \"ngSwitchDefault\"], [4, \"ngTemplateOutlet\"], [1, \"grid\", \"flex\", \"justify-content-between\"], [1, \"col-12\"], [\"indicatorsContentClass\", \"bulletStyle\", 3, \"value\", \"autoplayInterval\", \"verticalViewPortHeight\", \"orientation\", \"numVisible\", \"page\", \"numScroll\", \"showNavigators\", \"showIndicators\", \"circular\", 4, \"ngIf\"], [1, \"col-12\", \"md:col-6\"], [3, \"isSmall\"], [\"header\", \"Notifications\", \"headerBackgroundImage\", \"/assets/images/dashboard/gradient-sm-1.png\", \"blockClass\", \"block-gradient border-radius-bottom-10\", \"containerClass\", \"\", \"actionButtonText\", \"See All\", \"actionButtonRoute\", \"/notifications\"], [\"styleClass\", \"custombar1\"], [3, \"slimStyle\"], [1, \"col-12\", \"md:col-6\", \"h-full\"], [3, \"dayClicked\", \"lessons\"], [3, \"lessons\", \"disableAddNewLessonButton\", \"currentDate\"], [\"color\", \"trial\"], [3, \"activeStep\"], [\"indicatorsContentClass\", \"bulletStyle\", 3, \"value\", \"autoplayInterval\", \"verticalViewPortHeight\", \"orientation\", \"numVisible\", \"page\", \"numScroll\", \"showNavigators\", \"showIndicators\", \"circular\"], [\"pTemplate\", \"item\"], [3, \"bannerMode\"], [\"header\", \"Notifications\", \"headerBackgroundImage\", \"/assets/images/dashboard/gradient-sm-1.png\", \"blockClass\", \"block-gradient border-round-xl h-full\", \"containerClass\", \"\", \"actionButtonText\", \"See All\", \"actionButtonRoute\", \"/notifications\", 3, \"containerHeight\"], [3, \"formSubmitted\", 4, \"ngIf\"], [1, \"classroom-cards-space\"], [3, \"formSubmitted\"], [\"indicatorsContentClass\", \"bulletStyle\", 3, \"value\", \"autoplayInterval\", \"responsiveOptions\", \"verticalViewPortHeight\", \"numVisible\", \"page\", \"numScroll\", \"showNavigators\", \"showIndicators\", \"circular\", 4, \"ngIf\"], [\"headerBlockClass\", \"bg-cover \", \"header\", \"Notifications\", \"headerBackgroundImage\", \"/assets/images/dashboard/gradient-sm-1.png\", \"blockClass\", \"block-gradient border-radius-bottom-10 h-full\", \"containerClass\", \"\", \"actionButtonText\", \"See All\", \"actionButtonRoute\", \"/notifications\", 3, \"containerHeight\"], [1, \"mt-5\"], [\"indicatorsContentClass\", \"bulletStyle\", 3, \"value\", \"autoplayInterval\", \"responsiveOptions\", \"verticalViewPortHeight\", \"numVisible\", \"page\", \"numScroll\", \"showNavigators\", \"showIndicators\", \"circular\"], [3, \"classRoom\", \"teacher\", \"bannerMode\"], [\"header\", \"Notifications\", \"headerBackgroundImage\", \"/assets/images/dashboard/gradient-sm-1.png\", \"blockClass\", \"block-gradient border-radius-bottom-10\", \"containerHeight\", \"118\", \"containerClass\", \"\", \"actionButtonText\", \"See All\", \"actionButtonRoute\", \"/notifications\"], [\"indicatorsContentClass\", \"bulletStyle\", 3, \"value\", \"autoplayInterval\", \"verticalViewPortHeight\", \"showNavigators\", \"showIndicators\", \"circular\", 4, \"ngIf\"], [\"headerBlockClass\", \"bg-cover \", \"header\", \"Coming up\", \"headerBackgroundImage\", \"/assets/images/dashboard/gradient-sm-1.png\", \"blockClass\", \"block-gradient relative h-full border-radius-bottom-10\", \"containerClass\", \"\", 3, \"containerHeight\"], [3, \"lessons\", \"todayLessons\"], [\"header\", \"Notifications\", \"headerBackgroundImage\", \"/assets/images/dashboard/gradient-sm-1.png\", \"blockClass\", \"block-gradient border-radius-bottom-10 h-full\", \"containerClass\", \"\", \"actionButtonText\", \"See All\", \"actionButtonRoute\", \"/notifications\"], [\"indicatorsContentClass\", \"bulletStyle\", 3, \"value\", \"autoplayInterval\", \"verticalViewPortHeight\", \"showNavigators\", \"showIndicators\", \"circular\"], [3, \"classRoom\", \"bannerMode\"], [3, \"activeStep\", \"stepOneInfo\", \"stepThreeInfo\"], [\"id\", \"classroomBox\", 1, \"relative\", \"h-full\"], [1, \"relative\"], [1, \"border-round-xl\", \"overflow-hidden\", \"mt-4\"], [1, \"class-filters\", \"border-round-xl\"], [1, \"image-container\"], [\"src\", \"/assets/images/dashboard/class-filters-cut.svg\", \"alt\", \"class-filters img\", 1, \"md:block\"], [1, \"text-overlay\"], [1, \"font-3xl\"], [1, \"filters-content\", \"bg-white\", \"border-round-xl\"], [1, \"block\", \"relative\", \"mt-4\", \"md:mt-0\", \"mx-2\", \"md:mx-0\", 3, \"switchToggled\", \"isTrial\", \"switches\"], [4, \"ngFor\", \"ngForOf\"], [3, \"ngClass\"], [\"color\", \"expired\", 3, \"classRoom\"], [1, \"m-1\", \"h-full\"], [1, \"flex\", \"h-full\", \"align-items-center\", \"justify-content-center\", \"flex-column\"], [\"src\", \"/assets/icons/empty-classroom.png\", \"height\", \"280\"], [\"color\", \"expired\", 1, \"sm:pb-2\", \"block\", 3, \"classRoom\"], [\"header\", \" \", \"headerBackgroundImage\", \"/assets/images/dashboard/gradient-sm-1.png\", \"blockClass\", \"relative card\", \"containerClass\", \"\", 3, \"containerHeight\"], [3, \"scrollHeight\", \"classRooms\", \"lessons\", \"disableAddNewLessonButton\"], [3, \"classRooms\", \"lessons\", \"disableAddNewLessonButton\"], [3, \"lessons\"], [\"headerBlockClass\", \"bg-cover \", \"header\", \"Notifications\", \"headerBackgroundImage\", \"/assets/images/dashboard/gradient-sm-1.png\", \"blockClass\", \"block-gradient border-radius-bottom-10\", \"containerClass\", \"\", \"actionButtonText\", \"See All\", \"actionButtonRoute\", \"/notifications\", 3, \"containerHeight\"], [1, \"grid\", \"flex\", \"justify-content-between\", \"h-full\", \"flex-wrap\"], [3, \"classrooms\"], [3, \"teacher\", \"bannerMode\"]],\n    template: function DashboardComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵtemplate(0, DashboardComponent_Defer_0_Template, 3, 2)(1, DashboardComponent_DeferPlaceholder_1_Template, 2, 0);\n        i0.ɵɵdefer(2, 0, DashboardComponent_Defer_2_DepsFn, null, 1);\n        i0.ɵɵdeferOnViewport(0, -1);\n        i0.ɵɵelementStart(4, \"div\", 7)(5, \"div\", 8)(6, \"div\");\n        i0.ɵɵtext(7, \"Set new goal\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(8, \"img\", 9);\n        i0.ɵɵlistener(\"click\", function DashboardComponent_Template_img_click_8_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.toggleSetGoals());\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(9, \"div\", 10)(10, \"div\", 8)(11, \"div\");\n        i0.ɵɵtext(12, \"Edit goals\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(13, \"img\", 9);\n        i0.ɵɵlistener(\"click\", function DashboardComponent_Template_img_click_13_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.toggleEditGoals());\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(14, DashboardComponent_ng_template_14_Template, 22, 17, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(16, DashboardComponent_ng_template_16_Template, 12, 5, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(18, DashboardComponent_ng_template_18_Template, 18, 18, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(20, DashboardComponent_ng_template_20_Template, 1, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(22, DashboardComponent_ng_template_22_Template, 1, 1, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor)(24, DashboardComponent_ng_template_24_Template, 2, 2, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor)(26, DashboardComponent_ng_template_26_Template, 19, 14, \"ng-template\", null, 6, i0.ɵɵtemplateRefExtractor);\n      }\n    },\n    dependencies: [i17.NgClass, i17.NgForOf, i17.NgIf, i17.NgTemplateOutlet, i18.NextLessonComponent, i19.BlockViewerComponent, i20.CalendarAgendaComponent, i21.LessonScheduleBoxComponent, i22.GoalStepsBoxComponent, i23.TextCallToActionButtonComponent, i24.ClassroomCardComponent, i25.PrimeTemplate, i26.ClassroomStatusFiltersComponent, i27.ScrollPanel, i28.Carousel, i29.IntroGradientComponent, i30.DashboardNotificationsComponent, i31.DashboardNextStepsComponent, i32.DashboardBenefitsComponent, i33.CalendarPreviewWeekComponent, i34.DashboardContactUsFormComponent, i35.DashboardTeacherClassroomsComponent],\n    styles: [\".burger-menu[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  cursor: pointer;\\n}\\n.burger-menu[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.burger-menu[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]   .burger-inner[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 22px;\\n  left: 0px;\\n  top: 11px;\\n  border: 1px solid #ffffff;\\n  transition: border-color 0.2s ease-in 0.2s;\\n}\\n.burger-menu[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]   .burger-inner[_ngcontent-%COMP%]::before {\\n  position: absolute;\\n  content: \\\"\\\";\\n  top: -6px;\\n  left: -1px;\\n  width: 22px;\\n  border: 1px solid #ffffff;\\n  transition: top 0.2s ease-in 0.3s, transform ease-in 0.2s;\\n}\\n.burger-menu[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]   .burger-inner[_ngcontent-%COMP%]::after {\\n  position: absolute;\\n  content: \\\"\\\";\\n  top: 4px;\\n  left: -1px;\\n  width: 22px;\\n  border: 1px solid #ffffff;\\n  transition: top 0.2s ease-in 0.3s, transform ease-in 0.2s;\\n}\\n.burger-menu.active[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]   .burger-inner[_ngcontent-%COMP%] {\\n  border-color: transparent;\\n  transition: border-color ease-in 0.1s;\\n}\\n.burger-menu.active[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]   .burger-inner[_ngcontent-%COMP%]::before {\\n  top: -1px;\\n  transform: rotate(-45deg);\\n  transition: top ease-out 0.2s, transform 0.2s ease-out 0.3s;\\n}\\n.burger-menu.active[_ngcontent-%COMP%]   .burger-container[_ngcontent-%COMP%]   .burger-inner[_ngcontent-%COMP%]::after {\\n  top: -1px;\\n  transform: rotate(45deg);\\n  transition: top ease-out 0.2s, transform 0.2s ease-out 0.3s;\\n}\\n\\n.responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.circled[_ngcontent-%COMP%] {\\n  border-radius: 50%;\\n  width: 34px;\\n  height: 34px;\\n  \\n\\n  border: 1px solid #2E3D90;\\n  color: #000;\\n  text-align: center;\\n  font-size: 20px;\\n  vertical-align: middle;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.p-datepicker-inline[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n[_nghost-%COMP%]     .p-calendar.calendar-control {\\n  opacity: 1;\\n}\\n[_nghost-%COMP%]     .p-calendar.calendar-control .p-datepicker {\\n  border: 0;\\n  background: linear-gradient(#fff 0%, rgba(56, 115, 244, 0.11) 100%);\\n}\\n[_nghost-%COMP%]     .p-calendar.calendar-control .p-datepicker table td {\\n  padding: 0.5rem;\\n}\\n[_nghost-%COMP%]     .p-calendar.calendar-control .p-datepicker .p-datepicker-header {\\n  border-bottom: 0 none;\\n  position: absolute;\\n  top: -54px;\\n  right: 0;\\n  background: transparent;\\n  color: #fff;\\n}\\n[_nghost-%COMP%]     .p-calendar.calendar-control .p-datepicker .p-datepicker-header .p-datepicker-prev, [_nghost-%COMP%]     .p-calendar.calendar-control .p-datepicker .p-datepicker-header .p-datepicker-next {\\n  color: #fff;\\n}\\n[_nghost-%COMP%]     .p-calendar.calendar-control .p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-month, [_nghost-%COMP%]     .p-calendar.calendar-control .p-datepicker .p-datepicker-header .p-datepicker-title .p-datepicker-year {\\n  color: #fff;\\n}\\n[_nghost-%COMP%]     .p-calendar.calendar-control .p-datepicker table td > span {\\n  overflow: visible;\\n}\\n\\n.dashboard[_ngcontent-%COMP%] {\\n  padding: 22px;\\n  padding-top: 5px;\\n  border-radius: 10px;\\n  background-color: var(--white);\\n  width: var(--page-width);\\n  box-sizing: border-box;\\n  margin-top: 10px;\\n  position: relative;\\n}\\n\\n.first-section[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  margin-top: 10px;\\n  font-size: 15px;\\n}\\n\\n.left[_ngcontent-%COMP%], .right[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 50%;\\n  box-sizing: border-box;\\n  background-color: white;\\n  border-radius: 10px;\\n  padding: 15px;\\n  min-height: 150px;\\n  max-height: 150px;\\n  overflow-y: scroll;\\n  overflow-x: hidden;\\n}\\n\\n.left[_ngcontent-%COMP%] {\\n  margin-right: 5px;\\n}\\n\\n.left-title[_ngcontent-%COMP%], .left-title-student[_ngcontent-%COMP%] {\\n  width: 100%;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.left-title[_ngcontent-%COMP%]   .left-title-student-text[_ngcontent-%COMP%], .left-title-student[_ngcontent-%COMP%]   .left-title-student-text[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid var(--main-color);\\n  width: 100%;\\n  font-weight: bold;\\n}\\n.left-title[_ngcontent-%COMP%]   .gray-border-button[_ngcontent-%COMP%], .left-title-student[_ngcontent-%COMP%]   .gray-border-button[_ngcontent-%COMP%] {\\n  padding: 5px;\\n}\\n\\n.left-title[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid var(--main-color);\\n}\\n\\n.notification[_ngcontent-%COMP%] {\\n  display: flex;\\n  padding: 10px;\\n  align-items: center;\\n  cursor: pointer;\\n}\\n.notification[_ngcontent-%COMP%]   .lesson-circle[_ngcontent-%COMP%] {\\n  margin-right: 10px;\\n  width: 10px;\\n  height: 10px;\\n  border-radius: 50%;\\n}\\n\\n.first-section-lesson[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  margin: 10px;\\n  align-items: center;\\n}\\n.first-section-lesson[_ngcontent-%COMP%]   .lesson-circle[_ngcontent-%COMP%] {\\n  width: 10px;\\n  height: 10px;\\n  border-radius: 50%;\\n}\\n.first-section-lesson[_ngcontent-%COMP%]   .lesson-title[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n  width: 30%;\\n}\\n.first-section-lesson[_ngcontent-%COMP%]   .lesson-hours[_ngcontent-%COMP%] {\\n  width: 20%;\\n}\\n.first-section-lesson[_ngcontent-%COMP%]   .gray-border-button[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  padding: 5px;\\n  border-radius: 12px;\\n  box-sizing: border-box;\\n  text-align: center;\\n  width: 30%;\\n}\\n\\n.right[_ngcontent-%COMP%] {\\n  margin-left: 5px;\\n}\\n\\n.gradient[_ngcontent-%COMP%], .gradient-blog[_ngcontent-%COMP%] {\\n  position: absolute;\\n}\\n\\n.gradient[_ngcontent-%COMP%] {\\n  width: calc(var(--app-content-width) + var(--app-content-width) / 1.1);\\n  top: -400px;\\n  right: calc(-1 * var(--app-content-width) / 1.2);\\n  z-index: -1;\\n}\\n\\n.gradient-blog[_ngcontent-%COMP%] {\\n  width: calc(var(--app-content-width) + var(--app-content-width) / 1.1);\\n  top: -380px;\\n  left: calc(-1 * var(--app-content-width) * 1.2);\\n}\\n\\n.classrooms-big-title[_ngcontent-%COMP%], .blog-big-title[_ngcontent-%COMP%] {\\n  font-size: 120px;\\n  transform-origin: 0 0;\\n  transform: rotate(90deg);\\n  position: absolute;\\n  top: 0;\\n  font-weight: bold;\\n  color: var(--main-color);\\n  color: white;\\n}\\n\\n.rotate-minus-90[_ngcontent-%COMP%] {\\n  transform: rotate(-90deg);\\n}\\n\\n.blog[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n.blog[_ngcontent-%COMP%]   .blog-left-side[_ngcontent-%COMP%], .blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%] {\\n  height: 723px;\\n}\\n.blog[_ngcontent-%COMP%]   .blog-left-side[_ngcontent-%COMP%] {\\n  color: white;\\n  padding: 15px;\\n  box-sizing: border-box;\\n  width: 700px;\\n  background-size: cover;\\n  border-radius: 12px 0 0 12px;\\n  position: relative;\\n  font-size: 15px;\\n}\\n.blog[_ngcontent-%COMP%]   .blog-left-side[_ngcontent-%COMP%]   .blog-left-side-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n}\\n.blog[_ngcontent-%COMP%]   .blog-left-side[_ngcontent-%COMP%]   .blog-left-side-title[_ngcontent-%COMP%]   .blog-title-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.blog[_ngcontent-%COMP%]   .blog-left-side[_ngcontent-%COMP%]   .blog-left-side-title[_ngcontent-%COMP%]   .blog-title-content[_ngcontent-%COMP%]   .content-text[_ngcontent-%COMP%] {\\n  margin-left: 15px;\\n}\\n.blog[_ngcontent-%COMP%]   .blog-left-side[_ngcontent-%COMP%]   .main-info[_ngcontent-%COMP%] {\\n  position: absolute;\\n  bottom: 15px;\\n}\\n.blog[_ngcontent-%COMP%]   .blog-left-side[_ngcontent-%COMP%]   .main-info[_ngcontent-%COMP%]   .main-info-date[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n}\\n.blog[_ngcontent-%COMP%]   .blog-left-side[_ngcontent-%COMP%]   .main-info[_ngcontent-%COMP%]   .main-info-title[_ngcontent-%COMP%] {\\n  font-size: 60px;\\n  font-weight: bold;\\n}\\n.blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%] {\\n  border-radius: 0px 12px 12px 0px;\\n  width: 363px;\\n  font-size: 15px;\\n  z-index: 90;\\n}\\n.blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 221px;\\n  background: #fdfdfe;\\n  filter: drop-shadow(0px 3px 6px rgba(0, 0, 0, 0.16));\\n  padding: 10px;\\n}\\n.blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   [class$=-button][_ngcontent-%COMP%] {\\n  padding: 5px;\\n}\\n.blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%] {\\n  display: flex;\\n}\\n.blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-content-left[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n.blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-content-left[_ngcontent-%COMP%]   .item-content-date[_ngcontent-%COMP%], .blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-content-left[_ngcontent-%COMP%]   .item-content-description[_ngcontent-%COMP%] {\\n  color: #93949e;\\n  margin-top: 15px;\\n}\\n.blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-content-left[_ngcontent-%COMP%]   .item-content-date[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n}\\n.blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-content-left[_ngcontent-%COMP%]   .item-content-title[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  margin-top: 15px;\\n}\\n.blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-content-left[_ngcontent-%COMP%]   .item-content-tags[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-top: 15px;\\n  font-size: 12px;\\n}\\n.blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%]   .item-content[_ngcontent-%COMP%]   .item-content-right[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  width: 40%;\\n}\\n.blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]   .item-1[_ngcontent-%COMP%] {\\n  border-top-right-radius: 12px;\\n}\\n.blog[_ngcontent-%COMP%]   .blog-right-side[_ngcontent-%COMP%]   .item-3[_ngcontent-%COMP%] {\\n  border-bottom-right-radius: 12px;\\n}\\n\\n.tag[_ngcontent-%COMP%] {\\n  margin-left: 15px;\\n}\\n\\n.tag[_ngcontent-%COMP%]:first-child {\\n  margin-left: 0;\\n}\\n\\n.section[_ngcontent-%COMP%] {\\n  background-color: var(--white);\\n  border-top-left-radius: 28px;\\n  border-top-right-radius: 28px;\\n  border-bottom-left-radius: 28px;\\n  border-bottom-right-radius: 28px;\\n  margin-top: 0px;\\n}\\n.section[_ngcontent-%COMP%]   .section-content[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  transition: height 0.3s ease-out;\\n  margin: 10px;\\n  height: 0;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border: 1px solid #444070;\\n  font-size: 25px;\\n  text-align: center;\\n  padding: 15px 0;\\n  box-sizing: border-box;\\n  font-family: \\\"Raleway-Bold\\\";\\n  border-radius: 28px;\\n  position: relative;\\n  background: white;\\n}\\n.section-title[_ngcontent-%COMP%]   .toggle-section[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 9px;\\n  left: 10px;\\n}\\n.section-title[_ngcontent-%COMP%]   .toggle-section[_ngcontent-%COMP%]   .section-arrow[_ngcontent-%COMP%] {\\n  position: absolute;\\n  overflow: hidden;\\n  transition-duration: 0.2s;\\n  transition-property: transform;\\n  cursor: pointer;\\n}\\n\\n.tutorials-title[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-bottom: 1px solid var(--main-color);\\n  font-weight: bold;\\n  margin-bottom: 15px;\\n  padding-bottom: 10px;\\n}\\n\\n.guide[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n  position: relative;\\n}\\n.guide[_ngcontent-%COMP%]   .guide-main[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin-top: 10px;\\n  position: relative;\\n}\\n.guide[_ngcontent-%COMP%]   .guide-pdf[_ngcontent-%COMP%] {\\n  margin-top: 30px;\\n  border-radius: 30px;\\n  transition-duration: 0.2s;\\n  transition-property: transform;\\n}\\n\\n.guide-right[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  display: flex;\\n}\\n.guide-right[_ngcontent-%COMP%]   .main-color-button[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n}\\n\\n.blog-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  margin: 10px 0;\\n  position: relative;\\n}\\n\\n.input-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n  flex-basis: 100%;\\n}\\n\\n.input-fields[_ngcontent-%COMP%] {\\n  width: 100%;\\n  flex-basis: 100%;\\n}\\n\\n.suggest-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.history-title[_ngcontent-%COMP%], .history-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-top: 15px;\\n}\\n.history-title[_ngcontent-%COMP%]   .w50[_ngcontent-%COMP%], .history-list[_ngcontent-%COMP%]   .w50[_ngcontent-%COMP%] {\\n  width: 50%;\\n}\\n.history-title[_ngcontent-%COMP%]   .w30[_ngcontent-%COMP%], .history-list[_ngcontent-%COMP%]   .w30[_ngcontent-%COMP%] {\\n  width: 30%;\\n}\\n.history-title[_ngcontent-%COMP%]   .w20[_ngcontent-%COMP%], .history-list[_ngcontent-%COMP%]   .w20[_ngcontent-%COMP%] {\\n  width: 20%;\\n}\\n\\n.history-title[_ngcontent-%COMP%] {\\n  color: #93949e;\\n}\\n\\n.modal[_ngcontent-%COMP%] {\\n  width: 60%;\\n}\\n\\n.recommend-list[_ngcontent-%COMP%] {\\n  display: none;\\n  border-radius: 12px;\\n  background-color: white;\\n  border: 1px solid var(--main-color);\\n  position: absolute;\\n  \\n\\n  top: 230px;\\n  right: 0px;\\n  z-index: 1;\\n  flex-direction: column;\\n  max-height: 300px;\\n  overflow: auto;\\n}\\n.recommend-list[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  color: #93949e;\\n  margin-top: 10px;\\n}\\n.recommend-list[_ngcontent-%COMP%]   .recommend-list-item[_ngcontent-%COMP%] {\\n  margin-top: 10px;\\n}\\n\\n.rec-button[_ngcontent-%COMP%] {\\n  position: sticky;\\n  bottom: 0;\\n  margin-top: 10px;\\n}\\n\\n.goal-burger[_ngcontent-%COMP%] {\\n  margin-left: 10px;\\n  position: relative;\\n  border-radius: 8px;\\n  width: 30px;\\n  height: 30px;\\n  padding: 5px 0;\\n  border: 1px solid var(--main-color);\\n  display: flex;\\n  align-items: center;\\n  box-sizing: border-box;\\n  justify-content: center;\\n  cursor: pointer;\\n}\\n.goal-burger[_ngcontent-%COMP%]   .inner-circle[_ngcontent-%COMP%] {\\n  width: 3px;\\n  height: 3px;\\n  border-radius: 50%;\\n  background-color: var(--main-color);\\n  margin: 2px;\\n}\\n\\n.goal-burger[_ngcontent-%COMP%]:hover {\\n  background-color: var(--main-color);\\n}\\n.goal-burger[_ngcontent-%COMP%]:hover   .inner-circle[_ngcontent-%COMP%] {\\n  background-color: white;\\n}\\n\\n.goal-menu[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 0;\\n  top: 0;\\n  background-color: white;\\n  border-radius: 8px;\\n  border: 1px solid lightgray;\\n  padding: 10px;\\n}\\n.goal-menu[_ngcontent-%COMP%]   .goal-actions[_ngcontent-%COMP%] {\\n  margin-top: 15px;\\n}\\n.goal-menu[_ngcontent-%COMP%]   .goal-actions[_ngcontent-%COMP%]   .link-main-color[_ngcontent-%COMP%] {\\n  margin: 5px 0;\\n}\\n\\n[_nghost-%COMP%]     .bulletStyle {\\n  padding: 0.5rem;\\n}\\n[_nghost-%COMP%]     .bulletStyle .p-carousel-indicator {\\n  margin-bottom: 0;\\n}\\n[_nghost-%COMP%]     .bulletStyle .p-carousel-indicator.p-highlight button {\\n  background-color: #283583 !important;\\n}\\n[_nghost-%COMP%]     .bulletStyle .p-carousel-indicator button {\\n  background: #AAA !important;\\n  border-radius: 100px;\\n  height: 8px;\\n  width: 8px;\\n}\\n\\n.class-filters[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 3;\\n  width: 100%;\\n  height: auto;\\n  color: #666;\\n  text-align: center;\\n  box-sizing: border-box;\\n  border: 6px solid transparent;\\n  background-clip: padding-box, border-box;\\n  background-origin: padding-box, border-box;\\n  background-image: linear-gradient(#fff, #fff), linear-gradient(#8497FF, #2E3D90);\\n  padding-bottom: 1rem;\\n}\\n@media only screen and (min-width: 768px) {\\n  .class-filters[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n  }\\n}\\n@media only screen and (min-width: 576px) {\\n  .class-filters[_ngcontent-%COMP%]   .filters-content[_ngcontent-%COMP%] {\\n    padding-left: 11rem;\\n  }\\n}\\n.class-filters[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%] {\\n  position: absolute;\\n  display: inline-block;\\n  left: -35px;\\n  top: -10px;\\n}\\n@media only screen and (max-width: 768px) {\\n  .class-filters[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%] {\\n    position: relative;\\n    left: auto;\\n    width: 100%;\\n    top: auto;\\n    height: 54px;\\n    background-image: linear-gradient(180deg, hsl(231, 100%, 76%) 0%, hsl(231, 82%, 71%) 11%, hsl(231, 69%, 67%) 22%, hsl(232, 60%, 63%) 33%, hsl(232, 52%, 59%) 44%, hsl(232, 46%, 54%) 56%, hsl(232, 41%, 50%) 67%, hsl(232, 44%, 46%) 78%, hsl(231, 47%, 42%) 89%, hsl(231, 52%, 37%) 100%);\\n  }\\n}\\n.class-filters[_ngcontent-%COMP%]   .image-container[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  display: none;\\n  width: 100%;\\n  height: auto;\\n}\\n.class-filters[_ngcontent-%COMP%]   .text-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  text-align: center;\\n  color: white;\\n  padding: 10px;\\n}\\n.class-filters[_ngcontent-%COMP%]   .text-overlay[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n}\\n\\n[_nghost-%COMP%]     .small-input-switch .p-inputswitch {\\n  height: 1rem;\\n  width: 1.8rem;\\n}\\n[_nghost-%COMP%]     .small-input-switch .p-inputswitch.p-inputswitch-checked .p-inputswitch-slider::before {\\n  transform: translateX(1rem);\\n  background-image: linear-gradient(90deg, hsl(229, 95%, 71%) 0%, hsl(230, 90%, 68%) 11%, hsl(230, 86%, 66%) 22%, hsl(230, 83%, 63%) 33%, hsl(231, 79%, 60%) 44%, hsl(231, 77%, 58%) 56%, hsl(231, 74%, 55%) 67%, hsl(230, 72%, 52%) 78%, hsl(230, 75%, 48%) 89%, hsl(227, 90%, 43%) 100%);\\n}\\n[_nghost-%COMP%]     .small-input-switch .p-inputswitch .p-inputswitch-slider {\\n  background-image: linear-gradient(90deg, hsl(227, 51%, 93%) 0%, hsl(227, 55%, 92%) 11%, hsl(227, 58%, 91%) 22%, hsl(228, 61%, 90%) 33%, hsl(228, 62%, 88%) 44%, hsl(228, 64%, 87%) 56%, hsl(228, 65%, 86%) 67%, hsl(228, 66%, 85%) 78%, hsl(228, 67%, 84%) 89%, hsl(228, 67%, 83%) 100%);\\n}\\n[_nghost-%COMP%]     .small-input-switch .p-inputswitch .p-inputswitch-slider::before {\\n  width: 0.8rem;\\n  height: 0.8rem;\\n  margin-top: -0.4rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "UntypedFormControl", "UntypedFormGroup", "Validators", "fork<PERSON><PERSON>n", "of", "catchError", "finalize", "map", "skip", "switchMap", "take", "Status", "LessonStatus", "StudentTrialStatus", "UserRole", "SubSink", "data", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵelementContainer", "ɵɵtemplate", "DashboardComponent_Defer_0_ng_container_2_ng_container_1_ng_container_1_Template", "ɵɵadvance", "ɵɵproperty", "teacherTemplate_r3", "ctx_r1", "templateContext", "DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_2_ng_container_1_Template", "newOrForwaredTrialStatusTemplate_r4", "DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_3_ng_container_1_Template", "ignoredOrLostTrialStatusTemplate_r5", "DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_4_ng_container_1_Template", "paidOrActiveTemplate_r6", "DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_5_app_redirect_to_galaxy_1_Template", "status_r7", "toLowerCase", "StudentStatus", "GALAXY", "banner_r9", "bannerMode", "DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_6_p_carousel_3_ng_template_1_Template", "banners", "length", "DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_6_p_carousel_3_Template", "ɵɵlistener", "DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_6_Template_app_calendar_agenda_dayClicked_13_listener", "$event", "ɵɵrestoreView", "_r8", "ɵɵnextContext", "ɵɵresetView", "onAgendaDayClicked", "ɵɵstyleMap", "ɵɵpureFunction0", "_c4", "context", "lessons", "_c5", "currentAgendaDate", "DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_7_ng_container_1_Template", "DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_8_ng_container_1_Template", "DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_9_app_dashboard_request_trial_form_8_Template_app_dashboard_request_trial_form_formSubmitted_0_listener", "_r10", "onTrialRequestFormSubmitted", "DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_9_app_dashboard_request_trial_form_8_Template", "studentHasTrialRequest", "ɵɵtext", "DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_2_Template", "DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_3_Template", "DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_4_Template", "DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_5_Template", "DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_6_Template", "DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_7_Template", "DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_8_Template", "DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_9_Template", "DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_ng_container_10_Template", "FORWARDED", "IGNORED", "HAS_PAYMENT", "ASSIGNED", "LOST", "NEW", "NONE", "DashboardComponent_Defer_0_ng_container_2_ng_container_2_div_1_Template", "ɵɵpipeBind1", "studentStatus$", "DashboardComponent_Defer_0_ng_container_2_ng_container_1_Template", "DashboardComponent_Defer_0_ng_container_2_ng_container_2_Template", "role", "UserRoles", "TEACHER", "STUDENT", "DashboardComponent_Defer_0_ng_container_0_Template", "DashboardComponent_Defer_0_ng_container_2_Template", "<PERSON><PERSON><PERSON><PERSON>", "tmp_12_0", "banner_r12", "classRoom", "undefined", "_c7", "context_r13", "trialClassRooms", "teacher", "DashboardComponent_ng_template_14_p_carousel_2_ng_template_1_Template", "responsiveOptions", "DashboardComponent_ng_template_14_p_carousel_2_Template", "DashboardComponent_ng_template_14_Template_app_calendar_agenda_dayClicked_12_listener", "_r11", "DashboardComponent_ng_template_14_ng_container_14_Template", "DashboardComponent_ng_template_14_ng_container_15_Template", "lessonsBoxTemplate_r15", "ɵɵpureFunction1", "_c6", "$implicit", "classroomCardsTemplate_r14", "getNextStepNumber", "banner_r16", "DashboardComponent_ng_template_16_p_carousel_2_ng_template_1_Template", "DashboardComponent_ng_template_16_p_carousel_2_Template", "banner_r18", "DashboardComponent_ng_template_18_p_carousel_2_ng_template_1_Template", "context_r19", "_c8", "_c9", "DashboardComponent_ng_template_18_p_carousel_2_Template", "DashboardComponent_ng_template_18_Template_app_calendar_agenda_dayClicked_13_listener", "_r17", "DashboardComponent_ng_template_18_ng_container_15_Template", "DashboardComponent_ng_template_18_ng_container_16_Template", "DashboardComponent_ng_template_18_ng_container_17_Template", "todaysLessons", "context_r23", "filteredTrialClassRooms", "index_r22", "trialClassroom_r21", "trialClassroom_r24", "DashboardComponent_ng_template_20_ng_container_0_ng_container_3_ng_container_11_ng_container_7_Template", "filteredClassrooms", "DashboardComponent_ng_template_20_ng_container_0_ng_container_3_Template_app_classroom_status_filters_switchToggled_9_listener", "_r20", "toggleSwitch", "DashboardComponent_ng_template_20_ng_container_0_ng_container_3_ng_container_10_Template", "DashboardComponent_ng_template_20_ng_container_0_ng_container_3_ng_container_11_Template", "switches", "DashboardComponent_ng_template_20_ng_container_0_ng_container_3_Template", "DashboardComponent_ng_template_20_ng_container_0_Template", "context_r25", "classRooms", "DashboardComponent_ng_template_22_ng_container_0_Template", "context_r26", "DashboardComponent_ng_template_24_ng_container_1_Template", "context_r29", "banner_r28", "DashboardComponent_ng_template_26_p_carousel_2_ng_template_1_Template", "DashboardComponent_ng_template_26_p_carousel_2_Template", "DashboardComponent_ng_template_26_Template_app_calendar_agenda_dayClicked_13_listener", "_r27", "DashboardComponent_ng_template_26_ng_container_15_Template", "_c10", "lessonsBoxTemplateTeacher_r30", "DashboardComponent", "constructor", "generalService", "authService", "lessonService", "blogService", "tutorialService", "guideService", "goalService", "notificationService", "classroomService", "studentTrialService", "calendarService", "userService", "layoutService", "toastService", "cdr", "jitsiMeetService", "ratingAndReportService", "renderer", "ignoredOrLostTrialStatusTemplate", "newOrForwaredTrialStatusTemplate", "paidOrActiveTemplate", "classroomCardsWrapper", "subs", "loggedInUser", "allLessons", "oldValue", "newValue", "blog", "dummyBlogs", "guides", "showModal", "suggestForm", "controlErrors", "suggestError", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "recommendToUsers", "studentGoals", "allLessonsLoading", "todaysLessonsLoading", "showGoalsMenu", "notifications", "tutorials", "recHist<PERSON>", "userClassrooms", "loadingGoals", "loadingClassrooms", "today", "Date", "verticalViewPortHeight", "agendaHeight", "dummy<PERSON><PERSON><PERSON>", "divHeight", "user", "All", "Ongoing", "Completed", "Expired", "autoplayInterval", "hasAssignedLesson", "leftMenuHeight", "hasPayment", "getStyle", "date", "myDates", "year", "toString", "month", "padStart", "day", "dateString", "returnedS<PERSON>us", "includes", "executeCommand", "console", "log", "getParticipants", "api", "ngOnInit", "getLoggedInUser", "getUserRole", "isTrialRequested", "add", "deviceKind", "pipe", "subscribe", "res", "w576up", "adjustHeightListener", "loadInitialDataBasedOnRole", "initUpdateListener", "suggestion", "validators", "required", "<PERSON><PERSON><PERSON><PERSON>", "sink", "getRecHistory", "toret", "h", "some", "el", "studentName", "articleTitle", "push", "ngAfterViewInit", "loadScript", "url", "node", "document", "createElement", "setAttribute", "src", "type", "async", "charset", "getElementsByTagName", "append<PERSON><PERSON><PERSON>", "ngAfterContentInit", "updateListener", "getGoals", "ngOnDestroy", "unsubscribe", "onResize", "getNotificationColor", "ARRANGED", "COMPLETED", "getNotificationBd", "read", "getLessonColor", "status", "ARRANGED_TRIAL", "toggleSection", "sectionElement", "sectionArrowImgSrc", "onWindowScroll", "window", "pageYOffset", "gradientScale", "toggleReadAbout", "slideElements", "toggleSuggestIdea", "toggleHistory", "toggle<PERSON>iewG<PERSON>ls", "toggleSetGoals", "toggleEditGoals", "submitSuggestion", "valid", "getFormValidationErrors", "Object", "keys", "controls", "for<PERSON>ach", "key", "get", "errors", "keyError", "toggleRecommentTo", "element", "call", "getElementsByClassName", "id", "style", "display", "onAgendaHeight", "event", "closeRecommentListsOnClickOutside", "addEventListener", "setTimeout", "ignoreClickOnMeElement", "getElementById", "isClickInsideElement", "contains", "target", "showGuide", "offsetHeight", "classList", "remove", "height", "onRecommentCheckedList", "usersChecked", "rec", "ids", "share", "viewArticle", "blogItem", "open", "link", "isSent", "setShowToastmessage", "severity", "summary", "detail", "scrollTo", "updateStudentTrialStatus", "updateBanners", "loadInitialData", "userClassrooms$", "getLMSUserClassrooms", "tutorials$", "getTutorials", "userHasPayment$", "getUserHasPayment", "headers", "statusTrial$", "getStudentTrialStatus", "studentHasTrialRequest$", "getStudentHasTrialRequest", "hasTrialRequest", "trialObservables", "error", "statusTrial", "userHasPayment", "results", "classroomIds", "classroom", "lessonObservables", "classroomId", "getClassroomLessons", "newStatus", "getOngoingClassrooms", "isLastLessonWithinOneMonth", "isTrialClassroomWithCompletedFirstLesson", "isFirstLessonStatusInTrialClassroom", "isTrialClassroomDismissed", "loadInitialDataForTeacher", "observables", "filter", "unshift", "isStudentActive", "classrooms", "dateF", "formattedDate", "toLocaleDateString", "sideMenuHeight", "detectChanges", "stepToShow", "isTrialClassroomWithCompletedLesson", "resetSwitches", "filterLessons", "filterOngoing", "lesson", "levelFilter", "ON_GOING", "PENDING", "EXPIRED", "DISMISSED", "Dismissed", "reverse", "classroomsForCheck", "ongoingClassrooms", "_", "ɵɵdirectiveInject", "i1", "GeneralService", "i2", "AuthService", "i3", "LessonService", "i4", "BlogService", "i5", "TutorialService", "i6", "GuideService", "i7", "GoalService", "i8", "NotificationService", "i9", "ClassroomService", "i10", "StudentTrialService", "i11", "CalendarService", "i12", "UserService", "i13", "LayoutService", "i14", "ToastService", "ChangeDetectorRef", "i15", "JitsiMeetService", "i16", "RatingAndReportService", "Renderer2", "_2", "selectors", "viewQuery", "DashboardComponent_Query", "rf", "ctx", "DashboardComponent_resize_HostBindingHandler", "ɵɵresolveWindow", "DashboardComponent_scroll_HostBindingHandler", "DashboardComponent_Defer_0_Template", "DashboardComponent_DeferPlaceholder_1_Template", "ɵɵdefer", "DashboardComponent_Defer_2_DepsFn", "ɵɵdeferOnViewport", "DashboardComponent_Template_img_click_8_listener", "_r1", "DashboardComponent_Template_img_click_13_listener", "DashboardComponent_ng_template_14_Template", "ɵɵtemplateRefExtractor", "DashboardComponent_ng_template_16_Template", "DashboardComponent_ng_template_18_Template", "DashboardComponent_ng_template_20_Template", "DashboardComponent_ng_template_22_Template", "DashboardComponent_ng_template_24_Template", "DashboardComponent_ng_template_26_Template"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\dashboard\\dashboard.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { HttpHeaders } from '@angular/common/http';\r\nimport { ChangeDetectorRef, Component, ElementRef, HostListener, On<PERSON><PERSON>roy, OnInit, Renderer2, ViewChild } from '@angular/core';\r\nimport { UntypedFormControl, UntypedFormGroup, ValidationErrors, Validators } from '@angular/forms';\r\n\r\nimport { MenuItem } from 'primeng/api';\r\nimport { forkJoin, of } from 'rxjs';\r\nimport { catchError, finalize, map, skip, switchMap, take } from 'rxjs/operators';\r\nimport { Blog } from 'src/app/core/models/blog.model';\r\nimport { Classroom, ClassroomType, Level, Status, TrialTeacherRating } from 'src/app/core/models/classroom.model';\r\nimport { CircleLine, StudentGoal } from 'src/app/core/models/goal.model';\r\nimport { Guide } from 'src/app/core/models/guide.modal';\r\nimport { Lesson, LessonStatus } from 'src/app/core/models/lesson.model';\r\nimport { StudentTrialStatus } from 'src/app/core/models/student.model';\r\nimport { Tutorial } from 'src/app/core/models/tutorial.model';\r\nimport { User, UserRole } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { BlogService } from 'src/app/core/services/blog.service';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { GoalService } from 'src/app/core/services/goal.service';\r\nimport { GuideService } from 'src/app/core/services/guide.service';\r\nimport { LessonService } from 'src/app/core/services/lesson.service';\r\nimport { NotificationService } from 'src/app/core/services/notification.service';\r\nimport { StudentTrialService } from 'src/app/core/services/student-trial.service';\r\nimport { TutorialService } from 'src/app/core/services/tutorial.service';\r\nimport { SubSink } from 'subsink';\r\nimport * as data from '../../core/models/data';\r\nimport { UserService } from 'src/app/core/services/user.service';\r\nimport { LayoutService } from 'src/app/core/services/layout.service';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { CalendarService } from 'src/app/core/services/calendar.service';\r\nimport { JitsiMeetService } from 'src/app/core/services/jitsi-meet.service';\r\nimport { RatingAndReportService } from 'src/app/core/services/rating-and-report.service';\r\n\r\n@Component({\r\n  selector: 'app-dashboard',\r\n  templateUrl: './dashboard.component.html',\r\n  styleUrls: ['./dashboard.component.scss']\r\n})\r\nexport class DashboardComponent implements OnInit, OnDestroy {\r\n  @ViewChild('ignoredOrLostTrialStatusTemplate', { static: true }) ignoredOrLostTrialStatusTemplate: ElementRef = {} as ElementRef;\r\n  @ViewChild('newOrForwaredTrialStatusTemplate', { static: true }) newOrForwaredTrialStatusTemplate: ElementRef = {} as ElementRef;\r\n  @ViewChild('paidOrActiveTemplate', { static: true }) paidOrActiveTemplate: ElementRef = {} as ElementRef;\r\n  @ViewChild('classroomCardsWrapper', { static: false }) classroomCardsWrapper: ElementRef = {} as ElementRef;\r\n  private subs = new SubSink();\r\n  public loggedInUser: User = {} as User;\r\n  public allLessons: Lesson[] = [];\r\n  public todaysLessons: Lesson[] = [];\r\n  public oldValue: number = 0;\r\n  public newValue: number = 0;\r\n  public blog: Blog[] = this.blogService.dummyBlogs;\r\n  public guides: Guide[] = [];\r\n  public showModal: boolean = false;\r\n  public suggestForm: UntypedFormGroup = {} as UntypedFormGroup;\r\n  public controlErrors: ValidationErrors = {} as ValidationErrors;\r\n  public suggestError: boolean = false;\r\n  public suggestErrorLength: boolean = false;\r\n  public recommendToUsers: User[] = [];\r\n  public role: string = \"\";\r\n  public studentGoals: StudentGoal[] = [];\r\n  public allLessonsLoading: boolean = true;\r\n  public todaysLessonsLoading: boolean = true;\r\n  showGoalsMenu: boolean = false;\r\n  notifications: Notification[] = [];\r\n  tutorials: Tutorial[] = []\r\n  recHistory: any = [];\r\n  userClassrooms: Classroom[] = [];\r\n  loadingGoals: boolean = true;\r\n  loadingClassrooms: boolean = true;\r\n  value!: Date;\r\n  today = new Date();\r\n  studentHasTrialRequest = false;\r\n  showLoader = true;\r\n  banners: any[] = [];\r\n  items!: MenuItem[];\r\n  verticalViewPortHeight = 130;\r\n  agendaHeight = 0;\r\n  responsiveOptions = [];\r\n  LessonStatus = LessonStatus;\r\n  lessons = data.dummyLessons;\r\n  studentStatus$ = this.studentTrialService.studentStatus$;\r\n  StudentStatus = StudentTrialStatus;\r\n  currentAgendaDate = new Date();\r\n  divHeight: string = '140px';\r\n  UserRoles = UserRole;\r\n  public user: User = {} as User;\r\n  switches: Record<string, boolean> = {\r\n    All: false,\r\n    Ongoing: true,\r\n    Completed: false,\r\n    Expired: false,\r\n  };\r\n  autoplayInterval = 5000;\r\n  constructor(\r\n    public generalService: GeneralService,\r\n    public authService: AuthService,\r\n    private lessonService: LessonService,\r\n    private blogService: BlogService,\r\n    private tutorialService: TutorialService,\r\n    private guideService: GuideService,\r\n    private goalService: GoalService,\r\n    private notificationService: NotificationService,\r\n    private classroomService: ClassroomService,\r\n    private studentTrialService: StudentTrialService,\r\n    private calendarService: CalendarService,\r\n    private userService: UserService,\r\n    private layoutService: LayoutService,\r\n    private toastService: ToastService,\r\n    private cdr: ChangeDetectorRef,\r\n    private jitsiMeetService: JitsiMeetService,\r\n    private ratingAndReportService: RatingAndReportService,\r\n    private renderer: Renderer2,\r\n  ) { }\r\n\r\n  public templateContext = {\r\n    $implicit: { // default\r\n      hasAssignedLesson: false,\r\n      trialClassRooms: [] as Classroom[],\r\n      filteredTrialClassRooms: [] as Classroom[],\r\n      classRooms: [],\r\n      lessons: [],\r\n      todaysLessons: [],\r\n      leftMenuHeight: 0,\r\n      hasPayment: false,\r\n    }\r\n  }\r\n  // TODO: add real dates\r\n  getStyle(date: any) {\r\n    const myDates = ['2023-01-31', '2023-02-15', '2023-02-21', '2018-03-05'];\r\n    const year = date.year.toString();\r\n    const month = (date.month + 1).toString().padStart(2, '0');\r\n    const day = date.day.toString().padStart(2, '0');\r\n    const dateString = `${year}-${month}-${day}`;\r\n    const returnedStatus = myDates.includes(dateString);\r\n\r\n    return returnedStatus;\r\n  }\r\n\r\n\r\n  executeCommand(data: any) {\r\n    console.log(\r\n      'this.jitsiService.getParticipants():',\r\n      this.jitsiMeetService.getParticipants()\r\n    );\r\n\r\n    this.jitsiMeetService.api.executeCommand(\r\n      'sendEndpointTextMessage',\r\n      this.jitsiMeetService.getParticipants(),\r\n      'mover a principal'\r\n    );\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.user = this.authService.getLoggedInUser();\r\n    this.role = this.authService.getUserRole();\r\n    this.studentHasTrialRequest = this.studentTrialService.isTrialRequested;\r\n\r\n    this.allLessonsLoading = true;\r\n    this.loadingClassrooms = true;\r\n\r\n    this.subs.add(this.generalService.deviceKind.pipe().subscribe(res => {\r\n      if (res) {\r\n        if (res.w576up) {\r\n          this.verticalViewPortHeight = 130;\r\n          // this.renderer.addClass(this.document.getElementsByClassName('app')[0], 'h-full');\r\n        } else {\r\n          this.verticalViewPortHeight = 200;\r\n        }\r\n      }\r\n    }));\r\n    this.adjustHeightListener();\r\n    this.loadInitialDataBasedOnRole(this.role as UserRole);\r\n    this.initUpdateListener();\r\n\r\n    this.suggestForm = new UntypedFormGroup({\r\n      suggestion: new UntypedFormControl(null, {\r\n        validators: [Validators.required, Validators.minLength(20)],\r\n      })\r\n    })\r\n    // this.subs.sink = this.blogService.get().pipe(take(1)).subscribe(res => {\r\n    //   this.blog = res;\r\n    // })\r\n    this.subs.sink = this.blogService.getRecHistory().pipe(take(1)).subscribe(res => {\r\n      let toret: any = []\r\n      for (let h of res) {\r\n        if (!toret.some((el: any) => el.studentName == h.studentName && el.articleTitle == h.articleTitle)) {\r\n          toret.push(h)\r\n        }\r\n      }\r\n      this.recHistory = toret;\r\n    })\r\n  }\r\n\r\n  ngAfterViewInit() {\r\n\r\n    // document.getElementById('classrooms-big-title')!.style.right = -(window.innerWidth / 2 + 160) + \"px\";\r\n    // document.getElementById('tutorials-big-title')!.style.right = -(window.innerWidth / 2) + \"px\";\r\n    // document.getElementById('blog-big-title')!.style.left = -(window.innerWidth / 3.2) + \"px\";\r\n    // document.getElementById('guides-big-title')!.style.left = -(window.innerWidth / 3.2) + \"px\";\r\n  }\r\n\r\n\r\n  public loadScript(url: string) {\r\n    console.log('preparing to load...')\r\n    let node = document.createElement('script');\r\n    node.setAttribute(\"id\", \"lottie-player\");\r\n    node.src = url;\r\n    node.type = 'text/javascript';\r\n    node.async = true;\r\n    node.charset = 'utf-8';\r\n    document.getElementsByTagName('head')[0].appendChild(node);\r\n  }\r\n\r\n  ngAfterContentInit() {\r\n    if (this.role == UserRole.STUDENT) {\r\n      this.subs.add(this.goalService.updateListener.subscribe(res => {\r\n        this.subs.sink = this.goalService.getGoals().subscribe(res => {\r\n          this.loadingGoals = false;\r\n          this.studentGoals = res;\r\n        })\r\n      }));\r\n    }\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  @HostListener('window:resize')\r\n  onResize() {\r\n    this.adjustHeightListener();\r\n  }\r\n\r\n  getNotificationColor(type: string) {\r\n    if (type === LessonStatus.ARRANGED) {\r\n      return '#c056f5'\r\n    } else if (type === LessonStatus.COMPLETED) {\r\n      return '#3b9bed'\r\n    } else {\r\n      return '#F27769'\r\n    }\r\n  }\r\n\r\n  getNotificationBd(read: boolean) {\r\n    if (read) {\r\n      return \"white\"\r\n    } else {\r\n      return \"#efefef\"\r\n    }\r\n  }\r\n\r\n  getLessonColor(status: LessonStatus) {\r\n    if (status === LessonStatus.ARRANGED) {\r\n      return '#c056f5'\r\n    } else if (status === LessonStatus.ARRANGED_TRIAL) {\r\n      return '#E5B0FC'\r\n    }\r\n    else if (status === LessonStatus.COMPLETED) {\r\n      return '#3b9bed'\r\n    } else {\r\n      return '#F27769'\r\n    }\r\n  }\r\n\r\n  toggleSection(sectionElement: any, sectionArrowImgSrc: any) {\r\n    this.generalService.toggleSection(sectionElement, sectionArrowImgSrc, true)\r\n  }\r\n\r\n  @HostListener('window:scroll', ['$event'])\r\n  onWindowScroll($event: any) {\r\n    this.newValue = window.pageYOffset;\r\n    if (this.newValue > 400)\r\n      this.generalService.gradientScale(this.newValue, this.oldValue, 'classroom-gradient')\r\n    if (this.newValue > 1000)\r\n      this.generalService.gradientScale(this.newValue, this.oldValue, 'blog-gradient')\r\n    if (this.newValue > 1500)\r\n      this.generalService.gradientScale(this.newValue, this.oldValue, 'tutorial-gradient')\r\n    if (this.newValue > 2000)\r\n      this.generalService.gradientScale(this.newValue, this.oldValue, 'guide-gradient')\r\n    this.oldValue = this.newValue;\r\n  }\r\n\r\n  toggleReadAbout() {\r\n    this.showModal = !this.showModal;\r\n    this.generalService.slideElements(this.showModal, 'read-about')\r\n  }\r\n\r\n  toggleSuggestIdea() {\r\n    this.showModal = !this.showModal;\r\n    this.generalService.slideElements(this.showModal, 'suggest-idea')\r\n  }\r\n\r\n  toggleHistory() {\r\n    this.showModal = !this.showModal;\r\n    this.generalService.slideElements(this.showModal, 'recommendation-history')\r\n  }\r\n\r\n  toggleViewGoals() {\r\n    this.showModal = !this.showModal;\r\n    this.generalService.slideElements(this.showModal, 'view-goals')\r\n  }\r\n\r\n  toggleSetGoals() {\r\n    this.showModal = !this.showModal;\r\n    this.generalService.slideElements(this.showModal, 'set-goals')\r\n  }\r\n\r\n  toggleEditGoals() {\r\n    this.showModal = !this.showModal;\r\n    this.generalService.slideElements(this.showModal, 'edit-goals')\r\n  }\r\n\r\n  submitSuggestion() {\r\n    this.suggestError = false;\r\n    this.suggestErrorLength = false;\r\n    if (!this.suggestForm.valid) {\r\n      this.getFormValidationErrors()\r\n      return;\r\n    }\r\n    this.toggleSuggestIdea();\r\n  }\r\n\r\n  getFormValidationErrors() {\r\n    Object.keys(this.suggestForm.controls).forEach(key => {\r\n      this.controlErrors = this.suggestForm.get(key)!.errors!;\r\n      if (this.controlErrors != null) {\r\n        Object.keys(this.controlErrors).forEach(keyError => {\r\n          if (keyError == 'required') {\r\n            this.suggestError = true;\r\n          }\r\n          if (keyError == 'minlength') {\r\n            this.suggestErrorLength = true;\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  toggleRecommentTo(element: any) {\r\n    [].forEach.call(document.getElementsByClassName('recommend-list'), (el: any) => {\r\n      if (el.id !== element.id)\r\n        el.style.display = 'none'\r\n    })\r\n    if (element.style.display === 'none' || element.style.display === '') {\r\n      element.style.display = 'flex'\r\n    } else {\r\n      element.style.display = 'none'\r\n    }\r\n  }\r\n\r\n  onAgendaHeight(event: any) {\r\n    this.agendaHeight = event;\r\n  }\r\n\r\n  closeRecommentListsOnClickOutside() {\r\n    document.addEventListener('click', (event: any) => {\r\n      setTimeout(() => {// i dont know why..\r\n        let ignoreClickOnMeElement = document.getElementById('blog-right-side');\r\n        var isClickInsideElement = ignoreClickOnMeElement?.contains(event.target);\r\n        if (!isClickInsideElement) {\r\n          [].forEach.call(document.getElementsByClassName('recommend-list'), (el: any) => {\r\n            el.style.display = 'none'\r\n          })\r\n        }\r\n      }, 0);\r\n    });\r\n  }\r\n\r\n  showGuide(el: any) {\r\n    if (el.offsetHeight == 0) {\r\n      document.getElementById(el.id)?.classList.remove('no-visibility');\r\n      document.getElementById(el.id)?.classList.add('yes-visibility');\r\n      el.style.height = \"600px\"\r\n    } else {\r\n      document.getElementById(el.id)?.classList.remove('yes-visibility');\r\n      document.getElementById(el.id)?.classList.add('no-visibility');\r\n      el.style.height = \"0\"\r\n    }\r\n  }\r\n\r\n  onRecommentCheckedList(event: any) {\r\n    this.recommendToUsers = event.usersChecked;\r\n  }\r\n\r\n  rec(id: number) {\r\n    let ids = []\r\n    for (let user of this.recommendToUsers) {\r\n      ids.push(user.id)\r\n    }\r\n    this.blogService.share(ids, id).pipe(take(1)).subscribe(res => {\r\n    })\r\n  }\r\n\r\n  viewArticle(blogItem: Blog) {\r\n    window.open(blogItem.link, \"_blank\")\r\n  }\r\n\r\n  onTrialRequestFormSubmitted(isSent: boolean) {\r\n    if (isSent) {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'success',\r\n        summary: '',\r\n        detail: 'Trial request has been sent.'\r\n      });\r\n      window.scrollTo(0, 0);\r\n      this.studentTrialService.updateStudentTrialStatus(StudentTrialStatus.NEW);\r\n      this.updateBanners(StudentTrialStatus.NEW, []);\r\n    }\r\n  }\r\n\r\n  loadInitialData(): void {\r\n    this.banners = [];\r\n    const userClassrooms$ = this.classroomService.getLMSUserClassrooms(this.user.id);\r\n    const tutorials$ = this.tutorialService.getTutorials();\r\n    const userHasPayment$ = this.userService.getUserHasPayment();\r\n    const headers = new HttpHeaders({\r\n      'X-Bypass-Error-Interceptor': 'true'\r\n    });\r\n    const statusTrial$ = this.studentTrialService.getStudentTrialStatus(headers);\r\n    const studentHasTrialRequest$ = this.studentTrialService.getStudentHasTrialRequest();\r\n    this.showLoader = true;\r\n    this.subs.sink = studentHasTrialRequest$.pipe(\r\n      switchMap((hasTrialRequest) => {\r\n        if (hasTrialRequest) {\r\n          const trialObservables = {\r\n            userClassrooms: userClassrooms$.pipe(catchError(error => of(error))),\r\n            statusTrial: statusTrial$.pipe(catchError(error => of(false))),\r\n            userHasPayment: userHasPayment$.pipe(catchError(error => of(error))),\r\n            tutorials: tutorials$.pipe(catchError(error => of(error))),\r\n          };\r\n          return forkJoin(trialObservables);\r\n        } else {\r\n          return of(hasTrialRequest);\r\n        }\r\n      }),\r\n      switchMap((results: any) => {\r\n        // false is for no  trial status found (currently backend returns 400 error code if no trial is found)\r\n        if (results === false) {\r\n          return of(false);\r\n        }\r\n        const classroomIds = results.userClassrooms.map((classroom: any) => classroom.id);\r\n\r\n        if (classroomIds.length === 0) {\r\n          return of({ ...results, lessons: [] });\r\n        }\r\n\r\n        const lessonObservables = classroomIds.map((classroomId: string) =>\r\n          this.classroomService.getClassroomLessons(classroomId)\r\n        );\r\n\r\n        return forkJoin(lessonObservables).pipe(\r\n          map((lessons) => ({ ...results, lessons }))\r\n        );\r\n      }),\r\n      catchError((error) => {\r\n        console.error(error);\r\n        this.showLoader = false;\r\n        return of({});\r\n      }),\r\n      finalize(() => {\r\n        this.allLessonsLoading = false;\r\n        this.loadingClassrooms = false;\r\n        this.showLoader = false;\r\n      })\r\n    ).subscribe((res: any) => {\r\n      this.showLoader = false;\r\n      console.log(res);\r\n\r\n      if (res === false) {\r\n        this.studentTrialService.updateStudentTrialStatus(StudentTrialStatus.NONE);\r\n      } else {\r\n        const trialClassRooms = res.userClassrooms;\r\n        console.log(res);\r\n        let newStatus = StudentTrialStatus.NONE;\r\n        if (!res.statusTrial && !res.userHasPayment) {\r\n          newStatus = trialClassRooms.length > 0 ? StudentTrialStatus.FORWARDED : StudentTrialStatus.NONE;\r\n        } else {\r\n          newStatus = res.userHasPayment ? StudentTrialStatus.HAS_PAYMENT : res.statusTrial.toLowerCase();\r\n          if (newStatus.toLowerCase().includes('Trial'.toLowerCase())) {\r\n            newStatus = StudentTrialStatus.NEW;\r\n          }\r\n        }\r\n        // set status for dashboard\r\n\r\n        this.templateContext.$implicit.hasAssignedLesson = true;\r\n        this.templateContext.$implicit.hasPayment = res.userHasPayment;\r\n        this.templateContext.$implicit.trialClassRooms = trialClassRooms;\r\n        this.templateContext.$implicit.filteredTrialClassRooms = trialClassRooms;\r\n\r\n        // If there are no ongoing classrooms, toggle the switch to the \"All\" position.\r\n        // If there are ongoing classrooms, toggle the switch to the \"Ongoing\" position.\r\n        if (this.getOngoingClassrooms().length === 0) {\r\n          this.toggleSwitch('All');\r\n        } else {\r\n          this.toggleSwitch('Ongoing');\r\n        }\r\n        this.templateContext.$implicit.lessons = res.lessons;\r\n        // this.layoutService.updateMenuItemDisabledStatus(\"Classrooms\", trialClassRooms.length === 0);\r\n        // if (this.isStudentActive(trialClassRooms, res.userHasPayment)) {\r\n        //   this.layoutService.updateAllMenuItemsDisabledStatus(false);\r\n        // } else {\r\n        //   this.layoutService.updateMenuItemClassroomToTrialsForStudent();\r\n        // }\r\n\r\n        trialClassRooms.forEach((classroom: any) => {\r\n          console.log(classroom);\r\n          if (this.classroomService.isLastLessonWithinOneMonth(classroom)) {\r\n\r\n            if (this.classroomService.isTrialClassroomWithCompletedFirstLesson(classroom)) {\r\n              this.banners.push({ bannerMode: 'trial-assigned', classRoom: classroom });\r\n            } else {\r\n              if (this.classroomService.isFirstLessonStatusInTrialClassroom(classroom, LessonStatus.ARRANGED)) {\r\n                this.banners.push({ bannerMode: 'trial-arranged', classRoom: classroom });\r\n              } else if (this.classroomService.isTrialClassroomDismissed(classroom)) {\r\n                this.banners.push({ bannerMode: 'trial-dismissed', classRoom: classroom });\r\n              } else {\r\n                this.banners.push({ bannerMode: 'trial-assigned', classRoom: classroom });\r\n              }\r\n            }\r\n          }\r\n        });\r\n\r\n        console.log(newStatus)\r\n        this.updateBanners(newStatus, trialClassRooms);\r\n\r\n        // Adding banner in the second position\r\n        // TODO: add when referral system is ready\r\n        // this.banners.splice(1, 0, { bannerMode: 'primary-referral-code' });\r\n\r\n        this.studentTrialService.updateStudentTrialStatus(newStatus);\r\n      }\r\n    });\r\n  }\r\n\r\n  loadInitialDataForTeacher() {\r\n    this.banners = [];\r\n    const userClassrooms$ = this.classroomService.getLMSUserClassrooms(this.user.id);\r\n    const headers = new HttpHeaders({\r\n      'X-Bypass-Error-Interceptor': 'true'\r\n    });\r\n    this.subs.add(userClassrooms$.pipe(\r\n      switchMap((userClassrooms) => {\r\n        if (userClassrooms) {\r\n          const lessonObservables = userClassrooms.map((classroom: any) =>\r\n            this.classroomService.getClassroomLessons(classroom.id)\r\n          );\r\n          const observables = {\r\n            userClassrooms: userClassrooms$,\r\n            lessons: forkJoin(lessonObservables),\r\n          };\r\n          return forkJoin(observables);\r\n        } else {\r\n          return of(userClassrooms);\r\n        }\r\n      }),\r\n      catchError((error) => {\r\n        console.error(error);\r\n        this.showLoader = false;\r\n        return of({});\r\n      }),\r\n      finalize(() => {\r\n        this.updateBanners('', []);\r\n        this.showLoader = false;\r\n      })\r\n    ).subscribe((data: any) => {\r\n      this.showLoader = false;\r\n      // this.updateBanners('', []);\r\n      this.templateContext.$implicit.classRooms = data.userClassrooms;\r\n      // filter out empty lessons\r\n      this.templateContext.$implicit.lessons = data.lessons.filter((lessons: []) => lessons.length > 0);\r\n      this.templateContext.$implicit.todaysLessons = data.todaysLessons;\r\n    }));\r\n  }\r\n\r\n  // TODO: check if this is needed\r\n  // getKeyByValue(object: any, value: any): string | undefined {\r\n  //   for (const key in object) {\r\n  //     if (object[key] === value) {\r\n  //       return key;\r\n  //     }\r\n  //   }\r\n  //   return undefined;\r\n  // }\r\n\r\n\r\n  updateBanners(status: string, trialClassRooms: any[]) {\r\n    // TODO: check for more statuses\r\n    // this.studentTrialService.studentStatus$.subscribe((status: StudentTrialStatus) => {\r\n    if (this.role === this.UserRoles.TEACHER) {\r\n      this.banners.push({ bannerMode: 'primary' });\r\n    }\r\n    switch (status) {\r\n      case StudentTrialStatus.HAS_PAYMENT:\r\n        this.banners.unshift({ bannerMode: trialClassRooms.length > 0 ? 'primary-assigned' : 'primary-received' });\r\n        break;\r\n      case StudentTrialStatus.ASSIGNED:\r\n        this.banners.unshift({ bannerMode: 'trial-assigned' });\r\n        break;\r\n      case StudentTrialStatus.FORWARDED:\r\n        trialClassRooms.length > 0 ? null : this.banners.unshift({ bannerMode: 'trial-received' });\r\n        break;\r\n      case StudentTrialStatus.NEW:\r\n      case StudentTrialStatus.NONE:\r\n        this.banners.unshift({ bannerMode: 'trial-received' });\r\n        break;\r\n\r\n      case StudentTrialStatus.IGNORED:\r\n      case StudentTrialStatus.LOST:\r\n        this.banners.push({ bannerMode: 'unreachable' });\r\n        break;\r\n      case StudentTrialStatus.GALAXY:\r\n        break;\r\n    }\r\n  }\r\n\r\n  isStudentActive(classrooms: any[], hasPayment: boolean): boolean {\r\n    return classrooms.length > 0 && hasPayment;\r\n  }\r\n\r\n  onAgendaDayClicked(date: any) {\r\n    const dateF = new Date(date);\r\n    this.currentAgendaDate = dateF;\r\n    const formattedDate = dateF.toLocaleDateString('en-GB');\r\n    console.log(formattedDate);\r\n  }\r\n\r\n  adjustHeightListener() {\r\n    this.subs.sink = this.layoutService.sideMenuHeight.pipe(skip(1)).subscribe((res) => {\r\n      if (res !== 0) {\r\n        this.templateContext.$implicit.leftMenuHeight = res;\r\n        this.cdr.detectChanges(); // Manually trigger change detection // TODO: improve\r\n      }\r\n    });\r\n  }\r\n\r\n  getNextStepNumber(classrooms: Classroom[]): number {\r\n    if (!classrooms || classrooms.length === 0) {\r\n      return 1;\r\n    }\r\n    const stepToShow = classrooms.some((classroom) => this.classroomService.isTrialClassroomWithCompletedLesson(classroom)) ? 3 : 2;\r\n    return stepToShow;\r\n  }\r\n\r\n  toggleSwitch(key: string) {\r\n    console.log(key);\r\n    this.resetSwitches();\r\n    switch (key) {\r\n      case 'All':\r\n      case 'Ongoing':\r\n      case 'Active':\r\n      case 'Completed':\r\n      case 'Expired':\r\n      case 'Dismissed':\r\n      case 'Freeze':\r\n        this.switches[key] = !this.switches[key];\r\n        break;\r\n    }\r\n    this.filterLessons();\r\n  }\r\n\r\n  private resetSwitches() {\r\n    for (const key in this.switches) {\r\n      this.switches[key] = false;\r\n    }\r\n  }\r\n\r\n  /**\r\n  * Filters the list of lessons based on the selected level and status filters.\r\n  * @function\r\n  * @returns {void}\r\n  */\r\n  private filterLessons(filterOngoing = false) {\r\n    // Filter the lessons based on the selected switches\r\n    this.templateContext.$implicit.filteredTrialClassRooms = this.templateContext.$implicit.trialClassRooms.filter((lesson: Classroom) => {\r\n      let levelFilter: any;\r\n\r\n      // Filter by \"Ongoing\" switch\r\n      if (this.switches.Ongoing || filterOngoing) {\r\n        // Return lessons that have an \"ongoing\" status, \"arranged\" status, or \"pending\" status\r\n        return (\r\n          lesson.status.toLowerCase().includes(Status.ON_GOING.toLowerCase()) ||\r\n          lesson.status.toLowerCase().includes(LessonStatus.ARRANGED.toLowerCase()) ||\r\n          lesson.status.toLowerCase().includes(Status.PENDING.toLowerCase())\r\n        ) && (!levelFilter || levelFilter(lesson));\r\n      }\r\n\r\n      // Filter by \"Completed\" switch\r\n      if (this.switches.Completed) {\r\n        // Return lessons that have a \"completed\" status\r\n        return lesson.status.toLowerCase().includes(Status.COMPLETED.toLowerCase()) && (!levelFilter || levelFilter(lesson));\r\n      }\r\n\r\n      // Filter by \"Expired\" switch\r\n      if (this.switches.Expired) {\r\n        // Return lessons that have an \"expired\" or \"dismissed\" status\r\n        return (\r\n          lesson.status.toLowerCase().includes(Status.EXPIRED.toLowerCase()) ||\r\n          lesson.status.toLowerCase().includes(Status.DISMISSED.toLowerCase())\r\n        ) && (!levelFilter || levelFilter(lesson));\r\n      }\r\n\r\n      // Filter by \"Dismissed\" switch\r\n      if (this.switches.Dismissed) {\r\n        // Return lessons that have a \"dismissed\" status\r\n        return lesson.status.toLowerCase().includes(Status.DISMISSED.toLowerCase()) && (!levelFilter || levelFilter(lesson));\r\n      }\r\n\r\n      // Filter by \"All\" switch\r\n      if (this.switches.All) {\r\n        // Return all lessons\r\n        return true && (!levelFilter || levelFilter(lesson));\r\n      }\r\n\r\n      // Return false for all other cases\r\n      return false;\r\n    }).reverse();\r\n  }\r\n\r\n  private getOngoingClassrooms() {\r\n    const classroomsForCheck = this.templateContext.$implicit.filteredTrialClassRooms;\r\n    const ongoingClassrooms = classroomsForCheck.filter((lesson: Classroom) => {\r\n      return (\r\n        lesson.status.toLowerCase().includes(Status.ON_GOING.toLowerCase()) ||\r\n        lesson.status.toLowerCase().includes(LessonStatus.ARRANGED.toLowerCase()) ||\r\n        lesson.status.toLowerCase().includes(Status.PENDING.toLowerCase())\r\n      );\r\n    });\r\n    return ongoingClassrooms;\r\n  }\r\n\r\n  private loadInitialDataBasedOnRole(role: UserRole): void {\r\n    if (role === UserRole.STUDENT) {\r\n      this.loadInitialData();\r\n    } else {\r\n      this.loadInitialDataForTeacher();\r\n    }\r\n  }\r\n  private initUpdateListener() {\r\n    this.subs.add(this.calendarService.updateListener.subscribe((res: any) => {\r\n      if (res) {\r\n        this.loadInitialDataBasedOnRole(this.role as UserRole);\r\n      }\r\n    }));\r\n  }\r\n}\r\n", "@defer(on viewport) {\r\n<ng-container *ngIf=\"showLoader\">\r\n  <div class=\"grid\">\r\n    <div class=\"m-auto\">\r\n      <app-loader></app-loader>\r\n    </div>\r\n  </div>\r\n</ng-container>\r\n\r\n<div class=\"dashboard-wrapper pt-3 px-2 sm:px-3 md:pt-0 md:px-0\">\r\n  <ng-container *ngIf=\"!showLoader\">\r\n    <ng-container *ngIf=\"role == UserRoles.TEACHER\">\r\n      <ng-container *ngTemplateOutlet=\"teacherTemplate; context: templateContext\"></ng-container>\r\n    </ng-container>\r\n    <ng-container *ngIf=\"role == UserRoles.STUDENT\">\r\n    <div *ngIf=\"(studentStatus$ | async) as status\">\r\n      <ng-container [ngSwitch]=\"status\">\r\n        <ng-container *ngSwitchCase=\"StudentStatus.FORWARDED\">\r\n          <!-- Display content for the \"FORWARDED\" status -->\r\n          <ng-container *ngTemplateOutlet=\"newOrForwaredTrialStatusTemplate; context: templateContext\"></ng-container>\r\n        </ng-container>\r\n        <ng-container *ngSwitchCase=\"StudentStatus.IGNORED\">\r\n          <!-- Display content for the \"IGNORED\" status -->\r\n          <ng-container *ngTemplateOutlet=\"ignoredOrLostTrialStatusTemplate\"></ng-container>\r\n        </ng-container>\r\n        <ng-container *ngSwitchCase=\"StudentStatus.HAS_PAYMENT\">\r\n          <!-- Display content for the \"HAS_PAYMENT\" status -->\r\n          <!--  context: context -->\r\n            <ng-container *ngTemplateOutlet=\"paidOrActiveTemplate; context: templateContext\">\r\n          </ng-container>\r\n        </ng-container>\r\n        <ng-container *ngSwitchCase=\"StudentStatus.GALAXY\">\r\n          <!-- Display content for the \"GALAXY\" status -->\r\n          <app-redirect-to-galaxy *ngIf=\"status.toLowerCase() === StudentStatus.GALAXY.toLowerCase()\"></app-redirect-to-galaxy>\r\n        </ng-container>\r\n        <ng-container *ngSwitchCase=\"StudentStatus.ASSIGNED\">\r\n          <!-- Display content for the \"ASSIGNED\" status -->\r\n          <div class=\"grid flex justify-content-between\">\r\n            <div class=\"col-12\">\r\n              <p-carousel *ngIf=\"banners.length > 0\" [value]=\"banners\" [autoplayInterval]=\"5000\"\r\n                indicatorsContentClass=\"bulletStyle\" [verticalViewPortHeight]=\"'130px'\" [orientation]=\"'vertical'\"\r\n                [numVisible]=\"1\" [page]=\"1\" [numScroll]=\"1\" [showNavigators]=\"false\"\r\n                [showIndicators]=\"banners.length > 1 ? true : false\" [circular]=\"banners.length > 1 ? true : false\">\r\n                <ng-template let-banner pTemplate=\"item\">\r\n                  <app-intro-gradient [bannerMode]=\"banner.bannerMode\"></app-intro-gradient>\r\n                </ng-template>\r\n              </p-carousel>\r\n            </div>\r\n          </div>\r\n          <div class=\"grid flex justify-content-between\">\r\n            <div class=\"col-12 md:col-6\">\r\n              <app-goal-steps-box [isSmall]=\"true\"></app-goal-steps-box>\r\n            </div>\r\n            <div class=\"col-12 md:col-6\">\r\n              <app-block-viewer header=\"Notifications\"\r\n                headerBackgroundImage=\"/assets/images/dashboard/gradient-sm-1.png\"\r\n                blockClass=\"block-gradient border-radius-bottom-10\" containerClass=\"\" actionButtonText=\"See All\"\r\n                actionButtonRoute=\"/notifications\">\r\n                <p-scrollPanel [style]=\"{width: '100%', height: '118px'}\" styleClass=\"custombar1\">\r\n                  <app-dashboard-notifications [slimStyle]=\"true\"></app-dashboard-notifications>\r\n                </p-scrollPanel>\r\n              </app-block-viewer>\r\n            </div>\r\n          </div>\r\n          <div class=\"grid flex justify-content-between\">\r\n            <div class=\"col-12 md:col-6 h-full\">\r\n              <app-calendar-agenda [lessons]=\"context.lessons\" (dayClicked)=\"onAgendaDayClicked($event)\"></app-calendar-agenda>\r\n            </div>\r\n            <div class=\"col-12 md:col-6\">\r\n              <app-lesson-schedule-box [lessons]=\"[]\" [disableAddNewLessonButton]=\"true\"\r\n                [currentDate]=\"currentAgendaDate\"></app-lesson-schedule-box>\r\n            </div>\r\n          </div>\r\n          <app-classroom-card color=\"trial\"></app-classroom-card>\r\n          <app-dashboard-next-steps [activeStep]=\"2\"></app-dashboard-next-steps>\r\n          <app-dashboard-benefits></app-dashboard-benefits>\r\n          <app-goal-steps-box></app-goal-steps-box>\r\n        </ng-container>\r\n        <ng-container *ngSwitchCase=\"StudentStatus.LOST\">\r\n          <!-- Display content for the \"LOST\" status -->\r\n          <ng-container *ngTemplateOutlet=\"ignoredOrLostTrialStatusTemplate\"></ng-container>\r\n        </ng-container>\r\n        <ng-container *ngSwitchCase=\"StudentStatus.NEW\">\r\n          <!-- Display content for the \"NEW\" status -->\r\n          <ng-container *ngTemplateOutlet=\"newOrForwaredTrialStatusTemplate; context: templateContext\"></ng-container>\r\n        </ng-container>\r\n        <ng-container *ngSwitchCase=\"StudentStatus.NONE\">\r\n          <!-- Display content for the \"NONE\" status -->\r\n          <div class=\"grid flex justify-content-between\">\r\n            <div class=\"col-12 md:col-6\">\r\n              <app-calendar-preview-week></app-calendar-preview-week>\r\n            </div>\r\n            <div class=\"col-12 md:col-6\">\r\n              <app-block-viewer header=\"Notifications\"\r\n                headerBackgroundImage=\"/assets/images/dashboard/gradient-sm-1.png\"\r\n                blockClass=\"block-gradient border-round-xl h-full\" containerClass=\"\" actionButtonText=\"See All\"\r\n                actionButtonRoute=\"/notifications\"\r\n                [containerHeight]=\"118\">\r\n                <p-scrollPanel [style]=\"{width: '100%', height: '118px'}\" styleClass=\"custombar1\">\r\n                  <app-dashboard-notifications [slimStyle]=\"true\"></app-dashboard-notifications>\r\n                </p-scrollPanel>\r\n              </app-block-viewer>\r\n            </div>\r\n          </div>\r\n\r\n          <app-dashboard-request-trial-form *ngIf=\"!studentHasTrialRequest\"\r\n            (formSubmitted)=\"onTrialRequestFormSubmitted($event)\"></app-dashboard-request-trial-form>\r\n            <div class=\"classroom-cards-space\">\r\n          <app-dashboard-next-steps></app-dashboard-next-steps>\r\n          </div>\r\n          <div class=\"classroom-cards-space\">\r\n          <app-dashboard-benefits></app-dashboard-benefits>\r\n          </div>\r\n        </ng-container>\r\n        <ng-container *ngSwitchDefault>\r\n          <!-- Display content for unknown status -->\r\n          Unknown status.\r\n        </ng-container>\r\n      </ng-container>\r\n    </div>\r\n    <app-text-call-to-action-button></app-text-call-to-action-button>\r\n  </ng-container>\r\n  </ng-container>\r\n</div>\r\n}@placeholder {\r\n  <span>loading...</span>\r\n}\r\n<!-- ends dashboard-wrapper -->\r\n\r\n<!-- \r\n\r\n<div *ngIf=\"role=='Student'\" id=\"view-goals\" class=\"modal no-visibility p-0\">\r\n  <div class=\"popup-title p-20\">\r\n    <div>Yous Goals</div>\r\n    <img (click)=\"toggleViewGoals()\" src=\"/assets/icons/close.png\" class=\"close-img close-img-abs hvr-glow\">\r\n  </div>\r\n  <app-view-goals class=\"p-20\"></app-view-goals>\r\n</div> -->\r\n\r\n<div id=\"set-goals\" class=\"modal no-visibility\">\r\n  <div class=\"popup-title\">\r\n    <div>Set new goal</div>\r\n    <img (click)=\"toggleSetGoals()\" src=\"/assets/icons/close.png\" class=\"close-img close-img-abs hvr-glow\">\r\n  </div>\r\n  <!-- <app-create-goal></app-create-goal> -->\r\n</div>\r\n\r\n<div id=\"edit-goals\" class=\"modal no-visibility\">\r\n  <div class=\"popup-title\">\r\n    <div>Edit goals</div>\r\n    <img (click)=\"toggleEditGoals()\" src=\"/assets/icons/close.png\" class=\"close-img close-img-abs hvr-glow\">\r\n  </div>\r\n  <!-- <div *ngFor=\"let goal of studentGoals\"> -->\r\n  <!-- <app-create-goal [goals]=\"studentGoals\"></app-create-goal> -->\r\n  <!-- </div> -->\r\n</div>\r\n\r\n\r\n<ng-template #newOrForwaredTrialStatusTemplate let-context>\r\n  <div class=\"grid flex justify-content-between\">\r\n    <div class=\"col-12\">\r\n      <p-carousel *ngIf=\"banners.length > 0\" [value]=\"banners\" [autoplayInterval]=\"5000\"\r\n        [responsiveOptions]=\"responsiveOptions\" indicatorsContentClass=\"bulletStyle\"\r\n        [verticalViewPortHeight]=\"'130px'\" [numVisible]=\"1\" [page]=\"1\"\r\n        [numScroll]=\"1\" [showNavigators]=\"false\" [showIndicators]=\"banners.length > 1 ? true : false\"\r\n        [circular]=\"banners.length > 1 ? true : false\">\r\n        <ng-template let-banner pTemplate=\"item\">\r\n          <app-intro-gradient [classRoom]=\"banner.classRoom ?? {}\" [teacher]=\"context && context.trialClassRooms.length > 0 ? context.trialClassRooms[0].teacher: null\" [bannerMode]=\"banner.bannerMode\"></app-intro-gradient>\r\n        </ng-template>\r\n      </p-carousel>\r\n    </div>\r\n  </div>\r\n  <div class=\"grid flex justify-content-between\">\r\n    <div class=\"col-12 md:col-6\">\r\n      <app-goal-steps-box [isSmall]=\"true\"></app-goal-steps-box>\r\n    </div>\r\n    <div class=\"col-12 md:col-6\">\r\n      <app-block-viewer headerBlockClass=\"bg-cover \" header=\"Notifications\" headerBackgroundImage=\"/assets/images/dashboard/gradient-sm-1.png\"\r\n      blockClass=\"block-gradient border-radius-bottom-10 h-full\" [containerHeight]=\"119\" containerClass=\"\"\r\n      actionButtonText=\"See All\" actionButtonRoute=\"/notifications\">\r\n      <p-scrollPanel [style]=\"{width: '100%', height: '118px'}\" styleClass=\"custombar1\">\r\n          <app-dashboard-notifications [slimStyle]=\"true\"></app-dashboard-notifications>\r\n        </p-scrollPanel>\r\n      </app-block-viewer>\r\n    </div>\r\n  </div>\r\n  <div class=\"grid flex justify-content-between\">\r\n    <div class=\"col-12 md:col-6 h-full\">\r\n      <app-calendar-agenda [lessons]=\"context.lessons\" (dayClicked)=\"onAgendaDayClicked($event)\"></app-calendar-agenda>\r\n    </div>\r\n    <div class=\"col-12 md:col-6\">\r\n      <ng-container *ngTemplateOutlet=\"lessonsBoxTemplate; context: {$implicit: templateContext.$implicit}\">\r\n      </ng-container>\r\n    </div>\r\n  </div>\r\n  <ng-container *ngTemplateOutlet=\"classroomCardsTemplate; context: {$implicit: templateContext.$implicit}\">\r\n  </ng-container>\r\n\r\n  <div class=\"mt-5\">\r\n    <app-dashboard-next-steps [activeStep]=\"getNextStepNumber(context.trialClassRooms)\"></app-dashboard-next-steps>\r\n  </div>\r\n  <div class=\"classroom-cards-space\">\r\n  <app-dashboard-benefits></app-dashboard-benefits>\r\n</div>\r\n  <div class=\"classroom-cards-space\">\r\n  <app-goal-steps-box></app-goal-steps-box>\r\n</div>\r\n</ng-template>\r\n\r\n<ng-template #ignoredOrLostTrialStatusTemplate>\r\n  <div class=\"grid flex justify-content-between\">\r\n    <div class=\"col-12\">\r\n      <p-carousel *ngIf=\"banners.length > 0\" [value]=\"banners\" [autoplayInterval]=\"5000\"\r\n        indicatorsContentClass=\"bulletStyle\" [verticalViewPortHeight]=\"'130px'\" [orientation]=\"'vertical'\"\r\n        [numVisible]=\"1\" [page]=\"1\" [numScroll]=\"1\" [showNavigators]=\"false\"\r\n        [showIndicators]=\"banners.length > 1 ? true : false\" [circular]=\"banners.length > 1 ? true : false\">\r\n        <ng-template let-banner pTemplate=\"item\">\r\n          <app-intro-gradient [bannerMode]=\"banner.bannerMode\"></app-intro-gradient>\r\n        </ng-template>\r\n      </p-carousel>\r\n    </div>\r\n  </div>\r\n  <div class=\"grid flex justify-content-between\">\r\n    <div class=\"col-12 md:col-6\">\r\n      <app-calendar-preview-week></app-calendar-preview-week>\r\n    </div>\r\n    <div class=\"col-12 md:col-6\">\r\n      <app-block-viewer header=\"Notifications\" headerBackgroundImage=\"/assets/images/dashboard/gradient-sm-1.png\"\r\n        blockClass=\"block-gradient border-radius-bottom-10\" containerHeight=\"118\" containerClass=\"\"\r\n        actionButtonText=\"See All\" actionButtonRoute=\"/notifications\">\r\n        <p-scrollPanel [style]=\"{width: '100%', height: '118px'}\" styleClass=\"custombar1\">\r\n          <app-dashboard-notifications [slimStyle]=\"true\"></app-dashboard-notifications>\r\n        </p-scrollPanel>\r\n      </app-block-viewer>\r\n    </div>\r\n  </div>\r\n  <app-dashboard-contact-us-form></app-dashboard-contact-us-form>\r\n  <app-dashboard-benefits></app-dashboard-benefits>\r\n</ng-template>\r\n\r\n<ng-template #paidOrActiveTemplate let-context>\r\n  <div class=\"grid flex justify-content-between\">\r\n    <div class=\"col-12\">\r\n      <p-carousel *ngIf=\"banners.length > 0\" [value]=\"banners\" [autoplayInterval]=\"5000\"\r\n        indicatorsContentClass=\"bulletStyle\" [verticalViewPortHeight]=\"'130px'\"\r\n         [showNavigators]=\"false\"\r\n        [showIndicators]=\"banners.length > 1 ? true : false\" [circular]=\"banners.length > 1 ? true : false\">\r\n        <ng-template let-banner pTemplate=\"item\">\r\n          <app-intro-gradient [classRoom]=\"banner.classRoom\" [bannerMode]=\"banner.bannerMode\"></app-intro-gradient>\r\n        </ng-template>\r\n      </p-carousel>\r\n    </div>\r\n  </div>\r\n  <div class=\"grid flex justify-content-between\">\r\n    <div class=\"col-12 md:col-6\">\r\n      \r\n    <app-block-viewer headerBlockClass=\"bg-cover \" header=\"Coming up\" headerBackgroundImage=\"/assets/images/dashboard/gradient-sm-1.png\"\r\n    blockClass=\"block-gradient relative h-full border-radius-bottom-10\" [containerHeight]=\"129\" containerClass=\"\">\r\n    <app-next-lesson [lessons]=\"context.lessons\" [todayLessons]=\"context.todaysLessons\"></app-next-lesson>\r\n    </app-block-viewer>\r\n      <!-- <app-goal-steps-box [isSmall]=\"true\"></app-goal-steps-box> -->\r\n    </div>\r\n    <div class=\"col-12 md:col-6\">\r\n      <app-block-viewer header=\"Notifications\"\r\n        headerBackgroundImage=\"/assets/images/dashboard/gradient-sm-1.png\"\r\n        blockClass=\"block-gradient border-radius-bottom-10 h-full\" containerClass=\"\" actionButtonText=\"See All\"\r\n        actionButtonRoute=\"/notifications\">\r\n        <p-scrollPanel [style]=\"{width: '100%', height: '118px'}\" styleClass=\"custombar1\">\r\n          <app-dashboard-notifications [slimStyle]=\"true\"></app-dashboard-notifications>\r\n        </p-scrollPanel>\r\n      </app-block-viewer>\r\n    </div>\r\n  </div>\r\n  <div class=\"grid flex justify-content-between\">\r\n    <div class=\"col-12 md:col-6 h-full\">\r\n      <app-calendar-agenda [lessons]=\"context.lessons\" (dayClicked)=\"onAgendaDayClicked($event)\"></app-calendar-agenda>\r\n    </div>\r\n    <div class=\"col-12 md:col-6 h-full\">\r\n      <ng-container *ngTemplateOutlet=\"lessonsBoxTemplate; context: {$implicit: templateContext.$implicit}\">\r\n      </ng-container>\r\n    </div>\r\n  </div>\r\n  <ng-container *ngTemplateOutlet=\"classroomCardsTemplate; context: {$implicit: templateContext.$implicit}\">\r\n  </ng-container>\r\n  <!-- active student and paid doesnt need to see below -->\r\n  <ng-container *ngIf=\"context.trialClassRooms.length === 0\">\r\n  <div class=\"classroom-cards-space\">\r\n  <app-dashboard-next-steps [activeStep]=\"context.trialClassRooms.length > 0 ? 3 : 2\"\r\n    [stepOneInfo]=\"{\r\n  icon: '/assets/images/dashboard/next-steps-1-icon.svg',\r\n  title: 'Chose a package and begin learning!',\r\n  description: 'Once you you have purchased a package, we will contact you for more information regarding your level, your availability, your goals, and any specific difficulties or requirements. That way, we can match you with the most suitable of our teachers.'}\"\r\n    [stepThreeInfo]=\"{\r\n    icon: '/assets/images/dashboard/next-steps-1-icon.svg',\r\n    title: 'Arrange your first trial lesson',\r\n    description: 'Once you you have purchased a package, we will contact you for more information regarding your level, your availability, your goals, and any specific difficulties or requirements. That way, we can match you with the most suitable of our teachers.'}\"\r\n    ></app-dashboard-next-steps>\r\n    </div>\r\n  <app-dashboard-benefits></app-dashboard-benefits>\r\n  <div class=\"classroom-cards-space\">\r\n  <app-goal-steps-box></app-goal-steps-box>\r\n  </div>\r\n</ng-container>\r\n</ng-template>\r\n\r\n<ng-template #classroomCardsTemplate let-context>\r\n  <ng-container *ngIf=\"context && context.trialClassRooms && context.trialClassRooms.length > 0\">\r\n    <div id=\"classroomBox\" class=\"relative  h-full\">\r\n      <!--  [ngStyle]=\"{ 'minHeight': context.leftMenuHeight + 'px' }\" -->\r\n      <div class=\"relative\">\r\n        <ng-container *ngIf=\"context && context.trialClassRooms\">\r\n          <div class=\" border-round-xl  overflow-hidden mt-4\">\r\n          <div class=\"class-filters border-round-xl\">\r\n            <div class=\"image-container\">\r\n              <img src=\"/assets/images/dashboard/class-filters-cut.svg\" class=\"md:block\" alt=\"class-filters img\" />\r\n              <div class=\"text-overlay\">\r\n                <h2 class=\"font-3xl\">Classrooms</h2>\r\n              </div>\r\n            </div>\r\n            <div class=\"filters-content bg-white border-round-xl\">\r\n\r\n              <app-classroom-status-filters class=\"block relative mt-4 md:mt-0 mx-2 md:mx-0\" [isTrial]=\"false\" [switches]=\"switches\"\r\n              (switchToggled)=\"toggleSwitch($event)\"></app-classroom-status-filters>\r\n            </div>\r\n          </div>\r\n        </div>\r\n          <ng-container *ngFor=\"let trialClassroom of context.filteredTrialClassRooms; let index = i;\">\r\n            <div [ngClass]=\"context.filteredTrialClassRooms.length === 1 ? 'mt-3' : index > 0? 'classroom-cards-space' : 'mt-3'\">\r\n              <app-classroom-card [classRoom]=\"trialClassroom\" color=\"expired\"></app-classroom-card>\r\n            </div>\r\n          </ng-container>\r\n\r\n          <ng-container *ngIf=\"context.filteredTrialClassRooms.length === 0\">\r\n            <div class=\"m-1  h-full\">\r\n              <ng-container>\r\n                <div class=\"flex h-full align-items-center justify-content-center flex-column\">\r\n                  <img src=\"/assets/icons/empty-classroom.png\" height=\"280\">\r\n                  <strong>No classrooms found.</strong>\r\n                </div>\r\n              </ng-container>\r\n              <ng-container *ngFor=\"let trialClassroom of filteredClassrooms\">\r\n                <app-classroom-card class=\"sm:pb-2 block\" [classRoom]=\"trialClassroom\" color=\"expired\"></app-classroom-card>\r\n              </ng-container>\r\n            </div>\r\n          </ng-container>\r\n  \r\n        </ng-container>\r\n      </div>\r\n    </div>\r\n</ng-container>\r\n</ng-template>\r\n\r\n<ng-template #lessonsBoxTemplate let-context>\r\n  <ng-container *ngIf=\"context && context.trialClassRooms\">\r\n    <app-block-viewer header=\" \" headerBackgroundImage=\"/assets/images/dashboard/gradient-sm-1.png\"\r\n    blockClass=\"relative card\" [containerHeight]=\"386\" containerClass=\"\">\r\n    <app-lesson-schedule-box [scrollHeight]=\"340\" [classRooms]=\"context.trialClassRooms\" [lessons]=\"context.lessons\" \r\n    [disableAddNewLessonButton]=\"(context.classRooms.length === 0) && (context.trialClassRooms.length === 0)\"\r\n      ></app-lesson-schedule-box>\r\n  </app-block-viewer>\r\n  </ng-container>\r\n</ng-template>\r\n\r\n<ng-template #lessonsBoxTemplateTeacher let-context>\r\n    <app-block-viewer header=\" \" headerBackgroundImage=\"/assets/images/dashboard/gradient-sm-1.png\"\r\n    blockClass=\"relative card\" containerClass=\"\" [containerHeight]=\"500\">\r\n    <!--  [containerHeight]=\"347\" -->\r\n    <ng-container *ngIf=\"context.classRooms\">\r\n    <app-lesson-schedule-box [classRooms]=\"context.classRooms\" \r\n    [lessons]=\"context.lessons\" \r\n    [disableAddNewLessonButton]=\"(context.classRooms.length === 0)\"\r\n      ></app-lesson-schedule-box>\r\n    </ng-container>\r\n  </app-block-viewer>\r\n</ng-template>\r\n\r\n<ng-template #teacherTemplate let-context>\r\n<div class=\"grid flex justify-content-between\">\r\n  <div class=\"col-12\">\r\n    <p-carousel *ngIf=\"banners.length > 0\" [value]=\"banners\" [autoplayInterval]=\"5000\"\r\n      [responsiveOptions]=\"responsiveOptions\" indicatorsContentClass=\"bulletStyle\"\r\n      [verticalViewPortHeight]=\"'130px'\" [numVisible]=\"1\" [page]=\"1\"\r\n      [numScroll]=\"1\" [showNavigators]=\"false\" [showIndicators]=\"banners.length > 1 ? true : false\"\r\n      [circular]=\"banners.length > 1 ? true : false\">\r\n      <ng-template let-banner pTemplate=\"item\">\r\n        <app-intro-gradient [teacher]=\"context && context.trialClassRooms.length > 0 ? context.trialClassRooms[0].teacher: null\" [bannerMode]=\"banner.bannerMode\"></app-intro-gradient>\r\n      </ng-template>\r\n    </p-carousel>\r\n  </div>\r\n</div>\r\n<div class=\"grid flex justify-content-between\">\r\n  <div class=\"col-12 md:col-6\">\r\n    <app-block-viewer headerBlockClass=\"bg-cover \" header=\"Coming up\" headerBackgroundImage=\"/assets/images/dashboard/gradient-sm-1.png\"\r\n    blockClass=\"block-gradient relative h-full border-radius-bottom-10\" [containerHeight]=\"129\" containerClass=\"\">\r\n    <app-next-lesson [lessons]=\"context.lessons\"></app-next-lesson>\r\n    </app-block-viewer>\r\n  </div>\r\n  <div class=\"col-12 md:col-6\">\r\n    <app-block-viewer headerBlockClass=\"bg-cover \" header=\"Notifications\" headerBackgroundImage=\"/assets/images/dashboard/gradient-sm-1.png\"\r\n      blockClass=\"block-gradient border-radius-bottom-10\" [containerHeight]=\"119\" containerClass=\"\"\r\n      actionButtonText=\"See All\" actionButtonRoute=\"/notifications\">\r\n      <p-scrollPanel [style]=\"{width: '100%', height: '119px'}\" styleClass=\"custombar1\">\r\n        <app-dashboard-notifications [slimStyle]=\"true\"></app-dashboard-notifications>\r\n      </p-scrollPanel>\r\n    </app-block-viewer>\r\n  </div>\r\n</div>\r\n<div class=\"grid flex justify-content-between h-full flex-wrap\">\r\n  <div class=\"col-12 md:col-6 h-full\">\r\n    <app-calendar-agenda [lessons]=\"context.lessons\" (dayClicked)=\"onAgendaDayClicked($event)\"></app-calendar-agenda>\r\n  </div>\r\n  <div class=\"col-12 md:col-6 h-full\">\r\n    <ng-container *ngTemplateOutlet=\"lessonsBoxTemplateTeacher; context: {$implicit: templateContext.$implicit}\">\r\n    </ng-container>\r\n  </div>\r\n</div>\r\n<div class=\"classroom-cards-space\">\r\n  <app-dashboard-teacher-classrooms [classrooms]=\"context.classRooms\"></app-dashboard-teacher-classrooms>\r\n</div>\r\n<app-text-call-to-action-button></app-text-call-to-action-button>\r\n</ng-template>"], "mappings": "AAAA,SAASA,WAAW,QAAQ,sBAAsB;AAElD,SAASC,kBAAkB,EAAEC,gBAAgB,EAAoBC,UAAU,QAAQ,gBAAgB;AAGnG,SAASC,QAAQ,EAAEC,EAAE,QAAQ,MAAM;AACnC,SAASC,UAAU,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,QAAQ,gBAAgB;AAEjF,SAA0CC,MAAM,QAA4B,qCAAqC;AAGjH,SAAiBC,YAAY,QAAQ,kCAAkC;AACvE,SAASC,kBAAkB,QAAQ,mCAAmC;AAEtE,SAAeC,QAAQ,QAAQ,gCAAgC;AAW/D,SAASC,OAAO,QAAQ,SAAS;AACjC,OAAO,KAAKC,IAAI,MAAM,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICzB9CC,EAAA,CAAAC,uBAAA,GAAiC;IAE7BD,EADF,CAAAE,cAAA,cAAkB,cACI;IAClBF,EAAA,CAAAG,SAAA,iBAAyB;IAE7BH,EADE,CAAAI,YAAA,EAAM,EACF;;;;;;IAMFJ,EAAA,CAAAK,kBAAA,GAA2F;;;;;IAD7FL,EAAA,CAAAC,uBAAA,GAAgD;IAC9CD,EAAA,CAAAM,UAAA,IAAAC,gFAAA,2BAA4E;;;;;;IAA7DP,EAAA,CAAAQ,SAAA,EAAmC;IAAAR,EAAnC,CAAAS,UAAA,qBAAAC,kBAAA,CAAmC,4BAAAC,MAAA,CAAAC,eAAA,CAAwB;;;;;IAOtEZ,EAAA,CAAAK,kBAAA,GAA4G;;;;;IAF9GL,EAAA,CAAAC,uBAAA,GAAsD;IAEpDD,EAAA,CAAAM,UAAA,IAAAO,qGAAA,2BAA6F;;;;;;IAA9Eb,EAAA,CAAAQ,SAAA,EAAoD;IAAAR,EAApD,CAAAS,UAAA,qBAAAK,mCAAA,CAAoD,4BAAAH,MAAA,CAAAC,eAAA,CAAwB;;;;;IAI3FZ,EAAA,CAAAK,kBAAA,GAAkF;;;;;IAFpFL,EAAA,CAAAC,uBAAA,GAAoD;IAElDD,EAAA,CAAAM,UAAA,IAAAS,qGAAA,2BAAmE;;;;;;IAApDf,EAAA,CAAAQ,SAAA,EAAkD;IAAlDR,EAAA,CAAAS,UAAA,qBAAAO,mCAAA,CAAkD;;;;;IAK/DhB,EAAA,CAAAK,kBAAA,GACa;;;;;IAJjBL,EAAA,CAAAC,uBAAA,GAAwD;IAGpDD,EAAA,CAAAM,UAAA,IAAAW,qGAAA,2BAAiF;;;;;;IAAlEjB,EAAA,CAAAQ,SAAA,EAAwC;IAAAR,EAAxC,CAAAS,UAAA,qBAAAS,uBAAA,CAAwC,4BAAAP,MAAA,CAAAC,eAAA,CAAwB;;;;;IAKjFZ,EAAA,CAAAG,SAAA,6BAAqH;;;;;IAFvHH,EAAA,CAAAC,uBAAA,GAAmD;IAEjDD,EAAA,CAAAM,UAAA,IAAAa,+GAAA,qCAA4F;;;;;;IAAnEnB,EAAA,CAAAQ,SAAA,EAAiE;IAAjER,EAAA,CAAAS,UAAA,SAAAW,SAAA,CAAAC,WAAA,OAAAV,MAAA,CAAAW,aAAA,CAAAC,MAAA,CAAAF,WAAA,GAAiE;;;;;IAWlFrB,EAAA,CAAAG,SAAA,6BAA0E;;;;IAAtDH,EAAA,CAAAS,UAAA,eAAAe,SAAA,CAAAC,UAAA,CAAgC;;;;;IALxDzB,EAAA,CAAAE,cAAA,qBAGsG;IACpGF,EAAA,CAAAM,UAAA,IAAAoB,iHAAA,0BAAyC;IAG3C1B,EAAA,CAAAI,YAAA,EAAa;;;;IAJ0CJ,EAHhB,CAAAS,UAAA,UAAAE,MAAA,CAAAgB,OAAA,CAAiB,0BAA0B,mCACT,2BAA2B,iBAClF,WAAW,gBAAgB,yBAAyB,mBAAAhB,MAAA,CAAAgB,OAAA,CAAAC,MAAA,oBAChB,aAAAjB,MAAA,CAAAgB,OAAA,CAAAC,MAAA,oBAA+C;;;;;;IAP3G5B,EAAA,CAAAC,uBAAA,GAAqD;IAGjDD,EADF,CAAAE,cAAA,cAA+C,cACzB;IAClBF,EAAA,CAAAM,UAAA,IAAAuB,mGAAA,0BAGsG;IAM1G7B,EADE,CAAAI,YAAA,EAAM,EACF;IAEJJ,EADF,CAAAE,cAAA,cAA+C,cAChB;IAC3BF,EAAA,CAAAG,SAAA,6BAA0D;IAC5DH,EAAA,CAAAI,YAAA,EAAM;IAMFJ,EALJ,CAAAE,cAAA,cAA6B,2BAIU,wBAC+C;IAChFF,EAAA,CAAAG,SAAA,uCAA8E;IAItFH,EAHM,CAAAI,YAAA,EAAgB,EACC,EACf,EACF;IAGFJ,EAFJ,CAAAE,cAAA,eAA+C,eACT,+BACyD;IAA1CF,EAAA,CAAA8B,UAAA,wBAAAC,kIAAAC,MAAA;MAAAhC,EAAA,CAAAiC,aAAA,CAAAC,GAAA;MAAA,MAAAvB,MAAA,GAAAX,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAczB,MAAA,CAAA0B,kBAAA,CAAAL,MAAA,CAA0B;IAAA,EAAC;IAC5FhC,EAD6F,CAAAI,YAAA,EAAsB,EAC7G;IACNJ,EAAA,CAAAE,cAAA,eAA6B;IAC3BF,EAAA,CAAAG,SAAA,mCAC8D;IAElEH,EADE,CAAAI,YAAA,EAAM,EACF;IAINJ,EAHA,CAAAG,SAAA,8BAAuD,oCACe,8BACrB,0BACR;;;;;IArCxBH,EAAA,CAAAQ,SAAA,GAAwB;IAAxBR,EAAA,CAAAS,UAAA,SAAAE,MAAA,CAAAgB,OAAA,CAAAC,MAAA,KAAwB;IAYjB5B,EAAA,CAAAQ,SAAA,GAAgB;IAAhBR,EAAA,CAAAS,UAAA,iBAAgB;IAOnBT,EAAA,CAAAQ,SAAA,GAA0C;IAA1CR,EAAA,CAAAsC,UAAA,CAAAtC,EAAA,CAAAuC,eAAA,KAAAC,GAAA,EAA0C;IAC1BxC,EAAA,CAAAQ,SAAA,EAAkB;IAAlBR,EAAA,CAAAS,UAAA,mBAAkB;IAO9BT,EAAA,CAAAQ,SAAA,GAA2B;IAA3BR,EAAA,CAAAS,UAAA,YAAAE,MAAA,CAAA8B,OAAA,CAAAC,OAAA,CAA2B;IAGvB1C,EAAA,CAAAQ,SAAA,GAAc;IACrCR,EADuB,CAAAS,UAAA,YAAAT,EAAA,CAAAuC,eAAA,KAAAI,GAAA,EAAc,mCAAmC,gBAAAhC,MAAA,CAAAiC,iBAAA,CACvC;IAIb5C,EAAA,CAAAQ,SAAA,GAAgB;IAAhBR,EAAA,CAAAS,UAAA,iBAAgB;;;;;IAM1CT,EAAA,CAAAK,kBAAA,GAAkF;;;;;IAFpFL,EAAA,CAAAC,uBAAA,GAAiD;IAE/CD,EAAA,CAAAM,UAAA,IAAAuC,qGAAA,2BAAmE;;;;;;IAApD7C,EAAA,CAAAQ,SAAA,EAAkD;IAAlDR,EAAA,CAAAS,UAAA,qBAAAO,mCAAA,CAAkD;;;;;IAIjEhB,EAAA,CAAAK,kBAAA,GAA4G;;;;;IAF9GL,EAAA,CAAAC,uBAAA,GAAgD;IAE9CD,EAAA,CAAAM,UAAA,IAAAwC,qGAAA,2BAA6F;;;;;;IAA9E9C,EAAA,CAAAQ,SAAA,EAAoD;IAAAR,EAApD,CAAAS,UAAA,qBAAAK,mCAAA,CAAoD,4BAAAH,MAAA,CAAAC,eAAA,CAAwB;;;;;;IAqB3FZ,EAAA,CAAAE,cAAA,2CACwD;IAAtDF,EAAA,CAAA8B,UAAA,2BAAAiB,oLAAAf,MAAA;MAAAhC,EAAA,CAAAiC,aAAA,CAAAe,IAAA;MAAA,MAAArC,MAAA,GAAAX,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAiBzB,MAAA,CAAAsC,2BAAA,CAAAjB,MAAA,CAAmC;IAAA,EAAC;IAAChC,EAAA,CAAAI,YAAA,EAAmC;;;;;IApB7FJ,EAAA,CAAAC,uBAAA,GAAiD;IAG7CD,EADF,CAAAE,cAAA,cAA+C,cAChB;IAC3BF,EAAA,CAAAG,SAAA,gCAAuD;IACzDH,EAAA,CAAAI,YAAA,EAAM;IAOFJ,EANJ,CAAAE,cAAA,cAA6B,2BAKD,wBAC0D;IAChFF,EAAA,CAAAG,SAAA,sCAA8E;IAItFH,EAHM,CAAAI,YAAA,EAAgB,EACC,EACf,EACF;IAENJ,EAAA,CAAAM,UAAA,IAAA4C,yHAAA,+CACwD;IACtDlD,EAAA,CAAAE,cAAA,cAAmC;IACrCF,EAAA,CAAAG,SAAA,gCAAqD;IACrDH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,eAAmC;IACnCF,EAAA,CAAAG,SAAA,8BAAiD;IACjDH,EAAA,CAAAI,YAAA,EAAM;;;;;IAfAJ,EAAA,CAAAQ,SAAA,GAAuB;IAAvBR,EAAA,CAAAS,UAAA,wBAAuB;IACRT,EAAA,CAAAQ,SAAA,EAA0C;IAA1CR,EAAA,CAAAsC,UAAA,CAAAtC,EAAA,CAAAuC,eAAA,IAAAC,GAAA,EAA0C;IAC1BxC,EAAA,CAAAQ,SAAA,EAAkB;IAAlBR,EAAA,CAAAS,UAAA,mBAAkB;IAMpBT,EAAA,CAAAQ,SAAA,EAA6B;IAA7BR,EAAA,CAAAS,UAAA,UAAAE,MAAA,CAAAwC,sBAAA,CAA6B;;;;;IASlEnD,EAAA,CAAAC,uBAAA,GAA+B;IAE7BD,EAAA,CAAAoD,MAAA,wBACF;;;;;;IAtGJpD,EAAA,CAAAE,cAAA,UAAgD;IAC9CF,EAAA,CAAAC,uBAAA,OAAkC;IAkGhCD,EAjGA,CAAAM,UAAA,IAAA+C,sFAAA,2BAAsD,IAAAC,sFAAA,2BAIF,IAAAC,sFAAA,2BAII,IAAAC,sFAAA,2BAML,IAAAC,sFAAA,6BAIE,IAAAC,sFAAA,2BA2CJ,IAAAC,sFAAA,2BAID,IAAAC,sFAAA,4BAIC,KAAAC,uFAAA,2BA4BlB;;IAKnC7D,EAAA,CAAAI,YAAA,EAAM;;;;;IAvGUJ,EAAA,CAAAQ,SAAA,EAAmB;IAAnBR,EAAA,CAAAS,UAAA,aAAAW,SAAA,CAAmB;IAChBpB,EAAA,CAAAQ,SAAA,EAAqC;IAArCR,EAAA,CAAAS,UAAA,iBAAAE,MAAA,CAAAW,aAAA,CAAAwC,SAAA,CAAqC;IAIrC9D,EAAA,CAAAQ,SAAA,EAAmC;IAAnCR,EAAA,CAAAS,UAAA,iBAAAE,MAAA,CAAAW,aAAA,CAAAyC,OAAA,CAAmC;IAInC/D,EAAA,CAAAQ,SAAA,EAAuC;IAAvCR,EAAA,CAAAS,UAAA,iBAAAE,MAAA,CAAAW,aAAA,CAAA0C,WAAA,CAAuC;IAMvChE,EAAA,CAAAQ,SAAA,EAAkC;IAAlCR,EAAA,CAAAS,UAAA,iBAAAE,MAAA,CAAAW,aAAA,CAAAC,MAAA,CAAkC;IAIlCvB,EAAA,CAAAQ,SAAA,EAAoC;IAApCR,EAAA,CAAAS,UAAA,iBAAAE,MAAA,CAAAW,aAAA,CAAA2C,QAAA,CAAoC;IA2CpCjE,EAAA,CAAAQ,SAAA,EAAgC;IAAhCR,EAAA,CAAAS,UAAA,iBAAAE,MAAA,CAAAW,aAAA,CAAA4C,IAAA,CAAgC;IAIhClE,EAAA,CAAAQ,SAAA,EAA+B;IAA/BR,EAAA,CAAAS,UAAA,iBAAAE,MAAA,CAAAW,aAAA,CAAA6C,GAAA,CAA+B;IAI/BnE,EAAA,CAAAQ,SAAA,EAAgC;IAAhCR,EAAA,CAAAS,UAAA,iBAAAE,MAAA,CAAAW,aAAA,CAAA8C,IAAA,CAAgC;;;;;IAxEnDpE,EAAA,CAAAC,uBAAA,GAAgD;IAChDD,EAAA,CAAAM,UAAA,IAAA+D,uEAAA,mBAAgD;;IAyGhDrE,EAAA,CAAAG,SAAA,qCAAiE;;;;;IAzG3DH,EAAA,CAAAQ,SAAA,EAA+B;IAA/BR,EAAA,CAAAS,UAAA,SAAAT,EAAA,CAAAsE,WAAA,OAAA3D,MAAA,CAAA4D,cAAA,EAA+B;;;;;IALvCvE,EAAA,CAAAC,uBAAA,GAAkC;IAIhCD,EAHA,CAAAM,UAAA,IAAAkE,iEAAA,2BAAgD,IAAAC,iEAAA,2BAGA;;;;;IAHjCzE,EAAA,CAAAQ,SAAA,EAA+B;IAA/BR,EAAA,CAAAS,UAAA,SAAAE,MAAA,CAAA+D,IAAA,IAAA/D,MAAA,CAAAgE,SAAA,CAAAC,OAAA,CAA+B;IAG/B5E,EAAA,CAAAQ,SAAA,EAA+B;IAA/BR,EAAA,CAAAS,UAAA,SAAAE,MAAA,CAAA+D,IAAA,IAAA/D,MAAA,CAAAgE,SAAA,CAAAE,OAAA,CAA+B;;;;;IAblD7E,EAAA,CAAAM,UAAA,IAAAwE,kDAAA,2BAAiC;IAQjC9E,EAAA,CAAAE,cAAA,cAAiE;IAC/DF,EAAA,CAAAM,UAAA,IAAAyE,kDAAA,2BAAkC;IAiHpC/E,EAAA,CAAAI,YAAA,EAAM;;;;IA1HSJ,EAAA,CAAAS,UAAA,SAAAE,MAAA,CAAAqE,UAAA,CAAgB;IASdhF,EAAA,CAAAQ,SAAA,GAAiB;IAAjBR,EAAA,CAAAS,UAAA,UAAAE,MAAA,CAAAqE,UAAA,CAAiB;;;;;IAmHhChF,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAoD,MAAA,iBAAU;IAAApD,EAAA,CAAAI,YAAA,EAAO;;;;;IA0CfJ,EAAA,CAAAG,SAAA,6BAAoN;;;;;;IAAtDH,EAA1I,CAAAS,UAAA,eAAAwE,QAAA,GAAAC,UAAA,CAAAC,SAAA,cAAAF,QAAA,KAAAG,SAAA,GAAAH,QAAA,GAAAjF,EAAA,CAAAuC,eAAA,IAAA8C,GAAA,EAAoC,YAAAC,WAAA,IAAAA,WAAA,CAAAC,eAAA,CAAA3D,MAAA,OAAA0D,WAAA,CAAAC,eAAA,IAAAC,OAAA,QAAqG,eAAAN,UAAA,CAAAzD,UAAA,CAAiC;;;;;IANlMzB,EAAA,CAAAE,cAAA,qBAIiD;IAC/CF,EAAA,CAAAM,UAAA,IAAAmF,qEAAA,0BAAyC;IAG3CzF,EAAA,CAAAI,YAAA,EAAa;;;;IAJXJ,EAJqC,CAAAS,UAAA,UAAAE,MAAA,CAAAgB,OAAA,CAAiB,0BAA0B,sBAAAhB,MAAA,CAAA+E,iBAAA,CACzC,mCACL,iBAAiB,WAAW,gBAC/C,yBAAyB,mBAAA/E,MAAA,CAAAgB,OAAA,CAAAC,MAAA,oBAAqD,aAAAjB,MAAA,CAAAgB,OAAA,CAAAC,MAAA,oBAC/C;;;;;IA0BhD5B,EAAA,CAAAK,kBAAA,GACe;;;;;IAGnBL,EAAA,CAAAK,kBAAA,GACe;;;;;;IApCbL,EADF,CAAAE,cAAA,cAA+C,cACzB;IAClBF,EAAA,CAAAM,UAAA,IAAAqF,uDAAA,0BAIiD;IAMrD3F,EADE,CAAAI,YAAA,EAAM,EACF;IAEJJ,EADF,CAAAE,cAAA,cAA+C,cAChB;IAC3BF,EAAA,CAAAG,SAAA,6BAA0D;IAC5DH,EAAA,CAAAI,YAAA,EAAM;IAKJJ,EAJF,CAAAE,cAAA,cAA6B,2BAGmC,wBACoB;IAC9EF,EAAA,CAAAG,SAAA,sCAA8E;IAItFH,EAHM,CAAAI,YAAA,EAAgB,EACC,EACf,EACF;IAGFJ,EAFJ,CAAAE,cAAA,eAA+C,eACT,+BACyD;IAA1CF,EAAA,CAAA8B,UAAA,wBAAA8D,sFAAA5D,MAAA;MAAAhC,EAAA,CAAAiC,aAAA,CAAA4D,IAAA;MAAA,MAAAlF,MAAA,GAAAX,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAczB,MAAA,CAAA0B,kBAAA,CAAAL,MAAA,CAA0B;IAAA,EAAC;IAC5FhC,EAD6F,CAAAI,YAAA,EAAsB,EAC7G;IACNJ,EAAA,CAAAE,cAAA,eAA6B;IAC3BF,EAAA,CAAAM,UAAA,KAAAwF,0DAAA,2BAAsG;IAG1G9F,EADE,CAAAI,YAAA,EAAM,EACF;IACNJ,EAAA,CAAAM,UAAA,KAAAyF,0DAAA,2BAA0G;IAG1G/F,EAAA,CAAAE,cAAA,eAAkB;IAChBF,EAAA,CAAAG,SAAA,oCAA+G;IACjHH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAE,cAAA,eAAmC;IACnCF,EAAA,CAAAG,SAAA,8BAAiD;IACnDH,EAAA,CAAAI,YAAA,EAAM;IACJJ,EAAA,CAAAE,cAAA,eAAmC;IACnCF,EAAA,CAAAG,SAAA,0BAAyC;IAC3CH,EAAA,CAAAI,YAAA,EAAM;;;;;;;IA7CaJ,EAAA,CAAAQ,SAAA,GAAwB;IAAxBR,EAAA,CAAAS,UAAA,SAAAE,MAAA,CAAAgB,OAAA,CAAAC,MAAA,KAAwB;IAajB5B,EAAA,CAAAQ,SAAA,GAAgB;IAAhBR,EAAA,CAAAS,UAAA,iBAAgB;IAIuBT,EAAA,CAAAQ,SAAA,GAAuB;IAAvBR,EAAA,CAAAS,UAAA,wBAAuB;IAEnET,EAAA,CAAAQ,SAAA,EAA0C;IAA1CR,EAAA,CAAAsC,UAAA,CAAAtC,EAAA,CAAAuC,eAAA,KAAAC,GAAA,EAA0C;IACxBxC,EAAA,CAAAQ,SAAA,EAAkB;IAAlBR,EAAA,CAAAS,UAAA,mBAAkB;IAO9BT,EAAA,CAAAQ,SAAA,GAA2B;IAA3BR,EAAA,CAAAS,UAAA,YAAA6E,WAAA,CAAA5C,OAAA,CAA2B;IAGjC1C,EAAA,CAAAQ,SAAA,GAAsC;IAAAR,EAAtC,CAAAS,UAAA,qBAAAuF,sBAAA,CAAsC,4BAAAhG,EAAA,CAAAiG,eAAA,KAAAC,GAAA,EAAAvF,MAAA,CAAAC,eAAA,CAAAuF,SAAA,EAA+C;IAIzFnG,EAAA,CAAAQ,SAAA,EAA0C;IAAAR,EAA1C,CAAAS,UAAA,qBAAA2F,0BAAA,CAA0C,4BAAApG,EAAA,CAAAiG,eAAA,KAAAC,GAAA,EAAAvF,MAAA,CAAAC,eAAA,CAAAuF,SAAA,EAA+C;IAI5EnG,EAAA,CAAAQ,SAAA,GAAyD;IAAzDR,EAAA,CAAAS,UAAA,eAAAE,MAAA,CAAA0F,iBAAA,CAAAf,WAAA,CAAAC,eAAA,EAAyD;;;;;IAkB7EvF,EAAA,CAAAG,SAAA,6BAA0E;;;;IAAtDH,EAAA,CAAAS,UAAA,eAAA6F,UAAA,CAAA7E,UAAA,CAAgC;;;;;IALxDzB,EAAA,CAAAE,cAAA,qBAGsG;IACpGF,EAAA,CAAAM,UAAA,IAAAiG,qEAAA,0BAAyC;IAG3CvG,EAAA,CAAAI,YAAA,EAAa;;;;IAJ0CJ,EAHhB,CAAAS,UAAA,UAAAE,MAAA,CAAAgB,OAAA,CAAiB,0BAA0B,mCACT,2BAA2B,iBAClF,WAAW,gBAAgB,yBAAyB,mBAAAhB,MAAA,CAAAgB,OAAA,CAAAC,MAAA,oBAChB,aAAAjB,MAAA,CAAAgB,OAAA,CAAAC,MAAA,oBAA+C;;;;;IAJvG5B,EADF,CAAAE,cAAA,cAA+C,cACzB;IAClBF,EAAA,CAAAM,UAAA,IAAAkG,uDAAA,0BAGsG;IAM1GxG,EADE,CAAAI,YAAA,EAAM,EACF;IAEJJ,EADF,CAAAE,cAAA,cAA+C,cAChB;IAC3BF,EAAA,CAAAG,SAAA,gCAAuD;IACzDH,EAAA,CAAAI,YAAA,EAAM;IAKFJ,EAJJ,CAAAE,cAAA,cAA6B,2BAGqC,wBACoB;IAChFF,EAAA,CAAAG,SAAA,sCAA8E;IAItFH,EAHM,CAAAI,YAAA,EAAgB,EACC,EACf,EACF;IAENJ,EADA,CAAAG,SAAA,qCAA+D,8BACd;;;;IAzBhCH,EAAA,CAAAQ,SAAA,GAAwB;IAAxBR,EAAA,CAAAS,UAAA,SAAAE,MAAA,CAAAgB,OAAA,CAAAC,MAAA,KAAwB;IAkBpB5B,EAAA,CAAAQ,SAAA,GAA0C;IAA1CR,EAAA,CAAAsC,UAAA,CAAAtC,EAAA,CAAAuC,eAAA,IAAAC,GAAA,EAA0C;IAC1BxC,EAAA,CAAAQ,SAAA,EAAkB;IAAlBR,EAAA,CAAAS,UAAA,mBAAkB;;;;;IAiB/CT,EAAA,CAAAG,SAAA,6BAAyG;;;;IAAtDH,EAA/B,CAAAS,UAAA,cAAAgG,UAAA,CAAAtB,SAAA,CAA8B,eAAAsB,UAAA,CAAAhF,UAAA,CAAiC;;;;;IALvFzB,EAAA,CAAAE,cAAA,qBAGsG;IACpGF,EAAA,CAAAM,UAAA,IAAAoG,qEAAA,0BAAyC;IAG3C1G,EAAA,CAAAI,YAAA,EAAa;;;;IAJ0CJ,EAHhB,CAAAS,UAAA,UAAAE,MAAA,CAAAgB,OAAA,CAAiB,0BAA0B,mCACT,yBAC9C,mBAAAhB,MAAA,CAAAgB,OAAA,CAAAC,MAAA,oBAC2B,aAAAjB,MAAA,CAAAgB,OAAA,CAAAC,MAAA,oBAA+C;;;;;IAgCrG5B,EAAA,CAAAK,kBAAA,GACe;;;;;IAGnBL,EAAA,CAAAK,kBAAA,GACe;;;;;IAEfL,EAAA,CAAAC,uBAAA,GAA2D;IAC3DD,EAAA,CAAAE,cAAA,cAAmC;IACnCF,EAAA,CAAAG,SAAA,mCAS8B;IAC5BH,EAAA,CAAAI,YAAA,EAAM;IACRJ,EAAA,CAAAG,SAAA,6BAAiD;IACjDH,EAAA,CAAAE,cAAA,cAAmC;IACnCF,EAAA,CAAAG,SAAA,yBAAyC;IACzCH,EAAA,CAAAI,YAAA,EAAM;;;;;IAdoBJ,EAAA,CAAAQ,SAAA,GAAyD;IAKjFR,EALwB,CAAAS,UAAA,eAAAkG,WAAA,CAAApB,eAAA,CAAA3D,MAAA,aAAyD,gBAAA5B,EAAA,CAAAuC,eAAA,IAAAqE,GAAA,EAIoL,kBAAA5G,EAAA,CAAAuC,eAAA,IAAAsE,GAAA,EAIE;;;;;;IArDvQ7G,EADF,CAAAE,cAAA,cAA+C,cACzB;IAClBF,EAAA,CAAAM,UAAA,IAAAwG,uDAAA,yBAGsG;IAM1G9G,EADE,CAAAI,YAAA,EAAM,EACF;IAIJJ,EAHF,CAAAE,cAAA,cAA+C,cAChB,2BAGiF;IAC9GF,EAAA,CAAAG,SAAA,0BAAsG;IAGtGH,EAFA,CAAAI,YAAA,EAAmB,EAEb;IAMFJ,EALJ,CAAAE,cAAA,cAA6B,2BAIU,wBAC+C;IAChFF,EAAA,CAAAG,SAAA,uCAA8E;IAItFH,EAHM,CAAAI,YAAA,EAAgB,EACC,EACf,EACF;IAGFJ,EAFJ,CAAAE,cAAA,eAA+C,eACT,+BACyD;IAA1CF,EAAA,CAAA8B,UAAA,wBAAAiF,sFAAA/E,MAAA;MAAAhC,EAAA,CAAAiC,aAAA,CAAA+E,IAAA;MAAA,MAAArG,MAAA,GAAAX,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAczB,MAAA,CAAA0B,kBAAA,CAAAL,MAAA,CAA0B;IAAA,EAAC;IAC5FhC,EAD6F,CAAAI,YAAA,EAAsB,EAC7G;IACNJ,EAAA,CAAAE,cAAA,eAAoC;IAClCF,EAAA,CAAAM,UAAA,KAAA2G,0DAAA,2BAAsG;IAG1GjH,EADE,CAAAI,YAAA,EAAM,EACF;IAINJ,EAHA,CAAAM,UAAA,KAAA4G,0DAAA,2BAA0G,KAAAC,0DAAA,2BAG/C;;;;;;;IA1C1CnH,EAAA,CAAAQ,SAAA,GAAwB;IAAxBR,EAAA,CAAAS,UAAA,SAAAE,MAAA,CAAAgB,OAAA,CAAAC,MAAA,KAAwB;IAc6B5B,EAAA,CAAAQ,SAAA,GAAuB;IAAvBR,EAAA,CAAAS,UAAA,wBAAuB;IAC1ET,EAAA,CAAAQ,SAAA,EAA2B;IAACR,EAA5B,CAAAS,UAAA,YAAAkG,WAAA,CAAAjE,OAAA,CAA2B,iBAAAiE,WAAA,CAAAS,aAAA,CAAuC;IAShEpH,EAAA,CAAAQ,SAAA,GAA0C;IAA1CR,EAAA,CAAAsC,UAAA,CAAAtC,EAAA,CAAAuC,eAAA,KAAAC,GAAA,EAA0C;IAC1BxC,EAAA,CAAAQ,SAAA,EAAkB;IAAlBR,EAAA,CAAAS,UAAA,mBAAkB;IAO9BT,EAAA,CAAAQ,SAAA,GAA2B;IAA3BR,EAAA,CAAAS,UAAA,YAAAkG,WAAA,CAAAjE,OAAA,CAA2B;IAGjC1C,EAAA,CAAAQ,SAAA,GAAsC;IAAAR,EAAtC,CAAAS,UAAA,qBAAAuF,sBAAA,CAAsC,4BAAAhG,EAAA,CAAAiG,eAAA,KAAAC,GAAA,EAAAvF,MAAA,CAAAC,eAAA,CAAAuF,SAAA,EAA+C;IAIzFnG,EAAA,CAAAQ,SAAA,EAA0C;IAAAR,EAA1C,CAAAS,UAAA,qBAAA2F,0BAAA,CAA0C,4BAAApG,EAAA,CAAAiG,eAAA,KAAAC,GAAA,EAAAvF,MAAA,CAAAC,eAAA,CAAAuF,SAAA,EAA+C;IAGzFnG,EAAA,CAAAQ,SAAA,EAA0C;IAA1CR,EAAA,CAAAS,UAAA,SAAAkG,WAAA,CAAApB,eAAA,CAAA3D,MAAA,OAA0C;;;;;IAyCjD5B,EAAA,CAAAC,uBAAA,GAA6F;IAC3FD,EAAA,CAAAE,cAAA,cAAqH;IACnHF,EAAA,CAAAG,SAAA,6BAAsF;IACxFH,EAAA,CAAAI,YAAA,EAAM;;;;;;;IAFDJ,EAAA,CAAAQ,SAAA,EAA+G;IAA/GR,EAAA,CAAAS,UAAA,YAAA4G,WAAA,CAAAC,uBAAA,CAAA1F,MAAA,kBAAA2F,SAAA,wCAA+G;IAC9FvH,EAAA,CAAAQ,SAAA,EAA4B;IAA5BR,EAAA,CAAAS,UAAA,cAAA+G,kBAAA,CAA4B;;;;;IAYhDxH,EAAA,CAAAC,uBAAA,GAAgE;IAC9DD,EAAA,CAAAG,SAAA,6BAA4G;;;;;IAAlEH,EAAA,CAAAQ,SAAA,EAA4B;IAA5BR,EAAA,CAAAS,UAAA,cAAAgH,kBAAA,CAA4B;;;;;IAT5EzH,EAAA,CAAAC,uBAAA,GAAmE;IACjED,EAAA,CAAAE,cAAA,cAAyB;IACvBF,EAAA,CAAAC,uBAAA,GAAc;IACZD,EAAA,CAAAE,cAAA,cAA+E;IAC7EF,EAAA,CAAAG,SAAA,cAA0D;IAC1DH,EAAA,CAAAE,cAAA,aAAQ;IAAAF,EAAA,CAAAoD,MAAA,2BAAoB;IAC9BpD,EAD8B,CAAAI,YAAA,EAAS,EACjC;;IAERJ,EAAA,CAAAM,UAAA,IAAAoH,uGAAA,2BAAgE;IAGlE1H,EAAA,CAAAI,YAAA,EAAM;;;;;IAHqCJ,EAAA,CAAAQ,SAAA,GAAqB;IAArBR,EAAA,CAAAS,UAAA,YAAAE,MAAA,CAAAgH,kBAAA,CAAqB;;;;;;IA9BpE3H,EAAA,CAAAC,uBAAA,GAAyD;IAGrDD,EAFF,CAAAE,cAAA,cAAoD,cACT,cACZ;IAC3BF,EAAA,CAAAG,SAAA,cAAqG;IAEnGH,EADF,CAAAE,cAAA,cAA0B,aACH;IAAAF,EAAA,CAAAoD,MAAA,iBAAU;IAEnCpD,EAFmC,CAAAI,YAAA,EAAK,EAChC,EACF;IAGJJ,EAFF,CAAAE,cAAA,cAAsD,uCAGb;IAAvCF,EAAA,CAAA8B,UAAA,2BAAA8F,+HAAA5F,MAAA;MAAAhC,EAAA,CAAAiC,aAAA,CAAA4F,IAAA;MAAA,MAAAlH,MAAA,GAAAX,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAiBzB,MAAA,CAAAmH,YAAA,CAAA9F,MAAA,CAAoB;IAAA,EAAC;IAG5ChC,EAH6C,CAAAI,YAAA,EAA+B,EAClE,EACF,EACF;IAOJJ,EANA,CAAAM,UAAA,KAAAyH,wFAAA,2BAA6F,KAAAC,wFAAA,2BAM1B;;;;;;IAXgBhI,EAAA,CAAAQ,SAAA,GAAiB;IAACR,EAAlB,CAAAS,UAAA,kBAAiB,aAAAE,MAAA,CAAAsH,QAAA,CAAsB;IAKjFjI,EAAA,CAAAQ,SAAA,EAAoC;IAApCR,EAAA,CAAAS,UAAA,YAAA4G,WAAA,CAAAC,uBAAA,CAAoC;IAM9DtH,EAAA,CAAAQ,SAAA,EAAkD;IAAlDR,EAAA,CAAAS,UAAA,SAAA4G,WAAA,CAAAC,uBAAA,CAAA1F,MAAA,OAAkD;;;;;IA1BzE5B,EAAA,CAAAC,uBAAA,GAA+F;IAG3FD,EAFF,CAAAE,cAAA,cAAgD,cAExB;IACpBF,EAAA,CAAAM,UAAA,IAAA4H,wEAAA,4BAAyD;IAsC7DlI,EADE,CAAAI,YAAA,EAAM,EACF;;;;;IAtCaJ,EAAA,CAAAQ,SAAA,GAAwC;IAAxCR,EAAA,CAAAS,UAAA,SAAA4G,WAAA,IAAAA,WAAA,CAAA9B,eAAA,CAAwC;;;;;IAJ7DvF,EAAA,CAAAM,UAAA,IAAA6H,yDAAA,2BAA+F;;;;IAAhFnI,EAAA,CAAAS,UAAA,SAAA4G,WAAA,IAAAA,WAAA,CAAA9B,eAAA,IAAA8B,WAAA,CAAA9B,eAAA,CAAA3D,MAAA,KAA8E;;;;;IA+C7F5B,EAAA,CAAAC,uBAAA,GAAyD;IACvDD,EAAA,CAAAE,cAAA,2BACqE;IACrEF,EAAA,CAAAG,SAAA,kCAE6B;IAC/BH,EAAA,CAAAI,YAAA,EAAmB;;;;;IAJUJ,EAAA,CAAAQ,SAAA,EAAuB;IAAvBR,EAAA,CAAAS,UAAA,wBAAuB;IACzBT,EAAA,CAAAQ,SAAA,EAAoB;IAC7CR,EADyB,CAAAS,UAAA,qBAAoB,eAAA2H,WAAA,CAAA7C,eAAA,CAAuC,YAAA6C,WAAA,CAAA1F,OAAA,CAA4B,8BAAA0F,WAAA,CAAAC,UAAA,CAAAzG,MAAA,UAAAwG,WAAA,CAAA7C,eAAA,CAAA3D,MAAA,OACP;;;;;IAJ3G5B,EAAA,CAAAM,UAAA,IAAAgI,yDAAA,2BAAyD;;;;IAA1CtI,EAAA,CAAAS,UAAA,SAAA2H,WAAA,IAAAA,WAAA,CAAA7C,eAAA,CAAwC;;;;;IAcrDvF,EAAA,CAAAC,uBAAA,GAAyC;IACzCD,EAAA,CAAAG,SAAA,kCAG6B;;;;;IAHJH,EAAA,CAAAQ,SAAA,EAAiC;IAE1DR,EAFyB,CAAAS,UAAA,eAAA8H,WAAA,CAAAF,UAAA,CAAiC,YAAAE,WAAA,CAAA7F,OAAA,CAC/B,8BAAA6F,WAAA,CAAAF,UAAA,CAAAzG,MAAA,OACoC;;;;;IAN/D5B,EAAA,CAAAE,cAAA,2BACqE;IAErEF,EAAA,CAAAM,UAAA,IAAAkI,yDAAA,2BAAyC;IAM3CxI,EAAA,CAAAI,YAAA,EAAmB;;;;IAR4BJ,EAAA,CAAAS,UAAA,wBAAuB;IAErDT,EAAA,CAAAQ,SAAA,EAAwB;IAAxBR,EAAA,CAAAS,UAAA,SAAA8H,WAAA,CAAAF,UAAA,CAAwB;;;;;IAkBnCrI,EAAA,CAAAG,SAAA,6BAA+K;;;;;IAAtDH,EAArG,CAAAS,UAAA,YAAAgI,WAAA,IAAAA,WAAA,CAAAlD,eAAA,CAAA3D,MAAA,OAAA6G,WAAA,CAAAlD,eAAA,IAAAC,OAAA,QAAoG,eAAAkD,UAAA,CAAAjH,UAAA,CAAiC;;;;;IAN7JzB,EAAA,CAAAE,cAAA,qBAIiD;IAC/CF,EAAA,CAAAM,UAAA,IAAAqI,qEAAA,0BAAyC;IAG3C3I,EAAA,CAAAI,YAAA,EAAa;;;;IAJXJ,EAJqC,CAAAS,UAAA,UAAAE,MAAA,CAAAgB,OAAA,CAAiB,0BAA0B,sBAAAhB,MAAA,CAAA+E,iBAAA,CACzC,mCACL,iBAAiB,WAAW,gBAC/C,yBAAyB,mBAAA/E,MAAA,CAAAgB,OAAA,CAAAC,MAAA,oBAAqD,aAAAjB,MAAA,CAAAgB,OAAA,CAAAC,MAAA,oBAC/C;;;;;IA6BhD5B,EAAA,CAAAK,kBAAA,GACe;;;;;;IAnCjBL,EADF,CAAAE,cAAA,cAA+C,cACzB;IAClBF,EAAA,CAAAM,UAAA,IAAAsI,uDAAA,0BAIiD;IAMrD5I,EADE,CAAAI,YAAA,EAAM,EACF;IAGFJ,EAFJ,CAAAE,cAAA,cAA+C,cAChB,2BAEmF;IAC9GF,EAAA,CAAAG,SAAA,0BAA+D;IAEjEH,EADE,CAAAI,YAAA,EAAmB,EACf;IAKFJ,EAJJ,CAAAE,cAAA,cAA6B,2BAGqC,wBACoB;IAChFF,EAAA,CAAAG,SAAA,uCAA8E;IAItFH,EAHM,CAAAI,YAAA,EAAgB,EACC,EACf,EACF;IAGFJ,EAFJ,CAAAE,cAAA,eAAgE,eAC1B,+BACyD;IAA1CF,EAAA,CAAA8B,UAAA,wBAAA+G,sFAAA7G,MAAA;MAAAhC,EAAA,CAAAiC,aAAA,CAAA6G,IAAA;MAAA,MAAAnI,MAAA,GAAAX,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAczB,MAAA,CAAA0B,kBAAA,CAAAL,MAAA,CAA0B;IAAA,EAAC;IAC5FhC,EAD6F,CAAAI,YAAA,EAAsB,EAC7G;IACNJ,EAAA,CAAAE,cAAA,eAAoC;IAClCF,EAAA,CAAAM,UAAA,KAAAyI,0DAAA,2BAA6G;IAGjH/I,EADE,CAAAI,YAAA,EAAM,EACF;IACNJ,EAAA,CAAAE,cAAA,eAAmC;IACjCF,EAAA,CAAAG,SAAA,4CAAuG;IACzGH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAG,SAAA,sCAAiE;;;;;;IAxChDH,EAAA,CAAAQ,SAAA,GAAwB;IAAxBR,EAAA,CAAAS,UAAA,SAAAE,MAAA,CAAAgB,OAAA,CAAAC,MAAA,KAAwB;IAc+B5B,EAAA,CAAAQ,SAAA,GAAuB;IAAvBR,EAAA,CAAAS,UAAA,wBAAuB;IAC1ET,EAAA,CAAAQ,SAAA,EAA2B;IAA3BR,EAAA,CAAAS,UAAA,YAAAgI,WAAA,CAAA/F,OAAA,CAA2B;IAKU1C,EAAA,CAAAQ,SAAA,GAAuB;IAAvBR,EAAA,CAAAS,UAAA,wBAAuB;IAE5DT,EAAA,CAAAQ,SAAA,EAA0C;IAA1CR,EAAA,CAAAsC,UAAA,CAAAtC,EAAA,CAAAuC,eAAA,KAAAyG,IAAA,EAA0C;IAC1BhJ,EAAA,CAAAQ,SAAA,EAAkB;IAAlBR,EAAA,CAAAS,UAAA,mBAAkB;IAO9BT,EAAA,CAAAQ,SAAA,GAA2B;IAA3BR,EAAA,CAAAS,UAAA,YAAAgI,WAAA,CAAA/F,OAAA,CAA2B;IAGjC1C,EAAA,CAAAQ,SAAA,GAA6C;IAAAR,EAA7C,CAAAS,UAAA,qBAAAwI,6BAAA,CAA6C,4BAAAjJ,EAAA,CAAAiG,eAAA,KAAAC,GAAA,EAAAvF,MAAA,CAAAC,eAAA,CAAAuF,SAAA,EAA+C;IAK3EnG,EAAA,CAAAQ,SAAA,GAAiC;IAAjCR,EAAA,CAAAS,UAAA,eAAAgI,WAAA,CAAAJ,UAAA,CAAiC;;;AD1XrE,OAAM,MAAOa,kBAAkB;EAsD7BC,YACSC,cAA8B,EAC9BC,WAAwB,EACvBC,aAA4B,EAC5BC,WAAwB,EACxBC,eAAgC,EAChCC,YAA0B,EAC1BC,WAAwB,EACxBC,mBAAwC,EACxCC,gBAAkC,EAClCC,mBAAwC,EACxCC,eAAgC,EAChCC,WAAwB,EACxBC,aAA4B,EAC5BC,YAA0B,EAC1BC,GAAsB,EACtBC,gBAAkC,EAClCC,sBAA8C,EAC9CC,QAAmB;IAjBpB,KAAAjB,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,mBAAmB,GAAnBA,mBAAmB;IACnB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,QAAQ,GAARA,QAAQ;IAvE+C,KAAAC,gCAAgC,GAAe,EAAgB;IAC/D,KAAAC,gCAAgC,GAAe,EAAgB;IAC3E,KAAAC,oBAAoB,GAAe,EAAgB;IACjD,KAAAC,qBAAqB,GAAe,EAAgB;IACnG,KAAAC,IAAI,GAAG,IAAI5K,OAAO,EAAE;IACrB,KAAA6K,YAAY,GAAS,EAAU;IAC/B,KAAAC,UAAU,GAAa,EAAE;IACzB,KAAAxD,aAAa,GAAa,EAAE;IAC5B,KAAAyD,QAAQ,GAAW,CAAC;IACpB,KAAAC,QAAQ,GAAW,CAAC;IACpB,KAAAC,IAAI,GAAW,IAAI,CAACxB,WAAW,CAACyB,UAAU;IAC1C,KAAAC,MAAM,GAAY,EAAE;IACpB,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,WAAW,GAAqB,EAAsB;IACtD,KAAAC,aAAa,GAAqB,EAAsB;IACxD,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,kBAAkB,GAAY,KAAK;IACnC,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAA7G,IAAI,GAAW,EAAE;IACjB,KAAA8G,YAAY,GAAkB,EAAE;IAChC,KAAAC,iBAAiB,GAAY,IAAI;IACjC,KAAAC,oBAAoB,GAAY,IAAI;IAC3C,KAAAC,aAAa,GAAY,KAAK;IAC9B,KAAAC,aAAa,GAAmB,EAAE;IAClC,KAAAC,SAAS,GAAe,EAAE;IAC1B,KAAAC,UAAU,GAAQ,EAAE;IACpB,KAAAC,cAAc,GAAgB,EAAE;IAChC,KAAAC,YAAY,GAAY,IAAI;IAC5B,KAAAC,iBAAiB,GAAY,IAAI;IAEjC,KAAAC,KAAK,GAAG,IAAIC,IAAI,EAAE;IAClB,KAAAhJ,sBAAsB,GAAG,KAAK;IAC9B,KAAA6B,UAAU,GAAG,IAAI;IACjB,KAAArD,OAAO,GAAU,EAAE;IAEnB,KAAAyK,sBAAsB,GAAG,GAAG;IAC5B,KAAAC,YAAY,GAAG,CAAC;IAChB,KAAA3G,iBAAiB,GAAG,EAAE;IACtB,KAAA/F,YAAY,GAAGA,YAAY;IAC3B,KAAA+C,OAAO,GAAG3C,IAAI,CAACuM,YAAY;IAC3B,KAAA/H,cAAc,GAAG,IAAI,CAACsF,mBAAmB,CAACtF,cAAc;IACxD,KAAAjD,aAAa,GAAG1B,kBAAkB;IAClC,KAAAgD,iBAAiB,GAAG,IAAIuJ,IAAI,EAAE;IAC9B,KAAAI,SAAS,GAAW,OAAO;IAC3B,KAAA5H,SAAS,GAAG9E,QAAQ;IACb,KAAA2M,IAAI,GAAS,EAAU;IAC9B,KAAAvE,QAAQ,GAA4B;MAClCwE,GAAG,EAAE,KAAK;MACVC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,KAAK;MAChBC,OAAO,EAAE;KACV;IACD,KAAAC,gBAAgB,GAAG,IAAI;IAsBhB,KAAAjM,eAAe,GAAG;MACvBuF,SAAS,EAAE;QACT2G,iBAAiB,EAAE,KAAK;QACxBvH,eAAe,EAAE,EAAiB;QAClC+B,uBAAuB,EAAE,EAAiB;QAC1Ce,UAAU,EAAE,EAAE;QACd3F,OAAO,EAAE,EAAE;QACX0E,aAAa,EAAE,EAAE;QACjB2F,cAAc,EAAE,CAAC;QACjBC,UAAU,EAAE;;KAEf;EAbG;EAcJ;EACAC,QAAQA,CAACC,IAAS;IAChB,MAAMC,OAAO,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,CAAC;IACxE,MAAMC,IAAI,GAAGF,IAAI,CAACE,IAAI,CAACC,QAAQ,EAAE;IACjC,MAAMC,KAAK,GAAG,CAACJ,IAAI,CAACI,KAAK,GAAG,CAAC,EAAED,QAAQ,EAAE,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGN,IAAI,CAACM,GAAG,CAACH,QAAQ,EAAE,CAACE,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAChD,MAAME,UAAU,GAAG,GAAGL,IAAI,IAAIE,KAAK,IAAIE,GAAG,EAAE;IAC5C,MAAME,cAAc,GAAGP,OAAO,CAACQ,QAAQ,CAACF,UAAU,CAAC;IAEnD,OAAOC,cAAc;EACvB;EAGAE,cAAcA,CAAC7N,IAAS;IACtB8N,OAAO,CAACC,GAAG,CACT,sCAAsC,EACtC,IAAI,CAAC3D,gBAAgB,CAAC4D,eAAe,EAAE,CACxC;IAED,IAAI,CAAC5D,gBAAgB,CAAC6D,GAAG,CAACJ,cAAc,CACtC,yBAAyB,EACzB,IAAI,CAACzD,gBAAgB,CAAC4D,eAAe,EAAE,EACvC,mBAAmB,CACpB;EACH;EAEAE,QAAQA,CAAA;IACN,IAAI,CAACzB,IAAI,GAAG,IAAI,CAACnD,WAAW,CAAC6E,eAAe,EAAE;IAC9C,IAAI,CAACxJ,IAAI,GAAG,IAAI,CAAC2E,WAAW,CAAC8E,WAAW,EAAE;IAC1C,IAAI,CAAChL,sBAAsB,GAAG,IAAI,CAAC0G,mBAAmB,CAACuE,gBAAgB;IAEvE,IAAI,CAAC3C,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACQ,iBAAiB,GAAG,IAAI;IAE7B,IAAI,CAACvB,IAAI,CAAC2D,GAAG,CAAC,IAAI,CAACjF,cAAc,CAACkF,UAAU,CAACC,IAAI,EAAE,CAACC,SAAS,CAACC,GAAG,IAAG;MAClE,IAAIA,GAAG,EAAE;QACP,IAAIA,GAAG,CAACC,MAAM,EAAE;UACd,IAAI,CAACtC,sBAAsB,GAAG,GAAG;UACjC;QACF,CAAC,MAAM;UACL,IAAI,CAACA,sBAAsB,GAAG,GAAG;QACnC;MACF;IACF,CAAC,CAAC,CAAC;IACH,IAAI,CAACuC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,0BAA0B,CAAC,IAAI,CAAClK,IAAgB,CAAC;IACtD,IAAI,CAACmK,kBAAkB,EAAE;IAEzB,IAAI,CAAC1D,WAAW,GAAG,IAAInM,gBAAgB,CAAC;MACtC8P,UAAU,EAAE,IAAI/P,kBAAkB,CAAC,IAAI,EAAE;QACvCgQ,UAAU,EAAE,CAAC9P,UAAU,CAAC+P,QAAQ,EAAE/P,UAAU,CAACgQ,SAAS,CAAC,EAAE,CAAC;OAC3D;KACF,CAAC;IACF;IACA;IACA;IACA,IAAI,CAACvE,IAAI,CAACwE,IAAI,GAAG,IAAI,CAAC3F,WAAW,CAAC4F,aAAa,EAAE,CAACZ,IAAI,CAAC9O,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC+O,SAAS,CAACC,GAAG,IAAG;MAC9E,IAAIW,KAAK,GAAQ,EAAE;MACnB,KAAK,IAAIC,CAAC,IAAIZ,GAAG,EAAE;QACjB,IAAI,CAACW,KAAK,CAACE,IAAI,CAAEC,EAAO,IAAKA,EAAE,CAACC,WAAW,IAAIH,CAAC,CAACG,WAAW,IAAID,EAAE,CAACE,YAAY,IAAIJ,CAAC,CAACI,YAAY,CAAC,EAAE;UAClGL,KAAK,CAACM,IAAI,CAACL,CAAC,CAAC;QACf;MACF;MACA,IAAI,CAACvD,UAAU,GAAGsD,KAAK;IACzB,CAAC,CAAC;EACJ;EAEAO,eAAeA,CAAA;IAEb;IACA;IACA;IACA;EAAA;EAIKC,UAAUA,CAACC,GAAW;IAC3BhC,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACnC,IAAIgC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC3CF,IAAI,CAACG,YAAY,CAAC,IAAI,EAAE,eAAe,CAAC;IACxCH,IAAI,CAACI,GAAG,GAAGL,GAAG;IACdC,IAAI,CAACK,IAAI,GAAG,iBAAiB;IAC7BL,IAAI,CAACM,KAAK,GAAG,IAAI;IACjBN,IAAI,CAACO,OAAO,GAAG,OAAO;IACtBN,QAAQ,CAACO,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAACC,WAAW,CAACT,IAAI,CAAC;EAC5D;EAEAU,kBAAkBA,CAAA;IAChB,IAAI,IAAI,CAAC9L,IAAI,IAAI7E,QAAQ,CAACgF,OAAO,EAAE;MACjC,IAAI,CAAC6F,IAAI,CAAC2D,GAAG,CAAC,IAAI,CAAC3E,WAAW,CAAC+G,cAAc,CAACjC,SAAS,CAACC,GAAG,IAAG;QAC5D,IAAI,CAAC/D,IAAI,CAACwE,IAAI,GAAG,IAAI,CAACxF,WAAW,CAACgH,QAAQ,EAAE,CAAClC,SAAS,CAACC,GAAG,IAAG;UAC3D,IAAI,CAACzC,YAAY,GAAG,KAAK;UACzB,IAAI,CAACR,YAAY,GAAGiD,GAAG;QACzB,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC;IACL;EACF;EAEAkC,WAAWA,CAAA;IACT,IAAI,CAACjG,IAAI,CAACkG,WAAW,EAAE;EACzB;EAGAC,QAAQA,CAAA;IACN,IAAI,CAAClC,oBAAoB,EAAE;EAC7B;EAEAmC,oBAAoBA,CAACX,IAAY;IAC/B,IAAIA,IAAI,KAAKxQ,YAAY,CAACoR,QAAQ,EAAE;MAClC,OAAO,SAAS;IAClB,CAAC,MAAM,IAAIZ,IAAI,KAAKxQ,YAAY,CAACqR,SAAS,EAAE;MAC1C,OAAO,SAAS;IAClB,CAAC,MAAM;MACL,OAAO,SAAS;IAClB;EACF;EAEAC,iBAAiBA,CAACC,IAAa;IAC7B,IAAIA,IAAI,EAAE;MACR,OAAO,OAAO;IAChB,CAAC,MAAM;MACL,OAAO,SAAS;IAClB;EACF;EAEAC,cAAcA,CAACC,MAAoB;IACjC,IAAIA,MAAM,KAAKzR,YAAY,CAACoR,QAAQ,EAAE;MACpC,OAAO,SAAS;IAClB,CAAC,MAAM,IAAIK,MAAM,KAAKzR,YAAY,CAAC0R,cAAc,EAAE;MACjD,OAAO,SAAS;IAClB,CAAC,MACI,IAAID,MAAM,KAAKzR,YAAY,CAACqR,SAAS,EAAE;MAC1C,OAAO,SAAS;IAClB,CAAC,MAAM;MACL,OAAO,SAAS;IAClB;EACF;EAEAM,aAAaA,CAACC,cAAmB,EAAEC,kBAAuB;IACxD,IAAI,CAACpI,cAAc,CAACkI,aAAa,CAACC,cAAc,EAAEC,kBAAkB,EAAE,IAAI,CAAC;EAC7E;EAGAC,cAAcA,CAACzP,MAAW;IACxB,IAAI,CAAC8I,QAAQ,GAAG4G,MAAM,CAACC,WAAW;IAClC,IAAI,IAAI,CAAC7G,QAAQ,GAAG,GAAG,EACrB,IAAI,CAAC1B,cAAc,CAACwI,aAAa,CAAC,IAAI,CAAC9G,QAAQ,EAAE,IAAI,CAACD,QAAQ,EAAE,oBAAoB,CAAC;IACvF,IAAI,IAAI,CAACC,QAAQ,GAAG,IAAI,EACtB,IAAI,CAAC1B,cAAc,CAACwI,aAAa,CAAC,IAAI,CAAC9G,QAAQ,EAAE,IAAI,CAACD,QAAQ,EAAE,eAAe,CAAC;IAClF,IAAI,IAAI,CAACC,QAAQ,GAAG,IAAI,EACtB,IAAI,CAAC1B,cAAc,CAACwI,aAAa,CAAC,IAAI,CAAC9G,QAAQ,EAAE,IAAI,CAACD,QAAQ,EAAE,mBAAmB,CAAC;IACtF,IAAI,IAAI,CAACC,QAAQ,GAAG,IAAI,EACtB,IAAI,CAAC1B,cAAc,CAACwI,aAAa,CAAC,IAAI,CAAC9G,QAAQ,EAAE,IAAI,CAACD,QAAQ,EAAE,gBAAgB,CAAC;IACnF,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACC,QAAQ;EAC/B;EAEA+G,eAAeA,CAAA;IACb,IAAI,CAAC3G,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAChC,IAAI,CAAC9B,cAAc,CAAC0I,aAAa,CAAC,IAAI,CAAC5G,SAAS,EAAE,YAAY,CAAC;EACjE;EAEA6G,iBAAiBA,CAAA;IACf,IAAI,CAAC7G,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAChC,IAAI,CAAC9B,cAAc,CAAC0I,aAAa,CAAC,IAAI,CAAC5G,SAAS,EAAE,cAAc,CAAC;EACnE;EAEA8G,aAAaA,CAAA;IACX,IAAI,CAAC9G,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAChC,IAAI,CAAC9B,cAAc,CAAC0I,aAAa,CAAC,IAAI,CAAC5G,SAAS,EAAE,wBAAwB,CAAC;EAC7E;EAEA+G,eAAeA,CAAA;IACb,IAAI,CAAC/G,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAChC,IAAI,CAAC9B,cAAc,CAAC0I,aAAa,CAAC,IAAI,CAAC5G,SAAS,EAAE,YAAY,CAAC;EACjE;EAEAgH,cAAcA,CAAA;IACZ,IAAI,CAAChH,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAChC,IAAI,CAAC9B,cAAc,CAAC0I,aAAa,CAAC,IAAI,CAAC5G,SAAS,EAAE,WAAW,CAAC;EAChE;EAEAiH,eAAeA,CAAA;IACb,IAAI,CAACjH,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;IAChC,IAAI,CAAC9B,cAAc,CAAC0I,aAAa,CAAC,IAAI,CAAC5G,SAAS,EAAE,YAAY,CAAC;EACjE;EAEAkH,gBAAgBA,CAAA;IACd,IAAI,CAAC/G,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAAC,IAAI,CAACH,WAAW,CAACkH,KAAK,EAAE;MAC3B,IAAI,CAACC,uBAAuB,EAAE;MAC9B;IACF;IACA,IAAI,CAACP,iBAAiB,EAAE;EAC1B;EAEAO,uBAAuBA,CAAA;IACrBC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACrH,WAAW,CAACsH,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACnD,IAAI,CAACvH,aAAa,GAAG,IAAI,CAACD,WAAW,CAACyH,GAAG,CAACD,GAAG,CAAE,CAACE,MAAO;MACvD,IAAI,IAAI,CAACzH,aAAa,IAAI,IAAI,EAAE;QAC9BmH,MAAM,CAACC,IAAI,CAAC,IAAI,CAACpH,aAAa,CAAC,CAACsH,OAAO,CAACI,QAAQ,IAAG;UACjD,IAAIA,QAAQ,IAAI,UAAU,EAAE;YAC1B,IAAI,CAACzH,YAAY,GAAG,IAAI;UAC1B;UACA,IAAIyH,QAAQ,IAAI,WAAW,EAAE;YAC3B,IAAI,CAACxH,kBAAkB,GAAG,IAAI;UAChC;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EAEAyH,iBAAiBA,CAACC,OAAY;IAC5B,EAAE,CAACN,OAAO,CAACO,IAAI,CAAClD,QAAQ,CAACmD,sBAAsB,CAAC,gBAAgB,CAAC,EAAG3D,EAAO,IAAI;MAC7E,IAAIA,EAAE,CAAC4D,EAAE,KAAKH,OAAO,CAACG,EAAE,EACtB5D,EAAE,CAAC6D,KAAK,CAACC,OAAO,GAAG,MAAM;IAC7B,CAAC,CAAC;IACF,IAAIL,OAAO,CAACI,KAAK,CAACC,OAAO,KAAK,MAAM,IAAIL,OAAO,CAACI,KAAK,CAACC,OAAO,KAAK,EAAE,EAAE;MACpEL,OAAO,CAACI,KAAK,CAACC,OAAO,GAAG,MAAM;IAChC,CAAC,MAAM;MACLL,OAAO,CAACI,KAAK,CAACC,OAAO,GAAG,MAAM;IAChC;EACF;EAEAC,cAAcA,CAACC,KAAU;IACvB,IAAI,CAAClH,YAAY,GAAGkH,KAAK;EAC3B;EAEAC,iCAAiCA,CAAA;IAC/BzD,QAAQ,CAAC0D,gBAAgB,CAAC,OAAO,EAAGF,KAAU,IAAI;MAChDG,UAAU,CAAC,MAAK;QACd,IAAIC,sBAAsB,GAAG5D,QAAQ,CAAC6D,cAAc,CAAC,iBAAiB,CAAC;QACvE,IAAIC,oBAAoB,GAAGF,sBAAsB,EAAEG,QAAQ,CAACP,KAAK,CAACQ,MAAM,CAAC;QACzE,IAAI,CAACF,oBAAoB,EAAE;UACzB,EAAE,CAACnB,OAAO,CAACO,IAAI,CAAClD,QAAQ,CAACmD,sBAAsB,CAAC,gBAAgB,CAAC,EAAG3D,EAAO,IAAI;YAC7EA,EAAE,CAAC6D,KAAK,CAACC,OAAO,GAAG,MAAM;UAC3B,CAAC,CAAC;QACJ;MACF,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,CAAC;EACJ;EAEAW,SAASA,CAACzE,EAAO;IACf,IAAIA,EAAE,CAAC0E,YAAY,IAAI,CAAC,EAAE;MACxBlE,QAAQ,CAAC6D,cAAc,CAACrE,EAAE,CAAC4D,EAAE,CAAC,EAAEe,SAAS,CAACC,MAAM,CAAC,eAAe,CAAC;MACjEpE,QAAQ,CAAC6D,cAAc,CAACrE,EAAE,CAAC4D,EAAE,CAAC,EAAEe,SAAS,CAAC7F,GAAG,CAAC,gBAAgB,CAAC;MAC/DkB,EAAE,CAAC6D,KAAK,CAACgB,MAAM,GAAG,OAAO;IAC3B,CAAC,MAAM;MACLrE,QAAQ,CAAC6D,cAAc,CAACrE,EAAE,CAAC4D,EAAE,CAAC,EAAEe,SAAS,CAACC,MAAM,CAAC,gBAAgB,CAAC;MAClEpE,QAAQ,CAAC6D,cAAc,CAACrE,EAAE,CAAC4D,EAAE,CAAC,EAAEe,SAAS,CAAC7F,GAAG,CAAC,eAAe,CAAC;MAC9DkB,EAAE,CAAC6D,KAAK,CAACgB,MAAM,GAAG,GAAG;IACvB;EACF;EAEAC,sBAAsBA,CAACd,KAAU;IAC/B,IAAI,CAAChI,gBAAgB,GAAGgI,KAAK,CAACe,YAAY;EAC5C;EAEAC,GAAGA,CAACpB,EAAU;IACZ,IAAIqB,GAAG,GAAG,EAAE;IACZ,KAAK,IAAIhI,IAAI,IAAI,IAAI,CAACjB,gBAAgB,EAAE;MACtCiJ,GAAG,CAAC9E,IAAI,CAAClD,IAAI,CAAC2G,EAAE,CAAC;IACnB;IACA,IAAI,CAAC5J,WAAW,CAACkL,KAAK,CAACD,GAAG,EAAErB,EAAE,CAAC,CAAC5E,IAAI,CAAC9O,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC+O,SAAS,CAACC,GAAG,IAAG,CAC9D,CAAC,CAAC;EACJ;EAEAiG,WAAWA,CAACC,QAAc;IACxBjD,MAAM,CAACkD,IAAI,CAACD,QAAQ,CAACE,IAAI,EAAE,QAAQ,CAAC;EACtC;EAEA5R,2BAA2BA,CAAC6R,MAAe;IACzC,IAAIA,MAAM,EAAE;MACV,IAAI,CAAC7K,YAAY,CAAC8K,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;MACFxD,MAAM,CAACyD,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;MACrB,IAAI,CAACtL,mBAAmB,CAACuL,wBAAwB,CAACxV,kBAAkB,CAACuE,GAAG,CAAC;MACzE,IAAI,CAACkR,aAAa,CAACzV,kBAAkB,CAACuE,GAAG,EAAE,EAAE,CAAC;IAChD;EACF;EAEAmR,eAAeA,CAAA;IACb,IAAI,CAAC3T,OAAO,GAAG,EAAE;IACjB,MAAM4T,eAAe,GAAG,IAAI,CAAC3L,gBAAgB,CAAC4L,oBAAoB,CAAC,IAAI,CAAChJ,IAAI,CAAC2G,EAAE,CAAC;IAChF,MAAMsC,UAAU,GAAG,IAAI,CAACjM,eAAe,CAACkM,YAAY,EAAE;IACtD,MAAMC,eAAe,GAAG,IAAI,CAAC5L,WAAW,CAAC6L,iBAAiB,EAAE;IAC5D,MAAMC,OAAO,GAAG,IAAI/W,WAAW,CAAC;MAC9B,4BAA4B,EAAE;KAC/B,CAAC;IACF,MAAMgX,YAAY,GAAG,IAAI,CAACjM,mBAAmB,CAACkM,qBAAqB,CAACF,OAAO,CAAC;IAC5E,MAAMG,uBAAuB,GAAG,IAAI,CAACnM,mBAAmB,CAACoM,yBAAyB,EAAE;IACpF,IAAI,CAACjR,UAAU,GAAG,IAAI;IACtB,IAAI,CAAC0F,IAAI,CAACwE,IAAI,GAAG8G,uBAAuB,CAACzH,IAAI,CAC3C/O,SAAS,CAAE0W,eAAe,IAAI;MAC5B,IAAIA,eAAe,EAAE;QACnB,MAAMC,gBAAgB,GAAG;UACvBpK,cAAc,EAAEwJ,eAAe,CAAChH,IAAI,CAACnP,UAAU,CAACgX,KAAK,IAAIjX,EAAE,CAACiX,KAAK,CAAC,CAAC,CAAC;UACpEC,WAAW,EAAEP,YAAY,CAACvH,IAAI,CAACnP,UAAU,CAACgX,KAAK,IAAIjX,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;UAC9DmX,cAAc,EAAEX,eAAe,CAACpH,IAAI,CAACnP,UAAU,CAACgX,KAAK,IAAIjX,EAAE,CAACiX,KAAK,CAAC,CAAC,CAAC;UACpEvK,SAAS,EAAE4J,UAAU,CAAClH,IAAI,CAACnP,UAAU,CAACgX,KAAK,IAAIjX,EAAE,CAACiX,KAAK,CAAC,CAAC;SAC1D;QACD,OAAOlX,QAAQ,CAACiX,gBAAgB,CAAC;MACnC,CAAC,MAAM;QACL,OAAOhX,EAAE,CAAC+W,eAAe,CAAC;MAC5B;IACF,CAAC,CAAC,EACF1W,SAAS,CAAE+W,OAAY,IAAI;MACzB;MACA,IAAIA,OAAO,KAAK,KAAK,EAAE;QACrB,OAAOpX,EAAE,CAAC,KAAK,CAAC;MAClB;MACA,MAAMqX,YAAY,GAAGD,OAAO,CAACxK,cAAc,CAACzM,GAAG,CAAEmX,SAAc,IAAKA,SAAS,CAACtD,EAAE,CAAC;MAEjF,IAAIqD,YAAY,CAAC5U,MAAM,KAAK,CAAC,EAAE;QAC7B,OAAOzC,EAAE,CAAC;UAAE,GAAGoX,OAAO;UAAE7T,OAAO,EAAE;QAAE,CAAE,CAAC;MACxC;MAEA,MAAMgU,iBAAiB,GAAGF,YAAY,CAAClX,GAAG,CAAEqX,WAAmB,IAC7D,IAAI,CAAC/M,gBAAgB,CAACgN,mBAAmB,CAACD,WAAW,CAAC,CACvD;MAED,OAAOzX,QAAQ,CAACwX,iBAAiB,CAAC,CAACnI,IAAI,CACrCjP,GAAG,CAAEoD,OAAO,KAAM;QAAE,GAAG6T,OAAO;QAAE7T;MAAO,CAAE,CAAC,CAAC,CAC5C;IACH,CAAC,CAAC,EACFtD,UAAU,CAAEgX,KAAK,IAAI;MACnBvI,OAAO,CAACuI,KAAK,CAACA,KAAK,CAAC;MACpB,IAAI,CAACpR,UAAU,GAAG,KAAK;MACvB,OAAO7F,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,EACFE,QAAQ,CAAC,MAAK;MACZ,IAAI,CAACoM,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAACQ,iBAAiB,GAAG,KAAK;MAC9B,IAAI,CAACjH,UAAU,GAAG,KAAK;IACzB,CAAC,CAAC,CACH,CAACwJ,SAAS,CAAEC,GAAQ,IAAI;MACvB,IAAI,CAACzJ,UAAU,GAAG,KAAK;MACvB6I,OAAO,CAACC,GAAG,CAACW,GAAG,CAAC;MAEhB,IAAIA,GAAG,KAAK,KAAK,EAAE;QACjB,IAAI,CAAC5E,mBAAmB,CAACuL,wBAAwB,CAACxV,kBAAkB,CAACwE,IAAI,CAAC;MAC5E,CAAC,MAAM;QACL,MAAMmB,eAAe,GAAGkJ,GAAG,CAAC1C,cAAc;QAC1C8B,OAAO,CAACC,GAAG,CAACW,GAAG,CAAC;QAChB,IAAIoI,SAAS,GAAGjX,kBAAkB,CAACwE,IAAI;QACvC,IAAI,CAACqK,GAAG,CAAC4H,WAAW,IAAI,CAAC5H,GAAG,CAAC6H,cAAc,EAAE;UAC3CO,SAAS,GAAGtR,eAAe,CAAC3D,MAAM,GAAG,CAAC,GAAGhC,kBAAkB,CAACkE,SAAS,GAAGlE,kBAAkB,CAACwE,IAAI;QACjG,CAAC,MAAM;UACLyS,SAAS,GAAGpI,GAAG,CAAC6H,cAAc,GAAG1W,kBAAkB,CAACoE,WAAW,GAAGyK,GAAG,CAAC4H,WAAW,CAAChV,WAAW,EAAE;UAC/F,IAAIwV,SAAS,CAACxV,WAAW,EAAE,CAACsM,QAAQ,CAAC,OAAO,CAACtM,WAAW,EAAE,CAAC,EAAE;YAC3DwV,SAAS,GAAGjX,kBAAkB,CAACuE,GAAG;UACpC;QACF;QACA;QAEA,IAAI,CAACvD,eAAe,CAACuF,SAAS,CAAC2G,iBAAiB,GAAG,IAAI;QACvD,IAAI,CAAClM,eAAe,CAACuF,SAAS,CAAC6G,UAAU,GAAGyB,GAAG,CAAC6H,cAAc;QAC9D,IAAI,CAAC1V,eAAe,CAACuF,SAAS,CAACZ,eAAe,GAAGA,eAAe;QAChE,IAAI,CAAC3E,eAAe,CAACuF,SAAS,CAACmB,uBAAuB,GAAG/B,eAAe;QAExE;QACA;QACA,IAAI,IAAI,CAACuR,oBAAoB,EAAE,CAAClV,MAAM,KAAK,CAAC,EAAE;UAC5C,IAAI,CAACkG,YAAY,CAAC,KAAK,CAAC;QAC1B,CAAC,MAAM;UACL,IAAI,CAACA,YAAY,CAAC,SAAS,CAAC;QAC9B;QACA,IAAI,CAAClH,eAAe,CAACuF,SAAS,CAACzD,OAAO,GAAG+L,GAAG,CAAC/L,OAAO;QACpD;QACA;QACA;QACA;QACA;QACA;QAEA6C,eAAe,CAACmN,OAAO,CAAE+D,SAAc,IAAI;UACzC5I,OAAO,CAACC,GAAG,CAAC2I,SAAS,CAAC;UACtB,IAAI,IAAI,CAAC7M,gBAAgB,CAACmN,0BAA0B,CAACN,SAAS,CAAC,EAAE;YAE/D,IAAI,IAAI,CAAC7M,gBAAgB,CAACoN,wCAAwC,CAACP,SAAS,CAAC,EAAE;cAC7E,IAAI,CAAC9U,OAAO,CAAC+N,IAAI,CAAC;gBAAEjO,UAAU,EAAE,gBAAgB;gBAAE0D,SAAS,EAAEsR;cAAS,CAAE,CAAC;YAC3E,CAAC,MAAM;cACL,IAAI,IAAI,CAAC7M,gBAAgB,CAACqN,mCAAmC,CAACR,SAAS,EAAE9W,YAAY,CAACoR,QAAQ,CAAC,EAAE;gBAC/F,IAAI,CAACpP,OAAO,CAAC+N,IAAI,CAAC;kBAAEjO,UAAU,EAAE,gBAAgB;kBAAE0D,SAAS,EAAEsR;gBAAS,CAAE,CAAC;cAC3E,CAAC,MAAM,IAAI,IAAI,CAAC7M,gBAAgB,CAACsN,yBAAyB,CAACT,SAAS,CAAC,EAAE;gBACrE,IAAI,CAAC9U,OAAO,CAAC+N,IAAI,CAAC;kBAAEjO,UAAU,EAAE,iBAAiB;kBAAE0D,SAAS,EAAEsR;gBAAS,CAAE,CAAC;cAC5E,CAAC,MAAM;gBACL,IAAI,CAAC9U,OAAO,CAAC+N,IAAI,CAAC;kBAAEjO,UAAU,EAAE,gBAAgB;kBAAE0D,SAAS,EAAEsR;gBAAS,CAAE,CAAC;cAC3E;YACF;UACF;QACF,CAAC,CAAC;QAEF5I,OAAO,CAACC,GAAG,CAAC+I,SAAS,CAAC;QACtB,IAAI,CAACxB,aAAa,CAACwB,SAAS,EAAEtR,eAAe,CAAC;QAE9C;QACA;QACA;QAEA,IAAI,CAACsE,mBAAmB,CAACuL,wBAAwB,CAACyB,SAAS,CAAC;MAC9D;IACF,CAAC,CAAC;EACJ;EAEAM,yBAAyBA,CAAA;IACvB,IAAI,CAACxV,OAAO,GAAG,EAAE;IACjB,MAAM4T,eAAe,GAAG,IAAI,CAAC3L,gBAAgB,CAAC4L,oBAAoB,CAAC,IAAI,CAAChJ,IAAI,CAAC2G,EAAE,CAAC;IAChF,MAAM0C,OAAO,GAAG,IAAI/W,WAAW,CAAC;MAC9B,4BAA4B,EAAE;KAC/B,CAAC;IACF,IAAI,CAAC4L,IAAI,CAAC2D,GAAG,CAACkH,eAAe,CAAChH,IAAI,CAChC/O,SAAS,CAAEuM,cAAc,IAAI;MAC3B,IAAIA,cAAc,EAAE;QAClB,MAAM2K,iBAAiB,GAAG3K,cAAc,CAACzM,GAAG,CAAEmX,SAAc,IAC1D,IAAI,CAAC7M,gBAAgB,CAACgN,mBAAmB,CAACH,SAAS,CAACtD,EAAE,CAAC,CACxD;QACD,MAAMiE,WAAW,GAAG;UAClBrL,cAAc,EAAEwJ,eAAe;UAC/B7S,OAAO,EAAExD,QAAQ,CAACwX,iBAAiB;SACpC;QACD,OAAOxX,QAAQ,CAACkY,WAAW,CAAC;MAC9B,CAAC,MAAM;QACL,OAAOjY,EAAE,CAAC4M,cAAc,CAAC;MAC3B;IACF,CAAC,CAAC,EACF3M,UAAU,CAAEgX,KAAK,IAAI;MACnBvI,OAAO,CAACuI,KAAK,CAACA,KAAK,CAAC;MACpB,IAAI,CAACpR,UAAU,GAAG,KAAK;MACvB,OAAO7F,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC,EACFE,QAAQ,CAAC,MAAK;MACZ,IAAI,CAACgW,aAAa,CAAC,EAAE,EAAE,EAAE,CAAC;MAC1B,IAAI,CAACrQ,UAAU,GAAG,KAAK;IACzB,CAAC,CAAC,CACH,CAACwJ,SAAS,CAAEzO,IAAS,IAAI;MACxB,IAAI,CAACiF,UAAU,GAAG,KAAK;MACvB;MACA,IAAI,CAACpE,eAAe,CAACuF,SAAS,CAACkC,UAAU,GAAGtI,IAAI,CAACgM,cAAc;MAC/D;MACA,IAAI,CAACnL,eAAe,CAACuF,SAAS,CAACzD,OAAO,GAAG3C,IAAI,CAAC2C,OAAO,CAAC2U,MAAM,CAAE3U,OAAW,IAAKA,OAAO,CAACd,MAAM,GAAG,CAAC,CAAC;MACjG,IAAI,CAAChB,eAAe,CAACuF,SAAS,CAACiB,aAAa,GAAGrH,IAAI,CAACqH,aAAa;IACnE,CAAC,CAAC,CAAC;EACL;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAGAiO,aAAaA,CAACjE,MAAc,EAAE7L,eAAsB;IAClD;IACA;IACA,IAAI,IAAI,CAACb,IAAI,KAAK,IAAI,CAACC,SAAS,CAACC,OAAO,EAAE;MACxC,IAAI,CAACjD,OAAO,CAAC+N,IAAI,CAAC;QAAEjO,UAAU,EAAE;MAAS,CAAE,CAAC;IAC9C;IACA,QAAQ2P,MAAM;MACZ,KAAKxR,kBAAkB,CAACoE,WAAW;QACjC,IAAI,CAACrC,OAAO,CAAC2V,OAAO,CAAC;UAAE7V,UAAU,EAAE8D,eAAe,CAAC3D,MAAM,GAAG,CAAC,GAAG,kBAAkB,GAAG;QAAkB,CAAE,CAAC;QAC1G;MACF,KAAKhC,kBAAkB,CAACqE,QAAQ;QAC9B,IAAI,CAACtC,OAAO,CAAC2V,OAAO,CAAC;UAAE7V,UAAU,EAAE;QAAgB,CAAE,CAAC;QACtD;MACF,KAAK7B,kBAAkB,CAACkE,SAAS;QAC/ByB,eAAe,CAAC3D,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAACD,OAAO,CAAC2V,OAAO,CAAC;UAAE7V,UAAU,EAAE;QAAgB,CAAE,CAAC;QAC1F;MACF,KAAK7B,kBAAkB,CAACuE,GAAG;MAC3B,KAAKvE,kBAAkB,CAACwE,IAAI;QAC1B,IAAI,CAACzC,OAAO,CAAC2V,OAAO,CAAC;UAAE7V,UAAU,EAAE;QAAgB,CAAE,CAAC;QACtD;MAEF,KAAK7B,kBAAkB,CAACmE,OAAO;MAC/B,KAAKnE,kBAAkB,CAACsE,IAAI;QAC1B,IAAI,CAACvC,OAAO,CAAC+N,IAAI,CAAC;UAAEjO,UAAU,EAAE;QAAa,CAAE,CAAC;QAChD;MACF,KAAK7B,kBAAkB,CAAC2B,MAAM;QAC5B;IACJ;EACF;EAEAgW,eAAeA,CAACC,UAAiB,EAAExK,UAAmB;IACpD,OAAOwK,UAAU,CAAC5V,MAAM,GAAG,CAAC,IAAIoL,UAAU;EAC5C;EAEA3K,kBAAkBA,CAAC6K,IAAS;IAC1B,MAAMuK,KAAK,GAAG,IAAItL,IAAI,CAACe,IAAI,CAAC;IAC5B,IAAI,CAACtK,iBAAiB,GAAG6U,KAAK;IAC9B,MAAMC,aAAa,GAAGD,KAAK,CAACE,kBAAkB,CAAC,OAAO,CAAC;IACvD9J,OAAO,CAACC,GAAG,CAAC4J,aAAa,CAAC;EAC5B;EAEA/I,oBAAoBA,CAAA;IAClB,IAAI,CAACjE,IAAI,CAACwE,IAAI,GAAG,IAAI,CAAClF,aAAa,CAAC4N,cAAc,CAACrJ,IAAI,CAAChP,IAAI,CAAC,CAAC,CAAC,CAAC,CAACiP,SAAS,CAAEC,GAAG,IAAI;MACjF,IAAIA,GAAG,KAAK,CAAC,EAAE;QACb,IAAI,CAAC7N,eAAe,CAACuF,SAAS,CAAC4G,cAAc,GAAG0B,GAAG;QACnD,IAAI,CAACvE,GAAG,CAAC2N,aAAa,EAAE,CAAC,CAAC;MAC5B;IACF,CAAC,CAAC;EACJ;EAEAxR,iBAAiBA,CAACmR,UAAuB;IACvC,IAAI,CAACA,UAAU,IAAIA,UAAU,CAAC5V,MAAM,KAAK,CAAC,EAAE;MAC1C,OAAO,CAAC;IACV;IACA,MAAMkW,UAAU,GAAGN,UAAU,CAAClI,IAAI,CAAEmH,SAAS,IAAK,IAAI,CAAC7M,gBAAgB,CAACmO,mCAAmC,CAACtB,SAAS,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IAC/H,OAAOqB,UAAU;EACnB;EAEAhQ,YAAYA,CAAC6K,GAAW;IACtB9E,OAAO,CAACC,GAAG,CAAC6E,GAAG,CAAC;IAChB,IAAI,CAACqF,aAAa,EAAE;IACpB,QAAQrF,GAAG;MACT,KAAK,KAAK;MACV,KAAK,SAAS;MACd,KAAK,QAAQ;MACb,KAAK,WAAW;MAChB,KAAK,SAAS;MACd,KAAK,WAAW;MAChB,KAAK,QAAQ;QACX,IAAI,CAAC1K,QAAQ,CAAC0K,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC1K,QAAQ,CAAC0K,GAAG,CAAC;QACxC;IACJ;IACA,IAAI,CAACsF,aAAa,EAAE;EACtB;EAEQD,aAAaA,CAAA;IACnB,KAAK,MAAMrF,GAAG,IAAI,IAAI,CAAC1K,QAAQ,EAAE;MAC/B,IAAI,CAACA,QAAQ,CAAC0K,GAAG,CAAC,GAAG,KAAK;IAC5B;EACF;EAEA;;;;;EAKQsF,aAAaA,CAACC,aAAa,GAAG,KAAK;IACzC;IACA,IAAI,CAACtX,eAAe,CAACuF,SAAS,CAACmB,uBAAuB,GAAG,IAAI,CAAC1G,eAAe,CAACuF,SAAS,CAACZ,eAAe,CAAC8R,MAAM,CAAEc,MAAiB,IAAI;MACnI,IAAIC,WAAgB;MAEpB;MACA,IAAI,IAAI,CAACnQ,QAAQ,CAACyE,OAAO,IAAIwL,aAAa,EAAE;QAC1C;QACA,OAAO,CACLC,MAAM,CAAC/G,MAAM,CAAC/P,WAAW,EAAE,CAACsM,QAAQ,CAACjO,MAAM,CAAC2Y,QAAQ,CAAChX,WAAW,EAAE,CAAC,IACnE8W,MAAM,CAAC/G,MAAM,CAAC/P,WAAW,EAAE,CAACsM,QAAQ,CAAChO,YAAY,CAACoR,QAAQ,CAAC1P,WAAW,EAAE,CAAC,IACzE8W,MAAM,CAAC/G,MAAM,CAAC/P,WAAW,EAAE,CAACsM,QAAQ,CAACjO,MAAM,CAAC4Y,OAAO,CAACjX,WAAW,EAAE,CAAC,MAC9D,CAAC+W,WAAW,IAAIA,WAAW,CAACD,MAAM,CAAC,CAAC;MAC5C;MAEA;MACA,IAAI,IAAI,CAAClQ,QAAQ,CAAC0E,SAAS,EAAE;QAC3B;QACA,OAAOwL,MAAM,CAAC/G,MAAM,CAAC/P,WAAW,EAAE,CAACsM,QAAQ,CAACjO,MAAM,CAACsR,SAAS,CAAC3P,WAAW,EAAE,CAAC,KAAK,CAAC+W,WAAW,IAAIA,WAAW,CAACD,MAAM,CAAC,CAAC;MACtH;MAEA;MACA,IAAI,IAAI,CAAClQ,QAAQ,CAAC2E,OAAO,EAAE;QACzB;QACA,OAAO,CACLuL,MAAM,CAAC/G,MAAM,CAAC/P,WAAW,EAAE,CAACsM,QAAQ,CAACjO,MAAM,CAAC6Y,OAAO,CAAClX,WAAW,EAAE,CAAC,IAClE8W,MAAM,CAAC/G,MAAM,CAAC/P,WAAW,EAAE,CAACsM,QAAQ,CAACjO,MAAM,CAAC8Y,SAAS,CAACnX,WAAW,EAAE,CAAC,MAChE,CAAC+W,WAAW,IAAIA,WAAW,CAACD,MAAM,CAAC,CAAC;MAC5C;MAEA;MACA,IAAI,IAAI,CAAClQ,QAAQ,CAACwQ,SAAS,EAAE;QAC3B;QACA,OAAON,MAAM,CAAC/G,MAAM,CAAC/P,WAAW,EAAE,CAACsM,QAAQ,CAACjO,MAAM,CAAC8Y,SAAS,CAACnX,WAAW,EAAE,CAAC,KAAK,CAAC+W,WAAW,IAAIA,WAAW,CAACD,MAAM,CAAC,CAAC;MACtH;MAEA;MACA,IAAI,IAAI,CAAClQ,QAAQ,CAACwE,GAAG,EAAE;QACrB;QACA,OAAO,IAAI,KAAK,CAAC2L,WAAW,IAAIA,WAAW,CAACD,MAAM,CAAC,CAAC;MACtD;MAEA;MACA,OAAO,KAAK;IACd,CAAC,CAAC,CAACO,OAAO,EAAE;EACd;EAEQ5B,oBAAoBA,CAAA;IAC1B,MAAM6B,kBAAkB,GAAG,IAAI,CAAC/X,eAAe,CAACuF,SAAS,CAACmB,uBAAuB;IACjF,MAAMsR,iBAAiB,GAAGD,kBAAkB,CAACtB,MAAM,CAAEc,MAAiB,IAAI;MACxE,OACEA,MAAM,CAAC/G,MAAM,CAAC/P,WAAW,EAAE,CAACsM,QAAQ,CAACjO,MAAM,CAAC2Y,QAAQ,CAAChX,WAAW,EAAE,CAAC,IACnE8W,MAAM,CAAC/G,MAAM,CAAC/P,WAAW,EAAE,CAACsM,QAAQ,CAAChO,YAAY,CAACoR,QAAQ,CAAC1P,WAAW,EAAE,CAAC,IACzE8W,MAAM,CAAC/G,MAAM,CAAC/P,WAAW,EAAE,CAACsM,QAAQ,CAACjO,MAAM,CAAC4Y,OAAO,CAACjX,WAAW,EAAE,CAAC;IAEtE,CAAC,CAAC;IACF,OAAOuX,iBAAiB;EAC1B;EAEQhK,0BAA0BA,CAAClK,IAAc;IAC/C,IAAIA,IAAI,KAAK7E,QAAQ,CAACgF,OAAO,EAAE;MAC7B,IAAI,CAACyQ,eAAe,EAAE;IACxB,CAAC,MAAM;MACL,IAAI,CAAC6B,yBAAyB,EAAE;IAClC;EACF;EACQtI,kBAAkBA,CAAA;IACxB,IAAI,CAACnE,IAAI,CAAC2D,GAAG,CAAC,IAAI,CAACvE,eAAe,CAAC2G,cAAc,CAACjC,SAAS,CAAEC,GAAQ,IAAI;MACvE,IAAIA,GAAG,EAAE;QACP,IAAI,CAACG,0BAA0B,CAAC,IAAI,CAAClK,IAAgB,CAAC;MACxD;IACF,CAAC,CAAC,CAAC;EACL;EAAC,QAAAmU,CAAA,G;qBAhsBU3P,kBAAkB,EAAAlJ,EAAA,CAAA8Y,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAhZ,EAAA,CAAA8Y,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAlZ,EAAA,CAAA8Y,iBAAA,CAAAK,EAAA,CAAAC,aAAA,GAAApZ,EAAA,CAAA8Y,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAAtZ,EAAA,CAAA8Y,iBAAA,CAAAS,EAAA,CAAAC,eAAA,GAAAxZ,EAAA,CAAA8Y,iBAAA,CAAAW,EAAA,CAAAC,YAAA,GAAA1Z,EAAA,CAAA8Y,iBAAA,CAAAa,EAAA,CAAAC,WAAA,GAAA5Z,EAAA,CAAA8Y,iBAAA,CAAAe,EAAA,CAAAC,mBAAA,GAAA9Z,EAAA,CAAA8Y,iBAAA,CAAAiB,EAAA,CAAAC,gBAAA,GAAAha,EAAA,CAAA8Y,iBAAA,CAAAmB,GAAA,CAAAC,mBAAA,GAAAla,EAAA,CAAA8Y,iBAAA,CAAAqB,GAAA,CAAAC,eAAA,GAAApa,EAAA,CAAA8Y,iBAAA,CAAAuB,GAAA,CAAAC,WAAA,GAAAta,EAAA,CAAA8Y,iBAAA,CAAAyB,GAAA,CAAAC,aAAA,GAAAxa,EAAA,CAAA8Y,iBAAA,CAAA2B,GAAA,CAAAC,YAAA,GAAA1a,EAAA,CAAA8Y,iBAAA,CAAA9Y,EAAA,CAAA2a,iBAAA,GAAA3a,EAAA,CAAA8Y,iBAAA,CAAA8B,GAAA,CAAAC,gBAAA,GAAA7a,EAAA,CAAA8Y,iBAAA,CAAAgC,GAAA,CAAAC,sBAAA,GAAA/a,EAAA,CAAA8Y,iBAAA,CAAA9Y,EAAA,CAAAgb,SAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlB/R,kBAAkB;IAAAgS,SAAA;IAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;QAAlBrb,EAAA,CAAA8B,UAAA,oBAAAyZ,6CAAA;UAAA,OAAAD,GAAA,CAAAzK,QAAA,EAAU;QAAA,UAAA7Q,EAAA,CAAAwb,eAAA,CAAQ,oBAAAC,6CAAAzZ,MAAA;UAAA,OAAlBsZ,GAAA,CAAA7J,cAAA,CAAAzP,MAAA,CAAsB;QAAA,UAAAhC,EAAA,CAAAwb,eAAA,CAAJ;;;;;;;;;QCqF9Bxb,EA5HD,CAAAM,UAAA,IAAAob,mCAAA,OA8HC,IAAAC,8CAAA;QA9HD3b,EAAA,CAAA4b,OAAA,OAAAC,iCAAA,UA8HC;QA9HM7b,EAAA,CAAA8b,iBAAA,OAAW;QA6Id9b,EAFJ,CAAAE,cAAA,aAAgD,aACrB,UAClB;QAAAF,EAAA,CAAAoD,MAAA,mBAAY;QAAApD,EAAA,CAAAI,YAAA,EAAM;QACvBJ,EAAA,CAAAE,cAAA,aAAuG;QAAlGF,EAAA,CAAA8B,UAAA,mBAAAia,iDAAA;UAAA/b,EAAA,CAAAiC,aAAA,CAAA+Z,GAAA;UAAA,OAAAhc,EAAA,CAAAoC,WAAA,CAASkZ,GAAA,CAAApJ,cAAA,EAAgB;QAAA,EAAC;QAGnClS,EAHI,CAAAI,YAAA,EAAuG,EACnG,EAEF;QAIFJ,EAFJ,CAAAE,cAAA,cAAiD,cACtB,WAClB;QAAAF,EAAA,CAAAoD,MAAA,kBAAU;QAAApD,EAAA,CAAAI,YAAA,EAAM;QACrBJ,EAAA,CAAAE,cAAA,cAAwG;QAAnGF,EAAA,CAAA8B,UAAA,mBAAAma,kDAAA;UAAAjc,EAAA,CAAAiC,aAAA,CAAA+Z,GAAA;UAAA,OAAAhc,EAAA,CAAAoC,WAAA,CAASkZ,GAAA,CAAAnJ,eAAA,EAAiB;QAAA,EAAC;QAKpCnS,EALI,CAAAI,YAAA,EAAwG,EACpG,EAIF;QA6NNJ,EA1NA,CAAAM,UAAA,KAAA4b,0CAAA,kCAAAlc,EAAA,CAAAmc,sBAAA,CAA2D,KAAAC,0CAAA,iCAAApc,EAAA,CAAAmc,sBAAA,CAmDZ,KAAAE,0CAAA,kCAAArc,EAAA,CAAAmc,sBAAA,CA+BA,KAAAG,0CAAA,gCAAAtc,EAAA,CAAAmc,sBAAA,CAiEE,KAAAI,0CAAA,gCAAAvc,EAAA,CAAAmc,sBAAA,CA+CJ,KAAAK,0CAAA,gCAAAxc,EAAA,CAAAmc,sBAAA,CAWO,KAAAM,0CAAA,kCAAAzc,EAAA,CAAAmc,sBAAA,CAaV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}