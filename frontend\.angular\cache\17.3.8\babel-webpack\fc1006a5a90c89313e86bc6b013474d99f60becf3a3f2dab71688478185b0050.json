{"ast": null, "code": "import { CommonModule, DOCUMENT } from '@angular/common';\nimport { signal } from '@angular/core';\nimport { FormsModule, ReactiveFormsModule, UntypedFormControl, Validators } from '@angular/forms';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { RecaptchaModule } from 'ng-recaptcha';\nimport { ButtonModule } from 'primeng/button';\nimport { CheckboxModule } from 'primeng/checkbox';\nimport { forkJoin, of } from 'rxjs';\nimport { switchMap, take } from 'rxjs/operators';\nimport { UserRole, UserStatus } from 'src/app/core/models/user.model';\nimport { slideInOut, slideUpDownAnimation } from 'src/app/helpers/my-animations';\nimport { LoaderComponent } from 'src/app/shared/loader/loader.component';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/toast.service\";\nimport * as i2 from \"src/app/core/services/general.service\";\nimport * as i3 from \"src/app/core/services/chat.service\";\nimport * as i4 from \"src/app/core/services/auth.service\";\nimport * as i5 from \"src/app/core/services/user.service\";\nimport * as i6 from \"src/app/core/services/teacher-application.service\";\nimport * as i7 from \"src/app/core/services/student-trial.service\";\nimport * as i8 from \"@angular/router\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"primeng/button\";\nimport * as i11 from \"primeng/checkbox\";\nimport * as i12 from \"@angular/forms\";\nimport * as i13 from \"ng-recaptcha\";\nconst _c0 = [\"lottieSignupEl\"];\nconst LoginComponent_Defer_2_DepsFn = () => [i9.NgClass, i9.NgIf, i10.ButtonDirective, i11.Checkbox, i12.ɵNgNoValidate, i12.DefaultValueAccessor, i12.NgControlStatus, i12.NgControlStatusGroup, i12.RequiredValidator, i12.PatternValidator, i12.EmailValidator, i12.NgModel, i12.NgForm, LoaderComponent, i13.RecaptchaComponent];\nconst _c1 = a0 => ({\n  \"right-panel-active\": a0\n});\nconst _c2 = (a0, a1, a2) => ({\n  \"teacher-bg\": a0,\n  \"student-bg\": a1,\n  \"disabled-link\": a2\n});\nfunction LoginComponent_Defer_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 35)(2, \"div\", 36);\n    i0.ɵɵelement(3, \"app-loader\", 37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"scale\", 1.6);\n  }\n}\nfunction LoginComponent_Defer_0_ng_container_12_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtext(1, \"Email is not valid.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_Defer_0_ng_container_12_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 62);\n    i0.ɵɵtext(2, \"Great job! Your password meets the requirements.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LoginComponent_Defer_0_ng_container_12_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 62);\n    i0.ɵɵtext(2, \"Password must contain at least one capital letter and one number\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LoginComponent_Defer_0_ng_container_12_ng_template_27_Template(rf, ctx) {}\nfunction LoginComponent_Defer_0_ng_container_12_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtext(1, \"Passwords do not match.\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_Defer_0_ng_container_12_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64);\n    i0.ɵɵelement(2, \"button\", 65);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"label\", ctx_r2.registerButtonLabel)(\"icon\", ctx_r2.isRegisterLoading ? \"pi pi-spin pi-spinner\" : \"pi pi-arrow-right\")(\"ngClass\", i0.ɵɵpureFunction3(3, _c2, ctx_r2.registerAsTeacher, !ctx_r2.registerAsTeacher, ctx_r2.isRegisterLoading));\n  }\n}\nfunction LoginComponent_Defer_0_ng_container_12_ng_container_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction LoginComponent_Defer_0_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 38);\n    i0.ɵɵtext(2, \" Sign Up \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 39)(4, \"div\", 40)(5, \"div\", 41)(6, \"p-checkbox\", 42);\n    i0.ɵɵlistener(\"onChange\", function LoginComponent_Defer_0_ng_container_12_Template_p_checkbox_onChange_6_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onChange());\n    });\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Defer_0_ng_container_12_Template_p_checkbox_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedRole, $event) || (ctx_r2.selectedRole = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 40)(8, \"div\", 43)(9, \"p-checkbox\", 44);\n    i0.ɵɵlistener(\"onChange\", function LoginComponent_Defer_0_ng_container_12_Template_p_checkbox_onChange_9_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onChange());\n    });\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Defer_0_ng_container_12_Template_p_checkbox_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.selectedRole, $event) || (ctx_r2.selectedRole = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(10, \"form\", 45, 1);\n    i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Defer_0_ng_container_12_Template_form_ngSubmit_10_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const signupForm_r4 = i0.ɵɵreference(11);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onSignup(signupForm_r4));\n    });\n    i0.ɵɵelementStart(12, \"div\", 46)(13, \"div\", 47);\n    i0.ɵɵtext(14, \"Email * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"input\", 48, 2);\n    i0.ɵɵtemplate(17, LoginComponent_Defer_0_ng_container_12_div_17_Template, 2, 0, \"div\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 46)(19, \"div\", 47);\n    i0.ɵɵtext(20, \"Password *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 50)(22, \"i\", 51);\n    i0.ɵɵlistener(\"click\", function LoginComponent_Defer_0_ng_container_12_Template_i_click_22_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.togglemyPasswordFieldType(\"password\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"input\", 52, 3);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Defer_0_ng_container_12_Template_input_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.password, $event) || (ctx_r2.password = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(25, LoginComponent_Defer_0_ng_container_12_div_25_Template, 3, 0, \"div\", 10)(26, LoginComponent_Defer_0_ng_container_12_div_26_Template, 3, 0, \"div\", 53)(27, LoginComponent_Defer_0_ng_container_12_ng_template_27_Template, 0, 0, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 46)(30, \"div\", 47);\n    i0.ɵɵtext(31, \"Confirm Password *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\", 50)(33, \"i\", 51);\n    i0.ɵɵlistener(\"click\", function LoginComponent_Defer_0_ng_container_12_Template_i_click_33_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.togglemyPasswordFieldType(\"confirm\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(34, \"input\", 54, 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(36, LoginComponent_Defer_0_ng_container_12_div_36_Template, 2, 0, \"div\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 55)(38, \"p-checkbox\", 56);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function LoginComponent_Defer_0_ng_container_12_Template_p_checkbox_ngModelChange_38_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.termsAgreed, $event) || (ctx_r2.termsAgreed = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"a\", 57);\n    i0.ɵɵtext(40, \"terms and conditions*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerStart(41);\n    i0.ɵɵelementStart(42, \"div\", 58)(43, \"re-captcha\", 59);\n    i0.ɵɵlistener(\"resolved\", function LoginComponent_Defer_0_ng_container_12_Template_re_captcha_resolved_43_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.captchaResolvedEvent($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(44, LoginComponent_Defer_0_ng_container_12_div_44_Template, 3, 7, \"div\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(45, LoginComponent_Defer_0_ng_container_12_ng_container_45_Template, 1, 0, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const passwordInput_r5 = i0.ɵɵreference(24);\n    const templateName_r6 = i0.ɵɵreference(28);\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedRole);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.selectedRole);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.emailError);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getKeyValueFieldType(\"password\") ? \"pi-eye\" : \"pi-eye-slash\");\n    i0.ɵɵadvance();\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.password);\n    i0.ɵɵproperty(\"type\", ctx_r2.getKeyValueFieldType(\"password\") ? \"text\" : \"password\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", passwordInput_r5 && passwordInput_r5.valid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", passwordInput_r5.invalid)(\"ngIfElse\", templateName_r6);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getKeyValueFieldType(\"confirm\") ? \"pi-eye\" : \"pi-eye-slash\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"type\", ctx_r2.getKeyValueFieldType(\"confirm\") ? \"text\" : \"password\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.confirmError);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.termsAgreed);\n    i0.ɵɵproperty(\"value\", ctx_r2.termsAgreed);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedRole.length !== 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.registerAsTeacher && ctx_r2.selectedRole.length !== 0);\n  }\n}\nfunction LoginComponent_Defer_0_ng_container_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 35)(2, \"div\", 36);\n    i0.ɵɵelement(3, \"app-loader\", 37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"scale\", 1.6);\n  }\n}\nfunction LoginComponent_Defer_0_div_19_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtext(1, \"Email is not valid\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_Defer_0_div_19_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 76);\n    i0.ɵɵtext(1, \" Don't have an account? \");\n    i0.ɵɵelementStart(2, \"div\", 77);\n    i0.ɵɵlistener(\"click\", function LoginComponent_Defer_0_div_19_div_20_Template_div_click_2_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.switchView(true, true));\n    });\n    i0.ɵɵtext(3, \" Sign Up\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction LoginComponent_Defer_0_div_19_form_25_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵtext(1, \"Email is not valid\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_Defer_0_div_19_form_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"form\", 67, 6);\n    i0.ɵɵlistener(\"submit\", function LoginComponent_Defer_0_div_19_form_25_Template_form_submit_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const forgotForm_r11 = i0.ɵɵreference(1);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onForgot(forgotForm_r11));\n    });\n    i0.ɵɵelementStart(2, \"div\", 46)(3, \"div\", 47);\n    i0.ɵɵtext(4, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 62);\n    i0.ɵɵtext(6, \"Please enter your email address to receive the password reset instructions. If you don\\u2019t see the email in your inbox, please check your spam or junk folder.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"input\", 68, 7);\n    i0.ɵɵtemplate(9, LoginComponent_Defer_0_div_19_form_25_div_9_Template, 2, 0, \"div\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 78);\n    i0.ɵɵelement(11, \"button\", 79);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.emailError);\n  }\n}\nfunction LoginComponent_Defer_0_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 66)(1, \"div\", 39);\n    i0.ɵɵelement(2, \"div\", 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"form\", 67, 5);\n    i0.ɵɵlistener(\"submit\", function LoginComponent_Defer_0_div_19_Template_form_submit_3_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const loginForm_r8 = i0.ɵɵreference(4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onLogin(loginForm_r8));\n    });\n    i0.ɵɵelementStart(5, \"div\", 46)(6, \"div\", 47);\n    i0.ɵɵtext(7, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"input\", 68, 2);\n    i0.ɵɵtemplate(10, LoginComponent_Defer_0_div_19_div_10_Template, 2, 0, \"div\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 46)(12, \"div\", 47);\n    i0.ɵɵtext(13, \"Password\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 50)(15, \"i\", 51);\n    i0.ɵɵlistener(\"click\", function LoginComponent_Defer_0_div_19_Template_i_click_15_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.togglemyPasswordFieldType(\"email\"));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(16, \"input\", 69, 3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 70);\n    i0.ɵɵelement(19, \"button\", 71);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(20, LoginComponent_Defer_0_div_19_div_20_Template, 4, 0, \"div\", 72);\n    i0.ɵɵelementStart(21, \"div\", 73);\n    i0.ɵɵlistener(\"click\", function LoginComponent_Defer_0_div_19_Template_div_click_21_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.showForgotPassword = !ctx_r2.showForgotPassword);\n    });\n    i0.ɵɵtext(22, \" Forgot password? \");\n    i0.ɵɵelementStart(23, \"div\", 74);\n    i0.ɵɵtext(24, \" Get it here\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(25, LoginComponent_Defer_0_div_19_form_25_Template, 12, 1, \"form\", 75);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.emailError);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getKeyValueFieldType(\"email\") ? \"pi-eye\" : \"pi-eye-slash\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"type\", ctx_r2.getKeyValueFieldType(\"email\") ? \"text\" : \"password\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.is1024);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showForgotPassword);\n  }\n}\nfunction LoginComponent_Defer_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9);\n    i0.ɵɵtemplate(2, LoginComponent_Defer_0_ng_container_2_Template, 4, 1, \"ng-container\", 10);\n    i0.ɵɵelementStart(3, \"div\", 11)(4, \"div\", 12)(5, \"div\", 13)(6, \"div\", 14);\n    i0.ɵɵelement(7, \"div\", null, 0);\n    i0.ɵɵelementStart(9, \"div\", 15)(10, \"span\", 16);\n    i0.ɵɵtext(11, \"You have successfully registered an account!\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵtemplate(12, LoginComponent_Defer_0_ng_container_12_Template, 46, 16, \"ng-container\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 17)(14, \"div\", 18)(15, \"div\", 19)(16, \"span\", 20);\n    i0.ɵɵtext(17, \" Sign In \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(18, LoginComponent_Defer_0_ng_container_18_Template, 4, 1, \"ng-container\", 10)(19, LoginComponent_Defer_0_div_19_Template, 26, 5, \"div\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 22)(21, \"div\", 23)(22, \"div\", 24)(23, \"div\", 25)(24, \"a\", 26);\n    i0.ɵɵelement(25, \"img\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function LoginComponent_Defer_0_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.handleSwitchViewClick());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\")(28, \"h1\", 29);\n    i0.ɵɵtext(29, \"Welcome!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"h2\", 30)(31, \"a\", 31);\n    i0.ɵɵlistener(\"click\", function LoginComponent_Defer_0_Template_a_click_31_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.handleSwitchViewClick());\n    });\n    i0.ɵɵtext(32, \"Sign Up if you don't have an account\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(33, \"div\", 32)(34, \"div\", 25)(35, \"a\", 26);\n    i0.ɵɵelement(36, \"img\", 27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 33)(38, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function LoginComponent_Defer_0_Template_button_click_38_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.handleSwitchViewClick());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"div\")(40, \"a\", 34);\n    i0.ɵɵlistener(\"click\", function LoginComponent_Defer_0_Template_a_click_40_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.handleSwitchViewClick());\n    });\n    i0.ɵɵtext(41, \"Sign-In if you are already a member\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(42, \"div\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c1, ctx_r2.loginIsOpen));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"hidden\", ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"hidden\", !ctx_r2.showSuccessRegistrationMsg);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.showSuccessRegistrationMsg);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoading);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"label\", \"Sign Up\");\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"label\", \"Sign In\");\n  }\n}\nfunction LoginComponent_DeferPlaceholder_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36);\n    i0.ɵɵelement(2, \"app-loader\", 37);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scale\", 1.6);\n  }\n}\nexport let LoginComponent = /*#__PURE__*/(() => {\n  class LoginComponent {\n    constructor(toastService, generalService, chatService, authService, userService, teacherApplicationService, studentTrialService, router, zone, document, renderer, location, activatedRoute) {\n      this.toastService = toastService;\n      this.generalService = generalService;\n      this.chatService = chatService;\n      this.authService = authService;\n      this.userService = userService;\n      this.teacherApplicationService = teacherApplicationService;\n      this.studentTrialService = studentTrialService;\n      this.router = router;\n      this.zone = zone;\n      this.document = document;\n      this.renderer = renderer;\n      this.location = location;\n      this.activatedRoute = activatedRoute;\n      this.subs = new SubSink();\n      this.isLoading = false;\n      this.isRegisterLoading = false;\n      this.termsAgreed = false;\n      this.loginIsOpen = true;\n      this.signUpIsOpen = false;\n      this.registerAsTeacher = false;\n      this.is768 = false;\n      this.is1024 = false;\n      this.is1366 = false;\n      this.is1024up = false;\n      this.isBig = false;\n      this.emailError = false;\n      this.confirmError = false;\n      this.showSuccessRegistrationMsg = false;\n      this.showForgotPassword = false;\n      this.selectedRole = [];\n      this.registerButtonLabel = 'Register as Student';\n      this.ismyTextFieldType = {\n        email: false,\n        password: false,\n        confirm: false\n      };\n      this.passwordCheck = '';\n      this.passwordFormControl = new UntypedFormControl('', [Validators.required, Validators.pattern('^(?=.*[A-Z])(?=.*[0-9]).*$')]);\n      this.isDisabled = true;\n      this.hasAnimated = true;\n      this.captchaResolved = signal(false);\n      this.lottieSignupEl = {};\n      this.mwindow = this.document.defaultView;\n      console.log(activatedRoute.firstChild);\n      // try to switchView on register/login route detection\n    }\n    ngOnInit() {\n      // get return url from route parameters or default to ‘/’\n      this.returnUrl = this.activatedRoute.snapshot.queryParams['returnUrl'] || undefined;\n      if (this.router.url.includes('login')) {\n        this.loginIsOpen = true;\n      }\n      if (this.router.url.includes('register')) {\n        this.loginIsOpen = false;\n      }\n      this.subs.add(this.chatService.getFireBaseUser().subscribe(res => {\n        if (res) {\n          this.chatService.signOutFromFirebase();\n        }\n      }));\n      this.subs.add(this.authService.getAuthStatusListener().subscribe(authStatus => {\n        if (authStatus) {\n          // this.isLoading = false;\n        }\n      }));\n      this.subs.add(this.generalService.deviceKind.pipe(take(1)).subscribe(res => {\n        if (res) {\n          this.is1024 = res.is1024;\n          this.is1366 = res.is1366;\n          this.is768 = res.is768;\n          this.isBig = res.isBig;\n          this.is1024up = res.w1024up;\n          if (this.is1024up) {\n            // this.renderer.addClass(this.document.getElementsByClassName('app')[0], 'h-full');\n          }\n          console.log(res);\n        }\n      }));\n      this.registrationSuccessMessageListener();\n      // this.renderer.addClass(this.document.body, 'gradient-moving-body');\n    }\n    executeRecaptchaV3() {}\n    loadScript(url) {\n      console.log('preparing to load...');\n      let node = document.createElement('script');\n      node.setAttribute(\"id\", \"lottie-player\");\n      node.src = url;\n      node.type = 'text/javascript';\n      node.async = true;\n      node.charset = 'utf-8';\n      document.getElementsByTagName('head')[0].appendChild(node);\n    }\n    ngAfterViewInit() {}\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n      // this.renderer.removeClass(this.document.body, 'height-screen');\n      // this.renderer.removeClass(this.document.getElementsByClassName('app')[0], 'h-full');\n    }\n    registrationSuccessMessageListener() {\n      this.subs.add(this.authService.showRegistrationSuccessfullMessage.subscribe(res => {\n        console.log(res);\n        this.showSuccessRegistrationMsg = res;\n        if (res) {\n          this.addLottiePlayerForSignupCompleted(this.lottieSignupEl);\n        }\n      }));\n    }\n    checkBoxChange(args) {\n      this.registerAsTeacher = args.checked;\n    }\n    switchView(animate = false, changeHasAnimated = false) {\n      this.showSuccessRegistrationMsg = false;\n      if (changeHasAnimated) {\n        this.hasAnimated = changeHasAnimated;\n      }\n      if (this.isBig || this.is1366 || animate) {\n        if (this.loginIsOpen) {\n          if (this.is1024up) {\n            const elem1 = document.getElementById('left-side');\n            const elem2 = document.getElementById('right-side');\n            // const rect = elem2!.getBoundingClientRect();\n            // const parent = elem2!.parentElement;\n            // const leftOffset = elem2!.offsetLeft;\n            // const topOffset = elem1!.offsetTop;\n            // const leftWidth = elem2!.offsetWidth;\n            // const newLeft2 = rect.left - parent!.offsetLeft;\n            // const newLeft = newLeft2 + leftWidth;\n            // elem1!.style.left = newLeft - 2 + 'px';\n            document.getElementById('left-side').style.left = '470px';\n            document.getElementById('left-side').style.borderRadius = '0px 28px 28px 0';\n            document.getElementById('right-side').style.borderRadius = '28px 0px 0 28px';\n            document.getElementById('right-side').style.right = '270px';\n          }\n          const url = this.router.createUrlTree(['/auth/register'], {\n            relativeTo: this.activatedRoute\n          }).toString();\n          this.location.go(url);\n        } else {\n          if (this.is1024up) {\n            document.getElementById('left-side').style.left = '0';\n            document.getElementById('left-side').style.borderRadius = '28px 0px 0 28px';\n            document.getElementById('right-side').style.borderRadius = '0px 28px 28px 0';\n            document.getElementById('right-side').style.right = '0';\n          }\n          const url = this.router.createUrlTree(['/auth/login'], {\n            relativeTo: this.activatedRoute\n          }).toString();\n          this.location.go(url);\n        }\n      }\n      this.loginIsOpen = !this.loginIsOpen;\n      this.signUpIsOpen = !this.signUpIsOpen;\n    }\n    onChange() {\n      const selectedRole = this.selectedRole[this.selectedRole.length - 1];\n      this.selectedRole.length = 0;\n      this.selectedRole.push(selectedRole);\n      if (!selectedRole) {\n        this.selectedRole.length = 0;\n      }\n      this.detectRole();\n      this.isDisabled = false;\n    }\n    detectRole() {\n      if (this.selectedRole[0] === 'teacher') {\n        this.registerAsTeacher = true;\n        this.registerButtonLabel = 'Register as Teacher';\n      } else {\n        this.registerAsTeacher = false;\n        this.registerButtonLabel = 'Register as Student';\n      }\n    }\n    removeSpaces(control) {\n      if (control && control.value && !control.value.replace(/\\s/g, '').length) {\n        control.setValue('');\n      }\n      return null;\n    }\n    setValidatorsForTrimming(form, controlName) {\n      const control = form.controls[controlName];\n      control.setValue(control.value.trim());\n      control.setValidators([Validators.required, this.removeSpaces]);\n      control.updateValueAndValidity();\n    }\n    onLogin(form) {\n      this.setValidatorsForTrimming(form, 'email');\n      this.setValidatorsForTrimming(form, 'password');\n      this.emailError = this.validEmail(form);\n      const loginForm = form.value;\n      console.log(form.value);\n      if (form.invalid) {\n        this.toastService.setShowToastmessage({\n          severity: 'warn',\n          summary: '',\n          detail: 'Please enter email and password to continue.'\n        });\n        return;\n      }\n      this.isLoading = true;\n      let expiresInDuration = 10800;\n      let token = null;\n      let tokenInfo = null;\n      const result1$ = this.subs.add(this.authService.login(form.value.email.trim(), form.value.password.trim()).pipe(switchMap(response => {\n        token = response.token;\n        tokenInfo = this.authService.getDecodedAccessToken(token); // decode token\n        this.authService.setToken(token);\n        if (token) {\n          expiresInDuration = 10800;\n          // const expiresInDuration = tokenInfo.exp;\n          this.authService.setAuthTimer(expiresInDuration);\n          this.userId = response.id;\n          this.user = response;\n          if (tokenInfo.role === UserRole.TEACHER) {\n            return this.requestDataFromMultipleSources(response);\n            // return this.userService.getUserAvailability(response.id);\n          } else {\n            this.authService.authStatusListener.next(true);\n            const now = new Date();\n            const expirationDate = new Date(now.getTime() + expiresInDuration * 1000);\n            this.authService.saveAuthData(token, expirationDate, this.userId, this.user, tokenInfo.role);\n            // this.router.navigate(['/dashboard']);\n            return this.requestStudentTrialData();\n          }\n        } else {\n          return of(null);\n        }\n      })).subscribe(res => {\n        console.log(loginForm);\n        let u = {};\n        // TODO: check if below is needed for Firebase integration\n        // this.existsFirebaseUserEmail(loginForm);\n        if (tokenInfo.role === UserRole.TEACHER) {\n          let u = {\n            ...this.user,\n            availability: res[0],\n            off: res[1]\n          };\n        } else {\n          console.log(res);\n          // for student\n          this.studentTrialService.setTrialRequested(res[0]);\n          // this.user = this.user;\n        }\n        // this.user.role = 'trial'\n        this.authService.authStatusListener.next(true);\n        const now = new Date();\n        const expirationDate = new Date(now.getTime() + expiresInDuration * 1000);\n        this.authService.setIsAuthenticated(true);\n        this.authService.saveAuthData(token, expirationDate, this.userId, this.user, tokenInfo.role);\n        this.isLoading = false;\n        // checking teacher application status steps if all are false (means the user hasnt started filling forms yet)\n        if (tokenInfo.role.toLowerCase() === UserRole.TEACHER.toLowerCase()) {\n          // teacher is active\n          if (this.user?.status.toLowerCase() === UserStatus.ACTIVE.toLowerCase()) {\n            if (this.returnUrl) {\n              this.router.navigate([this.returnUrl]);\n            } else {\n              this.router.navigateByUrl('/dashboard');\n            }\n            return false;\n          }\n          const valuesArray = Object.values(res[2]);\n          const allFalse = valuesArray.every(value => value === false);\n          console.log(allFalse);\n          if (allFalse) {\n            this.router.navigateByUrl('/teacher/info');\n            return false;\n          } else {\n            this.router.navigateByUrl(this.teacherApplicationService.getTeacherStatusNavigationUrl(res[1]));\n            return false;\n          }\n        }\n        // student\n        if (this.returnUrl) {\n          this.router.navigate([this.returnUrl]);\n        } else {\n          this.router.navigateByUrl('/dashboard');\n        }\n        return false;\n      }, error => {\n        this.isLoading = false;\n      }));\n    }\n    onForgot(form) {\n      this.emailError = this.validEmail(form);\n      if (form.invalid) {\n        this.toastService.setShowToastmessage({\n          severity: 'warn',\n          summary: '',\n          detail: 'Please enter email to continue.'\n        });\n        return;\n      }\n      this.toastService.setShowToastmessage({\n        severity: 'success',\n        summary: '',\n        detail: 'Please check your email to reset your password.'\n      });\n      this.authService.forgot(form.value.email);\n      form.reset();\n    }\n    onSignup(form) {\n      let endPointType = this.registerAsTeacher ? 'RegisterTeacher' : 'RegisterStudent';\n      this.emailError = this.validEmail(form);\n      this.confirmError = this.confirmPassword(form.value.password, form.value.confirm);\n      let invalidInputs = '';\n      for (const controlName in form.controls) {\n        if (form.controls.hasOwnProperty(controlName)) {\n          const control = form.controls[controlName];\n          if (control.invalid) {\n            invalidInputs += `${controlName}, `;\n          }\n        }\n      }\n      let detailMessage = 'Please check all required fields to continue. \\n\\n';\n      if (invalidInputs !== '') {\n        invalidInputs = invalidInputs.slice(0, -2); // Remove the trailing comma and space\n        if (invalidInputs !== '') {\n          detailMessage += ` Invalid input for the following fields: ${invalidInputs}`;\n        }\n      }\n      if (!this.termsAgreed) {\n        detailMessage += ` Please agree to our terms & conditions.`;\n      }\n      if (!this.captchaResolved()) {\n        detailMessage += ` Please resolve the captcha to continue.`;\n      }\n      if (form.invalid || !this.termsAgreed || this.confirmError || !this.captchaResolved()) {\n        this.toastService.setShowToastmessage({\n          severity: 'warn',\n          summary: '',\n          detail: detailMessage\n        });\n        return;\n      }\n      this.isRegisterLoading = true;\n      this.authService.createUser(form.value.email, form.value.password, endPointType).subscribe(res => {\n        this.authService.setShowRegistrationSuccessfullMessage(true);\n        this.isRegisterLoading = false;\n      }, error => {\n        this.isRegisterLoading = false;\n        this.authService.authStatusListener.next(false);\n        form.reset();\n      });\n    }\n    onTeacherButtonClicked() {\n      this.registerAsTeacher = true;\n    }\n    onStudentButtonClicked() {\n      this.registerAsTeacher = false;\n    }\n    validEmail(form) {\n      if (form.value.email) {\n        let validEmail = form.value.email.trim().toLowerCase().match(/^\\S+@\\S+\\.\\S+$/);\n        if (validEmail === null && form.touched) {\n          return true;\n        }\n      }\n      return false;\n    }\n    confirmPassword(password, confirm) {\n      return password !== confirm;\n    }\n    //TODO GOOGLE LOGIN\n    // public externalLogin = () => {\n    //   this.authService.signInWithGoogle()\n    //   .then(res => {\n    //     const user: SocialUser = { ...res };\n    //     console.log(user);\n    //   }, error => console.log(error))\n    // }\n    togglemyPasswordFieldType(key) {\n      this.ismyTextFieldType[key] = !this.ismyTextFieldType[key];\n    }\n    getKeyValueFieldType(key) {\n      return this.ismyTextFieldType[key];\n    }\n    requestDataFromMultipleSources(response) {\n      let response1 = this.userService.getUserAvailability(response.id);\n      let response2 = this.authService.getTeacherStatus();\n      let response3 = this.teacherApplicationService.getTeacherApplicationStepsStatus();\n      // Observable.forkJoin (RxJS 5) changes to just forkJoin() in RxJS 6\n      return forkJoin([response1, response2, response3]);\n    }\n    requestStudentTrialData() {\n      let response2 = this.studentTrialService.getStudentHasTrialRequest();\n      // Observable.forkJoin (RxJS 5) changes to just forkJoin() in RxJS 6\n      return forkJoin([response2]);\n    }\n    /**\n     * Loads LottiePlayer library from the CDN and shows success registration message.\n     */\n    loadLottiePlayer() {\n      const _this = this;\n      const successMessageDelay = 10; // in milliseconds\n      const lottiePlayerCDN = '/assets/js/lottie-player.js';\n      setTimeout(() => {\n        var id = document.getElementById('lottie-player');\n        if (!id) {\n          _this.loadScript(lottiePlayerCDN);\n        }\n        _this.showSuccessRegistrationMsg = true;\n      }, successMessageDelay);\n    }\n    handleSwitchViewClick() {\n      const container = document.getElementById('container');\n      container.classList.toggle(\"right-panel-active\");\n      if (!container.classList.contains(\"right-panel-active\")) {\n        const url = this.router.createUrlTree(['/auth/register'], {\n          relativeTo: this.activatedRoute\n        }).toString();\n        this.location.go(url);\n      } else {\n        const url = this.router.createUrlTree(['/auth/login'], {\n          relativeTo: this.activatedRoute\n        }).toString();\n        this.location.go(url);\n      }\n    }\n    captchaResolvedEvent(captchaResponse) {\n      console.log(`Resolved captcha with response: ${captchaResponse}`);\n      this.captchaResolved.set(true);\n    }\n    addLottiePlayerForSignupCompleted(el) {\n      const lottiePlayer = `<lottie-player src=\"https://assets4.lottiefiles.com/packages/lf20_xwmj0hsk.json\"  \n    background=\"transparent\"  speed=\"1\"  style=\"width: 280px; height: 280px; margin:0 auto;\"  autoplay></lottie-player>`;\n      el.nativeElement.innerHTML = lottiePlayer;\n    }\n    handleFirebaseUserResponse(response) {\n      if (response) {\n        this.authService.firebaseLoggedIn.set(true);\n        this.subs.add(this.chatService.getFireBaseUser().subscribe({\n          next: user => {},\n          error: error => this.handleFirebaseError(error)\n        }));\n      }\n    }\n    handleFirebaseError(error) {\n      console.error(error);\n      alert(error);\n    }\n    existsFirebaseUserEmail(form) {\n      this.chatService.checkFirebaseUserExistsByEmail(form.email).then(exists => {\n        if (exists) {\n          this.subs.add(this.chatService.signInFirebaseUser(form.email, form.password).subscribe({\n            next: res => this.handleFirebaseUserResponse(res),\n            error: error => this.handleFirebaseError(error)\n          }));\n        } else {\n          this.subs.add(this.chatService.createFirebaseUser(form.email, form.password).subscribe({\n            next: res => this.handleFirebaseUserResponse(res),\n            error: error => this.handleFirebaseError(error)\n          }));\n        }\n      }).catch(error => this.handleFirebaseError(error));\n    }\n    static #_ = this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.ToastService), i0.ɵɵdirectiveInject(i2.GeneralService), i0.ɵɵdirectiveInject(i3.ChatService), i0.ɵɵdirectiveInject(i4.AuthService), i0.ɵɵdirectiveInject(i5.UserService), i0.ɵɵdirectiveInject(i6.TeacherApplicationService), i0.ɵɵdirectiveInject(i7.StudentTrialService), i0.ɵɵdirectiveInject(i8.Router), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i9.Location), i0.ɵɵdirectiveInject(i8.ActivatedRoute));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      viewQuery: function LoginComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.lottieSignupEl = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 0,\n      consts: [[\"lottieSignupEl\", \"\"], [\"signupForm\", \"ngForm\"], [\"emailInput\", \"ngModel\"], [\"passwordInput\", \"ngModel\"], [\"templateName\", \"\"], [\"loginForm\", \"ngForm\"], [\"forgotForm\", \"ngForm\"], [\"emailInputForgot\", \"ngModel\"], [\"id\", \"container\", 1, \"login-container\", \"w-100\", 3, \"ngClass\"], [1, \"form-container\", \"sign-up-container\"], [4, \"ngIf\"], [1, \"auth-form\", \"mx-3\", \"sm:mx-5\", \"md:mx-6\", \"lg:mx-7\", 3, \"hidden\"], [3, \"hidden\"], [1, \"surface-section\", \"px-4\", \"py-1\", \"md:px-2\", \"lg:px-2\", \"lg:mt-8\", \"text-center\"], [1, \"text-700\", \"mb-6\"], [1, \"mb-4\", \"font-bold\", \"text-2xl\"], [1, \"text-primary\", \"line-height-2\"], [1, \"form-container\", \"sign-in-container\"], [1, \"relative\", \"h-full\"], [1, \"title\", \"my-3\", \"md:my-4\", \"pb-3\", \"mx-3\", \"sm:mx-5\", \"md:mx-6\", \"lg:mx-7\", \"align-items-start\"], [1, \"text-center\", \"sm:text-left\"], [\"class\", \"auth-form  flex flex-column mx-3 sm:mx-5 md:mx-6 lg:mx-7\", 4, \"ngIf\"], [1, \"overlay-container\"], [1, \"overlay\"], [1, \"overlay-panel\", \"overlay-left\", \"left-side-content\", \"justify-content-between\", \"gap-2\", \"py-5\"], [1, \"logo\", \"my-3\", \"md:my-0\"], [\"href\", \"https://mylingotrip.com\", \"target\", \"_blank\"], [\"src\", \"/assets/icons/logo/mlt_logo-new.svg\", \"width\", \"100\"], [\"pButton\", \"\", \"type\", \"button\", 1, \"p-button-outlined\", \"auth-btn\", 3, \"click\", \"label\"], [1, \"title\", \"text-center\", \"line-height-1\"], [1, \"m-0\"], [1, \"fl-typo\", \"s12-16\", \"cursor-pointer\", \"hover:underline\", \"mt-2\", \"block\", 3, \"click\"], [1, \"overlay-panel\", \"overlay-right\", \"left-side-content\", \"justify-content-between\", \"gap-2\", \"py-5\"], [1, \"flex\", \"gap-3\", \"flex-column\"], [1, \"cursor-pointer\", \"hover:underline\", 3, \"click\"], [1, \"h-17rem\", \"md:h-auto\"], [1, \"abs-centered\"], [3, \"scale\"], [1, \"title\", \"my-3\", \"sm:my-2\", \"md:my-4\", \"text-center\", \"sm:text-left\"], [1, \"grid\"], [1, \"col-6\"], [1, \"checkbox-bg\", \"student-bg\"], [\"name\", \"group1\", \"value\", \"student\", \"label\", \"Register as Student\", \"inputId\", \"sf\", 1, \"teacher-checkbox\", 3, \"onChange\", \"ngModelChange\", \"ngModel\"], [1, \"checkbox-bg\", \"teacher-bg\"], [\"styleClass\", \"teacher-checkbox-inner\", \"name\", \"group1\", \"value\", \"teacher\", \"label\", \"Register as Teacher\", \"inputId\", \"ny\", 1, \"teacher-checkbox\", 3, \"onChange\", \"ngModelChange\", \"ngModel\"], [\"autocomplete\", \"off\", 3, \"ngSubmit\"], [1, \"input-field\"], [1, \"input-element-title\"], [\"name\", \"email\", \"ngModel\", \"\", \"type\", \"email\", \"placeholder\", \"Email\", \"required\", \"\", \"email\", \"\", \"autocomplete\", \"off\", 1, \"input-element\"], [\"class\", \"input-error\", 4, \"ngIf\"], [1, \"p-input-icon-right\", 2, \"width\", \"100%\"], [1, \"pi\", 2, \"margin-top\", \"-2px\", 3, \"click\", \"ngClass\"], [\"name\", \"password\", \"placeholder\", \"Password\", \"required\", \"\", \"autocomplete\", \"off\", \"pattern\", \"^(?=.*[A-Z])(?=.*[0-9]).*$\", 1, \"input-element\", 3, \"ngModelChange\", \"ngModel\", \"type\"], [4, \"ngIf\", \"ngIfElse\"], [\"name\", \"confirm\", \"ngModel\", \"\", \"type\", \"password\", \"placeholder\", \"Confirm Password\", \"required\", \"\", \"autocomplete\", \"off\", \"pattern\", \"^(?=.*[A-Z])(?=.*[0-9]).*$\", 1, \"input-element\", 3, \"type\"], [1, \"review-section\", \"flex\", \"align-items-center\", \"gap-1\", 2, \"font-size\", \"15px\", \"margin\", \"20px 0\", \"font-weight\", \"bold\"], [\"id\", \"termsCheckbox\", \"label\", \"I have read and agree to the website \", \"name\", \"groupname\", \"binary\", \"true\", 1, \"terms-checkbox\", 3, \"ngModelChange\", \"ngModel\", \"value\"], [\"href\", \"https://mylingotrip.com/terms-conditions/\", \"target\", \"_blank\", 2, \"color\", \"black\", \"font-size\", \"16px\"], [1, \"grid\", \"align-items-center\", \"justify-content-center\", \"w-full\", \"mx-auto\", \"mt-3\"], [\"siteKey\", \"6LdFlA4pAAAAAJzrvvdou58XS_zQQ94z0lwxMv8i\", 3, \"resolved\"], [\"class\", \"grid justify-content-center mt-3\", 4, \"ngIf\"], [1, \"input-error\"], [2, \"font-size\", \"12px\", \"margin-top\", \"10px\"], [1, \"grid\", \"justify-content-center\", \"mt-3\"], [1, \"sm:col-7\"], [\"pButton\", \"\", \"type\", \"submit\", \"iconPos\", \"right\", 1, \"auth-btn-right\", \"teacher-button\", \"disabled-link\", 3, \"label\", \"icon\", \"ngClass\"], [1, \"auth-form\", \"flex\", \"flex-column\", \"mx-3\", \"sm:mx-5\", \"md:mx-6\", \"lg:mx-7\"], [3, \"submit\"], [\"name\", \"email\", \"ngModel\", \"\", \"type\", \"email\", \"placeholder\", \"Email\", \"required\", \"\", \"email\", \"\", \"type\", \"text\", 1, \"input-element\"], [\"name\", \"password\", \"ngModel\", \"\", \"placeholder\", \"Password\", \"required\", \"\", 1, \"input-element\", 3, \"type\"], [1, \"input-field\", \"pt-2\"], [\"pButton\", \"\", \"type\", \"submit\", \"styleClass\", \"bg-primary\", \"label\", \"Sign In\", 1, \"sign-in-btn\", \"hvr-glow\"], [\"class\", \"alternative\", 4, \"ngIf\"], [1, \"alternative\", 3, \"click\"], [1, \"alternative-btn\"], [3, \"submit\", 4, \"ngIf\"], [1, \"alternative\"], [1, \"alternative-btn\", 3, \"click\"], [1, \"flex\", \"mt-3\"], [\"pButton\", \"\", \"type\", \"submit\", \"styleClass\", \"bg-primary\", \"label\", \"Submit\", 1, \"sign-in-btn\", \"hvr-glow\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, LoginComponent_Defer_0_Template, 43, 11)(1, LoginComponent_DeferPlaceholder_1_Template, 3, 1);\n          i0.ɵɵdefer(2, 0, LoginComponent_Defer_2_DepsFn, null, 1);\n          i0.ɵɵdeferOnViewport(0, -1);\n        }\n      },\n      dependencies: [CommonModule, MatProgressSpinnerModule, ButtonModule, CheckboxModule, FormsModule, ReactiveFormsModule, LoaderComponent, RecaptchaModule],\n      styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}@media screen and (max-width: 1024px){  .app-content{margin-top:0!important}}  .app-content.auth-route{margin-top:0!important;margin-bottom:0!important}.title[_ngcontent-%COMP%]{font-size:clamp(1.5rem,.47vw + 1.41rem,1.88rem)!important;text-align:center}@media only screen and (min-width: 768px){.title[_ngcontent-%COMP%]{text-align:left}}.auth[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;border-radius:10px;overflow:hidden;width:100%}.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]{width:80%;display:flex;justify-content:center;align-items:center}.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:100px}@media screen and (max-width: 1366px){.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]{width:100%}}@media screen and (max-width: 1024px){.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]{position:relative;flex-direction:column}}@media screen and (max-width: 768px){.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]{flex-direction:column-reverse}.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .logo[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:80px}}.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .left-side[_ngcontent-%COMP%]{height:500px;width:42%;border-top-left-radius:28px;border-bottom-left-radius:28px;background-image:linear-gradient(179deg,#9baaff -6%,#152caf 106%);display:flex;align-items:center;flex-direction:column;color:var(--white);position:relative;left:0}.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .left-side.login-is-open[_ngcontent-%COMP%]{background:linear-gradient(#927ffa,#5a5fe9)}.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .left-side.has-animated[_ngcontent-%COMP%]{transition:all .3s linear}@media screen and (max-width: 1024px){.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .left-side[_ngcontent-%COMP%]{flex-direction:column-reverse;align-items:center;justify-content:flex-start;height:-moz-fit-content;height:fit-content;position:relative;border-radius:0;bottom:0;width:100%}}.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .left-side[_ngcontent-%COMP%]   .left-side-content[_ngcontent-%COMP%]{height:100%;flex-direction:column;display:flex;justify-content:space-between;padding-top:32px;padding-bottom:40px;align-items:center;font-size:15px;font-weight:400;font-stretch:normal;font-style:normal;line-height:normal;letter-spacing:normal;text-align:center}.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .left-side[_ngcontent-%COMP%]   .left-side-content[_ngcontent-%COMP%]   .subtitle[_ngcontent-%COMP%]{font-size:16px}.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%]{height:500px;width:60%;border-top-right-radius:28px;border-bottom-right-radius:28px;color:var(--main-color);background-color:#fff;position:relative}.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .right-side.has-animated[_ngcontent-%COMP%]{transition:all .3s linear}@media screen and (max-width: 768px){.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%]{width:90%;height:auto}}@media screen and (min-width: 1024px){.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%]{right:0}}.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%]   .right-side-content[_ngcontent-%COMP%]{position:relative;width:100%}@media screen and (max-width: 768px){.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%]   .right-side-content[_ngcontent-%COMP%]{margin-top:30px;width:90%}}.auth[_ngcontent-%COMP%]   .auth-container[_ngcontent-%COMP%]   .right-side[_ngcontent-%COMP%]   .right-side-content[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{font-size:30px}.input-field[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:30px}.input-field[_ngcontent-%COMP%]{margin-top:.75rem;font-size:1.125rem;box-sizing:border-box}.input-field[_ngcontent-%COMP%]   .input-element-title[_ngcontent-%COMP%]{color:var(--main-color);font-size:clamp(.88rem,.16vw + .84rem,1rem);font-weight:700;font-stretch:normal;font-style:normal;line-height:normal;letter-spacing:normal;text-align:left}.input-field[_ngcontent-%COMP%]   .input-element[_ngcontent-%COMP%]{margin-top:10px;color:var(--main-color);width:100%;box-sizing:border-box;border:1px solid var(--my-gray-2);border-radius:8px;padding:11px;font-size:18px}.checkbox-bg[_ngcontent-%COMP%]{background-size:cover;background-repeat:no-repeat;background-position:center;height:44px;align-items:center;display:flex;justify-content:center;border-radius:8px}.teacher-bg[_ngcontent-%COMP%]{background:url(/assets/images/auth-btn-reg-teacher.png) no-repeat center center;background-size:cover}.student-bg[_ngcontent-%COMP%]{background:url(/assets/images/auth-btn-reg-student.png) no-repeat center center;background-size:cover}[_nghost-%COMP%]  .terms-checkbox .p-checkbox{width:18px;height:18px}[_nghost-%COMP%]  .terms-checkbox .p-checkbox-label{font-size:16px;cursor:pointer}@media screen and (max-width: 768px){[_nghost-%COMP%]  .terms-checkbox .p-checkbox-label{font-size:14px}}[_nghost-%COMP%]  .terms-checkbox .p-checkbox .p-checkbox-box.p-highlight .p-checkbox-icon.pi-check:before{top:1px;left:-5px}[_nghost-%COMP%]  .terms-checkbox .p-checkbox .p-checkbox-box{border-radius:50px}[_nghost-%COMP%]  .terms-checkbox .p-checkbox .p-checkbox-box.p-highlight{border-radius:50px}[_nghost-%COMP%]  .terms-checkbox .p-checkbox .p-checkbox-box .p-checkbox-icon{transform:scale(1.3)}[_nghost-%COMP%]  .terms-checkbox .p-component .p-checkbox-box{width:18px!important;height:18px!important}[_nghost-%COMP%]  .teacher-checkbox{height:32px;color:#fff}@media screen and (max-width: 768px){[_nghost-%COMP%]  .teacher-checkbox{padding-left:6px}}[_nghost-%COMP%]  .teacher-checkbox .p-checkbox{width:28px;height:28px}[_nghost-%COMP%]  .teacher-checkbox .p-checkbox-label{font-size:16px;cursor:pointer}@media screen and (max-width: 768px){[_nghost-%COMP%]  .teacher-checkbox .p-checkbox-label{font-size:14px}}[_nghost-%COMP%]  .teacher-checkbox .p-checkbox .p-checkbox-box.p-highlight .p-checkbox-icon.pi-check:before{top:0;left:-3px}[_nghost-%COMP%]  .teacher-checkbox .p-checkbox .p-checkbox-box{background:transparent;border:1px solid #ffffff;border-radius:50px}[_nghost-%COMP%]  .teacher-checkbox .p-checkbox .p-checkbox-box.p-highlight{background:transparent;border-color:#fff;border-radius:50px}[_nghost-%COMP%]  .teacher-checkbox .p-checkbox .p-checkbox-box .p-checkbox-icon{transform:scale(2.5)}[_nghost-%COMP%]  .teacher-checkbox .p-component .p-checkbox-box{width:25px!important;height:25px!important}.auth-btn[_ngcontent-%COMP%]{border:1px solid white;border-radius:10px;padding:10px;font-size:20px;width:250px;margin:0 auto;cursor:pointer;transition:all .2s ease-in;box-sizing:border-box;white-space:normal;height:50px;color:#fff}.auth-btn[_ngcontent-%COMP%]:hover{background:#fff!important;color:#746ef1!important}.sign-in-btn.p-button[_ngcontent-%COMP%]{padding-right:60px;padding-left:60px;border-radius:8px;background:#2e3d90}.p-button.teacher-bg[_ngcontent-%COMP%]:enabled:hover{background:url(/assets/images/auth-btn-reg-teacher.png)!important}.p-button.student-bg[_ngcontent-%COMP%]:enabled:hover{background:url(/assets/images/auth-btn-reg-student.png) no-repeat center center;background-size:cover}.auth-btn-right[_ngcontent-%COMP%]{width:100%;color:#fff;border-radius:8px;font-size:clamp(1rem,.16vw + .97rem,1.13rem)}.auth-btn-right[_ngcontent-%COMP%]   .teacher-bg[_ngcontent-%COMP%]{background:url(/assets/images/auth-btn-reg-teacher.png)!important}.auth-btn[_ngcontent-%COMP%]:hover{background:#fff;color:var(--main-color)}.alternative[_ngcontent-%COMP%]{font-size:clamp(.88rem,.16vw + .84rem,1rem);margin-top:15px;display:flex}.alternative-btn[_ngcontent-%COMP%]{font-weight:700;cursor:pointer;white-space:break-spaces}.alternative-btn[_ngcontent-%COMP%]:hover{text-decoration:underline}.spinner[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);-ms-transform:translate(-50%,-50%);-webkit-transform:translate(-50%,-50%)}.register-success[_ngcontent-%COMP%]{color:var(--light-purple);font-weight:700;display:flex;align-items:center;font-size:16px;padding:15px 0}.register-success[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:30px;margin-right:15px}.form-container[_ngcontent-%COMP%]{background-color:#fff;top:0;padding-bottom:1rem}@media only screen and (min-width: 768px){.form-container[_ngcontent-%COMP%]{height:100%;transition:all .6s ease-in-out;display:flex;align-items:center;justify-content:start;flex-direction:column;position:absolute;padding-bottom:0}}.sign-up-container[_ngcontent-%COMP%]{left:0;width:100%;z-index:2;display:block}@media only screen and (min-width: 768px){.sign-up-container[_ngcontent-%COMP%]{display:block;width:60%}}.login-container.right-panel-active[_ngcontent-%COMP%]   .sign-up-container[_ngcontent-%COMP%]{display:none}@media only screen and (min-width: 768px){.login-container.right-panel-active[_ngcontent-%COMP%]   .sign-up-container[_ngcontent-%COMP%]{display:block;transform:translate(100%)}}.sign-in-container[_ngcontent-%COMP%]{opacity:0;z-index:1;display:none}@media only screen and (min-width: 768px){.sign-in-container[_ngcontent-%COMP%]{display:block;left:-20%;width:60%}}.login-container.right-panel-active[_ngcontent-%COMP%]   .sign-in-container[_ngcontent-%COMP%]{opacity:1;z-index:5;display:block}@media only screen and (min-width: 768px){.login-container.right-panel-active[_ngcontent-%COMP%]   .sign-in-container[_ngcontent-%COMP%]{transform:translate(100%)}}@keyframes _ngcontent-%COMP%_show{0%,49.99%{opacity:0;z-index:1}50%,to{opacity:1;z-index:5}}.overlay-container[_ngcontent-%COMP%]{position:relative;top:0;width:100%;height:300px;overflow:hidden;transition:transform .6s ease-in-out;z-index:100}@media only screen and (min-width: 768px){.overlay-container[_ngcontent-%COMP%]{position:absolute;top:0;left:60%;width:50%;height:100%;overflow:hidden;transition:transform .6s ease-in-out}}@media only screen and (min-width: 768px){.login-container.right-panel-active[_ngcontent-%COMP%]   .overlay-container[_ngcontent-%COMP%]{transform:translate(-150%);left:60%;width:40%}}.login-container.right-panel-active[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]{background-image:linear-gradient(180deg,#8a9af5,#8091ef 10%,#7786e9,#6d7de3,#6071dc,#5766d6,#4e5dd0 65%,#4454ca 77%,#374ac3 88%,#2a40bb)}.overlay[_ngcontent-%COMP%]{background-image:linear-gradient(180deg,#8579f6,#8075f5 10%,#7d72f3,#776ef2,#716af0,#6c68ee,#6b68ed 65%,#6565ec 77%,#6163ea 88%,#595ee8);background-repeat:no-repeat;background-size:cover;background-position:0 0;color:#fff;position:relative;left:-100%;height:100%;width:200%;transform:translate(0);transition:transform .6s ease-in-out}.login-container.right-panel-active[_ngcontent-%COMP%]   .overlay[_ngcontent-%COMP%]{transform:translate(50%)}.overlay-panel[_ngcontent-%COMP%]{box-sizing:border-box;position:absolute;display:flex;align-items:center;justify-content:center;flex-direction:column;text-align:center;top:0;height:100%;width:50%;transform:translate(0);transition:transform .6s ease-in-out}@media only screen and (min-width: 768px){.overlay-panel[_ngcontent-%COMP%]{width:40%}}.overlay-left[_ngcontent-%COMP%]{display:none;transform:translate(-20%)}@media only screen and (min-width: 768px){.overlay-left[_ngcontent-%COMP%]{display:flex;transform:unset;width:60%}}.login-container.right-panel-active[_ngcontent-%COMP%]   .overlay-left[_ngcontent-%COMP%]{position:relative;display:block;display:flex;transform:unset;width:50%}.overlay-right[_ngcontent-%COMP%]{left:50%;right:0;transform:translate(0)}@media only screen and (min-width: 768px){.overlay-right[_ngcontent-%COMP%]{left:initial;right:0;transform:translate(-25%)}}.login-container.right-panel-active[_ngcontent-%COMP%]   .overlay-right[_ngcontent-%COMP%]{transform:translate(20%)}\"],\n      data: {\n        animation: [slideUpDownAnimation, slideInOut]\n      }\n    });\n  }\n  return LoginComponent;\n})();", "map": {"version": 3, "names": ["CommonModule", "DOCUMENT", "signal", "FormsModule", "ReactiveFormsModule", "UntypedFormControl", "Validators", "MatProgressSpinnerModule", "RecaptchaModule", "ButtonModule", "CheckboxModule", "fork<PERSON><PERSON>n", "of", "switchMap", "take", "UserRole", "UserStatus", "slideInOut", "slideUpDownAnimation", "LoaderComponent", "SubSink", "i13", "RecaptchaComponent", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵtext", "ctx_r2", "registerButtonLabel", "isRegisterLoading", "ɵɵpureFunction3", "_c2", "register<PERSON><PERSON><PERSON>er", "ɵɵelementContainer", "ɵɵlistener", "LoginComponent_Defer_0_ng_container_12_Template_p_checkbox_onChange_6_listener", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "onChange", "ɵɵtwoWayListener", "LoginComponent_Defer_0_ng_container_12_Template_p_checkbox_ngModelChange_6_listener", "$event", "ɵɵtwoWayBindingSet", "selectedR<PERSON>", "LoginComponent_Defer_0_ng_container_12_Template_p_checkbox_onChange_9_listener", "LoginComponent_Defer_0_ng_container_12_Template_p_checkbox_ngModelChange_9_listener", "LoginComponent_Defer_0_ng_container_12_Template_form_ngSubmit_10_listener", "signupForm_r4", "ɵɵreference", "onSignup", "ɵɵtemplate", "LoginComponent_Defer_0_ng_container_12_div_17_Template", "LoginComponent_Defer_0_ng_container_12_Template_i_click_22_listener", "togglemyPasswordFieldType", "LoginComponent_Defer_0_ng_container_12_Template_input_ngModelChange_23_listener", "password", "LoginComponent_Defer_0_ng_container_12_div_25_Template", "LoginComponent_Defer_0_ng_container_12_div_26_Template", "LoginComponent_Defer_0_ng_container_12_ng_template_27_Template", "ɵɵtemplateRefExtractor", "LoginComponent_Defer_0_ng_container_12_Template_i_click_33_listener", "LoginComponent_Defer_0_ng_container_12_div_36_Template", "LoginComponent_Defer_0_ng_container_12_Template_p_checkbox_ngModelChange_38_listener", "termsAgreed", "LoginComponent_Defer_0_ng_container_12_Template_re_captcha_resolved_43_listener", "captchaResolvedEvent", "LoginComponent_Defer_0_ng_container_12_div_44_Template", "LoginComponent_Defer_0_ng_container_12_ng_container_45_Template", "ɵɵtwoWayProperty", "emailError", "getKeyValueFieldType", "passwordInput_r5", "valid", "invalid", "templateName_r6", "confirmError", "length", "LoginComponent_Defer_0_div_19_div_20_Template_div_click_2_listener", "_r9", "switchView", "LoginComponent_Defer_0_div_19_form_25_Template_form_submit_0_listener", "_r10", "forgotForm_r11", "onForgot", "LoginComponent_Defer_0_div_19_form_25_div_9_Template", "LoginComponent_Defer_0_div_19_Template_form_submit_3_listener", "_r7", "loginForm_r8", "onLogin", "LoginComponent_Defer_0_div_19_div_10_Template", "LoginComponent_Defer_0_div_19_Template_i_click_15_listener", "LoginComponent_Defer_0_div_19_div_20_Template", "LoginComponent_Defer_0_div_19_Template_div_click_21_listener", "showForgotPassword", "LoginComponent_Defer_0_div_19_form_25_Template", "is1024", "LoginComponent_Defer_0_ng_container_2_Template", "LoginComponent_Defer_0_ng_container_12_Template", "LoginComponent_Defer_0_ng_container_18_Template", "LoginComponent_Defer_0_div_19_Template", "LoginComponent_Defer_0_Template_button_click_26_listener", "_r1", "handleSwitchViewClick", "LoginComponent_Defer_0_Template_a_click_31_listener", "LoginComponent_Defer_0_Template_button_click_38_listener", "LoginComponent_Defer_0_Template_a_click_40_listener", "ɵɵpureFunction1", "_c1", "loginIsOpen", "isLoading", "showSuccessRegistrationMsg", "LoginComponent", "constructor", "toastService", "generalService", "chatService", "authService", "userService", "teacherApplicationService", "studentTrialService", "router", "zone", "document", "renderer", "location", "activatedRoute", "subs", "signUpIsOpen", "is768", "is1366", "is1024up", "isBig", "ismyTextFieldType", "email", "confirm", "passwordCheck", "passwordFormControl", "required", "pattern", "isDisabled", "hasAnimated", "captchaResolved", "lottieSignupEl", "mwindow", "defaultView", "console", "log", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "returnUrl", "snapshot", "queryParams", "undefined", "url", "includes", "add", "getFireBaseUser", "subscribe", "res", "signOutFromFirebase", "getAuthStatusListener", "authStatus", "deviceKind", "pipe", "w1024up", "registrationSuccessMessageListener", "executeRecaptchaV3", "loadScript", "node", "createElement", "setAttribute", "src", "type", "async", "charset", "getElementsByTagName", "append<PERSON><PERSON><PERSON>", "ngAfterViewInit", "ngOnDestroy", "unsubscribe", "showRegistrationSuccessfullMessage", "addLottiePlayerForSignupCompleted", "checkBoxChange", "args", "checked", "animate", "changeHasAnimated", "elem1", "getElementById", "elem2", "style", "left", "borderRadius", "right", "createUrlTree", "relativeTo", "toString", "go", "push", "detectRole", "removeSpaces", "control", "value", "replace", "setValue", "setValidatorsForTrimming", "form", "controlName", "controls", "trim", "setValidators", "updateValueAndValidity", "validEmail", "loginForm", "setShowToastmessage", "severity", "summary", "detail", "expiresInDuration", "token", "tokenInfo", "result1$", "login", "response", "getDecodedAccessToken", "setToken", "setAuthTimer", "userId", "id", "user", "role", "TEACHER", "requestDataFromMultipleSources", "authStatusListener", "next", "now", "Date", "expirationDate", "getTime", "saveAuthData", "requestStudentTrialData", "u", "availability", "off", "setTrialRequested", "setIsAuthenticated", "toLowerCase", "status", "ACTIVE", "navigate", "navigateByUrl", "valuesArray", "Object", "values", "allFalse", "every", "getTeacherStatusNavigationUrl", "error", "forgot", "reset", "endPointType", "confirmPassword", "invalidInputs", "hasOwnProperty", "detailMessage", "slice", "createUser", "setShowRegistrationSuccessfullMessage", "onTeacherButtonClicked", "onStudentButtonClicked", "match", "touched", "key", "response1", "getUserAvailability", "response2", "getTeacherStatus", "response3", "getTeacherApplicationStepsStatus", "getStudentHasTrialRequest", "loadLottiePlayer", "_this", "successMessageDelay", "lottiePlayerCDN", "setTimeout", "container", "classList", "toggle", "contains", "captchaResponse", "set", "el", "lottiePlayer", "nativeElement", "innerHTML", "handleFirebaseUserResponse", "firebaseLoggedIn", "handleFirebaseError", "alert", "existsFirebaseUserEmail", "checkFirebaseUserExistsByEmail", "then", "exists", "signInFirebaseUser", "createFirebaseUser", "catch", "_", "ɵɵdirectiveInject", "i1", "ToastService", "i2", "GeneralService", "i3", "ChatService", "i4", "AuthService", "i5", "UserService", "i6", "TeacherApplicationService", "i7", "StudentTrialService", "i8", "Router", "NgZone", "Renderer2", "i9", "Location", "ActivatedRoute", "_2", "selectors", "viewQuery", "LoginComponent_Query", "rf", "ctx", "LoginComponent_Defer_0_Template", "LoginComponent_DeferPlaceholder_1_Template", "ɵɵdefer", "LoginComponent_Defer_2_DepsFn", "ɵɵdeferOnViewport", "styles", "data", "animation"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\auth\\login\\login.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\auth\\login\\login.component.html"], "sourcesContent": ["import { CommonModule, DOCUMENT, Location } from '@angular/common';\r\nimport { Component, ElementRef, Inject, NgZone, OnDestroy, OnInit, Renderer2, signal, ViewChild } from '@angular/core';\r\nimport { AbstractControl, FormsModule, NgForm, ReactiveFormsModule, UntypedFormControl, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { RecaptchaModule, ReCaptchaV3Service } from 'ng-recaptcha';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { CheckboxModule } from 'primeng/checkbox';\r\nimport { forkJoin, Observable, of } from 'rxjs';\r\nimport { switchMap, take } from 'rxjs/operators';\r\nimport { User, UserRole, UserStatus } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { ChatService } from 'src/app/core/services/chat.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { StudentTrialService } from 'src/app/core/services/student-trial.service';\r\nimport { TeacherApplicationService } from 'src/app/core/services/teacher-application.service';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { UserService } from 'src/app/core/services/user.service';\r\nimport { slideInOut, slideUpDownAnimation } from 'src/app/helpers/my-animations';\r\nimport { LoaderComponent } from 'src/app/shared/loader/loader.component';\r\nimport { SubSink } from 'subsink';\r\n\r\ntype TextFieldType = \"email\" | \"password\" | \"confirm\";\r\n@Component({\r\n  imports: [\r\n    CommonModule,\r\n    MatProgressSpinnerModule,\r\n    ButtonModule,\r\n    CheckboxModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    LoaderComponent,\r\n    RecaptchaModule,\r\n  ],\r\n  selector: 'app-login',\r\n  templateUrl: './login.component.html',\r\n  styleUrls: ['./login.component.scss'],\r\n  animations: [slideUpDownAnimation, slideInOut],\r\n  standalone: true,\r\n})\r\nexport class LoginComponent implements OnInit, OnDestroy {\r\n  private subs = new SubSink();\r\n  isLoading = false;\r\n  isRegisterLoading = false;\r\n  termsAgreed = false;\r\n  imagePreview?: string;\r\n  user?: User;\r\n  userId?: string | null;\r\n  loginIsOpen: boolean = true;\r\n  signUpIsOpen: boolean = false;\r\n  registerAsTeacher: boolean = false;\r\n  is768: boolean = false;\r\n  is1024: boolean = false;\r\n  is1366: boolean = false;\r\n  is1024up: boolean = false;\r\n  isBig: boolean = false;\r\n  emailError: boolean = false;\r\n  confirmError: boolean = false;\r\n  showSuccessRegistrationMsg: boolean = false;\r\n  showForgotPassword: boolean = false;\r\n  selectedRole: string[] = [];\r\n  registerButtonLabel = 'Register as Student';\r\n  returnUrl!: string;\r\n  ismyTextFieldType: Record<TextFieldType, boolean> = {\r\n    email: false,\r\n    password: false,\r\n    confirm: false,\r\n  };\r\n  passwordCheck: string = '';\r\n  passwordFormControl = new UntypedFormControl('', [\r\n    Validators.required,\r\n    Validators.pattern('^(?=.*[A-Z])(?=.*[0-9]).*$')\r\n  ]);\r\n  isDisabled = true;\r\n  hasAnimated = true;\r\n  private mwindow?: Window;\r\n  captchaResolved = signal(false);\r\n  @ViewChild('lottieSignupEl', { static: false }) lottieSignupEl: ElementRef = {} as ElementRef;\r\n  constructor(\r\n    private toastService: ToastService,\r\n    private generalService: GeneralService,\r\n    private chatService: ChatService,\r\n    public authService: AuthService,\r\n    public userService: UserService,\r\n    public teacherApplicationService: TeacherApplicationService,\r\n    public studentTrialService: StudentTrialService,\r\n    private router: Router,\r\n    private zone: NgZone,\r\n    @Inject(DOCUMENT) private document: Document,\r\n    private renderer: Renderer2,\r\n    private location: Location,\r\n    private activatedRoute: ActivatedRoute,\r\n  ) {\r\n    this.mwindow = <any>this.document.defaultView;\r\n    console.log(activatedRoute.firstChild)\r\n    // try to switchView on register/login route detection\r\n\r\n  }\r\n\r\n  ngOnInit() {\r\n\r\n    // get return url from route parameters or default to ‘/’\r\n    this.returnUrl = this.activatedRoute.snapshot.queryParams['returnUrl'] || undefined;\r\n    if (this.router.url.includes('login')) {\r\n      this.loginIsOpen = true;\r\n    }\r\n    if (this.router.url.includes('register')) {\r\n      this.loginIsOpen = false;\r\n    }\r\n\r\n    this.subs.add(this.chatService.getFireBaseUser().subscribe((res) => {\r\n      if (res) {\r\n        this.chatService.signOutFromFirebase();\r\n      }\r\n    }));\r\n\r\n    this.subs.add(this.authService.getAuthStatusListener().subscribe(authStatus => {\r\n      if (authStatus) {\r\n        // this.isLoading = false;\r\n      }\r\n    }));\r\n    this.subs.add(this.generalService.deviceKind.pipe(take(1)).subscribe(res => {\r\n      if (res) {\r\n        this.is1024 = res.is1024;\r\n        this.is1366 = res.is1366;\r\n        this.is768 = res.is768;\r\n        this.isBig = res.isBig;\r\n        this.is1024up = res.w1024up!;\r\n        if (this.is1024up) {\r\n\r\n          // this.renderer.addClass(this.document.getElementsByClassName('app')[0], 'h-full');\r\n        }\r\n        console.log(res);\r\n      }\r\n    }));\r\n    this.registrationSuccessMessageListener();\r\n    // this.renderer.addClass(this.document.body, 'gradient-moving-body');\r\n  }\r\n\r\n  public executeRecaptchaV3() {\r\n\r\n  }\r\n\r\n  public loadScript(url: string) {\r\n    console.log('preparing to load...')\r\n    let node = document.createElement('script');\r\n    node.setAttribute(\"id\", \"lottie-player\");\r\n    node.src = url;\r\n    node.type = 'text/javascript';\r\n    node.async = true;\r\n    node.charset = 'utf-8';\r\n    document.getElementsByTagName('head')[0].appendChild(node);\r\n  }\r\n  ngAfterViewInit() {\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n    // this.renderer.removeClass(this.document.body, 'height-screen');\r\n    // this.renderer.removeClass(this.document.getElementsByClassName('app')[0], 'h-full');\r\n  }\r\n\r\n  registrationSuccessMessageListener() {\r\n    this.subs.add(this.authService.showRegistrationSuccessfullMessage.subscribe(res => {\r\n      console.log(res);\r\n      this.showSuccessRegistrationMsg = res;\r\n      if (res) {\r\n        this.addLottiePlayerForSignupCompleted(this.lottieSignupEl);\r\n      }\r\n    }));\r\n  }\r\n\r\n  checkBoxChange(args: any) {\r\n    this.registerAsTeacher = args.checked\r\n  }\r\n\r\n  switchView(animate = false, changeHasAnimated = false) {\r\n    this.showSuccessRegistrationMsg = false;\r\n\r\n    if (changeHasAnimated) {\r\n      this.hasAnimated = changeHasAnimated;\r\n    }\r\n    if (this.isBig || this.is1366 || animate) {\r\n      if (this.loginIsOpen) {\r\n        if (this.is1024up) {\r\n\r\n          const elem1 = document.getElementById('left-side');\r\n          const elem2 = document.getElementById('right-side');\r\n\r\n          // const rect = elem2!.getBoundingClientRect();\r\n          // const parent = elem2!.parentElement;\r\n          // const leftOffset = elem2!.offsetLeft;\r\n          // const topOffset = elem1!.offsetTop;\r\n          // const leftWidth = elem2!.offsetWidth;\r\n          // const newLeft2 = rect.left - parent!.offsetLeft;\r\n\r\n          // const newLeft = newLeft2 + leftWidth;\r\n\r\n          // elem1!.style.left = newLeft - 2 + 'px';\r\n\r\n          document.getElementById('left-side')!.style.left = '470px';\r\n          document.getElementById('left-side')!.style.borderRadius = '0px 28px 28px 0';\r\n          document.getElementById('right-side')!.style.borderRadius = '28px 0px 0 28px';\r\n          document.getElementById('right-side')!.style.right = '270px';\r\n        }\r\n        const url = this.router.createUrlTree(['/auth/register'], { relativeTo: this.activatedRoute }).toString();\r\n        this.location.go(url);\r\n      } else {\r\n        if (this.is1024up) {\r\n          document.getElementById('left-side')!.style.left = '0';\r\n          document.getElementById('left-side')!.style.borderRadius = '28px 0px 0 28px';\r\n          document.getElementById('right-side')!.style.borderRadius = '0px 28px 28px 0';\r\n          document.getElementById('right-side')!.style.right = '0';\r\n        }\r\n        const url = this.router.createUrlTree(['/auth/login'], { relativeTo: this.activatedRoute }).toString();\r\n        this.location.go(url);\r\n      }\r\n    }\r\n    this.loginIsOpen = !this.loginIsOpen;\r\n    this.signUpIsOpen = !this.signUpIsOpen;\r\n  }\r\n\r\n  onChange() {\r\n    const selectedRole = this.selectedRole[this.selectedRole.length - 1];\r\n\r\n    this.selectedRole.length = 0;\r\n    this.selectedRole.push(selectedRole);\r\n    if (!selectedRole) {\r\n      this.selectedRole.length = 0;\r\n    }\r\n    this.detectRole();\r\n    this.isDisabled = false;\r\n  }\r\n\r\n  detectRole() {\r\n    if (this.selectedRole[0] === 'teacher') {\r\n      this.registerAsTeacher = true;\r\n      this.registerButtonLabel = 'Register as Teacher';\r\n    } else {\r\n      this.registerAsTeacher = false;\r\n      this.registerButtonLabel = 'Register as Student';\r\n    }\r\n  }\r\n\r\n  removeSpaces(control: AbstractControl) {\r\n    if (control && control.value && !control.value.replace(/\\s/g, '').length) {\r\n      control.setValue('');\r\n    }\r\n    return null;\r\n  }\r\n\r\n  setValidatorsForTrimming(form: NgForm, controlName: string) {\r\n    const control = form.controls[controlName];\r\n    control.setValue(control.value.trim());\r\n    control.setValidators([Validators.required, this.removeSpaces]);\r\n    control.updateValueAndValidity();\r\n  }\r\n\r\n  onLogin(form: NgForm) {\r\n    this.setValidatorsForTrimming(form, 'email');\r\n    this.setValidatorsForTrimming(form, 'password');\r\n    this.emailError = this.validEmail(form);\r\n    const loginForm = form.value;\r\n    console.log(form.value);\r\n    if (form.invalid) {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'warn',\r\n        summary: '',\r\n        detail: 'Please enter email and password to continue.'\r\n      });\r\n      return;\r\n    }\r\n\r\n    this.isLoading = true;\r\n    let expiresInDuration: number = 10800;\r\n    let token: string | null = null;\r\n    let tokenInfo: any = null;\r\n    const result1$ = this.subs.add(this.authService.login(form.value.email.trim(), form.value.password.trim())\r\n      .pipe(\r\n        switchMap((response: any) => {\r\n          token = response.token;\r\n          tokenInfo = this.authService.getDecodedAccessToken(token!); // decode token\r\n          this.authService.setToken(token!);\r\n          if (token) {\r\n            expiresInDuration = 10800;\r\n            // const expiresInDuration = tokenInfo.exp;\r\n            this.authService.setAuthTimer(expiresInDuration);\r\n            this.userId = response.id;\r\n            this.user = response;\r\n            if (tokenInfo.role === UserRole.TEACHER) {\r\n\r\n              return this.requestDataFromMultipleSources(response);\r\n              // return this.userService.getUserAvailability(response.id);\r\n            } else {\r\n              this.authService.authStatusListener.next(true);\r\n              const now = new Date();\r\n              const expirationDate = new Date(now.getTime() + expiresInDuration * 1000);\r\n              this.authService.saveAuthData(token!, expirationDate, this.userId!, this.user!, tokenInfo.role);\r\n              // this.router.navigate(['/dashboard']);\r\n              return (this.requestStudentTrialData());\r\n            }\r\n          } else {\r\n            return of(null);\r\n          }\r\n        }),\r\n      ).subscribe((res: any) => {\r\n        console.log(loginForm);\r\n        let u: User = {} as User;\r\n        // TODO: check if below is needed for Firebase integration\r\n        // this.existsFirebaseUserEmail(loginForm);\r\n        if (tokenInfo.role === UserRole.TEACHER) {\r\n          let u: User = { ...this.user, availability: res[0], off: res[1] } as User;\r\n        } else {\r\n\r\n          console.log(res);\r\n          // for student\r\n          this.studentTrialService.setTrialRequested(res[0]);\r\n          // this.user = this.user;\r\n        }\r\n        // this.user.role = 'trial'\r\n        this.authService.authStatusListener.next(true);\r\n        const now = new Date();\r\n        const expirationDate = new Date(now.getTime() + expiresInDuration * 1000);\r\n        this.authService.setIsAuthenticated(true);\r\n        this.authService.saveAuthData(token!, expirationDate, this.userId!, this.user!, tokenInfo.role);\r\n        this.isLoading = false;\r\n        // checking teacher application status steps if all are false (means the user hasnt started filling forms yet)\r\n\r\n        if (tokenInfo.role.toLowerCase() === UserRole.TEACHER.toLowerCase()) {\r\n          // teacher is active\r\n          if (this.user?.status.toLowerCase() === UserStatus.ACTIVE.toLowerCase()) {\r\n            if (this.returnUrl) {\r\n              this.router.navigate([this.returnUrl]);\r\n            } else {\r\n              this.router.navigateByUrl('/dashboard');\r\n            }\r\n            return false;\r\n          }\r\n          const valuesArray = Object.values(res[2]);\r\n          const allFalse = valuesArray.every(value => value === false);\r\n          console.log(allFalse);\r\n          if (allFalse) {\r\n            this.router.navigateByUrl('/teacher/info');\r\n            return false;\r\n          } else {\r\n            this.router.navigateByUrl(this.teacherApplicationService.getTeacherStatusNavigationUrl(res[1]));\r\n            return false;\r\n          }\r\n        }\r\n\r\n        // student\r\n        if (this.returnUrl) {\r\n          this.router.navigate([this.returnUrl]);\r\n        } else {\r\n          this.router.navigateByUrl('/dashboard');\r\n        }\r\n        return false;\r\n      }, error => {\r\n        this.isLoading = false;\r\n      }));\r\n\r\n\r\n  }\r\n\r\n  onForgot(form: NgForm) {\r\n    this.emailError = this.validEmail(form);\r\n    if (form.invalid) {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'warn',\r\n        summary: '',\r\n        detail: 'Please enter email to continue.'\r\n      });\r\n      return;\r\n    }\r\n\r\n    this.toastService.setShowToastmessage({\r\n      severity: 'success',\r\n      summary: '',\r\n      detail: 'Please check your email to reset your password.'\r\n    });\r\n    this.authService.forgot(form.value.email);\r\n    form.reset();\r\n  }\r\n\r\n  onSignup(form: NgForm) {\r\n    let endPointType = this.registerAsTeacher ? 'RegisterTeacher' : 'RegisterStudent';\r\n    this.emailError = this.validEmail(form);\r\n    this.confirmError = this.confirmPassword(form.value.password, form.value.confirm);\r\n\r\n    let invalidInputs = '';\r\n    for (const controlName in form.controls) {\r\n      if (form.controls.hasOwnProperty(controlName)) {\r\n        const control = form.controls[controlName];\r\n        if (control.invalid) {\r\n          invalidInputs += `${controlName}, `;\r\n        }\r\n      }\r\n    }\r\n\r\n    let detailMessage = 'Please check all required fields to continue. \\n\\n';\r\n    if (invalidInputs !== '') {\r\n      invalidInputs = invalidInputs.slice(0, -2); // Remove the trailing comma and space\r\n\r\n      if (invalidInputs !== '') {\r\n        detailMessage += ` Invalid input for the following fields: ${invalidInputs}`;\r\n      }\r\n    }\r\n    if (!this.termsAgreed) {\r\n      detailMessage += ` Please agree to our terms & conditions.`;\r\n    }\r\n    if (!this.captchaResolved()) {\r\n      detailMessage += ` Please resolve the captcha to continue.`;\r\n    }\r\n\r\n    if (form.invalid || !this.termsAgreed || this.confirmError || !this.captchaResolved()) {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'warn',\r\n        summary: '',\r\n        detail: detailMessage\r\n      });\r\n      return;\r\n    }\r\n    this.isRegisterLoading = true;\r\n    this.authService.createUser(form.value.email, form.value.password, endPointType)\r\n      .subscribe((res) => {\r\n        this.authService.setShowRegistrationSuccessfullMessage(true);\r\n        this.isRegisterLoading = false;\r\n      }, error => {\r\n        this.isRegisterLoading = false;\r\n        this.authService.authStatusListener.next(false);\r\n        form.reset();\r\n      });\r\n  }\r\n\r\n  public onTeacherButtonClicked(): void {\r\n    this.registerAsTeacher = true;\r\n  }\r\n\r\n  public onStudentButtonClicked(): void {\r\n    this.registerAsTeacher = false;\r\n  }\r\n\r\n  validEmail(form: NgForm) {\r\n    if (form.value.email) {\r\n      let validEmail = form.value.email.trim()\r\n        .toLowerCase()\r\n        .match(\r\n          /^\\S+@\\S+\\.\\S+$/\r\n        );\r\n      if (validEmail === null && form.touched) {\r\n        return true\r\n      }\r\n    }\r\n    return false;\r\n  };\r\n\r\n  confirmPassword(password: string, confirm: string) {\r\n    return password !== confirm\r\n  }\r\n\r\n  //TODO GOOGLE LOGIN\r\n  // public externalLogin = () => {\r\n  //   this.authService.signInWithGoogle()\r\n  //   .then(res => {\r\n  //     const user: SocialUser = { ...res };\r\n  //     console.log(user);\r\n  //   }, error => console.log(error))\r\n  // }\r\n\r\n  togglemyPasswordFieldType(key: TextFieldType): void {\r\n    this.ismyTextFieldType[key] = !this.ismyTextFieldType[key];\r\n  }\r\n\r\n  getKeyValueFieldType(key: TextFieldType): boolean {\r\n    return this.ismyTextFieldType[key];\r\n  }\r\n\r\n  private requestDataFromMultipleSources(response: any): Observable<any[]> {\r\n    let response1 = this.userService.getUserAvailability(response.id);\r\n    let response2 = this.authService.getTeacherStatus();\r\n    let response3 = this.teacherApplicationService.getTeacherApplicationStepsStatus();\r\n    // Observable.forkJoin (RxJS 5) changes to just forkJoin() in RxJS 6\r\n    return forkJoin([response1, response2, response3]);\r\n  }\r\n\r\n\r\n  private requestStudentTrialData(): Observable<any[]> {\r\n    let response2 = this.studentTrialService.getStudentHasTrialRequest();\r\n    // Observable.forkJoin (RxJS 5) changes to just forkJoin() in RxJS 6\r\n    return forkJoin([response2]);\r\n  }\r\n\r\n  /**\r\n   * Loads LottiePlayer library from the CDN and shows success registration message.\r\n   */\r\n  loadLottiePlayer() {\r\n    const _this = this;\r\n    const successMessageDelay = 10; // in milliseconds\r\n    const lottiePlayerCDN = '/assets/js/lottie-player.js';\r\n\r\n    setTimeout(() => {\r\n      var id = document.getElementById('lottie-player');\r\n      if (!id) {\r\n        _this.loadScript(lottiePlayerCDN);\r\n      }\r\n      _this.showSuccessRegistrationMsg = true;\r\n    }, successMessageDelay);\r\n  }\r\n\r\n  handleSwitchViewClick() {\r\n    const container = document.getElementById('container');\r\n    container!.classList.toggle(\"right-panel-active\");\r\n\r\n    if (!container!.classList.contains(\"right-panel-active\")) {\r\n\r\n      const url = this.router.createUrlTree(['/auth/register'], { relativeTo: this.activatedRoute }).toString();\r\n      this.location.go(url);\r\n    } else {\r\n\r\n      const url = this.router.createUrlTree(['/auth/login'], { relativeTo: this.activatedRoute }).toString();\r\n      this.location.go(url);\r\n    }\r\n  }\r\n\r\n  captchaResolvedEvent(captchaResponse: string) {\r\n    console.log(`Resolved captcha with response: ${captchaResponse}`);\r\n    this.captchaResolved.set(true);\r\n  }\r\n\r\n  private addLottiePlayerForSignupCompleted(el: ElementRef): void {\r\n    const lottiePlayer = `<lottie-player src=\"https://assets4.lottiefiles.com/packages/lf20_xwmj0hsk.json\"  \r\n    background=\"transparent\"  speed=\"1\"  style=\"width: 280px; height: 280px; margin:0 auto;\"  autoplay></lottie-player>`;\r\n    el.nativeElement.innerHTML = lottiePlayer;\r\n  }\r\n\r\n  private handleFirebaseUserResponse(response: any) {\r\n    if (response) {\r\n      this.authService.firebaseLoggedIn.set(true);\r\n      this.subs.add(this.chatService.getFireBaseUser().subscribe({\r\n        next: (user) => {\r\n        },\r\n        error: (error) => this.handleFirebaseError(error)\r\n      }));\r\n    }\r\n  }\r\n\r\n  private handleFirebaseError(error: any) {\r\n    console.error(error);\r\n    alert(error);\r\n  }\r\n\r\n  private existsFirebaseUserEmail(form: any) {\r\n    this.chatService.checkFirebaseUserExistsByEmail(form.email).then((exists) => {\r\n      if (exists) {\r\n        this.subs.add(this.chatService.signInFirebaseUser(form.email, form.password).subscribe({\r\n          next: (res) => this.handleFirebaseUserResponse(res),\r\n          error: (error) => this.handleFirebaseError(error)\r\n        }));\r\n      } else {\r\n        this.subs.add(this.chatService.createFirebaseUser(form.email, form.password).subscribe({\r\n          next: (res) => this.handleFirebaseUserResponse(res),\r\n          error: (error) => this.handleFirebaseError(error)\r\n        }));\r\n      }\r\n    }).catch((error) => this.handleFirebaseError(error));\r\n  }\r\n}\r\n", "<!-- \r\n  //TODO GOOGLE LOGIN\r\n  \r\n  <div class=\"card\">\r\n  <div class=\"card-body\">\r\n      <div>\r\n          <h2>External Providers</h2>\r\n          <div style=\"padding: 20px;\">\r\n              <button class=\"btn btn-info\" type=\"button\" (click)=\"externalLogin()\">Google</button>\r\n          </div>\r\n      </div>\r\n  </div>\r\n</div> -->\r\n\r\n\r\n@defer(on viewport) {\r\n\r\n<div class=\"login-container w-100\" id=\"container\" [ngClass]=\"{'right-panel-active': loginIsOpen}\">\r\n  <div class=\"form-container sign-up-container\">\r\n\r\n    <ng-container *ngIf=\"isLoading\">\r\n\r\n\r\n      <div class=\"h-17rem md:h-auto\">\r\n        <div class=\"abs-centered\">\r\n          <app-loader [scale]=\"1.6\"></app-loader>\r\n        </div>\r\n      </div>\r\n\r\n    </ng-container>\r\n    <div [hidden]=\"isLoading\" class=\"auth-form  mx-3 sm:mx-5 md:mx-6 lg:mx-7\">\r\n\r\n      <div [hidden]=\"!showSuccessRegistrationMsg\">\r\n        <div class=\"surface-section px-4 py-1 md:px-2 lg:px-2 lg:mt-8 text-center\">\r\n          <div class=\"text-700 mb-6\">\r\n\r\n            <div #lottieSignupEl></div>\r\n\r\n            <div class=\"mb-4 font-bold text-2xl\"><span class=\"text-primary line-height-2\">You have successfully\r\n                registered\r\n                an account!</span></div>\r\n            <!-- <p class=\"text-primary text-sm\">\r\n              Please check your email and confirm your address by\r\n              clicking the verification link we sent you.\r\n              If you can’t find the email, please check your spam folder.\r\n            </p> -->\r\n\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <ng-container *ngIf=\"!showSuccessRegistrationMsg;\">\r\n        <div class=\"title my-3 sm:my-2 md:my-4 text-center sm:text-left\">\r\n          Sign Up\r\n        </div>\r\n\r\n        <div class=\"grid\">\r\n          <div class=\"col-6\">\r\n            <div class=\"checkbox-bg student-bg\">\r\n              <p-checkbox class=\"teacher-checkbox\" (onChange)=\"onChange()\" name=\"group1\" value=\"student\"\r\n                label=\"Register as Student\" [(ngModel)]=\"selectedRole\" inputId=\"sf\"></p-checkbox>\r\n            </div>\r\n          </div>\r\n          <div class=\"col-6\">\r\n            <div class=\"checkbox-bg teacher-bg\">\r\n              <p-checkbox class=\"teacher-checkbox\" styleClass=\"teacher-checkbox-inner\" (onChange)=\"onChange()\"\r\n                name=\"group1\" value=\"teacher\" label=\"Register as Teacher\" [(ngModel)]=\"selectedRole\"\r\n                inputId=\"ny\"></p-checkbox>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n\r\n        <form (ngSubmit)=\"onSignup(signupForm)\" #signupForm=\"ngForm\" autocomplete=\"off\">\r\n          <div class=\"input-field\">\r\n            <div class=\"input-element-title\">Email * </div>\r\n            <input name=\"email\" ngModel type=\"email\" placeholder=\"Email\" #emailInput=\"ngModel\" required email\r\n              class=\"input-element\" autocomplete=\"off\" />\r\n            <div *ngIf=\"emailError\" class=\"input-error\">Email is not valid.</div>\r\n          </div>\r\n          <div class=\"input-field\">\r\n            <div class=\"input-element-title\">Password *</div>\r\n            <span class=\"p-input-icon-right\" style=\"width: 100%;\">\r\n              <i class=\"pi\" (click)=\"togglemyPasswordFieldType('password')\"\r\n                [ngClass]=\"getKeyValueFieldType('password')? 'pi-eye': 'pi-eye-slash'\" style=\"margin-top:-2px;\"></i>\r\n              <input name=\"password\" [(ngModel)]=\"password\"\r\n                [type]=\"getKeyValueFieldType('password') ? 'text' : 'password'\" placeholder=\"Password\" required\r\n                class=\"input-element\" #passwordInput=\"ngModel\" autocomplete=\"off\"\r\n                pattern=\"^(?=.*[A-Z])(?=.*[0-9]).*$\" />\r\n\r\n\r\n            </span>\r\n            <div *ngIf=\"passwordInput && passwordInput.valid\">\r\n              <div style=\"font-size:12px; margin-top:10px;\">Great job! Your password meets the requirements.</div>\r\n            </div>\r\n            <div *ngIf=\"passwordInput.invalid; else templateName\">\r\n              <div style=\"font-size:12px; margin-top:10px;\">Password must contain at least one capital letter and one\r\n                number</div>\r\n\r\n            </div>\r\n\r\n            <ng-template #templateName>\r\n\r\n              <!-- <div style=\"font-size:12px; margin-top:10px;\">Password must contain at least one capital letter and one number</div> -->\r\n\r\n            </ng-template>\r\n          </div>\r\n          <div class=\"input-field\">\r\n            <div class=\"input-element-title\">Confirm Password *</div>\r\n            <span class=\"p-input-icon-right\" style=\"width: 100%;\">\r\n              <i class=\"pi\" (click)=\"togglemyPasswordFieldType('confirm')\"\r\n                [ngClass]=\"getKeyValueFieldType('confirm')? 'pi-eye': 'pi-eye-slash'\" style=\"margin-top:-2px;\"></i>\r\n              <input name=\"confirm\" [type]=\"getKeyValueFieldType('confirm') ? 'text' : 'password'\" ngModel\r\n                type=\"password\" placeholder=\"Confirm Password\" #passwordInput=\"ngModel\" required class=\"input-element\"\r\n                autocomplete=\"off\" pattern=\"^(?=.*[A-Z])(?=.*[0-9]).*$\" />\r\n            </span>\r\n            <div *ngIf=\"confirmError\" class=\"input-error\">Passwords do not match.</div>\r\n          </div>\r\n          <div class=\"review-section flex align-items-center gap-1\"\r\n            style=\"font-size:15px; margin: 20px 0; font-weight: bold;\">\r\n            <!--  [(ngModel)]=\"selectedValues\" (onChange)=\"agree()\" -->\r\n            <p-checkbox id=\"termsCheckbox\" label=\"I have read and agree to the website \" class=\"terms-checkbox\"\r\n              name=\"groupname\" [(ngModel)]=\"termsAgreed\" binary=\"true\" [value]=\"termsAgreed\"></p-checkbox><a\r\n              href=\"https://mylingotrip.com/terms-conditions/\" target=\"_blank\"\r\n              style=\"color: black;font-size: 16px;\">terms and conditions*</a>\r\n          </div>\r\n          <ng-container>\r\n            <div class=\"grid align-items-center justify-content-center w-full mx-auto mt-3\">\r\n              <re-captcha (resolved)=\"captchaResolvedEvent($event)\"\r\n                siteKey=\"6LdFlA4pAAAAAJzrvvdou58XS_zQQ94z0lwxMv8i\"></re-captcha>\r\n            </div>\r\n          </ng-container>\r\n          <div class=\"grid justify-content-center mt-3\" *ngIf=\"this.selectedRole.length !== 0\">\r\n            <div class=\"sm:col-7\">\r\n              <button pButton type=\"submit\" [label]=\"registerButtonLabel\"\r\n                [icon]=\"isRegisterLoading ? 'pi pi-spin pi-spinner' : 'pi pi-arrow-right'\" iconPos=\"right\"\r\n                class=\"auth-btn-right teacher-button disabled-link\"\r\n                [ngClass]=\"{'teacher-bg' : registerAsTeacher, 'student-bg' : !registerAsTeacher, 'disabled-link': isRegisterLoading }\"></button>\r\n            </div>\r\n          </div>\r\n        </form>\r\n        <ng-container *ngIf=\"!registerAsTeacher && this.selectedRole.length !== 0\">\r\n          <!-- <div class=\"flex flex-column sm:flex-row gap-3 align-items-center sm:justify-content-center md:justify-content-between  mt-2\">\r\n        <button pButton pRipple type=\"button\" class=\"p-button p-button-md p-button-raised p-button-outlined btn-social sm:w-full\">\r\n          <div class=\"flex align-items-center pr-2\">\r\n            <img class=\"contact-icon-img\" src=\"/assets/icons/facebook_icon.svg\" width=\"32\" class=\"pr-2\" />\r\n            <span class=\"text-800 py-1\">Sign up with facebook</span>\r\n          </div>\r\n        </button>\r\n        <button pButton pRipple type=\"button\" class=\"p-button p-button-md p-button-raised pr-3 p-button-outlined btn-social sm:w-full\">\r\n          <div class=\"flex align-items-center pr-2\">\r\n            <img class=\"contact-icon-img\" src=\"/assets/icons/google_g_Logo.svg\" width=\"32\" class=\"pr-2\" />\r\n            <span class=\"text-800 py-1\">Sign up with Google</span>\r\n          </div>\r\n        </button>\r\n      </div> -->\r\n        </ng-container>\r\n\r\n      </ng-container>\r\n      <!-- <div *ngIf=\"is1024\" class=\"alternative\">\r\n        Already have an account? <div class=\"alternative-btn\" (click)=\"switchView()\"> Sign In</div>\r\n      </div> -->\r\n    </div>\r\n  </div>\r\n  <div class=\"form-container sign-in-container \">\r\n    <div class=\"relative h-full\">\r\n      <div class=\"title my-3 md:my-4 pb-3 mx-3 sm:mx-5 md:mx-6 lg:mx-7 align-items-start\">\r\n\r\n        <span class=\" text-center sm:text-left\">\r\n          Sign In\r\n        </span>\r\n\r\n      </div>\r\n\r\n      <ng-container *ngIf=\"isLoading\">\r\n\r\n\r\n        <div class=\"h-17rem md:h-auto\">\r\n          <div class=\"abs-centered\">\r\n            <app-loader [scale]=\"1.6\"></app-loader>\r\n          </div>\r\n        </div>\r\n\r\n      </ng-container>\r\n      <div *ngIf=\"!isLoading\" class=\"auth-form  flex flex-column mx-3 sm:mx-5 md:mx-6 lg:mx-7\">\r\n\r\n\r\n        <div class=\"grid\">\r\n          <div class=\"col-6\">\r\n          </div>\r\n        </div>\r\n\r\n        <!-- <div class=\"flex flex-column sm:flex-row gap-3 sm:gap-2 align-items-center justify-content-between\">\r\n        <button pButton pRipple type=\"button\" class=\"p-button p-button-md p-button-raised p-button-outlined btn-social sm:w-full\">\r\n          <div class=\"flex align-items-center lg:pr-3\">\r\n            <img class=\"contact-icon-img\" src=\"/assets/icons/facebook_icon.svg\" width=\"32\" class=\"pr-2\" />\r\n            <span class=\"text-800 py-1\">Login with facebook</span>\r\n          </div>\r\n        </button>\r\n        <button pButton pRipple type=\"button\" class=\"p-button p-button-md p-button-raised pr-3 p-button-outlined btn-social sm:w-full\">\r\n          <div class=\"flex align-items-center lg:pr-3\">\r\n            <img class=\"contact-icon-img\" src=\"/assets/icons/google_g_Logo.svg\" width=\"32\" class=\"pr-2\" />\r\n            <span class=\"text-800 py-1\">Login with Google</span>\r\n          </div>\r\n        </button>\r\n      </div> -->\r\n        <form (submit)=\"onLogin(loginForm)\" #loginForm=\"ngForm\">\r\n          <div class=\"input-field\">\r\n            <div class=\"input-element-title\">Email</div>\r\n            <input name=\"email\" ngModel type=\"email\" placeholder=\"Email\" #emailInput=\"ngModel\" required email\r\n              class=\"input-element\" type=\"text\">\r\n            <div *ngIf=\"emailError\" class=\"input-error\">Email is not valid</div>\r\n          </div>\r\n          <div class=\"input-field\">\r\n            <div class=\"input-element-title\">Password</div>\r\n\r\n            <span class=\"p-input-icon-right\" style=\"width: 100%;\">\r\n              <i class=\"pi\" (click)=\"togglemyPasswordFieldType('email')\"\r\n                [ngClass]=\"getKeyValueFieldType('email')? 'pi-eye': 'pi-eye-slash'\" style=\"margin-top:-2px;\"></i>\r\n              <input name=\"password\" ngModel [type]=\"getKeyValueFieldType('email') ? 'text' : 'password'\"\r\n                placeholder=\"Password\" #passwordInput=\"ngModel\" required class=\"input-element\" />\r\n            </span>\r\n          </div>\r\n          <div class=\"input-field pt-2\">\r\n            <button pButton type=\"submit\" styleClass=\"bg-primary\" label=\"Sign In\" class=\"mt-3\"\r\n              class=\"sign-in-btn hvr-glow\"></button>\r\n          </div>\r\n        </form>\r\n        <div *ngIf=\"is1024\" class=\"alternative\">\r\n          Don't have an account? <div class=\"alternative-btn\" (click)=\"switchView(true, true)\"> Sign Up</div>\r\n        </div>\r\n        <div class=\"alternative\" (click)=\"showForgotPassword = !showForgotPassword\">\r\n          Forgot password? <div class=\"alternative-btn\"> Get it here</div>\r\n        </div>\r\n        <form *ngIf=\"showForgotPassword\" (submit)=\"onForgot(forgotForm)\" #forgotForm=\"ngForm\">\r\n          <div class=\"input-field\">\r\n            <div class=\"input-element-title\">Email</div>\r\n            <div style=\"font-size:12px; margin-top:10px;\">Please enter your email address to receive the password reset\r\n              instructions. If you don’t see the email in your inbox, please check your spam or junk folder.</div>\r\n            <input name=\"email\" ngModel type=\"email\" placeholder=\"Email\" #emailInputForgot=\"ngModel\" required email\r\n              class=\"input-element\" type=\"text\">\r\n            <div *ngIf=\"emailError\" class=\"input-error\">Email is not valid</div>\r\n          </div>\r\n\r\n          <div class=\"flex mt-3\">\r\n            <button pButton type=\"submit\" styleClass=\"bg-primary\" label=\"Submit\" class=\"sign-in-btn hvr-glow\"></button>\r\n          </div>\r\n        </form>\r\n        <!-- <div class=\"alternative\">\r\n        Change password? <div class=\"alternative-btn\"> Change it here</div>\r\n      </div> -->\r\n      </div>\r\n\r\n    </div>\r\n  </div>\r\n  <div class=\"overlay-container\">\r\n    <div class=\"overlay\">\r\n      <div class=\"overlay-panel overlay-left left-side-content justify-content-between gap-2 py-5\">\r\n        <div class=\"logo my-3 md:my-0\">\r\n          <a href=\"https://mylingotrip.com\" target=\"_blank\">\r\n          <img src=\"/assets/icons/logo/mlt_logo-new.svg\" width=\"100\">\r\n          </a>\r\n        </div>\r\n\r\n        <button pButton type=\"button\" (click)=\"handleSwitchViewClick()\" [label]=\"'Sign Up'\"\r\n          class=\"p-button-outlined auth-btn\"></button>\r\n\r\n        <div>\r\n          <h1 class=\"title text-center line-height-1\">Welcome!</h1>\r\n          <h2 class=\"m-0\"><a class=\"fl-typo s12-16 cursor-pointer hover:underline mt-2 block\"\r\n              (click)=\"handleSwitchViewClick()\">Sign Up if you don't\r\n              have an account</a>\r\n          </h2>\r\n        </div>\r\n      </div>\r\n      <div class=\"overlay-panel overlay-right left-side-content justify-content-between gap-2 py-5\">\r\n        <div class=\"logo my-3 md:my-0\">\r\n          \r\n          <a href=\"https://mylingotrip.com\" target=\"_blank\">\r\n          <img src=\"/assets/icons/logo/mlt_logo-new.svg\" width=\"100\">\r\n          </a>\r\n        </div>\r\n\r\n        <div class=\"flex gap-3 flex-column\">\r\n\r\n          <button pButton type=\"button\" (click)=\"handleSwitchViewClick()\" [label]=\"'Sign In'\"\r\n            class=\"p-button-outlined auth-btn\"></button>\r\n\r\n          <div>\r\n            <a class=\"cursor-pointer hover:underline\" (click)=\"handleSwitchViewClick()\">Sign-In if you are already a\r\n              member</a>\r\n          </div>\r\n        </div>\r\n\r\n        <div>\r\n\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n}@placeholder {\r\n<div class=\"h-17rem md:h-auto\">\r\n  <div class=\"abs-centered\">\r\n    <app-loader [scale]=\"1.6\"></app-loader>\r\n  </div>\r\n</div>\r\n}"], "mappings": "AAAA,SAASA,YAAY,EAAEC,QAAQ,QAAkB,iBAAiB;AAClE,SAA8EC,MAAM,QAAmB,eAAe;AACtH,SAA0BC,WAAW,EAAUC,mBAAmB,EAAEC,kBAAkB,EAAiCC,UAAU,QAAQ,gBAAgB;AACzJ,SAASC,wBAAwB,QAAQ,oCAAoC;AAE7E,SAASC,eAAe,QAA4B,cAAc;AAClE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,QAAQ,EAAcC,EAAE,QAAQ,MAAM;AAC/C,SAASC,SAAS,EAAEC,IAAI,QAAQ,gBAAgB;AAChD,SAAeC,QAAQ,EAAEC,UAAU,QAAQ,gCAAgC;AAQ3E,SAASC,UAAU,EAAEC,oBAAoB,QAAQ,+BAA+B;AAChF,SAASC,eAAe,QAAQ,wCAAwC;AACxE,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;;;;;2RAW7BD,eAAe,EAAAE,GAAA,CAAAC,kBAAA;;;;;;;;;;;ICXfC,EAAA,CAAAC,uBAAA,GAAgC;IAI5BD,EADF,CAAAE,cAAA,cAA+B,cACH;IACxBF,EAAA,CAAAG,SAAA,qBAAuC;IAE3CH,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAFUJ,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAM,UAAA,cAAa;;;;;IAqDvBN,EAAA,CAAAE,cAAA,cAA4C;IAAAF,EAAA,CAAAO,MAAA,0BAAmB;IAAAP,EAAA,CAAAI,YAAA,EAAM;;;;;IAenEJ,EADF,CAAAE,cAAA,UAAkD,cACF;IAAAF,EAAA,CAAAO,MAAA,uDAAgD;IAChGP,EADgG,CAAAI,YAAA,EAAM,EAChG;;;;;IAEJJ,EADF,CAAAE,cAAA,UAAsD,cACN;IAAAF,EAAA,CAAAO,MAAA,uEACtC;IAEVP,EAFU,CAAAI,YAAA,EAAM,EAEV;;;;;;IAiBNJ,EAAA,CAAAE,cAAA,cAA8C;IAAAF,EAAA,CAAAO,MAAA,8BAAuB;IAAAP,EAAA,CAAAI,YAAA,EAAM;;;;;IAiB3EJ,EADF,CAAAE,cAAA,cAAqF,cAC7D;IACpBF,EAAA,CAAAG,SAAA,iBAGkI;IAEtIH,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAL4BJ,EAAA,CAAAK,SAAA,GAA6B;IAGzDL,EAH4B,CAAAM,UAAA,UAAAE,MAAA,CAAAC,mBAAA,CAA6B,SAAAD,MAAA,CAAAE,iBAAA,iDACiB,YAAAV,EAAA,CAAAW,eAAA,IAAAC,GAAA,EAAAJ,MAAA,CAAAK,iBAAA,GAAAL,MAAA,CAAAK,iBAAA,EAAAL,MAAA,CAAAE,iBAAA,EAE4C;;;;;IAI9HV,EAAA,CAAAc,kBAAA,GAee;;;;;;IAzGjBd,EAAA,CAAAC,uBAAA,GAAmD;IACjDD,EAAA,CAAAE,cAAA,cAAiE;IAC/DF,EAAA,CAAAO,MAAA,gBACF;IAAAP,EAAA,CAAAI,YAAA,EAAM;IAKAJ,EAHN,CAAAE,cAAA,cAAkB,cACG,cACmB,qBAEoC;IADjCF,EAAA,CAAAe,UAAA,sBAAAC,+EAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAYZ,MAAA,CAAAa,QAAA,EAAU;IAAA,EAAC;IAC9BrB,EAAA,CAAAsB,gBAAA,2BAAAC,oFAAAC,MAAA;MAAAxB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAAnB,EAAA,CAAAyB,kBAAA,CAAAjB,MAAA,CAAAkB,YAAA,EAAAF,MAAA,MAAAhB,MAAA,CAAAkB,YAAA,GAAAF,MAAA;MAAA,OAAAxB,EAAA,CAAAoB,WAAA,CAAAI,MAAA;IAAA,EAA0B;IAE5DxB,EAF0E,CAAAI,YAAA,EAAa,EAC/E,EACF;IAGFJ,EAFJ,CAAAE,cAAA,cAAmB,cACmB,qBAGnB;IAF0DF,EAAA,CAAAe,UAAA,sBAAAY,+EAAA;MAAA3B,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAYZ,MAAA,CAAAa,QAAA,EAAU;IAAA,EAAC;IACpCrB,EAAA,CAAAsB,gBAAA,2BAAAM,oFAAAJ,MAAA;MAAAxB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAAnB,EAAA,CAAAyB,kBAAA,CAAAjB,MAAA,CAAAkB,YAAA,EAAAF,MAAA,MAAAhB,MAAA,CAAAkB,YAAA,GAAAF,MAAA;MAAA,OAAAxB,EAAA,CAAAoB,WAAA,CAAAI,MAAA;IAAA,EAA0B;IAI5FxB,EAHqB,CAAAI,YAAA,EAAa,EACxB,EACF,EACF;IAGNJ,EAAA,CAAAE,cAAA,mBAAgF;IAA1EF,EAAA,CAAAe,UAAA,sBAAAc,0EAAA;MAAA7B,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAY,aAAA,GAAA9B,EAAA,CAAA+B,WAAA;MAAA,MAAAvB,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAYZ,MAAA,CAAAwB,QAAA,CAAAF,aAAA,CAAoB;IAAA,EAAC;IAEnC9B,EADF,CAAAE,cAAA,eAAyB,eACU;IAAAF,EAAA,CAAAO,MAAA,gBAAQ;IAAAP,EAAA,CAAAI,YAAA,EAAM;IAC/CJ,EAAA,CAAAG,SAAA,oBAC6C;IAC7CH,EAAA,CAAAiC,UAAA,KAAAC,sDAAA,kBAA4C;IAC9ClC,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAE,cAAA,eAAyB,eACU;IAAAF,EAAA,CAAAO,MAAA,kBAAU;IAAAP,EAAA,CAAAI,YAAA,EAAM;IAE/CJ,EADF,CAAAE,cAAA,gBAAsD,aAE8C;IADpFF,EAAA,CAAAe,UAAA,mBAAAoB,oEAAA;MAAAnC,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASZ,MAAA,CAAA4B,yBAAA,CAA0B,UAAU,CAAC;IAAA,EAAC;IACqCpC,EAAA,CAAAI,YAAA,EAAI;IACtGJ,EAAA,CAAAE,cAAA,oBAGyC;IAHlBF,EAAA,CAAAsB,gBAAA,2BAAAe,gFAAAb,MAAA;MAAAxB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAAnB,EAAA,CAAAyB,kBAAA,CAAAjB,MAAA,CAAA8B,QAAA,EAAAd,MAAA,MAAAhB,MAAA,CAAA8B,QAAA,GAAAd,MAAA;MAAA,OAAAxB,EAAA,CAAAoB,WAAA,CAAAI,MAAA;IAAA,EAAsB;IAM/CxB,EANE,CAAAI,YAAA,EAGyC,EAGpC;IAUPJ,EATA,CAAAiC,UAAA,KAAAM,sDAAA,kBAAkD,KAAAC,sDAAA,kBAGI,KAAAC,8DAAA,gCAAAzC,EAAA,CAAA0C,sBAAA,CAM3B;IAK7B1C,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAE,cAAA,eAAyB,eACU;IAAAF,EAAA,CAAAO,MAAA,0BAAkB;IAAAP,EAAA,CAAAI,YAAA,EAAM;IAEvDJ,EADF,CAAAE,cAAA,gBAAsD,aAE6C;IADnFF,EAAA,CAAAe,UAAA,mBAAA4B,oEAAA;MAAA3C,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASZ,MAAA,CAAA4B,yBAAA,CAA0B,SAAS,CAAC;IAAA,EAAC;IACqCpC,EAAA,CAAAI,YAAA,EAAI;IACrGJ,EAAA,CAAAG,SAAA,oBAE4D;IAC9DH,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAiC,UAAA,KAAAW,sDAAA,kBAA8C;IAChD5C,EAAA,CAAAI,YAAA,EAAM;IAIJJ,EAHF,CAAAE,cAAA,eAC6D,sBAGsB;IAA9DF,EAAA,CAAAsB,gBAAA,2BAAAuB,qFAAArB,MAAA;MAAAxB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAAnB,EAAA,CAAAyB,kBAAA,CAAAjB,MAAA,CAAAsC,WAAA,EAAAtB,MAAA,MAAAhB,MAAA,CAAAsC,WAAA,GAAAtB,MAAA;MAAA,OAAAxB,EAAA,CAAAoB,WAAA,CAAAI,MAAA;IAAA,EAAyB;IAAqCxB,EAAA,CAAAI,YAAA,EAAa;IAAAJ,EAAA,CAAAE,cAAA,aAEtD;IAAAF,EAAA,CAAAO,MAAA,6BAAqB;IAC/DP,EAD+D,CAAAI,YAAA,EAAI,EAC7D;IACNJ,EAAA,CAAAC,uBAAA,IAAc;IAEVD,EADF,CAAAE,cAAA,eAAgF,sBAEzB;IADzCF,EAAA,CAAAe,UAAA,sBAAAgC,gFAAAvB,MAAA;MAAAxB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAV,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAYZ,MAAA,CAAAwC,oBAAA,CAAAxB,MAAA,CAA4B;IAAA,EAAC;IAEvDxB,EADuD,CAAAI,YAAA,EAAa,EAC9D;;IAERJ,EAAA,CAAAiC,UAAA,KAAAgB,sDAAA,kBAAqF;IAQvFjD,EAAA,CAAAI,YAAA,EAAO;IACPJ,EAAA,CAAAiC,UAAA,KAAAiB,+DAAA,2BAA2E;;;;;;;IAjFvClD,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAmD,gBAAA,YAAA3C,MAAA,CAAAkB,YAAA,CAA0B;IAMI1B,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAmD,gBAAA,YAAA3C,MAAA,CAAAkB,YAAA,CAA0B;IAYlF1B,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAA4C,UAAA,CAAgB;IAMlBpD,EAAA,CAAAK,SAAA,GAAsE;IAAtEL,EAAA,CAAAM,UAAA,YAAAE,MAAA,CAAA6C,oBAAA,yCAAsE;IACjDrD,EAAA,CAAAK,SAAA,EAAsB;IAAtBL,EAAA,CAAAmD,gBAAA,YAAA3C,MAAA,CAAA8B,QAAA,CAAsB;IAC3CtC,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAA6C,oBAAA,mCAA+D;IAM7DrD,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAM,UAAA,SAAAgD,gBAAA,IAAAA,gBAAA,CAAAC,KAAA,CAA0C;IAG1CvD,EAAA,CAAAK,SAAA,EAA6B;IAAAL,EAA7B,CAAAM,UAAA,SAAAgD,gBAAA,CAAAE,OAAA,CAA6B,aAAAC,eAAA,CAAiB;IAgBhDzD,EAAA,CAAAK,SAAA,GAAqE;IAArEL,EAAA,CAAAM,UAAA,YAAAE,MAAA,CAAA6C,oBAAA,wCAAqE;IACjDrD,EAAA,CAAAK,SAAA,EAA8D;IAA9DL,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAA6C,oBAAA,kCAA8D;IAIhFrD,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAAkD,YAAA,CAAkB;IAML1D,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAmD,gBAAA,YAAA3C,MAAA,CAAAsC,WAAA,CAAyB;IAAe9C,EAAA,CAAAM,UAAA,UAAAE,MAAA,CAAAsC,WAAA,CAAqB;IAUnC9C,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAAkB,YAAA,CAAAiC,MAAA,OAAoC;IAStE3D,EAAA,CAAAK,SAAA,EAA0D;IAA1DL,EAAA,CAAAM,UAAA,UAAAE,MAAA,CAAAK,iBAAA,IAAAL,MAAA,CAAAkB,YAAA,CAAAiC,MAAA,OAA0D;;;;;IAiC3E3D,EAAA,CAAAC,uBAAA,GAAgC;IAI5BD,EADF,CAAAE,cAAA,cAA+B,cACH;IACxBF,EAAA,CAAAG,SAAA,qBAAuC;IAE3CH,EADE,CAAAI,YAAA,EAAM,EACF;;;;IAFUJ,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAM,UAAA,cAAa;;;;;IAgCzBN,EAAA,CAAAE,cAAA,cAA4C;IAAAF,EAAA,CAAAO,MAAA,yBAAkB;IAAAP,EAAA,CAAAI,YAAA,EAAM;;;;;;IAiBxEJ,EAAA,CAAAE,cAAA,cAAwC;IACtCF,EAAA,CAAAO,MAAA,+BAAuB;IAAAP,EAAA,CAAAE,cAAA,cAA8D;IAAjCF,EAAA,CAAAe,UAAA,mBAAA6C,mEAAA;MAAA5D,EAAA,CAAAiB,aAAA,CAAA4C,GAAA;MAAA,MAAArD,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASZ,MAAA,CAAAsD,UAAA,CAAW,IAAI,EAAE,IAAI,CAAC;IAAA,EAAC;IAAE9D,EAAA,CAAAO,MAAA,eAAO;IAC/FP,EAD+F,CAAAI,YAAA,EAAM,EAC/F;;;;;IAWFJ,EAAA,CAAAE,cAAA,cAA4C;IAAAF,EAAA,CAAAO,MAAA,yBAAkB;IAAAP,EAAA,CAAAI,YAAA,EAAM;;;;;;IAPxEJ,EAAA,CAAAE,cAAA,kBAAsF;IAArDF,EAAA,CAAAe,UAAA,oBAAAgD,sEAAA;MAAA/D,EAAA,CAAAiB,aAAA,CAAA+C,IAAA;MAAA,MAAAC,cAAA,GAAAjE,EAAA,CAAA+B,WAAA;MAAA,MAAAvB,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAUZ,MAAA,CAAA0D,QAAA,CAAAD,cAAA,CAAoB;IAAA,EAAC;IAE5DjE,EADF,CAAAE,cAAA,cAAyB,cACU;IAAAF,EAAA,CAAAO,MAAA,YAAK;IAAAP,EAAA,CAAAI,YAAA,EAAM;IAC5CJ,EAAA,CAAAE,cAAA,cAA8C;IAAAF,EAAA,CAAAO,MAAA,wKACkD;IAAAP,EAAA,CAAAI,YAAA,EAAM;IACtGJ,EAAA,CAAAG,SAAA,mBACoC;IACpCH,EAAA,CAAAiC,UAAA,IAAAkC,oDAAA,kBAA4C;IAC9CnE,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAE,cAAA,eAAuB;IACrBF,EAAA,CAAAG,SAAA,kBAA2G;IAE/GH,EADE,CAAAI,YAAA,EAAM,EACD;;;;IANGJ,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAA4C,UAAA,CAAgB;;;;;;IAtD1BpD,EAHF,CAAAE,cAAA,cAAyF,cAGrE;IAChBF,EAAA,CAAAG,SAAA,cACM;IACRH,EAAA,CAAAI,YAAA,EAAM;IAgBNJ,EAAA,CAAAE,cAAA,kBAAwD;IAAlDF,EAAA,CAAAe,UAAA,oBAAAqD,8DAAA;MAAApE,EAAA,CAAAiB,aAAA,CAAAoD,GAAA;MAAA,MAAAC,YAAA,GAAAtE,EAAA,CAAA+B,WAAA;MAAA,MAAAvB,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAUZ,MAAA,CAAA+D,OAAA,CAAAD,YAAA,CAAkB;IAAA,EAAC;IAE/BtE,EADF,CAAAE,cAAA,cAAyB,cACU;IAAAF,EAAA,CAAAO,MAAA,YAAK;IAAAP,EAAA,CAAAI,YAAA,EAAM;IAC5CJ,EAAA,CAAAG,SAAA,mBACoC;IACpCH,EAAA,CAAAiC,UAAA,KAAAuC,6CAAA,kBAA4C;IAC9CxE,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAE,cAAA,eAAyB,eACU;IAAAF,EAAA,CAAAO,MAAA,gBAAQ;IAAAP,EAAA,CAAAI,YAAA,EAAM;IAG7CJ,EADF,CAAAE,cAAA,gBAAsD,aAE2C;IADjFF,EAAA,CAAAe,UAAA,mBAAA0D,2DAAA;MAAAzE,EAAA,CAAAiB,aAAA,CAAAoD,GAAA;MAAA,MAAA7D,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASZ,MAAA,CAAA4B,yBAAA,CAA0B,OAAO,CAAC;IAAA,EAAC;IACqCpC,EAAA,CAAAI,YAAA,EAAI;IACnGJ,EAAA,CAAAG,SAAA,oBACmF;IAEvFH,EADE,CAAAI,YAAA,EAAO,EACH;IACNJ,EAAA,CAAAE,cAAA,eAA8B;IAC5BF,EAAA,CAAAG,SAAA,kBACwC;IAE5CH,EADE,CAAAI,YAAA,EAAM,EACD;IACPJ,EAAA,CAAAiC,UAAA,KAAAyC,6CAAA,kBAAwC;IAGxC1E,EAAA,CAAAE,cAAA,eAA4E;IAAnDF,EAAA,CAAAe,UAAA,mBAAA4D,6DAAA;MAAA3E,EAAA,CAAAiB,aAAA,CAAAoD,GAAA;MAAA,MAAA7D,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAAAZ,MAAA,CAAAoE,kBAAA,IAAApE,MAAA,CAAAoE,kBAAA;IAAA,EAAkD;IACzE5E,EAAA,CAAAO,MAAA,0BAAiB;IAAAP,EAAA,CAAAE,cAAA,eAA6B;IAACF,EAAA,CAAAO,MAAA,oBAAW;IAC5DP,EAD4D,CAAAI,YAAA,EAAM,EAC5D;IACNJ,EAAA,CAAAiC,UAAA,KAAA4C,8CAAA,oBAAsF;IAiBxF7E,EAAA,CAAAI,YAAA,EAAM;;;;IAxCMJ,EAAA,CAAAK,SAAA,IAAgB;IAAhBL,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAA4C,UAAA,CAAgB;IAOlBpD,EAAA,CAAAK,SAAA,GAAmE;IAAnEL,EAAA,CAAAM,UAAA,YAAAE,MAAA,CAAA6C,oBAAA,sCAAmE;IACtCrD,EAAA,CAAAK,SAAA,EAA4D;IAA5DL,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAA6C,oBAAA,gCAA4D;IAS3FrD,EAAA,CAAAK,SAAA,GAAY;IAAZL,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAAsE,MAAA,CAAY;IAMX9E,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAAoE,kBAAA,CAAwB;;;;;;IAxNrC5E,EADF,CAAAE,cAAA,aAAkG,aAClD;IAE5CF,EAAA,CAAAiC,UAAA,IAAA8C,8CAAA,2BAAgC;IAc1B/E,EAJN,CAAAE,cAAA,cAA0E,cAE5B,cACiC,cAC9C;IAEzBF,EAAA,CAAAG,SAAA,mBAA2B;IAEUH,EAArC,CAAAE,cAAA,cAAqC,gBAAyC;IAAAF,EAAA,CAAAO,MAAA,oDAE/D;IASrBP,EATqB,CAAAI,YAAA,EAAO,EAAM,EAOxB,EACF,EACF;IAENJ,EAAA,CAAAiC,UAAA,KAAA+C,+CAAA,6BAAmD;IAgHvDhF,EADE,CAAAI,YAAA,EAAM,EACF;IAKAJ,EAJN,CAAAE,cAAA,eAA+C,eAChB,eACyD,gBAE1C;IACtCF,EAAA,CAAAO,MAAA,iBACF;IAEFP,EAFE,CAAAI,YAAA,EAAO,EAEH;IAYNJ,EAVA,CAAAiC,UAAA,KAAAgD,+CAAA,2BAAgC,KAAAC,sCAAA,mBAUyD;IAsE7FlF,EADE,CAAAI,YAAA,EAAM,EACF;IAKEJ,EAJR,CAAAE,cAAA,eAA+B,eACR,eAC0E,eAC5D,aACqB;IAClDF,EAAA,CAAAG,SAAA,eAA2D;IAE7DH,EADE,CAAAI,YAAA,EAAI,EACA;IAENJ,EAAA,CAAAE,cAAA,kBACqC;IADPF,EAAA,CAAAe,UAAA,mBAAAoE,yDAAA;MAAAnF,EAAA,CAAAiB,aAAA,CAAAmE,GAAA;MAAA,MAAA5E,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASZ,MAAA,CAAA6E,qBAAA,EAAuB;IAAA,EAAC;IAC1BrF,EAAA,CAAAI,YAAA,EAAS;IAG5CJ,EADF,CAAAE,cAAA,WAAK,cACyC;IAAAF,EAAA,CAAAO,MAAA,gBAAQ;IAAAP,EAAA,CAAAI,YAAA,EAAK;IACzCJ,EAAhB,CAAAE,cAAA,cAAgB,aACsB;IAAlCF,EAAA,CAAAe,UAAA,mBAAAuE,oDAAA;MAAAtF,EAAA,CAAAiB,aAAA,CAAAmE,GAAA;MAAA,MAAA5E,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASZ,MAAA,CAAA6E,qBAAA,EAAuB;IAAA,EAAC;IAACrF,EAAA,CAAAO,MAAA,4CACnB;IAGvBP,EAHuB,CAAAI,YAAA,EAAI,EAClB,EACD,EACF;IAIFJ,EAHJ,CAAAE,cAAA,eAA8F,eAC7D,aAEqB;IAClDF,EAAA,CAAAG,SAAA,eAA2D;IAE7DH,EADE,CAAAI,YAAA,EAAI,EACA;IAIJJ,EAFF,CAAAE,cAAA,eAAoC,kBAGG;IADPF,EAAA,CAAAe,UAAA,mBAAAwE,yDAAA;MAAAvF,EAAA,CAAAiB,aAAA,CAAAmE,GAAA;MAAA,MAAA5E,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASZ,MAAA,CAAA6E,qBAAA,EAAuB;IAAA,EAAC;IAC1BrF,EAAA,CAAAI,YAAA,EAAS;IAG5CJ,EADF,CAAAE,cAAA,WAAK,aACyE;IAAlCF,EAAA,CAAAe,UAAA,mBAAAyE,oDAAA;MAAAxF,EAAA,CAAAiB,aAAA,CAAAmE,GAAA;MAAA,MAAA5E,MAAA,GAAAR,EAAA,CAAAmB,aAAA;MAAA,OAAAnB,EAAA,CAAAoB,WAAA,CAASZ,MAAA,CAAA6E,qBAAA,EAAuB;IAAA,EAAC;IAACrF,EAAA,CAAAO,MAAA,2CACpE;IAEZP,EAFY,CAAAI,YAAA,EAAI,EACR,EACF;IAENJ,EAAA,CAAAG,SAAA,WAEM;IAIdH,EAHM,CAAAI,YAAA,EAAM,EACF,EACF,EACF;;;;IA3R4CJ,EAAA,CAAAM,UAAA,YAAAN,EAAA,CAAAyF,eAAA,IAAAC,GAAA,EAAAlF,MAAA,CAAAmF,WAAA,EAA+C;IAG9E3F,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAAoF,SAAA,CAAe;IAUzB5F,EAAA,CAAAK,SAAA,EAAoB;IAApBL,EAAA,CAAAM,UAAA,WAAAE,MAAA,CAAAoF,SAAA,CAAoB;IAElB5F,EAAA,CAAAK,SAAA,EAAsC;IAAtCL,EAAA,CAAAM,UAAA,YAAAE,MAAA,CAAAqF,0BAAA,CAAsC;IAmB5B7F,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAM,UAAA,UAAAE,MAAA,CAAAqF,0BAAA,CAAkC;IA2HlC7F,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,UAAA,SAAAE,MAAA,CAAAoF,SAAA,CAAe;IAUxB5F,EAAA,CAAAK,SAAA,EAAgB;IAAhBL,EAAA,CAAAM,UAAA,UAAAE,MAAA,CAAAoF,SAAA,CAAgB;IAgF4C5F,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,UAAA,oBAAmB;IAqBjBN,EAAA,CAAAK,SAAA,IAAmB;IAAnBL,EAAA,CAAAM,UAAA,oBAAmB;;;;;IAmB3FN,EADF,CAAAE,cAAA,cAA+B,cACH;IACxBF,EAAA,CAAAG,SAAA,qBAAuC;IAE3CH,EADE,CAAAI,YAAA,EAAM,EACF;;;IAFUJ,EAAA,CAAAK,SAAA,GAAa;IAAbL,EAAA,CAAAM,UAAA,cAAa;;;ADzQ7B,WAAawF,cAAc;EAArB,MAAOA,cAAc;IAsCzBC,YACUC,YAA0B,EAC1BC,cAA8B,EAC9BC,WAAwB,EACzBC,WAAwB,EACxBC,WAAwB,EACxBC,yBAAoD,EACpDC,mBAAwC,EACvCC,MAAc,EACdC,IAAY,EACMC,QAAkB,EACpCC,QAAmB,EACnBC,QAAkB,EAClBC,cAA8B;MAZ9B,KAAAZ,YAAY,GAAZA,YAAY;MACZ,KAAAC,cAAc,GAAdA,cAAc;MACd,KAAAC,WAAW,GAAXA,WAAW;MACZ,KAAAC,WAAW,GAAXA,WAAW;MACX,KAAAC,WAAW,GAAXA,WAAW;MACX,KAAAC,yBAAyB,GAAzBA,yBAAyB;MACzB,KAAAC,mBAAmB,GAAnBA,mBAAmB;MAClB,KAAAC,MAAM,GAANA,MAAM;MACN,KAAAC,IAAI,GAAJA,IAAI;MACc,KAAAC,QAAQ,GAARA,QAAQ;MAC1B,KAAAC,QAAQ,GAARA,QAAQ;MACR,KAAAC,QAAQ,GAARA,QAAQ;MACR,KAAAC,cAAc,GAAdA,cAAc;MAlDhB,KAAAC,IAAI,GAAG,IAAIhH,OAAO,EAAE;MAC5B,KAAA+F,SAAS,GAAG,KAAK;MACjB,KAAAlF,iBAAiB,GAAG,KAAK;MACzB,KAAAoC,WAAW,GAAG,KAAK;MAInB,KAAA6C,WAAW,GAAY,IAAI;MAC3B,KAAAmB,YAAY,GAAY,KAAK;MAC7B,KAAAjG,iBAAiB,GAAY,KAAK;MAClC,KAAAkG,KAAK,GAAY,KAAK;MACtB,KAAAjC,MAAM,GAAY,KAAK;MACvB,KAAAkC,MAAM,GAAY,KAAK;MACvB,KAAAC,QAAQ,GAAY,KAAK;MACzB,KAAAC,KAAK,GAAY,KAAK;MACtB,KAAA9D,UAAU,GAAY,KAAK;MAC3B,KAAAM,YAAY,GAAY,KAAK;MAC7B,KAAAmC,0BAA0B,GAAY,KAAK;MAC3C,KAAAjB,kBAAkB,GAAY,KAAK;MACnC,KAAAlD,YAAY,GAAa,EAAE;MAC3B,KAAAjB,mBAAmB,GAAG,qBAAqB;MAE3C,KAAA0G,iBAAiB,GAAmC;QAClDC,KAAK,EAAE,KAAK;QACZ9E,QAAQ,EAAE,KAAK;QACf+E,OAAO,EAAE;OACV;MACD,KAAAC,aAAa,GAAW,EAAE;MAC1B,KAAAC,mBAAmB,GAAG,IAAIzI,kBAAkB,CAAC,EAAE,EAAE,CAC/CC,UAAU,CAACyI,QAAQ,EACnBzI,UAAU,CAAC0I,OAAO,CAAC,4BAA4B,CAAC,CACjD,CAAC;MACF,KAAAC,UAAU,GAAG,IAAI;MACjB,KAAAC,WAAW,GAAG,IAAI;MAElB,KAAAC,eAAe,GAAGjJ,MAAM,CAAC,KAAK,CAAC;MACiB,KAAAkJ,cAAc,GAAe,EAAgB;MAgB3F,IAAI,CAACC,OAAO,GAAQ,IAAI,CAACrB,QAAQ,CAACsB,WAAW;MAC7CC,OAAO,CAACC,GAAG,CAACrB,cAAc,CAACsB,UAAU,CAAC;MACtC;IAEF;IAEAC,QAAQA,CAAA;MAEN;MACA,IAAI,CAACC,SAAS,GAAG,IAAI,CAACxB,cAAc,CAACyB,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAIC,SAAS;MACnF,IAAI,IAAI,CAAChC,MAAM,CAACiC,GAAG,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;QACrC,IAAI,CAAC9C,WAAW,GAAG,IAAI;MACzB;MACA,IAAI,IAAI,CAACY,MAAM,CAACiC,GAAG,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;QACxC,IAAI,CAAC9C,WAAW,GAAG,KAAK;MAC1B;MAEA,IAAI,CAACkB,IAAI,CAAC6B,GAAG,CAAC,IAAI,CAACxC,WAAW,CAACyC,eAAe,EAAE,CAACC,SAAS,CAAEC,GAAG,IAAI;QACjE,IAAIA,GAAG,EAAE;UACP,IAAI,CAAC3C,WAAW,CAAC4C,mBAAmB,EAAE;QACxC;MACF,CAAC,CAAC,CAAC;MAEH,IAAI,CAACjC,IAAI,CAAC6B,GAAG,CAAC,IAAI,CAACvC,WAAW,CAAC4C,qBAAqB,EAAE,CAACH,SAAS,CAACI,UAAU,IAAG;QAC5E,IAAIA,UAAU,EAAE;UACd;QAAA;MAEJ,CAAC,CAAC,CAAC;MACH,IAAI,CAACnC,IAAI,CAAC6B,GAAG,CAAC,IAAI,CAACzC,cAAc,CAACgD,UAAU,CAACC,IAAI,CAAC3J,IAAI,CAAC,CAAC,CAAC,CAAC,CAACqJ,SAAS,CAACC,GAAG,IAAG;QACzE,IAAIA,GAAG,EAAE;UACP,IAAI,CAAC/D,MAAM,GAAG+D,GAAG,CAAC/D,MAAM;UACxB,IAAI,CAACkC,MAAM,GAAG6B,GAAG,CAAC7B,MAAM;UACxB,IAAI,CAACD,KAAK,GAAG8B,GAAG,CAAC9B,KAAK;UACtB,IAAI,CAACG,KAAK,GAAG2B,GAAG,CAAC3B,KAAK;UACtB,IAAI,CAACD,QAAQ,GAAG4B,GAAG,CAACM,OAAQ;UAC5B,IAAI,IAAI,CAAClC,QAAQ,EAAE;YAEjB;UAAA;UAEFe,OAAO,CAACC,GAAG,CAACY,GAAG,CAAC;QAClB;MACF,CAAC,CAAC,CAAC;MACH,IAAI,CAACO,kCAAkC,EAAE;MACzC;IACF;IAEOC,kBAAkBA,CAAA,GAEzB;IAEOC,UAAUA,CAACd,GAAW;MAC3BR,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC,IAAIsB,IAAI,GAAG9C,QAAQ,CAAC+C,aAAa,CAAC,QAAQ,CAAC;MAC3CD,IAAI,CAACE,YAAY,CAAC,IAAI,EAAE,eAAe,CAAC;MACxCF,IAAI,CAACG,GAAG,GAAGlB,GAAG;MACde,IAAI,CAACI,IAAI,GAAG,iBAAiB;MAC7BJ,IAAI,CAACK,KAAK,GAAG,IAAI;MACjBL,IAAI,CAACM,OAAO,GAAG,OAAO;MACtBpD,QAAQ,CAACqD,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAACC,WAAW,CAACR,IAAI,CAAC;IAC5D;IACAS,eAAeA,CAAA,GACf;IAEAC,WAAWA,CAAA;MACT,IAAI,CAACpD,IAAI,CAACqD,WAAW,EAAE;MACvB;MACA;IACF;IAEAd,kCAAkCA,CAAA;MAChC,IAAI,CAACvC,IAAI,CAAC6B,GAAG,CAAC,IAAI,CAACvC,WAAW,CAACgE,kCAAkC,CAACvB,SAAS,CAACC,GAAG,IAAG;QAChFb,OAAO,CAACC,GAAG,CAACY,GAAG,CAAC;QAChB,IAAI,CAAChD,0BAA0B,GAAGgD,GAAG;QACrC,IAAIA,GAAG,EAAE;UACP,IAAI,CAACuB,iCAAiC,CAAC,IAAI,CAACvC,cAAc,CAAC;QAC7D;MACF,CAAC,CAAC,CAAC;IACL;IAEAwC,cAAcA,CAACC,IAAS;MACtB,IAAI,CAACzJ,iBAAiB,GAAGyJ,IAAI,CAACC,OAAO;IACvC;IAEAzG,UAAUA,CAAC0G,OAAO,GAAG,KAAK,EAAEC,iBAAiB,GAAG,KAAK;MACnD,IAAI,CAAC5E,0BAA0B,GAAG,KAAK;MAEvC,IAAI4E,iBAAiB,EAAE;QACrB,IAAI,CAAC9C,WAAW,GAAG8C,iBAAiB;MACtC;MACA,IAAI,IAAI,CAACvD,KAAK,IAAI,IAAI,CAACF,MAAM,IAAIwD,OAAO,EAAE;QACxC,IAAI,IAAI,CAAC7E,WAAW,EAAE;UACpB,IAAI,IAAI,CAACsB,QAAQ,EAAE;YAEjB,MAAMyD,KAAK,GAAGjE,QAAQ,CAACkE,cAAc,CAAC,WAAW,CAAC;YAClD,MAAMC,KAAK,GAAGnE,QAAQ,CAACkE,cAAc,CAAC,YAAY,CAAC;YAEnD;YACA;YACA;YACA;YACA;YACA;YAEA;YAEA;YAEAlE,QAAQ,CAACkE,cAAc,CAAC,WAAW,CAAE,CAACE,KAAK,CAACC,IAAI,GAAG,OAAO;YAC1DrE,QAAQ,CAACkE,cAAc,CAAC,WAAW,CAAE,CAACE,KAAK,CAACE,YAAY,GAAG,iBAAiB;YAC5EtE,QAAQ,CAACkE,cAAc,CAAC,YAAY,CAAE,CAACE,KAAK,CAACE,YAAY,GAAG,iBAAiB;YAC7EtE,QAAQ,CAACkE,cAAc,CAAC,YAAY,CAAE,CAACE,KAAK,CAACG,KAAK,GAAG,OAAO;UAC9D;UACA,MAAMxC,GAAG,GAAG,IAAI,CAACjC,MAAM,CAAC0E,aAAa,CAAC,CAAC,gBAAgB,CAAC,EAAE;YAAEC,UAAU,EAAE,IAAI,CAACtE;UAAc,CAAE,CAAC,CAACuE,QAAQ,EAAE;UACzG,IAAI,CAACxE,QAAQ,CAACyE,EAAE,CAAC5C,GAAG,CAAC;QACvB,CAAC,MAAM;UACL,IAAI,IAAI,CAACvB,QAAQ,EAAE;YACjBR,QAAQ,CAACkE,cAAc,CAAC,WAAW,CAAE,CAACE,KAAK,CAACC,IAAI,GAAG,GAAG;YACtDrE,QAAQ,CAACkE,cAAc,CAAC,WAAW,CAAE,CAACE,KAAK,CAACE,YAAY,GAAG,iBAAiB;YAC5EtE,QAAQ,CAACkE,cAAc,CAAC,YAAY,CAAE,CAACE,KAAK,CAACE,YAAY,GAAG,iBAAiB;YAC7EtE,QAAQ,CAACkE,cAAc,CAAC,YAAY,CAAE,CAACE,KAAK,CAACG,KAAK,GAAG,GAAG;UAC1D;UACA,MAAMxC,GAAG,GAAG,IAAI,CAACjC,MAAM,CAAC0E,aAAa,CAAC,CAAC,aAAa,CAAC,EAAE;YAAEC,UAAU,EAAE,IAAI,CAACtE;UAAc,CAAE,CAAC,CAACuE,QAAQ,EAAE;UACtG,IAAI,CAACxE,QAAQ,CAACyE,EAAE,CAAC5C,GAAG,CAAC;QACvB;MACF;MACA,IAAI,CAAC7C,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;MACpC,IAAI,CAACmB,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACxC;IAEAzF,QAAQA,CAAA;MACN,MAAMK,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC,IAAI,CAACA,YAAY,CAACiC,MAAM,GAAG,CAAC,CAAC;MAEpE,IAAI,CAACjC,YAAY,CAACiC,MAAM,GAAG,CAAC;MAC5B,IAAI,CAACjC,YAAY,CAAC2J,IAAI,CAAC3J,YAAY,CAAC;MACpC,IAAI,CAACA,YAAY,EAAE;QACjB,IAAI,CAACA,YAAY,CAACiC,MAAM,GAAG,CAAC;MAC9B;MACA,IAAI,CAAC2H,UAAU,EAAE;MACjB,IAAI,CAAC5D,UAAU,GAAG,KAAK;IACzB;IAEA4D,UAAUA,CAAA;MACR,IAAI,IAAI,CAAC5J,YAAY,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;QACtC,IAAI,CAACb,iBAAiB,GAAG,IAAI;QAC7B,IAAI,CAACJ,mBAAmB,GAAG,qBAAqB;MAClD,CAAC,MAAM;QACL,IAAI,CAACI,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACJ,mBAAmB,GAAG,qBAAqB;MAClD;IACF;IAEA8K,YAAYA,CAACC,OAAwB;MACnC,IAAIA,OAAO,IAAIA,OAAO,CAACC,KAAK,IAAI,CAACD,OAAO,CAACC,KAAK,CAACC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC/H,MAAM,EAAE;QACxE6H,OAAO,CAACG,QAAQ,CAAC,EAAE,CAAC;MACtB;MACA,OAAO,IAAI;IACb;IAEAC,wBAAwBA,CAACC,IAAY,EAAEC,WAAmB;MACxD,MAAMN,OAAO,GAAGK,IAAI,CAACE,QAAQ,CAACD,WAAW,CAAC;MAC1CN,OAAO,CAACG,QAAQ,CAACH,OAAO,CAACC,KAAK,CAACO,IAAI,EAAE,CAAC;MACtCR,OAAO,CAACS,aAAa,CAAC,CAAClN,UAAU,CAACyI,QAAQ,EAAE,IAAI,CAAC+D,YAAY,CAAC,CAAC;MAC/DC,OAAO,CAACU,sBAAsB,EAAE;IAClC;IAEA3H,OAAOA,CAACsH,IAAY;MAClB,IAAI,CAACD,wBAAwB,CAACC,IAAI,EAAE,OAAO,CAAC;MAC5C,IAAI,CAACD,wBAAwB,CAACC,IAAI,EAAE,UAAU,CAAC;MAC/C,IAAI,CAACzI,UAAU,GAAG,IAAI,CAAC+I,UAAU,CAACN,IAAI,CAAC;MACvC,MAAMO,SAAS,GAAGP,IAAI,CAACJ,KAAK;MAC5BzD,OAAO,CAACC,GAAG,CAAC4D,IAAI,CAACJ,KAAK,CAAC;MACvB,IAAII,IAAI,CAACrI,OAAO,EAAE;QAChB,IAAI,CAACwC,YAAY,CAACqG,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACF;MACF;MAEA,IAAI,CAAC5G,SAAS,GAAG,IAAI;MACrB,IAAI6G,iBAAiB,GAAW,KAAK;MACrC,IAAIC,KAAK,GAAkB,IAAI;MAC/B,IAAIC,SAAS,GAAQ,IAAI;MACzB,MAAMC,QAAQ,GAAG,IAAI,CAAC/F,IAAI,CAAC6B,GAAG,CAAC,IAAI,CAACvC,WAAW,CAAC0G,KAAK,CAAChB,IAAI,CAACJ,KAAK,CAACrE,KAAK,CAAC4E,IAAI,EAAE,EAAEH,IAAI,CAACJ,KAAK,CAACnJ,QAAQ,CAAC0J,IAAI,EAAE,CAAC,CACvG9C,IAAI,CACH5J,SAAS,CAAEwN,QAAa,IAAI;QAC1BJ,KAAK,GAAGI,QAAQ,CAACJ,KAAK;QACtBC,SAAS,GAAG,IAAI,CAACxG,WAAW,CAAC4G,qBAAqB,CAACL,KAAM,CAAC,CAAC,CAAC;QAC5D,IAAI,CAACvG,WAAW,CAAC6G,QAAQ,CAACN,KAAM,CAAC;QACjC,IAAIA,KAAK,EAAE;UACTD,iBAAiB,GAAG,KAAK;UACzB;UACA,IAAI,CAACtG,WAAW,CAAC8G,YAAY,CAACR,iBAAiB,CAAC;UAChD,IAAI,CAACS,MAAM,GAAGJ,QAAQ,CAACK,EAAE;UACzB,IAAI,CAACC,IAAI,GAAGN,QAAQ;UACpB,IAAIH,SAAS,CAACU,IAAI,KAAK7N,QAAQ,CAAC8N,OAAO,EAAE;YAEvC,OAAO,IAAI,CAACC,8BAA8B,CAACT,QAAQ,CAAC;YACpD;UACF,CAAC,MAAM;YACL,IAAI,CAAC3G,WAAW,CAACqH,kBAAkB,CAACC,IAAI,CAAC,IAAI,CAAC;YAC9C,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;YACtB,MAAMC,cAAc,GAAG,IAAID,IAAI,CAACD,GAAG,CAACG,OAAO,EAAE,GAAGpB,iBAAiB,GAAG,IAAI,CAAC;YACzE,IAAI,CAACtG,WAAW,CAAC2H,YAAY,CAACpB,KAAM,EAAEkB,cAAc,EAAE,IAAI,CAACV,MAAO,EAAE,IAAI,CAACE,IAAK,EAAET,SAAS,CAACU,IAAI,CAAC;YAC/F;YACA,OAAQ,IAAI,CAACU,uBAAuB,EAAE;UACxC;QACF,CAAC,MAAM;UACL,OAAO1O,EAAE,CAAC,IAAI,CAAC;QACjB;MACF,CAAC,CAAC,CACH,CAACuJ,SAAS,CAAEC,GAAQ,IAAI;QACvBb,OAAO,CAACC,GAAG,CAACmE,SAAS,CAAC;QACtB,IAAI4B,CAAC,GAAS,EAAU;QACxB;QACA;QACA,IAAIrB,SAAS,CAACU,IAAI,KAAK7N,QAAQ,CAAC8N,OAAO,EAAE;UACvC,IAAIU,CAAC,GAAS;YAAE,GAAG,IAAI,CAACZ,IAAI;YAAEa,YAAY,EAAEpF,GAAG,CAAC,CAAC,CAAC;YAAEqF,GAAG,EAAErF,GAAG,CAAC,CAAC;UAAC,CAAU;QAC3E,CAAC,MAAM;UAELb,OAAO,CAACC,GAAG,CAACY,GAAG,CAAC;UAChB;UACA,IAAI,CAACvC,mBAAmB,CAAC6H,iBAAiB,CAACtF,GAAG,CAAC,CAAC,CAAC,CAAC;UAClD;QACF;QACA;QACA,IAAI,CAAC1C,WAAW,CAACqH,kBAAkB,CAACC,IAAI,CAAC,IAAI,CAAC;QAC9C,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;QACtB,MAAMC,cAAc,GAAG,IAAID,IAAI,CAACD,GAAG,CAACG,OAAO,EAAE,GAAGpB,iBAAiB,GAAG,IAAI,CAAC;QACzE,IAAI,CAACtG,WAAW,CAACiI,kBAAkB,CAAC,IAAI,CAAC;QACzC,IAAI,CAACjI,WAAW,CAAC2H,YAAY,CAACpB,KAAM,EAAEkB,cAAc,EAAE,IAAI,CAACV,MAAO,EAAE,IAAI,CAACE,IAAK,EAAET,SAAS,CAACU,IAAI,CAAC;QAC/F,IAAI,CAACzH,SAAS,GAAG,KAAK;QACtB;QAEA,IAAI+G,SAAS,CAACU,IAAI,CAACgB,WAAW,EAAE,KAAK7O,QAAQ,CAAC8N,OAAO,CAACe,WAAW,EAAE,EAAE;UACnE;UACA,IAAI,IAAI,CAACjB,IAAI,EAAEkB,MAAM,CAACD,WAAW,EAAE,KAAK5O,UAAU,CAAC8O,MAAM,CAACF,WAAW,EAAE,EAAE;YACvE,IAAI,IAAI,CAACjG,SAAS,EAAE;cAClB,IAAI,CAAC7B,MAAM,CAACiI,QAAQ,CAAC,CAAC,IAAI,CAACpG,SAAS,CAAC,CAAC;YACxC,CAAC,MAAM;cACL,IAAI,CAAC7B,MAAM,CAACkI,aAAa,CAAC,YAAY,CAAC;YACzC;YACA,OAAO,KAAK;UACd;UACA,MAAMC,WAAW,GAAGC,MAAM,CAACC,MAAM,CAAC/F,GAAG,CAAC,CAAC,CAAC,CAAC;UACzC,MAAMgG,QAAQ,GAAGH,WAAW,CAACI,KAAK,CAACrD,KAAK,IAAIA,KAAK,KAAK,KAAK,CAAC;UAC5DzD,OAAO,CAACC,GAAG,CAAC4G,QAAQ,CAAC;UACrB,IAAIA,QAAQ,EAAE;YACZ,IAAI,CAACtI,MAAM,CAACkI,aAAa,CAAC,eAAe,CAAC;YAC1C,OAAO,KAAK;UACd,CAAC,MAAM;YACL,IAAI,CAAClI,MAAM,CAACkI,aAAa,CAAC,IAAI,CAACpI,yBAAyB,CAAC0I,6BAA6B,CAAClG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/F,OAAO,KAAK;UACd;QACF;QAEA;QACA,IAAI,IAAI,CAACT,SAAS,EAAE;UAClB,IAAI,CAAC7B,MAAM,CAACiI,QAAQ,CAAC,CAAC,IAAI,CAACpG,SAAS,CAAC,CAAC;QACxC,CAAC,MAAM;UACL,IAAI,CAAC7B,MAAM,CAACkI,aAAa,CAAC,YAAY,CAAC;QACzC;QACA,OAAO,KAAK;MACd,CAAC,EAAEO,KAAK,IAAG;QACT,IAAI,CAACpJ,SAAS,GAAG,KAAK;MACxB,CAAC,CAAC,CAAC;IAGP;IAEA1B,QAAQA,CAAC2H,IAAY;MACnB,IAAI,CAACzI,UAAU,GAAG,IAAI,CAAC+I,UAAU,CAACN,IAAI,CAAC;MACvC,IAAIA,IAAI,CAACrI,OAAO,EAAE;QAChB,IAAI,CAACwC,YAAY,CAACqG,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACF;MACF;MAEA,IAAI,CAACxG,YAAY,CAACqG,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;MACF,IAAI,CAACrG,WAAW,CAAC8I,MAAM,CAACpD,IAAI,CAACJ,KAAK,CAACrE,KAAK,CAAC;MACzCyE,IAAI,CAACqD,KAAK,EAAE;IACd;IAEAlN,QAAQA,CAAC6J,IAAY;MACnB,IAAIsD,YAAY,GAAG,IAAI,CAACtO,iBAAiB,GAAG,iBAAiB,GAAG,iBAAiB;MACjF,IAAI,CAACuC,UAAU,GAAG,IAAI,CAAC+I,UAAU,CAACN,IAAI,CAAC;MACvC,IAAI,CAACnI,YAAY,GAAG,IAAI,CAAC0L,eAAe,CAACvD,IAAI,CAACJ,KAAK,CAACnJ,QAAQ,EAAEuJ,IAAI,CAACJ,KAAK,CAACpE,OAAO,CAAC;MAEjF,IAAIgI,aAAa,GAAG,EAAE;MACtB,KAAK,MAAMvD,WAAW,IAAID,IAAI,CAACE,QAAQ,EAAE;QACvC,IAAIF,IAAI,CAACE,QAAQ,CAACuD,cAAc,CAACxD,WAAW,CAAC,EAAE;UAC7C,MAAMN,OAAO,GAAGK,IAAI,CAACE,QAAQ,CAACD,WAAW,CAAC;UAC1C,IAAIN,OAAO,CAAChI,OAAO,EAAE;YACnB6L,aAAa,IAAI,GAAGvD,WAAW,IAAI;UACrC;QACF;MACF;MAEA,IAAIyD,aAAa,GAAG,oDAAoD;MACxE,IAAIF,aAAa,KAAK,EAAE,EAAE;QACxBA,aAAa,GAAGA,aAAa,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAE5C,IAAIH,aAAa,KAAK,EAAE,EAAE;UACxBE,aAAa,IAAI,4CAA4CF,aAAa,EAAE;QAC9E;MACF;MACA,IAAI,CAAC,IAAI,CAACvM,WAAW,EAAE;QACrByM,aAAa,IAAI,0CAA0C;MAC7D;MACA,IAAI,CAAC,IAAI,CAAC3H,eAAe,EAAE,EAAE;QAC3B2H,aAAa,IAAI,0CAA0C;MAC7D;MAEA,IAAI1D,IAAI,CAACrI,OAAO,IAAI,CAAC,IAAI,CAACV,WAAW,IAAI,IAAI,CAACY,YAAY,IAAI,CAAC,IAAI,CAACkE,eAAe,EAAE,EAAE;QACrF,IAAI,CAAC5B,YAAY,CAACqG,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,MAAM;UAChBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE+C;SACT,CAAC;QACF;MACF;MACA,IAAI,CAAC7O,iBAAiB,GAAG,IAAI;MAC7B,IAAI,CAACyF,WAAW,CAACsJ,UAAU,CAAC5D,IAAI,CAACJ,KAAK,CAACrE,KAAK,EAAEyE,IAAI,CAACJ,KAAK,CAACnJ,QAAQ,EAAE6M,YAAY,CAAC,CAC7EvG,SAAS,CAAEC,GAAG,IAAI;QACjB,IAAI,CAAC1C,WAAW,CAACuJ,qCAAqC,CAAC,IAAI,CAAC;QAC5D,IAAI,CAAChP,iBAAiB,GAAG,KAAK;MAChC,CAAC,EAAEsO,KAAK,IAAG;QACT,IAAI,CAACtO,iBAAiB,GAAG,KAAK;QAC9B,IAAI,CAACyF,WAAW,CAACqH,kBAAkB,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/C5B,IAAI,CAACqD,KAAK,EAAE;MACd,CAAC,CAAC;IACN;IAEOS,sBAAsBA,CAAA;MAC3B,IAAI,CAAC9O,iBAAiB,GAAG,IAAI;IAC/B;IAEO+O,sBAAsBA,CAAA;MAC3B,IAAI,CAAC/O,iBAAiB,GAAG,KAAK;IAChC;IAEAsL,UAAUA,CAACN,IAAY;MACrB,IAAIA,IAAI,CAACJ,KAAK,CAACrE,KAAK,EAAE;QACpB,IAAI+E,UAAU,GAAGN,IAAI,CAACJ,KAAK,CAACrE,KAAK,CAAC4E,IAAI,EAAE,CACrCqC,WAAW,EAAE,CACbwB,KAAK,CACJ,gBAAgB,CACjB;QACH,IAAI1D,UAAU,KAAK,IAAI,IAAIN,IAAI,CAACiE,OAAO,EAAE;UACvC,OAAO,IAAI;QACb;MACF;MACA,OAAO,KAAK;IACd;IAEAV,eAAeA,CAAC9M,QAAgB,EAAE+E,OAAe;MAC/C,OAAO/E,QAAQ,KAAK+E,OAAO;IAC7B;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEAjF,yBAAyBA,CAAC2N,GAAkB;MAC1C,IAAI,CAAC5I,iBAAiB,CAAC4I,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC5I,iBAAiB,CAAC4I,GAAG,CAAC;IAC5D;IAEA1M,oBAAoBA,CAAC0M,GAAkB;MACrC,OAAO,IAAI,CAAC5I,iBAAiB,CAAC4I,GAAG,CAAC;IACpC;IAEQxC,8BAA8BA,CAACT,QAAa;MAClD,IAAIkD,SAAS,GAAG,IAAI,CAAC5J,WAAW,CAAC6J,mBAAmB,CAACnD,QAAQ,CAACK,EAAE,CAAC;MACjE,IAAI+C,SAAS,GAAG,IAAI,CAAC/J,WAAW,CAACgK,gBAAgB,EAAE;MACnD,IAAIC,SAAS,GAAG,IAAI,CAAC/J,yBAAyB,CAACgK,gCAAgC,EAAE;MACjF;MACA,OAAOjR,QAAQ,CAAC,CAAC4Q,SAAS,EAAEE,SAAS,EAAEE,SAAS,CAAC,CAAC;IACpD;IAGQrC,uBAAuBA,CAAA;MAC7B,IAAImC,SAAS,GAAG,IAAI,CAAC5J,mBAAmB,CAACgK,yBAAyB,EAAE;MACpE;MACA,OAAOlR,QAAQ,CAAC,CAAC8Q,SAAS,CAAC,CAAC;IAC9B;IAEA;;;IAGAK,gBAAgBA,CAAA;MACd,MAAMC,KAAK,GAAG,IAAI;MAClB,MAAMC,mBAAmB,GAAG,EAAE,CAAC,CAAC;MAChC,MAAMC,eAAe,GAAG,6BAA6B;MAErDC,UAAU,CAAC,MAAK;QACd,IAAIxD,EAAE,GAAG1G,QAAQ,CAACkE,cAAc,CAAC,eAAe,CAAC;QACjD,IAAI,CAACwC,EAAE,EAAE;UACPqD,KAAK,CAAClH,UAAU,CAACoH,eAAe,CAAC;QACnC;QACAF,KAAK,CAAC3K,0BAA0B,GAAG,IAAI;MACzC,CAAC,EAAE4K,mBAAmB,CAAC;IACzB;IAEApL,qBAAqBA,CAAA;MACnB,MAAMuL,SAAS,GAAGnK,QAAQ,CAACkE,cAAc,CAAC,WAAW,CAAC;MACtDiG,SAAU,CAACC,SAAS,CAACC,MAAM,CAAC,oBAAoB,CAAC;MAEjD,IAAI,CAACF,SAAU,CAACC,SAAS,CAACE,QAAQ,CAAC,oBAAoB,CAAC,EAAE;QAExD,MAAMvI,GAAG,GAAG,IAAI,CAACjC,MAAM,CAAC0E,aAAa,CAAC,CAAC,gBAAgB,CAAC,EAAE;UAAEC,UAAU,EAAE,IAAI,CAACtE;QAAc,CAAE,CAAC,CAACuE,QAAQ,EAAE;QACzG,IAAI,CAACxE,QAAQ,CAACyE,EAAE,CAAC5C,GAAG,CAAC;MACvB,CAAC,MAAM;QAEL,MAAMA,GAAG,GAAG,IAAI,CAACjC,MAAM,CAAC0E,aAAa,CAAC,CAAC,aAAa,CAAC,EAAE;UAAEC,UAAU,EAAE,IAAI,CAACtE;QAAc,CAAE,CAAC,CAACuE,QAAQ,EAAE;QACtG,IAAI,CAACxE,QAAQ,CAACyE,EAAE,CAAC5C,GAAG,CAAC;MACvB;IACF;IAEAxF,oBAAoBA,CAACgO,eAAuB;MAC1ChJ,OAAO,CAACC,GAAG,CAAC,mCAAmC+I,eAAe,EAAE,CAAC;MACjE,IAAI,CAACpJ,eAAe,CAACqJ,GAAG,CAAC,IAAI,CAAC;IAChC;IAEQ7G,iCAAiCA,CAAC8G,EAAc;MACtD,MAAMC,YAAY,GAAG;wHAC+F;MACpHD,EAAE,CAACE,aAAa,CAACC,SAAS,GAAGF,YAAY;IAC3C;IAEQG,0BAA0BA,CAACxE,QAAa;MAC9C,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAAC3G,WAAW,CAACoL,gBAAgB,CAACN,GAAG,CAAC,IAAI,CAAC;QAC3C,IAAI,CAACpK,IAAI,CAAC6B,GAAG,CAAC,IAAI,CAACxC,WAAW,CAACyC,eAAe,EAAE,CAACC,SAAS,CAAC;UACzD6E,IAAI,EAAGL,IAAI,IAAI,CACf,CAAC;UACD4B,KAAK,EAAGA,KAAK,IAAK,IAAI,CAACwC,mBAAmB,CAACxC,KAAK;SACjD,CAAC,CAAC;MACL;IACF;IAEQwC,mBAAmBA,CAACxC,KAAU;MACpChH,OAAO,CAACgH,KAAK,CAACA,KAAK,CAAC;MACpByC,KAAK,CAACzC,KAAK,CAAC;IACd;IAEQ0C,uBAAuBA,CAAC7F,IAAS;MACvC,IAAI,CAAC3F,WAAW,CAACyL,8BAA8B,CAAC9F,IAAI,CAACzE,KAAK,CAAC,CAACwK,IAAI,CAAEC,MAAM,IAAI;QAC1E,IAAIA,MAAM,EAAE;UACV,IAAI,CAAChL,IAAI,CAAC6B,GAAG,CAAC,IAAI,CAACxC,WAAW,CAAC4L,kBAAkB,CAACjG,IAAI,CAACzE,KAAK,EAAEyE,IAAI,CAACvJ,QAAQ,CAAC,CAACsG,SAAS,CAAC;YACrF6E,IAAI,EAAG5E,GAAG,IAAK,IAAI,CAACyI,0BAA0B,CAACzI,GAAG,CAAC;YACnDmG,KAAK,EAAGA,KAAK,IAAK,IAAI,CAACwC,mBAAmB,CAACxC,KAAK;WACjD,CAAC,CAAC;QACL,CAAC,MAAM;UACL,IAAI,CAACnI,IAAI,CAAC6B,GAAG,CAAC,IAAI,CAACxC,WAAW,CAAC6L,kBAAkB,CAAClG,IAAI,CAACzE,KAAK,EAAEyE,IAAI,CAACvJ,QAAQ,CAAC,CAACsG,SAAS,CAAC;YACrF6E,IAAI,EAAG5E,GAAG,IAAK,IAAI,CAACyI,0BAA0B,CAACzI,GAAG,CAAC;YACnDmG,KAAK,EAAGA,KAAK,IAAK,IAAI,CAACwC,mBAAmB,CAACxC,KAAK;WACjD,CAAC,CAAC;QACL;MACF,CAAC,CAAC,CAACgD,KAAK,CAAEhD,KAAK,IAAK,IAAI,CAACwC,mBAAmB,CAACxC,KAAK,CAAC,CAAC;IACtD;IAAC,QAAAiD,CAAA,G;uBA7gBUnM,cAAc,EAAA9F,EAAA,CAAAkS,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAApS,EAAA,CAAAkS,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAtS,EAAA,CAAAkS,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAAxS,EAAA,CAAAkS,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA1S,EAAA,CAAAkS,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAA5S,EAAA,CAAAkS,iBAAA,CAAAW,EAAA,CAAAC,yBAAA,GAAA9S,EAAA,CAAAkS,iBAAA,CAAAa,EAAA,CAAAC,mBAAA,GAAAhT,EAAA,CAAAkS,iBAAA,CAAAe,EAAA,CAAAC,MAAA,GAAAlT,EAAA,CAAAkS,iBAAA,CAAAlS,EAAA,CAAAmT,MAAA,GAAAnT,EAAA,CAAAkS,iBAAA,CAgDfxT,QAAQ,GAAAsB,EAAA,CAAAkS,iBAAA,CAAAlS,EAAA,CAAAoT,SAAA,GAAApT,EAAA,CAAAkS,iBAAA,CAAAmB,EAAA,CAAAC,QAAA,GAAAtT,EAAA,CAAAkS,iBAAA,CAAAe,EAAA,CAAAM,cAAA;IAAA;IAAA,QAAAC,EAAA,G;YAhDP1N,cAAc;MAAA2N,SAAA;MAAAC,SAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UCsQ1B5T,EA/RD,CAAAiC,UAAA,IAAA6R,+BAAA,SAqSC,IAAAC,0CAAA;UArSD/T,EAAA,CAAAgU,OAAA,OAAAC,6BAAA,UAqSC;UArSMjU,EAAA,CAAAkU,iBAAA,OAAW;;;qBDUdzV,YAAY,EACZO,wBAAwB,EACxBE,YAAY,EACZC,cAAc,EACdP,WAAW,EACXC,mBAAmB,EACnBe,eAAe,EACfX,eAAe;MAAAkV,MAAA;MAAAC,IAAA;QAAAC,SAAA,EAKL,CAAC1U,oBAAoB,EAAED,UAAU;MAAC;IAAA;;SAGnCoG,cAAc;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}