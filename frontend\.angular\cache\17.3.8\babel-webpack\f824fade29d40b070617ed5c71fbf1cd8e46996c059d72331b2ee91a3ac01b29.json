{"ast": null, "code": "import { NavigationStart } from '@angular/router';\nimport { map, skip, switchMap, take } from 'rxjs/operators';\nimport { LessonStatus } from 'src/app/core/models/lesson.model';\nimport { UserRole } from 'src/app/core/models/user.model';\nimport { SubSink } from 'subsink';\nimport { LessonRatingCalendarDialogComponent } from '../lesson-rating-calendar-dialog/lesson-rating-calendar-dialog.component';\nimport { Status } from 'src/app/core/models/classroom.model';\nimport { forkJoin, of } from 'rxjs';\nimport { PackageDurationHours, PackageType } from 'src/app/core/models/package.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/dynamicdialog\";\nimport * as i2 from \"src/app/core/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"src/app/core/services/rating-and-report.service\";\nimport * as i5 from \"src/app/core/services/layout.service\";\nimport * as i6 from \"src/app/core/services/lesson.service\";\nimport * as i7 from \"src/app/core/services/classroom.service\";\nimport * as i8 from \"src/app/core/services/calendar.service\";\nimport * as i9 from \"src/app/core/services/general.service\";\nimport * as i10 from \"src/app/core/services/confirm-dialog.service\";\nimport * as i11 from \"src/app/core/services/toast.service\";\nimport * as i12 from \"src/app/core/services/package.service\";\nimport * as i13 from \"@angular/common\";\nimport * as i14 from \"primeng/api\";\nimport * as i15 from \"primeng/overlaypanel\";\nconst _c0 = [\"op\"];\nconst _c1 = (a0, a1) => [a0, a1];\nconst _c2 = (a0, a1, a2) => [a0, a1, a2];\nconst _c3 = a0 => ({\n  \"disabled\": a0\n});\nconst _c4 = a0 => ({\n  lesson: a0\n});\nconst _c5 = (a0, a1) => ({\n  \"disabled-button-charge\": a0,\n  \"disabled-button-not-charge\": a1\n});\nconst _c6 = a0 => ({\n  \"text-white\": a0\n});\nconst _c7 = () => ({\n  \"border-radius\": \"12px\"\n});\nfunction LessonInfoCalendarDialogComponent_ng_container_0_ng_container_5_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 7)(1, \"div\", 8);\n    i0.ɵɵtext(2, \"Participants\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 12);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.classroomService.getFullStudentNames(ctx_r1.dialogData.classroom.classroomStudents), \"\");\n  }\n}\nfunction LessonInfoCalendarDialogComponent_ng_container_0_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, LessonInfoCalendarDialogComponent_ng_container_0_ng_container_5_li_1_Template, 5, 1, \"li\", 19);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.dialogData.classroom && ctx_r1.dialogData.classroom.classroomStudents);\n  }\n}\nfunction LessonInfoCalendarDialogComponent_ng_container_0_ng_container_6_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 7)(1, \"div\", 8);\n    i0.ɵɵtext(2, \"Teacher\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 12);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r1.dialogData.classroom.teacher.firstName, \" \", ctx_r1.dialogData.classroom.teacher.lastName, \"\");\n  }\n}\nfunction LessonInfoCalendarDialogComponent_ng_container_0_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, LessonInfoCalendarDialogComponent_ng_container_0_ng_container_6_li_1_Template, 5, 2, \"li\", 19);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.dialogData.classroom.teacher);\n  }\n}\nfunction LessonInfoCalendarDialogComponent_ng_container_0_li_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 11)(1, \"div\", 8);\n    i0.ɵɵtext(2, \"Level\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 9);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.dialogData.classroom.activeLevel ? ctx_r1.dialogData.classroom.activeLevel : ctx_r1.dialogData.lesson.classroom.activeLevel);\n  }\n}\nfunction LessonInfoCalendarDialogComponent_ng_container_0_ng_container_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 15)(2, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function LessonInfoCalendarDialogComponent_ng_container_0_ng_container_31_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.generalService.goToBellbirdLink(ctx_r1.dialogData.lesson));\n    });\n    i0.ɵɵelementStart(3, \"div\", 17)(4, \"span\", 21);\n    i0.ɵɵtext(5, \" JOIN LESSON\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.generalService.isDifferenceWithin30Minutes(ctx_r1.dialogData.lesson.startingDate.toString(), ctx_r1.generalService.getCurrentTimeInTimezone(ctx_r1.user.timeZone), ctx_r1.dialogData.lesson))(\"pTooltip\", !ctx_r1.generalService.isDifferenceWithin30Minutes(ctx_r1.dialogData.lesson.startingDate.toString(), ctx_r1.generalService.getCurrentTimeInTimezone(ctx_r1.user.timeZone), ctx_r1.dialogData.lesson) ? \"You can join your lesson 30 minutes before the scheduled time\" : \"\")(\"ngClass\", i0.ɵɵpureFunction1(3, _c3, !ctx_r1.generalService.isDifferenceWithin30Minutes(ctx_r1.dialogData.lesson.startingDate.toString(), ctx_r1.generalService.getCurrentTimeInTimezone(ctx_r1.user.timeZone), ctx_r1.dialogData.lesson)));\n  }\n}\nfunction LessonInfoCalendarDialogComponent_ng_container_0_ng_container_37_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 15)(2, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function LessonInfoCalendarDialogComponent_ng_container_0_ng_container_37_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openLessonRatingDialog(\"view\"));\n    });\n    i0.ɵɵelementStart(3, \"div\", 17)(4, \"span\", 18);\n    i0.ɵɵelement(5, \"img\", 22);\n    i0.ɵɵtext(6, \" View Rating\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction LessonInfoCalendarDialogComponent_ng_container_0_ng_container_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 15)(2, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function LessonInfoCalendarDialogComponent_ng_container_0_ng_container_38_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openLessonRatingDialog(\"edit\"));\n    });\n    i0.ɵɵelementStart(3, \"div\", 17)(4, \"span\", 18);\n    i0.ɵɵelement(5, \"img\", 23);\n    i0.ɵɵtext(6, \" Edit Rating\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction LessonInfoCalendarDialogComponent_ng_container_0_ng_container_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 15);\n    i0.ɵɵelement(2, \"div\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const chargeTimeSpan_r6 = i0.ɵɵreference(42);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", chargeTimeSpan_r6)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, ctx_r1.dialogData.lesson));\n  }\n}\nfunction LessonInfoCalendarDialogComponent_ng_container_0_ng_container_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"div\", 24);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const chargeTimeSpan_r6 = i0.ɵɵreference(42);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", chargeTimeSpan_r6)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c4, ctx_r1.dialogData.lesson));\n  }\n}\nfunction LessonInfoCalendarDialogComponent_ng_container_0_ng_template_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function LessonInfoCalendarDialogComponent_ng_container_0_ng_template_41_Template_button_click_0_listener() {\n      const lesson_r8 = i0.ɵɵrestoreView(_r7).lesson;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(!lesson_r8.chargeTime && ctx_r1.role === ctx_r1.UserRoles.TEACHER ? ctx_r1.chargeTimeSelected() : null);\n    });\n    i0.ɵɵelementStart(1, \"div\", 17)(2, \"span\", 26);\n    i0.ɵɵelement(3, \"img\", 27);\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const lesson_r8 = ctx.lesson;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", lesson_r8.chargeTime || ctx_r1.role === ctx_r1.UserRoles.STUDENT)(\"pTooltip\", lesson_r8.chargeTime ? \"This lesson has already been charged.\" : \"\")(\"ngClass\", i0.ɵɵpureFunction2(5, _c5, lesson_r8.chargeTime, !lesson_r8.chargeTime && ctx_r1.role === ctx_r1.UserRoles.STUDENT));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c6, !lesson_r8.chargeTime && ctx_r1.role === ctx_r1.UserRoles.TEACHER));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", !lesson_r8.chargeTime && ctx_r1.role === ctx_r1.UserRoles.TEACHER ? \"Charge Time\" : !lesson_r8.chargeTime && ctx_r1.role === ctx_r1.UserRoles.STUDENT ? \"Not Charged\" : \"Already Charged\", \" \");\n  }\n}\nfunction LessonInfoCalendarDialogComponent_ng_container_0_ng_container_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 15)(2, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function LessonInfoCalendarDialogComponent_ng_container_0_ng_container_43_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onRescheduleSelected());\n    });\n    i0.ɵɵelementStart(3, \"div\", 17)(4, \"span\", 29);\n    i0.ɵɵelement(5, \"img\", 30);\n    i0.ɵɵtext(6, \" Edit Lesson\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction LessonInfoCalendarDialogComponent_ng_container_0_ng_container_44_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 15)(2, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function LessonInfoCalendarDialogComponent_ng_container_0_ng_container_44_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onStudentCancelSelected());\n    });\n    i0.ɵɵelementStart(3, \"div\", 17)(4, \"span\", 32);\n    i0.ɵɵelement(5, \"img\", 33);\n    i0.ɵɵtext(6, \" Cancel\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction LessonInfoCalendarDialogComponent_ng_container_0_ng_container_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 15)(2, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function LessonInfoCalendarDialogComponent_ng_container_0_ng_container_45_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onLessonAcceptedClicked());\n    });\n    i0.ɵɵelementStart(3, \"div\", 34)(4, \"span\", 32);\n    i0.ɵɵelement(5, \"img\", 35);\n    i0.ɵɵtext(6, \" Approve\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction LessonInfoCalendarDialogComponent_ng_container_0_ng_container_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 15)(2, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function LessonInfoCalendarDialogComponent_ng_container_0_ng_container_46_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onLessonRejectedClicked());\n    });\n    i0.ɵɵelementStart(3, \"div\", 34)(4, \"span\", 18);\n    i0.ɵɵelement(5, \"i\", 36);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.role === ctx_r1.UserRoles.TEACHER ? \"Reject\" : \"Remove\", \"\");\n  }\n}\nfunction LessonInfoCalendarDialogComponent_ng_container_0_ng_container_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 15)(2, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function LessonInfoCalendarDialogComponent_ng_container_0_ng_container_47_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onLessonDeleteSelected());\n    });\n    i0.ɵɵelementStart(3, \"div\", 38)(4, \"span\", 18);\n    i0.ɵɵelement(5, \"i\", 39);\n    i0.ɵɵelementStart(6, \"div\");\n    i0.ɵɵtext(7, \"Delete\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_ng_template_9_li_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 47);\n    i0.ɵɵlistener(\"click\", function LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_ng_template_9_li_1_Template_li_click_0_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.changeLessonStatus(ctx_r1.lessonStatus.COMPLETED));\n    });\n    i0.ɵɵelementStart(1, \"span\", 48);\n    i0.ɵɵelement(2, \"img\", 49);\n    i0.ɵɵelementStart(3, \"span\", 50);\n    i0.ɵɵtext(4, \" Complete \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"disableTime\", 2000);\n  }\n}\nfunction LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_ng_template_9_li_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r17 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 47);\n    i0.ɵɵlistener(\"click\", function LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_ng_template_9_li_2_Template_li_click_0_listener() {\n      i0.ɵɵrestoreView(_r17);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.changeLessonStatus(ctx_r1.lessonStatus.REARRANGE));\n    });\n    i0.ɵɵelementStart(1, \"span\", 48);\n    i0.ɵɵelement(2, \"img\", 51);\n    i0.ɵɵelementStart(3, \"span\", 50);\n    i0.ɵɵtext(4, \" Rearrange \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"disableTime\", 2000);\n  }\n}\nfunction LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_ng_template_9_li_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 47);\n    i0.ɵɵlistener(\"click\", function LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_ng_template_9_li_3_Template_li_click_0_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.changeLessonStatus(ctx_r1.lessonStatus.NO_SHOW));\n    });\n    i0.ɵɵelementStart(1, \"span\", 48);\n    i0.ɵɵelement(2, \"img\", 52);\n    i0.ɵɵelementStart(3, \"span\", 50);\n    i0.ɵɵtext(4, \" No Show \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"disableTime\", 2000);\n  }\n}\nfunction LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_ng_template_9_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 47);\n    i0.ɵɵlistener(\"click\", function LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_ng_template_9_li_4_Template_li_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.changeLessonStatus(ctx_r1.lessonStatus.CANCELED));\n    });\n    i0.ɵɵelementStart(1, \"span\", 48);\n    i0.ɵɵelement(2, \"img\", 53);\n    i0.ɵɵelementStart(3, \"span\", 50);\n    i0.ɵɵtext(4, \" Cancel \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"disableTime\", 2000);\n  }\n}\nfunction LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\", 44);\n    i0.ɵɵtemplate(1, LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_ng_template_9_li_1_Template, 5, 1, \"li\", 45)(2, LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_ng_template_9_li_2_Template, 5, 1, \"li\", 46)(3, LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_ng_template_9_li_3_Template, 5, 1, \"li\", 46)(4, LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_ng_template_9_li_4_Template, 5, 1, \"li\", 46);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.lessonService.getStatusNameFromStatusString(ctx_r1.dialogData.lesson.status) !== ctx_r1.lessonStatus.CANCELED && !ctx_r1.lessonService.getStatusNameFromStatusString(ctx_r1.dialogData.lesson.status).includes(ctx_r1.lessonStatus.NO_SHOW.toLowerCase()) && !ctx_r1.lessonService.getStatusNameFromStatusString(ctx_r1.dialogData.lesson.status).includes(ctx_r1.lessonStatus.COMPLETED.toLowerCase()));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.lessonService.getStatusNameFromStatusString(ctx_r1.dialogData.lesson.status).includes(ctx_r1.lessonStatus.ARRANGED));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.lessonService.getStatusNameFromStatusString(ctx_r1.dialogData.lesson.status).includes(ctx_r1.lessonStatus.NO_SHOW.toLowerCase()) && !ctx_r1.lessonService.getStatusNameFromStatusString(ctx_r1.dialogData.lesson.status.toLowerCase()).includes(ctx_r1.lessonStatus.CANCELED.toLowerCase()));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.lessonService.getStatusNameFromStatusString(ctx_r1.dialogData.lesson.status) !== ctx_r1.lessonStatus.CANCELED);\n  }\n}\nfunction LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 15)(2, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_Template_button_click_2_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const op_r15 = i0.ɵɵreference(8);\n      return i0.ɵɵresetView(op_r15.toggle($event));\n    });\n    i0.ɵɵelementStart(3, \"div\", 17)(4, \"span\", 18);\n    i0.ɵɵelement(5, \"img\", 41);\n    i0.ɵɵtext(6, \" Change Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p-overlayPanel\", 42, 1);\n    i0.ɵɵtemplate(9, LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_ng_template_9_Template, 5, 4, \"ng-template\", 43);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(7);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(2, _c7));\n  }\n}\nfunction LessonInfoCalendarDialogComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"app-block-viewer\", 3);\n    i0.ɵɵlistener(\"closeDialogEvent\", function LessonInfoCalendarDialogComponent_ng_container_0_Template_app_block_viewer_closeDialogEvent_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onDialogClose());\n    });\n    i0.ɵɵelementStart(2, \"div\", 4);\n    i0.ɵɵelement(3, \"img\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"ul\", 6);\n    i0.ɵɵtemplate(5, LessonInfoCalendarDialogComponent_ng_container_0_ng_container_5_Template, 2, 1, \"ng-container\", 2)(6, LessonInfoCalendarDialogComponent_ng_container_0_ng_container_6_Template, 2, 1, \"ng-container\", 2);\n    i0.ɵɵelementStart(7, \"li\", 7)(8, \"div\", 8);\n    i0.ɵɵtext(9, \"Language\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 9);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, LessonInfoCalendarDialogComponent_ng_container_0_li_12_Template, 5, 1, \"li\", 10);\n    i0.ɵɵelementStart(13, \"li\", 11)(14, \"div\", 8);\n    i0.ɵɵtext(15, \"Starts at\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 12);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"li\", 11)(19, \"div\", 8);\n    i0.ɵɵtext(20, \"Duration\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 12);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"li\", 11)(24, \"div\", 8);\n    i0.ɵɵtext(25, \"Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 12);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"div\", 13)(30, \"div\", 14);\n    i0.ɵɵtemplate(31, LessonInfoCalendarDialogComponent_ng_container_0_ng_container_31_Template, 6, 5, \"ng-container\", 2);\n    i0.ɵɵelementStart(32, \"div\", 15)(33, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function LessonInfoCalendarDialogComponent_ng_container_0_Template_button_click_33_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onGoToClassroomSelected());\n    });\n    i0.ɵɵelementStart(34, \"div\", 17)(35, \"span\", 18);\n    i0.ɵɵtext(36, \" Go to Classroom\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(37, LessonInfoCalendarDialogComponent_ng_container_0_ng_container_37_Template, 7, 0, \"ng-container\", 2)(38, LessonInfoCalendarDialogComponent_ng_container_0_ng_container_38_Template, 7, 0, \"ng-container\", 2)(39, LessonInfoCalendarDialogComponent_ng_container_0_ng_container_39_Template, 3, 4, \"ng-container\", 2)(40, LessonInfoCalendarDialogComponent_ng_container_0_ng_container_40_Template, 2, 4, \"ng-container\", 2)(41, LessonInfoCalendarDialogComponent_ng_container_0_ng_template_41_Template, 6, 10, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(43, LessonInfoCalendarDialogComponent_ng_container_0_ng_container_43_Template, 7, 0, \"ng-container\", 2)(44, LessonInfoCalendarDialogComponent_ng_container_0_ng_container_44_Template, 7, 0, \"ng-container\", 2)(45, LessonInfoCalendarDialogComponent_ng_container_0_ng_container_45_Template, 7, 0, \"ng-container\", 2)(46, LessonInfoCalendarDialogComponent_ng_container_0_ng_container_46_Template, 7, 1, \"ng-container\", 2)(47, LessonInfoCalendarDialogComponent_ng_container_0_ng_container_47_Template, 8, 0, \"ng-container\", 2)(48, LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_Template, 10, 3, \"ng-container\", 2);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate1(\"headerBackgroundImage\", \"/assets/images/dashboard/calendar/calendar-dialog-lesson-info-\", ctx_r1.getLessonStatusGradImage(), \".png\");\n    i0.ɵɵproperty(\"header\", ctx_r1.dialogData.lesson.status)(\"headerClass\", \"justify-content-center my-0\")(\"headerTextClass\", \"font-base font-semibold justify-content-center capitalize\")(\"showCloseDialogIcon\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"src\", \"/assets/images/dashboard/calendar/\", ctx_r1.getLessonStatusGradImage(), \"-gradient.png\", i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.role === ctx_r1.UserRoles.TEACHER);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.role === ctx_r1.UserRoles.STUDENT);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.dialogData.classroom.language ? ctx_r1.dialogData.classroom.language : ctx_r1.dialogData.lesson.classroom.language);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.classroomService.isTrialClassroom(ctx_r1.dialogData.classroom));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.generalService.getLessonTime(ctx_r1.dialogData.lesson.startingDate));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.generalService.convertHoursToMinutesWithSuffix(ctx_r1.dialogData.lesson.duration), \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(28, 26, ctx_r1.dialogData.lesson.startingDate, \"dd/MM/yyyy\"), \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpureFunction2(29, _c1, ctx_r1.lessonStatus.ARRANGED_TRIAL, ctx_r1.lessonStatus.ARRANGED).includes(ctx_r1.lessonService.getStatusNameFromStatusString(ctx_r1.dialogData.lesson.status)));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.lessonService.getStatusNameFromStatusString(ctx_r1.dialogData.lesson.status) === ctx_r1.lessonStatus.COMPLETED && !ctx_r1.classroomService.isTrialClassroom(ctx_r1.dialogData.classroom) && ctx_r1.completedLessonHasRating);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.lessonService.getStatusNameFromStatusString(ctx_r1.dialogData.lesson.status) === ctx_r1.lessonStatus.COMPLETED && !ctx_r1.classroomService.isTrialClassroom(ctx_r1.dialogData.classroom) && ctx_r1.completedLessonHasRating && ctx_r1.role === ctx_r1.UserRoles.TEACHER);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.lessonService.getStatusNameFromStatusString(ctx_r1.dialogData.lesson.status) === ctx_r1.lessonStatus.CANCELED.toLowerCase() || ctx_r1.lessonService.getStatusNameFromStatusString(ctx_r1.dialogData.lesson.status) === ctx_r1.lessonStatus.NO_SHOW.toLowerCase()) && !ctx_r1.classroomService.isTrialClassroom(ctx_r1.dialogData.classroom) && ctx_r1.role === ctx_r1.UserRoles.TEACHER);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (ctx_r1.lessonService.getStatusNameFromStatusString(ctx_r1.dialogData.lesson.status) === ctx_r1.lessonStatus.CANCELED.toLowerCase() || ctx_r1.lessonService.getStatusNameFromStatusString(ctx_r1.dialogData.lesson.status) === ctx_r1.lessonStatus.NO_SHOW.toLowerCase()) && !ctx_r1.classroomService.isTrialClassroom(ctx_r1.dialogData.classroom) && ctx_r1.role === ctx_r1.UserRoles.STUDENT);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpureFunction3(32, _c2, ctx_r1.lessonStatus.REQUESTED, ctx_r1.lessonStatus.ARRANGED_TRIAL, ctx_r1.lessonStatus.ARRANGED).includes(ctx_r1.lessonService.getStatusNameFromStatusString(ctx_r1.dialogData.lesson.status)));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.lessonService.getStatusNameFromStatusString(ctx_r1.dialogData.lesson.status) === ctx_r1.lessonStatus.ARRANGED && ctx_r1.role === ctx_r1.UserRoles.STUDENT);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.lessonService.getStatusNameFromStatusString(ctx_r1.dialogData.lesson.status) === ctx_r1.lessonStatus.REQUESTED && ctx_r1.role === ctx_r1.UserRoles.TEACHER);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.lessonService.getStatusNameFromStatusString(ctx_r1.dialogData.lesson.status) === ctx_r1.lessonStatus.REQUESTED);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.role === ctx_r1.UserRoles.TEACHER);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.role === ctx_r1.UserRoles.TEACHER && ctx_r1.lessonService.getStatusNameFromStatusString(ctx_r1.dialogData.lesson.status) !== ctx_r1.lessonStatus.COMPLETED && ctx_r1.lessonService.getStatusNameFromStatusString(ctx_r1.dialogData.lesson.status.toLowerCase()) !== ctx_r1.lessonStatus.REQUESTED.toLowerCase() && !ctx_r1.lessonService.getStatusNameFromStatusString(ctx_r1.dialogData.lesson.status.toLowerCase()).includes(ctx_r1.lessonStatus.CANCELED.toLowerCase()) && !ctx_r1.lessonService.getStatusNameFromStatusString(ctx_r1.dialogData.lesson.status.toLowerCase()).includes(ctx_r1.lessonStatus.NO_SHOW.toLowerCase()));\n  }\n}\nexport class LessonInfoCalendarDialogComponent {\n  constructor(ref, config, authService, router, ratingAndReportService, layoutService, lessonService, classroomService, calendarService, generalService, confirmDialogService, toastService, packageService) {\n    this.ref = ref;\n    this.config = config;\n    this.authService = authService;\n    this.router = router;\n    this.ratingAndReportService = ratingAndReportService;\n    this.layoutService = layoutService;\n    this.lessonService = lessonService;\n    this.classroomService = classroomService;\n    this.calendarService = calendarService;\n    this.generalService = generalService;\n    this.confirmDialogService = confirmDialogService;\n    this.toastService = toastService;\n    this.packageService = packageService;\n    this.role = UserRole.NONE;\n    this.UserRoles = UserRole;\n    this.LessonStatuses = LessonStatus.ARRANGED;\n    this.lessonStatus = LessonStatus;\n    this.classStatus = Status.COMPLETED_TRIAL;\n    this.dialogData = {};\n    this.completedLessonHasRating = false;\n    this.subs = new SubSink();\n    this.sideMenuHeight = '100%';\n    this.packageDurationHours = PackageDurationHours;\n    this.user = {};\n  }\n  ngOnInit() {\n    this.user = this.authService.getLoggedInUser();\n    this.role = this.authService.getLoggedInUser().role;\n    this.dialogData = this.config.data.dialogData;\n    // define dialogData classroom in case is empty from variable\n    this.dialogData.classroom = !this.generalService.isNullishObject(this.dialogData.classroom) ? this.dialogData.classroom : this.dialogData.lesson.classroom;\n    console.log(this.dialogData);\n    this.initConfirmDialogListener();\n    this.initRejectDialogListener();\n    this.checkCompletedLessonRating();\n    this.subs.sink = this.router.events.subscribe(event => {\n      if (event instanceof NavigationStart) {\n        // Close the dialog reference when navigation starts\n        this.ref.close();\n      }\n    });\n    this.subs.sink = this.layoutService.sideMenuHeight.pipe(skip(1)).subscribe(res => {\n      if (res !== 0) {\n        this.sideMenuHeight = res + 'px';\n      }\n    });\n    // this.onViewRatingSelected();\n  }\n  ngOnDestroy() {\n    this.ref.close();\n    this.subs.unsubscribe();\n    this.calendarService.setUpdateListener(false);\n  }\n  onDialogClose(data) {\n    this.ref.close(data);\n  }\n  onRescheduleSelected() {\n    const navigationExtras = {\n      queryParams: {\n        'lesson': JSON.stringify(this.dialogData.lesson),\n        'reschedule': true\n      }\n    };\n    this.router.navigate(['/dashboard/calendar/booking-system'], navigationExtras);\n    this.ref.close();\n  }\n  onGoToClassroomSelected() {\n    this.generalService.navigateToLessonDetails(this.dialogData.classroom, this.authService.getUserRole());\n    this.ref.close();\n  }\n  openLessonRatingDialog(ratingMode = 'create') {\n    console.log(this.dialogData);\n    const mainContentColumnElement = document.getElementById('mainContentColumn');\n    console.log(mainContentColumnElement.getBoundingClientRect().width);\n    this.generalService.openDialogWithComponent(this.dialogData.dialogService, LessonRatingCalendarDialogComponent, mainContentColumnElement.getBoundingClientRect().width, {\n      lesson: this.dialogData.lesson,\n      classroom: this.dialogData.classroom,\n      ratingMode: ratingMode\n    }, 'mainContentColumn', result => {\n      console.log(result);\n      // if (result && result.action === 'shareWithMultiple') {\n      //   this.shareLibraryFiles(result.map.map);\n      // }\n    }, this.sideMenuHeight);\n    this.ref.close();\n  }\n  /**\n   * Changes the status of the lesson based on the provided status string.\n   *\n   * @param {string} statusString - The status string to change the lesson status to.\n   */\n  changeLessonStatus(statusString) {\n    // Get the lesson status from the status string\n    const status = this.getLessonStatusFromString(statusString);\n    if (!status) {\n      // Do nothing if status string not recognized\n      return;\n    }\n    // Check if the classroom is a trial classroom\n    const isTrialClassroom = this.classroomService.isTrialClassroom(this.dialogData.classroom);\n    if (status === LessonStatus.COMPLETED && !isTrialClassroom) {\n      // Open the lesson rating dialog if the status is completed and it's not a trial classroom\n      this.openLessonRatingDialog();\n      return;\n    }\n    if (status === LessonStatus.CANCELED) {\n      // Cancel the selected lesson if the status is canceled\n      this.cancelLessonSelected();\n      return;\n    }\n    // Update the lesson status\n    this.updateLesson(status);\n  }\n  /**\n   * The function `getLessonStatusFromString` takes a string representing a lesson status and returns the\n   * corresponding LessonStatus enum value or null if the string does not match any valid status.\n   * @param {string} statusString - A string representing the status of a lesson.\n   * @returns a value of type LessonStatus or null.\n   */\n  getLessonStatusFromString(statusString) {\n    const lowercaseStatus = statusString.toLowerCase();\n    switch (lowercaseStatus) {\n      case 'completed':\n        return this.classroomService.isTrialClassroom(this.dialogData.classroom) ? LessonStatus.COMPLETED_TRIAL : LessonStatus.COMPLETED;\n      case 'rearrange':\n        return this.classroomService.isTrialClassroom(this.dialogData.classroom) ? LessonStatus.ARRANGED_TRIAL : LessonStatus.ARRANGED;\n      case 'no show':\n        return LessonStatus.NO_SHOW;\n      case 'canceled':\n        return this.classroomService.isTrialClassroom(this.dialogData.classroom) ? LessonStatus.CANCELED_TRIAL : LessonStatus.CANCELED;\n      default:\n        return null;\n    }\n  }\n  /**\n   * Updates the lesson with the given status.\n   *\n   * @param {LessonStatus} status - The new status for the lesson.\n   * @return {void}\n   */\n  updateLesson(status) {\n    const {\n      id,\n      classroomId,\n      startingDate,\n      duration,\n      isRecccuring\n    } = this.dialogData.lesson;\n    // Create the updated lesson object\n    const updatedLesson = {\n      id,\n      classroomId,\n      status,\n      startingDate,\n      duration,\n      isRecccuring,\n      answered: \"accepted\"\n    };\n    this.subs.add(this.lessonService.update(updatedLesson).subscribe(() => {\n      this.hideOverlayPanel();\n      this.setUpdateListeners();\n      this.onDialogClose({\n        ...this.dialogData,\n        status\n      });\n      this.showToastMessage('success', '', 'Lesson updated.');\n    }));\n    this.updateClassroomStatusBasedOnLessonStatus(status);\n  }\n  updateClassroomStatus(newStatus) {\n    this.subs.add(this.classroomService.changeClassroomStatus(this.dialogData.classroom.id, newStatus).subscribe(response => {\n      console.log(response);\n    }));\n  }\n  cancelLessonSelected() {\n    const {\n      classroom,\n      lesson\n    } = this.dialogData;\n    const foundPackage = this.packageService.findLessonPackageById(classroom.packages, lesson.packageId) || null;\n    const lessonStartDate = lesson.startingDate;\n    if (foundPackage) {\n      const shouldBypassCancellationPolicy = this.shouldBypassCancellationPolicyBasedOnPackage(foundPackage.type.toLowerCase(), lessonStartDate);\n      if (shouldBypassCancellationPolicy) {\n        this.prepareAndShowCancelLessonDialog();\n      } else if (!shouldBypassCancellationPolicy && !this.isTrialClassroom()) {\n        this.prepareAndShowChargeAndCancelLessonDialog(foundPackage);\n      }\n      console.log(foundPackage);\n    } else {\n      this.prepareAndShowCancelLessonDialog();\n    }\n  }\n  /**\n   * Function to display a confirmation dialog for canceling a lesson.\n   */\n  studentLessonCanceledDialog() {\n    // Set properties for the confirmation dialog\n    this.confirmDialogService.setProperties({\n      confirmMessage: 'Are you sure you want to cancel this lesson?',\n      acceptBtnLabel: 'Yes',\n      rejectBtnLabel: 'No',\n      action: 'cancel-lesson',\n      confirmIcon: '/assets/icons/clock-dialog-icon.svg',\n      showHeader: true,\n      dialogType: 'custom',\n      headerClass: 'my-custom-header-class'\n    });\n    // Show the confirmation dialog\n    this.confirmDialogService.show();\n  }\n  /**\n   * Function to display a confirmation dialog for charging time.\n   */\n  chargeTimeSelected() {\n    // Set properties for the confirmation dialog\n    this.confirmDialogService.setProperties({\n      confirmMessage: 'Are you sure you want to charge time?',\n      confirmIcon: '/assets/icons/clock-dialog-icon.svg',\n      acceptBtnLabel: 'Yes',\n      rejectBtnLabel: 'No',\n      showHeader: true,\n      dialogType: 'custom',\n      action: 'charge-lesson',\n      headerClass: 'my-custom-header-class'\n    });\n    // Show the confirmation dialog\n    this.confirmDialogService.show();\n  }\n  /**\n   * Function to display a confirmation dialog for charging time based on date and package type.\n   */\n  ChargeDatePackageType() {\n    // Set properties for the confirmation dialog\n    this.confirmDialogService.setProperties({\n      confirmMessage: 'Are you sure want to charge time?',\n      confirmIcon: '/assets/icons/clock-dialog-icon.svg',\n      acceptBtnLabel: 'Yes',\n      rejectBtnLabel: 'No',\n      showHeader: true,\n      dialogType: 'custom',\n      action: 'charge-lesson',\n      headerClass: 'my-custom-header-class'\n    });\n    // Show the confirmation dialog\n    this.confirmDialogService.show();\n  }\n  /**\n   * Function to get the lesson status gradient image.\n   *\n   * @returns The lesson status gradient image.\n   */\n  getLessonStatusGradImage() {\n    return this.lessonService.getStatusNameFromStatusString(this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status)).replace(/\\s+/g, '-');\n  }\n  /**\n   * Function to handle when student cancel is selected.\n   */\n  onStudentCancelSelected() {\n    const foundPackage = this.packageService.findLessonPackageById(this.dialogData.classroom.packages, this.dialogData.lesson.packageId) || null;\n    const lessonStartDate = this.dialogData.lesson.startingDate;\n    if (foundPackage) {\n      // Checking lesson package cancellation policy\n      const shouldBypassCancelationPolicy = this.shouldBypassCancellationPolicyBasedOnPackage(foundPackage.type.toLowerCase(), lessonStartDate);\n      if (shouldBypassCancelationPolicy) {\n        this.studentLessonCanceledDialog();\n      } else if (!shouldBypassCancelationPolicy && !this.isTrialClassroom()) {\n        this.prepareAndShowChargeAndCancelLessonDialogForStudent(foundPackage);\n      }\n      console.log(foundPackage);\n    } else {\n      this.studentLessonCanceledDialog();\n    }\n  }\n  /**\n   * Function to handle when lesson accepted is clicked.\n   */\n  onLessonAcceptedClicked() {\n    const params = {\n      lessonId: this.dialogData.lesson.id\n    };\n    this.subs.add(this.classroomService.acceptLessonRequest(this.dialogData.lesson.id).subscribe(res => {\n      if (res) {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Lesson request accepted.'\n        });\n        this.calendarService.setToggleOverlayListener(true);\n        this.calendarService.setUpdateListener(true);\n        this.classroomService.setSelectedClassroomPackagesUpdate(true);\n        this.onDialogClose();\n      }\n    }));\n  }\n  /**\n   * Function to handle when lesson rejected is clicked.\n   */\n  onLessonRejectedClicked() {\n    const params = {\n      lessonId: this.dialogData.lesson.id\n    };\n    this.subs.add(this.classroomService.rejectLessonRequest(this.dialogData.lesson.id).subscribe(res => {\n      if (res) {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Lesson request removed.'\n        });\n        this.calendarService.setToggleOverlayListener(true);\n        this.calendarService.setUpdateListener(true);\n        this.classroomService.setSelectedClassroomPackagesUpdate(true);\n        this.onDialogClose();\n      }\n    }));\n  }\n  /**\n   * Function to handle when lesson delete is selected.\n   */\n  onLessonDeleteSelected() {\n    this.confirmDialogService.setProperties({\n      confirmMessage: `\n      Are you sure you want to delete this lesson?`,\n      acceptBtnLabel: 'Yes',\n      rejectBtnLabel: 'No',\n      action: 'delete-lesson',\n      confirmIcon: '/assets/icons/clock-dialog-icon.svg',\n      showHeader: true,\n      dialogType: 'custom',\n      headerClass: 'my-custom-header-class'\n    });\n    this.confirmDialogService.show();\n  }\n  /**\n   * Function to delete the lesson.\n   */\n  deleteLesson() {\n    this.subs.add(this.lessonService.delete(this.dialogData.lesson.id).pipe(take(1)).subscribe(res => {\n      this.toastService.setShowToastmessage({\n        severity: 'success',\n        summary: '',\n        detail: 'Lesson deleted.'\n      });\n      this.onDialogClose();\n      this.calendarService.setUpdateListener(true);\n      this.lessonService.setDeleteListener(true);\n      this.classroomService.setSelectedClassroomPackagesUpdate(true);\n    }));\n  }\n  /**\n   * Function to check if the completed lesson has a rating.\n   */\n  checkCompletedLessonRating() {\n    const selectedlessonId = this.dialogData.lesson.id;\n    const selectedlessonStatus = this.dialogData.lesson.status;\n    if (selectedlessonStatus.toLowerCase() === LessonStatus.COMPLETED.toLowerCase()) {\n      this.subs.sink = this.ratingAndReportService.getLessonBreakDown(selectedlessonId).subscribe(res => {\n        this.completedLessonHasRating = res.length > 0;\n        console.log(res);\n      });\n    }\n  }\n  initConfirmDialogListener() {\n    this.subs.add(this.confirmDialogService.acceptSelected$.subscribe(res => {\n      this.confirmDialogService.hide();\n      console.log(res);\n      switch (res.action) {\n        case 'cancel-lesson':\n        case 'cancel-and-charge-lesson-student':\n          const statusToSend = this.isTrialClassroom() ? this.lessonStatus.CANCELED_TRIAL.toLowerCase() : this.lessonStatus.CANCELED.toLowerCase();\n          this.updateLesson(statusToSend);\n          this.calendarService.setUpdateListener(true);\n          break;\n        case 'cancel-and-charge-lesson':\n          this.chargeLessonAndUpdateTime();\n          break;\n        case 'charge-lesson':\n          const chargeLessonParams = {\n            lessonId: this.dialogData.lesson.id,\n            answer: true\n          };\n          this.subs.add(this.lessonService.chargeLessonTime(chargeLessonParams.lessonId, chargeLessonParams.answer).subscribe(res => {\n            if (res) {\n              this.toastService.setShowToastmessage({\n                severity: 'success',\n                summary: '',\n                detail: 'Lesson time charged.'\n              });\n              this.calendarService.setToggleOverlayListener(true);\n              this.calendarService.setUpdateListener(true);\n              this.classroomService.setSelectedClassroomPackagesUpdate(true);\n              this.dialogData.lesson.chargeTime = true;\n              this.onDialogClose();\n            }\n          }));\n          break;\n        case 'delete-lesson':\n          this.deleteLesson();\n          break;\n      }\n      this.classroomService.setSelectedClassroomPackagesUpdate(true);\n    }));\n  }\n  /**\n   * Initializes the listener for the reject dialog.\n   * Handles the action when the reject button is clicked in the dialog.\n   */\n  initRejectDialogListener() {\n    this.subs.add(this.confirmDialogService.rejectSelected$.subscribe(res => {\n      this.confirmDialogService.hide();\n      console.log(res);\n      switch (res.action) {\n        case 'charge-lesson':\n          this.handleChargeLesson(false);\n          break;\n        case 'cancel-and-charge-lesson':\n          this.cancelAndChargeLesson(false);\n          break;\n      }\n    }));\n  }\n  handleChargeLesson(answer) {\n    const chargeLessonParams = {\n      lessonId: this.dialogData.lesson.id,\n      answer: answer\n    };\n    this.subs.add(this.lessonService.chargeLessonTime(chargeLessonParams.lessonId, chargeLessonParams.answer).subscribe(res => {\n      if (res) {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: answer ? 'Lesson time charged.' : 'Lesson time not charged.'\n        });\n        this.calendarService.setToggleOverlayListener(true);\n        this.calendarService.setUpdateListener(true);\n        this.onDialogClose();\n      }\n    }));\n  }\n  /**\n   * Cancels and charges the lesson.\n   *\n   * @param answer - The answer to charge the lesson or not.\n   */\n  cancelAndChargeLesson(answer) {\n    const chargeLessonParams = {\n      lessonId: this.dialogData.lesson.id,\n      answer: answer\n    };\n    const statusToSend = this.isTrialClassroom() ? this.lessonStatus.CANCELED_TRIAL.toLowerCase() : this.lessonStatus.CANCELED.toLowerCase();\n    let lesson = {\n      id: this.dialogData.lesson.id,\n      classroomId: this.dialogData.lesson.classroomId,\n      status: statusToSend,\n      startingDate: this.dialogData.lesson.startingDate,\n      duration: this.dialogData.lesson.duration,\n      isRecccuring: this.dialogData.lesson.isRecccuring,\n      answered: \"accepted\"\n    };\n    this.subs.add(this.lessonService.update(lesson).pipe(switchMap(res => {\n      if (res) {\n        console.log(res);\n        const userIds = this.dialogData.classroom.classroomStudents.map(el => {\n          return el.aspUserId;\n        });\n        console.log(userIds);\n        this.completedLessonHasRating = res.length > 0;\n        return of(true);\n      }\n      return of(res);\n    })).subscribe(res => {\n      if (res) {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Lesson time not charged.'\n        });\n        this.calendarService.setToggleOverlayListener(true);\n        this.calendarService.setUpdateListener(true);\n        this.classroomService.setSelectedClassroomPackagesUpdate(true);\n        this.dialogData.lesson.chargeTime = true;\n        this.onDialogClose();\n      }\n    }));\n  }\n  /**\n   * Charges the lesson and updates the time.\n   */\n  chargeLessonAndUpdateTime() {\n    const chargeLessonParams = {\n      lessonId: this.dialogData.lesson.id,\n      answer: true\n    };\n    const statusToSend = this.isTrialClassroom() ? this.lessonStatus.CANCELED_TRIAL.toLowerCase() : this.lessonStatus.CANCELED.toLowerCase();\n    let lesson = {\n      id: this.dialogData.lesson.id,\n      classroomId: this.dialogData.lesson.classroomId,\n      status: statusToSend,\n      startingDate: this.dialogData.lesson.startingDate,\n      duration: this.dialogData.lesson.duration,\n      isRecccuring: this.dialogData.lesson.isRecccuring,\n      answered: \"accepted\"\n    };\n    this.subs.add(this.lessonService.update(lesson).pipe(switchMap(res => {\n      if (res) {\n        console.log(res);\n        const userIds = this.dialogData.classroom.classroomStudents.map(el => {\n          return el.aspUserId;\n        });\n        console.log(userIds);\n        this.completedLessonHasRating = res.length > 0;\n        return this.lessonService.chargeLessonTime(chargeLessonParams.lessonId, chargeLessonParams.answer);\n      }\n      return of(res);\n    })).subscribe(res => {\n      if (res) {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Lesson time charged.'\n        });\n        this.calendarService.setToggleOverlayListener(true);\n        this.calendarService.setUpdateListener(true);\n        this.classroomService.setSelectedClassroomPackagesUpdate(true);\n        this.dialogData.lesson.chargeTime = true;\n        this.onDialogClose();\n      }\n    }));\n  }\n  /**\n   * Checks if the classroom is a trial classroom.\n   * @returns {boolean} True if the classroom is a trial classroom, false otherwise.\n   */\n  isTrialClassroom() {\n    return this.classroomService.isTrialClassroom(this.dialogData.classroom);\n  }\n  changeCompletedToArrangedAndDeleteRating() {\n    let userRatingIds = [];\n    const selectedlessonId = this.dialogData.lesson.id;\n    this.ratingAndReportService.getLessonBreakDown(selectedlessonId).pipe(switchMap(res => {\n      if (!res) {\n        return of(null);\n      }\n      console.log(res);\n      // if (res.length === 0) {\n      //   return;\n      // }\n      const observables = this.dialogData.classroom.classroomStudents.map(user => this.ratingAndReportService.getUserLessonRatings(user.aspUserId, selectedlessonId));\n      return forkJoin(observables).pipe(map(ratings => ({\n        ratings,\n        id: res[0].id\n      })));\n    }), switchMap(res => {\n      console.log(res.ratings);\n      res.ratings.forEach(rating => {\n        console.log(rating);\n      });\n      const userRatingIds = [];\n      for (let rating of res.ratings) {\n        userRatingIds.push(rating[0].id);\n      }\n      console.log(res.ratings);\n      console.log(userRatingIds);\n      const observables = res.ratings[0].map(user => this.ratingAndReportService.deleteEndOfLessonRatings(userRatingIds, res.id));\n      return forkJoin(observables);\n    })).subscribe(() => {\n      this.changeLessonStatus(this.lessonStatus.REARRANGE);\n      // Handle the result of deleteEndOfLessonRatings\n    });\n    // this.subs.sink = this.ratingAndReportService.getLessonBreakDown(selectedlessonId).pipe(\n    //   switchMap((res: any) => {\n    //     if (res) {\n    //       console.log(res);\n    //       const userIds = this.dialogData.classroom.classroomStudents.map((el: User) => { return el.aspUserId });\n    //       console.log(userIds);\n    //       this.completedLessonHasRating = res.length > 0;\n    //       return this.ratingAndReportService.deleteEndOfLessonRatings(userIds,res[0].id );\n    //       return of(res);\n    //     }\n    //     return of(res);\n    //   })\n    // ).subscribe((res: any) => {\n    //   console.log(res);\n    // });\n  }\n  /**\n   * Prepares and shows the cancel lesson dialog.\n   */\n  prepareAndShowCancelLessonDialog() {\n    this.confirmDialogService.setProperties({\n      confirmMessage: 'Are you sure you want to cancel this lesson?',\n      acceptBtnLabel: 'Yes',\n      rejectBtnLabel: 'No',\n      showHeader: true,\n      dialogType: 'custom',\n      action: 'cancel-lesson',\n      acceptBtnClass: ' ',\n      rejectBtnClass: 'cd-dialog-btn-reject',\n      headerClass: 'my-custom-header-class'\n    });\n    this.confirmDialogService.show();\n  }\n  /**\n   * Prepares and shows the charge and cancel lesson dialog.\n   *\n   * @param foundPackage The found package for the lesson.\n   */\n  prepareAndShowChargeAndCancelLessonDialog(foundPackage) {\n    this.confirmDialogService.setProperties({\n      confirmMessage: foundPackage.type + ' package ' + this.packageDurationHours[foundPackage.type] + ' hours cancellation policy applies',\n      acceptBtnLabel: 'Accept & Charge',\n      rejectBtnLabel: 'Don\\'t Charge',\n      showHeader: true,\n      dialogType: 'custom',\n      action: 'cancel-and-charge-lesson',\n      headerClass: 'my-custom-header-class',\n      acceptBtnClass: 'red-accept-charge',\n      rejectBtnClass: 'cd-dialog-btn-accept'\n    });\n    this.confirmDialogService.show();\n  }\n  /**\n   * Prepares and shows the charge and cancel lesson dialog for the student.\n   *\n   * @param foundPackage The found package for the lesson.\n   */\n  prepareAndShowChargeAndCancelLessonDialogForStudent(foundPackage) {\n    this.confirmDialogService.setProperties({\n      confirmMessage: foundPackage.type + ' package ' + this.packageDurationHours[foundPackage.type] + ` hours cancellation policy applies. <br> \n      You will be charged for this lesson. <br><br> Do you want to continue?`,\n      acceptBtnLabel: 'Yes',\n      rejectBtnLabel: 'No',\n      showHeader: true,\n      dialogType: 'custom',\n      action: 'cancel-and-charge-lesson-student',\n      headerClass: 'my-custom-header-class',\n      acceptBtnClass: 'red-accept-charge',\n      rejectBtnClass: 'cd-dialog-btn-accept'\n    });\n    this.confirmDialogService.show();\n  }\n  /**\n   * Gets the package duration charge.\n   *\n   * @param state The state of the package.\n   * @param lessonStartDate The start date of the lesson.\n   * @returns Whether to bypass the cancellation policy.\n   */\n  shouldBypassCancellationPolicyBasedOnPackage(state, lessonStartDate) {\n    let shouldBypassCancelationPolicy = true;\n    switch (state.toLowerCase()) {\n      case PackageType.FLEX.toLowerCase():\n        shouldBypassCancelationPolicy = this.generalService.isWithinTimeRange(lessonStartDate, this.packageDurationHours[PackageType.FLEX]);\n        break;\n      case PackageType.REGULAR.toLowerCase():\n        shouldBypassCancelationPolicy = this.generalService.isWithinTimeRange(lessonStartDate, this.packageDurationHours[PackageType.REGULAR]);\n        break;\n      case PackageType.PREMIUM.toLowerCase():\n        shouldBypassCancelationPolicy = this.generalService.isWithinTimeRange(lessonStartDate, this.packageDurationHours[PackageType.PREMIUM]);\n        break;\n    }\n    return shouldBypassCancelationPolicy;\n  }\n  hideOverlayPanel() {\n    if (this.overlayPanel && !this.generalService.isNullishObject(this.overlayPanel)) {\n      this.overlayPanel.hide();\n    }\n  }\n  setUpdateListeners() {\n    this.calendarService.setUpdateListener(true);\n    this.classroomService.setSelectedClassroomPackagesUpdate(true);\n  }\n  showToastMessage(severity, summary, detail) {\n    this.toastService.setShowToastmessage({\n      severity,\n      summary,\n      detail\n    });\n  }\n  updateClassroomStatusBasedOnLessonStatus(status) {\n    switch (status) {\n      case LessonStatus.COMPLETED_TRIAL:\n        this.updateClassroomStatus(Status.COMPLETED_TRIAL);\n        break;\n      case LessonStatus.CANCELED:\n        // Do nothing for now, can add code here if needed\n        break;\n    }\n  }\n  static #_ = this.ɵfac = function LessonInfoCalendarDialogComponent_Factory(t) {\n    return new (t || LessonInfoCalendarDialogComponent)(i0.ɵɵdirectiveInject(i1.DynamicDialogRef), i0.ɵɵdirectiveInject(i1.DynamicDialogConfig), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.RatingAndReportService), i0.ɵɵdirectiveInject(i5.LayoutService), i0.ɵɵdirectiveInject(i6.LessonService), i0.ɵɵdirectiveInject(i7.ClassroomService), i0.ɵɵdirectiveInject(i8.CalendarService), i0.ɵɵdirectiveInject(i9.GeneralService), i0.ɵɵdirectiveInject(i10.ConfirmDialogService), i0.ɵɵdirectiveInject(i11.ToastService), i0.ɵɵdirectiveInject(i12.PackageService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LessonInfoCalendarDialogComponent,\n    selectors: [[\"app-lesson-info-calendar-dialog\"]],\n    viewQuery: function LessonInfoCalendarDialogComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.overlayPanel = _t.first);\n      }\n    },\n    decls: 1,\n    vars: 1,\n    consts: [[\"chargeTimeSpan\", \"\"], [\"op\", \"\"], [4, \"ngIf\"], [\"blockClass\", \"border-radius-bottom-10\", \"headerBlockClass\", \"py-1 border-round-lg bg-cover \", \"containerClass\", \"bg-white px-3 py-2 relative\", 3, \"closeDialogEvent\", \"header\", \"headerBackgroundImage\", \"headerClass\", \"headerTextClass\", \"showCloseDialogIcon\"], [1, \"img-container\"], [1, \"relative\", 3, \"src\"], [1, \"lesson-info-list\", \"list-none\", \"px-0\", \"m-0\", \"mb-1\", \"info-list-middle\", \"mx-3\", \"py-1\"], [1, \"flex\", \"align-items-center\", \"px-2\", \"surface-border\", \"flex-wrap\", \"font-xs\"], [1, \"w-6\", \"md:w-6\", \"text-right\", \"lg:text-center\"], [1, \"pl-2\", \"md:w-6\", \"md:flex-order-0\", \"flex-order-1\", \"font-semiBold\", \"capitalize\"], [\"class\", \"flex align-items-center px-2 surface-border font-xs flex-wrap\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"px-2\", \"surface-border\", \"font-xs\", \"flex-wrap\"], [1, \"pl-2\", \"md:w-6\", \"md:flex-order-0\", \"flex-order-1\", \"font-semiBold\"], [1, \"grid\", \"relative\"], [1, \"col-8\", \"mx-auto\"], [1, \"flex\", \"justify-content-evenly\", \"mb-1\"], [\"pbutton\", \"\", \"type\", \"button\", 1, \"p-button-outlined\", \"p-element\", \"p-button-rounded\", \"p-button-xs\", \"p-button\", \"p-component\", \"gray-border-button\", \"p-button-rounded\", \"w-full\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"jus\", \"ju\"], [1, \"font-xs\", \"flex\", \"gap-2\", \"justify-content-center\", \"align-items-center\"], [\"class\", \"flex align-items-center px-2 surface-border flex-wrap font-xs\", 4, \"ngIf\"], [\"pbutton\", \"\", \"type\", \"button\", \"tooltipPosition\", \"top\", 1, \"p-element\", \"p-button-rounded\", \"p-button-xs\", \"p-button\", \"p-component\", \"gray-border-button\", \"join-lesson-btn\", \"p-button-rounded\", \"text-white\", \"w-full\", 3, \"click\", \"disabled\", \"pTooltip\", \"ngClass\"], [1, \"font-xs\", \"flex\", \"gap-2\", \"justify-content-center\", \"align-items-center\", \"text-white\"], [\"src\", \"/assets/images/dashboard/calendar/rating.svg\", \"height\", \"14\"], [\"src\", \"/assets/images/dashboard/calendar/edit.svg\", \"height\", \"14\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"pbutton\", \"\", \"tooltipPosition\", \"top\", \"type\", \"button\", 1, \"p-element\", \"p-button-rounded\", \"p-button-xs\", \"p-button\", \"p-component\", \"gray-border-button\", \"p-button-rounded\", \"w-full\", 3, \"click\", \"disabled\", \"pTooltip\", \"ngClass\"], [1, \"font-xs\", \"flex\", \"gap-2\", \"justify-content-center\", \"align-items-center\", 3, \"ngClass\"], [\"src\", \"/assets/images/dashboard/calendar/hourglass-white.svg\", \"height\", \"14\"], [\"pbutton\", \"\", \"type\", \"button\", 1, \"lesson-btn\", \"reschedule\", \"p-button-outlined\", \"p-element\", \"p-button-rounded\", \"p-button-xs\", \"p-button\", \"p-component\", \"gray-border-button\", \"p-button-rounded\", \"w-full\", 3, \"click\"], [1, \"font-xs\", \"text-primary\", \"flex\", \"gap-2\", \"justify-content-center\", \"align-items-center\"], [\"src\", \"/assets/images/dashboard/calendar/reschedule-calendar-icon.svg\", \"width\", \"12\"], [\"pbutton\", \"\", \"type\", \"button\", 1, \"p-element\", \"p-button-rounded\", \"p-button-xs\", \"p-button\", \"p-component\", \"gray-border-button\", \"p-button-rounded\", \"w-full\", 3, \"click\"], [1, \"font-xs\", \"text-white\", \"flex\", \"gap-2\", \"justify-content-center\", \"align-items-center\"], [\"src\", \"/assets/images/dashboard/calendar/close-white.svg\", \"height\", \"14\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"jus\", \"ju\", \"w-4rem\"], [\"src\", \"/assets/icons/check-white.svg\", \"height\", \"10\"], [1, \"pi\", \"pi-times\", \"icon-btn\", \"font-xs\"], [\"pbutton\", \"\", \"type\", \"button\", 1, \"p-element\", \"p-button-rounded\", \"p-button-xs\", \"p-button\", \"p-component\", \"lesson-btn\", \"delete-color\", \"justify-content-center\", \"p-button-rounded\", \"w-full\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"jus\", \"tex\", \"justify-content-center\"], [1, \"pi\", \"pi-times-circle\"], [\"pbutton\", \"\", \"type\", \"button\", 1, \"lesson-btn\", \"edit-color\", \"p-button-outlined\", \"p-element\", \"p-button-rounded\", \"p-button\", \"p-button-xs\", \"gray-border-button\", \"p-component\", \"w-full\", 3, \"click\"], [\"src\", \"/assets/images/dashboard/calendar/change-status-loading-icon.svg\", \"height\", \"14\"], [\"styleClass\", \"lesson-info-change-status\"], [\"pTemplate\", \"content\"], [1, \"p-0\", \"m-0\", \"list-none\", \"font-xs\"], [\"disableOnClick\", \"\", \"class\", \"flex align-items-center border-bottom-1 surface-border px-5 py-1 mt-1 cursor-pointer  lesson-text-item\", 3, \"disableTime\", \"click\", 4, \"ngIf\"], [\"disableOnClick\", \"\", \"class\", \"flex align-items-center border-bottom-1 surface-border px-5 py-1 mt-1 cursor-pointer lesson-text-item\", 3, \"disableTime\", \"click\", 4, \"ngIf\"], [\"disableOnClick\", \"\", 1, \"flex\", \"align-items-center\", \"border-bottom-1\", \"surface-border\", \"px-5\", \"py-1\", \"mt-1\", \"cursor-pointer\", \"lesson-text-item\", 3, \"click\", \"disableTime\"], [1, \"text-900\", \"line-height-3\", \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-1\"], [\"src\", \"/assets/images/dashboard/calendar/check.svg\", \"width\", \"14\"], [1, \"text-700\"], [\"src\", \"/assets/images/dashboard/calendar/rearrange.svg\", \"width\", \"14\"], [\"src\", \"/assets/images/dashboard/calendar/hide.svg\", \"width\", \"14\"], [\"src\", \"/assets/images/dashboard/calendar/cancel.svg\", \"width\", \"14\"]],\n    template: function LessonInfoCalendarDialogComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, LessonInfoCalendarDialogComponent_ng_container_0_Template, 49, 36, \"ng-container\", 2);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.dialogData);\n      }\n    },\n    dependencies: [i13.NgClass, i13.NgIf, i13.NgTemplateOutlet, i14.PrimeTemplate, i15.OverlayPanel, i13.DatePipe],\n    styles: [\".lesson-info-list[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.lesson-info-list[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100px;\\n  height: 100px;\\n  object-fit: cover;\\n  z-index: 1;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n}\\n\\n.img-container[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 100px;\\n  height: 100px;\\n  object-fit: cover;\\n  z-index: 1;\\n  top: 60px;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n}\\n\\n.lesson-btn.join-lesson[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(90deg, hsl(231, 69%, 42%) 0%, hsl(232, 60%, 46%) 11%, hsl(233, 54%, 50%) 22%, hsl(233, 56%, 53%) 33%, hsl(233, 58%, 57%) 44%, hsl(233, 61%, 60%) 56%, hsl(233, 64%, 63%) 67%, hsl(232, 68%, 66%) 78%, hsl(232, 73%, 69%) 89%, hsl(231, 79%, 73%) 100%);\\n}\\n.lesson-btn.join-lesson-student[_ngcontent-%COMP%] {\\n  border: 1px solid transparent;\\n  background-color: #3d3d3d; \\n\\n  background-color: hsl(227, 73%, 60%);\\n  background-image: linear-gradient(90deg, hsl(233, 65%, 62%) 10%, hsl(240, 56%, 63%) 20%, hsl(247, 50%, 61%) 30%, hsl(253, 46%, 59%) 40%, hsl(259, 41%, 57%) 50%, hsl(276, 37%, 55%) 60%, hsl(294, 33%, 53%) 70%, hsl(310, 37%, 53%) 80%, hsl(322, 43%, 55%) 90%, hsl(331, 49%, 56%) 100%);\\n  color: #ffffff;\\n}\\n.lesson-btn.reschedule[_ngcontent-%COMP%] {\\n  background-color: #CCD6FF;\\n}\\n.lesson-btn.reschedule[_ngcontent-%COMP%] {\\n  background-color: #CCD6FF;\\n}\\n.lesson-btn.edit-color[_ngcontent-%COMP%] {\\n  border-color: #000B4A;\\n  color: #000B4A;\\n}\\n.lesson-btn.request-color[_ngcontent-%COMP%] {\\n  background-image: linear-gradient(90deg, hsl(189, 53%, 52%) 0%, hsl(190, 100%, 39%) 11%, hsl(194, 100%, 40%) 22%, hsl(198, 100%, 41%) 33%, hsl(202, 100%, 41%) 44%, hsl(206, 100%, 41%) 56%, hsl(209, 100%, 41%) 67%, hsl(213, 100%, 40%) 78%, hsl(229, 62%, 47%) 89%, hsl(255, 57%, 45%) 100%);\\n}\\n.lesson-btn.delete-color[_ngcontent-%COMP%] {\\n  border: 1px solid #BE0058;\\n  background-color: transparent;\\n  color: #BE0058;\\n}\\n.lesson-btn.delete-color[_ngcontent-%COMP%]:hover {\\n  color: #ffffff;\\n  background-color: #BE0058;\\n}\\n\\n.p-overlaypanel[_ngcontent-%COMP%]   .p-overlaypanel-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n\\n[_nghost-%COMP%]     .p-overlaypanel .p-overlaypanel-content {\\n  padding: 0 1rem;\\n}\\n\\n.lesson-text-item[_ngcontent-%COMP%]:hover {\\n  background-color: #e2ebfe;\\n  border-radius: 8px;\\n}\\n\\n.disabled-button-charge[_ngcontent-%COMP%] {\\n  background-color: #e25b79 !important;\\n  color: #ffffff !important;\\n  cursor: not-allowed;\\n}\\n\\n.disabled-button-not-charge[_ngcontent-%COMP%] {\\n  cursor: not-allowed;\\n}\\n.disabled-button-not-charge[_ngcontent-%COMP%]:disabled {\\n  background-color: var(--primary-color) !important;\\n  color: white !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["NavigationStart", "map", "skip", "switchMap", "take", "LessonStatus", "UserRole", "SubSink", "LessonRatingCalendarDialogComponent", "Status", "fork<PERSON><PERSON>n", "of", "PackageDurationHours", "PackageType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "classroomService", "getFullStudentNames", "dialogData", "classroom", "classroomStudents", "ɵɵelementContainerStart", "ɵɵtemplate", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_5_li_1_Template", "ɵɵproperty", "ɵɵtextInterpolate2", "teacher", "firstName", "lastName", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_6_li_1_Template", "ɵɵtextInterpolate", "activeLevel", "lesson", "ɵɵlistener", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_31_Template_button_click_2_listener", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "generalService", "goToBellbirdLink", "isDifferenceWithin30Minutes", "startingDate", "toString", "getCurrentTimeInTimezone", "user", "timeZone", "ɵɵpureFunction1", "_c3", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_37_Template_button_click_2_listener", "_r4", "openLessonRatingDialog", "ɵɵelement", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_38_Template_button_click_2_listener", "_r5", "chargeTimeSpan_r6", "_c4", "LessonInfoCalendarDialogComponent_ng_container_0_ng_template_41_Template_button_click_0_listener", "lesson_r8", "_r7", "chargeTime", "role", "UserRoles", "TEACHER", "chargeTimeSelected", "STUDENT", "ɵɵpureFunction2", "_c5", "_c6", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_43_Template_button_click_2_listener", "_r9", "onRescheduleSelected", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_44_Template_button_click_2_listener", "_r10", "onStudentCancelSelected", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_45_Template_button_click_2_listener", "_r11", "onLessonAcceptedClicked", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_46_Template_button_click_2_listener", "_r12", "onLessonRejectedClicked", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_47_Template_button_click_2_listener", "_r13", "onLessonDeleteSelected", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_ng_template_9_li_1_Template_li_click_0_listener", "_r16", "changeLessonStatus", "lessonStatus", "COMPLETED", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_ng_template_9_li_2_Template_li_click_0_listener", "_r17", "REARRANGE", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_ng_template_9_li_3_Template_li_click_0_listener", "_r18", "NO_SHOW", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_ng_template_9_li_4_Template_li_click_0_listener", "_r19", "CANCELED", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_ng_template_9_li_1_Template", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_ng_template_9_li_2_Template", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_ng_template_9_li_3_Template", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_ng_template_9_li_4_Template", "lessonService", "getStatusNameFromStatusString", "status", "includes", "toLowerCase", "ARRANGED", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_Template_button_click_2_listener", "$event", "_r14", "op_r15", "ɵɵreference", "toggle", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_ng_template_9_Template", "ɵɵstyleMap", "ɵɵpureFunction0", "_c7", "LessonInfoCalendarDialogComponent_ng_container_0_Template_app_block_viewer_closeDialogEvent_1_listener", "_r1", "onDialogClose", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_5_Template", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_6_Template", "LessonInfoCalendarDialogComponent_ng_container_0_li_12_Template", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_31_Template", "LessonInfoCalendarDialogComponent_ng_container_0_Template_button_click_33_listener", "onGoToClassroomSelected", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_37_Template", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_38_Template", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_39_Template", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_40_Template", "LessonInfoCalendarDialogComponent_ng_container_0_ng_template_41_Template", "ɵɵtemplateRefExtractor", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_43_Template", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_44_Template", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_45_Template", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_46_Template", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_47_Template", "LessonInfoCalendarDialogComponent_ng_container_0_ng_container_48_Template", "ɵɵpropertyInterpolate1", "getLessonStatusGradImage", "ɵɵsanitizeUrl", "language", "isTrialClassroom", "getLessonTime", "convertHoursToMinutesWithSuffix", "duration", "ɵɵpipeBind2", "_c1", "ARRANGED_TRIAL", "completedLessonHasRating", "ɵɵpureFunction3", "_c2", "REQUESTED", "LessonInfoCalendarDialogComponent", "constructor", "ref", "config", "authService", "router", "ratingAndReportService", "layoutService", "calendarService", "confirmDialogService", "toastService", "packageService", "NONE", "LessonStatuses", "classStatus", "COMPLETED_TRIAL", "subs", "sideMenuHeight", "packageDurationHours", "ngOnInit", "getLoggedInUser", "data", "isNullishObject", "console", "log", "initConfirmDialogListener", "initRejectDialogListener", "checkCompletedLessonRating", "sink", "events", "subscribe", "event", "close", "pipe", "res", "ngOnDestroy", "unsubscribe", "setUpdateListener", "navigationExtras", "queryParams", "JSON", "stringify", "navigate", "navigateToLessonDetails", "getUserRole", "ratingMode", "mainContentColumnElement", "document", "getElementById", "getBoundingClientRect", "width", "openDialogWithComponent", "dialogService", "result", "statusString", "getLessonStatusFromString", "cancelLessonSelected", "update<PERSON>esson", "lowercaseStatus", "CANCELED_TRIAL", "id", "classroomId", "isRecccuring", "<PERSON><PERSON><PERSON><PERSON>", "answered", "add", "update", "hideOverlayPanel", "setUpdateListeners", "showToastMessage", "updateClassroomStatusBasedOnLessonStatus", "updateClassroomStatus", "newStatus", "changeClassroomStatus", "response", "foundPackage", "findLessonPackageById", "packages", "packageId", "lessonStartDate", "shouldBypassCancellationPolicy", "shouldBypassCancellationPolicyBasedOnPackage", "type", "prepareAndShowCancelLessonDialog", "prepareAndShowChargeAndCancelLessonDialog", "studentLessonCanceledDialog", "setProperties", "confirmMessage", "acceptBtnLabel", "rejectBtnLabel", "action", "confirmIcon", "showHeader", "dialogType", "headerClass", "show", "ChargeDatePackageType", "replace", "shouldBypassCancelationPolicy", "prepareAndShowChargeAndCancelLessonDialogForStudent", "params", "lessonId", "acceptLessonRequest", "setShowToastmessage", "severity", "summary", "detail", "setToggleOverlayListener", "setSelectedClassroomPackagesUpdate", "rejectLessonRequest", "delete<PERSON><PERSON><PERSON>", "delete", "setDeleteListener", "<PERSON>lessonId", "selectedlessonStatus", "getLessonBreakDown", "length", "acceptSelected$", "hide", "statusToSend", "chargeLessonAndUpdateTime", "chargeLessonParams", "answer", "chargeLessonTime", "rejectSelected$", "handleChargeLesson", "cancelAndChargeLesson", "userIds", "el", "aspUserId", "changeCompletedToArrangedAndDeleteRating", "userRatingIds", "observables", "getUserLessonRatings", "ratings", "for<PERSON>ach", "rating", "push", "deleteEndOfLessonRatings", "acceptBtnClass", "rejectBtnClass", "state", "FLEX", "isWithinTimeRange", "REGULAR", "PREMIUM", "overlayPanel", "_", "ɵɵdirectiveInject", "i1", "DynamicDialogRef", "DynamicDialogConfig", "i2", "AuthService", "i3", "Router", "i4", "RatingAndReportService", "i5", "LayoutService", "i6", "LessonService", "i7", "ClassroomService", "i8", "CalendarService", "i9", "GeneralService", "i10", "ConfirmDialogService", "i11", "ToastService", "i12", "PackageService", "_2", "selectors", "viewQuery", "LessonInfoCalendarDialogComponent_Query", "rf", "ctx", "LessonInfoCalendarDialogComponent_ng_container_0_Template"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\calendar\\calendar-dialogs\\lesson-info-calendar-dialog\\lesson-info-calendar-dialog.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\calendar\\calendar-dialogs\\lesson-info-calendar-dialog\\lesson-info-calendar-dialog.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';\r\nimport { NavigationExtras, NavigationStart, Router } from '@angular/router';\r\nimport { EventImpl } from '@fullcalendar/core/internal';\r\nimport { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';\r\nimport { OverlayPanel } from 'primeng/overlaypanel';\r\nimport { map, skip, switchMap, take } from 'rxjs/operators';\r\nimport { LessonStatus, LessonUpdateRequest } from 'src/app/core/models/lesson.model';\r\nimport { User, UserRole } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { LessonService } from 'src/app/core/services/lesson.service';\r\nimport { SubSink } from 'subsink';\r\nimport { LessonRatingCalendarDialogComponent } from '../lesson-rating-calendar-dialog/lesson-rating-calendar-dialog.component';\r\nimport { ConfirmDialogService } from 'src/app/core/services/confirm-dialog.service';\r\nimport { CalendarService } from 'src/app/core/services/calendar.service';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { RatingAndReportService } from 'src/app/core/services/rating-and-report.service';\r\nimport { LayoutService } from 'src/app/core/services/layout.service';\r\nimport { Status } from 'src/app/core/models/classroom.model';\r\nimport { forkJoin, of } from 'rxjs';\r\nimport { LessonBreakdown, LessonUserRating } from 'src/app/core/models/rating.model';\r\nimport { Package, PackageDurationHours, PackageType } from 'src/app/core/models/package.model';\r\nimport { PackageService } from 'src/app/core/services/package.service';\r\n\r\n@Component({\r\n  selector: 'app-lesson-info-calendar-dialog',\r\n  templateUrl: './lesson-info-calendar-dialog.component.html',\r\n  styleUrls: ['./lesson-info-calendar-dialog.component.scss']\r\n})\r\nexport class LessonInfoCalendarDialogComponent implements OnInit, OnDestroy {\r\n\r\n  role: UserRole = UserRole.NONE;\r\n  UserRoles = UserRole;\r\n  LessonStatuses: LessonStatus = LessonStatus.ARRANGED;\r\n  lessonStatus = LessonStatus;\r\n  classStatus: Status = Status.COMPLETED_TRIAL;\r\n  dialogData: any = {};\r\n  completedLessonHasRating = false;\r\n  subs = new SubSink();\r\n  sideMenuHeight = '100%';\r\n  @ViewChild('op') overlayPanel!: OverlayPanel;\r\n  public readonly packageDurationHours = PackageDurationHours;\r\n  user: User = {} as User;\r\n  constructor(\r\n    private ref: DynamicDialogRef,\r\n    private config: DynamicDialogConfig,\r\n    private authService: AuthService,\r\n    private router: Router,\r\n    private ratingAndReportService: RatingAndReportService,\r\n    private layoutService: LayoutService,\r\n    public lessonService: LessonService,\r\n    public classroomService: ClassroomService,\r\n    public calendarService: CalendarService,\r\n    public generalService: GeneralService,\r\n    public confirmDialogService: ConfirmDialogService,\r\n    public toastService: ToastService,\r\n    public packageService: PackageService,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.user = this.authService.getLoggedInUser();\r\n    this.role = this.authService.getLoggedInUser().role;\r\n    this.dialogData = this.config.data.dialogData;\r\n    // define dialogData classroom in case is empty from variable\r\n    this.dialogData.classroom = !this.generalService.isNullishObject(this.dialogData.classroom) ? this.dialogData.classroom : this.dialogData.lesson.classroom;\r\n    console.log(this.dialogData);\r\n    this.initConfirmDialogListener();\r\n    this.initRejectDialogListener();\r\n    this.checkCompletedLessonRating();\r\n\r\n    this.subs.sink = this.router.events.subscribe(event => {\r\n      if (event instanceof NavigationStart) {\r\n        // Close the dialog reference when navigation starts\r\n        this.ref.close();\r\n      }\r\n    });\r\n    this.subs.sink = this.layoutService.sideMenuHeight.pipe(skip(1)).subscribe((res) => {\r\n      if (res !== 0) {\r\n        this.sideMenuHeight = res + 'px';\r\n      }\r\n    });\r\n    // this.onViewRatingSelected();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    this.ref.close();\r\n    this.subs.unsubscribe();\r\n    this.calendarService.setUpdateListener(false);\r\n  }\r\n\r\n  onDialogClose(data?: any) {\r\n    this.ref.close(data);\r\n  }\r\n\r\n  onRescheduleSelected() {\r\n    const navigationExtras: NavigationExtras = {\r\n      queryParams: { 'lesson': JSON.stringify(this.dialogData.lesson), 'reschedule': true }\r\n    };\r\n    this.router.navigate(['/dashboard/calendar/booking-system'], navigationExtras);\r\n    this.ref.close();\r\n  }\r\n\r\n  onGoToClassroomSelected() {\r\n    this.generalService.navigateToLessonDetails(this.dialogData.classroom, this.authService.getUserRole());\r\n    this.ref.close();\r\n  }\r\n\r\n  openLessonRatingDialog(ratingMode = 'create') {\r\n    console.log(this.dialogData);\r\n    const mainContentColumnElement = document.getElementById('mainContentColumn');\r\n    console.log(mainContentColumnElement!.getBoundingClientRect().width);\r\n\r\n    this.generalService.openDialogWithComponent(this.dialogData.dialogService, LessonRatingCalendarDialogComponent, \r\n      mainContentColumnElement!.getBoundingClientRect().width,\r\n      {\r\n        lesson: this.dialogData.lesson,\r\n        classroom: this.dialogData.classroom,\r\n        ratingMode: ratingMode,\r\n      }, 'mainContentColumn', (result: any) => {\r\n        console.log(result);\r\n        // if (result && result.action === 'shareWithMultiple') {\r\n        //   this.shareLibraryFiles(result.map.map);\r\n        // }\r\n      }, this.sideMenuHeight);\r\n    this.ref.close();\r\n  }\r\n\r\n  /**\r\n   * Changes the status of the lesson based on the provided status string.\r\n   *\r\n   * @param {string} statusString - The status string to change the lesson status to.\r\n   */\r\n  changeLessonStatus(statusString: string) {\r\n    // Get the lesson status from the status string\r\n    const status = this.getLessonStatusFromString(statusString);\r\n\r\n    if (!status) {\r\n      // Do nothing if status string not recognized\r\n      return;\r\n    }\r\n\r\n    // Check if the classroom is a trial classroom\r\n    const isTrialClassroom = this.classroomService.isTrialClassroom(\r\n      this.dialogData.classroom\r\n    );\r\n\r\n    if (status === LessonStatus.COMPLETED && !isTrialClassroom) {\r\n      // Open the lesson rating dialog if the status is completed and it's not a trial classroom\r\n      this.openLessonRatingDialog();\r\n      return;\r\n    }\r\n\r\n    if (status === LessonStatus.CANCELED) {\r\n      // Cancel the selected lesson if the status is canceled\r\n      this.cancelLessonSelected();\r\n      return;\r\n    }\r\n\r\n    // Update the lesson status\r\n    this.updateLesson(status);\r\n  }\r\n\r\n  /**\r\n   * The function `getLessonStatusFromString` takes a string representing a lesson status and returns the\r\n   * corresponding LessonStatus enum value or null if the string does not match any valid status.\r\n   * @param {string} statusString - A string representing the status of a lesson.\r\n   * @returns a value of type LessonStatus or null.\r\n   */\r\n  private getLessonStatusFromString(statusString: string): LessonStatus | null {\r\n\r\n    const lowercaseStatus = statusString.toLowerCase();\r\n\r\n    switch (lowercaseStatus) {\r\n      case 'completed':\r\n        return this.classroomService.isTrialClassroom(this.dialogData.classroom)\r\n          ? LessonStatus.COMPLETED_TRIAL\r\n          : LessonStatus.COMPLETED;\r\n\r\n      case 'rearrange':\r\n        return this.classroomService.isTrialClassroom(this.dialogData.classroom)\r\n          ? LessonStatus.ARRANGED_TRIAL\r\n          : LessonStatus.ARRANGED;\r\n\r\n      case 'no show':\r\n        return LessonStatus.NO_SHOW;\r\n\r\n      case 'canceled':\r\n        return this.classroomService.isTrialClassroom(this.dialogData.classroom)\r\n          ? LessonStatus.CANCELED_TRIAL\r\n          : LessonStatus.CANCELED;\r\n\r\n      default:\r\n        return null;\r\n    }\r\n\r\n  }\r\n\r\n\r\n  /**\r\n   * Updates the lesson with the given status.\r\n   *\r\n   * @param {LessonStatus} status - The new status for the lesson.\r\n   * @return {void} \r\n   */\r\n  updateLesson(status: LessonStatus) {\r\n    const { id, classroomId, startingDate, duration, isRecccuring } = this.dialogData.lesson;\r\n\r\n    // Create the updated lesson object\r\n    const updatedLesson: LessonUpdateRequest = {\r\n      id,\r\n      classroomId,\r\n      status,\r\n      startingDate,\r\n      duration,\r\n      isRecccuring,\r\n      answered: \"accepted\"\r\n    };\r\n\r\n    this.subs.add(\r\n      this.lessonService.update(updatedLesson).subscribe(() => {\r\n        this.hideOverlayPanel();\r\n        this.setUpdateListeners();\r\n        this.onDialogClose({ ...this.dialogData, status });\r\n        this.showToastMessage('success', '', 'Lesson updated.');\r\n      })\r\n    );\r\n\r\n    this.updateClassroomStatusBasedOnLessonStatus(status);\r\n  }\r\n\r\n  updateClassroomStatus(newStatus: Status) {\r\n    this.subs.add(this.classroomService.changeClassroomStatus(this.dialogData.classroom.id, newStatus).subscribe((response) => {\r\n      console.log(response);\r\n    }));\r\n  }\r\n\r\n  cancelLessonSelected() {\r\n    const { classroom, lesson } = this.dialogData;\r\n    const foundPackage = this.packageService.findLessonPackageById(classroom.packages, lesson.packageId) || null;\r\n    const lessonStartDate = lesson.startingDate;\r\n\r\n    if (foundPackage) {\r\n      const shouldBypassCancellationPolicy = this.shouldBypassCancellationPolicyBasedOnPackage(foundPackage.type.toLowerCase(), lessonStartDate);\r\n\r\n      if (shouldBypassCancellationPolicy) {\r\n        this.prepareAndShowCancelLessonDialog();\r\n      } else if (!shouldBypassCancellationPolicy && !this.isTrialClassroom()) {\r\n        this.prepareAndShowChargeAndCancelLessonDialog(foundPackage);\r\n      }\r\n\r\n      console.log(foundPackage);\r\n    } else {\r\n      this.prepareAndShowCancelLessonDialog();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Function to display a confirmation dialog for canceling a lesson.\r\n   */\r\n  private studentLessonCanceledDialog() {\r\n    // Set properties for the confirmation dialog\r\n    this.confirmDialogService.setProperties({\r\n      confirmMessage: 'Are you sure you want to cancel this lesson?',\r\n      acceptBtnLabel: 'Yes',\r\n      rejectBtnLabel: 'No',\r\n      action: 'cancel-lesson',\r\n      confirmIcon: '/assets/icons/clock-dialog-icon.svg',\r\n      showHeader: true,\r\n      dialogType: 'custom',\r\n      headerClass: 'my-custom-header-class'\r\n    });\r\n    // Show the confirmation dialog\r\n    this.confirmDialogService.show();\r\n  }\r\n\r\n  /**\r\n   * Function to display a confirmation dialog for charging time.\r\n   */\r\n  chargeTimeSelected() {\r\n    // Set properties for the confirmation dialog\r\n    this.confirmDialogService.setProperties({\r\n      confirmMessage: 'Are you sure you want to charge time?',\r\n      confirmIcon: '/assets/icons/clock-dialog-icon.svg',\r\n      acceptBtnLabel: 'Yes',\r\n      rejectBtnLabel: 'No',\r\n      showHeader: true,\r\n      dialogType: 'custom',\r\n      action: 'charge-lesson',\r\n      headerClass: 'my-custom-header-class'\r\n    });\r\n    // Show the confirmation dialog\r\n    this.confirmDialogService.show();\r\n  }\r\n\r\n  /**\r\n   * Function to display a confirmation dialog for charging time based on date and package type.\r\n   */\r\n  ChargeDatePackageType() {\r\n    // Set properties for the confirmation dialog\r\n    this.confirmDialogService.setProperties({\r\n      confirmMessage: 'Are you sure want to charge time?',\r\n      confirmIcon: '/assets/icons/clock-dialog-icon.svg',\r\n      acceptBtnLabel: 'Yes',\r\n      rejectBtnLabel: 'No',\r\n      showHeader: true,\r\n      dialogType: 'custom',\r\n      action: 'charge-lesson',\r\n      headerClass: 'my-custom-header-class'\r\n    });\r\n    // Show the confirmation dialog\r\n    this.confirmDialogService.show();\r\n  }\r\n\r\n  /**\r\n   * Function to get the lesson status gradient image.\r\n   * \r\n   * @returns The lesson status gradient image.\r\n   */\r\n  getLessonStatusGradImage() {\r\n    return this.lessonService.getStatusNameFromStatusString(this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status)).replace(/\\s+/g, '-');\r\n  }\r\n\r\n  /**\r\n   * Function to handle when student cancel is selected.\r\n   */\r\n  onStudentCancelSelected() {\r\n    const foundPackage = this.packageService.findLessonPackageById(this.dialogData.classroom.packages, this.dialogData.lesson.packageId) || null;\r\n    const lessonStartDate = this.dialogData.lesson.startingDate;\r\n    if (foundPackage) {\r\n      // Checking lesson package cancellation policy\r\n      const shouldBypassCancelationPolicy = this.shouldBypassCancellationPolicyBasedOnPackage(foundPackage!.type.toLowerCase(), lessonStartDate);\r\n      if (shouldBypassCancelationPolicy) {\r\n        this.studentLessonCanceledDialog();\r\n      } else if (!shouldBypassCancelationPolicy && !this.isTrialClassroom()) {\r\n        this.prepareAndShowChargeAndCancelLessonDialogForStudent(foundPackage);\r\n      }\r\n      console.log(foundPackage);\r\n    } else {\r\n      this.studentLessonCanceledDialog();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Function to handle when lesson accepted is clicked.\r\n   */\r\n  onLessonAcceptedClicked() {\r\n    const params = {\r\n      lessonId: this.dialogData.lesson.id\r\n    }\r\n    this.subs.add(this.classroomService.acceptLessonRequest(this.dialogData.lesson.id).subscribe((res) => {\r\n      if (res) {\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'success',\r\n          summary: '',\r\n          detail: 'Lesson request accepted.'\r\n        });\r\n        this.calendarService.setToggleOverlayListener(true);\r\n        this.calendarService.setUpdateListener(true);\r\n        this.classroomService.setSelectedClassroomPackagesUpdate(true);\r\n        this.onDialogClose();\r\n      }\r\n    }));\r\n  }\r\n\r\n  /**\r\n   * Function to handle when lesson rejected is clicked.\r\n   */\r\n  onLessonRejectedClicked() {\r\n    const params = {\r\n      lessonId: this.dialogData.lesson.id\r\n    }\r\n    this.subs.add(this.classroomService.rejectLessonRequest(this.dialogData.lesson.id).subscribe((res) => {\r\n      if (res) {\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'success',\r\n          summary: '',\r\n          detail: 'Lesson request removed.'\r\n        });\r\n        this.calendarService.setToggleOverlayListener(true);\r\n        this.calendarService.setUpdateListener(true);\r\n        this.classroomService.setSelectedClassroomPackagesUpdate(true);\r\n        this.onDialogClose();\r\n      }\r\n    }));\r\n  }\r\n\r\n  /**\r\n   * Function to handle when lesson delete is selected.\r\n   */\r\n  onLessonDeleteSelected() {\r\n    this.confirmDialogService.setProperties({\r\n      confirmMessage: `\r\n      Are you sure you want to delete this lesson?`,\r\n      acceptBtnLabel: 'Yes',\r\n      rejectBtnLabel: 'No',\r\n      action: 'delete-lesson',\r\n      confirmIcon: '/assets/icons/clock-dialog-icon.svg',\r\n      showHeader: true,\r\n      dialogType: 'custom',\r\n      headerClass: 'my-custom-header-class'\r\n    });\r\n    this.confirmDialogService.show();\r\n  }\r\n\r\n  /**\r\n   * Function to delete the lesson.\r\n   */\r\n  deleteLesson() {\r\n    this.subs.add(this.lessonService.delete(this.dialogData.lesson.id).pipe(take(1)).subscribe(res => {\r\n      this.toastService.setShowToastmessage({\r\n        severity: 'success',\r\n        summary: '',\r\n        detail: 'Lesson deleted.'\r\n      });\r\n      this.onDialogClose();\r\n      this.calendarService.setUpdateListener(true);\r\n      this.lessonService.setDeleteListener(true);\r\n      this.classroomService.setSelectedClassroomPackagesUpdate(true);\r\n    }));\r\n  }\r\n\r\n  /**\r\n   * Function to check if the completed lesson has a rating.\r\n   */\r\n  checkCompletedLessonRating() {\r\n    const selectedlessonId = this.dialogData.lesson.id;\r\n    const selectedlessonStatus = this.dialogData.lesson.status;\r\n    if (selectedlessonStatus.toLowerCase() === LessonStatus.COMPLETED.toLowerCase()) {\r\n      this.subs.sink = this.ratingAndReportService.getLessonBreakDown(selectedlessonId).subscribe((res: any) => {\r\n        \r\n        this.completedLessonHasRating = res.length > 0;\r\n        console.log(res);\r\n\r\n      });\r\n    }\r\n  }\r\n\r\n  private initConfirmDialogListener() {\r\n    this.subs.add(this.confirmDialogService.acceptSelected$.subscribe(res => {\r\n      this.confirmDialogService.hide();\r\n      console.log(res);\r\n  \r\n      switch (res.action) {\r\n        case 'cancel-lesson':\r\n        case 'cancel-and-charge-lesson-student':\r\n          const statusToSend = this.isTrialClassroom() ? this.lessonStatus.CANCELED_TRIAL.toLowerCase() as LessonStatus : this.lessonStatus.CANCELED.toLowerCase() as LessonStatus;\r\n          this.updateLesson(statusToSend);\r\n          this.calendarService.setUpdateListener(true);\r\n          break;\r\n  \r\n        case 'cancel-and-charge-lesson':\r\n          this.chargeLessonAndUpdateTime();\r\n          break;\r\n  \r\n        case 'charge-lesson':\r\n          const chargeLessonParams = {\r\n            lessonId: this.dialogData.lesson.id,\r\n            answer: true,\r\n          };\r\n          this.subs.add(this.lessonService.chargeLessonTime(chargeLessonParams.lessonId, chargeLessonParams.answer).subscribe((res) => {\r\n            if (res) {\r\n              this.toastService.setShowToastmessage({\r\n                severity: 'success',\r\n                summary: '',\r\n                detail: 'Lesson time charged.'\r\n              });\r\n              this.calendarService.setToggleOverlayListener(true);\r\n              this.calendarService.setUpdateListener(true);\r\n              this.classroomService.setSelectedClassroomPackagesUpdate(true);\r\n              this.dialogData.lesson.chargeTime = true;\r\n              this.onDialogClose();\r\n            }\r\n          }));\r\n          break;\r\n  \r\n        case 'delete-lesson':\r\n          this.deleteLesson();\r\n          break;\r\n      }\r\n  \r\n      this.classroomService.setSelectedClassroomPackagesUpdate(true);\r\n    }));\r\n  }\r\n\r\n  /**\r\n   * Initializes the listener for the reject dialog.\r\n   * Handles the action when the reject button is clicked in the dialog.\r\n   */\r\n  private initRejectDialogListener() {\r\n    this.subs.add(this.confirmDialogService.rejectSelected$.subscribe(res => {\r\n      this.confirmDialogService.hide();\r\n      console.log(res);\r\n\r\n      switch (res.action) {\r\n        case 'charge-lesson':\r\n          this.handleChargeLesson(false);\r\n          break;\r\n        case 'cancel-and-charge-lesson':\r\n          this.cancelAndChargeLesson(false);\r\n          break;\r\n      }\r\n    }));\r\n  }\r\n\r\n  private handleChargeLesson(answer: boolean) {\r\n    const chargeLessonParams = {\r\n      lessonId: this.dialogData.lesson.id,\r\n      answer: answer,\r\n    };\r\n\r\n    this.subs.add(this.lessonService.chargeLessonTime(chargeLessonParams.lessonId, chargeLessonParams.answer).subscribe((res) => {\r\n      if (res) {\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'success',\r\n          summary: '',\r\n          detail: answer ? 'Lesson time charged.' : 'Lesson time not charged.'\r\n        });\r\n\r\n        this.calendarService.setToggleOverlayListener(true);\r\n        this.calendarService.setUpdateListener(true);\r\n        this.onDialogClose();\r\n      }\r\n    }));\r\n  }\r\n\r\n  /**\r\n   * Cancels and charges the lesson.\r\n   * \r\n   * @param answer - The answer to charge the lesson or not.\r\n   */\r\n  private cancelAndChargeLesson(answer: boolean) {\r\n    const chargeLessonParams = {\r\n      lessonId: this.dialogData.lesson.id,\r\n      answer: answer,\r\n    };\r\n    const statusToSend = this.isTrialClassroom() ? this.lessonStatus.CANCELED_TRIAL.toLowerCase() as LessonStatus : this.lessonStatus.CANCELED.toLowerCase() as LessonStatus;\r\n    let lesson: LessonUpdateRequest = {\r\n      id: this.dialogData.lesson.id,\r\n      classroomId: this.dialogData.lesson.classroomId,\r\n      status: statusToSend,\r\n      startingDate: this.dialogData.lesson.startingDate,\r\n      duration: this.dialogData.lesson.duration,\r\n      isRecccuring: this.dialogData.lesson.isRecccuring,\r\n      answered: \"accepted\"\r\n    };\r\n  \r\n    this.subs.add(this.lessonService.update(lesson).pipe(\r\n      switchMap((res: any) => {\r\n        if (res) {\r\n          console.log(res);\r\n          const userIds = this.dialogData.classroom.classroomStudents.map((el: User) => { return el.aspUserId });\r\n          console.log(userIds);\r\n          this.completedLessonHasRating = res.length > 0;\r\n          return of(true);\r\n        }\r\n        return of(res);\r\n      })\r\n    ).subscribe((res: any) => {\r\n      if (res) {\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'success',\r\n          summary: '',\r\n          detail: 'Lesson time not charged.'\r\n        });\r\n        this.calendarService.setToggleOverlayListener(true);\r\n        this.calendarService.setUpdateListener(true);\r\n        this.classroomService.setSelectedClassroomPackagesUpdate(true);\r\n        this.dialogData.lesson.chargeTime = true;\r\n        this.onDialogClose();\r\n      }\r\n    }));\r\n  }\r\n\r\n  /**\r\n   * Charges the lesson and updates the time.\r\n   */\r\n  private chargeLessonAndUpdateTime() {\r\n    const chargeLessonParams = {\r\n      lessonId: this.dialogData.lesson.id,\r\n      answer: true,\r\n    }\r\n    const statusToSend = this.isTrialClassroom() ? this.lessonStatus.CANCELED_TRIAL.toLowerCase() as LessonStatus : this.lessonStatus.CANCELED.toLowerCase() as LessonStatus;\r\n    let lesson: LessonUpdateRequest = {\r\n      id: this.dialogData.lesson.id,\r\n      classroomId: this.dialogData.lesson.classroomId,\r\n      status: statusToSend,\r\n      startingDate: this.dialogData.lesson.startingDate,\r\n      duration: this.dialogData.lesson.duration,\r\n      isRecccuring: this.dialogData.lesson.isRecccuring,\r\n      answered: \"accepted\"\r\n    };\r\n  \r\n    this.subs.add(this.lessonService.update(lesson).pipe(\r\n      switchMap((res: any) => {\r\n        if (res) {\r\n          console.log(res);\r\n          const userIds = this.dialogData.classroom.classroomStudents.map((el: User) => { return el.aspUserId });\r\n          console.log(userIds);\r\n          this.completedLessonHasRating = res.length > 0;\r\n          return this.lessonService.chargeLessonTime(chargeLessonParams.lessonId, chargeLessonParams.answer);\r\n        }\r\n        return of(res);\r\n      })\r\n    ).subscribe((res: any) => {\r\n      if (res) {\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'success',\r\n          summary: '',\r\n          detail: 'Lesson time charged.'\r\n        });\r\n        this.calendarService.setToggleOverlayListener(true);\r\n        this.calendarService.setUpdateListener(true);\r\n        this.classroomService.setSelectedClassroomPackagesUpdate(true);\r\n        this.dialogData.lesson.chargeTime = true;\r\n        this.onDialogClose();\r\n      }\r\n    }));\r\n  }\r\n\r\n  /**\r\n   * Checks if the classroom is a trial classroom.\r\n   * @returns {boolean} True if the classroom is a trial classroom, false otherwise.\r\n   */\r\n  private isTrialClassroom(): boolean {\r\n    return this.classroomService.isTrialClassroom(this.dialogData.classroom);\r\n  }\r\n\r\n  private changeCompletedToArrangedAndDeleteRating() {\r\n    let userRatingIds: number[] = [];\r\n    const selectedlessonId = this.dialogData.lesson.id;\r\n\r\n      this.ratingAndReportService.getLessonBreakDown(selectedlessonId).pipe(\r\n        switchMap((res: LessonBreakdown[]) => {\r\n          if (!res) {\r\n            return of(null);\r\n          }\r\n          console.log(res);\r\n          // if (res.length === 0) {\r\n          //   return;\r\n          // }\r\n          const observables = this.dialogData.classroom.classroomStudents.map((user: User) =>\r\n            this.ratingAndReportService.getUserLessonRatings(user.aspUserId!, selectedlessonId)\r\n          );\r\n      \r\n          return forkJoin(observables).pipe(\r\n            map(ratings => ({ ratings, id: res[0].id }))\r\n          );\r\n        }),\r\n        switchMap((res: any) => {\r\n          console.log(res.ratings);\r\n          res.ratings.forEach((rating: any) => {\r\n            console.log(rating);\r\n          });\r\n          const userRatingIds: any[] = [];\r\n      \r\n          for (let rating of res.ratings) {\r\n            userRatingIds.push(rating[0].id);\r\n          }\r\n      \r\n          console.log(res.ratings);\r\n          console.log(userRatingIds);\r\n      \r\n          const observables = res.ratings[0].map((user: User) =>\r\n            this.ratingAndReportService.deleteEndOfLessonRatings(userRatingIds, res.id)\r\n          );\r\n      \r\n          return forkJoin(observables);\r\n        })\r\n      ).subscribe(() => {\r\n        this.changeLessonStatus(this.lessonStatus.REARRANGE);\r\n        // Handle the result of deleteEndOfLessonRatings\r\n      });\r\n\r\n    // this.subs.sink = this.ratingAndReportService.getLessonBreakDown(selectedlessonId).pipe(\r\n    //   switchMap((res: any) => {\r\n    //     if (res) {\r\n\r\n    //       console.log(res);\r\n    //       const userIds = this.dialogData.classroom.classroomStudents.map((el: User) => { return el.aspUserId });\r\n    //       console.log(userIds);\r\n    //       this.completedLessonHasRating = res.length > 0;\r\n    //       return this.ratingAndReportService.deleteEndOfLessonRatings(userIds,res[0].id );\r\n    //       return of(res);\r\n    //     }\r\n    //     return of(res);\r\n    //   })\r\n    // ).subscribe((res: any) => {\r\n        \r\n    //   console.log(res);\r\n\r\n    // });\r\n  }\r\n\r\n  /**\r\n   * Prepares and shows the cancel lesson dialog.\r\n   */\r\n  private prepareAndShowCancelLessonDialog() {\r\n    this.confirmDialogService.setProperties({\r\n      confirmMessage: 'Are you sure you want to cancel this lesson?',\r\n      acceptBtnLabel: 'Yes',\r\n      rejectBtnLabel: 'No',\r\n      showHeader: true,\r\n      dialogType: 'custom',\r\n      action: 'cancel-lesson',\r\n      acceptBtnClass: ' ',\r\n      rejectBtnClass: 'cd-dialog-btn-reject',\r\n      headerClass: 'my-custom-header-class'\r\n    });\r\n    this.confirmDialogService.show();\r\n  }\r\n\r\n  /**\r\n   * Prepares and shows the charge and cancel lesson dialog.\r\n   * \r\n   * @param foundPackage The found package for the lesson.\r\n   */\r\n  private prepareAndShowChargeAndCancelLessonDialog(foundPackage: Package | null) {\r\n    this.confirmDialogService.setProperties({\r\n      confirmMessage: foundPackage!.type + ' package ' + this.packageDurationHours[foundPackage!.type] + ' hours cancellation policy applies',\r\n      acceptBtnLabel: 'Accept & Charge',\r\n      rejectBtnLabel: 'Don\\'t Charge',\r\n      showHeader: true,\r\n      dialogType: 'custom',\r\n      action: 'cancel-and-charge-lesson',\r\n      headerClass: 'my-custom-header-class',\r\n      acceptBtnClass: 'red-accept-charge',\r\n      rejectBtnClass: 'cd-dialog-btn-accept'\r\n    });\r\n    this.confirmDialogService.show();\r\n  }\r\n  \r\n  /**\r\n   * Prepares and shows the charge and cancel lesson dialog for the student.\r\n   * \r\n   * @param foundPackage The found package for the lesson.\r\n   */\r\n  private prepareAndShowChargeAndCancelLessonDialogForStudent(foundPackage: Package | null) {\r\n    this.confirmDialogService.setProperties({\r\n      confirmMessage: foundPackage!.type + ' package ' + this.packageDurationHours[foundPackage!.type] + ` hours cancellation policy applies. <br> \r\n      You will be charged for this lesson. <br><br> Do you want to continue?`,\r\n      acceptBtnLabel: 'Yes',\r\n      rejectBtnLabel: 'No',\r\n      showHeader: true,\r\n      dialogType: 'custom',\r\n      action: 'cancel-and-charge-lesson-student',\r\n      headerClass: 'my-custom-header-class',\r\n      acceptBtnClass: 'red-accept-charge',\r\n      rejectBtnClass: 'cd-dialog-btn-accept'\r\n    });\r\n    this.confirmDialogService.show();\r\n  }\r\n\r\n  /**\r\n   * Gets the package duration charge.\r\n   * \r\n   * @param state The state of the package.\r\n   * @param lessonStartDate The start date of the lesson.\r\n   * @returns Whether to bypass the cancellation policy.\r\n   */\r\n  private shouldBypassCancellationPolicyBasedOnPackage(state: string, lessonStartDate: string) {\r\n    let shouldBypassCancelationPolicy = true;\r\n    switch (state.toLowerCase()) {\r\n      case PackageType.FLEX.toLowerCase():\r\n        shouldBypassCancelationPolicy = this.generalService.isWithinTimeRange(lessonStartDate, this.packageDurationHours[PackageType.FLEX]);\r\n        break;\r\n      case PackageType.REGULAR.toLowerCase():\r\n        shouldBypassCancelationPolicy = this.generalService.isWithinTimeRange(lessonStartDate, this.packageDurationHours[PackageType.REGULAR]);\r\n        break;\r\n      case PackageType.PREMIUM.toLowerCase():\r\n        shouldBypassCancelationPolicy = this.generalService.isWithinTimeRange(lessonStartDate, this.packageDurationHours[PackageType.PREMIUM]);\r\n        break;\r\n    }\r\n\r\n    return shouldBypassCancelationPolicy;\r\n  }\r\n\r\n\r\n  private hideOverlayPanel() {\r\n    if (this.overlayPanel && !this.generalService.isNullishObject(this.overlayPanel)) {\r\n      this.overlayPanel.hide();\r\n    }\r\n  }\r\n\r\n  private setUpdateListeners() {\r\n    this.calendarService.setUpdateListener(true);\r\n    this.classroomService.setSelectedClassroomPackagesUpdate(true);\r\n  }\r\n\r\n  private showToastMessage(severity: string, summary: string, detail: string) {\r\n    this.toastService.setShowToastmessage({ severity, summary, detail });\r\n  }\r\n\r\n  private updateClassroomStatusBasedOnLessonStatus(status: LessonStatus) {\r\n    switch (status) {\r\n      case LessonStatus.COMPLETED_TRIAL:\r\n        this.updateClassroomStatus(Status.COMPLETED_TRIAL);\r\n        break;\r\n      case LessonStatus.CANCELED:\r\n        // Do nothing for now, can add code here if needed\r\n        break;\r\n    }\r\n  }\r\n\r\n}\r\n", "<ng-container *ngIf=\"this.dialogData\">\r\n  <app-block-viewer [header]=\"this.dialogData.lesson.status\"\r\n    headerBackgroundImage=\"/assets/images/dashboard/calendar/calendar-dialog-lesson-info-{{getLessonStatusGradImage()}}.png\"\r\n    blockClass=\"border-radius-bottom-10\" headerBlockClass=\"py-1 border-round-lg bg-cover \"\r\n    containerClass=\"bg-white px-3 py-2 relative\" [headerClass]=\"'justify-content-center my-0'\"\r\n    [headerTextClass]=\"'font-base font-semibold justify-content-center capitalize'\" [showCloseDialogIcon]=\"true\"\r\n    (closeDialogEvent)=\"onDialogClose()\">\r\n\r\n    <div class=\"img-container\">\r\n\r\n      <img class=\" relative\" \r\n      src=\"/assets/images/dashboard/calendar/{{getLessonStatusGradImage()}}-gradient.png\" />\r\n    </div>\r\n    <ul class=\"lesson-info-list list-none px-0 m-0 mb-1 info-list-middle mx-3 py-1\">\r\n      <ng-container *ngIf=\"role === UserRoles.TEACHER\">\r\n        <li class=\"flex align-items-center px-2 surface-border flex-wrap font-xs\" *ngIf=\"dialogData.classroom && dialogData.classroom.classroomStudents\">\r\n          <div class=\"w-6 md:w-6 text-right lg:text-center\">Participants</div>\r\n          <div class=\"pl-2 md:w-6 md:flex-order-0 flex-order-1 font-semiBold\">\r\n            {{classroomService.getFullStudentNames(dialogData.classroom.classroomStudents)}}</div>\r\n        </li>\r\n      </ng-container>\r\n      <ng-container *ngIf=\"role === UserRoles.STUDENT\">\r\n\r\n      <li class=\"flex align-items-center px-2 surface-border flex-wrap font-xs\" *ngIf=\"dialogData.classroom.teacher\">\r\n        <div class=\"w-6 md:w-6 text-right lg:text-center\">Teacher</div>\r\n        <div class=\"pl-2 md:w-6 md:flex-order-0 flex-order-1 font-semiBold\">\r\n          {{dialogData.classroom.teacher.firstName}} {{dialogData.classroom.teacher.lastName}}</div>\r\n      </li>\r\n      </ng-container>\r\n      <li class=\"flex align-items-center px-2 surface-border flex-wrap font-xs\">\r\n        <div class=\"w-6 md:w-6 text-right lg:text-center\">Language</div>\r\n        <div class=\"pl-2 md:w-6 md:flex-order-0 flex-order-1 font-semiBold capitalize\">{{dialogData.classroom.language ? dialogData.classroom.language : dialogData.lesson.classroom.language}}</div>\r\n      </li>\r\n      <li *ngIf=\"!classroomService.isTrialClassroom(this.dialogData.classroom)\" class=\"flex align-items-center px-2 surface-border font-xs flex-wrap\">\r\n        <div class=\"w-6 md:w-6 text-right lg:text-center\">Level</div>\r\n        <div class=\"pl-2 md:w-6 md:flex-order-0 flex-order-1 font-semiBold capitalize\">{{dialogData.classroom.activeLevel ? dialogData.classroom.activeLevel : dialogData.lesson.classroom.activeLevel}}</div>\r\n      </li>\r\n      <li class=\"flex align-items-center px-2 surface-border font-xs flex-wrap\">\r\n        <div class=\"w-6 md:w-6 text-right lg:text-center\">Starts at</div>\r\n        <div class=\"pl-2 md:w-6 md:flex-order-0 flex-order-1 font-semiBold\">{{ generalService.getLessonTime(dialogData.lesson.startingDate) }}</div>\r\n      </li>\r\n      <li class=\"flex align-items-center px-2 surface-border font-xs flex-wrap\">\r\n        <div class=\"w-6 md:w-6 text-right lg:text-center\">Duration</div>\r\n        <div class=\"pl-2 md:w-6 md:flex-order-0 flex-order-1 font-semiBold\">\r\n          {{generalService.convertHoursToMinutesWithSuffix(dialogData.lesson.duration)}}</div>\r\n      </li>\r\n      <li class=\"flex align-items-center px-2 surface-border font-xs flex-wrap\">\r\n        <div class=\"w-6 md:w-6 text-right lg:text-center\">Date</div>\r\n        <div class=\"pl-2 md:w-6 md:flex-order-0 flex-order-1 font-semiBold\">\r\n          {{ dialogData.lesson.startingDate | date:'dd/MM/yyyy' }}</div>\r\n      </li>\r\n    </ul>\r\n\r\n    <div class=\"grid relative\">\r\n      <div class=\"col-8 mx-auto\">\r\n        <!-- <ng-container *ngIf=\"this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status) === lessonStatus.ARRANGED\">\r\n          <div class=\"flex justify-content-evenly mb-1\">\r\n            <button pbutton=\"\" type=\"button\"\r\n              class=\"lesson-btn join-lesson-student p-element p-button-rounded p-button p-button-xs gray-border-button p-component w-full\">\r\n              <div class=\"flex align-items-center gap-1 jus ju\">\r\n                <span class=\"font-xs\">Join Lesson</span>\r\n              </div>\r\n            </button>\r\n          </div>\r\n        </ng-container> -->\r\n        <ng-container *ngIf=\"[lessonStatus.ARRANGED_TRIAL, lessonStatus.ARRANGED].includes(this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status))\">\r\n       \r\n          <div class=\"flex justify-content-evenly mb-1\">\r\n          <button pbutton=\"\" type=\"button\"\r\n          [disabled]=\"!generalService.isDifferenceWithin30Minutes(this.dialogData.lesson.startingDate.toString(), \r\n                            this.generalService.getCurrentTimeInTimezone(this.user.timeZone!), this.dialogData.lesson)\"\r\n          [pTooltip]=\"!generalService.isDifferenceWithin30Minutes(this.dialogData.lesson.startingDate.toString(), \r\n                            this.generalService.getCurrentTimeInTimezone(this.user.timeZone!), this.dialogData.lesson) ? 'You can join your lesson 30 minutes before the scheduled time' : ''\"\r\n          tooltipPosition=\"top\"\r\n          (click)=\"generalService.goToBellbirdLink(this.dialogData.lesson)\"\r\n            class=\" p-element p-button-rounded p-button-xs p-button p-component gray-border-button join-lesson-btn p-button-rounded text-white w-full\"\r\n            [ngClass]=\"{\r\n            'disabled': !generalService.isDifferenceWithin30Minutes(this.dialogData.lesson.startingDate.toString(), \r\n                            this.generalService.getCurrentTimeInTimezone(this.user.timeZone!), this.dialogData.lesson) }\"\r\n            >\r\n            <div class=\"flex align-items-center gap-1 jus ju\">\r\n              <span class=\"font-xs  flex gap-2 justify-content-center align-items-center text-white\">\r\n                JOIN LESSON</span>\r\n            </div>\r\n          </button>\r\n\r\n          <!-- <button (click)=\"generalService.goToJitsiMeetLink(todayLessons[0])\" pButton\r\n                                class=\" p-button-xs p-button p-button-rounded w-full gray-border-button max-w-max\"\r\n                                [ngClass]=\"{'text-white': generalService.isDifferenceWithin30Minutes(this.dialogData.lesson.startingDate.toString(), \r\n                            this.generalService.getCurrentTimeInTimezone(this.user.timeZone!)), 'p-button-outlined': !generalService.isDifferenceWithin30Minutes(this.dialogData.lesson.startingDate.toString(), \r\n                            this.generalService.getCurrentTimeInTimezone(this.user.timeZone!))}\">\r\n                                JOIN LESSON</button> -->\r\n          </div>\r\n        </ng-container>\r\n                                \r\n        <div class=\"flex justify-content-evenly mb-1\">\r\n          <button pbutton=\"\" type=\"button\"\r\n          (click)=\"onGoToClassroomSelected()\"\r\n            class=\"p-button-outlined p-element p-button-rounded p-button-xs p-button p-component gray-border-button p-button-rounded w-full\">\r\n            <div class=\"flex align-items-center gap-1 jus ju\">\r\n              <span class=\"font-xs  flex gap-2 justify-content-center align-items-center\">\r\n                Go to Classroom</span>\r\n            </div>\r\n          </button>\r\n        </div>\r\n        <!-- \r\n        This ng-container will only render its content if all of the following conditions are met:\r\n        1. The lesson status is \"COMPLETED\"\r\n        2. The classroom is not a trial classroom\r\n        3. The completed lesson has a rating\r\n        -->\r\n        <ng-container *ngIf=\"this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status) === lessonStatus.COMPLETED\r\n        && !this.classroomService.isTrialClassroom(this.dialogData.classroom) && completedLessonHasRating\">\r\n          <div class=\"flex justify-content-evenly mb-1\">\r\n            <button pbutton=\"\" type=\"button\"\r\n            (click)=\"openLessonRatingDialog('view')\"\r\n              class=\"p-button-outlined p-element p-button-rounded p-button-xs p-button p-component gray-border-button p-button-rounded w-full\">\r\n              <div class=\"flex align-items-center gap-1 jus ju\">\r\n                <span class=\"font-xs  flex gap-2 justify-content-center align-items-center\">\r\n                  <img src=\"/assets/images/dashboard/calendar/rating.svg\" height=\"14\" />\r\n                  View Rating</span>\r\n              </div>\r\n            </button>\r\n          </div>\r\n        </ng-container>\r\n        <!-- \r\n        This ng-container will only render its content if all of the following conditions are met:\r\n        1. The lesson status is \"COMPLETED\"\r\n        2. The classroom is not a trial classroom\r\n        3. The completed lesson has a rating\r\n        4. The user's role is \"TEACHER\"\r\n        -->\r\n        <ng-container *ngIf=\"this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status) === lessonStatus.COMPLETED\r\n        && !this.classroomService.isTrialClassroom(this.dialogData.classroom) && completedLessonHasRating && role === UserRoles.TEACHER\">\r\n          <div class=\"flex justify-content-evenly mb-1\">\r\n            <button pbutton=\"\" type=\"button\"\r\n            (click)=\"openLessonRatingDialog('edit')\"\r\n              class=\"p-button-outlined p-element p-button-rounded p-button-xs p-button p-component gray-border-button p-button-rounded w-full\">\r\n              <div class=\"flex align-items-center gap-1 jus ju\">\r\n                <span class=\"font-xs  flex gap-2 justify-content-center align-items-center\">\r\n                  <img src=\"/assets/images/dashboard/calendar/edit.svg\" height=\"14\" />\r\n                  Edit Rating</span>\r\n              </div>\r\n            </button>\r\n          </div>\r\n        </ng-container>\r\n        <!-- \r\n        This ng-container will only render its content if all of the following conditions are met:\r\n        1. The lesson status is either \"CANCELED\" or \"NO_SHOW\"\r\n        2. The classroom is not a trial classroom\r\n        3. The user's role is \"TEACHER\"\r\n        -->\r\n        <ng-container *ngIf=\"\r\n        (this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status) === lessonStatus.CANCELED.toLowerCase() || \r\n        this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status) === lessonStatus.NO_SHOW.toLowerCase()) &&\r\n        !classroomService.isTrialClassroom(this.dialogData.classroom) && role === UserRoles.TEACHER\">\r\n          <div class=\"flex justify-content-evenly mb-1\"\r\n          >\r\n                <div [ngTemplateOutlet]=\"chargeTimeSpan\"\r\n                [ngTemplateOutletContext]=\"{ lesson: this.dialogData.lesson }\"></div>\r\n          </div>\r\n        </ng-container>\r\n\r\n        <!-- \r\n        This ng-container will only render its content if all of the following conditions are met:\r\n        1. The lesson status is either \"CANCELED\" or \"NO_SHOW\"\r\n        2. The classroom is not a trial classroom\r\n        3. The user's role is \"STUDENT\"\r\n        4. The lesson has a charge time\r\n        -->\r\n        <ng-container *ngIf=\"\r\n        (this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status) === lessonStatus.CANCELED.toLowerCase() || \r\n        this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status) === lessonStatus.NO_SHOW.toLowerCase()) &&\r\n        !classroomService.isTrialClassroom(this.dialogData.classroom) && role === UserRoles.STUDENT\">\r\n        <div [ngTemplateOutlet]=\"chargeTimeSpan\"\r\n        [ngTemplateOutletContext]=\"{ lesson: this.dialogData.lesson }\"></div>\r\n        </ng-container>\r\n\r\n        <ng-template #chargeTimeSpan let-lesson=\"lesson\">\r\n          <button pbutton=\"\" [disabled]=\"lesson.chargeTime || role === UserRoles.STUDENT\"\r\n            [pTooltip]=\"lesson.chargeTime ? 'This lesson has already been charged.' : ''\"\r\n            tooltipPosition=\"top\"\r\n            (click)=\"!lesson.chargeTime && role === UserRoles.TEACHER ? chargeTimeSelected() : null\" type=\"button\"\r\n              class=\"p-element p-button-rounded p-button-xs p-button p-component gray-border-button p-button-rounded w-full\"\r\n              [ngClass]=\"{'disabled-button-charge' : lesson.chargeTime,\r\n              'disabled-button-not-charge' : !lesson.chargeTime && role === UserRoles.STUDENT}\">\r\n              <div class=\"flex align-items-center gap-1 jus ju\">\r\n                <span class=\"font-xs flex gap-2 justify-content-center align-items-center\"\r\n                      [ngClass]=\"{'text-white': !lesson.chargeTime && role === UserRoles.TEACHER }\">\r\n                  <img src=\"/assets/images/dashboard/calendar/hourglass-white.svg\" height=\"14\" />\r\n                  <span>{{!lesson.chargeTime && role === UserRoles.TEACHER ? 'Charge Time' : \r\n                  !lesson.chargeTime && role === UserRoles.STUDENT ? 'Not Charged' :\r\n                  'Already Charged' }}\r\n                  </span>\r\n                </span>\r\n              </div>\r\n            </button>\r\n         </ng-template>\r\n        <!-- <ng-container *ngIf=\"(this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status) === lessonStatus.COMPLETED) && role === UserRoles.TEACHER\">\r\n          <div class=\"flex justify-content-evenly mb-1\">\r\n            <button pbutton=\"\" type=\"button\"\r\n              class=\"lesson-btn edit-color p-button-outlined p-element p-button-rounded p-button p-button-xs gray-border-button p-component w-full\">\r\n              <div class=\"flex align-items-center gap-1\">\r\n                <span class=\"font-xs flex gap-2 justify-content-center align-items-center\">\r\n                  <img src=\"/assets/images/dashboard/calendar/edit.svg\" height=\"14\" /> Edit lesson</span>\r\n              </div>\r\n            </button>\r\n          </div>\r\n        </ng-container> -->\r\n\r\n        <!-- \r\n          Conditions When can show button for \"Edit Lesson\":\r\n        if the lesson status is either \"REQUESTED\", \"ARRANGED_TRIAL\", or \"ARRANGED\"\r\n        -->\r\n        <ng-container *ngIf=\"[lessonStatus.REQUESTED, lessonStatus.ARRANGED_TRIAL, lessonStatus.ARRANGED].includes(this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status))\">\r\n          <div class=\"flex justify-content-evenly mb-1\">\r\n            <button pbutton=\"\" type=\"button\"\r\n            (click)=\"onRescheduleSelected()\"\r\n              class=\"lesson-btn reschedule p-button-outlined p-element p-button-rounded p-button-xs p-button p-component gray-border-button p-button-rounded w-full\">\r\n              <div class=\"flex align-items-center gap-1 jus ju\">\r\n                <span class=\"font-xs text-primary flex gap-2 justify-content-center align-items-center\">\r\n\r\n                  <img src=\"/assets/images/dashboard/calendar/reschedule-calendar-icon.svg\" width=\"12\" />\r\n                  Edit Lesson</span>\r\n              </div>\r\n            </button>\r\n          </div>\r\n        </ng-container>\r\n        <!-- <ng-container *ngIf=\"(this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status) === lessonStatus.COMPLETED) && role === UserRoles.TEACHER\">\r\n          <div class=\"flex justify-content-evenly mb-1\">\r\n            <button pbutton=\"\" type=\"button\"\r\n              class=\"lesson-btn reschedule p-button-outlined p-element p-button-rounded p-button-xs p-button p-component gray-border-button p-button-rounded w-full\">\r\n              <div class=\"flex align-items-center gap-1 jus ju\">\r\n                <span class=\"font-xs text-primary flex gap-2 justify-content-center align-items-center\">\r\n                  <img src=\"/assets/images/dashboard/calendar/forward-dark-blue.svg\" width=\"12\" /> Rearrange</span>\r\n              </div>\r\n            </button>\r\n          </div>\r\n        </ng-container> -->\r\n        <!-- <ng-container *ngIf=\"(this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status) === lessonStatus.COMPLETED) && role === UserRoles.TEACHER\">\r\n          <div class=\"flex justify-content-evenly mb-1\">\r\n            <button pbutton=\"\" type=\"button\"\r\n              class=\"p-button-outlined p-element p-button-rounded p-button-xs p-button p-component gray-border-button p-button-rounded w-full\">\r\n              <div class=\"flex align-items-center gap-1 jus ju\">\r\n                <span class=\"font-xs\">Rearrange</span>\r\n              </div>\r\n            </button>\r\n          </div>\r\n        </ng-container> -->\r\n\r\n        <!-- \r\n          Conditions When can show button for \"Cancel\":\r\n          1. The lesson status is \"ARRANGED\"\r\n          2. The user's role is \"STUDENT\"\r\n        -->\r\n        <ng-container *ngIf=\"this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status) === lessonStatus.ARRANGED && role === UserRoles.STUDENT\">\r\n          <div class=\"flex justify-content-evenly mb-1\">\r\n            <button pbutton=\"\"(click)=\"onStudentCancelSelected()\" type=\"button\"\r\n              class=\"p-element p-button-rounded p-button-xs p-button p-component gray-border-button p-button-rounded w-full\">\r\n              <div class=\"flex align-items-center gap-1 jus ju\">\r\n                <span class=\"font-xs text-white flex gap-2 justify-content-center align-items-center\">\r\n                  <img src=\"/assets/images/dashboard/calendar/close-white.svg\" height=\"14\" />\r\n                  Cancel</span>\r\n              </div>\r\n            </button>\r\n          </div>\r\n        </ng-container>\r\n        <!-- \r\n          Conditions When can show button for \"Approve\":\r\n          1. The lesson status is \"REQUESTED\"\r\n          2. The user's role is \"TEACHER\"\r\n        -->\r\n        <ng-container *ngIf=\"this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status) === lessonStatus.REQUESTED && role === UserRoles.TEACHER\">\r\n          <div class=\"flex justify-content-evenly mb-1\">\r\n            <button pbutton=\"\"(click)=\"onLessonAcceptedClicked()\" type=\"button\"\r\n              class=\"p-element p-button-rounded p-button-xs p-button p-component gray-border-button p-button-rounded w-full\">\r\n              <div class=\"flex align-items-center gap-1 jus ju w-4rem\">\r\n                <span class=\"font-xs text-white flex gap-2 justify-content-center align-items-center\">\r\n                  <img src=\"/assets/icons/check-white.svg\" height=\"10\" />\r\n                  Approve</span>\r\n              </div>\r\n            </button>\r\n          </div>\r\n        </ng-container>\r\n        <ng-container *ngIf=\"this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status) === lessonStatus.REQUESTED\">\r\n          <div class=\"flex justify-content-evenly mb-1\">\r\n            <button pbutton=\"\" type=\"button\"\r\n            (click)=\"onLessonRejectedClicked()\"\r\n              class=\"p-button-outlined p-element p-button-rounded p-button-xs p-button p-component gray-border-button p-button-rounded w-full\">\r\n              <div class=\"flex align-items-center gap-1 jus ju w-4rem\">\r\n                <span class=\"font-xs  flex gap-2 justify-content-center align-items-center\">\r\n                 <i class=\"pi pi-times icon-btn font-xs\"></i> {{role === UserRoles.TEACHER ? 'Reject' : 'Remove'}}</span>\r\n              </div>\r\n            </button>\r\n          </div>\r\n        </ng-container> \r\n\r\n        <ng-container *ngIf=\"role === UserRoles.TEACHER\">\r\n          <div class=\"flex justify-content-evenly mb-1\">\r\n            <button pbutton=\"\"(click)=\"onLessonDeleteSelected()\" type=\"button\"\r\n              class=\"p-element p-button-rounded p-button-xs p-button p-component lesson-btn delete-color justify-content-center p-button-rounded w-full\">\r\n              <div class=\"flex align-items-center gap-1 jus tex justify-content-center\">\r\n                <span class=\"font-xs flex gap-2 justify-content-center align-items-center\">\r\n                  <!-- <img src=\"/assets/images/dashboard/calendar/close-white.svg\" height=\"14\" /> -->\r\n                  <i class=\"pi pi-times-circle\"></i>\r\n                  <div>Delete</div></span>\r\n              </div>\r\n            </button>\r\n          </div>\r\n        </ng-container>\r\n\r\n        <!-- \r\n          Conditions When can show button for \"Change Status\":\r\n          1. The user's role is \"TEACHER\"\r\n          2. The lesson status is not \"COMPLETED\"\r\n          3. The lesson status is not \"REQUESTED\"\r\n          4. The lesson status does not include \"CANCELED\"\r\n          5. The lesson status does not include \"NO_SHOW\"\r\n        -->\r\n        <ng-container *ngIf=\"role === UserRoles.TEACHER && this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status) !== lessonStatus.COMPLETED\r\n        && this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status.toLowerCase()) !== lessonStatus.REQUESTED.toLowerCase()\r\n        && !this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status.toLowerCase()).includes(lessonStatus.CANCELED.toLowerCase())\r\n        && !this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status.toLowerCase()).includes(lessonStatus.NO_SHOW.toLowerCase())\">\r\n          <div class=\"flex justify-content-evenly mb-1\">\r\n            <button pbutton=\"\" type=\"button\" (click)=\"op.toggle($event)\"\r\n              class=\"lesson-btn edit-color p-button-outlined p-element p-button-rounded p-button p-button-xs gray-border-button p-component w-full\">\r\n              <div class=\"flex align-items-center gap-1 jus ju\">\r\n                <span class=\"font-xs flex gap-2 justify-content-center align-items-center\">\r\n                  <img src=\"/assets/images/dashboard/calendar/change-status-loading-icon.svg\" height=\"14\" /> Change\r\n                  Status</span>\r\n\r\n                <p-overlayPanel styleClass=\"lesson-info-change-status\" #op [style]=\"{'border-radius': '12px'}\">\r\n                  <ng-template pTemplate=\"content\">\r\n                    <ul class=\"p-0 m-0 list-none font-xs\">\r\n                      <li disableOnClick [disableTime]=\"2000\" class=\"flex align-items-center border-bottom-1 surface-border px-5 py-1 mt-1 cursor-pointer  lesson-text-item\"\r\n                      *ngIf=\"\r\n                      this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status) !== lessonStatus.CANCELED  && \r\n                      !this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status).includes(lessonStatus.NO_SHOW.toLowerCase()) && \r\n                      !this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status).includes(lessonStatus.COMPLETED.toLowerCase())\"\r\n                      (click)=\"changeLessonStatus(this.lessonStatus.COMPLETED)\"><span\r\n                          class=\"text-900 line-height-3 flex align-items-center justify-content-center gap-1\"><img src=\"/assets/images/dashboard/calendar/check.svg\"\r\n                            width=\"14\" />\r\n                          <span class=\"text-700\"> Complete\r\n                          </span></span></li>\r\n                      <li  disableOnClick [disableTime]=\"2000\"\r\n                      *ngIf=\"!this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status).includes(lessonStatus.ARRANGED)\"\r\n                      (click)=\"changeLessonStatus(this.lessonStatus.REARRANGE)\" class=\"flex align-items-center border-bottom-1 surface-border px-5 py-1 mt-1 cursor-pointer lesson-text-item\"><span\r\n                          class=\"text-900 line-height-3 flex align-items-center justify-content-center gap-1\"><img src=\"/assets/images/dashboard/calendar/rearrange.svg\"\r\n                            width=\"14\" />\r\n                          <span class=\"text-700\"> Rearrange </span></span></li>\r\n                      <li disableOnClick [disableTime]=\"2000\"\r\n                      *ngIf=\"\r\n                      !this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status).includes(lessonStatus.NO_SHOW.toLowerCase())\r\n                      && !this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status.toLowerCase()).includes(lessonStatus.CANCELED.toLowerCase())\" (click)=\"changeLessonStatus(this.lessonStatus.NO_SHOW)\" \r\n                      class=\"flex align-items-center border-bottom-1 surface-border px-5 py-1 mt-1 cursor-pointer lesson-text-item\"><span\r\n                          class=\"text-900 line-height-3 flex align-items-center justify-content-center gap-1\"><img src=\"/assets/images/dashboard/calendar/hide.svg\"\r\n                            width=\"14\" />\r\n                          <span class=\"text-700\"> No Show </span></span></li>\r\n                      <li disableOnClick [disableTime]=\"2000\" class=\"flex align-items-center border-bottom-1 surface-border px-5 py-1 mt-1 cursor-pointer lesson-text-item\"\r\n                      *ngIf=\"this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status) !== lessonStatus.CANCELED\"\r\n                      (click)=\"changeLessonStatus(this.lessonStatus.CANCELED)\"><span\r\n                          class=\"text-900 line-height-3 flex align-items-center justify-content-center gap-1\"><img src=\"/assets/images/dashboard/calendar/cancel.svg\"\r\n                            width=\"14\" />\r\n                          <span class=\"text-700\"> Cancel </span></span></li>\r\n                    </ul>\r\n                  </ng-template>\r\n                </p-overlayPanel>\r\n\r\n              </div>\r\n            </button>\r\n          </div>\r\n        </ng-container>\r\n        \r\n\r\n        <!-- TODO: add when functionality is added to change status in a completed lesson -->\r\n        <!-- <ng-container *ngIf=\"role === UserRoles.TEACHER && this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status) === lessonStatus.COMPLETED\">\r\n          <div class=\"flex justify-content-evenly mb-1\">\r\n            <button pbutton=\"\" type=\"button\" (click)=\"op.toggle($event)\"\r\n              class=\"lesson-btn edit-color p-button-outlined p-element p-button-rounded p-button p-button-xs gray-border-button p-component w-full\">\r\n              <div class=\"flex align-items-center gap-1 jus ju\">\r\n                <span class=\"font-xs flex gap-2 justify-content-center align-items-center\">\r\n                  <img src=\"/assets/images/dashboard/calendar/change-status-loading-icon.svg\" height=\"14\" /> Change\r\n                  Status</span>\r\n\r\n                <p-overlayPanel styleClass=\"lesson-info-change-status\" #op [style]=\"{'border-radius': '12px'}\">\r\n                  <ng-template pTemplate=\"content\">\r\n                    <ul class=\"p-0 m-0 list-none font-xs\">\r\n                      <li \r\n                      *ngIf=\"!this.lessonService.getStatusNameFromStatusString(this.dialogData.lesson.status).includes(lessonStatus.ARRANGED)\"\r\n                      (click)=\"changeCompletedToArrangedAndDeleteRating()\" class=\"flex align-items-center border-bottom-1 surface-border px-5 py-1 mt-1 cursor-pointer lesson-text-item\"><span\r\n                          class=\"text-900 line-height-3 flex align-items-center justify-content-center gap-1\"><img src=\"/assets/images/dashboard/calendar/rearrange.svg\"\r\n                            width=\"14\" />\r\n                          <span class=\"text-700\"> Rearrange </span></span></li>\r\n                    </ul>\r\n                  </ng-template>\r\n                </p-overlayPanel>\r\n\r\n              </div>\r\n            </button>\r\n          </div>\r\n        </ng-container> -->\r\n      </div>\r\n    </div>\r\n  </app-block-viewer>\r\n</ng-container>"], "mappings": "AACA,SAA2BA,eAAe,QAAgB,iBAAiB;AAI3E,SAASC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,QAAQ,gBAAgB;AAC3D,SAASC,YAAY,QAA6B,kCAAkC;AACpF,SAAeC,QAAQ,QAAQ,gCAAgC;AAK/D,SAASC,OAAO,QAAQ,SAAS;AACjC,SAASC,mCAAmC,QAAQ,0EAA0E;AAM9H,SAASC,MAAM,QAAQ,qCAAqC;AAC5D,SAASC,QAAQ,EAAEC,EAAE,QAAQ,MAAM;AAEnC,SAAkBC,oBAAoB,EAAEC,WAAW,QAAQ,mCAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICNpFC,EADF,CAAAC,cAAA,YAAiJ,aAC7F;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpEH,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAAE,MAAA,GAAgF;IACpFF,EADoF,CAAAG,YAAA,EAAM,EACrF;;;;IADDH,EAAA,CAAAI,SAAA,GAAgF;IAAhFJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,gBAAA,CAAAC,mBAAA,CAAAF,MAAA,CAAAG,UAAA,CAAAC,SAAA,CAAAC,iBAAA,MAAgF;;;;;IAJtFX,EAAA,CAAAY,uBAAA,GAAiD;IAC/CZ,EAAA,CAAAa,UAAA,IAAAC,6EAAA,iBAAiJ;;;;;IAAtEd,EAAA,CAAAI,SAAA,EAAoE;IAApEJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAG,UAAA,CAAAC,SAAA,IAAAJ,MAAA,CAAAG,UAAA,CAAAC,SAAA,CAAAC,iBAAA,CAAoE;;;;;IAS/IX,EADF,CAAAC,cAAA,YAA+G,aAC3D;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC/DH,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAAE,MAAA,GAAoF;IACxFF,EADwF,CAAAG,YAAA,EAAM,EACzF;;;;IADDH,EAAA,CAAAI,SAAA,GAAoF;IAApFJ,EAAA,CAAAgB,kBAAA,MAAAV,MAAA,CAAAG,UAAA,CAAAC,SAAA,CAAAO,OAAA,CAAAC,SAAA,OAAAZ,MAAA,CAAAG,UAAA,CAAAC,SAAA,CAAAO,OAAA,CAAAE,QAAA,KAAoF;;;;;IALxFnB,EAAA,CAAAY,uBAAA,GAAiD;IAEjDZ,EAAA,CAAAa,UAAA,IAAAO,6EAAA,iBAA+G;;;;;IAApCpB,EAAA,CAAAI,SAAA,EAAkC;IAAlCJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAG,UAAA,CAAAC,SAAA,CAAAO,OAAA,CAAkC;;;;;IAW3GjB,EADF,CAAAC,cAAA,aAAgJ,aAC5F;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC7DH,EAAA,CAAAC,cAAA,aAA+E;IAAAD,EAAA,CAAAE,MAAA,GAAiH;IAClMF,EADkM,CAAAG,YAAA,EAAM,EACnM;;;;IAD4EH,EAAA,CAAAI,SAAA,GAAiH;IAAjHJ,EAAA,CAAAqB,iBAAA,CAAAf,MAAA,CAAAG,UAAA,CAAAC,SAAA,CAAAY,WAAA,GAAAhB,MAAA,CAAAG,UAAA,CAAAC,SAAA,CAAAY,WAAA,GAAAhB,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAb,SAAA,CAAAY,WAAA,CAAiH;;;;;;IA8BhMtB,EAAA,CAAAY,uBAAA,GAAqK;IAGnKZ,EADA,CAAAC,cAAA,cAA8C,iBAY3C;IALHD,EAAA,CAAAwB,UAAA,mBAAAC,kGAAA;MAAAzB,EAAA,CAAA0B,aAAA,CAAAC,GAAA;MAAA,MAAArB,MAAA,GAAAN,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAASvB,MAAA,CAAAwB,cAAA,CAAAC,gBAAA,CAAAzB,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAuD;IAAA,EAAC;IAO7DvB,EADF,CAAAC,cAAA,cAAkD,eACuC;IACrFD,EAAA,CAAAE,MAAA,mBAAW;IAUjBF,EAViB,CAAAG,YAAA,EAAO,EAChB,EACC,EAQH;;;;;IAvBNH,EAAA,CAAAI,SAAA,GAC6G;IAM3GJ,EAPF,CAAAe,UAAA,cAAAT,MAAA,CAAAwB,cAAA,CAAAE,2BAAA,CAAA1B,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAU,YAAA,CAAAC,QAAA,IAAA5B,MAAA,CAAAwB,cAAA,CAAAK,wBAAA,CAAA7B,MAAA,CAAA8B,IAAA,CAAAC,QAAA,GAAA/B,MAAA,CAAAG,UAAA,CAAAc,MAAA,EAC6G,cAAAjB,MAAA,CAAAwB,cAAA,CAAAE,2BAAA,CAAA1B,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAU,YAAA,CAAAC,QAAA,IAAA5B,MAAA,CAAAwB,cAAA,CAAAK,wBAAA,CAAA7B,MAAA,CAAA8B,IAAA,CAAAC,QAAA,GAAA/B,MAAA,CAAAG,UAAA,CAAAc,MAAA,yEAEuE,YAAAvB,EAAA,CAAAsC,eAAA,IAAAC,GAAA,GAAAjC,MAAA,CAAAwB,cAAA,CAAAE,2BAAA,CAAA1B,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAU,YAAA,CAAAC,QAAA,IAAA5B,MAAA,CAAAwB,cAAA,CAAAK,wBAAA,CAAA7B,MAAA,CAAA8B,IAAA,CAAAC,QAAA,GAAA/B,MAAA,CAAAG,UAAA,CAAAc,MAAA,GAMrE;;;;;;IAiCjHvB,EAAA,CAAAY,uBAAA,GACmG;IAE/FZ,EADF,CAAAC,cAAA,cAA8C,iBAGuF;IADnID,EAAA,CAAAwB,UAAA,mBAAAgB,kGAAA;MAAAxC,EAAA,CAAA0B,aAAA,CAAAe,GAAA;MAAA,MAAAnC,MAAA,GAAAN,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAASvB,MAAA,CAAAoC,sBAAA,CAAuB,MAAM,CAAC;IAAA,EAAC;IAGpC1C,EADF,CAAAC,cAAA,cAAkD,eAC4B;IAC1ED,EAAA,CAAA2C,SAAA,cAAsE;IACtE3C,EAAA,CAAAE,MAAA,mBAAW;IAGnBF,EAHmB,CAAAG,YAAA,EAAO,EAChB,EACC,EACL;;;;;;;IASRH,EAAA,CAAAY,uBAAA,GACiI;IAE7HZ,EADF,CAAAC,cAAA,cAA8C,iBAGuF;IADnID,EAAA,CAAAwB,UAAA,mBAAAoB,kGAAA;MAAA5C,EAAA,CAAA0B,aAAA,CAAAmB,GAAA;MAAA,MAAAvC,MAAA,GAAAN,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAASvB,MAAA,CAAAoC,sBAAA,CAAuB,MAAM,CAAC;IAAA,EAAC;IAGpC1C,EADF,CAAAC,cAAA,cAAkD,eAC4B;IAC1ED,EAAA,CAAA2C,SAAA,cAAoE;IACpE3C,EAAA,CAAAE,MAAA,mBAAW;IAGnBF,EAHmB,CAAAG,YAAA,EAAO,EAChB,EACC,EACL;;;;;;IAQRH,EAAA,CAAAY,uBAAA,GAG6F;IAC3FZ,EAAA,CAAAC,cAAA,cACC;IACKD,EAAA,CAAA2C,SAAA,cACqE;IAC3E3C,EAAA,CAAAG,YAAA,EAAM;;;;;;;IAFKH,EAAA,CAAAI,SAAA,GAAmC;IACxCJ,EADK,CAAAe,UAAA,qBAAA+B,iBAAA,CAAmC,4BAAA9C,EAAA,CAAAsC,eAAA,IAAAS,GAAA,EAAAzC,MAAA,CAAAG,UAAA,CAAAc,MAAA,EACsB;;;;;IAWtEvB,EAAA,CAAAY,uBAAA,GAG6F;IAC7FZ,EAAA,CAAA2C,SAAA,cACqE;;;;;;;IADhE3C,EAAA,CAAAI,SAAA,EAAmC;IACxCJ,EADK,CAAAe,UAAA,qBAAA+B,iBAAA,CAAmC,4BAAA9C,EAAA,CAAAsC,eAAA,IAAAS,GAAA,EAAAzC,MAAA,CAAAG,UAAA,CAAAc,MAAA,EACsB;;;;;;IAI5DvB,EAAA,CAAAC,cAAA,iBAMsF;IAHpFD,EAAA,CAAAwB,UAAA,mBAAAwB,iGAAA;MAAA,MAAAC,SAAA,GAAAjD,EAAA,CAAA0B,aAAA,CAAAwB,GAAA,EAAA3B,MAAA;MAAA,MAAAjB,MAAA,GAAAN,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,EAAAoB,SAAA,CAAAE,UAAA,IAAA7C,MAAA,CAAA8C,IAAA,KAAA9C,MAAA,CAAA+C,SAAA,CAAAC,OAAA,GAA4DhD,MAAA,CAAAiD,kBAAA,EAAoB,GAAG,IAAI;IAAA,EAAC;IAKpFvD,EADF,CAAAC,cAAA,cAAkD,eAEoC;IAClFD,EAAA,CAAA2C,SAAA,cAA+E;IAC/E3C,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAGN;IAGNF,EAHM,CAAAG,YAAA,EAAO,EACF,EACH,EACC;;;;;IAZPH,EALe,CAAAe,UAAA,aAAAkC,SAAA,CAAAE,UAAA,IAAA7C,MAAA,CAAA8C,IAAA,KAAA9C,MAAA,CAAA+C,SAAA,CAAAG,OAAA,CAA4D,aAAAP,SAAA,CAAAE,UAAA,gDACA,YAAAnD,EAAA,CAAAyD,eAAA,IAAAC,GAAA,EAAAT,SAAA,CAAAE,UAAA,GAAAF,SAAA,CAAAE,UAAA,IAAA7C,MAAA,CAAA8C,IAAA,KAAA9C,MAAA,CAAA+C,SAAA,CAAAG,OAAA,EAKM;IAGzExD,EAAA,CAAAI,SAAA,GAA6E;IAA7EJ,EAAA,CAAAe,UAAA,YAAAf,EAAA,CAAAsC,eAAA,IAAAqB,GAAA,GAAAV,SAAA,CAAAE,UAAA,IAAA7C,MAAA,CAAA8C,IAAA,KAAA9C,MAAA,CAAA+C,SAAA,CAAAC,OAAA,EAA6E;IAE3EtD,EAAA,CAAAI,SAAA,GAGN;IAHMJ,EAAA,CAAAK,kBAAA,MAAA4C,SAAA,CAAAE,UAAA,IAAA7C,MAAA,CAAA8C,IAAA,KAAA9C,MAAA,CAAA+C,SAAA,CAAAC,OAAA,oBAAAL,SAAA,CAAAE,UAAA,IAAA7C,MAAA,CAAA8C,IAAA,KAAA9C,MAAA,CAAA+C,SAAA,CAAAG,OAAA,0CAGN;;;;;;IAqBVxD,EAAA,CAAAY,uBAAA,GAA6L;IAEzLZ,EADF,CAAAC,cAAA,cAA8C,iBAG6G;IADzJD,EAAA,CAAAwB,UAAA,mBAAAoC,kGAAA;MAAA5D,EAAA,CAAA0B,aAAA,CAAAmC,GAAA;MAAA,MAAAvD,MAAA,GAAAN,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAASvB,MAAA,CAAAwD,oBAAA,EAAsB;IAAA,EAAC;IAG5B9D,EADF,CAAAC,cAAA,cAAkD,eACwC;IAEtFD,EAAA,CAAA2C,SAAA,cAAuF;IACvF3C,EAAA,CAAAE,MAAA,mBAAW;IAGnBF,EAHmB,CAAAG,YAAA,EAAO,EAChB,EACC,EACL;;;;;;;IA6BRH,EAAA,CAAAY,uBAAA,GAA8J;IAE1JZ,EADF,CAAAC,cAAA,cAA8C,iBAEqE;IAD/FD,EAAA,CAAAwB,UAAA,mBAAAuC,kGAAA;MAAA/D,EAAA,CAAA0B,aAAA,CAAAsC,IAAA;MAAA,MAAA1D,MAAA,GAAAN,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAASvB,MAAA,CAAA2D,uBAAA,EAAyB;IAAA,EAAC;IAGjDjE,EADF,CAAAC,cAAA,cAAkD,eACsC;IACpFD,EAAA,CAAA2C,SAAA,cAA2E;IAC3E3C,EAAA,CAAAE,MAAA,cAAM;IAGdF,EAHc,CAAAG,YAAA,EAAO,EACX,EACC,EACL;;;;;;;IAORH,EAAA,CAAAY,uBAAA,GAA+J;IAE3JZ,EADF,CAAAC,cAAA,cAA8C,iBAEqE;IAD/FD,EAAA,CAAAwB,UAAA,mBAAA0C,kGAAA;MAAAlE,EAAA,CAAA0B,aAAA,CAAAyC,IAAA;MAAA,MAAA7D,MAAA,GAAAN,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAASvB,MAAA,CAAA8D,uBAAA,EAAyB;IAAA,EAAC;IAGjDpE,EADF,CAAAC,cAAA,cAAyD,eAC+B;IACpFD,EAAA,CAAA2C,SAAA,cAAuD;IACvD3C,EAAA,CAAAE,MAAA,eAAO;IAGfF,EAHe,CAAAG,YAAA,EAAO,EACZ,EACC,EACL;;;;;;;IAERH,EAAA,CAAAY,uBAAA,GAAiI;IAE7HZ,EADF,CAAAC,cAAA,cAA8C,iBAGuF;IADnID,EAAA,CAAAwB,UAAA,mBAAA6C,kGAAA;MAAArE,EAAA,CAAA0B,aAAA,CAAA4C,IAAA;MAAA,MAAAhE,MAAA,GAAAN,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAASvB,MAAA,CAAAiE,uBAAA,EAAyB;IAAA,EAAC;IAG/BvE,EADF,CAAAC,cAAA,cAAyD,eACqB;IAC3ED,EAAA,CAAA2C,SAAA,YAA4C;IAAC3C,EAAA,CAAAE,MAAA,GAAoD;IAGxGF,EAHwG,CAAAG,YAAA,EAAO,EACrG,EACC,EACL;;;;;IAH8CH,EAAA,CAAAI,SAAA,GAAoD;IAApDJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA8C,IAAA,KAAA9C,MAAA,CAAA+C,SAAA,CAAAC,OAAA,2BAAoD;;;;;;IAM1GtD,EAAA,CAAAY,uBAAA,GAAiD;IAE7CZ,EADF,CAAAC,cAAA,cAA8C,iBAEiG;IAD3HD,EAAA,CAAAwB,UAAA,mBAAAgD,kGAAA;MAAAxE,EAAA,CAAA0B,aAAA,CAAA+C,IAAA;MAAA,MAAAnE,MAAA,GAAAN,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAASvB,MAAA,CAAAoE,sBAAA,EAAwB;IAAA,EAAC;IAGhD1E,EADF,CAAAC,cAAA,cAA0E,eACG;IAEzED,EAAA,CAAA2C,SAAA,YAAkC;IAClC3C,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAGnBF,EAHmB,CAAAG,YAAA,EAAM,EAAO,EACtB,EACC,EACL;;;;;;;IA0BMH,EAAA,CAAAC,cAAA,aAK0D;IAA1DD,EAAA,CAAAwB,UAAA,mBAAAmD,iHAAA;MAAA3E,EAAA,CAAA0B,aAAA,CAAAkD,IAAA;MAAA,MAAAtE,MAAA,GAAAN,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAASvB,MAAA,CAAAuE,kBAAA,CAAAvE,MAAA,CAAAwE,YAAA,CAAAC,SAAA,CAA+C;IAAA,EAAC;IAAC/E,EAAA,CAAAC,cAAA,eAC8B;IAAAD,EAAA,CAAA2C,SAAA,cACrE;IACf3C,EAAA,CAAAC,cAAA,eAAuB;IAACD,EAAA,CAAAE,MAAA,iBACxB;IAAcF,EAAd,CAAAG,YAAA,EAAO,EAAO,EAAK;;;IATJH,EAAA,CAAAe,UAAA,qBAAoB;;;;;;IAUvCf,EAAA,CAAAC,cAAA,aAEwK;IAAxKD,EAAA,CAAAwB,UAAA,mBAAAwD,iHAAA;MAAAhF,EAAA,CAAA0B,aAAA,CAAAuD,IAAA;MAAA,MAAA3E,MAAA,GAAAN,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAASvB,MAAA,CAAAuE,kBAAA,CAAAvE,MAAA,CAAAwE,YAAA,CAAAI,SAAA,CAA+C;IAAA,EAAC;IAA+GlF,EAAA,CAAAC,cAAA,eAChF;IAAAD,EAAA,CAAA2C,SAAA,cACrE;IACf3C,EAAA,CAAAC,cAAA,eAAuB;IAACD,EAAA,CAAAE,MAAA,kBAAU;IAAcF,EAAd,CAAAG,YAAA,EAAO,EAAO,EAAK;;;IALrCH,EAAA,CAAAe,UAAA,qBAAoB;;;;;;IAMxCf,EAAA,CAAAC,cAAA,aAI8G;IADmCD,EAAA,CAAAwB,UAAA,mBAAA2D,iHAAA;MAAAnF,EAAA,CAAA0B,aAAA,CAAA0D,IAAA;MAAA,MAAA9E,MAAA,GAAAN,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAASvB,MAAA,CAAAuE,kBAAA,CAAAvE,MAAA,CAAAwE,YAAA,CAAAO,OAAA,CAA6C;IAAA,EAAC;IAC1FrF,EAAA,CAAAC,cAAA,eACtB;IAAAD,EAAA,CAAA2C,SAAA,cACrE;IACf3C,EAAA,CAAAC,cAAA,eAAuB;IAACD,EAAA,CAAAE,MAAA,gBAAQ;IAAcF,EAAd,CAAAG,YAAA,EAAO,EAAO,EAAK;;;IAPpCH,EAAA,CAAAe,UAAA,qBAAoB;;;;;;IAQvCf,EAAA,CAAAC,cAAA,aAEyD;IAAzDD,EAAA,CAAAwB,UAAA,mBAAA8D,iHAAA;MAAAtF,EAAA,CAAA0B,aAAA,CAAA6D,IAAA;MAAA,MAAAjF,MAAA,GAAAN,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAASvB,MAAA,CAAAuE,kBAAA,CAAAvE,MAAA,CAAAwE,YAAA,CAAAU,QAAA,CAA8C;IAAA,EAAC;IAACxF,EAAA,CAAAC,cAAA,eAC+B;IAAAD,EAAA,CAAA2C,SAAA,cACrE;IACf3C,EAAA,CAAAC,cAAA,eAAuB;IAACD,EAAA,CAAAE,MAAA,eAAO;IAAcF,EAAd,CAAAG,YAAA,EAAO,EAAO,EAAK;;;IALnCH,EAAA,CAAAe,UAAA,qBAAoB;;;;;IAzBzCf,EAAA,CAAAC,cAAA,aAAsC;IAyBpCD,EAxBA,CAAAa,UAAA,IAAA4E,4FAAA,iBAK0D,IAAAC,4FAAA,iBAO8G,IAAAC,4FAAA,iBAQ1D,IAAAC,4FAAA,iBAMrD;IAI3D5F,EAAA,CAAAG,YAAA,EAAK;;;;IA7BFH,EAAA,CAAAI,SAAA,EAIL;IAJKJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAuF,aAAA,CAAAC,6BAAA,CAAAxF,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAwE,MAAA,MAAAzF,MAAA,CAAAwE,YAAA,CAAAU,QAAA,KAAAlF,MAAA,CAAAuF,aAAA,CAAAC,6BAAA,CAAAxF,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAwE,MAAA,EAAAC,QAAA,CAAA1F,MAAA,CAAAwE,YAAA,CAAAO,OAAA,CAAAY,WAAA,QAAA3F,MAAA,CAAAuF,aAAA,CAAAC,6BAAA,CAAAxF,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAwE,MAAA,EAAAC,QAAA,CAAA1F,MAAA,CAAAwE,YAAA,CAAAC,SAAA,CAAAkB,WAAA,IAIL;IAMKjG,EAAA,CAAAI,SAAA,EAAsH;IAAtHJ,EAAA,CAAAe,UAAA,UAAAT,MAAA,CAAAuF,aAAA,CAAAC,6BAAA,CAAAxF,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAwE,MAAA,EAAAC,QAAA,CAAA1F,MAAA,CAAAwE,YAAA,CAAAoB,QAAA,EAAsH;IAMtHlG,EAAA,CAAAI,SAAA,EAEoK;IAFpKJ,EAAA,CAAAe,UAAA,UAAAT,MAAA,CAAAuF,aAAA,CAAAC,6BAAA,CAAAxF,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAwE,MAAA,EAAAC,QAAA,CAAA1F,MAAA,CAAAwE,YAAA,CAAAO,OAAA,CAAAY,WAAA,QAAA3F,MAAA,CAAAuF,aAAA,CAAAC,6BAAA,CAAAxF,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAwE,MAAA,CAAAE,WAAA,IAAAD,QAAA,CAAA1F,MAAA,CAAAwE,YAAA,CAAAU,QAAA,CAAAS,WAAA,IAEoK;IAMpKjG,EAAA,CAAAI,SAAA,EAA+G;IAA/GJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAuF,aAAA,CAAAC,6BAAA,CAAAxF,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAwE,MAAA,MAAAzF,MAAA,CAAAwE,YAAA,CAAAU,QAAA,CAA+G;;;;;;IAxC9HxF,EAAA,CAAAY,uBAAA,GAGgJ;IAE5IZ,EADF,CAAAC,cAAA,cAA8C,iBAE4F;IADvGD,EAAA,CAAAwB,UAAA,mBAAA2E,kGAAAC,MAAA;MAAApG,EAAA,CAAA0B,aAAA,CAAA2E,IAAA;MAAA,MAAAC,MAAA,GAAAtG,EAAA,CAAAuG,WAAA;MAAA,OAAAvG,EAAA,CAAA6B,WAAA,CAASyE,MAAA,CAAAE,MAAA,CAAAJ,MAAA,CAAiB;IAAA,EAAC;IAGxDpG,EADF,CAAAC,cAAA,cAAkD,eAC2B;IACzED,EAAA,CAAA2C,SAAA,cAA0F;IAAC3C,EAAA,CAAAE,MAAA,qBACrF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAEfH,EAAA,CAAAC,cAAA,4BAA+F;IAC7FD,EAAA,CAAAa,UAAA,IAAA4F,uFAAA,0BAAiC;IAsCzCzG,EAJM,CAAAG,YAAA,EAAiB,EAEb,EACC,EACL;;;;IAvC2DH,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAA0G,UAAA,CAAA1G,EAAA,CAAA2G,eAAA,IAAAC,GAAA,EAAmC;;;;;;IA3U9G5G,EAAA,CAAAY,uBAAA,GAAsC;IACpCZ,EAAA,CAAAC,cAAA,0BAKuC;IAArCD,EAAA,CAAAwB,UAAA,8BAAAqF,uGAAA;MAAA7G,EAAA,CAAA0B,aAAA,CAAAoF,GAAA;MAAA,MAAAxG,MAAA,GAAAN,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAAoBvB,MAAA,CAAAyG,aAAA,EAAe;IAAA,EAAC;IAEpC/G,EAAA,CAAAC,cAAA,aAA2B;IAEzBD,EAAA,CAAA2C,SAAA,aACsF;IACxF3C,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,YAAgF;IAQ9ED,EAPA,CAAAa,UAAA,IAAAmG,wEAAA,0BAAiD,IAAAC,wEAAA,0BAOA;IAS/CjH,EADF,CAAAC,cAAA,YAA0E,aACtB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAChEH,EAAA,CAAAC,cAAA,cAA+E;IAAAD,EAAA,CAAAE,MAAA,IAAwG;IACzLF,EADyL,CAAAG,YAAA,EAAM,EAC1L;IACLH,EAAA,CAAAa,UAAA,KAAAqG,+DAAA,iBAAgJ;IAK9IlH,EADF,CAAAC,cAAA,cAA0E,cACtB;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjEH,EAAA,CAAAC,cAAA,eAAoE;IAAAD,EAAA,CAAAE,MAAA,IAAkE;IACxIF,EADwI,CAAAG,YAAA,EAAM,EACzI;IAEHH,EADF,CAAAC,cAAA,cAA0E,cACtB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAChEH,EAAA,CAAAC,cAAA,eAAoE;IAClED,EAAA,CAAAE,MAAA,IAA8E;IAClFF,EADkF,CAAAG,YAAA,EAAM,EACnF;IAEHH,EADF,CAAAC,cAAA,cAA0E,cACtB;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5DH,EAAA,CAAAC,cAAA,eAAoE;IAClED,EAAA,CAAAE,MAAA,IAAwD;;IAE9DF,EAF8D,CAAAG,YAAA,EAAM,EAC7D,EACF;IAGHH,EADF,CAAAC,cAAA,eAA2B,eACE;IAWzBD,EAAA,CAAAa,UAAA,KAAAsG,yEAAA,0BAAqK;IA+BnKnH,EADF,CAAAC,cAAA,eAA8C,kBAGuF;IADnID,EAAA,CAAAwB,UAAA,mBAAA4F,mFAAA;MAAApH,EAAA,CAAA0B,aAAA,CAAAoF,GAAA;MAAA,MAAAxG,MAAA,GAAAN,EAAA,CAAA4B,aAAA;MAAA,OAAA5B,EAAA,CAAA6B,WAAA,CAASvB,MAAA,CAAA+G,uBAAA,EAAyB;IAAA,EAAC;IAG/BrH,EADF,CAAAC,cAAA,eAAkD,gBAC4B;IAC1ED,EAAA,CAAAE,MAAA,wBAAe;IAGvBF,EAHuB,CAAAG,YAAA,EAAO,EACpB,EACC,EACL;IAuNNH,EAhNA,CAAAa,UAAA,KAAAyG,yEAAA,0BACmG,KAAAC,yEAAA,0BAqB8B,KAAAC,yEAAA,0BAsBpC,KAAAC,yEAAA,0BAkBA,KAAAC,wEAAA,iCAAA1H,EAAA,CAAA2H,sBAAA,CAK5C,KAAAC,yEAAA,0BAoC4I,KAAAC,yEAAA,0BAyC/B,KAAAC,yEAAA,0BAiBC,KAAAC,yEAAA,0BAY9B,KAAAC,yEAAA,0BAahF,KAAAC,yEAAA,2BAyB+F;IAiFtJjI,EAFI,CAAAG,YAAA,EAAM,EACF,EACW;;;;;IAjZjBH,EAAA,CAAAI,SAAA,EAAwH;IAAxHJ,EAAA,CAAAkI,sBAAA,4FAAA5H,MAAA,CAAA6H,wBAAA,WAAwH;IAGxCnI,EAJhE,CAAAe,UAAA,WAAAT,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAwE,MAAA,CAAwC,8CAGkC,gFACX,6BAA6B;IAM1G/F,EAAA,CAAAI,SAAA,GAAmF;IAAnFJ,EAAA,CAAAkI,sBAAA,8CAAA5H,MAAA,CAAA6H,wBAAA,qBAAAnI,EAAA,CAAAoI,aAAA,CAAmF;IAGpEpI,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAA8C,IAAA,KAAA9C,MAAA,CAAA+C,SAAA,CAAAC,OAAA,CAAgC;IAOhCtD,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAA8C,IAAA,KAAA9C,MAAA,CAAA+C,SAAA,CAAAG,OAAA,CAAgC;IAUkCxD,EAAA,CAAAI,SAAA,GAAwG;IAAxGJ,EAAA,CAAAqB,iBAAA,CAAAf,MAAA,CAAAG,UAAA,CAAAC,SAAA,CAAA2H,QAAA,GAAA/H,MAAA,CAAAG,UAAA,CAAAC,SAAA,CAAA2H,QAAA,GAAA/H,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAb,SAAA,CAAA2H,QAAA,CAAwG;IAEpLrI,EAAA,CAAAI,SAAA,EAAmE;IAAnEJ,EAAA,CAAAe,UAAA,UAAAT,MAAA,CAAAC,gBAAA,CAAA+H,gBAAA,CAAAhI,MAAA,CAAAG,UAAA,CAAAC,SAAA,EAAmE;IAMFV,EAAA,CAAAI,SAAA,GAAkE;IAAlEJ,EAAA,CAAAqB,iBAAA,CAAAf,MAAA,CAAAwB,cAAA,CAAAyG,aAAA,CAAAjI,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAU,YAAA,EAAkE;IAKpIjC,EAAA,CAAAI,SAAA,GAA8E;IAA9EJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAwB,cAAA,CAAA0G,+BAAA,CAAAlI,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAkH,QAAA,MAA8E;IAK9EzI,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAK,kBAAA,MAAAL,EAAA,CAAA0I,WAAA,SAAApI,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAU,YAAA,oBAAwD;IAgB3CjC,EAAA,CAAAI,SAAA,GAAoJ;IAApJJ,EAAA,CAAAe,UAAA,SAAAf,EAAA,CAAAyD,eAAA,KAAAkF,GAAA,EAAArI,MAAA,CAAAwE,YAAA,CAAA8D,cAAA,EAAAtI,MAAA,CAAAwE,YAAA,CAAAoB,QAAA,EAAAF,QAAA,CAAA1F,MAAA,CAAAuF,aAAA,CAAAC,6BAAA,CAAAxF,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAwE,MAAA,GAAoJ;IA8CpJ/F,EAAA,CAAAI,SAAA,GACiF;IADjFJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAuF,aAAA,CAAAC,6BAAA,CAAAxF,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAwE,MAAA,MAAAzF,MAAA,CAAAwE,YAAA,CAAAC,SAAA,KAAAzE,MAAA,CAAAC,gBAAA,CAAA+H,gBAAA,CAAAhI,MAAA,CAAAG,UAAA,CAAAC,SAAA,KAAAJ,MAAA,CAAAuI,wBAAA,CACiF;IAoBjF7I,EAAA,CAAAI,SAAA,EAC+G;IAD/GJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAuF,aAAA,CAAAC,6BAAA,CAAAxF,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAwE,MAAA,MAAAzF,MAAA,CAAAwE,YAAA,CAAAC,SAAA,KAAAzE,MAAA,CAAAC,gBAAA,CAAA+H,gBAAA,CAAAhI,MAAA,CAAAG,UAAA,CAAAC,SAAA,KAAAJ,MAAA,CAAAuI,wBAAA,IAAAvI,MAAA,CAAA8C,IAAA,KAAA9C,MAAA,CAAA+C,SAAA,CAAAC,OAAA,CAC+G;IAmB/GtD,EAAA,CAAAI,SAAA,EAIpB;IAJoBJ,EAAA,CAAAe,UAAA,UAAAT,MAAA,CAAAuF,aAAA,CAAAC,6BAAA,CAAAxF,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAwE,MAAA,MAAAzF,MAAA,CAAAwE,YAAA,CAAAU,QAAA,CAAAS,WAAA,MAAA3F,MAAA,CAAAuF,aAAA,CAAAC,6BAAA,CAAAxF,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAwE,MAAA,MAAAzF,MAAA,CAAAwE,YAAA,CAAAO,OAAA,CAAAY,WAAA,QAAA3F,MAAA,CAAAC,gBAAA,CAAA+H,gBAAA,CAAAhI,MAAA,CAAAG,UAAA,CAAAC,SAAA,KAAAJ,MAAA,CAAA8C,IAAA,KAAA9C,MAAA,CAAA+C,SAAA,CAAAC,OAAA,CAIpB;IAcoBtD,EAAA,CAAAI,SAAA,EAIpB;IAJoBJ,EAAA,CAAAe,UAAA,UAAAT,MAAA,CAAAuF,aAAA,CAAAC,6BAAA,CAAAxF,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAwE,MAAA,MAAAzF,MAAA,CAAAwE,YAAA,CAAAU,QAAA,CAAAS,WAAA,MAAA3F,MAAA,CAAAuF,aAAA,CAAAC,6BAAA,CAAAxF,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAwE,MAAA,MAAAzF,MAAA,CAAAwE,YAAA,CAAAO,OAAA,CAAAY,WAAA,QAAA3F,MAAA,CAAAC,gBAAA,CAAA+H,gBAAA,CAAAhI,MAAA,CAAAG,UAAA,CAAAC,SAAA,KAAAJ,MAAA,CAAA8C,IAAA,KAAA9C,MAAA,CAAA+C,SAAA,CAAAG,OAAA,CAIpB;IAwCoBxD,EAAA,CAAAI,SAAA,GAA4K;IAA5KJ,EAAA,CAAAe,UAAA,SAAAf,EAAA,CAAA8I,eAAA,KAAAC,GAAA,EAAAzI,MAAA,CAAAwE,YAAA,CAAAkE,SAAA,EAAA1I,MAAA,CAAAwE,YAAA,CAAA8D,cAAA,EAAAtI,MAAA,CAAAwE,YAAA,CAAAoB,QAAA,EAAAF,QAAA,CAAA1F,MAAA,CAAAuF,aAAA,CAAAC,6BAAA,CAAAxF,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAwE,MAAA,GAA4K;IAyC5K/F,EAAA,CAAAI,SAAA,EAA6I;IAA7IJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAuF,aAAA,CAAAC,6BAAA,CAAAxF,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAwE,MAAA,MAAAzF,MAAA,CAAAwE,YAAA,CAAAoB,QAAA,IAAA5F,MAAA,CAAA8C,IAAA,KAAA9C,MAAA,CAAA+C,SAAA,CAAAG,OAAA,CAA6I;IAiB7IxD,EAAA,CAAAI,SAAA,EAA8I;IAA9IJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAuF,aAAA,CAAAC,6BAAA,CAAAxF,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAwE,MAAA,MAAAzF,MAAA,CAAAwE,YAAA,CAAAkE,SAAA,IAAA1I,MAAA,CAAA8C,IAAA,KAAA9C,MAAA,CAAA+C,SAAA,CAAAC,OAAA,CAA8I;IAY9ItD,EAAA,CAAAI,SAAA,EAAgH;IAAhHJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAAuF,aAAA,CAAAC,6BAAA,CAAAxF,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAwE,MAAA,MAAAzF,MAAA,CAAAwE,YAAA,CAAAkE,SAAA,CAAgH;IAahHhJ,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAA8C,IAAA,KAAA9C,MAAA,CAAA+C,SAAA,CAAAC,OAAA,CAAgC;IAsBhCtD,EAAA,CAAAI,SAAA,EAG4H;IAH5HJ,EAAA,CAAAe,UAAA,SAAAT,MAAA,CAAA8C,IAAA,KAAA9C,MAAA,CAAA+C,SAAA,CAAAC,OAAA,IAAAhD,MAAA,CAAAuF,aAAA,CAAAC,6BAAA,CAAAxF,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAwE,MAAA,MAAAzF,MAAA,CAAAwE,YAAA,CAAAC,SAAA,IAAAzE,MAAA,CAAAuF,aAAA,CAAAC,6BAAA,CAAAxF,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAwE,MAAA,CAAAE,WAAA,QAAA3F,MAAA,CAAAwE,YAAA,CAAAkE,SAAA,CAAA/C,WAAA,OAAA3F,MAAA,CAAAuF,aAAA,CAAAC,6BAAA,CAAAxF,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAwE,MAAA,CAAAE,WAAA,IAAAD,QAAA,CAAA1F,MAAA,CAAAwE,YAAA,CAAAU,QAAA,CAAAS,WAAA,QAAA3F,MAAA,CAAAuF,aAAA,CAAAC,6BAAA,CAAAxF,MAAA,CAAAG,UAAA,CAAAc,MAAA,CAAAwE,MAAA,CAAAE,WAAA,IAAAD,QAAA,CAAA1F,MAAA,CAAAwE,YAAA,CAAAO,OAAA,CAAAY,WAAA,IAG4H;;;ADpSnJ,OAAM,MAAOgD,iCAAiC;EAc5CC,YACUC,GAAqB,EACrBC,MAA2B,EAC3BC,WAAwB,EACxBC,MAAc,EACdC,sBAA8C,EAC9CC,aAA4B,EAC7B3D,aAA4B,EAC5BtF,gBAAkC,EAClCkJ,eAAgC,EAChC3H,cAA8B,EAC9B4H,oBAA0C,EAC1CC,YAA0B,EAC1BC,cAA8B;IAZ7B,KAAAT,GAAG,GAAHA,GAAG;IACH,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,aAAa,GAAbA,aAAa;IACd,KAAA3D,aAAa,GAAbA,aAAa;IACb,KAAAtF,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAkJ,eAAe,GAAfA,eAAe;IACf,KAAA3H,cAAc,GAAdA,cAAc;IACd,KAAA4H,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,cAAc,GAAdA,cAAc;IAzBvB,KAAAxG,IAAI,GAAa5D,QAAQ,CAACqK,IAAI;IAC9B,KAAAxG,SAAS,GAAG7D,QAAQ;IACpB,KAAAsK,cAAc,GAAiBvK,YAAY,CAAC2G,QAAQ;IACpD,KAAApB,YAAY,GAAGvF,YAAY;IAC3B,KAAAwK,WAAW,GAAWpK,MAAM,CAACqK,eAAe;IAC5C,KAAAvJ,UAAU,GAAQ,EAAE;IACpB,KAAAoI,wBAAwB,GAAG,KAAK;IAChC,KAAAoB,IAAI,GAAG,IAAIxK,OAAO,EAAE;IACpB,KAAAyK,cAAc,GAAG,MAAM;IAEP,KAAAC,oBAAoB,GAAGrK,oBAAoB;IAC3D,KAAAsC,IAAI,GAAS,EAAU;EAenB;EAEJgI,QAAQA,CAAA;IACN,IAAI,CAAChI,IAAI,GAAG,IAAI,CAACiH,WAAW,CAACgB,eAAe,EAAE;IAC9C,IAAI,CAACjH,IAAI,GAAG,IAAI,CAACiG,WAAW,CAACgB,eAAe,EAAE,CAACjH,IAAI;IACnD,IAAI,CAAC3C,UAAU,GAAG,IAAI,CAAC2I,MAAM,CAACkB,IAAI,CAAC7J,UAAU;IAC7C;IACA,IAAI,CAACA,UAAU,CAACC,SAAS,GAAG,CAAC,IAAI,CAACoB,cAAc,CAACyI,eAAe,CAAC,IAAI,CAAC9J,UAAU,CAACC,SAAS,CAAC,GAAG,IAAI,CAACD,UAAU,CAACC,SAAS,GAAG,IAAI,CAACD,UAAU,CAACc,MAAM,CAACb,SAAS;IAC1J8J,OAAO,CAACC,GAAG,CAAC,IAAI,CAAChK,UAAU,CAAC;IAC5B,IAAI,CAACiK,yBAAyB,EAAE;IAChC,IAAI,CAACC,wBAAwB,EAAE;IAC/B,IAAI,CAACC,0BAA0B,EAAE;IAEjC,IAAI,CAACX,IAAI,CAACY,IAAI,GAAG,IAAI,CAACvB,MAAM,CAACwB,MAAM,CAACC,SAAS,CAACC,KAAK,IAAG;MACpD,IAAIA,KAAK,YAAY9L,eAAe,EAAE;QACpC;QACA,IAAI,CAACiK,GAAG,CAAC8B,KAAK,EAAE;MAClB;IACF,CAAC,CAAC;IACF,IAAI,CAAChB,IAAI,CAACY,IAAI,GAAG,IAAI,CAACrB,aAAa,CAACU,cAAc,CAACgB,IAAI,CAAC9L,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC2L,SAAS,CAAEI,GAAG,IAAI;MACjF,IAAIA,GAAG,KAAK,CAAC,EAAE;QACb,IAAI,CAACjB,cAAc,GAAGiB,GAAG,GAAG,IAAI;MAClC;IACF,CAAC,CAAC;IACF;EACF;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACjC,GAAG,CAAC8B,KAAK,EAAE;IAChB,IAAI,CAAChB,IAAI,CAACoB,WAAW,EAAE;IACvB,IAAI,CAAC5B,eAAe,CAAC6B,iBAAiB,CAAC,KAAK,CAAC;EAC/C;EAEAvE,aAAaA,CAACuD,IAAU;IACtB,IAAI,CAACnB,GAAG,CAAC8B,KAAK,CAACX,IAAI,CAAC;EACtB;EAEAxG,oBAAoBA,CAAA;IAClB,MAAMyH,gBAAgB,GAAqB;MACzCC,WAAW,EAAE;QAAE,QAAQ,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACjL,UAAU,CAACc,MAAM,CAAC;QAAE,YAAY,EAAE;MAAI;KACpF;IACD,IAAI,CAAC+H,MAAM,CAACqC,QAAQ,CAAC,CAAC,oCAAoC,CAAC,EAAEJ,gBAAgB,CAAC;IAC9E,IAAI,CAACpC,GAAG,CAAC8B,KAAK,EAAE;EAClB;EAEA5D,uBAAuBA,CAAA;IACrB,IAAI,CAACvF,cAAc,CAAC8J,uBAAuB,CAAC,IAAI,CAACnL,UAAU,CAACC,SAAS,EAAE,IAAI,CAAC2I,WAAW,CAACwC,WAAW,EAAE,CAAC;IACtG,IAAI,CAAC1C,GAAG,CAAC8B,KAAK,EAAE;EAClB;EAEAvI,sBAAsBA,CAACoJ,UAAU,GAAG,QAAQ;IAC1CtB,OAAO,CAACC,GAAG,CAAC,IAAI,CAAChK,UAAU,CAAC;IAC5B,MAAMsL,wBAAwB,GAAGC,QAAQ,CAACC,cAAc,CAAC,mBAAmB,CAAC;IAC7EzB,OAAO,CAACC,GAAG,CAACsB,wBAAyB,CAACG,qBAAqB,EAAE,CAACC,KAAK,CAAC;IAEpE,IAAI,CAACrK,cAAc,CAACsK,uBAAuB,CAAC,IAAI,CAAC3L,UAAU,CAAC4L,aAAa,EAAE3M,mCAAmC,EAC5GqM,wBAAyB,CAACG,qBAAqB,EAAE,CAACC,KAAK,EACvD;MACE5K,MAAM,EAAE,IAAI,CAACd,UAAU,CAACc,MAAM;MAC9Bb,SAAS,EAAE,IAAI,CAACD,UAAU,CAACC,SAAS;MACpCoL,UAAU,EAAEA;KACb,EAAE,mBAAmB,EAAGQ,MAAW,IAAI;MACtC9B,OAAO,CAACC,GAAG,CAAC6B,MAAM,CAAC;MACnB;MACA;MACA;IACF,CAAC,EAAE,IAAI,CAACpC,cAAc,CAAC;IACzB,IAAI,CAACf,GAAG,CAAC8B,KAAK,EAAE;EAClB;EAEA;;;;;EAKApG,kBAAkBA,CAAC0H,YAAoB;IACrC;IACA,MAAMxG,MAAM,GAAG,IAAI,CAACyG,yBAAyB,CAACD,YAAY,CAAC;IAE3D,IAAI,CAACxG,MAAM,EAAE;MACX;MACA;IACF;IAEA;IACA,MAAMuC,gBAAgB,GAAG,IAAI,CAAC/H,gBAAgB,CAAC+H,gBAAgB,CAC7D,IAAI,CAAC7H,UAAU,CAACC,SAAS,CAC1B;IAED,IAAIqF,MAAM,KAAKxG,YAAY,CAACwF,SAAS,IAAI,CAACuD,gBAAgB,EAAE;MAC1D;MACA,IAAI,CAAC5F,sBAAsB,EAAE;MAC7B;IACF;IAEA,IAAIqD,MAAM,KAAKxG,YAAY,CAACiG,QAAQ,EAAE;MACpC;MACA,IAAI,CAACiH,oBAAoB,EAAE;MAC3B;IACF;IAEA;IACA,IAAI,CAACC,YAAY,CAAC3G,MAAM,CAAC;EAC3B;EAEA;;;;;;EAMQyG,yBAAyBA,CAACD,YAAoB;IAEpD,MAAMI,eAAe,GAAGJ,YAAY,CAACtG,WAAW,EAAE;IAElD,QAAQ0G,eAAe;MACrB,KAAK,WAAW;QACd,OAAO,IAAI,CAACpM,gBAAgB,CAAC+H,gBAAgB,CAAC,IAAI,CAAC7H,UAAU,CAACC,SAAS,CAAC,GACpEnB,YAAY,CAACyK,eAAe,GAC5BzK,YAAY,CAACwF,SAAS;MAE5B,KAAK,WAAW;QACd,OAAO,IAAI,CAACxE,gBAAgB,CAAC+H,gBAAgB,CAAC,IAAI,CAAC7H,UAAU,CAACC,SAAS,CAAC,GACpEnB,YAAY,CAACqJ,cAAc,GAC3BrJ,YAAY,CAAC2G,QAAQ;MAE3B,KAAK,SAAS;QACZ,OAAO3G,YAAY,CAAC8F,OAAO;MAE7B,KAAK,UAAU;QACb,OAAO,IAAI,CAAC9E,gBAAgB,CAAC+H,gBAAgB,CAAC,IAAI,CAAC7H,UAAU,CAACC,SAAS,CAAC,GACpEnB,YAAY,CAACqN,cAAc,GAC3BrN,YAAY,CAACiG,QAAQ;MAE3B;QACE,OAAO,IAAI;IACf;EAEF;EAGA;;;;;;EAMAkH,YAAYA,CAAC3G,MAAoB;IAC/B,MAAM;MAAE8G,EAAE;MAAEC,WAAW;MAAE7K,YAAY;MAAEwG,QAAQ;MAAEsE;IAAY,CAAE,GAAG,IAAI,CAACtM,UAAU,CAACc,MAAM;IAExF;IACA,MAAMyL,aAAa,GAAwB;MACzCH,EAAE;MACFC,WAAW;MACX/G,MAAM;MACN9D,YAAY;MACZwG,QAAQ;MACRsE,YAAY;MACZE,QAAQ,EAAE;KACX;IAED,IAAI,CAAChD,IAAI,CAACiD,GAAG,CACX,IAAI,CAACrH,aAAa,CAACsH,MAAM,CAACH,aAAa,CAAC,CAACjC,SAAS,CAAC,MAAK;MACtD,IAAI,CAACqC,gBAAgB,EAAE;MACvB,IAAI,CAACC,kBAAkB,EAAE;MACzB,IAAI,CAACtG,aAAa,CAAC;QAAE,GAAG,IAAI,CAACtG,UAAU;QAAEsF;MAAM,CAAE,CAAC;MAClD,IAAI,CAACuH,gBAAgB,CAAC,SAAS,EAAE,EAAE,EAAE,iBAAiB,CAAC;IACzD,CAAC,CAAC,CACH;IAED,IAAI,CAACC,wCAAwC,CAACxH,MAAM,CAAC;EACvD;EAEAyH,qBAAqBA,CAACC,SAAiB;IACrC,IAAI,CAACxD,IAAI,CAACiD,GAAG,CAAC,IAAI,CAAC3M,gBAAgB,CAACmN,qBAAqB,CAAC,IAAI,CAACjN,UAAU,CAACC,SAAS,CAACmM,EAAE,EAAEY,SAAS,CAAC,CAAC1C,SAAS,CAAE4C,QAAQ,IAAI;MACxHnD,OAAO,CAACC,GAAG,CAACkD,QAAQ,CAAC;IACvB,CAAC,CAAC,CAAC;EACL;EAEAlB,oBAAoBA,CAAA;IAClB,MAAM;MAAE/L,SAAS;MAAEa;IAAM,CAAE,GAAG,IAAI,CAACd,UAAU;IAC7C,MAAMmN,YAAY,GAAG,IAAI,CAAChE,cAAc,CAACiE,qBAAqB,CAACnN,SAAS,CAACoN,QAAQ,EAAEvM,MAAM,CAACwM,SAAS,CAAC,IAAI,IAAI;IAC5G,MAAMC,eAAe,GAAGzM,MAAM,CAACU,YAAY;IAE3C,IAAI2L,YAAY,EAAE;MAChB,MAAMK,8BAA8B,GAAG,IAAI,CAACC,4CAA4C,CAACN,YAAY,CAACO,IAAI,CAAClI,WAAW,EAAE,EAAE+H,eAAe,CAAC;MAE1I,IAAIC,8BAA8B,EAAE;QAClC,IAAI,CAACG,gCAAgC,EAAE;MACzC,CAAC,MAAM,IAAI,CAACH,8BAA8B,IAAI,CAAC,IAAI,CAAC3F,gBAAgB,EAAE,EAAE;QACtE,IAAI,CAAC+F,yCAAyC,CAACT,YAAY,CAAC;MAC9D;MAEApD,OAAO,CAACC,GAAG,CAACmD,YAAY,CAAC;IAC3B,CAAC,MAAM;MACL,IAAI,CAACQ,gCAAgC,EAAE;IACzC;EACF;EAEA;;;EAGQE,2BAA2BA,CAAA;IACjC;IACA,IAAI,CAAC5E,oBAAoB,CAAC6E,aAAa,CAAC;MACtCC,cAAc,EAAE,8CAA8C;MAC9DC,cAAc,EAAE,KAAK;MACrBC,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE,eAAe;MACvBC,WAAW,EAAE,qCAAqC;MAClDC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,QAAQ;MACpBC,WAAW,EAAE;KACd,CAAC;IACF;IACA,IAAI,CAACrF,oBAAoB,CAACsF,IAAI,EAAE;EAClC;EAEA;;;EAGAzL,kBAAkBA,CAAA;IAChB;IACA,IAAI,CAACmG,oBAAoB,CAAC6E,aAAa,CAAC;MACtCC,cAAc,EAAE,uCAAuC;MACvDI,WAAW,EAAE,qCAAqC;MAClDH,cAAc,EAAE,KAAK;MACrBC,cAAc,EAAE,IAAI;MACpBG,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,QAAQ;MACpBH,MAAM,EAAE,eAAe;MACvBI,WAAW,EAAE;KACd,CAAC;IACF;IACA,IAAI,CAACrF,oBAAoB,CAACsF,IAAI,EAAE;EAClC;EAEA;;;EAGAC,qBAAqBA,CAAA;IACnB;IACA,IAAI,CAACvF,oBAAoB,CAAC6E,aAAa,CAAC;MACtCC,cAAc,EAAE,mCAAmC;MACnDI,WAAW,EAAE,qCAAqC;MAClDH,cAAc,EAAE,KAAK;MACrBC,cAAc,EAAE,IAAI;MACpBG,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,QAAQ;MACpBH,MAAM,EAAE,eAAe;MACvBI,WAAW,EAAE;KACd,CAAC;IACF;IACA,IAAI,CAACrF,oBAAoB,CAACsF,IAAI,EAAE;EAClC;EAEA;;;;;EAKA7G,wBAAwBA,CAAA;IACtB,OAAO,IAAI,CAACtC,aAAa,CAACC,6BAA6B,CAAC,IAAI,CAACD,aAAa,CAACC,6BAA6B,CAAC,IAAI,CAACrF,UAAU,CAACc,MAAM,CAACwE,MAAM,CAAC,CAAC,CAACmJ,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;EAC/J;EAEA;;;EAGAjL,uBAAuBA,CAAA;IACrB,MAAM2J,YAAY,GAAG,IAAI,CAAChE,cAAc,CAACiE,qBAAqB,CAAC,IAAI,CAACpN,UAAU,CAACC,SAAS,CAACoN,QAAQ,EAAE,IAAI,CAACrN,UAAU,CAACc,MAAM,CAACwM,SAAS,CAAC,IAAI,IAAI;IAC5I,MAAMC,eAAe,GAAG,IAAI,CAACvN,UAAU,CAACc,MAAM,CAACU,YAAY;IAC3D,IAAI2L,YAAY,EAAE;MAChB;MACA,MAAMuB,6BAA6B,GAAG,IAAI,CAACjB,4CAA4C,CAACN,YAAa,CAACO,IAAI,CAAClI,WAAW,EAAE,EAAE+H,eAAe,CAAC;MAC1I,IAAImB,6BAA6B,EAAE;QACjC,IAAI,CAACb,2BAA2B,EAAE;MACpC,CAAC,MAAM,IAAI,CAACa,6BAA6B,IAAI,CAAC,IAAI,CAAC7G,gBAAgB,EAAE,EAAE;QACrE,IAAI,CAAC8G,mDAAmD,CAACxB,YAAY,CAAC;MACxE;MACApD,OAAO,CAACC,GAAG,CAACmD,YAAY,CAAC;IAC3B,CAAC,MAAM;MACL,IAAI,CAACU,2BAA2B,EAAE;IACpC;EACF;EAEA;;;EAGAlK,uBAAuBA,CAAA;IACrB,MAAMiL,MAAM,GAAG;MACbC,QAAQ,EAAE,IAAI,CAAC7O,UAAU,CAACc,MAAM,CAACsL;KAClC;IACD,IAAI,CAAC5C,IAAI,CAACiD,GAAG,CAAC,IAAI,CAAC3M,gBAAgB,CAACgP,mBAAmB,CAAC,IAAI,CAAC9O,UAAU,CAACc,MAAM,CAACsL,EAAE,CAAC,CAAC9B,SAAS,CAAEI,GAAG,IAAI;MACnG,IAAIA,GAAG,EAAE;QACP,IAAI,CAACxB,YAAY,CAAC6F,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAAClG,eAAe,CAACmG,wBAAwB,CAAC,IAAI,CAAC;QACnD,IAAI,CAACnG,eAAe,CAAC6B,iBAAiB,CAAC,IAAI,CAAC;QAC5C,IAAI,CAAC/K,gBAAgB,CAACsP,kCAAkC,CAAC,IAAI,CAAC;QAC9D,IAAI,CAAC9I,aAAa,EAAE;MACtB;IACF,CAAC,CAAC,CAAC;EACL;EAEA;;;EAGAxC,uBAAuBA,CAAA;IACrB,MAAM8K,MAAM,GAAG;MACbC,QAAQ,EAAE,IAAI,CAAC7O,UAAU,CAACc,MAAM,CAACsL;KAClC;IACD,IAAI,CAAC5C,IAAI,CAACiD,GAAG,CAAC,IAAI,CAAC3M,gBAAgB,CAACuP,mBAAmB,CAAC,IAAI,CAACrP,UAAU,CAACc,MAAM,CAACsL,EAAE,CAAC,CAAC9B,SAAS,CAAEI,GAAG,IAAI;MACnG,IAAIA,GAAG,EAAE;QACP,IAAI,CAACxB,YAAY,CAAC6F,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAAClG,eAAe,CAACmG,wBAAwB,CAAC,IAAI,CAAC;QACnD,IAAI,CAACnG,eAAe,CAAC6B,iBAAiB,CAAC,IAAI,CAAC;QAC5C,IAAI,CAAC/K,gBAAgB,CAACsP,kCAAkC,CAAC,IAAI,CAAC;QAC9D,IAAI,CAAC9I,aAAa,EAAE;MACtB;IACF,CAAC,CAAC,CAAC;EACL;EAEA;;;EAGArC,sBAAsBA,CAAA;IACpB,IAAI,CAACgF,oBAAoB,CAAC6E,aAAa,CAAC;MACtCC,cAAc,EAAE;mDAC6B;MAC7CC,cAAc,EAAE,KAAK;MACrBC,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE,eAAe;MACvBC,WAAW,EAAE,qCAAqC;MAClDC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,QAAQ;MACpBC,WAAW,EAAE;KACd,CAAC;IACF,IAAI,CAACrF,oBAAoB,CAACsF,IAAI,EAAE;EAClC;EAEA;;;EAGAe,YAAYA,CAAA;IACV,IAAI,CAAC9F,IAAI,CAACiD,GAAG,CAAC,IAAI,CAACrH,aAAa,CAACmK,MAAM,CAAC,IAAI,CAACvP,UAAU,CAACc,MAAM,CAACsL,EAAE,CAAC,CAAC3B,IAAI,CAAC5L,IAAI,CAAC,CAAC,CAAC,CAAC,CAACyL,SAAS,CAACI,GAAG,IAAG;MAC/F,IAAI,CAACxB,YAAY,CAAC6F,mBAAmB,CAAC;QACpCC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAE;OACT,CAAC;MACF,IAAI,CAAC5I,aAAa,EAAE;MACpB,IAAI,CAAC0C,eAAe,CAAC6B,iBAAiB,CAAC,IAAI,CAAC;MAC5C,IAAI,CAACzF,aAAa,CAACoK,iBAAiB,CAAC,IAAI,CAAC;MAC1C,IAAI,CAAC1P,gBAAgB,CAACsP,kCAAkC,CAAC,IAAI,CAAC;IAChE,CAAC,CAAC,CAAC;EACL;EAEA;;;EAGAjF,0BAA0BA,CAAA;IACxB,MAAMsF,gBAAgB,GAAG,IAAI,CAACzP,UAAU,CAACc,MAAM,CAACsL,EAAE;IAClD,MAAMsD,oBAAoB,GAAG,IAAI,CAAC1P,UAAU,CAACc,MAAM,CAACwE,MAAM;IAC1D,IAAIoK,oBAAoB,CAAClK,WAAW,EAAE,KAAK1G,YAAY,CAACwF,SAAS,CAACkB,WAAW,EAAE,EAAE;MAC/E,IAAI,CAACgE,IAAI,CAACY,IAAI,GAAG,IAAI,CAACtB,sBAAsB,CAAC6G,kBAAkB,CAACF,gBAAgB,CAAC,CAACnF,SAAS,CAAEI,GAAQ,IAAI;QAEvG,IAAI,CAACtC,wBAAwB,GAAGsC,GAAG,CAACkF,MAAM,GAAG,CAAC;QAC9C7F,OAAO,CAACC,GAAG,CAACU,GAAG,CAAC;MAElB,CAAC,CAAC;IACJ;EACF;EAEQT,yBAAyBA,CAAA;IAC/B,IAAI,CAACT,IAAI,CAACiD,GAAG,CAAC,IAAI,CAACxD,oBAAoB,CAAC4G,eAAe,CAACvF,SAAS,CAACI,GAAG,IAAG;MACtE,IAAI,CAACzB,oBAAoB,CAAC6G,IAAI,EAAE;MAChC/F,OAAO,CAACC,GAAG,CAACU,GAAG,CAAC;MAEhB,QAAQA,GAAG,CAACwD,MAAM;QAChB,KAAK,eAAe;QACpB,KAAK,kCAAkC;UACrC,MAAM6B,YAAY,GAAG,IAAI,CAAClI,gBAAgB,EAAE,GAAG,IAAI,CAACxD,YAAY,CAAC8H,cAAc,CAAC3G,WAAW,EAAkB,GAAG,IAAI,CAACnB,YAAY,CAACU,QAAQ,CAACS,WAAW,EAAkB;UACxK,IAAI,CAACyG,YAAY,CAAC8D,YAAY,CAAC;UAC/B,IAAI,CAAC/G,eAAe,CAAC6B,iBAAiB,CAAC,IAAI,CAAC;UAC5C;QAEF,KAAK,0BAA0B;UAC7B,IAAI,CAACmF,yBAAyB,EAAE;UAChC;QAEF,KAAK,eAAe;UAClB,MAAMC,kBAAkB,GAAG;YACzBpB,QAAQ,EAAE,IAAI,CAAC7O,UAAU,CAACc,MAAM,CAACsL,EAAE;YACnC8D,MAAM,EAAE;WACT;UACD,IAAI,CAAC1G,IAAI,CAACiD,GAAG,CAAC,IAAI,CAACrH,aAAa,CAAC+K,gBAAgB,CAACF,kBAAkB,CAACpB,QAAQ,EAAEoB,kBAAkB,CAACC,MAAM,CAAC,CAAC5F,SAAS,CAAEI,GAAG,IAAI;YAC1H,IAAIA,GAAG,EAAE;cACP,IAAI,CAACxB,YAAY,CAAC6F,mBAAmB,CAAC;gBACpCC,QAAQ,EAAE,SAAS;gBACnBC,OAAO,EAAE,EAAE;gBACXC,MAAM,EAAE;eACT,CAAC;cACF,IAAI,CAAClG,eAAe,CAACmG,wBAAwB,CAAC,IAAI,CAAC;cACnD,IAAI,CAACnG,eAAe,CAAC6B,iBAAiB,CAAC,IAAI,CAAC;cAC5C,IAAI,CAAC/K,gBAAgB,CAACsP,kCAAkC,CAAC,IAAI,CAAC;cAC9D,IAAI,CAACpP,UAAU,CAACc,MAAM,CAAC4B,UAAU,GAAG,IAAI;cACxC,IAAI,CAAC4D,aAAa,EAAE;YACtB;UACF,CAAC,CAAC,CAAC;UACH;QAEF,KAAK,eAAe;UAClB,IAAI,CAACgJ,YAAY,EAAE;UACnB;MACJ;MAEA,IAAI,CAACxP,gBAAgB,CAACsP,kCAAkC,CAAC,IAAI,CAAC;IAChE,CAAC,CAAC,CAAC;EACL;EAEA;;;;EAIQlF,wBAAwBA,CAAA;IAC9B,IAAI,CAACV,IAAI,CAACiD,GAAG,CAAC,IAAI,CAACxD,oBAAoB,CAACmH,eAAe,CAAC9F,SAAS,CAACI,GAAG,IAAG;MACtE,IAAI,CAACzB,oBAAoB,CAAC6G,IAAI,EAAE;MAChC/F,OAAO,CAACC,GAAG,CAACU,GAAG,CAAC;MAEhB,QAAQA,GAAG,CAACwD,MAAM;QAChB,KAAK,eAAe;UAClB,IAAI,CAACmC,kBAAkB,CAAC,KAAK,CAAC;UAC9B;QACF,KAAK,0BAA0B;UAC7B,IAAI,CAACC,qBAAqB,CAAC,KAAK,CAAC;UACjC;MACJ;IACF,CAAC,CAAC,CAAC;EACL;EAEQD,kBAAkBA,CAACH,MAAe;IACxC,MAAMD,kBAAkB,GAAG;MACzBpB,QAAQ,EAAE,IAAI,CAAC7O,UAAU,CAACc,MAAM,CAACsL,EAAE;MACnC8D,MAAM,EAAEA;KACT;IAED,IAAI,CAAC1G,IAAI,CAACiD,GAAG,CAAC,IAAI,CAACrH,aAAa,CAAC+K,gBAAgB,CAACF,kBAAkB,CAACpB,QAAQ,EAAEoB,kBAAkB,CAACC,MAAM,CAAC,CAAC5F,SAAS,CAAEI,GAAG,IAAI;MAC1H,IAAIA,GAAG,EAAE;QACP,IAAI,CAACxB,YAAY,CAAC6F,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAEgB,MAAM,GAAG,sBAAsB,GAAG;SAC3C,CAAC;QAEF,IAAI,CAAClH,eAAe,CAACmG,wBAAwB,CAAC,IAAI,CAAC;QACnD,IAAI,CAACnG,eAAe,CAAC6B,iBAAiB,CAAC,IAAI,CAAC;QAC5C,IAAI,CAACvE,aAAa,EAAE;MACtB;IACF,CAAC,CAAC,CAAC;EACL;EAEA;;;;;EAKQgK,qBAAqBA,CAACJ,MAAe;IAC3C,MAAMD,kBAAkB,GAAG;MACzBpB,QAAQ,EAAE,IAAI,CAAC7O,UAAU,CAACc,MAAM,CAACsL,EAAE;MACnC8D,MAAM,EAAEA;KACT;IACD,MAAMH,YAAY,GAAG,IAAI,CAAClI,gBAAgB,EAAE,GAAG,IAAI,CAACxD,YAAY,CAAC8H,cAAc,CAAC3G,WAAW,EAAkB,GAAG,IAAI,CAACnB,YAAY,CAACU,QAAQ,CAACS,WAAW,EAAkB;IACxK,IAAI1E,MAAM,GAAwB;MAChCsL,EAAE,EAAE,IAAI,CAACpM,UAAU,CAACc,MAAM,CAACsL,EAAE;MAC7BC,WAAW,EAAE,IAAI,CAACrM,UAAU,CAACc,MAAM,CAACuL,WAAW;MAC/C/G,MAAM,EAAEyK,YAAY;MACpBvO,YAAY,EAAE,IAAI,CAACxB,UAAU,CAACc,MAAM,CAACU,YAAY;MACjDwG,QAAQ,EAAE,IAAI,CAAChI,UAAU,CAACc,MAAM,CAACkH,QAAQ;MACzCsE,YAAY,EAAE,IAAI,CAACtM,UAAU,CAACc,MAAM,CAACwL,YAAY;MACjDE,QAAQ,EAAE;KACX;IAED,IAAI,CAAChD,IAAI,CAACiD,GAAG,CAAC,IAAI,CAACrH,aAAa,CAACsH,MAAM,CAAC5L,MAAM,CAAC,CAAC2J,IAAI,CAClD7L,SAAS,CAAE8L,GAAQ,IAAI;MACrB,IAAIA,GAAG,EAAE;QACPX,OAAO,CAACC,GAAG,CAACU,GAAG,CAAC;QAChB,MAAM6F,OAAO,GAAG,IAAI,CAACvQ,UAAU,CAACC,SAAS,CAACC,iBAAiB,CAACxB,GAAG,CAAE8R,EAAQ,IAAI;UAAG,OAAOA,EAAE,CAACC,SAAS;QAAC,CAAC,CAAC;QACtG1G,OAAO,CAACC,GAAG,CAACuG,OAAO,CAAC;QACpB,IAAI,CAACnI,wBAAwB,GAAGsC,GAAG,CAACkF,MAAM,GAAG,CAAC;QAC9C,OAAOxQ,EAAE,CAAC,IAAI,CAAC;MACjB;MACA,OAAOA,EAAE,CAACsL,GAAG,CAAC;IAChB,CAAC,CAAC,CACH,CAACJ,SAAS,CAAEI,GAAQ,IAAI;MACvB,IAAIA,GAAG,EAAE;QACP,IAAI,CAACxB,YAAY,CAAC6F,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAAClG,eAAe,CAACmG,wBAAwB,CAAC,IAAI,CAAC;QACnD,IAAI,CAACnG,eAAe,CAAC6B,iBAAiB,CAAC,IAAI,CAAC;QAC5C,IAAI,CAAC/K,gBAAgB,CAACsP,kCAAkC,CAAC,IAAI,CAAC;QAC9D,IAAI,CAACpP,UAAU,CAACc,MAAM,CAAC4B,UAAU,GAAG,IAAI;QACxC,IAAI,CAAC4D,aAAa,EAAE;MACtB;IACF,CAAC,CAAC,CAAC;EACL;EAEA;;;EAGQ0J,yBAAyBA,CAAA;IAC/B,MAAMC,kBAAkB,GAAG;MACzBpB,QAAQ,EAAE,IAAI,CAAC7O,UAAU,CAACc,MAAM,CAACsL,EAAE;MACnC8D,MAAM,EAAE;KACT;IACD,MAAMH,YAAY,GAAG,IAAI,CAAClI,gBAAgB,EAAE,GAAG,IAAI,CAACxD,YAAY,CAAC8H,cAAc,CAAC3G,WAAW,EAAkB,GAAG,IAAI,CAACnB,YAAY,CAACU,QAAQ,CAACS,WAAW,EAAkB;IACxK,IAAI1E,MAAM,GAAwB;MAChCsL,EAAE,EAAE,IAAI,CAACpM,UAAU,CAACc,MAAM,CAACsL,EAAE;MAC7BC,WAAW,EAAE,IAAI,CAACrM,UAAU,CAACc,MAAM,CAACuL,WAAW;MAC/C/G,MAAM,EAAEyK,YAAY;MACpBvO,YAAY,EAAE,IAAI,CAACxB,UAAU,CAACc,MAAM,CAACU,YAAY;MACjDwG,QAAQ,EAAE,IAAI,CAAChI,UAAU,CAACc,MAAM,CAACkH,QAAQ;MACzCsE,YAAY,EAAE,IAAI,CAACtM,UAAU,CAACc,MAAM,CAACwL,YAAY;MACjDE,QAAQ,EAAE;KACX;IAED,IAAI,CAAChD,IAAI,CAACiD,GAAG,CAAC,IAAI,CAACrH,aAAa,CAACsH,MAAM,CAAC5L,MAAM,CAAC,CAAC2J,IAAI,CAClD7L,SAAS,CAAE8L,GAAQ,IAAI;MACrB,IAAIA,GAAG,EAAE;QACPX,OAAO,CAACC,GAAG,CAACU,GAAG,CAAC;QAChB,MAAM6F,OAAO,GAAG,IAAI,CAACvQ,UAAU,CAACC,SAAS,CAACC,iBAAiB,CAACxB,GAAG,CAAE8R,EAAQ,IAAI;UAAG,OAAOA,EAAE,CAACC,SAAS;QAAC,CAAC,CAAC;QACtG1G,OAAO,CAACC,GAAG,CAACuG,OAAO,CAAC;QACpB,IAAI,CAACnI,wBAAwB,GAAGsC,GAAG,CAACkF,MAAM,GAAG,CAAC;QAC9C,OAAO,IAAI,CAACxK,aAAa,CAAC+K,gBAAgB,CAACF,kBAAkB,CAACpB,QAAQ,EAAEoB,kBAAkB,CAACC,MAAM,CAAC;MACpG;MACA,OAAO9Q,EAAE,CAACsL,GAAG,CAAC;IAChB,CAAC,CAAC,CACH,CAACJ,SAAS,CAAEI,GAAQ,IAAI;MACvB,IAAIA,GAAG,EAAE;QACP,IAAI,CAACxB,YAAY,CAAC6F,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAAClG,eAAe,CAACmG,wBAAwB,CAAC,IAAI,CAAC;QACnD,IAAI,CAACnG,eAAe,CAAC6B,iBAAiB,CAAC,IAAI,CAAC;QAC5C,IAAI,CAAC/K,gBAAgB,CAACsP,kCAAkC,CAAC,IAAI,CAAC;QAC9D,IAAI,CAACpP,UAAU,CAACc,MAAM,CAAC4B,UAAU,GAAG,IAAI;QACxC,IAAI,CAAC4D,aAAa,EAAE;MACtB;IACF,CAAC,CAAC,CAAC;EACL;EAEA;;;;EAIQuB,gBAAgBA,CAAA;IACtB,OAAO,IAAI,CAAC/H,gBAAgB,CAAC+H,gBAAgB,CAAC,IAAI,CAAC7H,UAAU,CAACC,SAAS,CAAC;EAC1E;EAEQyQ,wCAAwCA,CAAA;IAC9C,IAAIC,aAAa,GAAa,EAAE;IAChC,MAAMlB,gBAAgB,GAAG,IAAI,CAACzP,UAAU,CAACc,MAAM,CAACsL,EAAE;IAEhD,IAAI,CAACtD,sBAAsB,CAAC6G,kBAAkB,CAACF,gBAAgB,CAAC,CAAChF,IAAI,CACnE7L,SAAS,CAAE8L,GAAsB,IAAI;MACnC,IAAI,CAACA,GAAG,EAAE;QACR,OAAOtL,EAAE,CAAC,IAAI,CAAC;MACjB;MACA2K,OAAO,CAACC,GAAG,CAACU,GAAG,CAAC;MAChB;MACA;MACA;MACA,MAAMkG,WAAW,GAAG,IAAI,CAAC5Q,UAAU,CAACC,SAAS,CAACC,iBAAiB,CAACxB,GAAG,CAAEiD,IAAU,IAC7E,IAAI,CAACmH,sBAAsB,CAAC+H,oBAAoB,CAAClP,IAAI,CAAC8O,SAAU,EAAEhB,gBAAgB,CAAC,CACpF;MAED,OAAOtQ,QAAQ,CAACyR,WAAW,CAAC,CAACnG,IAAI,CAC/B/L,GAAG,CAACoS,OAAO,KAAK;QAAEA,OAAO;QAAE1E,EAAE,EAAE1B,GAAG,CAAC,CAAC,CAAC,CAAC0B;MAAE,CAAE,CAAC,CAAC,CAC7C;IACH,CAAC,CAAC,EACFxN,SAAS,CAAE8L,GAAQ,IAAI;MACrBX,OAAO,CAACC,GAAG,CAACU,GAAG,CAACoG,OAAO,CAAC;MACxBpG,GAAG,CAACoG,OAAO,CAACC,OAAO,CAAEC,MAAW,IAAI;QAClCjH,OAAO,CAACC,GAAG,CAACgH,MAAM,CAAC;MACrB,CAAC,CAAC;MACF,MAAML,aAAa,GAAU,EAAE;MAE/B,KAAK,IAAIK,MAAM,IAAItG,GAAG,CAACoG,OAAO,EAAE;QAC9BH,aAAa,CAACM,IAAI,CAACD,MAAM,CAAC,CAAC,CAAC,CAAC5E,EAAE,CAAC;MAClC;MAEArC,OAAO,CAACC,GAAG,CAACU,GAAG,CAACoG,OAAO,CAAC;MACxB/G,OAAO,CAACC,GAAG,CAAC2G,aAAa,CAAC;MAE1B,MAAMC,WAAW,GAAGlG,GAAG,CAACoG,OAAO,CAAC,CAAC,CAAC,CAACpS,GAAG,CAAEiD,IAAU,IAChD,IAAI,CAACmH,sBAAsB,CAACoI,wBAAwB,CAACP,aAAa,EAAEjG,GAAG,CAAC0B,EAAE,CAAC,CAC5E;MAED,OAAOjN,QAAQ,CAACyR,WAAW,CAAC;IAC9B,CAAC,CAAC,CACH,CAACtG,SAAS,CAAC,MAAK;MACf,IAAI,CAAClG,kBAAkB,CAAC,IAAI,CAACC,YAAY,CAACI,SAAS,CAAC;MACpD;IACF,CAAC,CAAC;IAEJ;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IAEA;EACF;EAEA;;;EAGQkJ,gCAAgCA,CAAA;IACtC,IAAI,CAAC1E,oBAAoB,CAAC6E,aAAa,CAAC;MACtCC,cAAc,EAAE,8CAA8C;MAC9DC,cAAc,EAAE,KAAK;MACrBC,cAAc,EAAE,IAAI;MACpBG,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,QAAQ;MACpBH,MAAM,EAAE,eAAe;MACvBiD,cAAc,EAAE,GAAG;MACnBC,cAAc,EAAE,sBAAsB;MACtC9C,WAAW,EAAE;KACd,CAAC;IACF,IAAI,CAACrF,oBAAoB,CAACsF,IAAI,EAAE;EAClC;EAEA;;;;;EAKQX,yCAAyCA,CAACT,YAA4B;IAC5E,IAAI,CAAClE,oBAAoB,CAAC6E,aAAa,CAAC;MACtCC,cAAc,EAAEZ,YAAa,CAACO,IAAI,GAAG,WAAW,GAAG,IAAI,CAAChE,oBAAoB,CAACyD,YAAa,CAACO,IAAI,CAAC,GAAG,oCAAoC;MACvIM,cAAc,EAAE,iBAAiB;MACjCC,cAAc,EAAE,eAAe;MAC/BG,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,QAAQ;MACpBH,MAAM,EAAE,0BAA0B;MAClCI,WAAW,EAAE,wBAAwB;MACrC6C,cAAc,EAAE,mBAAmB;MACnCC,cAAc,EAAE;KACjB,CAAC;IACF,IAAI,CAACnI,oBAAoB,CAACsF,IAAI,EAAE;EAClC;EAEA;;;;;EAKQI,mDAAmDA,CAACxB,YAA4B;IACtF,IAAI,CAAClE,oBAAoB,CAAC6E,aAAa,CAAC;MACtCC,cAAc,EAAEZ,YAAa,CAACO,IAAI,GAAG,WAAW,GAAG,IAAI,CAAChE,oBAAoB,CAACyD,YAAa,CAACO,IAAI,CAAC,GAAG;6EAC5B;MACvEM,cAAc,EAAE,KAAK;MACrBC,cAAc,EAAE,IAAI;MACpBG,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE,QAAQ;MACpBH,MAAM,EAAE,kCAAkC;MAC1CI,WAAW,EAAE,wBAAwB;MACrC6C,cAAc,EAAE,mBAAmB;MACnCC,cAAc,EAAE;KACjB,CAAC;IACF,IAAI,CAACnI,oBAAoB,CAACsF,IAAI,EAAE;EAClC;EAEA;;;;;;;EAOQd,4CAA4CA,CAAC4D,KAAa,EAAE9D,eAAuB;IACzF,IAAImB,6BAA6B,GAAG,IAAI;IACxC,QAAQ2C,KAAK,CAAC7L,WAAW,EAAE;MACzB,KAAKlG,WAAW,CAACgS,IAAI,CAAC9L,WAAW,EAAE;QACjCkJ,6BAA6B,GAAG,IAAI,CAACrN,cAAc,CAACkQ,iBAAiB,CAAChE,eAAe,EAAE,IAAI,CAAC7D,oBAAoB,CAACpK,WAAW,CAACgS,IAAI,CAAC,CAAC;QACnI;MACF,KAAKhS,WAAW,CAACkS,OAAO,CAAChM,WAAW,EAAE;QACpCkJ,6BAA6B,GAAG,IAAI,CAACrN,cAAc,CAACkQ,iBAAiB,CAAChE,eAAe,EAAE,IAAI,CAAC7D,oBAAoB,CAACpK,WAAW,CAACkS,OAAO,CAAC,CAAC;QACtI;MACF,KAAKlS,WAAW,CAACmS,OAAO,CAACjM,WAAW,EAAE;QACpCkJ,6BAA6B,GAAG,IAAI,CAACrN,cAAc,CAACkQ,iBAAiB,CAAChE,eAAe,EAAE,IAAI,CAAC7D,oBAAoB,CAACpK,WAAW,CAACmS,OAAO,CAAC,CAAC;QACtI;IACJ;IAEA,OAAO/C,6BAA6B;EACtC;EAGQ/B,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAAC+E,YAAY,IAAI,CAAC,IAAI,CAACrQ,cAAc,CAACyI,eAAe,CAAC,IAAI,CAAC4H,YAAY,CAAC,EAAE;MAChF,IAAI,CAACA,YAAY,CAAC5B,IAAI,EAAE;IAC1B;EACF;EAEQlD,kBAAkBA,CAAA;IACxB,IAAI,CAAC5D,eAAe,CAAC6B,iBAAiB,CAAC,IAAI,CAAC;IAC5C,IAAI,CAAC/K,gBAAgB,CAACsP,kCAAkC,CAAC,IAAI,CAAC;EAChE;EAEQvC,gBAAgBA,CAACmC,QAAgB,EAAEC,OAAe,EAAEC,MAAc;IACxE,IAAI,CAAChG,YAAY,CAAC6F,mBAAmB,CAAC;MAAEC,QAAQ;MAAEC,OAAO;MAAEC;IAAM,CAAE,CAAC;EACtE;EAEQpC,wCAAwCA,CAACxH,MAAoB;IACnE,QAAQA,MAAM;MACZ,KAAKxG,YAAY,CAACyK,eAAe;QAC/B,IAAI,CAACwD,qBAAqB,CAAC7N,MAAM,CAACqK,eAAe,CAAC;QAClD;MACF,KAAKzK,YAAY,CAACiG,QAAQ;QACxB;QACA;IACJ;EACF;EAAC,QAAA4M,CAAA,G;qBApwBUnJ,iCAAiC,EAAAjJ,EAAA,CAAAqS,iBAAA,CAAAC,EAAA,CAAAC,gBAAA,GAAAvS,EAAA,CAAAqS,iBAAA,CAAAC,EAAA,CAAAE,mBAAA,GAAAxS,EAAA,CAAAqS,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAA1S,EAAA,CAAAqS,iBAAA,CAAAM,EAAA,CAAAC,MAAA,GAAA5S,EAAA,CAAAqS,iBAAA,CAAAQ,EAAA,CAAAC,sBAAA,GAAA9S,EAAA,CAAAqS,iBAAA,CAAAU,EAAA,CAAAC,aAAA,GAAAhT,EAAA,CAAAqS,iBAAA,CAAAY,EAAA,CAAAC,aAAA,GAAAlT,EAAA,CAAAqS,iBAAA,CAAAc,EAAA,CAAAC,gBAAA,GAAApT,EAAA,CAAAqS,iBAAA,CAAAgB,EAAA,CAAAC,eAAA,GAAAtT,EAAA,CAAAqS,iBAAA,CAAAkB,EAAA,CAAAC,cAAA,GAAAxT,EAAA,CAAAqS,iBAAA,CAAAoB,GAAA,CAAAC,oBAAA,GAAA1T,EAAA,CAAAqS,iBAAA,CAAAsB,GAAA,CAAAC,YAAA,GAAA5T,EAAA,CAAAqS,iBAAA,CAAAwB,GAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAjC9K,iCAAiC;IAAA+K,SAAA;IAAAC,SAAA,WAAAC,wCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;QC9B9CnU,EAAA,CAAAa,UAAA,IAAAwT,yDAAA,4BAAsC;;;QAAvBrU,EAAA,CAAAe,UAAA,SAAAqT,GAAA,CAAA3T,UAAA,CAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}