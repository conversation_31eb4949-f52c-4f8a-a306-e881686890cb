{"ast": null, "code": "import { UntypedFormArray, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\nimport { Observable } from 'rxjs';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"src/app/core/services/general.service\";\nimport * as i4 from \"src/app/core/services/teacher-application.service\";\nimport * as i5 from \"src/app/core/services/auth.service\";\nimport * as i6 from \"src/app/core/services/toast.service\";\nimport * as i7 from \"primeng/api\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"primeng/dropdown\";\nimport * as i10 from \"primeng/calendar\";\nimport * as i11 from \"primeng/button\";\nimport * as i12 from \"primeng/ripple\";\nconst _c0 = [\"opportunityForm\"];\nfunction GeneralInfoComponent_ng_container_2_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \"* First Name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \"* Last Name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \"* Preffered name is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelement(1, \"img\", 37);\n    i0.ɵɵelementStart(2, \"div\", 38);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const country_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", country_r3.image, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(country_r3.name);\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \"* Country of Origin is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_ng_template_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36);\n    i0.ɵɵelement(1, \"img\", 37);\n    i0.ɵɵelementStart(2, \"div\", 38);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const country_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"src\", country_r4.image, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(country_r4.name);\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \"* Country of Residency is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_ng_template_36_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \"* Timezone is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_ng_template_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 38);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, GeneralInfoComponent_ng_container_2_ng_template_36_div_3_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const timezone_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(timezone_r5.text);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"timezone\"));\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \"* Timezone is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \"* Date of Birth is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_47_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 38);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const language_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(language_r7.name);\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_47_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\")(1, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function GeneralInfoComponent_ng_container_2_div_47_div_6_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.addnativeLanguage());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_47_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function GeneralInfoComponent_ng_container_2_div_47_ng_container_7_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const i_r10 = i0.ɵɵnextContext().index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeNativeLanguage(i_r10));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40);\n    i0.ɵɵelementContainerStart(2, 41);\n    i0.ɵɵelementStart(3, \"div\", 42)(4, \"p-dropdown\", 43);\n    i0.ɵɵlistener(\"onChange\", function GeneralInfoComponent_ng_container_2_div_47_Template_p_dropdown_onChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onOriginChange($event));\n    });\n    i0.ɵɵtemplate(5, GeneralInfoComponent_ng_container_2_div_47_ng_template_5_Template, 3, 1, \"ng-template\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, GeneralInfoComponent_ng_container_2_div_47_div_6_Template, 2, 0, \"div\", 2)(7, GeneralInfoComponent_ng_container_2_div_47_ng_container_7_Template, 2, 0, \"ng-container\", 2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r10 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"formGroupName\", i_r10);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"options\", ctx_r1.languages)(\"filter\", !ctx_r1.isTablet);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i_r10 === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r10 > 0);\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵtext(1, \"* Please select your Native languages \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \"* Email is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1, \"* Phone is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction GeneralInfoComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"form\", 8)(2, \"div\", 9)(3, \"div\", 10)(4, \"div\", 11)(5, \"div\", 12);\n    i0.ɵɵtext(6, \"First Name *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(7, \"input\", 13);\n    i0.ɵɵtemplate(8, GeneralInfoComponent_ng_container_2_div_8_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 11)(10, \"div\", 12);\n    i0.ɵɵtext(11, \"Last Name *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 15);\n    i0.ɵɵtemplate(13, GeneralInfoComponent_ng_container_2_div_13_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 16)(15, \"div\", 11)(16, \"div\", 12);\n    i0.ɵɵtext(17, \"Preffered Name *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"input\", 17);\n    i0.ɵɵtemplate(19, GeneralInfoComponent_ng_container_2_div_19_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 11)(21, \"div\", 12);\n    i0.ɵɵtext(22, \"Country of Origin *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"p-dropdown\", 18);\n    i0.ɵɵlistener(\"onChange\", function GeneralInfoComponent_ng_container_2_Template_p_dropdown_onChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCountryChange($event));\n    });\n    i0.ɵɵtemplate(24, GeneralInfoComponent_ng_container_2_ng_template_24_Template, 4, 2, \"ng-template\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, GeneralInfoComponent_ng_container_2_div_25_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 11)(27, \"div\", 12);\n    i0.ɵɵtext(28, \"Country of Residency *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"p-dropdown\", 20);\n    i0.ɵɵlistener(\"onChange\", function GeneralInfoComponent_ng_container_2_Template_p_dropdown_onChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCountryChange($event));\n    });\n    i0.ɵɵtemplate(30, GeneralInfoComponent_ng_container_2_ng_template_30_Template, 4, 2, \"ng-template\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, GeneralInfoComponent_ng_container_2_div_31_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 11)(33, \"div\", 12);\n    i0.ɵɵtext(34, \"Timezone *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"p-dropdown\", 21);\n    i0.ɵɵlistener(\"onChange\", function GeneralInfoComponent_ng_container_2_Template_p_dropdown_onChange_35_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onTimeZoneChange($event));\n    });\n    i0.ɵɵtemplate(36, GeneralInfoComponent_ng_container_2_ng_template_36_Template, 4, 2, \"ng-template\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(37, GeneralInfoComponent_ng_container_2_div_37_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"div\", 11)(39, \"div\", 12);\n    i0.ɵɵtext(40, \"Date of Birth *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"p-calendar\", 22);\n    i0.ɵɵlistener(\"onSelect\", function GeneralInfoComponent_ng_container_2_Template_p_calendar_onSelect_41_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onBirthDateSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(42, GeneralInfoComponent_ng_container_2_div_42_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"div\", 11)(44, \"div\", 12);\n    i0.ɵɵtext(45, \"Native Speaker in * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerStart(46, 23);\n    i0.ɵɵtemplate(47, GeneralInfoComponent_ng_container_2_div_47_Template, 8, 5, \"div\", 24)(48, GeneralInfoComponent_ng_container_2_div_48_Template, 2, 0, \"div\", 25);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"div\", 16)(50, \"div\", 26)(51, \"strong\");\n    i0.ɵɵtext(52, \"Enter your contact info\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(53, \"div\", 11)(54, \"div\", 12);\n    i0.ɵɵtext(55, \"E-mail *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(56, \"input\", 27);\n    i0.ɵɵtemplate(57, GeneralInfoComponent_ng_container_2_div_57_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 11)(59, \"div\", 12);\n    i0.ɵɵtext(60, \"Phone *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"div\", 28)(62, \"div\", 29)(63, \"div\", 30)(64, \"app-prime-input-dropdown\", 31);\n    i0.ɵɵlistener(\"valueSelected\", function GeneralInfoComponent_ng_container_2_Template_app_prime_input_dropdown_valueSelected_64_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPhoneCodeChange($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(65, \"div\", 32);\n    i0.ɵɵelement(66, \"input\", 33);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(67, GeneralInfoComponent_ng_container_2_div_67_Template, 2, 0, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"div\", 11)(69, \"div\", 12);\n    i0.ɵɵtext(70, \"Skype *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(71, \"input\", 34);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.form);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"fname\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"lname\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"preffered\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"options\", ctx_r1.countries)(\"filter\", !ctx_r1.isTablet);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"countryOrigin\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"options\", ctx_r1.countries)(\"filter\", !ctx_r1.isTablet);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"countryResidency\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"options\", ctx_r1.timezones)(\"filter\", !ctx_r1.isTablet);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"zone\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"maxDate\", ctx_r1.maxBirthdateTime)(\"defaultDate\", ctx_r1.maxBirthdateTime)(\"keepInvalid\", true)(\"monthNavigator\", true)(\"yearNavigator\", false)(\"yearRange\", \"2019:2021\")(\"firstDayOfWeek\", 1)(\"readonlyInput\", true);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"birth\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.nativeLanguages.controls);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"nativeLanguages\"));\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"email\"));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"parentForm\", ctx_r1.form)(\"countries\", ctx_r1.phoneCodes)(\"filter\", true)(\"inputName\", \"phoneCode\")(\"showTextAfterImage\", false)(\"withFlags\", true)(\"selectedItemValue\", ctx_r1.selectedPhoneCode);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.ifFieldValid(\"phone\"));\n  }\n}\nexport class GeneralInfoComponent {\n  constructor(router, fb, generalService, teacherService, authService, toast, confirmationService) {\n    this.router = router;\n    this.fb = fb;\n    this.generalService = generalService;\n    this.teacherService = teacherService;\n    this.authService = authService;\n    this.toast = toast;\n    this.confirmationService = confirmationService;\n    this.ngForm = {};\n    this.subs = new SubSink();\n    this.form = new UntypedFormGroup({});\n    this.isTablet = false;\n    this.timezones = this.generalService.getTimezones();\n    this.countries = this.generalService.getCountries();\n    this.phoneCodes = this.generalService.getPhoneCodes();\n    this.languages = this.generalService.languages;\n    this.tryToSave = false;\n    this.isLoading = true;\n    this.selectedCountry = \"\";\n    this.maxBirthdateTime = new Date();\n    this.formattedBirthDate = \"\";\n    this.nativeLanguagesModel = [];\n    this.formChanged = false;\n    this.selectedPhoneCode = {};\n  }\n  get nativeLanguages() {\n    return this.form.get('nativeLanguages');\n  }\n  ngOnInit() {\n    this.isLoading = true;\n    this.subs.add(this.teacherService.submitOnMenuClickListener.subscribe(res => {\n      if (res) {\n        this.onSubmit();\n      }\n    }));\n    console.log(this.maxBirthdateTime);\n    const date = new Date();\n    const newDate = this.maxBirthdateTime.setFullYear(this.maxBirthdateTime.getFullYear() - 18);\n    this.maxBirthdateTime = new Date(newDate);\n    this.teacherService.setCurrentStepIndex(0);\n    this.teacher = this.teacherService.dummyTeacher;\n    this.subs.add(this.generalService.deviceKind.subscribe(res => {\n      this.isTablet = res.is1024;\n    }));\n    this.subs.add(this.teacherService.getTeacherApplicationStepsStatus().subscribe(stepsStatuses => {\n      if (!stepsStatuses.step1) {\n        this.prepareSelectedPhoneCodeFromIP();\n      }\n    }));\n    this.subs.add(this.teacherService.getTeacherApplicationStep1(this.authService.getUserId()).subscribe(res => {\n      console.log(res);\n      this.updateStep1TeacherInfoFormValues(res);\n      this.initFormChangedListener();\n      this.isLoading = false;\n    }));\n    // updating title for mobile header text\n    // this.generalService.updateMobileMenuTitle(this.teacherService.getStepsTitle(0));\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n    this.teacherService.setSubmitOnMenuClickListener(false);\n  }\n  onSubmit() {\n    this.tryToSave = true;\n    let invalidInputs = '';\n    for (const controlName in this.form.controls) {\n      if (this.form.controls.hasOwnProperty(controlName)) {\n        const control = this.form.controls[controlName];\n        if (control.invalid) {\n          invalidInputs += `${controlName}, `;\n        }\n      }\n    }\n    let detailMessage = 'Please enter all required fields to continue.';\n    if (invalidInputs !== '') {\n      invalidInputs = invalidInputs.slice(0, -2); // Remove the trailing comma and space\n      if (invalidInputs !== '') {\n        detailMessage += `Invalid input for the following fields: ${invalidInputs}`;\n      }\n    }\n    if (!this.form.valid) {\n      this.toast.setShowToastmessage({\n        severity: 'warn',\n        summary: '',\n        detail: detailMessage\n      });\n      return;\n    }\n    this.formChanged = false;\n    const teacherApplicationStep1FormData = {\n      firstName: this.form.value.firstName,\n      lastName: this.form.value.lastName,\n      prefferedName: this.form.value.prefferedName,\n      countryOrigin: {\n        name: this.form.value.countryOrigin.name,\n        flagUrl: this.form.value.countryOrigin.image\n      },\n      countryResidency: {\n        name: this.form.value.countryResidency.name,\n        flagUrl: this.form.value.countryResidency.image\n      },\n      nativeLanguages: this.form.value.nativeLanguages,\n      timeZone: this.form.value.timeZone?.utc[0],\n      birth: this.generalService.convertDateStringToIsoFormatWithZeroTime(this.formattedBirthDate),\n      email: this.form.value.email,\n      phone: this.form.value.phone,\n      phoneCode: this.form.value.phoneCode.code,\n      skype: this.form.value.skype\n    };\n    this.subs.add(this.teacherService.updateAPITeacherApplicationStep1(teacherApplicationStep1FormData).subscribe(res => {\n      if (res) {\n        this.toast.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Your General details were saved.'\n        });\n        // this.toastr.success(\"Your general info were updated.\")\n        this.router.navigateByUrl('/teacher/education', {\n          replaceUrl: true\n        });\n      }\n    }));\n  }\n  updateStep1TeacherInfoFormValues(teacherStep1Object) {\n    if (this.isValidDate(teacherStep1Object.birth)) {\n      this.formattedBirthDate = this.generalService.formatDateToDMY(teacherStep1Object.birth);\n    } else {\n      this.formattedBirthDate = this.generalService.formatDateToDMY(this.maxBirthdateTime.toString());\n    }\n    if (!this.generalService.isNullishObject(teacherStep1Object.phoneCode)) {\n      this.selectedPhoneCode = this.phoneCodes.filter(el => el.code === teacherStep1Object.phoneCode)[0];\n    }\n    this.form = new UntypedFormGroup({\n      birth: new UntypedFormControl(this.formattedBirthDate, {\n        validators: [Validators.required]\n      }),\n      prefferedName: new UntypedFormControl(teacherStep1Object.prefferedName, {\n        validators: [Validators.required]\n      }),\n      firstName: new UntypedFormControl(teacherStep1Object.firstName, {\n        validators: [Validators.required]\n      }),\n      lastName: new UntypedFormControl(teacherStep1Object.lastName, {\n        validators: [Validators.required]\n      }),\n      email: new UntypedFormControl(teacherStep1Object.email, {\n        validators: [Validators.required, Validators.email]\n      }),\n      phoneCode: new UntypedFormControl(this.selectedPhoneCode, {\n        validators: [Validators.required]\n      }),\n      phone: new UntypedFormControl(teacherStep1Object.phone, {\n        validators: [Validators.required]\n      }),\n      skype: new UntypedFormControl(teacherStep1Object.skype || '-', {\n        validators: [Validators.required]\n      }),\n      nativeLanguages: new UntypedFormArray([], {\n        validators: [Validators.required]\n      }),\n      timeZone: new UntypedFormControl(this.timezones.find(el => el.utc.includes(teacherStep1Object.timeZone)), {\n        validators: [Validators.required]\n      }),\n      countryOrigin: new UntypedFormControl(!this.generalService.isNullishObject(teacherStep1Object.countryOrigin) ? this.countries.filter(el => el.name === teacherStep1Object.countryOrigin.name)[0] : null, {\n        validators: [Validators.required]\n      }),\n      countryResidency: new UntypedFormControl(!this.generalService.isNullishObject(teacherStep1Object.countryResidency) ? this.countries.filter(el => el.name === teacherStep1Object.countryResidency.name)[0] : null, {\n        validators: [Validators.required]\n      })\n    });\n    if (!this.generalService.isNullishObject(teacherStep1Object.nativeLanguages)) {\n      teacherStep1Object.nativeLanguages.forEach(element => {\n        this.addnativeLanguage(element.native);\n      });\n    } else {\n      this.nativeLanguagesModel.forEach(element => {\n        this.nativeLanguages.push(this.fb.group(element));\n      });\n      if (this.nativeLanguagesModel.length === 0) {\n        this.addnativeLanguage({});\n      }\n    }\n  }\n  onBirthDateSelected(event) {\n    console.log(event);\n    let d = new Date(Date.parse(event));\n    this.formattedBirthDate = this.generalService.formatDateToDMY(event);\n  }\n  isConfirm(field) {\n    return this.form.get(field)?.invalid && this.form.get(field)?.touched || this.tryToSave && this.form.get(field)?.invalid || this.form.value.password !== this.form.value.confirm && this.form.get(field)?.touched;\n  }\n  onOriginChange(event) {\n    setTimeout(() => {\n      this.selectedLanguage = event.value;\n    }, 100);\n  }\n  onCountryChange(event) {\n    setTimeout(() => {\n      this.selectedCountry = event.value;\n    }, 100);\n  }\n  onTimeZoneChange(event) {\n    setTimeout(() => {\n      this.selectedTimezone = event.value;\n    }, 100);\n  }\n  onPhoneCodeChange(event) {\n    setTimeout(() => {\n      this.selectedPhoneCode = event.value;\n      console.log(this.selectedPhoneCode);\n    }, 100);\n  }\n  initFormChangedListener() {\n    this.subs.add(this.form.valueChanges.subscribe(val => {\n      if (this.form.dirty) {\n        this.formChanged = true;\n      }\n    }));\n  }\n  ifFieldValid(field) {\n    this.teacherService.setStepValid(0, this.form, 'teacher-info-route');\n    return this.form.get(field)?.invalid && this.form.get(field)?.touched || this.tryToSave && this.form.get(field)?.invalid;\n  }\n  ifNativeLanguagesFieldValid(i) {\n    this.teacherService.setStepValid(0, this.form, 'teacher-info-route');\n    return this.tryToSave && this.form.get('nativeLanguages').controls[i].invalid;\n  }\n  addnativeLanguage(objectValue) {\n    if (this.nativeLanguages.length < 3) {\n      const group = new UntypedFormGroup({\n        native: new UntypedFormControl(objectValue, Validators.required)\n      });\n      this.nativeLanguages.push(group);\n    }\n  }\n  removeNativeLanguage(index) {\n    this.nativeLanguages.removeAt(index);\n  }\n  isValidDate(dateValue) {\n    const date = new Date(dateValue);\n    if (isNaN(date.getTime()) || date.getFullYear() < 1000) {\n      return false;\n    } else {\n      return true;\n    }\n  }\n  findCountryImage(phoneCode) {\n    return this.generalService.findCountryImage(phoneCode);\n  }\n  prepareSelectedPhoneCodeFromIP() {\n    this.generalService.getCountryCode().subscribe(res => {\n      if (res) {\n        console.log(res);\n        this.selectedPhoneCode = this.phoneCodes.filter(el => el.iso === res.country_code)[0];\n      }\n    });\n  }\n  canDeactivate() {\n    if (this.formChanged) {\n      return new Observable(observer => {\n        this.confirmationService.confirm({\n          header: '',\n          key: 'stepLeaveConfirmation',\n          message: 'Are you sure you want to leave this page? Your changes will be lost.',\n          accept: () => {\n            observer.next(true);\n            observer.complete();\n          },\n          reject: () => {\n            observer.next(false);\n            observer.complete();\n          }\n        });\n      });\n    } else {\n      return true;\n    }\n  }\n  static #_ = this.ɵfac = function GeneralInfoComponent_Factory(t) {\n    return new (t || GeneralInfoComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.UntypedFormBuilder), i0.ɵɵdirectiveInject(i3.GeneralService), i0.ɵɵdirectiveInject(i4.TeacherApplicationService), i0.ɵɵdirectiveInject(i5.AuthService), i0.ɵɵdirectiveInject(i6.ToastService), i0.ɵɵdirectiveInject(i7.ConfirmationService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: GeneralInfoComponent,\n    selectors: [[\"app-general-info\"]],\n    viewQuery: function GeneralInfoComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ngForm = _t.first);\n      }\n    },\n    decls: 10,\n    vars: 1,\n    consts: [[1, \"md:mt-3\"], [1, \"profile-info\"], [4, \"ngIf\"], [1, \"btns\", \"md:ml-5\", \"mt-4\"], [\"pRipple\", \"\", \"disabled\", \"\", 1, \"rounded-blue-button\", \"transparent\"], [\"src\", \"/assets/icons/arrow-left-blue.svg\"], [\"pRipple\", \"\", 1, \"rounded-blue-button\", 3, \"click\"], [\"src\", \"/assets/icons/arrow-right.svg\"], [3, \"formGroup\"], [1, \"profile-info-section\", 2, \"padding-top\", \"0\"], [1, \"input-fields\"], [1, \"input-field\"], [1, \"input-element-title\"], [\"formControlName\", \"firstName\", \"type\", \"text\", 1, \"input-element\"], [\"class\", \"input-error\", 4, \"ngIf\"], [\"formControlName\", \"lastName\", \"type\", \"text\", 1, \"input-element\"], [1, \"w-100\"], [\"formControlName\", \"prefferedName\", \"type\", \"text\", 1, \"input-element\"], [\"autocomplete\", \"autocomplete_off_hack_xfr4!k\", \"optionLabel\", \"name\", \"filterBy\", \"name\", \"placeholder\", \"Select Country\", \"formControlName\", \"countryOrigin\", \"styleClass\", \"dropdown-blue small-dropdown-items\", 3, \"onChange\", \"options\", \"filter\"], [\"pTemplate\", \"item\"], [\"autocomplete\", \"autocomplete_off_hack_xfr4!k\", \"optionLabel\", \"name\", \"filterBy\", \"name\", \"placeholder\", \"Select Country\", \"formControlName\", \"countryResidency\", \"styleClass\", \"dropdown-blue small-dropdown-items\", 3, \"onChange\", \"options\", \"filter\"], [\"autocomplete\", \"autocomplete_off_hack_xfr4!k\", \"optionLabel\", \"text\", \"filterBy\", \"text\", \"placeholder\", \"Select Zone\", \"formControlName\", \"timeZone\", \"styleClass\", \"dropdown-blue\", 3, \"onChange\", \"options\", \"filter\"], [\"dateFormat\", \"dd/mm/yy\", \"styleClass\", \"date-element\", \"inputStyleClass\", \"input-element\", \"formControlName\", \"birth\", 3, \"onSelect\", \"maxDate\", \"defaultDate\", \"keepInvalid\", \"monthNavigator\", \"yearNavigator\", \"yearRange\", \"firstDayOfWeek\", \"readonlyInput\"], [\"formArrayName\", \"nativeLanguages\"], [\"style\", \"position: relative; display: flex;flex-direction: column;\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"input-error w-100\", 4, \"ngIf\"], [1, \"step-heading-right\", \"m-t-30\", 2, \"padding\", \"10px\"], [\"formControlName\", \"email\", \"type\", \"email\", 1, \"input-element\"], [1, \"ui-g\", \"ui-fluid\"], [1, \"display-flex\"], [\"fxFlex\", \"\", 1, \"col--2of3\"], [\"optionLabel\", \"code\", \"filterBy\", \"code\", \"flagFilter\", \"code\", \"templateValue\", \"code\", \"styleClass\", \"dropdown-blue phone-code-input small-dropdown-items\", 3, \"valueSelected\", \"parentForm\", \"countries\", \"filter\", \"inputName\", \"showTextAfterImage\", \"withFlags\", \"selectedItemValue\"], [\"fxFlex\", \"\", 1, \"col--1of1\"], [\"formControlName\", \"phone\", \"type\", \"text\", 1, \"input-element\", \"phone-input\"], [\"formControlName\", \"skype\", \"type\", \"text\", 1, \"input-element\"], [1, \"input-error\"], [1, \"country-item\"], [3, \"src\"], [1, \"country-name\"], [2, \"position\", \"relative\", \"display\", \"flex\", \"flex-direction\", \"column\"], [2, \"flex-direction\", \"row\", \"display\", \"flex\", \"align-items\", \"center\"], [3, \"formGroupName\"], [1, \"display-flex\", \"align-center\", \"m-t-10\", 2, \"gap\", \"10px\", \"width\", \"100%\"], [\"autocomplete\", \"off\", \"optionLabel\", \"name\", \"filterBy\", \"name\", \"placeholder\", \"Select Native\", \"formControlName\", \"native\", \"styleClass\", \"dropdown-blue m-t-0\", 3, \"onChange\", \"options\", \"filter\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-plus\", 1, \"p-button-raised\", \"p-button-rounded\", \"plus-btn-circle\", 3, \"click\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-minus\", 1, \"p-button-raised\", \"p-button-rounded\", \"minus-btn-circle\", 3, \"click\"], [1, \"input-error\", \"w-100\"]],\n    template: function GeneralInfoComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"div\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1);\n        i0.ɵɵtemplate(2, GeneralInfoComponent_ng_container_2_Template, 72, 33, \"ng-container\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"button\", 4);\n        i0.ɵɵelement(5, \"img\", 5);\n        i0.ɵɵtext(6, \" Back\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(7, \"button\", 6);\n        i0.ɵɵlistener(\"click\", function GeneralInfoComponent_Template_button_click_7_listener() {\n          return ctx.onSubmit();\n        });\n        i0.ɵɵtext(8, \"Next \");\n        i0.ɵɵelement(9, \"img\", 7);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n      }\n    },\n    dependencies: [i8.NgForOf, i8.NgIf, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i2.FormGroupName, i2.FormArrayName, i9.Dropdown, i7.PrimeTemplate, i10.Calendar, i11.ButtonDirective, i12.Ripple],\n    styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.modal[_ngcontent-%COMP%] {\\n  min-width: 50vw;\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown:not(.p-disabled):hover {\\n  border: 2px solid rgb(228, 185, 84);\\n}\\n\\n  .no-label > .p-button-label {\\n  display: none;\\n}\\n\\ninput[type=file][_ngcontent-%COMP%] {\\n  visibility: hidden;\\n  width: 10px;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%] {\\n  position: relative;\\n  padding: 10px;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%]::-webkit-calendar-picker-indicator {\\n  color: transparent;\\n  background: none;\\n  z-index: 1;\\n}\\n\\ninput[type=date][_ngcontent-%COMP%]:before {\\n  color: transparent;\\n  background: none;\\n  display: block;\\n  font-family: \\\"FontAwesome\\\";\\n  content: \\\"\\\\f073\\\";\\n  \\n\\n  width: 20px;\\n  height: 25px;\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  color: #999;\\n}\\n\\n.image-preview[_ngcontent-%COMP%] {\\n  height: 10rem;\\n  margin: 1rem 0;\\n  border-radius: 50%;\\n  border: 3px solid var(--main-color);\\n}\\n\\n.image-preview[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  height: 100%;\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-header {\\n  padding: clamp(0.38rem, 0.47vw + 0.28rem, 0.75rem);\\n}\\n\\n[_nghost-%COMP%]     .p-dropdown-panel .p-dropdown-items .p-dropdown-item {\\n  margin-bottom: clamp(0.38rem, 0.47vw + 0.28rem, 0.75rem);\\n}\\n\\n.country-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: clamp(0.75rem, 0.31vw + 0.69rem, 1rem);\\n  font-size: clamp(0.88rem, 0.16vw + 0.84rem, 1rem);\\n}\\n.country-item[_ngcontent-%COMP%]   .country-name[_ngcontent-%COMP%] {\\n  white-space: pre-line; \\n\\n}\\n.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  \\n\\n\\n\\n\\n\\n  \\n\\n  display: inline-block;\\n  vertical-align: middle;\\n}\\n.country-item[_ngcontent-%COMP%]   .circular_image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  clip-path: circle();\\n}\\n.country-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 32px;\\n  margin-right: 0.5rem;\\n}\\n.country-item[_ngcontent-%COMP%]   img.smaller[_ngcontent-%COMP%] {\\n  width: 16px;\\n}\\n\\n.info-element[_ngcontent-%COMP%] {\\n  padding: 10px 0;\\n}\\n\\n.responsive-four-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 49%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 49%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-four-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(4) {\\n    width: 23.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.responsive-nested-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 992px) {\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 32%;\\n    margin-right: 1%;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n  .responsive-nested-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(3) {\\n    width: 32%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n.two-col-grid[_ngcontent-%COMP%]:after {\\n  display: table;\\n  content: \\\" \\\";\\n  clear: both;\\n}\\n\\n@media only screen and (min-width: 768px) {\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(1) {\\n    width: 23.5%;\\n    margin-right: 1%;\\n    margin-left: 0px;\\n    float: left;\\n  }\\n  .two-col-grid[_ngcontent-%COMP%]    > *[_ngcontent-%COMP%]:nth-child(2) {\\n    width: 74.5%;\\n    margin-right: 0px;\\n    margin-left: 1%;\\n    float: left;\\n  }\\n}\\n\\n\\n\\n\\n\\n\\n\\n.container[_ngcontent-%COMP%] {\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n}\\n.container[_ngcontent-%COMP%]:not(.is-fluid) {\\n  margin: 0 auto;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container[_ngcontent-%COMP%]:not(.is-fluid) {\\n    width: 100%;\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-sm[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 576px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 100vw;\\n  }\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-sm[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-md[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 768px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 95vw;\\n  }\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-md[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-lg[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 992px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 90vw;\\n  }\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-lg[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1200px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 996px;\\n  }\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-xxl[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 1400px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1050px;\\n  }\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-xxl[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-qhd[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: 2560px) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n@media only screen and (min-width: ) {\\n  .container-qhd[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.container-_2k[_ngcontent-%COMP%] {\\n  margin: 0 auto;\\n  padding-right: 1rem;\\n  padding-left: 1rem;\\n  width: 100%;\\n}\\n@media only screen and (min-width: ) {\\n  .container-_2k[_ngcontent-%COMP%] {\\n    max-width: 1264px;\\n  }\\n}\\n\\n.white-button[_ngcontent-%COMP%] {\\n  padding: 10px;\\n}\\n\\n.dd-style[_ngcontent-%COMP%] {\\n  border: 1px solid #aaa;\\n}\\n\\np-dropdown[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n[_nghost-%COMP%]     .p-calendar.p-calendar-w-btn {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n[_nghost-%COMP%]     .date-element {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.trash[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  cursor: pointer;\\n}\\n.trash[_ngcontent-%COMP%]    > img[_ngcontent-%COMP%] {\\n  width: 30px;\\n  margin-top: 5px;\\n}\\n\\n[_nghost-%COMP%]     .small-dropdown-items .p-dropdown-panel .p-dropdown-items .p-dropdown-item {\\n  padding: 0;\\n}\\n\\n[_nghost-%COMP%]     .small-dropdown-items .p-dropdown-items {\\n  min-width: clamp(10rem, 2.34vw + 9.53rem, 11.88rem);\\n}\\n\\n.phonecode-item[_ngcontent-%COMP%] {\\n  align-items: center;\\n  display: flex;\\n  gap: clamp(0.75rem, 0.31vw + 0.69rem, 1rem);\\n  font-size: clamp(0.88rem, 0.16vw + 0.84rem, 1rem);\\n}\\n.phonecode-item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: clamp(1.5rem, 0.63vw + 1.38rem, 2rem);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["UntypedFormArray", "UntypedFormControl", "UntypedFormGroup", "Validators", "Observable", "SubSink", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵproperty", "country_r3", "image", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "name", "country_r4", "ɵɵtemplate", "GeneralInfoComponent_ng_container_2_ng_template_36_div_3_Template", "timezone_r5", "text", "ctx_r1", "ifFieldValid", "language_r7", "ɵɵlistener", "GeneralInfoComponent_ng_container_2_div_47_div_6_Template_button_click_1_listener", "ɵɵrestoreView", "_r8", "ɵɵnextContext", "ɵɵresetView", "addnativeLanguage", "ɵɵelementContainerStart", "GeneralInfoComponent_ng_container_2_div_47_ng_container_7_Template_button_click_1_listener", "_r9", "i_r10", "index", "removeNativeLanguage", "GeneralInfoComponent_ng_container_2_div_47_Template_p_dropdown_onChange_4_listener", "$event", "_r6", "onOriginChange", "GeneralInfoComponent_ng_container_2_div_47_ng_template_5_Template", "GeneralInfoComponent_ng_container_2_div_47_div_6_Template", "GeneralInfoComponent_ng_container_2_div_47_ng_container_7_Template", "languages", "isTablet", "GeneralInfoComponent_ng_container_2_div_8_Template", "GeneralInfoComponent_ng_container_2_div_13_Template", "GeneralInfoComponent_ng_container_2_div_19_Template", "GeneralInfoComponent_ng_container_2_Template_p_dropdown_onChange_23_listener", "_r1", "onCountryChange", "GeneralInfoComponent_ng_container_2_ng_template_24_Template", "GeneralInfoComponent_ng_container_2_div_25_Template", "GeneralInfoComponent_ng_container_2_Template_p_dropdown_onChange_29_listener", "GeneralInfoComponent_ng_container_2_ng_template_30_Template", "GeneralInfoComponent_ng_container_2_div_31_Template", "GeneralInfoComponent_ng_container_2_Template_p_dropdown_onChange_35_listener", "onTimeZoneChange", "GeneralInfoComponent_ng_container_2_ng_template_36_Template", "GeneralInfoComponent_ng_container_2_div_37_Template", "GeneralInfoComponent_ng_container_2_Template_p_calendar_onSelect_41_listener", "onBirthDateSelected", "GeneralInfoComponent_ng_container_2_div_42_Template", "GeneralInfoComponent_ng_container_2_div_47_Template", "GeneralInfoComponent_ng_container_2_div_48_Template", "GeneralInfoComponent_ng_container_2_div_57_Template", "GeneralInfoComponent_ng_container_2_Template_app_prime_input_dropdown_valueSelected_64_listener", "onPhoneCodeChange", "GeneralInfoComponent_ng_container_2_div_67_Template", "form", "countries", "timezones", "maxBirthdateTime", "nativeLanguages", "controls", "phoneCodes", "selectedPhoneCode", "GeneralInfoComponent", "constructor", "router", "fb", "generalService", "teacherService", "authService", "toast", "confirmationService", "ngForm", "subs", "getTimezones", "getCountries", "getPhoneCodes", "tryToSave", "isLoading", "selectedCountry", "Date", "formattedBirthDate", "nativeLanguagesModel", "formChanged", "get", "ngOnInit", "add", "submitOnMenuClickListener", "subscribe", "res", "onSubmit", "console", "log", "date", "newDate", "setFullYear", "getFullYear", "setCurrentStepIndex", "teacher", "dummy<PERSON><PERSON><PERSON>", "deviceKind", "is1024", "getTeacherApplicationStepsStatus", "stepsStatuses", "step1", "prepareSelectedPhoneCodeFromIP", "getTeacherApplicationStep1", "getUserId", "updateStep1TeacherInfoFormValues", "initFormChangedListener", "ngOnDestroy", "unsubscribe", "setSubmitOnMenuClickListener", "invalidInputs", "controlName", "hasOwnProperty", "control", "invalid", "detailMessage", "slice", "valid", "setShowToastmessage", "severity", "summary", "detail", "teacherApplicationStep1FormData", "firstName", "value", "lastName", "prefferedName", "country<PERSON><PERSON><PERSON>", "flagUrl", "countryResidency", "timeZone", "utc", "birth", "convertDateStringToIsoFormatWithZeroTime", "email", "phone", "phoneCode", "code", "skype", "updateAPITeacherApplicationStep1", "navigateByUrl", "replaceUrl", "teacherStep1Object", "isValidDate", "formatDateToDMY", "toString", "isNullishObject", "filter", "el", "validators", "required", "find", "includes", "for<PERSON>ach", "element", "native", "push", "group", "length", "event", "d", "parse", "isConfirm", "field", "touched", "password", "confirm", "setTimeout", "selectedLanguage", "selectedTimezone", "valueChanges", "val", "dirty", "setStepValid", "ifNativeLanguagesFieldValid", "i", "objectValue", "removeAt", "dateValue", "isNaN", "getTime", "findCountryImage", "getCountryCode", "iso", "country_code", "canDeactivate", "observer", "header", "key", "message", "accept", "next", "complete", "reject", "_", "ɵɵdirectiveInject", "i1", "Router", "i2", "UntypedFormBuilder", "i3", "GeneralService", "i4", "TeacherApplicationService", "i5", "AuthService", "i6", "ToastService", "i7", "ConfirmationService", "_2", "selectors", "viewQuery", "GeneralInfoComponent_Query", "rf", "ctx", "GeneralInfoComponent_ng_container_2_Template", "GeneralInfoComponent_Template_button_click_7_listener"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\teacher-application\\general-info\\general-info.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\teacher-application\\general-info\\general-info.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\r\nimport { NgForm, UntypedFormArray, UntypedFormBuilder, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { ConfirmationService } from 'primeng/api';\r\nimport { Observable, of } from 'rxjs';\r\nimport { Country, Language, Timezone } from 'src/app/core/models/general.model';\r\nimport { Teacher, TeacherApplicationStep1, TeacherApplicationStep1Native } from 'src/app/core/models/teacher.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { TeacherApplicationService } from 'src/app/core/services/teacher-application.service';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { SubSink } from 'subsink';\r\n\r\n@Component({\r\n  selector: 'app-general-info',\r\n  templateUrl: './general-info.component.html',\r\n  styleUrls: ['./general-info.component.scss'],\r\n})\r\nexport class GeneralInfoComponent implements OnInit {\r\n  @ViewChild('opportunityForm', { static: true }) ngForm: NgForm = {} as NgForm;\r\n  private subs = new SubSink();\r\n  public form: UntypedFormGroup = new UntypedFormGroup({});\r\n  public isTablet: boolean = false;\r\n  public timezones: Timezone[] = this.generalService.getTimezones();\r\n  public countries: Country[] = this.generalService.getCountries();\r\n  public phoneCodes: any[] = this.generalService.getPhoneCodes();\r\n  public languages: Language[] = this.generalService.languages;\r\n  public selectedOriginCountry?: Country;\r\n  public selectedTimezone?: Timezone;\r\n  public selectedLanguage?: Language;\r\n  public tryToSave: boolean = false;\r\n  public isLoading: boolean = true;\r\n  public teacher?: Teacher;\r\n  public teacherStep1?: TeacherApplicationStep1;\r\n  public selectedCountry: string = \"\";\r\n  public maxBirthdateTime = new Date();\r\n  public formattedBirthDate: string = \"\";\r\n  private nativeLanguagesModel: { name: string }[] = [];\r\n  private formChanged = false;\r\n  public selectedPhoneCode: any | undefined = {} as any | undefined;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private fb: UntypedFormBuilder,\r\n    private generalService: GeneralService,\r\n    private teacherService: TeacherApplicationService,\r\n    private authService: AuthService,\r\n    private toast: ToastService,\r\n    private confirmationService: ConfirmationService\r\n  ) { }\r\n\r\n  get nativeLanguages(): UntypedFormArray {\r\n    return this.form.get('nativeLanguages') as UntypedFormArray;\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.isLoading = true;\r\n    this.subs.add(this.teacherService.submitOnMenuClickListener.subscribe(res => {\r\n      if (res) {\r\n        this.onSubmit();\r\n      }\r\n    }));\r\n\r\n\r\n    console.log(this.maxBirthdateTime);\r\n    const date = new Date();\r\n\r\n    const newDate = (this.maxBirthdateTime.setFullYear(this.maxBirthdateTime.getFullYear() - 18));\r\n    this.maxBirthdateTime = new Date(newDate);\r\n    this.teacherService.setCurrentStepIndex(0);\r\n    this.teacher = this.teacherService.dummyTeacher;\r\n\r\n    this.subs.add(this.generalService.deviceKind.subscribe(res => {\r\n      this.isTablet = res.is1024;\r\n    }));\r\n\r\n    this.subs.add(this.teacherService.getTeacherApplicationStepsStatus().subscribe(stepsStatuses => {\r\n      if (!stepsStatuses.step1) {\r\n        this.prepareSelectedPhoneCodeFromIP();\r\n      } \r\n    }));\r\n\r\n    this.subs.add(this.teacherService.getTeacherApplicationStep1(this.authService.getUserId()!).subscribe(res => {\r\n      console.log(res);\r\n      this.updateStep1TeacherInfoFormValues(res);\r\n      this.initFormChangedListener();\r\n      this.isLoading = false;\r\n    }));\r\n\r\n    // updating title for mobile header text\r\n    // this.generalService.updateMobileMenuTitle(this.teacherService.getStepsTitle(0));\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    this.subs.unsubscribe();\r\n    this.teacherService.setSubmitOnMenuClickListener(false);\r\n  }\r\n\r\n  onSubmit() {\r\n    this.tryToSave = true;\r\n\r\n    let invalidInputs = '';\r\n    for (const controlName in this.form.controls) {\r\n      if (this.form.controls.hasOwnProperty(controlName)) {\r\n        const control = this.form.controls[controlName];\r\n        if (control.invalid) {\r\n          invalidInputs += `${controlName}, `;\r\n        }\r\n      }\r\n    }\r\n\r\n    let detailMessage = 'Please enter all required fields to continue.';\r\n    if (invalidInputs !== '') {\r\n      invalidInputs = invalidInputs.slice(0, -2); // Remove the trailing comma and space\r\n\r\n      if (invalidInputs !== '') {\r\n        detailMessage += `Invalid input for the following fields: ${invalidInputs}`;\r\n      }\r\n    }\r\n    if (!this.form.valid) {\r\n      this.toast.setShowToastmessage({\r\n        severity: 'warn',\r\n        summary: '',\r\n        detail: detailMessage\r\n      });\r\n      return;\r\n    }\r\n    this.formChanged = false;\r\n    const teacherApplicationStep1FormData: TeacherApplicationStep1 = {\r\n      firstName: this.form.value.firstName,\r\n      lastName: this.form.value.lastName,\r\n      prefferedName: this.form.value.prefferedName,\r\n      countryOrigin: { name: this.form.value.countryOrigin.name, flagUrl: this.form.value.countryOrigin.image },\r\n      countryResidency: { name: this.form.value.countryResidency.name, flagUrl: this.form.value.countryResidency.image },\r\n      nativeLanguages: this.form.value.nativeLanguages,\r\n      timeZone: this.form.value.timeZone?.utc[0],\r\n      birth: this.generalService.convertDateStringToIsoFormatWithZeroTime(this.formattedBirthDate),\r\n      email: this.form.value.email,\r\n      phone: this.form.value.phone,\r\n      phoneCode: this.form.value.phoneCode.code,\r\n      skype: this.form.value.skype,\r\n    }\r\n    this.subs.add(this.teacherService.updateAPITeacherApplicationStep1(teacherApplicationStep1FormData).subscribe(res => {\r\n      if (res) {\r\n        this.toast.setShowToastmessage({ severity: 'success', summary: '', detail: 'Your General details were saved.' });\r\n        // this.toastr.success(\"Your general info were updated.\")\r\n        this.router.navigateByUrl('/teacher/education', { replaceUrl: true });\r\n      }\r\n    }));\r\n\r\n  }\r\n\r\n  updateStep1TeacherInfoFormValues(teacherStep1Object: TeacherApplicationStep1) {\r\n\r\n    if (this.isValidDate(teacherStep1Object.birth)) {\r\n      this.formattedBirthDate = this.generalService.formatDateToDMY(teacherStep1Object.birth);\r\n    } else {\r\n      this.formattedBirthDate = this.generalService.formatDateToDMY(this.maxBirthdateTime.toString());\r\n    }\r\n\r\n    if (!this.generalService.isNullishObject(teacherStep1Object.phoneCode)) {\r\n      this.selectedPhoneCode = this.phoneCodes.filter(el => el.code === teacherStep1Object.phoneCode)[0];\r\n    }\r\n\r\n    this.form = new UntypedFormGroup({\r\n      birth: new UntypedFormControl(this.formattedBirthDate, {\r\n        validators: [Validators.required]\r\n      }),\r\n      prefferedName: new UntypedFormControl(teacherStep1Object.prefferedName, {\r\n        validators: [Validators.required]\r\n      }),\r\n      firstName: new UntypedFormControl(teacherStep1Object.firstName, {\r\n        validators: [Validators.required]\r\n      }),\r\n      lastName: new UntypedFormControl(teacherStep1Object.lastName, {\r\n        validators: [Validators.required]\r\n      }),\r\n      email: new UntypedFormControl(teacherStep1Object.email, {\r\n        validators: [Validators.required, Validators.email]\r\n      }),\r\n      phoneCode: new UntypedFormControl(this.selectedPhoneCode, {\r\n        validators: [Validators.required]\r\n      }),\r\n      phone: new UntypedFormControl(teacherStep1Object.phone, {\r\n        validators: [Validators.required]\r\n      }),\r\n      skype: new UntypedFormControl(teacherStep1Object.skype || '-', {\r\n        validators: [Validators.required]\r\n      }),\r\n      nativeLanguages: new UntypedFormArray([], {\r\n        validators: [Validators.required]\r\n      }),\r\n      timeZone: new UntypedFormControl(\r\n        this.timezones.find(el => el.utc.includes(teacherStep1Object.timeZone!)), {\r\n        validators: [Validators.required]\r\n      }),\r\n      countryOrigin: new UntypedFormControl(\r\n        !this.generalService.isNullishObject(teacherStep1Object.countryOrigin) ?\r\n          this.countries.filter(el => el.name === teacherStep1Object.countryOrigin.name)[0] : null, {\r\n        validators: [Validators.required]\r\n      }),\r\n      countryResidency: new UntypedFormControl(\r\n        !this.generalService.isNullishObject(teacherStep1Object.countryResidency) ?\r\n          this.countries.filter(el => el.name === teacherStep1Object.countryResidency.name)[0] : null, {\r\n        validators: [Validators.required]\r\n      }),\r\n    });\r\n\r\n    if (!this.generalService.isNullishObject(teacherStep1Object.nativeLanguages)) {\r\n      teacherStep1Object.nativeLanguages.forEach(element => {\r\n        this.addnativeLanguage(element.native);\r\n      });\r\n    } else {\r\n      this.nativeLanguagesModel.forEach(element => {\r\n        this.nativeLanguages.push(this.fb.group(element))\r\n      });\r\n\r\n      if (this.nativeLanguagesModel.length === 0) {\r\n        this.addnativeLanguage({})\r\n      }\r\n    }\r\n\r\n  }\r\n\r\n  onBirthDateSelected(event: any) {\r\n    console.log(event);\r\n    let d = new Date(Date.parse(event));\r\n    this.formattedBirthDate = this.generalService.formatDateToDMY(event);\r\n  }\r\n\r\n  isConfirm(field: string) {\r\n    return ((this.form.get(field)?.invalid && this.form.get(field)?.touched) || (this.tryToSave && this.form.get(field)?.invalid) || (this.form.value.password !== this.form.value.confirm && this.form.get(field)?.touched))\r\n  }\r\n\r\n  onOriginChange(event: any) {\r\n    setTimeout(() => {\r\n      this.selectedLanguage = event.value\r\n    }, 100);\r\n  }\r\n\r\n  onCountryChange(event: any) {\r\n    setTimeout(() => {\r\n      this.selectedCountry = event.value\r\n    }, 100);\r\n  }\r\n\r\n  onTimeZoneChange(event: any) {\r\n    setTimeout(() => {\r\n      this.selectedTimezone = event.value\r\n    }, 100);\r\n  }\r\n\r\n  onPhoneCodeChange(event: any) {\r\n    setTimeout(() => {\r\n      this.selectedPhoneCode = event.value;\r\n      console.log(this.selectedPhoneCode);\r\n    }, 100);\r\n  }\r\n\r\n  initFormChangedListener() {\r\n    this.subs.add(this.form.valueChanges.subscribe(val => {\r\n      if (this.form.dirty) {\r\n        this.formChanged = true;\r\n      }\r\n    }));\r\n  }\r\n\r\n  ifFieldValid(field: string) {\r\n    this.teacherService.setStepValid(0, this.form, 'teacher-info-route')\r\n    return ((this.form.get(field)?.invalid && this.form.get(field)?.touched) || (this.tryToSave && this.form.get(field)?.invalid))\r\n  }\r\n\r\n  ifNativeLanguagesFieldValid(i: number) {\r\n    this.teacherService.setStepValid(0, this.form, 'teacher-info-route');\r\n    return ((this.tryToSave && (<UntypedFormArray>this.form.get('nativeLanguages')).controls[i].invalid))\r\n  }\r\n\r\n  addnativeLanguage(objectValue: object) {\r\n    if (this.nativeLanguages.length < 3) {\r\n      const group = new UntypedFormGroup({\r\n        native: new UntypedFormControl(objectValue, Validators.required),\r\n      });\r\n      this.nativeLanguages.push(group);\r\n    }\r\n  }\r\n\r\n  removeNativeLanguage(index: number) {\r\n    this.nativeLanguages.removeAt(index);\r\n  }\r\n\r\n  isValidDate(dateValue: string) {\r\n    const date = new Date(dateValue);\r\n    if (isNaN(date.getTime()) || date.getFullYear() < 1000) {\r\n      return false;\r\n    } else {\r\n      return true;\r\n    }\r\n  }\r\n\r\n  findCountryImage(phoneCode: string) {\r\n    return this.generalService.findCountryImage(phoneCode);\r\n  }\r\n\r\n  private prepareSelectedPhoneCodeFromIP() {\r\n    this.generalService.getCountryCode().subscribe((res: any) => {\r\n      if (res) {\r\n        console.log(res);\r\n        this.selectedPhoneCode = this.phoneCodes.filter(el => el.iso === res.country_code)[0];\r\n      }\r\n    });\r\n  }\r\n\r\n  canDeactivate(): Observable<boolean> | boolean {\r\n    if (this.formChanged) {\r\n      return new Observable((observer: any) => {\r\n        this.confirmationService.confirm({\r\n          header: '',\r\n          key: 'stepLeaveConfirmation',\r\n          message: 'Are you sure you want to leave this page? Your changes will be lost.',\r\n          accept: () => {\r\n            observer.next(true);\r\n            observer.complete();\r\n          },\r\n          reject: () => {\r\n            observer.next(false);\r\n            observer.complete();\r\n          }\r\n        });\r\n      });\r\n    } else {\r\n      return (true);\r\n    }\r\n  }\r\n}\r\n\r\n", "<div class=\"md:mt-3\"></div>\r\n<div class=\"profile-info\">\r\n    <!-- <ng-container *ngIf=\"isLoading\">\r\n        <div class=\"abs-centered\">\r\n        <p-progressSpinner></p-progressSpinner>\r\n        </div>\r\n    </ng-container> -->\r\n\r\n<ng-container *ngIf=\"!isLoading\">\r\n    <form [formGroup]=\"form\">\r\n        <div class=\"profile-info-section\" style=\"padding-top:0\">\r\n            <div class=\"input-fields\">\r\n\r\n                <div class=\"input-field\">\r\n                    <div class=\"input-element-title\">First Name *</div>\r\n                    <input class=\"input-element\" formControlName=\"firstName\" type=\"text\">\r\n                    <div *ngIf=\"ifFieldValid('fname')\" class=\"input-error\">* First Name is required</div>\r\n                </div>\r\n                <div class=\"input-field\">\r\n                    <div class=\"input-element-title\">Last Name *</div>\r\n                    <input class=\"input-element\" formControlName=\"lastName\" type=\"text\">\r\n                    <div *ngIf=\"ifFieldValid('lname')\" class=\"input-error\">* Last Name is required</div>\r\n                </div>\r\n                <!-- <div class=\"input-field\">\r\n                    <div class=\"input-element-title\">* Username</div>\r\n                    <input value=\"fds\" class=\"input-element\" formControlName=\"username\" type=\"text\">\r\n                    <div *ngIf=\"ifFieldValid('username')\" class=\"input-error\">* Username is required</div>\r\n                </div> -->\r\n                \r\n                <div class=\"w-100\">\r\n                <div class=\"input-field\">\r\n                    <div class=\"input-element-title\">Preffered Name *</div>\r\n                    <input class=\"input-element\" formControlName=\"prefferedName\" type=\"text\">\r\n                    <div *ngIf=\"ifFieldValid('preffered')\" class=\"input-error\">* Preffered name is required</div>\r\n                </div>\r\n                </div>\r\n\r\n\r\n\r\n                <div class=\"input-field\">\r\n                    <div class=\"input-element-title\">Country of Origin *</div>\r\n                    <p-dropdown autocomplete=\"autocomplete_off_hack_xfr4!k\" [options]=\"countries\"\r\n                        (onChange)=\"onCountryChange($event)\" optionLabel=\"name\" [filter]=\"!isTablet\" filterBy=\"name\"\r\n                         placeholder=\"Select Country\" formControlName=\"countryOrigin\" styleClass=\"dropdown-blue small-dropdown-items\">\r\n                        <ng-template let-country pTemplate=\"item\">\r\n                            <div class=\"country-item\">\r\n                                <img [src]=\"country.image\">\r\n                                <div class=\"country-name\">{{country.name}}</div>\r\n                            </div>\r\n                        </ng-template>\r\n                    </p-dropdown>\r\n                    <div *ngIf=\"ifFieldValid('countryOrigin')\" class=\"input-error\">* Country of Origin is required</div>\r\n                </div>\r\n\r\n\r\n                <div class=\"input-field\">\r\n                    <div class=\"input-element-title\">Country of Residency *</div>\r\n                    <p-dropdown  autocomplete=\"autocomplete_off_hack_xfr4!k\" [options]=\"countries\"\r\n                        (onChange)=\"onCountryChange($event)\" optionLabel=\"name\" [filter]=\"!isTablet\" filterBy=\"name\"\r\n                         placeholder=\"Select Country\" formControlName=\"countryResidency\" styleClass=\"dropdown-blue small-dropdown-items\">\r\n                        <ng-template let-country pTemplate=\"item\">\r\n                            <div class=\"country-item\">\r\n                                <img [src]=\"country.image\">\r\n                                <div class=\"country-name\">{{country.name}}</div>\r\n                            </div>\r\n                        </ng-template>\r\n                    </p-dropdown>\r\n                    <div *ngIf=\"ifFieldValid('countryResidency')\" class=\"input-error\">* Country of Residency is required</div>\r\n                </div>\r\n\r\n                <div class=\"input-field\">\r\n                    <div class=\"input-element-title\">Timezone *</div>\r\n                    <p-dropdown autocomplete=\"autocomplete_off_hack_xfr4!k\" [options]=\"timezones\"\r\n                        (onChange)=\"onTimeZoneChange($event)\" optionLabel=\"text\" [filter]=\"!isTablet\" filterBy=\"text\"\r\n                        placeholder=\"Select Zone\" formControlName=\"timeZone\" styleClass=\"dropdown-blue\">\r\n                        <ng-template let-timezone pTemplate=\"item\">\r\n                            <div class=\"country-item\">\r\n                                <div class=\"country-name\">{{timezone.text}}</div>\r\n                                <div *ngIf=\"ifFieldValid('timezone')\" class=\"input-error\">* Timezone is required</div>\r\n                            </div>\r\n                        </ng-template>\r\n                    </p-dropdown>\r\n                    <div *ngIf=\"ifFieldValid('zone')\" class=\"input-error\">* Timezone is required</div>\r\n                </div>\r\n                <div class=\"input-field\">\r\n                    <div class=\"input-element-title\">Date of Birth *</div>\r\n\r\n                    <p-calendar \r\n                    [maxDate]=\"maxBirthdateTime\"\r\n                    [defaultDate]=\"maxBirthdateTime\"\r\n                    dateFormat=\"dd/mm/yy\"\r\n                    styleClass=\"date-element\" \r\n                    inputStyleClass=\"input-element\"\r\n                    [keepInvalid]=\"true\"\r\n                    formControlName=\"birth\"\r\n                    [monthNavigator]=\"true\"\r\n                    [yearNavigator]=\"false\"\r\n                    [yearRange]=\"'2019:2021'\"\r\n                    [firstDayOfWeek]=\"1\"\r\n                    [readonlyInput]=\"true\"\r\n                    (onSelect)=\"onBirthDateSelected($event)\"\r\n                    ></p-calendar>\r\n                    <div *ngIf=\"ifFieldValid('birth')\" class=\"input-error\">* Date of Birth is required</div>\r\n                </div>\r\n                <div class=\"input-field\"> \r\n                    \r\n                <div class=\"input-element-title\">Native Speaker in * \r\n                  </div>\r\n                    <ng-container formArrayName=\"nativeLanguages\">\r\n                        <div *ngFor=\"let _ of nativeLanguages.controls; index as i\" style=\"position: relative; display: flex;flex-direction: column;\">\r\n                           <div style=\"flex-direction: row; display: flex;  align-items: center\">\r\n                            <ng-container [formGroupName]=\"i\">\r\n                                <div class=\"display-flex align-center  m-t-10\" style=\"gap: 10px;     width: 100%;\">\r\n                                <p-dropdown autocomplete=\"off\" [options]=\"languages\" (onChange)=\"onOriginChange($event)\"\r\n                                optionLabel=\"name\" [filter]=\"!isTablet\" filterBy=\"name\" \r\n                                placeholder=\"Select Native\" formControlName=\"native\" styleClass=\"dropdown-blue m-t-0\">\r\n                                <ng-template let-language pTemplate=\"item\">\r\n                                    <div class=\"country-item\">\r\n                                        <div class=\"country-name\">{{language.name}}</div>\r\n                                    </div>\r\n                                </ng-template>\r\n                            </p-dropdown>\r\n                            <div *ngIf=\" i === 0\">\r\n                                <button pButton type=\"button\" (click)=\"addnativeLanguage()\" class=\"p-button-raised p-button-rounded plus-btn-circle\" icon=\"pi pi-plus\"></button>\r\n                            </div> \r\n                            <ng-container *ngIf=\" i > 0\">\r\n                                <button pButton type=\"button\" (click)=\"removeNativeLanguage(i)\" class=\"p-button-raised p-button-rounded minus-btn-circle\" icon=\"pi pi-minus\"></button>\r\n                            </ng-container>\r\n                        </div>\r\n    \r\n                            </ng-container>\r\n                            </div>\r\n                        </div>\r\n                        <div *ngIf=\"ifFieldValid('nativeLanguages')\" class=\"input-error w-100\">* Please select your Native languages\r\n                        </div>\r\n                    </ng-container>\r\n                    <!-- <div *ngIf=\"ifFieldValid('native')\" class=\"input-error\">* Native is required</div> -->\r\n                </div>\r\n\r\n                <!-- <div class=\"input-field\">\r\n                    <div class=\"input-element-title\">* Password</div>\r\n                    <input class=\"input-element\" formControlName=\"password\" type=\"password\">\r\n                    <div *ngIf=\"ifFieldValid('password')\" class=\"input-error\">* Password is required</div>\r\n                </div>\r\n                <div class=\"input-field\">\r\n                    <div class=\"input-element-title\">* Confirm Password</div>\r\n                    <input class=\"input-element\" formControlName=\"confirm\" type=\"password\">\r\n                    <div *ngIf=\"isConfirm('confirm')\" class=\"input-error\">* Passwords do not much</div>\r\n                </div> -->\r\n                <!-- <div class=\"input-field\">\r\n                    <div class=\"input-element-title\">* City</div>\r\n                    <input class=\"input-element\" formControlName=\"city\" type=\"text\">\r\n                    <div *ngIf=\"ifFieldValid('city')\" class=\"input-error\">* City is required</div>\r\n                </div> -->\r\n                <div class=\"w-100\">\r\n            <div class=\"step-heading-right m-t-30\" style=\"padding: 10px;\"><strong>Enter your contact info</strong></div>\r\n        </div>\r\n                    <div class=\"input-field\">\r\n                        <div class=\"input-element-title\">E-mail *</div>\r\n                        <input class=\"input-element\" formControlName=\"email\" type=\"email\">\r\n                        <div *ngIf=\"ifFieldValid('email')\" class=\"input-error\">* Email is required</div>\r\n                    </div>\r\n                    <div class=\"input-field\">\r\n                        <div class=\"input-element-title\">Phone *</div>\r\n                        <div class=\"ui-g ui-fluid\">\r\n                              <div class=\"display-flex\">\r\n                                <div fxFlex class=\"col--2of3\">\r\n                              \r\n                        <app-prime-input-dropdown \r\n                        optionLabel=\"code\"\r\n                        [parentForm]=\"form\"\r\n                        [countries]=\"phoneCodes\"\r\n                        filterBy=\"code\"\r\n                        [filter]=\"true\"\r\n                        [inputName]=\"'phoneCode'\"\r\n                        [showTextAfterImage]=\"false\"\r\n                        [withFlags]=\"true\"\r\n                        flagFilter=\"code\"\r\n                        [selectedItemValue]=\"selectedPhoneCode\"\r\n                        templateValue=\"code\"\r\n                        styleClass=\"dropdown-blue phone-code-input small-dropdown-items\"\r\n                        (valueSelected)=\"onPhoneCodeChange($event)\">\r\n                        </app-prime-input-dropdown>\r\n                                    \r\n                                </div>\r\n                                <div fxFlex class=\"col--1of1\">\r\n                                    <input class=\"input-element phone-input\" formControlName=\"phone\" type=\"text\">\r\n                                </div>\r\n                              </div>\r\n                          </div>\r\n                        <div *ngIf=\"ifFieldValid('phone')\" class=\"input-error\">* Phone is required</div>\r\n                    </div>\r\n                    <div class=\"input-field\">\r\n                        <div class=\"input-element-title\">Skype *</div>\r\n                        <input class=\"input-element\" formControlName=\"skype\" type=\"text\">\r\n                    </div>\r\n            </div>\r\n        </div>\r\n    </form>\r\n</ng-container>\r\n</div>\r\n<div class=\"btns md:ml-5 mt-4\">\r\n    <button pRipple class=\"rounded-blue-button transparent\" disabled> <img src=\"/assets/icons/arrow-left-blue.svg\" /> Back</button>\r\n    <button pRipple (click)=\"onSubmit()\" class=\"rounded-blue-button\">Next <img src=\"/assets/icons/arrow-right.svg\" /></button>\r\n</div>\r\n"], "mappings": "AACA,SAAiBA,gBAAgB,EAAsBC,kBAAkB,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,gBAAgB;AAG/H,SAASC,UAAU,QAAY,MAAM;AAOrC,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;;;;;;;ICKbC,EAAA,CAAAC,cAAA,cAAuD;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAKrFH,EAAA,CAAAC,cAAA,cAAuD;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAYpFH,EAAA,CAAAC,cAAA,cAA2D;IAAAD,EAAA,CAAAE,MAAA,mCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAYrFH,EAAA,CAAAC,cAAA,cAA0B;IACtBD,EAAA,CAAAI,SAAA,cAA2B;IAC3BJ,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAC9CF,EAD8C,CAAAG,YAAA,EAAM,EAC9C;;;;IAFGH,EAAA,CAAAK,SAAA,EAAqB;IAArBL,EAAA,CAAAM,UAAA,QAAAC,UAAA,CAAAC,KAAA,EAAAR,EAAA,CAAAS,aAAA,CAAqB;IACAT,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAU,iBAAA,CAAAH,UAAA,CAAAI,IAAA,CAAgB;;;;;IAItDX,EAAA,CAAAC,cAAA,cAA+D;IAAAD,EAAA,CAAAE,MAAA,sCAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAU5FH,EAAA,CAAAC,cAAA,cAA0B;IACtBD,EAAA,CAAAI,SAAA,cAA2B;IAC3BJ,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAC9CF,EAD8C,CAAAG,YAAA,EAAM,EAC9C;;;;IAFGH,EAAA,CAAAK,SAAA,EAAqB;IAArBL,EAAA,CAAAM,UAAA,QAAAM,UAAA,CAAAJ,KAAA,EAAAR,EAAA,CAAAS,aAAA,CAAqB;IACAT,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAU,iBAAA,CAAAE,UAAA,CAAAD,IAAA,CAAgB;;;;;IAItDX,EAAA,CAAAC,cAAA,cAAkE;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAW9FH,EAAA,CAAAC,cAAA,cAA0D;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IADtFH,EADJ,CAAAC,cAAA,cAA0B,cACI;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjDH,EAAA,CAAAa,UAAA,IAAAC,iEAAA,kBAA0D;IAC9Dd,EAAA,CAAAG,YAAA,EAAM;;;;;IAFwBH,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAU,iBAAA,CAAAK,WAAA,CAAAC,IAAA,CAAiB;IACrChB,EAAA,CAAAK,SAAA,EAA8B;IAA9BL,EAAA,CAAAM,UAAA,SAAAW,MAAA,CAAAC,YAAA,aAA8B;;;;;IAIhDlB,EAAA,CAAAC,cAAA,cAAsD;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAoBlFH,EAAA,CAAAC,cAAA,cAAuD;IAAAD,EAAA,CAAAE,MAAA,kCAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAgBpEH,EADJ,CAAAC,cAAA,cAA0B,cACI;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAC/CF,EAD+C,CAAAG,YAAA,EAAM,EAC/C;;;;IADwBH,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAU,iBAAA,CAAAS,WAAA,CAAAR,IAAA,CAAiB;;;;;;IAKnDX,EADJ,CAAAC,cAAA,UAAsB,iBACqH;IAAzGD,EAAA,CAAAoB,UAAA,mBAAAC,kFAAA;MAAArB,EAAA,CAAAsB,aAAA,CAAAC,GAAA;MAAA,MAAAN,MAAA,GAAAjB,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASR,MAAA,CAAAS,iBAAA,EAAmB;IAAA,EAAC;IAC/D1B,EAD2I,CAAAG,YAAA,EAAS,EAC9I;;;;;;IACNH,EAAA,CAAA2B,uBAAA,GAA6B;IACzB3B,EAAA,CAAAC,cAAA,iBAA6I;IAA/GD,EAAA,CAAAoB,UAAA,mBAAAQ,2FAAA;MAAA5B,EAAA,CAAAsB,aAAA,CAAAO,GAAA;MAAA,MAAAC,KAAA,GAAA9B,EAAA,CAAAwB,aAAA,GAAAO,KAAA;MAAA,MAAAd,MAAA,GAAAjB,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASR,MAAA,CAAAe,oBAAA,CAAAF,KAAA,CAAuB;IAAA,EAAC;IAA8E9B,EAAA,CAAAG,YAAA,EAAS;;;;;;;IAhB3JH,EADH,CAAAC,cAAA,cAA8H,cACrD;IACrED,EAAA,CAAA2B,uBAAA,OAAkC;IAE9B3B,EADA,CAAAC,cAAA,cAAmF,qBAGG;IAFjCD,EAAA,CAAAoB,UAAA,sBAAAa,mFAAAC,MAAA;MAAAlC,EAAA,CAAAsB,aAAA,CAAAa,GAAA;MAAA,MAAAlB,MAAA,GAAAjB,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAAYR,MAAA,CAAAmB,cAAA,CAAAF,MAAA,CAAsB;IAAA,EAAC;IAGxFlC,EAAA,CAAAa,UAAA,IAAAwB,iEAAA,0BAA2C;IAK/CrC,EAAA,CAAAG,YAAA,EAAa;IAIbH,EAHA,CAAAa,UAAA,IAAAyB,yDAAA,iBAAsB,IAAAC,kEAAA,0BAGO;IAGjCvC,EAAA,CAAAG,YAAA,EAAM;;IAINH,EADI,CAAAG,YAAA,EAAM,EACJ;;;;;IArBYH,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,UAAA,kBAAAwB,KAAA,CAAmB;IAEE9B,EAAA,CAAAK,SAAA,GAAqB;IACjCL,EADY,CAAAM,UAAA,YAAAW,MAAA,CAAAuB,SAAA,CAAqB,YAAAvB,MAAA,CAAAwB,QAAA,CACb;IAQrCzC,EAAA,CAAAK,SAAA,GAAe;IAAfL,EAAA,CAAAM,UAAA,SAAAwB,KAAA,OAAe;IAGN9B,EAAA,CAAAK,SAAA,EAAa;IAAbL,EAAA,CAAAM,UAAA,SAAAwB,KAAA,KAAa;;;;;IAQhC9B,EAAA,CAAAC,cAAA,cAAuE;IAAAD,EAAA,CAAAE,MAAA,6CACvE;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IA0BNH,EAAA,CAAAC,cAAA,cAAuD;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IA8BhFH,EAAA,CAAAC,cAAA,cAAuD;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;;IAtLxGH,EAAA,CAAA2B,uBAAA,GAAiC;IAMb3B,EALhB,CAAAC,cAAA,cAAyB,aACmC,cAC1B,cAEG,cACY;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACnDH,EAAA,CAAAI,SAAA,gBAAqE;IACrEJ,EAAA,CAAAa,UAAA,IAAA6B,kDAAA,kBAAuD;IAC3D1C,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADJ,CAAAC,cAAA,cAAyB,eACY;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAClDH,EAAA,CAAAI,SAAA,iBAAoE;IACpEJ,EAAA,CAAAa,UAAA,KAAA8B,mDAAA,kBAAuD;IAC3D3C,EAAA,CAAAG,YAAA,EAAM;IASFH,EAFJ,CAAAC,cAAA,eAAmB,eACM,eACY;IAAAD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvDH,EAAA,CAAAI,SAAA,iBAAyE;IACzEJ,EAAA,CAAAa,UAAA,KAAA+B,mDAAA,kBAA2D;IAE/D5C,EADA,CAAAG,YAAA,EAAM,EACA;IAKFH,EADJ,CAAAC,cAAA,eAAyB,eACY;IAAAD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1DH,EAAA,CAAAC,cAAA,sBAEkH;IAD9GD,EAAA,CAAAoB,UAAA,sBAAAyB,6EAAAX,MAAA;MAAAlC,EAAA,CAAAsB,aAAA,CAAAwB,GAAA;MAAA,MAAA7B,MAAA,GAAAjB,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAAYR,MAAA,CAAA8B,eAAA,CAAAb,MAAA,CAAuB;IAAA,EAAC;IAEpClC,EAAA,CAAAa,UAAA,KAAAmC,2DAAA,0BAA0C;IAM9ChD,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAa,UAAA,KAAAoC,mDAAA,kBAA+D;IACnEjD,EAAA,CAAAG,YAAA,EAAM;IAIFH,EADJ,CAAAC,cAAA,eAAyB,eACY;IAAAD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC7DH,EAAA,CAAAC,cAAA,sBAEqH;IADjHD,EAAA,CAAAoB,UAAA,sBAAA8B,6EAAAhB,MAAA;MAAAlC,EAAA,CAAAsB,aAAA,CAAAwB,GAAA;MAAA,MAAA7B,MAAA,GAAAjB,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAAYR,MAAA,CAAA8B,eAAA,CAAAb,MAAA,CAAuB;IAAA,EAAC;IAEpClC,EAAA,CAAAa,UAAA,KAAAsC,2DAAA,0BAA0C;IAM9CnD,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAa,UAAA,KAAAuC,mDAAA,kBAAkE;IACtEpD,EAAA,CAAAG,YAAA,EAAM;IAGFH,EADJ,CAAAC,cAAA,eAAyB,eACY;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACjDH,EAAA,CAAAC,cAAA,sBAEoF;IADhFD,EAAA,CAAAoB,UAAA,sBAAAiC,6EAAAnB,MAAA;MAAAlC,EAAA,CAAAsB,aAAA,CAAAwB,GAAA;MAAA,MAAA7B,MAAA,GAAAjB,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAAYR,MAAA,CAAAqC,gBAAA,CAAApB,MAAA,CAAwB;IAAA,EAAC;IAErClC,EAAA,CAAAa,UAAA,KAAA0C,2DAAA,0BAA2C;IAM/CvD,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAa,UAAA,KAAA2C,mDAAA,kBAAsD;IAC1DxD,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADJ,CAAAC,cAAA,eAAyB,eACY;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEtDH,EAAA,CAAAC,cAAA,sBAcC;IADDD,EAAA,CAAAoB,UAAA,sBAAAqC,6EAAAvB,MAAA;MAAAlC,EAAA,CAAAsB,aAAA,CAAAwB,GAAA;MAAA,MAAA7B,MAAA,GAAAjB,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAAYR,MAAA,CAAAyC,mBAAA,CAAAxB,MAAA,CAA2B;IAAA,EAAC;IACvClC,EAAA,CAAAG,YAAA,EAAa;IACdH,EAAA,CAAAa,UAAA,KAAA8C,mDAAA,kBAAuD;IAC3D3D,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAFA,CAAAC,cAAA,eAAyB,eAEQ;IAAAD,EAAA,CAAAE,MAAA,4BAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACJH,EAAA,CAAA2B,uBAAA,QAA8C;IAyB1C3B,EAxBA,CAAAa,UAAA,KAAA+C,mDAAA,kBAA8H,KAAAC,mDAAA,kBAwBvD;;IAI/E7D,EAAA,CAAAG,YAAA,EAAM;IAkBoDH,EAD1D,CAAAC,cAAA,eAAmB,eACuC,cAAQ;IAAAD,EAAA,CAAAE,MAAA,+BAAuB;IACjGF,EADiG,CAAAG,YAAA,EAAS,EAAM,EAC1G;IAEUH,EADJ,CAAAC,cAAA,eAAyB,eACY;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC/CH,EAAA,CAAAI,SAAA,iBAAkE;IAClEJ,EAAA,CAAAa,UAAA,KAAAiD,mDAAA,kBAAuD;IAC3D9D,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADJ,CAAAC,cAAA,eAAyB,eACY;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAK9CH,EAJA,CAAAC,cAAA,eAA2B,eACK,eACM,oCAeM;IAA5CD,EAAA,CAAAoB,UAAA,2BAAA2C,gGAAA7B,MAAA;MAAAlC,EAAA,CAAAsB,aAAA,CAAAwB,GAAA;MAAA,MAAA7B,MAAA,GAAAjB,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAAiBR,MAAA,CAAA+C,iBAAA,CAAA9B,MAAA,CAAyB;IAAA,EAAC;IAGnClC,EAFR,CAAAG,YAAA,EAA2B,EAEb;IACNH,EAAA,CAAAC,cAAA,eAA8B;IAC1BD,EAAA,CAAAI,SAAA,iBAA6E;IAGvFJ,EAFM,CAAAG,YAAA,EAAM,EACF,EACJ;IACRH,EAAA,CAAAa,UAAA,KAAAoD,mDAAA,kBAAuD;IAC3DjE,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADJ,CAAAC,cAAA,eAAyB,eACY;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC9CH,EAAA,CAAAI,SAAA,iBAAiE;IAIrFJ,EAHgB,CAAAG,YAAA,EAAM,EACR,EACJ,EACH;;;;;IA7LDH,EAAA,CAAAK,SAAA,EAAkB;IAAlBL,EAAA,CAAAM,UAAA,cAAAW,MAAA,CAAAiD,IAAA,CAAkB;IAOFlE,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAM,UAAA,SAAAW,MAAA,CAAAC,YAAA,UAA2B;IAK3BlB,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAM,UAAA,SAAAW,MAAA,CAAAC,YAAA,UAA2B;IAY3BlB,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAM,UAAA,SAAAW,MAAA,CAAAC,YAAA,cAA+B;IAQmBlB,EAAA,CAAAK,SAAA,GAAqB;IACjBL,EADJ,CAAAM,UAAA,YAAAW,MAAA,CAAAkD,SAAA,CAAqB,YAAAlD,MAAA,CAAAwB,QAAA,CACG;IAS1EzC,EAAA,CAAAK,SAAA,GAAmC;IAAnCL,EAAA,CAAAM,UAAA,SAAAW,MAAA,CAAAC,YAAA,kBAAmC;IAMgBlB,EAAA,CAAAK,SAAA,GAAqB;IAClBL,EADH,CAAAM,UAAA,YAAAW,MAAA,CAAAkD,SAAA,CAAqB,YAAAlD,MAAA,CAAAwB,QAAA,CACE;IAS1EzC,EAAA,CAAAK,SAAA,GAAsC;IAAtCL,EAAA,CAAAM,UAAA,SAAAW,MAAA,CAAAC,YAAA,qBAAsC;IAKYlB,EAAA,CAAAK,SAAA,GAAqB;IAChBL,EADL,CAAAM,UAAA,YAAAW,MAAA,CAAAmD,SAAA,CAAqB,YAAAnD,MAAA,CAAAwB,QAAA,CACI;IAS3EzC,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAM,UAAA,SAAAW,MAAA,CAAAC,YAAA,SAA0B;IAMhClB,EAAA,CAAAK,SAAA,GAA4B;IAW5BL,EAXA,CAAAM,UAAA,YAAAW,MAAA,CAAAoD,gBAAA,CAA4B,gBAAApD,MAAA,CAAAoD,gBAAA,CACI,qBAIZ,wBAEG,wBACA,0BACE,qBACL,uBACE;IAGhBrE,EAAA,CAAAK,SAAA,EAA2B;IAA3BL,EAAA,CAAAM,UAAA,SAAAW,MAAA,CAAAC,YAAA,UAA2B;IAOVlB,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAM,UAAA,YAAAW,MAAA,CAAAqD,eAAA,CAAAC,QAAA,CAA6B;IAwB1CvE,EAAA,CAAAK,SAAA,EAAqC;IAArCL,EAAA,CAAAM,UAAA,SAAAW,MAAA,CAAAC,YAAA,oBAAqC;IA2BrClB,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAM,UAAA,SAAAW,MAAA,CAAAC,YAAA,UAA2B;IAUjClB,EAAA,CAAAK,SAAA,GAAmB;IAQnBL,EARA,CAAAM,UAAA,eAAAW,MAAA,CAAAiD,IAAA,CAAmB,cAAAjD,MAAA,CAAAuD,UAAA,CACK,gBAET,0BACU,6BACG,mBACV,sBAAAvD,MAAA,CAAAwD,iBAAA,CAEqB;IAYjCzE,EAAA,CAAAK,SAAA,GAA2B;IAA3BL,EAAA,CAAAM,UAAA,SAAAW,MAAA,CAAAC,YAAA,UAA2B;;;AD5KzD,OAAM,MAAOwD,oBAAoB;EAuB/BC,YACUC,MAAc,EACdC,EAAsB,EACtBC,cAA8B,EAC9BC,cAAyC,EACzCC,WAAwB,EACxBC,KAAmB,EACnBC,mBAAwC;IANxC,KAAAN,MAAM,GAANA,MAAM;IACN,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,mBAAmB,GAAnBA,mBAAmB;IA7BmB,KAAAC,MAAM,GAAW,EAAY;IACrE,KAAAC,IAAI,GAAG,IAAIrF,OAAO,EAAE;IACrB,KAAAmE,IAAI,GAAqB,IAAItE,gBAAgB,CAAC,EAAE,CAAC;IACjD,KAAA6C,QAAQ,GAAY,KAAK;IACzB,KAAA2B,SAAS,GAAe,IAAI,CAACU,cAAc,CAACO,YAAY,EAAE;IAC1D,KAAAlB,SAAS,GAAc,IAAI,CAACW,cAAc,CAACQ,YAAY,EAAE;IACzD,KAAAd,UAAU,GAAU,IAAI,CAACM,cAAc,CAACS,aAAa,EAAE;IACvD,KAAA/C,SAAS,GAAe,IAAI,CAACsC,cAAc,CAACtC,SAAS;IAIrD,KAAAgD,SAAS,GAAY,KAAK;IAC1B,KAAAC,SAAS,GAAY,IAAI;IAGzB,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAArB,gBAAgB,GAAG,IAAIsB,IAAI,EAAE;IAC7B,KAAAC,kBAAkB,GAAW,EAAE;IAC9B,KAAAC,oBAAoB,GAAuB,EAAE;IAC7C,KAAAC,WAAW,GAAG,KAAK;IACpB,KAAArB,iBAAiB,GAAoB,EAAqB;EAU7D;EAEJ,IAAIH,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACJ,IAAI,CAAC6B,GAAG,CAAC,iBAAiB,CAAqB;EAC7D;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACP,SAAS,GAAG,IAAI;IACrB,IAAI,CAACL,IAAI,CAACa,GAAG,CAAC,IAAI,CAAClB,cAAc,CAACmB,yBAAyB,CAACC,SAAS,CAACC,GAAG,IAAG;MAC1E,IAAIA,GAAG,EAAE;QACP,IAAI,CAACC,QAAQ,EAAE;MACjB;IACF,CAAC,CAAC,CAAC;IAGHC,OAAO,CAACC,GAAG,CAAC,IAAI,CAAClC,gBAAgB,CAAC;IAClC,MAAMmC,IAAI,GAAG,IAAIb,IAAI,EAAE;IAEvB,MAAMc,OAAO,GAAI,IAAI,CAACpC,gBAAgB,CAACqC,WAAW,CAAC,IAAI,CAACrC,gBAAgB,CAACsC,WAAW,EAAE,GAAG,EAAE,CAAE;IAC7F,IAAI,CAACtC,gBAAgB,GAAG,IAAIsB,IAAI,CAACc,OAAO,CAAC;IACzC,IAAI,CAAC1B,cAAc,CAAC6B,mBAAmB,CAAC,CAAC,CAAC;IAC1C,IAAI,CAACC,OAAO,GAAG,IAAI,CAAC9B,cAAc,CAAC+B,YAAY;IAE/C,IAAI,CAAC1B,IAAI,CAACa,GAAG,CAAC,IAAI,CAACnB,cAAc,CAACiC,UAAU,CAACZ,SAAS,CAACC,GAAG,IAAG;MAC3D,IAAI,CAAC3D,QAAQ,GAAG2D,GAAG,CAACY,MAAM;IAC5B,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC5B,IAAI,CAACa,GAAG,CAAC,IAAI,CAAClB,cAAc,CAACkC,gCAAgC,EAAE,CAACd,SAAS,CAACe,aAAa,IAAG;MAC7F,IAAI,CAACA,aAAa,CAACC,KAAK,EAAE;QACxB,IAAI,CAACC,8BAA8B,EAAE;MACvC;IACF,CAAC,CAAC,CAAC;IAEH,IAAI,CAAChC,IAAI,CAACa,GAAG,CAAC,IAAI,CAAClB,cAAc,CAACsC,0BAA0B,CAAC,IAAI,CAACrC,WAAW,CAACsC,SAAS,EAAG,CAAC,CAACnB,SAAS,CAACC,GAAG,IAAG;MAC1GE,OAAO,CAACC,GAAG,CAACH,GAAG,CAAC;MAChB,IAAI,CAACmB,gCAAgC,CAACnB,GAAG,CAAC;MAC1C,IAAI,CAACoB,uBAAuB,EAAE;MAC9B,IAAI,CAAC/B,SAAS,GAAG,KAAK;IACxB,CAAC,CAAC,CAAC;IAEH;IACA;EACF;EAEAgC,WAAWA,CAAA;IACT,IAAI,CAACrC,IAAI,CAACsC,WAAW,EAAE;IACvB,IAAI,CAAC3C,cAAc,CAAC4C,4BAA4B,CAAC,KAAK,CAAC;EACzD;EAEAtB,QAAQA,CAAA;IACN,IAAI,CAACb,SAAS,GAAG,IAAI;IAErB,IAAIoC,aAAa,GAAG,EAAE;IACtB,KAAK,MAAMC,WAAW,IAAI,IAAI,CAAC3D,IAAI,CAACK,QAAQ,EAAE;MAC5C,IAAI,IAAI,CAACL,IAAI,CAACK,QAAQ,CAACuD,cAAc,CAACD,WAAW,CAAC,EAAE;QAClD,MAAME,OAAO,GAAG,IAAI,CAAC7D,IAAI,CAACK,QAAQ,CAACsD,WAAW,CAAC;QAC/C,IAAIE,OAAO,CAACC,OAAO,EAAE;UACnBJ,aAAa,IAAI,GAAGC,WAAW,IAAI;QACrC;MACF;IACF;IAEA,IAAII,aAAa,GAAG,+CAA+C;IACnE,IAAIL,aAAa,KAAK,EAAE,EAAE;MACxBA,aAAa,GAAGA,aAAa,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAE5C,IAAIN,aAAa,KAAK,EAAE,EAAE;QACxBK,aAAa,IAAI,2CAA2CL,aAAa,EAAE;MAC7E;IACF;IACA,IAAI,CAAC,IAAI,CAAC1D,IAAI,CAACiE,KAAK,EAAE;MACpB,IAAI,CAAClD,KAAK,CAACmD,mBAAmB,CAAC;QAC7BC,QAAQ,EAAE,MAAM;QAChBC,OAAO,EAAE,EAAE;QACXC,MAAM,EAAEN;OACT,CAAC;MACF;IACF;IACA,IAAI,CAACnC,WAAW,GAAG,KAAK;IACxB,MAAM0C,+BAA+B,GAA4B;MAC/DC,SAAS,EAAE,IAAI,CAACvE,IAAI,CAACwE,KAAK,CAACD,SAAS;MACpCE,QAAQ,EAAE,IAAI,CAACzE,IAAI,CAACwE,KAAK,CAACC,QAAQ;MAClCC,aAAa,EAAE,IAAI,CAAC1E,IAAI,CAACwE,KAAK,CAACE,aAAa;MAC5CC,aAAa,EAAE;QAAElI,IAAI,EAAE,IAAI,CAACuD,IAAI,CAACwE,KAAK,CAACG,aAAa,CAAClI,IAAI;QAAEmI,OAAO,EAAE,IAAI,CAAC5E,IAAI,CAACwE,KAAK,CAACG,aAAa,CAACrI;MAAK,CAAE;MACzGuI,gBAAgB,EAAE;QAAEpI,IAAI,EAAE,IAAI,CAACuD,IAAI,CAACwE,KAAK,CAACK,gBAAgB,CAACpI,IAAI;QAAEmI,OAAO,EAAE,IAAI,CAAC5E,IAAI,CAACwE,KAAK,CAACK,gBAAgB,CAACvI;MAAK,CAAE;MAClH8D,eAAe,EAAE,IAAI,CAACJ,IAAI,CAACwE,KAAK,CAACpE,eAAe;MAChD0E,QAAQ,EAAE,IAAI,CAAC9E,IAAI,CAACwE,KAAK,CAACM,QAAQ,EAAEC,GAAG,CAAC,CAAC,CAAC;MAC1CC,KAAK,EAAE,IAAI,CAACpE,cAAc,CAACqE,wCAAwC,CAAC,IAAI,CAACvD,kBAAkB,CAAC;MAC5FwD,KAAK,EAAE,IAAI,CAAClF,IAAI,CAACwE,KAAK,CAACU,KAAK;MAC5BC,KAAK,EAAE,IAAI,CAACnF,IAAI,CAACwE,KAAK,CAACW,KAAK;MAC5BC,SAAS,EAAE,IAAI,CAACpF,IAAI,CAACwE,KAAK,CAACY,SAAS,CAACC,IAAI;MACzCC,KAAK,EAAE,IAAI,CAACtF,IAAI,CAACwE,KAAK,CAACc;KACxB;IACD,IAAI,CAACpE,IAAI,CAACa,GAAG,CAAC,IAAI,CAAClB,cAAc,CAAC0E,gCAAgC,CAACjB,+BAA+B,CAAC,CAACrC,SAAS,CAACC,GAAG,IAAG;MAClH,IAAIA,GAAG,EAAE;QACP,IAAI,CAACnB,KAAK,CAACmD,mBAAmB,CAAC;UAAEC,QAAQ,EAAE,SAAS;UAAEC,OAAO,EAAE,EAAE;UAAEC,MAAM,EAAE;QAAkC,CAAE,CAAC;QAChH;QACA,IAAI,CAAC3D,MAAM,CAAC8E,aAAa,CAAC,oBAAoB,EAAE;UAAEC,UAAU,EAAE;QAAI,CAAE,CAAC;MACvE;IACF,CAAC,CAAC,CAAC;EAEL;EAEApC,gCAAgCA,CAACqC,kBAA2C;IAE1E,IAAI,IAAI,CAACC,WAAW,CAACD,kBAAkB,CAACV,KAAK,CAAC,EAAE;MAC9C,IAAI,CAACtD,kBAAkB,GAAG,IAAI,CAACd,cAAc,CAACgF,eAAe,CAACF,kBAAkB,CAACV,KAAK,CAAC;IACzF,CAAC,MAAM;MACL,IAAI,CAACtD,kBAAkB,GAAG,IAAI,CAACd,cAAc,CAACgF,eAAe,CAAC,IAAI,CAACzF,gBAAgB,CAAC0F,QAAQ,EAAE,CAAC;IACjG;IAEA,IAAI,CAAC,IAAI,CAACjF,cAAc,CAACkF,eAAe,CAACJ,kBAAkB,CAACN,SAAS,CAAC,EAAE;MACtE,IAAI,CAAC7E,iBAAiB,GAAG,IAAI,CAACD,UAAU,CAACyF,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACX,IAAI,KAAKK,kBAAkB,CAACN,SAAS,CAAC,CAAC,CAAC,CAAC;IACpG;IAEA,IAAI,CAACpF,IAAI,GAAG,IAAItE,gBAAgB,CAAC;MAC/BsJ,KAAK,EAAE,IAAIvJ,kBAAkB,CAAC,IAAI,CAACiG,kBAAkB,EAAE;QACrDuE,UAAU,EAAE,CAACtK,UAAU,CAACuK,QAAQ;OACjC,CAAC;MACFxB,aAAa,EAAE,IAAIjJ,kBAAkB,CAACiK,kBAAkB,CAAChB,aAAa,EAAE;QACtEuB,UAAU,EAAE,CAACtK,UAAU,CAACuK,QAAQ;OACjC,CAAC;MACF3B,SAAS,EAAE,IAAI9I,kBAAkB,CAACiK,kBAAkB,CAACnB,SAAS,EAAE;QAC9D0B,UAAU,EAAE,CAACtK,UAAU,CAACuK,QAAQ;OACjC,CAAC;MACFzB,QAAQ,EAAE,IAAIhJ,kBAAkB,CAACiK,kBAAkB,CAACjB,QAAQ,EAAE;QAC5DwB,UAAU,EAAE,CAACtK,UAAU,CAACuK,QAAQ;OACjC,CAAC;MACFhB,KAAK,EAAE,IAAIzJ,kBAAkB,CAACiK,kBAAkB,CAACR,KAAK,EAAE;QACtDe,UAAU,EAAE,CAACtK,UAAU,CAACuK,QAAQ,EAAEvK,UAAU,CAACuJ,KAAK;OACnD,CAAC;MACFE,SAAS,EAAE,IAAI3J,kBAAkB,CAAC,IAAI,CAAC8E,iBAAiB,EAAE;QACxD0F,UAAU,EAAE,CAACtK,UAAU,CAACuK,QAAQ;OACjC,CAAC;MACFf,KAAK,EAAE,IAAI1J,kBAAkB,CAACiK,kBAAkB,CAACP,KAAK,EAAE;QACtDc,UAAU,EAAE,CAACtK,UAAU,CAACuK,QAAQ;OACjC,CAAC;MACFZ,KAAK,EAAE,IAAI7J,kBAAkB,CAACiK,kBAAkB,CAACJ,KAAK,IAAI,GAAG,EAAE;QAC7DW,UAAU,EAAE,CAACtK,UAAU,CAACuK,QAAQ;OACjC,CAAC;MACF9F,eAAe,EAAE,IAAI5E,gBAAgB,CAAC,EAAE,EAAE;QACxCyK,UAAU,EAAE,CAACtK,UAAU,CAACuK,QAAQ;OACjC,CAAC;MACFpB,QAAQ,EAAE,IAAIrJ,kBAAkB,CAC9B,IAAI,CAACyE,SAAS,CAACiG,IAAI,CAACH,EAAE,IAAIA,EAAE,CAACjB,GAAG,CAACqB,QAAQ,CAACV,kBAAkB,CAACZ,QAAS,CAAC,CAAC,EAAE;QAC1EmB,UAAU,EAAE,CAACtK,UAAU,CAACuK,QAAQ;OACjC,CAAC;MACFvB,aAAa,EAAE,IAAIlJ,kBAAkB,CACnC,CAAC,IAAI,CAACmF,cAAc,CAACkF,eAAe,CAACJ,kBAAkB,CAACf,aAAa,CAAC,GACpE,IAAI,CAAC1E,SAAS,CAAC8F,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACvJ,IAAI,KAAKiJ,kBAAkB,CAACf,aAAa,CAAClI,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE;QAC5FwJ,UAAU,EAAE,CAACtK,UAAU,CAACuK,QAAQ;OACjC,CAAC;MACFrB,gBAAgB,EAAE,IAAIpJ,kBAAkB,CACtC,CAAC,IAAI,CAACmF,cAAc,CAACkF,eAAe,CAACJ,kBAAkB,CAACb,gBAAgB,CAAC,GACvE,IAAI,CAAC5E,SAAS,CAAC8F,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACvJ,IAAI,KAAKiJ,kBAAkB,CAACb,gBAAgB,CAACpI,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,EAAE;QAC/FwJ,UAAU,EAAE,CAACtK,UAAU,CAACuK,QAAQ;OACjC;KACF,CAAC;IAEF,IAAI,CAAC,IAAI,CAACtF,cAAc,CAACkF,eAAe,CAACJ,kBAAkB,CAACtF,eAAe,CAAC,EAAE;MAC5EsF,kBAAkB,CAACtF,eAAe,CAACiG,OAAO,CAACC,OAAO,IAAG;QACnD,IAAI,CAAC9I,iBAAiB,CAAC8I,OAAO,CAACC,MAAM,CAAC;MACxC,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAAC5E,oBAAoB,CAAC0E,OAAO,CAACC,OAAO,IAAG;QAC1C,IAAI,CAAClG,eAAe,CAACoG,IAAI,CAAC,IAAI,CAAC7F,EAAE,CAAC8F,KAAK,CAACH,OAAO,CAAC,CAAC;MACnD,CAAC,CAAC;MAEF,IAAI,IAAI,CAAC3E,oBAAoB,CAAC+E,MAAM,KAAK,CAAC,EAAE;QAC1C,IAAI,CAAClJ,iBAAiB,CAAC,EAAE,CAAC;MAC5B;IACF;EAEF;EAEAgC,mBAAmBA,CAACmH,KAAU;IAC5BvE,OAAO,CAACC,GAAG,CAACsE,KAAK,CAAC;IAClB,IAAIC,CAAC,GAAG,IAAInF,IAAI,CAACA,IAAI,CAACoF,KAAK,CAACF,KAAK,CAAC,CAAC;IACnC,IAAI,CAACjF,kBAAkB,GAAG,IAAI,CAACd,cAAc,CAACgF,eAAe,CAACe,KAAK,CAAC;EACtE;EAEAG,SAASA,CAACC,KAAa;IACrB,OAAS,IAAI,CAAC/G,IAAI,CAAC6B,GAAG,CAACkF,KAAK,CAAC,EAAEjD,OAAO,IAAI,IAAI,CAAC9D,IAAI,CAAC6B,GAAG,CAACkF,KAAK,CAAC,EAAEC,OAAO,IAAM,IAAI,CAAC1F,SAAS,IAAI,IAAI,CAACtB,IAAI,CAAC6B,GAAG,CAACkF,KAAK,CAAC,EAAEjD,OAAQ,IAAK,IAAI,CAAC9D,IAAI,CAACwE,KAAK,CAACyC,QAAQ,KAAK,IAAI,CAACjH,IAAI,CAACwE,KAAK,CAAC0C,OAAO,IAAI,IAAI,CAAClH,IAAI,CAAC6B,GAAG,CAACkF,KAAK,CAAC,EAAEC,OAAQ;EAC1N;EAEA9I,cAAcA,CAACyI,KAAU;IACvBQ,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,gBAAgB,GAAGT,KAAK,CAACnC,KAAK;IACrC,CAAC,EAAE,GAAG,CAAC;EACT;EAEA3F,eAAeA,CAAC8H,KAAU;IACxBQ,UAAU,CAAC,MAAK;MACd,IAAI,CAAC3F,eAAe,GAAGmF,KAAK,CAACnC,KAAK;IACpC,CAAC,EAAE,GAAG,CAAC;EACT;EAEApF,gBAAgBA,CAACuH,KAAU;IACzBQ,UAAU,CAAC,MAAK;MACd,IAAI,CAACE,gBAAgB,GAAGV,KAAK,CAACnC,KAAK;IACrC,CAAC,EAAE,GAAG,CAAC;EACT;EAEA1E,iBAAiBA,CAAC6G,KAAU;IAC1BQ,UAAU,CAAC,MAAK;MACd,IAAI,CAAC5G,iBAAiB,GAAGoG,KAAK,CAACnC,KAAK;MACpCpC,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC9B,iBAAiB,CAAC;IACrC,CAAC,EAAE,GAAG,CAAC;EACT;EAEA+C,uBAAuBA,CAAA;IACrB,IAAI,CAACpC,IAAI,CAACa,GAAG,CAAC,IAAI,CAAC/B,IAAI,CAACsH,YAAY,CAACrF,SAAS,CAACsF,GAAG,IAAG;MACnD,IAAI,IAAI,CAACvH,IAAI,CAACwH,KAAK,EAAE;QACnB,IAAI,CAAC5F,WAAW,GAAG,IAAI;MACzB;IACF,CAAC,CAAC,CAAC;EACL;EAEA5E,YAAYA,CAAC+J,KAAa;IACxB,IAAI,CAAClG,cAAc,CAAC4G,YAAY,CAAC,CAAC,EAAE,IAAI,CAACzH,IAAI,EAAE,oBAAoB,CAAC;IACpE,OAAS,IAAI,CAACA,IAAI,CAAC6B,GAAG,CAACkF,KAAK,CAAC,EAAEjD,OAAO,IAAI,IAAI,CAAC9D,IAAI,CAAC6B,GAAG,CAACkF,KAAK,CAAC,EAAEC,OAAO,IAAM,IAAI,CAAC1F,SAAS,IAAI,IAAI,CAACtB,IAAI,CAAC6B,GAAG,CAACkF,KAAK,CAAC,EAAEjD,OAAQ;EAC/H;EAEA4D,2BAA2BA,CAACC,CAAS;IACnC,IAAI,CAAC9G,cAAc,CAAC4G,YAAY,CAAC,CAAC,EAAE,IAAI,CAACzH,IAAI,EAAE,oBAAoB,CAAC;IACpE,OAAS,IAAI,CAACsB,SAAS,IAAuB,IAAI,CAACtB,IAAI,CAAC6B,GAAG,CAAC,iBAAiB,CAAE,CAACxB,QAAQ,CAACsH,CAAC,CAAC,CAAC7D,OAAO;EACrG;EAEAtG,iBAAiBA,CAACoK,WAAmB;IACnC,IAAI,IAAI,CAACxH,eAAe,CAACsG,MAAM,GAAG,CAAC,EAAE;MACnC,MAAMD,KAAK,GAAG,IAAI/K,gBAAgB,CAAC;QACjC6K,MAAM,EAAE,IAAI9K,kBAAkB,CAACmM,WAAW,EAAEjM,UAAU,CAACuK,QAAQ;OAChE,CAAC;MACF,IAAI,CAAC9F,eAAe,CAACoG,IAAI,CAACC,KAAK,CAAC;IAClC;EACF;EAEA3I,oBAAoBA,CAACD,KAAa;IAChC,IAAI,CAACuC,eAAe,CAACyH,QAAQ,CAAChK,KAAK,CAAC;EACtC;EAEA8H,WAAWA,CAACmC,SAAiB;IAC3B,MAAMxF,IAAI,GAAG,IAAIb,IAAI,CAACqG,SAAS,CAAC;IAChC,IAAIC,KAAK,CAACzF,IAAI,CAAC0F,OAAO,EAAE,CAAC,IAAI1F,IAAI,CAACG,WAAW,EAAE,GAAG,IAAI,EAAE;MACtD,OAAO,KAAK;IACd,CAAC,MAAM;MACL,OAAO,IAAI;IACb;EACF;EAEAwF,gBAAgBA,CAAC7C,SAAiB;IAChC,OAAO,IAAI,CAACxE,cAAc,CAACqH,gBAAgB,CAAC7C,SAAS,CAAC;EACxD;EAEQlC,8BAA8BA,CAAA;IACpC,IAAI,CAACtC,cAAc,CAACsH,cAAc,EAAE,CAACjG,SAAS,CAAEC,GAAQ,IAAI;MAC1D,IAAIA,GAAG,EAAE;QACPE,OAAO,CAACC,GAAG,CAACH,GAAG,CAAC;QAChB,IAAI,CAAC3B,iBAAiB,GAAG,IAAI,CAACD,UAAU,CAACyF,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACmC,GAAG,KAAKjG,GAAG,CAACkG,YAAY,CAAC,CAAC,CAAC,CAAC;MACvF;IACF,CAAC,CAAC;EACJ;EAEAC,aAAaA,CAAA;IACX,IAAI,IAAI,CAACzG,WAAW,EAAE;MACpB,OAAO,IAAIhG,UAAU,CAAE0M,QAAa,IAAI;QACtC,IAAI,CAACtH,mBAAmB,CAACkG,OAAO,CAAC;UAC/BqB,MAAM,EAAE,EAAE;UACVC,GAAG,EAAE,uBAAuB;UAC5BC,OAAO,EAAE,sEAAsE;UAC/EC,MAAM,EAAEA,CAAA,KAAK;YACXJ,QAAQ,CAACK,IAAI,CAAC,IAAI,CAAC;YACnBL,QAAQ,CAACM,QAAQ,EAAE;UACrB,CAAC;UACDC,MAAM,EAAEA,CAAA,KAAK;YACXP,QAAQ,CAACK,IAAI,CAAC,KAAK,CAAC;YACpBL,QAAQ,CAACM,QAAQ,EAAE;UACrB;SACD,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,OAAQ,IAAI;IACd;EACF;EAAC,QAAAE,CAAA,G;qBA1TUtI,oBAAoB,EAAA1E,EAAA,CAAAiN,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAnN,EAAA,CAAAiN,iBAAA,CAAAG,EAAA,CAAAC,kBAAA,GAAArN,EAAA,CAAAiN,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAvN,EAAA,CAAAiN,iBAAA,CAAAO,EAAA,CAAAC,yBAAA,GAAAzN,EAAA,CAAAiN,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAA3N,EAAA,CAAAiN,iBAAA,CAAAW,EAAA,CAAAC,YAAA,GAAA7N,EAAA,CAAAiN,iBAAA,CAAAa,EAAA,CAAAC,mBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAApBtJ,oBAAoB;IAAAuJ,SAAA;IAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;QClBjCpO,EAAA,CAAAI,SAAA,aAA2B;QAC3BJ,EAAA,CAAAC,cAAA,aAA0B;QAO1BD,EAAA,CAAAa,UAAA,IAAAyN,4CAAA,4BAAiC;QAgMjCtO,EAAA,CAAAG,YAAA,EAAM;QAEFH,EADJ,CAAAC,cAAA,aAA+B,gBACsC;QAACD,EAAA,CAAAI,SAAA,aAA+C;QAACJ,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAC/HH,EAAA,CAAAC,cAAA,gBAAiE;QAAjDD,EAAA,CAAAoB,UAAA,mBAAAmN,sDAAA;UAAA,OAASF,GAAA,CAAAhI,QAAA,EAAU;QAAA,EAAC;QAA6BrG,EAAA,CAAAE,MAAA,YAAK;QAAAF,EAAA,CAAAI,SAAA,aAA2C;QACrHJ,EADqH,CAAAG,YAAA,EAAS,EACxH;;;QApMSH,EAAA,CAAAK,SAAA,GAAgB;QAAhBL,EAAA,CAAAM,UAAA,UAAA+N,GAAA,CAAA5I,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}