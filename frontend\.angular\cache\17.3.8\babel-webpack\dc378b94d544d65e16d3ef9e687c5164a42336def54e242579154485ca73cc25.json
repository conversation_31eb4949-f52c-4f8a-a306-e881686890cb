{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport { TeacherStatus } from '../models/teacher.model';\nimport { UserAvailabilityType } from '../models/user.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nconst BACKEND_URL = environment.apiUrl + \"/Teacher/\";\nconst BACKEND_LMS_TEACHER_URL = environment.apiUrl + \"/LMS/\";\nconst BACKEND_LMS_URL = environment.apiUrl + \"/LMS/\";\nexport let TeacherApplicationService = /*#__PURE__*/(() => {\n  class TeacherApplicationService {\n    constructor(http, router) {\n      this.http = http;\n      this.router = router;\n      this.step$ = new BehaviorSubject(\"Create your account\");\n      this.step = this.step$.asObservable();\n      this.currentStepIndex$ = new BehaviorSubject(0);\n      this.currentStepIndex = this.currentStepIndex$.asObservable();\n      this.submitOnMenuClickListener$ = new BehaviorSubject(false);\n      this.submitOnMenuClickListener = this.submitOnMenuClickListener$.asObservable();\n      this.updateAvailabilityListener$ = new BehaviorSubject(true);\n      this.updateAvailabilityListener = this.updateAvailabilityListener$.asObservable();\n      this.updateAvailabilityForSchedulerListener$ = new BehaviorSubject(false);\n      this.updateAvailabilityForSchedulerListener = this.updateAvailabilityForSchedulerListener$.asObservable();\n      this.showWithdrawConfirmationDialog$ = new BehaviorSubject(false);\n      this.showWithdrawConfirmationDialog = this.showWithdrawConfirmationDialog$.asObservable();\n      this.showApplicationProgressMenu$ = new BehaviorSubject(true);\n      this.showApplicationProgressMenu = this.showApplicationProgressMenu$.asObservable();\n      this.shouldRetrieveTeacherStatus$ = new BehaviorSubject(false);\n      this.shouldRetrieveTeacherStatus = this.shouldRetrieveTeacherStatus$.asObservable();\n      this.compareStepvalues$ = new BehaviorSubject({});\n      this.compareStepvalues = this.compareStepvalues$.asObservable();\n      // private stepsValid$: BehaviorSubject<boolean[]> = new BehaviorSubject<boolean[]>([false, false, false, false, false, false, false,]);\n      // public readonly stepsValid: Observable<boolean[]> = this.stepsValid$.asObservable();\n      this.stepsValid = [false, false, false, false, false, true];\n      this.stepsStatuses = {\n        step1: false,\n        step2: false,\n        step3: false,\n        step4: false,\n        step5: false,\n        step6: false\n      };\n      this.dummyTeacher = {\n        id: \"\",\n        // username: \"\",\n        preffered_name: \"\",\n        // password: \"\",\n        first_name: \"\",\n        last_name: \"\",\n        city: \"\",\n        country: {\n          name: \"\",\n          flagUrl: \"\"\n        },\n        country_origin: {\n          name: \"\",\n          flagUrl: \"\"\n        },\n        country_residency: {\n          name: \"\",\n          flagUrl: \"\"\n        },\n        native_language: \"\",\n        native_languages: [],\n        birth: '' + new Date(),\n        timezone: \"\",\n        email: \"\",\n        phone: \"\",\n        skype: \"\",\n        education: [],\n        language_speak: [],\n        language_teach: [],\n        teaching_methods: \"\",\n        years_experience: 0,\n        cv: \"\",\n        photo: \"\",\n        hours_per_week: \"\",\n        status: \"\",\n        availability: undefined,\n        date_applied: new Date()\n      };\n      this.teacher = {\n        id: \"\",\n        // username: \"ibekiaris\",\n        preffered_name: \"iraklis\",\n        // password: \"e\",\n        first_name: \"iraklis\",\n        last_name: \"bekiaris\",\n        city: \"serres\",\n        country: {\n          name: \"Wales\",\n          flagUrl: \"\"\n        },\n        country_origin: {\n          name: \"\",\n          flagUrl: \"\"\n        },\n        country_residency: {\n          name: \"\",\n          flagUrl: \"\"\n        },\n        native_language: \"Afar\",\n        native_languages: [],\n        birth: '' + new Date(),\n        timezone: \"(UTC-12:00) International Date Line West\",\n        email: \"<EMAIL>\",\n        phone: \"00306988582965\",\n        skype: \"iraklis.bekiaris\",\n        education: [{\n          name: \"Degree 1\",\n          institute: \"inst\",\n          fileUploads: [{\n            name: 'path1'\n          }]\n        }],\n        language_speak: [{\n          language: {\n            code: 'el',\n            nativeName: 'greek',\n            name: \"greek\"\n          },\n          level: \"C1\"\n        }, {\n          language: {\n            code: 'el',\n            nativeName: 'greek',\n            name: \"greek\"\n          },\n          level: \"C1\"\n        }],\n        language_teach: [{\n          language: {\n            code: 'el',\n            nativeName: 'greek',\n            name: \"greek\"\n          },\n          level: \"C1\"\n        }, {\n          language: {\n            code: 'el',\n            nativeName: 'greek',\n            name: \"greek\"\n          },\n          level: \"C1\"\n        }],\n        teaching_methods: \"On the other hand, we denounce with righteous indignation and dislike men who are so beguiled and demoralized by the charms of pleasure of the moment, so blinded by desire, that they cannot foresee the pain and trouble that are bound to ensue; and equal blame belongs to those who fail in their duty through weakness of will, which is the same as saying through shrinking from toil and pain. These cases are perfectly simple and easy to distinguish. In a free hour, when our power of choice is untrammelled and when nothing prevents our being able to do what we like best, every pleasure is to be welcomed and every pain avoided. But in certain circumstances and owing to the claims of duty or the obligations of business it will frequently occur that pleasures have to be repudiated and annoyances accepted. The wise man therefore always holds in thes\",\n        years_experience: 6,\n        cv: \"CV\",\n        photo: \"Photo\",\n        hours_per_week: \"5-10\",\n        status: \"Pending\",\n        availability: {\n          id: \"availability_2\",\n          type: UserAvailabilityType.AVAILABLE,\n          mon: [{\n            from: \"10:30\",\n            to: \"14:00\"\n          }, {\n            from: \"15:00\",\n            to: \"18:00\"\n          }, {\n            from: \"19:00\",\n            to: \"20:30\"\n          }],\n          tue: [{\n            from: \"10:30\",\n            to: \"14:00\"\n          }, {\n            from: \"15:00\",\n            to: \"18:00\"\n          }, {\n            from: \"19:00\",\n            to: \"20:30\"\n          }],\n          wed: [{\n            from: \"10:00\",\n            to: \"18:00\"\n          }],\n          thu: [{\n            from: \"12:30\",\n            to: \"14:30\"\n          }, {\n            from: \"16:00\",\n            to: \"20:00\"\n          }],\n          fri: [{\n            from: \"10:30\",\n            to: \"14:00\"\n          }, {\n            from: \"15:00\",\n            to: \"18:00\"\n          }],\n          sat: [{\n            from: \"10:30\",\n            to: \"14:00\"\n          }],\n          sun: []\n        },\n        date_applied: new Date()\n      };\n    }\n    setStep(section) {\n      let step = '';\n      switch (section) {\n        case 'info':\n          step = 'Create your account';\n          break;\n        case 'contact':\n          step = 'Enter your contact info';\n          break;\n        case 'education':\n          step = 'Enter your educational skills';\n          break;\n        case 'experience':\n          step = 'Enter your teaching experience & skills';\n          break;\n        case 'resume':\n          step = 'Upload your CV & Photo';\n          break;\n        case 'availability':\n          step = 'Add availability';\n          break;\n        case 'review':\n          step = 'Review & Submit';\n          break;\n        case 'status':\n          step = 'status';\n          break;\n        default:\n          step = '';\n      }\n      this.step$.next(step);\n    }\n    setCurrentStepIndex(index) {\n      this.currentStepIndex$.next(index);\n    }\n    setStepValid(index, form, id) {\n      if (!form.invalid) {\n        this.stepsValid[index] = true;\n      } else {\n        this.stepsValid[index] = false;\n      }\n    }\n    setStepValidWithBoolean(index, isNotValid, id) {\n      if (!isNotValid) {\n        this.stepsValid[index] = true;\n        // document.getElementById(id)!.querySelector('img')!.style.display = 'block';;\n        // document.getElementById(id)!.classList.remove('inactive-item');\n      } else {\n        this.stepsValid[index] = false;\n        // document.getElementById(id)!.querySelector('img')!.style.display = 'none';;\n        // document.getElementById(id)!.classList.add('inactive-item');\n      }\n    }\n    setSubmitOnMenuClickListener(submitted) {\n      this.submitOnMenuClickListener$.next(submitted);\n    }\n    setUpdateAvailabilityClickListener(update) {\n      this.updateAvailabilityListener$.next(update);\n    }\n    setUpdateAvailabilityForSchedulerClickListener(update) {\n      this.updateAvailabilityForSchedulerListener$.next(update);\n    }\n    setShowWithdrawConfirmationDialog(update) {\n      this.showWithdrawConfirmationDialog$.next(update);\n    }\n    setShowApplicationProgressMenu(update) {\n      this.showApplicationProgressMenu$.next(update);\n    }\n    setShouldRetrieveTeacherStatus(update) {\n      this.shouldRetrieveTeacherStatus$.next(update);\n    }\n    setCompareStepvalues(update) {\n      this.compareStepvalues$.next(update);\n    }\n    setStepStatuses(stepsStatuses) {\n      this.stepsStatuses = stepsStatuses;\n    }\n    registerTeacher(teacher) {\n      this.http.post(BACKEND_LMS_TEACHER_URL + \"SubmitTeacherApplication\", teacher).subscribe(res => {\n        console.log(\"[+] Success Registration: \");\n        this.router.navigateByUrl('/teacher/status', {\n          replaceUrl: true\n        });\n      }, error => {\n        console.error(error);\n      });\n    }\n    updateAvailability(availability) {\n      return this.http.post(BACKEND_URL + \"UpdateTeacherAvailability\", availability);\n    }\n    updateAPITeacherApplicationStep1(form) {\n      return this.http.post(BACKEND_LMS_TEACHER_URL + 'TeacherApplcationSaveStep1', form);\n    }\n    getTeacherApplicationStep1(teacherId) {\n      return this.http.get(BACKEND_LMS_TEACHER_URL + 'GetTeacherApplicationStep1?teacherId=' + teacherId);\n    }\n    getTeacherApplicationStep2() {\n      return this.http.get(BACKEND_LMS_TEACHER_URL + 'GetTeacherApplicationStep2');\n    }\n    updateTeacherApplicationStep2(form) {\n      return this.http.post(BACKEND_LMS_TEACHER_URL + 'TeacherApplcationSaveStep2', form);\n    }\n    getTeacherApplicationStep3() {\n      return this.http.get(BACKEND_LMS_TEACHER_URL + 'GetTeacherApplicationStep3');\n    }\n    updateAPITeacherApplicationStep3(form) {\n      return this.http.post(BACKEND_LMS_TEACHER_URL + 'TeacherApplcationSaveStep3', form);\n    }\n    getTeacherApplicationStep4() {\n      return this.http.get(BACKEND_LMS_TEACHER_URL + 'GetTeacherApplicationStep4');\n    }\n    updateAPITeacherApplicationStep4(form) {\n      return this.http.post(BACKEND_LMS_TEACHER_URL + 'TeacherApplcationSaveStep4', form);\n    }\n    getTeacherApplicationStep5() {\n      return this.http.get(BACKEND_LMS_TEACHER_URL + 'GetTeacherApplicationStep5');\n    }\n    updateAPITeacherApplicationStep5(form) {\n      return this.http.post(BACKEND_LMS_TEACHER_URL + 'TeacherApplcationSaveStep5', form);\n    }\n    getTeacherApplicationStep6() {\n      return this.http.get(BACKEND_LMS_TEACHER_URL + 'GetTeacherApplicationStep6');\n    }\n    getTeacherApplicationStepsStatus() {\n      return this.http.get(BACKEND_LMS_TEACHER_URL + 'GetApplicationStepsStatus');\n    }\n    getLingoTeachingLanguages() {\n      return this.http.get(BACKEND_LMS_TEACHER_URL + 'GetLingoTeachingLanguages');\n    }\n    deleteTeacherApplicationFiles(filename) {\n      return this.http.post(BACKEND_LMS_TEACHER_URL + 'DeleteTeacherApplicationFiles?fileName=' + filename, {});\n    }\n    withdrawnTeacherApplication() {\n      return this.http.post(BACKEND_LMS_TEACHER_URL + 'WithdrawnTeacherApplication', {});\n    }\n    restartTeacherApplication() {\n      return this.http.post(BACKEND_LMS_TEACHER_URL + 'RestartTeacherApplication', {});\n    }\n    updateTeacherAvailability(params) {\n      return this.http.post(BACKEND_LMS_URL + 'UpdateTeacherAvailability', params);\n    }\n    getStepsTitle(stepIndex) {\n      const stepTitles = ['Create your account', 'Enter your education info', 'Enter your teaching experience & skills', 'Upload your Resume', 'Add Availability', 'General Info'];\n      return stepTitles[stepIndex] || '';\n    }\n    getTeacherStatusNavigationUrl(status) {\n      let navigateUrl = '/dashboard';\n      switch (status.status.toLowerCase()) {\n        case TeacherStatus.CONSIDERATION.toLowerCase():\n        case TeacherStatus.NO_LONGER_CONSIDERED.toLowerCase():\n        case TeacherStatus.REJECTED.toLowerCase():\n        case TeacherStatus.NEW.toLowerCase():\n        case TeacherStatus.RECEIVED.toLowerCase():\n        case TeacherStatus.INCOMPLETE.toLowerCase():\n        case TeacherStatus.WITHDRAWN.toLowerCase():\n          navigateUrl = '/teacher/status';\n          break;\n      }\n      return navigateUrl;\n    }\n    isTeacherStatusApproved(status) {\n      if (status.toLowerCase() === TeacherStatus.APPROVED.toLowerCase()) {\n        return false;\n      }\n      return true;\n    }\n    static #_ = this.ɵfac = function TeacherApplicationService_Factory(t) {\n      return new (t || TeacherApplicationService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: TeacherApplicationService,\n      factory: TeacherApplicationService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return TeacherApplicationService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}