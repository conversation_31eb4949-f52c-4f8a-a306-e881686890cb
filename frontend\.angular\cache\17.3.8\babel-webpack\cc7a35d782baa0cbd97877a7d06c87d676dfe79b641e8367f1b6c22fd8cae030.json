{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nimport * as i2 from \"@angular/common\";\nconst _c0 = [\"*\"];\nconst _c1 = a0 => ({\n  \"background-image\": a0\n});\nexport class GAccordionComponent {\n  constructor(generalService) {\n    this.generalService = generalService;\n    this.title = '';\n    this.elementTitle = '';\n    this.isOpen = true;\n    this.backgroundImage = '';\n  }\n  ngOnInit() {}\n  toggleSection(sectionElement, sectionArrowImgSrc) {\n    this.generalService.toggleSection(sectionElement, sectionArrowImgSrc, true);\n  }\n  static #_ = this.ɵfac = function GAccordionComponent_Factory(t) {\n    return new (t || GAccordionComponent)(i0.ɵɵdirectiveInject(i1.GeneralService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: GAccordionComponent,\n    selectors: [[\"g-accordion\"]],\n    inputs: {\n      title: \"title\",\n      elementTitle: \"elementTitle\",\n      isOpen: \"isOpen\",\n      backgroundImage: \"backgroundImage\"\n    },\n    ngContentSelectors: _c0,\n    decls: 11,\n    vars: 7,\n    consts: [[\"arrowSrc\", \"\"], [\"aSection\", \"\"], [1, \"section\"], [1, \"section-title\", \"gradient-bg\", \"w-100\", 3, \"ngStyle\"], [1, \"text-white\", \"font-3xl\"], [1, \"toggle-section\", 3, \"click\"], [\"src\", \"/assets/icons/toogle-section.svg\", 1, \"section-arrow\"], [1, \"section-content\", 2, \"overflow\", \"inherit\"], [3, \"hidden\"]],\n    template: function GAccordionComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"span\", 4);\n        i0.ɵɵtext(3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 5);\n        i0.ɵɵlistener(\"click\", function GAccordionComponent_Template_div_click_4_listener() {\n          i0.ɵɵrestoreView(_r1);\n          const arrowSrc_r2 = i0.ɵɵreference(6);\n          const aSection_r3 = i0.ɵɵreference(8);\n          return i0.ɵɵresetView(ctx.toggleSection(aSection_r3, arrowSrc_r2));\n        });\n        i0.ɵɵelement(5, \"img\", 6, 0);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"div\", 7, 1)(9, \"div\", 8);\n        i0.ɵɵprojection(10);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(5, _c1, \"url(\" + ctx.backgroundImage + \")\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.title);\n        i0.ɵɵadvance(4);\n        i0.ɵɵattribute(\"id\", ctx.elementTitle)(\"open\", ctx.isOpen);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"hidden\", !ctx.isOpen);\n      }\n    },\n    dependencies: [i2.NgStyle],\n    styles: [\".section[_ngcontent-%COMP%] {\\n  background-color: var(--white);\\n  border-top-left-radius: 28px;\\n  border-top-right-radius: 28px;\\n  border-bottom-left-radius: 28px;\\n  border-bottom-right-radius: 28px;\\n  box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);\\n  margin-top: 20px;\\n  margin-bottom: 20px;\\n}\\n.section[_ngcontent-%COMP%]   .section-content[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n  transition: height 0.3s ease-out;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  width: 100%;\\n  font-size: 25px;\\n  text-align: center;\\n  padding: 15px 0;\\n  box-sizing: border-box;\\n  border-radius: 28px;\\n  position: relative;\\n}\\n.section-title.gradient-bg[_ngcontent-%COMP%] {\\n  background-position: center center;\\n  border-radius: 20px !important;\\n}\\n.section-title[_ngcontent-%COMP%]   .toggle-section[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 9px;\\n  right: 10px;\\n}\\n.section-title[_ngcontent-%COMP%]   .toggle-section[_ngcontent-%COMP%]   .section-arrow[_ngcontent-%COMP%] {\\n  transform: rotate(180deg);\\n  overflow: hidden;\\n  transition-duration: 0.2s;\\n  transition-property: transform;\\n  cursor: pointer;\\n  max-height: 32px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"],\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["GAccordionComponent", "constructor", "generalService", "title", "elementTitle", "isOpen", "backgroundImage", "ngOnInit", "toggleSection", "sectionElement", "sectionArrowImgSrc", "_", "i0", "ɵɵdirectiveInject", "i1", "GeneralService", "_2", "selectors", "inputs", "ngContentSelectors", "_c0", "decls", "vars", "consts", "template", "GAccordionComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "GAccordionComponent_Template_div_click_4_listener", "ɵɵrestoreView", "_r1", "arrowSrc_r2", "ɵɵreference", "aSection_r3", "ɵɵresetView", "ɵɵelement", "ɵɵprojection", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction1", "_c1", "ɵɵtextInterpolate"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\layout\\g-accordion\\g-accordion.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\layout\\g-accordion\\g-accordion.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\n\r\n@Component({\r\n  selector: 'g-accordion',\r\n  templateUrl: './g-accordion.component.html',\r\n  styleUrls: ['./g-accordion.component.scss'],\r\n  changeDetection: ChangeDetectionStrategy.OnPush\r\n})\r\nexport class GAccordionComponent implements OnInit {\r\n  @Input() title: string = '';\r\n  @Input() elementTitle: string = '';\r\n  @Input() isOpen = true;\r\n  @Input() backgroundImage: string = '';\r\n\r\n  constructor(private generalService: GeneralService) { }\r\n\r\n  ngOnInit(): void {\r\n  }\r\n\r\n  toggleSection(sectionElement: any, sectionArrowImgSrc: any) {\r\n    this.generalService.toggleSection(sectionElement, sectionArrowImgSrc, true)\r\n  }\r\n}\r\n", "<div class=\"section\">\r\n    <div class=\"section-title gradient-bg w-100\" [ngStyle]=\"{'background-image': 'url(' + backgroundImage + ')'}\">\r\n        <span class=\"text-white font-3xl\">{{title}}</span>\r\n        <div class=\"toggle-section\" (click)=\"toggleSection(aSection, arrowSrc)\">\r\n            <img #arrowSrc src=\"/assets/icons/toogle-section.svg\" class=\"section-arrow\">\r\n        </div>\r\n    </div>\r\n    <div #aSection [attr.id]=\"elementTitle\" [attr.open]=\"isOpen\" class=\"section-content\" style=\"overflow: inherit\">\r\n        <div [hidden]=\"!isOpen\">\r\n            <ng-content></ng-content>\r\n        </div>\r\n    </div>\r\n</div>"], "mappings": ";;;;;;;AASA,OAAM,MAAOA,mBAAmB;EAM9BC,YAAoBC,cAA8B;IAA9B,KAAAA,cAAc,GAAdA,cAAc;IALzB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,MAAM,GAAG,IAAI;IACb,KAAAC,eAAe,GAAW,EAAE;EAEiB;EAEtDC,QAAQA,CAAA,GACR;EAEAC,aAAaA,CAACC,cAAmB,EAAEC,kBAAuB;IACxD,IAAI,CAACR,cAAc,CAACM,aAAa,CAACC,cAAc,EAAEC,kBAAkB,EAAE,IAAI,CAAC;EAC7E;EAAC,QAAAC,CAAA,G;qBAbUX,mBAAmB,EAAAY,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAnBhB,mBAAmB;IAAAiB,SAAA;IAAAC,MAAA;MAAAf,KAAA;MAAAC,YAAA;MAAAC,MAAA;MAAAC,eAAA;IAAA;IAAAa,kBAAA,EAAAC,GAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;QCPxBd,EAFR,CAAAgB,cAAA,aAAqB,aAC6F,cACxE;QAAAhB,EAAA,CAAAiB,MAAA,GAAS;QAAAjB,EAAA,CAAAkB,YAAA,EAAO;QAClDlB,EAAA,CAAAgB,cAAA,aAAwE;QAA5ChB,EAAA,CAAAmB,UAAA,mBAAAC,kDAAA;UAAApB,EAAA,CAAAqB,aAAA,CAAAC,GAAA;UAAA,MAAAC,WAAA,GAAAvB,EAAA,CAAAwB,WAAA;UAAA,MAAAC,WAAA,GAAAzB,EAAA,CAAAwB,WAAA;UAAA,OAAAxB,EAAA,CAAA0B,WAAA,CAASX,GAAA,CAAAnB,aAAA,CAAA6B,WAAA,EAAAF,WAAA,CAAiC;QAAA,EAAC;QACnEvB,EAAA,CAAA2B,SAAA,gBAA4E;QAEpF3B,EADI,CAAAkB,YAAA,EAAM,EACJ;QAEFlB,EADJ,CAAAgB,cAAA,gBAA+G,aACnF;QACpBhB,EAAA,CAAA4B,YAAA,IAAyB;QAGrC5B,EAFQ,CAAAkB,YAAA,EAAM,EACJ,EACJ;;;QAX2ClB,EAAA,CAAA6B,SAAA,EAAgE;QAAhE7B,EAAA,CAAA8B,UAAA,YAAA9B,EAAA,CAAA+B,eAAA,IAAAC,GAAA,WAAAjB,GAAA,CAAArB,eAAA,QAAgE;QACvEM,EAAA,CAAA6B,SAAA,GAAS;QAAT7B,EAAA,CAAAiC,iBAAA,CAAAlB,GAAA,CAAAxB,KAAA,CAAS;QAKhCS,EAAA,CAAA6B,SAAA,GAAwB;;QAC9B7B,EAAA,CAAA6B,SAAA,GAAkB;QAAlB7B,EAAA,CAAA8B,UAAA,YAAAf,GAAA,CAAAtB,MAAA,CAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}