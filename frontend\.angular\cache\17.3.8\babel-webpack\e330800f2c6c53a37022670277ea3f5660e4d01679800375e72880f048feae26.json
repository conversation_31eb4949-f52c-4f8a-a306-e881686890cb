{"ast": null, "code": "import * as moment from 'moment';\nimport { skip } from 'rxjs/operators';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nimport * as i2 from \"src/app/core/services/classroom.service\";\nimport * as i3 from \"src/app/core/services/auth.service\";\nimport * as i4 from \"src/app/core/services/calendar.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"primeng/button\";\nimport * as i7 from \"primeng/scrollpanel\";\nimport * as i8 from \"../lesson-row-item/lesson-row-item.component\";\nconst _c0 = [\"scrollPanel\"];\nconst _c1 = a0 => ({\n  \"block-gradient-reverse\": a0\n});\nconst _c2 = () => ({\n  \"top\": \"2px\"\n});\nconst _c3 = a0 => ({\n  \"active\": a0\n});\nconst _c4 = a0 => ({\n  \"disabled-div\": a0\n});\nconst _c5 = a0 => ({\n  width: \"100%\",\n  height: a0\n});\nfunction LessonScheduleBoxComponent_ng_container_20_p_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 17);\n    i0.ɵɵtext(1, \" Your teacher will contact you as soon as possible to arrange your first lesson. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LessonScheduleBoxComponent_ng_container_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 12);\n    i0.ɵɵelement(2, \"img\", 13);\n    i0.ɵɵelementStart(3, \"div\", 14)(4, \"h5\", 15);\n    i0.ɵɵtext(5, \"You have no upcoming lessons yet!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, LessonScheduleBoxComponent_ng_container_20_p_6_Template, 2, 0, \"p\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isStudent);\n  }\n}\nfunction LessonScheduleBoxComponent_ng_container_21_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"app-lesson-row-item\", 18);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const lesson_r3 = i0.ɵɵnextContext().$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"lesson\", lesson_r3)(\"classroom\", lesson_r3.classroom ? lesson_r3.classroom : ctx_r1.classroom)(\"activeLevel\", ctx_r1.classroom.activeLevel ? ctx_r1.classroom.activeLevel : null)(\"title\", ctx_r1.classroom.title ? ctx_r1.classroom.title : ctx_r1.classroom.language);\n  }\n}\nfunction LessonScheduleBoxComponent_ng_container_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, LessonScheduleBoxComponent_ng_container_21_ng_container_1_Template, 2, 4, \"ng-container\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const lesson_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.generalService.isNullishObject(lesson_r3));\n  }\n}\nexport let LessonScheduleBoxComponent = /*#__PURE__*/(() => {\n  class LessonScheduleBoxComponent {\n    constructor(generalService, classroomService, authService, calendarService, cdr) {\n      this.generalService = generalService;\n      this.classroomService = classroomService;\n      this.authService = authService;\n      this.calendarService = calendarService;\n      this.cdr = cdr;\n      this.subs = new SubSink();\n      this.scrollPanel = {};\n      this.reverseGradient = false;\n      this.lessons = [];\n      this.classroom = {};\n      this.classRooms = [];\n      this.disableAddNewLessonButton = false;\n      this.currentDate = new Date();\n      this.scrollHeight = 300;\n      this.formattedDate = '';\n      this.filteredLessons = [];\n      this.selectedMonthDate = new Date();\n      this.selectedFilterType = 'upcoming';\n      this.user = {};\n    }\n    ngOnInit() {\n      const todayDate = new Date();\n      this.formattedDate = this.formatShownDate(todayDate); // e.g. THU 15 OCT\n      this.user = this.authService.getLoggedInUser();\n      this.filteredLessons = this.lessons;\n      this.filterLessonsBy(this.selectedFilterType);\n      this.initDateListener();\n      this.initCalendarAgendaMonthListener();\n    }\n    ngAfterViewInit() {\n      this.initCalendarAgendaHeightListener();\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n    }\n    ngOnChanges(changes) {\n      // remove empty arrays\n      this.lessons = this.lessons.flatMap(arr => arr);\n      this.filterLessonsBy(this.selectedFilterType);\n      if (changes.currentDate && !changes.currentDate.firstChange) {}\n    }\n    trackById(index, item) {\n      return +item.id;\n    }\n    /**\n     * Formats a date as a string in the format \"WEEKDAY DAY MONTH\", e.g. \"THU 15 OCT\".\n     * @param {Date} date - The date to format.\n     * @returns {string} The formatted date string.\n     */\n    formatShownDate(date) {\n      return moment(date).format('ddd DD MMM').toUpperCase();\n    }\n    onGoToBookingSystemClicked() {\n      this.generalService.navigateToBookingSystem();\n    }\n    getSelectedFilterType(type) {\n      return this.selectedFilterType === type;\n    }\n    filterLessonsBy(type) {\n      const now = moment.tz(moment(), this.user.timeZone);\n      switch (type) {\n        case 'today':\n          this.selectedFilterType = 'today';\n          this.filteredLessons = this.lessons.filter(lesson => {\n            const lessonDate = moment(lesson.startingDate);\n            return lessonDate.isSame(now, 'day');\n          });\n          break;\n        case 'upcoming':\n          this.selectedFilterType = 'upcoming';\n          this.filteredLessons = this.lessons.filter(lesson => {\n            const lessonDate = moment(lesson.startingDate);\n            return lessonDate.isSameOrAfter(now, 'day');\n          }).sort(this.sortByDate);\n          break;\n        case 'past':\n          this.selectedFilterType = 'past';\n          this.filteredLessons = this.lessons.filter(lesson => {\n            const lessonDate = moment(lesson.startingDate);\n            return lessonDate.isBefore(now, 'day');\n          }).sort(this.sortByDate);\n          break;\n        case 'month':\n          this.selectedFilterType = 'month';\n          this.filteredLessons = this.lessons.filter(lesson => {\n            const lessonDate = moment(lesson.startingDate);\n            return lessonDate.month() === moment(this.selectedMonthDate).month() && lessonDate.year() === moment(this.selectedMonthDate).year();\n          }).sort(this.sortByDate);\n          break;\n      }\n    }\n    sortByDate(a, b) {\n      const dateA = moment(a.startingDate);\n      const dateB = moment(b.startingDate);\n      return dateA.diff(dateB);\n    }\n    initDateListener() {\n      this.subs.add(this.classroomService.selectedAgendaDate.pipe(skip(1)).subscribe(res => {\n        const date = new Date(res);\n        const dateMormat = moment(date).format(\"YYYY-MM-DD\");\n        this.filteredLessons = this.lessons.filter(lesson => {\n          const lessonDate = moment(lesson.startingDate).format(\"YYYY-MM-DD\");\n          return dateMormat === lessonDate;\n        });\n        this.formattedDate = this.formatShownDate(date);\n      }));\n    }\n    initCalendarAgendaMonthListener() {\n      this.subs.add(this.calendarService.calendarAgendaMonthListener.pipe(skip(1)).subscribe(res => {\n        this.selectedMonthDate = res;\n      }));\n    }\n    initCalendarAgendaHeightListener() {\n      this.subs.add(this.calendarService.calendarAgendaHeightListener.subscribe(res => {\n        if (res) {\n          this.scrollHeight = res - 80;\n          if (this.scrollHeight) {\n            this.scrollPanel.refresh();\n            this.cdr.detectChanges();\n          }\n        }\n      }));\n    }\n    static #_ = this.ɵfac = function LessonScheduleBoxComponent_Factory(t) {\n      return new (t || LessonScheduleBoxComponent)(i0.ɵɵdirectiveInject(i1.GeneralService), i0.ɵɵdirectiveInject(i2.ClassroomService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.CalendarService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LessonScheduleBoxComponent,\n      selectors: [[\"app-lesson-schedule-box\"]],\n      viewQuery: function LessonScheduleBoxComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.scrollPanel = _t.first);\n        }\n      },\n      inputs: {\n        reverseGradient: \"reverseGradient\",\n        lessons: \"lessons\",\n        classroom: \"classroom\",\n        classRooms: \"classRooms\",\n        disableAddNewLessonButton: \"disableAddNewLessonButton\",\n        currentDate: \"currentDate\",\n        scrollHeight: \"scrollHeight\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 22,\n      vars: 28,\n      consts: [[\"scrollPanel\", \"\"], [3, \"ngClass\"], [1, \"button-group\", \"absolute\", \"px-4\", \"z-4\", 3, \"ngStyle\"], [1, \"type-button\", 3, \"click\", \"ngClass\"], [1, \"line\"], [\"slot\", \"start\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"py-2\", \"px-2\", 3, \"ngClass\"], [1, \"text-primary\"], [\"pButton\", \"\", \"type\", \"button\", \"icon\", \"pi pi-plus\", 1, \"p-button-raised\", \"p-button-rounded\", \"plus-btn-circle\", \"md\", 3, \"click\"], [\"styleClass\", \"custombar1\"], [4, \"ngIf\"], [4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"flex-column\", \"px-2\", \"py-2\", \"md:px-3\", \"lg:px-4\", \"md:py-3\", \"text-center\"], [\"src\", \"/assets/images/dashboard/fast-time.svg\", \"alt\", \"fast-time\", \"width\", \"90\"], [1, \"mt-3\", \"font-bold\"], [1, \"text-900\", \"font-semibold\", \"mt-1\"], [\"class\", \"font-medium mt-1\", 4, \"ngIf\"], [1, \"font-medium\", \"mt-1\"], [3, \"lesson\", \"classroom\", \"activeLevel\", \"title\"]],\n      template: function LessonScheduleBoxComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function LessonScheduleBoxComponent_Template_button_click_2_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.filterLessonsBy(\"today\"));\n          });\n          i0.ɵɵtext(3, \"Day\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"span\", 4);\n          i0.ɵɵelementStart(5, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function LessonScheduleBoxComponent_Template_button_click_5_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.filterLessonsBy(\"month\"));\n          });\n          i0.ɵɵtext(6, \"Month\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"span\", 4);\n          i0.ɵɵelementStart(8, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function LessonScheduleBoxComponent_Template_button_click_8_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.filterLessonsBy(\"upcoming\"));\n          });\n          i0.ɵɵtext(9, \"Upcoming\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(10, \"span\", 4);\n          i0.ɵɵelementStart(11, \"button\", 3);\n          i0.ɵɵlistener(\"click\", function LessonScheduleBoxComponent_Template_button_click_11_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.filterLessonsBy(\"past\"));\n          });\n          i0.ɵɵtext(12, \"Past\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementContainerStart(13, 5);\n          i0.ɵɵelementStart(14, \"div\", 6)(15, \"span\", 7);\n          i0.ɵɵtext(16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function LessonScheduleBoxComponent_Template_button_click_17_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onGoToBookingSystemClicked());\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementStart(18, \"p-scrollPanel\", 9, 0);\n          i0.ɵɵtemplate(20, LessonScheduleBoxComponent_ng_container_20_Template, 7, 1, \"ng-container\", 10)(21, LessonScheduleBoxComponent_ng_container_21_Template, 2, 1, \"ng-container\", 11);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(13, _c1, ctx.reverseGradient));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction0(15, _c2));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(16, _c3, ctx.getSelectedFilterType(\"today\")));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(18, _c3, ctx.getSelectedFilterType(\"month\")));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(20, _c3, ctx.getSelectedFilterType(\"upcoming\")));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(22, _c3, ctx.getSelectedFilterType(\"past\")));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(24, _c4, ctx.disableAddNewLessonButton));\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.formattedDate);\n          i0.ɵɵadvance(2);\n          i0.ɵɵstyleMap(i0.ɵɵpureFunction1(26, _c5, ctx.scrollHeight + \"px\"));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.filteredLessons.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngForOf\", ctx.filteredLessons)(\"ngForTrackBy\", ctx.trackById);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i5.NgStyle, i6.ButtonDirective, i7.ScrollPanel, i8.LessonRowItemComponent],\n      styles: [\".button-group[_ngcontent-%COMP%]{display:flex;justify-content:start;align-items:center}.type-button[_ngcontent-%COMP%]{padding:8px 0;background-color:transparent;border:1px solid transparent;border-radius:5px;cursor:pointer;color:#fff;position:relative}.type-button.active[_ngcontent-%COMP%]:after{border-bottom:3px solid #fff;width:10px;content:\\\"\\\";border-bottom:1px solid #fff;width:100%;position:absolute;bottom:3px;left:0}.type-button-active[_ngcontent-%COMP%]{color:var(--primary-color)}.line[_ngcontent-%COMP%]{width:1px;height:20px;background-color:#fff;opacity:.5;margin:0 10px}.block-gradient-reverse[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .type-button[_ngcontent-%COMP%]{color:var(--primary-color)}.block-gradient-reverse[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .line[_ngcontent-%COMP%]{background-color:#3345a7}\"]\n    });\n  }\n  return LessonScheduleBoxComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}