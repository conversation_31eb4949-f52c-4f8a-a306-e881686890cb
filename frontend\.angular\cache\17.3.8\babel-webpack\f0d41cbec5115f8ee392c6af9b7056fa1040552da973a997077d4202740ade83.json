{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { LibraryRoutingModule } from './library-routing.module';\nimport { LibraryComponent } from './library/library.component';\nimport { SharedModule } from 'src/app/shared/shared.module';\nimport { FormsModule } from '@angular/forms';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { LibraryFileRowItemComponent } from './library/components/library-file-row-item/library-file-row-item.component';\nimport { LibraryLeftSidebarComponent } from './library/components/library-left-sidebar/library-left-sidebar.component';\nimport { DynamicDialogRef } from 'primeng/dynamicdialog';\nimport { LibraryFileActionsDialogComponent } from './library/components/library-file-actions-dialog/library-file-actions-dialog.component';\nimport { SingleLibraryComponent } from './library/components/single-library/single-library.component';\nimport { SingleLibraryFolderItemComponent } from './library/components/single-library-folder-item/single-library-folder-item.component';\nimport { FilterPipe } from 'src/app/core/pipes/filter.pipe';\nlet LibraryModule = class LibraryModule {};\nLibraryModule = __decorate([NgModule({\n  declarations: [LibraryComponent, LibraryFileRowItemComponent, LibraryLeftSidebarComponent, LibraryFileActionsDialogComponent, SingleLibraryComponent, SingleLibraryFolderItemComponent, FilterPipe],\n  imports: [CommonModule, LibraryRoutingModule, SharedModule, FormsModule, ConfirmDialogModule],\n  exports: [LibraryComponent, LibraryLeftSidebarComponent, SingleLibraryComponent, LibraryFileActionsDialogComponent, FilterPipe],\n  providers: [DynamicDialogRef]\n})], LibraryModule);\nexport { LibraryModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "LibraryRoutingModule", "LibraryComponent", "SharedModule", "FormsModule", "ConfirmDialogModule", "LibraryFileRowItemComponent", "LibraryLeftSidebarComponent", "DynamicDialogRef", "LibraryFileActionsDialogComponent", "SingleLibraryComponent", "SingleLibraryFolderItemComponent", "FilterPipe", "LibraryModule", "__decorate", "declarations", "imports", "exports", "providers"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\library\\library.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nimport { LibraryRoutingModule } from './library-routing.module';\r\nimport { LibraryComponent } from './library/library.component';\r\nimport { BrowserModule } from '@angular/platform-browser';\r\nimport { SharedModule } from 'src/app/shared/shared.module';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\r\nimport { LibraryFileRowItemComponent } from './library/components/library-file-row-item/library-file-row-item.component';\r\nimport { LibraryLeftSidebarComponent } from './library/components/library-left-sidebar/library-left-sidebar.component';\r\nimport { DynamicDialogRef } from 'primeng/dynamicdialog';\r\nimport { LibraryFileActionsDialogComponent } from './library/components/library-file-actions-dialog/library-file-actions-dialog.component';\r\nimport { SingleLibraryComponent } from './library/components/single-library/single-library.component';\r\nimport { SingleLibraryFolderItemComponent } from './library/components/single-library-folder-item/single-library-folder-item.component';\r\nimport { FilterPipe } from 'src/app/core/pipes/filter.pipe';\r\n\r\n\r\n@NgModule({\r\n  declarations: [\r\n    LibraryComponent,\r\n    LibraryFileRowItemComponent,\r\n    LibraryLeftSidebarComponent,\r\n    LibraryFileActionsDialogComponent,\r\n    SingleLibraryComponent,\r\n    SingleLibraryFolderItemComponent,\r\n    FilterPipe\r\n  ],\r\n  imports: [\r\n    CommonModule,\r\n    LibraryRoutingModule,\r\n    SharedModule,\r\n    FormsModule,\r\n    ConfirmDialogModule,\r\n  ],\r\n  exports: [\r\n    LibraryComponent,\r\n    LibraryLeftSidebarComponent,\r\n    SingleLibraryComponent,\r\n    LibraryFileActionsDialogComponent,\r\n    FilterPipe\r\n  ],\r\n  providers: [DynamicDialogRef]\r\n})\r\nexport class LibraryModule { }\r\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,oBAAoB,QAAQ,0BAA0B;AAC/D,SAASC,gBAAgB,QAAQ,6BAA6B;AAE9D,SAASC,YAAY,QAAQ,8BAA8B;AAC3D,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,2BAA2B,QAAQ,4EAA4E;AACxH,SAASC,2BAA2B,QAAQ,0EAA0E;AACtH,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,iCAAiC,QAAQ,wFAAwF;AAC1I,SAASC,sBAAsB,QAAQ,8DAA8D;AACrG,SAASC,gCAAgC,QAAQ,sFAAsF;AACvI,SAASC,UAAU,QAAQ,gCAAgC;AA6BpD,IAAMC,aAAa,GAAnB,MAAMA,aAAa,GAAI;AAAjBA,aAAa,GAAAC,UAAA,EA1BzBf,QAAQ,CAAC;EACRgB,YAAY,EAAE,CACZb,gBAAgB,EAChBI,2BAA2B,EAC3BC,2BAA2B,EAC3BE,iCAAiC,EACjCC,sBAAsB,EACtBC,gCAAgC,EAChCC,UAAU,CACX;EACDI,OAAO,EAAE,CACPhB,YAAY,EACZC,oBAAoB,EACpBE,YAAY,EACZC,WAAW,EACXC,mBAAmB,CACpB;EACDY,OAAO,EAAE,CACPf,gBAAgB,EAChBK,2BAA2B,EAC3BG,sBAAsB,EACtBD,iCAAiC,EACjCG,UAAU,CACX;EACDM,SAAS,EAAE,CAACV,gBAAgB;CAC7B,CAAC,C,EACWK,aAAa,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}