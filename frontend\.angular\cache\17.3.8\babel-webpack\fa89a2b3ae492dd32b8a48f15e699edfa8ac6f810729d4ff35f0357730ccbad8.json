{"ast": null, "code": "/*! @license DOMPurify 2.4.7 | (c) Cure<PERSON> and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/2.4.7/LICENSE */\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() : typeof define === 'function' && define.amd ? define(factory) : (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.DOMPurify = factory());\n})(this, function () {\n  'use strict';\n\n  function _typeof(obj) {\n    \"@babel/helpers - typeof\";\n\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n      return typeof obj;\n    } : function (obj) {\n      return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    }, _typeof(obj);\n  }\n  function _setPrototypeOf(o, p) {\n    _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n      o.__proto__ = p;\n      return o;\n    };\n    return _setPrototypeOf(o, p);\n  }\n  function _isNativeReflectConstruct() {\n    if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n    if (Reflect.construct.sham) return false;\n    if (typeof Proxy === \"function\") return true;\n    try {\n      Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n      return true;\n    } catch (e) {\n      return false;\n    }\n  }\n  function _construct(Parent, args, Class) {\n    if (_isNativeReflectConstruct()) {\n      _construct = Reflect.construct;\n    } else {\n      _construct = function _construct(Parent, args, Class) {\n        var a = [null];\n        a.push.apply(a, args);\n        var Constructor = Function.bind.apply(Parent, a);\n        var instance = new Constructor();\n        if (Class) _setPrototypeOf(instance, Class.prototype);\n        return instance;\n      };\n    }\n    return _construct.apply(null, arguments);\n  }\n  function _toConsumableArray(arr) {\n    return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n  }\n  function _arrayWithoutHoles(arr) {\n    if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n  }\n  function _iterableToArray(iter) {\n    if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n  }\n  function _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n  }\n  function _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n    return arr2;\n  }\n  function _nonIterableSpread() {\n    throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n  var hasOwnProperty = Object.hasOwnProperty,\n    setPrototypeOf = Object.setPrototypeOf,\n    isFrozen = Object.isFrozen,\n    getPrototypeOf = Object.getPrototypeOf,\n    getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n  var freeze = Object.freeze,\n    seal = Object.seal,\n    create = Object.create; // eslint-disable-line import/no-mutable-exports\n\n  var _ref = typeof Reflect !== 'undefined' && Reflect,\n    apply = _ref.apply,\n    construct = _ref.construct;\n  if (!apply) {\n    apply = function apply(fun, thisValue, args) {\n      return fun.apply(thisValue, args);\n    };\n  }\n  if (!freeze) {\n    freeze = function freeze(x) {\n      return x;\n    };\n  }\n  if (!seal) {\n    seal = function seal(x) {\n      return x;\n    };\n  }\n  if (!construct) {\n    construct = function construct(Func, args) {\n      return _construct(Func, _toConsumableArray(args));\n    };\n  }\n  var arrayForEach = unapply(Array.prototype.forEach);\n  var arrayPop = unapply(Array.prototype.pop);\n  var arrayPush = unapply(Array.prototype.push);\n  var stringToLowerCase = unapply(String.prototype.toLowerCase);\n  var stringToString = unapply(String.prototype.toString);\n  var stringMatch = unapply(String.prototype.match);\n  var stringReplace = unapply(String.prototype.replace);\n  var stringIndexOf = unapply(String.prototype.indexOf);\n  var stringTrim = unapply(String.prototype.trim);\n  var regExpTest = unapply(RegExp.prototype.test);\n  var typeErrorCreate = unconstruct(TypeError);\n  function unapply(func) {\n    return function (thisArg) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      return apply(func, thisArg, args);\n    };\n  }\n  function unconstruct(func) {\n    return function () {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      return construct(func, args);\n    };\n  }\n  /* Add properties to a lookup table */\n\n  function addToSet(set, array, transformCaseFunc) {\n    var _transformCaseFunc;\n    transformCaseFunc = (_transformCaseFunc = transformCaseFunc) !== null && _transformCaseFunc !== void 0 ? _transformCaseFunc : stringToLowerCase;\n    if (setPrototypeOf) {\n      // Make 'in' and truthy checks like Boolean(set.constructor)\n      // independent of any properties defined on Object.prototype.\n      // Prevent prototype setters from intercepting set as a this value.\n      setPrototypeOf(set, null);\n    }\n    var l = array.length;\n    while (l--) {\n      var element = array[l];\n      if (typeof element === 'string') {\n        var lcElement = transformCaseFunc(element);\n        if (lcElement !== element) {\n          // Config presets (e.g. tags.js, attrs.js) are immutable.\n          if (!isFrozen(array)) {\n            array[l] = lcElement;\n          }\n          element = lcElement;\n        }\n      }\n      set[element] = true;\n    }\n    return set;\n  }\n  /* Shallow clone an object */\n\n  function clone(object) {\n    var newObject = create(null);\n    var property;\n    for (property in object) {\n      if (apply(hasOwnProperty, object, [property]) === true) {\n        newObject[property] = object[property];\n      }\n    }\n    return newObject;\n  }\n  /* IE10 doesn't support __lookupGetter__ so lets'\n   * simulate it. It also automatically checks\n   * if the prop is function or getter and behaves\n   * accordingly. */\n\n  function lookupGetter(object, prop) {\n    while (object !== null) {\n      var desc = getOwnPropertyDescriptor(object, prop);\n      if (desc) {\n        if (desc.get) {\n          return unapply(desc.get);\n        }\n        if (typeof desc.value === 'function') {\n          return unapply(desc.value);\n        }\n      }\n      object = getPrototypeOf(object);\n    }\n    function fallbackValue(element) {\n      console.warn('fallback value for', element);\n      return null;\n    }\n    return fallbackValue;\n  }\n  var html$1 = freeze(['a', 'abbr', 'acronym', 'address', 'area', 'article', 'aside', 'audio', 'b', 'bdi', 'bdo', 'big', 'blink', 'blockquote', 'body', 'br', 'button', 'canvas', 'caption', 'center', 'cite', 'code', 'col', 'colgroup', 'content', 'data', 'datalist', 'dd', 'decorator', 'del', 'details', 'dfn', 'dialog', 'dir', 'div', 'dl', 'dt', 'element', 'em', 'fieldset', 'figcaption', 'figure', 'font', 'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'head', 'header', 'hgroup', 'hr', 'html', 'i', 'img', 'input', 'ins', 'kbd', 'label', 'legend', 'li', 'main', 'map', 'mark', 'marquee', 'menu', 'menuitem', 'meter', 'nav', 'nobr', 'ol', 'optgroup', 'option', 'output', 'p', 'picture', 'pre', 'progress', 'q', 'rp', 'rt', 'ruby', 's', 'samp', 'section', 'select', 'shadow', 'small', 'source', 'spacer', 'span', 'strike', 'strong', 'style', 'sub', 'summary', 'sup', 'table', 'tbody', 'td', 'template', 'textarea', 'tfoot', 'th', 'thead', 'time', 'tr', 'track', 'tt', 'u', 'ul', 'var', 'video', 'wbr']); // SVG\n\n  var svg$1 = freeze(['svg', 'a', 'altglyph', 'altglyphdef', 'altglyphitem', 'animatecolor', 'animatemotion', 'animatetransform', 'circle', 'clippath', 'defs', 'desc', 'ellipse', 'filter', 'font', 'g', 'glyph', 'glyphref', 'hkern', 'image', 'line', 'lineargradient', 'marker', 'mask', 'metadata', 'mpath', 'path', 'pattern', 'polygon', 'polyline', 'radialgradient', 'rect', 'stop', 'style', 'switch', 'symbol', 'text', 'textpath', 'title', 'tref', 'tspan', 'view', 'vkern']);\n  var svgFilters = freeze(['feBlend', 'feColorMatrix', 'feComponentTransfer', 'feComposite', 'feConvolveMatrix', 'feDiffuseLighting', 'feDisplacementMap', 'feDistantLight', 'feFlood', 'feFuncA', 'feFuncB', 'feFuncG', 'feFuncR', 'feGaussianBlur', 'feImage', 'feMerge', 'feMergeNode', 'feMorphology', 'feOffset', 'fePointLight', 'feSpecularLighting', 'feSpotLight', 'feTile', 'feTurbulence']); // List of SVG elements that are disallowed by default.\n  // We still need to know them so that we can do namespace\n  // checks properly in case one wants to add them to\n  // allow-list.\n\n  var svgDisallowed = freeze(['animate', 'color-profile', 'cursor', 'discard', 'fedropshadow', 'font-face', 'font-face-format', 'font-face-name', 'font-face-src', 'font-face-uri', 'foreignobject', 'hatch', 'hatchpath', 'mesh', 'meshgradient', 'meshpatch', 'meshrow', 'missing-glyph', 'script', 'set', 'solidcolor', 'unknown', 'use']);\n  var mathMl$1 = freeze(['math', 'menclose', 'merror', 'mfenced', 'mfrac', 'mglyph', 'mi', 'mlabeledtr', 'mmultiscripts', 'mn', 'mo', 'mover', 'mpadded', 'mphantom', 'mroot', 'mrow', 'ms', 'mspace', 'msqrt', 'mstyle', 'msub', 'msup', 'msubsup', 'mtable', 'mtd', 'mtext', 'mtr', 'munder', 'munderover']); // Similarly to SVG, we want to know all MathML elements,\n  // even those that we disallow by default.\n\n  var mathMlDisallowed = freeze(['maction', 'maligngroup', 'malignmark', 'mlongdiv', 'mscarries', 'mscarry', 'msgroup', 'mstack', 'msline', 'msrow', 'semantics', 'annotation', 'annotation-xml', 'mprescripts', 'none']);\n  var text = freeze(['#text']);\n  var html = freeze(['accept', 'action', 'align', 'alt', 'autocapitalize', 'autocomplete', 'autopictureinpicture', 'autoplay', 'background', 'bgcolor', 'border', 'capture', 'cellpadding', 'cellspacing', 'checked', 'cite', 'class', 'clear', 'color', 'cols', 'colspan', 'controls', 'controlslist', 'coords', 'crossorigin', 'datetime', 'decoding', 'default', 'dir', 'disabled', 'disablepictureinpicture', 'disableremoteplayback', 'download', 'draggable', 'enctype', 'enterkeyhint', 'face', 'for', 'headers', 'height', 'hidden', 'high', 'href', 'hreflang', 'id', 'inputmode', 'integrity', 'ismap', 'kind', 'label', 'lang', 'list', 'loading', 'loop', 'low', 'max', 'maxlength', 'media', 'method', 'min', 'minlength', 'multiple', 'muted', 'name', 'nonce', 'noshade', 'novalidate', 'nowrap', 'open', 'optimum', 'pattern', 'placeholder', 'playsinline', 'poster', 'preload', 'pubdate', 'radiogroup', 'readonly', 'rel', 'required', 'rev', 'reversed', 'role', 'rows', 'rowspan', 'spellcheck', 'scope', 'selected', 'shape', 'size', 'sizes', 'span', 'srclang', 'start', 'src', 'srcset', 'step', 'style', 'summary', 'tabindex', 'title', 'translate', 'type', 'usemap', 'valign', 'value', 'width', 'xmlns', 'slot']);\n  var svg = freeze(['accent-height', 'accumulate', 'additive', 'alignment-baseline', 'ascent', 'attributename', 'attributetype', 'azimuth', 'basefrequency', 'baseline-shift', 'begin', 'bias', 'by', 'class', 'clip', 'clippathunits', 'clip-path', 'clip-rule', 'color', 'color-interpolation', 'color-interpolation-filters', 'color-profile', 'color-rendering', 'cx', 'cy', 'd', 'dx', 'dy', 'diffuseconstant', 'direction', 'display', 'divisor', 'dur', 'edgemode', 'elevation', 'end', 'fill', 'fill-opacity', 'fill-rule', 'filter', 'filterunits', 'flood-color', 'flood-opacity', 'font-family', 'font-size', 'font-size-adjust', 'font-stretch', 'font-style', 'font-variant', 'font-weight', 'fx', 'fy', 'g1', 'g2', 'glyph-name', 'glyphref', 'gradientunits', 'gradienttransform', 'height', 'href', 'id', 'image-rendering', 'in', 'in2', 'k', 'k1', 'k2', 'k3', 'k4', 'kerning', 'keypoints', 'keysplines', 'keytimes', 'lang', 'lengthadjust', 'letter-spacing', 'kernelmatrix', 'kernelunitlength', 'lighting-color', 'local', 'marker-end', 'marker-mid', 'marker-start', 'markerheight', 'markerunits', 'markerwidth', 'maskcontentunits', 'maskunits', 'max', 'mask', 'media', 'method', 'mode', 'min', 'name', 'numoctaves', 'offset', 'operator', 'opacity', 'order', 'orient', 'orientation', 'origin', 'overflow', 'paint-order', 'path', 'pathlength', 'patterncontentunits', 'patterntransform', 'patternunits', 'points', 'preservealpha', 'preserveaspectratio', 'primitiveunits', 'r', 'rx', 'ry', 'radius', 'refx', 'refy', 'repeatcount', 'repeatdur', 'restart', 'result', 'rotate', 'scale', 'seed', 'shape-rendering', 'specularconstant', 'specularexponent', 'spreadmethod', 'startoffset', 'stddeviation', 'stitchtiles', 'stop-color', 'stop-opacity', 'stroke-dasharray', 'stroke-dashoffset', 'stroke-linecap', 'stroke-linejoin', 'stroke-miterlimit', 'stroke-opacity', 'stroke', 'stroke-width', 'style', 'surfacescale', 'systemlanguage', 'tabindex', 'targetx', 'targety', 'transform', 'transform-origin', 'text-anchor', 'text-decoration', 'text-rendering', 'textlength', 'type', 'u1', 'u2', 'unicode', 'values', 'viewbox', 'visibility', 'version', 'vert-adv-y', 'vert-origin-x', 'vert-origin-y', 'width', 'word-spacing', 'wrap', 'writing-mode', 'xchannelselector', 'ychannelselector', 'x', 'x1', 'x2', 'xmlns', 'y', 'y1', 'y2', 'z', 'zoomandpan']);\n  var mathMl = freeze(['accent', 'accentunder', 'align', 'bevelled', 'close', 'columnsalign', 'columnlines', 'columnspan', 'denomalign', 'depth', 'dir', 'display', 'displaystyle', 'encoding', 'fence', 'frame', 'height', 'href', 'id', 'largeop', 'length', 'linethickness', 'lspace', 'lquote', 'mathbackground', 'mathcolor', 'mathsize', 'mathvariant', 'maxsize', 'minsize', 'movablelimits', 'notation', 'numalign', 'open', 'rowalign', 'rowlines', 'rowspacing', 'rowspan', 'rspace', 'rquote', 'scriptlevel', 'scriptminsize', 'scriptsizemultiplier', 'selection', 'separator', 'separators', 'stretchy', 'subscriptshift', 'supscriptshift', 'symmetric', 'voffset', 'width', 'xmlns']);\n  var xml = freeze(['xlink:href', 'xml:id', 'xlink:title', 'xml:space', 'xmlns:xlink']);\n  var MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\n\n  var ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\n  var TMPLIT_EXPR = seal(/\\${[\\w\\W]*}/gm);\n  var DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]/); // eslint-disable-line no-useless-escape\n\n  var ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\n\n  var IS_ALLOWED_URI = seal(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n  );\n  var IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\n  var ATTR_WHITESPACE = seal(/[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n  );\n  var DOCTYPE_NAME = seal(/^html$/i);\n  var getGlobal = function getGlobal() {\n    return typeof window === 'undefined' ? null : window;\n  };\n  /**\n   * Creates a no-op policy for internal use only.\n   * Don't export this function outside this module!\n   * @param {?TrustedTypePolicyFactory} trustedTypes The policy factory.\n   * @param {Document} document The document object (to determine policy name suffix)\n   * @return {?TrustedTypePolicy} The policy created (or null, if Trusted Types\n   * are not supported).\n   */\n\n  var _createTrustedTypesPolicy = function _createTrustedTypesPolicy(trustedTypes, document) {\n    if (_typeof(trustedTypes) !== 'object' || typeof trustedTypes.createPolicy !== 'function') {\n      return null;\n    } // Allow the callers to control the unique policy name\n    // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n    // Policy creation with duplicate names throws in Trusted Types.\n\n    var suffix = null;\n    var ATTR_NAME = 'data-tt-policy-suffix';\n    if (document.currentScript && document.currentScript.hasAttribute(ATTR_NAME)) {\n      suffix = document.currentScript.getAttribute(ATTR_NAME);\n    }\n    var policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n    try {\n      return trustedTypes.createPolicy(policyName, {\n        createHTML: function createHTML(html) {\n          return html;\n        },\n        createScriptURL: function createScriptURL(scriptUrl) {\n          return scriptUrl;\n        }\n      });\n    } catch (_) {\n      // Policy creation failed (most likely another DOMPurify script has\n      // already run). Skip creating the policy, as this will only cause errors\n      // if TT are enforced.\n      console.warn('TrustedTypes policy ' + policyName + ' could not be created.');\n      return null;\n    }\n  };\n  function createDOMPurify() {\n    var window = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : getGlobal();\n    var DOMPurify = function DOMPurify(root) {\n      return createDOMPurify(root);\n    };\n    /**\n     * Version label, exposed for easier checks\n     * if DOMPurify is up to date or not\n     */\n\n    DOMPurify.version = '2.4.7';\n    /**\n     * Array of elements that DOMPurify removed during sanitation.\n     * Empty if nothing was removed.\n     */\n\n    DOMPurify.removed = [];\n    if (!window || !window.document || window.document.nodeType !== 9) {\n      // Not running in a browser, provide a factory function\n      // so that you can pass your own Window\n      DOMPurify.isSupported = false;\n      return DOMPurify;\n    }\n    var originalDocument = window.document;\n    var document = window.document;\n    var DocumentFragment = window.DocumentFragment,\n      HTMLTemplateElement = window.HTMLTemplateElement,\n      Node = window.Node,\n      Element = window.Element,\n      NodeFilter = window.NodeFilter,\n      _window$NamedNodeMap = window.NamedNodeMap,\n      NamedNodeMap = _window$NamedNodeMap === void 0 ? window.NamedNodeMap || window.MozNamedAttrMap : _window$NamedNodeMap,\n      HTMLFormElement = window.HTMLFormElement,\n      DOMParser = window.DOMParser,\n      trustedTypes = window.trustedTypes;\n    var ElementPrototype = Element.prototype;\n    var cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n    var getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n    var getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n    var getParentNode = lookupGetter(ElementPrototype, 'parentNode'); // As per issue #47, the web-components registry is inherited by a\n    // new document created via createHTMLDocument. As per the spec\n    // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n    // a new empty registry is used when creating a template contents owner\n    // document, so we use that as our parent document to ensure nothing\n    // is inherited.\n\n    if (typeof HTMLTemplateElement === 'function') {\n      var template = document.createElement('template');\n      if (template.content && template.content.ownerDocument) {\n        document = template.content.ownerDocument;\n      }\n    }\n    var trustedTypesPolicy = _createTrustedTypesPolicy(trustedTypes, originalDocument);\n    var emptyHTML = trustedTypesPolicy ? trustedTypesPolicy.createHTML('') : '';\n    var _document = document,\n      implementation = _document.implementation,\n      createNodeIterator = _document.createNodeIterator,\n      createDocumentFragment = _document.createDocumentFragment,\n      getElementsByTagName = _document.getElementsByTagName;\n    var importNode = originalDocument.importNode;\n    var documentMode = {};\n    try {\n      documentMode = clone(document).documentMode ? document.documentMode : {};\n    } catch (_) {}\n    var hooks = {};\n    /**\n     * Expose whether this browser supports running the full DOMPurify.\n     */\n\n    DOMPurify.isSupported = typeof getParentNode === 'function' && implementation && implementation.createHTMLDocument !== undefined && documentMode !== 9;\n    var MUSTACHE_EXPR$1 = MUSTACHE_EXPR,\n      ERB_EXPR$1 = ERB_EXPR,\n      TMPLIT_EXPR$1 = TMPLIT_EXPR,\n      DATA_ATTR$1 = DATA_ATTR,\n      ARIA_ATTR$1 = ARIA_ATTR,\n      IS_SCRIPT_OR_DATA$1 = IS_SCRIPT_OR_DATA,\n      ATTR_WHITESPACE$1 = ATTR_WHITESPACE;\n    var IS_ALLOWED_URI$1 = IS_ALLOWED_URI;\n    /**\n     * We consider the elements and attributes below to be safe. Ideally\n     * don't add any new ones but feel free to remove unwanted ones.\n     */\n\n    /* allowed element names */\n\n    var ALLOWED_TAGS = null;\n    var DEFAULT_ALLOWED_TAGS = addToSet({}, [].concat(_toConsumableArray(html$1), _toConsumableArray(svg$1), _toConsumableArray(svgFilters), _toConsumableArray(mathMl$1), _toConsumableArray(text)));\n    /* Allowed attribute names */\n\n    var ALLOWED_ATTR = null;\n    var DEFAULT_ALLOWED_ATTR = addToSet({}, [].concat(_toConsumableArray(html), _toConsumableArray(svg), _toConsumableArray(mathMl), _toConsumableArray(xml)));\n    /*\n     * Configure how DOMPUrify should handle custom elements and their attributes as well as customized built-in elements.\n     * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n     * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n     * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n     */\n\n    var CUSTOM_ELEMENT_HANDLING = Object.seal(Object.create(null, {\n      tagNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null\n      },\n      attributeNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null\n      },\n      allowCustomizedBuiltInElements: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: false\n      }\n    }));\n    /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n\n    var FORBID_TAGS = null;\n    /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n\n    var FORBID_ATTR = null;\n    /* Decide if ARIA attributes are okay */\n\n    var ALLOW_ARIA_ATTR = true;\n    /* Decide if custom data attributes are okay */\n\n    var ALLOW_DATA_ATTR = true;\n    /* Decide if unknown protocols are okay */\n\n    var ALLOW_UNKNOWN_PROTOCOLS = false;\n    /* Decide if self-closing tags in attributes are allowed.\n     * Usually removed due to a mXSS issue in jQuery 3.0 */\n\n    var ALLOW_SELF_CLOSE_IN_ATTR = true;\n    /* Output should be safe for common template engines.\n     * This means, DOMPurify removes data attributes, mustaches and ERB\n     */\n\n    var SAFE_FOR_TEMPLATES = false;\n    /* Decide if document with <html>... should be returned */\n\n    var WHOLE_DOCUMENT = false;\n    /* Track whether config is already set on this instance of DOMPurify. */\n\n    var SET_CONFIG = false;\n    /* Decide if all elements (e.g. style, script) must be children of\n     * document.body. By default, browsers might move them to document.head */\n\n    var FORCE_BODY = false;\n    /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n     * string (or a TrustedHTML object if Trusted Types are supported).\n     * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n     */\n\n    var RETURN_DOM = false;\n    /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n     * string  (or a TrustedHTML object if Trusted Types are supported) */\n\n    var RETURN_DOM_FRAGMENT = false;\n    /* Try to return a Trusted Type object instead of a string, return a string in\n     * case Trusted Types are not supported  */\n\n    var RETURN_TRUSTED_TYPE = false;\n    /* Output should be free from DOM clobbering attacks?\n     * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n     */\n\n    var SANITIZE_DOM = true;\n    /* Achieve full DOM Clobbering protection by isolating the namespace of named\n     * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n     *\n     * HTML/DOM spec rules that enable DOM Clobbering:\n     *   - Named Access on Window (§7.3.3)\n     *   - DOM Tree Accessors (§3.1.5)\n     *   - Form Element Parent-Child Relations (§4.10.3)\n     *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n     *   - HTMLCollection (§4.2.10.2)\n     *\n     * Namespace isolation is implemented by prefixing `id` and `name` attributes\n     * with a constant string, i.e., `user-content-`\n     */\n\n    var SANITIZE_NAMED_PROPS = false;\n    var SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n    /* Keep element content when removing element? */\n\n    var KEEP_CONTENT = true;\n    /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n     * of importing it into a new Document and returning a sanitized copy */\n\n    var IN_PLACE = false;\n    /* Allow usage of profiles like html, svg and mathMl */\n\n    var USE_PROFILES = {};\n    /* Tags to ignore content of when KEEP_CONTENT is true */\n\n    var FORBID_CONTENTS = null;\n    var DEFAULT_FORBID_CONTENTS = addToSet({}, ['annotation-xml', 'audio', 'colgroup', 'desc', 'foreignobject', 'head', 'iframe', 'math', 'mi', 'mn', 'mo', 'ms', 'mtext', 'noembed', 'noframes', 'noscript', 'plaintext', 'script', 'style', 'svg', 'template', 'thead', 'title', 'video', 'xmp']);\n    /* Tags that are safe for data: URIs */\n\n    var DATA_URI_TAGS = null;\n    var DEFAULT_DATA_URI_TAGS = addToSet({}, ['audio', 'video', 'img', 'source', 'image', 'track']);\n    /* Attributes safe for values like \"javascript:\" */\n\n    var URI_SAFE_ATTRIBUTES = null;\n    var DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, ['alt', 'class', 'for', 'id', 'label', 'name', 'pattern', 'placeholder', 'role', 'summary', 'title', 'value', 'style', 'xmlns']);\n    var MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n    var SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n    var HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n    /* Document namespace */\n\n    var NAMESPACE = HTML_NAMESPACE;\n    var IS_EMPTY_INPUT = false;\n    /* Allowed XHTML+XML namespaces */\n\n    var ALLOWED_NAMESPACES = null;\n    var DEFAULT_ALLOWED_NAMESPACES = addToSet({}, [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE], stringToString);\n    /* Parsing of strict XHTML documents */\n\n    var PARSER_MEDIA_TYPE;\n    var SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n    var DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n    var transformCaseFunc;\n    /* Keep a reference to config to pass to hooks */\n\n    var CONFIG = null;\n    /* Ideally, do not touch anything below this line */\n\n    /* ______________________________________________ */\n\n    var formElement = document.createElement('form');\n    var isRegexOrFunction = function isRegexOrFunction(testValue) {\n      return testValue instanceof RegExp || testValue instanceof Function;\n    };\n    /**\n     * _parseConfig\n     *\n     * @param  {Object} cfg optional config literal\n     */\n    // eslint-disable-next-line complexity\n\n    var _parseConfig = function _parseConfig(cfg) {\n      if (CONFIG && CONFIG === cfg) {\n        return;\n      }\n      /* Shield configuration object from tampering */\n\n      if (!cfg || _typeof(cfg) !== 'object') {\n        cfg = {};\n      }\n      /* Shield configuration object from prototype pollution */\n\n      cfg = clone(cfg);\n      PARSER_MEDIA_TYPE =\n      // eslint-disable-next-line unicorn/prefer-includes\n      SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1 ? PARSER_MEDIA_TYPE = DEFAULT_PARSER_MEDIA_TYPE : PARSER_MEDIA_TYPE = cfg.PARSER_MEDIA_TYPE; // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n\n      transformCaseFunc = PARSER_MEDIA_TYPE === 'application/xhtml+xml' ? stringToString : stringToLowerCase;\n      /* Set configuration parameters */\n\n      ALLOWED_TAGS = 'ALLOWED_TAGS' in cfg ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc) : DEFAULT_ALLOWED_TAGS;\n      ALLOWED_ATTR = 'ALLOWED_ATTR' in cfg ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc) : DEFAULT_ALLOWED_ATTR;\n      ALLOWED_NAMESPACES = 'ALLOWED_NAMESPACES' in cfg ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString) : DEFAULT_ALLOWED_NAMESPACES;\n      URI_SAFE_ATTRIBUTES = 'ADD_URI_SAFE_ATTR' in cfg ? addToSet(clone(DEFAULT_URI_SAFE_ATTRIBUTES),\n      // eslint-disable-line indent\n      cfg.ADD_URI_SAFE_ATTR,\n      // eslint-disable-line indent\n      transformCaseFunc // eslint-disable-line indent\n      ) // eslint-disable-line indent\n      : DEFAULT_URI_SAFE_ATTRIBUTES;\n      DATA_URI_TAGS = 'ADD_DATA_URI_TAGS' in cfg ? addToSet(clone(DEFAULT_DATA_URI_TAGS),\n      // eslint-disable-line indent\n      cfg.ADD_DATA_URI_TAGS,\n      // eslint-disable-line indent\n      transformCaseFunc // eslint-disable-line indent\n      ) // eslint-disable-line indent\n      : DEFAULT_DATA_URI_TAGS;\n      FORBID_CONTENTS = 'FORBID_CONTENTS' in cfg ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc) : DEFAULT_FORBID_CONTENTS;\n      FORBID_TAGS = 'FORBID_TAGS' in cfg ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc) : {};\n      FORBID_ATTR = 'FORBID_ATTR' in cfg ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc) : {};\n      USE_PROFILES = 'USE_PROFILES' in cfg ? cfg.USE_PROFILES : false;\n      ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n\n      ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n\n      ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n\n      ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n\n      SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n\n      WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n\n      RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n\n      RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n\n      RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n\n      FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n\n      SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n\n      SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n\n      KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n\n      IN_PLACE = cfg.IN_PLACE || false; // Default false\n\n      IS_ALLOWED_URI$1 = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI$1;\n      NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n      CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n      if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)) {\n        CUSTOM_ELEMENT_HANDLING.tagNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n      }\n      if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)) {\n        CUSTOM_ELEMENT_HANDLING.attributeNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n      }\n      if (cfg.CUSTOM_ELEMENT_HANDLING && typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements === 'boolean') {\n        CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements = cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n      }\n      if (SAFE_FOR_TEMPLATES) {\n        ALLOW_DATA_ATTR = false;\n      }\n      if (RETURN_DOM_FRAGMENT) {\n        RETURN_DOM = true;\n      }\n      /* Parse profile info */\n\n      if (USE_PROFILES) {\n        ALLOWED_TAGS = addToSet({}, _toConsumableArray(text));\n        ALLOWED_ATTR = [];\n        if (USE_PROFILES.html === true) {\n          addToSet(ALLOWED_TAGS, html$1);\n          addToSet(ALLOWED_ATTR, html);\n        }\n        if (USE_PROFILES.svg === true) {\n          addToSet(ALLOWED_TAGS, svg$1);\n          addToSet(ALLOWED_ATTR, svg);\n          addToSet(ALLOWED_ATTR, xml);\n        }\n        if (USE_PROFILES.svgFilters === true) {\n          addToSet(ALLOWED_TAGS, svgFilters);\n          addToSet(ALLOWED_ATTR, svg);\n          addToSet(ALLOWED_ATTR, xml);\n        }\n        if (USE_PROFILES.mathMl === true) {\n          addToSet(ALLOWED_TAGS, mathMl$1);\n          addToSet(ALLOWED_ATTR, mathMl);\n          addToSet(ALLOWED_ATTR, xml);\n        }\n      }\n      /* Merge configuration parameters */\n\n      if (cfg.ADD_TAGS) {\n        if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n          ALLOWED_TAGS = clone(ALLOWED_TAGS);\n        }\n        addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n      }\n      if (cfg.ADD_ATTR) {\n        if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n          ALLOWED_ATTR = clone(ALLOWED_ATTR);\n        }\n        addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n      }\n      if (cfg.ADD_URI_SAFE_ATTR) {\n        addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n      }\n      if (cfg.FORBID_CONTENTS) {\n        if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n          FORBID_CONTENTS = clone(FORBID_CONTENTS);\n        }\n        addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n      }\n      /* Add #text in case KEEP_CONTENT is set to true */\n\n      if (KEEP_CONTENT) {\n        ALLOWED_TAGS['#text'] = true;\n      }\n      /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n\n      if (WHOLE_DOCUMENT) {\n        addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n      }\n      /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n\n      if (ALLOWED_TAGS.table) {\n        addToSet(ALLOWED_TAGS, ['tbody']);\n        delete FORBID_TAGS.tbody;\n      } // Prevent further manipulation of configuration.\n      // Not available in IE8, Safari 5, etc.\n\n      if (freeze) {\n        freeze(cfg);\n      }\n      CONFIG = cfg;\n    };\n    var MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, ['mi', 'mo', 'mn', 'ms', 'mtext']);\n    var HTML_INTEGRATION_POINTS = addToSet({}, ['foreignobject', 'desc', 'title', 'annotation-xml']); // Certain elements are allowed in both SVG and HTML\n    // namespace. We need to specify them explicitly\n    // so that they don't get erroneously deleted from\n    // HTML namespace.\n\n    var COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, ['title', 'style', 'font', 'a', 'script']);\n    /* Keep track of all possible SVG and MathML tags\n     * so that we can perform the namespace checks\n     * correctly. */\n\n    var ALL_SVG_TAGS = addToSet({}, svg$1);\n    addToSet(ALL_SVG_TAGS, svgFilters);\n    addToSet(ALL_SVG_TAGS, svgDisallowed);\n    var ALL_MATHML_TAGS = addToSet({}, mathMl$1);\n    addToSet(ALL_MATHML_TAGS, mathMlDisallowed);\n    /**\n     *\n     *\n     * @param  {Element} element a DOM element whose namespace is being checked\n     * @returns {boolean} Return false if the element has a\n     *  namespace that a spec-compliant parser would never\n     *  return. Return true otherwise.\n     */\n\n    var _checkValidNamespace = function _checkValidNamespace(element) {\n      var parent = getParentNode(element); // In JSDOM, if we're inside shadow DOM, then parentNode\n      // can be null. We just simulate parent in this case.\n\n      if (!parent || !parent.tagName) {\n        parent = {\n          namespaceURI: NAMESPACE,\n          tagName: 'template'\n        };\n      }\n      var tagName = stringToLowerCase(element.tagName);\n      var parentTagName = stringToLowerCase(parent.tagName);\n      if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n        return false;\n      }\n      if (element.namespaceURI === SVG_NAMESPACE) {\n        // The only way to switch from HTML namespace to SVG\n        // is via <svg>. If it happens via any other tag, then\n        // it should be killed.\n        if (parent.namespaceURI === HTML_NAMESPACE) {\n          return tagName === 'svg';\n        } // The only way to switch from MathML to SVG is via`\n        // svg if parent is either <annotation-xml> or MathML\n        // text integration points.\n\n        if (parent.namespaceURI === MATHML_NAMESPACE) {\n          return tagName === 'svg' && (parentTagName === 'annotation-xml' || MATHML_TEXT_INTEGRATION_POINTS[parentTagName]);\n        } // We only allow elements that are defined in SVG\n        // spec. All others are disallowed in SVG namespace.\n\n        return Boolean(ALL_SVG_TAGS[tagName]);\n      }\n      if (element.namespaceURI === MATHML_NAMESPACE) {\n        // The only way to switch from HTML namespace to MathML\n        // is via <math>. If it happens via any other tag, then\n        // it should be killed.\n        if (parent.namespaceURI === HTML_NAMESPACE) {\n          return tagName === 'math';\n        } // The only way to switch from SVG to MathML is via\n        // <math> and HTML integration points\n\n        if (parent.namespaceURI === SVG_NAMESPACE) {\n          return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n        } // We only allow elements that are defined in MathML\n        // spec. All others are disallowed in MathML namespace.\n\n        return Boolean(ALL_MATHML_TAGS[tagName]);\n      }\n      if (element.namespaceURI === HTML_NAMESPACE) {\n        // The only way to switch from SVG to HTML is via\n        // HTML integration points, and from MathML to HTML\n        // is via MathML text integration points\n        if (parent.namespaceURI === SVG_NAMESPACE && !HTML_INTEGRATION_POINTS[parentTagName]) {\n          return false;\n        }\n        if (parent.namespaceURI === MATHML_NAMESPACE && !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]) {\n          return false;\n        } // We disallow tags that are specific for MathML\n        // or SVG and should never appear in HTML namespace\n\n        return !ALL_MATHML_TAGS[tagName] && (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName]);\n      } // For XHTML and XML documents that support custom namespaces\n\n      if (PARSER_MEDIA_TYPE === 'application/xhtml+xml' && ALLOWED_NAMESPACES[element.namespaceURI]) {\n        return true;\n      } // The code should never reach this place (this means\n      // that the element somehow got namespace that is not\n      // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n      // Return false just in case.\n\n      return false;\n    };\n    /**\n     * _forceRemove\n     *\n     * @param  {Node} node a DOM node\n     */\n\n    var _forceRemove = function _forceRemove(node) {\n      arrayPush(DOMPurify.removed, {\n        element: node\n      });\n      try {\n        // eslint-disable-next-line unicorn/prefer-dom-node-remove\n        node.parentNode.removeChild(node);\n      } catch (_) {\n        try {\n          node.outerHTML = emptyHTML;\n        } catch (_) {\n          node.remove();\n        }\n      }\n    };\n    /**\n     * _removeAttribute\n     *\n     * @param  {String} name an Attribute name\n     * @param  {Node} node a DOM node\n     */\n\n    var _removeAttribute = function _removeAttribute(name, node) {\n      try {\n        arrayPush(DOMPurify.removed, {\n          attribute: node.getAttributeNode(name),\n          from: node\n        });\n      } catch (_) {\n        arrayPush(DOMPurify.removed, {\n          attribute: null,\n          from: node\n        });\n      }\n      node.removeAttribute(name); // We void attribute values for unremovable \"is\"\" attributes\n\n      if (name === 'is' && !ALLOWED_ATTR[name]) {\n        if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n          try {\n            _forceRemove(node);\n          } catch (_) {}\n        } else {\n          try {\n            node.setAttribute(name, '');\n          } catch (_) {}\n        }\n      }\n    };\n    /**\n     * _initDocument\n     *\n     * @param  {String} dirty a string of dirty markup\n     * @return {Document} a DOM, filled with the dirty markup\n     */\n\n    var _initDocument = function _initDocument(dirty) {\n      /* Create a HTML document */\n      var doc;\n      var leadingWhitespace;\n      if (FORCE_BODY) {\n        dirty = '<remove></remove>' + dirty;\n      } else {\n        /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n        var matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n        leadingWhitespace = matches && matches[0];\n      }\n      if (PARSER_MEDIA_TYPE === 'application/xhtml+xml' && NAMESPACE === HTML_NAMESPACE) {\n        // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n        dirty = '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' + dirty + '</body></html>';\n      }\n      var dirtyPayload = trustedTypesPolicy ? trustedTypesPolicy.createHTML(dirty) : dirty;\n      /*\n       * Use the DOMParser API by default, fallback later if needs be\n       * DOMParser not work for svg when has multiple root element.\n       */\n\n      if (NAMESPACE === HTML_NAMESPACE) {\n        try {\n          doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n        } catch (_) {}\n      }\n      /* Use createHTMLDocument in case DOMParser is not available */\n\n      if (!doc || !doc.documentElement) {\n        doc = implementation.createDocument(NAMESPACE, 'template', null);\n        try {\n          doc.documentElement.innerHTML = IS_EMPTY_INPUT ? emptyHTML : dirtyPayload;\n        } catch (_) {// Syntax error if dirtyPayload is invalid xml\n        }\n      }\n      var body = doc.body || doc.documentElement;\n      if (dirty && leadingWhitespace) {\n        body.insertBefore(document.createTextNode(leadingWhitespace), body.childNodes[0] || null);\n      }\n      /* Work on whole document or just its body */\n\n      if (NAMESPACE === HTML_NAMESPACE) {\n        return getElementsByTagName.call(doc, WHOLE_DOCUMENT ? 'html' : 'body')[0];\n      }\n      return WHOLE_DOCUMENT ? doc.documentElement : body;\n    };\n    /**\n     * _createIterator\n     *\n     * @param  {Document} root document/fragment to create iterator for\n     * @return {Iterator} iterator instance\n     */\n\n    var _createIterator = function _createIterator(root) {\n      return createNodeIterator.call(root.ownerDocument || root, root,\n      // eslint-disable-next-line no-bitwise\n      NodeFilter.SHOW_ELEMENT | NodeFilter.SHOW_COMMENT | NodeFilter.SHOW_TEXT, null, false);\n    };\n    /**\n     * _isClobbered\n     *\n     * @param  {Node} elm element to check for clobbering attacks\n     * @return {Boolean} true if clobbered, false if safe\n     */\n\n    var _isClobbered = function _isClobbered(elm) {\n      return elm instanceof HTMLFormElement && (typeof elm.nodeName !== 'string' || typeof elm.textContent !== 'string' || typeof elm.removeChild !== 'function' || !(elm.attributes instanceof NamedNodeMap) || typeof elm.removeAttribute !== 'function' || typeof elm.setAttribute !== 'function' || typeof elm.namespaceURI !== 'string' || typeof elm.insertBefore !== 'function' || typeof elm.hasChildNodes !== 'function');\n    };\n    /**\n     * _isNode\n     *\n     * @param  {Node} obj object to check whether it's a DOM node\n     * @return {Boolean} true is object is a DOM node\n     */\n\n    var _isNode = function _isNode(object) {\n      return _typeof(Node) === 'object' ? object instanceof Node : object && _typeof(object) === 'object' && typeof object.nodeType === 'number' && typeof object.nodeName === 'string';\n    };\n    /**\n     * _executeHook\n     * Execute user configurable hooks\n     *\n     * @param  {String} entryPoint  Name of the hook's entry point\n     * @param  {Node} currentNode node to work on with the hook\n     * @param  {Object} data additional hook parameters\n     */\n\n    var _executeHook = function _executeHook(entryPoint, currentNode, data) {\n      if (!hooks[entryPoint]) {\n        return;\n      }\n      arrayForEach(hooks[entryPoint], function (hook) {\n        hook.call(DOMPurify, currentNode, data, CONFIG);\n      });\n    };\n    /**\n     * _sanitizeElements\n     *\n     * @protect nodeName\n     * @protect textContent\n     * @protect removeChild\n     *\n     * @param   {Node} currentNode to check for permission to exist\n     * @return  {Boolean} true if node was killed, false if left alive\n     */\n\n    var _sanitizeElements = function _sanitizeElements(currentNode) {\n      var content;\n      /* Execute a hook if present */\n\n      _executeHook('beforeSanitizeElements', currentNode, null);\n      /* Check if element is clobbered or can clobber */\n\n      if (_isClobbered(currentNode)) {\n        _forceRemove(currentNode);\n        return true;\n      }\n      /* Check if tagname contains Unicode */\n\n      if (regExpTest(/[\\u0080-\\uFFFF]/, currentNode.nodeName)) {\n        _forceRemove(currentNode);\n        return true;\n      }\n      /* Now let's check the element's type and name */\n\n      var tagName = transformCaseFunc(currentNode.nodeName);\n      /* Execute a hook if present */\n\n      _executeHook('uponSanitizeElement', currentNode, {\n        tagName: tagName,\n        allowedTags: ALLOWED_TAGS\n      });\n      /* Detect mXSS attempts abusing namespace confusion */\n\n      if (currentNode.hasChildNodes() && !_isNode(currentNode.firstElementChild) && (!_isNode(currentNode.content) || !_isNode(currentNode.content.firstElementChild)) && regExpTest(/<[/\\w]/g, currentNode.innerHTML) && regExpTest(/<[/\\w]/g, currentNode.textContent)) {\n        _forceRemove(currentNode);\n        return true;\n      }\n      /* Mitigate a problem with templates inside select */\n\n      if (tagName === 'select' && regExpTest(/<template/i, currentNode.innerHTML)) {\n        _forceRemove(currentNode);\n        return true;\n      }\n      /* Remove element if anything forbids its presence */\n\n      if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n        /* Check if we have a custom element to handle */\n        if (!FORBID_TAGS[tagName] && _basicCustomElementTest(tagName)) {\n          if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)) return false;\n          if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)) return false;\n        }\n        /* Keep content except for bad-listed elements */\n\n        if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n          var parentNode = getParentNode(currentNode) || currentNode.parentNode;\n          var childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n          if (childNodes && parentNode) {\n            var childCount = childNodes.length;\n            for (var i = childCount - 1; i >= 0; --i) {\n              parentNode.insertBefore(cloneNode(childNodes[i], true), getNextSibling(currentNode));\n            }\n          }\n        }\n        _forceRemove(currentNode);\n        return true;\n      }\n      /* Check whether element has a valid namespace */\n\n      if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n        _forceRemove(currentNode);\n        return true;\n      }\n      /* Make sure that older browsers don't get fallback-tag mXSS */\n\n      if ((tagName === 'noscript' || tagName === 'noembed' || tagName === 'noframes') && regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)) {\n        _forceRemove(currentNode);\n        return true;\n      }\n      /* Sanitize element content to be template-safe */\n\n      if (SAFE_FOR_TEMPLATES && currentNode.nodeType === 3) {\n        /* Get the element's text content */\n        content = currentNode.textContent;\n        content = stringReplace(content, MUSTACHE_EXPR$1, ' ');\n        content = stringReplace(content, ERB_EXPR$1, ' ');\n        content = stringReplace(content, TMPLIT_EXPR$1, ' ');\n        if (currentNode.textContent !== content) {\n          arrayPush(DOMPurify.removed, {\n            element: currentNode.cloneNode()\n          });\n          currentNode.textContent = content;\n        }\n      }\n      /* Execute a hook if present */\n\n      _executeHook('afterSanitizeElements', currentNode, null);\n      return false;\n    };\n    /**\n     * _isValidAttribute\n     *\n     * @param  {string} lcTag Lowercase tag name of containing element.\n     * @param  {string} lcName Lowercase attribute name.\n     * @param  {string} value Attribute value.\n     * @return {Boolean} Returns true if `value` is valid, otherwise false.\n     */\n    // eslint-disable-next-line complexity\n\n    var _isValidAttribute = function _isValidAttribute(lcTag, lcName, value) {\n      /* Make sure attribute cannot clobber */\n      if (SANITIZE_DOM && (lcName === 'id' || lcName === 'name') && (value in document || value in formElement)) {\n        return false;\n      }\n      /* Allow valid data-* attributes: At least one character after \"-\"\n          (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n          XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n          We don't need to check the value; it's always URI safe. */\n\n      if (ALLOW_DATA_ATTR && !FORBID_ATTR[lcName] && regExpTest(DATA_ATTR$1, lcName)) ;else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR$1, lcName)) ;else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n        if (\n        // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n        // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n        _basicCustomElementTest(lcTag) && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag)) && (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName) || CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)) ||\n        // Alternative, second condition checks if it's an `is`-attribute, AND\n        // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        lcName === 'is' && CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))) ;else {\n          return false;\n        }\n        /* Check value is safe. First, is attr inert? If so, is safe */\n      } else if (URI_SAFE_ATTRIBUTES[lcName]) ;else if (regExpTest(IS_ALLOWED_URI$1, stringReplace(value, ATTR_WHITESPACE$1, ''))) ;else if ((lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') && lcTag !== 'script' && stringIndexOf(value, 'data:') === 0 && DATA_URI_TAGS[lcTag]) ;else if (ALLOW_UNKNOWN_PROTOCOLS && !regExpTest(IS_SCRIPT_OR_DATA$1, stringReplace(value, ATTR_WHITESPACE$1, ''))) ;else if (value) {\n        return false;\n      } else ;\n      return true;\n    };\n    /**\n     * _basicCustomElementCheck\n     * checks if at least one dash is included in tagName, and it's not the first char\n     * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n     * @param {string} tagName name of the tag of the node to sanitize\n     */\n\n    var _basicCustomElementTest = function _basicCustomElementTest(tagName) {\n      return tagName.indexOf('-') > 0;\n    };\n    /**\n     * _sanitizeAttributes\n     *\n     * @protect attributes\n     * @protect nodeName\n     * @protect removeAttribute\n     * @protect setAttribute\n     *\n     * @param  {Node} currentNode to sanitize\n     */\n\n    var _sanitizeAttributes = function _sanitizeAttributes(currentNode) {\n      var attr;\n      var value;\n      var lcName;\n      var l;\n      /* Execute a hook if present */\n\n      _executeHook('beforeSanitizeAttributes', currentNode, null);\n      var attributes = currentNode.attributes;\n      /* Check if we have attributes; if not we might have a text node */\n\n      if (!attributes) {\n        return;\n      }\n      var hookEvent = {\n        attrName: '',\n        attrValue: '',\n        keepAttr: true,\n        allowedAttributes: ALLOWED_ATTR\n      };\n      l = attributes.length;\n      /* Go backwards over all attributes; safely remove bad ones */\n\n      while (l--) {\n        attr = attributes[l];\n        var _attr = attr,\n          name = _attr.name,\n          namespaceURI = _attr.namespaceURI;\n        value = name === 'value' ? attr.value : stringTrim(attr.value);\n        lcName = transformCaseFunc(name);\n        /* Execute a hook if present */\n\n        hookEvent.attrName = lcName;\n        hookEvent.attrValue = value;\n        hookEvent.keepAttr = true;\n        hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n\n        _executeHook('uponSanitizeAttribute', currentNode, hookEvent);\n        value = hookEvent.attrValue;\n        /* Did the hooks approve of the attribute? */\n\n        if (hookEvent.forceKeepAttr) {\n          continue;\n        }\n        /* Remove attribute */\n\n        _removeAttribute(name, currentNode);\n        /* Did the hooks approve of the attribute? */\n\n        if (!hookEvent.keepAttr) {\n          continue;\n        }\n        /* Work around a security issue in jQuery 3.0 */\n\n        if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n          _removeAttribute(name, currentNode);\n          continue;\n        }\n        /* Sanitize attribute content to be template-safe */\n\n        if (SAFE_FOR_TEMPLATES) {\n          value = stringReplace(value, MUSTACHE_EXPR$1, ' ');\n          value = stringReplace(value, ERB_EXPR$1, ' ');\n          value = stringReplace(value, TMPLIT_EXPR$1, ' ');\n        }\n        /* Is `value` valid for this attribute? */\n\n        var lcTag = transformCaseFunc(currentNode.nodeName);\n        if (!_isValidAttribute(lcTag, lcName, value)) {\n          continue;\n        }\n        /* Full DOM Clobbering protection via namespace isolation,\n         * Prefix id and name attributes with `user-content-`\n         */\n\n        if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n          // Remove the attribute with this value\n          _removeAttribute(name, currentNode); // Prefix the value and later re-create the attribute with the sanitized value\n\n          value = SANITIZE_NAMED_PROPS_PREFIX + value;\n        }\n        /* Handle attributes that require Trusted Types */\n\n        if (trustedTypesPolicy && _typeof(trustedTypes) === 'object' && typeof trustedTypes.getAttributeType === 'function') {\n          if (namespaceURI) ;else {\n            switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n              case 'TrustedHTML':\n                {\n                  value = trustedTypesPolicy.createHTML(value);\n                  break;\n                }\n              case 'TrustedScriptURL':\n                {\n                  value = trustedTypesPolicy.createScriptURL(value);\n                  break;\n                }\n            }\n          }\n        }\n        /* Handle invalid data-* attribute set by try-catching it */\n\n        try {\n          if (namespaceURI) {\n            currentNode.setAttributeNS(namespaceURI, name, value);\n          } else {\n            /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n            currentNode.setAttribute(name, value);\n          }\n          arrayPop(DOMPurify.removed);\n        } catch (_) {}\n      }\n      /* Execute a hook if present */\n\n      _executeHook('afterSanitizeAttributes', currentNode, null);\n    };\n    /**\n     * _sanitizeShadowDOM\n     *\n     * @param  {DocumentFragment} fragment to iterate over recursively\n     */\n\n    var _sanitizeShadowDOM = function _sanitizeShadowDOM(fragment) {\n      var shadowNode;\n      var shadowIterator = _createIterator(fragment);\n      /* Execute a hook if present */\n\n      _executeHook('beforeSanitizeShadowDOM', fragment, null);\n      while (shadowNode = shadowIterator.nextNode()) {\n        /* Execute a hook if present */\n        _executeHook('uponSanitizeShadowNode', shadowNode, null);\n        /* Sanitize tags and elements */\n\n        if (_sanitizeElements(shadowNode)) {\n          continue;\n        }\n        /* Deep shadow DOM detected */\n\n        if (shadowNode.content instanceof DocumentFragment) {\n          _sanitizeShadowDOM(shadowNode.content);\n        }\n        /* Check attributes, sanitize if necessary */\n\n        _sanitizeAttributes(shadowNode);\n      }\n      /* Execute a hook if present */\n\n      _executeHook('afterSanitizeShadowDOM', fragment, null);\n    };\n    /**\n     * Sanitize\n     * Public method providing core sanitation functionality\n     *\n     * @param {String|Node} dirty string or DOM node\n     * @param {Object} configuration object\n     */\n    // eslint-disable-next-line complexity\n\n    DOMPurify.sanitize = function (dirty) {\n      var cfg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var body;\n      var importedNode;\n      var currentNode;\n      var oldNode;\n      var returnNode;\n      /* Make sure we have a string to sanitize.\n        DO NOT return early, as this will return the wrong type if\n        the user has requested a DOM object rather than a string */\n\n      IS_EMPTY_INPUT = !dirty;\n      if (IS_EMPTY_INPUT) {\n        dirty = '<!-->';\n      }\n      /* Stringify, in case dirty is an object */\n\n      if (typeof dirty !== 'string' && !_isNode(dirty)) {\n        if (typeof dirty.toString === 'function') {\n          dirty = dirty.toString();\n          if (typeof dirty !== 'string') {\n            throw typeErrorCreate('dirty is not a string, aborting');\n          }\n        } else {\n          throw typeErrorCreate('toString is not a function');\n        }\n      }\n      /* Check we can run. Otherwise fall back or ignore */\n\n      if (!DOMPurify.isSupported) {\n        if (_typeof(window.toStaticHTML) === 'object' || typeof window.toStaticHTML === 'function') {\n          if (typeof dirty === 'string') {\n            return window.toStaticHTML(dirty);\n          }\n          if (_isNode(dirty)) {\n            return window.toStaticHTML(dirty.outerHTML);\n          }\n        }\n        return dirty;\n      }\n      /* Assign config vars */\n\n      if (!SET_CONFIG) {\n        _parseConfig(cfg);\n      }\n      /* Clean up removed elements */\n\n      DOMPurify.removed = [];\n      /* Check if dirty is correctly typed for IN_PLACE */\n\n      if (typeof dirty === 'string') {\n        IN_PLACE = false;\n      }\n      if (IN_PLACE) {\n        /* Do some early pre-sanitization to avoid unsafe root nodes */\n        if (dirty.nodeName) {\n          var tagName = transformCaseFunc(dirty.nodeName);\n          if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n            throw typeErrorCreate('root node is forbidden and cannot be sanitized in-place');\n          }\n        }\n      } else if (dirty instanceof Node) {\n        /* If dirty is a DOM element, append to an empty document to avoid\n           elements being stripped by the parser */\n        body = _initDocument('<!---->');\n        importedNode = body.ownerDocument.importNode(dirty, true);\n        if (importedNode.nodeType === 1 && importedNode.nodeName === 'BODY') {\n          /* Node is already a body, use as is */\n          body = importedNode;\n        } else if (importedNode.nodeName === 'HTML') {\n          body = importedNode;\n        } else {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          body.appendChild(importedNode);\n        }\n      } else {\n        /* Exit directly if we have nothing to do */\n        if (!RETURN_DOM && !SAFE_FOR_TEMPLATES && !WHOLE_DOCUMENT &&\n        // eslint-disable-next-line unicorn/prefer-includes\n        dirty.indexOf('<') === -1) {\n          return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(dirty) : dirty;\n        }\n        /* Initialize the document to work on */\n\n        body = _initDocument(dirty);\n        /* Check we have a DOM node from the data */\n\n        if (!body) {\n          return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n        }\n      }\n      /* Remove first element node (ours) if FORCE_BODY is set */\n\n      if (body && FORCE_BODY) {\n        _forceRemove(body.firstChild);\n      }\n      /* Get node iterator */\n\n      var nodeIterator = _createIterator(IN_PLACE ? dirty : body);\n      /* Now start iterating over the created document */\n\n      while (currentNode = nodeIterator.nextNode()) {\n        /* Fix IE's strange behavior with manipulated textNodes #89 */\n        if (currentNode.nodeType === 3 && currentNode === oldNode) {\n          continue;\n        }\n        /* Sanitize tags and elements */\n\n        if (_sanitizeElements(currentNode)) {\n          continue;\n        }\n        /* Shadow DOM detected, sanitize it */\n\n        if (currentNode.content instanceof DocumentFragment) {\n          _sanitizeShadowDOM(currentNode.content);\n        }\n        /* Check attributes, sanitize if necessary */\n\n        _sanitizeAttributes(currentNode);\n        oldNode = currentNode;\n      }\n      oldNode = null;\n      /* If we sanitized `dirty` in-place, return it. */\n\n      if (IN_PLACE) {\n        return dirty;\n      }\n      /* Return sanitized string or DOM */\n\n      if (RETURN_DOM) {\n        if (RETURN_DOM_FRAGMENT) {\n          returnNode = createDocumentFragment.call(body.ownerDocument);\n          while (body.firstChild) {\n            // eslint-disable-next-line unicorn/prefer-dom-node-append\n            returnNode.appendChild(body.firstChild);\n          }\n        } else {\n          returnNode = body;\n        }\n        if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmod) {\n          /*\n            AdoptNode() is not used because internal state is not reset\n            (e.g. the past names map of a HTMLFormElement), this is safe\n            in theory but we would rather not risk another attack vector.\n            The state that is cloned by importNode() is explicitly defined\n            by the specs.\n          */\n          returnNode = importNode.call(originalDocument, returnNode, true);\n        }\n        return returnNode;\n      }\n      var serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n      /* Serialize doctype if allowed */\n\n      if (WHOLE_DOCUMENT && ALLOWED_TAGS['!doctype'] && body.ownerDocument && body.ownerDocument.doctype && body.ownerDocument.doctype.name && regExpTest(DOCTYPE_NAME, body.ownerDocument.doctype.name)) {\n        serializedHTML = '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n      }\n      /* Sanitize final string template-safe */\n\n      if (SAFE_FOR_TEMPLATES) {\n        serializedHTML = stringReplace(serializedHTML, MUSTACHE_EXPR$1, ' ');\n        serializedHTML = stringReplace(serializedHTML, ERB_EXPR$1, ' ');\n        serializedHTML = stringReplace(serializedHTML, TMPLIT_EXPR$1, ' ');\n      }\n      return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(serializedHTML) : serializedHTML;\n    };\n    /**\n     * Public method to set the configuration once\n     * setConfig\n     *\n     * @param {Object} cfg configuration object\n     */\n\n    DOMPurify.setConfig = function (cfg) {\n      _parseConfig(cfg);\n      SET_CONFIG = true;\n    };\n    /**\n     * Public method to remove the configuration\n     * clearConfig\n     *\n     */\n\n    DOMPurify.clearConfig = function () {\n      CONFIG = null;\n      SET_CONFIG = false;\n    };\n    /**\n     * Public method to check if an attribute value is valid.\n     * Uses last set config, if any. Otherwise, uses config defaults.\n     * isValidAttribute\n     *\n     * @param  {string} tag Tag name of containing element.\n     * @param  {string} attr Attribute name.\n     * @param  {string} value Attribute value.\n     * @return {Boolean} Returns true if `value` is valid. Otherwise, returns false.\n     */\n\n    DOMPurify.isValidAttribute = function (tag, attr, value) {\n      /* Initialize shared config vars if necessary. */\n      if (!CONFIG) {\n        _parseConfig({});\n      }\n      var lcTag = transformCaseFunc(tag);\n      var lcName = transformCaseFunc(attr);\n      return _isValidAttribute(lcTag, lcName, value);\n    };\n    /**\n     * AddHook\n     * Public method to add DOMPurify hooks\n     *\n     * @param {String} entryPoint entry point for the hook to add\n     * @param {Function} hookFunction function to execute\n     */\n\n    DOMPurify.addHook = function (entryPoint, hookFunction) {\n      if (typeof hookFunction !== 'function') {\n        return;\n      }\n      hooks[entryPoint] = hooks[entryPoint] || [];\n      arrayPush(hooks[entryPoint], hookFunction);\n    };\n    /**\n     * RemoveHook\n     * Public method to remove a DOMPurify hook at a given entryPoint\n     * (pops it from the stack of hooks if more are present)\n     *\n     * @param {String} entryPoint entry point for the hook to remove\n     * @return {Function} removed(popped) hook\n     */\n\n    DOMPurify.removeHook = function (entryPoint) {\n      if (hooks[entryPoint]) {\n        return arrayPop(hooks[entryPoint]);\n      }\n    };\n    /**\n     * RemoveHooks\n     * Public method to remove all DOMPurify hooks at a given entryPoint\n     *\n     * @param  {String} entryPoint entry point for the hooks to remove\n     */\n\n    DOMPurify.removeHooks = function (entryPoint) {\n      if (hooks[entryPoint]) {\n        hooks[entryPoint] = [];\n      }\n    };\n    /**\n     * RemoveAllHooks\n     * Public method to remove all DOMPurify hooks\n     *\n     */\n\n    DOMPurify.removeAllHooks = function () {\n      hooks = {};\n    };\n    return DOMPurify;\n  }\n  var purify = createDOMPurify();\n  return purify;\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "define", "amd", "globalThis", "self", "DOMPurify", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "_setPrototypeOf", "o", "p", "Object", "setPrototypeOf", "__proto__", "_isNativeReflectConstruct", "Reflect", "construct", "sham", "Proxy", "Boolean", "valueOf", "call", "e", "_construct", "Parent", "args", "Class", "a", "push", "apply", "<PERSON><PERSON><PERSON><PERSON>", "Function", "bind", "instance", "arguments", "_toConsumableArray", "arr", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "Array", "isArray", "_arrayLikeToArray", "iter", "from", "minLen", "n", "toString", "slice", "name", "test", "len", "length", "i", "arr2", "TypeError", "hasOwnProperty", "isFrozen", "getPrototypeOf", "getOwnPropertyDescriptor", "freeze", "seal", "create", "_ref", "fun", "thisValue", "x", "Func", "arrayForEach", "unapply", "for<PERSON>ach", "arrayPop", "pop", "arrayPush", "stringToLowerCase", "String", "toLowerCase", "stringToString", "stringMatch", "match", "stringReplace", "replace", "stringIndexOf", "indexOf", "stringTrim", "trim", "regExpTest", "RegExp", "typeErrorCreate", "unconstruct", "func", "thisArg", "_len", "_key", "_len2", "_key2", "addToSet", "set", "array", "transformCaseFunc", "_transformCaseFunc", "l", "element", "lcElement", "clone", "object", "newObject", "property", "lookupGetter", "prop", "desc", "get", "value", "fallback<PERSON><PERSON><PERSON>", "console", "warn", "html$1", "svg$1", "svgFilters", "svgDisallowed", "mathMl$1", "mathMlDisallowed", "text", "html", "svg", "mathMl", "xml", "MUSTACHE_EXPR", "ERB_EXPR", "TMPLIT_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_ALLOWED_URI", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "DOCTYPE_NAME", "getGlobal", "window", "_createTrustedTypesPolicy", "trustedTypes", "document", "createPolicy", "suffix", "ATTR_NAME", "currentScript", "hasAttribute", "getAttribute", "policyName", "createHTML", "createScriptURL", "scriptUrl", "_", "createDOMPurify", "undefined", "root", "version", "removed", "nodeType", "isSupported", "originalDocument", "DocumentFragment", "HTMLTemplateElement", "Node", "Element", "Node<PERSON><PERSON><PERSON>", "_window$NamedNodeMap", "NamedNodeMap", "MozNamedAttrMap", "HTMLFormElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElementPrototype", "cloneNode", "getNextSibling", "getChildNodes", "getParentNode", "template", "createElement", "content", "ownerDocument", "trustedTypesPolicy", "emptyHTML", "_document", "implementation", "createNodeIterator", "createDocumentFragment", "getElementsByTagName", "importNode", "documentMode", "hooks", "createHTMLDocument", "MUSTACHE_EXPR$1", "ERB_EXPR$1", "TMPLIT_EXPR$1", "DATA_ATTR$1", "ARIA_ATTR$1", "IS_SCRIPT_OR_DATA$1", "ATTR_WHITESPACE$1", "IS_ALLOWED_URI$1", "ALLOWED_TAGS", "DEFAULT_ALLOWED_TAGS", "concat", "ALLOWED_ATTR", "DEFAULT_ALLOWED_ATTR", "CUSTOM_ELEMENT_HANDLING", "tagNameCheck", "writable", "configurable", "enumerable", "attributeNameCheck", "allowCustomizedBuiltInElements", "FORBID_TAGS", "FORBID_ATTR", "ALLOW_ARIA_ATTR", "ALLOW_DATA_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "ALLOW_SELF_CLOSE_IN_ATTR", "SAFE_FOR_TEMPLATES", "WHOLE_DOCUMENT", "SET_CONFIG", "FORCE_BODY", "RETURN_DOM", "RETURN_DOM_FRAGMENT", "RETURN_TRUSTED_TYPE", "SANITIZE_DOM", "SANITIZE_NAMED_PROPS", "SANITIZE_NAMED_PROPS_PREFIX", "KEEP_CONTENT", "IN_PLACE", "USE_PROFILES", "FORBID_CONTENTS", "DEFAULT_FORBID_CONTENTS", "DATA_URI_TAGS", "DEFAULT_DATA_URI_TAGS", "URI_SAFE_ATTRIBUTES", "DEFAULT_URI_SAFE_ATTRIBUTES", "MATHML_NAMESPACE", "SVG_NAMESPACE", "HTML_NAMESPACE", "NAMESPACE", "IS_EMPTY_INPUT", "ALLOWED_NAMESPACES", "DEFAULT_ALLOWED_NAMESPACES", "PARSER_MEDIA_TYPE", "SUPPORTED_PARSER_MEDIA_TYPES", "DEFAULT_PARSER_MEDIA_TYPE", "CONFIG", "formElement", "isRegexOrFunction", "testValue", "_parseConfig", "cfg", "ADD_URI_SAFE_ATTR", "ADD_DATA_URI_TAGS", "ALLOWED_URI_REGEXP", "ADD_TAGS", "ADD_ATTR", "table", "tbody", "MATHML_TEXT_INTEGRATION_POINTS", "HTML_INTEGRATION_POINTS", "COMMON_SVG_AND_HTML_ELEMENTS", "ALL_SVG_TAGS", "ALL_MATHML_TAGS", "_checkValidNamespace", "parent", "tagName", "namespaceURI", "parentTagName", "_forceRemove", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "outerHTML", "remove", "_removeAttribute", "attribute", "getAttributeNode", "removeAttribute", "setAttribute", "_initDocument", "dirty", "doc", "leadingWhitespace", "matches", "dirtyPayload", "parseFromString", "documentElement", "createDocument", "innerHTML", "body", "insertBefore", "createTextNode", "childNodes", "_createIterator", "SHOW_ELEMENT", "SHOW_COMMENT", "SHOW_TEXT", "_isClobbered", "elm", "nodeName", "textContent", "attributes", "hasChildNodes", "_isNode", "_executeHook", "entryPoint", "currentNode", "data", "hook", "_sanitizeElements", "allowedTags", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "_basicCustomElementTest", "childCount", "_isValidAttribute", "lcTag", "lcName", "_sanitizeAttributes", "attr", "hookEvent", "attrName", "attrValue", "keepAttr", "allowedAttributes", "_attr", "forceKeepAttr", "getAttributeType", "setAttributeNS", "_sanitizeShadowDOM", "fragment", "shadowNode", "shadowIterator", "nextNode", "sanitize", "importedNode", "oldNode", "returnNode", "toStaticHTML", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nodeIterator", "shadowroot", "shadowrootmod", "serializedHTML", "doctype", "setConfig", "clearConfig", "isValidAttribute", "tag", "addHook", "hookFunction", "removeH<PERSON>", "removeHooks", "removeAllHooks", "purify"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/dompurify/dist/purify.js"], "sourcesContent": ["/*! @license DOMPurify 2.4.7 | (c) Cure<PERSON> and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/2.4.7/LICENSE */\n\n(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :\n  typeof define === 'function' && define.amd ? define(factory) :\n  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.DOMPurify = factory());\n})(this, (function () { 'use strict';\n\n  function _typeof(obj) {\n    \"@babel/helpers - typeof\";\n\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n      return typeof obj;\n    } : function (obj) {\n      return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    }, _typeof(obj);\n  }\n\n  function _setPrototypeOf(o, p) {\n    _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n      o.__proto__ = p;\n      return o;\n    };\n\n    return _setPrototypeOf(o, p);\n  }\n\n  function _isNativeReflectConstruct() {\n    if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n    if (Reflect.construct.sham) return false;\n    if (typeof Proxy === \"function\") return true;\n\n    try {\n      Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n      return true;\n    } catch (e) {\n      return false;\n    }\n  }\n\n  function _construct(Parent, args, Class) {\n    if (_isNativeReflectConstruct()) {\n      _construct = Reflect.construct;\n    } else {\n      _construct = function _construct(Parent, args, Class) {\n        var a = [null];\n        a.push.apply(a, args);\n        var Constructor = Function.bind.apply(Parent, a);\n        var instance = new Constructor();\n        if (Class) _setPrototypeOf(instance, Class.prototype);\n        return instance;\n      };\n    }\n\n    return _construct.apply(null, arguments);\n  }\n\n  function _toConsumableArray(arr) {\n    return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n  }\n\n  function _arrayWithoutHoles(arr) {\n    if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n  }\n\n  function _iterableToArray(iter) {\n    if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n  }\n\n  function _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n  }\n\n  function _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n\n    for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n    return arr2;\n  }\n\n  function _nonIterableSpread() {\n    throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n\n  var hasOwnProperty = Object.hasOwnProperty,\n      setPrototypeOf = Object.setPrototypeOf,\n      isFrozen = Object.isFrozen,\n      getPrototypeOf = Object.getPrototypeOf,\n      getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n  var freeze = Object.freeze,\n      seal = Object.seal,\n      create = Object.create; // eslint-disable-line import/no-mutable-exports\n\n  var _ref = typeof Reflect !== 'undefined' && Reflect,\n      apply = _ref.apply,\n      construct = _ref.construct;\n\n  if (!apply) {\n    apply = function apply(fun, thisValue, args) {\n      return fun.apply(thisValue, args);\n    };\n  }\n\n  if (!freeze) {\n    freeze = function freeze(x) {\n      return x;\n    };\n  }\n\n  if (!seal) {\n    seal = function seal(x) {\n      return x;\n    };\n  }\n\n  if (!construct) {\n    construct = function construct(Func, args) {\n      return _construct(Func, _toConsumableArray(args));\n    };\n  }\n\n  var arrayForEach = unapply(Array.prototype.forEach);\n  var arrayPop = unapply(Array.prototype.pop);\n  var arrayPush = unapply(Array.prototype.push);\n  var stringToLowerCase = unapply(String.prototype.toLowerCase);\n  var stringToString = unapply(String.prototype.toString);\n  var stringMatch = unapply(String.prototype.match);\n  var stringReplace = unapply(String.prototype.replace);\n  var stringIndexOf = unapply(String.prototype.indexOf);\n  var stringTrim = unapply(String.prototype.trim);\n  var regExpTest = unapply(RegExp.prototype.test);\n  var typeErrorCreate = unconstruct(TypeError);\n  function unapply(func) {\n    return function (thisArg) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n\n      return apply(func, thisArg, args);\n    };\n  }\n  function unconstruct(func) {\n    return function () {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n\n      return construct(func, args);\n    };\n  }\n  /* Add properties to a lookup table */\n\n  function addToSet(set, array, transformCaseFunc) {\n    var _transformCaseFunc;\n\n    transformCaseFunc = (_transformCaseFunc = transformCaseFunc) !== null && _transformCaseFunc !== void 0 ? _transformCaseFunc : stringToLowerCase;\n\n    if (setPrototypeOf) {\n      // Make 'in' and truthy checks like Boolean(set.constructor)\n      // independent of any properties defined on Object.prototype.\n      // Prevent prototype setters from intercepting set as a this value.\n      setPrototypeOf(set, null);\n    }\n\n    var l = array.length;\n\n    while (l--) {\n      var element = array[l];\n\n      if (typeof element === 'string') {\n        var lcElement = transformCaseFunc(element);\n\n        if (lcElement !== element) {\n          // Config presets (e.g. tags.js, attrs.js) are immutable.\n          if (!isFrozen(array)) {\n            array[l] = lcElement;\n          }\n\n          element = lcElement;\n        }\n      }\n\n      set[element] = true;\n    }\n\n    return set;\n  }\n  /* Shallow clone an object */\n\n  function clone(object) {\n    var newObject = create(null);\n    var property;\n\n    for (property in object) {\n      if (apply(hasOwnProperty, object, [property]) === true) {\n        newObject[property] = object[property];\n      }\n    }\n\n    return newObject;\n  }\n  /* IE10 doesn't support __lookupGetter__ so lets'\n   * simulate it. It also automatically checks\n   * if the prop is function or getter and behaves\n   * accordingly. */\n\n  function lookupGetter(object, prop) {\n    while (object !== null) {\n      var desc = getOwnPropertyDescriptor(object, prop);\n\n      if (desc) {\n        if (desc.get) {\n          return unapply(desc.get);\n        }\n\n        if (typeof desc.value === 'function') {\n          return unapply(desc.value);\n        }\n      }\n\n      object = getPrototypeOf(object);\n    }\n\n    function fallbackValue(element) {\n      console.warn('fallback value for', element);\n      return null;\n    }\n\n    return fallbackValue;\n  }\n\n  var html$1 = freeze(['a', 'abbr', 'acronym', 'address', 'area', 'article', 'aside', 'audio', 'b', 'bdi', 'bdo', 'big', 'blink', 'blockquote', 'body', 'br', 'button', 'canvas', 'caption', 'center', 'cite', 'code', 'col', 'colgroup', 'content', 'data', 'datalist', 'dd', 'decorator', 'del', 'details', 'dfn', 'dialog', 'dir', 'div', 'dl', 'dt', 'element', 'em', 'fieldset', 'figcaption', 'figure', 'font', 'footer', 'form', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'head', 'header', 'hgroup', 'hr', 'html', 'i', 'img', 'input', 'ins', 'kbd', 'label', 'legend', 'li', 'main', 'map', 'mark', 'marquee', 'menu', 'menuitem', 'meter', 'nav', 'nobr', 'ol', 'optgroup', 'option', 'output', 'p', 'picture', 'pre', 'progress', 'q', 'rp', 'rt', 'ruby', 's', 'samp', 'section', 'select', 'shadow', 'small', 'source', 'spacer', 'span', 'strike', 'strong', 'style', 'sub', 'summary', 'sup', 'table', 'tbody', 'td', 'template', 'textarea', 'tfoot', 'th', 'thead', 'time', 'tr', 'track', 'tt', 'u', 'ul', 'var', 'video', 'wbr']); // SVG\n\n  var svg$1 = freeze(['svg', 'a', 'altglyph', 'altglyphdef', 'altglyphitem', 'animatecolor', 'animatemotion', 'animatetransform', 'circle', 'clippath', 'defs', 'desc', 'ellipse', 'filter', 'font', 'g', 'glyph', 'glyphref', 'hkern', 'image', 'line', 'lineargradient', 'marker', 'mask', 'metadata', 'mpath', 'path', 'pattern', 'polygon', 'polyline', 'radialgradient', 'rect', 'stop', 'style', 'switch', 'symbol', 'text', 'textpath', 'title', 'tref', 'tspan', 'view', 'vkern']);\n  var svgFilters = freeze(['feBlend', 'feColorMatrix', 'feComponentTransfer', 'feComposite', 'feConvolveMatrix', 'feDiffuseLighting', 'feDisplacementMap', 'feDistantLight', 'feFlood', 'feFuncA', 'feFuncB', 'feFuncG', 'feFuncR', 'feGaussianBlur', 'feImage', 'feMerge', 'feMergeNode', 'feMorphology', 'feOffset', 'fePointLight', 'feSpecularLighting', 'feSpotLight', 'feTile', 'feTurbulence']); // List of SVG elements that are disallowed by default.\n  // We still need to know them so that we can do namespace\n  // checks properly in case one wants to add them to\n  // allow-list.\n\n  var svgDisallowed = freeze(['animate', 'color-profile', 'cursor', 'discard', 'fedropshadow', 'font-face', 'font-face-format', 'font-face-name', 'font-face-src', 'font-face-uri', 'foreignobject', 'hatch', 'hatchpath', 'mesh', 'meshgradient', 'meshpatch', 'meshrow', 'missing-glyph', 'script', 'set', 'solidcolor', 'unknown', 'use']);\n  var mathMl$1 = freeze(['math', 'menclose', 'merror', 'mfenced', 'mfrac', 'mglyph', 'mi', 'mlabeledtr', 'mmultiscripts', 'mn', 'mo', 'mover', 'mpadded', 'mphantom', 'mroot', 'mrow', 'ms', 'mspace', 'msqrt', 'mstyle', 'msub', 'msup', 'msubsup', 'mtable', 'mtd', 'mtext', 'mtr', 'munder', 'munderover']); // Similarly to SVG, we want to know all MathML elements,\n  // even those that we disallow by default.\n\n  var mathMlDisallowed = freeze(['maction', 'maligngroup', 'malignmark', 'mlongdiv', 'mscarries', 'mscarry', 'msgroup', 'mstack', 'msline', 'msrow', 'semantics', 'annotation', 'annotation-xml', 'mprescripts', 'none']);\n  var text = freeze(['#text']);\n\n  var html = freeze(['accept', 'action', 'align', 'alt', 'autocapitalize', 'autocomplete', 'autopictureinpicture', 'autoplay', 'background', 'bgcolor', 'border', 'capture', 'cellpadding', 'cellspacing', 'checked', 'cite', 'class', 'clear', 'color', 'cols', 'colspan', 'controls', 'controlslist', 'coords', 'crossorigin', 'datetime', 'decoding', 'default', 'dir', 'disabled', 'disablepictureinpicture', 'disableremoteplayback', 'download', 'draggable', 'enctype', 'enterkeyhint', 'face', 'for', 'headers', 'height', 'hidden', 'high', 'href', 'hreflang', 'id', 'inputmode', 'integrity', 'ismap', 'kind', 'label', 'lang', 'list', 'loading', 'loop', 'low', 'max', 'maxlength', 'media', 'method', 'min', 'minlength', 'multiple', 'muted', 'name', 'nonce', 'noshade', 'novalidate', 'nowrap', 'open', 'optimum', 'pattern', 'placeholder', 'playsinline', 'poster', 'preload', 'pubdate', 'radiogroup', 'readonly', 'rel', 'required', 'rev', 'reversed', 'role', 'rows', 'rowspan', 'spellcheck', 'scope', 'selected', 'shape', 'size', 'sizes', 'span', 'srclang', 'start', 'src', 'srcset', 'step', 'style', 'summary', 'tabindex', 'title', 'translate', 'type', 'usemap', 'valign', 'value', 'width', 'xmlns', 'slot']);\n  var svg = freeze(['accent-height', 'accumulate', 'additive', 'alignment-baseline', 'ascent', 'attributename', 'attributetype', 'azimuth', 'basefrequency', 'baseline-shift', 'begin', 'bias', 'by', 'class', 'clip', 'clippathunits', 'clip-path', 'clip-rule', 'color', 'color-interpolation', 'color-interpolation-filters', 'color-profile', 'color-rendering', 'cx', 'cy', 'd', 'dx', 'dy', 'diffuseconstant', 'direction', 'display', 'divisor', 'dur', 'edgemode', 'elevation', 'end', 'fill', 'fill-opacity', 'fill-rule', 'filter', 'filterunits', 'flood-color', 'flood-opacity', 'font-family', 'font-size', 'font-size-adjust', 'font-stretch', 'font-style', 'font-variant', 'font-weight', 'fx', 'fy', 'g1', 'g2', 'glyph-name', 'glyphref', 'gradientunits', 'gradienttransform', 'height', 'href', 'id', 'image-rendering', 'in', 'in2', 'k', 'k1', 'k2', 'k3', 'k4', 'kerning', 'keypoints', 'keysplines', 'keytimes', 'lang', 'lengthadjust', 'letter-spacing', 'kernelmatrix', 'kernelunitlength', 'lighting-color', 'local', 'marker-end', 'marker-mid', 'marker-start', 'markerheight', 'markerunits', 'markerwidth', 'maskcontentunits', 'maskunits', 'max', 'mask', 'media', 'method', 'mode', 'min', 'name', 'numoctaves', 'offset', 'operator', 'opacity', 'order', 'orient', 'orientation', 'origin', 'overflow', 'paint-order', 'path', 'pathlength', 'patterncontentunits', 'patterntransform', 'patternunits', 'points', 'preservealpha', 'preserveaspectratio', 'primitiveunits', 'r', 'rx', 'ry', 'radius', 'refx', 'refy', 'repeatcount', 'repeatdur', 'restart', 'result', 'rotate', 'scale', 'seed', 'shape-rendering', 'specularconstant', 'specularexponent', 'spreadmethod', 'startoffset', 'stddeviation', 'stitchtiles', 'stop-color', 'stop-opacity', 'stroke-dasharray', 'stroke-dashoffset', 'stroke-linecap', 'stroke-linejoin', 'stroke-miterlimit', 'stroke-opacity', 'stroke', 'stroke-width', 'style', 'surfacescale', 'systemlanguage', 'tabindex', 'targetx', 'targety', 'transform', 'transform-origin', 'text-anchor', 'text-decoration', 'text-rendering', 'textlength', 'type', 'u1', 'u2', 'unicode', 'values', 'viewbox', 'visibility', 'version', 'vert-adv-y', 'vert-origin-x', 'vert-origin-y', 'width', 'word-spacing', 'wrap', 'writing-mode', 'xchannelselector', 'ychannelselector', 'x', 'x1', 'x2', 'xmlns', 'y', 'y1', 'y2', 'z', 'zoomandpan']);\n  var mathMl = freeze(['accent', 'accentunder', 'align', 'bevelled', 'close', 'columnsalign', 'columnlines', 'columnspan', 'denomalign', 'depth', 'dir', 'display', 'displaystyle', 'encoding', 'fence', 'frame', 'height', 'href', 'id', 'largeop', 'length', 'linethickness', 'lspace', 'lquote', 'mathbackground', 'mathcolor', 'mathsize', 'mathvariant', 'maxsize', 'minsize', 'movablelimits', 'notation', 'numalign', 'open', 'rowalign', 'rowlines', 'rowspacing', 'rowspan', 'rspace', 'rquote', 'scriptlevel', 'scriptminsize', 'scriptsizemultiplier', 'selection', 'separator', 'separators', 'stretchy', 'subscriptshift', 'supscriptshift', 'symmetric', 'voffset', 'width', 'xmlns']);\n  var xml = freeze(['xlink:href', 'xml:id', 'xlink:title', 'xml:space', 'xmlns:xlink']);\n\n  var MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\n\n  var ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\n  var TMPLIT_EXPR = seal(/\\${[\\w\\W]*}/gm);\n  var DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]/); // eslint-disable-line no-useless-escape\n\n  var ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\n\n  var IS_ALLOWED_URI = seal(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n  );\n  var IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\n  var ATTR_WHITESPACE = seal(/[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n  );\n  var DOCTYPE_NAME = seal(/^html$/i);\n\n  var getGlobal = function getGlobal() {\n    return typeof window === 'undefined' ? null : window;\n  };\n  /**\n   * Creates a no-op policy for internal use only.\n   * Don't export this function outside this module!\n   * @param {?TrustedTypePolicyFactory} trustedTypes The policy factory.\n   * @param {Document} document The document object (to determine policy name suffix)\n   * @return {?TrustedTypePolicy} The policy created (or null, if Trusted Types\n   * are not supported).\n   */\n\n\n  var _createTrustedTypesPolicy = function _createTrustedTypesPolicy(trustedTypes, document) {\n    if (_typeof(trustedTypes) !== 'object' || typeof trustedTypes.createPolicy !== 'function') {\n      return null;\n    } // Allow the callers to control the unique policy name\n    // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n    // Policy creation with duplicate names throws in Trusted Types.\n\n\n    var suffix = null;\n    var ATTR_NAME = 'data-tt-policy-suffix';\n\n    if (document.currentScript && document.currentScript.hasAttribute(ATTR_NAME)) {\n      suffix = document.currentScript.getAttribute(ATTR_NAME);\n    }\n\n    var policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n\n    try {\n      return trustedTypes.createPolicy(policyName, {\n        createHTML: function createHTML(html) {\n          return html;\n        },\n        createScriptURL: function createScriptURL(scriptUrl) {\n          return scriptUrl;\n        }\n      });\n    } catch (_) {\n      // Policy creation failed (most likely another DOMPurify script has\n      // already run). Skip creating the policy, as this will only cause errors\n      // if TT are enforced.\n      console.warn('TrustedTypes policy ' + policyName + ' could not be created.');\n      return null;\n    }\n  };\n\n  function createDOMPurify() {\n    var window = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : getGlobal();\n\n    var DOMPurify = function DOMPurify(root) {\n      return createDOMPurify(root);\n    };\n    /**\n     * Version label, exposed for easier checks\n     * if DOMPurify is up to date or not\n     */\n\n\n    DOMPurify.version = '2.4.7';\n    /**\n     * Array of elements that DOMPurify removed during sanitation.\n     * Empty if nothing was removed.\n     */\n\n    DOMPurify.removed = [];\n\n    if (!window || !window.document || window.document.nodeType !== 9) {\n      // Not running in a browser, provide a factory function\n      // so that you can pass your own Window\n      DOMPurify.isSupported = false;\n      return DOMPurify;\n    }\n\n    var originalDocument = window.document;\n    var document = window.document;\n    var DocumentFragment = window.DocumentFragment,\n        HTMLTemplateElement = window.HTMLTemplateElement,\n        Node = window.Node,\n        Element = window.Element,\n        NodeFilter = window.NodeFilter,\n        _window$NamedNodeMap = window.NamedNodeMap,\n        NamedNodeMap = _window$NamedNodeMap === void 0 ? window.NamedNodeMap || window.MozNamedAttrMap : _window$NamedNodeMap,\n        HTMLFormElement = window.HTMLFormElement,\n        DOMParser = window.DOMParser,\n        trustedTypes = window.trustedTypes;\n    var ElementPrototype = Element.prototype;\n    var cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n    var getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n    var getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n    var getParentNode = lookupGetter(ElementPrototype, 'parentNode'); // As per issue #47, the web-components registry is inherited by a\n    // new document created via createHTMLDocument. As per the spec\n    // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n    // a new empty registry is used when creating a template contents owner\n    // document, so we use that as our parent document to ensure nothing\n    // is inherited.\n\n    if (typeof HTMLTemplateElement === 'function') {\n      var template = document.createElement('template');\n\n      if (template.content && template.content.ownerDocument) {\n        document = template.content.ownerDocument;\n      }\n    }\n\n    var trustedTypesPolicy = _createTrustedTypesPolicy(trustedTypes, originalDocument);\n\n    var emptyHTML = trustedTypesPolicy ? trustedTypesPolicy.createHTML('') : '';\n    var _document = document,\n        implementation = _document.implementation,\n        createNodeIterator = _document.createNodeIterator,\n        createDocumentFragment = _document.createDocumentFragment,\n        getElementsByTagName = _document.getElementsByTagName;\n    var importNode = originalDocument.importNode;\n    var documentMode = {};\n\n    try {\n      documentMode = clone(document).documentMode ? document.documentMode : {};\n    } catch (_) {}\n\n    var hooks = {};\n    /**\n     * Expose whether this browser supports running the full DOMPurify.\n     */\n\n    DOMPurify.isSupported = typeof getParentNode === 'function' && implementation && implementation.createHTMLDocument !== undefined && documentMode !== 9;\n    var MUSTACHE_EXPR$1 = MUSTACHE_EXPR,\n        ERB_EXPR$1 = ERB_EXPR,\n        TMPLIT_EXPR$1 = TMPLIT_EXPR,\n        DATA_ATTR$1 = DATA_ATTR,\n        ARIA_ATTR$1 = ARIA_ATTR,\n        IS_SCRIPT_OR_DATA$1 = IS_SCRIPT_OR_DATA,\n        ATTR_WHITESPACE$1 = ATTR_WHITESPACE;\n    var IS_ALLOWED_URI$1 = IS_ALLOWED_URI;\n    /**\n     * We consider the elements and attributes below to be safe. Ideally\n     * don't add any new ones but feel free to remove unwanted ones.\n     */\n\n    /* allowed element names */\n\n    var ALLOWED_TAGS = null;\n    var DEFAULT_ALLOWED_TAGS = addToSet({}, [].concat(_toConsumableArray(html$1), _toConsumableArray(svg$1), _toConsumableArray(svgFilters), _toConsumableArray(mathMl$1), _toConsumableArray(text)));\n    /* Allowed attribute names */\n\n    var ALLOWED_ATTR = null;\n    var DEFAULT_ALLOWED_ATTR = addToSet({}, [].concat(_toConsumableArray(html), _toConsumableArray(svg), _toConsumableArray(mathMl), _toConsumableArray(xml)));\n    /*\n     * Configure how DOMPUrify should handle custom elements and their attributes as well as customized built-in elements.\n     * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n     * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n     * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n     */\n\n    var CUSTOM_ELEMENT_HANDLING = Object.seal(Object.create(null, {\n      tagNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null\n      },\n      attributeNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null\n      },\n      allowCustomizedBuiltInElements: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: false\n      }\n    }));\n    /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n\n    var FORBID_TAGS = null;\n    /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n\n    var FORBID_ATTR = null;\n    /* Decide if ARIA attributes are okay */\n\n    var ALLOW_ARIA_ATTR = true;\n    /* Decide if custom data attributes are okay */\n\n    var ALLOW_DATA_ATTR = true;\n    /* Decide if unknown protocols are okay */\n\n    var ALLOW_UNKNOWN_PROTOCOLS = false;\n    /* Decide if self-closing tags in attributes are allowed.\n     * Usually removed due to a mXSS issue in jQuery 3.0 */\n\n    var ALLOW_SELF_CLOSE_IN_ATTR = true;\n    /* Output should be safe for common template engines.\n     * This means, DOMPurify removes data attributes, mustaches and ERB\n     */\n\n    var SAFE_FOR_TEMPLATES = false;\n    /* Decide if document with <html>... should be returned */\n\n    var WHOLE_DOCUMENT = false;\n    /* Track whether config is already set on this instance of DOMPurify. */\n\n    var SET_CONFIG = false;\n    /* Decide if all elements (e.g. style, script) must be children of\n     * document.body. By default, browsers might move them to document.head */\n\n    var FORCE_BODY = false;\n    /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n     * string (or a TrustedHTML object if Trusted Types are supported).\n     * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n     */\n\n    var RETURN_DOM = false;\n    /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n     * string  (or a TrustedHTML object if Trusted Types are supported) */\n\n    var RETURN_DOM_FRAGMENT = false;\n    /* Try to return a Trusted Type object instead of a string, return a string in\n     * case Trusted Types are not supported  */\n\n    var RETURN_TRUSTED_TYPE = false;\n    /* Output should be free from DOM clobbering attacks?\n     * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n     */\n\n    var SANITIZE_DOM = true;\n    /* Achieve full DOM Clobbering protection by isolating the namespace of named\n     * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n     *\n     * HTML/DOM spec rules that enable DOM Clobbering:\n     *   - Named Access on Window (§7.3.3)\n     *   - DOM Tree Accessors (§3.1.5)\n     *   - Form Element Parent-Child Relations (§4.10.3)\n     *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n     *   - HTMLCollection (§4.2.10.2)\n     *\n     * Namespace isolation is implemented by prefixing `id` and `name` attributes\n     * with a constant string, i.e., `user-content-`\n     */\n\n    var SANITIZE_NAMED_PROPS = false;\n    var SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n    /* Keep element content when removing element? */\n\n    var KEEP_CONTENT = true;\n    /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n     * of importing it into a new Document and returning a sanitized copy */\n\n    var IN_PLACE = false;\n    /* Allow usage of profiles like html, svg and mathMl */\n\n    var USE_PROFILES = {};\n    /* Tags to ignore content of when KEEP_CONTENT is true */\n\n    var FORBID_CONTENTS = null;\n    var DEFAULT_FORBID_CONTENTS = addToSet({}, ['annotation-xml', 'audio', 'colgroup', 'desc', 'foreignobject', 'head', 'iframe', 'math', 'mi', 'mn', 'mo', 'ms', 'mtext', 'noembed', 'noframes', 'noscript', 'plaintext', 'script', 'style', 'svg', 'template', 'thead', 'title', 'video', 'xmp']);\n    /* Tags that are safe for data: URIs */\n\n    var DATA_URI_TAGS = null;\n    var DEFAULT_DATA_URI_TAGS = addToSet({}, ['audio', 'video', 'img', 'source', 'image', 'track']);\n    /* Attributes safe for values like \"javascript:\" */\n\n    var URI_SAFE_ATTRIBUTES = null;\n    var DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, ['alt', 'class', 'for', 'id', 'label', 'name', 'pattern', 'placeholder', 'role', 'summary', 'title', 'value', 'style', 'xmlns']);\n    var MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n    var SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n    var HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n    /* Document namespace */\n\n    var NAMESPACE = HTML_NAMESPACE;\n    var IS_EMPTY_INPUT = false;\n    /* Allowed XHTML+XML namespaces */\n\n    var ALLOWED_NAMESPACES = null;\n    var DEFAULT_ALLOWED_NAMESPACES = addToSet({}, [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE], stringToString);\n    /* Parsing of strict XHTML documents */\n\n    var PARSER_MEDIA_TYPE;\n    var SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n    var DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n    var transformCaseFunc;\n    /* Keep a reference to config to pass to hooks */\n\n    var CONFIG = null;\n    /* Ideally, do not touch anything below this line */\n\n    /* ______________________________________________ */\n\n    var formElement = document.createElement('form');\n\n    var isRegexOrFunction = function isRegexOrFunction(testValue) {\n      return testValue instanceof RegExp || testValue instanceof Function;\n    };\n    /**\n     * _parseConfig\n     *\n     * @param  {Object} cfg optional config literal\n     */\n    // eslint-disable-next-line complexity\n\n\n    var _parseConfig = function _parseConfig(cfg) {\n      if (CONFIG && CONFIG === cfg) {\n        return;\n      }\n      /* Shield configuration object from tampering */\n\n\n      if (!cfg || _typeof(cfg) !== 'object') {\n        cfg = {};\n      }\n      /* Shield configuration object from prototype pollution */\n\n\n      cfg = clone(cfg);\n      PARSER_MEDIA_TYPE = // eslint-disable-next-line unicorn/prefer-includes\n      SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1 ? PARSER_MEDIA_TYPE = DEFAULT_PARSER_MEDIA_TYPE : PARSER_MEDIA_TYPE = cfg.PARSER_MEDIA_TYPE; // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n\n      transformCaseFunc = PARSER_MEDIA_TYPE === 'application/xhtml+xml' ? stringToString : stringToLowerCase;\n      /* Set configuration parameters */\n\n      ALLOWED_TAGS = 'ALLOWED_TAGS' in cfg ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc) : DEFAULT_ALLOWED_TAGS;\n      ALLOWED_ATTR = 'ALLOWED_ATTR' in cfg ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc) : DEFAULT_ALLOWED_ATTR;\n      ALLOWED_NAMESPACES = 'ALLOWED_NAMESPACES' in cfg ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString) : DEFAULT_ALLOWED_NAMESPACES;\n      URI_SAFE_ATTRIBUTES = 'ADD_URI_SAFE_ATTR' in cfg ? addToSet(clone(DEFAULT_URI_SAFE_ATTRIBUTES), // eslint-disable-line indent\n      cfg.ADD_URI_SAFE_ATTR, // eslint-disable-line indent\n      transformCaseFunc // eslint-disable-line indent\n      ) // eslint-disable-line indent\n      : DEFAULT_URI_SAFE_ATTRIBUTES;\n      DATA_URI_TAGS = 'ADD_DATA_URI_TAGS' in cfg ? addToSet(clone(DEFAULT_DATA_URI_TAGS), // eslint-disable-line indent\n      cfg.ADD_DATA_URI_TAGS, // eslint-disable-line indent\n      transformCaseFunc // eslint-disable-line indent\n      ) // eslint-disable-line indent\n      : DEFAULT_DATA_URI_TAGS;\n      FORBID_CONTENTS = 'FORBID_CONTENTS' in cfg ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc) : DEFAULT_FORBID_CONTENTS;\n      FORBID_TAGS = 'FORBID_TAGS' in cfg ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc) : {};\n      FORBID_ATTR = 'FORBID_ATTR' in cfg ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc) : {};\n      USE_PROFILES = 'USE_PROFILES' in cfg ? cfg.USE_PROFILES : false;\n      ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n\n      ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n\n      ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n\n      ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n\n      SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n\n      WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n\n      RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n\n      RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n\n      RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n\n      FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n\n      SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n\n      SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n\n      KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n\n      IN_PLACE = cfg.IN_PLACE || false; // Default false\n\n      IS_ALLOWED_URI$1 = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI$1;\n      NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n      CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n\n      if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)) {\n        CUSTOM_ELEMENT_HANDLING.tagNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n      }\n\n      if (cfg.CUSTOM_ELEMENT_HANDLING && isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)) {\n        CUSTOM_ELEMENT_HANDLING.attributeNameCheck = cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n      }\n\n      if (cfg.CUSTOM_ELEMENT_HANDLING && typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements === 'boolean') {\n        CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements = cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n      }\n\n      if (SAFE_FOR_TEMPLATES) {\n        ALLOW_DATA_ATTR = false;\n      }\n\n      if (RETURN_DOM_FRAGMENT) {\n        RETURN_DOM = true;\n      }\n      /* Parse profile info */\n\n\n      if (USE_PROFILES) {\n        ALLOWED_TAGS = addToSet({}, _toConsumableArray(text));\n        ALLOWED_ATTR = [];\n\n        if (USE_PROFILES.html === true) {\n          addToSet(ALLOWED_TAGS, html$1);\n          addToSet(ALLOWED_ATTR, html);\n        }\n\n        if (USE_PROFILES.svg === true) {\n          addToSet(ALLOWED_TAGS, svg$1);\n          addToSet(ALLOWED_ATTR, svg);\n          addToSet(ALLOWED_ATTR, xml);\n        }\n\n        if (USE_PROFILES.svgFilters === true) {\n          addToSet(ALLOWED_TAGS, svgFilters);\n          addToSet(ALLOWED_ATTR, svg);\n          addToSet(ALLOWED_ATTR, xml);\n        }\n\n        if (USE_PROFILES.mathMl === true) {\n          addToSet(ALLOWED_TAGS, mathMl$1);\n          addToSet(ALLOWED_ATTR, mathMl);\n          addToSet(ALLOWED_ATTR, xml);\n        }\n      }\n      /* Merge configuration parameters */\n\n\n      if (cfg.ADD_TAGS) {\n        if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n          ALLOWED_TAGS = clone(ALLOWED_TAGS);\n        }\n\n        addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n      }\n\n      if (cfg.ADD_ATTR) {\n        if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n          ALLOWED_ATTR = clone(ALLOWED_ATTR);\n        }\n\n        addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n      }\n\n      if (cfg.ADD_URI_SAFE_ATTR) {\n        addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n      }\n\n      if (cfg.FORBID_CONTENTS) {\n        if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n          FORBID_CONTENTS = clone(FORBID_CONTENTS);\n        }\n\n        addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n      }\n      /* Add #text in case KEEP_CONTENT is set to true */\n\n\n      if (KEEP_CONTENT) {\n        ALLOWED_TAGS['#text'] = true;\n      }\n      /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n\n\n      if (WHOLE_DOCUMENT) {\n        addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n      }\n      /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n\n\n      if (ALLOWED_TAGS.table) {\n        addToSet(ALLOWED_TAGS, ['tbody']);\n        delete FORBID_TAGS.tbody;\n      } // Prevent further manipulation of configuration.\n      // Not available in IE8, Safari 5, etc.\n\n\n      if (freeze) {\n        freeze(cfg);\n      }\n\n      CONFIG = cfg;\n    };\n\n    var MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, ['mi', 'mo', 'mn', 'ms', 'mtext']);\n    var HTML_INTEGRATION_POINTS = addToSet({}, ['foreignobject', 'desc', 'title', 'annotation-xml']); // Certain elements are allowed in both SVG and HTML\n    // namespace. We need to specify them explicitly\n    // so that they don't get erroneously deleted from\n    // HTML namespace.\n\n    var COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, ['title', 'style', 'font', 'a', 'script']);\n    /* Keep track of all possible SVG and MathML tags\n     * so that we can perform the namespace checks\n     * correctly. */\n\n    var ALL_SVG_TAGS = addToSet({}, svg$1);\n    addToSet(ALL_SVG_TAGS, svgFilters);\n    addToSet(ALL_SVG_TAGS, svgDisallowed);\n    var ALL_MATHML_TAGS = addToSet({}, mathMl$1);\n    addToSet(ALL_MATHML_TAGS, mathMlDisallowed);\n    /**\n     *\n     *\n     * @param  {Element} element a DOM element whose namespace is being checked\n     * @returns {boolean} Return false if the element has a\n     *  namespace that a spec-compliant parser would never\n     *  return. Return true otherwise.\n     */\n\n    var _checkValidNamespace = function _checkValidNamespace(element) {\n      var parent = getParentNode(element); // In JSDOM, if we're inside shadow DOM, then parentNode\n      // can be null. We just simulate parent in this case.\n\n      if (!parent || !parent.tagName) {\n        parent = {\n          namespaceURI: NAMESPACE,\n          tagName: 'template'\n        };\n      }\n\n      var tagName = stringToLowerCase(element.tagName);\n      var parentTagName = stringToLowerCase(parent.tagName);\n\n      if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n        return false;\n      }\n\n      if (element.namespaceURI === SVG_NAMESPACE) {\n        // The only way to switch from HTML namespace to SVG\n        // is via <svg>. If it happens via any other tag, then\n        // it should be killed.\n        if (parent.namespaceURI === HTML_NAMESPACE) {\n          return tagName === 'svg';\n        } // The only way to switch from MathML to SVG is via`\n        // svg if parent is either <annotation-xml> or MathML\n        // text integration points.\n\n\n        if (parent.namespaceURI === MATHML_NAMESPACE) {\n          return tagName === 'svg' && (parentTagName === 'annotation-xml' || MATHML_TEXT_INTEGRATION_POINTS[parentTagName]);\n        } // We only allow elements that are defined in SVG\n        // spec. All others are disallowed in SVG namespace.\n\n\n        return Boolean(ALL_SVG_TAGS[tagName]);\n      }\n\n      if (element.namespaceURI === MATHML_NAMESPACE) {\n        // The only way to switch from HTML namespace to MathML\n        // is via <math>. If it happens via any other tag, then\n        // it should be killed.\n        if (parent.namespaceURI === HTML_NAMESPACE) {\n          return tagName === 'math';\n        } // The only way to switch from SVG to MathML is via\n        // <math> and HTML integration points\n\n\n        if (parent.namespaceURI === SVG_NAMESPACE) {\n          return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n        } // We only allow elements that are defined in MathML\n        // spec. All others are disallowed in MathML namespace.\n\n\n        return Boolean(ALL_MATHML_TAGS[tagName]);\n      }\n\n      if (element.namespaceURI === HTML_NAMESPACE) {\n        // The only way to switch from SVG to HTML is via\n        // HTML integration points, and from MathML to HTML\n        // is via MathML text integration points\n        if (parent.namespaceURI === SVG_NAMESPACE && !HTML_INTEGRATION_POINTS[parentTagName]) {\n          return false;\n        }\n\n        if (parent.namespaceURI === MATHML_NAMESPACE && !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]) {\n          return false;\n        } // We disallow tags that are specific for MathML\n        // or SVG and should never appear in HTML namespace\n\n\n        return !ALL_MATHML_TAGS[tagName] && (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName]);\n      } // For XHTML and XML documents that support custom namespaces\n\n\n      if (PARSER_MEDIA_TYPE === 'application/xhtml+xml' && ALLOWED_NAMESPACES[element.namespaceURI]) {\n        return true;\n      } // The code should never reach this place (this means\n      // that the element somehow got namespace that is not\n      // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n      // Return false just in case.\n\n\n      return false;\n    };\n    /**\n     * _forceRemove\n     *\n     * @param  {Node} node a DOM node\n     */\n\n\n    var _forceRemove = function _forceRemove(node) {\n      arrayPush(DOMPurify.removed, {\n        element: node\n      });\n\n      try {\n        // eslint-disable-next-line unicorn/prefer-dom-node-remove\n        node.parentNode.removeChild(node);\n      } catch (_) {\n        try {\n          node.outerHTML = emptyHTML;\n        } catch (_) {\n          node.remove();\n        }\n      }\n    };\n    /**\n     * _removeAttribute\n     *\n     * @param  {String} name an Attribute name\n     * @param  {Node} node a DOM node\n     */\n\n\n    var _removeAttribute = function _removeAttribute(name, node) {\n      try {\n        arrayPush(DOMPurify.removed, {\n          attribute: node.getAttributeNode(name),\n          from: node\n        });\n      } catch (_) {\n        arrayPush(DOMPurify.removed, {\n          attribute: null,\n          from: node\n        });\n      }\n\n      node.removeAttribute(name); // We void attribute values for unremovable \"is\"\" attributes\n\n      if (name === 'is' && !ALLOWED_ATTR[name]) {\n        if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n          try {\n            _forceRemove(node);\n          } catch (_) {}\n        } else {\n          try {\n            node.setAttribute(name, '');\n          } catch (_) {}\n        }\n      }\n    };\n    /**\n     * _initDocument\n     *\n     * @param  {String} dirty a string of dirty markup\n     * @return {Document} a DOM, filled with the dirty markup\n     */\n\n\n    var _initDocument = function _initDocument(dirty) {\n      /* Create a HTML document */\n      var doc;\n      var leadingWhitespace;\n\n      if (FORCE_BODY) {\n        dirty = '<remove></remove>' + dirty;\n      } else {\n        /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n        var matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n        leadingWhitespace = matches && matches[0];\n      }\n\n      if (PARSER_MEDIA_TYPE === 'application/xhtml+xml' && NAMESPACE === HTML_NAMESPACE) {\n        // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n        dirty = '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' + dirty + '</body></html>';\n      }\n\n      var dirtyPayload = trustedTypesPolicy ? trustedTypesPolicy.createHTML(dirty) : dirty;\n      /*\n       * Use the DOMParser API by default, fallback later if needs be\n       * DOMParser not work for svg when has multiple root element.\n       */\n\n      if (NAMESPACE === HTML_NAMESPACE) {\n        try {\n          doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n        } catch (_) {}\n      }\n      /* Use createHTMLDocument in case DOMParser is not available */\n\n\n      if (!doc || !doc.documentElement) {\n        doc = implementation.createDocument(NAMESPACE, 'template', null);\n\n        try {\n          doc.documentElement.innerHTML = IS_EMPTY_INPUT ? emptyHTML : dirtyPayload;\n        } catch (_) {// Syntax error if dirtyPayload is invalid xml\n        }\n      }\n\n      var body = doc.body || doc.documentElement;\n\n      if (dirty && leadingWhitespace) {\n        body.insertBefore(document.createTextNode(leadingWhitespace), body.childNodes[0] || null);\n      }\n      /* Work on whole document or just its body */\n\n\n      if (NAMESPACE === HTML_NAMESPACE) {\n        return getElementsByTagName.call(doc, WHOLE_DOCUMENT ? 'html' : 'body')[0];\n      }\n\n      return WHOLE_DOCUMENT ? doc.documentElement : body;\n    };\n    /**\n     * _createIterator\n     *\n     * @param  {Document} root document/fragment to create iterator for\n     * @return {Iterator} iterator instance\n     */\n\n\n    var _createIterator = function _createIterator(root) {\n      return createNodeIterator.call(root.ownerDocument || root, root, // eslint-disable-next-line no-bitwise\n      NodeFilter.SHOW_ELEMENT | NodeFilter.SHOW_COMMENT | NodeFilter.SHOW_TEXT, null, false);\n    };\n    /**\n     * _isClobbered\n     *\n     * @param  {Node} elm element to check for clobbering attacks\n     * @return {Boolean} true if clobbered, false if safe\n     */\n\n\n    var _isClobbered = function _isClobbered(elm) {\n      return elm instanceof HTMLFormElement && (typeof elm.nodeName !== 'string' || typeof elm.textContent !== 'string' || typeof elm.removeChild !== 'function' || !(elm.attributes instanceof NamedNodeMap) || typeof elm.removeAttribute !== 'function' || typeof elm.setAttribute !== 'function' || typeof elm.namespaceURI !== 'string' || typeof elm.insertBefore !== 'function' || typeof elm.hasChildNodes !== 'function');\n    };\n    /**\n     * _isNode\n     *\n     * @param  {Node} obj object to check whether it's a DOM node\n     * @return {Boolean} true is object is a DOM node\n     */\n\n\n    var _isNode = function _isNode(object) {\n      return _typeof(Node) === 'object' ? object instanceof Node : object && _typeof(object) === 'object' && typeof object.nodeType === 'number' && typeof object.nodeName === 'string';\n    };\n    /**\n     * _executeHook\n     * Execute user configurable hooks\n     *\n     * @param  {String} entryPoint  Name of the hook's entry point\n     * @param  {Node} currentNode node to work on with the hook\n     * @param  {Object} data additional hook parameters\n     */\n\n\n    var _executeHook = function _executeHook(entryPoint, currentNode, data) {\n      if (!hooks[entryPoint]) {\n        return;\n      }\n\n      arrayForEach(hooks[entryPoint], function (hook) {\n        hook.call(DOMPurify, currentNode, data, CONFIG);\n      });\n    };\n    /**\n     * _sanitizeElements\n     *\n     * @protect nodeName\n     * @protect textContent\n     * @protect removeChild\n     *\n     * @param   {Node} currentNode to check for permission to exist\n     * @return  {Boolean} true if node was killed, false if left alive\n     */\n\n\n    var _sanitizeElements = function _sanitizeElements(currentNode) {\n      var content;\n      /* Execute a hook if present */\n\n      _executeHook('beforeSanitizeElements', currentNode, null);\n      /* Check if element is clobbered or can clobber */\n\n\n      if (_isClobbered(currentNode)) {\n        _forceRemove(currentNode);\n\n        return true;\n      }\n      /* Check if tagname contains Unicode */\n\n\n      if (regExpTest(/[\\u0080-\\uFFFF]/, currentNode.nodeName)) {\n        _forceRemove(currentNode);\n\n        return true;\n      }\n      /* Now let's check the element's type and name */\n\n\n      var tagName = transformCaseFunc(currentNode.nodeName);\n      /* Execute a hook if present */\n\n      _executeHook('uponSanitizeElement', currentNode, {\n        tagName: tagName,\n        allowedTags: ALLOWED_TAGS\n      });\n      /* Detect mXSS attempts abusing namespace confusion */\n\n\n      if (currentNode.hasChildNodes() && !_isNode(currentNode.firstElementChild) && (!_isNode(currentNode.content) || !_isNode(currentNode.content.firstElementChild)) && regExpTest(/<[/\\w]/g, currentNode.innerHTML) && regExpTest(/<[/\\w]/g, currentNode.textContent)) {\n        _forceRemove(currentNode);\n\n        return true;\n      }\n      /* Mitigate a problem with templates inside select */\n\n\n      if (tagName === 'select' && regExpTest(/<template/i, currentNode.innerHTML)) {\n        _forceRemove(currentNode);\n\n        return true;\n      }\n      /* Remove element if anything forbids its presence */\n\n\n      if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n        /* Check if we have a custom element to handle */\n        if (!FORBID_TAGS[tagName] && _basicCustomElementTest(tagName)) {\n          if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)) return false;\n          if (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)) return false;\n        }\n        /* Keep content except for bad-listed elements */\n\n\n        if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n          var parentNode = getParentNode(currentNode) || currentNode.parentNode;\n          var childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n\n          if (childNodes && parentNode) {\n            var childCount = childNodes.length;\n\n            for (var i = childCount - 1; i >= 0; --i) {\n              parentNode.insertBefore(cloneNode(childNodes[i], true), getNextSibling(currentNode));\n            }\n          }\n        }\n\n        _forceRemove(currentNode);\n\n        return true;\n      }\n      /* Check whether element has a valid namespace */\n\n\n      if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n        _forceRemove(currentNode);\n\n        return true;\n      }\n      /* Make sure that older browsers don't get fallback-tag mXSS */\n\n\n      if ((tagName === 'noscript' || tagName === 'noembed' || tagName === 'noframes') && regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)) {\n        _forceRemove(currentNode);\n\n        return true;\n      }\n      /* Sanitize element content to be template-safe */\n\n\n      if (SAFE_FOR_TEMPLATES && currentNode.nodeType === 3) {\n        /* Get the element's text content */\n        content = currentNode.textContent;\n        content = stringReplace(content, MUSTACHE_EXPR$1, ' ');\n        content = stringReplace(content, ERB_EXPR$1, ' ');\n        content = stringReplace(content, TMPLIT_EXPR$1, ' ');\n\n        if (currentNode.textContent !== content) {\n          arrayPush(DOMPurify.removed, {\n            element: currentNode.cloneNode()\n          });\n          currentNode.textContent = content;\n        }\n      }\n      /* Execute a hook if present */\n\n\n      _executeHook('afterSanitizeElements', currentNode, null);\n\n      return false;\n    };\n    /**\n     * _isValidAttribute\n     *\n     * @param  {string} lcTag Lowercase tag name of containing element.\n     * @param  {string} lcName Lowercase attribute name.\n     * @param  {string} value Attribute value.\n     * @return {Boolean} Returns true if `value` is valid, otherwise false.\n     */\n    // eslint-disable-next-line complexity\n\n\n    var _isValidAttribute = function _isValidAttribute(lcTag, lcName, value) {\n      /* Make sure attribute cannot clobber */\n      if (SANITIZE_DOM && (lcName === 'id' || lcName === 'name') && (value in document || value in formElement)) {\n        return false;\n      }\n      /* Allow valid data-* attributes: At least one character after \"-\"\n          (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n          XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n          We don't need to check the value; it's always URI safe. */\n\n\n      if (ALLOW_DATA_ATTR && !FORBID_ATTR[lcName] && regExpTest(DATA_ATTR$1, lcName)) ; else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR$1, lcName)) ; else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n        if ( // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n        // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n        _basicCustomElementTest(lcTag) && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag)) && (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName) || CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)) || // Alternative, second condition checks if it's an `is`-attribute, AND\n        // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        lcName === 'is' && CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements && (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp && regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value) || CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function && CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))) ; else {\n          return false;\n        }\n        /* Check value is safe. First, is attr inert? If so, is safe */\n\n      } else if (URI_SAFE_ATTRIBUTES[lcName]) ; else if (regExpTest(IS_ALLOWED_URI$1, stringReplace(value, ATTR_WHITESPACE$1, ''))) ; else if ((lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') && lcTag !== 'script' && stringIndexOf(value, 'data:') === 0 && DATA_URI_TAGS[lcTag]) ; else if (ALLOW_UNKNOWN_PROTOCOLS && !regExpTest(IS_SCRIPT_OR_DATA$1, stringReplace(value, ATTR_WHITESPACE$1, ''))) ; else if (value) {\n        return false;\n      } else ;\n\n      return true;\n    };\n    /**\n     * _basicCustomElementCheck\n     * checks if at least one dash is included in tagName, and it's not the first char\n     * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n     * @param {string} tagName name of the tag of the node to sanitize\n     */\n\n\n    var _basicCustomElementTest = function _basicCustomElementTest(tagName) {\n      return tagName.indexOf('-') > 0;\n    };\n    /**\n     * _sanitizeAttributes\n     *\n     * @protect attributes\n     * @protect nodeName\n     * @protect removeAttribute\n     * @protect setAttribute\n     *\n     * @param  {Node} currentNode to sanitize\n     */\n\n\n    var _sanitizeAttributes = function _sanitizeAttributes(currentNode) {\n      var attr;\n      var value;\n      var lcName;\n      var l;\n      /* Execute a hook if present */\n\n      _executeHook('beforeSanitizeAttributes', currentNode, null);\n\n      var attributes = currentNode.attributes;\n      /* Check if we have attributes; if not we might have a text node */\n\n      if (!attributes) {\n        return;\n      }\n\n      var hookEvent = {\n        attrName: '',\n        attrValue: '',\n        keepAttr: true,\n        allowedAttributes: ALLOWED_ATTR\n      };\n      l = attributes.length;\n      /* Go backwards over all attributes; safely remove bad ones */\n\n      while (l--) {\n        attr = attributes[l];\n        var _attr = attr,\n            name = _attr.name,\n            namespaceURI = _attr.namespaceURI;\n        value = name === 'value' ? attr.value : stringTrim(attr.value);\n        lcName = transformCaseFunc(name);\n        /* Execute a hook if present */\n\n        hookEvent.attrName = lcName;\n        hookEvent.attrValue = value;\n        hookEvent.keepAttr = true;\n        hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n\n        _executeHook('uponSanitizeAttribute', currentNode, hookEvent);\n\n        value = hookEvent.attrValue;\n        /* Did the hooks approve of the attribute? */\n\n        if (hookEvent.forceKeepAttr) {\n          continue;\n        }\n        /* Remove attribute */\n\n\n        _removeAttribute(name, currentNode);\n        /* Did the hooks approve of the attribute? */\n\n\n        if (!hookEvent.keepAttr) {\n          continue;\n        }\n        /* Work around a security issue in jQuery 3.0 */\n\n\n        if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n          _removeAttribute(name, currentNode);\n\n          continue;\n        }\n        /* Sanitize attribute content to be template-safe */\n\n\n        if (SAFE_FOR_TEMPLATES) {\n          value = stringReplace(value, MUSTACHE_EXPR$1, ' ');\n          value = stringReplace(value, ERB_EXPR$1, ' ');\n          value = stringReplace(value, TMPLIT_EXPR$1, ' ');\n        }\n        /* Is `value` valid for this attribute? */\n\n\n        var lcTag = transformCaseFunc(currentNode.nodeName);\n\n        if (!_isValidAttribute(lcTag, lcName, value)) {\n          continue;\n        }\n        /* Full DOM Clobbering protection via namespace isolation,\n         * Prefix id and name attributes with `user-content-`\n         */\n\n\n        if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n          // Remove the attribute with this value\n          _removeAttribute(name, currentNode); // Prefix the value and later re-create the attribute with the sanitized value\n\n\n          value = SANITIZE_NAMED_PROPS_PREFIX + value;\n        }\n        /* Handle attributes that require Trusted Types */\n\n\n        if (trustedTypesPolicy && _typeof(trustedTypes) === 'object' && typeof trustedTypes.getAttributeType === 'function') {\n          if (namespaceURI) ; else {\n            switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n              case 'TrustedHTML':\n                {\n                  value = trustedTypesPolicy.createHTML(value);\n                  break;\n                }\n\n              case 'TrustedScriptURL':\n                {\n                  value = trustedTypesPolicy.createScriptURL(value);\n                  break;\n                }\n            }\n          }\n        }\n        /* Handle invalid data-* attribute set by try-catching it */\n\n\n        try {\n          if (namespaceURI) {\n            currentNode.setAttributeNS(namespaceURI, name, value);\n          } else {\n            /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n            currentNode.setAttribute(name, value);\n          }\n\n          arrayPop(DOMPurify.removed);\n        } catch (_) {}\n      }\n      /* Execute a hook if present */\n\n\n      _executeHook('afterSanitizeAttributes', currentNode, null);\n    };\n    /**\n     * _sanitizeShadowDOM\n     *\n     * @param  {DocumentFragment} fragment to iterate over recursively\n     */\n\n\n    var _sanitizeShadowDOM = function _sanitizeShadowDOM(fragment) {\n      var shadowNode;\n\n      var shadowIterator = _createIterator(fragment);\n      /* Execute a hook if present */\n\n\n      _executeHook('beforeSanitizeShadowDOM', fragment, null);\n\n      while (shadowNode = shadowIterator.nextNode()) {\n        /* Execute a hook if present */\n        _executeHook('uponSanitizeShadowNode', shadowNode, null);\n        /* Sanitize tags and elements */\n\n\n        if (_sanitizeElements(shadowNode)) {\n          continue;\n        }\n        /* Deep shadow DOM detected */\n\n\n        if (shadowNode.content instanceof DocumentFragment) {\n          _sanitizeShadowDOM(shadowNode.content);\n        }\n        /* Check attributes, sanitize if necessary */\n\n\n        _sanitizeAttributes(shadowNode);\n      }\n      /* Execute a hook if present */\n\n\n      _executeHook('afterSanitizeShadowDOM', fragment, null);\n    };\n    /**\n     * Sanitize\n     * Public method providing core sanitation functionality\n     *\n     * @param {String|Node} dirty string or DOM node\n     * @param {Object} configuration object\n     */\n    // eslint-disable-next-line complexity\n\n\n    DOMPurify.sanitize = function (dirty) {\n      var cfg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var body;\n      var importedNode;\n      var currentNode;\n      var oldNode;\n      var returnNode;\n      /* Make sure we have a string to sanitize.\n        DO NOT return early, as this will return the wrong type if\n        the user has requested a DOM object rather than a string */\n\n      IS_EMPTY_INPUT = !dirty;\n\n      if (IS_EMPTY_INPUT) {\n        dirty = '<!-->';\n      }\n      /* Stringify, in case dirty is an object */\n\n\n      if (typeof dirty !== 'string' && !_isNode(dirty)) {\n        if (typeof dirty.toString === 'function') {\n          dirty = dirty.toString();\n\n          if (typeof dirty !== 'string') {\n            throw typeErrorCreate('dirty is not a string, aborting');\n          }\n        } else {\n          throw typeErrorCreate('toString is not a function');\n        }\n      }\n      /* Check we can run. Otherwise fall back or ignore */\n\n\n      if (!DOMPurify.isSupported) {\n        if (_typeof(window.toStaticHTML) === 'object' || typeof window.toStaticHTML === 'function') {\n          if (typeof dirty === 'string') {\n            return window.toStaticHTML(dirty);\n          }\n\n          if (_isNode(dirty)) {\n            return window.toStaticHTML(dirty.outerHTML);\n          }\n        }\n\n        return dirty;\n      }\n      /* Assign config vars */\n\n\n      if (!SET_CONFIG) {\n        _parseConfig(cfg);\n      }\n      /* Clean up removed elements */\n\n\n      DOMPurify.removed = [];\n      /* Check if dirty is correctly typed for IN_PLACE */\n\n      if (typeof dirty === 'string') {\n        IN_PLACE = false;\n      }\n\n      if (IN_PLACE) {\n        /* Do some early pre-sanitization to avoid unsafe root nodes */\n        if (dirty.nodeName) {\n          var tagName = transformCaseFunc(dirty.nodeName);\n\n          if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n            throw typeErrorCreate('root node is forbidden and cannot be sanitized in-place');\n          }\n        }\n      } else if (dirty instanceof Node) {\n        /* If dirty is a DOM element, append to an empty document to avoid\n           elements being stripped by the parser */\n        body = _initDocument('<!---->');\n        importedNode = body.ownerDocument.importNode(dirty, true);\n\n        if (importedNode.nodeType === 1 && importedNode.nodeName === 'BODY') {\n          /* Node is already a body, use as is */\n          body = importedNode;\n        } else if (importedNode.nodeName === 'HTML') {\n          body = importedNode;\n        } else {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          body.appendChild(importedNode);\n        }\n      } else {\n        /* Exit directly if we have nothing to do */\n        if (!RETURN_DOM && !SAFE_FOR_TEMPLATES && !WHOLE_DOCUMENT && // eslint-disable-next-line unicorn/prefer-includes\n        dirty.indexOf('<') === -1) {\n          return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(dirty) : dirty;\n        }\n        /* Initialize the document to work on */\n\n\n        body = _initDocument(dirty);\n        /* Check we have a DOM node from the data */\n\n        if (!body) {\n          return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n        }\n      }\n      /* Remove first element node (ours) if FORCE_BODY is set */\n\n\n      if (body && FORCE_BODY) {\n        _forceRemove(body.firstChild);\n      }\n      /* Get node iterator */\n\n\n      var nodeIterator = _createIterator(IN_PLACE ? dirty : body);\n      /* Now start iterating over the created document */\n\n\n      while (currentNode = nodeIterator.nextNode()) {\n        /* Fix IE's strange behavior with manipulated textNodes #89 */\n        if (currentNode.nodeType === 3 && currentNode === oldNode) {\n          continue;\n        }\n        /* Sanitize tags and elements */\n\n\n        if (_sanitizeElements(currentNode)) {\n          continue;\n        }\n        /* Shadow DOM detected, sanitize it */\n\n\n        if (currentNode.content instanceof DocumentFragment) {\n          _sanitizeShadowDOM(currentNode.content);\n        }\n        /* Check attributes, sanitize if necessary */\n\n\n        _sanitizeAttributes(currentNode);\n\n        oldNode = currentNode;\n      }\n\n      oldNode = null;\n      /* If we sanitized `dirty` in-place, return it. */\n\n      if (IN_PLACE) {\n        return dirty;\n      }\n      /* Return sanitized string or DOM */\n\n\n      if (RETURN_DOM) {\n        if (RETURN_DOM_FRAGMENT) {\n          returnNode = createDocumentFragment.call(body.ownerDocument);\n\n          while (body.firstChild) {\n            // eslint-disable-next-line unicorn/prefer-dom-node-append\n            returnNode.appendChild(body.firstChild);\n          }\n        } else {\n          returnNode = body;\n        }\n\n        if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmod) {\n          /*\n            AdoptNode() is not used because internal state is not reset\n            (e.g. the past names map of a HTMLFormElement), this is safe\n            in theory but we would rather not risk another attack vector.\n            The state that is cloned by importNode() is explicitly defined\n            by the specs.\n          */\n          returnNode = importNode.call(originalDocument, returnNode, true);\n        }\n\n        return returnNode;\n      }\n\n      var serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n      /* Serialize doctype if allowed */\n\n      if (WHOLE_DOCUMENT && ALLOWED_TAGS['!doctype'] && body.ownerDocument && body.ownerDocument.doctype && body.ownerDocument.doctype.name && regExpTest(DOCTYPE_NAME, body.ownerDocument.doctype.name)) {\n        serializedHTML = '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n      }\n      /* Sanitize final string template-safe */\n\n\n      if (SAFE_FOR_TEMPLATES) {\n        serializedHTML = stringReplace(serializedHTML, MUSTACHE_EXPR$1, ' ');\n        serializedHTML = stringReplace(serializedHTML, ERB_EXPR$1, ' ');\n        serializedHTML = stringReplace(serializedHTML, TMPLIT_EXPR$1, ' ');\n      }\n\n      return trustedTypesPolicy && RETURN_TRUSTED_TYPE ? trustedTypesPolicy.createHTML(serializedHTML) : serializedHTML;\n    };\n    /**\n     * Public method to set the configuration once\n     * setConfig\n     *\n     * @param {Object} cfg configuration object\n     */\n\n\n    DOMPurify.setConfig = function (cfg) {\n      _parseConfig(cfg);\n\n      SET_CONFIG = true;\n    };\n    /**\n     * Public method to remove the configuration\n     * clearConfig\n     *\n     */\n\n\n    DOMPurify.clearConfig = function () {\n      CONFIG = null;\n      SET_CONFIG = false;\n    };\n    /**\n     * Public method to check if an attribute value is valid.\n     * Uses last set config, if any. Otherwise, uses config defaults.\n     * isValidAttribute\n     *\n     * @param  {string} tag Tag name of containing element.\n     * @param  {string} attr Attribute name.\n     * @param  {string} value Attribute value.\n     * @return {Boolean} Returns true if `value` is valid. Otherwise, returns false.\n     */\n\n\n    DOMPurify.isValidAttribute = function (tag, attr, value) {\n      /* Initialize shared config vars if necessary. */\n      if (!CONFIG) {\n        _parseConfig({});\n      }\n\n      var lcTag = transformCaseFunc(tag);\n      var lcName = transformCaseFunc(attr);\n      return _isValidAttribute(lcTag, lcName, value);\n    };\n    /**\n     * AddHook\n     * Public method to add DOMPurify hooks\n     *\n     * @param {String} entryPoint entry point for the hook to add\n     * @param {Function} hookFunction function to execute\n     */\n\n\n    DOMPurify.addHook = function (entryPoint, hookFunction) {\n      if (typeof hookFunction !== 'function') {\n        return;\n      }\n\n      hooks[entryPoint] = hooks[entryPoint] || [];\n      arrayPush(hooks[entryPoint], hookFunction);\n    };\n    /**\n     * RemoveHook\n     * Public method to remove a DOMPurify hook at a given entryPoint\n     * (pops it from the stack of hooks if more are present)\n     *\n     * @param {String} entryPoint entry point for the hook to remove\n     * @return {Function} removed(popped) hook\n     */\n\n\n    DOMPurify.removeHook = function (entryPoint) {\n      if (hooks[entryPoint]) {\n        return arrayPop(hooks[entryPoint]);\n      }\n    };\n    /**\n     * RemoveHooks\n     * Public method to remove all DOMPurify hooks at a given entryPoint\n     *\n     * @param  {String} entryPoint entry point for the hooks to remove\n     */\n\n\n    DOMPurify.removeHooks = function (entryPoint) {\n      if (hooks[entryPoint]) {\n        hooks[entryPoint] = [];\n      }\n    };\n    /**\n     * RemoveAllHooks\n     * Public method to remove all DOMPurify hooks\n     *\n     */\n\n\n    DOMPurify.removeAllHooks = function () {\n      hooks = {};\n    };\n\n    return DOMPurify;\n  }\n\n  var purify = createDOMPurify();\n\n  return purify;\n\n}));\n"], "mappings": "AAAA;;AAEA,CAAC,UAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,CAACD,OAAO,GAAGD,OAAO,CAAC,CAAC,GACzF,OAAOG,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAACH,OAAO,CAAC,IAC3DD,MAAM,GAAG,OAAOM,UAAU,KAAK,WAAW,GAAGA,UAAU,GAAGN,MAAM,IAAIO,IAAI,EAAEP,MAAM,CAACQ,SAAS,GAAGP,OAAO,CAAC,CAAC,CAAC;AAC1G,CAAC,EAAE,IAAI,EAAG,YAAY;EAAE,YAAY;;EAElC,SAASQ,OAAOA,CAACC,GAAG,EAAE;IACpB,yBAAyB;;IAEzB,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,GAAG,EAAE;MAClG,OAAO,OAAOA,GAAG;IACnB,CAAC,GAAG,UAAUA,GAAG,EAAE;MACjB,OAAOA,GAAG,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;IAC7H,CAAC,EAAED,OAAO,CAACC,GAAG,CAAC;EACjB;EAEA,SAASK,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;IAC7BF,eAAe,GAAGG,MAAM,CAACC,cAAc,IAAI,SAASJ,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;MACxED,CAAC,CAACI,SAAS,GAAGH,CAAC;MACf,OAAOD,CAAC;IACV,CAAC;IAED,OAAOD,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC9B;EAEA,SAASI,yBAAyBA,CAAA,EAAG;IACnC,IAAI,OAAOC,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;IACtE,IAAID,OAAO,CAACC,SAAS,CAACC,IAAI,EAAE,OAAO,KAAK;IACxC,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;IAE5C,IAAI;MACFC,OAAO,CAACZ,SAAS,CAACa,OAAO,CAACC,IAAI,CAACN,OAAO,CAACC,SAAS,CAACG,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;MAC9E,OAAO,IAAI;IACb,CAAC,CAAC,OAAOG,CAAC,EAAE;MACV,OAAO,KAAK;IACd;EACF;EAEA,SAASC,UAAUA,CAACC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE;IACvC,IAAIZ,yBAAyB,CAAC,CAAC,EAAE;MAC/BS,UAAU,GAAGR,OAAO,CAACC,SAAS;IAChC,CAAC,MAAM;MACLO,UAAU,GAAG,SAASA,UAAUA,CAACC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE;QACpD,IAAIC,CAAC,GAAG,CAAC,IAAI,CAAC;QACdA,CAAC,CAACC,IAAI,CAACC,KAAK,CAACF,CAAC,EAAEF,IAAI,CAAC;QACrB,IAAIK,WAAW,GAAGC,QAAQ,CAACC,IAAI,CAACH,KAAK,CAACL,MAAM,EAAEG,CAAC,CAAC;QAChD,IAAIM,QAAQ,GAAG,IAAIH,WAAW,CAAC,CAAC;QAChC,IAAIJ,KAAK,EAAElB,eAAe,CAACyB,QAAQ,EAAEP,KAAK,CAACnB,SAAS,CAAC;QACrD,OAAO0B,QAAQ;MACjB,CAAC;IACH;IAEA,OAAOV,UAAU,CAACM,KAAK,CAAC,IAAI,EAAEK,SAAS,CAAC;EAC1C;EAEA,SAASC,kBAAkBA,CAACC,GAAG,EAAE;IAC/B,OAAOC,kBAAkB,CAACD,GAAG,CAAC,IAAIE,gBAAgB,CAACF,GAAG,CAAC,IAAIG,2BAA2B,CAACH,GAAG,CAAC,IAAII,kBAAkB,CAAC,CAAC;EACrH;EAEA,SAASH,kBAAkBA,CAACD,GAAG,EAAE;IAC/B,IAAIK,KAAK,CAACC,OAAO,CAACN,GAAG,CAAC,EAAE,OAAOO,iBAAiB,CAACP,GAAG,CAAC;EACvD;EAEA,SAASE,gBAAgBA,CAACM,IAAI,EAAE;IAC9B,IAAI,OAAOxC,MAAM,KAAK,WAAW,IAAIwC,IAAI,CAACxC,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAIuC,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAOH,KAAK,CAACI,IAAI,CAACD,IAAI,CAAC;EAC3H;EAEA,SAASL,2BAA2BA,CAAC9B,CAAC,EAAEqC,MAAM,EAAE;IAC9C,IAAI,CAACrC,CAAC,EAAE;IACR,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOkC,iBAAiB,CAAClC,CAAC,EAAEqC,MAAM,CAAC;IAC9D,IAAIC,CAAC,GAAGpC,MAAM,CAACJ,SAAS,CAACyC,QAAQ,CAAC3B,IAAI,CAACZ,CAAC,CAAC,CAACwC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtD,IAAIF,CAAC,KAAK,QAAQ,IAAItC,CAAC,CAACH,WAAW,EAAEyC,CAAC,GAAGtC,CAAC,CAACH,WAAW,CAAC4C,IAAI;IAC3D,IAAIH,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAON,KAAK,CAACI,IAAI,CAACpC,CAAC,CAAC;IACpD,IAAIsC,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACI,IAAI,CAACJ,CAAC,CAAC,EAAE,OAAOJ,iBAAiB,CAAClC,CAAC,EAAEqC,MAAM,CAAC;EAClH;EAEA,SAASH,iBAAiBA,CAACP,GAAG,EAAEgB,GAAG,EAAE;IACnC,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGhB,GAAG,CAACiB,MAAM,EAAED,GAAG,GAAGhB,GAAG,CAACiB,MAAM;IAErD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAG,IAAId,KAAK,CAACW,GAAG,CAAC,EAAEE,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAEC,IAAI,CAACD,CAAC,CAAC,GAAGlB,GAAG,CAACkB,CAAC,CAAC;IAErE,OAAOC,IAAI;EACb;EAEA,SAASf,kBAAkBA,CAAA,EAAG;IAC5B,MAAM,IAAIgB,SAAS,CAAC,sIAAsI,CAAC;EAC7J;EAEA,IAAIC,cAAc,GAAG9C,MAAM,CAAC8C,cAAc;IACtC7C,cAAc,GAAGD,MAAM,CAACC,cAAc;IACtC8C,QAAQ,GAAG/C,MAAM,CAAC+C,QAAQ;IAC1BC,cAAc,GAAGhD,MAAM,CAACgD,cAAc;IACtCC,wBAAwB,GAAGjD,MAAM,CAACiD,wBAAwB;EAC9D,IAAIC,MAAM,GAAGlD,MAAM,CAACkD,MAAM;IACtBC,IAAI,GAAGnD,MAAM,CAACmD,IAAI;IAClBC,MAAM,GAAGpD,MAAM,CAACoD,MAAM,CAAC,CAAC;;EAE5B,IAAIC,IAAI,GAAG,OAAOjD,OAAO,KAAK,WAAW,IAAIA,OAAO;IAChDc,KAAK,GAAGmC,IAAI,CAACnC,KAAK;IAClBb,SAAS,GAAGgD,IAAI,CAAChD,SAAS;EAE9B,IAAI,CAACa,KAAK,EAAE;IACVA,KAAK,GAAG,SAASA,KAAKA,CAACoC,GAAG,EAAEC,SAAS,EAAEzC,IAAI,EAAE;MAC3C,OAAOwC,GAAG,CAACpC,KAAK,CAACqC,SAAS,EAAEzC,IAAI,CAAC;IACnC,CAAC;EACH;EAEA,IAAI,CAACoC,MAAM,EAAE;IACXA,MAAM,GAAG,SAASA,MAAMA,CAACM,CAAC,EAAE;MAC1B,OAAOA,CAAC;IACV,CAAC;EACH;EAEA,IAAI,CAACL,IAAI,EAAE;IACTA,IAAI,GAAG,SAASA,IAAIA,CAACK,CAAC,EAAE;MACtB,OAAOA,CAAC;IACV,CAAC;EACH;EAEA,IAAI,CAACnD,SAAS,EAAE;IACdA,SAAS,GAAG,SAASA,SAASA,CAACoD,IAAI,EAAE3C,IAAI,EAAE;MACzC,OAAOF,UAAU,CAAC6C,IAAI,EAAEjC,kBAAkB,CAACV,IAAI,CAAC,CAAC;IACnD,CAAC;EACH;EAEA,IAAI4C,YAAY,GAAGC,OAAO,CAAC7B,KAAK,CAAClC,SAAS,CAACgE,OAAO,CAAC;EACnD,IAAIC,QAAQ,GAAGF,OAAO,CAAC7B,KAAK,CAAClC,SAAS,CAACkE,GAAG,CAAC;EAC3C,IAAIC,SAAS,GAAGJ,OAAO,CAAC7B,KAAK,CAAClC,SAAS,CAACqB,IAAI,CAAC;EAC7C,IAAI+C,iBAAiB,GAAGL,OAAO,CAACM,MAAM,CAACrE,SAAS,CAACsE,WAAW,CAAC;EAC7D,IAAIC,cAAc,GAAGR,OAAO,CAACM,MAAM,CAACrE,SAAS,CAACyC,QAAQ,CAAC;EACvD,IAAI+B,WAAW,GAAGT,OAAO,CAACM,MAAM,CAACrE,SAAS,CAACyE,KAAK,CAAC;EACjD,IAAIC,aAAa,GAAGX,OAAO,CAACM,MAAM,CAACrE,SAAS,CAAC2E,OAAO,CAAC;EACrD,IAAIC,aAAa,GAAGb,OAAO,CAACM,MAAM,CAACrE,SAAS,CAAC6E,OAAO,CAAC;EACrD,IAAIC,UAAU,GAAGf,OAAO,CAACM,MAAM,CAACrE,SAAS,CAAC+E,IAAI,CAAC;EAC/C,IAAIC,UAAU,GAAGjB,OAAO,CAACkB,MAAM,CAACjF,SAAS,CAAC4C,IAAI,CAAC;EAC/C,IAAIsC,eAAe,GAAGC,WAAW,CAAClC,SAAS,CAAC;EAC5C,SAASc,OAAOA,CAACqB,IAAI,EAAE;IACrB,OAAO,UAAUC,OAAO,EAAE;MACxB,KAAK,IAAIC,IAAI,GAAG3D,SAAS,CAACmB,MAAM,EAAE5B,IAAI,GAAG,IAAIgB,KAAK,CAACoD,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGD,IAAI,EAAEC,IAAI,EAAE,EAAE;QAC1GrE,IAAI,CAACqE,IAAI,GAAG,CAAC,CAAC,GAAG5D,SAAS,CAAC4D,IAAI,CAAC;MAClC;MAEA,OAAOjE,KAAK,CAAC8D,IAAI,EAAEC,OAAO,EAAEnE,IAAI,CAAC;IACnC,CAAC;EACH;EACA,SAASiE,WAAWA,CAACC,IAAI,EAAE;IACzB,OAAO,YAAY;MACjB,KAAK,IAAII,KAAK,GAAG7D,SAAS,CAACmB,MAAM,EAAE5B,IAAI,GAAG,IAAIgB,KAAK,CAACsD,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;QAC7FvE,IAAI,CAACuE,KAAK,CAAC,GAAG9D,SAAS,CAAC8D,KAAK,CAAC;MAChC;MAEA,OAAOhF,SAAS,CAAC2E,IAAI,EAAElE,IAAI,CAAC;IAC9B,CAAC;EACH;EACA;;EAEA,SAASwE,QAAQA,CAACC,GAAG,EAAEC,KAAK,EAAEC,iBAAiB,EAAE;IAC/C,IAAIC,kBAAkB;IAEtBD,iBAAiB,GAAG,CAACC,kBAAkB,GAAGD,iBAAiB,MAAM,IAAI,IAAIC,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAG1B,iBAAiB;IAE/I,IAAI/D,cAAc,EAAE;MAClB;MACA;MACA;MACAA,cAAc,CAACsF,GAAG,EAAE,IAAI,CAAC;IAC3B;IAEA,IAAII,CAAC,GAAGH,KAAK,CAAC9C,MAAM;IAEpB,OAAOiD,CAAC,EAAE,EAAE;MACV,IAAIC,OAAO,GAAGJ,KAAK,CAACG,CAAC,CAAC;MAEtB,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;QAC/B,IAAIC,SAAS,GAAGJ,iBAAiB,CAACG,OAAO,CAAC;QAE1C,IAAIC,SAAS,KAAKD,OAAO,EAAE;UACzB;UACA,IAAI,CAAC7C,QAAQ,CAACyC,KAAK,CAAC,EAAE;YACpBA,KAAK,CAACG,CAAC,CAAC,GAAGE,SAAS;UACtB;UAEAD,OAAO,GAAGC,SAAS;QACrB;MACF;MAEAN,GAAG,CAACK,OAAO,CAAC,GAAG,IAAI;IACrB;IAEA,OAAOL,GAAG;EACZ;EACA;;EAEA,SAASO,KAAKA,CAACC,MAAM,EAAE;IACrB,IAAIC,SAAS,GAAG5C,MAAM,CAAC,IAAI,CAAC;IAC5B,IAAI6C,QAAQ;IAEZ,KAAKA,QAAQ,IAAIF,MAAM,EAAE;MACvB,IAAI7E,KAAK,CAAC4B,cAAc,EAAEiD,MAAM,EAAE,CAACE,QAAQ,CAAC,CAAC,KAAK,IAAI,EAAE;QACtDD,SAAS,CAACC,QAAQ,CAAC,GAAGF,MAAM,CAACE,QAAQ,CAAC;MACxC;IACF;IAEA,OAAOD,SAAS;EAClB;EACA;AACF;AACA;AACA;;EAEE,SAASE,YAAYA,CAACH,MAAM,EAAEI,IAAI,EAAE;IAClC,OAAOJ,MAAM,KAAK,IAAI,EAAE;MACtB,IAAIK,IAAI,GAAGnD,wBAAwB,CAAC8C,MAAM,EAAEI,IAAI,CAAC;MAEjD,IAAIC,IAAI,EAAE;QACR,IAAIA,IAAI,CAACC,GAAG,EAAE;UACZ,OAAO1C,OAAO,CAACyC,IAAI,CAACC,GAAG,CAAC;QAC1B;QAEA,IAAI,OAAOD,IAAI,CAACE,KAAK,KAAK,UAAU,EAAE;UACpC,OAAO3C,OAAO,CAACyC,IAAI,CAACE,KAAK,CAAC;QAC5B;MACF;MAEAP,MAAM,GAAG/C,cAAc,CAAC+C,MAAM,CAAC;IACjC;IAEA,SAASQ,aAAaA,CAACX,OAAO,EAAE;MAC9BY,OAAO,CAACC,IAAI,CAAC,oBAAoB,EAAEb,OAAO,CAAC;MAC3C,OAAO,IAAI;IACb;IAEA,OAAOW,aAAa;EACtB;EAEA,IAAIG,MAAM,GAAGxD,MAAM,CAAC,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;;EAEh/B,IAAIyD,KAAK,GAAGzD,MAAM,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,UAAU,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,eAAe,EAAE,kBAAkB,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,gBAAgB,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;EACxd,IAAI0D,UAAU,GAAG1D,MAAM,CAAC,CAAC,SAAS,EAAE,eAAe,EAAE,qBAAqB,EAAE,aAAa,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,gBAAgB,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,oBAAoB,EAAE,aAAa,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC;EACtY;EACA;EACA;;EAEA,IAAI2D,aAAa,GAAG3D,MAAM,CAAC,CAAC,SAAS,EAAE,eAAe,EAAE,QAAQ,EAAE,SAAS,EAAE,cAAc,EAAE,WAAW,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,cAAc,EAAE,WAAW,EAAE,SAAS,EAAE,eAAe,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;EAC3U,IAAI4D,QAAQ,GAAG5D,MAAM,CAAC,CAAC,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;EAC9S;;EAEA,IAAI6D,gBAAgB,GAAG7D,MAAM,CAAC,CAAC,SAAS,EAAE,aAAa,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,gBAAgB,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;EACvN,IAAI8D,IAAI,GAAG9D,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC;EAE5B,IAAI+D,IAAI,GAAG/D,MAAM,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,gBAAgB,EAAE,cAAc,EAAE,sBAAsB,EAAE,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,QAAQ,EAAE,aAAa,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,yBAAyB,EAAE,uBAAuB,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,cAAc,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;EAC7qC,IAAIgE,GAAG,GAAGhE,MAAM,CAAC,CAAC,eAAe,EAAE,YAAY,EAAE,UAAU,EAAE,oBAAoB,EAAE,QAAQ,EAAE,eAAe,EAAE,eAAe,EAAE,SAAS,EAAE,eAAe,EAAE,gBAAgB,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,eAAe,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,qBAAqB,EAAE,6BAA6B,EAAE,eAAe,EAAE,iBAAiB,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,iBAAiB,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,cAAc,EAAE,WAAW,EAAE,QAAQ,EAAE,aAAa,EAAE,aAAa,EAAE,eAAe,EAAE,aAAa,EAAE,WAAW,EAAE,kBAAkB,EAAE,cAAc,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,eAAe,EAAE,mBAAmB,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,iBAAiB,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,aAAa,EAAE,kBAAkB,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,UAAU,EAAE,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,cAAc,EAAE,QAAQ,EAAE,eAAe,EAAE,qBAAqB,EAAE,gBAAgB,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,aAAa,EAAE,YAAY,EAAE,cAAc,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,QAAQ,EAAE,cAAc,EAAE,OAAO,EAAE,cAAc,EAAE,gBAAgB,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,kBAAkB,EAAE,aAAa,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,YAAY,EAAE,eAAe,EAAE,eAAe,EAAE,OAAO,EAAE,cAAc,EAAE,MAAM,EAAE,cAAc,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,YAAY,CAAC,CAAC;EAChxE,IAAIiE,MAAM,GAAGjE,MAAM,CAAC,CAAC,QAAQ,EAAE,aAAa,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,cAAc,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,eAAe,EAAE,QAAQ,EAAE,QAAQ,EAAE,gBAAgB,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,EAAE,eAAe,EAAE,sBAAsB,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;EAClqB,IAAIkE,GAAG,GAAGlE,MAAM,CAAC,CAAC,YAAY,EAAE,QAAQ,EAAE,aAAa,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;EAErF,IAAImE,aAAa,GAAGlE,IAAI,CAAC,2BAA2B,CAAC,CAAC,CAAC;;EAEvD,IAAImE,QAAQ,GAAGnE,IAAI,CAAC,uBAAuB,CAAC;EAC5C,IAAIoE,WAAW,GAAGpE,IAAI,CAAC,eAAe,CAAC;EACvC,IAAIqE,SAAS,GAAGrE,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAC;;EAEpD,IAAIsE,SAAS,GAAGtE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;;EAExC,IAAIuE,cAAc,GAAGvE,IAAI,CAAC,uFAAuF,CAAC;EAClH,CAAC;EACD,IAAIwE,iBAAiB,GAAGxE,IAAI,CAAC,uBAAuB,CAAC;EACrD,IAAIyE,eAAe,GAAGzE,IAAI,CAAC,6DAA6D,CAAC;EACzF,CAAC;EACD,IAAI0E,YAAY,GAAG1E,IAAI,CAAC,SAAS,CAAC;EAElC,IAAI2E,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnC,OAAO,OAAOC,MAAM,KAAK,WAAW,GAAG,IAAI,GAAGA,MAAM;EACtD,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;;EAGE,IAAIC,yBAAyB,GAAG,SAASA,yBAAyBA,CAACC,YAAY,EAAEC,QAAQ,EAAE;IACzF,IAAI3I,OAAO,CAAC0I,YAAY,CAAC,KAAK,QAAQ,IAAI,OAAOA,YAAY,CAACE,YAAY,KAAK,UAAU,EAAE;MACzF,OAAO,IAAI;IACb,CAAC,CAAC;IACF;IACA;;IAGA,IAAIC,MAAM,GAAG,IAAI;IACjB,IAAIC,SAAS,GAAG,uBAAuB;IAEvC,IAAIH,QAAQ,CAACI,aAAa,IAAIJ,QAAQ,CAACI,aAAa,CAACC,YAAY,CAACF,SAAS,CAAC,EAAE;MAC5ED,MAAM,GAAGF,QAAQ,CAACI,aAAa,CAACE,YAAY,CAACH,SAAS,CAAC;IACzD;IAEA,IAAII,UAAU,GAAG,WAAW,IAAIL,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,EAAE,CAAC;IAE3D,IAAI;MACF,OAAOH,YAAY,CAACE,YAAY,CAACM,UAAU,EAAE;QAC3CC,UAAU,EAAE,SAASA,UAAUA,CAACzB,IAAI,EAAE;UACpC,OAAOA,IAAI;QACb,CAAC;QACD0B,eAAe,EAAE,SAASA,eAAeA,CAACC,SAAS,EAAE;UACnD,OAAOA,SAAS;QAClB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,CAAC,EAAE;MACV;MACA;MACA;MACArC,OAAO,CAACC,IAAI,CAAC,sBAAsB,GAAGgC,UAAU,GAAG,wBAAwB,CAAC;MAC5E,OAAO,IAAI;IACb;EACF,CAAC;EAED,SAASK,eAAeA,CAAA,EAAG;IACzB,IAAIf,MAAM,GAAGxG,SAAS,CAACmB,MAAM,GAAG,CAAC,IAAInB,SAAS,CAAC,CAAC,CAAC,KAAKwH,SAAS,GAAGxH,SAAS,CAAC,CAAC,CAAC,GAAGuG,SAAS,CAAC,CAAC;IAE5F,IAAIxI,SAAS,GAAG,SAASA,SAASA,CAAC0J,IAAI,EAAE;MACvC,OAAOF,eAAe,CAACE,IAAI,CAAC;IAC9B,CAAC;IACD;AACJ;AACA;AACA;;IAGI1J,SAAS,CAAC2J,OAAO,GAAG,OAAO;IAC3B;AACJ;AACA;AACA;;IAEI3J,SAAS,CAAC4J,OAAO,GAAG,EAAE;IAEtB,IAAI,CAACnB,MAAM,IAAI,CAACA,MAAM,CAACG,QAAQ,IAAIH,MAAM,CAACG,QAAQ,CAACiB,QAAQ,KAAK,CAAC,EAAE;MACjE;MACA;MACA7J,SAAS,CAAC8J,WAAW,GAAG,KAAK;MAC7B,OAAO9J,SAAS;IAClB;IAEA,IAAI+J,gBAAgB,GAAGtB,MAAM,CAACG,QAAQ;IACtC,IAAIA,QAAQ,GAAGH,MAAM,CAACG,QAAQ;IAC9B,IAAIoB,gBAAgB,GAAGvB,MAAM,CAACuB,gBAAgB;MAC1CC,mBAAmB,GAAGxB,MAAM,CAACwB,mBAAmB;MAChDC,IAAI,GAAGzB,MAAM,CAACyB,IAAI;MAClBC,OAAO,GAAG1B,MAAM,CAAC0B,OAAO;MACxBC,UAAU,GAAG3B,MAAM,CAAC2B,UAAU;MAC9BC,oBAAoB,GAAG5B,MAAM,CAAC6B,YAAY;MAC1CA,YAAY,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG5B,MAAM,CAAC6B,YAAY,IAAI7B,MAAM,CAAC8B,eAAe,GAAGF,oBAAoB;MACrHG,eAAe,GAAG/B,MAAM,CAAC+B,eAAe;MACxCC,SAAS,GAAGhC,MAAM,CAACgC,SAAS;MAC5B9B,YAAY,GAAGF,MAAM,CAACE,YAAY;IACtC,IAAI+B,gBAAgB,GAAGP,OAAO,CAAC7J,SAAS;IACxC,IAAIqK,SAAS,GAAG/D,YAAY,CAAC8D,gBAAgB,EAAE,WAAW,CAAC;IAC3D,IAAIE,cAAc,GAAGhE,YAAY,CAAC8D,gBAAgB,EAAE,aAAa,CAAC;IAClE,IAAIG,aAAa,GAAGjE,YAAY,CAAC8D,gBAAgB,EAAE,YAAY,CAAC;IAChE,IAAII,aAAa,GAAGlE,YAAY,CAAC8D,gBAAgB,EAAE,YAAY,CAAC,CAAC,CAAC;IAClE;IACA;IACA;IACA;IACA;;IAEA,IAAI,OAAOT,mBAAmB,KAAK,UAAU,EAAE;MAC7C,IAAIc,QAAQ,GAAGnC,QAAQ,CAACoC,aAAa,CAAC,UAAU,CAAC;MAEjD,IAAID,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACE,OAAO,CAACC,aAAa,EAAE;QACtDtC,QAAQ,GAAGmC,QAAQ,CAACE,OAAO,CAACC,aAAa;MAC3C;IACF;IAEA,IAAIC,kBAAkB,GAAGzC,yBAAyB,CAACC,YAAY,EAAEoB,gBAAgB,CAAC;IAElF,IAAIqB,SAAS,GAAGD,kBAAkB,GAAGA,kBAAkB,CAAC/B,UAAU,CAAC,EAAE,CAAC,GAAG,EAAE;IAC3E,IAAIiC,SAAS,GAAGzC,QAAQ;MACpB0C,cAAc,GAAGD,SAAS,CAACC,cAAc;MACzCC,kBAAkB,GAAGF,SAAS,CAACE,kBAAkB;MACjDC,sBAAsB,GAAGH,SAAS,CAACG,sBAAsB;MACzDC,oBAAoB,GAAGJ,SAAS,CAACI,oBAAoB;IACzD,IAAIC,UAAU,GAAG3B,gBAAgB,CAAC2B,UAAU;IAC5C,IAAIC,YAAY,GAAG,CAAC,CAAC;IAErB,IAAI;MACFA,YAAY,GAAGnF,KAAK,CAACoC,QAAQ,CAAC,CAAC+C,YAAY,GAAG/C,QAAQ,CAAC+C,YAAY,GAAG,CAAC,CAAC;IAC1E,CAAC,CAAC,OAAOpC,CAAC,EAAE,CAAC;IAEb,IAAIqC,KAAK,GAAG,CAAC,CAAC;IACd;AACJ;AACA;;IAEI5L,SAAS,CAAC8J,WAAW,GAAG,OAAOgB,aAAa,KAAK,UAAU,IAAIQ,cAAc,IAAIA,cAAc,CAACO,kBAAkB,KAAKpC,SAAS,IAAIkC,YAAY,KAAK,CAAC;IACtJ,IAAIG,eAAe,GAAG/D,aAAa;MAC/BgE,UAAU,GAAG/D,QAAQ;MACrBgE,aAAa,GAAG/D,WAAW;MAC3BgE,WAAW,GAAG/D,SAAS;MACvBgE,WAAW,GAAG/D,SAAS;MACvBgE,mBAAmB,GAAG9D,iBAAiB;MACvC+D,iBAAiB,GAAG9D,eAAe;IACvC,IAAI+D,gBAAgB,GAAGjE,cAAc;IACrC;AACJ;AACA;AACA;;IAEI;;IAEA,IAAIkE,YAAY,GAAG,IAAI;IACvB,IAAIC,oBAAoB,GAAGvG,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,CAACwG,MAAM,CAACtK,kBAAkB,CAACkF,MAAM,CAAC,EAAElF,kBAAkB,CAACmF,KAAK,CAAC,EAAEnF,kBAAkB,CAACoF,UAAU,CAAC,EAAEpF,kBAAkB,CAACsF,QAAQ,CAAC,EAAEtF,kBAAkB,CAACwF,IAAI,CAAC,CAAC,CAAC;IACjM;;IAEA,IAAI+E,YAAY,GAAG,IAAI;IACvB,IAAIC,oBAAoB,GAAG1G,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,CAACwG,MAAM,CAACtK,kBAAkB,CAACyF,IAAI,CAAC,EAAEzF,kBAAkB,CAAC0F,GAAG,CAAC,EAAE1F,kBAAkB,CAAC2F,MAAM,CAAC,EAAE3F,kBAAkB,CAAC4F,GAAG,CAAC,CAAC,CAAC;IAC1J;AACJ;AACA;AACA;AACA;AACA;;IAEI,IAAI6E,uBAAuB,GAAGjM,MAAM,CAACmD,IAAI,CAACnD,MAAM,CAACoD,MAAM,CAAC,IAAI,EAAE;MAC5D8I,YAAY,EAAE;QACZC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAE,IAAI;QAChB/F,KAAK,EAAE;MACT,CAAC;MACDgG,kBAAkB,EAAE;QAClBH,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAE,IAAI;QAChB/F,KAAK,EAAE;MACT,CAAC;MACDiG,8BAA8B,EAAE;QAC9BJ,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE,KAAK;QACnBC,UAAU,EAAE,IAAI;QAChB/F,KAAK,EAAE;MACT;IACF,CAAC,CAAC,CAAC;IACH;;IAEA,IAAIkG,WAAW,GAAG,IAAI;IACtB;;IAEA,IAAIC,WAAW,GAAG,IAAI;IACtB;;IAEA,IAAIC,eAAe,GAAG,IAAI;IAC1B;;IAEA,IAAIC,eAAe,GAAG,IAAI;IAC1B;;IAEA,IAAIC,uBAAuB,GAAG,KAAK;IACnC;AACJ;;IAEI,IAAIC,wBAAwB,GAAG,IAAI;IACnC;AACJ;AACA;;IAEI,IAAIC,kBAAkB,GAAG,KAAK;IAC9B;;IAEA,IAAIC,cAAc,GAAG,KAAK;IAC1B;;IAEA,IAAIC,UAAU,GAAG,KAAK;IACtB;AACJ;;IAEI,IAAIC,UAAU,GAAG,KAAK;IACtB;AACJ;AACA;AACA;;IAEI,IAAIC,UAAU,GAAG,KAAK;IACtB;AACJ;;IAEI,IAAIC,mBAAmB,GAAG,KAAK;IAC/B;AACJ;;IAEI,IAAIC,mBAAmB,GAAG,KAAK;IAC/B;AACJ;AACA;;IAEI,IAAIC,YAAY,GAAG,IAAI;IACvB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAEI,IAAIC,oBAAoB,GAAG,KAAK;IAChC,IAAIC,2BAA2B,GAAG,eAAe;IACjD;;IAEA,IAAIC,YAAY,GAAG,IAAI;IACvB;AACJ;;IAEI,IAAIC,QAAQ,GAAG,KAAK;IACpB;;IAEA,IAAIC,YAAY,GAAG,CAAC,CAAC;IACrB;;IAEA,IAAIC,eAAe,GAAG,IAAI;IAC1B,IAAIC,uBAAuB,GAAGtI,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;IAC/R;;IAEA,IAAIuI,aAAa,GAAG,IAAI;IACxB,IAAIC,qBAAqB,GAAGxI,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC/F;;IAEA,IAAIyI,mBAAmB,GAAG,IAAI;IAC9B,IAAIC,2BAA2B,GAAG1I,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC/K,IAAI2I,gBAAgB,GAAG,oCAAoC;IAC3D,IAAIC,aAAa,GAAG,4BAA4B;IAChD,IAAIC,cAAc,GAAG,8BAA8B;IACnD;;IAEA,IAAIC,SAAS,GAAGD,cAAc;IAC9B,IAAIE,cAAc,GAAG,KAAK;IAC1B;;IAEA,IAAIC,kBAAkB,GAAG,IAAI;IAC7B,IAAIC,0BAA0B,GAAGjJ,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC2I,gBAAgB,EAAEC,aAAa,EAAEC,cAAc,CAAC,EAAEhK,cAAc,CAAC;IAChH;;IAEA,IAAIqK,iBAAiB;IACrB,IAAIC,4BAA4B,GAAG,CAAC,uBAAuB,EAAE,WAAW,CAAC;IACzE,IAAIC,yBAAyB,GAAG,WAAW;IAC3C,IAAIjJ,iBAAiB;IACrB;;IAEA,IAAIkJ,MAAM,GAAG,IAAI;IACjB;;IAEA;;IAEA,IAAIC,WAAW,GAAG1G,QAAQ,CAACoC,aAAa,CAAC,MAAM,CAAC;IAEhD,IAAIuE,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,SAAS,EAAE;MAC5D,OAAOA,SAAS,YAAYjK,MAAM,IAAIiK,SAAS,YAAY1N,QAAQ;IACrE,CAAC;IACD;AACJ;AACA;AACA;AACA;IACI;;IAGA,IAAI2N,YAAY,GAAG,SAASA,YAAYA,CAACC,GAAG,EAAE;MAC5C,IAAIL,MAAM,IAAIA,MAAM,KAAKK,GAAG,EAAE;QAC5B;MACF;MACA;;MAGA,IAAI,CAACA,GAAG,IAAIzP,OAAO,CAACyP,GAAG,CAAC,KAAK,QAAQ,EAAE;QACrCA,GAAG,GAAG,CAAC,CAAC;MACV;MACA;;MAGAA,GAAG,GAAGlJ,KAAK,CAACkJ,GAAG,CAAC;MAChBR,iBAAiB;MAAG;MACpBC,4BAA4B,CAAChK,OAAO,CAACuK,GAAG,CAACR,iBAAiB,CAAC,KAAK,CAAC,CAAC,GAAGA,iBAAiB,GAAGE,yBAAyB,GAAGF,iBAAiB,GAAGQ,GAAG,CAACR,iBAAiB,CAAC,CAAC;;MAEhK/I,iBAAiB,GAAG+I,iBAAiB,KAAK,uBAAuB,GAAGrK,cAAc,GAAGH,iBAAiB;MACtG;;MAEA4H,YAAY,GAAG,cAAc,IAAIoD,GAAG,GAAG1J,QAAQ,CAAC,CAAC,CAAC,EAAE0J,GAAG,CAACpD,YAAY,EAAEnG,iBAAiB,CAAC,GAAGoG,oBAAoB;MAC/GE,YAAY,GAAG,cAAc,IAAIiD,GAAG,GAAG1J,QAAQ,CAAC,CAAC,CAAC,EAAE0J,GAAG,CAACjD,YAAY,EAAEtG,iBAAiB,CAAC,GAAGuG,oBAAoB;MAC/GsC,kBAAkB,GAAG,oBAAoB,IAAIU,GAAG,GAAG1J,QAAQ,CAAC,CAAC,CAAC,EAAE0J,GAAG,CAACV,kBAAkB,EAAEnK,cAAc,CAAC,GAAGoK,0BAA0B;MACpIR,mBAAmB,GAAG,mBAAmB,IAAIiB,GAAG,GAAG1J,QAAQ,CAACQ,KAAK,CAACkI,2BAA2B,CAAC;MAAE;MAChGgB,GAAG,CAACC,iBAAiB;MAAE;MACvBxJ,iBAAiB,CAAC;MAClB,CAAC,CAAC;MAAA,EACAuI,2BAA2B;MAC7BH,aAAa,GAAG,mBAAmB,IAAImB,GAAG,GAAG1J,QAAQ,CAACQ,KAAK,CAACgI,qBAAqB,CAAC;MAAE;MACpFkB,GAAG,CAACE,iBAAiB;MAAE;MACvBzJ,iBAAiB,CAAC;MAClB,CAAC,CAAC;MAAA,EACAqI,qBAAqB;MACvBH,eAAe,GAAG,iBAAiB,IAAIqB,GAAG,GAAG1J,QAAQ,CAAC,CAAC,CAAC,EAAE0J,GAAG,CAACrB,eAAe,EAAElI,iBAAiB,CAAC,GAAGmI,uBAAuB;MAC3HpB,WAAW,GAAG,aAAa,IAAIwC,GAAG,GAAG1J,QAAQ,CAAC,CAAC,CAAC,EAAE0J,GAAG,CAACxC,WAAW,EAAE/G,iBAAiB,CAAC,GAAG,CAAC,CAAC;MAC1FgH,WAAW,GAAG,aAAa,IAAIuC,GAAG,GAAG1J,QAAQ,CAAC,CAAC,CAAC,EAAE0J,GAAG,CAACvC,WAAW,EAAEhH,iBAAiB,CAAC,GAAG,CAAC,CAAC;MAC1FiI,YAAY,GAAG,cAAc,IAAIsB,GAAG,GAAGA,GAAG,CAACtB,YAAY,GAAG,KAAK;MAC/DhB,eAAe,GAAGsC,GAAG,CAACtC,eAAe,KAAK,KAAK,CAAC,CAAC;;MAEjDC,eAAe,GAAGqC,GAAG,CAACrC,eAAe,KAAK,KAAK,CAAC,CAAC;;MAEjDC,uBAAuB,GAAGoC,GAAG,CAACpC,uBAAuB,IAAI,KAAK,CAAC,CAAC;;MAEhEC,wBAAwB,GAAGmC,GAAG,CAACnC,wBAAwB,KAAK,KAAK,CAAC,CAAC;;MAEnEC,kBAAkB,GAAGkC,GAAG,CAAClC,kBAAkB,IAAI,KAAK,CAAC,CAAC;;MAEtDC,cAAc,GAAGiC,GAAG,CAACjC,cAAc,IAAI,KAAK,CAAC,CAAC;;MAE9CG,UAAU,GAAG8B,GAAG,CAAC9B,UAAU,IAAI,KAAK,CAAC,CAAC;;MAEtCC,mBAAmB,GAAG6B,GAAG,CAAC7B,mBAAmB,IAAI,KAAK,CAAC,CAAC;;MAExDC,mBAAmB,GAAG4B,GAAG,CAAC5B,mBAAmB,IAAI,KAAK,CAAC,CAAC;;MAExDH,UAAU,GAAG+B,GAAG,CAAC/B,UAAU,IAAI,KAAK,CAAC,CAAC;;MAEtCI,YAAY,GAAG2B,GAAG,CAAC3B,YAAY,KAAK,KAAK,CAAC,CAAC;;MAE3CC,oBAAoB,GAAG0B,GAAG,CAAC1B,oBAAoB,IAAI,KAAK,CAAC,CAAC;;MAE1DE,YAAY,GAAGwB,GAAG,CAACxB,YAAY,KAAK,KAAK,CAAC,CAAC;;MAE3CC,QAAQ,GAAGuB,GAAG,CAACvB,QAAQ,IAAI,KAAK,CAAC,CAAC;;MAElC9B,gBAAgB,GAAGqD,GAAG,CAACG,kBAAkB,IAAIxD,gBAAgB;MAC7DyC,SAAS,GAAGY,GAAG,CAACZ,SAAS,IAAID,cAAc;MAC3ClC,uBAAuB,GAAG+C,GAAG,CAAC/C,uBAAuB,IAAI,CAAC,CAAC;MAE3D,IAAI+C,GAAG,CAAC/C,uBAAuB,IAAI4C,iBAAiB,CAACG,GAAG,CAAC/C,uBAAuB,CAACC,YAAY,CAAC,EAAE;QAC9FD,uBAAuB,CAACC,YAAY,GAAG8C,GAAG,CAAC/C,uBAAuB,CAACC,YAAY;MACjF;MAEA,IAAI8C,GAAG,CAAC/C,uBAAuB,IAAI4C,iBAAiB,CAACG,GAAG,CAAC/C,uBAAuB,CAACK,kBAAkB,CAAC,EAAE;QACpGL,uBAAuB,CAACK,kBAAkB,GAAG0C,GAAG,CAAC/C,uBAAuB,CAACK,kBAAkB;MAC7F;MAEA,IAAI0C,GAAG,CAAC/C,uBAAuB,IAAI,OAAO+C,GAAG,CAAC/C,uBAAuB,CAACM,8BAA8B,KAAK,SAAS,EAAE;QAClHN,uBAAuB,CAACM,8BAA8B,GAAGyC,GAAG,CAAC/C,uBAAuB,CAACM,8BAA8B;MACrH;MAEA,IAAIO,kBAAkB,EAAE;QACtBH,eAAe,GAAG,KAAK;MACzB;MAEA,IAAIQ,mBAAmB,EAAE;QACvBD,UAAU,GAAG,IAAI;MACnB;MACA;;MAGA,IAAIQ,YAAY,EAAE;QAChB9B,YAAY,GAAGtG,QAAQ,CAAC,CAAC,CAAC,EAAE9D,kBAAkB,CAACwF,IAAI,CAAC,CAAC;QACrD+E,YAAY,GAAG,EAAE;QAEjB,IAAI2B,YAAY,CAACzG,IAAI,KAAK,IAAI,EAAE;UAC9B3B,QAAQ,CAACsG,YAAY,EAAElF,MAAM,CAAC;UAC9BpB,QAAQ,CAACyG,YAAY,EAAE9E,IAAI,CAAC;QAC9B;QAEA,IAAIyG,YAAY,CAACxG,GAAG,KAAK,IAAI,EAAE;UAC7B5B,QAAQ,CAACsG,YAAY,EAAEjF,KAAK,CAAC;UAC7BrB,QAAQ,CAACyG,YAAY,EAAE7E,GAAG,CAAC;UAC3B5B,QAAQ,CAACyG,YAAY,EAAE3E,GAAG,CAAC;QAC7B;QAEA,IAAIsG,YAAY,CAAC9G,UAAU,KAAK,IAAI,EAAE;UACpCtB,QAAQ,CAACsG,YAAY,EAAEhF,UAAU,CAAC;UAClCtB,QAAQ,CAACyG,YAAY,EAAE7E,GAAG,CAAC;UAC3B5B,QAAQ,CAACyG,YAAY,EAAE3E,GAAG,CAAC;QAC7B;QAEA,IAAIsG,YAAY,CAACvG,MAAM,KAAK,IAAI,EAAE;UAChC7B,QAAQ,CAACsG,YAAY,EAAE9E,QAAQ,CAAC;UAChCxB,QAAQ,CAACyG,YAAY,EAAE5E,MAAM,CAAC;UAC9B7B,QAAQ,CAACyG,YAAY,EAAE3E,GAAG,CAAC;QAC7B;MACF;MACA;;MAGA,IAAI4H,GAAG,CAACI,QAAQ,EAAE;QAChB,IAAIxD,YAAY,KAAKC,oBAAoB,EAAE;UACzCD,YAAY,GAAG9F,KAAK,CAAC8F,YAAY,CAAC;QACpC;QAEAtG,QAAQ,CAACsG,YAAY,EAAEoD,GAAG,CAACI,QAAQ,EAAE3J,iBAAiB,CAAC;MACzD;MAEA,IAAIuJ,GAAG,CAACK,QAAQ,EAAE;QAChB,IAAItD,YAAY,KAAKC,oBAAoB,EAAE;UACzCD,YAAY,GAAGjG,KAAK,CAACiG,YAAY,CAAC;QACpC;QAEAzG,QAAQ,CAACyG,YAAY,EAAEiD,GAAG,CAACK,QAAQ,EAAE5J,iBAAiB,CAAC;MACzD;MAEA,IAAIuJ,GAAG,CAACC,iBAAiB,EAAE;QACzB3J,QAAQ,CAACyI,mBAAmB,EAAEiB,GAAG,CAACC,iBAAiB,EAAExJ,iBAAiB,CAAC;MACzE;MAEA,IAAIuJ,GAAG,CAACrB,eAAe,EAAE;QACvB,IAAIA,eAAe,KAAKC,uBAAuB,EAAE;UAC/CD,eAAe,GAAG7H,KAAK,CAAC6H,eAAe,CAAC;QAC1C;QAEArI,QAAQ,CAACqI,eAAe,EAAEqB,GAAG,CAACrB,eAAe,EAAElI,iBAAiB,CAAC;MACnE;MACA;;MAGA,IAAI+H,YAAY,EAAE;QAChB5B,YAAY,CAAC,OAAO,CAAC,GAAG,IAAI;MAC9B;MACA;;MAGA,IAAImB,cAAc,EAAE;QAClBzH,QAAQ,CAACsG,YAAY,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;MAClD;MACA;;MAGA,IAAIA,YAAY,CAAC0D,KAAK,EAAE;QACtBhK,QAAQ,CAACsG,YAAY,EAAE,CAAC,OAAO,CAAC,CAAC;QACjC,OAAOY,WAAW,CAAC+C,KAAK;MAC1B,CAAC,CAAC;MACF;;MAGA,IAAIrM,MAAM,EAAE;QACVA,MAAM,CAAC8L,GAAG,CAAC;MACb;MAEAL,MAAM,GAAGK,GAAG;IACd,CAAC;IAED,IAAIQ,8BAA8B,GAAGlK,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACpF,IAAImK,uBAAuB,GAAGnK,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,eAAe,EAAE,MAAM,EAAE,OAAO,EAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAClG;IACA;IACA;;IAEA,IAAIoK,4BAA4B,GAAGpK,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;IAC1F;AACJ;AACA;;IAEI,IAAIqK,YAAY,GAAGrK,QAAQ,CAAC,CAAC,CAAC,EAAEqB,KAAK,CAAC;IACtCrB,QAAQ,CAACqK,YAAY,EAAE/I,UAAU,CAAC;IAClCtB,QAAQ,CAACqK,YAAY,EAAE9I,aAAa,CAAC;IACrC,IAAI+I,eAAe,GAAGtK,QAAQ,CAAC,CAAC,CAAC,EAAEwB,QAAQ,CAAC;IAC5CxB,QAAQ,CAACsK,eAAe,EAAE7I,gBAAgB,CAAC;IAC3C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;IAEI,IAAI8I,oBAAoB,GAAG,SAASA,oBAAoBA,CAACjK,OAAO,EAAE;MAChE,IAAIkK,MAAM,GAAG1F,aAAa,CAACxE,OAAO,CAAC,CAAC,CAAC;MACrC;;MAEA,IAAI,CAACkK,MAAM,IAAI,CAACA,MAAM,CAACC,OAAO,EAAE;QAC9BD,MAAM,GAAG;UACPE,YAAY,EAAE5B,SAAS;UACvB2B,OAAO,EAAE;QACX,CAAC;MACH;MAEA,IAAIA,OAAO,GAAG/L,iBAAiB,CAAC4B,OAAO,CAACmK,OAAO,CAAC;MAChD,IAAIE,aAAa,GAAGjM,iBAAiB,CAAC8L,MAAM,CAACC,OAAO,CAAC;MAErD,IAAI,CAACzB,kBAAkB,CAAC1I,OAAO,CAACoK,YAAY,CAAC,EAAE;QAC7C,OAAO,KAAK;MACd;MAEA,IAAIpK,OAAO,CAACoK,YAAY,KAAK9B,aAAa,EAAE;QAC1C;QACA;QACA;QACA,IAAI4B,MAAM,CAACE,YAAY,KAAK7B,cAAc,EAAE;UAC1C,OAAO4B,OAAO,KAAK,KAAK;QAC1B,CAAC,CAAC;QACF;QACA;;QAGA,IAAID,MAAM,CAACE,YAAY,KAAK/B,gBAAgB,EAAE;UAC5C,OAAO8B,OAAO,KAAK,KAAK,KAAKE,aAAa,KAAK,gBAAgB,IAAIT,8BAA8B,CAACS,aAAa,CAAC,CAAC;QACnH,CAAC,CAAC;QACF;;QAGA,OAAOzP,OAAO,CAACmP,YAAY,CAACI,OAAO,CAAC,CAAC;MACvC;MAEA,IAAInK,OAAO,CAACoK,YAAY,KAAK/B,gBAAgB,EAAE;QAC7C;QACA;QACA;QACA,IAAI6B,MAAM,CAACE,YAAY,KAAK7B,cAAc,EAAE;UAC1C,OAAO4B,OAAO,KAAK,MAAM;QAC3B,CAAC,CAAC;QACF;;QAGA,IAAID,MAAM,CAACE,YAAY,KAAK9B,aAAa,EAAE;UACzC,OAAO6B,OAAO,KAAK,MAAM,IAAIN,uBAAuB,CAACQ,aAAa,CAAC;QACrE,CAAC,CAAC;QACF;;QAGA,OAAOzP,OAAO,CAACoP,eAAe,CAACG,OAAO,CAAC,CAAC;MAC1C;MAEA,IAAInK,OAAO,CAACoK,YAAY,KAAK7B,cAAc,EAAE;QAC3C;QACA;QACA;QACA,IAAI2B,MAAM,CAACE,YAAY,KAAK9B,aAAa,IAAI,CAACuB,uBAAuB,CAACQ,aAAa,CAAC,EAAE;UACpF,OAAO,KAAK;QACd;QAEA,IAAIH,MAAM,CAACE,YAAY,KAAK/B,gBAAgB,IAAI,CAACuB,8BAA8B,CAACS,aAAa,CAAC,EAAE;UAC9F,OAAO,KAAK;QACd,CAAC,CAAC;QACF;;QAGA,OAAO,CAACL,eAAe,CAACG,OAAO,CAAC,KAAKL,4BAA4B,CAACK,OAAO,CAAC,IAAI,CAACJ,YAAY,CAACI,OAAO,CAAC,CAAC;MACvG,CAAC,CAAC;;MAGF,IAAIvB,iBAAiB,KAAK,uBAAuB,IAAIF,kBAAkB,CAAC1I,OAAO,CAACoK,YAAY,CAAC,EAAE;QAC7F,OAAO,IAAI;MACb,CAAC,CAAC;MACF;MACA;MACA;;MAGA,OAAO,KAAK;IACd,CAAC;IACD;AACJ;AACA;AACA;AACA;;IAGI,IAAIE,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAE;MAC7CpM,SAAS,CAACzE,SAAS,CAAC4J,OAAO,EAAE;QAC3BtD,OAAO,EAAEuK;MACX,CAAC,CAAC;MAEF,IAAI;QACF;QACAA,IAAI,CAACC,UAAU,CAACC,WAAW,CAACF,IAAI,CAAC;MACnC,CAAC,CAAC,OAAOtH,CAAC,EAAE;QACV,IAAI;UACFsH,IAAI,CAACG,SAAS,GAAG5F,SAAS;QAC5B,CAAC,CAAC,OAAO7B,CAAC,EAAE;UACVsH,IAAI,CAACI,MAAM,CAAC,CAAC;QACf;MACF;IACF,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;;IAGI,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACjO,IAAI,EAAE4N,IAAI,EAAE;MAC3D,IAAI;QACFpM,SAAS,CAACzE,SAAS,CAAC4J,OAAO,EAAE;UAC3BuH,SAAS,EAAEN,IAAI,CAACO,gBAAgB,CAACnO,IAAI,CAAC;UACtCL,IAAI,EAAEiO;QACR,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOtH,CAAC,EAAE;QACV9E,SAAS,CAACzE,SAAS,CAAC4J,OAAO,EAAE;UAC3BuH,SAAS,EAAE,IAAI;UACfvO,IAAI,EAAEiO;QACR,CAAC,CAAC;MACJ;MAEAA,IAAI,CAACQ,eAAe,CAACpO,IAAI,CAAC,CAAC,CAAC;;MAE5B,IAAIA,IAAI,KAAK,IAAI,IAAI,CAACwJ,YAAY,CAACxJ,IAAI,CAAC,EAAE;QACxC,IAAI2K,UAAU,IAAIC,mBAAmB,EAAE;UACrC,IAAI;YACF+C,YAAY,CAACC,IAAI,CAAC;UACpB,CAAC,CAAC,OAAOtH,CAAC,EAAE,CAAC;QACf,CAAC,MAAM;UACL,IAAI;YACFsH,IAAI,CAACS,YAAY,CAACrO,IAAI,EAAE,EAAE,CAAC;UAC7B,CAAC,CAAC,OAAOsG,CAAC,EAAE,CAAC;QACf;MACF;IACF,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;;IAGI,IAAIgI,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAE;MAChD;MACA,IAAIC,GAAG;MACP,IAAIC,iBAAiB;MAErB,IAAI/D,UAAU,EAAE;QACd6D,KAAK,GAAG,mBAAmB,GAAGA,KAAK;MACrC,CAAC,MAAM;QACL;QACA,IAAIG,OAAO,GAAG7M,WAAW,CAAC0M,KAAK,EAAE,aAAa,CAAC;QAC/CE,iBAAiB,GAAGC,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC;MAC3C;MAEA,IAAIzC,iBAAiB,KAAK,uBAAuB,IAAIJ,SAAS,KAAKD,cAAc,EAAE;QACjF;QACA2C,KAAK,GAAG,gEAAgE,GAAGA,KAAK,GAAG,gBAAgB;MACrG;MAEA,IAAII,YAAY,GAAGzG,kBAAkB,GAAGA,kBAAkB,CAAC/B,UAAU,CAACoI,KAAK,CAAC,GAAGA,KAAK;MACpF;AACN;AACA;AACA;;MAEM,IAAI1C,SAAS,KAAKD,cAAc,EAAE;QAChC,IAAI;UACF4C,GAAG,GAAG,IAAIhH,SAAS,CAAC,CAAC,CAACoH,eAAe,CAACD,YAAY,EAAE1C,iBAAiB,CAAC;QACxE,CAAC,CAAC,OAAO3F,CAAC,EAAE,CAAC;MACf;MACA;;MAGA,IAAI,CAACkI,GAAG,IAAI,CAACA,GAAG,CAACK,eAAe,EAAE;QAChCL,GAAG,GAAGnG,cAAc,CAACyG,cAAc,CAACjD,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC;QAEhE,IAAI;UACF2C,GAAG,CAACK,eAAe,CAACE,SAAS,GAAGjD,cAAc,GAAG3D,SAAS,GAAGwG,YAAY;QAC3E,CAAC,CAAC,OAAOrI,CAAC,EAAE,CAAC;QAAA;MAEf;MAEA,IAAI0I,IAAI,GAAGR,GAAG,CAACQ,IAAI,IAAIR,GAAG,CAACK,eAAe;MAE1C,IAAIN,KAAK,IAAIE,iBAAiB,EAAE;QAC9BO,IAAI,CAACC,YAAY,CAACtJ,QAAQ,CAACuJ,cAAc,CAACT,iBAAiB,CAAC,EAAEO,IAAI,CAACG,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC;MAC3F;MACA;;MAGA,IAAItD,SAAS,KAAKD,cAAc,EAAE;QAChC,OAAOpD,oBAAoB,CAACrK,IAAI,CAACqQ,GAAG,EAAEhE,cAAc,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;MAC5E;MAEA,OAAOA,cAAc,GAAGgE,GAAG,CAACK,eAAe,GAAGG,IAAI;IACpD,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;;IAGI,IAAII,eAAe,GAAG,SAASA,eAAeA,CAAC3I,IAAI,EAAE;MACnD,OAAO6B,kBAAkB,CAACnK,IAAI,CAACsI,IAAI,CAACwB,aAAa,IAAIxB,IAAI,EAAEA,IAAI;MAAE;MACjEU,UAAU,CAACkI,YAAY,GAAGlI,UAAU,CAACmI,YAAY,GAAGnI,UAAU,CAACoI,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC;IACxF,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;;IAGI,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,GAAG,EAAE;MAC5C,OAAOA,GAAG,YAAYlI,eAAe,KAAK,OAAOkI,GAAG,CAACC,QAAQ,KAAK,QAAQ,IAAI,OAAOD,GAAG,CAACE,WAAW,KAAK,QAAQ,IAAI,OAAOF,GAAG,CAAC3B,WAAW,KAAK,UAAU,IAAI,EAAE2B,GAAG,CAACG,UAAU,YAAYvI,YAAY,CAAC,IAAI,OAAOoI,GAAG,CAACrB,eAAe,KAAK,UAAU,IAAI,OAAOqB,GAAG,CAACpB,YAAY,KAAK,UAAU,IAAI,OAAOoB,GAAG,CAAChC,YAAY,KAAK,QAAQ,IAAI,OAAOgC,GAAG,CAACR,YAAY,KAAK,UAAU,IAAI,OAAOQ,GAAG,CAACI,aAAa,KAAK,UAAU,CAAC;IAC9Z,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;;IAGI,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACtM,MAAM,EAAE;MACrC,OAAOxG,OAAO,CAACiK,IAAI,CAAC,KAAK,QAAQ,GAAGzD,MAAM,YAAYyD,IAAI,GAAGzD,MAAM,IAAIxG,OAAO,CAACwG,MAAM,CAAC,KAAK,QAAQ,IAAI,OAAOA,MAAM,CAACoD,QAAQ,KAAK,QAAQ,IAAI,OAAOpD,MAAM,CAACkM,QAAQ,KAAK,QAAQ;IACnL,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;IAGI,IAAIK,YAAY,GAAG,SAASA,YAAYA,CAACC,UAAU,EAAEC,WAAW,EAAEC,IAAI,EAAE;MACtE,IAAI,CAACvH,KAAK,CAACqH,UAAU,CAAC,EAAE;QACtB;MACF;MAEA7O,YAAY,CAACwH,KAAK,CAACqH,UAAU,CAAC,EAAE,UAAUG,IAAI,EAAE;QAC9CA,IAAI,CAAChS,IAAI,CAACpB,SAAS,EAAEkT,WAAW,EAAEC,IAAI,EAAE9D,MAAM,CAAC;MACjD,CAAC,CAAC;IACJ,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAGI,IAAIgE,iBAAiB,GAAG,SAASA,iBAAiBA,CAACH,WAAW,EAAE;MAC9D,IAAIjI,OAAO;MACX;;MAEA+H,YAAY,CAAC,wBAAwB,EAAEE,WAAW,EAAE,IAAI,CAAC;MACzD;;MAGA,IAAIT,YAAY,CAACS,WAAW,CAAC,EAAE;QAC7BtC,YAAY,CAACsC,WAAW,CAAC;QAEzB,OAAO,IAAI;MACb;MACA;;MAGA,IAAI5N,UAAU,CAAC,iBAAiB,EAAE4N,WAAW,CAACP,QAAQ,CAAC,EAAE;QACvD/B,YAAY,CAACsC,WAAW,CAAC;QAEzB,OAAO,IAAI;MACb;MACA;;MAGA,IAAIzC,OAAO,GAAGtK,iBAAiB,CAAC+M,WAAW,CAACP,QAAQ,CAAC;MACrD;;MAEAK,YAAY,CAAC,qBAAqB,EAAEE,WAAW,EAAE;QAC/CzC,OAAO,EAAEA,OAAO;QAChB6C,WAAW,EAAEhH;MACf,CAAC,CAAC;MACF;;MAGA,IAAI4G,WAAW,CAACJ,aAAa,CAAC,CAAC,IAAI,CAACC,OAAO,CAACG,WAAW,CAACK,iBAAiB,CAAC,KAAK,CAACR,OAAO,CAACG,WAAW,CAACjI,OAAO,CAAC,IAAI,CAAC8H,OAAO,CAACG,WAAW,CAACjI,OAAO,CAACsI,iBAAiB,CAAC,CAAC,IAAIjO,UAAU,CAAC,SAAS,EAAE4N,WAAW,CAAClB,SAAS,CAAC,IAAI1M,UAAU,CAAC,SAAS,EAAE4N,WAAW,CAACN,WAAW,CAAC,EAAE;QAClQhC,YAAY,CAACsC,WAAW,CAAC;QAEzB,OAAO,IAAI;MACb;MACA;;MAGA,IAAIzC,OAAO,KAAK,QAAQ,IAAInL,UAAU,CAAC,YAAY,EAAE4N,WAAW,CAAClB,SAAS,CAAC,EAAE;QAC3EpB,YAAY,CAACsC,WAAW,CAAC;QAEzB,OAAO,IAAI;MACb;MACA;;MAGA,IAAI,CAAC5G,YAAY,CAACmE,OAAO,CAAC,IAAIvD,WAAW,CAACuD,OAAO,CAAC,EAAE;QAClD;QACA,IAAI,CAACvD,WAAW,CAACuD,OAAO,CAAC,IAAI+C,uBAAuB,CAAC/C,OAAO,CAAC,EAAE;UAC7D,IAAI9D,uBAAuB,CAACC,YAAY,YAAYrH,MAAM,IAAID,UAAU,CAACqH,uBAAuB,CAACC,YAAY,EAAE6D,OAAO,CAAC,EAAE,OAAO,KAAK;UACrI,IAAI9D,uBAAuB,CAACC,YAAY,YAAY9K,QAAQ,IAAI6K,uBAAuB,CAACC,YAAY,CAAC6D,OAAO,CAAC,EAAE,OAAO,KAAK;QAC7H;QACA;;QAGA,IAAIvC,YAAY,IAAI,CAACG,eAAe,CAACoC,OAAO,CAAC,EAAE;UAC7C,IAAIK,UAAU,GAAGhG,aAAa,CAACoI,WAAW,CAAC,IAAIA,WAAW,CAACpC,UAAU;UACrE,IAAIsB,UAAU,GAAGvH,aAAa,CAACqI,WAAW,CAAC,IAAIA,WAAW,CAACd,UAAU;UAErE,IAAIA,UAAU,IAAItB,UAAU,EAAE;YAC5B,IAAI2C,UAAU,GAAGrB,UAAU,CAAChP,MAAM;YAElC,KAAK,IAAIC,CAAC,GAAGoQ,UAAU,GAAG,CAAC,EAAEpQ,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;cACxCyN,UAAU,CAACoB,YAAY,CAACvH,SAAS,CAACyH,UAAU,CAAC/O,CAAC,CAAC,EAAE,IAAI,CAAC,EAAEuH,cAAc,CAACsI,WAAW,CAAC,CAAC;YACtF;UACF;QACF;QAEAtC,YAAY,CAACsC,WAAW,CAAC;QAEzB,OAAO,IAAI;MACb;MACA;;MAGA,IAAIA,WAAW,YAAY/I,OAAO,IAAI,CAACoG,oBAAoB,CAAC2C,WAAW,CAAC,EAAE;QACxEtC,YAAY,CAACsC,WAAW,CAAC;QAEzB,OAAO,IAAI;MACb;MACA;;MAGA,IAAI,CAACzC,OAAO,KAAK,UAAU,IAAIA,OAAO,KAAK,SAAS,IAAIA,OAAO,KAAK,UAAU,KAAKnL,UAAU,CAAC,6BAA6B,EAAE4N,WAAW,CAAClB,SAAS,CAAC,EAAE;QACnJpB,YAAY,CAACsC,WAAW,CAAC;QAEzB,OAAO,IAAI;MACb;MACA;;MAGA,IAAI1F,kBAAkB,IAAI0F,WAAW,CAACrJ,QAAQ,KAAK,CAAC,EAAE;QACpD;QACAoB,OAAO,GAAGiI,WAAW,CAACN,WAAW;QACjC3H,OAAO,GAAGjG,aAAa,CAACiG,OAAO,EAAEa,eAAe,EAAE,GAAG,CAAC;QACtDb,OAAO,GAAGjG,aAAa,CAACiG,OAAO,EAAEc,UAAU,EAAE,GAAG,CAAC;QACjDd,OAAO,GAAGjG,aAAa,CAACiG,OAAO,EAAEe,aAAa,EAAE,GAAG,CAAC;QAEpD,IAAIkH,WAAW,CAACN,WAAW,KAAK3H,OAAO,EAAE;UACvCxG,SAAS,CAACzE,SAAS,CAAC4J,OAAO,EAAE;YAC3BtD,OAAO,EAAE4M,WAAW,CAACvI,SAAS,CAAC;UACjC,CAAC,CAAC;UACFuI,WAAW,CAACN,WAAW,GAAG3H,OAAO;QACnC;MACF;MACA;;MAGA+H,YAAY,CAAC,uBAAuB,EAAEE,WAAW,EAAE,IAAI,CAAC;MAExD,OAAO,KAAK;IACd,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACI;;IAGA,IAAIQ,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAEC,MAAM,EAAE5M,KAAK,EAAE;MACvE;MACA,IAAI+G,YAAY,KAAK6F,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,MAAM,CAAC,KAAK5M,KAAK,IAAI4B,QAAQ,IAAI5B,KAAK,IAAIsI,WAAW,CAAC,EAAE;QACzG,OAAO,KAAK;MACd;MACA;AACN;AACA;AACA;;MAGM,IAAIjC,eAAe,IAAI,CAACF,WAAW,CAACyG,MAAM,CAAC,IAAItO,UAAU,CAAC2G,WAAW,EAAE2H,MAAM,CAAC,EAAE,CAAC,KAAM,IAAIxG,eAAe,IAAI9H,UAAU,CAAC4G,WAAW,EAAE0H,MAAM,CAAC,EAAE,CAAC,KAAM,IAAI,CAACnH,YAAY,CAACmH,MAAM,CAAC,IAAIzG,WAAW,CAACyG,MAAM,CAAC,EAAE;QACtM;QAAK;QACL;QACA;QACAJ,uBAAuB,CAACG,KAAK,CAAC,KAAKhH,uBAAuB,CAACC,YAAY,YAAYrH,MAAM,IAAID,UAAU,CAACqH,uBAAuB,CAACC,YAAY,EAAE+G,KAAK,CAAC,IAAIhH,uBAAuB,CAACC,YAAY,YAAY9K,QAAQ,IAAI6K,uBAAuB,CAACC,YAAY,CAAC+G,KAAK,CAAC,CAAC,KAAKhH,uBAAuB,CAACK,kBAAkB,YAAYzH,MAAM,IAAID,UAAU,CAACqH,uBAAuB,CAACK,kBAAkB,EAAE4G,MAAM,CAAC,IAAIjH,uBAAuB,CAACK,kBAAkB,YAAYlL,QAAQ,IAAI6K,uBAAuB,CAACK,kBAAkB,CAAC4G,MAAM,CAAC,CAAC;QAAI;QAChgB;QACAA,MAAM,KAAK,IAAI,IAAIjH,uBAAuB,CAACM,8BAA8B,KAAKN,uBAAuB,CAACC,YAAY,YAAYrH,MAAM,IAAID,UAAU,CAACqH,uBAAuB,CAACC,YAAY,EAAE5F,KAAK,CAAC,IAAI2F,uBAAuB,CAACC,YAAY,YAAY9K,QAAQ,IAAI6K,uBAAuB,CAACC,YAAY,CAAC5F,KAAK,CAAC,CAAC,EAAE,CAAC,KAAM;UAClT,OAAO,KAAK;QACd;QACA;MAEF,CAAC,MAAM,IAAIyH,mBAAmB,CAACmF,MAAM,CAAC,EAAE,CAAC,KAAM,IAAItO,UAAU,CAAC+G,gBAAgB,EAAErH,aAAa,CAACgC,KAAK,EAAEoF,iBAAiB,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAM,IAAI,CAACwH,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,MAAM,KAAKD,KAAK,KAAK,QAAQ,IAAIzO,aAAa,CAAC8B,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,IAAIuH,aAAa,CAACoF,KAAK,CAAC,EAAE,CAAC,KAAM,IAAIrG,uBAAuB,IAAI,CAAChI,UAAU,CAAC6G,mBAAmB,EAAEnH,aAAa,CAACgC,KAAK,EAAEoF,iBAAiB,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAM,IAAIpF,KAAK,EAAE;QACva,OAAO,KAAK;MACd,CAAC,MAAM;MAEP,OAAO,IAAI;IACb,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;;IAGI,IAAIwM,uBAAuB,GAAG,SAASA,uBAAuBA,CAAC/C,OAAO,EAAE;MACtE,OAAOA,OAAO,CAACtL,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;IACjC,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAGI,IAAI0O,mBAAmB,GAAG,SAASA,mBAAmBA,CAACX,WAAW,EAAE;MAClE,IAAIY,IAAI;MACR,IAAI9M,KAAK;MACT,IAAI4M,MAAM;MACV,IAAIvN,CAAC;MACL;;MAEA2M,YAAY,CAAC,0BAA0B,EAAEE,WAAW,EAAE,IAAI,CAAC;MAE3D,IAAIL,UAAU,GAAGK,WAAW,CAACL,UAAU;MACvC;;MAEA,IAAI,CAACA,UAAU,EAAE;QACf;MACF;MAEA,IAAIkB,SAAS,GAAG;QACdC,QAAQ,EAAE,EAAE;QACZC,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE,IAAI;QACdC,iBAAiB,EAAE1H;MACrB,CAAC;MACDpG,CAAC,GAAGwM,UAAU,CAACzP,MAAM;MACrB;;MAEA,OAAOiD,CAAC,EAAE,EAAE;QACVyN,IAAI,GAAGjB,UAAU,CAACxM,CAAC,CAAC;QACpB,IAAI+N,KAAK,GAAGN,IAAI;UACZ7Q,IAAI,GAAGmR,KAAK,CAACnR,IAAI;UACjByN,YAAY,GAAG0D,KAAK,CAAC1D,YAAY;QACrC1J,KAAK,GAAG/D,IAAI,KAAK,OAAO,GAAG6Q,IAAI,CAAC9M,KAAK,GAAG5B,UAAU,CAAC0O,IAAI,CAAC9M,KAAK,CAAC;QAC9D4M,MAAM,GAAGzN,iBAAiB,CAAClD,IAAI,CAAC;QAChC;;QAEA8Q,SAAS,CAACC,QAAQ,GAAGJ,MAAM;QAC3BG,SAAS,CAACE,SAAS,GAAGjN,KAAK;QAC3B+M,SAAS,CAACG,QAAQ,GAAG,IAAI;QACzBH,SAAS,CAACM,aAAa,GAAG5K,SAAS,CAAC,CAAC;;QAErCuJ,YAAY,CAAC,uBAAuB,EAAEE,WAAW,EAAEa,SAAS,CAAC;QAE7D/M,KAAK,GAAG+M,SAAS,CAACE,SAAS;QAC3B;;QAEA,IAAIF,SAAS,CAACM,aAAa,EAAE;UAC3B;QACF;QACA;;QAGAnD,gBAAgB,CAACjO,IAAI,EAAEiQ,WAAW,CAAC;QACnC;;QAGA,IAAI,CAACa,SAAS,CAACG,QAAQ,EAAE;UACvB;QACF;QACA;;QAGA,IAAI,CAAC3G,wBAAwB,IAAIjI,UAAU,CAAC,MAAM,EAAE0B,KAAK,CAAC,EAAE;UAC1DkK,gBAAgB,CAACjO,IAAI,EAAEiQ,WAAW,CAAC;UAEnC;QACF;QACA;;QAGA,IAAI1F,kBAAkB,EAAE;UACtBxG,KAAK,GAAGhC,aAAa,CAACgC,KAAK,EAAE8E,eAAe,EAAE,GAAG,CAAC;UAClD9E,KAAK,GAAGhC,aAAa,CAACgC,KAAK,EAAE+E,UAAU,EAAE,GAAG,CAAC;UAC7C/E,KAAK,GAAGhC,aAAa,CAACgC,KAAK,EAAEgF,aAAa,EAAE,GAAG,CAAC;QAClD;QACA;;QAGA,IAAI2H,KAAK,GAAGxN,iBAAiB,CAAC+M,WAAW,CAACP,QAAQ,CAAC;QAEnD,IAAI,CAACe,iBAAiB,CAACC,KAAK,EAAEC,MAAM,EAAE5M,KAAK,CAAC,EAAE;UAC5C;QACF;QACA;AACR;AACA;;QAGQ,IAAIgH,oBAAoB,KAAK4F,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,MAAM,CAAC,EAAE;UAClE;UACA1C,gBAAgB,CAACjO,IAAI,EAAEiQ,WAAW,CAAC,CAAC,CAAC;;UAGrClM,KAAK,GAAGiH,2BAA2B,GAAGjH,KAAK;QAC7C;QACA;;QAGA,IAAImE,kBAAkB,IAAIlL,OAAO,CAAC0I,YAAY,CAAC,KAAK,QAAQ,IAAI,OAAOA,YAAY,CAAC2L,gBAAgB,KAAK,UAAU,EAAE;UACnH,IAAI5D,YAAY,EAAE,CAAC,KAAM;YACvB,QAAQ/H,YAAY,CAAC2L,gBAAgB,CAACX,KAAK,EAAEC,MAAM,CAAC;cAClD,KAAK,aAAa;gBAChB;kBACE5M,KAAK,GAAGmE,kBAAkB,CAAC/B,UAAU,CAACpC,KAAK,CAAC;kBAC5C;gBACF;cAEF,KAAK,kBAAkB;gBACrB;kBACEA,KAAK,GAAGmE,kBAAkB,CAAC9B,eAAe,CAACrC,KAAK,CAAC;kBACjD;gBACF;YACJ;UACF;QACF;QACA;;QAGA,IAAI;UACF,IAAI0J,YAAY,EAAE;YAChBwC,WAAW,CAACqB,cAAc,CAAC7D,YAAY,EAAEzN,IAAI,EAAE+D,KAAK,CAAC;UACvD,CAAC,MAAM;YACL;YACAkM,WAAW,CAAC5B,YAAY,CAACrO,IAAI,EAAE+D,KAAK,CAAC;UACvC;UAEAzC,QAAQ,CAACvE,SAAS,CAAC4J,OAAO,CAAC;QAC7B,CAAC,CAAC,OAAOL,CAAC,EAAE,CAAC;MACf;MACA;;MAGAyJ,YAAY,CAAC,yBAAyB,EAAEE,WAAW,EAAE,IAAI,CAAC;IAC5D,CAAC;IACD;AACJ;AACA;AACA;AACA;;IAGI,IAAIsB,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,QAAQ,EAAE;MAC7D,IAAIC,UAAU;MAEd,IAAIC,cAAc,GAAGtC,eAAe,CAACoC,QAAQ,CAAC;MAC9C;;MAGAzB,YAAY,CAAC,yBAAyB,EAAEyB,QAAQ,EAAE,IAAI,CAAC;MAEvD,OAAOC,UAAU,GAAGC,cAAc,CAACC,QAAQ,CAAC,CAAC,EAAE;QAC7C;QACA5B,YAAY,CAAC,wBAAwB,EAAE0B,UAAU,EAAE,IAAI,CAAC;QACxD;;QAGA,IAAIrB,iBAAiB,CAACqB,UAAU,CAAC,EAAE;UACjC;QACF;QACA;;QAGA,IAAIA,UAAU,CAACzJ,OAAO,YAAYjB,gBAAgB,EAAE;UAClDwK,kBAAkB,CAACE,UAAU,CAACzJ,OAAO,CAAC;QACxC;QACA;;QAGA4I,mBAAmB,CAACa,UAAU,CAAC;MACjC;MACA;;MAGA1B,YAAY,CAAC,wBAAwB,EAAEyB,QAAQ,EAAE,IAAI,CAAC;IACxD,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;AACA;IACI;;IAGAzU,SAAS,CAAC6U,QAAQ,GAAG,UAAUrD,KAAK,EAAE;MACpC,IAAI9B,GAAG,GAAGzN,SAAS,CAACmB,MAAM,GAAG,CAAC,IAAInB,SAAS,CAAC,CAAC,CAAC,KAAKwH,SAAS,GAAGxH,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MAChF,IAAIgQ,IAAI;MACR,IAAI6C,YAAY;MAChB,IAAI5B,WAAW;MACf,IAAI6B,OAAO;MACX,IAAIC,UAAU;MACd;AACN;AACA;;MAEMjG,cAAc,GAAG,CAACyC,KAAK;MAEvB,IAAIzC,cAAc,EAAE;QAClByC,KAAK,GAAG,OAAO;MACjB;MACA;;MAGA,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACuB,OAAO,CAACvB,KAAK,CAAC,EAAE;QAChD,IAAI,OAAOA,KAAK,CAACzO,QAAQ,KAAK,UAAU,EAAE;UACxCyO,KAAK,GAAGA,KAAK,CAACzO,QAAQ,CAAC,CAAC;UAExB,IAAI,OAAOyO,KAAK,KAAK,QAAQ,EAAE;YAC7B,MAAMhM,eAAe,CAAC,iCAAiC,CAAC;UAC1D;QACF,CAAC,MAAM;UACL,MAAMA,eAAe,CAAC,4BAA4B,CAAC;QACrD;MACF;MACA;;MAGA,IAAI,CAACxF,SAAS,CAAC8J,WAAW,EAAE;QAC1B,IAAI7J,OAAO,CAACwI,MAAM,CAACwM,YAAY,CAAC,KAAK,QAAQ,IAAI,OAAOxM,MAAM,CAACwM,YAAY,KAAK,UAAU,EAAE;UAC1F,IAAI,OAAOzD,KAAK,KAAK,QAAQ,EAAE;YAC7B,OAAO/I,MAAM,CAACwM,YAAY,CAACzD,KAAK,CAAC;UACnC;UAEA,IAAIuB,OAAO,CAACvB,KAAK,CAAC,EAAE;YAClB,OAAO/I,MAAM,CAACwM,YAAY,CAACzD,KAAK,CAACR,SAAS,CAAC;UAC7C;QACF;QAEA,OAAOQ,KAAK;MACd;MACA;;MAGA,IAAI,CAAC9D,UAAU,EAAE;QACf+B,YAAY,CAACC,GAAG,CAAC;MACnB;MACA;;MAGA1P,SAAS,CAAC4J,OAAO,GAAG,EAAE;MACtB;;MAEA,IAAI,OAAO4H,KAAK,KAAK,QAAQ,EAAE;QAC7BrD,QAAQ,GAAG,KAAK;MAClB;MAEA,IAAIA,QAAQ,EAAE;QACZ;QACA,IAAIqD,KAAK,CAACmB,QAAQ,EAAE;UAClB,IAAIlC,OAAO,GAAGtK,iBAAiB,CAACqL,KAAK,CAACmB,QAAQ,CAAC;UAE/C,IAAI,CAACrG,YAAY,CAACmE,OAAO,CAAC,IAAIvD,WAAW,CAACuD,OAAO,CAAC,EAAE;YAClD,MAAMjL,eAAe,CAAC,yDAAyD,CAAC;UAClF;QACF;MACF,CAAC,MAAM,IAAIgM,KAAK,YAAYtH,IAAI,EAAE;QAChC;AACR;QACQ+H,IAAI,GAAGV,aAAa,CAAC,SAAS,CAAC;QAC/BuD,YAAY,GAAG7C,IAAI,CAAC/G,aAAa,CAACQ,UAAU,CAAC8F,KAAK,EAAE,IAAI,CAAC;QAEzD,IAAIsD,YAAY,CAACjL,QAAQ,KAAK,CAAC,IAAIiL,YAAY,CAACnC,QAAQ,KAAK,MAAM,EAAE;UACnE;UACAV,IAAI,GAAG6C,YAAY;QACrB,CAAC,MAAM,IAAIA,YAAY,CAACnC,QAAQ,KAAK,MAAM,EAAE;UAC3CV,IAAI,GAAG6C,YAAY;QACrB,CAAC,MAAM;UACL;UACA7C,IAAI,CAACiD,WAAW,CAACJ,YAAY,CAAC;QAChC;MACF,CAAC,MAAM;QACL;QACA,IAAI,CAAClH,UAAU,IAAI,CAACJ,kBAAkB,IAAI,CAACC,cAAc;QAAI;QAC7D+D,KAAK,CAACrM,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;UACzB,OAAOgG,kBAAkB,IAAI2C,mBAAmB,GAAG3C,kBAAkB,CAAC/B,UAAU,CAACoI,KAAK,CAAC,GAAGA,KAAK;QACjG;QACA;;QAGAS,IAAI,GAAGV,aAAa,CAACC,KAAK,CAAC;QAC3B;;QAEA,IAAI,CAACS,IAAI,EAAE;UACT,OAAOrE,UAAU,GAAG,IAAI,GAAGE,mBAAmB,GAAG1C,SAAS,GAAG,EAAE;QACjE;MACF;MACA;;MAGA,IAAI6G,IAAI,IAAItE,UAAU,EAAE;QACtBiD,YAAY,CAACqB,IAAI,CAACkD,UAAU,CAAC;MAC/B;MACA;;MAGA,IAAIC,YAAY,GAAG/C,eAAe,CAAClE,QAAQ,GAAGqD,KAAK,GAAGS,IAAI,CAAC;MAC3D;;MAGA,OAAOiB,WAAW,GAAGkC,YAAY,CAACR,QAAQ,CAAC,CAAC,EAAE;QAC5C;QACA,IAAI1B,WAAW,CAACrJ,QAAQ,KAAK,CAAC,IAAIqJ,WAAW,KAAK6B,OAAO,EAAE;UACzD;QACF;QACA;;QAGA,IAAI1B,iBAAiB,CAACH,WAAW,CAAC,EAAE;UAClC;QACF;QACA;;QAGA,IAAIA,WAAW,CAACjI,OAAO,YAAYjB,gBAAgB,EAAE;UACnDwK,kBAAkB,CAACtB,WAAW,CAACjI,OAAO,CAAC;QACzC;QACA;;QAGA4I,mBAAmB,CAACX,WAAW,CAAC;QAEhC6B,OAAO,GAAG7B,WAAW;MACvB;MAEA6B,OAAO,GAAG,IAAI;MACd;;MAEA,IAAI5G,QAAQ,EAAE;QACZ,OAAOqD,KAAK;MACd;MACA;;MAGA,IAAI5D,UAAU,EAAE;QACd,IAAIC,mBAAmB,EAAE;UACvBmH,UAAU,GAAGxJ,sBAAsB,CAACpK,IAAI,CAAC6Q,IAAI,CAAC/G,aAAa,CAAC;UAE5D,OAAO+G,IAAI,CAACkD,UAAU,EAAE;YACtB;YACAH,UAAU,CAACE,WAAW,CAACjD,IAAI,CAACkD,UAAU,CAAC;UACzC;QACF,CAAC,MAAM;UACLH,UAAU,GAAG/C,IAAI;QACnB;QAEA,IAAIxF,YAAY,CAAC4I,UAAU,IAAI5I,YAAY,CAAC6I,aAAa,EAAE;UACzD;AACV;AACA;AACA;AACA;AACA;AACA;UACUN,UAAU,GAAGtJ,UAAU,CAACtK,IAAI,CAAC2I,gBAAgB,EAAEiL,UAAU,EAAE,IAAI,CAAC;QAClE;QAEA,OAAOA,UAAU;MACnB;MAEA,IAAIO,cAAc,GAAG9H,cAAc,GAAGwE,IAAI,CAACjB,SAAS,GAAGiB,IAAI,CAACD,SAAS;MACrE;;MAEA,IAAIvE,cAAc,IAAInB,YAAY,CAAC,UAAU,CAAC,IAAI2F,IAAI,CAAC/G,aAAa,IAAI+G,IAAI,CAAC/G,aAAa,CAACsK,OAAO,IAAIvD,IAAI,CAAC/G,aAAa,CAACsK,OAAO,CAACvS,IAAI,IAAIqC,UAAU,CAACiD,YAAY,EAAE0J,IAAI,CAAC/G,aAAa,CAACsK,OAAO,CAACvS,IAAI,CAAC,EAAE;QAClMsS,cAAc,GAAG,YAAY,GAAGtD,IAAI,CAAC/G,aAAa,CAACsK,OAAO,CAACvS,IAAI,GAAG,KAAK,GAAGsS,cAAc;MAC1F;MACA;;MAGA,IAAI/H,kBAAkB,EAAE;QACtB+H,cAAc,GAAGvQ,aAAa,CAACuQ,cAAc,EAAEzJ,eAAe,EAAE,GAAG,CAAC;QACpEyJ,cAAc,GAAGvQ,aAAa,CAACuQ,cAAc,EAAExJ,UAAU,EAAE,GAAG,CAAC;QAC/DwJ,cAAc,GAAGvQ,aAAa,CAACuQ,cAAc,EAAEvJ,aAAa,EAAE,GAAG,CAAC;MACpE;MAEA,OAAOb,kBAAkB,IAAI2C,mBAAmB,GAAG3C,kBAAkB,CAAC/B,UAAU,CAACmM,cAAc,CAAC,GAAGA,cAAc;IACnH,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;;IAGIvV,SAAS,CAACyV,SAAS,GAAG,UAAU/F,GAAG,EAAE;MACnCD,YAAY,CAACC,GAAG,CAAC;MAEjBhC,UAAU,GAAG,IAAI;IACnB,CAAC;IACD;AACJ;AACA;AACA;AACA;;IAGI1N,SAAS,CAAC0V,WAAW,GAAG,YAAY;MAClCrG,MAAM,GAAG,IAAI;MACb3B,UAAU,GAAG,KAAK;IACpB,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;IAGI1N,SAAS,CAAC2V,gBAAgB,GAAG,UAAUC,GAAG,EAAE9B,IAAI,EAAE9M,KAAK,EAAE;MACvD;MACA,IAAI,CAACqI,MAAM,EAAE;QACXI,YAAY,CAAC,CAAC,CAAC,CAAC;MAClB;MAEA,IAAIkE,KAAK,GAAGxN,iBAAiB,CAACyP,GAAG,CAAC;MAClC,IAAIhC,MAAM,GAAGzN,iBAAiB,CAAC2N,IAAI,CAAC;MACpC,OAAOJ,iBAAiB,CAACC,KAAK,EAAEC,MAAM,EAAE5M,KAAK,CAAC;IAChD,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;AACA;;IAGIhH,SAAS,CAAC6V,OAAO,GAAG,UAAU5C,UAAU,EAAE6C,YAAY,EAAE;MACtD,IAAI,OAAOA,YAAY,KAAK,UAAU,EAAE;QACtC;MACF;MAEAlK,KAAK,CAACqH,UAAU,CAAC,GAAGrH,KAAK,CAACqH,UAAU,CAAC,IAAI,EAAE;MAC3CxO,SAAS,CAACmH,KAAK,CAACqH,UAAU,CAAC,EAAE6C,YAAY,CAAC;IAC5C,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;IAGI9V,SAAS,CAAC+V,UAAU,GAAG,UAAU9C,UAAU,EAAE;MAC3C,IAAIrH,KAAK,CAACqH,UAAU,CAAC,EAAE;QACrB,OAAO1O,QAAQ,CAACqH,KAAK,CAACqH,UAAU,CAAC,CAAC;MACpC;IACF,CAAC;IACD;AACJ;AACA;AACA;AACA;AACA;;IAGIjT,SAAS,CAACgW,WAAW,GAAG,UAAU/C,UAAU,EAAE;MAC5C,IAAIrH,KAAK,CAACqH,UAAU,CAAC,EAAE;QACrBrH,KAAK,CAACqH,UAAU,CAAC,GAAG,EAAE;MACxB;IACF,CAAC;IACD;AACJ;AACA;AACA;AACA;;IAGIjT,SAAS,CAACiW,cAAc,GAAG,YAAY;MACrCrK,KAAK,GAAG,CAAC,CAAC;IACZ,CAAC;IAED,OAAO5L,SAAS;EAClB;EAEA,IAAIkW,MAAM,GAAG1M,eAAe,CAAC,CAAC;EAE9B,OAAO0M,MAAM;AAEf,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}