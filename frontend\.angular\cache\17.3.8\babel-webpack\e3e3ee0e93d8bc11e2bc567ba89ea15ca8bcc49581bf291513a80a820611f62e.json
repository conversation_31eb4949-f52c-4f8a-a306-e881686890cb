{"ast": null, "code": "import * as i2 from '@angular/common';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { forwardRef, EventEmitter, booleanAttribute, numberAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ContentChildren, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport * as i1 from 'primeng/api';\nimport { PrimeTemplate, SharedModule } from 'primeng/api';\nimport { CheckIcon } from 'primeng/icons/check';\nimport { TimesIcon } from 'primeng/icons/times';\nimport * as i3 from 'primeng/autofocus';\nimport { AutoFocusModule } from 'primeng/autofocus';\nconst _c0 = (a0, a1, a2) => ({\n  \"p-checkbox p-component\": true,\n  \"p-checkbox-disabled\": a0,\n  \"p-checkbox-focused\": a1,\n  \"p-variant-filled\": a2\n});\nconst _c1 = (a0, a1, a2) => ({\n  \"p-highlight\": a0,\n  \"p-disabled\": a1,\n  \"p-focus\": a2\n});\nconst _c2 = (a0, a1, a2) => ({\n  \"p-checkbox-label-active\": a0,\n  \"p-disabled\": a1,\n  \"p-checkbox-label-focus\": a2\n});\nfunction TriStateCheckbox_ng_container_5_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.checkboxTrueIcon);\n    i0.ɵɵattribute(\"data-pc-section\", \"checkIcon\");\n  }\n}\nfunction TriStateCheckbox_ng_container_5_ng_container_2_CheckIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"CheckIcon\", 11);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-checkbox-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"checkIcon\");\n  }\n}\nfunction TriStateCheckbox_ng_container_5_ng_container_2_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction TriStateCheckbox_ng_container_5_ng_container_2_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TriStateCheckbox_ng_container_5_ng_container_2_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction TriStateCheckbox_ng_container_5_ng_container_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵtemplate(1, TriStateCheckbox_ng_container_5_ng_container_2_span_2_1_Template, 1, 0, null, 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵattribute(\"data-pc-section\", \"checkIcon\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.checkIconTemplate);\n  }\n}\nfunction TriStateCheckbox_ng_container_5_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TriStateCheckbox_ng_container_5_ng_container_2_CheckIcon_1_Template, 1, 2, \"CheckIcon\", 9)(2, TriStateCheckbox_ng_container_5_ng_container_2_span_2_Template, 2, 2, \"span\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.checkIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.checkIconTemplate);\n  }\n}\nfunction TriStateCheckbox_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TriStateCheckbox_ng_container_5_span_1_Template, 1, 2, \"span\", 7)(2, TriStateCheckbox_ng_container_5_ng_container_2_Template, 3, 2, \"ng-container\", 5);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.checkboxTrueIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.checkboxTrueIcon);\n  }\n}\nfunction TriStateCheckbox_ng_container_6_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.checkboxFalseIcon);\n    i0.ɵɵattribute(\"data-pc-section\", \"uncheckIcon\");\n  }\n}\nfunction TriStateCheckbox_ng_container_6_ng_container_2_TimesIcon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"TimesIcon\", 11);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"styleClass\", \"p-checkbox-icon\");\n    i0.ɵɵattribute(\"data-pc-section\", \"uncheckIcon\");\n  }\n}\nfunction TriStateCheckbox_ng_container_6_ng_container_2_span_2_1_ng_template_0_Template(rf, ctx) {}\nfunction TriStateCheckbox_ng_container_6_ng_container_2_span_2_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, TriStateCheckbox_ng_container_6_ng_container_2_span_2_1_ng_template_0_Template, 0, 0, \"ng-template\");\n  }\n}\nfunction TriStateCheckbox_ng_container_6_ng_container_2_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 12);\n    i0.ɵɵtemplate(1, TriStateCheckbox_ng_container_6_ng_container_2_span_2_1_Template, 1, 0, null, 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵattribute(\"data-pc-section\", \"uncheckIcon\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r2.uncheckIconTemplate);\n  }\n}\nfunction TriStateCheckbox_ng_container_6_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TriStateCheckbox_ng_container_6_ng_container_2_TimesIcon_1_Template, 1, 2, \"TimesIcon\", 9)(2, TriStateCheckbox_ng_container_6_ng_container_2_span_2_Template, 2, 2, \"span\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.uncheckIconTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.uncheckIconTemplate);\n  }\n}\nfunction TriStateCheckbox_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, TriStateCheckbox_ng_container_6_span_1_Template, 1, 2, \"span\", 7)(2, TriStateCheckbox_ng_container_6_ng_container_2_Template, 3, 2, \"ng-container\", 5);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.checkboxFalseIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.checkboxFalseIcon);\n  }\n}\nfunction TriStateCheckbox_label_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 14);\n    i0.ɵɵlistener(\"click\", function TriStateCheckbox_label_7_Template_label_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const input_r2 = i0.ɵɵreference(3);\n      return i0.ɵɵresetView(ctx_r2.onClick($event, input_r2));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(3, _c2, ctx_r2.value != null, ctx_r2.disabled, ctx_r2.focused));\n    i0.ɵɵattribute(\"for\", ctx_r2.inputId);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r2.label);\n  }\n}\nconst TRISTATECHECKBOX_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => TriStateCheckbox),\n  multi: true\n};\n/**\n * TriStateCheckbox is used to select either 'true', 'false' or 'null' as the value.\n * @group Components\n */\nlet TriStateCheckbox = /*#__PURE__*/(() => {\n  class TriStateCheckbox {\n    cd;\n    config;\n    constructor(cd, config) {\n      this.cd = cd;\n      this.config = config;\n    }\n    /**\n     * When present, it specifies that the element should be disabled.\n     * @group Props\n     */\n    disabled;\n    /**\n     * Name of the component.\n     * @group Props\n     */\n    name;\n    /**\n     * Defines a string that labels the input for accessibility.\n     * @group Props\n     */\n    ariaLabel;\n    /**\n     * Establishes relationships between the component and label(s) where its value should be one or more element IDs.\n     * @group Props\n     */\n    ariaLabelledBy;\n    /**\n     * Specifies the input variant of the component.\n     * @group Props\n     */\n    variant = 'outlined';\n    /**\n     * Index of the element in tabbing order.\n     * @group Props\n     */\n    tabindex;\n    /**\n     * Identifier of the focus input to match a label defined for the component.\n     * @group Props\n     */\n    inputId;\n    /**\n     * Inline style of the component.\n     * @group Props\n     */\n    style;\n    /**\n     * Style class of the component.\n     * @group Props\n     */\n    styleClass;\n    /**\n     * Label of the checkbox.\n     * @group Props\n     */\n    label;\n    /**\n     * When present, it specifies that the component cannot be edited.\n     * @group Props\n     */\n    readonly;\n    /**\n     * Specifies the icon for checkbox true value.\n     * @group Props\n     */\n    checkboxTrueIcon;\n    /**\n     * Specifies the icon for checkbox false value.\n     * @group Props\n     */\n    checkboxFalseIcon;\n    /**\n     * When present, it specifies that the component should automatically get focus on load.\n     * @group Props\n     */\n    autofocus;\n    /**\n     * Callback to invoke on value change.\n     * @param {TriStateCheckboxChangeEvent} event - Custom change event.\n     * @group Emits\n     */\n    onChange = new EventEmitter();\n    templates;\n    checkIconTemplate;\n    uncheckIconTemplate;\n    focused;\n    value;\n    onModelChange = () => {};\n    onModelTouched = () => {};\n    onClick(event, input) {\n      if (!this.disabled && !this.readonly) {\n        this.toggle(event);\n        this.focused = true;\n        input.focus();\n      }\n    }\n    onKeyDown(event) {\n      if (event.key === 'Enter') {\n        this.toggle(event);\n        event.preventDefault();\n      }\n    }\n    toggle(event) {\n      if (this.value == null || this.value == undefined) this.value = true;else if (this.value == true) this.value = false;else if (this.value == false) this.value = null;\n      this.onModelChange(this.value);\n      this.onChange.emit({\n        originalEvent: event,\n        value: this.value\n      });\n    }\n    ngAfterContentInit() {\n      this.templates.forEach(item => {\n        switch (item.getType()) {\n          case 'checkicon':\n            this.checkIconTemplate = item.template;\n            break;\n          case 'uncheckicon':\n            this.uncheckIconTemplate = item.template;\n            break;\n        }\n      });\n    }\n    onFocus() {\n      this.focused = true;\n    }\n    onBlur() {\n      this.focused = false;\n      this.onModelTouched();\n    }\n    registerOnChange(fn) {\n      this.onModelChange = fn;\n    }\n    registerOnTouched(fn) {\n      this.onModelTouched = fn;\n    }\n    writeValue(value) {\n      this.value = value;\n      this.cd.markForCheck();\n    }\n    setDisabledState(disabled) {\n      this.disabled = disabled;\n      this.cd.markForCheck();\n    }\n    static ɵfac = function TriStateCheckbox_Factory(t) {\n      return new (t || TriStateCheckbox)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.PrimeNGConfig));\n    };\n    static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: TriStateCheckbox,\n      selectors: [[\"p-triStateCheckbox\"]],\n      contentQueries: function TriStateCheckbox_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, PrimeTemplate, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templates = _t);\n        }\n      },\n      hostAttrs: [1, \"p-element\"],\n      inputs: {\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n        name: \"name\",\n        ariaLabel: \"ariaLabel\",\n        ariaLabelledBy: \"ariaLabelledBy\",\n        variant: \"variant\",\n        tabindex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabindex\", \"tabindex\", numberAttribute],\n        inputId: \"inputId\",\n        style: \"style\",\n        styleClass: \"styleClass\",\n        label: \"label\",\n        readonly: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"readonly\", \"readonly\", booleanAttribute],\n        checkboxTrueIcon: \"checkboxTrueIcon\",\n        checkboxFalseIcon: \"checkboxFalseIcon\",\n        autofocus: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"autofocus\", \"autofocus\", booleanAttribute]\n      },\n      outputs: {\n        onChange: \"onChange\"\n      },\n      features: [i0.ɵɵProvidersFeature([TRISTATECHECKBOX_VALUE_ACCESSOR]), i0.ɵɵInputTransformsFeature],\n      decls: 8,\n      vars: 28,\n      consts: [[\"input\", \"\"], [3, \"click\", \"ngStyle\", \"ngClass\"], [1, \"p-hidden-accessible\"], [\"type\", \"checkbox\", \"inputmode\", \"none\", \"pAutoFocus\", \"\", 3, \"keydown\", \"focus\", \"blur\", \"name\", \"readonly\", \"disabled\", \"autofocus\"], [\"role\", \"checkbox\", 1, \"p-checkbox-box\", 3, \"ngClass\"], [4, \"ngIf\"], [\"class\", \"p-checkbox-label\", 3, \"ngClass\", \"click\", 4, \"ngIf\"], [\"class\", \"p-checkbox-icon\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"p-checkbox-icon\", 3, \"ngClass\"], [3, \"styleClass\", 4, \"ngIf\"], [\"class\", \"p-checkbox-icon\", 4, \"ngIf\"], [3, \"styleClass\"], [1, \"p-checkbox-icon\"], [4, \"ngTemplateOutlet\"], [1, \"p-checkbox-label\", 3, \"click\", \"ngClass\"]],\n      template: function TriStateCheckbox_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 1);\n          i0.ɵɵlistener(\"click\", function TriStateCheckbox_Template_div_click_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            const input_r2 = i0.ɵɵreference(3);\n            return i0.ɵɵresetView(ctx.onClick($event, input_r2));\n          });\n          i0.ɵɵelementStart(1, \"div\", 2)(2, \"input\", 3, 0);\n          i0.ɵɵlistener(\"keydown\", function TriStateCheckbox_Template_input_keydown_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onKeyDown($event));\n          })(\"focus\", function TriStateCheckbox_Template_input_focus_2_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFocus());\n          })(\"blur\", function TriStateCheckbox_Template_input_blur_2_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onBlur());\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 4);\n          i0.ɵɵtemplate(5, TriStateCheckbox_ng_container_5_Template, 3, 2, \"ng-container\", 5)(6, TriStateCheckbox_ng_container_6_Template, 3, 2, \"ng-container\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(7, TriStateCheckbox_label_7_Template, 2, 7, \"label\", 6);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.styleClass);\n          i0.ɵɵproperty(\"ngStyle\", ctx.style)(\"ngClass\", i0.ɵɵpureFunction3(20, _c0, ctx.disabled, ctx.focused, ctx.variant === \"filled\" || ctx.config.inputStyle() === \"filled\"));\n          i0.ɵɵattribute(\"data-pc-name\", \"tristatecheckbox\")(\"data-pc-section\", \"root\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"name\", ctx.name)(\"readonly\", ctx.readonly)(\"disabled\", ctx.disabled)(\"autofocus\", ctx.autofocus);\n          i0.ɵɵattribute(\"id\", ctx.inputId)(\"tabindex\", ctx.tabindex)(\"aria-labelledby\", ctx.ariaLabelledBy)(\"aria-label\", ctx.ariaLabel)(\"data-pc-section\", \"hiddenInput\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction3(24, _c1, ctx.value != null, ctx.disabled, ctx.focused));\n          i0.ɵɵattribute(\"aria-checked\", ctx.value === true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.value === true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.value === false);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.label);\n        }\n      },\n      dependencies: () => [i2.NgClass, i2.NgIf, i2.NgTemplateOutlet, i2.NgStyle, i3.AutoFocus, CheckIcon, TimesIcon],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return TriStateCheckbox;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet TriStateCheckboxModule = /*#__PURE__*/(() => {\n  class TriStateCheckboxModule {\n    static ɵfac = function TriStateCheckboxModule_Factory(t) {\n      return new (t || TriStateCheckboxModule)();\n    };\n    static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: TriStateCheckboxModule\n    });\n    static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [CommonModule, SharedModule, AutoFocusModule, CheckIcon, TimesIcon, SharedModule]\n    });\n  }\n  return TriStateCheckboxModule;\n})();\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { TRISTATECHECKBOX_VALUE_ACCESSOR, TriStateCheckbox, TriStateCheckboxModule };\n//# sourceMappingURL=primeng-tristatecheckbox.mjs.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}