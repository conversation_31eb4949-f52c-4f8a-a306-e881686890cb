{"ast": null, "code": "import { Status } from 'src/app/core/models/classroom.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nimport * as i2 from \"src/app/core/services/auth.service\";\nimport * as i3 from \"src/app/core/services/classroom.service\";\nconst _c0 = a0 => ({\n  classRoom: a0\n});\nconst _c1 = (a0, a1) => ({\n  \"teacher-role\": a0,\n  \"trial-classroom\": a1\n});\nfunction TeacherClassroomMiniCardComponent_ng_container_3_img_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 17);\n  }\n}\nfunction TeacherClassroomMiniCardComponent_ng_container_3_img_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 18);\n  }\n}\nfunction TeacherClassroomMiniCardComponent_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 6)(2, \"div\", 7)(3, \"div\", 8);\n    i0.ɵɵtext(4, \"Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 9)(6, \"div\");\n    i0.ɵɵtemplate(7, TeacherClassroomMiniCardComponent_ng_container_3_img_7_Template, 1, 0, \"img\", 10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 11);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"date\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(11, \"div\", 12);\n    i0.ɵɵelementStart(12, \"div\", 7)(13, \"div\", 8);\n    i0.ɵɵtext(14, \"Time\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 9)(16, \"div\");\n    i0.ɵɵtemplate(17, TeacherClassroomMiniCardComponent_ng_container_3_img_17_Template, 1, 0, \"img\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 11);\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(20, \"div\", 14)(21, \"div\");\n    i0.ɵɵelement(22, \"img\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 16);\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.arrangedTrialLesson);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.arrangedTrialLesson ? i0.ɵɵpipeBind2(10, 6, ctx_r0.startingDate, \"dd MMM\") : \"TBC\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.arrangedTrialLesson);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.arrangedTrialLesson ? ctx_r0.generalService.getLessonTime(ctx_r0.startingDate) : \"TBC\", \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r0.getStatusIcon(ctx_r0.classroom.status), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.classroom.status);\n  }\n}\nfunction TeacherClassroomMiniCardComponent_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 6)(2, \"div\", 7)(3, \"div\", 8);\n    i0.ɵɵtext(4, \"Total\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 9)(6, \"div\");\n    i0.ɵɵelement(7, \"img\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 11);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelement(10, \"div\", 12);\n    i0.ɵɵelementStart(11, \"div\", 7)(12, \"div\", 8);\n    i0.ɵɵtext(13, \"Remaining\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"div\", 9)(15, \"div\");\n    i0.ɵɵelement(16, \"img\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 11);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(19, \"div\", 14)(20, \"div\");\n    i0.ɵɵelement(21, \"img\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 16);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.classroomService.getAccumulatedTotalHours(ctx_r0.classroom.packages) || 0, \"h \");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.classroomService.getAccumulatedHoursLeft(ctx_r0.classroom.packages) || 0, \"h \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r0.getStatusIcon(ctx_r0.classroom.status), i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.classroom.status);\n  }\n}\nexport class TeacherClassroomMiniCardComponent {\n  constructor(generalService, authService, classroomService) {\n    this.generalService = generalService;\n    this.authService = authService;\n    this.classroomService = classroomService;\n    this.isTrial = false;\n    this.classroom = {};\n    this.arrangedTrialLesson = {};\n    this.ongoingClassroomPackage = {};\n  }\n  ngOnInit() {\n    if (!this.generalService.isNullishObject(this.classroom)) {}\n    if (this.isTrial) {\n      this.arrangedTrialLesson = this.classroom.lessons.find(lesson => lesson.status.toLowerCase().includes(\"arranged\") || lesson.status.toLowerCase().includes(\"completed\"));\n      if (this.arrangedTrialLesson) {\n        this.startingDate = this.arrangedTrialLesson.startingDate;\n      } else {}\n    }\n  }\n  getOngoingClassroomPackage() {\n    this.ongoingClassroomPackage = this.classroomService.getOngoingPackage(this.classroom.packages);\n    console.log(this.ongoingClassroomPackage);\n    if (this.classroom.packages && this.classroom.packages.length > 0) {\n      if (this.ongoingClassroomPackage) {\n        if (this.generalService.isNullishObject(this.ongoingClassroomPackage)) {\n          this.ongoingClassroomPackage = this.classroom.packages[0];\n          return this.ongoingClassroomPackage;\n        }\n        return this.ongoingClassroomPackage;\n      }\n    }\n    return {};\n  }\n  goToClassRoom() {\n    this.generalService.navigateToLessonDetails(this.classroom, this.authService.getUserRole());\n  }\n  getTrialStatusIcon(status) {\n    if (status.toLowerCase().includes(\"arranged\")) {\n      return \"assets/icons/lessons/paper-plane-blue.svg\";\n    } else if (status.toLowerCase().includes(\"dismissed\")) {\n      return \"assets/icons/lessons/dismissed-blue.svg\";\n    } else if (status.toLowerCase().includes(\"completed\")) {\n      return \"assets/icons/lessons/completed-blue.svg\";\n    } else {\n      return \"assets/icons/lessons/paper-plane-blue.svg\";\n    }\n  }\n  getStatusBgColor(status) {\n    switch (status.toLowerCase()) {\n      case Status.COMPLETED.toLowerCase():\n        return 'blue-completed';\n      case Status.COMPLETED_TRIAL.toLowerCase():\n        return 'blue-completed-trial';\n      case Status.ON_GOING.toLowerCase() && !this.isTrial:\n        return 'blue-dark';\n      case Status.EXPIRED.toLowerCase() && !this.isTrial:\n        return 'expired-gradient-lesson-bg';\n      case Status.DISMISSED_TRIAL.toLowerCase():\n        return 'dismissed-gradient-lesson-bg';\n    }\n    if (this.isTrial) {\n      return 'purple-gradient';\n    }\n    return 'blue-dark';\n  }\n  getStatusIcon(status) {\n    if (this.isTrial) {\n      return 'assets/icons/lessons/paper-plane-blue.svg';\n    }\n    switch (status.toLowerCase()) {\n      case Status.COMPLETED.toLowerCase():\n      case Status.COMPLETED_TRIAL.toLowerCase():\n        return 'assets/icons/classroom/blue-completed.svg';\n      case Status.ON_GOING.toLowerCase():\n        return 'assets/icons/classroom/blue-dark.svg';\n      case Status.EXPIRED.toLowerCase():\n        return '/assets/icons/classroom/expired.svg';\n    }\n    return 'assets/icons/classroom/blue-dark.svg';\n  }\n  static #_ = this.ɵfac = function TeacherClassroomMiniCardComponent_Factory(t) {\n    return new (t || TeacherClassroomMiniCardComponent)(i0.ɵɵdirectiveInject(i1.GeneralService), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.ClassroomService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: TeacherClassroomMiniCardComponent,\n    selectors: [[\"app-teacher-classroom-mini-card\"]],\n    inputs: {\n      isTrial: \"isTrial\",\n      classroom: \"classroom\"\n    },\n    decls: 7,\n    vars: 14,\n    consts: [[3, \"routerLink\", \"state\", \"ngClass\"], [\"headerBackgroundImage\", \"\", \"headerClass\", \"justify-content-center\", \"headerTextClass\", \"white-space-nowrap overflow-hidden text-overflow-ellipsis font-sm\", \"blockClass\", \"block-gradient border-round-xl cursor-pointer\", \"containerClass\", \" relative mb-3\", 3, \"header\", \"headerBlockClass\", \"tooltipLabel\"], [1, \"col-12\"], [4, \"ngIf\"], [1, \"absolute\", \"centered\", \"bottom-0\", \"level-icon-position\"], [3, \"name\"], [1, \"grid\", \"relative\", \"mt-1\", \"mx-3\", \"align-items-center\", \"justify-content-between\", \"border-line-bottom\"], [1, \"col-6\", \"grid\", \"align-items-center\", \"justify-content-center\", \"flex-column\"], [1, \"title-text\", \"mb-2\", \"font-sm\"], [1, \"flex\", \"flex-row\", \"gap-1\"], [\"src\", \"assets/images/classrooms/total-hours-clock-blue.svg\", \"alt\", \"clock\", \"class\", \"icon\", 4, \"ngIf\"], [1, \"font-bold\", \"font-xs\", \"text-blue-dark-color\"], [1, \"divider\"], [\"src\", \"assets/images/classrooms/total-times-clock-blue.svg\", \"alt\", \"clock\", \"class\", \"icon\", 4, \"ngIf\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"flex-row\", \"gap-2\", \"mt-2\", \"mb-3\"], [\"alt\", \"classroom\", 1, \"img-fluid\", 3, \"src\"], [1, \"class-status\", \"text-blue-dark-color\", \"font-sm\", \"capitalize\"], [\"src\", \"assets/images/classrooms/total-hours-clock-blue.svg\", \"alt\", \"clock\", 1, \"icon\"], [\"src\", \"assets/images/classrooms/total-times-clock-blue.svg\", \"alt\", \"clock\", 1, \"icon\"]],\n    template: function TeacherClassroomMiniCardComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"a\", 0)(1, \"app-block-viewer\", 1)(2, \"div\", 2);\n        i0.ɵɵtemplate(3, TeacherClassroomMiniCardComponent_ng_container_3_Template, 25, 9, \"ng-container\", 3)(4, TeacherClassroomMiniCardComponent_ng_container_4_Template, 24, 4, \"ng-container\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 4);\n        i0.ɵɵelement(6, \"g-level-circle\", 5);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"routerLink\", ctx.generalService.getLessonDetailsLink(ctx.classroom, ctx.authService.getUserRole()))(\"state\", i0.ɵɵpureFunction1(9, _c0, ctx.classroom))(\"ngClass\", i0.ɵɵpureFunction2(11, _c1, ctx.authService.isTeacher, ctx.classroom.type === \"Trial\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"header\", ctx.classroomService.getFormattedStudentNames(ctx.classroom.classroomStudents))(\"headerBlockClass\", ctx.getStatusBgColor(ctx.classroom.status) + \" \")(\"tooltipLabel\", ctx.classroomService.getFullStudentNames(ctx.classroom.classroomStudents));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.isTrial);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.isTrial);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"name\", ctx.classroom.activeLevel);\n      }\n    },\n    styles: [\".border-line-bottom[_ngcontent-%COMP%] {\\n  border-bottom: 0.5px solid rgba(94, 113, 219, 0.5);\\n}\\n\\n.title-text[_ngcontent-%COMP%] {\\n  letter-spacing: 0.01em;\\n  text-align: center;\\n  color: #000b4a;\\n}\\n\\n.text-blue-dark-color[_ngcontent-%COMP%] {\\n  color: #000b4a;\\n}\\n\\n.level-icon-position[_ngcontent-%COMP%] {\\n  left: 50%;\\n  transform: translateX(-50%);\\n  top: 100%;\\n}\\n\\n.divider[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  bottom: 0;\\n  width: 1px;\\n  \\n\\n  background-color: rgba(94, 113, 219, 0.5);\\n  \\n\\n  left: 50%;\\n  \\n\\n  transform: translateX(-50%);\\n  \\n\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NsYXNzcm9vbS90ZWFjaGVyLWNsYXNzcm9vbS1taW5pLWNhcmQvdGVhY2hlci1jbGFzc3Jvb20tbWluaS1jYXJkLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUVDO0VBQ0ksa0RBSFc7QUFFaEI7O0FBSUM7RUFDSSxzQkFBQTtFQUNBLGtCQUFBO0VBQ0EsY0FBQTtBQURMOztBQUlDO0VBQ0ksY0FBQTtBQURMOztBQUlDO0VBQ0ksU0FBQTtFQUNBLDJCQUFBO0VBQ0EsU0FBQTtBQURMOztBQUlDO0VBQ0ksa0JBQUE7RUFDQSxNQUFBO0VBQ0EsU0FBQTtFQUNBLFVBQUE7RUFDQSxpQ0FBQTtFQUNBLHlDQUFBO0VBQ0EsaUNBQUE7RUFDQSxTQUFBO0VBQ0Esd0RBQUE7RUFDQSwyQkFBQTtFQUNBLG9DQUFBO0FBREwiLCJzb3VyY2VzQ29udGVudCI6WyIgJGJvcmRlci1zdHlsZTogMC41cHggc29saWQgcmdiYSgjNWU3MWRiLCAwLjUpO1xyXG5cclxuIC5ib3JkZXItbGluZS1ib3R0b20ge1xyXG4gICAgIGJvcmRlci1ib3R0b206ICRib3JkZXItc3R5bGU7XHJcbiB9XHJcblxyXG4gLnRpdGxlLXRleHQge1xyXG4gICAgIGxldHRlci1zcGFjaW5nOiAwLjAxZW07XHJcbiAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gICAgIGNvbG9yOiAjMDAwYjRhO1xyXG4gfVxyXG5cclxuIC50ZXh0LWJsdWUtZGFyay1jb2xvciB7XHJcbiAgICAgY29sb3I6ICMwMDBiNGE7XHJcbiB9XHJcblxyXG4gLmxldmVsLWljb24tcG9zaXRpb24ge1xyXG4gICAgIGxlZnQ6IDUwJTtcclxuICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoLTUwJSk7XHJcbiAgICAgdG9wOiAxMDAlO1xyXG4gfVxyXG5cclxuIC5kaXZpZGVyIHtcclxuICAgICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgICAgdG9wOiAwO1xyXG4gICAgIGJvdHRvbTogMDtcclxuICAgICB3aWR0aDogMXB4O1xyXG4gICAgIC8qIFNldCB0aGUgd2lkdGggb2YgdGhlIGRpdmlkZXIgKi9cclxuICAgICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2JhKCM1ZTcxZGIsIDAuNSk7XHJcbiAgICAgLyogU2V0IHRoZSBjb2xvciBvZiB0aGUgZGl2aWRlciAqL1xyXG4gICAgIGxlZnQ6IDUwJTtcclxuICAgICAvKiBQb3NpdGlvbiB0aGUgZGl2aWRlciBpbiB0aGUgbWlkZGxlIG9mIHRoZSBjb250YWluZXIgKi9cclxuICAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVgoLTUwJSk7XHJcbiAgICAgLyogQ2VudGVyIHRoZSBkaXZpZGVyIGhvcml6b250YWxseSAqL1xyXG4gfSJdLCJzb3VyY2VSb290IjoiIn0= */\"],\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["Status", "i0", "ɵɵelement", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "TeacherClassroomMiniCardComponent_ng_container_3_img_7_Template", "TeacherClassroomMiniCardComponent_ng_container_3_img_17_Template", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "startingDate", "generalService", "getLessonTime", "getStatusIcon", "classroom", "status", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "classroomService", "getAccumulatedTotalHours", "packages", "getAccumulatedHoursLeft", "TeacherClassroomMiniCardComponent", "constructor", "authService", "isTrial", "ongoingClassroomPackage", "ngOnInit", "isNullishObject", "lessons", "find", "lesson", "toLowerCase", "includes", "getOngoingClassroomPackage", "getOngoingPackage", "console", "log", "length", "goToClassRoom", "navigateToLessonDetails", "getUserRole", "getTrialStatusIcon", "getStatusBgColor", "COMPLETED", "COMPLETED_TRIAL", "ON_GOING", "EXPIRED", "DISMISSED_TRIAL", "_", "ɵɵdirectiveInject", "i1", "GeneralService", "i2", "AuthService", "i3", "ClassroomService", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "TeacherClassroomMiniCardComponent_Template", "rf", "ctx", "TeacherClassroomMiniCardComponent_ng_container_3_Template", "TeacherClassroomMiniCardComponent_ng_container_4_Template", "getLessonDetailsLink", "ɵɵpureFunction1", "_c0", "ɵɵpureFunction2", "_c1", "<PERSON><PERSON><PERSON>er", "type", "getFormattedStudentNames", "classroomStudents", "getFullStudentNames", "activeLevel"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\classroom\\teacher-classroom-mini-card\\teacher-classroom-mini-card.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\classroom\\teacher-classroom-mini-card\\teacher-classroom-mini-card.component.html"], "sourcesContent": ["import { ChangeDetectionStrategy, Component, Input, OnInit } from '@angular/core';\r\nimport { Classroom, Status } from 'src/app/core/models/classroom.model';\r\nimport { Lesson, LessonStatus } from 'src/app/core/models/lesson.model';\r\nimport { Package } from 'src/app/core/models/package.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\n\r\n@Component({\r\n  selector: 'app-teacher-classroom-mini-card',\r\n  templateUrl: './teacher-classroom-mini-card.component.html',\r\n  styleUrls: ['./teacher-classroom-mini-card.component.scss'],\r\n  changeDetection: ChangeDetectionStrategy.OnPush\r\n})\r\nexport class TeacherClassroomMiniCardComponent implements OnInit {\r\n\r\n  @Input() isTrial: boolean = false;\r\n  @Input() classroom = {} as Classroom;\r\n  arrangedTrialLesson = {} as Lesson | undefined;\r\n  startingDate!: Date;\r\n  ongoingClassroomPackage = {} as Package;\r\n  constructor(\r\n    public generalService: GeneralService,\r\n    public authService: AuthService,\r\n    public classroomService: ClassroomService,\r\n    ) { }\r\n\r\n  ngOnInit(): void {\r\n    if (!this.generalService.isNullishObject(this.classroom)) {\r\n    }\r\n    if (this.isTrial) {\r\n      this.arrangedTrialLesson = this.classroom.lessons!.find(lesson => lesson.status.toLowerCase().includes(\"arranged\") || lesson.status.toLowerCase().includes(\"completed\"));\r\n      if (this.arrangedTrialLesson) {\r\n        this.startingDate = this.arrangedTrialLesson.startingDate;\r\n      } else {\r\n      }\r\n    }\r\n\r\n  }\r\n\r\n  getOngoingClassroomPackage(): Package {\r\n    this.ongoingClassroomPackage = this.classroomService.getOngoingPackage(this.classroom.packages!) as Package;\r\n    console.log(this.ongoingClassroomPackage);\r\n    if (this.classroom.packages && this.classroom.packages!.length > 0) {\r\n      if (this.ongoingClassroomPackage) {\r\n\r\n        if (this.generalService.isNullishObject(this.ongoingClassroomPackage)) {\r\n          this.ongoingClassroomPackage = this.classroom.packages![0];\r\n          return this.ongoingClassroomPackage;\r\n        }\r\n        return this.ongoingClassroomPackage;\r\n      }\r\n    }\r\n    return {} as Package;\r\n  }\r\n\r\n  goToClassRoom() {\r\n    this.generalService.navigateToLessonDetails(this.classroom, this.authService.getUserRole());\r\n  }\r\n\r\n  getTrialStatusIcon(status: string): string {\r\n    if (status.toLowerCase().includes(\"arranged\")) {\r\n      return \"assets/icons/lessons/paper-plane-blue.svg\";\r\n    } else if (status.toLowerCase().includes(\"dismissed\")) {\r\n      return \"assets/icons/lessons/dismissed-blue.svg\";\r\n    } else if (status.toLowerCase().includes(\"completed\")) {\r\n      return \"assets/icons/lessons/completed-blue.svg\";\r\n    } else {\r\n      return \"assets/icons/lessons/paper-plane-blue.svg\";\r\n    }\r\n  }\r\n\r\n  getStatusBgColor(status: string): string {\r\n\r\n    switch (status.toLowerCase()) {\r\n      case Status.COMPLETED.toLowerCase():\r\n        return 'blue-completed';\r\n      case Status.COMPLETED_TRIAL.toLowerCase():\r\n        return 'blue-completed-trial';\r\n      case Status.ON_GOING.toLowerCase() && !this.isTrial:\r\n        return 'blue-dark';\r\n        case Status.EXPIRED.toLowerCase() && !this.isTrial:\r\n          return 'expired-gradient-lesson-bg';\r\n          case Status.DISMISSED_TRIAL.toLowerCase():\r\n            return 'dismissed-gradient-lesson-bg';\r\n    }\r\n    if (this.isTrial) {\r\n      return 'purple-gradient';\r\n    }\r\n    return 'blue-dark';\r\n  }\r\n\r\n  getStatusIcon(status: string): string {\r\n    if (this.isTrial) {\r\n      return 'assets/icons/lessons/paper-plane-blue.svg';\r\n    }\r\n    switch (status.toLowerCase()) {\r\n      case Status.COMPLETED.toLowerCase():\r\n      case Status.COMPLETED_TRIAL.toLowerCase():\r\n        return 'assets/icons/classroom/blue-completed.svg';\r\n      case Status.ON_GOING.toLowerCase():\r\n        return 'assets/icons/classroom/blue-dark.svg';\r\n      case Status.EXPIRED.toLowerCase():\r\n        return '/assets/icons/classroom/expired.svg';\r\n    }\r\n    return 'assets/icons/classroom/blue-dark.svg';\r\n  }\r\n\r\n}\r\n", "<a [routerLink]=\"generalService.getLessonDetailsLink(classroom, this.authService.getUserRole())\" \r\n   [state]=\"{ classRoom: classroom }\" \r\n   [ngClass]=\"{ 'teacher-role': authService.isTeacher, 'trial-classroom': classroom.type === 'Trial' }\">\r\n<app-block-viewer [header]=\"classroomService.getFormattedStudentNames(classroom.classroomStudents)\" headerBackgroundImage=\"\" \r\n    [headerBlockClass]=\"getStatusBgColor(classroom.status) + ' '\"\r\n    headerClass=\"justify-content-center\"\r\n    [tooltipLabel]=\"classroomService.getFullStudentNames(classroom.classroomStudents)\"\r\n    headerTextClass=\"white-space-nowrap overflow-hidden text-overflow-ellipsis font-sm\"\r\n    blockClass=\"block-gradient border-round-xl cursor-pointer\" containerClass=\" relative mb-3\">\r\n    <div class=\"col-12\">\r\n    <ng-container *ngIf=\"isTrial\">\r\n\r\n        <div class=\"grid relative mt-1  mx-3 align-items-center justify-content-between border-line-bottom\">\r\n\r\n            <div class=\"col-6 grid align-items-center justify-content-center flex-column\">\r\n\r\n                <div class=\"title-text mb-2 font-sm\">Date</div>\r\n                <div class=\"flex flex-row gap-1\">\r\n                    <div><img *ngIf=\"!arrangedTrialLesson\" src=\"assets/images/classrooms/total-hours-clock-blue.svg\" alt=\"clock\" class=\"icon\"></div>\r\n                    <div class=\"font-bold font-xs text-blue-dark-color\"> {{arrangedTrialLesson ? (startingDate | date:\r\n                        'dd MMM'): 'TBC'}} </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"divider\"></div> <!-- New element for the divider -->\r\n            <div class=\"col-6 grid align-items-center justify-content-center flex-column\">\r\n                <div class=\"title-text mb-2 font-sm\">Time</div>\r\n                <div class=\"flex flex-row gap-1\">\r\n                    <div><img *ngIf=\"!arrangedTrialLesson\" src=\"assets/images/classrooms/total-times-clock-blue.svg\" alt=\"clock\" class=\"icon\"></div>\r\n                    <div class=\"font-bold font-xs text-blue-dark-color\"> {{arrangedTrialLesson ? (generalService.getLessonTime(startingDate) ):\r\n                        'TBC'}}</div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"flex  align-items-center justify-content-center flex-row gap-2  mt-2 mb-3\">\r\n            <div><img [src]=\"getStatusIcon(classroom.status)\" alt=\"classroom\"\r\n                    class=\"img-fluid\"></div>\r\n            <div class=\"class-status text-blue-dark-color font-sm capitalize\">{{classroom.status}}</div>\r\n        </div>\r\n        </ng-container>\r\n\r\n        <ng-container *ngIf=\"!isTrial\">\r\n        <div class=\"grid relative mt-1  mx-3 align-items-center justify-content-between border-line-bottom\">\r\n\r\n            <div class=\"col-6 grid align-items-center justify-content-center flex-column\">\r\n\r\n                <div class=\"title-text mb-2 font-sm\">Total</div>\r\n                <div class=\"flex flex-row gap-1\">\r\n                    <div><img src=\"assets/images/classrooms/total-hours-clock-blue.svg\" alt=\"clock\" class=\"icon\"></div>\r\n                    <div class=\"font-bold font-xs text-blue-dark-color\"> {{this.classroomService.getAccumulatedTotalHours(classroom.packages!) || 0 }}h </div>\r\n                </div>\r\n            </div>\r\n            <div class=\"divider\"></div> <!-- New element for the divider -->\r\n            <div class=\"col-6 grid align-items-center justify-content-center flex-column\">\r\n                <div class=\"title-text mb-2 font-sm\">Remaining</div>\r\n                <div class=\"flex flex-row gap-1\">\r\n                    <div><img src=\"assets/images/classrooms/total-times-clock-blue.svg\" alt=\"clock\" class=\"icon\"></div>\r\n                    <div class=\"font-bold font-xs text-blue-dark-color\"> {{this.classroomService.getAccumulatedHoursLeft(classroom.packages!) || 0 }}h </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <div class=\"flex  align-items-center justify-content-center flex-row gap-2  mt-2 mb-3\">\r\n            <div><img [src]=\"getStatusIcon(classroom.status)\" alt=\"classroom\"\r\n                    class=\"img-fluid\"></div>\r\n            <div class=\"class-status text-blue-dark-color font-sm capitalize\">{{classroom.status}}</div>\r\n        </div>\r\n        </ng-container>\r\n    </div>\r\n    <div class=\"absolute centered bottom-0 level-icon-position\">\r\n\r\n        <g-level-circle [name]=\"classroom.activeLevel\"></g-level-circle>\r\n\r\n    </div>\r\n</app-block-viewer>\r\n</a>"], "mappings": "AACA,SAAoBA,MAAM,QAAQ,qCAAqC;;;;;;;;;;;;;;ICiB9CC,EAAA,CAAAC,SAAA,cAAqH;;;;;IASrHD,EAAA,CAAAC,SAAA,cAAqH;;;;;IAjB1ID,EAAA,CAAAE,uBAAA,GAA8B;IAMlBF,EAJR,CAAAG,cAAA,aAAoG,aAElB,aAErC;IAAAH,EAAA,CAAAI,MAAA,WAAI;IAAAJ,EAAA,CAAAK,YAAA,EAAM;IAE3CL,EADJ,CAAAG,cAAA,aAAiC,UACxB;IAAAH,EAAA,CAAAM,UAAA,IAAAC,+DAAA,kBAAqH;IAAAP,EAAA,CAAAK,YAAA,EAAM;IAChIL,EAAA,CAAAG,cAAA,cAAoD;IAACH,EAAA,CAAAI,MAAA,GAC9B;;IAE/BJ,EAF+B,CAAAK,YAAA,EAAM,EAC3B,EACJ;IACNL,EAAA,CAAAC,SAAA,eAA2B;IAEvBD,EADJ,CAAAG,cAAA,cAA8E,cACrC;IAAAH,EAAA,CAAAI,MAAA,YAAI;IAAAJ,EAAA,CAAAK,YAAA,EAAM;IAE3CL,EADJ,CAAAG,cAAA,cAAiC,WACxB;IAAAH,EAAA,CAAAM,UAAA,KAAAE,gEAAA,kBAAqH;IAAAR,EAAA,CAAAK,YAAA,EAAM;IAChIL,EAAA,CAAAG,cAAA,eAAoD;IAACH,EAAA,CAAAI,MAAA,IAC1C;IAGvBJ,EAHuB,CAAAK,YAAA,EAAM,EACf,EACJ,EACJ;IAGFL,EADJ,CAAAG,cAAA,eAAuF,WAC9E;IAAAH,EAAA,CAAAC,SAAA,eACqB;IAAAD,EAAA,CAAAK,YAAA,EAAM;IAChCL,EAAA,CAAAG,cAAA,eAAkE;IAAAH,EAAA,CAAAI,MAAA,IAAoB;IAC1FJ,EAD0F,CAAAK,YAAA,EAAM,EAC1F;;;;;IApBiBL,EAAA,CAAAS,SAAA,GAA0B;IAA1BT,EAAA,CAAAU,UAAA,UAAAC,MAAA,CAAAC,mBAAA,CAA0B;IACgBZ,EAAA,CAAAS,SAAA,GAC9B;IAD8BT,EAAA,CAAAa,kBAAA,MAAAF,MAAA,CAAAC,mBAAA,GAAAZ,EAAA,CAAAc,WAAA,QAAAH,MAAA,CAAAI,YAAA,yBAC9B;IAOZf,EAAA,CAAAS,SAAA,GAA0B;IAA1BT,EAAA,CAAAU,UAAA,UAAAC,MAAA,CAAAC,mBAAA,CAA0B;IACgBZ,EAAA,CAAAS,SAAA,GAC1C;IAD0CT,EAAA,CAAAa,kBAAA,MAAAF,MAAA,CAAAC,mBAAA,GAAAD,MAAA,CAAAK,cAAA,CAAAC,aAAA,CAAAN,MAAA,CAAAI,YAAA,cAC1C;IAMTf,EAAA,CAAAS,SAAA,GAAuC;IAAvCT,EAAA,CAAAU,UAAA,QAAAC,MAAA,CAAAO,aAAA,CAAAP,MAAA,CAAAQ,SAAA,CAAAC,MAAA,GAAApB,EAAA,CAAAqB,aAAA,CAAuC;IAEiBrB,EAAA,CAAAS,SAAA,GAAoB;IAApBT,EAAA,CAAAsB,iBAAA,CAAAX,MAAA,CAAAQ,SAAA,CAAAC,MAAA,CAAoB;;;;;IAI1FpB,EAAA,CAAAE,uBAAA,GAA+B;IAKvBF,EAJR,CAAAG,cAAA,aAAoG,aAElB,aAErC;IAAAH,EAAA,CAAAI,MAAA,YAAK;IAAAJ,EAAA,CAAAK,YAAA,EAAM;IAE5CL,EADJ,CAAAG,cAAA,aAAiC,UACxB;IAAAH,EAAA,CAAAC,SAAA,cAAwF;IAAAD,EAAA,CAAAK,YAAA,EAAM;IACnGL,EAAA,CAAAG,cAAA,cAAoD;IAACH,EAAA,CAAAI,MAAA,GAA+E;IAE5IJ,EAF4I,CAAAK,YAAA,EAAM,EACxI,EACJ;IACNL,EAAA,CAAAC,SAAA,eAA2B;IAEvBD,EADJ,CAAAG,cAAA,cAA8E,cACrC;IAAAH,EAAA,CAAAI,MAAA,iBAAS;IAAAJ,EAAA,CAAAK,YAAA,EAAM;IAEhDL,EADJ,CAAAG,cAAA,cAAiC,WACxB;IAAAH,EAAA,CAAAC,SAAA,eAAwF;IAAAD,EAAA,CAAAK,YAAA,EAAM;IACnGL,EAAA,CAAAG,cAAA,eAAoD;IAACH,EAAA,CAAAI,MAAA,IAA8E;IAG/IJ,EAH+I,CAAAK,YAAA,EAAM,EACvI,EACJ,EACJ;IAGFL,EADJ,CAAAG,cAAA,eAAuF,WAC9E;IAAAH,EAAA,CAAAC,SAAA,eACqB;IAAAD,EAAA,CAAAK,YAAA,EAAM;IAChCL,EAAA,CAAAG,cAAA,eAAkE;IAAAH,EAAA,CAAAI,MAAA,IAAoB;IAC1FJ,EAD0F,CAAAK,YAAA,EAAM,EAC1F;;;;;IAjB2DL,EAAA,CAAAS,SAAA,GAA+E;IAA/ET,EAAA,CAAAa,kBAAA,MAAAF,MAAA,CAAAY,gBAAA,CAAAC,wBAAA,CAAAb,MAAA,CAAAQ,SAAA,CAAAM,QAAA,aAA+E;IAQ/EzB,EAAA,CAAAS,SAAA,GAA8E;IAA9ET,EAAA,CAAAa,kBAAA,MAAAF,MAAA,CAAAY,gBAAA,CAAAG,uBAAA,CAAAf,MAAA,CAAAQ,SAAA,CAAAM,QAAA,aAA8E;IAMjIzB,EAAA,CAAAS,SAAA,GAAuC;IAAvCT,EAAA,CAAAU,UAAA,QAAAC,MAAA,CAAAO,aAAA,CAAAP,MAAA,CAAAQ,SAAA,CAAAC,MAAA,GAAApB,EAAA,CAAAqB,aAAA,CAAuC;IAEiBrB,EAAA,CAAAS,SAAA,GAAoB;IAApBT,EAAA,CAAAsB,iBAAA,CAAAX,MAAA,CAAAQ,SAAA,CAAAC,MAAA,CAAoB;;;ADnDlG,OAAM,MAAOO,iCAAiC;EAO5CC,YACSZ,cAA8B,EAC9Ba,WAAwB,EACxBN,gBAAkC;IAFlC,KAAAP,cAAc,GAAdA,cAAc;IACd,KAAAa,WAAW,GAAXA,WAAW;IACX,KAAAN,gBAAgB,GAAhBA,gBAAgB;IARhB,KAAAO,OAAO,GAAY,KAAK;IACxB,KAAAX,SAAS,GAAG,EAAe;IACpC,KAAAP,mBAAmB,GAAG,EAAwB;IAE9C,KAAAmB,uBAAuB,GAAG,EAAa;EAKjC;EAENC,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAAChB,cAAc,CAACiB,eAAe,CAAC,IAAI,CAACd,SAAS,CAAC,EAAE,CAC1D;IACA,IAAI,IAAI,CAACW,OAAO,EAAE;MAChB,IAAI,CAAClB,mBAAmB,GAAG,IAAI,CAACO,SAAS,CAACe,OAAQ,CAACC,IAAI,CAACC,MAAM,IAAIA,MAAM,CAAChB,MAAM,CAACiB,WAAW,EAAE,CAACC,QAAQ,CAAC,UAAU,CAAC,IAAIF,MAAM,CAAChB,MAAM,CAACiB,WAAW,EAAE,CAACC,QAAQ,CAAC,WAAW,CAAC,CAAC;MACxK,IAAI,IAAI,CAAC1B,mBAAmB,EAAE;QAC5B,IAAI,CAACG,YAAY,GAAG,IAAI,CAACH,mBAAmB,CAACG,YAAY;MAC3D,CAAC,MAAM,CACP;IACF;EAEF;EAEAwB,0BAA0BA,CAAA;IACxB,IAAI,CAACR,uBAAuB,GAAG,IAAI,CAACR,gBAAgB,CAACiB,iBAAiB,CAAC,IAAI,CAACrB,SAAS,CAACM,QAAS,CAAY;IAC3GgB,OAAO,CAACC,GAAG,CAAC,IAAI,CAACX,uBAAuB,CAAC;IACzC,IAAI,IAAI,CAACZ,SAAS,CAACM,QAAQ,IAAI,IAAI,CAACN,SAAS,CAACM,QAAS,CAACkB,MAAM,GAAG,CAAC,EAAE;MAClE,IAAI,IAAI,CAACZ,uBAAuB,EAAE;QAEhC,IAAI,IAAI,CAACf,cAAc,CAACiB,eAAe,CAAC,IAAI,CAACF,uBAAuB,CAAC,EAAE;UACrE,IAAI,CAACA,uBAAuB,GAAG,IAAI,CAACZ,SAAS,CAACM,QAAS,CAAC,CAAC,CAAC;UAC1D,OAAO,IAAI,CAACM,uBAAuB;QACrC;QACA,OAAO,IAAI,CAACA,uBAAuB;MACrC;IACF;IACA,OAAO,EAAa;EACtB;EAEAa,aAAaA,CAAA;IACX,IAAI,CAAC5B,cAAc,CAAC6B,uBAAuB,CAAC,IAAI,CAAC1B,SAAS,EAAE,IAAI,CAACU,WAAW,CAACiB,WAAW,EAAE,CAAC;EAC7F;EAEAC,kBAAkBA,CAAC3B,MAAc;IAC/B,IAAIA,MAAM,CAACiB,WAAW,EAAE,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC7C,OAAO,2CAA2C;IACpD,CAAC,MAAM,IAAIlB,MAAM,CAACiB,WAAW,EAAE,CAACC,QAAQ,CAAC,WAAW,CAAC,EAAE;MACrD,OAAO,yCAAyC;IAClD,CAAC,MAAM,IAAIlB,MAAM,CAACiB,WAAW,EAAE,CAACC,QAAQ,CAAC,WAAW,CAAC,EAAE;MACrD,OAAO,yCAAyC;IAClD,CAAC,MAAM;MACL,OAAO,2CAA2C;IACpD;EACF;EAEAU,gBAAgBA,CAAC5B,MAAc;IAE7B,QAAQA,MAAM,CAACiB,WAAW,EAAE;MAC1B,KAAKtC,MAAM,CAACkD,SAAS,CAACZ,WAAW,EAAE;QACjC,OAAO,gBAAgB;MACzB,KAAKtC,MAAM,CAACmD,eAAe,CAACb,WAAW,EAAE;QACvC,OAAO,sBAAsB;MAC/B,KAAKtC,MAAM,CAACoD,QAAQ,CAACd,WAAW,EAAE,IAAI,CAAC,IAAI,CAACP,OAAO;QACjD,OAAO,WAAW;MAClB,KAAK/B,MAAM,CAACqD,OAAO,CAACf,WAAW,EAAE,IAAI,CAAC,IAAI,CAACP,OAAO;QAChD,OAAO,4BAA4B;MACnC,KAAK/B,MAAM,CAACsD,eAAe,CAAChB,WAAW,EAAE;QACvC,OAAO,8BAA8B;IAC7C;IACA,IAAI,IAAI,CAACP,OAAO,EAAE;MAChB,OAAO,iBAAiB;IAC1B;IACA,OAAO,WAAW;EACpB;EAEAZ,aAAaA,CAACE,MAAc;IAC1B,IAAI,IAAI,CAACU,OAAO,EAAE;MAChB,OAAO,2CAA2C;IACpD;IACA,QAAQV,MAAM,CAACiB,WAAW,EAAE;MAC1B,KAAKtC,MAAM,CAACkD,SAAS,CAACZ,WAAW,EAAE;MACnC,KAAKtC,MAAM,CAACmD,eAAe,CAACb,WAAW,EAAE;QACvC,OAAO,2CAA2C;MACpD,KAAKtC,MAAM,CAACoD,QAAQ,CAACd,WAAW,EAAE;QAChC,OAAO,sCAAsC;MAC/C,KAAKtC,MAAM,CAACqD,OAAO,CAACf,WAAW,EAAE;QAC/B,OAAO,qCAAqC;IAChD;IACA,OAAO,sCAAsC;EAC/C;EAAC,QAAAiB,CAAA,G;qBA5FU3B,iCAAiC,EAAA3B,EAAA,CAAAuD,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAzD,EAAA,CAAAuD,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA3D,EAAA,CAAAuD,iBAAA,CAAAK,EAAA,CAAAC,gBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAjCnC,iCAAiC;IAAAoC,SAAA;IAAAC,MAAA;MAAAlC,OAAA;MAAAX,SAAA;IAAA;IAAA8C,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,2CAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCL1CtE,EATJ,CAAAG,cAAA,WAEwG,0BAMT,aACvE;QAgChBH,EA/BJ,CAAAM,UAAA,IAAAkE,yDAAA,2BAA8B,IAAAC,yDAAA,2BA+BK;QA2BnCzE,EAAA,CAAAK,YAAA,EAAM;QACNL,EAAA,CAAAG,cAAA,aAA4D;QAExDH,EAAA,CAAAC,SAAA,wBAAgE;QAIxED,EAFI,CAAAK,YAAA,EAAM,EACS,EACf;;;QAzEDL,EAFA,CAAAU,UAAA,eAAA6D,GAAA,CAAAvD,cAAA,CAAA0D,oBAAA,CAAAH,GAAA,CAAApD,SAAA,EAAAoD,GAAA,CAAA1C,WAAA,CAAAiB,WAAA,IAA6F,UAAA9C,EAAA,CAAA2E,eAAA,IAAAC,GAAA,EAAAL,GAAA,CAAApD,SAAA,EAC3D,YAAAnB,EAAA,CAAA6E,eAAA,KAAAC,GAAA,EAAAP,GAAA,CAAA1C,WAAA,CAAAkD,SAAA,EAAAR,GAAA,CAAApD,SAAA,CAAA6D,IAAA,cACkE;QACrFhF,EAAA,CAAAS,SAAA,EAAiF;QAG/FT,EAHc,CAAAU,UAAA,WAAA6D,GAAA,CAAAhD,gBAAA,CAAA0D,wBAAA,CAAAV,GAAA,CAAApD,SAAA,CAAA+D,iBAAA,EAAiF,qBAAAX,GAAA,CAAAvB,gBAAA,CAAAuB,GAAA,CAAApD,SAAA,CAAAC,MAAA,QAClC,iBAAAmD,GAAA,CAAAhD,gBAAA,CAAA4D,mBAAA,CAAAZ,GAAA,CAAApD,SAAA,CAAA+D,iBAAA,EAEqB;QAInElF,EAAA,CAAAS,SAAA,GAAa;QAAbT,EAAA,CAAAU,UAAA,SAAA6D,GAAA,CAAAzC,OAAA,CAAa;QA+BT9B,EAAA,CAAAS,SAAA,EAAc;QAAdT,EAAA,CAAAU,UAAA,UAAA6D,GAAA,CAAAzC,OAAA,CAAc;QA8Bb9B,EAAA,CAAAS,SAAA,GAA8B;QAA9BT,EAAA,CAAAU,UAAA,SAAA6D,GAAA,CAAApD,SAAA,CAAAiE,WAAA,CAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}