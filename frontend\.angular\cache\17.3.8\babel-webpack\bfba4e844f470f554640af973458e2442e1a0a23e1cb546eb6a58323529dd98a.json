{"ast": null, "code": "import { UserRole } from 'src/app/core/models/user.model';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/general.service\";\nimport * as i2 from \"src/app/core/services/classroom.service\";\nimport * as i3 from \"src/app/core/services/toast.service\";\nimport * as i4 from \"src/app/core/services/calendar.service\";\nimport * as i5 from \"src/app/core/services/auth.service\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"@angular/common\";\nfunction AwaitingApprovalNotificationComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 20);\n    i0.ɵɵtext(2, \" Requested from: \");\n    i0.ɵɵelementStart(3, \"span\", 21);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.requestedName);\n  }\n}\nfunction AwaitingApprovalNotificationComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtext(1, \" Requested to: \");\n    i0.ɵɵelementStart(2, \"span\", 21);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.requestedName);\n  }\n}\nfunction AwaitingApprovalNotificationComponent_ng_container_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 22);\n    i0.ɵɵlistener(\"click\", function AwaitingApprovalNotificationComponent_ng_container_27_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onLessonAcceptedClicked());\n    });\n    i0.ɵɵelementStart(2, \"div\", 23)(3, \"span\", 18);\n    i0.ɵɵtext(4, \"ACCEPT\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"img\", 24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction AwaitingApprovalNotificationComponent_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function AwaitingApprovalNotificationComponent_button_28_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onLessonRescheduleClicked());\n    });\n    i0.ɵɵelementStart(1, \"div\", 23)(2, \"span\", 18);\n    i0.ɵɵtext(3, \"RESCHEDULE\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"img\", 26);\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class AwaitingApprovalNotificationComponent {\n  constructor(generalService, classroomService, toastService, calendarService, authService, router) {\n    this.generalService = generalService;\n    this.classroomService = classroomService;\n    this.toastService = toastService;\n    this.calendarService = calendarService;\n    this.authService = authService;\n    this.router = router;\n    this.subs = new SubSink();\n    this.UserRoles = UserRole;\n    this.role = UserRole.NONE;\n  }\n  ngOnInit() {\n    console.log(this.lesson);\n  }\n  ngOnDestroy() {\n    this.subs.unsubscribe();\n  }\n  onLessonAcceptedClicked() {\n    let params = {\n      lessonId: this.lessonId\n    };\n    console.log(params);\n    this.subs.add(this.classroomService.acceptLessonRequest(this.lessonId).subscribe(res => {\n      if (res) {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Lesson request accepted.'\n        });\n        this.calendarService.setToggleOverlayListener(true);\n        this.calendarService.setUpdateListener(true);\n      }\n    }));\n  }\n  onLessonRejectedClicked() {\n    let params = {\n      lessonId: this.lessonId\n    };\n    console.log(params);\n    this.subs.add(this.classroomService.rejectLessonRequest(this.lessonId).subscribe(res => {\n      if (res) {\n        this.toastService.setShowToastmessage({\n          severity: 'success',\n          summary: '',\n          detail: 'Lesson request rejected.'\n        });\n        this.calendarService.setToggleOverlayListener(true);\n        this.calendarService.setUpdateListener(true);\n      }\n    }));\n  }\n  onLessonRescheduleClicked() {\n    const navigationExtras = {\n      queryParams: {\n        'lesson': JSON.stringify(this.lesson),\n        'reschedule': true,\n        'answered': false\n      }\n    };\n    this.router.navigate(['/dashboard/calendar/booking-system'], navigationExtras);\n  }\n  static #_ = this.ɵfac = function AwaitingApprovalNotificationComponent_Factory(t) {\n    return new (t || AwaitingApprovalNotificationComponent)(i0.ɵɵdirectiveInject(i1.GeneralService), i0.ɵɵdirectiveInject(i2.ClassroomService), i0.ɵɵdirectiveInject(i3.ToastService), i0.ɵɵdirectiveInject(i4.CalendarService), i0.ɵɵdirectiveInject(i5.AuthService), i0.ɵɵdirectiveInject(i6.Router));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AwaitingApprovalNotificationComponent,\n    selectors: [[\"app-awaiting-approval-notification\"]],\n    inputs: {\n      index: \"index\",\n      lessonId: \"lessonId\",\n      time: \"time\",\n      date: \"date\",\n      duration: \"duration\",\n      requestedName: \"requestedName\",\n      avatar: \"avatar\",\n      role: \"role\",\n      lesson: \"lesson\"\n    },\n    decls: 35,\n    vars: 9,\n    consts: [[\"studentBlock\", \"\"], [1, \"grid\", \"my-1\", \"grid-nogutter\"], [1, \"col-2\", \"circle-num-gradient\"], [1, \"col\", \"avatar\", \"mb-1\"], [\"alt\", \"\", 1, \"avatarImg\", 3, \"error\", \"src\"], [4, \"ngIf\", \"ngIfElse\"], [1, \"col\", \"py-1\"], [1, \"flex\", \"font-sm\", \"justify-content-center\"], [1, \"flex\", \"flex-col\", \"mr-1\"], [\"src\", \"/assets/icons/calendar-sm.svg\", 1, \"w-10\"], [\"src\", \"/assets/icons/clock-blue.svg\", 1, \"w-10\"], [1, \"flex\", \"flex-col\"], [\"src\", \"/assets/icons/hourglass.svg\", 1, \"w-10\", \"mt-3px\"], [1, \"flex\", \"justify-content-evenly\", \"gap-2\"], [4, \"ngIf\"], [\"pbutton\", \"\", \"type\", \"button\", \"class\", \"p-element p-button-outlined p-button-rounded button-outlined p-button-xs p-button p-component py-1\", 3, \"click\", 4, \"ngIf\"], [\"pbutton\", \"\", \"type\", \"button\", 1, \"p-element\", \"p-button-outlined\", \"p-button-rounded\", \"button-outlined\", \"p-button-xs\", \"p-button\", \"p-component\", \"py-1\", \"btn-blue\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"gap-1\"], [1, \"font-xs\"], [1, \"pi\", \"pi-times\", \"icon-btn\"], [1, \"grid\", \"justify-content-center\", \"font-sm\"], [1, \"font-semibold\"], [\"pbutton\", \"\", \"type\", \"button\", 1, \"p-element\", \"btn-accept\", \"p-button-outlined\", \"p-button-rounded\", \"button-outlined\", \"p-button-xs\", \"p-button\", \"p-component\", \"py-1\", 3, \"click\"], [1, \"flex\", \"align-items-center\", \"gap-1\", \"jus\", \"ju\"], [\"src\", \"/assets/icons/check-white.svg\", 1, \"icon-btn\"], [\"pbutton\", \"\", \"type\", \"button\", 1, \"p-element\", \"p-button-outlined\", \"p-button-rounded\", \"button-outlined\", \"p-button-xs\", \"p-button\", \"p-component\", \"py-1\", 3, \"click\"], [\"src\", \"/assets/icons/date.svg\", 1, \"icon-btn\"]],\n    template: function AwaitingApprovalNotificationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n        i0.ɵɵtext(2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 3)(4, \"img\", 4);\n        i0.ɵɵlistener(\"error\", function AwaitingApprovalNotificationComponent_Template_img_error_4_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.generalService.setDefaultUserAvatar($event));\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementContainerStart(5);\n        i0.ɵɵtemplate(6, AwaitingApprovalNotificationComponent_ng_container_6_Template, 5, 1, \"ng-container\", 5)(7, AwaitingApprovalNotificationComponent_ng_template_7_Template, 4, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementStart(9, \"div\", 6)(10, \"div\", 7)(11, \"div\", 8)(12, \"span\");\n        i0.ɵɵelement(13, \"img\", 9);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"span\");\n        i0.ɵɵtext(15);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"div\", 8)(17, \"span\");\n        i0.ɵɵelement(18, \"img\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(19, \"span\");\n        i0.ɵɵtext(20);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(21, \"div\", 11)(22, \"span\");\n        i0.ɵɵelement(23, \"img\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"span\");\n        i0.ɵɵtext(25);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(26, \"div\", 13);\n        i0.ɵɵtemplate(27, AwaitingApprovalNotificationComponent_ng_container_27_Template, 6, 0, \"ng-container\", 14)(28, AwaitingApprovalNotificationComponent_button_28_Template, 5, 0, \"button\", 15);\n        i0.ɵɵelementStart(29, \"button\", 16);\n        i0.ɵɵlistener(\"click\", function AwaitingApprovalNotificationComponent_Template_button_click_29_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onLessonRejectedClicked());\n        });\n        i0.ɵɵelementStart(30, \"div\", 17)(31, \"span\", 18);\n        i0.ɵɵtext(32, \"REMOVE\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(33, \"i\", 19);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelement(34, \"hr\");\n      }\n      if (rf & 2) {\n        const studentBlock_r5 = i0.ɵɵreference(8);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(ctx.index);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"src\", ctx.generalService.getDomainFileNamePath(ctx.avatar), i0.ɵɵsanitizeUrl);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.role === ctx.UserRoles.TEACHER)(\"ngIfElse\", studentBlock_r5);\n        i0.ɵɵadvance(9);\n        i0.ɵɵtextInterpolate(ctx.date);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.time);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.duration);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.role === ctx.UserRoles.TEACHER);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.authService.isStudent);\n      }\n    },\n    dependencies: [i7.NgIf],\n    styles: [\".circle-num-gradient[_ngcontent-%COMP%] {\\n  width: 2.5rem;\\n  height: 2.5rem;\\n  border-radius: 50%;\\n  background: linear-gradient(180deg, #002ccf 0%, #c9d2ff 100%);\\n  border: 2px solid #A5B4F1;\\n  color: #fff;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\nhr[_ngcontent-%COMP%] {\\n  height: 1px;\\n  color: #071FD8;\\n  background-color: #071FD8;\\n  opacity: 0.3;\\n  margin: 1rem 0;\\n}\\n\\n.mt-3px[_ngcontent-%COMP%] {\\n  margin-top: 3px;\\n}\\n\\n.avatar[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding-top: 0;\\n  margin-left: -2.5rem;\\n}\\n\\n.icon-btn[_ngcontent-%COMP%] {\\n  width: 0.85rem;\\n}\\n\\n.btn-blue[_ngcontent-%COMP%] {\\n  background-color: #3F51B5;\\n  color: #fff;\\n  box-shadow: none;\\n}\\n\\n.btn-accept[_ngcontent-%COMP%] {\\n  background: rgb(64, 78, 207);\\n  background: linear-gradient(90deg, rgb(64, 78, 207) 0%, rgb(93, 191, 221) 100%);\\n  color: #fff;\\n}\\n\\n.avatarImg[_ngcontent-%COMP%] {\\n  width: 50px;\\n  border-radius: 50%;\\n  background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0.33, #002ccf), color-stop(0.67, #a796ff));\\n  padding: 3px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["UserRole", "SubSink", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "requested<PERSON><PERSON>", "ɵɵlistener", "AwaitingApprovalNotificationComponent_ng_container_27_Template_button_click_1_listener", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "onLessonAcceptedClicked", "ɵɵelement", "AwaitingApprovalNotificationComponent_button_28_Template_button_click_0_listener", "_r4", "onLessonRescheduleClicked", "AwaitingApprovalNotificationComponent", "constructor", "generalService", "classroomService", "toastService", "calendarService", "authService", "router", "subs", "UserRoles", "role", "NONE", "ngOnInit", "console", "log", "lesson", "ngOnDestroy", "unsubscribe", "params", "lessonId", "add", "acceptLessonRequest", "subscribe", "res", "setShowToastmessage", "severity", "summary", "detail", "setToggleOverlayListener", "setUpdateListener", "onLessonRejectedClicked", "rejectLessonRequest", "navigationExtras", "queryParams", "JSON", "stringify", "navigate", "_", "ɵɵdirectiveInject", "i1", "GeneralService", "i2", "ClassroomService", "i3", "ToastService", "i4", "CalendarService", "i5", "AuthService", "i6", "Router", "_2", "selectors", "inputs", "index", "time", "date", "duration", "avatar", "decls", "vars", "consts", "template", "AwaitingApprovalNotificationComponent_Template", "rf", "ctx", "AwaitingApprovalNotificationComponent_Template_img_error_4_listener", "$event", "_r1", "setDefaultUserAvatar", "ɵɵtemplate", "AwaitingApprovalNotificationComponent_ng_container_6_Template", "AwaitingApprovalNotificationComponent_ng_template_7_Template", "ɵɵtemplateRefExtractor", "AwaitingApprovalNotificationComponent_ng_container_27_Template", "AwaitingApprovalNotificationComponent_button_28_Template", "AwaitingApprovalNotificationComponent_Template_button_click_29_listener", "ɵɵproperty", "getDomainFileNamePath", "ɵɵsanitizeUrl", "TEACHER", "studentBlock_r5", "isStudent"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\calendar\\overlay-panel-block\\awaiting-approval-notification\\awaiting-approval-notification.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\calendar\\overlay-panel-block\\awaiting-approval-notification\\awaiting-approval-notification.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\r\nimport { NavigationExtras, Router } from '@angular/router';\r\nimport { Lesson } from 'src/app/core/models/lesson.model';\r\nimport { UserRole } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { CalendarService } from 'src/app/core/services/calendar.service';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { ToastService } from 'src/app/core/services/toast.service';\r\nimport { SubSink } from 'subsink';\r\n\r\n@Component({\r\n  selector: 'app-awaiting-approval-notification',\r\n  templateUrl: './awaiting-approval-notification.component.html',\r\n  styleUrls: ['./awaiting-approval-notification.component.scss']\r\n})\r\nexport class AwaitingApprovalNotificationComponent implements OnInit {\r\n  subs = new SubSink();\r\n  UserRoles = UserRole;\r\n  @Input() index!: number;\r\n  @Input() lessonId!: number;\r\n  @Input() time!: string;\r\n  @Input() date!: string;\r\n  @Input() duration!: number;\r\n  @Input() requestedName!: string;\r\n  @Input() avatar!: string;\r\n  @Input() role: UserRole = UserRole.NONE;\r\n  @Input() lesson!: Lesson;\r\n\r\n  constructor(\r\n    public generalService: GeneralService,\r\n    public classroomService: ClassroomService,\r\n    public toastService: ToastService,\r\n    public calendarService: CalendarService,\r\n    public authService: AuthService,\r\n    public router: Router,\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    console.log(this.lesson);\r\n  }\r\n\r\n\r\n  ngOnDestroy(): void {\r\n    this.subs.unsubscribe();\r\n  }\r\n\r\n  onLessonAcceptedClicked() {\r\n    let params = {\r\n      lessonId: this.lessonId\r\n    }\r\n    console.log(params);\r\n    this.subs.add(this.classroomService.acceptLessonRequest(this.lessonId).subscribe((res) => {\r\n      if (res) {\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'success',\r\n          summary: '',\r\n          detail: 'Lesson request accepted.'\r\n        });\r\n        this.calendarService.setToggleOverlayListener(true);\r\n        this.calendarService.setUpdateListener(true);\r\n      }\r\n    }));\r\n  }\r\n\r\n  onLessonRejectedClicked() {\r\n    let params = {\r\n      lessonId: this.lessonId\r\n    }\r\n    console.log(params);\r\n    this.subs.add(this.classroomService.rejectLessonRequest(this.lessonId).subscribe((res) => {\r\n      if (res) {\r\n        this.toastService.setShowToastmessage({\r\n          severity: 'success',\r\n          summary: '',\r\n          detail: 'Lesson request rejected.'\r\n        });\r\n        this.calendarService.setToggleOverlayListener(true);\r\n        this.calendarService.setUpdateListener(true);\r\n      }\r\n    }));\r\n  }\r\n\r\n  onLessonRescheduleClicked() {\r\n    const navigationExtras: NavigationExtras = {\r\n      queryParams: { 'lesson': JSON.stringify(this.lesson), 'reschedule': true, 'answered': false }\r\n    };\r\n    this.router.navigate(['/dashboard/calendar/booking-system'], navigationExtras);\r\n  }\r\n\r\n}\r\n", "<div class=\"grid my-1 grid-nogutter\">\r\n    <div class=\"col-2 circle-num-gradient\">{{ index }}</div>\r\n    <div class=\"col avatar mb-1\">\r\n        <img [src]=\"generalService.getDomainFileNamePath(avatar)\"\r\n        (error)=\"generalService.setDefaultUserAvatar($event)\" class=\"avatarImg\" alt=\"\">\r\n    </div>\r\n</div>\r\n<ng-container>\r\n    <ng-container *ngIf=\"role === UserRoles.TEACHER; else studentBlock\">\r\n        <div class=\"grid justify-content-center font-sm\">\r\n        Requested from: <span class=\"font-semibold\">{{ requestedName }}</span>\r\n        </div>\r\n    </ng-container>\r\n    <ng-template #studentBlock>\r\n        <div class=\"grid justify-content-center font-sm\">\r\n        Requested to: <span class=\"font-semibold\">{{ requestedName }}</span>\r\n        </div>\r\n    </ng-template>\r\n</ng-container>\r\n<div class=\"col py-1\">\r\n    <div class=\"flex font-sm justify-content-center\">\r\n        <div class=\"flex flex-col mr-1\">\r\n            <span><img src=\"/assets/icons/calendar-sm.svg\" class=\"w-10\"></span>\r\n            <span>{{ date }}</span>\r\n        </div>\r\n        <div class=\"flex flex-col mr-1\">\r\n            <span><img src=\"/assets/icons/clock-blue.svg\" class=\"w-10\"></span>\r\n            <span>{{ time }}</span>\r\n        </div>\r\n        <div class=\"flex flex-col\">\r\n            <span><img src=\"/assets/icons/hourglass.svg\" class=\"w-10 mt-3px\"></span>\r\n            <span>{{ duration }}</span>\r\n        </div>\r\n    </div>\r\n</div>\r\n<div class=\"flex justify-content-evenly gap-2\">\r\n    <ng-container *ngIf=\"role === UserRoles.TEACHER\">\r\n    <button pbutton=\"\" (click)=\"onLessonAcceptedClicked()\" type=\"button\" class=\"p-element btn-accept p-button-outlined p-button-rounded button-outlined p-button-xs p-button p-component py-1\">\r\n        <div class=\"flex align-items-center gap-1 jus ju\">\r\n            <span class=\"font-xs\">ACCEPT</span>\r\n            <img src=\"/assets/icons/check-white.svg\" class=\"icon-btn\">\r\n        </div>\r\n    </button>\r\n    </ng-container>\r\n    <button pbutton=\"\" *ngIf=\"authService.isStudent\"  (click)=\"onLessonRescheduleClicked()\" type=\"button\" class=\"p-element p-button-outlined p-button-rounded button-outlined p-button-xs p-button p-component py-1\">\r\n        <div class=\"flex align-items-center gap-1 jus ju\">\r\n            <span class=\"font-xs\">RESCHEDULE</span>\r\n            <img src=\"/assets/icons/date.svg\" class=\"icon-btn\">\r\n        </div>\r\n    </button>\r\n    <button pbutton=\"\" (click)=\"onLessonRejectedClicked()\" type=\"button\" \r\n    class=\"p-element p-button-outlined p-button-rounded button-outlined p-button-xs p-button p-component py-1 btn-blue\">\r\n        <div  class=\"flex align-items-center gap-1\">\r\n            <span class=\"font-xs\">REMOVE</span>\r\n            <i class=\"pi pi-times icon-btn\"></i>\r\n        </div>\r\n    </button>\r\n</div>\r\n<hr>"], "mappings": "AAGA,SAASA,QAAQ,QAAQ,gCAAgC;AAMzD,SAASC,OAAO,QAAQ,SAAS;;;;;;;;;;;ICD7BC,EAAA,CAAAC,uBAAA,GAAoE;IAChED,EAAA,CAAAE,cAAA,cAAiD;IACjDF,EAAA,CAAAG,MAAA,wBAAgB;IAAAH,EAAA,CAAAE,cAAA,eAA4B;IAAAF,EAAA,CAAAG,MAAA,GAAmB;IAC/DH,EAD+D,CAAAI,YAAA,EAAO,EAChE;;;;;IADsCJ,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,aAAA,CAAmB;;;;;IAI/DR,EAAA,CAAAE,cAAA,cAAiD;IACjDF,EAAA,CAAAG,MAAA,sBAAc;IAAAH,EAAA,CAAAE,cAAA,eAA4B;IAAAF,EAAA,CAAAG,MAAA,GAAmB;IAC7DH,EAD6D,CAAAI,YAAA,EAAO,EAC9D;;;;IADoCJ,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,aAAA,CAAmB;;;;;;IAqBjER,EAAA,CAAAC,uBAAA,GAAiD;IACjDD,EAAA,CAAAE,cAAA,iBAA2L;IAAxKF,EAAA,CAAAS,UAAA,mBAAAC,uFAAA;MAAAV,EAAA,CAAAW,aAAA,CAAAC,GAAA;MAAA,MAAAL,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAQ,uBAAA,EAAyB;IAAA,EAAC;IAE9Cf,EADJ,CAAAE,cAAA,cAAkD,eACxB;IAAAF,EAAA,CAAAG,MAAA,aAAM;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACnCJ,EAAA,CAAAgB,SAAA,cAA0D;IAElEhB,EADI,CAAAI,YAAA,EAAM,EACD;;;;;;;IAETJ,EAAA,CAAAE,cAAA,iBAAiN;IAA/JF,EAAA,CAAAS,UAAA,mBAAAQ,iFAAA;MAAAjB,EAAA,CAAAW,aAAA,CAAAO,GAAA;MAAA,MAAAX,MAAA,GAAAP,EAAA,CAAAa,aAAA;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAASP,MAAA,CAAAY,yBAAA,EAA2B;IAAA,EAAC;IAE/EnB,EADJ,CAAAE,cAAA,cAAkD,eACxB;IAAAF,EAAA,CAAAG,MAAA,iBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACvCJ,EAAA,CAAAgB,SAAA,cAAmD;IAE3DhB,EADI,CAAAI,YAAA,EAAM,EACD;;;ADjCb,OAAM,MAAOgB,qCAAqC;EAahDC,YACSC,cAA8B,EAC9BC,gBAAkC,EAClCC,YAA0B,EAC1BC,eAAgC,EAChCC,WAAwB,EACxBC,MAAc;IALd,KAAAL,cAAc,GAAdA,cAAc;IACd,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAlBf,KAAAC,IAAI,GAAG,IAAI7B,OAAO,EAAE;IACpB,KAAA8B,SAAS,GAAG/B,QAAQ;IAQX,KAAAgC,IAAI,GAAahC,QAAQ,CAACiC,IAAI;EAUnC;EAEJC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACC,MAAM,CAAC;EAC1B;EAGAC,WAAWA,CAAA;IACT,IAAI,CAACR,IAAI,CAACS,WAAW,EAAE;EACzB;EAEAtB,uBAAuBA,CAAA;IACrB,IAAIuB,MAAM,GAAG;MACXC,QAAQ,EAAE,IAAI,CAACA;KAChB;IACDN,OAAO,CAACC,GAAG,CAACI,MAAM,CAAC;IACnB,IAAI,CAACV,IAAI,CAACY,GAAG,CAAC,IAAI,CAACjB,gBAAgB,CAACkB,mBAAmB,CAAC,IAAI,CAACF,QAAQ,CAAC,CAACG,SAAS,CAAEC,GAAG,IAAI;MACvF,IAAIA,GAAG,EAAE;QACP,IAAI,CAACnB,YAAY,CAACoB,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAACtB,eAAe,CAACuB,wBAAwB,CAAC,IAAI,CAAC;QACnD,IAAI,CAACvB,eAAe,CAACwB,iBAAiB,CAAC,IAAI,CAAC;MAC9C;IACF,CAAC,CAAC,CAAC;EACL;EAEAC,uBAAuBA,CAAA;IACrB,IAAIZ,MAAM,GAAG;MACXC,QAAQ,EAAE,IAAI,CAACA;KAChB;IACDN,OAAO,CAACC,GAAG,CAACI,MAAM,CAAC;IACnB,IAAI,CAACV,IAAI,CAACY,GAAG,CAAC,IAAI,CAACjB,gBAAgB,CAAC4B,mBAAmB,CAAC,IAAI,CAACZ,QAAQ,CAAC,CAACG,SAAS,CAAEC,GAAG,IAAI;MACvF,IAAIA,GAAG,EAAE;QACP,IAAI,CAACnB,YAAY,CAACoB,mBAAmB,CAAC;UACpCC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,EAAE;UACXC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAACtB,eAAe,CAACuB,wBAAwB,CAAC,IAAI,CAAC;QACnD,IAAI,CAACvB,eAAe,CAACwB,iBAAiB,CAAC,IAAI,CAAC;MAC9C;IACF,CAAC,CAAC,CAAC;EACL;EAEA9B,yBAAyBA,CAAA;IACvB,MAAMiC,gBAAgB,GAAqB;MACzCC,WAAW,EAAE;QAAE,QAAQ,EAAEC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACpB,MAAM,CAAC;QAAE,YAAY,EAAE,IAAI;QAAE,UAAU,EAAE;MAAK;KAC5F;IACD,IAAI,CAACR,MAAM,CAAC6B,QAAQ,CAAC,CAAC,oCAAoC,CAAC,EAAEJ,gBAAgB,CAAC;EAChF;EAAC,QAAAK,CAAA,G;qBAxEUrC,qCAAqC,EAAApB,EAAA,CAAA0D,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA5D,EAAA,CAAA0D,iBAAA,CAAAG,EAAA,CAAAC,gBAAA,GAAA9D,EAAA,CAAA0D,iBAAA,CAAAK,EAAA,CAAAC,YAAA,GAAAhE,EAAA,CAAA0D,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAlE,EAAA,CAAA0D,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAApE,EAAA,CAAA0D,iBAAA,CAAAW,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;UAArCnD,qCAAqC;IAAAoD,SAAA;IAAAC,MAAA;MAAAC,KAAA;MAAAnC,QAAA;MAAAoC,IAAA;MAAAC,IAAA;MAAAC,QAAA;MAAArE,aAAA;MAAAsE,MAAA;MAAAhD,IAAA;MAAAK,MAAA;IAAA;IAAA4C,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+CAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;QCf9CpF,EADJ,CAAAE,cAAA,aAAqC,aACM;QAAAF,EAAA,CAAAG,MAAA,GAAW;QAAAH,EAAA,CAAAI,YAAA,EAAM;QAEpDJ,EADJ,CAAAE,cAAA,aAA6B,aAEsD;QAA/EF,EAAA,CAAAS,UAAA,mBAAA6E,oEAAAC,MAAA;UAAAvF,EAAA,CAAAW,aAAA,CAAA6E,GAAA;UAAA,OAAAxF,EAAA,CAAAc,WAAA,CAASuE,GAAA,CAAA/D,cAAA,CAAAmE,oBAAA,CAAAF,MAAA,CAA2C;QAAA,EAAC;QAE7DvF,EAHQ,CAAAI,YAAA,EAC+E,EAC7E,EACJ;QACNJ,EAAA,CAAAC,uBAAA,GAAc;QAMVD,EALA,CAAA0F,UAAA,IAAAC,6DAAA,0BAAoE,IAAAC,4DAAA,gCAAA5F,EAAA,CAAA6F,sBAAA,CAKzC;;QASnB7F,EAHZ,CAAAE,cAAA,aAAsB,cAC+B,cACb,YACtB;QAAAF,EAAA,CAAAgB,SAAA,cAAsD;QAAAhB,EAAA,CAAAI,YAAA,EAAO;QACnEJ,EAAA,CAAAE,cAAA,YAAM;QAAAF,EAAA,CAAAG,MAAA,IAAU;QACpBH,EADoB,CAAAI,YAAA,EAAO,EACrB;QAEFJ,EADJ,CAAAE,cAAA,cAAgC,YACtB;QAAAF,EAAA,CAAAgB,SAAA,eAAqD;QAAAhB,EAAA,CAAAI,YAAA,EAAO;QAClEJ,EAAA,CAAAE,cAAA,YAAM;QAAAF,EAAA,CAAAG,MAAA,IAAU;QACpBH,EADoB,CAAAI,YAAA,EAAO,EACrB;QAEFJ,EADJ,CAAAE,cAAA,eAA2B,YACjB;QAAAF,EAAA,CAAAgB,SAAA,eAA2D;QAAAhB,EAAA,CAAAI,YAAA,EAAO;QACxEJ,EAAA,CAAAE,cAAA,YAAM;QAAAF,EAAA,CAAAG,MAAA,IAAc;QAGhCH,EAHgC,CAAAI,YAAA,EAAO,EACzB,EACJ,EACJ;QACNJ,EAAA,CAAAE,cAAA,eAA+C;QAS3CF,EARA,CAAA0F,UAAA,KAAAI,8DAAA,2BAAiD,KAAAC,wDAAA,qBAQgK;QAMjN/F,EAAA,CAAAE,cAAA,kBACoH;QADjGF,EAAA,CAAAS,UAAA,mBAAAuF,wEAAA;UAAAhG,EAAA,CAAAW,aAAA,CAAA6E,GAAA;UAAA,OAAAxF,EAAA,CAAAc,WAAA,CAASuE,GAAA,CAAAnC,uBAAA,EAAyB;QAAA,EAAC;QAG9ClD,EADJ,CAAAE,cAAA,eAA4C,gBAClB;QAAAF,EAAA,CAAAG,MAAA,cAAM;QAAAH,EAAA,CAAAI,YAAA,EAAO;QACnCJ,EAAA,CAAAgB,SAAA,aAAoC;QAGhDhB,EAFQ,CAAAI,YAAA,EAAM,EACD,EACP;QACNJ,EAAA,CAAAgB,SAAA,UAAI;;;;QAzDuChB,EAAA,CAAAK,SAAA,GAAW;QAAXL,EAAA,CAAAM,iBAAA,CAAA+E,GAAA,CAAAX,KAAA,CAAW;QAEzC1E,EAAA,CAAAK,SAAA,GAAoD;QAApDL,EAAA,CAAAiG,UAAA,QAAAZ,GAAA,CAAA/D,cAAA,CAAA4E,qBAAA,CAAAb,GAAA,CAAAP,MAAA,GAAA9E,EAAA,CAAAmG,aAAA,CAAoD;QAK9CnG,EAAA,CAAAK,SAAA,GAAkC;QAAAL,EAAlC,CAAAiG,UAAA,SAAAZ,GAAA,CAAAvD,IAAA,KAAAuD,GAAA,CAAAxD,SAAA,CAAAuE,OAAA,CAAkC,aAAAC,eAAA,CAAiB;QAepDrG,EAAA,CAAAK,SAAA,GAAU;QAAVL,EAAA,CAAAM,iBAAA,CAAA+E,GAAA,CAAAT,IAAA,CAAU;QAIV5E,EAAA,CAAAK,SAAA,GAAU;QAAVL,EAAA,CAAAM,iBAAA,CAAA+E,GAAA,CAAAV,IAAA,CAAU;QAIV3E,EAAA,CAAAK,SAAA,GAAc;QAAdL,EAAA,CAAAM,iBAAA,CAAA+E,GAAA,CAAAR,QAAA,CAAc;QAKb7E,EAAA,CAAAK,SAAA,GAAgC;QAAhCL,EAAA,CAAAiG,UAAA,SAAAZ,GAAA,CAAAvD,IAAA,KAAAuD,GAAA,CAAAxD,SAAA,CAAAuE,OAAA,CAAgC;QAQ3BpG,EAAA,CAAAK,SAAA,EAA2B;QAA3BL,EAAA,CAAAiG,UAAA,SAAAZ,GAAA,CAAA3D,WAAA,CAAA4E,SAAA,CAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}