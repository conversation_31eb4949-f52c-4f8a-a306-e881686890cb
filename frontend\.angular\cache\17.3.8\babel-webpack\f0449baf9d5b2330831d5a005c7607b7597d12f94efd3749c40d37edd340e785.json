{"ast": null, "code": "import { BehaviorSubject } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { environment } from 'src/environments/environment';\nimport { Level } from '../models/classroom.model';\nimport { CircleType } from '../models/goal.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./user.service\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"./general.service\";\nconst BACKEND_URL = environment.apiUrl;\nconst BACKEND_URL_LMS = environment.apiUrl + \"/LMS\";\nexport class GoalService {\n  constructor(userService, http, generalService) {\n    this.userService = userService;\n    this.http = http;\n    this.generalService = generalService;\n    this.updateListener$ = new BehaviorSubject(false);\n    this.updateListener = this.updateListener$.asObservable();\n  }\n  setUpdateListener(listen) {\n    this.updateListener$.next(true);\n  }\n  getGoalLevelsInCircle(studentGoals, elForWidth) {\n    let circleLines = new Map();\n    for (let goal of studentGoals) {\n      let startingLevel = goal.startingLevel;\n      let currentLevel = goal.currentLevel;\n      let desiredLevel = goal.desiredLevel;\n      let levels = [Level.A1, Level.A2, Level.B1, Level.B2, Level.C1, Level.C2];\n      let startingLevelIndex = levels.findIndex(el => el === startingLevel);\n      let currentLevelIndex = levels.findIndex(el => el === currentLevel);\n      let desiredLevelIndex = levels.findIndex(el => el === desiredLevel);\n      let newCircleLines = [];\n      for (let i = startingLevelIndex; i <= currentLevelIndex; i++) {\n        let lineStyle = i == currentLevelIndex ? '1px dashed lightgray' : '1px solid lightgray';\n        let circleLine = {\n          text: levels[i],\n          lineStyle,\n          background: \"#a4a2e6\",\n          color: \"white\",\n          type: CircleType.GENERAL\n        };\n        newCircleLines.push(circleLine);\n        circleLines.set(goal.id, newCircleLines);\n      }\n      for (let i = currentLevelIndex + 1; i <= desiredLevelIndex; i++) {\n        let circleLine = {\n          text: levels[i],\n          lineStyle: '1px dashed lightgray',\n          background: \"white\",\n          color: \"#a4a2e6\",\n          type: CircleType.GENERAL\n        };\n        newCircleLines.push(circleLine);\n        circleLines.set(goal.id, newCircleLines);\n      }\n      for (let [key, value] of circleLines) {\n        for (let val of value) {\n          val.width = document.getElementById(elForWidth).clientWidth / (value.length - 1) - 21 + 'px';\n        }\n      }\n    }\n    return circleLines;\n  }\n  createGoal(goal) {\n    let studentGoal = {\n      studentId: goal.studentId,\n      startingLevel: goal.startingLevel,\n      currentLevel: goal.currentLevel,\n      desiredLevel: goal.desiredLevel,\n      desiredAchieveDate: this.generalService.toIsoString(goal.desiredAchieveDate),\n      hoursWeekly: goal.hoursWeekly,\n      // totalWeeks: goal.totalWeeks,\n      language: goal.language,\n      teacherId: goal.teacher.aspUserId,\n      // classroom: goal.classroom?.id,\n      reasons: goal.reasons,\n      areas: goal.areas\n    };\n    console.log(studentGoal);\n    return this.http.post(BACKEND_URL + \"/User/CreateStudentGoal\", studentGoal);\n  }\n  getGoals() {\n    return this.http.get(BACKEND_URL + \"/User/GetStudentGoals\").pipe(map(goals => goals.map(g => {\n      g.desiredAchieveDate = new Date(g.desiredAchieveDate);\n      return g;\n    })));\n  }\n  getClassroomGoals() {\n    return this.http.get(BACKEND_URL + \"/User/GetStudentGoals\");\n  }\n  updateGoal(newGoal, id) {\n    let goalToSend = {\n      startingLevel: newGoal.startingLevel,\n      currentLevel: newGoal.currentLevel,\n      desiredLevel: newGoal.desiredLevel,\n      desiredAchieveDate: this.generalService.toIsoString(newGoal.desiredAchieveDate),\n      hoursWeekly: newGoal.hoursWeekly,\n      // totalWeeks: newGoal.totalWeeks,\n      language: newGoal.language,\n      reasons: newGoal.reasons,\n      areas: newGoal.areas\n    };\n    return this.http.post(BACKEND_URL + \"/User/UpdateStudentGoal?studentGoalId=\" + id, goalToSend);\n  }\n  static #_ = this.ɵfac = function GoalService_Factory(t) {\n    return new (t || GoalService)(i0.ɵɵinject(i1.UserService), i0.ɵɵinject(i2.HttpClient), i0.ɵɵinject(i3.GeneralService));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: GoalService,\n    factory: GoalService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["BehaviorSubject", "map", "environment", "Level", "CircleType", "BACKEND_URL", "apiUrl", "BACKEND_URL_LMS", "GoalService", "constructor", "userService", "http", "generalService", "updateListener$", "updateListener", "asObservable", "setUpdateListener", "listen", "next", "getGoalLevelsInCircle", "studentGoals", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "circleLines", "Map", "goal", "startingLevel", "currentLevel", "desiredLevel", "levels", "A1", "A2", "B1", "B2", "C1", "C2", "startingLevelIndex", "findIndex", "el", "currentLevelIndex", "desiredLevelIndex", "newCircleLines", "i", "lineStyle", "circleLine", "text", "background", "color", "type", "GENERAL", "push", "set", "id", "key", "value", "val", "width", "document", "getElementById", "clientWidth", "length", "createGoal", "studentGoal", "studentId", "desiredAchieveDate", "toIsoString", "hoursWeekly", "language", "teacherId", "teacher", "aspUserId", "reasons", "areas", "console", "log", "post", "getGoals", "get", "pipe", "goals", "g", "Date", "getClassroomGoals", "updateGoal", "newGoal", "goalToSend", "_", "i0", "ɵɵinject", "i1", "UserService", "i2", "HttpClient", "i3", "GeneralService", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\services\\goal.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { environment } from 'src/environments/environment';\r\nimport { Level } from '../models/classroom.model';\r\nimport { CircleLine, CircleType, GoalReason, StudentGoal } from '../models/goal.model';\r\nimport { Category } from '../models/homework.model';\r\nimport { GeneralService } from './general.service';\r\nimport { UserService } from './user.service';\r\nconst BACKEND_URL = environment.apiUrl\r\nconst BACKEND_URL_LMS = environment.apiUrl + \"/LMS\";\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class GoalService {\r\n\r\n  private updateListener$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);\r\n  public readonly updateListener: Observable<boolean> = this.updateListener$.asObservable();\r\n\r\n  constructor(\r\n    private userService: UserService,\r\n    private http: HttpClient,\r\n    private generalService: GeneralService\r\n  ) { }\r\n\r\n  setUpdateListener(listen: boolean) {\r\n    this.updateListener$.next(true)\r\n  }\r\n\r\n  getGoalLevelsInCircle(studentGoals: StudentGoal[], elForWidth: string) {\r\n    let circleLines: Map<string, CircleLine[]> = new Map();\r\n\r\n    for (let goal of studentGoals) {\r\n      let startingLevel = goal.startingLevel;\r\n      let currentLevel = goal.currentLevel;\r\n      let desiredLevel = goal.desiredLevel;\r\n      let levels: Level[] = [Level.A1, Level.A2, Level.B1, Level.B2, Level.C1, Level.C2];\r\n      let startingLevelIndex = levels.findIndex(el => el === startingLevel);\r\n      let currentLevelIndex = levels.findIndex(el => el === currentLevel);\r\n      let desiredLevelIndex = levels.findIndex(el => el === desiredLevel);\r\n      let newCircleLines: CircleLine[] = [];\r\n      for (let i = startingLevelIndex; i <= currentLevelIndex; i++) {\r\n        let lineStyle = i == currentLevelIndex ? '1px dashed lightgray' : '1px solid lightgray'\r\n        let circleLine: CircleLine = {\r\n          text: levels[i],\r\n          lineStyle,\r\n          background: \"#a4a2e6\",\r\n          color: \"white\",\r\n          type: CircleType.GENERAL\r\n        }\r\n        newCircleLines.push(circleLine)\r\n        circleLines.set(goal.id!, newCircleLines);\r\n      }\r\n      for (let i = currentLevelIndex + 1; i <= desiredLevelIndex; i++) {\r\n        let circleLine: CircleLine = {\r\n          text: levels[i],\r\n          lineStyle: '1px dashed lightgray',\r\n          background: \"white\",\r\n          color: \"#a4a2e6\",\r\n          type: CircleType.GENERAL\r\n        }\r\n        newCircleLines.push(circleLine)\r\n        circleLines.set(goal.id!, newCircleLines);\r\n      }\r\n      for (let [key, value] of circleLines) {\r\n        for (let val of value) {\r\n          val.width = document.getElementById(elForWidth)!.clientWidth / (value.length - 1) - 21 + 'px'\r\n        }\r\n      }\r\n    }\r\n    return circleLines;\r\n  }\r\n\r\n  createGoal(goal: StudentGoal) {\r\n    let studentGoal = {\r\n      studentId: goal.studentId,\r\n      startingLevel: goal.startingLevel,\r\n      currentLevel: goal.currentLevel,\r\n      desiredLevel: goal.desiredLevel,\r\n      desiredAchieveDate: this.generalService.toIsoString(goal.desiredAchieveDate),\r\n      hoursWeekly: goal.hoursWeekly,\r\n      // totalWeeks: goal.totalWeeks,\r\n      language: goal.language,\r\n      teacherId: goal.teacher.aspUserId,\r\n      // classroom: goal.classroom?.id,\r\n      reasons: goal.reasons,\r\n      areas: goal.areas\r\n    }\r\n    console.log(studentGoal)\r\n    return this.http.post(BACKEND_URL + \"/User/CreateStudentGoal\", studentGoal)\r\n  }\r\n\r\n  getGoals(): Observable<StudentGoal[]> {\r\n    return this.http.get<StudentGoal[]>(BACKEND_URL + \"/User/GetStudentGoals\").pipe(\r\n      map(goals => goals.map(g => {\r\n        g.desiredAchieveDate = new Date(g.desiredAchieveDate)\r\n        return g\r\n      }))\r\n    )\r\n  }\r\n  public getClassroomGoals(): Observable<StudentGoal[]> {\r\n    return this.http.get<StudentGoal[]>(BACKEND_URL + \"/User/GetStudentGoals\");\r\n  }\r\n  updateGoal(newGoal: StudentGoal, id: string) {\r\n    let goalToSend = {\r\n      startingLevel: newGoal.startingLevel,\r\n      currentLevel: newGoal.currentLevel,\r\n      desiredLevel: newGoal.desiredLevel,\r\n      desiredAchieveDate: this.generalService.toIsoString(newGoal.desiredAchieveDate),\r\n      hoursWeekly: newGoal.hoursWeekly,\r\n      // totalWeeks: newGoal.totalWeeks,\r\n      language: newGoal.language,\r\n      reasons: newGoal.reasons,\r\n      areas: newGoal.areas\r\n    }\r\n    return this.http.post(BACKEND_URL + \"/User/UpdateStudentGoal?studentGoalId=\" + id, goalToSend)\r\n  }\r\n}\r\n"], "mappings": "AAEA,SAASA,eAAe,QAAoB,MAAM;AAClD,SAASC,GAAG,QAAQ,gBAAgB;AACpC,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAASC,KAAK,QAAQ,2BAA2B;AACjD,SAAqBC,UAAU,QAAiC,sBAAsB;;;;;AAItF,MAAMC,WAAW,GAAGH,WAAW,CAACI,MAAM;AACtC,MAAMC,eAAe,GAAGL,WAAW,CAACI,MAAM,GAAG,MAAM;AAKnD,OAAM,MAAOE,WAAW;EAKtBC,YACUC,WAAwB,EACxBC,IAAgB,EAChBC,cAA8B;IAF9B,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,cAAc,GAAdA,cAAc;IANhB,KAAAC,eAAe,GAA6B,IAAIb,eAAe,CAAU,KAAK,CAAC;IACvE,KAAAc,cAAc,GAAwB,IAAI,CAACD,eAAe,CAACE,YAAY,EAAE;EAMrF;EAEJC,iBAAiBA,CAACC,MAAe;IAC/B,IAAI,CAACJ,eAAe,CAACK,IAAI,CAAC,IAAI,CAAC;EACjC;EAEAC,qBAAqBA,CAACC,YAA2B,EAAEC,UAAkB;IACnE,IAAIC,WAAW,GAA8B,IAAIC,GAAG,EAAE;IAEtD,KAAK,IAAIC,IAAI,IAAIJ,YAAY,EAAE;MAC7B,IAAIK,aAAa,GAAGD,IAAI,CAACC,aAAa;MACtC,IAAIC,YAAY,GAAGF,IAAI,CAACE,YAAY;MACpC,IAAIC,YAAY,GAAGH,IAAI,CAACG,YAAY;MACpC,IAAIC,MAAM,GAAY,CAACzB,KAAK,CAAC0B,EAAE,EAAE1B,KAAK,CAAC2B,EAAE,EAAE3B,KAAK,CAAC4B,EAAE,EAAE5B,KAAK,CAAC6B,EAAE,EAAE7B,KAAK,CAAC8B,EAAE,EAAE9B,KAAK,CAAC+B,EAAE,CAAC;MAClF,IAAIC,kBAAkB,GAAGP,MAAM,CAACQ,SAAS,CAACC,EAAE,IAAIA,EAAE,KAAKZ,aAAa,CAAC;MACrE,IAAIa,iBAAiB,GAAGV,MAAM,CAACQ,SAAS,CAACC,EAAE,IAAIA,EAAE,KAAKX,YAAY,CAAC;MACnE,IAAIa,iBAAiB,GAAGX,MAAM,CAACQ,SAAS,CAACC,EAAE,IAAIA,EAAE,KAAKV,YAAY,CAAC;MACnE,IAAIa,cAAc,GAAiB,EAAE;MACrC,KAAK,IAAIC,CAAC,GAAGN,kBAAkB,EAAEM,CAAC,IAAIH,iBAAiB,EAAEG,CAAC,EAAE,EAAE;QAC5D,IAAIC,SAAS,GAAGD,CAAC,IAAIH,iBAAiB,GAAG,sBAAsB,GAAG,qBAAqB;QACvF,IAAIK,UAAU,GAAe;UAC3BC,IAAI,EAAEhB,MAAM,CAACa,CAAC,CAAC;UACfC,SAAS;UACTG,UAAU,EAAE,SAAS;UACrBC,KAAK,EAAE,OAAO;UACdC,IAAI,EAAE3C,UAAU,CAAC4C;SAClB;QACDR,cAAc,CAACS,IAAI,CAACN,UAAU,CAAC;QAC/BrB,WAAW,CAAC4B,GAAG,CAAC1B,IAAI,CAAC2B,EAAG,EAAEX,cAAc,CAAC;MAC3C;MACA,KAAK,IAAIC,CAAC,GAAGH,iBAAiB,GAAG,CAAC,EAAEG,CAAC,IAAIF,iBAAiB,EAAEE,CAAC,EAAE,EAAE;QAC/D,IAAIE,UAAU,GAAe;UAC3BC,IAAI,EAAEhB,MAAM,CAACa,CAAC,CAAC;UACfC,SAAS,EAAE,sBAAsB;UACjCG,UAAU,EAAE,OAAO;UACnBC,KAAK,EAAE,SAAS;UAChBC,IAAI,EAAE3C,UAAU,CAAC4C;SAClB;QACDR,cAAc,CAACS,IAAI,CAACN,UAAU,CAAC;QAC/BrB,WAAW,CAAC4B,GAAG,CAAC1B,IAAI,CAAC2B,EAAG,EAAEX,cAAc,CAAC;MAC3C;MACA,KAAK,IAAI,CAACY,GAAG,EAAEC,KAAK,CAAC,IAAI/B,WAAW,EAAE;QACpC,KAAK,IAAIgC,GAAG,IAAID,KAAK,EAAE;UACrBC,GAAG,CAACC,KAAK,GAAGC,QAAQ,CAACC,cAAc,CAACpC,UAAU,CAAE,CAACqC,WAAW,IAAIL,KAAK,CAACM,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,IAAI;QAC/F;MACF;IACF;IACA,OAAOrC,WAAW;EACpB;EAEAsC,UAAUA,CAACpC,IAAiB;IAC1B,IAAIqC,WAAW,GAAG;MAChBC,SAAS,EAAEtC,IAAI,CAACsC,SAAS;MACzBrC,aAAa,EAAED,IAAI,CAACC,aAAa;MACjCC,YAAY,EAAEF,IAAI,CAACE,YAAY;MAC/BC,YAAY,EAAEH,IAAI,CAACG,YAAY;MAC/BoC,kBAAkB,EAAE,IAAI,CAACnD,cAAc,CAACoD,WAAW,CAACxC,IAAI,CAACuC,kBAAkB,CAAC;MAC5EE,WAAW,EAAEzC,IAAI,CAACyC,WAAW;MAC7B;MACAC,QAAQ,EAAE1C,IAAI,CAAC0C,QAAQ;MACvBC,SAAS,EAAE3C,IAAI,CAAC4C,OAAO,CAACC,SAAS;MACjC;MACAC,OAAO,EAAE9C,IAAI,CAAC8C,OAAO;MACrBC,KAAK,EAAE/C,IAAI,CAAC+C;KACb;IACDC,OAAO,CAACC,GAAG,CAACZ,WAAW,CAAC;IACxB,OAAO,IAAI,CAAClD,IAAI,CAAC+D,IAAI,CAACrE,WAAW,GAAG,yBAAyB,EAAEwD,WAAW,CAAC;EAC7E;EAEAc,QAAQA,CAAA;IACN,OAAO,IAAI,CAAChE,IAAI,CAACiE,GAAG,CAAgBvE,WAAW,GAAG,uBAAuB,CAAC,CAACwE,IAAI,CAC7E5E,GAAG,CAAC6E,KAAK,IAAIA,KAAK,CAAC7E,GAAG,CAAC8E,CAAC,IAAG;MACzBA,CAAC,CAAChB,kBAAkB,GAAG,IAAIiB,IAAI,CAACD,CAAC,CAAChB,kBAAkB,CAAC;MACrD,OAAOgB,CAAC;IACV,CAAC,CAAC,CAAC,CACJ;EACH;EACOE,iBAAiBA,CAAA;IACtB,OAAO,IAAI,CAACtE,IAAI,CAACiE,GAAG,CAAgBvE,WAAW,GAAG,uBAAuB,CAAC;EAC5E;EACA6E,UAAUA,CAACC,OAAoB,EAAEhC,EAAU;IACzC,IAAIiC,UAAU,GAAG;MACf3D,aAAa,EAAE0D,OAAO,CAAC1D,aAAa;MACpCC,YAAY,EAAEyD,OAAO,CAACzD,YAAY;MAClCC,YAAY,EAAEwD,OAAO,CAACxD,YAAY;MAClCoC,kBAAkB,EAAE,IAAI,CAACnD,cAAc,CAACoD,WAAW,CAACmB,OAAO,CAACpB,kBAAkB,CAAC;MAC/EE,WAAW,EAAEkB,OAAO,CAAClB,WAAW;MAChC;MACAC,QAAQ,EAAEiB,OAAO,CAACjB,QAAQ;MAC1BI,OAAO,EAAEa,OAAO,CAACb,OAAO;MACxBC,KAAK,EAAEY,OAAO,CAACZ;KAChB;IACD,OAAO,IAAI,CAAC5D,IAAI,CAAC+D,IAAI,CAACrE,WAAW,GAAG,wCAAwC,GAAG8C,EAAE,EAAEiC,UAAU,CAAC;EAChG;EAAC,QAAAC,CAAA,G;qBAtGU7E,WAAW,EAAA8E,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAK,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAXtF,WAAW;IAAAuF,OAAA,EAAXvF,WAAW,CAAAwF,IAAA;IAAAC,UAAA,EAFV;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}