{"ast": null, "code": "import { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nconst BACKEND_URL = environment.apiUrl;\nconst BACKEND_LMS_URL = environment.apiUrl + \"/LMS/\";\nexport class NotificationService {\n  constructor(http) {\n    this.http = http;\n  }\n  create(notification) {\n    return this.http.post(BACKEND_LMS_URL + \"CreateNotification\", notification);\n  }\n  get() {\n    return this.http.get(BACKEND_LMS_URL + \"GetNotifications\");\n  }\n  updateRead(id) {\n    return this.http.post(BACKEND_LMS_URL + \"UpdateNotificationRead?id=\" + id, {});\n  }\n  markAsReadBulk(ids) {\n    return this.http.post(BACKEND_LMS_URL + \"UpdateNotificationsRead\", ids);\n  }\n  delete(id) {\n    return this.http.post(BACKEND_LMS_URL + \"DeleteNotification?id=\" + id, {});\n  }\n  static #_ = this.ɵfac = function NotificationService_Factory(t) {\n    return new (t || NotificationService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: NotificationService,\n    factory: NotificationService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["environment", "BACKEND_URL", "apiUrl", "BACKEND_LMS_URL", "NotificationService", "constructor", "http", "create", "notification", "post", "get", "updateRead", "id", "markAsReadBulk", "ids", "delete", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\services\\notification.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\nimport { NotificationPost } from '../models/notification.model';\r\nconst BACKEND_URL = environment.apiUrl;\r\nconst BACKEND_LMS_URL = environment.apiUrl + \"/LMS/\";\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class NotificationService {\r\n\r\n  constructor(\r\n    private http: HttpClient\r\n  ) { }\r\n\r\n  create(notification: NotificationPost) {\r\n    return this.http.post(BACKEND_LMS_URL + \"CreateNotification\", notification)\r\n  }\r\n\r\n  get() {\r\n    return this.http.get(BACKEND_LMS_URL + \"GetNotifications\")\r\n  }\r\n\r\n  updateRead(id: any) {\r\n    return this.http.post(BACKEND_LMS_URL + \"UpdateNotificationRead?id=\" + id, {})\r\n  }\r\n\r\n  markAsReadBulk(ids: any[]) {\r\n    return this.http.post(BACKEND_LMS_URL + \"UpdateNotificationsRead\", ids)\r\n  }\r\n\r\n  delete(id: number) {\r\n    return this.http.post(BACKEND_LMS_URL + \"DeleteNotification?id=\" + id, {});\r\n  }\r\n}\r\n"], "mappings": "AAGA,SAASA,WAAW,QAAQ,8BAA8B;;;AAE1D,MAAMC,WAAW,GAAGD,WAAW,CAACE,MAAM;AACtC,MAAMC,eAAe,GAAGH,WAAW,CAACE,MAAM,GAAG,OAAO;AAIpD,OAAM,MAAOE,mBAAmB;EAE9BC,YACUC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EACV;EAEJC,MAAMA,CAACC,YAA8B;IACnC,OAAO,IAAI,CAACF,IAAI,CAACG,IAAI,CAACN,eAAe,GAAG,oBAAoB,EAAEK,YAAY,CAAC;EAC7E;EAEAE,GAAGA,CAAA;IACD,OAAO,IAAI,CAACJ,IAAI,CAACI,GAAG,CAACP,eAAe,GAAG,kBAAkB,CAAC;EAC5D;EAEAQ,UAAUA,CAACC,EAAO;IAChB,OAAO,IAAI,CAACN,IAAI,CAACG,IAAI,CAACN,eAAe,GAAG,4BAA4B,GAAGS,EAAE,EAAE,EAAE,CAAC;EAChF;EAEAC,cAAcA,CAACC,GAAU;IACvB,OAAO,IAAI,CAACR,IAAI,CAACG,IAAI,CAACN,eAAe,GAAG,yBAAyB,EAAEW,GAAG,CAAC;EACzE;EAEAC,MAAMA,CAACH,EAAU;IACf,OAAO,IAAI,CAACN,IAAI,CAACG,IAAI,CAACN,eAAe,GAAG,wBAAwB,GAAGS,EAAE,EAAE,EAAE,CAAC;EAC5E;EAAC,QAAAI,CAAA,G;qBAxBUZ,mBAAmB,EAAAa,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAnBjB,mBAAmB;IAAAkB,OAAA,EAAnBlB,mBAAmB,CAAAmB,IAAA;IAAAC,UAAA,EAFlB;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}