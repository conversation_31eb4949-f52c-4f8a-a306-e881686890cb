{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"primeng/radiobutton\";\nconst _c0 = (a0, a1) => ({\n  \"surface-border\": a0,\n  \"border-primary\": a1\n});\nconst _c1 = (a0, a1) => ({\n  \"surface-50 text-600 surface-border\": a0,\n  \"bg-primary-reverse\": a1\n});\nexport class BuyPackageSuggestionBoxComponent {\n  constructor() {\n    this.tier1 = 1;\n    this.tier2 = 1;\n  }\n  static #_ = this.ɵfac = function BuyPackageSuggestionBoxComponent_Factory(t) {\n    return new (t || BuyPackageSuggestionBoxComponent)();\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: BuyPackageSuggestionBoxComponent,\n    selectors: [[\"app-buy-package-suggestion-box\"]],\n    decls: 38,\n    vars: 20,\n    consts: [[1, \"col-12\", \"p-3\"], [1, \"shadow-2\", \"border-round\", \"surface-card\", \"mb-3\", \"h-full\", \"flex-column\", \"justify-content-between\", \"flex\"], [1, \"p-4\"], [1, \"flex\", \"align-items-center\"], [1, \"inline-flex\", \"border-circle\", \"align-items-center\", \"justify-content-center\", \"bg-green-100\", \"mr-3\", 2, \"width\", \"38px\", \"height\", \"38px\"], [1, \"pi\", \"pi-globe\", \"text-xl\", \"text-green-600\"], [1, \"text-900\", \"font-medium\", \"text-2xl\"], [1, \"flex\", \"flex-row\", \"align-items-center\", \"justify-content-center\"], [1, \"surface-card\", \"border-2\", \"p-3\", \"border-round-top\", \"flex\", \"align-items-center\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [\"name\", \"tier1\", \"styleClass\", \"mr-3\", 3, \"ngModelChange\", \"value\", \"ngModel\"], [1, \"mr-4\", \"md:mr-8\"], [1, \"font-medium\", \"mb-1\"], [1, \"text-sm\"], [1, \"border-round\", \"border-1\", \"p-1\", \"ml-auto\", \"flex\", \"flex-nowrap\", 3, \"ngClass\"], [1, \"pi\", \"pi-star-fill\"], [1, \"surface-card\", \"border-2\", \"p-3\", \"flex\", \"align-items-center\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [1, \"pi\", \"pi-star-fill\", \"mr-2\"], [1, \"text-900\", \"my-3\", \"text-xl\", \"font-medium\"], [1, \"mt-0\", \"mb-3\", \"text-700\", \"line-height-3\"], [1, \"px-4\", \"py-3\", \"surface-100\", \"text-right\"], [\"pbutton\", \"\", \"pripple\", \"\", \"icon\", \"pi pi-arrow-right\", \"iconpos\", \"right\", \"label\", \"More\", 1, \"p-element\", \"p-ripple\", \"p-button-rounded\", \"p-button-success\", \"p-button\", \"p-component\"], [\"aria-hidden\", \"true\", 1, \"p-button-icon\", \"p-button-icon-right\", \"pi\", \"pi-arrow-right\"], [1, \"p-button-label\"], [1, \"p-ink\"]],\n    template: function BuyPackageSuggestionBoxComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"span\", 4);\n        i0.ɵɵelement(5, \"i\", 5);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"span\", 6);\n        i0.ɵɵtext(7, \"Card Title\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8);\n        i0.ɵɵlistener(\"click\", function BuyPackageSuggestionBoxComponent_Template_div_click_9_listener() {\n          return ctx.tier1 = 0;\n        });\n        i0.ɵɵelementStart(10, \"p-radioButton\", 9);\n        i0.ɵɵtwoWayListener(\"ngModelChange\", function BuyPackageSuggestionBoxComponent_Template_p_radioButton_ngModelChange_10_listener($event) {\n          i0.ɵɵtwoWayBindingSet(ctx.tier1, $event) || (ctx.tier1 = $event);\n          return $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(11, \"div\", 10)(12, \"div\", 11);\n        i0.ɵɵtext(13, \"Basic\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"span\", 12);\n        i0.ɵɵtext(15, \"Quam.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"div\", 13);\n        i0.ɵɵelement(17, \"i\", 14);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"div\", 15);\n        i0.ɵɵlistener(\"click\", function BuyPackageSuggestionBoxComponent_Template_div_click_18_listener() {\n          return ctx.tier1 = 1;\n        });\n        i0.ɵɵelementStart(19, \"p-radioButton\", 9);\n        i0.ɵɵtwoWayListener(\"ngModelChange\", function BuyPackageSuggestionBoxComponent_Template_p_radioButton_ngModelChange_19_listener($event) {\n          i0.ɵɵtwoWayBindingSet(ctx.tier1, $event) || (ctx.tier1 = $event);\n          return $event;\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"div\", 10)(21, \"div\", 11);\n        i0.ɵɵtext(22, \"Premium\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"span\", 12);\n        i0.ɵɵtext(24, \"Quam .\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(25, \"div\", 13);\n        i0.ɵɵelement(26, \"i\", 16)(27, \"i\", 14);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(28, \"div\", 17);\n        i0.ɵɵtext(29, \"Quam adipiscing vitae proin sagittis.\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"p\", 18);\n        i0.ɵɵtext(31, \"Eget sit amet tellus cras adipiscing enim. At quis risus sed vulputate odio. Proin libero nunc consequat interdum varius sit amet.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(32, \"div\", 19)(33, \"button\", 20);\n        i0.ɵɵelement(34, \"span\", 21);\n        i0.ɵɵelementStart(35, \"span\", 22);\n        i0.ɵɵtext(36, \"More\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(37, \"span\", 23);\n        i0.ɵɵelementEnd()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(8, _c0, ctx.tier1 !== 0, ctx.tier1 === 0));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"value\", 0);\n        i0.ɵɵtwoWayProperty(\"ngModel\", ctx.tier1);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(11, _c1, ctx.tier1 !== 0, ctx.tier1 === 0));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(14, _c0, ctx.tier1 !== 1, ctx.tier1 === 1));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"value\", 1);\n        i0.ɵɵtwoWayProperty(\"ngModel\", ctx.tier1);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(17, _c1, ctx.tier1 !== 1, ctx.tier1 === 1));\n      }\n    },\n    dependencies: [i1.NgClass, i2.NgControlStatus, i2.NgModel, i3.RadioButton],\n    styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9jbGFzc3Jvb20vcGFja2FnZXMvYnV5LXBhY2thZ2UvYnV5LXBhY2thZ2Utc3VnZ2VzdGlvbi1ib3gvYnV5LXBhY2thZ2Utc3VnZ2VzdGlvbi1ib3guY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGNBQWM7QUFDaEIiLCJzb3VyY2VzQ29udGVudCI6WyI6aG9zdCB7XG4gIGRpc3BsYXk6IGJsb2NrO1xufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"],\n    changeDetection: 0\n  });\n}", "map": {"version": 3, "names": ["BuyPackageSuggestionBoxComponent", "constructor", "tier1", "tier2", "_", "_2", "selectors", "decls", "vars", "consts", "template", "BuyPackageSuggestionBoxComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "BuyPackageSuggestionBoxComponent_Template_div_click_9_listener", "ɵɵtwoWayListener", "BuyPackageSuggestionBoxComponent_Template_p_radioButton_ngModelChange_10_listener", "$event", "ɵɵtwoWayBindingSet", "BuyPackageSuggestionBoxComponent_Template_div_click_18_listener", "BuyPackageSuggestionBoxComponent_Template_p_radioButton_ngModelChange_19_listener", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction2", "_c0", "ɵɵtwoWayProperty", "_c1"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\buy-package\\buy-package-suggestion-box\\buy-package-suggestion-box.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\classroom\\packages\\buy-package\\buy-package-suggestion-box\\buy-package-suggestion-box.component.html"], "sourcesContent": ["import { CommonModule } from \"@angular/common\";\nimport { ChangeDetectionStrategy, Component } from '@angular/core';\n\n@Component({\n    selector: 'app-buy-package-suggestion-box',\n    templateUrl: './buy-package-suggestion-box.component.html',\n    styleUrl: './buy-package-suggestion-box.component.css',\n    changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class BuyPackageSuggestionBoxComponent {\n    tier1: number = 1;\n\n    tier2: number = 1;\n }\n", "<div class=\"col-12 p-3\">\r\n    <div class=\"shadow-2 border-round surface-card mb-3 h-full flex-column justify-content-between flex\">\r\n        <div class=\"p-4\">\r\n            <div class=\"flex align-items-center\"><span\r\n                    class=\"inline-flex border-circle align-items-center justify-content-center bg-green-100 mr-3\"\r\n                    style=\"width: 38px; height: 38px;\"><i class=\"pi pi-globe text-xl text-green-600\"></i></span><span\r\n                    class=\"text-900 font-medium text-2xl\">Card Title</span></div>\r\n\r\n            <div class=\" flex flex-row align-items-center justify-content-center \">\r\n                <div class=\"surface-card border-2 p-3 border-round-top flex align-items-center cursor-pointer\"\r\n                    [ngClass]=\"{'surface-border': tier1 !== 0, 'border-primary': tier1 === 0}\" (click)=\"tier1 = 0\">\r\n                    <p-radioButton name=\"tier1\" [value]=\"0\" [(ngModel)]=\"tier1\" styleClass=\"mr-3\"></p-radioButton>\r\n                    <div class=\"mr-4 md:mr-8\">\r\n                        <div class=\"font-medium mb-1\">Basic</div>\r\n                        <span class=\"text-sm\">Quam.</span>\r\n                    </div>\r\n                    <div class=\"border-round border-1 p-1 ml-auto flex flex-nowrap\"\r\n                        [ngClass]=\"{'surface-50 text-600 surface-border': tier1 !== 0, 'bg-primary-reverse': tier1 === 0}\">\r\n                        <i class=\"pi pi-star-fill\"></i>\r\n                    </div>\r\n                </div>\r\n                <div class=\"surface-card border-2 p-3 flex align-items-center cursor-pointer\"\r\n                    [ngClass]=\"{'surface-border': tier1 !== 1, 'border-primary': tier1 === 1}\" (click)=\"tier1 = 1\">\r\n                    <p-radioButton name=\"tier1\" [value]=\"1\" [(ngModel)]=\"tier1\" styleClass=\"mr-3\"></p-radioButton>\r\n                    <div class=\"mr-4 md:mr-8\">\r\n                        <div class=\"font-medium mb-1\">Premium</div>\r\n                        <span class=\"text-sm\">Quam .</span>\r\n                    </div>\r\n                    <div class=\"border-round border-1 p-1 ml-auto flex flex-nowrap\"\r\n                        [ngClass]=\"{'surface-50 text-600 surface-border': tier1 !== 1, 'bg-primary-reverse': tier1 === 1}\">\r\n                        <i class=\"pi pi-star-fill mr-2\"></i>\r\n                        <i class=\"pi pi-star-fill\"></i>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n           \r\n            <div class=\"text-900 my-3 text-xl font-medium\">Quam adipiscing vitae proin sagittis.</div>\r\n            <p class=\"mt-0 mb-3 text-700 line-height-3\">Eget sit amet tellus cras adipiscing enim. At quis risus sed\r\n                vulputate odio. Proin libero nunc consequat interdum varius sit amet.</p>\r\n        </div>\r\n        <div class=\"px-4 py-3 surface-100 text-right\"><button pbutton=\"\" pripple=\"\" icon=\"pi pi-arrow-right\"\r\n                iconpos=\"right\" label=\"More\"\r\n                class=\"p-element p-ripple p-button-rounded p-button-success p-button p-component\"><span\r\n                    class=\"p-button-icon p-button-icon-right pi pi-arrow-right\" aria-hidden=\"true\"></span><span\r\n                    class=\"p-button-label\">More</span><span class=\"p-ink\"></span></button></div>\r\n    </div>\r\n</div>"], "mappings": ";;;;;;;;;;;;AASA,OAAM,MAAOA,gCAAgC;EAN7CC,YAAA;IAOI,KAAAC,KAAK,GAAW,CAAC;IAEjB,KAAAC,KAAK,GAAW,CAAC;;EACnB,QAAAC,CAAA,G;qBAJWJ,gCAAgC;EAAA;EAAA,QAAAK,EAAA,G;UAAhCL,gCAAgC;IAAAM,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,0CAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCNIE,EAHjD,CAAAC,cAAA,aAAwB,aACiF,aAChF,aACwB,cAEM;QAAAD,EAAA,CAAAE,SAAA,WAAkD;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAAAH,EAAA,CAAAC,cAAA,cACtD;QAAAD,EAAA,CAAAI,MAAA,iBAAU;QAAOJ,EAAP,CAAAG,YAAA,EAAO,EAAM;QAGjEH,EADJ,CAAAC,cAAA,aAAuE,aAEgC;QAApBD,EAAA,CAAAK,UAAA,mBAAAC,+DAAA;UAAA,OAAAP,GAAA,CAAAX,KAAA,GAAiB,CAAC;QAAA,EAAC;QAC9FY,EAAA,CAAAC,cAAA,wBAA8E;QAAtCD,EAAA,CAAAO,gBAAA,2BAAAC,kFAAAC,MAAA;UAAAT,EAAA,CAAAU,kBAAA,CAAAX,GAAA,CAAAX,KAAA,EAAAqB,MAAA,MAAAV,GAAA,CAAAX,KAAA,GAAAqB,MAAA;UAAA,OAAAA,MAAA;QAAA,EAAmB;QAAmBT,EAAA,CAAAG,YAAA,EAAgB;QAE1FH,EADJ,CAAAC,cAAA,eAA0B,eACQ;QAAAD,EAAA,CAAAI,MAAA,aAAK;QAAAJ,EAAA,CAAAG,YAAA,EAAM;QACzCH,EAAA,CAAAC,cAAA,gBAAsB;QAAAD,EAAA,CAAAI,MAAA,aAAK;QAC/BJ,EAD+B,CAAAG,YAAA,EAAO,EAChC;QACNH,EAAA,CAAAC,cAAA,eACuG;QACnGD,EAAA,CAAAE,SAAA,aAA+B;QAEvCF,EADI,CAAAG,YAAA,EAAM,EACJ;QACNH,EAAA,CAAAC,cAAA,eACmG;QAApBD,EAAA,CAAAK,UAAA,mBAAAM,gEAAA;UAAA,OAAAZ,GAAA,CAAAX,KAAA,GAAiB,CAAC;QAAA,EAAC;QAC9FY,EAAA,CAAAC,cAAA,wBAA8E;QAAtCD,EAAA,CAAAO,gBAAA,2BAAAK,kFAAAH,MAAA;UAAAT,EAAA,CAAAU,kBAAA,CAAAX,GAAA,CAAAX,KAAA,EAAAqB,MAAA,MAAAV,GAAA,CAAAX,KAAA,GAAAqB,MAAA;UAAA,OAAAA,MAAA;QAAA,EAAmB;QAAmBT,EAAA,CAAAG,YAAA,EAAgB;QAE1FH,EADJ,CAAAC,cAAA,eAA0B,eACQ;QAAAD,EAAA,CAAAI,MAAA,eAAO;QAAAJ,EAAA,CAAAG,YAAA,EAAM;QAC3CH,EAAA,CAAAC,cAAA,gBAAsB;QAAAD,EAAA,CAAAI,MAAA,cAAM;QAChCJ,EADgC,CAAAG,YAAA,EAAO,EACjC;QACNH,EAAA,CAAAC,cAAA,eACuG;QAEnGD,EADA,CAAAE,SAAA,aAAoC,aACL;QAG3CF,EAFQ,CAAAG,YAAA,EAAM,EACJ,EACJ;QAENH,EAAA,CAAAC,cAAA,eAA+C;QAAAD,EAAA,CAAAI,MAAA,6CAAqC;QAAAJ,EAAA,CAAAG,YAAA,EAAM;QAC1FH,EAAA,CAAAC,cAAA,aAA4C;QAAAD,EAAA,CAAAI,MAAA,0IAC6B;QAC7EJ,EAD6E,CAAAG,YAAA,EAAI,EAC3E;QACwCH,EAA9C,CAAAC,cAAA,eAA8C,kBAE4C;QAAAD,EAAA,CAAAE,SAAA,gBACQ;QAAAF,EAAA,CAAAC,cAAA,gBAC/D;QAAAD,EAAA,CAAAI,MAAA,YAAI;QAAAJ,EAAA,CAAAG,YAAA,EAAO;QAAAH,EAAA,CAAAE,SAAA,gBAA2B;QAEjFF,EAFiF,CAAAG,YAAA,EAAS,EAAM,EACtF,EACJ;;;QApCcH,EAAA,CAAAa,SAAA,GAA0E;QAA1Eb,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAe,eAAA,IAAAC,GAAA,EAAAjB,GAAA,CAAAX,KAAA,QAAAW,GAAA,CAAAX,KAAA,QAA0E;QAC9CY,EAAA,CAAAa,SAAA,EAAW;QAAXb,EAAA,CAAAc,UAAA,YAAW;QAACd,EAAA,CAAAiB,gBAAA,YAAAlB,GAAA,CAAAX,KAAA,CAAmB;QAMvDY,EAAA,CAAAa,SAAA,GAAkG;QAAlGb,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAe,eAAA,KAAAG,GAAA,EAAAnB,GAAA,CAAAX,KAAA,QAAAW,GAAA,CAAAX,KAAA,QAAkG;QAKtGY,EAAA,CAAAa,SAAA,GAA0E;QAA1Eb,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAe,eAAA,KAAAC,GAAA,EAAAjB,GAAA,CAAAX,KAAA,QAAAW,GAAA,CAAAX,KAAA,QAA0E;QAC9CY,EAAA,CAAAa,SAAA,EAAW;QAAXb,EAAA,CAAAc,UAAA,YAAW;QAACd,EAAA,CAAAiB,gBAAA,YAAAlB,GAAA,CAAAX,KAAA,CAAmB;QAMvDY,EAAA,CAAAa,SAAA,GAAkG;QAAlGb,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAe,eAAA,KAAAG,GAAA,EAAAnB,GAAA,CAAAX,KAAA,QAAAW,GAAA,CAAAX,KAAA,QAAkG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}