{"ast": null, "code": "import { fork<PERSON><PERSON>n, of } from 'rxjs';\nimport { startWith, switchMap, take } from 'rxjs/operators';\nimport { SubSink } from 'subsink';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/core/services/note.service\";\nimport * as i2 from \"src/app/core/services/note-listeners.service\";\nimport * as i3 from \"src/app/core/services/note-actions.service\";\nimport * as i4 from \"src/app/core/services/classroom.service\";\nimport * as i5 from \"src/app/core/services/auth.service\";\nimport * as i6 from \"src/app/core/services/general.service\";\nimport * as i7 from \"src/app/core/services/layout.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"../../../shared/block-viewer/block-viewer.component\";\nimport * as i10 from \"../../../shared/loader/loader.component\";\nimport * as i11 from \"../../library/library/components/library-left-sidebar/library-left-sidebar.component\";\nimport * as i12 from \"./notes-list/notes-list.component\";\nconst _c0 = [\"mainWrapper\"];\nconst _c1 = [\"leftSide\"];\nconst _c2 = [\"rightSide\"];\nconst _c3 = (a0, a1) => ({\n  expanded: a0,\n  \"w-full\": a1\n});\nconst _c4 = a0 => ({\n  collapsed: a0\n});\nconst _c5 = a0 => ({\n  \"mt-5\": a0\n});\nfunction NotesComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9, 2)(2, \"app-library-left-sidebar\", 10);\n    i0.ɵɵlistener(\"classroomSelected\", function NotesComponent_div_3_Template_app_library_left_sidebar_classroomSelected_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.prepareClassroomNotes($event));\n    })(\"myFilesSelected\", function NotesComponent_div_3_Template_app_library_left_sidebar_myFilesSelected_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.myNotesSelected($event));\n    })(\"collapsed\", function NotesComponent_div_3_Template_app_library_left_sidebar_collapsed_2_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.oncollapseNotesLeftSideChanged($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(6, _c4, ctx_r1.isLeftsideCollapsed));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"classrooms\", ctx_r1.classrooms)(\"leftSideHeight\", ctx_r1.leftSideHeight)(\"showMyNotes\", true)(\"autoSelectFirstClassroom\", ctx_r1.authService.isStudent)(\"firstListItemTitle\", \"My Notes\");\n  }\n}\nfunction NotesComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 11)(1, \"div\", 12);\n    i0.ɵɵelement(2, \"app-loader\", 13);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scale\", 1.2);\n  }\n}\nfunction NotesComponent_ng_container_7_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.authService.isStudent ? \"Notes shared by the teacher\" : \"Classroom notes created by me\", \"\");\n  }\n}\nfunction NotesComponent_ng_container_7_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelement(1, \"app-notes-list\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"height\", ctx_r1.leftSideHeight - 35 + \"px\");\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"viewTitle\", ctx_r1.authService.isStudent ? \"Teacher Notes\" : \"My Notes\");\n    i0.ɵɵproperty(\"withClassroom\", true)(\"notes\", ctx_r1.authService.isTeacher ? ctx_r1.teacherSharedClassStudentNotes : ctx_r1.classNotes)(\"hasAdd\", ctx_r1.authService.isStudent ? false : true)(\"classroom\", ctx_r1.classroom);\n  }\n}\nfunction NotesComponent_ng_container_7_ng_container_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 27);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.authService.isStudent ? \"Notes created by me\" : \"Notes shared by the students\", \" \");\n  }\n}\nfunction NotesComponent_ng_container_7_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28);\n    i0.ɵɵelement(1, \"app-notes-list\", 29);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"height\", ctx_r1.leftSideHeight - 35 + \"px\");\n    i0.ɵɵadvance();\n    i0.ɵɵpropertyInterpolate(\"viewTitle\", ctx_r1.authService.isStudent ? \"My Notes\" : \"Student Notes\");\n    i0.ɵɵproperty(\"withClassroom\", true)(\"notes\", ctx_r1.authService.isStudent ? ctx_r1.notes : ctx_r1.classNotes)(\"hasAdd\", ctx_r1.authService.isStudent ? true : false)(\"classroom\", ctx_r1.classroom);\n  }\n}\nfunction NotesComponent_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 14)(2, \"div\", 15);\n    i0.ɵɵelement(3, \"input\", 16);\n    i0.ɵɵelementStart(4, \"label\", 17);\n    i0.ɵɵelement(5, \"img\", 18);\n    i0.ɵɵelementStart(6, \"div\", 19)(7, \"span\", 20);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, NotesComponent_ng_container_7_ng_container_9_Template, 3, 1, \"ng-container\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 21);\n    i0.ɵɵtemplate(11, NotesComponent_ng_container_7_div_11_Template, 2, 7, \"div\", 22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 15);\n    i0.ɵɵelement(13, \"input\", 23);\n    i0.ɵɵelementStart(14, \"label\", 24);\n    i0.ɵɵelement(15, \"img\", 25);\n    i0.ɵɵelementStart(16, \"div\", 19)(17, \"span\", 20);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(19, NotesComponent_ng_container_7_ng_container_19_Template, 3, 1, \"ng-container\", 8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 26);\n    i0.ɵɵtemplate(21, NotesComponent_ng_container_7_div_21_Template, 2, 7, \"div\", 22);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.authService.isStudent ? \"Teacher Notes\" : \"My Notes\", \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLarge);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.classroomSelected);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.authService.isStudent ? \"My Notes\" : \"Student Notes\", \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isLarge);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.classroomSelected);\n  }\n}\nfunction NotesComponent_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 30);\n    i0.ɵɵelement(2, \"app-notes-list\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"height\", ctx_r1.leftSideHeight + 35 + \"px\");\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c5, ctx_r1.classroomSelected));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"notes\", ctx_r1.notes);\n  }\n}\nexport let NotesComponent = /*#__PURE__*/(() => {\n  class NotesComponent {\n    constructor(noteService, noteListenersService, noteActionsService, classroomService, authService, generalService, layoutService, cdr) {\n      this.noteService = noteService;\n      this.noteListenersService = noteListenersService;\n      this.noteActionsService = noteActionsService;\n      this.classroomService = classroomService;\n      this.authService = authService;\n      this.generalService = generalService;\n      this.layoutService = layoutService;\n      this.cdr = cdr;\n      this.mainWrapper = {};\n      this.leftSide = {};\n      this.rightSide = {};\n      this.isLibrary = false;\n      this.selectedClassroom = {};\n      this.subs = new SubSink();\n      this.user = {};\n      this.role = \"\";\n      this.notes = [];\n      this.classNotes = [];\n      this.classrooms = [];\n      this.classroom = {};\n      this.leftSideHeight = 0;\n      this.classroomSelected = false;\n      this.isMyNotesSelected = true;\n      this.teacherSharedClassStudentNotes = [];\n      this.isLoading = true;\n      this.isLarge = false;\n    }\n    ngOnInit() {\n      this.user = this.authService.getLoggedInUser();\n      this.role = this.authService.getUserRole();\n      this.classrooms = this.classroomService.sessionUserClassrooms;\n      this.subs.sink = this.classroomService.getLMSUserClassrooms(this.user.id).subscribe(res => {\n        this.classrooms = res;\n      });\n      this.initUpdateNoteListeners();\n      this.getNotes();\n      this.subs.add(this.generalService.deviceKind.pipe().subscribe(res => {\n        if (res) {\n          this.isLarge = res.w992up;\n        }\n      }));\n    }\n    ngOnChanges() {\n      if (this.hasSelectedClassroom) {\n        this.prepareClassroomNotes(this.selectedClassroom);\n      }\n    }\n    ngOnDestroy() {\n      this.subs.unsubscribe();\n    }\n    ngAfterContentInit() {\n      this.subs.sink = this.generalService.deviceKind.pipe(take(2), switchMap(res => {\n        console.log(res);\n        if (res.is576 || res.is992) {\n          return of(0);\n        } else {\n          return this.layoutService.sideMenuHeight;\n        }\n      }), startWith(0)).subscribe(height => {\n        if (height !== 0) {\n          this.leftSideHeight = height - 115;\n          if (this.mainWrapper) {\n            // this.mainWrapper.nativeElement.style.height = (height + 80 + 'px');\n          }\n          if (this.leftSide) {\n            this.leftSide.nativeElement.style.maxHeight = height + 'px';\n          }\n          if (this.rightSide) {\n            this.rightSide.nativeElement.style.height = this.classroomSelected ? height - 110 + 'px' : height - 110 + 'px';\n          }\n          this.cdr.detectChanges();\n        } else {\n          this.leftSideHeight = window.innerHeight - 220;\n          this.cdr.detectChanges();\n        }\n      });\n    }\n    get hasSelectedClassroom() {\n      return this.selectedClassroom && Object.keys(this.selectedClassroom).length > 0;\n    }\n    oncollapseNotesLeftSideChanged(event) {\n      console.log(event);\n      this.isLeftsideCollapsed = event;\n    }\n    getNotes() {\n      this.subs.sink = this.noteService.getUserCreatedNotes(this.user.id).subscribe(res => {\n        this.notes = this.filterMyNotesPerRole(res);\n        console.log(this.notes);\n        this.isLoading = false;\n      });\n    }\n    prepareClassroomNotes(classroom) {\n      if (!classroom) {\n        return;\n      }\n      this.isLoading = true;\n      this.isMyNotesSelected = false;\n      const {\n        nativeElement\n      } = this.rightSide;\n      this.classroom = classroom;\n      console.log(classroom);\n      this.classroomSelected = true;\n      this.subs.sink = this.noteService.getClassroomNotes(classroom.id).subscribe(res => {\n        this.classNotes = this.filterClassNotesPerRole(res);\n        this.teacherSharedClassStudentNotes = this.filterClassNotesWhichAreMine(res);\n        this.isLoading = false;\n      });\n      if (this.authService.isStudent) {\n        this.getNotes();\n      }\n    }\n    myNotesSelected(event) {\n      this.classroomSelected = false;\n      this.isMyNotesSelected = true;\n    }\n    initUpdateNoteListeners() {\n      this.subs.sink = this.noteService.updateNoteListener.subscribe(res => {\n        console.log(res);\n        if (res) {\n          const observables = [this.noteService.getUserCreatedNotes(this.user.id)];\n          if (!this.generalService.isNullishObject(this.classroom)) {\n            observables.push(this.noteService.getClassroomNotes(this.classroom.id));\n          }\n          forkJoin(observables).subscribe(([userNotes, classroomNotes]) => {\n            this.notes = this.filterMyNotesPerRole(userNotes);\n            console.log(userNotes);\n            if (classroomNotes) {\n              this.classNotes = this.filterClassNotesPerRole(classroomNotes);\n              this.teacherSharedClassStudentNotes = this.filterClassNotesWhichAreMine(classroomNotes);\n            }\n            this.isLoading = false;\n          });\n        }\n      });\n    }\n    filterClassNotesWhichAreMine(classroomNotes) {\n      return classroomNotes.filter(el => el.createdBy.aspUserId === this.user.aspUserId);\n    }\n    filterClassNotesPerRole(classroomNotes) {\n      if (this.authService.isStudent) {\n        return classroomNotes.filter(el => el.sharedWithUsers.some(user => user.aspUserId === this.user.aspUserId));\n      } else {\n        return classroomNotes.filter(el => el.sharedWithUsers.some(user => user.aspUserId === this.classroom.teacher.aspUserId));\n      }\n    }\n    filterMyNotesPerRole(classroomNotes) {\n      if (this.authService.isStudent) {\n        return classroomNotes.filter(el => el.classroomId === +this.classroom.id);\n      } else {\n        return classroomNotes.filter(el => el.classroomId === 0);\n      }\n    }\n    static #_ = this.ɵfac = function NotesComponent_Factory(t) {\n      return new (t || NotesComponent)(i0.ɵɵdirectiveInject(i1.NoteService), i0.ɵɵdirectiveInject(i2.NoteListenersService), i0.ɵɵdirectiveInject(i3.NoteActionsService), i0.ɵɵdirectiveInject(i4.ClassroomService), i0.ɵɵdirectiveInject(i5.AuthService), i0.ɵɵdirectiveInject(i6.GeneralService), i0.ɵɵdirectiveInject(i7.LayoutService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NotesComponent,\n      selectors: [[\"app-notes\"]],\n      viewQuery: function NotesComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n          i0.ɵɵviewQuery(_c1, 7);\n          i0.ɵɵviewQuery(_c2, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.mainWrapper = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.leftSide = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.rightSide = _t.first);\n        }\n      },\n      inputs: {\n        isLibrary: \"isLibrary\",\n        selectedClassroom: \"selectedClassroom\"\n      },\n      features: [i0.ɵɵNgOnChangesFeature],\n      decls: 9,\n      vars: 10,\n      consts: [[\"mainWrapper\", \"\"], [\"rightSide\", \"\"], [\"leftSide\", \"\"], [\"header\", \"Notes\", \"headerBackgroundImage\", \"/assets/images/library/library-header-bg.png\", \"blockClass\", \"border-radius-bottom-10 sm:mb-6 pb-2\", \"containerClass\", \"bg-white px-1 sm:px-3 py-2\", 3, \"headerClass\", \"headerTextClass\"], [1, \"notes\", \"grid\"], [\"id\", \"notes-left-side\", \"class\", \"notes-left-side\", 3, \"ngClass\", 4, \"ngIf\"], [\"id\", \"notes-right-side\", 1, \"notes-right-side\", \"border-round-3xl\", \"px-1\", \"py-2\", 3, \"ngClass\"], [\"class\", \"relative h-full block-gradient-reverse border-round-3xl flex align-items-center justify-content-center\", 4, \"ngIf\"], [4, \"ngIf\"], [\"id\", \"notes-left-side\", 1, \"notes-left-side\", 3, \"ngClass\"], [3, \"classroomSelected\", \"myFilesSelected\", \"collapsed\", \"classrooms\", \"leftSideHeight\", \"showMyNotes\", \"autoSelectFirstClassroom\", \"firstListItemTitle\"], [1, \"relative\", \"h-full\", \"block-gradient-reverse\", \"border-round-3xl\", \"flex\", \"align-items-center\", \"justify-content-center\"], [1, \"abs-centered\"], [3, \"scale\"], [1, \"tabs\"], [1, \"tab\"], [\"type\", \"radio\", \"name\", \"css-tabss\", \"id\", \"tab-11\", \"checked\", \"\", 1, \"tab-switch\"], [\"for\", \"tab-11\", 1, \"tab-label\", \"flex\", \"align-items-center\", \"sm:gap-2\", \"line-height-1\"], [\"src\", \"/assets/icons/file_manager.png\", 1, \"w-3rem\"], [1, \"flex\", \"align-items-center\", \"gap-3\"], [1, \"font-sm\", \"lg:w-4\"], [1, \"tab-content\", \"border-round-xl\"], [\"class\", \"block-gradient-reverse border-round-3xl px-1 py-2 overflow-y-auto\", 3, \"height\", 4, \"ngIf\"], [\"type\", \"radio\", \"name\", \"css-tabss\", \"id\", \"tab-22\", 1, \"tab-switch\"], [\"for\", \"tab-22\", 1, \"tab-label\", \"flex\", \"align-items-center\", \"sm:gap-2\", \"line-height-1\"], [\"src\", \"/assets/icons/3d_graduation_cap_7.png\", 1, \"w-3rem\"], [1, \"tab-content\"], [1, \"font-xs\"], [1, \"block-gradient-reverse\", \"border-round-3xl\", \"px-1\", \"py-2\", \"overflow-y-auto\"], [3, \"viewTitle\", \"withClassroom\", \"notes\", \"hasAdd\", \"classroom\"], [1, \"block-gradient-reverse\", \"border-round-3xl\", \"px-1\", \"py-2\", \"overflow-y-auto\", 3, \"ngClass\"], [3, \"notes\"]],\n      template: function NotesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"app-block-viewer\", 3)(1, \"div\", 4, 0);\n          i0.ɵɵtemplate(3, NotesComponent_div_3_Template, 3, 8, \"div\", 5);\n          i0.ɵɵelementStart(4, \"div\", 6, 1);\n          i0.ɵɵtemplate(6, NotesComponent_div_6_Template, 3, 1, \"div\", 7)(7, NotesComponent_ng_container_7_Template, 22, 6, \"ng-container\", 8)(8, NotesComponent_ng_container_8_Template, 3, 6, \"ng-container\", 8);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"headerClass\", \"justify-content-center my-2\")(\"headerTextClass\", \"font-xl font-semibold justify-content-center\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.hasSelectedClassroom);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(7, _c3, ctx.isLeftsideCollapsed, ctx.hasSelectedClassroom));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.classroomSelected && !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isMyNotesSelected && !ctx.isLoading);\n        }\n      },\n      dependencies: [i8.NgClass, i8.NgIf, i9.BlockViewerComponent, i10.LoaderComponent, i11.LibraryLeftSidebarComponent, i12.NotesListComponent],\n      styles: [\".responsive-four-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:49%;margin-right:0;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:49%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:49%;margin-right:0;margin-left:1%;float:left}}@media only screen and (min-width: 992px){.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:23.5%;margin-right:1%;margin-left:1%;float:left}.responsive-four-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(4){width:23.5%;margin-right:0;margin-left:1%;float:left}}.responsive-nested-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 992px){.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:32%;margin-right:1%;margin-left:0;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:32%;margin-right:1%;margin-left:1%;float:left}.responsive-nested-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(3){width:32%;margin-right:0;margin-left:1%;float:left}}.two-col-grid[_ngcontent-%COMP%]:after{display:table;content:\\\" \\\";clear:both}@media only screen and (min-width: 768px){.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(1){width:23.5%;margin-right:1%;margin-left:0;float:left}.two-col-grid[_ngcontent-%COMP%] > *[_ngcontent-%COMP%]:nth-child(2){width:74.5%;margin-right:0;margin-left:1%;float:left}}.container[_ngcontent-%COMP%]{padding-right:1rem;padding-left:1rem}.container[_ngcontent-%COMP%]:not(.is-fluid){margin:0 auto}@media only screen and (min-width: 576px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:100vw}}@media only screen and (min-width: 768px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:95vw}}@media only screen and (min-width: 992px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:90vw}}@media only screen and (min-width: 1200px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:996px}}@media only screen and (min-width: 1400px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1050px}}@media only screen and (min-width: 2560px){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}@media only screen and (min-width:){.container[_ngcontent-%COMP%]:not(.is-fluid){width:100%;max-width:1264px}}.container-sm[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 576px){.container-sm[_ngcontent-%COMP%]{max-width:100vw}}@media only screen and (min-width: 768px){.container-sm[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-sm[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-sm[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-sm[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-sm[_ngcontent-%COMP%]{max-width:1264px}}.container-md[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 768px){.container-md[_ngcontent-%COMP%]{max-width:95vw}}@media only screen and (min-width: 992px){.container-md[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-md[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-md[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-md[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-md[_ngcontent-%COMP%]{max-width:1264px}}.container-lg[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 992px){.container-lg[_ngcontent-%COMP%]{max-width:90vw}}@media only screen and (min-width: 1200px){.container-lg[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-lg[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-lg[_ngcontent-%COMP%]{max-width:1264px}}.container-xl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1200px){.container-xl[_ngcontent-%COMP%]{max-width:996px}}@media only screen and (min-width: 1400px){.container-xl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xl[_ngcontent-%COMP%]{max-width:1264px}}.container-xxl[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 1400px){.container-xxl[_ngcontent-%COMP%]{max-width:1050px}}@media only screen and (min-width: 2560px){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-xxl[_ngcontent-%COMP%]{max-width:1264px}}.container-qhd[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width: 2560px){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}@media only screen and (min-width:){.container-qhd[_ngcontent-%COMP%]{max-width:1264px}}.container-_2k[_ngcontent-%COMP%]{margin:0 auto;padding-right:1rem;padding-left:1rem;width:100%}@media only screen and (min-width:){.container-_2k[_ngcontent-%COMP%]{max-width:1264px}}.notes[_ngcontent-%COMP%]{background-color:#fff;padding:10px 0;border-radius:28px;font-size:15px;position:relative}.notes[_ngcontent-%COMP%]   .notes-right-side[_ngcontent-%COMP%]{width:100%;box-sizing:border-box;border-radius:28px;transition:width .3s ease-in-out;height:100%}@media only screen and (min-width: 992px){.notes[_ngcontent-%COMP%]   .notes-right-side[_ngcontent-%COMP%]{width:75%}}@media only screen and (min-width: 992px){.notes[_ngcontent-%COMP%]   .notes-right-side.expanded[_ngcontent-%COMP%]{width:97%}}.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]{width:100%;padding:4px 10px;border-radius:12px;transition:width .3s ease-in-out;box-shadow:0 3px 6px #00000029;box-sizing:border-box;align-items:center;height:100%;top:86px;background-color:#fff}@media only screen and (min-width: 992px){.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]{width:25%}}@media only screen and (min-width: 992px){.notes[_ngcontent-%COMP%]   .notes-left-side.collapsed[_ngcontent-%COMP%]{width:3%}}.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]   .link-main-color[_ngcontent-%COMP%]{width:150px;text-align:center;border-radius:50px}.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]   .classroom-title[_ngcontent-%COMP%]{padding:10px}.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]   .classroom-title.one-line[_ngcontent-%COMP%]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.notes[_ngcontent-%COMP%]   .notes-left-side[_ngcontent-%COMP%]   .seperator[_ngcontent-%COMP%]{width:100%;height:1px;background-color:#d9ddf0;margin:3px 0}.tabs[_ngcontent-%COMP%]{position:relative;height:35rem}@media only screen and (min-width: 768px){.tabs[_ngcontent-%COMP%]{height:31rem}}@media only screen and (min-width: 768px) and (max-height: 768px){.tabs[_ngcontent-%COMP%]{height:28rem}}@media only screen and (max-width: 768px){.tabs[_ngcontent-%COMP%]{height:40rem}}.tabs[_ngcontent-%COMP%]:before, .tabs[_ngcontent-%COMP%]:after{content:\\\"\\\";display:table}.tabs[_ngcontent-%COMP%]:after{clear:both}.tab[_ngcontent-%COMP%]{float:left;width:48%;margin-right:1%;margin-left:1%}@media only screen and (min-width: 768px){.tab[_ngcontent-%COMP%]{margin-top:-2px}}.tab-switch[_ngcontent-%COMP%]{display:none}.tab-label[_ngcontent-%COMP%]{position:relative;display:block;line-height:2.75em;height:3.7em;padding:0 1.318em;cursor:pointer;top:0;width:100%;transition:all .25s;border:1px solid #96a3e8;border-radius:31.5px;justify-content:center}@media only screen and (min-width: 768px){.tab-label[_ngcontent-%COMP%]{height:4em;justify-content:start}}.tab-label[_ngcontent-%COMP%]:hover{top:-.25rem;transition:top .25s}.tab-content[_ngcontent-%COMP%]{height:100%;position:absolute;z-index:1;top:4.5em;left:0;color:#2c3e50;opacity:0}@media only screen and (max-width: 768px){.tab-content[_ngcontent-%COMP%]{top:4.5em;height:max-content}}.tab-switch[_ngcontent-%COMP%]:checked + .tab-label[_ngcontent-%COMP%]{background:#fff;color:#fff;border-bottom:0;border-right:.125rem solid #fff;border:none;transition:all .35s;z-index:1;opacity:1;background-image:linear-gradient(180deg,#9280fa,#8d7cf8,#8779f6,#8275f5,#7d72f3 44%,#736af0 56%,#6c68ee,#6664ed,#6161ea,#595ee8);border-radius:31.5px;width:100%}.tab-switch[_ngcontent-%COMP%]:checked + label[_ngcontent-%COMP%] + .tab-content[_ngcontent-%COMP%]{z-index:2;opacity:1;transition:all .35s;width:100%}\"]\n    });\n  }\n  return NotesComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}