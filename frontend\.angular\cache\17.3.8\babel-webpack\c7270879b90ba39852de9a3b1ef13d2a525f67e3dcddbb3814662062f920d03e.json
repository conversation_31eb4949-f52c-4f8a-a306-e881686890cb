{"ast": null, "code": "import { signal } from '@angular/core';\nimport { BehaviorSubject } from 'rxjs';\nimport { environment } from 'src/environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./user.service\";\nimport * as i2 from \"@angular/common/http\";\nconst BACKEND_URL = environment.apiUrl;\nexport let LibraryService = /*#__PURE__*/(() => {\n  class LibraryService {\n    setUpdateFolderListener(libraryFile, folder) {\n      this.updateFolderListener$.next({\n        libraryFile: libraryFile,\n        folder: folder\n      });\n    }\n    setLibraryUpdatedListener(val) {\n      this.libraryUpdatedListener$.next(val);\n    }\n    moveFileToFolder(oldId, newId) {\n      return this.http.post(BACKEND_URL + \"/Library/MoveFileToFolder?fileId=\" + oldId + \"&newFolderId=\" + newId, {});\n    }\n    setDeleteListener(id) {\n      this.deleteListener$.next(id);\n    }\n    setDeleteFolderListener(id) {\n      this.deleteFolderListener$.next(id);\n    }\n    setGroupActions(showGroupActions) {\n      this.showGroupActions$.next(showGroupActions);\n    }\n    setshowGroupAction(showGroupActions) {\n      this.showGroupActionsListener$.next(showGroupActions);\n    }\n    setshowUploadAction(showGroupActions) {\n      this.showUploadActionsListener$.next(showGroupActions);\n    }\n    setCheckedNotes(checkedLibraries) {\n      this.checkedLibraries = checkedLibraries;\n    }\n    constructor(userService, http) {\n      this.userService = userService;\n      this.http = http;\n      this.refs = [];\n      this.appendDialogActionsTo = 'some-id';\n      this.showGroupActions$ = new BehaviorSubject(false);\n      this.showGroupActions = this.showGroupActions$.asObservable();\n      this.currentSelectedClassroomId$ = new BehaviorSubject(0);\n      this.currentSelectedClassroomId = this.currentSelectedClassroomId$.asObservable();\n      this.myLibrariesListener$ = new BehaviorSubject(true);\n      this.myLibrariesListener = this.myLibrariesListener$.asObservable();\n      this.updateListener$ = new BehaviorSubject(true);\n      this.updateListener = this.updateListener$.asObservable();\n      this.libraryUpdatedListener$ = new BehaviorSubject(false);\n      this.libraryUpdatedListener = this.libraryUpdatedListener$.asObservable();\n      this.updateFolderListener$ = new BehaviorSubject({\n        libraryFile: {},\n        folder: {}\n      });\n      this.updateFolderListener = this.updateFolderListener$.asObservable();\n      this.deleteListener$ = new BehaviorSubject(0);\n      this.deleteListener = this.deleteListener$.asObservable();\n      this.deleteFolderListener$ = new BehaviorSubject(0);\n      this.deleteFolderListener = this.deleteFolderListener$.asObservable();\n      this.showGroupActionsListener$ = new BehaviorSubject(true);\n      this.showGroupActionsListener = this.showGroupActionsListener$.asObservable();\n      this.showUploadActionsListener$ = new BehaviorSubject(false);\n      this.showUploadActionsListener = this.showUploadActionsListener$.asObservable();\n      this.checkedLibraries = [];\n      this.libraryToAdd = {};\n      this.folderToDelete = {};\n      this.selectedlibraryFile = {};\n      this.currentSelectedClassroom = {};\n      this.isPreviewingFromChat = signal(false);\n      this.checkedLibraryFiles = signal([]);\n      this.rootFolder = {\n        parent: \"\",\n        name: \"home\"\n      };\n    }\n    initializeEmptyLibraryObject() {\n      return {\n        classroom: 0,\n        libraryFiles: [],\n        folder: this.rootFolder\n      };\n    }\n    getFileFullPath(file) {\n      return environment.apiUrl + \"/\" + file;\n    }\n    getLibraryTitle(library) {\n      let title = \"\";\n      let i = 0;\n      let comma = \", \";\n      for (let file of library.libraryFiles) {\n        i++;\n        if (i == library.libraryFiles.length) {\n          comma = \"\";\n        }\n        title += this.getFileName(file.path) + comma;\n      }\n      return title;\n    }\n    getFileName(path) {\n      if (path) {\n        let n = path.lastIndexOf('/');\n        let result = path.substring(n + 1);\n        return result;\n      }\n      return path;\n    }\n    getLibraryFiles(library) {\n      let fileNames = [];\n      for (let file of library.libraryFiles) {\n        fileNames.push({\n          file: {\n            name: this.getFileName(file.path)\n          },\n          progress: 100\n        });\n      }\n      return fileNames;\n    }\n    getFolderPathOfFolder(folder, path, folders) {\n      path.push(folder);\n      if (folder.parent === \"\") {\n        return [];\n      }\n      if (folder.parent === this.rootFolder.name) {\n        return path.reverse();\n      } else {\n        let parentFolder = folders.filter(el => el.name === folder.parent)[0];\n        this.getFolderPathOfFolder(parentFolder, path, folders);\n      }\n      return path;\n    }\n    getInnerFoldersOfLibraries(foldersWithFiles, folders) {\n      let innerFolders = [];\n      for (let folderWithFiles of foldersWithFiles) {\n        let folder = {\n          name: folderWithFiles.name,\n          parent: folderWithFiles.parent\n        };\n        let frontFolders = this.getInnerFoldersOfFolder(folder, folders);\n        innerFolders = innerFolders.concat(frontFolders);\n      }\n      return innerFolders;\n    }\n    getInnerFoldersOfFolder(folder, folders) {\n      let frontFolders = folders.filter(el => {\n        return el.parent == folder.name;\n      });\n      return frontFolders.reverse();\n    }\n    getLibrariesOnRootFolders(foldersWithFiles) {\n      return foldersWithFiles.filter(library => {\n        return library.name === this.rootFolder.name;\n      });\n    }\n    getLibrariesOfFolder(folder, foldersWithFiles) {\n      return foldersWithFiles.filter(library => library.name == folder.name && library.parent == folder.parent);\n    }\n    createLibrary(library) {\n      return this.http.post(BACKEND_URL + \"/Library/CreateLibrary\", library);\n    }\n    getUserLibraries(userId) {\n      return this.http.get(BACKEND_URL + \"/Library/GetLibrariesUserCreated?UserId=\" + userId);\n    }\n    createFolder(folder) {\n      return this.http.post(BACKEND_URL + \"/Library/CreateFolder\", folder);\n    }\n    uploadFilesToFolder(files) {\n      return this.http.post(BACKEND_URL + \"/Library/UploadFilesToFolder\", files);\n    }\n    shareFileWithClassUsers(files) {\n      return this.http.post(BACKEND_URL + \"/Library/ShareFileWithClassUsers\", files);\n    }\n    unShareLibrary(files) {\n      return this.http.post(BACKEND_URL + \"/Library/UnShareLibrary\", files);\n    }\n    getUserCreatedLibFolders() {\n      return this.http.get(BACKEND_URL + \"/Library/GetUserCreatedLibFolders\");\n    }\n    getSharedByMeLibFolders(classroomId) {\n      return this.http.get(BACKEND_URL + \"/Library/GetShareByMe?ClassId=\" + classroomId);\n    }\n    getShareWithMeLibFolders(classroomId) {\n      return this.http.get(BACKEND_URL + \"/Library/GetShareWithMe?ClassId=\" + classroomId);\n    }\n    setCurrentSelectedClassroomId(id) {\n      this.currentSelectedClassroomId$.next(id);\n    }\n    setCurrentSelectedClassroom(classroom) {\n      this.currentSelectedClassroom = classroom;\n    }\n    getCurrentSelectedClassroom() {\n      return this.currentSelectedClassroom;\n    }\n    setMyLibrariesListener(listen) {\n      this.myLibrariesListener$.next(listen);\n    }\n    setUpdateListener(listen) {\n      this.updateListener$.next(listen);\n    }\n    setFolderToDelete(folder) {\n      this.folderToDelete = folder;\n    }\n    getFolderToDelete() {\n      return this.folderToDelete;\n    }\n    setLibraryFile(libFile) {\n      this.selectedlibraryFile = libFile;\n    }\n    getLibraryFile() {\n      return this.selectedlibraryFile;\n    }\n    openDialogWithComponent(dialogService, component, width = 360, dialogData, appendDialogActionsTo, resultHandler) {\n      this.refs.forEach(ref => ref.close());\n      this.refs = [];\n      let centerX = '50%';\n      let centerY = '50%';\n      if (appendDialogActionsTo) {\n        if (Object.keys(appendDialogActionsTo).length > 0) {\n          const element = document.querySelector('#' + appendDialogActionsTo);\n          const rect = element.getBoundingClientRect();\n          const centerX = rect.left + rect.width / 2 - width / 2;\n          const centerY = rect.top;\n        }\n      }\n      const dialogRef = dialogService.open(component, {\n        header: '',\n        width: width + 'px',\n        showHeader: false,\n        dismissableMask: true,\n        modal: true,\n        contentStyle: {\n          \"max-width\": \"100%\",\n          \"max-height\": \"400px\",\n          \"overflow\": \"auto\",\n          \"border-radius\": \"10px\",\n          \"padding\": \"0px\"\n        },\n        style: appendDialogActionsTo ? {\n          'left': centerX,\n          'top': centerY,\n          'position': `fixed`\n        } : {},\n        baseZIndex: 10000,\n        maskStyleClass: 'transparent-mask',\n        data: {\n          dialogData: dialogData\n        }\n      });\n      this.refs.push(dialogRef);\n      dialogRef.onClose.subscribe(data => {\n        console.log('Dialog closed with data:', data);\n        if (resultHandler) {\n          resultHandler(data);\n        }\n      });\n    }\n    // getClassroomFiles(classroomId: number) {\n    //   return this.http.get<any>(BACKEND_URL + \"/Library/GetClassroomFiles?classroomid=\" + classroomId)\n    // }\n    getClassroomFiles(classroomId) {\n      return this.http.get(BACKEND_URL + \"/Library/GetMyClassroomFiles?classroomId=\" + classroomId);\n    }\n    uploadClassroomFilesToFolder(req) {\n      return this.http.post(BACKEND_URL + \"/Library/UploadClassroomFilesToFolder\", req);\n    }\n    getUsersLibraryHasBeenSharedWith(fileId) {\n      return this.http.get(BACKEND_URL + \"/Library/GetUsersLibraryHasBeenSharedWith?fileId=\" + fileId);\n    }\n    delete(fileId) {\n      return this.http.post(BACKEND_URL + \"/Library/DeleteLibrary?libraryId=\" + fileId, {});\n    }\n    deleteFolder(folderId) {\n      return this.http.post(BACKEND_URL + \"/Library/DeleteFolder?FolderId=\" + folderId, {});\n    }\n    updateLibraryFileName(fileId, newName) {\n      return this.http.post(BACKEND_URL + \"/Library/UpdateLibraryFileName?fileId=\" + fileId + \"&newName=\" + newName, {});\n    }\n    getTeacherClassRooms() {\n      return this.http.get(BACKEND_URL + \"/Classrooms/GetTeacherClassrooms/\");\n    }\n    editFileLevels(newLevels) {\n      return this.http.post(BACKEND_URL + \"/Library/EditFileLevels\", newLevels);\n    }\n    editFileCategories(newFileCategories) {\n      return this.http.post(BACKEND_URL + \"/Library/EditFileCategories\", newFileCategories);\n    }\n    static #_ = this.ɵfac = function LibraryService_Factory(t) {\n      return new (t || LibraryService)(i0.ɵɵinject(i1.UserService), i0.ɵɵinject(i2.HttpClient));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: LibraryService,\n      factory: LibraryService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return LibraryService;\n})();", "map": {"version": 3, "names": ["signal", "BehaviorSubject", "environment", "BACKEND_URL", "apiUrl", "LibraryService", "setUpdateFolderListener", "libraryFile", "folder", "updateFolderListener$", "next", "setLibraryUpdatedListener", "val", "libraryUpdatedListener$", "moveFileToFolder", "oldId", "newId", "http", "post", "setDeleteListener", "id", "deleteListener$", "setDeleteFolderListener", "deleteFolderListener$", "setGroupActions", "showGroupActions", "showGroupActions$", "setshowGroupAction", "showGroupActionsListener$", "setshowUploadAction", "showUploadActionsListener$", "setCheckedNotes", "checkedLibraries", "constructor", "userService", "refs", "appendDialogActionsTo", "asObservable", "currentSelectedClassroomId$", "currentSelectedClassroomId", "myLibrariesListener$", "myLibrariesListener", "updateListener$", "updateListener", "libraryUpdatedListener", "updateFolderListener", "deleteListener", "deleteFolderListener", "showGroupActionsListener", "showUploadActionsListener", "libraryToAdd", "folderToDelete", "selectedlibraryFile", "currentSelectedClassroom", "isPreviewingFromChat", "checkedLibraryFiles", "rootFolder", "parent", "name", "initializeEmptyLibraryObject", "classroom", "libraryFiles", "getFile<PERSON>ull<PERSON><PERSON>", "file", "getLibraryTitle", "library", "title", "i", "comma", "length", "getFileName", "path", "n", "lastIndexOf", "result", "substring", "getLibraryFiles", "fileNames", "push", "progress", "getFolderPathOfFolder", "folders", "reverse", "parentFolder", "filter", "el", "getInnerFoldersOfLibraries", "foldersWithFiles", "innerFolders", "folderWithFiles", "frontFolders", "getInnerFoldersOfFolder", "concat", "getLibrariesOnRootFolders", "getLibrariesOfFolder", "createLibrary", "getUserLibraries", "userId", "get", "createFolder", "uploadFilesToFolder", "files", "shareFileWithClassUsers", "unShareLibrary", "getUserCreatedLibFolders", "getSharedByMeLibFolders", "classroomId", "getShareWithMeLibFolders", "setCurrentSelectedClassroomId", "setCurrentSelectedClassroom", "getCurrentSelectedClassroom", "setMyLibrariesListener", "listen", "setUpdateListener", "setFolderToDelete", "getFolderToDelete", "setLibraryFile", "libFile", "getLibraryFile", "openDialogWithComponent", "dialogService", "component", "width", "dialogData", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "ref", "close", "centerX", "centerY", "Object", "keys", "element", "document", "querySelector", "rect", "getBoundingClientRect", "left", "top", "dialogRef", "open", "header", "showHeader", "dismissableMask", "modal", "contentStyle", "style", "baseZIndex", "maskStyleClass", "data", "onClose", "subscribe", "console", "log", "getClassroomFiles", "uploadClassroomFilesToFolder", "req", "getUsersLibraryHasBeenSharedWith", "fileId", "delete", "deleteFolder", "folderId", "updateLibraryFileName", "newName", "getTeacherClassRooms", "editFileLevels", "newLevels", "editFileCategories", "newFileCategories", "_", "i0", "ɵɵinject", "i1", "UserService", "i2", "HttpClient", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\core\\services\\library.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable, signal } from '@angular/core';\r\nimport { BehaviorSubject, Observable } from 'rxjs';\r\nimport { environment } from 'src/environments/environment';\r\nimport { Classroom, Level } from '../models/classroom.model';\r\nimport { Category } from '../models/homework.model';\r\nimport { Folder, FoldersWithFiles, Library, LibraryFile, UploadFilesToFolderRequest } from '../models/library.model';\r\nimport { User } from '../models/user.model';\r\nimport { UserService } from './user.service';\r\nconst BACKEND_URL = environment.apiUrl\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class LibraryService {\r\n\r\n  private refs: any[] = [];\r\n  private appendDialogActionsTo = 'some-id';\r\n\r\n  private showGroupActions$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);\r\n  public showGroupActions: Observable<boolean> = this.showGroupActions$.asObservable();\r\n\r\n  private currentSelectedClassroomId$: BehaviorSubject<number> = new BehaviorSubject<number>(0);\r\n  public currentSelectedClassroomId: Observable<number> = this.currentSelectedClassroomId$.asObservable();\r\n\r\n  private myLibrariesListener$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);\r\n  public myLibrariesListener: Observable<boolean> = this.myLibrariesListener$.asObservable();\r\n\r\n  private updateListener$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);\r\n  public updateListener: Observable<boolean> = this.updateListener$.asObservable();\r\n\r\n  private libraryUpdatedListener$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);\r\n  public libraryUpdatedListener: Observable<boolean> = this.libraryUpdatedListener$.asObservable();\r\n\r\n  private updateFolderListener$: BehaviorSubject<{ libraryFile: LibraryFile, folder: Folder }> = new BehaviorSubject<{ libraryFile: LibraryFile, folder: Folder }>({ libraryFile: {} as LibraryFile, folder: {} as Folder });\r\n  public updateFolderListener: Observable<{ libraryFile: LibraryFile, folder: Folder }> = this.updateFolderListener$.asObservable();\r\n\r\n  private deleteListener$: BehaviorSubject<number> = new BehaviorSubject<number>(0);\r\n  public deleteListener: Observable<number> = this.deleteListener$.asObservable();\r\n\r\n  private deleteFolderListener$: BehaviorSubject<number> = new BehaviorSubject<number>(0);\r\n  public deleteFolderListener: Observable<number> = this.deleteFolderListener$.asObservable();\r\n\r\n  private showGroupActionsListener$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(true);\r\n  public showGroupActionsListener: Observable<boolean> = this.showGroupActionsListener$.asObservable();\r\n\r\n  private showUploadActionsListener$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);\r\n  public showUploadActionsListener: Observable<boolean> = this.showUploadActionsListener$.asObservable();\r\n\r\n  checkedLibraries: LibraryFile[] = [];\r\n  libraryToAdd: Library = {} as Library;\r\n  folderToDelete: Folder = {} as Folder;\r\n  selectedlibraryFile: LibraryFile = {} as LibraryFile;\r\n  currentSelectedClassroom: Classroom = {} as Classroom;\r\n\r\n  isPreviewingFromChat = signal(false);\r\n  checkedLibraryFiles = signal<LibraryFile[]>([]);\r\n\r\n  setUpdateFolderListener(libraryFile: LibraryFile, folder: Folder) {\r\n    this.updateFolderListener$.next({ libraryFile: libraryFile, folder: folder });\r\n  }\r\n\r\n  setLibraryUpdatedListener(val: boolean) {\r\n    this.libraryUpdatedListener$.next(val);\r\n  }\r\n\r\n  moveFileToFolder(oldId: number, newId: number) {\r\n    return this.http.post<LibraryFile>(BACKEND_URL + \"/Library/MoveFileToFolder?fileId=\" + oldId + \"&newFolderId=\" + newId, {})\r\n\r\n  }\r\n\r\n  setDeleteListener(id: number) {\r\n    this.deleteListener$.next(id);\r\n  }\r\n\r\n  setDeleteFolderListener(id: number) {\r\n    this.deleteFolderListener$.next(id);\r\n  }\r\n\r\n  setGroupActions(showGroupActions: boolean) {\r\n    this.showGroupActions$.next(showGroupActions);\r\n  }\r\n\r\n  setshowGroupAction(showGroupActions: boolean) {\r\n    this.showGroupActionsListener$.next(showGroupActions);\r\n  }\r\n\r\n  setshowUploadAction(showGroupActions: boolean) {\r\n    this.showUploadActionsListener$.next(showGroupActions);\r\n  }\r\n\r\n  setCheckedNotes(checkedLibraries: LibraryFile[]) {\r\n    this.checkedLibraries = checkedLibraries;\r\n  }\r\n\r\n  public rootFolder: Folder = {\r\n    parent: \"\",\r\n    name: \"home\"\r\n  }\r\n\r\n  constructor(\r\n    private userService: UserService,\r\n    private http: HttpClient,\r\n  ) { }\r\n\r\n  initializeEmptyLibraryObject(): Library {\r\n    return {\r\n      classroom: 0,\r\n      libraryFiles: [],\r\n      folder: this.rootFolder,\r\n    }\r\n  }\r\n\r\n  getFileFullPath(file: string) {\r\n    return environment.apiUrl + \"/\" + file;\r\n  }\r\n\r\n  getLibraryTitle(library: Library) {\r\n    let title = \"\";\r\n    let i = 0;\r\n    let comma = \", \"\r\n    for (let file of library.libraryFiles) {\r\n      i++;\r\n      if (i == library.libraryFiles.length) {\r\n        comma = \"\"\r\n      }\r\n      title += this.getFileName(file.path) + comma;\r\n    }\r\n    return title;\r\n  }\r\n\r\n  getFileName(path: string) {\r\n    if (path) {\r\n      let n = path.lastIndexOf('/');\r\n      let result = path.substring(n + 1);\r\n      return result;\r\n    }\r\n    return path\r\n  }\r\n\r\n  getLibraryFiles(library: Library) {\r\n    let fileNames = [];\r\n    for (let file of library.libraryFiles) {\r\n      fileNames.push({ file: { name: this.getFileName(file.path) }, progress: 100 });\r\n    }\r\n    return fileNames;\r\n  }\r\n\r\n  getFolderPathOfFolder(folder: Folder, path: Folder[], folders: Folder[]) {\r\n    path.push(folder);\r\n    if (folder.parent === \"\") {\r\n      return []\r\n    }\r\n    if (folder.parent === this.rootFolder.name) {\r\n      return path.reverse()\r\n    } else {\r\n      let parentFolder = folders.filter(el => el.name === folder.parent)[0]\r\n      this.getFolderPathOfFolder(parentFolder, path, folders);\r\n    }\r\n    return path\r\n  }\r\n\r\n  getInnerFoldersOfLibraries(foldersWithFiles: FoldersWithFiles[], folders: Folder[]): Folder[] {\r\n    let innerFolders: Folder[] = [];\r\n    for (let folderWithFiles of foldersWithFiles) {\r\n      let folder: Folder = {\r\n        name: folderWithFiles.name,\r\n        parent: folderWithFiles.parent\r\n      }\r\n      let frontFolders = this.getInnerFoldersOfFolder(folder, folders);\r\n      innerFolders = innerFolders.concat(frontFolders);\r\n    }\r\n    return innerFolders;\r\n  }\r\n\r\n  getInnerFoldersOfFolder(folder: Folder, folders: Folder[]) {\r\n    let frontFolders = folders.filter(el => {\r\n      return el.parent == folder.name\r\n    })\r\n    return frontFolders.reverse()\r\n  }\r\n\r\n  getLibrariesOnRootFolders(foldersWithFiles: FoldersWithFiles[]): FoldersWithFiles[] {\r\n    return foldersWithFiles.filter((library: FoldersWithFiles) => {\r\n      return library.name === this.rootFolder.name\r\n    });\r\n  }\r\n\r\n  getLibrariesOfFolder(folder: Folder, foldersWithFiles: FoldersWithFiles[]): FoldersWithFiles[] {\r\n    return foldersWithFiles.filter((library: FoldersWithFiles) => library.name == folder.name && library.parent == folder.parent)\r\n  }\r\n\r\n  createLibrary(library: Library) {\r\n    return this.http.post(BACKEND_URL + \"/Library/CreateLibrary\", library)\r\n  }\r\n\r\n  getUserLibraries(userId: string): Observable<Library[]> {\r\n    return this.http.get<Library[]>(BACKEND_URL + \"/Library/GetLibrariesUserCreated?UserId=\" + userId)\r\n  }\r\n\r\n  createFolder(folder: Folder) {\r\n    return this.http.post<Folder>(BACKEND_URL + \"/Library/CreateFolder\", folder)\r\n  }\r\n\r\n  uploadFilesToFolder(files: UploadFilesToFolderRequest) {\r\n    return this.http.post<LibraryFile[]>(BACKEND_URL + \"/Library/UploadFilesToFolder\", files)\r\n  }\r\n\r\n  shareFileWithClassUsers(files: LibraryFile[]) {\r\n    return this.http.post<LibraryFile[]>(BACKEND_URL + \"/Library/ShareFileWithClassUsers\", files)\r\n  }\r\n\r\n  unShareLibrary(files: LibraryFile[]) {\r\n    return this.http.post<LibraryFile[]>(BACKEND_URL + \"/Library/UnShareLibrary\", files)\r\n  }\r\n\r\n  getUserCreatedLibFolders() {\r\n    return this.http.get<FoldersWithFiles[]>(BACKEND_URL + \"/Library/GetUserCreatedLibFolders\")\r\n  }\r\n\r\n  getSharedByMeLibFolders(classroomId: number) {\r\n    return this.http.get<FoldersWithFiles[]>(BACKEND_URL + \"/Library/GetShareByMe?ClassId=\" + classroomId)\r\n  }\r\n\r\n  getShareWithMeLibFolders(classroomId: number) {\r\n    return this.http.get<FoldersWithFiles[]>(BACKEND_URL + \"/Library/GetShareWithMe?ClassId=\" + classroomId)\r\n  }\r\n\r\n  setCurrentSelectedClassroomId(id: number) {\r\n    this.currentSelectedClassroomId$.next(id);\r\n  }\r\n\r\n  setCurrentSelectedClassroom(classroom: Classroom) {\r\n    this.currentSelectedClassroom = classroom;\r\n  }\r\n\r\n  getCurrentSelectedClassroom() {\r\n    return this.currentSelectedClassroom;\r\n  }\r\n\r\n  setMyLibrariesListener(listen: boolean) {\r\n    this.myLibrariesListener$.next(listen);\r\n  }\r\n\r\n  setUpdateListener(listen: boolean) {\r\n    this.updateListener$.next(listen);\r\n  }\r\n\r\n  setFolderToDelete(folder: Folder) {\r\n    this.folderToDelete = folder;\r\n  }\r\n\r\n  getFolderToDelete(): Folder {\r\n    return this.folderToDelete;\r\n  }\r\n\r\n  setLibraryFile(libFile: LibraryFile) {\r\n    this.selectedlibraryFile = libFile;\r\n  }\r\n\r\n  getLibraryFile(): LibraryFile {\r\n    return this.selectedlibraryFile;\r\n  }\r\n\r\n  public openDialogWithComponent(dialogService: any, component: any, width = 360, dialogData?: any, appendDialogActionsTo?: any, resultHandler?: (result: any) => void) {\r\n    this.refs.forEach(ref => ref.close());\r\n    this.refs = [];\r\n    let centerX = '50%';\r\n    let centerY = '50%';\r\n    if (appendDialogActionsTo) {\r\n      if (Object.keys(appendDialogActionsTo).length > 0) {\r\n        const element = document.querySelector('#' + appendDialogActionsTo);\r\n        const rect = element!.getBoundingClientRect();\r\n        const centerX = rect.left + rect.width / 2 - width / 2;\r\n        const centerY = rect.top;\r\n      }\r\n    }\r\n    const dialogRef = (dialogService.open(component, {\r\n      header: '',\r\n      width: width + 'px',\r\n      showHeader: false,\r\n      dismissableMask: true,\r\n      modal: true,\r\n      contentStyle: {\r\n        \"max-width\": \"100%\", \"max-height\": \"400px\", \"overflow\": \"auto\", \"border-radius\": \"10px\", \"padding\": \"0px\",\r\n      },\r\n      style: appendDialogActionsTo ? { 'left': centerX, 'top': centerY, 'position': `fixed` } : {},\r\n      baseZIndex: 10000,\r\n      maskStyleClass: 'transparent-mask',\r\n      data: { dialogData: dialogData },\r\n    }));\r\n    this.refs.push(dialogRef);\r\n    dialogRef.onClose.subscribe((data: any) => {\r\n      console.log('Dialog closed with data:', data);\r\n      if (resultHandler) {\r\n        resultHandler(data);\r\n      }\r\n    });\r\n  }\r\n\r\n  // getClassroomFiles(classroomId: number) {\r\n  //   return this.http.get<any>(BACKEND_URL + \"/Library/GetClassroomFiles?classroomid=\" + classroomId)\r\n  // }\r\n\r\n  getClassroomFiles(classroomId: number) {\r\n    return this.http.get<any>(BACKEND_URL + \"/Library/GetMyClassroomFiles?classroomId=\" + classroomId)\r\n  }\r\n\r\n  uploadClassroomFilesToFolder(req: any) {\r\n    return this.http.post<any>(BACKEND_URL + \"/Library/UploadClassroomFilesToFolder\", req)\r\n  }\r\n\r\n  getUsersLibraryHasBeenSharedWith(fileId: any) {\r\n    return this.http.get<any>(BACKEND_URL + \"/Library/GetUsersLibraryHasBeenSharedWith?fileId=\" + fileId)\r\n  }\r\n\r\n  delete(fileId: any) {\r\n    return this.http.post<any>(BACKEND_URL + \"/Library/DeleteLibrary?libraryId=\" + fileId, {})\r\n  }\r\n\r\n  deleteFolder(folderId: any) {\r\n    return this.http.post<any>(BACKEND_URL + \"/Library/DeleteFolder?FolderId=\" + folderId, {})\r\n  }\r\n\r\n  updateLibraryFileName(fileId: any, newName: string) {\r\n    return this.http.post<any>(BACKEND_URL + \"/Library/UpdateLibraryFileName?fileId=\" + fileId + \"&newName=\" + newName, {});\r\n  }\r\n\r\n  getTeacherClassRooms() {\r\n    return this.http.get<any>(BACKEND_URL + \"/Classrooms/GetTeacherClassrooms/\");\r\n  }\r\n\r\n  editFileLevels(newLevels: any) {\r\n    return this.http.post<any>(BACKEND_URL + \"/Library/EditFileLevels\", newLevels);\r\n  }\r\n\r\n  editFileCategories(newFileCategories: any) {\r\n    return this.http.post<any>(BACKEND_URL + \"/Library/EditFileCategories\", newFileCategories);\r\n  }\r\n\r\n}\r\n"], "mappings": "AACA,SAAqBA,MAAM,QAAQ,eAAe;AAClD,SAASC,eAAe,QAAoB,MAAM;AAClD,SAASC,WAAW,QAAQ,8BAA8B;;;;AAM1D,MAAMC,WAAW,GAAGD,WAAW,CAACE,MAAM;AAItC,WAAaC,cAAc;EAArB,MAAOA,cAAc;IA4CzBC,uBAAuBA,CAACC,WAAwB,EAAEC,MAAc;MAC9D,IAAI,CAACC,qBAAqB,CAACC,IAAI,CAAC;QAAEH,WAAW,EAAEA,WAAW;QAAEC,MAAM,EAAEA;MAAM,CAAE,CAAC;IAC/E;IAEAG,yBAAyBA,CAACC,GAAY;MACpC,IAAI,CAACC,uBAAuB,CAACH,IAAI,CAACE,GAAG,CAAC;IACxC;IAEAE,gBAAgBA,CAACC,KAAa,EAAEC,KAAa;MAC3C,OAAO,IAAI,CAACC,IAAI,CAACC,IAAI,CAAcf,WAAW,GAAG,mCAAmC,GAAGY,KAAK,GAAG,eAAe,GAAGC,KAAK,EAAE,EAAE,CAAC;IAE7H;IAEAG,iBAAiBA,CAACC,EAAU;MAC1B,IAAI,CAACC,eAAe,CAACX,IAAI,CAACU,EAAE,CAAC;IAC/B;IAEAE,uBAAuBA,CAACF,EAAU;MAChC,IAAI,CAACG,qBAAqB,CAACb,IAAI,CAACU,EAAE,CAAC;IACrC;IAEAI,eAAeA,CAACC,gBAAyB;MACvC,IAAI,CAACC,iBAAiB,CAAChB,IAAI,CAACe,gBAAgB,CAAC;IAC/C;IAEAE,kBAAkBA,CAACF,gBAAyB;MAC1C,IAAI,CAACG,yBAAyB,CAAClB,IAAI,CAACe,gBAAgB,CAAC;IACvD;IAEAI,mBAAmBA,CAACJ,gBAAyB;MAC3C,IAAI,CAACK,0BAA0B,CAACpB,IAAI,CAACe,gBAAgB,CAAC;IACxD;IAEAM,eAAeA,CAACC,gBAA+B;MAC7C,IAAI,CAACA,gBAAgB,GAAGA,gBAAgB;IAC1C;IAOAC,YACUC,WAAwB,EACxBjB,IAAgB;MADhB,KAAAiB,WAAW,GAAXA,WAAW;MACX,KAAAjB,IAAI,GAAJA,IAAI;MAtFN,KAAAkB,IAAI,GAAU,EAAE;MAChB,KAAAC,qBAAqB,GAAG,SAAS;MAEjC,KAAAV,iBAAiB,GAA6B,IAAIzB,eAAe,CAAU,KAAK,CAAC;MAClF,KAAAwB,gBAAgB,GAAwB,IAAI,CAACC,iBAAiB,CAACW,YAAY,EAAE;MAE5E,KAAAC,2BAA2B,GAA4B,IAAIrC,eAAe,CAAS,CAAC,CAAC;MACtF,KAAAsC,0BAA0B,GAAuB,IAAI,CAACD,2BAA2B,CAACD,YAAY,EAAE;MAE/F,KAAAG,oBAAoB,GAA6B,IAAIvC,eAAe,CAAU,IAAI,CAAC;MACpF,KAAAwC,mBAAmB,GAAwB,IAAI,CAACD,oBAAoB,CAACH,YAAY,EAAE;MAElF,KAAAK,eAAe,GAA6B,IAAIzC,eAAe,CAAU,IAAI,CAAC;MAC/E,KAAA0C,cAAc,GAAwB,IAAI,CAACD,eAAe,CAACL,YAAY,EAAE;MAExE,KAAAxB,uBAAuB,GAA6B,IAAIZ,eAAe,CAAU,KAAK,CAAC;MACxF,KAAA2C,sBAAsB,GAAwB,IAAI,CAAC/B,uBAAuB,CAACwB,YAAY,EAAE;MAExF,KAAA5B,qBAAqB,GAAkE,IAAIR,eAAe,CAA+C;QAAEM,WAAW,EAAE,EAAiB;QAAEC,MAAM,EAAE;MAAY,CAAE,CAAC;MACnN,KAAAqC,oBAAoB,GAA6D,IAAI,CAACpC,qBAAqB,CAAC4B,YAAY,EAAE;MAEzH,KAAAhB,eAAe,GAA4B,IAAIpB,eAAe,CAAS,CAAC,CAAC;MAC1E,KAAA6C,cAAc,GAAuB,IAAI,CAACzB,eAAe,CAACgB,YAAY,EAAE;MAEvE,KAAAd,qBAAqB,GAA4B,IAAItB,eAAe,CAAS,CAAC,CAAC;MAChF,KAAA8C,oBAAoB,GAAuB,IAAI,CAACxB,qBAAqB,CAACc,YAAY,EAAE;MAEnF,KAAAT,yBAAyB,GAA6B,IAAI3B,eAAe,CAAU,IAAI,CAAC;MACzF,KAAA+C,wBAAwB,GAAwB,IAAI,CAACpB,yBAAyB,CAACS,YAAY,EAAE;MAE5F,KAAAP,0BAA0B,GAA6B,IAAI7B,eAAe,CAAU,KAAK,CAAC;MAC3F,KAAAgD,yBAAyB,GAAwB,IAAI,CAACnB,0BAA0B,CAACO,YAAY,EAAE;MAEtG,KAAAL,gBAAgB,GAAkB,EAAE;MACpC,KAAAkB,YAAY,GAAY,EAAa;MACrC,KAAAC,cAAc,GAAW,EAAY;MACrC,KAAAC,mBAAmB,GAAgB,EAAiB;MACpD,KAAAC,wBAAwB,GAAc,EAAe;MAErD,KAAAC,oBAAoB,GAAGtD,MAAM,CAAC,KAAK,CAAC;MACpC,KAAAuD,mBAAmB,GAAGvD,MAAM,CAAgB,EAAE,CAAC;MAuCxC,KAAAwD,UAAU,GAAW;QAC1BC,MAAM,EAAE,EAAE;QACVC,IAAI,EAAE;OACP;IAKG;IAEJC,4BAA4BA,CAAA;MAC1B,OAAO;QACLC,SAAS,EAAE,CAAC;QACZC,YAAY,EAAE,EAAE;QAChBrD,MAAM,EAAE,IAAI,CAACgD;OACd;IACH;IAEAM,eAAeA,CAACC,IAAY;MAC1B,OAAO7D,WAAW,CAACE,MAAM,GAAG,GAAG,GAAG2D,IAAI;IACxC;IAEAC,eAAeA,CAACC,OAAgB;MAC9B,IAAIC,KAAK,GAAG,EAAE;MACd,IAAIC,CAAC,GAAG,CAAC;MACT,IAAIC,KAAK,GAAG,IAAI;MAChB,KAAK,IAAIL,IAAI,IAAIE,OAAO,CAACJ,YAAY,EAAE;QACrCM,CAAC,EAAE;QACH,IAAIA,CAAC,IAAIF,OAAO,CAACJ,YAAY,CAACQ,MAAM,EAAE;UACpCD,KAAK,GAAG,EAAE;QACZ;QACAF,KAAK,IAAI,IAAI,CAACI,WAAW,CAACP,IAAI,CAACQ,IAAI,CAAC,GAAGH,KAAK;MAC9C;MACA,OAAOF,KAAK;IACd;IAEAI,WAAWA,CAACC,IAAY;MACtB,IAAIA,IAAI,EAAE;QACR,IAAIC,CAAC,GAAGD,IAAI,CAACE,WAAW,CAAC,GAAG,CAAC;QAC7B,IAAIC,MAAM,GAAGH,IAAI,CAACI,SAAS,CAACH,CAAC,GAAG,CAAC,CAAC;QAClC,OAAOE,MAAM;MACf;MACA,OAAOH,IAAI;IACb;IAEAK,eAAeA,CAACX,OAAgB;MAC9B,IAAIY,SAAS,GAAG,EAAE;MAClB,KAAK,IAAId,IAAI,IAAIE,OAAO,CAACJ,YAAY,EAAE;QACrCgB,SAAS,CAACC,IAAI,CAAC;UAAEf,IAAI,EAAE;YAAEL,IAAI,EAAE,IAAI,CAACY,WAAW,CAACP,IAAI,CAACQ,IAAI;UAAC,CAAE;UAAEQ,QAAQ,EAAE;QAAG,CAAE,CAAC;MAChF;MACA,OAAOF,SAAS;IAClB;IAEAG,qBAAqBA,CAACxE,MAAc,EAAE+D,IAAc,EAAEU,OAAiB;MACrEV,IAAI,CAACO,IAAI,CAACtE,MAAM,CAAC;MACjB,IAAIA,MAAM,CAACiD,MAAM,KAAK,EAAE,EAAE;QACxB,OAAO,EAAE;MACX;MACA,IAAIjD,MAAM,CAACiD,MAAM,KAAK,IAAI,CAACD,UAAU,CAACE,IAAI,EAAE;QAC1C,OAAOa,IAAI,CAACW,OAAO,EAAE;MACvB,CAAC,MAAM;QACL,IAAIC,YAAY,GAAGF,OAAO,CAACG,MAAM,CAACC,EAAE,IAAIA,EAAE,CAAC3B,IAAI,KAAKlD,MAAM,CAACiD,MAAM,CAAC,CAAC,CAAC,CAAC;QACrE,IAAI,CAACuB,qBAAqB,CAACG,YAAY,EAAEZ,IAAI,EAAEU,OAAO,CAAC;MACzD;MACA,OAAOV,IAAI;IACb;IAEAe,0BAA0BA,CAACC,gBAAoC,EAAEN,OAAiB;MAChF,IAAIO,YAAY,GAAa,EAAE;MAC/B,KAAK,IAAIC,eAAe,IAAIF,gBAAgB,EAAE;QAC5C,IAAI/E,MAAM,GAAW;UACnBkD,IAAI,EAAE+B,eAAe,CAAC/B,IAAI;UAC1BD,MAAM,EAAEgC,eAAe,CAAChC;SACzB;QACD,IAAIiC,YAAY,GAAG,IAAI,CAACC,uBAAuB,CAACnF,MAAM,EAAEyE,OAAO,CAAC;QAChEO,YAAY,GAAGA,YAAY,CAACI,MAAM,CAACF,YAAY,CAAC;MAClD;MACA,OAAOF,YAAY;IACrB;IAEAG,uBAAuBA,CAACnF,MAAc,EAAEyE,OAAiB;MACvD,IAAIS,YAAY,GAAGT,OAAO,CAACG,MAAM,CAACC,EAAE,IAAG;QACrC,OAAOA,EAAE,CAAC5B,MAAM,IAAIjD,MAAM,CAACkD,IAAI;MACjC,CAAC,CAAC;MACF,OAAOgC,YAAY,CAACR,OAAO,EAAE;IAC/B;IAEAW,yBAAyBA,CAACN,gBAAoC;MAC5D,OAAOA,gBAAgB,CAACH,MAAM,CAAEnB,OAAyB,IAAI;QAC3D,OAAOA,OAAO,CAACP,IAAI,KAAK,IAAI,CAACF,UAAU,CAACE,IAAI;MAC9C,CAAC,CAAC;IACJ;IAEAoC,oBAAoBA,CAACtF,MAAc,EAAE+E,gBAAoC;MACvE,OAAOA,gBAAgB,CAACH,MAAM,CAAEnB,OAAyB,IAAKA,OAAO,CAACP,IAAI,IAAIlD,MAAM,CAACkD,IAAI,IAAIO,OAAO,CAACR,MAAM,IAAIjD,MAAM,CAACiD,MAAM,CAAC;IAC/H;IAEAsC,aAAaA,CAAC9B,OAAgB;MAC5B,OAAO,IAAI,CAAChD,IAAI,CAACC,IAAI,CAACf,WAAW,GAAG,wBAAwB,EAAE8D,OAAO,CAAC;IACxE;IAEA+B,gBAAgBA,CAACC,MAAc;MAC7B,OAAO,IAAI,CAAChF,IAAI,CAACiF,GAAG,CAAY/F,WAAW,GAAG,0CAA0C,GAAG8F,MAAM,CAAC;IACpG;IAEAE,YAAYA,CAAC3F,MAAc;MACzB,OAAO,IAAI,CAACS,IAAI,CAACC,IAAI,CAASf,WAAW,GAAG,uBAAuB,EAAEK,MAAM,CAAC;IAC9E;IAEA4F,mBAAmBA,CAACC,KAAiC;MACnD,OAAO,IAAI,CAACpF,IAAI,CAACC,IAAI,CAAgBf,WAAW,GAAG,8BAA8B,EAAEkG,KAAK,CAAC;IAC3F;IAEAC,uBAAuBA,CAACD,KAAoB;MAC1C,OAAO,IAAI,CAACpF,IAAI,CAACC,IAAI,CAAgBf,WAAW,GAAG,kCAAkC,EAAEkG,KAAK,CAAC;IAC/F;IAEAE,cAAcA,CAACF,KAAoB;MACjC,OAAO,IAAI,CAACpF,IAAI,CAACC,IAAI,CAAgBf,WAAW,GAAG,yBAAyB,EAAEkG,KAAK,CAAC;IACtF;IAEAG,wBAAwBA,CAAA;MACtB,OAAO,IAAI,CAACvF,IAAI,CAACiF,GAAG,CAAqB/F,WAAW,GAAG,mCAAmC,CAAC;IAC7F;IAEAsG,uBAAuBA,CAACC,WAAmB;MACzC,OAAO,IAAI,CAACzF,IAAI,CAACiF,GAAG,CAAqB/F,WAAW,GAAG,gCAAgC,GAAGuG,WAAW,CAAC;IACxG;IAEAC,wBAAwBA,CAACD,WAAmB;MAC1C,OAAO,IAAI,CAACzF,IAAI,CAACiF,GAAG,CAAqB/F,WAAW,GAAG,kCAAkC,GAAGuG,WAAW,CAAC;IAC1G;IAEAE,6BAA6BA,CAACxF,EAAU;MACtC,IAAI,CAACkB,2BAA2B,CAAC5B,IAAI,CAACU,EAAE,CAAC;IAC3C;IAEAyF,2BAA2BA,CAACjD,SAAoB;MAC9C,IAAI,CAACP,wBAAwB,GAAGO,SAAS;IAC3C;IAEAkD,2BAA2BA,CAAA;MACzB,OAAO,IAAI,CAACzD,wBAAwB;IACtC;IAEA0D,sBAAsBA,CAACC,MAAe;MACpC,IAAI,CAACxE,oBAAoB,CAAC9B,IAAI,CAACsG,MAAM,CAAC;IACxC;IAEAC,iBAAiBA,CAACD,MAAe;MAC/B,IAAI,CAACtE,eAAe,CAAChC,IAAI,CAACsG,MAAM,CAAC;IACnC;IAEAE,iBAAiBA,CAAC1G,MAAc;MAC9B,IAAI,CAAC2C,cAAc,GAAG3C,MAAM;IAC9B;IAEA2G,iBAAiBA,CAAA;MACf,OAAO,IAAI,CAAChE,cAAc;IAC5B;IAEAiE,cAAcA,CAACC,OAAoB;MACjC,IAAI,CAACjE,mBAAmB,GAAGiE,OAAO;IACpC;IAEAC,cAAcA,CAAA;MACZ,OAAO,IAAI,CAAClE,mBAAmB;IACjC;IAEOmE,uBAAuBA,CAACC,aAAkB,EAAEC,SAAc,EAAEC,KAAK,GAAG,GAAG,EAAEC,UAAgB,EAAEvF,qBAA2B,EAAEwF,aAAqC;MAClK,IAAI,CAACzF,IAAI,CAAC0F,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,KAAK,EAAE,CAAC;MACrC,IAAI,CAAC5F,IAAI,GAAG,EAAE;MACd,IAAI6F,OAAO,GAAG,KAAK;MACnB,IAAIC,OAAO,GAAG,KAAK;MACnB,IAAI7F,qBAAqB,EAAE;QACzB,IAAI8F,MAAM,CAACC,IAAI,CAAC/F,qBAAqB,CAAC,CAACiC,MAAM,GAAG,CAAC,EAAE;UACjD,MAAM+D,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,GAAGlG,qBAAqB,CAAC;UACnE,MAAMmG,IAAI,GAAGH,OAAQ,CAACI,qBAAqB,EAAE;UAC7C,MAAMR,OAAO,GAAGO,IAAI,CAACE,IAAI,GAAGF,IAAI,CAACb,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC;UACtD,MAAMO,OAAO,GAAGM,IAAI,CAACG,GAAG;QAC1B;MACF;MACA,MAAMC,SAAS,GAAInB,aAAa,CAACoB,IAAI,CAACnB,SAAS,EAAE;QAC/CoB,MAAM,EAAE,EAAE;QACVnB,KAAK,EAAEA,KAAK,GAAG,IAAI;QACnBoB,UAAU,EAAE,KAAK;QACjBC,eAAe,EAAE,IAAI;QACrBC,KAAK,EAAE,IAAI;QACXC,YAAY,EAAE;UACZ,WAAW,EAAE,MAAM;UAAE,YAAY,EAAE,OAAO;UAAE,UAAU,EAAE,MAAM;UAAE,eAAe,EAAE,MAAM;UAAE,SAAS,EAAE;SACrG;QACDC,KAAK,EAAE9G,qBAAqB,GAAG;UAAE,MAAM,EAAE4F,OAAO;UAAE,KAAK,EAAEC,OAAO;UAAE,UAAU,EAAE;QAAO,CAAE,GAAG,EAAE;QAC5FkB,UAAU,EAAE,KAAK;QACjBC,cAAc,EAAE,kBAAkB;QAClCC,IAAI,EAAE;UAAE1B,UAAU,EAAEA;QAAU;OAC/B,CAAE;MACH,IAAI,CAACxF,IAAI,CAAC2C,IAAI,CAAC6D,SAAS,CAAC;MACzBA,SAAS,CAACW,OAAO,CAACC,SAAS,CAAEF,IAAS,IAAI;QACxCG,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEJ,IAAI,CAAC;QAC7C,IAAIzB,aAAa,EAAE;UACjBA,aAAa,CAACyB,IAAI,CAAC;QACrB;MACF,CAAC,CAAC;IACJ;IAEA;IACA;IACA;IAEAK,iBAAiBA,CAAChD,WAAmB;MACnC,OAAO,IAAI,CAACzF,IAAI,CAACiF,GAAG,CAAM/F,WAAW,GAAG,2CAA2C,GAAGuG,WAAW,CAAC;IACpG;IAEAiD,4BAA4BA,CAACC,GAAQ;MACnC,OAAO,IAAI,CAAC3I,IAAI,CAACC,IAAI,CAAMf,WAAW,GAAG,uCAAuC,EAAEyJ,GAAG,CAAC;IACxF;IAEAC,gCAAgCA,CAACC,MAAW;MAC1C,OAAO,IAAI,CAAC7I,IAAI,CAACiF,GAAG,CAAM/F,WAAW,GAAG,mDAAmD,GAAG2J,MAAM,CAAC;IACvG;IAEAC,MAAMA,CAACD,MAAW;MAChB,OAAO,IAAI,CAAC7I,IAAI,CAACC,IAAI,CAAMf,WAAW,GAAG,mCAAmC,GAAG2J,MAAM,EAAE,EAAE,CAAC;IAC5F;IAEAE,YAAYA,CAACC,QAAa;MACxB,OAAO,IAAI,CAAChJ,IAAI,CAACC,IAAI,CAAMf,WAAW,GAAG,iCAAiC,GAAG8J,QAAQ,EAAE,EAAE,CAAC;IAC5F;IAEAC,qBAAqBA,CAACJ,MAAW,EAAEK,OAAe;MAChD,OAAO,IAAI,CAAClJ,IAAI,CAACC,IAAI,CAAMf,WAAW,GAAG,wCAAwC,GAAG2J,MAAM,GAAG,WAAW,GAAGK,OAAO,EAAE,EAAE,CAAC;IACzH;IAEAC,oBAAoBA,CAAA;MAClB,OAAO,IAAI,CAACnJ,IAAI,CAACiF,GAAG,CAAM/F,WAAW,GAAG,mCAAmC,CAAC;IAC9E;IAEAkK,cAAcA,CAACC,SAAc;MAC3B,OAAO,IAAI,CAACrJ,IAAI,CAACC,IAAI,CAAMf,WAAW,GAAG,yBAAyB,EAAEmK,SAAS,CAAC;IAChF;IAEAC,kBAAkBA,CAACC,iBAAsB;MACvC,OAAO,IAAI,CAACvJ,IAAI,CAACC,IAAI,CAAMf,WAAW,GAAG,6BAA6B,EAAEqK,iBAAiB,CAAC;IAC5F;IAAC,QAAAC,CAAA,G;uBApUUpK,cAAc,EAAAqK,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;IAAA,QAAAC,EAAA,G;aAAd3K,cAAc;MAAA4K,OAAA,EAAd5K,cAAc,CAAA6K,IAAA;MAAAC,UAAA,EAFb;IAAM;;SAEP9K,cAAc;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}