{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { take } from 'rxjs/operators';\nimport { LessonStatus } from 'src/app/core/models/lesson.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"src/app/core/services/calendar.service\";\nimport * as i3 from \"src/app/core/services/lesson.service\";\nimport * as i4 from \"src/app/core/services/classroom.service\";\nimport * as i5 from \"src/app/core/services/general.service\";\nimport * as i6 from \"src/app/core/services/rating-and-report.service\";\nimport * as i7 from \"src/app/core/services/auth.service\";\nconst _c0 = [\"lessonRating\"];\nconst _c1 = a0 => ({\n  \"display\": a0\n});\nfunction LessonPopupComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function LessonPopupComponent_div_45_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.join());\n    });\n    i0.ɵɵtext(1, \"Join Lesson\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LessonPopupComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵlistener(\"click\", function LessonPopupComponent_div_46_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.navigateToClassroom());\n    });\n    i0.ɵɵtext(1, \"Go to Classroom\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LessonPopupComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵlistener(\"click\", function LessonPopupComponent_div_47_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.viewRating());\n    });\n    i0.ɵɵtext(1, \"View Rating\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LessonPopupComponent_span_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Request\\u00A0\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LessonPopupComponent_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31);\n    i0.ɵɵlistener(\"click\", function LessonPopupComponent_div_51_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      const changeStatusPopUp_r7 = i0.ɵɵreference(53);\n      return i0.ɵɵresetView(ctx_r2.openChangeStatusPopup(changeStatusPopUp_r7));\n    });\n    i0.ɵɵtext(1, \" Change Status \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LessonPopupComponent_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 32, 2);\n    i0.ɵɵlistener(\"click\", function LessonPopupComponent_div_58_Template_div_click_0_listener() {\n      const status_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.changeToStatus(status_r9));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const status_r9 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵattribute(\"id\", \"lesson-change-status-text-\" + ctx_r2.lesson.id);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", status_r9, \" \");\n  }\n}\nfunction LessonPopupComponent_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵlistener(\"click\", function LessonPopupComponent_div_59_Template_div_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleDelete());\n    });\n    i0.ɵɵtext(1, \" Delete\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LessonPopupComponent_app_lesson_rating_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"app-lesson-rating\", 34);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"users\", ctx_r2.classroom.users)(\"lesson\", ctx_r2.ratingToView.lesson)(\"lessonRatings\", ctx_r2.ratingToView.lessonBreakdown)(\"lessonUserRatings\", ctx_r2.ratingToView.userRatings)(\"mode\", \"view\");\n  }\n}\nfunction LessonPopupComponent_app_lesson_rating_79_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"app-lesson-rating\", 35);\n    i0.ɵɵlistener(\"ratingSubmitted\", function LessonPopupComponent_app_lesson_rating_79_Template_app_lesson_rating_ratingSubmitted_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.closeRating($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"mode\", \"create\")(\"lesson\", ctx_r2.lesson);\n  }\n}\nexport class LessonPopupComponent {\n  constructor(router, calendarService, lessonService, classroomService, generalService, ratingAndReportService, authService) {\n    this.router = router;\n    this.calendarService = calendarService;\n    this.lessonService = lessonService;\n    this.classroomService = classroomService;\n    this.generalService = generalService;\n    this.ratingAndReportService = ratingAndReportService;\n    this.authService = authService;\n    this.inScheduler = false;\n    this.lesson = {};\n    this.position = \"\";\n    this.right = \"\";\n    this.isScheduler = false;\n    this.closeLessonPopup = new EventEmitter();\n    this.lessonDeleted = new EventEmitter();\n    this.statusChanged = new EventEmitter();\n    this.reschedule = new EventEmitter();\n    this.role = {};\n    this.tryTodelete = false;\n    this.tryToChangeStatus = false;\n    this.recMsg = \"\";\n    this.showLessonPopup = false;\n    this.classroomId = \"\";\n    this.classroom = {};\n    this.showRating = false;\n    this.showRatingView = false;\n    this.ratingMode = \"create\";\n    this.showBg = true;\n    this.ratingToView = {};\n  }\n  ngOnInit() {\n    this.role = this.authService.getUserRole();\n    this.lessonService.getLessonClassroom(this.lesson.id).pipe(take(1)).subscribe(res => {\n      this.classroomId = res[0].id;\n      this.classroom = res[0];\n    });\n  }\n  getLessonRating() {}\n  closePopup() {\n    this.closeLessonPopup.emit({\n      show: false\n    });\n  }\n  toggleDelete(rec) {\n    this.tryTodelete = !this.tryTodelete;\n    this.recMsg = rec ? \"All recurrance lessons will also be deleted\" : \"\";\n  }\n  showChangeStatus() {\n    this.tryToChangeStatus = !this.tryToChangeStatus;\n  }\n  getLessonColor(status) {\n    return this.calendarService.getEventGradientColor(status);\n  }\n  closeLessonPopupOnClickOutside() {\n    document.addEventListener('click', event => {\n      setTimeout(() => {\n        let ignoreClickOnMeElement = document.getElementById('lesson-' + this.lesson.id);\n        var isClickInsideElement = ignoreClickOnMeElement?.contains(event.target);\n        if (!isClickInsideElement) {\n          // this.showLessonPopup = false;\n          this.generalService.slideOutElement('blur_bg');\n        }\n      }, 0);\n    });\n  }\n  openChangeStatusPopup(el) {\n    el.style.display = 'flex';\n  }\n  closeChangeStatusPopup(el) {\n    el.style.display = 'none';\n  }\n  getChangeStatuses(status) {\n    let statusesNamesToReturn = [];\n    if (status === LessonStatus.COMPLETED) {\n      statusesNamesToReturn.push(LessonStatus.CANCELED);\n      statusesNamesToReturn.push(LessonStatus.NO_SHOW);\n    } else if (status === LessonStatus.ARRANGED) {\n      statusesNamesToReturn.push(LessonStatus.COMPLETED);\n      statusesNamesToReturn.push(LessonStatus.CANCELED);\n      statusesNamesToReturn.push(LessonStatus.NO_SHOW);\n    } else if (status === LessonStatus.NO_SHOW) {\n      statusesNamesToReturn.push(LessonStatus.REARRANGE);\n      statusesNamesToReturn.push(LessonStatus.CANCELED);\n    } else {\n      statusesNamesToReturn.push(LessonStatus.REARRANGE);\n      statusesNamesToReturn.push(LessonStatus.NO_SHOW);\n    }\n    return statusesNamesToReturn;\n  }\n  viewRating() {\n    // this.lessonService.setLessonRatingListener(this.lesson)\n    this.ratingAndReportService.getRatingExtras(this.lesson, this.classroom).subscribe(res => {\n      console.log(res);\n      this.ratingToView = res[0];\n      console.log(this.ratingToView);\n      this.ratingMode = \"view\";\n      this.showRatingView = true;\n      this.generalService.slideElements(true, this.lessonRating.nativeElement.id);\n    });\n  }\n  changeToStatus(status) {\n    if (status === LessonStatus.COMPLETED) {\n      this.ratingAndReportService.ratingIsOpen = true;\n      // if (!this.inScheduler) {\n      this.ratingMode = \"create\";\n      this.showRating = true;\n      console.log(this.showBg);\n      if (!this.showBg) {\n        this.generalService.slideInElement(this.lessonRating.nativeElement.id);\n      } else {\n        this.generalService.slideElements(true, this.lessonRating.nativeElement.id);\n      }\n      // this.generalService.slideInElement(this.lessonRating.nativeElement.id!)\n      // } else {\n      //   console.log(\"in\")\n      // this.lessonService.setLessonRatingListener(this.lesson)\n      // }\n    } else if (status === LessonStatus.REARRANGE) {\n      if (this.isScheduler) {\n        this.reschedule.emit({\n          lesson: this.lesson\n        });\n      }\n    } else {\n      // TODO\n      this.lesson.status = status;\n      let lesson = {\n        id: this.lesson.id,\n        classroomId: this.lesson.classroomId,\n        status: status,\n        startingDate: this.generalService.toIsoString(this.lesson.startingDate),\n        duration: this.lesson.duration,\n        isRecccuring: this.lesson.isRecccuring,\n        answered: this.lesson.answered\n      };\n      this.lessonService.update(lesson).subscribe(res => {\n        this.statusChanged.emit();\n        this.lessonService.setUpdateListener(true);\n      });\n    }\n  }\n  join() {\n    console.log(this.lesson);\n    window.open(this.lesson.bbbLink, \"_blank\");\n  }\n  onReschedule() {\n    if (this.isScheduler) {\n      this.reschedule.emit({\n        lesson: this.lesson\n      });\n    } else {\n      this.lessonService.lessonToReschedule = this.lesson;\n      console.log(this.classroom);\n      this.classroomService.classroomToArrangeLesson = this.classroom;\n      this.router.navigate(['/reschedule']);\n    }\n  }\n  deleteLesson() {\n    // this.lessonService.setDeleteListener(true);\n    this.toggleDelete();\n    this.lessonDeleted.emit({\n      lesson: this.lesson\n    });\n    // this.showLessonPopup = false;\n  }\n  closeRating(event) {\n    if (event.rated) {\n      this.lesson.status = LessonStatus.COMPLETED;\n      this.closePopup();\n    }\n    this.ratingAndReportService.ratingIsOpen = false;\n    this.generalService.slideElements(false, this.lessonRating.nativeElement.id);\n  }\n  submitRating() {\n    this.ratingAndReportService.ratingIsOpen = false;\n  }\n  navigateToClassroom() {\n    console.log(\"/classrooms/lessons/\" + this.classroomId);\n    this.router.navigate([\"/classrooms/lessons/\" + this.classroomId]);\n  }\n  static #_ = this.ɵfac = function LessonPopupComponent_Factory(t) {\n    return new (t || LessonPopupComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.CalendarService), i0.ɵɵdirectiveInject(i3.LessonService), i0.ɵɵdirectiveInject(i4.ClassroomService), i0.ɵɵdirectiveInject(i5.GeneralService), i0.ɵɵdirectiveInject(i6.RatingAndReportService), i0.ɵɵdirectiveInject(i7.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: LessonPopupComponent,\n    selectors: [[\"app-lesson-popup\"]],\n    viewQuery: function LessonPopupComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.lessonRating = _t.first);\n      }\n    },\n    inputs: {\n      inScheduler: \"inScheduler\",\n      lesson: \"lesson\",\n      position: \"position\",\n      right: \"right\",\n      isScheduler: \"isScheduler\",\n      showBg: \"showBg\"\n    },\n    outputs: {\n      closeLessonPopup: \"closeLessonPopup\",\n      lessonDeleted: \"lessonDeleted\",\n      statusChanged: \"statusChanged\",\n      reschedule: \"reschedule\"\n    },\n    decls: 80,\n    vars: 36,\n    consts: [[\"changeStatusPopUp\", \"\"], [\"lessonRating\", \"\"], [\"changeStatusText\", \"\"], [1, \"lesson-popup\"], [1, \"lesson-popup-content\"], [1, \"popup-title\", 2, \"padding\", \"6px\"], [\"src\", \"/assets/icons/close.png\", 1, \"close-img\", \"close-img-abs\", \"hvr-glow\", 3, \"click\"], [1, \"lesson-popup-info\"], [1, \"lesson-popup-info-row\"], [1, \"item\"], [1, \"item\", \"right\"], [1, \"lesson-popup-buttons\"], [\"class\", \"light-purple-button\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"gray-border-button\", 3, \"click\", 4, \"ngIf\"], [1, \"gray-border-button\", \"border-purple\", 3, \"click\"], [4, \"ngIf\"], [\"class\", \"gray-border-button change-status\", 3, \"click\", 4, \"ngIf\"], [1, \"change-status-popup\"], [\"class\", \"text link-main-color\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"error-button\", 3, \"click\", 4, \"ngIf\"], [1, \"delete\", 3, \"ngStyle\"], [1, \"btns\"], [1, \"error-button\", 2, \"width\", \"50%\", 3, \"click\"], [1, \"white-button\", 2, \"width\", \"50%\", 3, \"click\"], [1, \"modal\", \"no-visibility\", \"p-0\", 2, \"top\", \"54.5%\", 3, \"id\"], [1, \"popup-title\", \"p-20\"], [1, \"p-0-20\"], [3, \"users\", \"lesson\", \"lessonRatings\", \"lessonUserRatings\", \"mode\", 4, \"ngIf\"], [3, \"mode\", \"lesson\", \"ratingSubmitted\", 4, \"ngIf\"], [1, \"light-purple-button\", 3, \"click\"], [1, \"gray-border-button\", 3, \"click\"], [1, \"gray-border-button\", \"change-status\", 3, \"click\"], [1, \"text\", \"link-main-color\", 3, \"click\"], [1, \"error-button\", 3, \"click\"], [3, \"users\", \"lesson\", \"lessonRatings\", \"lessonUserRatings\", \"mode\"], [3, \"ratingSubmitted\", \"mode\", \"lesson\"]],\n    template: function LessonPopupComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"div\");\n        i0.ɵɵtext(4, \"Lesson Details\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"img\", 6);\n        i0.ɵɵlistener(\"click\", function LessonPopupComponent_Template_img_click_5_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.closePopup());\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(6, \"div\", 7)(7, \"div\", 8)(8, \"div\", 9);\n        i0.ɵɵtext(9, \"Status:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(10, \"div\", 10);\n        i0.ɵɵtext(11);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(12, \"div\", 8)(13, \"div\", 9);\n        i0.ɵɵtext(14, \"Participants:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(15, \"div\", 10);\n        i0.ɵɵtext(16);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"div\", 8)(18, \"div\", 9);\n        i0.ɵɵtext(19, \"Language:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"div\", 10);\n        i0.ɵɵtext(21);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(22, \"div\", 8)(23, \"div\", 9);\n        i0.ɵɵtext(24, \"Level:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"div\", 10);\n        i0.ɵɵtext(26);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(27, \"div\", 8)(28, \"div\", 9);\n        i0.ɵɵtext(29, \"Starts at:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"div\", 10);\n        i0.ɵɵtext(31);\n        i0.ɵɵpipe(32, \"date\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(33, \"div\", 8)(34, \"div\", 9);\n        i0.ɵɵtext(35, \"Duration:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"div\", 10);\n        i0.ɵɵtext(37);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(38, \"div\", 8)(39, \"div\", 9);\n        i0.ɵɵtext(40, \"Date:\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(41, \"div\", 10);\n        i0.ɵɵtext(42);\n        i0.ɵɵpipe(43, \"date\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(44, \"div\", 11);\n        i0.ɵɵtemplate(45, LessonPopupComponent_div_45_Template, 2, 0, \"div\", 12)(46, LessonPopupComponent_div_46_Template, 2, 0, \"div\", 12)(47, LessonPopupComponent_div_47_Template, 2, 0, \"div\", 13);\n        i0.ɵɵelementStart(48, \"div\", 14);\n        i0.ɵɵlistener(\"click\", function LessonPopupComponent_Template_div_click_48_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onReschedule());\n        });\n        i0.ɵɵtemplate(49, LessonPopupComponent_span_49_Template, 2, 0, \"span\", 15);\n        i0.ɵɵtext(50, \"Reschedule \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(51, LessonPopupComponent_div_51_Template, 2, 0, \"div\", 16);\n        i0.ɵɵelementStart(52, \"div\", 17, 0)(54, \"div\", 5)(55, \"div\");\n        i0.ɵɵtext(56, \"Update Status\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(57, \"img\", 6);\n        i0.ɵɵlistener(\"click\", function LessonPopupComponent_Template_img_click_57_listener() {\n          i0.ɵɵrestoreView(_r1);\n          const changeStatusPopUp_r7 = i0.ɵɵreference(53);\n          return i0.ɵɵresetView(ctx.closeChangeStatusPopup(changeStatusPopUp_r7));\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(58, LessonPopupComponent_div_58_Template, 3, 2, \"div\", 18);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(59, LessonPopupComponent_div_59_Template, 2, 0, \"div\", 19);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(60, \"div\", 20);\n        i0.ɵɵtext(61, \" Are you sure you want to delete this class? \");\n        i0.ɵɵelement(62, \"br\");\n        i0.ɵɵtext(63);\n        i0.ɵɵelement(64, \"br\");\n        i0.ɵɵtext(65, \" You will not be able to undo this action \");\n        i0.ɵɵelementStart(66, \"div\", 21)(67, \"div\", 22);\n        i0.ɵɵlistener(\"click\", function LessonPopupComponent_Template_div_click_67_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.deleteLesson());\n        });\n        i0.ɵɵtext(68, \"Delete\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(69, \"div\", 23);\n        i0.ɵɵlistener(\"click\", function LessonPopupComponent_Template_div_click_69_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.toggleDelete());\n        });\n        i0.ɵɵtext(70, \"Cancel\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(71, \"div\", 24, 1)(73, \"div\", 25)(74, \"div\");\n        i0.ɵɵtext(75, \"Lesson Rating\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(76, \"img\", 6);\n        i0.ɵɵlistener(\"click\", function LessonPopupComponent_Template_img_click_76_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.closeRating({\n            rated: false\n          }));\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(77, \"div\", 26);\n        i0.ɵɵtemplate(78, LessonPopupComponent_app_lesson_rating_78_Template, 1, 5, \"app-lesson-rating\", 27)(79, LessonPopupComponent_app_lesson_rating_79_Template, 1, 2, \"app-lesson-rating\", 28);\n        i0.ɵɵelementEnd()();\n      }\n      if (rf & 2) {\n        i0.ɵɵstyleProp(\"background\", ctx.getLessonColor(ctx.lesson.status))(\"right\", ctx.right === \"\" ? \"initial\" : \"0\")(\"position\", ctx.position === \"\" ? \"absolute\" : \"relative\");\n        i0.ɵɵadvance(11);\n        i0.ɵɵtextInterpolate(ctx.lesson.status);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.lesson.title);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.lesson.classroom == null ? null : ctx.lesson.classroom.language);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.lesson.level);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate1(\"\", i0.ɵɵpipeBind3(32, 27, ctx.lesson.startingDate, \"h:mm a\", \"UTC\"), \" \");\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate(ctx.lesson.duration);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(43, 31, ctx.lesson.startingDate, \"dd/MM/yyyy\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.lesson.status === \"arranged\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !(ctx.router.url.includes(\"isTrial\") || ctx.router.url.includes(\"classrooms/lessons/\")) && (ctx.lesson.status === \"arranged\" || ctx.lesson.status === \"completed\"));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.lesson.status === \"completed\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.role == \"Student\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.lesson.status !== \"completed\" && ctx.role != \"Student\");\n        i0.ɵɵadvance();\n        i0.ɵɵattribute(\"id\", \"lesson-change-status-\" + ctx.lesson.id);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngForOf\", ctx.getChangeStatuses(ctx.lesson.status));\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.role != \"Student\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(34, _c1, !ctx.tryTodelete ? \"none\" : \"inline-block\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵtextInterpolate1(\" \", ctx.recMsg, \" \");\n        i0.ɵɵadvance(8);\n        i0.ɵɵpropertyInterpolate1(\"id\", \"lesson-rating-\", ctx.lesson.id, \"\");\n        i0.ɵɵadvance(7);\n        i0.ɵɵproperty(\"ngIf\", ctx.showRatingView);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.showRating);\n      }\n    },\n    styles: [\".lesson-popup[_ngcontent-%COMP%] {\\n  width: 260px;\\n  position: absolute;\\n  border-radius: 20px;\\n  z-index: 100;\\n  font-size: 15px;\\n  overflow-y: auto;\\n  padding: 6px;\\n}\\n.lesson-popup[_ngcontent-%COMP%]   .lesson-popup-content[_ngcontent-%COMP%] {\\n  background-clip: content-box;\\n  border-radius: 20px;\\n  background-color: white;\\n  display: flex;\\n  flex-direction: column;\\n}\\n.lesson-popup[_ngcontent-%COMP%]   .lesson-popup-content[_ngcontent-%COMP%]   .lesson-popup-info[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n.lesson-popup[_ngcontent-%COMP%]   .lesson-popup-content[_ngcontent-%COMP%]   .lesson-popup-info[_ngcontent-%COMP%]   .lesson-popup-info-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-evenly;\\n  padding: 5px 0;\\n  color: var(--main-color);\\n}\\n.lesson-popup[_ngcontent-%COMP%]   .lesson-popup-content[_ngcontent-%COMP%]   .lesson-popup-info[_ngcontent-%COMP%]   .lesson-popup-info-row[_ngcontent-%COMP%]   .item[_ngcontent-%COMP%] {\\n  width: 50%;\\n  white-space: pre-wrap;\\n}\\n.lesson-popup[_ngcontent-%COMP%]   .lesson-popup-content[_ngcontent-%COMP%]   .lesson-popup-info[_ngcontent-%COMP%]   .lesson-popup-info-row[_ngcontent-%COMP%]   .right[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n}\\n.lesson-popup[_ngcontent-%COMP%]   .lesson-popup-content[_ngcontent-%COMP%]   .lesson-popup-buttons[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n}\\n.lesson-popup[_ngcontent-%COMP%]   .lesson-popup-content[_ngcontent-%COMP%]   .lesson-popup-buttons[_ngcontent-%COMP%]   .light-purple-button[_ngcontent-%COMP%], .lesson-popup[_ngcontent-%COMP%]   .lesson-popup-content[_ngcontent-%COMP%]   .lesson-popup-buttons[_ngcontent-%COMP%]   .gray-border-button[_ngcontent-%COMP%], .lesson-popup[_ngcontent-%COMP%]   .lesson-popup-content[_ngcontent-%COMP%]   .lesson-popup-buttons[_ngcontent-%COMP%]   .error-button[_ngcontent-%COMP%] {\\n  font-size: 15px;\\n  padding: 5px;\\n  border-radius: 12px;\\n  box-sizing: border-box;\\n  text-align: center;\\n  width: 180px;\\n  margin-top: 15px;\\n}\\n\\n.delete[_ngcontent-%COMP%] {\\n  visibility: visible;\\n  box-sizing: border-box;\\n  font-weight: bold;\\n  padding: 13px;\\n}\\n\\n.change-status[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.change-status-popup[_ngcontent-%COMP%] {\\n  border-radius: 10px;\\n  display: none;\\n  background-color: white;\\n  margin-top: 10px;\\n  position: absolute;\\n  box-shadow: 0px -4px 4px rgba(0, 0, 0, 0.25);\\n  width: 200px;\\n  flex-direction: column;\\n}\\n.change-status-popup[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%] {\\n  padding: 10px;\\n  box-sizing: border-box;\\n  border-bottom: 1px solid #dcdcdc;\\n}\\n.change-status-popup[_ngcontent-%COMP%]   .text[_ngcontent-%COMP%]:last-child {\\n  border: 0;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "take", "LessonStatus", "i0", "ɵɵelementStart", "ɵɵlistener", "LessonPopupComponent_div_45_Template_div_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "join", "ɵɵtext", "ɵɵelementEnd", "LessonPopupComponent_div_46_Template_div_click_0_listener", "_r4", "navigateToClassroom", "LessonPopupComponent_div_47_Template_div_click_0_listener", "_r5", "viewRating", "LessonPopupComponent_div_51_Template_div_click_0_listener", "_r6", "changeStatusPopUp_r7", "ɵɵreference", "openChangeStatusPopup", "LessonPopupComponent_div_58_Template_div_click_0_listener", "status_r9", "_r8", "$implicit", "changeToStatus", "ɵɵadvance", "ɵɵtextInterpolate1", "LessonPopupComponent_div_59_Template_div_click_0_listener", "_r10", "toggleDelete", "ɵɵelement", "ɵɵproperty", "classroom", "users", "ratingToView", "lesson", "lessonBreakdown", "userRatings", "LessonPopupComponent_app_lesson_rating_79_Template_app_lesson_rating_ratingSubmitted_0_listener", "$event", "_r11", "closeRating", "LessonPopupComponent", "constructor", "router", "calendarService", "lessonService", "classroomService", "generalService", "ratingAndReportService", "authService", "inScheduler", "position", "right", "isScheduler", "close<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lessonDeleted", "statusChanged", "reschedule", "role", "tryTodelete", "tryToChangeStatus", "recMsg", "showLessonPopup", "classroomId", "showRating", "showRatingView", "ratingMode", "showBg", "ngOnInit", "getUserRole", "getLessonClassroom", "id", "pipe", "subscribe", "res", "getLessonRating", "closePopup", "emit", "show", "rec", "showChangeStatus", "getLessonColor", "status", "getEventGradientColor", "closeLessonPopupOnClickOutside", "document", "addEventListener", "event", "setTimeout", "ignoreClickOnMeElement", "getElementById", "isClickInsideElement", "contains", "target", "slideOutElement", "el", "style", "display", "closeChangeStatusPopup", "getChangeStatuses", "statusesNamesToReturn", "COMPLETED", "push", "CANCELED", "NO_SHOW", "ARRANGED", "REARRANGE", "getRatingExtras", "console", "log", "slideElements", "lessonRating", "nativeElement", "ratingIsOpen", "slideInElement", "startingDate", "toIsoString", "duration", "isRecccuring", "answered", "update", "setUpdateListener", "window", "open", "bbbLink", "onReschedule", "lessonToReschedule", "classroomToArrangeLesson", "navigate", "delete<PERSON><PERSON><PERSON>", "rated", "submitRating", "_", "ɵɵdirectiveInject", "i1", "Router", "i2", "CalendarService", "i3", "LessonService", "i4", "ClassroomService", "i5", "GeneralService", "i6", "RatingAndReportService", "i7", "AuthService", "_2", "selectors", "viewQuery", "LessonPopupComponent_Query", "rf", "ctx", "LessonPopupComponent_Template_img_click_5_listener", "_r1", "ɵɵtemplate", "LessonPopupComponent_div_45_Template", "LessonPopupComponent_div_46_Template", "LessonPopupComponent_div_47_Template", "LessonPopupComponent_Template_div_click_48_listener", "LessonPopupComponent_span_49_Template", "LessonPopupComponent_div_51_Template", "LessonPopupComponent_Template_img_click_57_listener", "LessonPopupComponent_div_58_Template", "LessonPopupComponent_div_59_Template", "LessonPopupComponent_Template_div_click_67_listener", "LessonPopupComponent_Template_div_click_69_listener", "LessonPopupComponent_Template_img_click_76_listener", "LessonPopupComponent_app_lesson_rating_78_Template", "LessonPopupComponent_app_lesson_rating_79_Template", "ɵɵstyleProp", "ɵɵtextInterpolate", "title", "language", "level", "ɵɵpipeBind3", "ɵɵpipeBind2", "url", "includes", "ɵɵpureFunction1", "_c1", "ɵɵpropertyInterpolate1"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\lesson\\lesson-popup\\lesson-popup.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\shared\\lesson\\lesson-popup\\lesson-popup.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { take } from 'rxjs/operators';\r\nimport { Classroom } from 'src/app/core/models/classroom.model';\r\nimport { Lesson, LessonPostRequest, LessonStatus, LessonUpdateRequest } from 'src/app/core/models/lesson.model';\r\nimport { LessonFullRating } from 'src/app/core/models/rating.model';\r\nimport { UserRole } from 'src/app/core/models/user.model';\r\nimport { AuthService } from 'src/app/core/services/auth.service';\r\nimport { CalendarService } from 'src/app/core/services/calendar.service';\r\nimport { ClassroomService } from 'src/app/core/services/classroom.service';\r\nimport { GeneralService } from 'src/app/core/services/general.service';\r\nimport { LessonService } from 'src/app/core/services/lesson.service';\r\nimport { RatingAndReportService } from 'src/app/core/services/rating-and-report.service';\r\n\r\n@Component({\r\n  selector: 'app-lesson-popup',\r\n  templateUrl: './lesson-popup.component.html',\r\n  styleUrls: ['./lesson-popup.component.scss']\r\n})\r\nexport class LessonPopupComponent implements OnInit {\r\n  @Input() inScheduler: boolean = false;\r\n  @Input() lesson: Lesson = {} as Lesson;\r\n  @Input() position: string = \"\";\r\n  @Input() right: string = \"\";\r\n  @Input() isScheduler: boolean = false;\r\n  @Output() closeLessonPopup = new EventEmitter<{ show: boolean }>();\r\n  @Output() lessonDeleted = new EventEmitter<{ lesson: Lesson }>();\r\n  @Output() statusChanged = new EventEmitter<any>();\r\n  @Output() reschedule = new EventEmitter<any>();\r\n  @ViewChild('lessonRating')\r\n  public lessonRating: any;\r\n  public role: UserRole = {} as UserRole\r\n  tryTodelete: boolean = false;\r\n  tryToChangeStatus: boolean = false;\r\n  recMsg: string = \"\";\r\n  showLessonPopup: boolean = false;\r\n  classroomId: string = \"\";\r\n  classroom: Classroom = {} as Classroom;\r\n  showRating: boolean = false;\r\n  showRatingView: boolean = false;\r\n  ratingMode = \"create\";\r\n  @Input() showBg: boolean = true;\r\n\r\n  constructor(\r\n    public router: Router,\r\n    private calendarService: CalendarService,\r\n    private lessonService: LessonService,\r\n    private classroomService: ClassroomService,\r\n    private generalService: GeneralService,\r\n    private ratingAndReportService: RatingAndReportService,\r\n    private authService: AuthService\r\n  ) { }\r\n\r\n  ngOnInit(): void {\r\n    this.role = this.authService.getUserRole();\r\n    this.lessonService.getLessonClassroom(this.lesson.id).pipe(take(1)).subscribe(res => {\r\n      this.classroomId = res[0].id\r\n      this.classroom = res[0];\r\n    })\r\n  }\r\n\r\n  getLessonRating() {\r\n  }\r\n\r\n  closePopup() {\r\n    this.closeLessonPopup.emit({ show: false });\r\n  }\r\n\r\n  toggleDelete(rec?: boolean) {\r\n    this.tryTodelete = !this.tryTodelete\r\n    this.recMsg = rec ? \"All recurrance lessons will also be deleted\" : \"\";\r\n  }\r\n\r\n  showChangeStatus() {\r\n    this.tryToChangeStatus = !this.tryToChangeStatus\r\n  }\r\n\r\n  getLessonColor(status: LessonStatus): string {\r\n    return this.calendarService.getEventGradientColor(status);\r\n  }\r\n\r\n  closeLessonPopupOnClickOutside() {\r\n    document.addEventListener('click', (event: any) => {\r\n      setTimeout(() => {// i dont know why..\r\n        let ignoreClickOnMeElement = document.getElementById('lesson-' + this.lesson.id);\r\n        var isClickInsideElement = ignoreClickOnMeElement?.contains(event.target);\r\n        if (!isClickInsideElement) {\r\n          // this.showLessonPopup = false;\r\n          this.generalService.slideOutElement('blur_bg')\r\n        }\r\n      }, 0);\r\n    });\r\n  }\r\n\r\n  openChangeStatusPopup(el: any) {\r\n    el.style.display = 'flex';\r\n  }\r\n\r\n  closeChangeStatusPopup(el: any) {\r\n    el.style.display = 'none';\r\n  }\r\n\r\n  getChangeStatuses(status: LessonStatus) {\r\n    let statusesNamesToReturn: LessonStatus[] = [];\r\n    if (status === LessonStatus.COMPLETED) {\r\n      statusesNamesToReturn.push(LessonStatus.CANCELED);\r\n      statusesNamesToReturn.push(LessonStatus.NO_SHOW);\r\n    }\r\n    else if (status === LessonStatus.ARRANGED) {\r\n      statusesNamesToReturn.push(LessonStatus.COMPLETED);\r\n      statusesNamesToReturn.push(LessonStatus.CANCELED);\r\n      statusesNamesToReturn.push(LessonStatus.NO_SHOW);\r\n    }\r\n    else if (status === LessonStatus.NO_SHOW) {\r\n      statusesNamesToReturn.push(LessonStatus.REARRANGE);\r\n      statusesNamesToReturn.push(LessonStatus.CANCELED);\r\n    } else {\r\n      statusesNamesToReturn.push(LessonStatus.REARRANGE);\r\n      statusesNamesToReturn.push(LessonStatus.NO_SHOW);\r\n    }\r\n    return statusesNamesToReturn;\r\n  }\r\n\r\n  ratingToView: LessonFullRating = {} as LessonFullRating;\r\n  viewRating() {\r\n    // this.lessonService.setLessonRatingListener(this.lesson)\r\n    this.ratingAndReportService.getRatingExtras(this.lesson, this.classroom).subscribe(res => {\r\n      console.log(res)\r\n      this.ratingToView = res[0]\r\n      console.log(this.ratingToView)\r\n      this.ratingMode = \"view\"\r\n      this.showRatingView = true;\r\n      this.generalService.slideElements(true, this.lessonRating.nativeElement.id!)\r\n    })\r\n\r\n  }\r\n\r\n  public changeToStatus(status: any) {\r\n    if (status === LessonStatus.COMPLETED) {\r\n      this.ratingAndReportService.ratingIsOpen = true;\r\n      // if (!this.inScheduler) {\r\n      this.ratingMode = \"create\"\r\n      this.showRating = true;\r\n      console.log(this.showBg)\r\n      if (!this.showBg) {\r\n        this.generalService.slideInElement(this.lessonRating.nativeElement.id!)\r\n      } else {\r\n        this.generalService.slideElements(true, this.lessonRating.nativeElement.id!)\r\n      }\r\n      // this.generalService.slideInElement(this.lessonRating.nativeElement.id!)\r\n      // } else {\r\n      //   console.log(\"in\")\r\n      // this.lessonService.setLessonRatingListener(this.lesson)\r\n      // }\r\n    } else if (status === LessonStatus.REARRANGE) {\r\n      if (this.isScheduler) {\r\n        this.reschedule.emit({ lesson: this.lesson })\r\n      }\r\n    } else {\r\n      // TODO\r\n      this.lesson.status = status;\r\n      let lesson: LessonUpdateRequest = {\r\n        id: this.lesson.id,\r\n        classroomId: this.lesson.classroomId,\r\n        status: status,\r\n        startingDate: this.generalService.toIsoString(this.lesson.startingDate),\r\n        duration: this.lesson.duration,\r\n        isRecccuring: this.lesson.isRecccuring,\r\n        answered: this.lesson.answered\r\n      }\r\n      this.lessonService.update(lesson).subscribe(res => {\r\n        this.statusChanged.emit();\r\n        this.lessonService.setUpdateListener(true);\r\n      })\r\n    }\r\n  }\r\n\r\n  join() {\r\n    console.log(this.lesson)\r\n    window.open(this.lesson.bbbLink, \"_blank\");\r\n  }\r\n\r\n  onReschedule() {\r\n    if (this.isScheduler) {\r\n      this.reschedule.emit({ lesson: this.lesson })\r\n    } else {\r\n      this.lessonService.lessonToReschedule = this.lesson;\r\n      console.log(this.classroom)\r\n      this.classroomService.classroomToArrangeLesson = this.classroom;\r\n      this.router.navigate(['/reschedule'])\r\n    }\r\n  }\r\n\r\n  deleteLesson() {\r\n    // this.lessonService.setDeleteListener(true);\r\n    this.toggleDelete();\r\n    this.lessonDeleted.emit({ lesson: this.lesson })\r\n    // this.showLessonPopup = false;\r\n  }\r\n\r\n  closeRating(event: any) {\r\n    if (event.rated) {\r\n      this.lesson.status = LessonStatus.COMPLETED\r\n      this.closePopup()\r\n    }\r\n    this.ratingAndReportService.ratingIsOpen = false;\r\n    this.generalService.slideElements(false, this.lessonRating.nativeElement.id)\r\n  }\r\n\r\n  submitRating() {\r\n    this.ratingAndReportService.ratingIsOpen = false;\r\n  }\r\n\r\n  navigateToClassroom() {\r\n    console.log(\"/classrooms/lessons/\" + this.classroomId)\r\n    this.router.navigate([\"/classrooms/lessons/\" + this.classroomId])\r\n  }\r\n}\r\n", "<div class=\"lesson-popup\" [style.background]=\"getLessonColor(lesson.status)\" [style.right]=\"right==='' ? 'initial': '0'\"\r\n    [style.position]=\"position==='' ? 'absolute': 'relative'\">\r\n    <div class=\"lesson-popup-content\">\r\n        <div class=\"popup-title\" style=\"padding:6px;\">\r\n            <div>Lesson Details</div>\r\n            <img (click)=\"closePopup()\" src=\"/assets/icons/close.png\" class=\"close-img close-img-abs hvr-glow\">\r\n        </div>\r\n        <div class=\"lesson-popup-info\">\r\n            <div class=\"lesson-popup-info-row\">\r\n                <div class=\"item\">Status:</div>\r\n                <div class=\"item right\">{{lesson.status}}</div>\r\n            </div>\r\n            <div class=\"lesson-popup-info-row\">\r\n                <div class=\"item\">Participants:</div>\r\n                <div class=\"item right\">{{lesson.title}}</div>\r\n            </div>\r\n            <div class=\"lesson-popup-info-row\">\r\n                <div class=\"item\">Language:</div>\r\n                <div class=\"item right\">{{lesson.classroom?.language}}</div>\r\n            </div>\r\n            <div class=\"lesson-popup-info-row\">\r\n                <div class=\"item\">Level:</div>\r\n                <div class=\"item right\">{{lesson.level}}</div>\r\n            </div>\r\n            <div class=\"lesson-popup-info-row\">\r\n                <div class=\"item\">Starts at:</div>\r\n                <div class=\"item right\">{{ lesson.startingDate | date:'h:mm a':'UTC' }}\r\n                </div>\r\n            </div>\r\n            <div class=\"lesson-popup-info-row\">\r\n                <div class=\"item\">Duration:</div>\r\n                <div class=\"item right\">{{lesson.duration}}</div>\r\n            </div>\r\n            <div class=\"lesson-popup-info-row\">\r\n                <div class=\"item\">Date:</div>\r\n                <div class=\"item right\">{{lesson.startingDate | date: 'dd/MM/yyyy'}}</div>\r\n            </div>\r\n        </div>\r\n        <div class=\"lesson-popup-buttons\">\r\n            <div *ngIf=\"lesson.status==='arranged'\" class=\"light-purple-button\" (click)=\"join()\">Join Lesson</div>\r\n            <div *ngIf=\"!(router.url.includes('isTrial') || router.url.includes('classrooms/lessons/')) && (lesson.status==='arranged' || \r\n            lesson.status==='completed')\" class=\"light-purple-button\"\r\n                (click)=\"navigateToClassroom()\">Go to\r\n                Classroom</div>\r\n            <!-- <div *ngIf=\"lesson.status==='completed'\" class=\"gray-border-button\">Edit Rating</div> -->\r\n            <div *ngIf=\"lesson.status==='completed'\" class=\"gray-border-button\" (click)=\"viewRating()\">View Rating</div>\r\n            <div class=\"gray-border-button border-purple\" (click)=\"onReschedule()\">\r\n                <span *ngIf=\"role=='Student'\">Request&nbsp;</span>Reschedule\r\n            </div>\r\n            <div *ngIf=\"lesson.status!=='completed' && role!='Student'\" class=\"gray-border-button change-status\"\r\n                (click)=\"openChangeStatusPopup(changeStatusPopUp)\">\r\n                Change Status\r\n            </div>\r\n            <div #changeStatusPopUp class=\"change-status-popup\" [attr.id]=\"'lesson-change-status-'+lesson.id\">\r\n                <div class=\"popup-title\" style=\"padding: 6px;\">\r\n                    <div>Update Status</div>\r\n                    <img (click)=\"closeChangeStatusPopup(changeStatusPopUp)\" src=\"/assets/icons/close.png\"\r\n                        class=\"close-img close-img-abs hvr-glow\">\r\n                </div>\r\n                <div #changeStatusText *ngFor=\"let status of getChangeStatuses(lesson.status)\"\r\n                    class=\"text link-main-color\" (click)=\"changeToStatus(status)\"\r\n                    [attr.id]=\"'lesson-change-status-text-'+lesson.id\">\r\n                    {{status}}\r\n                </div>\r\n            </div>\r\n            <div *ngIf=\"role!='Student'\" class=\"error-button\" (click)=\"toggleDelete()\">\r\n                Delete</div>\r\n        </div>\r\n        <div [ngStyle]=\"{'display':!tryTodelete ? 'none' : 'inline-block'}\" class=\"delete\">\r\n            Are you sure you want to delete this class?\r\n            <br>\r\n            {{recMsg}}\r\n            <br>\r\n            You will not be able to undo this action\r\n            <div class=\"btns\">\r\n                <div class=\"error-button\" style=\"width:50%\" (click)=\"deleteLesson()\">Delete</div>\r\n                <div class=\"white-button\" style=\"width:50%\" (click)=\"toggleDelete()\">Cancel</div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</div>\r\n\r\n<div #lessonRating id=\"lesson-rating-{{lesson.id}}\" class=\"modal no-visibility p-0\" style=\" top : 54.5%\">\r\n    <div class=\"popup-title p-20\">\r\n        <div>Lesson Rating</div>\r\n        <img (click)=\"closeRating({rated: false})\" src=\"/assets/icons/close.png\"\r\n            class=\"close-img close-img-abs hvr-glow\">\r\n    </div>\r\n    <div class=\"p-0-20\">\r\n        <app-lesson-rating *ngIf=\"showRatingView\" [users]=\"classroom.users\" [lesson]=\"ratingToView.lesson\"\r\n            [lessonRatings]=\"ratingToView.lessonBreakdown\" [lessonUserRatings]=\"ratingToView.userRatings\"\r\n            [mode]=\"'view'\">\r\n        </app-lesson-rating>\r\n        <app-lesson-rating *ngIf=\"showRating\" [mode]=\"'create'\" (ratingSubmitted)=\"closeRating($event)\"\r\n            [lesson]=\"lesson\">\r\n        </app-lesson-rating>\r\n    </div>\r\n</div>"], "mappings": "AAAA,SAAoBA,YAAY,QAA0C,eAAe;AAEzF,SAASC,IAAI,QAAQ,gBAAgB;AAErC,SAAoCC,YAAY,QAA6B,kCAAkC;;;;;;;;;;;;;;;;ICmCnGC,EAAA,CAAAC,cAAA,cAAqF;IAAjBD,EAAA,CAAAE,UAAA,mBAAAC,0DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAG,IAAA,EAAM;IAAA,EAAC;IAACT,EAAA,CAAAU,MAAA,kBAAW;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;;;IACtGX,EAAA,CAAAC,cAAA,cAEoC;IAAhCD,EAAA,CAAAE,UAAA,mBAAAU,0DAAA;MAAAZ,EAAA,CAAAI,aAAA,CAAAS,GAAA;MAAA,MAAAP,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAQ,mBAAA,EAAqB;IAAA,EAAC;IAACd,EAAA,CAAAU,MAAA,sBACvB;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;;;IAEnBX,EAAA,CAAAC,cAAA,cAA2F;IAAvBD,EAAA,CAAAE,UAAA,mBAAAa,0DAAA;MAAAf,EAAA,CAAAI,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAW,UAAA,EAAY;IAAA,EAAC;IAACjB,EAAA,CAAAU,MAAA,kBAAW;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;;IAExGX,EAAA,CAAAC,cAAA,WAA8B;IAAAD,EAAA,CAAAU,MAAA,oBAAa;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;;IAEtDX,EAAA,CAAAC,cAAA,cACuD;IAAnDD,EAAA,CAAAE,UAAA,mBAAAgB,0DAAA;MAAAlB,EAAA,CAAAI,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,MAAAa,oBAAA,GAAApB,EAAA,CAAAqB,WAAA;MAAA,OAAArB,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAgB,qBAAA,CAAAF,oBAAA,CAAwC;IAAA,EAAC;IAClDpB,EAAA,CAAAU,MAAA,sBACJ;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;;;IAOFX,EAAA,CAAAC,cAAA,iBAEuD;IADtBD,EAAA,CAAAE,UAAA,mBAAAqB,0DAAA;MAAA,MAAAC,SAAA,GAAAxB,EAAA,CAAAI,aAAA,CAAAqB,GAAA,EAAAC,SAAA;MAAA,MAAApB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAAqB,cAAA,CAAAH,SAAA,CAAsB;IAAA,EAAC;IAE7DxB,EAAA,CAAAU,MAAA,GACJ;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;;;IADFX,EAAA,CAAA4B,SAAA,GACJ;IADI5B,EAAA,CAAA6B,kBAAA,MAAAL,SAAA,MACJ;;;;;;IAEJxB,EAAA,CAAAC,cAAA,cAA2E;IAAzBD,EAAA,CAAAE,UAAA,mBAAA4B,0DAAA;MAAA9B,EAAA,CAAAI,aAAA,CAAA2B,IAAA;MAAA,MAAAzB,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAASF,MAAA,CAAA0B,YAAA,EAAc;IAAA,EAAC;IACtEhC,EAAA,CAAAU,MAAA,cAAM;IAAAV,EAAA,CAAAW,YAAA,EAAM;;;;;IAuBpBX,EAAA,CAAAiC,SAAA,4BAGoB;;;;IADhBjC,EAFsC,CAAAkC,UAAA,UAAA5B,MAAA,CAAA6B,SAAA,CAAAC,KAAA,CAAyB,WAAA9B,MAAA,CAAA+B,YAAA,CAAAC,MAAA,CAA+B,kBAAAhC,MAAA,CAAA+B,YAAA,CAAAE,eAAA,CAChD,sBAAAjC,MAAA,CAAA+B,YAAA,CAAAG,WAAA,CAA+C,gBAC9E;;;;;;IAEnBxC,EAAA,CAAAC,cAAA,4BACsB;IADkCD,EAAA,CAAAE,UAAA,6BAAAuC,gGAAAC,MAAA;MAAA1C,EAAA,CAAAI,aAAA,CAAAuC,IAAA;MAAA,MAAArC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAAP,EAAA,CAAAQ,WAAA,CAAmBF,MAAA,CAAAsC,WAAA,CAAAF,MAAA,CAAmB;IAAA,EAAC;IAE/F1C,EAAA,CAAAW,YAAA,EAAoB;;;;IADhBX,EADkC,CAAAkC,UAAA,kBAAiB,WAAA5B,MAAA,CAAAgC,MAAA,CAClC;;;AD3E7B,OAAM,MAAOO,oBAAoB;EAwB/BC,YACSC,MAAc,EACbC,eAAgC,EAChCC,aAA4B,EAC5BC,gBAAkC,EAClCC,cAA8B,EAC9BC,sBAA8C,EAC9CC,WAAwB;IANzB,KAAAN,MAAM,GAANA,MAAM;IACL,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACtB,KAAAC,WAAW,GAAXA,WAAW;IA9BZ,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAAhB,MAAM,GAAW,EAAY;IAC7B,KAAAiB,QAAQ,GAAW,EAAE;IACrB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,WAAW,GAAY,KAAK;IAC3B,KAAAC,gBAAgB,GAAG,IAAI7D,YAAY,EAAqB;IACxD,KAAA8D,aAAa,GAAG,IAAI9D,YAAY,EAAsB;IACtD,KAAA+D,aAAa,GAAG,IAAI/D,YAAY,EAAO;IACvC,KAAAgE,UAAU,GAAG,IAAIhE,YAAY,EAAO;IAGvC,KAAAiE,IAAI,GAAa,EAAc;IACtC,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAAC,iBAAiB,GAAY,KAAK;IAClC,KAAAC,MAAM,GAAW,EAAE;IACnB,KAAAC,eAAe,GAAY,KAAK;IAChC,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAhC,SAAS,GAAc,EAAe;IACtC,KAAAiC,UAAU,GAAY,KAAK;IAC3B,KAAAC,cAAc,GAAY,KAAK;IAC/B,KAAAC,UAAU,GAAG,QAAQ;IACZ,KAAAC,MAAM,GAAY,IAAI;IAkF/B,KAAAlC,YAAY,GAAqB,EAAsB;EAxEnD;EAEJmC,QAAQA,CAAA;IACN,IAAI,CAACV,IAAI,GAAG,IAAI,CAACT,WAAW,CAACoB,WAAW,EAAE;IAC1C,IAAI,CAACxB,aAAa,CAACyB,kBAAkB,CAAC,IAAI,CAACpC,MAAM,CAACqC,EAAE,CAAC,CAACC,IAAI,CAAC9E,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC+E,SAAS,CAACC,GAAG,IAAG;MAClF,IAAI,CAACX,WAAW,GAAGW,GAAG,CAAC,CAAC,CAAC,CAACH,EAAE;MAC5B,IAAI,CAACxC,SAAS,GAAG2C,GAAG,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC;EACJ;EAEAC,eAAeA,CAAA,GACf;EAEAC,UAAUA,CAAA;IACR,IAAI,CAACtB,gBAAgB,CAACuB,IAAI,CAAC;MAAEC,IAAI,EAAE;IAAK,CAAE,CAAC;EAC7C;EAEAlD,YAAYA,CAACmD,GAAa;IACxB,IAAI,CAACpB,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IACpC,IAAI,CAACE,MAAM,GAAGkB,GAAG,GAAG,6CAA6C,GAAG,EAAE;EACxE;EAEAC,gBAAgBA,CAAA;IACd,IAAI,CAACpB,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;EAClD;EAEAqB,cAAcA,CAACC,MAAoB;IACjC,OAAO,IAAI,CAACtC,eAAe,CAACuC,qBAAqB,CAACD,MAAM,CAAC;EAC3D;EAEAE,8BAA8BA,CAAA;IAC5BC,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAGC,KAAU,IAAI;MAChDC,UAAU,CAAC,MAAK;QACd,IAAIC,sBAAsB,GAAGJ,QAAQ,CAACK,cAAc,CAAC,SAAS,GAAG,IAAI,CAACxD,MAAM,CAACqC,EAAE,CAAC;QAChF,IAAIoB,oBAAoB,GAAGF,sBAAsB,EAAEG,QAAQ,CAACL,KAAK,CAACM,MAAM,CAAC;QACzE,IAAI,CAACF,oBAAoB,EAAE;UACzB;UACA,IAAI,CAAC5C,cAAc,CAAC+C,eAAe,CAAC,SAAS,CAAC;QAChD;MACF,CAAC,EAAE,CAAC,CAAC;IACP,CAAC,CAAC;EACJ;EAEA5E,qBAAqBA,CAAC6E,EAAO;IAC3BA,EAAE,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;EAC3B;EAEAC,sBAAsBA,CAACH,EAAO;IAC5BA,EAAE,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;EAC3B;EAEAE,iBAAiBA,CAACjB,MAAoB;IACpC,IAAIkB,qBAAqB,GAAmB,EAAE;IAC9C,IAAIlB,MAAM,KAAKvF,YAAY,CAAC0G,SAAS,EAAE;MACrCD,qBAAqB,CAACE,IAAI,CAAC3G,YAAY,CAAC4G,QAAQ,CAAC;MACjDH,qBAAqB,CAACE,IAAI,CAAC3G,YAAY,CAAC6G,OAAO,CAAC;IAClD,CAAC,MACI,IAAItB,MAAM,KAAKvF,YAAY,CAAC8G,QAAQ,EAAE;MACzCL,qBAAqB,CAACE,IAAI,CAAC3G,YAAY,CAAC0G,SAAS,CAAC;MAClDD,qBAAqB,CAACE,IAAI,CAAC3G,YAAY,CAAC4G,QAAQ,CAAC;MACjDH,qBAAqB,CAACE,IAAI,CAAC3G,YAAY,CAAC6G,OAAO,CAAC;IAClD,CAAC,MACI,IAAItB,MAAM,KAAKvF,YAAY,CAAC6G,OAAO,EAAE;MACxCJ,qBAAqB,CAACE,IAAI,CAAC3G,YAAY,CAAC+G,SAAS,CAAC;MAClDN,qBAAqB,CAACE,IAAI,CAAC3G,YAAY,CAAC4G,QAAQ,CAAC;IACnD,CAAC,MAAM;MACLH,qBAAqB,CAACE,IAAI,CAAC3G,YAAY,CAAC+G,SAAS,CAAC;MAClDN,qBAAqB,CAACE,IAAI,CAAC3G,YAAY,CAAC6G,OAAO,CAAC;IAClD;IACA,OAAOJ,qBAAqB;EAC9B;EAGAvF,UAAUA,CAAA;IACR;IACA,IAAI,CAACmC,sBAAsB,CAAC2D,eAAe,CAAC,IAAI,CAACzE,MAAM,EAAE,IAAI,CAACH,SAAS,CAAC,CAAC0C,SAAS,CAACC,GAAG,IAAG;MACvFkC,OAAO,CAACC,GAAG,CAACnC,GAAG,CAAC;MAChB,IAAI,CAACzC,YAAY,GAAGyC,GAAG,CAAC,CAAC,CAAC;MAC1BkC,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC5E,YAAY,CAAC;MAC9B,IAAI,CAACiC,UAAU,GAAG,MAAM;MACxB,IAAI,CAACD,cAAc,GAAG,IAAI;MAC1B,IAAI,CAAClB,cAAc,CAAC+D,aAAa,CAAC,IAAI,EAAE,IAAI,CAACC,YAAY,CAACC,aAAa,CAACzC,EAAG,CAAC;IAC9E,CAAC,CAAC;EAEJ;EAEOhD,cAAcA,CAAC2D,MAAW;IAC/B,IAAIA,MAAM,KAAKvF,YAAY,CAAC0G,SAAS,EAAE;MACrC,IAAI,CAACrD,sBAAsB,CAACiE,YAAY,GAAG,IAAI;MAC/C;MACA,IAAI,CAAC/C,UAAU,GAAG,QAAQ;MAC1B,IAAI,CAACF,UAAU,GAAG,IAAI;MACtB4C,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC1C,MAAM,CAAC;MACxB,IAAI,CAAC,IAAI,CAACA,MAAM,EAAE;QAChB,IAAI,CAACpB,cAAc,CAACmE,cAAc,CAAC,IAAI,CAACH,YAAY,CAACC,aAAa,CAACzC,EAAG,CAAC;MACzE,CAAC,MAAM;QACL,IAAI,CAACxB,cAAc,CAAC+D,aAAa,CAAC,IAAI,EAAE,IAAI,CAACC,YAAY,CAACC,aAAa,CAACzC,EAAG,CAAC;MAC9E;MACA;MACA;MACA;MACA;MACA;IACF,CAAC,MAAM,IAAIW,MAAM,KAAKvF,YAAY,CAAC+G,SAAS,EAAE;MAC5C,IAAI,IAAI,CAACrD,WAAW,EAAE;QACpB,IAAI,CAACI,UAAU,CAACoB,IAAI,CAAC;UAAE3C,MAAM,EAAE,IAAI,CAACA;QAAM,CAAE,CAAC;MAC/C;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAACA,MAAM,CAACgD,MAAM,GAAGA,MAAM;MAC3B,IAAIhD,MAAM,GAAwB;QAChCqC,EAAE,EAAE,IAAI,CAACrC,MAAM,CAACqC,EAAE;QAClBR,WAAW,EAAE,IAAI,CAAC7B,MAAM,CAAC6B,WAAW;QACpCmB,MAAM,EAAEA,MAAM;QACdiC,YAAY,EAAE,IAAI,CAACpE,cAAc,CAACqE,WAAW,CAAC,IAAI,CAAClF,MAAM,CAACiF,YAAY,CAAC;QACvEE,QAAQ,EAAE,IAAI,CAACnF,MAAM,CAACmF,QAAQ;QAC9BC,YAAY,EAAE,IAAI,CAACpF,MAAM,CAACoF,YAAY;QACtCC,QAAQ,EAAE,IAAI,CAACrF,MAAM,CAACqF;OACvB;MACD,IAAI,CAAC1E,aAAa,CAAC2E,MAAM,CAACtF,MAAM,CAAC,CAACuC,SAAS,CAACC,GAAG,IAAG;QAChD,IAAI,CAAClB,aAAa,CAACqB,IAAI,EAAE;QACzB,IAAI,CAAChC,aAAa,CAAC4E,iBAAiB,CAAC,IAAI,CAAC;MAC5C,CAAC,CAAC;IACJ;EACF;EAEApH,IAAIA,CAAA;IACFuG,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC3E,MAAM,CAAC;IACxBwF,MAAM,CAACC,IAAI,CAAC,IAAI,CAACzF,MAAM,CAAC0F,OAAO,EAAE,QAAQ,CAAC;EAC5C;EAEAC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACxE,WAAW,EAAE;MACpB,IAAI,CAACI,UAAU,CAACoB,IAAI,CAAC;QAAE3C,MAAM,EAAE,IAAI,CAACA;MAAM,CAAE,CAAC;IAC/C,CAAC,MAAM;MACL,IAAI,CAACW,aAAa,CAACiF,kBAAkB,GAAG,IAAI,CAAC5F,MAAM;MACnD0E,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC9E,SAAS,CAAC;MAC3B,IAAI,CAACe,gBAAgB,CAACiF,wBAAwB,GAAG,IAAI,CAAChG,SAAS;MAC/D,IAAI,CAACY,MAAM,CAACqF,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;IACvC;EACF;EAEAC,YAAYA,CAAA;IACV;IACA,IAAI,CAACrG,YAAY,EAAE;IACnB,IAAI,CAAC2B,aAAa,CAACsB,IAAI,CAAC;MAAE3C,MAAM,EAAE,IAAI,CAACA;IAAM,CAAE,CAAC;IAChD;EACF;EAEAM,WAAWA,CAAC+C,KAAU;IACpB,IAAIA,KAAK,CAAC2C,KAAK,EAAE;MACf,IAAI,CAAChG,MAAM,CAACgD,MAAM,GAAGvF,YAAY,CAAC0G,SAAS;MAC3C,IAAI,CAACzB,UAAU,EAAE;IACnB;IACA,IAAI,CAAC5B,sBAAsB,CAACiE,YAAY,GAAG,KAAK;IAChD,IAAI,CAAClE,cAAc,CAAC+D,aAAa,CAAC,KAAK,EAAE,IAAI,CAACC,YAAY,CAACC,aAAa,CAACzC,EAAE,CAAC;EAC9E;EAEA4D,YAAYA,CAAA;IACV,IAAI,CAACnF,sBAAsB,CAACiE,YAAY,GAAG,KAAK;EAClD;EAEAvG,mBAAmBA,CAAA;IACjBkG,OAAO,CAACC,GAAG,CAAC,sBAAsB,GAAG,IAAI,CAAC9C,WAAW,CAAC;IACtD,IAAI,CAACpB,MAAM,CAACqF,QAAQ,CAAC,CAAC,sBAAsB,GAAG,IAAI,CAACjE,WAAW,CAAC,CAAC;EACnE;EAAC,QAAAqE,CAAA,G;qBArMU3F,oBAAoB,EAAA7C,EAAA,CAAAyI,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA3I,EAAA,CAAAyI,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA7I,EAAA,CAAAyI,iBAAA,CAAAK,EAAA,CAAAC,aAAA,GAAA/I,EAAA,CAAAyI,iBAAA,CAAAO,EAAA,CAAAC,gBAAA,GAAAjJ,EAAA,CAAAyI,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAAnJ,EAAA,CAAAyI,iBAAA,CAAAW,EAAA,CAAAC,sBAAA,GAAArJ,EAAA,CAAAyI,iBAAA,CAAAa,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAApB3G,oBAAoB;IAAA4G,SAAA;IAAAC,SAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;QCfrB5J,EAJZ,CAAAC,cAAA,aAC8D,aACxB,aACgB,UACrC;QAAAD,EAAA,CAAAU,MAAA,qBAAc;QAAAV,EAAA,CAAAW,YAAA,EAAM;QACzBX,EAAA,CAAAC,cAAA,aAAmG;QAA9FD,EAAA,CAAAE,UAAA,mBAAA4J,mDAAA;UAAA9J,EAAA,CAAAI,aAAA,CAAA2J,GAAA;UAAA,OAAA/J,EAAA,CAAAQ,WAAA,CAASqJ,GAAA,CAAA7E,UAAA,EAAY;QAAA,EAAC;QAC/BhF,EADI,CAAAW,YAAA,EAAmG,EACjG;QAGEX,EAFR,CAAAC,cAAA,aAA+B,aACQ,aACb;QAAAD,EAAA,CAAAU,MAAA,cAAO;QAAAV,EAAA,CAAAW,YAAA,EAAM;QAC/BX,EAAA,CAAAC,cAAA,eAAwB;QAAAD,EAAA,CAAAU,MAAA,IAAiB;QAC7CV,EAD6C,CAAAW,YAAA,EAAM,EAC7C;QAEFX,EADJ,CAAAC,cAAA,cAAmC,cACb;QAAAD,EAAA,CAAAU,MAAA,qBAAa;QAAAV,EAAA,CAAAW,YAAA,EAAM;QACrCX,EAAA,CAAAC,cAAA,eAAwB;QAAAD,EAAA,CAAAU,MAAA,IAAgB;QAC5CV,EAD4C,CAAAW,YAAA,EAAM,EAC5C;QAEFX,EADJ,CAAAC,cAAA,cAAmC,cACb;QAAAD,EAAA,CAAAU,MAAA,iBAAS;QAAAV,EAAA,CAAAW,YAAA,EAAM;QACjCX,EAAA,CAAAC,cAAA,eAAwB;QAAAD,EAAA,CAAAU,MAAA,IAA8B;QAC1DV,EAD0D,CAAAW,YAAA,EAAM,EAC1D;QAEFX,EADJ,CAAAC,cAAA,cAAmC,cACb;QAAAD,EAAA,CAAAU,MAAA,cAAM;QAAAV,EAAA,CAAAW,YAAA,EAAM;QAC9BX,EAAA,CAAAC,cAAA,eAAwB;QAAAD,EAAA,CAAAU,MAAA,IAAgB;QAC5CV,EAD4C,CAAAW,YAAA,EAAM,EAC5C;QAEFX,EADJ,CAAAC,cAAA,cAAmC,cACb;QAAAD,EAAA,CAAAU,MAAA,kBAAU;QAAAV,EAAA,CAAAW,YAAA,EAAM;QAClCX,EAAA,CAAAC,cAAA,eAAwB;QAAAD,EAAA,CAAAU,MAAA,IACxB;;QACJV,EADI,CAAAW,YAAA,EAAM,EACJ;QAEFX,EADJ,CAAAC,cAAA,cAAmC,cACb;QAAAD,EAAA,CAAAU,MAAA,iBAAS;QAAAV,EAAA,CAAAW,YAAA,EAAM;QACjCX,EAAA,CAAAC,cAAA,eAAwB;QAAAD,EAAA,CAAAU,MAAA,IAAmB;QAC/CV,EAD+C,CAAAW,YAAA,EAAM,EAC/C;QAEFX,EADJ,CAAAC,cAAA,cAAmC,cACb;QAAAD,EAAA,CAAAU,MAAA,aAAK;QAAAV,EAAA,CAAAW,YAAA,EAAM;QAC7BX,EAAA,CAAAC,cAAA,eAAwB;QAAAD,EAAA,CAAAU,MAAA,IAA4C;;QAE5EV,EAF4E,CAAAW,YAAA,EAAM,EACxE,EACJ;QACNX,EAAA,CAAAC,cAAA,eAAkC;QAO9BD,EANA,CAAAgK,UAAA,KAAAC,oCAAA,kBAAqF,KAAAC,oCAAA,kBAGjD,KAAAC,oCAAA,kBAGuD;QAC3FnK,EAAA,CAAAC,cAAA,eAAuE;QAAzBD,EAAA,CAAAE,UAAA,mBAAAkK,oDAAA;UAAApK,EAAA,CAAAI,aAAA,CAAA2J,GAAA;UAAA,OAAA/J,EAAA,CAAAQ,WAAA,CAASqJ,GAAA,CAAA5B,YAAA,EAAc;QAAA,EAAC;QAClEjI,EAAA,CAAAgK,UAAA,KAAAK,qCAAA,mBAA8B;QAAoBrK,EAAA,CAAAU,MAAA,mBACtD;QAAAV,EAAA,CAAAW,YAAA,EAAM;QACNX,EAAA,CAAAgK,UAAA,KAAAM,oCAAA,kBACuD;QAK/CtK,EAFR,CAAAC,cAAA,kBAAkG,cAC/C,WACtC;QAAAD,EAAA,CAAAU,MAAA,qBAAa;QAAAV,EAAA,CAAAW,YAAA,EAAM;QACxBX,EAAA,CAAAC,cAAA,cAC6C;QADxCD,EAAA,CAAAE,UAAA,mBAAAqK,oDAAA;UAAAvK,EAAA,CAAAI,aAAA,CAAA2J,GAAA;UAAA,MAAA3I,oBAAA,GAAApB,EAAA,CAAAqB,WAAA;UAAA,OAAArB,EAAA,CAAAQ,WAAA,CAASqJ,GAAA,CAAAvD,sBAAA,CAAAlF,oBAAA,CAAyC;QAAA,EAAC;QAE5DpB,EAFI,CAAAW,YAAA,EAC6C,EAC3C;QACNX,EAAA,CAAAgK,UAAA,KAAAQ,oCAAA,kBAEuD;QAG3DxK,EAAA,CAAAW,YAAA,EAAM;QACNX,EAAA,CAAAgK,UAAA,KAAAS,oCAAA,kBAA2E;QAE/EzK,EAAA,CAAAW,YAAA,EAAM;QACNX,EAAA,CAAAC,cAAA,eAAmF;QAC/ED,EAAA,CAAAU,MAAA,qDACA;QAAAV,EAAA,CAAAiC,SAAA,UAAI;QACJjC,EAAA,CAAAU,MAAA,IACA;QAAAV,EAAA,CAAAiC,SAAA,UAAI;QACJjC,EAAA,CAAAU,MAAA,kDACA;QACIV,EADJ,CAAAC,cAAA,eAAkB,eACuD;QAAzBD,EAAA,CAAAE,UAAA,mBAAAwK,oDAAA;UAAA1K,EAAA,CAAAI,aAAA,CAAA2J,GAAA;UAAA,OAAA/J,EAAA,CAAAQ,WAAA,CAASqJ,GAAA,CAAAxB,YAAA,EAAc;QAAA,EAAC;QAACrI,EAAA,CAAAU,MAAA,cAAM;QAAAV,EAAA,CAAAW,YAAA,EAAM;QACjFX,EAAA,CAAAC,cAAA,eAAqE;QAAzBD,EAAA,CAAAE,UAAA,mBAAAyK,oDAAA;UAAA3K,EAAA,CAAAI,aAAA,CAAA2J,GAAA;UAAA,OAAA/J,EAAA,CAAAQ,WAAA,CAASqJ,GAAA,CAAA7H,YAAA,EAAc;QAAA,EAAC;QAAChC,EAAA,CAAAU,MAAA,cAAM;QAI3FV,EAJ2F,CAAAW,YAAA,EAAM,EAC/E,EACJ,EACJ,EACJ;QAIEX,EAFR,CAAAC,cAAA,kBAAyG,eACvE,WACrB;QAAAD,EAAA,CAAAU,MAAA,qBAAa;QAAAV,EAAA,CAAAW,YAAA,EAAM;QACxBX,EAAA,CAAAC,cAAA,cAC6C;QADxCD,EAAA,CAAAE,UAAA,mBAAA0K,oDAAA;UAAA5K,EAAA,CAAAI,aAAA,CAAA2J,GAAA;UAAA,OAAA/J,EAAA,CAAAQ,WAAA,CAASqJ,GAAA,CAAAjH,WAAA,CAAY;YAAA0F,KAAA,EAAQ;UAAK,CAAC,CAAC;QAAA,EAAC;QAE9CtI,EAFI,CAAAW,YAAA,EAC6C,EAC3C;QACNX,EAAA,CAAAC,cAAA,eAAoB;QAKhBD,EAJA,CAAAgK,UAAA,KAAAa,kDAAA,gCAEoB,KAAAC,kDAAA,gCAGE;QAG9B9K,EADI,CAAAW,YAAA,EAAM,EACJ;;;QAhGFX,EADsB,CAAA+K,WAAA,eAAAlB,GAAA,CAAAxE,cAAA,CAAAwE,GAAA,CAAAvH,MAAA,CAAAgD,MAAA,EAAkD,UAAAuE,GAAA,CAAArG,KAAA,0BAA4C,aAAAqG,GAAA,CAAAtG,QAAA,kCAC3D;QASrBvD,EAAA,CAAA4B,SAAA,IAAiB;QAAjB5B,EAAA,CAAAgL,iBAAA,CAAAnB,GAAA,CAAAvH,MAAA,CAAAgD,MAAA,CAAiB;QAIjBtF,EAAA,CAAA4B,SAAA,GAAgB;QAAhB5B,EAAA,CAAAgL,iBAAA,CAAAnB,GAAA,CAAAvH,MAAA,CAAA2I,KAAA,CAAgB;QAIhBjL,EAAA,CAAA4B,SAAA,GAA8B;QAA9B5B,EAAA,CAAAgL,iBAAA,CAAAnB,GAAA,CAAAvH,MAAA,CAAAH,SAAA,kBAAA0H,GAAA,CAAAvH,MAAA,CAAAH,SAAA,CAAA+I,QAAA,CAA8B;QAI9BlL,EAAA,CAAA4B,SAAA,GAAgB;QAAhB5B,EAAA,CAAAgL,iBAAA,CAAAnB,GAAA,CAAAvH,MAAA,CAAA6I,KAAA,CAAgB;QAIhBnL,EAAA,CAAA4B,SAAA,GACxB;QADwB5B,EAAA,CAAA6B,kBAAA,KAAA7B,EAAA,CAAAoL,WAAA,SAAAvB,GAAA,CAAAvH,MAAA,CAAAiF,YAAA,wBACxB;QAIwBvH,EAAA,CAAA4B,SAAA,GAAmB;QAAnB5B,EAAA,CAAAgL,iBAAA,CAAAnB,GAAA,CAAAvH,MAAA,CAAAmF,QAAA,CAAmB;QAInBzH,EAAA,CAAA4B,SAAA,GAA4C;QAA5C5B,EAAA,CAAAgL,iBAAA,CAAAhL,EAAA,CAAAqL,WAAA,SAAAxB,GAAA,CAAAvH,MAAA,CAAAiF,YAAA,gBAA4C;QAIlEvH,EAAA,CAAA4B,SAAA,GAAgC;QAAhC5B,EAAA,CAAAkC,UAAA,SAAA2H,GAAA,CAAAvH,MAAA,CAAAgD,MAAA,gBAAgC;QAChCtF,EAAA,CAAA4B,SAAA,EACqB;QADrB5B,EAAA,CAAAkC,UAAA,WAAA2H,GAAA,CAAA9G,MAAA,CAAAuI,GAAA,CAAAC,QAAA,eAAA1B,GAAA,CAAA9G,MAAA,CAAAuI,GAAA,CAAAC,QAAA,6BAAA1B,GAAA,CAAAvH,MAAA,CAAAgD,MAAA,mBAAAuE,GAAA,CAAAvH,MAAA,CAAAgD,MAAA,kBACqB;QAIrBtF,EAAA,CAAA4B,SAAA,EAAiC;QAAjC5B,EAAA,CAAAkC,UAAA,SAAA2H,GAAA,CAAAvH,MAAA,CAAAgD,MAAA,iBAAiC;QAE5BtF,EAAA,CAAA4B,SAAA,GAAqB;QAArB5B,EAAA,CAAAkC,UAAA,SAAA2H,GAAA,CAAA/F,IAAA,cAAqB;QAE1B9D,EAAA,CAAA4B,SAAA,GAAoD;QAApD5B,EAAA,CAAAkC,UAAA,SAAA2H,GAAA,CAAAvH,MAAA,CAAAgD,MAAA,oBAAAuE,GAAA,CAAA/F,IAAA,cAAoD;QAIN9D,EAAA,CAAA4B,SAAA,EAA6C;;QAMnD5B,EAAA,CAAA4B,SAAA,GAAmC;QAAnC5B,EAAA,CAAAkC,UAAA,YAAA2H,GAAA,CAAAtD,iBAAA,CAAAsD,GAAA,CAAAvH,MAAA,CAAAgD,MAAA,EAAmC;QAM3EtF,EAAA,CAAA4B,SAAA,EAAqB;QAArB5B,EAAA,CAAAkC,UAAA,SAAA2H,GAAA,CAAA/F,IAAA,cAAqB;QAG1B9D,EAAA,CAAA4B,SAAA,EAA8D;QAA9D5B,EAAA,CAAAkC,UAAA,YAAAlC,EAAA,CAAAwL,eAAA,KAAAC,GAAA,GAAA5B,GAAA,CAAA9F,WAAA,4BAA8D;QAG/D/D,EAAA,CAAA4B,SAAA,GACA;QADA5B,EAAA,CAAA6B,kBAAA,MAAAgI,GAAA,CAAA5F,MAAA,MACA;QAUOjE,EAAA,CAAA4B,SAAA,GAAgC;QAAhC5B,EAAA,CAAA0L,sBAAA,yBAAA7B,GAAA,CAAAvH,MAAA,CAAAqC,EAAA,KAAgC;QAOvB3E,EAAA,CAAA4B,SAAA,GAAoB;QAApB5B,EAAA,CAAAkC,UAAA,SAAA2H,GAAA,CAAAxF,cAAA,CAAoB;QAIpBrE,EAAA,CAAA4B,SAAA,EAAgB;QAAhB5B,EAAA,CAAAkC,UAAA,SAAA2H,GAAA,CAAAzF,UAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}