{"ast": null, "code": "import { coerceNumberProperty, coerceElement } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Directive, Input, Injectable, Optional, Inject, inject, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Output, ViewChild, SkipSelf, ElementRef, NgModule } from '@angular/core';\nimport { Subject, of, Observable, fromEvent, animationFrameScheduler, asapScheduler, Subscription, isObservable } from 'rxjs';\nimport { distinctUntilChanged, auditTime, filter, takeUntil, startWith, pairwise, switchMap, shareReplay } from 'rxjs/operators';\nimport * as i1 from '@angular/cdk/platform';\nimport { getRtlScrollAxisType, RtlScrollAxisType, supportsScrollBehavior, Platform } from '@angular/cdk/platform';\nimport { DOCUMENT } from '@angular/common';\nimport * as i2 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport * as i2$1 from '@angular/cdk/collections';\nimport { isDataSource, ArrayDataSource, _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy } from '@angular/cdk/collections';\n\n/** The injection token used to specify the virtual scrolling strategy. */\nconst _c0 = [\"contentWrapper\"];\nconst _c1 = [\"*\"];\nconst VIRTUAL_SCROLL_STRATEGY = /*#__PURE__*/new InjectionToken('VIRTUAL_SCROLL_STRATEGY');\n\n/** Virtual scrolling strategy for lists with items of known fixed size. */\nclass FixedSizeVirtualScrollStrategy {\n  /**\n   * @param itemSize The size of the items in the virtually scrolling list.\n   * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n   * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n   */\n  constructor(itemSize, minBufferPx, maxBufferPx) {\n    this._scrolledIndexChange = new Subject();\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    this.scrolledIndexChange = this._scrolledIndexChange.pipe(distinctUntilChanged());\n    /** The attached viewport. */\n    this._viewport = null;\n    this._itemSize = itemSize;\n    this._minBufferPx = minBufferPx;\n    this._maxBufferPx = maxBufferPx;\n  }\n  /**\n   * Attaches this scroll strategy to a viewport.\n   * @param viewport The viewport to attach this strategy to.\n   */\n  attach(viewport) {\n    this._viewport = viewport;\n    this._updateTotalContentSize();\n    this._updateRenderedRange();\n  }\n  /** Detaches this scroll strategy from the currently attached viewport. */\n  detach() {\n    this._scrolledIndexChange.complete();\n    this._viewport = null;\n  }\n  /**\n   * Update the item size and buffer size.\n   * @param itemSize The size of the items in the virtually scrolling list.\n   * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n   * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n   */\n  updateItemAndBufferSize(itemSize, minBufferPx, maxBufferPx) {\n    if (maxBufferPx < minBufferPx && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('CDK virtual scroll: maxBufferPx must be greater than or equal to minBufferPx');\n    }\n    this._itemSize = itemSize;\n    this._minBufferPx = minBufferPx;\n    this._maxBufferPx = maxBufferPx;\n    this._updateTotalContentSize();\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onContentScrolled() {\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onDataLengthChanged() {\n    this._updateTotalContentSize();\n    this._updateRenderedRange();\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onContentRendered() {\n    /* no-op */\n  }\n  /** @docs-private Implemented as part of VirtualScrollStrategy. */\n  onRenderedOffsetChanged() {\n    /* no-op */\n  }\n  /**\n   * Scroll to the offset for the given index.\n   * @param index The index of the element to scroll to.\n   * @param behavior The ScrollBehavior to use when scrolling.\n   */\n  scrollToIndex(index, behavior) {\n    if (this._viewport) {\n      this._viewport.scrollToOffset(index * this._itemSize, behavior);\n    }\n  }\n  /** Update the viewport's total content size. */\n  _updateTotalContentSize() {\n    if (!this._viewport) {\n      return;\n    }\n    this._viewport.setTotalContentSize(this._viewport.getDataLength() * this._itemSize);\n  }\n  /** Update the viewport's rendered range. */\n  _updateRenderedRange() {\n    if (!this._viewport) {\n      return;\n    }\n    const renderedRange = this._viewport.getRenderedRange();\n    const newRange = {\n      start: renderedRange.start,\n      end: renderedRange.end\n    };\n    const viewportSize = this._viewport.getViewportSize();\n    const dataLength = this._viewport.getDataLength();\n    let scrollOffset = this._viewport.measureScrollOffset();\n    // Prevent NaN as result when dividing by zero.\n    let firstVisibleIndex = this._itemSize > 0 ? scrollOffset / this._itemSize : 0;\n    // If user scrolls to the bottom of the list and data changes to a smaller list\n    if (newRange.end > dataLength) {\n      // We have to recalculate the first visible index based on new data length and viewport size.\n      const maxVisibleItems = Math.ceil(viewportSize / this._itemSize);\n      const newVisibleIndex = Math.max(0, Math.min(firstVisibleIndex, dataLength - maxVisibleItems));\n      // If first visible index changed we must update scroll offset to handle start/end buffers\n      // Current range must also be adjusted to cover the new position (bottom of new list).\n      if (firstVisibleIndex != newVisibleIndex) {\n        firstVisibleIndex = newVisibleIndex;\n        scrollOffset = newVisibleIndex * this._itemSize;\n        newRange.start = Math.floor(firstVisibleIndex);\n      }\n      newRange.end = Math.max(0, Math.min(dataLength, newRange.start + maxVisibleItems));\n    }\n    const startBuffer = scrollOffset - newRange.start * this._itemSize;\n    if (startBuffer < this._minBufferPx && newRange.start != 0) {\n      const expandStart = Math.ceil((this._maxBufferPx - startBuffer) / this._itemSize);\n      newRange.start = Math.max(0, newRange.start - expandStart);\n      newRange.end = Math.min(dataLength, Math.ceil(firstVisibleIndex + (viewportSize + this._minBufferPx) / this._itemSize));\n    } else {\n      const endBuffer = newRange.end * this._itemSize - (scrollOffset + viewportSize);\n      if (endBuffer < this._minBufferPx && newRange.end != dataLength) {\n        const expandEnd = Math.ceil((this._maxBufferPx - endBuffer) / this._itemSize);\n        if (expandEnd > 0) {\n          newRange.end = Math.min(dataLength, newRange.end + expandEnd);\n          newRange.start = Math.max(0, Math.floor(firstVisibleIndex - this._minBufferPx / this._itemSize));\n        }\n      }\n    }\n    this._viewport.setRenderedRange(newRange);\n    this._viewport.setRenderedContentOffset(this._itemSize * newRange.start);\n    this._scrolledIndexChange.next(Math.floor(firstVisibleIndex));\n  }\n}\n/**\n * Provider factory for `FixedSizeVirtualScrollStrategy` that simply extracts the already created\n * `FixedSizeVirtualScrollStrategy` from the given directive.\n * @param fixedSizeDir The instance of `CdkFixedSizeVirtualScroll` to extract the\n *     `FixedSizeVirtualScrollStrategy` from.\n */\nfunction _fixedSizeVirtualScrollStrategyFactory(fixedSizeDir) {\n  return fixedSizeDir._scrollStrategy;\n}\n/** A virtual scroll strategy that supports fixed-size items. */\nlet CdkFixedSizeVirtualScroll = /*#__PURE__*/(() => {\n  class CdkFixedSizeVirtualScroll {\n    constructor() {\n      this._itemSize = 20;\n      this._minBufferPx = 100;\n      this._maxBufferPx = 200;\n      /** The scroll strategy used by this directive. */\n      this._scrollStrategy = new FixedSizeVirtualScrollStrategy(this.itemSize, this.minBufferPx, this.maxBufferPx);\n    }\n    /** The size of the items in the list (in pixels). */\n    get itemSize() {\n      return this._itemSize;\n    }\n    set itemSize(value) {\n      this._itemSize = coerceNumberProperty(value);\n    }\n    /**\n     * The minimum amount of buffer rendered beyond the viewport (in pixels).\n     * If the amount of buffer dips below this number, more items will be rendered. Defaults to 100px.\n     */\n    get minBufferPx() {\n      return this._minBufferPx;\n    }\n    set minBufferPx(value) {\n      this._minBufferPx = coerceNumberProperty(value);\n    }\n    /**\n     * The number of pixels worth of buffer to render for when rendering new items. Defaults to 200px.\n     */\n    get maxBufferPx() {\n      return this._maxBufferPx;\n    }\n    set maxBufferPx(value) {\n      this._maxBufferPx = coerceNumberProperty(value);\n    }\n    ngOnChanges() {\n      this._scrollStrategy.updateItemAndBufferSize(this.itemSize, this.minBufferPx, this.maxBufferPx);\n    }\n    static #_ = this.ɵfac = function CdkFixedSizeVirtualScroll_Factory(t) {\n      return new (t || CdkFixedSizeVirtualScroll)();\n    };\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkFixedSizeVirtualScroll,\n      selectors: [[\"cdk-virtual-scroll-viewport\", \"itemSize\", \"\"]],\n      inputs: {\n        itemSize: \"itemSize\",\n        minBufferPx: \"minBufferPx\",\n        maxBufferPx: \"maxBufferPx\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: VIRTUAL_SCROLL_STRATEGY,\n        useFactory: _fixedSizeVirtualScrollStrategyFactory,\n        deps: [forwardRef(() => CdkFixedSizeVirtualScroll)]\n      }]), i0.ɵɵNgOnChangesFeature]\n    });\n  }\n  return CdkFixedSizeVirtualScroll;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Time in ms to throttle the scrolling events by default. */\nconst DEFAULT_SCROLL_TIME = 20;\n/**\n * Service contained all registered Scrollable references and emits an event when any one of the\n * Scrollable references emit a scrolled event.\n */\nlet ScrollDispatcher = /*#__PURE__*/(() => {\n  class ScrollDispatcher {\n    constructor(_ngZone, _platform, document) {\n      this._ngZone = _ngZone;\n      this._platform = _platform;\n      /** Subject for notifying that a registered scrollable reference element has been scrolled. */\n      this._scrolled = new Subject();\n      /** Keeps track of the global `scroll` and `resize` subscriptions. */\n      this._globalSubscription = null;\n      /** Keeps track of the amount of subscriptions to `scrolled`. Used for cleaning up afterwards. */\n      this._scrolledCount = 0;\n      /**\n       * Map of all the scrollable references that are registered with the service and their\n       * scroll event subscriptions.\n       */\n      this.scrollContainers = new Map();\n      this._document = document;\n    }\n    /**\n     * Registers a scrollable instance with the service and listens for its scrolled events. When the\n     * scrollable is scrolled, the service emits the event to its scrolled observable.\n     * @param scrollable Scrollable instance to be registered.\n     */\n    register(scrollable) {\n      if (!this.scrollContainers.has(scrollable)) {\n        this.scrollContainers.set(scrollable, scrollable.elementScrolled().subscribe(() => this._scrolled.next(scrollable)));\n      }\n    }\n    /**\n     * De-registers a Scrollable reference and unsubscribes from its scroll event observable.\n     * @param scrollable Scrollable instance to be deregistered.\n     */\n    deregister(scrollable) {\n      const scrollableReference = this.scrollContainers.get(scrollable);\n      if (scrollableReference) {\n        scrollableReference.unsubscribe();\n        this.scrollContainers.delete(scrollable);\n      }\n    }\n    /**\n     * Returns an observable that emits an event whenever any of the registered Scrollable\n     * references (or window, document, or body) fire a scrolled event. Can provide a time in ms\n     * to override the default \"throttle\" time.\n     *\n     * **Note:** in order to avoid hitting change detection for every scroll event,\n     * all of the events emitted from this stream will be run outside the Angular zone.\n     * If you need to update any data bindings as a result of a scroll event, you have\n     * to run the callback using `NgZone.run`.\n     */\n    scrolled(auditTimeInMs = DEFAULT_SCROLL_TIME) {\n      if (!this._platform.isBrowser) {\n        return of();\n      }\n      return new Observable(observer => {\n        if (!this._globalSubscription) {\n          this._addGlobalListener();\n        }\n        // In the case of a 0ms delay, use an observable without auditTime\n        // since it does add a perceptible delay in processing overhead.\n        const subscription = auditTimeInMs > 0 ? this._scrolled.pipe(auditTime(auditTimeInMs)).subscribe(observer) : this._scrolled.subscribe(observer);\n        this._scrolledCount++;\n        return () => {\n          subscription.unsubscribe();\n          this._scrolledCount--;\n          if (!this._scrolledCount) {\n            this._removeGlobalListener();\n          }\n        };\n      });\n    }\n    ngOnDestroy() {\n      this._removeGlobalListener();\n      this.scrollContainers.forEach((_, container) => this.deregister(container));\n      this._scrolled.complete();\n    }\n    /**\n     * Returns an observable that emits whenever any of the\n     * scrollable ancestors of an element are scrolled.\n     * @param elementOrElementRef Element whose ancestors to listen for.\n     * @param auditTimeInMs Time to throttle the scroll events.\n     */\n    ancestorScrolled(elementOrElementRef, auditTimeInMs) {\n      const ancestors = this.getAncestorScrollContainers(elementOrElementRef);\n      return this.scrolled(auditTimeInMs).pipe(filter(target => {\n        return !target || ancestors.indexOf(target) > -1;\n      }));\n    }\n    /** Returns all registered Scrollables that contain the provided element. */\n    getAncestorScrollContainers(elementOrElementRef) {\n      const scrollingContainers = [];\n      this.scrollContainers.forEach((_subscription, scrollable) => {\n        if (this._scrollableContainsElement(scrollable, elementOrElementRef)) {\n          scrollingContainers.push(scrollable);\n        }\n      });\n      return scrollingContainers;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n      return this._document.defaultView || window;\n    }\n    /** Returns true if the element is contained within the provided Scrollable. */\n    _scrollableContainsElement(scrollable, elementOrElementRef) {\n      let element = coerceElement(elementOrElementRef);\n      let scrollableElement = scrollable.getElementRef().nativeElement;\n      // Traverse through the element parents until we reach null, checking if any of the elements\n      // are the scrollable's element.\n      do {\n        if (element == scrollableElement) {\n          return true;\n        }\n      } while (element = element.parentElement);\n      return false;\n    }\n    /** Sets up the global scroll listeners. */\n    _addGlobalListener() {\n      this._globalSubscription = this._ngZone.runOutsideAngular(() => {\n        const window = this._getWindow();\n        return fromEvent(window.document, 'scroll').subscribe(() => this._scrolled.next());\n      });\n    }\n    /** Cleans up the global scroll listener. */\n    _removeGlobalListener() {\n      if (this._globalSubscription) {\n        this._globalSubscription.unsubscribe();\n        this._globalSubscription = null;\n      }\n    }\n    static #_ = this.ɵfac = function ScrollDispatcher_Factory(t) {\n      return new (t || ScrollDispatcher)(i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.Platform), i0.ɵɵinject(DOCUMENT, 8));\n    };\n    static #_2 = this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ScrollDispatcher,\n      factory: ScrollDispatcher.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return ScrollDispatcher;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Sends an event when the directive's element is scrolled. Registers itself with the\n * ScrollDispatcher service to include itself as part of its collection of scrolling events that it\n * can be listened to through the service.\n */\nlet CdkScrollable = /*#__PURE__*/(() => {\n  class CdkScrollable {\n    constructor(elementRef, scrollDispatcher, ngZone, dir) {\n      this.elementRef = elementRef;\n      this.scrollDispatcher = scrollDispatcher;\n      this.ngZone = ngZone;\n      this.dir = dir;\n      this._destroyed = new Subject();\n      this._elementScrolled = new Observable(observer => this.ngZone.runOutsideAngular(() => fromEvent(this.elementRef.nativeElement, 'scroll').pipe(takeUntil(this._destroyed)).subscribe(observer)));\n    }\n    ngOnInit() {\n      this.scrollDispatcher.register(this);\n    }\n    ngOnDestroy() {\n      this.scrollDispatcher.deregister(this);\n      this._destroyed.next();\n      this._destroyed.complete();\n    }\n    /** Returns observable that emits when a scroll event is fired on the host element. */\n    elementScrolled() {\n      return this._elementScrolled;\n    }\n    /** Gets the ElementRef for the viewport. */\n    getElementRef() {\n      return this.elementRef;\n    }\n    /**\n     * Scrolls to the specified offsets. This is a normalized version of the browser's native scrollTo\n     * method, since browsers are not consistent about what scrollLeft means in RTL. For this method\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param options specified the offsets to scroll to.\n     */\n    scrollTo(options) {\n      const el = this.elementRef.nativeElement;\n      const isRtl = this.dir && this.dir.value == 'rtl';\n      // Rewrite start & end offsets as right or left offsets.\n      if (options.left == null) {\n        options.left = isRtl ? options.end : options.start;\n      }\n      if (options.right == null) {\n        options.right = isRtl ? options.start : options.end;\n      }\n      // Rewrite the bottom offset as a top offset.\n      if (options.bottom != null) {\n        options.top = el.scrollHeight - el.clientHeight - options.bottom;\n      }\n      // Rewrite the right offset as a left offset.\n      if (isRtl && getRtlScrollAxisType() != RtlScrollAxisType.NORMAL) {\n        if (options.left != null) {\n          options.right = el.scrollWidth - el.clientWidth - options.left;\n        }\n        if (getRtlScrollAxisType() == RtlScrollAxisType.INVERTED) {\n          options.left = options.right;\n        } else if (getRtlScrollAxisType() == RtlScrollAxisType.NEGATED) {\n          options.left = options.right ? -options.right : options.right;\n        }\n      } else {\n        if (options.right != null) {\n          options.left = el.scrollWidth - el.clientWidth - options.right;\n        }\n      }\n      this._applyScrollToOptions(options);\n    }\n    _applyScrollToOptions(options) {\n      const el = this.elementRef.nativeElement;\n      if (supportsScrollBehavior()) {\n        el.scrollTo(options);\n      } else {\n        if (options.top != null) {\n          el.scrollTop = options.top;\n        }\n        if (options.left != null) {\n          el.scrollLeft = options.left;\n        }\n      }\n    }\n    /**\n     * Measures the scroll offset relative to the specified edge of the viewport. This method can be\n     * used instead of directly checking scrollLeft or scrollTop, since browsers are not consistent\n     * about what scrollLeft means in RTL. The values returned by this method are normalized such that\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param from The edge to measure from.\n     */\n    measureScrollOffset(from) {\n      const LEFT = 'left';\n      const RIGHT = 'right';\n      const el = this.elementRef.nativeElement;\n      if (from == 'top') {\n        return el.scrollTop;\n      }\n      if (from == 'bottom') {\n        return el.scrollHeight - el.clientHeight - el.scrollTop;\n      }\n      // Rewrite start & end as left or right offsets.\n      const isRtl = this.dir && this.dir.value == 'rtl';\n      if (from == 'start') {\n        from = isRtl ? RIGHT : LEFT;\n      } else if (from == 'end') {\n        from = isRtl ? LEFT : RIGHT;\n      }\n      if (isRtl && getRtlScrollAxisType() == RtlScrollAxisType.INVERTED) {\n        // For INVERTED, scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and\n        // 0 when scrolled all the way right.\n        if (from == LEFT) {\n          return el.scrollWidth - el.clientWidth - el.scrollLeft;\n        } else {\n          return el.scrollLeft;\n        }\n      } else if (isRtl && getRtlScrollAxisType() == RtlScrollAxisType.NEGATED) {\n        // For NEGATED, scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and\n        // 0 when scrolled all the way right.\n        if (from == LEFT) {\n          return el.scrollLeft + el.scrollWidth - el.clientWidth;\n        } else {\n          return -el.scrollLeft;\n        }\n      } else {\n        // For NORMAL, as well as non-RTL contexts, scrollLeft is 0 when scrolled all the way left and\n        // (scrollWidth - clientWidth) when scrolled all the way right.\n        if (from == LEFT) {\n          return el.scrollLeft;\n        } else {\n          return el.scrollWidth - el.clientWidth - el.scrollLeft;\n        }\n      }\n    }\n    static #_ = this.ɵfac = function CdkScrollable_Factory(t) {\n      return new (t || CdkScrollable)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ScrollDispatcher), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n    };\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkScrollable,\n      selectors: [[\"\", \"cdk-scrollable\", \"\"], [\"\", \"cdkScrollable\", \"\"]],\n      standalone: true\n    });\n  }\n  return CdkScrollable;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Time in ms to throttle the resize events by default. */\nconst DEFAULT_RESIZE_TIME = 20;\n/**\n * Simple utility for getting the bounds of the browser viewport.\n * @docs-private\n */\nlet ViewportRuler = /*#__PURE__*/(() => {\n  class ViewportRuler {\n    constructor(_platform, ngZone, document) {\n      this._platform = _platform;\n      /** Stream of viewport change events. */\n      this._change = new Subject();\n      /** Event listener that will be used to handle the viewport change events. */\n      this._changeListener = event => {\n        this._change.next(event);\n      };\n      this._document = document;\n      ngZone.runOutsideAngular(() => {\n        if (_platform.isBrowser) {\n          const window = this._getWindow();\n          // Note that bind the events ourselves, rather than going through something like RxJS's\n          // `fromEvent` so that we can ensure that they're bound outside of the NgZone.\n          window.addEventListener('resize', this._changeListener);\n          window.addEventListener('orientationchange', this._changeListener);\n        }\n        // Clear the cached position so that the viewport is re-measured next time it is required.\n        // We don't need to keep track of the subscription, because it is completed on destroy.\n        this.change().subscribe(() => this._viewportSize = null);\n      });\n    }\n    ngOnDestroy() {\n      if (this._platform.isBrowser) {\n        const window = this._getWindow();\n        window.removeEventListener('resize', this._changeListener);\n        window.removeEventListener('orientationchange', this._changeListener);\n      }\n      this._change.complete();\n    }\n    /** Returns the viewport's width and height. */\n    getViewportSize() {\n      if (!this._viewportSize) {\n        this._updateViewportSize();\n      }\n      const output = {\n        width: this._viewportSize.width,\n        height: this._viewportSize.height\n      };\n      // If we're not on a browser, don't cache the size since it'll be mocked out anyway.\n      if (!this._platform.isBrowser) {\n        this._viewportSize = null;\n      }\n      return output;\n    }\n    /** Gets a DOMRect for the viewport's bounds. */\n    getViewportRect() {\n      // Use the document element's bounding rect rather than the window scroll properties\n      // (e.g. pageYOffset, scrollY) due to in issue in Chrome and IE where window scroll\n      // properties and client coordinates (boundingClientRect, clientX/Y, etc.) are in different\n      // conceptual viewports. Under most circumstances these viewports are equivalent, but they\n      // can disagree when the page is pinch-zoomed (on devices that support touch).\n      // See https://bugs.chromium.org/p/chromium/issues/detail?id=489206#c4\n      // We use the documentElement instead of the body because, by default (without a css reset)\n      // browsers typically give the document body an 8px margin, which is not included in\n      // getBoundingClientRect().\n      const scrollPosition = this.getViewportScrollPosition();\n      const {\n        width,\n        height\n      } = this.getViewportSize();\n      return {\n        top: scrollPosition.top,\n        left: scrollPosition.left,\n        bottom: scrollPosition.top + height,\n        right: scrollPosition.left + width,\n        height,\n        width\n      };\n    }\n    /** Gets the (top, left) scroll position of the viewport. */\n    getViewportScrollPosition() {\n      // While we can get a reference to the fake document\n      // during SSR, it doesn't have getBoundingClientRect.\n      if (!this._platform.isBrowser) {\n        return {\n          top: 0,\n          left: 0\n        };\n      }\n      // The top-left-corner of the viewport is determined by the scroll position of the document\n      // body, normally just (scrollLeft, scrollTop). However, Chrome and Firefox disagree about\n      // whether `document.body` or `document.documentElement` is the scrolled element, so reading\n      // `scrollTop` and `scrollLeft` is inconsistent. However, using the bounding rect of\n      // `document.documentElement` works consistently, where the `top` and `left` values will\n      // equal negative the scroll position.\n      const document = this._document;\n      const window = this._getWindow();\n      const documentElement = document.documentElement;\n      const documentRect = documentElement.getBoundingClientRect();\n      const top = -documentRect.top || document.body.scrollTop || window.scrollY || documentElement.scrollTop || 0;\n      const left = -documentRect.left || document.body.scrollLeft || window.scrollX || documentElement.scrollLeft || 0;\n      return {\n        top,\n        left\n      };\n    }\n    /**\n     * Returns a stream that emits whenever the size of the viewport changes.\n     * This stream emits outside of the Angular zone.\n     * @param throttleTime Time in milliseconds to throttle the stream.\n     */\n    change(throttleTime = DEFAULT_RESIZE_TIME) {\n      return throttleTime > 0 ? this._change.pipe(auditTime(throttleTime)) : this._change;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n      return this._document.defaultView || window;\n    }\n    /** Updates the cached viewport size. */\n    _updateViewportSize() {\n      const window = this._getWindow();\n      this._viewportSize = this._platform.isBrowser ? {\n        width: window.innerWidth,\n        height: window.innerHeight\n      } : {\n        width: 0,\n        height: 0\n      };\n    }\n    static #_ = this.ɵfac = function ViewportRuler_Factory(t) {\n      return new (t || ViewportRuler)(i0.ɵɵinject(i1.Platform), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(DOCUMENT, 8));\n    };\n    static #_2 = this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: ViewportRuler,\n      factory: ViewportRuler.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return ViewportRuler;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nconst VIRTUAL_SCROLLABLE = /*#__PURE__*/new InjectionToken('VIRTUAL_SCROLLABLE');\n/**\n * Extending the {@link CdkScrollable} to be used as scrolling container for virtual scrolling.\n */\nlet CdkVirtualScrollable = /*#__PURE__*/(() => {\n  class CdkVirtualScrollable extends CdkScrollable {\n    constructor(elementRef, scrollDispatcher, ngZone, dir) {\n      super(elementRef, scrollDispatcher, ngZone, dir);\n    }\n    /**\n     * Measure the viewport size for the provided orientation.\n     *\n     * @param orientation The orientation to measure the size from.\n     */\n    measureViewportSize(orientation) {\n      const viewportEl = this.elementRef.nativeElement;\n      return orientation === 'horizontal' ? viewportEl.clientWidth : viewportEl.clientHeight;\n    }\n    static #_ = this.ɵfac = function CdkVirtualScrollable_Factory(t) {\n      return new (t || CdkVirtualScrollable)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ScrollDispatcher), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n    };\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkVirtualScrollable,\n      features: [i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return CdkVirtualScrollable;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Checks if the given ranges are equal. */\nfunction rangesEqual(r1, r2) {\n  return r1.start == r2.start && r1.end == r2.end;\n}\n/**\n * Scheduler to be used for scroll events. Needs to fall back to\n * something that doesn't rely on requestAnimationFrame on environments\n * that don't support it (e.g. server-side rendering).\n */\nconst SCROLL_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\n/** A viewport that virtualizes its scrolling with the help of `CdkVirtualForOf`. */\nlet CdkVirtualScrollViewport = /*#__PURE__*/(() => {\n  class CdkVirtualScrollViewport extends CdkVirtualScrollable {\n    /** The direction the viewport scrolls. */\n    get orientation() {\n      return this._orientation;\n    }\n    set orientation(orientation) {\n      if (this._orientation !== orientation) {\n        this._orientation = orientation;\n        this._calculateSpacerSize();\n      }\n    }\n    constructor(elementRef, _changeDetectorRef, ngZone, _scrollStrategy, dir, scrollDispatcher, viewportRuler, scrollable) {\n      super(elementRef, scrollDispatcher, ngZone, dir);\n      this.elementRef = elementRef;\n      this._changeDetectorRef = _changeDetectorRef;\n      this._scrollStrategy = _scrollStrategy;\n      this.scrollable = scrollable;\n      this._platform = inject(Platform);\n      /** Emits when the viewport is detached from a CdkVirtualForOf. */\n      this._detachedSubject = new Subject();\n      /** Emits when the rendered range changes. */\n      this._renderedRangeSubject = new Subject();\n      this._orientation = 'vertical';\n      /**\n       * Whether rendered items should persist in the DOM after scrolling out of view. By default, items\n       * will be removed.\n       */\n      this.appendOnly = false;\n      // Note: we don't use the typical EventEmitter here because we need to subscribe to the scroll\n      // strategy lazily (i.e. only if the user is actually listening to the events). We do this because\n      // depending on how the strategy calculates the scrolled index, it may come at a cost to\n      // performance.\n      /** Emits when the index of the first element visible in the viewport changes. */\n      this.scrolledIndexChange = new Observable(observer => this._scrollStrategy.scrolledIndexChange.subscribe(index => Promise.resolve().then(() => this.ngZone.run(() => observer.next(index)))));\n      /** A stream that emits whenever the rendered range changes. */\n      this.renderedRangeStream = this._renderedRangeSubject;\n      /**\n       * The total size of all content (in pixels), including content that is not currently rendered.\n       */\n      this._totalContentSize = 0;\n      /** A string representing the `style.width` property value to be used for the spacer element. */\n      this._totalContentWidth = '';\n      /** A string representing the `style.height` property value to be used for the spacer element. */\n      this._totalContentHeight = '';\n      /** The currently rendered range of indices. */\n      this._renderedRange = {\n        start: 0,\n        end: 0\n      };\n      /** The length of the data bound to this viewport (in number of items). */\n      this._dataLength = 0;\n      /** The size of the viewport (in pixels). */\n      this._viewportSize = 0;\n      /** The last rendered content offset that was set. */\n      this._renderedContentOffset = 0;\n      /**\n       * Whether the last rendered content offset was to the end of the content (and therefore needs to\n       * be rewritten as an offset to the start of the content).\n       */\n      this._renderedContentOffsetNeedsRewrite = false;\n      /** Whether there is a pending change detection cycle. */\n      this._isChangeDetectionPending = false;\n      /** A list of functions to run after the next change detection cycle. */\n      this._runAfterChangeDetection = [];\n      /** Subscription to changes in the viewport size. */\n      this._viewportChanges = Subscription.EMPTY;\n      if (!_scrollStrategy && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('Error: cdk-virtual-scroll-viewport requires the \"itemSize\" property to be set.');\n      }\n      this._viewportChanges = viewportRuler.change().subscribe(() => {\n        this.checkViewportSize();\n      });\n      if (!this.scrollable) {\n        // No scrollable is provided, so the virtual-scroll-viewport needs to become a scrollable\n        this.elementRef.nativeElement.classList.add('cdk-virtual-scrollable');\n        this.scrollable = this;\n      }\n    }\n    ngOnInit() {\n      // Scrolling depends on the element dimensions which we can't get during SSR.\n      if (!this._platform.isBrowser) {\n        return;\n      }\n      if (this.scrollable === this) {\n        super.ngOnInit();\n      }\n      // It's still too early to measure the viewport at this point. Deferring with a promise allows\n      // the Viewport to be rendered with the correct size before we measure. We run this outside the\n      // zone to avoid causing more change detection cycles. We handle the change detection loop\n      // ourselves instead.\n      this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n        this._measureViewportSize();\n        this._scrollStrategy.attach(this);\n        this.scrollable.elementScrolled().pipe(\n        // Start off with a fake scroll event so we properly detect our initial position.\n        startWith(null),\n        // Collect multiple events into one until the next animation frame. This way if\n        // there are multiple scroll events in the same frame we only need to recheck\n        // our layout once.\n        auditTime(0, SCROLL_SCHEDULER),\n        // Usually `elementScrolled` is completed when the scrollable is destroyed, but\n        // that may not be the case if a `CdkVirtualScrollableElement` is used so we have\n        // to unsubscribe here just in case.\n        takeUntil(this._destroyed)).subscribe(() => this._scrollStrategy.onContentScrolled());\n        this._markChangeDetectionNeeded();\n      }));\n    }\n    ngOnDestroy() {\n      this.detach();\n      this._scrollStrategy.detach();\n      // Complete all subjects\n      this._renderedRangeSubject.complete();\n      this._detachedSubject.complete();\n      this._viewportChanges.unsubscribe();\n      super.ngOnDestroy();\n    }\n    /** Attaches a `CdkVirtualScrollRepeater` to this viewport. */\n    attach(forOf) {\n      if (this._forOf && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('CdkVirtualScrollViewport is already attached.');\n      }\n      // Subscribe to the data stream of the CdkVirtualForOf to keep track of when the data length\n      // changes. Run outside the zone to avoid triggering change detection, since we're managing the\n      // change detection loop ourselves.\n      this.ngZone.runOutsideAngular(() => {\n        this._forOf = forOf;\n        this._forOf.dataStream.pipe(takeUntil(this._detachedSubject)).subscribe(data => {\n          const newLength = data.length;\n          if (newLength !== this._dataLength) {\n            this._dataLength = newLength;\n            this._scrollStrategy.onDataLengthChanged();\n          }\n          this._doChangeDetection();\n        });\n      });\n    }\n    /** Detaches the current `CdkVirtualForOf`. */\n    detach() {\n      this._forOf = null;\n      this._detachedSubject.next();\n    }\n    /** Gets the length of the data bound to this viewport (in number of items). */\n    getDataLength() {\n      return this._dataLength;\n    }\n    /** Gets the size of the viewport (in pixels). */\n    getViewportSize() {\n      return this._viewportSize;\n    }\n    // TODO(mmalerba): This is technically out of sync with what's really rendered until a render\n    // cycle happens. I'm being careful to only call it after the render cycle is complete and before\n    // setting it to something else, but its error prone and should probably be split into\n    // `pendingRange` and `renderedRange`, the latter reflecting whats actually in the DOM.\n    /** Get the current rendered range of items. */\n    getRenderedRange() {\n      return this._renderedRange;\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n      return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n    }\n    /**\n     * Sets the total size of all content (in pixels), including content that is not currently\n     * rendered.\n     */\n    setTotalContentSize(size) {\n      if (this._totalContentSize !== size) {\n        this._totalContentSize = size;\n        this._calculateSpacerSize();\n        this._markChangeDetectionNeeded();\n      }\n    }\n    /** Sets the currently rendered range of indices. */\n    setRenderedRange(range) {\n      if (!rangesEqual(this._renderedRange, range)) {\n        if (this.appendOnly) {\n          range = {\n            start: 0,\n            end: Math.max(this._renderedRange.end, range.end)\n          };\n        }\n        this._renderedRangeSubject.next(this._renderedRange = range);\n        this._markChangeDetectionNeeded(() => this._scrollStrategy.onContentRendered());\n      }\n    }\n    /**\n     * Gets the offset from the start of the viewport to the start of the rendered data (in pixels).\n     */\n    getOffsetToRenderedContentStart() {\n      return this._renderedContentOffsetNeedsRewrite ? null : this._renderedContentOffset;\n    }\n    /**\n     * Sets the offset from the start of the viewport to either the start or end of the rendered data\n     * (in pixels).\n     */\n    setRenderedContentOffset(offset, to = 'to-start') {\n      // In appendOnly, we always start from the top\n      offset = this.appendOnly && to === 'to-start' ? 0 : offset;\n      // For a horizontal viewport in a right-to-left language we need to translate along the x-axis\n      // in the negative direction.\n      const isRtl = this.dir && this.dir.value == 'rtl';\n      const isHorizontal = this.orientation == 'horizontal';\n      const axis = isHorizontal ? 'X' : 'Y';\n      const axisDirection = isHorizontal && isRtl ? -1 : 1;\n      let transform = `translate${axis}(${Number(axisDirection * offset)}px)`;\n      this._renderedContentOffset = offset;\n      if (to === 'to-end') {\n        transform += ` translate${axis}(-100%)`;\n        // The viewport should rewrite this as a `to-start` offset on the next render cycle. Otherwise\n        // elements will appear to expand in the wrong direction (e.g. `mat-expansion-panel` would\n        // expand upward).\n        this._renderedContentOffsetNeedsRewrite = true;\n      }\n      if (this._renderedContentTransform != transform) {\n        // We know this value is safe because we parse `offset` with `Number()` before passing it\n        // into the string.\n        this._renderedContentTransform = transform;\n        this._markChangeDetectionNeeded(() => {\n          if (this._renderedContentOffsetNeedsRewrite) {\n            this._renderedContentOffset -= this.measureRenderedContentSize();\n            this._renderedContentOffsetNeedsRewrite = false;\n            this.setRenderedContentOffset(this._renderedContentOffset);\n          } else {\n            this._scrollStrategy.onRenderedOffsetChanged();\n          }\n        });\n      }\n    }\n    /**\n     * Scrolls to the given offset from the start of the viewport. Please note that this is not always\n     * the same as setting `scrollTop` or `scrollLeft`. In a horizontal viewport with right-to-left\n     * direction, this would be the equivalent of setting a fictional `scrollRight` property.\n     * @param offset The offset to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n     */\n    scrollToOffset(offset, behavior = 'auto') {\n      const options = {\n        behavior\n      };\n      if (this.orientation === 'horizontal') {\n        options.start = offset;\n      } else {\n        options.top = offset;\n      }\n      this.scrollable.scrollTo(options);\n    }\n    /**\n     * Scrolls to the offset for the given index.\n     * @param index The index of the element to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n     */\n    scrollToIndex(index, behavior = 'auto') {\n      this._scrollStrategy.scrollToIndex(index, behavior);\n    }\n    /**\n     * Gets the current scroll offset from the start of the scrollable (in pixels).\n     * @param from The edge to measure the offset from. Defaults to 'top' in vertical mode and 'start'\n     *     in horizontal mode.\n     */\n    measureScrollOffset(from) {\n      // This is to break the call cycle\n      let measureScrollOffset;\n      if (this.scrollable == this) {\n        measureScrollOffset = _from => super.measureScrollOffset(_from);\n      } else {\n        measureScrollOffset = _from => this.scrollable.measureScrollOffset(_from);\n      }\n      return Math.max(0, measureScrollOffset(from ?? (this.orientation === 'horizontal' ? 'start' : 'top')) - this.measureViewportOffset());\n    }\n    /**\n     * Measures the offset of the viewport from the scrolling container\n     * @param from The edge to measure from.\n     */\n    measureViewportOffset(from) {\n      let fromRect;\n      const LEFT = 'left';\n      const RIGHT = 'right';\n      const isRtl = this.dir?.value == 'rtl';\n      if (from == 'start') {\n        fromRect = isRtl ? RIGHT : LEFT;\n      } else if (from == 'end') {\n        fromRect = isRtl ? LEFT : RIGHT;\n      } else if (from) {\n        fromRect = from;\n      } else {\n        fromRect = this.orientation === 'horizontal' ? 'left' : 'top';\n      }\n      const scrollerClientRect = this.scrollable.measureBoundingClientRectWithScrollOffset(fromRect);\n      const viewportClientRect = this.elementRef.nativeElement.getBoundingClientRect()[fromRect];\n      return viewportClientRect - scrollerClientRect;\n    }\n    /** Measure the combined size of all of the rendered items. */\n    measureRenderedContentSize() {\n      const contentEl = this._contentWrapper.nativeElement;\n      return this.orientation === 'horizontal' ? contentEl.offsetWidth : contentEl.offsetHeight;\n    }\n    /**\n     * Measure the total combined size of the given range. Throws if the range includes items that are\n     * not rendered.\n     */\n    measureRangeSize(range) {\n      if (!this._forOf) {\n        return 0;\n      }\n      return this._forOf.measureRangeSize(range, this.orientation);\n    }\n    /** Update the viewport dimensions and re-render. */\n    checkViewportSize() {\n      // TODO: Cleanup later when add logic for handling content resize\n      this._measureViewportSize();\n      this._scrollStrategy.onDataLengthChanged();\n    }\n    /** Measure the viewport size. */\n    _measureViewportSize() {\n      this._viewportSize = this.scrollable.measureViewportSize(this.orientation);\n    }\n    /** Queue up change detection to run. */\n    _markChangeDetectionNeeded(runAfter) {\n      if (runAfter) {\n        this._runAfterChangeDetection.push(runAfter);\n      }\n      // Use a Promise to batch together calls to `_doChangeDetection`. This way if we set a bunch of\n      // properties sequentially we only have to run `_doChangeDetection` once at the end.\n      if (!this._isChangeDetectionPending) {\n        this._isChangeDetectionPending = true;\n        this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n          this._doChangeDetection();\n        }));\n      }\n    }\n    /** Run change detection. */\n    _doChangeDetection() {\n      this._isChangeDetectionPending = false;\n      // Apply the content transform. The transform can't be set via an Angular binding because\n      // bypassSecurityTrustStyle is banned in Google. However the value is safe, it's composed of\n      // string literals, a variable that can only be 'X' or 'Y', and user input that is run through\n      // the `Number` function first to coerce it to a numeric value.\n      this._contentWrapper.nativeElement.style.transform = this._renderedContentTransform;\n      // Apply changes to Angular bindings. Note: We must call `markForCheck` to run change detection\n      // from the root, since the repeated items are content projected in. Calling `detectChanges`\n      // instead does not properly check the projected content.\n      this.ngZone.run(() => this._changeDetectorRef.markForCheck());\n      const runAfterChangeDetection = this._runAfterChangeDetection;\n      this._runAfterChangeDetection = [];\n      for (const fn of runAfterChangeDetection) {\n        fn();\n      }\n    }\n    /** Calculates the `style.width` and `style.height` for the spacer element. */\n    _calculateSpacerSize() {\n      this._totalContentHeight = this.orientation === 'horizontal' ? '' : `${this._totalContentSize}px`;\n      this._totalContentWidth = this.orientation === 'horizontal' ? `${this._totalContentSize}px` : '';\n    }\n    static #_ = this.ɵfac = function CdkVirtualScrollViewport_Factory(t) {\n      return new (t || CdkVirtualScrollViewport)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(VIRTUAL_SCROLL_STRATEGY, 8), i0.ɵɵdirectiveInject(i2.Directionality, 8), i0.ɵɵdirectiveInject(ScrollDispatcher), i0.ɵɵdirectiveInject(ViewportRuler), i0.ɵɵdirectiveInject(VIRTUAL_SCROLLABLE, 8));\n    };\n    static #_2 = this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CdkVirtualScrollViewport,\n      selectors: [[\"cdk-virtual-scroll-viewport\"]],\n      viewQuery: function CdkVirtualScrollViewport_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._contentWrapper = _t.first);\n        }\n      },\n      hostAttrs: [1, \"cdk-virtual-scroll-viewport\"],\n      hostVars: 4,\n      hostBindings: function CdkVirtualScrollViewport_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"cdk-virtual-scroll-orientation-horizontal\", ctx.orientation === \"horizontal\")(\"cdk-virtual-scroll-orientation-vertical\", ctx.orientation !== \"horizontal\");\n        }\n      },\n      inputs: {\n        orientation: \"orientation\",\n        appendOnly: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"appendOnly\", \"appendOnly\", booleanAttribute]\n      },\n      outputs: {\n        scrolledIndexChange: \"scrolledIndexChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: CdkScrollable,\n        useFactory: (virtualScrollable, viewport) => virtualScrollable || viewport,\n        deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], CdkVirtualScrollViewport]\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 4,\n      vars: 4,\n      consts: [[\"contentWrapper\", \"\"], [1, \"cdk-virtual-scroll-content-wrapper\"], [1, \"cdk-virtual-scroll-spacer\"]],\n      template: function CdkVirtualScrollViewport_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 1, 0);\n          i0.ɵɵprojection(2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(3, \"div\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵstyleProp(\"width\", ctx._totalContentWidth)(\"height\", ctx._totalContentHeight);\n        }\n      },\n      styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict;-webkit-overflow-scrolling:touch}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n  return CdkVirtualScrollViewport;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/** Helper to extract the offset of a DOM Node in a certain direction. */\nfunction getOffset(orientation, direction, node) {\n  const el = node;\n  if (!el.getBoundingClientRect) {\n    return 0;\n  }\n  const rect = el.getBoundingClientRect();\n  if (orientation === 'horizontal') {\n    return direction === 'start' ? rect.left : rect.right;\n  }\n  return direction === 'start' ? rect.top : rect.bottom;\n}\n/**\n * A directive similar to `ngForOf` to be used for rendering data inside a virtual scrolling\n * container.\n */\nlet CdkVirtualForOf = /*#__PURE__*/(() => {\n  class CdkVirtualForOf {\n    /** The DataSource to display. */\n    get cdkVirtualForOf() {\n      return this._cdkVirtualForOf;\n    }\n    set cdkVirtualForOf(value) {\n      this._cdkVirtualForOf = value;\n      if (isDataSource(value)) {\n        this._dataSourceChanges.next(value);\n      } else {\n        // If value is an an NgIterable, convert it to an array.\n        this._dataSourceChanges.next(new ArrayDataSource(isObservable(value) ? value : Array.from(value || [])));\n      }\n    }\n    /**\n     * The `TrackByFunction` to use for tracking changes. The `TrackByFunction` takes the index and\n     * the item and produces a value to be used as the item's identity when tracking changes.\n     */\n    get cdkVirtualForTrackBy() {\n      return this._cdkVirtualForTrackBy;\n    }\n    set cdkVirtualForTrackBy(fn) {\n      this._needsUpdate = true;\n      this._cdkVirtualForTrackBy = fn ? (index, item) => fn(index + (this._renderedRange ? this._renderedRange.start : 0), item) : undefined;\n    }\n    /** The template used to stamp out new elements. */\n    set cdkVirtualForTemplate(value) {\n      if (value) {\n        this._needsUpdate = true;\n        this._template = value;\n      }\n    }\n    /**\n     * The size of the cache used to store templates that are not being used for re-use later.\n     * Setting the cache size to `0` will disable caching. Defaults to 20 templates.\n     */\n    get cdkVirtualForTemplateCacheSize() {\n      return this._viewRepeater.viewCacheSize;\n    }\n    set cdkVirtualForTemplateCacheSize(size) {\n      this._viewRepeater.viewCacheSize = coerceNumberProperty(size);\n    }\n    constructor( /** The view container to add items to. */\n    _viewContainerRef, /** The template to use when stamping out new items. */\n    _template, /** The set of available differs. */\n    _differs, /** The strategy used to render items in the virtual scroll viewport. */\n    _viewRepeater, /** The virtual scrolling viewport that these items are being rendered in. */\n    _viewport, ngZone) {\n      this._viewContainerRef = _viewContainerRef;\n      this._template = _template;\n      this._differs = _differs;\n      this._viewRepeater = _viewRepeater;\n      this._viewport = _viewport;\n      /** Emits when the rendered view of the data changes. */\n      this.viewChange = new Subject();\n      /** Subject that emits when a new DataSource instance is given. */\n      this._dataSourceChanges = new Subject();\n      /** Emits whenever the data in the current DataSource changes. */\n      this.dataStream = this._dataSourceChanges.pipe(\n      // Start off with null `DataSource`.\n      startWith(null),\n      // Bundle up the previous and current data sources so we can work with both.\n      pairwise(),\n      // Use `_changeDataSource` to disconnect from the previous data source and connect to the\n      // new one, passing back a stream of data changes which we run through `switchMap` to give\n      // us a data stream that emits the latest data from whatever the current `DataSource` is.\n      switchMap(([prev, cur]) => this._changeDataSource(prev, cur)),\n      // Replay the last emitted data when someone subscribes.\n      shareReplay(1));\n      /** The differ used to calculate changes to the data. */\n      this._differ = null;\n      /** Whether the rendered data should be updated during the next ngDoCheck cycle. */\n      this._needsUpdate = false;\n      this._destroyed = new Subject();\n      this.dataStream.subscribe(data => {\n        this._data = data;\n        this._onRenderedDataChange();\n      });\n      this._viewport.renderedRangeStream.pipe(takeUntil(this._destroyed)).subscribe(range => {\n        this._renderedRange = range;\n        if (this.viewChange.observers.length) {\n          ngZone.run(() => this.viewChange.next(this._renderedRange));\n        }\n        this._onRenderedDataChange();\n      });\n      this._viewport.attach(this);\n    }\n    /**\n     * Measures the combined size (width for horizontal orientation, height for vertical) of all items\n     * in the specified range. Throws an error if the range includes items that are not currently\n     * rendered.\n     */\n    measureRangeSize(range, orientation) {\n      if (range.start >= range.end) {\n        return 0;\n      }\n      if ((range.start < this._renderedRange.start || range.end > this._renderedRange.end) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error(`Error: attempted to measure an item that isn't rendered.`);\n      }\n      // The index into the list of rendered views for the first item in the range.\n      const renderedStartIndex = range.start - this._renderedRange.start;\n      // The length of the range we're measuring.\n      const rangeLen = range.end - range.start;\n      // Loop over all the views, find the first and land node and compute the size by subtracting\n      // the top of the first node from the bottom of the last one.\n      let firstNode;\n      let lastNode;\n      // Find the first node by starting from the beginning and going forwards.\n      for (let i = 0; i < rangeLen; i++) {\n        const view = this._viewContainerRef.get(i + renderedStartIndex);\n        if (view && view.rootNodes.length) {\n          firstNode = lastNode = view.rootNodes[0];\n          break;\n        }\n      }\n      // Find the last node by starting from the end and going backwards.\n      for (let i = rangeLen - 1; i > -1; i--) {\n        const view = this._viewContainerRef.get(i + renderedStartIndex);\n        if (view && view.rootNodes.length) {\n          lastNode = view.rootNodes[view.rootNodes.length - 1];\n          break;\n        }\n      }\n      return firstNode && lastNode ? getOffset(orientation, 'end', lastNode) - getOffset(orientation, 'start', firstNode) : 0;\n    }\n    ngDoCheck() {\n      if (this._differ && this._needsUpdate) {\n        // TODO(mmalerba): We should differentiate needs update due to scrolling and a new portion of\n        // this list being rendered (can use simpler algorithm) vs needs update due to data actually\n        // changing (need to do this diff).\n        const changes = this._differ.diff(this._renderedItems);\n        if (!changes) {\n          this._updateContext();\n        } else {\n          this._applyChanges(changes);\n        }\n        this._needsUpdate = false;\n      }\n    }\n    ngOnDestroy() {\n      this._viewport.detach();\n      this._dataSourceChanges.next(undefined);\n      this._dataSourceChanges.complete();\n      this.viewChange.complete();\n      this._destroyed.next();\n      this._destroyed.complete();\n      this._viewRepeater.detach();\n    }\n    /** React to scroll state changes in the viewport. */\n    _onRenderedDataChange() {\n      if (!this._renderedRange) {\n        return;\n      }\n      this._renderedItems = this._data.slice(this._renderedRange.start, this._renderedRange.end);\n      if (!this._differ) {\n        // Use a wrapper function for the `trackBy` so any new values are\n        // picked up automatically without having to recreate the differ.\n        this._differ = this._differs.find(this._renderedItems).create((index, item) => {\n          return this.cdkVirtualForTrackBy ? this.cdkVirtualForTrackBy(index, item) : item;\n        });\n      }\n      this._needsUpdate = true;\n    }\n    /** Swap out one `DataSource` for another. */\n    _changeDataSource(oldDs, newDs) {\n      if (oldDs) {\n        oldDs.disconnect(this);\n      }\n      this._needsUpdate = true;\n      return newDs ? newDs.connect(this) : of();\n    }\n    /** Update the `CdkVirtualForOfContext` for all views. */\n    _updateContext() {\n      const count = this._data.length;\n      let i = this._viewContainerRef.length;\n      while (i--) {\n        const view = this._viewContainerRef.get(i);\n        view.context.index = this._renderedRange.start + i;\n        view.context.count = count;\n        this._updateComputedContextProperties(view.context);\n        view.detectChanges();\n      }\n    }\n    /** Apply changes to the DOM. */\n    _applyChanges(changes) {\n      this._viewRepeater.applyChanges(changes, this._viewContainerRef, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record, currentIndex), record => record.item);\n      // Update $implicit for any items that had an identity change.\n      changes.forEachIdentityChange(record => {\n        const view = this._viewContainerRef.get(record.currentIndex);\n        view.context.$implicit = record.item;\n      });\n      // Update the context variables on all items.\n      const count = this._data.length;\n      let i = this._viewContainerRef.length;\n      while (i--) {\n        const view = this._viewContainerRef.get(i);\n        view.context.index = this._renderedRange.start + i;\n        view.context.count = count;\n        this._updateComputedContextProperties(view.context);\n      }\n    }\n    /** Update the computed properties on the `CdkVirtualForOfContext`. */\n    _updateComputedContextProperties(context) {\n      context.first = context.index === 0;\n      context.last = context.index === context.count - 1;\n      context.even = context.index % 2 === 0;\n      context.odd = !context.even;\n    }\n    _getEmbeddedViewArgs(record, index) {\n      // Note that it's important that we insert the item directly at the proper index,\n      // rather than inserting it and the moving it in place, because if there's a directive\n      // on the same node that injects the `ViewContainerRef`, Angular will insert another\n      // comment node which can throw off the move when it's being repeated for all items.\n      return {\n        templateRef: this._template,\n        context: {\n          $implicit: record.item,\n          // It's guaranteed that the iterable is not \"undefined\" or \"null\" because we only\n          // generate views for elements if the \"cdkVirtualForOf\" iterable has elements.\n          cdkVirtualForOf: this._cdkVirtualForOf,\n          index: -1,\n          count: -1,\n          first: false,\n          last: false,\n          odd: false,\n          even: false\n        },\n        index\n      };\n    }\n    static #_ = this.ɵfac = function CdkVirtualForOf_Factory(t) {\n      return new (t || CdkVirtualForOf)(i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.IterableDiffers), i0.ɵɵdirectiveInject(_VIEW_REPEATER_STRATEGY), i0.ɵɵdirectiveInject(CdkVirtualScrollViewport, 4), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkVirtualForOf,\n      selectors: [[\"\", \"cdkVirtualFor\", \"\", \"cdkVirtualForOf\", \"\"]],\n      inputs: {\n        cdkVirtualForOf: \"cdkVirtualForOf\",\n        cdkVirtualForTrackBy: \"cdkVirtualForTrackBy\",\n        cdkVirtualForTemplate: \"cdkVirtualForTemplate\",\n        cdkVirtualForTemplateCacheSize: \"cdkVirtualForTemplateCacheSize\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: _VIEW_REPEATER_STRATEGY,\n        useClass: _RecycleViewRepeaterStrategy\n      }])]\n    });\n  }\n  return CdkVirtualForOf;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Provides a virtual scrollable for the element it is attached to.\n */\nlet CdkVirtualScrollableElement = /*#__PURE__*/(() => {\n  class CdkVirtualScrollableElement extends CdkVirtualScrollable {\n    constructor(elementRef, scrollDispatcher, ngZone, dir) {\n      super(elementRef, scrollDispatcher, ngZone, dir);\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n      return this.getElementRef().nativeElement.getBoundingClientRect()[from] - this.measureScrollOffset(from);\n    }\n    static #_ = this.ɵfac = function CdkVirtualScrollableElement_Factory(t) {\n      return new (t || CdkVirtualScrollableElement)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(ScrollDispatcher), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n    };\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkVirtualScrollableElement,\n      selectors: [[\"\", \"cdkVirtualScrollingElement\", \"\"]],\n      hostAttrs: [1, \"cdk-virtual-scrollable\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: VIRTUAL_SCROLLABLE,\n        useExisting: CdkVirtualScrollableElement\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return CdkVirtualScrollableElement;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Provides as virtual scrollable for the global / window scrollbar.\n */\nlet CdkVirtualScrollableWindow = /*#__PURE__*/(() => {\n  class CdkVirtualScrollableWindow extends CdkVirtualScrollable {\n    constructor(scrollDispatcher, ngZone, dir) {\n      super(new ElementRef(document.documentElement), scrollDispatcher, ngZone, dir);\n      this._elementScrolled = new Observable(observer => this.ngZone.runOutsideAngular(() => fromEvent(document, 'scroll').pipe(takeUntil(this._destroyed)).subscribe(observer)));\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n      return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n    }\n    static #_ = this.ɵfac = function CdkVirtualScrollableWindow_Factory(t) {\n      return new (t || CdkVirtualScrollableWindow)(i0.ɵɵdirectiveInject(ScrollDispatcher), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n    };\n    static #_2 = this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkVirtualScrollableWindow,\n      selectors: [[\"cdk-virtual-scroll-viewport\", \"scrollWindow\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: VIRTUAL_SCROLLABLE,\n        useExisting: CdkVirtualScrollableWindow\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n  return CdkVirtualScrollableWindow;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\nlet CdkScrollableModule = /*#__PURE__*/(() => {\n  class CdkScrollableModule {\n    static #_ = this.ɵfac = function CdkScrollableModule_Factory(t) {\n      return new (t || CdkScrollableModule)();\n    };\n    static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CdkScrollableModule\n    });\n    static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n  return CdkScrollableModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n/**\n * @docs-primary-export\n */\nlet ScrollingModule = /*#__PURE__*/(() => {\n  class ScrollingModule {\n    static #_ = this.ɵfac = function ScrollingModule_Factory(t) {\n      return new (t || ScrollingModule)();\n    };\n    static #_2 = this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ScrollingModule\n    });\n    static #_3 = this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [BidiModule, CdkScrollableModule, BidiModule, CdkScrollableModule]\n    });\n  }\n  return ScrollingModule;\n})();\n/*#__PURE__*/(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && void 0;\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkFixedSizeVirtualScroll, CdkScrollable, CdkScrollableModule, CdkVirtualForOf, CdkVirtualScrollViewport, CdkVirtualScrollable, CdkVirtualScrollableElement, CdkVirtualScrollableWindow, DEFAULT_RESIZE_TIME, DEFAULT_SCROLL_TIME, FixedSizeVirtualScrollStrategy, ScrollDispatcher, ScrollingModule, VIRTUAL_SCROLLABLE, VIRTUAL_SCROLL_STRATEGY, ViewportRuler, _fixedSizeVirtualScrollStrategyFactory };", "map": {"version": 3, "names": ["coerceNumberProperty", "coerceElement", "i0", "InjectionToken", "forwardRef", "Directive", "Input", "Injectable", "Optional", "Inject", "inject", "booleanAttribute", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Output", "ViewChild", "SkipSelf", "ElementRef", "NgModule", "Subject", "of", "Observable", "fromEvent", "animationFrameScheduler", "asapScheduler", "Subscription", "isObservable", "distinctUntilChanged", "auditTime", "filter", "takeUntil", "startWith", "pairwise", "switchMap", "shareReplay", "i1", "getRtlScrollAxisType", "RtlScrollAxisType", "supportsScrollBehavior", "Platform", "DOCUMENT", "i2", "BidiModule", "i2$1", "isDataSource", "ArrayDataSource", "_VIEW_REPEATER_STRATEGY", "_RecycleViewRepeaterStrategy", "_c0", "_c1", "VIRTUAL_SCROLL_STRATEGY", "FixedSizeVirtualScrollStrategy", "constructor", "itemSize", "minBufferPx", "maxBufferPx", "_scrolledIndexChange", "scrolledIndexChange", "pipe", "_viewport", "_itemSize", "_minBufferPx", "_maxBufferPx", "attach", "viewport", "_updateTotalContentSize", "_updateRenderedRange", "detach", "complete", "updateItemAndBufferSize", "ngDevMode", "Error", "onContentScrolled", "onDataLengthChanged", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ed", "onRenderedOffsetChanged", "scrollToIndex", "index", "behavior", "scrollToOffset", "setTotalContentSize", "getDataLength", "renderedRange", "getRenderedRange", "newRange", "start", "end", "viewportSize", "getViewportSize", "dataLength", "scrollOffset", "measureScrollOffset", "firstVisibleIndex", "maxVisibleItems", "Math", "ceil", "newVisibleIndex", "max", "min", "floor", "startBuffer", "expandStart", "end<PERSON><PERSON><PERSON>", "expandEnd", "setR<PERSON>edRange", "setRenderedContentOffset", "next", "_fixedSizeVirtualScrollStrategyFactory", "fixedSizeDir", "_scrollStrategy", "CdkFixedSizeVirtualScroll", "value", "ngOnChanges", "_", "ɵfac", "CdkFixedSizeVirtualScroll_Factory", "t", "_2", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "inputs", "standalone", "features", "ɵɵProvidersFeature", "provide", "useFactory", "deps", "ɵɵNgOnChangesFeature", "DEFAULT_SCROLL_TIME", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ngZone", "_platform", "document", "_scrolled", "_globalSubscription", "_scrolledCount", "scrollContainers", "Map", "_document", "register", "scrollable", "has", "set", "elementScrolled", "subscribe", "deregister", "scrollableReference", "get", "unsubscribe", "delete", "scrolled", "auditTimeInMs", "<PERSON><PERSON><PERSON><PERSON>", "observer", "_addGlobalListener", "subscription", "_removeGlobalListener", "ngOnDestroy", "for<PERSON>ach", "container", "ancestorScrolled", "elementOrElementRef", "ancestors", "getAncestorScrollContainers", "target", "indexOf", "scrollingContainers", "_subscription", "_scrollableContainsElement", "push", "_getWindow", "defaultView", "window", "element", "scrollableElement", "getElementRef", "nativeElement", "parentElement", "runOutsideAngular", "ScrollDispatcher_Factory", "ɵɵinject", "NgZone", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "CdkScrollable", "elementRef", "scroll<PERSON><PERSON><PERSON>tcher", "ngZone", "dir", "_destroyed", "_elementScrolled", "ngOnInit", "scrollTo", "options", "el", "isRtl", "left", "right", "bottom", "top", "scrollHeight", "clientHeight", "NORMAL", "scrollWidth", "clientWidth", "INVERTED", "NEGATED", "_applyScrollToOptions", "scrollTop", "scrollLeft", "from", "LEFT", "RIGHT", "CdkScrollable_Factory", "ɵɵdirectiveInject", "Directionality", "DEFAULT_RESIZE_TIME", "ViewportRuler", "_change", "_changeListener", "event", "addEventListener", "change", "_viewportSize", "removeEventListener", "_updateViewportSize", "output", "width", "height", "getViewportRect", "scrollPosition", "getViewportScrollPosition", "documentElement", "documentRect", "getBoundingClientRect", "body", "scrollY", "scrollX", "throttleTime", "innerWidth", "innerHeight", "ViewportRuler_Factory", "VIRTUAL_SCROLLABLE", "CdkVirtualScrollable", "measureViewportSize", "orientation", "viewportEl", "CdkVirtualScrollable_Factory", "ɵɵInheritDefinitionFeature", "rangesEqual", "r1", "r2", "SCROLL_SCHEDULER", "requestAnimationFrame", "CdkVirtualScrollViewport", "_orientation", "_calculateSpacerSize", "_changeDetectorRef", "viewportRuler", "_detachedSubject", "_renderedRangeSubject", "appendOnly", "Promise", "resolve", "then", "run", "renderedRangeStream", "_totalContentSize", "_totalContentWidth", "_totalContentHeight", "_rendered<PERSON><PERSON>e", "_dataLength", "_renderedContentOffset", "_renderedContentOffsetNeedsRewrite", "_isChangeDetectionPending", "_runAfterChangeDetection", "_viewportChanges", "EMPTY", "checkViewportSize", "classList", "add", "_measureViewportSize", "_markChangeDetectionNeeded", "forOf", "_forOf", "dataStream", "data", "<PERSON><PERSON><PERSON><PERSON>", "length", "_doChangeDetection", "measureBoundingClientRectWithScrollOffset", "size", "range", "getOffsetToRenderedContentStart", "offset", "to", "isHorizontal", "axis", "axisDirection", "transform", "Number", "_renderedContentTransform", "measureRenderedContentSize", "_from", "measureViewportOffset", "fromRect", "scrollerClientRect", "viewportClientRect", "contentEl", "_contentWrapper", "offsetWidth", "offsetHeight", "measureRangeSize", "runAfter", "style", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "runAfterChangeDetection", "fn", "CdkVirtualScrollViewport_Factory", "ChangeDetectorRef", "ɵcmp", "ɵɵdefineComponent", "viewQuery", "CdkVirtualScrollViewport_Query", "rf", "ctx", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "hostVars", "hostBindings", "CdkVirtualScrollViewport_HostBindings", "ɵɵclassProp", "ɵɵInputFlags", "HasDecoratorInputTransform", "outputs", "virtualScrollable", "ɵɵInputTransformsFeature", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "CdkVirtualScrollViewport_Template", "ɵɵprojectionDef", "ɵɵelementStart", "ɵɵprojection", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵstyleProp", "styles", "encapsulation", "changeDetection", "getOffset", "direction", "node", "rect", "CdkVirtualForOf", "cdkVirtualForOf", "_cdkVirtualForOf", "_dataSourceChanges", "Array", "cdkVirtualForTrackBy", "_cdkVirtualForTrackBy", "_needsUpdate", "item", "undefined", "cdkVirtualForTemplate", "_template", "cdkVirtualForTemplateCacheSize", "_view<PERSON><PERSON><PERSON>er", "viewCacheSize", "_viewContainerRef", "_differs", "viewChange", "prev", "cur", "_changeDataSource", "_differ", "_data", "_onRenderedDataChange", "observers", "renderedStartIndex", "rangeLen", "firstNode", "lastNode", "i", "view", "rootNodes", "ngDoCheck", "changes", "diff", "_renderedItems", "_updateContext", "_applyChanges", "slice", "find", "create", "oldDs", "newDs", "disconnect", "connect", "count", "context", "_updateComputedContextProperties", "detectChanges", "applyChanges", "record", "_adjustedPreviousIndex", "currentIndex", "_getEmbeddedViewArgs", "forEachIdentityChange", "$implicit", "last", "even", "odd", "templateRef", "CdkVirtualForOf_Factory", "ViewContainerRef", "TemplateRef", "Iterable<PERSON><PERSON><PERSON>", "useClass", "CdkVirtualScrollableElement", "CdkVirtualScrollableElement_Factory", "useExisting", "CdkVirtualScrollableWindow", "CdkVirtualScrollableWindow_Factory", "CdkScrollableModule", "CdkScrollableModule_Factory", "ɵmod", "ɵɵdefineNgModule", "_3", "ɵinj", "ɵɵdefineInjector", "ScrollingModule", "ScrollingModule_Factory", "imports"], "sources": ["C:/Projects/MLT-LG/MLT-Platform/frontend/node_modules/@angular/cdk/fesm2022/scrolling.mjs"], "sourcesContent": ["import { coerceNumberProperty, coerceElement } from '@angular/cdk/coercion';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, Directive, Input, Injectable, Optional, Inject, inject, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Output, ViewChild, SkipSelf, ElementRef, NgModule } from '@angular/core';\nimport { Subject, of, Observable, fromEvent, animationFrameScheduler, asapScheduler, Subscription, isObservable } from 'rxjs';\nimport { distinctUntilChanged, auditTime, filter, takeUntil, startWith, pairwise, switchMap, shareReplay } from 'rxjs/operators';\nimport * as i1 from '@angular/cdk/platform';\nimport { getRtlScrollAxisType, RtlScrollAxisType, supportsScrollBehavior, Platform } from '@angular/cdk/platform';\nimport { DOCUMENT } from '@angular/common';\nimport * as i2 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport * as i2$1 from '@angular/cdk/collections';\nimport { isDataSource, ArrayDataSource, _VIEW_REPEATER_STRATEGY, _RecycleViewRepeaterStrategy } from '@angular/cdk/collections';\n\n/** The injection token used to specify the virtual scrolling strategy. */\nconst VIRTUAL_SCROLL_STRATEGY = new InjectionToken('VIRTUAL_SCROLL_STRATEGY');\n\n/** Virtual scrolling strategy for lists with items of known fixed size. */\nclass FixedSizeVirtualScrollStrategy {\n    /**\n     * @param itemSize The size of the items in the virtually scrolling list.\n     * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n     * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n     */\n    constructor(itemSize, minBufferPx, maxBufferPx) {\n        this._scrolledIndexChange = new Subject();\n        /** @docs-private Implemented as part of VirtualScrollStrategy. */\n        this.scrolledIndexChange = this._scrolledIndexChange.pipe(distinctUntilChanged());\n        /** The attached viewport. */\n        this._viewport = null;\n        this._itemSize = itemSize;\n        this._minBufferPx = minBufferPx;\n        this._maxBufferPx = maxBufferPx;\n    }\n    /**\n     * Attaches this scroll strategy to a viewport.\n     * @param viewport The viewport to attach this strategy to.\n     */\n    attach(viewport) {\n        this._viewport = viewport;\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** Detaches this scroll strategy from the currently attached viewport. */\n    detach() {\n        this._scrolledIndexChange.complete();\n        this._viewport = null;\n    }\n    /**\n     * Update the item size and buffer size.\n     * @param itemSize The size of the items in the virtually scrolling list.\n     * @param minBufferPx The minimum amount of buffer (in pixels) before needing to render more\n     * @param maxBufferPx The amount of buffer (in pixels) to render when rendering more.\n     */\n    updateItemAndBufferSize(itemSize, minBufferPx, maxBufferPx) {\n        if (maxBufferPx < minBufferPx && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('CDK virtual scroll: maxBufferPx must be greater than or equal to minBufferPx');\n        }\n        this._itemSize = itemSize;\n        this._minBufferPx = minBufferPx;\n        this._maxBufferPx = maxBufferPx;\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onContentScrolled() {\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onDataLengthChanged() {\n        this._updateTotalContentSize();\n        this._updateRenderedRange();\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onContentRendered() {\n        /* no-op */\n    }\n    /** @docs-private Implemented as part of VirtualScrollStrategy. */\n    onRenderedOffsetChanged() {\n        /* no-op */\n    }\n    /**\n     * Scroll to the offset for the given index.\n     * @param index The index of the element to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling.\n     */\n    scrollToIndex(index, behavior) {\n        if (this._viewport) {\n            this._viewport.scrollToOffset(index * this._itemSize, behavior);\n        }\n    }\n    /** Update the viewport's total content size. */\n    _updateTotalContentSize() {\n        if (!this._viewport) {\n            return;\n        }\n        this._viewport.setTotalContentSize(this._viewport.getDataLength() * this._itemSize);\n    }\n    /** Update the viewport's rendered range. */\n    _updateRenderedRange() {\n        if (!this._viewport) {\n            return;\n        }\n        const renderedRange = this._viewport.getRenderedRange();\n        const newRange = { start: renderedRange.start, end: renderedRange.end };\n        const viewportSize = this._viewport.getViewportSize();\n        const dataLength = this._viewport.getDataLength();\n        let scrollOffset = this._viewport.measureScrollOffset();\n        // Prevent NaN as result when dividing by zero.\n        let firstVisibleIndex = this._itemSize > 0 ? scrollOffset / this._itemSize : 0;\n        // If user scrolls to the bottom of the list and data changes to a smaller list\n        if (newRange.end > dataLength) {\n            // We have to recalculate the first visible index based on new data length and viewport size.\n            const maxVisibleItems = Math.ceil(viewportSize / this._itemSize);\n            const newVisibleIndex = Math.max(0, Math.min(firstVisibleIndex, dataLength - maxVisibleItems));\n            // If first visible index changed we must update scroll offset to handle start/end buffers\n            // Current range must also be adjusted to cover the new position (bottom of new list).\n            if (firstVisibleIndex != newVisibleIndex) {\n                firstVisibleIndex = newVisibleIndex;\n                scrollOffset = newVisibleIndex * this._itemSize;\n                newRange.start = Math.floor(firstVisibleIndex);\n            }\n            newRange.end = Math.max(0, Math.min(dataLength, newRange.start + maxVisibleItems));\n        }\n        const startBuffer = scrollOffset - newRange.start * this._itemSize;\n        if (startBuffer < this._minBufferPx && newRange.start != 0) {\n            const expandStart = Math.ceil((this._maxBufferPx - startBuffer) / this._itemSize);\n            newRange.start = Math.max(0, newRange.start - expandStart);\n            newRange.end = Math.min(dataLength, Math.ceil(firstVisibleIndex + (viewportSize + this._minBufferPx) / this._itemSize));\n        }\n        else {\n            const endBuffer = newRange.end * this._itemSize - (scrollOffset + viewportSize);\n            if (endBuffer < this._minBufferPx && newRange.end != dataLength) {\n                const expandEnd = Math.ceil((this._maxBufferPx - endBuffer) / this._itemSize);\n                if (expandEnd > 0) {\n                    newRange.end = Math.min(dataLength, newRange.end + expandEnd);\n                    newRange.start = Math.max(0, Math.floor(firstVisibleIndex - this._minBufferPx / this._itemSize));\n                }\n            }\n        }\n        this._viewport.setRenderedRange(newRange);\n        this._viewport.setRenderedContentOffset(this._itemSize * newRange.start);\n        this._scrolledIndexChange.next(Math.floor(firstVisibleIndex));\n    }\n}\n/**\n * Provider factory for `FixedSizeVirtualScrollStrategy` that simply extracts the already created\n * `FixedSizeVirtualScrollStrategy` from the given directive.\n * @param fixedSizeDir The instance of `CdkFixedSizeVirtualScroll` to extract the\n *     `FixedSizeVirtualScrollStrategy` from.\n */\nfunction _fixedSizeVirtualScrollStrategyFactory(fixedSizeDir) {\n    return fixedSizeDir._scrollStrategy;\n}\n/** A virtual scroll strategy that supports fixed-size items. */\nclass CdkFixedSizeVirtualScroll {\n    constructor() {\n        this._itemSize = 20;\n        this._minBufferPx = 100;\n        this._maxBufferPx = 200;\n        /** The scroll strategy used by this directive. */\n        this._scrollStrategy = new FixedSizeVirtualScrollStrategy(this.itemSize, this.minBufferPx, this.maxBufferPx);\n    }\n    /** The size of the items in the list (in pixels). */\n    get itemSize() {\n        return this._itemSize;\n    }\n    set itemSize(value) {\n        this._itemSize = coerceNumberProperty(value);\n    }\n    /**\n     * The minimum amount of buffer rendered beyond the viewport (in pixels).\n     * If the amount of buffer dips below this number, more items will be rendered. Defaults to 100px.\n     */\n    get minBufferPx() {\n        return this._minBufferPx;\n    }\n    set minBufferPx(value) {\n        this._minBufferPx = coerceNumberProperty(value);\n    }\n    /**\n     * The number of pixels worth of buffer to render for when rendering new items. Defaults to 200px.\n     */\n    get maxBufferPx() {\n        return this._maxBufferPx;\n    }\n    set maxBufferPx(value) {\n        this._maxBufferPx = coerceNumberProperty(value);\n    }\n    ngOnChanges() {\n        this._scrollStrategy.updateItemAndBufferSize(this.itemSize, this.minBufferPx, this.maxBufferPx);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkFixedSizeVirtualScroll, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkFixedSizeVirtualScroll, isStandalone: true, selector: \"cdk-virtual-scroll-viewport[itemSize]\", inputs: { itemSize: \"itemSize\", minBufferPx: \"minBufferPx\", maxBufferPx: \"maxBufferPx\" }, providers: [\n            {\n                provide: VIRTUAL_SCROLL_STRATEGY,\n                useFactory: _fixedSizeVirtualScrollStrategyFactory,\n                deps: [forwardRef(() => CdkFixedSizeVirtualScroll)],\n            },\n        ], usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkFixedSizeVirtualScroll, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-virtual-scroll-viewport[itemSize]',\n                    standalone: true,\n                    providers: [\n                        {\n                            provide: VIRTUAL_SCROLL_STRATEGY,\n                            useFactory: _fixedSizeVirtualScrollStrategyFactory,\n                            deps: [forwardRef(() => CdkFixedSizeVirtualScroll)],\n                        },\n                    ],\n                }]\n        }], propDecorators: { itemSize: [{\n                type: Input\n            }], minBufferPx: [{\n                type: Input\n            }], maxBufferPx: [{\n                type: Input\n            }] } });\n\n/** Time in ms to throttle the scrolling events by default. */\nconst DEFAULT_SCROLL_TIME = 20;\n/**\n * Service contained all registered Scrollable references and emits an event when any one of the\n * Scrollable references emit a scrolled event.\n */\nclass ScrollDispatcher {\n    constructor(_ngZone, _platform, document) {\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        /** Subject for notifying that a registered scrollable reference element has been scrolled. */\n        this._scrolled = new Subject();\n        /** Keeps track of the global `scroll` and `resize` subscriptions. */\n        this._globalSubscription = null;\n        /** Keeps track of the amount of subscriptions to `scrolled`. Used for cleaning up afterwards. */\n        this._scrolledCount = 0;\n        /**\n         * Map of all the scrollable references that are registered with the service and their\n         * scroll event subscriptions.\n         */\n        this.scrollContainers = new Map();\n        this._document = document;\n    }\n    /**\n     * Registers a scrollable instance with the service and listens for its scrolled events. When the\n     * scrollable is scrolled, the service emits the event to its scrolled observable.\n     * @param scrollable Scrollable instance to be registered.\n     */\n    register(scrollable) {\n        if (!this.scrollContainers.has(scrollable)) {\n            this.scrollContainers.set(scrollable, scrollable.elementScrolled().subscribe(() => this._scrolled.next(scrollable)));\n        }\n    }\n    /**\n     * De-registers a Scrollable reference and unsubscribes from its scroll event observable.\n     * @param scrollable Scrollable instance to be deregistered.\n     */\n    deregister(scrollable) {\n        const scrollableReference = this.scrollContainers.get(scrollable);\n        if (scrollableReference) {\n            scrollableReference.unsubscribe();\n            this.scrollContainers.delete(scrollable);\n        }\n    }\n    /**\n     * Returns an observable that emits an event whenever any of the registered Scrollable\n     * references (or window, document, or body) fire a scrolled event. Can provide a time in ms\n     * to override the default \"throttle\" time.\n     *\n     * **Note:** in order to avoid hitting change detection for every scroll event,\n     * all of the events emitted from this stream will be run outside the Angular zone.\n     * If you need to update any data bindings as a result of a scroll event, you have\n     * to run the callback using `NgZone.run`.\n     */\n    scrolled(auditTimeInMs = DEFAULT_SCROLL_TIME) {\n        if (!this._platform.isBrowser) {\n            return of();\n        }\n        return new Observable((observer) => {\n            if (!this._globalSubscription) {\n                this._addGlobalListener();\n            }\n            // In the case of a 0ms delay, use an observable without auditTime\n            // since it does add a perceptible delay in processing overhead.\n            const subscription = auditTimeInMs > 0\n                ? this._scrolled.pipe(auditTime(auditTimeInMs)).subscribe(observer)\n                : this._scrolled.subscribe(observer);\n            this._scrolledCount++;\n            return () => {\n                subscription.unsubscribe();\n                this._scrolledCount--;\n                if (!this._scrolledCount) {\n                    this._removeGlobalListener();\n                }\n            };\n        });\n    }\n    ngOnDestroy() {\n        this._removeGlobalListener();\n        this.scrollContainers.forEach((_, container) => this.deregister(container));\n        this._scrolled.complete();\n    }\n    /**\n     * Returns an observable that emits whenever any of the\n     * scrollable ancestors of an element are scrolled.\n     * @param elementOrElementRef Element whose ancestors to listen for.\n     * @param auditTimeInMs Time to throttle the scroll events.\n     */\n    ancestorScrolled(elementOrElementRef, auditTimeInMs) {\n        const ancestors = this.getAncestorScrollContainers(elementOrElementRef);\n        return this.scrolled(auditTimeInMs).pipe(filter(target => {\n            return !target || ancestors.indexOf(target) > -1;\n        }));\n    }\n    /** Returns all registered Scrollables that contain the provided element. */\n    getAncestorScrollContainers(elementOrElementRef) {\n        const scrollingContainers = [];\n        this.scrollContainers.forEach((_subscription, scrollable) => {\n            if (this._scrollableContainsElement(scrollable, elementOrElementRef)) {\n                scrollingContainers.push(scrollable);\n            }\n        });\n        return scrollingContainers;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        return this._document.defaultView || window;\n    }\n    /** Returns true if the element is contained within the provided Scrollable. */\n    _scrollableContainsElement(scrollable, elementOrElementRef) {\n        let element = coerceElement(elementOrElementRef);\n        let scrollableElement = scrollable.getElementRef().nativeElement;\n        // Traverse through the element parents until we reach null, checking if any of the elements\n        // are the scrollable's element.\n        do {\n            if (element == scrollableElement) {\n                return true;\n            }\n        } while ((element = element.parentElement));\n        return false;\n    }\n    /** Sets up the global scroll listeners. */\n    _addGlobalListener() {\n        this._globalSubscription = this._ngZone.runOutsideAngular(() => {\n            const window = this._getWindow();\n            return fromEvent(window.document, 'scroll').subscribe(() => this._scrolled.next());\n        });\n    }\n    /** Cleans up the global scroll listener. */\n    _removeGlobalListener() {\n        if (this._globalSubscription) {\n            this._globalSubscription.unsubscribe();\n            this._globalSubscription = null;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: ScrollDispatcher, deps: [{ token: i0.NgZone }, { token: i1.Platform }, { token: DOCUMENT, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: ScrollDispatcher, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: ScrollDispatcher, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: i0.NgZone }, { type: i1.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\n/**\n * Sends an event when the directive's element is scrolled. Registers itself with the\n * ScrollDispatcher service to include itself as part of its collection of scrolling events that it\n * can be listened to through the service.\n */\nclass CdkScrollable {\n    constructor(elementRef, scrollDispatcher, ngZone, dir) {\n        this.elementRef = elementRef;\n        this.scrollDispatcher = scrollDispatcher;\n        this.ngZone = ngZone;\n        this.dir = dir;\n        this._destroyed = new Subject();\n        this._elementScrolled = new Observable((observer) => this.ngZone.runOutsideAngular(() => fromEvent(this.elementRef.nativeElement, 'scroll')\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(observer)));\n    }\n    ngOnInit() {\n        this.scrollDispatcher.register(this);\n    }\n    ngOnDestroy() {\n        this.scrollDispatcher.deregister(this);\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Returns observable that emits when a scroll event is fired on the host element. */\n    elementScrolled() {\n        return this._elementScrolled;\n    }\n    /** Gets the ElementRef for the viewport. */\n    getElementRef() {\n        return this.elementRef;\n    }\n    /**\n     * Scrolls to the specified offsets. This is a normalized version of the browser's native scrollTo\n     * method, since browsers are not consistent about what scrollLeft means in RTL. For this method\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param options specified the offsets to scroll to.\n     */\n    scrollTo(options) {\n        const el = this.elementRef.nativeElement;\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        // Rewrite start & end offsets as right or left offsets.\n        if (options.left == null) {\n            options.left = isRtl ? options.end : options.start;\n        }\n        if (options.right == null) {\n            options.right = isRtl ? options.start : options.end;\n        }\n        // Rewrite the bottom offset as a top offset.\n        if (options.bottom != null) {\n            options.top =\n                el.scrollHeight - el.clientHeight - options.bottom;\n        }\n        // Rewrite the right offset as a left offset.\n        if (isRtl && getRtlScrollAxisType() != RtlScrollAxisType.NORMAL) {\n            if (options.left != null) {\n                options.right =\n                    el.scrollWidth - el.clientWidth - options.left;\n            }\n            if (getRtlScrollAxisType() == RtlScrollAxisType.INVERTED) {\n                options.left = options.right;\n            }\n            else if (getRtlScrollAxisType() == RtlScrollAxisType.NEGATED) {\n                options.left = options.right ? -options.right : options.right;\n            }\n        }\n        else {\n            if (options.right != null) {\n                options.left =\n                    el.scrollWidth - el.clientWidth - options.right;\n            }\n        }\n        this._applyScrollToOptions(options);\n    }\n    _applyScrollToOptions(options) {\n        const el = this.elementRef.nativeElement;\n        if (supportsScrollBehavior()) {\n            el.scrollTo(options);\n        }\n        else {\n            if (options.top != null) {\n                el.scrollTop = options.top;\n            }\n            if (options.left != null) {\n                el.scrollLeft = options.left;\n            }\n        }\n    }\n    /**\n     * Measures the scroll offset relative to the specified edge of the viewport. This method can be\n     * used instead of directly checking scrollLeft or scrollTop, since browsers are not consistent\n     * about what scrollLeft means in RTL. The values returned by this method are normalized such that\n     * left and right always refer to the left and right side of the scrolling container irrespective\n     * of the layout direction. start and end refer to left and right in an LTR context and vice-versa\n     * in an RTL context.\n     * @param from The edge to measure from.\n     */\n    measureScrollOffset(from) {\n        const LEFT = 'left';\n        const RIGHT = 'right';\n        const el = this.elementRef.nativeElement;\n        if (from == 'top') {\n            return el.scrollTop;\n        }\n        if (from == 'bottom') {\n            return el.scrollHeight - el.clientHeight - el.scrollTop;\n        }\n        // Rewrite start & end as left or right offsets.\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        if (from == 'start') {\n            from = isRtl ? RIGHT : LEFT;\n        }\n        else if (from == 'end') {\n            from = isRtl ? LEFT : RIGHT;\n        }\n        if (isRtl && getRtlScrollAxisType() == RtlScrollAxisType.INVERTED) {\n            // For INVERTED, scrollLeft is (scrollWidth - clientWidth) when scrolled all the way left and\n            // 0 when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollWidth - el.clientWidth - el.scrollLeft;\n            }\n            else {\n                return el.scrollLeft;\n            }\n        }\n        else if (isRtl && getRtlScrollAxisType() == RtlScrollAxisType.NEGATED) {\n            // For NEGATED, scrollLeft is -(scrollWidth - clientWidth) when scrolled all the way left and\n            // 0 when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollLeft + el.scrollWidth - el.clientWidth;\n            }\n            else {\n                return -el.scrollLeft;\n            }\n        }\n        else {\n            // For NORMAL, as well as non-RTL contexts, scrollLeft is 0 when scrolled all the way left and\n            // (scrollWidth - clientWidth) when scrolled all the way right.\n            if (from == LEFT) {\n                return el.scrollLeft;\n            }\n            else {\n                return el.scrollWidth - el.clientWidth - el.scrollLeft;\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkScrollable, deps: [{ token: i0.ElementRef }, { token: ScrollDispatcher }, { token: i0.NgZone }, { token: i2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkScrollable, isStandalone: true, selector: \"[cdk-scrollable], [cdkScrollable]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkScrollable, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdk-scrollable], [cdkScrollable]',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: ScrollDispatcher }, { type: i0.NgZone }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }] });\n\n/** Time in ms to throttle the resize events by default. */\nconst DEFAULT_RESIZE_TIME = 20;\n/**\n * Simple utility for getting the bounds of the browser viewport.\n * @docs-private\n */\nclass ViewportRuler {\n    constructor(_platform, ngZone, document) {\n        this._platform = _platform;\n        /** Stream of viewport change events. */\n        this._change = new Subject();\n        /** Event listener that will be used to handle the viewport change events. */\n        this._changeListener = (event) => {\n            this._change.next(event);\n        };\n        this._document = document;\n        ngZone.runOutsideAngular(() => {\n            if (_platform.isBrowser) {\n                const window = this._getWindow();\n                // Note that bind the events ourselves, rather than going through something like RxJS's\n                // `fromEvent` so that we can ensure that they're bound outside of the NgZone.\n                window.addEventListener('resize', this._changeListener);\n                window.addEventListener('orientationchange', this._changeListener);\n            }\n            // Clear the cached position so that the viewport is re-measured next time it is required.\n            // We don't need to keep track of the subscription, because it is completed on destroy.\n            this.change().subscribe(() => (this._viewportSize = null));\n        });\n    }\n    ngOnDestroy() {\n        if (this._platform.isBrowser) {\n            const window = this._getWindow();\n            window.removeEventListener('resize', this._changeListener);\n            window.removeEventListener('orientationchange', this._changeListener);\n        }\n        this._change.complete();\n    }\n    /** Returns the viewport's width and height. */\n    getViewportSize() {\n        if (!this._viewportSize) {\n            this._updateViewportSize();\n        }\n        const output = { width: this._viewportSize.width, height: this._viewportSize.height };\n        // If we're not on a browser, don't cache the size since it'll be mocked out anyway.\n        if (!this._platform.isBrowser) {\n            this._viewportSize = null;\n        }\n        return output;\n    }\n    /** Gets a DOMRect for the viewport's bounds. */\n    getViewportRect() {\n        // Use the document element's bounding rect rather than the window scroll properties\n        // (e.g. pageYOffset, scrollY) due to in issue in Chrome and IE where window scroll\n        // properties and client coordinates (boundingClientRect, clientX/Y, etc.) are in different\n        // conceptual viewports. Under most circumstances these viewports are equivalent, but they\n        // can disagree when the page is pinch-zoomed (on devices that support touch).\n        // See https://bugs.chromium.org/p/chromium/issues/detail?id=489206#c4\n        // We use the documentElement instead of the body because, by default (without a css reset)\n        // browsers typically give the document body an 8px margin, which is not included in\n        // getBoundingClientRect().\n        const scrollPosition = this.getViewportScrollPosition();\n        const { width, height } = this.getViewportSize();\n        return {\n            top: scrollPosition.top,\n            left: scrollPosition.left,\n            bottom: scrollPosition.top + height,\n            right: scrollPosition.left + width,\n            height,\n            width,\n        };\n    }\n    /** Gets the (top, left) scroll position of the viewport. */\n    getViewportScrollPosition() {\n        // While we can get a reference to the fake document\n        // during SSR, it doesn't have getBoundingClientRect.\n        if (!this._platform.isBrowser) {\n            return { top: 0, left: 0 };\n        }\n        // The top-left-corner of the viewport is determined by the scroll position of the document\n        // body, normally just (scrollLeft, scrollTop). However, Chrome and Firefox disagree about\n        // whether `document.body` or `document.documentElement` is the scrolled element, so reading\n        // `scrollTop` and `scrollLeft` is inconsistent. However, using the bounding rect of\n        // `document.documentElement` works consistently, where the `top` and `left` values will\n        // equal negative the scroll position.\n        const document = this._document;\n        const window = this._getWindow();\n        const documentElement = document.documentElement;\n        const documentRect = documentElement.getBoundingClientRect();\n        const top = -documentRect.top ||\n            document.body.scrollTop ||\n            window.scrollY ||\n            documentElement.scrollTop ||\n            0;\n        const left = -documentRect.left ||\n            document.body.scrollLeft ||\n            window.scrollX ||\n            documentElement.scrollLeft ||\n            0;\n        return { top, left };\n    }\n    /**\n     * Returns a stream that emits whenever the size of the viewport changes.\n     * This stream emits outside of the Angular zone.\n     * @param throttleTime Time in milliseconds to throttle the stream.\n     */\n    change(throttleTime = DEFAULT_RESIZE_TIME) {\n        return throttleTime > 0 ? this._change.pipe(auditTime(throttleTime)) : this._change;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        return this._document.defaultView || window;\n    }\n    /** Updates the cached viewport size. */\n    _updateViewportSize() {\n        const window = this._getWindow();\n        this._viewportSize = this._platform.isBrowser\n            ? { width: window.innerWidth, height: window.innerHeight }\n            : { width: 0, height: 0 };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: ViewportRuler, deps: [{ token: i1.Platform }, { token: i0.NgZone }, { token: DOCUMENT, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: ViewportRuler, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: ViewportRuler, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: i1.Platform }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\nconst VIRTUAL_SCROLLABLE = new InjectionToken('VIRTUAL_SCROLLABLE');\n/**\n * Extending the {@link CdkScrollable} to be used as scrolling container for virtual scrolling.\n */\nclass CdkVirtualScrollable extends CdkScrollable {\n    constructor(elementRef, scrollDispatcher, ngZone, dir) {\n        super(elementRef, scrollDispatcher, ngZone, dir);\n    }\n    /**\n     * Measure the viewport size for the provided orientation.\n     *\n     * @param orientation The orientation to measure the size from.\n     */\n    measureViewportSize(orientation) {\n        const viewportEl = this.elementRef.nativeElement;\n        return orientation === 'horizontal' ? viewportEl.clientWidth : viewportEl.clientHeight;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkVirtualScrollable, deps: [{ token: i0.ElementRef }, { token: ScrollDispatcher }, { token: i0.NgZone }, { token: i2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkVirtualScrollable, usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkVirtualScrollable, decorators: [{\n            type: Directive\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: ScrollDispatcher }, { type: i0.NgZone }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }] });\n\n/** Checks if the given ranges are equal. */\nfunction rangesEqual(r1, r2) {\n    return r1.start == r2.start && r1.end == r2.end;\n}\n/**\n * Scheduler to be used for scroll events. Needs to fall back to\n * something that doesn't rely on requestAnimationFrame on environments\n * that don't support it (e.g. server-side rendering).\n */\nconst SCROLL_SCHEDULER = typeof requestAnimationFrame !== 'undefined' ? animationFrameScheduler : asapScheduler;\n/** A viewport that virtualizes its scrolling with the help of `CdkVirtualForOf`. */\nclass CdkVirtualScrollViewport extends CdkVirtualScrollable {\n    /** The direction the viewport scrolls. */\n    get orientation() {\n        return this._orientation;\n    }\n    set orientation(orientation) {\n        if (this._orientation !== orientation) {\n            this._orientation = orientation;\n            this._calculateSpacerSize();\n        }\n    }\n    constructor(elementRef, _changeDetectorRef, ngZone, _scrollStrategy, dir, scrollDispatcher, viewportRuler, scrollable) {\n        super(elementRef, scrollDispatcher, ngZone, dir);\n        this.elementRef = elementRef;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._scrollStrategy = _scrollStrategy;\n        this.scrollable = scrollable;\n        this._platform = inject(Platform);\n        /** Emits when the viewport is detached from a CdkVirtualForOf. */\n        this._detachedSubject = new Subject();\n        /** Emits when the rendered range changes. */\n        this._renderedRangeSubject = new Subject();\n        this._orientation = 'vertical';\n        /**\n         * Whether rendered items should persist in the DOM after scrolling out of view. By default, items\n         * will be removed.\n         */\n        this.appendOnly = false;\n        // Note: we don't use the typical EventEmitter here because we need to subscribe to the scroll\n        // strategy lazily (i.e. only if the user is actually listening to the events). We do this because\n        // depending on how the strategy calculates the scrolled index, it may come at a cost to\n        // performance.\n        /** Emits when the index of the first element visible in the viewport changes. */\n        this.scrolledIndexChange = new Observable((observer) => this._scrollStrategy.scrolledIndexChange.subscribe(index => Promise.resolve().then(() => this.ngZone.run(() => observer.next(index)))));\n        /** A stream that emits whenever the rendered range changes. */\n        this.renderedRangeStream = this._renderedRangeSubject;\n        /**\n         * The total size of all content (in pixels), including content that is not currently rendered.\n         */\n        this._totalContentSize = 0;\n        /** A string representing the `style.width` property value to be used for the spacer element. */\n        this._totalContentWidth = '';\n        /** A string representing the `style.height` property value to be used for the spacer element. */\n        this._totalContentHeight = '';\n        /** The currently rendered range of indices. */\n        this._renderedRange = { start: 0, end: 0 };\n        /** The length of the data bound to this viewport (in number of items). */\n        this._dataLength = 0;\n        /** The size of the viewport (in pixels). */\n        this._viewportSize = 0;\n        /** The last rendered content offset that was set. */\n        this._renderedContentOffset = 0;\n        /**\n         * Whether the last rendered content offset was to the end of the content (and therefore needs to\n         * be rewritten as an offset to the start of the content).\n         */\n        this._renderedContentOffsetNeedsRewrite = false;\n        /** Whether there is a pending change detection cycle. */\n        this._isChangeDetectionPending = false;\n        /** A list of functions to run after the next change detection cycle. */\n        this._runAfterChangeDetection = [];\n        /** Subscription to changes in the viewport size. */\n        this._viewportChanges = Subscription.EMPTY;\n        if (!_scrollStrategy && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('Error: cdk-virtual-scroll-viewport requires the \"itemSize\" property to be set.');\n        }\n        this._viewportChanges = viewportRuler.change().subscribe(() => {\n            this.checkViewportSize();\n        });\n        if (!this.scrollable) {\n            // No scrollable is provided, so the virtual-scroll-viewport needs to become a scrollable\n            this.elementRef.nativeElement.classList.add('cdk-virtual-scrollable');\n            this.scrollable = this;\n        }\n    }\n    ngOnInit() {\n        // Scrolling depends on the element dimensions which we can't get during SSR.\n        if (!this._platform.isBrowser) {\n            return;\n        }\n        if (this.scrollable === this) {\n            super.ngOnInit();\n        }\n        // It's still too early to measure the viewport at this point. Deferring with a promise allows\n        // the Viewport to be rendered with the correct size before we measure. We run this outside the\n        // zone to avoid causing more change detection cycles. We handle the change detection loop\n        // ourselves instead.\n        this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n            this._measureViewportSize();\n            this._scrollStrategy.attach(this);\n            this.scrollable\n                .elementScrolled()\n                .pipe(\n            // Start off with a fake scroll event so we properly detect our initial position.\n            startWith(null), \n            // Collect multiple events into one until the next animation frame. This way if\n            // there are multiple scroll events in the same frame we only need to recheck\n            // our layout once.\n            auditTime(0, SCROLL_SCHEDULER), \n            // Usually `elementScrolled` is completed when the scrollable is destroyed, but\n            // that may not be the case if a `CdkVirtualScrollableElement` is used so we have\n            // to unsubscribe here just in case.\n            takeUntil(this._destroyed))\n                .subscribe(() => this._scrollStrategy.onContentScrolled());\n            this._markChangeDetectionNeeded();\n        }));\n    }\n    ngOnDestroy() {\n        this.detach();\n        this._scrollStrategy.detach();\n        // Complete all subjects\n        this._renderedRangeSubject.complete();\n        this._detachedSubject.complete();\n        this._viewportChanges.unsubscribe();\n        super.ngOnDestroy();\n    }\n    /** Attaches a `CdkVirtualScrollRepeater` to this viewport. */\n    attach(forOf) {\n        if (this._forOf && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('CdkVirtualScrollViewport is already attached.');\n        }\n        // Subscribe to the data stream of the CdkVirtualForOf to keep track of when the data length\n        // changes. Run outside the zone to avoid triggering change detection, since we're managing the\n        // change detection loop ourselves.\n        this.ngZone.runOutsideAngular(() => {\n            this._forOf = forOf;\n            this._forOf.dataStream.pipe(takeUntil(this._detachedSubject)).subscribe(data => {\n                const newLength = data.length;\n                if (newLength !== this._dataLength) {\n                    this._dataLength = newLength;\n                    this._scrollStrategy.onDataLengthChanged();\n                }\n                this._doChangeDetection();\n            });\n        });\n    }\n    /** Detaches the current `CdkVirtualForOf`. */\n    detach() {\n        this._forOf = null;\n        this._detachedSubject.next();\n    }\n    /** Gets the length of the data bound to this viewport (in number of items). */\n    getDataLength() {\n        return this._dataLength;\n    }\n    /** Gets the size of the viewport (in pixels). */\n    getViewportSize() {\n        return this._viewportSize;\n    }\n    // TODO(mmalerba): This is technically out of sync with what's really rendered until a render\n    // cycle happens. I'm being careful to only call it after the render cycle is complete and before\n    // setting it to something else, but its error prone and should probably be split into\n    // `pendingRange` and `renderedRange`, the latter reflecting whats actually in the DOM.\n    /** Get the current rendered range of items. */\n    getRenderedRange() {\n        return this._renderedRange;\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n        return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n    }\n    /**\n     * Sets the total size of all content (in pixels), including content that is not currently\n     * rendered.\n     */\n    setTotalContentSize(size) {\n        if (this._totalContentSize !== size) {\n            this._totalContentSize = size;\n            this._calculateSpacerSize();\n            this._markChangeDetectionNeeded();\n        }\n    }\n    /** Sets the currently rendered range of indices. */\n    setRenderedRange(range) {\n        if (!rangesEqual(this._renderedRange, range)) {\n            if (this.appendOnly) {\n                range = { start: 0, end: Math.max(this._renderedRange.end, range.end) };\n            }\n            this._renderedRangeSubject.next((this._renderedRange = range));\n            this._markChangeDetectionNeeded(() => this._scrollStrategy.onContentRendered());\n        }\n    }\n    /**\n     * Gets the offset from the start of the viewport to the start of the rendered data (in pixels).\n     */\n    getOffsetToRenderedContentStart() {\n        return this._renderedContentOffsetNeedsRewrite ? null : this._renderedContentOffset;\n    }\n    /**\n     * Sets the offset from the start of the viewport to either the start or end of the rendered data\n     * (in pixels).\n     */\n    setRenderedContentOffset(offset, to = 'to-start') {\n        // In appendOnly, we always start from the top\n        offset = this.appendOnly && to === 'to-start' ? 0 : offset;\n        // For a horizontal viewport in a right-to-left language we need to translate along the x-axis\n        // in the negative direction.\n        const isRtl = this.dir && this.dir.value == 'rtl';\n        const isHorizontal = this.orientation == 'horizontal';\n        const axis = isHorizontal ? 'X' : 'Y';\n        const axisDirection = isHorizontal && isRtl ? -1 : 1;\n        let transform = `translate${axis}(${Number(axisDirection * offset)}px)`;\n        this._renderedContentOffset = offset;\n        if (to === 'to-end') {\n            transform += ` translate${axis}(-100%)`;\n            // The viewport should rewrite this as a `to-start` offset on the next render cycle. Otherwise\n            // elements will appear to expand in the wrong direction (e.g. `mat-expansion-panel` would\n            // expand upward).\n            this._renderedContentOffsetNeedsRewrite = true;\n        }\n        if (this._renderedContentTransform != transform) {\n            // We know this value is safe because we parse `offset` with `Number()` before passing it\n            // into the string.\n            this._renderedContentTransform = transform;\n            this._markChangeDetectionNeeded(() => {\n                if (this._renderedContentOffsetNeedsRewrite) {\n                    this._renderedContentOffset -= this.measureRenderedContentSize();\n                    this._renderedContentOffsetNeedsRewrite = false;\n                    this.setRenderedContentOffset(this._renderedContentOffset);\n                }\n                else {\n                    this._scrollStrategy.onRenderedOffsetChanged();\n                }\n            });\n        }\n    }\n    /**\n     * Scrolls to the given offset from the start of the viewport. Please note that this is not always\n     * the same as setting `scrollTop` or `scrollLeft`. In a horizontal viewport with right-to-left\n     * direction, this would be the equivalent of setting a fictional `scrollRight` property.\n     * @param offset The offset to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n     */\n    scrollToOffset(offset, behavior = 'auto') {\n        const options = { behavior };\n        if (this.orientation === 'horizontal') {\n            options.start = offset;\n        }\n        else {\n            options.top = offset;\n        }\n        this.scrollable.scrollTo(options);\n    }\n    /**\n     * Scrolls to the offset for the given index.\n     * @param index The index of the element to scroll to.\n     * @param behavior The ScrollBehavior to use when scrolling. Default is behavior is `auto`.\n     */\n    scrollToIndex(index, behavior = 'auto') {\n        this._scrollStrategy.scrollToIndex(index, behavior);\n    }\n    /**\n     * Gets the current scroll offset from the start of the scrollable (in pixels).\n     * @param from The edge to measure the offset from. Defaults to 'top' in vertical mode and 'start'\n     *     in horizontal mode.\n     */\n    measureScrollOffset(from) {\n        // This is to break the call cycle\n        let measureScrollOffset;\n        if (this.scrollable == this) {\n            measureScrollOffset = (_from) => super.measureScrollOffset(_from);\n        }\n        else {\n            measureScrollOffset = (_from) => this.scrollable.measureScrollOffset(_from);\n        }\n        return Math.max(0, measureScrollOffset(from ?? (this.orientation === 'horizontal' ? 'start' : 'top')) -\n            this.measureViewportOffset());\n    }\n    /**\n     * Measures the offset of the viewport from the scrolling container\n     * @param from The edge to measure from.\n     */\n    measureViewportOffset(from) {\n        let fromRect;\n        const LEFT = 'left';\n        const RIGHT = 'right';\n        const isRtl = this.dir?.value == 'rtl';\n        if (from == 'start') {\n            fromRect = isRtl ? RIGHT : LEFT;\n        }\n        else if (from == 'end') {\n            fromRect = isRtl ? LEFT : RIGHT;\n        }\n        else if (from) {\n            fromRect = from;\n        }\n        else {\n            fromRect = this.orientation === 'horizontal' ? 'left' : 'top';\n        }\n        const scrollerClientRect = this.scrollable.measureBoundingClientRectWithScrollOffset(fromRect);\n        const viewportClientRect = this.elementRef.nativeElement.getBoundingClientRect()[fromRect];\n        return viewportClientRect - scrollerClientRect;\n    }\n    /** Measure the combined size of all of the rendered items. */\n    measureRenderedContentSize() {\n        const contentEl = this._contentWrapper.nativeElement;\n        return this.orientation === 'horizontal' ? contentEl.offsetWidth : contentEl.offsetHeight;\n    }\n    /**\n     * Measure the total combined size of the given range. Throws if the range includes items that are\n     * not rendered.\n     */\n    measureRangeSize(range) {\n        if (!this._forOf) {\n            return 0;\n        }\n        return this._forOf.measureRangeSize(range, this.orientation);\n    }\n    /** Update the viewport dimensions and re-render. */\n    checkViewportSize() {\n        // TODO: Cleanup later when add logic for handling content resize\n        this._measureViewportSize();\n        this._scrollStrategy.onDataLengthChanged();\n    }\n    /** Measure the viewport size. */\n    _measureViewportSize() {\n        this._viewportSize = this.scrollable.measureViewportSize(this.orientation);\n    }\n    /** Queue up change detection to run. */\n    _markChangeDetectionNeeded(runAfter) {\n        if (runAfter) {\n            this._runAfterChangeDetection.push(runAfter);\n        }\n        // Use a Promise to batch together calls to `_doChangeDetection`. This way if we set a bunch of\n        // properties sequentially we only have to run `_doChangeDetection` once at the end.\n        if (!this._isChangeDetectionPending) {\n            this._isChangeDetectionPending = true;\n            this.ngZone.runOutsideAngular(() => Promise.resolve().then(() => {\n                this._doChangeDetection();\n            }));\n        }\n    }\n    /** Run change detection. */\n    _doChangeDetection() {\n        this._isChangeDetectionPending = false;\n        // Apply the content transform. The transform can't be set via an Angular binding because\n        // bypassSecurityTrustStyle is banned in Google. However the value is safe, it's composed of\n        // string literals, a variable that can only be 'X' or 'Y', and user input that is run through\n        // the `Number` function first to coerce it to a numeric value.\n        this._contentWrapper.nativeElement.style.transform = this._renderedContentTransform;\n        // Apply changes to Angular bindings. Note: We must call `markForCheck` to run change detection\n        // from the root, since the repeated items are content projected in. Calling `detectChanges`\n        // instead does not properly check the projected content.\n        this.ngZone.run(() => this._changeDetectorRef.markForCheck());\n        const runAfterChangeDetection = this._runAfterChangeDetection;\n        this._runAfterChangeDetection = [];\n        for (const fn of runAfterChangeDetection) {\n            fn();\n        }\n    }\n    /** Calculates the `style.width` and `style.height` for the spacer element. */\n    _calculateSpacerSize() {\n        this._totalContentHeight =\n            this.orientation === 'horizontal' ? '' : `${this._totalContentSize}px`;\n        this._totalContentWidth =\n            this.orientation === 'horizontal' ? `${this._totalContentSize}px` : '';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkVirtualScrollViewport, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }, { token: VIRTUAL_SCROLL_STRATEGY, optional: true }, { token: i2.Directionality, optional: true }, { token: ScrollDispatcher }, { token: ViewportRuler }, { token: VIRTUAL_SCROLLABLE, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"17.2.0\", type: CdkVirtualScrollViewport, isStandalone: true, selector: \"cdk-virtual-scroll-viewport\", inputs: { orientation: \"orientation\", appendOnly: [\"appendOnly\", \"appendOnly\", booleanAttribute] }, outputs: { scrolledIndexChange: \"scrolledIndexChange\" }, host: { properties: { \"class.cdk-virtual-scroll-orientation-horizontal\": \"orientation === \\\"horizontal\\\"\", \"class.cdk-virtual-scroll-orientation-vertical\": \"orientation !== \\\"horizontal\\\"\" }, classAttribute: \"cdk-virtual-scroll-viewport\" }, providers: [\n            {\n                provide: CdkScrollable,\n                useFactory: (virtualScrollable, viewport) => virtualScrollable || viewport,\n                deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], CdkVirtualScrollViewport],\n            },\n        ], viewQueries: [{ propertyName: \"_contentWrapper\", first: true, predicate: [\"contentWrapper\"], descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<!--\\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\\n  position.\\n-->\\n<div #contentWrapper class=\\\"cdk-virtual-scroll-content-wrapper\\\">\\n  <ng-content></ng-content>\\n</div>\\n<!--\\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\\n  so that the scrollbar captures the size of the entire data set.\\n-->\\n<div class=\\\"cdk-virtual-scroll-spacer\\\"\\n     [style.width]=\\\"_totalContentWidth\\\" [style.height]=\\\"_totalContentHeight\\\"></div>\\n\", styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict;-webkit-overflow-scrolling:touch}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkVirtualScrollViewport, decorators: [{\n            type: Component,\n            args: [{ selector: 'cdk-virtual-scroll-viewport', host: {\n                        'class': 'cdk-virtual-scroll-viewport',\n                        '[class.cdk-virtual-scroll-orientation-horizontal]': 'orientation === \"horizontal\"',\n                        '[class.cdk-virtual-scroll-orientation-vertical]': 'orientation !== \"horizontal\"',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, standalone: true, providers: [\n                        {\n                            provide: CdkScrollable,\n                            useFactory: (virtualScrollable, viewport) => virtualScrollable || viewport,\n                            deps: [[new Optional(), new Inject(VIRTUAL_SCROLLABLE)], CdkVirtualScrollViewport],\n                        },\n                    ], template: \"<!--\\n  Wrap the rendered content in an element that will be used to offset it based on the scroll\\n  position.\\n-->\\n<div #contentWrapper class=\\\"cdk-virtual-scroll-content-wrapper\\\">\\n  <ng-content></ng-content>\\n</div>\\n<!--\\n  Spacer used to force the scrolling container to the correct size for the *total* number of items\\n  so that the scrollbar captures the size of the entire data set.\\n-->\\n<div class=\\\"cdk-virtual-scroll-spacer\\\"\\n     [style.width]=\\\"_totalContentWidth\\\" [style.height]=\\\"_totalContentHeight\\\"></div>\\n\", styles: [\"cdk-virtual-scroll-viewport{display:block;position:relative;transform:translateZ(0)}.cdk-virtual-scrollable{overflow:auto;will-change:scroll-position;contain:strict;-webkit-overflow-scrolling:touch}.cdk-virtual-scroll-content-wrapper{position:absolute;top:0;left:0;contain:content}[dir=rtl] .cdk-virtual-scroll-content-wrapper{right:0;left:auto}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper{min-height:100%}.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-horizontal .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-left:0;padding-right:0;margin-left:0;margin-right:0;border-left-width:0;border-right-width:0;outline:none}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper{min-width:100%}.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>dl:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ol:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>table:not([cdkVirtualFor]),.cdk-virtual-scroll-orientation-vertical .cdk-virtual-scroll-content-wrapper>ul:not([cdkVirtualFor]){padding-top:0;padding-bottom:0;margin-top:0;margin-bottom:0;border-top-width:0;border-bottom-width:0;outline:none}.cdk-virtual-scroll-spacer{height:1px;transform-origin:0 0;flex:0 0 auto}[dir=rtl] .cdk-virtual-scroll-spacer{transform-origin:100% 0}\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [VIRTUAL_SCROLL_STRATEGY]\n                }] }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: ScrollDispatcher }, { type: ViewportRuler }, { type: CdkVirtualScrollable, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [VIRTUAL_SCROLLABLE]\n                }] }], propDecorators: { orientation: [{\n                type: Input\n            }], appendOnly: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], scrolledIndexChange: [{\n                type: Output\n            }], _contentWrapper: [{\n                type: ViewChild,\n                args: ['contentWrapper', { static: true }]\n            }] } });\n\n/** Helper to extract the offset of a DOM Node in a certain direction. */\nfunction getOffset(orientation, direction, node) {\n    const el = node;\n    if (!el.getBoundingClientRect) {\n        return 0;\n    }\n    const rect = el.getBoundingClientRect();\n    if (orientation === 'horizontal') {\n        return direction === 'start' ? rect.left : rect.right;\n    }\n    return direction === 'start' ? rect.top : rect.bottom;\n}\n/**\n * A directive similar to `ngForOf` to be used for rendering data inside a virtual scrolling\n * container.\n */\nclass CdkVirtualForOf {\n    /** The DataSource to display. */\n    get cdkVirtualForOf() {\n        return this._cdkVirtualForOf;\n    }\n    set cdkVirtualForOf(value) {\n        this._cdkVirtualForOf = value;\n        if (isDataSource(value)) {\n            this._dataSourceChanges.next(value);\n        }\n        else {\n            // If value is an an NgIterable, convert it to an array.\n            this._dataSourceChanges.next(new ArrayDataSource(isObservable(value) ? value : Array.from(value || [])));\n        }\n    }\n    /**\n     * The `TrackByFunction` to use for tracking changes. The `TrackByFunction` takes the index and\n     * the item and produces a value to be used as the item's identity when tracking changes.\n     */\n    get cdkVirtualForTrackBy() {\n        return this._cdkVirtualForTrackBy;\n    }\n    set cdkVirtualForTrackBy(fn) {\n        this._needsUpdate = true;\n        this._cdkVirtualForTrackBy = fn\n            ? (index, item) => fn(index + (this._renderedRange ? this._renderedRange.start : 0), item)\n            : undefined;\n    }\n    /** The template used to stamp out new elements. */\n    set cdkVirtualForTemplate(value) {\n        if (value) {\n            this._needsUpdate = true;\n            this._template = value;\n        }\n    }\n    /**\n     * The size of the cache used to store templates that are not being used for re-use later.\n     * Setting the cache size to `0` will disable caching. Defaults to 20 templates.\n     */\n    get cdkVirtualForTemplateCacheSize() {\n        return this._viewRepeater.viewCacheSize;\n    }\n    set cdkVirtualForTemplateCacheSize(size) {\n        this._viewRepeater.viewCacheSize = coerceNumberProperty(size);\n    }\n    constructor(\n    /** The view container to add items to. */\n    _viewContainerRef, \n    /** The template to use when stamping out new items. */\n    _template, \n    /** The set of available differs. */\n    _differs, \n    /** The strategy used to render items in the virtual scroll viewport. */\n    _viewRepeater, \n    /** The virtual scrolling viewport that these items are being rendered in. */\n    _viewport, ngZone) {\n        this._viewContainerRef = _viewContainerRef;\n        this._template = _template;\n        this._differs = _differs;\n        this._viewRepeater = _viewRepeater;\n        this._viewport = _viewport;\n        /** Emits when the rendered view of the data changes. */\n        this.viewChange = new Subject();\n        /** Subject that emits when a new DataSource instance is given. */\n        this._dataSourceChanges = new Subject();\n        /** Emits whenever the data in the current DataSource changes. */\n        this.dataStream = this._dataSourceChanges.pipe(\n        // Start off with null `DataSource`.\n        startWith(null), \n        // Bundle up the previous and current data sources so we can work with both.\n        pairwise(), \n        // Use `_changeDataSource` to disconnect from the previous data source and connect to the\n        // new one, passing back a stream of data changes which we run through `switchMap` to give\n        // us a data stream that emits the latest data from whatever the current `DataSource` is.\n        switchMap(([prev, cur]) => this._changeDataSource(prev, cur)), \n        // Replay the last emitted data when someone subscribes.\n        shareReplay(1));\n        /** The differ used to calculate changes to the data. */\n        this._differ = null;\n        /** Whether the rendered data should be updated during the next ngDoCheck cycle. */\n        this._needsUpdate = false;\n        this._destroyed = new Subject();\n        this.dataStream.subscribe(data => {\n            this._data = data;\n            this._onRenderedDataChange();\n        });\n        this._viewport.renderedRangeStream.pipe(takeUntil(this._destroyed)).subscribe(range => {\n            this._renderedRange = range;\n            if (this.viewChange.observers.length) {\n                ngZone.run(() => this.viewChange.next(this._renderedRange));\n            }\n            this._onRenderedDataChange();\n        });\n        this._viewport.attach(this);\n    }\n    /**\n     * Measures the combined size (width for horizontal orientation, height for vertical) of all items\n     * in the specified range. Throws an error if the range includes items that are not currently\n     * rendered.\n     */\n    measureRangeSize(range, orientation) {\n        if (range.start >= range.end) {\n            return 0;\n        }\n        if ((range.start < this._renderedRange.start || range.end > this._renderedRange.end) &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Error: attempted to measure an item that isn't rendered.`);\n        }\n        // The index into the list of rendered views for the first item in the range.\n        const renderedStartIndex = range.start - this._renderedRange.start;\n        // The length of the range we're measuring.\n        const rangeLen = range.end - range.start;\n        // Loop over all the views, find the first and land node and compute the size by subtracting\n        // the top of the first node from the bottom of the last one.\n        let firstNode;\n        let lastNode;\n        // Find the first node by starting from the beginning and going forwards.\n        for (let i = 0; i < rangeLen; i++) {\n            const view = this._viewContainerRef.get(i + renderedStartIndex);\n            if (view && view.rootNodes.length) {\n                firstNode = lastNode = view.rootNodes[0];\n                break;\n            }\n        }\n        // Find the last node by starting from the end and going backwards.\n        for (let i = rangeLen - 1; i > -1; i--) {\n            const view = this._viewContainerRef.get(i + renderedStartIndex);\n            if (view && view.rootNodes.length) {\n                lastNode = view.rootNodes[view.rootNodes.length - 1];\n                break;\n            }\n        }\n        return firstNode && lastNode\n            ? getOffset(orientation, 'end', lastNode) - getOffset(orientation, 'start', firstNode)\n            : 0;\n    }\n    ngDoCheck() {\n        if (this._differ && this._needsUpdate) {\n            // TODO(mmalerba): We should differentiate needs update due to scrolling and a new portion of\n            // this list being rendered (can use simpler algorithm) vs needs update due to data actually\n            // changing (need to do this diff).\n            const changes = this._differ.diff(this._renderedItems);\n            if (!changes) {\n                this._updateContext();\n            }\n            else {\n                this._applyChanges(changes);\n            }\n            this._needsUpdate = false;\n        }\n    }\n    ngOnDestroy() {\n        this._viewport.detach();\n        this._dataSourceChanges.next(undefined);\n        this._dataSourceChanges.complete();\n        this.viewChange.complete();\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._viewRepeater.detach();\n    }\n    /** React to scroll state changes in the viewport. */\n    _onRenderedDataChange() {\n        if (!this._renderedRange) {\n            return;\n        }\n        this._renderedItems = this._data.slice(this._renderedRange.start, this._renderedRange.end);\n        if (!this._differ) {\n            // Use a wrapper function for the `trackBy` so any new values are\n            // picked up automatically without having to recreate the differ.\n            this._differ = this._differs.find(this._renderedItems).create((index, item) => {\n                return this.cdkVirtualForTrackBy ? this.cdkVirtualForTrackBy(index, item) : item;\n            });\n        }\n        this._needsUpdate = true;\n    }\n    /** Swap out one `DataSource` for another. */\n    _changeDataSource(oldDs, newDs) {\n        if (oldDs) {\n            oldDs.disconnect(this);\n        }\n        this._needsUpdate = true;\n        return newDs ? newDs.connect(this) : of();\n    }\n    /** Update the `CdkVirtualForOfContext` for all views. */\n    _updateContext() {\n        const count = this._data.length;\n        let i = this._viewContainerRef.length;\n        while (i--) {\n            const view = this._viewContainerRef.get(i);\n            view.context.index = this._renderedRange.start + i;\n            view.context.count = count;\n            this._updateComputedContextProperties(view.context);\n            view.detectChanges();\n        }\n    }\n    /** Apply changes to the DOM. */\n    _applyChanges(changes) {\n        this._viewRepeater.applyChanges(changes, this._viewContainerRef, (record, _adjustedPreviousIndex, currentIndex) => this._getEmbeddedViewArgs(record, currentIndex), record => record.item);\n        // Update $implicit for any items that had an identity change.\n        changes.forEachIdentityChange((record) => {\n            const view = this._viewContainerRef.get(record.currentIndex);\n            view.context.$implicit = record.item;\n        });\n        // Update the context variables on all items.\n        const count = this._data.length;\n        let i = this._viewContainerRef.length;\n        while (i--) {\n            const view = this._viewContainerRef.get(i);\n            view.context.index = this._renderedRange.start + i;\n            view.context.count = count;\n            this._updateComputedContextProperties(view.context);\n        }\n    }\n    /** Update the computed properties on the `CdkVirtualForOfContext`. */\n    _updateComputedContextProperties(context) {\n        context.first = context.index === 0;\n        context.last = context.index === context.count - 1;\n        context.even = context.index % 2 === 0;\n        context.odd = !context.even;\n    }\n    _getEmbeddedViewArgs(record, index) {\n        // Note that it's important that we insert the item directly at the proper index,\n        // rather than inserting it and the moving it in place, because if there's a directive\n        // on the same node that injects the `ViewContainerRef`, Angular will insert another\n        // comment node which can throw off the move when it's being repeated for all items.\n        return {\n            templateRef: this._template,\n            context: {\n                $implicit: record.item,\n                // It's guaranteed that the iterable is not \"undefined\" or \"null\" because we only\n                // generate views for elements if the \"cdkVirtualForOf\" iterable has elements.\n                cdkVirtualForOf: this._cdkVirtualForOf,\n                index: -1,\n                count: -1,\n                first: false,\n                last: false,\n                odd: false,\n                even: false,\n            },\n            index,\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkVirtualForOf, deps: [{ token: i0.ViewContainerRef }, { token: i0.TemplateRef }, { token: i0.IterableDiffers }, { token: _VIEW_REPEATER_STRATEGY }, { token: CdkVirtualScrollViewport, skipSelf: true }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkVirtualForOf, isStandalone: true, selector: \"[cdkVirtualFor][cdkVirtualForOf]\", inputs: { cdkVirtualForOf: \"cdkVirtualForOf\", cdkVirtualForTrackBy: \"cdkVirtualForTrackBy\", cdkVirtualForTemplate: \"cdkVirtualForTemplate\", cdkVirtualForTemplateCacheSize: \"cdkVirtualForTemplateCacheSize\" }, providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkVirtualForOf, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkVirtualFor][cdkVirtualForOf]',\n                    providers: [{ provide: _VIEW_REPEATER_STRATEGY, useClass: _RecycleViewRepeaterStrategy }],\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ViewContainerRef }, { type: i0.TemplateRef }, { type: i0.IterableDiffers }, { type: i2$1._RecycleViewRepeaterStrategy, decorators: [{\n                    type: Inject,\n                    args: [_VIEW_REPEATER_STRATEGY]\n                }] }, { type: CdkVirtualScrollViewport, decorators: [{\n                    type: SkipSelf\n                }] }, { type: i0.NgZone }], propDecorators: { cdkVirtualForOf: [{\n                type: Input\n            }], cdkVirtualForTrackBy: [{\n                type: Input\n            }], cdkVirtualForTemplate: [{\n                type: Input\n            }], cdkVirtualForTemplateCacheSize: [{\n                type: Input\n            }] } });\n\n/**\n * Provides a virtual scrollable for the element it is attached to.\n */\nclass CdkVirtualScrollableElement extends CdkVirtualScrollable {\n    constructor(elementRef, scrollDispatcher, ngZone, dir) {\n        super(elementRef, scrollDispatcher, ngZone, dir);\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n        return (this.getElementRef().nativeElement.getBoundingClientRect()[from] -\n            this.measureScrollOffset(from));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkVirtualScrollableElement, deps: [{ token: i0.ElementRef }, { token: ScrollDispatcher }, { token: i0.NgZone }, { token: i2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkVirtualScrollableElement, isStandalone: true, selector: \"[cdkVirtualScrollingElement]\", host: { classAttribute: \"cdk-virtual-scrollable\" }, providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableElement }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkVirtualScrollableElement, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkVirtualScrollingElement]',\n                    providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableElement }],\n                    standalone: true,\n                    host: {\n                        'class': 'cdk-virtual-scrollable',\n                    },\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: ScrollDispatcher }, { type: i0.NgZone }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }] });\n\n/**\n * Provides as virtual scrollable for the global / window scrollbar.\n */\nclass CdkVirtualScrollableWindow extends CdkVirtualScrollable {\n    constructor(scrollDispatcher, ngZone, dir) {\n        super(new ElementRef(document.documentElement), scrollDispatcher, ngZone, dir);\n        this._elementScrolled = new Observable((observer) => this.ngZone.runOutsideAngular(() => fromEvent(document, 'scroll').pipe(takeUntil(this._destroyed)).subscribe(observer)));\n    }\n    measureBoundingClientRectWithScrollOffset(from) {\n        return this.getElementRef().nativeElement.getBoundingClientRect()[from];\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkVirtualScrollableWindow, deps: [{ token: ScrollDispatcher }, { token: i0.NgZone }, { token: i2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkVirtualScrollableWindow, isStandalone: true, selector: \"cdk-virtual-scroll-viewport[scrollWindow]\", providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableWindow }], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkVirtualScrollableWindow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'cdk-virtual-scroll-viewport[scrollWindow]',\n                    providers: [{ provide: VIRTUAL_SCROLLABLE, useExisting: CdkVirtualScrollableWindow }],\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: ScrollDispatcher }, { type: i0.NgZone }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }] });\n\nclass CdkScrollableModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkScrollableModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkScrollableModule, imports: [CdkScrollable], exports: [CdkScrollable] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkScrollableModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkScrollableModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    exports: [CdkScrollable],\n                    imports: [CdkScrollable],\n                }]\n        }] });\n/**\n * @docs-primary-export\n */\nclass ScrollingModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: ScrollingModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: ScrollingModule, imports: [BidiModule, CdkScrollableModule, CdkVirtualScrollViewport,\n            CdkFixedSizeVirtualScroll,\n            CdkVirtualForOf,\n            CdkVirtualScrollableWindow,\n            CdkVirtualScrollableElement], exports: [BidiModule, CdkScrollableModule, CdkFixedSizeVirtualScroll,\n            CdkVirtualForOf,\n            CdkVirtualScrollViewport,\n            CdkVirtualScrollableWindow,\n            CdkVirtualScrollableElement] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: ScrollingModule, imports: [BidiModule,\n            CdkScrollableModule, BidiModule, CdkScrollableModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: ScrollingModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        BidiModule,\n                        CdkScrollableModule,\n                        CdkVirtualScrollViewport,\n                        CdkFixedSizeVirtualScroll,\n                        CdkVirtualForOf,\n                        CdkVirtualScrollableWindow,\n                        CdkVirtualScrollableElement,\n                    ],\n                    exports: [\n                        BidiModule,\n                        CdkScrollableModule,\n                        CdkFixedSizeVirtualScroll,\n                        CdkVirtualForOf,\n                        CdkVirtualScrollViewport,\n                        CdkVirtualScrollableWindow,\n                        CdkVirtualScrollableElement,\n                    ],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkFixedSizeVirtualScroll, CdkScrollable, CdkScrollableModule, CdkVirtualForOf, CdkVirtualScrollViewport, CdkVirtualScrollable, CdkVirtualScrollableElement, CdkVirtualScrollableWindow, DEFAULT_RESIZE_TIME, DEFAULT_SCROLL_TIME, FixedSizeVirtualScrollStrategy, ScrollDispatcher, ScrollingModule, VIRTUAL_SCROLLABLE, VIRTUAL_SCROLL_STRATEGY, ViewportRuler, _fixedSizeVirtualScrollStrategyFactory };\n"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,aAAa,QAAQ,uBAAuB;AAC3E,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,eAAe;AAC9O,SAASC,OAAO,EAAEC,EAAE,EAAEC,UAAU,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,aAAa,EAAEC,YAAY,EAAEC,YAAY,QAAQ,MAAM;AAC7H,SAASC,oBAAoB,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,gBAAgB;AAChI,OAAO,KAAKC,EAAE,MAAM,uBAAuB;AAC3C,SAASC,oBAAoB,EAAEC,iBAAiB,EAAEC,sBAAsB,EAAEC,QAAQ,QAAQ,uBAAuB;AACjH,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,OAAO,KAAKC,IAAI,MAAM,0BAA0B;AAChD,SAASC,YAAY,EAAEC,eAAe,EAAEC,uBAAuB,EAAEC,4BAA4B,QAAQ,0BAA0B;;AAE/H;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AACA,MAAMC,uBAAuB,gBAAG,IAAIhD,cAAc,CAAC,yBAAyB,CAAC;;AAE7E;AACA,MAAMiD,8BAA8B,CAAC;EACjC;AACJ;AACA;AACA;AACA;EACIC,WAAWA,CAACC,QAAQ,EAAEC,WAAW,EAAEC,WAAW,EAAE;IAC5C,IAAI,CAACC,oBAAoB,GAAG,IAAIrC,OAAO,CAAC,CAAC;IACzC;IACA,IAAI,CAACsC,mBAAmB,GAAG,IAAI,CAACD,oBAAoB,CAACE,IAAI,CAAC/B,oBAAoB,CAAC,CAAC,CAAC;IACjF;IACA,IAAI,CAACgC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,SAAS,GAAGP,QAAQ;IACzB,IAAI,CAACQ,YAAY,GAAGP,WAAW;IAC/B,IAAI,CAACQ,YAAY,GAAGP,WAAW;EACnC;EACA;AACJ;AACA;AACA;EACIQ,MAAMA,CAACC,QAAQ,EAAE;IACb,IAAI,CAACL,SAAS,GAAGK,QAAQ;IACzB,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACX,oBAAoB,CAACY,QAAQ,CAAC,CAAC;IACpC,IAAI,CAACT,SAAS,GAAG,IAAI;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIU,uBAAuBA,CAAChB,QAAQ,EAAEC,WAAW,EAAEC,WAAW,EAAE;IACxD,IAAIA,WAAW,GAAGD,WAAW,KAAK,OAAOgB,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC9E,MAAMC,KAAK,CAAC,8EAA8E,CAAC;IAC/F;IACA,IAAI,CAACX,SAAS,GAAGP,QAAQ;IACzB,IAAI,CAACQ,YAAY,GAAGP,WAAW;IAC/B,IAAI,CAACQ,YAAY,GAAGP,WAAW;IAC/B,IAAI,CAACU,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAM,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACN,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAO,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACR,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;EACAQ,iBAAiBA,CAAA,EAAG;IAChB;EAAA;EAEJ;EACAC,uBAAuBA,CAAA,EAAG;IACtB;EAAA;EAEJ;AACJ;AACA;AACA;AACA;EACIC,aAAaA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IAC3B,IAAI,IAAI,CAACnB,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACoB,cAAc,CAACF,KAAK,GAAG,IAAI,CAACjB,SAAS,EAAEkB,QAAQ,CAAC;IACnE;EACJ;EACA;EACAb,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAAC,IAAI,CAACN,SAAS,EAAE;MACjB;IACJ;IACA,IAAI,CAACA,SAAS,CAACqB,mBAAmB,CAAC,IAAI,CAACrB,SAAS,CAACsB,aAAa,CAAC,CAAC,GAAG,IAAI,CAACrB,SAAS,CAAC;EACvF;EACA;EACAM,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAACP,SAAS,EAAE;MACjB;IACJ;IACA,MAAMuB,aAAa,GAAG,IAAI,CAACvB,SAAS,CAACwB,gBAAgB,CAAC,CAAC;IACvD,MAAMC,QAAQ,GAAG;MAAEC,KAAK,EAAEH,aAAa,CAACG,KAAK;MAAEC,GAAG,EAAEJ,aAAa,CAACI;IAAI,CAAC;IACvE,MAAMC,YAAY,GAAG,IAAI,CAAC5B,SAAS,CAAC6B,eAAe,CAAC,CAAC;IACrD,MAAMC,UAAU,GAAG,IAAI,CAAC9B,SAAS,CAACsB,aAAa,CAAC,CAAC;IACjD,IAAIS,YAAY,GAAG,IAAI,CAAC/B,SAAS,CAACgC,mBAAmB,CAAC,CAAC;IACvD;IACA,IAAIC,iBAAiB,GAAG,IAAI,CAAChC,SAAS,GAAG,CAAC,GAAG8B,YAAY,GAAG,IAAI,CAAC9B,SAAS,GAAG,CAAC;IAC9E;IACA,IAAIwB,QAAQ,CAACE,GAAG,GAAGG,UAAU,EAAE;MAC3B;MACA,MAAMI,eAAe,GAAGC,IAAI,CAACC,IAAI,CAACR,YAAY,GAAG,IAAI,CAAC3B,SAAS,CAAC;MAChE,MAAMoC,eAAe,GAAGF,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACI,GAAG,CAACN,iBAAiB,EAAEH,UAAU,GAAGI,eAAe,CAAC,CAAC;MAC9F;MACA;MACA,IAAID,iBAAiB,IAAII,eAAe,EAAE;QACtCJ,iBAAiB,GAAGI,eAAe;QACnCN,YAAY,GAAGM,eAAe,GAAG,IAAI,CAACpC,SAAS;QAC/CwB,QAAQ,CAACC,KAAK,GAAGS,IAAI,CAACK,KAAK,CAACP,iBAAiB,CAAC;MAClD;MACAR,QAAQ,CAACE,GAAG,GAAGQ,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACI,GAAG,CAACT,UAAU,EAAEL,QAAQ,CAACC,KAAK,GAAGQ,eAAe,CAAC,CAAC;IACtF;IACA,MAAMO,WAAW,GAAGV,YAAY,GAAGN,QAAQ,CAACC,KAAK,GAAG,IAAI,CAACzB,SAAS;IAClE,IAAIwC,WAAW,GAAG,IAAI,CAACvC,YAAY,IAAIuB,QAAQ,CAACC,KAAK,IAAI,CAAC,EAAE;MACxD,MAAMgB,WAAW,GAAGP,IAAI,CAACC,IAAI,CAAC,CAAC,IAAI,CAACjC,YAAY,GAAGsC,WAAW,IAAI,IAAI,CAACxC,SAAS,CAAC;MACjFwB,QAAQ,CAACC,KAAK,GAAGS,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEb,QAAQ,CAACC,KAAK,GAAGgB,WAAW,CAAC;MAC1DjB,QAAQ,CAACE,GAAG,GAAGQ,IAAI,CAACI,GAAG,CAACT,UAAU,EAAEK,IAAI,CAACC,IAAI,CAACH,iBAAiB,GAAG,CAACL,YAAY,GAAG,IAAI,CAAC1B,YAAY,IAAI,IAAI,CAACD,SAAS,CAAC,CAAC;IAC3H,CAAC,MACI;MACD,MAAM0C,SAAS,GAAGlB,QAAQ,CAACE,GAAG,GAAG,IAAI,CAAC1B,SAAS,IAAI8B,YAAY,GAAGH,YAAY,CAAC;MAC/E,IAAIe,SAAS,GAAG,IAAI,CAACzC,YAAY,IAAIuB,QAAQ,CAACE,GAAG,IAAIG,UAAU,EAAE;QAC7D,MAAMc,SAAS,GAAGT,IAAI,CAACC,IAAI,CAAC,CAAC,IAAI,CAACjC,YAAY,GAAGwC,SAAS,IAAI,IAAI,CAAC1C,SAAS,CAAC;QAC7E,IAAI2C,SAAS,GAAG,CAAC,EAAE;UACfnB,QAAQ,CAACE,GAAG,GAAGQ,IAAI,CAACI,GAAG,CAACT,UAAU,EAAEL,QAAQ,CAACE,GAAG,GAAGiB,SAAS,CAAC;UAC7DnB,QAAQ,CAACC,KAAK,GAAGS,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEH,IAAI,CAACK,KAAK,CAACP,iBAAiB,GAAG,IAAI,CAAC/B,YAAY,GAAG,IAAI,CAACD,SAAS,CAAC,CAAC;QACpG;MACJ;IACJ;IACA,IAAI,CAACD,SAAS,CAAC6C,gBAAgB,CAACpB,QAAQ,CAAC;IACzC,IAAI,CAACzB,SAAS,CAAC8C,wBAAwB,CAAC,IAAI,CAAC7C,SAAS,GAAGwB,QAAQ,CAACC,KAAK,CAAC;IACxE,IAAI,CAAC7B,oBAAoB,CAACkD,IAAI,CAACZ,IAAI,CAACK,KAAK,CAACP,iBAAiB,CAAC,CAAC;EACjE;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASe,sCAAsCA,CAACC,YAAY,EAAE;EAC1D,OAAOA,YAAY,CAACC,eAAe;AACvC;AACA;AAAA,IACMC,yBAAyB;EAA/B,MAAMA,yBAAyB,CAAC;IAC5B1D,WAAWA,CAAA,EAAG;MACV,IAAI,CAACQ,SAAS,GAAG,EAAE;MACnB,IAAI,CAACC,YAAY,GAAG,GAAG;MACvB,IAAI,CAACC,YAAY,GAAG,GAAG;MACvB;MACA,IAAI,CAAC+C,eAAe,GAAG,IAAI1D,8BAA8B,CAAC,IAAI,CAACE,QAAQ,EAAE,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,WAAW,CAAC;IAChH;IACA;IACA,IAAIF,QAAQA,CAAA,EAAG;MACX,OAAO,IAAI,CAACO,SAAS;IACzB;IACA,IAAIP,QAAQA,CAAC0D,KAAK,EAAE;MAChB,IAAI,CAACnD,SAAS,GAAG7D,oBAAoB,CAACgH,KAAK,CAAC;IAChD;IACA;AACJ;AACA;AACA;IACI,IAAIzD,WAAWA,CAAA,EAAG;MACd,OAAO,IAAI,CAACO,YAAY;IAC5B;IACA,IAAIP,WAAWA,CAACyD,KAAK,EAAE;MACnB,IAAI,CAAClD,YAAY,GAAG9D,oBAAoB,CAACgH,KAAK,CAAC;IACnD;IACA;AACJ;AACA;IACI,IAAIxD,WAAWA,CAAA,EAAG;MACd,OAAO,IAAI,CAACO,YAAY;IAC5B;IACA,IAAIP,WAAWA,CAACwD,KAAK,EAAE;MACnB,IAAI,CAACjD,YAAY,GAAG/D,oBAAoB,CAACgH,KAAK,CAAC;IACnD;IACAC,WAAWA,CAAA,EAAG;MACV,IAAI,CAACH,eAAe,CAACxC,uBAAuB,CAAC,IAAI,CAAChB,QAAQ,EAAE,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,WAAW,CAAC;IACnG;IAAC,QAAA0D,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAC,kCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFN,yBAAyB;IAAA,CAAmD;IAAA,QAAAO,EAAA,GAC7K,IAAI,CAACC,IAAI,kBAD8ErH,EAAE,CAAAsH,iBAAA;MAAAC,IAAA,EACJV,yBAAyB;MAAAW,SAAA;MAAAC,MAAA;QAAArE,QAAA;QAAAC,WAAA;QAAAC,WAAA;MAAA;MAAAoE,UAAA;MAAAC,QAAA,GADvB3H,EAAE,CAAA4H,kBAAA,CACmM,CAC7R;QACIC,OAAO,EAAE5E,uBAAuB;QAChC6E,UAAU,EAAEpB,sCAAsC;QAClDqB,IAAI,EAAE,CAAC7H,UAAU,CAAC,MAAM2G,yBAAyB,CAAC;MACtD,CAAC,CACJ,GAP2F7G,EAAE,CAAAgI,oBAAA;IAAA,EAOvD;EAC/C;EAAC,OA7CKnB,yBAAyB;AAAA;AA8C/B;EAAA,QAAAxC,SAAA,oBAAAA,SAAA;AAAA;;AAqBA;AACA,MAAM4D,mBAAmB,GAAG,EAAE;AAC9B;AACA;AACA;AACA;AAHA,IAIMC,gBAAgB;EAAtB,MAAMA,gBAAgB,CAAC;IACnB/E,WAAWA,CAACgF,OAAO,EAAEC,SAAS,EAAEC,QAAQ,EAAE;MACtC,IAAI,CAACF,OAAO,GAAGA,OAAO;MACtB,IAAI,CAACC,SAAS,GAAGA,SAAS;MAC1B;MACA,IAAI,CAACE,SAAS,GAAG,IAAIpH,OAAO,CAAC,CAAC;MAC9B;MACA,IAAI,CAACqH,mBAAmB,GAAG,IAAI;MAC/B;MACA,IAAI,CAACC,cAAc,GAAG,CAAC;MACvB;AACR;AACA;AACA;MACQ,IAAI,CAACC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;MACjC,IAAI,CAACC,SAAS,GAAGN,QAAQ;IAC7B;IACA;AACJ;AACA;AACA;AACA;IACIO,QAAQA,CAACC,UAAU,EAAE;MACjB,IAAI,CAAC,IAAI,CAACJ,gBAAgB,CAACK,GAAG,CAACD,UAAU,CAAC,EAAE;QACxC,IAAI,CAACJ,gBAAgB,CAACM,GAAG,CAACF,UAAU,EAAEA,UAAU,CAACG,eAAe,CAAC,CAAC,CAACC,SAAS,CAAC,MAAM,IAAI,CAACX,SAAS,CAAC7B,IAAI,CAACoC,UAAU,CAAC,CAAC,CAAC;MACxH;IACJ;IACA;AACJ;AACA;AACA;IACIK,UAAUA,CAACL,UAAU,EAAE;MACnB,MAAMM,mBAAmB,GAAG,IAAI,CAACV,gBAAgB,CAACW,GAAG,CAACP,UAAU,CAAC;MACjE,IAAIM,mBAAmB,EAAE;QACrBA,mBAAmB,CAACE,WAAW,CAAC,CAAC;QACjC,IAAI,CAACZ,gBAAgB,CAACa,MAAM,CAACT,UAAU,CAAC;MAC5C;IACJ;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACIU,QAAQA,CAACC,aAAa,GAAGvB,mBAAmB,EAAE;MAC1C,IAAI,CAAC,IAAI,CAACG,SAAS,CAACqB,SAAS,EAAE;QAC3B,OAAOtI,EAAE,CAAC,CAAC;MACf;MACA,OAAO,IAAIC,UAAU,CAAEsI,QAAQ,IAAK;QAChC,IAAI,CAAC,IAAI,CAACnB,mBAAmB,EAAE;UAC3B,IAAI,CAACoB,kBAAkB,CAAC,CAAC;QAC7B;QACA;QACA;QACA,MAAMC,YAAY,GAAGJ,aAAa,GAAG,CAAC,GAChC,IAAI,CAAClB,SAAS,CAAC7E,IAAI,CAAC9B,SAAS,CAAC6H,aAAa,CAAC,CAAC,CAACP,SAAS,CAACS,QAAQ,CAAC,GACjE,IAAI,CAACpB,SAAS,CAACW,SAAS,CAACS,QAAQ,CAAC;QACxC,IAAI,CAAClB,cAAc,EAAE;QACrB,OAAO,MAAM;UACToB,YAAY,CAACP,WAAW,CAAC,CAAC;UAC1B,IAAI,CAACb,cAAc,EAAE;UACrB,IAAI,CAAC,IAAI,CAACA,cAAc,EAAE;YACtB,IAAI,CAACqB,qBAAqB,CAAC,CAAC;UAChC;QACJ,CAAC;MACL,CAAC,CAAC;IACN;IACAC,WAAWA,CAAA,EAAG;MACV,IAAI,CAACD,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAACpB,gBAAgB,CAACsB,OAAO,CAAC,CAAC/C,CAAC,EAAEgD,SAAS,KAAK,IAAI,CAACd,UAAU,CAACc,SAAS,CAAC,CAAC;MAC3E,IAAI,CAAC1B,SAAS,CAACnE,QAAQ,CAAC,CAAC;IAC7B;IACA;AACJ;AACA;AACA;AACA;AACA;IACI8F,gBAAgBA,CAACC,mBAAmB,EAAEV,aAAa,EAAE;MACjD,MAAMW,SAAS,GAAG,IAAI,CAACC,2BAA2B,CAACF,mBAAmB,CAAC;MACvE,OAAO,IAAI,CAACX,QAAQ,CAACC,aAAa,CAAC,CAAC/F,IAAI,CAAC7B,MAAM,CAACyI,MAAM,IAAI;QACtD,OAAO,CAACA,MAAM,IAAIF,SAAS,CAACG,OAAO,CAACD,MAAM,CAAC,GAAG,CAAC,CAAC;MACpD,CAAC,CAAC,CAAC;IACP;IACA;IACAD,2BAA2BA,CAACF,mBAAmB,EAAE;MAC7C,MAAMK,mBAAmB,GAAG,EAAE;MAC9B,IAAI,CAAC9B,gBAAgB,CAACsB,OAAO,CAAC,CAACS,aAAa,EAAE3B,UAAU,KAAK;QACzD,IAAI,IAAI,CAAC4B,0BAA0B,CAAC5B,UAAU,EAAEqB,mBAAmB,CAAC,EAAE;UAClEK,mBAAmB,CAACG,IAAI,CAAC7B,UAAU,CAAC;QACxC;MACJ,CAAC,CAAC;MACF,OAAO0B,mBAAmB;IAC9B;IACA;IACAI,UAAUA,CAAA,EAAG;MACT,OAAO,IAAI,CAAChC,SAAS,CAACiC,WAAW,IAAIC,MAAM;IAC/C;IACA;IACAJ,0BAA0BA,CAAC5B,UAAU,EAAEqB,mBAAmB,EAAE;MACxD,IAAIY,OAAO,GAAG/K,aAAa,CAACmK,mBAAmB,CAAC;MAChD,IAAIa,iBAAiB,GAAGlC,UAAU,CAACmC,aAAa,CAAC,CAAC,CAACC,aAAa;MAChE;MACA;MACA,GAAG;QACC,IAAIH,OAAO,IAAIC,iBAAiB,EAAE;UAC9B,OAAO,IAAI;QACf;MACJ,CAAC,QAASD,OAAO,GAAGA,OAAO,CAACI,aAAa;MACzC,OAAO,KAAK;IAChB;IACA;IACAvB,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAACpB,mBAAmB,GAAG,IAAI,CAACJ,OAAO,CAACgD,iBAAiB,CAAC,MAAM;QAC5D,MAAMN,MAAM,GAAG,IAAI,CAACF,UAAU,CAAC,CAAC;QAChC,OAAOtJ,SAAS,CAACwJ,MAAM,CAACxC,QAAQ,EAAE,QAAQ,CAAC,CAACY,SAAS,CAAC,MAAM,IAAI,CAACX,SAAS,CAAC7B,IAAI,CAAC,CAAC,CAAC;MACtF,CAAC,CAAC;IACN;IACA;IACAoD,qBAAqBA,CAAA,EAAG;MACpB,IAAI,IAAI,CAACtB,mBAAmB,EAAE;QAC1B,IAAI,CAACA,mBAAmB,CAACc,WAAW,CAAC,CAAC;QACtC,IAAI,CAACd,mBAAmB,GAAG,IAAI;MACnC;IACJ;IAAC,QAAAvB,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAmE,yBAAAjE,CAAA;MAAA,YAAAA,CAAA,IAAwFe,gBAAgB,EArK1BlI,EAAE,CAAAqL,QAAA,CAqK0CrL,EAAE,CAACsL,MAAM,GArKrDtL,EAAE,CAAAqL,QAAA,CAqKgEnJ,EAAE,CAACI,QAAQ,GArK7EtC,EAAE,CAAAqL,QAAA,CAqKwF9I,QAAQ;IAAA,CAA6D;IAAA,QAAA6E,EAAA,GACtP,IAAI,CAACmE,KAAK,kBAtK6EvL,EAAE,CAAAwL,kBAAA;MAAAC,KAAA,EAsKYvD,gBAAgB;MAAAwD,OAAA,EAAhBxD,gBAAgB,CAAAjB,IAAA;MAAA0E,UAAA,EAAc;IAAM,EAAG;EACzJ;EAAC,OAnIKzD,gBAAgB;AAAA;AAoItB;EAAA,QAAA7D,SAAA,oBAAAA,SAAA;AAAA;;AAUA;AACA;AACA;AACA;AACA;AAJA,IAKMuH,aAAa;EAAnB,MAAMA,aAAa,CAAC;IAChBzI,WAAWA,CAAC0I,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,GAAG,EAAE;MACnD,IAAI,CAACH,UAAU,GAAGA,UAAU;MAC5B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;MACxC,IAAI,CAACC,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACC,GAAG,GAAGA,GAAG;MACd,IAAI,CAACC,UAAU,GAAG,IAAI/K,OAAO,CAAC,CAAC;MAC/B,IAAI,CAACgL,gBAAgB,GAAG,IAAI9K,UAAU,CAAEsI,QAAQ,IAAK,IAAI,CAACqC,MAAM,CAACZ,iBAAiB,CAAC,MAAM9J,SAAS,CAAC,IAAI,CAACwK,UAAU,CAACZ,aAAa,EAAE,QAAQ,CAAC,CACtIxH,IAAI,CAAC5B,SAAS,CAAC,IAAI,CAACoK,UAAU,CAAC,CAAC,CAChChD,SAAS,CAACS,QAAQ,CAAC,CAAC,CAAC;IAC9B;IACAyC,QAAQA,CAAA,EAAG;MACP,IAAI,CAACL,gBAAgB,CAAClD,QAAQ,CAAC,IAAI,CAAC;IACxC;IACAkB,WAAWA,CAAA,EAAG;MACV,IAAI,CAACgC,gBAAgB,CAAC5C,UAAU,CAAC,IAAI,CAAC;MACtC,IAAI,CAAC+C,UAAU,CAACxF,IAAI,CAAC,CAAC;MACtB,IAAI,CAACwF,UAAU,CAAC9H,QAAQ,CAAC,CAAC;IAC9B;IACA;IACA6E,eAAeA,CAAA,EAAG;MACd,OAAO,IAAI,CAACkD,gBAAgB;IAChC;IACA;IACAlB,aAAaA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACa,UAAU;IAC1B;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACIO,QAAQA,CAACC,OAAO,EAAE;MACd,MAAMC,EAAE,GAAG,IAAI,CAACT,UAAU,CAACZ,aAAa;MACxC,MAAMsB,KAAK,GAAG,IAAI,CAACP,GAAG,IAAI,IAAI,CAACA,GAAG,CAAClF,KAAK,IAAI,KAAK;MACjD;MACA,IAAIuF,OAAO,CAACG,IAAI,IAAI,IAAI,EAAE;QACtBH,OAAO,CAACG,IAAI,GAAGD,KAAK,GAAGF,OAAO,CAAChH,GAAG,GAAGgH,OAAO,CAACjH,KAAK;MACtD;MACA,IAAIiH,OAAO,CAACI,KAAK,IAAI,IAAI,EAAE;QACvBJ,OAAO,CAACI,KAAK,GAAGF,KAAK,GAAGF,OAAO,CAACjH,KAAK,GAAGiH,OAAO,CAAChH,GAAG;MACvD;MACA;MACA,IAAIgH,OAAO,CAACK,MAAM,IAAI,IAAI,EAAE;QACxBL,OAAO,CAACM,GAAG,GACPL,EAAE,CAACM,YAAY,GAAGN,EAAE,CAACO,YAAY,GAAGR,OAAO,CAACK,MAAM;MAC1D;MACA;MACA,IAAIH,KAAK,IAAIpK,oBAAoB,CAAC,CAAC,IAAIC,iBAAiB,CAAC0K,MAAM,EAAE;QAC7D,IAAIT,OAAO,CAACG,IAAI,IAAI,IAAI,EAAE;UACtBH,OAAO,CAACI,KAAK,GACTH,EAAE,CAACS,WAAW,GAAGT,EAAE,CAACU,WAAW,GAAGX,OAAO,CAACG,IAAI;QACtD;QACA,IAAIrK,oBAAoB,CAAC,CAAC,IAAIC,iBAAiB,CAAC6K,QAAQ,EAAE;UACtDZ,OAAO,CAACG,IAAI,GAAGH,OAAO,CAACI,KAAK;QAChC,CAAC,MACI,IAAItK,oBAAoB,CAAC,CAAC,IAAIC,iBAAiB,CAAC8K,OAAO,EAAE;UAC1Db,OAAO,CAACG,IAAI,GAAGH,OAAO,CAACI,KAAK,GAAG,CAACJ,OAAO,CAACI,KAAK,GAAGJ,OAAO,CAACI,KAAK;QACjE;MACJ,CAAC,MACI;QACD,IAAIJ,OAAO,CAACI,KAAK,IAAI,IAAI,EAAE;UACvBJ,OAAO,CAACG,IAAI,GACRF,EAAE,CAACS,WAAW,GAAGT,EAAE,CAACU,WAAW,GAAGX,OAAO,CAACI,KAAK;QACvD;MACJ;MACA,IAAI,CAACU,qBAAqB,CAACd,OAAO,CAAC;IACvC;IACAc,qBAAqBA,CAACd,OAAO,EAAE;MAC3B,MAAMC,EAAE,GAAG,IAAI,CAACT,UAAU,CAACZ,aAAa;MACxC,IAAI5I,sBAAsB,CAAC,CAAC,EAAE;QAC1BiK,EAAE,CAACF,QAAQ,CAACC,OAAO,CAAC;MACxB,CAAC,MACI;QACD,IAAIA,OAAO,CAACM,GAAG,IAAI,IAAI,EAAE;UACrBL,EAAE,CAACc,SAAS,GAAGf,OAAO,CAACM,GAAG;QAC9B;QACA,IAAIN,OAAO,CAACG,IAAI,IAAI,IAAI,EAAE;UACtBF,EAAE,CAACe,UAAU,GAAGhB,OAAO,CAACG,IAAI;QAChC;MACJ;IACJ;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI9G,mBAAmBA,CAAC4H,IAAI,EAAE;MACtB,MAAMC,IAAI,GAAG,MAAM;MACnB,MAAMC,KAAK,GAAG,OAAO;MACrB,MAAMlB,EAAE,GAAG,IAAI,CAACT,UAAU,CAACZ,aAAa;MACxC,IAAIqC,IAAI,IAAI,KAAK,EAAE;QACf,OAAOhB,EAAE,CAACc,SAAS;MACvB;MACA,IAAIE,IAAI,IAAI,QAAQ,EAAE;QAClB,OAAOhB,EAAE,CAACM,YAAY,GAAGN,EAAE,CAACO,YAAY,GAAGP,EAAE,CAACc,SAAS;MAC3D;MACA;MACA,MAAMb,KAAK,GAAG,IAAI,CAACP,GAAG,IAAI,IAAI,CAACA,GAAG,CAAClF,KAAK,IAAI,KAAK;MACjD,IAAIwG,IAAI,IAAI,OAAO,EAAE;QACjBA,IAAI,GAAGf,KAAK,GAAGiB,KAAK,GAAGD,IAAI;MAC/B,CAAC,MACI,IAAID,IAAI,IAAI,KAAK,EAAE;QACpBA,IAAI,GAAGf,KAAK,GAAGgB,IAAI,GAAGC,KAAK;MAC/B;MACA,IAAIjB,KAAK,IAAIpK,oBAAoB,CAAC,CAAC,IAAIC,iBAAiB,CAAC6K,QAAQ,EAAE;QAC/D;QACA;QACA,IAAIK,IAAI,IAAIC,IAAI,EAAE;UACd,OAAOjB,EAAE,CAACS,WAAW,GAAGT,EAAE,CAACU,WAAW,GAAGV,EAAE,CAACe,UAAU;QAC1D,CAAC,MACI;UACD,OAAOf,EAAE,CAACe,UAAU;QACxB;MACJ,CAAC,MACI,IAAId,KAAK,IAAIpK,oBAAoB,CAAC,CAAC,IAAIC,iBAAiB,CAAC8K,OAAO,EAAE;QACnE;QACA;QACA,IAAII,IAAI,IAAIC,IAAI,EAAE;UACd,OAAOjB,EAAE,CAACe,UAAU,GAAGf,EAAE,CAACS,WAAW,GAAGT,EAAE,CAACU,WAAW;QAC1D,CAAC,MACI;UACD,OAAO,CAACV,EAAE,CAACe,UAAU;QACzB;MACJ,CAAC,MACI;QACD;QACA;QACA,IAAIC,IAAI,IAAIC,IAAI,EAAE;UACd,OAAOjB,EAAE,CAACe,UAAU;QACxB,CAAC,MACI;UACD,OAAOf,EAAE,CAACS,WAAW,GAAGT,EAAE,CAACU,WAAW,GAAGV,EAAE,CAACe,UAAU;QAC1D;MACJ;IACJ;IAAC,QAAArG,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAwG,sBAAAtG,CAAA;MAAA,YAAAA,CAAA,IAAwFyE,aAAa,EAtUvB5L,EAAE,CAAA0N,iBAAA,CAsUuC1N,EAAE,CAACgB,UAAU,GAtUtDhB,EAAE,CAAA0N,iBAAA,CAsUiExF,gBAAgB,GAtUnFlI,EAAE,CAAA0N,iBAAA,CAsU8F1N,EAAE,CAACsL,MAAM,GAtUzGtL,EAAE,CAAA0N,iBAAA,CAsUoHlL,EAAE,CAACmL,cAAc;IAAA,CAA4D;IAAA,QAAAvG,EAAA,GAC1R,IAAI,CAACC,IAAI,kBAvU8ErH,EAAE,CAAAsH,iBAAA;MAAAC,IAAA,EAuUJqE,aAAa;MAAApE,SAAA;MAAAE,UAAA;IAAA,EAAoF;EACnM;EAAC,OAjJKkE,aAAa;AAAA;AAkJnB;EAAA,QAAAvH,SAAA,oBAAAA,SAAA;AAAA;;AAUA;AACA,MAAMuJ,mBAAmB,GAAG,EAAE;AAC9B;AACA;AACA;AACA;AAHA,IAIMC,aAAa;EAAnB,MAAMA,aAAa,CAAC;IAChB1K,WAAWA,CAACiF,SAAS,EAAE2D,MAAM,EAAE1D,QAAQ,EAAE;MACrC,IAAI,CAACD,SAAS,GAAGA,SAAS;MAC1B;MACA,IAAI,CAAC0F,OAAO,GAAG,IAAI5M,OAAO,CAAC,CAAC;MAC5B;MACA,IAAI,CAAC6M,eAAe,GAAIC,KAAK,IAAK;QAC9B,IAAI,CAACF,OAAO,CAACrH,IAAI,CAACuH,KAAK,CAAC;MAC5B,CAAC;MACD,IAAI,CAACrF,SAAS,GAAGN,QAAQ;MACzB0D,MAAM,CAACZ,iBAAiB,CAAC,MAAM;QAC3B,IAAI/C,SAAS,CAACqB,SAAS,EAAE;UACrB,MAAMoB,MAAM,GAAG,IAAI,CAACF,UAAU,CAAC,CAAC;UAChC;UACA;UACAE,MAAM,CAACoD,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACF,eAAe,CAAC;UACvDlD,MAAM,CAACoD,gBAAgB,CAAC,mBAAmB,EAAE,IAAI,CAACF,eAAe,CAAC;QACtE;QACA;QACA;QACA,IAAI,CAACG,MAAM,CAAC,CAAC,CAACjF,SAAS,CAAC,MAAO,IAAI,CAACkF,aAAa,GAAG,IAAK,CAAC;MAC9D,CAAC,CAAC;IACN;IACArE,WAAWA,CAAA,EAAG;MACV,IAAI,IAAI,CAAC1B,SAAS,CAACqB,SAAS,EAAE;QAC1B,MAAMoB,MAAM,GAAG,IAAI,CAACF,UAAU,CAAC,CAAC;QAChCE,MAAM,CAACuD,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACL,eAAe,CAAC;QAC1DlD,MAAM,CAACuD,mBAAmB,CAAC,mBAAmB,EAAE,IAAI,CAACL,eAAe,CAAC;MACzE;MACA,IAAI,CAACD,OAAO,CAAC3J,QAAQ,CAAC,CAAC;IAC3B;IACA;IACAoB,eAAeA,CAAA,EAAG;MACd,IAAI,CAAC,IAAI,CAAC4I,aAAa,EAAE;QACrB,IAAI,CAACE,mBAAmB,CAAC,CAAC;MAC9B;MACA,MAAMC,MAAM,GAAG;QAAEC,KAAK,EAAE,IAAI,CAACJ,aAAa,CAACI,KAAK;QAAEC,MAAM,EAAE,IAAI,CAACL,aAAa,CAACK;MAAO,CAAC;MACrF;MACA,IAAI,CAAC,IAAI,CAACpG,SAAS,CAACqB,SAAS,EAAE;QAC3B,IAAI,CAAC0E,aAAa,GAAG,IAAI;MAC7B;MACA,OAAOG,MAAM;IACjB;IACA;IACAG,eAAeA,CAAA,EAAG;MACd;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAMC,cAAc,GAAG,IAAI,CAACC,yBAAyB,CAAC,CAAC;MACvD,MAAM;QAAEJ,KAAK;QAAEC;MAAO,CAAC,GAAG,IAAI,CAACjJ,eAAe,CAAC,CAAC;MAChD,OAAO;QACHoH,GAAG,EAAE+B,cAAc,CAAC/B,GAAG;QACvBH,IAAI,EAAEkC,cAAc,CAAClC,IAAI;QACzBE,MAAM,EAAEgC,cAAc,CAAC/B,GAAG,GAAG6B,MAAM;QACnC/B,KAAK,EAAEiC,cAAc,CAAClC,IAAI,GAAG+B,KAAK;QAClCC,MAAM;QACND;MACJ,CAAC;IACL;IACA;IACAI,yBAAyBA,CAAA,EAAG;MACxB;MACA;MACA,IAAI,CAAC,IAAI,CAACvG,SAAS,CAACqB,SAAS,EAAE;QAC3B,OAAO;UAAEkD,GAAG,EAAE,CAAC;UAAEH,IAAI,EAAE;QAAE,CAAC;MAC9B;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAMnE,QAAQ,GAAG,IAAI,CAACM,SAAS;MAC/B,MAAMkC,MAAM,GAAG,IAAI,CAACF,UAAU,CAAC,CAAC;MAChC,MAAMiE,eAAe,GAAGvG,QAAQ,CAACuG,eAAe;MAChD,MAAMC,YAAY,GAAGD,eAAe,CAACE,qBAAqB,CAAC,CAAC;MAC5D,MAAMnC,GAAG,GAAG,CAACkC,YAAY,CAAClC,GAAG,IACzBtE,QAAQ,CAAC0G,IAAI,CAAC3B,SAAS,IACvBvC,MAAM,CAACmE,OAAO,IACdJ,eAAe,CAACxB,SAAS,IACzB,CAAC;MACL,MAAMZ,IAAI,GAAG,CAACqC,YAAY,CAACrC,IAAI,IAC3BnE,QAAQ,CAAC0G,IAAI,CAAC1B,UAAU,IACxBxC,MAAM,CAACoE,OAAO,IACdL,eAAe,CAACvB,UAAU,IAC1B,CAAC;MACL,OAAO;QAAEV,GAAG;QAAEH;MAAK,CAAC;IACxB;IACA;AACJ;AACA;AACA;AACA;IACI0B,MAAMA,CAACgB,YAAY,GAAGtB,mBAAmB,EAAE;MACvC,OAAOsB,YAAY,GAAG,CAAC,GAAG,IAAI,CAACpB,OAAO,CAACrK,IAAI,CAAC9B,SAAS,CAACuN,YAAY,CAAC,CAAC,GAAG,IAAI,CAACpB,OAAO;IACvF;IACA;IACAnD,UAAUA,CAAA,EAAG;MACT,OAAO,IAAI,CAAChC,SAAS,CAACiC,WAAW,IAAIC,MAAM;IAC/C;IACA;IACAwD,mBAAmBA,CAAA,EAAG;MAClB,MAAMxD,MAAM,GAAG,IAAI,CAACF,UAAU,CAAC,CAAC;MAChC,IAAI,CAACwD,aAAa,GAAG,IAAI,CAAC/F,SAAS,CAACqB,SAAS,GACvC;QAAE8E,KAAK,EAAE1D,MAAM,CAACsE,UAAU;QAAEX,MAAM,EAAE3D,MAAM,CAACuE;MAAY,CAAC,GACxD;QAAEb,KAAK,EAAE,CAAC;QAAEC,MAAM,EAAE;MAAE,CAAC;IACjC;IAAC,QAAAxH,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAoI,sBAAAlI,CAAA;MAAA,YAAAA,CAAA,IAAwF0G,aAAa,EA1cvB7N,EAAE,CAAAqL,QAAA,CA0cuCnJ,EAAE,CAACI,QAAQ,GA1cpDtC,EAAE,CAAAqL,QAAA,CA0c+DrL,EAAE,CAACsL,MAAM,GA1c1EtL,EAAE,CAAAqL,QAAA,CA0cqF9I,QAAQ;IAAA,CAA6D;IAAA,QAAA6E,EAAA,GACnP,IAAI,CAACmE,KAAK,kBA3c6EvL,EAAE,CAAAwL,kBAAA;MAAAC,KAAA,EA2cYoC,aAAa;MAAAnC,OAAA,EAAbmC,aAAa,CAAA5G,IAAA;MAAA0E,UAAA,EAAc;IAAM,EAAG;EACtJ;EAAC,OAnHKkC,aAAa;AAAA;AAoHnB;EAAA,QAAAxJ,SAAA,oBAAAA,SAAA;AAAA;AAUA,MAAMiL,kBAAkB,gBAAG,IAAIrP,cAAc,CAAC,oBAAoB,CAAC;AACnE;AACA;AACA;AAFA,IAGMsP,oBAAoB;EAA1B,MAAMA,oBAAoB,SAAS3D,aAAa,CAAC;IAC7CzI,WAAWA,CAAC0I,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,GAAG,EAAE;MACnD,KAAK,CAACH,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,GAAG,CAAC;IACpD;IACA;AACJ;AACA;AACA;AACA;IACIwD,mBAAmBA,CAACC,WAAW,EAAE;MAC7B,MAAMC,UAAU,GAAG,IAAI,CAAC7D,UAAU,CAACZ,aAAa;MAChD,OAAOwE,WAAW,KAAK,YAAY,GAAGC,UAAU,CAAC1C,WAAW,GAAG0C,UAAU,CAAC7C,YAAY;IAC1F;IAAC,QAAA7F,CAAA,GACQ,IAAI,CAACC,IAAI,YAAA0I,6BAAAxI,CAAA;MAAA,YAAAA,CAAA,IAAwFoI,oBAAoB,EAxe9BvP,EAAE,CAAA0N,iBAAA,CAwe8C1N,EAAE,CAACgB,UAAU,GAxe7DhB,EAAE,CAAA0N,iBAAA,CAwewExF,gBAAgB,GAxe1FlI,EAAE,CAAA0N,iBAAA,CAweqG1N,EAAE,CAACsL,MAAM,GAxehHtL,EAAE,CAAA0N,iBAAA,CAwe2HlL,EAAE,CAACmL,cAAc;IAAA,CAA4D;IAAA,QAAAvG,EAAA,GACjS,IAAI,CAACC,IAAI,kBAze8ErH,EAAE,CAAAsH,iBAAA;MAAAC,IAAA,EAyeJgI,oBAAoB;MAAA5H,QAAA,GAzelB3H,EAAE,CAAA4P,0BAAA;IAAA,EAyewD;EAC9J;EAAC,OAfKL,oBAAoB;AAAA;AAgB1B;EAAA,QAAAlL,SAAA,oBAAAA,SAAA;AAAA;;AAMA;AACA,SAASwL,WAAWA,CAACC,EAAE,EAAEC,EAAE,EAAE;EACzB,OAAOD,EAAE,CAAC1K,KAAK,IAAI2K,EAAE,CAAC3K,KAAK,IAAI0K,EAAE,CAACzK,GAAG,IAAI0K,EAAE,CAAC1K,GAAG;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,MAAM2K,gBAAgB,GAAG,OAAOC,qBAAqB,KAAK,WAAW,GAAG3O,uBAAuB,GAAGC,aAAa;AAC/G;AAAA,IACM2O,wBAAwB;EAA9B,MAAMA,wBAAwB,SAASX,oBAAoB,CAAC;IACxD;IACA,IAAIE,WAAWA,CAAA,EAAG;MACd,OAAO,IAAI,CAACU,YAAY;IAC5B;IACA,IAAIV,WAAWA,CAACA,WAAW,EAAE;MACzB,IAAI,IAAI,CAACU,YAAY,KAAKV,WAAW,EAAE;QACnC,IAAI,CAACU,YAAY,GAAGV,WAAW;QAC/B,IAAI,CAACW,oBAAoB,CAAC,CAAC;MAC/B;IACJ;IACAjN,WAAWA,CAAC0I,UAAU,EAAEwE,kBAAkB,EAAEtE,MAAM,EAAEnF,eAAe,EAAEoF,GAAG,EAAEF,gBAAgB,EAAEwE,aAAa,EAAEzH,UAAU,EAAE;MACnH,KAAK,CAACgD,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,GAAG,CAAC;MAChD,IAAI,CAACH,UAAU,GAAGA,UAAU;MAC5B,IAAI,CAACwE,kBAAkB,GAAGA,kBAAkB;MAC5C,IAAI,CAACzJ,eAAe,GAAGA,eAAe;MACtC,IAAI,CAACiC,UAAU,GAAGA,UAAU;MAC5B,IAAI,CAACT,SAAS,GAAG5H,MAAM,CAAC8B,QAAQ,CAAC;MACjC;MACA,IAAI,CAACiO,gBAAgB,GAAG,IAAIrP,OAAO,CAAC,CAAC;MACrC;MACA,IAAI,CAACsP,qBAAqB,GAAG,IAAItP,OAAO,CAAC,CAAC;MAC1C,IAAI,CAACiP,YAAY,GAAG,UAAU;MAC9B;AACR;AACA;AACA;MACQ,IAAI,CAACM,UAAU,GAAG,KAAK;MACvB;MACA;MACA;MACA;MACA;MACA,IAAI,CAACjN,mBAAmB,GAAG,IAAIpC,UAAU,CAAEsI,QAAQ,IAAK,IAAI,CAAC9C,eAAe,CAACpD,mBAAmB,CAACyF,SAAS,CAACrE,KAAK,IAAI8L,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAAC7E,MAAM,CAAC8E,GAAG,CAAC,MAAMnH,QAAQ,CAACjD,IAAI,CAAC7B,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MAC/L;MACA,IAAI,CAACkM,mBAAmB,GAAG,IAAI,CAACN,qBAAqB;MACrD;AACR;AACA;MACQ,IAAI,CAACO,iBAAiB,GAAG,CAAC;MAC1B;MACA,IAAI,CAACC,kBAAkB,GAAG,EAAE;MAC5B;MACA,IAAI,CAACC,mBAAmB,GAAG,EAAE;MAC7B;MACA,IAAI,CAACC,cAAc,GAAG;QAAE9L,KAAK,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE,CAAC;MAC1C;MACA,IAAI,CAAC8L,WAAW,GAAG,CAAC;MACpB;MACA,IAAI,CAAChD,aAAa,GAAG,CAAC;MACtB;MACA,IAAI,CAACiD,sBAAsB,GAAG,CAAC;MAC/B;AACR;AACA;AACA;MACQ,IAAI,CAACC,kCAAkC,GAAG,KAAK;MAC/C;MACA,IAAI,CAACC,yBAAyB,GAAG,KAAK;MACtC;MACA,IAAI,CAACC,wBAAwB,GAAG,EAAE;MAClC;MACA,IAAI,CAACC,gBAAgB,GAAGhQ,YAAY,CAACiQ,KAAK;MAC1C,IAAI,CAAC7K,eAAe,KAAK,OAAOvC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACrE,MAAMC,KAAK,CAAC,gFAAgF,CAAC;MACjG;MACA,IAAI,CAACkN,gBAAgB,GAAGlB,aAAa,CAACpC,MAAM,CAAC,CAAC,CAACjF,SAAS,CAAC,MAAM;QAC3D,IAAI,CAACyI,iBAAiB,CAAC,CAAC;MAC5B,CAAC,CAAC;MACF,IAAI,CAAC,IAAI,CAAC7I,UAAU,EAAE;QAClB;QACA,IAAI,CAACgD,UAAU,CAACZ,aAAa,CAAC0G,SAAS,CAACC,GAAG,CAAC,wBAAwB,CAAC;QACrE,IAAI,CAAC/I,UAAU,GAAG,IAAI;MAC1B;IACJ;IACAsD,QAAQA,CAAA,EAAG;MACP;MACA,IAAI,CAAC,IAAI,CAAC/D,SAAS,CAACqB,SAAS,EAAE;QAC3B;MACJ;MACA,IAAI,IAAI,CAACZ,UAAU,KAAK,IAAI,EAAE;QAC1B,KAAK,CAACsD,QAAQ,CAAC,CAAC;MACpB;MACA;MACA;MACA;MACA;MACA,IAAI,CAACJ,MAAM,CAACZ,iBAAiB,CAAC,MAAMuF,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QAC7D,IAAI,CAACiB,oBAAoB,CAAC,CAAC;QAC3B,IAAI,CAACjL,eAAe,CAAC9C,MAAM,CAAC,IAAI,CAAC;QACjC,IAAI,CAAC+E,UAAU,CACVG,eAAe,CAAC,CAAC,CACjBvF,IAAI;QACT;QACA3B,SAAS,CAAC,IAAI,CAAC;QACf;QACA;QACA;QACAH,SAAS,CAAC,CAAC,EAAEqO,gBAAgB,CAAC;QAC9B;QACA;QACA;QACAnO,SAAS,CAAC,IAAI,CAACoK,UAAU,CAAC,CAAC,CACtBhD,SAAS,CAAC,MAAM,IAAI,CAACrC,eAAe,CAACrC,iBAAiB,CAAC,CAAC,CAAC;QAC9D,IAAI,CAACuN,0BAA0B,CAAC,CAAC;MACrC,CAAC,CAAC,CAAC;IACP;IACAhI,WAAWA,CAAA,EAAG;MACV,IAAI,CAAC5F,MAAM,CAAC,CAAC;MACb,IAAI,CAAC0C,eAAe,CAAC1C,MAAM,CAAC,CAAC;MAC7B;MACA,IAAI,CAACsM,qBAAqB,CAACrM,QAAQ,CAAC,CAAC;MACrC,IAAI,CAACoM,gBAAgB,CAACpM,QAAQ,CAAC,CAAC;MAChC,IAAI,CAACqN,gBAAgB,CAACnI,WAAW,CAAC,CAAC;MACnC,KAAK,CAACS,WAAW,CAAC,CAAC;IACvB;IACA;IACAhG,MAAMA,CAACiO,KAAK,EAAE;MACV,IAAI,IAAI,CAACC,MAAM,KAAK,OAAO3N,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAChE,MAAMC,KAAK,CAAC,+CAA+C,CAAC;MAChE;MACA;MACA;MACA;MACA,IAAI,CAACyH,MAAM,CAACZ,iBAAiB,CAAC,MAAM;QAChC,IAAI,CAAC6G,MAAM,GAAGD,KAAK;QACnB,IAAI,CAACC,MAAM,CAACC,UAAU,CAACxO,IAAI,CAAC5B,SAAS,CAAC,IAAI,CAAC0O,gBAAgB,CAAC,CAAC,CAACtH,SAAS,CAACiJ,IAAI,IAAI;UAC5E,MAAMC,SAAS,GAAGD,IAAI,CAACE,MAAM;UAC7B,IAAID,SAAS,KAAK,IAAI,CAAChB,WAAW,EAAE;YAChC,IAAI,CAACA,WAAW,GAAGgB,SAAS;YAC5B,IAAI,CAACvL,eAAe,CAACpC,mBAAmB,CAAC,CAAC;UAC9C;UACA,IAAI,CAAC6N,kBAAkB,CAAC,CAAC;QAC7B,CAAC,CAAC;MACN,CAAC,CAAC;IACN;IACA;IACAnO,MAAMA,CAAA,EAAG;MACL,IAAI,CAAC8N,MAAM,GAAG,IAAI;MAClB,IAAI,CAACzB,gBAAgB,CAAC9J,IAAI,CAAC,CAAC;IAChC;IACA;IACAzB,aAAaA,CAAA,EAAG;MACZ,OAAO,IAAI,CAACmM,WAAW;IAC3B;IACA;IACA5L,eAAeA,CAAA,EAAG;MACd,OAAO,IAAI,CAAC4I,aAAa;IAC7B;IACA;IACA;IACA;IACA;IACA;IACAjJ,gBAAgBA,CAAA,EAAG;MACf,OAAO,IAAI,CAACgM,cAAc;IAC9B;IACAoB,yCAAyCA,CAAChF,IAAI,EAAE;MAC5C,OAAO,IAAI,CAACtC,aAAa,CAAC,CAAC,CAACC,aAAa,CAAC6D,qBAAqB,CAAC,CAAC,CAACxB,IAAI,CAAC;IAC3E;IACA;AACJ;AACA;AACA;IACIvI,mBAAmBA,CAACwN,IAAI,EAAE;MACtB,IAAI,IAAI,CAACxB,iBAAiB,KAAKwB,IAAI,EAAE;QACjC,IAAI,CAACxB,iBAAiB,GAAGwB,IAAI;QAC7B,IAAI,CAACnC,oBAAoB,CAAC,CAAC;QAC3B,IAAI,CAAC0B,0BAA0B,CAAC,CAAC;MACrC;IACJ;IACA;IACAvL,gBAAgBA,CAACiM,KAAK,EAAE;MACpB,IAAI,CAAC3C,WAAW,CAAC,IAAI,CAACqB,cAAc,EAAEsB,KAAK,CAAC,EAAE;QAC1C,IAAI,IAAI,CAAC/B,UAAU,EAAE;UACjB+B,KAAK,GAAG;YAAEpN,KAAK,EAAE,CAAC;YAAEC,GAAG,EAAEQ,IAAI,CAACG,GAAG,CAAC,IAAI,CAACkL,cAAc,CAAC7L,GAAG,EAAEmN,KAAK,CAACnN,GAAG;UAAE,CAAC;QAC3E;QACA,IAAI,CAACmL,qBAAqB,CAAC/J,IAAI,CAAE,IAAI,CAACyK,cAAc,GAAGsB,KAAM,CAAC;QAC9D,IAAI,CAACV,0BAA0B,CAAC,MAAM,IAAI,CAAClL,eAAe,CAACnC,iBAAiB,CAAC,CAAC,CAAC;MACnF;IACJ;IACA;AACJ;AACA;IACIgO,+BAA+BA,CAAA,EAAG;MAC9B,OAAO,IAAI,CAACpB,kCAAkC,GAAG,IAAI,GAAG,IAAI,CAACD,sBAAsB;IACvF;IACA;AACJ;AACA;AACA;IACI5K,wBAAwBA,CAACkM,MAAM,EAAEC,EAAE,GAAG,UAAU,EAAE;MAC9C;MACAD,MAAM,GAAG,IAAI,CAACjC,UAAU,IAAIkC,EAAE,KAAK,UAAU,GAAG,CAAC,GAAGD,MAAM;MAC1D;MACA;MACA,MAAMnG,KAAK,GAAG,IAAI,CAACP,GAAG,IAAI,IAAI,CAACA,GAAG,CAAClF,KAAK,IAAI,KAAK;MACjD,MAAM8L,YAAY,GAAG,IAAI,CAACnD,WAAW,IAAI,YAAY;MACrD,MAAMoD,IAAI,GAAGD,YAAY,GAAG,GAAG,GAAG,GAAG;MACrC,MAAME,aAAa,GAAGF,YAAY,IAAIrG,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MACpD,IAAIwG,SAAS,GAAI,YAAWF,IAAK,IAAGG,MAAM,CAACF,aAAa,GAAGJ,MAAM,CAAE,KAAI;MACvE,IAAI,CAACtB,sBAAsB,GAAGsB,MAAM;MACpC,IAAIC,EAAE,KAAK,QAAQ,EAAE;QACjBI,SAAS,IAAK,aAAYF,IAAK,SAAQ;QACvC;QACA;QACA;QACA,IAAI,CAACxB,kCAAkC,GAAG,IAAI;MAClD;MACA,IAAI,IAAI,CAAC4B,yBAAyB,IAAIF,SAAS,EAAE;QAC7C;QACA;QACA,IAAI,CAACE,yBAAyB,GAAGF,SAAS;QAC1C,IAAI,CAACjB,0BAA0B,CAAC,MAAM;UAClC,IAAI,IAAI,CAACT,kCAAkC,EAAE;YACzC,IAAI,CAACD,sBAAsB,IAAI,IAAI,CAAC8B,0BAA0B,CAAC,CAAC;YAChE,IAAI,CAAC7B,kCAAkC,GAAG,KAAK;YAC/C,IAAI,CAAC7K,wBAAwB,CAAC,IAAI,CAAC4K,sBAAsB,CAAC;UAC9D,CAAC,MACI;YACD,IAAI,CAACxK,eAAe,CAAClC,uBAAuB,CAAC,CAAC;UAClD;QACJ,CAAC,CAAC;MACN;IACJ;IACA;AACJ;AACA;AACA;AACA;AACA;AACA;IACII,cAAcA,CAAC4N,MAAM,EAAE7N,QAAQ,GAAG,MAAM,EAAE;MACtC,MAAMwH,OAAO,GAAG;QAAExH;MAAS,CAAC;MAC5B,IAAI,IAAI,CAAC4K,WAAW,KAAK,YAAY,EAAE;QACnCpD,OAAO,CAACjH,KAAK,GAAGsN,MAAM;MAC1B,CAAC,MACI;QACDrG,OAAO,CAACM,GAAG,GAAG+F,MAAM;MACxB;MACA,IAAI,CAAC7J,UAAU,CAACuD,QAAQ,CAACC,OAAO,CAAC;IACrC;IACA;AACJ;AACA;AACA;AACA;IACI1H,aAAaA,CAACC,KAAK,EAAEC,QAAQ,GAAG,MAAM,EAAE;MACpC,IAAI,CAAC+B,eAAe,CAACjC,aAAa,CAACC,KAAK,EAAEC,QAAQ,CAAC;IACvD;IACA;AACJ;AACA;AACA;AACA;IACIa,mBAAmBA,CAAC4H,IAAI,EAAE;MACtB;MACA,IAAI5H,mBAAmB;MACvB,IAAI,IAAI,CAACmD,UAAU,IAAI,IAAI,EAAE;QACzBnD,mBAAmB,GAAIyN,KAAK,IAAK,KAAK,CAACzN,mBAAmB,CAACyN,KAAK,CAAC;MACrE,CAAC,MACI;QACDzN,mBAAmB,GAAIyN,KAAK,IAAK,IAAI,CAACtK,UAAU,CAACnD,mBAAmB,CAACyN,KAAK,CAAC;MAC/E;MACA,OAAOtN,IAAI,CAACG,GAAG,CAAC,CAAC,EAAEN,mBAAmB,CAAC4H,IAAI,KAAK,IAAI,CAACmC,WAAW,KAAK,YAAY,GAAG,OAAO,GAAG,KAAK,CAAC,CAAC,GACjG,IAAI,CAAC2D,qBAAqB,CAAC,CAAC,CAAC;IACrC;IACA;AACJ;AACA;AACA;IACIA,qBAAqBA,CAAC9F,IAAI,EAAE;MACxB,IAAI+F,QAAQ;MACZ,MAAM9F,IAAI,GAAG,MAAM;MACnB,MAAMC,KAAK,GAAG,OAAO;MACrB,MAAMjB,KAAK,GAAG,IAAI,CAACP,GAAG,EAAElF,KAAK,IAAI,KAAK;MACtC,IAAIwG,IAAI,IAAI,OAAO,EAAE;QACjB+F,QAAQ,GAAG9G,KAAK,GAAGiB,KAAK,GAAGD,IAAI;MACnC,CAAC,MACI,IAAID,IAAI,IAAI,KAAK,EAAE;QACpB+F,QAAQ,GAAG9G,KAAK,GAAGgB,IAAI,GAAGC,KAAK;MACnC,CAAC,MACI,IAAIF,IAAI,EAAE;QACX+F,QAAQ,GAAG/F,IAAI;MACnB,CAAC,MACI;QACD+F,QAAQ,GAAG,IAAI,CAAC5D,WAAW,KAAK,YAAY,GAAG,MAAM,GAAG,KAAK;MACjE;MACA,MAAM6D,kBAAkB,GAAG,IAAI,CAACzK,UAAU,CAACyJ,yCAAyC,CAACe,QAAQ,CAAC;MAC9F,MAAME,kBAAkB,GAAG,IAAI,CAAC1H,UAAU,CAACZ,aAAa,CAAC6D,qBAAqB,CAAC,CAAC,CAACuE,QAAQ,CAAC;MAC1F,OAAOE,kBAAkB,GAAGD,kBAAkB;IAClD;IACA;IACAJ,0BAA0BA,CAAA,EAAG;MACzB,MAAMM,SAAS,GAAG,IAAI,CAACC,eAAe,CAACxI,aAAa;MACpD,OAAO,IAAI,CAACwE,WAAW,KAAK,YAAY,GAAG+D,SAAS,CAACE,WAAW,GAAGF,SAAS,CAACG,YAAY;IAC7F;IACA;AACJ;AACA;AACA;IACIC,gBAAgBA,CAACpB,KAAK,EAAE;MACpB,IAAI,CAAC,IAAI,CAACR,MAAM,EAAE;QACd,OAAO,CAAC;MACZ;MACA,OAAO,IAAI,CAACA,MAAM,CAAC4B,gBAAgB,CAACpB,KAAK,EAAE,IAAI,CAAC/C,WAAW,CAAC;IAChE;IACA;IACAiC,iBAAiBA,CAAA,EAAG;MAChB;MACA,IAAI,CAACG,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAACjL,eAAe,CAACpC,mBAAmB,CAAC,CAAC;IAC9C;IACA;IACAqN,oBAAoBA,CAAA,EAAG;MACnB,IAAI,CAAC1D,aAAa,GAAG,IAAI,CAACtF,UAAU,CAAC2G,mBAAmB,CAAC,IAAI,CAACC,WAAW,CAAC;IAC9E;IACA;IACAqC,0BAA0BA,CAAC+B,QAAQ,EAAE;MACjC,IAAIA,QAAQ,EAAE;QACV,IAAI,CAACtC,wBAAwB,CAAC7G,IAAI,CAACmJ,QAAQ,CAAC;MAChD;MACA;MACA;MACA,IAAI,CAAC,IAAI,CAACvC,yBAAyB,EAAE;QACjC,IAAI,CAACA,yBAAyB,GAAG,IAAI;QACrC,IAAI,CAACvF,MAAM,CAACZ,iBAAiB,CAAC,MAAMuF,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;UAC7D,IAAI,CAACyB,kBAAkB,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;MACP;IACJ;IACA;IACAA,kBAAkBA,CAAA,EAAG;MACjB,IAAI,CAACf,yBAAyB,GAAG,KAAK;MACtC;MACA;MACA;MACA;MACA,IAAI,CAACmC,eAAe,CAACxI,aAAa,CAAC6I,KAAK,CAACf,SAAS,GAAG,IAAI,CAACE,yBAAyB;MACnF;MACA;MACA;MACA,IAAI,CAAClH,MAAM,CAAC8E,GAAG,CAAC,MAAM,IAAI,CAACR,kBAAkB,CAAC0D,YAAY,CAAC,CAAC,CAAC;MAC7D,MAAMC,uBAAuB,GAAG,IAAI,CAACzC,wBAAwB;MAC7D,IAAI,CAACA,wBAAwB,GAAG,EAAE;MAClC,KAAK,MAAM0C,EAAE,IAAID,uBAAuB,EAAE;QACtCC,EAAE,CAAC,CAAC;MACR;IACJ;IACA;IACA7D,oBAAoBA,CAAA,EAAG;MACnB,IAAI,CAACa,mBAAmB,GACpB,IAAI,CAACxB,WAAW,KAAK,YAAY,GAAG,EAAE,GAAI,GAAE,IAAI,CAACsB,iBAAkB,IAAG;MAC1E,IAAI,CAACC,kBAAkB,GACnB,IAAI,CAACvB,WAAW,KAAK,YAAY,GAAI,GAAE,IAAI,CAACsB,iBAAkB,IAAG,GAAG,EAAE;IAC9E;IAAC,QAAA/J,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAiN,iCAAA/M,CAAA;MAAA,YAAAA,CAAA,IAAwF+I,wBAAwB,EAh2BlClQ,EAAE,CAAA0N,iBAAA,CAg2BkD1N,EAAE,CAACgB,UAAU,GAh2BjEhB,EAAE,CAAA0N,iBAAA,CAg2B4E1N,EAAE,CAACmU,iBAAiB,GAh2BlGnU,EAAE,CAAA0N,iBAAA,CAg2B6G1N,EAAE,CAACsL,MAAM,GAh2BxHtL,EAAE,CAAA0N,iBAAA,CAg2BmIzK,uBAAuB,MAh2B5JjD,EAAE,CAAA0N,iBAAA,CAg2BuLlL,EAAE,CAACmL,cAAc,MAh2B1M3N,EAAE,CAAA0N,iBAAA,CAg2BqOxF,gBAAgB,GAh2BvPlI,EAAE,CAAA0N,iBAAA,CAg2BkQG,aAAa,GAh2BjR7N,EAAE,CAAA0N,iBAAA,CAg2B4R4B,kBAAkB;IAAA,CAA4D;IAAA,QAAAlI,EAAA,GACnc,IAAI,CAACgN,IAAI,kBAj2B8EpU,EAAE,CAAAqU,iBAAA;MAAA9M,IAAA,EAi2BJ2I,wBAAwB;MAAA1I,SAAA;MAAA8M,SAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAj2BtBxU,EAAE,CAAA0U,WAAA,CAAA3R,GAAA;QAAA;QAAA,IAAAyR,EAAA;UAAA,IAAAG,EAAA;UAAF3U,EAAE,CAAA4U,cAAA,CAAAD,EAAA,GAAF3U,EAAE,CAAA6U,WAAA,QAAAJ,GAAA,CAAAhB,eAAA,GAAAkB,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,sCAAAV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFxU,EAAE,CAAAmV,WAAA,8CAAAV,GAAA,CAAAhF,WAAA,KAi2BY,YAAO,CAAC,4CAAAgF,GAAA,CAAAhF,WAAA,KAAR,YAAO,CAAC;QAAA;MAAA;MAAAhI,MAAA;QAAAgI,WAAA;QAAAgB,UAAA,GAj2BtBzQ,EAAE,CAAAoV,YAAA,CAAAC,0BAAA,8BAi2BkK5U,gBAAgB;MAAA;MAAA6U,OAAA;QAAA9R,mBAAA;MAAA;MAAAkE,UAAA;MAAAC,QAAA,GAj2BpL3H,EAAE,CAAA4H,kBAAA,CAi2B4e,CACtkB;QACIC,OAAO,EAAE+D,aAAa;QACtB9D,UAAU,EAAEA,CAACyN,iBAAiB,EAAExR,QAAQ,KAAKwR,iBAAiB,IAAIxR,QAAQ;QAC1EgE,IAAI,EAAE,CAAC,CAAC,IAAIzH,QAAQ,CAAC,CAAC,EAAE,IAAIC,MAAM,CAAC+O,kBAAkB,CAAC,CAAC,EAAEY,wBAAwB;MACrF,CAAC,CACJ,GAv2B2FlQ,EAAE,CAAAwV,wBAAA,EAAFxV,EAAE,CAAA4P,0BAAA,EAAF5P,EAAE,CAAAyV,mBAAA;MAAAC,kBAAA,EAAA1S,GAAA;MAAA2S,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAvB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFxU,EAAE,CAAAgW,eAAA;UAAFhW,EAAE,CAAAiW,cAAA,eAu2B6Q,CAAC;UAv2BhRjW,EAAE,CAAAkW,YAAA,EAu2B0S,CAAC;UAv2B7SlW,EAAE,CAAAmW,YAAA,CAu2BkT,CAAC;UAv2BrTnW,EAAE,CAAAoW,SAAA,YAu2BumB,CAAC;QAAA;QAAA,IAAA5B,EAAA;UAv2B1mBxU,EAAE,CAAAqW,SAAA,EAu2ByjB,CAAC;UAv2B5jBrW,EAAE,CAAAsW,WAAA,UAAA7B,GAAA,CAAAzD,kBAu2ByjB,CAAC,WAAAyD,GAAA,CAAAxD,mBAAsC,CAAC;QAAA;MAAA;MAAAsF,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAA6yD;EACp/E;EAAC,OA5WKvG,wBAAwB;AAAA;AA6W9B;EAAA,QAAA7L,SAAA,oBAAAA,SAAA;AAAA;;AAqCA;AACA,SAASqS,SAASA,CAACjH,WAAW,EAAEkH,SAAS,EAAEC,IAAI,EAAE;EAC7C,MAAMtK,EAAE,GAAGsK,IAAI;EACf,IAAI,CAACtK,EAAE,CAACwC,qBAAqB,EAAE;IAC3B,OAAO,CAAC;EACZ;EACA,MAAM+H,IAAI,GAAGvK,EAAE,CAACwC,qBAAqB,CAAC,CAAC;EACvC,IAAIW,WAAW,KAAK,YAAY,EAAE;IAC9B,OAAOkH,SAAS,KAAK,OAAO,GAAGE,IAAI,CAACrK,IAAI,GAAGqK,IAAI,CAACpK,KAAK;EACzD;EACA,OAAOkK,SAAS,KAAK,OAAO,GAAGE,IAAI,CAAClK,GAAG,GAAGkK,IAAI,CAACnK,MAAM;AACzD;AACA;AACA;AACA;AACA;AAHA,IAIMoK,eAAe;EAArB,MAAMA,eAAe,CAAC;IAClB;IACA,IAAIC,eAAeA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACC,gBAAgB;IAChC;IACA,IAAID,eAAeA,CAACjQ,KAAK,EAAE;MACvB,IAAI,CAACkQ,gBAAgB,GAAGlQ,KAAK;MAC7B,IAAInE,YAAY,CAACmE,KAAK,CAAC,EAAE;QACrB,IAAI,CAACmQ,kBAAkB,CAACxQ,IAAI,CAACK,KAAK,CAAC;MACvC,CAAC,MACI;QACD;QACA,IAAI,CAACmQ,kBAAkB,CAACxQ,IAAI,CAAC,IAAI7D,eAAe,CAACnB,YAAY,CAACqF,KAAK,CAAC,GAAGA,KAAK,GAAGoQ,KAAK,CAAC5J,IAAI,CAACxG,KAAK,IAAI,EAAE,CAAC,CAAC,CAAC;MAC5G;IACJ;IACA;AACJ;AACA;AACA;IACI,IAAIqQ,oBAAoBA,CAAA,EAAG;MACvB,OAAO,IAAI,CAACC,qBAAqB;IACrC;IACA,IAAID,oBAAoBA,CAAClD,EAAE,EAAE;MACzB,IAAI,CAACoD,YAAY,GAAG,IAAI;MACxB,IAAI,CAACD,qBAAqB,GAAGnD,EAAE,GACzB,CAACrP,KAAK,EAAE0S,IAAI,KAAKrD,EAAE,CAACrP,KAAK,IAAI,IAAI,CAACsM,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC9L,KAAK,GAAG,CAAC,CAAC,EAAEkS,IAAI,CAAC,GACxFC,SAAS;IACnB;IACA;IACA,IAAIC,qBAAqBA,CAAC1Q,KAAK,EAAE;MAC7B,IAAIA,KAAK,EAAE;QACP,IAAI,CAACuQ,YAAY,GAAG,IAAI;QACxB,IAAI,CAACI,SAAS,GAAG3Q,KAAK;MAC1B;IACJ;IACA;AACJ;AACA;AACA;IACI,IAAI4Q,8BAA8BA,CAAA,EAAG;MACjC,OAAO,IAAI,CAACC,aAAa,CAACC,aAAa;IAC3C;IACA,IAAIF,8BAA8BA,CAACnF,IAAI,EAAE;MACrC,IAAI,CAACoF,aAAa,CAACC,aAAa,GAAG9X,oBAAoB,CAACyS,IAAI,CAAC;IACjE;IACApP,WAAWA,CAAA,CACX;IACA0U,iBAAiB,EACjB;IACAJ,SAAS,EACT;IACAK,QAAQ,EACR;IACAH,aAAa,EACb;IACAjU,SAAS,EAAEqI,MAAM,EAAE;MACf,IAAI,CAAC8L,iBAAiB,GAAGA,iBAAiB;MAC1C,IAAI,CAACJ,SAAS,GAAGA,SAAS;MAC1B,IAAI,CAACK,QAAQ,GAAGA,QAAQ;MACxB,IAAI,CAACH,aAAa,GAAGA,aAAa;MAClC,IAAI,CAACjU,SAAS,GAAGA,SAAS;MAC1B;MACA,IAAI,CAACqU,UAAU,GAAG,IAAI7W,OAAO,CAAC,CAAC;MAC/B;MACA,IAAI,CAAC+V,kBAAkB,GAAG,IAAI/V,OAAO,CAAC,CAAC;MACvC;MACA,IAAI,CAAC+Q,UAAU,GAAG,IAAI,CAACgF,kBAAkB,CAACxT,IAAI;MAC9C;MACA3B,SAAS,CAAC,IAAI,CAAC;MACf;MACAC,QAAQ,CAAC,CAAC;MACV;MACA;MACA;MACAC,SAAS,CAAC,CAAC,CAACgW,IAAI,EAAEC,GAAG,CAAC,KAAK,IAAI,CAACC,iBAAiB,CAACF,IAAI,EAAEC,GAAG,CAAC,CAAC;MAC7D;MACAhW,WAAW,CAAC,CAAC,CAAC,CAAC;MACf;MACA,IAAI,CAACkW,OAAO,GAAG,IAAI;MACnB;MACA,IAAI,CAACd,YAAY,GAAG,KAAK;MACzB,IAAI,CAACpL,UAAU,GAAG,IAAI/K,OAAO,CAAC,CAAC;MAC/B,IAAI,CAAC+Q,UAAU,CAAChJ,SAAS,CAACiJ,IAAI,IAAI;QAC9B,IAAI,CAACkG,KAAK,GAAGlG,IAAI;QACjB,IAAI,CAACmG,qBAAqB,CAAC,CAAC;MAChC,CAAC,CAAC;MACF,IAAI,CAAC3U,SAAS,CAACoN,mBAAmB,CAACrN,IAAI,CAAC5B,SAAS,CAAC,IAAI,CAACoK,UAAU,CAAC,CAAC,CAAChD,SAAS,CAACuJ,KAAK,IAAI;QACnF,IAAI,CAACtB,cAAc,GAAGsB,KAAK;QAC3B,IAAI,IAAI,CAACuF,UAAU,CAACO,SAAS,CAAClG,MAAM,EAAE;UAClCrG,MAAM,CAAC8E,GAAG,CAAC,MAAM,IAAI,CAACkH,UAAU,CAACtR,IAAI,CAAC,IAAI,CAACyK,cAAc,CAAC,CAAC;QAC/D;QACA,IAAI,CAACmH,qBAAqB,CAAC,CAAC;MAChC,CAAC,CAAC;MACF,IAAI,CAAC3U,SAAS,CAACI,MAAM,CAAC,IAAI,CAAC;IAC/B;IACA;AACJ;AACA;AACA;AACA;IACI8P,gBAAgBA,CAACpB,KAAK,EAAE/C,WAAW,EAAE;MACjC,IAAI+C,KAAK,CAACpN,KAAK,IAAIoN,KAAK,CAACnN,GAAG,EAAE;QAC1B,OAAO,CAAC;MACZ;MACA,IAAI,CAACmN,KAAK,CAACpN,KAAK,GAAG,IAAI,CAAC8L,cAAc,CAAC9L,KAAK,IAAIoN,KAAK,CAACnN,GAAG,GAAG,IAAI,CAAC6L,cAAc,CAAC7L,GAAG,MAC9E,OAAOhB,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACjD,MAAMC,KAAK,CAAE,0DAAyD,CAAC;MAC3E;MACA;MACA,MAAMiU,kBAAkB,GAAG/F,KAAK,CAACpN,KAAK,GAAG,IAAI,CAAC8L,cAAc,CAAC9L,KAAK;MAClE;MACA,MAAMoT,QAAQ,GAAGhG,KAAK,CAACnN,GAAG,GAAGmN,KAAK,CAACpN,KAAK;MACxC;MACA;MACA,IAAIqT,SAAS;MACb,IAAIC,QAAQ;MACZ;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,QAAQ,EAAEG,CAAC,EAAE,EAAE;QAC/B,MAAMC,IAAI,GAAG,IAAI,CAACf,iBAAiB,CAACzO,GAAG,CAACuP,CAAC,GAAGJ,kBAAkB,CAAC;QAC/D,IAAIK,IAAI,IAAIA,IAAI,CAACC,SAAS,CAACzG,MAAM,EAAE;UAC/BqG,SAAS,GAAGC,QAAQ,GAAGE,IAAI,CAACC,SAAS,CAAC,CAAC,CAAC;UACxC;QACJ;MACJ;MACA;MACA,KAAK,IAAIF,CAAC,GAAGH,QAAQ,GAAG,CAAC,EAAEG,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;QACpC,MAAMC,IAAI,GAAG,IAAI,CAACf,iBAAiB,CAACzO,GAAG,CAACuP,CAAC,GAAGJ,kBAAkB,CAAC;QAC/D,IAAIK,IAAI,IAAIA,IAAI,CAACC,SAAS,CAACzG,MAAM,EAAE;UAC/BsG,QAAQ,GAAGE,IAAI,CAACC,SAAS,CAACD,IAAI,CAACC,SAAS,CAACzG,MAAM,GAAG,CAAC,CAAC;UACpD;QACJ;MACJ;MACA,OAAOqG,SAAS,IAAIC,QAAQ,GACtBhC,SAAS,CAACjH,WAAW,EAAE,KAAK,EAAEiJ,QAAQ,CAAC,GAAGhC,SAAS,CAACjH,WAAW,EAAE,OAAO,EAAEgJ,SAAS,CAAC,GACpF,CAAC;IACX;IACAK,SAASA,CAAA,EAAG;MACR,IAAI,IAAI,CAACX,OAAO,IAAI,IAAI,CAACd,YAAY,EAAE;QACnC;QACA;QACA;QACA,MAAM0B,OAAO,GAAG,IAAI,CAACZ,OAAO,CAACa,IAAI,CAAC,IAAI,CAACC,cAAc,CAAC;QACtD,IAAI,CAACF,OAAO,EAAE;UACV,IAAI,CAACG,cAAc,CAAC,CAAC;QACzB,CAAC,MACI;UACD,IAAI,CAACC,aAAa,CAACJ,OAAO,CAAC;QAC/B;QACA,IAAI,CAAC1B,YAAY,GAAG,KAAK;MAC7B;IACJ;IACAvN,WAAWA,CAAA,EAAG;MACV,IAAI,CAACpG,SAAS,CAACQ,MAAM,CAAC,CAAC;MACvB,IAAI,CAAC+S,kBAAkB,CAACxQ,IAAI,CAAC8Q,SAAS,CAAC;MACvC,IAAI,CAACN,kBAAkB,CAAC9S,QAAQ,CAAC,CAAC;MAClC,IAAI,CAAC4T,UAAU,CAAC5T,QAAQ,CAAC,CAAC;MAC1B,IAAI,CAAC8H,UAAU,CAACxF,IAAI,CAAC,CAAC;MACtB,IAAI,CAACwF,UAAU,CAAC9H,QAAQ,CAAC,CAAC;MAC1B,IAAI,CAACwT,aAAa,CAACzT,MAAM,CAAC,CAAC;IAC/B;IACA;IACAmU,qBAAqBA,CAAA,EAAG;MACpB,IAAI,CAAC,IAAI,CAACnH,cAAc,EAAE;QACtB;MACJ;MACA,IAAI,CAAC+H,cAAc,GAAG,IAAI,CAACb,KAAK,CAACgB,KAAK,CAAC,IAAI,CAAClI,cAAc,CAAC9L,KAAK,EAAE,IAAI,CAAC8L,cAAc,CAAC7L,GAAG,CAAC;MAC1F,IAAI,CAAC,IAAI,CAAC8S,OAAO,EAAE;QACf;QACA;QACA,IAAI,CAACA,OAAO,GAAG,IAAI,CAACL,QAAQ,CAACuB,IAAI,CAAC,IAAI,CAACJ,cAAc,CAAC,CAACK,MAAM,CAAC,CAAC1U,KAAK,EAAE0S,IAAI,KAAK;UAC3E,OAAO,IAAI,CAACH,oBAAoB,GAAG,IAAI,CAACA,oBAAoB,CAACvS,KAAK,EAAE0S,IAAI,CAAC,GAAGA,IAAI;QACpF,CAAC,CAAC;MACN;MACA,IAAI,CAACD,YAAY,GAAG,IAAI;IAC5B;IACA;IACAa,iBAAiBA,CAACqB,KAAK,EAAEC,KAAK,EAAE;MAC5B,IAAID,KAAK,EAAE;QACPA,KAAK,CAACE,UAAU,CAAC,IAAI,CAAC;MAC1B;MACA,IAAI,CAACpC,YAAY,GAAG,IAAI;MACxB,OAAOmC,KAAK,GAAGA,KAAK,CAACE,OAAO,CAAC,IAAI,CAAC,GAAGvY,EAAE,CAAC,CAAC;IAC7C;IACA;IACA+X,cAAcA,CAAA,EAAG;MACb,MAAMS,KAAK,GAAG,IAAI,CAACvB,KAAK,CAAChG,MAAM;MAC/B,IAAIuG,CAAC,GAAG,IAAI,CAACd,iBAAiB,CAACzF,MAAM;MACrC,OAAOuG,CAAC,EAAE,EAAE;QACR,MAAMC,IAAI,GAAG,IAAI,CAACf,iBAAiB,CAACzO,GAAG,CAACuP,CAAC,CAAC;QAC1CC,IAAI,CAACgB,OAAO,CAAChV,KAAK,GAAG,IAAI,CAACsM,cAAc,CAAC9L,KAAK,GAAGuT,CAAC;QAClDC,IAAI,CAACgB,OAAO,CAACD,KAAK,GAAGA,KAAK;QAC1B,IAAI,CAACE,gCAAgC,CAACjB,IAAI,CAACgB,OAAO,CAAC;QACnDhB,IAAI,CAACkB,aAAa,CAAC,CAAC;MACxB;IACJ;IACA;IACAX,aAAaA,CAACJ,OAAO,EAAE;MACnB,IAAI,CAACpB,aAAa,CAACoC,YAAY,CAAChB,OAAO,EAAE,IAAI,CAAClB,iBAAiB,EAAE,CAACmC,MAAM,EAAEC,sBAAsB,EAAEC,YAAY,KAAK,IAAI,CAACC,oBAAoB,CAACH,MAAM,EAAEE,YAAY,CAAC,EAAEF,MAAM,IAAIA,MAAM,CAAC1C,IAAI,CAAC;MAC1L;MACAyB,OAAO,CAACqB,qBAAqB,CAAEJ,MAAM,IAAK;QACtC,MAAMpB,IAAI,GAAG,IAAI,CAACf,iBAAiB,CAACzO,GAAG,CAAC4Q,MAAM,CAACE,YAAY,CAAC;QAC5DtB,IAAI,CAACgB,OAAO,CAACS,SAAS,GAAGL,MAAM,CAAC1C,IAAI;MACxC,CAAC,CAAC;MACF;MACA,MAAMqC,KAAK,GAAG,IAAI,CAACvB,KAAK,CAAChG,MAAM;MAC/B,IAAIuG,CAAC,GAAG,IAAI,CAACd,iBAAiB,CAACzF,MAAM;MACrC,OAAOuG,CAAC,EAAE,EAAE;QACR,MAAMC,IAAI,GAAG,IAAI,CAACf,iBAAiB,CAACzO,GAAG,CAACuP,CAAC,CAAC;QAC1CC,IAAI,CAACgB,OAAO,CAAChV,KAAK,GAAG,IAAI,CAACsM,cAAc,CAAC9L,KAAK,GAAGuT,CAAC;QAClDC,IAAI,CAACgB,OAAO,CAACD,KAAK,GAAGA,KAAK;QAC1B,IAAI,CAACE,gCAAgC,CAACjB,IAAI,CAACgB,OAAO,CAAC;MACvD;IACJ;IACA;IACAC,gCAAgCA,CAACD,OAAO,EAAE;MACtCA,OAAO,CAAC9E,KAAK,GAAG8E,OAAO,CAAChV,KAAK,KAAK,CAAC;MACnCgV,OAAO,CAACU,IAAI,GAAGV,OAAO,CAAChV,KAAK,KAAKgV,OAAO,CAACD,KAAK,GAAG,CAAC;MAClDC,OAAO,CAACW,IAAI,GAAGX,OAAO,CAAChV,KAAK,GAAG,CAAC,KAAK,CAAC;MACtCgV,OAAO,CAACY,GAAG,GAAG,CAACZ,OAAO,CAACW,IAAI;IAC/B;IACAJ,oBAAoBA,CAACH,MAAM,EAAEpV,KAAK,EAAE;MAChC;MACA;MACA;MACA;MACA,OAAO;QACH6V,WAAW,EAAE,IAAI,CAAChD,SAAS;QAC3BmC,OAAO,EAAE;UACLS,SAAS,EAAEL,MAAM,CAAC1C,IAAI;UACtB;UACA;UACAP,eAAe,EAAE,IAAI,CAACC,gBAAgB;UACtCpS,KAAK,EAAE,CAAC,CAAC;UACT+U,KAAK,EAAE,CAAC,CAAC;UACT7E,KAAK,EAAE,KAAK;UACZwF,IAAI,EAAE,KAAK;UACXE,GAAG,EAAE,KAAK;UACVD,IAAI,EAAE;QACV,CAAC;QACD3V;MACJ,CAAC;IACL;IAAC,QAAAoC,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAyT,wBAAAvT,CAAA;MAAA,YAAAA,CAAA,IAAwF2P,eAAe,EAhpCzB9W,EAAE,CAAA0N,iBAAA,CAgpCyC1N,EAAE,CAAC2a,gBAAgB,GAhpC9D3a,EAAE,CAAA0N,iBAAA,CAgpCyE1N,EAAE,CAAC4a,WAAW,GAhpCzF5a,EAAE,CAAA0N,iBAAA,CAgpCoG1N,EAAE,CAAC6a,eAAe,GAhpCxH7a,EAAE,CAAA0N,iBAAA,CAgpCmI7K,uBAAuB,GAhpC5J7C,EAAE,CAAA0N,iBAAA,CAgpCuKwC,wBAAwB,MAhpCjMlQ,EAAE,CAAA0N,iBAAA,CAgpC4N1N,EAAE,CAACsL,MAAM;IAAA,CAA4C;IAAA,QAAAlE,EAAA,GAC1W,IAAI,CAACC,IAAI,kBAjpC8ErH,EAAE,CAAAsH,iBAAA;MAAAC,IAAA,EAipCJuP,eAAe;MAAAtP,SAAA;MAAAC,MAAA;QAAAsP,eAAA;QAAAI,oBAAA;QAAAK,qBAAA;QAAAE,8BAAA;MAAA;MAAAhQ,UAAA;MAAAC,QAAA,GAjpCb3H,EAAE,CAAA4H,kBAAA,CAipC0S,CAAC;QAAEC,OAAO,EAAEhF,uBAAuB;QAAEiY,QAAQ,EAAEhY;MAA6B,CAAC,CAAC;IAAA,EAAiB;EAC/e;EAAC,OApPKgU,eAAe;AAAA;AAqPrB;EAAA,QAAAzS,SAAA,oBAAAA,SAAA;AAAA;;AAsBA;AACA;AACA;AAFA,IAGM0W,2BAA2B;EAAjC,MAAMA,2BAA2B,SAASxL,oBAAoB,CAAC;IAC3DpM,WAAWA,CAAC0I,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,GAAG,EAAE;MACnD,KAAK,CAACH,UAAU,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,GAAG,CAAC;IACpD;IACAsG,yCAAyCA,CAAChF,IAAI,EAAE;MAC5C,OAAQ,IAAI,CAACtC,aAAa,CAAC,CAAC,CAACC,aAAa,CAAC6D,qBAAqB,CAAC,CAAC,CAACxB,IAAI,CAAC,GACpE,IAAI,CAAC5H,mBAAmB,CAAC4H,IAAI,CAAC;IACtC;IAAC,QAAAtG,CAAA,GACQ,IAAI,CAACC,IAAI,YAAA+T,oCAAA7T,CAAA;MAAA,YAAAA,CAAA,IAAwF4T,2BAA2B,EAprCrC/a,EAAE,CAAA0N,iBAAA,CAorCqD1N,EAAE,CAACgB,UAAU,GAprCpEhB,EAAE,CAAA0N,iBAAA,CAorC+ExF,gBAAgB,GAprCjGlI,EAAE,CAAA0N,iBAAA,CAorC4G1N,EAAE,CAACsL,MAAM,GAprCvHtL,EAAE,CAAA0N,iBAAA,CAorCkIlL,EAAE,CAACmL,cAAc;IAAA,CAA4D;IAAA,QAAAvG,EAAA,GACxS,IAAI,CAACC,IAAI,kBArrC8ErH,EAAE,CAAAsH,iBAAA;MAAAC,IAAA,EAqrCJwT,2BAA2B;MAAAvT,SAAA;MAAAuN,SAAA;MAAArN,UAAA;MAAAC,QAAA,GArrCzB3H,EAAE,CAAA4H,kBAAA,CAqrCsJ,CAAC;QAAEC,OAAO,EAAEyH,kBAAkB;QAAE2L,WAAW,EAAEF;MAA4B,CAAC,CAAC,GArrCnO/a,EAAE,CAAA4P,0BAAA;IAAA,EAqrCyQ;EAC/W;EAAC,OAVKmL,2BAA2B;AAAA;AAWjC;EAAA,QAAA1W,SAAA,oBAAAA,SAAA;AAAA;;AAcA;AACA;AACA;AAFA,IAGM6W,0BAA0B;EAAhC,MAAMA,0BAA0B,SAAS3L,oBAAoB,CAAC;IAC1DpM,WAAWA,CAAC2I,gBAAgB,EAAEC,MAAM,EAAEC,GAAG,EAAE;MACvC,KAAK,CAAC,IAAIhL,UAAU,CAACqH,QAAQ,CAACuG,eAAe,CAAC,EAAE9C,gBAAgB,EAAEC,MAAM,EAAEC,GAAG,CAAC;MAC9E,IAAI,CAACE,gBAAgB,GAAG,IAAI9K,UAAU,CAAEsI,QAAQ,IAAK,IAAI,CAACqC,MAAM,CAACZ,iBAAiB,CAAC,MAAM9J,SAAS,CAACgH,QAAQ,EAAE,QAAQ,CAAC,CAAC5E,IAAI,CAAC5B,SAAS,CAAC,IAAI,CAACoK,UAAU,CAAC,CAAC,CAAChD,SAAS,CAACS,QAAQ,CAAC,CAAC,CAAC;IACjL;IACA4I,yCAAyCA,CAAChF,IAAI,EAAE;MAC5C,OAAO,IAAI,CAACtC,aAAa,CAAC,CAAC,CAACC,aAAa,CAAC6D,qBAAqB,CAAC,CAAC,CAACxB,IAAI,CAAC;IAC3E;IAAC,QAAAtG,CAAA,GACQ,IAAI,CAACC,IAAI,YAAAkU,mCAAAhU,CAAA;MAAA,YAAAA,CAAA,IAAwF+T,0BAA0B,EAhtCpClb,EAAE,CAAA0N,iBAAA,CAgtCoDxF,gBAAgB,GAhtCtElI,EAAE,CAAA0N,iBAAA,CAgtCiF1N,EAAE,CAACsL,MAAM,GAhtC5FtL,EAAE,CAAA0N,iBAAA,CAgtCuGlL,EAAE,CAACmL,cAAc;IAAA,CAA4D;IAAA,QAAAvG,EAAA,GAC7Q,IAAI,CAACC,IAAI,kBAjtC8ErH,EAAE,CAAAsH,iBAAA;MAAAC,IAAA,EAitCJ2T,0BAA0B;MAAA1T,SAAA;MAAAE,UAAA;MAAAC,QAAA,GAjtCxB3H,EAAE,CAAA4H,kBAAA,CAitC8G,CAAC;QAAEC,OAAO,EAAEyH,kBAAkB;QAAE2L,WAAW,EAAEC;MAA2B,CAAC,CAAC,GAjtC1Llb,EAAE,CAAA4P,0BAAA;IAAA,EAitCgO;EACtU;EAAC,OAVKsL,0BAA0B;AAAA;AAWhC;EAAA,QAAA7W,SAAA,oBAAAA,SAAA;AAAA;AASyB,IAEnB+W,mBAAmB;EAAzB,MAAMA,mBAAmB,CAAC;IAAA,QAAApU,CAAA,GACb,IAAI,CAACC,IAAI,YAAAoU,4BAAAlU,CAAA;MAAA,YAAAA,CAAA,IAAwFiU,mBAAmB;IAAA,CAAkD;IAAA,QAAAhU,EAAA,GACtK,IAAI,CAACkU,IAAI,kBAhuC8Etb,EAAE,CAAAub,gBAAA;MAAAhU,IAAA,EAguCS6T;IAAmB,EAAuD;IAAA,QAAAI,EAAA,GAC5K,IAAI,CAACC,IAAI,kBAjuC8Ezb,EAAE,CAAA0b,gBAAA,IAiuC+B;EACrI;EAAC,OAJKN,mBAAmB;AAAA;AAKzB;EAAA,QAAA/W,SAAA,oBAAAA,SAAA;AAAA;AAOA;AACA;AACA;AAFA,IAGMsX,eAAe;EAArB,MAAMA,eAAe,CAAC;IAAA,QAAA3U,CAAA,GACT,IAAI,CAACC,IAAI,YAAA2U,wBAAAzU,CAAA;MAAA,YAAAA,CAAA,IAAwFwU,eAAe;IAAA,CAAkD;IAAA,QAAAvU,EAAA,GAClK,IAAI,CAACkU,IAAI,kBA/uC8Etb,EAAE,CAAAub,gBAAA;MAAAhU,IAAA,EA+uCSoU;IAAe,EAQnF;IAAA,QAAAH,EAAA,GAC9B,IAAI,CAACC,IAAI,kBAxvC8Ezb,EAAE,CAAA0b,gBAAA;MAAAG,OAAA,GAwvCoCpZ,UAAU,EACxI2Y,mBAAmB,EAAE3Y,UAAU,EAAE2Y,mBAAmB;IAAA,EAAI;EACpE;EAAC,OAbKO,eAAe;AAAA;AAcrB;EAAA,QAAAtX,SAAA,oBAAAA,SAAA;AAAA;;AAwBA;AACA;AACA;;AAEA,SAASwC,yBAAyB,EAAE+E,aAAa,EAAEwP,mBAAmB,EAAEtE,eAAe,EAAE5G,wBAAwB,EAAEX,oBAAoB,EAAEwL,2BAA2B,EAAEG,0BAA0B,EAAEtN,mBAAmB,EAAE3F,mBAAmB,EAAE/E,8BAA8B,EAAEgF,gBAAgB,EAAEyT,eAAe,EAAErM,kBAAkB,EAAErM,uBAAuB,EAAE4K,aAAa,EAAEnH,sCAAsC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}