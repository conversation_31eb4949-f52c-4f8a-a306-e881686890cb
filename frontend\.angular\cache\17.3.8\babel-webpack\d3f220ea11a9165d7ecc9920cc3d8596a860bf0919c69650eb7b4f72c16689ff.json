{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/platform-browser\";\nexport let SharedCertificateComponent = /*#__PURE__*/(() => {\n  class SharedCertificateComponent {\n    constructor(route, meta) {\n      this.route = route;\n      this.meta = meta;\n    }\n    ngOnInit() {\n      this.route.paramMap.subscribe(params => {\n        // Get the 'permalink' parameter from the URL\n        const permalink = params.get('permalink');\n        // Do something with the permalink, e.g., store it in a variable\n        console.log('Permalink:', permalink);\n      });\n      this.meta.addTags([{\n        property: 'og:title',\n        content: 'This is a test title'\n      }, {\n        property: 'og:description',\n        content: 'This is a test description'\n      }, {\n        name: 'og:image',\n        content: 'https://covenanthousetoronto.ca/wp-content/uploads/2021/08/test-pattern-152459_960_720.png'\n      }]);\n    }\n    static #_ = this.ɵfac = function SharedCertificateComponent_Factory(t) {\n      return new (t || SharedCertificateComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.Meta));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SharedCertificateComponent,\n      selectors: [[\"app-shared-certificate\"]],\n      decls: 2,\n      vars: 0,\n      template: function SharedCertificateComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"p\");\n          i0.ɵɵtext(1, \"shared-certificate works!\");\n          i0.ɵɵelementEnd();\n        }\n      }\n    });\n  }\n  return SharedCertificateComponent;\n})();", "map": {"version": 3, "names": ["SharedCertificateComponent", "constructor", "route", "meta", "ngOnInit", "paramMap", "subscribe", "params", "permalink", "get", "console", "log", "addTags", "property", "content", "name", "_", "i0", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "i2", "Meta", "_2", "selectors", "decls", "vars", "template", "SharedCertificateComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\shared-link\\shared-certificate.component.ts", "C:\\Projects\\MLT-LG\\MLT-Platform\\frontend\\src\\app\\modules\\shared-link\\shared-certificate.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute } from '@angular/router';\r\nimport { Meta } from '@angular/platform-browser';\r\n\r\n@Component({\r\n  selector: 'app-shared-certificate',\r\n  templateUrl: './shared-certificate.component.html',\r\n  styleUrls: ['./shared-certificate.component.scss']\r\n})\r\nexport class SharedCertificateComponent implements OnInit {\r\n  constructor(private route: ActivatedRoute, private meta: Meta) { }\r\n\r\n  ngOnInit() {\r\n    this.route.paramMap.subscribe(params => {\r\n      // Get the 'permalink' parameter from the URL\r\n      const permalink = params.get('permalink');\r\n  \r\n      // Do something with the permalink, e.g., store it in a variable\r\n      console.log('Permalink:', permalink);\r\n    });\r\n\r\n    this.meta.addTags([ \r\n      { property: 'og:title', content: 'This is a test title' },\r\n      { property: 'og:description', content: 'This is a test description' }, \r\n      { name: 'og:image', content: 'https://covenanthousetoronto.ca/wp-content/uploads/2021/08/test-pattern-152459_960_720.png'}\r\n  ]);\r\n  }\r\n\r\n}\r\n", "<p>shared-certificate works!</p>\r\n"], "mappings": ";;;AASA,WAAaA,0BAA0B;EAAjC,MAAOA,0BAA0B;IACrCC,YAAoBC,KAAqB,EAAUC,IAAU;MAAzC,KAAAD,KAAK,GAALA,KAAK;MAA0B,KAAAC,IAAI,GAAJA,IAAI;IAAU;IAEjEC,QAAQA,CAAA;MACN,IAAI,CAACF,KAAK,CAACG,QAAQ,CAACC,SAAS,CAACC,MAAM,IAAG;QACrC;QACA,MAAMC,SAAS,GAAGD,MAAM,CAACE,GAAG,CAAC,WAAW,CAAC;QAEzC;QACAC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEH,SAAS,CAAC;MACtC,CAAC,CAAC;MAEF,IAAI,CAACL,IAAI,CAACS,OAAO,CAAC,CAChB;QAAEC,QAAQ,EAAE,UAAU;QAAEC,OAAO,EAAE;MAAsB,CAAE,EACzD;QAAED,QAAQ,EAAE,gBAAgB;QAAEC,OAAO,EAAE;MAA4B,CAAE,EACrE;QAAEC,IAAI,EAAE,UAAU;QAAED,OAAO,EAAE;MAA4F,CAAC,CAC7H,CAAC;IACF;IAAC,QAAAE,CAAA,G;uBAjBUhB,0BAA0B,EAAAiB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,IAAA;IAAA;IAAA,QAAAC,EAAA,G;YAA1BvB,0BAA0B;MAAAwB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTvCZ,EAAA,CAAAc,cAAA,QAAG;UAAAd,EAAA,CAAAe,MAAA,gCAAyB;UAAAf,EAAA,CAAAgB,YAAA,EAAI;;;;;SDSnBjC,0BAA0B;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}